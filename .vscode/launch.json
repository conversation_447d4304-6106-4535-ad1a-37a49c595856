{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Launch Start",
      "program": "${workspaceFolder}/scopes/legacy/scripts/start.js",
      "request": "launch",
      "env": {
        "BROWSER": "NONE"
      },
      "skipFiles": ["<node_internals>/**"],
      "type": "node",
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen"
    },
    {
      "name": "Launch via PNPM",
      "request": "launch",
      "runtimeArgs": ["run-script", "start"],
      "runtimeExecutable": "pnpm",
      "env": {
        "BROWSER": "NONE"
        //   "NODE_ENV": "development",
        //   "PUBLIC_URL": "http://localhost:3000"
      },
      "skipFiles": ["<node_internals>/**"],
      "type": "node",
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen"
    },
    {
      "type": "chrome",
      "request": "launch",
      "name": "[Launch] Chrome",
      "url": "http://my-dcbase.manyun-inc.com:3000",
      "webRoot": "${workspaceFolder}/scopes/legacy"
    },
    {
      "type": "msedge",
      "name": "Launch Microsoft Edge",
      "request": "launch",
      "runtimeArgs": ["--remote-debugging-port=9222"],
      "url": "http://my-dcbase.manyun-inc.com:3000" // Provide your project's url to finish configuring
    },
    {
      "type": "node",
      "request": "launch",
      "name": "[resource-hub] bit import",
      "cwd": "${workspaceFolder}/scopes/resource-hub",
      "outFiles": ["${workspaceFolder}/node_moodules/@manyun/**/*.js"],
      "sourceMaps": true,
      "program": "${workspaceFolder}/node_modules/@teambit/bit/dist/app.js",
      "args": ["import"],
      "env": {
        "NODE_ENV": "development"
      },
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen"
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Bit Debugger",
      "env": {
        "BUILD_ENV": "CI",
        "NODE_ENV": "development"
      },
      "program": "${workspaceFolder}/scopes/redash/node_modules/@teambit/bit/dist/app.js",
      "args": "${input:bitCommand}",
      "outFiles": [
        "${workspaceFolder}/scopes/redash/**/*.js",
        "${workspaceFolder}/scopes/redash/node_modules/**/dist/**/*.js"
      ],
      "console": "integratedTerminal",
      "sourceMaps": true,
      "internalConsoleOptions": "neverOpen",
      "cwd": "${workspaceFolder}/scopes/redash"
    }
  ],
  "inputs": [
    {
      "id": "bitCommand",
      "type": "promptString",
      "description": "Enter the command without the 'bit' prefix",
      "default": "build --reuse-capsules --rewrite --dev --tasks CoreExporter,TypescriptCompile,build_application app/redash"
    }
  ]
}
