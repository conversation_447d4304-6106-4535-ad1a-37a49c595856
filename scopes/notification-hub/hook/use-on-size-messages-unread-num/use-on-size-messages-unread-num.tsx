import { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { OnSiteMessageType } from '@manyun/notification-hub.model.on-site-messages';
import {
  getOnsiteMessagesUnreadNumAction,
  selecOnSiteMessagesUnreadNum,
} from '@manyun/notification-hub.state.on-site-messages';

export function useOnSizeMessagesUnreadNum(): readonly [
  () => void,
  {
    unreadNumKeyMap: Record<OnSiteMessageType, number>;
  }
] {
  const dispatch = useDispatch();

  const getOnSiteMessagesUnreadNum = useCallback(() => {
    dispatch(getOnsiteMessagesUnreadNumAction());
  }, [dispatch]);

  const unreadNumKeyMap = useSelector(selecOnSiteMessagesUnreadNum());

  return [getOnSiteMessagesUnreadNum, { unreadNumKeyMap }] as const;
}
