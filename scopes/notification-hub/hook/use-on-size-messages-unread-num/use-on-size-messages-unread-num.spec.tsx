import { act, renderHook } from '@testing-library/react-hooks';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { useRemoteMock } from '@manyun/service.request';

import { useOnSizeMessagesUnreadNum } from './use-on-size-messages-unread-num';

let mockOff: ReturnType<typeof useRemoteMock>;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

it('useCustomers', () => {
  const { result } = renderHook(() => useOnSizeMessagesUnreadNum(), { wrapper: FakeStore });
  act(() => {
    result.current[0]();
  });
  expect(typeof result.current[1].unreadNumKeyMap.all).toBe('number');
});
