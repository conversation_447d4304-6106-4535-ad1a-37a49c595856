import React from 'react';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';

import { useOnSizeMessagesUnreadNum } from './use-on-size-messages-unread-num';

const Basic = () => {
  const [getOnSiteMessagesUnreadNum, { unreadNumKeyMap }] = useOnSizeMessagesUnreadNum();

  React.useEffect(() => {
    getOnSiteMessagesUnreadNum();
  }, []);

  return (
    <>
      <p>result data: </p>
      <pre>{JSON.stringify(unreadNumKeyMap)}</pre>
    </>
  );
};

export const BasicUseOnSizeMessagesUnreadNum = () => {
  return (
    <FakeStore>
      <Basic />
    </FakeStore>
  );
};
