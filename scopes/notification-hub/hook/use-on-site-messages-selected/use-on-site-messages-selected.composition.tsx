import React from 'react';

import { OnSiteMessageType } from '@manyun/notification-hub.model.on-site-messages';
// @ts-ignore
import { OnSiteMessagesReduxWrapper } from '@manyun/notification-hub.state.on-site-messages/dist/on-site-messages.composition-wrapper';

import { useOnSiteMessagesSelected } from './use-on-site-messages-selected';

const Basic = () => {
  const [selectedIds, setSelectedIds] = useOnSiteMessagesSelected({ type: OnSiteMessageType.All });

  return (
    <>
      <p>Selected IDs: {selectedIds.join(', ')}</p>
      <button
        onClick={() => {
          setSelectedIds([1, 2, 3, 4, 5, 6]);
        }}
      >
        Set selected IDs to `1, 2, 3, 4, 5, 6`
      </button>
    </>
  );
};

export const BasicuseOnSiteMessagesSelected = () => (
  <OnSiteMessagesReduxWrapper>
    <Basic />
  </OnSiteMessagesReduxWrapper>
);
