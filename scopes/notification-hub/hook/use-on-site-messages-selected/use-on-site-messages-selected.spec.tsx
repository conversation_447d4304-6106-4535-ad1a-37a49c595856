import { act, renderHook } from '@testing-library/react-hooks';

import { OnSiteMessageType } from '@manyun/notification-hub.model.on-site-messages';
// @ts-ignore
import { OnSiteMessagesReduxWrapper } from '@manyun/notification-hub.state.on-site-messages/dist/on-site-messages.composition-wrapper';

import { useOnSiteMessagesSelected } from './use-on-site-messages-selected';

test('should re-render when `selectedIds` updated', () => {
  let populateEntities = false;
  const { result, rerender } = renderHook(
    () => useOnSiteMessagesSelected({ type: OnSiteMessageType.All, populateEntities }),
    { wrapper: OnSiteMessagesReduxWrapper }
  );
  const [selectedIds, setSelectedIds] = result.current;

  expect(selectedIds).toHaveLength(0);
  expect(typeof setSelectedIds).toBe('function');

  act(() => {
    setSelectedIds([1, 2]);
  });

  expect(result.current[0]).toEqual([1, 2]);

  populateEntities = true;
  rerender();

  // Because initial `entities` is `{}`
  expect(result.current[0]).toEqual([undefined, undefined]);
});
