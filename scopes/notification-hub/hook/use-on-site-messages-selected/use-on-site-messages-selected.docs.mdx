---
description: '获取/设置 `on-site-messages` 选中项。'
labels: ['hook', 'on-site-messages', 'selected-messages']
---

import { useOnSiteMessagesSelected } from './use-on-site-messages-selected';

## useOnSiteMessagesSelected

默认仅获取 `IDs`，如要获取消息实体，则须指定 `populateEntities: true`。

可通过 `setSelectedIds` 设置消息类型（`type`）的未读消息的选中的 `IDs`。

### 使用

```js
// 仅获取 IDs
const [selectedIds, setSelectedIds] = useOnSiteMessagesSelected({ type });

// 获取消息实体
const [selectedEntities, setSelectedIds] = useOnSiteMessagesSelected({ type, populateEntities: true });

setSelectedIds([1, 2]);
```
