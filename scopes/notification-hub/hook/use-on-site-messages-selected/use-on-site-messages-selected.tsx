import { useCallback } from 'react';
import { shallowEqual, useDispatch, useSelector } from 'react-redux';

import {
  OnSiteMessageState,
  OnSiteMessageType,
} from '@manyun/notification-hub.model.on-site-messages';
import {
  onSiteMessagesSliceActions,
  selectOnSiteMessagesEntities,
  selectStateTypedOnSiteMessages,
} from '@manyun/notification-hub.state.on-site-messages';

export type UseOnSiteMessagesSelectedProps<P extends boolean> = {
  type: OnSiteMessageType;

  /** 是否把消息实体数据一并带出 */
  populateEntities?: P;
};

type SetSelectedIdsFn = (selectedIds: number[]) => void;
export function useOnSiteMessagesSelected<P extends boolean = false>(
  props: UseOnSiteMessagesSelectedProps<P>
): P extends false
  ? readonly [number[], SetSelectedIdsFn]
  : readonly [ReturnType<ReturnType<typeof selectOnSiteMessagesEntities>>, SetSelectedIdsFn];

export function useOnSiteMessagesSelected({
  type,
  populateEntities = false,
}: UseOnSiteMessagesSelectedProps<boolean>) {
  const { selectedIds } = useSelector(
    selectStateTypedOnSiteMessages(OnSiteMessageState.Unread, type),
    (left, right) => shallowEqual(left.selectedIds, right.selectedIds)
  );
  const entities = useSelector(selectOnSiteMessagesEntities(selectedIds));

  const dispatch = useDispatch();
  const setSelectedIds: SetSelectedIdsFn = useCallback(
    (selectedIds: number[]) => {
      dispatch(onSiteMessagesSliceActions.setSelectedIds({ type, selectedIds }));
    },
    [dispatch, type]
  );

  return [populateEntities ? entities : selectedIds, setSelectedIds] as const;
}
