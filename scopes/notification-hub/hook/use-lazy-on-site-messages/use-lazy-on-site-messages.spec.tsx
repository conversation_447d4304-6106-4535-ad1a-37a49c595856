import { act, renderHook } from '@testing-library/react-hooks';

// import {
//   OnSiteMessageState,
//   OnSiteMessageType,
// } from '@manyun/notification-hub.model.on-site-messages';
// @ts-ignore
import { OnSiteMessagesReduxWrapper } from '@manyun/notification-hub.state.on-site-messages/dist/on-site-messages.composition-wrapper';

import { useLazyOnSiteMessages } from './use-lazy-on-site-messages';

test('should get the top 10 on-site messages', async () => {
  // const { result, waitForNextUpdate } = renderHook(
  //   () =>
  //     useLazyOnSiteMessages({
  //       state: OnSiteMessageState.Unread,
  //       type: OnSiteMessageType.All,
  //     }),
  //   { wrapper: OnSiteMessagesReduxWrapper }
  // );
  // expect(result.current[1].data.data).toHaveLength(0);
  // expect(result.current[1].data.total).toBe(0);
  // act(() => {
  //   result.current[0]();
  // });
  // await waitForNextUpdate();
  // expect(result.current[1].data.data).toHaveLength(10);
  // expect(result.current[1].data.total).toBe(100);
});
