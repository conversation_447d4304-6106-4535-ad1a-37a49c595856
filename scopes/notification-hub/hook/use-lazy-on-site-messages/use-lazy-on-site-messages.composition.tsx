import React, { useEffect } from 'react';

import {
  OnSiteMessageState,
  OnSiteMessageType,
} from '@manyun/notification-hub.model.on-site-messages';
// @ts-ignore
import { OnSiteMessagesReduxWrapper } from '@manyun/notification-hub.state.on-site-messages/dist/on-site-messages.composition-wrapper';

import { useLazyOnSiteMessages } from './use-lazy-on-site-messages';

const Basic = () => {
  const [getMessages, { data }] = useLazyOnSiteMessages({
    state: OnSiteMessageState.Unread,
    type: OnSiteMessageType.All,
  });

  useEffect(() => {
    getMessages();
  }, []);

  return (
    <>
      <p>result data: </p>
      <pre>{JSON.stringify(data, null, 2)}</pre>
    </>
  );
};

export const BasicuseLazyOnSiteMessages = () => {
  return (
    <OnSiteMessagesReduxWrapper>
      <Basic />
    </OnSiteMessagesReduxWrapper>
  );
};
