---
description: '一个获取通知中心-站内消息的 React Hook。'
labels: ['hook', 'on-site-messages']
---

## useLazyOnSiteMessages

### Signature

### 使用

```js
const [getMessages, { loading, data, error }] = useLazyOnSiteMessages({ state, type });

<p>loading: {loading.toString()}</p>
<pre>{error ? JSON.stringify(error) : JSON.stringify(data, null, 2)}</pre>
<button onClick={() => { getMessages(); }}>GET SOME MESSAGES</button>
```
