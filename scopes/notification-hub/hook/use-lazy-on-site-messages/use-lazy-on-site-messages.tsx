import { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import {
  OnSiteMessageState,
  OnSiteMessageType,
} from '@manyun/notification-hub.model.on-site-messages';
import {
  getOnSiteMessagesAction,
  selectOnSiteMessagesEntities,
  selectStateTypedOnSiteMessages,
} from '@manyun/notification-hub.state.on-site-messages';

export type UseLazyOnSiteMessagesProps = { state: OnSiteMessageState; type: OnSiteMessageType };

export function useLazyOnSiteMessages({ state, type }: UseLazyOnSiteMessagesProps) {
  const dispatch = useDispatch();

  const getOnSiteMessages = useCallback(() => {
    dispatch(getOnSiteMessagesAction({ state, type }));
  }, [dispatch, state, type]);

  const { loading, ids, total, error } = useSelector(selectStateTypedOnSiteMessages(state, type));
  const messages = useSelector(selectOnSiteMessagesEntities(ids));

  return [getOnSiteMessages, { loading, data: { data: messages, total }, error }] as const;
}
