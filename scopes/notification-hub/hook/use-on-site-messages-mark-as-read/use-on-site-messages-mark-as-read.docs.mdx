---
description: '将未读的通知中心-站内消息标记为已读。'
labels: ['hook', 'on-site-messages']
---

import { useOnSiteMessagesMarkAsRead } from './use-on-site-messages-mark-as-read';

## useOnSiteMessagesMarkAsRead

- 标记选择的消息：`markAsRead('selected')`

- 标记某个类型下所有的消息：`markAsRead('all')`

### 使用

```js
const [markAsRead, { loading }] = useOnSiteMessagesMarkAsRead({ type: OnSiteMessageType.All });

const onClick = useCallback(async () => {
  const result = await markAsRead('selected');
  message.info(`result: ${result}`);
}, [markAsRead]);

<Button type="primary" loading={loading} onClick={onClick}>
  Mark as read
</Button>;
```
