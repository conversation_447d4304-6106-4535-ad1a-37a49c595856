import { act, renderHook } from '@testing-library/react-hooks';

import { OnSiteMessageType } from '@manyun/notification-hub.model.on-site-messages';
// @ts-ignore
import { OnSiteMessagesReduxWrapper } from '@manyun/notification-hub.state.on-site-messages/dist/on-site-messages.composition-wrapper';
import { getMock } from '@manyun/service.request';

import { useOnSiteMessagesMarkAsRead } from './use-on-site-messages-mark-as-read';

const mock = getMock('web');

test('should toggle `loading` and resolve result', async () => {
  const { result } = renderHook(
    () => useOnSiteMessagesMarkAsRead({ type: OnSiteMessageType.All }),
    {
      wrapper: OnSiteMessagesReduxWrapper,
    }
  );

  mock.onAny().reply(200, { success: false });
  let _result: boolean | undefined = undefined;
  await act(async () => {
    _result = await result.current[0]({ variant: 'selected' });
  });

  expect((result.all as any)[0][1].loading).toBe(false);
  expect((result.all as any)[1][1].loading).toBe(true);
  expect(_result).toBe(false);

  mock.reset();
  mock.onAny().reply(200, { success: true });
  await act(async () => {
    _result = await result.current[0]({ variant: 'all' });
  });

  expect((result.all as any)[2][1].loading).toBe(false);
  expect((result.all as any)[3][1].loading).toBe(true);
  expect(_result).toBe(true);

  mock.restore();
});
