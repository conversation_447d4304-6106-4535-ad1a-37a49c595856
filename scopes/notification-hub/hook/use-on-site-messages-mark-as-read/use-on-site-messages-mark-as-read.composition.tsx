import React, { useCallback } from 'react';

import Button from 'antd/lib/button';
import 'antd/lib/button/style/css';
import message from 'antd/lib/message';
import 'antd/lib/message/style/css';

import { OnSiteMessageType } from '@manyun/notification-hub.model.on-site-messages';
// @ts-ignore
import { OnSiteMessagesReduxWrapper } from '@manyun/notification-hub.state.on-site-messages/dist/on-site-messages.composition-wrapper';
import { getMock } from '@manyun/service.request';

import { useOnSiteMessagesMarkAsRead } from './use-on-site-messages-mark-as-read';

const mock = getMock('web', { delayResponse: 3 * 1000 });
mock.onAny().reply(200, { success: true, data: true });

const Basic = () => {
  const [markAsRead, { loading }] = useOnSiteMessagesMarkAsRead({ type: OnSiteMessageType.All });

  const onClick = useCallback(async () => {
    const result = await markAsRead({ variant: 'selected' });
    message.info(`result: ${result}`);
  }, [markAsRead]);

  return (
    <Button type="primary" loading={loading} onClick={onClick}>
      Mark as read
    </Button>
  );
};

export const BasicuseOnSiteMessagesMarkAsRead = () => {
  return (
    <OnSiteMessagesReduxWrapper>
      <Basic />
    </OnSiteMessagesReduxWrapper>
  );
};
