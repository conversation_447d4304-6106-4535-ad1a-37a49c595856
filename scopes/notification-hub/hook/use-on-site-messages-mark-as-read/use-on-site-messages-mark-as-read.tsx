import { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import {
  OnSiteMessageState,
  OnSiteMessageType,
} from '@manyun/notification-hub.model.on-site-messages';
import type { SvcQuery } from '@manyun/notification-hub.service.mark-on-site-messages-as-read';
import {
  markAsReadAction,
  selectStateTypedOnSiteMessages,
} from '@manyun/notification-hub.state.on-site-messages';
import type { Variant } from '@manyun/service.dccm.mark-on-site-messages-as-read';

export type UseOnSiteMessagesMarkAsReadProps = {
  type: OnSiteMessageType;
};

export function useOnSiteMessagesMarkAsRead({ type }: UseOnSiteMessagesMarkAsReadProps) {
  const dispatch = useDispatch();

  const markAsRead = useCallback(
    ({ variant, successCb }: { variant: Variant; successCb?: (svcQuery: SvcQuery) => void }) => {
      return new Promise<boolean>(resolve => {
        dispatch(
          markAsReadAction({
            variant,
            type,
            callback: (result, svcQuery) => {
              if (result && typeof successCb === 'function') {
                successCb(svcQuery);
              }
              resolve(result);
            },
          })
        );
      });
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [type]
  );

  const { markingAsRead } = useSelector(
    selectStateTypedOnSiteMessages(OnSiteMessageState.Unread, type),
    (left, right) => left.markingAsRead === right.markingAsRead
  );

  return [markAsRead, { loading: markingAsRead }] as const;
}
