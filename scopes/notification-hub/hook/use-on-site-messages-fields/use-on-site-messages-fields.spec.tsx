import { act, renderHook } from '@testing-library/react-hooks';

import {
  OnSiteMessageState,
  OnSiteMessageType,
} from '@manyun/notification-hub.model.on-site-messages';
// @ts-ignore
import { OnSiteMessagesReduxWrapper } from '@manyun/notification-hub.state.on-site-messages/dist/on-site-messages.composition-wrapper';

import { useOnSiteMessagesFields } from './use-on-site-messages-fields';

test('should re-render when fields changed', () => {
  const { result } = renderHook(
    () =>
      useOnSiteMessagesFields({
        state: OnSiteMessageState.Unread,
        type: OnSiteMessageType.All,
      }),
    { wrapper: OnSiteMessagesReduxWrapper }
  );

  const [{ timeRange }, setFields] = result.current;

  expect(timeRange).toBe(undefined);
  expect(typeof setFields).toBe('function');

  const newTimeRange: [number, number] = [Date.now() - 60 * 1000, Date.now()];
  act(() => {
    setFields({
      timeRange: newTimeRange,
    });
  });

  expect(result.current[0].timeRange).toEqual(newTimeRange);
});
