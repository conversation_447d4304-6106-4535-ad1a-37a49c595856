import { useCallback } from 'react';
import { shallowEqual, useDispatch, useSelector } from 'react-redux';

import type {
  OnSiteMessageState,
  OnSiteMessageType,
} from '@manyun/notification-hub.model.on-site-messages';
import {
  onSiteMessagesSliceActions,
  selectStateTypedOnSiteMessages,
} from '@manyun/notification-hub.state.on-site-messages';
import type { SetFieldsActionPayload } from '@manyun/notification-hub.state.on-site-messages';

export type UseOnSiteMessagesFieldsProps = {
  state: OnSiteMessageState;
  type: OnSiteMessageType;
};

export function useOnSiteMessagesFields({ state, type }: UseOnSiteMessagesFieldsProps) {
  const { timeRange, page, pageSize } = useSelector(
    selectStateTypedOnSiteMessages(state, type),
    (left, right) =>
      shallowEqual(left.timeRange, right.timeRange) &&
      left.page === right.page &&
      left.pageSize === right.pageSize
  );

  const dispatch = useDispatch();
  const setFields = useCallback(
    ({
      timeRange,
      page,
      pageSize,
    }: Pick<SetFieldsActionPayload, 'timeRange' | 'page' | 'pageSize'>) => {
      dispatch(onSiteMessagesSliceActions.setFields({ state, type, timeRange, page, pageSize }));
    },
    [dispatch, state, type]
  );

  return [{ timeRange, page, pageSize }, setFields] as const;
}
