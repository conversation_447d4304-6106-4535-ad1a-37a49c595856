---
description: '获取/设置 `on-site-messages` 查询条件。'
labels: ['hook', 'on-site-messages', 'search-fields']
---

## useOnSiteMessagesFields

查询条件：

- `timeRange`: 创建时间的区间（[开始，结束]）。

`setFields`: 设置查询条件。

### 使用

```ts
const Demo = ({ state, type }) => {
  const [{ timeRange }, setFields] = useOnSiteMessagesFields({ state, type });

  return (
    <DatePicker.RangePicker
      value={timeRange.length < 2 ? [] : [moment(timeRange![0]), moment(timeRange![1])]}
      onChange={(moments) => {
        setFields({
          timeRange: moments.length < 2 ? [] : [moments[0]!.valueOf(), moments[1]!.valueOf()],
        });
      }}
    />
  );
};
```
