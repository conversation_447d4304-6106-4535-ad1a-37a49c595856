import React from 'react';

import DatePicker from 'antd/lib/date-picker';
import 'antd/lib/date-picker/style/css';
import moment from 'moment';

import {
  OnSiteMessageState,
  OnSiteMessageType,
} from '@manyun/notification-hub.model.on-site-messages';
// @ts-ignore
import { OnSiteMessagesReduxWrapper } from '@manyun/notification-hub.state.on-site-messages/dist/on-site-messages.composition-wrapper';

import { useOnSiteMessagesFields } from './use-on-site-messages-fields';

const Basic = () => {
  const [{ timeRange }, setFields] = useOnSiteMessagesFields({
    state: OnSiteMessageState.Unread,
    type: OnSiteMessageType.All,
  });

  return (
    <DatePicker.RangePicker
      value={timeRange.length < 2 ? undefined : [moment(timeRange[0]), moment(timeRange[1])]}
      onChange={dates => {
        if (!(dates && dates.length == 2 && dates[0] && dates[1])) {
          setFields({
            timeRange: [],
          });
          return;
        }
        setFields({
          timeRange: [dates[0].valueOf(), dates[1].valueOf()],
        });
      }}
    />
  );
};

export const BasicUseOnSiteMessagesFields = () => (
  <OnSiteMessagesReduxWrapper>
    <Basic />
  </OnSiteMessagesReduxWrapper>
);
