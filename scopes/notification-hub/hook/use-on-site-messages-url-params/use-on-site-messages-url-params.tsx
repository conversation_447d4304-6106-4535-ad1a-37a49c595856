import { useCallback } from 'react';
import { useHistory, useParams } from 'react-router-dom';

import type {
  OnSiteMessageState,
  OnSiteMessageType,
} from '@manyun/notification-hub.model.on-site-messages';
import { generateOnsiteMessageUrl } from '@manyun/notification-hub.route.notification-routes';

export type Params = {
  state: OnSiteMessageState;
  type: OnSiteMessageType;
};

export function useOnSiteMessagesUrlParams() {
  const { state, type } = useParams<Params>();
  const history = useHistory<undefined>();
  const setParams = useCallback(
    (params: Partial<Params>) => {
      history.push(
        generateOnsiteMessageUrl({ state: params.state || state, type: params.type || type })
      );
    },
    [history, state, type]
  );

  return [{ state, type }, setParams] as const;
}
