import React from 'react';

import { OnSiteMessagesRouterWrapper } from './composition-wrapper';
import { useOnSiteMessagesUrlParams } from './use-on-site-messages-url-params';

const DisplayOnSiteMessagesUrlParams = () => {
  const [{ state, type }] = useOnSiteMessagesUrlParams();

  return (
    <>
      <p>current message state: {state}</p>
      <p>current message type: {type}</p>
    </>
  );
};

export const BasicUseOnSiteMessagesUrlParams = () => {
  return (
    <OnSiteMessagesRouterWrapper state="unread">
      <DisplayOnSiteMessagesUrlParams />
    </OnSiteMessagesRouterWrapper>
  );
};
