---
description: '获取/设置 `on-site-messages` URL params 的 `React Hook`。'
labels: ['hook', 'on-site-messages', 'url-params']
---

import { useOnSiteMessagesUrlParams } from './use-on-site-messages-url-params';

## useOnSiteMessagesUrlParams

目前有 2 个 `URL params`:

- `state`: 消息状态（未读、已读）
- `type`: 消息类型（全部、公告、...）

提供了 `setParams` 方法来设置新的 `state` / `type`。

### 使用

获取 `state, type`, 并使用 `setParams` 方法。

```jsx
import { useOnSiteMessagesUrlParams } from '@manyun/notification-hub.hook.use-on-site-messages-url-params';

function OnSiteMessagesStateNTypeSwitcher() {
  const [{ state, type }, setParams] = useOnSiteMessagesUrlParams();

  return (
    <OnSiteMessagesStateSwitcher
      activeState={state}
      onChange={(newState) => {
        setParams({ state: newState });
      }}
    >
      <OnSiteMessagesTypeSwitcher
        activeType={type}
        onChange={(newType) => {
          setParams({ type: newType });
        }}
      />
    </OnSiteMessagesStateSwitcher>
  );
}
```
