import React from 'react';
import { Route, MemoryRouter as Router } from 'react-router-dom';

import {
  ON_SITE_MESSAGE_ROUTE_PATH,
  generateOnsiteMessageUrl,
} from '@manyun/notification-hub.route.notification-routes';

export type OnSiteMessagesRouterWrapperProps = {
  state: any;
  type?: any;
};

export const OnSiteMessagesRouterWrapper: React.FunctionComponent<
  OnSiteMessagesRouterWrapperProps
> = ({ state, type = 'all', children }) => {
  return (
    <Router initialEntries={[generateOnsiteMessageUrl({ state, type })]}>
      <Route path={ON_SITE_MESSAGE_ROUTE_PATH}>{children}</Route>
    </Router>
  );
};
