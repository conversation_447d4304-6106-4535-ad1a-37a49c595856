import React from 'react';

import { act, renderHook } from '@testing-library/react-hooks';

import { OnSiteMessageState } from '@manyun/notification-hub.model.on-site-messages';

import { OnSiteMessagesRouterWrapper } from './composition-wrapper';
import { useOnSiteMessagesUrlParams } from './use-on-site-messages-url-params';

test('should render "unread" state and "all" type', () => {
  const { result } = renderHook(() => useOnSiteMessagesUrlParams(), {
    wrapper: OnSiteMessagesRouterWrapper as any,
    initialProps: { state: OnSiteMessageState.Unread, type: 'all' },
  });
  const [{ state, type }] = result.current;

  expect(state).toBe(OnSiteMessageState.Unread);
  expect(type).toBe('all');
});

test('should render "read" state state and "all" type', () => {
  const { result } = renderHook(() => useOnSiteMessagesUrlParams(), {
    wrapper: OnSiteMessagesRouterWrapper as any,
    initialProps: { state: OnSiteMessageState.Unread },
  });
  const [{ state, type }, setParams] = result.current;

  expect(state).toBe(OnSiteMessageState.Unread);
  expect(typeof setParams).toBe('function');

  act(() => {
    setParams({ state: OnSiteMessageState.Unread });
  });

  expect(state).toBe(OnSiteMessageState.Unread);
});
