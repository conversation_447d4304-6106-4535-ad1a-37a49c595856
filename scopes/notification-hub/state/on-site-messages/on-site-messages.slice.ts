import { createSlice } from '@reduxjs/toolkit';
import type { CaseReducer, PayloadAction } from '@reduxjs/toolkit';

import {
  OnSiteMessageState,
  OnSiteMessageType,
} from '@manyun/notification-hub.model.on-site-messages';
import type { BackendOnSiteMessage } from '@manyun/notification-hub.model.on-site-messages';
import type { SvcRespData as SvcRespUnreadNumData } from '@manyun/notification-hub.service.fetch-on-site-messages-unread-num';

export interface ReadTypedOnSiteMessages {
  loading: boolean;
  error: string | null;
  ids: number[];
  total: number;
  page: number;
  pageSize: number;
  timeRange: [] | [number, number];
}

export interface UnreadTypedOnSiteMessages extends ReadTypedOnSiteMessages {
  markingAsRead: boolean;
  selectedIds: number[];
}

export type OnSiteMessagesSliceState = {
  entities: Record<string, BackendOnSiteMessage>;
  [OnSiteMessageState.Unread]: {
    [OnSiteMessageType.All]: UnreadTypedOnSiteMessages;
    [OnSiteMessageType.Approval]: UnreadTypedOnSiteMessages;
    [OnSiteMessageType.Asset]: UnreadTypedOnSiteMessages;
    [OnSiteMessageType.Event]: UnreadTypedOnSiteMessages;
    [OnSiteMessageType.Urge]: UnreadTypedOnSiteMessages;
    [OnSiteMessageType.Inventory]: UnreadTypedOnSiteMessages;
    [OnSiteMessageType.Notice]: UnreadTypedOnSiteMessages;
    [OnSiteMessageType.Ticket]: UnreadTypedOnSiteMessages;
    [OnSiteMessageType.Todo]: UnreadTypedOnSiteMessages;
    [OnSiteMessageType.ScheduleSubscribe]: UnreadTypedOnSiteMessages;
    [OnSiteMessageType.examSubscribe]: UnreadTypedOnSiteMessages;
    [OnSiteMessageType.Attendance]: UnreadTypedOnSiteMessages;
    [OnSiteMessageType.Performance]: UnreadTypedOnSiteMessages;
    [OnSiteMessageType.TestPerformance]: UnreadTypedOnSiteMessages;
    [OnSiteMessageType.Drill]: UnreadTypedOnSiteMessages;
    [OnSiteMessageType.SECURITY]: UnreadTypedOnSiteMessages;
    [OnSiteMessageType.RISK_REGISTER]: UnreadTypedOnSiteMessages;
    [OnSiteMessageType.REPORT]: UnreadTypedOnSiteMessages;
  };
  [OnSiteMessageState.Read]: {
    [OnSiteMessageType.All]: ReadTypedOnSiteMessages;
    [OnSiteMessageType.Approval]: ReadTypedOnSiteMessages;
    [OnSiteMessageType.Asset]: ReadTypedOnSiteMessages;
    [OnSiteMessageType.Event]: ReadTypedOnSiteMessages;
    [OnSiteMessageType.Urge]: ReadTypedOnSiteMessages;
    [OnSiteMessageType.Inventory]: ReadTypedOnSiteMessages;
    [OnSiteMessageType.Notice]: ReadTypedOnSiteMessages;
    [OnSiteMessageType.Ticket]: ReadTypedOnSiteMessages;
    [OnSiteMessageType.Todo]: ReadTypedOnSiteMessages;
    [OnSiteMessageType.ScheduleSubscribe]: UnreadTypedOnSiteMessages;
    [OnSiteMessageType.examSubscribe]: UnreadTypedOnSiteMessages;
    [OnSiteMessageType.Attendance]: ReadTypedOnSiteMessages;
    [OnSiteMessageType.Performance]: ReadTypedOnSiteMessages;
    [OnSiteMessageType.TestPerformance]: ReadTypedOnSiteMessages;
    [OnSiteMessageType.Drill]: ReadTypedOnSiteMessages;
    [OnSiteMessageType.SECURITY]: ReadTypedOnSiteMessages;
    [OnSiteMessageType.RISK_REGISTER]: ReadTypedOnSiteMessages;
    [OnSiteMessageType.REPORT]: ReadTypedOnSiteMessages;
  };
  unreadNumKeyMap: Record<OnSiteMessageType, number>;
};

export type SetFieldsActionPayload = {
  state: OnSiteMessageState;
  type: OnSiteMessageType;
  timeRange?: ReadTypedOnSiteMessages['timeRange'];
  page?: number;
  pageSize?: number;
};

export type OnSiteMessagesSliceCaseReducers = {
  setFields: CaseReducer<OnSiteMessagesSliceState, PayloadAction<SetFieldsActionPayload>>;
  setSelectedIds: CaseReducer<
    OnSiteMessagesSliceState,
    PayloadAction<{
      type: OnSiteMessageType;
      selectedIds: number[];
    }>
  >;
  fetchMessagesStart: CaseReducer<
    OnSiteMessagesSliceState,
    PayloadAction<{
      state: OnSiteMessageState;
      type: OnSiteMessageType;
    }>
  >;
  setMessages: CaseReducer<
    OnSiteMessagesSliceState,
    PayloadAction<{
      state: OnSiteMessageState;
      type: OnSiteMessageType;
      messages: BackendOnSiteMessage[];
      total: number;
    }>
  >;
  fetchMessagesError: CaseReducer<
    OnSiteMessagesSliceState,
    PayloadAction<{
      state: OnSiteMessageState;
      type: OnSiteMessageType;
      error: { message: string };
    }>
  >;
  toggleMarkingAsRead: CaseReducer<
    OnSiteMessagesSliceState,
    PayloadAction<{
      type: OnSiteMessageType;
    }>
  >;
  setUnreadNumKeyMap: CaseReducer<
    OnSiteMessagesSliceState,
    PayloadAction<{
      data: SvcRespUnreadNumData;
    }>
  >;
};

export const onSiteMessagesSlice = createSlice<
  OnSiteMessagesSliceState,
  OnSiteMessagesSliceCaseReducers,
  'notification-center.on-site-messages'
>({
  name: 'notification-center.on-site-messages',
  initialState: {
    entities: {},
    [OnSiteMessageState.Unread]: {
      [OnSiteMessageType.All]: getInitialUnreadTypedOnSiteMessages(),
      [OnSiteMessageType.Approval]: getInitialUnreadTypedOnSiteMessages(),
      [OnSiteMessageType.Asset]: getInitialUnreadTypedOnSiteMessages(),
      [OnSiteMessageType.Event]: getInitialUnreadTypedOnSiteMessages(),
      [OnSiteMessageType.Urge]: getInitialUnreadTypedOnSiteMessages(),
      [OnSiteMessageType.Inventory]: getInitialUnreadTypedOnSiteMessages(),
      [OnSiteMessageType.Notice]: getInitialUnreadTypedOnSiteMessages(),
      [OnSiteMessageType.Ticket]: getInitialUnreadTypedOnSiteMessages(),
      [OnSiteMessageType.Todo]: getInitialUnreadTypedOnSiteMessages(),
      [OnSiteMessageType.ScheduleSubscribe]: getInitialUnreadTypedOnSiteMessages(),
      [OnSiteMessageType.examSubscribe]: getInitialUnreadTypedOnSiteMessages(),
      [OnSiteMessageType.Attendance]: getInitialUnreadTypedOnSiteMessages(),
      [OnSiteMessageType.Performance]: getInitialUnreadTypedOnSiteMessages(),
      [OnSiteMessageType.TestPerformance]: getInitialUnreadTypedOnSiteMessages(),
      [OnSiteMessageType.Drill]: getInitialUnreadTypedOnSiteMessages(),
      [OnSiteMessageType.SECURITY]: getInitialUnreadTypedOnSiteMessages(),
      [OnSiteMessageType.RISK_REGISTER]: getInitialUnreadTypedOnSiteMessages(),
      [OnSiteMessageType.REPORT]: getInitialUnreadTypedOnSiteMessages(),
    },
    [OnSiteMessageState.Read]: {
      [OnSiteMessageType.All]: getInitialReadTypedOnSiteMessages(),
      [OnSiteMessageType.Approval]: getInitialReadTypedOnSiteMessages(),
      [OnSiteMessageType.Asset]: getInitialReadTypedOnSiteMessages(),
      [OnSiteMessageType.Event]: getInitialReadTypedOnSiteMessages(),
      [OnSiteMessageType.Urge]: getInitialReadTypedOnSiteMessages(),
      [OnSiteMessageType.Inventory]: getInitialReadTypedOnSiteMessages(),
      [OnSiteMessageType.Notice]: getInitialReadTypedOnSiteMessages(),
      [OnSiteMessageType.Ticket]: getInitialReadTypedOnSiteMessages(),
      [OnSiteMessageType.Todo]: getInitialReadTypedOnSiteMessages(),
      [OnSiteMessageType.ScheduleSubscribe]: getInitialUnreadTypedOnSiteMessages(),
      [OnSiteMessageType.examSubscribe]: getInitialUnreadTypedOnSiteMessages(),
      [OnSiteMessageType.Attendance]: getInitialReadTypedOnSiteMessages(),
      [OnSiteMessageType.Performance]: getInitialReadTypedOnSiteMessages(),
      [OnSiteMessageType.TestPerformance]: getInitialReadTypedOnSiteMessages(),
      [OnSiteMessageType.Drill]: getInitialReadTypedOnSiteMessages(),
      [OnSiteMessageType.SECURITY]: getInitialReadTypedOnSiteMessages(),
      [OnSiteMessageType.RISK_REGISTER]: getInitialReadTypedOnSiteMessages(),
      [OnSiteMessageType.REPORT]: getInitialReadTypedOnSiteMessages(),
    },
    unreadNumKeyMap: {
      [OnSiteMessageType.All]: 0,
      [OnSiteMessageType.Notice]: 0,
      [OnSiteMessageType.Event]: 0,
      [OnSiteMessageType.Urge]: 0,
      [OnSiteMessageType.Asset]: 0,
      [OnSiteMessageType.Ticket]: 0,
      [OnSiteMessageType.Inventory]: 0,
      [OnSiteMessageType.Approval]: 0,
      [OnSiteMessageType.Todo]: 0,
      [OnSiteMessageType.ScheduleSubscribe]: 0,
      [OnSiteMessageType.examSubscribe]: 0,
      [OnSiteMessageType.Attendance]: 0,
      [OnSiteMessageType.TestPerformance]: 0,
      [OnSiteMessageType.Performance]: 0,
      [OnSiteMessageType.Drill]: 0,
      [OnSiteMessageType.SECURITY]: 0,
      [OnSiteMessageType.RISK_REGISTER]: 0,
      [OnSiteMessageType.REPORT]: 0,
    },
  },
  reducers: {
    setFields(sliceState, { payload: { state, type, timeRange, page, pageSize } }) {
      if (timeRange !== undefined) {
        sliceState[state][type].timeRange = timeRange;
      }
      if (page !== undefined) {
        sliceState[state][type].page = page;
      }
      if (pageSize !== undefined) {
        sliceState[state][type].pageSize = pageSize;
      }
    },
    setSelectedIds(sliceState, { payload: { type, selectedIds } }) {
      sliceState[OnSiteMessageState.Unread][type].selectedIds = selectedIds;
    },
    fetchMessagesStart(sliceState, { payload: { state, type } }) {
      sliceState[state][type].ids = [];
      sliceState[state][type].error = null;
      sliceState[state][type].loading = true;
    },
    setMessages(sliceState, { payload: { state, type, messages, total } }) {
      const ids: number[] = [];
      messages.forEach(message => {
        ids.push(message.id);

        const existingMessage = sliceState.entities[message.id];
        if (existingMessage && existingMessage.gmtModify === message.gmtModify) {
          return;
        }
        sliceState.entities[message.id] = message;
      });
      sliceState[state][type].ids = ids;
      sliceState[state][type].loading = false;
      sliceState[state][type].total = total;
      if (
        state === OnSiteMessageState.Unread &&
        sliceState.unreadNumKeyMap[type] !== total &&
        sliceState.unreadNumKeyMap[type] < total
      ) {
        sliceState.unreadNumKeyMap[type] = total;
      }
    },
    fetchMessagesError(sliceState, { payload: { state, type, error } }) {
      sliceState[state][type].error = error.message;
      sliceState[state][type].loading = false;
    },
    toggleMarkingAsRead(sliceState, { payload: { type } }) {
      sliceState[OnSiteMessageState.Unread][type].markingAsRead =
        !sliceState[OnSiteMessageState.Unread][type].markingAsRead;
    },
    setUnreadNumKeyMap(sliceState, { payload: { data } }) {
      sliceState.unreadNumKeyMap = data;
    },
  },
});

export default onSiteMessagesSlice.reducer;

export function getInitialReadTypedOnSiteMessages(): ReadTypedOnSiteMessages {
  return {
    loading: false,
    error: null,
    ids: [],
    total: 0,
    page: 1,
    pageSize: 10,
    timeRange: [],
  };
}

export function getInitialUnreadTypedOnSiteMessages(): UnreadTypedOnSiteMessages {
  const initialReadTypedOnSiteMessages = getInitialReadTypedOnSiteMessages();

  return {
    ...initialReadTypedOnSiteMessages,
    markingAsRead: false,
    selectedIds: [],
  };
}
