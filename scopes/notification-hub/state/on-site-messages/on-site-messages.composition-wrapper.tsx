import React from 'react';
import { Provider } from 'react-redux';

import { configureStore } from '@reduxjs/toolkit';
import createSagaMiddleware from 'redux-saga';
import { all } from 'redux-saga/effects';

import onSiteMessagesWatchers from './on-site-messages.saga';
import { onSiteMessagesSlice } from './on-site-messages.slice';

const sagaMiddleware = createSagaMiddleware();
const store = configureStore({
  reducer: {
    [onSiteMessagesSlice.name]: onSiteMessagesSlice.reducer,
  },
  middleware: getDefaultMiddlewares => [
    ...getDefaultMiddlewares({ thunk: false, serializableCheck: false }),
    sagaMiddleware,
  ],
});

function* rootSaga() {
  yield all(onSiteMessagesWatchers);
}

sagaMiddleware.run(rootSaga);

export const OnSiteMessagesReduxWrapper: React.FunctionComponent = ({ children }) => (
  <Provider store={store}>{children}</Provider>
);
