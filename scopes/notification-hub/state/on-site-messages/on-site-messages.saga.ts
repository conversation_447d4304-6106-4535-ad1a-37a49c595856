// NOTE: DONOT REMOVE THIS IMPORT
// To fix `error TS2742: The inferred type of 'markAsReadSaga' cannot be named without a reference to '.pnpm/@manyun/service.request@0.0.2/node_modules/@manyun/service.request'. This is likely not portable. A type annotation is necessary.`
// import '@manyun/service.request';
import message from 'antd/es/message';
import { call, fork, put, select, takeLatest } from 'redux-saga/effects';
import type { SagaReturnType } from 'redux-saga/effects';

import { OnSiteMessageState } from '@manyun/notification-hub.model.on-site-messages';
import { fetchOnSiteMessages } from '@manyun/notification-hub.service.fetch-on-site-messages';
import { fetchOnSiteMessagesUnreadNum } from '@manyun/notification-hub.service.fetch-on-site-messages-unread-num';
import { markOnSiteMessagesAsRead } from '@manyun/notification-hub.service.mark-on-site-messages-as-read';
import type { SvcQuery } from '@manyun/notification-hub.service.mark-on-site-messages-as-read';

import {
  getOnSiteMessagesAction,
  getOnsiteMessagesUnreadNumAction,
  markAsReadAction,
  onSiteMessagesSliceActions,
} from './on-site-messages.action';
import { selectStateTypedOnSiteMessages } from './on-site-messages.selector';
import type { UnreadTypedOnSiteMessages } from './on-site-messages.slice';

/** Workers */

export function* getOnSiteMessagesSaga({
  payload: { state, type },
}: ReturnType<typeof getOnSiteMessagesAction>) {
  yield put(onSiteMessagesSliceActions.fetchMessagesStart({ state, type }));

  const existing: SagaReturnType<ReturnType<typeof selectStateTypedOnSiteMessages>> = yield select(
    selectStateTypedOnSiteMessages(state, type)
  );

  if (
    state === OnSiteMessageState.Unread &&
    (existing as UnreadTypedOnSiteMessages).selectedIds.length > 0
  ) {
    yield put(onSiteMessagesSliceActions.setSelectedIds({ type, selectedIds: [] }));
  }

  const { error, data }: SagaReturnType<typeof fetchOnSiteMessages> = yield call<
    typeof fetchOnSiteMessages
  >(fetchOnSiteMessages, {
    timeRange: existing.timeRange.length !== 2 ? undefined : existing.timeRange,
    state,
    type,
    page: existing.page,
    pageSize: existing.pageSize,
  });

  if (error) {
    message.error(error.message);
    yield put(onSiteMessagesSliceActions.fetchMessagesError({ state, type, error }));
    return;
  }

  yield put(
    onSiteMessagesSliceActions.setMessages({ state, type, messages: data.data, total: data.total })
  );
}

export function* markAsReadSaga({
  payload: { type, variant, callback },
}: ReturnType<
  typeof markAsReadAction
>): /* To fix TS compile error `error TS2742: The inferred type of 'markAsReadSaga' cannot be named without a reference to '.pnpm/@manyun/service.request@0.0.2/node_modules/@manyun/service.request'. This is likely not portable. A type annotation is necessary.` */ Generator<
  any,
  any,
  any
> {
  yield put(onSiteMessagesSliceActions.toggleMarkingAsRead({ type }));

  const existing: UnreadTypedOnSiteMessages = yield select(
    selectStateTypedOnSiteMessages(OnSiteMessageState.Unread, type)
  );

  const svcQuery: SvcQuery = {
    variant,
    type,
    ids: existing.selectedIds,
    timeRange: existing.timeRange.length ? existing.timeRange : undefined,
  };
  const { error, data }: SagaReturnType<typeof markOnSiteMessagesAsRead> = yield call(
    markOnSiteMessagesAsRead,
    svcQuery
  );

  yield put(onSiteMessagesSliceActions.toggleMarkingAsRead({ type }));

  if (error) {
    message.error(error.message);
    callback(false, svcQuery);
    return;
  }

  message.success('标记已读成功！');
  callback(true, svcQuery);
  yield put(onSiteMessagesSliceActions.setSelectedIds({ type, selectedIds: [] }));
  yield put(getOnSiteMessagesAction({ state: OnSiteMessageState.Unread, type }));
}

export function* getOnsiteMessagesUnreadNum() {
  const { error, data }: SagaReturnType<typeof fetchOnSiteMessagesUnreadNum> = yield call(
    fetchOnSiteMessagesUnreadNum
  );
  if (error) {
    message.error(error.message);
    return;
  }
  yield put(onSiteMessagesSliceActions.setUnreadNumKeyMap({ data }));
}

/** Watchers */

function* watchGetOnSiteMessages() {
  yield takeLatest(getOnSiteMessagesAction.type, getOnSiteMessagesSaga);
}

function* watchMarkAsRead() {
  yield takeLatest(markAsReadAction.type, markAsReadSaga);
}

function* watchGetOnsiteMessagesUnreadNum() {
  yield takeLatest(getOnsiteMessagesUnreadNumAction.type, getOnsiteMessagesUnreadNum);
}

export default [
  fork(watchGetOnSiteMessages),
  fork(watchMarkAsRead),
  fork(watchGetOnsiteMessagesUnreadNum),
];
