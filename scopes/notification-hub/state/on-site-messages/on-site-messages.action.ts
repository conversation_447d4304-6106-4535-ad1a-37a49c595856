import { createAction } from '@reduxjs/toolkit';

import {
  OnSiteMessageState,
  OnSiteMessageType,
} from '@manyun/notification-hub.model.on-site-messages';
import type { SvcQuery } from '@manyun/notification-hub.service.mark-on-site-messages-as-read';
import type { Variant } from '@manyun/service.dccm.mark-on-site-messages-as-read';

import { onSiteMessagesSlice } from './on-site-messages.slice';

const prefix = onSiteMessagesSlice.name;

export const onSiteMessagesSliceActions = onSiteMessagesSlice.actions;

export const getOnSiteMessagesAction = createAction<{
  state: OnSiteMessageState;
  type: OnSiteMessageType;
}>(prefix + '/GET_ON_SITE_MESSAGES');

export const markAsReadAction = createAction<{
  type: OnSiteMessageType;
  variant: Variant;
  callback: (result: boolean, svcQuery: SvcQuery) => void;
}>(prefix + '/MARK_AS_READ');

export const getOnsiteMessagesUnreadNumAction = createAction(
  prefix + '/GET_ON_SITE_MESSAGES_UNREAD_NUM'
);
