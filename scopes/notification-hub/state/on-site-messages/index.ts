import onSiteMessagesWatchers from './on-site-messages.saga';
import { onSiteMessagesSlice } from './on-site-messages.slice';

export type {
  OnSiteMessagesSliceState,
  UnreadTypedOnSiteMessages,
  ReadTypedOnSiteMessages,
  SetFieldsActionPayload,
} from './on-site-messages.slice';
export * from './on-site-messages.action';
export * from './on-site-messages.selector';
export { onSiteMessagesWatchers };
export default {
  [onSiteMessagesSlice.name]: onSiteMessagesSlice.reducer,
};
