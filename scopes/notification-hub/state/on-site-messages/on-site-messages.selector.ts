import {
  OnSiteMessageState,
  OnSiteMessageType,
} from '@manyun/notification-hub.model.on-site-messages';

import { onSiteMessagesSlice } from './on-site-messages.slice';
import type { OnSiteMessagesSliceState } from './on-site-messages.slice';

export const selectOnSiteMessagesEntities =
  (ids?: number[]) => (storeState: { [onSiteMessagesSlice.name]: OnSiteMessagesSliceState }) => {
    const { entities } = storeState[onSiteMessagesSlice.name];

    if (ids === undefined) {
      return Object.keys(entities).map(id => entities[id]);
    }

    return ids.map(id => entities[id.toString()]);
  };

export const selectStateTypedOnSiteMessages =
  <
    S extends OnSiteMessageState = OnSiteMessageState,
    T extends OnSiteMessageType = OnSiteMessageType
  >(
    state: S,
    type: T
  ) =>
  (storeState: {
    [onSiteMessagesSlice.name]: OnSiteMessagesSliceState;
  }): OnSiteMessagesSliceState[S][T] =>
    storeState[onSiteMessagesSlice.name][state][type];

export const selecOnSiteMessagesUnreadNum =
  () =>
  (storeState: {
    [onSiteMessagesSlice.name]: OnSiteMessagesSliceState;
  }): OnSiteMessagesSliceState['unreadNumKeyMap'] =>
    storeState[onSiteMessagesSlice.name].unreadNumKeyMap;
