/* THIS IS A BIT-AUTO-GENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY. */

/**
 * The Bitmap file is an auto generated file used by Bit to track all your Bit components. It maps the component to a folder in your file system.
 * This file should be committed to VCS(version control).
 * Components are listed using their component ID (https://bit.dev/reference/components/component-id).
 * If you want to delete components you can use the "bit remove <component-id>" command.
 * See the docs (https://bit.dev/reference/components/removing-components) for more information, or use "bit remove --help".
 */

{
    "hook/use-lazy-on-site-messages": {
        "name": "hook/use-lazy-on-site-messages",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-lazy-on-site-messages"
    },
    "hook/use-on-site-messages-fields": {
        "name": "hook/use-on-site-messages-fields",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-on-site-messages-fields"
    },
    "hook/use-on-site-messages-mark-as-read": {
        "name": "hook/use-on-site-messages-mark-as-read",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-on-site-messages-mark-as-read"
    },
    "hook/use-on-site-messages-selected": {
        "name": "hook/use-on-site-messages-selected",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-on-site-messages-selected"
    },
    "hook/use-on-site-messages-url-params": {
        "name": "hook/use-on-site-messages-url-params",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-on-site-messages-url-params"
    },
    "hook/use-on-size-messages-unread-num": {
        "name": "hook/use-on-size-messages-unread-num",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-on-size-messages-unread-num"
    },
    "model/notice": {
        "name": "model/notice",
        "scope": "manyun.notification-hub",
        "version": "0.0.1",
        "mainFile": "index.ts",
        "rootDir": "model/notice",
        "config": {
            "teammc.snowcone/node-env@2.2.9": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-env"
            }
        }
    },
    "model/notice-config": {
        "name": "model/notice-config",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "model/notice-config",
        "config": {
            "teammc.snowcone/node-env@2.2.9": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-env"
            }
        }
    },
    "model/on-site-messages": {
        "name": "model/on-site-messages",
        "scope": "manyun.notification-hub",
        "version": "0.0.7",
        "mainFile": "index.ts",
        "rootDir": "model/on-site-messages",
        "config": {
            "teammc.snowcone/node-env@2.2.9": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-env"
            }
        }
    },
    "page/alarm-notifications": {
        "name": "page/alarm-notifications",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "page/alarm-notifications",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/notification-channels-config": {
        "name": "page/notification-channels-config",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "page/notification-channels-config",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/on-site-messages": {
        "name": "page/on-site-messages",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "page/on-site-messages",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/webhook-config": {
        "name": "page/webhook-config",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "page/webhook-config",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "route/notification-routes": {
        "name": "route/notification-routes",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "route/notification-routes",
        "config": {
            "teammc.snowcone/react-esm-env@2.0.12": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/react-esm-env"
            }
        }
    },
    "service/add-notice": {
        "name": "service/add-notice",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "service/add-notice",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/bind-webhook-with-resources": {
        "name": "service/bind-webhook-with-resources",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "service/bind-webhook-with-resources",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/bind-webhook-with-scenes": {
        "name": "service/bind-webhook-with-scenes",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "service/bind-webhook-with-scenes",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/create-webhook": {
        "name": "service/create-webhook",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "service/create-webhook",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/delete-webhook": {
        "name": "service/delete-webhook",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "service/delete-webhook",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-active-notices": {
        "name": "service/fetch-active-notices",
        "scope": "manyun.notification-hub",
        "version": "0.0.3",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-active-notices",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-alarm-notification": {
        "name": "service/fetch-alarm-notification",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-alarm-notification",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-alarm-notifications": {
        "name": "service/fetch-alarm-notifications",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-alarm-notifications",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-notice": {
        "name": "service/fetch-notice",
        "scope": "manyun.notification-hub",
        "version": "0.0.3",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-notice",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-on-site-messages": {
        "name": "service/fetch-on-site-messages",
        "scope": "manyun.notification-hub",
        "version": "0.0.10",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-on-site-messages",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-on-site-messages-oow-devices": {
        "name": "service/fetch-on-site-messages-oow-devices",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-on-site-messages-oow-devices",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-on-site-messages-unread-num": {
        "name": "service/fetch-on-site-messages-unread-num",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-on-site-messages-unread-num",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-popup-list": {
        "name": "service/fetch-popup-list",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-popup-list",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-webhook-binded-scenes": {
        "name": "service/fetch-webhook-binded-scenes",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-webhook-binded-scenes",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-webhook-blocks": {
        "name": "service/fetch-webhook-blocks",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-webhook-blocks",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-webhook-scenes": {
        "name": "service/fetch-webhook-scenes",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-webhook-scenes",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-webhooks": {
        "name": "service/fetch-webhooks",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-webhooks",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/mark-on-site-messages-as-read": {
        "name": "service/mark-on-site-messages-as-read",
        "scope": "manyun.notification-hub",
        "version": "0.0.12",
        "mainFile": "index.ts",
        "rootDir": "service/mark-on-site-messages-as-read",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/notify/create-channel": {
        "name": "service/notify/create-channel",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "service/notify/create-channel",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/notify/delete-channel": {
        "name": "service/notify/delete-channel",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "service/notify/delete-channel",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/notify/fetch-channels": {
        "name": "service/notify/fetch-channels",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "service/notify/fetch-channels",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/notify/fetch-notice-templates": {
        "name": "service/notify/fetch-notice-templates",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "service/notify/fetch-notice-templates",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/notify/update-channel": {
        "name": "service/notify/update-channel",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "service/notify/update-channel",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/notify/update-notice-template": {
        "name": "service/notify/update-notice-template",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "service/notify/update-notice-template",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/readed-notice-popup": {
        "name": "service/readed-notice-popup",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "service/readed-notice-popup",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-notice": {
        "name": "service/update-notice",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "service/update-notice",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-publish-notice": {
        "name": "service/update-publish-notice",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "service/update-publish-notice",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-webhook": {
        "name": "service/update-webhook",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "service/update-webhook",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "state/on-site-messages": {
        "name": "state/on-site-messages",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "state/on-site-messages"
    },
    "ui/announcement-remind-notification": {
        "name": "ui/announcement-remind-notification",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/announcement-remind-notification",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/channel-config": {
        "name": "ui/channel-config",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/channel-config",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/on-site-message-content": {
        "name": "ui/on-site-message-content",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/on-site-message-content",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/on-site-messages": {
        "name": "ui/on-site-messages",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/on-site-messages",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/on-site-messages-filters": {
        "name": "ui/on-site-messages-filters",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/on-site-messages-filters",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/on-site-messages-mark-as-read": {
        "name": "ui/on-site-messages-mark-as-read",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/on-site-messages-mark-as-read",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/on-site-messages-oow-devices": {
        "name": "ui/on-site-messages-oow-devices",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/on-site-messages-oow-devices",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/on-site-messages-state-switcher": {
        "name": "ui/on-site-messages-state-switcher",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/on-site-messages-state-switcher",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/on-site-messages-type-switcher": {
        "name": "ui/on-site-messages-type-switcher",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/on-site-messages-type-switcher",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/receiver": {
        "name": "ui/receiver",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.notification-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/receiver",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "$schema-version": "15.0.0"
}