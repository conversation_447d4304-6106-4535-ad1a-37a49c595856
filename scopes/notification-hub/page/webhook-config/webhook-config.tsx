import React, { useEffect, useState } from 'react';

import { Card } from '@manyun/base-ui.ui.card';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { QueryFilter } from '@manyun/base-ui.ui.query-filter';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';

import {
  WebhookModal,
  WebhookScenesSelect,
  WebhookTable,
} from '@manyun/notification-hub.page.webhook-config';
import type { Webhook } from '@manyun/notification-hub.service.fetch-webhooks';
import { fetchWebhooks } from '@manyun/notification-hub.service.fetch-webhooks';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';

export type WebhookFilterFormValues = { location: string[]; scenes: string; status: boolean };
export function WebhookConfig() {
  const [dataSource, setDataSource] = useState<Webhook[]>([]);
  const [tablePage, setTablePage] = useState<{ pageNum: number; pageSize: number }>({
    pageNum: 1,
    pageSize: 10,
  });
  const [total, setTotal] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(false);
  const [formFilterValue, setFormFilterValue] = useState<WebhookFilterFormValues | {}>({});
  const [form] = Form.useForm<WebhookFilterFormValues>();
  const defaultPagiantion = { pageNum: 1, pageSize: 10 };
  const onReset = async () => {
    setFormFilterValue({});
    setLoading(true);
    const { error, data } = await fetchWebhooks(defaultPagiantion);
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    setDataSource(data.data);
    setTablePage(defaultPagiantion);

    setTotal(data.total);
  };
  const onSearch = async (value: WebhookFilterFormValues) => {
    setFormFilterValue(value);

    setLoading(true);
    const { error, data } = await fetchWebhooks({ ...defaultPagiantion, ...value });
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    setDataSource(data.data);
    setTablePage(defaultPagiantion);

    setTotal(data.total);
  };
  useEffect(() => {
    (async function () {
      setLoading(true);
      const { error, data } = await fetchWebhooks(defaultPagiantion);
      setLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      setDataSource(data.data);
      setTotal(data.total);
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  const onFinish = async (pagenation?: { pageNum: number; pageSize: number }) => {
    setLoading(true);
    const { error, data } = await fetchWebhooks({
      ...formFilterValue,
      pageNum: pagenation ? pagenation.pageNum : 1,
      pageSize: pagenation ? pagenation.pageSize : 10,
    });
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    setDataSource(data.data);
    setTotal(data.total);
    setTablePage(pagenation ?? defaultPagiantion);
  };
  return (
    <Card title="Webhook">
      <Space direction="vertical" style={{ width: '100%' }}>
        <Space align="start">
          <WebhookModal mode="new" onFinish={onFinish} />
          <QueryFilter<WebhookFilterFormValues>
            form={form}
            items={[
              {
                label: '名称',
                name: 'name',
                rules: [{ max: 64, message: '不能超过64字符' }],
                control: <Input allowClear />,
              },
              {
                label: '楼栋',
                name: 'location',
                control: (
                  <LocationTreeSelect multiple allowClear authorizedOnly includeVirtualBlocks />
                ),
              },
              {
                label: '通报场景',
                name: 'scenes',
                control: <WebhookScenesSelect trigger="onDidMount" allowClear />,
              },
              {
                label: '启用状态',
                name: 'status',
                control: (
                  <Select allowClear>
                    <Select.Option value={true}>启用</Select.Option>
                    <Select.Option value={false}>停用</Select.Option>
                  </Select>
                ),
              },
            ]}
            onSearch={onSearch}
            onReset={onReset}
          />
        </Space>
        <WebhookTable
          loading={loading}
          tablePage={tablePage}
          setTablePage={setTablePage}
          formFilterValue={formFilterValue}
          setLoading={setLoading}
          dataSource={dataSource}
          setDataSource={setDataSource}
          onFinish={onFinish}
          total={total}
        />
      </Space>
    </Card>
  );
}
