import React, { useEffect, useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Switch } from '@manyun/base-ui.ui.switch';

import { createWebhook } from '@manyun/notification-hub.service.create-webhook';
import type { Webhook } from '@manyun/notification-hub.service.fetch-webhooks';
import { updateWebhook } from '@manyun/notification-hub.service.update-webhook';

export type WebhookModalProps = {
  mode: 'edit' | 'new';
  detailInfo?: Webhook;
  onFinish: () => void;
};
export function WebhookModal({ mode, detailInfo, onFinish }: WebhookModalProps) {
  const [visible, setVisible] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [form] = Form.useForm();
  const onOk = () => {
    form.validateFields().then(async formValue => {
      setLoading(true);
      if (!isEditMode(mode)) {
        const { error } = await createWebhook({
          ...formValue,
        });
        setLoading(false);

        if (error) {
          message.error(error.message);
          return;
        }
        setVisible(false);

        onFinish();
        return;
      }
      const { error } = await updateWebhook({
        ...formValue,
        webHookId: detailInfo?.id,
      });
      setLoading(false);

      if (error) {
        message.error(error.message);
        return;
      }
      setVisible(false);

      onFinish();
    });
  };
  const showModal = () => {
    setVisible(true);
  };
  const closeModal = () => {
    setVisible(false);
  };

  useEffect(() => {
    if (visible) {
      form.resetFields();
    }
  }, [form, visible]);

  return (
    <>
      {isEditMode(mode) ? (
        <Button type="link" compact onClick={showModal}>
          编辑
        </Button>
      ) : (
        <Button type="primary" onClick={showModal}>
          新增Webhook
        </Button>
      )}
      <Modal
        title={isEditMode(mode) ? '编辑Webhook' : '新增Webhook'}
        okButtonProps={{ loading }}
        open={visible}
        okText="确认"
        onOk={onOk}
        onCancel={closeModal}
      >
        <Form form={form} labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
          <Form.Item
            label="Webhook名称"
            name="name"
            rules={[
              { required: true, message: '请输入Webhook名称' },
              {
                max: 64,
                message: '不能超过64字符',
              },
            ]}
            initialValue={detailInfo?.webHookName}
          >
            <Input allowClear />
          </Form.Item>
          <Form.Item
            label="Webhook地址"
            name="address"
            rules={[
              { required: true, message: '请输入Webhook地址' },
              {
                max: 256,
                message: '不能超过256字符',
              },
            ]}
            initialValue={detailInfo?.address}
          >
            <Input allowClear />
          </Form.Item>
          <Form.Item
            label="Webhook密钥"
            name="secret"
            rules={[
              { required: true, message: '请输入Webhook密钥' },
              {
                max: 256,
                message: '不能超过256字符',
              },
            ]}
            initialValue={detailInfo?.secret}
          >
            <Input type="password" allowClear />
          </Form.Item>
          <Form.Item
            label="启用状态"
            name="usable"
            valuePropName="checked"
            rules={[{ required: true, message: '请设置启用状态' }]}
            initialValue={detailInfo?.usable ?? true}
          >
            <Switch />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}

const isEditMode = (mode: string) => {
  return mode === 'edit';
};
