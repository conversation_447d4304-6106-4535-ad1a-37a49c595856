import React, { useEffect, useState } from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

import { fetchWebhookScenes } from '@manyun/notification-hub.service.fetch-webhook-scenes';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type WebhookScenesSelectProps = { trigger: 'onDidMount' | 'onFocus' } & SelectProps<any>;

export const WebhookScenesSelect = React.forwardRef(
  (
    { trigger = 'onDidMount', onFocus, ...props }: WebhookScenesSelectProps,
    ref?: React.Ref<RefSelectProps>
  ) => {
    const [options, setOptions] = useState<{ label: string; value: string }[]>([]);
    const [loading, setLoading] = useState<boolean>(false);

    useEffect(() => {
      if (trigger === 'onDidMount') {
        getScenes();
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const getScenes = async () => {
      setLoading(true);
      const { error, data } = await fetchWebhookScenes({ channel: 'WEB_HOOK' });
      setLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      setOptions(data.data.map(item => ({ label: item.name, value: item.notifyCode })));
    };

    return (
      <Select
        ref={ref}
        {...props}
        loading={loading}
        value={typeof props.value === 'number' ? String(props.value) : props.value}
        options={options}
        onFocus={evt => {
          if (trigger === 'onFocus') {
            getScenes();
          }
          onFocus?.(evt);
        }}
      />
    );
  }
);
