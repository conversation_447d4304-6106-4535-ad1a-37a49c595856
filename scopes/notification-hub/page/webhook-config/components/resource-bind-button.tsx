import React, { useEffect, useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Transfer } from '@manyun/base-ui.ui.transfer';
import { Tree } from '@manyun/base-ui.ui.tree';

import { flattenTreeData } from '@manyun/dc-brain.util.flatten-tree-data';
import { bindWebhookWithResources } from '@manyun/notification-hub.service.bind-webhook-with-resources';
import { fetchWebhookBlocks } from '@manyun/notification-hub.service.fetch-webhook-blocks';
import { useSpaces } from '@manyun/resource-hub.ui.location-tree-select';
import type { SpaceTreeNode } from '@manyun/resource-hub.ui.location-tree-select';

export type ResourceBindButtonProps = {
  webhookId: number;
  onFinish: () => void;
};
export type SpaceNode = {
  key: string;
} & SpaceTreeNode;
export function ResourceBindButton({ webhookId, onFinish }: ResourceBindButtonProps) {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [targetKeys, setTargetKeys] = useState<string[]>([]);
  const onChange = (keys: string[]) => {
    setTargetKeys(keys);
  };
  useEffect(() => {
    if (!visible) {
      setTargetKeys([]);
    }
    (async function () {
      const { error, data } = await fetchWebhookBlocks({ webHookId: webhookId });
      if (error) {
        message.error(error.message);
        return;
      }
      setTargetKeys(data.data);
    })();

    return () => {
      setTargetKeys([]);
    };
  }, [webhookId, visible]);
  const [{ treeSpaces }] = useSpaces({
    authorizedOnly: true,
    includeVirtualBlocks: false,
    disabledTypes: ['IDC'],
    nodeTypes: ['IDC', 'BLOCK'],
  });

  const transferDataSource = flattenTreeData(treeSpaces);
  const generateTree = (
    treeNodes: SpaceTreeNode[] | undefined = [],
    checkedKeys: string[] = []
  ): SpaceNode[] =>
    treeNodes.map(({ children, ...props }) => ({
      ...props,
      key: props.value,
      disabled: props.value.includes('.')
        ? checkedKeys.includes(props.value as string)
        : props.disabled,
      children: generateTree(children, checkedKeys),
    }));

  const onOk = async () => {
    setLoading(true);

    const { error } = await bindWebhookWithResources({
      webHookId: webhookId,
      blockGuidList: targetKeys,
    });
    setLoading(false);

    if (error) {
      message.error(error.message);
      return;
    }
    setVisible(false);
    message.success('关联资源成功');
    onFinish();
  };
  const showModal = () => {
    setVisible(true);
  };
  const closeModal = () => {
    setVisible(false);
  };
  const isChecked = (selectedKeys: (string | number)[], eventKey: string | number) =>
    selectedKeys.includes(eventKey);
  return (
    <>
      <Button type="link" compact onClick={showModal}>
        关联资源
      </Button>

      <Drawer
        size="large"
        title="关联资源"
        open={visible}
        extra={
          <Space>
            <Button onClick={closeModal}>取消</Button>
            <Button loading={loading} type="primary" onClick={onOk}>
              保存
            </Button>
          </Space>
        }
        onClose={closeModal}
      >
        <Transfer
          targetKeys={targetKeys}
          dataSource={transferDataSource}
          showSelectAll={false}
          rowKey={record => record.value}
          render={item => item.value}
          onChange={onChange}
        >
          {({ direction, onItemSelect, selectedKeys }) => {
            if (direction === 'left') {
              const checkedKeys = [...selectedKeys, ...targetKeys];
              return (
                <Tree
                  style={{
                    padding: 12,
                    overflowY: 'hidden',
                  }}
                  height={644}
                  checkable
                  checkStrictly
                  checkedKeys={checkedKeys}
                  treeData={generateTree(treeSpaces, targetKeys)}
                  onCheck={(_, { node: { key } }) => {
                    onItemSelect(key as string, !isChecked(checkedKeys, key));
                  }}
                  onSelect={(_, { node: { key } }) => {
                    onItemSelect(key as string, !isChecked(checkedKeys, key));
                  }}
                />
              );
            }
            return;
          }}
        </Transfer>
      </Drawer>
    </>
  );
}
