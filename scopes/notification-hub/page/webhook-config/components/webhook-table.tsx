import React from 'react';

import dayjs from 'dayjs';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import { Switch } from '@manyun/base-ui.ui.switch';
import { Table } from '@manyun/base-ui.ui.table';

import { User } from '@manyun/auth-hub.ui.user';
import {
  ResourceBindButton,
  ScenesBindModal,
  WebhookModal,
} from '@manyun/notification-hub.page.webhook-config';
import type { WebhookFilterFormValues } from '@manyun/notification-hub.page.webhook-config';
import { deleteWebhook } from '@manyun/notification-hub.service.delete-webhook';
import type { Webhook } from '@manyun/notification-hub.service.fetch-webhooks';
import { updateWebhook } from '@manyun/notification-hub.service.update-webhook';

export type WebhookTableProps = {
  dataSource: Webhook[];
  setDataSource: (dataSource: Webhook[]) => void;
  loading: boolean;
  setLoading: (value: boolean) => void;
  formFilterValue: WebhookFilterFormValues | {};
  onFinish: (pagenation?: { pageNum: number; pageSize: number }) => void;
  total: number;
  tablePage: { pageNum: number; pageSize: number };
  setTablePage: (pagination: { pageNum: number; pageSize: number }) => void;
};

export function WebhookTable({
  dataSource,
  setDataSource,
  loading,
  setLoading,
  formFilterValue,
  onFinish,
  total,
  tablePage,
  setTablePage,
}: WebhookTableProps) {
  return (
    <Table
      rowKey="id"
      loading={loading}
      columns={columns(onFinish)}
      scroll={{ x: 'max-content' }}
      dataSource={dataSource}
      pagination={{
        total: total,
        current: tablePage.pageNum,
        pageSize: tablePage.pageSize,
      }}
      onChange={pagination => {
        setTablePage({
          pageNum: pagination.current!,
          pageSize: pagination.pageSize!,
        });
        onFinish({ pageNum: pagination.current!, pageSize: pagination.pageSize! });
      }}
    />
  );
}

const columns = (onFinish: () => void) => [
  { title: 'Webhook名称', dataIndex: 'webHookName', width: '50%' },
  {
    title: '启用状态',
    dataIndex: 'usable',
    render: (status: boolean, record: Webhook) => (
      <Switch
        checked={status}
        onChange={checked => {
          updateWebhookStatus(checked, record, onFinish);
        }}
      />
    ),
  },
  {
    title: '操作人',
    dataIndex: 'operator',
    render: (id: string) => <User.Link id={Number(id)} />,
  },
  {
    title: '操作时间',
    dataIndex: 'gmtModified',
    render: (text: string) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
  },
  {
    title: '操作',
    render: (_: string, record: Webhook) => {
      return (
        <Space>
          <WebhookModal mode="edit" detailInfo={record} onFinish={onFinish} />
          <ResourceBindButton webhookId={record.id} onFinish={onFinish} />
          <ScenesBindModal webhookId={record.id} onFinish={onFinish} />
          <Popconfirm
            title="您即将删除该数据"
            onConfirm={async () => {
              const { error } = await deleteWebhook({ webHookId: record.id });
              if (error) {
                message.error(error.message);
                return;
              }
              message.success('删除成功');
              onFinish();
            }}
          >
            <Button type="link" compact>
              删除
            </Button>
          </Popconfirm>
        </Space>
      );
    },
  },
];

const updateWebhookStatus = async (value: boolean, record: Webhook, onFinish: () => void) => {
  const { error } = await updateWebhook({
    webHookId: record.id,
    name: record.webHookName,
    address: record.address,
    secret: record.secret,
    usable: value,
  });
  if (error) {
    message.error(error.message);
    return;
  }
  onFinish();
};
