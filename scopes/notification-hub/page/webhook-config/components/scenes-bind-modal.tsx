import React, { useState } from 'react';

import { Badge } from '@manyun/base-ui.ui.badge';
import { Button } from '@manyun/base-ui.ui.button';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Space } from '@manyun/base-ui.ui.space';
import { bindWebhookWithScenes } from '@manyun/notification-hub.service.bind-webhook-with-scenes';
import { fetchWebhookBindedScenes } from '@manyun/notification-hub.service.fetch-webhook-binded-scenes';
import { fetchWebhookScenes } from '@manyun/notification-hub.service.fetch-webhook-scenes';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { fetchMetaDataByType } from '@manyun/resource-hub.service.fetch-meta-data-by-type';

export type ScenesBindModalProps = {
  webhookId: number;
  onFinish: () => void;
};
export function ScenesBindModal({ webhookId, onFinish }: ScenesBindModalProps) {
  const [visible, setVisible] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [alarmValue, setAlarmValue] = useState<string>();
  const [otherValues, setOtherValues] = useState<string[]>([]);
  const [options, setOptions] = useState<
    { name: string; code: string; options: { label: string; value: string }[] }[]
  >([]);

  const initFn = async () => {
    /*所有场景*/
    const { error: scenesError, data: scenesData } = await fetchWebhookScenes({
      channel: 'WEB_HOOK',
    });
    setLoading(false);
    if (scenesError) {
      message.error(scenesError.message);
      return;
    }

    const scenesList = scenesData.data ?? [];
    /*场景初始值*/
    const { error, data } = await fetchWebhookBindedScenes({ webHookId: webhookId });
    if (error) {
      message.error(error.message);
      return;
    }
    const initScenesList = (data.data ?? [])?.filter(item => item.relateWebHook);

    /*通知类型*/
    const { error: notifyError, data: notifyData } = await fetchMetaDataByType({
      type: MetaType.NOTIFY_EVENT_TYPE,
    });
    if (notifyError) {
      message.error(notifyError.message);
      return;
    }
    const notifyList = notifyData.data ?? [];

    const scenesCodes = Array.from(new Set(scenesList.map(item => item.eventType)));
    const scenesMapArr = scenesCodes.map(code => {
      const name = notifyList.find(notifyItem => notifyItem.code === code)?.name ?? '--';
      const currectCodeSecenes = scenesList.filter(currect => currect.eventType === code);
      return {
        name,
        code,
        options: currectCodeSecenes.map(item => ({
          label: item.name,
          value: item.notifyCode,
        })),
      };
    });
    setOptions(scenesMapArr);

    setOtherValues(
      initScenesList.filter(item => item.eventType !== 'ALARM')?.map(item => item.notifyCode)
    );
    setAlarmValue(
      initScenesList.filter(item => item.eventType === 'ALARM')?.map(item => item.notifyCode)?.[0]
    );
  };

  const showModal = () => {
    initFn();
    setVisible(true);
  };
  const closeModal = () => {
    setOtherValues([]);
    setVisible(false);
  };
  const onOk = async () => {
    setLoading(true);

    const { error } = await bindWebhookWithScenes({
      webHookId: webhookId,
      notifyEventList: alarmValue ? [alarmValue, ...otherValues] : otherValues,
    });
    setLoading(false);

    if (error) {
      message.error(error.message);
      return;
    }
    setVisible(false);
    message.success('关联场景成功');

    onFinish();
  };

  const alarmOptions = options.filter(item => item.code === 'ALARM')?.[0];
  return (
    <>
      <Button type="link" compact onClick={showModal}>
        关联场景
      </Button>

      <Modal
        width={600}
        bodyStyle={{ maxHeight: 'calc(80vh - 55px)', overflowY: 'auto' }}
        title="关联场景"
        okButtonProps={{ loading }}
        open={visible}
        onOk={onOk}
        onCancel={closeModal}
      >
        <Space style={{ width: '100%' }} direction="vertical" size="large">
          {alarmOptions && (
            <Space style={{ width: '100%' }} direction="vertical" size="large">
              <Badge status="processing" text={alarmOptions.name} />
              <Radio.Group
                style={{ width: '100%' }}
                value={alarmValue}
                onChange={e => setAlarmValue(e.target.value)}
              >
                <Row>
                  {alarmOptions.options.map(item => {
                    return (
                      <Col key={`col_${item.value}`} span={6}>
                        <Radio
                          value={item.value}
                          onClick={e => {
                            const clickedValue = (e.target as HTMLInputElement)?.value;
                            if (clickedValue === alarmValue) {
                              setAlarmValue(undefined);
                            }
                          }}
                        >
                          {item.label}
                        </Radio>
                      </Col>
                    );
                  })}
                </Row>
              </Radio.Group>
            </Space>
          )}
          <Checkbox.Group
            style={{ width: '100%' }}
            value={otherValues}
            onChange={value => {
              setOtherValues(value as string[]);
            }}
          >
            <Space style={{ width: '100%' }} direction="vertical" size="large">
              {options
                .filter(item => item.code !== 'ALARM')
                .map(option => {
                  return (
                    <Space key={option.code} style={{ width: '100%' }} direction="vertical">
                      <Badge status="processing" text={option.name} />
                      <Row>
                        {option.options.map(item => {
                          return (
                            <Col key={`col_${item.value}`} span={6}>
                              <Checkbox value={item.value}>{item.label}</Checkbox>
                            </Col>
                          );
                        })}
                      </Row>
                    </Space>
                  );
                })}
            </Space>
          </Checkbox.Group>
        </Space>
      </Modal>
    </>
  );
}
