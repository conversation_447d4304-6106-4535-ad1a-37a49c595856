import { QuestionCircleOutlined } from '@ant-design/icons';
import React, { useCallback, useEffect, useState } from 'react';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Card } from '@manyun/base-ui.ui.card';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Collapse } from '@manyun/base-ui.ui.collapse';
import { Empty } from '@manyun/base-ui.ui.empty';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import type { ColumnType } from '@manyun/base-ui.ui.table';
import { Table } from '@manyun/base-ui.ui.table';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { UserSwitch } from '@manyun/notification-hub.model.notice-config';
import { fetchNoticeTemplatesWeb } from '@manyun/notification-hub.service.notify.fetch-notice-templates';
import { updateNoticeTemplateWeb } from '@manyun/notification-hub.service.notify.update-notice-template';
import type { ServiceQ as UpdateNotificationParams } from '@manyun/notification-hub.service.notify.update-notice-template';
import { Receiver } from '@manyun/notification-hub.ui.receiver';
import { useMetaData } from '@manyun/resource-hub.hook.use-meta-data';
import { MetaType } from '@manyun/resource-hub.model.metadata';

const alertTip =
  '提醒：您可以为每类消息设置接收人，平台不会将接收人信息对外披露或向第三方提供。资产、工单、库存等重要消息，建议您务必设置接收，防止消息遗漏造成损失。';
const receiverTip = '除默认接收人需要接收消息的用户';

export function NotificationChannelsConfig() {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [templates, setTemplates] = useState<any[]>([]);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [loadingMappings, setLoadingMappings] = useState<any>({});
  const [loading, setLoading] = useState(false);
  const [{ data }, { readMetaData }] = useMetaData(MetaType.NOTIFY_EVENT_TYPE);
  const [configUtil] = useConfigUtil();
  const channels = configUtil.getScopeCommonConfigs('notifications')?.channels;
  const showGlpApp = channels?.showGlpApp;
  const showFeiShuCard = channels?.showFeiShuCard;

  React.useEffect(() => {
    readMetaData();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const getNoticeTemplates = async () => {
    setLoading(true);
    const { data, error } = await fetchNoticeTemplatesWeb();
    setLoading(false);

    if (error) {
      message.error(error.message);
      return;
    }
    setTemplates(data.data);
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const tmp: any = {};
    data.data.forEach(({ eventType }) => {
      tmp[eventType] = false;
    });
    setLoadingMappings(tmp);
  };
  const generateNoticeTypeMap = useCallback(() => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const tmp: any = {};
    data?.data?.forEach(({ label, value }) => {
      tmp[value] = label;
    });
    return tmp;
  }, [data]);

  useEffect(() => {
    getNoticeTemplates();
  }, []);

  const getColumns = useCallback(
    (eventType: string) => {
      const update = async ({
        params,
        eventType,
      }: {
        params: UpdateNotificationParams;
        eventType: string;
      }) => {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        setLoadingMappings((prevValue: any) => ({ ...prevValue, [eventType]: true }));
        const { error } = await updateNoticeTemplateWeb(params);
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        setLoadingMappings((prevValue: any) => ({ ...prevValue, [eventType]: false }));
        setLoadingMappings(true);
        if (error) {
          message.error(error.message);
          return false;
        }
        // message.success('更新成功！');
        getNoticeTemplates();
        return true;
      };
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const defaultColumns: ColumnType<any>[] = [
        {
          title: '场景',
          dataIndex: 'title',
          width: 250,
        },
        {
          title: '站内信',
          dataIndex: 'onSiteMessage',
          render: (_, { channels, notifyCode, receiver, canEditChannels, roleList }) => {
            let checked = false;
            if (channels && Array.isArray(channels) && channels.includes('INTERNAL_MESSAGE')) {
              checked = true;
            }

            return (
              <Checkbox
                checked={checked}
                disabled={
                  Array.isArray(canEditChannels) && !canEditChannels.includes('INTERNAL_MESSAGE')
                }
                onChange={e => {
                  update({
                    params: {
                      notifyCode,
                      channels: 'INTERNAL_MESSAGE',
                      userIdList: receiver,
                      usable: e.target.checked,
                      roleList: roleList,
                    },
                    eventType,
                  });
                }}
              />
            );
          },
        },
        {
          title: '邮箱',
          dataIndex: 'email',
          render: (_, { channels, notifyCode, receiver, canEditChannels, roleList }) => {
            let checked = false;
            if (channels && Array.isArray(channels) && channels.includes('EMAIL')) {
              checked = true;
            }

            return (
              <Checkbox
                checked={checked}
                disabled={Array.isArray(canEditChannels) && !canEditChannels.includes('EMAIL')}
                onChange={e => {
                  update({
                    params: {
                      notifyCode,
                      channels: 'EMAIL',
                      userIdList: receiver,
                      usable: e.target.checked,
                      roleList: roleList,
                    },
                    eventType,
                  });
                }}
              />
            );
          },
        },
        {
          title: '飞书',
          dataIndex: 'feiShuCard',
          render: (_, { channels, notifyCode, receiver, canEditChannels, roleList }) => {
            let checked = false;
            if (Array.isArray(channels) && channels.includes('FEI_SHU_CARD')) {
              checked = true;
            }

            return (
              <Checkbox
                checked={checked}
                disabled={
                  Array.isArray(canEditChannels) && !canEditChannels.includes('FEI_SHU_CARD')
                }
                onChange={e => {
                  update({
                    params: {
                      notifyCode,
                      channels: 'FEI_SHU_CARD',
                      userIdList: receiver,
                      usable: e.target.checked,
                      roleList: roleList,
                    },
                    eventType,
                  });
                }}
              />
            );
          },
        },
        {
          title: '短信',
          dataIndex: 'sms',
          render: (_, { channels, notifyCode, receiver, canEditChannels, roleList }) => {
            let checked = false;
            if (channels && Array.isArray(channels) && channels.includes('SMS')) {
              checked = true;
            }

            return (
              <Checkbox
                checked={checked}
                disabled={Array.isArray(canEditChannels) && !canEditChannels.includes('SMS')}
                onChange={e => {
                  update({
                    params: {
                      notifyCode,
                      channels: 'SMS',
                      userIdList: receiver,
                      usable: e.target.checked,
                      roleList: roleList,
                    },
                    eventType,
                  });
                }}
              />
            );
          },
        },
        {
          title: 'WebHook',
          dataIndex: 'port',
          render: (_, { channels, notifyCode, receiver, canEditChannels, roleList }) => {
            const checked = channels && Array.isArray(channels) && channels.includes('WEB_HOOK');

            return (
              <Checkbox
                checked={checked}
                disabled={Array.isArray(canEditChannels) && !canEditChannels.includes('WEB_HOOK')}
                onChange={e => {
                  update({
                    params: {
                      notifyCode,
                      channels: 'WEB_HOOK',
                      userIdList: receiver,
                      usable: e.target.checked,
                      roleList: roleList,
                    },
                    eventType,
                  });
                }}
              />
            );
          },
        },

        {
          title: '电话',
          dataIndex: 'phone',
          render: (_, { channels, notifyCode, receiver, canEditChannels, roleList }) => {
            const checked = Boolean(Array.isArray(channels) && channels.includes('PHONE'));

            return (
              <Checkbox
                checked={checked}
                disabled={Array.isArray(canEditChannels) && !canEditChannels.includes('PHONE')}
                onChange={e => {
                  update({
                    params: {
                      notifyCode,
                      channels: 'PHONE',
                      userIdList: receiver,
                      usable: e.target.checked,
                      roleList: roleList,
                    },
                    eventType,
                  });
                }}
              />
            );
          },
        },
        {
          title: 'MyGLP手机推送',
          dataIndex: 'glpApp',
          render: (_, { channels, notifyCode, receiver, canEditChannels, roleList }) => {
            const checked = Array.isArray(channels) && channels.includes('GLP_APP');

            return (
              <Checkbox
                checked={checked}
                disabled={Array.isArray(canEditChannels) && !canEditChannels.includes('GLP_APP')}
                onChange={e => {
                  update({
                    params: {
                      notifyCode,
                      channels: 'GLP_APP',
                      userIdList: receiver,
                      usable: e.target.checked,
                      roleList: roleList,
                    },
                    eventType,
                  });
                }}
              />
            );
          },
        },
        {
          title: () => (
            <Space>
              接收对象
              <Tooltip title={receiverTip}>
                <QuestionCircleOutlined />
              </Tooltip>
            </Space>
          ),
          dataIndex: 'receiver',
          render: (receiver, { notifyCode, title, roleList, extraUserSwitch }) => {
            return extraUserSwitch !== UserSwitch.None ? (
              <Receiver
                userIds={receiver}
                notifyCode={notifyCode}
                extraUserSwitch={extraUserSwitch}
                roleList={roleList}
                update={params => update({ params, eventType })}
                noticeType={`${generateNoticeTypeMap()[eventType] || eventType}-${title}`}
              />
            ) : (
              <span>--</span>
            );
          },
        },
      ];

      return defaultColumns.filter(({ dataIndex }) => {
        if (dataIndex === 'feiShuCard') {
          return showFeiShuCard;
        } else if (dataIndex === 'glpApp') {
          return showGlpApp;
        }
        return true;
      });
    },
    [generateNoticeTypeMap, showFeiShuCard, showGlpApp]
  );

  return (
    <Card>
      <Space style={{ width: '100%', display: 'flex' }} direction="vertical" size="large">
        <Alert message={alertTip} type="info" showIcon closable />
        <Spin spinning={loading}>
          {!templates.length && (
            <div style={{ textAlign: 'center', width: '100%' }}>
              <Empty description="暂无数据" />
            </div>
          )}
          {templates.length > 0 && (
            <Collapse defaultActiveKey={templates.map(({ eventType }) => eventType)}>
              {templates.map(({ eventType, notifyEventList }) => {
                const num =
                  notifyEventList && Array.isArray(notifyEventList) ? notifyEventList.length : 0;

                return (
                  <Collapse.Panel
                    key={eventType}
                    header={`${generateNoticeTypeMap()[eventType] || eventType}（${num}）`}
                  >
                    <Table
                      rowKey="notifyCode"
                      dataSource={notifyEventList}
                      loading={loadingMappings[eventType]}
                      columns={getColumns(eventType)}
                      pagination={false}
                    />
                  </Collapse.Panel>
                );
              })}
            </Collapse>
          )}
        </Spin>
      </Space>
    </Card>
  );
}
