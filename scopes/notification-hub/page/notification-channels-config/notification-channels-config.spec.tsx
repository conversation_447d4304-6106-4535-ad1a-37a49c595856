import React from 'react';

import { fireEvent, render, screen, waitFor } from '@testing-library/react';

import { BasicNoticeTemplates } from './notification-channels-config.composition';

it('test notice templates', async () => {
  render(<BasicNoticeTemplates />);
  await waitFor(async () => {
    expect(await screen.queryByText('渠道配置')).toBeInTheDocument;
    expect(await screen.queryByText('工单时效通知')).toBeInTheDocument;
    expect(await screen.queryByText('工单（2）')).toBeInTheDocument;
    const select = await screen.findAllByLabelText('WebHook: select');
    fireEvent.focus(select[0]);
    expect(await screen.queryByText('yy')).toBeInTheDocument;
  });
});
