import React, { useEffect, useState } from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { useRemoteMock, webRequest } from '@manyun/service.request';

import { NotificationChannelsConfig } from './notification-channels-config';

export const BasicNoticeTemplates = () => {
  const [initialized, update] = useState(false);

  // useEffect(() => {
  //   webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
  //   update(true);
  // }, []);

  React.useEffect(() => {
    const mockOff = useRemoteMock('web');
    update(true);

    return () => {
      mockOff();
    };
  }, []);

  if (!initialized) {
    return <span>Loading</span>;
  }

  return (
    <ConfigProvider>
      <NotificationChannelsConfig />
    </ConfigProvider>
  );
};
