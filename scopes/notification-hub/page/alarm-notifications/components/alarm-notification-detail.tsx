import React, { useCallback, useEffect, useMemo, useState } from 'react';

import { User } from '@manyun/auth-hub.ui.user';
import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import type { AlarmNotificationDetail } from '@manyun/notification-hub.service.fetch-alarm-notification';
import { fetchAlarmNotification } from '@manyun/notification-hub.service.fetch-alarm-notification';
import type { AlarmNotificationRecord } from '@manyun/notification-hub.service.fetch-alarm-notifications';

import { AlarmNotificationState } from './alarm-notification-state';

export type AlarmNotificationDetailModalProps = {
  record: AlarmNotificationRecord;
};
export function AlarmNotificationDetailModal({ record }: AlarmNotificationDetailModalProps) {
  const [visible, setVisible] = useState(false);
  const [state, setState] = useState<{
    loading: boolean;
    data: AlarmNotificationDetail[];
    userName?: string;
  }>({
    loading: false,
    data: [],
  });

  const _fetchData = useCallback(async () => {
    setState(pre => ({ ...pre, loading: true }));
    const { error, data } = await fetchAlarmNotification({
      bizId: `${record.idcTag}_${record.alarmId}_${record.itemId}`,
      notifyCode: `ALARM`,
    });
    setState(pre => ({ ...pre, loading: false }));
    if (error) {
      message.error(error.message);
      return;
    }
    setState(pre => ({ ...pre, data: data.data }));
  }, [record.alarmId, record.idcTag, record.itemId]);

  const _filterData = useMemo(() => {
    if (!state.userName) {
      return state.data;
    }
    return state.data.filter(
      d => d.userName.toLocaleLowerCase().indexOf((state.userName ?? '').toLocaleLowerCase()) >= 0
    );
  }, [state.data, state.userName]);

  useEffect(() => {
    if (visible) {
      _fetchData();
    }
  }, [_fetchData, visible]);

  return (
    <>
      <Button
        type="link"
        compact
        onClick={() => {
          setVisible(true);
        }}
      >
        通报详情
      </Button>
      <Modal
        width={654}
        bodyStyle={{
          maxHeight: '452px',
          overflowY: 'auto',
        }}
        title="通报详情"
        visible={visible}
        footer={null}
        destroyOnClose
        onCancel={() => {
          setVisible(false);
        }}
      >
        <Space direction="vertical" style={{ width: ' 100%' }} size={16}>
          <Input.Search
            placeholder="搜索用户姓名"
            style={{ width: '216px' }}
            onSearch={value => {
              setState(pre => ({ ...pre, userName: value }));
            }}
          />
          <Table
            dataSource={_filterData}
            loading={state.loading}
            size="small"
            columns={[
              {
                title: '姓名',
                dataIndex: 'userName',
                render: (value, record) => <User.Link id={record.userId} name={value} />,
              },
              {
                title: '站内信',
                dataIndex: 'internalMsg',
                render: val => {
                  return <AlarmNotificationState value={val} />;
                },
              },
              {
                title: '邮件',
                dataIndex: 'email',
                render: val => <AlarmNotificationState value={val} />,
              },
              {
                title: '短信',
                dataIndex: 'sms',
                render: val => <AlarmNotificationState value={val} />,
              },
              {
                title: '电话',
                dataIndex: 'phone',
                render: val => <AlarmNotificationState value={val} isPhone />,
              },
            ]}
            pagination={false}
          />
        </Space>
      </Modal>
    </>
  );
}
