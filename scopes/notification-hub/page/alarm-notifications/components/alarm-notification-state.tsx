import React, { useMemo } from 'react';

import { Badge } from '@manyun/base-ui.ui.badge';
import { Space } from '@manyun/base-ui.ui.space';

import type { NotifyState } from '@manyun/notification-hub.service.fetch-alarm-notification';

export const NOTIFY_STATE_MAPPER: Record<NotifyState, string> = {
  FAILURE: '失败',
  WAITING_SEND: '待发送',
  WAITING_CONFIRM: '待确认',
  MISSED: '未接听',
  SUCCESS: '成功',
  NOT_CONNECTED: '未接通',
  NOT_CONFIRM: '未确认',
};

export const PHONE_NOTIFY_STATE_MAPPER: Record<NotifyState, string> = {
  FAILURE: '失败',
  WAITING_SEND: '待发送',
  WAITING_CONFIRM: '待确认',
  MISSED: '未接听',
  SUCCESS: '已确认',
  NOT_CONNECTED: '未接通',
  NOT_CONFIRM: '未确认',
};

export const NOTIFY_STATE_COLOR_MAPPER: Record<NotifyState, string> = {
  FAILURE: 'red',
  WAITING_SEND: 'gold',
  WAITING_CONFIRM: 'gold',
  SUCCESS: 'green',
  MISSED: 'red',
  NOT_CONNECTED: 'red',
  NOT_CONFIRM: 'gold',
};

export type AlarmNotificationStateProps = { value?: NotifyState; isPhone?: boolean };
export const AlarmNotificationState = ({ value, isPhone }: AlarmNotificationStateProps) => {
  const _mapper = useMemo(() => {
    return isPhone ? PHONE_NOTIFY_STATE_MAPPER : NOTIFY_STATE_MAPPER;
  }, [isPhone]);

  if (!value) {
    return <></>;
  }

  return (
    <Space size={0}>
      <Badge color={NOTIFY_STATE_COLOR_MAPPER[value] ?? 'red'} />
      {_mapper[value] ? _mapper[value] : value}
    </Space>
  );
};
