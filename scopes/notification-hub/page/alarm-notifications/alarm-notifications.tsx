import React, { useCallback, useState } from 'react';
import { useLocation } from 'react-router-dom';

import dayjs from 'dayjs';
import useDeepCompareEffect from 'use-deep-compare-effect';

import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';

import { getLocationSearchMap } from '@manyun/dc-brain.util.get-location-search-map';
import { AlarmLevelText } from '@manyun/monitoring.ui.alarm-level-text';
import { fetchAlarmNotifications } from '@manyun/notification-hub.service.fetch-alarm-notifications';
import type {
  AlarmNotificationRecord,
  SvcQuery,
} from '@manyun/notification-hub.service.fetch-alarm-notifications';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';

import { AlarmNotificationDetailModal } from './components/alarm-notification-detail';

export type State = {
  loading: boolean;
  data: AlarmNotificationRecord[];
  total: number;
  fields: SvcQuery;
};

export function AlarmNotifications() {
  const { search } = useLocation();
  const { alarmId, idc } = getLocationSearchMap(search, ['alarmId', 'idc']);

  const [state, setState] = useState<State>({
    loading: false,
    data: [],
    total: 0,
    fields: {
      page: 1,
      pageSize: 10,
      alarmId: alarmId ?? undefined,
      idc: idc ?? undefined,
    },
  });

  const _fetchData = useCallback(async () => {
    setState(pre => ({ ...pre, loading: true }));
    const { error, data } = await fetchAlarmNotifications({
      ...state.fields,
    });
    if (error) {
      message.error(error.message);
      setState(pre => ({ ...pre, loading: false }));
      return;
    }
    setState(pre => ({ ...pre, loading: false, data: data.data, total: data.total }));
  }, [state.fields]);

  useDeepCompareEffect(() => {
    _fetchData();
  }, [_fetchData]);

  return (
    <Card title="告警通报记录">
      <Space style={{ width: '100%' }} direction="vertical" size={16}>
        <Form layout="inline">
          <Form.Item>
            <Input.Search
              placeholder="告警内容"
              allowClear
              onSearch={value => {
                setState(pre => ({
                  ...pre,
                  fields: { ...pre.fields, content: value, page: 1, pageSize: 10 },
                }));
              }}
            />
          </Form.Item>
          <Form.Item>
            <LocationTreeSelect
              style={{ width: '216px' }}
              authorizedOnly
              placeholder="位置"
              includeVirtualBlocks
              allowClear
              onChange={value => {
                const _location = (value ?? '').split('.');
                setState(pre => ({
                  ...pre,
                  fields: {
                    ...pre.fields,
                    idc: _location[0],
                    block: _location[1],
                    page: 1,
                    pageSize: 10,
                  },
                }));
              }}
            />
          </Form.Item>
          <Form.Item>
            <DatePicker.RangePicker
              style={{ width: '263px' }}
              format="YYYY-MM-DD"
              placeholder={['开始日期', '结束日期']}
              allowClear
              onChange={value => {
                setState(pre => ({
                  ...pre,
                  fields: {
                    ...pre.fields,
                    timeRange:
                      value && value[0] && value[1]
                        ? [value[0].startOf('day').valueOf(), value[1].endOf('day').valueOf()]
                        : undefined,
                    page: 1,
                    pageSize: 10,
                  },
                }));
              }}
            />
          </Form.Item>
        </Form>
        <Table
          loading={state.loading}
          dataSource={state.data}
          columns={[
            {
              title: '通知时间',
              dataIndex: 'gmtCreate',
              render: value => (value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : '--'),
            },
            {
              title: '机房',
              dataIndex: 'idcTag',
            },
            {
              title: '楼栋',
              dataIndex: 'blockTag',
            },
            {
              title: '告警等级',
              dataIndex: 'alarmLevel',
              render: value => <AlarmLevelText code={value} />,
            },
            {
              title: '告警内容',
              dataIndex: 'content',
            },
            {
              title: '通报项目',
              dataIndex: 'itemName',
            },
            {
              title: '通报对象',
              dataIndex: 'informNum',
              render: value => (value ? `${value}人` : '--'),
            },
            {
              title: '操作',
              dataIndex: '_action',
              fixed: 'right',
              render: (_, record) => {
                return <AlarmNotificationDetailModal record={record} />;
              },
            },
          ]}
          scroll={{ x: 'max-content' }}
          pagination={{
            total: state.total,
            current: state.fields.page,
            pageSize: state.fields.pageSize,
          }}
          onChange={pagination => {
            setState(pre => ({
              ...pre,
              fields: {
                ...pre.fields,
                page: pagination.current!,
                pageSize: pagination.pageSize!,
              },
            }));
          }}
        />
      </Space>
    </Card>
  );
}
