import React from 'react';
import { MemoryRouter as Router } from 'react-router-dom';

import { generateOnsiteMessageUrl } from '@manyun/notification-hub.route.notification-routes';

export type OnSiteMessagesPageRouterWrapperProps = {
  state: any;
  type?: any;
};

export const OnSiteMessagesPageRouterWrapper: React.FunctionComponent<
  OnSiteMessagesPageRouterWrapperProps
> = ({ state, type = 'all', children }) => {
  return <Router initialEntries={[generateOnsiteMessageUrl({ state, type })]}>{children}</Router>;
};
