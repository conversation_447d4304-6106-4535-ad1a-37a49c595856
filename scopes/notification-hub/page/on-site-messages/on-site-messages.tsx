import React from 'react';
import { Route } from 'react-router-dom';

import { Card } from '@manyun/base-ui.ui.card';

import { OnSiteMessageState } from '@manyun/notification-hub.model.on-site-messages';
import { ON_SITE_MESSAGE_ROUTE_PATH } from '@manyun/notification-hub.route.notification-routes';
import type { SvcQuery as MarkOnSiteMessagesAsReadSvcQuery } from '@manyun/notification-hub.service.mark-on-site-messages-as-read';
import { OnSiteMessages } from '@manyun/notification-hub.ui.on-site-messages';
import { OnSiteMessagesFilters } from '@manyun/notification-hub.ui.on-site-messages-filters';
import { OnSiteMessagesMarkAsRead } from '@manyun/notification-hub.ui.on-site-messages-mark-as-read';
import { OnSiteMessagesStateSwitcher } from '@manyun/notification-hub.ui.on-site-messages-state-switcher';
import { OnSiteMessagesTypeSwitcher } from '@manyun/notification-hub.ui.on-site-messages-type-switcher';

import styles from './on-site-messages.module.less';

export type OnSiteMessagesPageProps = {
  onMarkAsReadSuccess?(svcQuery: MarkOnSiteMessagesAsReadSvcQuery): void;
};

export function OnSiteMessagesPage({ onMarkAsReadSuccess }: OnSiteMessagesPageProps) {
  return (
    <Route path={ON_SITE_MESSAGE_ROUTE_PATH}>
      <OnSiteMessagesStateSwitcher>
        {state => (
          <Card bordered={false} bodyStyle={{ width: 'calc(100vw - 48px - 16px - 200px)' }}>
            <OnSiteMessagesTypeSwitcher>
              {type => {
                return (
                  <div className={styles.contentContainer}>
                    {state === OnSiteMessageState.Read && (
                      <OnSiteMessagesFilters state={state} type={type} />
                    )}
                    {state === OnSiteMessageState.Unread && (
                      <div className={styles.markAsReadContainer}>
                        <OnSiteMessagesMarkAsRead
                          state={state}
                          type={type}
                          variant="selected"
                          onSuccess={onMarkAsReadSuccess}
                        />
                        <OnSiteMessagesMarkAsRead
                          state={state}
                          type={type}
                          variant="all"
                          onSuccess={onMarkAsReadSuccess}
                        />
                      </div>
                    )}
                    <OnSiteMessages state={state} type={type} />
                  </div>
                );
              }}
            </OnSiteMessagesTypeSwitcher>
          </Card>
        )}
      </OnSiteMessagesStateSwitcher>
    </Route>
  );
}
