import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
// @ts-ignore
import { OnSiteMessagesReduxWrapper } from '@manyun/notification-hub.state.on-site-messages/dist/on-site-messages.composition-wrapper';
import { useRemoteMock, webRequest } from '@manyun/service.request';

import { OnSiteMessagesPage } from './on-site-messages';
import { OnSiteMessagesPageRouterWrapper } from './on-site-messages.router-wrapper';

export const BasicOnSiteMessagesPage = () => {
  const [initialized, update] = React.useState(false);

  // React.useEffect(() => {
  //   webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
  //   update(true);
  // }, []);

  React.useEffect(() => {
    const mockOff = useRemoteMock('web');
    update(true);

    return () => {
      mockOff();
    };
  }, []);

  if (!initialized) {
    return <span>Loading</span>;
  }

  return (
    <ConfigProvider>
      <FakeStore>
        <OnSiteMessagesPageRouterWrapper state="unread">
          <OnSiteMessagesPage />
        </OnSiteMessagesPageRouterWrapper>
      </FakeStore>
    </ConfigProvider>
  );
};
