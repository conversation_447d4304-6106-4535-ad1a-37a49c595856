import React from 'react';

import { render } from '@testing-library/react';

import { BasicOnSiteMessagesPage } from './on-site-messages.composition';

it('should render with the correct text', () => {
  const { getAllByRole } = render(<BasicOnSiteMessagesPage />);
  const selectedTabPanes = getAllByRole('tab', { selected: true });
  expect(selectedTabPanes[0]).toHaveTextContent('未读');
  expect(selectedTabPanes[1]).toHaveTextContent('全部消息');
});
