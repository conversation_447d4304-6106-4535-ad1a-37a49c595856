export enum BackendOnSiteMessageType {
  all = '',
  notice = 'INSIDE_SUBSCRIBE',
  event = 'EVENT_SUBSCRIBE',
  asset = 'ASSERT_SUBSCRIBE',
  ticket = 'TASK_SUBSCRIBE',
  inventory = 'INVENTORY_SUBSCRIBE',
  approval = 'APPROVAL_SUBSCRIBE',
  todo = 'WAITING_SUBSCRIBE',
  scheduleSubscribe = 'SCHEDULE_SUBSCRIBE',
  examSubscribe = 'EXAM_SUBSCRIBE',
  attendance = 'ATTENDANCE_SUBSCRIBE',
  urge = 'URGE',
  testPerformance = 'PF_PERFORMANCE',
  performance = 'YEAR_PERFORMANCE',
  drill = 'DRILL',
  security = 'SECURITY',
  riskRegister = 'RISK_REGISTER',
  report = 'REPORT',
}

export enum BackendOnSiteMessageState {
  unread = '0',
  read = '1',
}

export enum OnSiteMessageType {
  All = 'all',
  Notice = 'notice',
  Event = 'event',
  Asset = 'asset',
  Ticket = 'ticket',
  Inventory = 'inventory',
  Approval = 'approval',
  Todo = 'todo',
  ScheduleSubscribe = 'scheduleSubscribe',
  examSubscribe = 'examSubscribe',
  Attendance = 'attendance',
  Urge = 'urge',
  TestPerformance = 'testPerformance',
  Performance = 'performance',
  Drill = 'drill',
  SECURITY = 'security',
  RISK_REGISTER = 'riskRegister',
  REPORT = 'report',
}

export enum OnSiteMessageState {
  Unread = 'unread',
  Read = 'read',
}

export enum TargetType {
  //人员
  PERSON = 'PERSON',

  /** 需编辑的用户 */
  PERSON_EDIT = 'PERSON_EDIT',

  //巡检工单
  INSPECTION = 'INSPECTION',

  // 盘点工单
  INVENTORY = 'INVENTORY',

  //维保工单
  MAINTENANCE = 'MAINTENANCE',

  //上下电工单
  POWER = 'POWER',

  //维修工单
  REPAIR = 'REPAIR',

  //出入库工单
  WAREHOUSE = 'WAREHOUSE',

  //资产出入门工单
  ACCESS = 'ACCESS',

  //上下线工单
  ON_OFF = 'ON_OFF',

  //事件详情
  EVENT_DETAIL = 'EVENT_DETAIL',

  //事件通报
  EVENT_REPORT = 'EVENT_REPORT',

  //公告详情
  NOTICE_DETAIL = 'NOTICE_DETAIL',

  //维保池
  MAINTENANCE_POOL = 'MAINTENANCE_POOL',

  //借用归还详情
  BORROW_RETURN_DETAIL = 'BORROW_RETURN_DETAIL',

  //审批详情
  APPROVAL_DETAIL = 'APPROVAL_DETAIL',

  // 上下架工单
  DEVICE_GENERAL = 'DEVICE_GENERAL',

  // 到货验收工单
  ACCEPT = 'ACCEPT',

  // 门禁卡
  ACCESS_CARD_AUTH = 'ACCESS_CARD_AUTH',

  /**
   * 告警详情
   */
  ALARM_DETAIL = 'ALARM_DETAIL',

  /**
   * 巡检计划
   */
  FACILITY_XJ = 'FACILITY_XJ',
  /**
   * 维护计划
   */
  FACILITY_WB = 'FACILITY_WB',
  /**
   * 盘点计划
   */
  FACILITY_PD = 'FACILITY_PD',

  /**
   * 告警传输
   */
  ALARM_TRANSFER = 'ALARM_TRANSFER',

  //设备库存列表
  DEVICE_INVENTORY_LIST = 'DEVICE_INVENTORY_LIST',
  //耗材库存列表
  SPARE_INVENTORY_LIST = 'SPARE_INVENTORY_LIST',
  /**
   * 考试管理列表
   */
  EXAM_MANAGE_LIST = 'EXAM_MANAGE_LIST',

  /**
   * 当前考试列表
   */
  EXAM_PARTICIPATE_LIST = 'EXAM_PARTICIPATE_LIST',

  /**
   * 历史考试列表
   */
  EXAM_HISTORY_LIST = 'EXAM_HISTORY_LIST',

  /**
   * 课程
   */
  COURSE_OWNER = 'COURSE_OWNER',
  /**
   * 考勤
   */
  ATTENDANCE = 'ATTENDANCE',

  //个人中心
  CERT_PERSONAL_CENTER = 'CERT_PERSONAL_CENTER',

  /**
   * 年度绩效
   */
  PERFORMANCE = 'PERFORMANCE',

  /**
   * 风险登记册
   */
  NEW_RISK_REGISTER = 'NEW_RISK_REGISTER',
  /**
   * 权限申请
   */
  AUTH_APPLY = 'AUTH_APPLY',
  /**
   * 用户授权
   */
  USER_CENTER = 'USER_CENTER',
  /** 风险检查 */
  RISK_CHECK = 'RISK_CHECK',
  /**
   * 演练工单
   */
  EME = 'EME',
  /**
   * 变更单
   */
  CHANGE = 'CHANGE',
  /**
   * 演练工单详情
   */
  DRILL_SERVICE_DETAIL = 'DRILL_SERVICE_DETAIL',
  /**
   * IT服务工单
   */
  IT_SERVICE = 'IT_SERVICE',
  /**
   * 入室工单
   */
  VISITOR = 'VISITOR',
  /**
   * 课程详情
   */
  COURSE_DETAIL = 'COURSE_DETAIL',
  /**
   * 门禁卡管理
   */
  ENTRANCE_CARD_MANAGEMENT = 'ENTRANCE_CARD_MANAGEMENT',
  /**
   * 事件内测版
   */
  N_EVENT_REPORT = 'N_EVENT_REPORT',
  /**
   * 罗盘
   */
  COMPASS = 'COMPASS',
}

export type TargetContent = {
  targetName: string;
  targetId: string;
  targetType: TargetType;
};

export type TargetContentList = TargetContent[] | null;

export type BackendOnSiteMessage = {
  id: number;
  parentType: string;
  secondType: string;
  insideScenes: string;
  title: string | null;
  userId: number;
  isRead: string;
  gmtCreate: string;
  gmtModify: string;
  content: string;
  targetContentList: TargetContentList;
};
