import type { BackendNotifyEvent } from './index';

export const mockReceiveMessageConfigList: BackendNotifyEvent[] = [
  {
    id: 25,
    notifyCode: 'EVENT_NOTIFY',
    title: '事件通知',
    channelList: ['INTERNAL_MESSAGE'],
    remark: '事件通知',
    eventType: 'TASK',
    channelConfigInfoList: [
      {
        channelType: 'WEB_HOOK',
        configGroup: 1,
        viewName: '钉钉告警机器人',
      },
    ],
    userIdList: [1],
  },
  {
    id: 24,
    notifyCode: 'NOTICE',
    title: '公告通知',
    channelList: ['EMAIL,WEB_HOOK,INTERNAL_MESSAGE'],
    remark: '公告通知',
    eventType: 'TASK',
    channelConfigInfoList: [
      {
        channelType: 'WEB_HOOK',
        configGroup: 1,
        viewName: '钉钉告警机器人',
      },
    ],
    userIdList: [1],
  },
];

export function getRandomMockUser() {
  const idx = Math.floor(Math.random() * mockReceiveMessageConfigList.length);

  return mockReceiveMessageConfigList[idx];
}
