import type { BackendEventUpgradeChannel } from '@manyun/ticket.model.event';

export enum UserSwitch {
  None,
  OnlyUser,
  OnlyRole,
  Both,
}
export type ChannelConfigInfoList = {
  configGroup: number;
  channelType: string;
  viewName: string;
}[];

export type BackendNotifyEvent = {
  canSelectChannelList: string[];
  canEditChannelList: string[];
  channelConfigInfoList: null | ChannelConfigInfoList;
  channelList: BackendEventUpgradeChannel[];
  eventType: string;
  id: number;
  notifyCode: string;
  remark: string;
  title: string;
  userIdList: null | number[];
  roleList: Role[];
  extraUserSwitch: UserSwitch;
};

export type Role = {
  roleName: string;
  roleCode: string;
  remark: string;
};

export type BackendNoticeConfig = {
  eventType: string;
  notifyEventList: BackendNotifyEvent[];
  roleList: Role[];
};

export enum ChannelConfigType {
  EMAIL = 'EMAIL',
  WEB_HOOK = 'WEB_HOOK',
}

export class NoticeConfig {
  constructor(
    public eventType: string,

    /**
     * 通知渠道 ID
     */
    public id: number,

    /**
     * 事件 code
     */
    public notifyCode: string,

    /**
     * 事件名称
     */
    public title: string,

    /**
     * 通知配置的渠道
     */
    public channels: string[],

    /**
     * 通知配置的可选渠道
     */
    public canSelectChannels: string[],
    /**
     * 通知配置的可编辑渠道
     */
    public canEditChannels: string[],
    /**
     * 配置的渠道的详情
     */
    public channelConfigInfoList: ChannelConfigInfoList | null,

    /**
     * 配置的渠道的接收人
     */
    public receiver: number[] | null,
    public roleList: Role[] | null,
    public remark: null | string,
    /** 控制可配置的渠道接收人类型  */
    public extraUserSwitch: UserSwitch
  ) {}

  static fromApiObject(object: BackendNotifyEvent) {
    return new NoticeConfig(
      object.eventType,
      object.id,
      object.notifyCode,
      object.title,
      object.channelList,
      object.canSelectChannelList,
      object.canEditChannelList,
      object.channelConfigInfoList,
      object.userIdList,
      object.roleList,
      object.remark,
      object.extraUserSwitch
    );
  }
}

export enum ChannelTypeKey {
  Email = 'EMAIL',
  Webhook = 'WEB_HOOK',
}

export enum WebhookChannelKey {
  Name = 'NAME',
  Address = 'ADDRESS',
  Secret = 'SECRET',
}

export enum EmailChannelKey {
  UserName = 'USERNAME',
  Password = 'PASSWORD',
  Host = 'HOST',
  Protocol = 'PROTOCOL',
  Port = 'PORT',
}
