export type Importance = 'HIGH' | 'MID' | 'LOW';
export type RangeType = 'ALL' | 'PART';
export type Status = 'UN_VALID' | 'VALID' | 'INVALID';

export type BackendNotice = {
  id: number;
  title: string;
  content: string;
  importance: Importance;
  rangeType: RangeType;
  validBeginDate: number;
  validEndDate: number;
  modifierId?: number;
  modifierName?: string;
  published?: boolean;
  gmtModified?: number;
  resourceList?: {
    id: number;
    resourceName: string;
    resourceCode: string;
    resourceType: string;
  }[];
  immediatelyNotify?: boolean /*是否立即发布 */;
  popUp?: boolean /*是否弹框提醒 */;
  status?: Status;
};
export class Notice {
  constructor(
    public id: number,
    public title: string,
    public content: string,
    public importance: Importance,
    public rangeType: RangeType,
    public validDataRange: [number, number],
    public modifierId?: number,
    public modifierName?: string,
    public published?: boolean,
    public gmtModified?: number,
    public resourceList?: {
      id: number;
      name: string;
      code: string;
      type: string;
    }[],
    public immediatelyNotify?: boolean,
    public popUp?: boolean,
    public status?: Status
  ) {}

  static fromApiObject(object: BackendNotice) {
    return new Notice(
      object.id,
      object.title,
      object.content,
      object.importance,
      object.rangeType,
      [object.validBeginDate, object.validEndDate],
      object.modifierId,
      object.modifierName,
      object.published,
      object.gmtModified,
      object.resourceList && object.resourceList.length > 0
        ? object.resourceList.map(resource => ({
            id: resource.id,
            name: resource.resourceName,
            code: resource.resourceCode,
            type: resource.resourceType,
          }))
        : [],
      object.immediatelyNotify,
      object.popUp,
      object.status
    );
  }
}
