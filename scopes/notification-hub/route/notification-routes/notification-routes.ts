import {
  OnSiteMessageState,
  OnSiteMessageType,
} from '@manyun/notification-hub.model.on-site-messages';

//公告通知列表
export const NOTICE_MANAGE_LIST = '/page/notice/list';
// 公告
export const NOTIFICATION_ROUTE_PATH = '/page/notice/:id';
export const generateNoticeLocation = ({ id }: { id: string | number }) =>
  NOTIFICATION_ROUTE_PATH.replace(':id', String(id));

export const ON_SITE_MESSAGE_ROUTE_PATH = '/page/inside-notice/:state/:type';
export const generateOnsiteMessageUrl = ({
  state,
  type,
}: {
  state: OnSiteMessageState;
  type: OnSiteMessageType;
}) => ON_SITE_MESSAGE_ROUTE_PATH.replace(':state', state).replace(':type', type);

export const NOTIFICATION_CHANNEL_CONFIG_ROUTE_PATH =
  '/page/notification-hub/notification-channels-config';

/**告警通报记录 */
export const ALARM_NOTIFICATIONS_ROUTE_PATH = '/page/notification-hub/alarm-notifaction-records';
export const ALARM_NOTIFICATIONS_ROUTE_AUTH_CODE = 'page_alarm-notification-records';

/** webhook 配置 */
export const WEBHOOK_CONFIG_ROUTE_PATH = '/page/notification-hub/webhook-config';
export const WEBHOOK_CONFIG_ROUTE_AUTH_CODE = 'page_notification-webhook-config';

export const NOTIFACTION_HUB_ROUTE_PATH_AUTH_CODE_MAPPER = {
  [ALARM_NOTIFICATIONS_ROUTE_PATH]: ALARM_NOTIFICATIONS_ROUTE_AUTH_CODE,
  [WEBHOOK_CONFIG_ROUTE_PATH]: WEBHOOK_CONFIG_ROUTE_AUTH_CODE,
};
