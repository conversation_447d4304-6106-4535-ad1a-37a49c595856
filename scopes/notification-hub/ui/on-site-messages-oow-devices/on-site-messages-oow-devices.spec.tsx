import React from 'react';

import { render, waitFor } from '@testing-library/react';

import {
  BasicOnSiteMessagesOowDevices,
  CustomizedColumnsOnSiteMessagesOowDevices,
} from './on-site-messages-oow-devices.composition';

test('should render with the default `columns`', async () => {
  const { getByText } = render(<BasicOnSiteMessagesOowDevices />);

  await waitFor(() => getByText('资产ID'));

  expect(getByText('资产ID')).toBeTruthy();
});

test('should render with the customized `columns`', async () => {
  const { queryByText, getByText } = render(<CustomizedColumnsOnSiteMessagesOowDevices />);

  await waitFor(() => getByText('SN'));

  expect(queryByText('资产ID')).toBe(null);
  expect(getByText('SN')).toBeTruthy();
});
