import React, { useCallback, useEffect, useState } from 'react';

import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnProps } from '@manyun/base-ui.ui.table';

import { fetchOnSiteMessagesOowDevices as fetchOnSiteMessagesOutOfWarrantyDevices } from '@manyun/notification-hub.service.fetch-on-site-messages-oow-devices';

type SpaceGuid = {
  blockGuid: string;
  blockTag: string;
  idcGuid: string;
  idcTag: string;
  roomGuid: string;
  roomTag: string;
};

type Record = string | undefined;

export type OnSiteMessagesOowDevicesProps = {
  /**
   * 暂时使用 `cloud-base-ui` 工程中设备管理列表页面表格的表头。
   * 后续设备管理的表格抽成 `bit component` 后再来重构这里。
   *
   * @deprecated
   */
  columns?: ColumnProps<any>[];

  scroll?: {
    x: number | true;
  };

  /**
   * 站内消息-资产-维保池-目标
   *
   * 格式: ``${oowTimestamp1},${oowTimestamp2}=${responsibleUserId}=${batchId}``
   */
  targetId: string;
};

export function OnSiteMessagesOowDevices({
  columns = defaultColumns,
  scroll,
  targetId,
}: OnSiteMessagesOowDevicesProps) {
  const [{ loading, data, total }, setData] = useState<{
    loading: boolean;
    data: any[];
    total: number;
  }>({ loading: false, data: [], total: 0 });
  const [{ page, pageSize }, setPagination] = useState<{
    page: number;
    pageSize: number;
  }>({ page: 1, pageSize: 10 });

  const getDevices = useCallback(
    ({ page = 1, pageSize = 10 }: { page?: number; pageSize?: number } = {}) => {
      setData(prev => ({ ...prev, loading: true }));
      const [oowTimestampsStr, blockGuid, batchIdStr] = targetId.split('=');
      fetchOnSiteMessagesOutOfWarrantyDevices({
        outOfWrrantyTimestamps: Number(oowTimestampsStr),
        batchId: batchIdStr,
        blockGuid: blockGuid,
        page,
        pageSize,
      }).then(({ data }) => {
        setData({ ...data, loading: false });
      });
    },
    [targetId]
  );

  const pageHandler = useCallback(
    (page: number, pageSize: number) => {
      getDevices({ page, pageSize });
      setPagination({ page, pageSize });
    },
    [getDevices]
  );

  useEffect(() => {
    getDevices();
  }, [getDevices]);

  return (
    <Table
      size="small"
      rowKey="guid"
      loading={loading}
      columns={columns}
      dataSource={data}
      pagination={{
        total: total,
        current: page,
        pageSize: pageSize,
        onChange: pageHandler,
      }}
      scroll={{
        x: scroll?.x === undefined ? defaultScrollX : scroll.x,
      }}
    />
  );
}

const defaultColumns = [
  {
    width: 120,
    ellipsis: true,
    title: '资产ID',
    dataIndex: 'assetNo',
  },
  {
    width: 120,
    ellipsis: true,
    title: 'SN',
    dataIndex: 'serialNumber',
  },
  {
    width: 120,
    ellipsis: true,
    title: '名称',
    dataIndex: 'name',
  },
  {
    width: 120,
    ellipsis: true,
    title: '三级分类',
    dataIndex: 'deviceType',
  },
  {
    width: 120,
    ellipsis: true,
    title: '机房编号',
    dataIndex: 'idcTag',
    render: (_: Record, { spaceGuid }: { spaceGuid: SpaceGuid }) => {
      return spaceGuid.idcTag;
    },
  },
  {
    width: 120,
    ellipsis: true,
    title: '楼栋编号',
    dataIndex: 'blockTag',
    render: (_: Record, { spaceGuid }: { spaceGuid: SpaceGuid }) => spaceGuid.blockTag,
  },
  {
    width: 120,
    ellipsis: true,
    title: '包间编号',
    dataIndex: 'roomTag',
    render: (_: Record, { spaceGuid }: { spaceGuid: SpaceGuid }) => spaceGuid.roomTag,
  },
  {
    width: 120,
    ellipsis: true,
    title: '扩展位置',
    dataIndex: 'extendPosition',
  },
  {
    width: 120,
    ellipsis: true,
    title: '厂商',
    dataIndex: 'vendor',
  },
  {
    width: 120,
    ellipsis: true,
    title: '型号',
    dataIndex: 'productModel',
  },
  {
    width: 120,
    ellipsis: true,
    title: '购买日期',
    dataIndex: 'purchaseTime',
  },
  {
    width: 120,
    ellipsis: true,
    title: '过保日期',
    dataIndex: 'warrantyTime',
  },
  {
    width: 120,
    ellipsis: true,
    title: '维保厂商',
    dataIndex: 'warrantyVendor',
  },
  {
    width: 120,
    ellipsis: true,
    title: '维保状态',
    dataIndex: 'warrantyStatusName',
    render: (
      _: Record,
      {
        warrantyStatus,
      }: {
        warrantyStatus: {
          code: string;
          name: string;
        };
      }
    ) => warrantyStatus.name,
  },
  {
    width: 120,
    ellipsis: true,
    title: '状态',
    dataIndex: 'operationStatusName',
    render: (
      _: Record,
      {
        operationStatus,
      }: {
        operationStatus: {
          code: string;
          name: string;
        };
      }
    ) => operationStatus.name,
  },
];

const defaultScrollX = defaultColumns.reduce((w, { width }) => w + width, 0);
