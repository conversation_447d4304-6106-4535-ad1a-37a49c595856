---
description: '将未读的通知中心-站内消息标记为已读的按钮。'
labels: ['component', 'on-site-messages']
---

import { OnSiteMessagesMarkAsRead } from './on-site-messages-mark-as-read';

## OnSiteMessagesMarkAsRead

- `variant: selected`: 按钮文案为“标记已读”，且会渲染成红色的危险按钮。

- `variant: all`: 按钮文案为“全部已读”。

### 使用

```js
<OnSiteMessagesMarkAsRead state={state} type={type} variant="selected" />

<OnSiteMessagesMarkAsRead state={state} type={type} variant="all" />
```
