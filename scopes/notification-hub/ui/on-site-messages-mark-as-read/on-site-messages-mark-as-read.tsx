import React, { useCallback } from 'react';

import { Button } from '@manyun/base-ui.ui.button';

import { useLazyOnSiteMessages } from '@manyun/notification-hub.hook.use-lazy-on-site-messages';
import { useOnSiteMessagesMarkAsRead } from '@manyun/notification-hub.hook.use-on-site-messages-mark-as-read';
import { useOnSiteMessagesSelected } from '@manyun/notification-hub.hook.use-on-site-messages-selected';
import { useOnSizeMessagesUnreadNum } from '@manyun/notification-hub.hook.use-on-size-messages-unread-num';
import type {
  OnSiteMessageState,
  OnSiteMessageType,
} from '@manyun/notification-hub.model.on-site-messages';
import type { SvcQuery } from '@manyun/notification-hub.service.mark-on-site-messages-as-read';
import type { Variant } from '@manyun/service.dccm.mark-on-site-messages-as-read';

export type OnSiteMessagesMarkAsReadProps = {
  state: OnSiteMessageState;
  type: OnSiteMessageType;
  variant: Variant;
  onSuccess?(svcQuery: SvcQuery): void;
};

export function OnSiteMessagesMarkAsRead({
  state,
  type,
  variant,
  onSuccess,
}: OnSiteMessagesMarkAsReadProps) {
  const [, messages] = useLazyOnSiteMessages({ state, type });
  const [selectedIds] = useOnSiteMessagesSelected({ type });
  const [markAsRead, { loading }] = useOnSiteMessagesMarkAsRead({ type });
  const [getOnSiteMessagesUnreadNum] = useOnSizeMessagesUnreadNum();

  const markVariantAsRead = useCallback(() => {
    markAsRead({
      variant,
      successCb: svcQuery => {
        getOnSiteMessagesUnreadNum();
        onSuccess?.(svcQuery);
      },
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [markAsRead, variant]);
  const selectedVariant = variant === 'selected';

  return (
    <Button
      // danger={selectedVariant}
      loading={loading}
      disabled={
        selectedVariant ? selectedIds.length <= 0 : messages.loading || messages.data.total <= 0
      }
      onClick={markVariantAsRead}
      type={selectedVariant ? 'primary' : 'default'}
    >
      {selectedVariant ? '标记已读' : '全部已读'}
    </Button>
  );
}
