import React from 'react';

import 'antd/lib/button/style/css';

import {
  OnSiteMessageState,
  OnSiteMessageType,
} from '@manyun/notification-hub.model.on-site-messages';
// @ts-ignore
import { OnSiteMessagesReduxWrapper } from '@manyun/notification-hub.state.on-site-messages/dist/on-site-messages.composition-wrapper';

import { OnSiteMessagesMarkAsRead } from './on-site-messages-mark-as-read';

export const MarkSelectedMessagesAsRead = () => (
  <OnSiteMessagesReduxWrapper>
    <OnSiteMessagesMarkAsRead
      state={OnSiteMessageState.Unread}
      type={OnSiteMessageType.All}
      variant="selected"
    />
  </OnSiteMessagesReduxWrapper>
);

export const MarkAllMessagesAsRead = () => (
  <OnSiteMessagesReduxWrapper>
    <OnSiteMessagesMarkAsRead
      state={OnSiteMessageState.Unread}
      type={OnSiteMessageType.All}
      variant="all"
    />
  </OnSiteMessagesReduxWrapper>
);
