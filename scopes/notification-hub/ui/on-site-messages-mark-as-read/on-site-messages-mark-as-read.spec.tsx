import React from 'react';

import { render } from '@testing-library/react';

import {
  MarkAllMessagesAsRead,
  MarkSelectedMessagesAsRead,
} from './on-site-messages-mark-as-read.composition';

test('should render the `selected` variant', () => {
  const { getByText } = render(<MarkSelectedMessagesAsRead />);
  expect(getByText('标记已读')).toBeTruthy();
});

test('should render the `all` variant', () => {
  const { getByText } = render(<MarkAllMessagesAsRead />);
  expect(getByText('全部已读')).toBeTruthy();
});
