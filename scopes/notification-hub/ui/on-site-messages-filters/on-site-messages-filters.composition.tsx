import React from 'react';

import {
  OnSiteMessageState,
  OnSiteMessageType,
} from '@manyun/notification-hub.model.on-site-messages';
// @ts-ignore
import { OnSiteMessagesReduxWrapper } from '@manyun/notification-hub.state.on-site-messages/dist/on-site-messages.composition-wrapper';

import { OnSiteMessagesFilters } from './on-site-messages-filters';

export const BasicOnSiteMessagesFilters = () => (
  <OnSiteMessagesReduxWrapper>
    <OnSiteMessagesFilters state={OnSiteMessageState.Unread} type={OnSiteMessageType.All} />
  </OnSiteMessagesReduxWrapper>
);
