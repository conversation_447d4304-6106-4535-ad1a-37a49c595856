import React from 'react';

import moment from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';

import { useLazyOnSiteMessages } from '@manyun/notification-hub.hook.use-lazy-on-site-messages';
import { useOnSiteMessagesFields } from '@manyun/notification-hub.hook.use-on-site-messages-fields';
import type {
  OnSiteMessageState,
  OnSiteMessageType,
} from '@manyun/notification-hub.model.on-site-messages';

import styles from './on-site-messages-filters.module.less';

export type OnSiteMessagesFiltersProps = {
  state: OnSiteMessageState;
  type: OnSiteMessageType;
};

export function OnSiteMessagesFilters({ state, type }: OnSiteMessagesFiltersProps) {
  const [getMessages, { loading }] = useLazyOnSiteMessages({ state, type });
  const [{ timeRange }, setFields] = useOnSiteMessagesFields({ state, type });

  return (
    <div className={styles.filtersContainer}>
      <label htmlFor="on-site-messages_fields--time-range">创建时间</label>
      <DatePicker.RangePicker
        id="on-site-messages_fields--time-range"
        value={timeRange.length < 2 ? undefined : [moment(timeRange[0]), moment(timeRange[1])]}
        onChange={dates => {
          if (!(dates && dates.length === 2 && dates[0] && dates[1])) {
            setFields({
              timeRange: [],
            });
            return;
          }
          setFields({
            timeRange: dates.length < 2 ? [] : [dates[0]!.valueOf(), dates[1]!.valueOf()],
          });
        }}
        showTime
      />
      <Button
        type="primary"
        loading={loading}
        onClick={() => {
          setFields({ page: 1 });
          getMessages();
        }}
      >
        搜索
      </Button>
    </div>
  );
}
