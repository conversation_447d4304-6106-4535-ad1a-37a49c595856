import React from 'react';

import 'antd/lib/message/style/css';

import {
  OnSiteMessageState,
  OnSiteMessageType,
} from '@manyun/notification-hub.model.on-site-messages';
// @ts-ignore
import { OnSiteMessagesReduxWrapper } from '@manyun/notification-hub.state.on-site-messages/dist/on-site-messages.composition-wrapper';

import { OnSiteMessages } from './on-site-messages';

export const BasicOnSiteMessages = () => (
  <OnSiteMessagesReduxWrapper>
    <OnSiteMessages state={OnSiteMessageState.Unread} type={OnSiteMessageType.All} />
  </OnSiteMessagesReduxWrapper>
);
