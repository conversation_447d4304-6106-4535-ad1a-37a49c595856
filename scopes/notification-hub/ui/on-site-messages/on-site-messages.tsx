import type { TableRowSelection } from 'antd/es/table/interface';
import get from 'lodash.get';
import moment from 'moment';
import React, { useCallback, useEffect } from 'react';
import { useSelector } from 'react-redux';

import { Modal } from '@manyun/base-ui.ui.modal';
import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useLazyOnSiteMessages } from '@manyun/notification-hub.hook.use-lazy-on-site-messages';
import { useOnSiteMessagesFields } from '@manyun/notification-hub.hook.use-on-site-messages-fields';
import { useOnSiteMessagesSelected } from '@manyun/notification-hub.hook.use-on-site-messages-selected';
import {
  BackendOnSiteMessage,
  OnSiteMessageState,
  OnSiteMessageType,
} from '@manyun/notification-hub.model.on-site-messages';
import type { TargetContentList } from '@manyun/notification-hub.model.on-site-messages';
import { OnSiteMessageContent } from '@manyun/notification-hub.ui.on-site-message-content';
import { OnSiteMessagesOowDevices } from '@manyun/notification-hub.ui.on-site-messages-oow-devices';
import type { BackendMetadata } from '@manyun/resource-hub.model.metadata';
import { SpaceOrDeviceLink } from '@manyun/resource-hub.ui.space-or-device-link';

import { SelectedStoreState, selectDeviceMapping } from './on-site-message-content-utils';
import styles from './on-site-messages.module.less';

export type OnSiteMessagesProps = {
  state: OnSiteMessageState;
  type: OnSiteMessageType;
};
type DeviceTypeMapper = Record<string, BackendMetadata | undefined> | undefined;
type ColumnRecord = string | undefined;
type SpaceGuid = {
  blockGuid: string;
  blockTag: string;
  idcGuid: string;
  idcTag: string;
  roomGuid: string;
  roomTag: string;
};

export function OnSiteMessages({ state, type }: OnSiteMessagesProps) {
  const [{ page, pageSize }, setFields] = useOnSiteMessagesFields({ state, type });
  const [selectedIds, setSelectedIds] = useOnSiteMessagesSelected({ type });
  const [getMessages, { loading, data }] = useLazyOnSiteMessages({ state, type });
  const deviceTypeMapper = useSelector<any, SelectedStoreState>(selectDeviceMapping);
  const [visible, setvisible] = React.useState<boolean | undefined>(false);
  const [deviceId, setDeviceId] = React.useState<string>('');
  const deviceColumns = getBaseColumns(deviceTypeMapper?.normalizedList).filter(
    ({ defaultVisible = true, visible }) => defaultVisible || visible
  );
  const scrollX = deviceColumns.reduce((w, { width }) => w + width, 0);

  useEffect(() => {
    getMessages();
  }, [getMessages]);

  const pageHandler = useCallback(
    (_page: number, _pageSize?: number) => {
      if (selectedIds.length > 0) {
        setSelectedIds([]);
      }
      setFields({ page: _page, pageSize: _pageSize });
      getMessages();
    },
    [selectedIds, setSelectedIds, setFields, getMessages]
  );

  const allowSelect = state === OnSiteMessageState.Unread;
  const rowSelection: TableRowSelection<BackendOnSiteMessage> | undefined = allowSelect
    ? {
        selectedRowKeys: selectedIds,
        onChange: setSelectedIds as any, // Avoid type errors, because antd Table's `rowSelection.onChange` has an incorrect type.
      }
    : undefined;

  const handleModalVisible = () => {
    setvisible(true);
  };

  const handleDeviceId = (id: string) => {
    setDeviceId(id);
  };

  const columns = [
    {
      ellipsis: true,
      title: '消息内容',
      dataIndex: 'content',
      render: (
        content: string,
        { targetContentList, id }: { targetContentList: TargetContentList; id: any }
      ) => {
        const Title = (
          <OnSiteMessageContent
            noticeId={id}
            content={content}
            targetContentList={targetContentList}
            handleModalVisible={handleModalVisible}
            handleDeviceId={handleDeviceId}
          />
        );

        return <Typography.Text ellipsis={{ tooltip: true }}>{Title}</Typography.Text>;
      },
    },
    {
      width: 180,
      ellipsis: true,
      title: '创建时间',
      dataIndex: 'gmtCreate',
      sorter: (a: any, b: any) => moment(a.gmtCreate).valueOf() - moment(b.gmtCreate).valueOf(),
      render: (gmtCreate: string) => moment(gmtCreate).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      width: 160,
      title: '类型',
      dataIndex: 'insideScenes',
    },
  ];
  return (
    <>
      <Table
        size="small"
        rowKey="id"
        columns={columns}
        loading={loading}
        dataSource={data.data}
        pagination={{
          /* REFACTOR ME START */
          // size: 'small',
          showQuickJumper: true,
          showTotal: () => `共 ${data.total} 条`,
          showSizeChanger: true,
          pageSizeOptions: ['10', '20', '30', '50', '100', '200'],
          /* REFACTOR ME END */

          current: page,
          pageSize,
          total: data.total,
          onChange: pageHandler,
        }}
        rowSelection={rowSelection}
        className={styles.onSiteMessagesTableContain}
      />
      <Modal
        visible={visible}
        title="过保设备"
        width="75%"
        footer={null}
        onCancel={() => setvisible(false)}
      >
        {visible && (
          <OnSiteMessagesOowDevices
            columns={deviceColumns}
            scroll={{ x: scrollX }}
            targetId={deviceId}
          />
        )}
      </Modal>
    </>
  );
}

const getBaseColumns = (metaCategory: DeviceTypeMapper | undefined) => [
  {
    width: 330,
    ellipsis: true,
    title: '资产ID',
    dataIndex: 'assetNo',
    visible: true,
    render: (assetNo: string, { guid }: { guid: string }) => {
      if (!assetNo) {
        return '--';
      }
      return <SpaceOrDeviceLink id={guid} type="DEVICE_GUID" text={assetNo} />;
    },
  },
  {
    width: 220,
    ellipsis: true,
    title: 'SN',
    dataIndex: 'serialNumber',
    defaultVisible: false,
  },
  {
    width: 220,
    ellipsis: true,
    title: '名称',
    dataIndex: 'name',
    visible: true,
  },
  {
    width: 220,
    ellipsis: true,
    title: '设备标签',
    dataIndex: 'deviceLabel',
    defaultVisible: false,
  },
  {
    width: 220,
    ellipsis: true,
    title: '映射标签',
    dataIndex: 'tag',
    defaultVisible: false,
  },
  {
    width: 120,
    ellipsis: true,
    title: '一级分类',
    dataIndex: 'topCategory',
    defaultVisible: false,
    render(topCategory: string) {
      return get(metaCategory, [topCategory, 'metaName'], topCategory);
    },
  },
  {
    width: 120,
    ellipsis: true,
    title: '二级分类',
    dataIndex: 'secondCategory',
    defaultVisible: false,
    render(secondCategory: string) {
      return get(metaCategory, [secondCategory, 'metaName'], secondCategory);
    },
  },
  {
    width: 120,
    ellipsis: true,
    title: '三级分类',
    dataIndex: 'deviceType',
    visible: true,
    render(deviceType: string) {
      return get(metaCategory, [deviceType, 'metaName'], deviceType);
    },
  },
  {
    width: 120,
    ellipsis: true,
    title: '机房编号',
    dataIndex: 'idcTag',
    render: (_: ColumnRecord, { spaceGuid }: { spaceGuid: SpaceGuid }) => spaceGuid.idcTag,
  },
  {
    width: 120,
    ellipsis: true,
    title: '楼栋编号',
    dataIndex: 'blockTag',
    render: (_: ColumnRecord, { spaceGuid }: { spaceGuid: SpaceGuid }) => spaceGuid.blockTag,
  },
  {
    width: 120,
    ellipsis: true,
    title: '包间编号',
    dataIndex: 'roomTag',
    render: (_: ColumnRecord, { spaceGuid }: { spaceGuid: SpaceGuid }) => spaceGuid.roomTag,
  },

  {
    ellipsis: true,
    width: 120,
    title: '扩展位置',
    dataIndex: 'extendPosition',
  },
  {
    ellipsis: true,
    width: 120,
    title: '厂商',
    dataIndex: 'vendor',
  },
  {
    ellipsis: true,
    width: 120,
    title: '型号',
    dataIndex: 'productModel',
  },
  {
    width: 120,
    title: '购买日期',
    dataIndex: 'purchaseTime',
    dataType: 'date',
  },
  {
    width: 120,
    title: '过保日期',
    dataIndex: 'warrantyTime',
    dataType: 'date',
  },
  {
    width: 120,
    ellipsis: true,
    title: '维保厂商',
    dataIndex: 'warrantyVendor',
  },
  {
    width: 120,
    ellipsis: true,
    title: '维保状态',
    dataIndex: 'warrantyStatusName',
    render: (
      _: ColumnRecord,
      {
        warrantyStatus,
      }: {
        warrantyStatus: {
          code: string;
          name: string;
        };
      }
    ) => warrantyStatus.name,
  },
  {
    width: 120,
    ellipsis: true,
    title: '状态',
    dataIndex: 'operationStatusName',
    render: (
      _: ColumnRecord,
      {
        operationStatus,
      }: {
        operationStatus: {
          code: string;
          name: string;
        };
      }
    ) => operationStatus.name,
  },
];
