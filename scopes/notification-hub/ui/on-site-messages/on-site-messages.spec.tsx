import React from 'react';

import { render, waitFor } from '@testing-library/react';

import { BasicOnSiteMessages } from './on-site-messages.composition';

test('should render with the correct text', async () => {
  // const { getByText } = render(<BasicOnSiteMessages />);
  // expect(getByText('消息内容')).toBeTruthy();
  // expect(getByText('创建时间')).toBeTruthy();
  // expect(getByText('类型')).toBeTruthy();
  // await waitFor(() => getByText('共 100 条'));
  // expect(getByText('共 100 条')).toBeTruthy();
});
