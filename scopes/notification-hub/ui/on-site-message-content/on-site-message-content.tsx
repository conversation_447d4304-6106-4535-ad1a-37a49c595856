import React from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { useApps } from '@manyun/dc-brain.context.apps';
import { Link } from '@manyun/dc-brain.navigation.link';
import { AnyTargetLink } from '@manyun/dc-brain.ui.any-target-link';
import { useOnSizeMessagesUnreadNum } from '@manyun/notification-hub.hook.use-on-size-messages-unread-num';
import type { TargetContentList } from '@manyun/notification-hub.model.on-site-messages';
import { TargetType } from '@manyun/notification-hub.model.on-site-messages';
import { markOnSiteMessagesAsRead } from '@manyun/notification-hub.service.mark-on-site-messages-as-read';

import { MaintenanceTargetRender } from './on-site-message-content-maintenance-render';

export type OnSiteMessageContentProps = {
  content: string;
  noticeId: number;
  targetContentList: TargetContentList;
  useNativeLink?: boolean;
  handleModalVisible?: () => void;
  handleDeviceId?: (id: string) => void;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
} & Record<string, any>;

export const OnSiteMessageContent = React.forwardRef(
  (
    {
      content,
      noticeId,
      targetContentList,
      useNativeLink,
      handleModalVisible,
      handleDeviceId,
      type,
      ...rest
    }: OnSiteMessageContentProps,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    ref?: React.Ref<any>
  ) => {
    const { sales } = useApps();
    // const [markAsRead, { loading }] = useOnSiteMessagesMarkAsRead({ type: OnSiteMessageType.All });
    const [getOnSiteMessagesUnreadNum] = useOnSizeMessagesUnreadNum();
    // const [selectedIds, setSelectedIds] = useOnSiteMessagesSelected({ type });
    const markNoticeAsRead = async (id: number, targetType: TargetType) => {
      const { error } = await markOnSiteMessagesAsRead({ ids: [id], variant: 'selected' });
      if (error) {
        message.error(error.message);
        return;
      }
      if (targetType !== TargetType.MAINTENANCE_POOL) {
        getOnSiteMessagesUnreadNum();
      }
    };

    if (!content) {
      return <span />;
    }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let newContent: any = content;
    if (targetContentList) {
      //listRegex = {0}/{1}/...  numberRegex=0/1/...
      const listRegex = /\{\d+\}/g;
      const numberRegex = /\d+/g;

      //去除{0}/{1}/...的内容数组
      const mainText = content.split(listRegex);

      if (!content.match(listRegex)?.length) {
        return (
          <span {...rest} ref={ref}>
            {content}
          </span>
        );
      }

      const numberList = content.match(listRegex)?.join().match(numberRegex);

      newContent = numberList?.map((item, index) => {
        const name = targetContentList[parseInt(item)]?.targetName;
        const id = targetContentList[parseInt(item)]?.targetId;
        const targetType = targetContentList[parseInt(item)]?.targetType;
        const shouldOpenInNewTab = [
          TargetType.APPROVAL_DETAIL,
          TargetType.DRILL_SERVICE_DETAIL,
          TargetType.EVENT_DETAIL,
          TargetType.CHANGE,
          TargetType.N_EVENT_REPORT,
        ].includes(targetType);
        let targetRender;
        if (targetType === TargetType.COMPASS) {
          return (
            <span
              key={item}
              onClick={() => {
                markNoticeAsRead(noticeId, targetType);
              }}
            >
              {mainText[index]} <a href={`${sales.baseURL}page/crm/idc-compass-data`}>{name}</a>
            </span>
          );
        }
        if (targetType === TargetType.MAINTENANCE_POOL) {
          targetRender = () => (
            <MaintenanceTargetRender
              text={name}
              id={id}
              handleModalVisible={handleModalVisible}
              handleDeviceId={handleDeviceId}
            />
          );
        }
        if (index === numberList.length - 1) {
          //last one
          return (
            <span
              key={item}
              onClick={() => {
                markNoticeAsRead(noticeId, targetType);
              }}
            >
              {mainText[index]}
              <AnyTargetLink
                name={name}
                id={id}
                type={targetType}
                render={targetRender}
                useNativeLink={useNativeLink}
                target={shouldOpenInNewTab ? '_blank' : '_self'}
              />
              {mainText[index + 1]}
            </span>
          );
        }
        return (
          <span
            key={item}
            onClick={() => {
              markNoticeAsRead(noticeId, targetType);
            }}
          >
            {mainText[index]}
            <AnyTargetLink
              name={name}
              id={id}
              type={targetType}
              render={targetRender}
              useNativeLink={useNativeLink}
              target={shouldOpenInNewTab ? '_blank' : '_self'}
            />
          </span>
        );
      });
    } else {
      //regex 正则匹配 *任意字符*
      const regex = /\*[\s\S]*\*/g;
      const subContext = content.match(regex);
      if (subContext && subContext[0]) {
        const [href, text] = subContext[0].replace(/\*/g, '').split('|');
        if (href && text) {
          newContent = (
            <>
              {content.replace(regex, '')}
              <Link href={href} external>
                {text}
              </Link>
            </>
          );
        }
      }
    }
    return (
      <span {...rest} ref={ref}>
        {newContent}
      </span>
    );
  }
);

OnSiteMessageContent.displayName = 'OnSiteMessageContent';
