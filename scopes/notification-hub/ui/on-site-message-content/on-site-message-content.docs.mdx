---
description: 'A OnSiteMessageContent component.'
labels: ['label1', 'label2', 'label3']
---

import { OnSiteMessageContent } from './on-site-message-content';

## React Component for rendering text

A basic div that renders some text

### Component usage

```js
<OnSiteMessageContent
  content="[超出过保]设备数量:{0},超出过保日期"
  targetContentList={[
    {
      targetId: '1638288000000=1=1',
      targetName: '1',
      targetType: TargetType.MAINTENANCE_POOL,
    },
  ]}
/>
```
