import React from 'react';

import { Button } from '@manyun/base-ui.ui.button';

type MaintenanceTargetRenderType = {
  text: string;
  id: string;
  handleModalVisible?: () => void;
  handleDeviceId?: (id: string) => void;
};

export function MaintenanceTargetRender({
  text,
  id,
  handleModalVisible,
  handleDeviceId,
}: MaintenanceTargetRenderType) {
  React.useEffect(() => {
    if (handleDeviceId) {
      handleDeviceId(id);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  return (
    <>
      <Button type="link" onClick={handleModalVisible} style={{ height: 'auto', padding: 0 }}>
        {text}
      </Button>
    </>
  );
}
