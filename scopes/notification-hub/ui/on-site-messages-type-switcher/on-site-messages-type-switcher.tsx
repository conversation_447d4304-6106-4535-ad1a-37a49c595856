import React from 'react';

import { Tabs } from '@manyun/base-ui.ui.tabs';
import { useOnSiteMessagesFields } from '@manyun/notification-hub.hook.use-on-site-messages-fields';
import { useOnSiteMessagesUrlParams } from '@manyun/notification-hub.hook.use-on-site-messages-url-params';
import { useOnSizeMessagesUnreadNum } from '@manyun/notification-hub.hook.use-on-size-messages-unread-num';
import {
  OnSiteMessageState,
  OnSiteMessageType,
} from '@manyun/notification-hub.model.on-site-messages';

export type OnSiteMessagesTypeSwitcherProps = {
  children: (type: OnSiteMessageType) => JSX.Element;
};

export function OnSiteMessagesTypeSwitcher({ children }: OnSiteMessagesTypeSwitcherProps) {
  const [{ type, state }, setUrlParams] = useOnSiteMessagesUrlParams();
  const [, setFields] = useOnSiteMessagesFields({ state, type });

  const [, { unreadNumKeyMap }] = useOnSizeMessagesUnreadNum();

  const showNum = (num: number) => {
    if (state === OnSiteMessageState.Unread) {
      return `(${num})`;
    }
    return '';
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
      <Tabs
        activeKey={type}
        onChange={newType => {
          setUrlParams({ type: newType as OnSiteMessageType });
          setFields({ page: 1 });
        }}
      >
        <Tabs.TabPane key={OnSiteMessageType.All} tab="全部消息" />
        <Tabs.TabPane
          key={OnSiteMessageType.Notice}
          tab={`公告消息${showNum(unreadNumKeyMap.notice)}`}
        />
        <Tabs.TabPane
          key={OnSiteMessageType.Urge}
          tab={`催办消息${showNum(unreadNumKeyMap.urge)}`}
        />
        <Tabs.TabPane
          key={OnSiteMessageType.Event}
          tab={`事件消息${showNum(unreadNumKeyMap.event)}`}
        />
        <Tabs.TabPane
          key={OnSiteMessageType.Drill}
          tab={`演练消息${showNum(unreadNumKeyMap.drill)}`}
        />
        <Tabs.TabPane
          key={OnSiteMessageType.Asset}
          tab={`资产消息${showNum(unreadNumKeyMap.asset)}`}
        />
        <Tabs.TabPane
          key={OnSiteMessageType.Ticket}
          tab={`工单消息${showNum(unreadNumKeyMap.ticket)}`}
        />
        <Tabs.TabPane
          key={OnSiteMessageType.Inventory}
          tab={`库存消息${showNum(unreadNumKeyMap.inventory)}`}
        />

        <Tabs.TabPane
          key={OnSiteMessageType.Attendance}
          tab={`考勤消息${showNum(unreadNumKeyMap.attendance)}`}
        />
        <Tabs.TabPane
          key={OnSiteMessageType.Approval}
          tab={`审批消息${showNum(unreadNumKeyMap.approval)}`}
        />
        <Tabs.TabPane
          key={OnSiteMessageType.Todo}
          tab={`待办消息${showNum(unreadNumKeyMap.todo)}`}
        />
        <Tabs.TabPane
          key={OnSiteMessageType.TestPerformance}
          tab={`试用期绩效${showNum(unreadNumKeyMap.testPerformance)}`}
        />
        <Tabs.TabPane
          key={OnSiteMessageType.Performance}
          tab={`年度绩效${showNum(unreadNumKeyMap.performance)}`}
        />
        <Tabs.TabPane
          key={OnSiteMessageType.ScheduleSubscribe}
          tab={`任务消息${showNum(unreadNumKeyMap.scheduleSubscribe)}`}
        />
        <Tabs.TabPane
          key={OnSiteMessageType.examSubscribe}
          tab={`知识消息${showNum(unreadNumKeyMap.examSubscribe)}`}
        />
        <Tabs.TabPane
          key={OnSiteMessageType.SECURITY}
          tab={`安全消息${showNum(unreadNumKeyMap.security)}`}
        />
        <Tabs.TabPane
          key={OnSiteMessageType.RISK_REGISTER}
          tab={`风险消息${showNum(unreadNumKeyMap.riskRegister)}`}
        />
        <Tabs.TabPane
          key={OnSiteMessageType.REPORT}
          tab={`报表消息${showNum(unreadNumKeyMap.report)}`}
        />
      </Tabs>
      {children(type)}
    </div>
  );
}
