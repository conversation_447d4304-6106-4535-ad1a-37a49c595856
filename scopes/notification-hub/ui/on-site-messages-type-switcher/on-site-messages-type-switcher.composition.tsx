import React from 'react';

import 'antd/lib/tabs/style/css';

import { OnSiteMessagesRouterWrapper } from '@manyun/notification-hub.hook.use-on-site-messages-url-params';
import { OnSiteMessageState } from '@manyun/notification-hub.model.on-site-messages';

import { OnSiteMessagesTypeSwitcher } from './on-site-messages-type-switcher';

export const BasicOnSiteMessagesTypeSwitcher = () => (
  <OnSiteMessagesRouterWrapper state={OnSiteMessageState.Unread}>
    <OnSiteMessagesTypeSwitcher>{type => <span>{type}</span>}</OnSiteMessagesTypeSwitcher>
  </OnSiteMessagesRouterWrapper>
);
