import React from 'react';

import { OnSiteMessagesRouterWrapper } from '@manyun/notification-hub.hook.use-on-site-messages-url-params';

import { OnSiteMessagesStateSwitcher } from './on-site-messages-state-switcher';

export const BasicOnSiteMessagesStateSwitcher = () => (
  <OnSiteMessagesRouterWrapper state="unread">
    <OnSiteMessagesStateSwitcher>{state => <span>{state}</span>}</OnSiteMessagesStateSwitcher>
  </OnSiteMessagesRouterWrapper>
);
