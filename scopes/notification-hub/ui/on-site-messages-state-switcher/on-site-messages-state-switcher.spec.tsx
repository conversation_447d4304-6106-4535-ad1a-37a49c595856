import React from 'react';

import { fireEvent, render } from '@testing-library/react';

import { OnSiteMessageState } from '@manyun/notification-hub.model.on-site-messages';

import { BasicOnSiteMessagesStateSwitcher } from './on-site-messages-state-switcher.composition';

test('should render with the correct text under the selected on-site messages state', () => {
  const { getByText, getByRole } = render(<BasicOnSiteMessagesStateSwitcher />);

  expect(getByText(OnSiteMessageState.Unread)).toBeTruthy();

  const readTab = getByRole('tab', { selected: false });
  fireEvent.click(readTab);

  expect(getByText(OnSiteMessageState.Read)).toBeTruthy();
});
