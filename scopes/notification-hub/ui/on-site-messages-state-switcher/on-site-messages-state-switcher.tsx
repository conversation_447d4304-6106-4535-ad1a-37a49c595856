import React from 'react';

import Icon from '@ant-design/icons';

import { Card } from '@manyun/base-ui.ui.card';
import { Menu } from '@manyun/base-ui.ui.menu';
import { Space } from '@manyun/base-ui.ui.space';

import { useOnSiteMessagesFields } from '@manyun/notification-hub.hook.use-on-site-messages-fields';
import { useOnSiteMessagesUrlParams } from '@manyun/notification-hub.hook.use-on-site-messages-url-params';
import { useOnSizeMessagesUnreadNum } from '@manyun/notification-hub.hook.use-on-size-messages-unread-num';
import { OnSiteMessageState } from '@manyun/notification-hub.model.on-site-messages';

import { Read, Unread } from './manyun-icons';
import styles from './on-site-messages-state-switcher.module.less';

export type OnSiteMessagesStateSwitcherProps = {
  children(state: OnSiteMessageState): JSX.Element;
};

export function OnSiteMessagesStateSwitcher({ children }: OnSiteMessagesStateSwitcherProps) {
  const [{ state, type }, setParams] = useOnSiteMessagesUrlParams();
  const [, setFields] = useOnSiteMessagesFields({ state, type });
  const [getOnSiteMessagesUnreadNum, { unreadNumKeyMap }] = useOnSizeMessagesUnreadNum();

  React.useEffect(() => {
    getOnSiteMessagesUnreadNum();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div style={{ display: 'flex', width: '100%' }}>
      <Space size="middle" className={styles.noticeSpace}>
        <Card
          style={{ height: '100%', minWidth: 200 }}
          className={styles.noticeStateCard}
          bordered={false}
        >
          <Menu
            mode="vertical"
            defaultSelectedKeys={[state]}
            onClick={({ key }) => {
              setParams({ state: key as OnSiteMessageState });
              setFields({ page: 1 });
            }}
          >
            <Menu.Item key={OnSiteMessageState.Unread}>
              <Icon component={Unread} />
              <span>未读({unreadNumKeyMap.all})</span>
            </Menu.Item>
            <Menu.Item key={OnSiteMessageState.Read}>
              <Icon component={Read} />
              <span>已读</span>
            </Menu.Item>
          </Menu>
        </Card>
        {children(state)}
      </Space>
    </div>
  );
}
