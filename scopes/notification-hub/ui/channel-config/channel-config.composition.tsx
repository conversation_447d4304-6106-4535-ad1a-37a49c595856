import React, { useEffect, useState } from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { useRemoteMock } from '@manyun/service.request';

import { ChannelConfig } from './channel-config';

export const BasicChannelConfig = () => {
  const [ready, setReady] = React.useState(false);

  React.useEffect(() => {
    const mockOff = useRemoteMock('web', {
      adapter: 'default',
    });
    setReady(true);

    return () => {
      mockOff();
    };
  }, []);

  if (!ready) {
    return <span>Loading</span>;
  }

  return (
    <ConfigProvider>
      <ChannelConfig createWebhookCb={() => true} />
    </ConfigProvider>
  );
};
