import React, { useEffect, useState } from 'react';

import { PlusOutlined } from '@ant-design/icons';
import { InfoCircleOutlined } from '@ant-design/icons';
import shortid from 'shortid';

import { Button } from '@manyun/base-ui.ui.button';
import { EditableTable } from '@manyun/base-ui.ui.editable-table';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';

import {
  ChannelConfigType,
  EmailChannelKey,
  WebhookChannelKey,
} from '@manyun/notification-hub.model.notice-config';
import { createChannelWeb } from '@manyun/notification-hub.service.notify.create-channel';
import { deleteChannelWeb } from '@manyun/notification-hub.service.notify.delete-channel';
import { fetchChannelsWeb } from '@manyun/notification-hub.service.notify.fetch-channels';
import type { BackendChannel } from '@manyun/notification-hub.service.notify.fetch-channels';
import {
  ServiceQ as updateChannelServiceQ,
  updateChannelWeb,
} from '@manyun/notification-hub.service.notify.update-channel';

export type ChannelConfigProps = {
  buttonText?: string;
  createWebhookCb: () => void;
};

export function ChannelConfig({ buttonText = '渠道配置', createWebhookCb }: ChannelConfigProps) {
  const [visible, setVisible] = useState(false);
  const [emailData, setEmailData] = useState<any[]>([]);
  const [emailEditingRowKey, setEmailEditingRowKey] = useState<string | null>(null);
  const [emaildeleteByCancel, setEmailDeleteByCancel] = useState(false);
  const [emailTableLoading, setEmailTableLoading] = useState(false);
  const [actionChannelType, setActionChannelType] = useState<ChannelConfigType>(
    ChannelConfigType.EMAIL
  );

  const getChannels = async () => {
    serviceStart();
    const { error, data } = await fetchChannelsWeb({ channelType: actionChannelType });
    serviceEnd();
    if (error) {
      message.error(error.message);
      return;
    }
    const dataSource = composeData(data.data, actionChannelType);

    if (actionChannelType === ChannelConfigType.EMAIL) {
      setEmailData(dataSource);
    }
  };

  useEffect(() => {
    if (!visible) {
      return;
    }
    getChannels();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible, actionChannelType]);

  const deleteChannel = async (params: { channelType: ChannelConfigType; configGroup: number }) => {
    serviceStart();
    const { error } = await deleteChannelWeb(params);
    serviceEnd();
    if (error) {
      message.error(error.message);
      return false;
    }
    message.success('删除成功！');
    getChannels();
    return true;
  };

  const updateChannel = async (params: any[]) => {
    serviceStart();
    const { error } = await updateChannelWeb(params);
    serviceEnd();
    if (error) {
      message.error(error.message);
      return false;
    }
    message.success('更新成功！');
    getChannels();
    return true;
  };

  const createChannel = async (channelConfigList: any[], channelType: ChannelConfigType) => {
    const params = {
      channelType,
      channelConfigList,
    };
    serviceStart();
    const { error } = await createChannelWeb(params);
    serviceEnd();
    if (error) {
      message.error(error.message);
      return false;
    }
    message.success('添加成功');
    getChannels();
    return true;
  };

  function serviceStart() {
    setEmailTableLoading(true);
  }

  function serviceEnd() {
    setEmailTableLoading(false);
  }

  function onClose() {
    setEmailEditingRowKey(null);
    setEmailDeleteByCancel(false);
    setActionChannelType(ChannelConfigType.WEB_HOOK);
    setVisible(false);
  }

  return (
    <div>
      <Button onClick={() => setVisible(true)}>{buttonText}</Button>
      <Modal
        width={1000}
        visible={visible}
        title="渠道配置"
        onCancel={onClose}
        onOk={() => {
          if (emailEditingRowKey) {
            message.error('有尚未保存的邮箱地址');
            return;
          }
          onClose();
        }}
      >
        <Tabs
          activeKey={actionChannelType}
          onChange={key =>
            setActionChannelType(key as ChannelConfigType.WEB_HOOK | ChannelConfigType.EMAIL)
          }
        >
          <Tabs.TabPane
            tab={
              <>
                邮箱服务器地址&nbsp;
                <Tooltip title="只能配置一个邮箱服务器地址。">
                  <InfoCircleOutlined />
                </Tooltip>
              </>
            }
            key={ChannelConfigType.EMAIL}
          >
            <EditableTable
              rowKey="id"
              size="small"
              showActionsColumn={true}
              mergeProp="mergeRowsKey"
              columns={[
                {
                  title: '用户名',
                  dataIndex: 'userName.configValue',
                  editable: true,
                  ellipsis: true,
                  editingCtrl: <Input style={{ width: 160 }} />,
                  formItemProps: {
                    rules: [
                      { required: true, message: '用户名必填！' },
                      { max: 128, message: '最多输入 128 个字符！' },
                    ],
                  },
                  render: (_, { userName }) => userName?.configValue,
                },
                {
                  title: '密码',
                  dataIndex: 'password.configValue',
                  editable: true,
                  ellipsis: true,
                  editingCtrl: <Input style={{ width: 160 }} />,
                  formItemProps: {
                    rules: [
                      { required: true, message: '密码必填！' },
                      { max: 128, message: '最多输入 128 个字符！' },
                    ],
                  },
                  render: (_, { password }) => password?.configValue,
                },
                {
                  title: '服务器地址',
                  dataIndex: 'serverAddress.configValue',
                  editable: true,
                  ellipsis: true,
                  editingCtrl: <Input />,
                  formItemProps: {
                    rules: [
                      { required: true, message: '服务器地址必填！' },
                      { max: 128, message: '最多输入 128 个字符！' },
                    ],
                  },
                  render: (_, { serverAddress }) => serverAddress?.configValue,
                },
                {
                  title: '协议',
                  dataIndex: 'https.configValue',
                  editable: true,
                  ellipsis: true,
                  editingCtrl: <Input />,
                  formItemProps: {
                    rules: [
                      { required: true, message: '协议必填！' },
                      { max: 128, message: '最多输入 128 个字符！' },
                    ],
                  },
                  render: (_, { https }) => https?.configValue,
                },
                {
                  title: '端口号',
                  dataIndex: 'port.configValue',
                  editable: true,
                  ellipsis: true,
                  editingCtrl: <InputNumber precision={0} max={9999} />,
                  formItemProps: {
                    rules: [{ required: true, message: '端口号必填！' }],
                  },
                  render: (_, { port }) => port?.configValue,
                },
              ]}
              dataSource={emailData}
              editingRowKey={emailEditingRowKey}
              loading={emailTableLoading}
              onEdit={(rowKey: string) => {
                setEmailEditingRowKey(rowKey);
              }}
              onCancel={() => {
                if (emaildeleteByCancel) {
                  setEmailData(prevData => {
                    const nextData = [...prevData];
                    const idx = nextData.findIndex(record => record.id === emailEditingRowKey);
                    if (idx > -1) {
                      nextData.splice(idx, 1);
                    }

                    return nextData;
                  });
                }
                setEmailEditingRowKey(null);
                setEmailDeleteByCancel(false);
              }}
              onSave={async (__, data: any[]) => {
                const row: {
                  'userName.configValue': string;
                  'password.configValue': string;
                  'serverAddress.configValue': string;
                  'https.configValue': string;
                  'port.configValue': string;
                } = data[0];
                let rs;
                if (emaildeleteByCancel) {
                  const channelConfigList = [
                    {
                      configKey: 'USERNAME',
                      configValue: row['userName.configValue'],
                    },
                    {
                      configKey: 'PASSWORD',
                      configValue: row['password.configValue'],
                    },
                    {
                      configKey: 'HOST',
                      configValue: row['serverAddress.configValue'],
                    },
                    {
                      configKey: 'PROTOCOL',
                      configValue: row['https.configValue'],
                    },
                    {
                      configKey: 'PORT',
                      configValue: row['port.configValue'],
                    },
                  ];
                  rs = await createChannel(channelConfigList, ChannelConfigType.EMAIL);
                } else {
                  const prev = emailData[0];
                  let params: updateChannelServiceQ = [];
                  if (prev.userName && prev.userName.configValue !== row['userName.configValue']) {
                    params.push({ id: prev.userName.id, configValue: row['userName.configValue'] });
                  }
                  if (prev.password && prev.password.configValue !== row['password.configValue']) {
                    params.push({ id: prev.password.id, configValue: row['password.configValue'] });
                  }
                  if (
                    prev.serverAddress &&
                    prev.serverAddress.configValue !== row['serverAddress.configValue']
                  ) {
                    params.push({
                      id: prev.serverAddress.id,
                      configValue: row['serverAddress.configValue'],
                    });
                  }
                  if (prev.https && prev.https.configValue !== row['https.configValue']) {
                    params.push({ id: prev.https.id, configValue: row['https.configValue'] });
                  }
                  if (prev.port && prev.port.configValue !== row['port.configValue']) {
                    params.push({ id: prev.port.id, configValue: row['port.configValue'] });
                  }
                  // 如果未更改任何数据，则不请求api
                  if (!params.length) {
                    setEmailEditingRowKey(null);
                    setEmailDeleteByCancel(false);
                    return;
                  }
                  rs = await updateChannel(params);
                }
                if (rs) {
                  getChannels();
                  setEmailEditingRowKey(null);
                  setEmailDeleteByCancel(false);
                }
              }}
              onDelete={(rowKey: string, data: any) => {
                Modal.confirm({
                  title: '确定要删除这条信息吗？',
                  content: '删除之后数据将不可更改。',
                  onOk() {
                    (async () => {
                      const rs = await deleteChannel({
                        channelType: ChannelConfigType.EMAIL,
                        configGroup: Number(rowKey),
                      });
                      if (rs) {
                        getChannels();
                        setEmailEditingRowKey(null);
                      }
                    })();
                  },
                });
              }}
              canDelete={(record: any, idx: number, hasEditingRow: boolean) => false}
            />
            {!emailData.length && (
              <Button
                type="link"
                onClick={() => {
                  const id = shortid();
                  setEmailData([
                    {
                      userName: '',
                      password: '',
                      host: '',
                      protocol: '',
                      port: '',
                      id,
                    },
                    ...emailData,
                  ]);
                  setEmailEditingRowKey(id);
                  setEmailDeleteByCancel(true);
                }}
              >
                <PlusOutlined />
                添加邮箱服务器地址
              </Button>
            )}
          </Tabs.TabPane>
        </Tabs>
      </Modal>
    </div>
  );
}

/**
 * 合成渠道数据，apiR是按照字段拆分的数组
 */
function composeData(
  backendData: BackendChannel[],
  channeltype: ChannelConfigType.WEB_HOOK | ChannelConfigType.EMAIL
) {
  let expectDataMap: any = {};
  backendData.forEach(channel => {
    if (!expectDataMap[channel.configGroup]) {
      expectDataMap[channel.configGroup] = { [channel.configKey]: channel };
      return;
    }
    expectDataMap[channel.configGroup] = {
      ...expectDataMap[channel.configGroup],
      [channel.configKey]: channel,
    };
  });
  return Object.keys(expectDataMap).map(configGroup => {
    const data = expectDataMap[configGroup];
    if (channeltype === ChannelConfigType.WEB_HOOK) {
      return {
        id: configGroup,
        name: data[WebhookChannelKey.Name],
        webhook: data[WebhookChannelKey.Address],
        robotKey: data[WebhookChannelKey.Secret],
      };
    }
    return {
      id: configGroup,
      userName: data[EmailChannelKey.UserName],
      password: data[EmailChannelKey.Password],
      serverAddress: data[EmailChannelKey.Host],
      https: data[EmailChannelKey.Protocol],
      port: data[EmailChannelKey.Port],
    };
  });
}
