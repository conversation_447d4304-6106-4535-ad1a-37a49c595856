import React from 'react';

import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { BasicReceiver } from './receiver.composition';

it('receiver', async () => {
  render(<BasicReceiver />);
  const num = await screen.findByText('1');
  expect(num).toBeInTheDocument();
  const numBtn = screen.getByLabelText('btn: user-num');
  await userEvent.click(numBtn);

  const modalTotal = await screen.findByText('接收人');

  expect(modalTotal).toBeInTheDocument();
});
