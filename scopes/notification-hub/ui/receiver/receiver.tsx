import React, { useEffect, useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import type { TabsProps } from '@manyun/base-ui.ui.tabs';

import { UserSwitch } from '@manyun/notification-hub.model.notice-config';
import type { Role } from '@manyun/notification-hub.model.notice-config';

import { ReceiveRoleTable } from './components/receive-role-table';
import { ReceiveUserTable } from './components/receive-user-table';

export type ReceiverProps = {
  userIds?: number[];
  roleList?: Role[];
  extraUserSwitch: UserSwitch;
  notifyCode: string;
  noticeType: string;
  update: (data: any) => Promise<Boolean>;
};
// 取消写死的方式，在后端给出相应字段前，先放开权限，下一期会根据后端给的字段进行判断
// const AvailableNotifyCodes = [
//   'HANDOVER_EMAIL',
//   'VISITOR_COMMON',
//   'VISITOR_VIP',
//   'EXAM_START',
//   'EXAM_REMIND',
//   'EXAM_CREATE',
//   'TASK_SLA',
//   'BORROW_TIMEOUT',
//   'BORROW',
//   'MAINTENANCE_SLA',
//   'INVENTORY_LEVEL',
//   'SCHEDULE_MSG',
// ];

// const AvailableEventTypes = ['ALARM', 'EVENT'];
export function Receiver({
  userIds,
  roleList,
  notifyCode,
  noticeType,
  extraUserSwitch,
  update,
}: ReceiverProps) {
  const [roleData, setRoleData] = useState<any[] | undefined>();
  const [userData, setUserData] = useState<any[] | undefined>();
  const [editingRoleRowKey, setEditingRoleRowKey] = useState<React.Key[]>([]);
  const [editingUserRowKey, setEditingUserRowKey] = useState<React.Key[]>([]);

  const [visible, setVisible] = useState(false);
  const [activeTabKey, setActiveTabKey] = useState<string | undefined>(undefined);

  const onClose = async () => {
    setVisible(false);
  };

  const onFinish = async () => {
    if (editingRoleRowKey?.length | editingUserRowKey?.length) {
      message.error('表格存在未保存的数据');
      return;
    }

    setVisible(false);

    const rs = await update({
      notifyCode,
      roleList: roleData,
      userIdList: userData,
    });

    if (rs) {
      message.success('操作成功');
      setEditingRoleRowKey([]);
      setEditingUserRowKey([]);
    }
  };
  useEffect(() => {
    if (visible) {
      setActiveTabKey('user');
      setRoleData(roleList);
      setUserData(userIds);
    }
  }, [roleList, userIds, visible]);

  return (
    <>
      <Button type="link" compact aria-label="btn: user-num" onClick={() => setVisible(true)}>
        查看
      </Button>
      <Modal width={700} title="接收对象" open={visible} onCancel={onClose} onOk={onFinish}>
        <Space style={{ width: '100%' }} direction="vertical">
          <p>消息类型：{noticeType}</p>
          <Tabs
            activeKey={activeTabKey}
            items={
              [
                extraUserSwitch === UserSwitch.OnlyRole
                  ? null
                  : {
                      label: '接收人',
                      key: 'user',
                      children: (
                        <ReceiveUserTable
                          userIds={userData}
                          setUserList={setUserData}
                          handleEditRowKeys={setEditingUserRowKey}
                        />
                      ),
                    },
                extraUserSwitch === UserSwitch.OnlyUser
                  ? null
                  : {
                      label: '角色',
                      key: 'role',
                      children: (
                        <ReceiveRoleTable
                          roleData={roleData}
                          setRoleData={setRoleData}
                          handleEditRowKeys={setEditingRoleRowKey}
                        />
                      ),
                    },
              ].filter(Boolean) as TabsProps['items']
            }
            onChange={setActiveTabKey}
          />
        </Space>
      </Modal>
    </>
  );
}
