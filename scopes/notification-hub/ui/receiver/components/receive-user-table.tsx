import React, { useEffect, useRef, useState } from 'react';

import shortid from 'shortid';

import {
  EditableFormInstance,
  EditableProTable,
  ProColumns,
} from '@manyun/base-ui.ui.editable-pro-table';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import type { DefaultOptionType } from '@manyun/base-ui.ui.select';

import { fetchUsersByIdsWeb } from '@manyun/auth-hub.service.pm.fetch-users-by-ids';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';

export type DataSourceType = {
  id: React.Key;
  user: {
    label: string;
    value: string;
    name: string;
  } | null;
  email: string;
  phone: string;
};
export type ReceiveUserTableProps = {
  userIds: number[] | undefined;
  setUserList: (userList?: number[] | React.Key[]) => void;
  handleEditRowKeys: (keys: React.Key[]) => void;
};

export function ReceiveUserTable({
  userIds,
  setUserList,
  handleEditRowKeys,
}: ReceiveUserTableProps) {
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [dataSource, setDataSource] = useState<DataSourceType[] | undefined>([]);
  const editorFormRef = useRef<EditableFormInstance<DataSourceType>>();

  const columns: ProColumns<DataSourceType>[] = [
    {
      title: '姓名',
      dataIndex: 'user',
      width: 160,
      formItemProps: () => {
        return {
          rules: [{ required: true, message: '此项为必填项' }],
        };
      },
      renderFormItem: (_, { recordKey, record }) => {
        return (
          <UserSelect
            onChange={(_, nodeValue: DefaultOptionType | DefaultOptionType[] | any) => {
              if (recordKey) {
                const user = nodeValue['data-user'];
                editorFormRef.current?.setRowData?.(recordKey.toString(), {
                  email: user.email,
                  phone: user.mobileNumber,
                  user: {
                    label: user.name,
                    value: user.id,
                    name: user.name,
                  },
                  id: user.id,
                });
              }
            }}
          />
        );
      },
      render: (_, { user }) => {
        return <span>{user?.name}</span>;
      },
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      renderFormItem: () => {
        return <Input disabled />;
      },
    },
    {
      title: '手机',
      width: 160,
      ellipsis: true,
      dataIndex: 'phone',
      renderFormItem: () => {
        return <Input disabled />;
      },
    },
    {
      title: '操作',
      valueType: 'option',
      width: 120,
      render: (text, record, _, action) => [
        // eslint-disable-next-line jsx-a11y/anchor-is-valid
        <a
          key="editable"
          onClick={() => {
            action?.startEditable?.(record.id);
          }}
        >
          编辑
        </a>,
        // eslint-disable-next-line jsx-a11y/anchor-is-valid
        <a
          key="delete"
          onClick={() => {
            const filterDataSource = dataSource?.filter(item => item.id !== record.id);
            setDataSource(filterDataSource);
            setUserList(filterDataSource?.map(({ id }) => id));
          }}
        >
          删除
        </a>,
      ],
    },
  ];
  useEffect(() => {
    // getUsers({ fields: { key: keyword, pageNum: 1, pageSize: 200, needResource: false } });
    if (userIds && userIds.length) {
      // setDataSource(userIds?.map(role => ({ ...role, id: role.roleCode })));
      const fetchUsers = async (list: any[]) => {
        const { error, data } = await fetchUsersByIdsWeb({ userIds: list });
        if (error) {
          message.error(error.message);
          return;
        }
        setDataSource(
          data.data.map((user: any) => ({
            id: user.id,
            user: {
              label: user.name,
              name: user.name,
              value: user.id,
            },
            email: user.email,
            phone: user.mobileNumber,
          }))
        );
      };
      fetchUsers(userIds);
    } else {
      setDataSource([]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userIds]);
  return (
    <EditableProTable
      rowKey="id"
      value={dataSource}
      scroll={{ x: 'max-content' }}
      columns={columns}
      onChange={value => {
        setDataSource(value);
        setUserList(value.map(({ id }) => id));
      }}
      editableFormRef={editorFormRef}
      recordCreatorProps={{
        position: 'top',
        record: () => ({
          id: shortid(),
          user: null,
          email: '--',
          phone: '--',
        }),
      }}
      editable={{
        type: 'single',
        editableKeys,
        onSave: async (rowKey, data, row) => {
          const idList = dataSource?.map(data => data.id);
          const isSelf = row?.id === data?.id;
          if (idList?.includes(data.id) && data.id !== rowKey.toString() && !isSelf) {
            message.error('添加用户重复');
            return Promise.reject();
          } else {
            return Promise.resolve();
          }
        },
        onChange: keys => {
          setEditableRowKeys(keys);
          handleEditRowKeys(keys);
        },
        actionRender: (row, config, dom) => [dom.save, dom.cancel],
      }}
    />
  );
}
