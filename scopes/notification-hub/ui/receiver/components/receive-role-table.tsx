import React, { useEffect, useRef, useState } from 'react';

import shortid from 'shortid';

import {
  EditableFormInstance,
  EditableProTable,
  ProColumns,
} from '@manyun/base-ui.ui.editable-pro-table';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import type { DefaultOptionType } from '@manyun/base-ui.ui.select';

import { useRoles } from '@manyun/auth-hub.hook.use-roles';
import { RoleSelect } from '@manyun/auth-hub.ui.role-select';
import { RoleText } from '@manyun/auth-hub.ui.role-text';
import { Role } from '@manyun/notification-hub.model.notice-config';

export type DataSourceType = {
  id: React.Key;
} & Role;
export type ReceiveRoleTableProps = {
  roleData: DataSourceType[] | undefined;
  setRoleData: (roleList: DataSourceType[] | undefined) => void;
  handleEditRowKeys: (keys: React.Key[]) => void;
};

export function ReceiveRoleTable({
  roleData,
  setRoleData,
  handleEditRowKeys,
}: ReceiveRoleTableProps) {
  const [, getRoles] = useRoles();

  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [dataSource, setDataSource] = useState<DataSourceType[] | undefined>([]);
  const editorFormRef = useRef<EditableFormInstance<DataSourceType>>();

  const columns: ProColumns<DataSourceType>[] = [
    {
      title: '角色名称',
      dataIndex: 'roleName',
      width: 160,
      formItemProps: () => {
        return {
          rules: [{ required: true, message: '此项为必填项' }],
        };
      },
      renderFormItem: (_, { recordKey, record }) => {
        return (
          <RoleSelect
            value={record?.roleCode}
            fieldNames={{ value: 'code', label: 'name' }}
            onChange={(_, nodeValue: DefaultOptionType | DefaultOptionType[] | any) => {
              if (recordKey) {
                editorFormRef.current?.setRowData?.(recordKey.toString(), {
                  roleCode: nodeValue.code,
                  roleName: nodeValue.name,
                  remark: nodeValue.remark,
                });
              }
            }}
          />
        );
      },
      render: (_, record) => {
        return <RoleText code={record.roleCode} />;
      },
    },
    {
      title: '角色ID',
      width: 100,

      dataIndex: 'roleCode',
      renderFormItem: () => {
        return <Input disabled />;
      },
    },
    {
      title: '备注',
      width: 120,
      ellipsis: true,
      dataIndex: 'remark',
      renderFormItem: () => {
        return <Input disabled />;
      },
    },
    {
      title: '操作',
      valueType: 'option',
      width: 120,
      render: (text, record, _, action) => [
        // eslint-disable-next-line jsx-a11y/anchor-is-valid
        <a
          key="editable"
          onClick={() => {
            action?.startEditable?.(record.id);
          }}
        >
          编辑
        </a>,
        // eslint-disable-next-line jsx-a11y/anchor-is-valid
        <a
          key="delete"
          onClick={() => {
            const filterDataSource = dataSource?.filter(item => item.id !== record.id);
            setDataSource(filterDataSource);
            setRoleData(filterDataSource);
          }}
        >
          删除
        </a>,
      ],
    },
  ];
  useEffect(() => {
    getRoles();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  useEffect(() => {
    if (roleData) {
      setDataSource(roleData?.map(role => ({ ...role, id: shortid() })));
    } else {
      setDataSource([]);
    }
  }, [roleData]);
  return (
    <EditableProTable
      rowKey="id"
      value={dataSource}
      scroll={{ x: 'max-content' }}
      columns={columns}
      onChange={value => {
        setDataSource(value);
        setRoleData(value);
      }}
      editableFormRef={editorFormRef}
      recordCreatorProps={{
        position: 'top',
        record: () => ({
          id: shortid(),
          roleCode: '--',
          roleName: '',
          remark: '--',
        }),
      }}
      editable={{
        type: 'single',
        editableKeys,
        onSave: async (rowKey, data, row) => {
          const isSelf = row?.roleCode === data?.roleCode;

          const idList = dataSource?.map(data => data.roleCode);
          if (idList?.includes(data.roleCode) && !isSelf) {
            message.error('添加角色重复');
            return Promise.reject();
          } else {
            return Promise.resolve();
          }
        },
        onChange: keys => {
          setEditableRowKeys(keys);
          handleEditRowKeys(keys);
        },
        actionRender: (row, config, dom) => [dom.save, dom.cancel],
      }}
    />
  );
}
