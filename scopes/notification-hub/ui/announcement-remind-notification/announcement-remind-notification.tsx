import React, { useCallback, useEffect, useState } from 'react';

import NotificationTwoTone from '@ant-design/icons/es/icons/NotificationTwoTone';
import moment from 'moment';

import { ConfigProvider } from '@manyun/base-ui.context.config';
import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { notification } from '@manyun/base-ui.ui.notification';
import { Space } from '@manyun/base-ui.ui.space';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';

import type { Importance } from '@manyun/notification-hub.model.notice';
import { generateNoticeLocation } from '@manyun/notification-hub.route.notification-routes';
import { fetchPopupList } from '@manyun/notification-hub.service.fetch-popup-list';
import type { BackEndPopUp } from '@manyun/notification-hub.service.fetch-popup-list';
import { readedNoticePopup } from '@manyun/notification-hub.service.readed-notice-popup';

import noticeVoice from './assets/notice-voice.mp3';

export type AnnouncementRemindNotificationProps = {
  key: number;
  id: number;
  title: string;
  importance: Importance;
  content: string;
  validBeginDate: number;
  validEndDate: number;
};

export function AnnouncementRemindNotificationList() {
  const [notificationList, setNotificationList] = useState<BackEndPopUp[]>();

  const _fetchPopupList = useCallback(async () => {
    const { data, error } = await fetchPopupList({ currentTime: moment().valueOf() });
    if (error) {
      message.error(error.message);
      return;
    }
    setNotificationList(data.data);
  }, []);

  useEffect(() => {
    _fetchPopupList();
    let countdownInterval = window.setInterval(_fetchPopupList, 5 * 60 * 1000);

    return () => {
      window.clearInterval(countdownInterval);
    };
  }, [_fetchPopupList]);

  return (
    <>
      {notificationList?.map(item => (
        <AnnouncementRemindNotification
          key={item.id}
          id={item.id}
          title={item.title}
          importance={item.importance}
          content={item.content}
          validBeginDate={item.validBeginDate}
          validEndDate={item.validEndDate}
        />
      ))}
    </>
  );
}

export function AnnouncementRemindNotification({
  id,
  title,
  importance,
  content,
  validBeginDate,
  validEndDate,
}: AnnouncementRemindNotificationProps) {
  const [submitLoading, setSubmitLoading] = useState<boolean>(false);

  const _readedNoticePopup = async () => {
    setSubmitLoading(true);

    const { error } = await readedNoticePopup({
      id: id,
    });
    setSubmitLoading(false);

    if (error) {
      message.error(error.message);
      return;
    }
  };

  const openNotification = () => {
    const key = id.toString();

    const onNotificationClose = () => {
      _readedNoticePopup();
      notification.close(key);
    };

    const header = (
      <ConfigProvider>
        <Space>
          <Typography.Text>{title}</Typography.Text>
          {switchTag(importance)}
        </Space>
      </ConfigProvider>
    );

    const mainBody = (
      <ConfigProvider>
        <Space direction="vertical">
          <Typography.Text>{content}</Typography.Text>
          <Typography.Text type="secondary">
            {moment(validBeginDate).format('YYYY-MM-DD')}~
            {moment(validEndDate).format('YYYY-MM-DD')}
          </Typography.Text>
        </Space>
      </ConfigProvider>
    );

    const btn = (
      <ConfigProvider>
        <Space>
          <Button type="link" onClick={onNotificationClose} loading={submitLoading}>
            我知道了
          </Button>
          <Button
            type="primary"
            onClick={() => {
              onNotificationClose();
              window.open(generateNoticeLocation({ id: id }));
            }}
          >
            查看详情
          </Button>
        </Space>
      </ConfigProvider>
    );

    new Audio(noticeVoice).play();

    notification.open({
      message: header,
      description: mainBody,
      duration: 0,
      btn,
      key,
      onClose: onNotificationClose,
      icon: <NotificationTwoTone style={{ fontSize: '16px' }} />,
      top: 88,
    });
  };

  useEffect(() => {
    if (id) {
      openNotification();
    }
  }, [id]);

  useEffect(() => {
    return () => {
      notification.close(id?.toString());
    };
  }, []);

  return null;
}

function switchTag(importance: Importance) {
  switch (importance) {
    case 'HIGH':
      return <Tag color="error">重要</Tag>;
    case 'MID':
      return <Tag color="warning">中等</Tag>;
    case 'LOW':
      return <Tag color="success">普通</Tag>;
    default:
      return null;
  }
}
