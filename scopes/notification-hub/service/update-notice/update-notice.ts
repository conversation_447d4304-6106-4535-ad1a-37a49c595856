/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-8
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcRespData } from './update-notice.type';

const endpoint = '/pm/notice/update';

/**
 * @see [Doc](YAPI http://172.16.0.17:13000/project/78/interface/api/1568)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      variant
    );

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: null,
      };
    }

    return { error, data: data, ...rest };
  };
}
