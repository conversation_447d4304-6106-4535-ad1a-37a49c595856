---
description: 'A updateNotice HTTP API service.'
labels: ['service', 'http', update-notice]
---

## 概述

TODO

## 使用

### Browser

```ts
import { updateNotice } from '@notification-hub/service.update-notice';

const { error, data } = await updateNotice('success');
const { error, data } = await updateNotice('error');
```

### Node

```ts
import { updateNotice } from '@notification-hub/service.update-notice/dist/index.node';

const { data } = await updateNotice('success');

try {
  const { data } = await updateNotice('error');
} catch(error) {
  // ...
}
```
