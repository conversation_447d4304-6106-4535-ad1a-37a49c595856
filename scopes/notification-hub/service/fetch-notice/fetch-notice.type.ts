/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import type { BackendNotice, Notice } from '@manyun/notification-hub.model.notice';

export type SvcQuery = { id: number };

export type SvcRespData = Notice | null;

export type RequestRespData = BackendNotice | null;

export type ApiR = Response<Notice>;
