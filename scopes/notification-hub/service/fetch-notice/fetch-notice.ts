/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import { Notice } from '@manyun/notification-hub.model.notice';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { RequestRespData, SvcQuery, SvcRespData } from './fetch-notice.type';

const endpoint = '/pm/notice/detail';

/**
 * @see [查询公告详情](http://172.16.0.17:13000/project/78/interface/api/1528)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async ({ id }: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: SvcQuery = { id };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, SvcQuery>(
      endpoint,
      params
    );

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: null,
      };
    }

    return { error, data: Notice.fromApiObject(data), ...rest };
  };
}
