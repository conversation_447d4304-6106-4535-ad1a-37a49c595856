---
description: 'A addNotice HTTP API service.'
labels: ['service', 'http', add-notice]
---

## 概述

添加公告的接口

## 使用

### Browser

```ts
import { addNotice } from '@notification-hub/service.add-notice';

const { error, data } = await addNotice('success');
const { error, data } = await addNotice('error');
```

### Node

```ts
import { addNotice } from '@notification-hub/service.add-notice/dist/index.node';

const { data } = await addNotice('success');

try {
  const { data } = await addNotice('error');
} catch (error) {
  // ...
}
```
