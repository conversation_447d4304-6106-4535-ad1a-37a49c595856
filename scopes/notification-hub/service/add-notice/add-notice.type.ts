/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-7
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcRespData = string | null;

export type RequestRespData = string | null;

export type ApiQ = {
  content: string;
  importance: string;
  rangeType: string;
  resourceIdList?: number[];
  title: string;
  validBeginDate: number;
  validEndDate: number;
  files?: any[];
  immediately: boolean /*是否立即生效 */;
  popUp: boolean /*是否弹出弹框 */;
};

export type ApiR = Response<RequestRespData>;
