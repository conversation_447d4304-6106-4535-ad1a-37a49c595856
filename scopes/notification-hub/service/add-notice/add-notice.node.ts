/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-7
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './add-notice';
import type { ApiQ, SvcRespData } from './add-notice.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function addNotice(variant: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
