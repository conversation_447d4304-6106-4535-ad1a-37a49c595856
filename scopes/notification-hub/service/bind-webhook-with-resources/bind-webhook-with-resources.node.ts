/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-1
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './bind-webhook-with-resources';
import type { ApiQ, SvcRespData } from './bind-webhook-with-resources.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function bindWebhookWithResources(
  svcQuery: ApiQ
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
