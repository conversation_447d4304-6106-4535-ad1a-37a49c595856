---
description: 'A bindWebhookWithResources HTTP API service.'
labels: ['service', 'http']
---

## 概述

TODO

## 使用

### Browser

```ts
import { bindWebhookWithResources } from '@manyun/notification-hub.service.bind-webhook-with-resources';

const { error, data } = await bindWebhookWithResources('success');
const { error, data } = await bindWebhookWithResources('error');
```

### Node

```ts
import { bindWebhookWithResources } from '@manyun/notification-hub.service.bind-webhook-with-resources/dist/index.node';

const { data } = await bindWebhookWithResources('success');
const { error, data } = await bindWebhookWithResources('error');
```
