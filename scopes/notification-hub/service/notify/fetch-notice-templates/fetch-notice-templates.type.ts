/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-11
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { BackendNoticeConfig } from '@manyun/notification-hub.model.notice-config';
import { NoticeConfig } from '@manyun/notification-hub.model.notice-config';

export type ServiceRD = {
  total: number;
  data: { eventType: string; notifyEventList: NoticeConfig[] }[];
};

export type RequestRD = {
  total: number;
  data: BackendNoticeConfig[] | null;
} | null;

export type ApiQ = {};

export type ApiR = ListResponse<RequestRD>;
