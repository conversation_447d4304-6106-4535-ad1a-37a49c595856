/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-11
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { endpoint } from './fetch-notice-templates';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock.onPost(endpoint).reply(() => {
    return [
      200,
      {
        data: [
          {
            id: 25,
            notifyCode: 'EVENT_NOTIFY',
            title: '事件通知',
            channels: 'INTERNAL_MESSAGE',
            remark: '事件通知',
            isDelete: 0,
            gmtCreate: 1627460889000,
            gmtModify: 1627460892000,
            eventType: null,
            contentTmp: '[事件] ${createrName}创建${blockGuid} ${eventLevel}事件，事件ID:{0}',
            extJson: null,
            notifyUser: null,
          },
          {
            id: 24,
            notifyCode: 'NOTICE',
            title: '公告通知',
            channels: 'EMAIL,WEB_HOOK,INTERNAL_MESSAGE',
            remark: '公告通知',
            isDelete: 0,
            gmtCreate: 1627440516000,
            gmtModify: 1627440518000,
            eventType: null,
            contentTmp: '{0}',
            extJson: {
              channelConfigInfoList: [
                {
                  channelType: 'WEB_HOOK',
                  configGroup: 1,
                  viewName: '钉钉告警机器人',
                },
              ],
            },
            notifyUser: {
              userIdList: [1],
            },
          },
        ],
        total: 10,
        success: true,
        errCode: null,
        errMessage: null,
      },
    ];
  });
}
