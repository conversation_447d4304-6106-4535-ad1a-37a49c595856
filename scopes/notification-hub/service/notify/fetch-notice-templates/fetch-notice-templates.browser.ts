/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-11
 *
 * @packageDocumentation
 */
import { NoticeConfig } from '@manyun/notification-hub.model.notice-config';
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { endpoint } from './fetch-notice-templates';
import type { ApiQ, RequestRD, ServiceRD } from './fetch-notice-templates.type';

/**
 * 通知模板列表
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/bnpnl9/xvsrpk#nBbmm)
 *
 * @param query Service query object
 * @returns
 */
export async function fetchNoticeTemplates(): Promise<EnhancedAxiosResponse<ServiceRD>> {
  const apiQ: ApiQ = {};

  const { error, data, ...rest } = await webRequest.tryPost<RequestRD, ApiQ>(endpoint, apiQ);

  if (error || data === null || !data.data) {
    return {
      ...rest,
      error,
      data: {
        total: 0,
        data: [],
      },
    };
  }

  return {
    ...rest,
    error,
    data: {
      total: data.total,
      data: data.data.map(({ notifyEventList, eventType }) => {
        if (!notifyEventList) {
          return {
            eventType,
            notifyEventList: [],
          };
        }
        return {
          eventType,
          notifyEventList: notifyEventList.map(NoticeConfig.fromApiObject),
        };
      }),
    },
  };
}
