/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-11
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { fetchNoticeTemplates as webService } from './fetch-notice-templates.browser';
import { registerWebMocks } from './fetch-notice-templates.mock';

// mocks for tests should resolve immediately.
registerWebMocks(getMock('web'));

test('fetchNoticeTemplates', async () => {
  const { data, error } = await webService();
  expect(typeof data.total).toBe('number');
  expect(error).toBe(undefined);
});
