/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-12
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import { ChannelConfigType } from '@manyun/notification-hub.model.notice-config';

export type ServiceQ = {
  channelType: ChannelConfigType.WEB_HOOK | ChannelConfigType.EMAIL;
  configGroup: number;
};

export type Bar = boolean;

export type RequestRD = boolean | null;

export type ApiQ = {
  channelType: ChannelConfigType.WEB_HOOK | ChannelConfigType.EMAIL;
  configGroup: number;
};

export type ApiR = Response<RequestRD>;
