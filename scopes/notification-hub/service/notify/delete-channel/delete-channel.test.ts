/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-12
 *
 * @packageDocumentation
 */
import { ChannelConfigType } from '@manyun/notification-hub.model.notice-config';
import { getMock } from '@manyun/service.request';

import { deleteChannel as webService } from './delete-channel.browser';
import { registerWebMocks } from './delete-channel.mock';

// mocks for tests should resolve immediately.
registerWebMocks(getMock('web'));

test('deleteChannel', async () => {
  const { data, error } = await webService({
    channelType: ChannelConfigType.EMAIL,
    configGroup: 2,
  });

  expect(typeof data).toBe('boolean');
  expect(error).toBe(undefined);
});
