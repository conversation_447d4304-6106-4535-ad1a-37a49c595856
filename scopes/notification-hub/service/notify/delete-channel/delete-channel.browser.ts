/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-12
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { endpoint } from './delete-channel';
import type { ApiQ, Bar, RequestRD, ServiceQ } from './delete-channel.type';

/**
 * 渠道配置删除
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/bnpnl9/xvsrpk#y6NTu)
 *
 * @param query Service query object
 * @returns
 */
export async function deleteChannel(query: ServiceQ): Promise<EnhancedAxiosResponse<Bar>> {
  const apiQ: ApiQ = {
    channelType: query.channelType,
    configGroup: query.configGroup,
  };

  const { error, data, ...rest } = await webRequest.tryPost<RequestRD, ApiQ>(endpoint, apiQ);

  if (error || data === null) {
    const _data: Bar = false;

    return { error, ...rest, data: _data };
  }

  return { error, data, ...rest };
}
