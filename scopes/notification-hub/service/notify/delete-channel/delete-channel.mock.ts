/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-12
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { endpoint } from './delete-channel';
import type { ApiQ, ApiR } from './delete-channel.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock.onPost(endpoint).reply(config => {
    return [
      200,
      {
        success: true,
        data: true,
        errCode: null,
        errMessage: null,
      } as ApiR,
    ];
  });
}
