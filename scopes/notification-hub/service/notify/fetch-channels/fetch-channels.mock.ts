/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-11
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { endpoint } from './fetch-channels';
import type { ApiQ, ApiR } from './fetch-channels.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock.onPost(endpoint).reply(config => {
    // POST 请求需要 parse config.data
    const { channelType } = JSON.parse(config.data) as ApiQ;

    return [
      200,
      {
        success: true,
        errCode: null,
        errMessage: null,
        data: Array(20)
          .fill(null)
          .map((_, idx) => ({
            id: idx + 1,
            channelType: channelType,
            configKey: 'NAME' + idx,
            configValue: '钉钉告警机器人' + idx,
            configGroup: 1,
            gmtCreate: 1631843914000,
            gmtModified: 1631843914000,
          })),
        total: 20,
      },
    ];
  });
}
