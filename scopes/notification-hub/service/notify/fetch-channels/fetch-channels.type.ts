/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-11
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import { ChannelConfigType } from '@manyun/notification-hub.model.notice-config';

export type BackendChannel = {
  id: number;
  channelType: string;
  configKey: string;
  configValue: string;
  configGroup: number;
  gmtCreate: number;
  gmtModified: number;
};

export type ServiceQ = {
  channelType: ChannelConfigType.EMAIL | ChannelConfigType.WEB_HOOK;
};

export type ServiceRD = {
  total: number;
  data: BackendChannel[];
};

export type RequestRD = {
  total: number;
  data: BackendChannel[] | null;
} | null;

export type ApiQ = ServiceQ;

export type ApiR = ListResponse<RequestRD>;
