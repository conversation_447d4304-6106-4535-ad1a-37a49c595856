/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-11
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { endpoint } from './fetch-channels';
import type { ApiQ, RequestRD, ServiceQ, ServiceRD } from './fetch-channels.type';

/**
 * 渠道配置查询
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/bnpnl9/xvsrpk#Gd5cj)
 *
 * @param query Service query object
 * @returns
 */
export async function fetchChannels(query: ServiceQ): Promise<EnhancedAxiosResponse<ServiceRD>> {
  const apiQ: ApiQ = {
    channelType: query.channelType,
  };
  const { error, data, ...rest } = await webRequest.tryPost<RequestRD, ApiQ>(endpoint, apiQ);

  if (error || data === null || data.data === null) {
    return { error, ...rest, data: { ...data, data: [], total: 0 } };
  }

  return {
    error,
    data: {
      data: data.data,
      total: data.total,
    },
    ...rest,
  };
}
