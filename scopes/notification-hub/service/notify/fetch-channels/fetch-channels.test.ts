/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-11
 *
 * @packageDocumentation
 */
import { ChannelConfigType } from '@manyun/notification-hub.model.notice-config';
import { getMock } from '@manyun/service.request';

import { fetchChannels as webService } from './fetch-channels.browser';
import { registerWebMocks } from './fetch-channels.mock';

// mocks for tests should resolve immediately.
registerWebMocks(getMock('web'));

test('fetchChannels', async () => {
  const { data, error } = await webService({ channelType: ChannelConfigType.EMAIL });
  expect(typeof data.total).toBe('number');
  expect(error).toBe(undefined);
});
