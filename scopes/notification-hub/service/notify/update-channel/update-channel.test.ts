/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-15
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { updateChannel as webService } from './update-channel.browser';
import { registerWebMocks } from './update-channel.mock';

// mocks for tests should resolve immediately.
registerWebMocks(getMock('web'));

test('updateChannel', async () => {
  const { data, error } = await webService([{ id: 9, configValue: '测试webHoo更新' }]);
  expect(data).toBe(true);
  expect(error).toBe(undefined);
});
