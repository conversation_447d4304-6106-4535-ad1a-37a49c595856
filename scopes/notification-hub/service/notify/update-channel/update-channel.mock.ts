/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-15
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { endpoint } from './update-channel';
import type { ApiR } from './update-channel.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock.onPost(endpoint).reply(() => {
    return [
      200,
      {
        success: true,
        errCode: null,
        errMessage: null,
      } as ApiR,
    ];
  });
}
