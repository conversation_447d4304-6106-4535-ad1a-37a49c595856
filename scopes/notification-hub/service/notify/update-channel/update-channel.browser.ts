/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-15
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { endpoint } from './update-channel';
import type { ApiQ, Bar, RequestRD, ServiceQ } from './update-channel.type';

/**
 * 渠道配置更新
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/bnpnl9/xvsrpk#x3K4f)
 *
 * @param query Service query object
 * @returns
 */
export async function updateChannel(query: ServiceQ): Promise<EnhancedAxiosResponse<Bar>> {
  const apiQ: ApiQ = {
    channelConfigList: query,
  };

  const { error, data, ...rest } = await webRequest.tryPost<RequestRD, ApiQ>(endpoint, apiQ);

  if (error || data === null) {
    const _data: Bar = false;

    return { error, ...rest, data: _data };
  }

  return { error, data, ...rest };
}
