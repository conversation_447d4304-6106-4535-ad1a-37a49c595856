/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-12
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type ServiceQ = {
  channelType: string;
  channelConfigList: { configKey: string; configValue: string }[];
};

export type Bar = boolean;

export type RequestRD = Bar | null;

export type ApiQ = ServiceQ;

export type ApiR = Response<RequestRD>;
