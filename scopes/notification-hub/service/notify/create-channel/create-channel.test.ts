/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-12
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { createChannel as webService } from './create-channel.browser';
import { registerWebMocks } from './create-channel.mock';

// mocks for tests should resolve immediately.
registerWebMocks(getMock('web'));

test('createChannel', async () => {
  const params = {
    channelType: 'WEB_HOOK',
    channelConfigList: [
      {
        configKey: 'NAME',
        configValue: '钉钉告警机器人',
      },
      {
        configKey: 'ADDRESS',
        configValue:
          'https://oapi.dingtalk.com/robot/send?access_token=325c84692d4e43de15dc93c232ecf389724d7a5f4f4d3dda6262cf59c7cb7723',
      },
      {
        configKey: 'SECRET',
        configValue: 'SEC0377ff20c5536736cca9ca2f102f2d6749df4c456a17f12e84415ccdade33ed2',
      },
    ],
  };
  const { data, error } = await webService(params);

  expect(data).toBe(true);
  expect(error).toBe(undefined);
});
