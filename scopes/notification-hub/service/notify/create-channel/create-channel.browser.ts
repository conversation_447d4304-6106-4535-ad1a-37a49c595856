/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-12
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { endpoint } from './create-channel';
import type { ApiQ, Bar, RequestRD, ServiceQ } from './create-channel.type';

/**
 * 渠道配置新增
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/bnpnl9/xvsrpk#V6sRd)
 *
 * @param query Service query object
 * @returns
 */
export async function createChannel(query: ServiceQ): Promise<EnhancedAxiosResponse<Bar>> {
  const apiQ: ApiQ = query;

  const { error, data, ...rest } = await webRequest.tryPost<RequestRD, ApiQ>(endpoint, apiQ);

  if (error || data === null) {
    const _data: Bar = false;

    return { error, ...rest, data: _data };
  }

  return { error, data, ...rest };
}
