/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-15
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { updateNoticeTemplate as webService } from './update-notice-template.browser';
import { registerWebMocks } from './update-notice-template.mock';

// mocks for tests should resolve immediately.
registerWebMocks(getMock('web'));

test('updateNoticeTemplate', async () => {
  const params = {
    notifyCode: 'NOTICE',
    channels: 'EMAIL',
    userIdList: [1],
    configInfoList: [
      {
        channelType: 'WEB_HOOK',
        configGroup: 1,
        viewName: '钉钉告警机器人',
      },
    ],
  };
  const { data, error } = await webService(params);

  expect(data).toBe(true);
  expect(error).toBe(undefined);
});
