/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-15
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import type { Role } from '@manyun/notification-hub.model.notice-config';

export type ConfigInfoList = {
  channelType: string;
  configGroup: number;
  viewName: string | null;
};

export type ServiceQ = {
  notifyCode: string;
  channels?: string;
  userIdList?: number[];
  configInfoList?: ConfigInfoList[];
  usable?: boolean;
  roleList?: Role[];
};

export type ServiceRD = boolean;

export type RequestRD = ServiceRD | null;

export type ApiQ = {
  notifyCode: string;
  channels?: string;
  userIdList?: number[] | null;
  configInfoList?: ConfigInfoList[];
  clearUser?: boolean;
  usable?: boolean;
  roleList?: Role[];
};

export type ApiR = Response<RequestRD>;
