/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-15
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { endpoint } from './update-notice-template';
import type { ApiQ, RequestRD, ServiceQ, ServiceRD } from './update-notice-template.type';

/**
 * 通知模板更新
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/bnpnl9/xvsrpk#oQRkY)
 *
 * @param query Service query object
 * @returns
 */
export async function updateNoticeTemplate(
  query: ServiceQ
): Promise<EnhancedAxiosResponse<ServiceRD>> {
  const apiD: ApiQ = {
    notifyCode: query.notifyCode,
    channels: query.channels,
    userIdList:
      Array.isArray(query.userIdList) && query.userIdList.length ? query.userIdList : null,
    configInfoList: query.configInfoList,
    clearUser: Array.isArray(query.userIdList) && !query.userIdList.length ? true : false,
    usable: query.usable,
    roleList: query.roleList,
  };
  const { error, data, ...rest } = await webRequest.tryPost<RequestRD, ApiQ>(endpoint, apiD);

  if (error || data === null) {
    return { error, ...rest, data: false };
  }

  return { error, data, ...rest };
}
