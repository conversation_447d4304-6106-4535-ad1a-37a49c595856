/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-1
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { bindWebhookWithScenes as webService } from './bind-webhook-with-scenes.browser';
import { bindWebhookWithScenes as nodeService } from './bind-webhook-with-scenes.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({
    webHookId: 1,
    notifyEventList: [],
  });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    webHookId: 0,
    notifyEventList: [],
  });

  expect(typeof error).toBe('object');
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService({
    webHookId: 1,
    notifyEventList: [],
  });

  expect(error).toBe(undefined);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({
    webHookId: 0,
    notifyEventList: [],
  });

  expect(typeof error).toBe('object');
});
