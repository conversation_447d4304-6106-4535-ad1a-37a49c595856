---
description: 'A bindWebhookWithScenes HTTP API service.'
labels: ['service', 'http']
---

## 概述

TODO

## 使用

### Browser

```ts
import { bindWebhookWithScenes } from '@manyun/notification-hub.service.bind-webhook-with-scenes';

const { error, data } = await bindWebhookWithScenes('success');
const { error, data } = await bindWebhookWithScenes('error');
```

### Node

```ts
import { bindWebhookWithScenes } from '@manyun/notification-hub.service.bind-webhook-with-scenes/dist/index.node';

const { data } = await bindWebhookWithScenes('success');
const { error, data } = await bindWebhookWithScenes('error');
```
