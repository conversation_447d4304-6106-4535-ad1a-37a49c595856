/**
 * <AUTHOR> Name <<EMAIL>>
 * @since 2021-12-14
 *
 * @packageDocumentation
 */
// 使用以下代码注册远程 Mocks
import { useRemoteMock } from '@manyun/service.request';

import { fetchOnSiteMessagesUnreadNum as webService } from './fetch-on-site-messages-unread-num.browser';

let mockOff: () => void;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

test('should resolve success response', async () => {
  const { error, data } = await webService();

  expect(error).toBe(undefined);
  expect(typeof data.all).toBe('number');
});
