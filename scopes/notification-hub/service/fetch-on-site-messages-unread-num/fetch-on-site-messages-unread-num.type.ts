/**
 * <AUTHOR> Name <<EMAIL>>
 * @since 2021-12-14
 *
 * @packageDocumentation
 */
import {
  BackendOnSiteMessageType,
  OnSiteMessageType,
} from '@manyun/notification-hub.model.on-site-messages';

export type SvcRespData = Record<OnSiteMessageType, number>;

export type BackendUnreadNum = {
  parentType: BackendOnSiteMessageType;
  unreadCount: number;
};

export type BackendUnreadNumData = BackendUnreadNum[];

export type RequestRespData = {
  data: BackendUnreadNumData | null;
  total: number;
};
