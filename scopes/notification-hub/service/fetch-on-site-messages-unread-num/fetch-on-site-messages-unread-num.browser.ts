/**
 * <AUTHOR> Name <<EMAIL>>
 * @since 2021-12-14
 *
 * @packageDocumentation
 */
import {
  BackendOnSiteMessageType,
  OnSiteMessageType,
} from '@manyun/notification-hub.model.on-site-messages';
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { endpoint } from './fetch-on-site-messages-unread-num';
import type {
  BackendUnreadNumData,
  RequestRespData,
  SvcRespData,
} from './fetch-on-site-messages-unread-num.type';

/**
 * 在这里补充 Service 说明
 *
 * @see [Doc](在这里补充 API 文档链接)
 *
 * @param variant
 * @returns
 */
export async function fetchOnSiteMessagesUnreadNum(): Promise<EnhancedAxiosResponse<SvcRespData>> {
  const { error, data, ...rest } = await webRequest.tryPost<RequestRespData>(endpoint, {});

  if (error || data === null || !data.data) {
    return {
      ...rest,
      error,
      data: {
        [OnSiteMessageType.All]: 0,
        [OnSiteMessageType.Notice]: 0,
        [OnSiteMessageType.Event]: 0,
        [OnSiteMessageType.Urge]: 0,
        [OnSiteMessageType.Asset]: 0,
        [OnSiteMessageType.Ticket]: 0,
        [OnSiteMessageType.Inventory]: 0,
        [OnSiteMessageType.Approval]: 0,
        [OnSiteMessageType.Todo]: 0,
        [OnSiteMessageType.ScheduleSubscribe]: 0,
        [OnSiteMessageType.examSubscribe]: 0,
        [OnSiteMessageType.Attendance]: 0,
        [OnSiteMessageType.Performance]: 0,
        [OnSiteMessageType.TestPerformance]: 0,
        [OnSiteMessageType.Drill]: 0,
        [OnSiteMessageType.SECURITY]: 0,
        [OnSiteMessageType.RISK_REGISTER]: 0,
        [OnSiteMessageType.REPORT]: 0,
      },
    };
  }

  return { error, data: getUnreadNum(data.data) as SvcRespData, ...rest };
}

function getUnreadNum(data: BackendUnreadNumData): SvcRespData {
  const statistics = {
    [OnSiteMessageType.All]: 0,
    [OnSiteMessageType.Notice]: 0,
    [OnSiteMessageType.Event]: 0,
    [OnSiteMessageType.Urge]: 0,
    [OnSiteMessageType.Asset]: 0,
    [OnSiteMessageType.Ticket]: 0,
    [OnSiteMessageType.Inventory]: 0,
    [OnSiteMessageType.Approval]: 0,
    [OnSiteMessageType.Todo]: 0,
    [OnSiteMessageType.ScheduleSubscribe]: 0,
    [OnSiteMessageType.examSubscribe]: 0,
    [OnSiteMessageType.Attendance]: 0,
    [OnSiteMessageType.Performance]: 0,
    [OnSiteMessageType.TestPerformance]: 0,
    [OnSiteMessageType.Drill]: 0,
    [OnSiteMessageType.SECURITY]: 0,
    [OnSiteMessageType.RISK_REGISTER]: 0,
    [OnSiteMessageType.REPORT]: 0,
  };
  data.forEach(({ parentType, unreadCount }) => {
    statistics[OnSiteMessageType.All] += unreadCount;

    if (parentType === BackendOnSiteMessageType.notice) {
      statistics[OnSiteMessageType.Notice] += unreadCount;
    }

    if (parentType === BackendOnSiteMessageType.event) {
      statistics[OnSiteMessageType.Event] += unreadCount;
    }
    if (parentType === BackendOnSiteMessageType.urge) {
      statistics[OnSiteMessageType.Urge] += unreadCount;
    }
    if (parentType === BackendOnSiteMessageType.asset) {
      statistics[OnSiteMessageType.Asset] += unreadCount;
    }

    if (parentType === BackendOnSiteMessageType.ticket) {
      statistics[OnSiteMessageType.Ticket] += unreadCount;
    }

    if (parentType === BackendOnSiteMessageType.approval) {
      statistics[OnSiteMessageType.Approval] += unreadCount;
    }

    if (parentType === BackendOnSiteMessageType.todo) {
      statistics[OnSiteMessageType.Todo] += unreadCount;
    }

    if (parentType === BackendOnSiteMessageType.scheduleSubscribe) {
      statistics[OnSiteMessageType.ScheduleSubscribe] += unreadCount;
    }

    if (parentType === BackendOnSiteMessageType.inventory) {
      statistics[OnSiteMessageType.Inventory] += unreadCount;
    }
    if (parentType === BackendOnSiteMessageType.examSubscribe) {
      statistics[OnSiteMessageType.examSubscribe] += unreadCount;
    }
    if (parentType === BackendOnSiteMessageType.attendance) {
      statistics[OnSiteMessageType.Attendance] += unreadCount;
    }
    if (parentType === BackendOnSiteMessageType.performance) {
      statistics[OnSiteMessageType.Performance] += unreadCount;
    }
    if (parentType === BackendOnSiteMessageType.testPerformance) {
      statistics[OnSiteMessageType.TestPerformance] += unreadCount;
    }
    if (parentType === BackendOnSiteMessageType.drill) {
      statistics[OnSiteMessageType.Drill] += unreadCount;
    }
    if (parentType === BackendOnSiteMessageType.security) {
      statistics[OnSiteMessageType.SECURITY] += unreadCount;
    }
    if (parentType === BackendOnSiteMessageType.riskRegister) {
      statistics[OnSiteMessageType.RISK_REGISTER] += unreadCount;
    }
    if (parentType === BackendOnSiteMessageType.report) {
      statistics[OnSiteMessageType.REPORT] += unreadCount;
    }
  });
  return statistics;
}
