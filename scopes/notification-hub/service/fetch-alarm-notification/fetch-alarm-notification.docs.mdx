---
description: 'A fetchAlarmNotification HTTP API service.'
labels: ['service', 'http', fetch-alarm-notification]
---

## 概述

查询告警通报详情

## 使用

### Browser

```ts
import { fetchAlarmNotification } from '@manyun/notification-hub.service.fetch-alarm-notification';

const { error, data } = await fetchAlarmNotification({
  bizId: '1',
});
```

### Node

```ts
import { fetchAlarmNotification } from '@manyun/notification-hub.service.fetch-alarm-notification/dist/index.node';

const { data } = await fetchAlarmNotification({
  bizId: '1',
});
```
