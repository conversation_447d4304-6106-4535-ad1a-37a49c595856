/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-5
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { BackendChannelType } from '@manyun/monitoring.model.notification-configure-item-object';

export type NotifyState =
  | 'WAITING_SEND'
  | 'WAITING_CONFIRM'
  | 'MISSED'
  | 'SUCCESS'
  | 'FAILURE'
  | 'NOT_CONNECTED'
  | 'NOT_CONFIRM';

export type SvcQuery = {
  bizId?: string;
  notifyCode?: string;
};

export type BackendAlarmNotificationDetail = {
  bizId: string;
  id: number;
  userId: number;
  userName: string;
  notifyChannel: BackendChannelType;
  receiver: string;
  recordStatus: NotifyState;
  title: string;
  failReason: string;
  content: string;
};

export type AlarmNotificationDetail = {
  id: number;
  bizId: string;
  userId: number;
  userName: string;
  /**邮件通知 */
  email?: NotifyState;
  /**短信通知 */
  sms?: NotifyState;
  /**电话通知 */
  phone?: NotifyState;
  /**站内信通知 */
  internalMsg?: NotifyState;
  /**webhook */
  webhook?: NotifyState;
};

export type SvcRespData = {
  data: AlarmNotificationDetail[];
  total: number;
};

export type RequestRespData = {
  data: BackendAlarmNotificationDetail[] | null;
  total: number;
} | null;

export type ApiQ = {
  bizId?: string;
  notifyCode?: string;
};

export type ApiR = ListResponse<Notification[] | null>;
