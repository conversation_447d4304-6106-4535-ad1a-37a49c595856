/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-5
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-alarm-notification';
import type { SvcQuery, SvcRespData } from './fetch-alarm-notification.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function fetchAlarmNotification(
  svcQuery?: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
