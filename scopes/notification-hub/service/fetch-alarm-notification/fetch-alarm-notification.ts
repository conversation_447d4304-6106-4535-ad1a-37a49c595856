/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-5
 *
 * @packageDocumentation
 */
import uniqBy from 'lodash.uniqby';

import type { BackendChannelType } from '@manyun/monitoring.model.notification-configure-item-object';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  AlarmNotificationDetail,
  ApiQ,
  BackendAlarmNotificationDetail,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './fetch-alarm-notification.type';

const endpoint = '/notify/record/list';

/**
 * 查询告警通报记录
 * @see [Doc](http://172.16.0.17:13000/project/92/interface/api/18924)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery?: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = { ...svcQuery };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    const _mergeData: AlarmNotificationDetail[] = [];
    const _uniqData = uniqBy(data.data, 'userId');
    _uniqData.forEach(d => {
      const _filterData = (data.data ?? []).filter(dt => dt.userId === d.userId);
      _mergeData.push({
        id: d.id,
        bizId: d.bizId,
        userId: d.userId,
        userName: d.userName,
        email: getChannelStatus({ channel: 'EMAIL', data: _filterData }),
        sms: getChannelStatus({ channel: 'SMS', data: _filterData }),
        phone: getChannelStatus({ channel: 'PHONE', data: _filterData }),
        internalMsg: getChannelStatus({ channel: 'INTERNAL_MESSAGE', data: _filterData }),
        webhook: getChannelStatus({ channel: 'WEB_HOOK', data: _filterData }),
      });
    });

    return { error, data: { data: _mergeData, total: _mergeData.length }, ...rest };
  };
}

const getChannelStatus = ({
  channel,
  data,
}: {
  channel: BackendChannelType;
  data: BackendAlarmNotificationDetail[];
}) => {
  const _filterData = data.find(d => d.notifyChannel === channel);
  return _filterData?.recordStatus;
};
