/**
 * <AUTHOR> Name <<EMAIL>>
 * @since 2021-12-7
 *
 * @packageDocumentation
 */
import { BackendOnSiteMessageType } from '@manyun/notification-hub.model.on-site-messages';
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { variantAllEndpoint, variantSelectedEndpoint } from './mark-on-site-messages-as-read';
import type {
  RequestRespData,
  SvcQuery,
  SvcRespData,
  VariantAllApiQ,
  VariantSelectedApiQ,
} from './mark-on-site-messages-as-read.type';

/**
 * 通知中心-站内消息-标记已读
 *
 * @see [Doc](在这里补充 API 文档链接)
 *
 * @param variant
 * @returns
 */
export async function markOnSiteMessagesAsRead(
  query: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  if (query.variant === 'selected') {
    const apiQ: VariantSelectedApiQ = {
      idList: query.ids!,
      // parentType: BackendOnSiteMessageType[query.type],
      // startTime: query.timeRange?.[0],
      // endTime: query.timeRange?.[1],
    };

    const { error, data, ...rest } = await webRequest.tryPost<RequestRespData, VariantSelectedApiQ>(
      variantSelectedEndpoint,
      apiQ
    );
    if (error || data === null) {
      return {
        ...rest,
        error,
        data: 0,
      };
    }
    return { error, data, ...rest };
  } else if (query.variant === 'all') {
    const apiQ: VariantAllApiQ = {
      parentType: BackendOnSiteMessageType[query.type!],
      secondType: query.subType,
      startTime: query.timeRange?.[0],
      endTime: query.timeRange?.[1],
    };

    const { error, data, ...rest } = await webRequest.tryPost<RequestRespData, VariantAllApiQ>(
      variantAllEndpoint,
      apiQ
    );
    if (error || data === null) {
      return {
        ...rest,
        error,
        data: 0,
      };
    }
    return { error, data, ...rest };
  } else {
    throw new Error(`Variant(${query.variant}) not supported!`);
  }
}
