/**
 * <AUTHOR> Name <<EMAIL>>
 * @since 2021-12-7
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import {
  BackendOnSiteMessageType,
  OnSiteMessageType,
} from '@manyun/notification-hub.model.on-site-messages';

export type Variant = 'selected' | 'all';

export type VariantSelectedApiQ = {
  idList: number[];
  // parentType: BackendOnSiteMessageType;
  // startTime?: number;
  // endTime?: number;
};

export type VariantAllApiQ = {
  parentType: BackendOnSiteMessageType;
  secondType?: string;
  startTime?: number;
  endTime?: number;
};

export type SvcQuery = {
  variant: Variant;
  type?: OnSiteMessageType;
  subType?: string;
  ids?: number[];
  timeRange?: [number, number];
};

export type SvcRespData = number;

export type RequestRespData = number | null;

export type ApiR = Response<RequestRespData>;
