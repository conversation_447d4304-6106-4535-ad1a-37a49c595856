/**
 * <AUTHOR> Name <<EMAIL>>
 * @since 2021-12-7
 *
 * @packageDocumentation
 */
import { OnSiteMessageType } from '@manyun/notification-hub.model.on-site-messages';
import { useRemoteMock } from '@manyun/service.request';

import { markOnSiteMessagesAsRead as webService } from './mark-on-site-messages-as-read.browser';

let mockOff: () => void;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

test('should resolve success response', async () => {
  const { error, data } = await webService({ type: OnSiteMessageType.All, variant: 'all' });

  expect(error).toBe(undefined);
  expect(data).toBe(0);
});

test('should resolve error response', async () => {
  const { error, data } = await webService({ type: OnSiteMessageType.Notice, variant: 'all' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toBe(0);
});
