/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-active-notices';
import type { SvcQuery, SvcRespData } from './fetch-active-notices.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function fetchActiveNotices(variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
