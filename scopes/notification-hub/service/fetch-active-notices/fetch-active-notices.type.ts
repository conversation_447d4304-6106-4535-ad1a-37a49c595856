/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { BackendNotice, Importance, Notice } from '@manyun/notification-hub.model.notice';

export type SvcQuery = {
  currentTime?: number;
  idcTag?: string;
  importance?: Importance;
};

export type SvcRespData = {
  data: Notice[];
  total: number;
};

export type RequestRespData = {
  data: BackendNotice[];
  total: number;
} | null;

export type ApiQ = {
  currentTime?: number;
  idcTag?: string;
  importance?: number;
};

export type ApiR = ListResponse<BackendNotice[]>;
