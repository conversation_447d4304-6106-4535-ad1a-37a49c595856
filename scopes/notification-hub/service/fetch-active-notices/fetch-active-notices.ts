/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import { Importance, Notice } from '@manyun/notification-hub.model.notice';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-active-notices.type';

const endpoint = '/pm/notice/list';

/**
 * @see [查询已生效公告列表](http://172.16.0.17:13000/project/78/interface/api/1536)
 *
 * @param request
 * @returns
 */

function handleImportance(importance?: Importance) {
  switch (importance) {
    case 'LOW':
      return 0;
    case 'MID':
      return 1;
    case 'HIGH':
      return 2;
    default:
      return;
  }
}

export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async ({
    currentTime,
    idcTag,
    importance,
  }: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = { currentTime, idcTag, importance: handleImportance(importance) };

    const { error, data, ...rest } = await request.tryGet<RequestRespData, ApiQ>(endpoint, {
      params,
    });

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      error,
      data: { data: data.data.map(d => Notice.fromApiObject(d)), total: data.total },
      ...rest,
    };
  };
}
