/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchActiveNotices as webService } from './fetch-active-notices.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { data } = await webService({ currentTime: 1 });

  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});
