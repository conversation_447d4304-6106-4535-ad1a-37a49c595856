/**
 * <AUTHOR> Name <<EMAIL>>
 * @since 2021-12-7
 *
 * @packageDocumentation
 */
import { OnSiteMessageType } from '@manyun/notification-hub.model.on-site-messages';
// 使用以下代码注册远程 Mocks
import { useRemoteMock } from '@manyun/service.request';

import { fetchOnSiteMessages as webService } from './fetch-on-site-messages.browser';

let mockOff: () => void;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

test('should resolve success response', async () => {
  const { data } = await webService({
    type: OnSiteMessageType.All,
    page: 1,
    pageSize: 1000,
  });

  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

// test('should resolve success ccs response', async () => {
//   const { error, data } = await webService({
//     type: OnSiteMessageType.Approval,
//     subType: 'CC_APPROVAL',
//     page: 1,
//     pageSize: 10,
//   });

//   console.log(data.data);
//   expect(error).toBe(undefined);
//   expect(data.data).toHaveProperty('length');
//   expect(typeof data.total).toBe('number');
// });
