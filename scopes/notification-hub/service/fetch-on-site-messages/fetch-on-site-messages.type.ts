/**
 * <AUTHOR> Name <<EMAIL>>
 * @since 2021-12-7
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import {
  BackendOnSiteMessageState,
  BackendOnSiteMessageType,
  OnSiteMessageState,
  OnSiteMessageType,
} from '@manyun/notification-hub.model.on-site-messages';
import type { BackendOnSiteMessage } from '@manyun/notification-hub.model.on-site-messages';

export type SvcQuery = {
  page: number;
  pageSize: number;
  state?: OnSiteMessageState;
  type: OnSiteMessageType;
  timeRange?: [number, number];
  subType?: string;
};

export type SvcRespData = {
  data: BackendOnSiteMessage[];
  total: number;
};

export type RequestRespData = {
  data: BackendOnSiteMessage[] | null;
  total: number;
};

export type ApiQ = {
  pageNum: number;
  pageSize: number;
  isRead?: BackendOnSiteMessageState;
  insideMsgPType: BackendOnSiteMessageType;
  startTime?: number;
  endTime?: number;
  insideMsgCType?: string;
};

export type ApiR = ListResponse<RequestRespData['data']>;
