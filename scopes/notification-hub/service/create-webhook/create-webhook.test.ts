/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-1
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { createWebhook as webService } from './create-webhook.browser';
import { createWebhook as nodeService } from './create-webhook.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({
    name: 'SUCCESS_ID',
    address: '1',
    secret: '1',
    usable: true,
  });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    name: 'FAIL_ID',
    address: '1',
    secret: '1',
    usable: true,
  });

  expect(typeof error).toBe('object');
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService({
    name: 'SUCCESS_ID',
    address: '1',
    secret: '1',
    usable: true,
  });

  expect(error).toBe(undefined);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({
    name: 'FAIL_ID',
    address: '1',
    secret: '1',
    usable: true,
  });

  expect(typeof error).toBe('object');
});
