/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-1
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './create-webhook';
import type { ApiQ, SvcRespData } from './create-webhook.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function createWebhook(svcQuery: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
