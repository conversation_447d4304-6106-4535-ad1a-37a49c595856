/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-1
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-webhook-blocks';
import type { ApiQ, SvcRespData } from './fetch-webhook-blocks.type';

const executor = getExecutor(webRequest);

/**
 * @param  svcQuery
 * @returns
 */
export function fetchWebhookBlocks(svcQuery: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
