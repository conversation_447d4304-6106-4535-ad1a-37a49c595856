/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-1
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchWebhookScenes as webService } from './fetch-webhook-scenes.browser';
import { fetchWebhookScenes as nodeService } from './fetch-webhook-scenes.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    channel: 'SUCCESS_ID',
  });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    channel: 'FAIL_ID',
  });

  expect(error).toBe(undefined);
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({
    channel: 'SUCCESS_ID',
  });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({
    channel: 'FAIL_ID',
  });

  expect(error).toBe(undefined);
});
