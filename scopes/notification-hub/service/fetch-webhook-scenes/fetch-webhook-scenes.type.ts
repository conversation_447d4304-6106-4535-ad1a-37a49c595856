/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-1
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type WebhookScene = {
  eventType: string;
  name: string;
  notifyCode: string;
};

export type SvcRespData = {
  data: WebhookScene[];
  total: number;
};

export type RequestRespData = {
  data: WebhookScene[] | null;
  total: number;
} | null;
// SvcQuery 与 ApiQ相同
export type ApiQ = {
  channel: string;
};

export type ApiR = ListResponse<WebhookScene[] | null>;
