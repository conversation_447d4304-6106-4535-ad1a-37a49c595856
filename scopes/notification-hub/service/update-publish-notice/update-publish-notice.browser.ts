/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-8
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './update-publish-notice';
import type { ApiQ, SvcRespData } from './update-publish-notice.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function updatePublishNotice(variant: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
