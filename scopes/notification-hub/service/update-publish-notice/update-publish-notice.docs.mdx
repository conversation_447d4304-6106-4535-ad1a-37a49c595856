---
description: 'A updatePublishNotice HTTP API service.'
labels: ['service', 'http', update-publish-notice]
---

## 概述

TODO

## 使用

### Browser

```ts
import { updatePublishNotice } from '@notification-hub/service.update-publish-notice';

const { error, data } = await updatePublishNotice('success');
const { error, data } = await updatePublishNotice('error');
```

### Node

```ts
import { updatePublishNotice } from '@notification-hub/service.update-publish-notice/dist/index.node';

const { data } = await updatePublishNotice('success');

try {
  const { data } = await updatePublishNotice('error');
} catch(error) {
  // ...
}
```
