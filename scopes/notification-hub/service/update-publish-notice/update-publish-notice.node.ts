/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-8
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './update-publish-notice';
import type { SvcQuery, SvcRespData } from './update-publish-notice.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function updatePublishNotice(
  variant?: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
