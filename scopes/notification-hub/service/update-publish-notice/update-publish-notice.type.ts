/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-8
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcRespData = string | null;

export type RequestRespData = string | null;

export type ApiQ = {
  bizScenario?: any;
  id: number;
  content: string;
  importance: string;
  rangeType: string;
  resourceIdList?: number[];
  title: string;
  tenantId?: string;
  validBeginDate: number;
  validEndDate: number;
  files?: any[];
  immediately: boolean;
  popUp: boolean;
};

export type ApiR = Response<RequestRespData>;
