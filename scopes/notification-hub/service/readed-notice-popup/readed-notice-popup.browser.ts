/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-1
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './readed-notice-popup';
import type { ApiQ, SvcRespData } from './readed-notice-popup.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function readedNoticePopup(svcQuery: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
