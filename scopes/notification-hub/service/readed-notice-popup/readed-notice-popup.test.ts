/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-1
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { readedNoticePopup as webService } from './readed-notice-popup.browser';
import { readedNoticePopup as nodeService } from './readed-notice-popup.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ id: 1 });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({ id: 0 });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({ id: 1 });

  expect(error).toBe(undefined);
});

test('[node] should resolve error response', async () => {
  const { error, data } = await nodeService({ id: 0 });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
