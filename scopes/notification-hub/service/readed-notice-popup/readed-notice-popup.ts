/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-1
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, SvcRespData } from './readed-notice-popup.type';

const endpoint = '/pm/notice/popUpView';

/**
 * 公告弹窗已读
 * @see [Doc](YAPI http://172.16.0.17:13000/project/78/interface/api/19441)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<string, ApiQ>(endpoint, svcQuery);

    return { error, data, ...rest };
  };
}
