---
description: 'A readedNoticePopup HTTP API service.'
labels: ['service', 'http']
---

## 概述

当公告通知弹窗已读后，该弹窗信息不再重复提醒

## 使用

### Browser

```ts
import { readedNoticePopup } from '@manyun/notification-hub.service.readed-notice-popup';

const { error, data } = await readedNoticePopup({ id: 1 });
const { error, data } = await readedNoticePopup({ id: 0 });
```

### Node

```ts
import { readedNoticePopup } from '@manyun/notification-hub.service.readed-notice-popup/dist/index.node';

const { data } = await readedNoticePopup({ id: 1 });
const { error, data } = await readedNoticePopup({ id: 0 });
```
