---
description: 'A fetchOnSiteMessagesOowDevices HTTP API service.'
labels: ['service', 'http', 'label3']
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchOnSiteMessagesOowDevices } from '@tnotification-hub/service.fetch-on-site-messages-oow-devices';

const { error, data } = await fetchOnSiteMessagesOowDevices('success');
const { error, data } = await fetchOnSiteMessagesOowDevices('error');
```
