/**
 * <AUTHOR> Name <<EMAIL>>
 * @since 2021-12-9
 *
 * @packageDocumentation
 */

export type Device = {
  guid: string;
  serialNumber: string;
  assetNo: string | null;
  name: string;
  tag: string;
  parentGuid: string | null;
  parentTag: string | null;
  spaceGuid: {
    idcTag: string | null;
    blockTag: string | null;
    roomTag: string | null;
    blockGuid: string | null;
    idcGuid: string | null;
    roomGuid: string | null;
  };
  deviceType: string;
  topCategory: string;
  secondCategory: string;
  vendor: string;
  productModel: string;
  operator: string;
  purchaseTime: string;
  warrantyTime: string;
  warrantyVendor: string;
  warrantyStatus: {
    code: string;
    name: string;
  };
  operationStatus: {
    code: 'ON' | 'OFF';
    name: string;
  };
  assetStatus: {
    code: string;
    name: string;
  };
  checkTime: string;
  enableTime: string;
  scrapTime: string;
  purchasePrice: number;
  netWorth: number;
  warrantyPrice: number;
  powerLine: string;
  extendPosition: string | null;
  channelId: number;
};

export type SvcQuery = {
  /** 过保时间戳集合 */
  outOfWrrantyTimestamps: number;
  /** 消息发送时选取的设备的批次（后端叫分片）*/
  batchId: string;
  blockGuid: string;

  page: number;
  pageSize: number;
};

export type SvcRespData = {
  data: Device[];
  total: number;
};

export type RequestRespData = {
  data: Device[] | null;
  total: number;
};

export type ApiQ = {
  warrantyTime: number;
  slaTimeMaintenanceEnum: string;
  blockGuid: string;
  pageNum: number;
  pageSize: number;
};
