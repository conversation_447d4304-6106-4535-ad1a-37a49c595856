/**
 * <AUTHOR> Name <<EMAIL>>
 * @since 2021-12-9
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { endpoint } from './fetch-on-site-messages-oow-devices';
import type {
  ApiQ,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './fetch-on-site-messages-oow-devices.type';

/**
 * 查询某条站内消息中的过保设备
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/bnpnl9/ng0oc9#Il5IE)
 *
 * @param variant
 * @returns
 */
export async function fetchOnSiteMessagesOowDevices(
  query: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  const params: ApiQ = {
    warrantyTime: query.outOfWrrantyTimestamps,
    slaTimeMaintenanceEnum: query.batchId,
    blockGuid: query.blockGuid,
    pageNum: query.page,
    pageSize: query.pageSize,
  };

  const { error, data, ...rest } = await webRequest.tryPost<RequestRespData, ApiQ>(
    endpoint,
    params
  );

  if (error || data === null || data.data === null) {
    return {
      ...rest,
      error,
      data: {
        data: [],
        total: 0,
      },
    };
  }

  return {
    error,
    data: {
      data: data.data,
      total: data.total,
    },
    ...rest,
  };
}
