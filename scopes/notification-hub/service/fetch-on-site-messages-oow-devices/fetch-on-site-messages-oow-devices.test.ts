/**
 * <AUTHOR> Name <<EMAIL>>
 * @since 2021-12-9
 *
 * @packageDocumentation
 */
// 使用以下代码注册远程 Mocks
import { useRemoteMock } from '@manyun/service.request';

import { fetchOnSiteMessagesOowDevices as webService } from './fetch-on-site-messages-oow-devices.browser';

let mockOff: () => void;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

test('should resolve success response', async () => {
  const params = {
    outOfWrrantyTimestamps: 1638288000000,
    batchId: '1',
    responsibleUserId: 1,
    page: 1,
    pageSize: 10,
  };
  const { error, data } = await webService(params);

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('should resolve error response', async () => {
  const params = {
    outOfWrrantyTimestamps: 1638288000000,
    batchId: '1',
    responsibleUserId: 1,
    page: 2,
    pageSize: 10,
  };

  const { error, data } = await webService(params);

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});
