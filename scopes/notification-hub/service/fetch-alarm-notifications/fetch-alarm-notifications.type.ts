/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-5
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  alarmId?: number;
  content?: string /**告警内容 */;
  idc?: string /**机房 */;
  block?: string /**楼栋 */;
  timeRange?: [number, number] /**开始时间、结束时间 */;
  page: number;
  pageSize: number;
};
export type BackendAlarmNotificationRecord = {
  id: number;
  alarmId: number;
  alarmLevel: string;
  content: string;
  idcTag: string;
  blockTag: string;
  notifyTime: number;
  itemId: string;
  itemName: string /**通报项名称 */;
  informNum: number /**通知人数 */;
  gmtCreate: string;
};

export type AlarmNotificationRecord = {
  id: number;
  alarmId: number;
  alarmLevel: string;
  content: string;
  idcTag: string;
  blockTag: string;
  notifyTime: number;
  itemId: string;
  itemName: string;
  informNum: number;
  gmtCreate: string;
};

export type SvcRespData = {
  data: AlarmNotificationRecord[];
  total: number;
};

export type RequestRespData = {
  data: BackendAlarmNotificationRecord[] | null;
  total: number;
} | null;

export type ApiQ = {
  alarmId?: number;
  content?: string;
  idcTag?: string;
  blockTag?: string;
  startTime?: number;
  endTime?: number;
  pageNum: number;
  pageSize: number;
};

export type ApiR = ListResponse<AlarmNotificationRecord[] | null>;
