---
description: 'A fetchAlarmNotifications HTTP API service.'
labels: ['service', 'http', fetch-alarm-notifications]
---

## 概述

查询告警通报记录

## 使用

### Browser

```ts
import { fetchAlarmNotifications } from '@manyun/notification-hub.service.fetch-alarm-notifications';

const { error, data } = await fetchAlarmNotifications({
  page: 1,
  pageSize: 10,
});
const { error, data } = await fetchAlarmNotifications({
  page: 0,
  pageSize: 10,
});
```

### Node

```ts
import { fetchAlarmNotifications } from '@manyun/notification-hub.service.fetch-alarm-notifications/dist/index.node';

const { data } = await fetchAlarmNotifications({
  page: 1,
  pageSize: 10,
});
const { error, data } = await fetchAlarmNotifications({
  page: 0,
  pageSize: 10,
});
```
