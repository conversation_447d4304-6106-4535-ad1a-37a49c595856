/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-5
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './fetch-alarm-notifications.type';

const endpoint = '/dcim/alarm/inform/record';

/**
 * 查询告警通报记录
 * @see [Doc](http://172.16.0.17:13000/project/158/interface/api/18690)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      pageNum: svcQuery.page,
      pageSize: svcQuery.pageSize,
      alarmId: svcQuery.alarmId,
      content: svcQuery.content,
      idcTag: svcQuery.idc,
      blockTag: svcQuery.block,
      startTime: svcQuery.timeRange && svcQuery.timeRange[0],
      endTime: svcQuery.timeRange && svcQuery.timeRange[1],
    };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return { error, data: { data: data.data, total: data.total }, ...rest };
  };
}
