/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-1
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './update-webhook';
import type { ApiQ, SvcRespData } from './update-webhook.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function updateWebhook(svcQuery: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
