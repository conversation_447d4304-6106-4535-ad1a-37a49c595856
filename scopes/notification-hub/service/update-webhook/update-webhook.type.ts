/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-1
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcRespData = boolean | null;
export type RequestRespData = boolean | null;

// SvcQuery 与 ApiQ 相同
export type ApiQ = {
  webHookId: number;
  name: string;
  address: string;
  secret: string;
  usable: boolean;
};

export type ApiR = WriteResponse;
