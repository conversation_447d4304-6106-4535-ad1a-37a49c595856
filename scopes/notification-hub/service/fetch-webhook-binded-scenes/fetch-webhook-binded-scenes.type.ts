/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-1
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type Scene = {
  eventType: string;
  notifyCode: string;
  name: string;
  relateWebHook: boolean;
};

export type SvcRespData = {
  data: Scene[];
  total: number;
};

export type RequestRespData = {
  data: Scene[] | null;
  total: number;
} | null;

// SvcQuery 与 ApiQ 相同
export type ApiQ = {
  webHookId: number;
};

export type ApiR = ListResponse<Scene[] | null>;
