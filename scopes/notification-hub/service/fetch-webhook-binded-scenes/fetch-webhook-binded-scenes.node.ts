/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-1
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-webhook-binded-scenes';
import type { ApiQ, SvcRespData } from './fetch-webhook-binded-scenes.type';

const executor = getExecutor(nodeRequest);

/**
 * @param  svcQuery
 * @returns
 */
export function fetchWebhookBindedScenes(
  svcQuery: ApiQ
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
