/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-1
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { Importance } from '@manyun/notification-hub.model.notice';

export type BackEndPopUp = {
  id: number;
  title: string;
  content: string /*公告内容 */;
  rangeType: string /*公告发布范围 */;
  validBeginDate: number;
  validEndDate: number;
  importance: Importance /*公告重要程度 */;
  modifierId: number;
  modifierName: string;
};

export type SvcRespData = {
  data: BackEndPopUp[];
  total: number;
};

export type RequestRespData = {
  data: BackEndPopUp[] | null;
  total: number;
} | null;

export type ApiQ = {
  currentTime: number;
};

export type ApiR = ListResponse<BackEndPopUp[] | null>;
