---
description: 'A fetchPopupList HTTP API service.'
labels: ['service', 'http']
---

## 概述

查询公告弹窗列表信息

## 使用

### Browser

```ts
import { fetchPopupList } from '@manyun/notification-hub.service.fetch-popup-list';

const { error, data } = await fetchPopupList({ currentTime: 1111111111111 });
const { error, data } = await fetchPopupList({ currentTime: 0 });
```

### Node

```ts
import { fetchPopupList } from '@manyun/notification-hub.service.fetch-popup-list/dist/index.node';

const { data } = await fetchPopupList({ currentTime: 1111111111111 });
const { error, data } = await fetchPopupList({ currentTime: 0 });
```
