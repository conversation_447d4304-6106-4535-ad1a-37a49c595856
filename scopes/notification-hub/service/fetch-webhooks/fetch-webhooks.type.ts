/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-1
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  pageSize: number;
  pageNum: number;
  location?: string[];
  scenes?: string;
  status?: boolean;
  name?: string;
};

export type Webhook = {
  id: number;
  webHookName: string;
  address: string;
  secret: string;
  usable: boolean;
  gmtCreate: number;
  gmtModified: number;
  operator: number;
  operatorName: string;
  creator: number;
  creatorName: string;
};

export type SvcRespData = {
  data: Webhook[];
  total: number;
};

export type RequestRespData = {
  data: Webhook[] | null;
  total: number;
} | null;

export type ApiQ = {
  pageSize: number;
  pageNum: number;
  blockGuidList?: string[];
  notifyCode?: string;
  usable?: boolean;
  webHookName?: string;
};

export type ApiR = ListResponse<Webhook[] | null>;
