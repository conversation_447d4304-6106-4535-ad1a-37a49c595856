---
description: 'A fetchWebhooks HTTP API service.'
labels: ['service', 'http']
---

## 概述

查询 webhook 列表的接口

## 使用

### Browser

```ts
import { fetchWebhooks } from '@manyun/notification-hub.service.fetch-webhooks';

const { error, data } = await fetchWebhooks('success');
const { error, data } = await fetchWebhooks('error');
```

### Node

```ts
import { fetchWebhooks } from '@manyun/notification-hub.service.fetch-webhooks/dist/index.node';

const { data } = await fetchWebhooks('success');
const { error, data } = await fetchWebhooks('error');
```
