import React from 'react';

import { Statistic } from '@manyun/base-ui.ui.statistic';
import type { StatisticProps } from '@manyun/base-ui.ui.statistic';

import Bar from './assets/Bar.svg';

export type DashboardStatisticProps = {
  icon?: React.ReactNode;
  mainColor: string;
  showBar?: boolean;
  subValue?: React.ReactNode;
} & StatisticProps;

export function DashboardStatistic({
  icon,
  mainColor,
  showBar = false,
  subValue,
  title,
  ...statisticRest
}: DashboardStatisticProps) {
  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        width: '100%',
      }}
    >
      {icon}
      <Statistic
        style={{ paddingLeft: 8, width: '100%' }}
        title={
          <div style={{ display: 'flex', flexDirection: 'column', width: 91 }}>
            <div style={{ color: 'var(--text-color-secondary)' }}>{title}</div>
            {showBar && <img width="100%" src={Bar} alt="" />}
          </div>
        }
        valueStyle={{ fontSize: 18, lineHeight: '28px', color: mainColor }}
        formatter={value => (
          <div style={{ width: '100%' }}>
            <span>
              {value > 999 ? value.toString().slice(0, 1) + ',' + value.toString().slice(1) : value}
            </span>
            {subValue && (
              <span
                style={{
                  paddingLeft: 8,
                  fontSize: 12,
                  lineHeight: '20px',
                  color: 'var(--text-color-secondary)',
                }}
              >
                {subValue}
              </span>
            )}
          </div>
        )}
        {...statisticRest}
      />
    </div>
  );
}
