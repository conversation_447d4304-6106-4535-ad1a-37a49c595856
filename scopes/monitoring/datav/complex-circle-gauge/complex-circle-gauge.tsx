import React, { useEffect, useMemo } from 'react';
import { useSelector } from 'react-redux';

import * as echarts from 'echarts/core';
import get from 'lodash.get';

import { Echarts } from '@manyun/base-ui.chart.echarts';
import { ThemeContext } from '@manyun/base-ui.chart.theme';
import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Statistic } from '@manyun/base-ui.ui.statistic';
import { Typography } from '@manyun/base-ui.ui.typography';

import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { PointsLineModalButton } from '@manyun/monitoring.chart.points-line';
import { usePointsData } from '@manyun/monitoring.hook.use-points-data';
import type { SubscribeMethod } from '@manyun/monitoring.hook.use-points-data';
import { usePointsYesterdayAvg } from '@manyun/monitoring.hook.use-points-yesterday-avg';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

import backgroundImage from './assets/gauge-bg.png';
import { ChartThemeLight } from './themes/chart-light';

/**测点订阅 */
export type SubscribeTarget = {
  moduleId: string;
  spaceGuid: string;
  predefinedCode: string;
  subscribeMethod?: SubscribeMethod;
};

export type ComplexCircleGaugeProps = {
  subscribeTarget: SubscribeTarget;
  title: string;
  /**是否展示变化率 */
  showRate?: boolean;
  /**仪表盘最大的数据值*/
  max?: number;
  /**图表尺寸 */
  chartSize?: number;
  unit?: string;
  precision?: number;
  modalTitle?: string;
  btnStyle?: React.CSSProperties;
};

const defaultChartSize = 200;

export function ComplexCircleGauge({
  subscribeTarget: { spaceGuid, moduleId, predefinedCode, subscribeMethod = 'fetch' },
  title,
  showRate,
  max = 100,
  modalTitle,
  chartSize = defaultChartSize,
  btnStyle,
  unit,
  precision = 2,
}: ComplexCircleGaugeProps) {
  const { idc, block } = getSpaceGuidMap(spaceGuid);
  const config = useSelector(selectCurrentConfig);
  const configUtil = useMemo(() => {
    return new ConfigUtil(config);
  }, [config]);
  const deviceType = configUtil.getOneDeviceType(
    block
      ? ConfigUtil.constants.deviceTypes.SPACE_BLOCK
      : ConfigUtil.constants.deviceTypes.SPACE_IDC
  );
  const pointCode = useMemo(
    () => (deviceType ? configUtil.getPointCode(deviceType, predefinedCode) : undefined),
    [deviceType, configUtil, predefinedCode]
  );
  const [pointsData] = usePointsData({
    blockGuid: spaceGuid,
    moduleId: moduleId,
    targets: pointCode
      ? [
          {
            guid: spaceGuid,
            type: deviceType,
            hardCodedPoints: [{ pointCode, dataType: 'AI' }],
          },
        ]
      : [],
    subscribeMethod,
  });

  const point = pointsData[spaceGuid]?.get(pointCode);

  const pointValue = (pointsData[spaceGuid]?.get(pointCode)?.value ?? '--') as string | number;
  const isInvalid = useMemo(() => {
    return typeof pointValue !== 'number';
  }, [pointValue]);
  const { json } = React.useContext(ThemeContext);
  const borderColor = get(ChartThemeLight, ['pie', 'border']);
  const containerSize = useMemo(() => chartSize + 24 * 2, [chartSize]);
  const scaleSize = useMemo(() => chartSize / defaultChartSize, [chartSize]);
  const valueTextColor = get(json, ['common', 'valueTextColor']);

  const [yesterdayAvgDatas, { fetchYesterdayAvg }] = usePointsYesterdayAvg({
    idc: idc!,
    pointGuids: pointCode
      ? [
          {
            deviceGuid: spaceGuid,
            pointCode,
          },
        ]
      : [],
  });

  useEffect(() => {
    fetchYesterdayAvg();
  }, [fetchYesterdayAvg]);

  const yesterdayAvg = useDeepCompareMemo(
    () => get(yesterdayAvgDatas, [0, 'value']),
    [yesterdayAvgDatas]
  );

  return (
    <div
      style={{
        padding: 24,
        height: containerSize,
        width: containerSize,
        backgroundImage: `url(${backgroundImage})`,
        backgroundRepeat: 'no-repeat',
        backgroundSize: `${containerSize}px ${containerSize}px`,
      }}
    >
      <div
        style={{
          position: 'absolute',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Echarts
          style={{ width: chartSize, height: chartSize }}
          variant="dashboard"
          opts={{ renderer: 'svg' }}
          option={{
            series: [
              {
                type: 'gauge',
                hoverAnimation: false,
                min: 0,
                max: max,
                startAngle: 0,
                endAngle: 360,
                radius: 100 * scaleSize,
                z: 2,
                progress: {
                  show: true,
                  width: 38 * scaleSize,
                  itemStyle: {
                    color: new echarts.graphic.LinearGradient(
                      1,
                      1,
                      0,
                      0,
                      ChartThemeLight.gauge.progress
                    ),
                  },
                },
                axisLine: {
                  lineStyle: {
                    opacity: 0,
                  },
                },
                axisTick: {
                  show: false,
                },
                splitLine: {
                  show: false,
                  lineStyle: {
                    opacity: 0,
                  },
                },
                axisLabel: {
                  show: false,
                },
                pointer: {
                  show: false,
                },
                title: {
                  fontSize: 14,
                  lineHeight: 22,
                  color: get(json, ['categoryAxis', 'axisLabel', 'color']),
                  offsetCenter: [0, '-25%'],
                },
                detail: {
                  show: false,
                },
                data: [
                  {
                    name: title,
                  },
                ],
              },
              !isInvalid && {
                type: 'pie',
                hoverAnimation: false,
                radius: [78 * scaleSize, 88 * scaleSize],
                startAngle: 0,
                emphasis: {
                  disabled: true,
                },
                labelLine: {
                  show: false,
                },
                markPoint: {},
                z: 1,
                data: [
                  {
                    value: pointValue,
                    itemStyle: {
                      color: new echarts.graphic.LinearGradient(
                        1,
                        1,
                        0,
                        0,
                        ChartThemeLight.pie.item
                      ),
                    },
                  },
                  {
                    name: '',
                    value: 0,
                    label: {
                      position: 'inside',
                      width: 10,
                      height: 10,
                      backgroundColor: get(json, ['color', 0]),
                      borderRadius: 10,
                      borderWidth: 5,
                      borderColor: borderColor,
                      shadowColor: ChartThemeLight.pie.label.shadow,
                      shadowBlur: 4,
                      shadowOffsetY: 4,
                    },
                  },
                  {
                    value: max - Number(pointValue),
                    itemStyle: { color: borderColor, opacity: 0 },
                  },
                ],
              },
            ],
          }}
        />
        <PointsLineModalButton
          btnStyle={{
            ...btnStyle,
          }}
          btnText={
            typeof point?.value === 'number' ? (
              <BtnText
                pointValue={point.value}
                pointUnit={unit}
                precision={precision}
                valueTextColor={valueTextColor}
                style={{
                  fontSize: btnStyle?.fontSize,
                }}
              />
            ) : (
              <Typography.Text>--</Typography.Text>
            )
          }
          modalText={modalTitle}
          variant="dashboard"
          idcTag={idc!}
          pointGuids={
            point
              ? [
                  {
                    deviceGuid: spaceGuid,
                    pointCode: point.pointCode ?? '',
                    unit: point.unit,
                  },
                ]
              : []
          }
          echartStyle={{ width: '100%', height: 400 }}
          seriesOption={[{ name: title }]}
          linkTextColorType="default"
        />
        {showRate && (
          <div
            style={{
              position: 'absolute',
              bottom: '30%',
              left: '50%',
              transform: 'translateX(-50%)',
            }}
          >
            {yesterdayAvg && typeof pointValue === 'number' ? (
              <Statistic.Trend
                previousValue={yesterdayAvg}
                currentValue={pointValue}
                precision={2}
              />
            ) : (
              <Typography.Text>--</Typography.Text>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

function BtnText({
  pointValue,
  pointUnit,
  precision,
  valueTextColor,
  style,
}: {
  pointValue: number;
  precision: number;
  pointUnit: string | undefined;
  valueTextColor: string;
  style: React.CSSProperties;
}) {
  return (
    <Typography.Text
      style={{
        color: valueTextColor,
        fontSize: '24px',
        lineHeight: '32px',
        ...style,
      }}
    >
      {pointValue.toFixed(precision)}
      {pointUnit ? (
        <Typography.Text
          style={{
            fontSize: 14,
            padding: '6px 0px 0px',
            color: valueTextColor,
          }}
        >
          {pointUnit}
        </Typography.Text>
      ) : (
        ''
      )}
    </Typography.Text>
  );
}
