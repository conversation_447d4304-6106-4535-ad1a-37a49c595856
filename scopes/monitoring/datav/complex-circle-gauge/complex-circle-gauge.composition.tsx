import React from 'react';

import { ConfigUtil } from '@manyun/dc-brain.util.config';

import { ComplexCircleGauge } from './complex-circle-gauge';

export const BasicComplexCircleGauge = () => {
  return (
    <ComplexCircleGauge
      subscribeTarget={{
        spaceGuid: 'EC06.A',
        moduleId: 'aaa',
        predefinedCode: ConfigUtil.constants.pointCodes.HV_INCOMING_SWITCHGEAR_LOAD_RATE_AVG,
      }}
      title="市电负载率"
    />
  );
};
