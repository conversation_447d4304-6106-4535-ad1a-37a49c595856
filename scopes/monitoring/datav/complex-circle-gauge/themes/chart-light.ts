export const ChartThemeLight = {
  pie: {
    border: 'white',
    label: {
      shadow: 'rgba(7, 109, 252, 0.25)',
    },
    item: [
      {
        offset: 0,
        color: 'rgba(1, 110, 255, 0)',
      },
      {
        offset: 0.25,
        color: 'rgba(95, 231, 249, 0.03)',
      },
      {
        offset: 0.5,
        color: 'rgba(33, 177, 255, 0.35)',
      },
      {
        offset: 0.75,
        color: 'rgba(1, 110, 255, 0.85)',
      },
      {
        offset: 1,
        color: 'rgba(1, 110, 255, 1)',
      },
    ],
  },
  gauge: {
    progress: [
      {
        offset: 0,
        color: 'rgba(1, 110, 255, 0)',
      },
      {
        offset: 0.25,
        color: 'rgba(95, 231, 249, 0.1)',
      },
      {
        offset: 0.5,
        color: 'rgba(33, 177, 255, 0.15)',
      },
      {
        offset: 0.75,
        color: 'rgba(1, 110, 255, 0.25)',
      },
      {
        offset: 1,
        color: 'rgba(1, 110, 255, 0.4)',
      },
    ],
  },
};
