import React, { useEffect, useRef } from 'react';
import { animated, useSpring } from 'react-spring';

import get from 'lodash.get';
import useDeepCompareEffect from 'use-deep-compare-effect';

import { Pie } from '@manyun/base-ui.chart.pie';
import type { PieProps } from '@manyun/base-ui.chart.pie';

import circle from './assets/circle.svg';
import backgroundImage from './assets/pie-bg.png';
import { ChartThemeLight } from './themes/chart-light';

export type AnimatedPieProps = {
  seriesData: { value: number; name: string }[];
  chartSize?: number;
} & Partial<Pick<PieProps, 'option'>>;

export function AnimatedPie({ seriesData, chartSize = 188, option }: AnimatedPieProps) {
  const pieLength = useRef(0);
  const pieIndex = useRef(0);
  const pieLastIndex = useRef(0);
  const pieInterval = useRef(0);

  const springProps = useSpring({
    loop: true,
    from: { rotateZ: 0 },
    to: { rotateZ: 360 },
    config: {
      mass: 5,
      tension: 50,
    },
  });

  useDeepCompareEffect(() => {
    pieLength.current = seriesData.length;
  }, [seriesData]);

  useEffect(() => {
    pieIndex.current = 0;
    pieLastIndex.current = 0;

    return () => {
      window.clearInterval(pieInterval.current);
    };
  }, []);

  return (
    <div
      style={{
        width: '100%',
        backgroundImage: `url(${backgroundImage})`,
        backgroundRepeat: 'no-repeat',
        backgroundSize: `${chartSize}px ${chartSize}px`,
      }}
    >
      <animated.div
        style={{
          width: chartSize,
          height: chartSize,
          padding: 10,
          position: 'absolute',
          ...springProps,
        }}
      >
        <img width="100%" height="100%" src={circle} alt="circle" />
      </animated.div>
      <Pie
        style={{ width: '100%', height: chartSize }}
        variant="dashboard"
        option={{
          tooltip: {
            show: false,
          },
          legend: {
            type: 'scroll',
            selectedMode: false,
            left: '50%',
            orient: 'vertical',
            itemWidth: 10,
            itemHeight: 10,
            icon: 'circle',
            ...get(option, ['legend']),
          },
          series: [
            {
              emptyCircleStyle: { color: ChartThemeLight.pie.emptyCircle },
              legendHoverLink: false,
              silent: true,
              type: 'pie',
              center: get(option, ['series', 0, 'center']) ?? [94, '50%'],
              radius: get(option, ['series', 0, 'radius']) ?? ['65%', '77%'],
              avoidLabelOverlap: false,
              emphasis: {
                itemStyle: {
                  shadowBlur: 4,
                  shadowColor: ChartThemeLight.pie.emphasisShadow,
                  shadowOffsetY: 4,
                },

                label: get(option, ['series', 0, 'emphasis', 'label']),
              },
              itemStyle: {
                borderWidth: 1,
                borderColor: ChartThemeLight.pie.border,
              },
              label: {
                show: false,
                position: 'center',
              },
              labelLine: {
                show: false,
              },
              data: seriesData,
            },
          ],
        }}
        onReady={instance => {
          let lastHighlight = 0;
          pieInterval.current = window.setInterval(function () {
            if (!pieLength.current) {
              pieIndex.current = 0;
              pieLastIndex.current = 0;
              return;
            }

            instance.dispatchAction({
              type: 'downplay',
              seriesIndex: 0,
              dataIndex: [pieLastIndex.current, lastHighlight],
            });
            instance.dispatchAction({
              type: 'highlight',
              seriesIndex: 0,
              dataIndex: pieIndex.current,
            });
            lastHighlight = pieIndex.current;
            pieLastIndex.current = pieIndex.current;
            pieIndex.current = (pieIndex.current + 1) % pieLength.current;
          }, 2 * 1000);
        }}
      />
    </div>
  );
}
