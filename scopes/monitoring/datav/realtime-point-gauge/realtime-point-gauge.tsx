import React, { useEffect, useMemo } from 'react';
import { useSelector } from 'react-redux';

import get from 'lodash.get';

import { ThemeContext } from '@manyun/base-ui.chart.theme';
import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Statistic } from '@manyun/base-ui.ui.statistic';
import { Typography } from '@manyun/base-ui.ui.typography';

import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { CommonGauge } from '@manyun/monitoring.chart.common-gauge';
import type { CommonGaugeProps } from '@manyun/monitoring.chart.common-gauge';
import { PointsLineModalButton } from '@manyun/monitoring.chart.points-line';
import type { PointsLineModalButtonProps } from '@manyun/monitoring.chart.points-line';
import type { PointTarget } from '@manyun/monitoring.hook.use-points-data';
import { usePointsData } from '@manyun/monitoring.hook.use-points-data';
import { usePointsYesterdayAvg } from '@manyun/monitoring.hook.use-points-yesterday-avg';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

export type RealtimePointGaugeProps = {
  pointCode?: string;
  deviceGuid?: string;
  deviceType?: string;
  blockGuid: string;
  moduleId: string;
  pointType?: 'device' | 'space';
  precision?: number;
} & Omit<CommonGaugeProps, 'value' | 'prevValue' | 'valueDisplay'> &
  Pick<PointsLineModalButtonProps, 'modalText'>;

export function RealtimePointGauge({
  pointCode,
  deviceGuid,
  deviceType,
  blockGuid,
  moduleId,
  pointType = 'space',
  precision = 2,
  max = 100,
  color,
  modalText,
  ...rest
}: RealtimePointGaugeProps) {
  const { json } = React.useContext(ThemeContext);
  const defaultColor = color ?? get(json, ['color', 0]);
  const { idc } = getSpaceGuidMap(blockGuid);
  const config = useSelector(selectCurrentConfig);
  const configUtil = useMemo(() => {
    return new ConfigUtil(config);
  }, [config]);
  const blockDeviceType = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_BLOCK);
  const guid = useMemo(
    () => (pointType === 'device' ? deviceGuid : blockGuid),
    [deviceGuid, blockGuid, pointType]
  );
  const type = useMemo(
    () => (pointType === 'device' ? deviceType : blockDeviceType),
    [deviceType, blockDeviceType, pointType]
  );
  const targetList: PointTarget[] = useDeepCompareMemo(
    () =>
      guid && type
        ? [
            {
              guid: guid,
              type: type,
              hardCodedPoints: pointCode
                ? [
                    {
                      pointCode,
                      dataType: 'AI',
                    },
                  ]
                : [],
            },
          ]
        : [],
    [guid, type, pointCode]
  );
  const [pointData] = usePointsData({
    blockGuid,
    moduleId: moduleId,
    targets: targetList,
  });

  const [yesterdayAvgDatas, { fetchYesterdayAvg }] = usePointsYesterdayAvg({
    idc: idc!,
    pointGuids:
      pointCode && guid
        ? [
            {
              deviceGuid: guid,
              pointCode,
            },
          ]
        : [],
  });

  useEffect(() => {
    fetchYesterdayAvg();
  }, [fetchYesterdayAvg]);

  const yesterdayAvg = useDeepCompareMemo(
    () => get(yesterdayAvgDatas, [0, 'value']),
    [yesterdayAvgDatas]
  );

  const point = pointCode && guid ? get(pointData, [guid])?.get(pointCode) : undefined;
  const value = (point?.value ?? '--') as string | number;
  const unit = point?.unit ?? '';

  return (
    <CommonGauge
      value={value}
      valueDisplay={
        ((pointType === 'device' && deviceGuid) || pointType === 'space') && idc ? (
          <PointsLineModalButton
            variant="dashboard"
            btnText={
              <Statistic
                valueStyle={{ color: defaultColor, fontSize: 23 }}
                value={
                  typeof value === 'number' && max !== 0
                    ? `${((value / max) * 100).toFixed(precision)}`
                    : '--'
                }
                suffix={<Typography.Text style={{ color: defaultColor }}>{unit}</Typography.Text>}
                singleLine
                colon={false}
              />
            }
            modalText={modalText}
            pointGuids={
              point
                ? [
                    {
                      deviceGuid: point.deviceGuid!,
                      pointCode: point.pointCode!,
                      serieName: point.name!,
                      unit,
                    },
                  ]
                : []
            }
            idcTag={idc}
          />
        ) : (
          '--'
        )
      }
      previousValue={yesterdayAvg}
      color={defaultColor}
      {...rest}
    />
  );
}
