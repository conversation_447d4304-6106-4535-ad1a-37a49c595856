import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useLatest } from 'react-use';

import dayjs from 'dayjs';
import useDeepCompareEffect from 'use-deep-compare-effect';

import { message } from '@manyun/base-ui.ui.message';

import { SimpleLine } from '@manyun/monitoring.chart.simple-line';
import type { SimpleLineProps } from '@manyun/monitoring.chart.simple-line';
import { fetchChartData } from '@manyun/monitoring.service.fetch-chart-data';
import type {
  Interval,
  SeriesData,
  SeriesExtData,
  SvcQuery,
} from '@manyun/monitoring.service.fetch-chart-data';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

export type SimplePointLineProps = {
  pointCode: string;
  spaceGuid: string;
  timeAmount?: number;
  interval?: 'h' | 'd' | 'M';
  onDataChange?: (seriesData: SeriesData, seriesExtData: SeriesExtData) => void;
} & Omit<SimpleLineProps, 'seriesData'> &
  Pick<SvcQuery, 'isQueryExt'>;

export function SimplePointLine({
  pointCode,
  spaceGuid,
  timeAmount = 12,
  interval = 'h',
  isQueryExt,
  onDataChange,
  ...rest
}: SimplePointLineProps) {
  const onDataChangeRef = useLatest(onDataChange);
  const [seriesData, setSeriesDataList] = useState<SeriesData>([]);
  const [seriesExtData, setSeriesExtDataList] = useState<SeriesExtData>([]);
  const { idc } = getSpaceGuidMap(spaceGuid);
  const pointGuidList = useMemo(
    () => (spaceGuid !== '' && pointCode ? [{ deviceGuid: spaceGuid, pointCode }] : []),
    [spaceGuid, pointCode]
  );

  const getChartData = useCallback(async () => {
    if (!pointGuidList.length || !idc) {
      setSeriesDataList([]);
      return;
    }
    const { data, error } = await fetchChartData({
      pointGuidList,
      startTime: dayjs().clone().subtract(timeAmount, interval).valueOf(),
      endTime: dayjs().valueOf(),
      function: 'MAX',
      interval: interval.toUpperCase() as Interval,
      idcTag: idc,
      isQueryExt,
    });
    if (error) {
      message.error(error.message);
      return;
    }
    setSeriesDataList(data.data);
    setSeriesExtDataList(data.extData);
  }, [idc, timeAmount, interval, pointGuidList, isQueryExt]);

  useEffect(() => {
    getChartData();
  }, [getChartData]);

  useDeepCompareEffect(() => {
    onDataChangeRef.current?.(seriesData, seriesExtData);
  }, [onDataChangeRef, seriesData, seriesExtData]);

  return (
    <SimpleLine
      seriesData={seriesData}
      seriesExtData={seriesExtData}
      variant="dashboard"
      {...rest}
    />
  );
}
