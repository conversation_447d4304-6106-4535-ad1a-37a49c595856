import React from 'react';

import get from 'lodash.get';

import { Gauge } from '@manyun/base-ui.chart.gauge';
import { ThemeContext } from '@manyun/base-ui.chart.theme';

export type SimpleGaugeProps = {
  value: string | number;
  chartSize?: number;
  color?: string;
  showDetail?: boolean;
  precision?: number;
  max?: number;
  valueDisplay?: React.ReactNode;
};

export function SimpleGauge({
  chartSize = 60,
  showDetail = false,
  color,
  value,
  precision = 2,
  max = 100,
  valueDisplay,
}: SimpleGaugeProps) {
  const { json } = React.useContext(ThemeContext);
  const defaultColor = color ?? get(json, ['color', 0]);

  return (
    <div>
      <Gauge
        style={{ height: chartSize, width: chartSize }}
        variant="dashboard"
        option={{
          series: {
            type: 'gauge',
            startAngle: -270,
            endAngle: 90,
            max: max,
            radius: '100%',
            progress: {
              show: true,
              roundCap: true,
              width: 4,
              itemStyle: {
                color: defaultColor,
              },
            },
            axisLine: {
              roundCap: true,
              lineStyle: {
                width: 4,
                /**@clf 之后需要维护在base-uis */
                color: [[1, '#DCE6F3']],
              },
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            axisLabel: {
              show: false,
            },
            anchor: {
              show: false,
            },
            pointer: {
              show: false,
            },
            detail: {
              show: !!!valueDisplay && showDetail,
              valueAnimation: true,
              fontSize: 14,
              lineHeight: 40,
              offsetCenter: [0, 0],
              color: defaultColor,
              formatter: function () {
                return typeof value === 'number' && max !== 0
                  ? `${((value / max) * 100).toFixed(precision)}%`
                  : '--';
              },
            },
            data: [
              {
                value: typeof value === 'number' ? value : 0,
              },
            ],
          },
        }}
      />
      {valueDisplay && (
        <div
          style={{
            width: chartSize,
            display: 'flex',
            justifyContent: 'center',
            position: 'absolute',
            bottom: '42%',
          }}
        >
          {valueDisplay}
        </div>
      )}
    </div>
  );
}
