import React, { use<PERSON>allback, useContext, useState } from 'react';

import dayjs from 'dayjs';
import get from 'lodash.get';

import { Line } from '@manyun/base-ui.chart.line';
import type { LineProps } from '@manyun/base-ui.chart.line';
import { ThemeContext } from '@manyun/base-ui.chart.theme';
import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';

import { DurationSelect, getTimeRangeFromNow } from '@manyun/monitoring.chart.duration-select';
import type { DurationSelectProps, TimeRange } from '@manyun/monitoring.chart.duration-select';
import { getXAxisOption } from '@manyun/monitoring.chart.points-line';
import { PointsLineExport } from '@manyun/monitoring.chart.points-line-export';
import { fetchAiOpsPointForecast } from '@manyun/monitoring.service.fetch-ai-ops-point-forecast';
import type { ForecastMapper } from '@manyun/monitoring.service.fetch-ai-ops-point-forecast';
import type {
  ChartFunction,
  Interval,
  SeriesData,
} from '@manyun/monitoring.service.fetch-chart-data';
import { fetchChartData } from '@manyun/monitoring.service.fetch-chart-data';

import { ChartThemeLight } from './themes/chart-theme-light';

export type AiOpsLineProps = {
  chartStyle?: React.CSSProperties;
  pointCode: string;
  deviceGuid: string;
  idc: string;
  /**场景ID */
  scenesId: number;
  unit?: string;
  /**是否需要展示预测线 */
  showForecast?: boolean;
  /**获取点位数据时的聚合规则 */
  chartFunction?: ChartFunction;
  /**是否展示下载按钮 */
  showExport?: boolean;
};

export function AiOpsLine({
  chartStyle,
  pointCode,
  deviceGuid,
  idc,
  scenesId,
  unit,
  showForecast,
  chartFunction = 'MAX',
  defaultValue = getTimeRangeFromNow(-2, 'hours'),
  showExport,
  ...durationRestProps
}: AiOpsLineProps & Omit<DurationSelectProps, 'onChange' | 'onDidMount'>) {
  const [timeInterval, setTimeInterval] = useState<moment.Moment[]>(defaultValue);
  const [seriesData, setSeriesDataList] = useState<SeriesData>([]);
  const [forecastMapper, setForecastMapper] = useState<ForecastMapper>({
    upper: null,
    lower: null,
    forecast: null,
  });
  const forecastSeries = useDeepCompareMemo(
    () =>
      forecastMapper.forecast
        ? {
            name: forecastMapper.forecast.name,
            data: forecastMapper.forecast.values,
          }
        : null,
    [forecastMapper]
  );
  const baselineSeries = useDeepCompareMemo(() => {
    const list = [];
    forecastMapper.upper &&
      list.push({
        name: forecastMapper.upper.name,
        data: forecastMapper.upper.values,
      });
    forecastMapper.lower &&
      list.push({
        name: forecastMapper.lower.name,
        data: forecastMapper.lower.values,
      });
    return list;
  }, [forecastMapper]);

  const { json } = useContext(ThemeContext);

  const getChartData = useCallback(
    async (timeRange: TimeRange, interval: Interval) => {
      setTimeInterval(timeRange);
      const startTime = timeRange[0].valueOf();
      const endTime = timeRange[1].valueOf();
      const chartResponse = await fetchChartData({
        pointGuidList: [{ pointCode, deviceGuid }],
        startTime,
        endTime,
        function: chartFunction,
        interval,
        idcTag: idc,
      });
      const forecastResponse = await fetchAiOpsPointForecast({
        idcTag: idc,
        scenesId,
        pointCode,
        deviceGuid,
        startTime,
        endTime,
        function: chartFunction,
        interval,
      });
      if (chartResponse.error) {
        message.error(chartResponse.error.message);
      } else {
        setSeriesDataList(chartResponse.data.data);
      }
      if (forecastResponse.error) {
        message.error(forecastResponse.error.message);
      } else {
        setForecastMapper(forecastResponse.data);
      }
    },
    [idc, pointCode, deviceGuid, chartFunction, scenesId]
  );

  const seriesNames = useDeepCompareMemo(() => {
    const names = baselineSeries.map(({ name }) => name);
    if (showForecast && forecastSeries) {
      names.push(forecastSeries.name);
    }
    names.push('实测值');

    return names;
  }, [baselineSeries, showForecast, forecastSeries]);

  const seriesDataList = useDeepCompareMemo(() => {
    const dataList: SeriesData = baselineSeries.map(({ data }) => data);
    if (showForecast && forecastSeries) {
      dataList.push(forecastSeries.data);
    }
    dataList.push(get(seriesData, [0], []));

    return dataList;
  }, [baselineSeries, showForecast, forecastSeries, seriesData]);

  return (
    <>
      <Space style={{ display: 'flex', justifyContent: 'space-between' }}>
        <DurationSelect
          allowInterval
          defaultValue={defaultValue}
          onChange={getChartData}
          onDidMount={getChartData}
          {...durationRestProps}
        />
        {showExport && (
          <PointsLineExport
            seriesConfigList={seriesNames.map(name => ({ name, unit }))}
            seriesDataList={seriesDataList}
          />
        )}
      </Space>
      <Line
        variant="dashboard"
        style={{ width: '100%', paddingTop: 10, ...chartStyle }}
        option={{
          grid: {
            left: 40,
            right: 16,
          },
          xAxis: getXAxisOption(
            {
              type: 'time',
            },
            timeInterval
          ),
          yAxis: {
            name: unit,
          },
          series: [
            ...baselineSeries.map(series => ({
              data: series.data,
              smooth: true,
              name: series.name,
              unit,
              color: ChartThemeLight.line.markLine,
              lineStyle: {
                width: 1,
                shadowBlur: 0,
                shadowOffsetY: 0,
                type: 'dashed',
              },
              emphasis: {
                lineStyle: {
                  width: 1,
                },
              },
            })),
            showForecast &&
              forecastSeries && {
                data: forecastSeries.data,
                smooth: true,
                name: forecastSeries.name,
                unit,
                color: get(json, ['color', 1]),
                lineStyle: {
                  width: 2,
                },
                markArea: {
                  emphasis: { disabled: true },
                  itemStyle: {
                    opacity: 0.1,
                    color: ChartThemeLight.line.markArea,
                  },
                  data: [[{ xAxis: 0 }, { xAxis: dayjs().valueOf() }]],
                },
              },
            {
              data: get(seriesData, [0], []),
              smooth: true,
              name: '实测值',
              unit,
              color: get(json, ['color', 0]),
              lineStyle: {
                width: 2,
              },
            },
          ].filter(series => series) as LineProps['option']['series'],
        }}
      />
    </>
  );
}
