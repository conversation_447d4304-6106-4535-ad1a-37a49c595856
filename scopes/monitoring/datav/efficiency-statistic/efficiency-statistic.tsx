import React, { useCallback, useEffect, useMemo, useState } from 'react';

import type { XAXisOption } from 'echarts/types/dist/shared';
import moment from 'moment';

import { Line } from '@manyun/base-ui.chart.line';
import { CarbonEmissionColorful, WaterConsumptionColorful } from '@manyun/base-ui.icons';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Empty } from '@manyun/base-ui.ui.empty';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Space } from '@manyun/base-ui.ui.space';
import { Statistic } from '@manyun/base-ui.ui.statistic';
import { Typography } from '@manyun/base-ui.ui.typography';

import { PointsLineExport } from '@manyun/monitoring.chart.points-line-export';
import { fetchPointOfflineData } from '@manyun/monitoring.service.fetch-point-offline-data';

type Picker = 'week' | 'month' | 'year';

const defaultTimeRangeMapper: Record<string, [number, number]> = {
  DAY: [moment().subtract(1, 'month').valueOf(), moment().valueOf()],
  MONTH: [moment().subtract(1, 'year').valueOf(), moment().valueOf()],
  YEAR: [moment().subtract(10, 'year').valueOf(), moment().valueOf()],
};

export type EfficiencyStatisticProps = {
  style?: React.CSSProperties;
  type: 'WUE' | 'CUE';
  value?: string | number;
  title?: string;
  textColor: string;
  labelTextColor: string;
  unit?: string;
  showExport?: boolean;
  spaceGuid: string;
};

export const EfficiencyStatistic = ({
  style,
  type,
  value,
  title,
  textColor,
  unit,
  labelTextColor,
  showExport = true,
  spaceGuid,
}: EfficiencyStatisticProps) => {
  const [open, setOpen] = useState(false);
  const [pickerType, setPickerType] = useState<string>('DAY');
  const [timeRange, setTimeRange] = useState<[number, number]>(defaultTimeRangeMapper['DAY']);

  const [seriesData, setSeriesData] = useState<[number, number][]>([]);

  const formatText = useMemo(() => {
    switch (pickerType) {
      case 'MONTH':
        return 'YYYY-MM';
      case 'YEAR':
        return 'YYYY';
      default:
        return 'YYYY-MM-DD';
    }
  }, [pickerType]);

  const interval = useMemo(() => {
    switch (pickerType) {
      case 'MONTH':
        return 30 * 24 * 60 * 60 * 1000;
      case 'YEAR':
        return 12 * 30 * 24 * 60 * 60 * 1000;
      default:
        return 24 * 60 * 60 * 1000;
    }
  }, [pickerType]);

  const onLoad = useCallback(async () => {
    const { error, data } = await fetchPointOfflineData({
      startTime: moment(timeRange[0]).format(formatText),
      endTime: moment(timeRange[1]).format(formatText),
      dateType: pickerType,
      deviceGuid: spaceGuid,
      pointCode: type,
    });
    if (error) {
      message.error(error.message);
    }
    const series = data.data.reduce((series: [number, number][], item) => {
      if (item.dataValue !== null && item.dataValue !== undefined) {
        series.push([moment(item.time).valueOf(), item.dataValue]);
      }
      return series;
    }, []);
    setSeriesData(series);
  }, [formatText, pickerType, spaceGuid, timeRange, type]);

  useEffect(() => {
    if (open) {
      onLoad();
    }
  }, [onLoad, open, timeRange]);

  return (
    <>
      <div style={style}>
        <Statistic
          title={
            <div style={{ display: 'flex', alignItems: 'center' }}>
              {type === 'WUE' && <WaterConsumptionColorful style={{ fontSize: 24 }} />}
              {type === 'CUE' && <CarbonEmissionColorful style={{ fontSize: 24 }} />}
              <Typography.Text style={{ fontSize: 14, color: textColor, marginLeft: 10 }}>
                {type}
              </Typography.Text>
            </div>
          }
          value={value}
          formatter={value =>
            !!value ? (
              <div
                style={{
                  cursor: 'pointer',
                }}
                onClick={() => {
                  setOpen(true);
                }}
              >
                <Typography.Text>{value}</Typography.Text>
                {value !== '--' && (
                  <Typography.Text
                    style={{ fontSize: 14, marginLeft: 6, color: `${labelTextColor}` }}
                  >
                    {unit || ''}
                  </Typography.Text>
                )}
              </div>
            ) : null
          }
        />
        {title && (
          <Typography.Text
            style={{
              color: textColor,
            }}
          >
            {title}
          </Typography.Text>
        )}
      </div>
      <Modal
        width={960}
        bodyStyle={{ minHeight: '450px', overflow: 'hidden', overflowY: 'scroll' }}
        open={open}
        title={type}
        footer={null}
        destroyOnClose
        onCancel={() => setOpen(false)}
      >
        <Space style={{ justifyContent: 'space-between', width: '100%', marginBottom: 30 }}>
          <Space size={16}>
            <Radio.Group
              optionType="button"
              options={[
                { label: '日', value: 'DAY' },
                { label: '月', value: 'MONTH' },
                { label: '年', value: 'YEAR' },
              ]}
              value={pickerType}
              onChange={e => {
                const value = e.target.value;
                setPickerType(value);
                setTimeRange(defaultTimeRangeMapper[value]);
              }}
            />
            <DatePicker.RangePicker
              style={{ width: 384 }}
              allowClear={false}
              picker={pickerType.toLocaleLowerCase() as Picker}
              value={[moment(timeRange[0]), moment(timeRange[1])]}
              onChange={values => {
                if (values) {
                  setTimeRange(values.map(value => moment(value).valueOf()) as [number, number]);
                }
              }}
            />
          </Space>
          {showExport && (
            <PointsLineExport
              text=""
              timeFormat={formatText}
              type="default"
              seriesConfigList={[{ name: type, unit: unit }]}
              seriesDataList={[seriesData]}
              compact={false}
            />
          )}
        </Space>
        {seriesData.length > 0 && seriesData.some(serieData => serieData.length > 0) ? (
          <Line
            variant="dashboard"
            style={{ height: 400, width: 915 }}
            option={{
              legend: {
                top: undefined,
                left: 'center',
                bottom: 10,
              },
              grid: {
                top: 60,
                left: 40,
                right: 40,
                bottom: 60,
              },
              toolbox: { feature: { dataZoom: { yAxisIndex: 'none' }, restore: {} } },
              xAxis: {
                type: 'time',
                axisLabel: {
                  formatter: function (value: string) {
                    return moment(value).format(pickerType === 'DAY' ? 'MM.DD' : formatText);
                  },
                  showMaxLabel: true,
                },
                minInterval: interval,
              } as XAXisOption,
              yAxis: {
                type: 'value',
                name: type,
              },
              series: [
                {
                  data: seriesData,
                  name: type,
                  type: 'line',
                  smooth: true,
                },
              ],
              tooltip: {
                timeFormat: formatText,
              },
            }}
          />
        ) : (
          <Empty />
        )}
      </Modal>
    </>
  );
};
