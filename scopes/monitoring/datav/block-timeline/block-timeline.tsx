import React from 'react';
import { useHistory } from 'react-router-dom';

import FullscreenExitOutlined from '@ant-design/icons/es/icons/FullscreenExitOutlined';
import FullscreenOutlined from '@ant-design/icons/es/icons/FullscreenOutlined';
import LeftOutlined from '@ant-design/icons/es/icons/LeftOutlined';
import RightOutlined from '@ant-design/icons/es/icons/RightOutlined';
import { useApolloClient } from '@apollo/client';
import classNames from 'classnames';
import get from 'lodash.get';

import { ThemeContext } from '@manyun/base-ui.chart.theme';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Space } from '@manyun/base-ui.ui.space';
import { Timeline } from '@manyun/base-ui.ui.timeline';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';

import { readSpace } from '@manyun/resource-hub.gql.client.spaces';
import { generateGraphixFloorViewRoutePath } from '@manyun/resource-hub.route.resource-routes';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

import styles from './block-timeline.module.less';

type BlockExtandType = {
  /** 楼栋切换 */
  onBlockChange: (block: string) => void;
  activeBlock?: string;
  idc?: string;
  onIdcClick?: () => void;
};

type BlockSwitchItem = {
  label: string;
  value: string;
  guid: string;
};

type BlockSwitcherProps = {
  /** 楼栋列表 */
  blocks: BlockSwitchItem[];
} & BlockExtandType;

type FloorSwitcherProps = {
  /** 楼层列表 */
  floorInfos: { floorGuid: string; floorName: string }[];
};

export type BlockSwitchPluginProps = {
  onCollapseChange: (showChart: boolean) => void;
  collapse: boolean;
  blockGuids: string[];
} & BlockExtandType &
  FloorSwitcherProps;

/**
 * 楼栋切换
 * @returns
 */
export function ResourceSwitcher({
  blocks = [],
  onBlockChange,
  activeBlock,
  idc,
  onIdcClick,
}: BlockSwitcherProps) {
  const { json } = React.useContext(ThemeContext);
  const pageIconInactiveColor = get(json, ['legend', 'pageIconInactiveColor']);

  const history = useHistory();

  return (
    <div className={styles.resourceSwitcher}>
      {idc && (
        <div className={styles.idcTimeLine}>
          <Tooltip title="机房">
            <div
              className={classNames(
                activeBlock
                  ? styles.idcTimeLineIcon
                  : styles.idcTimeLineIconActive
              )}
              onClick={() => {
                if (activeBlock) {
                } else {
                  onIdcClick?.();
                }
              }}
            />
          </Tooltip>
          <Divider
            type="vertical"
            className={styles.idcTimeLineDivider}
            style={{
              borderLeft: `1px solid ${pageIconInactiveColor}`,
              margin: '0 0 0 4px',
            }}
          />
        </div>
      )}
      <BlockSwitchTimeline
        blocks={blocks}
        activeBlock={activeBlock}
        onBlockChange={onBlockChange}
      />
    </div>
  );
}

export type BlockSwitchTimelineProps = {
  blocks: BlockSwitchItem[];
  activeBlock?: string;
  onBlockChange: (block: string) => void;
  style?: React.CSSProperties;
  pageSize?: number;
  className?: string;
};

export function BlockSwitchTimeline({
  blocks = [],
  activeBlock,
  onBlockChange,
  style,
  pageSize = 5,
  className,
}: BlockSwitchTimelineProps) {
  const [page, setPage] = React.useState(1);
  const [selected, setSelected] = React.useState('0');

  return (
    <div style={style} className={styles.blockTimeline}>
      <Timeline
        className={className}
        type="horizontal"
        pageSize={pageSize}
        selected={selected}
        page={page}
        leftIcon={<LeftOutlined />}
        rightIcon={<RightOutlined />}
        onChange={(changed) => {
          if (changed.page !== undefined) {
            setPage(changed.page);
          }
          if (changed.selected !== undefined) {
            setSelected(changed.selected.toString());
          }
        }}
      >
        {blocks.map((blockInfo, index) => {
          return (
            <BlockItem
              key={blockInfo.value}
              activeBlock={activeBlock}
              index={index}
              blockInfo={blockInfo}
              onBlockChange={(value) => {
                typeof onBlockChange === 'function' && onBlockChange(value);
              }}
            />
          );
        })}
      </Timeline>
    </div>
  );
}

export type BlockItemProps = {
  activeBlock?: string;
  blockInfo: BlockSwitchItem;
  index: number;
  onBlockChange: (value: string) => void;
};

export function BlockItem({
  activeBlock,
  blockInfo,
  index,
  onBlockChange,
}: BlockItemProps) {
  const { json } = React.useContext(ThemeContext);
  const color = get(json, ['color']);
  const { guid } = blockInfo;

  const client = useApolloClient();
  const blockSpace = readSpace(client, guid);

  return (
    <div
      className={classNames(
        styles.blockTimeLine,
        activeBlock === blockInfo.value && styles.blockTimeLineActive
      )}
    >
      <Tooltip title={blockSpace?.label || ''}>
        <Timeline.Item
          style={{
            color: activeBlock === blockInfo.value ? color[0] : undefined,
          }}
          id={index.toString()}
          onClick={() => {
            onBlockChange(blockInfo.value);
          }}
        >
          {blockInfo.label}
        </Timeline.Item>
      </Tooltip>
    </div>
  );
}

export function FloorSwitcher({ floorInfos }: FloorSwitcherProps) {
  const [page, setPage] = React.useState(1);
  return (
    <div className={styles.floorTimeLine}>
      <Timeline
        type="horizontal"
        pageSize={5}
        page={page}
        leftIcon={<LeftOutlined />}
        rightIcon={<RightOutlined />}
        onChange={(changed) => {
          if (changed.page !== undefined) {
            setPage(changed.page);
          }
        }}
      >
        {floorInfos.map((floorInfo) => {
          return <FloorItem key={floorInfo.floorGuid} floorInfo={floorInfo} />;
        })}
      </Timeline>
    </div>
  );
}

export type FloorItemProps = {
  floorInfo: { floorGuid: string; floorName: string };
};

export function FloorItem({ floorInfo }: FloorItemProps) {
  const [idc, block, floor] = floorInfo.floorGuid.split('.');
  return (
    <div className={classNames(styles.floorTimeLineIcon)}>
      <Tooltip title={floorInfo.floorName}>
        <Timeline.Item
          id={floor}
          onClick={() =>
            window.open(
              generateGraphixFloorViewRoutePath({
                idc,
                block,
                floor,
              })
            )
          }
        >
          {floor}
        </Timeline.Item>
      </Tooltip>
    </div>
  );
}

export function BlockSwitchPlugin({
  collapse,
  onCollapseChange,
  blockGuids = [],
  floorInfos = [],
  onBlockChange,
  activeBlock,
  idc,
  onIdcClick,
}: BlockSwitchPluginProps) {
  return (
    <div className={styles.blockPluginExtand}>
      <Space>
        {!!blockGuids?.length && (
          <ResourceSwitcher
            blocks={blockGuids.map((blockGuid) => {
              const block = getSpaceGuidMap(blockGuid).block!;
              return {
                label: block,
                value: block,
                guid: blockGuid,
              };
            })}
            activeBlock={activeBlock}
            idc={idc}
            onBlockChange={onBlockChange}
            onIdcClick={onIdcClick}
          />
        )}
        {!!floorInfos?.length && <FloorSwitcher floorInfos={floorInfos} />}
        <div className="plugin-container">
          {collapse ? (
            <Tooltip title="展开">
              <FullscreenOutlined onClick={() => onCollapseChange(false)} />
            </Tooltip>
          ) : (
            <Tooltip title="收拢">
              <FullscreenExitOutlined onClick={() => onCollapseChange(true)} />
            </Tooltip>
          )}
        </div>
      </Space>
    </div>
  );
}
