@import (reference) '@manyun/base-ui.theme.datav-theme/dist/datav-theme.less';

.resourceSwitcher {
  display: flex;
  height: 36px;
  line-height: 40px;
  background: fade(@background-color-light, 60%);
  border: 1px solid @radio-button-checked-bg;
  border-radius: 4px;
  padding: 2px 2px;
}
.blockTimeline {
  display: flex;
  justify-content: center;
  border-radius: 4px;
  :global {
    .@{prefixCls}-timeline {
      padding: 0px 6px;
      display: flex;
    }
    .@{prefixCls}-timeline-item-tail,
    .@{prefixCls}-timeline-item-head {
      display: none;
    }
    .@{prefixCls}-timeline-item-content {
      top: 6px;
      right: 16px;
    }
    .anticon {
      top: 9px;
    }
  }
}

.datavTimeLine {
  background-repeat: no-repeat;
  cursor: pointer;
  background-position: top;
}

.blockTimeLine {
  background-image: url('./assets/icon.svg');
  background-size: 29px 33px;
  margin: 0 6px;
  .datavTimeLine;
}

.idcTimeLineIcon {
  background-image: url('./assets/idc.svg');
  background-size: 22px 32px;
  width: 40px;
  height: 36px;
  .datavTimeLine;
}

.floorTimeLineIcon {
  background-image: url('./assets/floor.svg');
  margin: 0 6px;
  background-size: 16px 36px;
  .datavTimeLine;
}

.floorTimeLine {
  .resourceSwitcher;
  display: flex;
  justify-content: center;
  border-radius: 4px;
  :global {
    .@{prefixCls}-timeline {
      padding: 0px 6px;
      display: flex;
    }
    .@{prefixCls}-timeline-item-tail,
    .@{prefixCls}-timeline-item-head {
      display: none;
    }
    .@{prefixCls}-timeline-item-content {
      top: 1px;
      right: 24px;
      font-size: 12px;
    }
    .anticon {
      top: 9px;
    }
  }
}

.idcTimeLineIconActive {
  background-image: url('./assets/idc-active.svg');
  background-size: 22px 32px;
  .datavTimeLine;
  width: 40px;
  height: 36px;
}

.idcTimeLine {
  width: 40px;
  height: 36px;
  display: flex;
  align-items: center;
}

.idcTimeLineDivider {
  height: 18px;
  top: -3px;
}

.blockTimeLineActive {
  background-image: url('./assets/icon-active.svg');
}

.blockPluginExtand {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  :global {
    .plugin-container {
      cursor: pointer;
      width: 36px;
      height: 36px;
      background: fade(@background-color-light, 65%);
      border: 1px solid @radio-button-checked-bg;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      span {
        font-size: 18px;
      }
    }
  }
}
