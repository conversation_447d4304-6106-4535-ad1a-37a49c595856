export { Alarm } from './alarm';
export type { SyntheticState as AlarmSyntheticState, AlarmJSON } from './alarm';
export type {
  BackendAlarmState,
  BackendAlarmLifecycleState,
  BackendAlarmTriggerConditionCode,
  BackendAlarmType,
  BackendAlarmSourceDimension,
  BackendAlarm,
  BackMergeStatus,
  BackendExpectStatus,
} from './backend-alarm.type';
export { getAlarmLocales } from './locales';
export type { AlarmLocales } from './locales/type';
