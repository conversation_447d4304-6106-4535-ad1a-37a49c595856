import type { DataType } from '@manyun/resource-hub.model.point';

export type BackendAlarmState = 'TRIGGER' | 'SUPPRESS' | 'RECOVER';

export type BackendAlarmTriggerConditionCode = 'UPPER' | 'LOWER' | 'EQUAL' | 'OTHER';

export type BackendAlarmLifecycleState =
  | /** @deprecated */ 'INIT' /* 初始状态，出现了告警，但是没有达到阈值 */
  | 'ACTIVE' /* 未确认状态，未处理 */
  | 'CONFIRMED' /* 已确认状态 */
  | 'PROCESS' /* 已建单跟进状态 */
  | 'REMOVED'; /* 已删除状态 */

export type BackendAlarmType = 'ERROR' | 'WARN';

export type BackendExpectStatus =
  | /** 正常 */ 'NORMAL'
  | /** 预期内 */ 'EXPECTED'
  | /** 预期外 */ 'UNEXPECTED';

export type BackMergeStatus = 'UNMERGED' | 'CHILD' | 'ROOT';

export type BackendAlarmSourceDimension = 'IDC' | 'BLOCK' | 'ROOM' | 'COLUMN' | 'GRID' | 'DEVICE';

export type ShieldBizType = 'CHANGE' | 'POWER' | 'COMMON';

export type AlarmItemType = 'POINT' | 'DYNAMIC_BASELINE';

export type BackendAlarm = {
  id: number;
  itemId: number;
  itemType: AlarmItemType;
  threshold: string;
  gridCount: number;
  deviceCount: number;
  customerCount: number;
  notifyCount: number;
  deviceType: string;
  deviceLabel: string | null;
  deviceGuid: string;
  triggerGuid: string;
  deviceName: string | null;
  gridTag: string | null;
  columnTag: string | null;
  pointCode: string;
  pointCodeName: string;
  unit: string | null;
  validLimits: Record<string, string> | null;
  dataType: {
    code: DataType;
    name: string;
  };
  triggerSnapshot: string;
  pointValue: number;
  idcTag: string;
  blockTag: string | null;
  roomTag: string | null;
  roomName: string | null;
  roomType: string | null;
  triggerDimension: BackendAlarmSourceDimension;
  alarmStatus: {
    code: BackendAlarmLifecycleState;
    name: string;
  };
  triggerStatus: {
    code: BackendAlarmState;
    name: string;
  };
  triggerCondition: {
    code: BackendAlarmTriggerConditionCode;
    name: string;
  };
  confirmByName: string | null;
  confirmBy: number | null;
  alarmLevel: string;
  alarmType: {
    code: BackendAlarmType;
    name: string;
  };
  notifyContent: string;
  mergeCount: number;
  mergeRuleId: number;
  eventId: string | null;
  /**
   * 注意：不能用这个字段作为告警开始时间，因为它是
   * 告警数据入数据库的时间，而不是真正的开发时间
   *
   * @example
   *
   * ```ts
   * const gmtCreate = "2021-12-30 15:26:38";
   * ```
   * */
  gmtCreate: string;
  /** Date string like "2021-12-30 15:26:38" */
  triggerTime: string;
  confirmTime: string | null;
  recoverTime: string | null;
  /**
   * 真正的告警开始时间
   */
  activeTime: string;
  changeId: string | null;
  alarmReason: string | null;
  removeTime: string | null;
  removeDesc: string | null;
  removeBy: string | null;
  expectStatus: BackendExpectStatus;
  mergeStatus: BackMergeStatus;
  /** 告警触发时长 （秒） */
  alarmDuration: number;
  /* 屏蔽类型*/
  shieldBizType: ShieldBizType | null;
  totalCount: number;
};
