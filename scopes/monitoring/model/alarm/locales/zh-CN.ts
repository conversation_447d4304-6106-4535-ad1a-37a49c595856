import type { AlarmLocales } from './type';

export const zhCN: AlarmLocales = {
  type: {
    name: '告警类型',
  },
  level: '级别',
  source: {
    __self: '告警对象',
    spaceGuidMap: {
      __self: '位置',
      idcTag: '机房',
      blockTag: '楼',
      roomTag: '包间',
      columnTag: '机列',
      gridTag: '机柜',
    },
    roomName: '包间名称',
    roomType: '包间类型',
    deviceType: '设备类型',
    deviceLabel: '设备标签',
  },
  mergeCount: '收敛告警数',
  monitoringItem: {
    __self: '命中规则',
    threshold: '触发条件',
  },
  point: {
    name: '测点名称',
  },
  pointData: {
    current: '现值',
    snapshot: '告警值',
  },
  cause: {
    name: '告警原因',
  },
  state: {
    __self: '告警状态',
    TRIGGER: '触发中',
    SUPPRESS: '被抑制',
    RECOVER: '已恢复',
  },
  lifecycleState: {
    __self: '处理状态',
    INIT: '初始',
    ACTIVE: '未受理',
    CONFIRMED: '已受理',
    PROCESS: '已建单',
    REMOVED: '已删除',
  },
  createdAt: '告警开始时间',
  latestTriggeredAt: '最新告警时间',
  recoveredAt: '告警恢复时间',
  confirmedAt: '受理时间',
  removedAt: '下盯屏时间',
  eventId: '事件单号',
  confirmUser: {
    __self: '受理人',
  },
  gridCount: '影响机柜数',
  customerCount: '影响客户数',
  deviceCount: '影响设备数',
  notifyCount: '通知次数',
  message: '告警内容',
  reason: '处理反馈',
  removeUser: '下盯屏人',
  changeId: '变更单号',
  expectedStatus: {
    __self: '变更状态',
    NORMAL: '正常',
    EXPECTED: '预期内',
    UNEXPECTED: '预期外',
  },
  mergeStatus: {
    __self: '收敛状态',
    UNMERGED: '未收敛',
    CHILD: '被收敛',
    ROOT: '已收敛',
  },
  alarmDuration: '告警持续时长',
  alarmLabel: '告警标签',
};

export default zhCN;
