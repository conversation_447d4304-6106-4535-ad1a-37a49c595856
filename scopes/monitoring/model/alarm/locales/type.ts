import type {
  BackMergeStatus,
  BackendAlarmLifecycleState,
  BackendAlarmState,
  BackendExpectStatus,
} from '../backend-alarm.type';

// TODO @Jerry refactor this type into a top-level locale component
export type LocaleCode = 'zh-CN';

export type AlarmLocales = {
  type: {
    name: string;
  };
  level: string;
  source: {
    __self: string;
    spaceGuidMap: {
      __self: string;
      idcTag: string;
      blockTag: string;
      roomTag: string;
      columnTag: string;
      gridTag: string;
    };
    roomName: string;
    roomType: string;
    deviceType: string;
    deviceLabel: string;
  };
  mergeCount: string;
  monitoringItem: {
    __self: string;
    threshold: string;
  };
  point: {
    name: string;
  };
  pointData: {
    current: string;
    snapshot: string;
  };
  cause: {
    name: string;
  };
  /**
   * `__self` 表示 `state` 字段本身的文案
   */
  state: Record<'__self' | BackendAlarmState, string>;
  lifecycleState: Record<'__self' | BackendAlarmLifecycleState, string>;
  createdAt: string;
  latestTriggeredAt: string;
  recoveredAt: string;
  confirmedAt: string;
  removedAt: string;
  eventId: string;
  confirmUser: {
    __self: string;
  };
  gridCount: string;
  customerCount: string;
  deviceCount: string;
  notifyCount: string;
  message: string;
  reason: string;
  removeUser: string;
  changeId: string;
  expectedStatus: Record<'__self' | BackendExpectStatus, string>;
  mergeStatus: Record<'__self' | BackMergeStatus, string>;
  alarmDuration: string;
  alarmLabel: string;
};
