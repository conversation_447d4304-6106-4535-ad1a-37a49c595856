import dayjs from 'dayjs';
import cloneDeep from 'lodash.clonedeep';

import type { DataType } from '@manyun/resource-hub.model.point';

import type {
  AlarmItemType,
  BackMergeStatus,
  BackendAlarm,
  BackendAlarmLifecycleState,
  BackendAlarmSourceDimension,
  BackendAlarmState,
  BackendAlarmTriggerConditionCode,
  BackendAlarmType,
  BackendExpectStatus,
  ShieldBizType,
} from './backend-alarm.type';
import { getAlarmLocales } from './locales';
import type { AlarmLocales, LocaleCode } from './locales/type';

type RoomType = string;

type AlarmSource = {
  variant: 'device' | 'space';
  dimension: BackendAlarmSourceDimension;
  guid: string;
  name: string;
  spaceGuidMap: SpaceGuidMap;
  roomName: string | null;
  roomType: RoomType | null;
  /**
   * 设备标签
   */
  deviceLabel: string | null;
  deviceType: string;
  deviceTypeLevel1: string;
};

type SpaceGuidMap = {
  idcTag: string;
  blockTag: string | null;
  roomTag: string | null;
  columnTag: string | null;
  gridTag: string | null;
};

type ConfirmUser = {
  id: number;
  name: string;
} | null;

export type SyntheticState =
  | 'unaccepted-n-unrecovered'
  | 'accepted-n-unrecovered'
  | 'unaccepted-n-recovered'
  | 'accepted-n-recovered';

export type AlarmJSON = {
  id: number;
  /**
   * 告警对象
   */
  source: AlarmSource;
  state: {
    code: BackendAlarmState;
    name: string;
  };
  /**
   * 告警内容
   */
  message: string;
  /**
   * 告警等级编码
   */
  level: string;
  type: {
    code: BackendAlarmType;
    name: string;
  };
  lifecycleState: {
    code: BackendAlarmLifecycleState;
    name: string;
  };
  point: {
    code: string;
    name: string;
    validLimits: Record<string, string> | null;
    dataType: {
      code: DataType;
      name: string;
    };
    unit: string | null;
  };
  pointData: {
    current: number;
    snapshot: string;
  };
  /**
   * 告警原因
   */
  cause: {
    code: BackendAlarmTriggerConditionCode;
    name: string;
  };
  createdAt: number;
  latestTriggeredAt: number;
  mergeCount: number;
  /**
   * 影响机柜数
   */
  gridCount: number;
  /**
   * 影响设备数
   */
  deviceCount: number;
  /**
   * 影响客户数
   */
  customerCount: number;
  /**
   * 通知次数
   */
  notifyCount: number;
  eventId: string | null;
  /**
   * 命中的监控项
   */
  monitoringItem: {
    id: number;
    type: AlarmItemType;
    threshold: string;
  };
  confirmUser: ConfirmUser;
  confirmedAt: number | null;
  mergeRuleId: number;
  recoveredAt: number | null;
  activatedAt: number | null;
  changeId: string | null;
  reason: string | null;
  removedAt: number | null;
  removedDescriptions: string | null;
  removeUser: string | null;
  /**
   * 预期状态
   *
   * > e.g. 若告警是在变更单窗口内发生的，会视为预期内告警
   */
  expectedStatus: BackendExpectStatus;
  mergeStatus: BackMergeStatus;
  /** 告警触发时长 （秒） */
  alarmDuration: number;
  shieldBizType: ShieldBizType | null;
  totalCount: number;
};

export class Alarm {
  static UN_RESOLVED_ALARM_STATE_KEYS: BackendAlarmState[] = ['TRIGGER'];

  static fromApiObject(backendAlarm: BackendAlarm): Alarm {
    const copy = cloneDeep(backendAlarm);
    const isDevice = copy.triggerDimension === 'DEVICE';

    return new Alarm(
      copy.id,
      {
        variant: isDevice ? 'device' : 'space',
        dimension: copy.triggerDimension,
        guid: copy.triggerGuid,
        name: isDevice ? copy.deviceName! : copy.triggerGuid,
        spaceGuidMap: {
          idcTag: copy.idcTag,
          blockTag: copy.blockTag,
          roomTag: copy.roomTag,
          columnTag: copy.columnTag,
          gridTag: copy.gridTag,
        },
        roomName: copy.roomName,
        roomType: copy.roomType,
        deviceLabel: isDevice ? copy.deviceLabel : null,
        deviceType: copy.deviceType,
      },
      copy.triggerStatus,
      copy.notifyContent,
      copy.alarmLevel,
      copy.alarmType,
      copy.alarmStatus,
      {
        code: copy.pointCode,
        name: copy.pointCodeName,
        validLimits: copy.validLimits,
        dataType: copy.dataType,
        unit: copy.unit,
      },
      {
        current: copy.pointValue,
        snapshot: copy.triggerSnapshot,
      },
      copy.triggerCondition,
      new Date(copy.activeTime).getTime(),
      new Date(copy.triggerTime).getTime(),
      copy.mergeCount,
      copy.gridCount,
      copy.deviceCount,
      copy.notifyCount,
      copy.customerCount,
      copy.eventId,
      {
        id: copy.itemId,
        type: copy.itemType,
        threshold: copy.threshold,
      },
      copy.confirmBy !== null && copy.confirmByName
        ? {
            id: copy.confirmBy,
            name: copy.confirmByName,
          }
        : null,
      copy.confirmTime ? new Date(copy.confirmTime).getTime() : null,
      copy.mergeRuleId,
      copy.recoverTime ? new Date(copy.recoverTime).getTime() : null,
      copy.activeTime ? new Date(copy.activeTime).getTime() : null,
      copy.changeId,
      copy.alarmReason,
      copy.removeTime ? new Date(copy.removeTime).getTime() : null,
      copy.removeDesc,
      copy.removeBy,
      copy.expectStatus,
      copy.mergeStatus,
      copy.alarmDuration,
      copy.shieldBizType,
      copy.totalCount
    );
  }

  static fromJSON(alarmJSON: AlarmJSON): Alarm {
    const copy = cloneDeep(alarmJSON);

    return new Alarm(
      copy.id,
      copy.source,
      copy.state,
      copy.message,
      copy.level,
      copy.type,
      copy.lifecycleState,
      copy.point,
      copy.pointData,
      copy.cause,
      copy.createdAt,
      copy.latestTriggeredAt,
      copy.mergeCount,
      copy.gridCount,
      copy.deviceCount,
      copy.notifyCount,
      copy.customerCount,
      copy.eventId,
      copy.monitoringItem,
      copy.confirmUser,
      copy.confirmedAt,
      copy.mergeRuleId,
      copy.recoveredAt,
      copy.activatedAt,
      copy.changeId,
      copy.reason,
      copy.removedAt,
      copy.removedDescriptions,
      copy.removeUser,
      copy.expectedStatus,
      copy.mergeStatus,
      copy.alarmDuration,
      copy.shieldBizType,
      copy.totalCount
    );
  }

  /** @deprecated Use `static fromJSON` instead */
  static parseJSON = Alarm.fromJSON;

  /** @deprecated Use instance method instead */
  static toApiObject(alarm: Alarm): BackendAlarm {
    return {
      id: alarm.id,
      idcTag: alarm.source.spaceGuidMap.idcTag,
      blockTag: alarm.source.spaceGuidMap.blockTag,
      roomTag: alarm.source.spaceGuidMap.roomTag,
      columnTag: alarm.source.spaceGuidMap.columnTag,
      gridTag: alarm.source.spaceGuidMap.gridTag,
      roomName: alarm.source.roomName,
      roomType: alarm.source.roomType,
      triggerDimension: alarm.source.dimension,
      triggerGuid: alarm.source.guid,
      deviceGuid: alarm.source.guid,
      deviceName: alarm.source.dimension === 'DEVICE' ? alarm.source.name : null,
      deviceLabel: alarm.source.deviceLabel,
      triggerStatus: alarm.state,
      notifyContent: alarm.message,
      alarmLevel: alarm.level,
      alarmType: alarm.type,
      pointCode: alarm.point.code,
      pointCodeName: alarm.point.name,
      pointValue: alarm.pointData.current,
      triggerCondition: alarm.cause,
      gmtCreate: dayjs(alarm.createdAt).format('YYYY-MM-DD HH:mm:ss'),
      triggerTime: dayjs(alarm.latestTriggeredAt).format('YYYY-MM-DD HH:mm:ss'),
      mergeCount: alarm.mergeCount,
      threshold: alarm.monitoringItem.threshold,
      triggerSnapshot: alarm.pointData.snapshot,
      validLimits: alarm.point.validLimits,
      unit: alarm.point.unit,
      eventId: alarm.eventId,
      alarmStatus: alarm.lifecycleState,
      gridCount: alarm.gridCount,
      deviceCount: alarm.deviceCount,
      notifyCount: alarm.notifyCount,
      customerCount: alarm.customerCount,
      itemId: alarm.monitoringItem.id,
      itemType: alarm.monitoringItem.type,
      deviceType: alarm.source.deviceType,
      dataType: alarm.point.dataType,
      confirmBy: alarm.confirmUser ? alarm.confirmUser.id : null,
      confirmByName: alarm.confirmUser ? alarm.confirmUser.name : null,
      confirmTime: alarm.confirmedAt
        ? dayjs(alarm.confirmedAt).format('YYYY-MM-DD HH:mm:ss')
        : null,
      mergeRuleId: alarm.mergeRuleId,
      recoverTime: alarm.recoveredAt
        ? dayjs(alarm.recoveredAt).format('YYYY-MM-DD HH:mm:ss')
        : null,
      activeTime: dayjs(alarm.activatedAt).format('YYYY-MM-DD HH:mm:ss'),
      changeId: alarm.changeId,
      alarmReason: alarm.reason,
      removeTime: alarm.removedAt ? dayjs(alarm.removedAt).format('YYYY-MM-DD HH:mm:ss') : null,
      removeDesc: alarm.removedDescriptions,
      removeBy: alarm.removeUser,
      expectStatus: alarm.expectedStatus,
      mergeStatus: alarm.mergeStatus,
      alarmDuration: alarm.alarmDuration,
      shieldBizType: alarm.shieldBizType,
      totalCount: alarm.totalCount,
    };
  }

  static buildSyntheticState(
    lifecycleState: BackendAlarmLifecycleState,
    state: BackendAlarmState
  ): SyntheticState {
    return buildSyntheticState(lifecycleState, state);
  }

  private _localeCode: LocaleCode = 'zh-CN';
  private _locales: AlarmLocales;

  constructor(
    public id: number,
    /** 告警源 */
    public source: Omit<AlarmSource, 'deviceTypeLevel1'>,
    /** 处理状态 */
    public state: {
      code: BackendAlarmState;
      name: string;
    },
    /** 告警消息内容 */
    public message: string,
    public level: string,
    public type: {
      code: BackendAlarmType;
      name: string;
    },
    /** 告警状态 */
    public lifecycleState: {
      code: BackendAlarmLifecycleState;
      name: string;
    },
    public point: {
      code: string;
      name: string;
      validLimits: Record<string, string> | null;
      dataType: {
        code: DataType;
        name: string;
      };
      unit: string | null;
    },
    public pointData: {
      current: number;
      snapshot: string;
    },
    public cause: {
      code: BackendAlarmTriggerConditionCode;
      name: string;
    },
    public createdAt: number,
    public latestTriggeredAt: number,
    public mergeCount: number,
    public gridCount: number,
    public deviceCount: number,
    public notifyCount: number,
    public customerCount: number,
    public eventId: string | null,
    public monitoringItem: {
      id: number;
      type: AlarmItemType;
      threshold: string;
    },
    public confirmUser: ConfirmUser,
    public confirmedAt: number | null,
    public mergeRuleId: number,
    /** 恢复时间 */
    public recoveredAt: number | null,
    /** 确认时间 */
    public activatedAt: number | null,
    public changeId: string | null,
    /** 处理反馈 */
    public reason: string | null,
    public removedAt: number | null,
    /** 详细原因 */
    public removedDescriptions: string | null,
    /** 下盯屏人 */
    public removeUser: string | null,
    public expectedStatus: BackendExpectStatus,
    public mergeStatus: BackMergeStatus,
    public alarmDuration: number,
    public shieldBizType: ShieldBizType | null,
    public totalCount: number
  ) {
    this._locales = getAlarmLocales(this._localeCode);

    // Type fixes
    if (typeof this.level == 'number') {
      this.level = String(this.level);
    }
  }

  /**
   * @experimental
   */
  public get sourceDeviceTypeLevel1() {
    return this.source.deviceType?.slice(0, 1);
  }

  /**
   * 告警是否已下盯屏的标志
   */
  public get isClosed() {
    return this.removedAt !== null;
  }

  public get syntheticState(): SyntheticState {
    return buildSyntheticState(this.lifecycleState.code, this.state.code);
  }

  public get expectedStatusText(): string {
    return this._locales.expectedStatus[this.expectedStatus];
  }

  public setLocale(localeCode: LocaleCode) {
    this._localeCode = localeCode;
  }

  public toJSON(): AlarmJSON {
    return cloneDeep({
      id: this.id,
      source: { ...this.source, deviceTypeLevel1: this.sourceDeviceTypeLevel1 },
      state: this.state,
      message: this.message,
      level: this.level,
      type: this.type,
      lifecycleState: this.lifecycleState,
      point: this.point,
      pointData: this.pointData,
      cause: this.cause,
      createdAt: this.createdAt,
      latestTriggeredAt: this.latestTriggeredAt,
      mergeCount: this.mergeCount,
      gridCount: this.gridCount,
      deviceCount: this.deviceCount,
      notifyCount: this.notifyCount,
      customerCount: this.customerCount,
      eventId: this.eventId,
      monitoringItem: this.monitoringItem,
      confirmUser: this.confirmUser,
      confirmedAt: this.confirmedAt,
      mergeRuleId: this.mergeRuleId,
      recoveredAt: this.recoveredAt,
      activatedAt: this.activatedAt,
      changeId: this.changeId,
      reason: this.reason,
      removedAt: this.removedAt,
      removedDescriptions: this.removedDescriptions,
      removeUser: this.removeUser,
      expectedStatus: this.expectedStatus,
      mergeStatus: this.mergeStatus,
      alarmDuration: this.alarmDuration,
      shieldBizType: this.shieldBizType,
      totalCount: this.totalCount,
    });
  }

  public toApiObject(): BackendAlarm {
    return cloneDeep({
      id: this.id,
      idcTag: this.source.spaceGuidMap.idcTag,
      blockTag: this.source.spaceGuidMap.blockTag,
      roomTag: this.source.spaceGuidMap.roomTag,
      columnTag: this.source.spaceGuidMap.columnTag,
      gridTag: this.source.spaceGuidMap.gridTag,
      roomName: this.source.roomName,
      roomType: this.source.roomType,
      triggerDimension: this.source.dimension,
      triggerGuid: this.source.guid,
      deviceGuid: this.source.guid,
      deviceName: this.source.dimension === 'DEVICE' ? this.source.name : null,
      deviceLabel: this.source.deviceLabel,
      triggerStatus: this.state,
      notifyContent: this.message,
      alarmLevel: this.level,
      alarmType: this.type,
      pointCode: this.point.code,
      pointCodeName: this.point.name,
      pointValue: this.pointData.current,
      triggerCondition: this.cause,
      gmtCreate: dayjs(this.createdAt).format('YYYY-MM-DD HH:mm:ss'),
      triggerTime: dayjs(this.latestTriggeredAt).format('YYYY-MM-DD HH:mm:ss'),
      mergeCount: this.mergeCount,
      threshold: this.monitoringItem.threshold,
      triggerSnapshot: this.pointData.snapshot,
      validLimits: this.point.validLimits,
      unit: this.point.unit,
      eventId: this.eventId,
      alarmStatus: this.lifecycleState,
      gridCount: this.gridCount,
      deviceCount: this.deviceCount,
      notifyCount: this.notifyCount,
      customerCount: this.customerCount,
      itemId: this.monitoringItem.id,
      itemType: this.monitoringItem.type,
      deviceType: this.source.deviceType,
      dataType: this.point.dataType,
      confirmBy: this.confirmUser ? this.confirmUser.id : null,
      confirmByName: this.confirmUser ? this.confirmUser.name : null,
      confirmTime: this.confirmedAt ? dayjs(this.confirmedAt).format('YYYY-MM-DD HH:mm:ss') : null,
      mergeRuleId: this.mergeRuleId,
      recoverTime: this.recoveredAt ? dayjs(this.recoveredAt).format('YYYY-MM-DD HH:mm:ss') : null,
      activeTime: dayjs(this.activatedAt).format('YYYY-MM-DD HH:mm:ss'),
      changeId: this.changeId,
      alarmReason: this.reason,
      removeTime: this.removedAt ? dayjs(this.removedAt).format('YYYY-MM-DD HH:mm:ss') : null,
      removeDesc: this.removedDescriptions,
      removeBy: this.removeUser,
      expectStatus: this.expectedStatus,
      mergeStatus: this.mergeStatus,
      alarmDuration: this.alarmDuration,
      shieldBizType: this.shieldBizType,
      totalCount: this.totalCount,
    });
  }

  /**
   * 将告警生命周期状态置为“已受理”
   *
   * @returns
   */
  public accept() {
    const current = this.lifecycleState.code;
    if (current !== 'INIT' && current !== 'ACTIVE') {
      console.error(`Trying to  accept an unexpected lifecycle state alarm: `, this.toJSON());
      return;
    }

    this.lifecycleState.code = 'CONFIRMED';

    this.lifecycleState.name = this._locales.lifecycleState[this.lifecycleState.code];
  }

  /**
   * 将告警关联事件 ID，并将告警生命周期状态置为“已建单”
   *
   * @param eventId
   */
  public connectWithEvent(eventId: string) {
    this.eventId = eventId;

    this.lifecycleState.code = 'PROCESS';
    this.lifecycleState.name = this._locales.lifecycleState[this.lifecycleState.code];
  }
}

/**
 * 根据告警生命周期状态和告警状态生成一个方便判断的人造状态
 *
 * @param alarmLifecycleState
 * @param alarmState
 * @returns
 */
function buildSyntheticState(
  alarmLifecycleState: BackendAlarmLifecycleState,
  alarmState: BackendAlarmState
): SyntheticState {
  if (alarmLifecycleState === 'INIT' || alarmLifecycleState === 'ACTIVE') {
    if (alarmState === 'RECOVER') {
      return 'unaccepted-n-recovered';
    } else {
      return 'unaccepted-n-unrecovered';
    }
  }

  if (alarmState === 'RECOVER') {
    return 'accepted-n-recovered';
  } else {
    return 'accepted-n-unrecovered';
  }
}
