import type { LocaleCode } from '@teammc/react-intl';
import dayjs from 'dayjs';
import cloneDeep from 'lodash.clonedeep';

import type { BackendMonitoringData } from './backend-monitoring-data';
import { getMonitoringDataLocales } from './locales';
import type { MonitoringDataLocales } from './locales';

type SimpleUser = {
  id: number;
  name: string;
};

export type MonitoringDataJSON = {
  id: string;
  title: string;
  content: string;
  createdBy: SimpleUser;
  createdAt: number;
  modifiedAt: number | null;
};

export class MonitoringData {
  static fromApiObject(object: BackendMonitoringData) {
    const copy = cloneDeep(object);

    return new MonitoringData(
      copy.id,
      copy.articleTitle,
      copy.articleContent,
      {
        id: copy.createUserId,
        name: copy.createUserName,
      },
      dayjs(copy.gmtCreate).valueOf(),
      dayjs(copy.gmtModify).valueOf()
    );
  }

  static fromJSON(json: MonitoringDataJSON) {
    const copy = cloneDeep(json);

    return new MonitoringData(
      copy.id,
      copy.title,
      copy.content,
      copy.createdBy,
      copy.createdAt,
      copy.modifiedAt
    );
  }

  private _localeCode: LocaleCode = 'zh-CN';
  private _locales: MonitoringDataLocales;

  constructor(
    public id: string,
    public title: string,
    public content: string,
    public createdBy: SimpleUser,
    public createdAt: number,
    public modifiedAt: number
  ) {
    this._locales = getMonitoringDataLocales(this._localeCode);
  }

  public set locales(locales: MonitoringDataLocales) {
    this._locales = locales;
  }

  public get locales() {
    return this._locales;
  }

  public getFormattedCreatedAt(template = 'YYYY-MM-DD') {
    return dayjs(this.createdAt).format(template);
  }

  public getFormattedModifiedAt(template = 'YYYY-MM-DD') {
    return dayjs(this.modifiedAt).format(template);
  }

  public toApiObject(): BackendMonitoringData {
    return cloneDeep({
      id: this.id,
      articleTitle: this.title,
      articleContent: this.content,
      createUserId: this.createdBy.id,
      createUserName: this.createdBy.name,
      gmtCreate: this.getFormattedCreatedAt(),
      gmtModify: this.getFormattedModifiedAt(),
    });
  }

  public toJSON(): MonitoringDataJSON {
    return cloneDeep({
      id: this.id,
      title: this.title,
      content: this.content,
      createdBy: this.createdBy,
      createdAt: this.createdAt,
      modifiedAt: this.modifiedAt,
    });
  }
}
