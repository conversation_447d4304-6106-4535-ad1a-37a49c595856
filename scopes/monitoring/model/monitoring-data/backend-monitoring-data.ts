export type BackendMonitoringData = {
  id: string;
  articleTitle: string;
  articleContent: string;
  createUserId: number;
  createUserName: string;
  gmtCreate: string;
  gmtModify: string;
};

export enum BackendPointDataStatus {
  SocketOff = '0', // 设备通信中断
  CollectorSocketOff = '2', // 采集器通信中断
  DataException = '3', // 数据异常
  Normal = '1', // 正常
  Alarm = '31', // 点位告警
  OutOfRange = '30', // 非法值
  Warning = '32', // 点位预警
}

type BackendRealtimePointDataStatus = {
  code: BackendPointDataStatus;
  msg: string;
};

export type BackendRealtimePointData = {
  [deviceGuid: string]:
    | {
        time: number;
        blockGuid: string;
        status?: BackendRealtimePointDataStatus | null;
        pointValues: {
          [pointCode: string]:
            | {
                value: number;
                status?: BackendRealtimePointDataStatus | null;
              }
            | undefined;
        };
      }
    | undefined;
};
