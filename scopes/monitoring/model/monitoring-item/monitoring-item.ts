import shortid from 'shortid';

import type { BackendAlarmType } from '@manyun/monitoring.model.alarm';
import type { BackendTriggerRule, TriggerRuleJSON } from '@manyun/monitoring.model.trigger-rule';
import { TriggerRule, TriggerRuleType } from '@manyun/monitoring.model.trigger-rule';

import type { MonitorItemType } from './monitoring-item.type';
import { CUSTOMIZED_NOTIFICATION_TEXT } from './monitoring-item.type';

export type BackendMonitoringItem = {
  id?: number | string;
  name: string;
  alarmLevel: string;
  alarmType: BackendAlarmType | { code: BackendAlarmType; name: string };
  available: boolean;
  createIncident: boolean;
  deviceType: string;
  eventSecondCategory: string | null;
  eventSecondCategoryName?: string | null;
  eventTopCategory: string | null;
  eventTopCategoryName?: string | null;
  itemType:
    | MonitorItemType
    | {
        code: MonitorItemType;
        name: string;
      };
  notifyRule: string;
  pointCode: string;
  recoverInterval: number /**后端存的秒 */;
  triggerCount: number;
  triggerInterval: number /** 触发观察周期 */;
  pointName?: string | null;
  spaceGuid?: string | null;
  gmtCreate?: string;
  gmtModified?: string;
  lastOperator?: string;
  lastOperatorName?: string;
  triggerRules: BackendTriggerRule[] /**触发规则(前置规则、告警条件) */;
  triggerRule?: string /**后续删除 */;
  lock?: boolean;
  description?: string;
};

export type MonitoringItemJSON = {
  id?: number | string;
  name: string;
  alarmLevel: string | number;
  deviceType: string;
  triggerCount: number;
  triggerInterval: number;
  alarmType: BackendAlarmType | { code: BackendAlarmType; name: string };
  triggerRule?: string /**后续删除 */;
  itemType:
    | MonitorItemType
    | {
        code: MonitorItemType;
        name: string;
      };
  pointCode: string;
  preTriggerRules: TriggerRuleJSON[];
  triggerRules: TriggerRuleJSON[];
  recoverInterval: number /**前端存秒 */;
  enable: boolean;
  isCreateIncident: boolean;
  incidentType: string[];
  incidentTypeName: string[];
  notifyRules: NotifyRule[];
  pointName?: string | null;
  spaceGuid?: string | null;
  lock?: boolean;
  description?: string;
};

export class MonitoringItem {
  constructor(
    public name: string,
    /**
     *告警类型 预警,告警
     */
    public alarmType: BackendAlarmType | { code: BackendAlarmType; name: string },
    public alarmLevel: string,
    public deviceType: string,
    /**
     * 监控项内容
     */
    public itemType:
      | MonitorItemType
      | {
          code: MonitorItemType;
          name: string;
        },
    public pointCode: string,
    /**
     * 前置条件
     */
    public preTriggerRules: TriggerRule[],
    /**
     * 告警条件
     */
    public triggerRules: TriggerRule[],
    public triggerCount: number,
    public triggerInterval: number /** 触发观察周期 */,
    public recoverInterval: number /**前端展示秒 */,
    public enable: boolean,
    public isCreateIncident: boolean,
    /**
     * 事件类型
     */
    public incidentType: string[],
    public incidentTypeName: string[],

    /**
     * 告警通知文案
     */
    public notifyRules: NotifyRule[] /** */,

    public id?: string | number,
    /**
     * 监控点位名称
     */
    public pointName?: string | null,

    /**
     * 空间点位的spaceGuid
     */
    public spaceGuid?: string | null,
    /**
     * 规则锁
     */
    public lock?: boolean,
    /**
     * 备注
     */
    public description?: string,
    /**
     * 告警条件字符串
     */
    public alarmConditionString?: string,
    /**
     * 前置条件字符串
     */
    public preConditionString?: string
  ) {}

  static fromApiObject(backendItem: BackendMonitoringItem, metaData?: unknown[]): MonitoringItem {
    return new MonitoringItem(
      backendItem.name,
      typeof backendItem.alarmType === 'string'
        ? (backendItem.alarmType as BackendAlarmType)
        : backendItem.alarmType.code,
      backendItem.alarmLevel,
      backendItem.deviceType,
      backendItem.itemType,
      backendItem.pointCode,
      (backendItem.triggerRules ?? [])
        .filter(rule => rule.ruleType === TriggerRuleType.PreCondition)
        .map(rule => TriggerRule.fromApiObject(rule)) /**前置条件 */,
      (backendItem.triggerRules ?? [])
        .filter(rule => rule.ruleType === TriggerRuleType.AlarmCondition)
        .map(rule => TriggerRule.fromApiObject(rule)) /**告警条件 */,
      backendItem.triggerCount,
      backendItem.triggerInterval,
      backendItem.recoverInterval,
      backendItem.available,
      backendItem.createIncident,
      MonitoringItem.fromApiIncidentType(backendItem),
      MonitoringItem.fromApiIncidentTypeName(backendItem),
      MonitoringItem.fromApiNotifyRule(backendItem.notifyRule, metaData),
      backendItem.id,
      backendItem.pointName,
      backendItem.spaceGuid,
      backendItem.lock,
      backendItem.description
    );
  }

  static toApiObject(monitoringItem: Omit<MonitoringItem, 'toJSON'>): BackendMonitoringItem {
    return {
      id: monitoringItem.id as number,
      name: monitoringItem.name,
      alarmLevel: monitoringItem.alarmLevel,
      alarmType: monitoringItem.alarmType,
      available: monitoringItem.enable,
      createIncident: monitoringItem.isCreateIncident,
      eventSecondCategory: (monitoringItem?.incidentType ?? [])[1],
      eventTopCategory: (monitoringItem?.incidentType ?? [])[0],
      eventSecondCategoryName: (monitoringItem?.incidentTypeName ?? [])[1],
      eventTopCategoryName: (monitoringItem?.incidentTypeName ?? [])[0],
      deviceType: monitoringItem.deviceType,
      itemType: monitoringItem.itemType,
      triggerCount: monitoringItem.triggerCount,
      triggerInterval: monitoringItem.triggerInterval,
      recoverInterval: monitoringItem.recoverInterval,
      pointCode: monitoringItem.pointCode,
      pointName: monitoringItem.pointName,
      notifyRule: MonitoringItem.toApiNotifyRule(monitoringItem.notifyRules as []),
      triggerRules: [...monitoringItem.triggerRules, ...monitoringItem.preTriggerRules].map(
        triggerRule => TriggerRule.toApiObject(triggerRule)
      ),
      spaceGuid: monitoringItem.spaceGuid,
      lock: monitoringItem.lock,
      description: monitoringItem.description,
    };
  }

  static toApiNotifyRule(notifyRules: NotifyRule[]): string {
    return notifyRules
      .map(({ value, text }) => {
        if (value === CUSTOMIZED_NOTIFICATION_TEXT) {
          return text;
        }
        return '${' + value + '}';
      })
      .join('');
  }

  static fromApiNotifyRule(notifyRule: string, sourceData?: unknown[]) {
    if (!sourceData) {
      return [];
    }
    const fields: (string | NotifyRule)[] = [notifyRule];
    while (fields.some(item => typeof item === 'string')) {
      for (let idx = 0; idx < fields.length; idx++) {
        const item = fields[idx];
        if (typeof item !== 'string') {
          continue;
        }
        let shouldBreak = false;

        for (let index = 0; index < sourceData.length; index++) {
          const field = sourceData[index];
          const keyword = '${' + field.metaCode + '}';
          if (item.includes(keyword)) {
            const id = shortid();
            const tplItem: NotifyRule = {
              id,
              label: field.metaName,
              value: field.metaCode,
              description: field.description,
            };
            shouldBreak = true;
            const keywordIdx = item.indexOf(keyword);
            const left = item.slice(0, keywordIdx);
            const right = item.slice(keywordIdx + keyword.length);
            if (!left && right) {
              fields.splice(idx, 1, tplItem, right);
            } else if (left && !right) {
              fields.splice(idx, 1, left, tplItem);
            } else if (!left && !right) {
              fields.splice(idx, 1, tplItem);
            } else {
              fields.splice(idx, 1, left, tplItem, right);
            }
            break;
          }
        }
        if (!shouldBreak) {
          fields.splice(idx, 1, {
            id: shortid(),
            label: '自定义',
            value: CUSTOMIZED_NOTIFICATION_TEXT,
            text: item,
            description: '自定义配置告警内容',
          });
        }
        break;
      }
    }
    return fields as NotifyRule[];
  }

  static fromApiIncidentType(backendMonitorItem: BackendMonitoringItem) {
    const incidentType: string[] = [];
    if (backendMonitorItem.eventTopCategory) {
      incidentType.push(backendMonitorItem.eventTopCategory);
    }
    if (backendMonitorItem.eventSecondCategory) {
      incidentType.push(backendMonitorItem.eventSecondCategory);
    }
    return incidentType;
  }

  static fromApiIncidentTypeName(backendMonitorItem: BackendMonitoringItem) {
    const incidentType: string[] = [];
    if (backendMonitorItem.eventTopCategoryName) {
      incidentType.push(backendMonitorItem.eventTopCategoryName);
    }
    if (backendMonitorItem.eventSecondCategoryName) {
      incidentType.push(backendMonitorItem.eventSecondCategoryName);
    }
    return incidentType;
  }

  toJSON(): MonitoringItemJSON {
    const monitorItem = this;
    const monitorItemJson: unknown = {};
    Object.keys(monitorItem).forEach(key => {
      if (typeof (monitorItem as unknown)[key] !== 'function') {
        (monitorItemJson as unknown)[key] = (monitorItem as unknown)[key];
      }
    });
    return monitorItemJson as MonitoringItemJSON;
  }
}

export type NotifyRule = {
  id: string;
  label: string;
  description: string;
  value: string;
  notifyConfigMode?: string;
  pointId?: number;
  text?: string;
};
