import type { DynamicBaselineLocales } from './type';

export const zhCN: DynamicBaselineLocales = {
  id: '动态基线监测配置ID',
  blockGuid: '楼栋',
  name: '监测名称',
  alarmCondition: '监测模型',
  alarmType: '告警类型',
  alarmLevel: '告警级别',
  notifyWay: '通报渠道',
  enabled: '启用状态',
  point: '测点选择',
  areaList: '生效域配置',
  deviceType: '设备类型',
  configType: '配置范围',
  upper: '动态基线上限预警',
  lower: '动态基线下限预警',
  alarmScreen: '告警盯屏',
  sms: '短信通知',
};

export default zhCN;
