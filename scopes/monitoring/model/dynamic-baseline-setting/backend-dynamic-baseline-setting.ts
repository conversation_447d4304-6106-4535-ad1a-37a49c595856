import type { BackendAlarmType } from '@manyun/monitoring.model.alarm';

export type DynamicBaselineConfigType = 'BLOCK' | 'DEVICE' | 'ROOM';

export type BackendDynamicBaselinePointList = {
  deviceType: string;
  pointCode: string;
  pointName: string;
};

export type BackendDynamicBaselineAreaList = {
  blockGuid: string | null;
  deviceGuid: string | null;
  deviceName: string | null;
  deviceType: string | null;
  roomGuid: string | null;
  roomTag: string | null;
  targetType: string | null;
};

export type BackendDynamicBaseline = {
  id: string;
  gmtCreate: string;
  gmtModified: string;
  // 场景名称
  scenesName: string;
  // 场景类型
  scenesType: string;
  idcTag: string;
  blockGuid: string;
  // 是否启用
  enabled: boolean;
  // 通报方式
  notifyWay: string;
  // 监控模型
  alarmCondition: DynamicBaselineCondition;
  // 告警类型
  alarmType: BackendAlarmType;
  // 告警等级
  alarmLevel: number;
  // 生效域类型
  configType: DynamicBaselineConfigType;
  operatorId: number;
  operatorName: string;
  // 测点类型数
  pointCount: number;
  // 点位
  pointList: BackendDynamicBaselinePointList[];
  // 生效域
  areaList: BackendDynamicBaselineAreaList[];
};

export type BackendDynamicBaselineCreateParams = {
  name: string;
};
export type BackendDynamicBaselineListItem = {
  id: number;
};

/** 动态基线上下限 */
export type DynamicBaselineCondition = 'UPPER' | 'LOWER';

/** 通报渠道 */
export type DynamicBaselineNotifyWay = 'ALARM_SCREEN' | 'SMS';
