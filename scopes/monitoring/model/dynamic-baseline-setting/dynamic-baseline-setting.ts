import type { LocaleCode } from '@teammc/react-intl';
import cloneDeep from 'lodash.clonedeep';

import type { BackendAlarmType } from '@manyun/monitoring.model.alarm';

import type {
  BackendDynamicBaseline,
  BackendDynamicBaselineAreaList,
  BackendDynamicBaselinePointList,
  DynamicBaselineCondition,
  DynamicBaselineConfigType,
} from './backend-dynamic-baseline-setting';
import { getDynamicBaselineLocales } from './locales';
import type { DynamicBaselineLocales } from './locales';

type DynamicAlarm = {
  // 监控模型
  alarmCondition: DynamicBaselineCondition;
  // 告警类型
  alarmType: BackendAlarmType;
  // 告警等级
  alarmLevel: number;
};

export type DynamicBaselineJSON = {
  id: string;
  // 场景名称
  name: string;
  // 场景类型
  type: string;
  idcTag: string;
  blockGuid: string;
  // 是否启用
  enabled: boolean;
  // 通报方式
  notifyWay: string;
  alarm: DynamicAlarm;
  // 生效域类型
  configType: DynamicBaselineConfigType;
  operatorId: number;
  operatorName: string;
  createdAt: string;
  modifiedAt: string;
  // 测点类型数
  pointCount: number;
  // 点位
  pointList: BackendDynamicBaselinePointList[];
  // 生效域
  areaList: BackendDynamicBaselineAreaList[];
};

export class DynamicBaseline {
  static fromApiObject(object: BackendDynamicBaseline) {
    const copy = cloneDeep(object);

    return new DynamicBaseline(
      copy.id,
      copy.scenesName,
      copy.scenesType,
      copy.idcTag,
      copy.blockGuid,
      copy.enabled,
      copy.notifyWay,
      {
        alarmCondition: copy.alarmCondition,
        alarmType: copy.alarmType,
        alarmLevel: copy.alarmLevel,
      },
      copy.configType,
      copy.operatorId,
      copy.operatorName,
      copy.gmtCreate,
      copy.gmtModified,
      copy.pointCount,
      copy.pointList,
      copy.areaList
    );
  }

  static fromJSON(json: DynamicBaselineJSON) {
    const copy = cloneDeep(json);

    return new DynamicBaseline(
      copy.id,
      copy.name,
      copy.type,
      copy.idcTag,
      copy.blockGuid,
      copy.enabled,
      copy.notifyWay,
      copy.alarm,
      copy.configType,
      copy.operatorId,
      copy.operatorName,
      copy.createdAt,
      copy.modifiedAt,
      copy.pointCount,
      copy.pointList,
      copy.areaList
    );
  }

  private _localeCode: LocaleCode = 'zh-CN';
  private _locales: DynamicBaselineLocales;

  constructor(
    public id: string,
    public name: string,
    public type: string,
    public idcTag: string,
    public blockGuid: string,
    public enabled: boolean,
    public notifyWay: string,
    public alarm: DynamicAlarm,
    public configType: DynamicBaselineConfigType,
    public operatorId: number,
    public operatorName: string,
    public createdAt: string,
    public modifiedAt: string,
    public pointCount: number,
    // 点位
    public pointList: BackendDynamicBaselinePointList[],
    // 生效域
    public areaList: BackendDynamicBaselineAreaList[]
  ) {
    this._locales = getDynamicBaselineLocales(this._localeCode);
  }

  public set locales(locales: DynamicBaselineLocales) {
    this._locales = locales;
  }

  public get locales() {
    return this._locales;
  }

  public toApiObject(): BackendDynamicBaseline {
    return cloneDeep({
      id: this.id,
      gmtCreate: this.createdAt,
      gmtModified: this.modifiedAt,
      scenesName: this.name,
      scenesType: this.type,
      idcTag: this.idcTag,
      blockGuid: this.blockGuid,
      enabled: this.enabled,
      notifyWay: this.notifyWay,
      alarmCondition: this.alarm.alarmCondition,
      alarmType: this.alarm.alarmType,
      alarmLevel: this.alarm.alarmLevel,
      configType: this.configType,
      operatorId: this.operatorId,
      operatorName: this.operatorName,
      pointCount: this.pointCount,
      pointList: this.pointList,
      areaList: this.areaList,
    });
  }

  public toJSON(): DynamicBaselineJSON {
    return cloneDeep({
      id: this.id,
      createdAt: this.createdAt,
      modifiedAt: this.modifiedAt,
      name: this.name,
      type: this.type,
      idcTag: this.idcTag,
      blockGuid: this.blockGuid,
      enabled: this.enabled,
      notifyWay: this.notifyWay,
      alarm: {
        alarmCondition: this.alarm.alarmCondition,
        alarmType: this.alarm.alarmType,
        alarmLevel: this.alarm.alarmLevel,
      },
      configType: this.configType,
      operatorId: this.operatorId,
      operatorName: this.operatorName,
      pointCount: this.pointCount,
      pointList: this.pointList,
      areaList: this.areaList,
    });
  }
}
