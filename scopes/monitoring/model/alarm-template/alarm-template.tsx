import {
  BackendMonitoringItem,
  MonitoringItem,
  MonitoringItemJSON,
} from '@manyun/monitoring.model.monitoring-item';

import { AlarmTemplateType } from './alarm-template.type';

export type BackendStaticAlarmTemplate = {
  groupName: string;
  itemCount: number;
  schemeCount: number;
} & Pick<BackendAlarmTemplate, 'target'>;

export type StaticAlarmTemplate = {
  groupName: string;
  itemCount: number;
  schemeCount: number;
} & Pick<AlarmTemplate, 'target'>;

export type BackendAlarmTemplate = {
  id: number | string | undefined;
  name: string;
  type: AlarmTemplateType;
  target: string;
  available: boolean;
  monitorItemList: BackendMonitoringItem[] /**关联告警项 */;
  monitorSchemeIds?: number[] /** 关联模版组id */;
  itemCount?: number;
  description?: string;
  gmtCreate?: number | string | null;
  gmtModified?: number | string | null;
  lastOperator?: number | null;
  lastOperatorName?: string | null;
};

export type AlarmTemplateJSON = {
  id: number | string | undefined;
  type: AlarmTemplateType;
  name: string;
  target: string;
  monitorItems: MonitoringItemJSON[];
  enable: boolean;
  description?: string;
  associateGroupIds?: number[];
  gmtCreate?: number | string | null;
  gmtModified?: number | string | null;
  modifyUser?: {
    id?: number | null;
    name?: string | null;
  };
};

export class AlarmTemplate {
  constructor(
    public id: number | string | undefined,
    public type: AlarmTemplateType,
    public name: string,
    /**
     * 监控对象 (设备类型、空间 编码)
     */
    public target: string,
    public monitorItems: MonitoringItem[],
    public enable: boolean,
    public description?: string /**备注 */,
    public associateGroupIds?: number[] /**关联模版组 */,
    public gmtCreate?: number | string | null,
    public gmtModified?: number | string | null,
    public modifyUser?: {
      id?: number | null;
      name?: string | null;
    }
  ) {}

  static toApiObject(
    alarmTpl: Pick<
      AlarmTemplate,
      | 'id'
      | 'name'
      | 'target'
      | 'associateGroupIds'
      | 'description'
      | 'enable'
      | 'type'
      | 'monitorItems'
      | 'gmtCreate'
      | 'modifyUser'
      | 'gmtCreate'
      | 'gmtModified'
    >
  ): BackendAlarmTemplate {
    return {
      id: alarmTpl.id,
      name: alarmTpl.name,
      type: alarmTpl.type,
      target: alarmTpl.target,
      available: alarmTpl.enable,
      description: alarmTpl.description,
      monitorItemList: alarmTpl.monitorItems.map(item => MonitoringItem.toApiObject(item)),
      monitorSchemeIds: alarmTpl.associateGroupIds,
      gmtCreate: alarmTpl.gmtCreate,
      gmtModified: alarmTpl.gmtModified,
      lastOperator: alarmTpl.modifyUser?.id,
      lastOperatorName: alarmTpl.modifyUser?.name,
    };
  }

  static fromApiObject(backendAlarmTpl: BackendAlarmTemplate): AlarmTemplate {
    return new AlarmTemplate(
      backendAlarmTpl.id,
      backendAlarmTpl.type,
      backendAlarmTpl.name,
      backendAlarmTpl.target,
      backendAlarmTpl.monitorItemList.map(backendItem => MonitoringItem.fromApiObject(backendItem)),
      backendAlarmTpl.available,
      backendAlarmTpl.description,
      backendAlarmTpl.monitorSchemeIds,
      backendAlarmTpl.gmtCreate,
      backendAlarmTpl.gmtModified,
      {
        id: backendAlarmTpl.lastOperator,
        name: backendAlarmTpl.lastOperatorName,
      }
    );
  }

  toJSON(): AlarmTemplateJSON {
    const alarmTpl = this;
    const alarmTplJson: any = {};
    Object.keys(alarmTpl).forEach(key => {
      if (typeof (alarmTpl as any)[key] !== 'function') {
        (alarmTplJson as any)[key] = (alarmTpl as any)[key];
      }
    });
    return alarmTplJson as AlarmTemplateJSON;
  }
}
