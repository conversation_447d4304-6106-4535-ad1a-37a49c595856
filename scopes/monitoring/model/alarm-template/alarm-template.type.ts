export enum AlarmTemplateType {
  /**设备类型模板**/ Device = 'DEVICE_TYPE',
  /**实例模版 */ Instance = 'INSTANCE_TYPE',
}

export const AlarmTemplateTypeMapper = {
  [AlarmTemplateType.Device]: '设备类型模板',
  [AlarmTemplateType.Instance]: '实例模板',
};

export const AlarmTemplateTypeOptions = [
  { label: AlarmTemplateTypeMapper[AlarmTemplateType.Device], value: AlarmTemplateType.Device },
  { label: AlarmTemplateTypeMapper[AlarmTemplateType.Instance], value: AlarmTemplateType.Instance },
];
