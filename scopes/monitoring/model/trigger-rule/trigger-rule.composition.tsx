import React from 'react';

import { LimitValueMap, TriggerRuleLegacy } from './trigger-rule';

export function ReturnsReadableText() {
  return (
    <div>
      <h1>
        AI:
        {TriggerRuleLegacy.toReadableText(
          {
            lowerLimit: { operator: LimitValueMap.GREATER_THAN, limit: 10 },
            upperLimit: { operator: LimitValueMap.LESS_THAN, limit: 'NOMINAL_VOLTAGE' },
          },
          {}
        )}
      </h1>
      <h1>
        DI：
        {TriggerRuleLegacy.toReadableText([1, 2], { validLimits: ['1=闭合', '2=断开'] })}
      </h1>
    </div>
  );
}

export function ReturnsObject() {
  // console.log(TriggerRule.toObject('le=10'));
  // console.log(TriggerRule.toObject('gt=1;lt=NOMINAL_VOLTAGE'));

  return (
    <div>
      <h1>
        DI：
        {TriggerRuleLegacy.toObject('in=1,2,3')}
      </h1>
    </div>
  );
}
