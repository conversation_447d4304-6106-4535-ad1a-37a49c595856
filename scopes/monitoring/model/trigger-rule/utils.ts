// import { OrSplitSymbol, IntervalSplitSymbol } from './trigger-rule.type';
// /**
//  *
//  * @param valueData
//  *  eg: 变位告警 {"code":"ALL;1,0","name":"ALL;关闭,闭合"}
//  *  eg: 异常告警 {"code":"0,1","name":"断开,闭合"}
//  *
//  */
// export function parseTriggerRuleValueDataForDIPoint(valueData: { code: string; name: string }) {
//   let options: any[] = [];
//   const changeValue = valueData.code.split(IntervalSplitSymbol);
//   const changeName = valueData.name.split(IntervalSplitSymbol);

//   if (changeValue.length === 2) {
//     const beforeChageVal = changeValue[0];
//     const beforeChageName = changeName[0];
//     const beforeOptions = getOptions(beforeChageVal, beforeChageName);

//     const afterChangeVal = changeValue[1];
//     const afterChangeName = changeName[1];
//     const afterOptions = getOptions(afterChangeVal, afterChangeName);

//     options[0] = beforeOptions;
//     options[1] = afterOptions;
//   } else {
//     options = getOptions(valueData.code, valueData.name);
//   }
//   return options;
// }

// function getOptions(codeStr: string, nameStr: string) {
//   let options: { label: string; value: string }[] = [];
//   const codes = codeStr.split(OrSplitSymbol);
//   const names = nameStr.split(OrSplitSymbol);
//   codes.forEach((val, index) => {
//     if (names[index]) {
//       options.push({
//         label: names[index],
//         value: val,
//       });
//     }
//   });
//   return options;
// }
