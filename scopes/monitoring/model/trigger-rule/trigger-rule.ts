import get from 'lodash.get';

import { DIPoint } from '@manyun/monitoring.model.point';

import {
  ConditionType,
  DimensionType,
  DimensionTypeMapper,
  FormulaType,
  FormulaTypeMapper,
  IntervalSplitSymbol,
  OrSplitSymbol,
  OrString,
  PreInSignTypeLabel,
  SignType,
  SignTypeMapper,
  TriggerRuleType,
} from './trigger-rule.type';

export enum LimitValueMap {
  NULL = 'NULL',
  GREATER_THAN = 'gt',
  GREATER_THAN_OR_EQUALS = 'ge',
  LESS_THAN = 'lt',
  LESS_THAN_OR_EQUALS = 'le',
}

export const LimitTextMap = {
  [LimitValueMap.NULL]: '无',
  [LimitValueMap.GREATER_THAN]: '>',
  [LimitValueMap.GREATER_THAN_OR_EQUALS]: '≥',
  [LimitValueMap.LESS_THAN]: '<',
  [LimitValueMap.LESS_THAN_OR_EQUALS]: '≤',
};
export type Limit = {
  operator: LimitValueMap;
  limit: number | string;
};
type LimitType = 'lowerLimit' | 'upperLimit';
export type AITriggerRuleObject = Record<LimitType, Limit | null>;

export type DITriggerRuleObject = number[];

export type Options = {
  validLimits?: string[];
  unit?: string | null;
  specMappings?: Record<string, object>;
};

/**
 * 尝试修正监控项阈值的数据类型
 *
 * - 如果阈值为数字字符串，则转换成数字
 * - 反之，直接返回传入的阈值
 */
function tryFixLimitType(limit: string): string {
  const limitNumber = Number(limit);
  // 如果不是数字，则认为是规格编码字符串
  if (Number.isNaN(limitNumber)) {
    return limit;
  }
  return limitNumber.toString();
}

function handleLimitStr(
  obj: AITriggerRuleObject,
  limitStr: string,
  type: LimitType
): AITriggerRuleObject {
  const [operator, limit] = limitStr.split('=');
  obj[type] = { operator: operator as LimitValueMap, limit: tryFixLimitType(limit) };

  return obj;
}

/**
 * 处理监控项阈值文案（如果是规格编码，会尝试将规格编码转换成规格名称）
 *
 * @param {string|number} limit 监控项配置的阈值上限或下限
 * @param {Record<string, object>|undefined} specMappings 某个设备类型下的规格映射
 * @returns {string|number}
 */
function getLimitText(
  limit: Limit['limit'],
  specMappings?: Record<string, object>
): string | number {
  if (typeof limit == 'number') {
    return limit;
  }

  const spec = specMappings?.[limit];
  return !spec ? limit : get(spec, 'specName');
}
export class TriggerRuleLegacy {
  constructor(public triggerRule: string) {}

  /**
   * 将字符串处理成对应的TriggerRuleObject对象
   */
  static toObject(triggerRule: string) {
    // DI
    // example: in=1,2,3
    if (triggerRule.startsWith('in=')) {
      const [, limitStr] = triggerRule.split('=');
      return limitStr.split(',').map(Number); // DI 量的取值范围是数字
    }
    // AI
    const resultObject: AITriggerRuleObject = {
      lowerLimit: null,
      upperLimit: null,
    };
    if (!triggerRule.includes(';')) {
      // 只配置了下限
      if (
        triggerRule.includes(LimitValueMap.GREATER_THAN) ||
        triggerRule.includes(LimitValueMap.GREATER_THAN_OR_EQUALS)
      ) {
        return handleLimitStr(resultObject, triggerRule, 'lowerLimit');
      }
      // 只配置了上限
      else if (
        triggerRule.includes(LimitValueMap.LESS_THAN) ||
        triggerRule.includes(LimitValueMap.LESS_THAN_OR_EQUALS)
      ) {
        return handleLimitStr(resultObject, triggerRule, 'upperLimit');
      }
    } else {
      const [lowerLimitStr, upperLimitStr] = triggerRule.split(';');
      const newObj = handleLimitStr(resultObject, lowerLimitStr, 'lowerLimit');
      return handleLimitStr(newObj, upperLimitStr, 'upperLimit');
    }
  }

  /**
   * 将TriggerRuleObject对象处理成用户可读的字符串
   */
  static toReadableText<T extends AITriggerRuleObject | DITriggerRuleObject | null>(
    limits: T,
    { validLimits, unit, specMappings }: Options
  ): string {
    if (limits === null) {
      return '--';
    }
    if (Array.isArray(limits)) {
      if (Array.isArray(validLimits)) {
        return limits
          .map(limit => DIPoint.getValidLimitText(limit.toString(), validLimits))
          .join(',');
      }
      return limits.join(',');
    }
    const { lowerLimit, upperLimit } = limits as AITriggerRuleObject;
    let limitStr = '';
    if (lowerLimit) {
      const limitTxt = getLimitText(lowerLimit.limit, specMappings);
      limitStr += LimitTextMap[lowerLimit.operator] + limitTxt + (unit || '');
    }
    if (limitStr !== '') {
      limitStr += ',';
    }
    if (upperLimit) {
      const limitTxt = getLimitText(upperLimit.limit, specMappings);
      limitStr += LimitTextMap[upperLimit.operator] + limitTxt + (unit || '');
    }

    return limitStr;
  }
}

/** @yjx 由于220530 不实现测点走势图变更，因此原来 解析triggerRule字符串的逻辑暂不删除，后续修改测点走势图变更 */

export type ValueData = {
  code: string;
  name: string;
  value?: string;
};

export type BackendTriggerRule = {
  id: number | string;
  deviceType: string;
  pointCode: string;
  ruleType: TriggerRuleType /**规则类型：告警条件、前置条件 */;
  condition: ConditionType;
  dimension: DimensionType | { code: DimensionType; name: string };
  formulaType: FormulaType;
  sign: SignType;
  valueData: ValueData;
  pointName: string;
  formula?: string /** 表达式类型 */;
  itemId?: number /**所属监控项id */;
  threshold?: string;
  unit?: string /*单位 */;
};

export type TriggerRuleJSON = {
  id: number;
  type: TriggerRuleType;
  deviceType: string;
  pointCode: string;
  formulaType: FormulaType;
  sign: SignType;
  dimension: DimensionType | { code: DimensionType; name: string };
  valueData: ValueData;
  condition?: ConditionType;
  formula?: string;
  itemId?: number;
  threshold?: string;
  unit?: string;
  pointName: string;
};

export class TriggerRule {
  constructor(
    public id: number | string,
    public type: TriggerRuleType,
    public deviceType: string,
    public pointCode: string,
    public pointName: string,
    public formulaType: FormulaType,
    public sign: SignType,
    /**
     *  触发维度，默认值： DimensionType.Device
     */
    public dimension: DimensionType | { code: DimensionType; name: string },

    public valueData: ValueData,
    /**
     * 触发条件 -  和符号存在隐藏关联关系
     */
    public condition?: ConditionType,
    public formula?: string,
    public itemId?: number,
    public threshold?: string,
    public unit?: string
  ) {}

  static toApiObject(triggerRule: TriggerRule): BackendTriggerRule {
    return {
      id: triggerRule.id,
      deviceType: triggerRule.deviceType,
      pointCode: triggerRule.pointCode,
      ruleType: triggerRule.type,
      condition: TriggerRule.toApiCondition(triggerRule.sign),
      dimension: triggerRule.dimension,
      formulaType: triggerRule.formulaType,
      sign: triggerRule.sign,
      valueData: triggerRule.valueData,
      pointName: triggerRule.pointName,
      formula: triggerRule.formula,
      itemId: triggerRule.itemId,
      threshold: triggerRule.threshold,
      unit: triggerRule.unit,
    };
  }

  static fromApiObject(backendObj: BackendTriggerRule): TriggerRule {
    return new TriggerRule(
      backendObj.id,
      backendObj.ruleType,
      backendObj.deviceType,
      backendObj.pointCode,
      backendObj.pointName,
      backendObj.formulaType,
      backendObj.sign,
      typeof backendObj.dimension === 'object' ? backendObj.dimension.code : backendObj.dimension,
      backendObj.valueData,
      backendObj.condition,
      backendObj.formula,
      backendObj.itemId,
      backendObj.threshold,
      backendObj.unit
    );
  }

  static toApiCondition(singType: SignType): ConditionType {
    let condition: ConditionType = ConditionType.Lower;
    switch (singType) {
      case SignType.GreaterThan:
      case SignType.GreaterThanEqual:
        condition = ConditionType.Upper;
        break;
      case SignType.LessThan:
      case SignType.LessThanEqual:
        condition = ConditionType.Lower;
        break;
      case SignType.IncreaseThan:
      case SignType.RiseThan:
        condition = ConditionType.ContinuousRise;
        break;
      case SignType.DecreaseThan:
      case SignType.ReduceThan:
        condition = ConditionType.ContinuousRise;
        break;
      case SignType.In:
        condition = ConditionType.Abnormal;
        break;
      case SignType.Change:
        condition = ConditionType.Change;
        break;
    }
    return condition;
  }

  toJSON(): TriggerRuleJSON {
    const triggerRule = this;
    const triggerRuleJson: any = {};
    Object.keys(triggerRule).forEach(key => {
      if (typeof (triggerRule as any)[key] !== 'function') {
        (triggerRuleJson as any)[key] = (triggerRule as any)[key];
      }
    });
    return triggerRuleJson as TriggerRuleJSON;
  }

  /**
   * 将规则对象转换成文案
   */
  static toJSONString(triggerRule: TriggerRuleJSON): string {
    // const triggerRule = this;
    let jsonStr = '';
    const unit = triggerRule.unit ? triggerRule.unit : '';
    let text = '';
    switch (triggerRule.type) {
      case TriggerRuleType.AlarmCondition /**告警条件 */:
        switch (triggerRule.formulaType) {
          case FormulaType.Continuous /**连续 */:
            text = triggerRule?.valueData?.name ?? '';
            jsonStr = `${FormulaTypeMapper[FormulaType.Continuous]}${
              SignTypeMapper[triggerRule.sign]
            }${text}${unit}`;
            break;
          case FormulaType.Custom /**自定义值 */:
            text = triggerRule?.valueData?.name ?? '';
            jsonStr = `${SignTypeMapper[triggerRule.sign]}${text}${unit}`;
            break;
          case FormulaType.Rated:
          case FormulaType.Point /**额定值、测点值 */:
            text = triggerRule.valueData?.name ?? '';
            jsonStr = `${SignTypeMapper[triggerRule.sign]}${text}`;
            break;
          case FormulaType.Status /**异常告警 */:
            text = (triggerRule.valueData?.name ?? '').split(OrSplitSymbol).join(OrString);
            jsonStr = text;
            break;
          case FormulaType.Change /**变位告警 */:
            const changeArr = (triggerRule.valueData?.name ?? '').split(IntervalSplitSymbol);
            let preValue, nextValue;
            if (changeArr[0]) {
              preValue = changeArr[0].split(OrSplitSymbol).join(OrString);
            }
            if (changeArr[1]) {
              nextValue = changeArr[1].split(OrSplitSymbol).join(OrString);
            }
            if (preValue && nextValue) {
              jsonStr = `从${preValue}变为${nextValue}`;
            }
            break;
        }
        break;
      case TriggerRuleType.PreCondition /**前置条件 */:
        if (triggerRule.sign === SignType.In) {
          /**状态为(固定) */
          text = triggerRule.valueData.name.split(OrSplitSymbol).join(OrString);
        } else {
          text = triggerRule.valueData.name;
        }
        const dimensionName =
          typeof triggerRule.dimension === 'string'
            ? DimensionTypeMapper[triggerRule.dimension]
            : DimensionTypeMapper[triggerRule.dimension.code];
        jsonStr = `${dimensionName}${triggerRule.pointName ?? ''}${
          triggerRule.sign === SignType.In ? PreInSignTypeLabel : SignTypeMapper[triggerRule.sign]
        }${text}`;
        if (triggerRule.formulaType === FormulaType.Custom && triggerRule.unit) {
          jsonStr += jsonStr = `${triggerRule.unit}`;
        }
        /**dimension + ponitName + sign + value + unit*/
        break;
    }
    return jsonStr;
  }
}
