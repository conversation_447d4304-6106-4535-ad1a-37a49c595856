/**告警规则类型 */
export enum TriggerRuleType {
  /**前置条件 */ PreCondition = 'PRECONDITION',
  /**告警条件 */ AlarmCondition = 'ALARM_CONDITION',
}

/** 表达式类型 枚举 */
export enum FormulaType {
  /**自定义 */ Custom = 'CUSTOM',
  /**额定值 */ Rated = 'RATED',
  /**测点值 */ Point = 'POINT',
  /**连续 */ Continuous = 'CONTINUOUS',
  /**状态值   DI 异常告警*/ Status = 'STATUS',
  /**变位  DI 变位告警 */ Change = 'CHANGE',
}

export const FormulaTypeMapper: Record<string, string> = {
  [FormulaType.Custom]: '自定义',
  [FormulaType.Rated]: '额定值',
  [FormulaType.Point]: '测点值',
  [FormulaType.Continuous]: '连续',
  [FormulaType.Status]: '异常告警',
  [FormulaType.Change]: '变位告警',
};

/** 符号枚举 */
export enum SignType {
  /** " > ", "＞" */ GreaterThan = 'GREATER_THAN',
  /** " >= ", "≥" */ GreaterThanEqual = 'GREATER_THAN_EQUAL',
  /**" < ", "＜" */ LessThan = 'LESS_THAN',
  /**" <= ", "≤" */ LessThanEqual = 'LESS_THAN_EQUAL',
  /** ==, = */ Equal = 'EQUAL',
  /**" != ", "≠" */ NotEqual = 'NOT_EQUAL',
  /**" in ","状态为" */ In = 'IN',
  /**" CV != PV ","变为" */ Change = 'CHANGE',
  /**" (CV-PV)/PV>= ", "增幅≥" */ IncreaseThan = 'INCREASE_THAN',
  /**" (PV-CV)/PV>= ", "降幅≥" */ DecreaseThan = 'DECREASE_THAN',
  /**" CV-PV>= ", "升高≥"*/ RiseThan = 'RISE_THAN',
  /**" PV-CV>= ", "降低≥" */ ReduceThan = 'REDUCE_THAN',
}

export const SignTypeMapper: Record<string, string> = {
  [SignType.GreaterThan]: '>',
  [SignType.GreaterThanEqual]: '≥',
  [SignType.LessThan]: '<',
  [SignType.LessThanEqual]: '≤',
  [SignType.Equal]: '=',
  [SignType.NotEqual]: '≠',
  [SignType.In]: '异常状态为' /**告警条件显示：异常状态为; 前置条件显示：状态为 */,
  [SignType.Change]: '变为',
  [SignType.IncreaseThan]: '增幅≥',
  [SignType.DecreaseThan]: '降幅≥',
  [SignType.RiseThan]: '升高≥',
  [SignType.ReduceThan]: '降低≥',
};

export const signTypeFormulaMap: Record<SignType, string> = {
  [SignType.GreaterThan]: '>',
  [SignType.GreaterThanEqual]: '>=',
  [SignType.LessThan]: '<',
  [SignType.LessThanEqual]: '<=',
  [SignType.Equal]: '==',
  [SignType.NotEqual]: '!=',
  [SignType.In]: 'in',
  [SignType.Change]: 'CV != PV',
  [SignType.IncreaseThan]: '(CV-PV)/PV >=',
  [SignType.DecreaseThan]: '(PV-CV)/PV >=',
  [SignType.RiseThan]: 'CV-PV >=',
  [SignType.ReduceThan]: 'PV-CV >=',
};

/**前置条件告警显示 状态为*/
export const PreInSignTypeLabel = '状态为';

/** 触发条件枚举 */
export enum ConditionType {
  /**超上限*/ Upper = 'UPPER',
  /**超下限*/ Lower = 'LOWER',
  /**连续上升*/ ContinuousRise = 'CONTINUOUS_RISE',
  /**连续下降*/ ContinuousDecline = 'CONTINUOUS_DECLINE',
  /**状态异常*/ Abnormal = 'ABNORMAL',
  /**状态变化 */ Change = 'CHANGE',
}

export const ConditionTypeMapper: Record<string, string> = {
  [ConditionType.Upper]: '超上限',
  [ConditionType.Lower]: '超下限',
  [ConditionType.ContinuousRise]: '连续上升',
  [ConditionType.ContinuousDecline]: '连续下降',
  [ConditionType.Abnormal]: '状态异常',
  [ConditionType.Change]: '状态变化',
};

/** 维度枚举 */
export enum DimensionType {
  /**同设备  */ Device = 'DEVICE',
  /**同机柜 */ Grid = 'GRID',
  /**同机列 */ Cloumn = 'COLUMN',
  /**同包间 */ Room = 'ROOM',
  /**同楼栋 */ Block = 'BLOCK',
  /**同机房 */ Idc = 'IDC',
}

export const DimensionTypeMapper: Record<string, string> = {
  [DimensionType.Device]: '同设备',
  [DimensionType.Grid]: '同机柜',
  [DimensionType.Cloumn]: '同机列',
  [DimensionType.Room]: '同包间',
  [DimensionType.Block]: '同楼栋',
  [DimensionType.Idc]: '同机房',
};

export const OrSplitSymbol = ','; /**或 拆分符号 */
export const OrString = ' 或 ';
export const IntervalSplitSymbol = ';'; /**状态间隔拆分符号 从【】变位【】 */
export const AndString = ' 且 ';
