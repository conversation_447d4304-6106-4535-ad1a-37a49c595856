import type { LocaleCode } from '@teammc/react-intl';
import dayjs from 'dayjs';
import cloneDeep from 'lodash.clonedeep';

import type {
  BackendChannelConfig,
  ChannelCommunicationStatus,
  ChannelConfigProtocol,
  ChannelStatus,
} from './backend-channel-config';
import { getChannelConfigLocales } from './locales';
import type { ChannelConfigLocales } from './locales';

export type Device = {
  guid: string | null;
  name: string | null;
  type: string | null;
  num: string | null;
};

export type ChangeType = {
  name: string;
  code: string;
};

export type Config = {
  operatorType: {
    name: string;
    code: string;
  };
  registerNum: string;
};

export type Node = {
  name: string | null;
  id: string | null;
};

export type ChannelConfigJSON = {
  id: string;
  name: string;
  ip: string;
  port: string;
  protocol: ChannelConfigProtocol;
  blockGuid: string;
  roomTag: string;
  device: Device;
  channelType: ChangeType;
  channelStatus: ChannelStatus;
  channelCommunicationStatus: ChannelCommunicationStatus;
  config: Config[] | null;
  operator: {
    id: number;
    name: string;
  };
  password: string;
  username: string;
  vendor: string | null;
  node: Node;
  createTime: number;
  modifiedTime: number | null;
};

export class ChannelConfig {
  static fromApiObject(object: BackendChannelConfig) {
    const copy = cloneDeep(object);

    return new ChannelConfig(
      copy.id,
      copy.name,
      copy.ip,
      copy.port,
      copy.protocol,
      copy.blockGuid,
      copy.roomTag,
      {
        name: copy.deviceName,
        guid: copy.deviceGuid,
        type: copy.deviceType,
        num: copy.deviceNum,
      },
      copy.channelType,
      copy.channelStatus,
      copy.channelCommunicationStatus,
      copy.config ? JSON.parse(copy.config) : null,
      {
        id: copy.operatorId,
        name: copy.operatorName,
      },
      copy.password,
      copy.username,
      copy.vendor,
      {
        id: copy.nodeId,
        name: copy.nodeName,
      },
      dayjs(copy.gmtCreate).valueOf(),
      copy.gmtCreate ? dayjs(copy.gmtModified).valueOf() : null
    );
  }

  static fromJSON(json: ChannelConfigJSON) {
    const copy = cloneDeep(json);

    return new ChannelConfig(
      copy.id,
      copy.name,
      copy.ip,
      copy.port,
      copy.protocol,
      copy.blockGuid,
      copy.roomTag,
      copy.device,
      copy.channelType,
      copy.channelStatus,
      copy.channelCommunicationStatus,
      copy.config,
      copy.operator,
      copy.password,
      copy.username,
      copy.vendor,
      copy.node,
      copy.createTime,
      copy.modifiedTime
    );
  }

  private _localeCode: LocaleCode = 'zh-CN';
  private _locales: ChannelConfigLocales;

  constructor(
    public id: string,
    public name: string,
    public ip: string,
    public port: string,
    public protocol: ChannelConfigProtocol,
    public blockGuid: string,
    public roomTag: string,
    public device: Device,
    public channelType: ChangeType,
    public channelStatus: ChannelStatus,
    public channelCommunicationStatus: ChannelCommunicationStatus,
    public config: Config[] | null,
    public operator: {
      id: number;
      name: string;
    },
    public password: string,
    public username: string,
    public vendor: string | null,
    public node: Node,
    public createTime: number,
    public modifiedTime: number | null
  ) {
    this._locales = getChannelConfigLocales(this._localeCode);
  }

  public set locales(locales: ChannelConfigLocales) {
    this._locales = locales;
  }

  public get locales() {
    return this._locales;
  }

  public getFormattedCreateTime(template = 'YYYY-MM-DD HH:mm:ss') {
    return dayjs(this.createTime).format(template);
  }

  public getFormattedModifiedTime(template = 'YYYY-MM-DD HH:mm:ss') {
    return dayjs(this.modifiedTime).format(template);
  }

  public toApiObject(): BackendChannelConfig {
    return cloneDeep({
      id: this.id,
      name: this.name,
      ip: this.ip,
      port: this.port,
      protocol: this.protocol,
      blockGuid: this.blockGuid,
      roomTag: this.roomTag,
      deviceGuid: this.device.guid,
      deviceName: this.device.name,
      deviceType: this.device.type,
      deviceNum: this.device.name,
      channelType: {
        name: this.channelType.name,
        code: this.channelType.code,
      },
      channelStatus: {
        name: this.channelStatus.name,
        code: this.channelStatus.code,
      },
      channelCommunicationStatus: {
        name: this.channelCommunicationStatus.name,
        code: this.channelCommunicationStatus.code,
      },
      config: JSON.stringify(this.config),
      operatorName: this.operator.name,
      operatorId: this.operator.id,
      password: this.password,
      username: this.username,
      vendor: this.vendor,
      nodeId: this.node.id,
      nodeName: this.node.name,
      gmtCreate: dayjs(this.createTime).format('YYYY-MM-DD HH:mm:ss'),
      gmtModified: this.modifiedTime
        ? dayjs(this.modifiedTime).format('YYYY-MM-DD HH:mm:ss')
        : null,
    });
  }

  public toJSON(): ChannelConfigJSON {
    return cloneDeep({
      id: this.id,
      name: this.name,
      ip: this.ip,
      port: this.port,
      protocol: this.protocol,
      blockGuid: this.blockGuid,
      roomTag: this.roomTag,
      device: this.device,
      channelType: this.channelType,
      channelStatus: this.channelStatus,
      channelCommunicationStatus: this.channelCommunicationStatus,
      config: this.config,
      operator: this.operator,
      password: this.password,
      username: this.username,
      vendor: this.vendor,
      node: this.node,
      createTime: this.createTime,
      modifiedTime: this.modifiedTime,
    });
  }
}
