export type ChannelStatus = {
  name: string;
  code: 'ON' | 'OFF';
};

export type ChannelCommunicationStatus = {
  name: string;
  code: 'ONLINE' | 'OFFLINE' | 'ERR';
};

export type ChannelConfigProtocol = 'MODBUS_TCP' | 'MODBUS_TCP_SYNC' | 'ODCC' | 'B' | 'B1';

export type BackendChannelConfig = {
  id: string;
  name: string;
  ip: string;
  port: string;
  protocol: ChannelConfigProtocol;
  blockGuid: string;
  roomTag: string;
  deviceGuid: string | null;
  deviceName: string | null;
  deviceType: string | null;
  deviceNum: string | null;
  channelType: {
    name: string;
    code: string;
  };
  channelStatus: ChannelStatus;
  channelCommunicationStatus: ChannelCommunicationStatus;
  config: string | null;
  operatorName: string;
  operatorId: number;
  password: string;
  username: string;
  nodeId: string | null;
  nodeName: string | null;
  vendor: string | null;
  gmtCreate: string;
  gmtModified: string | null;
};
