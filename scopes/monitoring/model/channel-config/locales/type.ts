export type ChannelConfigLocales = {
  id: string;
  name: string;
  ip: string;
  port: string;
  protocol: string;
  blockGuid: string;
  roomTag: string;
  device: {
    __self: string;
    name: string;
    guid: string;
    type: string;
    num: string;
  };
  channelType: {
    __self: string;
    name: string;
    code: string;
  };
  channelStatus: {
    __self: string;
    ON: string;
    OFF: string;
  };
  channelCommunicationStatus: {
    __self: string;
    ONLINE: string;
    OFFLINE: string;
    ERR: string;
  };
  operator: {
    __self: string;
    id: string;
    name: string;
  };
  password: string;
  username: string;
  vendor: string;
  createTime: string;
  modifiedTime: string;
};
