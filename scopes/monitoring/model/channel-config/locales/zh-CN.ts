import type { ChannelConfigLocales } from './type';

export const zhCN: ChannelConfigLocales = {
  id: '通道ID',
  name: '通道名称',
  ip: '通道IP',
  port: '通道端口',
  protocol: '协议类型',
  blockGuid: '位置',
  roomTag: '包间',
  device: {
    __self: '设备',
    name: '通信设备',
    guid: '设备guid',
    type: '通信分类',
    num: '包含设备数',
  },
  channelType: {
    __self: '通道类型',
    name: '通道类型名称',
    code: '通道类型code',
  },
  channelStatus: {
    __self: '状态',
    ON: '已启用',
    OFF: '未启用',
  },
  channelCommunicationStatus: {
    __self: '通信状态',
    ONLINE: '通信正常',
    OFFLINE: '通信断开',
    ERR: '通信异常',
  },
  operator: {
    __self: '操作人',
    id: '操作人id',
    name: '操作人名称',
  },
  password: '密码',
  username: '用户名',
  vendor: '厂商型号',
  createTime: '创建时间',
  modifiedTime: '修改时间',
};

export default zhCN;
