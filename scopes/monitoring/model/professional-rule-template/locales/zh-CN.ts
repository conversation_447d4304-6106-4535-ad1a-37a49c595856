import type { ProfessionalRuleTemplateLocales } from './type';

export const zhCN: ProfessionalRuleTemplateLocales = {
  id: '模版 ID',
  name: '规则名称',
  bizCode: '业务分类',
  ruleCode: '规则类型',
  deviceType: '设备类型',
  triggerDesc: '触发条件配置说明',
  preConditionDesc: '前置条件配置说明',
  triggerInterval: '触发时长',
  recoverInterval: '恢复时长',
  changeTemplateId: '变更工单模版',
  scope: '适用范围',
  description: '规则介绍',
  energyPointDesc: '能效测点配置说明',
  instanceNum: '规则实例数量',
};

export default zhCN;
