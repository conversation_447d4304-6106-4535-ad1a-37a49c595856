import type { LocaleCode } from '@teammc/react-intl';
import cloneDeep from 'lodash.clonedeep';

import type { BackendProfessionalRuleTemplate } from './backend-professional-rule-template';
import { getProfessionalRuleTemplateLocales } from './locales';
import type { ProfessionalRuleTemplateLocales } from './locales';

export type ProfessionalRuleTemplateJSON = {
  /** 主键 id */
  id: number;
  /** 名称 */
  name: string;
  /** 业务分类 code */
  bizCode: string;
  /** 规则类型 code */
  ruleCode: string;
  /** 设备类型 */
  deviceType: string;
  /** 触发条件配置说明 */
  triggerDesc: string;
  /** 前置条件配置说明 */
  preConditionDesc?: string;
  /** 触发时长间隔，单位秒 */
  triggerInterval: number;
  /** 恢复观察间隔，单位秒 */
  recoverInterval: number;
  /** 变更模版 id */
  changeTemplateId: string;
  /** 适用范围 */
  scope?: string;
  /** 规则描述 */
  description: string;
  /** 能效测点配置说明 */
  energyPointDesc?: string;
  /** 实例数量 */
  instanceNum?: number;
};

export class ProfessionalRuleTemplate {
  static fromApiObject(object: BackendProfessionalRuleTemplate) {
    const copy = cloneDeep(object);

    return new ProfessionalRuleTemplate(
      copy.id,
      copy.name,
      copy.bizCode,
      copy.ruleCode,
      copy.deviceType,
      copy.triggerDesc,
      copy.preConditionDesc,
      copy.triggerInterval,
      copy.recoverInterval,
      copy.changeTemplateId,
      copy.scope,
      copy.description,
      copy.energyPointDesc,
      copy.instanceNum
    );
  }

  static fromJSON(json: ProfessionalRuleTemplateJSON) {
    const copy = cloneDeep(json);

    return new ProfessionalRuleTemplate(
      copy.id,
      copy.name,
      copy.bizCode,
      copy.ruleCode,
      copy.deviceType,
      copy.triggerDesc,
      copy.preConditionDesc ?? null,
      copy.triggerInterval,
      copy.recoverInterval,
      copy.changeTemplateId,
      copy.scope ?? null,
      copy.description,
      copy.energyPointDesc ?? null,
      copy.instanceNum ?? null
    );
  }

  private _localeCode: LocaleCode = 'zh-CN';
  private _locales: ProfessionalRuleTemplateLocales;

  constructor(
    public id: number,
    public name: string,
    public bizCode: string,
    public ruleCode: string,
    public deviceType: string,
    public triggerDesc: string,
    public preConditionDesc: string | null,
    public triggerInterval: number,
    public recoverInterval: number,
    public changeTemplateId: string,
    public scope: string | null,
    public description: string,
    public energyPointDesc: string | null,
    public instanceNum: number | null
  ) {
    this._locales = getProfessionalRuleTemplateLocales(this._localeCode);
  }

  public set locales(locales: ProfessionalRuleTemplateLocales) {
    this._locales = locales;
  }

  public get locales() {
    return this._locales;
  }

  public toApiObject(): BackendProfessionalRuleTemplate {
    return cloneDeep({
      id: this.id,
      name: this.name,
      bizCode: this.bizCode,
      ruleCode: this.ruleCode,
      deviceType: this.deviceType,
      triggerDesc: this.triggerDesc,
      preConditionDesc: this.preConditionDesc,
      triggerInterval: this.triggerInterval,
      recoverInterval: this.recoverInterval,
      changeTemplateId: this.changeTemplateId,
      scope: this.scope,
      description: this.description,
      energyPointDesc: this.energyPointDesc,
      instanceNum: this.instanceNum,
    });
  }

  public toJSON(): ProfessionalRuleTemplateJSON {
    return cloneDeep({
      id: this.id,
      name: this.name,
      bizCode: this.bizCode,
      ruleCode: this.ruleCode,
      deviceType: this.deviceType,
      triggerDesc: this.triggerDesc,
      preConditionDesc: this.preConditionDesc ?? undefined,
      triggerInterval: this.triggerInterval,
      recoverInterval: this.recoverInterval,
      changeTemplateId: this.changeTemplateId,
      scope: this.scope ?? undefined,
      description: this.description,
      energyPointDesc: this.energyPointDesc ?? undefined,
      instanceNum: this.instanceNum ?? undefined,
    });
  }
}
