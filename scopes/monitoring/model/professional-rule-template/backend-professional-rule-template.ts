export type BackendProfessionalRuleTemplate = {
  /** 主键 id */
  id: number;
  /** 名称 */
  name: string;
  /** 业务分类 code */
  bizCode: string;
  /** 规则类型 code */
  ruleCode: string;
  /** 设备类型 */
  deviceType: string;
  /** 触发条件配置说明 */
  triggerDesc: string;
  /** 前置条件配置说明 */
  preConditionDesc: string | null;
  /** 触发时长间隔，单位秒 */
  triggerInterval: number;
  /** 恢复观察间隔，单位秒 */
  recoverInterval: number;
  /** 变更模版 id */
  changeTemplateId: string;
  /** 适用范围 */
  scope: string | null;
  /** 规则描述 */
  description: string;
  /** 能效测点配置说明 */
  energyPointDesc: string | null;
  /** 实例数量 */
  instanceNum: number | null;
};
