export type ProfessionalRuleLocales = {
  id: string;
  schemeId: string;
  name: string;
  location: string;
  blockGuid: string;
  roomTag: string;
  device: {
    __self: string;
    guid: string;
    name: string;
    type: string;
  };
  bizCode: string;
  ruleCode: string;
  available: {
    __self: string;
    true: string;
    false: string;
  };
  availableForTable: {
    __self: string;
    true: string;
    false: string;
  };
  triggerInterval: string;
  triggerCount: string;
  recoverInterval: string;
  energyDevice: string;
  energyPoint: string;
  preConditions: string;
  triggerConditions: string;
};
