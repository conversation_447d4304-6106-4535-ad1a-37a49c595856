import type { ProfessionalRuleLocales } from './type';

export const zhCN: ProfessionalRuleLocales = {
  id: '规则 ID',
  schemeId: '规则模版',
  name: '规则名称',
  location: '位置',
  blockGuid: '楼栋',
  roomTag: '所在包间',
  device: {
    __self: '适用设备',
    guid: '设备 GUID',
    name: '设备名称',
    type: '设备类型',
  },
  bizCode: '业务分类',
  ruleCode: '规则类型',
  available: {
    __self: '是否开启',
    true: '开启',
    false: '关闭',
  },
  availableForTable: {
    __self: '规则状态',
    true: '已启用',
    false: '未启用',
  },
  triggerInterval: '触发时长',
  triggerCount: '累计触发次数',
  recoverInterval: '恢复时长',
  energyDevice: '能效设备',
  energyPoint: '能效测点',
  preConditions: '前置条件',
  triggerConditions: '触发条件',
};

export default zhCN;
