export type BackendProfessionalRule = {
  /** 主键 ID */
  id: number;
  /** 专家规则模版 ID */
  schemeId: number;
  /** 名称 */
  name: string;
  /** 楼栋 */
  blockGuid: string;
  /** 包间 */
  roomTag: string;
  /** 设备 guid */
  deviceGuid: string;
  /** 设备名称	 */
  deviceName: string;
  /** 业务分类 code */
  bizCode: string;
  /** 规则类型 code */
  ruleCode: string;
  /** 是否启用 */
  available: boolean;
  /** 触发时长，单位秒 */
  triggerInterval: number;
  /** 累计触发次数 */
  triggerCount: number;
  /** 恢复时长，单位秒 */
  recoverInterval: number;
};

/** 专家规则详细 */
export type BackendProfessionalRuleDetail = BackendProfessionalRule & {
  /** 能效设备 guid */
  energyDevice: string | null;
  /** 能效设备类型 */
  energyDeviceType: string | null;
  /** 能效测点 code */
  energyPoint: string | null;
  /** 规则表达式：后端所需 */
  ruleFormula: string;
  /** 规则描述：前端回填 */
  ruleJson: string;
};
