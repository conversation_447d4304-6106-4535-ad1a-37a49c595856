import type { LocaleCode } from '@teammc/react-intl';
import cloneDeep from 'lodash.clonedeep';

import { FormulaType, SignType } from '@manyun/monitoring.model.trigger-rule';
import type { TreePoint } from '@manyun/resource-hub.ui.resource-tree';

import type {
  BackendProfessionalRule,
  BackendProfessionalRuleDetail,
} from './backend-professional-rule';
import { getProfessionalRuleLocales } from './locales';
import type { ProfessionalRuleLocales } from './locales';

export type Device = {
  /** 设备 guid */
  guid: string;
  /** 设备名称	 */
  name: string;
};

export type ProfessionalRuleJSON = {
  /** 主键 ID */
  id: number;
  /** 专家规则模版 ID */
  schemeId: number;
  /** 名称 */
  name: string;
  /** 楼栋 */
  blockGuid: string;
  /** 包间 */
  roomTag: string;
  /** 设备 */
  device: Device;
  /** 业务分类 code */
  bizCode: string;
  /** 规则类型 code */
  ruleCode: string;
  /** 是否启用 */
  available: boolean;
  /** 触发时长，单位秒 */
  triggerInterval: number;
  /** 累计触发次数 */
  triggerCount: number;
  /** 恢复时长，单位秒 */
  recoverInterval: number;
};

export class ProfessionalRule {
  static fromApiObject(object: BackendProfessionalRule) {
    const copy = cloneDeep(object);

    return new ProfessionalRule(
      copy.id,
      copy.schemeId,
      copy.name,
      copy.blockGuid,
      copy.roomTag,
      {
        guid: copy.deviceGuid,
        name: copy.deviceName,
      },
      copy.bizCode,
      copy.ruleCode,
      copy.available,
      copy.triggerInterval,
      copy.triggerCount,
      copy.recoverInterval
    );
  }

  static fromJSON(json: ProfessionalRuleJSON) {
    const copy = cloneDeep(json);

    return new ProfessionalRule(
      copy.id,
      copy.schemeId,
      copy.name,
      copy.blockGuid,
      copy.roomTag,
      copy.device,
      copy.bizCode,
      copy.ruleCode,
      copy.available,
      copy.triggerInterval,
      copy.triggerCount,
      copy.recoverInterval
    );
  }

  private _localeCode: LocaleCode = 'zh-CN';
  private _locales: ProfessionalRuleLocales;

  constructor(
    public id: number,
    public schemeId: number,
    public name: string,
    public blockGuid: string,
    public roomTag: string,
    public device: Device,
    public bizCode: string,
    public ruleCode: string,
    public available: boolean,
    public triggerInterval: number,
    public triggerCount: number,
    public recoverInterval: number
  ) {
    this._locales = getProfessionalRuleLocales(this._localeCode);
  }

  public set locales(locales: ProfessionalRuleLocales) {
    this._locales = locales;
  }

  public get locales() {
    return this._locales;
  }

  public toApiObject(): BackendProfessionalRule {
    return cloneDeep({
      id: this.id,
      schemeId: this.schemeId,
      name: this.name,
      blockGuid: this.blockGuid,
      roomTag: this.roomTag,
      deviceGuid: this.device.guid,
      deviceName: this.device.name,
      bizCode: this.bizCode,
      ruleCode: this.ruleCode,
      available: this.available,
      triggerInterval: this.triggerInterval,
      triggerCount: this.triggerCount,
      recoverInterval: this.recoverInterval,
    });
  }

  public toJSON(): ProfessionalRuleJSON {
    return cloneDeep({
      id: this.id,
      schemeId: this.schemeId,
      name: this.name,
      blockGuid: this.blockGuid,
      roomTag: this.roomTag,
      device: this.device,
      bizCode: this.bizCode,
      ruleCode: this.ruleCode,
      available: this.available,
      triggerInterval: this.triggerInterval,
      triggerCount: this.triggerCount,
      recoverInterval: this.recoverInterval,
    });
  }
}

export type ConditionValue =
  | {
      type: FormulaType.Point;
      value: TreePoint;
    }
  | {
      type: FormulaType.Custom;
      value: string;
    };

export type RuleCondition = {
  firstValue: {
    type: FormulaType.Point;
    value: TreePoint;
  };
  symbol: SignType;
  secondValue: ConditionValue;
};

export type RuleJSON = {
  preConditions?: RuleCondition[];
  triggerConditions: RuleCondition[];
  energyPoint?: TreePoint;
};

export type ProfessionalRuleDetailJSON = ProfessionalRuleJSON & {
  /** 能效设备 guid */
  energyDevice?: string;
  /** 能效设备类型 */
  energyDeviceType?: string;
  /** 能效测点 code */
  energyPoint?: string;
  /** 规则表达式：后端所需 */
  ruleFormula: string;
  /** 规则描述 */
  ruleJson: RuleJSON;
};

export class ProfessionalRuleDetail extends ProfessionalRule {
  static fromApiObject(object: BackendProfessionalRuleDetail) {
    const copy = cloneDeep(object);

    return new ProfessionalRuleDetail(
      copy.id,
      copy.schemeId,
      copy.name,
      copy.blockGuid,
      copy.roomTag,
      {
        guid: copy.deviceGuid,
        name: copy.deviceName,
      },
      copy.bizCode,
      copy.ruleCode,
      copy.available,
      copy.triggerInterval,
      copy.triggerCount,
      copy.recoverInterval,
      copy.energyDevice,
      copy.energyDeviceType,
      copy.energyPoint,
      copy.ruleFormula,
      JSON.parse(copy.ruleJson) as RuleJSON
    );
  }

  static fromJSON(json: ProfessionalRuleDetailJSON) {
    const copy = cloneDeep(json);

    return new ProfessionalRuleDetail(
      copy.id,
      copy.schemeId,
      copy.name,
      copy.blockGuid,
      copy.roomTag,
      copy.device,
      copy.bizCode,
      copy.ruleCode,
      copy.available,
      copy.triggerInterval,
      copy.triggerCount,
      copy.recoverInterval,
      copy.energyDevice ?? null,
      copy.energyPoint ?? null,
      copy.energyDeviceType ?? null,
      copy.ruleFormula,
      copy.ruleJson
    );
  }

  constructor(
    public id: number,
    public schemeId: number,
    public name: string,
    public blockGuid: string,
    public roomTag: string,
    public device: Device,
    public bizCode: string,
    public ruleCode: string,
    public available: boolean,
    public triggerInterval: number,
    public triggerCount: number,
    public recoverInterval: number,
    public energyDevice: string | null,
    public energyDeviceType: string | null,
    public energyPoint: string | null,
    public ruleFormula: string,
    public ruleJson: RuleJSON
  ) {
    super(
      id,
      schemeId,
      name,
      blockGuid,
      roomTag,
      device,
      bizCode,
      ruleCode,
      available,
      triggerInterval,
      triggerCount,
      recoverInterval
    );
  }

  public toApiObject(): BackendProfessionalRuleDetail {
    return cloneDeep({
      ...super.toApiObject(),
      energyDevice: this.energyDevice,
      energyDeviceType: this.energyDeviceType,
      energyPoint: this.energyPoint,
      ruleFormula: this.ruleFormula,
      ruleJson: JSON.stringify(this.ruleJson),
    });
  }

  public toJSON(): ProfessionalRuleDetailJSON {
    return cloneDeep({
      ...super.toJSON(),
      energyDevice: this.energyDevice ?? undefined,
      energyDeviceType: this.energyDeviceType ?? undefined,
      energyPoint: this.energyPoint ?? undefined,
      ruleFormula: this.ruleFormula,
      ruleJson: this.ruleJson,
    });
  }
}
