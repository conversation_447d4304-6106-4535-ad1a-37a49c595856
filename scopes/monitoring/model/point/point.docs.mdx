---
description: '测点模型'
labels: ['model', 'point']
---

## Data Type

- `AI(Analog Input)`: 模拟量输入
- `AO(Analog Output)`: 模拟量输出
- `DI(Discrete/Digital Input)`: 开关量输入
- `DO(Discrete/Digital Output)`: 开关量输出

`AI, AO` 在渲染时需要带单位，`DI, DO` 在渲染时需要转换对应的文案

举个 🌰

```ts
const point: Point = getPointSomehow();
const isAnalogSignal = point.dataType.code === 'AI' || point.dataType.code === 'AO';
const isDigitalSignal = point.dataType.code === 'DI' || point.dataType.code === 'DO';
const pointData = getPointMonitoringData(device, {
  hardCodedPointCode: point.code,
  formatted: isAnalogSignal,
  reflected: isDigitalSignal,
});
```
