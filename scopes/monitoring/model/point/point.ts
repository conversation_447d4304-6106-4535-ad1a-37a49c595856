import cloneDeep from 'lodash.clonedeep';

export type PointDataState =
  | 'default'
  | /** 测点处于告警中 */ 'alarm'
  | /** 测点处于预警中 */ 'warning'
  | /** 测点值超出工作区间 */ 'out-of-range'
  | /** 实时数据通信中断 */ 'comms-interrupted';

export type DiPointValue = {
  NAME: string;
  STATUS_CODE?: string;
  ON_OFF_STATE?: 'on' | 'off';
};

export type ValueMapping = Record<string, DiPointValue>;
export type DataType =
  | /* Analog Input: 模拟量输入 */ 'AI'
  | /* Analog Output: 模拟量输出 */ 'AO'
  | /* Discrete/Digital Input: 开关量输入 */ 'DI'
  | /* Discrete/Digital Output: 开关量输出 */ 'DO'
  | 'ALARM';
export type PointType =
  | /*原始测点*/ 'ORI'
  | /*空间加工测点 */ 'CAL_SPACE'
  | /*空间聚合测点*/ 'AGG_SPACE'
  | /*设备加工测点*/ 'CAL_DEVICE'
  | /*设备聚合测点*/ 'AGG_DEVICE'
  | /*自定义测点*/ 'CUSTOM';

export type PointObject = {
  code: string;
  name: string;
};

export type BackendPoint = {
  id: number;
  alarmLevel: number | null;
  alarmType: PointObject | null;
  code: string;
  pointCode: string;
  dataType: {
    code: DataType;
    name: string;
  };
  description: string | null;
  deviceType: string;
  dimension: PointObject;
  extCount: number | null;
  extName: string | null;
  formula: string | null;
  formulaJson: string | null;
  isInitialized: boolean;
  isSub: boolean;
  name: string;
  pointType: PointObject;
  precision: number | null;
  priority: number | null;
  spaceGuid: string | null;
  unit: string | null;
  validLimits: string[];
  parentCode: string | null;
};

export type PointJSON = {
  id: number;
  code: string;
  name: string;
  extCount: number | null;
  extName: string | null;
  validLimits: string[];
  unit: string | null;
  dataType: {
    code: DataType;
    name: string;
  };
  pointType: PointObject;
  precision: number | null;
  deviceType: string;
  description: string | null;
  isInitialized: boolean;
  isSub: boolean;
  priority: number | null;
  spaceGuid: string | null;
  formulaJson: string | null;
  formula: string | null;
  alarmLevel: number | null;
  alarmType: PointObject | null;
  dimension: PointObject;
  parentCode: string | null;
};

export class Point {
  constructor(
    public id: number,
    public code: string,
    public name: string,
    public extCount: number | null,
    public extName: string | null,
    public validLimits: string[],
    public unit: string | null,
    public dataType: {
      code: DataType;
      name: string;
    },
    public pointType: PointObject,
    public precision: number | null,
    public deviceType: string,
    public description: string | null,
    public isInitialized: boolean,
    public isSub: boolean,
    public priority: number | null,
    public spaceGuid: string | null,
    public formulaJson: string | null,
    public formula: string | null,
    public alarmLevel: number | null,
    public alarmType: PointObject | null,
    public dimension: PointObject,
    public parentCode: string | null
  ) {}

  static fromApiObject(object: BackendPoint) {
    return new Point(
      object.id,
      object.code,
      object.name,
      object.extCount,
      object.extName,
      object.validLimits,
      object.unit,
      object.dataType,
      object.pointType,
      object.precision,
      object.deviceType,
      object.description,
      object.isInitialized,
      object.isSub,
      object.priority,
      object.spaceGuid,
      object.formulaJson,
      object.formula,
      object.alarmLevel,
      object.alarmType,
      object.dimension,
      object.parentCode
    );
  }

  toApiObject(): BackendPoint {
    return cloneDeep({
      alarmLevel: this.alarmLevel,
      alarmType: this.alarmType,
      id: this.id,
      code: this.code,
      pointCode: this.code,
      dataType: this.dataType,
      description: this.description,
      deviceType: this.deviceType,
      dimension: this.dimension,
      extCount: this.extCount,
      extName: this.extName,
      formula: this.formula,
      formulaJson: this.formulaJson,
      isInitialized: this.isInitialized,
      isSub: this.isSub,
      name: this.name,
      pointType: this.pointType,
      precision: this.precision,
      priority: this.priority,
      spaceGuid: this.spaceGuid,
      unit: this.unit,
      validLimits: this.validLimits,
      parentCode: this.parentCode,
    });
  }

  toJSON(): PointJSON {
    return cloneDeep({
      alarmLevel: this.alarmLevel,
      alarmType: this.alarmType,
      id: this.id,
      code: this.code,
      dataType: this.dataType,
      description: this.description,
      deviceType: this.deviceType,
      dimension: this.dimension,
      extCount: this.extCount,
      extName: this.extName,
      formula: this.formula,
      formulaJson: this.formulaJson,
      isInitialized: this.isInitialized,
      isSub: this.isSub,
      name: this.name,
      pointType: this.pointType,
      precision: this.precision,
      priority: this.priority,
      spaceGuid: this.spaceGuid,
      unit: this.unit,
      validLimits: this.validLimits,
      parentCode: this.parentCode,
    });
  }
}

export class AIPoint extends Point {
  static getValidLimitText(value: string, unit: string | null): string {
    if (value === '--' || !unit) {
      return value;
    }

    return `${value}${unit}`;
  }
}

export class DIPoint extends Point {
  static getValidLimitText(value: string, validLimits: string[]): string {
    const mappings = validLimits.map(validLimit => validLimit.split('='));
    // eslint-disable-next-line eqeqeq
    const text = mappings.find(([val]) => val == value);
    if (text === undefined) {
      return String(value);
    }
    return text[1];
  }
}
