import type { NonPointDataLocales } from './type';

export const zhCN: NonPointDataLocales = {
  category: '测点分类',
  name: '协议测点名称',
  dataType: '协议测点类型',
  unit: '协议单位',
  validLimits: '协议值含义',
  formula: '加工表达式',
  captureStatus: { __self: '采集状态', CAPTURED: '已采集', UNCAPTURED: '未采集' },
  pointType: { __self: '测点类型', NORMAL: '标准测点', NON: '非标测点' },
  nomarlId: '测点ID',
  updateChannelPoint: '是否同步修改通道数据',
};

export default zhCN;
