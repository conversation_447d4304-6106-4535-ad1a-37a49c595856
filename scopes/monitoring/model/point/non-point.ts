import cloneDeep from 'lodash.clonedeep';

import type { DataType } from './point';

export type PointObject = {
  code: string;
  name: string;
};

export type BackendNonPoint = {
  id: number;
  // 原始点位code
  code: string;
  // 原始点位名称
  name: string;
  // 协议测点类型
  dataType: DataType;
  // 协议单位
  unit: string;
  // 协议值含义
  validLimits: string;
  // 标准测点名称
  normalName: string;
  // 标准测点类型
  normalDataType: DataType;
  // 标准单位
  normalUnit: string;
  // 标准值含义
  normalValidLimits: string[];
  // 是否为非标测点
  nonPoint: boolean;
  // 加工表达式
  formula: string;
  blockGuid: string;
  // 型号
  productModel: string;
  // 三级分类
  deviceType: string;
  // 是否采集
  capture: boolean;
  isDeleted: boolean;
  gmtCreate: string;
  gmtModified: string;
  invalidValues?: string;
};

// 标准测点
type NormalPoint = {
  name: string;
  type: DataType;
  unit: string;
  validLimits: string[];
};

// 原始测点
type Point = {
  name: string;
  type: DataType;
  code: string;
  unit: string;
  validLimits: string;
};

export type NonPointJSON = {
  id: number;
  point: Point;
  normalPoint: NormalPoint;
  nonPoint: boolean;
  formula: string;
  invalidValues?: string;
  capture: boolean;
  isDeleted: boolean;
  blockGuid: string;
  productModel: string;
  deviceType: string;
  createdAt: string;
  modifiedAt: string;
};
/** 属地测点 */
export class NonPoint {
  constructor(
    public id: number,
    public point: Point,
    public normalPoint: NormalPoint,
    public nonPoint: boolean,
    public formula: string,
    public capture: boolean,
    public isDeleted: boolean,
    public blockGuid: string,
    public productModel: string,
    public deviceType: string,
    public createdAt: string,
    public modifiedAt: string,
    public invalidValues?: string
  ) {}

  static fromApiObject(object: BackendNonPoint) {
    return new NonPoint(
      object.id,
      {
        name: object.name,
        type: object.dataType,
        code: object.code,
        unit: object.unit,
        validLimits: object.validLimits,
      },
      {
        name: object.normalName,
        type: object.normalDataType,
        unit: object.normalUnit,
        validLimits: object.normalValidLimits,
      },
      object.nonPoint,
      object.formula,
      object.capture,
      object.isDeleted,
      object.blockGuid,
      object.productModel,
      object.deviceType,
      object.gmtCreate,
      object.gmtModified,
      object.invalidValues
    );
  }

  toApiObject(): BackendNonPoint {
    return cloneDeep({
      id: this.id,
      code: this.point.code,
      name: this.point.name,
      dataType: this.point.type,
      unit: this.point.unit,
      validLimits: this.point.validLimits,
      normalName: this.normalPoint.name,
      normalDataType: this.normalPoint.type,
      normalUnit: this.normalPoint.unit,
      normalValidLimits: this.normalPoint.validLimits,
      nonPoint: this.nonPoint,
      formula: this.formula,
      invalidValues: this.invalidValues,
      blockGuid: this.blockGuid,
      productModel: this.productModel,
      deviceType: this.deviceType,
      capture: this.capture,
      isDeleted: this.isDeleted,
      gmtCreate: this.createdAt,
      gmtModified: this.modifiedAt,
    });
  }

  toJSON(): NonPointJSON {
    return cloneDeep({
      id: this.id,
      point: this.point,
      normalPoint: this.normalPoint,
      nonPoint: this.nonPoint,
      formula: this.formula,
      invalidValues: this.invalidValues,
      capture: this.capture,
      isDeleted: this.isDeleted,
      blockGuid: this.blockGuid,
      productModel: this.productModel,
      deviceType: this.deviceType,
      createdAt: this.createdAt,
      modifiedAt: this.modifiedAt,
    });
  }
}
