const BLANK_PLACEHOLDER = '--' as const;

export type PointProps = {
  /**
   * 测点值
   */
  value: any;
  /**
   * 测点单位
   */
  unit: string | null;
};

/**
 * 给定一个测点属性对象，返回测点值的展示文案
 *
 * @param props 测点属性对象
 * @returns 测点值的展示文案
 */
export function formatPointValueText({ value, unit }: PointProps) {
  if (value === BLANK_PLACEHOLDER || !unit) {
    return value;
  }

  return `${value}${unit}`;
}

/** TODO @Jeery import this type from `@manyun/dc-brain.config.base` */
export type PointOnOffStateConfig = 'on' | 'off';

export type PointValueMapping = Record<
  string,
  { NAME: string; STATUS_CODE?: string; ON_OFF_STATE?: PointOnOffStateConfig }
>;

/**
 * @param validLimits DI 测点值对应的文案集合
 * @param statusCodes 页面上应显示的颜色状态枚举集合（参考 `<StatusText />` 组件的 `status` API）
 * @param onOffStates 用于判断开关状态的枚举集合
 * @returns
 */
export function generatePointValueMapping(
  validLimits: string[] = [],
  statusCodes: string[] = [],
  onOffStates: string[] = []
) {
  const statusCodeMappings = statusCodes.map(statusCode => statusCode.split('='));
  const onOffStateMappings: [string, PointOnOffStateConfig][] = onOffStates.map(
    onOffState => onOffState.split('=') as [string, PointOnOffStateConfig]
  );

  return validLimits.reduce<PointValueMapping>((map, validLimit) => {
    const [key, value] = validLimit.split('=');
    map[key] = { NAME: value };

    // eslint-disable-next-line eqeqeq
    const statusCode = statusCodeMappings.find(([k]) => k == key);
    if (statusCode) {
      map[key].STATUS_CODE = statusCode[1];
    }
    // eslint-disable-next-line eqeqeq
    const onOffState = onOffStateMappings.find(([k]) => k == key);
    if (onOffState) {
      map[key].ON_OFF_STATE = onOffState[1];
    }

    return map;
  }, {});
}

/**
 * 把 `generatePointValueMapping` 的结果转换成 `validLimitsMap`
 * @param valueMapping
 * @returns
 */
export function pointValueMapping2ValidLimitsMap(valueMapping: PointValueMapping) {
  return Object.keys(valueMapping).reduce<Record<string, string>>((map, value) => {
    map[value] = valueMapping[value].NAME;

    return map;
  }, {});
}

/**
 * 转换 DI 测点的文案
 * @param value
 * @param validLimits
 * @returns
 */
export function getDiPointValueText(value: number, validLimits: string[] = []) {
  const mappings = validLimits.map(validLimit => validLimit.split('='));
  const text = mappings.find(([val]) => val === String(value));
  if (text === undefined) {
    return String(value);
  }
  return text[1];
}

/**
 * 将测点取值范围表达式拆分成 `value`, `lavel` 形式的对象
 *
 * @param validLimits 测点取值范围表达式集合
 *
 * @example
 *
 * ```js
 * generateValidLimitsDataSource(['1=闭合','2=断开']);
 * // [{ value: '1', label: '闭合' }, { value: '2', label: '断开' }]
 * ```
 */
export function generateValidLimitsDataSource(validLimits: string[] = []) {
  return validLimits.map(validLimit => {
    const [value, label] = validLimit.split('=');

    return { value, label };
  });
}

/**
 * 根据每个元素的 count 是否大于 0 来判断最优先的元素
 * @param possibilities 已经排好序的可选项
 * @param defaults 若未从 possibilities 里找出最优元素，则使用此作为默认值
 */
export function getTop1ByPositiveCount(
  possibilities: Array<{ name: string; count: number }> = [],
  defaults: { name: string; count: number | string } = { name: BLANK_PLACEHOLDER, count: '' }
) {
  let top1;

  for (let index = 0; index < possibilities.length; index++) {
    const possibility = possibilities[index];
    if (possibility.count > 0) {
      top1 = possibility;
      break;
    }
  }

  if (top1 === undefined) {
    top1 = defaults;
  }

  return top1;
}
