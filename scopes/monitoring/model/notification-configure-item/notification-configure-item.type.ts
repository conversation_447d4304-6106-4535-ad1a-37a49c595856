/**通报类型 */
export type BackendAlarmNotificationConfigureType =
  | /**告警通报 */ 'ALARM_NOTIFY'
  | /**未响应通报*/ 'RESPONSE_NOTIFY'
  | /**未恢复通报*/ 'RECOVER_NOTIFY'
  | /**告警恢复 */ 'RECOVERED_NOTIFY';

export const ALARM_NOTIFICATION_CONFIGURE_TYPE_MAPPER: Record<
  BackendAlarmNotificationConfigureType,
  string
> = {
  ALARM_NOTIFY: '告警通报',
  RESPONSE_NOTIFY: '未响应通报',
  RECOVER_NOTIFY: '未恢复通报',
  RECOVERED_NOTIFY: '告警恢复',
};
