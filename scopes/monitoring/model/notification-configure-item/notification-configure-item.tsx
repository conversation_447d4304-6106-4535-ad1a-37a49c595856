import cloneDeep from 'lodash.clonedeep';

import {
  BackendNotificationItemObject,
  NotificationItemObject,
  NotificationItemObjectJSON,
} from '@manyun/monitoring.model.notification-configure-item-object';

import { BackendAlarmNotificationConfigureType } from './notification-configure-item.type';

export type BackendNotificationConfigureItem = {
  id: number;
  /**通报项名称 */
  name: string;
  /**告警通报类型 */
  type: BackendAlarmNotificationConfigureType;
  /**通报规则(时间)秒，注： 0s 立即通报 */
  rule: number;
  /**是否启用webhook */
  webhook: boolean;
  /**告警通知对象 */
  informTargetList: BackendNotificationItemObject[];
  gmtCreate?: string;
  gmtModified?: string;
};

export type NotificationConfigureItemJSON = {
  id: number | string;
  name: string;
  type: BackendAlarmNotificationConfigureType;
  rule: number;
  webhook: boolean;
  notificationObjects: NotificationItemObjectJSON[];
  gmtCreate?: string;
  gmtModified?: string;
};

export class NotificationConfigureItem {
  constructor(
    public id: number | string,
    public name: string,
    public type: BackendAlarmNotificationConfigureType,
    public rule: number,
    public webhook: boolean,
    public notificationObjects: NotificationItemObject[],
    public gmtCreate?: string,
    public gmtModified?: string
  ) {}

  static fromApiObject(backendItem: BackendNotificationConfigureItem): NotificationConfigureItem {
    const copy = cloneDeep(backendItem);
    return new NotificationConfigureItem(
      copy.id,
      copy.name,
      copy.type,
      copy.rule,
      copy.webhook,
      copy.informTargetList.map(target => NotificationItemObject.fromApiObject(target)),
      copy.gmtCreate,
      copy.gmtModified
    );
  }

  static toApiObject(
    notificationItem: NotificationConfigureItem
  ): BackendNotificationConfigureItem {
    const copy = cloneDeep(notificationItem);
    return {
      id: copy.id as number,
      name: copy.name,
      type: copy.type,
      rule: copy.rule,
      webhook: copy.webhook,
      informTargetList: copy.notificationObjects.map(obj =>
        NotificationItemObject.toApiObject(obj)
      ),
      gmtCreate: copy.gmtCreate,
      gmtModified: copy.gmtModified,
    };
  }

  static parseJSON(notificationItem: NotificationConfigureItem): NotificationConfigureItemJSON {
    const copy = cloneDeep(notificationItem);
    return {
      id: copy.id,
      name: copy.name,
      type: copy.type,
      rule: copy.rule,
      webhook: copy.webhook,
      notificationObjects: copy.notificationObjects.map(obj =>
        NotificationItemObject.parseJSON(obj)
      ),
      gmtCreate: copy.gmtCreate,
      gmtModified: copy.gmtModified,
    };
  }
}
