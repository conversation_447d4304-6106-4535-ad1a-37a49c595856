/**告警通报对象类型 */
export type BackendObjectType = 'ROLE';

/**角色排班状态 */
export type BackedScheduleStatus =
  | /**全部*/ 'ALL'
  | /**在班次*/ 'ON_DUTY'
  | /**未在班次 */ 'NOT_ON_DUTY';

export const SCHEDULE_STATUS_MAPPER: Record<BackedScheduleStatus, string> = {
  ALL: '全部',
  ON_DUTY: '在班次',
  NOT_ON_DUTY: '未在班次',
};

/**通知渠道 */
export type BackendChannelType =
  | /**电话*/ 'PHONE'
  | /**短信*/ 'SMS'
  | /**邮件 */ 'EMAIL'
  | /**站内信*/ 'INTERNAL_MESSAGE'
  | /**webhook */ 'WEB_HOOK';

export const CHANNEL_SEPARATOR = ',';
