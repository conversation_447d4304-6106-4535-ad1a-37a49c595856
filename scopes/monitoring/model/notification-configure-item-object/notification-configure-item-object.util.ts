import { NotificationItemObjectJSON } from './notification-configure-item-object';
import type { BackendChannelType } from './notification-configure-item-object.type';
import { CHANNEL_SEPARATOR } from './notification-configure-item-object.type';

export type ChannelField = 'email' | 'sms' | 'phone' | 'internalMsg';

export function transformChannelFromApi(
  backendChannels: string,
  channelType: BackendChannelType
): boolean {
  const formatArray = backendChannels.split(CHANNEL_SEPARATOR);
  return formatArray.includes(channelType);
}

export function transformChannelToApi(
  obj: Pick<NotificationItemObjectJSON, 'email' | 'phone' | 'sms' | 'internalMsg'>
): string {
  const channelArray: BackendChannelType[] = [];
  if (obj.email) {
    channelArray.push('EMAIL');
  }
  if (obj.internalMsg) {
    channelArray.push('INTERNAL_MESSAGE');
  }
  if (obj.phone) {
    channelArray.push('PHONE');
  }
  if (obj.sms) {
    channelArray.push('SMS');
  }
  return channelArray.join(CHANNEL_SEPARATOR);
}
