import cloneDeep from 'lodash.clonedeep';

import type {
  BackedScheduleStatus,
  BackendObjectType,
} from './notification-configure-item-object.type';
import {
  transformChannelFromApi,
  transformChannelToApi,
} from './notification-configure-item-object.util';

export type BackendNotificationItemObject = {
  /**角色code */
  targetId: string;
  /**对象类型 */
  targetType: BackendObjectType;
  /**角色名称 */
  targetName: string;
  /**排班状态 */
  dutyStatus: BackedScheduleStatus;
  /** 通知渠道，多个,隔开*/
  channels: string;
};

export type NotificationItemObjectJSON = {
  code: string;
  name: string;
  type: BackendObjectType;
  status: BackedScheduleStatus;
  email: boolean;
  sms: boolean;
  phone: boolean;
  internalMsg: boolean;
};

export class NotificationItemObject {
  constructor(
    /**对象编码 */
    public code: string,
    /**对象名称 */
    public name: string,
    /**对象类型 */
    public type: BackendObjectType,
    /**对象排班状态 */
    public status: BackedScheduleStatus,
    /**邮件通知*/
    public email: boolean,
    //**短信通知 */
    public sms: boolean,
    /**电话通知 */
    public phone: boolean,
    /**站内信通知 */
    public internalMsg: boolean
  ) {}

  static fromApiObject(backendItemObject: BackendNotificationItemObject): NotificationItemObject {
    const copy = cloneDeep(backendItemObject);
    return new NotificationItemObject(
      copy.targetId,
      copy.targetName,
      copy.targetType,
      copy.dutyStatus,
      transformChannelFromApi(copy.channels, 'EMAIL'),
      transformChannelFromApi(copy.channels, 'SMS'),
      transformChannelFromApi(copy.channels, 'PHONE'),
      transformChannelFromApi(copy.channels, 'INTERNAL_MESSAGE')
    );
  }

  static toApiObject(itemObject: NotificationItemObject): BackendNotificationItemObject {
    const copy = cloneDeep(itemObject);
    return {
      targetId: copy.code,
      targetName: copy.name,
      targetType: copy.type,
      channels: transformChannelToApi(copy),
      dutyStatus: copy.status,
    };
  }

  static parseJSON(itemObjectJSON: NotificationItemObjectJSON): NotificationItemObject {
    const copy = cloneDeep(itemObjectJSON);
    return new NotificationItemObject(
      copy.code,
      copy.name,
      copy.type,
      copy.status,
      copy.email,
      copy.sms,
      copy.phone,
      copy.internalMsg
    );
  }

  // toJSON() {}
}
