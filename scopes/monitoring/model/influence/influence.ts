import type { SpaceGuid } from '@manyun/resource-hub.model.device';

export type InfluenceCustomerProps = {
  customerName: string | null;
  influenceGridCount: number | null;
};

export type InfluenceDeviceProps = {
  columnTag: string | null;
  deviceGuid: string | null;
  deviceName: string | null;
  deviceTag: string | null;
  deviceType: string | null;
  extendPosition: string | null;
  gridGuid: string | null;
  gridTag: string | null;
  roomColumnTag: string | null;
  spaceGuid: SpaceGuid | null;
};

export type InfluenceGridProps = {
  gridCustomer: string | null;
  gridGuid: string | null;
  gridTag: string | null;
  gridType: {
    code: string;
    name: string;
  } | null;
  spaceGuid: SpaceGuid | null;
};

export type BackendInfluence = {
  influenceCustomers: Array<InfluenceCustomerProps> | null;
  influenceDevices: Array<InfluenceDeviceProps> | null;
  influenceGrids: Array<InfluenceGridProps> | null;
};

export class Influence {
  public influenceCustomers: Array<InfluenceCustomerProps> | null;
  public influenceDevices: Array<InfluenceDeviceProps> | null;
  public influenceGrids: Array<InfluenceGridProps> | null;

  constructor(
    influenceCustomers: Array<InfluenceCustomerProps> | null,
    influenceDevices: Array<InfluenceDeviceProps> | null,
    influenceGrids: Array<InfluenceGridProps> | null
  ) {
    this.influenceCustomers = influenceCustomers;
    this.influenceDevices = influenceDevices;
    this.influenceGrids = influenceGrids;
  }

  static fromApiObject(object: BackendInfluence) {
    return new Influence(object.influenceCustomers, object.influenceDevices, object.influenceGrids);
  }

  static toApiObject(influence: Influence): BackendInfluence {
    return {
      influenceCustomers: influence.influenceCustomers,
      influenceDevices: influence.influenceDevices,
      influenceGrids: influence.influenceGrids,
    };
  }
}
