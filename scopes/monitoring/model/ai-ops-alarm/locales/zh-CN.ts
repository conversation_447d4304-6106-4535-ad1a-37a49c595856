import { Feedback } from '../backend-ai-ops-alarm';
import type { AiOpsAlarmLocales } from './type';

export const zhCN: AiOpsAlarmLocales = {
  scenesName: '监测场景',
  scenesType: '场景类型',
  source: {
    idcTag: '机房',
    blockTag: '楼栋',
    roomTag: '包间',
    deviceName: '监测对象',
    pointName: '测点名称',
  },
  alarmCondition: '监控模型',
  alarmType: '类型',
  alarmLevel: '级别',
  feedback: {
    __self: '反馈结果',
    [Feedback.On]: '有效',
    [Feedback.Off]: '无效',
  },
  triggerStatus: { __self: '监测状态', TRIGGER: '触发中', RECOVER: '已恢复' },
  triggerAt: '触发时间',
  recoverAt: '恢复时间',
  createAt: '开始时间',
};

export default zhCN;
