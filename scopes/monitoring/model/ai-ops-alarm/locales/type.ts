import type { Feedback } from '../backend-ai-ops-alarm';

export type AiOpsAlarmLocales = {
  scenesName: string;
  scenesType: string;
  source: {
    idcTag: string;
    blockTag: string;
    roomTag: string;
    deviceName: string;
    pointName: string;
  };
  alarmCondition: string;
  alarmType: string;
  alarmLevel: string;
  feedback: Record<Feedback | '__self', string>;
  triggerStatus: { __self: string; TRIGGER: string; RECOVER: string };
  triggerAt: string;
  recoverAt: string;
  createAt: string;
};
