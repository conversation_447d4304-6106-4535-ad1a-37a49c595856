import type { LocaleCode } from '@teammc/react-intl';
import dayjs from 'dayjs';
import cloneDeep from 'lodash.clonedeep';

import type { BackendAiOpsAlarm, Feedback, TriggerStatus } from './backend-ai-ops-alarm';
import { getAiOpsAlarmLocales } from './locales';
import type { AiOpsAlarmLocales } from './locales';

type Source = {
  idcTag: string;
  blockTag: string;
  roomTag: string;
  deviceGuid: string;
  deviceName: string;
  pointName: string;
  pointCode: string;
};

type ScenesType = {
  name: string;
  code: string;
};

type AlarmCondition = {
  name: string;
  code: string;
};

type AlarmType = {
  name: string;
  code: string;
};

type FeedbackMap = {
  name: string;
  code: Feedback | null;
  desc: string;
};

type TriggerStatusMap = {
  name: string;
  code: TriggerStatus;
};

export type AiOpsAlarmJSON = {
  id: string;
  scenesId: number;
  scenesName: string;
  scenesType: ScenesType;
  source: Source;
  unit: string;
  alarmCondition: AlarmCondition;
  alarmType: AlarmType;
  alarmLevel: number;
  deviceType: string;
  roomType: string;
  feedback: FeedbackMap;
  triggerStatus: TriggerStatusMap;
  alarmDesc: string;
  createAt: number;
  triggerAt: number;
  recoverAt: number | null;
  feedbackAt: number | null;
  gmtCreateAt: number;
  gmtModifiedAt: number | null;
};

export class AiOpsAlarm {
  static fromApiObject(object: BackendAiOpsAlarm) {
    const copy = cloneDeep(object);

    return new AiOpsAlarm(
      copy.id,
      copy.scenesId,
      copy.scenesName,
      { name: copy.scenesType, code: copy.scenesTypeCode },
      {
        idcTag: copy.idcTag,
        blockTag: copy.blockTag,
        roomTag: copy.roomTag,
        deviceGuid: copy.deviceGuid,
        deviceName: copy.deviceName,
        pointName: copy.pointName,
        pointCode: copy.pointCode,
      },
      copy.unit,
      { name: copy.alarmCondition, code: copy.alarmConditionCode },
      { name: copy.alarmType, code: copy.alarmTypeCode },
      copy.alarmLevel,
      copy.deviceType,
      copy.roomType,
      { name: copy.feedback, code: copy.feedbackCode, desc: copy.feedbackDesc },
      { name: copy.triggerStatus, code: copy.triggerStatusCode },
      copy.alarmDesc,
      dayjs(copy.firstTime).valueOf(),
      dayjs(copy.triggerTime).valueOf(),
      copy.recoverTime ? dayjs(copy.recoverTime).valueOf() : null,
      copy.feedbackTime ? dayjs(copy.feedbackTime).valueOf() : null,
      dayjs(copy.gmtCreate).valueOf(),
      copy.gmtModified ? dayjs(copy.gmtModified).valueOf() : null
    );
  }

  static fromJSON(json: AiOpsAlarmJSON) {
    const copy = cloneDeep(json);

    return new AiOpsAlarm(
      copy.id,
      copy.scenesId,
      copy.scenesName,
      copy.scenesType,
      copy.source,
      copy.unit,
      copy.alarmCondition,
      copy.alarmType,
      copy.alarmLevel,
      copy.deviceType,
      copy.roomType,
      copy.feedback,
      copy.triggerStatus,
      copy.alarmDesc,
      copy.createAt,
      copy.triggerAt,
      copy.recoverAt,
      copy.feedbackAt,
      copy.gmtCreateAt,
      copy.gmtModifiedAt
    );
  }

  private _localeCode: LocaleCode = 'zh-CN';
  private _locales: AiOpsAlarmLocales;

  public getFormattedcreateAtAt(template = 'YYYY-MM-DD HH:mm:ss') {
    return dayjs(this.createAt).format(template);
  }

  public getFormattedRecoverAt(template = 'YYYY-MM-DD HH:mm:ss') {
    return dayjs(this.recoverAt).format(template);
  }

  constructor(
    public id: string,
    public scenesId: number,
    public scenesName: string,
    public scenesType: ScenesType,
    public source: Source,
    public unit: string,
    public alarmCondition: AlarmCondition,
    public alarmType: AlarmType,
    public alarmLevel: number,
    public deviceType: string,
    public roomType: string,
    public feedback: FeedbackMap,
    public triggerStatus: TriggerStatusMap,
    public alarmDesc: string,
    public createAt: number,
    public triggerAt: number,
    public recoverAt: number | null,
    public feedbackAt: number | null,
    public gmtCreateAt: number,
    public gmtModifiedAt: number | null
  ) {
    this._locales = getAiOpsAlarmLocales(this._localeCode);
  }

  public set locales(locales: AiOpsAlarmLocales) {
    this._locales = locales;
  }

  public get locales() {
    return this._locales;
  }

  public toApiObject(): BackendAiOpsAlarm {
    return cloneDeep({
      id: this.id,
      scenesId: this.scenesId,
      scenesName: this.scenesName,
      scenesType: this.scenesType.name,
      scenesTypeCode: this.scenesType.code,
      idcTag: this.source.idcTag,
      blockTag: this.source.blockTag,
      roomTag: this.source.roomTag,
      deviceGuid: this.source.deviceGuid,
      deviceName: this.source.deviceName,
      pointName: this.source.pointName,
      pointCode: this.source.pointCode,
      unit: this.unit,
      alarmCondition: this.alarmCondition.name,
      alarmConditionCode: this.alarmCondition.code,
      alarmType: this.alarmType.name,
      alarmTypeCode: this.alarmType.code,
      alarmLevel: this.alarmLevel,
      deviceType: this.deviceType,
      roomType: this.roomType,
      feedback: this.feedback.name,
      feedbackCode: this.feedback.code,
      feedbackDesc: this.feedback.desc,
      triggerStatus: this.triggerStatus.name,
      triggerStatusCode: this.triggerStatus.code,
      alarmDesc: this.alarmDesc,
      firstTime: dayjs(this.createAt).format('YYYY-MM-DD HH:mm:ss'),
      triggerTime: dayjs(this.triggerAt).format('YYYY-MM-DD HH:mm:ss'),
      recoverTime: this.recoverAt ? dayjs(this.recoverAt).format('YYYY-MM-DD HH:mm:ss') : null,
      feedbackTime: this.feedbackAt ? dayjs(this.feedbackAt).format('YYYY-MM-DD HH:mm:ss') : null,
      gmtCreate: dayjs(this.gmtCreateAt).format('YYYY-MM-DD HH:mm:ss'),
      gmtModified: this.gmtModifiedAt
        ? dayjs(this.gmtModifiedAt).format('YYYY-MM-DD HH:mm:ss')
        : null,
    });
  }

  public toJSON(): AiOpsAlarmJSON {
    return cloneDeep({
      id: this.id,
      scenesId: this.scenesId,
      scenesName: this.scenesName,
      scenesType: this.scenesType,
      source: this.source,
      unit: this.unit,
      alarmCondition: this.alarmCondition,
      alarmType: this.alarmType,
      alarmLevel: this.alarmLevel,
      deviceType: this.deviceType,
      roomType: this.roomType,
      feedback: this.feedback,
      triggerStatus: this.triggerStatus,
      alarmDesc: this.alarmDesc,
      createAt: this.createAt,
      triggerAt: this.triggerAt,
      recoverAt: this.recoverAt,
      feedbackAt: this.feedbackAt,
      gmtCreateAt: this.gmtCreateAt,
      gmtModifiedAt: this.gmtModifiedAt,
    });
  }
}
