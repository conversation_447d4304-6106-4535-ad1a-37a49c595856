export type TriggerStatus = 'TRIGGER' | 'RECOVER';

export enum Feedback {
  On = 1,
  Off = 0,
}

export type BackendAiOpsAlarm = {
  id: string;
  scenesId: number;
  scenesName: string;
  scenesType: string;
  scenesTypeCode: string;
  idcTag: string;
  blockTag: string;
  roomTag: string;
  deviceGuid: string;
  deviceName: string;
  deviceType: string;
  roomType: string;
  pointName: string;
  pointCode: string;
  unit: string;
  alarmCondition: string;
  alarmConditionCode: string;
  alarmType: string;
  alarmTypeCode: string;
  alarmLevel: number;
  feedback: string;
  feedbackCode: Feedback | null;
  feedbackDesc: string;
  triggerStatus: string;
  triggerStatusCode: TriggerStatus;
  alarmDesc: string;
  firstTime: string;
  triggerTime: string;
  recoverTime: string | null;
  feedbackTime: string | null;
  gmtCreate: string;
  gmtModified: string | null;
};
