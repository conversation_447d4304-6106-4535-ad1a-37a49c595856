/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-25
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { RequestRespData, SvcRespData } from './fetch-channel-type.type';

const endpoint = '/dccm/channel/get/type';

/**
 * @see [通道类型](https://manyun.yuque.com/luc1u2/ilvfpi/gi0apm#uNkWY)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryGet<RequestRespData>(endpoint);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {},
      };
    }

    return { error, data, ...rest };
  };
}
