/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-25
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-channel-type';
import type { SvcRespData } from './fetch-channel-type.type';

const executor = getExecutor(nodeRequest);

/**
 * @returns
 */
export function fetchChannelType(): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor();
}
