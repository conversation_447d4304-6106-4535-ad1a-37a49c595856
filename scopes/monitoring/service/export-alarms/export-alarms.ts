/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-11
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { RequestRespData, SvcQuery, SvcRespData } from './export-alarms.type';

const endpoint = '/dcim/alarm/model/export';

/**
 * @see [告警列表导出](http://172.16.0.17:13000/project/86/interface/api/18568)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery?: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, SvcQuery>(
      endpoint,
      svcQuery,
      {
        responseType: 'blob',
      }
    );
    return { error, data, ...rest };
  };
}
