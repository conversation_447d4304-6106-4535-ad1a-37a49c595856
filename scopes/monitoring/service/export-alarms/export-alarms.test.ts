/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-11
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { exportAlarms as webService } from './export-alarms.browser';
import { exportAlarms as nodeService } from './export-alarms.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    includeColumnFiledNames: ['alarmState'],
    idcTag: 'EC06',
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { data } = await webService({ includeColumnFiledNames: [''], idcTag: 'EC06' });

  expect(data).toBe(null);
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({
    includeColumnFiledNames: ['alarmState'],
    idcTag: 'EC06',
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[node] should resolve error response', async () => {
  const { data } = await nodeService({ includeColumnFiledNames: [''], idcTag: 'EC06' });

  expect(data).toBe(null);
});
