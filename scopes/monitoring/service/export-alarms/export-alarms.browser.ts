/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-11
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './export-alarms';
import type { SvcQuery, SvcRespData } from './export-alarms.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function exportAlarms(svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
