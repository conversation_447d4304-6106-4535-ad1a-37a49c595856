/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-28
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { createChannelConfig as webService } from './create-channel-config.browser';
import { createChannelConfig as nodeService } from './create-channel-config.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});
test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    blockGuid: 'CQ1.A',
    channelName: '测试2',
    channelStatus: 'OFF',
    channelType: 'HVAC',
    ip: '***********',
    port: '8080',
    protocol: 'MODBUS_TCP_SYNC',
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { data } = await webService({});

  expect(data).toBe(null);
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({
    blockGuid: 'CQ1.A',
    channelName: '测试2',
    channelStatus: 'OFF',
    channelType: 'HVAC',
    ip: '***********',
    port: '8080',
    protocol: 'MODBUS_TCP_SYNC',
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[node] should resolve error response', async () => {
  const { data } = await nodeService({});

  expect(data).toBe(null);
});
