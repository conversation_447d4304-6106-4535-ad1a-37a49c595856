/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-28
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type Config = {
  operatorType: string;
  registerNum: number;
};

export type SvcQuery = ApiQ;

export type SvcRespData = RequestRespData;

export type RequestRespData = boolean | null;

export type ApiQ = {
  channelName: string;
  ip?: string;
  port: string;
  protocol: string;
  channelType: string;
  blockGuid: string;
  deviceGuid?: string;
  channelStatus: string;
  username?: string;
  password?: string;
  config?: Config[];
};

export type ApiR = WriteResponse;
