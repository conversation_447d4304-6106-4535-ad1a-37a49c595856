---
description: 'A createChannelConfig HTTP API service.'
labels: ['service', 'http']
---

## 概述

通道创建

## 使用

### Browser

```ts
import { createChannelConfig } from '@manyun/monitoring.service.create-channel-config';

const { error, data } = await createChannelConfig({
  blockGuid: 'CQ1.A',
  channelName: '测试2',
  channelStatus: 'OFF',
  channelType: 'HVAC',
  ip: '***********',
  port: '8080',
  protocol: 'MODBUS_TCP_SYNC',
});
```
