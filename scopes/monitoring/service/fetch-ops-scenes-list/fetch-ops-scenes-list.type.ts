/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-21
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type {
  BackendDynamicBaseline,
  DynamicBaselineJSON,
} from '@manyun/monitoring.model.dynamic-baseline-setting';

export type SvcQuery = {
  pageNum: number;
  // 监测名称
  name?: string;
  blockGuidList?: string[];
  // 监测模型
  alarmCondition?: string;
  // 通报方式
  notifyWay?: string;
  // 是否启用
  enable?: boolean;
  pageSize?: number;
};

export type SvcRespData = {
  data: DynamicBaselineJSON[];
  total: number;
};

export type RequestRespData = {
  data: BackendDynamicBaseline[] | null;
  total: number;
} | null;

export type ApiR = ListResponse<BackendDynamicBaseline[] | null>;
