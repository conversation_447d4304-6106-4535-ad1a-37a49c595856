---
description: '查询动态基线配置列表'
labels: ['service', 'http']
---

## 使用

### Browser

```ts
import { fetchOpsScenesList } from '@monitoring/service.fetch-ops-scenes-list';

const { error, data } = await fetchOpsScenesList({
  pageNum: 1,
});
```

### Node

```ts
import { fetchOpsScenesList } from '@monitoring/service.fetch-ops-scenes-list/dist/index.node';

const { error, data } = await fetchOpsScenesList({
  pageNum: 1,
});
```
