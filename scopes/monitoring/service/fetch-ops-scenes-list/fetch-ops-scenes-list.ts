/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-21
 *
 * @packageDocumentation
 */
import { DynamicBaseline } from '@manyun/monitoring.model.dynamic-baseline-setting';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { RequestRespData, SvcQuery, SvcRespData } from './fetch-ops-scenes-list.type';

const endpoint = '/dccm/ai/ops/scenes/query';

/**
 * @see [查询配置列表](http://172.16.0.17:13000/project/210/interface/api/20568)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (params: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, SvcQ<PERSON>y>(
      endpoint,
      params
    );

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      error,
      data: {
        data: data.data.map(item => DynamicBaseline.fromApiObject(item).toJSON()),
        total: data.total,
      },
      ...rest,
    };
  };
}
