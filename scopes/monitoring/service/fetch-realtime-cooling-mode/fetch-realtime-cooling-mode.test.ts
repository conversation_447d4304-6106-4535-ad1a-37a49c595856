/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-17
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchRealtimeCoolingMode as webService } from './fetch-realtime-cooling-mode.browser';
import { fetchRealtimeCoolingMode as nodeService } from './fetch-realtime-cooling-mode.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({ blockGuid: 'EC06.A' });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({ blockGuid: 'EC01.A' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService({ blockGuid: 'EC06.A' });

  expect(error).toBe(undefined);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({ blockGuid: 'EC01.A' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
