/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-17
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { RequestRespData, SvcQuery, SvcRespData } from './fetch-realtime-cooling-mode.type';

const endpoint = '/dccm/block/realtime/coolmode/query';

/**
 * @see [Doc](http://172.16.0.17:13000/project/180/interface/api/19384)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async ({
    blockGuid,
    queryGranularity = 5,
  }: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, SvcQuery>(endpoint, {
      blockGuid,
      queryGranularity,
    });

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: null,
      };
    }

    return { error, data, ...rest };
  };
}
