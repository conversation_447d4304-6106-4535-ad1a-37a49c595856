/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-17
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  blockGuid: string;
  queryGranularity?: number;
};

export type Recommend = {
  blockGuid: string;
  recommendCoolingMode: number | null;
  nextRecommendCoolingMode: number | null;
  nextChangeTime: string | null;
};

export type SvcRespData = Recommend | null;

export type RequestRespData = SvcRespData;

export type ApiR = Response<SvcRespData>;
