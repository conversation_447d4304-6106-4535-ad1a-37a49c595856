/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-4
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import type { BackendRealtimePointData } from '@manyun/monitoring.model.monitoring-data';

type DeviceGuid = string;

export type SvcQuery = {
  idc: string;
  /**
   * @example
   *
   * ```ts
   * const deviceGuid = '10101';
   * const pointCode1 = '1001000';
   * const pointCode2 = '1002000';
   * const pointGuids = { [deviceGuid]: `${pointCode1},${pointCode2}` };
   * ```
   */
  pointGuids: Record<DeviceGuid, string>;
};

export type SvcRespData = BackendRealtimePointData;

export type RequestRespData = BackendRealtimePointData | null;

export type ApiQ = Record<string, string>;

export type ApiR = Response<BackendRealtimePointData | null>;
