/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-4
 *
 * @packageDocumentation
 */
import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-point-values.type';

const endpoint = '/dcsocket/point/value/fetch/point/v2';

/**
 * @see [查询测点的实时数据](http://172.16.0.17:13000/project/53/interface/api/17864)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const apiQ: ApiQ = svcQuery.pointGuids;

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ, { idc: string }>(
      endpoint,
      apiQ,
      {
        headers: { token: env.DCSOCKET_TOKEN!, 'X-IDC': svcQuery.idc },
        params: { idc: svcQuery.idc },
      }
    );

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: {},
      };
    }

    return { error, data, ...rest };
  };
}
