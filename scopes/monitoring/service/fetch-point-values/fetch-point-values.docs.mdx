---
description: 'A fetchPointValues HTTP API service.'
labels: ['service', 'http', fetch-point-values]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchPointValues } from '@manyun/monitoring.service.fetch-point-values';

const { error, data } = await fetchPointValues('success');
const { error, data } = await fetchPointValues('error');
```

### Node

```ts
import { fetchPointValues } from '@manyun/monitoring.service.fetch-point-values/dist/index.node';

const { data } = await fetchPointValues('success');

try {
  const { data } = await fetchPointValues('error');
} catch (error) {
  // ...
}
```
