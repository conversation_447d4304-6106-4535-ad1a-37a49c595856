/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-12
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { batchRemoveAlarms as webService } from './batch-remove-alarms.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({
    alarmIds: [1],
    orderNo: '1',
    alarmReason: '1111',
    removeDesc: '22222',
  });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    alarmIds: [0],
    orderNo: '1',
    alarmReason: '1111',
    removeDesc: '22222',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
