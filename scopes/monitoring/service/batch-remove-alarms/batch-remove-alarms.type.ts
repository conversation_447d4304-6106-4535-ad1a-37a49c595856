/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-12
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcRespData = boolean | null;

export type RequestRespData = boolean | null;

// SvcQuery 与 apiQ 相同
export type ApiQ = {
  alarmIds: number[];
  orderNo?: string | null;
  alarmReason: string;
  removeDesc: string;
};

export type ApiR = WriteResponse;
