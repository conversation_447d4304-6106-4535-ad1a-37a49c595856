/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-12
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './batch-remove-alarms';
import type { ApiQ, SvcRespData } from './batch-remove-alarms.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function batchRemoveAlarms(variant?: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
