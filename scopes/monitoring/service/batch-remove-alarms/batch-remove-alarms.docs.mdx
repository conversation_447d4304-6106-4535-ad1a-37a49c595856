---
description: 'A batchRemoveAlarms HTTP API service.'
labels: ['service', 'http', batch-remove-alarms]
---

## 概述

TODO

## 使用

### Browser

```ts
import { batchRemoveAlarms } from '@monitoring/service.batch-remove-alarms';

const { error, data } = await batchRemoveAlarms('success');
const { error, data } = await batchRemoveAlarms('error');
```

### Node

```ts
import { batchRemoveAlarms } from '@monitoring/service.batch-remove-alarms/dist/index.node';

const { data } = await batchRemoveAlarms('success');

try {
  const { data } = await batchRemoveAlarms('error');
} catch (error) {
  // ...
}
```
