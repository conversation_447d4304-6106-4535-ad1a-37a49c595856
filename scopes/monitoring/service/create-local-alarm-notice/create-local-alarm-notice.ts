/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-12-5
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiArgs } from './create-local-alarm-notice.type';

const endpoint = '/dccm/alarm/inform/edit/local';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/158/interface/api/25812)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (args: ApiArgs): Promise<EnhancedAxiosResponse<boolean>> => {
    return await request.tryPost<boolean, ApiArgs>(endpoint, args);
  };
}
