/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-12-5
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type InformTarget = {
  /**
   * 目标用户id
   */
  targetId: string;
  /**
   * 目标类型
   */
  targetType: 'USER' | 'ROLE';
  /**
   * 排版状态
   */
  dutyStatus: string;
  /**
   * 通知渠道（多个以逗号隔开）
   */
  channels: string;
};

export type ApiArgs = {
  /**
   * 通报项id（新增时为空，编辑时为通报项id）
   */
  id?: number;
  /**
   * 目标机房或楼栋guid
   */
  targetId?: string;
  /**
   * 目标类型
   */
  targetType?: string;
  /**
   * 通报名称
   */
  name: string;
  /**
   * 通报类型
   */
  type: string;
  /**
   * 通报规则
   */
  rule: number;
  /**
   * 告警等级（多个以逗号隔开）
   */
  alarmLevels: string;
  /**
   * 使用专业（多个以逗号隔开）
   */
  deviceType?: string;
  /**
   * webhook
   */
  webhook: boolean;
  /* webhook id（多个以逗号隔开）*/
  webhookIdList?: string;
  /* 模版组id，（多个以逗号隔开） */
  schemeIdList?: string;
  /**
   * 是否通报
   */
  notified: boolean;
  informTargetList: InformTarget[];
};

export type ApiResponse = WriteResponse;
