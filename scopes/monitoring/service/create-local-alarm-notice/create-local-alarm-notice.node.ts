/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-12-5
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './create-local-alarm-notice';
import type { ApiArgs } from './create-local-alarm-notice.type';

const executor = getExecutor(nodeRequest);

/**
 * @param args
 * @returns
 */
export function createLocalAlarmNotice(args: ApiArgs): Promise<EnhancedAxiosResponse<boolean>> {
  return executor(args);
}
