/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-15
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import {
  BackendProfessionalRuleTemplate,
  ProfessionalRuleTemplateJSON,
} from '@manyun/monitoring.model.professional-rule-template';

export type SvcQuery = {
  id: number;
};

export type SvcRespData = ProfessionalRuleTemplateJSON | null;

export type RequestRespData = BackendProfessionalRuleTemplate | null;

export type ApiQ = SvcQuery;

export type ApiR = Response<RequestRespData>;
