/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-26
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiData,
  Preferences,
  RequestResult,
  Result,
} from './save-user-alarms-monitoring-board-preferences.type';

const endpoint = '/dcim/alarm/user/config/update';

/**
 * @see [Doc](http://172.16.0.17:13000/project/86/interface/api/18456)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (preferences: Preferences): Promise<EnhancedAxiosResponse<Result>> => {
    const apiData: ApiData = {
      config: JSON.stringify(preferences),
    };

    const { error, data, ...rest } = await request.tryPost<RequestResult, ApiData>(
      endpoint,
      apiData
    );

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: false,
      };
    }

    return { error, data: true, ...rest };
  };
}
