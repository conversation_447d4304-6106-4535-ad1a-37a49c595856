---
description: 'A saveUserAlarmsMonitoringBoardPreferences HTTP API service.'
labels: ['service', 'http', save-user-alarms-monitoring-board-preferences]
---

## 概述

TODO

## 使用

### Browser

```ts
import { saveUserAlarmsMonitoringBoardPreferences } from '@manyun/monitoring.service.save-user-alarms-monitoring-board-preferences';

const { error, data } = await saveUserAlarmsMonitoringBoardPreferences('success');
const { error, data } = await saveUserAlarmsMonitoringBoardPreferences('error');
```

### Node

```ts
import { saveUserAlarmsMonitoringBoardPreferences } from '@manyun/monitoring.service.save-user-alarms-monitoring-board-preferences/dist/index.node';

const { data } = await saveUserAlarmsMonitoringBoardPreferences('success');

try {
  const { data } = await saveUserAlarmsMonitoringBoardPreferences('error');
} catch (error) {
  // ...
}
```
