/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-26
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './save-user-alarms-monitoring-board-preferences';
import type { Preferences, Result } from './save-user-alarms-monitoring-board-preferences.type';

const executor = getExecutor(nodeRequest);

/**
 * @param preferences
 * @returns
 */
export function saveUserAlarmsMonitoringBoardPreferences(
  preferences: Preferences
): Promise<EnhancedAxiosResponse<Result>> {
  return executor(preferences);
}
