---
description: 'A fetchDiffPointData HTTP API service.'
labels: ['service', 'idc', 'point-value-diff']
---

## 概述

获取点位数据一段时间差值

## 使用

### Browser

```ts
import { fetchDiffPointData } from '@manyun/monitoring.service.fetch-diff-point-data';

const { error, data } = await fetchDiffPointData({
  endTime: 1640587680000,
  pointGuidList: [
    {
      deviceGuid: 'EC06',
      pointCode: '1031000',
    },
    {
      deviceGuid: 'EC06',
      pointCode: '1027000',
    },
    {
      deviceGuid: 'EC06.A',
      pointCode: '1134000',
    },
    {
      deviceGuid: 'EC06.A',
      pointCode: '1085000',
    },
  ],
  startTime: 1640587620000,
});
```
