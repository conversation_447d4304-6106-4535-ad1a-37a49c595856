/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-28
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchDiffPointData as webService } from './fetch-diff-point-data.browser';

let mockOff: () => void;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

test('should resolve chanshou success response', async () => {
  const { error, data } = await webService({
    idcTag: 'EC06',
    endTime: 1640587680000,
    pointGuidList: [
      {
        deviceGuid: 'EC06',
        pointCode: '1031000',
      },
      {
        deviceGuid: 'EC06',
        pointCode: '1027000',
      },
      {
        deviceGuid: 'EC06.A',
        pointCode: '1134000',
      },
      {
        deviceGuid: 'EC06.A',
        pointCode: '1085000',
      },
    ],
    startTime: 1640587620000,
  });

  expect(error).toBe(undefined);
  expect(data['EC06.1031000']).toBe(50);
});

test('should resolve chanshou error response', async () => {
  const { error } = await webService({
    idcTag: 'EC06',
    endTime: 1640587680001,
    pointGuidList: [
      {
        deviceGuid: 'EC06',
        pointCode: '1031000',
      },
      {
        deviceGuid: 'EC06',
        pointCode: '1027000',
      },
      {
        deviceGuid: 'EC06.A',
        pointCode: '1134000',
      },
      {
        deviceGuid: 'EC06.A',
        pointCode: '1085000',
      },
    ],
    startTime: 1640587620000,
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
