/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-28
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { endpoint } from './fetch-diff-point-data';
import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-diff-point-data.type';

/**
 * 获取点位数据一段时间差值
 *
 * @see [获取点位数据一段时间差值](http://172.16.0.17:13000/project/86/interface/api/5488)
 *
 * @param query Service query object
 * @returns
 */
export async function fetchDiffPointData({
  idcTag,
  ...props
}: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  const apiQ = props;

  const { error, data, ...rest } = await webRequest.tryPost<RequestRespData, ApiQ>(endpoint, apiQ, {
    headers: {
      'X-IDC': idcTag,
    },
  });

  if (error || data === null) {
    return {
      ...rest,
      error,
      data: apiQ.pointGuidList.reduce<SvcRespData>((newMapper, pointGuid) => {
        const newKey: string = `${pointGuid.deviceGuid}.${pointGuid.pointCode}`;
        newMapper[newKey] = '--';
        return newMapper;
      }, {}),
    };
  }

  return { error, data, ...rest };
}
