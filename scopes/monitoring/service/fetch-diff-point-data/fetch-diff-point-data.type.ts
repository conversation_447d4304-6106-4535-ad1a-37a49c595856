/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-28
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type PointGuid = {
  deviceGuid: string;
  pointCode: string;
};

export type ApiQ = {
  startTime: number;
  endTime: number;
  pointGuidList: PointGuid[];
};

export type SvcQuery = {
  idcTag: string;
} & ApiQ;

export type SvcRespData = Record<string, number | string>;

export type RequestRespData = SvcRespData | null;

export type ApiR = Response<RequestRespData>;
