/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-29
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './delete-channel-point';
import type { SvcQuery, SvcRespData } from './delete-channel-point.type';

const executor = getExecutor(webRequest);

/**
 * @param params
 * @returns
 */
export function deleteChannelPoint(params: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(params);
}
