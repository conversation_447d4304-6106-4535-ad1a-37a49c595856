/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-29
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = ApiQ;

export type SvcRespData = RequestRespData;

export type RequestRespData = boolean | null;

export type ApiQ = {
  channelId: string; //只传channelId删除通道下所有
  deviceGuid?: string; //只传deviceGuid删除设备下所有
  pointCode?: string;
};

export type ApiR = WriteResponse;
