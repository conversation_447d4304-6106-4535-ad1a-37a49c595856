/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-29
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './delete-channel-point.type';

const endpoint = '/dccm/channel/point/delete';

/**
 * @see [删除通道下点位](https://manyun.yuque.com/ewe5b3/sbs6q1/kguhhd#MSSFk)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (params: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    return { error, data, ...rest };
  };
}
