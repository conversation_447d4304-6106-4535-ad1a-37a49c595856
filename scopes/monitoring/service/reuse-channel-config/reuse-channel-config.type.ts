/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-30
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = ApiQ;

export type SvcRespData = RequestRespData;

export type RequestRespData = boolean | null;

export type ApiQ = {
  sourceChannelId: string;
  targetChannelIds: string[];
  copyConfig?: boolean;
  copyPoint?: boolean;
};

export type ApiR = WriteResponse;
