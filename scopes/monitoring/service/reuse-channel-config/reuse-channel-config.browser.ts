/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-30
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './reuse-channel-config';
import type { SvcQuery, SvcRespData } from './reuse-channel-config.type';

const executor = getExecutor(webRequest);

/**
 * @param params
 * @returns
 */
export function reuseChannelConfig(params: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(params);
}
