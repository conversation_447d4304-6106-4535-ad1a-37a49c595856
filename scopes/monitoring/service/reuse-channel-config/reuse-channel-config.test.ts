/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-30
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { reuseChannelConfig as webService } from './reuse-channel-config.browser';
import { reuseChannelConfig as nodeService } from './reuse-channel-config.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    sourceChannelId: '4605',
    targetChannelIds: ['4455'],
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { data } = await webService({
    sourceChannelId: '1',
    targetChannelIds: ['1'],
  });

  expect(data).toBe(null);
});

test('[web] should resolve success response', async () => {
  const { error, data } = await nodeService({
    sourceChannelId: '4605',
    targetChannelIds: ['4455'],
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { data } = await nodeService({
    sourceChannelId: '1',
    targetChannelIds: ['1'],
  });

  expect(data).toBe(null);
});
