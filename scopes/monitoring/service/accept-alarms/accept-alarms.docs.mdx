---
description: 'A acceptAlarms HTTP API service.'
labels: ['service', 'http', accept-alarms]
---

## 概述

TODO

## 使用

### Browser

```ts
import { acceptAlarms } from '@manyun/monitoring.service.accept-alarms';

const { error, data } = await acceptAlarms('success');
const { error, data } = await acceptAlarms('error');
```

### Node

```ts
import { acceptAlarms } from '@manyun/monitoring.service.accept-alarms/dist/index.node';

const { data } = await acceptAlarms('success');

try {
  const { data } = await acceptAlarms('error');
} catch (error) {
  // ...
}
```
