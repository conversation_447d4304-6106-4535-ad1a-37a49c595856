/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-24
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './accept-alarms';
import type { SvcData, SvcRespData } from './accept-alarms.type';

const executor = getExecutor(webRequest);

/**
 * @param svcData
 * @returns
 */
export function acceptAlarms(svcData: SvcData): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcData);
}
