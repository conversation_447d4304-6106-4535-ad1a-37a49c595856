/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-24
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiData, RequestRespData, SvcData, SvcRespData } from './accept-alarms.type';

const endpoint = '/dcim/alarm/batch/confirm';

/**
 * @see [Doc](http://172.16.0.17:13000/project/86/interface/api/2128)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async ({ alarmIds }: SvcData): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const apiData: ApiData = { alarmIds };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiData>(
      endpoint,
      apiData
    );

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: false,
      };
    }

    return { error, data: true, ...rest };
  };
}
