/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-23
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-point-values-by-guids';
import type { SvcQuery, SvcRespData } from './fetch-point-values-by-guids.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function fetchPointValuesByGuids(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
