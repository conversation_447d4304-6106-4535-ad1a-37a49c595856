/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-23
 *
 * @packageDocumentation
 */
import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './fetch-point-values-by-guids.type';

const endpoint = '/dcsocket/point/value/fetch/v2';

/**
 * @see [Doc](http://172.16.0.17:13000/project/53/interface/api/20730)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const apiQ: ApiQ = new FormData();
    apiQ.append('deviceGuidList', svcQuery.guids.join(','));

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ, { idc: string }>(
      endpoint,
      apiQ,
      {
        headers: { token: env.DCSOCKET_TOKEN!, 'X-IDC': svcQuery.idc },
      }
    );

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: {},
      };
    }

    return { error, data, ...rest };
  };
}
