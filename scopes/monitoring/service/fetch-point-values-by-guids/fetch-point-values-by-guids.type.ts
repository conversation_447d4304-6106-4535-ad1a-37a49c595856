/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-23
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import type { BackendRealtimePointData } from '@manyun/monitoring.model.monitoring-data';

export type SvcQuery = {
  idc: string;
  /** 空间/设备 guid 数组 */
  guids: string[];
};

export type SvcRespData = BackendRealtimePointData;

export type RequestRespData = BackendRealtimePointData | null;

export type ApiQ = FormData;

export type ApiR = Response<BackendRealtimePointData | null>;
