/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-29
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = string;

export type SvcRespData = {
  hlsUrl: string;
  sessionId: string;
} | null;

export type RequestRespData = {
  hlsUrl: string;
  sessionId: string;
} | null;

export type ApiQ = {
  guid: string;
};

export type ApiR = Response<RequestRespData>;
