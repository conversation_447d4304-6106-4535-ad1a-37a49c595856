/**
 * <AUTHOR> Name <<EMAIL>>
 * @since 2022-7-1
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-alarm-point';
import type { SvcQuery, SvcRespData } from './fetch-alarm-point.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function fetchAlarmPoint(variant?: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
