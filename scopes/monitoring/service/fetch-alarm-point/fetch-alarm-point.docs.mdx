---
description: 'A fetchAlarmPoint HTTP API service.'
labels: ['service', 'http', fetch-alarm-point]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchAlarmPoint } from '@manyun/monitoring.service.fetch-alarm-point';

const { error, data } = await fetchAlarmPoint('success');
const { error, data } = await fetchAlarmPoint('error');
```

### Node

```ts
import { fetchAlarmPoint } from '@manyun/monitoring.service.fetch-alarm-point/dist/index.node';

const { data } = await fetchAlarmPoint('success');

try {
  const { data } = await fetchAlarmPoint('error');
} catch (error) {
  // ...
}
```
