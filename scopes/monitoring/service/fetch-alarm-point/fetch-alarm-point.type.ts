/**
 * <AUTHOR> Name <<EMAIL>>
 * @since 2022-7-1
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import { Alarm } from '@manyun/monitoring.model.alarm';
import type { BackendAlarm } from '@manyun/monitoring.model.alarm';

export type SvcQuery = {
  triggerGuid: string;
  pointCode: string;
  startTime: number;
  endTime: number;
};

export type SvcRespData = {
  data: Alarm[];
  total: number;
};

export type RequestRespData = {
  data: Alarm[] | null;
  total: number;
} | null;

export type ApiQ = {
  variant?: 'success' | 'error';
};

export type ApiR = ListResponse<BackendAlarm[] | null>;
