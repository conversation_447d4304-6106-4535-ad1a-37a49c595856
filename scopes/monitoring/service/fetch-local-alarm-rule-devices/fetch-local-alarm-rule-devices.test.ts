/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-23
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchLocalAlarmRuleDevices as webService } from './fetch-local-alarm-rule-devices.browser';
import { fetchLocalAlarmRuleDevices as nodeService } from './fetch-local-alarm-rule-devices.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    pageNum: 1,
    pageSize: 10,
    itemId: '89',
    blockGuid: 'EC06.A',
  });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({
    pageNum: 1,
    pageSize: 10,
    itemId: '89',
    blockGuid: 'EC06.A',
  });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});
