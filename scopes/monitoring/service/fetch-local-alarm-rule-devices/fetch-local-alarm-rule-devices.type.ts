/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-23
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type LocalAlarmRuleDevice = {
  deviceGuid: string;
  deviceName: string;
  deviceTag: string;
  vendor: string;
  productModel: string;
  spaceGuid: string;
  roomName: string;
  roomTag: string;
  deviceType: string;
  enable: boolean;
};

export type SvcQuery = {
  pageNum: number;
  pageSize: number;
  itemId: string;
  blockGuid: string;
  deviceName?: string;
  roomTag?: string;
  enable?: boolean;
};

export type ApiQ = SvcQuery;

export type RequestRespData = {
  data: LocalAlarmRuleDevice[];
  total: number;
};

export type SvcRespData = {
  data: LocalAlarmRuleDevice[];
  total: number;
};

export type ApiR = ListResponse<RequestRespData[] | null>;
