/**
 * <AUTHOR> W <<EMAIL>>
 * @since 2022-7-14
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchTopology as webService } from './fetch-topology.browser';
import { fetchTopology as nodeService } from './fetch-topology.node';
import type { Topology } from './fetch-topology.type';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

const successResultData: Topology = {
  id: 1,
  topologyType: 'ELECTRIC_POWER',
  blockGuids: ['EC06.A'],
  graph: { pages: [] },
  abstractData: { nodeList: [], flowList: [] },
  // 'Thu, 14 Jul 2022 13:42:45 GMT'
  createdAt: 1657806165000,
  updatedAt: null,
};

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ blockGuid: 'EC06.A', topologyType: 'ELECTRIC_POWER' });

  expect(error).toBe(undefined);
  expect(data).toMatchObject(successResultData);
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({
    blockGuid: 'EC06.NOT.EXISTS',
    topologyType: 'ELECTRIC_POWER',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toBeNull();
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({
    blockGuid: 'EC06.A',
    topologyType: 'ELECTRIC_POWER',
  });

  expect(error).toBe(undefined);
  expect(data).toMatchObject(successResultData);
});

test('[node] should resolve error response', async () => {
  const { error, data } = await nodeService({
    blockGuid: 'EC06.NOT.EXISTS',
    topologyType: 'ELECTRIC_POWER',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toBeNull();
});
