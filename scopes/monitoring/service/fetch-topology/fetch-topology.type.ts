/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-14
 *
 * @packageDocumentation
 */
import type { StoreSnapshotOut } from '@manyun/dc-brain.aura-graphix';
import type { BackendTopologyType } from '@manyun/dc-brain.config.base';
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type BackendTopology = {
  id: number;
  topologyType: BackendTopologyType;
  blocks: string[];
  viewJson: string;
  topologyJson: string;
  gmtCreate: string;
  gmtModify: string | null;
};

/**
 * @warning DONOT use this type outside of this service component,
 * because `@<PERSON>` would refactor this type with a `Typology` model
 */
export type Topology = {
  id: number;
  topologyType: BackendTopologyType;
  blockGuids: string[];
  /**
   * 用于绘图的数据源
   */
  graph: StoreSnapshotOut;
  /**
   * 从绘图数据源中抽象出的节点和节点间连线的数据
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  abstractData: any;
  createdAt: number;
  updatedAt: number | null;
};

export type SvcRespData = Topology | null;

export type RequestRespData = BackendTopology | null;

export type ApiQ = {
  tenantId?: string | null;
  blockGuid: string;
  topologyType: BackendTopologyType;
};

export type ApiR = Response<BackendTopology | null>;
