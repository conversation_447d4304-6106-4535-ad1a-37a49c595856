/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-14
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-topology';
import type { ApiQ, SvcRespData } from './fetch-topology.type';

const executor = getExecutor(nodeRequest);

/**
 * @param apiQ
 * @returns
 */
export function fetchTopology(apiQ: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(apiQ);
}
