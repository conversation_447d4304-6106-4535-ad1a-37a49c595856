---
description: 'A fetchTopology HTTP API service.'
labels: ['service', 'http']
---

## 概述

查询楼对应的拓扑

## 使用

### Browser

```ts
import { fetchTopology } from '@manyun/monitoring.service.fetch-topology';

const { error, data } = await fetchTopology({
  blockGuid: 'EC06.A',
  topologyType: 'ELECTRIC_POWER',
});
```

### Node

```ts
import { fetchTopology } from '@manyun/monitoring.service.fetch-topology/dist/index.node';

const { error, data } = await fetchTopology({
  blockGuid: 'EC06.A',
  topologyType: 'ELECTRIC_POWER',
});
```
