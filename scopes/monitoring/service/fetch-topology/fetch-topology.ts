/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-14
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcRespData } from './fetch-topology.type';

const endpoint = '/dccm/topology/view/query';

/**
 * @see [Doc](http://172.16.0.17:13000/project/106/interface/api/5640)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (apiQ: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, apiQ);

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: null,
      };
    }

    try {
      const graph = JSON.parse(data.viewJson);
      const abstractData = JSON.parse(data.topologyJson);

      return {
        error,
        data: {
          id: data.id,
          topologyType: data.topologyType,
          blockGuids: data.blocks,
          graph,
          abstractData,
          createdAt: new Date(data.gmtCreate).getTime(),
          updatedAt: data.gmtModify === null ? null : new Date(data.gmtModify).getTime(),
        },
        ...rest,
      };
    } catch (_error) {
      return {
        ...rest,
        error: {
          code: 'SERVICE_INTERNAL_ERROR',
          message: (_error as Error).message,
        },
        data: null,
      };
    }
  };
}
