/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-26
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './export-local-alarm-rule';
import type { SvcQuery, SvcRespData } from './export-local-alarm-rule.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function exportLocalAlarmRule(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
