/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-26
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { SvcQuery, SvcRespData } from './export-local-alarm-rule.type';

const endpoint = '/dccm/local/monitor/item/export';

/**
 * @see [导出属地监控](http://172.16.0.17:13000/project/228/interface/api/23142)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (params: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    return await request.tryPost<SvcRespData, SvcQuery>(endpoint, params, {
      responseType: 'blob',
    });
  };
}
