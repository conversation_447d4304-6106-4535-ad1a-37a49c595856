/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-26
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { exportLocalAlarmRule as webService } from './export-local-alarm-rule.browser';
import { exportLocalAlarmRule as nodeService } from './export-local-alarm-rule.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    blockGuid: '',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({
    blockGuid: 'EC06.A',
  });
  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({
    blockGuid: '',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
