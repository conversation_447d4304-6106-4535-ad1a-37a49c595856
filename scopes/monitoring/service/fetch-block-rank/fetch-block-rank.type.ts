/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-15
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  // 楼栋
  blockGuid: string;
  // 昨日日期： 格式（2022-11-14）
  time: string;
  // 机房
  idcTag: string;
};

export type BLockRankDataModel = {
  // 楼栋
  blockGuid: string;
  // 区域code
  regionCode: string;
  // 区域排行
  regionRank: number;
  // 全国排行
  totalRank: number;
  // 区域楼栋数量
  regionNum: number;
  // 全国楼栋数量
  totalNum: number;
};

export type SvcRespData = BLockRankDataModel | null;

export type RequestRespData = BLockRankDataModel | null;

export type ApiQ = SvcQuery;

export type ApiR = ListResponse<BLockRankDataModel | null>;
