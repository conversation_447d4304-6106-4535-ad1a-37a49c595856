/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-15
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-block-rank';
import type { SvcQuery, SvcRespData } from './fetch-block-rank.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchBlockRank(variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
