/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-15
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchBlockRank as webService } from './fetch-block-rank.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    blockGuid: 'EC06.A',
    time: '2022-11-14',
    idcTag: 'EC06',
  });

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('regionRank');
});
