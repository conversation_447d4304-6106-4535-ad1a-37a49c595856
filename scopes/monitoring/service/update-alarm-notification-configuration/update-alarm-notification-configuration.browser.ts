/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-1
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './update-alarm-notification-configuration';
import type { SvcQuery, SvcRespData } from './update-alarm-notification-configuration.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function updateAlarmNotificationConfiguration(
  variant: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
