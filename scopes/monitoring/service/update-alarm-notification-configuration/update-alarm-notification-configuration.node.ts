/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-1
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './update-alarm-notification-configuration';
import type { SvcQuery, SvcRespData } from './update-alarm-notification-configuration.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function updateAlarmNotificationConfiguration(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
