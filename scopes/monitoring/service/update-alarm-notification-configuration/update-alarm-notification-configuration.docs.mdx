---
description: 'A updateAlarmNotificationConfiguration HTTP API service.'
labels: ['service', 'http', update-alarm-notification-configuration]
---

## 概述

编辑告警通报配置

## 使用

### Browser

```ts
import { updateAlarmNotificationConfiguration } from '@manyun/monitoring.service.update-alarm-notification-configuration';

const { error, data } = await updateAlarmNotificationConfiguration({
  levelCode: '1',
  items: [
    {
      id: '1',
      name: 'name',
      type: 'ALARM_NOTIFY',
      rule: 0,
      webhook: false,
      notificationObjects: [
        {
          code: '1',
          name: '角色',
          type: 'ROLE',
          status: 'ALL',
          phone: true,
          sms: true,
          internalMsg: false,
          email: false,
        },
      ],
    },
  ],
});
const { error, data } = await updateAlarmNotificationConfiguration({
  levelCode: 'error',
  items: [],
});
```

### Node

```ts
import { updateAlarmNotificationConfiguration } from '@manyun/monitoring.service.update-alarm-notification-configuration/dist/index.node';

const { data } = await updateAlarmNotificationConfiguration({
  levelCode: '1',
  items: [
    {
      id: '1',
      name: 'name',
      type: 'ALARM_NOTIFY',
      rule: 0,
      webhook: false,
      notificationObjects: [
        {
          code: '1',
          name: '角色',
          type: 'ROLE',
          status: 'ALL',
          phone: true,
          sms: true,
          internalMsg: false,
          email: false,
        },
      ],
    },
  ],
});
const { error, data } = await updateAlarmNotificationConfiguration({
  levelCode: 'error',
  items: [],
});
```
