/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-1
 *
 * @packageDocumentation
 */
import {
  BackendNotificationConfigureItem,
  NotificationConfigureItem,
} from '@manyun/monitoring.model.notification-configure-item';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './update-alarm-notification-configuration.type';

const endpoint = '/dcim/alarm/inform/update';

/**
 * @see [Doc](http://172.16.0.17:13000/project/158/interface/api/18708)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      alarmLevel: svcQuery.levelCode,
      informItemList: svcQuery.items.map(item => {
        let backendItem: Omit<BackendNotificationConfigureItem, 'id'> & { id: number | undefined } =
          {
            ...NotificationConfigureItem.toApiObject(item),
          };
        if (typeof item.id === 'string') {
          /**UI 新增的配置项，不需要向后端传递id */
          backendItem.id = undefined;
        }
        return backendItem;
      }),
    };

    return await request.tryPost<RequestRespData, ApiQ>(endpoint, params);
  };
}
