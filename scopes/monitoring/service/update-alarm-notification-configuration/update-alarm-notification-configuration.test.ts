/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-1
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { updateAlarmNotificationConfiguration as webService } from './update-alarm-notification-configuration.browser';
import { updateAlarmNotificationConfiguration as nodeService } from './update-alarm-notification-configuration.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({
    levelCode: '1',
    items: [
      {
        id: '1',
        name: 'name',
        type: 'ALARM_NOTIFY',
        rule: 0,
        webhook: false,
        notificationObjects: [
          {
            code: '1',
            name: '角色',
            type: 'ROLE',
            status: 'ALL',
            phone: true,
            sms: true,
            internalMsg: false,
            email: false,
          },
        ],
      },
    ],
  });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    levelCode: 'error',
    items: [],
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService({
    levelCode: '1',
    items: [
      {
        id: '1',
        name: 'name',
        type: 'ALARM_NOTIFY',
        rule: 0,
        webhook: false,
        notificationObjects: [
          {
            code: '1',
            name: '角色',
            type: 'ROLE',
            status: 'ALL',
            phone: true,
            sms: true,
            internalMsg: false,
            email: false,
          },
        ],
      },
    ],
  });

  expect(error).toBe(undefined);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({
    levelCode: 'error',
    items: [],
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
