/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-1
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type {
  BackendNotificationConfigureItem,
  NotificationConfigureItem,
} from '@manyun/monitoring.model.notification-configure-item';

export type SvcQuery = {
  levelCode: string;
  items: NotificationConfigureItem[];
};

export type SvcRespData = boolean;

export type RequestRespData = boolean;

export type ApiQ = {
  alarmLevel: string;
  informItemList: (Omit<BackendNotificationConfigureItem, 'id'> & { id: number | undefined })[];
};

export type ApiR = WriteResponse;
