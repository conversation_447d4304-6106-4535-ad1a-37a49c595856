/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-1
 *
 * @packageDocumentation
 */
import {
  BackendNotificationConfigureItem,
  NotificationConfigureItem,
} from '@manyun/monitoring.model.notification-configure-item';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './create-alarm-notification-configuration.type';

const endpoint = '/dcim/alarm/inform/create';

/**
 * 创建告警通报配置
 * @see [Doc](http://172.16.0.17:13000/project/158/interface/api/18699)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      alarmLevel: svcQuery.levelCode,
      informItemList: svcQuery.items
        .map(item => NotificationConfigureItem.toApiObject(item))
        .map(item => {
          const optionItem: Omit<BackendNotificationConfigureItem, 'id'> & {
            id?: string | number;
          } = { ...item };
          if (optionItem.id) {
            delete optionItem.id;
          }
          return optionItem;
        }),
    };

    return await request.tryPost<RequestRespData, ApiQ>(endpoint, params);
  };
}
