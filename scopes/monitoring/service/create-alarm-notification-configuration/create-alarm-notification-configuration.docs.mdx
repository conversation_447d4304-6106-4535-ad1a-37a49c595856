---
description: 'A createAlarmNotificationConfiguration HTTP API service.'
labels: ['service', 'http', create-alarm-notification-configuration]
---

## 概述

创建告警通报配置

## 使用

### Browser

```ts
import { createAlarmNotificationConfiguration } from '@manyun/monitoring.service.create-alarm-notification-configuration';

const { error, data } = await createAlarmNotificationConfiguration({
  levelCode: '1',
  items: [
    {
      id: '1',
      name: 'name',
      type: 'ALARM_NOTIFY',
      rule: 0,
      webhook: false,
      notificationObjects: [
        {
          code: '1',
          name: '角色',
          type: 'ROLE',
          status: 'ALL',
          phone: true,
          sms: true,
          internalMsg: false,
          email: false,
        },
      ],
    },
  ],
});
```

### Node

```ts
import { createAlarmNotificationConfiguration } from '@manyun/monitoring.service.create-alarm-notification-configuration/dist/index.node';
const { error, data } = await createAlarmNotificationConfiguration({
  levelCode: '1',
  items: [
    {
      id: '1',
      name: 'name',
      type: 'ALARM_NOTIFY',
      rule: 0,
      webhook: false,
      notificationObjects: [
        {
          code: '1',
          name: '角色',
          type: 'ROLE',
          status: 'ALL',
          phone: true,
          sms: true,
          internalMsg: false,
          email: false,
        },
      ],
    },
  ],
});
```
