---
description: 'A fetchAlarmConfiguration HTTP API service.'
labels: ['service', 'http', fetch-alarm-configuration]
---

## 概述

【告警模板配置】通过告警模板 id 请求告警模板详情

## 使用

### Browser

```ts
import { fetchAlarmConfiguration } from '@manyun/monitoring.service.fetch-alarm-configuration';

const { error, data } = await fetchAlarmConfiguration({ id: 1 });
```

### Node

```ts
import { fetchAlarmConfiguration } from '@manyun/monitoring.service.fetch-alarm-configuration/dist/index.node';

const { data } = await fetchAlarmConfiguration({ id: 1 });

try {
  const { data } = await fetchAlarmConfiguration({ id: 0 });
} catch (error) {
  // ...
}
```
