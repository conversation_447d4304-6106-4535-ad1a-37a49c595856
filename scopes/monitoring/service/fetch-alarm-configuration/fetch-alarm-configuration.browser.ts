/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-16
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-alarm-configuration';
import type { SvcQuery, SvcRespData } from './fetch-alarm-configuration.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchAlarmConfiguration(
  variant: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
