/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-16
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import { AlarmTemplateJSON, BackendAlarmTemplate } from '@manyun/monitoring.model.alarm-template';

export type SvcQuery = {
  id: number;
};

export type SvcRespData = Omit<AlarmTemplateJSON, 'monitorItems'> | null;

export type RequestRespData = Omit<BackendAlarmTemplate, 'monitorItemList'> | null;

export type ApiQ = {
  id: number;
};

export type ApiR = Response<RequestRespData>;
