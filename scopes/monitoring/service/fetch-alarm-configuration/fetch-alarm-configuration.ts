/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-16
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './fetch-alarm-configuration.type';

const endpoint = '/dccm/monitor/group/query/by/id';

/**
 * 通过告警模板id 请求告警模板详情
 * @see [Doc](http://172.16.0.17:13000/project/136/interface/api/8120)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = { id: variant.id };

    const { error, data, ...rest } = await request.tryGet<RequestRespData, ApiQ>(endpoint, {
      params,
    });

    return {
      error,
      data: data
        ? {
            id: data.id,
            name: data.name,
            type: data.type,
            target: data.target,
            enable: data.available,
            description: data.description,
            associateGroupIds: data.monitorSchemeIds,
          }
        : data,
      ...rest,
    };
  };
}
