/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-17
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { updateProfessionalRuleAvailable } from './update-professional-rule-available.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('should resolve error response', async () => {
  const { error } = await updateProfessionalRuleAvailable({
    ids: [Number.NaN],
    available: false,
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
