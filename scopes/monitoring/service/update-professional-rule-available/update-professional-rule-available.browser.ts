/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-17
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './update-professional-rule-available';
import type { SvcQuery, SvcRespData } from './update-professional-rule-available.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function updateProfessionalRuleAvailable(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
