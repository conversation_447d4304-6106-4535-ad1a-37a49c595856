/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-25
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-channel-protocol-type';
import type { SvcRespData } from './fetch-channel-protocol-type.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchChannelProtocolType(): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor();
}
