/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-21
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  //场景id
  id: number;
};
export type OpsPoint = {
  idcTag: string;
  blockTag: string;
  roomTag: string;
  deviceGuid: string;
  pointCode: string;
  deviceName: string;
  pointName: string;
};

export type SvcRespData = {
  data: OpsPoint[];
  total: number;
};

export type RequestRespData = {
  data: OpsPoint[] | null;
  total: number;
} | null;

export type ApiR = ListResponse<OpsPoint[] | null>;
