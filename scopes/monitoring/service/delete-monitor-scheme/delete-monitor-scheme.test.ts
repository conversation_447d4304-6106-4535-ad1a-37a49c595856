/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-17
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { deleteMonitorScheme as webService } from './delete-monitor-scheme.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({ id: 1 });

  expect(error).toBe(undefined);
});
