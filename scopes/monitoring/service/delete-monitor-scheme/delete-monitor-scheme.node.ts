/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-17
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './delete-monitor-scheme';
import type { ApiQ, SvcRespData } from './delete-monitor-scheme.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function deleteMonitorScheme(variant: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
