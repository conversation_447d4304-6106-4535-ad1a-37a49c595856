/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-11-22
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './update-block-cool-mode-configs';
import type { ApiArgs, ApiResponse } from './update-block-cool-mode-configs.type';

const executor = getExecutor(webRequest);

/**
 * @param args
 * @returns
 */
export function updateBlockCoolModeConfigs(
  args: ApiArgs
): Promise<EnhancedAxiosResponse<ApiResponse['data']>> {
  return executor(args);
}
