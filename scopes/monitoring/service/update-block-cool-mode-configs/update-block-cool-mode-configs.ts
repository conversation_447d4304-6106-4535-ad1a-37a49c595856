/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-11-22
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiArgs, ApiResponse } from './update-block-cool-mode-configs.type';

const endpoint = '/dccm/block/coolingmode/configs/update';

/**
 * @see [Doc](http://172.16.0.17:13000/project/180/interface/api/25567)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (args: ApiArgs): Promise<EnhancedAxiosResponse<ApiResponse['data']>> => {
    return await request.tryPost<ApiResponse['data'], ApiArgs>(endpoint, args);
  };
}
