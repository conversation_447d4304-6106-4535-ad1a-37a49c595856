---
description: 'A fetchAlarmConfigurationsByDeviceType HTTP API service.'
labels: ['service', 'http', fetch-alarm-configurations-by-device-type]
---

## 概述

根据设备类型查询告警模板

## 使用

### Browser

```ts
import { fetchAlarmConfigurationsByDeviceType } from '@manyun/monitoring.fetch-alarm-configurations-by-device-type';

const { error, data } = await fetchAlarmConfigurationsByDeviceType({ deviceType: '10011' });
const { error, data } = await fetchAlarmConfigurationsByDeviceType({ deviceType: '10010' });
```

### Node

```ts
import { fetchAlarmConfigurationsByDeviceType } from '@manyun/monitoring.fetch-alarm-configurations-by-device-type/dist/index.node';

const { data } = await fetchAlarmConfigurationsByDeviceType({ deviceType: '10011' });

try {
  const { data } = await fetchAlarmConfigurationsByDeviceType({ deviceType: '10011' });
} catch (error) {
  // ...
}
```
