/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-12
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './fetch-alarm-configurations-by-device-type.type';

const generateEndPoint = (deviceType: string) => `dccm/monitor/group/query/${deviceType}`;

/**
 * 根据设备类型查询告警模板
 *
 * @see [Doc](http://172.16.0.17:13000/project/136/interface/api/8128)
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {};

    const endpoint = generateEndPoint(variant.deviceType);

    const { error, data, ...rest } = await request.tryGet<RequestRespData, ApiQ>(endpoint, {
      params,
    });

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      error,
      data: {
        data: data.data.map(d => ({
          id: d.id,
          name: d.name,
          enable: d.available,
          description: d.description,
        })),
        total: data.total,
      },
      ...rest,
    };
  };
}
