/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-12
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import { AlarmTemplate, BackendAlarmTemplate } from '@manyun/monitoring.model.alarm-template';

export type SvcQuery = {
  deviceType: string /**设备类型 */;
};

export type SvcRespData = {
  data: Pick<AlarmTemplate, 'id' | 'name' | 'enable' | 'description'>[];
  total: number;
};

export type RequestRespData = {
  data: Pick<BackendAlarmTemplate, 'available' | 'description' | 'id' | 'name'>[] | null;
  total: number;
} | null;

export type ApiQ = {};

export type ApiR = ListResponse<RequestRespData>;
