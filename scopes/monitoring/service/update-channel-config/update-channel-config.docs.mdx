---
description: 'A updateChannelConfig HTTP API service.'
labels: ['service', 'http']
---

## 概述

通道编辑

## 使用

### Browser

```ts
import { updateChannelConfig } from '@manyun/monitoring.service.update-channel-config';

const { error, data } = await updateChannelConfig({
  channelId: '455',
  blockGuid: 'CQ1.A',
  channelName: '测试',
  channelStatus: 'OFF',
  channelType: 'HVAC',
  ip: '***********',
  port: '8080',
  protocol: 'MODBUS_TCP_SYNC',
});
```
