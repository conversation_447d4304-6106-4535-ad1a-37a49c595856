/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-11
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { BackendAlarmLifecycleState } from '@manyun/monitoring.model.alarm';

export type SvcQuery = {
  idcTag: string;
  blockTag?: string;
};

export type AlarmStatistic = {
  name: string;
  status: BackendAlarmLifecycleState;
  value: number;
  code: string;
};

export type SvcRespData = {
  data: AlarmStatistic[];
  total: number;
};

export type RequestRespData = {
  data: AlarmStatistic[] | null;
  total: number;
} | null;

export type ApiR = ListResponse<AlarmStatistic[] | null>;
