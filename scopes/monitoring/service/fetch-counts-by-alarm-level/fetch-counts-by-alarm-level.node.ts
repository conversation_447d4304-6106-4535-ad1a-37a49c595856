/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-11
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-counts-by-alarm-level';
import type { SvcQuery, SvcRespData } from './fetch-counts-by-alarm-level.type';

const executor = getExecutor(nodeRequest);

/**
 * @param params
 * @returns
 */
export function fetchCountsByAlarmLevel(
  params: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(params);
}
