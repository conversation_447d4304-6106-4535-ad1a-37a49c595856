---
description: 'A fetchCountsByAlarmLevel HTTP API service.'
labels: ['service', 'http']
---

## 概述

根据机房或楼栋维度查询的按告警类型划分的数量统计

## 使用

### Browser

```ts
import { fetchCountsByAlarmLevel } from '@manyun/monitoring.service.fetch-counts-by-alarm-level';

const { error, data } = await fetchCountsByAlarmLevel({ idcTag: 'EC01' });
const { error, data } = await fetchCountsByAlarmLevel({ idcTag: 'EC02' });
```

### Node

```ts
import { fetchCountsByAlarmLevel } from '@manyun/monitoring.service.fetch-counts-by-alarm-level/dist/index.node';

const { data } = await fetchCountsByAlarmLevel({ idcTag: 'EC01' });
const { error, data } = await fetchCountsByAlarmLevel({ idcTag: 'EC02' });
```
