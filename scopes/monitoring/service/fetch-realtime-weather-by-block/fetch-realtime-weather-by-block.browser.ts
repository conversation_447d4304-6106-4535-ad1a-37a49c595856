/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-17
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-realtime-weather-by-block';
import type { SvcQuery, SvcRespData } from './fetch-realtime-weather-by-block.type';

const executor = getExecutor(webRequest);

/**
 * @param params
 * @returns
 */
export function fetchRealtimeWeatherByBlock(
  params: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(params);
}
