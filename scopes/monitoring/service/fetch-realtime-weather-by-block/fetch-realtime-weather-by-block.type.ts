/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-17
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  blockGuid: string;
};

export type BlockWeather = {
  condition: string;
  conditionId: string;
  humidity: string;
  icon: string;
  pressure: string;
  realFeel: string;
  sunRise: string;
  sunSet: string;
  temp: string;
  tips: string;
  updateTime: string | null;
  uvi: string;
  vis: string;
  windDegrees: string;
  windDir: string;
  windLevel: string;
  windSpeed: string;
};

export type SvcRespData = BlockWeather | null;

export type RequestRespData = SvcRespData;

export type ApiR = Response<SvcRespData>;
