/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-17
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { DataType } from '@manyun/monitoring.model.point';

export type SvcQuery = {
  // 是否非标
  nonPoint: boolean;
  // 测点ID
  code?: string;
  // 协议测点名称
  name: string;
  // 协议测点类型
  dataType: DataType;
  blockGuid: string;
  deviceType: string;
  // 型号
  productModel: string;
  unit?: string;
  // 值含义
  validLimits?: string;
  // 表达式
  formula?: string;
  // 是否采集
  capture: boolean;
};

export type ApiR = WriteResponse;

export type SvcRespData = boolean;
