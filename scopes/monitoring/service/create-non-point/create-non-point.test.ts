/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-17
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { createNonPoint as webService } from './create-non-point.browser';
import { createNonPoint as nodeService } from './create-non-point.node';
import { SvcQuery } from './create-non-point.type';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

const params: SvcQuery = {
  nonPoint: true,
  code: '100101',
  name: 'TEST',
  dataType: 'AI',
  blockGuid: 'EC06.A',
  deviceType: '10101',
  productModel: '100100',
  capture: true,
};

test('[web] should resolve success response', async () => {
  const { error, data } = await webService(params);

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    ...params,
    code: '',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService(params);
  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({
    ...params,
    code: '',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
