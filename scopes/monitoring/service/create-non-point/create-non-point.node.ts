/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-17
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './create-non-point';
import type { SvcQuery, SvcRespData } from './create-non-point.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function createNonPoint(svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
