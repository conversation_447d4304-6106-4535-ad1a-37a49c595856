/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-12-5
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { BackendAlarmNotificationConfigureType } from '@manyun/monitoring.model.notification-configure-item';

export type Channel = 'PHONE' | 'SMS' | 'EMAIL' | 'INTERNAL_MESSAGE';
export type DutyStatus = 'ALL' | 'ON_DUTY' | 'NOT_ON_DUTY';

export type ApiArgs = {
  /**
   * 通报项目名称
   */
  name?: string;
  /**
   * 机房楼栋
   */
  targetIds?: string[];
  /**
   * 通报类型
   */
  types?: string[];
  /**
   * 是否启用
   */
  enable?: boolean;
  pageSize: number;
  pageNum: number;
};

export type LocalAlarmNotice = {
  /**
   * 通报项id
   */
  id: number;
  /**
   * 目标机房或楼栋
   */
  targetId?: string;
  /**
   * 目标类型
   */
  targetType: string;
  /**
   * 通报项名称
   */
  name: string;
  /**
   * 通报类型
   */
  type: BackendAlarmNotificationConfigureType;
  /**
   * 通报规则时间（单位：秒）
   */
  rule: number;
  /**
   * 适用告警等级（逗号隔开）
   */
  alarmLevels: string;
  /**
   * 适用专业类型（逗号隔开）
   */
  deviceType?: string;
  /**
   * 适用专业类型描述（逗号隔开）
   */
  deviceTypeDesc: string;
  /**
   * true-启用，false不启用
   */
  notified: boolean;
  /**
   * webhook
   */
  webhook: boolean;
  /** webhook id列表，多个逗号隔开 */
  webhookId?: string;
  schemeList?: { id: number; name: string }[];
  /**
   * 操作人id
   */
  operatorId: number;
  /**
   * 操作人
   */
  operatorName: string;
  /**
   * 创建时间
   */
  gmtCreate: string;
  /**
   * 修改时间
   */
  gmtModified: string;
};

export type ApiResponse = ListResponse<LocalAlarmNotice[]>;
