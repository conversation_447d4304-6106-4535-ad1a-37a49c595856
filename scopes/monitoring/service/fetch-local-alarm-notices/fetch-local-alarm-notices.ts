/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-12-5
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiArgs, ApiResponse } from './fetch-local-alarm-notices.type';

const endpoint = '/dccm/alarm/inform/list/local';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/158/interface/api/25819)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (
    args: ApiArgs
  ): Promise<EnhancedAxiosResponse<Pick<ApiResponse, 'data' | 'total'>>> => {
    const { error, data, ...rest } = await request.tryPost<
      Pick<ApiResponse, 'data' | 'total'> | null,
      ApiArgs
    >(endpoint, args);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return { error, data: { data: data.data, total: data.total }, ...rest };
  };
}
