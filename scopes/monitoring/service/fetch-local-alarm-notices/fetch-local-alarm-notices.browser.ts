/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-12-5
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-local-alarm-notices';
import type { ApiArgs, ApiResponse } from './fetch-local-alarm-notices.type';

const executor = getExecutor(webRequest);

/**
 * @param args
 * @returns
 */
export function fetchLocalAlarmNotices(
  args: ApiArgs
): Promise<EnhancedAxiosResponse<Pick<ApiResponse, 'data' | 'total'>>> {
  return executor(args);
}
