/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-1
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './export-channel-device';
import type { SvcQuery, SvcRespData } from './export-channel-device.type';

const executor = getExecutor(nodeRequest);

/**
 * @param params
 * @returns
 */
export function exportChannelDevice(params: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(params);
}
