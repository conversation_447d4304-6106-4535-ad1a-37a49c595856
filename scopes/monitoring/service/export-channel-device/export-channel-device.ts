/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-1
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './export-channel-device.type';

const endpoint = '/dccm/channel/device/point/export';

/**
 * @see [设备信息导出](http://172.16.0.17:13000/project/182/interface/api/19430)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (params: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryGet<RequestRespData, ApiQ>(endpoint, {
      params,
      responseType: 'blob',
    });

    return { error, data, ...rest };
  };
}
