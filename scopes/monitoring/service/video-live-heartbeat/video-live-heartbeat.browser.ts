/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-29
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './video-live-heartbeat';
import type { SvcQuery, SvcRespData } from './video-live-heartbeat.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function videoLiveHeartbeat(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
