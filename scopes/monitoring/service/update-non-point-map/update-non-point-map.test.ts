/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-17
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { updateNonPointMap as webService } from './update-non-point-map.browser';
import { updateNonPointMap as nodeService } from './update-non-point-map.node';
import { SvcQuery } from './update-non-point-map.type';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});
const params: SvcQuery = {
  id: 1001,
  code: '1001000',
  formula: 'TEST',
  capture: true,
  nonPoint: true,
};

test('[web] should resolve success response', async () => {
  const { error, data } = await webService(params);

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    ...params,
    code: '',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService(params);
  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({
    ...params,
    code: '',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
