/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-17
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  id: number;
  // 是否非标
  nonPoint: boolean;
  // 测点ID
  code?: string;
  // 表达式
  formula?: string;
  updateChannelPoint: boolean;
  invalidValues?: string;
};

export type ApiR = WriteResponse;

export type SvcRespData = boolean;
