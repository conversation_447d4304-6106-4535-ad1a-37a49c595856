/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-17
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './update-non-point-map';
import type { SvcQuery, SvcRespData } from './update-non-point-map.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function updateNonPointMap(svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
