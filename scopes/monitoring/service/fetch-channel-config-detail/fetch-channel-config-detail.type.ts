/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-28
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type {
  BackendChannelConfig,
  ChannelConfigJSON,
} from '@manyun/monitoring.model.channel-config';

export type SvcQuery = {
  channelIds: string[];
};

export type SvcRespData = {
  data: ChannelConfigJSON[];
  total: number;
};

export type RequestRespData = {
  data: BackendChannelConfig[] | null;
  total: number;
} | null;

export type ApiQ = SvcQuery;

export type ApiR = ListResponse<ChannelConfigJSON[] | null>;
