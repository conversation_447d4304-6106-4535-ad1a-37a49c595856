/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-12
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './create-alarm-configuration';
import type { SvcQuery, SvcRespData } from './create-alarm-configuration.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function createAlarmConfiguration(
  variant: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
