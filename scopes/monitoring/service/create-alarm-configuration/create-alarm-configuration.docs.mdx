---
description: 'A createAlarmConfiguration HTTP API service.'
labels: ['service', 'http', create-alarm-configuration]
---

## 概述

新增告警模板配置

## 使用

### Browser

```ts
import { createAlarmConfiguration } from '@manyun/monitoring.create-alarm-configuration';

const { error, data } = await createAlarmConfiguration({
  id: undefined,
  name: '',
  target: '',
  enable: false,
  type: AlarmTemplateType.Device,
  monitorItems: [],
});
```

### Node

```ts
import { createAlarmConfiguration } from '@manyun/monitoring.create-alarm-configuration/dist/index.node';

const { data } = await createAlarmConfiguration({
  id: undefined,
  name: '',
  target: '',
  enable: false,
  type: AlarmTemplateType.Device,
  monitorItems: [],
});
```
