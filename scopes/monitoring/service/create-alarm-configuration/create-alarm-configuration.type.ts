/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-12
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import {
  AlarmTemplate,
  BackendAlarmTemplate,
  BackendStaticAlarmTemplate,
  StaticAlarmTemplate,
} from '@manyun/monitoring.model.alarm-template';

export type SvcQuery = Pick<
  AlarmTemplate,
  | 'id'
  | 'name'
  | 'target'
  | 'associateGroupIds'
  | 'description'
  | 'enable'
  | 'type'
  | 'monitorItems'
>;

export type SvcRespData = StaticAlarmTemplate | null;

export type RequestRespData = BackendStaticAlarmTemplate | null;

export type ApiQ = Pick<
  BackendAlarmTemplate,
  | 'id'
  | 'name'
  | 'target'
  | 'description'
  | 'available'
  | 'monitorItemList'
  | 'monitorSchemeIds'
  | 'type'
>;

export type ApiR = Response<RequestRespData>;
