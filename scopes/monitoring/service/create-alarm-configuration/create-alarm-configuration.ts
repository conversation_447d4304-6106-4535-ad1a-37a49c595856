/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-12
 *
 * @packageDocumentation
 */
import { AlarmTemplate } from '@manyun/monitoring.model.alarm-template';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './create-alarm-configuration.type';

const endpoint = '/dccm/monitor/group/add';

/**
 * 新增告警模板配置
 * @see [Doc](http://172.16.0.17:13000/project/140/interface/api/15120)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = AlarmTemplate.toApiObject(variant);

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    return {
      error,
      data,
      ...rest,
    };
  };
}
