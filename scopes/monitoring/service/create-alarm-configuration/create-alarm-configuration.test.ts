/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-12
 *
 * @packageDocumentation
 */
import { AlarmTemplateType } from '@manyun/monitoring.model.alarm-template';
import { useRemoteMock } from '@manyun/service.request';

import { createAlarmConfiguration as webService } from './create-alarm-configuration.browser';
import { createAlarmConfiguration as nodeService } from './create-alarm-configuration.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    id: undefined,
    name: '',
    target: '',
    enable: false,
    type: AlarmTemplateType.Device,
    monitorItems: [],
  });

  expect(error).toBe(undefined);
});

// test('[web] should resolve error response', async () => {
//   const { error, data } = await webService('error');

//   expect(typeof error!.code).toBe('string');
//   expect(typeof error!.message).toBe('string');
//   expect(data.data).toHaveProperty('length');
//   expect(typeof data.total).toBe('number');
// });

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({
    id: undefined,
    name: '',
    target: '',
    enable: false,
    type: AlarmTemplateType.Device,
    monitorItems: [],
  });

  expect(error).toBe(undefined);
});

// test('[node] should resolve error response', async () => {
//   const { error, data } = await nodeService('error');

//   expect(typeof error!.code).toBe('string');
//   expect(typeof error!.message).toBe('string');
//   expect(data.data).toHaveProperty('length');
//   expect(typeof data.total).toBe('number');
// });
