/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-16
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export enum BatteryPackChargeType {
  /**充电**/
  Charge = 1,
  /**放电**/
  DisCharge = 2,
}

export type BackendBatteryPackChargeRecord = {
  id: number;
  type: BatteryPackChargeType;
  startTime: number;
  endTime: number;
  startSOCValue: number;
  endSOCValue: number;
};

export type BatteryPackChargeRecord = {
  index: number;
  typeName: string;
  formatStartTime: string;
  formatEndTime: string;
  formatStartSOCValue: string;
  formatEndSOCValue: string;
  continueTime: string;
  soc: string;
} & BackendBatteryPackChargeRecord;

export type SvcRespData = {
  data: BatteryPackChargeRecord[];
  total: number;
};

export type RequestRespData = {
  data: BatteryPackChargeRecord[] | null;
  total: number;
} | null;

export type ApiQ = {
  deviceGuid: string;
  pointCode: string;
  type?: BatteryPackChargeType;
  startTime?: number;
  endTime?: number;
};

export type ApiR = ListResponse<BatteryPackChargeRecord[] | null>;
