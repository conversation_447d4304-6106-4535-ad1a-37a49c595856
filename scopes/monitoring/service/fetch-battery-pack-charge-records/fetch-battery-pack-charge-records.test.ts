/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-16
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchBatteryPackChargeRecords as webService } from './fetch-battery-pack-charge-records.browser';
import { fetchBatteryPackChargeRecords as nodeService } from './fetch-battery-pack-charge-records.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    deviceGuid: '1080701',
    pointCode: '2001000',
  });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({
    deviceGuid: '1080701',
    pointCode: '2001000',
  });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});
