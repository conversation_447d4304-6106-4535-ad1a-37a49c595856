/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-16
 *
 * @packageDocumentation
 */
import isnil from 'lodash.isnil';
import moment from 'moment';

import { formatInterval } from '@manyun/base-ui.util.date-fns.format-interval';

import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import {
  ApiQ,
  BatteryPackChargeType,
  RequestRespData,
  SvcRespData,
} from './fetch-battery-pack-charge-records.type';

const endpoint = '/dcom/point/event/list';

/**
 * @see [电池充放电记录](http://172.16.0.17:13000/project/146/interface/api/19893)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      deviceGuid: svcQuery.deviceGuid,
      pointCode: svcQuery.pointCode,
      type: svcQuery.type,
      startTime: svcQuery.startTime,
      endTime: svcQuery.endTime,
    };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      error,
      data: {
        data: data.data.map((chargeRecord, index) => ({
          ...chargeRecord,
          index: index + 1,
          typeName: chargeRecord.type === BatteryPackChargeType.Charge ? '充电' : '放电',
          formatStartTime: moment(chargeRecord.startTime).format('YYYY-MM-DD HH:mm:ss'),
          formatEndTime: moment(chargeRecord.endTime).format('YYYY-MM-DD HH:mm:ss'),
          continueTime:
            !isnil(chargeRecord.startTime) && !isnil(chargeRecord.endTime)
              ? formatInterval((+chargeRecord.endTime - +chargeRecord.startTime) / 1000)
              : '--',
          formatStartSOCValue: chargeRecord.startSOCValue ? `${chargeRecord.startSOCValue}%` : '--',
          formatEndSOCValue: chargeRecord.endSOCValue ? `${chargeRecord.endSOCValue}%` : '--',
          soc:
            !isnil(chargeRecord.endSOCValue) && !isnil(chargeRecord.startSOCValue)
              ? (+chargeRecord.endSOCValue - +chargeRecord.startSOCValue).toFixed(2) + '%'
              : '--',
        })),
        total: data.total,
      },
      ...rest,
    };
  };
}
