/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-16
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-battery-pack-charge-records';
import type { ApiQ, SvcRespData } from './fetch-battery-pack-charge-records.type';

const executor = getExecutor(webRequest);

/**
 * @returns
 */
export function fetchBatteryPackChargeRecords(
  params: ApiQ
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(params);
}
