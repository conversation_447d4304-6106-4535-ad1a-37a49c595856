/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-17
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './import-non-point';
import type { ApiQ, SvcRespData } from './import-non-point.type';

const executor = getExecutor(nodeRequest);

/**
 * @param  svcQuery
 * @returns
 */
export function importNonPoint(svcQuery: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
