/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-17
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type ImportedNonPint = {
  // 设备型号
  productModel: string;
  name: string;
  // 测点类型
  dataType: string;
  unit: string;
  // 状态量值含义
  validLimits: string;
  deviceType: string;
  code: string;
  // 加工表达式
  formula: string;
  pointName: string;
  invalidValues?: string;
};
export type ApplyImportExcelCheck = {
  errMessage: Record<string, string>;
  errDto: ImportedNonPint;
  rowTag: number;
};

export type ApplyNonPintImport = {
  checkTotal: number;
  correctTotal: number;
  excelCheckErrDtos: ApplyImportExcelCheck[];
  faultTotal: number;
};

export type SvcRespData = ApplyNonPintImport;

export type RequestRespData = ApplyNonPintImport;
export type ApiQ = FormData;

export type ApiR = Response<RequestRespData>;
