/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-17
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { importNonPoint as webService } from './import-non-point.browser';
import { importNonPoint as nodeService } from './import-non-point.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const fd = new FormData();
  const file: File = new File(['somthing'], 'file.xlsx', { type: 'excel/xlsx' });
  fd.append('file', file);
  const { error } = await webService({
    file: fd,
    blockGuid: 'EC06.A',
  });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const fd = new FormData();
  const file: File = new File(['somthing'], 'file.xlsx', { type: 'excel/xlsx' });
  fd.append('file', file);
  const { error } = await webService({
    file: fd,
    blockGuid: '',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const fd = new FormData();
  const file: File = new File(['somthing'], 'file.xlsx', { type: 'excel/xlsx' });
  fd.append('file', file);
  const { error } = await nodeService({
    file: fd,
    blockGuid: 'EC06.A',
  });

  expect(error).toBe(undefined);
});

test('[node] should resolve error response', async () => {
  const fd = new FormData();
  const file: File = new File(['somthing'], 'file.xlsx', { type: 'excel/xlsx' });
  fd.append('file', file);
  const { error } = await nodeService({
    file: fd,
    blockGuid: '',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
