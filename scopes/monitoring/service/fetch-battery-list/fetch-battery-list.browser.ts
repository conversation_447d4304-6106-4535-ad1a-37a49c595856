/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-1-5
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-battery-list';
import type { SvcQuery, SvcRespData } from './fetch-battery-list.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchBatteryList(variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
