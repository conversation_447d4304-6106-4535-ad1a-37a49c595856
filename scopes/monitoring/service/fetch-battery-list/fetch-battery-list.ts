/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-1-5
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { RequestRespData, SvcQuery, SvcRespData } from './fetch-battery-list.type';

const endpoint = '/dccm/view/battery/list/query';

/**
 * @see [查询电池组下电池](http://172.16.0.17:13000/project/198/interface/api/20010)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (params: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, SvcQuery>(
      endpoint,
      params
    );

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      error,
      data: {
        data: data.data,
        total: data.total,
      },
      ...rest,
    };
  };
}
