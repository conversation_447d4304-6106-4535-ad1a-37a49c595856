/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-1-5
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  deviceGuid: string;
};

export type Battery = {
  /** 电池guid */
  guid: string;
  /** 电池名称	*/
  name: string;
  /** 电池组guid */
  parentGuid: string;
  /** 电池组名称 */
  parentName: string;
};

export type SvcRespData = {
  data: Battery[];
  total: number;
};

export type RequestRespData = {
  data: Battery[] | null;
  total: number;
} | null;

export type ApiR = ListResponse<Battery[] | null>;
