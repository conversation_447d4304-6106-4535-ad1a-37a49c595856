/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-15
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import {
  BackendProfessionalRuleTemplate,
  ProfessionalRuleTemplateJSON,
} from '@manyun/monitoring.model.professional-rule-template';

export type SvcQuery = {
  /** 规则名称 */
  name?: string;
  /** 业务分类 code */
  bizCode?: string;
  /** 规则类型 code */
  ruleCode?: string;
  /** 设备类型 */
  deviceType?: string;
  /** 适用范围 */
  scope?: string;
  /** 页数 */
  pageNum: number;
  /** 每页大小 */
  pageSize: number;
};

export type SvcRespData = {
  data: ProfessionalRuleTemplateJSON[];
  total: number;
};

export type RequestRespData = {
  data: BackendProfessionalRuleTemplate[] | null;
  total: number;
} | null;

export type ApiQ = SvcQuery;

export type ApiR = Response<RequestRespData>;
