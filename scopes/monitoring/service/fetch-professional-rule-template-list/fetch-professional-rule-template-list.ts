/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-15
 *
 * @packageDocumentation
 */
import { ProfessionalRuleTemplate } from '@manyun/monitoring.model.professional-rule-template';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './fetch-professional-rule-template-list.type';

const endpoint = '/dccm/expert/rule/scheme/list';

/**
 * @see [Doc](http://172.16.0.17:13000/project/178/interface/api/19357)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = { ...svcQuery };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }
    return {
      error,
      data: {
        data: data.data.map(item => ProfessionalRuleTemplate.fromApiObject(item).toJSON()),
        total: data.total,
      },
      ...rest,
    };
  };
}
