/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-2-21
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-alarm-idcs';
import type { SvcRespData } from './fetch-alarm-idcs.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchAlarmIdcs(): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor();
}
