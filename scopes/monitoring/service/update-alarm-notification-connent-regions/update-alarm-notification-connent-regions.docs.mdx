---
description: 'A updateAlarmNotificationConnentRegions HTTP API service.'
labels: ['service', 'http', update-alarm-notification-connent-regions]
---

## 概述

更新告警通报生效域

## 使用

### Browser

```ts
import { updateAlarmNotificationConnentRegions } from '@manyun/monitoring.service.update-alarm-notification-connent-regions';

const { error, data } = await updateAlarmNotificationConnentRegions({
  levelCode: '1',
  connectRegions: [
    {
      id: 'EC01',
      type: 'IDC',
    },
  ],
});
const { error, data } = await updateAlarmNotificationConnentRegions({
  levelCode: 'error',
  connectRegions: [],
});
```

### Node

```ts
import { updateAlarmNotificationConnentRegions } from '@manyun/monitoring.service.update-alarm-notification-connent-regions/dist/index.node';

const { data } = await updateAlarmNotificationConnentRegions({
  levelCode: '1',
  connectRegions: [
    {
      id: 'EC01',
      type: 'IDC',
    },
  ],
});
const { error, data } = await updateAlarmNotificationConnentRegions({
  levelCode: 'error',
  connectRegions: [],
});
```
