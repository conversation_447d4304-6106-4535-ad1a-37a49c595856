/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-2
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type RegionType = 'IDC' | 'BLOCK';
export type SvcQuery = {
  levelCode: string;
  connectRegions: {
    id: string;
    type: RegionType;
  }[];
};

export type SvcRespData = boolean;

export type RequestRespData = boolean;

export type ApiQ = {
  alarmLevel: string;
  regionList: {
    targetId: string;
    targetType: RegionType;
  }[];
};

export type ApiR = WriteResponse;
