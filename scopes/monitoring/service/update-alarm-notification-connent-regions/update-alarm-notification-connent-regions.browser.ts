/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-2
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './update-alarm-notification-connent-regions';
import type { SvcQuery, SvcRespData } from './update-alarm-notification-connent-regions.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function updateAlarmNotificationConnentRegions(
  variant: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
