/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-2
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { updateAlarmNotificationConnentRegions as webService } from './update-alarm-notification-connent-regions.browser';
import { updateAlarmNotificationConnentRegions as nodeService } from './update-alarm-notification-connent-regions.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({
    levelCode: '1',
    connectRegions: [
      {
        id: 'EC01',
        type: 'IDC',
      },
    ],
  });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    levelCode: 'error',
    connectRegions: [],
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService({
    levelCode: '1',
    connectRegions: [
      {
        id: 'EC01',
        type: 'IDC',
      },
    ],
  });

  expect(error).toBe(undefined);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({
    levelCode: 'error',
    connectRegions: [],
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
