/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-2
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './update-alarm-notification-connent-regions.type';

const endpoint = '/dcim/alarm/inform/region/relate';

/**
 * 更新告警通报生效域
 * @see [Doc](http://172.16.0.17:13000/project/158/interface/api/18717)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      alarmLevel: variant.levelCode,
      regionList: variant.connectRegions.map(region => ({
        targetId: region.id,
        targetType: region.type,
      })),
    };

    return await request.tryPost<RequestRespData, ApiQ>(endpoint, params);
  };
}
