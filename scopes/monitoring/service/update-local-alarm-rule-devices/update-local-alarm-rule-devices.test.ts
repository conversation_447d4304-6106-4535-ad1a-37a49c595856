/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-23
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { updateLocalAlarmRuleDevices as webService } from './update-local-alarm-rule-devices.browser';
import { updateLocalAlarmRuleDevices as nodeService } from './update-local-alarm-rule-devices.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    itemId: '89',
    blockGuid: 'EC06.A',
    deviceGuidList: ['1010212'],
    available: true,
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    itemId: '0',
    blockGuid: 'EC06.A',
    deviceGuidList: ['1010212'],
    available: true,
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await webService({
    itemId: '89',
    blockGuid: 'EC06.A',
    deviceGuidList: ['1010212'],
    available: true,
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({
    itemId: '0',
    blockGuid: 'EC06.A',
    deviceGuidList: ['1010212'],
    available: true,
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
