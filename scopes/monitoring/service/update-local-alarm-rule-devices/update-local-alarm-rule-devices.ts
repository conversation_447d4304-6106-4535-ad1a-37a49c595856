/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-23
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './update-local-alarm-rule-devices.type';

const endpoint = '/dccm/local/monitor/item/device/relate';

/**
 * @see [监控项关联设备](http://172.16.0.17:13000/project/228/interface/api/22989)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      svcQuery
    );

    return { error, data, ...rest };
  };
}
