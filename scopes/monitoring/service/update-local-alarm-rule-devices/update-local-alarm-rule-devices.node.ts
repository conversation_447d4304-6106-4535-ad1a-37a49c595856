/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-23
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './update-local-alarm-rule-devices';
import type { SvcQuery, SvcRespData } from './update-local-alarm-rule-devices.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function updateLocalAlarmRuleDevices(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
