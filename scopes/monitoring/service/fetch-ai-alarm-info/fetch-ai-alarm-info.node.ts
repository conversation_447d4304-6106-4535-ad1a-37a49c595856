/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-20
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-ai-alarm-info';
import type { SvcQuery, SvcRespData } from './fetch-ai-alarm-info.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function fetchAiAlarmInfo(svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
