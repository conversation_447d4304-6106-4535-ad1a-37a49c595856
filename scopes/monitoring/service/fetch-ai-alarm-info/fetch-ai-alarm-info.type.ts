/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-20
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { AiOpsAlarmJSON, BackendAiOpsAlarm } from '@manyun/monitoring.model.ai-ops-alarm';

export type BackendAiOpsAlarmDetail = BackendAiOpsAlarm & {
  operatorId: string;
  operatorName: string;
  notifyWay: string;
  notifyWayCode: string;
  scenesParam: string;
};

export type AiOpsAlarmDetail = {
  operator: {
    name: string;
    id: string;
  };
  notifyWay: { name: string; code: string };
  scenesParam: string;
} & AiOpsAlarmJSON;

export type SvcQuery = {
  id: string;
};

export type SvcRespData = AiOpsAlarmDetail | null;

export type RequestRespData = BackendAiOpsAlarmDetail | null;

export type ApiQ = SvcQuery;

export type ApiR = ListResponse<AiOpsAlarmDetail | null>;
