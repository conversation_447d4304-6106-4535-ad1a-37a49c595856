/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-20
 *
 * @packageDocumentation
 */
import type { BackendAiOpsAlarm } from '@manyun/monitoring.model.ai-ops-alarm';
import { AiOpsAlarm } from '@manyun/monitoring.model.ai-ops-alarm';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-ai-alarm-info.type';

const endpoint = '/dcim/ai/alarm/query/detail';

/**
 * @see [ai告警详情](http://172.16.0.17:13000/project/204/interface/api/20640)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      svcQuery
    );

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: null,
      };
    }

    return {
      error,
      data: {
        ...AiOpsAlarm.fromApiObject(data as BackendAiOpsAlarm).toJSON(),
        operator: { name: data.operatorName, id: data.operatorId },
        notifyWay: { name: data.notifyWay, code: data.notifyWayCode },
        scenesParam: data.scenesParam,
      },
      ...rest,
    };
  };
}
