/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-15
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './update-professional-rule';
import type { SvcQuery, SvcRespData } from './update-professional-rule.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function updateProfessionalRule(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
