/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-13
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchNonPoints as webService } from './fetch-non-points.browser';
import { fetchNonPoints as nodeService } from './fetch-non-points.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    blockGuid: 'EC06.A',
    deviceType: '10101',
    productModel: '101',
  });

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('length');
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    blockGuid: 'EC06.A',
    deviceType: '10101',
    productModel: '',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({
    blockGuid: 'EC06.A',
    deviceType: '10101',
    productModel: '101',
  });

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('length');
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({
    blockGuid: 'EC06.A',
    deviceType: '10101',
    productModel: '',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
