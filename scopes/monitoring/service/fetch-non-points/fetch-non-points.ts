/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-13
 *
 * @packageDocumentation
 */
import { NonPoint } from '@manyun/monitoring.model.point';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { RequestRespData, SvcQuery, SvcRespData } from './fetch-non-points.type';

const endpoint = '/dccm/block/point/list/query';

/**
 * @see [查询属地测点列表](http://172.16.0.17:13000/project/222/interface/api/22863)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, SvcQuery>(
      endpoint,
      svcQuery
    );

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
        },
      };
    }

    return {
      error,
      data: {
        data: data.data.map(item => NonPoint.fromApiObject(item).toJSON()),
      },
      ...rest,
    };
  };
}
