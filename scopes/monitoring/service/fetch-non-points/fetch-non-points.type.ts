/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-13
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import { BackendNonPoint, NonPointJSON } from '@manyun/monitoring.model.point';

export type SvcQuery = {
  blockGuid: string;
  // 三级分类
  deviceType: string;
  // 型号
  productModel: string;
  // 测点类型
  dataTypeList?: string[];
  // 是否为非标点位
  nonPoint?: boolean;
  // 是否采集
  capture?: boolean;
};

export type SvcRespData = {
  data: NonPointJSON[];
};

export type RequestRespData = {
  data: BackendNonPoint[] | null;
} | null;

export type ApiR = ListResponse<NonPointJSON | null>;
