/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-17
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  blockGuid: string;
  startDate: string;
  endDate: string;
  queryGranularity?: number;
};

export type PointValueForecast = {
  blockGuid: string;
  dbt: number; //干球温度,
  wbt: number; //湿球温度
  humidity: number; //室外湿度
  dewPointTemp: number; //露点温度
  coolingMode: number;
  changeCoolingMode: number | null;
  occurrenceTime: string;
  occurrenceDate: string;
  isNeedChangePoint: boolean; //是否切换
};

export type SvcRespData = {
  data: PointValueForecast[];
  total: number;
};

export type RequestRespData = {
  data: PointValueForecast[] | null;
  total: number;
} | null;

export type ApiR = ListResponse<PointValueForecast[] | null>;
