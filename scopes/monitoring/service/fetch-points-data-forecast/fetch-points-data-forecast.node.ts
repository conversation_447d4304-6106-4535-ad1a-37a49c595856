/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-17
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-points-data-forecast';
import type { SvcQuery, SvcRespData } from './fetch-points-data-forecast.type';

const executor = getExecutor(nodeRequest);

/**
 * @param params
 * @returns
 */
export function fetchPointsDataForecast(
  params: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(params);
}
