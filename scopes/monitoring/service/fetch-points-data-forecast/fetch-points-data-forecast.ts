/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-17
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { RequestRespData, SvcQuery, SvcRespData } from './fetch-points-data-forecast.type';

const endpoint = '/dccm/block/cool/pmf/points/query';

/**
 * @see [Doc](http://172.16.0.17:13000/project/180/interface/api/19374)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async ({
    queryGranularity = 5,
    ...params
  }: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, SvcQuery>(endpoint, {
      queryGranularity,
      ...params,
    });

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return { error, data: { data: data.data, total: data.total }, ...rest };
  };
}
