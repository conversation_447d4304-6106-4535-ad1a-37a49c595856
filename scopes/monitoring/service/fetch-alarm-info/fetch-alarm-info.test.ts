/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-19
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchAlarmInfo as webService } from './fetch-alarm-info.browser';
import { fetchAlarmInfo as nodeService } from './fetch-alarm-info.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ deviceType: '11103', id: 309, pointCode: '2001025' });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('recoverInterval');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({ deviceType: '11103', id: 309, pointCode: '2001025' });

  expect(error?.code).toBe(undefined);
  expect(data.data).toHaveProperty('triggerCount');
  expect(typeof data.total).toBe('number');
});
