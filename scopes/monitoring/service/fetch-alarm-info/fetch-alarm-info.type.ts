/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-19
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { BackendTriggerRule } from '@manyun/monitoring.model.trigger-rule';

export type SvcQuery = ApiQ;

export type AlarmInfo = {
  alarmLevel: number;
  alarmType: string;
  available: boolean;
  createIncident: boolean;
  deviceType: string;
  eventSecondCategory: string;
  eventTopCategory: string;
  gmtCreate: string;
  gmtModified: string;
  groupId: number;
  groupName: string;
  id: number;
  itemType: string;
  lastOperator: number;
  lastOperatorName: string;
  name: string;
  notifyRule: string;
  pointCode: string;
  pointInfo: {
    dataType: string;
    deviceType: string;
    name: string;
    pointCode: string;
    pointType: string;
  };
  recoverInterval: number;
  spaceGuid: string;
  triggerCount: number;
  triggerInterval: number;
  triggerRules: string;
  monitorItem: {
    triggerRules?: BackendTriggerRule[];
  };
  description?: string;
};

export type SvcRespData = AlarmInfo;

export type RequestRespData = AlarmInfo | null;

export type ApiQ = {
  deviceType: string;
  id: number;
  pointCode: string;
};

export type ApiR = ListResponse<AlarmInfo | null>;
