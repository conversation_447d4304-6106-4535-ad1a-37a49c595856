/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-12
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './fetch-alarm-template-groups.type';

const endpointAll = '/dccm/monitor/scheme/query/all';
const endpointCondition = '/dccm/monitor/group/query/associated/schemes';

/**
 * 查询模板组(所有或根据模板id查询关联的模板组)
 *
 * @see [Doc](http://172.16.0.17:13000/project/136/interface/api/8312)
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant?: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = { ...variant };

    const endpoint = variant?.groupId ? endpointCondition : endpointAll;

    const { error, data, ...rest } = await request.tryGet<RequestRespData, ApiQ>(endpoint, {
      params,
    });

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return { error, data: { data: data.data, total: data.total }, ...rest };
  };
}
