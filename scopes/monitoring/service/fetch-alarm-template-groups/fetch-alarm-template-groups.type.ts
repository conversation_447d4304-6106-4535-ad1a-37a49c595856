/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-12
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  groupId?: number;
};

/** 后续可抽成模板 实体 */
export type BackendTemplateGroup = {
  id: number;
  name: string;
  available?: boolean;
  groupNum?: number;
};

export type TemplateGroup = {
  id: number;
  name: string;
  available?: boolean;
  groupNum?: number;
};

export type SvcRespData = {
  data: TemplateGroup[];
  total: number;
};

export type RequestRespData = {
  data: BackendTemplateGroup[] | null;
  total: number;
} | null;

export type ApiQ = {
  groupId?: number;
};

export type ApiR = ListResponse<RequestRespData[] | null>;
