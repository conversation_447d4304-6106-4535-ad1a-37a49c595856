/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-12
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-alarm-template-groups';
import type { SvcQuery, SvcRespData } from './fetch-alarm-template-groups.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchAlarmTemplateGroups(
  variant?: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
