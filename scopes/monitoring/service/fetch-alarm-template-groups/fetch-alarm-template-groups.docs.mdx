---
description: 'A fetchAlarmTemplateGroups HTTP API service.'
labels: ['service', 'http', fetch-alarm-template-groups]
---

## 概述

查询模板组(所有或根据模板 id 查询关联的模板组)

## 使用

### Browser

```ts
import { fetchAlarmTemplateGroups } from '@manyun/monitoring.fetch-alarm-template-groups';

const { error, data } = await fetchAlarmTemplateGroups();
```

### Node

```ts
import { fetchAlarmTemplateGroups } from '@manyun/monitoring.fetch-alarm-template-groups/dist/index.node';

const { data } = await fetchAlarmTemplateGroups();

try {
  const { data } = await fetchAlarmTemplateGroups();
} catch (error) {
  // ...
}
```
