/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-4-8
 *
 * @packageDocumentation
 */

import { request } from '@glpdev/symphony.services.request';
import type { EnhancedAxiosResponse } from '@glpdev/symphony.services.request';

import { getExecutor } from './fetch-alarm-template-by-params.js';
import type { ApiArgs, ApiResponse } from './fetch-alarm-template-by-params.type.js';

/**
 * @param args
* @returns
 */
export function fetchAlarmTemplateByParams(args: ApiArgs): Promise<EnhancedAxiosResponse<Pick<ApiResponse, 'data' | 'total'>>> {
  if (!request) {
    throw new Error('request instance expected.');
  }
  const executor = getExecutor(request);

  return executor(args);
}
