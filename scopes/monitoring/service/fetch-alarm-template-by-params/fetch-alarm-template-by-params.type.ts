/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-4-8
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type ApiArgs = {
  available?: boolean;
  idcTag?: string;
  idcTags?: string[];
  lastOperatorName?: string;
  name?: string;
  needTotalCount?: boolean;
  region?: string;
  targetId?: string;
  targetType?: string;
  pageNum: number;
  pageSize: number;
};

export type AlarmTemplate = {
  available: boolean;
  gmtCreate: string;
  gmtModified: string;
  groupNum?: number;
  id: number;
  lastOperator: number;
  lastOperatorName: string;
  name: string;
};

export type ApiResponse = ListResponse<AlarmTemplate[]>;
