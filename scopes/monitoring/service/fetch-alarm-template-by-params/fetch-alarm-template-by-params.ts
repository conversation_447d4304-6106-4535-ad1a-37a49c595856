/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-4-8
 *
 * @packageDocumentation
 */

import type { EnhancedAxiosResponse, Request } from '@glpdev/symphony.services.request';

import type { ApiArgs, ApiResponse } from './fetch-alarm-template-by-params.type.js';

const endpoint = '/dccm/monitor/scheme/query';

/**
* @see [Doc](http://yapi.manyun-local.com/project/140/interface/api/15360)
*
* @param request
* @returns
*/
export function getExecutor<T extends Request = Request>(request: T) {
  return async (args: ApiArgs): Promise<EnhancedAxiosResponse<Pick<ApiResponse, 'data' | 'total'>>> => {
    const { error, data, ...rest } = await request.tryPost<Pick<ApiResponse, 'data' | 'total'> | null, ApiArgs>(endpoint, args);

  if (error || data === null || data.data === null) {
    return {
      ...rest,
      error,
      data: {
        data: [],
        total: 0,
      },
    };
  }

  return { error, data: { data: data.data, total: data.total }, ...rest };
  };
}             
