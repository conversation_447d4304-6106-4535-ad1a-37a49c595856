/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-17
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { DataType } from '@manyun/monitoring.model.point';

export type SvcQuery = {
  id: number;
  // 协议测点名称
  name: string;
  // 协议测点类型
  dataType: DataType;
  // 是否采集
  capture: boolean;
  unit?: string;
  // 值含义
  validLimits?: string;
  // 表达式
  formula?: string;
};

export type ApiR = WriteResponse;

export type SvcRespData = boolean;
