/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-17
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { SvcQuery, SvcRespData } from './export-non-point.type';

const endpoint = '/dccm/block/point/export';

/**
 * @see [导出属地测点](http://172.16.0.17:13000/project/222/interface/api/22962)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (params: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    return await request.tryPost<SvcRespData, SvcQuery>(endpoint, params, {
      responseType: 'blob',
    });
  };
}
