/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-28
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-operator-type';
import type { SvcRespData } from './fetch-operator-type.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchOperatorType(): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor();
}
