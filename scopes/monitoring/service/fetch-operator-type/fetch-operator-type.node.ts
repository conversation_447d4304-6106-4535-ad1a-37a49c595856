/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-28
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-operator-type';
import type { SvcRespData } from './fetch-operator-type.type';

const executor = getExecutor(nodeRequest);

/**
 * @returns
 */
export function fetchOperatorType(): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor();
}
