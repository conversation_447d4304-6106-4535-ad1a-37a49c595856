/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-15
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-professional-rule-list';
import type { SvcQuery, SvcRespData } from './fetch-professional-rule-list.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function fetchProfessionalRuleList(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
