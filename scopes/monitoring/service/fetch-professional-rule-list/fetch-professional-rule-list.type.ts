/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-15
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import {
  BackendProfessionalRule,
  ProfessionalRuleJSON,
} from '@manyun/monitoring.model.professional-rule';

export type SvcQuery = {
  /** 规则名称 */
  name?: string;
  /** 楼栋 guid 列表 */
  blockGuidList?: string[];
  /** 包间 */
  roomTag?: string;
  /** 设备名称	 */
  deviceName?: string;
  /** 设备类型	 */
  deviceType?: string;
  /** 业务分类 code */
  bizCode?: string;
  /** 规则类型 code */
  ruleCode?: string;
  /** 是否启用 */
  available?: boolean;
  /** 模版 ID */
  schemeId?: number;
  /** 页数 */
  pageNum: number;
  /** 每页大小 */
  pageSize: number;
};

export type SvcRespData = {
  data: ProfessionalRuleJSON[];
  total: number;
};
export type RequestRespData = {
  data: BackendProfessionalRule[] | null;
  total: number;
} | null;

export type ApiQ = SvcQuery;

export type ApiR = Response<RequestRespData>;
