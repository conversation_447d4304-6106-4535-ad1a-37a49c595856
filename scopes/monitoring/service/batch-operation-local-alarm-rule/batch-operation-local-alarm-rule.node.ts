/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-5-22
 *
 * @packageDocumentation
 */

import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './batch-operation-local-alarm-rule.js';
import type { ApiArgs,  } from './batch-operation-local-alarm-rule.type.js';

const executor = getExecutor(nodeRequest);

/**
 * @param args
* @returns
 */
export function batchOperationLocalAlarmRule(args: ApiArgs): Promise<EnhancedAxiosResponse<boolean>> {
  return executor(args);
}
