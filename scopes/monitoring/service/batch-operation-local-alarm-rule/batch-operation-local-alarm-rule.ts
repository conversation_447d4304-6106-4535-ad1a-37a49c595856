/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-5-22
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiArgs } from './batch-operation-local-alarm-rule.type.js';

const endpoint = '/dccm/local/monitor/item/batch/operation';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/228/interface/api/27046)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (args: ApiArgs): Promise<EnhancedAxiosResponse<boolean>> => {
    return await request.tryPost<boolean, ApiArgs>(endpoint, args);
  };
}
