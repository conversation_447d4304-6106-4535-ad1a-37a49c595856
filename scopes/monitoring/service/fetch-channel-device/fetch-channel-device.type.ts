/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-29
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  channelId: string;
  deviceName?: string;
  deviceType?: string;
  pageNum: number;
  pageSize: number;
};

export type ChannelDevice = {
  channelId: string;
  deviceName: string;
  deviceGuid: string;
  deviceType: string;
  pointNum: string;
  spaceGuid: string;
};

export type SvcRespData = {
  data: ChannelDevice[];
  total: number;
};

export type RequestRespData = {
  data: ChannelDevice[] | null;
  total: number;
} | null;

export type ApiQ = SvcQuery;

export type ApiR = ListResponse<ChannelDevice[] | null>;
