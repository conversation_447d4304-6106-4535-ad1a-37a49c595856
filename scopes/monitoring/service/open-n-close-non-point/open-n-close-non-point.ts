/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-17
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { SvcQuery, SvcRespData } from './open-n-close-non-point.type';

const endpoint = '/dccm/block/point/enable';

/**
 * @see [启停属地测点](http://172.16.0.17:13000/project/222/interface/api/22890)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (params: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<SvcRespData, SvcQuery>(endpoint, params);

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: false,
      };
    }

    return { error, data: data, ...rest };
  };
}
