/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-17
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { openNCloseNonPoint as webService } from './open-n-close-non-point.browser';
import { openNCloseNonPoint as nodeService } from './open-n-close-non-point.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    ids: [1001],
    enable: true,
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({ ids: [], enable: true });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({ ids: [1001], enable: true });
  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({ ids: [], enable: true });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
