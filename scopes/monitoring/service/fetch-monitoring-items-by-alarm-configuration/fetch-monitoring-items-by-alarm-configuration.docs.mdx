---
description: 'A fetchMonitoringItemsByAlarmConfiguration HTTP API service.'
labels: ['service', 'http', fetch-monitoring-items-by-alarm-configuration]
---

## 概述

根据告警模板 id 查询监控项

## 使用

### Browser

```ts
import { fetchMonitoringItemsByAlarmConfiguration } from '@manyun/monitoring.fetch-monitoring-items-by-alarm-configuration';

const { error, data } = await fetchMonitoringItemsByAlarmConfiguration({ groupId: 1 });
const { error, data } = await fetchMonitoringItemsByAlarmConfiguration({});
```

### Node

```ts
import { fetchMonitoringItemsByAlarmConfiguration } from '@manyun/monitoring.fetch-monitoring-items-by-alarm-configuration/dist/index.node';

const { data } = await fetchMonitoringItemsByAlarmConfiguration({ groupId: 1 });

try {
  const { data } = await fetchMonitoringItemsByAlarmConfiguration({});
} catch (error) {
  // ...
}
```
