/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-12
 *
 * @packageDocumentation
 */
import { MonitoringItem } from '@manyun/monitoring.model.monitoring-item';
import { fetchTreeModeMetaData } from '@manyun/resource-hub.service.fetch-tree-mode-meta-data';
import type { BackendData } from '@manyun/resource-hub.service.fetch-tree-mode-meta-data';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './fetch-monitoring-items-by-alarm-configuration.type';

const endpoint = '/dccm/monitor/group/query/associated/items';

/**
 * 根据告警模板id查询监控项
 * @see [Doc](http://172.16.0.17:13000/project/136/interface/api/8104)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async ({
    groupId,
    needAlarmNotify = true,
  }: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = { groupId: groupId };

    const { error, data, ...rest } = await request.tryGet<RequestRespData, ApiQ>(endpoint, {
      params,
    });

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    let metaData: BackendData[] = [];
    if (needAlarmNotify) {
      const { error, data } = await fetchTreeModeMetaData({
        topCategory: 'ALARM_TOP_NOTIFY',
        secondCategory: 'ALARM_SECOND_NOTIFY',
      });
      if (error === undefined) {
        data.data.forEach(dt => {
          if (dt.children) {
            dt.children.forEach(children => {
              metaData.push(children);
            });
          }
        });
      }
    }

    return {
      error,
      data: {
        data: data.data.map(d => MonitoringItem.fromApiObject(d, metaData)),
        total: data.total,
      },
      ...rest,
    };
  };
}
