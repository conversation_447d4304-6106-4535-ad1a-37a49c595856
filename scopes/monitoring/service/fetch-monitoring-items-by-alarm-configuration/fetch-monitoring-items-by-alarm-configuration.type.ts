/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-12
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import { BackendMonitoringItem, MonitoringItem } from '@manyun/monitoring.model.monitoring-item';

export type SvcQuery = { groupId: number; needAlarmNotify?: boolean };

export type SvcRespData = {
  data: MonitoringItem[];
  total: number;
};

export type RequestRespData = {
  data: BackendMonitoringItem[] | null;
  total: number;
} | null;

export type ApiQ = {
  groupId: number;
};

export type ApiR = ListResponse<RequestRespData>;
