/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-1
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './import-channel-template';
import type { SvcRespData } from './import-channel-template.type';

const executor = getExecutor(webRequest);

/**
 * @returns
 */
export function importChannelTemplate(): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor();
}
