/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-15
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import { ProfessionalRuleDetailJSON } from '@manyun/monitoring.model.professional-rule';

export type SvcQuery = Omit<ProfessionalRuleDetailJSON, 'id' | 'triggerCount'>;

export type SvcRespData = number;

export type RequestRespData = number;

export type ApiQ = Omit<
  ProfessionalRuleDetailJSON,
  'id' | 'triggerCount' | 'device' | 'ruleJson'
> & {
  /** 设备 guid */
  deviceGuid: string;
  /** 设备名称	 */
  deviceName: string;
  /** 规则描述 */
  ruleJson: string;
};

export type ApiR = Response<RequestRespData | null>;
