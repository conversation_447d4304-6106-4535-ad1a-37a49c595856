/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-15
 *
 * @packageDocumentation
 */
import { SignType } from '@manyun/monitoring.model.trigger-rule';
import { useRemoteMock } from '@manyun/service.request';

import { createProfessionalRule } from './create-professional-rule.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('should resolve error response', async () => {
  const { error } = await createProfessionalRule({
    schemeId: Number.NaN,
    name: '',
    blockGuid: '',
    roomTag: '',
    bizCode: '',
    ruleCode: '',
    device: {
      guid: '',
      name: '',
    },
    available: false,
    triggerInterval: Number.NaN,
    recoverInterval: Number.NaN,
    ruleFormula: '',
    ruleJson: {
      triggerConditions: [
        {
          firstValue: {
            type: 'custom',
            value: '',
          },
          symbol: SignType.Equal,
          secondValue: {
            type: 'custom',
            value: '',
          },
        },
      ],
    },
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
