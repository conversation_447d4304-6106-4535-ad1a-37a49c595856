/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-21
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, SvcRespData } from './update-ops-scene.type';

const endpoint = '/dccm/ai/ops/scenes/update';

/**
 * @see [编辑场景配置](http://172.16.0.17:13000/project/210/interface/api/20595)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (params: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<SvcRespData, ApiQ>(endpoint, params);

    if (error || data === null) {
      return { error, ...rest, data: null };
    }

    return { error, data: data, ...rest };
  };
}
