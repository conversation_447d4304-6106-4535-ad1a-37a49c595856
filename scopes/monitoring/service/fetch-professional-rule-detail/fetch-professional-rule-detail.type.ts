/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-15
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import {
  BackendProfessionalRuleDetail,
  ProfessionalRuleDetailJSON,
} from '@manyun/monitoring.model.professional-rule';

export type SvcQuery = {
  id: number;
};

export type SvcRespData = ProfessionalRuleDetailJSON | null;

export type RequestRespData = BackendProfessionalRuleDetail | null;

export type ApiQ = SvcQuery;

export type ApiR = Response<RequestRespData>;
