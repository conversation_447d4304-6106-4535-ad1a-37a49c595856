/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-15
 *
 * @packageDocumentation
 */
import { ProfessionalRuleDetail } from '@manyun/monitoring.model.professional-rule';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './fetch-professional-rule-detail.type';

const endpoint = '/dccm/expert/rule/item/detail';

/**
 * @see [Doc](http://172.16.0.17:13000/project/178/interface/api/19363)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = { ...svcQuery };

    const { error, data, ...rest } = await request.tryGet<RequestRespData, ApiQ>(endpoint, {
      params,
    });

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: null,
      };
    }

    return { error, data: ProfessionalRuleDetail.fromApiObject(data).toJSON(), ...rest };
  };
}
