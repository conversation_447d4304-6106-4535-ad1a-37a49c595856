/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-15
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchProfessionalRuleDetail as webService } from './fetch-professional-rule-detail.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('should resolve error response', async () => {
  const { error } = await webService({ id: Number.NaN });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
