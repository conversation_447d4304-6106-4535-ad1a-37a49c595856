/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-15
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './create-professional-rule-template.type';

const endpoint = '/dccm/expert/rule/scheme/add';

/**
 * @see [Doc](http://172.16.0.17:13000/project/178/interface/api/19356)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = { ...svcQuery };

    return await request.tryPost<RequestRespData, ApiQ>(endpoint, params);
  };
}
