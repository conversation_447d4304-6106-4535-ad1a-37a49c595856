/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-15
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './create-professional-rule-template';
import type { SvcQuery, SvcRespData } from './create-professional-rule-template.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function createProfessionalRuleTemplate(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
