/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-15
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import { ProfessionalRuleTemplateJSON } from '@manyun/monitoring.model.professional-rule-template';

export type SvcQuery = Omit<ProfessionalRuleTemplateJSON, 'id' | 'instanceNum'>;

export type SvcRespData = number;

export type RequestRespData = number;

export type ApiQ = SvcQuery;

export type ApiR = Response<RequestRespData | null>;
