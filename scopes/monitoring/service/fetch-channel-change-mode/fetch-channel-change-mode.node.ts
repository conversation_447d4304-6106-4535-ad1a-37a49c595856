/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-29
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-channel-change-mode';
import type { SvcRespData } from './fetch-channel-change-mode.type';

const executor = getExecutor(nodeRequest);

/**
 * @returns
 */
export function fetchChannelChangeMode(): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor();
}
