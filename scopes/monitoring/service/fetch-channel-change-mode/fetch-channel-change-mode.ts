/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-29
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { RequestRespData, SvcRespData } from './fetch-channel-change-mode.type';

const endpoint = '/dccm/channel/change/mode';

/**
 * @see [获取所有转换方式](http://172.16.0.17:13000/project/182/interface/api/19479)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryGet<RequestRespData>(endpoint);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {},
      };
    }

    return { error, data, ...rest };
  };
}
