---
description: 'A fetchAlarmsStatistics HTTP API service.'
labels: ['service', 'http']
---

## 概述

查询（按照设备一级分类统计的）告警数量

## 使用

### Browser

```ts
import { fetchAlarmsStatistics } from '@manyun/monitoring.service.fetch-alarms-statistics';

const { error, data } = await fetchAlarmsStatistics({
  idc: 'EC06',
  block: 'A',
  room: 'A1-1',
});
```

### Node

```ts
import { fetchAlarmsStatistics } from '@manyun/monitoring.service.fetch-alarms-statistics/dist/index.node';
```
