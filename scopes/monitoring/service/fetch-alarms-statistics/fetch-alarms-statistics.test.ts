/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-8
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchAlarmsStatistics as webService } from './fetch-alarms-statistics.browser';
import { fetchAlarmsStatistics as nodeService } from './fetch-alarms-statistics.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

const mockResult = {
  '1': 10,
  '2': 9,
  '3': 7,
  '4': 0,
};

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    idc: 'EC06',
    block: 'A',
    room: 'A1-1',
  });

  expect(error).toBe(undefined);
  expect(data).toStrictEqual(mockResult);
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({
    idc: 'EC06',
    block: 'A',
    room: 'not-exists',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toStrictEqual({});
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({
    idc: 'EC06',
    block: 'A',
    room: 'A1-1',
  });

  expect(error).toBe(undefined);
  expect(data).toStrictEqual(mockResult);
});

test('[node] should resolve error response', async () => {
  const { error, data } = await nodeService({
    idc: 'EC06',
    block: 'A',
    room: 'not-exists',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toStrictEqual({});
});
