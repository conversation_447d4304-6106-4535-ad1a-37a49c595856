/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-8
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-alarms-statistics';
import type { SvcQuery, SvcRespData } from './fetch-alarms-statistics.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function fetchAlarmsStatistics(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
