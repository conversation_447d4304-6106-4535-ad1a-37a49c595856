/**
 * <AUTHOR> W <<EMAIL>>
 * @since 2022-9-8
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  idc: string;
  block: string;
  room: string;
};

export type DeviceType = string;
export type AlarmsCount = number;
export type Statistics = Record<DeviceType, AlarmsCount>;

export type SvcRespData = Statistics;

export type RequestRespData = Statistics | null;

export type ApiQ = {
  idcTag: string;
  blockTag: string;
  roomTag: string;
};

export type ApiR = Response<Statistics | null>;
