/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-8
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-alarms-statistics.type';

const endpoint = '/dcim/alarm/device/type/count';

/**
 * @see [Doc](http://172.16.0.17:13000/project/86/interface/api/18969)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const apiQ: ApiQ = {
      idcTag: svcQuery.idc,
      blockTag: svcQuery.block,
      roomTag: svcQuery.room,
    };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, apiQ);

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: {},
      };
    }

    return { error, data, ...rest };
  };
}
