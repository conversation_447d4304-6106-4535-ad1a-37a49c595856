/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-20
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchAiOpsPointForecast as webService } from './fetch-ai-ops-point-forecast.browser';
import { fetchAiOpsPointForecast as nodeService } from './fetch-ai-ops-point-forecast.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({
    idcTag: 'EC06',
    scenesId: 1,
    deviceGuid: '123',
    pointCode: '1010100',
    startTime: 1,
    endTime: 2,
    interval: 'D',
    function: 'AVG',
  });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    idcTag: 'EC06',
    scenesId: 2,
    deviceGuid: '123',
    pointCode: '1010100',
    startTime: 1,
    endTime: 2,
    interval: 'D',
    function: 'AVG',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService({
    idcTag: 'EC06',
    scenesId: 1,
    deviceGuid: '123',
    pointCode: '1010100',
    startTime: 1,
    endTime: 2,
    interval: 'D',
    function: 'AVG',
  });

  expect(error).toBe(undefined);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({
    idcTag: 'EC06',
    scenesId: 2,
    deviceGuid: '123',
    pointCode: '1010100',
    startTime: 1,
    endTime: 2,
    interval: 'D',
    function: 'AVG',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
