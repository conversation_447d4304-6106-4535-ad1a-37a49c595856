/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-20
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-ai-ops-point-forecast';
import type { SvcQuery, SvcRespData } from './fetch-ai-ops-point-forecast.type';

const executor = getExecutor(webRequest);

/**
 * @param params
 * @returns
 */
export function fetchAiOpsPointForecast(
  params: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(params);
}
