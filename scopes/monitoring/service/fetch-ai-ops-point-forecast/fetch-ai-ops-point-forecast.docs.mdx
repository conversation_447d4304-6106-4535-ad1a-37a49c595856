---
description: 'A fetchAiOpsPointForecast HTTP API service.'
labels: ['service', 'http']
---

## 概述

获取测点 AI-OPS 动态基线数据与预测数据

## 使用

### Browser

```ts
import { fetchAiOpsPointForecast } from '@manyun/monitoring.service.fetch-ai-ops-point-forecast';

const { error, data } = await fetchAiOpsPointForecast({
  idcTag: 'EC06',
  scenesId: 1,
  deviceGuid: '123',
  pointCode: '1010100',
  startTime: 1,
  endTime: 2,
  interval: 'D',
  function: 'AVG',
});
```
