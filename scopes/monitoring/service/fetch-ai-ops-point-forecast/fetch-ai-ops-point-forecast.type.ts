/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-20
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import type { ChartFunction, Interval } from '@manyun/monitoring.service.fetch-chart-data';

export type DataType = 'upper' | 'lower' | 'forecast';
export type PointValue = number | null;
export type PointValues = [number, PointValue][];

export type SvcQuery = {
  idcTag: string;
  /**场景ID */
  scenesId: number;
  deviceGuid: string;
  pointCode: string;
  startTime: number;
  endTime: number;
  /**获取点位数据时的聚合规则 */
  function: ChartFunction;
  /**时间间隔粒度 */
  interval: Interval;
};

export type ForecastMapper = Record<DataType, { name: string; values: PointValues } | null>;

export type BackendData = {
  upperView: boolean;
  upperName: string;
  lowerView: boolean;
  lowerName: string;
  pointData: (Record<DataType, { value: PointValue }> & { time: number })[];
};

export type SvcRespData = ForecastMapper;

export type RequestRespData = BackendData | null;

export type ApiR = Response<RequestRespData>;
