/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-20
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  PointValues,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './fetch-ai-ops-point-forecast.type';

const endpoint = '/dcim/ai/forecast/data/query';

/**
 * @see [Doc](http://172.16.0.17:13000/project/204/interface/api/20649)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (params: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, SvcQuery>(
      endpoint,
      params
    );

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: {
          upper: null,
          lower: null,
          forecast: null,
        },
      };
    }
    const { upperView, upperName, lowerName, lowerView, pointData } = data;
    const upperValues: PointValues = [];
    const lowerValues: PointValues = [];
    const forecastValues: PointValues = [];
    pointData &&
      pointData.length &&
      pointData
        .sort((a, b) => a.time - b.time)
        .forEach(({ time, upper, lower, forecast }) => {
          upperValues.push([time, upper.value]);
          lowerValues.push([time, lower.value]);
          forecastValues.push([time, forecast.value]);
        });

    return {
      error,
      data: {
        upper: upperView
          ? {
              name: upperName,
              values: upperValues,
            }
          : null,
        lower: lowerView ? { name: lowerName, values: lowerValues } : null,
        forecast: { name: '预测值', values: forecastValues },
      },
      ...rest,
    };
  };
}
