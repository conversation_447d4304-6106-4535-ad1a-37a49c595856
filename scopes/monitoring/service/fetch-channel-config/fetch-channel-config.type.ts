/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-25
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type {
  BackendChannelConfig,
  ChannelConfigJSON,
} from '@manyun/monitoring.model.channel-config';

export type SvcQuery = {
  pageSize: number;
  pageNum: number;
  blockGuid?: string;
  condition?: string;
  channelType?: string;
  protocol?: string;
  channelStatus?: string;
  deviceType?: string;
  roomTag?: string;
};

export type SvcRespData = {
  data: ChannelConfigJSON[];
  total: number;
};

export type RequestRespData = {
  data: BackendChannelConfig[] | null;
  total: number;
} | null;

export type ApiQ = SvcQuery;

export type ApiR = ListResponse<ChannelConfigJSON[] | null>;
