/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-25
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchChannelConfig as webService } from './fetch-channel-config.browser';
import { fetchChannelConfig as nodeService } from './fetch-channel-config.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ pageSize: 10, pageNum: 1 });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({ pageSize: 10, pageNum: 1 });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});
