/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-25
 *
 * @packageDocumentation
 */
import { ChannelConfig } from '@manyun/monitoring.model.channel-config';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-channel-config.type';

const endpoint = '/dccm/channel/page/query';

/**
 * @see [通道列表](http://172.16.0.17:13000/project/182/interface/api/19428)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      svcQuery
    );

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      error,
      data: {
        data: data.data.map(item => ChannelConfig.fromApiObject(item).toJSON()),
        total: data.total,
      },
      ...rest,
    };
  };
}
