/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-2-21
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchPointValueTops as webService } from './fetch-point-value-tops.browser';
import { fetchPointValueTops as nodeService } from './fetch-point-value-tops.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({
    idcTag: 'EC01',
    blockTag: 'A',
    sortOrder: 'DESC',
    dimension: 'DEVICE',
    devicePointList: [],
  });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    idcTag: 'EC02',
    blockTag: 'A',
    sortOrder: 'DESC',
    dimension: 'DEVICE',
    devicePointList: [],
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService({
    idcTag: 'EC01',
    blockTag: 'A',
    sortOrder: 'DESC',
    dimension: 'DEVICE',
    devicePointList: [],
  });

  expect(error).toBe(undefined);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({
    idcTag: 'EC02',
    blockTag: 'A',
    sortOrder: 'DESC',
    dimension: 'DEVICE',
    devicePointList: [],
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
