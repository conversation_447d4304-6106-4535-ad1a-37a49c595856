/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-2-21
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-point-value-tops';
import type { SvcQuery, SvcRespData } from './fetch-point-value-tops.type';

const executor = getExecutor(nodeRequest);

/**
 * @param params
 * @returns
 */
export function fetchPointValueTops(params: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(params);
}
