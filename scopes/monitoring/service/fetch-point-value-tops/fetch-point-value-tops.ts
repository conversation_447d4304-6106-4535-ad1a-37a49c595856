/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-2-21
 *
 * @packageDocumentation
 */
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { RequestRespData, SvcQuery, SvcRespData } from './fetch-point-value-tops.type';

const endpoint = '/dccm/real/device/point/top';

/**
 * @see [Doc](http://172.16.0.17:13000/project/152/interface/api/20235)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (params: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, SvcQuery>(
      endpoint,
      params
    );

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      error,
      data: {
        data: data.data.map(device => {
          const { idc, block, room } = getSpaceGuidMap(device.spaceGuid);
          return { ...device, idcTag: idc!, blockTag: block!, roomTag: room! };
        }),
        total: data.total,
      },
      ...rest,
    };
  };
}
