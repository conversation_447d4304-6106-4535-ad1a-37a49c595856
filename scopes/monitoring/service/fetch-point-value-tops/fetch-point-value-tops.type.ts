/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-2-21
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { BackendRoomType } from '@manyun/resource-hub.model.room';

export type SvcQuery = {
  idcTag: string;
  blockTag: string;
  sortOrder: 'DESC' | 'ASC';
  size?: number;
  dimension: 'DEVICE' | 'ROOM' | 'COLUMN';
  typeList?: BackendRoomType[];
  devicePointList: {
    deviceType: string;
    pointCode: string;
  }[];
};

export type BackendDevice = {
  guid: string;
  name: string;
  deviceType: string;
  pointCode: string;
  spaceGuid: string;
  roomName: string;
  dataValue: number;
  rated?: string;
  unit?: string;
};

export type Device = BackendDevice & {
  idcTag: string;
  blockTag: string;
  roomTag: string;
};

export type SvcRespData = {
  data: Device[];
  total: number;
};

export type RequestRespData = {
  data: BackendDevice[] | null;
  total: number;
} | null;

export type ApiR = ListResponse<RequestRespData>;
