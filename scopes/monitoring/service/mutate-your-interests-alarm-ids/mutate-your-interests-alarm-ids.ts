/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-23
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  RequestRespData,
  SvcData,
  SvcRespData,
} from './mutate-your-interests-alarm-ids.type';

const endpoint = '/dcim/alarm/user/occur/update';

/**
 * @see [Doc](http://172.16.0.17:13000/project/86/interface/api/18472)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async ({ action, alarmIds }: SvcData): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      operatorType: action,
      alarmIds,
    };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: false,
      };
    }

    return { error, data, ...rest };
  };
}
