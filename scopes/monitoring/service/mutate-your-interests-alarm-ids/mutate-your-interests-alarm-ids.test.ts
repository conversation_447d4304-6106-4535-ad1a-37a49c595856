/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-23
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { mutateYourInterestsAlarmIds as webService } from './mutate-your-interests-alarm-ids.browser';

// import { mutateYourInterestsAlarmIds as nodeService } from './mutate-your-interests-alarm-ids.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should add alarms successfully', async () => {
  const { error, data } = await webService({ action: 'ADD', alarmIds: [50, 51, 52] });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should delete alarms successfully', async () => {
  const { error, data } = await webService({ action: 'DELETE', alarmIds: [1, 2, 3] });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});
