/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-23
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcData = {
  action: 'ADD' | 'DELETE';
  alarmIds: number[];
};

export type SvcRespData = boolean;

export type RequestRespData = boolean | null;

export type ApiQ = {
  operatorType: 'ADD' | 'DELETE';
  alarmIds: number[];
};

export type ApiR = ListResponse<boolean | null>;
