/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-23
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './mutate-your-interests-alarm-ids';
import type { SvcData, SvcRespData } from './mutate-your-interests-alarm-ids.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcData
 * @returns
 */
export function mutateYourInterestsAlarmIds(
  svcData: SvcData
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcData);
}
