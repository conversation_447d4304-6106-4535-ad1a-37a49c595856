/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-12-6
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './update-local-alarm-notice-status';
import type { ApiArgs } from './update-local-alarm-notice-status.type';

const executor = getExecutor(nodeRequest);

/**
 * @param args
 * @returns
 */
export function updateLocalAlarmNoticeStatus(
  args: ApiArgs
): Promise<EnhancedAxiosResponse<boolean>> {
  return executor(args);
}
