/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-12-6
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiArgs } from './update-local-alarm-notice-status.type';

const endpoint = '/dccm/alarm/inform/update/status/local';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/158/interface/api/25924)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (args: ApiArgs): Promise<EnhancedAxiosResponse<boolean>> => {
    return await request.tryPost<boolean, ApiArgs>(endpoint, args);
  };
}
