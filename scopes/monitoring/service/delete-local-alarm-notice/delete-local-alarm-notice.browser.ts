/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-12-6
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './delete-local-alarm-notice';
import type { ApiArgs } from './delete-local-alarm-notice.type';

const executor = getExecutor(webRequest);

/**
 * @param args
 * @returns
 */
export function deleteLocalAlarmNotice(args: ApiArgs): Promise<EnhancedAxiosResponse<boolean>> {
  return executor(args);
}
