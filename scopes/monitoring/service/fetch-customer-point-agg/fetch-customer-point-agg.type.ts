/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-2-21
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  idcTag: string;
  blockTag: string;
  pointCode: string;
  customerNo?: string;
  itType?: string;
  aggMode: 'MAX' | 'MIN' | 'SUM' | 'AVG' | 'COUNT';
  dataValue?: number;
};

export type Customer = {
  guid: string;
  pointCode: string;
  dataValue: number | null;
  unit: string | null;
  rated: string | null;
  customerName?: string;
};

export type SvcRespData = {
  data: Customer[];
  total: number;
};

export type RequestRespData = {
  data: Customer[] | null;
  total: number;
} | null;

export type ApiR = ListResponse<Customer[] | null>;
