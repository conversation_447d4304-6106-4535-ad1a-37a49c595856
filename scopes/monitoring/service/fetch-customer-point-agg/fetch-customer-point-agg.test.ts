/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-2-21
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchCustomerPointAgg as webService } from './fetch-customer-point-agg.browser';
import { fetchCustomerPointAgg as nodeService } from './fetch-customer-point-agg.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    idcTag: 'EC01',
    blockTag: 'A',
    pointCode: '1010101',
    aggMode: 'COUNT',
    dataValue: 0,
  });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({
    idcTag: 'EC02',
    blockTag: 'A',
    pointCode: '1010101',
    aggMode: 'COUNT',
    dataValue: 0,
  });
  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({
    idcTag: 'EC01',
    blockTag: 'A',
    pointCode: '1010101',
    aggMode: 'COUNT',
    dataValue: 0,
  });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[node] should resolve error response', async () => {
  const { error, data } = await nodeService({
    idcTag: 'EC02',
    blockTag: 'A',
    pointCode: '1010101',
    aggMode: 'COUNT',
    dataValue: 0,
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});
