/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-1-17
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-point-thresholds';
import type { SvcQuery, SvcRespData } from './fetch-point-thresholds.type';

const executor = getExecutor(webRequest);

/**
 * @param query Service query object
 * @returns
 */
export function fetchPointThresholds(query: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(query);
}
