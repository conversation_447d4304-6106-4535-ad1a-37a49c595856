/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-1-17
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type {
  BackendMonitoringItem,
  MonitoringItemJSON,
} from '@manyun/monitoring.model.monitoring-item';

export type SvcQuery = {
  deviceGuid?: string;
  deviceType: string;
  pointCode: string;
  spaceGuid?: string;
  /*传blockGuid后端走单独的逻辑查询*/
  blockGuid?: string;
};

export type BackendThreshold = {
  groupId: number;
  groupName: string;
  groupStatus: boolean /**模板状态 */;
  alarmLevel: number;
  triggerCount: number;
  triggerRule: string;
} & Pick<
  BackendMonitoringItem,
  'id' | 'name' | 'alarmLevel' | 'alarmType' | 'triggerRules' | 'available'
>;

export type Threshold = Pick<
  MonitoringItemJSON,
  'id' | 'name' | 'alarmLevel' | 'alarmType' | 'triggerRules' | 'enable' | 'preTriggerRules'
> & {
  groupId: number;
  groupName: string;
  groupStatus: boolean /**模板状态 */;
};

export type SvcRespData = {
  data: Threshold[];
  total: number;
};

export type RequestRespData = {
  data: BackendThreshold[] | null;
  total: number;
} | null;

export type ApiR = ListResponse<BackendThreshold[] | null>;
