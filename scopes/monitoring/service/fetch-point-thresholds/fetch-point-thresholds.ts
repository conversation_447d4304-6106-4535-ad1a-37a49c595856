/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-1-17
 *
 * @packageDocumentation
 */
import { TriggerRule, TriggerRuleType } from '@manyun/monitoring.model.trigger-rule';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { RequestRespData, SvcQuery, SvcRespData } from './fetch-point-thresholds.type';

const endpoint = '/dccm/item/point/query';

/**
 * @see [获取点位监控项](http://172.16.0.17:13000/project/140/interface/api/15512)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (query: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: SvcQuery = query;

    const { error, data, ...rest } = await request.tryPost<RequestRespData, SvcQuery>(
      endpoint,
      params
    );

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      error,
      data: {
        data: data.data.map(threshold => {
          return {
            id: threshold.id,
            name: threshold.name,
            alarmLevel: threshold.alarmLevel,
            alarmType:
              typeof threshold.alarmType === 'object'
                ? threshold.alarmType.code
                : threshold.alarmType,
            triggerCount: threshold.triggerCount,
            triggerRule: threshold.triggerRule,
            triggerRules: (threshold.triggerRules || [])
              .filter(rule => rule.ruleType === TriggerRuleType.AlarmCondition)
              .map(rule => TriggerRule.fromApiObject(rule).toJSON()),
            preTriggerRules: (threshold.triggerRules || [])
              .filter(rule => rule.ruleType === TriggerRuleType.PreCondition)
              .map(rule => TriggerRule.fromApiObject(rule).toJSON()),
            enable: threshold.available,
            groupId: threshold.groupId,
            groupName: threshold.groupName,
            groupStatus: threshold.groupStatus,
          };
        }),
        total: data.total,
      },
      ...rest,
    };
  };
}
