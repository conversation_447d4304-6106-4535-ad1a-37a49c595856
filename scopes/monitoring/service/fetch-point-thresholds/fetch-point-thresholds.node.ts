/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-1-17
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-point-thresholds';
import type { SvcQuery, SvcRespData } from './fetch-point-thresholds.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function fetchPointThresholds(
  variant?: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  throw new Error("fetchPointThresholds's node version not implemented!");
}
