/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-1-17
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchPointThresholds as webService } from './fetch-point-thresholds.browser';

let webMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
});
afterAll(() => {
  webMockOff();
});

test('[web] should resolve success response', async () => {
  const { data } = await webService({ deviceType: '1', pointCode: '1', spaceGuid: '1' });

  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({ deviceType: '2', pointCode: '1', spaceGuid: '1' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});
