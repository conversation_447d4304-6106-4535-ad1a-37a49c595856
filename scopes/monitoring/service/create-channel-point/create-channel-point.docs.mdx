---
description: 'A createChannelPoint HTTP API service.'
labels: ['service', 'http']
---

## 概述

通道测点创建

## 使用

### Browser

```ts
import { createChannelPoint } from '@manyun/monitoring.service.create-channel-point';

const { error, data } = await createChannelPoint({
  channelId: '4605',
  deviceGuid: 'S1N1080406',
  blockGuid: 'EC01.A',
  deviceType: '10804',
  pointGuid: '*******.********.*******',
});
```
