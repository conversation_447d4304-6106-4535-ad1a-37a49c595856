/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-29
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './create-channel-point';
import type { SvcQuery, SvcRespData } from './create-channel-point.type';

const executor = getExecutor(nodeRequest);

/**
 * @param params
 * @returns
 */
export function createChannelPoint(params: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(params);
}
