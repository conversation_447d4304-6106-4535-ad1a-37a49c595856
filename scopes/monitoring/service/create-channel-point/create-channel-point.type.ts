/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-29
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = ApiQ;

export type SvcRespData = RequestRespData;

export type RequestRespData = boolean | null;

export type ApiQ = {
  channelId: string;
  blockGuid: string;
  deviceGuid: string;
  deviceType: string;
  pointCode: string;
  dataType?: string;
  operationType?: string; //操作类型
  changeMode?: string; //转换方式
  zoom?: number; //缩放因子
  elementId?: number; //单元标识符
  position?: number; //偏移位
  address?: number; //寄存器起始地址
  registerSwap?: boolean; //是否交换寄存器位置
  targetGuid?: string; //目标设备guid
  baseValue?: number; //基数
  pointGuid?: string; //点位guid
  bitCount?: number; //bit位
  formula?: string;
};

export type ApiR = WriteResponse;
