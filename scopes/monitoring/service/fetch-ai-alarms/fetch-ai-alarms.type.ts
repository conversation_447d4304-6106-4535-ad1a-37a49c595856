/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-20
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { AiOpsAlarmJSON, BackendAiOpsAlarm } from '@manyun/monitoring.model.ai-ops-alarm';

export type SvcQuery = {
  pageSize: number;
  pageNum: number;
  positions: string[];
  alarmType?: string;
  alarmLevel?: string;
  triggerStatus?: string;
  feedback?: string;
  deviceType?: string[];
  alarmCondition?: string;
  operatorId?: string;
  triggerStartTime?: number;
  triggerEndTime?: number;
  recoverStartTime?: number;
  recoverEndTime?: number;
};

export type SvcRespData = {
  data: AiOpsAlarmJSON[];
  total: number;
};

export type RequestRespData = {
  data: BackendAiOpsAlarm[] | null;
  total: number;
} | null;

export type ApiQ = SvcQuery;

export type ApiR = ListResponse<AiOpsAlarmJSON[] | null>;
