/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-20
 *
 * @packageDocumentation
 */
import { AiOpsAlarm } from '@manyun/monitoring.model.ai-ops-alarm';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-ai-alarms.type';

const endpoint = '/dcim/ai/alarm/query';

/**
 * @see [ai监测列表](http://172.16.0.17:13000/project/204/interface/api/20622)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      svcQuery
    );

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      error,
      data: {
        data: data.data.map(item => AiOpsAlarm.fromApiObject(item).toJSON()),
        total: data.total,
      },
      ...rest,
    };
  };
}
