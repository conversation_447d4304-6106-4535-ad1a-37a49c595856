/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-21
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { updateAiAlarm as webService } from './update-ai-alarm.browser';
import { updateAiAlarm as nodeService } from './update-ai-alarm.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({
    id: '31',
    feedback: 0,
    feedbackDesc: '反馈',
  });

  expect(error).toBe(undefined);
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService({
    id: '31',
    feedback: 0,
    feedbackDesc: '反馈',
  });

  expect(error).toBe(undefined);
});
