/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-21
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { RequestRespData, SvcQuery, SvcRespData } from './delete-ops-scene.type';

const endpoint = '/dccm/ai/ops/scenes/delete';

/**
 * @see [删除场景配置](http://172.16.0.17:13000/project/210/interface/api/20604)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (params: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, SvcQuery>(
      endpoint,
      params
    );

    if (error || data === null) {
      return { error, ...rest, data: null };
    }

    return { error, data: data, ...rest };
  };
}
