/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-15
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './delete-professional-rule-template';
import type { SvcQuery, SvcRespData } from './delete-professional-rule-template.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function deleteProfessionalRuleTemplate(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
