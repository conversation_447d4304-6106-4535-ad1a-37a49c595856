/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-29
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-channel-data-type';
import type { SvcRespData } from './fetch-channel-data-type.type';

const executor = getExecutor(nodeRequest);

/**
 * @returns
 */
export function fetchChannelDataType(): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor();
}
