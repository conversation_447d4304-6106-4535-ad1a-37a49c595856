/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-20
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type AiMonitor = {
  idcTag: string;
  blockTag: string;
  scenesType: string;
  monitorNum: number;
  aiAlarmNum: number;
};

export type SvcQuery = {
  blockGuidList: string[];
};

export type SvcRespData = {
  data: AiMonitor[];
  total: number;
};

export type RequestRespData = {
  data: AiMonitor[] | null;
  total: number;
} | null;

export type ApiQ = SvcQuery;

export type ApiR = ListResponse<AiMonitor[] | null>;
