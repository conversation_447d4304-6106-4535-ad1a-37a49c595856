/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-23
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-your-interests-alarm-ids';
import type { SvcRespData } from './fetch-your-interests-alarm-ids.type';

const executor = getExecutor(nodeRequest);

/**
 * @returns
 */
export function fetchYourInterestsAlarmIds(): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor();
}
