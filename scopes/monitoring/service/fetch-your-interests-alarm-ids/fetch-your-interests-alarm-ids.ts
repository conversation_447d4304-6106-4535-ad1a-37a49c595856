/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-23
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { RequestRespData, SvcRespData } from './fetch-your-interests-alarm-ids.type';

const endpoint = '/dcim/alarm/user/occur/query';

/**
 * @see [Doc](http://172.16.0.17:13000/project/86/interface/api/18464)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryGet<RequestRespData>(endpoint);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      error,
      data: {
        data: data.data.map(({ id, occurTime }) => ({ id, followedAt: occurTime })),
        total: data.total,
      },
      ...rest,
    };
  };
}
