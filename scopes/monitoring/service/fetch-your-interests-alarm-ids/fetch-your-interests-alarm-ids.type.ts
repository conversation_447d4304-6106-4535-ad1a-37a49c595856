/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-23
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type BackendYourInterestsAlarm = {
  /** Alarm ID */
  id: number;
  /** timestamp */
  occurTime: number;
};

export type YourInterestsAlarm = {
  /** Alarm ID */
  id: number;
  /** timestamp */
  followedAt: number;
};

export type SvcRespData = {
  data: YourInterestsAlarm[];
  total: number;
};

export type RequestRespData = {
  data: BackendYourInterestsAlarm[] | null;
  total: number;
} | null;

export type ApiR = ListResponse<BackendYourInterestsAlarm[] | null>;
