/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-15
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { endpoint } from './export-alarm-transmission-records';
import type { SvcQuery } from './export-alarm-transmission-records.type';

/**
 * 离线告警数据导出
 *
 * @see [Doc](http://172.16.0.17:13000/project/96/interface/api/2792)
 *
 * @param variant
 * @returns
 */
export async function exportAlarmTransmissionRecords(
  query: SvcQuery
): Promise<EnhancedAxiosResponse<Blob>> {
  const data: SvcQuery = query;
  return await webRequest.tryPost<Blob, SvcQuery>(endpoint, data, {
    responseType: 'blob',
  });
}
