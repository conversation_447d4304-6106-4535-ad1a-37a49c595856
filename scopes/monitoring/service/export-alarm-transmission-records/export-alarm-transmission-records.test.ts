/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-15
 *
 * @packageDocumentation
 */
// 使用以下代码注册本地 Mocks
import { getMock } from '@manyun/service.request';

import { exportAlarmTransmissionRecords as webService } from './export-alarm-transmission-records.browser';
import { registerWebMocks } from './export-alarm-transmission-records.mock';

// mocks for tests should resolve immediately.
registerWebMocks(getMock('web'));

// 使用以下代码注册远程 Mocks

// import { useRemoteMock } from '@manyun/service.request';

// let mockOff: () => void;
// beforeAll(() => {
//   mockOff = useRemoteMock('web');
// });
// afterAll(() => {
//   mockOff();
// });

test('should resolve success response', async () => {
  const { error, data, config } = await webService({
    alarmEndTime: 1639583999999,
    alarmStartTime: 1639497600000,
  });
  expect(error).toBe(undefined);
  expect(data).toBeInstanceOf(Blob);
  expect(data.type).toBe('application/vnd.ms-excel');
});
