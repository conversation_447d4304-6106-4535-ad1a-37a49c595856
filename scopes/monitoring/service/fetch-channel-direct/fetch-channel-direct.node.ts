/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-30
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-channel-direct';
import type { SvcQuery, SvcRespData } from './fetch-channel-direct.type';

const executor = getExecutor(nodeRequest);

/**
 * @param channelId
 * @returns
 */
export function fetchChannelDirect(
  channelId: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(channelId);
}
