/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-30
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-channel-direct.type';

const endpoint = '/dccm/channel/direct';

/**
 * @see [判断通道是否为直采通道](http://172.16.0.17:13000/project/182/interface/api/19394)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (channelId: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryGet<RequestRespData, ApiQ>(
      `${endpoint}/${channelId}`
    );

    return { error, data, ...rest };
  };
}
