/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-5-22
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

const endpoint = '/dccm/local/monitor/item/model/download';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/228/interface/api/27056)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (): Promise<EnhancedAxiosResponse<Blob>> => {
    return await request.tryGet<Blob>(endpoint, {
      responseType: 'blob',
    });
  };
}
