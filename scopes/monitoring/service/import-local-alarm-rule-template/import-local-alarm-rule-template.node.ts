/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-5-22
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './import-local-alarm-rule-template.js';

const executor = getExecutor(nodeRequest);

/**
 * @returns
 */
export function importLocalAlarmRuleTemplate(): Promise<EnhancedAxiosResponse<Blob>> {
  return executor();
}
