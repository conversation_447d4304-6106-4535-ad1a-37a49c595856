/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-1-17
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type ChartFunction = 'MAX' | 'MIN' | 'AVG';
export type Interval = 'S' | 'M' | 'H' | 'D' | 'M30' | 'M5' | 'H2' | 'H12' | 'M10';

export type PointGuid = { deviceGuid: string; pointCode: string; sourcePointCodes?: string[] };
export type PointGuidList = Omit<PointGuid, 'sourcePointCodes'>[];
export type SeriesData = [number, number | string | null][][];
export type ReqInterceptor = (param: Omit<SvcQuery, 'idcTag'>) => Omit<SvcQuery, 'idcTag'>;
export type RespInterceptor = (chartData: BackendChartData[]) => BackendChartData[];
export type ExtData = {
  guid: string;
  name: string;
  deviceType: string;
  roomTag?: string;
  roomName?: string;
};
export type ExtType = keyof ExtData;
export type SeriesExtData = Record<number, ExtData>[];

export type SvcQuery = {
  startTime: number;
  endTime: number;
  function: ChartFunction;
  interval: Interval;
  pointGuidList: PointGuid[];
  idcTag: string;
  /**是否查询扩展属性 */
  isQueryExt?: boolean;
};

export type ApiQ = {
  startTime: number;
  endTime: number;
  function: ChartFunction;
  interval: Interval;
  pointGuidList: PointGuidList;
  isQueryExt?: boolean;
};

export type BackendChartData = {
  time: number;
  values: Record<string, number | null>;
  extDataMap?: Record<string, ExtData>;
};

export type SvcRespData = {
  data: SeriesData;
  extData: SeriesExtData;
  total: number;
};

export type RequestRespData = {
  data: BackendChartData[] | null;
  total: number;
} | null;

export type ApiR = ListResponse<BackendChartData[] | null>;
