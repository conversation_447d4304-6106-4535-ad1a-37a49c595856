/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-1-17
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-chart-data';
import type {
  ReqInterceptor,
  RespInterceptor,
  SvcQuery,
  SvcRespData,
} from './fetch-chart-data.type';

const executor = getExecutor(webRequest);

/**
 * @param query Service query object
 * @returns
 */
export function fetchChartData(
  query: SvcQuery,
  reqInterceptor?: ReqInterceptor,
  respInterceptor?: RespInterceptor
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(query, reqInterceptor, respInterceptor);
}
