/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-1-17
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchChartData as webService } from './fetch-chart-data.browser';

let webMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
});
afterAll(() => {
  webMockOff();
});

test('[web] should resolve success response', async () => {
  const { data } = await webService({
    idcTag: 'EC01',
    startTime: 11111,
    endTime: 11111,
    function: 'MAX',
    interval: 'S',
    pointGuidList: [
      {
        deviceGuid: 'EC06.A',
        pointCode: '1005000',
      },
    ],
  });

  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({
    idcTag: 'EC01',
    startTime: 11111,
    endTime: 11111,
    function: 'MAX',
    interval: 'M',
    pointGuidList: [
      {
        deviceGuid: 'EC01',
        pointCode: '10101',
      },
    ],
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});
