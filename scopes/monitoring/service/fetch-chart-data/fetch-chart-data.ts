/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-1-17
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  BackendChartData,
  PointGuidList,
  ReqInterceptor,
  RequestRespData,
  RespInterceptor,
  SeriesData,
  SeriesExtData,
  SvcQuery,
  SvcRespData,
} from './fetch-chart-data.type';

const endpoint = '/dcim/data/guid/duration';

/**
 * @see [获取图表数据](http://172.16.0.17:13000/project/86/interface/api/2312)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (
    { idcTag, ...query }: SvcQuery,
    reqInterceptor?: ReqInterceptor,
    respInterceptor?: RespInterceptor
  ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const newQuery = reqInterceptor ? reqInterceptor(query) : query;
    const apiQ: ApiQ = {
      ...newQuery,
      pointGuidList: newQuery.pointGuidList.map(({ deviceGuid, pointCode }) => ({
        deviceGuid,
        pointCode,
      })),
    };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, apiQ, {
      headers: {
        'X-IDC': idcTag,
      },
    });

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          extData: [],
          total: 0,
        },
      };
    }

    const seriesData = respInterceptor ? respInterceptor(data.data) : data.data;

    return {
      error,
      data: { ...getSeriesDatas(seriesData, query.pointGuidList), total: data.total },
      ...rest,
    };
  };
}

function getSeriesDatas(rangeData: BackendChartData[], pointGuidList: PointGuidList) {
  const seriesData: SeriesData = pointGuidList.map(() => []);
  const seriesExtData: SeriesExtData = pointGuidList.map(() => ({}));

  rangeData.forEach(({ time, values, extDataMap }) => {
    Object.keys(values).forEach(key => {
      const idx = pointGuidList.findIndex(
        ({ deviceGuid, pointCode }) => `${deviceGuid}.${pointCode}` === key
      );

      if (idx < 0) {
        return;
      }
      const value = values[key];
      seriesData[idx] = [...seriesData[idx], [time, value]];
    });
    extDataMap &&
      Object.keys(extDataMap).forEach(key => {
        const idx = pointGuidList.findIndex(
          ({ deviceGuid, pointCode }) => `${deviceGuid}.${pointCode}` === key
        );
        if (idx < 0) {
          return;
        }
        seriesExtData[idx][time] = extDataMap[key];
      });
  });
  seriesData.forEach(item => {
    item.sort((a, b) => a[0] - b[0]);
  });

  return { data: seriesData, extData: seriesExtData };
}
