/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-1-17
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-chart-data';
import type { SvcQuery, SvcRespData } from './fetch-chart-data.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function fetchChartData(variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  throw new Error("fetchChartData's node version not implemented!");
}
