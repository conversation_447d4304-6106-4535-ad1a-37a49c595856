---
description: 'A fetchChartData HTTP API service.'
labels: ['service', 'http', fetch-chart-data]
---

## 概述

获取图表数据

## 使用

### Browser

```ts
import { fetchChartData } from '@manyun/monitoring.service.fetch-chart-data';

const { error, data } = await fetchChartData({
  idcTag: 'EC01',
  startTime: 11111,
  endTime: 11111,
  function: 'MAX',
  interval: 'M',
  pointGuidList: [
    {
      deviceGuid: 'EC06.A',
      pointCode: '1005000',
    },
  ],
});
```
