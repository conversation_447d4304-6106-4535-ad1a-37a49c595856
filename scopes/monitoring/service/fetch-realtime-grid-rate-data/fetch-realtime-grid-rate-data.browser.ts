/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-7
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-realtime-grid-rate-data';
import type { SvcQuery, SvcRespData } from './fetch-realtime-grid-rate-data.type';

const executor = getExecutor(webRequest);

/**
 * @param params
 * @returns
 */
export function fetchRealtimeGridRateData(
  params: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(params);
}
