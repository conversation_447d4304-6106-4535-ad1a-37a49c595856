/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-7
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { BackendRoomType } from '@manyun/resource-hub.model.room';

export type SvcQuery = {
  idcTag: string;
  blockTag: string;
  roomTypeList?: string[];
};

export type Room = {
  idcTag: string;
  blockTag: string;
  roomTag: string;
  roomGuid: string;
  roomName: string;
  roomType: BackendRoomType;
  rated: number;
  onNum: number;
  offNum: number;
  totalNum: number;
  ratedPower?: number;
  signedPower?: number;
};

export type SvcRespData = {
  data: Room[];
  total: number;
};

export type RequestRespData = {
  data: Room[] | null;
  total: number;
} | null;

export type ApiR = ListResponse<RequestRespData>;
