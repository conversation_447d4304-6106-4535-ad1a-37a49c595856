/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-7
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchRealtimeGridRateData as webService } from './fetch-realtime-grid-rate-data.browser';
import { fetchRealtimeGridRateData as nodeService } from './fetch-realtime-grid-rate-data.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({ idcTag: 'EC06', blockTag: 'A' });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({ idcTag: 'EC06', blockTag: 'B' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService({ idcTag: 'EC06', blockTag: 'A' });

  expect(error).toBe(undefined);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({ idcTag: 'EC06', blockTag: 'B' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
