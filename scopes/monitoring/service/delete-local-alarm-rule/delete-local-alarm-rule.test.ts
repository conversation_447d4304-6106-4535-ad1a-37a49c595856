/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-21
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { deleteLocalAlarmRule as webService } from './delete-local-alarm-rule.browser';
import { deleteLocalAlarmRule as nodeService } from './delete-local-alarm-rule.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ itemId: '89' });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({ itemId: '1' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await webService({ itemId: '89' });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({ itemId: '1' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
