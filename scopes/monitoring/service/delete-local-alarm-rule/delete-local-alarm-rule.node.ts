/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-21
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './delete-local-alarm-rule';
import type { SvcQuery, SvcRespData } from './delete-local-alarm-rule.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function deleteLocalAlarmRule(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
