/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-21
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchOpsScene as webService } from './fetch-ops-scene.browser';
import { fetchOpsScene as nodeService } from './fetch-ops-scene.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    id: 1,
  });

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('name');
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    id: 0,
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({
    id: 1,
  });

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('name');
});

test('[node] should resolve error response', async () => {
  const { error } = await webService({
    id: 0,
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
