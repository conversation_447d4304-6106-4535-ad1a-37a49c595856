/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-21
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-ops-scene';
import type { SvcQuery, SvcRespData } from './fetch-ops-scene.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function fetchOpsScene(svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
