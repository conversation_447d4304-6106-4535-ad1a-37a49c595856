/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-21
 *
 * @packageDocumentation
 */
import { DynamicBaseline } from '@manyun/monitoring.model.dynamic-baseline-setting';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { RequestRespData, SvcQuery, SvcRespData } from './fetch-ops-scene.type';

const endpoint = '/dccm/ai/ops/scenes/query/detail';

/**
 * @see [查询配置详情](http://172.16.0.17:13000/project/210/interface/api/20577)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (params: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, SvcQ<PERSON>y>(
      endpoint,
      params
    );

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: null,
      };
    }

    return { error, data: DynamicBaseline.fromApiObject(data).toJSON(), ...rest };
  };
}
