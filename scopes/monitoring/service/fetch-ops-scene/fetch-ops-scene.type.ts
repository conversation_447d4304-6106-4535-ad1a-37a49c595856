/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-21
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import {
  BackendDynamicBaseline,
  DynamicBaselineJSON,
} from '@manyun/monitoring.model.dynamic-baseline-setting';

export type SvcQuery = {
  /** 场景id */
  id: number;
};

export type SvcRespData = DynamicBaselineJSON | null;

export type RequestRespData = BackendDynamicBaseline | null;

export type ApiR = ListResponse<BackendDynamicBaseline | null>;
