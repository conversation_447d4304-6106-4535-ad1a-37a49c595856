/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-30
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  channelId: string;
  deviceGuid: string;
  deviceType: string;
};

export type ApiQ = SvcQuery;

export type UnconfigPoint = Record<string, string>;

export type SvcRespData = UnconfigPoint;

export type RequestRespData = UnconfigPoint;

export type ApiR = ListResponse<UnconfigPoint | null>;
