/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-30
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchChannelUnconfigPoint as webService } from './fetch-channel-unconfig-point.browser';
import { fetchChannelUnconfigPoint as nodeService } from './fetch-channel-unconfig-point.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    channelId: '4605',
    deviceGuid: 'S1N1080406',
    deviceType: '10804',
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({
    channelId: '4605',
    deviceGuid: 'S1N1080406',
    deviceType: '10804',
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});
