/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-19
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  deviceTypeList: string[];
};

export type DeviceTypePointsModel = {
  // 测点类型： AI DI AO DO ALARM
  dataType: string;
  // 设备类型
  deviceType: string;
  // 点位名称
  name: string;
  // 点位code
  pointCode: string;
  // 点位类型
  // 枚举: 原始点位,加工点位,设备内加工点位,聚合点位,设备内聚合点位,自定义点位
  pointType: string;
  // 单位
  unit: string;
  validLimits: string[];
  item: string;
  validMap: object;
  // 备注
  description: string;
};

export type SvcRespData = {
  data: DeviceTypePointsModel[];
  total: number;
};

export type RequestRespData = {
  data: DeviceTypePointsModel[] | null;
  total: number;
} | null;

export type ApiQ = SvcQuery;

export type ApiR = ListResponse<DeviceTypePointsModel[] | null>;
