/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-19
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-device-type-points';
import type { SvcQuery, SvcRespData } from './fetch-device-type-points.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchDeviceTypePoints(
  params: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(params);
}
