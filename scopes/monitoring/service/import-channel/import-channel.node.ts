/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-1
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './import-channel';
import type { SvcQuery, SvcRespData } from './import-channel.type';

const executor = getExecutor(nodeRequest);

/**
 * @param params
 * @returns
 */
export function importChannel(params: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(params);
}
