/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-1
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { importChannel as webService } from './import-channel.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ file: '12' });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({ file: '23' });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});
