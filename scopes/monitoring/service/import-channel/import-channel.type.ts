/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-1
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = FormData;

export type ChannelImportexcelCheck = {
  errMessage: Record<string, string>;
  errDto: {
    id: number;
    channelName: string;
    ip: string;
    port: string;
    username: string;
    password: string;
    protocol: string;
    channelType: string;
    channelStatus: string;
    blockGuid: string;
    deviceGuid: string;
    config: string;
    vendor: string;
    productModel: string;
    operatorId: number;
    operatorName: string;
  };
};

export type ChannelImport = {
  checkTotal: number;
  correctTotal: number;
  faultTotal: number;
  excelCheckErrDtos: ChannelImportexcelCheck[];
};

export type SvcRespData = ChannelImport;

export type RequestRespData = ChannelImport | null;

export type ApiQ = SvcQuery;
export type ApiR = ListResponse<ChannelImport | null>;
