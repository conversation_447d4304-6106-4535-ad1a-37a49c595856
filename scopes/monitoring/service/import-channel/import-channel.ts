/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-1
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './import-channel.type';

const endpoint = '/dccm/channel/import';

/**
 * @see [批量导入通道](http://172.16.0.17:13000/project/182/interface/api/19386)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (params: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: [],
      };
    }

    return { error, data, ...rest };
  };
}
