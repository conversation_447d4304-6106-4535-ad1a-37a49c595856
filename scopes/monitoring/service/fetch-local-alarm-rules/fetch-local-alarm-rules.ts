/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-17
 *
 * @packageDocumentation
 */
import shortid from 'shortid';

import { MonitoringItem } from '@manyun/monitoring.model.monitoring-item';
import { fetchTreeModeMetaData } from '@manyun/resource-hub.service.fetch-tree-mode-meta-data';
import type { BackendData } from '@manyun/resource-hub.service.fetch-tree-mode-meta-data';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-local-alarm-rules.type';

const endpoint = '/dccm/local/monitor/item/query';

/**
 * @see [查询告警规则列表](http://172.16.0.17:13000/project/228/interface/api/22872)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      svcQuery
    );

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    const metaData: BackendData[] = [];

    const { error: treeModeMetaDataError, data: treeModeMetaData } = await fetchTreeModeMetaData({
      topCategory: 'ALARM_TOP_NOTIFY',
      secondCategory: 'ALARM_SECOND_NOTIFY',
    });
    if (treeModeMetaDataError === undefined) {
      treeModeMetaData.data.forEach(dt => {
        if (dt.children) {
          dt.children.forEach(children => {
            metaData.push(children);
          });
        }
      });
    }

    return {
      error,
      data: {
        data: data.data.map(item => {
          if (item.id !== null) {
            return {
              ...MonitoringItem.fromApiObject(item, metaData).toJSON(),
              key: item.id as string,
              nonPoint: item.nonPoint,
              dataType: item.dataType,
              source: item.source,
              unit: item.unit,
              validLimits: item.validLimits,
              parentItemId: item.parentItemId,
            };
          }
          return {
            id: null,
            key: shortid(),
            nonPoint: item.nonPoint,
            pointName: item.pointName,
            pointCode: item.pointCode,
            dataType: item.dataType,
            deviceType: item.deviceType,
          };
        }),
        total: data.total,
      },
      ...rest,
    };
  };
}
