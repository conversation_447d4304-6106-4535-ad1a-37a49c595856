/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-17
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type {
  BackendMonitoringItem,
  MonitoringItemJSON,
} from '@manyun/monitoring.model.monitoring-item';
import type { DataType } from '@manyun/monitoring.model.point';

export type BackendNotConfiguredPoint = {
  id: null;
  nonPoint: boolean;
  pointName: string;
  pointCode: string;
  dataType: DataType;
  deviceType: string;
};

export type NotConfiguredPoint = {
  id: null;
  key: string;
  nonPoint: boolean;
  pointName: string;
  pointCode: string;
  dataType: DataType;
  deviceType: string;
};

export type BackendLocalAlarmRule =
  | (BackendMonitoringItem & {
      nonPoint: boolean;
      dataType: DataType;
      source: 'CENTER' | 'LOCAL';
      unit: string;
      validLimits: string[];
      parentItemId: string;
    })
  | BackendNotConfiguredPoint;

export type LocalAlarmRuleJSON =
  | (MonitoringItemJSON & {
      key: string;
      nonPoint: boolean;
      dataType: DataType;
      source: 'CENTER' | 'LOCAL';
      unit: string;
      validLimits: string[];
      parentItemId: string;
    })
  | NotConfiguredPoint;

export type SvcQuery = {
  blockGuid: string;
  deviceType: string;
  showAlarm?: boolean;
};

export type SvcRespData = {
  data: LocalAlarmRuleJSON[];
  total: number;
};

export type RequestRespData = {
  data: BackendLocalAlarmRule[];
  total: number;
};

export type ApiQ = SvcQuery;

export type ApiR = ListResponse<LocalAlarmRuleJSON[] | null>;
