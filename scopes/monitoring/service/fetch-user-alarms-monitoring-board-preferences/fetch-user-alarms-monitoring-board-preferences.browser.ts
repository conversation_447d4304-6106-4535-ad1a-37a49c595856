/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-26
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-user-alarms-monitoring-board-preferences';
import type { SvcRespData } from './fetch-user-alarms-monitoring-board-preferences.type';

const executor = getExecutor(webRequest);

/**
 * @returns
 */
export function fetchUserAlarmsMonitoringBoardPreferences(): Promise<
  EnhancedAxiosResponse<SvcRespData>
> {
  return executor();
}
