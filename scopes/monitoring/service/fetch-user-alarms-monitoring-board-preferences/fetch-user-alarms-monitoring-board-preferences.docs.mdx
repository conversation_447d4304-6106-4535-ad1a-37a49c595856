---
description: 'A fetchUserAlarmsMonitoringBoardPreferences HTTP API service.'
labels: ['service', 'http', fetch-user-alarms-monitoring-board-preferences]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchUserAlarmsMonitoringBoardPreferences } from '@manyun/monitoring.service.fetch-user-alarms-monitoring-board-preferences';

const { error, data } = await fetchUserAlarmsMonitoringBoardPreferences('success');
const { error, data } = await fetchUserAlarmsMonitoringBoardPreferences('error');
```

### Node

```ts
import { fetchUserAlarmsMonitoringBoardPreferences } from '@manyun/monitoring.service.fetch-user-alarms-monitoring-board-preferences/dist/index.node';

const { data } = await fetchUserAlarmsMonitoringBoardPreferences('success');

try {
  const { data } = await fetchUserAlarmsMonitoringBoardPreferences('error');
} catch (error) {
  // ...
}
```
