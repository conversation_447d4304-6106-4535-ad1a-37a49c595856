/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-26
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  RequestRespData,
  SvcRespData,
} from './fetch-user-alarms-monitoring-board-preferences.type';

const endpoint = '/dcim/alarm/user/config/query';

/**
 * @see [Doc](http://172.16.0.17:13000/project/86/interface/api/18448)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const response = await request.tryGet<RequestRespData>(endpoint);

    if (response.error || response.data === null) {
      return {
        ...response,
        data: null,
      };
    }

    let data: any = null;
    try {
      data = JSON.parse(response.data);
    } catch (error: any) {
      response.error = {
        message: error.message,
        code: 'INTERNAL/JSON_PARSE_ERROR',
      };
    }

    return {
      ...response,
      data,
    };
  };
}
