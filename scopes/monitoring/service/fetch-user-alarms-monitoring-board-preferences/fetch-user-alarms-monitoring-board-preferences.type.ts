/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-26
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

type PreferencesJSONString = string;

/**
 * Keep it be `any` for now. Maintain the exact type in component:
 * `manyun.monitoring/state/alarms-monitoring-board`
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type PreferencesJSON = any;

export type SvcRespData = PreferencesJSON | null;

export type RequestRespData = PreferencesJSONString | null;

export type ApiR = Response<PreferencesJSONString | null>;
