/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-26
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-user-alarms-monitoring-board-preferences';
import type { SvcRespData } from './fetch-user-alarms-monitoring-board-preferences.type';

const executor = getExecutor(nodeRequest);

/**
 * @returns
 */
export function fetchUserAlarmsMonitoringBoardPreferences(): Promise<
  EnhancedAxiosResponse<SvcRespData>
> {
  return executor();
}
