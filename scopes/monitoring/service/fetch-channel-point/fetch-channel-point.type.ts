/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-29
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  channelId: string;
  deviceGuid: string;
  pointName?: string;
  pointDataType?: string;
  showAll?: boolean;
};

export type ChannelPoint = {
  id: string;
  channelId: string;
  pointCode: string; //点位code
  pointGuid: string; //点位guid
  pointName: string; //点位名称
  targetGuid: string; //目标设备
  address: number; //寄存器起始地址
  baseValue: number; //基数
  formula: string; //表达式
  bitCount: number; //bit位
  blockGuid: string; //楼栋
  changeMode: { name: string; code: string }; //转换方式
  deviceGuid: string; //设备guid
  deviceType: string; //设备类型
  elementId: number; //单元标识符
  operationType: { name: string; code: string }; //操作类型
  dataType: { name: string; code: string }; //数据类型
  pointDataType: { name: string; code: 'AI' | 'DI' | 'AO' | 'DO' }; //点位类型
  position: number; //偏移位
  registerSwap: boolean; //是否交换寄存器位置
  zoom: number; //缩放因子
  value: string; //测点最新值
  validLimits: string[];
  unit: string;
  gmtCreate: string;
  gmtModified: string;
};

export type SvcRespData = {
  data: ChannelPoint[];
  total: number;
};

export type RequestRespData = {
  data: ChannelPoint[] | null;
  total: number;
} | null;

export type ApiQ = SvcQuery;
export type ApiR = ListResponse<ChannelPoint[] | null>;
