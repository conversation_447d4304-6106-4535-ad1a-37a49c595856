/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-29
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-channel-point';
import type { SvcQuery, SvcRespData } from './fetch-channel-point.type';

const executor = getExecutor(webRequest);

/**
 * @param params
 * @returns
 */
export function fetchChannelPoint(params: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(params);
}
