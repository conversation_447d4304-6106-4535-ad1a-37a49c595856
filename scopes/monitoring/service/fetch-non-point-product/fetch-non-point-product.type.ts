/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-17
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  blockGuid: string;
  deviceType: string;
};

export type SvcRespData = {
  data: string[];
  total: number;
};

export type RequestRespData = {
  data: string[];
  total: number;
} | null;

export type ApiR = ListResponse<string[] | null>;
