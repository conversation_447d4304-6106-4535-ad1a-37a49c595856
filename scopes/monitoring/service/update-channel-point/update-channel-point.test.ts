/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-29
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { updateChannelPoint as webService } from './update-channel-point.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    id: '56',
    channelId: '4605',
    deviceGuid: 'S1N1080406',
    blockGuid: 'EC01.A',
    deviceType: '10804',
    pointCode: '1001000',
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { data } = await webService({
    id: '56',
    channelId: '0',
    deviceGuid: '0',
    blockGuid: 'EC01.A',
    deviceType: '10804',
    pointCode: '1001000',
  });

  expect(data).toBe(null);
});
