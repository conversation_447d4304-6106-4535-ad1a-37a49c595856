/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-1
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchExpertRule as webService } from './fetch-expert-rule.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ id: 1 });

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('changeTemplateId');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await webService({ id: 1 });

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('changeTemplateId');
});
