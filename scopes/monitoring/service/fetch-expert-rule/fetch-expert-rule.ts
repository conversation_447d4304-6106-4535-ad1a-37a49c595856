/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-1
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-expert-rule.type';

const endpoint = '/dccm/expert/rule/item/detail';

/**
 * @see [专家规则详情](http://172.16.0.17:13000/project/178/interface/api/19363)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (params: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryGet<RequestRespData, ApiQ>(endpoint, {
      params,
    });

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: null,
      };
    }
    return {
      error,
      data,
      ...rest,
    };
  };
}
