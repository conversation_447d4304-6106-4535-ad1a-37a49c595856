/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-1
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-expert-rule';
import type { SvcQuery, SvcRespData } from './fetch-expert-rule.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchExpertRule(variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
