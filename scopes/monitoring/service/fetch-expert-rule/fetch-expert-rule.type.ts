/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-1
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { DataType } from '@manyun/monitoring.model.point';

export type SvcQuery = ApiQ;

export type ExpertRule = {
  id: number; //规则实例id
  schemeId: number; //规则模板id
  name: string; //名称
  blockGuid: string; //楼栋
  roomTag: string; //包间
  deviceGuid: string; //	适用对象guid
  deviceName: string; //适用对象名称
  deviceType: string;
  bizCode: string; //业务类型code
  ruleCode: string; //规则类型code
  available: boolean; //是否启用
  triggerInterval: number; //触发时长
  recoverInterval: number; //恢复时长
  energyDevice: string; //能效设备
  energyDeviceName: string; //能效设备名称
  energyDeviceType: string;
  energyPoint: string; //能效点位
  energyPointName: string; //	能效点位名称
  ruleJson: string; //规则表达式
  description: string; //规则介绍
  scope: string; //适用条件
  triggerDesc: string; //触发条件配置说明
  preConditionDesc: string; //前置条件配置说明
  energyPointDesc: string;
  changeTemplateId: string;
  dataType: {
    code: DataType;
    name: string;
  };
  validLimits: string[];
  unit: string;
};

export type SvcRespData = ExpertRule | null;

export type RequestRespData = ExpertRule | null;

export type ApiQ = {
  id: number;
};

export type ApiR = ListResponse<ExpertRule | null>;
