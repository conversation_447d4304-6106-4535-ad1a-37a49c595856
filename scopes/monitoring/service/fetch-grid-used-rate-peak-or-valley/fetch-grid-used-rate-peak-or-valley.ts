/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-2-21
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './fetch-grid-used-rate-peak-or-valley.type';

const endpoint = '/dccm/pg/grid/used/top';

/**
 * @see [Doc](http://172.16.0.17:13000/project/152/interface/api/20262)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (params: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, SvcQuery>(
      endpoint,
      params
    );
    if (error || data === null) {
      return {
        ...rest,
        error,
        data: 0,
      };
    }

    return { error, data, ...rest };
  };
}
