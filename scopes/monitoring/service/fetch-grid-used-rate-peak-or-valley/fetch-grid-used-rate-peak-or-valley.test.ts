/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-2-21
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchGridUsedRatePeakOrValley as webService } from './fetch-grid-used-rate-peak-or-valley.browser';
import { fetchGridUsedRatePeakOrValley as nodeService } from './fetch-grid-used-rate-peak-or-valley.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({
    startTime: '2022-06-13',
    endTime: '2022-06-13',
    blockGuid: 'EC06.A',
    type: 'GRID',
    mode: 'max',
  });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    startTime: '2022-06-13',
    endTime: '2022-06-13',
    blockGuid: 'EC06.A',
    type: 'GRID',
    mode: 'min',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService({
    startTime: '2022-06-13',
    endTime: '2022-06-13',
    blockGuid: 'EC06.A',
    type: 'GRID',
    mode: 'max',
  });

  expect(error).toBe(undefined);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({
    startTime: '2022-06-13',
    endTime: '2022-06-13',
    blockGuid: 'EC06.A',
    type: 'GRID',
    mode: 'min',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
