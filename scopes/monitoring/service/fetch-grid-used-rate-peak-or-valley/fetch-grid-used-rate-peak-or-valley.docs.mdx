---
description: 'A fetchGridUsedRatePeakOrValley HTTP API service.'
labels: ['service', 'http']
---

## 概述

查询机柜使用率峰|谷值

## 使用

### Browser

```ts
import { fetchGridUsedRatePeakOrValley } from '@manyun/monitoring.service.fetch-grid-used-rate-peak-or-valley';

const { error, data } = await fetchGridUsedRatePeakOrValley({
  startTime: '2022-06-13',
  endTime: '2022-06-13',
  blockGuid: 'EC06.A',
  type: 'GRID',
  mode: 'max',
});
```
