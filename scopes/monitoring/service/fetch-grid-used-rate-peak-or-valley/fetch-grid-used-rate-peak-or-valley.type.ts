/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-2-21
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  startTime: string;
  endTime: string;
  type: 'GRID' | 'ROOM';
  mode: 'max' | 'min';
  blockGuid: string;
  roomGuid?: string;
};

export type SvcRespData = number;

export type RequestRespData = number | null;

export type ApiR = Response<RequestRespData>;
