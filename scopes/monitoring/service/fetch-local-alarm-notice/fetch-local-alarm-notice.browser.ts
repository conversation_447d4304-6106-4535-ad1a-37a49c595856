/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-12-5
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-local-alarm-notice';
import type { ApiArgs, ApiResponse } from './fetch-local-alarm-notice.type';

const executor = getExecutor(webRequest);

/**
 * @param args
 * @returns
 */
export function fetchLocalAlarmNotice(
  args: ApiArgs
): Promise<EnhancedAxiosResponse<ApiResponse['data']>> {
  return executor(args);
}
