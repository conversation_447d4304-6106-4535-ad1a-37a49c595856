/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-12-5
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiArgs, ApiResponse } from './fetch-local-alarm-notice.type';

const endpoint = '/dccm/alarm/inform/detail/local';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/158/interface/api/25826)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (args: ApiArgs): Promise<EnhancedAxiosResponse<ApiResponse['data']>> => {
    return await request.tryPost<ApiResponse['data'], ApiArgs>(endpoint, args);
  };
}
