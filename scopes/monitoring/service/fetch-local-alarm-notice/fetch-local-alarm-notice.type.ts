/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-12-5
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import type { BackendAlarmNotificationConfigureType } from '@manyun/monitoring.model.notification-configure-item';

export type ApiArgs = {
  /**
   * 通报项id
   */
  id: number;
};

export type InformTarget = {
  /**
   * 目标用户id
   */
  targetId: string;
  /**
   * 目标类型
   */
  targetType: 'USER' | 'ROLE';
  /**
   * 角色
   */
  roleCodes: string;
  /**
   * 角色描述
   */
  roleNames: string;
  /**
   * 排版状态
   */
  dutyStatus: 'ALL' | 'ON_DUTY' | 'NOT_ON_DUTY';
  /**
   * 通知渠道
   */
  channels: string;
};

export type LocalAlarmNoticeDetail = {
  /**
   * 通报项id
   */
  id: number;
  /**
   * 目标机房或楼栋
   */
  targetId?: string;
  /**
   * 目标类型
   */
  targetType: string;
  /**
   * 通报项名称
   */
  name: string;
  /**
   * 通报类型
   */
  type: BackendAlarmNotificationConfigureType;
  /**
   * 通报规则时间（单位：秒）
   */
  rule: number;
  /**
   * 适用告警等级（逗号隔开）
   */
  alarmLevels: string;
  /**
   * 适用专业类型（逗号隔开）
   */
  deviceType?: string;
  /**
   * true-启用，false不启用
   */
  notified: boolean;
  /**
   * webhook
   */
  webhook: boolean;
  /** webhook id列表，多个逗号隔开 */
  webhookId?: string;
  schemeList?: { id: number; name: string }[];
  /**
   * 操作人id
   */
  operatorId: number;
  /**
   * 操作人
   */
  operatorName: string;
  /**
   * 创建时间
   */
  gmtCreate: string;
  /**
   * 修改时间
   */
  gmtModified: string;
  informTargetList: InformTarget[];
};

export type ApiResponse = Response<LocalAlarmNoticeDetail>;
