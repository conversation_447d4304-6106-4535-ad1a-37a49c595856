/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-22
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type AlarmMergeInfo = {
  id: number;
  /*告警开始时间*/
  triggerTime: string;
  /*告警恢复时间*/
  recoverTime: string;
  /*告警告警值*/
  triggerSnapshot: string;
};
export type SvcQuery = {
  idcTag: string;
  id: number;
};

export type ApiQ = SvcQuery;

export type RequestRespData = {
  data: AlarmMergeInfo[];
  total: number;
};

export type SvcRespData = {
  data: AlarmMergeInfo[];
  total: number;
};

export type ApiR = ListResponse<RequestRespData[] | null>;
