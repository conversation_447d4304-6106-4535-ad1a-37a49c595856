/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-22
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-alarm-merge';
import type { SvcQuery, SvcRespData } from './fetch-alarm-merge.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function fetchAlarmMerge(svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
