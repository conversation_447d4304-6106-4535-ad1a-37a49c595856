/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-1
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './export-channel';
import type { SvcQuery, SvcRespData } from './export-channel.type';

const executor = getExecutor(webRequest);

/**
 * @param params
 * @returns
 */
export function exportChannel(params: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(params);
}
