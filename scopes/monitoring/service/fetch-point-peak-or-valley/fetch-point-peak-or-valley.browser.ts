/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-2-21
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-point-peak-or-valley';
import type { SvcQuery, SvcRespData } from './fetch-point-peak-or-valley.type';

const executor = getExecutor(webRequest);

/**
 * @param params
 * @returns
 */
export function fetchPointPeakOrValley(
  params: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(params);
}
