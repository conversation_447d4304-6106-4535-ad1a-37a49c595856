/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-2-21
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { RequestRespData, SvcQuery, SvcRespData } from './fetch-point-peak-or-valley.type';

const endpoint = '/dccm/pg/point/data/agg/query';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/152/interface/api/26778)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (params: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    return await request.tryPost<RequestRespData, SvcQuery>(endpoint, params);
  };
}
