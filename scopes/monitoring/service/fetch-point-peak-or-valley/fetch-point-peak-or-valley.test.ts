/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-2-21
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchPointPeakOrValley as webService } from './fetch-point-peak-or-valley.browser';
import { fetchPointPeakOrValley as nodeService } from './fetch-point-peak-or-valley.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({
    deviceGuid: '1',
    pointCode: '1010101',
    mode: 'min',
    idc: 'EC01',
  });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    deviceGuid: '1',
    pointCode: '1010101',
    mode: 'min',
    idc: 'EC02',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService({
    deviceGuid: '1',
    pointCode: '1010101',
    mode: 'min',
    idc: 'EC01',
  });

  expect(error).toBe(undefined);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({
    deviceGuid: '1',
    pointCode: '1010101',
    mode: 'min',
    idc: 'EC02',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
