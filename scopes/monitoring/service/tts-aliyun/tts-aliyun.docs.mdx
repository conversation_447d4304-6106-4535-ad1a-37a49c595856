---
description: 'A ttsAliyun HTTP API service.'
labels: ['service', 'http']
---

## 概述

[阿里云长文本语音合成服务](https://help.aliyun.com/document_detail/130508.html)

## 使用

### Browser

```ts
import { ttsAliyun } from '@manyun/monitoring.service.tts-aliyun';

const { error, data } = await ttsAliyun({ text: 'Hello world!' });
```

### Node

```ts
import { ttsAliyun } from '@manyun/monitoring.service.tts-aliyun/dist/index.node';
```
