/**
 * <AUTHOR> W <<EMAIL>>
 * @since 2022-8-9
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { ttsAliyun as webService } from './tts-aliyun.browser';
import { ttsAliyun as nodeService } from './tts-aliyun.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    text: 'Hello world!',
  });

  expect(error).toBe(undefined);
  // Why it's not an instance of `ArrayBuffer`?
  // See https://github.com/axios/axios/blob/main/lib/adapters/http.js#L189
  expect(data).toBeInstanceOf(Buffer);
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({
    text: 'Hello world!',
  });

  expect(error).toBe(undefined);
  expect(data).toBeInstanceOf(Buffer);
});
