/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-9
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, SvcQuery } from './tts-aliyun.type';

const endpoint = '/dcom/voice';

/**
 * @see [Doc](http://172.16.0.17:13000/project/160/interface/api/18951)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (
    svcQuery: SvcQuery,
    signal?: AbortSignal
  ): Promise<EnhancedAxiosResponse<ArrayBuffer | null>> => {
    const params: ApiQ = {
      text: svcQuery.text,
      voiceType: svcQuery.voice ?? 'qingqing',
    };

    const { error, data, ...rest } = await request.tryPost<ArrayBuffer | null, ApiQ>(
      endpoint,
      params,
      {
        cache: false,
        responseType: 'arraybuffer',
        signal,
        timeout: 60 * 1000,
      }
    );
    if (error || data === null) {
      return {
        error,
        data: null,
        ...rest,
      };
    }

    return { error, data, ...rest };
  };
}
