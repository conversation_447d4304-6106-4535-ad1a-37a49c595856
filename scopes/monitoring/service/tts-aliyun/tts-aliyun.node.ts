/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-9
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './tts-aliyun';
import type { SvcQuery } from './tts-aliyun.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function ttsAliyun(
  svcQuery: SvcQuery,
  signal?: AbortSignal
): Promise<EnhancedAxiosResponse<ArrayBuffer | null>> {
  return executor(svcQuery, signal);
}
