export type VoiceType = {
  name: string;
  voice: string;
  type: 'female-standard' | /*温柔女声*/ 'female-gentle' | 'male-standard';
  scenarios:
    | /* 通用*/ 'general'
    | /* 客服*/ 'customer-services'
    | /* 童声 */ 'kids'
    | /* 英文*/ 'English'
    | /* 方言 */ 'dialect'
    | /* 文学 */ 'literary'
    | /* 多语种 */ 'multilingual'
    | /* Ultra HD 超高清 */ 'UHD'
    | /* 直播 */ 'live-streaming'
    | /* 美式英文 */ 'English-US';
  languageSupport: 'Chinese-n-English' | 'Chinese-only' | 'English-only' | 'Cantonese-n-English';
  /**
   * 支持采样率（Hz）
   */
  sampleRate: '8K/16K' | '8K/16K/24K' | '8K/16K/24K/48K';
  /**
   * 支持字\句级别时间戳
   */
  timestampSupport: boolean;
  /**
   * 支持儿化音
   */
  rhoticAccentSupport: boolean;
  quality: 'lite' | 'standard' | /* 精品版 */ 'excellent';
};

export const VoiceTypes: VoiceType[] = [
  {
    name: '知琪',
    voice: 'zhiqi',
    type: 'female-gentle',
    scenarios: 'UHD',
    languageSupport: 'Cantonese-n-English',
    sampleRate: '8K/16K/24K/48K',
    timestampSupport: true,
    rhoticAccentSupport: false,
    quality: 'excellent',
  },
];
