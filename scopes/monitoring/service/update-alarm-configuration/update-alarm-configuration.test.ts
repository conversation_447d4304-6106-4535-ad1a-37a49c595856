/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-12
 *
 * @packageDocumentation
 */
import { AlarmTemplateType } from '@manyun/monitoring.model.alarm-template';
import { useRemoteMock } from '@manyun/service.request';

import { updateAlarmConfiguration as webService } from './update-alarm-configuration.browser';
import { updateAlarmConfiguration as nodeService } from './update-alarm-configuration.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({
    id: 1,
    type: AlarmTemplateType.Device,
    name: 'mock',
    target: '10010',
    monitorItems: [],
    enable: false,
  });

  expect(error).toBe(undefined);
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService({
    id: 1,
    type: AlarmTemplateType.Device,
    name: 'mock',
    target: '10010',
    monitorItems: [],
    enable: false,
  });

  expect(error).toBe(undefined);
});
