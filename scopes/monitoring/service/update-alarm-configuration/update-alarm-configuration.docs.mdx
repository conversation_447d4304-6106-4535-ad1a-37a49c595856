---
description: 'A updateAlarmConfiguration HTTP API service.'
labels: ['service', 'http', update-alarm-configuration]
---

## 概述

更新告警模板配置

## 使用

### Browser

```ts
import { updateAlarmConfiguration } from '@manyun/monitoring.update-alarm-configuration';

const { error, data } = await updateAlarmConfiguration({
  id: 1,
  type: AlarmTemplateType.Device,
  name: 'mock',
  target: '10010',
  monitorItems: [],
  enable: false,
});
const { error, data } = await updateAlarmConfiguration();
```

### Node

```ts
import { updateAlarmConfiguration } from '@manyun/monitoring.update-alarm-configuration/dist/index.node';

const { data } = await updateAlarmConfiguration({
  id: 1,
  type: AlarmTemplateType.Device,
  name: 'mock',
  target: '10010',
  monitorItems: [],
  enable: false,
});

try {
  const { data } = await updateAlarmConfiguration();
} catch (error) {
  // ...
}
```
