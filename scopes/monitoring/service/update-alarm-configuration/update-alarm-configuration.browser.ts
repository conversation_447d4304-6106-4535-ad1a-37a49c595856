/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-12
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './update-alarm-configuration';
import type { SvcQuery, SvcRespData } from './update-alarm-configuration.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function updateAlarmConfiguration(
  variant: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
