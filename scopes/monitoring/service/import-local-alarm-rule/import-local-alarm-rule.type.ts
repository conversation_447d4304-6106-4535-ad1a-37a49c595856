/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-5-22
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common.js';

export type ApiQ = FormData;

export type ImportexcelCheck = {
  errMessage: Record<string, string>;
  errDto: {
    /**
     * 测点分类
     */
    type: string;
    /**
     * 测点编码
     */
    pointCode: string;
    /**
     * 测点名称
     */
    pointName: string;
    /**
     * 测点类型
     */
    dataType: string;
    /**
     * 单位
     */
    unit: string;
    /**
     * 状态含义
     */
    validLimits: string;
    /**
     * 告警规则ID
     */
    itemId: string;
    /**
     * 前置条件
     */
    preRule: string;
    /**
     * 告警条件
     */
    alarmRule: string;
    /**
     * 触发观察周期(s)
     */
    triggerInterval: number;
    /**
     * 恢复观察周期(s)
     */
    recoverInterval: number;
    /**
     * 告警等级
     */
    alarmLevel: string;
    /**
     * 告警类型
     */
    alarmType: string;
    /**
     * 启用状态
     */
    available: string;
    /**
     * 规则锁
     */
    lock: string;
    /**
     * 规则备注
     */
    description: string;
    /**
     * 清单
     */
    guidList: string;
    /**
     * 设备类型
     */
    deviceType: string;
  };
};

export type ApiResponseData = {
  faultTotal: number;
  checkTotal: number;
  correctTotal: number;
  excelCheckErrDtos: ImportexcelCheck[];
};

export type ApiResponse = Response<ApiResponseData>;
