/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-5-22
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, ApiResponse } from './import-local-alarm-rule.type.js';

const endpoint = '/dccm/local/monitor/item/import';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/228/interface/api/27041)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: ApiQ): Promise<EnhancedAxiosResponse<ApiResponse['data']>> => {
    return await request.tryPost<ApiResponse['data']>(endpoint, svcQuery);
  };
}
