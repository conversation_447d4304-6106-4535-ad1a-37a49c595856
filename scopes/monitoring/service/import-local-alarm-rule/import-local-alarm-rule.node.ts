/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-5-22
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './import-local-alarm-rule.js';
import type { ApiQ, ApiResponse } from './import-local-alarm-rule.type.js';

const executor = getExecutor(nodeRequest);

/**
 * @returns
 */
export function importLocalAlarmRule(
  params: ApiQ
): Promise<EnhancedAxiosResponse<ApiResponse['data']>> {
  return executor(params);
}
