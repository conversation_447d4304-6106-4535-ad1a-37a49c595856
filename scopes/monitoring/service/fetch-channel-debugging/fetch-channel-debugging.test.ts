/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-30
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchChannelDebugging as webService } from './fetch-channel-debugging.browser';
import { fetchChannelDebugging as nodeService } from './fetch-channel-debugging.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ channelId: '4455' });

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('length');
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({ channelId: '4455' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(error).toBe(undefined);
  expect(data).toHaveProperty('length');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({ channelId: '4455' });

  expect(error).toBe(undefined);
  expect(error).toBe(undefined);
  expect(data).toHaveProperty('length');
});

test('[node] should resolve error response', async () => {
  const { error, data } = await nodeService({ channelId: '4455' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(error).toBe(undefined);
  expect(data).toHaveProperty('length');
});
