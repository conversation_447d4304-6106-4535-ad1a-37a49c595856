/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-30
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-channel-debugging';
import type { SvcQuery, SvcRespData } from './fetch-channel-debugging.type';

const executor = getExecutor(webRequest);

/**
 * @param params
 * @returns
 */
export function fetchChannelDebugging(
  params: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(params);
}
