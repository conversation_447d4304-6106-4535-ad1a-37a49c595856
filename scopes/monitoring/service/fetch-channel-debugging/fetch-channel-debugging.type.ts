/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-30
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = ApiQ;

export type PointData = {
  pointCode: string;
  pointName: string;
  value: string;
  outOfRange: boolean; //是否超过测点工作区间
  error: boolean; //是否有异常
  errMsg: boolean;
};
export type ChannelDebugging = {
  deviceName: string;
  deviceType: string;
  deviceGuid: string;
  pointDataList?: PointData[];
};

export type SvcRespData = { data: ChannelDebugging[]; total: number };

export type RequestRespData = { data: ChannelDebugging[]; total: number };

export type ApiQ = {
  channelId: string;
};

export type ApiR = ListResponse<ChannelDebugging[] | null>;
