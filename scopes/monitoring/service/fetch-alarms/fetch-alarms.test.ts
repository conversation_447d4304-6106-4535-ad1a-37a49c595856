/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-12
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchAlarms as webService } from './fetch-alarms.browser';

let webMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
});
afterAll(() => {
  webMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ pageNum: 1, pageSize: 10, idcTag: 'EC01' });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({ pageNum: 1, pageSize: 10, idcTag: 'EC02' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
