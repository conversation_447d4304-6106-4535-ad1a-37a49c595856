/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-12
 *
 * @packageDocumentation
 */
import { Alarm } from '@manyun/monitoring.model.alarm';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { RequestRespData, SvcQuery, SvcRespData } from './fetch-alarms.type';

const endpoint = '/dcim/alarm/query';

/**
 * @see [查询告警列表](http://172.16.0.17:13000/project/86/interface/api/2224)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, SvcQuery>(
      endpoint,
      variant
    );

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      error,
      data: { data: data.data.map(Alarm.fromApiObject), total: data.total },
      ...rest,
    };
  };
}
