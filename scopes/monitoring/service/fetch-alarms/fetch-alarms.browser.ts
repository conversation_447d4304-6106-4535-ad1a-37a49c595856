/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-12
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-alarms';
import type { SvcQuery, SvcRespData } from './fetch-alarms.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchAlarms(variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
