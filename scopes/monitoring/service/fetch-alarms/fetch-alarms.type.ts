/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-12
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type {
  Alarm,
  BackMergeStatus,
  BackendAlarm,
  BackendAlarmLifecycleState,
  BackendAlarmState,
  BackendExpectStatus,
} from '@manyun/monitoring.model.alarm';

export type SortField =
  | 'alarmLevel'
  | 'triggerTime'
  | 'gmtCreate'
  | 'recoverTime'
  | 'confirmTime'
  | 'removeTime'
  | 'alarmStatus';
export type SvcQuery = {
  idcTag: string;
  isQueryData?: boolean;
  blockTags?: string[];
  roomTags?: string[];
  alarmType?: string;
  confirmByName?: string;
  triggerStatus?: BackendAlarmState[];
  alarmId?: number;
  alarmLevel?: number[];
  alarmStatus?: BackendAlarmLifecycleState[];
  nofityContent?: string;
  alarmCreateTimeStart?: number;
  alarmCreateTimeEnd?: number;
  recoverTimeStart?: number;
  recoverTimeEnd?: number;
  deviceName?: string;
  deviceTypeList?: string[];
  status?: 'REMOVED' | null;
  deviceGuid?: string;
  pageSize: number;
  pageNum: number;
  changeId?: number;
  eventId?: string;
  id?: number;
  isExpected?: boolean;
  isMerge?: boolean;
  sortField?: SortField;
  sortOrder?: 'ASCEND' | 'DESCEND';
  triggerTime?: string;
  triggerTimeEnd?: string;
  notifyContent?: string;
  pointCode?: string;
  mergeStatusList?: BackMergeStatus[];
  expectStatusList?: BackendExpectStatus[];
  bizId?: string;
  bizType?: string;
};

export type SvcRespData = {
  data: Alarm[];
  total: number;
};

export type RequestRespData = {
  data: BackendAlarm[] | null;
  total: number;
} | null;

export type ApiR = ListResponse<BackendAlarm[] | null>;
