/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-21
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { updateLocalAlarmRule as webService } from './update-local-alarm-rule.browser';
import { updateLocalAlarmRule as nodeService } from './update-local-alarm-rule.node';
import type { ApiQ } from './update-local-alarm-rule.type';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

const params: ApiQ = {
  id: 3,
  blockGuid: 'EC06.A',
  alarmLevel: '1',
  alarmType: 'ERROR',
  description: '123',
  deviceType: '10101',
  enable: true,
  incidentType: ['1', '1'],
  incidentTypeName: ['电力故障', '电力超限'],
  isCreateIncident: true,
  itemType: 'POINT',
  lock: true,
  name: '相电流Ia',
  notifyRules: [],
  pointCode: '1004000',
  pointName: '相电流Ia',
  preTriggerRules: [],
  recoverInterval: 2,
  spaceGuid: null,
  triggerCount: 1,
  triggerRule: undefined,
  triggerRules: [],
};

const errorParams: ApiQ = {
  ...params,
  blockGuid: '',
};

test('[web] should resolve success response', async () => {
  const { error, data } = await webService(params);

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService(errorParams);

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await webService(params);

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService(errorParams);

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
