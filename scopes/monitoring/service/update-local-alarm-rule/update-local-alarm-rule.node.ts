/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-21
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './update-local-alarm-rule';
import type { SvcQuery, SvcRespData } from './update-local-alarm-rule.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function updateLocalAlarmRule(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
