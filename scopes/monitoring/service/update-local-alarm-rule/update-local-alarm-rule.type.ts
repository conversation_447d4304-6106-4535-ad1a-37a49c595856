/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-21
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { BackendMonitoringItem } from '@manyun/monitoring.model.monitoring-item';

export type SvcQuery = BackendMonitoringItem & {
  blockGuid: string;
};

export type RequestRespData = boolean | null;

export type SvcRespData = RequestRespData;

export type ApiQ = SvcQuery;

export type ApiR = WriteResponse;
