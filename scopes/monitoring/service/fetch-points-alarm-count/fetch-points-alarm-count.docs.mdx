---
description: 'A fetchPointsAlarmCount HTTP API service.'
labels: ['service', 'http']
---

## 概述

查询空间位置下存在告警的点位对应的告警数

## 使用

### Browser

```ts
import { fetchPointsAlarmCount } from '@manyun/monitoring.service.fetch-points-alarm-count';

const { error, data } = await fetchPointsAlarmCount({ idcTag: 'EC01' });
const { error, data } = await fetchPointsAlarmCount({ idcTag: 'EC02' });
```

### Node

```ts
import { fetchPointsAlarmCount } from '@manyun/monitoring.service.fetch-points-alarm-count/dist/index.node';

const { data } = await fetchPointsAlarmCount({ idcTag: 'EC01' });
const { error, data } = await fetchPointsAlarmCount({ idcTag: 'EC02' });
```
