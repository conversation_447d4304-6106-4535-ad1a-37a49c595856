/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-1
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-points-alarm-count';
import type { SvcQuery, SvcRespData } from './fetch-points-alarm-count.type';

const executor = getExecutor(nodeRequest);

/**
 * @param params
 * @returns
 */
export function fetchPointsAlarmCount(
  params: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(params);
}
