/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-1
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  idcTag: string;
  blockTag?: string;
  roomTag?: string;
};

export type DeviceCount = Record<string, Record<string, number>>;

export type AlarmCounts = {
  roomAlarmCount: DeviceCount;
  blockDeviceCount: DeviceCount;
  deviceCount: Record<string, number>;
  totalAlarmCount: number;
};

export type SvcRespData = AlarmCounts | null;

export type RequestRespData = AlarmCounts | null;

export type ApiQ = {
  blockTag?: string;
  roomTag?: string;
};

export type ApiR = Response<RequestRespData>;
