/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-30
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { updateChannelStatus as webService } from './update-channel-status.browser';
import { updateChannelStatus as nodeService } from './update-channel-status.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ channelIds: ['5463'], channelStatus: 'ON' });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { data } = await webService({
    channelIds: ['1'],
    channelStatus: 'ON',
  });

  expect(data).toBe(null);
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({ channelIds: ['5463'], channelStatus: 'ON' });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[node] should resolve error response', async () => {
  const { data } = await nodeService({ channelIds: ['1'], channelStatus: 'ON' });

  expect(data).toBe(null);
});
