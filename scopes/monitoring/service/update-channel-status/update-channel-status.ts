/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-30
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './update-channel-status.type';

const endpoint = '/dccm/channel/batch/update/status';

/**
 * @see [批量修改通道启用状态](https://manyun.yuque.com/ewe5b3/sbs6q1/kguhhd)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (params: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    return { error, data, ...rest };
  };
}
