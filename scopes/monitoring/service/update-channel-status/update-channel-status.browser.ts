/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-30
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './update-channel-status';
import type { SvcQuery, SvcRespData } from './update-channel-status.type';

const executor = getExecutor(webRequest);

/**
 * @param params
 * @returns
 */
export function updateChannelStatus(params: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(params);
}
