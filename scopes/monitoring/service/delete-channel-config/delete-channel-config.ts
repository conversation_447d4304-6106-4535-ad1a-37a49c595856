/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-30
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './delete-channel-config.type';

const endpoint = '/dccm/channel/delete';

/**
 * @see [通道删除](http://172.16.0.17:13000/project/182/interface/api/19390)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (params: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    return { error, data, ...rest };
  };
}
