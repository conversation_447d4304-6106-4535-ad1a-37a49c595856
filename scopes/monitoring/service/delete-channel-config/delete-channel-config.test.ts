/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-30
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { deleteChannelConfig as webService } from './delete-channel-config.browser';
import { deleteChannelConfig as nodeService } from './delete-channel-config.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});
test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ channelId: '5463' });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { data } = await webService({ channelId: '1' });

  expect(data).toBe(null);
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({ channelId: '5463' });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[node] should resolve error response', async () => {
  const { data } = await nodeService({ channelId: '1' });

  expect(data).toBe(null);
});
