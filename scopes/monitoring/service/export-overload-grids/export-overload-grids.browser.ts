/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-2-21
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './export-overload-grids';
import type { SvcQuery, SvcRespData } from './export-overload-grids.type';

const executor = getExecutor(webRequest);

/**
 * @param params
 * @returns
 */
export function exportOverloadGrids(params: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(params);
}
