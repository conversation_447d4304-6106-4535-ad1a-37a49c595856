/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-2-21
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { RequestRespData, SvcQuery, SvcRespData } from './export-overload-grids.type';

const endpoint = '/dccm/real/grid/load/export';

/**
 * @see [Doc](http://172.16.0.17:13000/project/152/interface/api/20289)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (params: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    return await request.tryPost<RequestRespData, SvcQuery>(endpoint, params, {
      responseType: 'blob',
    });
  };
}
