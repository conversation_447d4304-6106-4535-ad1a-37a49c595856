/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-2-21
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { exportOverloadGrids as webService } from './export-overload-grids.browser';
import { exportOverloadGrids as nodeService } from './export-overload-grids.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({ idcTag: 'EC06', blockTag: 'A', pointCode: '1010101' });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({ idcTag: 'EC06', blockTag: 'A', pointCode: '1010102' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService({ idcTag: 'EC06', blockTag: 'A', pointCode: '1010101' });

  expect(error).toBe(undefined);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({ idcTag: 'EC06', blockTag: 'A', pointCode: '1010102' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
