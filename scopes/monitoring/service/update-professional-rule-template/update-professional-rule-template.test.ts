/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-15
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { updateProfessionalRuleTemplate } from './update-professional-rule-template.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve error response', async () => {
  const { error } = await updateProfessionalRuleTemplate({
    id: Number.NaN,
    name: '',
    bizCode: '',
    ruleCode: '',
    deviceType: '',
    triggerDesc: '',
    triggerInterval: Number.NaN,
    recoverInterval: Number.NaN,
    changeTemplateId: '',
    description: '',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
