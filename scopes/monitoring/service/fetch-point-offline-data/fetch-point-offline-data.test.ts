/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-9-26
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchPointOfflineData as webService } from './fetch-point-offline-data.browser';
import { fetchPointOfflineData as nodeService } from './fetch-point-offline-data.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    startTime: '2013',
    endTime: '2023',
    dateType: 'YEAR',
    deviceGuid: 'EC01',
    pointCode: 'EC01',
  });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({
    startTime: '2013',
    endTime: '2023',
    dateType: 'YEAR',
    deviceGuid: 'EC02',
    pointCode: 'EC02',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({
    startTime: '2013',
    endTime: '2023',
    dateType: 'YEAR',
    deviceGuid: 'EC01',
    pointCode: 'EC01',
  });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[node] should resolve error response', async () => {
  const { error, data } = await nodeService({
    startTime: '2013',
    endTime: '2023',
    dateType: 'YEAR',
    deviceGuid: 'EC02',
    pointCode: 'EC02',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});
