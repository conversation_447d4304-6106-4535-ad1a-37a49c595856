/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-9-26
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type ApiArgs = {
  /**
   * 日期  2022-11-14    2022-11   2022
   */
  startTime: string;
  /**
   * 日期  2022-11-14    2022-11   2022
   */
  endTime: string;
  /**
   * DAY,MONTH,YEAR
   */
  dateType: string;
  /**
   * 空间、设备guid
   */
  deviceGuid: string;
  /**
   * 查询点位code，WUE  CUE
   */
  pointCode: string;
};

export type ApiResponseData = {
  /**
   * 空间、设备guid
   */
  deviceGuid: string;
  /**
   * 查询点位code
   */
  pointCode: string;
  /**
   * 时间   2022-11-14    2022-11   2022
   */
  time: string;
  /**
   * 值
   */
  dataValue?: number | null;
};

export type ApiResponse = ListResponse<ApiResponseData[]>;
