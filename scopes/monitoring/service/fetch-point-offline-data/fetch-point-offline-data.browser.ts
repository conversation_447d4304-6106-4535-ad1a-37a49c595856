/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-9-26
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-point-offline-data';
import type { ApiArgs, ApiResponse } from './fetch-point-offline-data.type';

const executor = getExecutor(webRequest);

/**
 * @param args
 * @returns
 */
export function fetchPointOfflineData(
  args: ApiArgs
): Promise<EnhancedAxiosResponse<Pick<ApiResponse, 'data' | 'total'>>> {
  return executor(args);
}
