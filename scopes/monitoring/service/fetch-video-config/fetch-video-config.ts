/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-26
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { RequestRespData, SvcRespData } from './fetch-video-config.type';

const endpoint = '/pm/store/video/configs';

/**
 * @see [Doc](http://172.16.0.17:13000/project/78/interface/api/19920)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    return await request.tryGet<RequestRespData>(endpoint);
  };
}
