/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-26
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchVideoConfig } from './fetch-video-config.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('should resolve success response', async () => {
  const { error } = await fetchVideoConfig();

  expect(error).toBe(undefined);
});
