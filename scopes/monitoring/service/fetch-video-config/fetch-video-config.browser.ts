/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-26
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-video-config';
import type { SvcRespData } from './fetch-video-config.type';

const executor = getExecutor(webRequest);

export function fetchVideoConfig(): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor();
}
