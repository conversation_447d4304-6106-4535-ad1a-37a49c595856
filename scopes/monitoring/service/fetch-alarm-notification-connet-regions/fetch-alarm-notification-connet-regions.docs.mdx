---
description: 'A fetchAlarmNotificationConnetRegions HTTP API service.'
labels: ['service', 'http', fetch-alarm-notification-connet-regions]
---

## 概述

查询告警通报配置生效域

## 使用

### Browser

```ts
import { fetchAlarmNotificationConnetRegions } from '@manyun/monitoring.service.fetch-alarm-notification-connet-regions';

const { error, data } = await fetchAlarmNotificationConnetRegions({ levelCode: '1' });
const { error, data } = await fetchAlarmNotificationConnetRegions({ levelCode: 'error' });
```

### Node

```ts
import { fetchAlarmNotificationConnetRegions } from '@manyun/monitoring.service.fetch-alarm-notification-connet-regions/dist/index.node';

const { data } = await fetchAlarmNotificationConnetRegions({ levelCode: '1' });
const { error, data } = await fetchAlarmNotificationConnetRegions({ levelCode: 'error' });
```
