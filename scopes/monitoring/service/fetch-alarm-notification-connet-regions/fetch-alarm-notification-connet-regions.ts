/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-2
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './fetch-alarm-notification-connet-regions.type';

const endpoint = '/dcim/alarm/inform/region/query';

/**
 * 查询告警通报配置生效域
 * @see [Doc](http://172.16.0.17:13000/project/158/interface/api/18681)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = { alarmLevel: variant.levelCode };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }
    return { error, data: { data: data.data.map(d => ({ ...d })), total: data.total }, ...rest };
  };
}
