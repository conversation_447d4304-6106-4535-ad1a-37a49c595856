/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-2
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = { levelCode: string };

export type BackendConnectRegion = {
  id: number;
  region: string | null;
  idcTag: string | null;
  roomTag: string | null;
  blockTag: string | null;
  gmtCreate?: string;
  gmtModified?: string;
  operatorId?: number;
  operatorName?: string;
};

export type ConnectRegion = {
  id: number;
  region: string | null;
  idcTag: string | null;
  blockTag: string | null;
  gmtCreate?: string;
  gmtModified?: string;
  operatorId?: number;
  operatorName?: string;
};

export type SvcRespData = {
  data: ConnectRegion[];
  total: number;
};

export type RequestRespData = {
  data: BackendConnectRegion[] | null;
  total: number;
} | null;

export type ApiQ = {
  alarmLevel: string;
};

export type ApiR = ListResponse<ConnectRegion[] | null>;
