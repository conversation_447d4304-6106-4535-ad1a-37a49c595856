/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-6-9
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { deleteAlarmsByClickOnce as webService } from './delete-alarms-by-click-once.browser';
import { deleteAlarmsByClickOnce as nodeService } from './delete-alarms-by-click-once.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    alarmIds: [31439],
    alarmReason: 'test',
    removeDesc: 'test',
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({ alarmIds: [1], alarmReason: 'test', removeDesc: 'test' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await webService({
    alarmIds: [31439],
    alarmReason: 'test',
    removeDesc: 'test',
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({ alarmIds: [1], alarmReason: 'test', removeDesc: 'test' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
