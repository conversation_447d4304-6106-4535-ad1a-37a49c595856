/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-6-9
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './delete-alarms-by-click-once.type';

const endpoint = '/dcim/alarm/remove';

/**
 * @see [一键下盯屏](http://172.16.0.17:13000/project/86/interface/api/24087)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      svcQuery
    );

    return { error, data, ...rest };
  };
}
