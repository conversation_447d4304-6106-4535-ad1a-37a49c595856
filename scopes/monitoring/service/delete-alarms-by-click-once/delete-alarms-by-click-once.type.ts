/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-6-9
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  alarmIds: number[];
  alarmReason: string;
  removeDesc: string;
};

export type RequestRespData = boolean | null;

export type SvcRespData = RequestRespData;

export type ApiQ = SvcQuery;

export type ApiR = WriteResponse;
