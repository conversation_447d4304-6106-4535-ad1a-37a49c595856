/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-6-9
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './delete-alarms-by-click-once';
import type { SvcQuery, SvcRespData } from './delete-alarms-by-click-once.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function deleteAlarmsByClickOnce(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
