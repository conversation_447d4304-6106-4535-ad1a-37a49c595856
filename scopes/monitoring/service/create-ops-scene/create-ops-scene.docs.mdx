---
description: '创建动态基线配置'
labels: ['service', 'http']
---

## 使用

### Browser

```ts
import { createOpsScene } from '@monitoring/service.create-ops-scene';

const params = {
  blockGuid: 'EC03.G',
  alarmCondition: 'UPPER',
  alarmType: 'WARN',
  alarmLevel: '1',
  enabled: false,
  configType: 'BLOCK',
  areaList: [
    {
      targetGuid: 'EC06.A.1-1',
      targetType: 'ROOM',
      targetName: 'SXDTYG_DA_A运营商机房',
      blockGuid: 'EC06.A',
      deviceType: '90103',
    },
  ],
  scenesName: '111',
  idcTag: 'EC03',
  pointList: [{ deviceType: '10101', pointCode: '1010101', pointName: '测点1' }],
};
const { error, data } = await createOpsScene(params);
```

```ts
import { updateOpsScene } from '@monitoring/service.update-ops-scene/dist/index.node';

const errorParams = {
  blockGuid: '',
  alarmCondition: 'UPPER',
  alarmType: 'WARN',
  alarmLevel: '1',
  enabled: false,
  configType: 'BLOCK',
  areaList: [
    {
      targetGuid: 'EC06.A.1-1',
      targetType: 'ROOM',
      targetName: 'SXDTYG_DA_A运营商机房',
      blockGuid: 'EC06.A',
      deviceType: '90103',
    },
  ],
  scenesName: '111',
  idcTag: 'EC03',
  pointList: [{ deviceType: '10101', pointCode: '1010101', pointName: '测点1' }],
};

const { error, data } = await createOpsScene(errorParams);
```
