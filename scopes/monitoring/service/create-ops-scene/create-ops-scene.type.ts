/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-21
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { DynamicBaselineConfigType } from '@manyun/monitoring.model.dynamic-baseline-setting';

export type SvcRespData = boolean | null;

export type RequestRespData = boolean | null;

export type ApiQ = {
  // 场景名称
  scenesName: string;
  idcTag: string;
  blockGuid: string;
  // 是否启用
  enabled: boolean;
  // 通报方式
  notifyWay?: string;
  // 监控模型
  alarmCondition: string;
  // 告警类型
  alarmType: string;
  // 告警等级
  alarmLevel: string;
  configType: DynamicBaselineConfigType;
  // 点位
  pointList: {
    deviceType: string;
    pointCode: string;
    pointName: string;
  }[];
  // 生效域
  areaList?: {
    blockGuid: string;
    targetGuid: string;
    targetType: string;
    targetName: string;
    deviceType: string;
  }[];
};

export type ApiR = WriteResponse;
