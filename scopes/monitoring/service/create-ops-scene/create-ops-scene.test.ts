/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-21
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { createOpsScene as webService } from './create-ops-scene.browser';
import { createOpsScene as nodeService } from './create-ops-scene.node';
import type { ApiQ } from './create-ops-scene.type';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

const params: ApiQ = {
  blockGuid: 'EC03.G',
  alarmCondition: 'UPPER',
  alarmType: 'WARN',
  alarmLevel: '1',
  enabled: false,
  configType: 'BLOCK',
  areaList: [
    {
      targetGuid: 'EC06.A.1-1',
      targetType: 'ROOM',
      targetName: 'SXDTYG_DA_A运营商机房',
      blockGuid: 'EC06.A',
      deviceType: '90103',
    },
  ],
  scenesName: '111',
  idcTag: 'EC03',
  pointList: [{ deviceType: '10101', pointCode: '1010101', pointName: '测点1' }],
};

const errorParams: ApiQ = {
  ...params,
  blockGuid: '',
};

test('[web] should resolve success response', async () => {
  const { error, data } = await webService(params);

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService(errorParams);

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await webService(params);

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService(errorParams);

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
