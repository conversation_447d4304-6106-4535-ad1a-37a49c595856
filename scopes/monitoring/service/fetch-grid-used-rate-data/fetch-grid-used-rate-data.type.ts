/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-2-21
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  startTime: string;
  endTime: string;
  blockGuid: string;
  type: 'GRID' | 'ROOM' | 'CUSTOMER';
  roomGuid?: string;
};

export type GridData = {
  dt: string;
  idcTag: string;
  blockGuid: string;
  roomGuid?: string;
  roomTag?: string;
  customer?: string;
  customerName?: string;
  usedNum: number;
  totalNum: number;
  sdNum: number;
  xdNum: number;
  useRated: number;
};

export type SvcRespData = {
  data: GridData[];
  total: number;
};

export type RequestRespData = {
  data: GridData[] | null;
  total: number;
} | null;

export type ApiR = ListResponse<GridData[] | null>;
