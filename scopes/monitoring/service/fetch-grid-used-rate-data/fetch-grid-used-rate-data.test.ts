/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-2-21
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchGridUsedRateData as webService } from './fetch-grid-used-rate-data.browser';
import { fetchGridUsedRateData as nodeService } from './fetch-grid-used-rate-data.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    startTime: '2022-06-13',
    endTime: '2022-06-13',
    blockGuid: 'EC06.A',
    type: 'CUSTOMER',
  });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({
    startTime: '2022-06-13',
    endTime: '2022-06-13',
    blockGuid: 'EC06.B',
    type: 'CUSTOMER',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({
    startTime: '2022-06-13',
    endTime: '2022-06-13',
    blockGuid: 'EC06.A',
    type: 'CUSTOMER',
  });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[node] should resolve error response', async () => {
  const { error, data } = await nodeService({
    startTime: '2022-06-13',
    endTime: '2022-06-13',
    blockGuid: 'EC06.B',
    type: 'CUSTOMER',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});
