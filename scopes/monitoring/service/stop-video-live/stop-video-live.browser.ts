/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-29
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './stop-video-live';
import type { SvcQuery, SvcRespData } from './stop-video-live.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function stopVideoLive(svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
