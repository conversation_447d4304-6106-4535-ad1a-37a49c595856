/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-29
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { stopVideoLive as webService } from './stop-video-live.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('should resolve error response', async () => {
  const { error } = await webService({ guid: '', sessionId: '' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
