/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-29
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './stop-video-live.type';

const endpoint = '/dcom/video/live/hls/stop';

/**
 * @see [Doc](http://172.16.0.17:13000/project/162/interface/api/18987)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = svcQuery;

    const { error, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    return { error, ...rest };
  };
}
