import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type {
  AlarmShieldsResponse,
  Maybe,
  QueryAlarmShieldsArgs,
} from '../generated-types/graphql';

export type AlarmShieldsData = {
  alarmShields: Maybe<AlarmShieldsResponse>;
};

export const GET_ALARM_SHIELDS_DATA: DocumentNode = gql`
  query GetAlarmShields($params: QueryAlarmShieldsParams!) {
    alarmShields(params: $params) {
      data {
        id
        name
        blockGuid
        effectStatus {
          code
          name
        }
        enable
        startTime
        endTime
        gmtCreate
        gmtModified
        operatorId
        operatorName
      }
      total
    }
  }
`;

export function useAlarmShields(
  options?: QueryHookOptions<AlarmShieldsData, QueryAlarmShieldsArgs>
): QueryResult<AlarmShieldsData, QueryAlarmShieldsArgs> {
  return useQuery(GET_ALARM_SHIELDS_DATA, {
    fetchPolicy: 'network-only',
    ...options,
  });
}

export function useLazyAlarmShields(
  options?: LazyQueryHookOptions<AlarmShieldsData, QueryAlarmShieldsArgs>
): LazyQueryResultTuple<AlarmShieldsData, QueryAlarmShieldsArgs> {
  return useLazyQuery(GET_ALARM_SHIELDS_DATA, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
