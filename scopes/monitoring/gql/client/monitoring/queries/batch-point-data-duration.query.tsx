import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type {
  BatchPointDataDurationResponse,
  Maybe,
  QueryBatchPointDataDurationArgs,
} from '../generated-types/graphql';

export type BatchPointDataDurationData = {
  batchPointDataDuration: Maybe<BatchPointDataDurationResponse>;
};

export const GET_BATCH_POINT_DURATION_DATA: DocumentNode = gql`
  query GetBatchPointDataDuration(
    $startTime: String!
    $endTime: String!
    $dateType: String!
    $pointGuidList: [PointGuidInput!]!
  ) {
    batchPointDataDuration(
      startTime: $startTime
      endTime: $endTime
      dateType: $dateType
      pointGuidList: $pointGuidList
    ) {
      data {
        deviceGuid
        pointCode
        time
        dataValue
      }
      total
    }
  }
`;

export function useBatchPointDataDuration(
  options?: QueryHookOptions<BatchPointDataDurationData, QueryBatchPointDataDurationArgs>
): QueryResult<BatchPointDataDurationData, QueryBatchPointDataDurationArgs> {
  return useQuery(GET_BATCH_POINT_DURATION_DATA, {
    fetchPolicy: 'network-only',
    ...options,
  });
}

export function useLazyBatchPointDataDuration(
  options?: LazyQueryHookOptions<BatchPointDataDurationData, QueryBatchPointDataDurationArgs>
): LazyQueryResultTuple<BatchPointDataDurationData, QueryBatchPointDataDurationArgs> {
  return useLazyQuery(GET_BATCH_POINT_DURATION_DATA, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
