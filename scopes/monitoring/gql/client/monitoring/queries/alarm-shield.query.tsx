import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type { AlarmShieldDetail, Maybe, QueryAlarmShieldArgs } from '../generated-types/graphql';

export type AlarmShieldData = {
  alarmShield: Maybe<AlarmShieldDetail>;
};

export const GET_ALARM_SHIELDS_DATA: DocumentNode = gql`
  query GetAlarmShield($id: Long!) {
    alarmShield(id: $id) {
      id
      name
      blockGuid
      effectStatus {
        code
        name
      }
      enable
      startTime
      endTime
      gmtCreate
      gmtModified
      operatorId
      operatorName
      deviceShieldScopes {
        guid
        type
        floorTag
        floorName
        roomTag
        roomName
        roomType
        deviceName
        deviceLabel
        deviceOperationStatus
        productModel
        vendorCode
        pointCode
        pointName
      }
      pointShieldScopes {
        guid
        type
        floorTag
        floorName
        roomTag
        roomName
        roomType
        deviceName
        deviceOperationStatus
        productModel
        vendorCode
        pointCode
        pointName
      }
      spaceShieldScopes {
        guid
        type
        floorTag
        floorName
        roomTag
        roomName
        roomType
        deviceName
        deviceOperationStatus
        productModel
        vendorCode
        pointCode
        pointName
      }
    }
  }
`;

export function useAlarmShield(
  options?: QueryHookOptions<AlarmShieldData, QueryAlarmShieldArgs>
): QueryResult<AlarmShieldData, QueryAlarmShieldArgs> {
  return useQuery(GET_ALARM_SHIELDS_DATA, {
    fetchPolicy: 'network-only',
    ...options,
  });
}

export function useLazyAlarmShield(
  options?: LazyQueryHookOptions<AlarmShieldData, QueryAlarmShieldArgs>
): LazyQueryResultTuple<AlarmShieldData, QueryAlarmShieldArgs> {
  return useLazyQuery(GET_ALARM_SHIELDS_DATA, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
