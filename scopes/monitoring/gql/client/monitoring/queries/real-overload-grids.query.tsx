import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type {
  Maybe,
  QueryRealOverloadGridsArgs,
  RealOverloadGridsResponse,
} from '../generated-types/graphql';

export type RealOverloadGridsData = {
  realOverloadGrids: Maybe<RealOverloadGridsResponse>;
};

export const GET_OVERLOAD_GRIDS: DocumentNode = gql`
  query GetRealOverloadGrids(
    $idcTag: String!
    $blockTag: String!
    $roomTag: String!
    $pointCode: String!
    $deviceTypeList: [String!]
  ) {
    realOverloadGrids(
      idcTag: $idcTag
      blockTag: $blockTag
      roomTag: $roomTag
      pointCode: $pointCode
      deviceTypeList: $deviceTypeList
    ) {
      data {
        gridGuid
        idcTag
        blockTag
        roomTag
        gridTag
        columnTag
        ratedPower
        signedPower
        gridType {
          code
          name
        }
        unitCount
        customerId
        customerName
        avgPowerWeek
        avgPowerMonth
        relateDeviceList {
          idcTag
          blockTag
          roomTag
          deviceTag
          deviceLabel
          deviceGuid
          assetNo
          deviceName
          deviceType
          extendPosition
          powerLine
        }
      }
      total
    }
  }
`;

export function useRealOverloadGrids(
  options?: QueryHookOptions<RealOverloadGridsData, QueryRealOverloadGridsArgs>
): QueryResult<RealOverloadGridsData, QueryRealOverloadGridsArgs> {
  return useQuery(GET_OVERLOAD_GRIDS, {
    fetchPolicy: 'network-only',
    ...options,
  });
}

export function useLazyRealOverloadGrids(
  options?: LazyQueryHookOptions<RealOverloadGridsData, QueryRealOverloadGridsArgs>
): LazyQueryResultTuple<RealOverloadGridsData, QueryRealOverloadGridsArgs> {
  return useLazyQuery(GET_OVERLOAD_GRIDS, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
