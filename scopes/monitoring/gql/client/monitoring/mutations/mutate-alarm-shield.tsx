import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  CreateAlarmShieldResponse,
  DeleteAlarmShieldResponse,
  FinishAlarmShieldResponse,
  Maybe,
  MutationCreateAlarmShieldArgs,
  MutationDeleteAlarmShieldArgs,
  MutationFinishAlarmShieldArgs,
  MutationUpdateAlarmShieldArgs,
  UpdateAlarmShieldResponse,
} from '../generated-types/graphql';

export type CreateAlarmShieldData = {
  createAlarmShield: Maybe<CreateAlarmShieldResponse>;
};

export type UpdateAlarmShieldData = {
  updateAlarmShield: Maybe<UpdateAlarmShieldResponse>;
};

export type DeleteAlarmShieldData = {
  deleteAlarmShield: Maybe<DeleteAlarmShieldResponse>;
};

export type FinishAlarmShieldData = {
  finishAlarmShield: Maybe<FinishAlarmShieldResponse>;
};

export const CreateAlarmShield: DocumentNode = gql`
  mutation CreateAlarmShield($params: CreateAlarmShieldParams!) {
    createAlarmShield(params: $params) {
      code
      message
      success
    }
  }
`;

export const UpdateAlarmShield: DocumentNode = gql`
  mutation UpdateAlarmShield($params: UpdateAlarmShieldParams!) {
    updateAlarmShield(params: $params) {
      code
      message
      success
    }
  }
`;

export const DeleteAlarmShield: DocumentNode = gql`
  mutation DeleteAlarmShield($ids: [Long!]!) {
    deleteAlarmShield(ids: $ids) {
      code
      message
      success
    }
  }
`;

export const FinishAlarmShield: DocumentNode = gql`
  mutation FinishAlarmShield($ids: [Long!]!) {
    finishAlarmShield(ids: $ids) {
      code
      message
      success
    }
  }
`;

export function useCreateAlarmShield(
  options?: MutationHookOptions<CreateAlarmShieldData, MutationCreateAlarmShieldArgs>
): MutationTuple<CreateAlarmShieldData, MutationCreateAlarmShieldArgs> {
  return useMutation(CreateAlarmShield, {
    fetchPolicy: 'network-only',
    ...options,
  });
}

export function useUpdateAlarmShield(
  options?: MutationHookOptions<UpdateAlarmShieldData, MutationUpdateAlarmShieldArgs>
): MutationTuple<UpdateAlarmShieldData, MutationUpdateAlarmShieldArgs> {
  return useMutation(UpdateAlarmShield, {
    fetchPolicy: 'network-only',
    ...options,
  });
}

export function useDeleteAlarmShield(
  options?: MutationHookOptions<DeleteAlarmShieldData, MutationDeleteAlarmShieldArgs>
): MutationTuple<DeleteAlarmShieldData, MutationDeleteAlarmShieldArgs> {
  return useMutation(DeleteAlarmShield, {
    fetchPolicy: 'network-only',
    ...options,
  });
}

export function useFinishAlarmShield(
  options?: MutationHookOptions<FinishAlarmShieldData, MutationFinishAlarmShieldArgs>
): MutationTuple<FinishAlarmShieldData, MutationFinishAlarmShieldArgs> {
  return useMutation(FinishAlarmShield, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
