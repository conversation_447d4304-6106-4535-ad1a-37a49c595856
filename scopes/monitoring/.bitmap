/* THIS IS A BIT-AUTO-GENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY. */

/**
 * The Bitmap file is an auto generated file used by Bit to track all your Bit components. It maps the component to a folder in your file system.
 * This file should be committed to VCS(version control).
 * Components are listed using their component ID (https://bit.dev/reference/components/component-id).
 * If you want to delete components you can use the "bit remove <component-id>" command.
 * See the docs (https://bit.dev/reference/components/removing-components) for more information, or use "bit remove --help".
 */

{
    "cache/preferences": {
        "name": "cache/preferences",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "cache/preferences"
    },
    "chart/common-gauge": {
        "name": "chart/common-gauge",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "chart/common-gauge"
    },
    "chart/duration-select": {
        "name": "chart/duration-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "chart/duration-select"
    },
    "chart/hook/use-mark-lines": {
        "name": "chart/hook/use-mark-lines",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "chart/hook/use-mark-lines"
    },
    "chart/points-line": {
        "name": "chart/points-line",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "chart/points-line"
    },
    "chart/points-line-export": {
        "name": "chart/points-line-export",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "chart/points-line-export"
    },
    "chart/points-state-line": {
        "name": "chart/points-state-line",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "chart/points-state-line"
    },
    "chart/pue-pie": {
        "name": "chart/pue-pie",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "chart/pue-pie"
    },
    "chart/simple-line": {
        "name": "chart/simple-line",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "chart/simple-line"
    },
    "chart/wue-gauge": {
        "name": "chart/wue-gauge",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "chart/wue-gauge"
    },
    "datav/ai-ops-line": {
        "name": "datav/ai-ops-line",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "datav/ai-ops-line",
        "config": {
            "teambit.react/react": {},
            "teambit.envs/envs": {
                "env": "teambit.react/react"
            }
        }
    },
    "datav/animated-pie": {
        "name": "datav/animated-pie",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "datav/animated-pie",
        "config": {
            "teambit.react/react": {},
            "teambit.envs/envs": {
                "env": "teambit.react/react"
            }
        }
    },
    "datav/block-timeline": {
        "name": "datav/block-timeline",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "datav/block-timeline",
        "config": {
            "teambit.react/react": {},
            "teambit.envs/envs": {
                "env": "teambit.react/react"
            }
        }
    },
    "datav/complex-circle-gauge": {
        "name": "datav/complex-circle-gauge",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "datav/complex-circle-gauge",
        "config": {
            "teambit.react/react": {},
            "teambit.envs/envs": {
                "env": "teambit.react/react"
            }
        }
    },
    "datav/dashboard-statistic": {
        "name": "datav/dashboard-statistic",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "datav/dashboard-statistic",
        "config": {
            "teambit.react/react": {},
            "teambit.envs/envs": {
                "env": "teambit.react/react"
            }
        }
    },
    "datav/efficiency-statistic": {
        "name": "datav/efficiency-statistic",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "datav/efficiency-statistic",
        "config": {
            "teambit.react/react": {},
            "teambit.envs/envs": {
                "env": "teambit.react/react"
            }
        }
    },
    "datav/realtime-point-gauge": {
        "name": "datav/realtime-point-gauge",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "datav/realtime-point-gauge",
        "config": {
            "teambit.react/react": {},
            "teambit.envs/envs": {
                "env": "teambit.react/react"
            }
        }
    },
    "datav/simple-gauge": {
        "name": "datav/simple-gauge",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "datav/simple-gauge",
        "config": {
            "teambit.react/react": {},
            "teambit.envs/envs": {
                "env": "teambit.react/react"
            }
        }
    },
    "datav/simple-point-line": {
        "name": "datav/simple-point-line",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "datav/simple-point-line",
        "config": {
            "teambit.react/react": {},
            "teambit.envs/envs": {
                "env": "teambit.react/react"
            }
        }
    },
    "gql/client/monitoring": {
        "name": "gql/client/monitoring",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "gql/client/monitoring",
        "config": {
            "teammc.snowcone/gql-react-env@2.0.13": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/gql-react-env"
            }
        }
    },
    "hook/use-alarm-configuration-template": {
        "name": "hook/use-alarm-configuration-template",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "hook/use-alarm-configuration-template"
    },
    "hook/use-cue-data": {
        "name": "hook/use-cue-data",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "hook/use-cue-data"
    },
    "hook/use-merged-point-guids": {
        "name": "hook/use-merged-point-guids",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "hook/use-merged-point-guids"
    },
    "hook/use-point-peak-or-valley": {
        "name": "hook/use-point-peak-or-valley",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "hook/use-point-peak-or-valley"
    },
    "hook/use-point-value-tops": {
        "name": "hook/use-point-value-tops",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "hook/use-point-value-tops"
    },
    "hook/use-points-data": {
        "name": "hook/use-points-data",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "hook/use-points-data"
    },
    "hook/use-points-yesterday-avg": {
        "name": "hook/use-points-yesterday-avg",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "hook/use-points-yesterday-avg"
    },
    "hook/use-real-time-weather": {
        "name": "hook/use-real-time-weather",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "hook/use-real-time-weather"
    },
    "hook/use-route-space-params": {
        "name": "hook/use-route-space-params",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "hook/use-route-space-params"
    },
    "hook/use-rule-type": {
        "name": "hook/use-rule-type",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "hook/use-rule-type"
    },
    "hook/use-wue-data": {
        "name": "hook/use-wue-data",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "hook/use-wue-data"
    },
    "model/ai-ops-alarm": {
        "name": "model/ai-ops-alarm",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "model/ai-ops-alarm",
        "config": {
            "teammc.snowcone/node-env@2.2.9": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-env"
            }
        }
    },
    "model/alarm": {
        "name": "model/alarm",
        "scope": "manyun.monitoring",
        "version": "fca200833de60658cfa3b2fa0a2f9102e5218c48",
        "mainFile": "index.ts",
        "rootDir": "model/alarm",
        "config": {
            "teammc.snowcone/node-env@2.2.9": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-env"
            }
        }
    },
    "model/alarm-template": {
        "name": "model/alarm-template",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "model/alarm-template",
        "config": {
            "teammc.snowcone/node-env@2.2.9": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-env"
            }
        }
    },
    "model/channel-config": {
        "name": "model/channel-config",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "model/channel-config",
        "config": {
            "teammc.snowcone/node-env@2.2.9": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-env"
            }
        }
    },
    "model/dynamic-baseline-setting": {
        "name": "model/dynamic-baseline-setting",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "model/dynamic-baseline-setting",
        "config": {
            "teammc.snowcone/node-env@2.2.9": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-env"
            }
        }
    },
    "model/influence": {
        "name": "model/influence",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "model/influence",
        "config": {
            "teammc.snowcone/node-env@2.2.9": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-env"
            }
        }
    },
    "model/monitoring-data": {
        "name": "model/monitoring-data",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "model/monitoring-data",
        "config": {
            "teammc.snowcone/node-env@2.2.9": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-env"
            }
        }
    },
    "model/monitoring-item": {
        "name": "model/monitoring-item",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "model/monitoring-item",
        "config": {
            "teammc.snowcone/node-env@2.2.9": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-env"
            }
        }
    },
    "model/notification-configure-item": {
        "name": "model/notification-configure-item",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "model/notification-configure-item",
        "config": {
            "teammc.snowcone/node-env@2.2.9": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-env"
            }
        }
    },
    "model/notification-configure-item-object": {
        "name": "model/notification-configure-item-object",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "model/notification-configure-item-object",
        "config": {
            "teammc.snowcone/node-env@2.2.9": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-env"
            }
        }
    },
    "model/point": {
        "name": "model/point",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "model/point",
        "config": {
            "teammc.snowcone/node-env@2.2.9": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-env"
            }
        }
    },
    "model/professional-rule": {
        "name": "model/professional-rule",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "model/professional-rule",
        "config": {
            "teammc.snowcone/node-env@2.2.9": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-env"
            }
        }
    },
    "model/professional-rule-template": {
        "name": "model/professional-rule-template",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "model/professional-rule-template",
        "config": {
            "teammc.snowcone/node-env@2.2.9": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-env"
            }
        }
    },
    "model/trigger-rule": {
        "name": "model/trigger-rule",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "model/trigger-rule",
        "config": {
            "teammc.snowcone/node-env@2.2.9": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-env"
            }
        }
    },
    "page/ai-ops-alarms": {
        "name": "page/ai-ops-alarms",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "page/ai-ops-alarms",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/ai-ops-statistics": {
        "name": "page/ai-ops-statistics",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "page/ai-ops-statistics",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/alarm-configuration-mutator": {
        "name": "page/alarm-configuration-mutator",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "page/alarm-configuration-mutator",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/alarm-notice-configuration": {
        "name": "page/alarm-notice-configuration",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "page/alarm-notice-configuration",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/alarm-notice-configuration-mutator": {
        "name": "page/alarm-notice-configuration-mutator",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "page/alarm-notice-configuration-mutator",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/alarm-notice-configurations": {
        "name": "page/alarm-notice-configurations",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "page/alarm-notice-configurations",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/alarm-shield": {
        "name": "page/alarm-shield",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "page/alarm-shield",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/alarm-shield-list": {
        "name": "page/alarm-shield-list",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "page/alarm-shield-list",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/alarm-shield-mutator": {
        "name": "page/alarm-shield-mutator",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "page/alarm-shield-mutator",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/alarm-staring-screen": {
        "name": "page/alarm-staring-screen",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "page/alarm-staring-screen",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/battery-pack-view": {
        "name": "page/battery-pack-view",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "page/battery-pack-view",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/channel-config-create": {
        "name": "page/channel-config-create",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "page/channel-config-create",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/channel-config-detail": {
        "name": "page/channel-config-detail",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "page/channel-config-detail",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/channel-config-import": {
        "name": "page/channel-config-import",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "page/channel-config-import",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/channel-config-list": {
        "name": "page/channel-config-list",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "page/channel-config-list",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/dynamic-baseline-setting-mutator": {
        "name": "page/dynamic-baseline-setting-mutator",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "page/dynamic-baseline-setting-mutator",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/dynamic-baseline-settings": {
        "name": "page/dynamic-baseline-settings",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "page/dynamic-baseline-settings",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/liquid-cooling": {
        "name": "page/liquid-cooling",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "page/liquid-cooling",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/local-alarm-notice-configuration": {
        "name": "page/local-alarm-notice-configuration",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "page/local-alarm-notice-configuration",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/local-alarm-notice-configurations": {
        "name": "page/local-alarm-notice-configurations",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "page/local-alarm-notice-configurations",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/local-alarm-notice-mutator": {
        "name": "page/local-alarm-notice-mutator",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "page/local-alarm-notice-mutator",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/local-alarm-rule-import": {
        "name": "page/local-alarm-rule-import",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "page/local-alarm-rule-import",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/local-alarm-rule-list": {
        "name": "page/local-alarm-rule-list",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "page/local-alarm-rule-list",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/non-point-import": {
        "name": "page/non-point-import",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "page/non-point-import",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/non-points": {
        "name": "page/non-points",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "page/non-points",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/points-chart": {
        "name": "page/points-chart",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "page/points-chart",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/professional-rule-editor": {
        "name": "page/professional-rule-editor",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "page/professional-rule-editor",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/professional-rule-list": {
        "name": "page/professional-rule-list",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "page/professional-rule-list",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/professional-rule-template-editor": {
        "name": "page/professional-rule-template-editor",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "page/professional-rule-template-editor",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/professional-rule-template-list": {
        "name": "page/professional-rule-template-list",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "page/professional-rule-template-list",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/room-view": {
        "name": "page/room-view",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "page/room-view",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/topology-graphix": {
        "name": "page/topology-graphix",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "page/topology-graphix",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "route/monitoring-routes": {
        "name": "route/monitoring-routes",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "route/monitoring-routes",
        "config": {
            "teammc.snowcone/react-esm-env@2.0.12": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/react-esm-env"
            }
        }
    },
    "service/accept-alarms": {
        "name": "service/accept-alarms",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/accept-alarms",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/batch-operation-local-alarm-rule": {
        "name": "service/batch-operation-local-alarm-rule",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/batch-operation-local-alarm-rule",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/batch-remove-alarms": {
        "name": "service/batch-remove-alarms",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/batch-remove-alarms",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/create-alarm-configuration": {
        "name": "service/create-alarm-configuration",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/create-alarm-configuration",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/create-alarm-notification-configuration": {
        "name": "service/create-alarm-notification-configuration",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/create-alarm-notification-configuration",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/create-channel-config": {
        "name": "service/create-channel-config",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/create-channel-config",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/create-channel-point": {
        "name": "service/create-channel-point",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/create-channel-point",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/create-local-alarm-notice": {
        "name": "service/create-local-alarm-notice",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/create-local-alarm-notice",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/create-non-point": {
        "name": "service/create-non-point",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/create-non-point",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/create-ops-scene": {
        "name": "service/create-ops-scene",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/create-ops-scene",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/create-professional-rule": {
        "name": "service/create-professional-rule",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/create-professional-rule",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/create-professional-rule-template": {
        "name": "service/create-professional-rule-template",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/create-professional-rule-template",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/delete-alarms-by-click-once": {
        "name": "service/delete-alarms-by-click-once",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/delete-alarms-by-click-once",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/delete-channel-config": {
        "name": "service/delete-channel-config",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/delete-channel-config",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/delete-channel-point": {
        "name": "service/delete-channel-point",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/delete-channel-point",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/delete-local-alarm-notice": {
        "name": "service/delete-local-alarm-notice",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/delete-local-alarm-notice",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/delete-local-alarm-rule": {
        "name": "service/delete-local-alarm-rule",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/delete-local-alarm-rule",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/delete-monitor-scheme": {
        "name": "service/delete-monitor-scheme",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/delete-monitor-scheme",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/delete-non-point": {
        "name": "service/delete-non-point",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/delete-non-point",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/delete-ops-scene": {
        "name": "service/delete-ops-scene",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/delete-ops-scene",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/delete-professional-rule": {
        "name": "service/delete-professional-rule",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/delete-professional-rule",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/delete-professional-rule-template": {
        "name": "service/delete-professional-rule-template",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/delete-professional-rule-template",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/export-alarm-transmission-records": {
        "name": "service/export-alarm-transmission-records",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/export-alarm-transmission-records",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/export-alarms": {
        "name": "service/export-alarms",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/export-alarms",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/export-channel": {
        "name": "service/export-channel",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/export-channel",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/export-channel-device": {
        "name": "service/export-channel-device",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/export-channel-device",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/export-local-alarm-rule": {
        "name": "service/export-local-alarm-rule",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/export-local-alarm-rule",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/export-non-point": {
        "name": "service/export-non-point",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/export-non-point",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/export-overload-grids": {
        "name": "service/export-overload-grids",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/export-overload-grids",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-ai-alarm-info": {
        "name": "service/fetch-ai-alarm-info",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-ai-alarm-info",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-ai-alarms": {
        "name": "service/fetch-ai-alarms",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-ai-alarms",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-ai-monitors": {
        "name": "service/fetch-ai-monitors",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-ai-monitors",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-ai-ops-point-forecast": {
        "name": "service/fetch-ai-ops-point-forecast",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-ai-ops-point-forecast",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-alarm-configuration": {
        "name": "service/fetch-alarm-configuration",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-alarm-configuration",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-alarm-configurations-by-device-type": {
        "name": "service/fetch-alarm-configurations-by-device-type",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-alarm-configurations-by-device-type",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-alarm-idcs": {
        "name": "service/fetch-alarm-idcs",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-alarm-idcs",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-alarm-info": {
        "name": "service/fetch-alarm-info",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-alarm-info",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-alarm-merge": {
        "name": "service/fetch-alarm-merge",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-alarm-merge",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-alarm-notification-connet-regions": {
        "name": "service/fetch-alarm-notification-connet-regions",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-alarm-notification-connet-regions",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-alarm-point": {
        "name": "service/fetch-alarm-point",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-alarm-point",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-alarm-template-by-params": {
        "name": "service/fetch-alarm-template-by-params",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-alarm-template-by-params",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-alarm-template-groups": {
        "name": "service/fetch-alarm-template-groups",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-alarm-template-groups",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-alarms": {
        "name": "service/fetch-alarms",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-alarms",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-alarms-statistics": {
        "name": "service/fetch-alarms-statistics",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-alarms-statistics",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-battery-list": {
        "name": "service/fetch-battery-list",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-battery-list",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-battery-pack-charge-records": {
        "name": "service/fetch-battery-pack-charge-records",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-battery-pack-charge-records",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-block-rank": {
        "name": "service/fetch-block-rank",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-block-rank",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-channel-change-mode": {
        "name": "service/fetch-channel-change-mode",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-channel-change-mode",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-channel-config": {
        "name": "service/fetch-channel-config",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-channel-config",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-channel-config-detail": {
        "name": "service/fetch-channel-config-detail",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-channel-config-detail",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-channel-data-type": {
        "name": "service/fetch-channel-data-type",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-channel-data-type",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-channel-debugging": {
        "name": "service/fetch-channel-debugging",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-channel-debugging",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-channel-device": {
        "name": "service/fetch-channel-device",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-channel-device",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-channel-direct": {
        "name": "service/fetch-channel-direct",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-channel-direct",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-channel-point": {
        "name": "service/fetch-channel-point",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-channel-point",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-channel-protocol-type": {
        "name": "service/fetch-channel-protocol-type",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-channel-protocol-type",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-channel-type": {
        "name": "service/fetch-channel-type",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-channel-type",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-channel-unconfig-point": {
        "name": "service/fetch-channel-unconfig-point",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-channel-unconfig-point",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-chart-data": {
        "name": "service/fetch-chart-data",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-chart-data",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-counts-by-alarm-level": {
        "name": "service/fetch-counts-by-alarm-level",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-counts-by-alarm-level",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-customer-point-agg": {
        "name": "service/fetch-customer-point-agg",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-customer-point-agg",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-device-type-points": {
        "name": "service/fetch-device-type-points",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-device-type-points",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-diff-point-data": {
        "name": "service/fetch-diff-point-data",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-diff-point-data",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-expert-rule": {
        "name": "service/fetch-expert-rule",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-expert-rule",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-grid-used-rate-data": {
        "name": "service/fetch-grid-used-rate-data",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-grid-used-rate-data",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-grid-used-rate-peak-or-valley": {
        "name": "service/fetch-grid-used-rate-peak-or-valley",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-grid-used-rate-peak-or-valley",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-local-alarm-notice": {
        "name": "service/fetch-local-alarm-notice",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-local-alarm-notice",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-local-alarm-notices": {
        "name": "service/fetch-local-alarm-notices",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-local-alarm-notices",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-local-alarm-rule-devices": {
        "name": "service/fetch-local-alarm-rule-devices",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-local-alarm-rule-devices",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-local-alarm-rules": {
        "name": "service/fetch-local-alarm-rules",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-local-alarm-rules",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-monitoring-items-by-alarm-configuration": {
        "name": "service/fetch-monitoring-items-by-alarm-configuration",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-monitoring-items-by-alarm-configuration",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-non-point-product": {
        "name": "service/fetch-non-point-product",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-non-point-product",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-non-points": {
        "name": "service/fetch-non-points",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-non-points",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-operator-type": {
        "name": "service/fetch-operator-type",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-operator-type",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-ops-point": {
        "name": "service/fetch-ops-point",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-ops-point",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-ops-scene": {
        "name": "service/fetch-ops-scene",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-ops-scene",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-ops-scenes-list": {
        "name": "service/fetch-ops-scenes-list",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-ops-scenes-list",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-point-offline-data": {
        "name": "service/fetch-point-offline-data",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-point-offline-data",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-point-peak-or-valley": {
        "name": "service/fetch-point-peak-or-valley",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-point-peak-or-valley",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-point-thresholds": {
        "name": "service/fetch-point-thresholds",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-point-thresholds",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-point-value-tops": {
        "name": "service/fetch-point-value-tops",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-point-value-tops",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-point-values": {
        "name": "service/fetch-point-values",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-point-values",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-point-values-by-guids": {
        "name": "service/fetch-point-values-by-guids",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-point-values-by-guids",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-points-alarm-count": {
        "name": "service/fetch-points-alarm-count",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-points-alarm-count",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-points-data-forecast": {
        "name": "service/fetch-points-data-forecast",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-points-data-forecast",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-professional-rule-detail": {
        "name": "service/fetch-professional-rule-detail",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-professional-rule-detail",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-professional-rule-list": {
        "name": "service/fetch-professional-rule-list",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-professional-rule-list",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-professional-rule-template-detail": {
        "name": "service/fetch-professional-rule-template-detail",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-professional-rule-template-detail",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-professional-rule-template-list": {
        "name": "service/fetch-professional-rule-template-list",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-professional-rule-template-list",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-realtime-cooling-mode": {
        "name": "service/fetch-realtime-cooling-mode",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-realtime-cooling-mode",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-realtime-grid-rate-data": {
        "name": "service/fetch-realtime-grid-rate-data",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-realtime-grid-rate-data",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-realtime-weather-by-block": {
        "name": "service/fetch-realtime-weather-by-block",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-realtime-weather-by-block",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-topology": {
        "name": "service/fetch-topology",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-topology",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-user-alarms-monitoring-board-preferences": {
        "name": "service/fetch-user-alarms-monitoring-board-preferences",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-user-alarms-monitoring-board-preferences",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-video-config": {
        "name": "service/fetch-video-config",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-video-config",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-your-interests-alarm-ids": {
        "name": "service/fetch-your-interests-alarm-ids",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-your-interests-alarm-ids",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/import-channel": {
        "name": "service/import-channel",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/import-channel",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/import-channel-template": {
        "name": "service/import-channel-template",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/import-channel-template",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/import-local-alarm-rule": {
        "name": "service/import-local-alarm-rule",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/import-local-alarm-rule",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/import-local-alarm-rule-template": {
        "name": "service/import-local-alarm-rule-template",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/import-local-alarm-rule-template",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/import-non-point": {
        "name": "service/import-non-point",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/import-non-point",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/mutate-your-interests-alarm-ids": {
        "name": "service/mutate-your-interests-alarm-ids",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/mutate-your-interests-alarm-ids",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/open-n-close-non-point": {
        "name": "service/open-n-close-non-point",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/open-n-close-non-point",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/open-n-close-ops-scene": {
        "name": "service/open-n-close-ops-scene",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/open-n-close-ops-scene",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/reuse-channel-config": {
        "name": "service/reuse-channel-config",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/reuse-channel-config",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/save-user-alarms-monitoring-board-preferences": {
        "name": "service/save-user-alarms-monitoring-board-preferences",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/save-user-alarms-monitoring-board-preferences",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/start-video-live": {
        "name": "service/start-video-live",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/start-video-live",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/stop-video-live": {
        "name": "service/stop-video-live",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/stop-video-live",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/tts-aliyun": {
        "name": "service/tts-aliyun",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/tts-aliyun",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-ai-alarm": {
        "name": "service/update-ai-alarm",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/update-ai-alarm",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-alarm-configuration": {
        "name": "service/update-alarm-configuration",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/update-alarm-configuration",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-alarm-notification-configuration": {
        "name": "service/update-alarm-notification-configuration",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/update-alarm-notification-configuration",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-alarm-notification-connent-regions": {
        "name": "service/update-alarm-notification-connent-regions",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/update-alarm-notification-connent-regions",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-block-cool-mode-configs": {
        "name": "service/update-block-cool-mode-configs",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/update-block-cool-mode-configs",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-channel-config": {
        "name": "service/update-channel-config",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/update-channel-config",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-channel-point": {
        "name": "service/update-channel-point",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/update-channel-point",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-channel-status": {
        "name": "service/update-channel-status",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/update-channel-status",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-local-alarm-notice-status": {
        "name": "service/update-local-alarm-notice-status",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/update-local-alarm-notice-status",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-local-alarm-rule": {
        "name": "service/update-local-alarm-rule",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/update-local-alarm-rule",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-local-alarm-rule-devices": {
        "name": "service/update-local-alarm-rule-devices",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/update-local-alarm-rule-devices",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-non-point": {
        "name": "service/update-non-point",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/update-non-point",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-non-point-map": {
        "name": "service/update-non-point-map",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/update-non-point-map",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-ops-scene": {
        "name": "service/update-ops-scene",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/update-ops-scene",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-professional-rule": {
        "name": "service/update-professional-rule",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/update-professional-rule",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-professional-rule-available": {
        "name": "service/update-professional-rule-available",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/update-professional-rule-available",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-professional-rule-template": {
        "name": "service/update-professional-rule-template",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/update-professional-rule-template",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-video-config": {
        "name": "service/update-video-config",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/update-video-config",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/video-live-heartbeat": {
        "name": "service/video-live-heartbeat",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "service/video-live-heartbeat",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "state/alarm-configuration-template": {
        "name": "state/alarm-configuration-template",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "state/alarm-configuration-template"
    },
    "state/alarm-notification-configure": {
        "name": "state/alarm-notification-configure",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "state/alarm-notification-configure"
    },
    "state/alarms-monitoring-board": {
        "name": "state/alarms-monitoring-board",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "state/alarms-monitoring-board"
    },
    "state/dashboard-idc": {
        "name": "state/dashboard-idc",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "state/dashboard-idc"
    },
    "state/rule-types": {
        "name": "state/rule-types",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "state/rule-types"
    },
    "state/subscriptions": {
        "name": "state/subscriptions",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "state/subscriptions",
        "config": {
            "teambit.envs/envs": {
                "env": "teammc.snowcone/react-esm-env"
            },
            "teammc.snowcone/react-esm-env@2.0.12": {}
        }
    },
    "state/topology": {
        "name": "state/topology",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "state/topology"
    },
    "theme/topology-theme": {
        "name": "theme/topology-theme",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "theme/topology-theme",
        "config": {
            "teambit.harmony/node": {},
            "teambit.envs/envs": {
                "env": "teambit.harmony/node"
            }
        }
    },
    "ui/add-device-to-diff-tool": {
        "name": "ui/add-device-to-diff-tool",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/add-device-to-diff-tool",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/add-point-to-diff-tool": {
        "name": "ui/add-point-to-diff-tool",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/add-point-to-diff-tool",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/alarm-card": {
        "name": "ui/alarm-card",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/alarm-card",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/alarm-drawer": {
        "name": "ui/alarm-drawer",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/alarm-drawer",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/alarm-expect-status-select": {
        "name": "ui/alarm-expect-status-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/alarm-expect-status-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/alarm-label": {
        "name": "ui/alarm-label",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/alarm-label",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/alarm-level-select": {
        "name": "ui/alarm-level-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/alarm-level-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/alarm-level-text": {
        "name": "ui/alarm-level-text",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/alarm-level-text",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/alarm-lifecy-status-text": {
        "name": "ui/alarm-lifecy-status-text",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/alarm-lifecy-status-text",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/alarm-lifecycle-state-select": {
        "name": "ui/alarm-lifecycle-state-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/alarm-lifecycle-state-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/alarm-merge-status-select": {
        "name": "ui/alarm-merge-status-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/alarm-merge-status-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/alarm-monitoring-item-mutator": {
        "name": "ui/alarm-monitoring-item-mutator",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/alarm-monitoring-item-mutator",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/alarm-monitoring-item-state-updator": {
        "name": "ui/alarm-monitoring-item-state-updator",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/alarm-monitoring-item-state-updator",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/alarm-monitoring-item-table": {
        "name": "ui/alarm-monitoring-item-table",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/alarm-monitoring-item-table",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/alarm-monitoring-item-trigger-rule-setting": {
        "name": "ui/alarm-monitoring-item-trigger-rule-setting",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/alarm-monitoring-item-trigger-rule-setting",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/alarm-notice-tpl-cfg": {
        "name": "ui/alarm-notice-tpl-cfg",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/alarm-notice-tpl-cfg",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/alarm-notice-type": {
        "name": "ui/alarm-notice-type",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/alarm-notice-type",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/alarm-reason-text": {
        "name": "ui/alarm-reason-text",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/alarm-reason-text",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/alarm-screen-card": {
        "name": "ui/alarm-screen-card",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/alarm-screen-card",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/alarm-shield-scope-table": {
        "name": "ui/alarm-shield-scope-table",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/alarm-shield-scope-table",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/alarm-shield-status-text": {
        "name": "ui/alarm-shield-status-text",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/alarm-shield-status-text",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/alarm-state-select": {
        "name": "ui/alarm-state-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/alarm-state-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/alarm-status-text": {
        "name": "ui/alarm-status-text",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/alarm-status-text",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/alarm-type": {
        "name": "ui/alarm-type",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/alarm-type",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/alarm-type-text": {
        "name": "ui/alarm-type-text",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/alarm-type-text",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/alarms-filter-form": {
        "name": "ui/alarms-filter-form",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/alarms-filter-form",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/camera-video": {
        "name": "ui/camera-video",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/camera-video",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/channel-change-mode-select": {
        "name": "ui/channel-change-mode-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/channel-change-mode-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/channel-communication-status-text": {
        "name": "ui/channel-communication-status-text",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/channel-communication-status-text",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/channel-data-type-select": {
        "name": "ui/channel-data-type-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/channel-data-type-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/channel-protocol-type-select": {
        "name": "ui/channel-protocol-type-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/channel-protocol-type-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/channel-status-select": {
        "name": "ui/channel-status-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/channel-status-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/channel-status-text": {
        "name": "ui/channel-status-text",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/channel-status-text",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/channel-type-select": {
        "name": "ui/channel-type-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/channel-type-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/channels-table": {
        "name": "ui/channels-table",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/channels-table",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/device-diff-tool": {
        "name": "ui/device-diff-tool",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/device-diff-tool",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/dynamic-baseline-condition-select": {
        "name": "ui/dynamic-baseline-condition-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/dynamic-baseline-condition-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/feekback-results-select": {
        "name": "ui/feekback-results-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/feekback-results-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/feekback-results-text": {
        "name": "ui/feekback-results-text",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/feekback-results-text",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/filter-alarms": {
        "name": "ui/filter-alarms",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/filter-alarms",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/graph-point-line-modal": {
        "name": "ui/graph-point-line-modal",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/graph-point-line-modal",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/host-idc-select": {
        "name": "ui/host-idc-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/host-idc-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/local-alarm-notice-object-table": {
        "name": "ui/local-alarm-notice-object-table",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/local-alarm-notice-object-table",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/local-alarm-rule-origin-select": {
        "name": "ui/local-alarm-rule-origin-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/local-alarm-rule-origin-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/local-alarm-rule-status-select": {
        "name": "ui/local-alarm-rule-status-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/local-alarm-rule-status-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/monitoring-status-select": {
        "name": "ui/monitoring-status-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/monitoring-status-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/operator-type-select": {
        "name": "ui/operator-type-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/operator-type-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/ops-scene-notify-way-select": {
        "name": "ui/ops-scene-notify-way-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/ops-scene-notify-way-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/point-capture-status-select": {
        "name": "ui/point-capture-status-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/point-capture-status-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/point-category-select": {
        "name": "ui/point-category-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/point-category-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/point-data-renderer": {
        "name": "ui/point-data-renderer",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/point-data-renderer",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/point-diff-tool": {
        "name": "ui/point-diff-tool",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/point-diff-tool",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/point-monitor-item-modal-view": {
        "name": "ui/point-monitor-item-modal-view",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/point-monitor-item-modal-view",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/professional-rule-template-select": {
        "name": "ui/professional-rule-template-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/professional-rule-template-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/redirect-to-ecc": {
        "name": "ui/redirect-to-ecc",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/redirect-to-ecc",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/rule-type-cascader": {
        "name": "ui/rule-type-cascader",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/rule-type-cascader",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/rule-type-text": {
        "name": "ui/rule-type-text",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/rule-type-text",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/ticket-card": {
        "name": "ui/ticket-card",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/ticket-card",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/trigger-rules-text": {
        "name": "ui/trigger-rules-text",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "ui/trigger-rules-text",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "util/alarms-tts-manager": {
        "name": "util/alarms-tts-manager",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "util/alarms-tts-manager",
        "config": {
            "teammc.snowcone/node-env@2.2.9": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-env"
            }
        }
    },
    "util/get-electricity-unit": {
        "name": "util/get-electricity-unit",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "util/get-electricity-unit",
        "config": {
            "teammc.snowcone/node-env@2.2.9": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-env"
            }
        }
    },
    "util/get-monitoring-data": {
        "name": "util/get-monitoring-data",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "util/get-monitoring-data",
        "config": {
            "teammc.snowcone/node-env@2.2.9": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-env"
            }
        }
    },
    "util/get-weather-icon": {
        "name": "util/get-weather-icon",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "util/get-weather-icon",
        "config": {
            "teammc.snowcone/node-env@2.2.9": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-env"
            }
        }
    },
    "util/tts-service": {
        "name": "util/tts-service",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.monitoring",
        "mainFile": "index.ts",
        "rootDir": "util/tts-service",
        "config": {
            "teammc.snowcone/node-env@2.2.9": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-env"
            }
        }
    },
    "$schema-version": "17.0.0"
}