import { utilities } from '@manyun/dc-brain.navigation.link';
import type { Tab<PERSON>ey as ChannelConfigDetailTabKey } from '@manyun/monitoring.page.channel-config-detail';
import { DEVICE_RECORD_ROUTE_PATH } from '@manyun/resource-hub.route.resource-routes';
import { getSpaceGuid, getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

const { generatePath } = utilities;

/** 区域监控、单设备视图 */
export type OtherInfosTab =
  | 'file'
  | 'alarm'
  | 'event'
  | 'change'
  | 'maintenance'
  | 'inspection'
  | 'repair';

//dc-brain-web mf中使用 跳转ecc首页
export const generateMonitoringDataViewIdcRoutePath = ({ idc }: { idc: string }) => {
  // @ts-ignore ts error
  return `${window.apps.monitoring.replace('[idc]', idc)}workspace`;
};

export const generateRoomMonitoringUrl = (params: {
  idc: string;
  block: string;
  room: string;
  /** 需要开启聚光灯效果的设备 GUID */
  spotlightTarget?: string;
}) => {
  //const path = generatePath(ROOM_MONITORING, params);

  let search = '';
  if (params.spotlightTarget) {
    search += `?spotlightTarget=${params.spotlightTarget}`;
  }
  const _guid = getSpaceGuid(params.idc, params.block, params.room)!;
  const guid = encodeURIComponent(_guid.replace(/#/g, '_hashtag_'));
  const path = `redirect-loading?guid=${guid}&search=${search}&type=room`;

  // @ts-ignore ts error
  return `${window.apps.monitoring.replace('[idc]', params.idc)}${path}`;
};

export const generateSpaceOrDeviceRoutePath = ({
  guid,
  type = 'DEVICE',
  spotlightTarget,
}: {
  guid: string;
  type?: 'DEVICE' | 'IDC' | 'BLOCK' | 'ROOM' | 'GRID' | 'COLUMN';
  spotlightTarget?: string;
}) => {
  const { idc, block, room } = getSpaceGuidMap(guid);

  if (type === 'DEVICE') {
    return DEVICE_RECORD_ROUTE_PATH.replace(':guid', guid) || '';
  }
  if (type === 'ROOM') {
    //此处现在跳到ecc
    return generateRoomMonitoringUrl({
      idc: idc!,
      block: block!,
      room: room!,
      spotlightTarget,
    });
  }
  if (type === 'IDC') {
    const { idc } = getSpaceGuidMap(guid);
    // @ts-ignore ts error
    return `${window.apps.monitoring.replace('[idc]', idc)}workspace`;
  }
  if (type === 'BLOCK') {
    // @ts-ignore ts error
    return `${window.apps.monitoring.replace('[idc]', idc)}workspace/${block}`;
  }
  if (type === 'GRID' || type === 'COLUMN') {
    const _type = type === 'COLUMN' ? 'column' : 'grid';
    const path = `redirect-loading?guid=${window.encodeURIComponent(guid)}&type=${_type}`;
    // @ts-ignore ts error
    return `${window.apps.monitoring.replace('[idc]', idc)}${path}`;
  }
  return '/';
};

// 监控项配置列表
export const MONITOR_ITEM_CONFIG_LIST = '/page/monitor-item-config/list';
// 某个 ID 的监控项配置详情
export const SPECIFIC_MONITOR_ITEM_CONFIG = '/page/monitor-item-config/:configId';

// 预览某个 ID 的（电力/暖通）视图
export const PREVIEW_SPECIFIC_TOPOLOGY = '/page/boundary-graph-share/:boundary/:idc/:block';

/** 新建电力/暖通拓扑 V2 */
export const TOPOLOGY_GRAPHIX = '/page/graphix/topology/:topologyType/:idc/:block/:mode';
export type TopologyGraphixRouteParams = {
  topologyType: string;
  idc: string;
  block: string;
  mode: 'new' | 'edit';
};
export const generateTopologyGraphixRoutePath = (params: TopologyGraphixRouteParams) =>
  generatePath(TOPOLOGY_GRAPHIX, params);

// 模板组列表
export const TEMPLATE_GROUP_VIEW_LIST = '/page/template-group-view/list';
// 新建模板组
export const CREATE_TEMPLATE_GROUP_CONFIG = '/page/template-group-view/create';
// 编辑模板组配置
export const EDIT_TEMPLATE_GROUP_CONFIG = '/page/template-group-view/:id/edit';
// 生效域配置
export const TEMPLATE_AREA_CONFIG = '/page/template-group-view/area-config';
// 查看模板组配置
export const VIEW_TEMPLATE_GROUP_CONFIG = '/page/template-group-view/:id/view';
// 告警盯屏列表
export const ALARM_SCREEN_LIST = '/page/alarm-screen/:idc';

// 告警模板编辑页面
export const ALARM_CONFIGURATION_TEMPLATE_EDIT =
  '/page/monitoring/alarm-configuration-template/:id/edit';
// 告警模板详情页面
export const ALARM_CONFIGURATION_TEMPLATE_DETAIL =
  '/page/monitoring/alarm-configuration-template/:id/detail';
// 告警模板列表
export const ALARM_CONFIGURATION_TEMPLATE_LIST =
  '/page/monitoring/alarm-configuration-template/list';
// 告警模板新建
export const ALARM_CONFIGURATION_TEMPLATE_NEW = '/page/monitoring/alarm-configuration-template/new';
// 告警详情
export const ALARM_SCREEN_DETAIL = '/page/alarm-screen/detail/:id';

// 机房工作台
export const MONITORING_IDC_ROUTE_PATH = '/page/monitoring/:idc';
// @FIXME 这里目前用的是 机房工作台 的菜单权限，应使用页面权限
export const MONITORING_IDC_ROUTE_AUTH_CODE = 'idc_workbench';
export type MonitoringIdcRouteParams = {
  idc: string;
};

// 收敛配置列表
export const MERGED_MONITOR_CONFIG_LIST = '/page/merged-monitor-config/list';
// 新建收敛配置
export const NEW_MERGED_MONITOR_CONFIG = '/page/merged-monitor-config/new';
export type SpecificMergedMonitorConfigParams = {
  id: string;
};
// 查看某个收敛配置
export const SPECIFIC_MERGED_MONITOR_CONFIG = '/page/merged-monitor-config/:id';
export const generateSpecificMergedMonitorConfig = (params: SpecificMergedMonitorConfigParams) =>
  generatePath(SPECIFIC_MERGED_MONITOR_CONFIG, params);

// 编辑某个收敛配置
export const EDIT_SPECIFIC_MERGED_MONITOR_CONFIG = '/page/merged-monitor-config/:id/edit';
export const MERGES_PROCESSED_POINT_CONFIG = '/page/agg-n-cal-point/new';
export const MERGES_PROCESSED_POINT_DETAIL = '/page/agg-n-cal-point';
export const MERGES_PROCESSED_POINT_EDIT = '/page/agg-n-cal-point/:deviceType/:pointCode/edit';

export const generateMergesProcessedPointDetailPath = (params: {
  tab?: 'device' | 'space' | 'custom';
}) => {
  let search = '';
  if (params.tab) {
    search += `?tab=${params.tab}`;
  }

  return `${MERGES_PROCESSED_POINT_DETAIL}${search}`;
};

// 电池组
export const BATTERY_PACK = '/page/battery-pack/:idc';
// 生效域
export const AREA_CONNECT_TEMPLATE = '/page/area-connect-template';
// iPad端测点曲线图
export const POINTS_CHART = '/page/points-chart';

/**告警通报配置列表页 */
export const ALARM_NOTICE_CONFIGURATIONS_ROUTE_PATH =
  '/page/monitoring/alarm-notice-configurations';
export const ALARM_NOTICE_CONFIGURATIONS_ROUTE_AUTH_CODE = 'page_alarm-notice-configurations';

/**告警通报配置详情页*/
export const ALARM_NOTICE_CONFIGURATION_ROUTE_PATH =
  '/page/monitoring/alarm-notice-configurations/:id';
export const ALARM_NOTICE_CONFIGURATION_ROUTE_AUTH_CODE = 'page_alarm-notice-configuration-detail';

/** 告警通报配置新建、编辑页*/
export const MUTATOR_ALARM_NOTICE_CONFIGURATION_ROUTE_PATH =
  '/page/monitoring/alarm-notice-configuration/:type/:id';
/*通报新建编辑复制*/
export type AlarmNoticeConfigurationParams = {
  id: string;
  type: 'edit' | 'copy' | 'create';
};
export const generateAlarmNoticeConfigurationEditRoutePath = ({
  id,
  type,
}: AlarmNoticeConfigurationParams) =>
  generatePath(MUTATOR_ALARM_NOTICE_CONFIGURATION_ROUTE_PATH, { id, type });
/*通报详情*/
export const generateAlarmNoticeConfigurationRoutePath = ({ id }: { id: string }) =>
  generatePath(ALARM_NOTICE_CONFIGURATION_ROUTE_PATH, { id });

/**属地告警通报配置列表页 */
export const LOCAL_ALARM_NOTICE_CONFIGURATIONS_ROUTE_PATH =
  '/page/monitoring/local-alarm-notice-configurations';
export const LOCAL_ALARM_NOTICE_CONFIGURATIONS_ROUTE_AUTH_CODE =
  'page_local-alarm-notice-configurations';

/**属地告警通报配置详情页*/
export const LOCAL_ALARM_NOTICE_CONFIGURATION_ROUTE_PATH =
  '/page/monitoring/local-alarm-notice-configuration/detail/:id';

export const LOCAL_ALARM_NOTICE_CONFIGURATION_ROUTE_AUTH_CODE =
  'page_local-alarm-notice-configuration_detail';

/** 属地告警通报配置新建*/
export const LOCAL_CREATE_ALARM_NOTICE_CONFIGURATION_ROUTE_PATH =
  '/page/monitoring/local-alarm-notice-configuration/create';

export const LOCAL_CREATE_ALARM_NOTICE_CONFIGURATION_ROUTE_PATH_AUTH_CODE =
  'page_local-alarm-notice-configuration_create';

/** 属地告警通报配置编辑 复制*/
export const LOCAL_EDIT_ALARM_NOTICE_CONFIGURATION_ROUTE_PATH =
  '/page/monitoring/local-alarm-notice-configuration/:type/:id';
export const LOCAL_EDIT_ALARM_NOTICE_CONFIGURATION_ROUTE_PATH_AUTH_CODE =
  'page_local-alarm-notice-configuration_edit';

export type LocalAlarmNoticeConfigurationParams = {
  id: string;
  type: 'edit' | 'copy';
};
/*属地通报编辑复制*/
export const generateLocalAlarmNoticeConfigurationEditRoutePath = ({
  id,
  type,
}: LocalAlarmNoticeConfigurationParams) =>
  generatePath(LOCAL_EDIT_ALARM_NOTICE_CONFIGURATION_ROUTE_PATH, { id, type });
/*属地通报详情*/
export const generateLocalAlarmNoticeConfigurationRoutePath = ({ id }: { id: string }) =>
  generatePath(LOCAL_ALARM_NOTICE_CONFIGURATION_ROUTE_PATH, { id });

/** ai-ops 动态基线配置列表 */
export const DYNAMIC_BASELINE_SETTING_ROUTE_PATH = '/page/monitoring/dynamic-baseline-list';
export const DYNAMIC_BASELINE_SETTING_ROUTE_AUTH_CODE = 'page_dynamic-baseline-setting';

/** ai-ops 动态基线配置新增 */
export const DYNAMIC_BASELINE_SETTING_CREATE_ROUTE_PATH =
  '/page/monitoring/dynamic-baseline-setting/create';
/** ai-ops 动态基线配置编辑|复制 */
export const DYNAMIC_BASELINE_SETTING_EDIT_ROUTE_PATH =
  '/page/monitoring/dynamic-baseline-setting/:id/:type';
export const DYNAMIC_BASELINE_SETTING_EDIT_ROUTE_AUTH_CODE = 'page_dynamic-baseline-setting-edit';

export type MutateDynamicBaselineParams = {
  id: string;
  type: 'edit' | 'copy';
};
/**
 * 动态基线配置 | 编辑复制 URL
 * @param {string} type
 * @param {string} id
 * @returns
 */

export const generateDynamicBaselineSettingEditRoutePath = ({
  id,
  type,
}: MutateDynamicBaselineParams) =>
  generatePath(DYNAMIC_BASELINE_SETTING_EDIT_ROUTE_PATH, { id, type });

/**
 * 告警模板编辑页面 URL
 * @param {object} variables
 * @param {string} variables.id 告警id
 */
export type AlarmConfigurationTemplateEditParams = {
  id: string;
};
export const generateAlarmConfigurationTemplateEditUrl = ({
  id,
}: AlarmConfigurationTemplateEditParams) => ALARM_CONFIGURATION_TEMPLATE_EDIT.replace(':id', id);

/**
 * 告警模板详情页面 URL
 * @param {object} variables
 * @param {string} variables.id 告警id
 */
export const generateAlarmConfigurationTemplateDetailUrl = ({
  id,
}: AlarmConfigurationTemplateEditParams) => ALARM_CONFIGURATION_TEMPLATE_DETAIL.replace(':id', id);

export const MONITORING_ROUTER_PATH_AUTH_CODE_MAPPER: Record<string, string> = {
  [ALARM_NOTICE_CONFIGURATIONS_ROUTE_PATH]: ALARM_NOTICE_CONFIGURATIONS_ROUTE_AUTH_CODE,
  [ALARM_NOTICE_CONFIGURATION_ROUTE_PATH]: ALARM_NOTICE_CONFIGURATION_ROUTE_AUTH_CODE,
};

export const generateSpecificMonitorItemConfigLocation = ({
  configId,
  deviceType,
  pointCode,
}: {
  configId: string;
  deviceType: string;
  pointCode: string;
}) => {
  let search = '?';
  if (deviceType) {
    search += `deviceType=${deviceType}&`;
  }
  if (pointCode) {
    search += `pointCode=${pointCode}`;
  }
  return {
    pathname: SPECIFIC_MONITOR_ITEM_CONFIG.replace(':configId', configId),
    search,
  };
};

// 专家规则配置
// 规则列表
export const PROFESSIONAL_RULE_LIST_ROUTE_PATH =
  '/page/monitoring/professional-rule-configuration/rule-list';
export const PROFESSIONAL_RULE_LIST_ROUTE_AUTH_CODE = 'page_professional-rule-list';
export type ProfessionalRuleListRouteParams = {
  templateId: number;
};
export const generateProfessionalRuleListRoutePath = ({
  templateId,
}: ProfessionalRuleListRouteParams) =>
  `${PROFESSIONAL_RULE_LIST_ROUTE_PATH}?templateId=${templateId}`;

export const NEW_PROFESSIONAL_RULE_ROUTE_PATH =
  '/page/monitoring/professional-rule-configuration/rule-list/new';
export const NEW_PROFESSIONAL_RULE_ROUTE_AUTH_CODE = 'page_new-professional-rule';
export type NewProfessionalRuleRouteParams = {
  templateId?: number;
  id?: number;
};
export const generateNewProfessionalRuleRoutePath = (params?: NewProfessionalRuleRouteParams) => {
  if (!params) {
    return NEW_PROFESSIONAL_RULE_ROUTE_PATH;
  }
  if (params.id) {
    return `${NEW_PROFESSIONAL_RULE_ROUTE_PATH}?id=${params.id}`;
  }
  if (params.templateId) {
    return `${NEW_PROFESSIONAL_RULE_ROUTE_PATH}?templateId=${params.templateId}`;
  }
  return NEW_PROFESSIONAL_RULE_ROUTE_PATH;
};

export const EDIT_PROFESSIONAL_RULE_ROUTE_PATH =
  '/page/monitoring/professional-rule-configuration/rule-list/:id/edit';
export const EDIT_PROFESSIONAL_RULE_ROUTE_AUTH_CODE = 'page_edit-professional-rule';
export type EditProfessionalRuleRouteParams = {
  id: number;
};
export const generateEditProfessionalRuleRoutePath = (params: EditProfessionalRuleRouteParams) =>
  generatePath(EDIT_PROFESSIONAL_RULE_ROUTE_PATH, params);

// 规则模版
export const PROFESSIONAL_RULE_TEMPLATE_LIST_ROUTE_PATH =
  '/page/monitoring/professional-rule-configuration/rule-template-list';
export const PROFESSIONAL_RULE_TEMPLATE_LIST_ROUTE_AUTH_CODE =
  'page_professional-rule-template-list';
export const NEW_PROFESSIONAL_RULE_TEMPLATE_ROUTE_PATH =
  '/page/monitoring/professional-rule-configuration/rule-template-list/new';
export const NEW_PROFESSIONAL_RULE_TEMPLATE_ROUTE_AUTH_CODE = 'page_new-professional-rule-template';
export const EDIT_PROFESSIONAL_RULE_TEMPLATE_ROUTE_PATH =
  '/page/monitoring/professional-rule-configuration/rule-template-list/:id/edit';
export const EDIT_PROFESSIONAL_RULE_TEMPLATE_ROUTE_AUTH_CODE =
  'page_edit-professional-rule-template';
export type EditProfessionalRuleTemplateRouteParams = {
  id: number;
};
export const generateEditProfessionalRuleTemplateRoutePath = (
  params: EditProfessionalRuleTemplateRouteParams
) => generatePath(EDIT_PROFESSIONAL_RULE_TEMPLATE_ROUTE_PATH, params);

/** 通道列表 */
export const CHANNEL_CONFIG_LIST = '/page/channel-config/list';

export const CHANNEL_CONFIG_NEW = '/page/channel-config/new';

export const CHANNEL_CONFIG_EDIT = '/page/channel-config/edit/:id';
export const CHANNEL_CONFIG_COPY = '/page/channel-config/copy/:id';
export const CHANNEL_CONFIG_DETAIL = '/page/channel-config/detail/:id';
export const CHANNEL_CONFIG_ASSOCIATE = '/page/channel-config/associate/:id';
export const CHANNEL_CONFIG_IMPORT = '/page/channel-config/import';

export const CHANNEL_CONFIG_IMPORT_DEVICE = '/page/channel-config/import/:id';

export const generateEditChannelRoutePath = (params: { id: string }) =>
  generatePath(CHANNEL_CONFIG_EDIT, params);

export const generateCopyChannelRoutePath = (params: { id: string }) =>
  generatePath(CHANNEL_CONFIG_COPY, params);

export const generateChannelDetailRoutePath = (params: {
  id: string;
  tab?: ChannelConfigDetailTabKey;
}) => {
  const path = generatePath(CHANNEL_CONFIG_DETAIL, params);
  let search = '';
  if (params.tab) {
    search += `?tab=${params.tab}`;
  }
  return `${path}${search}`;
};

export const generateChannelAssociateRoutePath = (params: { id: string }) =>
  generatePath(CHANNEL_CONFIG_ASSOCIATE, params);

export const generateChannelDeviceRoutePath = (params: { id: string }) =>
  generatePath(CHANNEL_CONFIG_IMPORT_DEVICE, params);

export const AI_OPS_ALARM_ROUTE_PATH = '/page/monitoring/ai-ops-alarms';
export const AI_OPS_ALARM_ROUTE_PATH_CODE = 'page_ai_ops_alarms';

export const AI_OPS_MONITORING_STATISTICS_ROUTE_PATH = '/page/monitoring/ai-ops-statistics';
export const AI_OPS_MONITORING_STATISTICS_ROUTE_PATH_CODE = 'page_ai_ops_statistics';

//告警规则配置
export const LOCAL_ALARM_RULE_PATH = '/page/monitoring/configs/local-alarm-rules';
export const LOCAL_ALARM_RULE_PATH_CODE = 'page_local-alarm-rule';

export const generateLocalAlarmRuleRoutePath = ({ idc }: { idc: string }) => {
  return `${generatePath(LOCAL_ALARM_RULE_PATH)}?idc=${idc}`;
};

export const LOCAL_ALARM_RULE_IMPORT_PATH = '/page/monitoring/local-alarm-rule-import';
export const LOCAL_ALARM_RULE_IMPORT_PATH_CODE = 'page_local-alarm-rule-import';

export const generateLocalAlarmRuleImportRoutePath = ({ blockGuid }: { blockGuid: string }) => {
  return `${LOCAL_ALARM_RULE_IMPORT_PATH}?blockGuid=${blockGuid}`;
};

/** 属地监控配置-设备测点配置 */
export const NON_POINT_ROUTE_PATH = '/page/monitoring/configs/non-point';
export const NON_POINT_ROUTE_AUTH_CODE = 'page_non-point';

export const NON_POINT_IMPORT_ROUTE_PATH = '/page/monitoring/non-point-import';
export const NON_POINT_IMPORT_ROUTE_AUTH_CODE = 'page_non-point-import';

export const generateNonPointImportRoutePath = (params: { blockGuid: string }) => {
  const path = generatePath(NON_POINT_IMPORT_ROUTE_PATH, params);
  return `${path}${`?blockGuid=${params.blockGuid}&idcTag=${
    getSpaceGuidMap(params.blockGuid).idc
  }`}`;
};

export const generateNonPointRoutePath = ({ idc }: { idc: string }) => {
  return `${generatePath(NON_POINT_ROUTE_PATH)}?idc=${idc}`;
};

/** 定制化液冷监控 */
export type LiquidCoolingRouteParams = {
  idc: string;
  block: string;
  room: string;
};
export const LIQUID_COOLING_ROUTE_PATH = '/page/monitoring/liquid-cooling/:idc/:block/:room';
export const LIQUID_COOLING_ROUTE_AUTH_CODE = 'page_liquid-cooling';

/**告警屏蔽*/
export const ALARM_SHIELD_LIST_ROUTE_PATH = '/page/monitoring/alarm-shield/list';
export const ALARM_SHIELD_LIST_ROUTE_AUTH_CODE = 'page_alarm-shield-list';

export const ALARM_SHIELD_DETAIL_ROUTE_PATH = '/page/monitoring/alarm-shield/detail/:id';
export const ALARM_SHIELD_DETAIL_ROUTE_AUTH_CODE = 'page_alarm-shield_detail';

export const generateAlarmShieldDetailRoutePath = ({ id }: { id: string }) =>
  generatePath(ALARM_SHIELD_DETAIL_ROUTE_PATH, { id });

export const ALARM_SHIELD_CREATE_ROUTE_PATH = '/page/monitoring/alarm-shield/create';
export const ALARM_SHIELD_CREATE_ROUTE_AUTH_CODE = 'page_alarm-shield_create';

export const ALARM_SHIELD_EDIT_ROUTE_PATH = '/page/monitoring/alarm-shield/edit/:id';
export const ALARM_SHIELD_EDIT_ROUTE_AUTH_CODE = 'page_alarm-shield_edit';

export const generateAlarmShieldEditRoutePath = ({
  id,
  mode = 'edit',
}: {
  id: string;
  mode?: 'edit' | 'copy';
}) => {
  const path = generatePath(ALARM_SHIELD_EDIT_ROUTE_PATH, { id });
  return `${path}?mode=${mode}`;
};

export const CUSTOM_POINT_ROUTE_PATH = '/page/monitoring/configs/custom-points/:idc';
export const CUSTOM_POINT_ROUTE_CODE = 'page_monitoring_custom-points';

export const generateCustomPointRoutePath = ({ idc }: { idc: string }) =>
  generatePath(CUSTOM_POINT_ROUTE_PATH, { idc });
