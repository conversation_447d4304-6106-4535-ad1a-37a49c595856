import { waitFor } from '@testing-library/react';
import { renderHook } from '@testing-library/react-hooks';

import { createCompositionWrapper } from '@manyun/monitoring.state.rule-types';
import { useRemoteMock } from '@manyun/service.request';

import { useRuleType } from './use-rule-type';

const StoreCompositionWrapper = createCompositionWrapper();

let mockOff: ReturnType<typeof useRemoteMock>;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

it('should return rule type 10101', async () => {
  const { result } = renderHook(() => useRuleType('10101'), { wrapper: StoreCompositionWrapper });
  await waitFor(() => {
    expect(result.current!.metaCode).toBe('10101');
  });
});
