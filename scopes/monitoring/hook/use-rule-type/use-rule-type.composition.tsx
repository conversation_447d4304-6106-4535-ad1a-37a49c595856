import React from 'react';

import { wrapCompositions } from '@manyun/dc-brain.composition.compositions-wrapper';
import { createCompositionWrapper } from '@manyun/monitoring.state.rule-types';

import { useRuleType } from './use-rule-type';

const StoreCompositionWrapper = createCompositionWrapper();
const CompositionWrapper = wrapCompositions([StoreCompositionWrapper]);

export const BasicuseRuleType = () => {
  const { metaName, metaCode } = useRuleType('10101') ?? {};

  return (
    <code>
      {metaName}({metaCode})
    </code>
  );
};

export const RenderRuleType = () => (
  <CompositionWrapper>
    <BasicuseRuleType />
  </CompositionWrapper>
);
