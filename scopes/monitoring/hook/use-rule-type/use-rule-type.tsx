import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import shallowequal from 'shallowequal';

import { message } from '@manyun/base-ui.ui.message';

import { getRuleTypesAction, selectRuleTypes } from '@manyun/monitoring.state.rule-types';
import type { RuleType } from '@manyun/monitoring.state.rule-types';

export function useRuleType(code: string): RuleType | undefined {
  const dispatch = useDispatch();
  React.useEffect(() => {
    dispatch(getRuleTypesAction({ callback: error => error && message.error(error.message) }));
  }, [dispatch]);

  const { entities } = useSelector(selectRuleTypes, (left, right) =>
    shallowequal(left.entities, right.entities)
  );

  return entities?.[code];
}
