import { useCallback, useState } from 'react';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { message } from '@manyun/base-ui.ui.message';

import { fetchPointValueTops } from '@manyun/monitoring.service.fetch-point-value-tops';
import type { Device, SvcQuery } from '@manyun/monitoring.service.fetch-point-value-tops';

export type UsePointValueTopsProps = SvcQuery;

export function usePointValueTops(params: UsePointValueTopsProps) {
  const [topsData, setTopsData] = useState<Device[]>([]);
  const queryParams = useDeepCompareMemo(() => params, [params]);

  const fetchPointValueTopsData = useCallback(async () => {
    if (!queryParams.devicePointList.length) {
      return;
    }
    const { data, error } = await fetchPointValueTops(queryParams);
    if (error) {
      message.error(error.message);
      return;
    }
    setTopsData(data.data);
  }, [queryParams]);

  return [topsData, { fetchPointValueTopsData }] as const;
}
