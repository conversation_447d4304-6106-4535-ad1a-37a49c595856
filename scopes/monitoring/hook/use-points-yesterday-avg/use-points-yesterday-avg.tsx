import { useCallback, useState } from 'react';

import dayjs from 'dayjs';
import get from 'lodash.get';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { message } from '@manyun/base-ui.ui.message';

import { fetchChartData } from '@manyun/monitoring.service.fetch-chart-data';
import type { PointGuid } from '@manyun/monitoring.service.fetch-chart-data';

export type UsePointsYesterdayAvgProps = {
  idc: string;
  pointGuids: PointGuid[];
};

export function usePointsYesterdayAvg({ idc, pointGuids }: UsePointsYesterdayAvgProps) {
  const [yesterdayAvgDatas, setYesterdayAvgDatas] = useState<
    (PointGuid & { value: number | undefined })[]
  >([]);
  const pointGuidList = useDeepCompareMemo(() => pointGuids, [pointGuids]);

  const fetchYesterdayAvg = useCallback(async () => {
    if (!pointGuidList.length) {
      setYesterdayAvgDatas([]);
      return;
    }
    const yesterday = dayjs().subtract(1, 'day');
    const { data, error } = await fetchChartData({
      pointGuidList,
      startTime: yesterday.startOf('d').valueOf(),
      endTime: yesterday.endOf('d').valueOf(),
      function: 'AVG',
      interval: 'D',
      idcTag: idc,
    });
    if (error) {
      message.error(error.message);
      return;
    }
    setYesterdayAvgDatas(
      data.data.map((series, index) => {
        const val = get(series, [0, 1]);

        return {
          ...pointGuidList[index],
          value: typeof val === 'number' ? val : undefined,
        };
      })
    );
  }, [idc, pointGuidList]);

  return [yesterdayAvgDatas, { fetchYesterdayAvg }] as const;
}
