import { useCallback, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useMountedState } from 'react-use';

import { message } from '@manyun/base-ui.ui.message';

import type {
  MonitoringItem,
  MonitoringItemJSON,
  NotifyRule,
} from '@manyun/monitoring.model.monitoring-item';
import type { SvcRespData } from '@manyun/monitoring.service.fetch-alarm-configuration';
import { fetchAlarmConfiguration } from '@manyun/monitoring.service.fetch-alarm-configuration';
import { fetchAlarmTemplateGroups } from '@manyun/monitoring.service.fetch-alarm-template-groups';
import type { TemplateGroup } from '@manyun/monitoring.service.fetch-alarm-template-groups';
import { fetchMonitoringItemsByAlarmConfiguration } from '@manyun/monitoring.service.fetch-monitoring-items-by-alarm-configuration';
import {
  alarmConfigurationTemplateSliceActions,
  selectCloneTemplatesEntities,
  selectCurrentMutateType,
  selectMutateAlarmConfiguration,
} from '@manyun/monitoring.state.alarm-configuration-template';
import type {
  MutateType,
  SetMutateFieldsActionPayload,
  SetMutateMonitorItemFieldsActionPayload,
} from '@manyun/monitoring.state.alarm-configuration-template';

export function useCurrentMutateType() {
  const dispatch = useDispatch();
  const currentMutateType = useSelector(selectCurrentMutateType);

  const setCurrentMutateType = useCallback(
    (type: MutateType) => {
      dispatch(alarmConfigurationTemplateSliceActions.setCurrentMutateType({ type }));
    },
    [dispatch]
  );

  return [currentMutateType, setCurrentMutateType] as const;
}

export function useMutateTplFields(mutateType: MutateType) {
  const dispatch = useDispatch();
  const mutateFileds = useSelector(selectMutateAlarmConfiguration(mutateType));

  const monitoringItems = mutateFileds.monitorItemIds.map(
    id => mutateFileds.monitorItemEntities[id]
  );

  const setMutateTplFields = useCallback(
    (fields: Omit<SetMutateFieldsActionPayload, 'mutateType'>) => {
      dispatch(
        alarmConfigurationTemplateSliceActions.setMutateFields({
          ...fields,
          mutateType,
        })
      );
    },
    [dispatch, mutateType]
  );

  const addMutateTplMonitorItem = useCallback(
    (item: Partial<MonitoringItemJSON>) => {
      dispatch(
        alarmConfigurationTemplateSliceActions.addMutateMonitorItem({
          mutateType,
          ...item,
        })
      );
    },
    [dispatch, mutateType]
  );

  const deleteMutateTplMonitorItem = useCallback(
    (id: number | string) => {
      dispatch(alarmConfigurationTemplateSliceActions.deleteMutateMonitorItem({ mutateType, id }));
    },
    [dispatch, mutateType]
  );

  const updateMutateTplMonitorItemFields = useCallback(
    (fields: Omit<SetMutateMonitorItemFieldsActionPayload, 'mutateType'>) => {
      dispatch(
        alarmConfigurationTemplateSliceActions.setMutateMonitorItemFields({
          ...fields,
          mutateType,
        })
      );
    },
    [dispatch, mutateType]
  );

  const updateMutateTplMonitorItemNotifyRules = useCallback(
    ({
      id,
      notifyRules,
    }: {
      id?: Pick<MonitoringItemJSON, 'id'> | undefined;
      notifyRules: NotifyRule[] /**新的规则 */;
    }) => {
      dispatch(
        alarmConfigurationTemplateSliceActions.setMutateMonitorItemNotifyRules({
          mutateType,
          id: id as unknown,
          notifyRules,
        })
      );
    },
    [dispatch, mutateType]
  );

  return [
    { ...mutateFileds, monitoringItems },
    {
      setMutateTplFields,
      addMutateTplMonitorItem,
      deleteMutateTplMonitorItem,
      updateMutateTplMonitorItemFields,
      updateMutateTplMonitorItemNotifyRules,
    },
  ] as const;
}

/**
 * 请求克隆模板
 * @returns
 */
export function useSetCloneTemplates() {
  const dispatch = useDispatch();
  const entities = useSelector(selectCloneTemplatesEntities);

  const setCloneTemplates = useCallback(
    data => {
      dispatch(alarmConfigurationTemplateSliceActions.setCloneTemplates({ data }));
    },
    [dispatch]
  );

  return [entities, setCloneTemplates] as const;
}

/**
 *  请求模板组(所有、或关联)
 * @returns
 */
export function useTemplateGroups() {
  const isMounted = useMountedState();
  const [data, setData] = useState<TemplateGroup[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  const fetchTplGroups = useCallback(
    async (varinat?: { groupId?: number; callback?: (data: TemplateGroup[]) => void }) => {
      setLoading(true);
      const { error, data } = await fetchAlarmTemplateGroups({ groupId: varinat?.groupId });
      if (isMounted()) {
        setLoading(false);
      }
      if (error) {
        message.error(error.message);
        return;
      }
      if (isMounted()) {
        setData(data.data);
      }

      varinat?.callback && varinat?.callback(data.data);
    },
    [isMounted]
  );
  return [{ loading, tplGroups: data }, fetchTplGroups] as const;
}

export function useBatchUpdateAlarmMonitorItemState(mutateType: MutateType) {
  const dispatch = useDispatch();

  const updateMonitoringItems = useCallback(
    (ids: (number | string)[] | string | number, enable: boolean) => {
      const isBatch = Array.isArray(ids);
      dispatch(
        alarmConfigurationTemplateSliceActions.setMutateMonitorItemsFieldsEnable({
          mutateType,
          ids: isBatch ? ids : [ids],
          enable: enable,
        })
      );
      if (isBatch) {
        dispatch(
          alarmConfigurationTemplateSliceActions.setMutateFields({
            mutateType,
            selectedMonitorItemIds: [],
          })
        );
      }
    },
    [dispatch, mutateType]
  );

  return [updateMonitoringItems] as const;
}

export function useGetAlarmTemplateAssociatedMonitoringItem(mutateType: MutateType) {
  const dispatch = useDispatch();
  const [loading, setLoaing] = useState<boolean>(false);
  const [monitoringItems, setMonitoringItems] = useState<MonitoringItem[]>([]);
  const isMounted = useMountedState();

  const fetchMonitorItems = useCallback(
    async ({ id, callback }: { id: number; callback?: () => void }) => {
      setLoaing(true);
      const { error, data } = await fetchMonitoringItemsByAlarmConfiguration({
        groupId: id,
      });
      if (isMounted()) {
        setLoaing(false);
      }
      if (error) {
        message.error(error.message);
        return;
      }
      const ids: number[] = [];
      const entity: Record<number | string, MonitoringItemJSON> = {};
      data.data.forEach(monitorItem => {
        ids.push(monitorItem.id as number);
        entity[monitorItem.id as number] = monitorItem as MonitoringItemJSON;
      });
      dispatch(
        alarmConfigurationTemplateSliceActions.setMutateFields({
          mutateType,
          monitorItemEntities: entity,
          monitorItemIds: ids,
          notifyConfigMode: 'INDEPENDENT',
        })
      );
      if (isMounted()) {
        setMonitoringItems(data.data);
      }
    },
    [dispatch, isMounted, mutateType]
  );

  return [{ loading, monitoringItems }, { fetchMonitorItems }] as const;
}

export function useGetAlarmTemplateConfiguration(mutateType: MutateType) {
  const dispatch = useDispatch();
  const [loading, setLoaing] = useState<boolean>(false);

  const fetchAlarmConfigure = useCallback(
    async ({ id, callback }: { id: number; callback?: (data: SvcRespData) => void }) => {
      setLoaing(true);
      const getBasicInfo = await fetchAlarmConfiguration({
        id: id,
      });
      const getAssociateGroups = await fetchAlarmTemplateGroups({
        groupId: id,
      });

      Promise.all([getBasicInfo, getAssociateGroups])
        .then(res => {
          const basicInfoRes = res[0];
          const associateGroupsRes = res[1];
          const associateGroupIds = associateGroupsRes.data.data.map(d => d.id);
          if (basicInfoRes.error) {
            message.error(basicInfoRes.error.message);
          }
          if (associateGroupsRes.error) {
            message.error(associateGroupsRes.error.message);
          }
          if (basicInfoRes.error && associateGroupsRes.error) {
            return;
          }
          const basicInfo = { ...basicInfoRes.data, associateGroupIds } as SvcRespData;
          callback && callback(basicInfo);
          dispatch(
            alarmConfigurationTemplateSliceActions.setMutateFields({
              mutateType,
              ...basicInfo,
            })
          );
        })
        .finally(() => {
          setLoaing(false);
        });
    },
    [dispatch, mutateType]
  );

  return [{ loading }, { fetchAlarmConfigure }] as const;
}
