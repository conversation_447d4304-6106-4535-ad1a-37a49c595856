import React, { useMemo, useState } from 'react';
import { useSelector } from 'react-redux';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { message } from '@manyun/base-ui.ui.message';

import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { getTimeRangeFromNow } from '@manyun/monitoring.chart.duration-select';
import type { SvcRespData } from '@manyun/monitoring.service.fetch-diff-point-data';
import { fetchDiffPointData } from '@manyun/monitoring.service.fetch-diff-point-data';
import { getSpaceGuid } from '@manyun/resource-hub.util.space-guid';

import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';

type DevicePoint = { deviceGuid: string; pointCode: string };

type WueList = {
  key: string;
  value: (DevicePoint & { wue: string })[];
};

/**
 * 获取WUE动态值
 * getPointCode获取水code和it总用量
 * 走接口获取wue数据
 * @param idc 如： 'EC06'
 * @param blocks 如：['A']
 * @returns
 */
export function useWueData(idc: string, blocks: string[]) {
  const [idcWueList, setIdcWueList] = useState<WueList[]>([]);
  const [blockWueList, setBlockWueList] = useState<WueList[]>([]);

  const config = useSelector(selectCurrentConfig);
  const configUtil = useMemo(() => new ConfigUtil(config), [config]);
  const idcDeviceType = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_IDC);
  const blockDeviceType = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_BLOCK);
  const blockWaterPointCode = useMemo(
    () =>
      blockDeviceType
        ? configUtil.getPointCode(blockDeviceType, ConfigUtil.constants.pointCodes.WATER_USE)
        : undefined,
    [blockDeviceType, configUtil]
  );
  const blockPowerPointCode = useMemo(
    () =>
      blockDeviceType
        ? configUtil.getPointCode(
            blockDeviceType,
            ConfigUtil.constants.pointCodes.ACTIVE_IT_POWER_ENERGY_SUM
          )
        : undefined,
    [blockDeviceType, configUtil]
  );
  const idcPowerPointCode = useMemo(
    () =>
      idcDeviceType
        ? configUtil.getPointCode(
            idcDeviceType,
            ConfigUtil.constants.pointCodes.ACTIVE_IT_POWER_ENERGY_SUM
          )
        : undefined,
    [configUtil, idcDeviceType]
  );
  const WUEIntervalDays = useMemo(() => configUtil.getWUEIntervalDays(), [configUtil]);

  const idcWaterPointCode = useMemo(
    () =>
      idcDeviceType
        ? configUtil.getPointCode(idcDeviceType, ConfigUtil.constants.pointCodes.WATER_USE)
        : undefined,
    [configUtil, idcDeviceType]
  );

  const idcPointGuidList = useMemo(
    () => [
      {
        deviceGuid: idc,
        pointCode: idcWaterPointCode,
      },
      {
        deviceGuid: idc,
        pointCode: idcPowerPointCode,
      },
    ],
    [idc, idcPowerPointCode, idcWaterPointCode]
  );

  const wuePointCodes = blocks.map(block => {
    const deviceGuid = getSpaceGuid(idc, block)!;
    return {
      water: {
        deviceGuid,
        pointCode: blockWaterPointCode,
      },
      power: {
        deviceGuid,
        pointCode: blockPowerPointCode,
      },
    };
  });

  const blockPointGuidList: DevicePoint[] = useDeepCompareMemo(
    () => [...wuePointCodes.map(item => item?.water), ...wuePointCodes.map(item => item?.power)],
    [wuePointCodes]
  );

  const fetchPointData = React.useCallback(async () => {
    const timeRange = getTimeRangeFromNow(-WUEIntervalDays, 'day', false);

    if (idc) {
      const { data, error } = await fetchDiffPointData({
        idcTag: idc,
        startTime: timeRange[0].valueOf(),
        endTime: timeRange[1].valueOf(),
        pointGuidList: [...idcPointGuidList, ...blockPointGuidList],
      });
      if (error) {
        message.error(error.message);
        return;
      }

      if (data) {
        const wueList = Object.keys(data).map(key => {
          const keyLen = key.split('.').length;
          const isBlock = keyLen > 2;
          const code = key.split('.')[keyLen - 1];

          const PointGuidList = isBlock
            ? blockPointGuidList.filter(point => point.pointCode === code)
            : idcPointGuidList.filter(point => point.pointCode === code);

          return {
            key,
            value: getWuePointValue(
              PointGuidList,
              isBlock,
              data,
              blockWaterPointCode,
              idcWaterPointCode,
              blockPowerPointCode,
              idcPowerPointCode
            ),
          };
        });
        setIdcWueList(wueList.filter(list => list.key.split('.').length <= 2));
        setBlockWueList(wueList.filter(list => list.key.split('.').length > 2));
      }
    }
  }, [
    WUEIntervalDays,
    blockPointGuidList,
    blockPowerPointCode,
    blockWaterPointCode,
    idc,
    idcPointGuidList,
    idcPowerPointCode,
    idcWaterPointCode,
  ]);

  React.useEffect(() => {
    fetchPointData();
    const currentInterval = window.setInterval(fetchPointData, 15 * 1000);

    return () => {
      window.clearInterval(currentInterval);
    };
  }, [fetchPointData]);

  return { blockWueList, idcWueList };
}

function getWuePointValue(
  list: DevicePoint[],
  isBlock: boolean,
  data: SvcRespData,
  blockWaterPointCode: string,
  idcWaterPointCode: string,
  blockPowerPointCode: string,
  idcPowerPointCode: string
) {
  return list.map(({ deviceGuid, pointCode }: DevicePoint) => {
    const water = data[`${deviceGuid}.${isBlock ? blockWaterPointCode : idcWaterPointCode}`];
    const power = data[`${deviceGuid}.${isBlock ? blockPowerPointCode : idcPowerPointCode}`];

    return {
      deviceGuid,
      pointCode,
      wue:
        typeof water === 'number' && typeof power === 'number' && water * power !== 0
          ? Number((water / power) * 1000).toFixed(2)
          : BLANK_PLACEHOLDER,
    };
  });
}
