import { useCallback, useEffect, useState } from 'react';

import { message } from '@manyun/base-ui.ui.message';

import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { usePointsData } from '@manyun/monitoring.hook.use-points-data';
import type { BlockWeather } from '@manyun/monitoring.service.fetch-realtime-weather-by-block';
import { fetchRealtimeWeatherByBlock } from '@manyun/monitoring.service.fetch-realtime-weather-by-block';

export type RealTimeWeatherProps = {
  blockGuid: string;
};

/**
 * 获取实时天气数据
 */
export function useRealTimeWeather({ blockGuid }: RealTimeWeatherProps) {
  const [weather, setWeather] = useState<BlockWeather>();
  const fetchRealtimeWeather = useCallback(async () => {
    if (!blockGuid) {
      return;
    }
    const { data, error } = await fetchRealtimeWeatherByBlock({ blockGuid });
    if (error) {
      message.error(error.message);
      return;
    }
    data && setWeather(data);
  }, [blockGuid]);

  useEffect(() => {
    fetchRealtimeWeather();
    const countdownInterval = window.setInterval(fetchRealtimeWeather, 5 * 60 * 1000);

    return () => {
      window.clearInterval(countdownInterval);
    };
  }, [fetchRealtimeWeather]);

  const deviceType = ConfigUtil.constants.deviceTypes.SPACE_BLOCK;
  const dryBulbPointCode = ConfigUtil.constants.pointCodes.PSYCHROMETER_T_OF_DRY_BULB_AVG;
  const wetBulbPointCode = ConfigUtil.constants.pointCodes.PSYCHROMETER_T_OF_WET_BULB_AVG;
  const outdoorHumidityPointCode = ConfigUtil.constants.pointCodes.OUTDOOR_RH_AVG;
  const dewBulbPointCode = ConfigUtil.constants.pointCodes.PSYCHROMETER_T_OF_DEW_POINT_AVG;
  const [pointsData] = usePointsData({
    blockGuid: blockGuid!,
    moduleId: 'tempturePointsData',
    targets: [
      {
        guid: blockGuid!,
        predefinedType: deviceType,
        predefinedPoints: [
          {
            pointCode: dryBulbPointCode,
            dataType: 'AI',
          },
          {
            pointCode: wetBulbPointCode,
            dataType: 'AI',
          },
          {
            pointCode: outdoorHumidityPointCode,
            dataType: 'AI',
          },
          {
            pointCode: dewBulbPointCode,
            dataType: 'AI',
          },
        ],
      },
    ],
  });
  const dryBulbPointData = pointsData[blockGuid]?.get(dryBulbPointCode);
  const wetBulbPointData = pointsData[blockGuid]?.get(wetBulbPointCode);
  const outdoorHumidityPointData = pointsData[blockGuid]?.get(outdoorHumidityPointCode);
  const dewBulbPointData = pointsData[blockGuid]?.get(dewBulbPointCode);

  return {
    weather,
    /** 干球温度 */
    dryBulbPointData,
    /** 湿球温度 */
    wetBulbPointData,
    /** 室外湿度 */
    outdoorHumidityPointData,
    /** 露点温度 */
    dewBulbPointData,
  };
}
