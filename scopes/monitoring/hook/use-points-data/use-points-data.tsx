import { useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useDeepCompareEffect } from 'react-use';

import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import { syncCommonDataAction } from '@manyun/dc-brain.state.common';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import type { DataType } from '@manyun/monitoring.model.point';
import type { SvcQuery as PointValueSvcQuery } from '@manyun/monitoring.service.fetch-point-values';
import { fetchPointValues } from '@manyun/monitoring.service.fetch-point-values';
import type { SubscriptionsMode } from '@manyun/monitoring.state.subscriptions';
import {
  SUBSCRIPTIONS_MODE,
  getMonitoringData,
  addRealtimeNAlarmsDataSubscriptionActionCreator as subscribe,
  subscriptionsSliceActions,
  removeRealtimeNAlarmsDataSubscriptionActionCreator as unsubscribe,
} from '@manyun/monitoring.state.subscriptions';
import type { PointData } from '@manyun/monitoring.util.get-monitoring-data';
import { generateGetPointMonitoringData } from '@manyun/monitoring.util.get-monitoring-data';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

type PredefinedPointCode = string;
export type PointMapType = Map<PredefinedPointCode, PointData>;
export type PointsDataType = Record<string, PointMapType>;

type SimplePointType = {
  /** 测点类型 */
  dataType?: DataType;
  /** 测点精度 */
  precision?: number;
};
export type SimplePoints = {
  /** 预定义测点(eg:ConfigUtil.constants.pointCodes.IT_LOAD_RATE_MAX) 或者真实测点(eg:1004000) */
  pointCode: string;
} & SimplePointType;

type PointBase = {
  /**  设备｜空间guid */
  guid: string;
  /**  设备｜空间type */
  type?: string;
  /** 预定义type, 如 ConfigUtil.constants.deviceTypes.SPACE_BLOCK */
  predefinedType?: string;
};

export type PointTarget = PointBase &
  (
    | {
        /** 真实测点 */
        hardCodedPoints: SimplePoints[];
      }
    | {
        /** 预定义测点  */
        predefinedPoints: SimplePoints[];
      }
  );

export type SubscribeMethod = 'ws' | 'fetch';

/** 监控测点实时值 */
export function usePointsData({
  blockGuid,
  moduleId,
  mode = SUBSCRIPTIONS_MODE.REALTIME_N_ALARMS, // 默认
  targets = [],
  subscribeMethod = 'fetch', // 默认
  fetchModeTime = 15 * 1000, // 默认 fetch方式时请求周期
}: {
  blockGuid: string;
  /** 自定义唯一订阅id */
  moduleId: string;
  /** 订阅设备｜空间 */
  targets: PointTarget[];
  mode?: SubscriptionsMode;
  /** 订阅方式 */
  subscribeMethod?: SubscribeMethod; // subscribeMethod = fetch, targets需要hardCodedPoints
  fetchModeTime?: number; // fetch方式时请求周期
}) {
  const [pointsData, setPointDataMapper] = useState<PointsDataType>({});

  const dispatch = useDispatch();
  const { devicesRealtimeData, devicesAlarmsData } =
    useSelector(getMonitoringData);
  const config = useSelector(selectCurrentConfig);

  const targetGuidList = targets.map((info) => info.guid);
  const configUtil = useMemo(() => new ConfigUtil(config), [config]);

  const deviceTypesPointsDefinitionList = Array.from(
    new Set(
      targets
        .map((point) => {
          if (point.type) {
            return point.type;
          }
          if (point.predefinedType) {
            return configUtil.getOneDeviceType(point.predefinedType);
          }
          return undefined;
        })
        .filter(Boolean) as string[]
    )
  );

  const pointTargets = targets
    .map((point) => {
      if (point.type) {
        return {
          ...point,
          type: point.type,
        };
      }
      if (point.predefinedType) {
        return {
          ...point,
          type: configUtil.getOneDeviceType(point.predefinedType),
        };
      }
      return undefined;
    })
    .filter(Boolean) as PointTarget[];

  useDeepCompareEffect(() => {
    if (deviceTypesPointsDefinitionList.length) {
      dispatch(
        syncCommonDataAction({
          // 同步测点定义类型
          strategy: {
            deviceTypesPointsDefinition: deviceTypesPointsDefinitionList,
          },
        })
      );
    }
  }, [dispatch, deviceTypesPointsDefinitionList]);

  useDeepCompareEffect(() => {
    const isSubscribe =
      blockGuid && targetGuidList.length && subscribeMethod === 'ws';
    if (isSubscribe) {
      dispatch(
        subscribe({ mode, blockGuid, moduleId, deviceGuids: targetGuidList })
      );
    }

    return () => {
      if (isSubscribe) {
        dispatch(unsubscribe({ blockGuid, mode, moduleId }));
      }
    };
  }, [blockGuid, dispatch, mode, moduleId, targetGuidList, subscribeMethod]);

  useDeepCompareEffect(() => {
    if (pointTargets.length) {
      const getPointMonitoringData = generateGetPointMonitoringData({
        realtimeData:
          mode === SUBSCRIPTIONS_MODE.ALARMS_ONLY ? null : devicesRealtimeData,
        alarmsData:
          mode === SUBSCRIPTIONS_MODE.REALTIME_ONLY ? null : devicesAlarmsData,
        configUtil,
      });
      let mapper = {};
      mapper = pointTargets.reduce((map: PointsDataType, target) => {
        const { guid, type, predefinedType } = target;
        let hardCodedPointCodes: SimplePoints[] = [];
        if ('hardCodedPoints' in target) {
          const { hardCodedPoints } = target;
          hardCodedPointCodes = hardCodedPoints;
          if (!hardCodedPoints && devicesRealtimeData) {
            hardCodedPointCodes = Object.keys(
              devicesRealtimeData[guid].pointValueMap
            ).map((pointCode) => ({
              pointCode,
            }));
          }
        } else if ('predefinedPoints' in target) {
          const { predefinedPoints } = target;
          hardCodedPointCodes = predefinedPoints;
        }

        const deviceTypeLocal =
          type ??
          (predefinedType && configUtil.getOneDeviceType(predefinedType));
        if (typeof deviceTypeLocal !== 'string') {
          if (env.NODE_ENV === 'development') {
            throw new Error(`guid: ${guid}-缺少设备类型`);
          } else {
            console.error(`guid: ${guid}-缺少设备类型`);
          }
        } else {
          map[guid] = new Map( // TODO: @cf 存在targets内有predefined和hardedCode同时存在则map对象被覆盖问题(guid一样)，需要兼容
            hardCodedPointCodes?.map(({ pointCode, dataType, precision }) => [
              pointCode,
              getPointMonitoringData(
                {
                  deviceGuid: guid,
                  deviceType: deviceTypeLocal,
                },
                {
                  formatted: dataType === 'AI' || dataType === 'AO',
                  reflected: dataType === 'DI' || dataType === 'DO',
                  valueMappingIncluded: dataType === 'DI' || dataType === 'DO',
                  precision:
                    dataType === 'AI' || dataType === 'AO'
                      ? precision
                      : undefined,
                  ...('hardCodedPoints' in target
                    ? {
                        hardCodedPointCode: pointCode,
                      }
                    : {
                        pointType: pointCode,
                      }),
                }
              ),
            ])
          );
        }
        return map;
      }, {});
      setPointDataMapper(mapper);
    }
  }, [
    pointTargets,
    devicesRealtimeData,
    configUtil,
    mode,
    devicesAlarmsData,
    subscribeMethod,
  ]);

  useDeepCompareEffect(() => {
    let currentInterval = -1;

    if (pointTargets.length && blockGuid && subscribeMethod === 'fetch') {
      const getPointValueByFetch = async () => {
        if (blockGuid) {
          const idc = getSpaceGuidMap(blockGuid).idc!;
          const pointGuidsMap = pointTargets.reduce((result, curr) => {
            if ('hardCodedPoints' in curr) {
              result[curr.guid] = curr.hardCodedPoints
                .map((target) => target?.pointCode)
                .flat()
                .filter(Boolean)
                .join(',');
            }
            return result;
          }, {});

          const params: PointValueSvcQuery = {
            idc,
            pointGuids: pointGuidsMap,
          };

          const { error, data } = await fetchPointValues(params);
          if (error) {
            console.error(`fetchPointValuesError:${error.message}`);
            return;
          }
          dispatch(subscriptionsSliceActions.receivedRealtimeData(data));
        }
      };
      getPointValueByFetch();
      currentInterval = window.setInterval(getPointValueByFetch, fetchModeTime);
    }

    return () => {
      window.clearInterval(currentInterval);
    };
  }, [subscribeMethod, pointTargets, dispatch, blockGuid, fetchModeTime]);

  return [pointsData, { configUtil }] as const;
}
