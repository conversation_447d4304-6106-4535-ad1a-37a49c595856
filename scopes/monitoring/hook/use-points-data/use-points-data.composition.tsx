import React from 'react';

import { ConfigUtil } from '@manyun/dc-brain.util.config';

import { usePointsData } from './use-points-data';

export const BasicusePointsData = () => {
  const [hardCodedPointData] = usePointsData({
    // 真实测点
    blockGuid: 'EC06.A',
    moduleId: 'EfficiencyHardCodedPointData',
    targets: [
      {
        guid: 'EC06.A',
        type: '10101',
        hardCodedPoints: [
          {
            pointCode: '1004000',
            dataType: 'AI',
          },
        ],
      },
    ],
  });

  const [predefinedPointData] = usePointsData({
    // 预定义测点
    blockGuid: 'EC06.A',
    moduleId: 'EfficiencyPredefinedPointData',
    targets: [
      {
        guid: 'EC06.A',
        type: '10101',
        predefinedPoints: [
          {
            pointCode: ConfigUtil.constants.pointCodes.IT_LOAD_RATE_MAX,
            dataType: 'AI',
          },
        ],
      },
    ],
  });

  return (
    <>
      <h1>hardCodedPointData: {hardCodedPointData['EC06.A'].get('1004000')?.value}</h1>
      <h1>
        {predefinedPointData['EC06.A'].get(ConfigUtil.constants.pointCodes.IT_LOAD_RATE_MAX)?.value}
      </h1>
    </>
  );
};
