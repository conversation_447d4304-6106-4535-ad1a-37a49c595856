---
description: '获取空间 设备 测点监控数据（实时和告警），支持预定义或者'
labels: ['hook', 'point']
---

### Component usage

```js
import { ConfigUtil } from '@manyun/dc-brain.util.config';

import { usePointsData } from './use-points-data';

const [predefinedPointData] = usePointsData({
  // 预定义测点
  blockGuid: 'EC06.A',
  moduleId: 'EfficiencyPredefinedPointData',
  targets: [
    {
      guid: 'EC06.A',
      type: '10101',
      predefinedPoints: [
        {
          pointCode: ConfigUtil.constants.pointCodes.IT_LOAD_RATE_MAX,
          dataType: 'AI',
        },
      ],
    },
  ],
});

<h1>
  {predefinedPointData['EC06.A'].get(ConfigUtil.constants.pointCodes.IT_LOAD_RATE_MAX)?.value}
</h1>;
```
