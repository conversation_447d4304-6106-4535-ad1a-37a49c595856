import { useCallback, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';

import get from 'lodash.get';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { message } from '@manyun/base-ui.ui.message';

import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import type { Device } from '@manyun/resource-hub.model.device';
import { fetchDevices } from '@manyun/resource-hub.service.fetch-devices';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

export type UseMergedPointGuidsProps = {
  deviceType: string;
  blockGuid: string;
};

export type MergedPointGuid = {
  powerLine: string;
  deviceType: string;
  guid: string;
  pointCode: string;
};

export function useMergedPointGuids({ deviceType, blockGuid }: UseMergedPointGuidsProps) {
  const [devices, setDevices] = useState<Device[]>([]);
  const { idc, block } = getSpaceGuidMap(blockGuid);
  const config = useSelector(selectCurrentConfig);
  const configUtil = useMemo(() => {
    return new ConfigUtil(config);
  }, [config]);
  const blockDeviceType = configUtil.getOneDeviceType(
    ConfigUtil.constants.deviceTypes.SPACE_BLOCK
  )!;

  const fetchMergedPointGuids = useCallback(async () => {
    if (!idc || !block) {
      return;
    }
    const { data, error } = await fetchDevices({
      deviceTypes: [deviceType],
      block: block,
      idc: idc,
    });
    if (error) {
      message.error(error.message);
      return;
    }
    setDevices(data.data);
  }, [idc, block, deviceType]);

  const pointGuids = useDeepCompareMemo(() => {
    if (!devices.length) {
      return [];
    }
    const mapper = devices.reduce((map, { powerLine, guid, spaceGuid }) => {
      if (!powerLine) {
        return map;
      }
      const firstCode = powerLine[0];
      if (!map[firstCode]) {
        map[firstCode] = {
          powerLine: firstCode,
          deviceType,
          guid,
          pointCode: configUtil.getPointCode(
            deviceType,
            ConfigUtil.constants.pointCodes.HV_INCOMING_SWITCHGEAR_LOAD_RATE
          ),
        };
      } else {
        const preCode = get(ConfigUtil.constants.pointCodes, [
          `UTILITY_GRID_LOAD_RATE_LINE_${firstCode}_AVG`,
        ]);
        if (!preCode) {
          return map;
        }
        map[firstCode] = {
          powerLine: firstCode,
          deviceType: blockDeviceType,
          guid: spaceGuid.blockGuid!,
          pointCode: configUtil.getPointCode(blockDeviceType, preCode),
        };
      }

      return map;
    }, {} as Record<string, MergedPointGuid>);

    return Object.keys(mapper)
      .map(powerLine => mapper[powerLine])
      .filter(point => point.pointCode !== undefined)
      .sort((a, b) => a.powerLine.localeCompare(b.powerLine));
  }, [devices, configUtil]);

  return [pointGuids, { fetchMergedPointGuids }] as const;
}
