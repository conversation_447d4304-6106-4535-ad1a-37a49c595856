import { generatePageUrl } from './use-route-space-params';

it('should return the correct value', () => {
  expect(
    generatePageUrl('/page/monitoring/:idc/:block/:room', { idc: 'EC06', block: 'A', room: 'A1' })
  ).toBe('/page/monitoring/EC06/A/A1');
  expect(generatePageUrl('/page/cameras/:idc', { idc: 'EC06' })).toBe('/page/cameras/EC06');
  expect(generatePageUrl('/page/cameras/:idc', { idc: 'EC06', block: 'A', room: 'A1' })).toBe(
    '/page/cameras/EC06'
  );
  expect(generatePageUrl('/page/cameras/:test/:idc', { test: '123', idc: 'EC06' })).toBe(
    '/page/cameras/123/EC06'
  );
});
