import { useCallback } from 'react';
import { useHistory, useLocation, useRouteMatch } from 'react-router-dom';

import omit from 'lodash.omit';

import { getLocationSearchMap, qs } from '@manyun/base-ui.util.query-string';

export function useRouteSpaceParams() {
  const match = useRouteMatch<{
    idc?: string;
    block?: string;
    room?: string;
  }>();
  const { path } = match; // 为 /:idc 格式的地址
  const location = useLocation();
  const history = useHistory();
  const originalUrl = `${location.pathname}${location.search}`;

  const generateReplacedUrl = useCallback(
    (idc: string, block?: string, room?: string) => {
      // 若参数中没有 idc，则跳转到机房监控大屏
      if (!match.params.idc) {
        return;
      }

      // 若 search 中存在楼栋或者包间（例如：视频监控），则也替换其中的楼栋和包间参数
      if (
        location.search.includes('block=') ||
        location.search.includes('room=')
      ) {
        const searchParamsMap = getLocationSearchMap(location.search);
        const newSearchParamsStr = qs.stringify(
          omit(
            searchParamsMap,
            !block ? ['block', 'room'] : !room ? ['room'] : []
          )
        );
        return generatePageUrl(
          path,
          { ...match.params, idc },
          newSearchParamsStr ? `?${newSearchParamsStr}` : ''
        );
      }

      // 若传递了楼栋或者包间，则也替换其中的楼栋和包间参数（针对包间视图这样的页面）
      if (block && room) {
        return generatePageUrl(
          path,
          { ...match.params, idc, block, room },
          location.search
        );
      }

      // 若只传递了 block
      if (block) {
        // 若还需要传递 room，但是未传递，直接跳转到机房监控大屏
        if (match.params.room) {
          return;
        }
        return generatePageUrl(
          path,
          { ...match.params, idc, block },
          location.search
        );
      }

      // 若还需要传递 block 和 room，但是未传递，直接跳转到机房监控大屏
      if (match.params.block || match.params.room) {
        return;
      }
      return generatePageUrl(path, { ...match.params, idc }, location.search);
    },
    [location.search, match.params, path]
  );

  const replaceRouteSpaceParams = useCallback(
    (idc: string, block?: string, room?: string) => {
      const replacedUrl = generateReplacedUrl(idc, block, room);
      if (replacedUrl !== originalUrl) {
        history.push(replacedUrl);
      }
    },
    [generateReplacedUrl, history, originalUrl]
  );
  return replaceRouteSpaceParams;
}

/**
 * 生成页面地址
 * path: 形如 /:idc 格式的地址
 * params：匹配的参数
 * search：location.search
 * */
export function generatePageUrl(path: string, params: Object, search = '') {
  const pathname = Object.entries(params).reduce(
    (acc, [key, value]) => acc.replace(new RegExp(`/:${key}{1}?`), `/${value}`),
    path
  );
  return `${pathname}${search}`;
}
