import React, { useMemo, useState } from 'react';
import { useSelector } from 'react-redux';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { message } from '@manyun/base-ui.ui.message';

import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { getTimeRangeFromNow } from '@manyun/monitoring.chart.duration-select';
import type { SvcRespData } from '@manyun/monitoring.service.fetch-diff-point-data';
import { fetchDiffPointData } from '@manyun/monitoring.service.fetch-diff-point-data';
import { getSpaceGuid } from '@manyun/resource-hub.util.space-guid';

import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';

type DevicePoint = { deviceGuid: string; pointCode: string };

type CueList = {
  key: string;
  value: (DevicePoint & { cue: string })[];
};

/**
 * 获取cue动态值
 * getPointCode获取碳排放量和it总用量
 * 走接口获取cue数据
 * @param idc 如： 'EC06'
 * @param blocks 如：['A']
 * @returns
 */
export function useCueData(idc: string, blocks: string[]) {
  const [idcCueList, setIdcCueList] = useState<CueList[]>([]);
  const [blockCueList, setBlockCueList] = useState<CueList[]>([]);

  const config = useSelector(selectCurrentConfig);
  const configUtil = useMemo(() => new ConfigUtil(config), [config]);
  const idcDeviceType = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_IDC);
  const blockDeviceType = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_BLOCK);
  const blockPowerPointCode = useMemo(
    () =>
      blockDeviceType
        ? configUtil.getPointCode(
            blockDeviceType,
            ConfigUtil.constants.pointCodes.ACTIVE_IT_POWER_ENERGY_SUM
          )
        : undefined,
    [blockDeviceType, configUtil]
  );
  const idcPowerPointCode = useMemo(
    () =>
      idcDeviceType
        ? configUtil.getPointCode(
            idcDeviceType,
            ConfigUtil.constants.pointCodes.ACTIVE_IT_POWER_ENERGY_SUM
          )
        : undefined,
    [configUtil, idcDeviceType]
  );
  /**@todo @Jerry import form ConfigUtil */
  const blockCarbonPointCode = useMemo(() => '1260000', []);
  const idcCarbonPointCode = useMemo(() => '1048000', []);
  const cueIntervalDays = useMemo(() => configUtil.getWUEIntervalDays(), [configUtil]);

  const idcPointGuidList = useMemo(
    () => [
      {
        deviceGuid: idc,
        pointCode: idcCarbonPointCode,
      },
      {
        deviceGuid: idc,
        pointCode: idcPowerPointCode,
      },
    ],
    [idc, idcPowerPointCode, idcCarbonPointCode]
  );

  const cuePointCodes = blocks.map(block => {
    const deviceGuid = getSpaceGuid(idc, block)!;
    return {
      water: {
        deviceGuid,
        pointCode: blockCarbonPointCode,
      },
      power: {
        deviceGuid,
        pointCode: blockPowerPointCode,
      },
    };
  });

  const blockPointGuidList: DevicePoint[] = useDeepCompareMemo(
    () => [...cuePointCodes.map(item => item?.water), ...cuePointCodes.map(item => item?.power)],
    [cuePointCodes]
  );

  const fetchPointData = React.useCallback(async () => {
    const timeRange = getTimeRangeFromNow(-cueIntervalDays, 'day', false);

    if (idc) {
      const { data, error } = await fetchDiffPointData({
        idcTag: idc,
        startTime: timeRange[0].valueOf(),
        endTime: timeRange[1].valueOf(),
        pointGuidList: [...idcPointGuidList, ...blockPointGuidList],
      });
      if (error) {
        message.error(error.message);
        return;
      }

      if (data) {
        const CueList = Object.keys(data).map(key => {
          const keyLen = key.split('.').length;
          const isBlock = keyLen > 2;
          const code = key.split('.')[keyLen - 1];

          const PointGuidList = isBlock
            ? blockPointGuidList.filter(point => point.pointCode === code)
            : idcPointGuidList.filter(point => point.pointCode === code);

          return {
            key,
            value: getCuePointValue(
              PointGuidList,
              isBlock,
              data,
              blockCarbonPointCode,
              idcCarbonPointCode,
              blockPowerPointCode,
              idcPowerPointCode
            ),
          };
        });
        setIdcCueList(CueList.filter(list => list.key.split('.').length <= 2));
        setBlockCueList(CueList.filter(list => list.key.split('.').length > 2));
      }
    }
  }, [
    cueIntervalDays,
    blockPointGuidList,
    blockPowerPointCode,
    blockCarbonPointCode,
    idc,
    idcPointGuidList,
    idcPowerPointCode,
    idcCarbonPointCode,
  ]);

  React.useEffect(() => {
    fetchPointData();
    const currentInterval = window.setInterval(fetchPointData, 15 * 1000);

    return () => {
      window.clearInterval(currentInterval);
    };
  }, [fetchPointData]);

  return { blockCueList, idcCueList };
}

function getCuePointValue(
  list: DevicePoint[],
  isBlock: boolean,
  data: SvcRespData,
  blockCarbonPointCode: string,
  idcCarbonPointCode: string,
  blockPowerPointCode: string,
  idcPowerPointCode: string
) {
  return list.map(({ deviceGuid, pointCode }: DevicePoint) => {
    const carbon = data[`${deviceGuid}.${isBlock ? blockCarbonPointCode : idcCarbonPointCode}`];
    const power = data[`${deviceGuid}.${isBlock ? blockPowerPointCode : idcPowerPointCode}`];

    return {
      deviceGuid,
      pointCode,
      cue:
        typeof carbon === 'number' && typeof power === 'number' && carbon * power !== 0
          ? Number(carbon / power).toFixed(2)
          : BLANK_PLACEHOLDER,
    };
  });
}
