import { useCallback, useState } from 'react';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { message } from '@manyun/base-ui.ui.message';

import { fetchPointPeakOrValley } from '@manyun/monitoring.service.fetch-point-peak-or-valley';

export type PointGuid = {
  deviceGuid: string;
  pointCode: string;
  mode: 'min' | 'max';
};

export type UsePointPeakOrValleyProps = {
  idc: string;
  pointGuids: PointGuid[];
};

export function usePointPeakOrValley({ idc, pointGuids }: UsePointPeakOrValleyProps) {
  const [peakOrValleyData, setPeakOrValleyDatas] = useState<
    (PointGuid & { value: number | null })[]
  >([]);
  const pointGuidList = useDeepCompareMemo(() => pointGuids, [pointGuids]);

  const fetchPointsValue = useCallback(async () => {
    if (!pointGuidList.length) {
      setPeakOrValleyDatas([]);
      return;
    }
    const promiseArr = pointGuidList.map(({ deviceGuid, pointCode, mode }) =>
      fetchPointPeakOrValley({
        idc,
        deviceGuid,
        pointCode,
        mode,
      })
    );
    Promise.all(promiseArr)
      .then(results => {
        setPeakOrValleyDatas(
          results.map(({ data, error }, index) => {
            if (error) {
              message.error(error.message);
            }

            return {
              ...pointGuidList[index],
              value: data,
            };
          })
        );
      })
      .catch(error => {
        console.error(error);
      });
  }, [idc, pointGuidList]);

  return [peakOrValleyData, { fetchPointsValue }] as const;
}
