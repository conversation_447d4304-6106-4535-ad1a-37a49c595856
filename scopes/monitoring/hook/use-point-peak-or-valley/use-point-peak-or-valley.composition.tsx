import React, { useEffect } from 'react';

import get from 'lodash.get';

import { usePointPeakOrValley } from './use-point-peak-or-valley';

export const BasicUsePointPeakOrValley = () => {
  const [peakOrValleyData, { fetchPointsValue }] = usePointPeakOrValley({
    idc: 'EC06',
    pointGuids: [
      {
        pointCode: '1010101',
        deviceGuid: 'ECO6.A',
        mode: 'max',
      },
    ],
  });

  useEffect(() => {
    fetchPointsValue();
  }, [fetchPointsValue]);

  return (
    <>{`${get(peakOrValleyData, [0, 'pointCode'])} 近1年峰值 :${get(peakOrValleyData, [
      0,
      'value',
    ])}`}</>
  );
};
