import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import type { BackendChartData, PointGuidList } from '@manyun/monitoring.service.fetch-chart-data';
import { useRemoteMock } from '@manyun/service.request';

import { PointsStateLine, PointsStateLineModalButton } from './points-state-line';

export const BasicPointStateLine = () => {
  const [ready, setReady] = React.useState(false);

  React.useEffect(() => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const mockOff = useRemoteMock('web', {
      adapter: 'default',
    });
    setReady(true);

    return () => {
      mockOff();
    };
  }, []);

  if (!ready) {
    return <span>loading</span>;
  }

  return (
    <ConfigProvider>
      <PointsStateLine
        idcTag=""
        pointGuids={[
          {
            deviceGuid: 'EC06.A',
            pointCode: '2005000',
          },
        ]}
        validLimitsMap={{ 0: '关闭', 1: '打开' }}
        seriesOption={[{ name: '开关状态' }]}
      />
    </ConfigProvider>
  );
};

export const AIPointStateLine = () => {
  const [ready, setReady] = React.useState(false);
  const pointGuids = [
    {
      deviceGuid: 'EC06.B',
      pointCode: 'fake-point-code_line-voltage--abc',
      sourcePointCodes: ['1005000', '1005001'],
    },
  ];

  React.useEffect(() => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const mockOff = useRemoteMock('web', {
      adapter: 'default',
    });
    setReady(true);

    return () => {
      mockOff();
    };
  }, []);

  if (!ready) {
    return <span>loading</span>;
  }

  return (
    <ConfigProvider>
      <PointsStateLine
        idcTag=""
        pointGuids={pointGuids}
        validLimitsMap={{ 0: '关闭', 1: '打开' }}
        seriesOption={[{ name: '开关状态' }]}
        reqInterceptor={({ pointGuidList, ...rest }) => {
          const authenticPointGuids = [] as PointGuidList;
          pointGuidList.forEach(({ deviceGuid, sourcePointCodes }) => {
            authenticPointGuids.push(
              ...(sourcePointCodes || []).map(pointCode => ({ deviceGuid, pointCode }))
            );
          });

          return {
            pointGuidList: authenticPointGuids,
            ...rest,
          };
        }}
        respInterceptor={data => {
          if (data.length === 0) {
            return [];
          }
          const resp = data.map(({ time, values }) => ({
            time,
            values: pointGuids.reduce((acc, { deviceGuid, pointCode, sourcePointCodes }) => {
              const sourcePointValues = sourcePointCodes.map(
                sourcePointCode => values[`${deviceGuid}.${sourcePointCode}`]
              );
              const on = sourcePointValues.every(val => val && val > 37);
              const off = sourcePointValues.every(val => val && val <= 37);

              let value = null;
              if (on) {
                value = 1;
              } else if (off) {
                value = 0;
              }

              acc[`${deviceGuid}.${pointCode}`] = value;

              return acc;
            }, {} as BackendChartData['values']),
          }));

          return resp;
        }}
      />
    </ConfigProvider>
  );
};

export const BasicPointStateLineModalButton = () => {
  const [ready, setReady] = React.useState(false);

  React.useEffect(() => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const mockOff = useRemoteMock('web', {
      adapter: 'default',
    });
    setReady(true);

    return () => {
      mockOff();
    };
  }, []);

  if (!ready) {
    return <span>loading</span>;
  }

  return (
    <ConfigProvider>
      <PointsStateLineModalButton
        idcTag=""
        btnText="按钮"
        modalText="弹窗"
        pointGuids={[
          {
            deviceGuid: 'EC06.A',
            pointCode: '2005000',
          },
        ]}
        validLimitsMap={{ 0: '关闭', 1: '打开' }}
        seriesOption={[{ name: '开关状态' }]}
      />
    </ConfigProvider>
  );
};
