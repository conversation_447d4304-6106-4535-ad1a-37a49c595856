---
description: 'A PointsStateLine component.'
labels: ['monitoring', 'chart', 'points-state-line']
---

import { PointsStateLine, PointsStateLineModalButton } from './points-state-line';

## 测点点位数据阶梯折线图：直接图表展示、按钮点击后弹窗展示图表

### Component usage

```js
<PointsStateLine
    pointGuids={[
        {
            deviceGuid: 'EC06.A',
            pointCode: '2005000',
        },
    ]}
    validLimitsMap={{ 0: '关闭', 1: '打开' }}
/>

<PointsStateLine
    pointGuids={[
        {
            deviceGuid: 'EC06.A',
            pointCode: '2005000',
        },
    ]}
    validLimitsMap={{ 0: '关闭', 1: '打开' }}
/>

<PointsStateLineModalButton
    btnText="按钮"
    modalText="弹窗"
    pointGuids={[
        {
            deviceGuid: 'EC06.B',
            pointCode: 'fake-point-code_line-voltage--abc',
            sourcePointCodes: ['1005000', '1005001'],
        },
    ]}
    validLimitsMap={{ 0: '关闭', 1: '打开' }}
    seriesOption={[{ name: '开关状态' }]}
    reqInterceptor={({ pointGuidList, ...rest }) => {
        ...
    }}
    respInterceptor={(data) => {
        ...
    }}
/>
```
