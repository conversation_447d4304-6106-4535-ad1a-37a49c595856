import React, { useCallback, useEffect, useState } from 'react';
import { useDeepCompareEffect, usePrevious } from 'react-use';

import type { XAXisOption, YAXisOption } from 'echarts/types/dist/shared';
import get from 'lodash.get';
import moment from 'moment';
import shallowequal from 'shallowequal';

import { Line } from '@manyun/base-ui.chart.line';
import type { LineProps, Series } from '@manyun/base-ui.chart.line';
import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { Empty } from '@manyun/base-ui.ui.empty';
import { Row } from '@manyun/base-ui.ui.grid';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import type { ModalProps } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import type { TooltipProps } from '@manyun/base-ui.ui.tooltip';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';

import { DurationSelect } from '@manyun/monitoring.chart.duration-select';
import type { IntervalKeyMap, TimeRange } from '@manyun/monitoring.chart.duration-select';
import { useDIPointMarkLines } from '@manyun/monitoring.chart.hook.use-mark-lines';
import type { ValidLimits } from '@manyun/monitoring.chart.hook.use-mark-lines';
import { getXAxisOption } from '@manyun/monitoring.chart.points-line';
import type { PointsLineProps } from '@manyun/monitoring.chart.points-line';
import { PointsLineExport } from '@manyun/monitoring.chart.points-line-export';
import type { ValueMapping } from '@manyun/monitoring.model.point';
import { fetchChartData } from '@manyun/monitoring.service.fetch-chart-data';
import type {
  ReqInterceptor,
  RespInterceptor,
  SeriesData,
} from '@manyun/monitoring.service.fetch-chart-data';
import { AddPointToDiffTool } from '@manyun/monitoring.ui.add-point-to-diff-tool';

type LineChartOption = LineProps['option'];
type Variant = LineProps['variant'];

export type PointGuid = {
  deviceGuid: string;
  pointCode: string;
  sourcePointCodes?: string[];
  valueMapping?: ValueMapping;
  serieName?: string;
};

export type PointsStateLineProps = {
  idcTag: string;
  /* 后续将被废弃 */
  validLimitsMap: ValidLimits;
  reqInterceptor?: ReqInterceptor;
  respInterceptor?: RespInterceptor;
  pointGuids: PointGuid[];
} & Omit<PointsLineProps, 'pointGuids' | 'showSetting'>;

function modifyNothing<T>(anything: T) {
  return anything;
}

export function PointsStateLine({
  echartStyle,
  durationSelectStyle,
  pointGuids,
  variant = 'normal',
  basicOption = {},
  seriesOption,
  chartFunction = 'MAX',
  chartInterval,
  allowInterval,
  defaultIntervalEnabled,
  defaultTimeRange,
  validLimitsMap,
  reqInterceptor = modifyNothing,
  respInterceptor = modifyNothing,
  idcTag,
  startTime,
  endTime,
  interval,
  onCloseModal,
  tooltipFormat = 'YYYY-MM-DD HH:mm:ss',
  showToolbox = true,
  showExport,
  notMerge = false,
}: PointsStateLineProps) {
  const [forceUpdateCount, forceUpdate] = useState(-1);
  const [timeInterval, setTimeInterval] = useState([] as moment.Moment[]);
  const [chartOption, setChartOption] = useState({ series: [] } as LineChartOption);
  const [chartSeriesData, setChartSeriesDataList] = useState([] as SeriesData);
  const prevPointGuids = usePrevious(pointGuids);
  const [{ markLines, seriesData, yAxisCategories }, getMarkLines] = useDIPointMarkLines(
    validLimitsMap,
    pointGuids
  );

  const pointGuidsNotChanged = useDeepCompareMemo(() => {
    return (
      Array.isArray(prevPointGuids) &&
      Array.isArray(pointGuids) &&
      prevPointGuids.length === pointGuids.length &&
      prevPointGuids.every((pointGuid, idx) => shallowequal(pointGuid, pointGuids[idx]))
    );
  }, [prevPointGuids, pointGuids]);

  const memoPointGuids = useDeepCompareMemo(
    () =>
      pointGuids.map(({ deviceGuid, pointCode, sourcePointCodes }) => ({
        deviceGuid,
        pointCode,
        sourcePointCodes,
      })),
    [pointGuids]
  );

  const getChartData = useCallback(
    async (
      momentStartTime: moment.Moment,
      momentEndTime: moment.Moment,
      interval: IntervalKeyMap
    ) => {
      setTimeInterval([momentStartTime, momentEndTime]);
      const req = {
        pointGuidList: memoPointGuids,
        startTime: momentStartTime.valueOf(),
        endTime: momentEndTime.valueOf(),
        function: chartFunction,
        interval: chartInterval ?? interval,
        idcTag,
      };
      const { data, error } = await fetchChartData(req, reqInterceptor, respInterceptor);
      if (error) {
        message.error(error.message);
        return;
      }
      if (data.data.length === 0) {
        return;
      }

      setChartSeriesDataList(data.data);
    },
    [chartFunction, chartInterval, idcTag, memoPointGuids, reqInterceptor, respInterceptor]
  );

  useEffect(() => {
    if (startTime && endTime && interval) {
      getChartData(moment(startTime), moment(endTime), interval);
    }
  }, [startTime, endTime, interval, getChartData]);

  const getT = useCallback(
    async ([momentStartTime, momentEndTime]: TimeRange, interval: IntervalKeyMap) => {
      getChartData(momentStartTime, momentEndTime, interval);
    },
    [getChartData]
  );

  useDeepCompareEffect(() => {
    // 如果 `prevPointGuids` 是 `undefined` 的话，
    // `<DurationSelect />` 的 `onDidMount` 将会被调用，
    //  这种情况下无需 `force update`
    if (prevPointGuids === undefined || pointGuidsNotChanged) {
      return;
    }
    forceUpdate(c => c + 1);
  }, [prevPointGuids, pointGuidsNotChanged]);

  useDeepCompareEffect(() => {
    if (chartSeriesData.length === 0) {
      return;
    }
    getMarkLines(chartSeriesData);
  }, [chartSeriesData, getMarkLines]);

  useDeepCompareEffect(() => {
    if (seriesData.length > 0) {
      setChartOption(prev => {
        const series = seriesData.map((item, index) => {
          return {
            name: get(seriesOption, [index, 'name']) || get(pointGuids, [index, 'serieName']),
            type: 'line',
            step: 'start',
            smooth: true,
            showSymbol: false,
            connectNulls: false,
            markLine: {
              symbol: 'none',
              label: {
                show: false,
              },
            },
            data: item,
            ...get(seriesOption, [index]),
          };
        }) as Series[];

        return {
          ...prev,
          series,
        };
      });
    }
  }, [seriesData, seriesOption, pointGuids]);

  useDeepCompareEffect(() => {
    if (chartSeriesData.length !== 1) {
      return;
    }
    if (markLines.length > 0) {
      setChartOption(prev => {
        const prevFirstSeries = get(prev, ['series', 0]);

        return {
          ...prev,
          series: prevFirstSeries
            ? [{ ...prevFirstSeries, markLine: { ...prevFirstSeries.markLine, data: markLines } }]
            : [],
        };
      });
    }
  }, [markLines, chartSeriesData]);

  if (!pointGuids || pointGuids.length === 0) {
    return <Empty description="缺少必要参数：`pointGuids`" />;
  }

  return (
    <div>
      <Row style={{ alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
        <DurationSelect
          style={durationSelectStyle}
          extraData={forceUpdateCount}
          allowInterval={allowInterval}
          defaultIntervalEnabled={defaultIntervalEnabled}
          defaultValue={defaultTimeRange}
          onChange={getT}
          onDidMount={getT}
        />
        {showExport && (
          <PointsLineExport
            seriesConfigList={
              Array.isArray(seriesOption)
                ? seriesOption.map(({ name }) => ({
                    name: typeof name === 'string' ? name : '',
                  }))
                : []
            }
            seriesDataList={seriesData}
          />
        )}
      </Row>

      {chartSeriesData.length > 0 && chartSeriesData.some(serieData => serieData.length > 0) ? (
        <Line
          style={echartStyle}
          variant={variant}
          option={{
            ...chartOption,
            ...getBaseOption(
              basicOption,
              timeInterval,
              yAxisCategories,
              variant,
              tooltipFormat,
              showToolbox
            ),
          }}
          notMerge={notMerge}
        />
      ) : (
        <Empty />
      )}
    </div>
  );
}

function getBaseOption(
  {
    xAxis = {
      type: 'time',
    },
    tooltip,
    toolbox,
    yAxis,
    grid,
    ...rest
  }: LineChartOption,
  timeInterval: moment.Moment[],
  yAxisCategories: string[],
  variant: Variant,
  tooltipFormat: string,
  showToolbox: boolean
): LineChartOption {
  return {
    ...rest,
    tooltip: {
      trigger: 'axis',
      confine: true,
      axisPointer: {
        axis: 'x',
      },
      timeFormat: tooltipFormat,
      enterable: true,
      extraCssText: 'max-height: 200px; overflow-y: auto',
      ...tooltip,
    },
    toolbox: {
      show: showToolbox,
      feature: { dataZoom: { yAxisIndex: 'none' } },
      top: variant === 'dashboard' ? 15 : undefined,
      ...toolbox,
    },
    yAxis: {
      type: 'category',
      minInterval: 1,
      axisLabel: { interval: 0, formatter: formatYAxisLabel },
      data: yAxisCategories,
    } as YAXisOption,
    xAxis: getXAxisOption(xAxis as XAXisOption, timeInterval),
    grid:
      variant === 'dashboard'
        ? {
            top: showToolbox ? 67 : 40,
            right: 60,
            left: 60,
            ...grid,
          }
        : { top: showToolbox ? 52 : 40, ...grid },
  };
}

function formatYAxisLabel(value: string) {
  if (value) {
    let str = '';
    const num = 4;
    const rowNum = Math.ceil(value.length / num);
    if (rowNum > 1) {
      for (let i = 0; i < rowNum; i++) {
        let temp = '';
        const start = i * num;
        const end = start + num;
        temp = value.substring(start, end) + '\n';
        str += temp;
      }
      return str;
    }
  }
  return value;
}

export type PointsStateLineModalButtonProps = {
  btnStyle?: React.CSSProperties;
  modalStyle?: React.CSSProperties;
  btnText: React.ReactNode;
  modalText: React.ReactNode;
  btnTooltipProps?: TooltipProps;
} & ButtonProps &
  ModalProps &
  PointsStateLineProps;

const defaultModalStyle = { minWidth: (1200 / 1600) * 100 + '%', maxWidth: 1200 };
const defaultButtonStyle = { padding: 0, height: 'auto', lineHeight: 1 };

export function PointsStateLineModalButton({
  echartStyle,
  btnStyle,
  modalStyle,
  variant,
  pointGuids,
  chartFunction,
  chartInterval,
  btnText,
  modalText,
  footer = null,
  validLimitsMap,
  idcTag,
  seriesOption,
  linkTextColorType,
  showExport = true,
  btnTooltipProps,
}: PointsStateLineModalButtonProps) {
  const [visible, setVisible] = useState(false);
  const toggleVisible = (e: { stopPropagation: () => void }) => {
    e.stopPropagation();
    setVisible(!visible);
  };

  const btnRender = (
    <Button
      style={{ ...defaultButtonStyle, ...btnStyle }}
      type="link"
      linkTextColorType={linkTextColorType}
      onClick={e => toggleVisible(e)}
    >
      {btnText}
    </Button>
  );

  return (
    <>
      {btnTooltipProps ? <Tooltip {...btnTooltipProps}>{btnRender}</Tooltip> : btnRender}
      <Modal
        style={{ ...defaultModalStyle, ...modalStyle }}
        open={visible}
        title={
          <Space>
            {modalText}
            <AddPointToDiffTool pointGuids={pointGuids} onCloseModal={() => setVisible(false)} />
          </Space>
        }
        footer={footer}
        destroyOnClose
        onCancel={toggleVisible}
      >
        <PointsStateLine
          echartStyle={echartStyle}
          variant={variant}
          seriesOption={seriesOption}
          idcTag={idcTag}
          pointGuids={pointGuids}
          allowInterval={visible}
          chartFunction={chartFunction}
          chartInterval={chartInterval}
          validLimitsMap={validLimitsMap}
          showExport={showExport}
          onCloseModal={() => setVisible(false)}
        />
      </Modal>
    </>
  );
}
