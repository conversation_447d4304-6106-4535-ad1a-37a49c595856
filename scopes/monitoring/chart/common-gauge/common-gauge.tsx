import React from 'react';

import get from 'lodash.get';

import { Gauge } from '@manyun/base-ui.chart.gauge';
import { ThemeContext } from '@manyun/base-ui.chart.theme';
import { toRgbColorObject } from '@manyun/base-ui.chart.util';
import { Statistic } from '@manyun/base-ui.ui.statistic';
import { Typography } from '@manyun/base-ui.ui.typography';

export type CommonGaugeProps = {
  /**当前值 */
  value: number | string;
  /**当前值的展示 */
  valueDisplay: React.ReactNode;
  title?: string;
  /**历史值，用作数值比较 */
  previousValue?: number;
  color?: string;
  max?: number;
  showRate?: boolean;
  className?: string;
  containerStyle?: React.CSSProperties;
};

export function CommonGauge({
  value,
  title = '当前值',
  previousValue,
  color,
  max = 100,
  showRate = true,
  className,
  containerStyle,
  valueDisplay,
}: CommonGaugeProps) {
  const { json } = React.useContext(ThemeContext);
  const labelTextColor = get(json, ['common', 'labelTextColor']);
  const defaultColor = color ?? get(json, ['color', 0]);
  const { r, g, b } = toRgbColorObject(defaultColor);

  return (
    <div style={containerStyle}>
      <div style={{ position: 'absolute' }} className={className}>
        <Gauge
          style={{ width: '100%', height: '100%' }}
          variant="dashboard"
          option={{
            series: [
              {
                type: 'gauge',
                center: ['50%', '55%'],
                radius: '100%',
                max: max,
                progress: {
                  show: true,
                  width: 6,
                  roundCap: true,
                  itemStyle: {
                    color: defaultColor,
                    shadowColor: `rgba(${r}, ${g}, ${b}, 0.25)`,
                    shadowBlur: 4,
                    shadowOffsetY: 4,
                  },
                },
                axisLine: {
                  roundCap: true,
                  lineStyle: {
                    width: 6,
                    color: [[1, '#DCE6F3']],
                  },
                },
                axisTick: {
                  show: false,
                },
                splitLine: {
                  show: false,
                },
                axisLabel: {
                  show: false,
                },
                anchor: {
                  show: false,
                },
                pointer: {
                  show: false,
                },
                title: {
                  offsetCenter: [0, '20%'],
                  color: labelTextColor,
                  fontSize: 12,
                  lineHeight: 20,
                },
                detail: {
                  show: false,
                },
                data: [
                  {
                    value: typeof value === 'number' ? value : 0,
                    name: title,
                  },
                ],
              },
            ],
          }}
        />
        <div
          style={{
            position: 'absolute',
            bottom: '40%',
            left: '50%',
            transform: 'translateX(-50%)',
          }}
        >
          {valueDisplay}
        </div>
        {showRate && (
          <div
            style={{
              position: 'absolute',
              bottom: 4,
              left: '50%',
              transform: 'translateX(-50%)',
            }}
          >
            {previousValue && typeof value === 'number' ? (
              <Statistic.Trend previousValue={previousValue} currentValue={value} precision={2} />
            ) : (
              <Typography.Text>--</Typography.Text>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
