import React, { useCallback, useMemo } from 'react';
import { useSelector } from 'react-redux';
import { useLatest } from 'react-use';

import dayjs from 'dayjs';
import get from 'lodash.get';
import uniq from 'lodash.uniq';

import { FileExport } from '@manyun/base-ui.ui.file-export';
import type { FileExportProps } from '@manyun/base-ui.ui.file-export';
import type { SheetHeader } from '@manyun/base-ui.util.xlsx';
import { saveJSONAsXLSX } from '@manyun/base-ui.util.xlsx';

import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import type { SeriesData, SeriesExtData } from '@manyun/monitoring.service.fetch-chart-data';

type CSVData = {
  time: number;
  value: (string | number | null)[];
  room: (string | null)[];
  column: (string | null)[];
};

export type PointsLineExportProps = {
  /** 图表系列配置数据，name代表系列名称，showRoom代表展示包间信息，showColumn代表展示机列信息 */
  seriesConfigList: {
    name: string;
    unit?: string | null;
    showRoom?: boolean;
    showColumn?: boolean;
  }[];
  /** 图表系列数据 */
  seriesDataList: SeriesData;
  /** 图表系列额外数据 */
  seriesExtDataList?: SeriesExtData;
  /** 下载文件名称 */
  exportFileName?: string;
  /** 图表导出按时间排序方式 */
  isAscending?: boolean;
  timeFormat?: string;
} & Omit<FileExportProps, 'data' | 'filename' | 'disabled'>;

export function PointsLineExport({
  seriesConfigList,
  seriesDataList,
  seriesExtDataList,
  exportFileName = '数据下载',
  isAscending,
  timeFormat = 'YY-MM-DD HH:mm:ss',
  ...rest
}: PointsLineExportProps) {
  const seriesListRef = useLatest(seriesConfigList);
  const seriesDataListRef = useLatest(seriesDataList ?? []);
  const seriesExtDataRef = useLatest(seriesExtDataList ?? []);
  const maxLengthSeries = seriesDataListRef.current.reduce((result, series) => {
    return result.concat(series.map(([time]) => time));
  }, [] as number[]);
  const sortedSeriesTimes = uniq(maxLengthSeries).sort((a, b) => (isAscending ? a - b : b - a));
  const config = useSelector(selectCurrentConfig);
  const configUtil = useMemo(() => {
    return new ConfigUtil(config);
  }, [config]);
  const columnDeviceType = configUtil.getOneDeviceType(
    ConfigUtil.constants.deviceTypes.SPACE_COLUMN
  );

  const onFileExport = useCallback(() => {
    let columns: SheetHeader[] = [
      {
        title: '时间',
        dataIndex: 'time',
        stringify: (time: number) => (time ? dayjs(time).format(timeFormat) : ''),
      },
    ];
    seriesListRef.current.forEach(({ name, unit, showRoom, showColumn }, index) => {
      columns = [...columns, ...getColumns(index, name, unit, showRoom, showColumn)];
    });
    const contentList = sortedSeriesTimes.map((sortedTime, index) => {
      const pointExportData: CSVData = { time: 0, value: [], room: [], column: [] };
      seriesListRef.current.forEach((_, seriesIndex) => {
        pointExportData.time = sortedTime;
        const current = seriesDataListRef.current[seriesIndex];
        const listIndex = current.findIndex(series => series[0] === sortedTime);
        const list = current[listIndex];
        if (list && list.length === 2) {
          const [time, value] = list;
          const extData = get(seriesExtDataRef.current, [seriesIndex, time]);
          const roomTag = get(extData, ['roomTag']);
          const roomName = get(extData, ['roomName']);
          const name = get(extData, ['name']);
          const deviceType = get(extData, ['deviceType']);
          pointExportData.value[seriesIndex] = value;
          pointExportData.room[seriesIndex] =
            roomTag || roomName ? `${roomTag ?? ''} ${roomName ?? ''}` : null;
          pointExportData.column[seriesIndex] = name
            ? `${name}${deviceType === columnDeviceType ? '列' : ''}`
            : null;
        } else {
          pointExportData.value[seriesIndex] = null;
          pointExportData.room[seriesIndex] = null;
          pointExportData.column[seriesIndex] = null;
        }
      });

      return pointExportData;
    });
    saveJSONAsXLSX([{ data: contentList, headers: columns }], exportFileName);

    return Promise.reject();
  }, [
    seriesListRef,
    sortedSeriesTimes,
    exportFileName,
    timeFormat,
    seriesDataListRef,
    seriesExtDataRef,
    columnDeviceType,
  ]);

  return (
    <FileExport
      {...rest}
      filename={exportFileName}
      disabled={!maxLengthSeries.length}
      data={onFileExport}
    />
  );
}

const getColumns = (
  seriesIndex: number,
  seriesName: string,
  seriesUnit?: string | null,
  showRoom?: boolean,
  showColumn?: boolean
): SheetHeader[] => {
  const index = seriesIndex.toString();
  const defaultColumns = [
    {
      title: '包间',
      dataIndex: ['room', index],
      show: showRoom,
    },
    {
      title: '机列',
      dataIndex: ['column', index],
      show: showColumn,
    },
    {
      title: seriesUnit ? `${seriesName} (${seriesUnit})` : seriesName,
      dataIndex: ['value', index],
      show: true,
    },
  ];

  return defaultColumns.filter(column => column.show);
};
