import React, { useCallback, useContext, useEffect, useRef, useState } from 'react';
import { useDeepCompareEffect, usePrevious, useShallowCompareEffect } from 'react-use';

import { SettingOutlined } from '@ant-design/icons';
import type { XAXisOption } from 'echarts/types/dist/shared';
import get from 'lodash.get';
import merge from 'lodash.merge';
import moment from 'moment';
import shallowequal from 'shallowequal';

import { Line } from '@manyun/base-ui.chart.line';
import type { LineProps, Series } from '@manyun/base-ui.chart.line';
import { ThemeContext } from '@manyun/base-ui.chart.theme';
import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { Dropdown } from '@manyun/base-ui.ui.dropdown';
import { Empty } from '@manyun/base-ui.ui.empty';
import { Row } from '@manyun/base-ui.ui.grid';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import type { ModalProps } from '@manyun/base-ui.ui.modal';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Space } from '@manyun/base-ui.ui.space';
import { Switch } from '@manyun/base-ui.ui.switch';
import type { TooltipProps } from '@manyun/base-ui.ui.tooltip';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';

import { DurationSelect } from '@manyun/monitoring.chart.duration-select';
import type { IntervalKeyMap, TimeRange } from '@manyun/monitoring.chart.duration-select';
import { useAIPointMarkLines } from '@manyun/monitoring.chart.hook.use-mark-lines';
import { PointsLineExport } from '@manyun/monitoring.chart.points-line-export';
import { fetchChartData } from '@manyun/monitoring.service.fetch-chart-data';
import type {
  ChartFunction,
  Interval,
  SeriesData,
} from '@manyun/monitoring.service.fetch-chart-data';
import { AddPointToDiffTool } from '@manyun/monitoring.ui.add-point-to-diff-tool';
import { useLazyPointsByPointGuids } from '@manyun/resource-hub.hook.use-points';

import { getXAxisOption } from './points-line-utils';

type LineChartOption = LineProps['option'];
type BasicOption = Omit<LineChartOption, 'series'>;
export type Variant = LineProps['variant'];
export type PointGuid = {
  deviceGuid: string;
  pointCode: string;
  deviceType?: string;
  spaceGuid?: string;
  serieName?: string;
  unit?: string | null;
};
export type SeriesOption = Series[];
export type PointsLineProps = {
  durationSelectStyle?: React.CSSProperties;
  echartStyle?: React.CSSProperties;
  pointGuids: PointGuid[];
  variant?: Variant;
  basicOption?: BasicOption;
  seriesOption?: SeriesOption;
  /** 配置获取图表数据接口参数 */
  chartFunction?: ChartFunction;
  chartInterval?: Interval;
  /** 配置DurationSelect */
  allowInterval?: boolean;
  defaultIntervalEnabled?: boolean;
  defaultTimeRange?: TimeRange;
  idcTag: string;
  startTime?: number;
  endTime?: number;
  interval?: IntervalKeyMap;
  onCloseModal?: () => void;
  tooltipFormat?: string;
  showToolbox?: boolean;
  /**是否展示下载按钮 */
  showExport?: boolean;
  /**是否展示设置按钮，展示时默认开启峰谷值展示 */
  showSetting?: boolean;
  /**图表配置项是否不跟之前设置的 option 进行合并。默认为 false。即表示合并 */
  notMerge?: LineProps['notMerge'];
};

const chartFunctionMapper: Record<ChartFunction, string> = {
  AVG: '按区间均值聚合',
  MAX: '按区间最大值聚合',
  MIN: '按区间最小值聚合',
};

export function PointsLine({
  echartStyle,
  durationSelectStyle,
  variant = 'normal',
  pointGuids,
  basicOption = {},
  seriesOption,
  chartFunction = 'AVG',
  chartInterval,
  allowInterval,
  defaultIntervalEnabled,
  defaultTimeRange,
  idcTag,
  startTime,
  endTime,
  interval,
  onCloseModal,
  tooltipFormat = 'YYYY-MM-DD HH:mm:ss',
  showToolbox = true,
  showExport,
  showSetting,
  notMerge = false,
}: PointsLineProps) {
  const [forceUpdateCount, forceUpdate] = useState(-1);
  const [timeInterval, setTimeInterval] = useState([] as moment.Moment[]);
  const [chartOption, setChartOption] = useState({ series: [] } as LineChartOption);
  const [seriesData, setSeriesDataList] = useState([] as SeriesData);
  const [markerEnabled, setMarkerEnabled] = useState(showSetting);
  const prevPointGuids = usePrevious(pointGuids);
  const [{ markLines }, getMarkLines] = useAIPointMarkLines(pointGuids?.[0]);
  const [getPoints, { points }] = useLazyPointsByPointGuids();
  const shouldGetPointsConfig = useDeepCompareMemo(() => {
    if (pointGuids.length) {
      if (pointGuids.some(point => point.unit)) {
        return false;
      }
      return pointGuids.every(point => point.deviceType);
    }
    return false;
  }, [pointGuids]);
  const seriesUnits = useDeepCompareMemo(() => {
    return shouldGetPointsConfig
      ? pointGuids.map(({ pointCode, deviceType }) => {
          const point = points.find(p => p.code === pointCode && p.deviceType === deviceType);

          return point?.unit ?? undefined;
        })
      : pointGuids.map(point => point.unit ?? undefined);
  }, [shouldGetPointsConfig, points, pointGuids]);
  const serieNames = useDeepCompareMemo<string[]>(
    () =>
      pointGuids.map(
        (_, index) =>
          (get(seriesOption, [index, 'name']) || get(pointGuids, [index, 'serieName'])) as string
      ),
    [seriesOption, pointGuids]
  );
  const isSeriesUpdated = useRef(false);
  const { json } = useContext(ThemeContext);
  const borderColor = get(json, ['markPoint', 'label', 'color']);

  const [selectedFunction, setSelectedFunction] = useState(chartFunction);

  const pointGuidsNotChanged = useDeepCompareMemo(() => {
    return (
      Array.isArray(prevPointGuids) &&
      Array.isArray(pointGuids) &&
      prevPointGuids.length === pointGuids.length &&
      prevPointGuids.every((pointGuid, idx) => shallowequal(pointGuid, pointGuids[idx]))
    );
  }, [prevPointGuids, pointGuids]);
  const memoSeriesOption = useDeepCompareMemo(() => seriesOption, [seriesOption]);
  const memoPointGuids = useDeepCompareMemo(
    () =>
      pointGuids.map(({ deviceGuid, pointCode }) => ({
        deviceGuid,
        pointCode,
      })),
    [pointGuids]
  );

  const getChartData = useCallback(
    async (
      momentStartTime: moment.Moment,
      momentEndTime: moment.Moment,
      interval: IntervalKeyMap
    ) => {
      setTimeInterval([momentStartTime, momentEndTime]);
      isSeriesUpdated.current = false;
      const { data, error } = await fetchChartData({
        pointGuidList: memoPointGuids,
        startTime: momentStartTime.valueOf(),
        endTime: momentEndTime.valueOf(),
        function: selectedFunction,
        interval: chartInterval ?? interval,
        idcTag,
      });
      if (error) {
        message.error(error.message);
        return;
      }
      if (data.data.length === 0) {
        return;
      }
      setSeriesDataList(data.data);

      setChartOption(prev => {
        const series = data.data.map((item, index) => {
          return {
            name: serieNames[index],
            type: 'line',
            smooth: true,
            showSymbol: false,
            markLine: {
              symbol: 'none',
              label: {
                show: true,
                position: 'insideStartTop',
                formatter: '{b}({c})',
              },
            },
            data: item,
            unit: get(seriesUnits, [index]),
            ...get(memoSeriesOption, [index]),
          };
        });
        return {
          ...prev,
          series,
        } as LineChartOption;
      });
      isSeriesUpdated.current = true;
    },
    [
      selectedFunction,
      chartInterval,
      memoPointGuids,
      memoSeriesOption,
      idcTag,
      seriesUnits,
      serieNames,
    ]
  );

  useEffect(() => {
    if (startTime && endTime && interval) {
      getChartData(moment(startTime), moment(endTime), interval);
    }
  }, [startTime, endTime, interval, getChartData]);

  const getT = useCallback(
    ([momentStartTime, momentEndTime]: TimeRange, interval: IntervalKeyMap) => {
      if (startTime && endTime && interval) {
        return;
      }
      getChartData(momentStartTime, momentEndTime, interval);
    },
    [startTime, endTime, getChartData]
  );

  useDeepCompareEffect(() => {
    // 如果 `prevPointGuids` 是 `undefined` 的话，
    // `<DurationSelect />` 的 `onDidMount` 将会被调用，
    //  这种情况下无需 `force update`
    if (prevPointGuids === undefined || pointGuidsNotChanged) {
      return;
    }
    forceUpdate(c => c + 1);
  }, [prevPointGuids, pointGuidsNotChanged]);

  useDeepCompareEffect(() => {
    if (pointGuidsNotChanged || pointGuids.length !== 1) {
      return;
    }
    getMarkLines();
  }, [pointGuidsNotChanged, pointGuids, getMarkLines]);

  useShallowCompareEffect(() => {
    if (markLines.length > 0) {
      setChartOption(prev => {
        const prevFirstSeries = get(prev.series, ['0']);
        return {
          ...prev,
          series: [
            {
              ...prevFirstSeries,
              markLine: { ...get(prevFirstSeries, ['markLine']), data: markLines },
            },
          ],
        };
      });
    }
  }, [markLines]);

  useDeepCompareEffect(() => {
    if (shouldGetPointsConfig) {
      getPoints(
        pointGuids.map(({ deviceType, pointCode }) => ({ deviceType: deviceType!, pointCode }))
      );
    }
  }, [getPoints, pointGuids, shouldGetPointsConfig]);

  useShallowCompareEffect(() => {
    isSeriesUpdated.current &&
      setChartOption(prev => {
        const prevSeries = get(prev, ['series'], []);

        return {
          ...prev,
          series: prevSeries.map((series, index) => ({
            ...series,
            unit: get(seriesUnits, [index]),
          })),
        };
      });
  }, [seriesUnits, isSeriesUpdated.current]);

  useShallowCompareEffect(() => {
    showSetting &&
      isSeriesUpdated.current &&
      setChartOption(prev => {
        const prevSeries = get(prev, ['series'], []);

        return {
          ...prev,
          series: prevSeries.map(series => {
            const prevMarkPoint = get(series, ['markPoint'], {});
            const prevMarkLine = get(series, ['markLine'], {});
            const prevMarkLineData: { type: string; name: string }[] = get(
              prevMarkLine,
              ['data'],
              []
            );
            const currentMarkLineData = markerEnabled
              ? [...prevMarkLineData, { type: 'average', name: '均值' }]
              : prevMarkLineData.filter(data => data.type !== 'average');

            return {
              ...series,
              markLine: {
                symbol: 'none',
                label: {
                  show: true,
                  position: 'insideStartTop',
                  formatter: '{b}({c})',
                  color: 'inherit',
                  textBorderColor: borderColor,
                  textBorderWidth: 4,
                },
                data: currentMarkLineData,
              },
              markPoint: {
                ...prevMarkPoint,
                symbolSize: 8,
                symbol: 'emptyCircle',
                label: {
                  formatter: '{b}: {c}',
                  color: 'inherit',
                  textBorderColor: borderColor,
                  textBorderWidth: 4,
                },
                emphasis: {
                  label: {
                    color: 'inherit',
                  },
                },
                data: markerEnabled
                  ? [
                      { type: 'max', name: '最大值', label: { offset: [0, -16] } },
                      { type: 'min', name: '最小值', label: { offset: [0, -16] } },
                    ]
                  : [],
              },
            };
          }),
        } as LineChartOption;
      });
  }, [markerEnabled, isSeriesUpdated.current, showSetting, borderColor]);

  if (!pointGuids || pointGuids.length === 0) {
    return <Empty description="缺少必要参数：`pointGuids`" />;
  }

  return (
    <div>
      <Row style={{ alignItems: 'center' }}>
        <Row style={{ justifyContent: 'space-between', width: '100%' }}>
          <DurationSelect
            style={durationSelectStyle}
            extraData={forceUpdateCount}
            allowInterval={allowInterval}
            defaultIntervalEnabled={defaultIntervalEnabled}
            defaultValue={defaultTimeRange}
            onChange={getT}
            onDidMount={getT}
          />
          <Space>
            {showSetting && (
              <Dropdown
                overlayStyle={{ width: 210 }}
                menu={{
                  items: [
                    {
                      key: 'chartFunction',
                      label: (
                        <Space style={{ width: '100%' }} direction="vertical">
                          <Typography.Text>数据聚合方式</Typography.Text>
                          <Radio.Group
                            value={selectedFunction}
                            onChange={({ target }) => {
                              setSelectedFunction(target.value);
                              forceUpdate(c => c + 1);
                            }}
                          >
                            <Space direction="vertical">
                              {Object.keys(chartFunctionMapper).map(chartFunction => (
                                <Radio key={chartFunction} value={chartFunction}>
                                  {chartFunctionMapper[chartFunction as ChartFunction]}
                                </Radio>
                              ))}
                            </Space>
                          </Radio.Group>
                        </Space>
                      ),
                    },
                    {
                      key: 'peakOrValley',
                      label: (
                        <Row justify="space-between">
                          <Typography.Text>显示曲线峰谷值</Typography.Text>
                          <Switch checked={markerEnabled} onChange={setMarkerEnabled} />
                        </Row>
                      ),
                    },
                  ],
                }}
              >
                <Button icon={<SettingOutlined />} />
              </Dropdown>
            )}
            {showExport && (
              <PointsLineExport
                text=""
                type="default"
                compact={false}
                seriesConfigList={serieNames.map((name, index) => ({
                  name,
                  unit: seriesUnits[index],
                }))}
                seriesDataList={seriesData}
              />
            )}
          </Space>
        </Row>
      </Row>
      {seriesData.length > 0 && seriesData.some(serieData => serieData.length > 0) ? (
        <Line
          style={echartStyle}
          variant={variant}
          option={merge(
            chartOption,
            getBaseOption(basicOption, timeInterval, variant, tooltipFormat, showToolbox)
          )}
          notMerge={notMerge}
        />
      ) : (
        <Empty />
      )}
    </div>
  );
}

function getBaseOption(
  {
    xAxis = {
      type: 'time',
    },
    tooltip,
    toolbox,
    grid,
    ...rest
  }: LineChartOption,
  timeInterval: moment.Moment[],
  variant: Variant,
  tooltipFormat: string,
  showToolbox: boolean
): LineChartOption {
  return {
    ...rest,
    tooltip: {
      trigger: 'axis',
      confine: true,
      timeFormat: tooltipFormat,
      enterable: true,
      extraCssText: 'max-height: 200px; overflow-y: auto',
      ...tooltip,
    },
    toolbox: {
      show: showToolbox,
      feature: {
        dataZoom: { yAxisIndex: 'none' },
      },
      top: variant === 'dashboard' ? 15 : undefined,
      ...toolbox,
    },
    xAxis: getXAxisOption(xAxis as XAXisOption, timeInterval),
    grid:
      variant === 'dashboard'
        ? {
            top: showToolbox ? 67 : 40,
            right: 60,
            left: 60,
            ...grid,
          }
        : { top: showToolbox ? 52 : 40, right: 8, ...grid },
  };
}

export type PointsLineModalButtonProps = {
  btnStyle?: React.CSSProperties;
  modalStyle?: React.CSSProperties;
  btnText: React.ReactNode;
  modalText: React.ReactNode;
  btnTooltipProps?: TooltipProps;
} & ButtonProps &
  ModalProps &
  PointsLineProps;

const defaultModalStyle = { minWidth: (1200 / 1600) * 100 + '%', maxWidth: 1200 };

export function PointsLineModalButton({
  echartStyle,
  btnStyle,
  modalStyle,
  variant,
  pointGuids,
  chartFunction,
  chartInterval,
  btnText,
  modalText,
  footer = null,
  idcTag,
  seriesOption,
  linkTextColorType,
  showExport = true,
  showSetting = true,
  btnTooltipProps,
}: PointsLineModalButtonProps) {
  const [visible, setVisible] = useState(false);
  const toggleVisible = (e: { stopPropagation: () => void }) => {
    e.stopPropagation();
    setVisible(!visible);
  };

  const btnRender = (
    <Button
      style={btnStyle}
      compact
      type="link"
      linkTextColorType={linkTextColorType}
      onClick={e => toggleVisible(e)}
    >
      {btnText}
    </Button>
  );

  return (
    <>
      {btnTooltipProps ? <Tooltip {...btnTooltipProps}>{btnRender}</Tooltip> : btnRender}
      <Modal
        style={{ ...defaultModalStyle, ...modalStyle }}
        open={visible}
        title={
          <Space>
            {modalText}
            <AddPointToDiffTool pointGuids={pointGuids} onCloseModal={() => setVisible(false)} />
          </Space>
        }
        footer={footer}
        destroyOnClose
        onCancel={toggleVisible}
      >
        <PointsLine
          echartStyle={echartStyle}
          variant={variant}
          seriesOption={seriesOption}
          idcTag={idcTag}
          pointGuids={pointGuids}
          allowInterval={visible}
          chartFunction={chartFunction}
          chartInterval={chartInterval}
          showExport={showExport}
          showSetting={showSetting}
          onCloseModal={() => setVisible(false)}
        />
      </Modal>
    </>
  );
}
