---
description: 'A PointsLine component.'
labels: ['monitoring', 'chart', 'points-line']
---

import { PointsLine } from './points-line';

## 测点点位数据折线图：直接图表展示、按钮点击后弹窗展示图表

### Component usage

```js
<PointsLine
    pointGuids={[
        {
        deviceGuid: 'EC06.A',
        pointCode: '1005000',
        deviceType: '10101',
        },
    ]}
    seriesOption={[{ name: '电流1' }]}
/>

<PointsLine
    pointGuids={[
        {
        deviceGuid: 'EC06.B',
        pointCode: '1005000',
        deviceType: '10101',
        },
        {
        deviceGuid: 'EC06.B',
        pointCode: '1005001',
        deviceType: '10101',
        },
    ]}
    seriesOption={[{ name: '电流1' }, { name: '电流2' }]}
/>

<PointsLineModalButton
    btnText="按钮"
    modalText="弹窗"
    pointGuids={[
        {
        deviceGuid: 'EC06.A',
        pointCode: '1005000',
        deviceType: '10101',
        },
    ]}
    seriesOption={[{ name: '电流1' }]}
/>
```
