import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { useRemoteMock } from '@manyun/service.request';

import { PointsLine, PointsLineModalButton } from './points-line';

export const BasicPointLine = () => {
  const [ready, setReady] = React.useState(false);

  React.useEffect(() => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const mockOff = useRemoteMock('web', {
      adapter: 'default',
    });
    setReady(true);

    return () => {
      mockOff();
    };
  }, []);

  if (!ready) {
    return <span>loading</span>;
  }

  return (
    <ConfigProvider>
      <PointsLine
        idcTag=""
        pointGuids={[
          {
            deviceGuid: 'EC06.A',
            pointCode: '1005000',
            deviceType: '10101',
            spaceGuid: 'EC06.A',
          },
        ]}
        seriesOption={[{ name: '电流1' }]}
      />
    </ConfigProvider>
  );
};

export const BasicPointsLine = () => {
  const [ready, setReady] = React.useState(false);

  React.useEffect(() => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const mockOff = useRemoteMock('web', {
      adapter: 'default',
    });
    setReady(true);

    return () => {
      mockOff();
    };
  }, []);

  if (!ready) {
    return <span>loading</span>;
  }

  return (
    <ConfigProvider>
      <PointsLine
        idcTag=""
        pointGuids={[
          {
            deviceGuid: 'EC06.B',
            pointCode: '1005000',
            deviceType: '10101',
          },
          {
            deviceGuid: 'EC06.B',
            pointCode: '1005001',
            deviceType: '10101',
          },
        ]}
        seriesOption={[{ name: '电流1' }, { name: '电流2' }]}
      />
    </ConfigProvider>
  );
};

export const BasicPointLineModalButton = () => {
  const [ready, setReady] = React.useState(false);

  React.useEffect(() => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const mockOff = useRemoteMock('web', {
      adapter: 'default',
    });
    setReady(true);

    return () => {
      mockOff();
    };
  }, []);

  if (!ready) {
    return <span>loading</span>;
  }

  return (
    <ConfigProvider>
      <PointsLineModalButton
        idcTag=""
        btnText="按钮"
        modalText="弹窗"
        pointGuids={[
          {
            deviceGuid: 'EC06.A',
            pointCode: '1005000',
            deviceType: '10101',
            spaceGuid: 'EC06.A',
          },
        ]}
        seriesOption={[{ name: '电流1' }]}
      />
    </ConfigProvider>
  );
};
