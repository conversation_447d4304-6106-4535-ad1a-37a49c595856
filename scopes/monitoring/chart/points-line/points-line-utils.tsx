import type { XAXisOption } from 'echarts/types/dist/shared';
import get from 'lodash.get';
import moment from 'moment';

import { getAxisLabel } from '@manyun/base-ui.chart.util';

export const getTimeMask = (diffMinutes: number) => {
  let timeMask = 'YYYY-MM';
  if (diffMinutes < 60) {
    timeMask = 'HH:mm:ss';
  } else if (diffMinutes < 60 * 24) {
    timeMask = 'HH:mm';
  } else if (diffMinutes < 60 * 24 * 2) {
    timeMask = 'MM-DD HH:mm';
  } else if (diffMinutes < 60 * 24 * 12) {
    timeMask = 'MM-DD';
  }

  return timeMask;
};

export const getXAxisOption = (xAxis: XAXisOption, timeInterval: moment.Moment[]) => {
  let timeDiff = 0;
  if (Array.isArray(timeInterval) && timeInterval.length === 2) {
    timeDiff = moment(timeInterval[1]).diff(moment(timeInterval[0]), 'minutes');
  }
  const timeMask = getTimeMask(timeDiff);

  return (
    Array.isArray(xAxis)
      ? xAxis.map(x => ({
          ...x,
          axisLabel: { ...getAxisLabel(xAxis, timeMask), ...get(x, ['axisLabel'], {}) },
        }))
      : {
          ...xAxis,
          axisLabel: { ...getAxisLabel(xAxis, timeMask), ...get(xAxis, ['axisLabel'], {}) },
        }
  ) as XAXisOption;
};
