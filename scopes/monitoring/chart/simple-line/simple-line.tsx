import React, { useContext, useMemo } from 'react';
import { useSelector } from 'react-redux';

import dayjs from 'dayjs';
import * as echarts from 'echarts/core';
import type { XAXisOption, YAXisOption } from 'echarts/types/dist/shared';
import get from 'lodash.get';

import { Line } from '@manyun/base-ui.chart.line';
import type { LineProps, Series } from '@manyun/base-ui.chart.line';
import { ThemeContext } from '@manyun/base-ui.chart.theme';
import { toRgbColorObject } from '@manyun/base-ui.chart.util';
import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';

import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { getXAxisOption } from '@manyun/monitoring.chart.points-line';
import type {
  ExtType,
  SeriesData,
  SeriesExtData,
} from '@manyun/monitoring.service.fetch-chart-data';

type BasicOption = Omit<LineProps['option'], 'series'>;

export type CommonLineProps = {
  seriesData: SeriesData;
  seriesExtData?: SeriesExtData;
  chartStyle?: React.CSSProperties;
  variant?: LineProps['variant'];
  seriesUnits?: (string | undefined | null)[];
  tooltipLabels?: ExtType[];
  chartOptions?: BasicOption;
};

export type SimpleLineProps = CommonLineProps & {
  markLineValue?: number;
  lineColor?: string;
  timeFormatter?: string;
};

export function SimpleLine({
  seriesData,
  seriesExtData,
  markLineValue,
  lineColor,
  chartStyle,
  variant,
  seriesUnits,
  tooltipLabels,
  timeFormatter = 'YY-MM-DD HH:mm:ss',
}: SimpleLineProps) {
  const { json } = React.useContext(ThemeContext);
  const colors = get(json, ['color']);
  const defaultColor = lineColor ?? colors[0];
  const { r, g, b } = toRgbColorObject(defaultColor);
  const config = useSelector(selectCurrentConfig);
  const configUtil = useMemo(() => {
    return new ConfigUtil(config);
  }, [config]);
  const columnDeviceType = configUtil.getOneDeviceType(
    ConfigUtil.constants.deviceTypes.SPACE_COLUMN
  );

  return (
    <Line
      style={{ width: '100%', ...chartStyle }}
      variant={variant}
      option={{
        grid: {
          top: 4,
          left: 4,
          right: 4,
          bottom: 4,
        },
        legend: {
          show: false,
        },
        xAxis: {
          show: false,
          type: 'time',
        } as XAXisOption,
        yAxis: {
          show: false,
        },
        series: seriesData.map(series => {
          return {
            animation: false,
            data: series as [number, string | number][],
            smooth: true,
            color: defaultColor,
            markLine: {
              symbol: 'none',
              animation: false,
              label: {
                show: false,
              },
              data:
                typeof markLineValue === 'number'
                  ? [
                      {
                        yAxis: markLineValue,
                        lineStyle: {
                          color: colors[5],
                        },
                      },
                    ]
                  : [],
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: `rgba(${r}, ${g}, ${b}, 0.24)` },
                { offset: 1, color: `rgba(${r}, ${g}, ${b}, 0)` },
              ]),
            },
          };
        }),
        tooltip: {
          formatter: function (params) {
            const [time, value] = get(params, [0, 'value']);
            const seriesIndex = get(params, [0, 'seriesIndex']);
            const extData = get(seriesExtData, [seriesIndex, time]);
            const timeFormat = dayjs(time).format(timeFormatter);
            let tooltipText = `${value ?? '无数据'} ${get(seriesUnits, [0]) ?? ''}`;
            if (tooltipLabels) {
              if (tooltipLabels.find(label => label === 'name')) {
                const nameText = get(extData, ['name']);
                tooltipText = `${nameText ?? '--'}${
                  nameText && get(extData, ['deviceType']) === columnDeviceType ? '列' : ''
                }${nameText ? ': ' : ''}${tooltipText}`;
              }
              if (tooltipLabels.find(label => label === 'roomName')) {
                tooltipText = `${get(extData, ['roomName']) ?? '--'} ${tooltipText}`;
              }
              if (tooltipLabels.find(label => label === 'roomTag')) {
                tooltipText = `${get(extData, ['roomTag']) ?? '--'} ${tooltipText}`;
              }
            }

            return `${timeFormat} ${tooltipText}`;
          },
        },
      }}
    />
  );
}

export type DefaultLineProps = CommonLineProps & {
  seriesNames: string[];
  defaultColors?: string[];
  markLineData?: { name: string; value: number }[];
  timeInterval?: moment.Moment[];
  yAXisOption?: YAXisOption | YAXisOption[];
};

export function DefaultLine({
  seriesData,
  chartStyle,
  variant,
  defaultColors,
  seriesNames,
  markLineData = [],
  seriesUnits,
  timeInterval = [],
  yAXisOption,
  chartOptions,
}: DefaultLineProps) {
  const { json } = useContext(ThemeContext);
  const memoSeriesData = useDeepCompareMemo(() => {
    const colors = defaultColors ?? get(json, ['color']);
    const labelTextColor = get(json, ['common', 'labelTextColor']);
    const valueTextColor = get(json, ['common', 'valueTextColor']);

    return [
      ...seriesData.map((series, index) => {
        return {
          data: series,
          smooth: true,
          name: seriesNames[index],
          color: colors[index],
          unit: get(seriesUnits, [index]),
        };
      }),
      {
        type: 'line',
        unit: get(seriesUnits, [0]),
        markLine: {
          symbol: 'none',
          animation: false,
          label: {
            show: false,
            position: 'insideEndTop',
          },
          emphasis: {
            label: {
              show: true,
              color: labelTextColor,
              rich: {
                val: {
                  fontSize: 12,
                  lineHeight: 20,
                  color: valueTextColor,
                },
              },
              formatter: (params: { name: string; value: string }) =>
                `${params.name}  {val|${params.value}}`,
            },
          },
          data: markLineData.length
            ? markLineData.map(({ name, value }, index) => ({
                name,
                yAxis: value,
                lineStyle: {
                  color: index === 0 ? get(json, ['color', 6]) : get(json, ['color', 8]),
                },
              }))
            : [],
        },
      },
    ] as Series[];
  }, [seriesData, seriesNames, seriesUnits, markLineData, json, defaultColors]);

  const memoChartOption = useDeepCompareMemo(
    () => ({
      xAxis: getXAxisOption(
        {
          type: 'time',
        },
        timeInterval
      ),
      yAxis: yAXisOption,
      grid: { top: 40, right: 36, left: 36, bottom: 20 },
      ...chartOptions,
    }),
    [timeInterval, yAXisOption, chartOptions]
  );

  return (
    <Line
      style={{ width: '100%', ...chartStyle }}
      variant={variant}
      option={{
        ...memoChartOption,
        series: memoSeriesData,
      }}
    />
  );
}
