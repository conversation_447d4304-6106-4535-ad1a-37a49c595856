import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import get from 'lodash.get';

import { Pie } from '@manyun/base-ui.chart.pie';

import type { Config } from '@manyun/dc-brain.config.base';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { PointsLineModalButton } from '@manyun/monitoring.chart.points-line';
import type { PointGuid, SeriesOption } from '@manyun/monitoring.chart.points-line';
import {
  getMonitoringData,
  addRealtimeNAlarmsDataSubscriptionActionCreator as subscribe,
  removeRealtimeNAlarmsDataSubscriptionActionCreator as unsubscribe,
} from '@manyun/monitoring.state.subscriptions';

import {
  SUBSCRIPTIONS_MODE, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.constants/subscriptions';
import {
  getCurrentConfig, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/selectors/configSelectors';
import {
  getBlocks, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/selectors/idcWorkbenchSelectors';
import {
  generateGetDeviceMonitoringData, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.utils/device';

export type PuePieProps = {
  idc: string;
};

const data = [
  { value: 0, name: 'IT占比', type: 'IT' },
  { value: 0, name: '设施占比', type: 'device' },
];
const BLANK_PLACEHOLDER = '--';

export function PuePie({ idc }: PuePieProps) {
  const dispatch = useDispatch();
  const [pointGuids, setPointGuids] = React.useState([] as PointGuid[]);
  const [seriesOption, setSeriesOption] = React.useState([] as SeriesOption);
  const blocks = useSelector(getBlocks) as string[];
  const devicesData = useSelector(getMonitoringData) as { devicesRealtimeData: any };
  const config = useSelector(getCurrentConfig) as Config;
  const configUtil = React.useMemo(() => {
    return new ConfigUtil(config);
  }, [config]);
  const totalPowerPointCode = React.useRef('');
  const idcPuePointCode = React.useRef('');
  const ITPowerPointCode = React.useRef('');
  const pue = React.useRef<string | number>('');
  const moduleId = 'idc-workbench_pue';
  const mode = SUBSCRIPTIONS_MODE.REALTIME_N_ALARMS;
  const targets = React.useMemo(() => {
    if (!idc) {
      return null;
    }

    return [
      {
        mode,
        blockGuid: idc,
        moduleId,
        deviceGuids: [idc],
      },
    ];
  }, [idc, mode]);

  React.useEffect(() => {
    if (targets) {
      // @ts-ignore
      dispatch(subscribe({ targets }));
    }
    return () => {
      if (targets) {
        // @ts-ignore
        dispatch(unsubscribe({ mode, moduleId }));
      }
    };
  }, [targets, mode, dispatch]);

  React.useEffect(() => {
    const idcDeviceType = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_IDC);
    const blockDeviceType = configUtil.getOneDeviceType(
      ConfigUtil.constants.deviceTypes.SPACE_BLOCK
    );
    idcPuePointCode.current = configUtil.getPointCode(
      idcDeviceType,
      ConfigUtil.constants.pointCodes.PUE
    );
    totalPowerPointCode.current = configUtil.getPointCode(
      idcDeviceType,
      ConfigUtil.constants.pointCodes.ACTIVE_POWER_SUM
    );
    ITPowerPointCode.current = configUtil.getPointCode(
      idcDeviceType,
      ConfigUtil.constants.pointCodes.ACTIVE_IT_POWER_SUM
    );
    const pointGuids = [
      {
        deviceGuid: idc,
        pointCode: idcPuePointCode.current,
      },
    ];
    const seriesOption = [{ name: idc }];
    blocks.forEach((item, index) => {
      let spaceGuid = `${idc}.${item}`;
      pointGuids.push({
        deviceGuid: spaceGuid,
        pointCode: configUtil.getPointCode(blockDeviceType, ConfigUtil.constants.pointCodes.PUE, {
          spaceGuid,
        }),
      });
      seriesOption.push({ name: spaceGuid });
    });
    setPointGuids(pointGuids);
    setSeriesOption(seriesOption);
  }, [blocks, idc, configUtil]);

  React.useEffect(() => {
    const { concretePointCodeMappings, pointsDefinitionMap } = config;
    const idcDevice = {
      deviceGuid: idc,
      deviceType: configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_IDC),
    };
    const getData = generateGetDeviceMonitoringData(
      devicesData.devicesRealtimeData,
      undefined,
      pointsDefinitionMap,
      concretePointCodeMappings
    );
    const totalPower = getData(idcDevice, { hardCodedPointCode: totalPowerPointCode.current });
    const itPower = getData(idcDevice, { hardCodedPointCode: ITPowerPointCode.current });
    pue.current = get(getData(idcDevice, { hardCodedPointCode: idcPuePointCode.current }), [
      'value',
    ]) as string | number;
    if (typeof totalPower.value === 'number' && typeof itPower.value === 'number') {
      data[0].value = itPower.value;
      data[1].value = totalPower.value - itPower.value;
    } else {
      data.forEach(item => (item.value = 0));
    }
  }, [idc, config, configUtil, devicesData]);

  return (
    <div style={{ position: 'absolute', top: 42, width: '100%', zIndex: 0 }}>
      <Pie
        style={{ height: 138 }}
        onEvents={{
          mousemove: (param: any, echarts: any) => {
            echarts.getZr().setCursorStyle('default');
          },
        }}
        option={{
          backgroundColor: '',
          legend: {
            orient: 'vertical',
            top: '40%',
            left: '16%',
            itemWidth: 6,
            icon: 'circle',
            formatter(name) {
              let count = 0;
              data.forEach(item => {
                count = count + item.value;
              });
              const val = data.filter(item => item.name === name);
              return `${name} ${
                isNaN(val[0].value) || isNaN(count) || count === 0
                  ? BLANK_PLACEHOLDER
                  : Number((val[0].value / count) * 100).toFixed(2)
              } %`;
            },
          },
          series: [
            {
              type: 'pie',
              radius: ['65%', '90%'],
              center: ['75%', '55%'],
              animation: false,
              avoidLabelOverlap: false,
              legendHoverLink: false,
              emphasis: {
                scale: false,
              },
              label: {
                formatter: '{b|\n\n实时PUE}',
                position: 'center',
                rich: {
                  b: {
                    fontSize: 12,
                  },
                },
              },
              data: data,
            },
          ],
        }}
      />
      {pue.current && (
        <div
          style={{
            position: 'absolute',
            top: 55,
            left: '75%',
            transform: 'translateX(-50%)',
            fontSize: 20,
          }}
        >
          <PointsLineModalButton
            idcTag={idc}
            btnText={pue.current}
            modalText="PUE"
            pointGuids={pointGuids}
            seriesOption={seriesOption}
            btnStyle={{ fontSize: 20 }}
          />
        </div>
      )}
    </div>
  );
}
