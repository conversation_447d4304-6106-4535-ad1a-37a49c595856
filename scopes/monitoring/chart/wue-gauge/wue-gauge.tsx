import React from 'react';
import { useSelector } from 'react-redux';

import { Gauge } from '@manyun/base-ui.chart.gauge';
import { Button } from '@manyun/base-ui.ui.button';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';

import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { getTimeRangeFromNow } from '@manyun/monitoring.chart.duration-select';
import { fetchDiffPointData } from '@manyun/monitoring.service.fetch-diff-point-data';
import type { PointGuid } from '@manyun/monitoring.service.fetch-diff-point-data';

import {
  getBlocks, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/selectors/idcWorkbenchSelectors';

import styles from './wue-gauge-module.less';

export type WueGaugeProps = {
  idc: string;
};
type PointGuidList = { wue?: string; deviceGuid: string }[];

const BLANK_PLACEHOLDER = '--';

export function WueGauge({ idc }: WueGaugeProps) {
  const blocks = useSelector(getBlocks) as string[];
  const config = useSelector(selectCurrentConfig);
  const blockWaterPointCode = React.useRef();
  const blockPowerPointCode = React.useRef();
  const idcWaterPointCode = React.useRef();
  const idcPowerPointCode = React.useRef();
  const WUEIntervalDays = React.useRef(1);
  const [idcList, setIdcList] = React.useState([{ deviceGuid: idc }] as PointGuidList);
  const [blockGuidList, setBlockGuidList] = React.useState([] as PointGuidList);
  const [blockPointGuidList, setBlockPointGuidList] = React.useState([] as PointGuid[]);
  const [idcPointGuidList, setIdcPointGuidList] = React.useState([] as PointGuid[]);
  const [visible, setVisiable] = React.useState(false);
  const toggleVisiable = () => setVisiable(!visible);

  React.useEffect(() => {
    const configUtil = new ConfigUtil(config);
    const idcDeviceType = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_IDC);
    const blockDeviceType = configUtil.getOneDeviceType(
      ConfigUtil.constants.deviceTypes.SPACE_BLOCK
    );
    idcWaterPointCode.current = configUtil.getPointCode(
      idcDeviceType,
      ConfigUtil.constants.pointCodes.WATER_USE
    );
    blockWaterPointCode.current = configUtil.getPointCode(
      blockDeviceType,
      ConfigUtil.constants.pointCodes.WATER_USE
    );
    idcPowerPointCode.current = configUtil.getPointCode(
      idcDeviceType,
      ConfigUtil.constants.pointCodes.ACTIVE_IT_POWER_ENERGY_SUM
    );
    blockPowerPointCode.current = configUtil.getPointCode(
      blockDeviceType,
      ConfigUtil.constants.pointCodes.ACTIVE_IT_POWER_ENERGY_SUM
    );
    setIdcPointGuidList([
      {
        deviceGuid: idc,
        pointCode: idcWaterPointCode.current,
      },
      {
        deviceGuid: idc,
        pointCode: idcPowerPointCode.current,
      },
    ]);
    const blockPointGuidList = [] as PointGuid[];
    const blockList = [] as PointGuidList;
    blocks.forEach(item => {
      let spaceGuid = `${idc}.${item}`;
      blockList.push({ deviceGuid: spaceGuid });
      blockPointGuidList.push(
        ...[
          {
            deviceGuid: spaceGuid,
            pointCode: blockWaterPointCode.current,
          },
          {
            deviceGuid: spaceGuid,
            pointCode: blockPowerPointCode.current,
          },
        ]
      );
    });
    setBlockGuidList(blockList);
    setBlockPointGuidList(blockPointGuidList);
    WUEIntervalDays.current = configUtil.getWUEIntervalDays();
  }, [config, blocks, idc]);

  const fetchPointData = React.useCallback(async () => {
    if (idcPointGuidList.length === 0) {
      return;
    }
    const timeRange = getTimeRangeFromNow(-WUEIntervalDays.current, 'day', false);

    const { data, error } = await fetchDiffPointData({
      idcTag: idc,
      startTime: timeRange[0].valueOf(),
      endTime: timeRange[1].valueOf(),
      pointGuidList: [...idcPointGuidList, ...blockPointGuidList],
    });
    if (error) {
      message.error(error.message);
    }
    Object.keys(data).forEach(key => {
      const isBlock = key.split('.').length > 2;
      if (isBlock) {
        setBlockGuidList(prev => handleResponseData(prev, isBlock));
      } else {
        setIdcList(prev => handleResponseData(prev, isBlock));
      }

      function handleResponseData(list: PointGuidList, isBlock: boolean): PointGuidList {
        return list.map(({ deviceGuid }) => {
          const water =
            data[
              `${deviceGuid}.${isBlock ? blockWaterPointCode.current : idcWaterPointCode.current}`
            ];
          const power =
            data[
              `${deviceGuid}.${isBlock ? blockPowerPointCode.current : idcPowerPointCode.current}`
            ];

          return {
            deviceGuid,
            wue:
              typeof water === 'number' && typeof power === 'number' && power !== 0
                ? Number((water / power) * 1000).toFixed(2)
                : BLANK_PLACEHOLDER,
          };
        });
      }
    });
  }, [idcPointGuidList, blockPointGuidList, idc]);

  React.useEffect(() => {
    fetchPointData();
    const currentInterval = window.setInterval(fetchPointData, 15 * 1000);

    return () => {
      window.clearInterval(currentInterval);
    };
  }, [fetchPointData]);

  return (
    <div style={{ position: 'absolute', width: '100%' }}>
      <Gauge
        style={{ height: 138 }}
        option={{
          backgroundColor: '',
          series: [
            {
              type: 'gauge',
              center: ['50%', '95%'],
              radius: '150%',
              startAngle: 180,
              endAngle: 0,
              max: 10,
              axisLine: {
                lineStyle: {
                  width: 18,
                  color: [
                    [0.3, '#36CFC9'],
                    [0.7, '#1890FF'],
                    [1, '#FF4D4F'],
                  ],
                },
              },
              splitLine: {
                distance: -20,
                length: 20,
                lineStyle: {
                  color: '#fff',
                  width: 2,
                },
              },
              axisLabel: {
                color: '#fff',
                fontSize: 10,
                distance: 20,
              },
              pointer: {
                length: '10%',
                width: 4,
                itemStyle: {
                  color: 'auto',
                },
                offsetCenter: [0, -55],
              },
              axisTick: {
                splitNumber: 1,
                distance: 10,
                lineStyle: {
                  width: 0,
                },
              },
              detail: {
                show: false,
              },
              data: [
                {
                  value: Number(idcList[0].wue),
                },
              ],
            },
          ],
        }}
      />
      <div
        style={{
          position: 'absolute',
          bottom: 0,
          left: '50%',
          top: '88px',
          transform: 'translateX(-50%)',
        }}
      >
        <Button type="link" style={{ fontSize: 20 }} onClick={toggleVisiable}>{`${
          idcList[0].wue || BLANK_PLACEHOLDER
        }L/kwh`}</Button>
      </div>
      <Modal
        title="WUE"
        visible={visible}
        width={(750 / 1600) * 100 + '%'}
        onCancel={toggleVisiable}
        footer={null}
        destroyOnClose
      >
        <Descriptions className={styles.styledDescriptions} column={2} bordered>
          {blockGuidList.map((block, index) => (
            <Descriptions.Item key={`description-${index}`} span={1} label={block.deviceGuid}>
              <div style={{ textAlign: 'right' }}>
                {block.wue || BLANK_PLACEHOLDER}
                <span style={{ paddingLeft: 12 }}>L/kwh</span>
              </div>
            </Descriptions.Item>
          ))}
        </Descriptions>
      </Modal>
    </div>
  );
}
