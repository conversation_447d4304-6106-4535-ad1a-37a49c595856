import React, { useCallback, useEffect, useRef, useState } from 'react';

import type { Moment } from 'moment';

import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import type { DatePickerProps } from '@manyun/base-ui.ui.date-picker';
import { Select } from '@manyun/base-ui.ui.select';

import {
  getIntervalByTimeRange,
  getSelectedTimeRange,
  getTimeRangeFromNow,
} from './duration-select-utils';

export enum TimeUnitKeyMap {
  MONTHS = 'months',
  DAYS = 'days',
  HOURS = 'hours',
}

export enum IntervalKeyMap {
  DAYS = 'D',
  HOURS = 'H',
  MINUTES = 'M',
  SECONDS = 'S',
  FIVE_MINUTES = 'M5',
}

export const NO_MATCHING_PREDEFINED_DURATION_OPTION_KEY: string = 'NO_MATCHING';
const delay = 10 * 1000;

type Noop = (value: TimeRange, interval: IntervalKeyMap) => void;
export type TimeRange = [Moment, Moment];

export type DurationSelectProps = {
  style?: React.CSSProperties;
  allowInterval?: boolean;
  defaultIntervalEnabled?: boolean;
  defaultValue?: TimeRange;
  extraData?: number;
  onDidMount: Noop;
  onChange: Noop;
  showTimeRangeSelect?: boolean;
  rangePickerRanges?: Record<string, [Moment, Moment]>;
} & Pick<DatePickerProps, 'size'>;

export function DurationSelect({
  style,
  allowInterval = false,
  defaultIntervalEnabled = false,
  defaultValue = getTimeRangeFromNow(-2, 'hours'),
  extraData,
  onChange,
  onDidMount,
  showTimeRangeSelect = true,
  rangePickerRanges = {
    近2小时: getTimeRangeFromNow(-2, 'hour'),
    近1天: getTimeRangeFromNow(-1, 'day'),
    近7天: getTimeRangeFromNow(-7, 'day'),
    近1月: getTimeRangeFromNow(-1, 'month'),
    近1年: getTimeRangeFromNow(-1, 'year'),
  },
  size,
}: DurationSelectProps) {
  const [intervalEnabled, setIntervalEnabled] = useState(defaultIntervalEnabled);
  const [[startTime, endTime], setTimeRange] = useState(defaultValue);
  const diffMinutes = endTime.diff(startTime, 'minutes');
  const selectedTimeRange = getSelectedTimeRange(diffMinutes);
  let _selectedTimeRangeCopy = selectedTimeRange;
  // See issue [#3627](http://chandao.manyun-local.com/zentao/bug-view-3627.html)
  if (_selectedTimeRangeCopy === NO_MATCHING_PREDEFINED_DURATION_OPTION_KEY) {
    _selectedTimeRangeCopy = `${diffMinutes} minutes`;
  }

  useEffect(() => {
    const interval = getIntervalByTimeRange(defaultValue);
    onDidMount(defaultValue, interval);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const intervalRef = useRef(0);
  const callback = useCallback(() => {
    const [amount, unit] = splitSelectedTimeRange(_selectedTimeRangeCopy);
    const newTimeRange = getTimeRangeFromNow(-amount, unit);
    setTimeRange(newTimeRange);
    const interval = getIntervalByTimeRange(newTimeRange);
    onChange(newTimeRange, interval);
  }, [_selectedTimeRangeCopy, onChange]);

  const prevExtraData = usePrevious(extraData);
  useEffect(() => {
    if (prevExtraData === undefined || prevExtraData === extraData) {
      return;
    }
    callback();
  }, [prevExtraData, extraData, callback]);

  useEffect(() => {
    if (!intervalEnabled) {
      return;
    }
    const currentInterval = window.setInterval(callback, delay);
    intervalRef.current = currentInterval;

    return () => {
      window.clearInterval(currentInterval);
    };
  }, [intervalEnabled, callback]);

  useEffect(() => {
    if (!allowInterval) {
      window.clearInterval(intervalRef.current);
    }
  }, [allowInterval]);

  return (
    <div style={{ display: 'flex', alignItems: 'center', ...style }}>
      {showTimeRangeSelect && (
        <Select
          style={{ minWidth: 120 }}
          value={selectedTimeRange}
          onChange={value => {
            const [amount, unit] = splitSelectedTimeRange(value);
            const timeRange = getTimeRangeFromNow(-amount, unit);
            setTimeRange(timeRange);
            const interval = getIntervalByTimeRange(timeRange);
            onChange(timeRange, interval);
          }}
        >
          <Select.Option value={NO_MATCHING_PREDEFINED_DURATION_OPTION_KEY}>自定义</Select.Option>
          <Select.Option value="5 minutes">最近5分钟</Select.Option>
          <Select.Option value="15 minutes">最近15分钟</Select.Option>
          <Select.Option value="30 minutes">最近30分钟</Select.Option>
          <Select.Option value="1 hour">最近1小时</Select.Option>
          <Select.Option value="2 hours">最近2小时</Select.Option>
          <Select.Option value="3 hours">最近3小时</Select.Option>
          <Select.Option value="6 hours">最近6小时</Select.Option>
          <Select.Option value="12 hours">最近12小时</Select.Option>
          <Select.Option value="1 day">最近1天</Select.Option>
          <Select.Option value="2 days">最近2天</Select.Option>
          <Select.Option value="7 days">最近1周</Select.Option>
          <Select.Option value="30 days">最近1月</Select.Option>
          <Select.Option value="90 days">最近3月</Select.Option>
          <Select.Option value="180 days">最近半年</Select.Option>
          <Select.Option value="365 days">最近1年</Select.Option>
        </Select>
      )}

      <div style={{ marginRight: 8, marginLeft: -1 }}>
        <DatePicker.RangePicker
          size={size}
          allowClear={false}
          showTime={{ format: 'HH:mm' }}
          format="YYYY-MM-DD HH:mm"
          value={[startTime, endTime]}
          ranges={rangePickerRanges}
          onChange={momentTimeRange => {
            if (momentTimeRange === null) {
              return;
            }
            const [start, end] = momentTimeRange;
            setTimeRange([start!, end!]);
            const interval = getIntervalByTimeRange([start!, end!]);
            onChange([start!, end!], interval);
          }}
          onOk={momentTimeRange => {
            if (momentTimeRange === null) {
              return;
            }
            const [start, end] = momentTimeRange;
            const interval = getIntervalByTimeRange([start!, end!]);
            onChange([start!, end!], interval);
          }}
        />
      </div>
      {allowInterval && (
        <Checkbox
          checked={intervalEnabled}
          onChange={({ target: { checked } }) => {
            setIntervalEnabled(checked);
          }}
        >
          自动刷新
        </Checkbox>
      )}
    </div>
  );
}

function usePrevious<T>(value: T) {
  const ref = React.useRef<T>();

  React.useEffect(() => {
    ref.current = value;
  }, [value]);

  return ref.current;
}

function splitSelectedTimeRange(timeRangeStr: string) {
  return timeRangeStr.split(' ');
}
