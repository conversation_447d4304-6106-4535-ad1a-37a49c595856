import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';
import { Theme } from '@manyun/base-ui.theme.theme';

import { DurationSelect } from './duration-select';

export const BasicDurationSelect = () => (
  <ConfigProvider>
    <Theme>
      <DurationSelect
        onDidMount={() => {
          // console.log('onDidMount');
        }}
        onChange={() => {
          // console.log('onChange');
        }}
        allowInterval={true}
      />
    </Theme>
  </ConfigProvider>
);
