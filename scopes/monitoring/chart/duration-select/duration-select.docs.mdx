---
description: 'A DurationSelect component.'
labels: ['monitoring-item', 'duration-select']
---

import { DurationSelect } from './duration-select';

## React Component for set Chart Value Duration

区间选择器， 用于设置图表数据刷新频度和时间区间

### Component usage

```js
<DurationSelect
  onDidMount={() => {
    console.log('onDidMount');
  }}
  onChange={() => {
    console.log('onChange');
  }}
  allowInterval={true}
/>
```
