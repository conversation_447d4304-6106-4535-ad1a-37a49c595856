import moment from 'moment';

import { IntervalKeyMap, NO_MATCHING_PREDEFINED_DURATION_OPTION_KEY } from './duration-select';

/**
 * Get a specific Time Range that `<DurationSelect />` component can consume.
 *
 * Notes
 *
 * Why are the start seconds setting to 0?
 *
 * First things first, we hid the seconds select on the `<DurationSelect />` component,
 * cause' let the user select seconds is meaningless. But we need to retrieve the
 * line chart data just starts from the selected minute of the `Start Time`,
 * so we set seconds to `:00`. But it's not suited for the `End Time`.
 * If we omit the seconds, we just lost the line chart data for the omitted seconds,
 * thus the user won't see the latest-minute data until 1 minute later.
 *
 * @param {number} [offset=-1]
 * @param {moment.unitOfTime.DurationConstructor} [unit='day']
 */
export function getTimeRangeFromNow(
  offset = -1,
  unit = 'day',
  isSecondZero = true
): [moment.Moment, moment.Moment] {
  const now = moment();

  // Note: It should be noted that moments are mutable.
  // Calling any of the manipulation methods will change the original moment.
  // If you want to create a copy and manipulate it,
  // you should use moment#clone before manipulating the moment.
  // [More info on cloning](https://momentjs.com/docs/#/parsing/moment-clone/)
  const copy = now.clone();
  const past = copy.add(offset, unit as moment.unitOfTime.DurationConstructor);

  return isSecondZero ? [past.seconds(0), now] : [past, now];
}

export function getSelectedTimeRange(diffMinutes: number) {
  switch (diffMinutes) {
    case 5:
      return '5 minutes';
    case 15:
      return '15 minutes';
    case 30:
      return '30 minutes';
    case 60:
      return '1 hour';
    case 60 * 2:
      return '2 hours';
    case 60 * 3:
      return '3 hours';
    case 60 * 6:
      return '6 hours';
    case 60 * 12:
      return '12 hours';
    case 60 * 24:
      return '1 day';
    case 60 * 24 * 2:
      return '2 days';
    case 60 * 24 * 7:
      return '7 days';
    case 60 * 24 * 30:
      return '30 days';
    case 60 * 24 * 30 * 3:
      return '90 days';
    case 60 * 24 * 30 * 6:
      return '180 days';
    case 60 * 24 * 365:
      return '365 days';
    default:
      // See issue [#3280](http://chandao.manyun-local.com/zentao/bug-view-3280.html)
      return NO_MATCHING_PREDEFINED_DURATION_OPTION_KEY;
  }
}

const ONE_HOUR_MS = 60 * 60 * 1000;
const ONE_DAY_MS = ONE_HOUR_MS * 24;
/**
 * Get the chart data request's `interval`.
 *
 * @param {[import('moment').Moment, import('moment').Moment]} timeRange
 * @returns
 */
export function getIntervalByTimeRange([momentStart, momentEnd]: [moment.Moment, moment.Moment]) {
  // Note: It should be noted that the `momentStart` which generated from
  // `getTimeRangeFromNow` function, it's seconds is always 0. So, we should
  // do the same thing on `momentEnd`, otherwise, the difference between
  // these 2 moment might overflow.
  const end = momentEnd.clone().seconds(0);
  const diffMs = end.diff(momentStart);
  if (diffMs <= 2 * ONE_HOUR_MS) {
    return IntervalKeyMap.SECONDS;
  } else if (diffMs > 2 * ONE_HOUR_MS && diffMs <= ONE_DAY_MS) {
    return IntervalKeyMap.MINUTES;
  } else if (diffMs > ONE_DAY_MS && diffMs <= 5 * ONE_DAY_MS) {
    return IntervalKeyMap.FIVE_MINUTES;
  } else if (diffMs > 5 * ONE_DAY_MS && diffMs <= 60 * ONE_DAY_MS) {
    return IntervalKeyMap.HOURS;
  }

  return IntervalKeyMap.DAYS;
}
