import { message } from '@manyun/base-ui.ui.message';

import type { DiPointValue, ValueMapping } from '@manyun/monitoring.model.point';
import { fetchPointThresholds } from '@manyun/monitoring.service.fetch-point-thresholds';
import type {
  SvcQuery as PointGuid,
  Threshold,
} from '@manyun/monitoring.service.fetch-point-thresholds';

export type PointThreshold = Threshold[];
type ExpectValidLimits = Record<string, string>;
export type ValidLimits = string[] | ExpectValidLimits | ValueMapping;

export async function getPointThresholds(point: PointGuid) {
  //todo: 确定是否会重复触发这段逻辑，若重复，则需要添加过滤操作
  const { error, data } = await fetchPointThresholds(point);
  if (error) {
    message.error(error.message);
  }

  return data.data;
}

/**
 * 数组类型 ["0=退出", "1=合入"]
 * 对象类型 {0: '退出', 1: '合入'}
 * 对象类型 {0: {NAME: '退出', ...}, 1: {NAME: '合入', ...} }
 */
export function handleValidLimits(validLimits: ValidLimits): ExpectValidLimits {
  if (!validLimits) {
    return {};
  }
  if (Array.isArray(validLimits)) {
    const formattedValidLimits: ExpectValidLimits = {};
    validLimits.forEach(item => {
      const limit = item.split('=');
      formattedValidLimits[limit[0]] = limit[1];
    });
    return formattedValidLimits;
  }
  const keys = Object.keys(validLimits);
  if (typeof validLimits[keys[0]] === 'string') {
    return validLimits as ExpectValidLimits;
  } else {
    const formattedValidLimits: ExpectValidLimits = {};
    Object.keys(validLimits).forEach(item => {
      formattedValidLimits[Number(item)] = (validLimits[Number(item)] as DiPointValue).NAME;
    });
    return formattedValidLimits;
  }
}
