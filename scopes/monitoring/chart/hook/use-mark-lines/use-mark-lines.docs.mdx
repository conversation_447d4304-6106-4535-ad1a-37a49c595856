---
description: 'A React Hook that gets chart markLines option.'
labels: ['hook', 'getMarkLinesAndSomething']
---

import { useAIPointMarkLines } from './use-mark-lines';

## A React Hook to get chart option marklines

### Component usage

```js
import { useAIPointMarkLines, useDIPointMarkLines } from './use-mark-lines';

const [{ markLines, loading }, getMarkLines] = useAIPointMarkLines({
  deviceGuid: 'EC06.A',
  pointCode: '1005000',
  deviceType: '10101',
  spaceGuid: 'EC06.A',
});

React.useEffect(() => {
  getMarkLines();
}, []);

const [{ markLines, yAxisCategories }, getMarkLines] = useDIPointMarkLines({
  0: { NAME: '退出' },
  1: { NAME: '合入' },
});

React.useEffect(() => {
  getMarkLines([
    [
      [1111, 0],
      [2222, 1],
      [3333, 2],
    ],
  ]);
}, []);
```
