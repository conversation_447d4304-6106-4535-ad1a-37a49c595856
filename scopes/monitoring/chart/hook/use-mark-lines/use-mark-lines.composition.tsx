import React from 'react';

import get from 'lodash.get';

import { ConfigProvider } from '@manyun/base-ui.context.config';
import { Spin } from '@manyun/base-ui.ui.spin';

import { useRemoteMock } from '@manyun/service.request';

import { useAIPointMarkLines, useDIPointMarkLines } from './use-mark-lines';

export const BasicUseAIPointMarkLines = () => {
  const [ready, setReady] = React.useState(false);
  const [{ markLines, loading }, getMarkLines] = useAIPointMarkLines({
    deviceGuid: 'EC06.A',
    pointCode: '1005000',
    deviceType: '10101',
    spaceGuid: 'EC06.A',
  });

  React.useEffect(() => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const mockOff = useRemoteMock('web', {
      adapter: 'default',
    });
    setReady(true);
    getMarkLines();

    return () => {
      mockOff();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (!ready) {
    return <span>loading</span>;
  }

  if (loading) {
    return <Spin spinning={loading} size="small" delay={5000} />;
  }

  return (
    <ConfigProvider>
      <h1>
        The async ai markLines first items yAxis :
        {`${get(markLines?.[0], ['name'])} - ${get(markLines?.[0], ['yAxis'])}`}
      </h1>
    </ConfigProvider>
  );
};

export const BasicUseDIPointMarkLines = () => {
  const [{ markLines, yAxisCategories }, getMarkLines] = useDIPointMarkLines({
    0: { NAME: '退出' },
    1: { NAME: '合入' },
  });

  React.useEffect(() => {
    getMarkLines([
      [
        [1111, 0],
        [2222, 1],
        [3333, 2],
      ],
    ]);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      <h1>
        The di markLines first items yAxis :
        {`${get(markLines?.[0], ['name'])} - ${get(markLines?.[0], ['yAxis'])}`}
      </h1>
      <h1>The yAxisCategories : {yAxisCategories?.join(' ')}</h1>
    </>
  );
};
