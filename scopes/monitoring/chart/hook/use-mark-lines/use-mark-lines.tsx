import React from 'react';

import get from 'lodash.get';
import uniq from 'lodash.uniq';
import uniqBy from 'lodash.uniqby';
import tinygradient from 'tinygradient';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';

import type { PointGuid as DIPointGuid } from '@manyun/monitoring.chart.points-state-line';
import { TriggerRuleLegacy } from '@manyun/monitoring.model.trigger-rule';
import type { SeriesData } from '@manyun/monitoring.service.fetch-chart-data';
import type { SvcQuery as PointGuid } from '@manyun/monitoring.service.fetch-point-thresholds';

import { getPointThresholds, handleValidLimits } from './use-mark-lines-utils';
import type { ValidLimits } from './use-mark-lines-utils';

const markLineGradient = tinygradient(['#e84749', '#1890ff']);

export function useAIPointMarkLines(point: Partial<PointGuid> = {}) {
  const [markLines, setMarkLines] = React.useState([] as any[]);
  const [loading, setLoading] = React.useState(false);

  const getMarkLines = React.useCallback(async () => {
    if (!get(point, 'spaceGuid') || !get(point, 'deviceType')) {
      return;
    }
    setLoading(true);
    const thresholds = await getPointThresholds(point as PointGuid);
    setLoading(false);
    if (thresholds === null) {
      setMarkLines([]);
      return;
    }
    const validThresholds: any[] = [];
    thresholds.forEach(
      ({ alarmType: { code }, triggerRule }: { alarmType: any; triggerRule: string }) => {
        if (code !== 'ERROR') {
          return;
        }
        const rules = TriggerRuleLegacy.toObject(triggerRule);
        if (rules === null || rules === undefined || Array.isArray(rules)) {
          return;
        }
        const { lowerLimit, upperLimit } = rules;
        const threshold = [];
        if (lowerLimit !== null) {
          threshold[0] = lowerLimit.limit;
        }
        if (upperLimit !== null) {
          threshold[1] = upperLimit.limit;
        }
        if (!threshold.length) {
          return;
        }
        validThresholds.push(threshold);
      }
    );
    const colors = markLineGradient.rgb(validThresholds.length > 2 ? validThresholds.length : 2);
    const markLines = validThresholds.reduce(
      (markLineData, [lowerLimit, upperLimit], validThresholdIdx) => {
        const lineStyle = { color: colors[validThresholdIdx].toHexString() };
        if (Number(lowerLimit)) {
          markLineData.push({
            lineStyle,
            name: '下限',
            yAxis: lowerLimit,
          });
        }
        if (Number(upperLimit)) {
          markLineData.push({
            lineStyle,
            name: '上限',
            yAxis: upperLimit,
          });
        }

        return markLineData;
      },
      []
    );
    setMarkLines(markLines);
  }, [point]);

  return [{ markLines, loading }, getMarkLines] as const;
}

export function useDIPointMarkLines(validLimitsMap: ValidLimits, pointGuids: DIPointGuid[]) {
  const [markLines, setMarkLines] = React.useState([] as any[]);
  const [seriesData, setSeriesData] = React.useState([] as SeriesData);
  const [yAxisCategories, setYAxisCategories] = React.useState([] as string[]);
  const pointValueMappins = useDeepCompareMemo(() => {
    return pointGuids
      .map(point => point.valueMapping)
      .filter(valueMapping => valueMapping !== undefined);
  }, [pointGuids]);
  const memoValidLimitsMap = useDeepCompareMemo(() => validLimitsMap, [validLimitsMap]);

  const getMarkLines = React.useCallback(
    (chartSeriesData: SeriesData) => {
      setMarkLines([]);
      setYAxisCategories([]);
      const list = [...chartSeriesData];
      list.forEach((seriesData, index) => {
        const yAxisCategories: string[] = [];
        const validLimits = handleValidLimits(pointValueMappins[index] || memoValidLimitsMap);
        const validLimitsKeys = Object.keys(validLimits);
        let hasError = false;
        seriesData.forEach(item => {
          if (item[1] === null) {
            return;
          }
          const validLimitName = validLimits[item[1]];
          if (validLimitName) {
            !yAxisCategories.some(yAxisCategory => yAxisCategory === validLimitName) &&
              yAxisCategories.push(validLimitName);
          } else {
            hasError = true;
          }
          item[1] = validLimitName || '错误值';
        });
        hasError && yAxisCategories.push('错误值');
        const colors = markLineGradient.rgb(Math.max(validLimitsKeys.length, 2));
        const markLines = validLimitsKeys.map((limit, index) => {
          const lineStyle = { color: colors[index].toHexString() };
          return {
            lineStyle,
            name: validLimits[limit],
            yAxis: Number(limit),
            label: { show: false },
          };
        });
        setMarkLines(prev => uniqBy([...prev, ...markLines], 'name'));
        setYAxisCategories(prev => uniq([...prev, ...yAxisCategories]));
      });
      setSeriesData(list);
    },
    [memoValidLimitsMap, pointValueMappins]
  );

  return [{ markLines, seriesData, yAxisCategories }, getMarkLines] as const;
}
