import React, { useMemo } from 'react';
import { useSelector } from 'react-redux';

import { Empty } from '@manyun/base-ui.ui.empty';
import { Form } from '@manyun/base-ui.ui.form';
import type { FormInstance } from '@manyun/base-ui.ui.form';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import {
  useCurrentMutateType,
  useMutateTplFields,
} from '@manyun/monitoring.hook.use-alarm-configuration-template';
import { AlarmTemplateType } from '@manyun/monitoring.model.alarm-template';
import { AlarmNoticeTplCfg } from '@manyun/monitoring.ui.alarm-notice-tpl-cfg';
import { selectDeviceTypes } from '@manyun/resource-hub.state.device-types';

export type NoticeConfigureFormValues = {
  notifyConfigMode: 'GLOBAL' | 'INSTANCE';
};

export type NoticeConfigureProps = {
  link: { href: string; title: React.ReactNode };
  form: FormInstance<NoticeConfigureFormValues>;
};

export const NoticeConfigure = ({ link, form }: NoticeConfigureProps) => {
  const [mutateType] = useCurrentMutateType();
  const [
    { type, target, notifyConfigMode, globalNoticeRules, monitoringItems },
    { setMutateTplFields },
  ] = useMutateTplFields(mutateType);
  /**监控对象 */
  const { entities } = useSelector(selectDeviceTypes);

  const targetName = useMemo(() => {
    if (!target) {
      return null;
    }
    return type === AlarmTemplateType.Device ? entities[target]?.metaName ?? target : target;
  }, [entities, target, type]);

  return (
    <div id={link.href}>
      <Space direction="vertical" style={{ width: '100%' }}>
        <Typography.Title level={5}>{link.title}</Typography.Title>
        {targetName && <Typography.Text> 监控对象： {targetName}</Typography.Text>}
        <Radio.Group
          value={notifyConfigMode}
          onChange={e => {
            setMutateTplFields({ notifyConfigMode: e.target.value });
          }}
        >
          <Radio.Button value="GLOBAL">全局配置</Radio.Button>
          <Radio.Button value="INDEPENDENT">逐条配置</Radio.Button>
        </Radio.Group>
        <Form form={form} layout="vertical">
          {notifyConfigMode === 'GLOBAL' && (
            <AlarmNoticeTplCfg form={form} monitoringItem={{ notifyRules: globalNoticeRules }} />
          )}
          {notifyConfigMode === 'INDEPENDENT' &&
            monitoringItems.map(monitoring => (
              <div key={'notifyRuleConfig_monitoringItem' + monitoring.id}>
                <AlarmNoticeTplCfg form={form} monitoringItem={monitoring as any} />
              </div>
            ))}
          {notifyConfigMode === 'INDEPENDENT' && monitoringItems.length === 0 && (
            <Empty
              description={<Typography.Text type="danger">请先添加监控规则</Typography.Text>}
            />
          )}
        </Form>
      </Space>
    </div>
  );
};
