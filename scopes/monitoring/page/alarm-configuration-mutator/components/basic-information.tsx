import React, { use<PERSON>allback, useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';

import { Form } from '@manyun/base-ui.ui.form';
import type { FormInstance } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import type { SelectProps } from '@manyun/base-ui.ui.select';
import { Switch } from '@manyun/base-ui.ui.switch';
import { Typography } from '@manyun/base-ui.ui.typography';

import {
  useCurrentMutateType,
  useGetAlarmTemplateAssociatedMonitoringItem,
  useMutateTplFields,
  useSetCloneTemplates,
  useTemplateGroups,
} from '@manyun/monitoring.hook.use-alarm-configuration-template';
import {
  AlarmTemplateType,
  /* AlarmTemplateTypeOptions */
} from '@manyun/monitoring.model.alarm-template';
import { fetchAlarmConfigurationsByDeviceType } from '@manyun/monitoring.service.fetch-alarm-configurations-by-device-type';
import type {
  MutateType,
  SetMutateFieldsActionPayload,
} from '@manyun/monitoring.state.alarm-configuration-template';
import { selectDeviceTypes } from '@manyun/resource-hub.state.device-types';
import { DeviceTypeCascader } from '@manyun/resource-hub.ui.device-type-cascader';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';

const formItemLayout = {
  style: { width: 300 },
};

export type BasicInformationFormValues = Partial<SetMutateFieldsActionPayload>;

export type BasicInformationProps = {
  link: { href: string; title: React.ReactNode };
  form: FormInstance<BasicInformationFormValues>;
  mutateType: MutateType;
};

export const BasicInformation = React.forwardRef<unknown, BasicInformationProps>(
  ({ link, form, mutateType }, ref?) => {
    const [fields, { setMutateTplFields }] = useMutateTplFields(mutateType);
    const [{ loading, tplGroups }, fetchTplGroups] = useTemplateGroups();

    /**设备类型 */
    const { entities } = useSelector(selectDeviceTypes);

    const getTargetName = useCallback(
      (value?: string) => {
        if (!value) {
          return;
        }
        return fields.type === AlarmTemplateType.Device
          ? entities[value]?.metaName ?? fields.target
          : value;
      },
      [entities, fields.target, fields.type]
    );

    const handleChange = useCallback(
      (type: AlarmTemplateType, field: 'type' | 'target', value?: string) => {
        let clearValues: Omit<SetMutateFieldsActionPayload, 'mutateType'> = {
          monitorItemEntities: {},
          monitorItemIds: [],
          selectedMonitorItemIds: [],
        };
        if (field === 'type') {
          clearValues.target = undefined;
          clearValues.name = undefined;
          form.setFieldsValue({
            target: undefined,
            name: undefined,
          });
        } else if (field === 'target') {
          clearValues.name = `${getTargetName(value)}-告警模板`;
          form.setFieldsValue({
            name: `${getTargetName(value)}-告警模板`,
          });
        }
        switch (type) {
          case AlarmTemplateType.Device:
            break;
          case AlarmTemplateType.Instance:
            clearValues = {
              ...clearValues,
              associateGroupIds: [],
              cloneTemplateId: undefined,
            };
            break;
        }
        form.setFieldsValue({
          associateGroupIds: undefined,
          cloneTemplateId: undefined,
        });

        return clearValues;
      },
      [form, getTargetName]
    );

    const isCreate = useMemo(() => mutateType === 'create', [mutateType]);
    const isDeviceTpl = useMemo(() => fields.type === AlarmTemplateType.Device, [fields.type]);

    useEffect(() => {
      if (isDeviceTpl) {
        fetchTplGroups();
      }
    }, [fetchTplGroups, isDeviceTpl]);

    return (
      <Form
        ref={ref}
        id={link.href}
        form={form}
        initialValues={fields}
        layout="vertical"
        onValuesChange={changedValues => {
          let newValues = { ...changedValues };
          if (changedValues.type) {
            const clearValues = handleChange(changedValues.type, 'type', changedValues.type);
            newValues = { ...newValues, ...clearValues };
          }
          if (changedValues.target) {
            const clearValues = handleChange(changedValues.type, 'target', changedValues.target);
            newValues = { ...newValues, ...clearValues };
          }
          setMutateTplFields(newValues);
        }}
        {...formItemLayout}
      >
        <Typography.Title level={5}>{link.title}</Typography.Title>
        {/* <Form.Item
          label="模板类型"
          name="type"
          rules={[
            {
              required: true,
              message: '模板类型必选',
            },
          ]}
        >
          <Select options={AlarmTemplateTypeOptions} disabled />
        </Form.Item> */}
        {!isDeviceTpl ? (
          <Form.Item
            label="监控对象"
            name="target"
            rules={[{ required: true, message: '监控对象必选' }]}
          >
            <LocationTreeSelect nodeTypes={['IDC']} disabled={!isCreate} />
          </Form.Item>
        ) : (
          <Form.Item
            label="监控对象"
            name="target"
            rules={[{ required: true, message: '监控对象必选' }]}
          >
            <DeviceTypeCascader
              dataType={['snDevice', 'space']}
              disabledTypeList={['C0', 'C1']}
              numbered
              disabled={!isCreate}
            />
          </Form.Item>
        )}
        {isDeviceTpl && isCreate && (
          <Form.Item label="克隆模板" name="cloneTemplateId">
            <CloneTemplateSelect form={form} target={fields.target} allowClear />
          </Form.Item>
        )}
        <Form.Item
          label="模板名称"
          name="name"
          rules={[
            { required: true, message: '模板名称必填', whitespace: true },
            {
              max: 60,
              message: '最多输入 60 个字符！',
            },
          ]}
        >
          <Input allowClear />
        </Form.Item>
        <Form.Item label="是否启用" name="enable" valuePropName="checked">
          <Switch checkedChildren="开启" unCheckedChildren="停用" />
        </Form.Item>
        {isDeviceTpl && (
          <Form.Item label="关联模板组" name="associateGroupIds">
            <Select
              loading={loading}
              options={tplGroups.map(item => ({ label: item.name, value: item.id }))}
              allowClear
              mode="multiple"
            />
          </Form.Item>
        )}
        <Form.Item
          label="备注"
          name="description"
          rules={[
            {
              max: 67,
              message: '最多输入 67 个字符！',
            },
          ]}
        >
          <Input.TextArea allowClear />
        </Form.Item>
      </Form>
    );
  }
);
BasicInformation.displayName = 'BasicInformation';

type CloneTemplateSelectProps = {
  target: string | undefined;
  form: FormInstance<BasicInformationFormValues>;
} & Omit<SelectProps, 'options'>;

export const CloneTemplateSelect = React.forwardRef<unknown, CloneTemplateSelectProps>(
  ({ target, form, ...props }, ref?) => {
    const [options, setOptions] = useState<{ label: string; value: number }[]>([]);
    const [cloneTplEntities, setCloneTplEntities] = useSetCloneTemplates();
    const [currentMutateType] = useCurrentMutateType();
    const [, { setMutateTplFields }] = useMutateTplFields(currentMutateType);
    const [, { fetchMonitorItems }] =
      useGetAlarmTemplateAssociatedMonitoringItem(currentMutateType);
    const [, fetchTplGroups] = useTemplateGroups();

    const fetchCloneTpls = useCallback(async () => {
      if (target === undefined) {
        return;
      }
      const { error, data } = await fetchAlarmConfigurationsByDeviceType({ deviceType: target });
      if (error) {
        message.error(error.message);
        return;
      }
      setCloneTplEntities(data.data);
      setOptions(data.data.map(d => ({ label: d.name, value: d.id as number })));
    }, [setCloneTplEntities, target]);

    useEffect(() => {
      if (target) {
        fetchCloneTpls();
      }
    }, [fetchCloneTpls, target]);

    const onInternalChange = useCallback(
      async val => {
        if (val) {
          const tpl = cloneTplEntities[val as number];
          if (tpl) {
            /**查询模板关联的模板组 */
            fetchTplGroups({
              groupId: tpl.id as number,
              callback: data => {
                const associateGroupIds = data.map(d => d.id);
                setMutateTplFields({ associateGroupIds });
                form.setFieldsValue({ associateGroupIds });
              },
            });
            /**查询关联的监控项 */
            fetchMonitorItems({
              id: tpl.id as number,
            });
            const newValues = {
              name: tpl.name,
              enable: tpl.enable,
              description: tpl.description,
            };
            setMutateTplFields({ ...newValues });
            form.setFieldsValue({
              ...newValues,
            });
          }
        }
      },
      [cloneTplEntities, fetchMonitorItems, fetchTplGroups, form, setMutateTplFields]
    );

    return (
      <Select
        ref={ref}
        {...props}
        options={options}
        disabled={!target}
        onChange={val => {
          onInternalChange(val);
          props.onChange && props.onChange(val, []);
        }}
      />
    );
  }
);

CloneTemplateSelect.displayName = 'CloneTemplateSelect';
