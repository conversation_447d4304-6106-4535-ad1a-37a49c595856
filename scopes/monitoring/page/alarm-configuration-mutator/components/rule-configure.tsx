import React, { useEffect, useMemo, useState } from 'react';

import shortid from 'shortid';

import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import {
  useGetAlarmTemplateAssociatedMonitoringItem,
  useMutateTplFields,
} from '@manyun/monitoring.hook.use-alarm-configuration-template';
import { AlarmMonitoringItemMutator } from '@manyun/monitoring.ui.alarm-monitoring-item-mutator';
import { AlarmMonitoringItemStateUpdator } from '@manyun/monitoring.ui.alarm-monitoring-item-state-updator';
import { AlarmMonitoringItemTable } from '@manyun/monitoring.ui.alarm-monitoring-item-table';

import styles from './rule-configure.module.less';

export type RuleConfigureProps = {
  link: { href: string; title: React.ReactNode };
  groupId?: number | string /**告警模板id */;
};

export const RuleConfigure = ({ link, groupId }: RuleConfigureProps) => {
  const currentMutateType = groupId !== undefined ? 'update' : 'create';
  const [{ loading }, { fetchMonitorItems }] =
    useGetAlarmTemplateAssociatedMonitoringItem(currentMutateType);
  const [
    { selectedMonitorItemIds, monitoringItems },
    { setMutateTplFields, deleteMutateTplMonitorItem, addMutateTplMonitorItem },
  ] = useMutateTplFields(currentMutateType);
  const [key, setKey] = useState<string | undefined>(undefined);

  const dataSource = useMemo(() => {
    if (key) {
      return monitoringItems.filter(item => item.name.includes(key));
    }
    return monitoringItems;
  }, [key, monitoringItems]);

  useEffect(() => {
    if (groupId !== undefined) {
      fetchMonitorItems({ id: Number(groupId) });
    }
  }, [fetchMonitorItems, groupId]);

  return (
    <div id={link.href}>
      <Space style={{ width: '100%' }} direction="vertical" className={styles.rulesTableContainer}>
        <Typography.Title level={5}>{link.title}</Typography.Title>
        <Space size="middle">
          <AlarmMonitoringItemMutator showLock addRulebuttonProps={{ type: 'primary' }} />
          <Input.Search
            allowClear
            style={{ width: 300 }}
            onSearch={value => {
              setKey(value);
            }}
          />
        </Space>
        <AlarmMonitoringItemTable
          dataSource={dataSource}
          rowKey="id"
          loading={loading}
          scroll={{ x: 'fit-content' }}
          showColumns={[
            'name',
            'alarmLevel',
            'alarmType',
            'preTriggerRules',
            'triggerRules',
            'enable',
            'lock',
            'isCreateIncident',
            'incidentType',
          ]}
          operation={{
            title: '操作',
            key: 'option',
            width: 140,
            fixed: 'right',
            valueType: 'option',
            render: (_, entity) => [
              <div key={entity.id}>
                <AlarmMonitoringItemMutator
                  id={entity.id}
                  showLock
                  addRulebuttonProps={{ type: 'link', compact: true }}
                />
              </div>,
              <Button
                key={`copy_${entity.id}`}
                type="link"
                compact
                onClick={() => {
                  addMutateTplMonitorItem({ ...entity, id: shortid() });
                }}
              >
                复制
              </Button>,
              currentMutateType === 'create' ? (
                <Button
                  key={`delete_${entity.id}`}
                  type="link"
                  compact
                  onClick={() => {
                    deleteMutateTplMonitorItem(entity.id as unknown);
                  }}
                >
                  删除
                </Button>
              ) : (
                <Popconfirm
                  key="popconfirm"
                  title={`确定删除 ${entity.name} 告警规则，删除后需重新配置？`}
                  onConfirm={() => {
                    deleteMutateTplMonitorItem(entity.id as unknown);
                  }}
                >
                  <Button key={`delete_${entity.id}`} type="link" compact>
                    删除
                  </Button>
                </Popconfirm>
              ),
            ],
          }}
          options={{
            search: true,
          }}
          toolBarRender={false}
          search={false}
          rowSelection={{
            selectedRowKeys: selectedMonitorItemIds,
            onChange: changableRowKeys => {
              setMutateTplFields({ selectedMonitorItemIds: changableRowKeys });
            },
          }}
          tableAlertRender={({ selectedRowKeys, selectedRows, onCleanSelected }) => (
            <Space size={24}>
              <span>
                已选 {selectedRowKeys.length} 项
                <span style={{ marginLeft: 8 }} onClick={onCleanSelected}>
                  取消选择
                </span>
              </span>
            </Space>
          )}
          tableAlertOptionRender={() => {
            return (
              <Space size={16}>
                <AlarmMonitoringItemStateUpdator
                  enable
                  mode="batch"
                  ids={selectedMonitorItemIds}
                  refKey="batchEnable"
                />
                <AlarmMonitoringItemStateUpdator
                  enable={false}
                  mode="batch"
                  refKey="batchDisenable"
                  ids={selectedMonitorItemIds}
                  showConfirm={currentMutateType === 'update'}
                />
              </Space>
            );
          }}
          locale={{
            emptyText: <Typography.Text type="danger">至少添加1条监控规则</Typography.Text>,
          }}
          ghost
        />
      </Space>
    </div>
  );
};
