import React, { useCallback, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useHistory, useParams } from 'react-router-dom';
import { useMountedState } from 'react-use';

import { Anchor } from '@manyun/base-ui.ui.anchor';
import type { AnchorLinkProps } from '@manyun/base-ui.ui.anchor';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import {
  useCurrentMutateType,
  useGetAlarmTemplateConfiguration,
  useMutateTplFields,
} from '@manyun/monitoring.hook.use-alarm-configuration-template';
import type { MonitoringItem, MonitoringItemJSON } from '@manyun/monitoring.model.monitoring-item';
import { ALARM_CONFIGURATION_TEMPLATE_LIST } from '@manyun/monitoring.route.monitoring-routes';
import type { AlarmConfigurationTemplateEditParams } from '@manyun/monitoring.route.monitoring-routes';
import { mutateAlarmConfigurationAction } from '@manyun/monitoring.state.alarm-configuration-template';

import styles from './alarm-configuration-mutator.module.less';
import { BasicInformation } from './components/basic-information';
import type { BasicInformationFormValues } from './components/basic-information';
import { NoticeConfigure } from './components/notice-configure';
import type { NoticeConfigureFormValues } from './components/notice-configure';
import { RuleConfigure } from './components/rule-configure';

export type AlarmConfigurationMutatorProps = {};

const AnchorLinks: AnchorLinkProps[] = [
  {
    href: 'basic-info',
    title: '模板信息',
  },
  {
    href: 'rule-configure',
    title: '规则配置',
  },
  {
    href: 'notice-configure',
    title: '文案配置',
  },
];

export function AlarmConfigurationMutator() {
  const dispatch = useDispatch();
  const history = useHistory();
  const { id } = useParams<AlarmConfigurationTemplateEditParams>();
  const currentMutateType = id !== undefined ? 'update' : 'create';
  const [, setMutateType] = useCurrentMutateType();
  const [{ loading }, { fetchAlarmConfigure }] =
    useGetAlarmTemplateConfiguration(currentMutateType);
  const [fields, { setMutateTplFields }] = useMutateTplFields(currentMutateType);
  const [form] = Form.useForm<BasicInformationFormValues>();
  const [noticeForm] = Form.useForm<NoticeConfigureFormValues>();

  const isMounted = useMountedState();

  useEffect(() => {
    /**编辑页面逻辑 */
    if (id !== undefined) {
      /**请求告警模板详情 */
      fetchAlarmConfigure({
        id: Number(id),
        callback: data => {
          if (isMounted()) {
            form.setFieldsValue({ ...data });
          }
        },
      });
    }
    setMutateType(currentMutateType);
  }, [currentMutateType, fetchAlarmConfigure, form, id, isMounted, setMutateType]);

  const onSubmit = useCallback(async () => {
    try {
      await form.validateFields();
    } catch (error: unknown) {
      form.scrollToField(error?.errorFields[0].name.toString(), {
        behavior: actions => {
          actions.forEach(action => {
            action.el.scrollTop = action.top - 55;
          });
        },
      });
      return;
    }

    if (fields.monitoringItems.length === 0) {
      message.error('请添加监控项');
      return;
    }

    try {
      await noticeForm.validateFields();
    } catch (err: unknown) {
      const errorIds: string[] = [];
      (err?.errorFields ?? []).forEach((fields: { name: unknown }) => {
        const names = fields.name.toString();
        const id = names.split('_$$_')[0];
        if (id) {
          errorIds.push(id);
        }
      });
      if (errorIds.length > 0) {
        setMutateTplFields({ validateNotifyErrorIds: errorIds });
      }
      if (err?.errorFields[0].name.toString()) {
        const elem = document.getElementById(err?.errorFields[0].name.toString());
        elem?.scrollIntoView();
      }
      return;
    }

    const monitorItems: MonitoringItemJSON[] = [...fields.monitoringItems].map(item => ({
      ...item,
      id: typeof item.id === 'string' ? undefined : item.id,
    }));
    if (fields.notifyConfigMode === 'GLOBAL') {
      monitorItems.forEach(monitorItem => {
        monitorItem.notifyRules = fields.globalNoticeRules;
      });
    }

    dispatch(
      mutateAlarmConfigurationAction({
        mutateType: currentMutateType,
        alarmTpl: {
          id: fields.id,
          name: fields.name!,
          type: fields.type!,
          target: fields.target!,
          description: fields.description,
          associateGroupIds: fields.associateGroupIds!,
          monitorItems: [...monitorItems] as MonitoringItem[],
          enable: fields.enable!,
        },
        callback: () => {
          message.success('操作成功');
          history.push(ALARM_CONFIGURATION_TEMPLATE_LIST);
        },
      })
    );
  }, [
    currentMutateType,
    setMutateTplFields,
    dispatch,
    fields.associateGroupIds,
    fields.description,
    fields.enable,
    fields.globalNoticeRules,
    fields.id,
    fields.monitoringItems,
    fields.name,
    fields.notifyConfigMode,
    fields.target,
    fields.type,
    form,
    noticeForm,
    history,
  ]);

  return (
    <>
      <Card className={styles.mutatorContainer} id="alarmConfigurationContainer" loading={loading}>
        <Space className={styles.mutatorContent} align="start">
          <Space direction="vertical" style={{ width: '100%' }}>
            <Typography.Title level={4}>
              {currentMutateType === 'create' ? '新建' : '编辑'}告警模板
            </Typography.Title>
            <BasicInformation mutateType={currentMutateType} link={AnchorLinks[0]} form={form} />
            <RuleConfigure link={AnchorLinks[1]} groupId={id} />
            <NoticeConfigure link={AnchorLinks[2]} form={noticeForm} />
          </Space>
          <Anchor
            getContainer={() => document.getElementById('alarmConfigurationContainer') as unknown}
          >
            {AnchorLinks.map(link => (
              <Anchor.Link key={link.href} href={`#${link.href}`} title={link.title} />
            ))}
          </Anchor>
        </Space>
      </Card>
      <FooterToolBar fixed>
        <Space size={12}>
          <Button type="primary" loading={fields.loading} onClick={onSubmit}>
            提交
          </Button>
          <Button
            onClick={() => {
              history.goBack();
            }}
          >
            返回
          </Button>
        </Space>
      </FooterToolBar>
    </>
  );
}
