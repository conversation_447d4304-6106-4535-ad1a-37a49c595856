/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-1-3
 *
 * @packageDocumentation
 */
import React, { useEffect, useState } from 'react';
import { useHistory, useParams } from 'react-router-dom';

import dayjs from 'dayjs';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { PageHeader } from '@manyun/base-ui.ui.page-header';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import { useAuthorized } from '@manyun/auth-hub.hook.gql.use-authorized';
import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import type { ShieldScope } from '@manyun/monitoring.gql.client.monitoring';
import {
  useDeleteAlarmShield,
  useFinishAlarmShield,
  useLazyAlarmShield,
} from '@manyun/monitoring.gql.client.monitoring';
import {
  ALARM_SHIELD_LIST_ROUTE_PATH,
  generateAlarmShieldEditRoutePath,
} from '@manyun/monitoring.route.monitoring-routes';
import type { ShieldPoint } from '@manyun/monitoring.ui.alarm-shield-scope-table';
import {
  AlarmShieldScopeTable,
  ShieldPointTable,
} from '@manyun/monitoring.ui.alarm-shield-scope-table';
import AlarmShieldStatusText from '@manyun/monitoring.ui.alarm-shield-status-text';

import styles from './alarm-shield.module.less';

type AlarmShieldScopeType = 'DEVICE' | 'POINT' | 'SPACE';

const tabList: { key: AlarmShieldScopeType; tab: string }[] = [
  {
    key: 'DEVICE',
    tab: '设备维度',
  },
  {
    key: 'POINT',
    tab: '测点维度',
  },
  {
    key: 'SPACE',
    tab: '包间维度',
  },
];

export function AlarmShield() {
  const { id } = useParams<{ id: string }>();
  const history = useHistory();
  const [getAlarmShield, { data, loading }] = useLazyAlarmShield();
  const [deleteAlarmShield] = useDeleteAlarmShield();
  const [finishAlarmShield] = useFinishAlarmShield();
  const [, { checkCode }] = useAuthorized();

  const [activeTabKey, setActiveTabKey] = useState<AlarmShieldScopeType>('DEVICE');
  const [deviceSearchValue, setDeviceSearchValue] = useState<string>('');
  const [pointSearchValue, setPointSearchValue] = useState<string>('');
  const [spaceSearchValue, setSpaceSearchValue] = useState<string>('');

  useEffect(() => {
    getAlarmShield({ variables: { id: Number(id) } });
  }, [getAlarmShield, id]);

  const alarmShield = data?.alarmShield;

  const shieldPoints = (
    alarmShield?.pointShieldScopes && alarmShield.pointShieldScopes?.length > 0
      ? alarmShield.pointShieldScopes.map(item => {
          return {
            ...item,
            key: `${item?.guid}${item?.pointCode}`,
          };
        })
      : []
  ) as ShieldPoint[];

  const contentMap: Record<AlarmShieldScopeType, React.ReactNode> = {
    DEVICE: (
      <Space style={{ width: '100%' }} direction="vertical" size="large">
        <Input.Search
          style={{ width: 272 }}
          placeholder="搜索设备编号、名称"
          onSearch={value => setDeviceSearchValue(value)}
        />
        <AlarmShieldScopeTable
          dataIndexs={['deviceName', 'roomName', 'vendor', 'deviceOperationStatus']}
          dataSource={((alarmShield?.deviceShieldScopes ?? []) as ShieldPoint[]).filter(
            item =>
              item.deviceName?.includes(deviceSearchValue) ||
              item.deviceLabel?.includes(deviceSearchValue)
          )}
        />
      </Space>
    ),
    POINT: (
      <Space style={{ width: '100%' }} direction="vertical" size="large">
        <Input.Search
          style={{ width: 272 }}
          placeholder="搜索测点名称、ID"
          onSearch={value => setPointSearchValue(value)}
        />
        <ShieldPointTable
          dataSource={shieldPoints.filter(item => {
            return (
              (item.pointName &&
                item.pointName.toLowerCase().includes(pointSearchValue.toLowerCase())) ||
              (item.pointCode &&
                item.pointCode.toLowerCase().includes(pointSearchValue.toLowerCase()))
            );
          })}
        />
      </Space>
    ),
    SPACE: (
      <Space style={{ width: '100%' }} direction="vertical" size="large">
        <Input.Search
          style={{ width: 272 }}
          placeholder="搜索包间编号、名称"
          onSearch={value => setSpaceSearchValue(value)}
        />
        <AlarmShieldScopeTable
          dataIndexs={['roomName', 'roomType']}
          dataSource={((alarmShield?.spaceShieldScopes ?? []) as ShieldScope[]).filter(item => {
            return (
              (item.roomTag && item.roomTag.includes(spaceSearchValue)) ||
              (item.roomName && item.roomName.includes(spaceSearchValue))
            );
          })}
        />
      </Space>
    ),
  };


  const operationList = useDeepCompareMemo(() => {
    let _operationList = [];
    if(!alarmShield){
      return _operationList
    }
    if(alarmShield.effectStatus.code === 'ACTIVE'){
      _operationList.push('arlyTermination');
    }
    if(checkCode('element_alarm-shield_edit') && alarmShield.effectStatus.code !== 'EXPIRED') {
      _operationList.push('edit');
    }
    if(checkCode('element_alarm-shield_create')) {
      _operationList.push('copy');
    }
    if(checkCode('element_alarm-shield_delete') && alarmShield.effectStatus.code === 'PENDING' ) {
      _operationList.push('delete');
    }
    return _operationList
  },[alarmShield, checkCode])
  
  return (
    <Card loading={loading}>
      {alarmShield && (
        <Space style={{ width: '100%' }} direction="vertical" size="middle">
          <PageHeader
            style={{ padding: 0 }}
            title={
              <Space>
                <div>{alarmShield.name}</div>
                <AlarmShieldStatusText {...alarmShield.effectStatus} />
              </Space>
            }
            onBack={() => history.push(ALARM_SHIELD_LIST_ROUTE_PATH)}
          />
          <Space className={styles.alarmShieldContent} size="large">
            <Typography.Text>机房楼栋： {alarmShield.blockGuid}</Typography.Text>
            <Typography.Text>
              告警屏蔽时间：
              {`${dayjs(alarmShield.startTime).format('YYYY-MM-DD HH:mm:ss')}~${dayjs(
                alarmShield.endTime
              ).format('YYYY-MM-DD HH:mm:ss')}`}
            </Typography.Text>
            <Typography.Text>更新人：{alarmShield.operatorName}</Typography.Text>
            <Typography.Text>
              更新时间：{dayjs(alarmShield.gmtModified).format('YYYY-MM-DD HH:mm:ss')}
            </Typography.Text>
          </Space>
          <Typography.Title showBadge level={5}>
            屏蔽范围
          </Typography.Title>
          <Card
            tabList={tabList}
            activeTabKey={activeTabKey}
            onTabChange={key => {
              setActiveTabKey(key as AlarmShieldScopeType);
            }}
          >
            {contentMap[activeTabKey]}
          </Card>
          {operationList.length > 0 && (
            <FooterToolBar>
              <Space style={{ padding: '8px 8px 8px 16px' }}>
                {alarmShield.effectStatus.code === 'ACTIVE'  && (
                  <Popconfirm
                    title={
                      <div style={{ width: 350 }}>
                        您即将提前结束该屏蔽配置，提前结束后会将该屏蔽配置的屏蔽结束时间改为当前时刻，是否要提前结束？
                      </div>
                    }
                    placement="topLeft"
                    onConfirm={async () => {
                      const { data } = await finishAlarmShield({
                        variables: { ids: [alarmShield.id] },
                      });
                      if (!data?.finishAlarmShield?.success) {
                        message.error(data?.finishAlarmShield?.message);
                        return;
                      }
                      message.success('提前结束成功！');
                      getAlarmShield({ variables: { id: Number(id) } });
                    }}
                  >
                    <Button type="primary">提前结束</Button>
                  </Popconfirm>
                )}
                {checkCode('element_alarm-shield_edit') && alarmShield.effectStatus.code !== 'EXPIRED' &&  (
                  <Button
                    href={generateAlarmShieldEditRoutePath({
                      id: alarmShield.id,
                    })}
                    target="_blank"
                    type={!operationList.includes('arlyTermination') ? 'primary' : undefined}
                  >
                    编辑
                  </Button>
                )}
                {checkCode('element_alarm-shield_create') && (
                  <Button
                    href={generateAlarmShieldEditRoutePath({
                      id: alarmShield.id,
                      mode: 'copy'
                    })}
                    target="_blank"
                    type={(!operationList.includes('arlyTermination') && !operationList.includes('edit') ) ? 'primary' : undefined}
                  >
                    复制
                  </Button>
                )}
                {checkCode('element_alarm-shield_delete') &&
                  alarmShield.effectStatus.code === 'PENDING' && (
                    <Popconfirm
                      title={
                        <div style={{ width: 331 }}>
                          您即将删除选中屏蔽配置，删除后该屏蔽配置不可恢复 是否要删除？
                        </div>
                      }
                      placement="topLeft"
                      onConfirm={async () => {
                        const { data } = await deleteAlarmShield({
                          variables: { ids: [alarmShield.id] },
                        });
                        if (!data?.deleteAlarmShield?.success) {
                          message.error(data?.deleteAlarmShield?.message);
                          return;
                        }
                        message.success('删除成功！');
                        history.push(ALARM_SHIELD_LIST_ROUTE_PATH);
                      }}
                    >
                      <Button type={operationList.length === 1 ? 'primary' : undefined}>删除</Button>
                    </Popconfirm>
                  )}
              </Space>
           </FooterToolBar>
          )}
        </Space>
      )}
    </Card>
  );
}
