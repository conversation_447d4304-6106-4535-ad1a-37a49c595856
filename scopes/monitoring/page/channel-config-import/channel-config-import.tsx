import React, { useMemo, useState } from 'react';
import { useHistory } from 'react-router-dom';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { ExportType } from '@manyun/base-ui.ui.file-export/dist/file-export';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';

import { Upload } from '@manyun/dc-brain.ui.upload';
import { CHANNEL_CONFIG_LIST } from '@manyun/monitoring.route.monitoring-routes';
import { ChannelImport, importChannel } from '@manyun/monitoring.service.import-channel';
import { importChannelTemplate } from '@manyun/monitoring.service.import-channel-template';

import { getChannelImportColumns } from './utils';

export function ChannelConfigImport() {
  const history = useHistory();

  const [loading, setLoading] = useState<boolean>(false);
  const [exportLoading, setExportLoading] = useState<boolean>(false);

  const [channelImport, setChannelImport] = useState<ChannelImport>();

  const handleFileExport = async (type: ExportType) => {
    setExportLoading(true);
    const { error, data } = await importChannelTemplate();
    setExportLoading(false);
    if (error) {
      message.error(error.message);
    }
    return data;
  };

  const errorAlert = useMemo(() => {
    if (!channelImport) {
      return '';
    }
    return (
      <Space>
        <Typography.Text>导入: {channelImport.checkTotal}</Typography.Text>
        <Typography.Text>成功: {channelImport.correctTotal}</Typography.Text>
        <Typography.Text>失败: {channelImport.faultTotal}</Typography.Text>
        <Typography.Text>当前仅展示失败内容</Typography.Text>
      </Space>
    );
  }, [channelImport]);

  return (
    <Card>
      <Space direction="vertical" style={{ width: '100%' }} size="middle">
        <Space size="middle">
          <Upload
            maxCount={1}
            showUploadList={false}
            customRequest={async ({ file, onError, onSuccess }) => {
              setLoading(true);
              const fd = new FormData();
              fd.append('file', file);
              const { data, error } = await importChannel(fd);
              setLoading(false);
              if (error) {
                message.error(error.message);
                onError!(new Error(error.message));
              } else {
                setChannelImport(data);
                onSuccess!(data);
                if (data.faultTotal === 0) {
                  message.success('导入成功！');
                  history.push(CHANNEL_CONFIG_LIST);
                }
              }
            }}
            accept=".csv,.xls,.xlsx"
          >
            <Button type="primary" loading={loading}>
              上传文件
            </Button>
          </Upload>
          <FileExport
            text="下载通道模版"
            filename="通道模版.xls"
            disabled={exportLoading}
            data={type => {
              return handleFileExport(type);
            }}
          />
        </Space>
        {channelImport && <Alert message={errorAlert} type="info" />}
        <Table
          scroll={{ x: 'max-content' }}
          dataSource={channelImport ? channelImport.excelCheckErrDtos : []}
          rowKey="id"
          columns={getChannelImportColumns()}
          loading={loading}
        />
      </Space>
    </Card>
  );
}
