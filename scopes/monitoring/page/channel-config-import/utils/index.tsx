import React from 'react';

import QuestionCircleOutlined from '@ant-design/icons/es/icons/QuestionCircleOutlined';

import { Space } from '@manyun/base-ui.ui.space';
import type { ColumnType } from '@manyun/base-ui.ui.table';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';

import { ChannelImportexcelCheck } from '@manyun/monitoring.service.import-channel';

type ChannelImportColumnKey =
  | 'id'
  | 'blockGuid'
  | 'channelType'
  | 'channelName'
  | 'ip'
  | 'port'
  | 'protocol'
  | 'username'
  | 'password';
export const getChannelImportColumns = () => {
  const getRenderItem = (record: ChannelImportexcelCheck, key: ChannelImportColumnKey) => {
    const { errDto, errMessage } = record;
    return (
      <Space>
        {errMessage[key] ? (
          <Typography.Text type="danger">{errDto[key] ?? '--'}</Typography.Text>
        ) : (
          errDto[key] ?? '--'
        )}
        {errMessage[key] && (
          <Tooltip title={errMessage[key]}>
            <QuestionCircleOutlined style={{ fontSize: 12 }} />
          </Tooltip>
        )}
      </Space>
    );
  };

  const columns: ColumnType<ChannelImportexcelCheck>[] = [
    {
      title: '通道ID',
      dataIndex: 'id',
      fixed: 'left',
      render: (_, record) => {
        return getRenderItem(record, 'id');
      },
    },
    {
      title: '位置',
      dataIndex: 'blockGuid',
      render: (_, record) => {
        return getRenderItem(record, 'blockGuid');
      },
    },
    {
      title: '通道类型',
      dataIndex: 'channelType',
      render: (_, record) => {
        return getRenderItem(record, 'channelType');
      },
    },
    {
      title: '通道名称',
      dataIndex: 'channelName',
      render: (_, record) => {
        return getRenderItem(record, 'channelName');
      },
    },
    {
      title: '通道IP',
      dataIndex: 'ip',
      render: (_, record) => {
        return getRenderItem(record, 'ip');
      },
    },
    {
      title: '通道端口',
      dataIndex: 'port',
      render: (_, record) => {
        return getRenderItem(record, 'port');
      },
    },
    {
      title: '通信协议',
      dataIndex: 'protocol',
      render: (_, record) => {
        return getRenderItem(record, 'protocol');
      },
    },
    {
      title: '用户名',
      dataIndex: 'username',
      render: (_, record) => {
        return getRenderItem(record, 'username');
      },
    },
    {
      title: '密码',
      dataIndex: 'password',
      render: (_, record) => {
        return getRenderItem(record, 'password');
      },
    },
  ];
  return columns;
};
