import React from 'react';

import { QuestionCircleOutlined } from '@ant-design/icons';

import { Space } from '@manyun/base-ui.ui.space';
import type { ColumnType } from '@manyun/base-ui.ui.table';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';

import type { ImportexcelCheck } from '@manyun/monitoring.service.import-local-alarm-rule';

type ImportColumnKey =
  | 'deviceType'
  | 'type'
  | 'pointCode'
  | 'pointName'
  | 'dataType'
  | 'unit'
  | 'validLimits'
  | 'itemId'
  | 'preRule'
  | 'alarmRule'
  | 'triggerInterval'
  | 'recoverInterval'
  | 'alarmLevel'
  | 'alarmType'
  | 'available'
  | 'lock'
  | 'description'
  | 'guidList'
  | 'deviceType';
export const getImportColumns = () => {
  const getRenderItem = (record: ImportexcelCheck, key: ImportColumnKey) => {
    const { errDto, errMessage } = record;
    return (
      <Space>
        {errMessage[key] ? (
          <Typography.Text type="danger">{errDto[key] ?? '--'}</Typography.Text>
        ) : (
          errDto[key] ?? '--'
        )}
        {errMessage[key] && (
          <Tooltip title={errMessage[key]}>
            <QuestionCircleOutlined style={{ fontSize: 12 }} />
          </Tooltip>
        )}
      </Space>
    );
  };

  const columns: ColumnType<ImportexcelCheck>[] = [
    {
      title: '设备类型',
      dataIndex: 'deviceType',
      fixed: 'left',
    },
    {
      title: '测点分类',
      dataIndex: 'type',
    },
    {
      title: '测点编码',
      dataIndex: 'pointCode',
    },
    {
      title: '测点名称',
      dataIndex: 'pointName',
    },
    {
      title: '测点类型',
      dataIndex: 'dataType',
    },
    {
      title: '单位',
      dataIndex: 'unit',
    },
    {
      title: '状态含义',
      dataIndex: 'validLimits',
    },
    {
      title: '告警规则ID',
      dataIndex: 'itemId',
    },
    {
      title: '前置条件',
      dataIndex: 'preRule',
    },
    {
      title: '告警条件',
      dataIndex: 'alarmRule',
    },
    {
      title: '触发观察周期(秒)',
      dataIndex: 'triggerInterval',
    },
    {
      title: '恢复观察周期(秒)',
      dataIndex: 'recoverInterval',
    },
    {
      title: '告警等级',
      dataIndex: 'alarmLevel',
    },
    {
      title: '告警类型',
      dataIndex: 'alarmType',
    },
    {
      title: '启用状态',
      dataIndex: 'available',
    },
    {
      title: '规则锁',
      dataIndex: 'lock',
    },
    {
      title: '处置建议',
      dataIndex: 'description',
    },
    {
      title: '清单',
      dataIndex: 'guidList',
    },
  ];

  return columns.map(item => {
    return {
      ...item,
      render: (_: ImportColumnKey, record: ImportexcelCheck) => {
        return getRenderItem(record, item.dataIndex as ImportColumnKey);
      },
    };
  });
};
