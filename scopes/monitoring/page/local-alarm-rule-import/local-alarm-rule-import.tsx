import React, { useMemo, useState } from 'react';
import { useHistory, useLocation } from 'react-router-dom';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';

import { Upload } from '@manyun/dc-brain.ui.upload';
import { generateLocalAlarmRuleRoutePath } from '@manyun/monitoring.route.monitoring-routes';
import type { ApiResponseData as ImportLocalAlarmRule } from '@manyun/monitoring.service.import-local-alarm-rule';
import { importLocalAlarmRule } from '@manyun/monitoring.service.import-local-alarm-rule';
import { importLocalAlarmRuleTemplate } from '@manyun/monitoring.service.import-local-alarm-rule-template';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

import { getImportColumns } from './utils';

export function LocalAlarmRuleImport() {
  const history = useHistory();
  const { search } = useLocation();

  const { blockGuid } = getLocationSearchMap<{ blockGuid: string }>(search);

  const { idc } = getSpaceGuidMap(blockGuid);

  const [loading, setLoading] = useState<boolean>(false);
  const [exportLoading, setExportLoading] = useState<boolean>(false);

  const [localAlarmRule, setLocalAlarmRule] = useState<ImportLocalAlarmRule>();

  const handleFileExport = async () => {
    setExportLoading(true);
    const { error, data } = await importLocalAlarmRuleTemplate();
    setExportLoading(false);
    if (error) {
      message.error(error.message);
    }
    return data;
  };

  const errorAlert = useMemo(() => {
    if (!localAlarmRule) {
      return '';
    }
    return (
      <Space>
        <Typography.Text>导入: {localAlarmRule.checkTotal}</Typography.Text>
        <Typography.Text>成功: {localAlarmRule.correctTotal}</Typography.Text>
        <Typography.Text>失败: {localAlarmRule.faultTotal}</Typography.Text>
        <Typography.Text>当前仅展示失败内容</Typography.Text>
      </Space>
    );
  }, [localAlarmRule]);

  return (
    <Card>
      <Space direction="vertical" style={{ width: '100%' }} size="middle">
        <Space size="middle">
          <Upload
            maxCount={1}
            showUploadList={false}
            customRequest={async ({ file, onError, onSuccess }) => {
              setLoading(true);
              const fd = new FormData();
              fd.append('file', file);
              fd.append('blockGuid', blockGuid);
              console.log('file', file);
              const { data, error } = await importLocalAlarmRule(fd);

              setLoading(false);
              if (error) {
                message.error(error.message);
                onError!(new Error(error.message));
              } else {
                setLocalAlarmRule(data);
                onSuccess!(data);
                if (data.faultTotal === 0) {
                  message.success('导入成功！');
                  if (idc) {
                    history.push(generateLocalAlarmRuleRoutePath({ idc }));
                  }
                }
              }
            }}
            accept=".csv,.xls,.xlsx"
          >
            <Button type="primary" loading={loading}>
              上传文件
            </Button>
          </Upload>
          <FileExport
            text="下载模版"
            filename="告警规则导入模版.xlsx"
            disabled={exportLoading}
            data={() => {
              return handleFileExport();
            }}
          />
        </Space>
        {localAlarmRule && <Alert message={errorAlert} type="info" />}
        <Table
          scroll={{ x: 'max-content' }}
          tableLayout="fixed"
          dataSource={localAlarmRule ? localAlarmRule.excelCheckErrDtos : []}
          rowKey="id"
          columns={getImportColumns()}
          loading={loading}
        />
      </Space>
    </Card>
  );
}
