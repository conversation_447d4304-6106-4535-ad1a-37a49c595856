import React from 'react';

import type { InputNumberProps } from '@manyun/base-ui.ui.input-number';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

export type AlarmNoticeRuleInputProps = { rule?: number; type?: string } & InputNumberProps;

export const AlarmNoticeRuleInput = ({ rule, type, ...restprops }: AlarmNoticeRuleInputProps) => {
  return (
    <Space align="center">
      <InputNumber
        style={{ width: '120px' }}
        addonAfter="s"
        max={100000}
        min={type === 'ALARM_NOTIFY' || type === 'RECOVERED_NOTIFY' ? 0 : 1}
        disabled={rule === 0}
        precision={0}
        {...restprops}
      />
      {rule !== undefined && Number(rule) === 0 && (
        <Typography.Text type="secondary">即刻通报</Typography.Text>
      )}
    </Space>
  );
};
