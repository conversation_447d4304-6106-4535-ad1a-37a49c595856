import React, { useEffect, useState } from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import type { SelectProps } from '@manyun/base-ui.ui.select';
import { fetchWebhooks } from '@manyun/notification-hub.service.fetch-webhooks';

export type WebhookSelectProps = { blockGuid: string } & Omit<SelectProps, 'options'>;

export const WebhookSelect = ({ blockGuid, ...restprops }: WebhookSelectProps) => {
  const [options, setOptions] = useState<SelectProps['options']>([]);

  useEffect(
    function () {
      (async () => {
        const { error, data } = await fetchWebhooks({
          pageNum: 1,
          pageSize: 500,
          location: [blockGuid],
          status: true,
        });
        if (error) {
          message.error(error.message);
          return;
        }
        setOptions(
          data.data.map(item => {
            return {
              label: item.webHookName,
              value: item.id,
            };
          })
        );
      })();
    },
    [blockGuid]
  );

  return <Select {...restprops} options={options} />;
};
