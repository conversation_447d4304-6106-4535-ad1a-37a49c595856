import React, { useEffect, useState } from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import type { SelectProps } from '@manyun/base-ui.ui.select';

import { fetchDeviceTypeTree } from '@manyun/resource-hub.service.fetch-device-type-tree';

export type AlarmNoticeDeviceTypeSelectProps = { idcTag: string; blockTag: string } & Omit<
  SelectProps,
  'options'
>;

export const AlarmNoticeDeviceTypeSelect = ({
  idcTag,
  blockTag,
  ...restprops
}: AlarmNoticeDeviceTypeSelectProps) => {
  const [options, setOptions] = useState<SelectProps['options']>([]);

  useEffect(
    function () {
      (async () => {
        const { error, data } = await fetchDeviceTypeTree({ numbered: true, idcTag, blockTag });
        if (error) {
          message.error(error.message);
          return;
        }
        setOptions(
          data.data.map(item => {
            return {
              label: item.metaName,
              value: item.metaCode,
            };
          })
        );
      })();
    },
    [blockTag, idcTag]
  );

  return <Select {...restprops} options={options} />;
};
