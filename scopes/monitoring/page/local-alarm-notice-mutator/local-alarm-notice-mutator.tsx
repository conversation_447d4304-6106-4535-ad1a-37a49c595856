import { message } from 'antd';
import React, { useCallback, useEffect, useState } from 'react';
import { useHistory, useParams } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { Space } from '@manyun/base-ui.ui.space';
import { Switch } from '@manyun/base-ui.ui.switch';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { LocalAlarmNoticeConfigurationParams } from '@manyun/monitoring.route.monitoring-routes';
import { LOCAL_ALARM_NOTICE_CONFIGURATIONS_ROUTE_PATH } from '@manyun/monitoring.route.monitoring-routes';
import type { ApiArgs as CreateLocalAlarmNoticeParams } from '@manyun/monitoring.service.create-local-alarm-notice';
import { createLocalAlarmNotice } from '@manyun/monitoring.service.create-local-alarm-notice';
import { fetchLocalAlarmNotice } from '@manyun/monitoring.service.fetch-local-alarm-notice';
import { AlarmLevelSelect } from '@manyun/monitoring.ui.alarm-level-select';
import { AlarmNoticeTypeSelect } from '@manyun/monitoring.ui.alarm-notice-type';
import type { AlarmNoticeObject } from '@manyun/monitoring.ui.local-alarm-notice-object-table';
import { LocalAlarmNoticeObjectTable } from '@manyun/monitoring.ui.local-alarm-notice-object-table';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

import { AlarmNoticeDeviceTypeSelect } from './components/alarm-notice-device-type-select';
import { AlarmNoticeRuleInput } from './components/alarm-notice-rule-input';
import { WebhookSelect } from './components/webhook-select';

export function LocalAlarmNoticeMutator() {
  const [form] = Form.useForm();
  const history = useHistory();
  const locationParams = useParams<LocalAlarmNoticeConfigurationParams>();

  const [defaultInformTargetList, setDefaultInformTargetList] = useState<AlarmNoticeObject[]>();
  const [loading, setLoading] = useState(false);
  const [btnLoading, setBtnLoading] = useState(false);

  const getLocalAlarmNotice = useCallback(
    async (id: number) => {
      setLoading(true);
      const { data, error } = await fetchLocalAlarmNotice({ id });
      setLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      const {
        targetId,
        name,
        type,
        rule,
        alarmLevels,
        deviceType,
        webhook,
        webhookId,
        informTargetList,
      } = data;
      const { idc } = getSpaceGuidMap(data.targetId!);

      const _informTargetList = informTargetList.map(item => {
        return {
          targetId: item.targetId,
          dutyStatus: item.dutyStatus,
          phone: item.channels.includes('PHONE'),
          sms: item.channels.includes('SMS'),
          email: item.channels.includes('EMAIL'),
          internalMsg: item.channels.includes('INTERNAL_MESSAGE'),
        };
      });
      setDefaultInformTargetList(_informTargetList);
      form.setFieldsValue({
        targetId: [idc, targetId],
        name,
        type,
        rule,
        alarmLevels: alarmLevels?.split(','),
        deviceType: deviceType?.split(','),
        webhookIdList: webhookId?.split(',').map(item => Number(item)),
        webhook,
        informTargetList: _informTargetList,
      });
    },
    [form]
  );

  useEffect(() => {
    if (locationParams.id) {
      getLocalAlarmNotice(Number(locationParams.id));
    }
  }, [getLocalAlarmNotice, locationParams.id]);

  const onNoticeTypeChange = (value: string, option: { label: string }) => {
    form.setFieldsValue({ name: option.label });
    if (value === 'ALARM_NOTIFY' || value === 'RECOVERED_NOTIFY') {
      form.setFieldsValue({ rule: 0 });
    } else {
      form.setFieldsValue({ rule: undefined });
    }
  };
  const onSubmit = async () => {
    const values = await form.validateFields();

    const {
      targetId,
      name,
      type,
      rule,
      alarmLevels,
      deviceType,
      webhook,
      webhookIdList,
      informTargetList,
    } = values;

    const params: CreateLocalAlarmNoticeParams = {
      targetId: targetId?.length > 1 ? targetId[1] : undefined,
      targetType: 'BLOCK',
      name,
      type,
      rule,
      alarmLevels: alarmLevels?.join(','),
      deviceType: deviceType?.join(','),
      webhook,
      webhookIdList: webhookIdList?.join(','),
      notified: true,
      informTargetList: informTargetList?.map((item: AlarmNoticeObject) => {
        const _channels = [];
        if (item.phone) {
          _channels.push('PHONE');
        }
        if (item.email) {
          _channels.push('EMAIL');
        }
        if (item.sms) {
          _channels.push('SMS');
        }
        if (item.internalMsg) {
          _channels.push('INTERNAL_MESSAGE');
        }
        return {
          targetId: item.targetId,
          targetType: 'USER',
          dutyStatus: item.dutyStatus,
          channels: _channels.join(','),
        };
      }),
    };
    if (locationParams.type === 'edit') {
      params.id = Number(locationParams.id);
    }
    setBtnLoading(true);
    const { error } = await createLocalAlarmNotice(params);
    setBtnLoading(false);

    if (error) {
      message.error(error.message);
      return;
    }
    if (locationParams.type === 'edit') {
      message.success('更新属地告警通报成功！');
    } else {
      message.success('创建属地告警通报成功！');
    }
    history.push(LOCAL_ALARM_NOTICE_CONFIGURATIONS_ROUTE_PATH);
  };

  const type = Form.useWatch('type', form);
  const rule = Form.useWatch('rule', form);
  const webhook = Form.useWatch('webhook', form);
  const targetId = Form.useWatch('targetId', form);
  const blockGuid = targetId?.length > 1 ? targetId[1] : '';
  const { idc, block } = getSpaceGuidMap(blockGuid);
  return (
    <Card loading={loading}>
      <Space style={{ width: '75%' }} size="middle" direction="vertical">
        <Typography.Title showBadge level={5}>
          {locationParams.type === 'edit' ? '编辑' : '新建'}属地通报规则
        </Typography.Title>
        <Form
          form={form}
          initialValues={{ webhook: false }}
          labelCol={{ span: 3 }}
          wrapperCol={{ span: 21 }}
        >
          <Form.Item
            label="机房楼栋"
            name="targetId"
            rules={[
              {
                required: true,
                message: '楼栋必填',
              },
              {
                type: 'array',
                len: 2,
                message: '必须选择到楼栋',
              },
            ]}
          >
            <LocationCascader
              style={{ width: 422 }}
              nodeTypes={['IDC', 'BLOCK']}
              authorizedOnly
              allowClear
              disabled={locationParams.type === 'edit'}
              onChange={() => {
                form.setFieldsValue({ informTargetList: [], webhookIdList: undefined });
              }}
            />
          </Form.Item>
          <Form.Item
            label="通报类型"
            name="type"
            rules={[{ required: true, message: '通报类型必填' }]}
          >
            <AlarmNoticeTypeSelect
              style={{ width: 422 }}
              onChange={(value, option) => onNoticeTypeChange(value, option as { label: string })}
            />
          </Form.Item>
          <Form.Item
            label="通报名称"
            name="name"
            rules={[
              {
                required: true,
                whitespace: true,
                message: '通报名称必填',
              },
              {
                type: 'string',
                max: 100,
                message: '最多输入 100 个字符！',
              },
            ]}
          >
            <Input style={{ width: 422 }} />
          </Form.Item>
          <Form.Item
            label="通报条件"
            name="rule"
            rules={[{ required: true, message: '通报条件必填' }]}
          >
            <AlarmNoticeRuleInput type={type} rule={rule} />
          </Form.Item>
          <Form.Item
            label="适用等级"
            name="alarmLevels"
            rules={[{ required: true, message: '适用等级必填' }]}
          >
            <AlarmLevelSelect style={{ width: 422 }} trigger="onDidMount" mode="multiple" />
          </Form.Item>
          {idc && block && (
            <Form.Item
              label="适用专业"
              name="deviceType"
              rules={[{ required: true, message: '适用专业必填' }]}
            >
              <AlarmNoticeDeviceTypeSelect
                style={{ width: 422 }}
                idcTag={idc}
                blockTag={block}
                allowClear
                mode="multiple"
              />
            </Form.Item>
          )}
          <Form.Item
            label="webhook通报"
            name="webhook"
            tooltip="通报会发送到告警所在的楼栋群"
            required
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
          {blockGuid && webhook && (
            <Form.Item
              name="webhookIdList"
              rules={[{ required: true, message: 'weebhook群必填' }]}
              wrapperCol={{ span: 21, offset: 3 }}
            >
              <WebhookSelect
                style={{ width: 422 }}
                blockGuid={blockGuid}
                mode="multiple"
                maxTagCount="responsive"
              />
            </Form.Item>
          )}
        </Form>
      </Space>
      <Form form={form} layout="vertical">
        {blockGuid && (
          <Form.Item
            label="通报对象"
            name="informTargetList"
            labelCol={{ span: undefined }}
            wrapperCol={{ span: undefined }}
            rules={[{ required: true, message: '通报对象必填' }]}
          >
            <LocalAlarmNoticeObjectTable
              blockGuid={blockGuid}
              savedValue={defaultInformTargetList}
              mode={locationParams.type === 'edit' ? 'edit' : 'create'}
            />
          </Form.Item>
        )}
      </Form>
      <FooterToolBar fixed>
        <Space size={16}>
          <Button type="primary" loading={btnLoading} onClick={onSubmit}>
            提交
          </Button>
          <Button href={LOCAL_ALARM_NOTICE_CONFIGURATIONS_ROUTE_PATH}>取消</Button>
        </Space>
      </FooterToolBar>
    </Card>
  );
}
