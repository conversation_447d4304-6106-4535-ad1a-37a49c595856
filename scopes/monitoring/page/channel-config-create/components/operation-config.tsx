import React, { useEffect, useMemo, useState } from 'react';

import uniqueId from 'lodash/uniqueId';

import { Button } from '@manyun/base-ui.ui.button';
import { Space } from '@manyun/base-ui.ui.space';

import type { Config } from '@manyun/monitoring.model.channel-config';
import { ChannelConfigOperations, TableConfig } from '@manyun/monitoring.ui.channels-table';

import type { Config as ParamConfig } from '../channel-config-create';

export type OperationConfigProps = {
  configList: Config[] | null;
  onChange: (arg: ParamConfig[]) => void;
};

export function OperationConfig({ configList, onChange }: OperationConfigProps) {
  const [tableConfigList, setTableConfigList] = useState<TableConfig[]>([]);

  useEffect(() => {
    if (!configList?.length) {
      setTableConfigList(tableConfigList);
      return;
    }
    const newTableConfigList: TableConfig[] = configList.map(item => {
      return {
        ...item,
        status: 'readOnly',
        id: uniqueId('operationConfig'),
      };
    });
    setTableConfigList([...tableConfigList, ...newTableConfigList]);
    onChange(getConfigParams(newTableConfigList));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [configList]);

  const onOperatorTypeSelect = ({
    option,
    record,
  }: {
    option: { label: string; value: string };
    record: TableConfig;
  }) => {
    const newTableConfigList: TableConfig[] = tableConfigList.map(item => {
      if (record.id === item.id) {
        return {
          ...item,
          operatorType: {
            name: option.label,
            code: option.value,
          },
        };
      } else {
        return item;
      }
    });
    setTableConfigList(newTableConfigList);
  };

  const onRegisterNumChange = (value: string | number, record: TableConfig) => {
    const newTableConfigList: TableConfig[] = tableConfigList.map(item => {
      if (record.id === item.id) {
        return {
          ...item,
          registerNum: String(value),
        };
      } else {
        return item;
      }
    });
    setTableConfigList(newTableConfigList);
  };

  const onAddConfig = () => {
    if (!tableConfigList.length) {
      const newConfig: TableConfig = {
        operatorType: {
          name: '',
          code: '',
        },
        registerNum: '',
        status: 'editing',
        id: uniqueId('operationConfig'),
      };
      setTableConfigList([newConfig, ...tableConfigList]);
    } else {
      const newConfig: TableConfig = tableConfigList.slice(0, 1)[0];
      setTableConfigList([
        { ...newConfig, status: 'editing', id: uniqueId('operationConfig') },
        ...tableConfigList,
      ]);
    }
  };

  const onSave = (record: TableConfig) => {
    const newTableConfigList: TableConfig[] = tableConfigList.map(item => {
      if (record.id === item.id) {
        return {
          ...item,
          status: 'readOnly',
        };
      } else {
        return item;
      }
    });
    setTableConfigList(newTableConfigList);
    onChange(getConfigParams(newTableConfigList));
  };

  const onCancal = () => {
    const newTableConfigList = tableConfigList.filter(item => item.status === 'readOnly');
    setTableConfigList(newTableConfigList);
    onChange(getConfigParams(newTableConfigList));
  };

  const onDelete = (record: TableConfig) => {
    const newTableConfigList = tableConfigList.filter(item => item.id !== record.id);
    setTableConfigList(newTableConfigList);
    onChange(getConfigParams(newTableConfigList));
  };

  const onEdit = (record: TableConfig) => {
    const newTableConfigList: TableConfig[] = tableConfigList.map(item => {
      if (record.id === item.id) {
        return {
          ...item,
          status: 'editing',
        };
      } else {
        return item;
      }
    });
    setTableConfigList(newTableConfigList);
    onChange(getConfigParams(newTableConfigList));
  };

  const getConfigParams = (tableConfiglist: TableConfig[]) => {
    const paramConfigList: ParamConfig[] = tableConfiglist.map(item => {
      return {
        operatorType: item.operatorType.code,
        registerNum: item.registerNum,
      };
    });
    return paramConfigList;
  };

  const isAdd = useMemo(() => {
    return tableConfigList.some(item => item.status === 'editing');
  }, [tableConfigList]);

  return (
    <Space style={{ width: '100%' }} direction="vertical">
      <div>操作配置:</div>
      <Button type="primary" onClick={onAddConfig} disabled={isAdd}>
        添加操作配置
      </Button>
      <ChannelConfigOperations
        tableConfigList={tableConfigList}
        fromPage="create"
        onOperatorTypeSelect={onOperatorTypeSelect}
        onRegisterNumChange={onRegisterNumChange}
        onSave={onSave}
        onEdit={onEdit}
        onDelete={onDelete}
        onCancal={onCancal}
      />
    </Space>
  );
}
