import React, { useEffect, useState } from 'react';
import { useHistory, useParams } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';

import type { ChannelConfigJSON } from '@manyun/monitoring.model.channel-config';
import { CHANNEL_CONFIG_LIST } from '@manyun/monitoring.route.monitoring-routes';
import { createChannelConfig } from '@manyun/monitoring.service.create-channel-config';
import { fetchChannelConfigDetail } from '@manyun/monitoring.service.fetch-channel-config-detail';
import { updateChannelConfig } from '@manyun/monitoring.service.update-channel-config';
import { ChannelProtocolTypeSelect } from '@manyun/monitoring.ui.channel-protocol-type-select';
import { ChannelTypeSelect } from '@manyun/monitoring.ui.channel-type-select';
import { DeviceSelect } from '@manyun/resource-hub.ui.device-select';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';

import { OperationConfig } from './components/operation-config';

export type ChannelConfigCreateProps = {
  mode: 'new' | 'edit' | 'copy';
};

export type Config = {
  operatorType: string;
  registerNum: string;
};
export function ChannelConfigCreate({ mode }: ChannelConfigCreateProps) {
  const history = useHistory();
  const [form] = Form.useForm();
  const { id } = useParams<{ id?: string }>();

  const [loading, setLoading] = useState(false);
  const [btnLoading, setBtnLoading] = useState(false);
  const [channelConfig, setChannelConfig] = useState<ChannelConfigJSON>();
  const [blockGuid, setBlockGuid] = useState<string[]>([]);

  //用于form
  const [configList, setConfigList] = useState<Config[]>();

  const { validateFields, setFieldsValue } = form;

  useEffect(() => {
    (async () => {
      if (id && (mode === 'edit' || mode === 'copy')) {
        setLoading(true);
        const { error, data } = await fetchChannelConfigDetail({ channelIds: [id] });
        setLoading(false);
        if (error) {
          message.error(error.message);
          return;
        }
        const channelConfig = data.data.length ? data.data[0] : null;

        if (!channelConfig) {
          return;
        }
        setChannelConfig(channelConfig);
        setFieldsValue({
          channelName: channelConfig.name,
          ip: channelConfig.ip,
          port: channelConfig.port,
          protocol: channelConfig.protocol,
          channelType: channelConfig.channelType.code,
          blockGuid: channelConfig.blockGuid,
          deviceGuid: channelConfig.device.guid,
          channelStatus: channelConfig.channelStatus.code,
          username: channelConfig.username,
          password: channelConfig.password,
        });
        setBlockGuid(channelConfig.blockGuid.split('.'));
      }
    })();
  }, [id, mode, setFieldsValue]);

  const onSubmit = async () => {
    const values = await validateFields();
    setBtnLoading(true);
    if (mode === 'new' || mode === 'copy') {
      const { error, data } = await createChannelConfig({ ...values, config: configList });
      setBtnLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      message.success('创建成功！');
      history.push(CHANNEL_CONFIG_LIST);
      return data;
    } else {
      const { error, data } = await updateChannelConfig({
        ...values,
        config: configList,
        channelId: id,
      });
      setBtnLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      message.success('更新成功！');
      history.push(CHANNEL_CONFIG_LIST);
      return data;
    }
  };

  const onOperationConfig = (configList: Config[]) => {
    setConfigList(configList);
  };
  const isODCC = Form.useWatch('protocol') === 'ODCC';

  return (
    <Spin spinning={loading}>
      <Card title="基本信息">
        <Form
          layout="horizontal"
          form={form}
          style={{ width: 600 }}
          colon
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 20 }}
        >
          <Form.Item
            label="通道名称"
            name="channelName"
            rules={[
              {
                required: true,
                whitespace: true,
                message: '通道名称必填！',
              },
              {
                max: 20,
                message: '最多输入 20 个字符！',
              },
            ]}
          >
            <Input allowClear style={{ width: 211 }} />
          </Form.Item>
          <Form.Item
            label="通道IP"
            name="ip"
            rules={[
              {
                required: true,
                whitespace: true,
                message: '通道IP必填！',
              },
              {
                pattern:
                  /^((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})){3}$/,
                message: 'IP格式不正确',
              },
              {
                max: 20,
                message: '最多输入 20 个字符！',
              },
            ]}
          >
            <Input allowClear style={{ width: 211 }} />
          </Form.Item>
          <Form.Item
            label="通道端口"
            name="port"
            rules={[
              {
                required: true,
                whitespace: true,
                message: '通道端口必填！',
              },
              {
                pattern: /^([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-5]{2}[0-3][0-5])$/,
                message: '端口格式不正确',
              },
              {
                max: 20,
                message: '最多输入 20 个字符！',
              },
            ]}
          >
            <Input allowClear style={{ width: 211 }} />
          </Form.Item>
          <Form.Item
            label="支持协议"
            name="protocol"
            rules={[
              {
                required: true,
                whitespace: true,
                message: '支持协议必填！',
              },
            ]}
          >
            <ChannelProtocolTypeSelect allowClear style={{ width: 211 }} />
          </Form.Item>
          <Form.Item
            label="用户名"
            name="username"
            rules={[
              {
                required: isODCC,
                message: '用户名必填！',
              },
              {
                max: 20,
                message: '最多输入 20 个字符！',
              },
            ]}
          >
            <Input allowClear style={{ width: 211 }} />
          </Form.Item>

          <Form.Item
            label="密码"
            name="password"
            rules={[
              {
                required: isODCC,
                message: '密码必填！',
              },
              {
                max: 20,
                message: '最多输入 20 个字符！',
              },
            ]}
          >
            <Input allowClear style={{ width: 211 }} />
          </Form.Item>
          <Form.Item
            label="通道类型"
            name="channelType"
            rules={[
              {
                required: true,
                message: '通道类型必填！',
              },
            ]}
          >
            <ChannelTypeSelect allowClear style={{ width: 211 }} />
          </Form.Item>
          <Form.Item
            label="位置"
            name="blockGuid"
            rules={[
              {
                required: true,
                message: '位置必填！',
              },
            ]}
          >
            <LocationTreeSelect
              style={{ width: 211 }}
              disabledTypes={['IDC']}
              showSearch
              authorizedOnly
              onSelect={(value: string) => setBlockGuid(value.split('.'))}
            />
          </Form.Item>
          {!!blockGuid.length && (
            <Form.Item label="通信设备" name="deviceGuid">
              <DeviceSelect
                style={{ width: 211 }}
                allowClear
                disabled={!blockGuid.length}
                idcTag={blockGuid[0]}
                blockTag={blockGuid[1]}
              />
            </Form.Item>
          )}
          <Form.Item
            label="状态"
            name="channelStatus"
            initialValue="OFF"
            rules={[
              {
                required: true,
                message: '状态必填！',
              },
            ]}
          >
            <Radio.Group>
              <Radio key="ON" value="ON">
                启用
              </Radio>
              <Radio key="OFF" value="OFF">
                停用
              </Radio>
            </Radio.Group>
          </Form.Item>
        </Form>
        <OperationConfig
          configList={channelConfig?.config ? channelConfig.config : null}
          onChange={onOperationConfig}
        />
        <FooterToolBar>
          <Space>
            <Button loading={btnLoading} type="primary" onClick={onSubmit}>
              提交
            </Button>
            <Button
              loading={btnLoading}
              onClick={() => {
                history.push(CHANNEL_CONFIG_LIST);
              }}
            >
              取消
            </Button>
          </Space>
        </FooterToolBar>
      </Card>
    </Spin>
  );
}
