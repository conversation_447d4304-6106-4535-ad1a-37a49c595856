import type { LineElementInstance, PolylineElementInstance } from '@manyun/dc-brain.aura-graphix';

import type { FuelPipeType } from '../constants/fuel-pipe.constant';
import { FuelPipeColorsMapper } from '../constants/fuel-pipe.constant';

/**
 * @param element Line or Polyline element
 * @param fuelPipeType Fuel pipe type
 */
export function updateFuelPipeColor(
  element: LineElementInstance | PolylineElementInstance,
  fuelPipeType: FuelPipeType
) {
  const stroke = FuelPipeColorsMapper[fuelPipeType];
  element.set({ stroke, custom: { ...element.custom, stroke, fuelPipeType } });
}
