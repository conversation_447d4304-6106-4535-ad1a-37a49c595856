import type { ValueOf } from 'type-fest';

/** @deprecated Replaced by `type TopologyType` from package `@manyun/dc-brain.config.base` */
export const TopologyTypesMapper = {
  /** 电力拓扑 */
  ELECTRIC_POWER: 'ELECTRIC_POWER',
  /** 暖通拓扑 */
  HVAC: 'HVAC',
  /** 设施包间拓扑 */
  ROOM_FACILITY: 'ROOM_FACILITY',
  /**
   * 燃油系统图
   */
  FUEL_SYSTEM: 'FUEL_SYSTEM',
} as const;

/** @deprecated Replaced by `type TopologyType` from package `@manyun/dc-brain.config.base` */
export type TopologyType = ValueOf<typeof TopologyTypesMapper>;
