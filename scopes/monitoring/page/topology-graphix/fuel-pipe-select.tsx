import React from 'react';

import { observer } from 'mobx-react-lite';

import { Select } from '@manyun/base-ui.ui.select';

import type { LineElementInstance, PolylineElementInstance } from '@manyun/dc-brain.aura-graphix';

import { FuelPipeType } from './constants/fuel-pipe.constant';
import { updateFuelPipeColor } from './utils/update-fuel-pipe-color.util';

export type FuelPipeTypeSelectProps = {
  element: LineElementInstance | PolylineElementInstance;
};

export const FuelPipeTypeSelect = observer(({ element }: FuelPipeTypeSelectProps) => {
  return (
    <Select
      style={{ width: '100%' }}
      size="small"
      value={element.custom?.fuelPipeType}
      options={[
        {
          label: '供油线路',
          value: FuelPipeType.Supply,
        },
        {
          label: '回油线路',
          value: FuelPipeType.Return,
        },
      ]}
      onSelect={(fuelPipeType: FuelPipeType) => {
        updateFuelPipeColor(element, fuelPipeType);
      }}
    />
  );
});
