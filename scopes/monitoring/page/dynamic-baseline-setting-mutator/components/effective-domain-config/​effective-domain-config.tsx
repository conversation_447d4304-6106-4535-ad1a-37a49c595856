import React, { useCallback, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useDeepCompareEffect, useUpdateEffect } from 'react-use';

import { Button } from '@manyun/base-ui.ui.button';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import { EditableProTable } from '@manyun/base-ui.ui.editable-pro-table';
import type { ProColumns } from '@manyun/base-ui.ui.editable-pro-table';
import { Form } from '@manyun/base-ui.ui.form';
import type { FormInstance } from '@manyun/base-ui.ui.form';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import {
  getDeviceTypesAction,
  selectDeviceTypes,
  selectDeviceTypesEntities,
} from '@manyun/resource-hub.state.device-types';
import { getRoomTypeAction } from '@manyun/resource-hub.state.room-type';
import { selectSpaces } from '@manyun/resource-hub.state.space';
import { ResourceTree } from '@manyun/resource-hub.ui.resource-tree';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

export type EffectiveDomainConfigProps = {
  form: FormInstance;
  isCreate: boolean;
};

type DynamicBaselineArea = {
  blockGuid: string;
  roomTag: string;
  targetGuid: string;
  deviceGuid?: string;
};

export function EffectiveDomainConfig({ form, isCreate }: EffectiveDomainConfigProps) {
  const [visible, setVisible] = useState(false);
  const toggleVisible = useCallback(() => setVisible(!visible), [visible]);
  const [resourceKeys, setResourceKeys] = useState<string[]>([]);
  const dispatch = useDispatch();
  const config = useSelector(selectCurrentConfig);
  const configUtil = new ConfigUtil(config);
  const roomDeviceType = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_ROOM);
  const { entities: spaceEntities } = useSelector(selectSpaces);
  const { codes, entities: deviceEntities } = useSelector(selectDeviceTypes);
  const deviceTypeData = useSelector(selectDeviceTypesEntities(codes));
  const formAreaList: DynamicBaselineArea[] = Form.useWatch('areaList', form);
  const blockGuid: string = Form.useWatch('blockGuid', form);

  useDeepCompareEffect(() => {
    if (formAreaList) {
      setResourceKeys(formAreaList.map(area => area.targetGuid));
    }
  }, [formAreaList]);

  React.useEffect(() => {
    dispatch(getRoomTypeAction());
    dispatch(getDeviceTypesAction());
  }, [dispatch]);

  const columns: ProColumns<DynamicBaselineArea>[] = [
    {
      width: 100,
      title: '楼栋',
      dataIndex: 'blockGuid',
    },
    {
      width: 100,
      title: '包间',
      dataIndex: 'roomTag',
      render: (_, record: DynamicBaselineArea) => getSpaceGuidMap(record.targetGuid).room,
    },
    {
      title: '操作',
      width: 100,
      dataIndex: 'option',
      fixed: 'right',
      render: (_, record: DynamicBaselineArea) => (
        <Popconfirm
          title={
            <Space direction="vertical">
              <>是否确定删除</>
            </Space>
          }
          onConfirm={async () => {
            const list = formAreaList.filter(
              (area: { targetGuid: string }) => area.targetGuid !== record.targetGuid
            );
            form.setFieldValue('areaList', list);
            setResourceKeys(list.map(area => area.targetGuid));
          }}
        >
          <Typography.Link>删除</Typography.Link>
        </Popconfirm>
      ),
    },
  ];

  const handleSave = useCallback(() => {
    setVisible(false);
    const areaList = resourceKeys
      .filter(key => getSpaceGuidMap(key).room) // 至少需要包间
      .map(key => {
        //  eg: EC06.A.A1-1.30607
        const keyList = key.split('.');
        const isDevice = keyList.length > 3;
        const deviceGuid = isDevice ? keyList[3] : undefined;
        const targetName = isDevice
          ? deviceTypeData.find(device => device.metaCode === deviceGuid)?.metaName
          : spaceEntities[key]?.name;
        const deviceType =
          isDevice && deviceGuid ? deviceEntities[deviceGuid]?.metaType : roomDeviceType;
        return {
          blockGuid: getSpaceGuidMap(key).idc + '.' + getSpaceGuidMap(key).block,
          targetType: isDevice ? 'DEVICE' : 'ROOM',
          targetGuid: isDevice ? deviceGuid : key,
          roomTag: getSpaceGuidMap(key).room,
          deviceType,
          targetName,
        };
      });
    form.setFieldValue('areaList', areaList);
  }, [deviceEntities, form, resourceKeys, spaceEntities, deviceTypeData, roomDeviceType]);

  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([blockGuid]);
  useUpdateEffect(() => {
    if (isCreate) {
      setExpandedKeys([blockGuid]);
      setResourceKeys([]);
      form.setFieldValue('areaList', []);
    }
  }, [blockGuid, form, isCreate]);

  return (
    <Form.Item noStyle shouldUpdate={(pre, next) => pre.configType !== next.configType}>
      {({ getFieldValue }) => {
        if (getFieldValue('configType') === 'LIMIT') {
          return (
            <>
              <Button style={{ marginBottom: 10 }} disabled={!blockGuid} onClick={toggleVisible}>
                选择生效域
              </Button>
              <Form.Item name="areaList" rules={[{ required: true, message: '请选择生效域' }]}>
                <EditableProTable
                  columns={columns}
                  size="small"
                  rowKey={record => record.targetGuid}
                  recordCreatorProps={false}
                />
              </Form.Item>
              <Drawer
                open={visible}
                width={400}
                title="选择生效域"
                extra={
                  <Space>
                    <Button onClick={() => setVisible(false)}>取消</Button>
                    <Button type="primary" onClick={handleSave}>
                      保存
                    </Button>
                  </Space>
                }
                destroyOnClose
                onClose={() => setVisible(false)}
              >
                <ResourceTree
                  style={{ width: '100%' }}
                  authorizedOnly
                  treeMode={['IDC', 'BLOCK', 'ROOM']}
                  checkable
                  checkableNodes={['ROOM']}
                  defaultCheckedKeys={resourceKeys}
                  showFilter={{ deviceType: false }}
                  spaceGuid={blockGuid}
                  expandedKeys={expandedKeys}
                  onExpandedKeysChange={keys => setExpandedKeys(keys)}
                  onCheck={keys => setResourceKeys(keys as string[])}
                />
              </Drawer>
            </>
          );
        }
        return null;
      }}
    </Form.Item>
  );
}
