import React, { useCallback, useEffect, useState } from 'react';
import { useHistory, useParams } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Form } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Switch } from '@manyun/base-ui.ui.switch';

import { getDynamicBaselineLocales } from '@manyun/monitoring.model.dynamic-baseline-setting';
import type { MutateDynamicBaselineParams } from '@manyun/monitoring.route.monitoring-routes';
import { createOpsScene } from '@manyun/monitoring.service.create-ops-scene';
import { fetchOpsScene } from '@manyun/monitoring.service.fetch-ops-scene';
import { updateOpsScene } from '@manyun/monitoring.service.update-ops-scene';
import { AlarmLevelRadio } from '@manyun/monitoring.ui.alarm-level-select';
import { AlarmTypeRadio } from '@manyun/monitoring.ui.alarm-type';
import { DynamicBaselineConditionCheckbox } from '@manyun/monitoring.ui.dynamic-baseline-condition-select';
import { OpsSceneNotifyWayCheckbox } from '@manyun/monitoring.ui.ops-scene-notify-way-select';
import { DeviceTypeCascader } from '@manyun/resource-hub.ui.device-type-cascader';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import { PointSelect } from '@manyun/resource-hub.ui.point-select';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

import { EffectiveDomainConfig } from './components/effective-domain-config';

enum DynamicBaselineSettingEditorModes {
  Edit = 'edit',
  Copy = 'copy',
}

const DYNAMIC_BASELINE_EDITOR_MODES: Record<string, string> = {
  [DynamicBaselineSettingEditorModes.Edit]: '编辑',
  [DynamicBaselineSettingEditorModes.Copy]: '复制',
};

const layout = {
  labelCol: { span: 7 },
  wrapperCol: { span: 17 },
};

const formListLayout = {
  wrapperCol: { span: 14, offset: 7 },
};

export function DynamicBaselineSettingMutator() {
  const locales = getDynamicBaselineLocales();
  const { type: mutatorType, id: urlId } = useParams<MutateDynamicBaselineParams>();

  const isEdit = mutatorType === 'edit';
  const isCopy = mutatorType === 'copy';
  const isCreate = !isEdit && !isCopy;
  const [form] = Form.useForm();
  const history = useHistory();
  const [submitLoading, setSubmitLoading] = useState<boolean>(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    (async () => {
      if ((isEdit || isCopy) && urlId) {
        setLoading(true);
        const { error, data } = await fetchOpsScene({ id: Number(urlId) });
        setLoading(false);
        if (error) {
          message.error(error.message);
          return;
        }
        if (data && data.pointList.length) {
          form.setFieldsValue({
            ...data,
            ...data.alarm,
            alarmCondition: data.alarm.alarmCondition.split(','),
            deviceType: data.pointList[0].deviceType,
            point: {
              value: data.pointList[0].pointCode,
              label: data.pointList[0].pointName,
            },
            areaList: data.areaList.map(area => ({
              blockGuid: area.blockGuid,
              deviceType: area.deviceType,
              targetType: area.targetType,
              roomGuid: area.roomGuid,
              targetGuid: area.targetType === 'ROOM' ? area.roomGuid : area.deviceGuid,
              targetName: area.targetType === 'ROOM' ? area.roomTag : area.deviceName,
            })),
          });
        }
      }
    })();
  }, [urlId, isEdit, form, isCopy]);

  const submit = useCallback(
    async formValues => {
      setSubmitLoading(true);
      const params = {
        ...formValues,
        scenesName: formValues.name,
        alarmCondition: Array.isArray(formValues?.alarmCondition)
          ? formValues.alarmCondition.join(',')
          : formValues?.alarmCondition,
        notifyWay: Array.isArray(formValues?.notifyWay)
          ? formValues.notifyWay.join(',')
          : formValues?.notifyWay,
        idcTag: getSpaceGuidMap(formValues.blockGuid).idc,
        pointList: [
          {
            deviceType: formValues.deviceType,
            pointCode: formValues?.point?.value,
            pointName: formValues?.point?.label,
          },
        ],
      };
      if (formValues.configType !== 'BLOCK') {
        params.areaList = formValues.areaList;
      }
      if (isEdit) {
        params.id = +urlId;
      }
      const { error } = isEdit ? await updateOpsScene(params) : await createOpsScene(params);
      if (error) {
        message.error(error.message);
      } else {
        message.success('操作成功！');
        history.goBack();
      }
      setSubmitLoading(false);
    },
    [history, isEdit, urlId]
  );

  return (
    <Card
      title={`${isCreate ? '新建' : DYNAMIC_BASELINE_EDITOR_MODES[mutatorType]}动态基线配置`}
      style={{
        minHeight: 'var(--content-height)',
      }}
    >
      <Spin spinning={loading}>
        <Form form={form} {...layout} onFinish={submit}>
          <Row gutter={24}>
            <Col span={24}>
              <Form.Item
                label={locales['blockGuid']}
                name="blockGuid"
                rules={[
                  {
                    required: true,
                  },
                ]}
              >
                <LocationTreeSelect
                  allowClear
                  disabledTypes={['IDC']}
                  authorizedOnly
                  style={{ width: 200 }}
                  disabled={isEdit || isCopy}
                />
              </Form.Item>
              <Form.Item
                label={locales['name']}
                name="name"
                rules={[
                  {
                    required: true,
                  },
                  {
                    max: 100,
                    message: '最多输入 100 个字符！',
                  },
                ]}
              >
                <Input style={{ width: 300 }} />
              </Form.Item>
              <Form.Item
                label={locales['alarmCondition']}
                name="alarmCondition"
                rules={[{ required: true, message: '请选择监测模型' }]}
              >
                <DynamicBaselineConditionCheckbox />
              </Form.Item>

              <Form.Item
                label={locales['alarmType']}
                name="alarmType"
                rules={[{ required: true, message: '请选择告警类型' }]}
              >
                <AlarmTypeRadio />
              </Form.Item>

              <Form.Item
                label={locales['alarmLevel']}
                name="alarmLevel"
                rules={[{ required: true, message: '请选择告警级别' }]}
              >
                <AlarmLevelRadio />
              </Form.Item>

              <Form.Item label={locales['notifyWay']} name="notifyWay">
                <OpsSceneNotifyWayCheckbox disabledOptions={['SMS']} />
              </Form.Item>

              <Form.Item
                label={locales['enabled']}
                name="enabled"
                valuePropName="checked"
                initialValue
              >
                <Switch />
              </Form.Item>

              <Form.Item
                label={locales['deviceType']}
                name="deviceType"
                rules={[{ required: true, message: '请选择设备类型' }]}
              >
                <DeviceTypeCascader
                  dataType={['snDevice']}
                  disabledTypeList={['C0', 'C1']}
                  numbered
                  style={{ width: 300 }}
                  onChange={() => {
                    form.setFieldValue('point', undefined);
                  }}
                />
              </Form.Item>

              <Form.Item noStyle shouldUpdate={(pre, next) => pre.deviceType !== next.deviceType}>
                {({ getFieldValue }) => {
                  if (getFieldValue('deviceType')) {
                    return (
                      <Form.Item
                        label={locales['point']}
                        name="point"
                        rules={[{ required: true, message: '请选择测点' }]}
                      >
                        <PointSelect
                          style={{ width: 300 }}
                          showSearch
                          treeNodeFilterProp="title"
                          labelInValue
                          dataTypeList={['AI']}
                          pointTypeList={[
                            'ORI',
                            'CAL_SPACE',
                            'AGG_SPACE',
                            'CAL_DEVICE',
                            'AGG_DEVICE',
                            'CUSTOM',
                          ]}
                          spaceGuid={getFieldValue('blockGuid')}
                          deviceType={getFieldValue('deviceType')}
                        />
                      </Form.Item>
                    );
                  }
                  return null;
                }}
              </Form.Item>
              <Form.Item noStyle shouldUpdate={(pre, next) => pre.configType !== next.configType}>
                <Form.Item
                  name="configType"
                  label={locales['configType']}
                  rules={[{ required: true, message: '请选择范围' }]}
                >
                  <Radio.Group
                    options={[
                      {
                        label: '不限范围',
                        value: 'BLOCK',
                      },
                      {
                        label: '指定范围',
                        value: 'LIMIT',
                      },
                    ]}
                  />
                </Form.Item>
              </Form.Item>
              <Form.Item {...formListLayout}>
                <EffectiveDomainConfig form={form} isCreate={isCreate} />
              </Form.Item>

              <Form.Item wrapperCol={{ offset: 7, span: 17 }}>
                <Space size="middle">
                  <Button type="primary" htmlType="submit" loading={submitLoading}>
                    提交
                  </Button>
                  <Button onClick={() => history.goBack()}>取消</Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Spin>
    </Card>
  );
}
