import React, { useEffect } from 'react';

import CloseCircleFilled from '@ant-design/icons/es/icons/CloseCircleFilled';
import classNames from 'classnames';

import { Button } from '@manyun/base-ui.ui.button';
import { Row } from '@manyun/base-ui.ui.grid';
import { Typography } from '@manyun/base-ui.ui.typography';

import { ResourcePointDrawer } from '@manyun/resource-hub.ui.resource-point-drawer';
import type { TreePoint } from '@manyun/resource-hub.ui.resource-point-drawer';

import styles from './resource-point-button.module.less';

export type ResourcePointButtonProps = {
  style?: React.CSSProperties;
  className?: string;
  blockGuid?: string;
  value?: TreePoint;
  onChange?: (value?: TreePoint) => void;
};

export default function ResourcePointButton(props: ResourcePointButtonProps) {
  const { className, style, blockGuid, value, onChange } = props;
  const [visible, setVisible] = React.useState(false);

  const [expandedKeys, setExpandedKeys] = React.useState<React.Key[]>([]);
  useEffect(() => {
    if (blockGuid) {
      setExpandedKeys([blockGuid]);
    }
  }, [blockGuid]);

  return (
    <>
      <Button
        style={style}
        className={classNames(styles.button, className)}
        disabled={!blockGuid}
        onClick={() => setVisible(true)}
      >
        <Row justify="space-between" wrap={false} align="middle">
          <Typography.Text ellipsis>
            {/** todo：这边是 form 使用有误，导致 value 可能为 number，优化 form 后可删除该判断 */}
            {value && typeof value !== 'number' ? `${value.target.name}.${value.name}` : '选择测点'}
          </Typography.Text>
          {value && typeof value !== 'number' && (
            <CloseCircleFilled
              className={styles.clearIcon}
              onClick={e => {
                e.stopPropagation();
                onChange?.(undefined);
              }}
            />
          )}
        </Row>
      </Button>
      <ResourcePointDrawer
        open={visible}
        maxCheckedCount={1}
        resourceTreeProps={{
          spaceGuid: blockGuid,
          expandedKeys: expandedKeys,
          onExpandedKeysChange: keys => setExpandedKeys(keys),
        }}
        onClose={() => setVisible(false)}
        onChange={points => onChange?.(points?.[0])}
      />
    </>
  );
}
