import React from 'react';

import CopyOutlined from '@ant-design/icons/es/icons/CopyOutlined';
import DeleteOutlined from '@ant-design/icons/es/icons/DeleteOutlined';
import PlusSquareOutlined from '@ant-design/icons/es/icons/PlusSquareOutlined';
import type { FormListFieldData, FormListOperation } from 'antd/es/form/FormList';

import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import type { FormInstance } from '@manyun/base-ui.ui.form';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';

import {
  FormulaType,
  FormulaTypeMapper,
  SignType,
  SignTypeMapper,
} from '@manyun/monitoring.model.trigger-rule';

import ResourcePointButton from '../resource-point-button';
import styles from './conditions-form-list.module.less';

const symbolOptions = [
  {
    label: SignTypeMapper[SignType.GreaterThanEqual],
    value: SignType.GreaterThanEqual,
  },
  {
    label: SignTypeMapper[SignType.LessThanEqual],
    value: SignType.LessThanEqual,
  },
  {
    label: SignTypeMapper[SignType.GreaterThan],
    value: SignType.GreaterThan,
  },
  {
    label: SignTypeMapper[SignType.LessThan],
    value: SignType.LessThan,
  },
  {
    label: SignTypeMapper[SignType.Equal],
    value: SignType.Equal,
  },
];

const valueTypeOptions = [
  {
    label: FormulaTypeMapper[FormulaType.Custom],
    value: FormulaType.Custom,
  },
  {
    label: FormulaTypeMapper[FormulaType.Point],
    value: FormulaType.Point,
  },
];

type ConditionsFormProps = {
  minCount?: number;
  form: FormInstance;
  name: string;
  blockGuid?: string;
};

export const defaultValue = {
  firstValue: {
    type: FormulaType.Point,
    value: undefined,
  },
  symbol: undefined,
  secondValue: {
    type: FormulaType.Custom,
    value: undefined,
  },
};

export default function ConditionsFormList(props: ConditionsFormProps) {
  const { blockGuid, minCount = 0, form, name } = props;

  return (
    <div className={styles.formListWrapper}>
      <Form.List name={name}>
        {(fields, { add, remove }) => (
          <Space direction="vertical" size={0}>
            {fields.map(field => (
              <ConditionsFormItem
                key={field.key}
                field={field}
                fields={fields}
                name={name}
                form={form}
                remove={remove}
                add={add}
                minCount={minCount}
                blockGuid={blockGuid}
              />
            ))}
            <Button
              type="link"
              compact
              className={styles.add}
              icon={<PlusSquareOutlined />}
              onClick={() => add(defaultValue)}
            >
              添加条件
            </Button>
          </Space>
        )}
      </Form.List>
    </div>
  );
}

type ConditionsFormItemProps = {
  field: FormListFieldData;
  fields: FormListFieldData[];
  form: FormInstance;
  name: string;
  minCount: number;
  blockGuid?: string;
} & Partial<FormListOperation>;

function ConditionsFormItem({
  field,
  fields,
  form,
  name,
  minCount,
  remove,
  add,
  blockGuid,
}: ConditionsFormItemProps) {
  const type = Form.useWatch([name, field.name, 'secondValue', 'type'], form);
  const firstPoint = Form.useWatch([name, field.name, 'firstValue', 'value'], form);

  return (
    <Form.Item key={field.key} noStyle required={false}>
      <Space key={field.key} align="center">
        <Form.Item
          {...field}
          key="firstValue"
          name={[field.name, 'firstValue', 'value']}
          rules={[
            {
              required: true,
              message: '请选择',
            },
          ]}
        >
          <ResourcePointButton
            style={{ maxWidth: '15vw', minWidth: '120px' }}
            blockGuid={blockGuid}
          />
        </Form.Item>
        <Form.Item
          {...field}
          key="symbol"
          name={[field.name, 'symbol']}
          rules={[
            {
              required: true,
              message: '请选择',
            },
          ]}
        >
          <Select style={{ width: 90 }}>
            {symbolOptions.map(option => (
              <Select.Option key={option.value} value={option.value}>
                {option.label}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item
          {...field}
          key="secondValue-type"
          name={[field.name, 'secondValue', 'type']}
          rules={[
            {
              required: true,
              message: '请选择',
            },
          ]}
        >
          <Select style={{ width: 90 }}>
            {valueTypeOptions.map(option => (
              <Select.Option key={option.value} value={option.value}>
                {option.label}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item
          {...field}
          key="secondValue-value"
          name={[field.name, 'secondValue', 'value']}
          /** todo：后续需优化该 formItem，目前杂糅在一起了 */
          rules={[
            {
              validator: (_, value) => {
                if (type === FormulaType.Point) {
                  if (typeof value === 'undefined' || typeof value === 'number') {
                    return Promise.reject(new Error('请选择'));
                  }
                  return Promise.resolve();
                }
                if (typeof value !== 'number') {
                  return Promise.reject(new Error('请输入'));
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          {type === FormulaType.Point ? (
            <ResourcePointButton style={{ maxWidth: '15vw', minWidth: '120px' }} />
          ) : (
            <InputNumber style={{ width: 120 }} addonAfter={firstPoint?.unit} />
          )}
        </Form.Item>
        <CopyOutlined
          className={styles.icon}
          onClick={() => {
            add?.(form.getFieldValue([name, field.name]));
          }}
        />
        {fields.length > minCount ? (
          <DeleteOutlined className={styles.icon} onClick={() => remove?.(field.name)} />
        ) : null}
      </Space>
    </Form.Item>
  );
}
