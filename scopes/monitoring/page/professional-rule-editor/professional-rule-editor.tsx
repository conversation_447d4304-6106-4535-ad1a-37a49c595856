import React, { useEffect, useState } from 'react';
import { useHistory, useLocation, useParams } from 'react-router-dom';
import { useUpdateEffect } from 'react-use';

import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Switch } from '@manyun/base-ui.ui.switch';
import { Typography } from '@manyun/base-ui.ui.typography';

import { getProfessionalRuleLocales } from '@manyun/monitoring.model.professional-rule';
import type {
  ConditionValue,
  RuleCondition,
  RuleJSON,
} from '@manyun/monitoring.model.professional-rule';
import { FormulaType, signTypeFormulaMap } from '@manyun/monitoring.model.trigger-rule';
import { PROFESSIONAL_RULE_LIST_ROUTE_PATH } from '@manyun/monitoring.route.monitoring-routes';
import { createProfessionalRule } from '@manyun/monitoring.service.create-professional-rule';
import type { SvcQuery as Params } from '@manyun/monitoring.service.create-professional-rule';
import { fetchProfessionalRuleDetail } from '@manyun/monitoring.service.fetch-professional-rule-detail';
import { updateProfessionalRule } from '@manyun/monitoring.service.update-professional-rule';
import { ProfessionalRuleTemplateSelect } from '@manyun/monitoring.ui.professional-rule-template-select';
import { DeviceSelect } from '@manyun/resource-hub.ui.device-select';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';
import type { TreePoint } from '@manyun/resource-hub.ui.resource-point-drawer';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

import ConditionsFormList, {
  defaultValue as defaultConditionsValue,
} from './components/conditions-form-list';
import ResourcePointButton from './components/resource-point-button';
import styles from './professional-rule-editor.module.less';

export type ProfessionalRuleEditorProps = {
  mode: 'new' | 'edit';
};

type InitialValues = {
  template?: {
    value: number;
  };
  blockGuid?: string[];
  device?: {
    label: string;
    value: string;
    spaceGuid: { roomTag: string };
  };
  triggerInterval?: number;
  recoverInterval?: number;
  available?: boolean;
  triggerConditions: RuleCondition[] | Array<typeof defaultConditionsValue>;
  preConditions?: RuleCondition[];
  energyPoint?: TreePoint;
};

/** form 默认值 */
const defaultValue: InitialValues = {
  template: undefined,
  blockGuid: undefined,
  device: undefined,
  triggerInterval: undefined,
  recoverInterval: undefined,
  available: true,
  triggerConditions: [defaultConditionsValue],
  preConditions: undefined,
  energyPoint: undefined,
};

const locales = getProfessionalRuleLocales();

export function ProfessionalRuleEditor({ mode }: ProfessionalRuleEditorProps) {
  const [form] = Form.useForm();
  const { id } = useParams<{ id?: string }>();

  // 复制
  const { search } = useLocation();
  const urlSearchParams = new URLSearchParams(search);
  const ruleId = urlSearchParams.get('id');

  // 模版新建规则
  const templateId = urlSearchParams.get('templateId');
  useEffect(() => {
    if (templateId) {
      const value = {
        ...defaultValue,
        template: {
          value: Number(templateId),
        },
      };
      setInitialValues(value);
      form.setFieldsValue(value);
    }
  }, [form, templateId]);

  const [loading, setLoading] = useState(false);
  const [initialValues, setInitialValues] = useState<InitialValues>(defaultValue);
  useEffect(() => {
    (async () => {
      if (mode === 'edit' || ruleId !== null) {
        setLoading(true);
        const { error, data } = await fetchProfessionalRuleDetail({
          id: mode === 'edit' ? Number(id) : Number(ruleId),
        });
        setLoading(false);
        if (error) {
          message.error(error.message);
          return;
        }
        if (data === null) {
          message.error('规则 ID 有误，请选择正确的规则数据！');
          return;
        }

        const value: InitialValues = {
          template: {
            value: data.schemeId,
          },
          blockGuid: data.blockGuid.split('.'),
          device: {
            label: data.device.name,
            value: data.device.guid,
            spaceGuid: {
              roomTag: data.roomTag,
            },
          },
          triggerInterval: data.triggerInterval,
          recoverInterval: data.recoverInterval,
          available: !!data.available,
          triggerConditions: data.ruleJson.triggerConditions,
          preConditions: data.ruleJson.preConditions,
          energyPoint: data.ruleJson.energyPoint,
        };
        setInitialValues(value);
        form.setFieldsValue(value);
      }
    })();
  }, [form, id, mode, ruleId]);

  // submit
  const history = useHistory();
  const [submitLoading, setSubmitLoading] = useState<boolean>(false);
  const submit = async () => {
    const values = await form.validateFields();
    setSubmitLoading(true);
    const ruleJson: RuleJSON = {
      preConditions: values.preConditions,
      triggerConditions: values.triggerConditions,
      energyPoint: values.energyPoint,
    };
    const params: Params = {
      schemeId: values.template.value,
      name: values.template.name,
      bizCode: values.template.bizCode,
      ruleCode: values.template.ruleCode,
      blockGuid: values.blockGuid.join('.'),
      roomTag: values.device.spaceGuid.roomTag,
      device: {
        guid: values.device.guid ?? values.device.value,
        name: values.device.name ?? values.device.label,
      },
      available: !!values.available,
      triggerInterval: values.triggerInterval,
      recoverInterval: values.recoverInterval,
      energyDevice: values.energyPoint?.target?.guid,
      energyDeviceType: values.energyPoint?.deviceType,
      energyPoint: values.energyPoint?.code,
      ruleFormula: generateRuleFormula(ruleJson),
      ruleJson,
    };
    if (mode === 'new') {
      const { error } = await createProfessionalRule(params);
      if (error) {
        message.error(error.message);
      } else {
        message.success('创建成功！');
        history.push(PROFESSIONAL_RULE_LIST_ROUTE_PATH);
      }
    } else {
      const { error } = await updateProfessionalRule({
        ...params,
        id: Number(id),
      });
      if (error) {
        message.error(error.message);
      } else {
        message.success('更新成功！');
        history.push(PROFESSIONAL_RULE_LIST_ROUTE_PATH);
      }
    }
    setSubmitLoading(false);
  };

  const template = Form.useWatch('template', form);
  const blockGuid = Form.useWatch('blockGuid', form);

  useUpdateEffect(() => {
    if (mode === 'new') {
      form.setFields([
        { name: 'device', value: undefined },
        { name: 'triggerConditions', value: defaultValue['triggerConditions'] },
        { name: 'preConditions', value: defaultValue['preConditions'] },
        { name: 'energyPoint', value: defaultValue['energyPoint'] },
      ]);
    }
  }, [blockGuid, form, mode]);

  useEffect(() => {
    if (template !== undefined && mode === 'new') {
      form.setFieldsValue({
        ...form.getFieldsValue(),
        triggerInterval: template.triggerInterval,
        recoverInterval: template.recoverInterval,
      });
    }
  }, [template, form, mode]);

  const device = Form.useWatch('device', form);
  useUpdateEffect(() => {
    if (mode === 'new') {
      form.setFields([{ name: 'template', value: undefined }]);
    }
  }, [device, form, mode]);

  return (
    <Space className={styles.wrapper} size="large" direction="vertical">
      <Row className={styles.header}>
        <Typography.Text className={styles.title}>
          {`${mode === 'new' ? '新建' : '编辑'}专家规则`}
        </Typography.Text>
      </Row>
      <Row className={styles.content}>
        <Col flex="70 70 0">
          <Spin spinning={loading}>
            <Form
              form={form}
              className={styles.form}
              labelCol={{ span: 4 }}
              wrapperCol={{ span: 20 }}
              labelAlign="right"
              initialValues={initialValues}
            >
              <Form.Item
                label={locales['blockGuid']}
                name="blockGuid"
                wrapperCol={{ span: 6 }}
                required
                rules={[
                  {
                    required: true,
                    message: '请选择',
                  },
                  {
                    type: 'array',
                    len: 2,
                    message: '必须选择到楼栋',
                  },
                ]}
              >
                <LocationCascader
                  allowClear
                  authorizedOnly
                  disabled={mode === 'edit'}
                  nodeTypes={['IDC', 'BLOCK']}
                />
              </Form.Item>
              <Form.Item
                label={locales['device']['__self']}
                name="device"
                wrapperCol={{ span: 6 }}
                required
                rules={[
                  {
                    required: true,
                    message: '请选择',
                  },
                ]}
              >
                <DeviceSelect
                  allowClear
                  disabled={blockGuid === undefined || blockGuid.length !== 2 || mode === 'edit'}
                  idcTag={blockGuid?.[1]}
                  blockTag={getSpaceGuidMap(blockGuid?.[1]).block!}
                  labelInValue
                />
              </Form.Item>
              <Form.Item
                label={locales['schemeId']}
                name="template"
                wrapperCol={{ span: 12 }}
                required
                rules={[
                  {
                    required: true,
                    message: '请选择',
                  },
                ]}
              >
                <ProfessionalRuleTemplateSelect
                  allowClear
                  labelInValue
                  deviceType={device?.deviceCategory?.level3}
                  disabled={device === undefined || mode === 'edit'}
                />
              </Form.Item>
              <Form.Item
                label={locales['available']['__self']}
                name="available"
                required
                valuePropName="checked"
              >
                <Switch
                  checkedChildren={locales['available']['true']}
                  unCheckedChildren={locales['available']['false']}
                />
              </Form.Item>
              <Form.Item label={locales['triggerConditions']} required>
                <ConditionsFormList
                  minCount={1}
                  name="triggerConditions"
                  form={form}
                  blockGuid={blockGuid?.[1]}
                />
              </Form.Item>
              <Form.Item label={locales['preConditions']}>
                <ConditionsFormList name="preConditions" form={form} blockGuid={blockGuid?.[1]} />
              </Form.Item>
              <Row>
                <Col span={8}>
                  <Form.Item
                    label={locales['triggerInterval']}
                    name="triggerInterval"
                    labelCol={{ span: 12 }}
                    wrapperCol={{ span: 12 }}
                    required
                    rules={[
                      {
                        required: true,
                        message: '请输入',
                      },
                      {
                        pattern: new RegExp('^\\d+$'),
                        message: '只能为非负整数',
                      },
                      {
                        type: 'number',
                        max: 86400,
                        message: '不能超过 86400',
                      },
                    ]}
                  >
                    <InputNumber style={{ width: '100%' }} addonAfter="秒" />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label={locales['recoverInterval']}
                    name="recoverInterval"
                    labelCol={{ span: 12 }}
                    wrapperCol={{ span: 12 }}
                    required
                    rules={[
                      {
                        required: true,
                        message: '请输入',
                      },
                      {
                        pattern: new RegExp('^\\d+$'),
                        message: '只能为非负整数',
                      },
                      {
                        type: 'number',
                        max: 86400,
                        message: '不能超过 86400',
                      },
                    ]}
                  >
                    <InputNumber style={{ width: '100%' }} addonAfter="秒" />
                  </Form.Item>
                </Col>
              </Row>
              <Form.Item label={locales['energyPoint']} name="energyPoint">
                <ResourcePointButton style={{ maxWidth: '30vw' }} blockGuid={blockGuid?.[1]} />
              </Form.Item>
              <Form.Item wrapperCol={{ offset: 4, span: 20 }}>
                <Space size="middle">
                  <Button type="primary" loading={submitLoading} onClick={() => submit()}>
                    提交
                  </Button>
                  <Button onClick={() => history.push(PROFESSIONAL_RULE_LIST_ROUTE_PATH)}>
                    返回
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Spin>
        </Col>
        <Col flex="30 30 0">
          <section className={styles.textWrapper}>
            {template?.description && (
              <>
                <Typography.Text className={styles.title}>规则介绍</Typography.Text>
                <Typography.Paragraph type="secondary" className={styles.paragraph}>
                  {template.description}
                </Typography.Paragraph>
              </>
            )}
            {template?.scope && (
              <>
                <Typography.Text className={styles.title}>适用范围</Typography.Text>
                <Typography.Paragraph type="secondary" className={styles.paragraph}>
                  {template.scope}
                </Typography.Paragraph>
              </>
            )}
            {template?.triggerDesc && (
              <>
                <Typography.Text className={styles.title}>触发条件配置说明</Typography.Text>
                <Typography.Paragraph type="secondary" className={styles.paragraph}>
                  {template.triggerDesc}
                </Typography.Paragraph>
              </>
            )}
            {template?.preConditionDesc && (
              <>
                <Typography.Text className={styles.title}>前置条件配置说明</Typography.Text>
                <Typography.Paragraph type="secondary" className={styles.paragraph}>
                  {template.preConditionDesc}
                </Typography.Paragraph>
              </>
            )}
            {template?.energyPointDesc && (
              <>
                <Typography.Text className={styles.title}>能效影响配置说明</Typography.Text>
                <Typography.Paragraph type="secondary" className={styles.paragraph}>
                  {template.energyPointDesc}
                </Typography.Paragraph>
              </>
            )}
          </section>
        </Col>
      </Row>
    </Space>
  );
}

/** 根据 ruleJson 生成 ruleFormula */
function generateRuleFormula(ruleJson: RuleJSON) {
  let res = '';
  if (ruleJson.preConditions && ruleJson.preConditions.length !== 0) {
    const conditions = ruleJson.preConditions;
    const conditionsStr = conditions.reduce(
      (acc, cur) =>
        `${acc} && ${getRuleValueStr(cur.firstValue)} ${
          signTypeFormulaMap[cur.symbol]
        } ${getRuleValueStr(cur.secondValue)}`,
      ''
    );
    res = `( ${conditionsStr.substring(4)} ) && `;
  }
  const conditions = ruleJson.triggerConditions;
  const conditionsStr = conditions.reduce(
    (acc, cur) =>
      `${acc} && ${getRuleValueStr(cur.firstValue)} ${
        signTypeFormulaMap[cur.symbol]
      } ${getRuleValueStr(cur.secondValue)}`,
    ''
  );
  res = `${res}( ${conditionsStr.substring(4)} )`;
  return res;
}

function getRuleValueStr(value: ConditionValue) {
  if (value.type === FormulaType.Custom) {
    return value.value;
  }
  const point = value.value;
  if (point.dimension.code === 'DEVICE') {
    return `D_${point.deviceId}_${point.code}`;
  }
  return `S_${point.spaceGuid}_${point.code}`;
}
