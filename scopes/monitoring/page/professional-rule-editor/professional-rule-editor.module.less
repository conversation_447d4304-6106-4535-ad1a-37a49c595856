@import (reference) '@manyun/base-ui.theme.theme/dist/theme.less';

.wrapper {
  width: 100%;
  height: 100%;
  background-color: @body-background;

  .header {
    width: 100%;
    padding: @padding-md @padding-lg;
    border-bottom: 1px solid @border-color-split;
  }

  .title {
    font-size: @font-size-lg;
    font-weight: 500;
  }

  .textWrapper {
    margin-left: @margin-lg;
    padding-left: @padding-lg;
    border-left: 1px solid @border-color-split;

    .title {
      display: inline-block;
      margin-top: @margin-lg;
      margin-bottom: @margin-xs;
      font-size: @font-size-base;

      &:first-child {
        margin-top: 0;
      }
    }

    .paragraph {
      margin-bottom: 0;
      word-break: break-all;
    }
  }
}

.content {
  padding: 0 @padding-lg @padding-lg;
}
