import React, { useEffect, useState } from 'react';
import { useHistory, useLocation } from 'react-router-dom';
import { useUpdateEffect } from 'react-use';

import { Button } from '@manyun/base-ui.ui.button';
import { Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';

import { ProfessionalRuleJSON } from '@manyun/monitoring.model.professional-rule';
import { NEW_PROFESSIONAL_RULE_ROUTE_PATH } from '@manyun/monitoring.route.monitoring-routes';
import { fetchProfessionalRuleList } from '@manyun/monitoring.service.fetch-professional-rule-list';
import { updateProfessionalRuleAvailable } from '@manyun/monitoring.service.update-professional-rule-available';

import FilterFormPopover, {
  defaultFilterFormValue,
  filterFormPopoverContext,
} from './components/filter-form-popover';
import type { FilterFormValue } from './components/filter-form-popover';
import Table from './components/table';
import type { TableProps } from './components/table';
import styles from './professional-rule-list.module.less';

export function ProfessionalRuleList() {
  // 处理页面自规则模版跳转
  const { search } = useLocation();
  const urlSearchParams = new URLSearchParams(search);
  const originalTemplateId = urlSearchParams.get('templateId');
  const templateId =
    originalTemplateId !== null && originalTemplateId !== ''
      ? Number(originalTemplateId)
      : undefined;
  useEffect(() => {
    if (templateId !== undefined) {
      setFilterFormValue(value => ({
        ...value,
        schemeId: templateId,
      }));
      setShouldSearch(true);
    }
  }, [templateId]);

  // 表格分页相关
  const [pagination, setPagination] = useState({ pageNum: 1, pageSize: 10, total: 0 });
  const paginationConfig: TableProps['pagination'] = {
    total: pagination.total,
    current: pagination.pageNum,
    pageSize: pagination.pageSize,
    onChange: (pageNum, pageSize) => {
      setPagination({
        ...pagination,
        pageNum,
        pageSize,
      });
    },
  };
  // 查询条件相关
  const [filterFormValue, setFilterFormValue] = useState<FilterFormValue>(defaultFilterFormValue);
  const [ruleName, setRuleName] = useState<string>();
  // 请求数据
  const [shouldSearch, setShouldSearch] = useState<boolean>(templateId === undefined);
  const [loading, setLoading] = useState(false);
  const [tableData, setTableData] = useState<ProfessionalRuleJSON[]>([]);
  useEffect(() => {
    (async () => {
      if (!shouldSearch) {
        return;
      }
      setShouldSearch(false);
      setLoading(true);
      const location = filterFormValue.location ? filterFormValue.location[0]?.value : undefined;
      const blockGuid = location?.split('.')?.slice(0, 2)?.join('.');
      const { error, data } = await fetchProfessionalRuleList({
        name: ruleName,
        blockGuidList: blockGuid ? [blockGuid] : undefined,
        roomTag: location?.split('.')?.[2],
        bizCode: filterFormValue.ruleType?.[0],
        ruleCode: filterFormValue.ruleType?.[1],
        deviceType: filterFormValue.deviceType,
        available: filterFormValue.available,
        schemeId: filterFormValue.schemeId,
        pageNum: pagination.pageNum,
        pageSize: pagination.pageSize,
      });
      setLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      setTableData(data.data);
      setPagination({ ...pagination, total: data.total });
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [shouldSearch]);
  // 每次切换分页，则重新请求一次数据
  useUpdateEffect(() => {
    setShouldSearch(true);
  }, [pagination.pageNum, pagination.pageSize]);

  // 选择框
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const rowSelection: TableProps['rowSelection'] = {
    selectedRowKeys,
    onChange: newSelectedRowKeys => setSelectedRowKeys(newSelectedRowKeys),
  };

  const handleUpdateAvailable = async (ids: number[], checked: boolean) => {
    const { error } = await updateProfessionalRuleAvailable({
      ids,
      available: checked,
    });
    if (error) {
      message.error(error.message);
      return;
    }
    message.success('更新成功！');
    setShouldSearch(true);
  };

  const history = useHistory();

  return (
    <Space direction="vertical" size="middle" className={styles.container}>
      <Row>
        <Typography.Text className={styles.title}>专家规则列表</Typography.Text>
      </Row>
      <Row align="middle">
        <Space size="middle">
          <Button type="primary" onClick={() => history.push(NEW_PROFESSIONAL_RULE_ROUTE_PATH)}>
            新建规则
          </Button>
          <Input.Search
            placeholder="请输入规则名称"
            value={ruleName}
            onChange={e => setRuleName(e.target.value)}
            onSearch={() => setShouldSearch(true)}
          />
          <filterFormPopoverContext.Provider value={{ filterFormValue, setFilterFormValue }}>
            <FilterFormPopover onSearch={() => setShouldSearch(true)} />
          </filterFormPopoverContext.Provider>
        </Space>
      </Row>
      <Row>
        <Table
          loading={loading}
          dataSource={tableData}
          pagination={paginationConfig}
          refreshTable={() => setShouldSearch(true)}
          rowSelection={rowSelection}
        />
      </Row>
      <Row align="middle" justify="space-between" className={styles.footer}>
        <Space size="middle">
          <span>已选择 {selectedRowKeys.length} 项</span>
          {selectedRowKeys.length !== 0 && (
            <Button type="link" compact onClick={() => setSelectedRowKeys([])}>
              取消选择
            </Button>
          )}
        </Space>
        <Space size="middle">
          <Tooltip title={selectedRowKeys.length === 0 ? '选择内容后，方可批量操作' : ''}>
            {/** 不能直接使用 disabled 属性，否则会导致 tooltip 无法展示 */}
            <Button
              className={selectedRowKeys.length === 0 ? styles.disabled : ''}
              onClick={() => handleUpdateAvailable(selectedRowKeys as number[], true)}
            >
              批量启用
            </Button>
          </Tooltip>
          <Tooltip title={selectedRowKeys.length === 0 ? '选择内容后，方可批量操作' : ''}>
            {/** 不能直接使用 disabled 属性，否则会导致 tooltip 无法展示 */}
            <Button
              className={selectedRowKeys.length === 0 ? styles.disabled : ''}
              onClick={() => handleUpdateAvailable(selectedRowKeys as number[], false)}
            >
              批量停用
            </Button>
          </Tooltip>
        </Space>
      </Row>
    </Space>
  );
}
