import React from 'react';
import { Link } from 'react-router-dom';

import { But<PERSON> } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Switch } from '@manyun/base-ui.ui.switch';
import { Table as BaseTable } from '@manyun/base-ui.ui.table';
import type { TableProps as BaseTableProps, ColumnType } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { ProfessionalRuleJSON } from '@manyun/monitoring.model.professional-rule';
import { getProfessionalRuleLocales } from '@manyun/monitoring.model.professional-rule';
import {
  generateEditProfessionalRuleRoutePath,
  generateNewProfessionalRuleRoutePath,
  generateRoomMonitoringUrl,
  generateSpaceOrDeviceRoutePath,
} from '@manyun/monitoring.route.monitoring-routes';
import { deleteProfessionalRule } from '@manyun/monitoring.service.delete-professional-rule';
import { updateProfessionalRuleAvailable } from '@manyun/monitoring.service.update-professional-rule-available';
import { RuleTypeText } from '@manyun/monitoring.ui.rule-type-text';

const BLANK_PLACEHOLDER = '--';
const locales = getProfessionalRuleLocales();

export type TableProps = Omit<BaseTableProps<ProfessionalRuleJSON>, 'columns' | 'rowKey'> & {
  refreshTable: () => void;
};

export default function Table({ refreshTable, ...rest }: TableProps) {
  const handleDelete = (record: ProfessionalRuleJSON) => {
    Modal.confirm({
      title: '确定删除该条规则？',
      okText: '确定删除',
      cancelText: '取消',
      onOk: async () => {
        const { error } = await deleteProfessionalRule({ id: record.id });
        if (error) {
          message.error(error.message);
          return;
        }
        message.success('删除成功！');
        refreshTable();
      },
    });
  };

  const handleUpdateAvailable = async (record: ProfessionalRuleJSON, checked: boolean) => {
    const { error } = await updateProfessionalRuleAvailable({
      ids: [record.id],
      available: checked,
    });
    if (error) {
      message.error(error.message);
      return;
    }
    message.success('更新成功！');
    refreshTable();
  };

  const columns: Array<ColumnType<ProfessionalRuleJSON>> = [
    {
      title: locales.blockGuid,
      dataIndex: 'blockGuid',
      key: 'blockGuid',
      width: 120,
      render: (_, record) => record.blockGuid || BLANK_PLACEHOLDER,
    },
    {
      title: locales.roomTag,
      dataIndex: 'roomTag',
      key: 'roomTag',
      width: 120,
      render: (_, record) =>
        record.roomTag ? (
          <Link
            target="_blank"
            onClick={e => {
              e.stopPropagation();
              window.open(
                generateRoomMonitoringUrl({
                  idc: record.blockGuid.split('.')[0],
                  block: record.blockGuid.split('.')[1],
                  room: record.roomTag,
                })
              );
            }}
          >
            {record.roomTag}
          </Link>
        ) : (
          BLANK_PLACEHOLDER
        ),
    },
    {
      title: locales.device.name,
      dataIndex: 'deviceName',
      key: 'deviceName',
      width: 180,
      render: (_, record) =>
        record.device.guid ? (
          <Link
            to={generateSpaceOrDeviceRoutePath({
              guid: record.device.guid,
              type: 'DEVICE',
            })}
            target="_blank"
            onClick={e => e.stopPropagation()}
          >
            {record.device.name}
          </Link>
        ) : (
          BLANK_PLACEHOLDER
        ),
    },
    {
      title: locales.bizCode,
      dataIndex: 'bizCode',
      key: 'bizCode',
      width: 120,
      render: (_, record) => <RuleTypeText bizCode={record.bizCode} />,
    },
    {
      title: locales.ruleCode,
      dataIndex: 'ruleCode',
      key: 'ruleCode',
      width: 120,
      render: (_, record) => <RuleTypeText bizCode={record.bizCode} ruleCode={record.ruleCode} />,
    },
    {
      title: locales.name,
      dataIndex: 'name',
      key: 'name',
      width: 240,
      render: value => (
        <Typography.Text ellipsis={{ tooltip: value }}>
          {value || BLANK_PLACEHOLDER}
        </Typography.Text>
      ),
    },
    {
      title: locales.availableForTable.__self,
      dataIndex: 'available',
      key: 'available',
      width: 120,
      render: (_, record) => (
        <Switch
          checkedChildren={locales.availableForTable.true}
          unCheckedChildren={locales.availableForTable.false}
          checked={record.available}
          onChange={checked => handleUpdateAvailable(record, checked)}
        />
      ),
    },
    {
      title: locales.triggerCount,
      dataIndex: 'triggerCount',
      key: 'triggerCount',
      width: 180,
      render: (_, record) => record.triggerCount,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 150,
      fixed: 'right',
      render: (_, record) => (
        <Space size="middle">
          <Link to={generateEditProfessionalRuleRoutePath({ id: record.id })}>编辑</Link>
          <Button type="link" compact onClick={() => handleDelete(record)}>
            删除
          </Button>
          <Link to={generateNewProfessionalRuleRoutePath({ id: record.id })}>复制</Link>
        </Space>
      ),
    },
  ];

  return (
    <BaseTable<ProfessionalRuleJSON>
      columns={columns}
      scroll={{ x: 'max-content' }}
      style={{ width: '100%' }}
      rowKey="id"
      {...rest}
    />
  );
}
