import { createContext } from 'react';

export const defaultFilterFormValue = {
  ruleType: undefined,
  schemeId: undefined,
  location: undefined,
  deviceType: undefined,
  available: undefined,
} as const;

export type FilterFormValue = {
  ruleType?: string[];
  schemeId?: number;
  location?: {
    label: string;
    value: string;
  }[];
  deviceType?: string;
  available?: boolean;
};

export type FilterFormPopoverContext = {
  filterFormValue: FilterFormValue;
  setFilterFormValue: (value: FilterFormValue) => void;
} | null;

export const filterFormPopoverContext = createContext<FilterFormPopoverContext>(null);
