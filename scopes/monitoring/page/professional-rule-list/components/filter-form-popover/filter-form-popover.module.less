@import (reference) '@manyun/base-ui.theme.theme/dist/theme.less';

@overlay-width: 512px;

.overlay {
  width: @overlay-width;
  background-color: @component-background;

  .form {
    padding: @padding-lg;

    :global {
      .@{prefixCls}-form-item {
        margin-bottom: @margin-md;

        .@{prefixCls}-form-item-label {
          padding: 0 0 @padding-xss;
        }
      }
    }
  }

  :global {
    .@{prefixCls}-popover-inner-content {
      padding: 0;
    }
  }
}

:global(.anticon).icon {
  font-size: @font-size-lg;
  color: @text-color-secondary;

  &.primary {
    color: @primary-color;
  }
}
