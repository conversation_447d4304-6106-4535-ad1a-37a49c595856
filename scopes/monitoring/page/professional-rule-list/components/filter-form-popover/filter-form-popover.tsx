import React, { useContext, useEffect, useState } from 'react';

import { FilterOutlined } from '@ant-design/icons';
import classNames from 'classnames';

import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import type { FormProps, Rule } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Popover } from '@manyun/base-ui.ui.popover';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';

import { getProfessionalRuleLocales } from '@manyun/monitoring.model.professional-rule';
import { ProfessionalRuleTemplateSelect } from '@manyun/monitoring.ui.professional-rule-template-select';
import { RuleTypeCascader } from '@manyun/monitoring.ui.rule-type-cascader';
import { DeviceTypeCascader } from '@manyun/resource-hub.ui.device-type-cascader';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';

import { defaultFilterFormValue, filterFormPopoverContext } from './filter-form-popover-context';
import styles from './filter-form-popover.module.less';

export type FilterFormPopoverProps = FilterFormProps;

export default function FilterFormPopover(props: FilterFormPopoverProps) {
  const [open, setOpen] = useState(false);
  const contextValue = useContext(filterFormPopoverContext);
  const iconClassName = classNames(
    styles.icon,
    contextValue?.filterFormValue &&
      Object.values(contextValue.filterFormValue).some(value => value !== undefined) &&
      styles.primary
  );
  return (
    <Popover
      overlayClassName={styles.overlay}
      open={open}
      content={<FilterForm {...props} />}
      placement="bottomRight"
      trigger="click"
      arrowPointAtCenter
      onOpenChange={setOpen}
    >
      <Tooltip title="筛选">
        <FilterOutlined className={iconClassName} />
      </Tooltip>
    </Popover>
  );
}

type FilterFormProps = {
  onSearch: () => void;
};

const locals = getProfessionalRuleLocales();

function FilterForm({ onSearch }: FilterFormProps) {
  const [form] = Form.useForm();
  const formItemProps = [
    {
      label: locals['ruleCode'],
      name: 'ruleType',
      colSpan: 12,
      children: <RuleTypeCascader allowClear />,
    },
    {
      label: locals['schemeId'],
      name: 'schemeId',
      colSpan: 12,
      children: <ProfessionalRuleTemplateSelect allowClear />,
    },
    {
      label: locals['location'],
      name: 'location',
      colSpan: 12,
      children: (
        <LocationTreeSelect
          allowClear
          treeCheckStrictly
          nodeTypes={['IDC', 'BLOCK', 'ROOM']}
          disabledTypes={['IDC']}
          maxTagCount="responsive"
          treeCheckable="true"
          authorizedOnly
        />
      ),
      rules: [
        {
          type: 'array',
          max: 1,
          message: '最多只能选择一项',
        },
      ] as Rule[],
    },
    {
      label: locals['device']['type'],
      name: 'deviceType',
      colSpan: 12,
      children: (
        <DeviceTypeCascader
          numbered
          dataType={['snDevice']}
          disabledTypeList={['C0', 'C1']}
          placeholder="请选择设备类型"
          allowClear
        />
      ),
    },
    {
      label: locals['availableForTable']['__self'],
      name: 'available',
      colSpan: 12,
      children: (
        <Select allowClear>
          <Select.Option value>{locals['available']['true']}</Select.Option>
          <Select.Option value={false}>{locals['available']['false']}</Select.Option>
        </Select>
      ),
    },
  ];
  const contextValue = useContext(filterFormPopoverContext);
  const onValuesChange: FormProps['onValuesChange'] = (_, value) => {
    const formValue = { ...value };
    contextValue?.setFilterFormValue(formValue);
  };

  useEffect(() => {
    form.setFieldsValue({ ...contextValue?.filterFormValue });
  }, [contextValue?.filterFormValue, form]);

  const handleReset = () => {
    form.resetFields();
    contextValue?.setFilterFormValue(defaultFilterFormValue);
    onSearch();
  };

  return (
    <Form form={form} layout="vertical" className={styles.form} onValuesChange={onValuesChange}>
      <Row gutter={16}>
        {formItemProps.map(itemProps => {
          const { label, name, rules, colSpan, children } = itemProps;
          return (
            <Col key={`${name}`} span={colSpan}>
              <Form.Item label={label} name={name} rules={rules}>
                {children}
              </Form.Item>
            </Col>
          );
        })}
      </Row>
      <Row justify="end">
        <Space>
          <Button onClick={() => handleReset()}>重置</Button>
          <Button type="primary" onClick={() => form.validateFields().then(() => onSearch())}>
            搜索
          </Button>
        </Space>
      </Row>
    </Form>
  );
}
