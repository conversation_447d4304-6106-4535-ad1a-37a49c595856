@import (reference) '@manyun/base-ui.theme.theme/dist/theme.less';

@footer-height: @height-base + 2 * @padding-sm;

.container {
  width: 100%;
  height: 100%;
  margin-bottom: @footer-height;
  padding: @padding-lg;
  background-color: @body-background;

  .title {
    font-size: @font-size-lg;
    font-weight: 500;
  }
}

.footer {
  position: fixed;
  width: 100vw;
  height: @footer-height;
  left: 0;
  bottom: 0;
  padding: @padding-sm @padding-lg;
  background-color: @component-background;
  z-index: @zindex-table-fixed;

  &.hidden {
    display: none;
  }

  .disabled {

    &,
    &:hover,
    &:focus,
    &:active {
      color: @disabled-color;
      border-color: @normal-color;
      background: @disabled-bg;
      text-shadow: none;
      box-shadow: none;
      cursor: not-allowed;
    }
  }
}
