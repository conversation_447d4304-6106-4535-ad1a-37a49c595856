import { QuestionCircleOutlined } from '@ant-design/icons';
import React, { useCallback, useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';

import { Card } from '@manyun/base-ui.ui.card';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Divider } from '@manyun/base-ui.ui.divider';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import type {
  InformTarget,
  LocalAlarmNoticeDetail,
} from '@manyun/monitoring.service.fetch-local-alarm-notice';
import { fetchLocalAlarmNotice } from '@manyun/monitoring.service.fetch-local-alarm-notice';
import { AlarmLevelText } from '@manyun/monitoring.ui.alarm-level-text';
import { AlarmNoticeTypeText } from '@manyun/monitoring.ui.alarm-notice-type';
import type { AlarmNoticeObject } from '@manyun/monitoring.ui.local-alarm-notice-object-table';
import { LocalAlarmNoticeObjectTable } from '@manyun/monitoring.ui.local-alarm-notice-object-table';
import type { Webhook } from '@manyun/notification-hub.service.fetch-webhooks';
import { fetchWebhooks } from '@manyun/notification-hub.service.fetch-webhooks';
import { DeviceTypeText } from '@manyun/resource-hub.ui.device-type-text';

export function LocalAlarmNoticeConfiguration() {
  const { id } = useParams<{ id: string }>();

  const [LocalAlarmNoticeDetail, setLocalAlarmNoticeDetail] = useState<LocalAlarmNoticeDetail>();
  const [informTargetList, setInformTargetList] = useState<AlarmNoticeObject[]>();
  const [webhooks, setWebhooks] = useState<Webhook[]>([]);

  const [loading, setLoading] = useState(false);
  const getLocalAlarmNotice = useCallback(async (id: number) => {
    setLoading(true);
    const { data, error } = await fetchLocalAlarmNotice({ id });
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }

    setLocalAlarmNoticeDetail(data);
    setInformTargetList(
      data.informTargetList.map((item: InformTarget) => {
        return {
          targetId: item.targetId,
          dutyStatus: item.dutyStatus,
          phone: item.channels.includes('PHONE'),
          sms: item.channels.includes('SMS'),
          email: item.channels.includes('EMAIL'),
          internalMsg: item.channels.includes('INTERNAL_MESSAGE'),
        };
      })
    );
    const { error: webHookError, data: webhookData } = await fetchWebhooks({
      pageNum: 1,
      pageSize: 500,
      status: true,
    });
    if (error) {
      message.error(webHookError.message);
      return;
    }
    setWebhooks(webhookData.data);
  }, []);

  useEffect(() => {
    if (id) {
      getLocalAlarmNotice(Number(id));
    }
  }, [getLocalAlarmNotice, id]);

  return (
    <Card loading={loading}>
      {LocalAlarmNoticeDetail && (
        <Space style={{ width: '100%' }} size="middle" direction="vertical">
          <Descriptions
            title={
              <Typography.Title showBadge level={5}>
                基本信息
              </Typography.Title>
            }
            column={4}
          >
            <Descriptions.Item label="机房楼栋">
              {LocalAlarmNoticeDetail.targetId}
            </Descriptions.Item>
            <Descriptions.Item label="通报名称">{LocalAlarmNoticeDetail.name}</Descriptions.Item>
            <Descriptions.Item label="通报类型">
              <AlarmNoticeTypeText type={LocalAlarmNoticeDetail.type} />
            </Descriptions.Item>
            <Descriptions.Item label="通报条件">
              {LocalAlarmNoticeDetail.rule === 0
                ? '0s即刻通报'
                : `${LocalAlarmNoticeDetail.rule}通报`}
            </Descriptions.Item>
            <Descriptions.Item label="适用等级">
              <Space>
                {LocalAlarmNoticeDetail.alarmLevels.split(',').map((item: string) => (
                  <AlarmLevelText key={item} code={item} />
                ))}
              </Space>
            </Descriptions.Item>
            <Descriptions.Item label="适用专业">
              <Space>
                {LocalAlarmNoticeDetail.deviceType
                  ?.split(',')
                  .map((item: string) => <DeviceTypeText key={item} code={item} />)}
              </Space>
            </Descriptions.Item>
            <Descriptions.Item
              label={
                <Tooltip title="通报会发送到告警所在的楼栋群">
                  <Space size={4}>
                    webhook
                    <QuestionCircleOutlined />
                  </Space>
                </Tooltip>
              }
            >
              {LocalAlarmNoticeDetail.webhook ? '开启' : '关闭'}
              <Space split={<Divider type="vertical" />} size={0}>
                {LocalAlarmNoticeDetail.webhook ? '开启' : '关闭'}
                <Space split="、" size={0}>
                  {LocalAlarmNoticeDetail.webhookId?.split(',').map(id => {
                    return webhooks.find(item => item.id === Number(id))?.webHookName ?? id;
                  })}
                </Space>
              </Space>
            </Descriptions.Item>
          </Descriptions>
          {informTargetList && LocalAlarmNoticeDetail.targetId && (
            <>
              <Typography.Title showBadge level={5}>
                通报对象
              </Typography.Title>
              <LocalAlarmNoticeObjectTable
                blockGuid={LocalAlarmNoticeDetail.targetId}
                savedValue={informTargetList}
                value={informTargetList}
                isEditable={false}
              />
            </>
          )}
        </Space>
      )}
    </Card>
  );
}
