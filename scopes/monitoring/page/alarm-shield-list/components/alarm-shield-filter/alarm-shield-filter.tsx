import React from 'react';

import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import type { FormInstance, Rule } from '@manyun/base-ui.ui.form';
import { QueryFilter } from '@manyun/base-ui.ui.query-filter';
import { Select } from '@manyun/base-ui.ui.select';

import type { QueryAlarmShieldsParams } from '@manyun/monitoring.gql.client.monitoring';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';

export type AlarmShidleFilterProps = {
  form: FormInstance<Omit<QueryAlarmShieldsParams, 'pageNum' | 'pageSize'>>;
  onSearch: (values: Omit<QueryAlarmShieldsParams, 'pageNum' | 'pageSize'>) => void;
};

export function AlarmShidleFilter({ form, onSearch }: AlarmShidleFilterProps) {
  const items = React.useMemo(
    () => [
      {
        label: '屏蔽开始时间',
        name: 'startTime',
        span: 2,
        control: (
          <DatePicker.RangePicker
            showTime={{ format: 'HH:mm:ss' }}
            format="YYYY-MM-DD HH:mm:ss"
            allowClear
          />
        ),
      },
      {
        label: '屏蔽结束时间',
        name: 'endTime',
        span: 2,
        control: (
          <DatePicker.RangePicker
            showTime={{ format: 'HH:mm:ss' }}
            format="YYYY-MM-DD HH:mm:ss"
            allowClear
          />
        ),
      },
      {
        label: '机房楼栋',
        name: 'blockGuid',
        rules: [
          {
            type: 'array',
            len: 2,
            message: '必须选择到楼栋',
          },
        ] as Rule[],
        control: <LocationCascader authorizedOnly allowClear />,
      },
      {
        label: '生效状态',
        name: 'effectStatus',
        control: (
          <Select
            options={[
              { value: 'ACTIVE', label: '生效中' },
              { value: 'PENDING', label: '待生效' },
              { value: 'EXPIRED', label: '已失效' },
            ]}
            allowClear
          />
        ),
      },
    ],
    []
  );

  return (
    <QueryFilter
      form={form}
      items={items}
      onSearch={params => {
        const { blockGuid, effectStatus, startTime, endTime } = params;

        onSearch({
          blockGuid: blockGuid && blockGuid?.length > 1 ? blockGuid[1] : undefined,
          effectStatus,
          startTime:
            Array.isArray(startTime) && startTime.length > 1 ? startTime[0].valueOf() : undefined,
          endTime:
            Array.isArray(startTime) && startTime.length > 1 ? startTime[1].valueOf() : undefined,
          finishStartTime:
            Array.isArray(endTime) && endTime.length > 1 ? endTime[0].valueOf() : undefined,
          finishEndTime:
            Array.isArray(endTime) && endTime.length > 1 ? endTime[1].valueOf() : undefined,
        });
      }}
      onReset={() => {
        onSearch({
          blockGuid: undefined,
          effectStatus: undefined,
          enable: undefined,
          startTime: undefined,
          endTime: undefined,
        });
        form.resetFields();
      }}
    />
  );
}
