import React from 'react';
import { Link } from 'react-router-dom';

import dayjs from 'dayjs';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import type { ColumnType, TableProps } from '@manyun/base-ui.ui.table';
import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';

import type { AlarmShield } from '@manyun/monitoring.gql.client.monitoring';
import { generateAlarmShieldDetailRoutePath } from '@manyun/monitoring.route.monitoring-routes';

import { AlarmShieldStatusText } from '../alarm-shield-status-text';

const alarmShieldColumns: ColumnType<AlarmShield>[] = [
  {
    title: '名称',
    dataIndex: 'name',
    fixed: 'left',
    render: (_, record) => {
      return (
        <Typography.Text style={{ maxWidth: 220 }} ellipsis={{ tooltip: record.name }}>
          <Link target="_blank" to={generateAlarmShieldDetailRoutePath({ id: record.id })}>
            {record.name}
          </Link>
        </Typography.Text>
      );
    },
  },
  {
    title: '机房楼栋',
    dataIndex: 'blockGuid',
  },
  {
    title: '生效状态',
    dataIndex: 'effectStatus',
    render: status => <AlarmShieldStatusText code={status.code} name={status.name} />,
  },
  {
    title: '屏蔽开始时间',
    dataIndex: 'startTime',
    render: startTime => dayjs(startTime).format('YYYY-MM-DD HH:mm:ss'),
  },
  {
    title: '屏蔽结束时间',
    dataIndex: 'endTime',
    render: endTime => dayjs(endTime).format('YYYY-MM-DD HH:mm:ss'),
  },
  {
    title: '更新人',
    dataIndex: 'operatorName',
    render: operatorName => operatorName ?? '--',
  },
  {
    title: '更新时间',
    dataIndex: 'gmtModified',
    render: gmtModified => (gmtModified ? dayjs(gmtModified).format('YYYY-MM-DD HH:mm:ss') : '--'),
  },
];

export const defaultColumnsConfig = [
  {
    title: '名称',
    dataIndex: 'name',
    show: true,
    disable: false,
  },
  {
    title: '机房楼栋',
    dataIndex: 'blockGuid',
    show: true,
    disable: false,
  },
  {
    title: '生效状态',
    dataIndex: 'effectStatus',
    show: true,
    disable: false,
  },
  {
    title: '屏蔽开始时间',
    dataIndex: 'startTime',
    show: true,
    disable: false,
  },
  {
    title: '屏蔽结束时间',
    dataIndex: 'endTime',
    show: true,
    disable: false,
  },
  {
    title: '更新人',
    dataIndex: 'operatorName',
    show: true,
    disable: false,
  },
  {
    title: '更新时间',
    dataIndex: 'gmtModified',
    show: true,
    disable: false,
  },
];

export type ColumnDataIndex =
  | 'name'
  | 'blockGuid'
  | 'effectStatus'
  | 'startTime'
  | 'endTime'
  | 'operatorName'
  | 'gmtModified';

export type AlarmShieldTableProps = {
  dataIndexs?: ColumnDataIndex[];
  operation?: ColumnType<AlarmShield>;
} & Omit<TableProps<AlarmShield>, 'columns'>;

export function AlarmShieldTable({
  dataIndexs = [
    'name',
    'blockGuid',
    'effectStatus',
    'startTime',
    'endTime',
    'operatorName',
    'gmtModified',
  ],
  operation,
  ...rest
}: AlarmShieldTableProps) {
  const columns = useDeepCompareMemo(() => {
    const newColumns = dataIndexs
      .map(dataIndex => {
        return alarmShieldColumns.find(item => item.dataIndex === dataIndex);
      })
      .filter((item): item is ColumnType<AlarmShield> => item !== undefined);

    if (operation) {
      return [...newColumns, operation];
    }
    return newColumns;
  }, [dataIndexs, operation]);

  return (
    <Table
      columns={columns}
      scroll={{ x: 'max-content' }}
      tableLayout="fixed"
      rowKey="id"
      {...rest}
    />
  );
}
