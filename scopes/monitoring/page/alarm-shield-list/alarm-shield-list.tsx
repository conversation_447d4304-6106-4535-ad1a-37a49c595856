/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-1-3
 *
 * @packageDocumentation
 */
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { ReloadOutlined } from '@ant-design/icons';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import type { ColumnType as EditColumnType } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';

import { useAuthorized } from '@manyun/auth-hub.hook.gql.use-authorized';
import { EditColumns } from '@manyun/dc-brain.ui.edit-columns';
import type {
  AlarmShield,
  QueryAlarmShieldsParams,
} from '@manyun/monitoring.gql.client.monitoring';
import {
  useDeleteAlarmShield,
  useFinishAlarmShield,
  useLazyAlarmShields,
} from '@manyun/monitoring.gql.client.monitoring';
import {
  ALARM_SHIELD_CREATE_ROUTE_PATH,
  generateAlarmShieldEditRoutePath,
} from '@manyun/monitoring.route.monitoring-routes';

import { AlarmShidleFilter } from './components/alarm-shield-filter';
import type { AlarmShieldTableProps } from './components/alarm-shield-table';
import { AlarmShieldTable, defaultColumnsConfig } from './components/alarm-shield-table';

export function AlarmShieldList() {
  const [form] = Form.useForm();
  const [, { checkCode }] = useAuthorized();
  const [getAlarmShields, { data, loading }] = useLazyAlarmShields();
  const [deleteAlarmShield] = useDeleteAlarmShield();
  const [finishAlarmShield] = useFinishAlarmShield();

  const [columnsConfig, setColumnsConfig] = useState<EditColumnType[]>(defaultColumnsConfig);
  const [pagination, setPagination] = useState<{
    pageNum: number;
    pageSize: number;
  }>({ pageNum: 1, pageSize: 10 });
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);

  const filterParamsRef = useRef<Omit<QueryAlarmShieldsParams, 'pageSize' | 'pageNum'>>();

  const fetchAlarmShields = useCallback(
    async (params?: Partial<QueryAlarmShieldsParams>) => {
      const pageNum = params?.pageNum ?? pagination.pageNum;
      const pageSize = params?.pageSize ?? pagination.pageSize;
      setPagination({ pageNum, pageSize });

      await getAlarmShields({
        variables: {
          params: {
            pageNum,
            pageSize,
            ...filterParamsRef.current,
            ...params,
          },
        },
      });
    },
    [getAlarmShields, pagination.pageNum, pagination.pageSize]
  );

  useEffect(() => {
    fetchAlarmShields({ pageSize: 10, pageNum: 1 });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onSearch = async (params: Omit<QueryAlarmShieldsParams, 'pageSize' | 'pageNum'>) => {
    filterParamsRef.current = { ...filterParamsRef.current, ...params };
    fetchAlarmShields({ pageNum: 1 });
  };

  const onDelete = useCallback(
    async (ids: number[]) => {
      const { data } = await deleteAlarmShield({ variables: { ids } });
      if (!data?.deleteAlarmShield?.success) {
        message.error(data?.deleteAlarmShield?.message);
        return;
      }
      message.success('删除成功！');
      fetchAlarmShields();
    },
    [deleteAlarmShield, fetchAlarmShields]
  );

  const operation: AlarmShieldTableProps['operation'] = useMemo(() => {
    return {
      title: '操作',
      fixed: 'right',
      render: (_, record: AlarmShield) => {
        return (
          <Space>
            {record.effectStatus.code === 'ACTIVE' && (
              <Popconfirm
                title={
                  <div style={{ width: 350 }}>
                    您即将提前结束该屏蔽配置，提前结束后会将该屏蔽配置的屏蔽结束时间改为当前时刻，是否要提前结束？
                  </div>
                }
                placement="topRight"
                onConfirm={async () => {
                  const { data } = await finishAlarmShield({ variables: { ids: [record.id] } });
                  if (!data?.finishAlarmShield?.success) {
                    message.error(data?.finishAlarmShield?.message);
                    return;
                  }
                  message.success('提前结束成功！');
                  fetchAlarmShields();
                }}
              >
                <Button type="link" compact>
                  提前结束
                </Button>
              </Popconfirm>
            )}
            {checkCode('element_alarm-shield_edit') && record.effectStatus.code !== 'EXPIRED' && (
              <Button
                type="link"
                href={generateAlarmShieldEditRoutePath({
                  id: record.id,
                })}
                target="_blank"
                compact
              >
                编辑
              </Button>
            )}
            {checkCode('element_alarm-shield_create') && (
              <Button
                type="link"
                href={generateAlarmShieldEditRoutePath({
                  id: record.id,
                  mode: 'copy'
                })}
                target="_blank"
                compact
              >
                复制
              </Button>
            )}
            {checkCode('element_alarm-shield_delete') && record.effectStatus.code === 'PENDING' && (
              <Popconfirm
                title={
                  <div style={{ width: 331 }}>
                    您即将删除该屏蔽配置，删除后该屏蔽配置不可恢复 是否要删除？
                  </div>
                }
                placement="topRight"
                onConfirm={async () => {
                  onDelete([record.id]);
                }}
              >
                <Button type="link" compact>
                  删除
                </Button>
              </Popconfirm>
            )}
          </Space>
        );
      },
    };
  }, [checkCode, fetchAlarmShields, finishAlarmShield, onDelete]);

  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys as number[]);
    },
  };

  return (
    <>
      <Card>
        <Space style={{ width: '100%' }} size="large" direction="vertical">
          <AlarmShidleFilter form={form} onSearch={onSearch} />
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <Space size="middle">
              {checkCode('element_alarm-shield_create') && (
                <Button type="primary" target="_blank" href={ALARM_SHIELD_CREATE_ROUTE_PATH}>
                  新增配置
                </Button>
              )}
              <Input.Search
                style={{ width: 272 }}
                placeholder="请搜索名称"
                onSearch={value => {
                  filterParamsRef.current = { ...filterParamsRef.current, name: value };
                  fetchAlarmShields({ pageNum: 1 });
                }}
                onChange={(e: { target: { value: string } }) => {
                  filterParamsRef.current = { ...filterParamsRef.current, name: e.target.value };
                }}
              />
            </Space>
            <Space size="middle">
              <Button icon={<ReloadOutlined />} onClick={() => fetchAlarmShields()} />
              <Button
                icon={
                  <EditColumns
                    uniqKey="MONITORING_ALARMS_SHIELD_TABLE_COLUMNS"
                    defaultValue={defaultColumnsConfig}
                    allowSetAsFixed={false}
                    listsHeight={500}
                    onChange={(value: EditColumnType[]) => {
                      setColumnsConfig(value);
                    }}
                  />
                }
              />
            </Space>
          </div>
          <AlarmShieldTable
            loading={loading}
            dataIndexs={
              columnsConfig
                .filter(item => item.show)
                .map(item => item.dataIndex) as AlarmShieldTableProps['dataIndexs']
            }
            rowSelection={rowSelection}
            operation={operation}
            dataSource={data?.alarmShields?.data ?? []}
            pagination={{
              total: data?.alarmShields?.total ?? 0,
              current: pagination.pageNum,
              pageSize: pagination.pageSize,
              onChange: (pageNum: number, pageSize: number) => {
                fetchAlarmShields({ pageNum, pageSize });
              },
            }}
          />
        </Space>
      </Card>
      {selectedRowKeys.length > 0 && (
        <FooterToolBar>
          <Space style={{ width: '100%', padding: '8px 8px 8px 16px' }}>
            <Popconfirm
              title="您即将删除选中屏蔽配置，删除后该屏蔽配置不可恢复
              是否要删除？"
              onConfirm={async () => {
                onDelete(selectedRowKeys);
                setSelectedRowKeys([]);
              }}
            >
              <Button>批量删除</Button>
            </Popconfirm>
            <Typography.Text>已选择 {selectedRowKeys.length} 项</Typography.Text>
            <Button type="link" compact onClick={() => setSelectedRowKeys([])}>
              取消选择
            </Button>
          </Space>
        </FooterToolBar>
      )}
    </>
  );
}
