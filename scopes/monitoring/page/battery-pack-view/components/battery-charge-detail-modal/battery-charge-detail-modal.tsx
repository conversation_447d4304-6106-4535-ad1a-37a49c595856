import React, { useMemo, useState } from 'react';
import { useSelector } from 'react-redux';

import moment from 'moment';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Button } from '@manyun/base-ui.ui.button';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Space } from '@manyun/base-ui.ui.space';

import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import type { IntervalKeyMap, TimeRange } from '@manyun/monitoring.chart.duration-select';
import {
  DurationSelect,
  getIntervalByTimeRange,
  getTimeRangeFromNow,
} from '@manyun/monitoring.chart.duration-select';
import { PointsLine } from '@manyun/monitoring.chart.points-line';
import type { BatteryPackChargeRecord } from '@manyun/monitoring.service.fetch-battery-pack-charge-records';
import type { BackendDevice } from '@manyun/resource-hub.model.device';

export function BatteryChargeDetailModal({
  chargeRecord,
  device,
}: {
  device: BackendDevice;
  chargeRecord: BatteryPackChargeRecord;
}) {
  const [visible, setVisible] = React.useState(false);
  const { guid: deviceGuid, deviceType, spaceGuid } = device;
  const config = useSelector(selectCurrentConfig);
  const configUtil = useMemo(() => {
    return new ConfigUtil(config);
  }, [config]);
  const [timeRange, setTimeRange] = useState<TimeRange>([
    moment(chargeRecord.startTime).add(-1, 'm'),
    moment(chargeRecord.endTime).add(1, 'm'),
  ]);
  const defaultInterval = getIntervalByTimeRange([
    moment(chargeRecord.startTime)!,
    moment(chargeRecord.endTime)!,
  ]);
  const [interval, setInterval] = useState(defaultInterval);

  const CHARGE_POINT_RADIO_OPTIONS = useMemo(
    () => [
      {
        text: '组端电压',
        predifiendPointCode: ConfigUtil.constants.pointCodes.BATTERY_UNIT_VOLTAGE,
      },
      {
        text: '组端电流',
        predifiendPointCode: ConfigUtil.constants.pointCodes.BATTERY_UNIT_CURRENT,
      },
      {
        text: '单体电压',
        predifiendPointCode: ConfigUtil.constants.pointCodes.BATTERY_VOLTAGE,
        type: 'BATTERY_VOLTAGE_MAIN', // 主点位
      },
      {
        text: '单体温度',
        predifiendPointCode: ConfigUtil.constants.pointCodes.BATTERY_T,
        type: 'BATTERY_T_MAIN', // 主点位
      },
    ],
    []
  );

  const [activeKey, setActiveKey] = useState<string>(
    CHARGE_POINT_RADIO_OPTIONS[0].predifiendPointCode
  );

  const batteryUnitVoltagePointInfo = configUtil.getPointDefinition(
    device.deviceType,
    configUtil.getPointCode(
      // 组端电压点位信息
      device.deviceType,
      ConfigUtil.constants.pointCodes.BATTERY_UNIT_VOLTAGE
    )
  );

  const batteryTempChildsPointInfo = configUtil.getPointsDefinition(
    // 单体温度(主点位)下拓展点位信息
    deviceType,
    {
      parentPointCode: configUtil.getPointCode(
        device.deviceType,
        ConfigUtil.constants.pointCodes.BATTERY_T
      ),
    }
  );

  const batteryVoltageChildsPointInfo = configUtil.getPointsDefinition(
    // 单体电压(主点位)下拓展点位信息
    deviceType,
    {
      parentPointCode: configUtil.getPointCode(
        device.deviceType,
        ConfigUtil.constants.pointCodes.BATTERY_VOLTAGE
      ),
    }
  );

  const seriesOption = useDeepCompareMemo(() => {
    if (activeKey === ConfigUtil.constants.pointCodes.BATTERY_VOLTAGE) {
      return Object.values(batteryVoltageChildsPointInfo).map(pointInfo => ({
        name: pointInfo.name,
      }));
    } else if (activeKey === ConfigUtil.constants.pointCodes.BATTERY_T) {
      return Object.values(batteryTempChildsPointInfo).map(pointInfo => ({
        name: pointInfo.name,
      }));
    } else if (activeKey === ConfigUtil.constants.pointCodes.BATTERY_UNIT_VOLTAGE) {
      return [
        {
          name: '电压',
        },
      ];
    } else {
      return [
        {
          name: '电流',
        },
      ];
    }
  }, [
    activeKey,
    batteryTempChildsPointInfo,
    batteryVoltageChildsPointInfo,
    batteryUnitVoltagePointInfo.unit,
  ]);

  const pointGuids = useMemo(() => {
    if (
      CHARGE_POINT_RADIO_OPTIONS.findIndex(
        option =>
          option?.type === 'BATTERY_VOLTAGE_MAIN' && option.predifiendPointCode === activeKey
      ) > -1
    ) {
      return Object.keys(batteryVoltageChildsPointInfo).map(code => ({
        deviceGuid,
        pointCode: code,
        deviceType,
      }));
    }

    if (
      CHARGE_POINT_RADIO_OPTIONS.findIndex(
        option => option?.type === 'BATTERY_T_MAIN' && option.predifiendPointCode === activeKey
      ) > -1
    ) {
      return Object.keys(batteryTempChildsPointInfo).map(code => ({
        deviceGuid,
        pointCode: code,
        deviceType,
      }));
    }
    return [
      {
        deviceGuid,
        pointCode: configUtil.getPointCode(deviceType, activeKey!),
        deviceType,
      },
    ];
  }, [
    CHARGE_POINT_RADIO_OPTIONS,
    deviceGuid,
    configUtil,
    deviceType,
    activeKey,
    batteryVoltageChildsPointInfo,
    batteryTempChildsPointInfo,
  ]);

  const onGetParams = ([momentStartTime, momentEndTime]: TimeRange, interval: IntervalKeyMap) => {
    setTimeRange([momentStartTime, momentEndTime]);
    setInterval(interval);
  };

  return (
    <>
      <Button type="link" compact onClick={() => setVisible(true)}>
        查看
      </Button>
      <Modal
        bodyStyle={{ height: '450px' }}
        width={900}
        footer={null}
        open={visible}
        title={`电池组${chargeRecord.type === 1 ? '充电' : '放电'}曲线`}
        onCancel={() => setVisible(false)}
      >
        <Space style={{ width: '100%' }} direction="vertical">
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <Radio.Group value={activeKey} onChange={e => setActiveKey(e.target.value)}>
              {CHARGE_POINT_RADIO_OPTIONS.map(({ text, predifiendPointCode }) => {
                return (
                  <Radio.Button key={predifiendPointCode} value={predifiendPointCode}>
                    {text}
                  </Radio.Button>
                );
              })}
            </Radio.Group>
            <DurationSelect
              showTimeRangeSelect={false}
              rangePickerRanges={{
                近2小时: getTimeRangeFromNow(-2, 'hour'),
                近1天: getTimeRangeFromNow(-1, 'day'),
                近7天: getTimeRangeFromNow(-7, 'day'),
                近1月: getTimeRangeFromNow(-1, 'month'),
                近1年: getTimeRangeFromNow(-1, 'year'),
              }}
              defaultValue={timeRange}
              onChange={onGetParams}
              onDidMount={onGetParams}
            />
          </div>
          <PointsLine
            durationSelectStyle={{ display: 'none' }}
            echartStyle={{ width: '100%' }}
            idcTag={spaceGuid.idcTag!}
            pointGuids={pointGuids}
            seriesOption={seriesOption}
            basicOption={{
              tooltip: {
                confine: true,
              },
            }}
            startTime={moment(timeRange[0]).valueOf()}
            endTime={moment(timeRange[1]).valueOf()}
            interval={interval}
            showToolbox={false}
            showExport
            notMerge
          />
        </Space>
      </Modal>
    </>
  );
}
