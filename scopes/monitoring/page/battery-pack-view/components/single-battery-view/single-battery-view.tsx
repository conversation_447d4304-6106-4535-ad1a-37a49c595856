import React, { useMemo, useState } from 'react';
import { useSelector } from 'react-redux';

import { MenuOutlined } from '@ant-design/icons';
import sortBy from 'lodash.sortby';

import { PetalsOutlined } from '@manyun/base-ui.icons';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';

import { PointsDataType } from '@manyun/monitoring.hook.use-points-data';
import { Battery } from '@manyun/monitoring.service.fetch-battery-list';
import { getMonitoringData } from '@manyun/monitoring.state.subscriptions';
import { generateGetDeviceAlarmStatus } from '@manyun/monitoring.util.get-monitoring-data';
import { BackendDevice } from '@manyun/resource-hub.model.device';

import { BatteryBarEcharts } from '../bar-echarts/bar-echarts';
import { BatteryDeviceView } from './device-view';

const cardHeight = `calc(var(--content-height) - 138px)`;

type SingleBatteryViewProps = {
  /** 电池组下的单体电池 */
  childBatterys: Battery[];
  /** 选中电池组 */
  selectedBattery: BackendDevice;
  pointsData: {
    voltage: PointsDataType;
    tempeture: PointsDataType;
    internalResistance: PointsDataType;
  };
  pointsCode: {
    voltage: string;
    tempeture: string;
    internalResistance: string;
  };
};

/** 视图切换模式 */
type ViewMode = 'point' | 'device';

/** 电池组-单体电池视图 */
export function SingleBatteryView({
  childBatterys,
  selectedBattery,
  pointsData,
  pointsCode,
}: SingleBatteryViewProps) {
  const [viewMode, setViewMode] = useState<ViewMode>('point'); // device-设备查看模式，point-测点数据查看模式

  // 判断当前设备是否告警，如果有 按照告警排序
  const { devicesAlarmsData } = useSelector(getMonitoringData);
  const getDeviceAlarmStatus = generateGetDeviceAlarmStatus(devicesAlarmsData);
  const batteryWithAlarm = sortBy(
    childBatterys.map(item => {
      return {
        ...item,
        alarm: getDeviceAlarmStatus(item.guid).isAlerting,
      };
    }),
    'alarm'
  );

  const voltageSeriesData = useMemo(
    () =>
      pointsData.voltage[selectedBattery.guid] instanceof Map
        ? [...pointsData.voltage[selectedBattery.guid].values()].map(point =>
            point.isBlank ? null : point.value
          )
        : [],
    [pointsData.voltage, selectedBattery.guid]
  );

  const tempetureSeriesData = useMemo(
    () =>
      pointsData.tempeture[selectedBattery.guid] instanceof Map
        ? [...pointsData.tempeture[selectedBattery.guid].values()].map(point =>
            point.isBlank ? null : point.value
          )
        : [],
    [pointsData.tempeture, selectedBattery.guid]
  );

  const internalResistanceSeriesData = useMemo(
    () =>
      pointsData.internalResistance[selectedBattery.guid] instanceof Map
        ? [...pointsData.internalResistance[selectedBattery.guid].values()].map(point =>
            point.isBlank ? null : point.value
          )
        : [],
    [pointsData.internalResistance, selectedBattery.guid]
  );

  return (
    <Card
      title={
        <Button type="link" onClick={() => setViewMode(viewMode === 'device' ? 'point' : 'device')}>
          {viewMode === 'device' ? (
            <PetalsOutlined style={{ fontSize: 17 }} />
          ) : (
            <MenuOutlined style={{ fontSize: 17 }} />
          )}
          切换视图
        </Button>
      }
      bodyStyle={{
        height: cardHeight,
        overflowY: 'auto',
      }}
    >
      {viewMode === 'device' && (
        <BatteryDeviceView
          childBatterys={batteryWithAlarm}
          selectedBattery={selectedBattery}
          pointsData={pointsData}
          pointsCode={pointsCode}
        />
      )}
      {viewMode === 'point' && (
        <BatteryBarEcharts
          childBatterys={childBatterys}
          seriesData={{
            voltage: voltageSeriesData,
            tempeture: tempetureSeriesData,
            internalResistance: internalResistanceSeriesData,
          }}
        />
      )}
    </Card>
  );
}
