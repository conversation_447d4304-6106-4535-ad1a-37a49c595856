import React from 'react';
import { Link } from 'react-router-dom';

import styled from 'styled-components';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Card } from '@manyun/base-ui.ui.card';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';

import { PointsDataType } from '@manyun/monitoring.hook.use-points-data';
import { PointChartText } from '@manyun/monitoring.ui.point-data-renderer';
import { BackendDevice } from '@manyun/resource-hub.model.device';
import { generateDeviceRecordRoutePath } from '@manyun/resource-hub.route.resource-routes';

import { ChildBatterysType } from '../../battery-pack-type';
import { getBatteryCode } from '../../battery-pack-util';

const StyledWrapper = styled.div`
  display: grid;
  grid-template-columns: 20% 20% 20% 20% 20%;
  grid-gap: 10px;
  padding-left: 18px;
  padding-right: 18px;
`;

export type BatteryDeviceViewProps = {
  /** 电池组下的单体电池 */
  childBatterys: ChildBatterysType[];
  /** 选中电池组 */
  selectedBattery: BackendDevice;
  pointsData: {
    /** 单体电压 */
    voltage: PointsDataType;
    /** 单体温度 */
    tempeture: PointsDataType;
    /** 单体内阻 */
    internalResistance: PointsDataType;
  };
  pointsCode: {
    voltage: string;
    tempeture: string;
    internalResistance: string;
  };
};

export function BatteryDeviceView({
  childBatterys,
  selectedBattery,
  pointsData,
  pointsCode,
}: BatteryDeviceViewProps) {
  return (
    <StyledWrapper>
      {childBatterys.map(item => {
        return (
          <Card>
            <Space>
              <Descriptions
                title={
                  <Tooltip title={item.name}>
                    <Typography.Text
                      style={{
                        color: item.alarm ? `var(--${prefixCls}-error-color)` : 'var(--text-color)',
                      }}
                    >
                      {item.name}
                    </Typography.Text>
                  </Tooltip>
                }
              >
                <Descriptions.Item label="所属设备" span={4}>
                  <Link
                    to={generateDeviceRecordRoutePath({ guid: item.parentGuid })}
                    target="_blank"
                  >
                    {item.parentName}
                  </Link>
                </Descriptions.Item>
                <Descriptions.Item label="单体电压" span={4}>
                  <PointChartText
                    spaceGuid={selectedBattery.spaceGuid.blockGuid!}
                    pointData={
                      getBatteryCode(item.guid, pointsCode.voltage)
                        ? pointsData.voltage[selectedBattery.guid]?.get(
                            getBatteryCode(item.guid, pointsCode.voltage)!
                          )!
                        : null
                    }
                    dataType="AI"
                  />
                </Descriptions.Item>
                <Descriptions.Item label="单体温度" span={4}>
                  <PointChartText
                    spaceGuid={selectedBattery.spaceGuid.blockGuid!}
                    pointData={
                      getBatteryCode(item.guid, pointsCode.tempeture)
                        ? pointsData.tempeture[selectedBattery.guid]?.get(
                            getBatteryCode(item.guid, pointsCode.tempeture)!
                          )!
                        : null
                    }
                    dataType="AI"
                  />
                </Descriptions.Item>
                <Descriptions.Item label="单体内阻" span={4}>
                  <PointChartText
                    spaceGuid={selectedBattery.spaceGuid.blockGuid!}
                    pointData={
                      getBatteryCode(item.guid, pointsCode.internalResistance)
                        ? pointsData.internalResistance[selectedBattery.guid]?.get(
                            getBatteryCode(item.guid, pointsCode.internalResistance)!
                          )!
                        : null
                    }
                    dataType="AI"
                  />
                </Descriptions.Item>
              </Descriptions>
            </Space>
          </Card>
        );
      })}
    </StyledWrapper>
  );
}
