import React, { useEffect, useState } from 'react';

import { BarSeriesOption } from 'echarts/charts';
import * as echarts from 'echarts/core';
import isNil from 'lodash.isnil';

import { Bar } from '@manyun/base-ui.chart.bar';

import { Battery } from '@manyun/monitoring.service.fetch-battery-list';
import { PointData } from '@manyun/monitoring.util.get-monitoring-data';

type SeriesDataType = Pick<PointData, 'value'>['value'] | null;
export type BatteryBarEchartsProps = {
  /** 电池组下的单体电池 */
  childBatterys: Battery[];
  seriesData: {
    voltage: SeriesDataType[];
    tempeture: SeriesDataType[];
    internalResistance: SeriesDataType[];
  };
};

type BarChartProps = {
  /** 电池组下的单体电池 */
  childBatterys: Battery[];
  seriesData: SeriesDataType[];
  onChartReady: ((instance: echarts.ECharts) => void) | undefined;
  yAxisUnit: string;
  yAxisName: string;
};

export function BatteryBarEcharts({ childBatterys, seriesData }: BatteryBarEchartsProps) {
  const [readyCharts, setReadyCharts] = useState<echarts.ECharts[]>([]);

  useEffect(() => {
    if (readyCharts.length !== 3) {
      return;
    }
    echarts.connect(readyCharts);
  }, [readyCharts]);

  return (
    <>
      <BarChart
        childBatterys={childBatterys}
        onChartReady={chart => {
          setReadyCharts(prev => [...prev, chart]);
        }}
        seriesData={seriesData.voltage}
        yAxisName="电压"
        yAxisUnit="V"
      />
      <BarChart
        childBatterys={childBatterys}
        onChartReady={chart => {
          setReadyCharts(prev => [...prev, chart]);
        }}
        seriesData={seriesData.tempeture}
        yAxisName="温度"
        yAxisUnit="℃"
      />
      <BarChart
        childBatterys={childBatterys}
        onChartReady={chart => {
          setReadyCharts(prev => [...prev, chart]);
        }}
        seriesData={seriesData.internalResistance}
        yAxisName="内阻"
        yAxisUnit="μΩ"
      />
    </>
  );
}

function BarChart({
  onChartReady,
  childBatterys,
  seriesData,
  yAxisUnit,
  yAxisName,
}: BarChartProps) {
  return (
    <Bar
      style={{ height: '200px' }}
      onReady={onChartReady}
      option={{
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
          },
          formatter: function (params) {
            if (!params || !Array.isArray(params)) {
              return '';
            }
            const series = params
              ?.map(({ name, value }) => {
                const valueTxt = isNil(value) ? '无数据' : `${value} ${yAxisUnit}`;
                return `${name}： ${valueTxt}`;
              })
              .join('<br />');
            return series;
          },
        },
        xAxis: {
          type: 'category',
          data: childBatterys.map(({ name }: { name: string }) => {
            const data = name.split('.');
            return data[data.length - 1];
          }),
        },
        yAxis: { type: 'value', name: yAxisName },
        series: [
          {
            type: 'bar',
            data: seriesData as BarSeriesOption['data'],
          },
        ],
        toolbox: { show: false },
      }}
    />
  );
}
