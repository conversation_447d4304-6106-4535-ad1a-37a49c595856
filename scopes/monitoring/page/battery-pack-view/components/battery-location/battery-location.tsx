import React, { useMemo } from 'react';

import sortBy from 'lodash.sortby';

import { BatteryLocationColorful } from '@manyun/base-ui.icons';
import { Card } from '@manyun/base-ui.ui.card';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';

import { PointsDataType } from '@manyun/monitoring.hook.use-points-data';
import { PointData } from '@manyun/monitoring.util.get-monitoring-data';
import { BackendDevice } from '@manyun/resource-hub.model.device';

export type BatteryLocationProps = {
  cardHeight: string;
  selectedBattery: BackendDevice;
  pointsData: {
    voltage: PointsDataType;
    tempeture: PointsDataType;
    internalResistance: PointsDataType;
  };
  pointsCode: {
    voltage: string;
    tempeture: string;
    internalResistance: string;
  };
};

type BatteryinfoType = {
  title: string;
  valueType: string;
  pointsData: PointData[];
};

export function BatteryLocation({ cardHeight, selectedBattery, pointsData }: BatteryLocationProps) {
  const voltageData = useMemo(
    () =>
      pointsData.voltage[selectedBattery.guid] instanceof Map
        ? [...pointsData.voltage[selectedBattery.guid].values()].filter(point => !point.isBlank)
        : [],
    [pointsData.voltage, selectedBattery.guid]
  );

  const voltageMaxOption = useMemo(
    () =>
      voltageData.filter(
        point => point.value === sortBy(voltageData, 'value')[voltageData.length - 1]?.value
      ),
    [voltageData]
  );

  const voltageMinOption = useMemo(
    () => voltageData.filter(point => point.value === sortBy(voltageData, 'value')[0]?.value),
    [voltageData]
  );

  const tempeturePointsData = useMemo(
    () =>
      pointsData.tempeture[selectedBattery.guid] instanceof Map
        ? [...pointsData.tempeture[selectedBattery.guid].values()].filter(point => !point.isBlank)
        : [],
    [pointsData.tempeture, selectedBattery.guid]
  );
  const tempetureMaxOption = useMemo(
    () =>
      tempeturePointsData.filter(
        point =>
          point.value ===
          sortBy(tempeturePointsData, 'value')[tempeturePointsData.length - 1]?.value
      ),
    [tempeturePointsData]
  );

  const internalResistanceData = useMemo(
    () =>
      pointsData.internalResistance[selectedBattery.guid] instanceof Map
        ? [...pointsData.internalResistance[selectedBattery.guid].values()].filter(
            point => !point.isBlank
          )
        : [],
    [pointsData.internalResistance, selectedBattery.guid]
  );

  const internalResistanceMaxOption = useMemo(
    () =>
      internalResistanceData.filter(
        point =>
          point.value ===
          sortBy(internalResistanceData, 'value')[internalResistanceData.length - 1]?.value
      ),
    [internalResistanceData]
  );

  return (
    <Card
      title="电池组定位"
      bodyStyle={{
        height: cardHeight,
        overflowY: 'auto',
      }}
      headStyle={{
        border: 0,
      }}
    >
      <BatteryInfo title="最高电压电池信息" pointsData={voltageMaxOption} valueType="电压值" />
      <BatteryInfo title="最低电压电池信息" pointsData={voltageMinOption} valueType="电压值" />
      <BatteryInfo
        title="最高内阻电池信息"
        pointsData={internalResistanceMaxOption}
        valueType="内阻值"
      />
      <BatteryInfo title="最高温度电池信息" pointsData={tempetureMaxOption} valueType="温度值" />
    </Card>
  );
}

function BatteryInfo({ title, valueType, pointsData }: BatteryinfoType) {
  return (
    <div style={{ height: '130px', display: 'flex' }}>
      <BatteryLocationColorful style={{ fontSize: 60, marginRight: 16 }} />
      <Space direction="vertical" size="middle">
        <Typography.Text>{title}</Typography.Text>
        <Typography.Text>
          编号：
          {pointsData?.length ? (
            <Tooltip title={pointsData.map(point => point?.pointCode?.slice(-3)).join(',')}>
              <Typography.Text>
                {pointsData.map(point => point?.pointCode?.slice(-3)).join(',')}
              </Typography.Text>
            </Tooltip>
          ) : null}
        </Typography.Text>
        <Typography.Text>
          {valueType}：
          {pointsData?.length ? (
            <Tooltip title={pointsData[0]?.formattedText}>
              <Typography.Text>{pointsData[0].formattedText}</Typography.Text>
            </Tooltip>
          ) : null}
        </Typography.Text>
      </Space>
    </div>
  );
}
