import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import moment from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Tag } from '@manyun/base-ui.ui.tag';
import { toCSVBlob } from '@manyun/base-ui.util.csv';

import { syncCommonDataAction } from '@manyun/dc-brain.state.common';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import type { BatteryPackChargeRecord } from '@manyun/monitoring.service.fetch-battery-pack-charge-records';
import {
  BatteryPackChargeType,
  fetchBatteryPackChargeRecords,
} from '@manyun/monitoring.service.fetch-battery-pack-charge-records';
import type { BackendDevice } from '@manyun/resource-hub.model.device';

import { BatteryChargeDetailModal } from './components/battery-charge-detail-modal/battery-charge-detail-modal';

/** 电池组-充放电记录 */
export type BatteryPackViewProps = {
  device: BackendDevice;
};

export function BatteryPackView({ device }: BatteryPackViewProps) {
  const [form] = Form.useForm();

  const config = useSelector(selectCurrentConfig);
  const [dataSource, setDataSource] = useState<BatteryPackChargeRecord[]>([]);
  const configUtil = useMemo(() => {
    return new ConfigUtil(config);
  }, [config]);
  const [loading, setLoading] = useState(false);
  const dispatch = useDispatch();

  const handleSearchBatteryCharge = useCallback(async () => {
    const formValues = await form.validateFields();
    const params: {
      type?: BatteryPackChargeType;
      startTime?: number;
      endTime?: number;
    } = {};
    if (formValues.type) {
      params.type = formValues.type;
    }
    if (formValues.timeRange) {
      params.startTime = moment(formValues.timeRange[0]).valueOf();
      params.endTime = moment(formValues.timeRange[1]).valueOf();
    }
    setLoading(true);
    const { error, data } = await fetchBatteryPackChargeRecords({
      deviceGuid: device.guid,
      pointCode: configUtil.getPointCode(
        device.deviceType,
        ConfigUtil.constants.pointCodes.BATTERY_UNIT_WORKING_MODE
      ),
      ...params,
    });
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    if (data) {
      setDataSource(data.data);
    }
  }, [configUtil, device.deviceType, device.guid, form]);

  const handleFileExport = useCallback(() => {
    const columns = getColumns(device);
    columns.pop();
    return toCSVBlob(
      {
        columns: columns,
        data: dataSource,
      },
      {
        bom: true,
      }
    );
  }, [dataSource, device]);

  useEffect(() => {
    handleSearchBatteryCharge();
  }, [handleSearchBatteryCharge]);

  const batteryDeviceType = configUtil.getOneDeviceType(
    //电池组设备类型
    ConfigUtil.constants.deviceTypes.BATTERY_UNIT
  );

  useEffect(() => {
    if (batteryDeviceType) {
      dispatch(
        syncCommonDataAction({
          strategy: {
            deviceTypesPointsDefinition: [batteryDeviceType],
          },
        })
      );
    }
  }, [batteryDeviceType, dispatch]);

  return (
    <Card>
      <Space style={{ width: '100%' }} size="middle" direction="vertical">
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <Form form={form} layout="inline">
            <Form.Item label="充放电类型" name="type">
              <Select
                style={{ width: 200 }}
                showSearch
                optionLabelProp="label"
                optionFilterProp="label"
                options={[
                  {
                    label: '充电',
                    value: 1,
                  },
                  {
                    label: '放电',
                    value: 2,
                  },
                ]}
              />
            </Form.Item>
            <Form.Item name="timeRange" label="时间">
              <DatePicker.RangePicker />
            </Form.Item>
            <Form.Item>
              <Space>
                <Button type="primary" onClick={handleSearchBatteryCharge}>
                  搜索
                </Button>
                <Button
                  onClick={async () => {
                    await form.resetFields();
                    handleSearchBatteryCharge();
                  }}
                >
                  重置
                </Button>
              </Space>
            </Form.Item>
          </Form>
          <FileExport
            filename={`${device.name}.csv`}
            disabled={!dataSource.length}
            data={type => {
              return handleFileExport();
            }}
          />
        </div>
        <Table
          dataSource={dataSource}
          columns={getColumns(device)}
          scroll={{ x: 'max-content' }}
          rowKey={record => record.id}
          loading={loading}
        />
      </Space>
    </Card>
  );
}

const getColumns = (device: BackendDevice) => [
  {
    title: '序号',
    dataIndex: 'index',
  },
  {
    title: '类型',
    dataIndex: 'typeName',
    render: (typeName: string, record: BatteryPackChargeRecord) => {
      if (record.type) {
        return (
          <Tag color={`${record.type === BatteryPackChargeType.Charge ? 'success' : 'volcano'}`}>
            {typeName}
          </Tag>
        );
      }
      return '--';
    },
  },
  {
    title: '起始时间',
    dataIndex: 'formatStartTime',
    width: 200,
  },
  {
    title: '结束时间',
    dataIndex: 'formatEndTime',
    width: 200,
  },
  {
    title: '持续时长',
    dataIndex: 'continueTime',
  },
  {
    title: '起始SOC',
    dataIndex: 'formatStartSOCValue',
  },
  {
    title: '结束SOC',
    dataIndex: 'formatEndSOCValue',
  },
  {
    title: 'SOC变动',
    dataIndex: 'soc',
  },
  {
    title: '明细',
    dataIndex: 'detail',
    render: (value: string, record: BatteryPackChargeRecord) => (
      <BatteryChargeDetailModal device={device} chargeRecord={record} />
    ),
  },
];
