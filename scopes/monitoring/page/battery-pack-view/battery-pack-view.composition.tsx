import React from 'react';

import { BatteryPackView } from './battery-pack-view';

export const BasicBatteryPackView = () => {
  return (
    <BatteryPackView
      device={{
        id: 773275,
        guid: '1080101',
        serialNumber: 'SN1080101',
        assetNo: 'A207',
        name: '铅酸阀控蓄电池01',
        tag: '铅酸阀控蓄电池tag.01',
        deviceLabel: '铅酸阀控蓄电池tag.01',
        groupName: null,
        parentGuid: '1080701',
        parentTag: 'A1-1电池组01tag',
        parentName: '电池组01',
        spaceGuid: {
          idcTag: 'EC06',
          blockTag: 'A',
          roomTag: 'A1-1',
          blockGuid: 'EC06.A',
          roomGuid: 'EC06.A.A1-1',
          idcGuid: 'EC06',
        },
        deviceType: '10801',
        topCategory: '1',
        secondCategory: '108',
        vendor: 'zj01',
        productModel: '10801',
        operator: null,
        operatorId: null,
        purchaseTime: '2020-08-10',
        warrantyTime: '2020-08-10',
        warrantyVendor: 'zj01',
        warrantyStatus: {
          code: 'EXPIRED',
          name: '超期',
        },
        operationStatus: {
          code: 'ON',
          name: '已启用',
        },
        assetStatus: {
          code: 'NORMAL',
          name: '正常',
        },
        checkTime: '2020-08-10',
        enableTime: '2020-08-10',
        scrapTime: '2020-08-10',
        purchasePrice: 1,
        netWorth: 1,
        warrantyPrice: 1,
        powerLine: 'A',
        unit: null,
        extendPosition: null,
        onlineStatus: null,
        channelId: null,
        costBlock: '',
        roomType: null,
        roomTypeName: '',
        roomName: '',
        qrCodeText: '',
      }}
    />
  );
};
