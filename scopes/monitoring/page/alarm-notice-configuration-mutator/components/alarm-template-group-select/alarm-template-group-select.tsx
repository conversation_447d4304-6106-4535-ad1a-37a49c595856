import React, { useEffect, useState } from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import type { SelectProps } from '@manyun/base-ui.ui.select';
import { fetchAlarmTemplateByParams } from '@manyun/monitoring.service.fetch-alarm-template-by-params';

export type AlarmTemplateSelectProps = { blockGuid?: string } & Omit<SelectProps, 'options'>;

export const AlarmTemplateSelect = ({ ...restprops }: AlarmTemplateSelectProps) => {
  const [options, setOptions] = useState<SelectProps['options']>([]);

  useEffect(function () {
    (async () => {
      const { error, data } = await fetchAlarmTemplateByParams({
        pageNum: 1,
        pageSize: 500,
        // targetType: 'BLOCK',
        // targetId: blockGuid,
        available: true,
      });
      if (error) {
        message.error(error.message);
        return;
      }
      setOptions(
        data.data.map(item => {
          return {
            label: item.name,
            value: item.id,
          };
        })
      );
    })();
  }, []);

  return <Select {...restprops} options={options} />;
};
