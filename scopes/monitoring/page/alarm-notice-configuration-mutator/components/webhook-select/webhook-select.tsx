import React, { useEffect, useState } from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import type { SelectProps } from '@manyun/base-ui.ui.select';
import { fetchWebhooks } from '@manyun/notification-hub.service.fetch-webhooks';

export type WebhookSelectProps = Omit<SelectProps, 'options'>;

export const WebhookSelect = ({ ...restprops }: WebhookSelectProps) => {
  const [options, setOptions] = useState<SelectProps['options']>([]);

  useEffect(function () {
    (async () => {
      const { error, data } = await fetchWebhooks({
        pageNum: 1,
        pageSize: 500,
        status: true,
      });
      if (error) {
        message.error(error.message);
        return;
      }
      setOptions(
        data.data.map(item => {
          return {
            label: item.webHookName,
            value: item.id,
          };
        })
      );
    })();
  }, []);

  return <Select {...restprops} options={options} />;
};
