import React, { useCallback, useEffect, useState } from 'react';
import { useHistory, useParams } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Switch } from '@manyun/base-ui.ui.switch';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { AlarmNoticeConfigurationParams } from '@manyun/monitoring.route.monitoring-routes';
import { ALARM_NOTICE_CONFIGURATIONS_ROUTE_PATH } from '@manyun/monitoring.route.monitoring-routes';
import type { ApiArgs as CreateLocalAlarmNoticeParams } from '@manyun/monitoring.service.create-local-alarm-notice';
import { createLocalAlarmNotice } from '@manyun/monitoring.service.create-local-alarm-notice';
import { fetchLocalAlarmNotice } from '@manyun/monitoring.service.fetch-local-alarm-notice';
import { AlarmLevelSelect } from '@manyun/monitoring.ui.alarm-level-select';
import { AlarmNoticeTypeSelect } from '@manyun/monitoring.ui.alarm-notice-type';
import type {
  AlarmNoticeObject,
  NotificationItemObject,
} from '@manyun/monitoring.ui.local-alarm-notice-object-table';
import {
  AlarmNoticeRoleTable,
  LocalAlarmNoticeObjectTable,
} from '@manyun/monitoring.ui.local-alarm-notice-object-table';

import { AlarmNoticeRuleInput } from './components/alarm-notice-rule-input';
import { AlarmTemplateSelect } from './components/alarm-template-group-select';
import { WebhookSelect } from './components/webhook-select';

export function AlarmNoticeConfigurationMutator() {
  const [form] = Form.useForm();
  const history = useHistory();
  const locationParams = useParams<AlarmNoticeConfigurationParams>();

  const [defaultInformTargetUserList, setDefaultInformTargetUserList] =
    useState<AlarmNoticeObject[]>();
  const [defaultInformTargetRoleList, setDefaultInformTargetRoleList] =
    useState<NotificationItemObject[]>();
  const [btnLoading, setBtnLoading] = useState(false);
  const [loading, setLoading] = useState(false);

  const getAlarmNotice = useCallback(
    async (id: number) => {
      setLoading(true);
      const { data, error } = await fetchLocalAlarmNotice({ id });
      setLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      const { name, type, rule, alarmLevels, schemeList, webhook, webhookId, informTargetList } =
        data;

      const _informTargetList = informTargetList.map(item => {
        return {
          targetId: item.targetId,
          targetType: item.targetType,
          dutyStatus: item.dutyStatus,
          phone: item.channels.includes('PHONE'),
          sms: item.channels.includes('SMS'),
          email: item.channels.includes('EMAIL'),
          internalMsg: item.channels.includes('INTERNAL_MESSAGE'),
        };
      });
      const _informTargetUserList = _informTargetList.filter(item => item.targetType === 'USER');

      const _informTargetRoleList = _informTargetList
        .filter(item => item.targetType === 'ROLE')
        .map(item => {
          return {
            code: item.targetId,
            ...item,
          };
        });
      setDefaultInformTargetRoleList(_informTargetRoleList);
      setDefaultInformTargetUserList(_informTargetUserList);
      form.setFieldsValue({
        name,
        type,
        rule,
        alarmLevels: alarmLevels?.split(','),
        schemeIdList: schemeList?.map(item => Number(item.id)),
        webhook,
        webhookId: webhookId?.split(',').map(item => Number(item)),
        informTargetUserList: _informTargetUserList,
        informTargetRoleList: _informTargetRoleList,
      });
    },
    [form]
  );

  useEffect(() => {
    if (locationParams.id && locationParams.type !== 'create') {
      getAlarmNotice(Number(locationParams.id));
    }
  }, [getAlarmNotice, locationParams.id, locationParams.type]);

  const onNoticeTypeChange = (value: string, option: { label: string }) => {
    form.setFieldsValue({ name: option.label });
    if (value === 'ALARM_NOTIFY' || value === 'RECOVERED_NOTIFY') {
      form.setFieldsValue({ rule: 0 });
    } else {
      form.setFieldsValue({ rule: undefined });
    }
  };
  const onSubmit = async () => {
    const values = await form.validateFields();

    const {
      name,
      type,
      rule,
      alarmLevels,
      webhook,
      webhookId,
      schemeIdList,
      informTargetRoleList,
      informTargetUserList,
    } = values;

    const _informTargetRoleList = informTargetRoleList?.map((item: NotificationItemObject) => {
      const _channels = [];
      if (item.phone) {
        _channels.push('PHONE');
      }
      if (item.email) {
        _channels.push('EMAIL');
      }
      if (item.sms) {
        _channels.push('SMS');
      }
      if (item.internalMsg) {
        _channels.push('INTERNAL_MESSAGE');
      }
      return {
        targetId: item.code,
        targetType: 'ROLE',
        dutyStatus: item.dutyStatus,
        channels: _channels.join(','),
      };
    });
    const _informTargetUserList = informTargetUserList?.map((item: AlarmNoticeObject) => {
      const _channels = [];
      if (item.phone) {
        _channels.push('PHONE');
      }
      if (item.email) {
        _channels.push('EMAIL');
      }
      if (item.sms) {
        _channels.push('SMS');
      }
      if (item.internalMsg) {
        _channels.push('INTERNAL_MESSAGE');
      }
      return {
        targetId: item.targetId,
        targetType: 'USER',
        dutyStatus: item.dutyStatus,
        channels: _channels.join(','),
      };
    });

    const params: CreateLocalAlarmNoticeParams = {
      name,
      type,
      rule,
      alarmLevels: alarmLevels?.join(','),
      webhook,
      webhookIdList: webhookId?.join(','),
      schemeIdList: schemeIdList?.join(','),
      notified: true,
      informTargetList: [...(_informTargetUserList ?? []), ...(_informTargetRoleList ?? [])],
    };

    if (locationParams.type === 'edit') {
      params.id = Number(locationParams.id);
    }
    setBtnLoading(true);
    const { error } = await createLocalAlarmNotice(params);
    setBtnLoading(false);

    if (error) {
      message.error(error.message);
      return;
    }
    if (locationParams.type === 'edit') {
      message.success('更新告警通报成功！');
    } else {
      message.success('创建告警通报成功！');
    }
    history.push(ALARM_NOTICE_CONFIGURATIONS_ROUTE_PATH);
  };

  const type = Form.useWatch('type', form);
  const rule = Form.useWatch('rule', form);
  const webhook = Form.useWatch('webhook', form);

  return (
    <Card loading={loading}>
      <Space style={{ width: '75%' }} size="middle" direction="vertical">
        <Typography.Title showBadge level={5}>
          {locationParams.type === 'edit' ? '编辑' : '新建'}通报规则
        </Typography.Title>
        <Form
          form={form}
          initialValues={{ webhook: false }}
          labelCol={{ span: 3 }}
          wrapperCol={{ span: 21 }}
        >
          <Form.Item
            label="通报类型"
            name="type"
            rules={[{ required: true, message: '通报类型必填' }]}
          >
            <AlarmNoticeTypeSelect
              style={{ width: 422 }}
              onChange={(value, option) => onNoticeTypeChange(value, option as { label: string })}
            />
          </Form.Item>
          <Form.Item
            label="通报名称"
            name="name"
            rules={[
              {
                required: true,
                whitespace: true,
                message: '通报名称必填',
              },
              {
                type: 'string',
                max: 100,
                message: '最多输入 100 个字符！',
              },
            ]}
          >
            <Input style={{ width: 422 }} />
          </Form.Item>
          <Form.Item
            label="通报条件"
            name="rule"
            rules={[{ required: true, message: '通报条件必填' }]}
          >
            <AlarmNoticeRuleInput type={type} rule={rule} />
          </Form.Item>
          <Form.Item
            label="适用等级"
            name="alarmLevels"
            rules={[{ required: true, message: '适用等级必填' }]}
          >
            <AlarmLevelSelect style={{ width: 422 }} trigger="onDidMount" mode="multiple" />
          </Form.Item>

          <Form.Item
            label="适用模版组"
            name="schemeIdList"
            rules={[{ required: true, message: '适用模版组' }]}
          >
            <AlarmTemplateSelect style={{ width: 422 }} allowClear mode="multiple" />
          </Form.Item>

          <Form.Item
            label="webhook通报"
            name="webhook"
            tooltip="通报会发送到告警所在的楼栋群"
            required
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
          {webhook && (
            <Form.Item
              name="webhookId"
              rules={[{ required: true, message: 'weebhook群必填' }]}
              wrapperCol={{ span: 21, offset: 3 }}
            >
              <WebhookSelect style={{ width: 422 }} mode="multiple" maxTagCount="responsive" />
            </Form.Item>
          )}
        </Form>
      </Space>
      <Space style={{ width: '100%' }} size="middle" direction="vertical">
        <Typography.Title showBadge level={5}>
          通报对象
        </Typography.Title>
        <Form form={form} layout="vertical">
          <Form.Item
            label="按角色通报"
            name="informTargetRoleList"
            rules={[{ required: true, message: '角色通报对象必填' }]}
          >
            <AlarmNoticeRoleTable savedValue={defaultInformTargetRoleList} />
          </Form.Item>
          <Form.Item
            label="按人员通报"
            name="informTargetUserList"
            labelCol={{ span: undefined }}
            wrapperCol={{ span: undefined }}
            rules={[{ required: true, message: '人员通报必填' }]}
          >
            <LocalAlarmNoticeObjectTable
              savedValue={defaultInformTargetUserList}
              mode={locationParams.type === 'edit' ? 'edit' : 'create'}
            />
          </Form.Item>
        </Form>
      </Space>
      <FooterToolBar fixed>
        <Space size={16}>
          <Button type="primary" loading={btnLoading} onClick={onSubmit}>
            提交
          </Button>
          <Button href={ALARM_NOTICE_CONFIGURATIONS_ROUTE_PATH}>取消</Button>
        </Space>
      </FooterToolBar>
    </Card>
  );
}
