/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-1-3
 *
 * @packageDocumentation
 */
import React, { useEffect, useState } from 'react';
import { useHistory, useParams, useLocation } from 'react-router-dom';

import type { Moment } from 'moment';
import moment from 'moment';
import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { PageHeader } from '@manyun/base-ui.ui.page-header';
import { Space } from '@manyun/base-ui.ui.space';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import { Typography } from '@manyun/base-ui.ui.typography';

import {
  useCreateAlarmShield,
  useLazyAlarmShield,
  useUpdateAlarmShield,
} from '@manyun/monitoring.gql.client.monitoring';
import type {
  AlarmShieldDetail,
  ShieldScope,
  ShieldScopeInput,
} from '@manyun/monitoring.gql.client.monitoring';
import { ALARM_SHIELD_LIST_ROUTE_PATH } from '@manyun/monitoring.route.monitoring-routes';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

import { ShieldDeviceCard } from './components/shield-device-card';
import { ShieldPointCard } from './components/shield-point-card';
import { ShieldSpaceCard } from './components/shield-space-card';

type ShieldTabKey = 'SPACE' | 'DEVICE' | 'POINT';

const disabledDate = (current: Moment) => {
  return current && current < moment().startOf('day');
};

const generateRange = (start: number, end: number) => {
  /* 计算时间选择框 时分秒 disabled*/
  const result = [];
  for (let i = start; i < end; i++) {
    result.push(i);
  }
  return result;
};

const disabledTime = (selectCurrentTime: Moment | null) => {
  const compareMoment = moment();
  const hour = compareMoment.hour();
  const minute = compareMoment.minute();
  const second = compareMoment.second();
  if (selectCurrentTime && selectCurrentTime.isSame(compareMoment, 'day')) {
    const selectCurrentHour = selectCurrentTime.hour();
    const selectCurrentMinute = selectCurrentTime.minute();
    if (selectCurrentHour === hour) {
      return {
        disabledHours: () => generateRange(0, hour),
        disabledMinutes: () => generateRange(0, minute),
        disabledSeconds: () => (selectCurrentMinute === minute ? generateRange(0, second) : []),
      };
    } else {
      return {
        disabledHours: () => generateRange(0, hour + 1),
        disabledMinutes: () => [],
        disabledSeconds: () => [],
      };
    }
  }
  return {
    disabledHours: () => [],
    disabledMinutes: () => [],
    disabledSeconds: () => [],
  };
};

export function AlarmShieldMutator() {
  const { id } = useParams<{ id: string }>();
  const { search } = useLocation();
  const history = useHistory();
  const [form] = Form.useForm();

  const { mode } = getLocationSearchMap<{ mode?: 'edit' | 'copy' }>(
    search
  );

  const [createAlarmShield, { loading }] = useCreateAlarmShield({
    onCompleted(data) {
      if (!data.createAlarmShield?.success) {
        message.error(data.createAlarmShield?.message);
        return;
      }
      message.success('创建成功！');
      history.push(ALARM_SHIELD_LIST_ROUTE_PATH);
    },
  });

  const [updateAlarmShield] = useUpdateAlarmShield({
    onCompleted(data) {
      if (!data.updateAlarmShield?.success) {
        message.error(data.updateAlarmShield?.message);
        return;
      }
      message.success('更新成功！');
      history.push(ALARM_SHIELD_LIST_ROUTE_PATH);
    },
  });

  const [getAlarmShield] = useLazyAlarmShield();

  const [activeTabKey, setActiveTabKey] = useState<ShieldTabKey>('DEVICE');
  const [alarmShield, setAlarmShield] = useState<AlarmShieldDetail>();

  const [shieldDeviceList, setShieldDeviceList] = useState<ShieldScopeInput[]>([]);
  const [shieldPointList, setShieldPointList] = useState<ShieldScopeInput[]>([]);
  const [shieldSpaceList, setShieldSpaceList] = useState<ShieldScopeInput[]>([]);

  useEffect(() => {
    if (id) {
      (async () => {
        const res = await getAlarmShield({ variables: { id: Number(id) } });
        const alarmShield = res.data?.alarmShield;
        if (alarmShield) {
          setAlarmShield(alarmShield);
          const {
            blockGuid,
            name,
            startTime,
            endTime,
            enable,
            pointShieldScopes,
            spaceShieldScopes,
          } = alarmShield;

          form.setFieldsValue({
            blockGuid: [getSpaceGuidMap(blockGuid).idc, blockGuid],
            name,
            startTime: mode=== 'edit' ? moment(startTime): undefined ,
            endTime: mode=== 'edit' ? moment(endTime) : undefined,
            enable,
          });
          if (pointShieldScopes) {
            onPointChange(pointShieldScopes as ShieldScope[]);
          }
          if (spaceShieldScopes) {
            onSpaceChange(spaceShieldScopes as ShieldScope[]);
          }
        }
      })();
    }
  }, [form, getAlarmShield, id, mode]);

  const onDeviceChange = (value: ShieldScope[]) => {
    setShieldDeviceList(
      value.map(item => {
        return {
          guid: item.guid,
          type: item.type,
        };
      })
    );
  };

  const onPointChange = (value: ShieldScope[]) => {
    setShieldPointList(
      value.map(item => {
        return {
          guid: item.guid,
          type: 'POINT',
          pointCode: item.pointCode,
          pointName: item.pointName,
        };
      })
    );
  };

  const onSpaceChange = (value: ShieldScope[]) => {
    setShieldSpaceList(
      value.map(item => {
        return {
          guid: item.guid,
          type: item.type,
        };
      })
    );
  };

  const onSubmit = async () => {
    const values = await form.validateFields();

    const { blockGuid, name, startTime, endTime } = values;
    const _startTime = startTime ? startTime.valueOf() : undefined;
    const _endTime = endTime ? endTime.valueOf() : undefined;

    const shieldScopeList = [...shieldDeviceList, ...shieldPointList, ...shieldSpaceList];
    if (!shieldScopeList.length) {
      message.error('请选择屏蔽范围！');
      return;
    }
    if (_startTime && _endTime && _startTime > _endTime) {
      message.error('结束时间不得早于开始时间！');
      return;
    }
    const params = {
      name,
      blockGuid: blockGuid && blockGuid?.length > 1 ? blockGuid[1] : undefined,
      startTime: _startTime,
      endTime: _endTime,
      shieldScopeList,
    };
    if (id && mode==='edit') {
      updateAlarmShield({ variables: { params: { id: Number(id), ...params } } });
      return;
    }
    createAlarmShield({ variables: { params } });
  };

  const blockGuidList = Form.useWatch('blockGuid', form);
  const blockGuid = blockGuidList?.length > 1 ? blockGuidList[1] : undefined;

  return (
    <Card>
      <Space style={{ width: '100%' }} direction="vertical" size="middle">
        <PageHeader
          style={{ padding: 0 }}
          title={id && mode === 'edit' ? '编辑告警屏蔽设置' : '新建告警屏蔽设置'}
          onBack={() => history.push(ALARM_SHIELD_LIST_ROUTE_PATH)}
        />
        <Typography.Title showBadge level={5}>
          基本信息
        </Typography.Title>
        <Form
          form={form}
          labelCol={{ xl: 2 }}
          wrapperCol={{ xl: 22 }}
          initialValues={{ enable: true }}
        >
          <Form.Item
            label="机房楼栋"
            name="blockGuid"
            rules={[
              {
                required: true,
                message: '机房楼栋必填',
              },
              {
                type: 'array',
                len: 2,
                message: '必须选择到楼栋',
              },
            ]}
          >
            <LocationCascader
              style={{ width: 216 }}
              disabled={id && mode === 'edit'}
              nodeTypes={['IDC', 'BLOCK']}
              authorizedOnly
              allowClear
            />
          </Form.Item>
          <Form.Item
            label="屏蔽名称"
            name="name"
            rules={[
              {
                required: true,
                message: '屏蔽名称必填',
              },
              {
                type: 'string',
                max: 100,
                message: '最多输入 100 个字符！',
              },
            ]}
          >
            <Input style={{ width: 216 }} allowClear />
          </Form.Item>
          <Form.Item
            label="屏蔽开始时间"
            name="startTime"
            rules={[
              {
                required: true,
                message: '屏蔽开始时间必填',
              },
            ]}
          >
            <DatePicker
              style={{ width: 216 }}
              disabled={alarmShield?.effectStatus.code === 'ACTIVE' && mode === 'edit'}
              disabledDate={disabledDate}
              disabledTime={disabledTime}
              showTime
              allowClear
            />
          </Form.Item>
          <Form.Item
            label="屏蔽结束时间"
            name="endTime"
            rules={[
              {
                required: true,
                message: '屏蔽结束时间必填',
              },
            ]}
          >
            <DatePicker
              style={{ width: 216 }}
              disabledDate={disabledDate}
              disabledTime={disabledTime}
              showTime
              allowClear
            />
          </Form.Item>
        </Form>
        <Typography.Title showBadge level={5}>
          屏蔽范围
        </Typography.Title>
        <Card>
          <Tabs
            items={[
              {
                key: 'DEVICE',
                label: `设备维度(${shieldDeviceList.length})`,
                children: (
                  <ShieldDeviceCard
                    blockGuid={blockGuid}
                    initShieldDevices={alarmShield?.deviceShieldScopes as ShieldScope[]}
                    onChange={onDeviceChange}
                  />
                ),
              },
              {
                key: 'POINT',
                label: `测点维度(${shieldPointList.length})`,
                children: (
                  <ShieldPointCard
                    blockGuid={blockGuid}
                    initShieldPoints={alarmShield?.pointShieldScopes as ShieldScope[]}
                    onChange={onPointChange}
                  />
                ),
              },
              {
                key: 'SPACE',
                label: `包间维度(${shieldSpaceList.length})`,
                children: (
                  <ShieldSpaceCard
                    blockGuid={blockGuid}
                    initShieldSpaces={alarmShield?.spaceShieldScopes as ShieldScope[]}
                    onChange={onSpaceChange}
                  />
                ),
              },
            ]}
            activeKey={activeTabKey}
            onChange={key => {
              setActiveTabKey(key as ShieldTabKey);
            }}
          />
        </Card>
      </Space>
      <FooterToolBar>
        <Space>
          <Button type="primary" loading={loading} onClick={onSubmit}>
            提交
          </Button>
          <Button href={ALARM_SHIELD_LIST_ROUTE_PATH}>取消</Button>
        </Space>
      </FooterToolBar>
    </Card>
  );
}
