import React, { use<PERSON>allback, useMemo, useState } from 'react';
import { useDeepCompareEffect } from 'react-use';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import type { ShieldScope } from '@manyun/monitoring.gql.client.monitoring';
import type {
  ShieldPoint,
  ShieldPointTableProps,
} from '@manyun/monitoring.ui.alarm-shield-scope-table';
import { ShieldPointTable } from '@manyun/monitoring.ui.alarm-shield-scope-table';
import type { TreePoint } from '@manyun/resource-hub.ui.resource-point-drawer';
import { ResourcePointDrawer } from '@manyun/resource-hub.ui.resource-point-drawer';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

export type ShieldPointCardProps = {
  blockGuid: string;
  initShieldPoints?: ShieldScope[];
  onChange?: (arg: ShieldScope[]) => void;
};

export function ShieldPointCard({ blockGuid, initShieldPoints, onChange }: ShieldPointCardProps) {
  const [open, setOpen] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [shieldPoints, setShieldPoints] = useState<ShieldPoint[]>([]);
  const [searchValue, setSearchValue] = useState<string>('');

  useDeepCompareEffect(() => {
    if (initShieldPoints && initShieldPoints?.length > 0) {
      const _shieldPoints = initShieldPoints.map(item => {
        return {
          ...item,
          key: `${item.guid}${item.pointCode}`,
        };
      });
      setShieldPoints(_shieldPoints);
      onChange?.(_shieldPoints);
    }
  }, [initShieldPoints]);

  const onSelectPoint = async (points: TreePoint[]) => {
    const oldShieldPointsKeys = shieldPoints.map(item => item.key);
    const newShieldPoints = points
      .map(item => {
        let roomTag;
        let roomName;
        if (item.target.type === 'DEVICE') {
          roomTag = item.target.device?.spaceGuid.roomTag;
          roomName = item.target.device?.roomName;
        }
        if (item.target.type === 'ROOM') {
          const { room } = getSpaceGuidMap(item.target.guid);
          roomTag = room;
          roomName = item.target.name?.split(' ')[1];
        }
        return {
          key: `${item.target.guid}${item.code}`,
          guid: item.target.guid,
          type: 'POINT',
          pointName: item.name,
          pointCode: item.code,
          roomTag,
          roomName,
          deviceName: item.target.device?.name,
          deviceLabel: item.target.device?.deviceLabel,
        };
      })
      .filter(item => !oldShieldPointsKeys.includes(item.key));

    const _shieldPoints = [...shieldPoints, ...newShieldPoints];
    setShieldPoints(_shieldPoints);
    onChange?.(_shieldPoints);
  };

  const onDelete = useCallback(
    (keys: string[]) => {
      setSelectedRowKeys(selectedRowKeys =>
        selectedRowKeys.filter(key => !keys.includes(key as string))
      );
      const _shieldPoints = shieldPoints.filter(item => !keys.includes(item.key));
      setShieldPoints(_shieldPoints);
      onChange?.(_shieldPoints);
    },
    [onChange, shieldPoints]
  );

  const operation: ShieldPointTableProps['operation'] = useMemo(() => {
    return {
      title: '操作',
      fixed: 'right',
      render: (_, record) => {
        return (
          <Popconfirm title="您即将删除此数据" onConfirm={() => onDelete([record.key])}>
            <Typography.Link>删除</Typography.Link>
          </Popconfirm>
        );
      },
    };
  }, [onDelete]);

  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
    },
  };

  return (
    <Space style={{ width: '100%' }} direction="vertical" size="large">
      <Space size="middle">
        <Button
          type="primary"
          onClick={() => {
            if (!blockGuid) {
              message.error('请先选择位置');
              return;
            }
            setOpen(true);
          }}
        >
          添加
        </Button>
        <Input.Search
          style={{ width: 272 }}
          placeholder="搜索测点名称、ID"
          onSearch={value => setSearchValue(value)}
        />
      </Space>
      {selectedRowKeys.length > 0 && (
        <Alert
          message={
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Space>
                <Typography.Text>已选择 {selectedRowKeys.length} 项</Typography.Text>
                <Button type="link" compact onClick={() => setSelectedRowKeys([])}>
                  取消选择
                </Button>
              </Space>
              <Popconfirm
                title="您即将删除选中数据"
                onConfirm={() => onDelete(selectedRowKeys as string[])}
              >
                <Typography.Link>批量删除</Typography.Link>
              </Popconfirm>
            </div>
          }
          type="info"
        />
      )}
      <ShieldPointTable
        dataSource={shieldPoints.filter(item => {
          return (
            (item.pointName && item.pointName.toLowerCase().includes(searchValue.toLowerCase())) ||
            (item.pointCode && item.pointCode.toLowerCase().includes(searchValue.toLowerCase()))
          );
        })}
        operation={operation}
        rowSelection={rowSelection}
      />
      <ResourcePointDrawer
        open={open}
        placement="left"
        destroyOnClose
        maxCheckedCount={50}
        resourceTreeProps={{
          spaceGuid: blockGuid,
          treeMode: ['BLOCK', 'ROOM', 'DEVICE'],
        }}
        isQueryNon
        onClose={() => setOpen(false)}
        onChange={onSelectPoint}
      />
    </Space>
  );
}
