import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react';
import { useDeepCompareEffect } from 'react-use';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import type { ShieldScope } from '@manyun/monitoring.gql.client.monitoring';
import type { AlarmShieldScopeTableProps } from '@manyun/monitoring.ui.alarm-shield-scope-table';
import { AlarmShieldScopeTable } from '@manyun/monitoring.ui.alarm-shield-scope-table';
import type { TreeNode } from '@manyun/resource-hub.ui.resource-tree';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

import { ShieldSpaceDrawer } from './components/shield-space-drawer';

export type ShieldSpaceCardProps = {
  blockGuid?: string;
  initShieldSpaces?: ShieldScope[];
  onChange?: (arg: ShieldScope[]) => void;
};
export function ShieldSpaceCard({ blockGuid, initShieldSpaces, onChange }: ShieldSpaceCardProps) {
  const [open, setOpen] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [shieldSpaces, setShieldSpaces] = useState<ShieldScope[]>([]);
  const [searchValue, setSearchValue] = useState<string>('');

  useDeepCompareEffect(() => {
    if (initShieldSpaces && initShieldSpaces?.length > 0) {
      setShieldSpaces(initShieldSpaces);
      onChange?.(initShieldSpaces);
    }
  }, [initShieldSpaces]);

  const onSelectSpace = (value: TreeNode[]) => {
    const oldShieldSpacesKeys = shieldSpaces.map(item => item.guid);
    const newShieldSpaces = value
      .map(item => {
        const roomName = typeof item.title === 'string' ? item.title?.split(' ')[1] : '--';
        const { room } = getSpaceGuidMap(item.key);
        return {
          guid: item.key,
          type: item.type,
          roomName,
          roomTag: room,
          roomType: item.custom.type,
        };
      })
      .filter(item => !oldShieldSpacesKeys.includes(item.guid));

    const _shieldSpaces = [...shieldSpaces, ...newShieldSpaces];
    setShieldSpaces(_shieldSpaces);
    onChange?.(_shieldSpaces);
  };

  const onDelete = useCallback(
    (keys: string[]) => {
      setSelectedRowKeys(selectedRowKeys =>
        selectedRowKeys.filter(key => !keys.includes(key as string))
      );
      const _shieldSpaces = shieldSpaces.filter(item => !keys.includes(item.guid));
      setShieldSpaces(_shieldSpaces);
      onChange?.(_shieldSpaces);
    },
    [onChange, shieldSpaces]
  );

  const operation: AlarmShieldScopeTableProps['operation'] = useMemo(() => {
    return {
      title: '操作',
      fixed: 'right',
      render: (_, record) => {
        return (
          <Popconfirm title="您即将删除此数据" onConfirm={() => onDelete([record.guid])}>
            <Typography.Link>删除</Typography.Link>
          </Popconfirm>
        );
      },
    };
  }, [onDelete]);

  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
    },
  };

  return (
    <Space style={{ width: '100%' }} direction="vertical" size="large">
      <Space size="middle">
        <Button
          type="primary"
          onClick={() => {
            if (!blockGuid) {
              message.error('请先选择位置');
              return;
            }
            setOpen(true);
          }}
        >
          添加
        </Button>
        <Input.Search
          style={{ width: 272 }}
          placeholder="搜索包间编号、名称"
          onSearch={value => setSearchValue(value)}
        />
      </Space>
      {selectedRowKeys.length > 0 && (
        <Alert
          message={
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Space>
                <Typography.Text>已选择 {selectedRowKeys.length} 项</Typography.Text>
                <Button type="link" compact onClick={() => setSelectedRowKeys([])}>
                  您即将删除选中数据
                </Button>
              </Space>
              <Popconfirm
                title="您即将删除选中数据"
                onConfirm={() => onDelete(selectedRowKeys as string[])}
              >
                <Typography.Link>批量删除</Typography.Link>
              </Popconfirm>
            </div>
          }
          type="info"
        />
      )}
      <AlarmShieldScopeTable
        dataIndexs={['roomName', 'roomType']}
        dataSource={shieldSpaces.filter(item => {
          return (
            (item.roomTag && item.roomTag.includes(searchValue)) ||
            (item.roomName && item.roomName.includes(searchValue))
          );
        })}
        operation={operation}
        rowSelection={rowSelection}
      />
      {blockGuid && (
        <ShieldSpaceDrawer
          spaceGuid={blockGuid}
          open={open}
          onClose={() => setOpen(false)}
          onChange={onSelectSpace}
        />
      )}
    </Space>
  );
}
