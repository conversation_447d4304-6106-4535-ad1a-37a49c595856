import React, { useCallback, useMemo, useState } from 'react';
import { useDeepCompareEffect } from 'react-use';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import type { ShieldScope } from '@manyun/monitoring.gql.client.monitoring';
import type { AlarmShieldScopeTableProps } from '@manyun/monitoring.ui.alarm-shield-scope-table';
import { AlarmShieldScopeTable } from '@manyun/monitoring.ui.alarm-shield-scope-table';
import type { TreeNode } from '@manyun/resource-hub.ui.resource-tree';

import { ShieldDeviceDrawer } from './components/shield-device-drawer';

export type ShieldDeviceCardProps = {
  blockGuid: string;
  initShieldDevices?: ShieldScope[];
  onChange?: (arg: ShieldScope[]) => void;
};

export function ShieldDeviceCard({
  blockGuid,
  initShieldDevices,
  onChange,
}: ShieldDeviceCardProps) {
  const [open, setOpen] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [shieldDevices, setShieldDevices] = useState<ShieldScope[]>([]);
  const [searchValue, setSearchValue] = useState<string>('');

  useDeepCompareEffect(() => {
    if (initShieldDevices && initShieldDevices?.length > 0) {
      setShieldDevices(initShieldDevices);
      onChange?.(initShieldDevices);
    }
  }, [initShieldDevices]);

  const onSelectDevice = (value: TreeNode[]) => {
    const oldShieldDevicesKeys = shieldDevices.map(item => item.guid);
    const newShieldDevices = value
      .map(item => {
        return {
          guid: item.device?.guid!,
          type: 'DEVICE',
          deviceName: item.device?.name,
          deviceLabel: item.device?.deviceLabel,
          roomTag: item.device?.spaceGuid.roomTag,
          roomName: item.device?.roomName,
          productModel: item.device?.productModel,
          vendorCode: item.device?.vendor,
          deviceOperationStatus: item.device?.deviceStatus.operation.code,
        };
      })
      .filter(item => !oldShieldDevicesKeys.includes(item.guid));

    const _shieldDevices = [...shieldDevices, ...newShieldDevices];
    setShieldDevices(_shieldDevices);
    onChange?.(_shieldDevices);
  };

  const onDelete = useCallback(
    (keys: string[]) => {
      setSelectedRowKeys(selectedRowKeys =>
        selectedRowKeys.filter(key => !keys.includes(key as string))
      );
      const _shieldDevices = shieldDevices.filter(item => !keys.includes(item.guid));
      setShieldDevices(_shieldDevices);
      onChange?.(_shieldDevices);
    },
    [onChange, shieldDevices]
  );

  const operation: AlarmShieldScopeTableProps['operation'] = useMemo(() => {
    return {
      title: '操作',
      fixed: 'right',
      render: (_, record) => {
        return (
          <Popconfirm title="您即将删除此数据" onConfirm={() => onDelete([record.guid])}>
            <Typography.Link>删除</Typography.Link>
          </Popconfirm>
        );
      },
    };
  }, [onDelete]);

  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
    },
  };

  return (
    <Space style={{ width: '100%' }} direction="vertical" size="large">
      <Space size="middle">
        <Button
          type="primary"
          onClick={() => {
            if (!blockGuid) {
              message.error('请先选择位置');
              return;
            }
            setOpen(true);
          }}
        >
          添加
        </Button>
        <Input.Search
          style={{ width: 272 }}
          placeholder="搜索设备编号、名称"
          onSearch={value => setSearchValue(value)}
        />
      </Space>
      {selectedRowKeys.length > 0 && (
        <Alert
          message={
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Space>
                <Typography.Text>已选择 {selectedRowKeys.length} 项</Typography.Text>
                <Button type="link" compact onClick={() => setSelectedRowKeys([])}>
                  取消选择
                </Button>
              </Space>
              <Popconfirm
                title="您即将删除选中数据"
                onConfirm={() => onDelete(selectedRowKeys as string[])}
              >
                <Typography.Link>批量删除</Typography.Link>
              </Popconfirm>
            </div>
          }
          type="info"
        />
      )}
      <AlarmShieldScopeTable
        dataSource={shieldDevices.filter(
          item => item.deviceName?.includes(searchValue) || item.deviceLabel?.includes(searchValue)
        )}
        operation={operation}
        rowSelection={rowSelection}
      />
      <ShieldDeviceDrawer
        spaceGuid={blockGuid}
        open={open}
        onClose={() => setOpen(false)}
        onChange={onSelectDevice}
      />
    </Space>
  );
}
