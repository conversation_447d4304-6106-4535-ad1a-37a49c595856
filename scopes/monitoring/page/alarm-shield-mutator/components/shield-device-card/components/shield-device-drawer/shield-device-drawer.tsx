import React, { useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import type { TreeNode } from '@manyun/resource-hub.ui.resource-tree';
import { ResourceTree } from '@manyun/resource-hub.ui.resource-tree';

export type ShieldDeviceDrawerProps = {
  open: boolean;
  onClose: () => void;
  onChange?: (arg: TreeNode[]) => void;
  spaceGuid: string;
};

export function ShieldDeviceDrawer({
  open,
  onClose,
  onChange,
  spaceGuid,
}: ShieldDeviceDrawerProps) {
  const [checkedNodes, setCheckedNodes] = useState<TreeNode[]>([]);

  const handleOk = () => {
    onChange?.(checkedNodes);
    onClose();
  };

  return (
    <Drawer
      title={
        <Space>
          屏蔽设备维度
          <Typography.Text type="secondary"> 已选（{checkedNodes.length}）</Typography.Text>
        </Space>
      }
      placement="left"
      width={440}
      open={open}
      destroyOnClose
      extra={
        <Space>
          <Button onClick={onClose}>取消</Button>
          <Button type="primary" onClick={handleOk}>
            保存
          </Button>
        </Space>
      }
      onClose={onClose}
    >
      <ResourceTree
        authorizedOnly
        checkable
        checkableNodes={['DEVICE']}
        showFilter
        treeMode={['BLOCK', 'FLOOR', 'ROOM', 'DEVICE']}
        spaceGuid={spaceGuid}
        onCheck={(_, info) => {
          setCheckedNodes(info.checkedNodes);
        }}
      />
    </Drawer>
  );
}
