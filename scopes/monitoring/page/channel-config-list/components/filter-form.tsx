import React, { useState } from 'react';

import { FilterOutlined } from '@ant-design/icons';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Dropdown } from '@manyun/base-ui.ui.dropdown';
import { Form } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';

import { ChannelProtocolTypeSelect } from '@manyun/monitoring.ui.channel-protocol-type-select';
import { ChannelStatusSelect } from '@manyun/monitoring.ui.channel-status-select';
import { ChannelTypeSelect } from '@manyun/monitoring.ui.channel-type-select';
import { DeviceTypeCascader } from '@manyun/resource-hub.ui.device-type-cascader';

export type Params = {
  channelType?: string;
  protocol?: string;
  channelStatus?: string;
  deviceType?: string;
};

export type FilterFormPopoverProps = {
  onChange: (arg: Params) => void;
};

export function FilterFormPopover({ onChange }: FilterFormPopoverProps) {
  const [visible, setVisible] = useState(false);
  const [formParams, setFormParams] = useState<Params>({});

  const onHide = () => {
    setVisible(false);
  };

  const handleVisibleChange = (newVisible: boolean) => {
    setVisible(newVisible);
  };

  const onFilterFormChange = (params: Params) => {
    setFormParams(params);
    onChange(params);
  };

  return (
    <Dropdown
      visible={visible}
      onVisibleChange={handleVisibleChange}
      overlay={<FilterForm onHide={onHide} onChange={onFilterFormChange} />}
      placement="bottomRight"
      trigger={['click']}
      arrow={{ pointAtCenter: true }}
    >
      <Tooltip title="筛选">
        {Object.keys(formParams).length ? (
          <FilterOutlined style={{ color: `var(--${prefixCls}-primary-color)` }} />
        ) : (
          <FilterOutlined />
        )}
      </Tooltip>
    </Dropdown>
  );
}

type FilterFormProps = {
  onHide: () => void;
  onChange: (arg: Params) => void;
};

function FilterForm({ onHide, onChange }: FilterFormProps) {
  const [form] = Form.useForm();

  const formItems = [
    {
      label: '通道类型',
      name: 'channelType',
      colSpan: 12,
      render: <ChannelTypeSelect allowClear />,
    },
    {
      label: '通道协议类型',
      name: 'protocol',
      colSpan: 12,
      render: <ChannelProtocolTypeSelect allowClear />,
    },
    {
      label: '通道状态',
      name: 'channelStatus',
      colSpan: 12,
      render: <ChannelStatusSelect allowClear />,
    },
    {
      label: '设备分类',
      name: 'deviceType',
      colSpan: 12,
      render: (
        <DeviceTypeCascader
          numbered
          dataType={['snDevice']}
          category="categorycode"
          disabledTypeList={['C0', 'C1']}
          allowClear
        />
      ),
    },
  ];
  const onReset = () => {
    form.resetFields();
    onChange({});
    onHide();
  };

  const onSearch = async () => {
    const values = await form.validateFields();
    onChange(getRealParams(values));
    onHide();
  };
  return (
    <Card style={{ width: 464 }}>
      <Form layout="vertical" form={form}>
        <Row gutter={16}>
          {formItems.map(item => {
            const { label, name, colSpan, render } = item;
            return (
              <Col span={colSpan} key={name}>
                <Form.Item label={label} name={name}>
                  {React.cloneElement(render, {
                    style: { ...render.props?.style },
                  })}
                </Form.Item>
              </Col>
            );
          })}
        </Row>
        <Row justify="end">
          <Space>
            <Button onClick={onReset}>重置</Button>
            <Button type="primary" onClick={onSearch}>
              搜索
            </Button>
          </Space>
        </Row>
      </Form>
    </Card>
  );
}

export const getRealParams = (params: Record<string, string>) => {
  const newParams = Object.keys(params)
    .filter(key => params[key])
    .reduce((result, key) => ({ ...result, [key]: params[key] }), {});
  return newParams;
};
