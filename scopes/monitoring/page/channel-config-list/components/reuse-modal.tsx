import type { Key } from 'react';
import React, { useEffect, useState } from 'react';

import type { CheckboxChangeEvent } from 'antd/es/checkbox';

import { Card } from '@manyun/base-ui.ui.card';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';

import type { ChannelConfigJSON } from '@manyun/monitoring.model.channel-config';
import type { SvcQuery as SearchParams } from '@manyun/monitoring.service.fetch-channel-config';
import { fetchChannelConfig } from '@manyun/monitoring.service.fetch-channel-config';
import { fetchChannelDirect } from '@manyun/monitoring.service.fetch-channel-direct';
import { reuseChannelConfig } from '@manyun/monitoring.service.reuse-channel-config';
import { ChannelConfigTable } from '@manyun/monitoring.ui.channels-table';
import { DeviceTypeCascader } from '@manyun/resource-hub.ui.device-type-cascader';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';

type ReuseModalProps = {
  visible: boolean;
  onClose: () => void;
  sourceChannel: ChannelConfigJSON;
  onRefresh: () => void;
};

export function ReuseModal({ visible, onClose, sourceChannel, onRefresh }: ReuseModalProps) {
  const [loading, setLoading] = useState<boolean>(false);

  const [channelConfigList, setChannelConfigList] = useState<ChannelConfigJSON[]>([]);

  const [pagination, setPagination] = useState({ pageNum: 1, pageSize: 10, total: 0 });
  const [condition, setCondition] = useState<string>();
  const [deviceType, setDeviceType] = useState<string>();
  const [blockGuid, setBlockGuid] = useState<string>(sourceChannel.blockGuid);

  const [configChecked, setConfigChecked] = useState<boolean>(true);
  const [pointChecked, setPointChecked] = useState<boolean>(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<Key[]>([]);

  const [isChannelDirect, setIsChannelDirect] = useState<boolean>(false);
  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: Key[]) => setSelectedRowKeys(newSelectedRowKeys),
    getCheckboxProps: (record: ChannelConfigJSON) => ({
      disabled: record.id === sourceChannel.id,
    }),
  };

  useEffect(() => {
    if (visible && sourceChannel) {
      getChannelConfigList({
        blockGuid,
        pageNum: pagination.pageNum,
        pageSize: pagination.pageSize,
      });
      getChannelDirect();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sourceChannel, visible]);

  const getChannelDirect = async () => {
    const { error, data } = await fetchChannelDirect(sourceChannel.id);
    if (error) {
      message.error(error.message);
      return;
    }
    if (data !== null) {
      setIsChannelDirect(data);
    }
  };

  const getChannelConfigList = async (params: SearchParams) => {
    setLoading(true);
    const { error, data } = await fetchChannelConfig({
      ...params,
      protocol: sourceChannel.protocol,
    });
    setLoading(false);
    if (error) {
      message.error(error.message);
      setChannelConfigList([]);
      return;
    }
    setSelectedRowKeys([]);
    setPagination({ pageNum: params.pageNum, pageSize: params.pageSize, total: data.total });
    setChannelConfigList(data.data);
  };

  const handleOk = async () => {
    if (!selectedRowKeys.length) {
      message.error('请至少选择一个通道！');
      return;
    }
    setLoading(true);
    const params = isChannelDirect
      ? {
          sourceChannelId: sourceChannel.id,
          targetChannelIds: selectedRowKeys as string[],
          copyConfig: configChecked,
          copyPoint: pointChecked,
        }
      : {
          sourceChannelId: sourceChannel.id,
          targetChannelIds: selectedRowKeys as string[],
          copyConfig: configChecked,
        };
    const { error } = await reuseChannelConfig(params);
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    message.success('复用成功');
    onRefresh();
    onClose();
  };

  const onSearch = async (value: string) => {
    const params = {
      blockGuid,
      condition: value,
      deviceType,
      pageNum: 1,
      pageSize: 10,
    };
    getChannelConfigList(params);
  };

  const onSelectDeviceType = (value: string) => {
    setDeviceType(value);
    const params = {
      blockGuid,
      deviceType: value,
      condition,
      pageNum: 1,
      pageSize: 10,
    };
    getChannelConfigList(params);
  };

  const onSelectBlockGuid = (value: string) => {
    setBlockGuid(value);
    const params = {
      blockGuid: value,
      condition,
      deviceType,
      pageNum: 1,
      pageSize: 10,
    };
    getChannelConfigList(params);
  };

  return (
    <Modal
      width={1160}
      bodyStyle={{ height: 561, overflowY: 'auto', padding: 0 }}
      title="复用通道配置"
      visible={visible}
      onOk={handleOk}
      onCancel={onClose}
    >
      <div style={{ display: 'flex' }}>
        <Card style={{ width: 240 }} title="选择复用内容" bordered={false}>
          <Space size={16} direction="vertical">
            <Checkbox
              checked={configChecked}
              onChange={(e: CheckboxChangeEvent) => setConfigChecked(e.target.checked)}
            >
              操作配置
            </Checkbox>
            {isChannelDirect && (
              <Checkbox
                checked={pointChecked}
                onChange={(e: CheckboxChangeEvent) => setPointChecked(e.target.checked)}
              >
                直采设备测点配置
              </Checkbox>
            )}
          </Space>
        </Card>
        <Card
          style={{ borderBottom: 'none', flex: 1 }}
          title="选择应用对象"
          extra="仅限同类型通道应用"
        >
          <Space size={16}>
            <Input.Search
              allowClear
              placeholder="输入通道名称/IP"
              style={{ width: 264 }}
              onChange={e => setCondition(e.target.value)}
              onSearch={onSearch}
            />
            <Space>
              设备类型
              <DeviceTypeCascader
                numbered
                dataType={['snDevice']}
                disabledTypeList={['C0', 'C1']}
                placeholder="请选择设备类型"
                allowClear
                style={{ width: 200 }}
                onChange={(value: string) => onSelectDeviceType(value)}
              />
            </Space>
            <Space>
              楼栋
              <LocationTreeSelect
                style={{ width: 200 }}
                value={blockGuid}
                allowClear
                disabledTypes={['IDC']}
                showSearch
                authorizedOnly
                onChange={value => onSelectBlockGuid(value as string)}
              />
            </Space>
          </Space>
          <ChannelConfigTable
            style={{ marginTop: 16 }}
            fromPage="reuseModal"
            dataSource={channelConfigList}
            dataIndexs={['name', 'ip', 'blockGuid', 'device', 'deviceType', 'roomTag', 'vendor']}
            loading={loading}
            pagination={{
              total: pagination.total,
              current: pagination.pageNum,
              pageSize: pagination.pageSize,
              onChange: (pageNum: number, pageSize: number) => {
                getChannelConfigList({ condition, deviceType, blockGuid, pageNum, pageSize });
              },
            }}
            rowSelection={rowSelection}
          />
        </Card>
      </div>
    </Modal>
  );
}
