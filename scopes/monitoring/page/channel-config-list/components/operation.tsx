import React from 'react';
import { <PERSON> } from 'react-router-dom';

import { EllipsisOutlined } from '@ant-design/icons';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Dropdown } from '@manyun/base-ui.ui.dropdown';
import { Menu } from '@manyun/base-ui.ui.menu';
import type { MenuProps } from '@manyun/base-ui.ui.menu';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';

import type { ChannelConfigJSON } from '@manyun/monitoring.model.channel-config';
import {
  generateChannelAssociateRoutePath,
  generateCopyChannelRoutePath,
  generateEditChannelRoutePath,
} from '@manyun/monitoring.route.monitoring-routes';
import { deleteChannelConfig } from '@manyun/monitoring.service.delete-channel-config';
import { updateChannelStatus } from '@manyun/monitoring.service.update-channel-status';

type OperationProps = {
  record: ChannelConfigJSON;
  onRefresh: () => void;
  onShowReuseModal: (arg: ChannelConfigJSON) => void;
};

export function Operation({ record, onRefresh, onShowReuseModal }: OperationProps) {
  const onSelectMenu: MenuProps['onClick'] = async ({ key }) => {
    if (key === 'delete') {
      Modal.confirm({
        title: '您即将删除此通道',
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          const { error } = await deleteChannelConfig({ channelId: record.id });
          if (error) {
            message.error(error.message);
            return;
          }
          message.success('删除成功');
          onRefresh();
        },
      });
    }

    if (key === 'copy') {
      window.open(generateCopyChannelRoutePath({ id: record.id }));
    }
    if (key === 'reuse') {
      onShowReuseModal(record);
    }
    if (key === 'stopUsing') {
      Modal.confirm({
        title: '您即将停用该通道',
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          const { error } = await updateChannelStatus({
            channelIds: [record.id],
            channelStatus: 'OFF',
          });
          if (error) {
            message.error(error.message);
            return;
          }
          message.success('停用成功');
          onRefresh();
        },
      });
    }
    if (key === 'startUsing') {
      Modal.confirm({
        title: '您即将启用该通道',
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          const { error } = await updateChannelStatus({
            channelIds: [record.id],
            channelStatus: 'ON',
          });
          if (error) {
            message.error(error.message);
            return;
          }
          message.success('启用成功');
          onRefresh();
        },
      });
    }
  };
  const menu = (
    <Menu onClick={onSelectMenu}>
      <Menu.Item key="delete">删除</Menu.Item>
      <Menu.Item key="copy">复制</Menu.Item>
      <Menu.Item key="reuse">复用</Menu.Item>
      {record.channelStatus.code === 'ON' && <Menu.Item key="stopUsing">停用</Menu.Item>}
      {record.channelStatus.code === 'OFF' && <Menu.Item key="startUsing">启用</Menu.Item>}
    </Menu>
  );

  return (
    <Space split={<Divider type="vertical" />} size={0}>
      <Link target="_blank" to={generateEditChannelRoutePath({ id: record.id })}>
        <Button type="link" compact>
          编辑
        </Button>
      </Link>
      <Link target="_blank" to={generateChannelAssociateRoutePath({ id: record.id })}>
        <Button type="link" compact>
          关联
        </Button>
      </Link>
      <Dropdown overlay={menu} trigger={['click']} placement="bottomRight">
        <EllipsisOutlined style={{ color: `var(--${prefixCls}-primary-color)` }} />
      </Dropdown>
    </Space>
  );
}
