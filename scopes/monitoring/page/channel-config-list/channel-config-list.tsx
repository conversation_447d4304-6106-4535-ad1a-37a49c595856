import React, { Key, useCallback, useEffect, useMemo, useState } from 'react';
import { useHistory } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { ExportType } from '@manyun/base-ui.ui.file-export/dist/file-export';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import type { ChannelConfigJSON } from '@manyun/monitoring.model.channel-config';
import {
  CHANNEL_CONFIG_IMPORT,
  CHANNEL_CONFIG_NEW,
} from '@manyun/monitoring.route.monitoring-routes';
import { exportChannel } from '@manyun/monitoring.service.export-channel';
import {
  SvcQuery as SearchParams,
  fetchChannelConfig,
} from '@manyun/monitoring.service.fetch-channel-config';
import { updateChannelStatus } from '@manyun/monitoring.service.update-channel-status';
import { ChannelConfigTable, ChannelConfigTableProps } from '@manyun/monitoring.ui.channels-table';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';

import styles from './channel-config-list.module.less';
import { FilterFormPopover, Params as FormParams } from './components/filter-form';
import { Operation } from './components/operation';
import { ReuseModal } from './components/reuse-modal';

export function ChannelConfigList() {
  const history = useHistory();

  const [loading, setLoading] = useState<boolean>(false);
  const [exportLoading, setExportLoading] = useState<boolean>(false);

  const [blockGuid, setBlockGuid] = useState<string>();
  const [condition, setCondition] = useState<string>();

  const [formParams, setFormParams] = useState<FormParams>({});
  const [pagination, setPagination] = useState({ pageNum: 1, pageSize: 10, total: 0 });

  const [channelConfigList, setChannelConfigList] = useState<ChannelConfigJSON[]>([]);

  const [selectedChannel, setSelectedChannel] = useState<ChannelConfigJSON>();

  const [selectedRowKeys, setSelectedRowKeys] = useState<Key[]>([]);

  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: Key[]) => setSelectedRowKeys(newSelectedRowKeys),
  };

  const getChannelConfigList = useCallback(async (params: SearchParams) => {
    setLoading(true);
    const { error, data } = await fetchChannelConfig(params);
    setLoading(false);
    if (error) {
      message.error(error.message);
      setChannelConfigList([]);
      return;
    }
    setPagination({ pageNum: params.pageNum, pageSize: params.pageSize, total: data.total });
    setChannelConfigList(data.data);
  }, []);

  useEffect(() => {
    getChannelConfigList({ pageNum: 1, pageSize: 10 });
  }, [getChannelConfigList]);

  const onFilterForm = (params: FormParams) => {
    setFormParams(params);
    getChannelConfigList({
      ...params,
      blockGuid,
      condition,
      pageNum: 1,
      pageSize: 10,
    });
  };

  const onSearch = async (value: string) => {
    const params = {
      ...formParams,
      blockGuid,
      condition: value,
      pageNum: 1,
      pageSize: 10,
    };
    getChannelConfigList(params);
  };

  const onSelectBlockGuid = (value: string) => {
    setBlockGuid(value);
    const params = {
      ...formParams,
      blockGuid: value,
      condition,
      pageNum: 1,
      pageSize: 10,
    };
    getChannelConfigList(params);
  };

  const onShowReuseModal = (record: ChannelConfigJSON) => {
    setSelectedChannel(record);
  };

  const onStartUsing = async () => {
    const { error } = await updateChannelStatus({
      channelIds: selectedRowKeys as string[],
      channelStatus: 'ON',
    });
    if (error) {
      message.error(error.message);
      return;
    }
    message.success('启用成功');
    onCancalSelect();
    onRefresh();
  };

  const onStopUsing = async () => {
    const { error } = await updateChannelStatus({
      channelIds: selectedRowKeys as string[],
      channelStatus: 'OFF',
    });
    if (error) {
      message.error(error.message);
      return;
    }
    message.success('停用成功');
    onCancalSelect();
    onRefresh();
  };

  const onRefresh = useCallback(() => {
    getChannelConfigList({ ...formParams, blockGuid, condition, pageNum: 1, pageSize: 10 });
  }, [blockGuid, condition, formParams, getChannelConfigList]);

  const onCancalSelect = () => {
    setSelectedRowKeys([]);
  };

  const handleFileExport = async (type: ExportType) => {
    setExportLoading(true);
    const { error, data } = await exportChannel({ channelIds: selectedRowKeys as string[] });
    setExportLoading(false);

    if (error) {
      message.error(error.message);
    }
    message.success('导出成功');
    return data;
  };

  const operation: ChannelConfigTableProps['operation'] = useMemo(() => {
    return {
      title: '操作',
      fixed: 'right',
      render: (_, record: ChannelConfigJSON) => {
        return (
          <Operation record={record} onRefresh={onRefresh} onShowReuseModal={onShowReuseModal} />
        );
      },
    };
  }, [onRefresh]);

  return (
    <Card>
      <Space direction="vertical" style={{ width: '100%' }} size={16}>
        <Space size={16}>
          <Button type="primary" onClick={() => history.push(CHANNEL_CONFIG_IMPORT)}>
            导入通道
          </Button>
          <Button onClick={() => history.push(CHANNEL_CONFIG_NEW)}>新建通道</Button>
          <LocationTreeSelect
            style={{ width: 265 }}
            allowClear
            disabledTypes={['IDC']}
            showSearch
            authorizedOnly
            onChange={value => onSelectBlockGuid(value as string)}
          />
          <Input.Search
            style={{ width: 211 }}
            allowClear
            placeholder="输入通道名称/IP"
            onChange={e => setCondition(e.target.value)}
            onSearch={onSearch}
          />
          <FilterFormPopover onChange={onFilterForm} />
        </Space>
        <ChannelConfigTable
          dataSource={channelConfigList}
          dataIndexs={[
            'id',
            'blockGuid',
            'channelType',
            'name',
            'channelCommunicationStatus',
            'ip',
            'port',
            'protocol',
            'deviceNum',
            'channelStatus',
            'device',
            'vendor',
            'deviceType',
            'roomTag',
          ]}
          operation={operation}
          loading={loading}
          pagination={{
            total: pagination.total,
            current: pagination.pageNum,
            pageSize: pagination.pageSize,
            onChange: (pageNum: number, pageSize: number) => {
              getChannelConfigList({ ...formParams, blockGuid, condition, pageNum, pageSize });
            },
          }}
          rowSelection={rowSelection}
        />
      </Space>
      {!!selectedRowKeys.length && (
        <div className={styles.channelFooter}>
          <Space>
            <Typography.Text>已选择 {selectedRowKeys.length} 项</Typography.Text>
            <Typography.Link onClick={onCancalSelect}>取消选择</Typography.Link>
            <Typography.Text style={{ marginLeft: 32 }}>
              可将其导出为带通道ID的文件,修改后再次导入以替换现有数据
            </Typography.Text>
          </Space>
          <Space size="middle">
            <FileExport
              text="导出"
              filename="通道.xls"
              disabled={exportLoading}
              data={type => {
                return handleFileExport(type);
              }}
            />
            <Button onClick={onStartUsing}>启用</Button>
            <Button onClick={onStopUsing}>停用</Button>
          </Space>
        </div>
      )}

      {selectedChannel && (
        <ReuseModal
          visible={!!selectedChannel}
          sourceChannel={selectedChannel}
          onClose={() => setSelectedChannel(undefined)}
          onRefresh={onRefresh}
        />
      )}
    </Card>
  );
}
