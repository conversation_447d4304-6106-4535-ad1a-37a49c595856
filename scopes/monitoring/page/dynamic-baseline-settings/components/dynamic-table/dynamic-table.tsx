import type { Key } from 'react';
import React, { useState } from 'react';
import { Link, useHistory } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import { Switch } from '@manyun/base-ui.ui.switch';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { generateVersionManageDetailRoutePath } from '@manyun/dc-brain.route.admin-routes';
import { getDynamicBaselineLocales } from '@manyun/monitoring.model.dynamic-baseline-setting';
import type { DynamicBaselineJSON } from '@manyun/monitoring.model.dynamic-baseline-setting';
import {
  DYNAMIC_BASELINE_SETTING_CREATE_ROUTE_PATH,
  generateDynamicBaselineSettingEditRoutePath,
} from '@manyun/monitoring.route.monitoring-routes';
import { deleteOpsScene } from '@manyun/monitoring.service.delete-ops-scene';
import { openNCloseOpsScene } from '@manyun/monitoring.service.open-n-close-ops-scene';
import { AlarmLevelText } from '@manyun/monitoring.ui.alarm-level-text';
import { AlarmTypeText } from '@manyun/monitoring.ui.alarm-type';

import { DynamicPointDrawer } from '../dynamic-point-drawer';

export type Pagination = { pageNum: number; pageSize: number };

export type DynamicBaselineTableProps = {
  dataSource: DynamicBaselineJSON[];
  loading: boolean;
  total: number;
  onDataSourceChange: (params?: Record<string, string | number>) => void;
  pagination: Pagination;
  onPaginationChange: React.Dispatch<React.SetStateAction<Pagination>>;
};

export function DynamicBaselineTable({
  loading,
  dataSource,
  onDataSourceChange,
  total,
  pagination,
  onPaginationChange,
}: DynamicBaselineTableProps) {
  const history = useHistory();

  const locales = getDynamicBaselineLocales();
  const [selectedRowKeys, setSelectedRowKeys] = useState<Key[]>([]);
  const [dynamicDrawerId, setDynamicDrawerId] = useState<string>();

  const handleSetDynamicStatus = async (ids: Key[] = [], enabled: boolean) => {
    if (!ids.length) {
      return;
    }
    const { error } = await openNCloseOpsScene({
      ids: ids.map(id => String(id)),
      enable: enabled,
    });
    if (error) {
      message.error(error.message);
      return;
    }
    message.success('设置成功');
    onDataSourceChange();
  };

  const columns: Array<ColumnType<DynamicBaselineJSON>> = [
    { title: '机房楼栋', dataIndex: 'blockGuid' },
    {
      title: '监测名称',
      dataIndex: 'name',
      render: (text, record) => (
        <Typography.Link
          onClick={() => {
            setDynamicDrawerId(record.id);
          }}
        >
          {text}
        </Typography.Link>
      ),
    },
    {
      title: '监测模型',
      dataIndex: 'alarmCondition',
      render: (_, record) => {
        return record.alarm.alarmCondition
          .split(',')
          .map(type => {
            return type === 'LOWER' ? locales['lower'] : locales['upper'];
          })
          .join(',');
      },
    },
    {
      title: '测点类型数',
      dataIndex: 'pointCount',
    },
    {
      title: '类型',
      dataIndex: 'sceneTypes',
      render: (_, record) => <AlarmTypeText value={record.alarm.alarmType} />,
    },
    {
      title: '告警等级',
      dataIndex: 'alarmlevel',
      render: (_, record) => <AlarmLevelText code={record.alarm.alarmLevel} />,
    },
    {
      title: '启用状态',
      dataIndex: 'enabled',
      render: (_, record) => {
        return (
          <Switch
            checked={record.enabled}
            onChange={checked => {
              handleSetDynamicStatus([record.id], checked);
            }}
          />
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 200,
      fixed: 'right',
      render: (_, record) => {
        return (
          <Space>
            <Link
              to={generateDynamicBaselineSettingEditRoutePath({
                id: String(record.id),
                type: 'edit',
              })}
            >
              编辑
            </Link>

            <Link
              to={generateDynamicBaselineSettingEditRoutePath({
                id: String(record.id),
                type: 'copy',
              })}
            >
              复制
            </Link>

            <Popconfirm
              key="remove"
              title="确定删除该条配置？"
              onConfirm={async () => {
                const { error } = await deleteOpsScene({
                  id: +record.id,
                });
                if (error) {
                  message.error(error.message);
                  return;
                }
                message.success('删除成功');
                onDataSourceChange();
              }}
            >
              <Button compact type="link">
                删除
              </Button>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  return (
    <>
      <Space style={{ marginBottom: 10 }}>
        <Link to={DYNAMIC_BASELINE_SETTING_CREATE_ROUTE_PATH}>
          <Button type="primary">新建配置</Button>
        </Link>
        <Button
          disabled={!(selectedRowKeys.length > 0)}
          onClick={() => {
            handleSetDynamicStatus(selectedRowKeys, true);
          }}
        >
          启用
        </Button>
        <Button
          disabled={!(selectedRowKeys.length > 0)}
          onClick={() => {
            handleSetDynamicStatus(selectedRowKeys, false);
          }}
        >
          禁用
        </Button>
        <Button
          onClick={() => history.push(generateVersionManageDetailRoutePath({ tab: 'AI_SCENES' }))}
        >
          版本发布
        </Button>
      </Space>
      <Table
        rowKey="id"
        dataSource={dataSource}
        scroll={{ x: 'max-content' }}
        tableLayout="fixed"
        columns={columns}
        rowSelection={{
          selectedRowKeys: selectedRowKeys,
          onChange: (selectedRowKeys, selectedRows) => {
            setSelectedRowKeys(selectedRowKeys);
          },
        }}
        loading={loading}
        pagination={{
          total,
          pageSize: pagination.pageSize,
          current: pagination.pageNum,
          onChange: (current, size) => {
            onPaginationChange({ pageNum: current, pageSize: size });
            onDataSourceChange({
              pageNum: current,
              pageSize: size,
            });
          },
        }}
      />
      {dynamicDrawerId && (
        <DynamicPointDrawer
          visible={!!dynamicDrawerId}
          id={+dynamicDrawerId}
          onClose={() => setDynamicDrawerId(undefined)}
        />
      )}
    </>
  );
}
