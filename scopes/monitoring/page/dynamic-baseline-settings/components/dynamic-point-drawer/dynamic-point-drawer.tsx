import React, { use<PERSON>allback, useEffect, useMemo, useState } from 'react';

import { Drawer } from '@manyun/base-ui.ui.drawer';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';

import { AiOpsLine } from '@manyun/monitoring.datav.ai-ops-line';
import { fetchOpsPoint } from '@manyun/monitoring.service.fetch-ops-point';
import type { OpsPoint } from '@manyun/monitoring.service.fetch-ops-point';
import { getSpaceGuid } from '@manyun/resource-hub.util.space-guid';

export type DynamicPointDrawerProps = {
  id: number;
  visible: boolean;
  onClose: () => void;
};

export function DynamicPointDrawer({ id, visible = false, onClose }: DynamicPointDrawerProps) {
  const [dataSource, setDataSource] = useState<OpsPoint[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchKeys, setSearchKeys] = useState('');

  const pointsList = useMemo(() => {
    if (!searchKeys) {
      return dataSource;
    }
    return dataSource.filter(
      data =>
        data.idcTag.toLowerCase().indexOf(searchKeys.toLowerCase()) > -1 ||
        data.roomTag.toLowerCase().indexOf(searchKeys.toLowerCase()) > -1 ||
        data.blockTag.toLowerCase().indexOf(searchKeys.toLowerCase()) > -1 ||
        data.pointName.toLowerCase().indexOf(searchKeys.toLowerCase()) > -1 ||
        data.deviceName.toLowerCase().indexOf(searchKeys.toLowerCase()) > -1
    );
  }, [dataSource, searchKeys]);

  const columns: Array<ColumnType<OpsPoint>> = [
    {
      title: '位置',
      dataIndex: 'blockGuid',
      render: (text, record) => getSpaceGuid(record.idcTag, record.blockTag, record.roomTag),
    },
    { title: '设备', dataIndex: 'deviceName' },
    { title: '测点', dataIndex: 'pointName' },
    {
      title: '动态基线',
      dataIndex: 'ops',
      render: (_, record) => <PointLine opsPoint={record} scenesId={id} />,
    },
  ];

  const getDynamicPoints = useCallback(async () => {
    setLoading(true);
    const { error, data } = await fetchOpsPoint({ id });
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    setDataSource(data.data);
  }, [id]);

  useEffect(() => {
    if (id) {
      getDynamicPoints();
    }
  }, [getDynamicPoints, id]);

  return (
    <Drawer
      destroyOnClose
      title="动态基线查询"
      bodyStyle={{ overflowY: 'auto' }}
      placement="right"
      open={visible}
      contentWrapperStyle={{ width: window.innerWidth / 2 }}
      onClose={onClose}
    >
      <Space direction="vertical" size="middle" style={{ width: '100%' }}>
        <Input
          allowClear
          placeholder="请输入位置设备或测点"
          style={{ width: 200 }}
          onChange={({ target }) => setSearchKeys(target.value)}
        />
        <Table
          dataSource={pointsList}
          columns={columns}
          loading={loading}
          pagination={false}
          scroll={{ x: 'max-content' }}
          tableLayout="fixed"
        />
      </Space>
    </Drawer>
  );
}

function PointLine({ opsPoint, scenesId }: { opsPoint: OpsPoint; scenesId: number }) {
  const [visible, setVisible] = useState(false);

  return (
    <>
      <Typography.Link
        onClick={() => {
          setVisible(true);
        }}
      >
        详情
      </Typography.Link>
      <Modal
        title={`${opsPoint.deviceName}_${opsPoint.pointName}`}
        maskClosable
        open={visible}
        width={850}
        footer={null}
        style={{
          padding: '16px 0px',
        }}
        onCancel={() => setVisible(false)}
      >
        <AiOpsLine
          showExport
          chartStyle={{ width: 800, height: 400 }}
          pointCode={opsPoint.pointCode}
          deviceGuid={opsPoint.deviceGuid}
          idc={opsPoint.idcTag}
          scenesId={scenesId}
          unit="℃"
        />
      </Modal>
    </>
  );
}
