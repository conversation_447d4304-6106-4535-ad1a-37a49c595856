import React from 'react';

import type { FormInstance } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { QueryFilter } from '@manyun/base-ui.ui.query-filter';
import { Select } from '@manyun/base-ui.ui.select';

import type { SvcQuery as QueryParams } from '@manyun/monitoring.service.fetch-ops-scenes-list';
import { DynamicBaselineConditionSelect } from '@manyun/monitoring.ui.dynamic-baseline-condition-select';
import { OpsSceneNotifyWaySelect } from '@manyun/monitoring.ui.ops-scene-notify-way-select';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';

export type DynamicQueryFilterProps = {
  form: FormInstance<QueryParams>;
  onSearch: (values: QueryParams) => void;
  onReset: () => void;
};

export function DynamicQueryFilter({ form, onSearch, onReset }: DynamicQueryFilterProps) {
  const items = React.useMemo(
    () =>
      [
        {
          label: '监测名称',
          name: 'name',
          control: <Input allowClear />,
        },
        {
          label: '机房楼栋',
          name: 'blockGuidList',
          control: (
            <LocationTreeSelect
              style={{ width: 200 }}
              multiple
              authorizedOnly
              disabledTypes={['IDC']}
              allowClear
            />
          ),
        },
        {
          label: '监测场景',
          name: 'alarmCondition',
          control: <DynamicBaselineConditionSelect style={{ width: 200 }} allowClear />,
        },
        {
          label: '通报方式',
          name: 'notifyWay',
          control: <OpsSceneNotifyWaySelect style={{ width: 200 }} allowClear />,
        },
        {
          label: '启用状态',
          name: 'enable',
          control: (
            <Select
              style={{ width: 200 }}
              options={[
                { value: true, label: '启用' },
                { value: false, label: '禁用' },
              ]}
              allowClear
            />
          ),
        },
      ].filter(Boolean),
    []
  );

  return <QueryFilter form={form} items={items} onSearch={onSearch} onReset={onReset} />;
}
