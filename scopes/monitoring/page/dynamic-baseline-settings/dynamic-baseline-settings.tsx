import React, { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { useShallowCompareEffect } from 'react-use';

import { Card } from '@manyun/base-ui.ui.card';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { getLocationSearchMap, setLocationSearch } from '@manyun/base-ui.util.query-string';

import type { DynamicBaselineJSON } from '@manyun/monitoring.model.dynamic-baseline-setting';
import { fetchOpsScenesList } from '@manyun/monitoring.service.fetch-ops-scenes-list';
import type { SvcQuery as QueryParams } from '@manyun/monitoring.service.fetch-ops-scenes-list';

import { DynamicQueryFilter } from './components/dynamic-query-filter/dynamic-query-filter';
import type { Pagination } from './components/dynamic-table';
import { DynamicBaselineTable } from './components/dynamic-table';

export function DynamicBaselineSettings() {
  const { search } = useLocation();
  const { pageNum, pageSize } = getLocationSearchMap<{ pageNum?: number; pageSize?: number }>(
    search,
    {
      parseNumbers: true,
    }
  );

  const [pagination, setPagination] = useState<Pagination>({
    pageNum: pageNum ?? 1,
    pageSize: pageSize ?? 10,
  });

  useShallowCompareEffect(() => {
    setLocationSearch({
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize,
    });
  }, [pagination]);

  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [queryParams, setQueryParams] = useState<QueryParams>({
    pageNum: pagination.pageNum,
  });
  const [form] = Form.useForm();
  const [dataSource, setDataSource] = useState<DynamicBaselineJSON[]>([]);

  const getDynamicBaseline = async (params: QueryParams) => {
    setQueryParams(params);
    setLoading(true);
    const { error, data } = await fetchOpsScenesList({
      ...params,
    });
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    setDataSource(data.data);
    setTotal(data.total);
  };

  useEffect(() => {
    getDynamicBaseline({
      ...pagination,
    });
  }, [pagination]);

  const onReset = () => {
    form.resetFields();
    setPagination({
      pageNum: 1,
      pageSize: 10,
    });
    getDynamicBaseline({
      pageNum: 1,
      pageSize: 10,
    });
  };

  return (
    <Card
      title={
        <Typography.Title level={5} showBadge>
          动态基线配置
        </Typography.Title>
      }
    >
      <Space style={{ width: '100%' }} direction="vertical" size={16}>
        <DynamicQueryFilter
          form={form}
          onReset={onReset}
          onSearch={values => {
            getDynamicBaseline({
              ...values,
            });
          }}
        />
        <DynamicBaselineTable
          loading={loading}
          total={total}
          dataSource={dataSource}
          pagination={pagination}
          onDataSourceChange={params => {
            getDynamicBaseline({
              ...queryParams,
              ...pagination,
              ...params,
            });
          }}
          onPaginationChange={setPagination}
        />
      </Space>
    </Card>
  );
}
