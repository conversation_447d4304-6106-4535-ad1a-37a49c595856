import React from 'react';
import { <PERSON> } from 'react-router-dom';

import { But<PERSON> } from '@manyun/base-ui.ui.button';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Space } from '@manyun/base-ui.ui.space';
import type { ColumnType } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';

import { generateSpaceOrDeviceRoutePath } from '@manyun/monitoring.route.monitoring-routes';
import { ChannelDebugging, PointData } from '@manyun/monitoring.service.fetch-channel-debugging';
import { ChannelDevice } from '@manyun/monitoring.service.fetch-channel-device';
import { DeviceTypeText } from '@manyun/resource-hub.ui.device-type-text';

import type { TabKey } from '../channel-config-detail';

const BLANK = '--';

export const getDeviceInfoColumns = ({
  onDelete,
  onShowPointList,
}: {
  onDelete: (arg: ChannelDevice) => void;
  onShowPointList: (arg: ChannelDevice) => void;
}) => {
  const columns: ColumnType<ChannelDevice>[] = [
    {
      title: '设备名称',
      dataIndex: 'deviceName',
      render: (text, record) => {
        return (
          <Link
            to={generateSpaceOrDeviceRoutePath({
              guid: record.deviceGuid,
              type: 'DEVICE',
            })}
            target="_blank"
          >
            {text}
          </Link>
        );
      },
    },
    { title: '设备编码', dataIndex: 'deviceGuid' },
    {
      title: '设备类型',
      dataIndex: 'deviceType',
      render: text => {
        return text ? <DeviceTypeText code={text} /> : BLANK;
      },
    },
    { title: '位置', dataIndex: 'spaceGuid' },
    { title: '测点数', dataIndex: 'pointNum' },
    {
      title: '操作',
      key: '_actions',
      fixed: 'right',
      render: (_, record) => (
        <Space split={<Divider type="vertical" />} size={0}>
          <Button type="link" compact onClick={() => onShowPointList(record)}>
            测点详情
          </Button>
          <Button type="link" compact onClick={() => onDelete(record)}>
            删除
          </Button>
        </Space>
      ),
    },
  ];
  return columns;
};

export const getChannelDeubggingColumns = (setActiveTabKey: (tabKey: TabKey) => void) => {
  const columns: ColumnType<ChannelDebugging>[] = [
    {
      title: '设备名称',
      dataIndex: 'deviceName',
      fixed: 'left',
      render: text => {
        return (
          <Typography.Link onClick={() => setActiveTabKey('deviceInfo')}>{text}</Typography.Link>
        );
      },
    },
    {
      title: '设备类型',
      dataIndex: 'deviceType',
      render: text => {
        return text ? <DeviceTypeText code={text} /> : BLANK;
      },
    },
    { title: '设备guid', dataIndex: 'deviceGuid' },
  ];
  return columns;
};

export const channelDeubggingPointColumns: ColumnType<PointData>[] = [
  { title: '测点ID', dataIndex: 'pointCode' },
  { title: '测点名称', dataIndex: 'pointName' },
  { title: '测点值', dataIndex: 'value' },
  {
    title: '工作区间校验',
    dataIndex: 'outOfRange',
    render: (_, record) => {
      if (record.outOfRange) {
        return <Typography.Text type="danger">异常</Typography.Text>;
      }
      return '正常';
    },
  },
  {
    title: '其他校验',
    dataIndex: 'error',
    render: (_, record) => {
      if (record.error) {
        return <Typography.Text type="danger">{record.errMsg}</Typography.Text>;
      }
      return '';
    },
  },
];
