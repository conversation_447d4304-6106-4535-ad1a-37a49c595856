import React, { useCallback, useEffect, useState } from 'react';
import { Link } from 'react-router-dom';

import { saveAs } from 'file-saver';

import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';

import type { ChannelConfigJSON } from '@manyun/monitoring.model.channel-config';
import { generateChannelDeviceRoutePath } from '@manyun/monitoring.route.monitoring-routes';
import { deleteChannelPoint } from '@manyun/monitoring.service.delete-channel-point';
import { exportChannelDevice } from '@manyun/monitoring.service.export-channel-device';
import {
  ChannelDevice,
  SvcQuery as ChannelDeviceParams,
  fetchChannelDevice,
} from '@manyun/monitoring.service.fetch-channel-device';
import { DeviceTypeCascader } from '@manyun/resource-hub.ui.device-type-cascader';

import { getDeviceInfoColumns } from '../utils';
import { PointList } from './point-list';

export type DeviceInfoProps = { channelConfig: ChannelConfigJSON };

type Params = Omit<ChannelDeviceParams, 'channelId'>;

export function DeviceInfo({ channelConfig }: DeviceInfoProps) {
  const [loading, setLoading] = useState<boolean>(false);
  const [pagination, setPagination] = useState({ pageNum: 1, pageSize: 10, total: 0 });
  const [deviceName, setDeviceName] = useState<string>();
  const [deviceType, setSeviceType] = useState<string>();

  const [channelDeviceList, setChannelDeviceList] = useState<ChannelDevice[]>([]);

  const [selectChannelDevice, setSelectChannelDevice] = useState<ChannelDevice>();

  const getChannelDeviceList = useCallback(
    async (params: Params) => {
      setLoading(true);
      const { error, data } = await fetchChannelDevice({ ...params, channelId: channelConfig.id });
      setLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      setChannelDeviceList(data.data);
      setPagination({ pageNum: params.pageNum, pageSize: params.pageSize, total: data.total });
    },
    [channelConfig.id]
  );

  useEffect(() => {
    getChannelDeviceList({
      pageNum: 1,
      pageSize: 10,
    });
  }, [getChannelDeviceList]);

  const onSearch = (value: string) => {
    const params = {
      deviceName: value,
      deviceType,
      pageNum: 1,
      pageSize: pagination.pageSize,
    };
    getChannelDeviceList(params);
  };

  const onSelectDevice = (value: string) => {
    setSeviceType(value);
    const params = {
      deviceName,
      deviceType: value,
      pageNum: 1,
      pageSize: 10,
    };
    getChannelDeviceList(params);
  };

  const onDelete = (record: ChannelDevice) => {
    Modal.confirm({
      title: '您即将删除此数据！',
      okText: '确定删除',
      cancelText: '取消',
      onOk: async () => {
        const { error } = await deleteChannelPoint({
          channelId: channelConfig.id,
          deviceGuid: record.deviceGuid,
        });
        if (error) {
          message.error(error.message);
          return;
        }
        getChannelDeviceList({ deviceName, deviceType, pageNum: 1, pageSize: 10 });
      },
    });
  };

  const onShowPointList = (channelDevice: ChannelDevice) => {
    setSelectChannelDevice(channelDevice);
  };

  const onHidePointList = () => {
    setSelectChannelDevice(undefined);
  };

  const onExportClick = async () => {
    const { error, data } = await exportChannelDevice({
      channelId: channelConfig.id,
    });
    if (error) {
      message.error(error.message);
      return;
    }
    if (data) {
      saveAs(data, '通道设备信息.xls');
      message.success('导出成功');
    }
  };
  return (
    <>
      {selectChannelDevice ? (
        <PointList
          channelDevice={selectChannelDevice}
          onHidePointList={onHidePointList}
          protocol={channelConfig.protocol}
        />
      ) : (
        <>
          <Space size={16}>
            <Button type="primary" onClick={onExportClick}>
              导出设备信息
            </Button>
            <Link to={generateChannelDeviceRoutePath({ id: channelConfig.id })}>
              <Button>导入设备信息</Button>
            </Link>
            <Input.Search
              allowClear
              placeholder="设备名称/ID"
              onChange={e => setDeviceName(e.target.value)}
              onSearch={onSearch}
              style={{ width: 216 }}
            />
            <DeviceTypeCascader
              numbered
              dataType={['snDevice']}
              category="categorycode"
              disabledTypeList={['C0', 'C1']}
              placeholder="请选择设备类型"
              onChange={(value: string) => {
                onSelectDevice(value);
              }}
              allowClear
              style={{ width: 216 }}
            />
          </Space>
          <Table
            style={{ marginTop: 16 }}
            scroll={{ x: 'max-content' }}
            dataSource={channelDeviceList}
            rowKey="id"
            columns={getDeviceInfoColumns({ onDelete, onShowPointList })}
            loading={loading}
            pagination={{
              total: pagination.total,
              current: pagination.pageNum,
              pageSize: pagination.pageSize,
              onChange: (pageNum, pageSize) => {
                getChannelDeviceList({ deviceName, deviceType, pageNum, pageSize });
              },
            }}
          />
        </>
      )}
    </>
  );
}
