import React, { useCallback, useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';

import { ArrowLeftOutlined } from '@ant-design/icons';
import type { CheckboxChangeEvent } from 'antd/es/checkbox';

import { Button } from '@manyun/base-ui.ui.button';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import { configSliceActions as configActions } from '@manyun/dc-brain.state.config';
import type { ChannelConfigProtocol } from '@manyun/monitoring.model.channel-config';
import { deleteChannelPoint } from '@manyun/monitoring.service.delete-channel-point';
import type { ChannelDevice } from '@manyun/monitoring.service.fetch-channel-device';
import type {
  ChannelPoint,
  SvcQuery as PointParams,
} from '@manyun/monitoring.service.fetch-channel-point';
import { fetchChannelPoint } from '@manyun/monitoring.service.fetch-channel-point';
import {
  SUBSCRIPTIONS_MODE,
  addRealtimeNAlarmsDataSubscriptionActionCreator as subscribe,
  removeRealtimeNAlarmsDataSubscriptionActionCreator as unsubscribe,
} from '@manyun/monitoring.state.subscriptions';
import { PointTypeSelect } from '@manyun/resource-hub.ui.point-type-select';

import { PointDrawer } from './components/point-drawer';
import { PointListTable } from './components/point-table';

export type PointListProps = {
  channelDevice: ChannelDevice;
  protocol: ChannelConfigProtocol;
  onHidePointList: () => void;
};

type Params = Omit<PointParams, 'channelId' | 'deviceGuid'> & { pageNum: number; pageSize: number };

export type Mode = 'create' | 'edit' | 'copy';

const moduleId = 'channel-point';

const realTimemode = SUBSCRIPTIONS_MODE.REALTIME_N_ALARMS;

export function PointList({ channelDevice, protocol, onHidePointList }: PointListProps) {
  const dispatch = useDispatch();

  const [loading, setLoading] = useState<boolean>(false);

  const [pointName, setPointName] = useState<string>();
  const [pointDataType, setPointDataType] = useState<string>();
  const [showAll, setShowAll] = useState<boolean>(true);
  const [pagination, setPagination] = useState<{
    pageNum: number;
    pageSize: number;
    total: number;
  }>({ pageNum: 1, pageSize: 10, total: 0 });

  const [channelPointList, setChannelPointList] = useState<ChannelPoint[]>([]);

  const [mode, setMode] = useState<Mode>('create');
  const [visible, setVisible] = useState<boolean>(false);
  const [selectChannelPoint, setSelectChannelPoint] = useState<ChannelPoint>();

  const getPointList = useCallback(
    async (params: Params) => {
      const { pageNum, pageSize } = params;
      setLoading(true);
      const { error, data } = await fetchChannelPoint({
        ...params,
        channelId: channelDevice.channelId,
        deviceGuid: channelDevice.deviceGuid,
      });
      setLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      setChannelPointList(data.data.slice((pageNum - 1) * pageSize, pageNum * pageSize));
      setPagination({ pageNum, pageSize, total: data.data.length });
    },
    [channelDevice.channelId, channelDevice.deviceGuid]
  );

  useEffect(() => {
    getPointList({ showAll: true, pageNum: pagination.pageNum, pageSize: pagination.pageSize });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    dispatch(
      subscribe({
        mode: realTimemode,
        blockGuid: channelDevice.spaceGuid,
        moduleId,
        deviceGuids: [channelDevice.deviceGuid],
      })
    );
    return () => {
      dispatch(unsubscribe({ blockGuid: channelDevice.spaceGuid, mode: realTimemode, moduleId }));
    };
  }, [channelDevice.deviceGuid, channelDevice.spaceGuid, dispatch]);

  useEffect(() => {
    channelPointList.forEach(item => {
      dispatch(
        configActions.updatePointsDefinitionMap({
          [channelDevice.deviceType]: {
            [item.pointCode]: {
              name: item.pointName,
              dataType: item.pointDataType.code,
              unit: item.unit,
              validLimits: item.validLimits,
            },
          },
        })
      );
    });
  }, [channelDevice.deviceType, channelPointList, dispatch]);

  const onPointNameSearch = (value: string) => {
    const params = {
      pointName: value,
      pointDataType,
      showAll,
      pageNum: 1,
      pageSize: pagination.pageSize,
    };
    getPointList(params);
  };

  const onPointTypeChange = (value: string) => {
    setPointDataType(value);
    const params = {
      pointName,
      pointDataType: value,
      showAll,
      pageNum: 1,
      pageSize: pagination.pageSize,
    };
    getPointList(params);
  };

  const onChange = (e: CheckboxChangeEvent) => {
    setShowAll(e.target.checked);
    const params = {
      pointName,
      pointDataType,
      showAll: e.target.checked,
      pageNum: 1,
      pageSize: pagination.pageSize,
    };
    getPointList(params);
  };

  const onRefresh = () => {
    getPointList({ pointName, showAll, pointDataType, pageNum: 1, pageSize: pagination.pageSize });
  };

  const onChangePage = (pageNum: number, pageSize: number) => {
    getPointList({ pointName, showAll, pointDataType, pageNum, pageSize });
  };

  const onEdit = ({ record, mode }: { record?: ChannelPoint; mode: Mode }) => {
    setMode(mode);
    setVisible(true);
    if (record) {
      setSelectChannelPoint(record);
    }
  };

  const onDelete = async (record: ChannelPoint) => {
    Modal.confirm({
      title: '您即将删除此数据！',
      okText: '确定删除',
      cancelText: '取消',
      onOk: async () => {
        const { error } = await deleteChannelPoint({
          channelId: channelDevice.channelId,
          deviceGuid: channelDevice.deviceGuid,
          pointCode: record.pointCode,
        });
        if (error) {
          message.error(error.message);
          return;
        }
        message.success('删除成功');
        onRefresh();
      },
    });
  };

  return (
    <Space direction="vertical" style={{ width: '100%' }} size={16}>
      <Space style={{ cursor: 'pointer' }} onClick={onHidePointList}>
        <ArrowLeftOutlined style={{ fontSize: 14 }} />
        <Typography.Text>{channelDevice.deviceName}</Typography.Text>
      </Space>
      <Space size={16}>
        <Button type="primary" onClick={onRefresh}>
          刷新
        </Button>
        <Button onClick={() => onEdit({ mode: 'create' })}>新增测点</Button>
        <Input.Search
          allowClear
          placeholder="搜索测点名称/ID"
          style={{ width: 216 }}
          onChange={e => setPointName(e.target.value)}
          onSearch={onPointNameSearch}
        />
        <PointTypeSelect style={{ width: 216 }} allowClear onChange={onPointTypeChange} />
        <Checkbox checked={showAll} onChange={onChange}>
          展示该设备全部原始测点
        </Checkbox>
      </Space>
      <PointListTable
        loading={loading}
        dataSource={channelPointList}
        protocol={protocol}
        pagination={{
          total: pagination.total,
          current: pagination.pageNum,
          pageSize: pagination.pageSize,
          onChange: onChangePage,
        }}
        onEdit={onEdit}
        onDelete={onDelete}
      />
      <PointDrawer
        visible={visible}
        mode={mode}
        channelPointList={channelPointList}
        selectChannelPoint={selectChannelPoint}
        channelDevice={channelDevice}
        protocol={protocol}
        onClose={() => setVisible(false)}
        onRefresh={onRefresh}
      />
    </Space>
  );
}
