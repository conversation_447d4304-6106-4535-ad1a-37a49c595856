import React, { useMemo, useState } from 'react';

import useDeepCompareEffect from 'use-deep-compare-effect';

import { Button } from '@manyun/base-ui.ui.button';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Select } from '@manyun/base-ui.ui.select';
import type { SelectProps } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import type { ChannelConfigProtocol } from '@manyun/monitoring.model.channel-config';
import type { ApiQ as ChannelPointParams } from '@manyun/monitoring.service.create-channel-point';
import { createChannelPoint } from '@manyun/monitoring.service.create-channel-point';
import type { ChannelDevice } from '@manyun/monitoring.service.fetch-channel-device';
import type { ChannelPoint } from '@manyun/monitoring.service.fetch-channel-point';
import { fetchChannelUnconfigPoint } from '@manyun/monitoring.service.fetch-channel-unconfig-point';
import { updateChannelPoint } from '@manyun/monitoring.service.update-channel-point';
import { ChannelChangeModeSelect } from '@manyun/monitoring.ui.channel-change-mode-select';
import { ChannelDataTypeSelect } from '@manyun/monitoring.ui.channel-data-type-select';
import { OperatorTypeSelect } from '@manyun/monitoring.ui.operator-type-select';
import { getSpaceGuid, getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

import type { Mode } from '../../index';

export type PointDrawerProps = {
  visible: boolean;
  onClose: () => void;
  onRefresh: () => void;
  mode: Mode;
  channelPointList: ChannelPoint[];
  selectChannelPoint?: ChannelPoint;
  channelDevice: ChannelDevice;
  protocol: ChannelConfigProtocol;
};

type ChannelPointFormParams = Omit<
  ChannelPointParams,
  'channelId' | 'blockGuid' | 'deviceGuid' | 'deviceType'
>;

export function PointDrawer({
  visible,
  onClose,
  channelDevice,
  mode,
  channelPointList,
  selectChannelPoint,
  onRefresh,
  protocol,
}: PointDrawerProps) {
  const [form] = Form.useForm();
  const { setFieldsValue, validateFields } = form;
  const [loading, setLoading] = useState(false);

  const [pointOptions, setPointOptions] = useState<SelectProps['options']>([]);

  const noOdccAndBAndB1Protocol = useMemo(
    () => protocol === 'B' || protocol === 'B1' || protocol === 'ODCC',
    [protocol]
  );

  useDeepCompareEffect(() => {
    if (!visible) {
      return;
    }
    if (mode === 'create') {
      (async () => {
        const { error, data } = await fetchChannelUnconfigPoint({
          channelId: channelDevice.channelId,
          deviceGuid: channelDevice.deviceGuid,
          deviceType: channelDevice.deviceType,
        });
        if (error) {
          message.error(error.message);
          return;
        }
        const pointOptions = Object.keys(data).map(item => {
          return { value: item, label: `${data[item]}(${item})` };
        });
        setPointOptions(pointOptions);
      })();
    } else {
      const pointOptions = channelPointList.map(item => {
        return {
          value: item.pointCode,
          label: `${item.pointName}(${item.pointCode})`,
        };
      });
      setPointOptions(pointOptions);
    }
    if ((mode === 'copy' || mode === 'edit') && selectChannelPoint) {
      const {
        pointCode,
        dataType,
        operationType,
        changeMode,
        elementId,
        position,
        address,
        registerSwap,
        bitCount,
        pointGuid,
        targetGuid,
        formula,
      } = selectChannelPoint;

      if (noOdccAndBAndB1Protocol) {
        setFieldsValue({
          pointCode,
          pointGuid,
          targetGuid,
          formula,
        });
      } else {
        setFieldsValue({
          pointCode,
          dataType: dataType ? dataType.code : null,
          operationType: operationType ? operationType.code : null,
          changeMode: changeMode ? changeMode.code : null,
          elementId,
          position,
          address,
          registerSwap,
          bitCount,
          formula,
        });
      }
    }
  }, [
    channelDevice,
    channelPointList,
    mode,
    noOdccAndBAndB1Protocol,
    selectChannelPoint,
    setFieldsValue,
    visible,
  ]);

  const blockGuid = useMemo(() => {
    const { idc, block } = getSpaceGuidMap(channelDevice.spaceGuid);
    if (idc) {
      return getSpaceGuid(idc, block);
    }
    return null;
  }, [channelDevice.spaceGuid]);

  const onSubmit = async () => {
    const values = await validateFields();
    const {
      pointCode,
      dataType,
      operationType,
      changeMode,
      elementId,
      position,
      address,
      registerSwap,
      bitCount,
      pointGuid,
      targetGuid,
      formula,
    } = values;

    let params: ChannelPointFormParams;

    if (noOdccAndBAndB1Protocol) {
      params = {
        pointGuid,
        targetGuid,
        pointCode,
        formula,
      };
    } else {
      params = {
        pointCode,
        dataType,
        operationType,
        changeMode,
        elementId,
        position,
        address,
        registerSwap,
        bitCount,
        formula,
      };
    }

    if (!blockGuid) {
      return;
    }

    if (mode === 'edit' && selectChannelPoint) {
      const { error, data } = await updateChannelPoint({
        id: selectChannelPoint.id,
        channelId: channelDevice.channelId,
        blockGuid,
        ...params,
      });
      setLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      onClose();
      onRefresh();
      message.success('编辑成功！');
      return data;
    } else {
      const { error, data } = await createChannelPoint({
        channelId: channelDevice.channelId,
        blockGuid,
        deviceGuid: channelDevice.deviceGuid,
        deviceType: channelDevice.deviceType,
        ...params,
      });
      setLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      onClose();
      onRefresh();
      message.success('创建成功！');
      return data;
    }
  };
  return (
    <Drawer
      title={
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography.Text>{mode === 'edit' ? '编辑测点' : '新增测点'}</Typography.Text>
          <Space>
            <Button type="primary" loading={loading} onClick={onSubmit}>
              确定
            </Button>
            <Button onClick={onClose}>取消</Button>
          </Space>
        </div>
      }
      destroyOnClose
      bodyStyle={{ overflowY: 'auto' }}
      placement="right"
      visible={visible}
      contentWrapperStyle={{ width: 572 }}
      onClose={onClose}
    >
      <Form
        layout="horizontal"
        form={form}
        style={{ width: 600 }}
        colon
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
      >
        <Form.Item
          label="测点名称"
          name="pointCode"
          rules={[
            {
              required: true,
              message: '测点名称必填！',
            },
          ]}
        >
          <Select style={{ width: 224 }} options={pointOptions} disabled={mode === 'edit'} />
        </Form.Item>
        {noOdccAndBAndB1Protocol ? (
          <>
            <Form.Item label="原始测点" name="pointGuid">
              <Input style={{ width: 224 }} />
            </Form.Item>
            <Form.Item label="原始设备" name="targetGuid">
              <Input style={{ width: 224 }} />
            </Form.Item>
          </>
        ) : (
          <>
            <Form.Item label="数据类型" name="dataType">
              <ChannelDataTypeSelect style={{ width: 224 }} />
            </Form.Item>
            <Form.Item label="操作类型" name="operationType">
              <OperatorTypeSelect style={{ width: 224 }} />
            </Form.Item>
            <Form.Item label="转换方式" name="changeMode">
              <ChannelChangeModeSelect style={{ width: 224 }} />
            </Form.Item>
            <Form.Item label="单元标识符" name="elementId">
              <InputNumber style={{ width: 224 }} />
            </Form.Item>
            <Form.Item label="偏移位" name="position">
              <InputNumber style={{ width: 224 }} />
            </Form.Item>
            <Form.Item label="寄存器起始地址" name="address">
              <InputNumber style={{ width: 224 }} />
            </Form.Item>
            <Form.Item label="是否交换寄存器" name="registerSwap" initialValue>
              <Radio.Group>
                <Radio key="true" value>
                  是
                </Radio>
                <Radio key="false" value={false}>
                  否
                </Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item label="bit总数" name="bitCount">
              <InputNumber style={{ width: 224 }} />
            </Form.Item>
          </>
        )}

        <Form.Item label="表达式" name="formula">
          <Input style={{ width: 224 }} />
        </Form.Item>
      </Form>
    </Drawer>
  );
}
