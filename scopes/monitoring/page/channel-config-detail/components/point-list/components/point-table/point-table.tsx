import React from 'react';

import dayjs from 'dayjs';

import { Badge } from '@manyun/base-ui.ui.badge';
import { Button } from '@manyun/base-ui.ui.button';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Space } from '@manyun/base-ui.ui.space';
import type { ColumnType, TableProps } from '@manyun/base-ui.ui.table';
import { Table } from '@manyun/base-ui.ui.table';

import { PointsLineModalButton } from '@manyun/monitoring.chart.points-line';
import { PointsStateLineModalButton } from '@manyun/monitoring.chart.points-state-line';
import {
  generatePointValueMapping,
  generateValidLimitsDataSource,
} from '@manyun/monitoring.model.point';
import type { ChannelPoint } from '@manyun/monitoring.service.fetch-channel-point';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

import type { Mode } from '../../point-list';

const BLANK = '--';

export const getPointColumns = ({
  protocol,
  onEdit,
  onDelete,
}: {
  protocol: string;
  onEdit: ({ record, mode }: { record: ChannelPoint; mode: Mode }) => void;
  onDelete: (record: ChannelPoint) => void;
}) => {
  const columns: ColumnType<ChannelPoint>[] = [
    { title: '测点名称', dataIndex: 'pointName', fixed: 'left' },
    { title: '测点id', dataIndex: 'pointCode' },
    {
      title: '点位类型',
      dataIndex: 'pointDataType',
      render: (_, record) => {
        if (!record.pointDataType) {
          return BLANK;
        }
        return record.pointDataType.name;
      },
    },
    {
      title: '最新值',
      dataIndex: 'value',
      render: (text, record) => {
        if (!record.deviceGuid) {
          return BLANK;
        }

        const isAnalogSignal =
          record.pointDataType.code === 'AI' || record.pointDataType.code === 'AO';

        const valueMapping = record.validLimits
          ? generatePointValueMapping(record.validLimits)
          : {};

        const btnText = (isAnalogSignal ? text : valueMapping[text]?.NAME) ?? BLANK;

        const { idc } = getSpaceGuidMap(record.blockGuid);
        const pointGuids = [
          {
            deviceGuid: record.deviceGuid,
            pointCode: record.pointCode,
            unit: record.unit,
          },
        ];

        const seriesOption = [{ name: record.pointName }];

        return (
          <>
            {isAnalogSignal ? (
              <PointsLineModalButton
                idcTag={idc!}
                btnText={btnText}
                modalText={record.pointName}
                pointGuids={pointGuids}
                seriesOption={seriesOption}
              />
            ) : (
              <PointsStateLineModalButton
                idcTag={idc!}
                btnText={btnText}
                modalText={record.pointName}
                pointGuids={pointGuids}
                seriesOption={seriesOption}
                validLimitsMap={record.validLimits || {}}
              />
            )}
          </>
        );
      },
    },
    {
      title: '数据时间',
      dataIndex: 'data',
      render: (_, record) => {
        if (record.gmtModified) {
          return dayjs(record.gmtModified).format('YYYY-MM-DD HH:mm:ss');
        }
        if (record.gmtCreate) {
          return dayjs(record.gmtCreate).format('YYYY-MM-DD HH:mm:ss');
        }
        return BLANK;
      },
    },
    {
      title: '工作区间',
      dataIndex: 'validLimits',
      render: (validLimits, record) => {
        //临时解决方案，一月份需要建立channelPoint的model进行重构
        if (validLimits) {
          const validLimitsDataSource = generateValidLimitsDataSource(validLimits);
          const validLimitLabels: string[] = validLimitsDataSource.map(item => {
            return item.label;
          });
          if (record.pointDataType.code === 'AI' || record.pointDataType.code === 'AO') {
            return validLimitLabels.join('~');
          } else {
            return validLimitLabels.join(',');
          }
        }

        return BLANK;
      },
    },
    {
      title: '工作区间检查',
      dataIndex: 'validLimitsCheck',
      render: (_, record) => {
        if (record.validLimits && !isNaN(Number(record.value))) {
          const isAnalogSignal =
            record.pointDataType.code === 'AI' || record.pointDataType.code === 'AO';

          if (isAnalogSignal) {
            const [start, end] = getValidLimitsValuesMap(record.validLimits);

            if (record.value <= end && record.value >= start) {
              return <Badge status="success" text="正常" />;
            }
            return <Badge status="error" text="异常" />;
          } else {
            const valueMapping = generatePointValueMapping(record.validLimits);
            if (valueMapping[record.value]) {
              return <Badge status="success" text="正常" />;
            }
            return <Badge status="error" text="异常" />;
          }
        }
        return BLANK;
      },
    },
    {
      title: '数据类型',
      dataIndex: 'dataType',
      render: (_, record) => {
        if (!record.dataType) {
          return BLANK;
        }
        return record.dataType.name;
      },
    },
    {
      title: '操作类型',
      dataIndex: 'operationType',
      render: (_, record) => {
        if (!record.operationType) {
          return BLANK;
        }
        return record.operationType.name;
      },
    },
    { title: '缩放因子', dataIndex: 'zoom' },
    { title: '基数', dataIndex: 'baseValue' },
    {
      title: '转换方式',
      dataIndex: 'changeMode',
      render: (_, record) => {
        if (!record.changeMode) {
          return BLANK;
        }
        return record.changeMode.name;
      },
    },
    { title: '单元标识符', dataIndex: 'elementId' },
    { title: '偏移位', dataIndex: 'position' },
    { title: '寄存器起始地址', dataIndex: 'address' },
    {
      title: '是否交换寄存器位置',
      dataIndex: 'registerSwap',
      render: registerSwap => {
        if (registerSwap === null || registerSwap === undefined) {
          return '';
        }
        if (registerSwap) {
          return '是';
        }
        return '否';
      },
    },
    { title: '原始测点', dataIndex: 'pointGuid' },
    { title: '原始设备', dataIndex: 'targetGuid' },
    { title: '加工表达式', dataIndex: 'formula' },
    {
      title: '操作',
      dataIndex: 'actions',
      fixed: 'right',
      render: (_, record) => (
        <Space split={<Divider type="vertical" />} size={0}>
          {record.deviceGuid ? (
            <>
              <Button type="link" compact onClick={() => onEdit({ record, mode: 'edit' })}>
                编辑
              </Button>
              <Button type="link" compact onClick={() => onDelete(record)}>
                删除
              </Button>
              <Button type="link" compact onClick={() => onEdit({ record, mode: 'copy' })}>
                复制
              </Button>
            </>
          ) : (
            <Button type="link" compact onClick={() => onEdit({ record, mode: 'create' })}>
              创建
            </Button>
          )}
        </Space>
      ),
    },
  ];
  if (protocol === 'MODBUS_TCP' || protocol === 'MODBUS_TCP_SYNC') {
    const moduleBusDataIndexs: string[] = [
      'pointName',
      'pointCode',
      'pointDataType',
      'value',
      'data',
      'validLimits',
      'validLimitsCheck',
      'dataType',
      'operationType',
      'changeMode',
      'elementId',
      'position',
      'address',
      'registerSwap',
      'formula',
      'actions',
    ];
    const newColumns = moduleBusDataIndexs
      .map(columnCode => {
        const columnItem = columns.find(item => item.dataIndex === columnCode);
        return columnItem;
      })
      .filter(item => item);
    return newColumns;
  }
  if (protocol === 'ODCC' || protocol === 'B' || protocol === 'B1') {
    const odccDataIndexs: string[] = [
      'pointName',
      'pointCode',
      'pointDataType',
      'value',
      'data',
      'validLimits',
      'validLimitsCheck',
      'pointGuid',
      'targetGuid',
      'formula',
      'actions',
    ];
    const newColumns = odccDataIndexs
      .map(columnCode => {
        const columnItem = columns.find(item => item.dataIndex === columnCode);
        return columnItem;
      })
      .filter(item => item);
    return newColumns;
  }
  return columns;
};

export type PointListTableProps = {
  protocol: string;
  onEdit: ({ record, mode }: { record: ChannelPoint; mode: Mode }) => void;
  onDelete: (record: ChannelPoint) => void;
} & Omit<TableProps<ChannelPoint>, 'columns'>;

export function PointListTable({ protocol, onEdit, onDelete, ...rest }: PointListTableProps) {
  return (
    <Table
      scroll={{ x: 'max-content' }}
      rowKey="pointCode"
      columns={getPointColumns({ protocol, onEdit, onDelete })}
      {...rest}
    />
  );
}

const getValidLimitsValuesMap = (validLimits: string[]) => {
  if (!validLimits) {
    return BLANK;
  }
  const validLimitValues = validLimits.map(item => {
    const limitItem = item.split('=');
    if (limitItem.length > 1) {
      return limitItem[1];
    }
    return '';
  });

  return validLimitValues;
};
