import React, { useContext, useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';

import type { ChannelConfigJSON } from '@manyun/monitoring.model.channel-config';
import {
  ChannelDebugging,
  fetchChannelDebugging,
} from '@manyun/monitoring.service.fetch-channel-debugging';

import type { TabKey } from '../channel-config-detail';
import { ChannelConfigDetailContext } from '../channel-config-detail-context';
import styles from '../channel-config-detail.module.less';
import { channelDeubggingPointColumns, getChannelDeubggingColumns } from '../utils';

export type ChannelDeubggingProps = {
  channelConfig: ChannelConfigJSON;
  setActiveTabKey: (tabKey: TabKey) => void;
};

const expandedRowRender = (record: ChannelDebugging, isHaveOtherColumn?: boolean) => {
  return (
    <Table
      columns={
        isHaveOtherColumn
          ? channelDeubggingPointColumns.filter(item => item.dataIndex !== 'error')
          : channelDeubggingPointColumns
      }
      dataSource={record?.pointDataList}
      pagination={false}
    />
  );
};

export function ChannelDeubgging({ channelConfig, setActiveTabKey }: ChannelDeubggingProps) {
  const contextValue = useContext(ChannelConfigDetailContext);

  const [loading, setLoading] = useState<boolean>(false);

  const [channelChannelSuccessDeubgging, setChannelChannelSuccessDeubgging] = useState<
    ChannelDebugging[]
  >([]);
  const [channelChannelErrorDeubgging, setChannelChannelErrorDeubgging] = useState<
    ChannelDebugging[]
  >([]);

  const onOpenDeubgging = async () => {
    setLoading(true);
    const { error, data } = await fetchChannelDebugging({ channelId: channelConfig.id });
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    const channelChannelSuccessDeubgging = data.data
      .map(item => {
        const { pointDataList } = item;
        const pointErrorItem = pointDataList?.filter(
          pointItem => !pointItem.outOfRange && !pointItem.error
        );
        return {
          deviceName: item.deviceName,
          deviceType: item.deviceType,
          deviceGuid: item.deviceGuid,
          pointDataList: pointErrorItem,
        };
      })
      .filter(item => item.pointDataList);
    const channelChannelErrorDeubgging = data.data
      .map(item => {
        const { pointDataList } = item;
        const pointErrorItem = pointDataList?.filter(
          pointItem => pointItem.outOfRange || pointItem.error
        );
        return {
          deviceName: item.deviceName,
          deviceType: item.deviceType,
          deviceGuid: item.deviceGuid,
          pointDataList: pointErrorItem,
        };
      })
      .filter(item => Array.isArray(item.pointDataList) && item.pointDataList.length);
    setChannelChannelErrorDeubgging(channelChannelErrorDeubgging);
    setChannelChannelSuccessDeubgging(channelChannelSuccessDeubgging);
  };

  const onClear = () => {
    setChannelChannelErrorDeubgging([]);
    setChannelChannelSuccessDeubgging([]);
  };

  return (
    <Space direction="vertical" style={{ width: '100%' }} size={16}>
      <Space size={16}>
        <Button type="primary" onClick={onOpenDeubgging}>
          启动调试
        </Button>
        <Button onClick={onClear}>清空记录</Button>
      </Space>
      <div className={styles.channelDebugging}>
        <Card className={styles.channelDebuggingCard}>
          <Table
            scroll={{ x: 'max-content' }}
            dataSource={channelChannelSuccessDeubgging}
            rowKey="deviceGuid"
            columns={getChannelDeubggingColumns(setActiveTabKey)}
            expandable={{
              expandedRowRender: (record: ChannelDebugging) => expandedRowRender(record, true),
              defaultExpandedRowKeys: ['0'],
            }}
            loading={loading}
          />
        </Card>
        <Card className={styles.channelDebuggingCard}>
          <Table
            scroll={{ x: 'max-content' }}
            dataSource={channelChannelErrorDeubgging}
            rowKey="deviceGuid"
            columns={getChannelDeubggingColumns(contextValue.setActiveTabKey)}
            expandable={{
              expandedRowRender: (record: ChannelDebugging) => expandedRowRender(record, false),
              defaultExpandedRowKeys: ['0'],
            }}
            loading={loading}
          />
        </Card>
      </div>
    </Space>
  );
}
