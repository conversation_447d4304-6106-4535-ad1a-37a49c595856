import uniqueId from 'lodash/uniqueId';
import React, { useMemo } from 'react';
import { Link } from 'react-router-dom';

import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Typography } from '@manyun/base-ui.ui.typography';
import { generateServerNodeDetailPath } from '@manyun/dc-brain.route.admin-routes';
import type { ChannelConfigJSON } from '@manyun/monitoring.model.channel-config';
import {
  generateRoomMonitoringUrl,
  generateSpaceOrDeviceRoutePath,
} from '@manyun/monitoring.route.monitoring-routes';
import type { TableConfig } from '@manyun/monitoring.ui.channels-table';
import { ChannelConfigOperations } from '@manyun/monitoring.ui.channels-table';
import { DeviceTypeText } from '@manyun/resource-hub.ui.device-type-text';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

export type BasicInfoProps = { channelConfig: ChannelConfigJSON };

const BLANK = '--';

export function BasicInfo({ channelConfig }: BasicInfoProps) {
  const tableConfigList = useMemo(() => {
    if (!channelConfig.config) {
      return [];
    }
    const configList: TableConfig[] = channelConfig.config.map(item => {
      return {
        ...item,
        status: 'readOnly',
        id: uniqueId('operationConfig'),
      };
    });
    return configList;
  }, [channelConfig.config]);

  const { idc, block } = getSpaceGuidMap(channelConfig.blockGuid);
  return (
    <>
      <Descriptions column={4} title="基本配置">
        <Descriptions.Item label="通道名称">{channelConfig.name}</Descriptions.Item>
        <Descriptions.Item label="通道ID">{channelConfig.id}</Descriptions.Item>
        <Descriptions.Item label="通信状态">
          {channelConfig?.channelCommunicationStatus?.name ?? BLANK}
        </Descriptions.Item>
        <Descriptions.Item label="所属节点">
          {channelConfig.node.id ? (
            <Link
              to={generateServerNodeDetailPath({ id: String(channelConfig.node.id) })}
              target="_blank"
            >
              {channelConfig.node.name}
            </Link>
          ) : (
            BLANK
          )}
        </Descriptions.Item>
        <Descriptions.Item label="位置">{channelConfig.blockGuid}</Descriptions.Item>
        <Descriptions.Item label="IP">{channelConfig.ip}</Descriptions.Item>
        <Descriptions.Item label="通道端口">{channelConfig.port}</Descriptions.Item>
        <Descriptions.Item label="通道类型">{channelConfig.channelType.name}</Descriptions.Item>
        <Descriptions.Item label="支持协议">{channelConfig.protocol}</Descriptions.Item>
        <Descriptions.Item label="状态">{channelConfig.channelStatus.name}</Descriptions.Item>
        <Descriptions.Item label="用户名">{channelConfig.username ?? BLANK}</Descriptions.Item>
        <Descriptions.Item label="密码">{channelConfig.password ?? BLANK}</Descriptions.Item>
        <Descriptions.Item label="通信设备">
          {channelConfig.device.guid ? (
            <Link
              to={generateSpaceOrDeviceRoutePath({
                guid: channelConfig.device.guid,
                type: 'DEVICE',
              })}
              target="_blank"
            >
              {channelConfig.device.name}
            </Link>
          ) : (
            BLANK
          )}
        </Descriptions.Item>
        <Descriptions.Item label="厂商型号">{channelConfig.vendor ?? BLANK}</Descriptions.Item>
        <Descriptions.Item label="设备类型">
          {channelConfig.device.type ? <DeviceTypeText code={channelConfig.device.type} /> : BLANK}
        </Descriptions.Item>
        <Descriptions.Item label="所在包间">
          {channelConfig.roomTag ? (
            <Link
              target="_blank"
              onClick={() => {
                window.open(
                  generateRoomMonitoringUrl({
                    idc: idc!,
                    block: block!,
                    room: channelConfig.roomTag,
                  })
                );
              }}
            >
              {channelConfig.roomTag}
            </Link>
          ) : (
            BLANK
          )}
        </Descriptions.Item>
      </Descriptions>
      <Typography.Title level={5} style={{ padding: '12px 0' }}>
        操作配置
      </Typography.Title>
      <ChannelConfigOperations tableConfigList={tableConfigList} fromPage="detail" />
    </>
  );
}
