import React, { useEffect, useMemo, useState } from 'react';
import { useLocation, useParams } from 'react-router-dom';

import { Card } from '@manyun/base-ui.ui.card';
import { message } from '@manyun/base-ui.ui.message';
import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';

import type { ChannelConfigJSON } from '@manyun/monitoring.model.channel-config';
import { fetchChannelConfigDetail } from '@manyun/monitoring.service.fetch-channel-config-detail';

import { ChannelConfigDetailContext } from './channel-config-detail-context';
import { BasicInfo } from './components/basic-info';
import { ChannelDeubgging } from './components/channel-debugging';
import { DeviceInfo } from './components/device-info';

export type TabKey = 'basicInfo' | 'deviceInfo' | 'channelDebugging';

const tabList = [
  {
    key: 'basicInfo',
    tab: '基本信息',
  },
  {
    key: 'deviceInfo',
    tab: '设备信息',
  },
  {
    key: 'channelDebugging',
    tab: '通道调试',
  },
];

export function ChannelConfigDetail() {
  const [loading, setLoading] = useState<boolean>(false);
  const { id } = useParams<{ id?: string }>();
  const { search } = useLocation();
  const { tab } = getLocationSearchMap<{ tab: TabKey | undefined }>(search);

  const [channelConfig, setChannelConfig] = useState<ChannelConfigJSON>();

  const [activeTabKey, setActiveTabKey] = useState<TabKey>('basicInfo');

  useEffect(() => {
    if (tab) {
      setActiveTabKey(tab);
    }
  }, [tab]);

  useEffect(() => {
    (async () => {
      if (!id) {
        setChannelConfig(undefined);
        return;
      }
      setLoading(true);
      const { error, data } = await fetchChannelConfigDetail({ channelIds: [id] });
      setLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      if (data.data.length) {
        setChannelConfig(data.data[0]);
      }
    })();
  }, [id]);

  const tabContent = useMemo(() => {
    if (!channelConfig) {
      return '';
    }
    if (activeTabKey === 'basicInfo') {
      return <BasicInfo channelConfig={channelConfig} />;
    }
    if (activeTabKey === 'deviceInfo') {
      return <DeviceInfo channelConfig={channelConfig} />;
    }
    if (activeTabKey === 'channelDebugging') {
      return <ChannelDeubgging channelConfig={channelConfig} setActiveTabKey={setActiveTabKey} />;
    }
    return '';
  }, [activeTabKey, channelConfig]);

  return (
    <ChannelConfigDetailContext.Provider value={{ setActiveTabKey }}>
      <Card
        loading={loading}
        tabList={tabList}
        activeTabKey={activeTabKey}
        onTabChange={key => {
          setActiveTabKey(key as TabKey);
        }}
      >
        {tabContent}
      </Card>
    </ChannelConfigDetailContext.Provider>
  );
}
