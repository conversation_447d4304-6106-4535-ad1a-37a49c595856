import { createContext } from 'react';

import type { LocalAlarmNotice } from '@manyun/monitoring.service.fetch-local-alarm-notices';

export type Handlers = {
  updateLocalAlarmNoticeStatus: (localAlarmNotice: LocalAlarmNotice) => void;
};
export type LocalAlarmNoticesContextProps = [Handlers];
const initialValue: LocalAlarmNoticesContextProps = [
  {
    updateLocalAlarmNoticeStatus: () => {},
  },
];
export const LocalAlarmNoticesContext = createContext<LocalAlarmNoticesContextProps>(initialValue);
