import dayjs from 'dayjs';
import React from 'react';

import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Popover } from '@manyun/base-ui.ui.popover';
import { Space } from '@manyun/base-ui.ui.space';
import type { ColumnType, TableProps } from '@manyun/base-ui.ui.table';
import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { generateLocalAlarmNoticeConfigurationRoutePath } from '@manyun/monitoring.route.monitoring-routes';
import type { LocalAlarmNotice } from '@manyun/monitoring.service.fetch-local-alarm-notices';
import { AlarmLevelText } from '@manyun/monitoring.ui.alarm-level-text';
import { AlarmNoticeTypeText } from '@manyun/monitoring.ui.alarm-notice-type';
import { DeviceTypeText } from '@manyun/resource-hub.ui.device-type-text';

import { LocalAlarmNoticeConfigurationSwitch } from '../local-alarm-notice-configuration-switch';

const LocalAlarmNoticeConfigurationColumns: ColumnType<LocalAlarmNotice>[] = [
  {
    title: '机房楼栋',
    dataIndex: 'targetId',
    fixed: 'left',
  },
  {
    title: '通报项目名称',
    dataIndex: 'name',
    render: (_, record) => {
      if (record.id) {
        return (
          <Popover content={<div style={{ maxWidth: 360 }}>{record.name}</div>}>
            <Typography.Link
              style={{ maxWidth: 360 }}
              ellipsis
              onClick={() => {
                window.open(
                  generateLocalAlarmNoticeConfigurationRoutePath({ id: String(record.id) }),
                  '_blank'
                );
              }}
            >
              {record.name}
            </Typography.Link>
          </Popover>
        );
      }
      return null;
    },
  },
  {
    title: '通报类型',
    dataIndex: 'type',
    render: (_, record) => {
      return <AlarmNoticeTypeText type={record.type} />;
    },
  },
  {
    title: '适用告警等级',
    dataIndex: 'alarmLevels',
    render: alarmLevels => {
      return (
        <Space>
          {alarmLevels.split(',').map((item: string) => (
            <AlarmLevelText key={item} code={item} />
          ))}
        </Space>
      );
    },
  },
  {
    title: '适用专业类型',
    dataIndex: 'deviceType',
    render: deviceType => {
      return (
        <Space>
          {deviceType?.split(',').map((item: string) => <DeviceTypeText key={item} code={item} />)}
        </Space>
      );
    },
  },
  {
    title: '状态',
    dataIndex: 'notified',
    render: (_, record) => <LocalAlarmNoticeConfigurationSwitch localAlarmNotice={record} />,
  },
  {
    title: '操作人',
    dataIndex: 'operator',
    render: (_, record) => (
      <UserLink userId={Number(record.operatorId)} userName={record.operatorName} />
    ),
  },
  {
    title: '操作时间',
    dataIndex: 'gmtModified',
    render: gmtModified => dayjs(gmtModified).format('YYYY-MM-DD HH:mm:ss'),
  },
];

export type ColumnDataIndex =
  | 'targetId'
  | 'name'
  | 'type'
  | 'alarmLevels'
  | 'deviceType'
  | 'notified'
  | 'operator'
  | 'gmtModified';

export type LocalAlarmNoticeConfigurationTableProps = {
  dataIndexs?: ColumnDataIndex[];
  operation?: ColumnType<LocalAlarmNotice>;
} & Omit<TableProps<LocalAlarmNotice>, 'columns'>;

export function LocalAlarmNoticeConfigurationTable({
  dataIndexs = [
    'targetId',
    'name',
    'type',
    'alarmLevels',
    'deviceType',
    'notified',
    'operator',
    'gmtModified',
  ],
  operation,
  ...rest
}: LocalAlarmNoticeConfigurationTableProps) {
  const columns = useDeepCompareMemo(() => {
    const newColumns = dataIndexs
      .map(dataIndex => {
        return LocalAlarmNoticeConfigurationColumns.find(item => item.dataIndex === dataIndex);
      })
      .filter((item): item is ColumnType<LocalAlarmNotice> => item !== undefined);

    if (operation) {
      return [...newColumns, operation];
    }
    return newColumns;
  }, [dataIndexs, operation]);

  return (
    <Table
      columns={columns}
      scroll={{ x: 'max-content' }}
      tableLayout="fixed"
      rowKey="id"
      {...rest}
    />
  );
}
