import React, { useContext } from 'react';

import { Switch } from '@manyun/base-ui.ui.switch';

import type { LocalAlarmNotice } from '@manyun/monitoring.service.fetch-local-alarm-notices';

import { LocalAlarmNoticesContext } from '../../local-alarm-notice-configurations.context';

export type LocalAlarmNoticeConfigurationSwitchProps = {
  localAlarmNotice: LocalAlarmNotice;
};

export function LocalAlarmNoticeConfigurationSwitch({
  localAlarmNotice,
}: LocalAlarmNoticeConfigurationSwitchProps) {
  const [{ updateLocalAlarmNoticeStatus }] = useContext(LocalAlarmNoticesContext);

  const onChange = (checked: boolean) => {
    updateLocalAlarmNoticeStatus({ ...localAlarmNotice, notified: checked });
  };
  return <Switch checked={localAlarmNotice.notified} onChange={onChange} />;
}
