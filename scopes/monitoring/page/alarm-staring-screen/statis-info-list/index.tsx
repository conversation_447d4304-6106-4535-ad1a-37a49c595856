import React, { useMemo } from 'react';
import { useSelector } from 'react-redux';

import { Badge } from '@manyun/base-ui.ui.badge';
import { Carousel } from '@manyun/base-ui.ui.carousel';
import { Row } from '@manyun/base-ui.ui.grid';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import {
  getAlarmsMonitoringBoardLocales,
  selectStatistics,
  selectStatisticsPreferences,
} from '@manyun/monitoring.state.alarms-monitoring-board';
import type { StatisticsVariant } from '@manyun/monitoring.state.alarms-monitoring-board';
import { AlarmLevelText } from '@manyun/monitoring.ui.alarm-level-text';
import { DeviceTypeText } from '@manyun/resource-hub.ui.device-type-text';
import { RoomTypeText } from '@manyun/resource-hub.ui.room-type-text';

import styles from '../header/header.module.less';

export type TimeSpan = [number, number];

const { statistics: statisticsMap } = getAlarmsMonitoringBoardLocales();

export function StatisInfoList() {
  const statistics = useSelector(selectStatistics());

  const { variants, visible } = useSelector(selectStatisticsPreferences);

  const values = useMemo(() => {
    const arr: StatisticsVariant[] = [];
    const statistics = Object.keys(variants) as StatisticsVariant[];
    statistics.forEach(item => {
      if (variants[item].visible) {
        arr.push(item);
      }
    });
    return arr;
  }, [variants]);

  const getText = (value: string, type: StatisticsVariant) => {
    switch (type) {
      case 'byAlarmLevels':
        return <AlarmLevelText code={value} />;
      case 'byDeviceTypes':
        return <DeviceTypeText code={value} />;
      case 'byRoomTypes':
        return <RoomTypeText code={value} />;
      default:
        return '';
    }
  };

  return (
    <>
      {visible && (
        <Carousel autoplay dotPosition="left" dots={{ className: styles.cursomDots }}>
          {values.map(item => {
            const title = statisticsMap[item];
            const statisticItem = statistics[item];

            return (
              <div key={item}>
                <Row align="middle" className={styles.header} style={{ paddingLeft: '8px' }}>
                  <Space size={48} style={{ lineHeight: '0px' }}>
                    <Typography.Text
                      style={{ marginLeft: '24px', display: 'block', width: '160px' }}
                    >
                      {title}
                    </Typography.Text>

                    {Object.keys(statisticItem).map(optionItem => {
                      return (
                        <Space key={optionItem} style={{ whiteSpace: 'nowrap' }}>
                          <Badge
                            status="processing"
                            text={<>{getText(optionItem, item)}:</>}
                            style={{ lineHeight: 'normal' }}
                          />
                          <Typography.Text>
                            {statisticItem[optionItem]}
                            {statisticItem[optionItem] && '个'}
                          </Typography.Text>
                        </Space>
                      );
                    })}
                  </Space>
                </Row>
              </div>
            );
          })}
        </Carousel>
      )}
    </>
  );
}
