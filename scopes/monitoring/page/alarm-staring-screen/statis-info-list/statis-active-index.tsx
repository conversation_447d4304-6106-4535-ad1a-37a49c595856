import React from 'react';

import classNames from 'classnames';

import { Space } from '@manyun/base-ui.ui.space';

import styles from '../header/header.module.less';

export type StatisActiveIndexProps = {
  activeIndex: number;
};

export function StatisActiveIndex({ activeIndex }: StatisActiveIndexProps) {
  return (
    <Space direction="vertical" size={4}>
      {[0, 1, 2, 3].map(item => {
        return (
          <div
            key={item}
            className={classNames(
              styles.statisIndex,
              item === activeIndex && styles.statisActiveIndexColor
            )}
          ></div>
        );
      })}
    </Space>
  );
}
