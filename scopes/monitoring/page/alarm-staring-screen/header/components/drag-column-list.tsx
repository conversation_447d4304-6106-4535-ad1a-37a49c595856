import React, { useMemo } from 'react';
import { DragDropContext, Draggable, Droppable } from 'react-beautiful-dnd';
import { useDispatch, useSelector } from 'react-redux';

import { HolderOutlined } from '@ant-design/icons';

import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Row } from '@manyun/base-ui.ui.grid';
import { Typography } from '@manyun/base-ui.ui.typography';

import {
  selectBoardPreferences,
  selectPreferences,
  updateSpecificBoardPreferences,
} from '@manyun/monitoring.state.alarms-monitoring-board';

import { GetTitle } from '../../components/get-title.js';
import styles from '../header.module.less';

export function DragColumnList() {
  const dispatch = useDispatch();

  const { layout } = useSelector(selectPreferences);
  // @ts-expect-error ts(2769)
  const boardPreferences = useSelector(selectBoardPreferences(layout));
  const { boardColumns } = boardPreferences;

  const onChange = (e: { target: { checked: boolean; value: string } }) => {
    const {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      target: { checked, value },
    } = e;

    dispatch(
      updateSpecificBoardPreferences({
        layout,
        preferences: {
          [value]: {
            layout: checked ? 'card' : 'mini',
          },
        },
      })
    );
  };

  const checkedValues = useMemo(() => {
    const arr: string[] = [];
    boardColumns.forEach(item => {
      if (boardPreferences[item].layout === 'card') {
        arr.push(item);
      }
    });
    return arr;
  }, [boardColumns, boardPreferences]);

  const onDragEnd = (result: any) => {
    const { source, destination } = result;

    if (!destination) {
      return;
    }
    const list = [...boardColumns];

    const destinationIndex = destination.index ?? 0;
    const sourceIndex = source.index ?? 0;

    [list[destinationIndex], list[sourceIndex]] = [list[sourceIndex], list[destinationIndex]];

    dispatch(
      updateSpecificBoardPreferences({
        layout,
        preferences: {
          boardColumns: list,
        },
      })
    );
  };

  return (
    <>
      <Typography.Text>设置默认展开列</Typography.Text>
      <DragDropContext onDragEnd={onDragEnd}>
        <Droppable droppableId="drag-alarm-line">
          {(provided: any) => (
            <Checkbox.Group
              className={styles.checkbox}
              value={checkedValues}
              ref={provided.innerRef}
              {...provided.droppableProps}
            >
              {boardColumns.map((item, index) => {
                return (
                  <Draggable draggableId={item.toString()} index={index} key={item}>
                    {(provided: any) => (
                      <Row
                        justify="space-between"
                        align="middle"
                        className={styles.checkboxItem}
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        {...provided.dragHandleProps}
                      >
                        <Checkbox onChange={onChange} value={item}>
                          <GetTitle code={item} />
                        </Checkbox>
                        <HolderOutlined className={styles.checkboxItemIcon} />
                      </Row>
                    )}
                  </Draggable>
                );
              })}
              {provided.placeholder}
            </Checkbox.Group>
          )}
        </Droppable>
      </DragDropContext>
    </>
  );
}
