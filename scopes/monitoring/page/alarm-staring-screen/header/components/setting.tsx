import React, { useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { SettingOutlined } from '@ant-design/icons';

import { Card } from '@manyun/base-ui.ui.card';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Dropdown } from '@manyun/base-ui.ui.dropdown';
import { Row } from '@manyun/base-ui.ui.grid';
import { Space } from '@manyun/base-ui.ui.space';
import { Switch } from '@manyun/base-ui.ui.switch';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';

import {
  getAlarmsMonitoringBoardLocales,
  selectPreferences,
  selectStatisticsPreferences,
  updateStatisticsPreferences,
} from '@manyun/monitoring.state.alarms-monitoring-board';
import type { StatisticsVariant } from '@manyun/monitoring.state.alarms-monitoring-board';

import { DragColumnList } from './drag-column-list.js';

export function Setting() {
  const [open, setOpen] = useState(false);

  return (
    <Dropdown
      dropdownRender={() => <SettingContent />}
      open={open}
      trigger={['click']}
      placement="bottomRight"
      arrow={{ pointAtCenter: true }}
      onOpenChange={open => setOpen(open)}
    >
      <Tooltip title="设置">
        <SettingOutlined />
      </Tooltip>
    </Dropdown>
  );
}

const { statistics } = getAlarmsMonitoringBoardLocales();
function SettingContent() {
  const dispatch = useDispatch();
  const { layout } = useSelector(selectPreferences);

  const statisticsPreferences = useSelector(selectStatisticsPreferences);

  const { visible, variants } = statisticsPreferences;

  const onSwitchChange = (checked: boolean) => {
    dispatch(
      updateStatisticsPreferences({
        variant: 'root',
        visible: !visible,
      })
    );
  };

  const onChange = (e: { target: { checked: boolean; value: StatisticsVariant } }) => {
    const {
      target: { checked, value },
    } = e;
    dispatch(
      updateStatisticsPreferences({
        variant: value,
        visible: checked,
      })
    );
  };

  const values = useMemo(() => {
    const arr: StatisticsVariant[] = [];
    Object.keys(variants).forEach((item: StatisticsVariant) => {
      if (variants[item].visible) {
        arr.push(item);
      }
    });
    return arr;
  }, [variants]);

  return (
    <Card style={{ width: 250 }} title="设置" size="small">
      <Space direction="vertical" size={13} style={{ width: '100%' }}>
        <Row justify="space-between">
          统计信息展示
          <Switch checked={visible} onChange={onSwitchChange} />
        </Row>
        <Checkbox.Group value={values}>
          <Space direction="vertical" style={{ width: '100%' }}>
            {Object.keys(statistics).map(item => {
              return (
                <Checkbox key={item} value={item} onChange={onChange}>
                  {statistics[item]}
                </Checkbox>
              );
            })}
          </Space>
        </Checkbox.Group>

        {layout !== 'table' && <DragColumnList />}
      </Space>
    </Card>
  );
}
