import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { BulletinBoardOutlined } from '@manyun/base-ui.icons';
import { Dropdown } from '@manyun/base-ui.ui.dropdown';
import { Menu } from '@manyun/base-ui.ui.menu';
import type { MenuProps } from '@manyun/base-ui.ui.menu';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';

import type { LayoutVariant } from '@manyun/monitoring.state.alarms-monitoring-board';
import {
  selectPreferences,
  updatePreferences,
} from '@manyun/monitoring.state.alarms-monitoring-board';

const items = [
  { label: '告警状态看板', key: 'board--by-states' },
  { label: '专业类型看板', key: 'board--by-scopes' },
  { label: '告警等级看板', key: 'board--by-alarm-levels' },
  { label: '常规列表', key: 'table' },
];

export function BoardStatus() {
  const dispatch = useDispatch();

  const { layout, autoRefresh, ttsService } = useSelector(selectPreferences);

  const onSelect: MenuProps['onClick'] = ({ key }) => {
    dispatch(
      updatePreferences({
        layout: key as LayoutVariant,
        autoRefresh,
        ttsService,
      })
    );
  };
  return (
    <Dropdown
      dropdownRender={() => <Menu items={items} selectedKeys={[layout]} onClick={onSelect} />}
      placement="bottomRight"
      trigger={['click']}
      arrow={{ pointAtCenter: true }}
    >
      <Tooltip title="看板类型">
        <span>
          <BulletinBoardOutlined />
        </span>
      </Tooltip>
    </Dropdown>
  );
}
