import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useShallowCompareEffect } from 'react-use';

import {
  EnvironmentOutlined,
  FullscreenExitOutlined,
  FullscreenOutlined,
  SoundOutlined,
} from '@ant-design/icons';
import classNames from 'classnames';

import {
  AutoRefreshOffOutlined,
  AutoRefreshOnOutlined,
  MuteOnOutlined,
} from '@manyun/base-ui.icons';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';

import {
  createBoardAction,
  resetBoardAction,
  selectPreferences,
  updatePreferences,
} from '@manyun/monitoring.state.alarms-monitoring-board';
import { AdvancedAlarmsFilterForm } from '@manyun/monitoring.ui.alarms-filter-form';
import type { FilterFormParams } from '@manyun/monitoring.ui.alarms-filter-form';
import { useSpaces } from '@manyun/resource-hub.gql.client.spaces';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';
import { getSpaceGuid, getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

import { BoardStatus } from './components/board-status.js';
import { Setting } from './components/setting.js';
import styles from './header.module.less';

export type HeaderProps = {
  idc: string;
  defaultBlockGuids?: string[];
  defaultRoomGuids?: string[];
  fullScreen: boolean;
  onSetFullScreen: (visible: boolean) => void;
};

export function Header({
  idc,
  defaultBlockGuids,
  defaultRoomGuids,
  fullScreen,
  onSetFullScreen,
}: HeaderProps) {
  const dispatch = useDispatch();

  const { layout, autoRefresh, ttsService } = useSelector(selectPreferences);

  const [blockGuids, setBlockGuids] = useState<string[]>();

  const [formParams, setFormParams] = useState<Record<string, string[] | undefined>>({
    blockGuids: defaultBlockGuids,
    roomGuids: defaultRoomGuids,
  });

  const { data } = useSpaces({
    variables: {
      nodeTypes: ['BLOCK'],
      authorizedOnly: true,
      idc,
    },
    fetchPolicy: 'network-only',
  });

  const allBlockGuids = data?.spaces?.map(item => item.value) ?? [];

  useShallowCompareEffect(
    function () {
      if (defaultBlockGuids) {
        setBlockGuids(defaultBlockGuids);
        return;
      }
      setBlockGuids(allBlockGuids);
    },
    [allBlockGuids, defaultBlockGuids]
  );

  const onSearchBlock = async (value: string[]) => {
    setBlockGuids(value);
    onSearchAlarm({
      ...formParams,
      blockGuids: formParams?.blockGuids ? formParams.blockGuids : value,
    });
  };

  const onFilterFormChange = (params: FilterFormParams) => {
    const {
      notifyContent,
      alarmTypes,
      alarmLevels,
      alarmStates,
      alarmLifecycleStates,
      roomGuids,
      roomTypes,
      deviceTypes,
      lastModifyUserId,
      alarmsTriggeredAtTimeRange,
      alarmsRecoveredAtTimeRange,
    } = params;

    const popoverBlockGuids = Array.isArray(roomGuids)
      ? Array.from(
          new Set(
            roomGuids.map((item: string) => {
              const { idc, block } = getSpaceGuidMap(item);
              return getSpaceGuid(idc!, block);
            })
          )
        ).filter((item): item is string => item !== null)
      : undefined;

    const filterParams = {
      alarmContent: notifyContent,
      alarmTypes: alarmTypes ? [alarmTypes] : undefined,
      alarmLevels: alarmLevels ? [alarmLevels] : undefined,
      alarmStates,
      alarmLifecycleStates,
      roomGuids: roomGuids?.filter((item: string) => getSpaceGuidMap(item).room),
      roomTypes: roomTypes && roomTypes.length ? roomTypes : undefined,
      deviceTypes: deviceTypes && deviceTypes.length ? deviceTypes : undefined,
      lastModifyUserId,
      alarmsTriggeredAtTimeRange,
      alarmsRecoveredAtTimeRange,
    };
    setFormParams(filterParams);

    onSearchAlarm({ blockGuids: popoverBlockGuids ?? blockGuids, ...filterParams });
  };

  const onSearchAlarm = (params: Record<string, unknown>) => {
    dispatch(resetBoardAction());
    dispatch(
      createBoardAction({
        filters: {
          idc,
          ...params,
        },
      })
    );
  };

  const onSound = (ttsServiceStatus: boolean) => {
    dispatch(
      updatePreferences({
        layout,
        ttsService: ttsServiceStatus,
        autoRefresh,
      })
    );
  };

  const onAutoRefresh = (autoRefreshStatus: boolean) => {
    dispatch(
      updatePreferences({
        layout,
        ttsService,
        autoRefresh: autoRefreshStatus,
      })
    );
  };
  return (
    <div className={styles.header}>
      <Space>
        当前浏览
        <EnvironmentOutlined />
        <LocationCascader
          style={{ width: 200 }}
          maxTagCount="responsive"
          allowClear={false}
          size="small"
          multiple
          showSearch
          authorizedOnly
          nodeTypes={['BLOCK']}
          idc={idc}
          value={blockGuids?.map(item => {
            return [item];
          })}
          onChange={(_, __, value) => {
            if (Array.isArray(value)) {
              onSearchBlock(value as string[]);
            }
          }}
        />
      </Space>
      <Space size="large">
        <AdvancedAlarmsFilterForm
          idc={idc}
          inputProps={{ style: { width: 200 }, size: 'small', placeholder: '搜索告警内容' }}
          defaultValue={{ roomGuids: defaultRoomGuids }}
          onChange={onFilterFormChange}
        />

        <Tooltip title="语音播报">
          {ttsService ? (
            <SoundOutlined onClick={() => onSound(false)} />
          ) : (
            <span onClick={() => onSound(true)}>
              <MuteOnOutlined className={classNames(styles.errorColor, styles.pointer)} />
            </span>
          )}
        </Tooltip>
        <Tooltip title="自动刷新">
          {autoRefresh ? (
            <span onClick={() => onAutoRefresh(false)}>
              <AutoRefreshOnOutlined className={styles.pointer} />
            </span>
          ) : (
            <span onClick={() => onAutoRefresh(true)}>
              <AutoRefreshOffOutlined className={classNames(styles.errorColor, styles.pointer)} />
            </span>
          )}
        </Tooltip>
        <Typography.Text disabled>|</Typography.Text>
        <BoardStatus />
        <Setting />
        <Tooltip title="全屏">
          {fullScreen ? (
            <FullscreenExitOutlined onClick={() => onSetFullScreen(false)} />
          ) : (
            <FullscreenOutlined onClick={() => onSetFullScreen(true)} />
          )}
        </Tooltip>
      </Space>
    </div>
  );
}
