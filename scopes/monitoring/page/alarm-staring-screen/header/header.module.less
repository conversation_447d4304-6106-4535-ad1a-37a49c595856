@import (reference) '@manyun/base-ui.theme.theme/dist/theme.less';

.header {
  width: 100%;
  height: 38px;
  padding: 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  overflow-x: auto;
  background-color: @background-color-light;
}

.checkbox {
  width: 100%;
  .checkboxItem {
    cursor: pointer;
    margin-top: 8px;
    .checkboxItemIcon {
      visibility: hidden;
    }
    &:hover {
      background-color: @primary-1;
    }
    &:hover .checkboxItemIcon {
      visibility: inherit;
    }
  }
}
.statisIndex {
  width: 4px;
  height: 4px;
  border-radius: 100%;
  background-color: @layout-body-background;
}
.statisActiveIndexColor {
  background-color: @select-multiple-item-disabled-color;
}
.errorColor {
  color: @error-color;
}
.pointer {
  cursor: pointer;
}
.fullScreenStyle {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  background-color: @background-color-light;
}
//轮播图面板指示点不添加自定义样式指示点实现不了
.cursomDots {
  line-height: 0;
  top: 45% !important;
  button {
    width: 4px !important;
    height: 4px !important;
    border-radius: 100%;
    opacity: 1 !important;
    background-color: #f0f0f0 !important;
  }

  :global(.slick-active) {
    button {
      width: 4px !important;
      height: 4px !important;
      border-radius: 100%;
      background-color: @select-multiple-item-disabled-color !important;
    }
  }
  > li {
    height: 0px !important;
  }
}
