import React, { CSSProperties, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { StarFilled, StarOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import get from 'lodash.get';

import { ArrowRightOutlined, ConnectOutlined } from '@manyun/base-ui.icons';
import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { Divider } from '@manyun/base-ui.ui.divider';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';

import { Alarm } from '@manyun/monitoring.model.alarm';
import type { AlarmJSON } from '@manyun/monitoring.model.alarm';
import {
  acceptAlarmsAction,
  addAlarmsIntoInterestsAction,
  removeAlarmsFromInterestsAction,
  selectYourInterestsAlarmsIds,
} from '@manyun/monitoring.state.alarms-monitoring-board';

import { AlarmRemoveConfirmModal } from '../../components/alarm-remove-confirm-modal.js';
import AssociatedTicket from './associated-ticket.js';
import { CreateEvent } from './create-event.js';

export type AlarmOperationProps = {
  style?: CSSProperties;
  idc: string;
  alarm: AlarmJSON;
  mode?: 'icon' | 'button' | 'link';
  showInterestBtn?: boolean;
  showCreateBtn?: boolean;
  showDetailBtnFn?: (arg: AlarmJSON) => void;
  onRemoveCallBack?: () => void;
};

export function AlarmOperation({
  style,
  idc,
  alarm,
  mode = 'link',
  showInterestBtn = false,
  showCreateBtn = false,
  showDetailBtnFn,
  onRemoveCallBack,
}: AlarmOperationProps) {
  const dispatch = useDispatch();

  const [associatedVisible, setAssociatedVisible] = useState<boolean>(false);
  const [associatedSelectedRowKeys, setAssociatedSelectedRowKeys] = useState<number[]>([]);
  const [associatedSelectedRows, setAssociatedSelectedRows] = useState<AlarmJSON | []>();

  const interestsAlarmsIds = useSelector(selectYourInterestsAlarmsIds);

  //受理
  const onAcceptAlarm = (e: { stopPropagation: () => void }) => {
    e.stopPropagation();
    dispatch(
      acceptAlarmsAction({
        alarmIds: [alarm.id],
        callback: error => {
          if (!error) {
            message.success('受理成功');
          } else {
            message.error(error.message);
          }
        },
      })
    );
  };

  //关注
  const onIntoInterest = (e: { stopPropagation: () => void }) => {
    e.stopPropagation();
    if (interestsAlarmsIds.includes(alarm.id)) {
      dispatch(
        removeAlarmsFromInterestsAction({
          alarmIds: [alarm.id],
          callback: error => {
            if (!error) {
              message.success('取消关注成功');
            } else {
              message.error(error.message);
            }
          },
        })
      );
    } else {
      dispatch(
        addAlarmsIntoInterestsAction({
          alarmIds: [alarm.id],
          callback: error => {
            if (!error) {
              message.success('关注成功');
            } else {
              message.error(error.message);
            }
          },
        })
      );
    }
  };

  const onAssociated = (e: {
    stopPropagation: () => void;
    nativeEvent: { stopImmediatePropagation: () => void };
  }) => {
    e.stopPropagation();
    e.nativeEvent.stopImmediatePropagation();
    setAssociatedVisible(true);
    setAssociatedSelectedRowKeys([alarm.id]);
    setAssociatedSelectedRows(alarm);
  };

  const onAssociatedCancel = () => {
    setAssociatedVisible(false);
    setAssociatedSelectedRowKeys([]);
    setAssociatedSelectedRows([]);
  };

  const resetSelectedRowKeys = () => {
    setAssociatedVisible(false);
    setAssociatedSelectedRowKeys([]);
    setAssociatedSelectedRows([]);
  };

  const status = Alarm.buildSyntheticState(alarm.lifecycleState.code, alarm.state.code);

  const isLink: boolean = mode === 'link';

  const spaceSize: number = mode === 'link' ? 0 : mode === 'icon' ? 4 : 16;

  const getInterestBtns = () => {
    if (!showInterestBtn) {
      return;
    }
    if (mode === 'icon') {
      return interestsAlarmsIds.includes(alarm.id) ? (
        <Tooltip title="关注">
          <StarFilled
            style={{ color: `var(--${prefixCls}-primary-color)`, fontSize: 16 }}
            onClick={onIntoInterest}
          />
        </Tooltip>
      ) : (
        <Tooltip title="关注">
          <StarOutlined
            style={{ color: `var(--${prefixCls}-primary-color)`, fontSize: 16 }}
            onClick={onIntoInterest}
          />
        </Tooltip>
      );
    }
    if (mode === 'button' || mode === 'link') {
      return (
        <Button
          type={mode === 'button' ? 'default' : 'link'}
          compact={mode === 'link'}
          onClick={onIntoInterest}
        >
          {interestsAlarmsIds.includes(alarm.id) ? '取消关注' : '关注'}
        </Button>
      );
    }

    return;
  };

  const getAcceptBtns = ({ isMainBtn }: { isMainBtn?: boolean }) => {
    if (mode === 'icon') {
      return (
        <Tooltip title="受理">
          <ArrowRightOutlined
            style={{ color: `var(--${prefixCls}-primary-color)`, fontSize: 16 }}
            disabled={alarm.lifecycleState.code !== 'ACTIVE'}
            onClick={onAcceptAlarm}
          />
        </Tooltip>
      );
    }

    const type = mode === 'button' ? (isMainBtn ? 'primary' : 'default') : 'link';
    if (mode === 'button' || mode === 'link') {
      return (
        <Button
          type={type}
          compact={mode === 'link'}
          disabled={alarm.lifecycleState.code !== 'ACTIVE'}
          onClick={onAcceptAlarm}
        >
          受理
        </Button>
      );
    }
    return null;
  };

  const getAssociateBtns = ({ isMainBtn }: { isMainBtn?: boolean }) => {
    if (mode === 'icon') {
      return (
        <Tooltip title="关联">
          <ConnectOutlined
            style={{ color: `var(--${prefixCls}-primary-color)`, fontSize: 16 }}
            disabled={alarm.lifecycleState.code === 'PROCESS'}
            onClick={e => onAssociated(e)}
          />
        </Tooltip>
      );
    }
    const type = mode === 'button' ? (isMainBtn ? 'primary' : 'default') : 'link';

    if (mode === 'button' || mode === 'link') {
      return (
        <Button
          type={type}
          compact={mode === 'link'}
          disabled={alarm.lifecycleState.code === 'PROCESS'}
          onClick={e => onAssociated(e)}
        >
          关联
        </Button>
      );
    }
    return null;
  };

  const getDetailBtn = () => {
    if (showDetailBtnFn) {
      return (
        <Button
          type={mode === 'button' ? 'default' : 'link'}
          compact={isLink}
          onClick={() => {
            showDetailBtnFn(alarm);
          }}
        >
          详情
        </Button>
      );
    }
    return null;
  };

  if (status === 'unaccepted-n-unrecovered') {
    return (
      <Space style={style} split={isLink && <Divider type="vertical" />} size={spaceSize}>
        {getInterestBtns()}
        {getAcceptBtns({})}
        {getDetailBtn()}
      </Space>
    );
  } else if (status === 'accepted-n-unrecovered') {
    return (
      <Space style={style} split={isLink && <Divider type="vertical" />} size={spaceSize}>
        {getInterestBtns()}

        {showCreateBtn && (
          <CreateEvent
            idcTag={idc}
            disabled={alarm.lifecycleState.code === 'PROCESS'}
            blockTag={alarm.source.spaceGuidMap.blockTag}
            alarmIds={[alarm.id]}
            activeTime={alarm.activatedAt ?? dayjs().valueOf()}
            confirm={() => {
              return {
                confirmBy: get(alarm, 'alarm.confirmUser.id', null),
                confirmByName: get(alarm, 'alarm.confirmUser.name', null),
              };
            }}
            type={mode === 'button' ? 'default' : 'link'}
            selectedRows={[alarm]}
          />
        )}
        {getAssociateBtns({ isMainBtn: true })}
        {associatedVisible && (
          <AssociatedTicket
            alarmIds={associatedSelectedRowKeys}
            text="关联"
            selectedRows={associatedSelectedRows}
            idc={idc}
            blockTag={alarm.source.spaceGuidMap.blockTag}
            params={{
              idcTag: idc,
              alarmStatus: alarm.lifecycleState.code,
              pageSize: 10,
              pageNum: 1,
            }}
            resetSelectedRowKeys={resetSelectedRowKeys}
            onCancel={onAssociatedCancel}
          />
        )}
        {getDetailBtn()}
      </Space>
    );
  } else if (status === 'unaccepted-n-recovered') {
    return (
      <Space style={style} split={isLink && <Divider type="vertical" />} size={spaceSize}>
        {getInterestBtns()}
        {getAcceptBtns({ isMainBtn: true })}
        {getDetailBtn()}
      </Space>
    );
  } else if (status === 'accepted-n-recovered') {
    const statusCode = alarm.lifecycleState.code;
    return (
      <Space style={style} split={isLink && <Divider type="vertical" />} size={spaceSize}>
        {getInterestBtns()}

        {showCreateBtn && (
          <CreateEvent
            idcTag={idc}
            disabled={alarm.lifecycleState.code === 'PROCESS'}
            blockTag={alarm.source.spaceGuidMap.blockTag}
            alarmIds={[alarm.id]}
            activeTime={alarm.activatedAt ?? null}
            confirm={() => {
              return {
                confirmBy: get(alarm, 'alarm.confirmUser.id', null),
                confirmByName: get(alarm, 'alarm.confirmUser.name', null),
              };
            }}
            type={mode === 'button' ? 'default' : 'link'}
            selectedRows={[alarm]}
          />
        )}
        {getAssociateBtns({})}
        {associatedVisible && (
          <AssociatedTicket
            alarmIds={associatedSelectedRowKeys}
            text="关联"
            selectedRows={associatedSelectedRows}
            idc={idc}
            blockTag={alarm.source.spaceGuidMap.blockTag}
            params={{
              idcTag: idc,
              alarmStatus: alarm.lifecycleState.code,
              pageSize: 10,
              pageNum: 1,
            }}
            resetSelectedRowKeys={resetSelectedRowKeys}
            onCancel={onAssociatedCancel}
          />
        )}
        <AlarmRemoveConfirmModal
          mode={mode}
          disabled={!['CONFIRMED', 'PROCESS'].includes(statusCode)}
          buttonType={mode === 'button' ? 'primary' : 'link'}
          record={alarm}
          idc={idc}
          blockTag={alarm.source.spaceGuidMap.blockTag}
          alarmStatus={statusCode}
          params={{
            idcTag: idc,
            alarmStatus: statusCode,
            pageSize: 10,
            pageNum: 1,
          }}
          onRemoveCallBack={onRemoveCallBack}
        />
        {getDetailBtn()}
      </Space>
    );
  } else {
    return <></>;
  }
}
