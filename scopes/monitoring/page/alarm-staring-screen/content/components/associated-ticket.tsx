import React, { Component } from 'react';
import { connect } from 'react-redux';

import { FiltersForm } from '@galiojs/awesome-antd';
import trim from 'lodash/trim';

import { Cascader } from '@manyun/base-ui.ui.cascader';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';

import { connectAlarmsWithEventAction } from '@manyun/monitoring.state.alarms-monitoring-board';
import { generateEventDetailRoutePath } from '@manyun/ticket.route.ticket-routes';
import { EventLevelText } from '@manyun/ticket.ui.event-level-text';

import { Ellipsis, GutterWrapper, TinyTable } from '@manyun/dc-brain.legacy.components';
// import { alarmAssociateEvent } from '@manyun/dc-brain.legacy.services/alarmScreenService';
import { METADATA_TYPE } from '@manyun/dc-brain.legacy.constants';
import { fetchAlarmScreenList } from '@manyun/dc-brain.legacy.redux/actions/alarmScreenActions';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import { fetchAssociatePage } from '@manyun/dc-brain.legacy.services/eventCenterService';
import { getEventTypeName } from '@manyun/dc-brain.legacy.utils';

const columns = ctx => [
  {
    title: '事件ID',
    dataIndex: 'eventNo',
    dataType: {
      type: 'link',
      options: {
        to(text) {
          return { pathname: generateEventDetailRoutePath({ id: text }) };
        },
      },
    },
  },
  {
    title: '机房',
    dataIndex: 'idcTag',
  },
  {
    title: '事件级别',
    dataIndex: 'eventLevel',
    render: text => <EventLevelText code={text} />,
  },
  {
    title: '事件类型',
    dataIndex: 'topCategory',
    render: text => (
      <span>{getEventTypeName(ctx.props.eventTypes, text, METADATA_TYPE.EVENT_TOP_CATEGORY)}</span>
    ),
  },
  {
    title: '事件子类型',
    dataIndex: 'secondCategory',
    render: text => (
      <span>
        {getEventTypeName(ctx.props.eventTypes, text, METADATA_TYPE.EVENT_SECOND_CATEGORY)}
      </span>
    ),
  },
  {
    title: '事件描述',
    dataIndex: 'eventDesc',
    width: 240,
    render(title) {
      return (
        <Ellipsis lines={1} tooltip>
          {title}
        </Ellipsis>
      );
    },
  },
  {
    title: '事件状态',
    dataIndex: ['eventStatus', 'desc'],
  },
];

class AssociatedTicket extends Component {
  state = {
    selectedRowKeys: [],
    pageSize: 10,
    eventList: [],
    total: 0,
    fieldsValue: {},
  };

  componentDidMount() {
    this.fetchAssociatePage(1, 10);
    this.props.syncCommonData({ strategy: { eventTypes: 'IF_NULL' } });
  }

  fetchAssociatePage = async (pageNum, pageSize) => {
    const { idc, blockTag } = this.props;
    const { fieldsValue } = this.state;
    const { response, error } = await fetchAssociatePage({
      pageNum,
      pageSize,
      idcTags: [idc],
      blockTags: [`${idc}.${blockTag}`],
      eventId: trim(fieldsValue?.eventId),
      key: trim(fieldsValue.key),
      topCategorys: fieldsValue?.incidentType?.[0] && [fieldsValue.incidentType[0]],
      secondCategorys: fieldsValue?.incidentType?.[1] && [fieldsValue.incidentType[1]],
    });
    if (response) {
      this.setState({
        pageNum,
        pageSize,
        eventList: response.data,
        total: response.total,
      });
    } else {
      message.error(error || '请求事件列表失败');
    }
  };
  handleOk = () => {
    this.props.onCreateTicketConfirm(this.state.selectedRowKeys);
  };

  handleCancel = () => {
    this.props.onCreateTicketVisible();
  };

  onSelectChange = selectedRowKeys => {
    this.setState({ selectedRowKeys });
  };

  onOk = async () => {
    this.props.connectAlarmsWithEvent({
      alarmIds: this.props.alarmIds,
      eventId: this.state.selectedRowKeys[0],
      idc: this.props.idc,
      alarmStartTimes: Array.isArray(this.props.selectedRows)
        ? this.props.selectedRows.map(item => item.activatedAt)
        : [this.props.selectedRows.activatedAt],
      callback: error => {
        if (!error) {
          message.success('关联成功');
          this.props.onCancel();
        } else {
          message.error(error.message);
        }
      },
    });
    // const { response, error } = await alarmAssociateEvent({
    //   eventId: this.state.selectedRowKeys[0],
    //   alarmIds: this.props.alarmIds,
    //   idcTag: this.props.idc,
    //   // causeDevices: this.props.selectedRows.map(item => item.deviceGuid),
    // });
    // if (response) {
    //   message.success('关联成功');
    //   //this.props.getStatusCount();
    //   this.props.onCancel();
    //   // this.props.fetchAlarmScreenList(this.props.params);
    //   // this.props.resetSelectedRowKeys && this.props.resetSelectedRowKeys();
    //   this.setState({
    //     selectedRowKeys: [],
    //     pageNum: 1,
    //     pageSize: 10,
    //     fieldsValue: {},
    //     eventList: [],
    //     total: 0,
    //   });
    //   return true;
    // } else {
    //   message.error(error || '关联失败');
    // }
  };

  searchEventList = async fieldsValue => {
    await this.setState({ fieldsValue });
    this.fetchAssociatePage(1, 10);
  };

  reset = () => {
    this.fetchAssociatePage(1, 10);
  };

  onChangePage = (pageNum, pageSize) => {
    this.fetchAssociatePage(pageNum, pageSize);
  };

  onButtonClick = () => {
    let otherBlock = false;
    const { selectedRows } = this.props;
    const list = selectedRows.filter(item => item.alarmStatus.code === 'PROCESS');
    if (list.length) {
      message.error('勾选数据中存在已建单数据，请确认。');
      return false;
    }
    selectedRows.forEach(i => {
      if (selectedRows.filter(j => j.blockTag !== i.blockTag).length) {
        otherBlock = true;
      }
    });
    if (otherBlock) {
      message.error('只可选择相同楼栋的告警关联事件');
      return false;
    }
    return true;
  };

  render() {
    const { text, style = {}, treeList } = this.props;
    const { selectedRowKeys, selectedRows, total, pageNum, pageSize, eventList } = this.state;

    return (
      <span
        onClick={event => {
          event.stopPropagation();
          event.nativeEvent.stopImmediatePropagation();
        }}
      >
        <Modal
          // text={text}
          title={text}
          open
          okText="关联"
          style={{ minWidth: 1100, maxWidth: 1200, width: '75%', ...style }}
          // onVisibleChanged={this.onVisibleChanged}
          // disabled={disabled}
          // style={style}
          // type={type}
          // onButtonClick={event => {
          //   this.onButtonClick();
          //   event.stopPropagation();
          //   event.nativeEvent.stopImmediatePropagation();
          // }}
          okButtonProps={{ disabled: !selectedRowKeys.length }}
          onCancel={event => {
            this.props.onCancel();
            event.stopPropagation();
            event.nativeEvent.stopImmediatePropagation();
          }}
          onOk={this.onOk}
        >
          <GutterWrapper>
            <FiltersForm
              items={[
                {
                  label: '事件类型',
                  id: 'incidentType',
                  control: (
                    <Cascader
                      placeholder=""
                      changeOnSelect
                      options={treeList}
                      fieldNames={{
                        value: 'metaCode',
                        label: 'metaName',
                        children: 'children',
                      }}
                    />
                  ),
                },
                { label: '事件ID', id: 'eventId', control: <Input allowClear /> },
                {
                  label: '事件描述',
                  id: 'key',
                  control: <Input allowClear placeholder="请输入关键字" />,
                },
              ]}
              onSearch={this.searchEventList}
              onReset={this.reset}
            />
            <TinyTable
              rowKey="id"
              dataSource={eventList}
              // loading={eventList.loading}
              columns={columns(this)}
              rowSelection={{
                selectedRowKeys,
                selectedRows,
                onChange: this.onSelectChange,
                type: 'radio',
              }}
              pagination={{
                total: total,
                current: pageNum,
                pageSize: pageSize,
                showSizeChanger: true,
                onChange: this.onChangePage,
              }}
            />
          </GutterWrapper>
        </Modal>
      </span>
    );
  }
}
const mapStateToProps = ({ common: { eventTypes } }) => {
  let treeList = [];
  if (eventTypes) {
    treeList = eventTypes.treeList.map(topCategory => {
      if (topCategory.children.length === 0) {
        return {
          ...topCategory,
          children: null,
        };
      }
      const second = topCategory.children.map(secondCategory => {
        if (secondCategory.children.length === 0) {
          return {
            ...secondCategory,
            children: null,
          };
        }
        return secondCategory;
      });
      return {
        ...topCategory,
        children: second,
      };
    });
  }

  return {
    treeList: treeList,
    eventTypes: eventTypes ? eventTypes.normalizedList : {},
  };
};

const mapDispatchToProps = {
  fetchAssociatePage,
  syncCommonData: syncCommonDataActionCreator,
  fetchAlarmScreenList: fetchAlarmScreenList,
  connectAlarmsWithEvent: connectAlarmsWithEventAction,
};

export default connect(mapStateToProps, mapDispatchToProps)(AssociatedTicket);
