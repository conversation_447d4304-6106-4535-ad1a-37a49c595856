import { EyeInvisibleOutlined, EyeOutlined } from '@ant-design/icons';
import cls from 'classnames';
import List from 'rc-virtual-list';
import React, { useCallback, useEffect, useState } from 'react';
import { Link } from 'react-router-dom';

import { CameraOutlined } from '@manyun/base-ui.icons';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { AlarmJSON } from '@manyun/monitoring.model.alarm';
import { fetchVideoConfig } from '@manyun/monitoring.service.fetch-video-config';
import { updateVideoConfig } from '@manyun/monitoring.service.update-video-config';
import { CameraVideo, CameraVideoModal } from '@manyun/monitoring.ui.camera-video';
import type { Device } from '@manyun/resource-hub.model.device';
import { fetchPageDevices } from '@manyun/resource-hub.service.fetch-page-devices';

import styles from './styles.module.less';

type CameraListProps = {
  className?: string;
  headerClassName?: string;
  alarm: AlarmJSON;
};

export function CameraList(props: CameraListProps) {
  const [simple, setSimple] = useState(true);
  const source = props.alarm.source;
  const spaceGuidMap = source.spaceGuidMap;

  const [devices, setDevices] = useState<Device[]>([]);
  useEffect(() => {
    fetchPageDevices({
      idcTag: spaceGuidMap.idcTag,
      blockTag: spaceGuidMap.blockTag!,
      roomTags: [spaceGuidMap.roomTag!],
      deviceTypeList: ['30221'], // 摄像头设备编号
      pageNum: 1,
      pageSize: 1000,
    }).then(({ error, data }) => {
      if (error) {
        message.error(error.message);
        return;
      }
      setDevices(data.data);
    });
  }, [source.variant, spaceGuidMap.blockTag, spaceGuidMap.idcTag, spaceGuidMap.roomTag]);

  const getVideoConfig = useCallback(() => {
    fetchVideoConfig().then(({ error, data }) => {
      if (error) {
        message.error(error.message);
        return;
      }
      if (data.isSimpleCameraList !== undefined) {
        setSimple(data.isSimpleCameraList === 'true');
      }
    });
  }, []);
  useEffect(() => {
    getVideoConfig();
  }, [getVideoConfig]);
  const handleEyeIconClick = useCallback((simple: boolean) => {
    updateVideoConfig({
      configs: { isSimpleCameraList: simple },
    }).then(({ error }) => {
      if (error) {
        message.error(error.message);
        return;
      }
      setSimple(simple);
    });
  }, []);

  const [currentDevice, setCurrentDevice] = useState<Device | null>(null);
  const [modalVisible, setModalVisible] = useState(false);
  const showModal = useCallback((device: Device) => {
    setCurrentDevice(device);
    setModalVisible(true);
  }, []);

  if (!(spaceGuidMap.idcTag && spaceGuidMap.blockTag && spaceGuidMap.roomTag)) {
    return null;
  }

  return (
    <div className={cls(styles.wrapper, props.className)}>
      <header className={cls(styles.header, props.headerClassName)}>
        视频监控
        <Space size="small">
          {simple ? (
            <Tooltip title="隐藏监控画面">
              <EyeInvisibleOutlined
                className={styles.icon}
                onClick={() => handleEyeIconClick(false)}
              />
            </Tooltip>
          ) : (
            <Tooltip title="显示监控画面">
              <EyeOutlined className={styles.icon} onClick={() => handleEyeIconClick(true)} />
            </Tooltip>
          )}
          <Tooltip title="该包间内的所有摄像监控">
            <Link to="\" target="_blank">
              <CameraOutlined className={styles.icon} />
            </Link>
          </Tooltip>
        </Space>
      </header>
      <section className={styles.content}>
        {simple ? (
          <List data={devices} itemKey="id">
            {device => (
              <Typography.Link
                className={styles.link}
                textColorType="default"
                onClick={() => showModal(device)}
              >
                {device.name}
              </Typography.Link>
            )}
          </List>
        ) : (
          <List data={devices.slice(0, 4)} itemKey="id">
            {device => (
              <div className={styles.playerWrapper} onClick={() => showModal(device)}>
                <CameraVideo guid={device.guid} controls={false} showTitle name={device.name} />
              </div>
            )}
          </List>
        )}
      </section>
      {currentDevice !== null && (
        <CameraVideoModal
          guid={currentDevice.guid}
          title={currentDevice.name}
          visible={modalVisible}
          onCancel={() => setModalVisible(false)}
        />
      )}
    </div>
  );
}
