@import '~@manyun/base-ui.theme.theme/dist/theme.variable.less';

@player-height: 126px;

.wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  border: 1px solid @border-color-split;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: @padding-md @padding-sm;
  font-size: @font-size-lg;
  border-bottom: 1px solid @border-color-split;
}

.content {
  flex: 1;
  overflow: auto;

  .link {
    padding: @padding-md @padding-md 0;
  }

  .playerWrapper {
    height: @player-height;
    padding: @padding-xs @padding-xs 0;
  }
}

.icon {
  color: fade(@black, 65%);
}
