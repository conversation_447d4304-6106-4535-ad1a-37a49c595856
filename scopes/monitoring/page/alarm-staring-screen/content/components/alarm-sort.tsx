import React, { useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { CaretDownOutlined, CaretUpOutlined, SwapOutlined } from '@ant-design/icons';

import { Card } from '@manyun/base-ui.ui.card';
import { Dropdown } from '@manyun/base-ui.ui.dropdown';
import { Row } from '@manyun/base-ui.ui.grid';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';

import {
  resetPreferencesAction,
  selectPreferences,
  updateSpecificBoardPreferences,
} from '@manyun/monitoring.state.alarms-monitoring-board';
import type { SortOrder } from '@manyun/monitoring.state.alarms-monitoring-board';

import styles from '../content.module.less';
import { layoutVariantToHump } from '../utils/index.js';

export type AlarmSortProps = {
  preferenceKey?: string;
  sort: string;
};

export function AlarmSort({ preferenceKey, sort }: AlarmSortProps) {
  const [open, setOpen] = useState(false);

  return (
    <Dropdown
      open={open}
      dropdownRender={() => <SortCard preferenceKey={preferenceKey} sort={sort} />}
      placement="bottomRight"
      trigger={['click']}
      arrow={{ pointAtCenter: true }}
      onOpenChange={open => setOpen(open)}
    >
      <Tooltip title="排序">
        <SwapOutlined rotate={-90} />
      </Tooltip>
    </Dropdown>
  );
}

type SortCardProps = {
  preferenceKey?: string;
  sort: string;
};
type SortItem = {
  title: string;
  code: string;
};
const sortList: SortItem[] = [
  { title: '综合排序', code: 'combined-ranking' },
  { title: '告警等级', code: 'alarm-level' },
  { title: '告警开始时间', code: 'alarm-created-at' },
  { title: '最新告警时间', code: 'latest-triggered-at' },
  { title: '告警恢复时间', code: 'alarm-recovered-at' },
];

const interestsSortList: SortItem[] = [
  { title: '综合排序', code: 'combined-ranking' },
  { title: '告警等级', code: 'alarm-level' },
  { title: '告警开始时间', code: 'alarm-created-at' },
  { title: '最新告警时间', code: 'latest-triggered-at' },
  { title: '告警恢复时间', code: 'alarm-recovered-at' },
  { title: '关注时间', code: 'followed-at' },
];

function SortCard({ sort, preferenceKey }: SortCardProps) {
  const dispatch = useDispatch();
  const { layout } = useSelector(selectPreferences);

  //换成sortType
  const [activeCode, sortOrder]: [string, SortOrder] =
    sort === 'combined-ranking' ? [sort, null] : sort.split('--');

  const onSortClick = (item: SortItem) => {
    const { code } = item;
    let newSortOrder;
    if (code === 'combined-ranking') {
      newSortOrder = '';
    } else if (code === activeCode) {
      newSortOrder = sortOrder === 'ASC' ? 'DESC' : sortOrder === 'DESC' ? '' : 'ASC';
    } else {
      newSortOrder = 'ASC';
    }
    const sort = newSortOrder ? [code, newSortOrder].join('--') : 'combined-ranking';

    dispatch(
      updateSpecificBoardPreferences({
        layout,
        preferences:
          layout === 'table'
            ? { sort }
            : {
                [preferenceKey!]: {
                  sort,
                },
              },
      })
    );
  };

  const TooltipTitle = (code: string) => {
    if (code === 'combined-ranking') {
      return '';
    } else if (code === activeCode) {
      return sortOrder === 'ASC' ? '点击降序' : sortOrder === 'DESC' ? '取消排序' : '点击升序';
    } else {
      return '点击升序';
    }
  };

  const onReset = () => {
    dispatch(
      resetPreferencesAction({
        target: layout === 'table' ? layout : `${layoutVariantToHump(layout)}${preferenceKey}`,
        properties: ['sort'],
      })
    );
  };

  const newSortList = useMemo(
    function () {
      if (preferenceKey === 'your-interests') {
        return interestsSortList;
      }
      return sortList;
    },
    [preferenceKey]
  );

  return (
    <Card
      style={{ width: 151 }}
      size="small"
      title="排序"
      extra={<Typography.Link onClick={onReset}>重置</Typography.Link>}
    >
      <Space size="middle" direction="vertical" style={{ width: '100%' }}>
        {newSortList.map(item => {
          const { code, title } = item;
          return (
            <Tooltip key={code} title={TooltipTitle(code)}>
              <Row justify="space-between" align="middle" onClick={() => onSortClick(item)}>
                {activeCode === code ? (
                  <Typography.Link>{title}</Typography.Link>
                ) : (
                  <Typography.Text className={styles.textHover}>{title}</Typography.Text>
                )}
                {code !== 'combined-ranking' && (
                  <div className={styles.flexboxColumn} style={{ cursor: 'pointer' }}>
                    <CaretUpOutlined
                      className={
                        activeCode === code && sortOrder === 'ASC'
                          ? styles.sortIconActive
                          : styles.sortIconDefault
                      }
                      style={{ height: '12px' }}
                    />
                    <CaretDownOutlined
                      className={
                        activeCode === code && sortOrder === 'DESC'
                          ? styles.sortIconActive
                          : styles.sortIconDefault
                      }
                      style={{ height: '12px' }}
                    />
                  </div>
                )}
              </Row>
            </Tooltip>
          );
        })}
      </Space>
    </Card>
  );
}
