import React from 'react';
import { useLatest } from 'react-use';

import type { AlarmJSON } from '@manyun/monitoring.model.alarm';
import { useLazyDeviceTypeTreeNodeFragment } from '@manyun/resource-hub.gql.client.resources';
import type { Metadata } from '@manyun/resource-hub.gql.client.resources';

export type AlarmTitleProps = {
  alarm: AlarmJSON;
  alarmLevels: Metadata[];
};

export function AlarmTitle({ alarm, alarmLevels }: AlarmTitleProps) {
  const [readDeviceTypeTreeNode] = useLazyDeviceTypeTreeNodeFragment();
  const readDeviceTypeTreeNodeRef = useLatest(readDeviceTypeTreeNode);

  const {
    point: { validLimits },
    pointData: { snapshot },
  } = alarm;

  const status = validLimits && validLimits[Number(snapshot)];

  const alarmLevelMap = alarmLevels.find(item => item.code === alarm.level);
  const alarmLevelText = alarmLevelMap?.name ?? '未知';
  const deviceTypeText = readDeviceTypeTreeNodeRef.current(alarm.source.deviceType)?.name ?? '未知';

  const title =
    alarm.point.dataType.code === 'AI' ? (
      <span style={{ fontSize: 14 }}>
        {alarmLevelText}
        {alarm.source.roomName ? `${alarm.source.roomName}_` : ''}
        {deviceTypeText}
        {alarm.point.name ? `_${alarm.point.name}` : ''}
        {alarm.cause.name ? `_${alarm.cause.name}` : ''}
        {alarm?.pointData.snapshot
          ? `_告警值:${alarm?.pointData.snapshot}${alarm.point.unit || ''}`
          : ''}
      </span>
    ) : (
      <span style={{ fontSize: 14 }}>
        {alarmLevelText}
        {alarm.source.roomName ? `${alarm.source.roomName}_` : ''}
        {deviceTypeText}
        {alarm.point.name ? `_${alarm.point.name}` : ''}
        {`_${status}告警 `}
      </span>
    );

  return <>{title}</>;
}
