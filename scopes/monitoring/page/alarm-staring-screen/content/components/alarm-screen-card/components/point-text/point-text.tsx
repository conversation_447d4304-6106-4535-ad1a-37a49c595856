import React from 'react';

import { PointsLineModalButton } from '@manyun/monitoring.chart.points-line';
import { PointsStateLineModalButton } from '@manyun/monitoring.chart.points-state-line';
import type { AlarmJSON } from '@manyun/monitoring.model.alarm';

export type PointTextProps = {
  idc: string;
  alarm: AlarmJSON;
};

export function PointText({ idc, alarm }: PointTextProps) {
  const point = alarm.point;
  const isAI = point.dataType.code === 'AI';

  const pointGuids = [
    {
      deviceGuid: alarm.source.guid,
      pointCode: point.code,
      unit: point.unit,
    },
  ];
  const seriesOption = [{ name: point.name }];

  const current = alarm.pointData.current;
  const currentText =
    typeof current == 'number'
      ? isAI
        ? `${alarm.pointData.current}${point.unit ?? ''}`
        : alarm.point.validLimits?.[current] ?? '--'
      : '--';

  return (
    <span
      onClick={event => {
        event.stopPropagation();
        event.nativeEvent.stopImmediatePropagation();
      }}
    >
      {isAI ? (
        <PointsLineModalButton
          idcTag={idc}
          btnText={<span style={{ fontSize: 12 }}>{currentText}</span>}
          modalText={point.name}
          pointGuids={pointGuids}
          seriesOption={seriesOption}
          btnTooltipProps={{ title: `现值:${currentText}` }}
        />
      ) : (
        <PointsStateLineModalButton
          idcTag={idc}
          btnText={<span style={{ fontSize: 12 }}>{currentText}</span>}
          modalText={point.name}
          pointGuids={pointGuids}
          seriesOption={seriesOption}
          validLimitsMap={point.validLimits || {}}
          btnTooltipProps={{ title: `现值:${currentText}` }}
        />
      )}
    </span>
  );
}
