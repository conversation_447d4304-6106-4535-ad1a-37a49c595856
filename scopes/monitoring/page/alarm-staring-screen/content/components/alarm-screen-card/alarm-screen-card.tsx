import React, { useCallback, useMemo } from 'react';

import classNames from 'classnames';
import dayjs from 'dayjs';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Badge } from '@manyun/base-ui.ui.badge';
import { Card } from '@manyun/base-ui.ui.card';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';

import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import { Link } from '@manyun/dc-brain.navigation.link';
import type { AlarmJSON } from '@manyun/monitoring.model.alarm';
import { generateSpaceOrDeviceRoutePath } from '@manyun/monitoring.route.monitoring-routes';
import type { CardLayoutColumnConfig } from '@manyun/monitoring.state.alarms-monitoring-board';
import { AlarmDrawer } from '@manyun/monitoring.ui.alarm-drawer';
import { AlarmLabel } from '@manyun/monitoring.ui.alarm-label';
import { AlarmLifecyStatusText } from '@manyun/monitoring.ui.alarm-lifecy-status-text';
import { AlarmStatusText } from '@manyun/monitoring.ui.alarm-status-text';
import type { Metadata } from '@manyun/resource-hub.gql.client.resources';
import { getSpaceGuid } from '@manyun/resource-hub.util.space-guid';

import styles from './alarm-screen-card.less';
import { AlarmTitle } from './components/alarm-title/index.js';
import { PointText } from './components/point-text/index.js';

export type AlarmScreenCardProps = {
  style?: React.CSSProperties;
  alarm: AlarmJSON;
  columns: CardLayoutColumnConfig[];
  alarmLevels: Metadata[];
  Operation: React.FC<{
    style?: React.CSSProperties;
    idc: string;
    alarm: AlarmJSON;
    mode?: 'icon' | 'button' | 'link';
    showInterestBtn?: boolean;
    showCreateBtn?: boolean;
    showDetailBtnFn?: (arg: AlarmJSON) => void;
    onRemoveCallBack?: () => void;
  }>;
  onRowClick: (alarmId: number) => void;
};

export function AlarmScreenCard({
  style,
  alarm,
  columns,
  alarmLevels,
  Operation,
  onRowClick,
}: AlarmScreenCardProps) {
  const idc = alarm.source.spaceGuidMap.idcTag;

  const getCardClassName = useCallback(() => {
    if (
      dayjs(new Date().valueOf()).diff(alarm.createdAt, 'minutes') > 2 &&
      alarm.lifecycleState.code === 'ACTIVE'
    ) {
      return styles.errBoxShadow;
    }

    if (
      dayjs(new Date().valueOf()).diff(alarm.createdAt, 'minutes') > 1 &&
      alarm.lifecycleState.code === 'ACTIVE'
    ) {
      return styles.warnBoxShadow;
    }
    return '';
  }, [alarm.createdAt, alarm.lifecycleState.code]);

  const columnsCodeList = useDeepCompareMemo(() => {
    const columnCodes: string[] = [];
    columns.forEach(item => {
      if (item.visible) {
        columnCodes.push(item.dataIndex);
      }
    });
    return columnCodes;
  }, [columns]);

  const alarmShowColumnList = useMemo(() => {
    const list = [];
    if (env.__DEBUG_MODE__) {
      list.push(
        <Space key="id">
          <span>id</span>
          <span>{alarm.id}</span>
        </Space>
      );
    }
    if (columnsCodeList.includes('createdAt') || columnsCodeList.includes('recoveredAt')) {
      //告警开始时间 告警恢复时间
      const createdAtText = alarm.createdAt ? dayjs(alarm.createdAt).format('MM-DD HH:mm:ss') : '-';
      const recoveredAtText = alarm.recoveredAt
        ? dayjs(alarm.recoveredAt).format('MM-DD HH:mm:ss')
        : '-';
      list.push(
        <Space key="createdAt-recoveredAt" style={{ fontSize: 12 }} split="~">
          {columnsCodeList.includes('createdAt') && (
            <Tooltip title={`告警开始时间: ${createdAtText}`}>
              <Typography.Text style={{ fontSize: 12 }} type="secondary">
                {createdAtText}
              </Typography.Text>
            </Tooltip>
          )}
          {columnsCodeList.includes('recoveredAt') && (
            <Tooltip title={`告警恢复时间: ${recoveredAtText}`}>
              <Typography.Text style={{ fontSize: 12 }} type="secondary">
                {recoveredAtText}
              </Typography.Text>
            </Tooltip>
          )}
        </Space>
      );
    }

    if (columnsCodeList.includes('source.spaceGuidMap')) {
      //位置
      const spaceGuid = getSpaceGuid(
        idc,
        alarm.source.spaceGuidMap.blockTag,
        alarm.source.spaceGuidMap.roomTag
      );

      const sourceSpaceGuidMapText = alarm.source.spaceGuidMap.blockTag
        ? getSpaceGuid(alarm.source.spaceGuidMap.blockTag, alarm.source.spaceGuidMap.roomTag)
        : idc;

      list.push(
        <Tooltip key="source.spaceGuidMap" title={`位置: ${sourceSpaceGuidMapText}`}>
          <span style={{ fontSize: 12 }}>
            <Link
              href={generateSpaceOrDeviceRoutePath({
                guid: spaceGuid!,
                type: alarm.source.spaceGuidMap.roomTag
                  ? 'ROOM'
                  : alarm.source.spaceGuidMap.blockTag
                    ? 'BLOCK'
                    : 'IDC',
                spotlightTarget: alarm.source.dimension === 'DEVICE' ? alarm.source.guid : '',
              })}
              target="_blank"
              onClick={e => e.stopPropagation()}
            >
              {alarm.source.spaceGuidMap.blockTag
                ? getSpaceGuid(
                    alarm.source.spaceGuidMap.blockTag,
                    alarm.source.spaceGuidMap.roomTag
                  )
                : idc}
            </Link>
          </span>
        </Tooltip>
      );
    }
    if (columnsCodeList.includes('source')) {
      //告警对象
      list.push(
        <Tooltip key="source" title={`告警对象: ${alarm.source.name}`}>
          {alarm.source.dimension === 'GRID' || alarm.source.dimension === 'COLUMN' ? (
            <Typography.Text style={{ maxWidth: 200, fontSize: 12 }} type="secondary" ellipsis>
              {alarm.source.name}
            </Typography.Text>
          ) : (
            <Typography.Text style={{ maxWidth: 200, fontSize: 12 }} ellipsis>
              <Link
                href={generateSpaceOrDeviceRoutePath({
                  guid: alarm.source.guid,
                  type: alarm.source.dimension,
                })}
                target="_blank"
                onClick={e => e.stopPropagation()}
              >
                {alarm.source.name}
              </Link>
            </Typography.Text>
          )}
        </Tooltip>
      );
    }
    if (columnsCodeList.includes('pointData.current')) {
      //现值
      list.push(<PointText key="pointData.current" alarm={alarm} idc={idc} />);
    }

    return (
      <Space split={<Divider type="vertical" />} size={0}>
        {list}
      </Space>
    );
  }, [alarm, columnsCodeList, idc]);

  return (
    <div style={style}>
      <Card
        style={{ width: 'calc(100% - 24px)', height: 'calc(100% - 12px)' }}
        className={classNames('alarmCard', getCardClassName())}
        size="small"
        bordered={false}
        onClick={() => onRowClick(alarm.id)}
      >
        <Space style={{ width: '100%' }} direction="vertical" size={4}>
          <Tooltip title={<AlarmTitle alarm={alarm} alarmLevels={alarmLevels} />}>
            <Badge
              style={{
                width: '100%',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              }}
              status={alarm?.type.code === 'ERROR' ? 'error' : 'warning'}
              text={<AlarmTitle alarm={alarm} alarmLevels={alarmLevels} />}
            />
          </Tooltip>

          {alarmShowColumnList}
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <Space size={0}>
              {columnsCodeList.includes('lifecycleState') && (
                <AlarmLifecyStatusText code={alarm.lifecycleState.code} shape="__tag__" />
              )}
              {columnsCodeList.includes('alarmState') && (
                <AlarmStatusText code={alarm.state.code} shape="__tag__" />
              )}
              {columnsCodeList.includes('alarmLabel') && <AlarmLabel alarm={alarm} />}
            </Space>
            <Operation
              style={{ alignItems: 'flex-start' }}
              mode="link"
              showInterestBtn
              idc={idc}
              alarm={alarm}
            />
          </div>
        </Space>
      </Card>
    </div>
  );
}
