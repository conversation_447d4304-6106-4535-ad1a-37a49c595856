import styled, { css } from 'styled-components';

export const AlarmStyle = styled.div`
  ${props => {
    const { colorWarning, boxShadow, colorErrorBorderHover } = props.theme.tokens;

    return css`
      .warnBoxShadow {
        box-shadow: 0px 1px 8px ${colorWarning};
      }
      .errBoxShadow {
        box-shadow: 0px 1px 8px ${colorErrorBorderHover};
      }
      .alarmCard {
        margin: 12px;
        box-shadow: 0px 1px 8px ${boxShadow};
      }
    `;
  }}
`;
