import uniqBy from 'lodash.uniqby';
import minBy from 'lodash/minBy';
import React, { useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import type { AlarmJSON } from '@manyun/monitoring.model.alarm';
import { FaultTargetType } from '@manyun/ticket.model.event';
import { generateEventDetailRoutePath } from '@manyun/ticket.route.ticket-routes';
import { createEvent } from '@manyun/ticket.service.create-event';
import { EventMutator } from '@manyun/ticket.ui.event-mutator';

export type CreateEventProps = {
  style?: React.CSSProperties;
  selectedRows: AlarmJSON[];
  idcTag: string;
  blockTag: string;
  alarmIds: number[];
  activeTime: number | null;
  confirm: () => AlarmJSON | null;
  onCallBack?: () => void;
} & Pick<ButtonProps, 'disabled' | 'type'>;

export function CreateEvent({
  style,
  idcTag,
  blockTag,
  alarmIds,
  activeTime,
  selectedRows,
  type,
  disabled,
  confirm,
  onCallBack,
}: CreateEventProps) {
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState<boolean>(false);

  const [form] = Form.useForm();
  const infoType = Form.useWatch('infoType', form);
  const causeDevice = Form.useWatch('causeDevice', form);

  const submit = () => {
    form.validateFields().then(() => {
      handleOk();
    });
  };

  const handleOk = async () => {
    setLoading(true);
    const { confirmedAt, confirmUser } = confirm() || {};
    const { eventLevel, infoType, eventDesc, eventTitle } = form.getFieldsValue();
    const { data, error } = await createEvent({
      eventTitle,
      idcTag,
      blockTag: `${idcTag}.${blockTag}`,
      eventSource: '1',
      isFalseAlarm: 0,
      occurTime: new Date(activeTime!).getTime(),
      detectTime: new Date(activeTime!).getTime(),
      upgradeTime: new Date(activeTime!).getTime(),
      confirmTime: confirmedAt ? new Date(confirmedAt).getTime() : undefined,
      confirmBy: confirmUser?.id,
      confirmByName: confirmUser?.name,
      isChangeAlarm: false,
      eventSourceName: '监控系统',
      eventLevel: eventLevel.key,
      eventLevelName: eventLevel.label,
      infoType,
      causeDevice: getCauseDevice(),
      eventDesc,
      associateAlarmInfo: {
        alarmIds,
        alarmStartTimes: selectedRows.map(item => item.createdAt),
      },
    });
    if (data) {
      setLoading(false);
      setVisible(false);
      form.resetFields();
      onCallBack?.();
      window.open(generateEventDetailRoutePath({ id: data }));
    } else {
      setLoading(false);
      message.error(error?.message || '创建事件失败');
    }
  };

  const getCauseDevice = () => {
    if (Array.isArray(causeDevice)) {
      if (infoType === 'DEVICE') {
        return causeDevice.map(item => item.key ?? item.deviceGuid).join(',');
      }
      if (infoType === 'ROOM') {
        return causeDevice.join(',');
      }
    }
    return causeDevice;
  };
  const onButtonClick = () => {
    let otherBlock = false;
    const list = selectedRows.filter(item => item.lifecycleState.code === 'PROCESS');
    if (list.length) {
      message.error('勾选数据中存在已建单数据，请确认。');
      return;
    }
    selectedRows.forEach(i => {
      if (
        selectedRows.filter(j => j.source.spaceGuidMap.blockTag !== i.source.spaceGuidMap.blockTag)
          .length
      ) {
        otherBlock = true;
      }
    });
    if (otherBlock) {
      message.error('只可选择相同楼栋的告警新建事件');
      return;
    }
    setVisible(true);
  };

  const getEventDescInitValue = () => {
    if (selectedRows.length === 1) {
      return selectedRows[0].message;
    }
    const minRow = minBy(selectedRows, 'activatedAt');
    return minRow?.message;
  };

  const getInfoTypeInitValue = () => {
    if (selectedRows.filter(row => row.source.dimension === 'IDC').length) {
      return FaultTargetType.Other;
    }
    if (selectedRows.filter(row => row.source.dimension === 'DEVICE').length) {
      return FaultTargetType.Device;
    }
    return undefined;
  };

  const getCauseDeviceInitValue = () => {
    const rows = selectedRows.filter(row => row.source.dimension === 'DEVICE');
    if (rows.length) {
      return uniqBy(rows, 'source.guid').map((row: AlarmJSON) => {
        return {
          deviceType: row.source.deviceType,
          deviceGuid: row.source.guid,
          deviceName: row.source.name,
          roomTag: row.source.spaceGuidMap.roomTag,
          key: row.source.guid,
        };
      });
    }
    return undefined;
  };

  return (
    <Space style={{ width: '100%', display: 'flex' }} direction="vertical" size="large">
      <Button type={type} disabled={disabled} onClick={onButtonClick}>
        新建事件
      </Button>
      <Drawer
        open={visible}
        title="新建事件"
        style={style}
        size="large"
        placement="right"
        width={752}
        destroyOnClose
        extra={
          <Space>
            <Button
              onClick={() => {
                setVisible(false);
              }}
            >
              取消
            </Button>
            <Button type="primary" loading={loading} onClick={submit}>
              创建
            </Button>
          </Space>
        }
        onClose={() => {
          setVisible(false);
        }}
      >
        <EventMutator
          showFooter={false}
          externalForm={form}
          mode="create"
          formInitialValues={{
            eventDesc: getEventDescInitValue(),
            infoType: getInfoTypeInitValue(),
            causeDevice: getCauseDeviceInitValue(),
          }}
          getLocationFromExternal={`${idcTag}.${blockTag}`}
          unusedFormItems={[
            'location',
            'isFalseAlarm',
            'eventSource',
            'occurTime',
            'detectTime',
            'incidentType',
            'changeCode',
            'files',
            'isChangeAlarm',
            'causeBy',
            'causeDesc',
            'liableDept',
            'liablePerson',
            'eventOwnerIdList',
          ]}
        />
      </Drawer>
    </Space>
  );
}
