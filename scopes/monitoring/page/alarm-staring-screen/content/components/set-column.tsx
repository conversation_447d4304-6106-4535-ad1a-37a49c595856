import React, { useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { SettingOutlined } from '@ant-design/icons';
import get from 'lodash.get';

import { Card } from '@manyun/base-ui.ui.card';
import type { CheckboxChangeEvent } from '@manyun/base-ui.ui.checkbox';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Dropdown } from '@manyun/base-ui.ui.dropdown';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';

import { getAlarmLocales } from '@manyun/monitoring.model.alarm';
import {
  resetPreferencesAction,
  selectPreferences,
  updateSpecificBoardPreferences,
} from '@manyun/monitoring.state.alarms-monitoring-board';

import { layoutVariantToHump } from '../utils/index.js';

export type Column = {
  visible: boolean;
  locked: boolean;
  dataIndex: string;
  sortOrder?: string | null;
};
export type SetColumnProps = {
  columns: Column[];
  preferenceKey?: string;
  isTable?: boolean;
};

const alarmLocales = getAlarmLocales();

export function SetColumn({ columns, preferenceKey, isTable }: SetColumnProps) {
  const [open, setOpen] = useState(false);

  const onHide = () => {
    setOpen(false);
  };

  return (
    <Dropdown
      open={open}
      dropdownRender={() => (
        <SetColumnCard
          columns={columns}
          preferenceKey={preferenceKey}
          isTable={!!isTable}
          onHide={onHide}
        />
      )}
      placement="bottomRight"
      trigger={['click']}
      arrow={{ pointAtCenter: true }}
      onOpenChange={open => setOpen(open)}
    >
      <Tooltip title="列展示">
        <SettingOutlined />
      </Tooltip>
    </Dropdown>
  );
}

type SetColumnCardProps = {
  onHide: () => void;
  columns: Column[];
  preferenceKey: string | undefined;
  isTable: boolean;
};

function SetColumnCard({ onHide, columns, preferenceKey, isTable }: SetColumnCardProps) {
  const dispatch = useDispatch();
  const { layout } = useSelector(selectPreferences);

  const onChange = (e: CheckboxChangeEvent, index: number) => {
    dispatch(
      updateSpecificBoardPreferences({
        layout,
        preferences: { visible: e.target.checked },
        target:
          layout === 'table'
            ? `layoutColumns.table[${index}]`
            : `${preferenceKey}.layoutColumns.${isTable ? 'table' : 'card'}[${index}]`,
      })
    );
  };

  const values = useMemo(() => {
    const arr: string[] = [];
    columns.forEach((item: Column) => {
      if (item.visible) {
        arr.push(item.dataIndex);
      }
    });
    return arr;
  }, [columns]);

  function getTitle(dataIndex: string) {
    if (!dataIndex) {
      return '';
    }
    if (dataIndex === 'source') {
      return alarmLocales.source.__self;
    }
    if (dataIndex === 'source.spaceGuidMap') {
      return alarmLocales.source.spaceGuidMap.__self;
    }
    if (dataIndex === 'lifecycleState') {
      return alarmLocales.lifecycleState.__self;
    }
    if (dataIndex === 'alarmState') {
      return alarmLocales.state.__self;
    }
    if (dataIndex === 'confirmUser') {
      return alarmLocales.confirmUser.__self;
    }

    if (dataIndex === 'monitoringItem') {
      return alarmLocales.monitoringItem.__self;
    }

    const title = get(alarmLocales, dataIndex.split('.'), null);
    if (typeof title == 'string') {
      return title;
    } else {
      return dataIndex;
    }
  }

  const onReset = () => {
    const properties: ('layoutColumns.card' | 'layoutColumns.table')[] =
      layout === 'table' || isTable ? ['layoutColumns.table'] : ['layoutColumns.card'];
    dispatch(
      resetPreferencesAction({
        target: layout === 'table' ? 'table' : `${layoutVariantToHump(layout)}${preferenceKey}`,
        properties,
      })
    );
  };

  return (
    <Card
      style={{ width: 240 }}
      size="small"
      title={`列展示(${values.length}/${columns.length})`}
      extra={<Typography.Link onClick={onReset}>重置</Typography.Link>}
    >
      <Checkbox.Group value={values}>
        <Space direction="vertical" style={{ width: '100%' }}>
          {columns.map((item, index) => {
            const { locked, dataIndex } = item;
            return (
              <Checkbox
                key={dataIndex}
                value={dataIndex}
                disabled={locked}
                onChange={e => onChange(e, index)}
              >
                {getTitle(dataIndex)}
              </Checkbox>
            );
          })}
        </Space>
      </Checkbox.Group>
    </Card>
  );
}
