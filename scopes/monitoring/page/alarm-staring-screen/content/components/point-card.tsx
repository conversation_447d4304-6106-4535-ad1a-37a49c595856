import React, { useMemo } from 'react';

import moment from 'moment';
import type { Moment } from 'moment';

import { Card } from '@manyun/base-ui.ui.card';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Empty } from '@manyun/base-ui.ui.empty';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';

import { PointsLine, PointsLineModalButton } from '@manyun/monitoring.chart.points-line';
import {
  PointsStateLine,
  PointsStateLineModalButton,
} from '@manyun/monitoring.chart.points-state-line';
import type { AlarmJSON } from '@manyun/monitoring.model.alarm';
import { AddPointToDiffTool } from '@manyun/monitoring.ui.add-point-to-diff-tool';
import { usePoints } from '@manyun/resource-hub.hook.use-points';

export type PointTextProps = {
  idc: string;
  alarm: AlarmJSON;
};

export function PointText({ idc, alarm }: PointTextProps) {
  const point = alarm.point;
  const isAI = point.dataType.code === 'AI';

  const pointGuids = [
    {
      deviceGuid: alarm.source.guid,
      pointCode: point.code,
      unit: point.unit,
    },
  ];
  const seriesOption = [{ name: point.name }];

  const current = alarm.pointData.current;
  const currentText =
    typeof current == 'number'
      ? isAI
        ? `${alarm.pointData.current}${point.unit ?? ''}`
        : alarm.point.validLimits?.[current] ?? '--'
      : '--';

  return (
    <span
      onClick={event => {
        event.stopPropagation();
        event.nativeEvent.stopImmediatePropagation();
      }}
    >
      {isAI ? (
        <PointsLineModalButton
          idcTag={idc}
          btnText={currentText}
          modalText={point.name}
          pointGuids={pointGuids}
          seriesOption={seriesOption}
        />
      ) : (
        <PointsStateLineModalButton
          idcTag={idc}
          btnText={currentText}
          modalText={point.name}
          pointGuids={pointGuids}
          seriesOption={seriesOption}
          validLimitsMap={point.validLimits || {}}
        />
      )}
    </span>
  );
}

type PointCardProps = {
  alarm: AlarmJSON;
  idc: string;
};

export function PointCard({ alarm, idc }: PointCardProps) {
  const [{ data }] = usePoints({
    fields: { deviceType: alarm?.source.deviceType, isOnlyCore: true },
  });

  return (
    <Card>
      {data.length > 0 ? (
        <Descriptions column={3} size="small" labelStyle={{ maxWidth: 140 }}>
          {data.map(point => (
            <Descriptions.Item
              key={point.code}
              label={
                <Tooltip placement="topLeft" title={point.name} mouseEnterDelay={1}>
                  <Typography.Text ellipsis>{point.name}</Typography.Text>
                </Tooltip>
              }
            >
              <PointText alarm={alarm} idc={idc} />
            </Descriptions.Item>
          ))}
        </Descriptions>
      ) : (
        <Empty />
      )}
    </Card>
  );
}

export function PointModal({ alarm, idc }: PointCardProps) {
  const point = alarm.point;
  const seriesOption = [{ name: point.name }];
  const pointGuids = useMemo(() => {
    return [
      {
        deviceGuid: alarm.source.guid,
        pointCode: alarm.point.code,
        unit: point.unit,
      },
    ];
  }, [alarm.point.code, alarm.source.guid, point.unit]);

  const defaultTimeRange: [Moment, Moment] = useMemo(() => {
    const startTime = moment(alarm.createdAt).subtract(1, 'hours');

    if (alarm.state.code === 'RECOVER' && alarm.recoveredAt) {
      const isOverAnHour =
        moment(new Date().getTime()).diff(moment(alarm.recoveredAt), 'hours') > 1;

      if (isOverAnHour) {
        return [startTime, moment(alarm.recoveredAt).add(1, 'hours')];
      }

      return [startTime, moment()];
    }
    return [startTime, moment()];
  }, [alarm.createdAt, alarm.recoveredAt, alarm.state.code]);

  return (
    <Card
      title={
        <Space>
          {alarm.point.name}
          <AddPointToDiffTool pointGuids={pointGuids} />
        </Space>
      }
      style={{ height: 405, overflowY: 'auto' }}
    >
      {alarm.point.dataType.code === 'DI' ? (
        <PointsStateLine
          defaultTimeRange={defaultTimeRange}
          idcTag={idc}
          pointGuids={pointGuids}
          seriesOption={seriesOption}
          chartFunction="MAX"
          allowInterval
          validLimitsMap={alarm.point.validLimits || {}}
        />
      ) : (
        <PointsLine
          defaultTimeRange={defaultTimeRange}
          idcTag={idc}
          pointGuids={pointGuids}
          seriesOption={seriesOption}
          allowInterval
          chartFunction="MAX"
        />
      )}
    </Card>
  );
}
