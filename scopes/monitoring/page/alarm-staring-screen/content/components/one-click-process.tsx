import React, { useState } from 'react';

import { EllipsisOutlined } from '@ant-design/icons';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Dropdown } from '@manyun/base-ui.ui.dropdown';
import { Menu } from '@manyun/base-ui.ui.menu';
import type { MenuProps } from '@manyun/base-ui.ui.menu';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import type { AlarmJSON } from '@manyun/monitoring.model.alarm';

import { AlarmRemoveConfirmModal } from '../../components/alarm-remove-confirm-modal.js';

export type OneClickProcessProps = {
  disabled?: boolean;
  preferenceKey?: string;
  isRemovedByClickOnce?: boolean;
  alarms?: AlarmJSON[];
  onChange: (value: string) => void;
  onClear?: () => void;
};

type MenuItem = Required<MenuProps>['items'][number];

export function OneClickProcess({
  disabled = false,
  preferenceKey,
  isRemovedByClickOnce,
  alarms,
  onChange,
  onClear,
}: OneClickProcessProps) {
  const [, { checkCode }] = useAuthorized();
  const [selectedKeys, setSelectedKeys] = useState<string[]>(['accepts']);
  const items = useDeepCompareMemo(() => {
    let newMenus: MenuItem[] = [];
    if (disabled) {
      newMenus = [{ label: '一键受理', key: 'accepts', disabled: true }];
      if (preferenceKey === 'your-interests') {
        newMenus = [
          { label: '一键受理', key: 'accepts', disabled: true },
          { label: '一键取消关注', key: 'removeInterests', disabled: true },
        ];
      }
    } else {
      newMenus = [{ label: '一键受理', key: 'accepts' }];
      if (preferenceKey === 'your-interests') {
        newMenus = [
          { label: '一键受理', key: 'accepts' },
          { label: '一键取消关注', key: 'removeInterests' },
        ];
      }
    }

    if (isRemovedByClickOnce && alarms && checkCode('element_alarm-screen-one-click-remove')) {
      newMenus.push({
        label: (
          <AlarmRemoveConfirmModal
            isRemovedByClickOnce
            buttonType="link"
            disabled={disabled}
            selectedAlarmIds={alarms.map(item => item.id)}
            selectedAlarms={alarms}
            onRemoveCallBack={onClear}
          />
        ),
        key: 'oneClickProcess',
      });
    }

    return newMenus;
  }, [alarms, checkCode, disabled, isRemovedByClickOnce, preferenceKey]);

  const onSelect: MenuProps['onClick'] = ({ key }) => {
    setSelectedKeys([key]);
    onChange(key);
  };
  return (
    <Dropdown
      dropdownRender={() => <Menu items={items} selectedKeys={selectedKeys} onClick={onSelect} />}
      placement="bottomRight"
      trigger={['click']}
      arrow={{ pointAtCenter: true }}
    >
      <EllipsisOutlined />
    </Dropdown>
  );
}
