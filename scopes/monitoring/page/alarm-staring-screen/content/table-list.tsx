import type { Key } from 'react';
import React, { useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';

import minBy from 'lodash/minBy';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Row } from '@manyun/base-ui.ui.grid';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType, TableProps } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';

import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import type { AlarmJSON } from '@manyun/monitoring.model.alarm';
import {
  acceptAlarmsAction,
  addAlarmsIntoInterestsAction,
  removeAlarmsFromInterestsAction,
  selectAlarms,
  selectAlarmsGroupsIds,
  selectBoardPreferences,
} from '@manyun/monitoring.state.alarms-monitoring-board';

import { useAlarmStaringScreen } from '../alarm-staring-screen.context.js';
import { AlarmRemoveConfirmModal } from '../components/alarm-remove-confirm-modal.js';
import { AlarmSort } from './components/alarm-sort.js';
import AssociatedTicket from './components/associated-ticket.js';
import { CreateEvent } from './components/create-event.js';
import { OneClickProcess } from './components/one-click-process.js';
import type { Column } from './components/set-column.js';
import { SetColumn } from './components/set-column.js';
import styles from './content.module.less';
import { getCloumns } from './utils/get-columns.js';

export function TableList() {
  const dispatch = useDispatch();
  const { idc } = useParams<{ idc: string }>();
  const [, { setAlarmId }] = useAlarmStaringScreen();

  const alarms = useSelector(selectAlarms);

  const boardPreferences = useSelector(selectBoardPreferences('table'));
  const { layoutColumns, sort } = boardPreferences;

  const { table: showColumns } = layoutColumns;

  const alarmsGroupsIds = useSelector(selectAlarmsGroupsIds('table'));

  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const [selectedRows, setSelectedRows] = useState<AlarmJSON[]>([]);

  //关联
  const [associatedVisible, setAssociatedVisible] = useState<boolean>(false);
  const [associatedSelectedRowKeys, setAssociatedSelectedRowKeys] = useState<number[]>([]);
  const [associatedSelectedRows, setAssociatedSelectedRows] = useState<AlarmJSON[]>();

  const dataSource = alarmsGroupsIds.map(item => alarms[item]);

  const onResetSelectedRowKeys = () => {
    setSelectedRowKeys([]);
    setSelectedRows([]);
  };

  const onSelectChange = (newSelectedRowKeys: Key[], selectedRows: AlarmJSON[]) => {
    setSelectedRowKeys(newSelectedRowKeys as number[]);
    setSelectedRows(selectedRows);
  };

  const rowSelection: TableProps<AlarmJSON>['rowSelection'] = {
    selectedRowKeys,
    onChange: onSelectChange,
    renderCell(_value, _record, _index, node) {
      return {
        props: {
          style: {
            padding: 0,
          },
          className: styles.selectionCell,
          onClick: (evt: React.MouseEvent<HTMLTableCellElement>) => {
            evt.stopPropagation();
          },
        },
        children: node,
      };
    },
  };

  const onShowDetail = (record: AlarmJSON) => {
    setAlarmId(record.id);
  };

  const getConfirm = () => {
    const minTime = minBy(selectedRows, 'confirmedAt');
    if (minTime) {
      return minTime;
    }
    return null;
  };

  const getActiveTime = () => {
    const minTime = minBy(selectedRows, 'activatedAt');
    if (minTime) {
      return minTime.activatedAt;
    }
    return null;
  };

  const getFooter = () => {
    return (
      <Row justify="space-between" className={styles.tableFooter}>
        <Space>
          <Typography.Text>已选择 {selectedRowKeys.length} 项</Typography.Text>
          <Typography.Link onClick={onResetSelectedRowKeys}>取消选择</Typography.Link>
        </Space>
        <Space size="middle">
          <AlarmRemoveConfirmModal
            disabled={
              selectedRows.length
                ? !selectedRows.every(item =>
                    ['CONFIRMED', 'PROCESS'].includes(item.lifecycleState.code)
                  )
                : true
            }
            buttonType="primary"
            selectedAlarmIds={selectedRowKeys}
            selectedAlarms={selectedRows}
            resetSelectedRowKeys={resetSelectedRowKeys}
            onRemoveCallBack={onResetSelectedRowKeys}
          />
          <Button
            disabled={
              selectedRows.length
                ? selectedRows.some(
                    item =>
                      item.lifecycleState.code === 'PROCESS' ||
                      item.lifecycleState.code === 'ACTIVE'
                  )
                : true
            }
            onClick={onAssociatedVisible}
          >
            批量关联
          </Button>
          <CreateEvent
            idcTag={idc}
            blockTag={
              selectedRows.length && selectedRows[0] && selectedRows[0].source.spaceGuidMap.blockTag
            }
            alarmIds={selectedRowKeys}
            activeTime={getActiveTime()}
            confirm={getConfirm}
            type="primary"
            disabled={
              selectedRows.length
                ? selectedRows.some(
                    item =>
                      item.lifecycleState.code === 'PROCESS' ||
                      item.lifecycleState.code === 'ACTIVE'
                  )
                : true
            }
            selectedRows={selectedRows}
            onCallBack={onResetSelectedRowKeys}
          />
          <Button disabled={!selectedRowKeys.length} onClick={onBatchIntoInterests}>
            批量关注
          </Button>
          <Button disabled={!selectedRowKeys.length} onClick={onBatchAcceptAlarms}>
            批量受理
          </Button>
        </Space>
      </Row>
    );
  };
  //关注
  const onBatchIntoInterests = () => {
    dispatch(
      addAlarmsIntoInterestsAction({
        alarmIds: selectedRowKeys,
        callback: error => {
          if (!error) {
            message.success('批量关注成功');
            onResetSelectedRowKeys();
          } else {
            message.error(error.message);
          }
        },
      })
    );
  };

  //批量受理
  const onBatchAcceptAlarms = () => {
    dispatch(
      acceptAlarmsAction({
        alarmIds: selectedRowKeys,
        callback: error => {
          if (!error) {
            message.success('批量受理成功');
            onResetSelectedRowKeys();
          } else {
            message.error(error.message);
          }
        },
      })
    );
  };

  //关联
  const onAssociatedVisible = () => {
    let otherBlock = false;

    const list = selectedRows.filter(item => item.lifecycleState.code === 'PROCESS');
    if (list.length) {
      message.error('勾选数据中存在已建单数据，请确认。');
      return;
    }
    selectedRows.forEach(i => {
      if (
        selectedRows.filter(j => j.source.spaceGuidMap.blockTag !== i.source.spaceGuidMap.blockTag)
          .length
      ) {
        otherBlock = true;
      }
    });
    if (otherBlock) {
      message.error('只可选择相同楼栋的告警关联事件');
      return;
    }
    setAssociatedVisible(true);
    setAssociatedSelectedRowKeys(selectedRowKeys);
    setAssociatedSelectedRows(selectedRows);
  };

  const resetSelectedRowKeys = () => {
    setAssociatedVisible(false);
    setAssociatedSelectedRowKeys([]);
    setAssociatedSelectedRows([]);
    onResetSelectedRowKeys();
  };

  const alarmColumns = useMemo(() => {
    const columnCodes: string[] = [];
    if (env.__DEBUG_MODE__) {
      columnCodes.push('id');
    }
    showColumns.forEach(item => {
      if (item.visible) {
        columnCodes.push(item.dataIndex);
      }
    });
    columnCodes.push('operation');

    const newColumns = columnCodes
      .map(columnCode => {
        const columnItem = getCloumns({
          idc,
          showInterest: true,
          showDetailBtnFn: onShowDetail,
        }).find(item => item.dataIndex === columnCode);
        return columnItem;
      })
      .filter((item): item is ColumnType<AlarmJSON> => item !== undefined);
    return newColumns;
  }, [idc, showColumns]);

  const onChange = (value: string) => {
    if (value === 'removeInterests') {
      dispatch(
        removeAlarmsFromInterestsAction({
          alarmIds: alarmsGroupsIds,
          callback: error => {
            if (!error) {
              message.success('取消关注成功');
            } else {
              message.error(error.message);
            }
          },
        })
      );
    }
    if (value === 'accepts') {
      dispatch(
        acceptAlarmsAction({
          alarmIds: alarmsGroupsIds,
          callback: error => {
            if (!error) {
              message.success('受理成功');
            } else {
              message.error(error.message);
            }
          },
        })
      );
    }
  };

  return (
    <Card
      title="告警盯屏列表页"
      bordered={false}
      size="small"
      bodyStyle={{
        height: window.innerHeight - 246,
        overflowY: 'auto',
        paddingBottom: 50,
      }}
      extra={
        <Space size={17} className={styles.secondaryDark} style={{ cursor: 'pointer' }}>
          <AlarmSort sort={sort} />
          <SetColumn columns={showColumns as Column[]} />
          <OneClickProcess
            disabled={!dataSource.length}
            alarms={dataSource}
            isRemovedByClickOnce
            onChange={onChange}
            onClear={onResetSelectedRowKeys}
          />
        </Space>
      }
    >
      <Table
        scroll={{ x: 'max-content' }}
        rowSelection={rowSelection}
        dataSource={dataSource}
        rowKey="id"
        columns={alarmColumns}
      />
      {!!selectedRowKeys.length && getFooter()}
      {associatedVisible && (
        <AssociatedTicket
          alarmIds={associatedSelectedRowKeys}
          type="link"
          text="关联"
          selectedRows={associatedSelectedRows}
          idc={idc}
          blockTag={associatedSelectedRows?.[0]?.source.spaceGuidMap.blockTag}
          onCancel={resetSelectedRowKeys}
        />
      )}
    </Card>
  );
}
