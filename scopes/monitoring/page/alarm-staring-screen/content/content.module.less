@import (reference) '@manyun/base-ui.theme.theme/dist/theme.less';

.secondaryDark {
  color: @text-color-secondary;
}
.activeColor {
  color: @primary-color;
}
.errorColor {
  color: @error-color;
}

.alarmItemTitle {
  padding-bottom: 8px;
  display: flex;
  justify-content: space-between;
  .collectIcon {
    visibility: hidden;
    cursor: pointer;
  }

  &:hover .collectIcon {
    visibility: inherit;
  }
}
.textHover {
  &:hover {
    color: @primary-color;
    cursor: pointer;
  }
}
.flexboxColumn {
  display: flex;
  flex-direction: column;
  & > .sortIconDefault {
    color: @text-color-secondary;
  }
  & > .sortIconActive {
    color: @primary-color;
  }
}
.description {
  :global(.manyun-descriptions-header) {
    margin-bottom: 0;
    padding: 8px 24px;
    border-top: 1px solid @border-color-split;
    border-left: 1px solid @border-color-split;
    border-right: 1px solid @border-color-split;
  }
}
.tableFooter {
  width: 100vw;
  padding: 16px 24px;
  background-color: @white;
  border: 1px solid @background-color-base;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 10;
}

.selectionCell {
  :global(.@{prefixCls}-checkbox-wrapper) {
    padding: @table-padding-vertical @padding-xs;
  }
}
