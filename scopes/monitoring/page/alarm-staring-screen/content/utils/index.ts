import dayjs from 'dayjs';

import { formatInterval } from '@manyun/base-ui.util.date-fns.format-interval';

import type { LayoutVariant } from '@manyun/monitoring.state.alarms-monitoring-board';

export const emptyText = '--';

export const layoutVariantToHump = (layout: LayoutVariant) => {
  switch (layout) {
    case 'board--by-states':
      return 'boards.byStates.';
    case 'board--by-scopes':
      return 'boards.byScopes.';
    case 'board--by-alarm-levels':
      return 'boards.byAlarmLevels.';
    default:
      return '';
  }
};

/**
 * 获取俩个时间戳差值以时分秒形式显示
 * @param {number} startTime
 * @param {number} endTime
 */
export const getDateDiffString = (startTime: number | null, endTime: number | null) => {
  if (!startTime || !endTime) {
    return '';
  }
  const alarmSeconds = dayjs(startTime).diff(endTime, 's');
  //const dayString = Math.floor(alarmSeconds / 60 / 60 / 24);
  const hours = String(Math.floor(alarmSeconds / 60 / 60)).padStart(2, '0');
  const minutes = String(Math.floor((alarmSeconds / 60) % 60)).padStart(2, '0');
  const seconds = String(Math.floor(alarmSeconds % 60)).padStart(2, '0');
  return `${hours}:${minutes}:${seconds}`;
};

/**
 * formatInterval封装 大于一小时展示分
 * @param {number} interval
 */
export const getformatInterval = (interval: number) => {
  if (!interval) {
    return '';
  }
  if (interval > 60 * 60) {
    return formatInterval(Math.floor(interval / 60), 'minutes');
  } else {
    return formatInterval(interval, 'seconds');
  }
};

export const TOPOLOGY_BOUNDARY_MAP = {
  // 柴发
  GENERATOR: 'GENERATOR',
  // 高压
  HV: 'HV',
  // 低压
  LV: 'LV',
  // 暖通
  HVAC: 'HVAC',
};
/** @deprecated Replaced by `type TopologyType` from package `@manyun/dc-brain.config.base` */
export const TOPOLOGY_TYPE_KEY_MAP = {
  /** 电力拓扑 */
  ELECTRIC_POWER: 'ELECTRIC_POWER',
  /** 暖通拓扑 */
  HVAC: 'HVAC',
  /** 设施包间拓扑 */
  ROOM_FACILITY: 'ROOM_FACILITY',
};
