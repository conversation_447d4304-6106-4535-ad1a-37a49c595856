import React from 'react';
import { Link } from 'react-router-dom';

import dayjs from 'dayjs';
import get from 'lodash.get';

import type { ColumnType } from '@manyun/base-ui.ui.table';
import { formatInterval } from '@manyun/base-ui.util.date-fns.format-interval';

import { getAlarmLocales } from '@manyun/monitoring.model.alarm';
import type {
  AlarmJSON,
  BackMergeStatus,
  BackendExpectStatus,
} from '@manyun/monitoring.model.alarm';
import {
  generateRoomMonitoringUrl,
  generateSpaceOrDeviceRoutePath,
  generateSpecificMonitorItemConfigLocation,
} from '@manyun/monitoring.route.monitoring-routes';
import { AlarmLabel } from '@manyun/monitoring.ui.alarm-label';
import { AlarmLevelText } from '@manyun/monitoring.ui.alarm-level-text';
import { AlarmLifecyStatusText } from '@manyun/monitoring.ui.alarm-lifecy-status-text';
import { AlarmReasonText } from '@manyun/monitoring.ui.alarm-reason-text';
import { AlarmStatusText } from '@manyun/monitoring.ui.alarm-status-text';
import { AlarmTypeText } from '@manyun/monitoring.ui.alarm-type-text';
import { DeviceTypeText } from '@manyun/resource-hub.ui.device-type-text';
import { RoomTypeText } from '@manyun/resource-hub.ui.room-type-text';
import { getSpaceGuid } from '@manyun/resource-hub.util.space-guid';
import {
  generateChangeTicketDetail,
  generateEventDetailRoutePath,
} from '@manyun/ticket.route.ticket-routes';

import { AlarmOperation } from '../components/alarm-operation.js';
import { PointText } from '../components/point-card.js';
import { emptyText } from './index.js';

const alarmLocales = getAlarmLocales();

const dateFormat = (time: number | null, format: string) => {
  return time ? dayjs(time).format(format) : emptyText;
};

export const getCloumns = ({
  idc,
  showInterest,
  showDetailBtnFn,
  format = 'MM-DD HH:mm:ss',
}: {
  idc: string;
  showInterest?: boolean;
  showDetailBtnFn?: (arg: AlarmJSON) => void;
  format?: string;
}) => {
  const columns: ColumnType<AlarmJSON>[] = [
    {
      title: '告警id',
      dataIndex: 'id',
      fixed: 'left',
    },
    {
      title: alarmLocales.state.__self, //告警状态
      dataIndex: 'alarmState',
      render: (_, record: AlarmJSON) => {
        return <AlarmStatusText code={record.state.code} />;
      },
    },
    {
      title: alarmLocales.cause.name, //告警原因
      dataIndex: 'cause.name',
      render: (_, record: AlarmJSON) => {
        return record.cause.name;
      },
    },
    {
      title: alarmLocales.confirmUser.__self, //受理人
      dataIndex: 'confirmUser',
      render: (_, record: AlarmJSON) => {
        if (record.confirmUser?.name === 'SYSTEM') {
          return '系统';
        }
        return get(record, 'confirmUser.name', emptyText);
      },
    },
    {
      title: alarmLocales.confirmedAt, //受理时间
      dataIndex: 'confirmedAt',
      render: (text: number) => dateFormat(text, format),
    },
    {
      title: alarmLocales.removedAt, //下盯屏时间
      dataIndex: 'removedAt',
      render: (text: number) => dateFormat(text, format),
    },
    {
      title: alarmLocales.createdAt, //告警开始时间
      dataIndex: 'createdAt',
      render: (text: number) => dateFormat(text, format),
    },
    {
      title: alarmLocales.customerCount, //影响客户数
      dataIndex: 'customerCount',
    },
    {
      title: alarmLocales.deviceCount, //影响设备数
      dataIndex: 'deviceCount',
    },
    {
      title: alarmLocales.eventId, //事件单号
      dataIndex: 'eventId',
      render: (_, { eventId }) =>
        eventId ? (
          <Link
            target="_blank"
            to={generateEventDetailRoutePath({
              id: eventId,
            })}
          >
            {eventId}
          </Link>
        ) : (
          emptyText
        ),
    },
    {
      title: alarmLocales.gridCount, //影响机柜数
      dataIndex: 'gridCount',
    },
    {
      title: alarmLocales.latestTriggeredAt, //最新告警时间
      dataIndex: 'latestTriggeredAt',
      render: (text: number) => dateFormat(text, format),
    },
    {
      title: alarmLocales.alarmLabel, //标签
      dataIndex: 'alarmLabel',
      render: (_, alarm) => <AlarmLabel alarm={alarm} />,
    },
    {
      title: alarmLocales.level, //级别
      dataIndex: 'level',
      render: (text: string | number) => {
        return <AlarmLevelText code={text} />;
      },
    },
    {
      title: alarmLocales.lifecycleState.__self, //处理状态
      dataIndex: 'lifecycleState',
      render: (_, record: AlarmJSON) => {
        return <AlarmLifecyStatusText code={record.lifecycleState.code} />;
      },
    },
    {
      title: alarmLocales.mergeCount, //收敛告警数
      dataIndex: 'mergeCount',
    },
    {
      title: alarmLocales.mergeStatus.__self, //收敛告警状态
      dataIndex: 'mergeStatus',
      render: (mergeStatus: BackMergeStatus) => {
        return alarmLocales.mergeStatus[mergeStatus];
      },
    },
    {
      title: alarmLocales.message, //告警内容
      dataIndex: 'message',
    },
    {
      title: alarmLocales.monitoringItem.__self, //命中规则id
      dataIndex: 'monitoringItem',
      render: (_, record: AlarmJSON) => {
        return (
          <Link
            to={generateSpecificMonitorItemConfigLocation({
              configId: String(record.monitoringItem.id),
              deviceType: record.source.deviceType,
              pointCode: record.point.code,
            })}
          >
            查看
          </Link>
        );
      },
    },
    {
      title: alarmLocales.monitoringItem.threshold, //命中规则
      dataIndex: 'monitoringItem.threshold',
      render: (_, record: AlarmJSON) => {
        return record.monitoringItem.threshold;
      },
    },
    {
      title: alarmLocales.notifyCount, //通知次数
      dataIndex: 'notifyCount',
    },
    {
      title: alarmLocales.point.name, //测点名称
      dataIndex: 'point.name',
      render: (_, record: AlarmJSON) => {
        return record.point.name;
      },
    },
    {
      title: alarmLocales.pointData.current, //现值
      dataIndex: 'pointData.current',
      render: (_, record: AlarmJSON) => {
        return <PointText alarm={record} idc={idc} />;
      },
    },
    {
      title: alarmLocales.pointData.snapshot, //告警值
      dataIndex: 'pointData.snapshot',
      render: (_, record: AlarmJSON) => {
        const isAI = record.point.dataType.code === 'AI';
        if (isAI) {
          return `${record.pointData.snapshot}${record.point.unit || ''}`;
        } else {
          return (
            record.point.validLimits && record.point.validLimits[Number(record.pointData.snapshot)]
          );
        }
      },
    },
    {
      title: alarmLocales.recoveredAt, //告警恢复时间
      dataIndex: 'recoveredAt',
      render: (text: number) => dateFormat(text, format),
    },
    {
      title: alarmLocales.alarmDuration, // 告警持续时长
      dataIndex: 'alarmDuration',
      render: (text: number) => {
        return text ? formatInterval(text) : emptyText;
      },
    },
    {
      title: alarmLocales.source.__self, //告警对象
      dataIndex: 'source',
      render: (_, record: AlarmJSON) => {
        if (record.source.dimension === 'GRID' || record.source.dimension === 'COLUMN') {
          return record.source.name;
        }
        return (
          <Link
            to={generateSpaceOrDeviceRoutePath({
              guid: record.source.guid,
              type: record.source.dimension,
            })}
            target="_blank"
            onClick={e => e.stopPropagation()}
          >
            {record.source.name}
          </Link>
        );
      },
    },
    {
      title: alarmLocales.source.deviceLabel, //设备标签
      dataIndex: 'source.deviceLabel',
      render: (_, record: AlarmJSON) => {
        return record.source.deviceLabel;
      },
    },
    {
      title: alarmLocales.source.deviceType, //设备类型
      dataIndex: 'source.deviceType',
      render: (_, record: AlarmJSON) => {
        return <DeviceTypeText code={record.source.deviceType} />;
      },
    },
    {
      title: alarmLocales.source.roomType,
      dataIndex: 'source.roomType',
      render: (_, record: AlarmJSON) => {
        return record.source.roomType ? <RoomTypeText code={record.source.roomType} /> : emptyText;
      },
    },
    {
      title: alarmLocales.source.spaceGuidMap.__self, //位置
      dataIndex: 'source.spaceGuidMap',
      render: (_, record: AlarmJSON) => {
        const { blockTag, roomTag } = record.source.spaceGuidMap;
        const spaceGuid = getSpaceGuid(idc, blockTag, roomTag);

        const text =
          blockTag && roomTag && record.source.roomName
            ? `${blockTag}楼.${roomTag}${record.source.roomName}`
            : blockTag
              ? `${blockTag}楼`
              : idc;

        if (!spaceGuid) {
          return emptyText;
        }

        return (
          <Link
            to={generateSpaceOrDeviceRoutePath({
              guid: spaceGuid,
              type: roomTag ? 'ROOM' : blockTag ? 'BLOCK' : 'IDC',
              spotlightTarget: record.source.dimension === 'DEVICE' ? record.source.guid : '',
            })}
            target="_blank"
            onClick={e => e.stopPropagation()}
          >
            {text}
          </Link>
        );
      },
    },
    {
      title: alarmLocales.source.spaceGuidMap.blockTag,
      dataIndex: 'source.spaceGuidMap.blockTag',
      render: (_, record: AlarmJSON) => {
        return record.source.spaceGuidMap.blockTag;
      },
    },
    {
      title: alarmLocales.source.spaceGuidMap.columnTag, //机列
      dataIndex: 'source.spaceGuidMap.columnTag',
      render: (_, record: AlarmJSON) => {
        return record.source.spaceGuidMap.columnTag;
      },
    },
    {
      title: alarmLocales.source.spaceGuidMap.gridTag, //机柜
      dataIndex: 'source.spaceGuidMap.gridTag',
      render: (_, record: AlarmJSON) => {
        return record.source.spaceGuidMap.gridTag;
      },
    },
    {
      title: alarmLocales.source.spaceGuidMap.idcTag,
      dataIndex: 'source.spaceGuidMap.idcTag',
      render: (_, record: AlarmJSON) => {
        return record.source.spaceGuidMap.idcTag;
      },
    },
    {
      title: alarmLocales.source.spaceGuidMap.roomTag, //包间
      dataIndex: 'source.spaceGuidMap.roomTag',
      render: (_, record: AlarmJSON) => {
        if (!record.source.spaceGuidMap.blockTag || !record.source.spaceGuidMap.roomTag) {
          return emptyText;
        }
        return (
          <Link
            onClick={e => { 
              window.open(generateRoomMonitoringUrl({
                idc,
                block: record.source.spaceGuidMap.blockTag,
                room: record.source.spaceGuidMap.roomTag,
                spotlightTarget: record.source.dimension === 'DEVICE' ? record.source.guid : '',
              }))
              e.stopPropagation() 
            }}
          >
            {record.source.spaceGuidMap.roomTag}
          </Link>
        );
      },
    },
    {
      title: alarmLocales.source.roomName,
      dataIndex: 'source.roomName',
      render: (_, record: AlarmJSON) => {
        return record.source.roomName || emptyText;
      },
    },
    {
      title: alarmLocales.type.name, //告警类型
      dataIndex: 'type.name',
      fixed: 'left',
      render: (_, record: AlarmJSON) => {
        return <AlarmTypeText code={record.type.code} />;
      },
    },
    {
      title: alarmLocales.reason, //处理反馈
      dataIndex: 'reason',
      render: (text: string | null) => {
        if (text === 'SYSTEM') {
          return '系统下盯屏';
        }
        return text ? <AlarmReasonText code={text} /> : emptyText;
      },
    },
    {
      title: alarmLocales.removeUser, //下盯屏人
      dataIndex: 'removeUser',
      render: (text: string) => {
        if (text === 'SYSTEM') {
          return '系统';
        }
        return text || emptyText;
      },
    },
    {
      title: alarmLocales.changeId, //变更单号
      dataIndex: 'changeId',
      render: (changeId: string) => {
        if (changeId) {
          return (
            <Link to={generateChangeTicketDetail({ id: changeId })} target="_blank">
              {changeId}
            </Link>
          );
        }
        return emptyText;
      },
    },
    {
      title: alarmLocales.expectedStatus.__self, //变更状态
      dataIndex: 'expectedStatus',
      render: (text: BackendExpectStatus) => {
        return alarmLocales.expectedStatus[text];
      },
    },
    {
      title: '操作',
      dataIndex: 'operation',
      fixed: 'right',
      render: (_, record: AlarmJSON) => {
        return (
          <AlarmOperation
            idc={idc}
            alarm={record}
            showInterestBtn={showInterest}
            showDetailBtnFn={showDetailBtnFn}
          />
        );
      },
    },
  ];
  return columns;
};
