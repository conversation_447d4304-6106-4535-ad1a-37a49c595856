import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { ColumnWidthOutlined } from '@ant-design/icons';

import { Card } from '@manyun/base-ui.ui.card';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';

import type { AlarmJSON } from '@manyun/monitoring.model.alarm';
import {
  selectPreferences,
  updateSpecificBoardPreferences,
} from '@manyun/monitoring.state.alarms-monitoring-board';

import { GetTitle } from '../components/get-title.js';

export type SmallListProps = {
  preferenceKey: string;
  alarmList: AlarmJSON[];
};

export function SmallList({ preferenceKey, alarmList }: SmallListProps) {
  const dispatch = useDispatch();
  const { layout } = useSelector(selectPreferences);

  const onToCard = () => {
    dispatch(
      updateSpecificBoardPreferences({
        layout,
        preferences: {
          [preferenceKey]: {
            layout: 'card',
          },
        },
      })
    );
  };

  return (
    <Card
      title={
        <Tooltip title="卡片布局">
          <ColumnWidthOutlined style={{ cursor: 'pointer' }} onClick={onToCard} />
        </Tooltip>
      }
      bordered={false}
      style={{ width: 84, textAlign: 'center' }}
      bodyStyle={{
        height: window.innerHeight - 264,
        overflow: 'auto',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
      }}
    >
      <p style={{ writingMode: 'verticalLr', width: 14 }}>
        <GetTitle code={preferenceKey} />
      </p>
      {alarmList.length ? (
        <Typography.Text type="danger">{alarmList.length}</Typography.Text>
      ) : (
        <p>{alarmList.length}</p>
      )}
    </Card>
  );
}
