import type { ReactNode } from 'react';
import React, { useMemo } from 'react';
import { useSelector } from 'react-redux';

import { Space } from '@manyun/base-ui.ui.space';

import type { AlarmJSON } from '@manyun/monitoring.model.alarm';
import {
  selectAlarms,
  selectAlarmsGroupsIds,
  selectBoardPreferences,
  selectPreferences,
  selectYourInterestsAlarmsIds,
} from '@manyun/monitoring.state.alarms-monitoring-board';

import { CommonList } from './common-list.js';
import { LargeList } from './large-list.js';
import { SmallList } from './small-list.js';
import { TableList } from './table-list.js';

export type ContentProps = {
  children?: ReactNode;
};

export function Content({ children }: ContentProps) {
  const { layout } = useSelector(selectPreferences);
  // @ts-expect-error ts(2769)
  const boardPreferences = useSelector(selectBoardPreferences(layout));
  const { boardColumns } = boardPreferences;
  // @ts-expect-error ts(2769)
  const alarmsGroupsIds = useSelector(selectAlarmsGroupsIds(layout));

  const interestsAlarmsIds = useSelector(selectYourInterestsAlarmsIds);

  const alarms = useSelector(selectAlarms);

  const averageWith = useMemo(
    function () {
      if (layout !== 'table') {
        const screenWidth = window.innerWidth;
        const nowBoardColumns = ['your-interests', ...boardColumns];
        let miniNum: number = 0;
        let cardNum: number = 0;
        let tableNum: number = 0;
        nowBoardColumns.forEach(preferenceKey => {
          const preferenceItem = boardPreferences[preferenceKey];
          const { layout } = preferenceItem;
          if (layout === 'card') {
            cardNum = cardNum + 1;
          } else if (layout === 'table') {
            tableNum = tableNum + 1;
          } else {
            miniNum = miniNum + 1;
          }
        });
        const average =
          (screenWidth - 48 - miniNum * 84 - (nowBoardColumns.length - 1) * 16) /
          (cardNum + tableNum * 3);
        if (average < 590) {
          return 590;
        } else {
          return average;
        }
      }
      return 0;
    },
    [boardColumns, boardPreferences, layout]
  );

  if (layout === 'table') {
    return <TableList />;
  }

  const newBoardColumns = ['your-interests', ...boardColumns];

  return (
    <Space style={{ display: 'flex', width: '100%', overflowX: 'auto' }} size="middle">
      {newBoardColumns.map(preferenceKey => {
        const preferenceItem = boardPreferences[preferenceKey];
        const { layout } = preferenceItem;

        let alarmList: AlarmJSON[] = [];
        if (preferenceKey === 'your-interests') {
          alarmList = interestsAlarmsIds.map(item => alarms[item]).filter(item => item);
        } else {
          if (!alarmsGroupsIds[preferenceKey]) {
            alarmList = [];
          } else {
            alarmList = alarmsGroupsIds[preferenceKey]
              .map(item => alarms[item])
              .filter(item => item);
          }
        }
        if (layout === 'mini') {
          return (
            <SmallList key={preferenceKey} preferenceKey={preferenceKey} alarmList={alarmList} />
          );
        }
        if (layout === 'card') {
          return (
            <CommonList
              key={preferenceKey}
              preferenceKey={preferenceKey}
              preference={preferenceItem}
              alarmList={alarmList}
              averageWith={averageWith}
            />
          );
        }
        if (layout === 'table') {
          return (
            <LargeList
              key={preferenceKey}
              preferenceKey={preferenceKey}
              preference={preferenceItem}
              alarmList={alarmList}
              averageWith={averageWith}
            />
          );
        }
        return '';
      })}
    </Space>
  );
}
