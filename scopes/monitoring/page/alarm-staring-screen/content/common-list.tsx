import React, { useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { AutoSizer, List as VList } from 'react-virtualized';

import { UnorderedListOutlined, VerticalAlignMiddleOutlined } from '@ant-design/icons';

import { Card } from '@manyun/base-ui.ui.card';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';

import type { AlarmJSON } from '@manyun/monitoring.model.alarm';
import type { BoardPreferences } from '@manyun/monitoring.state.alarms-monitoring-board';
import {
  acceptAlarmsAction,
  removeAlarmsFromInterestsAction,
  selectPreferences,
  updateSpecificBoardPreferences,
} from '@manyun/monitoring.state.alarms-monitoring-board';
import { useMetadata } from '@manyun/resource-hub.gql.client.resources';
import { MetaType } from '@manyun/resource-hub.model.metadata';

import { useAlarmStaringScreen } from '../alarm-staring-screen.context.js';
import { AlarmLoading } from '../components/alarm-loading.js';
import { GetTitle } from '../components/get-title.js';
import { AlarmOperation } from './components/alarm-operation.js';
import { AlarmScreenCard } from './components/alarm-screen-card/index.js';
import { AlarmSort } from './components/alarm-sort.js';
import { OneClickProcess } from './components/one-click-process.js';
import { SetColumn } from './components/set-column.js';
import styles from './content.module.less';

export type CommonListProps = {
  alarmList: AlarmJSON[];
  preferenceKey: string;
  preference: BoardPreferences;
  averageWith?: number;
};

export function CommonList({
  alarmList,
  preferenceKey,
  preference,
  averageWith = 590,
}: CommonListProps) {
  const dispatch = useDispatch();
  const { layout } = useSelector(selectPreferences);
  const { sort, layoutColumns } = preference;
  const { card: showColumns } = layoutColumns;

  const [, { setAlarmId }] = useAlarmStaringScreen();

  const metaDataRes = useMetadata({
    variables: { type: MetaType.ALARM_LEVEL },
    fetchPolicy: 'cache-and-network',
  });
  const alarmLevels = metaDataRes.data?.metadata ?? [];

  const onToChangeBoard = (type: 'table' | 'mini') => {
    dispatch(
      updateSpecificBoardPreferences({
        layout,
        preferences: {
          [preferenceKey]: {
            layout: type,
          },
        },
      })
    );
  };
  const onChange = (value: string) => {
    const ids = alarmList.map((item: AlarmJSON) => item.id);
    if (value === 'removeInterests') {
      dispatch(
        removeAlarmsFromInterestsAction({
          alarmIds: ids,
          callback: error => {
            if (!error) {
              message.success('取消关注成功');
            } else {
              message.error(error.message);
            }
          },
        })
      );
    } else {
      dispatch(
        acceptAlarmsAction({
          alarmIds: ids,
          callback: error => {
            if (!error) {
              message.success('受理成功');
            } else {
              message.error(error.message);
            }
          },
        })
      );
    }
  };

  const isShowOneClick = useMemo(() => {
    return preferenceKey === 'accepted-n-recovered' || preferenceKey === 'accepted-n-unrecovered';
  }, [preferenceKey]);

  const onRowClick = (alarmId: number) => {
    setAlarmId(alarmId);
  };

  const renderItem = ({
    index,
    key,
    style,
  }: {
    index: number;
    key: string;
    style: React.CSSProperties;
  }) => {
    //高度不一致时需用CellMeasurer解决
    return (
      <AlarmScreenCard
        key={key}
        style={style}
        alarm={alarmList[index]}
        columns={showColumns}
        alarmLevels={alarmLevels}
        Operation={AlarmOperation}
        onRowClick={onRowClick}
      />
    );
  };

  return (
    <Card
      title={
        <div>
          <GetTitle code={preferenceKey} />
          {`(${alarmList.length ?? 0})`}
        </div>
      }
      bordered={false}
      style={{ width: averageWith }}
      bodyStyle={{ height: window.innerHeight - 264, overflowY: 'auto', padding: 12 }}
      extra={
        <Space size={17} className={styles.secondaryDark} style={{ cursor: 'pointer' }}>
          <Tooltip title="列表布局">
            <UnorderedListOutlined onClick={() => onToChangeBoard('table')} />
          </Tooltip>
          <Tooltip title="缩略布局">
            <VerticalAlignMiddleOutlined rotate={90} onClick={() => onToChangeBoard('mini')} />
          </Tooltip>

          <AlarmSort preferenceKey={preferenceKey} sort={sort} />
          <SetColumn columns={showColumns} preferenceKey={preferenceKey} />
          {isShowOneClick || (
            <OneClickProcess
              preferenceKey={preferenceKey}
              disabled={!alarmList.length}
              onChange={onChange}
            />
          )}
        </Space>
      }
    >
      {alarmList.length ? (
        <AutoSizer>
          {({ width, height }) => (
            <VList
              style={{ paddingBottom: 24 }}
              width={width}
              height={height}
              overscanRowCount={10}
              rowCount={alarmList.length}
              rowHeight={118}
              rowRenderer={renderItem}
            />
          )}
        </AutoSizer>
      ) : (
        <AlarmLoading />
      )}
    </Card>
  );
}
