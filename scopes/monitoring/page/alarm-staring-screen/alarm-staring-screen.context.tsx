import { createContext, useContext } from 'react';

export type Values = {
  alarmId?: number;
};

export type Handlers = { setAlarmId: (alarmId?: number) => void };

export type AlarmStaringScreenContextProps = [Values, Handlers];

const initialValue: AlarmStaringScreenContextProps = [
  { alarmId: undefined },
  { setAlarmId: () => {} },
];

export const AlarmStaringScreenContext =
  createContext<AlarmStaringScreenContextProps>(initialValue);

export const useAlarmStaringScreen = () => useContext(AlarmStaringScreenContext);
