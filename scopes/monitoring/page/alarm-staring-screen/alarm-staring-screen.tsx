import React, { useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation, useParams } from 'react-router-dom';
import { useDeepCompareEffect } from 'react-use';

import { userSliceActions } from '@manyun/auth-hub.state.user';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';
import {
  createBoardAction,
  getAlarmsMonitoringBoardLocales,
  resetBoardAction,
  selectAlarms,
  selectCurrentState,
} from '@manyun/monitoring.state.alarms-monitoring-board';
import { AlarmDrawer } from '@manyun/monitoring.ui.alarm-drawer';
import { ttsService } from '@manyun/monitoring.util.tts-service';
import { useDeviceTypesTree } from '@manyun/resource-hub.gql.client.resources';
import { getSpaceGuid } from '@manyun/resource-hub.util.space-guid';

import { AlarmStaringScreenContext } from './alarm-staring-screen.context.js';
import { AlarmOperation } from './content/components/alarm-operation.js';
import { Content } from './content/index.js';
import styles from './header/header.module.less';
import { Header } from './header/index.js';
import { StatisInfoList } from './statis-info-list/index.js';

const { initializingStage } = getAlarmsMonitoringBoardLocales();

//路由已删除，有其他组件引用，暂时没删除
export function AlarmStaringScreen() {
  const { idc } = useParams<{ idc: string }>();
  const { initializing, stage } = useSelector(selectCurrentState);
  const dispatch = useDispatch();
  const alarms = useSelector(selectAlarms);

  const { search } = useLocation();
  const { block: blockTag, room: roomTag } = getLocationSearchMap<{
    block?: string;
    room?: string;
  }>(search);

  useDeviceTypesTree();

  const [fullScreen, setFullScreen] = useState<boolean>(false);
  const [alarmId, setAlarmId] = useState<number>();

  const defaultBlockGuids = useMemo(() => {
    if (idc && blockTag) {
      const blockGuid = getSpaceGuid(idc, blockTag)!;
      return [blockGuid];
    }
    return undefined;
  }, [blockTag, idc]);

  const defaultRoomGuids = useMemo(() => {
    if (idc && blockTag && roomTag) {
      const roomGuid = getSpaceGuid(idc, blockTag, roomTag)!;
      return [roomGuid];
    }
    return undefined;
  }, [blockTag, idc, roomTag]);

  useDeepCompareEffect(() => {
    (async () => {
      dispatch(
        createBoardAction({
          hardRefresh: true,
          filters: {
            idc,
            blockGuids: defaultBlockGuids,
            roomGuids: defaultRoomGuids,
          },
        })
      );
      dispatch(userSliceActions.setUserIdc(idc));
    })();

    return () => {
      ttsService.cancel();
      dispatch(resetBoardAction());
    };
  }, [defaultBlockGuids, defaultRoomGuids, dispatch, idc]);

  const alarm = alarmId ? alarms[alarmId] : undefined;

  return (
    <div className={fullScreen ? styles.fullScreenStyle : ''}>
      <Spin spinning={initializing} tip={stage && initializingStage[stage]}>
        <Space style={{ width: '100%' }} direction="vertical" size={8}>
          <Header
            idc={idc}
            defaultBlockGuids={defaultBlockGuids}
            defaultRoomGuids={defaultRoomGuids}
            fullScreen={fullScreen}
            onSetFullScreen={setFullScreen}
          />
          <StatisInfoList />
          <AlarmStaringScreenContext.Provider value={[{ alarmId }, { setAlarmId }]}>
            <Content />
          </AlarmStaringScreenContext.Provider>
          {alarm && (
            <AlarmDrawer
              visible={!!alarm}
              idc={idc}
              alarm={alarm}
              extra={
                <AlarmOperation
                  idc={idc}
                  alarm={alarm}
                  mode="button"
                  showInterestBtn
                  showCreateBtn
                  onRemoveCallBack={() => setAlarmId(undefined)}
                />
              }
              onClose={() => setAlarmId(undefined)}
            />
          )}
        </Space>
      </Spin>
    </div>
  );
}
