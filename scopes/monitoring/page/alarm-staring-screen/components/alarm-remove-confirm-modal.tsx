/*eslint-disable*/

/*原js文件整体迁移过来*/
import { CheckCircleOutlined } from '@ant-design/icons';
import React, { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import type { AlarmJSON } from '@manyun/monitoring.model.alarm';
import { closeAlarmsAction } from '@manyun/monitoring.state.alarms-monitoring-board';
import { Metadata, MetaType } from '@manyun/resource-hub.model.metadata';
import { fetchMetaDataByTypeWeb } from '@manyun/resource-hub.service.fetch-meta-data-by-type';
import { fetchChanges } from '@manyun/ticket.service.fetch-changes';
import type { Change } from '@manyun/ticket.service.fetch-changes';
import { fetchEvents } from '@manyun/ticket.service.fetch-events';
import type { Event } from '@manyun/ticket.service.fetch-events';
import { ChangeTable } from '@manyun/ticket.ui.change-table';
import { EventTable } from '@manyun/ticket.ui.event-table';

const { Option } = Select;
const { TextArea } = Input;
export type AlarmRemoveConfirmModalProps = {
  mode?: 'icon' | 'button' | 'link';
  disabled?: boolean;
  buttonType: 'link' | 'text' | 'dashed' | 'default' | 'ghost' | 'primary' | undefined;
  // 获取盯屏数据的接口会在531重构，因此返回的查询结果类型在517暂时使用any
  record?: AlarmJSON;
  selectedAlarmIds?: string[] | number[];
  // 获取盯屏数据的接口会在531重构，因此传参类型在517暂时使用any
  selectedAlarms?: AlarmJSON[];
  params?: any;
  fetchAlarmScreenList?: (params: any) => void;
  resetSelectedRowKeys?: () => void;
  getStatusCount?: (status: string) => void;
  alarmStatus?: string;
  onRemoveCallBack?: () => void;
  isRemovedByClickOnce?: boolean;
  onOk?: ({
    alarmIds,
    reason,
    descriptions,
    ticketNumber,
  }: {
    alarmIds: number[];
    reason: string;
    descriptions: string;
    ticketNumber?: string | null;
  }) => void;
};

export const AlarmRemoveConfirmModal = (props: AlarmRemoveConfirmModalProps) => {
  const dispatch = useDispatch();

  const {
    mode,
    disabled,
    buttonType,
    record,
    selectedAlarmIds,
    selectedAlarms,
    resetSelectedRowKeys,
    onRemoveCallBack,
    isRemovedByClickOnce,
    onOk,
  } = props;
  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
  const [form] = Form.useForm();
  const [reasonType, setReasonType] = useState<string>('');
  const [reasonTypes, setReasonTypes] = useState<Metadata[] | []>([]);
  const [selectLoading, setSelectLoading] = useState<boolean>(true);
  // 筛选
  const [filterValue, setFilterValue] = useState<{
    id?: number | string;
    status?: string;
    title?: string;
  }>({});

  // 用于选择列表项
  const [eventList, setEventList] = useState<Event[]>([]);
  const [changeList, setChangeList] = useState<Change[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [pageSize, setPageSize] = useState<number>(10);
  const [pageNum, setPageNum] = useState<number>(1);
  const [loading, setLoading] = useState<boolean>(true);
  const [total, setTotal] = useState<number>(0);

  useEffect(() => {
    const getReasonTypes = async () => {
      const { data, error } = await fetchMetaDataByTypeWeb({ type: MetaType.ALARM_REASON });
      if (error) {
        message.error(error.message);
        return;
      }
      if (isRemovedByClickOnce) {
        setReasonTypes(data.data.filter(item => item.code !== 'CHANGE' && item.code !== 'EVENT'));
        return;
      }
      setReasonTypes(data.data);
    };
    if (isModalVisible) {
      setSelectLoading(true);
      form.resetFields();
      getReasonTypes();
      setSelectLoading(false);
      reset();
      setReasonType('');
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isModalVisible]);

  useEffect(() => {
    const getEventList = async () => {
      const { data, error } = await fetchEvents({
        pageNum: 1,
        pageSize,
      });
      if (error) {
        message.error(error.message);
        return;
      }
      setEventList(data.data);
      setTotal(data.total);
      setLoading(false);
    };

    const getChangeList = async () => {
      const { data, error } = await fetchChanges({
        pageNum: 1,
        pageSize,
        orderByCondition: 'gmtModified',
        statusList: ['CHANGING', 'IN_SUMMARY', 'WAITING_CLOSE', 'FINISH'],
      });
      if (error) {
        message.error(error.message);
        return;
      }
      setChangeList(data.data);
      setTotal(data.total);
      setLoading(false);
    };
    reset();
    if (reasonType === 'EVENT') {
      getEventList();
    }
    if (reasonType === 'CHANGE') {
      getChangeList();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [reasonType]);

  const reset = () => {
    setLoading(true);
    setPagination({ pageNum: 1, pageSize: 10 });
    setSelectedRowKeys([]);
    setFilterValue({});
    form.resetFields(['removeDesc']);
  };

  const showModal = (event: React.MouseEvent<HTMLElement, MouseEvent>) => {
    event.stopPropagation();
    event.nativeEvent.stopImmediatePropagation();
    setIsModalVisible(true);
  };

  const handleOk = async () => {
    const { alarmReason, removeDesc } = await form.validateFields();

    if (isRemovedByClickOnce && selectedAlarms) {
      const alarmIds = selectedAlarms
        .filter(
          item =>
            (item.lifecycleState.code === 'CONFIRMED' || item.lifecycleState.code === 'PROCESS') &&
            item.state.code === 'RECOVER'
        )
        .map(item => item.id);

      if (!alarmIds.length) {
        message.error('当前无可下盯屏告警！');
        return;
      }
      const ticketNumber = alarmIds.length ? alarmIds[0].toString() : null;
      if (onOk) {
        onOk({
          alarmIds,
          reason: alarmReason,
          descriptions: removeDesc,
          ticketNumber,
        });
      } else {
        dispatch(
          closeAlarmsAction({
            alarmIds,
            reason: alarmReason,
            descriptions: removeDesc,
            ticketNumber,
            isRemovedByClickOnce: true,
            callback: error => {
              if (!error) {
                message.success('下盯屏成功');
                onRemoveCallBack && onRemoveCallBack();
              } else {
                message.error(error.message);
              }
            },
          })
        );
      }

      setIsModalVisible(false);
      return;
    }

    if (!selectedRowKeys.length && ['EVENT', 'CHANGE'].includes(reasonType)) {
      return message.warn('请选择关联单号');
    }

    setIsModalVisible(false);

    const alarmIds = record ? [record.id] : selectedAlarmIds;
    const descriptions = removeDesc ? removeDesc : reasonType === 'EVENT' ? '关联事件' : '关联变更';
    const ticketNumber = selectedRowKeys.length ? selectedRowKeys[0].toString() : null;

    if (onOk) {
      onOk({
        alarmIds,
        reason: alarmReason,
        descriptions,
        ticketNumber,
      });
    } else {
      dispatch(
        closeAlarmsAction({
          alarmIds,
          reason: alarmReason,
          descriptions,
          ticketNumber,
          callback: error => {
            if (!error) {
              message.success('下盯屏成功');
              onRemoveCallBack && onRemoveCallBack();
            } else {
              message.error(error.message);
            }
          },
        })
      );
    }

    setLoading(true);

    resetSelectedRowKeys && resetSelectedRowKeys();
  };

  const onBatchRemove = async () => {
    const selectedRows = record ? [record] : selectedAlarms;

    let isEvent = true;
    const eventIds = selectedRows?.filter(item => item.eventId).map(item => item.eventId);

    const areAllTicketsSame = eventIds => {
      if (eventIds.length < selectedRows.length) {
        if (eventIds.length !== 0) {
          return false;
        } else {
          isEvent = false;
          return areAllTicketsSame(selectedRows.filter(item => item.changeId));
        }
      } else {
        let same = true;

        for (let i = 0; i < eventIds.length; i++) {
          if (eventIds[i] !== eventIds[0]) {
            same = false;
          }
        }
        return same;
      }
    };
    if (areAllTicketsSame(eventIds)) {
      const alarmIds = record ? [record.id] : selectedAlarmIds;
      const reason = isEvent ? 'EVENT' : 'CHANGE';
      const descriptions = isEvent ? '关联事件' : '关联变更';
      if (onOk) {
        onOk({
          alarmIds,
          reason,
          descriptions,
        });
      } else {
        dispatch(
          closeAlarmsAction({
            alarmIds,
            reason,
            descriptions,
            callback: error => {
              if (!error) {
                message.success('下盯屏成功');
                onRemoveCallBack && onRemoveCallBack();
              } else {
                message.error(error.message);
              }
            },
          })
        );
      }
    } else {
      message.error('勾选的数据中关联的事件或变更单不一致，无法批量下盯屏，请重选。');
      return;
    }
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };
  const onSelectChange = (selectedRowKeys: React.Key[], selectedRows: Event[] | Change[]) => {
    setSelectedRowKeys([...selectedRowKeys]);
  };

  // 分页数据
  const [pagination, setPagination] = useState({ pageNum: 1, pageSize: 10 });
  const handleSearch = (pageNum?: number, pageSize?: number) => {
    setPagination({ pageNum: pageNum || 1, pageSize: pageSize || 10 });
    fetchAssociatePage(pageNum || 1, pageSize || 10);
  };

  const pageHandler = (_page?: number, _pageSize?: number) => {
    handleSearch(_page, _pageSize);
  };

  const fetchListByFilterParams = () => {
    fetchAssociatePage(1, pageSize);
  };
  const fetchAssociatePage = async (pageNum: number, pageSize: number) => {
    setLoading(true);
    if (reasonType === 'EVENT') {
      const { data, error } = await fetchEvents({
        pageNum,
        pageSize,
        eventId: filterValue.id ? Number.parseInt(filterValue.id.toString()) : undefined,
      });
      if (error) {
        message.error(error.message);
        return;
      }
      setPageNum(pageNum);
      setPageSize(pageSize);
      setTotal(data.total);
      setEventList(data.data);
      setLoading(false);
    } else {
      const { data, error } = await fetchChanges({
        pageNum,
        pageSize,
        changeOrderId: filterValue?.id?.toString(),
        orderByCondition: 'gmtModified',
        title: filterValue.title,
        statusList: filterValue.status
          ? [filterValue.status]
          : ['CHANGING', 'IN_SUMMARY', 'WAITING_CLOSE', 'FINISH'],
      });
      if (error) {
        message.error(error.message);
        return;
      }
      setPageNum(pageNum);
      setPageSize(pageSize);
      setTotal(data.total);
      setChangeList(data.data);

      setLoading(false);
    }
  };

  const onFinish = () => {};
  const onFinishFailed = () => {};
  const alarms = record ? [record] : selectedAlarms;

  const associateAlarmDisable = alarms?.length
    ? !alarms.every(item => ['CONFIRMED', 'PROCESS'].includes(item.lifecycleState.code))
    : true;

  return (
    <span
      onClick={event => {
        event.stopPropagation();
        event.nativeEvent.stopImmediatePropagation();
      }}
    >
      {alarms?.filter(item => !!item.eventId || !!item.changeId)?.length &&
      !isRemovedByClickOnce ? (
        <Popconfirm
          title="确定要将选中的告警下盯屏吗？"
          placement="bottom"
          onConfirm={() => {
            onBatchRemove();
            return Promise.resolve(true);
          }}
        >
          {mode === 'icon' ? (
            <Tooltip title="下盯屏">
              <CheckCircleOutlined
                style={{ color: `var(--${prefixCls}-primary-color)`, fontSize: 16 }}
                disabled={associateAlarmDisable}
              />
            </Tooltip>
          ) : (
            <Button
              type={buttonType}
              compact={buttonType === 'link'}
              disabled={associateAlarmDisable}
            >
              下盯屏
            </Button>
          )}
        </Popconfirm>
      ) : (
        <>
          <Modal
            bodyStyle={{ maxHeight: '450px', overflow: 'hidden auto' }}
            width={960}
            title={record?.message}
            closable={false}
            open={isModalVisible}
            onOk={handleOk}
            onCancel={handleCancel}
          >
            <Form
              form={form}
              name="basic"
              labelCol={{ span: 4 }}
              wrapperCol={{ span: 20 }}
              initialValues={{ remember: true }}
              onFinish={onFinish}
              onFinishFailed={onFinishFailed}
              autoComplete="off"
            >
              <Form.Item
                label="告警原因"
                name="alarmReason"
                rules={[{ required: true, message: '请选择告警原因类型' }]}
              >
                <Select
                  style={{ width: '200px' }}
                  onChange={value => {
                    setReasonType(value);
                  }}
                >
                  {reasonTypes.map((item: Metadata) => (
                    <Option value={item.code} key={item.id}>
                      {item.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
              {['CHANGE', 'EVENT'].includes(reasonType) && (
                <Form.Item label={reasonType === 'CHANGE' ? '关联变更' : '关联事件'} name="orderNo">
                  <Space direction="vertical" size={16} style={{ width: '100%' }}>
                    <Space size={16}>
                      {reasonType === 'CHANGE' ? (
                        <Input
                          value={filterValue?.id?.toString()}
                          placeholder="搜索变更单号"
                          onChange={e => {
                            setFilterValue({ ...filterValue, id: e.target.value });
                          }}
                          allowClear
                          onPressEnter={fetchListByFilterParams}
                          style={{ width: 200 }}
                        />
                      ) : (
                        <Input
                          value={filterValue?.id?.toString()}
                          placeholder="搜索事件ID"
                          onChange={e => {
                            setFilterValue({ ...filterValue, id: e.target.value });
                          }}
                          onPressEnter={fetchListByFilterParams}
                          allowClear
                          style={{ width: 200 }}
                        />
                      )}
                      {reasonType === 'CHANGE' && (
                        <Input
                          value={filterValue?.title}
                          placeholder="搜索变更标题"
                          onChange={e => {
                            setFilterValue({ ...filterValue, title: e.target.value });
                          }}
                          onPressEnter={fetchListByFilterParams}
                          allowClear
                          style={{ width: 200 }}
                        />
                      )}

                      {reasonType === 'CHANGE' && (
                        <Select
                          onChange={value => {
                            setFilterValue({ ...filterValue, status: value });
                          }}
                          placeholder="搜索变更状态"
                          style={{ width: 200 }}
                          loading={selectLoading}
                          allowClear
                        >
                          <Option value="CHANGING" key={0}>
                            变更中
                          </Option>
                          <Option value="IN_SUMMARY" key={1}>
                            总结
                          </Option>
                          <Option value="WAITING_CLOSE" key={2}>
                            待关闭
                          </Option>
                          <Option value="FINISH" key={3}>
                            结束
                          </Option>
                        </Select>
                      )}
                      <Button type="primary" onClick={fetchListByFilterParams}>
                        搜索
                      </Button>
                    </Space>

                    {reasonType === 'CHANGE' ? (
                      <ChangeTable
                        dataSource={changeList}
                        rowSelection={{
                          onChange: onSelectChange,
                          type: 'radio',
                        }}
                        pagination={{
                          total,
                          current: pagination.pageNum,
                          pageSize: pagination.pageSize,
                        }}
                        onChange={pagination => {
                          pageHandler(pagination.current, pagination.pageSize);
                        }}
                        loading={loading}
                      />
                    ) : (
                      <EventTable
                        dataSource={eventList}
                        rowSelection={{
                          onChange: onSelectChange,
                          type: 'radio',
                        }}
                        pagination={{
                          total,
                          current: pagination.pageNum,
                          pageSize: pagination.pageSize,
                        }}
                        onChange={pagination => {
                          pageHandler(pagination.current, pagination.pageSize);
                        }}
                        loading={loading}
                      />
                    )}
                  </Space>
                </Form.Item>
              )}

              {!['CHANGE', 'EVENT'].includes(reasonType) && (
                <Form.Item
                  label="原因描述"
                  name="removeDesc"
                  rules={[
                    { required: true, message: '请输入原因描述' },
                    {
                      validator: (_, value) => {
                        if (value?.length > 200) {
                          return Promise.reject('输入原因字数不能超过200');
                        } else {
                          return Promise.resolve();
                        }
                      },
                    },
                  ]}
                >
                  <TextArea style={{ width: '400px' }} />
                </Form.Item>
              )}
            </Form>
          </Modal>
          {isRemovedByClickOnce ? (
            <Button
              disabled={disabled}
              type={buttonType}
              compact
              onClick={event => {
                showModal(event);
              }}
            >
              一键下盯屏
            </Button>
          ) : mode === 'icon' ? (
            <Tooltip title="下盯屏">
              <CheckCircleOutlined
                style={{ color: `var(--${prefixCls}-primary-color)`, fontSize: 16 }}
                disabled={disabled}
                onClick={event => {
                  showModal(event);
                }}
              />
            </Tooltip>
          ) : (
            <Button
              disabled={disabled}
              type={buttonType}
              compact={buttonType === 'link'}
              onClick={event => {
                showModal(event);
              }}
            >
              下盯屏
            </Button>
          )}
        </>
      )}
    </span>
  );
};
