import React from 'react';
import { useSelector } from 'react-redux';

import type { AlarmJSON } from '@manyun/monitoring.model.alarm';
import type {
  AlarmsViewingState,
  ScopeVariant,
} from '@manyun/monitoring.state.alarms-monitoring-board';
import {
  getAlarmsMonitoringBoardLocales,
  selectPreferences,
} from '@manyun/monitoring.state.alarms-monitoring-board';
import { AlarmLevelText } from '@manyun/monitoring.ui.alarm-level-text';
import { AlarmLifecyStatusText } from '@manyun/monitoring.ui.alarm-lifecy-status-text';
import { AlarmStatusText } from '@manyun/monitoring.ui.alarm-status-text';
import { DeviceTypeText } from '@manyun/resource-hub.ui.device-type-text';

const { boards } = getAlarmsMonitoringBoardLocales();
const { yourInterests, byStates, byScopes, expecteds } = boards;

export type GetTitleProps = {
  code: string;
};

export function GetTitle({ code }: GetTitleProps) {
  const { layout } = useSelector(selectPreferences);
  if (layout === 'table') {
    return <></>;
  }
  if (code === 'your-interests') {
    return <>{yourInterests}</>;
  }
  if (code === 'expecteds') {
    return <>{expecteds}</>;
  }
  if (layout === 'board--by-states') {
    return <>{byStates[code as AlarmsViewingState]}</>;
  }
  if (layout === 'board--by-scopes') {
    return <>{byScopes[code as ScopeVariant]}</>;
  }
  if (layout === 'board--by-alarm-levels') {
    return <AlarmLevelText code={code.toString()} />;
  }
  return <></>;
}

export function AlarmTitle({
  alarmItem,
  columnsCodeList,
}: {
  alarmItem: AlarmJSON;
  columnsCodeList: string[];
}) {
  if (!alarmItem) {
    return <></>;
  }
  const {
    point: { validLimits },
    pointData: { snapshot },
  } = alarmItem;

  const status = validLimits && validLimits[Number(snapshot)];

  const title =
    alarmItem.point.dataType.code === 'AI' ? (
      <span style={{ fontSize: '16px' }}>
        <AlarmLevelText code={alarmItem.level} />{' '}
        {alarmItem.source.roomName ? `${alarmItem.source.roomName}_` : ''}
        <DeviceTypeText code={alarmItem.source.deviceType} />
        {alarmItem.point.name ? `_${alarmItem.point.name}` : ''}
        {alarmItem.cause.name ? `_${alarmItem.cause.name}` : ''}
        {alarmItem?.pointData.snapshot
          ? `_告警值:${alarmItem?.pointData.snapshot}${alarmItem.point.unit || ''}`
          : ''}{' '}
        {columnsCodeList.includes('lifecycleState') && (
          <AlarmLifecyStatusText code={alarmItem.lifecycleState.code} shape="__tag__" />
        )}
        {columnsCodeList.includes('alarmState') && (
          <AlarmStatusText code={alarmItem.state.code} shape="__tag__" />
        )}
      </span>
    ) : (
      <span style={{ fontSize: '16px' }}>
        <AlarmLevelText code={alarmItem.level} />{' '}
        {alarmItem.source.roomName ? `${alarmItem.source.roomName}_` : ''}
        <DeviceTypeText code={alarmItem.source.deviceType} />
        {alarmItem.point.name ? `_${alarmItem.point.name}` : ''}
        {`_${status}告警 `}{' '}
        {columnsCodeList.includes('lifecycleState') && (
          <AlarmLifecyStatusText code={alarmItem.lifecycleState.code} shape="__tag__" />
        )}
        {columnsCodeList.includes('alarmState') && (
          <AlarmStatusText code={alarmItem.state.code} shape="__tag__" />
        )}
      </span>
    );
  return <>{title}</>;
}
