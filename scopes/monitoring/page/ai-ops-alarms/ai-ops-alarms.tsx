import React, { useMemo, useRef, useState } from 'react';
import { useSelector } from 'react-redux';

import useDeepCompareEffect from 'use-deep-compare-effect';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Divider } from '@manyun/base-ui.ui.divider';
import type { ColumnType as EditColumnType } from '@manyun/base-ui.ui.edit-columns';
import { Form } from '@manyun/base-ui.ui.form';
import { FullScreen } from '@manyun/base-ui.ui.full-screen';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';

import { selectMe } from '@manyun/auth-hub.state.user';
import type { AiOpsAlarmJSON } from '@manyun/monitoring.model.ai-ops-alarm';
import { fetchAiAlarms } from '@manyun/monitoring.service.fetch-ai-alarms';
import type { SvcQuery as FetchAiAlarmsParams } from '@manyun/monitoring.service.fetch-ai-alarms';
import { updateAiAlarm } from '@manyun/monitoring.service.update-ai-alarm';
import { useSpaces } from '@manyun/resource-hub.ui.location-tree-select';

import { AiOpsAlarmDrawer } from './components/ai-ops-alarm-drawer';
import { AiOpsAlarmFacebackModal } from './components/ai-ops-alarm-faceback-modal';
import type { FilterForm } from './components/ai-ops-alarm-header';
import { AiOpsAlarmHeader, aiOpsAlarmHeaderContext } from './components/ai-ops-alarm-header';
import type { AiOpsAlarmTableProps, ColumnDataIndex } from './components/ai-ops-alarm-table';
import { AiOpsAlarmTable } from './components/ai-ops-alarm-table';

export function AiOpsAlarms() {
  const [form] = Form.useForm();

  const { idc } = useSelector(selectMe, (left, right) => left.idc === right.idc);

  const [loading, setLoading] = useState(false);

  const [aiOpsMonitoringList, setAiOpsAlarmList] = useState<AiOpsAlarmJSON[]>([]);

  const [aiOpsAlarm, setAiOpsAlarm] = useState<AiOpsAlarmJSON>();

  const [columnsConfig, setColumnsConfig] = useState<EditColumnType<AiOpsAlarmJSON>[]>();

  const [blockGuids, setBlockGuids] = useState<string[]>([]);

  const [pagination, setPagination] = useState<{
    pageNum: number;
    pageSize: number;
    total: number;
  }>({ pageNum: 1, pageSize: 10, total: 0 });

  const filterParamsRef = useRef<FilterForm>();

  const [modalOpen, setModalOpen] = useState(false);
  const [open, setOpen] = useState(false);

  const [{ treeSpaces }] = useSpaces({
    authorizedOnly: true,
    includeVirtualBlocks: false,
    nodeTypes: ['IDC', 'BLOCK'],
    idc: idc ?? undefined,
  });

  useDeepCompareEffect(() => {
    setBlockGuids(treeSpaces.map(item => item.value));
  }, [treeSpaces]);

  const getAiAlarms = async (params: { pageSize: number; pageNum: number }) => {
    setLoading(true);

    const positions = filterParamsRef.current?.roomGuids?.length
      ? filterParamsRef.current.roomGuids
      : blockGuids;

    const aiAlarmsParams: FetchAiAlarmsParams = {
      pageNum: params.pageNum,
      pageSize: params.pageSize,
      positions,
      alarmType: filterParamsRef.current?.alarmType,
      alarmLevel: filterParamsRef.current?.alarmLevel,
      triggerStatus: filterParamsRef.current?.triggerStatus,
      feedback: filterParamsRef.current?.feedback,
      deviceType: filterParamsRef.current?.deviceType,
      alarmCondition: filterParamsRef.current?.alarmCondition,
      operatorId: filterParamsRef.current?.operatorId,
      triggerStartTime: filterParamsRef.current?.triggerStartTime,
      triggerEndTime: filterParamsRef.current?.triggerEndTime,
      recoverStartTime: filterParamsRef.current?.recoverStartTime,
      recoverEndTime: filterParamsRef.current?.recoverEndTime,
    };

    const { data, error } = await fetchAiAlarms(aiAlarmsParams);
    setLoading(false);

    if (!error) {
      setAiOpsAlarmList(data.data);
      setPagination({ pageNum: params.pageNum, pageSize: params.pageSize, total: data.total });
    } else {
      message.error(error.message);
    }
  };

  useDeepCompareMemo(() => {
    if (blockGuids.length) {
      getAiAlarms({
        pageSize: pagination.pageSize,
        pageNum: pagination.pageNum,
      });
    }
  }, [blockGuids]);

  const onChangePage = (pageNum: number, pageSize: number) => {
    getAiAlarms({
      pageNum,
      pageSize,
    });
  };

  const onFilterFormChange = (filterForm: FilterForm) => {
    filterParamsRef.current = filterForm;
    getAiAlarms({
      pageSize: pagination.pageSize,
      pageNum: 1,
    });
  };

  const onHandfeedbackClick = async () => {
    try {
      const values = await form.validateFields();
      const { feedback, feedbackDesc } = values;
      if (aiOpsAlarm) {
        const { error } = await updateAiAlarm({
          id: aiOpsAlarm.id,
          feedback,
          feedbackDesc,
        });
        if (!error) {
          message.success('反馈成功！');
          getAiAlarms({
            pageSize: pagination.pageSize,
            pageNum: 1,
          });
        } else {
          message.error(error.message);
        }
      }
      setModalOpen(false);
    } catch (error) {
      return;
    }
  };

  const operation: AiOpsAlarmTableProps['operation'] = useMemo(() => {
    return {
      title: '操作',
      fixed: 'right',
      render: (_, record: AiOpsAlarmJSON) => {
        return (
          <Space split={<Divider type="vertical" />} size={0}>
            <Button
              type="link"
              compact
              onClick={() => {
                setOpen(true);
                setAiOpsAlarm(record);
              }}
            >
              详细
            </Button>
            <Button
              type="link"
              compact
              onClick={() => {
                setModalOpen(true);
                setAiOpsAlarm(record);
                form.setFieldsValue({
                  feedback: record.feedback.code,
                  feedbackDesc: record.feedback.desc,
                });
              }}
            >
              反馈
            </Button>
          </Space>
        );
      },
    };
  }, [form]);

  return (
    <FullScreen>
      <Space style={{ display: 'flex' }} direction="vertical" size="middle">
        <aiOpsAlarmHeaderContext.Provider
          value={{
            blockGuids,
            onFilterFormChange,
            onBlockChange: blockGuids => setBlockGuids(blockGuids),
            onSetColumnsConfig: columnsConfig => setColumnsConfig(columnsConfig),
          }}
        >
          <AiOpsAlarmHeader idc={idc!} />
        </aiOpsAlarmHeaderContext.Provider>

        <Card>
          <AiOpsAlarmTable
            loading={loading}
            dataSource={aiOpsMonitoringList}
            dataIndexs={
              columnsConfig &&
              columnsConfig.filter(item => item.show).map(item => item.dataIndex as ColumnDataIndex)
            }
            operation={operation}
            pagination={{
              total: pagination.total,
              current: pagination.pageNum,
              pageSize: pagination.pageSize,
              onChange: onChangePage,
            }}
          />
        </Card>
      </Space>
      {aiOpsAlarm && (
        <AiOpsAlarmFacebackModal
          open={modalOpen}
          aiOpsAlarm={aiOpsAlarm}
          form={form}
          onClose={() => setModalOpen(false)}
          onHandfeedbackClick={onHandfeedbackClick}
        />
      )}
      {aiOpsAlarm && (
        <AiOpsAlarmDrawer
          aiOpsMonitoringItem={aiOpsAlarm}
          open={open}
          onClose={() => setOpen(false)}
        />
      )}
    </FullScreen>
  );
}
