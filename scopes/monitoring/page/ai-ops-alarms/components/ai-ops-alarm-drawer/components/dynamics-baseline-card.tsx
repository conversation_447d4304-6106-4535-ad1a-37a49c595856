import type { CSSProperties } from 'react';
import React from 'react';

import { Card } from '@manyun/base-ui.ui.card';

import { AiOpsLine } from '@manyun/monitoring.datav.ai-ops-line';
import type { AiOpsAlarmDetail } from '@manyun/monitoring.service.fetch-ai-alarm-info';
import { AddPointToDiffTool } from '@manyun/monitoring.ui.add-point-to-diff-tool';

export type DynamicsBaselineCardProps = {
  style?: CSSProperties;
  detail: AiOpsAlarmDetail;
};

export function DynamicsBaselineCard({ style, detail }: DynamicsBaselineCardProps) {
  return (
    <Card
      style={style}
      title={detail.source.pointName}
      extra={
        <AddPointToDiffTool
          pointGuids={[
            { deviceGuid: detail.source.deviceGuid, pointCode: detail.source.pointCode },
          ]}
        />
      }
    >
      <AiOpsLine
        idc={detail.source.idcTag}
        pointCode={detail.source.pointCode}
        deviceGuid={detail.source.deviceGuid}
        scenesId={detail.scenesId}
        unit={detail.unit}
      />
    </Card>
  );
}
