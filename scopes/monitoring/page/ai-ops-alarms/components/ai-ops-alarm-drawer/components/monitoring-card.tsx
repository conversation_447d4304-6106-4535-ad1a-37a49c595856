import type { CSSProperties, ReactNode } from 'react';
import React, { useMemo } from 'react';
import { Link } from 'react-router-dom';

import { Badge } from '@manyun/base-ui.ui.badge';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Space } from '@manyun/base-ui.ui.space';

import { generateSpaceOrDeviceRoutePath } from '@manyun/monitoring.route.monitoring-routes';
import type { AiOpsAlarmDetail } from '@manyun/monitoring.service.fetch-ai-alarm-info';
import { AlarmLevelText } from '@manyun/monitoring.ui.alarm-level-text';
import { FeekbackResultsText } from '@manyun/monitoring.ui.feekback-results-text';
import { RoomTypeText } from '@manyun/resource-hub.ui.room-type-text';
import { getSpaceGuid } from '@manyun/resource-hub.util.space-guid';

export type MonitoringCardProps = {
  style?: CSSProperties;
  detail: AiOpsAlarmDetail;
};

type Description = {
  label: string | null;
  text: ReactNode;
};

export function MonitoringCard({ style, detail }: MonitoringCardProps) {
  const monitoringObjects: Description[] = useMemo(() => {
    return [
      {
        label: '监测对象',
        text: (
          <Link
            to={generateSpaceOrDeviceRoutePath({
              guid: detail.source.deviceGuid,
            })}
            target="_blank"
          >
            {detail.source.deviceName}
          </Link>
        ),
      },
      {
        label: '机房楼栋',
        text: getSpaceGuid(detail.source.idcTag, detail.source.blockTag),
      },
      {
        label: '包间位置',
        text: (
          <Link
            to={generateSpaceOrDeviceRoutePath({
              guid: getSpaceGuid(
                detail.source.idcTag,
                detail.source.blockTag,
                detail.source.roomTag
              )!,
              type: 'ROOM',
            })}
            target="_blank"
            onClick={e => e.stopPropagation()}
          >
            {`${detail.source.roomTag} `}
            <RoomTypeText code={detail.roomType} />
          </Link>
        ),
      },
    ];
  }, [
    detail.roomType,
    detail.source.blockTag,
    detail.source.deviceGuid,
    detail.source.deviceName,
    detail.source.idcTag,
    detail.source.roomTag,
  ]);

  const monitoringRules = useMemo(() => {
    return [
      {
        label: '告警类型',
        text: detail.alarmType.name,
      },
      {
        label: '告警级别',
        text: <AlarmLevelText code={detail.alarmLevel} />,
      },
      {
        label: '监测场景',
        text: detail.scenesName,
      },
      {
        label: '监测名称',
        text: detail.scenesType.name,
      },
      {
        label: '监测模型',
        text: detail.alarmCondition.name,
      },
    ];
  }, [
    detail.alarmCondition.name,
    detail.alarmLevel,
    detail.alarmType.name,
    detail.scenesName,
    detail.scenesType.name,
  ]);

  const notificationFeedbacks = useMemo(() => {
    return [
      {
        label: '通报渠道',
        text: detail.notifyWay.name,
      },
      {
        label: '反馈结果',
        text:
          detail.feedback.code !== null ? (
            <FeekbackResultsText code={detail.feedback.code} />
          ) : (
            '--'
          ),
      },
      {
        label: '反馈备注',
        text: detail.feedback.desc,
      },
    ];
  }, [detail.feedback.code, detail.feedback.desc, detail.notifyWay.name]);

  return (
    <Space style={style} direction="vertical">
      <Descriptions size="middle" bordered>
        <Descriptions.Item span={6} labelStyle={{ display: 'none' }}>
          <Badge status="processing" text="监测对象" />
        </Descriptions.Item>
        {monitoringObjects.map(item => {
          return (
            <Descriptions.Item
              key={item.label}
              label={item.label}
              span={3}
              labelStyle={{ width: 110 }}
            >
              {item.text}
            </Descriptions.Item>
          );
        })}
      </Descriptions>
      <Descriptions size="middle" bordered>
        <Descriptions.Item span={6} labelStyle={{ display: 'none' }}>
          <Badge status="processing" text="监测规则" />
        </Descriptions.Item>
        {monitoringRules.map(item => {
          return (
            <Descriptions.Item
              key={item.label}
              label={item.label}
              span={3}
              labelStyle={{ width: 110 }}
            >
              {item.text}
            </Descriptions.Item>
          );
        })}
      </Descriptions>
      <Descriptions size="middle" bordered>
        <Descriptions.Item span={6} labelStyle={{ display: 'none' }}>
          <Badge status="processing" text="通报反馈" />
        </Descriptions.Item>
        {notificationFeedbacks.map(item => {
          return (
            <Descriptions.Item
              key={item.label}
              label={item.label}
              span={3}
              labelStyle={{ width: 110 }}
            >
              {item.text}
            </Descriptions.Item>
          );
        })}
      </Descriptions>
    </Space>
  );
}
