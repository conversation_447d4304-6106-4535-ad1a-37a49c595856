import React, { useEffect, useMemo, useState } from 'react';

import { AlertOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';

import { Card } from '@manyun/base-ui.ui.card';
import { Space } from '@manyun/base-ui.ui.space';
import { Steps } from '@manyun/base-ui.ui.steps';
import { Typography } from '@manyun/base-ui.ui.typography';

import { getformatInterval } from '@manyun/monitoring.page.alarm-staring-screen';
import type { AiOpsAlarmDetail } from '@manyun/monitoring.service.fetch-ai-alarm-info';
import { AlarmStatusText } from '@manyun/monitoring.ui.alarm-status-text';

import styles from '../ai-ops-alarm-drawer.module.less';

export type ProgressCardProps = {
  style?: React.CSSProperties;
  detail: AiOpsAlarmDetail;
};

export function ProgressCard({ detail, style }: ProgressCardProps) {
  const [unAcceptedTime, setUnAcceptedTime] = useState<number>(0);

  const alarmTime = useMemo(() => {
    let alarmSeconds: number;
    if (detail.triggerStatus.code === 'RECOVER') {
      alarmSeconds = dayjs(detail.recoverAt).diff(detail.createAt, 'seconds');
    } else {
      alarmSeconds = unAcceptedTime;
    }
    return getformatInterval(alarmSeconds);
  }, [detail.createAt, detail.recoverAt, detail.triggerStatus.code, unAcceptedTime]);

  useEffect(() => {
    const { createAt } = detail;
    let unAcceptedTime = dayjs().diff(createAt, 'seconds');

    const timer = window.setInterval(function () {
      unAcceptedTime = unAcceptedTime + 1;

      setUnAcceptedTime(unAcceptedTime);
    }, 1000);

    return () => {
      clearInterval(timer);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const alarmProgress = useMemo(() => {
    return (
      <Steps
        direction="vertical"
        current={detail.triggerStatus.code === 'RECOVER' ? 3 : 1}
        size="small"
      >
        <Steps.Step
          title="告警开始"
          description={dayjs(detail.createAt).format('YYYY-MM-DD HH:mm:ss')}
        />
        <Steps.Step
          title="最新告警时间"
          description={dayjs(detail.triggerAt).format('YYYY-MM-DD HH:mm:ss')}
        />

        <Steps.Step
          title="告警已恢复"
          description={
            detail.triggerStatus.code === 'RECOVER'
              ? dayjs(detail.recoverAt).format('YYYY-MM-DD HH:mm:ss')
              : null
          }
        />
      </Steps>
    );
  }, [detail.createAt, detail.recoverAt, detail.triggerAt, detail.triggerStatus.code]);

  //受理进度
  const feedbackProgress = useMemo(() => {
    if (detail.feedback.code !== null) {
      return (
        <Steps direction="vertical" current={1} size="small">
          <Steps.Step
            title={
              <Space size={6}>
                <Typography.Text>已反馈</Typography.Text>
                <Typography.Text type="secondary">{detail.operator.name ?? '系统'}</Typography.Text>
              </Space>
            }
            description={dayjs(detail.feedbackAt).format('YYYY-MM-DD HH:mm:ss')}
          />
        </Steps>
      );
    } else {
      return <Typography.Text style={{ paddingLeft: 40 }}>未反馈</Typography.Text>;
    }
  }, [detail.feedback.code, detail.feedbackAt, detail.operator.name]);

  return (
    <Card style={style}>
      <Space size={32} direction="vertical" style={{ width: '100%' }}>
        <Space>
          <AlertOutlined
            style={{ fontSize: 26 }}
            className={
              detail.triggerStatus.code === 'RECOVER' ? styles.secondaryDark : styles.errorColor
            }
          />
          <Typography.Text>{alarmTime}</Typography.Text>
          <AlarmStatusText code={detail.triggerStatus.code} />
        </Space>
        <Typography.Text type="secondary">告警进度</Typography.Text>
        {alarmProgress}
        <Typography.Text type="secondary">反馈进度</Typography.Text>
        {feedbackProgress}
      </Space>
    </Card>
  );
}
