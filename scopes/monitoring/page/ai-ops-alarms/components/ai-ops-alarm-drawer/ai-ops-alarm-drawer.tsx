import React, { useEffect, useState } from 'react';

import { Drawer } from '@manyun/base-ui.ui.drawer';
import { Spin } from '@manyun/base-ui.ui.spin';

import type { AiOpsAlarmJSON } from '@manyun/monitoring.model.ai-ops-alarm';
import type { AiOpsAlarmDetail } from '@manyun/monitoring.service.fetch-ai-alarm-info';
import { fetchAiAlarmInfo } from '@manyun/monitoring.service.fetch-ai-alarm-info';

import { DynamicsBaselineCard } from './components/dynamics-baseline-card';
import { MonitoringCard } from './components/monitoring-card';
import { ProgressCard } from './components/progress-card';

export type AiOpsAlarmDrawerProps = {
  aiOpsMonitoringItem: AiOpsAlarmJSON;
  open: boolean;
  onClose: () => void;
};

export function AiOpsAlarmDrawer({ aiOpsMonitoringItem, open, onClose }: AiOpsAlarmDrawerProps) {
  const [detail, setDetail] = useState<AiOpsAlarmDetail>();
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    if (!open) {
      return;
    }
    (async () => {
      setLoading(true);
      const { data, error } = await fetchAiAlarmInfo({ id: aiOpsMonitoringItem.id });
      setLoading(false);
      if (!error && data !== null) {
        setDetail(data);
      }
    })();
  }, [aiOpsMonitoringItem.id, open]);

  return (
    <Drawer
      destroyOnClose
      title={detail?.alarmDesc ?? ''}
      placement="right"
      open={open}
      contentWrapperStyle={{ width: '85%', minWidth: 1440, maxWidth: 1920 }}
      afterOpenChange={open => {
        if (!open) {
          setDetail(undefined);
        }
      }}
      onClose={onClose}
    >
      <Spin spinning={loading}>
        {detail && (
          <div
            style={{ display: 'flex', gap: 8, height: 'calc(100vh - 103px)', overflowY: 'hidden' }}
          >
            <ProgressCard style={{ width: 252, height: '100%' }} detail={detail} />
            <div
              style={{
                flex: 1,
                display: 'flex',
                gap: 8,
                flexDirection: 'column',
              }}
            >
              <DynamicsBaselineCard style={{ width: '100%' }} detail={detail} />
            </div>
            <MonitoringCard
              style={{ width: 326, height: '100%', overflowY: 'auto' }}
              detail={detail}
            />
          </div>
        )}
      </Spin>
    </Drawer>
  );
}
