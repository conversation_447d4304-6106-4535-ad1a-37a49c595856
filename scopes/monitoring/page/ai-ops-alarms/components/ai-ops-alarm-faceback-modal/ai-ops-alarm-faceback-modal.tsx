import React from 'react';

import type { FormInstance } from '@manyun/base-ui.ui.form';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Radio } from '@manyun/base-ui.ui.radio';

import type { AiOpsAlarmJSON } from '@manyun/monitoring.model.ai-ops-alarm';
import { Feedback, getAiOpsAlarmLocales } from '@manyun/monitoring.model.ai-ops-alarm';

type FeedbackFormValues = {
  feedback: Feedback;
  feedbackDesc: string;
};
export type AiOpsAlarmFacebackModalProps = {
  open: boolean;
  aiOpsAlarm: AiOpsAlarmJSON;
  form: FormInstance<FeedbackFormValues>;
  onClose: () => void;
  onHandfeedbackClick: () => void;
};

export function AiOpsAlarmFacebackModal({
  open,
  form,
  aiOpsAlarm,
  onClose,
  onHandfeedbackClick,
}: AiOpsAlarmFacebackModalProps) {
  const alarmLocales = getAiOpsAlarmLocales();

  return (
    <Modal title={aiOpsAlarm.alarmDesc} open={open} onOk={onHandfeedbackClick} onCancel={onClose}>
      <Form form={form}>
        <Form.Item label="受理反馈" name="feedback">
          <Radio.Group
            options={[
              { label: alarmLocales.feedback[Feedback.On], value: Feedback.On },
              { label: alarmLocales.feedback[Feedback.Off], value: Feedback.Off },
            ]}
          />
        </Form.Item>
        <Form.Item label="备注" name="feedbackDesc">
          <Input.TextArea rows={2} maxLength={50} />
        </Form.Item>
      </Form>
    </Modal>
  );
}
