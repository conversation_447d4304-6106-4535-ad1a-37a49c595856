import { createContext } from 'react';

import type { ColumnType as EditColumnType } from '@manyun/base-ui.ui.edit-columns';

import type { AiOpsAlarmJSON } from '@manyun/monitoring.model.ai-ops-alarm';

import type { FilterForm } from './ai-ops-alarm-header';

export type AiOpsAlarmHeaderContextProps = {
  blockGuids: string[];
  onBlockChange: (value: string[]) => void;
  onSetColumnsConfig: (value: EditColumnType<AiOpsAlarmJSON>[]) => void;
  onFilterFormChange: (value: FilterForm) => void;
};

const initialValue: AiOpsAlarmHeaderContextProps = {
  blockGuids: [],
  onBlockChange: () => {},
  onSetColumnsConfig: () => {},
  onFilterFormChange: () => {},
};

export const aiOpsAlarmHeaderContext = createContext<AiOpsAlarmHeaderContextProps>(initialValue);
