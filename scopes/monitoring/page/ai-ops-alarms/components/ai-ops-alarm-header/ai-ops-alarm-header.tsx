import React, { useContext } from 'react';

import { EnvironmentOutlined } from '@ant-design/icons';

import { FullScreen } from '@manyun/base-ui.ui.full-screen';
import { Space } from '@manyun/base-ui.ui.space';

import { EditColumns } from '@manyun/dc-brain.ui.edit-columns';
import { getAiOpsAlarmLocales } from '@manyun/monitoring.model.ai-ops-alarm';
import type { SvcQuery as FetchAiAlarmsParams } from '@manyun/monitoring.service.fetch-ai-alarms';
import type { FilterFormParams } from '@manyun/monitoring.ui.alarms-filter-form';
import { AlarmsFilterForm } from '@manyun/monitoring.ui.alarms-filter-form';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';

import { getDefaultColumnsConfig } from '../ai-ops-alarm-table';
import { aiOpsAlarmHeaderContext } from './ai-ops-alarm-header.context';
import styles from './ai-ops-alarm-header.module.less';

export type FilterForm = Omit<FetchAiAlarmsParams, 'pageSize' | 'pageNum' | 'positions'> & {
  roomGuids: string[] | undefined;
};

export type AiOpsAlarmHeaderProps = {
  idc: string;
};

export function AiOpsAlarmHeader({ idc }: AiOpsAlarmHeaderProps) {
  const locales = getAiOpsAlarmLocales();

  const contextValue = useContext(aiOpsAlarmHeaderContext);

  const onFilterChange = (params: FilterFormParams) => {
    const {
      alarmTypes,
      alarmLevels,
      monitoringStatus,
      feekbackResults,
      roomGuids,
      deviceTypes,
      monitoringModel,
      alarmsTriggeredAtTimeRange,
      alarmsRecoveredAtTimeRange,
    } = params;

    const filterForm: FilterForm = {
      roomGuids,
      alarmType: alarmTypes,
      alarmLevel: alarmLevels,
      triggerStatus: monitoringStatus,
      feedback: feekbackResults,
      deviceType: deviceTypes,
      alarmCondition: monitoringModel,
      triggerStartTime: alarmsTriggeredAtTimeRange?.length
        ? alarmsTriggeredAtTimeRange[0]
        : undefined,
      triggerEndTime: alarmsTriggeredAtTimeRange?.length
        ? alarmsTriggeredAtTimeRange[1]
        : undefined,
      recoverStartTime: alarmsRecoveredAtTimeRange?.length
        ? alarmsRecoveredAtTimeRange[0]
        : undefined,
      recoverEndTime: alarmsRecoveredAtTimeRange?.length
        ? alarmsRecoveredAtTimeRange[1]
        : undefined,
    };
    contextValue.onFilterFormChange(filterForm);
  };

  return (
    <div className={styles.header}>
      <Space size={16}>
        AI-OPS监测
        <EnvironmentOutlined />
        <LocationTreeSelect
          style={{ width: 200 }}
          allowClear
          multiple
          maxTagCount="responsive"
          treeCheckable="true"
          size="small"
          authorizedOnly
          idc={idc}
          value={contextValue.blockGuids}
          onChange={value => contextValue.onBlockChange(value as string[])}
        />
      </Space>
      <Space size={16}>
        <AlarmsFilterForm
          idc={idc}
          showFormItems={[
            'alarm-types',
            'alarm-levels',
            'monitoring-status',
            'feekback-results',
            'room-guids',
            'device-types',
            'monitoring-model',
            'alarms-triggered-at-time-range',
            'alarms-recovered-at-time-range',
          ]}
          onChange={onFilterChange}
        />
        <EditColumns
          uniqKey="AI_OPS_MONITORING_TABLE_COLUMNS"
          defaultValue={getDefaultColumnsConfig(locales)}
          allowSetAsFixed={false}
          listsHeight={500}
          onChange={value => {
            contextValue.onSetColumnsConfig(value);
          }}
        />
        <FullScreen.Icon />
      </Space>
    </div>
  );
}
