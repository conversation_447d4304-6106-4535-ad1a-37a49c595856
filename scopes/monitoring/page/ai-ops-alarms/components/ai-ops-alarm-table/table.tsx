import React from 'react';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType, TableProps } from '@manyun/base-ui.ui.table';

import type { AiOpsAlarmJSON, AiOpsAlarmLocales } from '@manyun/monitoring.model.ai-ops-alarm';
import { AiOpsAlarm, getAiOpsAlarmLocales } from '@manyun/monitoring.model.ai-ops-alarm';
import { AlarmLevelText } from '@manyun/monitoring.ui.alarm-level-text';
import { AlarmStatusText } from '@manyun/monitoring.ui.alarm-status-text';
import { FeekbackResultsText } from '@manyun/monitoring.ui.feekback-results-text';

const BLANK = '--';

const getAiOpsAlarmColumns = (locales: AiOpsAlarmLocales) => {
  const aiOpsMonitoringColumns: ColumnType<AiOpsAlarmJSON>[] = [
    {
      title: locales['scenesName'],
      dataIndex: 'scenesName',
      fixed: 'left',
    },
    {
      title: locales['alarmType'],
      dataIndex: 'alarmType',
      render: (_, record) => {
        return record.alarmType.name ?? BLANK;
      },
    },
    {
      title: locales['alarmLevel'],
      dataIndex: 'alarmLevel',
      render: alarmLevel => <AlarmLevelText code={alarmLevel} />,
    },
    {
      title: locales['source']['blockTag'],
      dataIndex: 'source.blockTag',
      render: (_, record) => {
        return record.source.blockTag ?? BLANK;
      },
    },
    {
      title: locales['source']['roomTag'],
      dataIndex: 'source.roomTag',
      render: (_, record) => {
        return record.source.roomTag ?? BLANK;
      },
    },
    {
      title: locales['source']['deviceName'],
      dataIndex: 'source.deviceName',
      render: (_, record) => {
        return record.source.deviceName ?? BLANK;
      },
    },
    {
      title: locales['source']['pointName'],
      dataIndex: 'source.pointName',
      render: (_, record) => {
        return record.source.pointName ?? BLANK;
      },
    },
    {
      title: locales['alarmCondition'],
      dataIndex: 'alarmCondition.name',
      render: (_, record) => {
        return record.alarmCondition.name ?? BLANK;
      },
    },
    {
      title: locales['triggerStatus']['__self'],
      dataIndex: 'triggerStatus',
      render: triggerStatus => {
        return <AlarmStatusText code={triggerStatus.code} />;
      },
    },
    {
      title: locales['createAt'],
      dataIndex: 'createAt',
      render: (_, record) => {
        return AiOpsAlarm.fromJSON(record).getFormattedcreateAtAt();
      },
    },
    {
      title: locales['recoverAt'],
      dataIndex: 'recoverAt',
      render: (_, record) => {
        return record.recoverAt ? AiOpsAlarm.fromJSON(record).getFormattedRecoverAt() : BLANK;
      },
    },
    {
      title: locales['feedback']['__self'],
      dataIndex: 'feedback',
      render: feedback => {
        if (feedback.code === null) {
          return BLANK;
        }
        return <FeekbackResultsText code={feedback.code} />;
      },
    },
  ];
  return aiOpsMonitoringColumns;
};

export const getDefaultColumnsConfig = (locales: AiOpsAlarmLocales) => {
  const defaultColumnsConfig: ColumnType<AiOpsAlarmJSON>[] = getAiOpsAlarmColumns(locales).map(
    item => {
      if (item.dataIndex === 'scenesName') {
        return {
          title: item.title,
          dataIndex: item.dataIndex,
          show: true,
          disable: true,
        };
      }
      return {
        title: item.title,
        dataIndex: item.dataIndex,
        show: true,
      };
    }
  );
  return defaultColumnsConfig;
};

export type ColumnDataIndex =
  | 'scenesName'
  | 'alarmType'
  | 'alarmLevel'
  | 'source.blockTag'
  | 'source.roomTag'
  | 'source.deviceName'
  | 'source.pointName'
  | 'alarmCondition'
  | 'triggerStatus'
  | 'triggerAt'
  | 'recoverAt'
  | 'feedback';

export type AiOpsAlarmTableProps = {
  dataIndexs?: ColumnDataIndex[];
  operation?: ColumnType<AiOpsAlarmJSON>;
} & Omit<TableProps<AiOpsAlarmJSON>, 'columns'>;

export function AiOpsAlarmTable({
  dataIndexs = [
    'scenesName',
    'alarmType',
    'alarmLevel',
    'source.blockTag',
    'source.roomTag',
    'source.deviceName',
    'source.pointName',
    'alarmCondition',
    'triggerStatus',
    'triggerAt',
    'recoverAt',
    'feedback',
  ],
  operation,
  ...rest
}: AiOpsAlarmTableProps) {
  const columns = useDeepCompareMemo(() => {
    const locales = getAiOpsAlarmLocales();

    const newColumns = dataIndexs
      .map(dataIndex => {
        return getAiOpsAlarmColumns(locales).find(item => item.dataIndex === dataIndex);
      })
      .filter((item): item is ColumnType<AiOpsAlarmJSON> => item !== undefined);

    if (operation) {
      return [...newColumns, operation];
    }
    return newColumns;
  }, [dataIndexs, operation]);

  return (
    <Table
      columns={columns}
      scroll={{ x: 'max-content' }}
      tableLayout="fixed"
      rowKey="id"
      {...rest}
    />
  );
}
