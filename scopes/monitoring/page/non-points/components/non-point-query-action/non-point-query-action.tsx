import React, { useCallback, useContext, useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import type { ColumnType } from '@manyun/dc-brain.ui.edit-columns';
import { EditColumns } from '@manyun/dc-brain.ui.edit-columns';
import type { NonPointJSON } from '@manyun/monitoring.model.point';
import { exportNonPoint } from '@manyun/monitoring.service.export-non-point';

import { NonPointContext } from '../../non-point-context';
import { NonPointMutator } from '../non-point-mutator';

export type NonPointQueryActionProps = {
  setSearchKeys: (value: string) => void;
  dataSource: NonPointJSON[];
  onReset: () => void;
  onColumnsChange: (columns: ColumnType<NonPointJSON>[]) => void;
  defaultColumnsConfig: ColumnType<NonPointJSON>[];
};

export function NonPointQueryAction({
  setSearchKeys,
  onReset,
  dataSource,
  onColumnsChange,
  defaultColumnsConfig,
}: NonPointQueryActionProps) {
  const [visible, setVisible] = useState(false);
  const [{ deviceType, blockGuid, productModel }] = useContext(NonPointContext);

  const handleFileExport = useCallback(async () => {
    if (!blockGuid) {
      return;
    }
    const { error, data } = await exportNonPoint({
      blockGuid,
      deviceType,
      productModel,
    });
    if (error) {
      message.error(error.message);
      return;
    }
    return data;
  }, [blockGuid, deviceType, productModel]);

  return (
    <div
      style={{
        display: 'flex',
        justifyContent: 'space-between',
      }}
    >
      <Space>
        <Button
          type="primary"
          disabled={!deviceType || !blockGuid || !productModel}
          onClick={() => {
            setVisible(true);
          }}
        >
          新建测点
        </Button>
        <Input.Search
          placeholder="请输入测点名称/ID"
          onChange={e => setSearchKeys(e.target.value)}
        />
      </Space>
      <Space>
        {blockGuid && deviceType && productModel && (
          <FileExport
            text=""
            filename="测点映射.xlsx"
            disabled={!dataSource.length}
            data={() => {
              return handleFileExport();
            }}
          />
        )}
        <EditColumns
          uniqKey="NonPointEditColumns"
          defaultValue={defaultColumnsConfig}
          listsHeight={500}
          onChange={(columns: ColumnType<NonPointJSON>[]) => {
            onColumnsChange(columns);
          }}
        />
      </Space>
      {deviceType && blockGuid && productModel && (
        <NonPointMutator
          title="新建测点"
          deviceType={deviceType}
          blockGuid={blockGuid}
          productModel={productModel}
          visible={visible}
          onClose={() => setVisible(false)}
          onReset={onReset}
        />
      )}
    </div>
  );
}
