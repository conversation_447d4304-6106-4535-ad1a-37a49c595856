import saveAs from 'file-saver';
import React, { useCallback, useContext, useMemo, useState } from 'react';
import { useHistory } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Divider } from '@manyun/base-ui.ui.divider';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { generateVersionManageDetailRoutePath } from '@manyun/dc-brain.route.admin-routes';
import { generateNonPointImportRoutePath } from '@manyun/monitoring.route.monitoring-routes';
import { exportNonPoint } from '@manyun/monitoring.service.export-non-point';
import { DeviceTypeTree } from '@manyun/resource-hub.ui.device-type-cascader';
import type { DeviceTreeNode } from '@manyun/resource-hub.ui.device-type-cascader/use-device';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

import { NonPointContext } from '../../non-point-context';
import styles from './non-point-action.module.less';

export type NonPointActionProps = {
  onDeviceTypeChange: (DataNode: DeviceTreeNode) => void;
  selectedKeys: string[];
};
export function NonPointAction({ onDeviceTypeChange, selectedKeys }: NonPointActionProps) {
  const [{ blockGuid }] = useContext(NonPointContext);
  const idc = getSpaceGuidMap(blockGuid!).idc!;

  const blockTag = useMemo(
    () => (blockGuid ? getSpaceGuidMap(blockGuid).block! : undefined),
    [blockGuid]
  );

  const [exportLoading, setExportLoading] = useState(false);
  const history = useHistory();
  const handleExportBlockPoint = useCallback(async () => {
    if (!blockGuid) {
      return;
    }
    const { error, data } = await exportNonPoint({
      blockGuid,
    });
    setExportLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    saveAs(data, `${blockGuid}_测点.xlsx`);
  }, [blockGuid]);

  return (
    <>
      <div className={styles.pointAction}>
        {idc && blockTag && (
          <DeviceTypeTree
            style={{ width: 200 }}
            treeStyle={{
              overflowY: 'auto',
              height: 'calc((var(--content-height) - 200px)',
            }}
            idcTag={idc}
            blockTag={blockTag}
            numbered
            dataType={['snDevice']}
            inputProps={{
              style: { marginBottom: 8 },
              placeholder: '输入搜索内容',
            }}
            selectedKeys={selectedKeys}
            onSelect={(_, info) => {
              const selectedNodes = info.selectedNodes;
              if (selectedNodes.length) {
                const node = selectedNodes[0] as DeviceTreeNode;
                onDeviceTypeChange(node);
              }
            }}
          />
        )}
      </div>
      <Divider style={{ margin: '16px 0' }} />
      <Space
        size={8}
        style={{
          margin: '0 16px',
          overflow: 'auto',
          width: '85%',
        }}
      >
        <Button target="_blank" type="primary" href={generateVersionManageDetailRoutePath({ idc })}>
          发布测点
        </Button>
        {blockGuid && (
          <Button onClick={() => history.push(generateNonPointImportRoutePath({ blockGuid }))}>
            导入测点
          </Button>
        )}

        <Button loading={exportLoading} disabled={!blockGuid} onClick={handleExportBlockPoint}>
          导出测点
        </Button>
      </Space>
    </>
  );
}
