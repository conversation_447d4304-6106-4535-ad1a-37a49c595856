import type { Key } from 'react';
import React, { useContext, useState } from 'react';
import { useShallowCompareEffect } from 'react-use';

import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Switch } from '@manyun/base-ui.ui.switch';
import type { ColumnType } from '@manyun/base-ui.ui.table';
import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';

import type { NonPointJSON } from '@manyun/monitoring.model.point';
import { deleteNonPoint } from '@manyun/monitoring.service.delete-non-point';

import { NonPointContext } from '../../non-point-context';
import { NonPointMapMutator } from '../non-point-map-mutator';
import { NonPointMutator } from '../non-point-mutator';

export type Pagination = { pageNum: number; pageSize: number };

export type NonPointTableProps = {
  dataSource: NonPointJSON[];
  loading: boolean;
  onReset: () => void;
  onSeletedItemChange: (ids: NonPointJSON[]) => void;
  selectedIds: Key[];
  columnsConfig: ColumnType<NonPointJSON>[];
};

export function NonPointTable({
  loading,
  dataSource,
  onReset,
  onSeletedItemChange,
  selectedIds,
  columnsConfig,
}: NonPointTableProps) {
  const [selectedRowKeys, setSelectedRowKeys] = useState<Key[]>([]);
  const [{ deviceType, blockGuid, productModel }] = useContext(NonPointContext);
  const [visible, setVisible] = useState(false);
  const [mapVisible, setMapVisible] = useState(false);
  const [nonPoint, setNonPoint] = useState<NonPointJSON>();
  const [delChannelPoint, setDelChannelPoint] = useState(false);

  useShallowCompareEffect(() => {
    setSelectedRowKeys(selectedIds);
  }, [selectedIds]);

  const handleOperateDelete = async (id: number) => {
    const { error } = await deleteNonPoint({
      ids: [id],
      delChannelPoint,
    });
    if (error) {
      message.error(error.message);
      return;
    }
    message.success('删除成功');
    onReset();
  };

  const columns: ColumnType<NonPointJSON>[] = columnsConfig.concat([
    {
      title: '操作',
      width: 160,
      dataIndex: 'action',
      fixed: 'right',
      render: (_, record) => {
        return (
          <Space>
            <Typography.Link
              onClick={() => {
                setVisible(true);
                setNonPoint(record);
              }}
            >
              编辑
            </Typography.Link>
            <Typography.Link
              onClick={() => {
                setMapVisible(true);
                setNonPoint(record);
              }}
            >
              修改映射
            </Typography.Link>
            <DeleteConfirm
              variant="popconfirm"
              targetName="测点"
              title={
                <Space direction="vertical">
                  <Typography.Text>
                    系统会同步删除已映射的测点，且不可恢复，请谨慎操作。
                  </Typography.Text>
                  <Typography.Text>
                    是否同步删除测点数据：
                    <Switch onChange={check => setDelChannelPoint(check)} />
                  </Typography.Text>
                </Space>
              }
              onOk={() => {
                handleOperateDelete(record.id);
                return Promise.resolve(true);
              }}
            >
              <Typography.Link>删除</Typography.Link>
            </DeleteConfirm>
          </Space>
        );
      },
    },
  ]);

  return (
    <div>
      <Table
        rowKey="id"
        scroll={{ x: 'max-content' }}
        tableLayout="fixed"
        dataSource={dataSource}
        columns={columns}
        rowSelection={{
          selectedRowKeys: selectedRowKeys,
          onChange: (selectedRowKeys, selectedRows) => {
            onSeletedItemChange(selectedRows);
            setSelectedRowKeys(selectedRowKeys);
          },
        }}
        loading={loading}
        pagination={false}
      />
      {deviceType && blockGuid && productModel && (
        <NonPointMutator
          deviceType={deviceType}
          blockGuid={blockGuid}
          productModel={productModel}
          title="编辑测点"
          visible={visible}
          nonPoint={nonPoint}
          onClose={() => setVisible(false)}
          onReset={onReset}
        />
      )}
      {deviceType && blockGuid && (
        <NonPointMapMutator
          deviceType={deviceType}
          blockGuid={blockGuid}
          title="修改映射"
          visible={mapVisible}
          nonPoint={nonPoint}
          onClose={() => setMapVisible(false)}
          onReset={onReset}
        />
      )}
    </div>
  );
}
