import React from 'react';

import type { ColumnType } from '@manyun/base-ui.ui.table';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import type { NonPointDataLocales, NonPointJSON } from '@manyun/monitoring.model.point';
import { PointCaptureStatusSwitch } from '@manyun/monitoring.ui.point-capture-status-select';

const columnName = (_: string, record: NonPointJSON) => (
  <Tooltip title={record.point.name}>{record.point.name}</Tooltip>
);
const columnCode = (_: string, record: NonPointJSON) => record.point.code;
const columnType = (_: string, record: NonPointJSON) => record.point.type;
const columnUnit = (_: string, record: NonPointJSON) => record.point.unit || '--';
const columnValidLimits = (_: string, record: NonPointJSON) => record.point.validLimits || '--';
const columnNormalName = (_: string, record: NonPointJSON) => record.normalPoint.name;
const columnNormalType = (_: string, record: NonPointJSON) => record.normalPoint.type;
const columnNormalUnit = (_: string, record: NonPointJSON) => record.normalPoint.unit || '--';
const columnNormalValidLimits = (_: string, record: NonPointJSON) =>
  record.normalPoint.validLimits || '--';

export const getColumns: (
  locales: NonPointDataLocales,
  callback?: (ids: number[] | undefined, enabled: boolean) => Promise<void>
) => ColumnType<NonPointJSON>[] = (locales, callback) => {
  const nonPoint = (_: string, record: NonPointJSON) =>
    record.nonPoint ? locales.pointType.NON : locales.pointType.NORMAL;

  return [
    {
      title: '测点分类',
      dataIndex: 'nonPoint',
      key: 'nonPoint',
      fixed: 'left',
      width: 100,
      show: true,
      render: nonPoint,
      stringify: nonPoint,
    },
    {
      title: '测点ID',
      dataIndex: 'code',
      fixed: 'left',
      width: 120,
      show: true,
      render: columnCode,
      stringify: columnCode,
      sorter: (a: NonPointJSON, b: NonPointJSON) => +a.point.code - +b.point.code,
      defaultSortOrder: 'ascend',
    },
    {
      title: '协议测点名称',
      dataIndex: 'name',
      ellipsis: { showTitle: false },
      width: 120,
      show: true,
      render: columnName,
      stringify: (_: string, record: NonPointJSON) => record.point.name,
    },
    {
      title: '协议测点类型',
      dataIndex: 'type',
      show: true,
      width: 140,
      render: columnType,
      stringify: columnType,
    },
    {
      title: '协议单位',
      dataIndex: 'unit',
      width: 100,
      show: true,
      render: columnUnit,
      stringify: columnUnit,
    },
    {
      title: '协议值含义',
      dataIndex: 'validLimits',
      width: 180,
      show: true,
      render: columnValidLimits,
      stringify: columnValidLimits,
    },
    {
      title: '标准测点名称',
      dataIndex: 'normalName',
      width: 120,
      show: true,
      render: columnNormalName,
      stringify: columnNormalName,
    },
    {
      title: '标准测点类型',
      dataIndex: 'normalPointType',
      show: true,
      width: 140,
      render: columnNormalType,
      stringify: columnNormalType,
    },
    {
      title: '标准单位',
      dataIndex: 'normalPointUnit',
      width: 100,
      show: true,
      render: columnNormalUnit,
      stringify: columnNormalUnit,
    },
    {
      title: '标准值含义',
      dataIndex: 'normalPointValidLimits',
      width: 180,
      show: true,
      render: columnNormalValidLimits,
      stringify: columnNormalValidLimits,
    },
    {
      title: '加工表达式',
      dataIndex: 'formula',
      width: 120,
      show: true,
    },
    { title: '非法值', dataIndex: 'invalidValues', width: 120, show: true },
    {
      title: '采集状态',
      dataIndex: 'capture',
      width: 100,
      show: true,
      render: (_: string, record: NonPointJSON) => {
        if (callback) {
          return (
            <PointCaptureStatusSwitch
              checked={record.capture}
              disabled={!record.nonPoint}
              onChange={(checked: boolean) => callback([record.id], checked)}
            />
          );
        }
        return null;
      },
      stringify: (_: string, record: NonPointJSON) => (record.capture ? '已采集' : '未采集'),
    },
  ];
};
