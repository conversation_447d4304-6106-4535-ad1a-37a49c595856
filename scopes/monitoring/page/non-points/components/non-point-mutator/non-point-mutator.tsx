import React, { use<PERSON>allback, useMemo, useState } from 'react';
import { useShallowCompareEffect } from 'react-use';

import { Button } from '@manyun/base-ui.ui.button';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import { Form, getNoSpaceNTabFormValidateRules } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import type { NonPointJSON } from '@manyun/monitoring.model.point';
import { getNonPointDataLocales } from '@manyun/monitoring.model.point';
import { createNonPoint } from '@manyun/monitoring.service.create-non-point';
import { updateNonPoint } from '@manyun/monitoring.service.update-non-point';
import { PointCaptureStatusSwitch } from '@manyun/monitoring.ui.point-capture-status-select';
import { PointCategorySelect } from '@manyun/monitoring.ui.point-category-select';
import { PointSelect } from '@manyun/resource-hub.ui.point-select';
import { PointTypeSelect } from '@manyun/resource-hub.ui.point-type-select';
import { SpecUnitSelect } from '@manyun/resource-hub.ui.spec-unit-select';

export type NonPointMutatorProps = {
  visible: boolean;
  onClose: () => void;
  title: string;
  onReset: () => void;
  deviceType: string;
  blockGuid: string;
  productModel: string;
  nonPoint?: NonPointJSON;
};

export function NonPointMutator({
  nonPoint,
  visible = false,
  onClose,
  title,
  onReset,
  deviceType,
  blockGuid,
  productModel,
}: NonPointMutatorProps) {
  const locales = getNonPointDataLocales();
  const isCreate = useMemo(() => !nonPoint?.id, [nonPoint?.id]);
  const [form] = Form.useForm();

  useShallowCompareEffect(() => {
    if (!isCreate && nonPoint && visible) {
      form.setFieldsValue({
        category: nonPoint.nonPoint ? 'NON_POINT' : 'NORMAL_POINT',
        name: nonPoint.point.name,
        dataType: nonPoint.point.type,
        unit: nonPoint.point.unit,
        validLimits: nonPoint.point.validLimits,
        formula: nonPoint.formula,
        capture: nonPoint.capture,
        nomarlId: !nonPoint.nonPoint ? nonPoint.point.code : undefined,
      });
    }
  }, [form, isCreate, nonPoint, visible]);

  const handleLeave = useCallback(() => {
    isCreate && form.resetFields();
    onClose();
  }, [form, isCreate, onClose]);

  const [submitLoading, setSubmitLoading] = useState<boolean>(false);
  const submit = useCallback(async () => {
    const formValues = await form.validateFields();

    const params = {
      ...formValues,
      capture: formValues.capture ?? true,
      nonPoint: formValues.category === 'NON_POINT',
      blockGuid: blockGuid,
      deviceType: deviceType,
      productModel: productModel,
    };
    if (formValues.category === 'NORMAL_POINT') {
      params.code = formValues.nomarlId?.value ?? formValues.nomarlId;
    }
    setSubmitLoading(true);
    if (!isCreate && nonPoint) {
      params.id = nonPoint.id;
    }
    const { error } = isCreate ? await createNonPoint(params) : await updateNonPoint(params);
    setSubmitLoading(false);
    if (error) {
      message.error(error.message);
    } else {
      message.success('操作成功');
      handleLeave();
      onReset();
    }
  }, [form, blockGuid, deviceType, productModel, isCreate, nonPoint, handleLeave, onReset]);

  return (
    <Drawer
      destroyOnClose
      title={title}
      bodyStyle={{ overflowY: 'auto' }}
      placement="right"
      open={visible}
      contentWrapperStyle={{ width: window.innerWidth / 3 }}
      extra={
        <Space size="middle">
          <Button onClick={handleLeave}>取消</Button>
          <Button type="primary" loading={submitLoading} onClick={submit}>
            确定
          </Button>
        </Space>
      }
      onClose={handleLeave}
    >
      <Form form={form} layout="vertical">
        <Row gutter={24}>
          <Col span={24}>
            <Form.Item
              label={locales.category}
              name="category"
              rules={[
                {
                  required: true,
                },
              ]}
            >
              <PointCategorySelect style={{ width: 300 }} disabled={!isCreate} />
            </Form.Item>

            <Form.Item
              label={locales.dataType}
              name="dataType"
              rules={[
                {
                  required: true,
                },
              ]}
            >
              <PointTypeSelect />
            </Form.Item>

            <Form.Item
              noStyle
              shouldUpdate={(pre, next) =>
                pre.category !== next.category || pre.dataType !== next.dataType
              }
            >
              {({ getFieldValue }) => {
                if (getFieldValue('category') === 'NORMAL_POINT' && getFieldValue('dataType')) {
                  return (
                    <Form.Item
                      label={locales.nomarlId}
                      name="nomarlId"
                      rules={[{ required: true }]}
                    >
                      <PointSelect
                        isRemoveMain
                        style={{ width: 300 }}
                        showSearch
                        treeNodeLabelProp="textLabel"
                        treeNodeFilterProp="textLabel"
                        labelInValue
                        dataTypeList={[getFieldValue('dataType')]}
                        pointTypeList={[
                          'ORI',
                          'CAL_SPACE',
                          'AGG_SPACE',
                          'CAL_DEVICE',
                          'AGG_DEVICE',
                          'CUSTOM',
                        ]}
                        spaceGuid={blockGuid}
                        deviceType={deviceType}
                        disabled={!isCreate}
                      />
                    </Form.Item>
                  );
                } else {
                  return null;
                }
              }}
            </Form.Item>
            <Form.Item
              label={locales.name}
              name="name"
              rules={[
                ...getNoSpaceNTabFormValidateRules({ label: locales.name }),
                {
                  required: true,
                },
                {
                  max: 32,
                  message: '最多输入 32 个字符！',
                },
              ]}
            >
              <Input style={{ width: 300 }} />
            </Form.Item>
            <Form.Item
              label={locales.unit}
              name="unit"
              rules={[
                {
                  max: 100,
                  message: '最多输入 100 个字符！',
                },
              ]}
            >
              <SpecUnitSelect
                style={{
                  width: 200,
                }}
                allowClear
                showSearch
              />
            </Form.Item>
            <Form.Item noStyle shouldUpdate={(pre, next) => pre.dataType !== next.dataType}>
              {({ getFieldValue }) => {
                if (getFieldValue('dataType') === 'DI' || getFieldValue('dataType') === 'DO') {
                  return (
                    <Form.Item
                      label={locales.validLimits}
                      name="validLimits"
                      rules={[
                        {
                          max: 100,
                          message: '最多输入 100 个字符！',
                        },
                      ]}
                    >
                      <Input style={{ width: 300 }} placeholder="如: 0=正常;1=报警" />
                    </Form.Item>
                  );
                } else {
                  return null;
                }
              }}
            </Form.Item>

            {isCreate && (
              <Form.Item
                label={locales.formula}
                name="formula"
                rules={[
                  {
                    max: 100,
                    message: '最多输入 100 个字符！',
                  },
                ]}
              >
                <Input.TextArea />
              </Form.Item>
            )}
            {isCreate && (
              <Form.Item label="非法值" name="invalidValues">
                <Input style={{ width: 224 }} />
              </Form.Item>
            )}
            <Form.Item label={locales.captureStatus.__self} name="capture" valuePropName="checked">
              <PointCaptureStatusSwitch
                checkedChildren="开启"
                unCheckedChildren="关闭"
                defaultChecked
                disabled={!nonPoint?.nonPoint && !isCreate}
              />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Drawer>
  );
}
