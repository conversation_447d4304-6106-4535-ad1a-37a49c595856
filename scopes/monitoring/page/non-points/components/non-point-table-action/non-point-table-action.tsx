import React, { useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Divider } from '@manyun/base-ui.ui.divider';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Switch } from '@manyun/base-ui.ui.switch';
import { Typography } from '@manyun/base-ui.ui.typography';

import type { NonPointJSON } from '@manyun/monitoring.model.point';
import { deleteNonPoint } from '@manyun/monitoring.service.delete-non-point';
import { openNCloseNonPoint } from '@manyun/monitoring.service.open-n-close-non-point';

export type NonPointTableActionProps = {
  pointIds: number[];
  style: React.CSSProperties;
  onSeletedItemChange: (ids: NonPointJSON[]) => void;
  onDataSourceChange: (params?: Record<string, string | number>) => void;
};
export function NonPointTableAction({
  pointIds,
  style,
  onSeletedItemChange,
  onDataSourceChange,
}: NonPointTableActionProps) {
  const isPointsNil = !pointIds.length;
  const [delChannelPoint, setDelChannelPoint] = useState(false);

  const handleOperateStatus = async (pointIds: number[], enabled: boolean) => {
    const { error } = await openNCloseNonPoint({
      ids: pointIds,
      enable: enabled,
    });
    if (error) {
      message.error(error.message);
      return;
    }
    message.success('设置成功');
    onDataSourceChange();
    onSeletedItemChange([]);
  };

  const handleOperateDelete = async (pointIds: number[]) => {
    const { error } = await deleteNonPoint({
      ids: pointIds,
      delChannelPoint,
    });
    if (error) {
      message.error(error.message);
      return;
    }
    message.success('删除成功');
    onDataSourceChange();
    onSeletedItemChange([]);
  };

  return (
    <div style={style}>
      <Divider style={{ margin: '16px 0' }} />
      <Space
        size={8}
        style={{
          display: 'flex',
          alignItems: 'center',
          padding: '0 16px',
        }}
      >
        <Button
          disabled={isPointsNil}
          onClick={() => {
            handleOperateStatus(pointIds, true);
          }}
        >
          批量启用
        </Button>
        <Button
          disabled={isPointsNil}
          onClick={() => {
            handleOperateStatus(pointIds, false);
          }}
        >
          批量停用
        </Button>
        <DeleteConfirm
          variant="popconfirm"
          targetName="测点"
          title={
            <Space direction="vertical">
              <Typography.Text>
                系统会同步删除已映射的测点，且不可恢复，请谨慎操作。
              </Typography.Text>
              <Typography.Text>
                是否同步删除测点数据：
                <Switch onChange={check => setDelChannelPoint(check)} />
              </Typography.Text>
            </Space>
          }
          onOk={() => {
            handleOperateDelete(pointIds);
            return Promise.resolve(true);
          }}
        >
          <Button disabled={isPointsNil}>批量删除</Button>
        </DeleteConfirm>
        {!isPointsNil && <Typography.Text>已选择{pointIds.length}项</Typography.Text>}
        {!isPointsNil && (
          <Typography.Link onClick={() => onSeletedItemChange([])}>取消选择</Typography.Link>
        )}
      </Space>
    </div>
  );
}
