import React from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import type { FormInstance } from '@manyun/base-ui.ui.form';
import { Form } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Space } from '@manyun/base-ui.ui.space';

import type { PointCaptureStatus } from '@manyun/monitoring.ui.point-capture-status-select';
import { PointCaptureStatusSelect } from '@manyun/monitoring.ui.point-capture-status-select';
import type { PointCategory } from '@manyun/monitoring.ui.point-category-select';
import { PointCategorySelect } from '@manyun/monitoring.ui.point-category-select';
import { PointTypeSelect } from '@manyun/resource-hub.ui.point-type-select';

export type ExtraParams = {
  // 是否为非标点位
  pointCategory?: PointCategory;
  // 是否采集
  isCapture?: PointCaptureStatus;
  dataTypeList?: string[];
};

export type NonPointQueryFilterProps = {
  onReset: () => void;
  form: FormInstance;
  onSearch(values: ExtraParams): void;
};

export function NonPointQueryFilter({ onReset, onSearch, form }: NonPointQueryFilterProps) {
  return (
    <div
      style={{
        marginTop: 16,
      }}
    >
      <Form
        layout="vertical"
        form={form}
        onFinish={onSearch}
        onReset={() => {
          form.resetFields();
          onReset();
        }}
      >
        <Row gutter={16}>
          <Col span={6}>
            <Form.Item label="测点类型" name="dataTypeList">
              <PointTypeSelect
                style={{ width: '100%' }}
                allowClear
                mode="multiple"
                placeholder="不限"
              />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="测点分类" name="pointCategory">
              <PointCategorySelect style={{ width: '100%' }} allowClear placeholder="不限" />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="采集状态" name="isCapture">
              <PointCaptureStatusSelect allowClear placeholder="不限" />
            </Form.Item>
          </Col>
          <Col span={4}>
            <Space style={{ marginTop: 30 }}>
              <Button htmlType="reset">重置</Button>
              <Button type="primary" htmlType="submit">
                搜索
              </Button>
            </Space>
          </Col>
        </Row>
      </Form>
    </div>
  );
}
