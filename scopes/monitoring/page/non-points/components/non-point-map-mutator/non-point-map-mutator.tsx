import React, { useCallback, useState } from 'react';
import { useShallowCompareEffect } from 'react-use';

import { Button } from '@manyun/base-ui.ui.button';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import { Form } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Switch } from '@manyun/base-ui.ui.switch';
import type { NonPointJSON } from '@manyun/monitoring.model.point';
import { getNonPointDataLocales } from '@manyun/monitoring.model.point';
import { updateNonPointMap } from '@manyun/monitoring.service.update-non-point-map';
import { PointCategorySelect } from '@manyun/monitoring.ui.point-category-select';
import { PointSelect } from '@manyun/resource-hub.ui.point-select';
import { PointTypeSelect } from '@manyun/resource-hub.ui.point-type-select';

export type NonPointMapMutatorProps = {
  visible: boolean;
  onClose: () => void;
  title: string;
  onReset: () => void;
  deviceType: string;
  blockGuid: string;
  nonPoint?: NonPointJSON;
};

/** 修改映射测点，数据反向处理 */
export function NonPointMapMutator({
  nonPoint,
  visible = false,
  onClose,
  title,
  onReset,
  deviceType,
  blockGuid,
}: NonPointMapMutatorProps) {
  const locales = getNonPointDataLocales();
  const [form] = Form.useForm();

  useShallowCompareEffect(() => {
    if (nonPoint && visible) {
      form.setFieldsValue({
        category: nonPoint?.nonPoint ? 'NON_POINT' : 'NORMAL_POINT',
        name: nonPoint.point.name,
        formula: nonPoint.formula,
        dataType: nonPoint.point.type,
        nomarlId: !nonPoint?.nonPoint ? nonPoint.point.code : undefined,
        updateChannelPoint: false,
        invalidValues: nonPoint.invalidValues,
      });
    }
  }, [form, nonPoint, visible]);

  const handleLeave = useCallback(() => {
    form.resetFields();
    onClose();
  }, [form, onClose]);

  const [submitLoading, setSubmitLoading] = useState<boolean>(false);
  const submit = useCallback(async () => {
    const formValues = await form.validateFields();
    if (!nonPoint) {
      return;
    }
    const params: {
      nonPoint: boolean;
      formula: string;
      name: string;
      id: number;
      code?: string;
      updateChannelPoint: boolean;
      invalidValues?: string;
    } = {
      nonPoint: formValues.category === 'NON_POINT',
      formula: formValues.formula,
      name: formValues.name,
      id: nonPoint.id,
      updateChannelPoint: formValues.updateChannelPoint,
      invalidValues: formValues.invalidValues,
    };
    if (formValues.category === 'NORMAL_POINT') {
      params.code = formValues.nomarlId?.value ?? formValues.nomarlId;
    }
    setSubmitLoading(true);
    const { error } = await updateNonPointMap(params);
    setSubmitLoading(false);
    if (error) {
      message.error(error.message);
    } else {
      message.success('操作成功');
      handleLeave();
      onReset();
    }
  }, [form, nonPoint, handleLeave, onReset]);

  return (
    <Drawer
      destroyOnClose
      title={title}
      bodyStyle={{ overflowY: 'auto' }}
      placement="right"
      open={visible}
      contentWrapperStyle={{ width: window.innerWidth / 3 }}
      extra={
        <Space size="middle">
          <Button onClick={handleLeave}>取消</Button>
          <DeleteConfirm
            variant="popconfirm"
            targetName="映射"
            title="系统会同步修改已映射的测点，且不可还原，请谨慎操作。"
            onOk={() => {
              submit();
              return Promise.resolve(true);
            }}
          >
            <Button type="primary" loading={submitLoading} onClick={submit}>
              确定
            </Button>
          </DeleteConfirm>
        </Space>
      }
      onClose={handleLeave}
    >
      <Form form={form} layout="vertical">
        <Row gutter={24}>
          <Col span={24}>
            <Form.Item
              label={locales.name}
              name="name"
              rules={[
                {
                  required: true,
                },
              ]}
            >
              <Input style={{ width: 300 }} disabled />
            </Form.Item>

            <Form.Item
              label={locales.category}
              name="category"
              rules={[
                {
                  required: true,
                },
              ]}
            >
              <PointCategorySelect style={{ width: 300 }} />
            </Form.Item>

            <Form.Item noStyle shouldUpdate={(pre, next) => pre.category !== next.category}>
              {({ getFieldValue }) => {
                if (getFieldValue('category') === 'NORMAL_POINT') {
                  return (
                    <Form.Item
                      label={locales.dataType}
                      name="dataType"
                      rules={[
                        {
                          required: true,
                        },
                      ]}
                    >
                      <PointTypeSelect />
                    </Form.Item>
                  );
                } else {
                  return null;
                }
              }}
            </Form.Item>

            <Form.Item
              noStyle
              shouldUpdate={(pre, next) =>
                pre.category !== next.category || pre.dataType !== next.dataType
              }
            >
              {({ getFieldValue }) => {
                if (getFieldValue('category') === 'NORMAL_POINT') {
                  return (
                    <Form.Item
                      label={locales.nomarlId}
                      name="nomarlId"
                      rules={[{ required: true }]}
                    >
                      <PointSelect
                        isRemoveMain
                        style={{ width: 300 }}
                        showSearch
                        treeNodeLabelProp="textLabel"
                        treeNodeFilterProp="textLabel"
                        labelInValue
                        dataTypeList={[getFieldValue('dataType')]}
                        pointTypeList={[
                          'ORI',
                          'CAL_SPACE',
                          'AGG_SPACE',
                          'CAL_DEVICE',
                          'AGG_DEVICE',
                          'CUSTOM',
                        ]}
                        spaceGuid={blockGuid}
                        deviceType={deviceType}
                      />
                    </Form.Item>
                  );
                } else {
                  return null;
                }
              }}
            </Form.Item>
            <Form.Item
              label={locales.updateChannelPoint}
              name="updateChannelPoint"
              valuePropName="checked"
              rules={[
                {
                  required: true,
                },
              ]}
            >
              <Switch />
            </Form.Item>

            <Form.Item
              label={locales.formula}
              name="formula"
              rules={[
                {
                  max: 100,
                  message: '最多输入 100 个字符！',
                },
              ]}
            >
              <Input.TextArea />
            </Form.Item>
            <Form.Item label="非法值" name="invalidValues">
              <Input style={{ width: 224 }} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Drawer>
  );
}
