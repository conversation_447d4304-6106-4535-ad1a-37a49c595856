import React, { useContext, useEffect, useState } from 'react';
import { useShallowCompareEffect } from 'react-use';

import { message } from '@manyun/base-ui.ui.message';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Spin } from '@manyun/base-ui.ui.spin';

import { fetchNonPointProduct } from '@manyun/monitoring.service.fetch-non-point-product';

import { NonPointContext } from '../../non-point-context';

export type NonPointProductModelProps = {
  blockGuid: string;
  deviceType: string;
  onProductModelChange?: (productModel?: string) => void;
};

export function NonPointProductModel({
  onProductModelChange,
  blockGuid,
  deviceType,
}: NonPointProductModelProps) {
  const [options, setOptions] = useState<{ value: string; label: string }[]>([]);
  const [{ productModel }] = useContext(NonPointContext);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    (async () => {
      setLoading(true);
      const { data, error } = await fetchNonPointProduct({
        blockGuid,
        deviceType,
      });
      setLoading(false);
      if (error) {
        message.error(error.message);
      }
      setOptions(
        data.data.map(item => ({
          value: item,
          label: item,
        }))
      );
    })();
  }, [blockGuid, deviceType]);

  useShallowCompareEffect(() => {
    if (options.length) {
      onProductModelChange?.(options[0].value);
    } else {
      onProductModelChange?.(undefined);
    }
  }, [options]);

  return (
    <Spin spinning={loading}>
      <Radio.Group
        style={{
          width: options.length > 5 ? 600 : 'auto',
          overflowX: 'auto',
          height: 32,
        }}
        value={productModel}
        optionType="button"
        options={options}
        onChange={e => onProductModelChange?.(e.target.value)}
      />
    </Spin>
  );
}
