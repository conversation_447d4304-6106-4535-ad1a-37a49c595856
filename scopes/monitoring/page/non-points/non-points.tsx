import React, { use<PERSON><PERSON>back, useContext, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { useLocation } from 'react-router-dom';
import { useShallowCompareEffect } from 'react-use';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Card } from '@manyun/base-ui.ui.card';
import { Empty } from '@manyun/base-ui.ui.empty';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Typography } from '@manyun/base-ui.ui.typography';
import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';

import type { ColumnType } from '@manyun/dc-brain.ui.edit-columns';
import type { NonPointJSON } from '@manyun/monitoring.model.point';
import { getNonPointDataLocales } from '@manyun/monitoring.model.point';
import type { SvcQuery as QueryParams } from '@manyun/monitoring.service.fetch-non-points';
import { fetchNonPoints } from '@manyun/monitoring.service.fetch-non-points';
import { openNCloseNonPoint } from '@manyun/monitoring.service.open-n-close-non-point';
import { selectBlocksByIdc } from '@manyun/resource-hub.state.space';
import type { DeviceTreeNode } from '@manyun/resource-hub.ui.device-type-cascader/use-device';
import { MyBlocks } from '@manyun/resource-hub.ui.my-blocks';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

import { NonPointAction } from './components/non-point-action';
import { NonPointProductModel } from './components/non-point-product-model';
import { NonPointQueryAction } from './components/non-point-query-action';
import { NonPointQueryFilter } from './components/non-point-query-filter';
import type { ExtraParams } from './components/non-point-query-filter/non-point-query-filter';
import { NonPointTable } from './components/non-point-table';
import { NonPointTableAction } from './components/non-point-table-action';
import { getColumns } from './components/non-point-table/get-columns';
import { NonPointContext } from './non-point-context';
import styles from './non-point.module.less';

type Params = {
  blockGuid?: string;
  deviceType?: string;
  productModel?: string;
};

export function NonPoints() {
  const { search } = useLocation()!;
  const { idc } = getLocationSearchMap<{
    idc?: string;
  }>(search);
  const [tableLoading, setTableLoading] = useState(false);
  const [dataSource, setDataSource] = useState<NonPointJSON[]>([]);
  const [selectedItem, setSelectedItem] = useState<NonPointJSON[]>([]);
  const [form] = Form.useForm();
  const [searchKeys, setSearchKeys] = useState<string>();
  const [defaultParams, setDefaultParams] = useState<Params>({});
  const [extraParams, setExtraParams] = useState<ExtraParams>();
  const [deviceName, setDeviceName] = useState<string>('测点映射');

  const selectBlockGuids = useSelector(selectBlocksByIdc({ idc: idc! }));
  const locales = getNonPointDataLocales();
  const points = useMemo(() => {
    if (!searchKeys) {
      return dataSource;
    }
    return dataSource.filter(
      data =>
        data.point.code.toLowerCase().indexOf(searchKeys.toLowerCase()) > -1 ||
        data.point.name.toLowerCase().indexOf(searchKeys.toLowerCase()) > -1
    );
  }, [dataSource, searchKeys]);

  const getNonPoints = useCallback(
    async ({ blockGuid, deviceType, productModel }: Params, extraParams?: ExtraParams) => {
      if (!idc || !blockGuid || !deviceType || !productModel) {
        setDataSource([]);
        return;
      }
      if (extraParams) {
        setExtraParams(extraParams);
      }
      const params: ExtraParams & QueryParams = {
        blockGuid,
        deviceType,
        productModel,
        ...extraParams,
      };
      if (extraParams?.isCapture) {
        params.capture = extraParams?.isCapture === 'ON';
      }
      if (extraParams?.pointCategory) {
        params.nonPoint = extraParams?.pointCategory === 'NON_POINT';
      }
      setTableLoading(true);
      const { error, data } = await fetchNonPoints(params);
      setTableLoading(false);
      if (error) {
        message.error(error.message);
      }
      setDataSource(data.data);
    },
    [idc]
  );

  const handleSetStatus = async (ids: number[] = [], enabled: boolean) => {
    if (!ids.length) {
      return;
    }
    const { error } = await openNCloseNonPoint({
      ids: ids,
      enable: enabled,
    });
    if (error) {
      message.error(error.message);
      return;
    }
    message.success('设置成功');
  };

  const [columnsConfig, setColumnsConfig] = useState<ColumnType<NonPointJSON>[]>([]);

  useShallowCompareEffect(() => {
    if (selectBlockGuids.length && idc) {
      setDefaultParams(prev => ({
        deviceType: undefined,
        productModel: undefined,
        blockGuid: selectBlockGuids[0],
      }));
    }
    setDataSource([]);
  }, [idc, selectBlockGuids]);

  const onDeviceTypeChange = useCallback(
    (node: DeviceTreeNode) => {
      setDeviceName(node.title);
      if (node.type === 'C2') {
        setDefaultParams(prev => ({
          ...prev,
          deviceType: node.value,
          productModel: undefined,
        }));
        form.resetFields();
        setSelectedItem([]);
      }
    },
    [form]
  );

  const columns = useDeepCompareMemo(() => {
    const columnShowConfig = columnsConfig.filter(column => column.show);
    return columnShowConfig
      .map(column =>
        getColumns(locales, async (ids: number[] = [], enabled: boolean) => {
          await handleSetStatus(ids, enabled);
          if (defaultParams) {
            getNonPoints(defaultParams, extraParams);
          }
        }).find(item => item.dataIndex === column.dataIndex)
      )
      .filter((item): item is ColumnType<NonPointJSON> => typeof item !== 'undefined');
  }, [columnsConfig, locales, defaultParams, getNonPoints, extraParams]);

  return (
    <NonPointContext.Provider
      value={[
        {
          deviceType: defaultParams?.deviceType,
          productModel: defaultParams?.productModel,
          blockGuid: defaultParams?.blockGuid,
        },
      ]}
    >
      <BlockBar
        onActiveBlockGuidChange={blockGuid => {
          setDefaultParams(prev => ({
            blockGuid: blockGuid,
            productModel: undefined,
            deviceType: undefined,
          }));
        }}
      />
      <div style={{ display: 'flex', gap: 16, height: 'calc(100vh - 170px)' }}>
        <DeviceTypeAction onDeviceTypeChange={onDeviceTypeChange} />
        {defaultParams?.deviceType ? (
          <Card
            style={{ width: 'calc(100% - 352px)' }}
            title={
              <Typography.Title level={5} showBadge>
                {deviceName}
              </Typography.Title>
            }
            className={styles.pointContainer}
            extra={
              defaultParams?.blockGuid ? (
                <NonPointProductModel
                  blockGuid={defaultParams.blockGuid}
                  deviceType={defaultParams.deviceType}
                  onProductModelChange={productModel => {
                    setDefaultParams(prev => ({
                      ...prev,
                      productModel: productModel,
                    }));
                    if (productModel) {
                      getNonPoints({
                        blockGuid: defaultParams.blockGuid!,
                        deviceType: defaultParams.deviceType!,
                        productModel: productModel,
                      });
                    } else {
                      form.resetFields();
                      setDataSource([]);
                    }
                  }}
                />
              ) : null
            }
            bodyStyle={{
              height: 'calc(100% - 137px)',
              overflowY: 'auto',
            }}
          >
            <NonPointQueryAction
              dataSource={dataSource}
              setSearchKeys={setSearchKeys}
              defaultColumnsConfig={getColumns(locales)}
              onReset={() => getNonPoints(defaultParams)}
              onColumnsChange={setColumnsConfig}
            />
            <NonPointQueryFilter
              form={form}
              onReset={() => {
                form.resetFields();
                getNonPoints(defaultParams);
              }}
              onSearch={values => {
                getNonPoints(defaultParams, {
                  ...values,
                });
              }}
            />
            <NonPointTable
              loading={tableLoading}
              dataSource={points}
              selectedIds={selectedItem.map(item => item.id)}
              columnsConfig={columns}
              onSeletedItemChange={setSelectedItem}
              onReset={() => {
                getNonPoints(defaultParams, extraParams);
              }}
            />
            <NonPointTableAction
              pointIds={selectedItem.map(item => item.id) as number[]}
              style={{
                position: 'absolute',
                bottom: 19,
                left: 0,
                width: '100%',
              }}
              onSeletedItemChange={setSelectedItem}
              onDataSourceChange={params => {
                getNonPoints(defaultParams, {
                  ...params,
                });
              }}
            />
          </Card>
        ) : (
          <Card
            style={{ width: 'calc(100% - 352px)' }}
            bodyStyle={{
              position: 'relative',
              height: 'calc(var(--content-height) - 57px)',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <Empty description="暂无数据，请选择设备三级分类后查看" />
          </Card>
        )}
      </div>
    </NonPointContext.Provider>
  );
}

function BlockBar({
  onActiveBlockGuidChange,
}: {
  onActiveBlockGuidChange: (blockGuid: string) => void;
}) {
  const [{ blockGuid }] = useContext(NonPointContext);

  return (
    <div className={styles.nonPointStrip}>
      <Typography.Title
        level={5}
        showBadge
        style={{
          marginBottom: 0,
        }}
      >
        设备测点配置
      </Typography.Title>
      <MyBlocks
        idc={getSpaceGuidMap(blockGuid!).idc!}
        value={blockGuid}
        onChange={e => onActiveBlockGuidChange(e.target.value)}
      />
    </div>
  );
}

function DeviceTypeAction({
  onDeviceTypeChange,
}: {
  onDeviceTypeChange: (DataNode: DeviceTreeNode) => void;
}) {
  const [{ deviceType }] = useContext(NonPointContext);

  return (
    <Card
      style={{ width: 336, height: '100%' }}
      bodyStyle={{
        padding: 0,
      }}
      className={styles.actionCard}
    >
      <NonPointAction
        selectedKeys={deviceType ? [deviceType] : []}
        onDeviceTypeChange={onDeviceTypeChange}
      />
    </Card>
  );
}
