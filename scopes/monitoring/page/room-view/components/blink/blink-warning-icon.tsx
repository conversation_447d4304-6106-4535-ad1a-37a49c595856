import React from 'react';

import ExclamationCircleFilled from '@ant-design/icons/es/icons/ExclamationCircleFilled';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';

import { Blink } from './blink';
import type { BlinkProps } from './blink';

export function BlinkWarningIcon(props: BlinkProps) {
  return (
    <Blink {...props}>
      <ExclamationCircleFilled style={{ color: `var(--${prefixCls}-warning-color)` }} />
    </Blink>
  );
}
