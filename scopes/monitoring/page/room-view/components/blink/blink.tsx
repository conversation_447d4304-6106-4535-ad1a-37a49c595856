import React from 'react';
import { a, useSpring } from 'react-spring';

export type BlinkProps = {
  style?: React.CSSProperties;
  /**
   * @defaultValue `true`
   */
  blinking?: boolean;
  children: React.ReactNode;
};

export function Blink({ style, blinking = true, children }: BlinkProps) {
  const isAnimatingRef = React.useRef(false);
  const [styles, api] = useSpring(() => ({
    config: {
      duration: 250,
    },
    from: {
      opacity: 1,
    },
  }));
  React.useEffect(() => {
    if (blinking && !isAnimatingRef.current) {
      api.start({
        opacity: 0,
        loop: { reverse: true },
      });
      isAnimatingRef.current = true;
    } else if (!blinking && isAnimatingRef.current) {
      api.stop();
      isAnimatingRef.current = false;
    }
  }, [blinking, api]);

  return <a.div style={{ display: 'inline-block', ...style, ...styles }}>{children}</a.div>;
}
