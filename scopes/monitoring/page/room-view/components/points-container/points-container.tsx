import React, { useState } from 'react';
import { useSelector } from 'react-redux';

import useDeepCompareEffect from 'use-deep-compare-effect';

import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Input } from '@manyun/base-ui.ui.input';
import { Space } from '@manyun/base-ui.ui.space';

import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { Point } from '@manyun/monitoring.model.point';
import { usePoints } from '@manyun/resource-hub.hook.use-points';
import { PointsTable } from '@manyun/resource-hub.ui.points-table';

// 测点类型
const pointStateOptions = [
  { value: 'AGG', label: '聚合测点' },
  { value: 'CAL', label: '加工测点' },
  { value: 'CUSTOM', label: '自定义测点' },
];

export type PointsContainerProps = {
  spaceGuid: string;
};

export function PointsContainer({ spaceGuid }: PointsContainerProps) {
  const [points, setPoints] = useState<Point[]>([]); // 选中的测点
  const [checkedState, setCheckedState] = useState(pointStateOptions.map(v => v.value)); // 默认全部选中
  const [searchText, setSearchText] = useState(''); // 搜索文本

  const config = useSelector(selectCurrentConfig);
  const configUtil = new ConfigUtil(config);
  const roomDeviceType = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_ROOM);
  const [{ data }] = usePoints({
    fields: {
      deviceType: roomDeviceType,
      spaceGuid,
      isRemoveMain: true,
      pointTypeList: ['CAL_SPACE', 'AGG_SPACE', 'CUSTOM'],
    },
  });
  const hasCustomPoints = data.some(point => point.pointType.code.toLowerCase() === 'custom');
  const pointTypeOptions = hasCustomPoints
    ? pointStateOptions
    : pointStateOptions.filter(({ value }) => value.toLowerCase() !== 'custom');

  // 搜索、筛选测点
  useDeepCompareEffect(() => {
    if (!checkedState?.length) {
      setPoints([]);
    } else {
      setPoints(
        data.filter(
          (point: Point) =>
            (point.code.includes(searchText || '') || point.name.includes(searchText || '')) &&
            checkedState.find(state => point.pointType.code?.includes(state))
        )
      );
    }
  }, [data, checkedState, searchText]);

  return (
    <Space direction="vertical" style={{ width: '100%' }} size="middle">
      <Space>
        <Input.Search
          placeholder="请输入测点名称/编号"
          style={{ width: '200px' }}
          onSearch={setSearchText}
        />
        <Checkbox.Group
          options={pointTypeOptions}
          value={checkedState}
          // @ts-expect-error ts(2322)
          onChange={setCheckedState}
        />
      </Space>
      <PointsTable
        spaceGuid={spaceGuid}
        guid={spaceGuid}
        deviceType={roomDeviceType}
        points={points}
        hideNoDataItems
      />
    </Space>
  );
}
