import React, { useCallback, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Empty } from '@manyun/base-ui.ui.empty';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { Space } from '@manyun/base-ui.ui.space';

import { TicketCard } from '@manyun/monitoring.ui.ticket-card';
import { fetchTicketsByType } from '@manyun/ticket.fetch-tickets-by-type';
import type { Ticket } from '@manyun/ticket.model.ticket';
import { BackendTaskStatus } from '@manyun/ticket.model.ticket';

import { getCommonData } from '@manyun/dc-brain.legacy.redux/selectors/commonSelectors';

const TicketStateOptions = [
  { value: BackendTaskStatus.WAITTAKEOVER, label: '未接单' },
  { value: BackendTaskStatus.PROCESSING, label: '处理中' },
];

const TicketTypes = [
  'INSPECTION', // 巡检单
  'REPAIR', // 维修单
  'MAINTENANCE', // 维保单
  'INVENTORY', // 盘点单
  'POWER', // 上下电申请
  // 'ON_OFF', //上下线
  'DEVICE_GENERAL', // 上下架
];
export type TicketsContainerProps = {
  spaceGuids: string[];
};

export function TicketsContainer({ spaceGuids }: TicketsContainerProps) {
  const [tickets, setTickets] = useState<Ticket[]>([]); // 选中工单
  const [checkedState, setCheckedState] = useState(TicketStateOptions.map(v => v.value)); // 默认全部选中
  const [searchText, setSearchText] = useState(''); // 搜索文本
  const { ticketTypes } = useSelector(getCommonData);
  const ticketTypeList = ticketTypes?.normalizedList || {};

  // 搜索、筛选工单
  const queryTickets = useCallback(() => {
    if (!checkedState.length) {
      setTickets([]);
      return;
    }
    const promiseArr = TicketTypes.map(taskType =>
      fetchTicketsByType({
        spaceGuid: spaceGuids.join('.'),
        taskType,
        taskStatusList: checkedState,
      })
    );
    Promise.all(promiseArr)
      .then(results => {
        const dataList = results.map(({ data: { data } }) => data).flat();
        let tickets = [...dataList];
        if (searchText) {
          tickets = dataList.filter(ticket =>
            ticket.id.toLowerCase().includes(searchText.toLowerCase())
          );
        }
        setTickets(tickets);
      })
      .catch(error => {
        // eslint-disable-next-line no-console
        console.error(error);
      });
  }, [checkedState, searchText, spaceGuids]);

  useEffect(() => {
    queryTickets();
    const interval = window.setInterval(() => {
      queryTickets();
    }, 15 * 1000);

    return () => {
      window.clearInterval(interval);
    };
  }, [queryTickets]);

  return (
    <Space direction="vertical" size={16} style={{ width: '100%' }}>
      <Space>
        <Input.Search
          style={{ width: '200px' }}
          placeholder="请输入工单号"
          onSearch={value => {
            setSearchText(value);
          }}
        />
        <Checkbox.Group
          options={TicketStateOptions}
          value={checkedState}
          onChange={checked => {
            setCheckedState(checked as BackendTaskStatus[]);
          }}
        />
      </Space>
      {!tickets.length ? (
        <div
          style={{
            width: '100%',
            height: '100%',
            minHeight: 280,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <Empty />
        </div>
      ) : (
        <Row wrap gutter={[16, 16]}>
          {tickets.map((ticket: Ticket) => {
            return (
              <Col key={ticket.id} span={12}>
                <TicketCard ticket={ticket} ticketTypeList={ticketTypeList} />
              </Col>
            );
          })}
        </Row>
      )}
    </Space>
  );
}
