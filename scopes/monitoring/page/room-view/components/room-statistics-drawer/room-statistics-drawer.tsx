/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import classNames from 'classnames';

import { Space } from '@manyun/base-ui.ui.space';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import type { TabsProps } from '@manyun/base-ui.ui.tabs';
import { Typography } from '@manyun/base-ui.ui.typography';

import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import type { Point } from '@manyun/monitoring.model.point';
import { getMonitoringData } from '@manyun/monitoring.state.subscriptions';
import type { DiPointValue } from '@manyun/monitoring.util.get-monitoring-data';
import { generateGetPointMonitoringData } from '@manyun/monitoring.util.get-monitoring-data';
import { usePoints } from '@manyun/resource-hub.hook.use-points';
import { selectPointsEntities } from '@manyun/resource-hub.state.points';
import type { Ticket } from '@manyun/ticket.model.ticket';

import {
  cancelGetAlarmsActionCreator,
  getAlarmInfoActionCreator, // @ts-ignore: fix legacy codes
} from '@manyun/dc-brain.legacy.redux/actions/roomMonitoringActions';

import { AlarmsContainer } from '../alarms-container';
import { CorePoints } from '../core-points';
import { CustomerStatistics } from '../customer-statistics';
import { PointsContainer } from '../points-container';
import { StatisticsContainer } from '../statistics-container';
import { TicketsContainer } from '../tickets-container';
import { useStatisticsData } from './const';
import type { SummaryItemType } from './const';
import styles from './room-statistics-drawer.module.less';

export type RoomStatisticsDrawerProps = {
  drawerVisible: boolean;
  isRacksRoom: boolean;
  spaceGuids: string[];
  tickets: Ticket[];
};

const getTop3Points = (deviceType: string) => {
  const codes = ['1015000', '1016000', '1064000'];
  const pointKeys = codes.map((val: string) => `${deviceType}.${val}`);
  return pointKeys;
};

const getTabs = (isRacksRoom: boolean, spaceGuids: string[], totalOfCabinet: number) => {
  const tabList: TabsProps['items'] = [
    {
      key: 'POINT',
      label: '测点',
      children: <PointsContainer spaceGuid={spaceGuids.join('.')} />,
    },
    {
      key: 'ALARM',
      label: '告警',
      children: <AlarmsContainer spaceGuids={spaceGuids} />,
    },
    {
      key: 'TICKET',
      label: '工单',
      children: <TicketsContainer spaceGuids={spaceGuids} />,
    },
  ];
  if (isRacksRoom) {
    tabList.push({
      key: 'CUSTOMER',
      label: '客户分布',
      children: <CustomerStatistics totalOfCabinet={totalOfCabinet} spaceGuids={spaceGuids} />,
    });
  }
  return tabList;
};

const DrawerContent = ({
  isRacksRoom,
  spaceGuids,
  statisticsData,
  loadingCorePoints,
}: {
  isRacksRoom: boolean;
  spaceGuids: string[];
  statisticsData: any;
  loadingCorePoints: boolean;
}) => {
  const tabs = getTabs(isRacksRoom, spaceGuids, statisticsData.totalOfCabinet.value);

  return (
    <Space style={{ width: '100%' }} direction="vertical" size={24}>
      {isRacksRoom && (
        <StatisticsContainer statisticsData={statisticsData} spaceGuids={spaceGuids} />
      )}
      <CorePoints
        isRacksRoom={isRacksRoom}
        spaceGuid={spaceGuids.join('.')}
        isLoading={loadingCorePoints}
      />
      <Space style={{ width: '100%' }} direction="vertical" size={12}>
        <Typography.Title level={5}>{isRacksRoom ? '运行数据' : '监控数据'}</Typography.Title>
        <Tabs items={tabs} size="small" defaultActiveKey={tabs[0].key} />
      </Space>
    </Space>
  );
};

const renderPointValue = (
  point: Point,
  spaceGuids: string[],
  roomDeviceType: string,
  getPointMonitoringData: any
) => {
  const isAnalogSignal = point.dataType.code === 'AI' || point.dataType.code === 'AO';
  const isDigitalSignal = point.dataType.code === 'DI' || point.dataType.code === 'DO';
  const pointData = getPointMonitoringData(
    { deviceGuid: spaceGuids.join('.'), deviceType: roomDeviceType },
    {
      hardCodedPointCode: point.code,
      formatted: isAnalogSignal,
      reflected: isDigitalSignal,
    }
  );
  const text = isDigitalSignal ? (pointData.value as DiPointValue).NAME : pointData.value;

  return { value: text, unit: pointData.unit };
};

/**
 * 获取右侧边栏概要数据
 * @param isRackRoom 是否IT包间： true - IT包间
 * @param data 概览数据
 * @returns
 */
const getSummaryList = (
  isRackRoom = false,
  data: any,
  ticketsCount: number,
  top3Points: Point[],
  spaceGuids: string[],
  roomDeviceType: string,
  getPointMonitoringData: any
): SummaryItemType[] => {
  const summaries: SummaryItemType[] = [];
  if (isRackRoom) {
    summaries.push(
      {
        name: 'IT负荷率',
        value: data.itLoadRate.value,
        unit: data.itLoadRate.unit,
      },
      {
        name: '实时功率',
        value: data.totalPowerByRoom.value,
        unit: data.totalPowerByRoom.unit,
      },
      {
        name: '上电率',
        value: Number.isNaN(data.powerOnRate.value) ? 0 : data.powerOnRate.value,
        unit: '%',
      }
    );
  } else {
    top3Points.forEach((p: Point) => {
      const { value, unit } = renderPointValue(
        p,
        spaceGuids,
        roomDeviceType,
        getPointMonitoringData
      );
      summaries.push({
        name: p.name,
        value,
        unit,
      });
    });
  }
  summaries.push({
    name: '告警',
    value: data.pendingAddressAlarms.value,
    status: data.pendingAddressAlarms.value > 0 ? 'danger' : 'default',
    isCommon: true,
  });
  summaries.push({
    name: '工单',
    value: ticketsCount,
    status: 'default',
    isCommon: true,
  });

  return summaries;
};

export function RoomStatisticsDrawer({
  drawerVisible,
  isRacksRoom,
  spaceGuids,
  tickets,
}: RoomStatisticsDrawerProps) {
  const configUtil = new ConfigUtil(useSelector(selectCurrentConfig));
  const roomDeviceType = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_ROOM)!;
  const [{ loading }] = usePoints({
    fields: {
      deviceType: roomDeviceType,
      isRemoveMain: true,
      pointTypeList: ['CAL_SPACE', 'AGG_SPACE', 'CUSTOM'],
      spaceGuid: spaceGuids.join('.'),
    },
  });
  const statisticsData = useStatisticsData(spaceGuids);
  const corePointsData = useSelector(
    selectPointsEntities(isRacksRoom ? [] : getTop3Points(roomDeviceType))
  );

  const { devicesRealtimeData, devicesAlarmsData } = useSelector(getMonitoringData);
  const getPointMonitoringData = generateGetPointMonitoringData({
    realtimeData: devicesRealtimeData,
    alarmsData: devicesAlarmsData,
    configUtil,
  });

  const summaries = getSummaryList(
    isRacksRoom,
    statisticsData,
    tickets.length,
    corePointsData,
    spaceGuids,
    roomDeviceType,
    getPointMonitoringData
  );

  const dispatch = useDispatch();
  // 查询未确认的告警数据
  useEffect(() => {
    const [idc, block, room] = spaceGuids;
    dispatch(
      // @ts-ignore: fix legacy codes
      getAlarmInfoActionCreator({
        idc,
        block,
        room,
        alarmStatus: ['ACTIVE'],
        pageNo: 1,
        pageSize: 10,
      })
    );

    return () => {
      dispatch(cancelGetAlarmsActionCreator());
    };
  }, [dispatch, spaceGuids]);

  if (!summaries.length) {
    return null;
  }

  return (
    <div className={classNames(styles.summaryDrawer, drawerVisible && styles.expandDrawer)}>
      {drawerVisible ? (
        <DrawerContent
          isRacksRoom={isRacksRoom}
          spaceGuids={spaceGuids}
          statisticsData={statisticsData}
          loadingCorePoints={loading}
        />
      ) : (
        <div className={styles.summaryBar}>
          {summaries.map(summary => (
            <div key={summary.name}>
              <div className={classNames(styles.name, summary.isCommon && styles.commonInfo)}>
                <Typography.Text type="secondary">{summary.name}</Typography.Text>
              </div>
              <div>
                <Typography.Text
                  className={styles.value}
                  type={summary.status === 'default' ? undefined : summary.status}
                >
                  {summary.value}
                </Typography.Text>
                {summary.value !== '--' && (
                  <Typography.Text className={styles.unit}>{summary.unit}</Typography.Text>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
