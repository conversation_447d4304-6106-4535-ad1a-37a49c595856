.summaryDrawer {
  width: 115px;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  background: var(--component-background);

  .expandDrawer {
    width: 637x;
  }

  .summaryBar {
    text-align: center;
    padding: 0 12px;

    .name {
      font-size: 12px;
      margin: 42px 0 4px 0;
    }

    .commonInfo {
      margin-top: 62px;
    }

    .value {
      font-size: 16px;
    }

    .unit {
      font-size: 12px;
    }
  }
}

.expandDrawer {
  width: 637px;
  padding: 24px;
}
