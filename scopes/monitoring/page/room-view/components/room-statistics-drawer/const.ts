import { useSelector } from 'react-redux';

import get from 'lodash.get';

import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { getMonitoringData } from '@manyun/monitoring.state.subscriptions';
import { generateGetPointMonitoringData } from '@manyun/monitoring.util.get-monitoring-data';
import { typeofRacksRoom } from '@manyun/resource-hub.util.type-of-racks-room';

import { STATUS_MAP } from '@manyun/dc-brain.legacy.constants/statusColor';
import {
  getAlarmInfo,
  getInfraData,
  getRoomInfo,
} from '@manyun/dc-brain.legacy.redux/selectors/roomMonitoringSelectors';

export type SummaryItemType = {
  name: string; // 摘要名称
  value: string | number | JSX.Element; // 数据值
  unit?: string;
  status?: 'default' | 'danger'; // 数据状态
  isCommon?: boolean; // 是否是公共摘要信息(如：告警、工单)，demo上‘告警‘、’工单’两项上边距较大，此处单独标识一下
};

/**
 * 从Store中获取概览数据
 * @param spaceGuids
 * @returns
 */
export const useStatisticsData = (spaceGuids: string[]) => {
  const alarmInfo = useSelector(getAlarmInfo);
  const roomInfo = useSelector(getRoomInfo);
  const infrastructureData = useSelector(getInfraData);
  const { devicesRealtimeData, devicesAlarmsData } = useSelector(getMonitoringData);
  const config = useSelector(selectCurrentConfig);
  const configUtil = new ConfigUtil(config, { defaultSpaceGuid: spaceGuids.join('.') });
  const spaceRoomDeviceType = configUtil.getOneDeviceType(
    ConfigUtil.constants.deviceTypes.SPACE_ROOM
  )!;
  const deviceGuid = spaceGuids.join('.');
  const device = { deviceGuid, deviceType: spaceRoomDeviceType };
  const getData = generateGetPointMonitoringData({
    realtimeData: devicesRealtimeData,
    alarmsData: devicesAlarmsData,
    configUtil,
  });

  const itLoadRatePointCode = configUtil.getPointCode(
    spaceRoomDeviceType,
    ConfigUtil.constants.pointCodes.PDU_LOAD_RATE_AVG
  );
  const itLoadRatePointGuid = {
    deviceGuid,
    pointCode: [itLoadRatePointCode],
  };
  // IT负荷率
  const itLoadRate = getData(device, {
    formatted: true,
    hardCodedPointCode: itLoadRatePointCode,
    defaults: { status: STATUS_MAP.REFERENCE },
  });

  const roomPowerSumPointCode = configUtil.getPointCode(
    spaceRoomDeviceType,
    ConfigUtil.constants.pointCodes.RPP_INPUT_POWER_SUM
  );
  const totalPowerByRoomPointGuid = {
    deviceGuid,
    pointCode: [roomPowerSumPointCode],
  };
  // 实时功率
  const totalPowerByRoom = getData(device, {
    formatted: true,
    hardCodedPointCode: roomPowerSumPointCode,
    defaults: { status: STATUS_MAP.REFERENCE },
  });

  const isRacksRoom = typeofRacksRoom(roomInfo?.roomType);
  const roomTAvgPointCode = configUtil.getPointCode(
    spaceRoomDeviceType,
    isRacksRoom
      ? ConfigUtil.constants.pointCodes.COLD_AISLE_T_AVG
      : ConfigUtil.constants.pointCodes.THTB_SENSOR_T
  );
  const avgTPointGuid = {
    deviceGuid,
    pointCode: [roomTAvgPointCode],
  };
  // 平均温度
  const avgT = getData(device, {
    formatted: true,
    hardCodedPointCode: roomTAvgPointCode,
    defaults: { status: STATUS_MAP.REFERENCE },
  });

  const roomRhAvgPointCode = configUtil.getPointCode(
    spaceRoomDeviceType,
    ConfigUtil.constants.pointCodes.COLD_AISLE_RH_AVG
  );
  const avgRHPointGuid = {
    deviceGuid,
    pointCode: [roomRhAvgPointCode],
  };
  // 平均湿度
  const avgRH = getData(device, {
    formatted: true,
    hardCodedPointCode: roomRhAvgPointCode,
    defaults: { status: STATUS_MAP.REFERENCE },
  });

  const poweredGridCount = get(infrastructureData, ['result', 'poweredGridCount'], 0);
  const gridCount = get(infrastructureData, ['result', 'gridCount'], 0);
  // 上电率
  const powerOnRate = {
    value: gridCount === 0 ? 0 : ((poweredGridCount / gridCount) * 100).toFixed(1),
  };
  // 总机柜数
  const totalOfCabinet = {
    value: gridCount,
  };

  // 待处理告警数
  const pendingAddressAlarms = {
    value: alarmInfo.total,
  };

  return {
    spaceGuids,
    itLoadRate,
    totalPowerByRoom,
    avgT,
    avgRH,
    powerOnRate,
    totalOfCabinet,
    pendingAddressAlarms,
    itLoadRatePointGuid,
    totalPowerByRoomPointGuid,
    avgTPointGuid,
    avgRHPointGuid,
    spaceRoomDeviceType,
  };
};
