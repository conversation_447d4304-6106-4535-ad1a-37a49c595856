import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Pie } from '@manyun/base-ui.chart.pie';
import { Empty } from '@manyun/base-ui.ui.empty';

import { getRoomCustomersActionCreator } from '@manyun/dc-brain.legacy.redux/actions/roomMonitoringActions';
// @ts-ignore
import { getCustomersInfo } from '@manyun/dc-brain.legacy.redux/selectors/roomMonitoringSelectors';

export type CustomerStatisticsProps = {
  totalOfCabinet: number; // 总机柜数
  spaceGuids: string[];
};

export function CustomerStatistics({ totalOfCabinet, spaceGuids }: CustomerStatisticsProps) {
  const dispatch = useDispatch();
  const { data } = useSelector(getCustomersInfo) as { data: any };
  const [idc, block, room] = spaceGuids;

  useEffect(() => {
    dispatch(getRoomCustomersActionCreator({ idc, block, room }));
  }, [block, dispatch, idc, room]);

  //  将customers数据转换为图表数据源格式
  const dataSource = data.map((c: any) => ({
    name: `${c.customer || '--'}    ${
      Math.round((c.roomGridCount * 10000) / totalOfCabinet) / 100
    }%  ${c.roomGridCount}`,
    value: c.roomGridCount,
  }));

  if (!dataSource.length) {
    return (
      <div style={{ width: '100%', padding: 70 }}>
        <Empty />
      </div>
    );
  }

  return (
    <Pie
      style={{ width: 560, height: 260 }}
      option={{
        tooltip: {
          trigger: 'item',
          formatter: function (param) {
            return `客户分布<br>${param?.data?.name || ''}`;
          },
        },
        legend: {
          top: '30%',
          icon: 'circle',
          left: 'right',
          orient: 'vertical',
          align: 'left',
          textStyle: {
            padding: [0, 100, 0, 0],
          },
        },
        series: [
          {
            name: '客户分布',
            type: 'pie',
            left: '-150',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 0,
              borderColor: '#fff',
              borderWidth: 5,
            },
            label: {
              show: false,
            },
            labelLine: {
              show: false,
            },
            data: dataSource,
          },
        ],
      }}
    />
  );
}
