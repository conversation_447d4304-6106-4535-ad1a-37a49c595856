import React from 'react';
import { Link } from 'react-router-dom';

import { CameraOutlined } from '@manyun/base-ui.icons';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { env } from '@manyun/dc-brain.app-env.react-app-envs';

export type CamerasLinkProps = {
  idc: string;
  block: string;
  room: string;
};

export function CamerasLink({ idc, block, room }: CamerasLinkProps) {
  if (env.FFS_CAMERAS_SERVICE !== 'enabled') {
    return null;
  }

  return (
    <Tooltip title="该包间下所有摄像监控">
      <Link style={{ fontSize: 16 }} target="_blank" to="\">
        <CameraOutlined />
      </Link>
    </Tooltip>
  );
}
