import React from 'react';

import { BulletCCTVCameraTwoTone } from '@manyun/base-ui.icons';

import { useBulletCameraPreview } from './../bullet-camera';

export type BulletCameraProps = {
  deviceGuid: string;
  deviceName: string;
  deviceType: string;
  extendPosition: string;
  position: {
    top: number;
  };
};

const BASE_REF_SIZE = 64;
const FONT_SIZE = 16;
// in `px`
const HORIZONTAL_MARGIN = 24;

export function BulletCamera({
  deviceName,
  deviceGuid,
  deviceType,
  position,
  extendPosition,
}: BulletCameraProps) {
  const [, { open, setDevice }] = useBulletCameraPreview();

  // Place bullet camera as closer as its grid
  const placement = extendPosition.endsWith('.L')
    ? 'right'
    : extendPosition.endsWith('.R')
    ? 'left'
    : 'unknown';

  return (
    <span
      role="button"
      style={{
        cursor: 'pointer',
        fontSize: FONT_SIZE,
        position: 'absolute',
        top: position.top + (BASE_REF_SIZE / 2 - FONT_SIZE / 2) / 2,
        left: placement === 'left' ? HORIZONTAL_MARGIN : undefined,
        right: placement === 'right' ? HORIZONTAL_MARGIN : undefined,
      }}
      onClick={() => {
        setDevice({
          name: deviceName,
          guid: deviceGuid,
          type: deviceType,
        });
        open();
      }}
    >
      <BulletCCTVCameraTwoTone />
    </span>
  );
}
