import React from 'react';

import { CameraVideoModal } from '@manyun/monitoring.ui.camera-video';

export type SimpleDevice = {
  name: string;
  guid: string;
  type: string;
};

export type Values = {
  visible: boolean;
  device: SimpleDevice | null;
};

export type Handlers = {
  open: () => void;
  close: () => void;
  setDevice: (device: SimpleDevice | null) => void;
};

const noop = () => {};
const initialValue: [Values, Handlers] = [
  { visible: false, device: null },
  {
    open: noop,
    close: noop,
    setDevice: noop,
  },
];
const BulletCameraPreviewContext = React.createContext<[Values, Handlers]>(initialValue);

export const useBulletCameraPreview = () => React.useContext(BulletCameraPreviewContext);

export const BulletCameraPreviewProvider: React.FC = ({ children }) => {
  const [visible, setVisible] = React.useState(false);
  const [device, setDevice] = React.useState<SimpleDevice | null>(null);

  return (
    <BulletCameraPreviewContext.Provider
      value={[
        { visible, device },
        {
          open: () => {
            setVisible(true);
          },
          close: () => {
            setVisible(false);
          },
          setDevice,
        },
      ]}
    >
      {children}
    </BulletCameraPreviewContext.Provider>
  );
};

export function BulletCameraModal() {
  const [{ visible, device }, { close, setDevice }] = useBulletCameraPreview();

  if (!device) {
    return null;
  }

  return (
    <CameraVideoModal
      open={visible}
      title={device.name}
      guid={device.guid}
      onCancel={() => {
        close();
        setDevice(null);
      }}
    />
  );
}
