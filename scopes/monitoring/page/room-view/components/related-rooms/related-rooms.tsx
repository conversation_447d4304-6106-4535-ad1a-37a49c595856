import React from 'react';
import { Link } from 'react-router-dom';

import { Divider } from '@manyun/base-ui.ui.divider';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import { generateRoomMonitoringUrl } from '@manyun/monitoring.route.monitoring-routes';

type RoomType = string;
type RoomName = string;

export type RelatedRoomsProps = {
  rooms: Record<RoomType, RoomName>;
};

export const RelatedRooms = ({ rooms }: RelatedRoomsProps) => {
  const roomList = Object.entries(rooms);

  return (
    <Space>
      <Typography.Text type="secondary">关联的包间：</Typography.Text>
      <div>
        {roomList.map((roomInfo: string[], idx: number) => {
          const [idc, block, room] = roomInfo[0].split('.');

          return (
            <React.Fragment key={roomInfo[0]}>
              <Typography.Link
                onClick={() => {
                  window.open(generateRoomMonitoringUrl({
                    idc,
                    block,
                    room,
                  }))
                }}
              >
                {`${roomInfo[0].split('.')[2]} ${roomInfo[1]}`}
              </Typography.Link>
              {idx !== roomList.length - 1 && <Divider type="vertical" />}
            </React.Fragment>
          );
        })}
      </div>
    </Space>
  );
};
