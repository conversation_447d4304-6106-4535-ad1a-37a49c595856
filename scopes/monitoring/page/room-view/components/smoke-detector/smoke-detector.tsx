import React from 'react';
import { useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import { animated, useSpring } from 'react-spring';

import { SmokeDetectorTwoTone } from '@manyun/base-ui.icons';
import { Popover } from '@manyun/base-ui.ui.popover';

import { getMonitoringData } from '@manyun/monitoring.state.subscriptions';
import { generateDeviceRecordRoutePath } from '@manyun/resource-hub.route.resource-routes';
import { CorePointsCard } from '@manyun/resource-hub.ui.core-points-card';

import { STATUS_MAP } from '@manyun/dc-brain.legacy.constants/statusColor';
// REFACTORME: @JERRY
import { generateGetDeviceAlarmStatus } from '@manyun/dc-brain.legacy.utils/device';

export type SmokeDetectorProps = {
  deviceGuid: string;
  deviceName: string;
  deviceType: string;
  extendPosition: string;
  position: {
    top: number;
  };
};

const BASE_REF_SIZE = 64;
const FONT_SIZE = 16;
// in `px`
const HORIZONTAL_MARGIN = 24;

export function SmokeDetector({
  deviceGuid,
  deviceType,
  position,
  extendPosition,
}: SmokeDetectorProps) {
  // Place bullet camera as closer as its grid
  const placement = extendPosition.endsWith('.L')
    ? 'right'
    : extendPosition.endsWith('.R')
    ? 'left'
    : 'unknown';

  const { devicesAlarmsData } = useSelector(getMonitoringData);
  const getDeviceAlarmStatus = generateGetDeviceAlarmStatus(devicesAlarmsData);
  const isAlerting = getDeviceAlarmStatus(deviceGuid) !== STATUS_MAP.DEFAULT;

  return (
    <Popover
      content={
        <CorePointsCard
          style={{ margin: '-12px -16px' }}
          bordered={false}
          deviceGuid={deviceGuid}
          deviceType={deviceType}
          hideNoDataItems
        />
      }
    >
      <span
        style={{
          fontSize: FONT_SIZE,
          position: 'absolute',
          top: position.top + (BASE_REF_SIZE / 2 - FONT_SIZE / 2) / 2,
          left: placement === 'left' ? HORIZONTAL_MARGIN : undefined,
          right: placement === 'right' ? HORIZONTAL_MARGIN : undefined,
        }}
      >
        <span style={{ position: 'relative', display: 'block' }}>
          {isAlerting && <AlertingAnimation />}
          <Link to={generateDeviceRecordRoutePath({ guid: deviceGuid })}>
            <SmokeDetectorTwoTone
              style={{ position: 'relative', zIndex: 1 }}
              secondaryColor={isAlerting ? 'var(--manyun-error-color)' : undefined}
            />
          </Link>
        </span>
      </span>
    </Popover>
  );
}

function AlertingAnimation() {
  const styles = useSpring({
    loop: true,
    from: {
      transform: 'scale(1)',
      backgroundColor: 'transparent',
      opacity: 0,
    },
    to: {
      transform: 'scale(1.2)',
      backgroundColor: 'var(--manyun-error-color)',
      opacity: 1,
    },
  });

  return (
    <animated.span
      style={{
        position: 'absolute',
        zIndex: 0,
        top: 4,
        display: 'block',
        width: FONT_SIZE,
        height: FONT_SIZE,
        borderRadius: '50%',
        ...styles,
      }}
    />
  );
}
