import React from 'react';
import { Link } from 'react-router-dom';

import { Divider } from '@manyun/base-ui.ui.divider';
import { Empty } from '@manyun/base-ui.ui.empty';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Pagination } from '@manyun/base-ui.ui.pagination';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Typography } from '@manyun/base-ui.ui.typography';
import { Alarm } from '@manyun/monitoring.model.alarm';
import { fetchAlarms } from '@manyun/monitoring.service.fetch-alarms';
import { fetchAlarmsStatistics } from '@manyun/monitoring.service.fetch-alarms-statistics';
import { AlarmCard } from '@manyun/monitoring.ui.alarm-card';
import { AlarmDrawer } from '@manyun/monitoring.ui.alarm-drawer';

import styles from './alarms-container.module.less';

export type AlarmsContainerProps = {
  spaceGuids: string[];
};

type AlarmsCardsProps = {
  idc: string;
  alarms: Alarm[];
};

const AlarmsCards = ({ idc, alarms }: AlarmsCardsProps) => {
  const alarmRef = React.useRef<Alarm | null>(null);
  const [alarmDrawerVisible, setAlarmDrawerVisible] = React.useState(false);

  if (alarms.length <= 0) {
    return (
      <div
        style={{
          width: '100%',
          height: '100%',
          minHeight: 280,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Empty />
      </div>
    );
  }

  return (
    <>
      <Row wrap gutter={[16, 16]}>
        {alarms.map((alarm: Alarm) => (
          <Col key={alarm.id} span={12}>
            <AlarmCard
              bordered
              alarm={alarm}
              onClick={() => {
                alarmRef.current = alarm;
                setAlarmDrawerVisible(true);
              }}
            />
          </Col>
        ))}
      </Row>
      {alarmRef.current && alarmDrawerVisible && (
        <AlarmDrawer
          visible
          idc={idc}
          alarm={alarmRef.current!.toJSON()}
          onClose={() => {
            alarmRef.current = null;
            setAlarmDrawerVisible(false);
          }}
        />
      )}
    </>
  );
};

type Statistics = {
  powerSystem: number | null;
  hvacSystem: number | null;
  elvSystem: number | null;
  others: number | null;
};

export const AlarmsContainer = ({ spaceGuids }: AlarmsContainerProps) => {
  const [alarms, setAlarms] = React.useState<Alarm[]>([]);
  const [searchValue, setSearchValue] = React.useState<string>();
  const [paging, setPaging] = React.useState({
    pageSize: 10,
    pageNum: 1,
    total: 0,
  });
  const [loading, setLoading] = React.useState(true);
  const [statistics, setStatistics] = React.useState<Statistics>({
    powerSystem: null,
    hvacSystem: null,
    elvSystem: null,
    others: null,
  });

  const [idc, block, room] = spaceGuids;
  const getAlarms = React.useCallback(async () => {
    setLoading(true);
    const { error, data } = await fetchAlarms({
      idcTag: idc!,
      blockTags: [block!],
      roomTags: [room!],
      deviceName: searchValue,
      status: 'REMOVED',
      alarmStatus: ['ACTIVE', 'PROCESS', 'CONFIRMED'],
      triggerStatus: Alarm.UN_RESOLVED_ALARM_STATE_KEYS,
      pageNum: paging.pageNum,
      pageSize: paging.pageSize,
    });
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    const alarms = data?.data || [];
    setAlarms(alarms);
    setPaging({ ...paging, total: data?.total || 0 });
  }, [paging.pageNum, paging.pageSize, searchValue, idc, block, room]);
  const getAlarmsStatistics = React.useCallback(async () => {
    const { error, data } = await fetchAlarmsStatistics({
      idc,
      block,
      room,
    });
    if (error) {
      message.error(error.message);
      return;
    }
    const others = Object.keys(data).reduce<number | null>((count, deviceType) => {
      // eslint-disable-next-line eqeqeq
      if (deviceType != '1' && deviceType != '2' && deviceType != '3') {
        if (count === null) {
          count = 0;
        }
        count += data[deviceType] ?? 0;
      }
      return count;
    }, null);
    setStatistics({
      // REFACTORME @Jerry use `ConfigUtil` to get these concrete device types
      powerSystem: data['1'] ?? null,
      hvacSystem: data['2'] ?? null,
      elvSystem: data['3'] ?? null,
      others,
    });
  }, [idc, block, room]);

  React.useEffect(() => {
    getAlarms();
    getAlarmsStatistics();
    const interval = window.setInterval(() => {
      getAlarms();
      getAlarmsStatistics();
    }, 15 * 1000);

    return () => {
      window.clearInterval(interval);
    };
  }, [getAlarms, getAlarmsStatistics]);

  const showPowerSystemStatistics = statistics.powerSystem !== null;
  const showHvacSystemStatistics = statistics.hvacSystem !== null;
  const showElvSystemStatistics = statistics.elvSystem !== null;
  const showOthersStatistics = statistics.others !== null;

  return (
    <Space
      direction="vertical"
      size={16}
      style={{
        width: '100%',
        height: '100%',
        overflow: 'hidden',
      }}
    >
      {alarms.length > 0 &&
        (showPowerSystemStatistics ||
          showHvacSystemStatistics ||
          showElvSystemStatistics ||
          showOthersStatistics) && (
          <Space
            className={styles.statisticsContainer}
            size={4}
            split={<Divider type="vertical" />}
          >
            {showPowerSystemStatistics && (
              <Space>
                <Typography.Text>强电</Typography.Text>
                <Typography.Title level={4}>{statistics.powerSystem}</Typography.Title>
              </Space>
            )}
            {showHvacSystemStatistics && (
              <Space>
                <Typography.Text>暖通</Typography.Text>
                <Typography.Title level={4}>{statistics.hvacSystem}</Typography.Title>
              </Space>
            )}
            {showElvSystemStatistics && (
              <Space>
                <Typography.Text>弱电</Typography.Text>
                <Typography.Title level={4}>{statistics.elvSystem}</Typography.Title>
              </Space>
            )}
            {showOthersStatistics && (
              <Space>
                <Typography.Text>其他</Typography.Text>
                <Typography.Title level={4}>{statistics.others}</Typography.Title>
              </Space>
            )}
          </Space>
        )}
      <Space
        style={{
          width: '100%',
          justifyContent: 'space-between',
        }}
      >
        <Input.Search
          placeholder="请输入设备名称"
          onSearch={text => {
            setSearchValue(text);
            setPaging({ ...paging, pageNum: 1, pageSize: 10 });
          }}
        />
        <Link to="/">历史告警</Link>
      </Space>
      <Spin style={{ width: '100%', height: '100%' }} spinning={loading}>
        <AlarmsCards idc={idc} alarms={alarms} />
      </Spin>
      <Pagination
        current={paging.pageNum}
        pageSize={paging.pageSize}
        total={paging.total}
        hideOnSinglePage
        style={{ float: 'right' }}
        onChange={(pageNum, pageSize) => {
          setPaging({ ...paging, pageNum, pageSize });
        }}
      />
    </Space>
  );
};
