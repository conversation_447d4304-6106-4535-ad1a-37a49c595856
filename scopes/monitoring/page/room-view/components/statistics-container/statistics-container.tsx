import React from 'react';

import { Typography } from '@manyun/base-ui.ui.typography';

// @ts-ignore: fix legacy codes
import DesignArchitecture from '@manyun/dc-brain.legacy.pages/room-monitoring/components/design-architecture';
// @ts-ignore: fix legacy codes
import { RoomStatistics } from '@manyun/dc-brain.legacy.pages/room-monitoring/components/room-statistics';

export type StatisticsContainerProps = {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  statisticsData: any;
  spaceGuids: string[];
};

export function StatisticsContainer({ statisticsData, spaceGuids }: StatisticsContainerProps) {
  return (
    <div>
      <Typography.Title level={5}>概览数据</Typography.Title>
      <RoomStatistics statisticsData={statisticsData} />
      <DesignArchitecture idc={spaceGuids[0]} block={spaceGuids[1]} />
    </div>
  );
}
