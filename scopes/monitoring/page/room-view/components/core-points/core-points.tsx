import React from 'react';
import { useSelector } from 'react-redux';

import { Divider } from '@manyun/base-ui.ui.divider';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Typography } from '@manyun/base-ui.ui.typography';

import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { PointsLineModalButton } from '@manyun/monitoring.chart.points-line';
import { PointsStateLineModalButton } from '@manyun/monitoring.chart.points-state-line';
import type { Point } from '@manyun/monitoring.model.point';
import { getMonitoringData } from '@manyun/monitoring.state.subscriptions';
import { PointDataRenderer } from '@manyun/monitoring.ui.point-data-renderer';
import { generateGetPointMonitoringData } from '@manyun/monitoring.util.get-monitoring-data';
import { selectPointsEntities } from '@manyun/resource-hub.state.points';

import { getCurrentConfig } from '@manyun/dc-brain.legacy.redux/selectors/configSelectors';
import { getSpaceGuidMap } from '@manyun/dc-brain.legacy.utils';

/**
 * 获取包间核心测点数据
 * @param isRacksRoom true - IT包间，false - 设施包间
 * @returns 测点code
 */
const useCorePoints = (isRacksRoom: boolean, deviceType: string) => {
  let codes;
  if (isRacksRoom) {
    codes = ['1024000', '1012000', '1064000', '1065000'];
  } else {
    codes = ['1015000', '1016000', '1064000', '1065000'];
  }
  const pointKeys = codes.map((val: string) => `${deviceType}.${val}`);
  return pointKeys;
};

export type CorePointsProps = {
  isRacksRoom: boolean; // IT包间 - true
  spaceGuid: string;
  isLoading: boolean;
};

export function CorePoints({ isRacksRoom, spaceGuid, isLoading }: CorePointsProps) {
  const configUtil = new ConfigUtil(useSelector(getCurrentConfig));
  const roomDeviceType = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_ROOM)!;
  const corePointsData = useSelector(
    selectPointsEntities(useCorePoints(isRacksRoom, roomDeviceType))
  );

  const { idc } = getSpaceGuidMap(spaceGuid);
  const { devicesRealtimeData, devicesAlarmsData } = useSelector(getMonitoringData);
  const getPointMonitoringData = generateGetPointMonitoringData({
    realtimeData: devicesRealtimeData,
    alarmsData: devicesAlarmsData,
    configUtil,
  });

  const renderPointValue = (point: Point) => {
    const isAI = point.dataType.code === 'AI';
    const realTimeData = getPointMonitoringData(
      { deviceGuid: spaceGuid, deviceType: roomDeviceType },
      isAI
        ? {
            hardCodedPointCode: point.code,
            formatted: true,
          }
        : {
            hardCodedPointCode: point.code,
            reflected: true,
          }
    );
    const btn = <PointDataRenderer key={'renderer_' + point.code} data={realTimeData} />;
    const pointGuids = [
      {
        deviceGuid: spaceGuid,
        pointCode: point.code,
        unit: point.unit,
      },
    ];
    const seriesOption = [{ name: point.name }];
    return isAI ? (
      <PointsLineModalButton
        idcTag={idc!}
        btnText={btn}
        modalText={point.name}
        pointGuids={pointGuids}
        seriesOption={seriesOption}
      />
    ) : (
      <PointsStateLineModalButton
        idcTag={idc!}
        btnText={btn}
        modalText={point.name}
        pointGuids={pointGuids}
        seriesOption={seriesOption}
        validLimitsMap={point.validLimits}
      />
    );
  };

  return (
    <Space direction="vertical" size={16} style={{ width: '581px' }}>
      <Typography.Title level={5}>核心点位</Typography.Title>
      <Spin spinning={isLoading} style={{ width: '100%', minHeight: 72 }}>
        <Space
          style={{ width: '100%', justifyContent: 'space-between' }}
          split={<Divider style={{ height: 24 }} type="vertical" />}
        >
          {corePointsData.map((point: Point) => {
            return (
              <div key={point.id}>
                <Typography.Text type="secondary">{point.name}</Typography.Text>
                <div>{renderPointValue(point)}</div>
              </div>
            );
          })}
        </Space>
      </Spin>
    </Space>
  );
}
