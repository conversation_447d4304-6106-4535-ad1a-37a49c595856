import get from 'lodash.get';
import uniq from 'lodash.uniq';
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import useDeepCompareEffect from 'use-deep-compare-effect';

import { Card } from '@manyun/base-ui.ui.card';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType } from '@manyun/base-ui.ui.table';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import { syncCommonDataAction } from '@manyun/dc-brain.state.common';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { PointsLineModalButton } from '@manyun/monitoring.chart.points-line';
import { PointsStateLineModalButton } from '@manyun/monitoring.chart.points-state-line';
import {
  generateRoomMonitoringUrl,
  generateSpaceOrDeviceRoutePath,
} from '@manyun/monitoring.route.monitoring-routes';
import {
  SUBSCRIPTIONS_MODE,
  getMonitoringData,
  addRealtimeNAlarmsDataSubscriptionActionCreator as subscribe,
  removeRealtimeNAlarmsDataSubscriptionActionCreator as unsubscribe,
} from '@manyun/monitoring.state.subscriptions';
import { PointDataRenderer } from '@manyun/monitoring.ui.point-data-renderer';
import { generateGetPointMonitoringData } from '@manyun/monitoring.util.get-monitoring-data';
import { Device } from '@manyun/resource-hub.model.device';
import { fetchDeviceByGuid } from '@manyun/resource-hub.service.fetch-device-by-guid';

import { STATUS_MAP } from '@manyun/dc-brain.legacy.constants/statusColor';
// @ts-ignore
import { getChildrenActionCreator } from '@manyun/dc-brain.legacy.redux/__next/device/device.action';
import {
  selectChildDevices, // @ts-ignore
  selectDevicesCache,
} from '@manyun/dc-brain.legacy.redux/__next/device/device.selector';
// @ts-ignore
import { getCommonData } from '@manyun/dc-brain.legacy.redux/selectors/commonSelectors';
// @ts-ignore: Could not find a declaration file
import { getCurrentConfig } from '@manyun/dc-brain.legacy.redux/selectors/configSelectors';
import { generateGetDeviceAlarmStatus } from '@manyun/dc-brain.legacy.utils/device';

/**
 * 获取子设备信息
 * @param guid 当前设备guid
 * @returns
 */
const useChildDevices = (guid: string): Device[] => {
  const devicesCache: Record<string, unknown> = useSelector(selectDevicesCache);
  const deviceGuids: string[] | undefined = useSelector(selectChildDevices(guid));
  const childDevices =
    deviceGuids?.map((deviceGuid: string) => Device.fromApiObject(devicesCache[deviceGuid])) ?? [];
  const deviceTypes = uniq(childDevices.map((device: Device) => device.deviceCategory.level3));

  const dispatch = useDispatch();

  useDeepCompareEffect(() => {
    if (!deviceTypes.length) {
      return;
    }
    dispatch(
      syncCommonDataAction({
        strategy: {
          deviceTypesPointsDefinition: deviceTypes,
        },
      })
    );
  }, [deviceTypes, dispatch]);

  return childDevices;
};

function CircuitBreakerOnOffState({ device }: { device: Device }) {
  // @ts-ignore
  const { devicesRealtimeData, devicesAlarmsData } = useSelector(getMonitoringData);
  const configUtil = new ConfigUtil(useSelector(getCurrentConfig));
  const getPointMonitoringData = generateGetPointMonitoringData({
    realtimeData: devicesRealtimeData,
    alarmsData: devicesAlarmsData,
    configUtil,
  });
  const data = getPointMonitoringData(
    { deviceGuid: device.guid, deviceType: device.deviceCategory.level3 },
    {
      pointType: ConfigUtil.constants.pointCodes.CIRCUIT_BREAKER_ON_OFF_STATE,
      reflected: true,
      valueMappingIncluded: true,
    }
  );
  const btn = <PointDataRenderer data={data} />;
  const pointGuids = [{ deviceGuid: device.guid, pointCode: data.pointCode, unit: data.unit }];
  const seriesOption = [{ name: data.name }];

  return (
    <PointsStateLineModalButton
      idcTag={device.spaceGuid.idcTag!}
      btnText={btn}
      modalText={data.name}
      pointGuids={pointGuids}
      seriesOption={seriesOption}
      validLimitsMap={data.valueMapping!}
    />
  );
}

function ActivePowerSum({ device }: { device: Device }) {
  // @ts-ignore
  const { devicesRealtimeData, devicesAlarmsData } = useSelector(getMonitoringData);
  const configUtil = new ConfigUtil(useSelector(getCurrentConfig));
  const getPointMonitoringData = generateGetPointMonitoringData({
    realtimeData: devicesRealtimeData,
    alarmsData: devicesAlarmsData,
    configUtil,
  });
  const data = getPointMonitoringData(
    { deviceGuid: device.guid, deviceType: device.deviceCategory.level3 },
    {
      pointType: ConfigUtil.constants.pointCodes.ACTIVE_POWER_SUM,
      formatted: true,
    }
  );
  const btn = <PointDataRenderer data={data} />;
  const pointGuids = [{ deviceGuid: device.guid, pointCode: data.pointCode, unit: data.unit }];
  const seriesOption = [{ name: data.name }];

  return (
    <PointsLineModalButton
      idcTag={device.spaceGuid.idcTag!}
      btnText={btn}
      modalText={data.name}
      pointGuids={pointGuids}
      seriesOption={seriesOption}
    />
  );
}

function ChildDeviceStatus({ device }: { device: Device }) {
  // @ts-ignore
  const { devicesAlarmsData } = useSelector(getMonitoringData);
  const getAlarmStatus = generateGetDeviceAlarmStatus(devicesAlarmsData);
  const alarmStatus = getAlarmStatus(device.guid);
  if (alarmStatus === STATUS_MAP.ALARM || alarmStatus === STATUS_MAP.WARNING) {
    return <Tag color="error">异常</Tag>;
  }
  return <Tag color="success">正常</Tag>;
}

const columns: ColumnType<Device>[] = [
  {
    dataIndex: 'name',
    title: '抽屉式开关',
    render: (_: undefined, device) => (
      <Link
        to={generateSpaceOrDeviceRoutePath({
          guid: device.guid,
        })}
      >
        {device.name}
      </Link>
    ),
  },
  {
    key: 'status',
    title: '状态',
    render: (_: undefined, device) => <ChildDeviceStatus device={device} />,
  },
  {
    key: 'circuitBreakerStatus',
    title: '断路器合闸状态',
    render: (_: undefined, device) => <CircuitBreakerOnOffState device={device} />,
  },
  {
    key: 'totalActivePower',
    title: '总有功功率',
    render: (_: undefined, device) => <ActivePowerSum device={device} />,
  },
];

export type OutletCabinetProps = {
  deviceGuid: string;
};

export const OutletCabinet = ({ deviceGuid }: OutletCabinetProps) => {
  const { roomTypes } = useSelector(getCommonData);
  const [deviceData, setDeviceData] = useState<Device | null>(null);
  const dispatch = useDispatch();

  const childDevices = useChildDevices(deviceGuid);
  const deviceGuids = (childDevices || []).map(d => d.guid);

  const { name, roomType, spaceGuid, blockGuid, idc, block, room } = useMemo(() => {
    const { spaceGuid, roomType = '', name = '' } = deviceData || {};
    const {
      idcTag = '',
      blockTag = '',
      roomTag = '',
      blockGuid = '',
      roomGuid = '',
    } = spaceGuid || {};
    return {
      name,
      roomType: roomType,
      spaceGuid: roomGuid,
      blockGuid,
      idc: idcTag,
      block: blockTag,
      room: roomTag,
    };
  }, [deviceData]);

  // 订阅所有子设备数据
  useDeepCompareEffect(() => {
    const mode = SUBSCRIPTIONS_MODE.REALTIME_N_ALARMS;
    const moduleId = 'outlet-cabinet-card';
    if (blockGuid) {
      dispatch(
        subscribe({
          targets: [
            {
              mode,
              blockGuid,
              moduleId,
              deviceGuids,
            },
          ],
        })
      );
    }

    return () => {
      if (blockGuid) {
        dispatch(unsubscribe({ blockGuid, mode, moduleId }));
      }
    };
  }, [blockGuid, deviceGuids, dispatch]);

  // 查询子设备
  useEffect(() => {
    if (!spaceGuid) {
      return;
    }
    dispatch(
      getChildrenActionCreator({
        spaceGuid: spaceGuid,
        deviceGuid: deviceGuid,
      })
    );
  }, [dispatch, deviceGuid, spaceGuid]);

  useEffect(() => {
    fetchDeviceByGuid({ guid: deviceGuid }).then(({ data }) => setDeviceData(data));
  }, [deviceGuid]);

  if (!deviceData) {
    return null;
  }

  return (
    <Card
      bordered={false}
      title={
        <Link
          to={generateSpaceOrDeviceRoutePath({
            guid: deviceGuid,
          })}
        >
          {name}
        </Link>
      }
      extra={
        <>
          设备位置：
          {idc && block && room && (
            <Typography.Link
              onClick={e => {
                window.open(
                  generateRoomMonitoringUrl({
                    idc,
                    block,
                    room,
                  })
                );
              }}
            >
              {`${room} ${get(roomTypes, roomType!, roomType!)}`}
            </Typography.Link>
          )}
        </>
      }
      style={{ width: 517, maxHeight: 600, overflowY: 'auto' }}
      bodyStyle={{ padding: '12px' }}
    >
      <Table size="small" columns={columns} pagination={false} dataSource={childDevices || []} />
    </Card>
  );
};
