import React, { useMemo, useState } from 'react';
import { useHistory, useLocation } from 'react-router-dom';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';

import { Upload } from '@manyun/dc-brain.ui.upload';
import { generateNonPointRoutePath } from '@manyun/monitoring.route.monitoring-routes';
import { exportNonPoint } from '@manyun/monitoring.service.export-non-point';
import type { ApplyNonPintImport } from '@manyun/monitoring.service.import-non-point';
import { importNonPoint } from '@manyun/monitoring.service.import-non-point';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

import { getImportColumns } from './utils';

export function NonPointImport() {
  const history = useHistory();

  const [loading, setLoading] = useState<boolean>(false);
  const [exportLoading, setExportLoading] = useState<boolean>(false);

  const [pointsImport, setPointsImport] = useState<ApplyNonPintImport>();

  const handleFileExport = async () => {
    setExportLoading(true);
    const { error, data } = await exportNonPoint({});
    setExportLoading(false);
    if (error) {
      message.error(error.message);
    }
    return data;
  };

  const { search } = useLocation();
  const { blockGuid } = getLocationSearchMap<{ blockGuid: string | undefined }>(search);

  const errorAlert = useMemo(() => {
    if (!pointsImport) {
      return '';
    }
    return (
      <Space>
        <Typography.Text>经校验，正常: {pointsImport.correctTotal}项</Typography.Text>
        <Typography.Text>异常: {pointsImport.faultTotal}项</Typography.Text>
        <Typography.Text>导入失败，请修正数据后重新导入，当前仅展示异常内容 </Typography.Text>
      </Space>
    );
  }, [pointsImport]);

  return (
    <Card>
      <Space direction="vertical" style={{ width: '100%' }} size="middle">
        <Space size="middle">
          <Upload
            maxCount={1}
            showUploadList={false}
            customRequest={async ({ file, onError, onSuccess }) => {
              if (!blockGuid) {
                return;
              }
              setLoading(true);
              const fd = new FormData();
              fd.append('file', file);
              fd.append('blockGuid', blockGuid);
              const { data, error } = await importNonPoint(fd);
              setLoading(false);
              if (error) {
                message.error(error.message);
                onError!(new Error(error.message));
              } else {
                setPointsImport(data);
                onSuccess!(data);
                if (data.faultTotal === 0) {
                  message.success('导入成功！');
                  const idc = getSpaceGuidMap(blockGuid).idc;
                  if (idc) {
                    history.push(generateNonPointRoutePath({ idc }));
                  }
                }
              }
            }}
            accept=".csv,.xls,.xlsx"
          >
            <Button type="primary" loading={loading}>
              上传文件
            </Button>
          </Upload>
          <FileExport
            text="下载测点模版"
            filename="测点模版.xls"
            disabled={exportLoading}
            data={() => {
              return handleFileExport();
            }}
          />
        </Space>
        {pointsImport && <Alert message={errorAlert} type="info" />}
        <Table
          rowKey="id"
          scroll={{ x: 'max-content' }}
          tableLayout="fixed"
          dataSource={pointsImport ? pointsImport.excelCheckErrDtos : []}
          columns={getImportColumns()}
          loading={loading}
        />
      </Space>
    </Card>
  );
}
