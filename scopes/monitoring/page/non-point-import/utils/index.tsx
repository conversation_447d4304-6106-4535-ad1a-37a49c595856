import QuestionCircleOutlined from '@ant-design/icons/es/icons/QuestionCircleOutlined';
import React from 'react';

import { Space } from '@manyun/base-ui.ui.space';
import type { ColumnType } from '@manyun/base-ui.ui.table';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { ApplyImportExcelCheck } from '@manyun/monitoring.service.import-non-point';

type ImportColumnKey =
  | 'productModel'
  | 'name'
  | 'dataType'
  | 'unit'
  | 'validLimits'
  | 'deviceType'
  | 'code'
  | 'pointName'
  | 'formula'
  | 'invalidValues';
export const getImportColumns = () => {
  const getRenderItem = (record: ApplyImportExcelCheck, key: ImportColumnKey) => {
    const { errDto, errMessage } = record;
    return (
      <Space>
        {errMessage[key] ? (
          <Typography.Text type="danger">{errDto[key] ?? '--'}</Typography.Text>
        ) : (
          errDto[key] ?? '--'
        )}
        {errMessage[key] && (
          <Tooltip title={errMessage[key]}>
            <QuestionCircleOutlined style={{ fontSize: 12 }} />
          </Tooltip>
        )}
      </Space>
    );
  };

  const columns: ColumnType<ApplyImportExcelCheck>[] = [
    {
      title: '设备型号',
      dataIndex: 'productModel',
      fixed: 'left',
      render: (_, record) => {
        return getRenderItem(record, 'productModel');
      },
    },
    {
      title: '协议测点名称',
      dataIndex: 'name',
      render: (_, record) => {
        return getRenderItem(record, 'name');
      },
    },
    {
      title: '协议测点类型',
      dataIndex: 'dataType',
      render: (_, record) => {
        return getRenderItem(record, 'dataType');
      },
    },
    {
      title: '协议单位',
      dataIndex: 'unit',
      render: (_, record) => {
        return getRenderItem(record, 'unit');
      },
    },
    {
      title: '协议值含义',
      dataIndex: 'validLimits',
      render: (_, record) => {
        return getRenderItem(record, 'validLimits');
      },
    },
    {
      title: '设备类型',
      dataIndex: 'deviceType',
      render: (_, record) => {
        return getRenderItem(record, 'deviceType');
      },
    },
    {
      title: '测点ID',
      dataIndex: 'code',
      render: (_, record) => {
        return getRenderItem(record, 'code');
      },
    },
    {
      title: '标准测点名称',
      dataIndex: 'pointName',
      render: (_, record) => {
        return getRenderItem(record, 'pointName');
      },
    },
    {
      title: '加工表达式',
      dataIndex: 'formula',
      render: (_, record) => {
        return getRenderItem(record, 'formula');
      },
    },
    {
      title: '非法值',
      dataIndex: 'invalidValues',
      render: (_, record) => {
        return getRenderItem(record, 'invalidValues');
      },
    },
  ];
  return columns;
};
