import React, { useEffect, useState } from 'react';

import type { ValidLimits } from '@manyun/monitoring.chart.hook.use-mark-lines';
import { PointsLine } from '@manyun/monitoring.chart.points-line';
import type { PointGuid } from '@manyun/monitoring.chart.points-line';
import { PointsStateLine } from '@manyun/monitoring.chart.points-state-line';

// @ts-ignore
import { POINT_DATA_TYPE_CODE_MAP } from '@manyun/dc-brain.legacy.constants/point';

export function PointsChart() {
  const [idcTag, setIdcTag] = useState('');
  const [pointGuid, setPointGuid] = useState<PointGuid[]>([]);
  const [validLimits, setValidLimits] = useState<ValidLimits>({});
  const [dataType, setDataType] = useState('');

  useEffect(() => {
    window.addEventListener('message', msg => {
      setMessage(msg.data);
    });
    return () => {
      window.removeEventListener('message', () => {
        setIdcTag('');
      });
    };
  }, []);

  const setMessage = (msg: string) => {
    try {
      const {
        idcTag: _idcTag,
        pointGuid: _pointGuid,
        validLimits: _validLimits,
        dataType: _dataType,
      } = JSON.parse(msg);
      setIdcTag(_idcTag);
      setPointGuid([_pointGuid]);
      setValidLimits(_validLimits);
      setDataType(_dataType);
    } catch (error) {
      console.error(error);
    }
  };

  if (!idcTag) {
    return null;
  }

  return (
    <>
      {dataType === POINT_DATA_TYPE_CODE_MAP.DI ? (
        <PointsStateLine
          idcTag={idcTag}
          pointGuids={pointGuid}
          chartFunction="MAX"
          validLimitsMap={validLimits}
        />
      ) : (
        <PointsLine idcTag={idcTag} pointGuids={pointGuid} chartFunction="MAX" />
      )}
    </>
  );
}
