import React, { useEffect, useState } from 'react';
import { useHistory, useParams } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Typography } from '@manyun/base-ui.ui.typography';

import { getProfessionalRuleTemplateLocales } from '@manyun/monitoring.model.professional-rule-template';
import { PROFESSIONAL_RULE_TEMPLATE_LIST_ROUTE_PATH } from '@manyun/monitoring.route.monitoring-routes';
import { createProfessionalRuleTemplate } from '@manyun/monitoring.service.create-professional-rule-template';
import type { SvcQuery as Params } from '@manyun/monitoring.service.create-professional-rule-template';
import { fetchProfessionalRuleTemplateDetail } from '@manyun/monitoring.service.fetch-professional-rule-template-detail';
import { updateProfessionalRuleTemplate } from '@manyun/monitoring.service.update-professional-rule-template';
import { RuleTypeCascader } from '@manyun/monitoring.ui.rule-type-cascader';
import { DeviceTypeCascader } from '@manyun/resource-hub.ui.device-type-cascader';
import { ChangeTemplateIdSelect } from '@manyun/ticket.ui.change-template-id-select';

import styles from './professional-rule-template-editor.module.less';

export type ProfessionalRuleTemplateEditorProps = {
  mode: 'new' | 'edit';
};

type InitialValues = {
  name: string;
  ruleType: string[];
  deviceType: string;
  description: string;
  scope?: string;
  triggerDesc: string;
  preConditionDesc?: string;
  triggerInterval: number;
  recoverInterval: number;
  changeTemplateId: string;
  energyPointDesc?: string;
};

const locales = getProfessionalRuleTemplateLocales();

export function ProfessionalRuleTemplateEditor({ mode }: ProfessionalRuleTemplateEditorProps) {
  const [form] = Form.useForm();
  const { id } = useParams<{ id?: string }>();

  const [loading, setLoading] = useState(false);
  const [initialValues, setInitialValues] = useState<InitialValues>();
  useEffect(() => {
    (async () => {
      if (mode === 'edit') {
        setLoading(true);
        const { error, data } = await fetchProfessionalRuleTemplateDetail({ id: Number(id) });
        setLoading(false);
        if (error) {
          message.error(error.message);
          return;
        }
        if (data === null) {
          message.error('规则 ID 有误，请选择正确的规则模版数据！');
          return;
        }
        const value: InitialValues = {
          name: data.name,
          ruleType: [data.bizCode, data.ruleCode],
          deviceType: data.deviceType,
          description: data.description,
          scope: data.scope,
          triggerDesc: data.triggerDesc,
          preConditionDesc: data.preConditionDesc,
          triggerInterval: data.triggerInterval,
          recoverInterval: data.recoverInterval,
          changeTemplateId: data.changeTemplateId,
          energyPointDesc: data.energyPointDesc,
        };
        setInitialValues(value);
        form.setFieldsValue(value);
      }
    })();
  }, [form, id, mode]);

  // submit
  const history = useHistory();
  const [submitLoading, setSubmitLoading] = useState<boolean>(false);
  const submit = async () => {
    const values = await form.validateFields();
    setSubmitLoading(true);
    const params: Params = {
      name: values.name,
      bizCode: values.ruleType[0],
      ruleCode: values.ruleType[1],
      deviceType: values.deviceType,
      description: values.description,
      scope: values.scope,
      triggerDesc: values.triggerDesc,
      preConditionDesc: values.preConditionDesc,
      triggerInterval: values.triggerInterval,
      recoverInterval: values.recoverInterval,
      changeTemplateId: values.changeTemplateId,
      energyPointDesc: values.energyPointDesc,
    };
    if (mode === 'new') {
      const { error } = await createProfessionalRuleTemplate(params);
      if (error) {
        message.error(error.message);
      } else {
        message.success('创建成功！');
        history.push(PROFESSIONAL_RULE_TEMPLATE_LIST_ROUTE_PATH);
      }
    } else {
      const { error } = await updateProfessionalRuleTemplate({
        ...params,
        id: Number(id),
      });
      if (error) {
        message.error(error.message);
      } else {
        message.success('更新成功！');
        history.push(PROFESSIONAL_RULE_TEMPLATE_LIST_ROUTE_PATH);
      }
    }
    setSubmitLoading(false);
  };

  return (
    <Space className={styles.wrapper} size="large" direction="vertical">
      <Row className={styles.header}>
        <Typography.Text className={styles.title}>
          {`${mode === 'new' ? '新建' : '编辑'}专家规则模版`}
        </Typography.Text>
      </Row>
      <Spin spinning={loading}>
        <Form
          form={form}
          className={styles.form}
          labelCol={{ span: 8 }}
          labelAlign="right"
          initialValues={initialValues}
        >
          <Form.Item
            label={locales['ruleCode']}
            name="ruleType"
            wrapperCol={{ span: 4 }}
            required
            rules={[
              {
                required: true,
                message: '请选择',
              },
              {
                type: 'array',
                len: 2,
                message: '必须选择规则类型',
              },
            ]}
          >
            <RuleTypeCascader allowClear />
          </Form.Item>
          <Form.Item
            label={locales['deviceType']}
            name="deviceType"
            wrapperCol={{ span: 4 }}
            required
            rules={[
              {
                required: true,
                message: '请选择',
              },
            ]}
          >
            <DeviceTypeCascader
              numbered
              dataType={['snDevice']}
              category="categorycode"
              disabledTypeList={['C0', 'C1']}
              placeholder="请选择设备类型"
              allowClear
            />
          </Form.Item>
          <Form.Item
            label={locales['name']}
            name="name"
            wrapperCol={{ span: 8 }}
            required
            rules={[
              {
                required: true,
                message: '请输入',
              },
              {
                max: 50,
                message: '最多输入 50 个字符！',
              },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label={locales['description']}
            name="description"
            wrapperCol={{ span: 8 }}
            required
            rules={[
              {
                required: true,
                message: '请输入',
              },
            ]}
          >
            <Input.TextArea showCount maxLength={100} />
          </Form.Item>
          <Form.Item label={locales['scope']} name="scope" wrapperCol={{ span: 8 }}>
            <Input.TextArea showCount maxLength={100} />
          </Form.Item>
          <Form.Item
            label={locales['triggerDesc']}
            name="triggerDesc"
            wrapperCol={{ span: 8 }}
            required
            rules={[
              {
                required: true,
                message: '请输入',
              },
            ]}
          >
            <Input.TextArea showCount maxLength={100} />
          </Form.Item>
          <Form.Item
            label={locales['preConditionDesc']}
            name="preConditionDesc"
            wrapperCol={{ span: 8 }}
          >
            <Input.TextArea showCount maxLength={100} />
          </Form.Item>
          <Row>
            <Col span={2} />
            <Col span={9}>
              <Form.Item
                label={locales['triggerInterval']}
                name="triggerInterval"
                labelCol={{ span: 16 }}
                wrapperCol={{ span: 8 }}
                initialValue={0}
                required
                rules={[
                  {
                    required: true,
                    message: '请输入',
                  },
                  {
                    pattern: new RegExp('^\\d+$'),
                    message: '只能为非负整数',
                  },
                  {
                    type: 'number',
                    max: 86400,
                    message: '不能超过 86400',
                  },
                ]}
              >
                <InputNumber style={{ width: '100%' }} addonAfter="秒" />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                label={locales['recoverInterval']}
                name="recoverInterval"
                labelCol={{ span: 8 }}
                wrapperCol={{ span: 12 }}
                required
                initialValue={0}
                rules={[
                  {
                    required: true,
                    message: '请输入',
                  },
                  {
                    pattern: new RegExp('^\\d+$'),
                    message: '只能为非负整数',
                  },
                  {
                    type: 'number',
                    max: 86400,
                    message: '不能超过 86400',
                  },
                ]}
              >
                <InputNumber style={{ width: '100%' }} addonAfter="秒" />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            label={locales['changeTemplateId']}
            name="changeTemplateId"
            wrapperCol={{ span: 8 }}
            required
            rules={[
              {
                required: true,
                message: '请选择',
              },
            ]}
          >
            <ChangeTemplateIdSelect />
          </Form.Item>
          <Form.Item
            label={locales['energyPointDesc']}
            name="energyPointDesc"
            wrapperCol={{ span: 8 }}
          >
            <Input.TextArea showCount maxLength={100} />
          </Form.Item>
          <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
            <Space size="middle">
              <Button type="primary" onClick={() => submit()} loading={submitLoading}>
                提交
              </Button>
              <Button onClick={() => history.push(PROFESSIONAL_RULE_TEMPLATE_LIST_ROUTE_PATH)}>
                返回
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Spin>
    </Space>
  );
}
