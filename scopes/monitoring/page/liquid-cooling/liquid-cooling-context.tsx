import React from 'react';

export type LiqudSpace = {
  idc: string;
  block: string;
  room: string;
  spaceGuid: string;
};

type Values = {
  liquidSpace: LiqudSpace | undefined;
};

export type Handlers = {
  setLiquidSpace?: (space: LiqudSpace) => void;
};

const noop = () => {};
const initialValue: [Values, Handlers] = [
  {
    liquidSpace: undefined,
  },
  {
    setLiquidSpace: noop,
  },
];

export const LiquidCoolingContext = React.createContext<[Values, Handlers]>(initialValue);
