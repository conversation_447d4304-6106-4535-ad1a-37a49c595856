import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';

import { ThemeProvider as ChartThemeProvider } from '@manyun/base-ui.chart.theme';
import { FullScreen } from '@manyun/base-ui.ui.full-screen';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import { getSpaceGuid } from '@manyun/resource-hub.util.space-guid';

import { LiquidCoolMonitoring } from './components/liquid-cooling-monitoring';
import { LiquidTopologyGraphixPreview } from './components/liquid-cooling-monitoring/graphix-preview';
import type { LiqudSpace } from './liquid-cooling-context';
import { LiquidCoolingContext } from './liquid-cooling-context';
import styles from './liquid-cooling.module.less';

enum LiquidCool {
  Monitoring = 'Monitoring',
  Topology = 'Topology',
  Electric = 'Electric',
  Alarm = 'Alarm',
  Video = 'Video',
}

type LiquidCoolingType = `${LiquidCool}`;

const typesText = {
  [LiquidCool.Monitoring]: '液冷监控',
  [LiquidCool.Topology]: '液冷拓扑',
  [LiquidCool.Electric]: '电力拓扑',
  [LiquidCool.Alarm]: '告警监控',
  [LiquidCool.Video]: '视频监控',
};

const tabList = Object.keys(typesText).map(data => {
  return {
    label: typesText[data as LiquidCoolingType],
    value: data,
  };
});

export function LiquidCooling() {
  const { idc, block, room } = useParams<{ idc: string; block: string; room: string }>();
  const spaceGuid = getSpaceGuid(idc, block, room);
  const [activeTabKey, setActiveTabKey] = useState<LiquidCoolingType>('Monitoring');
  const [liquidSpace, setLiquidSpace] = useState<LiqudSpace>();
  useEffect(() => {
    if (idc && block && room && spaceGuid) {
      setLiquidSpace({
        idc,
        block,
        room,
        spaceGuid,
      });
    }
  }, [idc, block, room, spaceGuid]);

  if (!spaceGuid) {
    return null;
  }
  return (
    <LiquidCoolingContext.Provider value={[{ liquidSpace }, { setLiquidSpace }]}>
      <FullScreen>
        <ChartThemeProvider theme="datav_light">
          <div style={{ paddingBottom: 16, marginTop: 16 }}>
            <div className={styles.liquidCoolingTab}>
              <Tabs
                activeKey={activeTabKey}
                items={tabList.map(({ label, value }) => ({
                  label,
                  key: value,
                }))}
                onChange={key => {
                  if (key === LiquidCool.Alarm) {
                    window.open('/');
                  } else {
                    setActiveTabKey(key as LiquidCoolingType);
                  }
                }}
              />
              <FullScreen.Icon
                style={{
                  display: 'flex',
                  alignItems: 'center',
                }}
              />
            </div>
            <div>
              {activeTabKey === 'Monitoring' && spaceGuid && <LiquidCoolMonitoring />}
              {activeTabKey === 'Topology' && <LiquidTopologyGraphixPreview />}
            </div>
          </div>
        </ChartThemeProvider>
      </FullScreen>
    </LiquidCoolingContext.Provider>
  );
}
