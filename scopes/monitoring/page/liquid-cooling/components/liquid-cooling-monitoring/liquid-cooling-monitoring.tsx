import React, { useContext } from 'react';

import { Col, Row } from '@manyun/base-ui.ui.grid';

import { LiquidCoolingContext } from '../../liquid-cooling-context';
import { Alarm } from './alarm';
import { LiquidMonitorinGraphixPreview } from './graphix-preview';
import { ParameterSetting } from './parameter-setting';
import { Weather } from './weather';

export function LiquidCoolMonitoring() {
  const [{ liquidSpace }] = useContext(LiquidCoolingContext);
  return (
    <>
      {liquidSpace?.idc && liquidSpace.block ? (
        <Row gutter={[16, 16]}>
          <Col span={18}>
            <Row gutter={[16, 16]}>
              <Col span={24}>
                <LiquidMonitorinGraphixPreview />
              </Col>
              <Col span={24}>
                <ParameterSetting />
              </Col>
            </Row>
          </Col>
          <Col span={6}>
            <Row gutter={[16, 16]}>
              <Col span={24}>
                <Weather />
              </Col>
              <Col span={24}>
                <Alarm />
              </Col>
            </Row>
          </Col>
        </Row>
      ) : null}
    </>
  );
}
