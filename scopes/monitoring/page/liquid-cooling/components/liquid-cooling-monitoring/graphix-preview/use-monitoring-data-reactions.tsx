import React from 'react';
import { useSelector } from 'react-redux';
import { usePrevious } from 'react-use';

import debug from 'debug';

import type {
  AnimationSnapshotIn,
  AnyElementInstance,
  GroupElementInstance,
  ImageElementInstance,
  StoreInstance,
  TextElementInstance,
} from '@manyun/dc-brain.aura-graphix';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { getMonitoringData } from '@manyun/monitoring.state.subscriptions';
import {
  generateGetDeviceAlarmStatus,
  generateGetPointValue,
} from '@manyun/monitoring.util.get-monitoring-data';

import { getElementStates, updateElementAlertingAnimation, updatePointTextAttrs } from './utils';

const logger = debug('topology:hooks:useMonitoringDataReactions');

export type MonitoringDataReactionsHookOptions = {
  store: StoreInstance;
};

export const useMonitoringDataReactions = ({ store }: MonitoringDataReactionsHookOptions) => {
  const config = useSelector(selectCurrentConfig);
  const configUtil = React.useMemo(() => new ConfigUtil(config), [config]);
  const typeofAirEconomizer = configUtil.typeofDeviceGen(
    ConfigUtil.constants.deviceTypes.AIR_ECONOMIZER
  );
  const typeofCDU = configUtil.typeofDeviceGen(ConfigUtil.constants.deviceTypes.CDU);
  const typeofInterColumnSophisticatedAirCooledCRAC = configUtil.typeofDeviceGen(
    ConfigUtil.constants.deviceTypes.INTER_COLUMN_SOPHISTICATED_AIR_COOLED_CRAC
  );
  const typeofACSmartPDU = configUtil.typeofDeviceGen(
    ConfigUtil.constants.deviceTypes.AC_SMART_PDU
  );
  const { devicesRealtimeData, devicesAlarmsData } = useSelector(getMonitoringData);
  const getDeviceAlarmStatus = generateGetDeviceAlarmStatus(devicesAlarmsData);
  const prevAlarmsData = usePrevious(devicesAlarmsData);
  const prevReltimeData = usePrevious(devicesRealtimeData);
  React.useEffect(() => {
    const { airEconomizers, cdus, cracs, pdus, otherDevices, pointTexts } = groupByElemCustomType(
      store,
      {
        typeofAirEconomizer,
        typeofCDU,
        typeofInterColumnSophisticatedAirCooledCRAC,
        typeofACSmartPDU,
      }
    );
    const getPrevValue = generateGetPointValue(prevReltimeData, prevAlarmsData);
    const getValue = generateGetPointValue(devicesRealtimeData, devicesAlarmsData);
    airEconomizers.forEach(airEconomizer => {
      const { deviceGuid, deviceType } = airEconomizer.custom;
      const self = airEconomizer.findOne(
        (child: AnyElementInstance) => child.custom?.type === 'device_image'
      ) as ImageElementInstance;
      const fan = airEconomizer.findOne(
        (child: AnyElementInstance) => child.custom?.type === 'parts_fan'
      ) as ImageElementInstance;
      const sprayer = airEconomizer.findOne(
        (child: AnyElementInstance) => child.custom?.type === 'parts_sprayer'
      ) as ImageElementInstance;
      const pump = airEconomizer.findOne(
        (child: AnyElementInstance) => child.custom?.type === 'parts_pump'
      ) as ImageElementInstance;
      const elementConfigs = configUtil.getTopologyElementConfig(deviceType, 'LIQUID_MONITORING');
      if (!elementConfigs || !elementConfigs.statePointsExpressions) {
        logger(
          `element(${deviceType}) topology configs('statePointsExpressions') not found in topology(LIQUID_MONITORING) configs.`
        );
      } else {
        const prevStates = getElementStates(
          elementConfigs.statePointsExpressions,
          getPrevValue(deviceGuid)
        );
        const states = getElementStates(
          elementConfigs.statePointsExpressions,
          getValue(deviceGuid)
        );
        updateDevicePartsWorkingState(
          fan,
          prevStates.fans as ExpressionResults,
          states.fans as ExpressionResults,
          {
            spin: states.fans.working
              ? {
                  duration: 700,
                }
              : false,
          }
        );
        updateDevicePartsWorkingState(
          sprayer,
          prevStates.sprayers as ExpressionResults,
          states.sprayers as ExpressionResults,
          {
            pulse: states.sprayers.working,
          }
        );
        updateDevicePartsWorkingState(
          pump,
          prevStates.pumps as ExpressionResults,
          states.pumps as ExpressionResults,
          {
            spin: states.pumps.working
              ? {
                  duration: 1000,
                }
              : false,
          }
        );
      }
      const { isAlerting } = getDeviceAlarmStatus(deviceGuid);
      updateElementAlertingAnimation(self, { isHighlighting: isAlerting, isAlerting });
    });
    cdus.forEach(cdu => {
      const { deviceGuid, deviceType } = cdu.custom;
      const self = cdu.findOne(
        (child: AnyElementInstance) => child.custom?.type === 'device_image'
      ) as ImageElementInstance;
      const pump = cdu.findOne(
        (child: AnyElementInstance) => child.custom?.type === 'parts_pump'
      ) as ImageElementInstance;
      const elementConfigs = configUtil.getTopologyElementConfig(deviceType, 'LIQUID_MONITORING');
      if (!elementConfigs || !elementConfigs.statePointsExpressions) {
        logger(
          `element(${deviceType}) topology configs('statePointsExpressions') not found in topology(LIQUID_MONITORING) configs.`
        );
      } else {
        const prevStates = getElementStates(
          elementConfigs.statePointsExpressions,
          getPrevValue(deviceGuid)
        );
        const states = getElementStates(
          elementConfigs.statePointsExpressions,
          getValue(deviceGuid)
        );
        updateDevicePartsWorkingState(
          pump,
          prevStates.pumps as ExpressionResults,
          states.pumps as ExpressionResults,
          {
            spin: states.pumps.working
              ? {
                  duration: 1000,
                }
              : false,
          }
        );
      }
      const { isAlerting } = getDeviceAlarmStatus(deviceGuid);
      updateElementAlertingAnimation(self, { isHighlighting: isAlerting, isAlerting });
    });
    cracs.forEach(crac => {
      const { deviceGuid, deviceType } = crac.custom;
      const self = crac.findOne(
        (child: AnyElementInstance) => child.custom?.type === 'device_image'
      ) as ImageElementInstance;
      const fan = crac.findOne(
        (child: AnyElementInstance) => child.custom?.type === 'parts_fan'
      ) as ImageElementInstance;
      const elementConfigs = configUtil.getTopologyElementConfig(deviceType, 'LIQUID_MONITORING');
      if (!elementConfigs || !elementConfigs.statePointsExpressions) {
        logger(
          `element(${deviceType}) topology configs('statePointsExpressions') not found in topology(LIQUID_MONITORING) configs.`
        );
      } else {
        const prevStates = getElementStates(
          elementConfigs.statePointsExpressions,
          getPrevValue(deviceGuid)
        );
        const states = getElementStates(
          elementConfigs.statePointsExpressions,
          getValue(deviceGuid)
        );
        updateDevicePartsWorkingState(
          fan,
          prevStates.fans as ExpressionResults,
          states.fans as ExpressionResults,
          {
            spin: states.fans.working
              ? {
                  duration: 700,
                }
              : false,
          }
        );
      }
      const { isAlerting } = getDeviceAlarmStatus(deviceGuid);
      updateElementAlertingAnimation(self, { isHighlighting: isAlerting, isAlerting });
    });
    pdus.forEach(pdu => {
      const { deviceGuid, deviceType } = pdu.custom;
      const pduImage = pdu.findOne(
        (child: AnyElementInstance) => child.custom?.type === 'device_image'
      ) as ImageElementInstance;
      const elementConfigs = configUtil.getTopologyElementConfig(deviceType, 'LIQUID_MONITORING');
      if (!elementConfigs || !elementConfigs.statePointsExpressions) {
        logger(
          `element(${deviceType}) topology configs('statePointsExpressions') not found in topology(LIQUID_MONITORING) configs.`
        );
      } else {
        const prevStates = getElementStates(
          elementConfigs.statePointsExpressions,
          getPrevValue(deviceGuid)
        );
        const states = getElementStates(
          elementConfigs.statePointsExpressions,
          getValue(deviceGuid)
        );
        updatePDUWorkingState(
          pduImage,
          prevStates.self as ExpressionResults,
          states.self as ExpressionResults
        );
      }
      const { isAlerting } = getDeviceAlarmStatus(deviceGuid);
      updateElementAlertingAnimation(pduImage, { isHighlighting: isAlerting, isAlerting });
    });
    otherDevices.forEach(element => {
      const { deviceGuid } = element.custom;
      const self = (element?.findOne?.(
        (child: AnyElementInstance) => child.custom?.type === 'device_image'
      ) ??
        element?.findOne?.(
          (child: AnyElementInstance) => child.custom?.type === 'device'
        )) as ImageElementInstance;
      const { isAlerting } = getDeviceAlarmStatus(deviceGuid);

      if (self) {
        updateElementAlertingAnimation(self, { isHighlighting: isAlerting, isAlerting });
      }
    });
    pointTexts.forEach(pointText => {
      updatePointTextAttrs(pointText, { getPrevValue, getValue });
    });
  }, [
    prevReltimeData,
    prevAlarmsData,
    devicesRealtimeData,
    devicesAlarmsData,
    store,
    typeofAirEconomizer,
    configUtil,
    getDeviceAlarmStatus,
    typeofCDU,
    typeofInterColumnSophisticatedAirCooledCRAC,
    typeofACSmartPDU,
  ]);
};

type TypeofSomeDeviceFn = (deviceType: string) => boolean;

function groupByElemCustomType(
  store: StoreInstance,
  {
    typeofAirEconomizer,
    typeofCDU,
    typeofInterColumnSophisticatedAirCooledCRAC,
    typeofACSmartPDU,
  }: {
    typeofAirEconomizer: TypeofSomeDeviceFn;
    typeofCDU: TypeofSomeDeviceFn;
    typeofInterColumnSophisticatedAirCooledCRAC: TypeofSomeDeviceFn;
    typeofACSmartPDU: TypeofSomeDeviceFn;
  }
) {
  const airEconomizers: GroupElementInstance[] = [];
  const cdus: GroupElementInstance[] = [];
  const cracs: GroupElementInstance[] = [];
  const pdus: GroupElementInstance[] = [];
  const otherDevices: GroupElementInstance[] = [];
  const pointTexts: TextElementInstance[] = [];

  store.activePage?.children.forEach(element => {
    const customType = element.custom?.type;
    const isDevice = customType === 'device';
    const isPointText = customType === 'point-text';
    if (isDevice) {
      const { deviceType } = element.custom;
      if (typeofAirEconomizer(deviceType)) {
        airEconomizers.push(element as unknown as GroupElementInstance);
      } else if (typeofCDU(deviceType)) {
        cdus.push(element as unknown as GroupElementInstance);
      } else if (typeofInterColumnSophisticatedAirCooledCRAC(deviceType)) {
        cracs.push(element as unknown as GroupElementInstance);
      } else if (typeofACSmartPDU(deviceType)) {
        pdus.push(element as unknown as GroupElementInstance);
      } else {
        otherDevices.push(element as unknown as GroupElementInstance);
      }
    } else if (isPointText) {
      pointTexts.push(element as unknown as TextElementInstance);
    }
  });

  return {
    airEconomizers,
    cdus,
    cracs,
    pdus,
    otherDevices,
    pointTexts,
  };
}

type ExpressionResults = {
  working: boolean;
  notWorking: boolean;
};
function updateElementWorkingState(
  element: ImageElementInstance,
  prevStates: ExpressionResults,
  states: ExpressionResults,
  callback: (
    result: 'noisy-data' | boolean,
    infos: {
      filename: string;
      ext: string;
    }
  ) => void
) {
  const filename = element.src.substring(element.src.lastIndexOf('/') + 1);
  const ext = filename.substring(filename.lastIndexOf('.') + 1);
  const infos = { filename, ext };

  if (states.working === states.notWorking && element.visible) {
    callback('noisy-data', infos);
    return;
  }
  if (prevStates.working !== states.working || prevStates.notWorking !== states.notWorking) {
    callback(states.working, infos);
  }
}

function updateDevicePartsWorkingState(
  element: ImageElementInstance,
  prevStates: ExpressionResults,
  states: ExpressionResults,
  animation: AnimationSnapshotIn
) {
  updateElementWorkingState(element, prevStates, states, (result, { filename, ext }) => {
    // hide the parts if the point data is not valid
    if (result === 'noisy-data') {
      element.set({
        visible: false,
      });
      return;
    }
    const newFilename = `${result ? 'working' : 'not-working'}.${ext}`;
    const newSrc = element.src.replace(filename, newFilename);
    element.set({
      visible: true,
      src: newSrc,
      animation,
    });
  });
}

function updatePDUWorkingState(
  element: ImageElementInstance,
  prevStates: ExpressionResults,
  states: ExpressionResults
) {
  updateElementWorkingState(element, prevStates, states, (result, { filename, ext }) => {
    let newFilename = 'default';
    if (result === 'noisy-data') {
      newFilename = 'no-data';
    } else {
      newFilename = result ? 'on' : 'off';
    }
    const newSrc = element.src.replace(filename, `${newFilename}.${ext}`);
    element.set({
      src: newSrc,
    });
  });
}
