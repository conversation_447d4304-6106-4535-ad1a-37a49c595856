import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';
import { useShallowCompareEffect } from 'react-use';

import debug from 'debug';
import cloneDeep from 'lodash.clonedeep';

import { message } from '@manyun/base-ui.ui.message';

import type { AnyElementInstance, StoreInstance } from '@manyun/dc-brain.aura-graphix';
import {
  AuraGraphixPreview as Preview,
  ThemeCompositions,
  calculatePointOnLine,
  createStore,
  flattenDeepElements,
  getLineLength,
  getPoints2d,
} from '@manyun/dc-brain.aura-graphix';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import '@manyun/dc-brain.ui.custom-shapes/alerting-marker';
import '@manyun/dc-brain.ui.custom-shapes/highlight';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import type { PointGuid } from '@manyun/monitoring.chart.points-state-line';
import type { LiquidCoolingRouteParams } from '@manyun/monitoring.route.monitoring-routes';
import { fetchTopology } from '@manyun/monitoring.service.fetch-topology';
import {
  SUBSCRIPTIONS_MODE,
  getMonitoringData,
  addRealtimeNAlarmsDataSubscriptionActionCreator as subscribe,
  removeRealtimeNAlarmsDataSubscriptionActionCreator as unsubscribe,
} from '@manyun/monitoring.state.subscriptions';
import { relatedElementUtil } from '@manyun/monitoring.state.topology';
import { GraphPointLineModal } from '@manyun/monitoring.ui.graph-point-line-modal';
import {
  generateGetDeviceAlarmStatus,
  generateGetPointValue,
} from '@manyun/monitoring.util.get-monitoring-data';
import { generateDeviceRecordRoutePath } from '@manyun/resource-hub.route.resource-routes';
import { CorePointsCard } from '@manyun/resource-hub.ui.core-points-card';
import { getSpaceGuid } from '@manyun/resource-hub.util.space-guid';

import { PresetCard } from '../../preset-card';
import waterLeakMarkerPng from './assets/marker.png';
import { useMonitoringDataReactions } from './use-monitoring-data-reactions';
import { fixElementAttrs, useTypeofFuncs } from './utils';

const logger = debug('room-monitoring:infra-room-graph-preview');

const mode = SUBSCRIPTIONS_MODE.REALTIME_N_ALARMS;
const moduleId = 'liquid-monitoring-graphix-preview';
const WATER_LEAK_MARKER_HIGHLIGHT_ALERTING_COLOR = 'rgb(245, 34, 45)';

export function LiquidMonitorinGraphixPreview() {
  const { idc, block, room } = useParams<LiquidCoolingRouteParams>();
  const blockGuid = getSpaceGuid(idc, block)!;
  const roomGuid = getSpaceGuid(idc, block, room)!;
  const [store, setStore] = React.useState<StoreInstance | null>(null);
  const dispatch = useDispatch();
  const config = useSelector(selectCurrentConfig);
  const configUtil = React.useMemo(() => new ConfigUtil(config), [config]);

  const { typeofWaterLeakDetector, typeofPositioningWaterLeakDetector } =
    useTypeofFuncs(configUtil);

  useShallowCompareEffect(() => {
    (async () => {
      const { data, error } = await fetchTopology({
        blockGuid: roomGuid,
        topologyType: 'LIQUID_MONITORING',
      });
      if (error) {
        message.error(error.message);
        return;
      }
      if (!data || !data.graph.pages.length) {
        return;
      }
      const __store = createStore();
      const { pages, ...rest } = data.graph;

      const clone = cloneDeep({
        ...rest,
        pages: pages.map(({ children, ...restPage }) => ({
          ...restPage,
          background: 'transparent',
          children: flattenDeepElements(children),
        })),
      });
      fixElementAttrs(clone.pages[0].children);

      const deviceGuids: string[] = [];
      const waterLeakMarkers: unknown[] = [];
      clone.pages[0].children.forEach(elem => {
        if (elem.custom?.type === 'device') {
          deviceGuids.push(elem.custom.deviceGuid);
          if (Array.isArray(elem.children)) {
            elem.children.push(
              {
                id: `${elem.id}_$$_highlight`,
                type: 'highlight',
                width: elem.width,
                height: elem.height,
                locked: true,
                animating: false,
              },
              {
                id: `${elem.id}_$$_alerting-marker`,
                type: 'alerting-marker',
                x: elem.width - 13,
                y: 13,
                locked: true,
                animating: false,
              }
            );
          }

          if (typeofPositioningWaterLeakDetector(elem.custom.deviceType)) {
            if (elem.type === 'line') {
              waterLeakMarkers.push({
                id: `${elem.id}_$$_water-leak-marker_group`,
                type: 'group',
                x: 0,
                y: 0,
                width: 36,
                height: 36,
                locked: true,
                visible: false,
                children: [
                  {
                    id: `${elem.id}_$$_water-leak-marker_highlight`,
                    type: 'highlight',
                    shape: 'circle',
                    x: 18,
                    y: 18,
                    radius: 36,
                    fill: WATER_LEAK_MARKER_HIGHLIGHT_ALERTING_COLOR,
                    stroke: WATER_LEAK_MARKER_HIGHLIGHT_ALERTING_COLOR,
                    shadowColor: WATER_LEAK_MARKER_HIGHLIGHT_ALERTING_COLOR,
                    locked: true,
                    animating: true,
                  },
                  {
                    id: `${elem.id}_$$_water-leak-marker`,
                    type: 'image',
                    src: waterLeakMarkerPng,
                    x: 0,
                    y: 0,
                    width: 36,
                    height: 36,
                    locked: true,
                  },
                ],
              });
            } else {
              logger('Found a PositioningWaterLeakDetector which is not type of a line: ', elem);
            }
          }
        }
      });

      if (waterLeakMarkers.length > 0) {
        clone.pages[0].children.push(...waterLeakMarkers);
      }

      __store.loadJSON(clone).then(() => {
        setStore(__store);
      });

      dispatch(subscribe({ mode, blockGuid, moduleId, deviceGuids }));
    })();

    return () => {
      setStore(null);
      dispatch(unsubscribe({ mode, blockGuid, moduleId }));
    };
  }, [blockGuid, dispatch]);

  const { devicesRealtimeData, devicesAlarmsData } = useSelector(getMonitoringData);
  const getDeviceAlarmStatus = generateGetDeviceAlarmStatus(devicesAlarmsData);
  const checkAlerting = React.useCallback(
    deviceGuid => getDeviceAlarmStatus(deviceGuid).isAlerting,
    [getDeviceAlarmStatus]
  );

  React.useEffect(() => {
    if (!store) {
      return;
    }

    const waterLeakDetectors: unknown[] = [];
    const positioningWaterLeakDetectors: unknown[] = [];
    store?.activePage?.children.forEach(child => {
      const isDevice = child.custom?.type === 'device';
      const isDeviceGroup = child.custom?.type === 'device_group';
      if (isDevice || isDeviceGroup) {
        let deviceElement = child;
        if (isDeviceGroup) {
          deviceElement = child.children.find(
            (el: { custom: { type: string } }) => el.custom?.type === 'device'
          );
        }
        const deviceType = deviceElement.custom.deviceType;
        if (typeofWaterLeakDetector(deviceType)) {
          waterLeakDetectors.push(deviceElement);
        } else if (typeofPositioningWaterLeakDetector(deviceType)) {
          positioningWaterLeakDetectors.push(deviceElement);
        }
      }
    });
    const getValue = generateGetPointValue(devicesRealtimeData, devicesAlarmsData);

    waterLeakDetectors.forEach(waterLeakDetector => {
      // @ts-expect-error: TS7031 because waterLeakDetector type unknown
      const { deviceGuid } = waterLeakDetector.custom;
      const isAlerting = checkAlerting(deviceGuid);
      // @ts-expect-error: TS7031 because waterLeakDetector type unknown
      waterLeakDetector.set({
        animation: {
          blink: isAlerting,
        },
      });
    });
    positioningWaterLeakDetectors.forEach(positioningWaterLeakDetector => {
      // @ts-expect-error: TS7031 because positioningWaterLeakDetectors type unknown
      const { deviceGuid } = positioningWaterLeakDetector.custom;

      const isAlerting = checkAlerting(deviceGuid);
      // @ts-expect-error: TS7031 because positioningWaterLeakDetectors type unknown
      positioningWaterLeakDetector.set({
        animation: {
          blink: isAlerting,
        },
      });

      // @ts-expect-error: TS7031 because positioningWaterLeakDetectors type unknown
      if (positioningWaterLeakDetector.type !== 'line') {
        logger(
          'Found a PositioningWaterLeakDetector which is not type of a line, the id is %s',
          // @ts-expect-error: TS7031 because positioningWaterLeakDetectors type unknown
          positioningWaterLeakDetector.id
        );
        return;
      }

      // @ts-expect-error: TS7031 because positioningWaterLeakDetectors type unknown
      const markerGroupElementId = `${positioningWaterLeakDetector.id}_$$_water-leak-marker_group`;
      const markerGroupElement = store.activePage?.findOne(markerGroupElementId);

      // TODO: @Jerry replace `'1001000'` with the corresponding predefined one
      const { value: leakPosition } = getValue(deviceGuid)('1001000');
      if (leakPosition <= 0) {
        if (markerGroupElement) {
          markerGroupElement.set({
            visible: false,
          });
        }
        return;
      }
      // TODO: @Jerry replace `'1001000'` with the corresponding predefined one
      const { value: length } = getValue(deviceGuid)('1002000');
      const { length: abstractLength, segments } = getLineLength(
        // @ts-expect-error: TS7031 because positioningWaterLeakDetectors type unknown
        positioningWaterLeakDetector.points
      );
      const abstractLeakPosition = (leakPosition / length) * abstractLength;
      let segmentIdx = -1;
      let leftLength = abstractLeakPosition;
      segments.reduce((sum, segmentLength, idx) => {
        const next = sum + segmentLength;
        if (abstractLeakPosition > sum && abstractLeakPosition <= next) {
          segmentIdx = idx;
        }
        if (segmentIdx <= -1) {
          leftLength -= segmentLength;
        }

        return next;
      }, 0);
      if (segmentIdx <= -1) {
        return;
      }
      // @ts-expect-error: TS7031 because positioningWaterLeakDetectors type unknown
      const points2d = getPoints2d(positioningWaterLeakDetector.points);
      const refPointStart = points2d[segmentIdx];
      const refPointEnd = points2d[segmentIdx + 1];
      const point = calculatePointOnLine([...refPointStart, ...refPointEnd], leftLength);
      if (!point) {
        return;
      }
      logger('Found leak position @ point: ', point);
      if (markerGroupElement) {
        // @ts-expect-error: TS7031 because positioningWaterLeakDetectors type unknown
        markerGroupElement.set({
          // @ts-expect-error: TS7031 because positioningWaterLeakDetectors type unknown
          x: point[0] - markerGroupElement.width / 2,
          // @ts-expect-error: TS7031 because positioningWaterLeakDetectors type unknown
          y: point[1] - markerGroupElement.height / 2,
          visible: true,
        });
      }
    });
  }, [
    store,
    configUtil,
    typeofWaterLeakDetector,
    typeofPositioningWaterLeakDetector,
    devicesRealtimeData,
    devicesAlarmsData,
    checkAlerting,
  ]);

  let preview = null;
  if (store) {
    preview = <LiquidCoolingSystemTopologyPreview store={store} />;
  }

  return (
    <PresetCard style={{ height: 630 }} bodyStyle={{ height: '100%' }}>
      <ThemeCompositions theme={document.body.dataset.theme === 'dark' ? 'dark' : 'light'}>
        {preview}
      </ThemeCompositions>
    </PresetCard>
  );
}

type LiquidCoolingSystemTopologyPreviewProps = {
  store: StoreInstance;
};

function LiquidCoolingSystemTopologyPreview({ store }: LiquidCoolingSystemTopologyPreviewProps) {
  useMonitoringDataReactions({ store });
  const config = useSelector(selectCurrentConfig);
  const configUtil = React.useMemo(() => new ConfigUtil(config), [config]);
  const typeofBulletCamera = configUtil.typeofDeviceGen(
    ConfigUtil.constants.deviceTypes.BULLET_CCTV_CAMERA
  );
  const { idc } = useParams<LiquidCoolingRouteParams>();
  const [pointGuids, setPointGuids] = useState<PointGuid[]>([]);
  const [pointLineModalVisible, setPointLineModalVisible] = useState(false);

  return (
    <>
      <GraphPointLineModal
        visible={pointLineModalVisible}
        pointGuids={pointGuids}
        idc={idc}
        modalTitle={pointGuids[0]?.serieName}
        onVisibleChange={() => setPointLineModalVisible(!pointLineModalVisible)}
      />
      <Preview
        width="100%"
        height="100%"
        store={store}
        // @ts-expect-error: TS7031 because tooltipRender type any
        tooltipRender={({ currentTarget }, { Container }) => {
          const element = currentTarget.getAttr('element');
          if (!element) {
            return;
          }

          let deviceElement;
          // 设备和设备名称的编组
          const isDeviceGroup = element.custom?.type === 'device_group';
          const isDevice = element.custom?.type === 'device';
          if (isDeviceGroup) {
            deviceElement = element.children.find(
              (el: { custom: { type: string } }) => el.custom?.type === 'device'
            );
          } else if (isDevice) {
            deviceElement = element;
          }

          if (!deviceElement) {
            return;
          }

          if (typeofBulletCamera(deviceElement.custom.deviceType) || deviceElement.custom.isRack) {
            return null;
          }

          return (
            <Container innerStyle={{ padding: 0 }}>
              <CorePointsCard
                deviceGuid={deviceElement.id}
                deviceType={deviceElement.custom.deviceType}
                bordered={false}
              />
            </Container>
          );
        }}
        onElementClick={(element: AnyElementInstance) => {
          if (element.custom?.type === 'device') {
            const { deviceGuid, isRack } = element.custom;
            if (deviceGuid && !isRack) {
              window.open(
                generateDeviceRecordRoutePath({
                  guid: deviceGuid,
                })
              );
            }
          } else if (element.custom?.type === 'device_text') {
            const [hostId] = relatedElementUtil.split(element.id);
            if (hostId) {
              window.open(
                generateDeviceRecordRoutePath({
                  guid: hostId,
                })
              );
            }
          } else if (element.custom?.type === 'point-text') {
            const { point } = element.custom;
            const pointGuids = [point].map(() => ({
              ...point,
              deviceGuid: point.deviceGuid,
              unit: point.unit,
              pointCode: point.code,
              serieName: point.name,
            }));
            setPointLineModalVisible(true);
            setPointGuids(pointGuids);
          }
        }}
      />
    </>
  );
}
