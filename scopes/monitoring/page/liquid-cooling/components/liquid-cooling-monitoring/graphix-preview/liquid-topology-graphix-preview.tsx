import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useParams } from 'react-router-dom';

// @ts-ignore: fix legacy codes
import InfraRoomGraphPreview from '@manyun/dc-brain.legacy.pages/room-monitoring/components/infra-room-graph-preview';
import { getRoomInfoActionCreator } from '@manyun/dc-brain.legacy.redux/actions/roomMonitoringActions';

import { PresetCard } from '../../preset-card';

export function LiquidTopologyGraphixPreview() {
  const dispatch = useDispatch();
  const { idc, block, room } = useParams<{ idc: string; block: string; room: string }>();
  const topologyType = 'LIQUID_TOPOLOGY';
  useEffect(() => {
    // @ts-ignore: fix legacy codes
    dispatch(getRoomInfoActionCreator({ idc, block, room, topologyType }));
  }, [block, dispatch, idc, room]);
  const spaceGuids = React.useMemo(() => [idc, block, room], [idc, block, room]);

  return (
    <PresetCard
      bodyStyle={{
        height: 'calc(var(--content-height) - 65px)',
        overflowY: 'auto',
      }}
    >
      {/* @ts-ignore: fix legacy codes */}
      <InfraRoomGraphPreview spaceGuids={spaceGuids} />
    </PresetCard>
  );
}
