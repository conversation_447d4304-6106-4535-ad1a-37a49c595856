import React from 'react';

import get from 'lodash.get';
import template from 'lodash.template';

import { ThemeColors, traverse } from '@manyun/dc-brain.aura-graphix';
import type {
  AnyElementInstance,
  GroupElementInstance,
  ImageElementInstance,
  TextElementInstance,
  TextElementSnapshotIn,
} from '@manyun/dc-brain.aura-graphix';
import type { TopologyElementConfig } from '@manyun/dc-brain.config.base';
import { ConfigUtil } from '@manyun/dc-brain.util.config';

// TODO: @Jerry refactor it into `@manyun/resource-hub.ui.topology-graphix`
// @ts-ignore: TS6307
import { tryEvaluate } from '@manyun/dc-brain.legacy.pages/topology-graphix/utils/expr-eval';

/**
 * 将表达式中的测点转为测点值
 *
 * @param expression `expr-eval` 可解析的表达式
 * @param getValueByPointCode 获取测点实时数据的方法
 * @returns
 */
export function replacePointValue(
  expression: string,
  getValueByPointCode: (pointCode: string) => { value: number }
): string {
  const regex = /\$\{[A-Z]*[0-9]+\}/;
  const m = regex.exec(expression);
  if (m === null) {
    return expression;
  }
  const firstMatch = m[0];
  const [prefix, postfix] = expression.split(firstMatch);
  const pointCode = firstMatch.replace('${', '').replace('}', '');

  // 保证测点值被转换成 `string` 进行字符串拼接
  // [prefix, null, postfix].join() 的方式将会把 `undefined, null, []` 转换成 `""`（空字符串）
  // See [Array#join#description](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/join#description)
  const pointValueString = String(getValueByPointCode(pointCode).value);

  const newExpression = [prefix, pointValueString, postfix].join('');

  return replacePointValue(newExpression, getValueByPointCode);
}

// NOTE: can not use CSS Variable here
const ALERTING_COLOR = '#ff4d4f';

export function updateElementAlertingAnimation(
  element: ImageElementInstance,
  { isHighlighting, isAlerting }: { isHighlighting: boolean; isAlerting: boolean }
) {
  const group = element.group as GroupElementInstance;
  if (!group) {
    return;
  }
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const highlight = group.findOne(child => child.type === 'highlight') as any;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const alertingMarker = group.findOne(child => child.type === 'alerting-marker') as any;
  const text = group.findOne(child => child.type === 'text') as TextElementInstance;
  if (isAlerting) {
    if (element.type === 'image' && element.animation.blink === false) {
      element.set({
        animation: {
          blink: true,
        },
      });
    }
    if (highlight && highlight.animating === false) {
      highlight.set({
        animating: true,
        fill: ALERTING_COLOR,
      });
    }
    if (alertingMarker && alertingMarker.animating === false) {
      alertingMarker.set({
        animating: true,
      });
    }
    if (text) {
      if (text.custom === undefined) {
        text.custom = {};
        text.set({ custom: {} });
      }
      const __DEFAULT_FILL = text.stroke;
      text.set({
        custom: {
          ...text.custom,
          __DEFAULT_FILL,
        },
        fill: ALERTING_COLOR,
      });
    }
  } else {
    if (element.type === 'image' && element.animation.blink) {
      element.set({
        animation: {
          blink: false,
        },
      });
    }
    if (highlight) {
      highlight.set({
        animating: isHighlighting,
        fill: highlight.__DEFAULT_COLOR,
      });
    }
    if (alertingMarker && alertingMarker.animating) {
      alertingMarker.set({
        animating: false,
      });
    }
    if (text) {
      if (text.custom.__DEFAULT_FILL !== text.fill) {
        text.set({
          fill: text.custom.__DEFAULT_FILL,
        });
      }
    }
  }
}

type GetValueFn = (pointCode: string) => {
  value: number;
  isAlerting: boolean;
};
export const getElementStates = (
  statePointsExpressions: NonNullable<TopologyElementConfig['statePointsExpressions']>,
  getValue: GetValueFn
) => {
  const results = Object.keys(statePointsExpressions).reduce<
    Record<string, Record<string, boolean>>
  >((_results, stateKey) => {
    const expressions = statePointsExpressions[stateKey];
    _results[stateKey] = Object.keys(expressions).reduce<Record<string, boolean>>(
      (_expressionResults, expressionKey) => {
        const expression = expressions[expressionKey];
        const actualExpression = replacePointValue(expression, getValue);
        _expressionResults[expressionKey] = tryEvaluate(actualExpression);

        return _expressionResults;
      },
      {}
    );

    return _results;
  }, {});

  return results;
};

type GetPointValueFn = (
  deviceType: string
) => (pointCode: string) => { isAlerting: boolean; value: number };
// TODO: @Jerry move it into `resource-hub/ui/topology-graphix`
export const updatePointTextAttrs = (
  pointText: TextElementInstance,
  {
    getPrevValue,
    getValue,
  }: {
    getPrevValue: GetPointValueFn;
    getValue: GetPointValueFn;
  }
) => {
  const { point, textTemplate: tpl } = pointText.custom;
  const { isAlerting: prevIsAlerting } = getPrevValue(point.deviceGuid)(point.code);
  const { value, isAlerting } = getValue(point.deviceGuid)(point.code);
  const attrs: Partial<TextElementSnapshotIn> = { text: '--' };
  if (Number.isNaN(value)) {
    attrs.text = template(tpl)({ value: '--' });
  } else if (point.dataType === 'DI' || point.dataType === 'DO') {
    const mappings = (point.validLimits as string[]).map(validLimit => validLimit.split('='));
    const t = mappings.find(([val]) => Number(val) === value);
    if (t) {
      attrs.text = template(tpl)({ value: t[1] });
    }
  } else {
    attrs.text = template(tpl)({ value });
  }
  if (prevIsAlerting !== isAlerting) {
    if (isAlerting) {
      if (pointText.fill !== ALERTING_COLOR) {
        attrs.fill = ALERTING_COLOR;
      }
    } else {
      if (pointText.fill !== pointText.custom.__DEFAULT_FILL) {
        attrs.fill = pointText.custom.__DEFAULT_FILL;
      }
    }
  }
  pointText.set(attrs);
};

export function fixElementAttrs(elements: AnyElementInstance[]) {
  traverse(elements, element => {
    const customType = element.custom?.type;
    if (customType === 'point-text') {
      (element as TextElementInstance).fill = ThemeColors.AnchorTextColor;
      (element as TextElementInstance).stroke = ThemeColors.AnchorTextColor;
      (element as TextElementInstance).text = (element as TextElementInstance).text.replace(
        // eslint-disable-next-line no-template-curly-in-string
        '${value}',
        '--'
      );
      (element as TextElementInstance).custom = {
        ...element.custom,
        __DEFAULT_FILL: (element as TextElementInstance).fill,
        __DEFAULT_STROKE: (element as TextElementInstance).stroke,
      };
    }
    element.listening = true;
    element.locked = true;
  });
}

export function useTypeofFuncs(configUtil: ConfigUtil) {
  return React.useMemo(() => {
    const typeofWaterLeakDetector = configUtil.typeofDeviceGen(
      ConfigUtil.constants.deviceTypes.WATER_LEAK_DETECTOR
    );
    const typeofPositioningWaterLeakDetector = configUtil.typeofDeviceGen(
      ConfigUtil.constants.deviceTypes.POSITIONING_WATER_LEAK_DETECTOR
    );

    return {
      typeofWaterLeakDetector,
      typeofPositioningWaterLeakDetector,
    };
  }, [configUtil]);
}

const NO_DATA_POINT_VALUE = Number.NaN;

/**
 * 从实时数据中获取某个设备下某个点位的测点值的方法生成器
 *
 * @param {Record<string, object>} realtimeData
 * @param {Record<string, object>} alarmsData
 * @returns
 */
export function generateGetPointValue(realtimeData: unknown, alarmsData: unknown) {
  /**
   * 获取某个设备下某个点位的测点值
   *
   * > `undefined, null` 会被处理成 `Number.NaN`
   *
   * @param {string} deviceGuid
   * @returns {(pointCode: string) => number}
   */
  const getPointValue = (deviceGuid: string) => {
    const deviceRealtimeData = get(realtimeData, [deviceGuid, 'pointValueMap']);
    const devicePointsAlarmsCount = get(alarmsData, [deviceGuid, 'pointsCount']);

    return (pointCode: string) => {
      let value = get(deviceRealtimeData, [pointCode, 'value'], NO_DATA_POINT_VALUE);
      if (value === undefined || value === null) {
        value = NO_DATA_POINT_VALUE;
      }

      const alarmsCount = get(devicePointsAlarmsCount, pointCode);
      const isAlerting = alarmsCount && (alarmsCount.ERROR > 0 || alarmsCount.WARN > 0);

      return { value, isAlerting };
    };
  };

  return getPointValue;
}
