@import '~@manyun/base-ui.theme.theme/dist/theme.variable.less';
.weather {
  padding: 24px;
  position: relative;
  display: flex;
  justify-content: space-between;
  background-image: url('./assets/block.png');
  width: 100%;
  height: 160px;
  background-repeat: no-repeat;
  background-size: 100% 160px;
  :global {
    .position {
      color: @text-color-secondary;
      font-size: 14px;
      margin-bottom: 16px;
    }
    .weatherCondition {
      display: flex;
      margin-top: 16px;
      .introduce {
        width: 35%;
      }
      .tempture {
        display: flex;
        justify-content: right;
        width: auto;
        padding-left: 12px;
      }
    }
  }
}
