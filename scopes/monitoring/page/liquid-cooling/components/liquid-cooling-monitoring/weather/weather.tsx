import EnvironmentOutlined from '@ant-design/icons/es/icons/EnvironmentOutlined';
import React, { useContext } from 'react';

import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useRealTimeWeather } from '@manyun/monitoring.hook.use-real-time-weather';
import { PointChartText } from '@manyun/monitoring.ui.point-data-renderer';
import { getWeatherIcon } from '@manyun/monitoring.util.get-weather-icon';

import { LiquidCoolingContext } from '../../../liquid-cooling-context';
import { PresetCard } from '../../preset-card';
import styles from './weather.module.less';

const address = '数据中心气象';

export const Weather = () => {
  const [{ liquidSpace }] = useContext(LiquidCoolingContext);
  const blockGuid = `${liquidSpace?.idc}.${liquidSpace?.block}`;

  const {
    weather,
    dryBulbPointData,
    wetBulbPointData,
    outdoorHumidityPointData,
    dewBulbPointData,
  } = useRealTimeWeather({ blockGuid });

  const bulbs = [
    {
      label: '干球温度',
      pointData: dryBulbPointData,
    },
    {
      label: '室外湿度',
      pointData: outdoorHumidityPointData,
    },
    {
      label: '湿球温度',
      pointData: wetBulbPointData,
    },
    {
      label: '露点温度',
      pointData: dewBulbPointData,
    },
  ];

  return (
    <PresetCard
      style={{
        height: 150,
        padding: '12px',
      }}
      className={styles.weather}
      bodyStyle={{
        width: '100%',
      }}
    >
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
        }}
      >
        <Typography.Text className="position">
          <EnvironmentOutlined />
          {` ${address}`}
        </Typography.Text>
        <Typography.Link target="_blank">更多</Typography.Link>
      </div>

      {weather && (
        <div className="weatherCondition">
          <div className="introduce">
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <Typography.Text style={{ fontSize: 24 }}>{weather.condition}</Typography.Text>
              {getWeatherIcon(Number(weather.icon), { fontSize: 30 })}
            </div>
            <Typography.Text>{`${weather.windDir} ${weather.windLevel}级`}</Typography.Text>
          </div>
          <Row className="tempture" gutter={[8, 8]}>
            {bulbs.map(bulb => {
              return (
                <Col key={bulb.label} span={12}>
                  <Typography.Text style={{ fontSize: 12 }}>
                    {bulb.label}
                    {` `}
                    <PointChartText
                      pointData={bulb.pointData!}
                      dataType="AI"
                      spaceGuid={blockGuid}
                    />
                  </Typography.Text>
                </Col>
              );
            })}
          </Row>
        </div>
      )}
    </PresetCard>
  );
};
