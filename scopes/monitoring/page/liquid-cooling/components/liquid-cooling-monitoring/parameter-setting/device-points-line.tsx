import React, { useContext, useMemo } from 'react';

// import { useSelector } from 'react-redux';
import dayjs from 'dayjs';
import get from 'lodash.get';
import type { Moment } from 'moment';

import type { LineProps } from '@manyun/base-ui.chart.line';
import { ThemeContext } from '@manyun/base-ui.chart.theme';

// import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
// import { ConfigUtil } from '@manyun/dc-brain.util.config';
import type { IntervalKeyMap } from '@manyun/monitoring.chart.duration-select';
import type { SeriesOption } from '@manyun/monitoring.chart.points-line';
import { PointsLine } from '@manyun/monitoring.chart.points-line';

import { LiquidCoolingContext } from '../../../liquid-cooling-context';

type LineChartOption = LineProps['option'];
type BasicOption = Omit<LineChartOption, 'series'>;

export type DevicePointsLineProps = {
  timeRange: [Moment, Moment];
  interval: IntervalKeyMap;
  spaceGuid: string;
  seriesOption: SeriesOption;
  pointGuids: {
    deviceGuid: string;
    pointCode: string;
    deviceType: string;
  }[];
  echartStyle: React.CSSProperties;
  basicOption?: BasicOption;
};

export const DevicePointsLine = ({
  timeRange,
  seriesOption,
  pointGuids,
  echartStyle,
  basicOption,
  interval,
}: DevicePointsLineProps) => {
  const [{ liquidSpace }] = useContext(LiquidCoolingContext);

  // const config = useSelector(selectCurrentConfig);
  // const configUtil = useMemo(() => {
  //   return new ConfigUtil(config);
  // }, [config]);
  const points = useMemo(() => {
    return pointGuids.map(({ deviceGuid, pointCode, deviceType }) => ({
      deviceGuid,
      pointCode: pointCode,
      // TODO: 如果用预定义测点，则用getPointCode
      // configUtil.getPointCode(deviceType!, pointCode),
      deviceType,
    }));
  }, [pointGuids]);
  const { json } = useContext(ThemeContext);
  const colors = useMemo(() => {
    if (pointGuids.length > 2) {
      return [
        get(json, ['color', 0]),
        get(json, ['color', 1]),
        get(json, ['color', 3]),
        get(json, ['color', 6]),
      ];
    }
    return [get(json, ['color', 0]), get(json, ['color', 3])];
  }, [pointGuids, json]);

  return (
    <PointsLine
      variant="dashboard"
      idcTag={liquidSpace?.idc!}
      pointGuids={points}
      echartStyle={{ width: '100%', ...echartStyle }}
      seriesOption={
        Array.isArray(seriesOption)
          ? seriesOption.map((series, index) => ({ ...series, color: colors[index] }))
          : undefined
      }
      showToolbox={false}
      basicOption={{
        legend: {
          top: 'top',
          left: undefined,
          bottom: undefined,
          right: 'right',
          width: '50%',
          type: 'scroll',
        },
        grid: {
          top: 40,
          bottom: 20,
          right: 40,
        },
        xAxis: {
          type: 'time',
          axisLabel: {
            formatter: function (value: string) {
              return dayjs(value).format('HH:mm');
            },
          },
          minInterval: interval ?? 900 * 1000,
        },
        ...basicOption,
      }}
      durationSelectStyle={{ display: 'none' }}
      defaultTimeRange={timeRange}
      defaultIntervalEnabled
      allowInterval
    />
  );
};
