import React, { useContext } from 'react';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import type { ColumnType, TableProps } from '@manyun/base-ui.ui.table';
import { Table } from '@manyun/base-ui.ui.table';

import type { PointMapType } from '@manyun/monitoring.hook.use-points-data';
import { usePointsData } from '@manyun/monitoring.hook.use-points-data';
import type { DataType } from '@manyun/monitoring.model.point';

import { LiquidCoolingContext } from '../../../liquid-cooling-context';

export type DeviceType = {
  guid: string;
  name: string;
  deviceType: string;
  pointTarget: {
    pointCode: string;
    dataType: DataType;
  }[];
  deviceName?: string;
  pointDatas?: PointMapType;
};

export type DevicePointTableProps = {
  devices: DeviceType[];
  columns: Array<ColumnType<DeviceType>>;
  scroll: TableProps<DeviceType>['scroll'];
  deviceType: string;
  style?: React.CSSProperties;
};

export const DevicePointTable = ({
  devices,
  columns,
  style,
  scroll,
  deviceType,
}: DevicePointTableProps) => {
  const [{ liquidSpace }] = useContext(LiquidCoolingContext);
  const blockGuid = `${liquidSpace?.idc}.${liquidSpace?.block}`;

  const subscribeTargets = useDeepCompareMemo(
    () =>
      devices.map(device => ({
        guid: device.guid!,
        type: deviceType,
        hardCodedPoints: device.pointTarget.map(info => ({
          // TODO: 如果target取得预定义，则需要替换为predefinedPoints
          pointCode: info.pointCode,
          dataType: info.dataType,
        })),
      })),
    [devices]
  );

  const [pointsData] = usePointsData({
    blockGuid: blockGuid!,
    moduleId: 'LiquidCoolingDevices',
    targets: subscribeTargets,
  });

  const generateDataSource = useDeepCompareMemo(
    () =>
      devices.map((device: DeviceType) => {
        return {
          ...device,
          deviceName: device.name,
          pointDatas: pointsData[device.guid],
        };
      }),
    [devices, pointsData]
  );

  return (
    <Table
      rowKey="guid"
      scroll={{ x: 'max-content', ...scroll }}
      tableLayout="fixed"
      size="small"
      style={{
        height: 300,
        ...style,
      }}
      columns={columns}
      dataSource={generateDataSource}
      pagination={false}
    />
  );
};
