import React, {
  use<PERSON>allback,
  useContext,
  useEffect,
  useLayoutEffect,
  useMemo,
  useState,
} from 'react';
import { Link } from 'react-router-dom';

import debounce from 'lodash.debounce';
import get from 'lodash.get';

// import { useSelector } from 'react-redux';
// import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
// import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { ThemeContext } from '@manyun/base-ui.chart.theme';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { message } from '@manyun/base-ui.ui.message';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import { Typography } from '@manyun/base-ui.ui.typography';

import {
  getIntervalByTimeRange,
  getTimeRangeFromNow,
} from '@manyun/monitoring.chart.duration-select';
import type { DataType } from '@manyun/monitoring.model.point';
import { PointChartText } from '@manyun/monitoring.ui.point-data-renderer';
import { generateDeviceRecordRoutePath } from '@manyun/resource-hub.route.resource-routes';
import { fetchDevices } from '@manyun/resource-hub.service.fetch-devices';

import { LiquidCoolingContext } from '../../../liquid-cooling-context';
import { PresetCard } from '../../preset-card';
import type { DeviceType } from './device-point-table';
import { DevicePointTable } from './device-point-table';
import { DevicePointsLine } from './device-points-line';

enum ParamsSettingTabs {
  ParamsSetting = 'ParamsSetting',
  RunningData = 'RunningData',
}

type ParamsSettingTab = `${ParamsSettingTabs}`;

const typesText = {
  [ParamsSettingTabs.RunningData]: '运行趋势',
  [ParamsSettingTabs.ParamsSetting]: '参数设定',
};

const tabs = Object.keys(typesText).map(data => {
  return {
    label: typesText[data as ParamsSettingTab],
    value: data,
  };
});

export const ParameterSetting = () => {
  const [activeTabKey, setActiveTabKey] = useState<ParamsSettingTab>('RunningData');
  const [dryCoolerDevices, setDryCoolerDevices] = useState<DeviceType[]>([]);
  const [cduDevices, setCduDevices] = useState<DeviceType[]>([]);
  // const config = useSelector(selectCurrentConfig);
  // const configUtil = new ConfigUtil(config);
  /** 干冷器 */
  // TODO: 待本地配置
  const dryCoolerDeviceType = '20227';
  // configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.TRANSFORMER); 如果替换为预定义测点需要注意订阅方式
  /** CUD */
  // TODO: 待本地配置
  const cduDeviceType = '20226';
  // configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.TRANSFORMER); 如果替换为预定义测点需要注意订阅方式
  const [{ liquidSpace }] = useContext(LiquidCoolingContext);
  const { idc, block } = liquidSpace!;
  const blockGuid = `${liquidSpace?.idc}.${liquidSpace?.block}`;

  const runningStatusPointTargets = useMemo(
    // 状态
    () => ({
      pointCode: '2001000',
      // ConfigUtil.constants.pointCodes.CHILLER_WORKING_STATE, // TODO: 2001000
      dataType: 'DI' as DataType,
    }),
    []
  );
  const dryCoolerOutWaterSetPointTargets = useMemo(
    // 供液温度-设定
    () => ({
      pointCode: '3001000',
      // ConfigUtil.constants.pointCodes.CHILLER_DIFF_T_OF_CHILLED_SNR_WATER, // TODO: 3001000
      dataType: 'AI' as DataType,
    }),
    []
  );
  const dryCoolerOutWaterRealTimePointTargets = useMemo(
    // 供液温度-实际  TODO: 1004000
    () => ({
      pointCode: '1004000',
      // ConfigUtil.constants.pointCodes.CHILLER_T_OF_COOLED_WATER_SUPPLY,
      dataType: 'AI' as DataType,
    }),
    []
  );
  // TODO:
  const backupPointTargets = useMemo(
    // 备份
    () => ({
      pointCode: '2008000',
      // ConfigUtil.constants.pointCodes.CHILLER_WORKING_STATE, // TODO: 2008000
      dataType: 'DI' as DataType,
    }),
    []
  );
  const controlModeTargets = useMemo(
    // 控制模式
    () => ({
      pointCode: '2007000',
      // ConfigUtil.constants.pointCodes.CHILLER_WORKING_STATE, // TODO: 2007000
      dataType: 'DI' as DataType,
    }),
    []
  );

  const cduSupplySetPointTargets = useMemo(
    // 供液温度-设定
    () => ({
      pointCode: '3002000',
      // ConfigUtil.constants.pointCodes.ACTIVE_IT_POWER_SUM,
      dataType: 'AI' as DataType,
    }),
    []
  );
  const cduSupplyRealTimePointTargets = useMemo(
    // 供液温度-实际
    () => ({
      pointCode: '1010000',
      // ConfigUtil.constants.pointCodes.ACTIVE_IT_POWER_SUM,
      dataType: 'AI' as DataType,
    }),
    []
  );
  const cduPressureSetPointTargets = useMemo(
    // 二次侧压差-设定
    () => ({
      pointCode: '3001000',
      // ConfigUtil.constants.pointCodes.ACTIVE_IT_POWER_SUM,
      dataType: 'AI' as DataType,
    }),
    []
  );
  const cduPressureRealTimePointTargets = useMemo(
    //二次侧压差-实际
    () => ({
      pointCode: '1009000',
      // ConfigUtil.constants.pointCodes.ACTIVE_IT_POWER_SUM,
      dataType: 'AI' as DataType,
    }),
    []
  );
  const cduFlowSetPointTargets = useMemo(
    // 二次侧流量-设定
    () => ({
      pointCode: '3003000',
      // ConfigUtil.constants.pointCodes.ACTIVE_IT_POWER_SUM,
      dataType: 'AI' as DataType,
    }),
    []
  );
  const cduFlowRealTimePointTargets = useMemo(
    // 二次侧流量-实际
    () => ({
      pointCode: '1012000',
      // ConfigUtil.constants.pointCodes.ACTIVE_IT_POWER_SUM,
      dataType: 'AI' as DataType,
    }),
    []
  );

  const getDevices = useCallback(
    async deviceType => {
      if (idc && block && deviceType) {
        const { data, error } = await fetchDevices({
          deviceTypes: [deviceType],
          idc: idc,
          block: block,
          operationStatus: 'ON',
        });
        if (error) {
          message.error(error.message);
          return;
        }
        if (deviceType === dryCoolerDeviceType) {
          setDryCoolerDevices(
            data.data.map(device => ({
              pointTarget: [
                runningStatusPointTargets,
                dryCoolerOutWaterSetPointTargets,
                dryCoolerOutWaterRealTimePointTargets,
              ],
              guid: device.guid,
              name: device.name,
              deviceType: device.deviceCategory.level3,
            }))
          );
        }
        if (deviceType === cduDeviceType) {
          // TODO: 需要新增本地三级分类
          setCduDevices(
            data.data.map(device => ({
              pointTarget: [
                runningStatusPointTargets,
                backupPointTargets,
                controlModeTargets,
                cduSupplySetPointTargets,
                cduSupplyRealTimePointTargets,
                cduPressureSetPointTargets,
                cduPressureRealTimePointTargets,
                cduFlowSetPointTargets,
                cduFlowRealTimePointTargets,
              ],
              guid: device.guid,
              name: device.name,
              deviceType: device.deviceCategory.level3,
            }))
          );
        }
      }
    },
    [
      backupPointTargets,
      block,
      cduFlowRealTimePointTargets,
      cduFlowSetPointTargets,
      cduPressureRealTimePointTargets,
      cduPressureSetPointTargets,
      cduSupplyRealTimePointTargets,
      cduSupplySetPointTargets,
      controlModeTargets,
      dryCoolerOutWaterRealTimePointTargets,
      dryCoolerOutWaterSetPointTargets,
      idc,
      runningStatusPointTargets,
    ]
  );

  useEffect(() => {
    if (dryCoolerDeviceType) {
      getDevices(dryCoolerDeviceType);
    }
    if (cduDeviceType) {
      getDevices(cduDeviceType);
    }
  }, [cduDeviceType, dryCoolerDeviceType, getDevices]);

  const dryCoolerColumns = useMemo(
    () => [
      {
        title: '设备',
        dataIndex: 'deviceName',
        width: 90,
        render: (_: unknown, record: DeviceType) => {
          return (
            <Link to={generateDeviceRecordRoutePath({ guid: record.guid })} target="_blank">
              {record.deviceName}
            </Link>
          );
        },
      },
      {
        title: '状态',
        dataIndex: runningStatusPointTargets.pointCode,
        width: 70,
        render: (_: unknown, record: DeviceType) => {
          return record?.pointDatas ? (
            <PointChartText
              spaceGuid={blockGuid}
              pointData={record.pointDatas?.get(runningStatusPointTargets.pointCode)!}
              dataType="DI"
            />
          ) : (
            '--'
          );
        },
      },
      {
        title: (
          <Typography.Text>
            供液温度
            <Typography.Text style={{ color: 'var(--text-color-secondary)' }}>
              (设定｜实际)
            </Typography.Text>
          </Typography.Text>
        ),
        width: 170,
        dataIndex: dryCoolerOutWaterSetPointTargets.pointCode, // TODO: merge
        render: (_: unknown, record: DeviceType) => {
          return record?.pointDatas ? (
            <>
              <PointChartText
                spaceGuid={blockGuid}
                pointData={record.pointDatas.get(dryCoolerOutWaterSetPointTargets.pointCode)!}
                dataType="AI"
              />
              <Typography.Text
                style={{ color: 'var(--text-color-secondary)' }}
              >{` | `}</Typography.Text>
              <PointChartText
                spaceGuid={blockGuid}
                pointData={record.pointDatas.get(dryCoolerOutWaterRealTimePointTargets.pointCode)!}
                dataType="AI"
              />
            </>
          ) : (
            '--'
          );
        },
      },
      {
        title: (
          <Typography.Text>
            供液温度<Typography.Text>(实际)</Typography.Text>）
          </Typography.Text>
        ),
        dataIndex: dryCoolerOutWaterRealTimePointTargets.pointCode,
        show: false,
      },
    ],
    [
      blockGuid,
      dryCoolerOutWaterRealTimePointTargets.pointCode,
      dryCoolerOutWaterSetPointTargets.pointCode,
      runningStatusPointTargets.pointCode,
    ]
  );

  const cduDeviceColumns = [
    {
      title: '设备',
      dataIndex: 'deviceName',
      width: 100,
      render: (_: unknown, record: DeviceType) => {
        return (
          <Link to={generateDeviceRecordRoutePath({ guid: record.guid })} target="_blank">
            {record.deviceName}
          </Link>
        );
      },
    },
    {
      title: '状态',
      dataIndex: runningStatusPointTargets.pointCode,
      width: 80,
      render: (_: unknown, record: DeviceType) => {
        return record?.pointDatas ? (
          <PointChartText
            spaceGuid={blockGuid}
            pointData={record.pointDatas?.get(runningStatusPointTargets.pointCode)!}
            dataType="DI"
          />
        ) : (
          '--'
        );
      },
    },
    {
      title: '备份',
      dataIndex: backupPointTargets.pointCode,
      width: 80,
      render: (_: unknown, record: DeviceType) => {
        return record?.pointDatas ? (
          <PointChartText
            spaceGuid={blockGuid}
            pointData={record.pointDatas?.get(backupPointTargets.pointCode)!}
            dataType="DI"
          />
        ) : (
          '--'
        );
      },
    },
    {
      title: '控制模式',
      dataIndex: controlModeTargets.pointCode,
      width: 90,
      render: (_: unknown, record: DeviceType) => {
        return record?.pointDatas ? (
          <PointChartText
            spaceGuid={blockGuid}
            pointData={record.pointDatas?.get(controlModeTargets.pointCode)!}
            dataType="DI"
          />
        ) : (
          '--'
        );
      },
    },
    {
      title: (
        <Typography.Text>
          供液温度
          <Typography.Text style={{ color: 'var(--text-color-secondary)' }}>
            (设定｜实际)
          </Typography.Text>
        </Typography.Text>
      ),
      width: 170,
      dataIndex: cduSupplySetPointTargets.pointCode, // TODO: merge
      render: (_: unknown, record: DeviceType) => {
        return record?.pointDatas ? (
          <>
            <PointChartText
              spaceGuid={blockGuid}
              pointData={record.pointDatas.get(cduSupplySetPointTargets.pointCode)!}
              dataType="AI"
            />
            <Typography.Text
              style={{ color: 'var(--text-color-secondary)' }}
            >{` | `}</Typography.Text>
            <PointChartText
              spaceGuid={blockGuid}
              pointData={record.pointDatas.get(cduSupplyRealTimePointTargets.pointCode)!}
              dataType="AI"
            />
          </>
        ) : (
          '--'
        );
      },
    },
    {
      title: (
        <Typography.Text>
          供液温度<Typography.Text>(实际)</Typography.Text>）
        </Typography.Text>
      ),
      dataIndex: cduSupplyRealTimePointTargets.pointCode,
      show: false,
    },
    {
      title: (
        <Typography.Text>
          二次侧压差
          <Typography.Text style={{ color: 'var(--text-color-secondary)' }}>
            (设定｜实际)
          </Typography.Text>
        </Typography.Text>
      ),
      width: 190,
      dataIndex: cduPressureSetPointTargets.pointCode,
      render: (_: unknown, record: DeviceType) => {
        return record?.pointDatas ? (
          <>
            <PointChartText
              spaceGuid={blockGuid}
              pointData={record.pointDatas.get(cduPressureSetPointTargets.pointCode)!}
              dataType="AI"
            />
            <Typography.Text
              style={{ color: 'var(--text-color-secondary)' }}
            >{` | `}</Typography.Text>
            <PointChartText
              spaceGuid={blockGuid}
              pointData={record.pointDatas.get(cduPressureRealTimePointTargets.pointCode)!}
              dataType="AI"
            />
          </>
        ) : (
          '--'
        );
      },
    },
    {
      title: (
        <Typography.Text>
          二次侧压差<Typography.Text>(实际)</Typography.Text>）
        </Typography.Text>
      ),
      dataIndex: cduPressureRealTimePointTargets.pointCode,
      show: false,
    },
    {
      title: (
        <Typography.Text>
          二次侧流量
          <Typography.Text style={{ color: 'var(--text-color-secondary)' }}>
            (设定｜实际)
          </Typography.Text>
        </Typography.Text>
      ),
      width: 190,
      dataIndex: cduFlowSetPointTargets.pointCode,
      render: (_: unknown, record: DeviceType) => {
        return record?.pointDatas ? (
          <>
            <PointChartText
              spaceGuid={blockGuid}
              pointData={record.pointDatas.get(cduFlowSetPointTargets.pointCode)!}
              dataType="AI"
            />
            <Typography.Text
              style={{ color: 'var(--text-color-secondary)' }}
            >{` | `}</Typography.Text>
            <PointChartText
              spaceGuid={blockGuid}
              pointData={record.pointDatas.get(cduFlowRealTimePointTargets.pointCode)!}
              dataType="AI"
            />
          </>
        ) : (
          '--'
        );
      },
    },
    {
      title: (
        <Typography.Text>
          二次侧流量<Typography.Text>(实际)</Typography.Text>）
        </Typography.Text>
      ),
      dataIndex: cduFlowRealTimePointTargets.pointCode,
      show: false,
    },
  ];

  const [start, end] = getTimeRangeFromNow(-2, 'hour');
  const defaultInterval = getIntervalByTimeRange([start!, end!]);
  const { json } = React.useContext(ThemeContext);
  const textColor = get(json, ['legend', 'pageTextStyle', 'color']);
  const [colSpan, setColSpan] = useState({
    dryCooler: 7,
    cdu: 17,
  });
  const deviceDatas = [
    {
      type: 'dryCooler',
      columns: dryCoolerColumns,
      devices: dryCoolerDevices,
      colSpan: colSpan.dryCooler,
      deviceType: dryCoolerDeviceType,
    },
    {
      type: 'cdu',
      columns: cduDeviceColumns,
      devices: cduDevices,
      colSpan: colSpan.cdu,
      deviceType: cduDeviceType,
    },
  ];

  const runningDatasMapper = [
    {
      pointCode: '1010000',
      // ConfigUtil.constants.pointCodes.PLATE_HEAT_EXCHANGER_T_OF_CHILLED_WATER_SUPPLY, // 供液温度
      // TODO: 需要注意真实测点和预定义，转换
      unit: '℃',
      title: '二次侧供液温度',
    },
    {
      pointCode: '1009000', // 二次侧压差 1009000 TODO:
      unit: 'bar',
      title: '二次侧压差',
    },
    {
      pointCode: '1012000', // 二次侧流量 1012000 TODO:
      unit: 'L/min ',
      title: '二次侧流量',
    },
  ];

  const updateColumnColSpan = useCallback(() => {
    const width = document.documentElement.clientWidth;
    setColSpan(
      width > 1700
        ? {
            dryCooler: 7,
            cdu: 17,
          }
        : {
            dryCooler: 9,
            cdu: 15,
          }
    );
  }, []);

  useLayoutEffect(() => {
    window.addEventListener('resize', debounce(updateColumnColSpan, 200));
    return () => window.removeEventListener('resize', updateColumnColSpan);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <PresetCard style={{ height: 320, padding: '6px 24px 2px 24px' }}>
      <Tabs
        activeKey={activeTabKey}
        items={tabs.map(({ label, value }) => ({
          label,
          key: value,
        }))}
        onChange={key => setActiveTabKey(key as ParamsSettingTab)}
      />
      <Row gutter={[8, 8]}>
        {activeTabKey === 'ParamsSetting' &&
          deviceDatas.map(device => (
            <Col key={device.type} span={device.colSpan}>
              <DevicePointTable
                columns={device.columns}
                devices={device.devices}
                deviceType={device.deviceType!}
                scroll={{
                  y: 210,
                }}
              />
            </Col>
          ))}
      </Row>
      <Row
        gutter={[8, 8]}
        style={{
          marginBottom: 12,
        }}
      >
        {activeTabKey === 'RunningData' &&
          runningDatasMapper.map(data => {
            return (
              <Col key={data.pointCode} span={8}>
                {cduDevices.length ? (
                  <DevicePointsLine
                    echartStyle={{ width: '100%', height: 195 }}
                    timeRange={[start, end]}
                    interval={defaultInterval}
                    spaceGuid={blockGuid}
                    pointGuids={cduDevices.map(device => ({
                      pointCode: data.pointCode,
                      deviceGuid: device.guid,
                      deviceType: device.deviceType,
                    }))}
                    seriesOption={cduDevices.map(device => ({ name: device.name }))}
                    basicOption={{
                      yAxis: [
                        {
                          name: data.unit,
                        },
                      ],
                      title: {
                        text: data.title,
                        left: 30,
                        top: -3,
                        textStyle: {
                          fontSize: 12,
                          color: textColor,
                        },
                      },
                    }}
                  />
                ) : null}
              </Col>
            );
          })}
      </Row>
    </PresetCard>
  );
};
