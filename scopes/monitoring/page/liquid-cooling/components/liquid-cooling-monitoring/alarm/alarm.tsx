import React, { useContext, useState } from 'react';
import { useSelector } from 'react-redux';
import { useShallowCompareEffect } from 'react-use';

import { Divider } from '@manyun/base-ui.ui.divider';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Statistic } from '@manyun/base-ui.ui.statistic';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import { Typography } from '@manyun/base-ui.ui.typography';

import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { usePointsData } from '@manyun/monitoring.hook.use-points-data';
import { PointChartText } from '@manyun/monitoring.ui.point-data-renderer';
import type { PointData } from '@manyun/monitoring.util.get-monitoring-data';

import { LiquidCoolingContext } from '../../../liquid-cooling-context';
import { PresetCard } from '../../preset-card';
import { AlarmMonitoring } from './alarm-monitoring';

enum LiquidCoolMonitoringAlarm {
  RunningData = 'RunningData',
  AlarmMonitoring = 'AlarmMonitoring',
}

type CoolingType = `${LiquidCoolMonitoringAlarm}`;

const typesText = {
  [LiquidCoolMonitoringAlarm.RunningData]: '运行指标',
  [LiquidCoolMonitoringAlarm.AlarmMonitoring]: '告警监控',
};

const tabs = Object.keys(typesText).map(data => {
  return {
    label: typesText[data as CoolingType],
    value: data,
  };
});

type PointsItems = {
  pointData?: PointData | undefined;
  label?: string;
  pointCode?: string;
  hide?: boolean;
  hasTotal?: boolean;
};

export const Alarm = () => {
  const [{ liquidSpace }] = useContext(LiquidCoolingContext);
  const blockGuid = `${liquidSpace?.idc}.${liquidSpace?.block}`;
  const roomGuid = `${liquidSpace?.idc}.${liquidSpace?.block}.${liquidSpace?.room}`;
  const [activeTabKey, setActiveTabKey] = useState<CoolingType>('RunningData');

  const pPUEPointCode = '1021000';
  // ConfigUtil.constants.pointCodes.PUE;
  const lpePointCode = '1128000'; //TODO:
  const itPowerPointCode = '1012000';
  const liquidCoolingPowerPointCode = '1125000';
  const cracPowerPointCode = '1017000';
  const coolingsPointCode = '1127000';
  const liquidCoolingsPointCode = '1126000';
  const cracCoolingsPointCode = '1019000';
  const cracSupplyPointCode = '1022000';
  const liquidCoolingsEfficiencyPointCode = '1129000';
  const cracEfficiencyPointCode = '1020000';
  const dryColderRunningPointCode = '1118000';
  const dryColderRunningTotalPointCode = '1119000';
  const cduRunningPointCode = '1121000';
  const cduRunningTotalPointCode = '1122000';
  const cracRunningPointCode = '1114000';
  const cracRunningTotalPointCode = '1123000';
  const allPowerPointCode = '1124000';
  const highSupplyTempPointCode = '1130000';
  const lowSupplyPressurePointCode = '1131000';
  const lowTwicePressurePointCode = '1132000';
  const coldChanelHighTempPointCode = '1064000';
  const coldChanelHighWetPointCode = '1065000';
  const sendWindPointCode = '1133000';
  const allElectricPointCode = '1135000';
  const allWaterPointCode = '1136000';
  const allWuePointCode = '1137000';
  const itAllElectricPointCode = '1023000';
  const liquidCoolingAllElectricPointCode = '1134000';
  const CracAllElectricPointCode = '1018000';
  const commonPoints = [
    {
      label: '干冷器运行数',
      pointCode: dryColderRunningPointCode,
      hasTotal: true,
    },
    {
      label: '干冷器运行总数',
      pointCode: dryColderRunningTotalPointCode,
      hide: true,
    },
    {
      label: 'CDU运行数',
      pointCode: cduRunningPointCode,
      hasTotal: true,
    },
    {
      label: 'CDU运行总数',
      pointCode: cduRunningTotalPointCode,
      hide: true,
    },
    {
      label: '空调运行数',
      pointCode: cracRunningPointCode,
      hasTotal: true,
    },
    {
      label: '空调运行总数',
      pointCode: cracRunningTotalPointCode,
      hide: true,
    },
    {
      label: '总功率',
      pointCode: allPowerPointCode,
    },
    {
      label: 'pPUE',
      pointCode: pPUEPointCode,
    },
    {
      label: 'LPE液冷占比',
      pointCode: lpePointCode,
    },
    {
      label: 'IT总功率',
      pointCode: itPowerPointCode,
    },
    {
      label: '液冷总功率',
      pointCode: liquidCoolingPowerPointCode,
    },
    {
      label: '空调总功率',
      pointCode: cracPowerPointCode,
    },
    {
      label: '总制冷量',
      pointCode: coolingsPointCode,
    },
    {
      label: '液冷总冷量',
      pointCode: liquidCoolingsPointCode,
    },
    {
      label: '空调总冷量',
      pointCode: cracCoolingsPointCode,
    },
    {
      label: '冷量供需比',
      pointCode: cracSupplyPointCode,
    },
    {
      label: '液冷能效比',
      pointCode: liquidCoolingsEfficiencyPointCode,
    },
    {
      label: '空调能效比',
      pointCode: cracEfficiencyPointCode,
    },
  ];
  const itSafePoints = [
    {
      label: '最高供液温度',
      pointCode: highSupplyTempPointCode,
    },
    {
      label: '最低供液压力',
      pointCode: lowSupplyPressurePointCode,
    },
    {
      label: '最低二次侧压差',
      pointCode: lowTwicePressurePointCode,
    },
    {
      label: '冷通道最高温度',
      pointCode: coldChanelHighTempPointCode,
    },
    {
      label: '冷通道最高湿度',
      pointCode: coldChanelHighWetPointCode,
    },
    {
      label: '最高空调送风',
      pointCode: sendWindPointCode,
    },
  ];
  const energyConsumptionPoints = [
    {
      label: '总用电量',
      pointCode: allElectricPointCode,
    },
    {
      label: '总用水量',
      pointCode: allWaterPointCode,
    },
    {
      label: '累计WUE',
      pointCode: allWuePointCode,
    },
    {
      label: 'IT总用电量',
      pointCode: itAllElectricPointCode,
    },
    {
      label: '液冷总用电量',
      pointCode: liquidCoolingAllElectricPointCode,
    },
    {
      label: '空调总用电量',
      pointCode: CracAllElectricPointCode,
    },
  ];

  const [commonPointsItems, setCommonPointsItems] = useState<{
    common: PointsItems[];
    itSafe: PointsItems[];
    energyConsumption: PointsItems[];
  }>({
    common: commonPoints,
    itSafe: itSafePoints,
    energyConsumption: energyConsumptionPoints,
  });
  const config = useSelector(selectCurrentConfig);
  const configUtil = new ConfigUtil(config);
  // const blockDeviceType = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_BLOCK);
  const [pointsData] = usePointsData({
    blockGuid: blockGuid,
    moduleId: 'LiquidAlarmRunningData',
    targets: [
      {
        guid: roomGuid,
        // predefinedType: ConfigUtil.constants.deviceTypes.SPACE_BLOCK, TODO: 如果走预定义需要处理
        type: configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_ROOM),
        // predefinedPoints: [...itSafePoints, ...itSafePoints].map(data => ({
        hardCodedPoints: [...commonPoints, ...itSafePoints, ...energyConsumptionPoints].map(
          data => ({
            pointCode: data.pointCode,
            dataType: 'AI',
          })
        ),
      },
    ],
  });

  useShallowCompareEffect(() => {
    if (pointsData) {
      setCommonPointsItems({
        common: commonPoints.map(point => ({
          ...point,
          pointData: pointsData[roomGuid]?.get(point.pointCode),
        })),
        itSafe: itSafePoints.map(point => ({
          ...point,
          pointData: pointsData[roomGuid]?.get(point.pointCode),
        })),
        energyConsumption: energyConsumptionPoints.map(point => ({
          ...point,
          pointData: pointsData[roomGuid]?.get(point.pointCode),
        })),
      });
    }
  }, [pointsData, roomGuid]);

  return (
    <PresetCard style={{ height: 800, overflowY: 'auto', padding: '12px 24px 24px 24px' }}>
      <Tabs
        activeKey={activeTabKey}
        items={tabs.map(({ label, value }) => ({
          label,
          key: value,
        }))}
        onChange={key => setActiveTabKey(key as CoolingType)}
      />
      {activeTabKey === 'RunningData' && (
        <>
          <CoolingStatisticRow items={commonPointsItems.common} blockGuid={blockGuid} />
          <Divider
            style={{
              margin: '16px 0px',
            }}
            type="horizontal"
            dashed
          />
          <CoolingStatisticRow
            items={commonPointsItems.itSafe}
            blockGuid={blockGuid}
            style={{ marginTop: 8 }}
          />
          <Divider
            style={{
              margin: '16px 0px',
            }}
            type="horizontal"
            dashed
          />
          <CoolingStatisticRow
            items={commonPointsItems.energyConsumption}
            blockGuid={blockGuid}
            style={{ marginTop: 8 }}
          />
        </>
      )}
      {activeTabKey === 'AlarmMonitoring' && <AlarmMonitoring />}
    </PresetCard>
  );
};

function CoolingStatisticRow({
  items,
  blockGuid,
  style,
}: {
  blockGuid: string;
  items: PointsItems[];
  style?: React.CSSProperties;
}) {
  return (
    <Row gutter={[8, 8]} style={style}>
      {items.map((item, index) => {
        if (item.hide) {
          return null;
        }
        return (
          <Col key={item.pointCode} span={8} style={{ position: 'relative', paddingLeft: 10 }}>
            <Statistic
              valueStyle={{
                lineHeight: '22px',
              }}
              title={
                item?.pointData ? (
                  <div style={{ display: 'flex' }}>
                    <PointChartText
                      style={{
                        fontSize: 16,
                        fontWeight: 500,
                      }}
                      spaceGuid={blockGuid}
                      pointData={item.pointData}
                      dataType="AI"
                      showUnit
                    />
                    {item.hasTotal && (
                      <div>
                        <Typography.Text
                          style={{
                            fontSize: 14,
                          }}
                        >
                          /
                        </Typography.Text>
                        <PointChartText
                          style={{
                            fontSize: 14,
                            fontWeight: 500,
                          }}
                          spaceGuid={blockGuid}
                          pointData={items[index + 1].pointData!}
                          dataType="AI"
                          showUnit
                        />
                      </div>
                    )}
                  </div>
                ) : (
                  '--'
                )
              }
              value={item.label}
              formatter={value => (
                <Typography.Text
                  style={{
                    fontSize: 12,
                    fontWeight: 400,
                  }}
                >
                  {value}
                </Typography.Text>
              )}
            />
            {(index + 1) % 3 !== 0 && (
              <Divider
                type="vertical"
                style={{ height: 36, margin: 0, position: 'absolute', right: 0 }}
              />
            )}
          </Col>
        );
      })}
    </Row>
  );
}
