import React, { useCallback, useContext, useEffect, useRef, useState } from 'react';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { message } from '@manyun/base-ui.ui.message';
import { Typography } from '@manyun/base-ui.ui.typography';
import type {
  AlarmJSON,
  BackendAlarmLifecycleState,
  BackendAlarmState,
} from '@manyun/monitoring.model.alarm';
import type { SortField, SvcQuery } from '@manyun/monitoring.service.fetch-alarms';
import { fetchAlarms } from '@manyun/monitoring.service.fetch-alarms';
import { AlarmScreenCard } from '@manyun/monitoring.ui.alarm-screen-card';
import type { FilterFormParams } from '@manyun/monitoring.ui.alarms-filter-form';
import { AdvancedAlarmsFilterForm } from '@manyun/monitoring.ui.alarms-filter-form';

import { LiquidCoolingContext } from '../../../liquid-cooling-context';

type SortOrder = 'ASCEND' | 'DESCEND' | undefined;
type FormParams = Omit<SvcQuery, 'idcTag' | 'pageSize' | 'pageNum'>;

type AlarmMonitoringProps = {
  defaultValue?: {
    alarmLevel: string | undefined;
    roomGuids: string[] | undefined;
    deviceName: string | undefined;
    deviceTypeList: string[] | undefined;
    triggerStatus: BackendAlarmState[] | undefined;
    alarmStatus: BackendAlarmLifecycleState[] | undefined;
  };
};

const defaultPagination = {
  pageSize: 200,
  pageNum: 1,
};

export function AlarmMonitoring({ defaultValue }: AlarmMonitoringProps) {
  const [{ liquidSpace }] = useContext(LiquidCoolingContext);
  const roomGuid = `${liquidSpace?.idc}.${liquidSpace?.block}.${liquidSpace?.room}`;

  const [alarmList, setAlarmList] = useState<AlarmJSON[]>([]);

  const [alarmSortField, setAlarmSortField] = useState<SortField>('gmtCreate');
  const [alarmSortOrder, setAlarmSortOrder] = useState<SortOrder>('DESCEND');
  const [triggerNum, setTriggerNum] = useState(0);

  const filterParamsRefDefaultValue = useDeepCompareMemo(() => {
    return {
      alarmLevel: defaultValue?.alarmLevel ? [+defaultValue.alarmLevel] : undefined,
      deviceName: defaultValue?.deviceName,
      deviceTypeList: defaultValue?.deviceTypeList,
      triggerStatus: defaultValue?.triggerStatus,
      alarmStatus: defaultValue?.alarmStatus,
    };
  }, [
    defaultValue?.alarmStatus,
    defaultValue?.deviceName,
    defaultValue?.deviceTypeList,
    defaultValue?.roomGuids,
    defaultValue?.triggerStatus,
  ]);

  const filterParamsRef = useRef<FormParams | undefined>(filterParamsRefDefaultValue);

  const getAlarms = useCallback(async () => {
    const { data, error } = await fetchAlarms({
      isQueryData: true,
      idcTag: liquidSpace?.idc!,
      roomTags: [liquidSpace?.room!],
      blockTags: [liquidSpace?.block!],
      ...filterParamsRef.current,
      sortField: alarmSortField,
      sortOrder: alarmSortOrder,
      ...defaultPagination,
    });
    if (!error) {
      const list = data.data.map(item => item.toJSON());
      setAlarmList(list);
      setTriggerNum(list.filter(alarm => alarm.state.code !== 'RECOVER').length);
    } else {
      message.error(error.message);
    }
  }, [alarmSortField, alarmSortOrder, liquidSpace?.block, liquidSpace?.idc, liquidSpace?.room]);

  useEffect(() => {
    getAlarms();
  }, [getAlarms]);

  const onFilterForm = (params: FilterFormParams) => {
    const {
      notifyContent,
      alarmTypes,
      alarmLevels,
      alarmStates,
      alarmLifecycleStates,
      deviceName,
      deviceTypes,
      lastModifyUserName,
      alarmsTriggeredAtTimeRange,
      alarmsRecoveredAtTimeRange,
    } = params;

    const filterParams = {
      notifyContent,
      alarmType: alarmTypes,
      alarmLevel: alarmLevels ? [+alarmLevels] : undefined,
      triggerStatus: alarmStates,
      alarmStatus: alarmLifecycleStates,
      deviceName,
      deviceTypeList: deviceTypes,
      confirmByName: lastModifyUserName,
      alarmCreateTimeStart: alarmsTriggeredAtTimeRange ? alarmsTriggeredAtTimeRange[0] : undefined,
      alarmCreateTimeEnd: alarmsTriggeredAtTimeRange ? alarmsTriggeredAtTimeRange[1] : undefined,
      recoverTimeStart: alarmsRecoveredAtTimeRange ? alarmsRecoveredAtTimeRange[0] : undefined,
      recoverTimeEnd: alarmsRecoveredAtTimeRange ? alarmsRecoveredAtTimeRange[1] : undefined,
    };
    filterParamsRef.current = filterParams;

    getAlarms();
  };

  const onSortChange = ({ value, sortOrder }: { value: string; sortOrder: SortOrder }) => {
    setAlarmSortField(value as SortField);
    setAlarmSortOrder(sortOrder);
  };

  useEffect(() => {
    const currentInterval = window.setInterval(getAlarms, 3 * 1000);

    return () => {
      window.clearInterval(currentInterval);
    };
  }, [getAlarms]);

  return (
    <div>
      <AdvancedAlarmsFilterForm
        idc={liquidSpace?.idc!}
        showFormItems={[
          'alarm-types',
          'alarm-levels',
          'alarm-states',
          'alarm-lifecycle-states',
          'room-guids',
          'device-name',
          'device-types',
          'last-modify-userId',
          'alarms-triggered-at-time-range',
          'alarms-recovered-at-time-range',
        ]}
        disabledItems={['room-guids']}
        deviceTypeSelectable={['C2']}
        defaultValue={{
          roomGuids: [roomGuid],
        }}
        onChange={onFilterForm}
      />

      <AlarmScreenCard
        bodyStyle={{ height: 580, padding: 0 }}
        headStyle={{ paddingLeft: 0 }}
        title={
          <>
            {alarmList.length ? (
              <Typography.Text
                style={{
                  cursor: 'pointer',
                }}
              >
                共{alarmList.length}条告警，其中
                <Typography.Text type="danger">{triggerNum}</Typography.Text>条未恢复
              </Typography.Text>
            ) : (
              <Typography.Text>
                共
                <Typography.Link
                  style={{
                    cursor: 'pointer',
                  }}
                >
                  0
                </Typography.Link>
                条告警
              </Typography.Text>
            )}
          </>
        }
        alarmList={alarmList}
        extra={<Typography.Link target="_blank">更多</Typography.Link>}
        onRefresh={getAlarms}
        onSortChange={onSortChange}
      />
    </div>
  );
}
