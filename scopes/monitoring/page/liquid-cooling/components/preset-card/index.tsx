import React from 'react';

import { Container } from '@manyun/base-ui.ui.container';
import type { ContainerProps } from '@manyun/base-ui.ui.container';
import { Typography } from '@manyun/base-ui.ui.typography';

export const PresetCard = ({
  children,
  title,
  style,
  bodyStyle,
  ...rest
}: ContainerProps & { title?: React.ReactNode; bodyStyle?: React.CSSProperties }) => (
  <Container style={{ padding: 24, ...style }} {...rest}>
    {typeof title === 'string' ? (
      <Typography.Title showBadge level={5}>
        {title}
      </Typography.Title>
    ) : (
      title
    )}
    <div style={bodyStyle}>{children}</div>
  </Container>
);
