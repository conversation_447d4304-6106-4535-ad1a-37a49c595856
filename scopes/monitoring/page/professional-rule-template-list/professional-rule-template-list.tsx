import React, { useEffect, useState } from 'react';
import { useHistory } from 'react-router-dom';
import { useUpdateEffect } from 'react-use';

import { Button } from '@manyun/base-ui.ui.button';
import { Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import { ProfessionalRuleTemplateJSON } from '@manyun/monitoring.model.professional-rule-template';
import { NEW_PROFESSIONAL_RULE_TEMPLATE_ROUTE_PATH } from '@manyun/monitoring.route.monitoring-routes';
import { fetchProfessionalRuleTemplateList } from '@manyun/monitoring.service.fetch-professional-rule-template-list';

import FilterFormPopover, {
  defaultFilterFormValue,
  filterFormPopoverContext,
} from './components/filter-form-popover';
import type { FilterFormValue } from './components/filter-form-popover';
import Table from './components/table';
import type { TableProps } from './components/table';
import styles from './professional-rule-template-list.module.less';

export function ProfessionalRuleTemplateList() {
  // 表格分页相关
  const [pagination, setPagination] = useState({ pageNum: 1, pageSize: 10, total: 0 });
  const paginationConfig: TableProps['pagination'] = {
    total: pagination.total,
    current: pagination.pageNum,
    pageSize: pagination.pageSize,
    onChange: (pageNum, pageSize) => {
      setPagination({
        ...pagination,
        pageNum,
        pageSize,
      });
    },
  };
  // 查询条件相关
  const [filterFormValue, setFilterFormValue] = useState<FilterFormValue>(defaultFilterFormValue);
  const [ruleName, setRuleName] = useState<string>();
  // 请求数据
  const [shouldSearch, setShouldSearch] = useState<boolean>(true);
  const [loading, setLoading] = useState(false);
  const [tableData, setTableData] = useState<ProfessionalRuleTemplateJSON[]>([]);
  useEffect(() => {
    (async () => {
      if (!shouldSearch) {
        return;
      }
      setShouldSearch(false);
      setLoading(true);
      const { error, data } = await fetchProfessionalRuleTemplateList({
        name: ruleName,
        bizCode: filterFormValue.ruleType?.[0],
        ruleCode: filterFormValue.ruleType?.[1],
        deviceType: filterFormValue.deviceType,
        pageNum: pagination.pageNum,
        pageSize: pagination.pageSize,
      });
      setLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      setTableData(data.data);
      setPagination({ ...pagination, total: data.total });
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [shouldSearch]);
  // 每次切换分页，则重新请求一次数据
  useUpdateEffect(() => {
    setShouldSearch(true);
  }, [pagination.pageNum, pagination.pageSize]);

  const history = useHistory();

  return (
    <Space direction="vertical" size="middle" className={styles.container}>
      <Row>
        <Typography.Text className={styles.title}>专家规则模版列表</Typography.Text>
      </Row>
      <Row align="middle">
        <Space size="middle">
          <Button
            type="primary"
            onClick={() => history.push(NEW_PROFESSIONAL_RULE_TEMPLATE_ROUTE_PATH)}
          >
            新建模版
          </Button>
          <Input.Search
            placeholder="请输入规则名称"
            value={ruleName}
            onChange={e => setRuleName(e.target.value)}
            onSearch={() => setShouldSearch(true)}
          />
          <filterFormPopoverContext.Provider value={{ filterFormValue, setFilterFormValue }}>
            <FilterFormPopover onSearch={() => setShouldSearch(true)} />
          </filterFormPopoverContext.Provider>
        </Space>
      </Row>
      <Row>
        <Table
          loading={loading}
          dataSource={tableData}
          pagination={paginationConfig}
          refreshTable={() => setShouldSearch(true)}
        />
      </Row>
    </Space>
  );
}
