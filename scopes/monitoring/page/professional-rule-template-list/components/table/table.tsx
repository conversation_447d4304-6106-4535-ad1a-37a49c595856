import React from 'react';
import { Link } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Table as BaseTable } from '@manyun/base-ui.ui.table';
import type { TableProps as BaseTableProps } from '@manyun/base-ui.ui.table';
import type { ColumnType } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';

import {
  ProfessionalRuleTemplateJSON,
  getProfessionalRuleTemplateLocales,
} from '@manyun/monitoring.model.professional-rule-template';
import {
  generateEditProfessionalRuleTemplateRoutePath,
  generateNewProfessionalRuleRoutePath,
  generateProfessionalRuleListRoutePath,
} from '@manyun/monitoring.route.monitoring-routes';
import { deleteProfessionalRuleTemplate } from '@manyun/monitoring.service.delete-professional-rule-template';
import { RuleTypeText } from '@manyun/monitoring.ui.rule-type-text';
import { DeviceTypeText } from '@manyun/resource-hub.ui.device-type-text';

const BLANK_PLACEHOLDER = '--';
const locales = getProfessionalRuleTemplateLocales();

export type TableProps = Omit<
  BaseTableProps<ProfessionalRuleTemplateJSON>,
  'columns' | 'rowKey'
> & {
  refreshTable: () => void;
};

export default function Table({ refreshTable, ...rest }: TableProps) {
  const handleDelete = (record: ProfessionalRuleTemplateJSON) => {
    Modal.confirm({
      title: '确定删除该条规则模版？',
      okText: '确定删除',
      cancelText: '取消',
      onOk: async () => {
        const { error } = await deleteProfessionalRuleTemplate({ id: record.id });
        if (error) {
          message.error(error.message);
          return;
        }
        message.success('删除成功！');
        refreshTable();
      },
    });
  };

  const columns: Array<ColumnType<ProfessionalRuleTemplateJSON>> = [
    {
      title: locales['id'],
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: locales['bizCode'],
      dataIndex: 'bizCode',
      key: 'bizCode',
      width: 120,
      render: (_, record) => <RuleTypeText bizCode={record.bizCode} />,
    },
    {
      title: locales['ruleCode'],
      dataIndex: 'ruleCode',
      key: 'ruleCode',
      width: 120,
      render: (_, record) => <RuleTypeText bizCode={record.bizCode} ruleCode={record.ruleCode} />,
    },
    {
      title: locales['deviceType'],
      dataIndex: 'deviceType',
      key: 'deviceType',
      width: 180,
      render: (_, record) => <DeviceTypeText code={record.deviceType} />,
    },
    {
      title: locales['name'],
      dataIndex: 'name',
      key: 'name',
      width: 240,
      render: value => (
        <Typography.Text ellipsis={{ tooltip: value }}>
          {value || BLANK_PLACEHOLDER}
        </Typography.Text>
      ),
    },
    {
      title: locales['scope'],
      dataIndex: 'scope',
      key: 'scope',
      width: 240,
      render: value => (
        <Typography.Text ellipsis={{ tooltip: value }}>
          {value || BLANK_PLACEHOLDER}
        </Typography.Text>
      ),
    },
    {
      title: locales['instanceNum'],
      dataIndex: 'instanceNum',
      key: 'instanceNum',
      width: 150,
      render: (_, record) =>
        record.instanceNum ? (
          <Link to={generateProfessionalRuleListRoutePath({ templateId: record.id })}>
            {record.instanceNum}
          </Link>
        ) : (
          BLANK_PLACEHOLDER
        ),
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 180,
      fixed: 'right',
      render: (_, record) => (
        <Space size="middle">
          <Link to={generateEditProfessionalRuleTemplateRoutePath({ id: record.id })}>编辑</Link>
          <Button type="link" onClick={() => handleDelete(record)} compact>
            删除
          </Button>
          <Link to={generateNewProfessionalRuleRoutePath({ templateId: record.id })}>新建规则</Link>
        </Space>
      ),
    },
  ];

  return (
    <BaseTable<ProfessionalRuleTemplateJSON>
      columns={columns}
      scroll={{ x: 'max-content' }}
      style={{ width: '100%' }}
      rowKey="id"
      {...rest}
    />
  );
}
