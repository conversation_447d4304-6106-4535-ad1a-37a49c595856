import React, { useContext, useState } from 'react';

import { FilterOutlined } from '@ant-design/icons';
import classNames from 'classnames';

import { Button } from '@manyun/base-ui.ui.button';
import { Dropdown } from '@manyun/base-ui.ui.dropdown';
import { Form } from '@manyun/base-ui.ui.form';
import type { FormProps } from '@manyun/base-ui.ui.form';
import { Row } from '@manyun/base-ui.ui.grid';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';

import { getProfessionalRuleTemplateLocales } from '@manyun/monitoring.model.professional-rule-template';
import { RuleTypeCascader } from '@manyun/monitoring.ui.rule-type-cascader';
import { DeviceTypeCascader } from '@manyun/resource-hub.ui.device-type-cascader';

import { defaultFilterFormValue, filterFormPopoverContext } from './filter-form-popover-context';
import styles from './filter-form-popover.module.less';

export type FilterFormPopoverProps = FilterFormProps;

export default function FilterFormPopover(props: FilterFormPopoverProps) {
  const [visible, setVisible] = useState(false);

  const contextValue = useContext(filterFormPopoverContext);
  const iconClassName = classNames(
    styles.icon,
    contextValue?.filterFormValue &&
      Object.values(contextValue.filterFormValue).some(value => value !== undefined) &&
      styles.primary
  );

  return (
    <Dropdown
      visible={visible}
      onVisibleChange={setVisible}
      overlay={<FilterForm {...props} />}
      overlayClassName={styles.overlay}
      placement="bottomRight"
      trigger={['click']}
      arrow={{ pointAtCenter: true }}
    >
      <Tooltip title="筛选">
        <FilterOutlined className={iconClassName} />
      </Tooltip>
    </Dropdown>
  );
}

type FilterFormProps = {
  onSearch: () => void;
};

const locals = getProfessionalRuleTemplateLocales();

function FilterForm({ onSearch }: FilterFormProps) {
  const [form] = Form.useForm();

  const contextValue = useContext(filterFormPopoverContext);
  const onValuesChange: FormProps['onValuesChange'] = (_, value) => {
    contextValue?.setFilterFormValue(value);
  };

  const handleReset = () => {
    form.resetFields();
    contextValue?.setFilterFormValue(defaultFilterFormValue);
    onSearch();
  };

  return (
    <Form form={form} layout="vertical" className={styles.form} onValuesChange={onValuesChange}>
      <Form.Item label={locals['ruleCode']} name="ruleType">
        <RuleTypeCascader allowClear />
      </Form.Item>
      <Form.Item label={locals['deviceType']} name="deviceType">
        <DeviceTypeCascader
          numbered
          dataType={['snDevice']}
          category="categorycode"
          disabledTypeList={['C0', 'C1']}
          placeholder="请选择设备类型"
          allowClear
        />
      </Form.Item>
      <Row justify="end">
        <Space>
          <Button onClick={() => handleReset()}>重置</Button>
          <Button type="primary" onClick={() => onSearch()}>
            搜索
          </Button>
        </Space>
      </Row>
    </Form>
  );
}
