import { createContext } from 'react';

export const defaultFilterFormValue = {
  ruleType: undefined,
  deviceType: undefined,
} as const;

export type FilterFormValue = {
  ruleType?: string[];
  deviceType?: string;
};

export type FilterFormPopoverContext = {
  filterFormValue: FilterFormValue;
  setFilterFormValue: (value: FilterFormValue) => void;
} | null;

export const filterFormPopoverContext = createContext<FilterFormPopoverContext>(null);
