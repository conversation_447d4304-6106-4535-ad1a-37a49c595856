import React from 'react';

import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import styles from './ai-ops-statistics.module.less';
import BaseLine from './assets/base-line.png';
import Battery from './assets/battery.png';
import Device from './assets/device.png';
import DisabledBaseLine from './assets/disabled-base-line.png';
import DisabledBattery from './assets/disabled-battery.png';
import DisabledDevice from './assets/disabled-device.png';
import DisabledIdcTemperature from './assets/disabled-idc-temperature.png';
import DisabledIt from './assets/disabled-it.png';
import DisabledWorker from './assets/disabled-worker.png';
import IdcTemperature from './assets/idc-temperature.png';
import It from './assets/it.png';
import MonitorHead from './assets/monitor-head.png';
import Worker from './assets/worker.png';
import { SenceCard } from './components/sence-card';

export type AiOpsStatisticsProps = {};

export type Sence = {
  key: string;
  title: string;
  subTitle: string;
  icon: string;
  disabledIcon: string;
  status: 'OFF' | 'ON';
};

const senceList: Sence[] = [
  {
    key: 'dynamicBaseline',
    title: '动态基线预警',
    subTitle:
      '根据设备测点数据自身的波动特性，使用机器学习的方法，预测其数据趋势，并实时判断设备测点的实际值与预测值之间的偏差，可以提前识别出设备测点数据的异常情况，做出预警而防微杜渐。',
    icon: BaseLine,
    disabledIcon: DisabledBaseLine,
    status: 'ON',
  },
  {
    key: 'machineRoomTemperature',
    title: '机房温湿度预警',
    subTitle:
      '使用动态基线监测机房内的温湿度变化，识别出单点温湿度的异常波动，以及模块级的温升变化，发出预警，可以帮助维护人员提前发现高温点等，以迅速采取措施，将温湿度告警消除于萌芽之中。',
    icon: IdcTemperature,
    disabledIcon: DisabledIdcTemperature,
    status: 'ON',
  },
  {
    key: 'itLoad',
    title: '设备数据质量检测',
    subTitle:
      '面向数据中心的海量设备数据，使用机器学习、数据挖掘的方法，对数据进行检查和评估，以确定数据是否可靠、准确和完整，并修复错误数据，以减少因数据错误导致的错误决策。',
    icon: It,
    disabledIcon: DisabledIt,
    status: 'OFF',
  },
  {
    key: 'batteryQuality',
    title: '电池健康度分析',
    subTitle:
      '在蓄电池的日常运行，以及充放电测试过程中，监测和记录电池的电压、内阻、温度等数据，使用算法评估电池的健康状况，在必要时更换电池。',
    icon: Battery,
    disabledIcon: DisabledBattery,
    status: 'OFF',
  },
  {
    key: 'equipmentDegradation',
    title: '设备劣化分析',
    subTitle:
      '使用机器学习、智能推荐等技术，对设备的劣化状况进行定期评估，采取必要的措施来维护，可以延长设备的寿命、提高设备的可靠性和性能，并减少因设备故障引起的安全风险和经济损失。',
    icon: Device,
    disabledIcon: DisabledDevice,
    status: 'OFF',
  },
  {
    key: 'peopleIntelligence',
    title: 'AI节能',
    subTitle:
      '面向暖通系统，使用算法生成冷站和末端空调的最优控制策略，并自动下发给设备执行，以平衡冷量供需，并确保冷却塔、冷机、冷泵、空调等设备均处于高性能工况，从而节约冷源能耗，降低PUE。',
    icon: Worker,
    disabledIcon: DisabledWorker,
    status: 'OFF',
  },
];

export function AiOpsStatistics() {
  return (
    <>
      <div className={styles.statisticsHead}>
        <img
          width="100%"
          style={{ width: '100%', minHeight: 150 }}
          src={MonitorHead}
          alt="暂无数据"
        />
        <Space
          style={{ position: 'absolute', left: 'calc(50% - 640px)', top: 'calc(50% - 45px)' }}
          direction="vertical"
        >
          <Typography.Title level={4}>AI-OPS</Typography.Title>
          <Typography.Text type="secondary" style={{ display: 'block', fontSize: 14, width: 836 }}>
            AI for data center
            operations，是使用机器学习、数据挖掘、智能推荐等技术，在数据中心的运维场景中，对监测数据进行分析和处理，
            以快速识别异常、自动诊断故障、定位潜在问题、提供处置建议，来提高数据中心运维的效率和可靠性。
          </Typography.Text>
        </Space>
      </div>
      <div className={styles.monitorCard}>
        {senceList.map(item => {
          return <SenceCard key={item.key} sence={item} />;
        })}
      </div>
    </>
  );
}
