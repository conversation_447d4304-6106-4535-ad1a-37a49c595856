import React from 'react';

import { Divider } from '@manyun/base-ui.ui.divider';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import {
  AI_OPS_ALARM_ROUTE_PATH,
  DYNAMIC_BASELINE_SETTING_ROUTE_PATH,
} from '@manyun/monitoring.route.monitoring-routes';

import type { Sence } from '../../ai-ops-statistics';
import styles from './sence-card.module.less';

export type SenceCardProps = {
  sence: Sence;
};

export function SenceCard({ sence }: SenceCardProps) {
  const { title, subTitle, icon, disabledIcon, status } = sence;

  return (
    <div style={{ display: 'flex', flexDirection: 'column' }}>
      <div className={styles.monitorHead}>
        <img
          style={{ width: 88, height: 88 }}
          src={status === 'ON' ? icon : disabledIcon}
          alt="暂无数据"
        />
        <Typography.Text disabled={status === 'OFF'} style={{ fontSize: 20, textAlign: 'center' }}>
          {title}
        </Typography.Text>
        <Typography.Text
          type="secondary"
          style={{ textAlignLast: 'center', fontSize: 12 }}
          disabled={status === 'OFF'}
        >
          {subTitle}
        </Typography.Text>
      </div>
      <div className={styles.monitorFoot}>
        {status === 'ON' ? (
          <Space split={<Divider type="vertical" />}>
            <Typography.Link href={DYNAMIC_BASELINE_SETTING_ROUTE_PATH} target="_blank">
              查看监测项
            </Typography.Link>
            <Typography.Link href={AI_OPS_ALARM_ROUTE_PATH} target="_blank">
              查看异常项
            </Typography.Link>
          </Space>
        ) : (
          <Typography.Text disabled>敬请期待</Typography.Text>
        )}
      </div>
    </div>
  );
}
