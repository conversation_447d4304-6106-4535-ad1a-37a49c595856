import { ExportOutlined } from '@ant-design/icons';
import saveAs from 'file-saver';
import React, { useState } from 'react';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import type { CheckboxChangeEvent } from '@manyun/base-ui.ui.checkbox';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import type { ColumnType } from '@manyun/base-ui.ui.table';
import type { DataNode } from '@manyun/base-ui.ui.tree';
import { EditColumns } from '@manyun/dc-brain.ui.edit-columns';
import { exportLocalAlarmRule } from '@manyun/monitoring.service.export-local-alarm-rule';
import { AlarmMonitoringItemMutator } from '@manyun/monitoring.ui.alarm-monitoring-item-mutator';

import { useLocalAlarmRuleList } from '../../local-alarm-rule-list-context';
import { defaultColumnsConfig } from '../local-alarm-rule-table';

export type LocalAlarmRuleQueryActionProps = {
  blockGuid: string;
  deviceTypeInfo: DataNode;
  onSetColumnsConfigClick: (value: ColumnType[]) => void;
  onSearch: (value: string) => void;
};

export function LocalAlarmRuleQueryAction({
  blockGuid,
  deviceTypeInfo,
  onSetColumnsConfigClick,
  onSearch,
}: LocalAlarmRuleQueryActionProps) {
  const [{ showAlarm }, { setShowAlarm, getRuleList, onAddAlarmRule }] = useLocalAlarmRuleList();

  const [exportLoading, setExportLoading] = useState(false);

  const onChange = (e: CheckboxChangeEvent) => {
    const showAlarm = e.target.checked;
    setShowAlarm(showAlarm);
    getRuleList({ showAlarm });
  };

  const onHandleExportAlarmRule = async () => {
    if (!blockGuid || !deviceTypeInfo?.key) {
      return;
    }
    setExportLoading(true);
    const { error, data } = await exportLocalAlarmRule({
      blockGuid,
      deviceType: deviceTypeInfo?.key as string,
    });

    setExportLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    saveAs(data, `${blockGuid}_告警规则.xlsx`);
  };

  return (
    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
      <Space size="middle">
        <AlarmMonitoringItemMutator
          pointParams={{ isQueryNon: true, blockGuid, isRemoveSub: false }}
          addRulebuttonProps={{ type: 'primary' }}
          onOk={onAddAlarmRule}
        />
        <Input.Search
          style={{ width: 272 }}
          placeholder="请输入测点名称/ID"
          onChange={e => onSearch(e.target.value)}
          onSearch={value => onSearch(value)}
        />
        <Checkbox checked={showAlarm} onChange={onChange}>
          仅显示告警规则
        </Checkbox>
      </Space>
      <Space>
        <Space style={{ cursor: 'pointer' }} onClick={onHandleExportAlarmRule}>
          <ExportOutlined style={{ color: `var(--${prefixCls}-primary-color)` }} />
          <Button loading={exportLoading} compact type="link">
            导出
          </Button>
        </Space>
        <EditColumns
          uniqKey="MONITORING_LOCAL_ALARMS_RULES_TABLE"
          defaultValue={defaultColumnsConfig}
          allowSetAsFixed={false}
          listsHeight={500}
          onChange={(value: ColumnType[]) => {
            onSetColumnsConfigClick(value);
          }}
        />
      </Space>
    </div>
  );
}
