import React from 'react';

import { Switch } from '@manyun/base-ui.ui.switch';

import type { LocalAlarmRuleJSON } from '@manyun/monitoring.service.fetch-local-alarm-rules';

import { useLocalAlarmRuleList } from '../../../../local-alarm-rule-list-context';

export type LocalAlarmRuleStatusSwitchProps = {
  localAlarmRule: LocalAlarmRuleJSON;
};

export function LocalAlarmRuleStatusSwitch({ localAlarmRule }: LocalAlarmRuleStatusSwitchProps) {
  const [, { onAddAlarmRule }] = useLocalAlarmRuleList();

  if (!localAlarmRule.id) {
    return <></>;
  }

  return (
    <Switch
      checked={localAlarmRule.enable}
      disabled={localAlarmRule.source === 'CENTER' && localAlarmRule.lock}
      onChange={checked => onAddAlarmRule({ ...localAlarmRule, enable: checked })}
    />
  );
}
