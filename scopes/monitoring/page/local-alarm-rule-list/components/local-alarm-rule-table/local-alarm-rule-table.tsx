import FileSyncOutlined from '@ant-design/icons/es/icons/FileSyncOutlined';
import LockOutlined from '@ant-design/icons/es/icons/LockOutlined';
import React from 'react';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType, TableProps } from '@manyun/base-ui.ui.table';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { BackendAlarmType } from '@manyun/monitoring.model.alarm';
import type { DataType } from '@manyun/monitoring.model.point';
import { TriggerRuleType } from '@manyun/monitoring.model.trigger-rule';
import type { LocalAlarmRuleJSON } from '@manyun/monitoring.service.fetch-local-alarm-rules';
import { AlarmLevelText } from '@manyun/monitoring.ui.alarm-level-text';
import { AlarmTypeText } from '@manyun/monitoring.ui.alarm-type-text';
import { TriggerRulesText } from '@manyun/monitoring.ui.trigger-rules-text';

import { LocalAlarmRuleStatusSwitch } from './components/local-alarm-rule-status-switch';

const BLANK = '--';

const dataTypeMap: Record<DataType, string> = {
  AI: '模拟量读点',
  ALARM: '告警点',
  AO: '模拟量写点',
  DI: '状态量读点',
  DO: '状态量写点',
};

export const localAlarmRuleColumns: ColumnType<LocalAlarmRuleJSON>[] = [
  {
    title: '测点分类',
    dataIndex: 'nonPoint',
    fixed: 'left',
    render: nonPoint => {
      return nonPoint ? '非标测点' : '标准测点';
    },
  },
  {
    title: '测点ID',
    dataIndex: 'pointCode',
    fixed: 'left',
    sorter: (a, b) => Number(a.pointCode) - Number(b.pointCode),
  },
  {
    title: '测点名称',
    dataIndex: 'pointName',
  },
  {
    title: '测点类型',
    dataIndex: 'dataType',
    render: (_, record) => {
      return dataTypeMap[record.dataType];
    },
  },
  {
    title: '告警类型',
    dataIndex: 'alarmType',
    render: (_, record) => {
      if (record.id === null) {
        return BLANK;
      }

      return <AlarmTypeText code={record.alarmType as BackendAlarmType} />;
    },
  },
  {
    title: '告警级别',
    dataIndex: 'alarmLevel',
    sorter: (a, b) => {
      if (a.id && b.id) {
        return Number(a.alarmLevel) - Number(b.alarmLevel);
      }
      return 0;
    },
    render: (_, record) => {
      if (record.id === null) {
        return BLANK;
      }

      return <AlarmLevelText code={record.alarmLevel} />;
    },
  },
  {
    title: '前置条件',
    dataIndex: 'preTriggerRules',
    render: (_, record) => {
      if (record.id === null) {
        return BLANK;
      }

      return (
        <TriggerRulesText
          type={TriggerRuleType.PreCondition}
          triggerRules={record?.preTriggerRules ?? []}
        />
      );
    },
  },
  {
    title: '告警条件',
    dataIndex: 'triggerRules',
    render: (_, record) => {
      if (record.id === null) {
        return BLANK;
      }
      return (
        <TriggerRulesText
          type={TriggerRuleType.AlarmCondition}
          triggerRules={record?.triggerRules ?? []}
        />
      );
    },
  },
  {
    title: '触发观察周期(秒)',
    dataIndex: 'triggerInterval',
    render: (_, record) => {
      if (record.id === null) {
        return BLANK;
      }
      return record.triggerInterval;
    },
  },
  {
    title: '恢复观察周期(秒)',
    dataIndex: 'recoverInterval',
    render: (_, record) => {
      if (record.id === null) {
        return BLANK;
      }
      return record.recoverInterval;
    },
  },
  {
    title: '是否创建事件',
    dataIndex: 'isCreateIncident',
    render: (_, record) => {
      if (record.id === null) {
        return BLANK;
      }
      return record.isCreateIncident ? '是' : '否';
    },
  },
  {
    title: '事件类型',
    key: 'incidentType',
    dataIndex: 'incidentType',
    render: (_, record) => {
      if (record.id === null) {
        return BLANK;
      }
      return (record.incidentTypeName ?? []).length
        ? (record.incidentTypeName ?? []).join('/')
        : '--';
    },
  },
  {
    title: '处置建议',
    dataIndex: 'description',
    render: (_, record) => {
      if (record.id === null || !record.description) {
        return BLANK;
      }
      return (
        <Typography.Text style={{ maxWidth: 220 }} ellipsis={{ tooltip: record.description }}>
          {record.description}
        </Typography.Text>
      );
    },
  },
  {
    title: '状态',
    dataIndex: 'enable',
    render: (_, record) => {
      if (record.id === null) {
        return BLANK;
      }
      return <LocalAlarmRuleStatusSwitch localAlarmRule={record} />;
    },
  },
  {
    title: '规则来源',
    dataIndex: 'source',
    render: (_, record) => {
      if (record.id === null) {
        return BLANK;
      }

      const text = record.source === 'CENTER' ? '集团' : '属地';

      if (record.source === 'CENTER') {
        if (record.lock) {
          return (
            <Space>
              {text}
              <Tooltip title="集团已锁定该规则内容，属地无法修改">
                <Typography>
                  <LockOutlined style={{ color: 'red' }} />
                </Typography>
              </Tooltip>
            </Space>
          );
        }
        if (record.parentItemId) {
          return (
            <Space>
              <span>{text}</span>
              <Tooltip title="属地已修改该规则内容">
                <FileSyncOutlined style={{ color: 'orange' }} />
              </Tooltip>
            </Space>
          );
        }
      }

      return text;
    },
  },
];

export const defaultColumnsConfig = localAlarmRuleColumns.map(item => {
  if (item.dataIndex === 'nonPoint' || item.dataIndex === 'pointCode') {
    return {
      title: item.title,
      dataIndex: item.dataIndex,
      show: true,
      fixed: 'left',
    };
  }
  if (item.dataIndex === 'pointName') {
    return {
      title: item.title,
      dataIndex: item.dataIndex,
      show: true,
      disable: true,
      fixed: 'left',
    };
  }
  if (
    item.dataIndex === 'triggerInterval' ||
    item.dataIndex === 'recoverInterval' ||
    item.dataIndex === 'isCreateIncident' ||
    item.dataIndex === 'incidentType'
  ) {
    return {
      title: item.title,
      dataIndex: item.dataIndex,
      show: false,
    };
  }
  return {
    title: item.title,
    dataIndex: item.dataIndex,
    show: true,
  };
}) as ColumnType[];

export type ColumnDataIndex =
  | 'nonPoint'
  | 'pointCode'
  | 'pointName'
  | 'dataType'
  | 'alarmType'
  | 'alarmLevel'
  | 'preTriggerRules'
  | 'triggerRules'
  | 'triggerInterval'
  | 'recoverInterval'
  | 'isCreateIncident'
  | 'incidentType'
  | 'desc'
  | 'enable'
  | 'enableNum'
  | 'disableNum'
  | 'source';

export type LocalAlarmRuleTableProps = {
  dataIndexs?: ColumnDataIndex[];
  operation?: ColumnType<LocalAlarmRuleJSON>;
} & Omit<TableProps<LocalAlarmRuleJSON>, 'columns'>;

export function LocalAlarmRuleTable({
  dataIndexs = [
    'nonPoint',
    'pointCode',
    'pointName',
    'dataType',
    'alarmType',
    'alarmLevel',
    'preTriggerRules',
    'triggerRules',
    'triggerInterval',
    'recoverInterval',
    'isCreateIncident',
    'incidentType',
    'desc',
    'enable',
    'enableNum',
    'disableNum',
    'source',
  ],
  operation,
  ...rest
}: LocalAlarmRuleTableProps) {
  const columns = useDeepCompareMemo(() => {
    const newColumns = dataIndexs
      .map(dataIndex => {
        return localAlarmRuleColumns.find(item => item.dataIndex === dataIndex);
      })
      .filter((item): item is ColumnType<LocalAlarmRuleJSON> => item !== undefined);

    if (operation) {
      return [...newColumns, operation];
    }
    return newColumns;
  }, [dataIndexs, operation]);

  return <Table columns={columns} scroll={{ x: 'max-content' }} rowKey="key" {...rest} />;
}
