import type { CSSProperties } from 'react';
import React from 'react';

import type { RadioProps } from '@manyun/base-ui.ui.radio';
import { Typography } from '@manyun/base-ui.ui.typography';

import { MyBlocks } from '@manyun/resource-hub.ui.my-blocks';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

import styles from './local-alarm-rule-head.module.less';

export type LocalAlarmRuleHeadProps = {
  style?: CSSProperties;
  blockGuid: string;
  onChange: (value: string) => void;
};

export function LocalAlarmRuleHead({ style, blockGuid, onChange }: LocalAlarmRuleHeadProps) {
  const onMyBlocksChange: RadioProps['onChange'] = e => {
    onChange(e.target.value);
  };

  return (
    <div style={style} className={styles.head}>
      <Typography.Title style={{ marginBottom: 0 }} level={5} showBadge>
        告警规则配置
      </Typography.Title>
      <MyBlocks
        idc={getSpaceGuidMap(blockGuid).idc!}
        value={blockGuid}
        onChange={onMyBlocksChange}
      />
    </div>
  );
}
