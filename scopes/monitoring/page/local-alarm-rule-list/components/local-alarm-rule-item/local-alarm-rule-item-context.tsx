import { createContext } from 'react';

import type { SvcQuery as LocalAlarmRuleDevicesParams } from '@manyun/monitoring.service.fetch-local-alarm-rule-devices';

export type LocalAlarmRuleItemContextProps = {
  localAlarmRuleId: string;
  getEquipmentList: (params?: Partial<LocalAlarmRuleDevicesParams>) => void;
};

const initialValue: LocalAlarmRuleItemContextProps = {
  localAlarmRuleId: '',
  getEquipmentList: () => {},
};

export const LocalAlarmRuleItemContext =
  createContext<LocalAlarmRuleItemContextProps>(initialValue);
