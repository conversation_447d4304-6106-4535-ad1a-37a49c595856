import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useDispatch } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { configSliceActions as configActions } from '@manyun/dc-brain.state.config';
import type {
  LocalAlarmRuleDevice,
  SvcQuery as LocalAlarmRuleDevicesParams,
} from '@manyun/monitoring.service.fetch-local-alarm-rule-devices';
import { fetchLocalAlarmRuleDevices } from '@manyun/monitoring.service.fetch-local-alarm-rule-devices';
import type { LocalAlarmRuleJSON } from '@manyun/monitoring.service.fetch-local-alarm-rules';
import { updateLocalAlarmRuleDevices } from '@manyun/monitoring.service.update-local-alarm-rule-devices';
import {
  addRealtimeNAlarmsDataSubscriptionActionCreator as subscribe,
  removeRealtimeNAlarmsDataSubscriptionActionCreator as unsubscribe,
} from '@manyun/monitoring.state.subscriptions';
import { LocalAlarmRuleStatusSelect } from '@manyun/monitoring.ui.local-alarm-rule-status-select';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

import { LocalAlarmRuleItemTable, mode, moduleId } from './components/local-alarm-rule-item-table';
import { LocalAlarmRuleItemContext } from './local-alarm-rule-item-context';

export type LocalAlarmRuleItemProps = {
  localAlarmRule: LocalAlarmRuleJSON;
  blockGuid: string;
  open: boolean;
  onClose: () => void;
};

export function LocalAlarmRuleItem({
  localAlarmRule,
  blockGuid,
  open,
  onClose,
}: LocalAlarmRuleItemProps) {
  const dispatch = useDispatch();

  const [loading, setLoading] = useState<boolean>(false);

  const [equipmentList, setEquipmentList] = useState<LocalAlarmRuleDevice[]>([]);

  const [selectedIds, setSelectedIds] = useState<string[]>([]);

  const [pagination, setPagination] = useState<{
    pageNum: number;
    pageSize: number;
    total?: number;
  }>({ pageNum: 1, pageSize: 10, total: 0 });

  const fetchEquipmentParamsRef = useRef<Partial<LocalAlarmRuleDevicesParams>>({});

  const getEquipmentList = useCallback(
    async (params?: Partial<LocalAlarmRuleDevicesParams>) => {
      setLoading(true);
      const { data, error } = await fetchLocalAlarmRuleDevices({
        pageNum: pagination.pageNum,
        pageSize: pagination.pageSize,
        itemId: localAlarmRule.id as string,
        blockGuid,
        ...fetchEquipmentParamsRef.current,
        ...params,
      });
      setLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      setEquipmentList(
        data.data.map(item => ({
          ...item,
          pointName: localAlarmRule.pointName,
          pointCode: localAlarmRule.pointCode,
        }))
      );
      setPagination({
        pageNum: params?.pageNum ?? pagination.pageNum,
        pageSize: params?.pageSize ?? pagination.pageSize,
        total: data.total,
      });
      onClearTableRows();

      const deviceGuids = data.data.map(item => item.deviceGuid);
      dispatch(
        subscribe({
          mode,
          blockGuid,
          moduleId,
          deviceGuids,
        })
      );
      if (localAlarmRule.id) {
        const pointsDefinitionMap = {
          [localAlarmRule.deviceType]: {
            [localAlarmRule.pointCode]: {
              parentCode: null,
              name: localAlarmRule.pointName!,
              dataType: localAlarmRule.dataType as 'AI' | 'AO' | 'DI' | 'DO',
              unit: localAlarmRule.unit,
              validLimits: localAlarmRule.validLimits,
            },
          },
        };
        dispatch(configActions.updatePointsDefinitionMap(pointsDefinitionMap));
      }
    },
    [blockGuid, dispatch, localAlarmRule, pagination.pageNum, pagination.pageSize]
  );

  useEffect(() => {
    if (!open) {
      return;
    }
    getEquipmentList();
    return () => {
      dispatch(unsubscribe({ blockGuid, mode, moduleId }));
    };
  }, [blockGuid, dispatch, getEquipmentList, open]);

  const onChangePage = (pageNum: number, pageSize: number) => {
    setPagination({ pageNum, pageSize });
    onClearTableRows();
  };

  const onEnableClick = async (enable: boolean) => {
    const { error } = await updateLocalAlarmRuleDevices({
      itemId: localAlarmRule.id as string,
      blockGuid,
      deviceGuidList: selectedIds,
      available: enable,
    });
    if (error) {
      message.error(error.message);
      return;
    }
    onClearTableRows();
    if (enable) {
      message.success('批量启用成功');
    } else {
      message.success('批量停用成功');
    }
    getEquipmentList();
  };

  const onClearTableRows = () => {
    setSelectedIds([]);
  };

  return (
    <Modal
      bodyStyle={{ maxHeight: 678, overflowY: 'auto' }}
      width={948}
      title="告警规则清单"
      destroyOnClose
      afterClose={() => {
        fetchEquipmentParamsRef.current = {};
        setPagination({ pageNum: 1, pageSize: 10 });
        onClearTableRows();
      }}
      open={open}
      footer={null}
      onCancel={onClose}
    >
      <Space style={{ width: '100%' }} direction="vertical">
        <Space>
          <Button type="primary" disabled={!selectedIds.length} onClick={() => onEnableClick(true)}>
            批量启用
          </Button>
          <Button disabled={!selectedIds.length} onClick={() => onEnableClick(false)}>
            批量停用
          </Button>
          <Input
            style={{ width: 216 }}
            placeholder="告警对象"
            allowClear
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
              fetchEquipmentParamsRef.current = {
                ...fetchEquipmentParamsRef.current,
                deviceName: e.target.value,
              };
              getEquipmentList({ pageNum: 1 });
            }}
          />
          <LocationTreeSelect
            style={{ width: 216 }}
            authorizedOnly
            placeholder="包间"
            idc={getSpaceGuidMap(blockGuid).idc!}
            block={getSpaceGuidMap(blockGuid).block!}
            nodeTypes={['ROOM']}
            allowClear
            showSearch
            onClear={() => {
              fetchEquipmentParamsRef.current = {
                ...fetchEquipmentParamsRef.current,
                roomTag: undefined,
              };
              getEquipmentList({ pageNum: 1 });
            }}
            onChange={(value: string) => {
              const roomTag = getSpaceGuidMap(value as string).room;
              if (roomTag) {
                fetchEquipmentParamsRef.current = { ...fetchEquipmentParamsRef.current, roomTag };
                getEquipmentList({ pageNum: 1 });
              }
            }}
          />
          <LocalAlarmRuleStatusSelect
            style={{ width: 216 }}
            placeholder="规则状态"
            allowClear
            onClear={() => {
              fetchEquipmentParamsRef.current = {
                ...fetchEquipmentParamsRef.current,
                enable: undefined,
              };
              getEquipmentList({ pageNum: 1 });
            }}
            onSelect={(value: string) => {
              fetchEquipmentParamsRef.current = {
                ...fetchEquipmentParamsRef.current,
                enable: value === 'ON' ? true : false,
              };
              getEquipmentList({ pageNum: 1 });
            }}
          />
        </Space>
        <LocalAlarmRuleItemContext.Provider
          value={{ localAlarmRuleId: localAlarmRule.id as string, getEquipmentList }}
        >
          <LocalAlarmRuleItemTable
            rowKey="deviceGuid"
            size="middle"
            blockGuid={blockGuid}
            localAlarmRule={localAlarmRule}
            loading={loading}
            dataSource={equipmentList}
            rowSelection={{
              selectedRowKeys: selectedIds,
              onChange: selectedRowKeys => {
                setSelectedIds(selectedRowKeys as string[]);
              },
            }}
            pagination={{
              total: pagination.total,
              current: pagination.pageNum,
              pageSize: pagination.pageSize,
              onChange: onChangePage,
            }}
          />
        </LocalAlarmRuleItemContext.Provider>
      </Space>
    </Modal>
  );
}
