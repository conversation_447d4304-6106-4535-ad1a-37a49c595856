import React, { useContext } from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { Switch } from '@manyun/base-ui.ui.switch';

import type { LocalAlarmRuleDevice } from '@manyun/monitoring.service.fetch-local-alarm-rule-devices';
import { updateLocalAlarmRuleDevices } from '@manyun/monitoring.service.update-local-alarm-rule-devices';
import { getSpaceGuid, getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

import { LocalAlarmRuleItemContext } from '../../local-alarm-rule-item-context';

export type LocalAlarmRuleItemStatusSwitchProps = {
  checked: boolean;
  localAlarmRuleItem: LocalAlarmRuleDevice;
};

export function LocalAlarmRuleItemStatusSwitch({
  checked = false,
  localAlarmRuleItem,
}: LocalAlarmRuleItemStatusSwitchProps) {
  const contextValue = useContext(LocalAlarmRuleItemContext);

  const onChange = async (checked: boolean) => {
    const { deviceGuid, spaceGuid } = localAlarmRuleItem;
    const { idc, block } = getSpaceGuidMap(spaceGuid);
    const blockGuid = getSpaceGuid(idc!, block);
    const { error } = await updateLocalAlarmRuleDevices({
      itemId: contextValue.localAlarmRuleId,
      blockGuid: blockGuid!,
      deviceGuidList: [deviceGuid],
      available: checked,
    });
    if (error) {
      message.error(error.message);
      return;
    }
    if (checked) {
      message.success('启用成功');
    } else {
      message.success('停用成功');
    }
    contextValue.getEquipmentList();
  };

  return <Switch checked={checked} onChange={onChange} />;
}
