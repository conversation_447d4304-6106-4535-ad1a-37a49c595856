import React from 'react';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType, TableProps } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import type { PointTarget, PointsDataType } from '@manyun/monitoring.hook.use-points-data';
import { usePointsData } from '@manyun/monitoring.hook.use-points-data';
import type { DataType } from '@manyun/monitoring.model.point';
import { generateSpaceOrDeviceRoutePath } from '@manyun/monitoring.route.monitoring-routes';
import type { LocalAlarmRuleDevice } from '@manyun/monitoring.service.fetch-local-alarm-rule-devices';
import type { LocalAlarmRuleJSON } from '@manyun/monitoring.service.fetch-local-alarm-rules';
import { SUBSCRIPTIONS_MODE } from '@manyun/monitoring.state.subscriptions';
import { PointChartText } from '@manyun/monitoring.ui.point-data-renderer';
import type { PointData } from '@manyun/monitoring.util.get-monitoring-data';

import { LocalAlarmRuleItemStatusSwitch } from '../local-alarm-rule-item-status-switch';

export const moduleId = 'local-alarm-rule-item-table';

export const mode = SUBSCRIPTIONS_MODE.REALTIME_N_ALARMS;

const BLANK = '--';

const getEquipmentRuleColumns = ({
  pointData,
  point,
  blockGuid,
  virtuallyDeviceMap,
}: {
  pointData: PointsDataType;
  point: { code: string; dataType: DataType };
  blockGuid: string;
  virtuallyDeviceMap: Record<'BLOCK' | 'ROOM' | 'IDC' | 'GRID' | 'COLUMN', string>;
}) => {
  const equipmentRuleColumns: ColumnType<LocalAlarmRuleDevice>[] = [
    {
      title: '告警对象',
      dataIndex: 'deviceGuid',
      fixed: 'left',
      render: (_, record) => {
        const type =
          record.deviceType === virtuallyDeviceMap.IDC
            ? 'IDC'
            : record.deviceType === virtuallyDeviceMap.BLOCK
              ? 'BLOCK'
              : record.deviceType === virtuallyDeviceMap.ROOM
                ? 'ROOM'
                : record.deviceType === virtuallyDeviceMap.GRID
                  ? 'GRID'
                  : record.deviceType === virtuallyDeviceMap.COLUMN
                    ? 'COLUMN'
                    : 'DEVICE';
        if (type === 'COLUMN') {
          return record.deviceTag ?? BLANK;
        }
        return (
          <Typography.Link
            onClick={() => {
              window.open(
                generateSpaceOrDeviceRoutePath({
                  guid: record.deviceGuid,
                  type,
                })
              );
            }}
          >
            {record.deviceTag ?? BLANK}
          </Typography.Link>
        );
      },
    },
    {
      title: '对象名称',
      dataIndex: 'deviceName',
      render: (_, record) => {
        if (
          record.deviceType === virtuallyDeviceMap.GRID ||
          record.deviceType === virtuallyDeviceMap.COLUMN
        ) {
          return BLANK;
        }
        return record.deviceName;
      },
    },
    {
      title: '厂商型号',
      dataIndex: 'vendor',
      render: (_, record) => {
        return record.vendor && record.productModel
          ? `${record.vendor}-${record.productModel}`
          : BLANK;
      },
    },
    {
      title: '所在包间',
      dataIndex: 'roomName',
      render: (_, record) => {
        if (!record.roomTag && !record.roomName) {
          return BLANK;
        }
        return (
          <Typography.Text
            style={{ maxWidth: 220 }}
            ellipsis={{
              tooltip: (
                <>
                  {record.roomTag ? `${record.roomTag}  ` : ''}
                  {record.roomName ?? ''}
                </>
              ),
            }}
          >
            {record.roomTag ? `${record.roomTag}  ` : ''}
            {record.roomName ?? ''}
          </Typography.Text>
        );
      },
    },
    {
      title: '测点名称',
      dataIndex: 'pointName',
    },
    {
      title: '实时值',
      dataIndex: 'realTime',
      render: (_, record) => {
        return (
          <PointChartText
            pointData={pointData[record.deviceGuid]?.get(point.code) as PointData}
            spaceGuid={blockGuid!}
            dataType={point.dataType}
            linkTextColorType="primary"
            showUnit
          />
        );
      },
    },
    {
      title: '规则状态',
      dataIndex: 'enable',
      render: (enable, record) => {
        return <LocalAlarmRuleItemStatusSwitch checked={enable} localAlarmRuleItem={record} />;
      },
    },
  ];
  return equipmentRuleColumns;
};

export type ColumnDataIndex =
  | 'deviceGuid'
  | 'deviceName'
  | 'vendor'
  | 'roomName'
  | 'pointName'
  | 'realTime'
  | 'enable';

export type LocalAlarmRuleItemTableProps = {
  blockGuid: string;
  localAlarmRule: LocalAlarmRuleJSON;
  dataIndexs?: ColumnDataIndex[];
  operation?: ColumnType<LocalAlarmRuleDevice>;
} & Omit<TableProps<LocalAlarmRuleDevice>, 'columns'>;

export function LocalAlarmRuleItemTable({
  blockGuid,
  localAlarmRule,
  dataIndexs = [
    'deviceGuid',
    'deviceName',
    'vendor',
    'roomName',
    'pointName',
    'realTime',
    'enable',
  ],
  operation,
  ...rest
}: LocalAlarmRuleItemTableProps) {
  const [configUtil] = useConfigUtil();

  const virtuallyDeviceMap: Record<'BLOCK' | 'ROOM' | 'IDC' | 'GRID' | 'COLUMN', string> =
    React.useMemo(() => {
      return {
        IDC: configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_IDC)!,
        BLOCK: configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_BLOCK)!,
        ROOM: configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_ROOM)!,
        GRID: configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_GRID)!,
        COLUMN: configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_COLUMN)!,
      };
    }, [configUtil]);

  const targetList: PointTarget[] = useDeepCompareMemo(() => {
    if (rest.dataSource) {
      const target = rest.dataSource.map(item => {
        return {
          guid: item.deviceGuid,
          type: item.deviceType,
          hardCodedPoints: [
            {
              pointCode: localAlarmRule.pointCode,
              dataType: localAlarmRule.dataType,
            },
          ],
        };
      });
      return target;
    }
    return [];
  }, [localAlarmRule.dataType, localAlarmRule.pointCode, rest.dataSource]);

  const [pointData] = usePointsData({
    blockGuid,
    moduleId: moduleId,
    targets: targetList,
    subscribeMethod: 'fetch',
  });

  const columns = useDeepCompareMemo(() => {
    const pointMap = {
      pointData,
      point: {
        code: localAlarmRule.pointCode,
        dataType: localAlarmRule.dataType,
      },
      blockGuid,
      virtuallyDeviceMap,
    };

    const newColumns = dataIndexs
      .map(dataIndex => {
        return getEquipmentRuleColumns(pointMap).find(item => item.dataIndex === dataIndex);
      })
      .filter((item): item is ColumnType<LocalAlarmRuleDevice> => item !== undefined);

    if (operation) {
      return [...newColumns, operation];
    }
    return newColumns;
  }, [
    blockGuid,
    dataIndexs,
    localAlarmRule.dataType,
    localAlarmRule.pointCode,
    operation,
    pointData,
    virtuallyDeviceMap,
  ]);

  return <Table columns={columns} scroll={{ x: 'max-content' }} rowKey="id" {...rest} />;
}
