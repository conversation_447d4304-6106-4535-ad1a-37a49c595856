import { EllipsisOutlined } from '@ant-design/icons';
import cloneDeep from 'lodash.clonedeep';
import React, { useCallback, useMemo, useRef, useState } from 'react';
import { useDeepCompareEffect } from 'react-use';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Dropdown } from '@manyun/base-ui.ui.dropdown';
import { Empty } from '@manyun/base-ui.ui.empty';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import type { ColumnType } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { batchOperationLocalAlarmRule } from '@manyun/monitoring.service.batch-operation-local-alarm-rule';
import { deleteLocalAlarmRule } from '@manyun/monitoring.service.delete-local-alarm-rule';
import type { LocalAlarmRuleJSON } from '@manyun/monitoring.service.fetch-local-alarm-rules';
import { AlarmMonitoringItemMutator } from '@manyun/monitoring.ui.alarm-monitoring-item-mutator';
import type { DeviceTreeNode } from '@manyun/resource-hub.ui.device-type-cascader';

import { useLocalAlarmRuleList } from '../../local-alarm-rule-list-context';
import { LocalAlarmRuleItem } from '../local-alarm-rule-item';
import { LocalAlarmRuleQueryAction } from '../local-alarm-rule-query-action';
import type { QueryFilterParams } from '../local-alarm-rule-query-filter';
import { LocalAlarmRuleQueryFilter, defaultFormValue } from '../local-alarm-rule-query-filter';
import type { ColumnDataIndex, LocalAlarmRuleTableProps } from '../local-alarm-rule-table';
import { LocalAlarmRuleTable, defaultColumnsConfig } from '../local-alarm-rule-table';

export type LocalAlarmRuleContentProps = {
  style?: React.CSSProperties;
  deviceTypeInfo?: DeviceTreeNode;
  loading: boolean;
  blockGuid: string;
  localAlarmRuleList: LocalAlarmRuleJSON[];
};

export function LocalAlarmRuleContent({
  style,
  deviceTypeInfo,
  loading,
  blockGuid,
  localAlarmRuleList,
}: LocalAlarmRuleContentProps) {
  const [, { getRuleList, onAddAlarmRule }] = useLocalAlarmRuleList();
  const [localAlarmRuleItemOpen, setLocalAlarmRuleItemOpen] = useState<boolean>(false);
  const [localAlarmRule, setLocalAlarmRule] = useState<LocalAlarmRuleJSON>();

  const [localAlarmRuleListFiltered, setLocalAlarmRuleListFiltered] = useState<
    LocalAlarmRuleJSON[]
  >([]);

  const [columnsConfig, setColumnsConfig] = useState<ColumnType[]>(defaultColumnsConfig);

  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  const filterParamsRef = useRef<QueryFilterParams & { content?: string }>({
    ...defaultFormValue,
    content: undefined,
  });

  const onSetColumnsConfigClick = (value: ColumnType[]) => {
    setColumnsConfig(value);
  };

  const onSelectMenu = useCallback(
    async (e: { key: string }, record) => {
      if (e.key === 'copy') {
        onAddAlarmRule({ ...record, id: undefined });
      }
      if (e.key === 'delete') {
        setLocalAlarmRule(record);
      }
    },
    [onAddAlarmRule]
  );

  const getLocalAlarmRuleList = useCallback(() => {
    const { alarmLevels, dataTypes, pointCategory, enableStatus, source, content } =
      filterParamsRef.current;

    let deepLocalAlarmRuleList = cloneDeep(localAlarmRuleList);

    if (alarmLevels && alarmLevels.length) {
      deepLocalAlarmRuleList = deepLocalAlarmRuleList.filter(item => {
        const alarmLevel = item.id ? String(item.alarmLevel) : '';
        return alarmLevels.includes(alarmLevel);
      });
    }
    if (dataTypes && dataTypes.length) {
      deepLocalAlarmRuleList = deepLocalAlarmRuleList.filter(item =>
        dataTypes.includes(item.dataType)
      );
    }
    if (pointCategory === 'NON_POINT') {
      deepLocalAlarmRuleList = deepLocalAlarmRuleList.filter(item => item.nonPoint);
    }
    if (pointCategory === 'NORMAL_POINT') {
      deepLocalAlarmRuleList = deepLocalAlarmRuleList.filter(item => !item.nonPoint);
    }
    if (enableStatus === 'ON') {
      deepLocalAlarmRuleList = deepLocalAlarmRuleList.filter(item => {
        if (item.id === null) {
          return false;
        }
        return item.enable;
      });
    }
    if (enableStatus === 'OFF') {
      deepLocalAlarmRuleList = deepLocalAlarmRuleList.filter(item => {
        if (item.id === null) {
          return false;
        }
        return !item.enable;
      });
    }
    if (source === 'CENTER') {
      deepLocalAlarmRuleList = deepLocalAlarmRuleList.filter(item => {
        if (item.id === null) {
          return false;
        }
        return item.source === 'CENTER';
      });
    }
    if (source === 'LOCAL') {
      deepLocalAlarmRuleList = deepLocalAlarmRuleList.filter(item => {
        if (item.id === null) {
          return false;
        }
        return item.source === 'LOCAL';
      });
    }
    if (content) {
      deepLocalAlarmRuleList = deepLocalAlarmRuleList.filter(item => {
        return (
          (item.pointName && item.pointName.toLowerCase().includes(content.toLowerCase())) ||
          item.pointCode.toLowerCase().includes(content.toLowerCase())
        );
      });
    }
    setLocalAlarmRuleListFiltered(deepLocalAlarmRuleList);
  }, [localAlarmRuleList]);

  useDeepCompareEffect(() => {
    getLocalAlarmRuleList();
  }, [getLocalAlarmRuleList, localAlarmRuleList]);

  const onQueryFilterChange = (params: QueryFilterParams) => {
    filterParamsRef.current = { ...filterParamsRef.current, ...params };
    getLocalAlarmRuleList();
  };

  const onInputSearch = (value: string) => {
    filterParamsRef.current = { ...filterParamsRef.current, content: value };
    getLocalAlarmRuleList();
  };

  const onDelete = useCallback(async () => {
    if (!localAlarmRule) {
      return;
    }
    const { error } = await deleteLocalAlarmRule({
      itemId: localAlarmRule.id as string,
    });

    if (error) {
      message.error(error.message);
      return;
    }
    message.success('删除规则成功！');
    getRuleList();
  }, [getRuleList, localAlarmRule]);

  const onBatchOperationLocalAlarmRule = async (operationType: 'ON' | 'OFF') => {
    if (!selectedRowKeys.length) {
      return;
    }
    const { error } = await batchOperationLocalAlarmRule({
      operationType,
      itemIdList: selectedRowKeys as number[],
    });
    if (error) {
      message.error(error.message);
      return;
    }
    message.success(operationType === 'ON' ? '批量启用规则成功！' : '批量停用规则成功！');
    setSelectedRowKeys([]);
    getRuleList();
  };

  const dataIndexs = useDeepCompareMemo(() => {
    return columnsConfig.filter(item => item.show).map(item => item.dataIndex as ColumnDataIndex);
  }, [columnsConfig]);

  const operation: LocalAlarmRuleTableProps['operation'] = useMemo(() => {
    const getItems = (record: LocalAlarmRuleJSON) => {
      if (!record.id) {
        return;
      }
      return [
        { label: '复制', key: 'copy' },
        {
          label:
            record.source === 'LOCAL' ? (
              <DeleteConfirm
                variant="popconfirm"
                targetName="测点"
                title="确认删除，删除后不可恢复"
                onOk={() => {
                  onDelete();
                  return Promise.resolve(true);
                }}
              >
                <>删除</>
              </DeleteConfirm>
            ) : (
              '删除'
            ),
          key: 'delete',
          disabled: record.source === 'CENTER',
        },
      ];
    };

    return {
      title: '操作',
      fixed: 'right',
      render: (_, record: LocalAlarmRuleJSON) => {
        return record.id ? (
          <Space>
            <Button
              type="link"
              compact
              onClick={() => {
                setLocalAlarmRule(record);
                setLocalAlarmRuleItemOpen(true);
              }}
            >
              清单
            </Button>
            {record.source === 'CENTER' && record.lock ? (
              <Button type="link" compact disabled>
                编辑
              </Button>
            ) : (
              <AlarmMonitoringItemMutator
                id={record.id}
                pointParams={{ isQueryNon: true, blockGuid, isRemoveSub: false }}
                addRulebuttonProps={{ type: 'link', compact: true }}
                alarmLevelDisabled={false}
                alarmTypeDisabled={false}
                onOk={onAddAlarmRule}
              />
            )}

            <Dropdown
              menu={{
                items: getItems(record),
                onClick: e => onSelectMenu(e, record),
              }}
              trigger={['click']}
              placement="bottomRight"
            >
              <EllipsisOutlined style={{ color: `var(--${prefixCls}-primary-color)` }} />
            </Dropdown>
          </Space>
        ) : (
          <AlarmMonitoringItemMutator
            showPointCodeMap={{
              pointCode: record.pointCode,
              pointName: record.pointName as string,
              deviceType: record.deviceType,
            }}
            pointParams={{ isQueryNon: true, blockGuid, isRemoveSub: false }}
            addRulebuttonProps={{ type: 'link', compact: true }}
            onOk={onAddAlarmRule}
          />
        );
      },
    };
  }, [blockGuid, onAddAlarmRule, onDelete, onSelectMenu]);

  const rowSelection = {
    getCheckboxProps: (record: LocalAlarmRuleJSON) => ({
      disabled: (record.source === 'CENTER' && record.lock) || !record?.id,
    }),
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
    },
  };

  const bodyStyle: React.CSSProperties = useMemo(() => {
    if (deviceTypeInfo?.type === 'C2') {
      return {
        height: 'calc(100% - 57px)',
        overflowY: 'auto',
        paddingBottom: selectedRowKeys.length ? 66 : 0,
      };
    }

    return {
      height: '100%',
      overflowY: 'auto',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    };
  }, [deviceTypeInfo?.type, selectedRowKeys.length]);

  return (
    <Card
      style={{ ...style, position: 'relative' }}
      bodyStyle={bodyStyle}
      title={
        deviceTypeInfo?.type === 'C2' && (
          <Typography.Title level={5} showBadge>
            {deviceTypeInfo.title}
          </Typography.Title>
        )
      }
    >
      {deviceTypeInfo?.type === 'C2' ? (
        <>
          <Space direction="vertical" style={{ width: '100%' }}>
            <LocalAlarmRuleQueryAction
              blockGuid={blockGuid}
              deviceTypeInfo={deviceTypeInfo}
              onSetColumnsConfigClick={onSetColumnsConfigClick}
              onSearch={onInputSearch}
            />
            <LocalAlarmRuleQueryFilter onChange={onQueryFilterChange} />
          </Space>

          <LocalAlarmRuleTable
            dataIndexs={dataIndexs}
            loading={loading}
            operation={operation}
            dataSource={localAlarmRuleListFiltered}
            rowSelection={rowSelection}
            pagination={false}
          />

          {localAlarmRule && (
            <LocalAlarmRuleItem
              blockGuid={blockGuid}
              localAlarmRule={localAlarmRule}
              open={localAlarmRuleItemOpen}
              onClose={() => setLocalAlarmRuleItemOpen(false)}
            />
          )}
          {!!selectedRowKeys.length && (
            <Card
              style={{
                position: 'absolute',
                width: 'calc(100% + 2px)',
                bottom: 0,
                left: -1,
                zIndex: 2,
              }}
              bodyStyle={{ padding: 14 }}
            >
              <Space>
                <DeleteConfirm
                  variant="popconfirm"
                  targetName="启用规则"
                  title="您即将批量启用规则，请谨慎操作！"
                  onOk={() => {
                    onBatchOperationLocalAlarmRule('ON');
                    return Promise.resolve(true);
                  }}
                >
                  <Button>批量启用</Button>
                </DeleteConfirm>
                <DeleteConfirm
                  variant="popconfirm"
                  targetName="停用规则"
                  title="您即将批量停用规则，请谨慎操作！"
                  onOk={() => {
                    onBatchOperationLocalAlarmRule('OFF');
                    return Promise.resolve(true);
                  }}
                >
                  <Button>批量停用</Button>
                </DeleteConfirm>

                <Typography.Text>已选择 {selectedRowKeys.length} 项</Typography.Text>
                <Typography.Link onClick={() => setSelectedRowKeys([])}>取消选择</Typography.Link>
              </Space>
            </Card>
          )}
        </>
      ) : (
        <Empty description="暂无数据，请选择三级分类后查看" />
      )}
    </Card>
  );
}
