import React from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Space } from '@manyun/base-ui.ui.space';

import { AlarmLevelSelect } from '@manyun/monitoring.ui.alarm-level-select';
import { LocalAlarmRuleOriginSelect } from '@manyun/monitoring.ui.local-alarm-rule-origin-select';
import { LocalAlarmRuleStatusSelect } from '@manyun/monitoring.ui.local-alarm-rule-status-select';
import { PointCategorySelect } from '@manyun/monitoring.ui.point-category-select';
import { PointTypeSelect } from '@manyun/resource-hub.ui.point-type-select';

export type QueryFilterParams = {
  alarmLevels?: string[];
  dataTypes?: string[];
  pointCategory?: string;
  enableStatus?: string;
  source?: string;
};

export const defaultFormValue: QueryFilterParams = {
  alarmLevels: undefined,
  dataTypes: undefined,
  pointCategory: undefined,
  enableStatus: undefined,
  source: undefined,
};

export type LocalAlarmRuleQueryFilterProps = {
  onChange: (params: QueryFilterParams) => void;
};

export function LocalAlarmRuleQueryFilter({ onChange }: LocalAlarmRuleQueryFilterProps) {
  const [form] = Form.useForm();

  const onFinish = (values: QueryFilterParams) => {
    onChange(values);
  };
  return (
    <div>
      <Form
        layout="vertical"
        form={form}
        onFinish={onFinish}
        onReset={() => {
          form.resetFields();
          onChange(defaultFormValue);
        }}
      >
        <Row gutter={16}>
          <Col span={4}>
            <Form.Item label="测点类型" name="dataTypes">
              <PointTypeSelect
                style={{ width: '100%' }}
                placeholder="不限"
                allowClear
                mode="multiple"
                maxTagCount="responsive"
              />
            </Form.Item>
          </Col>
          <Col span={4}>
            <Form.Item label="测点分类" name="pointCategory">
              <PointCategorySelect style={{ width: '100%' }} allowClear placeholder="不限" />
            </Form.Item>
          </Col>
          <Col span={4}>
            <Form.Item label="告警等级" name="alarmLevels">
              <AlarmLevelSelect
                allowClear
                mode="multiple"
                maxTagCount="responsive"
                placeholder="不限"
              />
            </Form.Item>
          </Col>
          <Col span={4}>
            <Form.Item label="启用状态" name="enableStatus">
              <LocalAlarmRuleStatusSelect allowClear placeholder="不限" />
            </Form.Item>
          </Col>
          <Col span={4}>
            <Form.Item label="规则来源" name="source">
              <LocalAlarmRuleOriginSelect allowClear placeholder="不限" />
            </Form.Item>
          </Col>
          <Col span={4}>
            <Space style={{ marginTop: 30 }}>
              <Button htmlType="reset">重置</Button>
              <Button type="primary" htmlType="submit">
                搜索
              </Button>
            </Space>
          </Col>
        </Row>
      </Form>
    </div>
  );
}
