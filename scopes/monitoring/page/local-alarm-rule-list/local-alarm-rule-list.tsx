import saveAs from 'file-saver';
import React, { useCallback, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation } from 'react-router-dom';
import { useShallowCompareEffect } from 'react-use';

import { selectMyResources } from '@manyun/auth-hub.state.user';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Divider } from '@manyun/base-ui.ui.divider';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import type { TreeProps } from '@manyun/base-ui.ui.tree';
import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';
import { generateVersionManageDetailRoutePath } from '@manyun/dc-brain.route.admin-routes';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { MonitoringItem } from '@manyun/monitoring.model.monitoring-item';
import type { MonitoringItemJSON } from '@manyun/monitoring.model.monitoring-item';
import { generateLocalAlarmRuleImportRoutePath } from '@manyun/monitoring.route.monitoring-routes';
import { exportLocalAlarmRule } from '@manyun/monitoring.service.export-local-alarm-rule';
import type {
  SvcQuery as FetchLocalAlarmRulesParams,
  LocalAlarmRuleJSON,
} from '@manyun/monitoring.service.fetch-local-alarm-rules';
import { fetchLocalAlarmRules } from '@manyun/monitoring.service.fetch-local-alarm-rules';
import { updateLocalAlarmRule } from '@manyun/monitoring.service.update-local-alarm-rule';
import { alarmConfigurationTemplateSliceActions } from '@manyun/monitoring.state.alarm-configuration-template';
import { DeviceTypeTree } from '@manyun/resource-hub.ui.device-type-cascader';
import type { DeviceTreeNode } from '@manyun/resource-hub.ui.device-type-cascader';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

import { LocalAlarmRuleContent } from './components/local-alarm-rule-content';
import { LocalAlarmRuleHead } from './components/local-alarm-rule-head';
import { LocalAlarmRuleListContext } from './local-alarm-rule-list-context';

export function LocalAlarmRuleList() {
  const dispatch = useDispatch();
  const { search } = useLocation()!;
  const { idc } = getLocationSearchMap<{
    idc?: string;
  }>(search);
  const myResource = useSelector(selectMyResources);
  const blockGuids = myResource
    .filter(resource => resource.parentCode === idc)
    .map(resource => resource.code);
  const [deviceTypeInfo, setDeviceTypeInfo] = useState<DeviceTreeNode>();

  const [blockGuid, setBlockGuid] = useState<string | null>(null);

  const [showAlarm, setShowAlarm] = useState<boolean>(false);

  const [loading, setLoading] = useState<boolean>(false);
  const [exportLoading, setExportLoading] = useState(false);

  const [localAlarmRuleList, setLocalAlarmRuleList] = useState<LocalAlarmRuleJSON[]>([]);

  const getRuleList = useCallback(
    async (params?: Partial<FetchLocalAlarmRulesParams>) => {
      const realBlockGuid = params?.blockGuid ?? blockGuid;
      const realDeviceType = params?.deviceType ?? deviceTypeInfo?.value;

      if (!realBlockGuid || !realDeviceType) {
        return;
      }

      setLoading(true);
      const { data, error } = await fetchLocalAlarmRules({
        blockGuid: realBlockGuid,
        deviceType: realDeviceType,
        showAlarm,
        ...params,
      });
      setLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }

      const monitorItemEntities = data.data.reduce(
        (result: Record<string, MonitoringItemJSON>, item) => {
          if (item.id) {
            result[item.id] = item;
          }
          return result;
        },
        {}
      );

      const monitorItemIds = data.data
        .filter((item): item is LocalAlarmRuleJSON & { id: string } => item.id !== null)
        .map(item => item.id);

      dispatch(
        alarmConfigurationTemplateSliceActions.setCurrentMutateType({
          type: 'create',
        })
      );
      dispatch(
        alarmConfigurationTemplateSliceActions.setMutateFields({
          mutateType: 'create',
          target: realDeviceType,
          monitorItemEntities,
          monitorItemIds,
        })
      );
      setLocalAlarmRuleList(data.data);
    },
    [blockGuid, deviceTypeInfo?.value, dispatch, showAlarm]
  );

  const onAddAlarmRule = useCallback(
    async (monitorItem: MonitoringItemJSON, callback?: () => void) => {
      if (!blockGuid) {
        return;
      }
      const defaultNotifyRule =
        // eslint-disable-next-line no-template-curly-in-string
        '${ALARM_LEVEL}${ALARM_TYPE},${BLOCK!}楼${ROOM!}包间,${DEVICE_TYPE}_${POINT}_${TRIGGER_CONDITION}_告警值:${TRIGGER_VALUE},告警设备:${FAULT_DEVICE!},告警时间:${TIME?string("MM-dd HH:mm:ss")}';

      const monitoringItem = MonitoringItem.toApiObject(monitorItem as MonitoringItem);

      const { error } = await updateLocalAlarmRule({
        ...monitoringItem,
        notifyRule: monitoringItem.notifyRule ? monitoringItem.notifyRule : defaultNotifyRule,
        blockGuid,
      });
      if (error) {
        message.error(error.message);
        return;
      }

      if (monitorItem.id) {
        message.success('编辑规则成功！');
      } else {
        message.success('添加规则成功！');
      }
      callback?.();
      getRuleList();
    },
    [blockGuid, getRuleList]
  );

  const onSelectDeviceType: TreeProps['onSelect'] = (selectedKeys, info) => {
    if (selectedKeys.length) {
      const deviceTypeInfo = info.selectedNodes[0] as DeviceTreeNode;

      setDeviceTypeInfo(deviceTypeInfo);
      if (deviceTypeInfo.type === 'C2') {
        getRuleList({ deviceType: deviceTypeInfo.value });
      }
      return;
    }
    setDeviceTypeInfo(undefined);
  };

  const onClearDate = () => {
    setLocalAlarmRuleList([]);
    setDeviceTypeInfo(undefined);
  };

  const onBlockTagChange = (value: string) => {
    if (value) {
      setBlockGuid(value);
      onClearDate();
    }
  };

  useShallowCompareEffect(() => {
    if (blockGuids.length && idc) {
      setBlockGuid(blockGuids[0]);
      onClearDate();
    }
  }, [blockGuids, idc]);

  const onHandleExportAlarmRule = async () => {
    if (!blockGuid) {
      return;
    }
    setExportLoading(true);
    const { error, data } = await exportLocalAlarmRule({ blockGuid });

    setExportLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    saveAs(data, `${blockGuid}_告警规则.xlsx`);
  };

  const blockTag = useMemo(
    () => (blockGuid ? getSpaceGuidMap(blockGuid).block! : undefined),
    [blockGuid]
  );

  const config = useSelector(selectCurrentConfig);
  const configUtil = new ConfigUtil(config);
  const idcType = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_IDC);

  return (
    <LocalAlarmRuleListContext.Provider
      value={[{ showAlarm }, { setShowAlarm, getRuleList, onAddAlarmRule }]}
    >
      <Space direction="vertical" size="middle" style={{ width: '100%' }}>
        {blockGuid && <LocalAlarmRuleHead blockGuid={blockGuid} onChange={onBlockTagChange} />}
        <div style={{ display: 'flex', gap: 16, height: 'calc(100vh - 170px)' }}>
          <Card
            style={{ width: 336, height: '100%' }}
            bodyStyle={{
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'space-between',
              paddingBottom: 16,
            }}
          >
            {getSpaceGuidMap(blockGuid!).idc && blockTag && (
              <DeviceTypeTree
                style={{ width: 200 }}
                treeStyle={{
                  overflowY: 'auto',
                  height: 'calc((var(--content-height) - 200px)',
                }}
                idcTag={getSpaceGuidMap(blockGuid!).idc!}
                blockTag={blockTag}
                selectedKeys={deviceTypeInfo?.value ? [deviceTypeInfo.value] : []}
                numbered
                dataType={['snDevice', 'space']}
                inputProps={{
                  style: { marginBottom: 8 },
                  placeholder: '输入搜索内容',
                }}
                optionFilter={item => {
                  if (item.metaStyle === 'SPACE') {
                    return item.metaCode !== idcType;
                  }
                  return true;
                }}
                onSelect={onSelectDeviceType}
              />
            )}
            <div>
              <Divider
                style={{
                  width: 'calc(100% + 48px)',
                  margin: '16px 0 16px -24px',
                }}
              />
              <Space>
                <Button
                  target="_blank"
                  type="primary"
                  href={generateVersionManageDetailRoutePath({ idc, tab: 'NON_ALARM' })}
                >
                  发布规则
                </Button>
                <Button target="_blank" href={generateLocalAlarmRuleImportRoutePath({ blockGuid })}>
                  导入规则
                </Button>
                <Button loading={exportLoading} onClick={onHandleExportAlarmRule}>
                  导出规则
                </Button>
              </Space>
            </div>
          </Card>
          {blockGuid && (
            <LocalAlarmRuleContent
              style={{ width: 'calc(100% - 352px)', height: '100%' }}
              loading={loading}
              deviceTypeInfo={deviceTypeInfo}
              blockGuid={blockGuid}
              localAlarmRuleList={localAlarmRuleList}
            />
          )}
        </div>
      </Space>
    </LocalAlarmRuleListContext.Provider>
  );
}
