import { createContext, useContext } from 'react';

import type { MonitoringItemJSON } from '@manyun/monitoring.model.monitoring-item';
import type { SvcQuery as localAlarmRulesParams } from '@manyun/monitoring.service.fetch-local-alarm-rules';

export type Values = {
  showAlarm: boolean;
};

export type Handlers = {
  setShowAlarm: (value: boolean) => void;
  getRuleList: (params?: Partial<localAlarmRulesParams>) => void;
  onAddAlarmRule: (params: MonitoringItemJSON) => void;
};

export type LocalAlarmRuleListContextProps = [Values, Handlers];

const initialValue: LocalAlarmRuleListContextProps = [
  { showAlarm: false },
  {
    setShowAlarm: () => {},
    getRuleList: () => {},
    onAddAlarmRule: () => {},
  },
];

export const LocalAlarmRuleListContext =
  createContext<LocalAlarmRuleListContextProps>(initialValue);

export const useLocalAlarmRuleList = () => useContext(LocalAlarmRuleListContext);
