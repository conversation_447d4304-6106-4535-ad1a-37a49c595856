import React, { useContext } from 'react';

import { Switch } from '@manyun/base-ui.ui.switch';
import type { LocalAlarmNotice } from '@manyun/monitoring.service.fetch-local-alarm-notices';

import { AlarmNoticesContext } from '../../alarm-notice-configurations.context';

export type AlarmNoticeConfigurationSwitchProps = {
  localAlarmNotice: LocalAlarmNotice;
};

export function AlarmNoticeConfigurationSwitch({
  localAlarmNotice,
}: AlarmNoticeConfigurationSwitchProps) {
  const [{ updateLocalAlarmNoticeStatus }] = useContext(AlarmNoticesContext);

  const onChange = (checked: boolean) => {
    updateLocalAlarmNoticeStatus({ ...localAlarmNotice, notified: checked });
  };
  return <Switch checked={localAlarmNotice.notified} onChange={onChange} />;
}
