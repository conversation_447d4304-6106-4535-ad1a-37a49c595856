import dayjs from 'dayjs';
import React from 'react';

import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Popover } from '@manyun/base-ui.ui.popover';
import { Space } from '@manyun/base-ui.ui.space';
import type { ColumnType, TableProps } from '@manyun/base-ui.ui.table';
import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { generateAlarmNoticeConfigurationRoutePath } from '@manyun/monitoring.route.monitoring-routes';
import type { LocalAlarmNotice } from '@manyun/monitoring.service.fetch-local-alarm-notices';
import { AlarmLevelText } from '@manyun/monitoring.ui.alarm-level-text';
import { AlarmNoticeTypeText } from '@manyun/monitoring.ui.alarm-notice-type';

import { AlarmNoticeConfigurationSwitch } from '../alarm-notice-configuration-switch';

const alarmNoticeConfigurationColumns: ColumnType<LocalAlarmNotice>[] = [
  {
    title: '通报项目名称',
    dataIndex: 'name',
    fixed: 'left',
    render: (_, record) => {
      if (record.id) {
        return (
          <Popover content={<div style={{ maxWidth: 360 }}>{record.name}</div>}>
            <Typography.Link
              style={{ maxWidth: 360 }}
              ellipsis
              onClick={() => {
                window.open(
                  generateAlarmNoticeConfigurationRoutePath({ id: String(record.id) }),
                  '_blank'
                );
              }}
            >
              {record.name}
            </Typography.Link>
          </Popover>
        );
      }
      return null;
    },
  },
  {
    title: '通报类型',
    dataIndex: 'type',
    render: (_, record) => {
      return <AlarmNoticeTypeText type={record.type} />;
    },
  },
  {
    title: '适用告警等级',
    dataIndex: 'alarmLevels',
    render: alarmLevels => {
      return (
        <Space>
          {alarmLevels?.split(',').map((item: string) => <AlarmLevelText key={item} code={item} />)}
        </Space>
      );
    },
  },
  {
    title: '适用模版组',
    dataIndex: 'schemeList',
    render: schemeList => {
      if (!schemeList?.length) {
        return '--';
      }
      return (
        <Space split={<Divider type="vertical" />}>
          {schemeList.map((item: { id: number; name: string }) => item.name)}
        </Space>
      );
    },
  },
  {
    title: '状态',
    dataIndex: 'notified',
    render: (_, record) => <AlarmNoticeConfigurationSwitch localAlarmNotice={record} />,
  },
  {
    title: '操作人',
    dataIndex: 'operator',
    render: (_, record) => (
      <UserLink userId={Number(record.operatorId)} userName={record.operatorName} />
    ),
  },
  {
    title: '操作时间',
    dataIndex: 'gmtModified',
    render: gmtModified => dayjs(gmtModified).format('YYYY-MM-DD HH:mm:ss'),
  },
];

export type ColumnDataIndex =
  | 'name'
  | 'type'
  | 'alarmLevels'
  | 'deviceType'
  | 'schemeList'
  | 'notified'
  | 'operator'
  | 'gmtModified';

export type AlarmNoticeConfigurationTableProps = {
  dataIndexs?: ColumnDataIndex[];
  operation?: ColumnType<LocalAlarmNotice>;
} & Omit<TableProps<LocalAlarmNotice>, 'columns'>;

export function AlarmNoticeConfigurationTable({
  dataIndexs = [
    'name',
    'type',
    'alarmLevels',
    'deviceType',
    'schemeList',
    'notified',
    'operator',
    'gmtModified',
  ],
  operation,
  ...rest
}: AlarmNoticeConfigurationTableProps) {
  const columns = useDeepCompareMemo(() => {
    const newColumns = dataIndexs
      .map(dataIndex => {
        return alarmNoticeConfigurationColumns.find(item => item.dataIndex === dataIndex);
      })
      .filter((item): item is ColumnType<LocalAlarmNotice> => item !== undefined);

    if (operation) {
      return [...newColumns, operation];
    }
    return newColumns;
  }, [dataIndexs, operation]);

  return (
    <Table
      columns={columns}
      scroll={{ x: 'max-content' }}
      tableLayout="fixed"
      rowKey="id"
      {...rest}
    />
  );
}
