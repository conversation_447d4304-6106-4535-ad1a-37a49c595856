import React, { useCallback, useEffect, useMemo, useState } from 'react';

import { useAuthorized } from '@manyun/auth-hub.hook.gql.use-authorized';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { generateAlarmNoticeConfigurationEditRoutePath } from '@manyun/monitoring.route.monitoring-routes';
import { deleteLocalAlarmNotice } from '@manyun/monitoring.service.delete-local-alarm-notice';
import type {
  ApiArgs as FetchLocalAlarmNoticesParams,
  LocalAlarmNotice,
} from '@manyun/monitoring.service.fetch-local-alarm-notices';
import { fetchLocalAlarmNotices } from '@manyun/monitoring.service.fetch-local-alarm-notices';
import { updateLocalAlarmNoticeStatus } from '@manyun/monitoring.service.update-local-alarm-notice-status';
import { AlarmNoticeTypeSelect } from '@manyun/monitoring.ui.alarm-notice-type';

import { AlarmNoticesContext } from './alarm-notice-configurations.context';
import type { AlarmNoticeConfigurationTableProps } from './components/alarm-notice-configuration-table';
import { AlarmNoticeConfigurationTable } from './components/alarm-notice-configuration-table';

export function AlarmNoticeConfigurations() {
  const [, { checkCode }] = useAuthorized();

  const [noticeName, setNoticename] = useState<string>();
  const [enableStatus, setEnableStatus] = useState<boolean>();
  const [noticeType, setNoticeTypee] = useState<string[]>();

  const [loading, setLoading] = useState(false);
  const [localAlarmNotices, setLocalAlarmNotices] = useState<LocalAlarmNotice[]>([]);

  const [pagination, setPagination] = useState<{
    pageNum: number;
    pageSize: number;
    total: number;
  }>({ pageNum: 1, pageSize: 10, total: 0 });

  const fetchParams: FetchLocalAlarmNoticesParams = useMemo(() => {
    return {
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize,
      targetIds: ['ALL'],
      name: noticeName,
      enable: enableStatus,
      types: noticeType,
    };
  }, [enableStatus, noticeName, noticeType, pagination.pageNum, pagination.pageSize]);

  const getLocalAlarmNotices = async (params: FetchLocalAlarmNoticesParams) => {
    setLoading(true);
    const { data, error } = await fetchLocalAlarmNotices(params);

    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    setPagination({ pageNum: params.pageNum, pageSize: params.pageSize, total: data.total });
    setLocalAlarmNotices(data.data);
  };

  useEffect(() => {
    getLocalAlarmNotices(fetchParams);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onNoticeSearch = (value: string) => {
    getLocalAlarmNotices({ ...fetchParams, name: value, pageNum: 1 });
    setNoticename(value);
  };

  const onEnableStatusChange = async (value: boolean) => {
    setEnableStatus(value);
    getLocalAlarmNotices({ ...fetchParams, enable: value, pageNum: 1 });
  };

  const onNoticeTypeChange = async (types: string[]) => {
    setNoticeTypee(types);
    getLocalAlarmNotices({ ...fetchParams, types, pageNum: 1 });
  };

  const onChangePage = (pageNum: number, pageSize: number) => {
    getLocalAlarmNotices({ ...fetchParams, pageNum, pageSize });
  };

  const onUpdateLocalAlarmNoticeStatus = async (localAlarmNotice: LocalAlarmNotice) => {
    setLoading(true);
    const { error } = await updateLocalAlarmNoticeStatus({
      id: localAlarmNotice.id,
      notified: localAlarmNotice.notified,
    });
    if (error) {
      message.error(error.message);
      return;
    }
    message.success('更新状态成功！');
    getLocalAlarmNotices(fetchParams);
  };

  const onDelete = useCallback(
    async (id: number) => {
      const { error } = await deleteLocalAlarmNotice({ id });
      if (error) {
        message.error(error.message);
        return;
      }
      message.success('删除成功！');
      getLocalAlarmNotices({ ...fetchParams, pageNum: 1 });
    },
    [fetchParams]
  );

  const operation: AlarmNoticeConfigurationTableProps['operation'] = useMemo(() => {
    return {
      title: '操作',
      fixed: 'right',
      render: (_, record: LocalAlarmNotice) => {
        return (
          <Space>
            {checkCode('element-alarm-notice-configuration_operate') && (
              <Button
                type="link"
                href={generateAlarmNoticeConfigurationEditRoutePath({
                  id: String(record.id),
                  type: 'edit',
                })}
                target="_blank"
                compact
              >
                编辑
              </Button>
            )}
            {checkCode('element-alarm-notice-configuration_operate') && (
              <Button
                type="link"
                href={generateAlarmNoticeConfigurationEditRoutePath({
                  id: String(record.id),
                  type: 'copy',
                })}
                target="_blank"
                compact
              >
                复制
              </Button>
            )}
            {checkCode('element-alarm-notice-configuration_operate') && (
              <DeleteConfirm
                variant="popconfirm"
                targetName="告警通报"
                title="您即将删除该数据，请谨慎操作！"
                onOk={() => {
                  onDelete(record.id);
                  return Promise.resolve(true);
                }}
              >
                <Button type="link" compact>
                  删除
                </Button>
              </DeleteConfirm>
            )}
          </Space>
        );
      },
    };
  }, [checkCode, onDelete]);

  return (
    <Card>
      <Space style={{ width: '100%' }} size="middle" direction="vertical">
        <Typography.Title showBadge level={5}>
          告警通报配置
        </Typography.Title>
        <Space>
          {checkCode('element-alarm-notice-configuration_operate') && (
            <Button
              type="primary"
              href={generateAlarmNoticeConfigurationEditRoutePath({
                id: 'new',
                type: 'create',
              })}
              target="_blank"
            >
              新建
            </Button>
          )}

          <Input.Search
            style={{ width: 251 }}
            placeholder="通报项目名称"
            allowClear
            value={noticeName}
            onChange={e => {
              setNoticename(e.target.value);
            }}
            onSearch={onNoticeSearch}
          />

          <AlarmNoticeTypeSelect
            style={{ width: 232 }}
            placeholder="通报类型"
            allowClear
            mode="multiple"
            maxTagCount="responsive"
            value={noticeType}
            onChange={onNoticeTypeChange}
          />
          <Select
            style={{ width: 232 }}
            value={enableStatus}
            options={[
              { label: '启用', value: true },
              { label: '禁用', value: false },
            ]}
            allowClear
            placeholder="启用状态"
            onChange={onEnableStatusChange}
          />
        </Space>
        <AlarmNoticesContext.Provider
          value={[{ updateLocalAlarmNoticeStatus: onUpdateLocalAlarmNoticeStatus }]}
        >
          <AlarmNoticeConfigurationTable
            dataSource={localAlarmNotices}
            loading={loading}
            operation={operation}
            pagination={{
              total: pagination.total,
              current: pagination.pageNum,
              pageSize: pagination.pageSize,
              onChange: onChangePage,
            }}
          />
        </AlarmNoticesContext.Provider>
      </Space>
    </Card>
  );
}
