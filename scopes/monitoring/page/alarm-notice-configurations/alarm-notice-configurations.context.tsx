import { createContext } from 'react';

import type { LocalAlarmNotice } from '@manyun/monitoring.service.fetch-local-alarm-notices';

export type Handlers = {
  updateLocalAlarmNoticeStatus: (localAlarmNotice: LocalAlarmNotice) => void;
};
export type AlarmNoticesContextProps = [Handlers];
const initialValue: AlarmNoticesContextProps = [
  {
    updateLocalAlarmNoticeStatus: () => {},
  },
];
export const AlarmNoticesContext = createContext<AlarmNoticesContextProps>(initialValue);
