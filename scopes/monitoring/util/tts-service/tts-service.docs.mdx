---
description: 'A Text To Speech utility component'
---

\[DEPRECATED WARNING!!!\] It was deprecated in favor of `util/alarms-tts-manager`

```ts
ttsService.speak({
    text: 'Hello, how are you today ?',
    queue: false // current speech will be interrupted,
    listeners: {
        onstart: () => {
            console.log("Start utterance")
        },
        onend: () => {
            console.log("End utterance")
        },
        onresume: () => {
            console.log("Resume utterance")
        },
        onboundary: (event) => {
            console.log(event.name + ' boundary reached after ' + event.elapsedTime + ' milliseconds.')
        }
    }
}).then(() => {
    console.log("Success !")
}).catch(e => {
    console.error("An error occurred :", e)
})
```
