// @ts-expect-error: TS7016 because of no TS type definitions
import Speech from 'speak-tts';

export type SpeechOptions = {
  volume?: number;
  rate?: number;
  pitch?: number;
  lang?: string;
  voice?: string;
  splitSentences?: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  listeners?: any;
};

export type SimpleAlarm = {
  id: number;
  level: string;
  message: string;
};

export type SpeakOptions = {
  alarm: SimpleAlarm;
  queue?: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  listeners?: any;
};

export type TtsServiceOptions = {
  speechOptions?: SpeechOptions;
};

const defaultSpeechOptions: SpeechOptions = {
  volume: 1,
  rate: 1,
  pitch: 1,
  lang: 'zh-CN',
  voice: 'Microsoft Xiaoxiao Online (Natural) - Chinese (Mainland)',
};

export class TtsService {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private speech: any;
  private _current?: SimpleAlarm;

  constructor({ speechOptions }: TtsServiceOptions = {}) {
    this.speech = new Speech();
    this.speech.init({ ...defaultSpeechOptions, ...speechOptions });
  }

  public getCurrentSpeakingAlarm(): SimpleAlarm | undefined {
    return this._current;
  }

  public speak({ alarm, ...options }: SpeakOptions): Promise<void> {
    return this.speech.speak({
      ...options,
      text: alarm.message,
      listeners: {
        onstart: () => {
          this._current = alarm;
          options.listeners?.onstart?.();
        },
        onended: () => {
          this._current = undefined;
          options.listeners?.onended?.();
        },
      },
    });
  }

  /**@deprecated */
  public pause() {
    if (this.speech.speaking()) {
      this.speech.pause();
    }
  }

  /**@deprecated */
  public resume() {
    if (this.speech.paused()) {
      this.speech.resume();
    }
  }

  public cancel() {
    if (this.speech.pending() || this.speech.speaking()) {
      this.speech.cancel();
    }
    this._current = undefined;
  }
}

export const ttsService = new TtsService();
