import { Observable, Subscriber } from 'rxjs';

/**
 * @see https://github.com/BabylonJS/Babylon.js/blob/master/packages/dev/core/src/Audio/audioEngine.ts#L24
 */
export class AudioEngine {
  private _audioContext?: AudioContext;
  private _audioContextInitialized: boolean = false;
  private _onAudioStateChangedSubscriber?: Subscriber<AudioContextState>;

  public canUseWebAudio: boolean = false;
  public unlocked: boolean = true;
  public onAudioStateChangedObservable: Observable<AudioContextState>;

  public get audioContext(): AudioContext | undefined {
    if (!this._audioContextInitialized) {
      this._initializeAudioContext();
    }
    return this._audioContext;
  }

  constructor() {
    if (
      typeof window.AudioContext != 'undefined' ||
      // @ts-expect-error ts(2339)
      typeof window.webkitAudioContext !== 'undefined'
    ) {
      // @ts-expect-error ts(2339)
      window.AudioContext = window.AudioContext || window.webkitAudioContext;
      this.canUseWebAudio = true;
    }
    this.onAudioStateChangedObservable = new Observable<AudioContextState>(subscriber => {
      this._onAudioStateChangedSubscriber = subscriber;
    });
  }

  private _initializeAudioContext() {
    try {
      if (this.canUseWebAudio) {
        if (!this._audioContext) {
          this._audioContext = new AudioContext();
        }
        this._audioContextInitialized = true;
      }
    } catch (error) {
      this.canUseWebAudio = false;
      console.error(error);
    }
  }

  public lock(): void {
    this._triggerSuspendedState();
  }

  public unlock(): void {
    this._triggerRunningState();
  }

  private _triggerSuspendedState() {
    this.unlocked = false;
    this._onAudioStateChangedSubscriber?.next('suspended');
  }

  private _tryToRun = false;
  private _triggerRunningState() {
    if (this._tryToRun) {
      return;
    }
    this._tryToRun = true;

    this._resumeAudioContext()
      .then(() => {
        this._tryToRun = false;
        // Notify users that the audio stack is unlocked/unmuted
        this.unlocked = true;
        this._onAudioStateChangedSubscriber?.next('running');
      })
      .catch(() => {
        this._tryToRun = false;
        this.unlocked = false;
      });
  }

  private _resumeAudioContext(): Promise<void> {
    let result: Promise<void>;
    if (this._audioContext!.resume !== undefined) {
      result = this._audioContext!.resume();
    }
    return result! || Promise.resolve();
  }
}

export const audioEngine = new AudioEngine();
