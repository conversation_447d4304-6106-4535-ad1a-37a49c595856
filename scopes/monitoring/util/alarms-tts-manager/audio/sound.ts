import { Observable, Observer, Subscriber, Subscription } from 'rxjs';

import { audioEngine } from './audio-engine';

export type SoundOptions = {
  autoPlay?: boolean;
  loop?: boolean;
  offset?: number;
  length?: number;
};

/**
 * @see https://github.com/BabylonJS/Babylon.js/blob/master/packages/dev/core/src/Audio/sound.ts#L19
 */
export class Sound {
  private _audioBuffer?: AudioBuffer;
  private _soundSource?: AudioBufferSourceNode;
  private _isReadyToPlay: boolean = false;
  private _loop: boolean = false;
  private _startTime: number = 0;
  private _startOffset: number = 0;
  private _offset?: number;
  private _length?: number;
  private _onEndSubscriber?: Subscriber<void>;
  private _onEndSubscription?: Subscription;

  public name: string;
  public autoplay: boolean = false;
  public isPlaying: boolean = false;
  public isPaused: boolean = false;
  public onEndObservable: Observable<void>;

  constructor(
    name: string,
    arrayBuffer: ArrayBuffer,
    onEndObserver?: Pick<Observer<void>, 'complete'>,
    options?: SoundOptions
  ) {
    this.name = name;

    this.onEndObservable = new Observable<void>(subscriber => {
      this._onEndSubscriber = subscriber;
    });
    if (onEndObserver) {
      this._onEndSubscription = this.onEndObservable.subscribe(onEndObserver);
    }

    if (options) {
      this.autoplay = options.autoPlay ?? false;
      this._loop = options.loop ?? false;
      this._length = options.length;
      this._offset = options.offset;
    }

    if (audioEngine.canUseWebAudio && audioEngine.audioContext) {
      if (arrayBuffer.byteLength > 0) {
        this._soundLoaded(arrayBuffer);
      }
    }
  }

  private async _soundLoaded(audioData: ArrayBuffer) {
    if (!audioEngine.audioContext) {
      return;
    }
    try {
      const audioBuffer = await audioEngine.audioContext.decodeAudioData(audioData);
      this._audioBuffer = audioBuffer;
      this._isReadyToPlay = true;
      if (this.autoplay) {
        this.play(0, this._offset, this._length);
      }
    } catch (error) {
      console.error(error);
    }
  }

  public play(time?: number, offset?: number, length?: number): void {
    if (this._isReadyToPlay && audioEngine.audioContext) {
      try {
        if (this._startOffset < 0) {
          time = -this._startOffset;
          this._startOffset = 0;
        }
        const startTime = audioEngine.audioContext.currentTime + (time ?? 0);

        const tryToPlay = () => {
          if (audioEngine.audioContext) {
            offset = offset ?? this._offset;
            length = length ?? this._length;

            if (this._soundSource) {
              const oldSource = this._soundSource;
              oldSource.onended = () => {
                oldSource.disconnect();
              };
            }
            this._soundSource = audioEngine.audioContext.createBufferSource();
            if (this._soundSource && this._audioBuffer) {
              this._soundSource.buffer = this._audioBuffer;
              this._soundSource.connect(audioEngine.audioContext.destination);
              if (offset !== undefined) {
                this._soundSource.loopStart = offset;
              }
              if (length !== undefined) {
                this._soundSource.loopEnd = (offset! | 0) + length;
              }
              this._soundSource.onended = () => {
                this.isPlaying = false;
                this._startOffset = 0;
                this._onEndSubscriber?.complete();
              };
              const actualOffset = this.isPaused
                ? this._startOffset % this._soundSource.buffer.duration
                : offset ?? 0;
              this._soundSource.start(startTime, actualOffset, this._loop ? undefined : length);
            }
          }
        };

        if (audioEngine.audioContext.state === 'suspended') {
          setTimeout(() => {
            if (audioEngine.audioContext!.state === 'suspended') {
              audioEngine.lock();
              if (this.autoplay) {
                const subscription = audioEngine.onAudioStateChangedObservable.subscribe(state => {
                  if (state === 'running') {
                    tryToPlay();
                    subscription.unsubscribe();
                  }
                });
                // TODO: @Jerry should unlock by a user gesture on the page
                audioEngine.unlock();
              }
            } else {
              tryToPlay();
            }
          }, 500);
        } else {
          tryToPlay();
        }

        this._startTime = startTime;
        this.isPlaying = true;
        this.isPaused = false;
      } catch (error) {
        console.error(
          'Error while trying to play audio: ' + this.name + ', ' + (error as Error).message
        );
      }
    }
  }

  public pause(): void {
    if (this.isPlaying) {
      this.isPaused = true;
      if (audioEngine.audioContext) {
        this.stop(0);
        this._startOffset += audioEngine.audioContext.currentTime - this._startTime;
      }
    }
  }

  public stop(time?: number): void {
    if (this.isPlaying) {
      if (audioEngine.audioContext && this._soundSource) {
        const stopTime = time ? audioEngine.audioContext.currentTime + time : undefined;
        this._soundSource.stop(stopTime);
        if (stopTime === undefined) {
          this.isPlaying = false;
          this._soundSource.onended = () => void 0;
        } else {
          this._soundSource.onended = () => {
            this.isPlaying = false;
          };
        }
        if (!this.isPaused) {
          this._startOffset = 0;
        }
      }
    }
  }

  public dispose(): void {
    if (audioEngine.canUseWebAudio) {
      if (this.isPlaying) {
        this.stop();
      }
      this._isReadyToPlay = false;
      if (this._soundSource) {
        this._soundSource.disconnect();
        this._soundSource = undefined;
      }
      this._audioBuffer = undefined;
      if (this._onEndSubscription) {
        this._onEndSubscription.unsubscribe();
      }
    }
  }

  public isReady(): boolean {
    return this._isReadyToPlay;
  }
}
