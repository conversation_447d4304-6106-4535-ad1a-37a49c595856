import React from 'react';

import { AlarmsTtsManager } from './alarms-tts-manager';

export const BasicAlarmsTtsManager = () => {
  React.useEffect(() => {
    const alarmsTtsManager = new AlarmsTtsManager(subscriber => {
      subscriber.next({
        id: 0,
        level: '1',
        message: 'Hello world!',
      });
    });
    // alarmsTtsManager.playAsync();

    return () => {
      alarmsTtsManager.dispose();
    };
  }, []);

  return <div>Hello world!</div>;
};
