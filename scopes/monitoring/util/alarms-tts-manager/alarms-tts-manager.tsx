import { Observable, Observer, Subscriber, Subscription, TeardownLogic } from 'rxjs';

import type { AlarmJSON } from '@manyun/monitoring.model.alarm';
import { ttsAliyun } from '@manyun/monitoring.service.tts-aliyun';

import { Sound } from './audio/sound';

export type AlarmMain = Pick<AlarmJSON, 'id' | 'level' | 'message'>;

type Activity = {
  id: number;
  abortController: AbortController;
};

export class AlarmsTtsManager {
  private _queue: AlarmMain[] = [];
  /**
   * Current playing `Alarm`
   */
  private _current?: AlarmMain;
  private _sound?: Sound;
  private _alarmSubscription?: Subscription;
  /**
   * Current on-going activities
   */
  private _activities: Activity[] = [];

  public alarmObservable: Observable<AlarmMain>;
  public alarmObserver: Observer<AlarmMain>;
  public get isPlaying() {
    if (this._current) {
      return true;
    }
    return this._sound ? this._sound.isPlaying : false;
  }
  public get current(): AlarmMain | undefined {
    return this._current;
  }

  constructor(
    subscribe?: (this: Observable<AlarmMain>, subscriber: Subscriber<AlarmMain>) => TeardownLogic
  ) {
    this.alarmObservable = new Observable(subscribe);
    this.alarmObserver = {
      next: alarm => {
        this._push(alarm);
      },
      error: () => {},
      complete: () => {},
    };
    this._alarmSubscription = this.alarmObservable.subscribe(this.alarmObserver);
  }

  private _push(alarm: AlarmMain): void {
    const existing = this._current ?? (this._queue[0] as AlarmMain | undefined);
    if (existing && alarm.level <= existing.level) {
      // If an `alarm's level` less than another,
      // that means it has a higher priority.
      this._queue = [alarm, ...this._queue];
      if (this.isPlaying) {
        this.stop();
      }
    } else {
      this._queue.push(alarm);
    }
  }

  public async playAsync(alarm?: AlarmMain): Promise<void> {
    let _alarm: AlarmMain | undefined;
    if (alarm) {
      if (this.isPlaying) {
        this.stop();
      }
      this._current = alarm;
      _alarm = alarm;
    } else {
      _alarm = this._shift();
    }
    if (!_alarm) {
      return;
    }
    this._clearActivities();
    const arrayBuffer = await this._produceAudioArrayBuffer(_alarm);
    if (!arrayBuffer || (this._current && this._current.id !== _alarm.id)) {
      return;
    }
    if (this._sound) {
      this._sound.dispose();
    }
    this._sound = new Sound(
      `alarms-tts--${_alarm.id}`,
      arrayBuffer,
      {
        complete: () => {
          if (this._current && this._current.id === _alarm!.id) {
            this.stop();
          }
        },
      },
      {
        autoPlay: true,
      }
    );
  }

  private async _produceAudioArrayBuffer(alarm: AlarmMain): Promise<ArrayBuffer | null> {
    const abortController = new AbortController();
    this._activities.push({
      id: alarm.id,
      abortController,
    });
    const { error, data } = await ttsAliyun(
      {
        text: alarm.message,
      },
      abortController.signal
    );
    if (error) {
      console.error(error);
    }
    return data;
  }

  private _shift(): AlarmMain | undefined {
    const alarm = this._queue.shift();
    this._current = alarm;

    return alarm;
  }

  private _clearActivities() {
    if (this._activities.length > 0) {
      this._activities.forEach(({ abortController }) => {
        abortController.abort();
      });
      this._activities = [];
    }
  }

  public clear(): void {
    this._queue = [];
    this._clearActivities();
  }

  public pause(): void {
    if (this._sound) {
      this._sound.pause();
    }
  }

  public resume(): void {
    if (this._sound?.isPaused) {
      this._sound.play();
    }
  }

  public stop(): void {
    if (this._current) {
      this._current = undefined;
    }
    if (this._sound) {
      this._sound.dispose();
      this._sound = undefined;
    }
    this._clearActivities();
  }

  public dispose(): void {
    this.clear();
    this.stop();
    if (this._alarmSubscription) {
      this._alarmSubscription.unsubscribe();
    }
  }
}

export let alarmsTtsManager: AlarmsTtsManager | undefined;
export function createAlarmsTtsManager(
  subscribe: (this: Observable<AlarmMain>, subscriber: Subscriber<AlarmMain>) => TeardownLogic
) {
  alarmsTtsManager = new AlarmsTtsManager(subscribe);

  return alarmsTtsManager;
}
