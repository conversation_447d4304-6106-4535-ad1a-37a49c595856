---
description: An AlarmsTtsManager component.
---

告警语音播报

### Component usage

```js
import { createAlarmsTtsManager } from '@manyun/monitoring.util.alarms-tts-manager';
import type { Subscriber } from 'rxjs';
import type { AlarmMain } from '@manyun/monitoring.util.alarms-tts-manager';

let subscriberRef: Subscriber<AlarmMain>;
const alarmsTtsManager = createAlarmsTtsManager(subscriber => {
  subscriberRef = subscriber;
});
// Push an alarm
subscriberRef.next({
  id: 0,
  level: '1',
  message: 'Hello world!',
});
// Start playing
if (!alarmsTtsManager.isPlaying) {
  alarmsTtsManager.playAsync();
}
// Dispose
alarmsTtsManager.dispose();
```
