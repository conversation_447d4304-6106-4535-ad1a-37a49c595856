import React from 'react';

import {
  Blizzard,
  Cloudy,
  Fog,
  <PERSON>l,
  Haze,
  HeavyRain,
  HeavySnow,
  LightRain,
  ModerateRain,
  ModerateSnow,
  PartlyCloudy,
  Rainstorm,
  Sandstorm,
  <PERSON>er,
  <PERSON>leet,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
} from '@manyun/base-ui.icons';

import cloudy from './assets/cloudy-bg.png';
import rain from './assets/rain-bg.png';
import sunny from './assets/sunny-bg.png';

export function getWeatherIcon(conditionId: number, style?: React.CSSProperties) {
  switch (conditionId) {
    case 0:
    case 30:
      return <Sunny style={style} />;
    case 1:
    case 31:
      return <PartlyCloudy style={style} />;
    case 2:
      return <Cloudy style={style} />;
    case 3:
      return <Shower style={style} />;
    case 13:
    case 34:
      return <SnowShower style={style} />;
    case 18:
    case 32:
      return <Fog style={style} />;
    case 20:
    case 36:
    case 29:
    case 35:
      return <Sandstorm style={style} />;
    case 45:
    case 46:
      return <Haze style={style} />;
    case 4:
      return <Thunder style={style} />;
    case 5:
      return <Hail style={style} />;
    case 6:
    case 19:
      return <Sleet style={style} />;
    case 7:
      return <LightRain style={style} />;
    case 8:
      return <ModerateRain style={style} />;
    case 9:
      return <HeavyRain style={style} />;
    case 10:
      return <Rainstorm style={style} />;
    case 14:
      return <Snow style={style} />;
    case 15:
      return <ModerateSnow style={style} />;
    case 16:
      return <HeavySnow style={style} />;
    case 17:
      return <Blizzard style={style} />;
    default:
      return;
  }
}

export function getWeatherBackground(conditionId: number) {
  switch (conditionId) {
    case 0:
    case 30:
      return sunny;
    case 1:
    case 31:
    case 3:
    case 13:
    case 34:
    case 18:
    case 32:
      return cloudy;

    default:
      return rain;
  }
}
