import get from 'lodash.get';

import type { ConfigUtil } from '@manyun/dc-brain.util.config';
import { formatPointValueText, generatePointValueMapping } from '@manyun/monitoring.model.point';
import type { PointDataState } from '@manyun/monitoring.model.point';

const BLANK_PLACEHOLDER = '--';

export type Target = {
  deviceGuid: string;
  deviceType: string;
};

export type DiPointValue = {
  NAME: string;
  STATUS_CODE?: string;
  ON_OFF_STATE?: 'on' | 'off' | undefined;
  /**
   * ERROR: CANT REFLECTED BY PROVIDED `valueMappings`
   *
   * @example
   *
   * ```ts
   *   const valueMappings = { "0": "OFF", "1": "ON" };
   *   const value = "2";
   *   const result = valueMappings[value];
   *   // undefined
   *   // Out Of Range Error
   * ```
   */
  ERROR_OUT_OF_RANGE: boolean;
};

export type PointData = {
  deviceGuid: string;
  deviceType: string;
  /**
   * 测点名称
   * */
  name: string | null;
  pointCode: string | null;
  unit: string | null;
  /**
   * 测点原始实时数据
   *
   * - AI 测点 `originalValue` 永远和 `value` 一致
   * - DI 测点当 `reflected: true` 时，`originalValue` 与 `value` 不一致
   */
  originalValue: number;
  value: string | number | DiPointValue;
  /**
   * AI 测点实时数据格式化之后的文本
   */
  formattedText: string;
  /** @deprecated Use `isBlank` instead */
  disabled: boolean;
  /**
   * 表示未获取到实时数据
   */
  isBlank: boolean;
  status: PointDataState;
  /**
   * DI 测点文案翻译数据源
   */
  valueMapping?: ReturnType<typeof generatePointValueMapping>;
};

export interface BaseOptions {
  defaults?: Partial<PointData>;
}

export interface PredefinedPointOptions extends BaseOptions {
  pointType: string;
}

export interface HardcodedPointOptions extends BaseOptions {
  /**
   * Hard-coded `pointCode`.(If you exactly know what you are doing, you can specify a `pointCode` instead of a `pointType`.)
   */
  hardCodedPointCode: string | null;
}

export type AIPointOptions = (PredefinedPointOptions | HardcodedPointOptions) & {
  /**
   * 用一个数字来表示取值精度（最多使用 20 位小数）
   *
   * @example
   *
   * ```ts
   * precision: 1
   * // { originalValue: 1.11, value: 1, formattedText: '1%', unit: '%' }
   *
   * precision: 0.1
   * // { originalValue: 1.11, value: 1.1, formattedText: '1.1%', unit: '%' }
   * ```
   */
  precision?: number;
  /**
   * Format value text with `Point Unit`.
   *
   * @defaultValue `false`
   */
  formatted?: boolean;
};

export type DIPointOptions = (PredefinedPointOptions | HardcodedPointOptions) & {
  /**
   * **`DI Point` only**
   *
   * Returns `DI Point` value text.
   *
   * @defaultValue `false`
   */
  reflected?: boolean;
  /**
   * **`DI Point` only**
   *
   * `DI Point "validLimits"`
   */
  validLimits?: string[];
  /**
   * **`DI Point` only**
   *
   * Returns with `DI Point` value mappings.
   */
  valueMappingIncluded?: boolean;
};

export type Options = AIPointOptions | DIPointOptions;

/**
 * Given a `deviceType` and a `pointType`, return `pointCode` and `pointAttrs`.
 *
 * @param deviceType Original or customized `deviceType`.
 * @param pointType Customized `pointType` in the front-end.
 * @param hardCodedPointCode Hard-coded `pointCode`.
 * @param configUtil
 * @returns `pointCode` and `pointAttrs`
 */
function getPointByType(
  deviceType: string,
  pointType: string,
  hardCodedPointCode: string | null = null,
  configUtil: ConfigUtil
) {
  if (hardCodedPointCode) {
    return {
      pointCode: hardCodedPointCode,
      pointAttrs: configUtil.getPointDefinition(deviceType, hardCodedPointCode),
    };
  }

  if (pointType) {
    const pointCode = configUtil.getPointCode(deviceType, pointType);
    const pointAttrs = configUtil.getPointDefinition(deviceType, pointCode);

    return {
      pointCode,
      pointAttrs,
    };
  }

  return {
    pointCode: null,
    pointAttrs: null,
  };
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function generateGetDeviceAlarmStatus(alarmData: any) {
  return (
    deviceGuid: string,
    { defaultStatus = 'default' }: { defaultStatus?: PointDataState } = {}
  ): { status: PointDataState; isAlerting: boolean } => {
    let _status: PointDataState = defaultStatus;

    const deviceAlarms = alarmData?.[deviceGuid];
    if (deviceAlarms?.count.ERROR > 0) {
      _status = 'alarm';
    } else if (deviceAlarms?.count.WARN > 0) {
      _status = 'warning';
    }

    return {
      status: _status,
      isAlerting: _status === 'alarm' || _status === 'warning',
    };
  };
}

// TODO: @Jerry move these types into `@manyun/monitoring.state.subscriptions`
type TargetRealtimeData = {
  lastUpdatedAt: number;
  status: PointDataState | null;
  pointValueMap: Record<
    string,
    {
      value: number;
      status: PointDataState;
    }
  >;
};
type RealtimeData = Record<string, TargetRealtimeData>;

/**
 * Generate a function to get `point` monitoring data and alarm status.(Currying)
 */
export function generateGetPointMonitoringData({
  realtimeData,
  alarmsData,
  configUtil,
}: {
  realtimeData: RealtimeData | null;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  alarmsData: any;
  configUtil: ConfigUtil;
}) {
  return ({ deviceGuid, deviceType }: Target, options: Options) => {
    const defaultValue: PointData = {
      deviceGuid,
      deviceType,
      name: null,
      pointCode: null,
      originalValue: Number.NaN,
      value: BLANK_PLACEHOLDER,
      formattedText: BLANK_PLACEHOLDER,
      unit: null,
      disabled: true,
      isBlank: true,
      status: 'default',
      ...options.defaults,
    };

    if ((options as DIPointOptions).reflected) {
      defaultValue.value = {
        NAME: BLANK_PLACEHOLDER,
        STATUS_CODE: 'UNKNOWN',
        ON_OFF_STATE: undefined,
        ERROR_OUT_OF_RANGE: false,
      } as DiPointValue;
    }

    const { pointCode, pointAttrs } = getPointByType(
      deviceType,
      (options as PredefinedPointOptions).pointType,
      (options as HardcodedPointOptions).hardCodedPointCode,
      configUtil
    );

    if (pointCode === null) {
      console.error(
        'Point code not found(deviceType=%s, predefinedPointCode=%s)',
        deviceType,
        (options as PredefinedPointOptions).pointType
      );
      return defaultValue;
    }

    defaultValue.pointCode = pointCode;

    let valueMapping = generatePointValueMapping((options as DIPointOptions).validLimits || []);
    if (pointAttrs) {
      const { name, unit, validLimits, statusCodes, onOffStateMappings } = pointAttrs;
      defaultValue.name = name;
      defaultValue.unit = unit;
      if (validLimits) {
        valueMapping = generatePointValueMapping(validLimits, statusCodes, onOffStateMappings);
        if ((options as DIPointOptions).valueMappingIncluded) {
          defaultValue.valueMapping = valueMapping;
        }
      }
    }
    const deviceData = get(realtimeData, deviceGuid);

    if (deviceData === undefined) {
      return defaultValue;
    }

    const pointData = get(deviceData.pointValueMap, pointCode);

    // 注意：测点值一般是数字，特殊情况下可能是 undefined / null，不能是 ""
    if (
      pointData === undefined ||
      pointData.value === undefined ||
      pointData.value === null ||
      Number.isNaN(pointData.value)
    ) {
      if ((options as AIPointOptions).formatted) {
        return {
          ...defaultValue,
          formattedText: formatPointValueText(defaultValue),
        };
      }

      return defaultValue;
    }

    const trimmedValue =
      typeof (options as AIPointOptions).precision == 'number'
        ? Number(
            pointData.value.toFixed(getNumberPrecision((options as AIPointOptions).precision!))
          )
        : pointData.value;
    const basePointData: PointData = {
      ...defaultValue,
      disabled: false,
      isBlank: false,
      ...pointData,
      originalValue: pointData.value,
      value: trimmedValue,
      // if point value exists, set default `formattedText` to point value
      // to keep consistency.
      formattedText: trimmedValue.toString(),
    };

    if ((options as AIPointOptions).formatted) {
      return {
        ...basePointData,
        formattedText: formatPointValueText(basePointData),
      };
    }

    if (!(options as DIPointOptions).reflected) {
      return basePointData;
    }

    if (!valueMapping) {
      console.error(
        `No 'VALUE_MAPPING' during reflecting point(${pointCode}) data value of device model(${deviceType}).`
      );
      return basePointData;
    }

    let value = valueMapping[pointData.value];
    if (value === undefined) {
      console.warn(
        `No 'VALUE_MAPPING' of point value(${pointData.value}) of device(${deviceGuid}).`
      );
      (value as DiPointValue) = {
        NAME: 'UNKNOWN',
        STATUS_CODE: 'UNKNOWN',
        ON_OFF_STATE: undefined,
        ERROR_OUT_OF_RANGE: true,
      };
    } else {
      (value as DiPointValue).ERROR_OUT_OF_RANGE = false;
    }

    return {
      ...basePointData,
      value: value as DiPointValue,
    };
  };
}

function validateNumber(num: string | number) {
  if (typeof num === 'number') {
    return !Number.isNaN(num);
  }

  // Empty
  if (!num) {
    return false;
  }

  return (
    // 11.28
    /^\s*-?\d+(\.\d+)?\s*$/.test(num) ||
    // 1.
    /^\s*-?\d+\.\s*$/.test(num) ||
    // .1
    /^\s*-?\.\d+\s*$/.test(num)
  );
}

function getNumberPrecision(number: number) {
  const numStr: string = String(number);

  if (numStr.includes('.') && validateNumber(numStr)) {
    return numStr.length - numStr.indexOf('.') - 1;
  }

  return 0;
}

const NO_DATA_POINT_VALUE = Number.NaN;

/**
 * 从实时数据中获取某个设备下某个点位的测点值的方法生成器
 *
 * @param realtimeData
 * @param alarmsData
 * @returns
 */
export function generateGetPointValue(
  realtimeData: RealtimeData | null | undefined,
  alarmsData: unknown
) {
  /**
   * 获取某个设备下某个点位的测点值
   *
   * > `undefined, null` 会被处理成 `Number.NaN`
   *
   * @param deviceGuid
   * @returns
   */
  const getPointValue = (deviceGuid: string) => {
    const deviceRealtimeData = get(realtimeData, [deviceGuid, 'pointValueMap']);
    const devicePointsAlarmsCount = get(alarmsData, [deviceGuid, 'pointsCount']);

    return (pointCode: string) => {
      let value = get(deviceRealtimeData, [pointCode, 'value'], NO_DATA_POINT_VALUE);
      if (value === undefined || value === null) {
        value = NO_DATA_POINT_VALUE;
      }

      const alarmsCount = get(devicePointsAlarmsCount, pointCode);
      const isAlerting = alarmsCount && (alarmsCount.ERROR > 0 || alarmsCount.WARN > 0);

      return { value, isAlerting };
    };
  };

  return getPointValue;
}
