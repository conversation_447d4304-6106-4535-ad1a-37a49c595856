---
description: 'A util function to get point monitoring data and alarm status'
---

```tsx
import { useSelector } from 'react-redux';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { getMonitoringData } from '@manyun/monitoring.state.subscriptions';

const { devicesRealtimeData, devicesAlarmsData } = useSelector(getMonitoringData);
const config = useSelector(getCurrentTenantConfig);
const configUtil = ConfigUtil(config);
const getPointMonitoringData = generateGetPointMonitoringData({
  realtimeData: devicesRealtimeData,
  alarmsData: devicesAlarmsData,
  configUtil
});

const rpp = { deviceGuid: 'fake-device-guid', deviceType: 'fake-device-type' };

// --- 1. You don't have the runtime `point code`, then use `point type` ---

// AI Point
const rppLoadRate = getPointMonitoringData(rpp, {
  pointType: ConfigUtil.constants.pointCodes.RPP_LOAD_RATE,
  formatted: true,
});

// DI Point
const rppOnOffState = getPointMonitoringData(rpp, {
  pointType: ConfigUtil.constants.pointCodes.RPP_INPUT_ON_OFF_STATE,
  reflected: true,
});

// --- 2. You have the runtime `point code`, then use `hardCodedPointCode` ---

const pointData = getPointMonitoringData(target, {
  hardCodedPointCode: '1001000',
});

// --- Rendering ---

<PointDataRenderer data={rppLoadRate} />
<PointDataRenderer data={rppOnOffState} />
```
