export type GetElectricityUnitProps = {
  value: number;
};
/**
 * 0-1000kwh显示单位kwh，
 * 1000-1000000显示单位mwh，
 * 1000000-1000000000显示单位gwh
 * @param value
 */
export function getElectricityUnit({ value }: GetElectricityUnitProps) {
  if (value >= 0 && value < 1000) {
    return {
      value: Number(value.toFixed(2)),
      unit: '/Kwh',
    };
  } else if (value >= 1000 && value < 1000000) {
    return {
      value: Number((value / 1000).toFixed(2)),
      unit: '/Mwh',
    };
  } else if (value >= 1000000 && value < 1000000000) {
    return {
      value: Number((value / 1000 / 1000).toFixed(2)),
      unit: '/Gwh',
    };
  }
  return {
    value: '--',
    unit: '',
  };
}
