import merge from 'lodash.merge';
import type { PartialDeep } from 'type-fest';

const CACHE_KEY = 'manyun.monitoring/preferences' as const;

export type Preferences = {
  ttsService: {
    enabled: boolean;
  };
};

const defaultPreferences: Preferences = {
  ttsService: {
    enabled: true,
  },
};

export function setPreferences(partialPreferences: PartialDeep<Preferences>) {
  window.localStorage.setItem(
    CACHE_KEY,
    JSON.stringify(merge(getPreferences(), partialPreferences))
  );
}

export function getPreferences(): Preferences {
  const cache = window.localStorage.getItem(CACHE_KEY);

  return cache !== null ? JSON.parse(cache) : defaultPreferences;
}
