import { ruleTypesSlice } from './rule-types.slice';
import type { RuleTypesSliceState } from './rule-types.slice';

export const selectRuleTypes = (storeState: { [ruleTypesSlice.name]: RuleTypesSliceState }) => {
  return storeState[ruleTypesSlice.name];
};

export const selectRuleTypesEntities =
  (codes?: string[]) => (storeState: { [ruleTypesSlice.name]: RuleTypesSliceState }) => {
    const { entities } = storeState[ruleTypesSlice.name];

    if (codes === undefined) {
      return Object.keys(entities).map(code => entities[code]);
    }

    return codes.map(code => entities[code]);
  };
