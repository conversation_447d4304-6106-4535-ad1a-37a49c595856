import ruleTypesWatchers from './rule-types.saga';
import { ruleTypesSlice } from './rule-types.slice';

export type { RuleTypesSliceState, RuleType } from './rule-types.slice';
export * from './rule-types.action';
export * from './rule-types.selector';
export { ruleTypesWatchers };
export default {
  [ruleTypesSlice.name]: ruleTypesSlice.reducer,
};

// dev use only
// ------
export { createCompositionWrapper } from './rule-types.composition-wrapper.creator';
