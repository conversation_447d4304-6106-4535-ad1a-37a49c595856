import { createAction } from '@reduxjs/toolkit';

import { RequestError } from '@manyun/service.request';

import { ruleTypesSlice } from './rule-types.slice';

const prefix = ruleTypesSlice.name;

export const ruleTypesSliceActions = ruleTypesSlice.actions;

export const getRuleTypesAction = createAction<
  | {
      callback?: (error: RequestError | null) => void;
    }
  | undefined
>(prefix + '/GET_RULE_TYPES');
