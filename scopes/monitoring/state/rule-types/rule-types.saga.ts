import cloneDeep from 'lodash.clonedeep';
import { call, fork, put, take } from 'redux-saga/effects';
import type { SagaReturnType } from 'redux-saga/effects';
import shallowequal from 'shallowequal';

import { fetchTreeModeMetaData } from '@manyun/resource-hub.service.fetch-tree-mode-meta-data';

import { getRuleTypesAction, ruleTypesSliceActions } from './rule-types.action';

/** Workers */

export function* getRuleTypesSaga({ payload }: ReturnType<typeof getRuleTypesAction>) {
  yield put(ruleTypesSliceActions.fetchRuleTypesStart());

  const { error, data }: SagaReturnType<typeof fetchTreeModeMetaData> = yield call(
    fetchTreeModeMetaData,
    {
      topCategory: 'EXPERT_BIZ_TYPE',
      secondCategory: 'EXPERT_RULE_TYPE',
    }
  );

  payload?.callback?.(error ?? null);

  if (error) {
    yield put(ruleTypesSliceActions.fetchRuleTypesError());
    return;
  }

  yield put(ruleTypesSliceActions.setRuleTypes(data));
}

/** Watchers */

function* watchGetRuleTypes() {
  let called = false;
  let lastActionPayload = null;

  while (true) {
    const action: SagaReturnType<typeof getRuleTypesAction> = yield take(getRuleTypesAction.type);
    if (called === false || shallowequal(lastActionPayload, action.payload)) {
      // 请求优化
      called = true;
      lastActionPayload = cloneDeep(action.payload);
      yield getRuleTypesSaga(action);
      called = false;
      lastActionPayload = null;
    }
  }
}

export default [fork(watchGetRuleTypes)];
