import { createSlice } from '@reduxjs/toolkit';
import type { CaseReducer, PayloadAction } from '@reduxjs/toolkit';
import shallowequal from 'shallowequal';

import type { BackendData } from '@manyun/resource-hub.service.fetch-tree-mode-meta-data';

export { BackendData as RuleType };

export type RuleTypesSliceState = {
  /**
   * an `code: entity` object.
   */
  entities: Record<string, BackendData>;

  /**
   * an `codes` array that exist in `entities`.
   */
  codes: string[];

  /**
   * a count that stores total size of `RuleTypes`.
   */
  total: number;

  /**
   * an indicator can be used to indicate whether is fetching data from a remote API.
   */
  loading: boolean;
};

type RuleTypesSliceCaseReducers = {
  fetchRuleTypesStart: CaseReducer<RuleTypesSliceState, PayloadAction<undefined>>;
  setRuleTypes: CaseReducer<
    RuleTypesSliceState,
    PayloadAction<{
      data: BackendData[];
      total: number;
    }>
  >;
  fetchRuleTypesError: CaseReducer<RuleTypesSliceState, PayloadAction<undefined>>;
};

export const ruleTypesSlice = createSlice<
  RuleTypesSliceState,
  RuleTypesSliceCaseReducers,
  'monitoring.rule-types'
>({
  name: 'monitoring.rule-types',
  initialState: {
    entities: {},
    codes: [],
    total: 0,
    loading: false,
  },
  reducers: {
    fetchRuleTypesStart(sliceState) {
      sliceState.loading = true;
    },
    setRuleTypes(sliceState, { payload: { data, total } }) {
      const codes: string[] = [];
      data.forEach(ruleType => {
        codes.push(ruleType.metaCode);

        // cache update performance optimization
        const existingRuleTypes = sliceState.entities[ruleType.id];
        if (existingRuleTypes && shallowequal(existingRuleTypes, ruleType)) {
          return;
        }

        sliceState.entities[ruleType.metaCode] = ruleType;
      });
      sliceState.codes = codes;
      sliceState.loading = false;
      sliceState.total = total;
    },
    fetchRuleTypesError(sliceState) {
      sliceState.loading = false;
    },
  },
});
