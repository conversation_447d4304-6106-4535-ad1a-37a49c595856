import type {
  AlarmsViewingState,
  InitializingStage,
  LayoutVariant,
  ScopeVariant,
  StatisticsVariant,
} from './../alarms-monitoring-board.slice';

export type LocaleCode = 'zh-CN';

export type AlarmsMonitoringBoardLocales = {
  initializingStage: Record<Exclude<InitializingStage, null>, string>;
  layout: Record<LayoutVariant, string>;
  statistics: Record<StatisticsVariant, string>;
  boards: {
    yourInterests: string;
    expecteds: string;
    byStates: Record<AlarmsViewingState, string>;
    byScopes: Record<ScopeVariant | 'space', string>;
  };
};
