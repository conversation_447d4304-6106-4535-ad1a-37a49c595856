import type { AlarmsMonitoringBoardLocales } from './type';

export const zhCN: AlarmsMonitoringBoardLocales = {
  initializingStage: {
    's1/preparing': '准备中...',
    's2/fetching-preferences': '获取偏好配置中...',
    's3/subscribing-alarms': '订阅告警中...',
    's4/fetching-your-interests': '获取用户关注的告警中...',
  },
  layout: {
    'board--by-alarm-levels': '告警等级看板',
    'board--by-scopes': '专业类型看板',
    'board--by-states': '告警状态看板',
    table: '常规列表',
  },
  statistics: {
    byAlarmLevels: '未恢复告警 等级分布',
    byDeviceTypes: '未恢复告警 设备类型分布',
    byRoomTypes: '未恢复告警 包间类型分布',
    // byCreatedAt: '全部告警时间分布',
  },
  boards: {
    yourInterests: '关注',
    expecteds: '预期内',
    byStates: {
      accepted: '已受理',
      'accepted-n-recovered': '已受理已恢复',
      'accepted-n-unrecovered': '已受理未恢复',
      'connected-with-tickets': '已建单',
      unaccepted: '未受理',
      'unaccepted-n-recovered': '未受理已恢复',
      'unaccepted-n-unrecovered': '未受理未恢复',
    },
    byScopes: {
      'power-system': '电力',
      'hvac-system': '暖通',
      'elv-system': '弱电',
      space: '其他',
    },
  },
};

export default zhCN;
