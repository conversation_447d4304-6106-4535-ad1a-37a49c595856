import alarmsMonitoringBoardWatchers from './alarms-monitoring-board.saga';
import { alarmsMonitoringBoardSlice } from './alarms-monitoring-board.slice';

export { getAlarmsMonitoringBoardLocales } from './locales';
export type { AlarmsMonitoringBoardLocales } from './locales';
export * as timeSpanUtil from './utils/timespan-util';
export type {
  ColumnType,
  TableColumnSortOrder,
  ColumnConfig,
  AllColumnsConfig,
  CardLayoutColumnConfig,
  TableLayoutColumnConfig,
} from './utils/column-util';
export type {
  TimeSpan,
  AlarmId,
  AlarmsViewingState,
  ScopeVariant,
  LayoutVariant,
  StatisticsVariant,
  BoardVariant,
  SortVariant,
  SortOrder,
  SortValue,
  BoardLayout,
  BoardPreferences,
  YourInterests,
  StatisticVariantPreferences,
  ByStatesBoardPreferences,
  ByScopesBoardPreferences,
  ByDeviceTypesBoardPreferences,
  ByAlarmLevelsBoardPreferences,
  TablePreferences,
  Preferences,
  Statistics,
  ByStatesAlarmsGroup,
  ByDeviceTypesAlarmsGroup,
  ByScopesAlarmsGroup,
  BySpaceAlarmsGroup,
  ByAlarmLevelsGroup,
  InitializingStage,
  AlarmMessageDescriptor,
  AlarmsMonitoringBoardSliceState,
} from './alarms-monitoring-board.slice';
export * from './alarms-monitoring-board.action';
export * from './alarms-monitoring-board.selector';
export { alarmsMonitoringBoardWatchers };
export default {
  [alarmsMonitoringBoardSlice.name]: alarmsMonitoringBoardSlice.reducer,
};
