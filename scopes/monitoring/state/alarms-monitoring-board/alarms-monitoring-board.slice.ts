import { createSlice } from '@reduxjs/toolkit';
// import dayjs from 'dayjs';
import type { CaseReducer, PayloadAction } from '@reduxjs/toolkit';
import debug from 'debug';
import cloneDeep from 'lodash.clonedeep';
import get from 'lodash.get';
import merge from 'lodash.merge';
import mergeWith from 'lodash.mergewith';
import orderBy from 'lodash.orderby';
import remove from 'lodash.remove';
import set from 'lodash.set';
import setWith from 'lodash.setwith';
import uniq from 'lodash.uniq';

import { Alarm } from '@manyun/monitoring.model.alarm';
import type {
  AlarmJSON,
  AlarmSyntheticState,
  BackendAlarm,
  BackendAlarmLifecycleState,
  BackendAlarmState,
  BackendAlarmType,
} from '@manyun/monitoring.model.alarm';
import type { YourInterestsAlarm } from '@manyun/monitoring.service.fetch-your-interests-alarm-ids';
import type {
  AlarmUpdatesAction,
  AlarmsMonitoringBoardData,
} from '@manyun/monitoring.state.subscriptions';
import type { MetadataJSON } from '@manyun/resource-hub.model.metadata';

import {
  byAlarmLevelsBoardCardLayoutColumnsConfigs,
  byScopesBoardCardLayoutColumnsConfigs,
  byStatesAcceptedAndRecoveredBoardCardLayoutColumnsConfigs,
  byStatesAcceptedAndUnrecoveredBoardCardLayoutColumnsConfigs,
  byStatesUnAcceptedAndRecoveredBoardCardLayoutColumnsConfigs,
  byStatesUnacceptedAndUnrecoveredBoardCardLayoutColumnsConfigs,
  defaultBoardCardLayoutColumns,
  defaultBoardCardLayoutColumnsConfigs,
  defaultBoardPreferences,
  defaultBoardTableLayoutColumnsConfigs,
  yourInterestsBoardCardLayoutColumnsConfigs,
} from './utils/column-util';
import type { CardLayoutColumnConfig, TableLayoutColumnConfig } from './utils/column-util';

const logger = debug('manyun.monitoring/state/alarms-monitoring-board:slice');

const ALARMS_CACHE_COUNT_MAX = 5000;
const PENDING_UPDATES_COUNT_MAX = 100;

/*判断是否为预期内告警*/
function isExpectedFn(alarm: AlarmJSON) {
  return alarm.expectedStatus === 'EXPECTED' || alarm.mergeStatus === 'CHILD';
}

type AlarmLevel = string;
type DeviceType = string;
/**
 * TODO: @Jerry import this type from `Room` model
 */
type RoomType = string;
/**
 * A time span represents a period of time(from a timestamp to a timestamp)
 *
 * @example
 *
 * ```ts
 * const from = Date.now() - 60 * 1000;
 * const to = Date.now();
 * const timeSpan = [from, to];
 * ```
 */
export type TimeSpan = [number, number];
export type AlarmId = number;
export type AlarmsViewingState =
  | 'expecteds'
  | 'unaccepted'
  | 'accepted'
  | /* 已建单*/ 'connected-with-tickets'
  | AlarmSyntheticState;

/**
 * Codes configured in the pakcage `@manyun/dc-brain.config.base` for below items:
 *
 * - Power System
 * - HVAC System
 * - ELV System(弱电)
 * - [WIP]Others(其他：不属于以上 3 种)
 */
export type ScopeVariant = 'expecteds' | 'power-system' | 'hvac-system' | 'elv-system';

export type LayoutVariant =
  | 'expecteds'
  | 'board--by-states'
  | /* 专业类型看板 */ 'board--by-scopes'
  | 'board--by-alarm-levels'
  | 'table';
export type StatisticsVariant = 'byAlarmLevels' | 'byDeviceTypes' | 'byRoomTypes';
// Disabled. See http://chandao.manyun-local.com/zentao/bug-view-6070.html
// | 'byCreatedAt';
export type BoardVariant = 'byDeviceTypes' | 'byAlarmLevels' | 'byStates' | 'byScopes';
export type SortVariant =
  | /* 综合排序 */ 'combined-ranking'
  | 'alarm-level'
  | 'alarm-created-at'
  | 'alarm-recovered-at'
  | /* 关注时间 */ 'followed-at';
export type SortOrder = 'ASC' | 'DESC' | null;
export type SortValue =
  | 'combined-ranking'
  | 'alarm-level--ASC'
  | 'alarm-level--DESC'
  | 'alarm-created-at--ASC'
  | 'alarm-created-at--DESC'
  | 'latest-triggered-at--ASC'
  | 'latest-triggered-at--DESC'
  | 'alarm-recovered-at--ASC'
  | 'alarm-recovered-at--DESC'
  | 'followed-at--ASC'
  | 'followed-at--DESC';

export type BoardLayout = 'mini' | 'card' | 'table';

export type BoardPreferences = {
  /**
   * 当前看板的布局（可以通过偏好配置中的“设置默认展开列”来更新这个字段）
   */
  layout: BoardLayout;
  sort: SortValue;
  layoutColumns: {
    card: CardLayoutColumnConfig[];
    table: TableLayoutColumnConfig[];
  };
};

export type YourInterests = 'your-interests';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type BoardPreferencesMapper<T extends keyof any> = Record<T | YourInterests, BoardPreferences> & {
  boardColumns: T[];
};

export type StatisticVariantPreferences = {
  visible: true;
};

export type ByStatesBoardPreferences = BoardPreferencesMapper<AlarmsViewingState>;
export type ByScopesBoardPreferences = BoardPreferencesMapper<ScopeVariant | 'space'>;
export type ByDeviceTypesBoardPreferences = BoardPreferencesMapper<DeviceType>;
export type ByAlarmLevelsBoardPreferences = BoardPreferencesMapper<AlarmLevel>;

export type TablePreferences = {
  layoutColumns: {
    table: TableLayoutColumnConfig[];
  };
  sort: SortValue;
};

export type Preferences = {
  layout: LayoutVariant;
  /**
   * 语音播报服务
   */
  ttsService: boolean;
  autoRefresh: boolean;
  statistics: {
    visible: boolean;
    variants: Record<StatisticsVariant, StatisticVariantPreferences>;
  };
  boards: {
    byStates: ByStatesBoardPreferences;
    /**
     * 专业类型看板
     */
    byScopes: ByScopesBoardPreferences;
    byDeviceTypes: ByDeviceTypesBoardPreferences;
    byAlarmLevels: ByAlarmLevelsBoardPreferences;
  };
  table: TablePreferences;
};

export type Statistics = {
  byAlarmLevels: Record<AlarmLevel, number>;
  byDeviceTypes: Record<DeviceType, number>;
  byRoomTypes: Record<RoomType, number>;
  // byCreatedAt: [TimeSpan, number][];
};

export type ByStatesAlarmsGroup = Record<AlarmsViewingState, AlarmId[]>;
export type ByDeviceTypesAlarmsGroup = Record<DeviceType, AlarmId[]>;
export type ByScopesAlarmsGroup = Record<ScopeVariant, AlarmId[]>;
export type BySpaceAlarmsGroup = Record<'root', AlarmId[]>;
export type ByAlarmLevelsGroup = Record<AlarmLevel, AlarmId[]>;

export type InitializingStage =
  | /* `initializing === false` */ null
  | /* 准备中 */ 's1/preparing'
  | /* 获取偏好配置中 */ 's2/fetching-preferences'
  | /* 订阅告警中 */ 's3/subscribing-alarms'
  | /* 获取用户关注的告警中 */ 's4/fetching-your-interests';

export type SimpleChangedAlarm = Pick<AlarmsMonitoringBoardData, 'id'> &
  Partial<Omit<AlarmsMonitoringBoardData, 'type'>>;
export type AlarmMessageDescriptor = {
  action: AlarmUpdatesAction;
  alarm: SimpleChangedAlarm;
};
export type LeftAlarmMessageDescriptor = {
  action: Exclude<AlarmUpdatesAction, 'DEL'>;
  alarm: SimpleChangedAlarm;
};

export type AlarmsMonitoringBoardSliceState = {
  initializing: boolean;
  initializingStage: InitializingStage;
  /**
   * @deprecated
   */
  resetting: boolean;
  preferences: Preferences;
  /**
   * Alarms counts group by some conditons, auto-generated on every updates
   */
  statistics: Statistics;
  /**
   * All alarms IDs
   */
  ids: AlarmId[];
  alarms: Record<AlarmId, AlarmJSON>;
  /**
   * 关闭自动刷新时，所有更新入此队列，当重新打开时再更新一波
   */
  pendingUpdates: AlarmMessageDescriptor[];
  /**
   * 用户关注的告警 ID 集合
   */
  yourInterests: AlarmId[];
  /**
   * 用户关注的告警的额外信息。比如：关注时间
   */
  yourInterestsAlarms: Record<AlarmId, YourInterestsAlarm>;
  alarmsGroups: {
    byStates: ByStatesAlarmsGroup;
    byDeviceTypes: ByDeviceTypesAlarmsGroup;
    byScopes: ByScopesAlarmsGroup;
    /**
     * @deprecated should move it into `byScopes.others`
     */
    bySpace: BySpaceAlarmsGroup;
    byAlarmLevels: ByAlarmLevelsGroup;
  };
};

type AlarmsMonitoringBoardSliceCaseReducers = {
  /**
   * @internal
   */
  initialize: CaseReducer<AlarmsMonitoringBoardSliceState, PayloadAction<undefined>>;
  /**
   * @internal
   */
  setAlarmLevels: CaseReducer<AlarmsMonitoringBoardSliceState, PayloadAction<MetadataJSON[]>>;
  /**
   * @internal
   */
  nextInitializingStage: CaseReducer<
    AlarmsMonitoringBoardSliceState,
    PayloadAction<{
      /**
       * - 是否经历获取用户的偏好配置阶段
       * - 是否经历获取用户关注的告警阶段
       *
       * @defaultValue `false`
       */
      hardRefresh: boolean;
    }>
  >;
  /**
   * @internal
   */
  initialized: CaseReducer<AlarmsMonitoringBoardSliceState, PayloadAction<undefined>>;
  /**
   * @internal
   */
  setYourInterests: CaseReducer<
    AlarmsMonitoringBoardSliceState,
    PayloadAction<YourInterestsAlarm[]>
  >;
  /**
   * @internal
   */
  addIntoYourInterests: CaseReducer<AlarmsMonitoringBoardSliceState, PayloadAction<AlarmId[]>>;
  /**
   * @internal
   */
  removeFromYourInterests: CaseReducer<AlarmsMonitoringBoardSliceState, PayloadAction<AlarmId[]>>;
  /**
   * @internal
   */
  reset: CaseReducer<AlarmsMonitoringBoardSliceState, PayloadAction<undefined>>;
  /**
   * @internal
   */
  replacePreferences: CaseReducer<AlarmsMonitoringBoardSliceState, PayloadAction<Preferences>>;
  /**
   * @internal
   */
  setPreferences: CaseReducer<
    AlarmsMonitoringBoardSliceState,
    PayloadAction<Partial<Pick<Preferences, 'layout' | 'ttsService' | 'autoRefresh'>>>
  >;
  /**
   * @warning Use this with caution: because the path could change in the future, thus it will broken.
   * @internal
   * @example
   *
   * ```ts
   * setStatisticsPreferencesByPath({ path: 'statistics.visible', value: true });
   * setStatisticsPreferencesByPath({ path: ['statistics', 'variants', 'byAlarmLevels', 'visible'], value: true });
   * ```
   */
  setPreferencesByPath: CaseReducer<
    AlarmsMonitoringBoardSliceState,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    PayloadAction<{
      path: string | string[];
      value: unknown;
      strategy?: 'replace' | 'mergeDeep';
    }>
  >;
  /**
   * @internal
   */
  bulkUpdate: CaseReducer<
    AlarmsMonitoringBoardSliceState,
    PayloadAction<{
      alarms: AlarmMessageDescriptor[];
      deviceTypes: {
        powerSystem: string;
        hvacSystem: string;
        elvSystem: string;
      };
      /**
       * Ignore `auto-refresh` switch
       *
       * @defaultValue `false`
       */
      forceUpdate?: boolean;
    }>
  >;
  /**
   * Remove alarms by alarm IDs
   *
   * @interal
   */
  removeAlarms: CaseReducer<
    AlarmsMonitoringBoardSliceState,
    PayloadAction<{
      alarmIds: AlarmId[];
      /**
       * 是否删除 `alarms` 中的实体数据
       *
       * @defaultValue `true`
       */
      destroy?: boolean;
    }>
  >;
};

export const alarmsMonitoringBoardSlice = createSlice<
  AlarmsMonitoringBoardSliceState,
  AlarmsMonitoringBoardSliceCaseReducers,
  'monitoring.alarms-monitoring-board'
>({
  name: 'monitoring.alarms-monitoring-board',
  initialState: createInitialState(),
  reducers: {
    initialize: state => {
      state.initializing = true;
      state.initializingStage = 's1/preparing';
    },
    setAlarmLevels: (state, { payload: alarmLevelsMetaDataJSON }) => {
      alarmLevelsMetaDataJSON.forEach(alarmLevelMetaDataJSON => {
        state.preferences.boards.byAlarmLevels.boardColumns.push(
          String(alarmLevelMetaDataJSON.code)
        );
        state.preferences.boards.byAlarmLevels[String(alarmLevelMetaDataJSON.code)] = {
          layout: 'card',
          sort: 'combined-ranking',
          layoutColumns: {
            card: byAlarmLevelsBoardCardLayoutColumnsConfigs,
            table: defaultBoardTableLayoutColumnsConfigs,
          },
        };
        state.alarmsGroups.byAlarmLevels[String(alarmLevelMetaDataJSON.code)] = [];
      });
    },
    nextInitializingStage: (state, { payload: { hardRefresh = false } }) => {
      switch (state.initializingStage) {
        case 's1/preparing':
          state.initializingStage = hardRefresh
            ? 's2/fetching-preferences'
            : 's3/subscribing-alarms';
          break;
        case 's2/fetching-preferences':
          state.initializingStage = 's3/subscribing-alarms';
          break;
        case 's3/subscribing-alarms':
          state.initializingStage = hardRefresh ? 's4/fetching-your-interests' : null;
          break;
        default:
          break;
      }
    },
    initialized: state => {
      state.initializing = false;
      state.initializingStage = null;
    },
    setYourInterests: (state, { payload: yourInterestsAlarms }) => {
      const alarmIds: AlarmId[] = [];
      yourInterestsAlarms.forEach(yourInterestsAlarm => {
        alarmIds.push(yourInterestsAlarm.id);
        state.yourInterestsAlarms[yourInterestsAlarm.id] = yourInterestsAlarm;
      });
      state.yourInterests = alarmIds;
    },
    addIntoYourInterests: (state, { payload: alarmIds }) => {
      state.yourInterests = uniq([...state.yourInterests, ...alarmIds]);
      const board = mapLayout2Board(state.preferences.layout);
      if (board !== null) {
        sortAlarmGroup(state, board, 'your-interests');
      }
    },
    removeFromYourInterests: (state, { payload: alarmIds }) => {
      const predicate = (id: AlarmId) => alarmIds.includes(id);
      remove(state.yourInterests, predicate);
    },
    reset: () => createInitialState(),
    replacePreferences: (state, { payload: preferences }) => {
      const copy = cloneDeep(preferences);
      // Note: we should use the `alarm levels` from `meta data` only
      copy.boards.byAlarmLevels.boardColumns = [];

      Object.values(copy.boards).forEach(boardValue => {
        Object.values(boardValue).forEach(boardPreference => {
          if (!Array.isArray(boardPreference)) {
            boardPreference.layoutColumns.card = boardPreference.layoutColumns.card
              .filter(item => defaultBoardCardLayoutColumns.includes(item.dataIndex))
              .map(item => {
                return {
                  ...item,
                  locked: false,
                };
              });
          }
        });
      });
      const preferencesCopy = cloneDeep(state.preferences);

      const customizer = (ours: unknown, theirs: unknown) => {
        if (Array.isArray(ours) && Array.isArray(theirs)) {
          return ours.map(item => {
            if (typeof item == 'object' && item !== null && 'dataIndex' in item) {
              const match = theirs.find(theirItem => theirItem.dataIndex === item.dataIndex);
              if (match) {
                return {
                  ...item,
                  ...match,
                };
              }
            }
            return item;
          });
        }
        return undefined;
      };
      state.preferences = mergeWith(preferencesCopy, copy, customizer);
    },
    setPreferences: (state, { payload: partialPreferences }) => {
      if (typeof partialPreferences.layout == 'string') {
        state.preferences.layout = partialPreferences.layout;
        // Re-sort "your interests alarms"
        const board = mapLayout2Board(state.preferences.layout);
        if (board !== null) {
          sortAlarmGroup(state, board, 'your-interests');
        }
      }
      if (typeof partialPreferences.ttsService == 'boolean') {
        state.preferences.ttsService = partialPreferences.ttsService;
      }
      if (typeof partialPreferences.autoRefresh == 'boolean') {
        state.preferences.autoRefresh = partialPreferences.autoRefresh;
      }
    },
    setPreferencesByPath: (state, { payload: { path, value, strategy = 'replace' } }) => {
      if (strategy === 'replace') {
        set(state.preferences, path, value);
      } else if (strategy === 'mergeDeep') {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        let others: any = {};
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        let _value: any = value;
        if (typeof path === 'string' && path.endsWith(']')) {
          const parentPath = path.substring(0, path.indexOf('['));
          others = setWith({}, parentPath, get(state.preferences, parentPath), Object);
          _value = merge(get(others, path), value);
        }
        const source: Partial<Preferences> = setWith(others, path, _value, Object);
        merge(state.preferences, source);
      }
      const paths = Array.isArray(path) ? path : path.split('.');
      const only2Paths = paths.length === 2;
      const hasSort = paths.includes('sort');
      if ((only2Paths || hasSort) && paths[0] === 'boards') {
        function sortBoardColumn(board: BoardVariant, boardColumn: string) {
          if (board === 'byScopes') {
            if (boardColumn === 'space') {
              sortAlarmGroup(state, 'bySpace', 'space');
            } else {
              sortAlarmGroup(state, 'byScopes', boardColumn);
            }
          } else {
            sortAlarmGroup(state, board, boardColumn);
          }
        }
        if (only2Paths) {
          const [, board] = paths as ['boards', BoardVariant];
          Object.keys(value).forEach(boardColumn => {
            if (value[boardColumn].sort !== undefined) {
              sortBoardColumn(board, boardColumn);
            }
          });
        } else if (hasSort) {
          const [, board, boardColumn] = paths as ['boards', BoardVariant, string];
          sortBoardColumn(board, boardColumn);
        }
      }

      if (
        (path === 'table' && value.sort !== undefined) ||
        (only2Paths && hasSort && paths[0] === 'table')
      ) {
        sortAlarmTableGroup(state);
      }
    },
    bulkUpdate: (state, { payload: { alarms, deviceTypes, forceUpdate = false } }) => {
      if (forceUpdate === false && !state.preferences.autoRefresh) {
        const count = ALARMS_CACHE_COUNT_MAX - state.ids.length + PENDING_UPDATES_COUNT_MAX;
        if (count > 0) {
          state.pendingUpdates = alarms.slice(-count);
        }
        return;
      }

      const scopeTypeMappers: {
        scopeDeviceType: string;
        scopeType: ScopeVariant;
      }[] = [
        { scopeDeviceType: deviceTypes.powerSystem, scopeType: 'power-system' },
        { scopeDeviceType: deviceTypes.hvacSystem, scopeType: 'hvac-system' },
        { scopeDeviceType: deviceTypes.elvSystem, scopeType: 'elv-system' },
      ];
      const pendingUpdates: LeftAlarmMessageDescriptor[] = [];
      // Step 1: delete alarms
      [...state.pendingUpdates, ...alarms].forEach(({ action, alarm: _backendAlarmData }) => {
        const existingAlarm = state.alarms[_backendAlarmData.id] as AlarmJSON | undefined;
        if (
          action === 'UPDATE' &&
          !existingAlarm &&
          !pendingUpdates.some(p => p.alarm.id === _backendAlarmData.id && p.action === 'ADD')
        ) {
          logger(
            `Skipping to push an alarm(${_backendAlarmData.id}) update message due to existing alarm not found!`,
            _backendAlarmData
          );
          return;
        }

        if (action === 'DEL') {
          const alarm = Alarm.fromApiObject(
            existingAlarm
              ? merge(Alarm.fromJSON(existingAlarm).toApiObject(), _backendAlarmData)
              : (_backendAlarmData as BackendAlarm)
          ).toJSON();
          const previousAlarmViewingStates = existingAlarm
            ? transformAlarmViewingStates(
                existingAlarm.lifecycleState.code,
                existingAlarm.state.code
              )
            : [];

          remove(state.pendingUpdates, pendingUpdate => pendingUpdate.alarm.id === alarm.id);
          remove(state.ids, (id: AlarmId) => id === alarm.id);
          delete state.alarms[alarm.id];

          // statistics start
          // ------
          // state.statistics.byCreatedAt.forEach(([[from, to]], idx) => {
          //   if (dayjs(from).isBefore(currentCreatedAt) && dayjs(to).isAfter(currentCreatedAt)) {
          //     state.statistics.byCreatedAt[idx][1] -= 1;
          //   }
          // });

          // alarms groups start
          // ------
          if (
            Array.isArray(state.alarmsGroups.byAlarmLevels[alarm.level]) &&
            state.alarmsGroups.byAlarmLevels[alarm.level].includes(alarm.id)
          ) {
            state.alarmsGroups.byAlarmLevels[alarm.level] = state.alarmsGroups.byAlarmLevels[
              alarm.level
            ].filter(id => id !== alarm.id);
          }
          if (
            Array.isArray(state.alarmsGroups.byDeviceTypes[alarm.source.deviceType]) &&
            state.alarmsGroups.byDeviceTypes[alarm.source.deviceType].includes(alarm.id)
          ) {
            state.alarmsGroups.byDeviceTypes[alarm.source.deviceType] =
              state.alarmsGroups.byDeviceTypes[alarm.source.deviceType].filter(
                id => id !== alarm.id
              );
          }

          // `'board--by-scopes'` start
          // ------
          const scopeTypeMapper = scopeTypeMappers.find(
            item => item.scopeDeviceType === alarm.source.deviceTypeLevel1
          );
          if (
            scopeTypeMapper &&
            Array.isArray(state.alarmsGroups.byScopes[scopeTypeMapper.scopeType]) &&
            state.alarmsGroups.byScopes[scopeTypeMapper.scopeType].includes(alarm.id)
          ) {
            state.alarmsGroups.byScopes[scopeTypeMapper.scopeType] = state.alarmsGroups.byScopes[
              scopeTypeMapper.scopeType
            ].filter(id => id !== alarm.id);
          }

          if (
            Array.isArray(state.alarmsGroups.bySpace.root) &&
            state.alarmsGroups.bySpace.root.includes(alarm.id)
          ) {
            state.alarmsGroups.bySpace.root = state.alarmsGroups.bySpace.root.filter(
              id => id !== alarm.id
            );
          }

          previousAlarmViewingStates.forEach(alarmViewingState => {
            if (
              Array.isArray(state.alarmsGroups.byStates[alarmViewingState]) &&
              state.alarmsGroups.byStates[alarmViewingState].includes(alarm.id)
            ) {
              state.alarmsGroups.byStates[alarmViewingState] = state.alarmsGroups.byStates[
                alarmViewingState
              ].filter(id => id !== alarm.id);
            }
          });

          return;
        }

        pendingUpdates.push({ action, alarm: _backendAlarmData });
      });

      /**
       * 用于判断是否要重新排序用户关注的告警
       */
      let includesYourInterests = false;

      // Step 2: add/update alarms
      // ------
      pendingUpdates.forEach(({ action, alarm: _backendAlarmData }) => {
        const existingAlarm = state.alarms[_backendAlarmData.id] as AlarmJSON | undefined;
        const alarm = Alarm.fromApiObject(
          existingAlarm
            ? merge(Alarm.fromJSON(existingAlarm).toApiObject(), _backendAlarmData)
            : (_backendAlarmData as BackendAlarm)
        ).toJSON();
        if (state.yourInterests.includes(alarm.id) && includesYourInterests === false) {
          includesYourInterests = true;
        }

        const isDeviceAlarm = alarm.source.dimension === 'DEVICE';
        const previousAlarmViewingStates = existingAlarm
          ? transformAlarmViewingStates(existingAlarm.lifecycleState.code, existingAlarm.state.code)
          : [];
        const currentAlarmViewingStates = transformAlarmViewingStates(
          alarm.lifecycleState.code,
          alarm.state.code
        );
        const setExpectedAlarmFn = (
          state: AlarmsMonitoringBoardSliceState,
          boardVariant: 'byStates' | 'byAlarmLevels' | 'byScopes',
          alarm: AlarmJSON
        ) => {
          if (alarm.expectedStatus === 'EXPECTED' || alarm.mergeStatus === 'CHILD') {
            const expecteds = state.alarmsGroups[boardVariant]['expecteds'];
            if (expecteds.includes(alarm.id)) {
              state.alarmsGroups[boardVariant]['expecteds'] = state.alarmsGroups[boardVariant][
                'expecteds'
              ].filter(id => id !== alarm.id);
            }
            state.alarmsGroups[boardVariant]['expecteds'].push(alarm.id);
          }
          /*当其他card告警变成预期内告警，需要把此告警从之前card移除出去*/
          currentAlarmViewingStates.forEach(alarmViewingState => {
            const currectAlarmsGroups = state.alarmsGroups.byStates[alarmViewingState];
            if (currectAlarmsGroups) {
              state.alarmsGroups.byStates[alarmViewingState] = currectAlarmsGroups.filter(
                id => id !== alarm.id
              );
            }
          });
        };

        const removeExpectedAlarmFn = (
          state: AlarmsMonitoringBoardSliceState,
          boardVariant: 'byStates' | 'byAlarmLevels' | 'byScopes',
          alarm: AlarmJSON
        ) => {
          const expecteds = state.alarmsGroups[boardVariant]['expecteds'];
          if (expecteds.includes(alarm.id)) {
            state.alarmsGroups[boardVariant]['expecteds'] = state.alarmsGroups[boardVariant][
              'expecteds'
            ].filter(id => id !== alarm.id);
          }
        };

        if (!existingAlarm) {
          if (action === 'ADD') {
            if (state.ids.length >= ALARMS_CACHE_COUNT_MAX) {
              logger(
                `Skipping to add alarm(${alarm.id}) due to max alarms cache count(${ALARMS_CACHE_COUNT_MAX}) reached!`,
                _backendAlarmData
              );
              if (state.pendingUpdates.length < PENDING_UPDATES_COUNT_MAX) {
                state.pendingUpdates.push({ action, alarm: _backendAlarmData });
              }
              return;
            }

            state.ids.push(alarm.id);
            state.alarms[alarm.id] = alarm;

            // initializing boards preferences
            if (!state.preferences.boards.byAlarmLevels.boardColumns.includes(alarm.level)) {
              state.preferences.boards.byAlarmLevels.boardColumns.push(alarm.level);
            }
            const existingByAlarmLevelBoardPreferences =
              state.preferences.boards.byAlarmLevels[alarm.level];
            if (existingByAlarmLevelBoardPreferences === undefined) {
              state.preferences.boards.byAlarmLevels[alarm.level] = {
                layout: 'card',
                sort: 'combined-ranking',
                layoutColumns: {
                  card: byAlarmLevelsBoardCardLayoutColumnsConfigs,
                  table: defaultBoardTableLayoutColumnsConfigs,
                },
              };
            }

            if (
              !state.preferences.boards.byDeviceTypes.boardColumns.includes(alarm.source.deviceType)
            ) {
              state.preferences.boards.byDeviceTypes.boardColumns.push(alarm.source.deviceType);
            }
            const existingByDeviceTypeBoardPreferences =
              state.preferences.boards.byDeviceTypes[alarm.source.deviceType];
            if (existingByDeviceTypeBoardPreferences === undefined) {
              state.preferences.boards.byDeviceTypes[alarm.source.deviceType] =
                createInitialBoardPreferences();
            }

            // statistics start
            // ------
            // state.statistics.byCreatedAt.forEach(([[from, to]], idx) => {
            //   if (dayjs(from).isBefore(currentCreatedAt) && dayjs(to).isAfter(currentCreatedAt)) {
            //     state.statistics.byCreatedAt[idx][1] += 1;
            //   }
            // });

            // alarms groups start
            // ------
            if (isExpectedFn(alarm)) {
              //set expected alarm
              setExpectedAlarmFn(state, 'byStates', alarm);
              setExpectedAlarmFn(state, 'byAlarmLevels', alarm);
              setExpectedAlarmFn(state, 'byScopes', alarm);
            } else {
              if (state.alarmsGroups.byAlarmLevels[alarm.level] === undefined) {
                state.alarmsGroups.byAlarmLevels[alarm.level] = [alarm.id];
              } else {
                state.alarmsGroups.byAlarmLevels[alarm.level].push(alarm.id);
              }
              const scopeTypeMapper = scopeTypeMappers.find(
                item => item.scopeDeviceType === alarm.source.deviceTypeLevel1
              );
              if (isDeviceAlarm && scopeTypeMapper) {
                if (state.alarmsGroups.byDeviceTypes[alarm.source.deviceType] === undefined) {
                  state.alarmsGroups.byDeviceTypes[alarm.source.deviceType] = [alarm.id];
                } else {
                  state.alarmsGroups.byDeviceTypes[alarm.source.deviceType].push(alarm.id);
                }
                state.alarmsGroups.byScopes[scopeTypeMapper.scopeType].push(alarm.id);
              } else {
                // 我们认为不是设备下的测点告警的话，那就属于“其他”测点告警
                if (state.alarmsGroups.bySpace.root === undefined) {
                  state.alarmsGroups.bySpace.root = [alarm.id];
                } else {
                  state.alarmsGroups.bySpace.root.push(alarm.id);
                }
              }
              currentAlarmViewingStates.forEach(alarmViewingState => {
                if (state.alarmsGroups.byStates[alarmViewingState] === undefined) {
                  state.alarmsGroups.byStates[alarmViewingState] = [alarm.id];
                } else {
                  state.alarmsGroups.byStates[alarmViewingState].push(alarm.id);
                }
              });
            }
          } else if (action === 'UPDATE') {
            logger(
              `Skipping to update alarm(${alarm.id}) due to existing alarm not found!`,
              _backendAlarmData
            );
          }
        } else {
          if (action === 'ADD' || action === 'UPDATE') {
            state.alarms[alarm.id] = alarm;

            // statistics start
            // ------
            // 如果告警已存在且本次更新为“已恢复”的状态，则需要将统计数据减 1
            if (
              existingAlarm.state.code !== 'RECOVER' &&
              alarm.state.code === 'RECOVER' &&
              !isExpectedFn(alarm)
            ) {
              if (typeof state.statistics.byAlarmLevels[existingAlarm.level] == 'number') {
                state.statistics.byAlarmLevels[existingAlarm.level] -= 1;
              }
              if (
                typeof state.statistics.byDeviceTypes[existingAlarm.source.deviceType] == 'number'
              ) {
                state.statistics.byDeviceTypes[existingAlarm.source.deviceType] -= 1;
              }
              if (
                typeof existingAlarm.source.roomType == 'string' &&
                typeof state.statistics.byRoomTypes[existingAlarm.source.roomType] == 'number'
              ) {
                state.statistics.byRoomTypes[existingAlarm.source.roomType] -= 1;
              }
            }
            // const previousCreatedAt = dayjs(existingAlarm.createdAt);
            // state.statistics.byCreatedAt.forEach(([[from, to]], idx) => {
            //   if (dayjs(from).isBefore(previousCreatedAt) && dayjs(to).isAfter(previousCreatedAt)) {
            //     state.statistics.byCreatedAt[idx][1] -= 1;
            //   }
            //   if (dayjs(from).isBefore(currentCreatedAt) && dayjs(to).isAfter(currentCreatedAt)) {
            //     state.statistics.byCreatedAt[idx][1] += 1;
            //   }
            // });

            // alarms groups start
            // ------
            // 理论上告警等级、告警对象的设备类型、GUID 都不会发生改变
            // 这里先不处理这些分组
            if (isExpectedFn(alarm)) {
              //set expected alarm
              setExpectedAlarmFn(state, 'byStates', alarm);
              setExpectedAlarmFn(state, 'byAlarmLevels', alarm);
              setExpectedAlarmFn(state, 'byScopes', alarm);
            } else {
              removeExpectedAlarmFn(state, 'byStates', alarm);
              removeExpectedAlarmFn(state, 'byAlarmLevels', alarm);
              removeExpectedAlarmFn(state, 'byScopes', alarm);
              previousAlarmViewingStates.forEach(alarmViewingState => {
                if (
                  Array.isArray(state.alarmsGroups.byStates[alarmViewingState]) &&
                  state.alarmsGroups.byStates[alarmViewingState].includes(alarm.id)
                ) {
                  state.alarmsGroups.byStates[alarmViewingState] = state.alarmsGroups.byStates[
                    alarmViewingState
                  ].filter(id => id !== alarm.id);
                }
              });
              currentAlarmViewingStates.forEach(alarmViewingState => {
                if (state.alarmsGroups.byStates[alarmViewingState] === undefined) {
                  state.alarmsGroups.byStates[alarmViewingState] = [alarm.id];
                } else {
                  state.alarmsGroups.byStates[alarmViewingState].push(alarm.id);
                }
              });
            }
          }
        }
      });

      // Step 3: sort alarms
      // ------
      sortAlarmGroup(state, 'byStates');
      sortAlarmGroup(state, 'byScopes');
      sortAlarmGroup(state, 'bySpace', 'space');
      sortAlarmGroup(state, 'byAlarmLevels');
      sortAlarmTableGroup(state);
      if (includesYourInterests) {
        const board = mapLayout2Board(state.preferences.layout);
        if (board !== null) {
          sortAlarmGroup(state, board, 'your-interests');
        }
      }

      // Step4: count alarms
      countAlarmGroup(state);
    },
    removeAlarms: (state, { payload: { alarmIds, destroy = true } }) => {
      const predicate = (id: AlarmId) => alarmIds.includes(id);

      remove(state.ids, predicate);
      remove(state.yourInterests, predicate);

      type AlarmsGroupKey = keyof typeof state.alarmsGroups;
      type Key = keyof (typeof state.alarmsGroups)[AlarmsGroupKey];
      (Object.keys(state.alarmsGroups) as AlarmsGroupKey[]).forEach(alarmsGroup => {
        (Object.keys(state.alarmsGroups[alarmsGroup]) as Key[]).forEach(key => {
          remove(state.alarmsGroups[alarmsGroup][key], predicate);
        });
      });

      if (destroy) {
        alarmIds.forEach(id => {
          delete state.alarms[id];
        });
      }
    },
  },
});

const alarmLifecycleStateSortOrders: Record<BackendAlarmLifecycleState, number> = {
  INIT: 0,
  ACTIVE: 1,
  CONFIRMED: 2,
  PROCESS: 3,
  REMOVED: 4,
};
const alarmStateSortOrders: Record<BackendAlarmState, number> = {
  TRIGGER: 0,
  RECOVER: 2,
  SUPPRESS: 3,
};
const alarmTypes: Record<BackendAlarmType, number> = {
  ERROR: 0,
  WARN: 1,
};

function mapLayout2Board(layout: LayoutVariant): BoardVariant | null {
  switch (layout) {
    case 'board--by-states':
      return 'byStates';
    case 'board--by-scopes':
      return 'byScopes';
    case 'board--by-alarm-levels':
      return 'byAlarmLevels';
    default:
      return null;
  }
}

function countAlarmGroup(state: AlarmsMonitoringBoardSliceState) {
  state.statistics = state.ids.reduce<Statistics>(
    (mapper, alarmId) => {
      const alarm = state.alarms[alarmId];
      if (isExpectedFn(alarm)) {
        return mapper;
      }
      if (alarm.state.code !== 'RECOVER') {
        mapper.byAlarmLevels[alarm.level] = (mapper.byAlarmLevels[alarm.level] ?? 0) + 1;
        mapper.byDeviceTypes[alarm.source.deviceType] =
          (mapper.byDeviceTypes[alarm.source.deviceType] ?? 0) + 1;
        if (typeof alarm.source.roomType == 'string') {
          mapper.byRoomTypes[alarm.source.roomType] =
            (mapper.byRoomTypes[alarm.source.roomType] ?? 0) + 1;
        }
      }

      return mapper;
    },
    { byAlarmLevels: {}, byDeviceTypes: {}, byRoomTypes: {} }
  );
}

function sortAlarmGroup(
  state: AlarmsMonitoringBoardSliceState,
  group: BoardVariant | 'bySpace',
  /**
   * Sort this `boardColumn` only
   */
  boardColumn?: string
) {
  const board: BoardVariant = group === 'bySpace' ? 'byScopes' : group;
  if (boardColumn === 'your-interests') {
    const alarmIds = state.yourInterests;
    const { sort } = state.preferences.boards[board]['your-interests'];
    const newAlarmIds = sortAlarms(sort, alarmIds, state.alarms, state.yourInterestsAlarms);
    state.yourInterests = newAlarmIds;
  } else {
    state.preferences.boards[board].boardColumns.forEach(_boardColumn => {
      // `_boardColumn` is a number if `board` is `byAlarmLevels`
      if (boardColumn !== undefined && boardColumn !== _boardColumn.toString()) {
        // Skipping...
        return;
      }

      const alarmIds =
        (_boardColumn === 'space'
          ? state.alarmsGroups.bySpace.root
          : // eslint-disable-next-line @typescript-eslint/no-explicit-any
            ((state.alarmsGroups[group] as any)[_boardColumn] as number[])) ?? [];

      if (alarmIds.length <= 0) {
        return;
      }

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const { sort } = (state.preferences.boards[board] as any)[_boardColumn] as BoardPreferences;
      const newAlarmIds = sortAlarms(sort, alarmIds, state.alarms, state.yourInterestsAlarms);
      if (_boardColumn === 'space') {
        state.alarmsGroups.bySpace.root = newAlarmIds;
      } else {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (state.alarmsGroups[group] as any)[_boardColumn] = newAlarmIds;
      }
    });
  }
}

function sortAlarmTableGroup(state: AlarmsMonitoringBoardSliceState) {
  const newAlarmIds = sortAlarms(
    state.preferences.table.sort,
    state.ids,
    state.alarms,
    state.yourInterestsAlarms
  );
  state.ids = newAlarmIds;
}

type AlarmSortOrder = {
  alarmId: AlarmId;
  lifecycleState: number;
  state: number;
  type: number;
  createdAt: number;
  latestTriggeredAt: number;
  recoveredAt: number | null;
  followedAt: number | null;
  level: number;
};

function sortAlarms(
  sort: SortValue,
  alarmIds: number[],
  alarms: Record<AlarmId, AlarmJSON>,
  yourInterestsAlarms: Record<AlarmId, YourInterestsAlarm>
) {
  let newAlarmIds: number[] = [...alarmIds];

  const alarmsSortOrders: AlarmSortOrder[] = [];
  alarmIds.forEach(alarmId => {
    const alarm = alarms[alarmId] as AlarmJSON | undefined;

    // `alarm` could be undefined when sorting `your-interests` alarms
    if (alarm === undefined) {
      return;
    }

    const yourInterestsAlarm = yourInterestsAlarms[alarmId] as YourInterestsAlarm | undefined;

    alarmsSortOrders.push({
      alarmId,
      lifecycleState: alarmLifecycleStateSortOrders[alarm.lifecycleState.code],
      state: alarmStateSortOrders[alarm.state.code],
      type: alarmTypes[alarm.type.code],
      createdAt: alarm.createdAt,
      latestTriggeredAt: alarm.latestTriggeredAt,
      recoveredAt: alarm.recoveredAt,
      followedAt: yourInterestsAlarm?.followedAt ?? null,
      level: Number(alarm.level),
    });
  });

  switch (sort) {
    case 'combined-ranking':
      newAlarmIds = orderBy(
        alarmsSortOrders,
        ['lifecycleState', 'state', 'level', 'type', 'createdAt'],
        ['asc', 'asc', 'asc', 'asc', 'desc']
      ).map(({ alarmId }) => alarmId);
      break;
    case 'alarm-created-at--ASC':
    case 'alarm-created-at--DESC':
      newAlarmIds = orderBy(
        alarmsSortOrders,
        ['createdAt'],
        [sort.endsWith('ASC') ? 'asc' : 'desc']
      ).map(({ alarmId }) => alarmId);
      break;
    case 'latest-triggered-at--ASC':
    case 'latest-triggered-at--DESC':
      newAlarmIds = orderBy(
        alarmsSortOrders,
        ['latestTriggeredAt'],
        [sort.endsWith('ASC') ? 'asc' : 'desc']
      ).map(({ alarmId }) => alarmId);
      break;
    case 'alarm-recovered-at--ASC':
    case 'alarm-recovered-at--DESC':
      newAlarmIds = orderBy(
        alarmsSortOrders,
        ['recoveredAt'],
        [sort.endsWith('ASC') ? 'asc' : 'desc']
      ).map(({ alarmId }) => alarmId);
      break;
    case 'alarm-level--ASC':
    case 'alarm-level--DESC':
      //  FIXME @Jerry 无法按照告警等级排序，因为等级编码不是有序的
      newAlarmIds = orderBy(
        alarmsSortOrders,
        ['level'],
        [sort.endsWith('ASC') ? 'asc' : 'desc']
      ).map(({ alarmId }) => alarmId);
      break;
    case 'followed-at--ASC':
    case 'followed-at--DESC':
      newAlarmIds = orderBy(
        alarmsSortOrders,
        ['followedAt'],
        [sort.endsWith('ASC') ? 'asc' : 'desc']
      ).map(({ alarmId }) => alarmId);
      break;
    default:
      break;
  }

  return newAlarmIds;
}

export function createInitialState(): AlarmsMonitoringBoardSliceState {
  return {
    initializing: false,
    initializingStage: null,
    resetting: false,
    preferences: {
      layout: 'board--by-states',
      ttsService: true,
      autoRefresh: true,
      statistics: {
        visible: true,
        variants: {
          byAlarmLevels: { visible: true },
          byDeviceTypes: { visible: true },
          byRoomTypes: { visible: true },
          // byCreatedAt: { visible: true },
        },
      },
      boards: {
        byStates: {
          boardColumns: [
            'expecteds',
            'unaccepted-n-unrecovered',
            'accepted-n-unrecovered',
            'unaccepted-n-recovered',
            'accepted-n-recovered',
          ],
          'your-interests': {
            layout: 'card',
            sort: 'followed-at--DESC',
            layoutColumns: {
              card: yourInterestsBoardCardLayoutColumnsConfigs,
              table: defaultBoardTableLayoutColumnsConfigs,
            },
          },
          // Unused begins
          // ------
          expecteds: defaultBoardPreferences,
          unaccepted: createInitialBoardPreferences(),
          accepted: createInitialBoardPreferences(),
          'connected-with-tickets': createInitialBoardPreferences(),
          // ------
          // Unused ends

          'unaccepted-n-unrecovered': {
            layout: 'card',
            sort: 'alarm-created-at--DESC',
            layoutColumns: {
              card: byStatesUnacceptedAndUnrecoveredBoardCardLayoutColumnsConfigs,
              table: defaultBoardTableLayoutColumnsConfigs,
            },
          },
          'accepted-n-unrecovered': {
            layout: 'card',
            sort: 'alarm-created-at--DESC',
            layoutColumns: {
              card: byStatesAcceptedAndUnrecoveredBoardCardLayoutColumnsConfigs,
              table: defaultBoardTableLayoutColumnsConfigs,
            },
          },
          'unaccepted-n-recovered': {
            layout: 'mini',
            sort: 'alarm-recovered-at--DESC',
            layoutColumns: {
              card: byStatesUnAcceptedAndRecoveredBoardCardLayoutColumnsConfigs,
              table: defaultBoardTableLayoutColumnsConfigs,
            },
          },
          'accepted-n-recovered': {
            layout: 'mini',
            sort: 'alarm-recovered-at--DESC',
            layoutColumns: {
              card: byStatesAcceptedAndRecoveredBoardCardLayoutColumnsConfigs,
              table: defaultBoardTableLayoutColumnsConfigs,
            },
          },
        },
        byScopes: {
          boardColumns: ['expecteds', 'power-system', 'hvac-system', 'elv-system', 'space'],
          'your-interests': {
            layout: 'mini',
            sort: 'followed-at--DESC',
            layoutColumns: {
              card: yourInterestsBoardCardLayoutColumnsConfigs,
              table: defaultBoardTableLayoutColumnsConfigs,
            },
          },
          expecteds: defaultBoardPreferences,
          'power-system': {
            layout: 'card',
            sort: 'combined-ranking',
            layoutColumns: {
              card: byScopesBoardCardLayoutColumnsConfigs,
              table: defaultBoardTableLayoutColumnsConfigs,
            },
          },
          'hvac-system': {
            layout: 'card',
            sort: 'combined-ranking',
            layoutColumns: {
              card: byScopesBoardCardLayoutColumnsConfigs,
              table: defaultBoardTableLayoutColumnsConfigs,
            },
          },
          'elv-system': {
            layout: 'card',
            sort: 'combined-ranking',
            layoutColumns: {
              card: byScopesBoardCardLayoutColumnsConfigs,
              table: defaultBoardTableLayoutColumnsConfigs,
            },
          },
          space: {
            layout: 'mini',
            sort: 'combined-ranking',
            layoutColumns: {
              card: byScopesBoardCardLayoutColumnsConfigs,
              table: defaultBoardTableLayoutColumnsConfigs,
            },
          },
        },
        // @ts-expect-error: TS2322 because of type mismatch
        byDeviceTypes: {
          boardColumns: ['expecteds'],
          'your-interests': {
            layout: 'mini',
            sort: 'followed-at--DESC',
            layoutColumns: {
              card: defaultBoardCardLayoutColumnsConfigs,
              table: defaultBoardTableLayoutColumnsConfigs,
            },
          },
          expecteds: defaultBoardPreferences,
        },
        // @ts-expect-error: TS2322 because of type mismatch
        byAlarmLevels: {
          boardColumns: ['expecteds'],
          'your-interests': {
            layout: 'mini',
            sort: 'followed-at--DESC',
            layoutColumns: {
              card: yourInterestsBoardCardLayoutColumnsConfigs,
              table: defaultBoardTableLayoutColumnsConfigs,
            },
          },
          expecteds: defaultBoardPreferences,
        },
      },
      table: {
        layoutColumns: {
          table: defaultBoardTableLayoutColumnsConfigs,
        },
        sort: 'alarm-created-at--DESC',
      },
    },
    statistics: {
      byAlarmLevels: {},
      byDeviceTypes: {},
      byRoomTypes: {},
      // byCreatedAt: createInitialByCreatedAtStatisticsTimeSpans().map(timeSpan => [timeSpan, 0]),
    },
    ids: [],
    alarms: {},
    pendingUpdates: [],
    yourInterests: [],
    yourInterestsAlarms: {},
    alarmsGroups: {
      byStates: {
        expecteds: [],
        unaccepted: [],
        accepted: [],
        'connected-with-tickets': [],
        'unaccepted-n-unrecovered': [],
        'accepted-n-unrecovered': [],
        'unaccepted-n-recovered': [],
        'accepted-n-recovered': [],
      },
      byDeviceTypes: { expecteds: [] },
      byScopes: {
        expecteds: [],
        'power-system': [],
        'hvac-system': [],
        'elv-system': [],
      },
      bySpace: {
        root: [],
      },
      byAlarmLevels: { expecteds: [] },
    },
  };
}

/**
 * 从当前时间往前推 24 小时，每 3 个小时一个时间段，共 8 段
 */
// function createInitialByCreatedAtStatisticsTimeSpans(): TimeSpan[] {
//   const timeSpans: TimeSpan[] = [];
//   let now = dayjs();
//   let current = now.valueOf();
//   Array(8)
//     .fill(null)
//     .forEach(() => {
//       const next = now.subtract(3, 'hour');
//       timeSpans.push([next.valueOf(), current]);

//       now = next;
//       current = next.valueOf();
//     });

//   return timeSpans;
// }

function createInitialBoardPreferences(): BoardPreferences {
  return {
    layout: 'mini',
    sort: 'combined-ranking',
    layoutColumns: {
      card: defaultBoardCardLayoutColumnsConfigs,
      table: defaultBoardTableLayoutColumnsConfigs,
    },
  };
}

/**
 * 根据告警生命周期状态和告警状态转换出 View 层需要的分类
 *
 * @param alarmLifecycleState
 * @param alarmState
 * @returns
 */
function transformAlarmViewingStates(
  alarmLifecycleState: BackendAlarmLifecycleState,
  alarmState: BackendAlarmState
): AlarmsViewingState[] {
  const alarmViewingStates: AlarmsViewingState[] = [];

  if (alarmLifecycleState === 'INIT' || alarmLifecycleState === 'ACTIVE') {
    alarmViewingStates.push('unaccepted');
    if (alarmState === 'RECOVER') {
      alarmViewingStates.push('unaccepted-n-recovered');
    } else {
      alarmViewingStates.push('unaccepted-n-unrecovered');
    }
  } else {
    if (alarmLifecycleState === 'PROCESS') {
      alarmViewingStates.push('connected-with-tickets');
    }
    alarmViewingStates.push('accepted');
    if (alarmState === 'RECOVER') {
      alarmViewingStates.push('accepted-n-recovered');
    } else {
      alarmViewingStates.push('accepted-n-unrecovered');
    }
  }

  return alarmViewingStates;
}
