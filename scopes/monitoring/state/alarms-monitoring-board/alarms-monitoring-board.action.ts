import { createAction } from '@reduxjs/toolkit';
import type { PartialDeep } from 'type-fest';

import type {
  AlarmUpdatesAction,
  AlarmsMonitoringBoardData,
  AlarmsMonitoringBoardDataSubscriptionsTarget,
} from '@manyun/monitoring.state.subscriptions';
import type { RequestError } from '@manyun/service.request';

import { alarmsMonitoringBoardSlice } from './alarms-monitoring-board.slice';
import type {
  AlarmId,
  ByAlarmLevelsBoardPreferences,
  ByScopesBoardPreferences,
  ByStatesBoardPreferences,
  LayoutVariant,
  Preferences,
  StatisticsVariant,
  TablePreferences,
} from './alarms-monitoring-board.slice';
import type { ColumnConfig } from './utils/column-util';

const prefix = `${alarmsMonitoringBoardSlice.name}/` as const;
const postfix = '--async' as const;

export const alarmsMonitoringBoardSliceActions = alarmsMonitoringBoardSlice.actions;

export type CreateActionPayload = {
  filters: AlarmsMonitoringBoardDataSubscriptionsTarget;
  /**
   * - 是否重新获取用户的偏好配置
   * - 是否重新获取用户关注的告警
   *
   * @defaultValue `false`
   */
  hardRefresh?: boolean;
};
const createBoardActionType = `${prefix}create${postfix}` as const;
export const createBoardAction = createAction<CreateActionPayload, typeof createBoardActionType>(
  createBoardActionType
);

export const resetBoardAction = createAction<void>(prefix + 'reset' + postfix);

const getYourInterestsActionType = `${prefix}get-your-interests${postfix}` as const;
/**
 * @internal
 */
export const getYourInterestsAction = createAction<void, typeof getYourInterestsActionType>(
  getYourInterestsActionType
);

export type UpdatePreferencesPayload = Pick<Preferences, 'layout' | 'ttsService' | 'autoRefresh'>;
const updatePreferencesActionType = `${prefix}update-preferences${postfix}` as const;
export const updatePreferences = createAction<
  UpdatePreferencesPayload,
  typeof updatePreferencesActionType
>(updatePreferencesActionType);

export type UpdateStatisticsPreferencesPayload = {
  /**
   * `'root'`: 统计信息展示的全局开关
   */
  variant: 'root' | StatisticsVariant;
  /**
   * 表示是否展示 `variant` 对应的统计信息
   */
  visible: boolean;
};
const updateStatisticsPreferencesActionType =
  `${prefix}update-statistics-preferences${postfix}` as const;
export const updateStatisticsPreferences = createAction<
  UpdateStatisticsPreferencesPayload,
  typeof updateStatisticsPreferencesActionType
>(updateStatisticsPreferencesActionType);

export interface UpdateSpecificBoardPreferencesPayload {
  layout: LayoutVariant;
  preferences:
    | PartialDeep<TablePreferences>
    | PartialDeep<ByStatesBoardPreferences>
    | PartialDeep<ByScopesBoardPreferences>
    | PartialDeep<ByAlarmLevelsBoardPreferences>
    | PartialDeep<ColumnConfig>;
  /**
   * 可以使用这个参数更新 `layoutColumns.card/table` 数组中的某项偏好配置
   *
   * @example
   * ```ts
   * // 假设要更新 `index: 4` 的 `column config`
   * // byStates['accepted-n-recovered']
   * const target = 'accepted-n-recovered.layoutColumns.card[4]';
   * // byScopes['power-system']
   * const target = 'power-system.layoutColumns.card[4]';
   * // byAlarmLevels['1']
   * const target = '1.layoutColumns.card[4]';
   * // table
   * const target = 'layoutColumns.table[4]'
   * ```
   */
  target?: string;
}
export interface UpdateTablePreferencesActionPayload extends UpdateSpecificBoardPreferencesPayload {
  layout: 'table';
  preferences: PartialDeep<TablePreferences>;
}
export interface UpdateByStatesBoardPreferencesActionPayload
  extends UpdateSpecificBoardPreferencesPayload {
  layout: 'board--by-states';
  preferences: PartialDeep<ByStatesBoardPreferences>;
}
export interface UpdateByScopesBoardPreferencesActionPayload
  extends UpdateSpecificBoardPreferencesPayload {
  layout: 'board--by-scopes';
  preferences: PartialDeep<ByScopesBoardPreferences>;
}
export interface UpdateByAlarmsBoardPreferencesActionPayload
  extends UpdateSpecificBoardPreferencesPayload {
  layout: 'board--by-alarm-levels';
  preferences: PartialDeep<ByAlarmLevelsBoardPreferences>;
}
const updateSpecificBoardPreferencesActionType =
  `${prefix}update-specific-board-preferences${postfix}` as const;
export const updateSpecificBoardPreferences = createAction<
  | UpdateSpecificBoardPreferencesPayload
  | UpdateTablePreferencesActionPayload
  | UpdateByStatesBoardPreferencesActionPayload
  | UpdateByScopesBoardPreferencesActionPayload
  | UpdateByAlarmsBoardPreferencesActionPayload,
  typeof updateSpecificBoardPreferencesActionType
>(updateSpecificBoardPreferencesActionType);

export type ResetPreferencesActionPayload = {
  /**
   * @example
   * ```ts
   * const target1 = 'boards.byStates.unaccepted-n-unrecovered';
   * const target2 = 'table';
   * ```
   */
  target: string;
  /**
   * 需要被重置的属性集合
   */
  properties: ('sort' | 'layoutColumns.card' | 'layoutColumns.table')[];
};
const resetPreferencesActionType = `${prefix}reset-preferences${postfix}` as const;
/**
 * 重置某个看板或常规列表的部分偏好配置
 *
 * @example
 * ```ts
 * dispatch(resetPreferencesAction({
 *   target: 'boards.byStates.unaccepted-n-unrecovered',
 *   // target: 'boards.byScopes.power-system',
 *   // target: 'boards.byAlarmLevels.1',
 *   // target: 'table',
 *   properties: ['sort'];
 *   // properties: ['layoutColumns.card'];
 *   // properties: ['layoutColumns.table'];
 * }));
 * ```
 */
export const resetPreferencesAction = createAction<
  ResetPreferencesActionPayload,
  typeof resetPreferencesActionType
>(resetPreferencesActionType);

export type AddAlarmsIntoInterestsActionPayload = {
  alarmIds: AlarmId[];
  callback?(error: RequestError | null): void;
};
const addAlarmsIntoInterestsActionType = `${prefix}add-alarms-into-interests${postfix}` as const;
/**
 * 批量关注告警
 */
export const addAlarmsIntoInterestsAction = createAction<
  AddAlarmsIntoInterestsActionPayload,
  typeof addAlarmsIntoInterestsActionType
>(addAlarmsIntoInterestsActionType);

export type RemoveAlarmsFromInterestsActionPayload = {
  alarmIds: AlarmId[];
  callback?(error: RequestError | null): void;
};
const removeAlarmsFromInterestsActionType =
  `${prefix}remove-alarms-from-interests${postfix}` as const;
/**
 * 批量取消关注告警
 */
export const removeAlarmsFromInterestsAction = createAction<
  RemoveAlarmsFromInterestsActionPayload,
  typeof removeAlarmsFromInterestsActionType
>(removeAlarmsFromInterestsActionType);

export type AcceptAlarmsActionPayload = {
  alarmIds: AlarmId[];
  callback?(error: RequestError | null): void;
};
const acceptAlarmsActionType = `${prefix}accept-alarms${postfix}` as const;
/**
 * 批量受理告警
 */
export const acceptAlarmsAction = createAction<
  AcceptAlarmsActionPayload,
  typeof acceptAlarmsActionType
>(acceptAlarmsActionType);

export type CloseAlarmsActionPayload = {
  alarmIds: AlarmId[];
  reason: string;
  descriptions: string;
  ticketNumber?: string | null;
  isRemovedByClickOnce?: boolean;
  callback?(error: RequestError | null): void;
};
const closeAlarmsActionType = `${prefix}close-alarms${postfix}` as const;
/**
 * 批量下盯屏告警
 */
export const closeAlarmsAction = createAction<
  CloseAlarmsActionPayload,
  typeof closeAlarmsActionType
>(closeAlarmsActionType);

export type ConnectAlarmsWithEventActionPayload = {
  alarmIds: AlarmId[];
  eventId: string;
  idc: string;
  deviceGuids?: string[];
  alarmStartTimes: number[];
  callback?(error: RequestError | null): void;
};
const connectAlarmsWithEventActionType = `${prefix}connect-alarms-with-event${postfix}` as const;
export const connectAlarmsWithEventAction = createAction<
  ConnectAlarmsWithEventActionPayload,
  typeof connectAlarmsWithEventActionType
>(connectAlarmsWithEventActionType);

export type ReceiveAlarmsMessageActionPayload = {
  alarms: Array<{
    action: AlarmUpdatesAction;
    alarm: Omit<AlarmsMonitoringBoardData, 'type'>;
  }>;
  /**
   * Ignore `auto-refresh` switch
   *
   * @defaultValue `false`
   */
  forceUpdate?: boolean;
};
/**
 * WS 告警盯屏订阅中需要调用此 Action 来更新告警盯屏的数据
 */
export const receiveAlarmsMessageAction = createAction<ReceiveAlarmsMessageActionPayload>(
  prefix + 'receive-alarms-message' + postfix
);

export type LaunchPointValuesIntervalActionPayload = {
  idc: string;
};
const launchPointValuesIntervalActionType =
  `${prefix}launch-point-values-interval${postfix}` as const;
/**
 * @interval
 */
export const launchPointValuesIntervalAction = createAction<
  LaunchPointValuesIntervalActionPayload,
  typeof launchPointValuesIntervalActionType
>(launchPointValuesIntervalActionType);

const terminatePointValuesIntervalActionType =
  `${prefix}terminate-point-values-interval${postfix}` as const;
/**
 * @interval
 */
export const terminatePointValuesIntervalAction = createAction<
  void,
  typeof terminatePointValuesIntervalActionType
>(terminatePointValuesIntervalActionType);
