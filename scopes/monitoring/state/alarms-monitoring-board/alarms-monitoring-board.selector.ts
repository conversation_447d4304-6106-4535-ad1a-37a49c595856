import pick from 'lodash.pick';

import { alarmsMonitoringBoardSlice } from './alarms-monitoring-board.slice';
import type {
  AlarmId,
  AlarmsMonitoringBoardSliceState,
  ByAlarmLevelsGroup,
  ByStatesAlarmsGroup,
  LayoutVariant,
  Preferences,
  ScopeVariant,
  Statistics,
  StatisticsVariant,
} from './alarms-monitoring-board.slice';

type StoreState = {
  [alarmsMonitoringBoardSlice.name]: AlarmsMonitoringBoardSliceState;
};

/**
 * 用于拿到当前 Slice State 处于的状态
 *
 * - `initializing`: 表示初始化中，应在 UI 层面渲染 `loading...` 效果
 * - `stage`: 表示初始化阶段，可以在 UI 层面渲染相应的文案
 *
 * @param storeState
 * @returns
 */
export const selectCurrentState = (storeState: StoreState) => {
  const { initializing, initializingStage } = storeState[alarmsMonitoringBoardSlice.name];

  return {
    initializing,
    stage: initializingStage,
  };
};

/**
 * Warning: this is not a public API, INTERNAL USE ONLY.
 *
 * @internal
 * @param storeState
 * @returns
 */
export const selectPreferencesSnapshot = (storeState: StoreState) =>
  storeState[alarmsMonitoringBoardSlice.name].preferences;

/**
 * 获取基本的偏好配置
 *
 * - 布局方式
 * - 语音播报启用状态
 * - 自动刷新启用状态
 *
 * @param storeState
 * @returns
 */
export const selectPreferences = (storeState: StoreState) => {
  const { preferences } = storeState[alarmsMonitoringBoardSlice.name];

  return pick(preferences, ['layout', 'ttsService', 'autoRefresh']);
};

/**
 * 获取统计信息展示的偏好配置
 *
 * @param storeState
 * @returns
 */
export const selectStatisticsPreferences = (storeState: StoreState) =>
  storeState[alarmsMonitoringBoardSlice.name].preferences.statistics;

/**
 * 获取常规列表布局下的偏好配置
 *
 * @param layout
 */
export function selectBoardPreferences(
  layout: 'table'
): (storeState: StoreState) => Preferences['table'];
/**
 * 获取告警状态看板布局下的偏好配置
 *
 * @param layout
 */
export function selectBoardPreferences(
  layout: 'board--by-states'
): (storeState: StoreState) => Preferences['boards']['byStates'];
/**
 * 获取专业类型看板布局下的偏好配置
 *
 * @param layout
 */
export function selectBoardPreferences(
  layout: 'board--by-scopes'
): (storeState: StoreState) => Preferences['boards']['byScopes'];
/**
 * 获取告警等级看板布局下的偏好配置
 *
 * @param layout
 *
 * @example
 *
 * ```tsx
 * const byAlarmLevelsPreferences = useSelector(selectBoardPreferences('board--by-alarm-levels'));
 * const alarmLevel1Preferences = byAlarmLevelsPreferences[alarmLevel1Code];
 * ```
 */
export function selectBoardPreferences(
  layout: 'board--by-alarm-levels'
): (storeState: StoreState) => Preferences['boards']['byAlarmLevels'];
export function selectBoardPreferences(layout: LayoutVariant) {
  return (storeState: StoreState) => {
    const {
      preferences: { boards, table },
    } = storeState[alarmsMonitoringBoardSlice.name];
    if (layout === 'table') {
      return table;
    } else if (layout === 'board--by-states') {
      return boards.byStates;
    } else if (layout === 'board--by-scopes') {
      return boards.byScopes;
    } else if (layout === 'board--by-alarm-levels') {
      return boards.byAlarmLevels;
    } else {
      return null;
    }
  };
}

/**
 * 获取所有的统计信息
 */
export function selectStatistics(): (storeState: StoreState) => Statistics;
/**
 * 获取按告警等级分布的统计信息
 *
 * @param statisticsVariant
 */
export function selectStatistics(
  statisticsVariant: 'byAlarmLevels'
): (storeState: StoreState) => Statistics['byAlarmLevels'];
/**
 * 获取告警设备类型分布的统计信息
 *
 * @param statisticsVariant
 */
export function selectStatistics(
  statisticsVariant: 'byDeviceTypes'
): (storeState: StoreState) => Statistics['byDeviceTypes'];
/**
 * 获取告警包间类型分布的统计信息
 *
 * @param statisticsVariant
 */
export function selectStatistics(
  statisticsVariant: 'byRoomTypes'
): (storeState: StoreState) => Statistics['byRoomTypes'];
// /**
//  * 获取告警时间分布的的统计信息
//  *
//  * @param statisticsVariant
//  */
// export function selectStatistics(
//   statisticsVariant: 'byCreatedAt'
// ): (storeState: StoreState) => Statistics['byCreatedAt'];
export function selectStatistics(statisticsVariant?: StatisticsVariant) {
  return (storeState: StoreState) => {
    const { statistics } = storeState[alarmsMonitoringBoardSlice.name];

    if (statisticsVariant === undefined) {
      return statistics;
    }

    return statistics[statisticsVariant];
  };
}

export function selectAlarms(storeState: StoreState) {
  return storeState[alarmsMonitoringBoardSlice.name].alarms;
}

export function selectPendingUpdates(storeState: StoreState) {
  return storeState[alarmsMonitoringBoardSlice.name].pendingUpdates;
}

/**
 * 获取当前用户已关注的告警 ID 集合
 *
 * > Why? 为什么这里不直接返回告警数据呢？
 * > 如果这里使用 `yourInterests.map(id => alarms[id])` 这种方式的话
 * > 将会每次调用都生成一个新的数组，会影响 UI 渲染性能，所以建议调用方在 UI 层面
 * > 使用 `useDeepCompareMemo` 的方式组装告警数据
 *
 * @example
 *
 * ```tsx
 * import { useSelector } from 'react-redux';
 * import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
 *
 * const yourInterestsAlarmsIds = useSelector(selectYourInterestsAlarmsIds);
 * const alarms = useSelector(selectAlarms);
 * const yourInterestsAlarms = useDeepCompareMemo(() => {
 *   const _alarms: AlarmJSON[] = [];
 *   yourInterestsAlarmsIds.forEach(id => {
 *     const _alarm = alarms[id];
 *     _alarms.push(_alarm);
 *   });
 * }, [yourInterestsAlarmsIds, alarms]);
 * ```
 *
 * @param storeState
 * @returns
 */
export function selectYourInterestsAlarmsIds(storeState: StoreState) {
  return storeState[alarmsMonitoringBoardSlice.name].yourInterests;
}

type ByScopesAlarmsGroups = Record<ScopeVariant | 'space', AlarmId[]>;

/**
 * 获取常规列表下的告警 ID 集合（目前是缓存中的所有 ID）
 *
 * @param layout
 */
export function selectAlarmsGroupsIds(layout: 'table'): (storeState: StoreState) => AlarmId[];
/**
 * 获取告警状态看板下每个看板的告警 ID 集合
 *
 * @param layout
 */
export function selectAlarmsGroupsIds(
  layout: 'board--by-states'
): (storeState: StoreState) => ByStatesAlarmsGroup;
/**
 * 获取专业类型看板下每个看板的告警 ID 集合
 *
 * @param layout
 */
export function selectAlarmsGroupsIds(
  layout: 'board--by-scopes'
): (storeState: StoreState) => ByScopesAlarmsGroups;
/**
 * 获取告警等级看板下每个看板的告警 ID 集合
 *
 * @param layout
 */
export function selectAlarmsGroupsIds(
  layout: 'board--by-alarm-levels'
): (storeState: StoreState) => ByAlarmLevelsGroup;
export function selectAlarmsGroupsIds(layout: LayoutVariant) {
  return (storeState: StoreState) => {
    const { ids, alarmsGroups } = storeState[alarmsMonitoringBoardSlice.name];
    if (layout === 'table') {
      return ids;
    } else if (layout === 'board--by-states') {
      return alarmsGroups['byStates'];
    } else if (layout === 'board--by-scopes') {
      return {
        ...alarmsGroups['byScopes'],
        space: alarmsGroups['bySpace'].root,
      };
    } else if (layout === 'board--by-alarm-levels') {
      return alarmsGroups['byAlarmLevels'];
    } else {
      return null;
    }
  };
}
