import type { BoardPreferences } from '../alarms-monitoring-board.slice';

export type ColumnType =
  | 'type.name'
  | 'level'
  | 'createdAt'
  | /* 位置 */ 'source.spaceGuidMap'
  | 'source.spaceGuidMap.idcTag'
  | 'source.spaceGuidMap.blockTag'
  | 'source.spaceGuidMap.roomTag'
  | 'source.roomName'
  | 'source.roomType'
  | /* 告警对象，使用 `source.name` 展示对象名称 */ 'source'
  | 'source.deviceType'
  | 'source.deviceLabel'
  | 'mergeCount'
  | 'point.name'
  | 'cause.name'
  | 'pointData.snapshot'
  | /* 触发条件 */ 'monitoringItem.threshold'
  | /* 现值 */ 'pointData.current'
  | 'alarmState'
  | 'latestTriggeredAt'
  | 'alarmLabel'
  | 'recoveredAt'
  | 'eventId'
  | /* 处理状态 */ 'lifecycleState'
  | 'confirmUser'
  | 'confirmedAt'
  | /* 命中规则 */ 'monitoringItem'
  | 'gridCount'
  | 'customerCount'
  | 'deviceCount'
  | 'notifyCount'
  | 'source.spaceGuidMap.columnTag'
  | 'source.spaceGuidMap.gridTag'
  | 'message';

// See https://ant.design/components/table-cn/#Column
export type TableColumnSortOrder = 'ascend' | 'descend' | false;

export type ColumnConfig = {
  /**
   * `null` 表示此字段不支持排序
   */
  sortOrder: TableColumnSortOrder | null;
  visible: boolean;
  /**
   * `locked: true`：表示不允许改变此列的 `visible`
   */
  locked: boolean;
};
export type AllColumnsConfig = Record<ColumnType, ColumnConfig>;
export type CardLayoutColumnConfig = {
  dataIndex: ColumnType;
} & Pick<ColumnConfig, 'visible' | 'locked'>;
export type TableLayoutColumnConfig = {
  dataIndex: ColumnType;
} & ColumnConfig;

export const defaultBoardCardLayoutColumns: ColumnType[] = [
  'lifecycleState',
  'alarmState',
  'alarmLabel',
  'createdAt',
  'recoveredAt',
  'source.spaceGuidMap',
  'source',
  'pointData.current',
];

const boardCardLayoutColumnsConfigs: CardLayoutColumnConfig[] =
  defaultBoardCardLayoutColumns.map<CardLayoutColumnConfig>(dataIndex => {
    if (
      [
        'lifecycleState',
        'alarmState',
        'alarmLabel',
        'createdAt',
        'source',
        'source.spaceGuidMap',
      ].includes(dataIndex)
    ) {
      return {
        dataIndex,
        visible: true,
        locked: false,
      };
    }
    return {
      dataIndex,
      visible: false,
      locked: false,
    };
  });

export const defaultBoardCardLayoutColumnsConfigs: CardLayoutColumnConfig[] =
  boardCardLayoutColumnsConfigs;

export const yourInterestsBoardCardLayoutColumnsConfigs: CardLayoutColumnConfig[] =
  boardCardLayoutColumnsConfigs;

export const byStatesUnacceptedAndUnrecoveredBoardCardLayoutColumnsConfigs: CardLayoutColumnConfig[] =
  boardCardLayoutColumnsConfigs;

export const byStatesAcceptedAndUnrecoveredBoardCardLayoutColumnsConfigs: CardLayoutColumnConfig[] =
  boardCardLayoutColumnsConfigs;

export const byStatesUnAcceptedAndRecoveredBoardCardLayoutColumnsConfigs: CardLayoutColumnConfig[] =
  boardCardLayoutColumnsConfigs;

export const byStatesAcceptedAndRecoveredBoardCardLayoutColumnsConfigs: CardLayoutColumnConfig[] =
  boardCardLayoutColumnsConfigs;

export const byScopesBoardCardLayoutColumnsConfigs: CardLayoutColumnConfig[] =
  boardCardLayoutColumnsConfigs;

export const byAlarmLevelsBoardCardLayoutColumnsConfigs: CardLayoutColumnConfig[] =
  boardCardLayoutColumnsConfigs;

const defaultBoardTableLayoutColumns: ColumnType[] = [
  'type.name',
  'level',
  'source.spaceGuidMap.blockTag',
  'source.spaceGuidMap.roomTag',
  'source.roomName',
  'source.spaceGuidMap',
  'source',
  'source.deviceType',
  'mergeCount',
  'point.name',
  'cause.name',
  'monitoringItem.threshold',
  'pointData.snapshot',
  'pointData.current',
  'alarmState',
  'createdAt',
  'latestTriggeredAt',
  'alarmLabel',
  'recoveredAt',
  'eventId',
  'lifecycleState',
  'confirmUser',
];

export const allColumnsConfig: AllColumnsConfig = {
  'type.name': {
    sortOrder: null,
    visible: true,
    locked: true,
  },
  level: {
    sortOrder: null,
    visible: true,
    locked: false,
  },
  createdAt: {
    sortOrder: false,
    visible: true,
    locked: false,
  },
  'source.spaceGuidMap': {
    sortOrder: null,
    visible: true,
    locked: false,
  },
  'source.spaceGuidMap.idcTag': {
    sortOrder: null,
    visible: false,
    locked: false,
  },
  'source.spaceGuidMap.blockTag': {
    sortOrder: null,
    visible: false,
    locked: false,
  },
  'source.spaceGuidMap.roomTag': {
    sortOrder: false,
    visible: true,
    locked: false,
  },
  'source.roomName': {
    sortOrder: null,
    visible: true,
    locked: false,
  },
  'source.roomType': {
    sortOrder: null,
    visible: false,
    locked: false,
  },
  source: {
    sortOrder: false,
    visible: true,
    locked: false,
  },
  'source.deviceType': {
    sortOrder: null,
    visible: false,
    locked: false,
  },
  'source.deviceLabel': {
    sortOrder: null,
    visible: false,
    locked: false,
  },
  mergeCount: {
    sortOrder: false,
    visible: true,
    locked: false,
  },
  'point.name': {
    sortOrder: null,
    visible: true,
    locked: false,
  },
  'cause.name': {
    sortOrder: null,
    visible: true,
    locked: false,
  },
  'pointData.snapshot': {
    sortOrder: null,
    visible: true,
    locked: false,
  },
  'monitoringItem.threshold': {
    sortOrder: null,
    visible: true,
    locked: false,
  },
  'pointData.current': {
    sortOrder: null,
    visible: true,
    locked: false,
  },
  alarmState: {
    sortOrder: null,
    visible: true,
    locked: false,
  },
  latestTriggeredAt: {
    sortOrder: null,
    visible: false,
    locked: false,
  },
  alarmLabel: {
    sortOrder: null,
    visible: true,
    locked: false,
  },
  recoveredAt: {
    sortOrder: null,
    visible: true,
    locked: false,
  },
  eventId: {
    sortOrder: null,
    visible: false,
    locked: false,
  },
  lifecycleState: {
    sortOrder: null,
    visible: false,
    locked: false,
  },
  confirmUser: {
    sortOrder: null,
    visible: false,
    locked: false,
  },
  confirmedAt: {
    sortOrder: null,
    visible: false,
    locked: false,
  },
  monitoringItem: {
    sortOrder: null,
    visible: false,
    locked: false,
  },
  gridCount: {
    sortOrder: null,
    visible: false,
    locked: false,
  },
  customerCount: {
    sortOrder: null,
    visible: false,
    locked: false,
  },
  deviceCount: {
    sortOrder: null,
    visible: false,
    locked: false,
  },
  notifyCount: {
    sortOrder: null,
    visible: false,
    locked: false,
  },
  'source.spaceGuidMap.columnTag': {
    sortOrder: null,
    visible: false,
    locked: false,
  },
  'source.spaceGuidMap.gridTag': {
    sortOrder: null,
    visible: false,
    locked: false,
  },
  message: {
    sortOrder: null,
    visible: false,
    locked: false,
  },
};

export const defaultBoardTableLayoutColumnsConfigs: TableLayoutColumnConfig[] =
  defaultBoardTableLayoutColumns.map<TableLayoutColumnConfig>(dataIndex => ({
    dataIndex,
    ...allColumnsConfig[dataIndex],
  }));

export const defaultBoardPreferences: BoardPreferences = {
  layout: 'card',
  sort: 'alarm-created-at--DESC',
  layoutColumns: {
    card: defaultBoardCardLayoutColumnsConfigs,
    table: defaultBoardTableLayoutColumnsConfigs,
  },
};
