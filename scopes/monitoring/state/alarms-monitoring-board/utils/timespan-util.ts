import dayjs from 'dayjs';
import calendar from 'dayjs/plugin/calendar';

import type { TimeSpan } from './../alarms-monitoring-board.slice';

// See https://day.js.org/docs/en/display/calendar-time
dayjs.extend(calendar);

// TODO: @Jerry locale wise
const calendarFormats = {
  sameDay: 'H点',
  lastDay: '[昨日]H[点]',
};

/**
 * ```js
 * const yesterday = dayjs('2022-06-21 19:30').valueOf();
 * const today = dayjs('2022-06-22 03:30').valueOf();
 * timeSpanUtil.format([yesterday, today]); // "昨日19点-3点"
 * ```
 *
 * @param timeSpan
 * @returns
 */
export function format([from, to]: TimeSpan) {
  return `${dayjs(from).calendar(null, calendarFormats)} - ${dayjs(to).calendar(
    null,
    calendarFormats
  )}`;
}
