import debugFactory from 'debug';
import get from 'lodash.get';
import type { ActionCreator, AnyAction } from 'redux';
import {
  all,
  call,
  delay,
  fork,
  put,
  race,
  select,
  take,
  takeEvery,
  takeLatest,
} from 'redux-saga/effects';
import type { SagaReturnType } from 'redux-saga/effects';

import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { setPreferences } from '@manyun/monitoring.cache.preferences';
import type { AlarmJSON } from '@manyun/monitoring.model.alarm';
import { Alarm } from '@manyun/monitoring.model.alarm';
import { acceptAlarms } from '@manyun/monitoring.service.accept-alarms';
import { batchRemoveAlarms } from '@manyun/monitoring.service.batch-remove-alarms';
import { deleteAlarmsByClickOnce } from '@manyun/monitoring.service.delete-alarms-by-click-once';
import { fetchPointValues } from '@manyun/monitoring.service.fetch-point-values';
import { fetchUserAlarmsMonitoringBoardPreferences } from '@manyun/monitoring.service.fetch-user-alarms-monitoring-board-preferences';
import { fetchYourInterestsAlarmIds } from '@manyun/monitoring.service.fetch-your-interests-alarm-ids';
import { mutateYourInterestsAlarmIds } from '@manyun/monitoring.service.mutate-your-interests-alarm-ids';
import { saveUserAlarmsMonitoringBoardPreferences } from '@manyun/monitoring.service.save-user-alarms-monitoring-board-preferences';
import {
  subscribeAlarmsMonitoringBoardAction,
  unsubscribeAlarmsMonitoringBoardAction,
} from '@manyun/monitoring.state.subscriptions';
import type {
  AlarmUpdatesAction,
  AlarmsMonitoringBoardData,
} from '@manyun/monitoring.state.subscriptions';
import { alarmsTtsManager } from '@manyun/monitoring.util.alarms-tts-manager';
import { ttsService } from '@manyun/monitoring.util.tts-service';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { fetchMetaDataByType } from '@manyun/resource-hub.service.fetch-meta-data-by-type';
import type { RequestError } from '@manyun/service.request';
import { connectEventWithAlarms } from '@manyun/ticket.service.connect-event-with-alarms';

import {
  acceptAlarmsAction,
  addAlarmsIntoInterestsAction,
  alarmsMonitoringBoardSliceActions,
  closeAlarmsAction,
  connectAlarmsWithEventAction,
  createBoardAction,
  getYourInterestsAction,
  launchPointValuesIntervalAction,
  receiveAlarmsMessageAction,
  removeAlarmsFromInterestsAction,
  resetBoardAction,
  resetPreferencesAction,
  terminatePointValuesIntervalAction,
  updatePreferences,
  updateSpecificBoardPreferences,
  updateStatisticsPreferences,
} from './alarms-monitoring-board.action';
import {
  selectAlarms,
  selectCurrentState,
  selectPendingUpdates,
  selectPreferencesSnapshot,
} from './alarms-monitoring-board.selector';
import { createInitialState } from './alarms-monitoring-board.slice';
import type { BoardVariant, SimpleChangedAlarm } from './alarms-monitoring-board.slice';
import {
  byAlarmLevelsBoardCardLayoutColumnsConfigs,
  defaultBoardTableLayoutColumnsConfigs,
} from './utils/column-util';

const debug = debugFactory('manyun.monitoring/state/alarms-monitoring-board:saga');

/** Workers */

export function* createBoardSaga({
  payload: { filters, hardRefresh = false },
}: ReturnType<typeof createBoardAction>) {
  const { initializing }: SagaReturnType<typeof selectCurrentState> =
    yield select(selectCurrentState);
  if (initializing) {
    debug("It's currently initializing a board now, skipping...");
    return;
  }
  yield put(alarmsMonitoringBoardSliceActions.initialize());
  const alarmLevelsMetaDataResp: SagaReturnType<typeof fetchMetaDataByType> = yield call(
    fetchMetaDataByType,
    {
      type: MetaType.ALARM_LEVEL,
    }
  );
  if (alarmLevelsMetaDataResp.error) {
    debug('Failed to fetch alarm levels meta data, error: ', alarmLevelsMetaDataResp.error);
  }
  if (alarmLevelsMetaDataResp.data.data) {
    yield put(
      alarmsMonitoringBoardSliceActions.setAlarmLevels(
        alarmLevelsMetaDataResp.data.data.map(metaData => metaData.toJSON())
      )
    );
  }
  yield put(alarmsMonitoringBoardSliceActions.nextInitializingStage({ hardRefresh }));
  const { error, data }: SagaReturnType<typeof fetchUserAlarmsMonitoringBoardPreferences> =
    yield call(fetchUserAlarmsMonitoringBoardPreferences);
  if (error) {
    debug('Failed to fetch existing user preferences, error: ', error);
    // Note: DONOT PUT A `return` here.
    // Because we wanna fallback to the default preferences.
  }
  if (data !== null) {
    setPreferences({ ttsService: { enabled: !!data.ttsService } });
    // If we get the remote user preferences, just replace it for good.
    yield put(alarmsMonitoringBoardSliceActions.replacePreferences(data));
  }
  yield put(alarmsMonitoringBoardSliceActions.nextInitializingStage({ hardRefresh }));
  yield put(
    subscribeAlarmsMonitoringBoardAction({
      target: filters,
      // 建立 WebSocket 连接后查询当前用户关注的告警 ID 集合（异步的）
      // （目前先不等待上一步完成）视为初始化完成
      successActions: [
        // TODO: @Jerry Turn off `hardRefresh` until `hardReset`(resetBoardAction) is ready
        /*hardRefresh ? */ getYourInterestsAction /* : null*/,
        alarmsMonitoringBoardSliceActions.initialized,
        /* hardRefresh ? */ () => launchPointValuesIntervalAction({ idc: filters.idc }) /* : null*/,
      ].filter(Boolean) as ActionCreator<AnyAction>[],
    })
  );
}

export function* getYourInterestsSaga() {
  const { initializing, stage }: SagaReturnType<typeof selectCurrentState> =
    yield select(selectCurrentState);
  if (initializing && stage === 's3/subscribing-alarms') {
    yield put(alarmsMonitoringBoardSliceActions.nextInitializingStage({ hardRefresh: true }));
  }
  const { error, data }: SagaReturnType<typeof fetchYourInterestsAlarmIds> = yield call(
    fetchYourInterestsAlarmIds
  );
  if (error) {
    debug('Failed to fetch your interests alarm ids, error: ', error);
    return;
  }
  yield put(alarmsMonitoringBoardSliceActions.setYourInterests(data.data));
}

export function* mutateYourInterestsSaga({
  type,
  payload: { alarmIds, callback },
}: ReturnType<typeof addAlarmsIntoInterestsAction | typeof removeAlarmsFromInterestsAction>) {
  if (type === addAlarmsIntoInterestsAction.type) {
    // Remote update
    const { error }: SagaReturnType<typeof mutateYourInterestsAlarmIds> = yield call(
      mutateYourInterestsAlarmIds,
      { action: 'ADD', alarmIds }
    );
    if (!error) {
      // Local update
      yield put(alarmsMonitoringBoardSliceActions.addIntoYourInterests(alarmIds));
    }
    if (typeof callback == 'function') {
      yield call(callback, error ?? null);
    }
  } else if (type === removeAlarmsFromInterestsAction.type) {
    // Remote update
    const { error }: SagaReturnType<typeof mutateYourInterestsAlarmIds> = yield call(
      mutateYourInterestsAlarmIds,
      { action: 'DELETE', alarmIds }
    );
    if (!error) {
      // Local update
      yield put(alarmsMonitoringBoardSliceActions.removeFromYourInterests(alarmIds));
    }
    if (typeof callback == 'function') {
      yield call(callback, error ?? null);
    }
  }
}

export function* resetBoardSaga(_action: ReturnType<typeof resetBoardAction>) {
  // TODO: @Jerry should add a `hardReset` action payload
  yield put(terminatePointValuesIntervalAction());
  yield put(unsubscribeAlarmsMonitoringBoardAction());
  yield put(alarmsMonitoringBoardSliceActions.reset());
}

function* getScopesDeviceTypes() {
  const config: SagaReturnType<typeof selectCurrentConfig> = yield select(selectCurrentConfig);
  const configUtil = new ConfigUtil(config);
  const powerSystemDeviceType = configUtil.getOneDeviceType(
    ConfigUtil.constants.deviceTypes.POWER_SYSTEM
  );
  const hvacSystemDeviceType = configUtil.getOneDeviceType(
    ConfigUtil.constants.deviceTypes.HVAC_SYSTEM
  );
  const elvSystemDeviceType = configUtil.getOneDeviceType(
    ConfigUtil.constants.deviceTypes.ELV_SYSTEM
  );

  return { powerSystemDeviceType, hvacSystemDeviceType, elvSystemDeviceType };
}

export function* receiveAlarmsMessageSaga({
  payload: { alarms, forceUpdate },
}: ReturnType<typeof receiveAlarmsMessageAction>) {
  const { powerSystemDeviceType, hvacSystemDeviceType, elvSystemDeviceType } =
    yield getScopesDeviceTypes();
  yield put(
    alarmsMonitoringBoardSliceActions.bulkUpdate({
      alarms,
      deviceTypes: {
        powerSystem: powerSystemDeviceType,
        hvacSystem: hvacSystemDeviceType,
        elvSystem: elvSystemDeviceType,
      },
      forceUpdate,
    })
  );
}

export function* getPointValuesIntervalSaga({
  payload: { idc },
}: ReturnType<typeof launchPointValuesIntervalAction>) {
  const alarms: SagaReturnType<typeof selectAlarms> = yield select(selectAlarms);
  const pointGuids = Object.values(alarms).reduce<Record<string, string>>((mapper, alarmJSON) => {
    if (!mapper[alarmJSON.source.guid]) {
      mapper[alarmJSON.source.guid] = alarmJSON.point.code;
    } else {
      mapper[alarmJSON.source.guid] += `,${alarmJSON.point.code}`;
    }

    return mapper;
  }, {});

  if (Object.keys(pointGuids).length > 0) {
    const { error, data }: SagaReturnType<typeof fetchPointValues> = yield call(fetchPointValues, {
      idc,
      pointGuids,
    });
    if (!error) {
      const _alarms: Array<{
        action: AlarmUpdatesAction;
        alarm: SimpleChangedAlarm;
      }> = [];
      Object.keys(alarms).forEach(id => {
        const alarm = alarms[Number(id)];
        const pointValue = data[alarm.source.guid]?.pointValues[alarm.point.code]?.value;
        if (pointValue !== undefined) {
          _alarms.push({
            action: 'UPDATE',
            alarm: {
              id: alarm.id,
              pointValue,
            },
          });
        }
      });
      if (_alarms.length > 0) {
        const { powerSystemDeviceType, hvacSystemDeviceType, elvSystemDeviceType } =
          yield getScopesDeviceTypes();
        yield put(
          alarmsMonitoringBoardSliceActions.bulkUpdate({
            alarms: _alarms,
            deviceTypes: {
              powerSystem: powerSystemDeviceType,
              hvacSystem: hvacSystemDeviceType,
              elvSystem: elvSystemDeviceType,
            },
          })
        );
      }
    }
  }

  const { timeout } = yield race({
    cancelAction: take(terminatePointValuesIntervalAction.type),
    timeout: delay(10 * 1000),
  });

  if (timeout) {
    yield put(launchPointValuesIntervalAction({ idc }));
  }
}

function* digestPendingUpdates() {
  const pendingUpdates: SagaReturnType<typeof selectPendingUpdates> =
    yield select(selectPendingUpdates);
  if (pendingUpdates.length > 0) {
    const { powerSystemDeviceType, hvacSystemDeviceType, elvSystemDeviceType } =
      yield getScopesDeviceTypes();
    yield put(
      alarmsMonitoringBoardSliceActions.bulkUpdate({
        // 这里不用传 `pendingUpdates`，
        // 因为 `bulkUpdate` 内部已处理
        alarms: [],
        deviceTypes: {
          powerSystem: powerSystemDeviceType,
          hvacSystem: hvacSystemDeviceType,
          elvSystem: elvSystemDeviceType,
        },
        forceUpdate: false,
      })
    );
  }
}

export function* updatePreferencesSaga({
  type,
  payload,
}: ReturnType<
  | typeof updatePreferences
  | typeof updateStatisticsPreferences
  | typeof updateSpecificBoardPreferences
>) {
  // Local update start
  // ------
  if (type === updatePreferences.type) {
    yield put(alarmsMonitoringBoardSliceActions.setPreferences(payload));
    if ('ttsService' in payload) {
      // update local storage cache
      setPreferences({ ttsService: { enabled: payload.ttsService } });
      if (!payload.ttsService) {
        if (env.MONITORING_ALARMS_TTS_SERVICE === 'built-in') {
          ttsService.cancel();
        } else {
          alarmsTtsManager?.pause();
        }
      } else {
        if (env.MONITORING_ALARMS_TTS_SERVICE === 'built-in') {
          // CAN NOT PAUSE OR RESUME `speechSynthesis` HERE!
          // so, I chose to do nothing.
          // ttsService.resume();
        } else {
          alarmsTtsManager?.resume();
        }
      }
    }
    if ('autoRefresh' in payload && payload.autoRefresh === true) {
      yield digestPendingUpdates();
    }
  } else if (type === updateStatisticsPreferences.type) {
    let path: string[];
    if (payload.variant === 'root') {
      path = ['statistics', 'visible'];
    } else {
      path = ['statistics', 'variants', payload.variant, 'visible'];
    }
    yield put(
      alarmsMonitoringBoardSliceActions.setPreferencesByPath({ path, value: payload.visible })
    );
  } else if (type === updateSpecificBoardPreferences.type) {
    if (payload.layout === 'table') {
      yield put(
        alarmsMonitoringBoardSliceActions.setPreferencesByPath({
          path: payload.target ? `table.${payload.target}` : 'table',
          value: payload.preferences,
          strategy: 'mergeDeep',
        })
      );
    } else {
      let variant: BoardVariant | undefined;
      switch (payload.layout) {
        case 'board--by-alarm-levels':
          variant = 'byAlarmLevels';
          break;
        case 'board--by-states':
          variant = 'byStates';
          break;
        case 'board--by-scopes':
          variant = 'byScopes';
          break;
        default:
          break;
      }
      if (variant === undefined) {
        debug(`varaint(${variant}) expected! payload: `, payload);
        return;
      }
      yield put(
        alarmsMonitoringBoardSliceActions.setPreferencesByPath({
          path: payload.target ? `boards.${variant}.${payload.target}` : ['boards', variant],
          value: payload.preferences,
          strategy: 'mergeDeep',
        })
      );
    }
  }

  // Remote update start
  // ------
  const preferencesSnapshot: SagaReturnType<typeof selectPreferencesSnapshot> =
    yield select(selectPreferencesSnapshot);
  const { error }: SagaReturnType<typeof saveUserAlarmsMonitoringBoardPreferences> = yield call(
    saveUserAlarmsMonitoringBoardPreferences,
    preferencesSnapshot
  );
  if (error) {
    debug('Failed to save preferences, error: ', error);
  }
}

export function* resetPreferencesSaga({
  payload: { target, properties },
}: ReturnType<typeof resetPreferencesAction>) {
  const initialSliceState = createInitialState();
  yield all(
    properties.map(property => {
      const path = `${target}.${property}`;
      let fallbackValue: any = null; // eslint-disable-line
      if (target.startsWith('boards.byAlarmLevels') || target.startsWith('boards.byDeviceTypes')) {
        if (property === 'sort') {
          fallbackValue = 'combined-ranking';
        } else if (property === 'layoutColumns.card') {
          fallbackValue = byAlarmLevelsBoardCardLayoutColumnsConfigs;
        } else if (property === 'layoutColumns.table') {
          fallbackValue = defaultBoardTableLayoutColumnsConfigs;
        }
      }
      const defaultValue = get(initialSliceState.preferences, path, fallbackValue);

      return put(
        alarmsMonitoringBoardSliceActions.setPreferencesByPath({
          path,
          value: defaultValue,
        })
      );
    })
  );
  // Remote update start
  // ------
  const preferencesSnapshot: SagaReturnType<typeof selectPreferencesSnapshot> =
    yield select(selectPreferencesSnapshot);
  const { error }: SagaReturnType<typeof saveUserAlarmsMonitoringBoardPreferences> = yield call(
    saveUserAlarmsMonitoringBoardPreferences,
    preferencesSnapshot
  );
  if (error) {
    debug('Failed to reset preferences, error: ', error);
  }
}

export function* acceptAlarmsSaga({
  payload: { alarmIds, callback },
}: ReturnType<typeof acceptAlarmsAction>) {
  const { error }: SagaReturnType<typeof acceptAlarms> = yield call(acceptAlarms, { alarmIds });
  if (!error) {
    const alarmsJSON: SagaReturnType<typeof selectAlarms> = yield select(selectAlarms);
    const alarms: Array<{
      action: AlarmUpdatesAction;
      alarm: Omit<AlarmsMonitoringBoardData, 'type'>;
    }> = [];
    alarmIds.forEach(id => {
      const alarmJSON = alarmsJSON[id] as AlarmJSON | undefined;
      if (!alarmJSON) {
        return;
      }
      const alarm = Alarm.fromJSON(alarmJSON);
      alarm.accept();
      alarms.push({
        action: 'UPDATE',
        alarm: alarm.toApiObject(),
      });
    });
    yield put(receiveAlarmsMessageAction({ alarms, forceUpdate: true }));
  }
  if (typeof callback == 'function') {
    callback(error ?? null);
  }
}

export function* closeAlarmsSaga({
  payload: { alarmIds, reason, descriptions, ticketNumber, isRemovedByClickOnce, callback },
}: ReturnType<typeof closeAlarmsAction>) {
  let error: RequestError | undefined;
  if (isRemovedByClickOnce) {
    const { error: deleteAlarmsByClickOnceError }: SagaReturnType<typeof deleteAlarmsByClickOnce> =
      yield call(deleteAlarmsByClickOnce, {
        alarmIds,
        alarmReason: reason,
        removeDesc: descriptions,
      });
    error = deleteAlarmsByClickOnceError;
  } else {
    const { error: batchRemoveAlarmsError }: SagaReturnType<typeof batchRemoveAlarms> = yield call(
      batchRemoveAlarms,
      {
        alarmIds,
        alarmReason: reason,
        removeDesc: descriptions,
        orderNo: ticketNumber,
      }
    );
    error = batchRemoveAlarmsError;
  }

  if (!error) {
    yield put(
      alarmsMonitoringBoardSliceActions.removeAlarms({
        alarmIds,
        // These alarms will be destroyed by WebSocket (DELETE)messages
        // See `alarms-monitoring-board.action.ts#receiveAlarmsMessageAction`
        destroy: false,
      })
    );

    yield digestPendingUpdates();
  }
  if (typeof callback == 'function') {
    yield call(callback, error ?? null);
  }
}

export function* connectAlarmsWithEventSaga({
  payload: { alarmIds, eventId, deviceGuids, idc, alarmStartTimes, callback },
}: ReturnType<typeof connectAlarmsWithEventAction>) {
  const { error }: SagaReturnType<typeof connectEventWithAlarms> = yield call(
    connectEventWithAlarms,
    {
      id: eventId,
      idc,
      alarmIds,
      deviceGuids,
      alarmStartTimes,
    }
  );
  if (!error) {
    const alarmsJSON: SagaReturnType<typeof selectAlarms> = yield select(selectAlarms);
    const alarms: Array<{
      action: AlarmUpdatesAction;
      alarm: Omit<AlarmsMonitoringBoardData, 'type'>;
    }> = [];
    alarmIds.forEach(id => {
      const alarmJSON = alarmsJSON[id] as AlarmJSON | undefined;
      if (!alarmJSON) {
        return;
      }
      const alarm = Alarm.fromJSON(alarmJSON);
      alarm.connectWithEvent(eventId);
      alarms.push({
        action: 'UPDATE',
        alarm: alarm.toApiObject(),
      });
    });
    yield put(receiveAlarmsMessageAction({ alarms, forceUpdate: true }));
  }
  if (typeof callback == 'function') {
    yield call(callback, error ?? null);
  }
}

/** Watchers */

function* watchCreateBoard() {
  yield takeLatest(createBoardAction.type, createBoardSaga);
}

function* watchGetYourInterests() {
  while (true) {
    yield take(getYourInterestsAction.type);
    yield fork(getYourInterestsSaga);
  }
}

function* watchMutateYourInterests() {
  yield takeEvery(
    [addAlarmsIntoInterestsAction.type, removeAlarmsFromInterestsAction.type],
    mutateYourInterestsSaga
  );
}

function* watchResetBoard() {
  yield takeLatest(resetBoardAction.type, resetBoardSaga);
}

function* watchReceiveAlarmsMessage() {
  yield takeEvery(receiveAlarmsMessageAction.type, receiveAlarmsMessageSaga);
}

function* watchLaunchPointValuesInterval() {
  yield takeLatest(launchPointValuesIntervalAction.type, getPointValuesIntervalSaga);
}

function* watchUpdatePreferences() {
  yield takeEvery(
    [updatePreferences.type, updateStatisticsPreferences.type, updateSpecificBoardPreferences.type],
    updatePreferencesSaga
  );
}

function* watchResetPreferences() {
  yield takeEvery(resetPreferencesAction.type, resetPreferencesSaga);
}

function* watchAcceptAlarms() {
  yield takeLatest(acceptAlarmsAction.type, acceptAlarmsSaga);
}

function* watchCloseAlarms() {
  yield takeLatest(closeAlarmsAction.type, closeAlarmsSaga);
}

function* watchConnectAlarmsWithEvent() {
  yield takeLatest(connectAlarmsWithEventAction.type, connectAlarmsWithEventSaga);
}

export default [
  fork(watchCreateBoard),
  fork(watchGetYourInterests),
  fork(watchMutateYourInterests),
  fork(watchResetBoard),
  fork(watchReceiveAlarmsMessage),
  fork(watchLaunchPointValuesInterval),
  fork(watchUpdatePreferences),
  fork(watchResetPreferences),
  fork(watchAcceptAlarms),
  fork(watchCloseAlarms),
  fork(watchConnectAlarmsWithEvent),
];
