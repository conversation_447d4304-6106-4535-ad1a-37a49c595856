import { expectSaga } from 'redux-saga-test-plan';
import * as matchers from 'redux-saga-test-plan/matchers';

import { Alarm } from '@manyun/monitoring.model.alarm';
import { batchRemoveAlarms } from '@manyun/monitoring.service.batch-remove-alarms';
import { mutateYourInterestsAlarmIds } from '@manyun/monitoring.service.mutate-your-interests-alarm-ids';
import { subscribeAlarmsMonitoringBoardAction } from '@manyun/monitoring.state.subscriptions';
import { useRemoteMock } from '@manyun/service.request';

import {
  CreateActionPayload,
  addAlarmsIntoInterestsAction,
  alarmsMonitoringBoardSliceActions,
  closeAlarmsAction,
  createBoardAction,
  getYourInterestsAction,
  receiveAlarmsMessageAction,
  removeAlarmsFromInterestsAction,
} from './alarms-monitoring-board.action';
import {
  closeAlarmsSaga,
  createBoardSaga,
  getYourInterestsSaga,
  mutateYourInterestsSaga, // resetBoardSaga,
  receiveAlarmsMessageSaga,
} from './alarms-monitoring-board.saga';
import {
  AlarmId,
  AlarmUpdatesAction,
  alarmsMonitoringBoardSlice,
  createInitialState,
} from './alarms-monitoring-board.slice';
import type { AlarmsMonitoringBoardSliceState } from './alarms-monitoring-board.slice';

let webMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
});
afterAll(() => {
  webMockOff();
});

test('should handle alarms being updated', () => {
  const mockAlarms: Array<{ action: AlarmUpdatesAction; alarm: Alarm }> = [
    {
      action: 'ADD',
      alarm: new Alarm(
        0,
        {
          deviceLabel: null,
          deviceType: '10901',
          guid: 'EC06',
          variant: 'space',
          dimension: 'IDC',
          name: 'EC06',
          spaceGuidMap: {
            idcTag: 'EC06',
            blockTag: null,
            roomTag: null,
            columnTag: null,
            gridTag: null,
          },
          roomType: null,
        },
        { code: 'TRIGGER', name: '触发中' },
        '',
        '1',
        { code: 'ERROR', name: '告警' },
        { code: 'ACTIVE', name: '激活' },
        {
          code: '1001000',
          name: '温度',
          dataType: {
            code: 'AI',
            name: '模拟量读点',
          },
          validLimits: null,
          unit: '℃',
        },
        { current: 25, snapshot: '26℃' },
        { code: 'UPPER', name: '超上限' },
        // 把创建时间往前掰 1s 是为了确保计算 `statistics.byCreatedAt`
        // 时，将这条告警统计到第一个 `timeSpan` 中去
        Date.now() - 1000,
        Date.now(),
        0,
        0,
        0,
        0,
        0,
        null,
        { id: 0, threshold: '<25℃' },
        null,
        null,
        0,
        null,
        Date.now() - 1000,
        null,
        null,
        null
      ),
    },
  ];
  const previousState = createInitialState();
  expect(
    alarmsMonitoringBoardSlice.reducer(
      previousState,
      alarmsMonitoringBoardSliceActions.bulkUpdate({ alarms: mockAlarms })
    )
  ).toEqual<AlarmsMonitoringBoardSliceState>({
    ...previousState,
    statistics: {
      byAlarmLevels: {
        [mockAlarms[0].alarm.level]: 1,
      },
      byDeviceTypes: {
        [mockAlarms[0].alarm.source.deviceType]: 1,
      },
      byRoomTypes: {},
      byCreatedAt: [
        [previousState.statistics.byCreatedAt[0][0], 1],
        ...previousState.statistics.byCreatedAt.slice(1),
      ],
    },
    ids: [mockAlarms[0].alarm.id],
    alarms: {
      [mockAlarms[0].alarm.id]: mockAlarms[0].alarm,
    },
    alarmsGroups: {
      ...previousState.alarmsGroups,
      byAlarmLevels: {
        [mockAlarms[0].alarm.level]: [mockAlarms[0].alarm.id],
      },
      // byDeviceTypes: {
      //   [mockAlarms[0].alarm.source.deviceType]: [mockAlarms[0].alarm.id],
      // },
      bySpace: {
        root: [mockAlarms[0].alarm.id],
      },
      byStates: {
        ...previousState.alarmsGroups.byStates,
        unaccepted: [mockAlarms[0].alarm.id],
        'unaccepted-n-unrecovered': [mockAlarms[0].alarm.id],
      },
    },
  });
});

test('should create a alarms monitoring board', () => {
  const filters: CreateActionPayload['filters'] = {
    idc: 'EC06',
  };
  return expectSaga(createBoardSaga, createBoardAction({ filters }))
    .withState({ [alarmsMonitoringBoardSlice.name]: createInitialState() })
    .put(alarmsMonitoringBoardSliceActions.initialize())
    .put(
      subscribeAlarmsMonitoringBoardAction({
        target: filters,
        successActions: [getYourInterestsAction, alarmsMonitoringBoardSliceActions.initialized],
      })
    )
    .run();
});

test('should bulk update received alarms', () => {
  return expectSaga(receiveAlarmsMessageSaga, receiveAlarmsMessageAction({ alarms: [] }))
    .withState({ [alarmsMonitoringBoardSlice.name]: createInitialState() })
    .put(alarmsMonitoringBoardSliceActions.bulkUpdate({ alarms: [] }))
    .run();
});

test('should get 11 your interested alarms', () => {
  return expectSaga(getYourInterestsSaga)
    .put(alarmsMonitoringBoardSliceActions.setYourInterests([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]))
    .run();
});

test('should add alarms(50,51,52) into your interests alarms', () => {
  const alarmIds: AlarmId[] = [50, 51, 52];

  return expectSaga(mutateYourInterestsSaga, addAlarmsIntoInterestsAction({ alarmIds }))
    .provide([[matchers.call.fn(mutateYourInterestsAlarmIds), { error: undefined }]])
    .call(mutateYourInterestsAlarmIds, { action: 'ADD', alarmIds })
    .put(alarmsMonitoringBoardSliceActions.addIntoYourInterests(alarmIds))
    .run();
});

test('should remove alarms(50,51,52) from your interests alarms', () => {
  const alarmIds: AlarmId[] = [50, 51, 52];

  return expectSaga(mutateYourInterestsSaga, removeAlarmsFromInterestsAction({ alarmIds }))
    .provide([[matchers.call.fn(mutateYourInterestsAlarmIds), { error: undefined }]])
    .call(mutateYourInterestsAlarmIds, { action: 'DELETE', alarmIds })
    .put(alarmsMonitoringBoardSliceActions.removeFromYourInterests(alarmIds))
    .run();
});

test('should close alarms(50,51,52)', () => {
  const alarmIds: AlarmId[] = [50, 51, 52];

  return expectSaga(
    closeAlarmsSaga,
    closeAlarmsAction({
      alarmIds,
      reason: 'I just wanna close these alarms',
      descriptions: 'blah blah blah...',
    })
  )
    .provide([[matchers.call.fn(batchRemoveAlarms), { error: undefined }]])
    .put(alarmsMonitoringBoardSliceActions.removeAlarms({ alarmIds, destroy: false }))
    .run();
});
