---
description: 'AlarmsMonitoringBoard redux(slice state, selector, action, saga) 组件.'
labels: ['redux', 'slice-state', 'alarms']
---

## 使用

1. 集成 `reducer(slice state)`

```js
import alarmsMonitoringBoardSliceReducer from '@manyun/monitoring.state.alarms-monitoring-board';

const rootReducer = {
  ...alarmsMonitoringBoardSliceReducer,
  // other slice reducers...
};
```

2. 集成 `saga`

```js
import { all } from 'redux-saga/effects';
import { alarmsMonitoringBoardWatchers } from '@manyun/monitoring.state.alarms-monitoring-board';

const function* rootSaga() {
  yield all(
    ...alarmsMonitoringBoardWatchers,
    // other sagas...
  );
};
```

### 看板

### 按序渲染看板的列

```tsx
import {
  getAlarmsMonitoringBoardLocales,
  selectBoardPreferences,
} from '@manyun/monitoring.state.alarms-monitoring-board';

const locales = getAlarmsMonitoringBoardLocales();
const { boardColumns } = useSelecotr(selectBoardPreferences('board--by-scopes'));
boardColumns.map(boardColumn => <Card title={locales.boards.byScopes[boardColumn]} />);
```

### 更新偏好配置

#### 按布局更新部分的偏好配置

```ts
// 仅更新专业类型看板下的“电力”列的布局
dispatch(
  updateSpecificBoardPreferences({
    layout: 'board--by-scopes',
    preferences: {
      'power-system': {
        layout: 'mini',
      },
    },
  })
);

// 仅更新专业类型看板下的列的渲染顺序
updateSpecificBoardPreferences({
  layout: 'board--by-scopes',
  preferences: {
    boardColumns: ['hvac-system', 'elv-system', 'space', 'power-system'],
  },
});
```

### 格式化 statistics.byCreatedAt#timespan

```ts
import { timeSpanUtil } from '@manyun/monitoring.state.alarms-monitoring-board';

const yesterday = dayjs('2022-06-21 19:30').valueOf();
const today = dayjs('2022-06-22 03:30').valueOf();
timeSpanUtil.format([yesterday, today]); // "昨日19点-3点"
```

## 在 Composition 中测试

### 模拟告警数据的新增、更新和删除

```tsx
// some-component.composition.tsx

import { useDispatch } from 'react-redux';
import { useRemoteMock } from '@manyun/service.request';
import {
  timeSpanUtil,
  receiveAlarmsMessageAction,
  selectStatistics,
} from '@manyun/monitoring.state.alarms-monitoring-board';

export function BasicSomeComponent() {
  const [ready, setReady] = React.useState(false);
  const dispatch = useDispatch();

  React.useEffect(() => {
    const mockOff = useRemoteMock('web');
    (async () => {
      // 依赖 `fetchAlarms` 的 mock数据：http://172.16.0.17:13000/project/86/interface/api/2224
      const { error, data } = await fetchAlarms({
        pageNum: 1,
        pageSize: 10,
        idcTag: 'EC01',
      });
      if (error) {
        console.error(error);
        return;
      }
      dispatch(
        receiveAlarmsMessageAction({
          alarms: data.data.map(alarm => ({
            action: 'ADD',
            // 为了防止 `alarm` 对象被 `redux` 改为 `immutable`
            alarm: Alarm.parseJSON(alarm.toJSON()),
          })),
        })
      );
      // 15s 后更新这些数据
      window.setTimeout(() => {
        dispatch(
          receiveAlarmsMessageAction({
            alarms: data.data.map(alarm => {
              const copy = Alarm.parseJSON(alarm.toJSON());
              copy.accept();

              return {
                action: 'UPDATE',
                alarm: Alarm.parseJSON(copy.toJSON()),
              };
            }),
          })
        );
      }, 15 * 1000);
      // 30s 后删除这堆数据
      window.setTimeout(() => {
        dispatch(
          receiveAlarmsMessageAction({
            alarms: data.data.map(alarm => ({
              action: 'DEL',
              alarm: Alarm.parseJSON(alarm.toJSON()),
            })),
          })
        );
      }, 30 * 1000);
    })();

    return () => {
      mockOff();
    };
  }, []);

  const byCreatedAtStatistics = useSelector(selectStatistics('byCreatedAt'));

  return (
    <div>
      {byCreatedAtStatistics.map(([[from, to], count]) => (
        <p key={`${from}-${to}`}>
          {timeSpanUtil.format([from, to])}: {count}
        </p>
      ))}
    </div>
  );
}
```
