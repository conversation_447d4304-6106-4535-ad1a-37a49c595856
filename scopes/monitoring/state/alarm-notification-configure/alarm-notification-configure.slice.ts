import { createSlice } from '@reduxjs/toolkit';
import type { CaseReducer, PayloadAction } from '@reduxjs/toolkit';
import shortid from 'shortid';

import { NotificationConfigureItemJSON } from '@manyun/monitoring.model.notification-configure-item';
import { NotificationItemObjectJSON } from '@manyun/monitoring.model.notification-configure-item-object';

export type AlarmNotificationConfigureItemProperty = Partial<
  Pick<NotificationConfigureItemJSON, 'id' | 'name' | 'type' | 'rule' | 'webhook'>
> & {
  /**告警通报对象 */
  notificationObjectEntities: Record<string, NotificationItemObjectJSON>;
  notificationObjectCodes: string[];
};

export type MutateAlarmNotificationConfigureProperty = {
  levelCode: string | null /** 告警级别code **/;
  alarmNotificationItemEntites: Record<number | string, AlarmNotificationConfigureItemProperty>;
  alarmNotificationItemIds: (number | string)[];
  /**折叠面板 activeKey */
  activePanelKeys?: (string | number)[];
};

export type MutateType = 'create' | 'update';

export type CurrentMutateConfigure = {
  type: MutateType;
  alarmLevelCode: string;
  loading?: boolean;
} | null;

export type SetMutateNotificationItemFields = {
  id: number | string /**通报项id */;
} & Partial<Omit<AlarmNotificationConfigureItemProperty, 'id'>>;

export type AlarmNotificationConfigureSliceState = {
  /**当前正在操作的通报配置 */
  currentMutateConfigure: CurrentMutateConfigure;
  /**
   *  缓存不同告警级别 新增时未提交的数据
   *  eg: Record<"1", { levelCode: '1', alarmNotificationItemIds: [], alarmNotificationItemEntites: {}}>
   */
  create: Record<string, MutateAlarmNotificationConfigureProperty>;
  update: Record<string, MutateAlarmNotificationConfigureProperty>;
};

type AlarmNotificationConfigureSliceCaseReducers = {
  setCurrentMutateConfigureFields: CaseReducer<
    AlarmNotificationConfigureSliceState,
    PayloadAction<CurrentMutateConfigure>
  >;
  setMutateSubmitStart: CaseReducer<AlarmNotificationConfigureSliceState>;
  setMutateSubmitEnd: CaseReducer<AlarmNotificationConfigureSliceState>;
  /**
   * 编辑通报配置
   */
  setMutateNotificationConfigure: CaseReducer<
    AlarmNotificationConfigureSliceState,
    PayloadAction<Partial<MutateAlarmNotificationConfigureProperty>>
  >;
  /**
   * 新增一条告警通报项
   */
  addMutateNotificationItem: CaseReducer<
    AlarmNotificationConfigureSliceState,
    PayloadAction<AlarmNotificationConfigureItemProperty | undefined>
  >;
  /**
   *  编辑某一条告警通报项
   */
  setMutateNotificationItemFields: CaseReducer<
    AlarmNotificationConfigureSliceState,
    PayloadAction<SetMutateNotificationItemFields>
  >;
  /**
   * 删除某一条告警通报项目
   */
  deleteMutateNotificationItem: CaseReducer<
    AlarmNotificationConfigureSliceState,
    PayloadAction<{ id: number | string }>
  >;

  /**
   * 清空当前告警级别下的通报项配置, 页面取消操作
   */
  resetMutateNotificationItems: CaseReducer<AlarmNotificationConfigureSliceState>;
};

export const alarmNotificationConfigureSlice = createSlice<
  AlarmNotificationConfigureSliceState,
  AlarmNotificationConfigureSliceCaseReducers,
  'monitoring.alarm-notification-configure'
>({
  name: 'monitoring.alarm-notification-configure',
  initialState: {
    currentMutateConfigure: null,
    create: {},
    update: {},
  },
  reducers: {
    setCurrentMutateConfigureFields: (state, { payload }) => {
      if (payload) {
        state.currentMutateConfigure = { ...state.currentMutateConfigure, ...payload };
      }
    },

    setMutateSubmitStart: state => {
      if (state.currentMutateConfigure) {
        state.currentMutateConfigure = {
          ...state.currentMutateConfigure,
          loading: true,
        };
      }
    },
    setMutateSubmitEnd: state => {
      if (state.currentMutateConfigure) {
        state.currentMutateConfigure = {
          ...state.currentMutateConfigure,
          loading: false,
        };
      }
    },
    setMutateNotificationConfigure: (state, { payload }) => {
      if (state.currentMutateConfigure) {
        const { alarmLevelCode, type } = state.currentMutateConfigure;
        if (state[type][alarmLevelCode]) {
          state[type][alarmLevelCode] = {
            ...state[type][alarmLevelCode],
            ...payload,
          };
          return;
        }
        state[type] = {
          ...state[type],
          [alarmLevelCode]: {
            levelCode: alarmLevelCode,
            alarmNotificationItemIds: [],
            alarmNotificationItemEntites: {},
            ...payload,
          },
        };
      }
    },
    addMutateNotificationItem: (state, { payload }) => {
      const uniqId = payload ? (payload.id as string | number) : shortid();
      const initializeItem: AlarmNotificationConfigureItemProperty = payload
        ? payload
        : {
            id: uniqId,
            notificationObjectCodes: [],
            notificationObjectEntities: {},
            webhook: false,
          };

      if (state.currentMutateConfigure) {
        const { alarmLevelCode, type } = state.currentMutateConfigure;
        if (state[type][alarmLevelCode]) {
          state[type][alarmLevelCode].alarmNotificationItemIds.push(uniqId);
          state[type][alarmLevelCode].alarmNotificationItemEntites[uniqId] = initializeItem;
          state[type][alarmLevelCode].activePanelKeys?.push(uniqId);
          return;
        }

        state[type] = {
          ...state[type],
          [alarmLevelCode]: {
            levelCode: alarmLevelCode,
            alarmNotificationItemIds: [uniqId],
            alarmNotificationItemEntites: { [uniqId]: initializeItem },
            activePanelKeys: [uniqId],
          },
        };
      }
    },
    setMutateNotificationItemFields: (state, { payload: { id, ...fields } }) => {
      if (state.currentMutateConfigure) {
        const { alarmLevelCode, type } = state.currentMutateConfigure;
        if (
          state[type][alarmLevelCode] &&
          state[type][alarmLevelCode].alarmNotificationItemEntites[id]
        ) {
          state[type][alarmLevelCode].alarmNotificationItemEntites[id] = {
            ...state[type][alarmLevelCode].alarmNotificationItemEntites[id],
            ...fields,
          };
        }
      }
    },

    deleteMutateNotificationItem: (state, { payload: { id } }) => {
      if (state.currentMutateConfigure) {
        const { alarmLevelCode, type } = state.currentMutateConfigure;
        if (state[type][alarmLevelCode]) {
          if (state[type][alarmLevelCode].alarmNotificationItemEntites[id]) {
            delete state[type][alarmLevelCode].alarmNotificationItemEntites[id];
          }
          state[type][alarmLevelCode].alarmNotificationItemIds = state[type][
            alarmLevelCode
          ].alarmNotificationItemIds.filter(uniq => uniq !== id);
        }
      }
    },

    resetMutateNotificationItems: state => {
      if (state.currentMutateConfigure) {
        const { alarmLevelCode, type } = state.currentMutateConfigure;
        if (state[type][alarmLevelCode]) {
          delete state[type][alarmLevelCode];
        }
      }
    },
  },
});
