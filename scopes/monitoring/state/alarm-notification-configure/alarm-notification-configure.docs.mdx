---
description: 'AlarmNotificationConfigure redux(slice state, selector, action, saga) 组件.'
labels: ['redux', 'slice-state', 'label3']
---

## 使用

1. 集成 `reducer(slice state)`

```js
import alarmNotificationConfigureSliceReducer from '@manyun/[scope].state.alarm-notification-configure';

const rootReducer = {
  ...alarmNotificationConfigureSliceReducer,
  // other slice reducers...
};
```

2. 集成 `saga`

```js
import { all } from 'redux-saga/effects';
import { alarmNotificationConfigureWatchers } from '@manyun/[scope].state.alarm-notification-configure';

const function* rootSaga() {
  yield all(
    ...alarmNotificationConfigureWatchers,
    // other sagas...
  );
};
```