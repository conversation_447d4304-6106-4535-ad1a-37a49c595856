import { alarmNotificationConfigureSlice } from './alarm-notification-configure.slice';
import type { AlarmNotificationConfigureSliceState } from './alarm-notification-configure.slice';

type StoreState = {
  [alarmNotificationConfigureSlice.name]: AlarmNotificationConfigureSliceState;
};

export const selectCurrentMutateConigure = (storeState: StoreState) => {
  const { currentMutateConfigure } = storeState[alarmNotificationConfigureSlice.name];

  return {
    type: currentMutateConfigure?.type,
    alarmLevelCode: currentMutateConfigure?.alarmLevelCode,
    loading: currentMutateConfigure?.loading,
  };
};

export const selectMutateNotificationConfigure = (storeState: StoreState) => {
  let _params = storeState[alarmNotificationConfigureSlice.name].currentMutateConfigure;
  if (
    !_params ||
    !storeState[alarmNotificationConfigureSlice.name][_params.type][_params.alarmLevelCode]
  ) {
    return {
      levelCode: null,
      alarmNotificationItemEntites: {},
      alarmNotificationItemIds: [],
    };
  }
  return storeState[alarmNotificationConfigureSlice.name][_params.type][_params.alarmLevelCode];
};
