import { alarmNotificationConfigureSlice } from './alarm-notification-configure.slice';
import type { AlarmNotificationConfigureSliceState } from './alarm-notification-configure.slice';

test('should return the initial state', () => {
  return expect(
    alarmNotificationConfigureSlice.reducer(undefined, {} as any)
  ).toEqual<AlarmNotificationConfigureSliceState>({
    currentMutateConfigure: null,
    create: {},
    update: {},
  });
});
