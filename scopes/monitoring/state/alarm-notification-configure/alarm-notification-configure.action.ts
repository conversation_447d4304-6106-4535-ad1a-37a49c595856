import { createAction } from '@reduxjs/toolkit';

import { alarmNotificationConfigureSlice } from './alarm-notification-configure.slice';

const prefix = alarmNotificationConfigureSlice.name;

export const alarmNotificationConfigureSliceActions = alarmNotificationConfigureSlice.actions;

export const mutateAlarmNotificationConfigureAction = createAction<
  | {
      callback?: () => void;
    }
  | undefined
>(prefix + '/MUTATE_ALARM_NOTIFICATION-CONFIGURE');
