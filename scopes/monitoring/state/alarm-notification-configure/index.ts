import alarmNotificationConfigureWatchers from './alarm-notification-configure.saga';
import { alarmNotificationConfigureSlice } from './alarm-notification-configure.slice';

export type {
  AlarmNotificationConfigureSliceState,
  AlarmNotificationConfigureItemProperty,
  CurrentMutateConfigure,
  MutateAlarmNotificationConfigureProperty,
  SetMutateNotificationItemFields,
} from './alarm-notification-configure.slice';
export * from './alarm-notification-configure.action';
export * from './alarm-notification-configure.selector';
export { alarmNotificationConfigureWatchers };
export default {
  [alarmNotificationConfigureSlice.name]: alarmNotificationConfigureSlice.reducer,
};

// dev use only
// ------
export { createCompositionWrapper } from './alarm-notification-configure.composition-wrapper.creator';
