import { call, fork, put, select, takeLatest } from 'redux-saga/effects';
import type { SagaReturnType } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { createAlarmNotificationConfiguration } from '@manyun/monitoring.service.create-alarm-notification-configuration';
import type { SvcQuery } from '@manyun/monitoring.service.update-alarm-notification-configuration';
import { updateAlarmNotificationConfiguration } from '@manyun/monitoring.service.update-alarm-notification-configuration';

import {
  alarmNotificationConfigureSliceActions,
  mutateAlarmNotificationConfigureAction,
} from './alarm-notification-configure.action';
import {
  selectCurrentMutateConigure,
  selectMutateNotificationConfigure,
} from './alarm-notification-configure.selector';

/** Workers */

export function* mutateAlarmNotificationConfigureSaga({
  payload,
}: ReturnType<typeof mutateAlarmNotificationConfigureAction>) {
  const { type, alarmLevelCode }: SagaReturnType<typeof selectCurrentMutateConigure> = yield select(
    selectCurrentMutateConigure
  );
  if (type && alarmLevelCode) {
    yield put(alarmNotificationConfigureSliceActions.setMutateSubmitStart());

    const {
      levelCode,
      alarmNotificationItemEntites,
      alarmNotificationItemIds,
    }: SagaReturnType<typeof selectMutateNotificationConfigure> = yield select(
      selectMutateNotificationConfigure
    );

    let response:
      | SagaReturnType<typeof createAlarmNotificationConfiguration>
      | SagaReturnType<typeof updateAlarmNotificationConfiguration>;

    let svcD: SvcQuery = {
      levelCode: levelCode ?? '',
      items: alarmNotificationItemIds
        .map(id => alarmNotificationItemEntites[id])
        .map(item => ({
          id: item.id!,
          name: item.name!,
          type: item.type!,
          rule: item.rule!,
          webhook: item.webhook!,
          notificationObjects: item.notificationObjectCodes.map(
            code => item.notificationObjectEntities[code]
          ),
        })),
    };
    if (type === 'create') {
      response = yield call<typeof createAlarmNotificationConfiguration>(
        createAlarmNotificationConfiguration,
        svcD
      );
    } else {
      response = yield call<typeof updateAlarmNotificationConfiguration>(
        updateAlarmNotificationConfiguration,
        svcD
      );
    }
    yield put(alarmNotificationConfigureSliceActions.setMutateSubmitEnd());

    if (response.error) {
      message.error(response.error.message);
      return;
    }

    yield put(alarmNotificationConfigureSliceActions.resetMutateNotificationItems());
    message.success('操作成功！');
    /**跳转至列表页面 */
    if (payload?.callback) {
      payload?.callback();
    }
  }
}

/** Watchers */

function* watchMutateAlarmNotificationConfigure() {
  yield takeLatest(
    mutateAlarmNotificationConfigureAction.type,
    mutateAlarmNotificationConfigureSaga
  );
}

export default [fork(watchMutateAlarmNotificationConfigure)];
