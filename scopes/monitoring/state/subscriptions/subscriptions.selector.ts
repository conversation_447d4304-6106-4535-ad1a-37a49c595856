import uniq from 'lodash.uniq';

import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

import { subscriptionsSlice } from './subscriptions.slice.js';
import type { SubscriptionsSliceState } from './subscriptions.slice.js';

type StoreState = {
  [subscriptionsSlice.name]: SubscriptionsSliceState;
};

export const getIsRunning = (storeState: StoreState) =>
  storeState[subscriptionsSlice.name].isRunning;
export const getSubscriptions = (storeState: StoreState) => {
  const {
    realtimeDataSubscriptions,
    realtimeDataSubscriptionsBlockGuidMapper,
    alarmsDataSubscriptions,
    alarmsDataSubscriptionsBlockGuidMapper,
  } = storeState[subscriptionsSlice.name];
  const subscriptions: {
    type: string;
    blockGuid: string;
    deviceGuids: string[];
  }[] = [];
  if (realtimeDataSubscriptions) {
    Object.keys(realtimeDataSubscriptions)
      .reduce<{ blockGuid: string; deviceGuids: string[] }[]>((acc, moduleId) => {
        const blockGuid = realtimeDataSubscriptionsBlockGuidMapper[moduleId];
        if (!blockGuid) {
          return acc;
        }
        const deviceGuids = realtimeDataSubscriptions[moduleId];
        const { idc } = getSpaceGuidMap(blockGuid);
        // Note
        // 因为后端对于同一个用户的订阅是覆盖的，以最后一次订阅为准，
        // 所以需要按机房合并成一次订阅，因为现在不存在多个机房同时订阅的场景
        const existingIdx = acc.findIndex(target => target.blockGuid === idc!);
        if (existingIdx > -1) {
          acc[existingIdx].deviceGuids = [...acc[existingIdx].deviceGuids, ...deviceGuids];
        } else {
          acc.push({
            blockGuid: idc!,
            deviceGuids,
          });
        }

        return acc;
      }, [])
      .forEach(({ blockGuid, deviceGuids }) => {
        subscriptions.push({
          type: 'realtime-data',
          blockGuid,
          deviceGuids: uniq(deviceGuids),
        });
      });
  }
  if (alarmsDataSubscriptions) {
    Object.keys(alarmsDataSubscriptions)
      .reduce<{ blockGuid: string; deviceGuids: string[] }[]>((acc, moduleId) => {
        const blockGuid = alarmsDataSubscriptionsBlockGuidMapper[moduleId];
        if (!blockGuid) {
          return acc;
        }
        const { idc } = getSpaceGuidMap(blockGuid);
        const deviceGuids = alarmsDataSubscriptions[moduleId];
        const existingIdx = acc.findIndex(target => target.blockGuid === idc);
        if (existingIdx > -1) {
          acc[existingIdx].deviceGuids = [...acc[existingIdx].deviceGuids, ...deviceGuids];
        } else {
          acc.push({
            blockGuid: idc!,
            deviceGuids,
          });
        }

        return acc;
      }, [])
      .forEach(({ blockGuid, deviceGuids }) => {
        subscriptions.push({
          type: 'alarms-data',
          blockGuid,
          deviceGuids: uniq(deviceGuids),
        });
      });
  }

  return subscriptions;
};
export const getShouldSubscribe = (storeState: StoreState) =>
  !!storeState[subscriptionsSlice.name].realtimeDataSubscriptions ||
  !!storeState[subscriptionsSlice.name].alarmsDataSubscriptions;
export const getMonitoringData = (storeState: StoreState) => {
  const { devicesRealtimeData, devicesAlarmsData } = storeState[subscriptionsSlice.name];

  return {
    devicesRealtimeData,
    devicesAlarmsData,
  };
};

export const selectAlarmsMonitoringBoardSubscriptions = (storeState: StoreState) =>
  storeState[subscriptionsSlice.name].alarmsMonitoringBoard.subscription;

export const selectNotificationsMessages = (storeState: StoreState) =>
  storeState[subscriptionsSlice.name].notifications;
