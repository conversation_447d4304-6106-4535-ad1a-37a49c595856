import * as StompJs from '@stomp/stompjs';
import type { StompHeaders } from '@stomp/stompjs';
import noop from 'lodash.noop';
import SockJs from 'sockjs-client';

import { env } from '@manyun/dc-brain.app-env.react-app-envs';

export type ClientConfig = {
  url: string;
  connectHeaders?: StompHeaders;
};

export async function createWebSocketClient({ url, connectHeaders }: ClientConfig) {
  const client = new StompJs.Client({
    webSocketFactory: () => new SockJs(url),
    connectHeaders,
    reconnectDelay: 5 * 1000,
    heartbeatIncoming: 4 * 1000,
    heartbeatOutgoing: 4 * 1000,
    splitLargeFrames: true,
    onWebSocketClose: env.__DEV_MODE__ ? console.error : noop,
  });

  client.onStompError = function (frame) {
    // Will be invoked in case of error encountered at Broker
    // Bad login/passcode typically will cause an error
    // Complaint brokers will set `message` header with a brief message. Body may contain details.
    // Compliant brokers will terminate the connection after any error
    // eslint-disable-next-line no-console
    console.log('Broker reported error: ' + frame.headers['message']);
    // eslint-disable-next-line no-console
    console.log('Additional details: ' + frame.body);
  };

  return client;
}
