import notificationsSubscriptionsWatchers from './subscriptions.saga.js';
import { subscriptionsSlice } from './subscriptions.slice.js';
import realtimeDataSubscriptionsWatchers from './variants/realtime-data-subscriptions.saga.js';

const subscriptionsWatchers = [
  ...realtimeDataSubscriptionsWatchers,
  ...notificationsSubscriptionsWatchers,
];

export { alarmTransmissionStateLocale } from './notifications-websocket-client.js';
export { SUBSCRIPTIONS_MODE } from './subscriptions.constant.js';
export * from './subscriptions.action.js';
export * from './subscriptions.selector.js';
export { subscriptionsWatchers };
export default {
  [subscriptionsSlice.name]: subscriptionsSlice.reducer,
};
export type { SubscriptionsSliceState } from './subscriptions.slice.js';
export * from './subscriptions.type.js';
