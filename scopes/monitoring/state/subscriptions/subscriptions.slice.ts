import { createSlice } from '@reduxjs/toolkit';
import type { CaseReducer, PayloadAction } from '@reduxjs/toolkit';
import set from 'lodash.set';

import type { BackendRealtimePointData } from '@manyun/monitoring.model.monitoring-data';
import { BackendPointDataStatus } from '@manyun/monitoring.model.monitoring-data';
import type { PointDataState } from '@manyun/monitoring.model.point';

import type { BackendAlarmsDataIncomingMessage } from './notifications-websocket-client.js';
import type {
  AlarmsMonitoringBoardDataSubscriptionsTarget,
  ModuleId,
} from './subscriptions.type.js';

type BlockGuid = string;
type DeviceGuid = string;
type PointCode = string;
type PointsAlarmsCount = Record<
  PointCode,
  {
    ERROR: number;
    WARN: number;
  }
>;
type PointValueMap = Record<
  PointCode,
  {
    value: number;
    status: PointDataState;
  }
>;

type DevicesRealtimeData = Record<
  DeviceGuid,
  {
    lastUpdatedAt: number;
    status: PointDataState | null;
    pointValueMap: PointValueMap;
  }
>;

const alarmsDataStateMapper: Record<BackendPointDataStatus, PointDataState> = {
  [BackendPointDataStatus.SocketOff]: 'comms-interrupted',
  [BackendPointDataStatus.CollectorSocketOff]: 'comms-interrupted',
  [BackendPointDataStatus.DataException]: 'comms-interrupted',
  [BackendPointDataStatus.Normal]: 'default',
  [BackendPointDataStatus.Alarm]: 'alarm',
  [BackendPointDataStatus.OutOfRange]: 'out-of-range',
  [BackendPointDataStatus.Warning]: 'warning',
};

export type SubscriptionsSliceState = {
  /**
   * WebSocket connection active indicator.
   *
   * @defaultValue `false`
   */
  isRunning: boolean;

  /**
   * To cache the `Device GUIDs` for Realtime Data Subscription.
   * It's grouped by `Module ID`, then grouped by `Block GUID`.
   *
   * @example
   * ```json
   * {
   *   "mock-module-id-01": ["mock-device-guid-01", "mock-device-guid-02"]
   * }
   * ```
   */
  realtimeDataSubscriptions: Record<ModuleId, DeviceGuid[]> | null;
  realtimeDataSubscriptionsBlockGuidMapper: Record<ModuleId, BlockGuid | null>;
  devicesRealtimeData: DevicesRealtimeData | null;

  alarmsDataSubscriptions: Record<ModuleId, DeviceGuid[]> | null;
  alarmsDataSubscriptionsBlockGuidMapper: Record<ModuleId, BlockGuid | null>;
  /**
   * @deprecated
   *
   * 需要等待所有引用 `devicesAlarmsData` 的地方全部重构完，
   * 然后将 `devicesAlarmsData` 相关代码删除
   */
  devicesAlarmsData: Record<
    DeviceGuid,
    | {
        lastUpdatedAt: number;
        count: {
          ERROR: number;
          WARN: number;
        };
        pointsCount: PointsAlarmsCount;
      }
    | undefined
  > | null;

  alarmsMonitoringBoard: {
    subscription: AlarmsMonitoringBoardDataSubscriptionsTarget | null;
  };

  notifications: {
    /**
     * an `id: entity` object.
     */
    entities: Record<string, BackendAlarmsDataIncomingMessage>;

    /**
     * an `id` array that exist in `entities`.
     */
    ids: BackendAlarmsDataIncomingMessage['id'][];
  };
};

type SubscriptionsSliceCaseReducers = {
  launch: CaseReducer<SubscriptionsSliceState, PayloadAction<undefined>>;
  addRealtimeDataSubscription: CaseReducer<
    SubscriptionsSliceState,
    PayloadAction<{
      moduleId: ModuleId;
      blockGuid: BlockGuid;
      deviceGuids: DeviceGuid[];
    }>
  >;
  removeRealtimeDataSubscriptionByModuleId: CaseReducer<
    SubscriptionsSliceState,
    PayloadAction<{
      moduleId: ModuleId;
    }>
  >;
  receivedRealtimeData: CaseReducer<
    SubscriptionsSliceState,
    PayloadAction<BackendRealtimePointData>
  >;
  addAlarmsDataSubscription: CaseReducer<
    SubscriptionsSliceState,
    PayloadAction<{
      moduleId: ModuleId;
      blockGuid: BlockGuid;
      deviceGuids: DeviceGuid[];
    }>
  >;
  removeAlarmsDataSubscriptionByModuleId: CaseReducer<
    SubscriptionsSliceState,
    PayloadAction<{
      moduleId: ModuleId;
    }>
  >;
  addAlarmsMonitoringBoardSubscription: CaseReducer<
    SubscriptionsSliceState,
    PayloadAction<AlarmsMonitoringBoardDataSubscriptionsTarget>
  >;
  removeAlarmsMonitoringBoardSubscription: CaseReducer<
    SubscriptionsSliceState,
    PayloadAction<undefined>
  >;
  pushNotificationsMessage: CaseReducer<
    SubscriptionsSliceState,
    PayloadAction<BackendAlarmsDataIncomingMessage>
  >;
  removeNotificationsMessagesByIds: CaseReducer<SubscriptionsSliceState, PayloadAction<string[]>>;
};

export const subscriptionsSlice = createSlice<
  SubscriptionsSliceState,
  SubscriptionsSliceCaseReducers,
  'monitoring.subscriptions'
>({
  name: 'monitoring.subscriptions',
  initialState: {
    isRunning: false,
    realtimeDataSubscriptions: null,
    realtimeDataSubscriptionsBlockGuidMapper: {},
    devicesRealtimeData: null,

    alarmsDataSubscriptions: null,
    alarmsDataSubscriptionsBlockGuidMapper: {},
    devicesAlarmsData: null,

    alarmsMonitoringBoard: {
      subscription: null,
    },

    notifications: {
      entities: {},
      ids: [],
    },
  },
  reducers: {
    launch: state => {
      state.isRunning = true;
    },
    addRealtimeDataSubscription(state, { payload: { moduleId, blockGuid, deviceGuids } }) {
      state.realtimeDataSubscriptionsBlockGuidMapper[moduleId] = blockGuid;
      if (state.realtimeDataSubscriptions === null) {
        state.realtimeDataSubscriptions = {
          [moduleId]: deviceGuids,
        };
      } else {
        set(state.realtimeDataSubscriptions, [moduleId], deviceGuids);
      }
    },
    removeRealtimeDataSubscriptionByModuleId(state, { payload: { moduleId } }) {
      state.realtimeDataSubscriptionsBlockGuidMapper[moduleId] = null;
      if (state.realtimeDataSubscriptions === null) {
        return;
      }
      delete state.realtimeDataSubscriptions[moduleId];
      if (Object.keys(state.realtimeDataSubscriptions).length <= 0) {
        state.realtimeDataSubscriptions = null;
      }
    },

    /**
     * 存储从 dcsocket 应用接收到的实时数据（推、拉方式都可）
     */
    receivedRealtimeData(state, { payload: devicesRealtimeDataByDeviceGuid }) {
      if (state.devicesRealtimeData === null) {
        state.devicesRealtimeData = {};
      }
      if (state.devicesAlarmsData === null) {
        state.devicesAlarmsData = {};
      }
      Object.entries(devicesRealtimeDataByDeviceGuid).forEach(([deviceGuid, deviceValueMap]) => {
        const { time, status: deviceStatus, pointValues } = deviceValueMap!;
        const deviceState = getDeviceOrPointState(deviceStatus);

        const pointsAlarmsCount: PointsAlarmsCount =
          state.devicesAlarmsData![deviceGuid]?.pointsCount ?? {};
        const currentPointValueMap = Object.entries(pointValues).reduce(
          (map, [pointCode, pointValueMap]) => {
            const { status: pointStatus, value } = pointValueMap!;
            const pointState = getDeviceOrPointState(deviceStatus, pointStatus);
            map[pointCode] = {
              ...map[pointCode],
              value,
              status: pointState,
            };
            pointsAlarmsCount[pointCode] = {
              ERROR: pointState === 'alarm' ? 1 : 0,
              WARN: pointState === 'warning' ? 1 : 0,
            };

            return map;
          },
          state.devicesRealtimeData![deviceGuid]?.pointValueMap || {}
        );
        state.devicesRealtimeData![deviceGuid] = {
          lastUpdatedAt: time,
          status: deviceState,
          pointValueMap:
            deviceState === 'comms-interrupted'
              ? Object.keys(currentPointValueMap).reduce((map, pointCode) => {
                  map[pointCode].status = 'comms-interrupted';

                  return map;
                }, currentPointValueMap)
              : currentPointValueMap,
        };

        const deviceAlarmsCount = { ERROR: 0, WARN: 0 };
        Object.entries(pointsAlarmsCount).forEach(([_pointCode, { ERROR, WARN }]) => {
          deviceAlarmsCount.ERROR += ERROR;
          deviceAlarmsCount.WARN += WARN;
        });
        state.devicesAlarmsData![deviceGuid] = {
          lastUpdatedAt: time,
          count: deviceAlarmsCount,
          pointsCount: pointsAlarmsCount,
        };
      });
    },
    addAlarmsDataSubscription(state, { payload: { moduleId, blockGuid, deviceGuids } }) {
      state.alarmsDataSubscriptionsBlockGuidMapper[moduleId] = blockGuid;
      if (state.alarmsDataSubscriptions === null) {
        state.alarmsDataSubscriptions = {
          [moduleId]: deviceGuids,
        };
      } else {
        set(state.alarmsDataSubscriptions, [moduleId], deviceGuids);
      }
    },
    removeAlarmsDataSubscriptionByModuleId(state, { payload: { moduleId } }) {
      state.alarmsDataSubscriptionsBlockGuidMapper[moduleId] = null;
      if (state.alarmsDataSubscriptions === null) {
        return;
      }
      delete state.alarmsDataSubscriptions[moduleId];
      if (Object.keys(state.alarmsDataSubscriptions).length <= 0) {
        state.alarmsDataSubscriptions = null;
      }
    },
    addAlarmsMonitoringBoardSubscription: (state, { payload: target }) => {
      state.alarmsMonitoringBoard.subscription = target;
    },
    removeAlarmsMonitoringBoardSubscription: state => {
      state.alarmsMonitoringBoard.subscription = null;
    },
    pushNotificationsMessage(sliceState, { payload: message }) {
      // cache update performance optimization
      const existingSubscriptions: BackendAlarmsDataIncomingMessage | undefined =
        sliceState.notifications.entities[message.id];
      if (existingSubscriptions && existingSubscriptions.alarmTime === message.alarmTime) {
        return;
      }

      sliceState.notifications.entities[message.id] = message;
      if (!existingSubscriptions) {
        sliceState.notifications.ids.push(message.id);
      }
    },
    removeNotificationsMessagesByIds(sliceState, { payload: ids }) {
      ids.forEach(id => {
        delete sliceState.notifications.entities[id];
      });
      sliceState.notifications.ids = ids.filter(_id => !ids.includes(_id));
    },
  },
});

function getDeviceOrPointState(
  deviceStatus: { code: BackendPointDataStatus; msg: string } | null | undefined,
  pointStatus?: { code: BackendPointDataStatus; msg: string } | null
) {
  let state: PointDataState = alarmsDataStateMapper[BackendPointDataStatus.Normal];
  if (
    deviceStatus?.code === BackendPointDataStatus.SocketOff ||
    deviceStatus?.code === BackendPointDataStatus.CollectorSocketOff ||
    deviceStatus?.code === BackendPointDataStatus.DataException
  ) {
    state = alarmsDataStateMapper[BackendPointDataStatus.SocketOff];
  } else if (!!deviceStatus && pointStatus === undefined) {
    state = alarmsDataStateMapper[deviceStatus.code];
  } else if (!!pointStatus) {
    state = alarmsDataStateMapper[pointStatus.code];
  }

  return state;
}
