import { createAction } from '@reduxjs/toolkit';
import type { ActionCreator, AnyAction } from '@reduxjs/toolkit';

import { subscriptionsSlice } from './subscriptions.slice.js';
import type {
  AlarmsMonitoringBoardDataSubscriptionsTarget,
  SubscriptionsTarget,
  UnSubscribeTarget,
} from './subscriptions.type.js';

const prefix = `${subscriptionsSlice.name}/` as const;
const postfix = '--async';
/**
 * @deprecated
 */
const LEGACY_SUBSCRIPTIONS_SLICE_NAME = 'subscriptions';

export const subscriptionsSliceActions = subscriptionsSlice.actions;

export const launchNotificationsWebSocketAction = createAction<{ idc: string }>(
  prefix + 'LAUNCH_NOTIFICATIONS_WEBSOCKET' + postfix
);

const legacyAddRealtimeDataSubscriptionsActionType =
  `${LEGACY_SUBSCRIPTIONS_SLICE_NAME}/ADD_REALTIME_N_ALARMS_DATA_SUBSCRIPTION` as const;
export const addRealtimeNAlarmsDataSubscriptionActionCreator = createAction<
  | SubscriptionsTarget
  | {
      targets: SubscriptionsTarget[];
    },
  typeof legacyAddRealtimeDataSubscriptionsActionType
>(legacyAddRealtimeDataSubscriptionsActionType);

const legacyRemoveRealtimeDataSubscriptionsActionType =
  `${LEGACY_SUBSCRIPTIONS_SLICE_NAME}/REMOVE_REALTIME_N_ALARMS_DATA_SUBSCRIPTION` as const;
export const removeRealtimeNAlarmsDataSubscriptionActionCreator = createAction<
  UnSubscribeTarget,
  typeof legacyRemoveRealtimeDataSubscriptionsActionType
>(legacyRemoveRealtimeDataSubscriptionsActionType);

const subscribeAlarmsMonitoringBoardActionType =
  `${prefix}subscribeAlarmsMonitoringBoard${postfix}` as const;
export const subscribeAlarmsMonitoringBoardAction = createAction<
  {
    target: AlarmsMonitoringBoardDataSubscriptionsTarget;
    successActions?: ActionCreator<AnyAction>[];
  },
  typeof subscribeAlarmsMonitoringBoardActionType
>(subscribeAlarmsMonitoringBoardActionType);

const unsubscribeAlarmsMonitoringBoardActionType =
  `${prefix}unsubscribeAlarmsMonitoringBoard${postfix}` as const;
export const unsubscribeAlarmsMonitoringBoardAction = createAction<
  undefined,
  typeof unsubscribeAlarmsMonitoringBoardActionType
>(unsubscribeAlarmsMonitoringBoardActionType);
