import type {
  BackendAlarm,
  BackendAlarmLifecycleState,
  BackendAlarmState,
  BackendAlarmType,
} from '@manyun/monitoring.model.alarm';

import type { SUBSCRIPTIONS_MODE } from './subscriptions.constant.js';

// @TODO @Jerry import it from somewhere
type AlarmLevel = string;
type RoomType = string;

export type ModuleId = string;
export type SubscriptionsMode = (typeof SUBSCRIPTIONS_MODE)[keyof typeof SUBSCRIPTIONS_MODE];
export type SubscriptionsType =
  | 'realtime-data'
  | 'alarms-data'
  | /* 告警盯屏数据 */ 'alarms-monitoring-board-data';

export type RealtimeDataSubscriptionsTarget = {
  mode: SubscriptionsMode;
  moduleId: ModuleId;
  blockGuid: string;
  deviceGuids: string[];
};
export type AlarmsDataSubscriptionsTarget = {
  mode: SubscriptionsMode;
  moduleId: ModuleId;
  blockGuid: string;
  deviceGuids: string[];
};
export type AlarmsMonitoringBoardDataSubscriptionsTarget = {
  idc: string;
  blockGuids?: string[];
  roomGuids?: string[];
  roomTypes?: RoomType[];
  alarmTypes?: BackendAlarmType[];
  alarmLevels?: AlarmLevel[];
  alarmStates?: BackendAlarmState[];
  alarmLifecycleStates?: BackendAlarmLifecycleState[];
  deviceTypes?: string[];
  lastModifyUserId?: number;
  /**
   * timestamps
   */
  alarmsTriggeredAtTimeRange?: [number, number];
  /**
   * timestamps
   */
  alarmsRecoveredAtTimeRange?: [number, number];
  alarmContent?: string;
};
export type SubscriptionsTarget =
  | RealtimeDataSubscriptionsTarget
  | AlarmsDataSubscriptionsTarget
  | AlarmsMonitoringBoardDataSubscriptionsTarget;
export type UnSubscribeTarget = {
  mode: SubscriptionsMode;
  moduleId: string;
  blockGuid: string;
};

export type AlarmUpdatesAction = 'ADD' | 'UPDATE' | 'DEL';
export type AlarmsMonitoringBoardData = { type: AlarmUpdatesAction } & Pick<
  BackendAlarm,
  'id' | 'alarmLevel' | 'notifyContent'
> &
  Partial<Omit<BackendAlarm, 'id' | 'alarmLevel' | 'notifyContent'>>;
