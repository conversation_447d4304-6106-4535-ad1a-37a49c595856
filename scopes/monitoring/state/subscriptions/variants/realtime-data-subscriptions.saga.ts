import * as StompJs from '@stomp/stompjs';
import debug from 'debug';
import merge from 'lodash.merge';
import noop from 'lodash.noop';
import throttle from 'lodash.throttle';
import { eventChannel } from 'redux-saga';
// import type { Subscriber } from 'rxjs';
import type { Task } from 'redux-saga';
import {
  all,
  call,
  cancel,
  cancelled,
  delay,
  fork,
  put,
  select,
  take,
  takeEvery,
  takeLatest,
} from 'redux-saga/effects';
import type { SagaReturnType } from 'redux-saga/effects';
import SockJs from 'sockjs-client';

import { getUserInfo } from '@manyun/auth-hub.cache.user';
import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import { getPreferences } from '@manyun/monitoring.cache.preferences';
import { Alarm } from '@manyun/monitoring.model.alarm';
import type { BackendAlarm } from '@manyun/monitoring.model.alarm';
import type { BackendRealtimePointData } from '@manyun/monitoring.model.monitoring-data';
import { receiveAlarmsMessageAction } from '@manyun/monitoring.state.alarms-monitoring-board';
import { createAlarmsTtsManager } from '@manyun/monitoring.util.alarms-tts-manager';
import { ttsService } from '@manyun/monitoring.util.tts-service';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

import {
  addRealtimeNAlarmsDataSubscriptionActionCreator,
  removeRealtimeNAlarmsDataSubscriptionActionCreator,
  subscribeAlarmsMonitoringBoardAction,
  subscriptionsSliceActions,
  unsubscribeAlarmsMonitoringBoardAction,
} from '../subscriptions.action.js';
// import type { AlarmMain } from '@manyun/monitoring.util.alarms-tts-manager';
import { SUBSCRIPTIONS_MODE } from '../subscriptions.constant.js';
import {
  getShouldSubscribe,
  getSubscriptions,
  selectAlarmsMonitoringBoardSubscriptions,
} from '../subscriptions.selector.js';
import type {
  AlarmUpdatesAction,
  AlarmsDataSubscriptionsTarget,
  AlarmsMonitoringBoardData,
  RealtimeDataSubscriptionsTarget,
} from '../subscriptions.type.js';

const stompjsDebug = debug('stompjs:client');

function sanitizeAlarmMessage(message: string) {
  return message.replace(/_/gm, ' ');
}

const verifyWebSocketInfo = async ({ idc }: { idc: string }) => {
  // See https://github.com/sockjs/sockjs-client/issues/268#issuecomment-154130487
  const json = await (
    await fetch(
      `/ws/dcsocket/info?idc=${idc}&t=${Date.now()}&sessionId=${localStorage.getItem('sessionId')!}`
    )
  ).json();
  if (json.success === false) {
    return Promise.reject(`${json.errMessage}(${json.errCode})`);
  }
  return Promise.resolve();
};

const createSock = ({ idc }: { idc: string }) =>
  new SockJs(`/ws/dcsocket?idc=${idc}&sessionId=${localStorage.getItem('sessionId')!}`);

const configureConnection = ({ idc, userId }: { idc: string; userId: number | string }) => ({
  webSocketFactory: () => createSock({ idc }),
  connectHeaders: {
    'X-IDC': idc,
    token: env.DCSOCKET_TOKEN ?? 'fake-token',
    // 后端要解析用户 ID
    userId: `${userId}@${Date.now()}`,
  },
});

const createWebSocketConnection = async ({ idc, userId }: { idc: string; userId: number }) =>
  new Promise<StompJs.Client>((resolve, reject) => {
    verifyWebSocketInfo({ idc })
      .then(() => {
        const client = new StompJs.Client({
          ...configureConnection({ idc, userId }),
          reconnectDelay: 5 * 1000,
          heartbeatIncoming: 4 * 1000,
          heartbeatOutgoing: 4 * 1000,
          splitLargeFrames: true,
          debug: stompjsDebug,
          onWebSocketClose: env.__DEV_MODE__ ? console.error : noop,
        });

        client.onStompError = function (frame) {
          // Will be invoked in case of error encountered at Broker
          // Bad login/passcode typically will cause an error
          // Complaint brokers will set `message` header with a brief message. Body may contain details.
          // Compliant brokers will terminate the connection after any error
          // eslint-disable-next-line no-console
          console.log('Broker reported error: ' + frame.headers['message']);
          // eslint-disable-next-line no-console
          console.log('Additional details: ' + frame.body);
        };

        if (env.__DEV_MODE__) {
          // eslint-disable-next-line no-console
          console.log(
            [
              'Subscriptoins socket connected, you can test it over `window.__dcbaseSubscriptionsTest` function.',
              'This function takes one object argument which has below signature:',
              'interface SubscriptionsTestRealtimeDataArg {',
              "  type: 'realtime-data';",
              '  payload: {',
              '    blockGuid: string;',
              '    data: Record<string, { time: number; pointValues: Record<string, { pointCode: string; dataValue: number }> }>;',
              '  };',
              '}',
            ].join('\n')
          );
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          (window as any).__dcbaseSubscriptionsTest = ({
            type,
            payload: { blockGuid, data },
          }: {
            type: 'realtime-data' | 'alarms-monitoring-board';
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            payload: any;
          }) => {
            const headers = { blockGuid };
            if (type === 'realtime-data') {
              client.publish({
                destination: '/app/point/value/send',
                headers,
                body: JSON.stringify(data),
              });
            }

            if (type === 'alarms-monitoring-board') {
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              Object.values(data).forEach((backendAlarm: any) => {
                // eslint-disable-next-line no-console
                console.log(Alarm.fromApiObject(backendAlarm).toJSON());
              });
              client.publish({
                destination: '/app/alarm/data/send',
                headers,
                body: JSON.stringify(data),
              });
            }
          };
        }

        resolve(client);
      })
      .catch(reject);
  });

const createSocketChannel = (
  client: StompJs.Client,
  targets: typeof targetsMap,
  intervalMs = 1 * 1000
) => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  return eventChannel<any>(emit => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const realtimeDataQueue: any[] = [];
    let realtimeDataIntervalId: number;
    const receivedRealtimeData = () => {
      const end = realtimeDataQueue.length;
      if (end > 0) {
        let data = {};
        let idx = 0;
        while (idx < end) {
          // Update point values incrementally here.
          // Because we could have the same GUID in the `data`
          // and the `shifted item`, we merge them recursively
          // to not lose data.
          data = merge(data, realtimeDataQueue.shift());
          idx++;
        }
        emit(subscriptionsSliceActions.receivedRealtimeData(data));
      }
      realtimeDataIntervalId = window.setTimeout(receivedRealtimeData, intervalMs);
    };

    const userPointValueHandler = throttle(response => {
      try {
        const data: BackendRealtimePointData = JSON.parse(response.body);
        if (!(data && Object.keys(data).length > 0)) {
          return;
        }

        realtimeDataQueue.push(data);

        if (realtimeDataIntervalId === undefined) {
          realtimeDataIntervalId = window.setTimeout(receivedRealtimeData, 0);
        }
      } catch (error) {
        console.error(error);
      }
    }, 0);

    const alarmsTtsManager = createAlarmsTtsManager(subscriber => {
      // Do nothing...
      // alarmsTtsManagerSubscriber = subscriber;
    });
    const alarmsMonitoringBoardDataQueue: AlarmsMonitoringBoardData[] = [];
    type AlarmsMonitoringBoardDataIntervalIdsMap = Record<
      'appChannel' | 'userChannel',
      number | undefined
    >;
    const alarmsMonitoringBoardDataIntervalIdsMap: AlarmsMonitoringBoardDataIntervalIdsMap = {
      appChannel: undefined,
      userChannel: undefined,
    };
    const receivedAlarmsMonitoringBoardData = (
      channel: 'app' | 'user',
      intervalIdsMap: AlarmsMonitoringBoardDataIntervalIdsMap
    ) => {
      const ttsAlarms: Omit<AlarmsMonitoringBoardData, 'type'>[] = [];
      const end = Math.min(1000, alarmsMonitoringBoardDataQueue.length);
      if (end > 0) {
        const data: {
          action: AlarmUpdatesAction;
          alarm: Omit<AlarmsMonitoringBoardData, 'type'>;
        }[] = [];
        let idx = 0;
        while (idx < end) {
          const item = alarmsMonitoringBoardDataQueue.shift();
          if (item !== undefined) {
            const { type, ...backendAlarm } = item;
            if (
              type === 'ADD' &&
              !(backendAlarm.expectStatus === 'EXPECTED' || backendAlarm.mergeStatus === 'CHILD')
            ) {
              ttsAlarms.push(backendAlarm);
            }
            data.push({
              action: type ?? 'UPDATE',
              alarm: backendAlarm,
            });
          }
          idx++;
        }

        const preferences = getPreferences();
        if (ttsAlarms.length > 0 && preferences.ttsService.enabled && channel === 'user') {
          ttsAlarms.forEach(alarm => {
            const simpleAlarm = {
              id: alarm.id,
              level: alarm.alarmLevel,
              message: sanitizeAlarmMessage(alarm.notifyContent),
            };
            if ((env.MONITORING_ALARMS_TTS_SERVICE ?? 'built-in') === 'built-in') {
              const currentSpeakingAlarm = ttsService.getCurrentSpeakingAlarm();
              if (
                currentSpeakingAlarm &&
                Number(alarm.alarmLevel) <= Number(currentSpeakingAlarm.level)
              ) {
                ttsService.cancel();
              }
              ttsService.speak({ alarm: simpleAlarm, queue: false });
            } else {
              if (
                !alarmsTtsManager.current ||
                Number(alarm.alarmLevel) <= Number(alarmsTtsManager.current.level)
              ) {
                alarmsTtsManager.playAsync(simpleAlarm);
              }
            }
          });
        }

        emit(receiveAlarmsMessageAction({ alarms: data }));
      }
      if (channel === 'user' || (channel === 'app' && alarmsMonitoringBoardDataQueue.length > 0)) {
        const intervalIdChannel = channel === 'user' ? 'userChannel' : 'appChannel';
        intervalIdsMap[intervalIdChannel] = window.setTimeout(
          receivedAlarmsMonitoringBoardData,
          intervalMs,
          channel,
          intervalIdsMap
        );
      }
    };
    const appAlarmsMonitoringBoardDataHandler = throttle(response => {
      try {
        const data: (BackendAlarm & { type: AlarmUpdatesAction })[] = JSON.parse(response.body);
        if (!(data && Object.keys(data).length > 0)) {
          return;
        }
        alarmsMonitoringBoardDataQueue.push(...data);
        receivedAlarmsMonitoringBoardData('app', alarmsMonitoringBoardDataIntervalIdsMap);
      } catch (error) {
        console.error(error);
      }
    }, 0);
    const userAlarmsMonitoringBoardDataHandler = throttle(response => {
      try {
        const data: (BackendAlarm & { type: AlarmUpdatesAction })[] = JSON.parse(response.body);
        if (!(data && Object.keys(data).length > 0)) {
          return;
        }
        alarmsMonitoringBoardDataQueue.push(...data);
        if (alarmsMonitoringBoardDataIntervalIdsMap.userChannel === undefined) {
          alarmsMonitoringBoardDataIntervalIdsMap.userChannel = window.setTimeout(
            receivedAlarmsMonitoringBoardData,
            0,
            'user',
            alarmsMonitoringBoardDataIntervalIdsMap
          );
        }
      } catch (error) {
        console.error(error);
      }
    }, 0);

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const subscriptions: any[] = [];

    const current = targets.get('current')!;

    if (current.some(({ type }) => type === 'realtime-data')) {
      subscriptions.push(client.subscribe('/user/point/value', userPointValueHandler));
    }

    current.forEach(
      ({
        type,
        blockGuid,
        deviceGuids,
        // REFACTORME: @Jerry
        alarmMonitoringBoardSubscriptionsTarget,
      }) => {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const headers: any = {};
        if (type === 'realtime-data' || type === 'alarms-data') {
          const { idc } = getSpaceGuidMap(blockGuid)!;
          headers['X-IDC'] = idc;
          headers.deviceGuidList = deviceGuids.join(',');
        }
        if (type === 'realtime-data') {
          subscriptions.push(
            client.subscribe('/app/point/value/subscribe', userPointValueHandler, headers)
          );
        }

        // REFACTORME: @Jerry
        if (type === 'alarms-monitoring-board') {
          subscriptions.push(
            client.subscribe('/app/alarm/data/subscribe', appAlarmsMonitoringBoardDataHandler, {
              alarmDataParam: JSON.stringify({
                idcTag: alarmMonitoringBoardSubscriptionsTarget.idc,
                blockGuidList: alarmMonitoringBoardSubscriptionsTarget.blockGuids ?? null,
                roomGuidList: alarmMonitoringBoardSubscriptionsTarget.roomGuids ?? null,
                alarmLevel: alarmMonitoringBoardSubscriptionsTarget.alarmLevels ?? null,
                alarmStatus: alarmMonitoringBoardSubscriptionsTarget.alarmLifecycleStates ?? null,
                triggerStatus: alarmMonitoringBoardSubscriptionsTarget.alarmStates ?? null,
                alarmType: alarmMonitoringBoardSubscriptionsTarget.alarmTypes?.[0] ?? null,
                roomTypeList: alarmMonitoringBoardSubscriptionsTarget.roomTypes ?? null,
                deviceTypeList: alarmMonitoringBoardSubscriptionsTarget.deviceTypes ?? null,
                confirmBy: alarmMonitoringBoardSubscriptionsTarget.lastModifyUserId ?? null,
                alarmCreateTimeStart:
                  alarmMonitoringBoardSubscriptionsTarget.alarmsTriggeredAtTimeRange?.[0] ?? null,
                alarmCreateTimeEnd:
                  alarmMonitoringBoardSubscriptionsTarget.alarmsTriggeredAtTimeRange?.[1] ?? null,
                recoverTimeStart:
                  alarmMonitoringBoardSubscriptionsTarget.alarmsRecoveredAtTimeRange?.[0] ?? null,
                recoverTimeEnd:
                  alarmMonitoringBoardSubscriptionsTarget.alarmsRecoveredAtTimeRange?.[1] ?? null,
                notifyContent: alarmMonitoringBoardSubscriptionsTarget.alarmContent ?? null,
              }),
            })
          );
          subscriptions.push(
            client.subscribe('/user/alarm/data', userAlarmsMonitoringBoardDataHandler)
          );
        }
      }
    );

    emit({ type: 'subscribed', payload: targets });

    return () => {
      subscriptions.forEach(({ id }) => {
        client.unsubscribe(id);
      });

      realtimeDataIntervalId !== undefined && window.clearTimeout(realtimeDataIntervalId);

      alarmsMonitoringBoardDataIntervalIdsMap.appChannel !== undefined &&
        window.clearTimeout(alarmsMonitoringBoardDataIntervalIdsMap.appChannel);
      alarmsMonitoringBoardDataIntervalIdsMap.userChannel !== undefined &&
        window.clearTimeout(alarmsMonitoringBoardDataIntervalIdsMap.userChannel);
      alarmsTtsManager.dispose();
    };
  });
};

// Workers
// ------

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function* watchSocketChannel(
  client: StompJs.Client,
  subscriptions: typeof targetsMap,
  intervalMs?: number
) {
  const socketChannel: SagaReturnType<typeof createSocketChannel> = yield call(
    createSocketChannel,
    client,
    subscriptions,
    intervalMs
  );

  try {
    while (true) {
      const action: SagaReturnType<typeof take> = yield take(socketChannel);
      yield put(action);
    }
  } finally {
    if ((yield cancelled()) as SagaReturnType<typeof cancelled>) {
      socketChannel.close();
    }
  }
}

const launchActionType = 'LAUNCH_SUBSCRIPTIONS_CONNECTION' as const;

type LaunchAction = {
  type: typeof launchActionType;
  payload: {
    idc: string;
    userId: number;
  };
};

const terminateActionType = 'subscriptions/terminate-realtime-data-subscription' as const;

type TerminateAction = {
  type: typeof terminateActionType;
};

/**
 * Current idc's WebSocket connection
 */
let connectedIdc: string | null = null;
let client: StompJs.Client | null = null;
let subscribeTask: Task | null = null;
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const targetsMap = new Map<'current', any[]>([['current', []]]);
export function* launchSaga({ payload: { idc, userId } }: LaunchAction) {
  const subscriptions: SagaReturnType<typeof getSubscriptions> = yield select(getSubscriptions);

  if (subscriptions.length <= 0) {
    return;
  }

  targetsMap.set('current', subscriptions);
  if (!client) {
    try {
      client = yield call(createWebSocketConnection, { idc, userId });
      connectedIdc = idc;
    } catch (error) {
      console.error(error);
    }
  } else {
    yield client.deactivate();
    if (connectedIdc !== idc) {
      try {
        yield verifyWebSocketInfo({ idc });
        client.configure({
          ...configureConnection({ idc, userId }),
        });
        connectedIdc = idc;
      } catch (error) {
        client = null;
        console.error(error);
      }
    }
  }

  if (!client) {
    console.error(`Failed to create WebSocket connection for IDC(${idc}).`);
    return;
  }

  const connectChannel = eventChannel(emit => {
    if (client) {
      client.onConnect = () => {
        emit({ type: 'connected' });
      };
    }

    return () => {
      if (client) {
        client.onConnect = noop;
      }
    };
  });

  if (!client.active) {
    client.activate();
    yield put(subscriptionsSliceActions.launch());
  }

  try {
    while (true) {
      const action: SagaReturnType<() => { type: string }> = yield take(connectChannel);
      if (action.type === 'connected') {
        if (subscribeTask) {
          yield cancel(subscribeTask);
        }

        subscribeTask = yield fork(watchSocketChannel, client, targetsMap);
      }
    }
  } finally {
    connectChannel.close();
  }
}

export function* terminateSaga(_action: TerminateAction) {
  if (subscribeTask) {
    yield cancel(subscribeTask);
    subscribeTask = null;
  }
}

export function* mutateSubscriptionsSaga({
  type,
  payload,
}: ReturnType<
  | typeof addRealtimeNAlarmsDataSubscriptionActionCreator
  | typeof removeRealtimeNAlarmsDataSubscriptionActionCreator
>) {
  let syntheticTargets: (RealtimeDataSubscriptionsTarget | AlarmsDataSubscriptionsTarget)[] = [];
  if ('targets' in payload && Array.isArray(payload.targets) && payload.targets.length > 0) {
    syntheticTargets = payload.targets as (
      | RealtimeDataSubscriptionsTarget
      | AlarmsDataSubscriptionsTarget
    )[];
  } else {
    syntheticTargets.push(
      payload as RealtimeDataSubscriptionsTarget | AlarmsDataSubscriptionsTarget
    );
  }

  // FIXME @Jerry
  let idc = '';

  function* adjustSubscriptions({
    mode,
    blockGuid,
    moduleId,
    deviceGuids,
  }: RealtimeDataSubscriptionsTarget | AlarmsDataSubscriptionsTarget) {
    const spaceGuidMap = getSpaceGuidMap(blockGuid);
    idc = spaceGuidMap.idc!;

    if (type === addRealtimeNAlarmsDataSubscriptionActionCreator.type) {
      if (
        mode === SUBSCRIPTIONS_MODE.REALTIME_N_ALARMS ||
        mode === SUBSCRIPTIONS_MODE.REALTIME_ONLY
      ) {
        yield put(
          subscriptionsSliceActions.addRealtimeDataSubscription({
            blockGuid,
            moduleId,
            deviceGuids,
          })
        );
      }
      if (
        mode === SUBSCRIPTIONS_MODE.REALTIME_N_ALARMS ||
        mode === SUBSCRIPTIONS_MODE.ALARMS_ONLY
      ) {
        yield put(
          subscriptionsSliceActions.addAlarmsDataSubscription({
            blockGuid,
            moduleId,
            deviceGuids,
          })
        );
      }
    }

    if (type === removeRealtimeNAlarmsDataSubscriptionActionCreator.type) {
      if (
        mode === SUBSCRIPTIONS_MODE.REALTIME_N_ALARMS ||
        mode === SUBSCRIPTIONS_MODE.REALTIME_ONLY
      ) {
        yield put(subscriptionsSliceActions.removeRealtimeDataSubscriptionByModuleId({ moduleId }));
      }
      if (
        mode === SUBSCRIPTIONS_MODE.REALTIME_N_ALARMS ||
        mode === SUBSCRIPTIONS_MODE.ALARMS_ONLY
      ) {
        yield put(subscriptionsSliceActions.removeAlarmsDataSubscriptionByModuleId({ moduleId }));
      }
    }
  }
  const effects = syntheticTargets.map(adjustSubscriptions);
  yield all(effects);

  const shouldSubscribe: SagaReturnType<typeof getShouldSubscribe> =
    yield select(getShouldSubscribe);
  const { userId } = getUserInfo();
  if (shouldSubscribe && idc && userId !== null) {
    yield put({ type: launchActionType, payload: { idc, userId } });
  } else {
    yield put({ type: terminateActionType });
  }
}

const establishAlarmsMonitoringSubscriptionActionType =
  'monitoring.subscriptions/establish-alarms-monitoring-subscription--async' as const;
/**
 * Current idc's WebSocket connection
 */
let connectedAlarmsMonitoringSubscriptionIdc: string | null = null;
let alarmsMonitoringSubscriptionClient: StompJs.Client | null = null;
let alarmsMonitoringSubscriptionTask: Task | null = null;
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const alarmsTargetsMap = new Map<'current', any[]>([['current', []]]);
export function* establishAlarmsMonitoringSubscriptionSaga({
  payload: { idc },
}: {
  type: typeof establishAlarmsMonitoringSubscriptionActionType;
  payload: { idc: string };
}) {
  const alarmMonitoringBoardSubscriptionsTarget: SagaReturnType<
    typeof selectAlarmsMonitoringBoardSubscriptions
  > = yield select(selectAlarmsMonitoringBoardSubscriptions);
  if (alarmMonitoringBoardSubscriptionsTarget === null) {
    return;
  }

  alarmsTargetsMap.set('current', [
    {
      type: 'alarms-monitoring-board',
      alarmMonitoringBoardSubscriptionsTarget,
    },
  ]);

  // Sleep 1sec to avoid concurrent `launch` calls
  yield delay(1000);

  const { userId } = getUserInfo();
  if (!alarmsMonitoringSubscriptionClient && userId !== null) {
    try {
      yield verifyWebSocketInfo({ idc });
      alarmsMonitoringSubscriptionClient = yield call(createWebSocketConnection, { idc, userId });
      connectedAlarmsMonitoringSubscriptionIdc = idc;
    } catch (error) {
      console.error(error);
    }
  } else {
    if (alarmsMonitoringSubscriptionClient) {
      yield alarmsMonitoringSubscriptionClient.deactivate();
      if (connectedAlarmsMonitoringSubscriptionIdc !== idc) {
        try {
          yield verifyWebSocketInfo({ idc });
          alarmsMonitoringSubscriptionClient.configure({
            webSocketFactory: () => createSock({ idc }),
          });
          connectedAlarmsMonitoringSubscriptionIdc = idc;
        } catch (error) {
          alarmsMonitoringSubscriptionClient = null;
          console.error(error);
        }
      }
    }
  }

  if (!alarmsMonitoringSubscriptionClient) {
    console.error(`Failed to create alarms WebSocket connection for IDC(${idc}).`);
    return;
  }

  const connectChannel = eventChannel(emit => {
    if (alarmsMonitoringSubscriptionClient) {
      alarmsMonitoringSubscriptionClient.onConnect = () => {
        emit({ type: 'connected' });
      };
    }

    return () => {
      if (alarmsMonitoringSubscriptionClient) {
        alarmsMonitoringSubscriptionClient.onConnect = noop;
      }
    };
  });

  if (!alarmsMonitoringSubscriptionClient.active) {
    alarmsMonitoringSubscriptionClient.activate();
    yield put(subscriptionsSliceActions.launch());
  }

  try {
    while (true) {
      const action: SagaReturnType<() => { type: string }> = yield take(connectChannel);
      if (action.type === 'connected') {
        if (alarmsMonitoringSubscriptionTask) {
          yield cancel(alarmsMonitoringSubscriptionTask);
        }

        alarmsMonitoringSubscriptionTask = yield fork(
          watchSocketChannel,
          alarmsMonitoringSubscriptionClient,
          alarmsTargetsMap,
          3 * 1000
        );
      }
    }
  } finally {
    connectChannel.close();
  }
}

export function* mutateAlarmsMonitoringBoardSubscriptionsSaga({
  type,
  payload,
}: ReturnType<
  typeof subscribeAlarmsMonitoringBoardAction | typeof unsubscribeAlarmsMonitoringBoardAction
>) {
  if (type === subscribeAlarmsMonitoringBoardAction.type) {
    yield put(subscriptionsSliceActions.addAlarmsMonitoringBoardSubscription(payload.target));
    yield put({
      type: establishAlarmsMonitoringSubscriptionActionType,
      payload: { idc: payload.target.idc },
    });
    const dispatches = payload.successActions?.map(succssAction => put(succssAction()));
    if (dispatches !== undefined) {
      yield all(dispatches);
    }
  } else if (type === unsubscribeAlarmsMonitoringBoardAction.type) {
    yield put(subscriptionsSliceActions.removeAlarmsMonitoringBoardSubscription());
    if (alarmsMonitoringSubscriptionTask) {
      yield cancel(alarmsMonitoringSubscriptionTask);
    }
  }
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
let lastTask: Task<any> | null = null;
function* subscriptionsSaga(action: LaunchAction | TerminateAction) {
  if (action.type === terminateActionType) {
    if (lastTask) {
      yield cancel(lastTask);
      yield call(terminateSaga, action);
    }
  } else {
    // Sleep 1sec to avoid concurrent `launch` calls
    yield delay(1000);
    if (lastTask) {
      yield cancel(lastTask); // cancel is no-op if the task has already terminated
    }
    lastTask = yield fork(launchSaga, action);
  }
}

// Watchers
// ------

function* watchSubscriptions() {
  yield takeLatest([launchActionType, terminateActionType], subscriptionsSaga);
}

function* watchAddNRemoveSubscription() {
  yield takeEvery(
    [
      addRealtimeNAlarmsDataSubscriptionActionCreator.type,
      removeRealtimeNAlarmsDataSubscriptionActionCreator.type,
    ],
    mutateSubscriptionsSaga
  );
}

function* watchAlarmsMonitoringSubscriptions() {
  yield takeLatest(
    establishAlarmsMonitoringSubscriptionActionType,
    establishAlarmsMonitoringSubscriptionSaga
  );
}

function* watchMutateAlarmsMonitoringSubscriptions() {
  yield takeEvery(
    [subscribeAlarmsMonitoringBoardAction.type, unsubscribeAlarmsMonitoringBoardAction.type],
    mutateAlarmsMonitoringBoardSubscriptionsSaga
  );
}

export default [
  fork(watchSubscriptions),
  fork(watchAddNRemoveSubscription),
  fork(watchAlarmsMonitoringSubscriptions),
  fork(watchMutateAlarmsMonitoringSubscriptions),
];
