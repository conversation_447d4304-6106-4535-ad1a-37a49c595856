import type { Client as StompClient } from '@stomp/stompjs';
import type { AnyAction } from 'redux';
import type { Task } from 'redux-saga';
import { call, cancel, cancelled, fork, put, select, take } from 'redux-saga/effects';
import type { CancelledEffect, SagaReturnType } from 'redux-saga/effects';

import { selectMyResourceCodes } from '@manyun/auth-hub.state.user';

import {
  createSocketChannel as createNotificationsSocketChannel,
  createWebSocketClient as createNotificationsWebSocketClient,
} from './notifications-websocket-client.js';
import { launchNotificationsWebSocketAction } from './subscriptions.action.js';

/** Workers */

/** Watchers */

function* watchNotificationsSocketChannel(client: StompClient) {
  const socketChannel: SagaReturnType<typeof createNotificationsSocketChannel> = yield call(
    createNotificationsSocketChannel,
    client,
    [
      {
        type: 'alarms-data',
      },
    ]
  );

  if (!client.active) {
    client.activate();
  }

  try {
    while (true) {
      const action: AnyAction = yield take(socketChannel);
      yield put(action);
    }
  } finally {
    if ((yield cancelled()) as CancelledEffect) {
      socketChannel.close();
    }
  }
}

let subscribedIdc: string | null = null;
let notificationsWebSocketClient: StompClient | null = null;
let notificationsSocketChannelTask: Task | null = null;
function* watchLaunchNotificationsWebSocket() {
  const {
    payload: { idc },
  }: SagaReturnType<typeof launchNotificationsWebSocketAction> = yield take(
    launchNotificationsWebSocketAction.type
  );
  const myResourceCodes: SagaReturnType<typeof selectMyResourceCodes> =
    yield select(selectMyResourceCodes);
  // Subscribe "blocks" under "idc" only.
  const blockGuids = myResourceCodes.filter(
    spaceGuid => spaceGuid.split('.').length === 2 && spaceGuid.startsWith(idc + '.')
  );
  if ((notificationsWebSocketClient && subscribedIdc === idc) || blockGuids.length <= 0) {
    return;
  }
  if (notificationsSocketChannelTask !== null) {
    yield cancel(notificationsSocketChannelTask);
    notificationsWebSocketClient = null;
    subscribedIdc = null;
  }
  const client: SagaReturnType<typeof createNotificationsWebSocketClient> = yield call(
    createNotificationsWebSocketClient,
    { idc, blockGuids }
  );
  notificationsWebSocketClient = client;
  notificationsSocketChannelTask = yield fork(watchNotificationsSocketChannel, client);
}

export default [fork(watchLaunchNotificationsWebSocket)];
