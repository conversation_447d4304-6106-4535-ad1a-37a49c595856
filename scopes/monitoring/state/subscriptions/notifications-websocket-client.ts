import type {
  Client as Stomp<PERSON>lient,
  StompHeaders,
  IMessage as StompMessage,
  StompSubscription,
} from '@stomp/stompjs';
import type { AnyAction } from 'redux';
import { eventChannel } from 'redux-saga';
import shortid from 'shortid';

import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import type { BackendAlarmState } from '@manyun/monitoring.model.alarm';

import { subscriptionsSliceActions } from './subscriptions.action.js';
import { createWebSocketClient as createClient } from './websocket-client.js';

export type NotificationsWebSocketClientConfig = {
  idc: string;
  blockGuids: string[];
};

export async function createWebSocketClient({
  idc,
  blockGuids,
}: NotificationsWebSocketClientConfig) {
  // Every client should use a unique "userId",
  // so we put together a timestamp of now.
  const randomUserId = shortid() + '.' + Date.now();
  const client = createClient({
    url: `/ws/dcsocket?idc=${idc}&sessionId=${localStorage.getItem('sessionId')!}`,
    connectHeaders: {
      'X-IDC': idc,
      token: env.DCSOCKET_TOKEN!,
      userId: randomUserId,
      blockGuidSet: blockGuids.join(','),
    },
  });

  return client;
}

export type NotificationsWebSocketTarget = {
  type: 'alarms-data';
  subscribeHeaders?: StompHeaders;
};

type BackendAlarmTransmissionState = 'open' | 'resolved';

type BaseAlarmsDataIncomingMessage = {
  id: string;
  idcTag: string;
  blockTag: string;
  roomTag: string | null;
  deviceType: string | null;
  triggerGuid: string;
  deviceName: string | null;
  pointCode: string;
  pointName: string | null;
  alarmValue: string;
  alarmLevel: number;
  alarmLevelName: string;
  alarmContent: string;
  /** 最新触发时间 */
  alarmTime: number;
};

export type BackendAlarmsSubscriptionIncomingMessage = BaseAlarmsDataIncomingMessage & {
  businessScene: 'ALARM_SCREEN';
  alarmStatus: BackendAlarmState;
};

export type BackendAlarmsTransmissionIncomingMessage = BaseAlarmsDataIncomingMessage & {
  businessScene: 'ALARM_TRANSMISSION';
  alarmStatus: BackendAlarmTransmissionState;
};

export type BackendAlarmsDataIncomingMessage =
  | BackendAlarmsSubscriptionIncomingMessage
  | BackendAlarmsTransmissionIncomingMessage;

export const alarmTransmissionStateLocale: Record<BackendAlarmTransmissionState, string> = {
  open: '触发',
  resolved: '恢复',
};

export function createSocketChannel(client: StompClient, targets: NotificationsWebSocketTarget[]) {
  return eventChannel<AnyAction>(emit => {
    const userAlarmsDataHandler = (message: StompMessage) => {
      try {
        const data = JSON.parse(message.body) as BackendAlarmsDataIncomingMessage;
        if (!(data && Object.keys(data).length > 0)) {
          return;
        }
        emit(subscriptionsSliceActions.pushNotificationsMessage(data));
      } catch (error) {
        console.error(error);
      }
    };

    const subs: StompSubscription[] = [];
    const subscribeTargets = () => {
      targets.forEach(({ type, subscribeHeaders }) => {
        if (type === 'alarms-data') {
          subs.push(client.subscribe('/user/alarm/new', userAlarmsDataHandler, subscribeHeaders));
        }
      });
    };
    if (client.connected) {
      subscribeTargets();
    } else {
      client.onConnect = subscribeTargets;
    }

    return () => {
      subs.forEach(sub => {
        sub.unsubscribe();
      });
    };
  });
}
