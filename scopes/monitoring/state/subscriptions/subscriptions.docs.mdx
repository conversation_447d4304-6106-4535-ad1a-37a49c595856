---
description: 'Subscriptions redux(slice state, selector, action, saga) 组件.'
labels: ['redux', 'slice-state', 'subscriptions']
---

WebSocket Subscriptions

- Realtime data subscriptions

- Alarms data subscriptions

- \[Page\] Alarms monitoring board subscriptions

## TODOs

[x] One WebSocket connection
[x] Independent subscriptions

## 使用

1. 集成 `reducer(slice state)`

```js
import subscriptionsSliceReducer from '@manyun/monitoring.state.subscriptions';

const rootReducer = {
  ...subscriptionsSliceReducer,
  // other slice reducers...
};
```

2. 集成 `saga`

```js
import { all } from 'redux-saga/effects';
import { subscriptionsWatchers } from '@manyun/monitoring.state.subscriptions';

const function* rootSaga() {
  yield all(
    ...subscriptionsWatchers,
    // other sagas...
  );
};
```

## 测试

### Realtime Data Subscriptions

```ts
window.__dcbaseSubscriptionsTest({
  type: 'realtime-data',
  payload: {
    data: {
      '1010108': {
        time: Date.now(),
        pointValues: {
          '1009000': {
            pointCode: '1009000',
            dataValue: 12.33,
          },
        },
      },
    },
  },
});
```

或者在 Redux Dev Tools 中 dispatch actions

```js
let action = {
  type: 'monitoring.subscriptions/receivedRealtimeData',
  payload: {
    '29875c1a0ebb4a058c48230c7f7fb767': {
      time: 1668411347545,
      pointValues: {
        1001000: {
          status: { code: '0' },
          value: 1.3,
        },
        1002000: {
          status: { code: '0' },
          value: 5.6,
        },
      },
    },
  },
};
```

### Alarms Subscriptions

```ts
window.__dcbaseSubscriptionsTest({
  type: 'alarms-data',
  payload: {
    blockGuid: 'sxdtyg.5',
    data: {
      /* device GUID */ c427a16aa5eb4fd9ab673299c4915b86: {
        blockGuid: 'sxdtyg.5',
        time: Date.now(),
        totalErrorAlarm: 1,
        totalWarnAlarm: 0,
        pointErrorAlarms: { '2001000': 1 },
        pointWarnAlarms: null,
      },
    },
  },
});
```

### Alarms Monitoring Board Subscriptions

```ts
(() => {
  function generateAlarm(id, level, message) {
    return {
      id,
      itemId: 3291,
      threshold: '<= 342V',
      mergeRuleId: 0,
      gridCount: 0,
      deviceCount: 2,
      customerCount: 0,
      notifyCount: 0,
      deviceType: '10323',
      deviceGuid: '1032323',
      triggerGuid: '1032323',
      deviceTag: null,
      deviceName: 'A1-1低压出线柜抽屉式开关23',
      deviceLabel: 'A1-1低压出线柜抽屉式开关23tag',
      gridTag: null,
      columnTag: null,
      pointCode: '1004000',
      pointCodeName: '线电压Uab',
      unit: 'V',
      validLimits: ['le=15000', 'ge=0'],
      dataType: 'AI',
      triggerSnapshot: '9.0',
      pointValue: null,
      idcTag: 'EC06',
      blockTag: 'A',
      roomTag: 'A1-1',
      triggerDimension: 'DEVICE',
      alarmStatus: 'ACTIVE',
      triggerStatus: 'TRIGGER',
      triggerCondition: 'LOWER',
      confirmByName: null,
      confirmBy: null,
      removeBy: null,
      alarmLevel: level,
      alarmType: 'ERROR',
      notifyContent: message,
      mergeCount: 0,
      eventId: null,
      changeId: null,
      alarmReason: null,
      removeDesc: null,
      gmtCreate: Date.now(),
      triggerTime: Date.now(),
      activeTime: Date.now(),
      confirmTime: null,
      recoverTime: null,
    };
  }

  window.__dcbaseSubscriptionsTest({
    type: 'alarms-monitoring-board',
    payload: {
      blockGuid: 'EC06.A',
      data: {
        285370: {
          type: 'ADD',

          ...generateAlarm(
            285370,
            '8',
            '[8级8级8级8级8级]告警，04-21 18:16:54A楼A1-1包间出现线电压Uab告警，告警设备：A1-1低压出线柜抽屉式开关23，机柜影响面：影响机柜0个'
          ),
        },
        285371: {
          type: 'ADD',

          ...generateAlarm(
            285371,
            '9',
            '[9级9级9级9级9级]告警，04-21 18:16:54A楼A1-1包间出现线电压Uab告警，告警设备：A1-1低压出线柜抽屉式开关23，机柜影响面：影响机柜0个'
          ),
        },
        285372: {
          type: 'ADD',

          ...generateAlarm(
            285372,
            '10',
            '[10级10级10级10级]告警，04-21 18:16:54A楼A1-1包间出现线电压Uab告警，告警设备：A1-1低压出线柜抽屉式开关23，机柜影响面：影响机柜0个'
          ),
        },
      },
    },
  });

  window.setTimeout(() => {
    window.__dcbaseSubscriptionsTest({
      type: 'alarms-monitoring-board',
      payload: {
        blockGuid: 'EC06.A',
        data: {
          285380: {
            type: 'ADD',

            ...generateAlarm(
              285370,
              '8',
              '[8级8级8级8级8级]告警，04-21 18:16:54A楼A1-1包间出现线电压Uab告警，告警设备：A1-1低压出线柜抽屉式开关23，机柜影响面：影响机柜0个'
            ),
          },
          285379: {
            type: 'ADD',

            ...generateAlarm(
              285371,
              '9',
              '[9级9级9级9级9级]告警，04-21 18:16:54A楼A1-1包间出现线电压Uab告警，告警设备：A1-1低压出线柜抽屉式开关23，机柜影响面：影响机柜0个'
            ),
          },
          285378: {
            type: 'ADD',

            ...generateAlarm(
              285372,
              '10',
              '[10级10级10级10级]告警，04-21 18:16:54A楼A1-1包间出现线电压Uab告警，告警设备：A1-1低压出线柜抽屉式开关23，机柜影响面：影响机柜0个'
            ),
          },
        },
      },
    });
  }, 6 * 1000);
})();
```
