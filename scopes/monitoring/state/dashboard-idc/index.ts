import ticketWatchers from './dashboard-idc.saga';
import { ticketSlice } from './dashboard-idc.slice';

export type { TicketSliceState, Todo } from './dashboard-idc.slice';
export { TodoType, TodoTypeText } from './dashboard-idc.slice';
export * from './dashboard-idc.action';
export * from './dashboard-idc.selector';
export { ticketWatchers };
export default {
  [ticketSlice.name]: ticketSlice.reducer,
};
