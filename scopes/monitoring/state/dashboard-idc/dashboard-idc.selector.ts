import sortBy from 'lodash.sortby';

// import { JobType } from '@manyun/ticket.model.task';
import { TodoType, TodoTypeText, ticketSlice } from './dashboard-idc.slice';
import type { TicketSliceState } from './dashboard-idc.slice';

export const selectTodosWithTime =
  (date: string) => (storeState: { [ticketSlice.name]: TicketSliceState }) => {
    const { todos, visitRecords } = storeState[ticketSlice.name];
    const data: {
      type: TodoType;
      typeText: TodoTypeText;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      data: any[];
    }[] = [];
    const dateData = todos[date];
    if (!dateData) {
      return [];
    }
    const visitors = sortBy(
      dateData.filter(todo => todo.type === TodoType.VISITORS),
      'time'
    );
    if (visitors.length) {
      data.push({
        type: visitors[0].type,
        typeText: visitors[0].typeText,
        data: visitors,
      });
    }
    const tasks = sortBy(
      dateData.filter(todo => todo.type === TodoType.TASK),
      'time'
    );
    if (tasks.length) {
      data.push({
        type: tasks[0].type,
        typeText: tasks[0].typeText,
        data: tasks,
      });
    }
    const exams = sortBy(
      dateData.filter(todo => todo.type === TodoType.EXAM),
      'time'
    );
    if (exams.length) {
      data.push({
        type: exams[0].type,
        typeText: exams[0].typeText,
        data: exams,
      });
    }

    data.push({
      type: TodoType.VistRecords,
      typeText: TodoTypeText.VistRecords,
      data: visitRecords,
    });

    return data;
  };

export const selectTodosTimes = () => (storeState: { [ticketSlice.name]: TicketSliceState }) => {
  const { todos } = storeState[ticketSlice.name];
  return sortBy(Object.keys(todos));
};
