---
description: 'Ticket redux(slice state, selector, action, saga) 组件.'
labels: ['redux', 'slice-state', 'label3']
---

## 使用

1. 集成 `reducer(slice state)`

```js
import ticketSliceReducer from '@manyun/[scope].state.ticket';

const rootReducer = {
  ...ticketSliceReducer,
  // other slice reducers...
};
```

2. 集成 `saga`

```js
import { all } from 'redux-saga/effects';
import { ticketWatchers } from '@manyun/[scope].state.ticket';

const function* rootSaga() {
  yield all(
    ...ticketWatchers,
    // other sagas...
  );
};
```