import dayjs from 'dayjs';
import moment from 'moment';
import { all, call, fork, put, takeLatest } from 'redux-saga/effects';
import type { SagaReturnType } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import {
  BackendExamStatus,
  Variant,
  fetchExamsWeb,
} from '@manyun/knowledge-hub.service.dcexam.fetch-exams';
import { fetchPagedVisitsRecords } from '@manyun/sentry.service.fetch-paged-visits-records';
import { Task } from '@manyun/ticket.model.task';
import { fetchCalendarTasks } from '@manyun/ticket.service.fetch-calendar-tasks';
import { fetchPagedVisitors } from '@manyun/ticket.service.fetch-paged-visitors';

import { getToDosAction, ticketSliceActions } from './dashboard-idc.action';
import { TodoType, TodoTypeText, Todos } from './dashboard-idc.slice';

/** Workers */

export function* getToDos({
  payload: { startTime, endTime, idc },
}: ReturnType<typeof getToDosAction>) {
  const [tasks, exams, visitors, visitsRecords]: [
    SagaReturnType<typeof fetchCalendarTasks>,
    SagaReturnType<typeof fetchExamsWeb>,
    SagaReturnType<typeof fetchPagedVisitors>,
    SagaReturnType<typeof fetchPagedVisitsRecords>
  ] = yield all([
    // 任务
    call<typeof fetchCalendarTasks>(fetchCalendarTasks, {
      startDate: dayjs(startTime).format('YYYY-MM-DD'),
      endDate: dayjs(endTime).format('YYYY-MM-DD'),
      idcTag: idc,
    }),
    // 考试
    call<typeof fetchExamsWeb>(fetchExamsWeb, {
      variant: Variant.AllExam,
      examStatus: [BackendExamStatus.INIT, BackendExamStatus.PROCESS], //  考试中
      timeRange: [startTime, endTime],
      page: 1,
      pageSize: 5000,
      idcTag: idc,
    }),
    // 访客
    call<typeof fetchPagedVisitors>(fetchPagedVisitors, {
      idcTag: idc,
      approveStartTime: startTime,
      approveEndTime: endTime,
      pageNum: 1,
      pageSize: 5000,
      taskType: 'VISITOR',
      taskStatus: '1',
    }),
    // 入园超时人员
    call<typeof fetchPagedVisitsRecords>(fetchPagedVisitsRecords, {
      idc,
      enteredAtRange: [null, dayjs().subtract(12, 'hours').valueOf()],
      status: 'ENTERED',
      page: 1,
      pageSize: 200,
    }),
  ]);
  if (tasks.error) {
    message.error(tasks.error.message);
  }
  if (exams.error) {
    message.error(exams.error.message);
  }
  if (visitors.error) {
    message.error(visitors.error.message);
  }
  // 当前时间的零点
  const todosMapping: Todos = {};
  tasks.data?.data?.forEach(({ localDate, calendarExecuteInfoSet }) => {
    if (!calendarExecuteInfoSet.length) {
      return;
    }
    const todoTask = calendarExecuteInfoSet.map(task => ({
      time: moment(localDate).valueOf(),
      triggerTime: Task.fromJSON(task).getFormattedEffectedTime('HH:mm'),
      title: task.name,
      id: task.id,
      type: TodoType.TASK,
      typeText: TodoTypeText.TASK,
      jobType: task.jobType,
    }));
    todosMapping[localDate] = [...(todosMapping[localDate] || []), ...todoTask];
  });
  Array(31)
    .fill(null)
    .forEach((_, index) => {
      const startOfThisDay = dayjs(startTime + index * 24 * 60 * 60 * 1000).startOf('day');
      const startOfThisDayTimestamp = startOfThisDay.valueOf();
      const formattedStartOfDay = startOfThisDay.format('YYYY-MM-DD');

      exams.data?.data?.forEach(exam => {
        if (!exam.startTime || !exam.endTime) {
          return;
        }
        // 开始时间所在的当天显示具体的时间，到结束日期中间的每一天都显示00:00，这里判断 currentTime 是否是 开始时间所在的当天
        const examsStartTimeFormat = moment(exam.startTime).format('YYYY-MM-DD');
        const examsEndTimeFormat = moment(exam.endTime).format('YYYY-MM-DD');

        if (examsStartTimeFormat === formattedStartOfDay) {
          todosMapping[formattedStartOfDay] = [
            ...(todosMapping[formattedStartOfDay] || []),
            {
              id: exam.id,
              title: exam.name,
              time: exam.startTime,
              type: TodoType.EXAM,
              typeText: TodoTypeText.EXAM,
              isStartTime: true,
              examStatus: exam.status,
            },
          ];
        } else if (examsEndTimeFormat === formattedStartOfDay) {
          todosMapping[formattedStartOfDay] = [
            ...(todosMapping[formattedStartOfDay] || []),
            {
              id: exam.id,
              title: exam.name,
              time: exam.endTime,
              type: TodoType.EXAM,
              typeText: TodoTypeText.EXAM,
              isEndTime: true,
              examStatus: exam.status,
            },
          ];
        } else if (
          startOfThisDayTimestamp > exam.startTime &&
          startOfThisDayTimestamp <= exam.endTime
        ) {
          todosMapping[formattedStartOfDay] = [
            ...(todosMapping[formattedStartOfDay] || []),
            {
              id: exam.id,
              title: exam.name,
              time: startOfThisDayTimestamp,
              type: TodoType.EXAM,
              typeText: TodoTypeText.EXAM,
              examStatus: exam.status,
            },
          ];
        }
      });

      visitors.data?.data?.forEach(({ taskProperties, id, title }) => {
        const approveStartTime = taskProperties.approveStartTime;
        const approveEndTime = taskProperties.approveEndTime;
        if (!approveStartTime || !approveEndTime) {
          return;
        }
        // 授权开始时间所在的当天显示具体的时间，到授权结束日期中间的每一天都显示00:00，这里判断 currentTime 是否是 开始时间所在的当天
        const visitorStartTimeFormat = dayjs(approveStartTime).format('YYYY-MM-DD');
        const visitorEndTimeFormat = dayjs(approveEndTime).format('YYYY-MM-DD');

        if (visitorStartTimeFormat === formattedStartOfDay) {
          todosMapping[formattedStartOfDay] = [
            ...(todosMapping[formattedStartOfDay] || []),
            {
              id: id,
              title,
              time: approveStartTime,
              type: TodoType.VISITORS,
              isStartTime: true,
              typeText: TodoTypeText.VISITORS,
            },
          ];
        } else if (visitorEndTimeFormat === formattedStartOfDay) {
          todosMapping[formattedStartOfDay] = [
            ...(todosMapping[formattedStartOfDay] || []),
            {
              id: id,
              title,
              time: approveEndTime,
              type: TodoType.VISITORS,
              isEndTime: true,
              typeText: TodoTypeText.VISITORS,
            },
          ];
        } else if (
          startOfThisDayTimestamp > approveStartTime &&
          startOfThisDayTimestamp <= approveEndTime
        ) {
          todosMapping[formattedStartOfDay] = [
            ...(todosMapping[formattedStartOfDay] || []),
            {
              id: id,
              title,
              time: startOfThisDayTimestamp,
              type: TodoType.VISITORS,
              typeText: TodoTypeText.VISITORS,
            },
          ];
        }
      });
    });
  yield put(
    ticketSliceActions.setTodos({
      todos: todosMapping,
      visitRecords: visitsRecords.data.data.sort((a, b) => a.enteredAt - b.enteredAt),
    })
  );
}

/** Watchers */

function* watchGetToDos() {
  yield takeLatest(getToDosAction.type, getToDos);
}

// eslint-disable-next-line import/no-anonymous-default-export
export default [fork(watchGetToDos)];
