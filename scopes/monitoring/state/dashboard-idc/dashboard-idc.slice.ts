import { createSlice } from '@reduxjs/toolkit';
import type { CaseReducer, PayloadAction } from '@reduxjs/toolkit';

import { BackendExamStatus } from '@manyun/knowledge-hub.service.dcexam.fetch-exams';
import { VisitRecordJSON } from '@manyun/sentry.model.visit-record';
import { JobType } from '@manyun/ticket.model.task';

export enum TodoType {
  VISITORS = 'visitors',
  TASK = 'task',
  EXAM = 'exam',
  VistRecords = 'visit-records',
}

/**
 * @deprecated Use locales
 */
export enum TodoTypeText {
  VISITORS = '访客',
  TASK = '任务',
  EXAM = '考试',
  VistRecords = '入园超时人员',
}

export type Todo = {
  /** 待办事项时间 */
  time: number;
  /** 待办事项标题 */
  title: string;
  id: number | string;
  type: TodoType;
  typeText: TodoTypeText;
  isStartTime?: boolean;
  isEndTime?: boolean;
  /** 试卷状态 */
  examStatus?: BackendExamStatus;
  /** 任务触发时间 */
  triggerTime?: string;
  jobType?: JobType;
};

export type Todos = Record<string, Todo[]>;

export type TicketSliceState = {
  /**
   * @deprecated 需要拆分字段分开维护
   */
  todos: Todos;
  visitRecords: VisitRecordJSON[];
};

type TicketSliceCaseReducers = {
  setTodos: CaseReducer<
    TicketSliceState,
    PayloadAction<{
      todos: Todos;
      visitRecords: VisitRecordJSON[];
    }>
  >;
};

export const ticketSlice = createSlice<
  TicketSliceState,
  TicketSliceCaseReducers,
  'monitoring.dashboard.idc'
>({
  name: 'monitoring.dashboard.idc',
  initialState: {
    todos: {},
    visitRecords: [],
  },
  reducers: {
    setTodos(sliceState, { payload: { todos: legacyTodos, visitRecords } }) {
      sliceState.todos = legacyTodos;
      sliceState.visitRecords = visitRecords;
    },
  },
});
