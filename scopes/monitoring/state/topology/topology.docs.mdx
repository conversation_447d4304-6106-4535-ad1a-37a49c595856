---
description: 'Topology redux(slice state, selector, action, saga) 组件.'
labels: ['redux', 'slice-state', 'topology']
---

## 使用

1. 集成 `reducer(slice state)`

```js
import topologySliceReducer from '@manyun/monitoring.state.topology';

const rootReducer = {
  ...topologySliceReducer,
  // other slice reducers...
};
```

2. 集成 `saga`

```js
import { all } from 'redux-saga/effects';
import { topologyWatchers } from '@manyun/monitoring.state.topology';

const function* rootSaga() {
  yield all(
    ...topologyWatchers,
    // other sagas...
  );
};
```
