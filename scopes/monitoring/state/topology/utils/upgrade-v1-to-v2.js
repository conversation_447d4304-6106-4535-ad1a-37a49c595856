import { getContainerRect } from '@manyun/dc-brain.aura-graphix';

/**
 * Upgrade data structure V1 -> V2
 * @param {import('@/biz-types/topology').v1.TopologyJSON} data V1 data structure
 * @param {import('@manyun/dc-brain.legacy.utils/ConfigUtil').default} configUtil
 * @returns {import('@/biz-types/topology').v2.TopologyJSON}
 */
export function upgradeV1ToV2({ groups, nodes }, configUtil) {
  const rect = getContainerRect(nodes);

  /**
   * @type {import('@/biz-types/topology').v2.Page['children']}
   */
  const elements = nodes.map(node => {
    if (node.abstractType === 'device') {
      const elementConfig = configUtil.getTopologyElementConfig(node.nodeType);
      const width = elementConfig?.size[0] || node.width;
      const height = elementConfig?.size[1] || node.height;
      let x = node.x;
      if (node.shape === 'RPP') {
        x = node.x + 25;
      } else if (node.shape === 'transformer') {
        x = node.x + 10;
      }
      // 变压器检修隔离柜
      if (node.nodeType === '10107') {
        x = node.x + 32;
      }
      const deviceGroup = {
        id: node.id,
        type: 'group',
        canUngroup: false,
        x, //  === rect.x ? 0 : node.x - rect.x,
        y: node.y, // === rect.y ? 0 : node.y - rect.y,
        width,
        height,
        rotation: node.rotation,
        anchorPointsConfig: elementConfig?.anchorPointsConfig || node.anchorPointsConfig,
        anchorPointsPlacementConfig: elementConfig?.anchorPointsPlacementConfig,
        edges: node.edges,
        custom: {
          __deprecated_shape: node.shape,
          __deprecated_img: node.img,
          type: 'device',
          deviceGuid: node.id,
          deviceType: node.nodeType,
          spaceGuid: node.spaceGuid,
          extendPosition: node.extendPosition,
          name: node.name,
          remarkTextRowKeys: node.remarkTexts?.map(remarkText => remarkText.rowKey),
          visiblePointCodes: node.visiblePoints?.map(point => point.code),
        },
        children: [
          {
            custom: {
              type: 'device/image',
            },
            type: 'image',
            src: elementConfig?.thumbnail,
            id: node.id + '_$$_device',
            x: 0,
            y: 0,
            width,
            height,
            locked: true,
          },
        ],
      };

      return deviceGroup;
    }

    if (node.nodeType === 'remark-text') {
      return {
        id: node.id,
        type: 'text',
        x: node.x, // === rect.x ? 0 : node.x - rect.x,
        y: node.y, // === rect.y ? 0 : node.y - rect.y,
        width: node.width,
        height: node.fontSize,
        text: node.text,
        fontSize: node.fontSize,
        rotation: node.rotation,
        custom: {
          type: 'remark-text',
        },
      };
    } else if (node.nodeType === 'point-text') {
      return {
        id: node.point.deviceGuid + '_$$_' + node.point.code,
        type: 'text',
        x: node.x, // === rect.x ? 0 : node.x - rect.x,
        y: node.y, // === rect.y ? 0 : node.y - rect.y,
        width: node.width,
        height: node.fontSize,
        text: node.textTemplate,
        fontSize: node.fontSize,
        rotation: node.rotation,
        custom: {
          type: 'point-text',
          point: node.point,
          textTemplate: node.textTemplate,
        },
      };
    } else if (node.nodeType === 'bus-line') {
      return {
        id: node.id,
        type: 'group',
        canUngroup: false,
        x: node.x, // === rect.x ? 0 : node.x - rect.x,
        y: node.y, // === rect.y ? 0 : node.y - rect.y,
        width: node.width,
        height: node.height,
        anchorPointsConfig: node.anchorPointsConfig,
        edges:
          node.edges?.map((edge, idx) => ({
            id: edge.id,
            linkPointIndex: idx,
            linkType: edge.linkType,
          })) || [],
        rotation: node.rotation,
        custom: {
          type: 'bus-way',
          name: '母线',
        },
        children: [
          {
            custom: {
              type: 'bus-way_rect',
            },
            id: node.id + '_$$_bus-way',
            type: 'rect',
            x: 0,
            y: 0,
            width: node.width,
            height: node.height,
            fill: node.fill,
            locked: true,
          },
        ],
      };
    }

    if (node.shape === 'polyline') {
      return {
        id: node.id,
        type: 'polyline',
        x: node.x,
        y: node.y,
        width: node.width,
        height: node.height,
        showArrows: true,
        strokeWidth: 6,
        points: node.points,
        // .map((pointXOrY, idx) => {
        //   if (idx % 2 === 0) {
        //     return pointXOrY === rect.x ? 0 : pointXOrY - rect.x;
        //   }
        //   return pointXOrY === rect.y ? 0 : pointXOrY - rect.y;
        // }),
        source: node.source,
        target: node.target,
        startPointPlacement: node.startPointPlacement,
        endPointPlacement: node.endPointPlacement,
      };
    }

    throw new Error('Can not upgrade node: ', node);
  });

  return {
    pages: [
      {
        id: 'default-page',
        padding: 1000,
        ...rect,
        scale: 1,
        children: elements,
      },
    ],
  };
}
