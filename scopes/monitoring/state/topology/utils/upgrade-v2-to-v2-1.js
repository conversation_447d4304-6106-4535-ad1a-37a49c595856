/**
 * Upgrade data structure V2 -> V2.1
 * @param {import('@/biz-types/topology').v2.TopologyJSON} data V2 data structure
 * @returns {import('@/biz-types/topology').v2.TopologyJSON}
 */

const traverse = element => {
  if (element && element.type === 'group') {
    if (element.anchorPointsPlacementConfig.length) {
      element.defaultAnchorPointsPlacementConfig = element.anchorPointsPlacementConfig;
    }

    if (element.children) {
      element.children.forEach(element => traverse(element));
    }
  }

  return element;
};

export function upgradeV2ToV2Point1(data) {
  data.pages[0].children.forEach(element => {
    traverse(element);
  });

  return data;
}
