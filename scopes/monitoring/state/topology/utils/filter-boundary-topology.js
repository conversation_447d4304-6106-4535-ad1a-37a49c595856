import uniqBy from 'lodash.uniqby';

import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';

import { flattenDeepElements } from '@manyun/dc-brain.aura-graphix';

import * as relatedElementUtil from './related-element';

function generateNodeOf(findConnectedNodes, comparator) {
  const recurse = (node, depth = 0) => {
    if (depth > 0 && comparator(node.custom?.deviceType, node.id)) {
      return true;
    }

    // It's nether a parent nor a child of any node.
    if (!(Array.isArray(node.edges) && node.edges.length)) {
      return false;
    }

    const connectedNodes = findConnectedNodes(node.edges);

    return connectedNodes.some(connectedNode => recurse(connectedNode, depth + 1));
  };

  return recurse;
}

function filterGeneratorTopology(topology, utils) {
  const others = [];
  const generators = [];
  topology.forEach(item => {
    if (!['device', 'bus-way'].includes(item.custom?.type)) {
      others.push(item);
      return;
    }
    if (utils.typeofGenerator(item.custom.deviceType)) {
      generators.push(item);
    }
  });

  const nodes = [];

  let skipComparatorForGenerator;
  const stopAtGisg = element => utils.typeofGisg(element.custom?.deviceType);
  generators.forEach((generator, index) => {
    if (index === 1) {
      skipComparatorForGenerator = element => nodes.some(node => node.id === element.id);
    }
    const children = findChildrenRecursively(generator, {
      findChildren: utils.findChildItems,
      skipComparator: skipComparatorForGenerator,
      stopComparator: stopAtGisg,
    });
    [generator, ...children].forEach(child => {
      if (nodes.some(node => node.id === child.id)) {
        return;
      }

      nodes.push(child);
    });
  });

  others.forEach(item => {
    if (
      (['line', 'polyline'].includes(item.type) &&
        utils.hasOne(nodes, item.source.id) &&
        utils.hasOne(nodes, item.target.id)) ||
      (item.type === 'text' && utils.relatedNodeExists(nodes, item.id))
    ) {
      nodes.push(item);
    }
  });

  return nodes;
}

function filterHvTopology(topology, utils) {
  const others = [];
  const hvisocs = [];
  const gics = [];
  const generators = [];
  topology.forEach(item => {
    if (!['device', 'bus-way'].includes(item.custom?.type)) {
      others.push(item);
      return;
    }
    if (utils.typeofHvisoc(item.custom.deviceType)) {
      hvisocs.push(item);
      return;
    } else if (utils.typeofGenerator(item.custom.deviceType)) {
      generators.push(item);
    } else if (utils.typeofGisg(item.custom.deviceType)) {
      gics.push(item);
    }
  });

  const nodes = [...gics];

  const { embed, showGenerators } = getLocationSearchMap(window.location.search, [
    'embed',
    'showGenerators',
  ]);
  // 作为 iframe 嵌入到 easyV 大屏的时候，URL 参数中需要带 `embed=true`
  // 这样在拆分高（中）压拓扑图时不会把柴发（包含）到柴发进线柜之间的设备显示出来
  // 但是如果 `showGenerators=true`，则仍显示
  if (!embed || showGenerators === 'true') {
    nodes.push(...generators);

    let skipComparatorForGenerator;
    const stopAtGisgOrTransformer = element =>
      utils.typeofGisg(element.custom?.deviceType) ||
      // 如果图中没有配 GISG（柴发进线柜），则最多只显示到 TRANSFORMER（变压器）
      utils.typeofTransformer(element.custom?.deviceType);
    generators.forEach((generator, index) => {
      if (index === 1) {
        skipComparatorForGenerator = element => nodes.some(node => node.id === element.id);
      }
      const children = findChildrenRecursively(generator, {
        findChildren: utils.findChildItems,
        skipComparator: skipComparatorForGenerator,
        stopComparator: stopAtGisgOrTransformer,
      });
      [generator, ...children].forEach(child => {
        if (nodes.some(node => node.id === child.id)) {
          return;
        }

        nodes.push(child);
      });
    });
  }

  let skipComparatorForHvisoc;
  const stopAtTransformer = element => utils.typeofTransformer(element.custom?.deviceType);

  hvisocs.forEach((hvisoc, index) => {
    // 为了优化性能，我们认为第一次递归后，大部分相同下联的设备都被找到了
    // 所以只在第二次生成 `skipComparator`
    if (index === 1) {
      skipComparatorForHvisoc = element => nodes.some(node => node.id === element.id);
    }
    const children = findChildrenRecursively(hvisoc, {
      findChildren: utils.findChildItems,
      skipComparator: skipComparatorForHvisoc,
      stopComparator: stopAtTransformer,
    });
    [hvisoc, ...children].forEach(child => {
      if (nodes.some(node => node.id === child.id)) {
        return;
      }

      nodes.push(child);
    });
  });

  others.forEach(item => {
    if (
      (['line', 'polyline'].includes(item.type) &&
        utils.hasOne(nodes, item.source.id) &&
        utils.hasOne(nodes, item.target.id)) ||
      (item.type === 'text' && utils.relatedNodeExists(nodes, item.id))
    ) {
      nodes.push(item);
    }
  });

  return nodes;
}

/**
 * 拆分低压视图
 * 变压器需分组：参考任务：http://chandao.manyun-local.com/zentao/task-view-170.html
 * @param {*} topology
 * @param {*} utils
 * @returns
 */
function filterLvTopology(topology, utils) {
  const childNodeOfTransformer = generateNodeOf(utils.findParentItems, utils.typeofTransformer);

  const startsWithTransformerNodes = [];
  const hiddenNodeIds = [];
  const nodes = [];
  const others = [];
  topology.forEach(item => {
    if (!['device', 'bus-way'].includes(item.custom?.type)) {
      others.push(item);
      return;
    }
    if (utils.typeofTransformer(item.custom.deviceType)) {
      startsWithTransformerNodes.push([item]);

      // 把变压器的直接上联及其上联的上联也存起来，但是要在拓扑中隐藏掉（`visible: false`）
      // 用于判断变压器的状态及其下联连线的状态
      const uplinkElements = utils.findParentItems(item.edges || []);
      const extraUplinkElements = utils.findParentItems(uplinkElements?.[0]?.edges ?? []);
      const hideElement = uplinkElement => ({ ...uplinkElement, visible: false });
      startsWithTransformerNodes[startsWithTransformerNodes.length - 1].push(
        ...uplinkElements.map(hideElement),
        ...extraUplinkElements.map(hideElement)
      );
      const getElementId = uplinkElement => uplinkElement.id;
      hiddenNodeIds.push(
        ...uplinkElements.map(getElementId),
        ...extraUplinkElements.map(getElementId)
      );
    }
    if (utils.typeofLvbtc(item.custom.deviceType) || childNodeOfTransformer(item)) {
      nodes.push(item);
    }
  });

  // 每个变压器及下联设备分为一组
  startsWithTransformerNodes.forEach(([/* 第一个为变压器 */ transformer], idx) => {
    const children = findChildrenRecursively(transformer, { findChildren: utils.findChildItems });
    children.forEach(child => {
      if (startsWithTransformerNodes[idx].some(node => node.id === child.id)) {
        return;
      }

      startsWithTransformerNodes[idx].push(child);
    });
  });

  const transformerGroups = [];
  // 若无下联的低压联络柜，则无法分组
  const validTransformers = startsWithTransformerNodes.filter(
    nodes => nodes.length > 1 && nodes.some(node => utils.typeofLvbtc(node.custom.deviceType))
  );
  // 找到具有相同低压联络柜/相连低压联络柜的变压器组，分为同一组
  validTransformers.forEach(([transformer1, ...children1], idx) => {
    const lvbtc = children1.find(node => utils.typeofLvbtc(node.custom.deviceType));
    validTransformers.forEach(([transformer2, ...children2], innerIdx) => {
      if (
        idx === innerIdx ||
        transformerGroups.some(group => group.key.includes(transformer2.id))
      ) {
        return;
      }
      const innerLvbtc = children2.find(node => utils.typeofLvbtc(node.custom.deviceType));
      // 如果2个变压器的下联低压联络柜是同一个，或者2个低压联络柜相连，则这2个变压器应分为一组
      if (
        lvbtc.id === innerLvbtc.id ||
        lvbtc.edges.some(edge => innerLvbtc.edges.some(innerEdge => innerEdge.id === edge.id))
      ) {
        const edge1 = transformer1.edges.find(edge => edge.linkType === 'source');
        const line1 = others.find(item => item.id === edge1.id);
        const targetNodeOfTransformer1 = children1.find(node => node.id === line1.target.id);
        const offset1 = {
          x:
            targetNodeOfTransformer1.x +
            targetNodeOfTransformer1.width / 2 -
            transformer1.x -
            transformer1.width / 2,
          y: targetNodeOfTransformer1.y - transformer1.height - 180 - transformer1.y,
        };

        const edge2 = transformer2.edges.find(edge => edge.linkType === 'source');
        const line2 = others.find(item => item.id === edge2.id);
        const targetNodeOfTransformer2 = children2.find(node => node.id === line2.target.id);
        const offset2 = {
          x:
            targetNodeOfTransformer2.x +
            targetNodeOfTransformer2.width / 2 -
            transformer2.x -
            transformer2.width / 2,
          y: targetNodeOfTransformer2.y - transformer2.height - 180 - transformer2.y,
        };
        const groupNodes = [
          {
            ...transformer1,
            x: transformer1.x + offset1.x,
            y: transformer1.y + offset1.y,
          },
          {
            ...transformer2,
            x: transformer2.x + offset2.x,
            y: transformer2.y + offset2.y,
          },
          ...uniqBy([...children1, ...children2], 'id'),
        ];
        others.forEach(item => {
          if (
            ['line', 'polyline'].includes(item.type) &&
            utils.hasOne(groupNodes, item.source.id) &&
            utils.hasOne(groupNodes, item.target.id)
          ) {
            if (item.id === line1.id) {
              groupNodes.push({
                ...item,
                points: [
                  item.points[0] + offset1.x,
                  item.points[1] + offset1.y,
                  item.points[item.points.length - 2],
                  item.points[item.points.length - 1],
                ],
              });
            } else if (item.id === line2.id) {
              groupNodes.push({
                ...item,
                points: [
                  item.points[0] + offset2.x,
                  item.points[1] + offset2.y,
                  item.points[item.points.length - 2],
                  item.points[item.points.length - 1],
                ],
              });
            } else {
              groupNodes.push({
                ...item,
                visible:
                  !hiddenNodeIds.includes(item.source.id) &&
                  !hiddenNodeIds.includes(item.target.id),
              });
            }
          } else if (item.type === 'text' && utils.relatedNodeExists(groupNodes, item.id)) {
            const [parentNodeId] = item.id.split('_$$_');
            if (parentNodeId === transformer1.id) {
              groupNodes.push({ ...item, x: item.x + offset1.x, y: item.y + offset1.y });
            } else if (parentNodeId === transformer2.id) {
              groupNodes.push({ ...item, x: item.x + offset2.x, y: item.y + offset2.y });
            } else if (!hiddenNodeIds.includes(parentNodeId)) {
              groupNodes.push(item);
            }
          }
        });
        transformerGroups.push({
          key: transformer1.id + '_$$_' + transformer2.id,
          name: transformer1.custom.name + ' & ' + transformer2.custom.name,
          nodes: groupNodes,
        });
      }
    });
  });

  // 若未拆分出变压器组，且存在变压器及其下联设备
  // 则认为每个变压器为1组
  if (transformerGroups.length <= 0 && startsWithTransformerNodes.length > 0) {
    return startsWithTransformerNodes.map(nodes => {
      const extraNodes = [];

      others.forEach(item => {
        if (
          ['line', 'polyline'].includes(item.type) &&
          utils.hasOne(nodes, item.source.id) &&
          utils.hasOne(nodes, item.target.id)
        ) {
          extraNodes.push(item);
        } else if (item.type === 'text' && utils.relatedNodeExists(nodes, item.id)) {
          extraNodes.push(item);
        }
      });

      return {
        key: nodes[0].id,
        name: nodes[0].name,
        nodes: [...nodes, ...extraNodes],
      };
    });
  }

  return transformerGroups;
}

/**
 * 根据拓扑边界（topologyBoundary）过滤出对应的元素集合
 *
 * > 注意：返回的元素集合是已被解除编组的（即拉平后的数据，不会存在人为的编组）
 *
 * @param {objects[]} elements 拓扑中所有元素集合
 * @param {string} topologyBoundary 拓扑边界
 * @param {object} extraUtils 额外的工具方法。如：判断设备类型的（typeofGenerator）
 * @returns
 */
export function filterTopologyByBoundaryType(elements, topologyBoundary, extraUtils) {
  // 先解除所有可解除的编组，方便后续遍历
  const topology = flattenDeepElements(elements);

  const relatedNodeExists = (data, textId) =>
    data.some(item => {
      return (
        (item.custom?.remarkTextRowKeys &&
          item.custom.remarkTextRowKeys.some(rowKey => textId.endsWith(rowKey))) ||
        (item.custom?.visiblePointCodes &&
          item.custom.visiblePointCodes.some(
            pointCode => relatedElementUtil.join(item.custom.deviceGuid, pointCode) === textId
          ))
      );
    });

  const findOne = itemId => topology.find(({ id }) => itemId === id);

  const findChildItems = generateFindConnectedNodes('source', 'target', { findOne });
  const findParentItems = generateFindConnectedNodes('target', 'source', { findOne });

  const utils = { ...extraUtils, hasOne, relatedNodeExists, findChildItems, findParentItems };

  switch (topologyBoundary) {
    case 'GENERATOR':
      return filterGeneratorTopology(topology, utils);
    case 'HV':
      return filterHvTopology(topology, utils);
    case 'LV':
      return filterLvTopology(topology, utils);
    default:
      return topology;
  }
}

function hasOne(data, itemId) {
  return data.findIndex(({ id }) => itemId === id) > -1;
}

function generateFindConnectedNodes(edgeLinkType, oppositeEdgeLinkType, { findOne }) {
  return edges => {
    const oppositeNodes = edges.reduce((nodes, { id, linkType }) => {
      if (linkType === edgeLinkType) {
        const line = findOne(id);
        if (!line) {
          return nodes;
        }

        const node = findOne(line[oppositeEdgeLinkType].id);
        if (!node) {
          return nodes;
        }

        if (!hasOne(nodes, node.id)) {
          nodes.push(node);
        }
      }

      return nodes;
    }, []);

    return oppositeNodes;
  };
}

// /**
//  * @param {object} element
//  * @param {{ findParents: Function; skipComparator: Function; stopComparator: Function }} utils
//  * @returns
//  */
// function findParentsRecursively(element, { findParents, skipComparator, stopComparator }) {
//   const parents = [];

//   const recurse = edges => {
//     if (!(Array.isArray(edges) && edges.length)) {
//       return;
//     }

//     const parentElements = findParents(edges);
//     parentElements.forEach(elem => {
//       if (typeof skipComparator == 'function' && skipComparator(elem)) {
//         return;
//       }

//       if (!parents.some(parent => parent.id === elem.id)) {
//         parents.push(elem);
//       }

//       if (typeof stopComparator == 'function' && stopComparator(elem)) {
//         return;
//       }

//       recurse(elem.edges);
//     });
//   };

//   recurse(element.edges);

//   return parents;
// }

/**
 * @param {object} element
 * @param {{ findChildren: Function; skipComparator: Function; stopComparator: Function }} utils
 * @returns
 */
function findChildrenRecursively(element, { findChildren, skipComparator, stopComparator }) {
  const children = [];

  const recurse = edges => {
    if (!(Array.isArray(edges) && edges.length)) {
      return;
    }
    const childElements = findChildren(edges);
    childElements.forEach(childElement => {
      if (typeof skipComparator == 'function' && skipComparator(childElement)) {
        return;
      }

      if (!children.some(child => child.id === childElement.id)) {
        children.push(childElement);
      }

      if (typeof stopComparator == 'function' && stopComparator(childElement)) {
        return;
      }

      recurse(childElement.edges);
    });
  };

  recurse(element.edges);

  return children;
}
