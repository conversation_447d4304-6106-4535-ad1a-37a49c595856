import { nanoid } from 'nanoid';

const DELIMITER = '_$$_';

/**
 * @param hostId The host element ID
 * @returns
 */
export function generate(hostId: string) {
  const id = nanoid();

  return `${hostId}${DELIMITER}${id}`;
}

/**
 * @param hostId The host element ID
 * @param id The related element ID
 * @returns
 */
export function join(hostId: string, id: string) {
  return `${hostId}${DELIMITER}${id}`;
}

/**
 * @param fullId The host element ID and the related element ID concatenated together
 * @returns
 */
export function split(fullId: string) {
  return fullId.split(DELIMITER);
}
