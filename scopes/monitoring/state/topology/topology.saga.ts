/* eslint-disable @typescript-eslint/no-explicit-any */
import cloneDeep from 'lodash.clonedeep';
import { call, fork, put, select, takeLatest } from 'redux-saga/effects';
import type { SagaReturnType, SelectEffect } from 'redux-saga/effects';

import type {
  AnyElementSnapshotOut,
  GroupElementSnapshotOut,
  LineElementSnapshotOut,
  RectElementInstance,
  StoreSnapshotOut,
  TextElementSnapshotOut,
} from '@manyun/dc-brain.aura-graphix';
import { flattenDeepElements, getContainerRect, traverse } from '@manyun/dc-brain.aura-graphix';
import type { BackendTopologyType, Config, TopologyBoundary } from '@manyun/dc-brain.config.base';
import { syncCommonDataAction } from '@manyun/dc-brain.state.common';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { fetchTopology } from '@manyun/monitoring.service.fetch-topology';
import { Colors as ThemeColors } from '@manyun/monitoring.theme.topology-theme';
import type { BackendDevice } from '@manyun/resource-hub.model.device';
import { fetchDevices } from '@manyun/resource-hub.service.fetch-devices';
import { getSpaceGuid } from '@manyun/resource-hub.util.space-guid';

import {
  changeTransformerGroupAction,
  getAllBoundaryGraphsAction,
  getBlockFullGraphAction,
  getBlockGensetsGraphAction,
  getBlockHVGraphAction,
  getBlockLVGraphAction,
  topologySliceActions,
} from './topology.action';
import { selectBlockGraph, selectBlockLVGraph } from './topology.selector';
import type { GraphPreviewResultCode } from './topology.slice';
import { filterTopologyByBoundaryType } from './utils/filter-boundary-topology';
import { upgradeV1ToV2 } from './utils/upgrade-v1-to-v2';
import { upgradeV2ToV2Point1 } from './utils/upgrade-v2-to-v2-1';

function fixProperties(snapshot: StoreSnapshotOut) {
  traverse(snapshot.pages[0].children, (node: AnyElementSnapshotOut) => {
    if (
      node.custom?.type === 'bus-way' &&
      !(node as GroupElementSnapshotOut).allowAddAnchorPoints
    ) {
      (node as GroupElementSnapshotOut).allowAddAnchorPoints = true;
    }
  });

  return snapshot;
}

const filterDeviceGuids = (graph: StoreSnapshotOut) => {
  const deviceGuids: string[] = [];

  graph.pages[0].children.forEach(element => {
    if (element.custom?.type === 'device') {
      deviceGuids.push(element.custom.deviceGuid);
    }
  });

  return deviceGuids;
};

function electricGraphTraverseVistor(node: AnyElementSnapshotOut) {
  if (node.type === 'polyline' || node.type === 'line') {
    (node as LineElementSnapshotOut).stroke = ThemeColors.LineColor;
  } else if (node.type === 'text') {
    if (node.custom?.type === 'point-text') {
      (node as TextElementSnapshotOut).fill = ThemeColors.AnchorTextColor;
      (node as TextElementSnapshotOut).stroke = ThemeColors.AnchorTextColor;
    } else {
      (node as TextElementSnapshotOut).fill = ThemeColors.TextColor;
      (node as TextElementSnapshotOut).stroke = ThemeColors.TextColor;
    }
  } else if (node.custom?.type === 'bus-way') {
    ((node as GroupElementSnapshotOut).children[0] as RectElementInstance).fill =
      ThemeColors.LineColor;
  }
}
function updatePreviewAttrs(
  graph: StoreSnapshotOut,
  elements: AnyElementSnapshotOut[],
  { traverseVisitor = electricGraphTraverseVistor } = {}
) {
  // 根据过滤后的元素重新生成容器 Rect
  // 以便 AuraGraphix 加载数据后计算出最合适的适应画布的缩放比例
  const { x, y, width, height } = getContainerRect(elements);

  const copy = cloneDeep({
    ...graph,
    pages: [
      {
        ...graph.pages[0],
        background: 'transparent',
        padding: 16,
        x,
        y,
        width,
        height,
        children: elements,
      },
    ],
  });

  traverse(copy.pages[0].children, node => {
    if (node.type === 'polyline' || node.type === 'line') {
      (node as LineElementSnapshotOut).showArrows = false;
      node.listening = false;
    } else if (node.type === 'text') {
      if (node.custom?.type === 'point-text') {
        (node as TextElementSnapshotOut).text = (node as TextElementSnapshotOut).text.replace(
          // eslint-disable-next-line no-template-curly-in-string
          '${value}',
          '--'
        );
      }
    } else if (node.custom?.type === 'pipe-network' || node.custom?.type === 'bus-way') {
      node.listening = false;
    }
    traverseVisitor(node);
  });

  return copy;
}

/**
 * 生成一系列 `typeofDeviceType` 的工具方法
 * @param predefinedDeviceTypes
 * @returns
 */
function* generateTypeofDeviceTypeUtils(
  predefinedDeviceTypes: { funcName: string; predefinedDeviceType: string }[]
) {
  const config: SagaReturnType<typeof selectCurrentConfig> = yield select(selectCurrentConfig);
  const configUtil = new ConfigUtil(config);

  const utils = predefinedDeviceTypes.reduce<Record<string, (deviceType: string) => boolean>>(
    (acc, { funcName, predefinedDeviceType }) => {
      acc[funcName] = configUtil.typeofDeviceGen(predefinedDeviceType);

      return acc;
    },
    {}
  );

  return utils;
}

/** Workers */

function* getDeviceTypesByTopologyData(graph: StoreSnapshotOut) {
  const config: SagaReturnType<typeof selectCurrentConfig> = yield select(selectCurrentConfig);
  const configUtil = new ConfigUtil(config);

  const blockDeviceType = configUtil.getOneDeviceType(
    ConfigUtil.constants.deviceTypes.SPACE_BLOCK
  )!;
  const deviceTypes = graph.pages[0].children.reduce<string[]>(
    (types, { custom }) => {
      if (custom?.type !== 'device' || types.includes(custom?.deviceType)) {
        return types;
      }
      types.push(custom.deviceType);

      return types;
    },
    [blockDeviceType]
  );

  return deviceTypes;
}

function* syncBlockDevicesPointDefinitions(graph: StoreSnapshotOut) {
  const deviceTypes: SagaReturnType<typeof getDeviceTypesByTopologyData> =
    yield getDeviceTypesByTopologyData(graph);
  if (!deviceTypes?.length) {
    return;
  }
  yield put(
    syncCommonDataAction({
      strategy: {
        deviceTypesPointsDefinition: deviceTypes,
      },
    })
  );
}

function* getGraphDevices({
  idc,
  block,
  graph,
}: {
  idc: string;
  block: string;
  graph: StoreSnapshotOut;
}) {
  const deviceGuids = filterDeviceGuids(graph);
  if (!deviceGuids.length) {
    return null;
  }

  const { error, data }: SagaReturnType<typeof fetchDevices> = yield call<typeof fetchDevices>(
    fetchDevices,
    {
      idc,
      deviceGuids,
    }
  );
  if (error) {
    return null;
  }

  return data.data.map(device => device.toApiObject());
}

function* filterGensetsGraph(
  graph: StoreSnapshotOut
): Generator<SelectEffect, StoreSnapshotOut | null, Config> {
  const config: SagaReturnType<typeof selectCurrentConfig> = yield select(selectCurrentConfig);
  const configUtil = new ConfigUtil(config);

  const generatorTypes =
    configUtil.getAllDeviceTypes(ConfigUtil.constants.deviceTypes.GENERATOR) || [];
  const typeofGenerator = (deviceType: string) => generatorTypes.includes(deviceType);

  const gisgTypes =
    configUtil.getAllDeviceTypes(ConfigUtil.constants.deviceTypes.GENERATOR_INCOMING_CABINET) || [];
  const typeofGisg = (deviceType: string) => gisgTypes.includes(deviceType);

  const utils = { typeofGenerator, typeofGisg };

  const gensetElements = filterTopologyByBoundaryType(graph.pages[0].children, 'GENERATOR', utils);

  if (gensetElements.length <= 0) {
    return null;
  }

  return updatePreviewAttrs(graph, gensetElements);
}

type GetGraphOptions = {
  idc: string;
  block: string;
  topologyType: BackendTopologyType;
  extras?: {
    topologyBoundary?: TopologyBoundary;
    tenantId?: string | null;
  };
};
type GetGraphResult = {
  type?: 'cache';
  code: GraphPreviewResultCode;
  id: number | null;
  graph: StoreSnapshotOut | null;
};
function* getGraph({
  idc,
  block,
  topologyType,
  extras,
}: GetGraphOptions): Generator<any, GetGraphResult, any> {
  const blockGuid = getSpaceGuid(idc, block)!;

  const selector = selectBlockGraph(blockGuid, topologyType, extras?.topologyBoundary);
  const existingBlockGraph: SagaReturnType<typeof selector> = yield select(selector);

  const result: GetGraphResult = { code: 'no-graph', id: null, graph: null };

  const now = Date.now();
  if (
    existingBlockGraph!.graph !== null &&
    now - (existingBlockGraph!.updatedAt || now) <= 60 * 1000
  ) {
    result.type = 'cache';
    result.code = 'success';
    result.id = existingBlockGraph!.id;
    result.graph = existingBlockGraph!.graph;

    return result;
  }

  const { error, data }: SagaReturnType<typeof fetchTopology> = yield call<typeof fetchTopology>(
    fetchTopology,
    {
      blockGuid,
      topologyType,

      // iframe 嵌入到 easyv 里时需要传递租户 ID
      tenantId: extras?.tenantId,
    }
  );
  if (error) {
    result.code = 'error';

    return result;
  } else if (!data) {
    return result;
  }

  const config: SagaReturnType<typeof selectCurrentConfig> = yield select(selectCurrentConfig);
  const configUtil = new ConfigUtil(config);

  let graph = data.graph as any;
  if ((graph.version === undefined || graph.version === 1) && topologyType === 'ELECTRIC_POWER') {
    graph = upgradeV1ToV2(data, configUtil);
  } else if (graph.version === 2) {
    graph = upgradeV2ToV2Point1(graph.snapshot);
  } else if (graph.version === 2.1) {
    graph = fixProperties(graph.snapshot);
  }
  result.code = 'success';
  result.id = data.id;
  result.graph = graph as StoreSnapshotOut;

  return result;
}

type BlockGraphInfos = {
  idc: string;
  block: string;
  topologyType: BackendTopologyType;
  topologyBoundary: TopologyBoundary;
  code: GraphPreviewResultCode;
  id: number | null;
  graph: StoreSnapshotOut | null;
};
type GetBlockGraphInfosOptions = {
  idc: string;
  block: string;
  topologyType: BackendTopologyType;
  topologyBoundary: TopologyBoundary;
  tenantId?: string | null;
};
function* getBlockGraphInfos({
  idc,
  block,
  topologyType,
  topologyBoundary,
  tenantId,
}: GetBlockGraphInfosOptions): Generator<any, BlockGraphInfos, any> {
  const { code, id, graph }: SagaReturnType<typeof getGraph> = yield getGraph({
    idc,
    block,
    topologyType,
    extras: { topologyBoundary, tenantId },
  });

  // 为了方便在 Redux logs / devtools 中查看当前拓扑数据的相关信息
  // 以下字段不一定会在 action handler 中用到
  const result: BlockGraphInfos = {
    idc,
    block,
    topologyType,
    topologyBoundary,
    code,
    id: null,
    graph: null,
  };

  if (code === 'error' || code === 'no-graph') {
    return result;
  }

  if (code === 'success' && graph) {
    result.code = 'success';
    result.id = id;
    result.graph = graph;

    return result;
  }

  console.error(`code(${code}) not implemented.`);
  return result;
}

export function* getBlockGensetsGraphSaga({
  payload: { idc, block, tenantId },
}: ReturnType<typeof getBlockGensetsGraphAction>) {
  const blockGuid = getSpaceGuid(idc, block)!;
  const topologyType = 'ELECTRIC_POWER';
  const topologyBoundary = 'GENERATOR';
  yield put(
    topologySliceActions.fetchTopologyStart({
      blockGuid,
      topologyType,
      topologyBoundary,
    })
  );

  const { code, id, graph }: SagaReturnType<typeof getBlockGraphInfos> = yield getBlockGraphInfos({
    idc,
    block,
    topologyType,
    topologyBoundary,
    tenantId,
  });
  let _gensetsGraph = graph;
  let _devices: BackendDevice[] = [];
  if (graph) {
    _gensetsGraph = yield filterGensetsGraph(graph);

    if (_gensetsGraph) {
      yield syncBlockDevicesPointDefinitions(_gensetsGraph);

      const devices: SagaReturnType<typeof getGraphDevices> = yield getGraphDevices({
        idc,
        block,
        graph: _gensetsGraph,
      });
      if (devices) {
        _devices = devices;
      }
    }
  }

  yield put(
    topologySliceActions.setTopology({
      blockGuid,
      topologyType,
      topologyBoundary,
      blockGraph: {
        code: _gensetsGraph ? code : 'empty-graph',
        id,
        graph: _gensetsGraph,
        custom: {
          devices: _devices,
        },
      },
    })
  );
}

function* filterHVGraph(graph: StoreSnapshotOut) {
  const utils: SagaReturnType<typeof generateTypeofDeviceTypeUtils> =
    yield generateTypeofDeviceTypeUtils([
      {
        funcName: 'typeofGenerator',
        predefinedDeviceType: ConfigUtil.constants.deviceTypes.GENERATOR,
      },
      {
        funcName: 'typeofGisg',
        predefinedDeviceType: ConfigUtil.constants.deviceTypes.GENERATOR_INCOMING_CABINET,
      },
      {
        funcName: 'typeofHvbtc',
        predefinedDeviceType: ConfigUtil.constants.deviceTypes.HV_BUS_TIE_CABINET,
      },
      {
        funcName: 'typeofHvic',
        predefinedDeviceType: ConfigUtil.constants.deviceTypes.HV_INCOMING_SWITCHGEAR,
      },
      {
        funcName: 'typeofHvoc',
        predefinedDeviceType: ConfigUtil.constants.deviceTypes.HV_OUTGOING_CABINET,
      },
      {
        funcName: 'typeofHvisoc',
        predefinedDeviceType: ConfigUtil.constants.deviceTypes.HV_ISOLATING_CABINET,
      },
      {
        funcName: 'typeofHvptc',
        predefinedDeviceType: ConfigUtil.constants.deviceTypes.HV_PT_CABINET,
      },
      {
        funcName: 'typeofTransformer',
        predefinedDeviceType: ConfigUtil.constants.deviceTypes.TRANSFORMER,
      },
    ]);

  const HVElements = filterTopologyByBoundaryType(graph.pages[0].children, 'HV', utils);

  if (HVElements.length <= 0) {
    return null;
  }

  return updatePreviewAttrs(graph, HVElements);
}

export function* getBlockHVGraphSaga({
  payload: { idc, block, tenantId, topologyType = 'ELECTRIC_POWER' },
}: ReturnType<typeof getBlockHVGraphAction>): Generator<any, void, any> {
  const blockGuid = getSpaceGuid(idc, block)!;
  const topologyBoundary = 'HV';
  yield put(
    topologySliceActions.fetchTopologyStart({
      blockGuid,
      topologyType,
      topologyBoundary,
    })
  );

  const { code, id, graph }: SagaReturnType<typeof getBlockGraphInfos> = yield getBlockGraphInfos({
    idc,
    block,
    topologyType,
    topologyBoundary,
    tenantId,
  });
  let _hvGraph = graph;
  let _devices: BackendDevice[] = [];
  if (graph) {
    _hvGraph = yield filterHVGraph(graph);

    if (_hvGraph) {
      yield syncBlockDevicesPointDefinitions(_hvGraph);

      const devices: SagaReturnType<typeof getGraphDevices> = yield getGraphDevices({
        idc,
        block,
        graph: _hvGraph,
      });
      if (devices) {
        _devices = devices;
      }
    }
  }

  yield put(
    topologySliceActions.setTopology({
      blockGuid,
      topologyType,
      topologyBoundary,
      blockGraph: {
        code: _hvGraph ? code : 'empty-graph',
        id,
        graph: _hvGraph,
        custom: {
          devices: _devices,
        },
      },
    })
  );
}

export type LVGraphItem = {
  key: string;
  name: string;
  graph: StoreSnapshotOut;
};
function* filterLVGraph(graph: StoreSnapshotOut) {
  const utils: SagaReturnType<typeof generateTypeofDeviceTypeUtils> =
    yield generateTypeofDeviceTypeUtils([
      {
        funcName: 'typeofTransformer',
        predefinedDeviceType: ConfigUtil.constants.deviceTypes.TRANSFORMER,
      },
      {
        funcName: 'typeofLvbtc',
        predefinedDeviceType: ConfigUtil.constants.deviceTypes.LV_BUS_TIE_CABINET,
      },
    ]);

  const transformerGroups = filterTopologyByBoundaryType(graph.pages[0].children, 'LV', utils);

  return transformerGroups.map(({ nodes, ...rest }) => ({
    ...rest,
    graph: updatePreviewAttrs(graph, nodes),
  }));
}

export function* getBlockLVGraphSaga({
  payload: { idc, block, tenantId, topologyType = 'ELECTRIC_POWER' },
}: ReturnType<typeof getBlockLVGraphAction>) {
  const blockGuid = getSpaceGuid(idc, block)!;
  const topologyBoundary = 'LV';
  yield put(
    topologySliceActions.fetchTopologyStart({
      blockGuid,
      topologyType,
      topologyBoundary,
    })
  );

  const { code, id, graph }: SagaReturnType<typeof getBlockGraphInfos> = yield getBlockGraphInfos({
    idc,
    block,
    topologyType,
    topologyBoundary,
    tenantId,
  });
  let _activeTransformerGroupKey: string | null = null;
  let _lvGraph: StoreSnapshotOut | null = null;
  let _lvGraphs: LVGraphItem[] = [];
  let _devices: BackendDevice[] = [];
  const _extraDevices: BackendDevice[] = [];
  if (graph) {
    _lvGraphs = yield filterLVGraph(graph);

    if (_lvGraphs.length > 0) {
      _activeTransformerGroupKey = _lvGraphs[0].key;
      _lvGraph = _lvGraphs[0].graph;
      yield syncBlockDevicesPointDefinitions(_lvGraph);

      const devices: SagaReturnType<typeof getGraphDevices> = yield getGraphDevices({
        idc,
        block,
        graph: _lvGraph,
      });
      if (devices) {
        _devices = devices;
      }
    }
  }

  yield put(
    topologySliceActions.setTopology({
      blockGuid,
      topologyType,
      topologyBoundary,
      blockGraph: {
        code: _lvGraph ? code : 'empty-graph',
        id,
        graph: _lvGraph,
        custom: {
          devices: _devices,
          extraDevices: _extraDevices,
          lvGraphs: _lvGraphs,
          activeTransformerGroupKey: _activeTransformerGroupKey,
        },
      },
    })
  );
}

export function* getBlockFullGraphSaga({
  payload: { idc, block, tenantId, topologyType = 'LIQUID_ELECTRIC' },
}: ReturnType<typeof getBlockLVGraphAction>) {
  const blockGuid = getSpaceGuid(idc, block)!;
  const topologyBoundary = 'LV';
  yield put(
    topologySliceActions.fetchTopologyStart({
      blockGuid,
      topologyType,
      topologyBoundary,
    })
  );

  const { code, id, graph }: SagaReturnType<typeof getBlockGraphInfos> = yield getBlockGraphInfos({
    idc,
    block,
    topologyType,
    topologyBoundary,
    tenantId,
  });
  const _activeTransformerGroupKey: string | null = null;
  let _devices: BackendDevice[] = [];
  const _extraDevices: BackendDevice[] = [];
  let _graph = null;
  if (graph) {
    _graph = updatePreviewAttrs(graph, graph?.pages[0].children);
    yield syncBlockDevicesPointDefinitions(_graph);

    _graph.pages = _graph.pages.map(page => ({
      ...page,
      children: flattenDeepElements(page.children), // 拍平拿到设备
    }));

    const devices: SagaReturnType<typeof getGraphDevices> = yield getGraphDevices({
      idc,
      block,
      graph: _graph,
    });

    if (devices) {
      _devices = devices;
    }
  }

  yield put(
    topologySliceActions.setTopology({
      blockGuid,
      topologyType,
      topologyBoundary,
      blockGraph: {
        code: _graph ? code : 'empty-graph',
        id,
        graph: _graph,
        custom: {
          devices: _devices,
          extraDevices: _extraDevices,
          activeTransformerGroupKey: _activeTransformerGroupKey,
        },
      },
    })
  );
}

function* changeTransformerGroupSaga({
  payload: { idc, block, key },
}: ReturnType<typeof changeTransformerGroupAction>) {
  const blockGuid = getSpaceGuid(idc, block)!;
  const topologyType = 'ELECTRIC_POWER';
  const topologyBoundary = 'LV';
  yield put(
    topologySliceActions.toggleLoading({ blockGuid, topologyType, topologyBoundary, loading: true })
  );

  const _selectBlockLVGraph = selectBlockLVGraph(blockGuid);
  const blockLVGraph: SagaReturnType<typeof _selectBlockLVGraph> =
    yield select(_selectBlockLVGraph);
  const { graph } = blockLVGraph.custom!.lvGraphs.find(({ key: _key }) => _key === key)!;
  yield syncBlockDevicesPointDefinitions(graph);
  const devices: SagaReturnType<typeof getGraphDevices> = yield getGraphDevices({
    idc,
    block,
    graph,
  });

  yield put(
    topologySliceActions.setTopology({
      blockGuid,
      topologyType,
      topologyBoundary,
      blockGraph: {
        code: blockLVGraph.code,
        id: blockLVGraph.id,
        graph,
        custom: {
          ...blockLVGraph.custom!,
          devices,
          activeTransformerGroupKey: key,
        },
      },
    })
  );
}

export function* getAllBoundaryGraphsSaga(action: ReturnType<typeof getAllBoundaryGraphsAction>) {
  yield call(getBlockGensetsGraphSaga, action);
  yield call(getBlockHVGraphSaga, action);
  yield call(getBlockLVGraphSaga, action);
}

/** Watchers */

function* watchGetBlockGensetsGraph() {
  yield takeLatest(getBlockGensetsGraphAction.type, getBlockGensetsGraphSaga);
}

function* watchGetBlockHVGraph() {
  yield takeLatest(getBlockHVGraphAction.type, getBlockHVGraphSaga);
}

function* watchGetBlockLVGraph() {
  yield takeLatest(getBlockLVGraphAction.type, getBlockLVGraphSaga);
}

function* watchGetBlockFullGraph() {
  yield takeLatest(getBlockFullGraphAction.type, getBlockFullGraphSaga);
}

function* watchChangeTransformerGroup() {
  yield takeLatest(changeTransformerGroupAction.type, changeTransformerGroupSaga);
}

function* watchGetAllBoundaryGraphs() {
  yield takeLatest(getAllBoundaryGraphsAction.type, getAllBoundaryGraphsSaga);
}

export default [
  fork(watchGetBlockGensetsGraph),
  fork(watchGetBlockHVGraph),
  fork(watchGetBlockLVGraph),
  fork(watchChangeTransformerGroup),
  fork(watchGetAllBoundaryGraphs),
  fork(watchGetBlockFullGraph),
];
