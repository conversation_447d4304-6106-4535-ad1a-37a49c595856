import { createSlice } from '@reduxjs/toolkit';
import type { CaseReducer, PayloadAction } from '@reduxjs/toolkit';

import type { StoreSnapshotOut as Graph } from '@manyun/dc-brain.aura-graphix';
import type { TopologyBoundary, TopologyType } from '@manyun/dc-brain.config.base';

export type GraphPreviewResultCode = 'error' | 'no-graph' | 'empty-graph' | 'success';

/**
 * @example
 *
 * ```ts
 * 'EC01.A_$$_ELECTRIC_POWER'
 * 'EC01.A_$$_ELECTRIC_POWER_$$_GENERATOR'
 * 'EC01.A_$$_ELECTRIC_POWER_$$_HV'
 * 'EC01.A_$$_ELECTRIC_POWER_$$_LV'
 * 'EC01.A_$$_HVAC'
 * ```
 */
type BlockGraphUniqueKey = string;
export type BlockGraph<TCustom = unknown> = {
  loading: boolean;
  code: GraphPreviewResultCode | null;
  id: number | null;
  graph: Graph | null;
  /**
   * 可存储额外的自定义属性
   *
   * - 低压视图：存储当前选中的变压器组的 Key
   */
  custom?: TCustom;
  updatedAt: number;
};

export type TopologySliceState = {
  /**
   * 楼维度的完整电力、拆分后的电力（柴发、高压、低压）、暖通拓扑
   */
  blockGraphs: Record<BlockGraphUniqueKey, BlockGraph | undefined>;
};

type TopologySliceCaseReducers = {
  toggleLoading: CaseReducer<
    TopologySliceState,
    PayloadAction<{
      blockGuid: string;
      topologyType: TopologyType;
      topologyBoundary: TopologyBoundary;
      loading: boolean;
    }>
  >;
  fetchTopologyStart: CaseReducer<
    TopologySliceState,
    PayloadAction<{
      blockGuid: string;
      topologyType: TopologyType;
      topologyBoundary: TopologyBoundary;
    }>
  >;
  setTopology: CaseReducer<
    TopologySliceState,
    PayloadAction<{
      blockGuid: string;
      topologyType: TopologyType;
      topologyBoundary: TopologyBoundary;
      blockGraph: Pick<BlockGraph, 'code' | 'id' | 'graph' | 'custom'>;
    }>
  >;
  fetchTopologyError: CaseReducer<
    TopologySliceState,
    PayloadAction<{
      blockGuid: string;
      topologyType: TopologyType;
      topologyBoundary: TopologyBoundary;
    }>
  >;
  setCustom: CaseReducer<
    TopologySliceState,
    PayloadAction<{
      blockGuid: string;
      topologyType: TopologyType;
      topologyBoundary: TopologyBoundary;
      custom: unknown;
    }>
  >;
};

const SEPARATOR = '_$$_' as const;

export function generateBlockGraphUniqueKey(
  blockGuid: string,
  topologyType: TopologyType,
  topologyBoundary?: TopologyBoundary
) {
  return [blockGuid, topologyType, topologyBoundary].join(SEPARATOR);
}

export function splitBLockGraphUniqueKey(uniqueKey: string) {
  const [blockGuid, topologyType, topologyBoundary] = uniqueKey.split(SEPARATOR);

  return {
    blockGuid,
    topologyType: topologyType as TopologyType,
    topologyBoundary: topologyBoundary as TopologyBoundary,
  };
}

export const topologySlice = createSlice<
  TopologySliceState,
  TopologySliceCaseReducers,
  'monitoring.topology'
>({
  name: 'monitoring.topology',
  initialState: {
    blockGraphs: {},
  },
  reducers: {
    toggleLoading(sliceState, { payload: { blockGuid, topologyType, topologyBoundary, loading } }) {
      const uniqueKey = generateBlockGraphUniqueKey(blockGuid, topologyType, topologyBoundary);
      if (sliceState.blockGraphs[uniqueKey]) {
        sliceState.blockGraphs[uniqueKey]!.loading = loading;
      }
    },
    fetchTopologyStart(sliceState, { payload: { blockGuid, topologyType, topologyBoundary } }) {
      const uniqueKey = generateBlockGraphUniqueKey(blockGuid, topologyType, topologyBoundary);
      sliceState.blockGraphs[uniqueKey] = {
        ...sliceState.blockGraphs[uniqueKey],
        loading: true,
        code: null,
        id: null,
        graph: null,
        custom: null,
        updatedAt: Date.now(),
      };
    },
    setTopology(
      sliceState,
      { payload: { blockGuid, topologyType, topologyBoundary, blockGraph } }
    ) {
      const uniqueKey = generateBlockGraphUniqueKey(blockGuid, topologyType, topologyBoundary);
      sliceState.blockGraphs[uniqueKey] = {
        ...sliceState.blockGraphs[uniqueKey],
        ...blockGraph,
        loading: false,
        updatedAt: Date.now(),
      };
    },
    fetchTopologyError(sliceState, { payload: { blockGuid, topologyType, topologyBoundary } }) {
      const uniqueKey = generateBlockGraphUniqueKey(blockGuid, topologyType, topologyBoundary);
      sliceState.blockGraphs[uniqueKey]!.loading = false;
    },
    setCustom(sliceState, { payload: { blockGuid, topologyType, topologyBoundary, custom } }) {
      const uniqueKey = generateBlockGraphUniqueKey(blockGuid, topologyType, topologyBoundary);
      if (sliceState.blockGraphs[uniqueKey]) {
        sliceState.blockGraphs[uniqueKey]!.custom = custom;
      }
    },
  },
});
