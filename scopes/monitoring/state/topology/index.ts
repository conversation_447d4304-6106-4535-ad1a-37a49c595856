import topologyWatchers from './topology.saga';
import { topologySlice } from './topology.slice';

export type { TopologySliceState } from './topology.slice';
export { generateBlockGraphUniqueKey, splitBLockGraphUniqueKey } from './topology.slice';
export * from './topology.action';
export * from './topology.selector';
export { getAllBoundaryGraphsSaga } from './topology.saga';
export { topologyWatchers };
export default {
  [topologySlice.name]: topologySlice.reducer,
};

// Custom utilities
// ------
export { filterTopologyByBoundaryType } from './utils/filter-boundary-topology';
export * as relatedElementUtil from './utils/related-element';

// dev use only
// ------
export { createCompositionWrapper } from './topology.composition-wrapper.creator';
