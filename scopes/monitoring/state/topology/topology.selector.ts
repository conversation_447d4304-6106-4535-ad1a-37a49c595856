import type { TopologyBoundary, TopologyType } from '@manyun/dc-brain.config.base';
import type { BackendDevice } from '@manyun/resource-hub.model.device';

import type { LVGraphItem } from './topology.saga';
import { generateBlockGraphUniqueKey, topologySlice } from './topology.slice';
import type { BlockGraph, TopologySliceState } from './topology.slice';

type StoreState = {
  [topologySlice.name]: TopologySliceState;
};

export const selectBlockGraph =
  (blockGuid: string, tpologyType: TopologyType, topologyBoundary?: TopologyBoundary) =>
  (storeState: StoreState) => {
    return storeState[topologySlice.name].blockGraphs[
      generateBlockGraphUniqueKey(blockGuid, tpologyType, topologyBoundary)
    ];
  };

export type BlockGensetsGraph = BlockGraph<{
  devices: BackendDevice[] | null;
} | null>;
export const selectBlockGensetsGraph =
  (blockGuid: string) =>
  (storeState: StoreState): BlockGensetsGraph => {
    const infos =
      storeState[topologySlice.name].blockGraphs[
        generateBlockGraphUniqueKey(blockGuid, 'ELECTRIC_POWER', 'GENERATOR')
      ];

    return {
      loading: infos?.loading ?? false,
      code: infos?.code ?? null,
      id: infos?.id ?? null,
      graph: infos?.graph ?? null,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      custom: (infos?.custom as any) ?? {
        devices: [],
      },
      updatedAt: infos?.updatedAt ?? Date.now(),
    };
  };

export type BlockHVGraph = BlockGraph<{
  devices: BackendDevice[] | null;
} | null>;
export const selectBlockHVGraph =
  (blockGuid: string, topologyType: TopologyType = 'ELECTRIC_POWER') =>
  (storeState: StoreState): BlockHVGraph => {
    const infos =
      storeState[topologySlice.name].blockGraphs[
        generateBlockGraphUniqueKey(blockGuid, topologyType, 'HV')
      ];

    return {
      loading: infos?.loading ?? false,
      code: infos?.code ?? null,
      id: infos?.id ?? null,
      graph: infos?.graph ?? null,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      custom: (infos?.custom as any) ?? {
        devices: [],
      },
      updatedAt: infos?.updatedAt ?? Date.now(),
    };
  };

export type BlockLVGraph = BlockGraph<{
  devices: BackendDevice[] | null;
  extraDevices: BackendDevice[] | null;
  lvGraphs: LVGraphItem[];
  activeTransformerGroupKey: string | null;
} | null>;
export const selectBlockLVGraph =
  (blockGuid: string, topologyType: TopologyType = 'ELECTRIC_POWER') =>
  (storeState: StoreState): BlockLVGraph => {
    const infos =
      storeState[topologySlice.name].blockGraphs[
        generateBlockGraphUniqueKey(blockGuid, topologyType, 'LV')
      ];

    return {
      loading: infos?.loading ?? false,
      code: infos?.code ?? null,
      id: infos?.id ?? null,
      graph: infos?.graph ?? null,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      custom: (infos?.custom as any) ?? {
        devices: [],
        extraDevices: [],
        lvGraphs: [],
        activeTransformerGroupKey: null,
      },
      updatedAt: infos?.updatedAt ?? Date.now(),
    };
  };

export type BlockFullGraph = BlockGraph<{
  devices: BackendDevice[] | null;
  extraDevices: BackendDevice[] | null;
  activeTransformerGroupKey: string | null;
} | null>;
export const selectBlockFullGraph =
  (blockGuid: string, topologyType: TopologyType) =>
  (storeState: StoreState): BlockFullGraph => {
    const infos =
      storeState[topologySlice.name].blockGraphs[
        generateBlockGraphUniqueKey(blockGuid, topologyType, 'LV')
      ];

    return {
      loading: infos?.loading ?? false,
      code: infos?.code ?? null,
      id: infos?.id ?? null,
      graph: infos?.graph ?? null,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      custom: (infos?.custom as any) ?? {
        devices: [],
        extraDevices: [],
        activeTransformerGroupKey: null,
      },
      updatedAt: infos?.updatedAt ?? Date.now(),
    };
  };
