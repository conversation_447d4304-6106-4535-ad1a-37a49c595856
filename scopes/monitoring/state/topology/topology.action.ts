import { createAction } from '@reduxjs/toolkit';

import type { TopologyType } from '@manyun/dc-brain.config.base';

import { topologySlice } from './topology.slice';

const prefix = `${topologySlice.name}/` as const;
const postfix = '/async' as const;

export const topologySliceActions = topologySlice.actions;

const getBlockGensetsGraphActionType = prefix + 'get-block-gensets-graph' + postfix;
export const getBlockGensetsGraphAction = createAction<
  {
    idc: string;
    block: string;
    tenantId?: string | null;
  },
  typeof getBlockGensetsGraphActionType
>(getBlockGensetsGraphActionType);

const getBlockHVGraphActionType = prefix + 'get-block-hv-graph' + postfix;
export const getBlockHVGraphAction = createAction<
  {
    idc: string;
    block: string;
    tenantId?: string | null;
    topologyType?: TopologyType;
  },
  typeof getBlockHVGraphActionType
>(getBlockHVGraphActionType);

const getBlockLVGraphActionType = prefix + 'get-block-lv-graph' + postfix;
export const getBlockLVGraphAction = createAction<
  {
    idc: string;
    block: string;
    tenantId?: string | null;
    topologyType?: TopologyType;
  },
  typeof getBlockLVGraphActionType
>(getBlockLVGraphActionType);

const getBlockFullGraphActionType = prefix + 'get-block-full-graph' + postfix;
export const getBlockFullGraphAction = createAction<
  {
    idc: string;
    block: string;
    tenantId?: string | null;
    topologyType?: TopologyType;
  },
  typeof getBlockFullGraphActionType
>(getBlockFullGraphActionType);

const changeTransformerGroupActionType = prefix + 'change-transformer-group' + postfix;
export const changeTransformerGroupAction = createAction<
  {
    idc: string;
    block: string;
    key: string;
  },
  typeof changeTransformerGroupActionType
>(changeTransformerGroupActionType);

const getAllBoundaryGraphsActionActionType = prefix + 'get-all-boundary-graphs' + postfix;
export const getAllBoundaryGraphsAction = createAction<
  {
    idc: string;
    block: string;
    topologyType?: TopologyType;
  },
  typeof getAllBoundaryGraphsActionActionType
>(getAllBoundaryGraphsActionActionType);
