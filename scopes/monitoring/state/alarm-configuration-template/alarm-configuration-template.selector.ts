import { alarmConfigurationTemplateSlice } from './alarm-configuration-template.slice';
import type {
  AlarmConfigurationTemplateSliceState,
  MutateType,
} from './alarm-configuration-template.slice';

export const selectAlarmConfigurationTemplateEntities =
  (ids?: string[]) =>
  (storeState: {
    [alarmConfigurationTemplateSlice.name]: AlarmConfigurationTemplateSliceState;
  }) => {
    const { entities, all } = storeState[alarmConfigurationTemplateSlice.name];

    if (ids === undefined) {
      return Object.keys(entities).map(id => entities[id]);
    }

    return all.ids.map(id => entities[id]);
  };

export const selectAlarmConfigurationAll = (storeState: {
  [alarmConfigurationTemplateSlice.name]: AlarmConfigurationTemplateSliceState;
}) => {
  return storeState[alarmConfigurationTemplateSlice.name].all;
};

export const selectMutateAlarmConfiguration =
  (mutateType?: MutateType) =>
  (storeState: {
    [alarmConfigurationTemplateSlice.name]: AlarmConfigurationTemplateSliceState;
  }) => {
    return storeState[alarmConfigurationTemplateSlice.name][
      mutateType ? mutateType : storeState[alarmConfigurationTemplateSlice.name].currentMutateType
    ];
  };

export const selectCurrentMutateType = (storeState: {
  [alarmConfigurationTemplateSlice.name]: AlarmConfigurationTemplateSliceState;
}) => {
  return storeState[alarmConfigurationTemplateSlice.name].currentMutateType;
};

export const selectCloneTemplatesEntities = (storeState: {
  [alarmConfigurationTemplateSlice.name]: AlarmConfigurationTemplateSliceState;
}) => {
  return storeState[alarmConfigurationTemplateSlice.name].cloneTemplateEntities;
};

export const selectAlarmConfigurationTemplate = (storeState: {
  [alarmConfigurationTemplateSlice.name]: AlarmConfigurationTemplateSliceState;
}) => {
  return storeState[alarmConfigurationTemplateSlice.name];
};
