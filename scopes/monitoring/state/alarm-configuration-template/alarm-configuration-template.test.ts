import { AlarmTemplateType } from '@manyun/monitoring.model.alarm-template';

import { alarmConfigurationTemplateSlice } from './alarm-configuration-template.slice';
import type { AlarmConfigurationTemplateSliceState } from './alarm-configuration-template.slice';

test('should return the initial state', () => {
  expect(
    alarmConfigurationTemplateSlice.reducer(undefined, {} as any)
  ).toEqual<AlarmConfigurationTemplateSliceState>({
    entities: {},
    cloneTemplateEntities: {},
    currentMutateType: 'create',
    all: {
      loading: false,
      ids: [],
      total: 0,
      field: {},
      selectedIds: [],
    },
    create: {
      type: AlarmTemplateType.Instance,
      enable: true,
      monitorItemEntities: {},
      monitorItemIds: [],
      selectedMonitorItemIds: [],
      notifyConfigMode: 'GLOBAL',
      globalNoticeRules: [],
      validateNotifyErrorIds: [],
      loading: false,
      copyNoticeRules: [],
    },
    update: {
      type: AlarmTemplateType.Instance,
      enable: true,
      monitorItemEntities: {},
      monitorItemIds: [],
      selectedMonitorItemIds: [],
      notifyConfigMode: 'INDEPENDENT', //新建时默认全局配置， 编辑时默认单条配置
      globalNoticeRules: [],
      validateNotifyErrorIds: [],
      loading: false,
      copyNoticeRules: [],
    },
  });
});
