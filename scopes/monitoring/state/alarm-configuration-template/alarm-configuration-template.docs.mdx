---
description: 'AlarmConfigurationTemplate redux(slice state, selector, action, saga) 组件.'
labels: ['redux', 'slice-state', 'label3']
---

## 使用

1. 集成 `reducer(slice state)`

```js
import alarmConfigurationTemplateSliceReducer from '@manyun/[scope].state.alarm-configuration-template';

const rootReducer = {
  ...alarmConfigurationTemplateSliceReducer,
  // other slice reducers...
};
```

2. 集成 `saga`

```js
import { all } from 'redux-saga/effects';
import { alarmConfigurationTemplateWatchers } from '@manyun/[scope].state.alarm-configuration-template';

const function* rootSaga() {
  yield all(
    ...alarmConfigurationTemplateWatchers,
    // other sagas...
  );
};
```