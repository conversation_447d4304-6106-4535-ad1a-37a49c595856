import { createAction } from '@reduxjs/toolkit';

import type { SvcQuery, SvcRespData } from '@manyun/monitoring.service.create-alarm-configuration';

import { MutateType, alarmConfigurationTemplateSlice } from './alarm-configuration-template.slice';

const prefix = alarmConfigurationTemplateSlice.name;

export const alarmConfigurationTemplateSliceActions = alarmConfigurationTemplateSlice.actions;

/**@YJX 参数代办 */
export const getAlarmConfigurationTemplatesAction = createAction<undefined>(
  prefix + '/GET_ALARM_CONFIGURATIONS'
);

export const mutateAlarmConfigurationAction = createAction<{
  mutateType: MutateType;
  alarmTpl: SvcQuery;
  callback?: (data: SvcRespData) => void;
}>(prefix + '/MUTATE_ALARM_CONFIGURATIONS');
