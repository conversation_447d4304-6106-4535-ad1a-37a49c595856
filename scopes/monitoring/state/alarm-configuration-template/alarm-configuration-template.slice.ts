import { createSlice } from '@reduxjs/toolkit';
import type { CaseReducer, PayloadAction } from '@reduxjs/toolkit';
import shortid from 'shortid';

import { AlarmTemplateType } from '@manyun/monitoring.model.alarm-template';
import type { AlarmTemplateJSON } from '@manyun/monitoring.model.alarm-template';
import type { MonitoringItemJSON, NotifyRule } from '@manyun/monitoring.model.monitoring-item';
import { TriggerRuleType } from '@manyun/monitoring.model.trigger-rule';
import type { TriggerRuleJSON } from '@manyun/monitoring.model.trigger-rule';

export type AllSliceState = {
  ids: number[];
  total: number;
  field: {} /**后续待定 */;
  loading: boolean;
  selectedIds: number[];
};
export type MutateType = 'create' | 'update';

export type MutateSliceState = Partial<
  Pick<
    AlarmTemplateJSON,
    | 'id'
    | 'type'
    | 'name'
    | 'target'
    | 'enable'
    | 'associateGroupIds'
    | 'description'
    | 'monitorItems'
  >
> & {
  cloneTemplateId?: number;
  monitorItemEntities: Record<string | number, MonitoringItemJSON>;
  monitorItemIds: (string | number)[];
  selectedMonitorItemIds: (string | number)[];
  notifyConfigMode: 'GLOBAL' | 'INDEPENDENT' /**告警通知配置类型： 全局配置、逐条配置**/;
  globalNoticeRules: NotifyRule[] /**全局配置的通知规则 */;
  validateNotifyErrorIds: string[] /**通知错误id */;
  loading: boolean;
  copyNoticeRules: NotifyRule[] /**复制的告警通知 */;
};

export type CloneTemplate = Pick<AlarmTemplateJSON, 'id' | 'name' | 'enable' | 'description'>;

export type AlarmConfigurationTemplateSliceState = {
  /**
   * an `id: entity` object.
   */
  entities: Record<string, AlarmTemplateJSON>;
  cloneTemplateEntities: Record<number, CloneTemplate>;
  all: AllSliceState;
  currentMutateType: MutateType;
  create: MutateSliceState;
  update: MutateSliceState;
};

export type SetAlarmConfigurationTemplatesFieldActionPayload = {
  page: number;
  pageSize: number;
  //和service查询条件 保持一致
};

export type SetMutateFieldsActionPayload = {
  mutateType: MutateType;
} & Partial<MutateSliceState>;

export type SetMutateMonitorItemFieldsActionPayload = {
  mutateType: MutateType;
  id: string | number /**监控项唯一id */;
} & Partial<MonitoringItemJSON>;

export type SetAddMutateMonitorItemActionPayload = {
  mutateType: MutateType;
} & Partial<MonitoringItemJSON>;

type AlarmConfigurationTemplateSliceCaseReducers = {
  fetchAlarmConfigurationTemplateStart: CaseReducer<
    AlarmConfigurationTemplateSliceState,
    PayloadAction<undefined>
  >;
  setAlarmConfigurationTemplates: CaseReducer<
    AlarmConfigurationTemplateSliceState,
    PayloadAction<{
      data: AlarmTemplateJSON[];
      total: number;
    }>
  >;
  fetchAlarmConfigurationTemplateError: CaseReducer<
    AlarmConfigurationTemplateSliceState,
    PayloadAction<undefined>
  >;
  setAlarmConfigurationTemplatesField: CaseReducer<
    AlarmConfigurationTemplateSliceState,
    PayloadAction<SetAlarmConfigurationTemplatesFieldActionPayload>
  > /**列表查询条件**/;
  setAlarmConfigurationTemplatesSelectedIds: CaseReducer<
    AlarmConfigurationTemplateSliceState,
    PayloadAction<{ selectedIds: number[] }>
  >;
  /**Mutate */
  setCurrentMutateType: CaseReducer<
    AlarmConfigurationTemplateSliceState,
    PayloadAction<{ type: MutateType }>
  >;
  setMutateFields: CaseReducer<
    AlarmConfigurationTemplateSliceState,
    PayloadAction<SetMutateFieldsActionPayload>
  >;
  setMutateMonitorItemFields: CaseReducer<
    AlarmConfigurationTemplateSliceState,
    PayloadAction<SetMutateMonitorItemFieldsActionPayload>
  >;
  addMutateMonitorItem: CaseReducer<
    AlarmConfigurationTemplateSliceState,
    PayloadAction<SetAddMutateMonitorItemActionPayload>
  >;
  deleteMutateMonitorItem: CaseReducer<
    AlarmConfigurationTemplateSliceState,
    PayloadAction<{ mutateType: MutateType; id: string | number }>
  >;
  /**批量更新监控项状态 默认取selectedMonitorItemIds**/
  setMutateMonitorItemsFieldsEnable: CaseReducer<
    AlarmConfigurationTemplateSliceState,
    PayloadAction<{ mutateType: MutateType; ids: (string | number)[]; enable: boolean }>
  >;
  /**更新某个监控项下的触发规则**/
  setMutateMonitorItemTriggerRules: CaseReducer<
    AlarmConfigurationTemplateSliceState,
    PayloadAction<{
      mutateType: MutateType;
      id: string | number;
      ruleType: TriggerRuleType;
      triggerRules: TriggerRuleJSON[] /**新的触发规则 */;
    }>
  >;
  /**更新某个监控项下的通知文案**/
  setMutateMonitorItemNotifyRules: CaseReducer<
    AlarmConfigurationTemplateSliceState,
    PayloadAction<{
      mutateType: MutateType;
      id?: string | number /** 监控项id, 单条配置id必填 */;
      notifyRules: NotifyRule[] /**新的规则 */;
    }>
  >;
  resetMutate: CaseReducer<AlarmConfigurationTemplateSliceState, PayloadAction<MutateType>>;

  setMutateSubmitStart: CaseReducer<
    AlarmConfigurationTemplateSliceState,
    PayloadAction<MutateType>
  >;
  setMutateSubmitEnd: CaseReducer<AlarmConfigurationTemplateSliceState, PayloadAction<MutateType>>;

  setCloneTemplates: CaseReducer<
    AlarmConfigurationTemplateSliceState,
    PayloadAction<{ data: CloneTemplate[] }>
  >;
};

export const alarmConfigurationTemplateSlice = createSlice<
  AlarmConfigurationTemplateSliceState,
  AlarmConfigurationTemplateSliceCaseReducers,
  'monitoring.alarm-configuration-template'
>({
  name: 'monitoring.alarm-configuration-template',
  initialState: {
    entities: {},
    cloneTemplateEntities: {},
    all: {
      loading: false,
      ids: [],
      total: 0,
      field: {},
      selectedIds: [],
    },
    currentMutateType: 'create',
    create: generateMutateInitialState('create'),
    update: generateMutateInitialState('update'),
  },
  reducers: {
    fetchAlarmConfigurationTemplateStart(sliceState) {
      sliceState.all.loading = true;
    },
    setAlarmConfigurationTemplates(sliceState, { payload: { data, total } }) {
      const ids: number[] = [];
      data.forEach(tpl => {
        const tplId = tpl.id as number;
        ids.push(tplId);
        // cache update performance optimization
        const existingAlarmConfigurationTemplate = sliceState.entities[tplId];
        if (
          existingAlarmConfigurationTemplate &&
          existingAlarmConfigurationTemplate.gmtModified === tpl.gmtModified
        ) {
          return;
        }

        sliceState.entities[tplId] = tpl;
      });
      sliceState.all.ids = ids;
      sliceState.all.loading = false;
      sliceState.all.total = total;
    },
    fetchAlarmConfigurationTemplateError(sliceState) {
      sliceState.all.loading = false;
    },
    setAlarmConfigurationTemplatesField(sliceState, { payload }) {},
    setAlarmConfigurationTemplatesSelectedIds(sliceState, { payload: { selectedIds } }) {
      sliceState.all.selectedIds = selectedIds;
    },
    setCurrentMutateType(sliceState, { payload: { type } }) {
      sliceState.currentMutateType = type;
    },
    setMutateFields(sliceState, { payload: { mutateType, ...fields } }) {
      sliceState[mutateType] = { ...sliceState[mutateType], ...fields };
    },
    setMutateMonitorItemFields(sliceState, { payload: { mutateType, id, ...fileds } }) {
      const mutateMonitorItemEntities = sliceState[mutateType].monitorItemEntities;
      if (mutateMonitorItemEntities[id]) {
        mutateMonitorItemEntities[id] = { ...mutateMonitorItemEntities[id], ...fileds };
      }
    },
    addMutateMonitorItem(sliceState, { payload: { mutateType, id, ...item } }) {
      const mutateAlarm = sliceState[mutateType];
      const uniqId: string | number = id ? id : shortid();
      if (!mutateAlarm.monitorItemIds.includes(uniqId)) {
        mutateAlarm.monitorItemIds.push(uniqId);
        mutateAlarm.monitorItemEntities[uniqId] = { ...item, id: uniqId } as MonitoringItemJSON;
      }
    },
    deleteMutateMonitorItem(sliceState, { payload: { mutateType, id } }) {
      const mutateAlarmTpl = sliceState[mutateType];
      if (mutateAlarmTpl.monitorItemIds.includes(id)) {
        mutateAlarmTpl.monitorItemIds = mutateAlarmTpl.monitorItemIds.filter(uniq => uniq !== id);
        mutateAlarmTpl.selectedMonitorItemIds = mutateAlarmTpl.selectedMonitorItemIds.filter(
          d => d !== id
        );
        delete mutateAlarmTpl.monitorItemEntities[id];
      }
    },
    setMutateMonitorItemsFieldsEnable(sliceState, { payload: { mutateType, ids, enable } }) {
      const mutateAlarm = sliceState[mutateType];
      const monitorItemIds = ids ? ids : mutateAlarm.selectedMonitorItemIds;
      monitorItemIds.forEach(uniq => {
        mutateAlarm.monitorItemEntities[uniq] = {
          ...mutateAlarm.monitorItemEntities[uniq],
          enable: enable,
        };
      });
    },
    setMutateMonitorItemTriggerRules(
      sliceState,
      { payload: { mutateType, id, ruleType, triggerRules } }
    ) {
      const mutateAlarm = sliceState[mutateType];
      if (mutateAlarm.monitorItemEntities[id]) {
        if (ruleType === TriggerRuleType.PreCondition) {
          mutateAlarm.monitorItemEntities[id] = {
            ...mutateAlarm.monitorItemEntities[id],
            preTriggerRules: triggerRules,
          };
        } else {
          mutateAlarm.monitorItemEntities[id] = {
            ...mutateAlarm.monitorItemEntities[id],
            triggerRules: triggerRules,
          };
        }
      }
    },
    setMutateMonitorItemNotifyRules(sliceState, { payload: { mutateType, id, notifyRules } }) {
      const mutateAlarm = sliceState[mutateType];
      if (id) {
        if (mutateAlarm.monitorItemEntities[id]) {
          mutateAlarm.monitorItemEntities[id].notifyRules = notifyRules;
          mutateAlarm.monitorItemIds = [...mutateAlarm.monitorItemIds];
        }
      } else {
        mutateAlarm.globalNoticeRules = notifyRules;
      }
    },

    resetMutate(sliceState, { payload }) {
      sliceState[payload] = generateMutateInitialState(payload);
    },

    setMutateSubmitStart(sliceState, { payload }) {
      sliceState[payload].loading = true;
    },

    setMutateSubmitEnd(sliceState, { payload }) {
      sliceState[payload].loading = false;
    },

    setCloneTemplates(sliceState, { payload: { data } }) {
      data.forEach(d => {
        sliceState.cloneTemplateEntities[d.id as number] = d;
      });
    },
  },
});

function generateMutateInitialState(mutateType: MutateType) {
  const obj: MutateSliceState = {
    type: AlarmTemplateType.Device,
    enable: true,
    monitorItemEntities: {},
    monitorItemIds: [],
    selectedMonitorItemIds: [],
    notifyConfigMode: mutateType === 'create' ? 'GLOBAL' : 'INDEPENDENT', //新建时默认全局配置， 编辑时默认单条配置
    globalNoticeRules: [],
    validateNotifyErrorIds: [],
    loading: false,
    copyNoticeRules: [],
  };
  return obj;
}
