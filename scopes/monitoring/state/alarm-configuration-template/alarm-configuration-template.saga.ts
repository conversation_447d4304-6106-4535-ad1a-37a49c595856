import { call, fork, put, takeLatest } from 'redux-saga/effects';
import type { SagaReturnType } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { createAlarmConfiguration } from '@manyun/monitoring.service.create-alarm-configuration';
import { updateAlarmConfiguration } from '@manyun/monitoring.service.update-alarm-configuration';

import {
  alarmConfigurationTemplateSliceActions,
  getAlarmConfigurationTemplatesAction,
  mutateAlarmConfigurationAction,
} from './alarm-configuration-template.action';

/** Workers */

export function* getAlarmConfigurationTemplateSaga({
  payload,
}: ReturnType<typeof getAlarmConfigurationTemplatesAction>) {
  yield put(alarmConfigurationTemplateSliceActions.fetchAlarmConfigurationTemplateStart());

  // const { error, data }: SagaReturnType<typeof someService> = yield call(someService);

  // if (error) {
  //   yield put(alarmConfigurationTemplateSliceActions.fetchAlarmConfigurationTemplateError());
  //   return;
  // }

  // yield put(alarmConfigurationTemplateSliceActions.setAlarmConfigurationTemplate(data));
}

export function* mutateAlarmConfigurationSaga({
  payload: { mutateType, alarmTpl, callback },
}: ReturnType<typeof mutateAlarmConfigurationAction>) {
  yield put(alarmConfigurationTemplateSliceActions.setMutateSubmitStart(mutateType));

  let response:
    | SagaReturnType<typeof createAlarmConfiguration>
    | SagaReturnType<typeof updateAlarmConfiguration>;

  if (mutateType === 'create') {
    response = yield call<typeof createAlarmConfiguration>(createAlarmConfiguration, {
      ...alarmTpl,
    });
  } else {
    response = yield call<typeof updateAlarmConfiguration>(updateAlarmConfiguration, {
      ...alarmTpl,
    });
  }

  yield put(alarmConfigurationTemplateSliceActions.setMutateSubmitEnd(mutateType));

  if (response.error) {
    message.error(response.error.message);
    return;
  }
  callback && callback(response.data);
  yield put(alarmConfigurationTemplateSliceActions.resetMutate(mutateType));
}

/** Watchers */

function* watchGetAlarmConfigurationTemplate() {
  yield takeLatest(getAlarmConfigurationTemplatesAction.type, getAlarmConfigurationTemplateSaga);
}

function* watchMutateConfiguration() {
  yield takeLatest(mutateAlarmConfigurationAction.type, mutateAlarmConfigurationSaga);
}

export default [fork(watchGetAlarmConfigurationTemplate), fork(watchMutateConfiguration)];
