import alarmConfigurationTemplateWatchers from './alarm-configuration-template.saga';
import { alarmConfigurationTemplateSlice } from './alarm-configuration-template.slice';

export type {
  AlarmConfigurationTemplateSliceState,
  MutateType,
  SetMutateFieldsActionPayload,
  SetMutateMonitorItemFieldsActionPayload,
  CloneTemplate,
} from './alarm-configuration-template.slice';
export * from './alarm-configuration-template.action';
export * from './alarm-configuration-template.selector';
export { alarmConfigurationTemplateWatchers };
export default {
  [alarmConfigurationTemplateSlice.name]: alarmConfigurationTemplateSlice.reducer,
};
