import React, { useEffect, useState } from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import type { SelectProps } from '@manyun/base-ui.ui.select';

import { fetchOperatorType } from '@manyun/monitoring.service.fetch-operator-type';

export const OperatorTypeSelect = ({ ...props }: SelectProps) => {
  const [options, setOptions] = useState<SelectProps['options']>([]);
  useEffect(function () {
    (async () => {
      const { error, data } = await fetchOperatorType();
      if (error) {
        message.error(error.message);
      }
      const list = Object.keys(data).map(item => {
        return { value: item, label: data[item] };
      });
      setOptions(list);
    })();
  }, []);

  return <Select options={options} {...props} />;
};
