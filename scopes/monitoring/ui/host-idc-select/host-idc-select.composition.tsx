import { configureStore } from '@reduxjs/toolkit';
import React from 'react';
import { MemoryRouter as Router } from 'react-router-dom';
import { combineReducers } from 'redux';

import userSliceReducer, { getInitialUser } from '@manyun/auth-hub.state.user';
import { StoreProvider } from '@manyun/base-ui-web.context.store-context';
import { Metadata, MetaType } from '@manyun/resource-hub.model.metadata';
import spaceSliceReducer, { getInitialSpaces } from '@manyun/resource-hub.state.space';

import { HostIdcSelect } from './host-idc-select';

const fakeUser = getInitialUser();
fakeUser.idc = 'EC06';
fakeUser.resourceCodes = ['EC06', 'EC01'];
const fakeSpaces = getInitialSpaces();
fakeSpaces.codes = ['EC06', 'EC01'];
const EC06 = new Metadata(
  0,
  MetaType.Idc,
  'EC06',
  'EC06',
  '',
  null,
  'SPACE',
  Date.now(),
  Date.now(),
  { id: 0, name: 'admin' },
  false,
  '',
  null,
).toJSON();
const EC01 = new Metadata(
  0,
  MetaType.Idc,
  'EC01',
  'EC01',
  '',
  null,
  'SPACE',
  Date.now(),
  Date.now(),
  { id: 0, name: 'admin' },
  false,
  '',
  null
).toJSON();
fakeSpaces.entities = { EC06, EC01 };

const fakeStore = configureStore({
  preloadedState: {
    user: fakeUser,
    'resource.spaces': fakeSpaces,
  },
  reducer: combineReducers({
    ...userSliceReducer,
    ...spaceSliceReducer,
  }),
});

export const BasicHostIdcSelect = () => {
  return (
    <StoreProvider store={fakeStore}>
      <Router>
        <HostIdcSelect />
      </Router>
    </StoreProvider>
  );
};
