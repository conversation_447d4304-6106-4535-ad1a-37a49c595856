import DownOutlined from '@ant-design/icons/es/icons/DownOutlined';
import MenuFoldOutlined from '@ant-design/icons/es/icons/MenuFoldOutlined';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory, useLocation, useParams } from 'react-router-dom';

import { selectMe, selectMyResourceCodes, userSliceActions } from '@manyun/auth-hub.state.user';
import { Dropdown } from '@manyun/base-ui.ui.dropdown';
import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';
import { useLayout } from '@manyun/dc-brain.context.layout';
import { useRouteSpaceParams } from '@manyun/monitoring.hook.use-route-space-params';
import { useSpaces } from '@manyun/resource-hub.ui.location-tree-select';

import styles from './host-idc-select.module.less';

export type IdcDropdownProps = {
  /** 自定义菜单样式 */
  menuStyles?: React.CSSProperties;
  dropdownButtonStyle?: React.CSSProperties;
  showSideBarFold?: boolean;
};

type IdcMenu = {
  label: string;
  key: string | number;
};

/**
 * 机房选择器
 * @param param
 * @returns
 */
export function HostIdcSelect({
  menuStyles,
  dropdownButtonStyle,
  showSideBarFold,
}: IdcDropdownProps) {
  const dispatch = useDispatch();
  const history = useHistory();
  const [idcMenu, setIdcMenu] = useState<IdcMenu[]>([]);
  const { idc: currentIdc } = useSelector(selectMe, (left, right) => left.idc === right.idc);
  const [idc, setIdc] = useState<string | null>(currentIdc);

  const [{ treeSpaces }] = useSpaces({
    authorizedOnly: true,
    nodeTypes: ['IDC'],
    includeVirtualBlocks: false,
  });

  const { search } = useLocation();
  const { idc: urlParamsIdc } = useParams<{ idc?: string }>();
  const urlIdc = getLocationSearchMap<{ idcTag?: string }>(search).idcTag || urlParamsIdc;
  const myResourcesCodes = useSelector(selectMyResourceCodes);
  const isUrlIdcAuthorized = !!urlIdc && myResourcesCodes.includes(urlIdc);

  useEffect(() => {
    if (isUrlIdcAuthorized) {
      // 默认取urlidc为主
      setIdc(urlIdc);
      dispatch(userSliceActions.setUserIdc(urlIdc));
    } else {
      setIdc(null);
    }
  }, [dispatch, isUrlIdcAuthorized, urlIdc]);

  useEffect(() => {
    if (treeSpaces?.length) {
      setIdcMenu(
        treeSpaces.map(space => {
          return {
            label: space.title,
            key: space.value,
          };
        })
      );
    }
  }, [treeSpaces]);

  const replaceRouteSpaceParams = useRouteSpaceParams();
  const handleMenuClick = (value: { key: string }) => {
    const idc = value.key;
    dispatch(userSliceActions.setUserIdc(idc));
    replaceRouteSpaceParams(idc);
  };

  const handleDropdownButtonClick: React.MouseEventHandler<HTMLButtonElement> = e => {
    e.preventDefault();
    const idc = e.currentTarget.textContent;
    if (!idc) {
      return;
    }
    // Note: see https://stackoverflow.com/a/11305926
    // To remove the zero-width-space from the string(`idc`).
    if (!idc.replace(/[\u200B-\u200D\uFEFF]/g, '')) {
      return;
    }
    dispatch(userSliceActions.setUserIdc(idc));
  };

  return (
    <div className={styles.idcDropdown}>
      {showSideBarFold && <SideBarFold />}
      <Dropdown.Button
        style={dropdownButtonStyle}
        icon={<DownOutlined />}
        size="small"
        menu={{
          items: idcMenu,
          style: {
            maxHeight: 200,
            overflowY: 'scroll',
            ...menuStyles,
          },
          onClick: handleMenuClick,
        }}
        onClick={handleDropdownButtonClick}
      >
        {idc ??
          /* Use a zero-width-space to avoid incorrect alignment when `idc` is `null`. */ '\u200B'}
      </Dropdown.Button>
    </div>
  );
}

function SideBarFold() {
  const { setSideBarVisible, setSideBarTab } = useLayout();

  const toggleSideBar = () => {
    setSideBarVisible(true);
    setSideBarTab('roomview');
  };

  return (
    <div
      style={{
        cursor: 'pointer',
        background: 'var(--component-background)',
        border: '1px solid var(--border-color-base)',
        borderRadius: 2,
        padding: '0 4px',
        marginRight: 8,
      }}
      onClick={() => toggleSideBar()}
    >
      <MenuFoldOutlined style={{ fontSize: 15, color: 'var(--text-color)' }} />
    </div>
  );
}
