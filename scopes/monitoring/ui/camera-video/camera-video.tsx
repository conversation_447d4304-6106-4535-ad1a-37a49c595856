import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useInterval, useLatest } from 'react-use';

import CloseOutlined from '@ant-design/icons/es/icons/CloseOutlined';
import LoadingOutlined from '@ant-design/icons/es/icons/LoadingOutlined';
import WarningOutlined from '@ant-design/icons/es/icons/WarningOutlined';
import cls from 'classnames';

import { Space } from '@manyun/base-ui.ui.space';
import { VideoPlayer, customComponents } from '@manyun/base-ui.ui.video-player';
import type {
  Player,
  PlayerOptions,
  PlayerState,
  VideoPlayerProps,
} from '@manyun/base-ui.ui.video-player';

import { startVideoLive } from '@manyun/monitoring.service.start-video-live';
import type { SvcRespData as VideoData } from '@manyun/monitoring.service.start-video-live';
import { stopVideoLive } from '@manyun/monitoring.service.stop-video-live';
import { videoLiveHeartbeat } from '@manyun/monitoring.service.video-live-heartbeat';

import styles from './camera-video.module.less';

/** 大尺寸 video 最小宽度 */
const smallestWidth = 480;

export type CameraVideoProps = {
  guid: string;
  name?: string;
  showTitle?: boolean;
  showCloseIcon?: boolean;
  onClose?: () => void;
} & Omit<VideoPlayerProps, 'controlBarSize'>;

const defaultOptions: PlayerOptions = {
  autoplay: true,
  bigPlayButton: false,
  fill: true,
  language: 'zh-CN',
  muted: true,
  controls: true,
  controlBar: {
    volumePanel: false,
    progressControl: false,
    remainingTimeDisplay: false,
    pictureInPictureToggle: false,
    fullscreenToggle: false,
    liveDisplay: false,
    seekToLive: false,
    currentTimeDisplay: false,
    durationDisplay: false,
  },
};

/** 定时器延时 */
const intervalDelay = 200;
/** 心跳延时 */
const heartbeatDelay = 30 * 1000;

export function CameraVideo(props: CameraVideoProps) {
  const { guid, name, showTitle, showCloseIcon, onClose, className, ...rest } = props;
  const [videoData, setVideoData] = useState<VideoData>(null);
  const [loading, setLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [videoPlayer, setVideoPlayer] = useState<Player | null>(null);
  const [hlsUrl, setHlsUrl] = useState<string>();

  // retry when url error
  const [delay, setDelay] = useState<number | null>(null);
  const startInterval = useCallback(() => {
    setDelay(intervalDelay);
  }, []);
  const clearInterval = useCallback(() => {
    setDelay(null);
  }, []);
  const currentRetries = useRef(0);
  useInterval(() => {
    if (!videoData) {
      return;
    }
    const url = videoData.hlsUrl;
    fetch(url)
      .then(() => {
        setLoading(false);
        clearInterval();
        setHlsUrl(url);
      })
      .finally(() => {
        currentRetries.current++;
        if (currentRetries.current > heartbeatDelay / intervalDelay) {
          clearInterval();
          setHasError(true);
        }
      });
  }, delay);

  // start video live
  useEffect(() => {
    setLoading(true);
    startVideoLive(props.guid).then(({ error, data }) => {
      if (error) {
        setLoading(false);
        return setHasError(true);
      }
      setVideoData(data);
      startInterval();
    });
    return () => {
      // reset state
      clearInterval();
      setVideoData(null);
      setLoading(true);
      setHasError(false);
      setVideoPlayer(null);
      setHlsUrl(undefined);
      currentRetries.current = 0;
    };
  }, [clearInterval, props.guid, startInterval]);

  // video live heartbeat
  const timerRef = useRef<number>();
  useEffect(() => {
    if (videoData) {
      timerRef.current = window.setInterval(() => {
        videoLiveHeartbeat({ guid, sessionId: videoData.sessionId }).then(({ error }) => {
          if (error) {
            setHasError(true);
          }
        });
      }, heartbeatDelay);
    }
    return () => {
      if (timerRef.current) {
        window.clearInterval(timerRef.current);
      }
    };
  }, [guid, videoData]);

  // support onClose
  const handleClose = useCallback(() => {
    if (videoData !== null) {
      stopVideoLive({ guid, sessionId: videoData.sessionId });
    }
    onClose?.();
  }, [guid, onClose, videoData]);

  // custom controls
  const customControlsView = useMemo(
    () =>
      // eslint-disable-next-line react/display-name
      ({ state }: { state: PlayerState }) => {
        if (!showTitle && !showCloseIcon) {
          return null;
        }
        return (
          <div
            className={cls(
              state.userActive ? styles.active : styles.inactive,
              styles.customControls
            )}
          >
            {showTitle && name}
            {showCloseIcon && <CloseOutlined onClick={handleClose} />}
          </div>
        );
      },
    [handleClose, name, showCloseIcon, showTitle]
  );

  useEffect(() => {
    if (videoPlayer === null) {
      return;
    }
    videoPlayer.on('error', () => {
      videoPlayer.error(null);
    });

    customComponents.forEach(customComponent => {
      videoPlayer.getChild('ControlBar')!.addChild(customComponent);
    });
    // fix controlBar order
    videoPlayer.getChild('ControlBar')!.addChild('fullscreenToggle');
  }, [videoPlayer]);

  const [isSmall, setIsSmall] = useState(false); // video 是否为小型尺寸 video
  const handleStateChange = useCallback((state: PlayerState) => {
    setIsSmall(state.currentWidth < smallestWidth);
  }, []);

  // 使用 ref、useMemo 以避免不必要的 rerender（这会导致视频刷新）
  const restRef = useLatest(rest);
  const view = useMemo(() => {
    if (hasError) {
      return <ErrorText />;
    }
    if (loading) {
      return <LoadingText />;
    }
    if (!hlsUrl) {
      return <ErrorText />;
    }
    return (
      <VideoPlayer
        sources={[{ src: hlsUrl, type: 'application/x-mpegURL' }]}
        className={cls(styles.video, isSmall && styles.small, className)}
        options={defaultOptions}
        crossorigin="anonymous"
        playsinline
        controlBarSize={isSmall ? 'small' : 'default'}
        onMounted={({ player }) => {
          setVideoPlayer(player);
        }}
        onStateChange={handleStateChange}
        {...restRef.current}
      >
        {customControlsView}
      </VideoPlayer>
    );
  }, [
    className,
    customControlsView,
    handleStateChange,
    hasError,
    hlsUrl,
    isSmall,
    loading,
    restRef,
  ]);

  return view;
}

function LoadingText() {
  return (
    <Space className={styles.wrapper}>
      <LoadingOutlined spin />
      暂无数据
    </Space>
  );
}

function ErrorText() {
  return (
    <Space className={styles.wrapper}>
      <WarningOutlined />
      播放异常
    </Space>
  );
}
