import React from 'react';

import cls from 'classnames';

import { Modal, ModalProps } from '@manyun/base-ui.ui.modal';

import { CameraVideo } from './camera-video';
import styles from './camera-video.module.less';

export type CameraVideoModalProps = {
  guid: string;
} & ModalProps;

export function CameraVideoModal({ guid, className, ...rest }: CameraVideoModalProps) {
  return (
    <Modal
      className={cls(styles.modal, className)}
      width="80vw" // fix style width
      style={{ height: '85vh' }}
      footer={null}
      {...rest}
    >
      <CameraVideo guid={guid} />
    </Modal>
  );
}
