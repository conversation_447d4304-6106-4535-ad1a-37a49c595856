@import '~@manyun/base-ui.theme.theme/dist/theme.variable.less';

.wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.video {
  .customControls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: absolute;
    top: 0;
    width: 100%;
    padding: @padding-md @padding-lg;
    background-color: fade(@black, 65%);
    transition: opacity 1s;

    &.active {
      opacity: 1;
    }

    &.inactive {
      opacity: 0;
    }
  }

  &.small {
    .customControls {
      padding: @padding-xs @padding-md;
    }
  }
}


.modal {
  :global {
    .@{prefixCls}-modal-content {
      display: flex;
      flex-direction: column;
      height: 100%;

      .@{prefixCls}-modal-body {
        flex: 1;
      }
    }
  }
}
