import React from 'react';

import { useMetaData } from '@manyun/resource-hub.hook.use-meta-data';
import { MetaType } from '@manyun/resource-hub.model.metadata';

export type AlarmLevelTextProps = {
  /**
   * 等级code
   */
  code: string | number;
};

export function AlarmLevelText({ code }: AlarmLevelTextProps) {
  const [{ data }, { readMetaData }] = useMetaData(MetaType.ALARM_LEVEL);
  React.useEffect(() => {
    readMetaData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [code]);

  const name = data.entities[code] ? data.entities[code].name : '未知';

  return <>{name}</>;
}
