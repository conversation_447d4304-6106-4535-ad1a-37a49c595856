import React, { use<PERSON>allback, useEffect, useMemo, useRef, useState } from 'react';
import shortid from 'shortid';

import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { Button } from '@manyun/base-ui.ui.button';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Switch } from '@manyun/base-ui.ui.switch';
import { Typography } from '@manyun/base-ui.ui.typography';
import {
  useCurrentMutateType,
  useMutateTplFields,
} from '@manyun/monitoring.hook.use-alarm-configuration-template';
import { AlarmTemplateType } from '@manyun/monitoring.model.alarm-template';
import type { MonitoringItemJSON } from '@manyun/monitoring.model.monitoring-item';
import type { Point } from '@manyun/monitoring.model.point';
import { FormulaType } from '@manyun/monitoring.model.trigger-rule';
import { AlarmLevelSelect } from '@manyun/monitoring.ui.alarm-level-select';
import { AlarmMonitoringItemTriggerRuleSetting } from '@manyun/monitoring.ui.alarm-monitoring-item-trigger-rule-setting';
import { AlarmTypeSelect } from '@manyun/monitoring.ui.alarm-type';
import { PointMonitorItemModalView } from '@manyun/monitoring.ui.point-monitor-item-modal-view';
import type { PointMonitorItemModalViewRefProps } from '@manyun/monitoring.ui.point-monitor-item-modal-view';
import { useLazyMetadata } from '@manyun/resource-hub.gql.client.resources';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { fetchPoint } from '@manyun/resource-hub.service.fetch-point';
import MetatypeSelect from '@manyun/resource-hub.ui.metatype-select';
import { PointSelect } from '@manyun/resource-hub.ui.point-select';
import type { PointSelectProps } from '@manyun/resource-hub.ui.point-select';
import { useJudgeEventBetaVersion } from '@manyun/ticket.gql.client.tickets';
import { EventTypeCascader } from '@manyun/ticket.ui.event-type-cascader';

export type AlarmMonitoringItemMutatorProps = {
  id?: number | string;
  //添加时展示测点名称
  showPointCodeMap?: {
    pointCode: string;
    pointName: string;
    deviceType: string;
  };
  //非标测点参数
  pointParams?: { isRemoveSub?: boolean; isQueryNon: boolean; blockGuid: string };
  showLock?: boolean;
  addRulebuttonProps?: Omit<ButtonProps, 'onClick' | 'disabled'>;
  alarmTypeDisabled?: boolean;
  alarmLevelDisabled?: boolean;
  onOk?: (pararm: MonitoringItemJSON, callback?: () => void) => void;
};

export type MonitoringItemFormValues = Partial<MonitoringItemJSON>;

export function AlarmMonitoringItemMutator({
  id,
  showPointCodeMap,
  pointParams,
  showLock = false,
  addRulebuttonProps,
  alarmTypeDisabled,
  alarmLevelDisabled,
  onOk,
}: AlarmMonitoringItemMutatorProps) {
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showTrigger, setShowTrigger] = useState(true);
  const [pointName, setPointName] = useState<string | null>(null); /**监控测点 */
  const [pointCode, setPointCode] = useState<string | null>(null);
  const [currenMutateType] = useCurrentMutateType();
  const [
    { monitorItemEntities, target, type, monitorItemIds },
    { updateMutateTplMonitorItemFields, addMutateTplMonitorItem },
  ] = useMutateTplFields(currenMutateType);
  const triggerRules = Form.useWatch('triggerRules', form);
  const [getMetaDataRes, { data: metaDataRes }] = useLazyMetadata({
    variables: { type: MetaType.N_EVENT_CATEGORY },
    fetchPolicy: 'cache-and-network',
  });

  const [judgeEventBetaVersion, { data: eventBetaVersion }] = useJudgeEventBetaVersion({
    onCompleted(data) {
      if (data.judgeEventBetaVersion?.data) {
        getMetaDataRes();
      }
    },
  });

  /**监控项信息 */
  const monitorItem = monitorItemEntities[id as string | number];

  /**监控测点(测点实体) */
  const [monitoringPoint, setMonitoringPoint] = useState<Point | undefined>(undefined);

  const [isCreateIncident, setIsCreateIncident] = useState<boolean>(
    monitorItem ? monitorItem.isCreateIncident : true
  );

  const eventCategorys = metaDataRes?.metadata ?? [];

  const isCreate = id === undefined;

  const pointConfiguredRef = useRef<PointMonitorItemModalViewRefProps | null>(null);

  /** PointSelect 参数 */
  const getPointParams = useMemo(() => {
    const params: Pick<
      PointSelectProps,
      | 'pointTypeList'
      | 'deviceType'
      | 'dataTypeList'
      | 'isRemoveSub'
      | 'spaceGuid'
      | 'isQueryNon'
      | 'blockGuid'
    > = {
      dataTypeList: ['AI', 'DI'],
    };
    if (type === AlarmTemplateType.Device) {
      params.deviceType = target;
      params.isRemoveSub = true;
    } else {
      params.spaceGuid = target;
      params.pointTypeList = ['CUSTOM'];
    }
    if (pointParams) {
      return { ...params, ...pointParams };
    }
    return params;
  }, [type, pointParams, target]);

  const onDIRuleFormulaTypeChange = useCallback(
    (showTrigger: boolean) => {
      setShowTrigger(true);
      if (!showTrigger) {
        // 变位告警的情况下初始化默认字段数据
        form.setFieldValue('triggerInterval', 0);
        form.setFieldValue('recoverInterval', 5 * 60);
      }
    },
    [form]
  );

  useEffect(() => {
    if (isCreate && visible && !form.getFieldValue('triggerInterval')) {
      form.setFieldValue('triggerInterval', 0);
    }
  }, [form, isCreate, visible]);

  /**新建时自动填充上条记录的触发事件和恢复周期 */
  const autoSetTriggeFromLastItem = useCallback(() => {
    if (monitorItemIds.length === 0) {
      return;
    }
    const lastMonitorItemId = monitorItemIds.find(
      (_, index) => index === monitorItemIds.length - 1
    );
    if (lastMonitorItemId) {
      const lastMonitorItem = monitorItemEntities[lastMonitorItemId];
      if (lastMonitorItem) {
        form.setFieldsValue({
          triggerInterval: lastMonitorItem.triggerInterval,
          recoverInterval: lastMonitorItem.recoverInterval,
        });
      }
    }
  }, [form, monitorItemEntities, monitorItemIds]);

  /**获取请求已配置模板的参数 */
  const getConfiguredTplParams = useCallback(
    (monitoringPoint?: Pick<Point, 'code' | 'deviceType'> & { blockGuid?: string }) => {
      /**注： 不传 spaceGuid、deviceGuid*/
      if (monitoringPoint === undefined) {
        return {} as unknown;
      }
      const params = {
        deviceType: monitoringPoint.deviceType,
        pointCode: monitoringPoint.code,
        blockGuid: monitoringPoint.blockGuid,
      };
      return params;
    },
    []
  );

  const onSubmit = useCallback(() => {
    setLoading(true);
    form
      .validateFields()
      .then(() => {
        const formValues = form.getFieldsValue();
        const isBeta = eventBetaVersion?.judgeEventBetaVersion?.data;
        const changedMointoringItem: MonitoringItemJSON = {
          ...monitorItem,
          notifyRules: monitorItem?.notifyRules ?? [],
          ...formValues,
          itemType: type === AlarmTemplateType.Device ? 'POINT' : 'CUSTOM',
          deviceType: monitoringPoint?.deviceType ?? monitorItem?.deviceType,
          pointCode: pointCode,
          pointName: pointName,
          name: monitoringPoint?.name ?? monitorItem?.name /**监控项名称 */,
          enable: formValues.enable ?? true,
          triggerInterval: formValues.triggerInterval,
          recoverInterval: formValues.recoverInterval,
          isCreateIncident: isCreateIncident,
          incidentType: isCreateIncident
            ? isBeta
              ? [formValues.incidentType.value]
              : (formValues.incidentType ?? []).map((opt: { value: string }) => opt.value)
            : undefined,
          incidentTypeName: isCreateIncident
            ? isBeta
              ? [formValues.incidentType.label]
              : (formValues.incidentType ?? []).map((opt: { label: string }) => opt.label)
            : undefined,
          spaceGuid: monitoringPoint?.spaceGuid,
          id: isCreate ? shortid() : id,
          lock: formValues.lock ?? true,
        };

        if (isCreate) {
          if (onOk) {
            onOk({ ...changedMointoringItem, id: undefined }, () => setVisible(false));
            return;
          }
          addMutateTplMonitorItem({ ...changedMointoringItem });
        } else {
          if (onOk) {
            onOk(changedMointoringItem, () => setVisible(false));
            return;
          }
          updateMutateTplMonitorItemFields({
            ...changedMointoringItem,
            id: changedMointoringItem.id!,
          });
        }
        setVisible(false);
        message.success('操作成功');
      })
      .finally(() => {
        setLoading(false);
      });
  }, [
    form,
    monitorItem,
    type,
    monitoringPoint?.deviceType,
    monitoringPoint?.name,
    monitoringPoint?.spaceGuid,
    pointCode,
    pointName,
    isCreateIncident,
    isCreate,
    id,
    eventBetaVersion,
    onOk,
    addMutateTplMonitorItem,
    updateMutateTplMonitorItemFields,
  ]);

  /**@FIX yjx points state中取 */
  const _fetchPointInfo = useCallback(
    async ({
      deviceType,
      pointCode,
      callback,
    }: {
      deviceType: string | undefined;
      pointCode: string | undefined;
      callback?: (point: Point) => void;
    }) => {
      const { error, data } = await fetchPoint({
        devicePointList: [`${deviceType}.${pointCode}`],
        blockGuidList: pointParams?.blockGuid ? [pointParams.blockGuid] : undefined,
      });
      if (error) {
        message.error(error.message);
        return;
      }
      if (data.data.length > 0) {
        setMonitoringPoint(data.data[0]);
        callback && callback(data.data[0]);
      }
    },
    [pointParams?.blockGuid]
  );

  const onSelectPoint = ({
    label,
    value,
    deviceType,
  }: {
    label: string;
    value: string;
    deviceType: string;
  }) => {
    setPointName(label);
    setPointCode(value);
    setShowTrigger(true);
    form.setFieldsValue({
      triggerRules: [],
      preTriggerRules: [],
      alarmType: undefined,
      alarmLevel: undefined,
      triggerInterval:
        form.getFieldValue('triggerInterval') !== undefined
          ? form.getFieldValue('triggerInterval')
          : undefined,
      recoverInterval: undefined,
      isCreateIncident: undefined,
      incidentType: undefined,
      enable: true,
    });
    _fetchPointInfo({
      deviceType,
      pointCode: value,
      callback: point => {
        pointConfiguredRef?.current?.reloadData({
          pointCode: value,
          deviceType,
          blockGuid: pointParams?.blockGuid,
          spaceGuid: pointParams?.blockGuid,
        });
      },
    });
  };
  const getIncidentType = () => {
    if (eventBetaVersion?.judgeEventBetaVersion?.data) {
      if (eventCategorys.filter(item => item.code === monitorItem.incidentType[0]).length) {
        return {
          label: monitorItem.incidentTypeName[0],
          value: monitorItem.incidentType[0],
        };
      } else {
        return [];
      }
    } else {
      if ((monitorItem?.incidentType ?? []).length === 2) {
        return [
          {
            label: monitorItem.incidentTypeName[0],
            value: monitorItem.incidentType[0],
          },
          {
            label: monitorItem.incidentTypeName[1],
            value: monitorItem.incidentType[1],
          },
        ];
      }
    }
    return [];
  };

  return (
    <>
      <Button
        type={isCreate ? 'primary' : 'link'}
        compact={!isCreate}
        {...addRulebuttonProps}
        disabled={!target}
        onClick={() => {
          if (pointParams?.blockGuid) {
            const [idcTag] = pointParams.blockGuid.split('.');
            judgeEventBetaVersion({ variables: { guid: idcTag } });
          }
          if (!isCreate) {
            const isChange =
              !!monitorItem?.triggerRules?.length &&
              monitorItem?.triggerRules?.every(rule => rule.formulaType === FormulaType.Change);
            onDIRuleFormulaTypeChange(!isChange);
            form.setFieldsValue({
              ...monitorItem,
              incidentType: getIncidentType(),

              isCreateIncident: monitorItem.isCreateIncident ? 'true' : 'false',
            });
          } else {
            form.resetFields();
            autoSetTriggeFromLastItem();
          }

          if (monitorItem?.deviceType && monitorItem?.pointCode) {
            _fetchPointInfo({
              deviceType: monitorItem?.deviceType,
              pointCode: monitorItem?.pointCode,
            });
          }
          setVisible(true);
          setPointCode(monitorItem?.pointCode);
          setPointName(monitorItem?.pointName ?? null);

          if (showPointCodeMap) {
            onSelectPoint({
              value: showPointCodeMap.pointCode,
              label: showPointCodeMap.pointName,
              deviceType: showPointCodeMap.deviceType,
            });
            form.setFieldsValue({
              pointCode: showPointCodeMap.pointCode,
            });
          }
        }}
      >
        {isCreate ? '添加规则' : '编辑'}
      </Button>
      {visible && (
        <Drawer
          destroyOnClose
          visible={visible}
          title={isCreate ? '添加规则' : '编辑规则'}
          width={890}
          extra={
            <Space>
              <Button onClick={() => setVisible(false)}>取消</Button>
              <Button type="primary" loading={loading} onClick={onSubmit}>
                确定
              </Button>
            </Space>
          }
          placement="right"
          onClose={() => {
            setVisible(false);
          }}
        >
          <Form layout="vertical" form={form} initialValues={monitorItem}>
            <Form.Item
              label="监控测点名称"
              name="pointCode"
              rules={[
                {
                  required: true,
                  message: '监控测点必选',
                },
              ]}
              extra={
                <PointMonitorItemModalView
                  ref={pointConfiguredRef}
                  id={id}
                  text="点击查看"
                  modalTitle="已配置规则列表"
                  pointData={getConfiguredTplParams(
                    monitorItem
                      ? {
                          code: monitorItem.pointCode,
                          deviceType: monitorItem.deviceType,
                          blockGuid: pointParams?.blockGuid,
                        }
                      : undefined
                  )}
                  didMount
                >
                  <Typography.Text type="secondary">
                    注：{pointName ? pointName : form.getFieldValue('name')}已配置过告警规则
                  </Typography.Text>
                </PointMonitorItemModalView>
              }
            >
              <PointSelect
                labelInValue
                {...getPointParams}
                style={{
                  width: 360,
                }}
                disabled={!isCreate}
                treeNodeFilterProp="title"
                showSearch
                onChange={(value, _, __, point) => {
                  onSelectPoint({
                    value: value.value,
                    label: value.label,
                    deviceType: point ? point.deviceType : getPointParams.deviceType,
                  });
                }}
              />
            </Form.Item>
            <Space direction="horizontal" style={{ width: '100%' }}>
              <Form.Item
                label="告警类型"
                name="alarmType"
                rules={[
                  {
                    required: true,
                    message: '告警类型必选',
                  },
                ]}
              >
                <AlarmTypeSelect
                  style={{ width: 110 }}
                  disabled={typeof alarmTypeDisabled === 'boolean' ? alarmTypeDisabled : !isCreate}
                />
              </Form.Item>
              <Form.Item
                label="告警等级"
                name="alarmLevel"
                rules={[
                  {
                    required: true,
                    message: '告警等级必选',
                  },
                ]}
              >
                <AlarmLevelSelect
                  style={{ width: 110 }}
                  disabled={
                    typeof alarmLevelDisabled === 'boolean' ? alarmLevelDisabled : !isCreate
                  }
                />
              </Form.Item>
            </Space>
            <Form.Item
              label="告警条件"
              name="triggerRule"
              tooltip="告警条件请配置需告警的情况，多个告警条件​之间为 '或' 的关系"
              rules={[
                {
                  required: true,
                  message: '告警条件必填',
                  validator: () => {
                    if ((form.getFieldValue('triggerRules') || []).length === 0) {
                      return Promise.reject('告警条件必填');
                    } else {
                      return Promise.resolve();
                    }
                  },
                },
              ]}
              wrapperCol={{ span: 20 }}
            >
              <AlarmMonitoringItemTriggerRuleSetting
                name="triggerRules"
                monitoringPoint={monitoringPoint}
                form={form}
                onDIRuleFormulaTypeChange={onDIRuleFormulaTypeChange}
              />
            </Form.Item>
            <Form.Item
              label="前置条件"
              name="preTriggerRule"
              tooltip="前置条件请配置同设备点位，或同空间下聚合加工点位；多个前置条件之间为 ‘且’ 的关系"
            >
              <AlarmMonitoringItemTriggerRuleSetting
                name="preTriggerRules"
                monitoringPoint={monitoringPoint}
                form={form}
                pointParams={pointParams}
                onDIRuleFormulaTypeChange={onDIRuleFormulaTypeChange}
              />
            </Form.Item>
            <>
              <Form.Item
                label="触发观察周期(秒)"
                name="triggerInterval"
                tooltip="指告警触发后，持续多长时间，才会被判断为告警产生，以减少测点值抖动带来的误告警。默认值为0，代表告警触发即告警产生。"
                rules={[
                  {
                    required: true,
                    message: '触发观察周期必填',
                  },
                ]}
              >
                <InputNumber
                  style={{ width: 110 }}
                  min={0}
                  precision={0}
                  max={100000}
                  disabled={!showTrigger}
                />
              </Form.Item>
              <Form.Item
                label="恢复观察周期(秒)"
                name="recoverInterval"
                tooltip="指告警发生后,多长时间内未产生新的告警,默认恢复正常"
                rules={[
                  {
                    required: true,
                    message: '恢复观察周期必填',
                  },
                ]}
              >
                <InputNumber
                  style={{ width: 110 }}
                  min={
                    (triggerRules ?? []).some(rule => rule.formulaType === FormulaType.Change)
                      ? 1
                      : 0
                  }
                  precision={0}
                  max={!showTrigger ? 300 : 100000}
                />
              </Form.Item>
            </>
            <Space direction="horizontal" style={{ width: '100%' }}>
              <Form.Item
                label="是否创建事件"
                name="isCreateIncident"
                rules={[
                  {
                    required: true,
                    message: '是否创建事件必选',
                  },
                ]}
              >
                <Select
                  style={{ width: 110 }}
                  onChange={value => {
                    setIsCreateIncident(value === 'true' ? true : false);
                  }}
                >
                  <Select.Option value="true">是</Select.Option>
                  <Select.Option value="false">否</Select.Option>
                </Select>
              </Form.Item>
              {isCreateIncident &&
                (eventBetaVersion?.judgeEventBetaVersion?.data ? (
                  <Form.Item
                    label="专业分类"
                    name="incidentType"
                    rules={[
                      {
                        required: true,
                        message: '专业分类必选',
                      },
                    ]}
                  >
                    <MetatypeSelect
                      metaType={MetaType.N_EVENT_CATEGORY}
                      style={{ width: 220 }}
                      labelInValue
                    />
                  </Form.Item>
                ) : (
                  <Form.Item
                    label="事件类型"
                    name="incidentType"
                    rules={[
                      {
                        required: true,
                        message: '事件类型必选',
                        type: 'number',
                        transform: value => (value?.length === 2 ? 2 : false),
                      },
                    ]}
                    getValueFromEvent={(_value, selectedOptions: []) => selectedOptions}
                    getValueProps={(selectedOptions: { value: string | number }[]) => {
                      return { value: (selectedOptions ?? []).map(opt => opt.value) };
                    }}
                  >
                    <EventTypeCascader style={{ width: 220 }} />
                  </Form.Item>
                ))}
            </Space>
            <Form.Item label="处置建议" name="description">
              <Input.TextArea style={{ width: 328 }} maxLength={1000} rows={4} />
            </Form.Item>
            <Form.Item label="是否启用" name="enable" valuePropName="checked">
              <Switch defaultChecked checkedChildren="开启" unCheckedChildren="停用" />
            </Form.Item>
            {showLock && (
              <Form.Item label="规则锁" name="lock" valuePropName="checked">
                <Switch defaultChecked checkedChildren="开启" unCheckedChildren="停用" />
              </Form.Item>
            )}
          </Form>
        </Drawer>
      )}
    </>
  );
}
