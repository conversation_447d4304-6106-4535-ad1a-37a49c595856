import React, { useCallback, useEffect, useImperativeHandle, useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';

import { fetchPointThresholds } from '@manyun/monitoring.service.fetch-point-thresholds';
import type { SvcQuery, Threshold } from '@manyun/monitoring.service.fetch-point-thresholds';
import { AlarmMonitoringItemTable } from '@manyun/monitoring.ui.alarm-monitoring-item-table';

export type PointMonitorItemModalViewRefProps = {
  reloadData: (query: SvcQuery) => void;
};
export type PointMonitorItemModalViewProps = {
  id?: number | string;
  text: string | React.ReactNode;
  modalTitle: string | React.ReactNode;
  didMount?: boolean;
  pointData: SvcQuery;
  children?: React.ReactNode;
};

export const PointMonitorItemModalView = React.forwardRef(
  (
    { id, text, modalTitle, didMount = false, pointData, children }: PointMonitorItemModalViewProps,
    ref?: React.Ref<PointMonitorItemModalViewRefProps>
  ) => {
    const [visible, setVisible] = useState(false);
    const [loading, setLoading] = useState(false);
    const [dataSource, setDataSource] = useState<Threshold[]>([]);

    useImperativeHandle(ref, () => ({
      reloadData: query => {
        fetchPointMonitoringItems(query);
      },
    }));

    const fetchPointMonitoringItems = useCallback(
      async (query?: SvcQuery) => {
        setLoading(true);
        const { error, data } = await fetchPointThresholds(query ? query : pointData);
        setLoading(false);
        if (error) {
          message.error(error.message);
          return;
        }
        setDataSource(data.data.filter(item => item.id !== id));
      },
      [id, pointData]
    );

    useEffect(() => {
      if (didMount && pointData.pointCode) {
        fetchPointMonitoringItems();
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const onVisibleChanged = useCallback(() => {
      setVisible(true);
      if (!didMount) {
        fetchPointMonitoringItems();
      }
    }, [fetchPointMonitoringItems, didMount]);

    if (didMount && dataSource.length === 0) {
      return null;
    }

    return (
      <>
        <Space>
          {children}
          <Button
            type="link"
            compact
            onClick={() => {
              onVisibleChanged();
            }}
          >
            {text}
          </Button>
        </Space>
        <Modal
          open={visible}
          footer={null}
          title={modalTitle}
          bodyStyle={{ maxHeight: '480px', overflowY: 'auto' }}
          width={1000}
          onCancel={() => {
            setVisible(false);
          }}
        >
          <AlarmMonitoringItemTable
            rowKey="id"
            scroll={{ x: 'max-content' }}
            loading={loading}
            showColumns={[
              'groupName',
              'alarmType',
              'alarmLevel',
              'preTriggerRules',
              'triggerRules',
              'tagEnable',
              'groupStatus',
            ]}
            dataSource={dataSource!}
            search={false}
            toolBarRender={false}
            pagination={false}
          />
        </Modal>
      </>
    );
  }
);

PointMonitorItemModalView.displayName = 'PointMonitorItemModalView';
