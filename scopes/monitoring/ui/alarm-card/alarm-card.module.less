.main {
  position: relative;
  width: 100%;
  padding-left: 8px;

  &::before {
    content: ' ';
    position: absolute;
    left: -8px;
    width: 4px;
    height: 100%;
    border-radius: 4px;
  }

  &.alerting {
    &::before {
      background-color: var(--manyun-error-color);
    }
  }

  &.warning {
    &::before {
      background-color: var(--manyun-warning-color);
    }
  }

  .title {
    max-width: 180px;
    height: 44px;
    word-break: break-all;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
  }
}
