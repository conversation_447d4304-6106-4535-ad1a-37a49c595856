import React from 'react';
import { MemoryRouter as Router } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { Alarm } from '@manyun/monitoring.model.alarm';

import { AlarmCard } from './alarm-card';

export const BasicAlarmCard = () => (
  <ConfigProvider>
    <Router>
      <div style={{ padding: 64 }}>
        <AlarmCard
          alarm={Alarm.parseJSON({
            id: 112233,
            source: {
              variant: 'device',
              dimension: 'DEVICE',
              guid: 'some-device-guid',
              name: 'Some Device Name',
              spaceGuidMap: {
                idcTag: 'idc',
                blockTag: 'block',
                roomTag: 'room',
                columnTag: 'column',
                gridTag: 'grid',
              },
              deviceLabel: 'label',
              deviceType: '10101',
              roomType: 'IT_ROOM',
              deviceTypeLevel1: '1',
            },
            state: {
              code: 'TRIGGER',
              name: '触发中',
            },
            message: '【1级】冷通道湿度超上限巴拉巴拉巴拉',
            level: '2',
            type: {
              code: 'ERROR',
              name: '告警',
            },
            lifecycleState: {
              code: 'ACTIVE',
              name: '未确认',
            },
            point: {
              code: '111000',
              name: '这是一个测点名称',
              validLimits: null,
              dataType: { code: 'AI', name: 'ai' },
              unit: 'v',
            },
            pointData: {
              current: 1,
              snapshot: '',
            },
            cause: {
              code: 'UPPER',
              name: '超上限',
            },
            createdAt: Date.now(),
            latestTriggeredAt: Date.now(),
            mergeCount: 1,
            gridCount: 1,
            deviceCount: 1,
            notifyCount: 1,
            customerCount: 1,

            eventId: 2,
            monitoringItem: {
              id: 1,
              threshold: '',
            },
            confirmUser: {
              id: 1,
              name: '',
            },
            confirmedAt: Date.now(),
            mergeRuleId: 1,
            recoveredAt: Date.now(),
            activatedAt: Date.now(),
            changeId: '1',
            removedDescriptions: '',
            reason: '',
          })}
        />
      </div>
    </Router>
  </ConfigProvider>
);
