import React from 'react';
import { Link } from 'react-router-dom';

import classNames from 'classnames';
import dayjs from 'dayjs';

import { Card } from '@manyun/base-ui.ui.card';
import { Space } from '@manyun/base-ui.ui.space';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';

import type { Alarm, BackendAlarmState } from '@manyun/monitoring.model.alarm';
import { generateSpaceOrDeviceRoutePath } from '@manyun/monitoring.route.monitoring-routes';
import { AlarmLevelText } from '@manyun/monitoring.ui.alarm-level-text';

import styles from './alarm-card.module.less';

const AlarmStateTagColorMapper: Record<BackendAlarmState, string> = {
  TRIGGER: 'error',
  SUPPRESS: 'default',
  RECOVER: 'success',
};

export type AlarmCardProps = {
  alarm: Alarm;
  bordered?: boolean;
  onClick?: React.MouseEventHandler<HTMLDivElement>;
};

export function AlarmCard({ bordered, alarm, onClick }: AlarmCardProps) {
  const typeClasses = classNames(styles.main, {
    [styles.alerting]: alarm.type.code === 'ERROR',
    [styles.warning]: alarm.type.code === 'WARN',
  });

  return (
    <Card size="small" bordered={bordered} onClick={onClick}>
      <Space style={{ width: '100%' }} direction="vertical">
        <Space className={typeClasses} direction="vertical">
          <Space style={{ width: '100%', justifyContent: 'space-between' }}>
            <Link
              className={styles.title}
              title={alarm.source.name}
              to={generateSpaceOrDeviceRoutePath({
                guid: alarm.source.guid,
                type: alarm.source.dimension,
              })}
            >
              {alarm?.source?.name || '--'}
            </Link>
            <Tag color={AlarmStateTagColorMapper[alarm.state.code]}>{alarm.state.name}</Tag>
          </Space>
          <Typography.Text ellipsis>
            【<AlarmLevelText code={alarm.level} />】{alarm.point.name}-{alarm.cause.name}
          </Typography.Text>
        </Space>
        <Typography.Text style={{ float: 'right', fontSize: 12 }} type="secondary">
          {dayjs(alarm.latestTriggeredAt).format('YYYY-MM-DD HH:mm:ss')}
        </Typography.Text>
      </Space>
    </Card>
  );
}
