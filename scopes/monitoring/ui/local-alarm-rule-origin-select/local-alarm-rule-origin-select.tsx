import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

export type LocalAlarmRuleOriginSelectProps = Omit<SelectProps, 'options'>;

const options: SelectProps['options'] = [
  { value: 'CENTER', label: '集团' },
  { value: 'LOCAL', label: '属地' },
];

export const LocalAlarmRuleOriginSelect = React.forwardRef(
  (props: SelectProps, ref: React.Ref<RefSelectProps>) => {
    return <Select {...props} ref={ref} options={options} />;
  }
);

LocalAlarmRuleOriginSelect.displayName = 'LocalAlarmRuleOriginSelect';
