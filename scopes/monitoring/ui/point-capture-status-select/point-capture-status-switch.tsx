import React from 'react';

import { Switch } from '@manyun/base-ui.ui.switch';
import type { SwitchProps } from '@manyun/base-ui.ui.switch';

export type PointCaptureStatusSwitchProps = SwitchProps;

export const PointCaptureStatusSwitch = React.forwardRef(
  (props: PointCaptureStatusSwitchProps, ref: React.Ref<HTMLElement>) => {
    return <Switch ref={ref} {...props} />;
  }
);

PointCaptureStatusSwitch.displayName = 'PointCaptureStatusSwitch';
