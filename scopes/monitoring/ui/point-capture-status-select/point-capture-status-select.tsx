import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

import { getNonPointDataLocales } from '@manyun/monitoring.model.point';

export type PointCaptureStatusSelectProps = Omit<SelectProps, 'options' | 'ref'>;

export type PointCaptureStatus = 'ON' | 'OFF';
/**
 * 属地测点-采集状态
 */
export const PointCaptureStatusSelect = React.forwardRef(
  (props: PointCaptureStatusSelectProps, ref: React.Ref<RefSelectProps>) => {
    const locales = getNonPointDataLocales();

    const options = [
      { value: 'ON', label: locales.captureStatus.CAPTURED },
      { value: 'OFF', label: locales.captureStatus.UNCAPTURED },
    ];

    return <Select {...props} ref={ref} options={options} />;
  }
);

PointCaptureStatusSelect.displayName = 'PointCaptureStatusSelect';
