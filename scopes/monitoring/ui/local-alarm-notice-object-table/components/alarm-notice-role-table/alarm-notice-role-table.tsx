import React, { useCallback, useEffect } from 'react';

import { useRoles } from '@manyun/auth-hub.hook.use-roles';
import type { Role } from '@manyun/auth-hub.model.role';
import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';

import { ChannelCheckbox } from './components/channel-checkbox';
import { DutyStatusRadioGroup, DutyStatusText } from './components/duty-status';

export type NotificationItemObject = {
  code: string;
  name?: string;
  dutyStatus: 'ALL' | 'ON_DUTY' | 'NOT_ON_DUTY';
  email: boolean;
  sms: boolean;
  phone: boolean;
  internalMsg: boolean;
  disabled?: boolean;
};

export const DEFAULT_OBJECT_CONFIG: Pick<
  NotificationItemObject,
  'dutyStatus' | 'email' | 'sms' | 'internalMsg' | 'phone'
> = {
  dutyStatus: 'ALL' /**默认选中全部 */,
  email: false,
  sms: false,
  internalMsg: true /**默认选中站内信 */,
  phone: false,
};

export type AlarmNoticeRoleTableProps = {
  savedValue?: NotificationItemObject[];
  value?: NotificationItemObject[];
  onChange?: (value: NotificationItemObject[]) => void;
  isEditableTable?: boolean;
};

export function AlarmNoticeRoleTable({
  savedValue,
  isEditableTable = true,
  value,
  onChange,
}: AlarmNoticeRoleTableProps) {
  const [{ loading, codes, entities }, getRoles] = useRoles();

  useEffect(() => {
    getRoles();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const dataSource = useDeepCompareMemo(() => {
    const savedIds = (savedValue ?? []).map(item => item.code);
    //保存后的值放在最前面
    const _roles = (codes.map((code: string) => entities[code]) as Role[]).sort((a, b) => {
      const aPriority = savedIds.includes(a.code) ? 0 : 1; // 匹配到的优先级高
      const bPriority = savedIds.includes(b.code) ? 0 : 1;
      return aPriority - bPriority;
    });
    const oldValueCodes = (value ?? []).map(item => item.code);

    let data = _roles.map(item => {
      const findObj = (value ?? []).find(v => v.code === item.code);
      if (findObj && oldValueCodes.includes(item.code)) {
        return {
          name: item.name,
          code: item.code,
          dutyStatus: findObj.dutyStatus,
          phone: findObj.phone,
          sms: findObj.sms,
          email: findObj.email,
          internalMsg: findObj.internalMsg,
          disabled: false,
        };
      }
      return {
        name: item.name,
        code: item.code,
        ...DEFAULT_OBJECT_CONFIG,
        disabled: true,
      };
    }, []);
    if (!isEditableTable) {
      data = data
        .filter(item => oldValueCodes.includes(item.code))
        .map(item => {
          return {
            ...item,
            disabled: true,
          };
        });
    }
    return data;
  }, [codes, entities, isEditableTable, savedValue, value]);

  const rowSelection = useDeepCompareMemo(() => {
    return isEditableTable
      ? {
          selectedRowKeys: (value ?? []).map(obj => obj.code),
          onChange: (_: React.Key[], selectedRows: NotificationItemObject[]) => {
            const _mergeData: NotificationItemObject[] = [];
            const _valueCodes = (value ?? []).map(v => v.code);
            selectedRows.forEach(selectedRow => {
              if (_valueCodes.includes(selectedRow.code as string)) {
                const originObj = (value ?? []).find(v => v.code === selectedRow.code);
                if (originObj) {
                  _mergeData.push(originObj);
                }
              } else {
                _mergeData.push({
                  ...DEFAULT_OBJECT_CONFIG,
                  code: selectedRow.code,
                  name: selectedRow.name,
                });
              }
            });
            if (onChange) {
              onChange(_mergeData);
            }
          },
        }
      : undefined;
  }, [isEditableTable, onChange, value]);

  const _onChangeValue = useCallback(
    (changedRecord: NotificationItemObject) => {
      if (onChange) {
        const _newValues = (value ?? []).map(dt => {
          return changedRecord.code === dt.code ? changedRecord : dt;
        });
        onChange(_newValues);
      }
    },
    [onChange, value]
  );

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      {/* <div style={{ display: 'flex', justifyContent: 'end' }}>
        <Input.Search
          size="small"
          placeholder="搜索角色名称"
          allowClear
          style={{ width: '216px' }}
          onSearch={value => {
            getRoles({ name: value });
          }}
        />
      </div> */}
      <Table
        scroll={{ x: 'max-content' }}
        tableLayout="fixed"
        loading={loading}
        size="small"
        dataSource={isEditableTable ? dataSource : dataSource.filter(d => d.disabled === true)}
        rowKey="code"
        rowSelection={rowSelection}
        pagination={{
          showQuickJumper: false,
        }}
        columns={[
          {
            title: '角色',
            dataIndex: 'name',
            fixed: 'left',
          },
          {
            title: '排班状态',
            dataIndex: 'dutyStatus',

            render: (dutyStatus, record) => {
              return isEditableTable ? (
                <DutyStatusRadioGroup
                  value={dutyStatus}
                  disabled={record.disabled}
                  onChange={e => {
                    const _value = e.target.value;
                    const _newRecord = { ...record, dutyStatus: _value };
                    _onChangeValue(_newRecord);
                  }}
                />
              ) : (
                <DutyStatusText value={dutyStatus} />
              );
            },
          },
          {
            title: '电话',
            dataIndex: 'phone',
            render: (_, record) => {
              return <ChannelCheckbox type="phone" record={record} onChange={_onChangeValue} />;
            },
          },
          {
            title: '短信',
            dataIndex: 'sms',
            render: (_, record) => {
              return <ChannelCheckbox type="sms" record={record} onChange={_onChangeValue} />;
            },
          },
          {
            title: '邮件',
            dataIndex: 'email',
            render: (_, record) => {
              return <ChannelCheckbox type="email" record={record} onChange={_onChangeValue} />;
            },
          },
          {
            title: '站内信',
            dataIndex: 'internalMsg',
            render: (_, record) => {
              return (
                <ChannelCheckbox type="internalMsg" record={record} onChange={_onChangeValue} />
              );
            },
          },
        ]}
      />
    </Space>
  );
}
