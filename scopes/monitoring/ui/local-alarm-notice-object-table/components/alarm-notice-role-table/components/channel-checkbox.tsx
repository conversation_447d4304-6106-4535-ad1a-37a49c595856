import React from 'react';

import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import type { CheckboxProps } from '@manyun/base-ui.ui.checkbox';
import { message } from '@manyun/base-ui.ui.message';

import type { NotificationItemObject } from '../alarm-notice-role-table';

export type ChannelCheckboxProps = {
  type: 'phone' | 'sms' | 'email' | 'internalMsg';
  record: NotificationItemObject & { disabled?: boolean };
  onChange?: (newRecord: NotificationItemObject) => void;
} & Omit<CheckboxProps, 'onChange'>;

/**复选框 */
export const ChannelCheckbox = ({ record, type, onChange, ...props }: ChannelCheckboxProps) => {
  return (
    <Checkbox
      {...props}
      disabled={record?.disabled}
      checked={record ? record[type] : false}
      onChange={e => {
        const _checked = e.target.checked;
        const _record = { ...record, [type]: _checked };
        if (!_record.email && !_record.internalMsg && !_record.phone && !_record.sms) {
          message.error('至少保留一个通报渠道!');
          return;
        }
        onChange && onChange({ ...record, [type]: _checked });
      }}
    />
  );
};
