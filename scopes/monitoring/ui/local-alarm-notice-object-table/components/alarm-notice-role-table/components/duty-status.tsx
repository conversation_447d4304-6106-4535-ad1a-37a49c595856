import React from 'react';

import { Radio } from '@manyun/base-ui.ui.radio';
import type { RadioProps } from '@manyun/base-ui.ui.radio';

export type DutyStatusRadioGroupProps = Omit<RadioProps, 'options'>;

type BackedScheduleStatus = 'ALL' | 'ON_DUTY' | 'NOT_ON_DUTY';

export const SCHEDULE_STATUS_MAPPER: Record<BackedScheduleStatus, string> = {
  ALL: '全部',
  ON_DUTY: '在班次',
  NOT_ON_DUTY: '未在班次',
};
export const OPTIPNS: { label: string; value: BackedScheduleStatus }[] = [
  {
    label: SCHEDULE_STATUS_MAPPER.ALL,
    value: 'ALL',
  },
  {
    label: SCHEDULE_STATUS_MAPPER.ON_DUTY,
    value: 'ON_DUTY',
  },
  {
    label: SCHEDULE_STATUS_MAPPER.NOT_ON_DUTY,
    value: 'NOT_ON_DUTY',
  },
];
export const DutyStatusRadioGroup = ({ ...props }: DutyStatusRadioGroupProps) => {
  return <Radio.Group {...props} options={OPTIPNS} />;
};

export const DutyStatusText = ({ value }: { value: BackedScheduleStatus }) => {
  return <>{SCHEDULE_STATUS_MAPPER[value] ? SCHEDULE_STATUS_MAPPER[value] : value}</>;
};
