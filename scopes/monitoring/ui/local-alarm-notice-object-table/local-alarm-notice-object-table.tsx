import React, { useCallback, useEffect, useState } from 'react';
import { useDeepCompareEffect } from 'react-use';

import { fetchPagedUsers } from '@manyun/auth-hub.service.fetch-paged-users';
import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Divider } from '@manyun/base-ui.ui.divider';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType } from '@manyun/base-ui.ui.table';

import { DutyStatusRadioGroup, DutyStatusText } from './components/alarm-notice-role-table';
import { ChannelCheckbox } from './components/channel-checkbox';

type Role = {
  code: string;
  name: string;
};

export type AlarmNoticeObject = {
  targetId: string;
  name?: string;
  roles?: Role[];
  dutyStatus: string;
  phone: boolean;
  sms: boolean;
  email: boolean;
  internalMsg: boolean;
  disabled?: boolean;
};

export type LocalAlarmNoticeObjectTableProps = {
  blockGuid?: string;
  value?: AlarmNoticeObject[];
  /*之前保存的值，传入放在表格最前列*/
  savedValue?: AlarmNoticeObject[];
  /* 新建编辑复制都是编辑状态，只有详情是不可编辑*/
  isEditable?: boolean;
  /* 区分 */
  mode?: 'edit' | 'create';
  onChange?: (value: AlarmNoticeObject[]) => void;
};

export const LocalAlarmNoticeObjectTable = ({
  blockGuid,
  value,
  savedValue,
  isEditable = true,
  mode = 'create',
  onChange,
}: LocalAlarmNoticeObjectTableProps) => {
  const [loading, setLoading] = useState(false);
  const [alarmNoticeObjects, setAlarmNoticeObjects] = useState<AlarmNoticeObject[]>([]);

  const [rolesOptions, setRolesOptions] = useState<Role[]>([]);
  const [filteredValue, setFilteredValue] = useState<string[]>([]);
  const getAlarmNoticeObjects = async ({
    blockGuid,
    userId,
    userIds,
  }: {
    blockGuid?: string;
    userId?: string;
    userIds?: number[];
  }) => {
    setLoading(true);
    let usersList = [];
    const { error, data } = await fetchPagedUsers({
      userId: Number(userId),
      resourceCode: blockGuid,
      needRole: true,
      pageNum: 1,
      pageSize: 2000,
    });
    setLoading(false);
    if (error) {
      message.error(error.code);
      return;
    }
    usersList = data.data ?? [];

    if (userIds?.length) {
      //请求已选择人员并加到前面
      const { error, data } = await fetchPagedUsers({
        userIds,
        needRole: true,
        pageNum: 1,
        pageSize: 1000,
      });

      const _userIdList = (data.data ?? []).map(item => item.id);

      usersList = [
        ...(data.data ?? []),
        //过滤已选择人员
        ...usersList.filter(item => !_userIdList.includes(item.id)),
      ];
      if (error) {
        message.error(error.code);
        return;
      }
    }

    setAlarmNoticeObjects(
      usersList.map(item => {
        return {
          targetId: String(item.id),
          name: item.name,
          roles: item.roles?.map(item => item) ?? [],
          dutyStatus: '',
          phone: false,
          sms: false,
          email: false,
          internalMsg: false,
          disabled: true,
        };
      })
    );
    const _rolesOptions: Role[] = [];
    usersList.forEach(item => {
      const oldRolesCodes = _rolesOptions.map(item => item.code);
      const roles = item.roles ?? [];
      roles.forEach(role => {
        if (!oldRolesCodes.includes(role.code)) {
          _rolesOptions.push({ name: role.name, code: role.code });
        }
      });
    });
    setFilteredValue([]);
    setRolesOptions(_rolesOptions);
  };

  useDeepCompareEffect(() => {
    if (isEditable && savedValue?.length) {
      const userIds = savedValue.map(item => Number(item.targetId));
      getAlarmNoticeObjects({ userIds, blockGuid });
    }
  }, [blockGuid, savedValue, isEditable]);

  useEffect(() => {
    if (mode === 'create') {
      getAlarmNoticeObjects({ blockGuid });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const rowSelection = useDeepCompareMemo(() => {
    return isEditable
      ? {
          selectedRowKeys: (value ?? []).map(obj => obj.targetId),
          onChange: (_: React.Key[], selectedRows: AlarmNoticeObject[]) => {
            const oldValueCodes = (value ?? []).map(item => item.targetId);
            const _selectedRows = selectedRows.map(item => {
              const originObj = (value ?? []).find(v => v.targetId === item.targetId);
              if (oldValueCodes.includes(item.targetId) && originObj) {
                return {
                  ...item,
                  dutyStatus: originObj.dutyStatus,
                  phone: originObj.phone,
                  sms: originObj.sms,
                  email: originObj.email,
                  internalMsg: originObj.internalMsg,
                };
              } else {
                return {
                  ...item,
                  dutyStatus: 'ALL',
                  internalMsg: true,
                };
              }
            });
            const alarmNoticeCodes = alarmNoticeObjects.map(item => item.targetId);
            const oldHideValue = (value ?? []).filter(
              item => !alarmNoticeCodes.includes(item.targetId)
            );

            onChange?.([..._selectedRows, ...oldHideValue]);
          },
        }
      : undefined;
  }, [alarmNoticeObjects, isEditable, onChange, value]);

  const _onChangeValue = useCallback(
    (record: AlarmNoticeObject) => {
      const _newValues = (value ?? []).map(item => {
        return record.targetId === item.targetId ? record : item;
      });
      onChange?.(_newValues);
    },
    [onChange, value]
  );

  const dataSource = useDeepCompareMemo(() => {
    const oldValueCodes = (value ?? []).map(item => item.targetId);
    let data = alarmNoticeObjects.map(item => {
      const originObj = (value ?? []).find(v => v.targetId === item.targetId);
      if (oldValueCodes.includes(item.targetId) && originObj) {
        return {
          ...item,
          dutyStatus: originObj.dutyStatus,
          phone: originObj.phone,
          sms: originObj.sms,
          email: originObj.email,
          internalMsg: originObj.internalMsg,
          disabled: false,
        };
      }
      return item;
    });
    if (!isEditable) {
      data = data
        .filter(item => oldValueCodes.includes(item.targetId))
        .map(item => {
          return {
            ...item,
            disabled: true,
          };
        });
    }
    return data;
  }, [alarmNoticeObjects, isEditable, value]);

  const columns: ColumnType<AlarmNoticeObject>[] = [
    {
      title: '姓名',
      dataIndex: 'targetId',
      fixed: 'left',
      render: (_, record) => {
        return <UserLink userId={Number(record.targetId)} userName={record.name} target="_blank" />;
      },
    },
    {
      title: '角色',
      dataIndex: 'roles',
      filteredValue,
      filters: isEditable
        ? rolesOptions.map(item => {
            return { text: item.name, value: item.code };
          })
        : undefined,
      onFilter: (value, record) => {
        const rolesCode = record.roles?.map(item => item.code);
        if (rolesCode) {
          return rolesCode.includes(value as string);
        }
        return true;
      },
      render: (_, record) => {
        return (
          <Space size={0} split={<Divider type="vertical" />}>
            {record.roles?.map(item => item.name)}
          </Space>
        );
      },
    },
    {
      title: '排班状态',
      dataIndex: 'dutyStatus',
      width: 300,
      render: (status, record) => {
        return isEditable ? (
          <DutyStatusRadioGroup
            value={status}
            disabled={record.disabled}
            onChange={e => {
              const _value = e.target.value;
              const _newRecord = { ...record, dutyStatus: _value };
              _onChangeValue(_newRecord);
            }}
          />
        ) : (
          <DutyStatusText value={status} />
        );
      },
    },
    {
      title: '电话',
      dataIndex: 'phone',
      render: (_, record) => {
        return <ChannelCheckbox type="phone" record={record} onChange={_onChangeValue} />;
      },
    },
    {
      title: '短信',
      dataIndex: 'sms',
      render: (_, record) => {
        return <ChannelCheckbox type="sms" record={record} onChange={_onChangeValue} />;
      },
    },
    {
      title: '邮件',
      dataIndex: 'email',
      render: (_, record) => {
        return <ChannelCheckbox type="email" record={record} onChange={_onChangeValue} />;
      },
    },
    {
      title: '站内信',
      dataIndex: 'internalMsg',
      render: (_, record) => {
        return <ChannelCheckbox type="internalMsg" record={record} onChange={_onChangeValue} />;
      },
    },
  ];

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      {isEditable && (
        <div style={{ display: 'flex', justifyContent: 'end' }}>
          <UserSelect
            allowClear
            size="small"
            style={{ width: '216px' }}
            onChange={(option: { id: string }) => {
              getAlarmNoticeObjects({ userId: option?.id });
            }}
          />
        </div>
      )}
      <Table
        loading={loading}
        scroll={{ x: 'max-content' }}
        tableLayout="fixed"
        size="small"
        dataSource={dataSource}
        rowKey="targetId"
        columns={columns}
        pagination={{
          showQuickJumper: false,
        }}
        rowSelection={rowSelection}
        onChange={(_, filters) => {
          const roles = filters?.roles ?? [];
          setFilteredValue(roles as string[]);
        }}
      />
    </Space>
  );
};
