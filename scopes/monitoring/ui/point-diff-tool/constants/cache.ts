import type { DeviceType } from '@manyun/monitoring.ui.device-diff-tool';

const LOCAL_KEY = 'DC_BASE_POINT_CONTRAST_DATA';
const LOCAL_EXPORT_KEY = 'DC_BASE_POINT_EXPORT_DATA';

export function getState() {
  try {
    const localData = localStorage.getItem(LOCAL_KEY);
    if (localData === null) {
      return [];
    }
    return JSON.parse(localData);
  } catch (error) {}
}

export function saveState(obj: DeviceType[]) {
  try {
    localStorage.setItem(LOCAL_KEY, JSON.stringify(obj));
  } catch (error) {}
}

export function clearState() {
  window.localStorage.removeItem(LOCAL_KEY);
}

export function getExportState() {
  try {
    const localData = localStorage.getItem(LOCAL_EXPORT_KEY);
    if (localData === null) {
      return [];
    }
    return JSON.parse(localData);
  } catch (error) {}
}

export function saveExportState(obj: DeviceType[]) {
  try {
    localStorage.setItem(LOCAL_EXPORT_KEY, JSON.stringify(obj));
  } catch (error) {}
}

export function clearExportState() {
  window.localStorage.removeItem(LOCAL_EXPORT_KEY);
}
