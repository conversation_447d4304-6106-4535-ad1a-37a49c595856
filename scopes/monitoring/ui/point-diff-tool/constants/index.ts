import dayjs from 'dayjs';
import moment from 'moment';

import { message } from '@manyun/base-ui.ui.message';

import { fetchChartData } from '@manyun/monitoring.service.fetch-chart-data';
import type { DeviceType } from '@manyun/monitoring.ui.device-diff-tool';

import type { PointGuid } from '../components/point-modal';
import { saveExportState } from './cache';

export const deviceColumns = [
  {
    dataIndex: 'name',
    title: '设备名称',
  },
  {
    title: '位置',
    dataIndex: 'spaceGuid',
    render: ({
      idcTag,
      blockTag,
      roomTag,
    }: {
      idcTag: string;
      blockTag: string;
      roomTag: string;
    }) => {
      return `${idcTag}.${blockTag}.${roomTag}`;
    },
  },
];

export const fullScreenStyle = {
  width: '100vw',
  height: '100vh',
  position: 'fixed',
  top: 0,
  left: 0,
  zIndex: 99,
};

export const getExportData = (pointGuids: PointGuid[], data: unknown) => {
  const firstLines: unknown = [];
  pointGuids.forEach(_ => {
    firstLines.push('设备名称:');
    firstLines.push('测点名称:');
    firstLines.push('时间');
    firstLines.push('数值');
  });
  const contentList: unknown = [];
  const maxLengthItem = data.reduce((result: unknown, item: unknown) => {
    if (result.length > item.length) {
      return result;
    } else {
      return item;
    }
  }, []);
  maxLengthItem.forEach((_: unknown, index: number) => {
    const pointExportArr: unknown = [];
    pointGuids.forEach((pointGuidItem, pointGuidIndex) => {
      pointExportArr.push(pointGuidItem.deviceName);
      pointExportArr.push(pointGuidItem.pointName);
      if (data[pointGuidIndex][index]) {
        pointExportArr.push(moment(data[pointGuidIndex][index][0]).format('YYYY-MM-DD HH:mm:ss'));
        pointExportArr.push(data[pointGuidIndex][index][1]);
      } else {
        pointExportArr.push(' ');
        pointExportArr.push(' ');
      }
    });
    contentList.push(pointExportArr);
  });
  return [firstLines, ...contentList];
};

export const getAllCheckedValues = (list: DeviceType[], checkedValues: string[]) => {
  const allcheckedValues = list.reduce((result: string[], item: DeviceType) => {
    const { pointList = [] } = item;
    pointList.forEach(pointItem => {
      result.push(item.guid + pointItem.code);
    });
    return result;
  }, []);
  const newCheckedValues = checkedValues.filter(item => allcheckedValues.includes(item));
  return newCheckedValues;
};

export const maxSelectCount = 50;

export const setExportData = async ({
  selectDevices,
  idc,
}: {
  selectDevices: DeviceType[];
  idc: string;
}) => {
  if (!idc) {
    return;
  }

  const pointGuids: PointGuid[] = [];
  selectDevices.forEach(deviceItem => {
    const { pointList = [] } = deviceItem;
    pointList.forEach(pointItem => {
      const { code, name } = pointItem;
      pointGuids.push({
        deviceGuid: deviceItem.guid,
        pointCode: code,
        pointName: name,
        deviceName: deviceItem.name,
      });
    });
  });

  const { data, error } = await fetchChartData({
    pointGuidList: pointGuids.map(item => {
      return { deviceGuid: item.deviceGuid, pointCode: item.pointCode };
    }),
    startTime: dayjs().subtract(2, 'hours').valueOf(),
    endTime: dayjs().valueOf(),
    function: 'MAX',
    interval: 'S',
    idcTag: idc,
  });
  if (error) {
    message.error(error);
    return;
  }
  if (data.data.length) {
    saveExportState(getExportData(pointGuids, data.data));
  }
};
