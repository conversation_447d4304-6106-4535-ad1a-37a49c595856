import React, { ReactNode } from 'react';

import { Row } from '@manyun/base-ui.ui.grid';
import { Typography } from '@manyun/base-ui.ui.typography';

type TitleProps = {
  title: ReactNode;
  style?: React.CSSProperties;
};

export function Title({ title, style }: TitleProps) {
  return (
    <Row style={style} align="middle">
      <Typography.Text type="danger">*</Typography.Text>
      {title}
    </Row>
  );
}
