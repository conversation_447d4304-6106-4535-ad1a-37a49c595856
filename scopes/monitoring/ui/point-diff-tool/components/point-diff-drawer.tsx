import React from 'react';
import { useSelector } from 'react-redux';

import cloneDeep from 'lodash.clonedeep';

import { message } from '@manyun/base-ui.ui.message';

import { selectMe } from '@manyun/auth-hub.state.user';
import type { DeviceType } from '@manyun/monitoring.ui.device-diff-tool';
import type { TreePoint } from '@manyun/resource-hub.ui.resource-point-drawer';
import { ResourcePointDrawer } from '@manyun/resource-hub.ui.resource-point-drawer';

import { maxSelectCount, setExportData } from '../constants';
import { saveState } from '../constants/cache';

export type PointDiffDrawerProps = {
  open: boolean;
  onClose: () => void;
  selectDeviceList: DeviceType[];
  setSelectDeviceList: (arg: DeviceType[]) => void;
  setCollapseactiveKey: (arg: string[]) => void;
  oldCheckedValues: string[];
  getAxisLinelist: (arg1: string | string[], arg2: DeviceType[]) => void;
};

export function PointDiffDrawer({
  open,
  selectDeviceList,
  setSelectDeviceList,
  setCollapseactiveKey,
  oldCheckedValues,
  getAxisLinelist,
  onClose,
}: PointDiffDrawerProps) {
  const { idc } = useSelector(selectMe, (left, right) => left.idc === right.idc);

  const onChange = async (points: TreePoint[]) => {
    if (!points.length) {
      message.error('请选择至少一个测点！');
      return;
    }
    const isAllDevicePoint = points.every(item => item.target.type === 'DEVICE');

    if (!isAllDevicePoint) {
      message.error('请选择设备类型测点！');
      return;
    }

    const copySelectDeviceList = cloneDeep(selectDeviceList);

    const currentSelectDeviceGuids = Array.from(new Set(points.map(item => item.target.guid)));
    const currentSelectDevices = currentSelectDeviceGuids.map(guid => {
      //抽屉选中的测点
      const point = points.find(item => item.target.guid === guid);
      //处理后跟随设备的测点list
      const pointList = points.filter(item => item.target.guid === guid);
      return { key: guid, ...point?.target?.device?.toApiObject(), pointList };
    });
    let checkedValues: string[] = [];
    currentSelectDevices.forEach(item => {
      const { pointList = [] } = item;
      pointList.forEach(pointItem => {
        checkedValues.push(`${item.guid}${pointItem.code}`);
      });
    });

    const oldSelectGuids = copySelectDeviceList.map(item => item.guid);

    const newSelectDevices = currentSelectDevices.filter(
      item => !oldSelectGuids.includes(item.guid!)
    );

    const oldSelectDevices = copySelectDeviceList.map((alreadySelectItem: DeviceType) => {
      const { pointList: oldPointList = [] } = alreadySelectItem;
      const pointCodes = oldPointList.map(item => item.code);
      currentSelectDevices.forEach(newSelectItem => {
        if (newSelectItem.guid === alreadySelectItem.guid) {
          const { pointList = [] } = newSelectItem;
          pointList.forEach(pointItem => {
            if (!pointCodes.includes(pointItem.code)) {
              oldPointList.push(pointItem);
            }
          });
        }
      });
      return alreadySelectItem;
    });

    const selectDevices: DeviceType[] = [...newSelectDevices, ...oldSelectDevices];

    const selectLength = selectDevices.reduce((result, item) => {
      const { pointList = [] } = item;
      return result + pointList.length;
    }, 0);

    if (selectLength > maxSelectCount) {
      message.error('添加失败，测点比对个数已达上限');
      return;
    }

    if (!idc) {
      return;
    }
    await setExportData({ selectDevices, idc });

    checkedValues = Array.from(new Set([...checkedValues, ...oldCheckedValues]));
    getAxisLinelist(checkedValues, selectDevices);
    setSelectDeviceList(selectDevices);
    saveState(selectDevices);
    setCollapseactiveKey(selectDevices.map(item => item.guid));
    onClose();
  };

  return (
    <ResourcePointDrawer
      open={open}
      placement="left"
      destroyOnClose
      isQueryNon
      maxCheckedCount={50}
      resourceTreeProps={{
        spaceGuid: idc!,
        fetchDevicesParams: { operationStatus: 'ON' },
      }}
      onClose={onClose}
      onChange={onChange}
    />
  );
}
