import React, { useState } from 'react';
import { useSelector } from 'react-redux';

import QuestionCircleOutlined from '@ant-design/icons/es/icons/QuestionCircleOutlined';
import cloneDeep from 'lodash.clonedeep';
import get from 'lodash.get';
import moment from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Form } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';

import { selectMe } from '@manyun/auth-hub.state.user';
import { fetchChartData } from '@manyun/monitoring.service.fetch-chart-data';
import type { DeviceType } from '@manyun/monitoring.ui.device-diff-tool';
import { fetchPointsByCondition } from '@manyun/resource-hub.service.fetch-points-by-condition';
import { useSpaces } from '@manyun/resource-hub.ui.location-tree-select';
import { RoomSelect } from '@manyun/resource-hub.ui.room-select';

import { AssetClassificationApiTreeSelect } from '@manyun/dc-brain.legacy.components';
import { equipmentManageService } from '@manyun/dc-brain.legacy.services';

import { deviceColumns, getExportData, maxSelectCount } from '../constants';
import { saveExportState, saveState } from '../constants/cache';
import type { PointObject, PointType } from '../point-diff-tool.type';
import styles from '../point.module.less';
import { TableTransfer } from './table-transfer';
import { Title } from './title';

const { Option } = Select;
type PointToolModalProps = {
  visible: boolean;
  onClose: () => void;
  alreadySelectLength: number;
  sameTypeItem: DeviceType | null;
  sameDeviceItem: DeviceType | null;
  selectDeviceList: DeviceType[];
  setSelectDeviceList: (arg: DeviceType[]) => void;
  setCollapseactiveKey: (arg: string[]) => void;
  oldCheckedValues: string[];
  getAxisLinelist: (arg1: string | string[], arg2: DeviceType[]) => void;
};

export type PointGuid = {
  deviceGuid: string;
  pointCode: string;
  pointName?: string;
  deviceName?: string;
  deviceType?: string;
  spaceGuid?: string;
};

export function PointModal({
  visible,
  onClose,
  sameTypeItem,
  sameDeviceItem,
  selectDeviceList,
  setSelectDeviceList,
  setCollapseactiveKey,
  alreadySelectLength,
  oldCheckedValues,
  getAxisLinelist,
}: PointToolModalProps) {
  const [form] = Form.useForm();
  const { idc } = useSelector(selectMe, (left, right) => left.idc === right.idc);

  const [pointList, setPointList] = useState<PointType[]>([]);
  const [pointTargetKeys, setPointTargetKeys] = useState<string[]>([]);

  const [addSameTypePoint, setAddSameTypePoint] = useState<boolean>(false);

  const [selectDeviceItem, setSelectDeviceItem] = useState<DeviceType | {}>({});
  const [deviceList, setDeviceList] = useState<DeviceType[]>([]);
  const [deviceTargetKeys, setDeviceTargetKeys] = useState<string[]>([]);

  const [{ treeSpaces }] = useSpaces({
    authorizedOnly: true,
    nodeTypes: ['IDC', 'BLOCK'],
    disabledTypes: [],
    idc: idc ?? undefined,
    includeVirtualBlocks: false,
  });
  React.useEffect(() => {
    if (visible) {
      const spaceGuidList: never[] = [];
      if (sameTypeItem) {

        const { spaceGuid: { blockGuid }, topCategory, secondCategory, deviceType, guid } = sameTypeItem;
        const formParams = {
          spaceGuidList,
          cascaderList: {
            firstCategoryCode: topCategory,
            secondCategoryCode: secondCategory,
            thirdCategorycode: deviceType,
            numbered: true,
          },
          name: guid,
        };
        form.setFieldsValue(formParams);
        const params = {
          spaceGuidList: idc ? [blockGuid] : [],
          topCategory: topCategory,
          secondCategory: secondCategory,
          deviceTypeList: [deviceType],
          pageNum: 1,
          pageSize: 500,
        };
        setPointList([]);
        setPointTargetKeys([]);
        setDeviceTargetKeys([]);
        setAddSameTypePoint(true);
        setSelectDeviceItem(sameTypeItem);
        getDeviceName(params);
      } else if (sameDeviceItem) {
        const { topCategory, secondCategory, deviceType, guid, pointList = [] } = sameDeviceItem;
        const pointCodes = pointList.map(item => item.code);
        const formParams = {
          spaceGuidList,
          cascaderList: {
            firstCategoryCode: topCategory,
            secondCategoryCode: secondCategory,
            thirdCategorycode: deviceType,
            numbered: true,
          },
          name: guid,
        };
        form.setFieldsValue(formParams);
        const deviceParams = {
          spaceGuidList,
          topCategory: topCategory,
          secondCategory: secondCategory,
          deviceTypeList: [deviceType],
          pageNum: 1,
          pageSize: 500,
        };
        const pointParams = {
          spaceGuid: idc,
          deviceType,
        };
        setPointTargetKeys(pointCodes);
        setDeviceTargetKeys([]);
        setSelectDeviceItem(sameDeviceItem);
        getPointList(pointParams);
        getDeviceName(deviceParams);
      } else {
        form.setFieldsValue({
          spaceGuidList,
          cascaderList: null,
          name: null,
        });
        setPointList([]);
        setDeviceList([]);
        setPointTargetKeys([]);
        setDeviceTargetKeys([]);
        setAddSameTypePoint(false);
      }
    }
    // eslint-disable-next-line
  }, [form, idc, sameTypeItem, visible]);
  const onDeviceNameChange = async () => {
    const values = await form.validateFields();
    const { spaceGuidList, cascaderList } = values;
    const thirdCategorycode = get(cascaderList, 'thirdCategorycode', null);
    const params = {
      spaceGuidList: spaceGuidList.length ? spaceGuidList : [treeSpaces[0].value],
      topCategory: cascaderList?.firstCategoryCode,
      secondCategory: cascaderList?.secondCategoryCode,
      deviceTypeList: thirdCategorycode ? [thirdCategorycode] : null,
      pageNum: 1,
      pageSize: 500,
    };
    getDeviceName(params);
    setPointTargetKeys([]);
    setDeviceTargetKeys([]);
    setSelectDeviceItem({});
    setPointList([]);
    form.setFieldsValue({ name: null });
  };

  const getDeviceName = async (params: {
    spaceGuidList: string[] | null;
    topCategory: string | null;
    secondCategory: string | null;
    deviceTypeList: string[] | null;
    pageNum: number;
    pageSize: number;
  }) => {
    const {
      response: { data = [] },
    } = await equipmentManageService.fetchEquipmentListPage({ ...params, operationStatus: 'ON' });
    const list = data.map((item: DeviceType) => {
      return {
        key: item.guid,
        ...item,
      };
    });
    setDeviceList(list);
  };

  const onSelectDeviceChange = (value: string) => {
    const deviceItem = deviceList.find(item => item.guid === value);
    if (deviceItem) {
      setSelectDeviceItem(deviceItem);
    } else {
      setSelectDeviceItem({});
    }
  };

  const onSearch = () => {
    const { deviceType = '', spaceGuid } = selectDeviceItem;
    if (!deviceType) {
      message.error('请选择一个设备');
      return;
    }

    const params = {
      spaceGuid: spaceGuid.blockGuid,
      deviceType,
    };
    getPointList(params);
  };

  const getPointList = async (params: unknown) => {
    const {
      data: { data },
    } = await fetchPointsByCondition(params);
    const list = data.map(item => {
      const { priority, pointType } = item;
      return {
        key: item.code,
        ...item,
        filterString: priority ? `${pointType.code}CORE` : pointType.code,
      };
    });
    const corePoints = list.filter(item => item.priority);
    const otherPoints = list.filter(item => !item.priority);
    setPointList([...corePoints, ...otherPoints]);
  };

  const onReset = () => {
    form.setFieldsValue({
      spaceGuidList: [],
      cascaderList: {
        firstCategoryCode: '',
        secondCategoryCode: '',
        thirdCategorycode: '',
        numbered: true,
      },
      name: null,
    });
    setPointList([]);
    setDeviceList([]);
    setSelectDeviceItem({});
    setPointTargetKeys([]);
    setDeviceTargetKeys([]);
    setAddSameTypePoint(false);
  };

  const onSelectSameTypePoint = ({ target: { checked } }: { target: { checked: boolean } }) => {
    if (pointTargetKeys.length * 2 + alreadySelectLength > maxSelectCount) {
      message.error('测点比对个数已达上限');
      return;
    }
    setAddSameTypePoint(checked);
    if (checked) {
      setTimeout(function () {
        document.getElementById('selectDevie')?.scrollIntoView();
      }, 200);
    }
  };

  const onOk = async () => {
    let selectDevices: DeviceType[];
    let checkedValues: string[] = [];

    if (sameTypeItem) {
      const currentSelectDevices = deviceList
        .filter((item: DeviceType) => deviceTargetKeys.includes(item.guid))
        .map((deviceItem: DeviceType) => {
          deviceItem.pointList = sameTypeItem.pointList;
          sameTypeItem.pointList?.forEach(pointItem => {
            checkedValues.push(`${deviceItem.guid}${pointItem.code}`);
          });
          return deviceItem;
        });
      selectDevices = [...currentSelectDevices, ...selectDeviceList];
    } else if (sameDeviceItem) {
      selectDevices = selectDeviceList.map(item => {
        if (item.guid === sameDeviceItem.guid) {
          const oldPointList = cloneDeep(sameDeviceItem.pointList) || [];
          const oldPointKeys = oldPointList?.map(item => item.code);
          if (pointTargetKeys?.length > oldPointKeys?.length) {
            const newAddKeys = pointTargetKeys.filter(item => !oldPointKeys.includes(item));
            newAddKeys.forEach(item => {
              checkedValues.push(`${sameDeviceItem.guid}${item}`);
            });
          }
          item.pointList = pointList.filter((item: PointType) =>
            pointTargetKeys.includes(item.code)
          );
        }
        return item;
      });
    } else {
      if (!pointTargetKeys.length) {
        message.error('请选择至少一个测点');
        return;
      }
      const currentSelectPoints = pointList.filter((item: PointType) =>
        pointTargetKeys.includes(item.code)
      );
      //当前选择设备
      const currentSelectDevices = deviceList
        .filter((item: DeviceType) =>
          [...deviceTargetKeys, selectDeviceItem.guid].includes(item.guid)
        )
        .map((item: DeviceType) => {
          item.pointList = currentSelectPoints;
          return item;
        });

      currentSelectDevices.forEach(item => {
        const { pointList = [] } = item;
        pointList.forEach(pointItem => {
          checkedValues.push(`${item.guid}${pointItem.code}`);
        });
      });

      const oldSelectGuids = selectDeviceList.map(item => item.guid);

      const newSelectDevices = currentSelectDevices.filter(
        item => !oldSelectGuids.includes(item.guid)
      );

      const oldSelectDevices = selectDeviceList.map((alreadySelectItem: DeviceType) => {
        const { pointList: oldPointList = [] } = alreadySelectItem;
        const pointCodes = oldPointList.map(item => item.code);
        currentSelectDevices.forEach(newSelectItem => {
          if (newSelectItem.guid === alreadySelectItem.guid) {
            const { pointList = [] } = newSelectItem;
            pointList.forEach(pointItem => {
              if (!pointCodes.includes(pointItem.code)) {
                oldPointList.push(pointItem);
              }
            });
          }
        });
        return alreadySelectItem;
      });

      selectDevices = [...newSelectDevices, ...oldSelectDevices];
    }

    const selectLength = selectDevices.reduce((result, item) => {
      const { pointList = [] } = item;
      return result + pointList.length;
    }, 0);

    if (selectLength > maxSelectCount) {
      message.error('添加失败，测点比对个数已达上限');
      return;
    }
    //导出数据
    const pointGuids: PointGuid[] = [];
    selectDevices.forEach(deviceItem => {
      const { pointList = [] } = deviceItem;
      pointList.forEach(pointItem => {
        const { code, name } = pointItem;
        pointGuids.push({
          deviceGuid: deviceItem.guid,
          pointCode: code,
          pointName: name,
          deviceName: deviceItem.name,
        });
      });
    });
    const { data } = await fetchChartData({
      pointGuidList: pointGuids.map(item => {
        return { deviceGuid: item.deviceGuid, pointCode: item.pointCode };
      }),
      startTime: moment().subtract(2, 'hours').valueOf(),
      endTime: moment().valueOf(),
      function: 'MAX',
      interval: 'S',
      idcTag: idc || '',
    });

    if (data.data.length) {
      saveExportState(getExportData(pointGuids, data.data));
    }

    checkedValues = Array.from(new Set([...checkedValues, ...oldCheckedValues]));
    getAxisLinelist(checkedValues, selectDevices);
    setSelectDeviceList(selectDevices);
    saveState(selectDevices);
    setCollapseactiveKey(selectDevices.map(item => item.guid));
    onClose();
  };

  let haveSelectLength = maxSelectCount - alreadySelectLength;
  let selectLength = pointTargetKeys.length * (1 + deviceTargetKeys.length);

  if (sameDeviceItem) {
    const { pointList = [] } = sameDeviceItem;
    haveSelectLength = maxSelectCount - alreadySelectLength + pointList?.length;
  }

  if (sameTypeItem) {
    const { pointList = [] } = sameTypeItem;
    selectLength = deviceTargetKeys.length * pointList.length;
  }

  const onSetPointTargetKeys = (nextTargetKeys: React.SetStateAction<string[]>) => {
    if (nextTargetKeys.length > haveSelectLength) {
      message.error('添加失败，测点比对个数已达上限');
      return;
    }
    setPointTargetKeys(nextTargetKeys);
  };
  const onSetDeviceTargetKeys = (nextTargetKeys: React.SetStateAction<string[]>) => {
    if (nextTargetKeys.length * pointTargetKeys.length > haveSelectLength) {
      message.error('添加失败，测点比对个数已达上限');
      return;
    }
    if (sameTypeItem) {
      const { pointList = [] } = sameTypeItem;
      if (nextTargetKeys.length * pointList.length > haveSelectLength) {
        message.error('添加失败，测点比对个数已达上限');
        return;
      }
    }
    setDeviceTargetKeys(nextTargetKeys);
  };
  const pointColumns = [
    {
      dataIndex: 'code',
      title: '测点ID',
      fixed: 'left',
    },
    {
      dataIndex: 'name',
      title: '测点名称',
    },
    {
      dataIndex: 'dataType',
      with: 120,
      title: '测点类型',
      render: (item: PointObject) => get(item, 'name', '--'),
      filters: [
        { text: '原始测点', value: 'ORI' },
        { text: '聚合测点', value: 'AGG_DEVICE' },
        { text: '加工测点', value: 'CAL_DEVICE' },
        { text: '核心测点', value: 'CORE' },
      ],
      onFilter: (value: string, record: { filterString: string | string[] }) =>
        record.filterString.includes(value),
    },
  ];
  return (
    <Modal
      width={872}
      bodyStyle={{
        height: '500px',
        overflowY: 'scroll',
      }}
      title={
        <Space>
          {sameTypeItem ? '添加同类型设备测点' : '添加测点'}
          <Tooltip placement="top" title="仅支持添加已启用设备的测点">
            <QuestionCircleOutlined />
          </Tooltip>
          <Typography.Text
            type="secondary"
            style={{ fontWeight: '400', fontSize: '14px', marginLeft: '13px' }}
          >
            {`${selectLength}/${haveSelectLength}`}
          </Typography.Text>
        </Space>
      }
      open={visible}
      onOk={onOk}
      onCancel={onClose}
    >
      <Form layout="vertical" form={form}>
        <Row gutter={16} className={styles.pointModalFrom}>
          <Col span={6}>
            <Form.Item label="位置" name="spaceGuidList">
              <RoomSelect
                blockSelectProps={{
                  authorizedOnly: true,
                  idc: idc ?? undefined,
                  disabled: !!(sameTypeItem || sameDeviceItem),
                }}
                disabled={!!(sameTypeItem || sameDeviceItem)}
                maxTagCount={0}
                mode="multiple"
                onChange={onDeviceNameChange}
              />
            </Form.Item>
          </Col>
          <Col span={6} style={{ paddingRight: 0 }}>
            <Form.Item
              label={
                <Space>
                  设备名称<Typography.Text type="danger">*</Typography.Text>
                  <Tooltip placement="top" title="当前仅支持选择设备后再选择测点进行添加比对">
                    <QuestionCircleOutlined />
                  </Tooltip>
                </Space>
              }
              name="cascaderList"
            >
              <AssetClassificationApiTreeSelect
                dataType={['snDevice']}
                placeholder="设备分类"
                disabled={!!(sameTypeItem || sameDeviceItem)}
                category="allCategoryCode"
                allowClear
                onChange={onDeviceNameChange}
              />
            </Form.Item>
          </Col>
          <Col span={6} style={{ paddingLeft: 0 }}>
            <Form.Item label=" " name="name">
              <Select
                showSearch
                allowClear
                placeholder="设备名称"
                disabled={!!(sameTypeItem || sameDeviceItem)}
                optionFilterProp="children"
                onChange={onSelectDeviceChange}
              >
                {deviceList.map(item => {
                  const { guid, name } = item;
                  return (
                    <Option key={guid} value={guid}>
                      {name}
                    </Option>
                  );
                })}
              </Select>
            </Form.Item>
          </Col>
          <Col span={6}>
            {!(sameTypeItem || sameDeviceItem) && (
              <Space style={{ marginTop: '30px' }}>
                <Button type="primary" onClick={onSearch}>
                  确认
                </Button>
                <Button onClick={onReset}>重置</Button>
              </Space>
            )}
          </Col>
        </Row>
      </Form>
      {!sameTypeItem && (
        <>
          <Title title="测点选择" style={{ paddingBottom: '16px' }} />
          <TableTransfer
            listStyle={{ width: 'calc(50% - 26px)' }}
            dataSource={pointList}
            targetKeys={pointTargetKeys}
            showSearch
            filterOption={(inputValue: string, item: { name: string; code: string }) =>
              item.name.indexOf(inputValue) !== -1 || item.code.indexOf(inputValue) !== -1
            }
            columns={pointColumns}
            onChange={(nextTargetKeys: string[]) => onSetPointTargetKeys(nextTargetKeys)}
          />
        </>
      )}
      {!(sameTypeItem || sameDeviceItem) && (
        <Space style={{ padding: '16px 0' }}>
          <Checkbox checked={addSameTypePoint} onChange={onSelectSameTypePoint}>
            <a href="#selectDevie" className={styles.linkToDevice}>
              添加同类型设备测点{' '}
            </a>
          </Checkbox>
          <Tooltip
            placement="top"
            title="添加同类型设备后，会自动添加当前被匹配设备已选择测点的相同测点"
          >
            <QuestionCircleOutlined />
          </Tooltip>
        </Space>
      )}

      {!sameDeviceItem && addSameTypePoint && (
        <div id="selectDevie">
          <Title title="设备选择" style={{ paddingBottom: '16px' }} />
          <TableTransfer
            listStyle={{ width: 'calc(50% - 26px)' }}
            dataSource={deviceList.filter(
              item => !selectDeviceList.map(deviceItem => deviceItem.guid).includes(item.guid)
            )}
            targetKeys={deviceTargetKeys}
            showSearch
            filterOption={(inputValue: string, item: { name: string }) =>
              item.name.indexOf(inputValue) !== -1
            }
            columns={deviceColumns}
            onChange={(nextTargetKeys: string[]) => onSetDeviceTargetKeys(nextTargetKeys)}
          />
        </div>
      )}
    </Modal>
  );
}
