@import '~@manyun/base-ui.theme.theme/dist/theme.variable.less';

.container {
  width: calc(100vw - 212px);
  :global(.manyun-card-head) {
    font-weight: 400;
  }
  :global(.manyun-card-body) {
    padding: 0;
  }
  .contentLeft {
    .collapse {
      margin: 12px;
      background-color: @white;
      :global(.manyun-collapse-item) {
        border: 1px solid @normal-color;
        border-bottom: none;
        margin-top: 16px;
        :global(.manyun-collapse-header) {
          border-bottom: 1px solid @normal-color;
          background-color: @descriptions-bg;
        }
      }
    }
    .checkbox {
      color: @text-color-secondary;
      .checkboxItem {
        padding: 12px 5px;
        cursor: pointer;

        &:hover {
          background-color: @primary-1;
        }
        .checkboxItemLabel {
          max-width: 70%;
          align-items: center;
          :global(.manyun-checkbox) {
            margin-bottom: 0.2em;
          }
        }
        .checkboxItemDelete {
          visibility: hidden;
        }
        &:hover > .checkboxItemDelete {
          visibility: inherit;
        }
      }
      .activeCheckboxItem {
        background-color: @primary-1;
      }
    }
  }
}
.pointModalFrom {
  width: 100%;
}
.linkToDevice {
  color: @text-color;
}
.download {
  color: @text-color-secondary;
}
