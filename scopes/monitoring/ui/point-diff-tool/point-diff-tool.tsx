import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import {
  DeleteOutlined,
  FullscreenExitOutlined,
  FullscreenOutlined,
  PlusOutlined,
  PlusSquareOutlined,
  QuestionCircleOutlined,
} from '@ant-design/icons';
import classNames from 'classnames';
import dayjs from 'dayjs';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Collapse } from '@manyun/base-ui.ui.collapse';
import { Empty } from '@manyun/base-ui.ui.empty';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import { saveJSONAsXLSX } from '@manyun/base-ui.util.xlsx';

import { selectMe } from '@manyun/auth-hub.state.user';
import { useLayout } from '@manyun/dc-brain.context.layout';
import { DurationSelect } from '@manyun/monitoring.chart.duration-select';
import type { IntervalKeyMap, TimeRange } from '@manyun/monitoring.chart.duration-select';
import { PointsLine } from '@manyun/monitoring.chart.points-line';
import { PointsStateLine } from '@manyun/monitoring.chart.points-state-line';
import { generatePointValueMapping } from '@manyun/monitoring.model.point';
import { fetchChartData } from '@manyun/monitoring.service.fetch-chart-data';
import type { DeviceType } from '@manyun/monitoring.ui.device-diff-tool';
import { DeviceTypeText } from '@manyun/resource-hub.ui.device-type-text';

import { PointDiffDrawer } from './components/point-diff-drawer';
import type { PointGuid } from './components/point-modal';
import { PointModal } from './components/point-modal';
import { fullScreenStyle, getAllCheckedValues, maxSelectCount } from './constants';
import { clearExportState, clearState, getState, saveState } from './constants/cache';
import type {
  PointDiffToolProps,
  PointType,
  diffAxisPoint,
  pointGuid,
  sameAxisPoint,
} from './point-diff-tool.type';
import styles from './point.module.less';

export function PointDiffTool({ title }: PointDiffToolProps) {
  const { sideBarTab } = useLayout();
  const { idc } = useSelector(selectMe, (left, right) => left.idc === right.idc);

  const [fullScreen, setFullScreen] = useState<boolean>(false);

  const [checkedValues, setCheckedValues] = useState<string[]>([]);
  const [collapseactiveKey, setCollapseactiveKey] = useState<string[] | string>(
    getState().map((item: DeviceType) => item.guid)
  );
  const [pointDrawerOpen, setPointDrawerOpen] = useState<boolean>(false);

  const [visible, setVisible] = useState<boolean>(false);
  const [axis, setAxis] = useState<string>('sameAxis');
  const [selectDeviceList, setSelectDeviceList] = useState<DeviceType[]>(getState());

  const [diffAxislist, setDiffAxislist] = useState<diffAxisPoint[]>([]);

  const [sameAiAxispointList, setSameAiAxispointList] = useState<sameAxisPoint | null>();
  const [sameDiAxispointList, setSameDiAxispointList] = useState<sameAxisPoint | null>();

  const [startTime, setStartTime] = useState<number>();
  const [endTime, setEndTime] = useState<number>();
  const [interval, setInterval] = useState<IntervalKeyMap>();

  const [sameTypeItem, setSameTypeItem] = useState<DeviceType | null>(null);
  const [sameDeviceItem, setSameDeviceItem] = useState<DeviceType | null>(null);

  useEffect(() => {
    if (sideBarTab === 'pointdifftool') {
      setSelectDeviceList(getState());
    }
  }, [sideBarTab]);
  const maxLength = selectDeviceList.reduce((result, item) => {
    const { pointList = [] } = item;
    return result + pointList.length;
  }, 0);

  const maxSelectCountfn = () => {
    if (maxLength > maxSelectCount - 1) {
      message.error('测点比对个数已达上限');
      return;
    }
  };

  const onShowModel = (e: { stopPropagation: () => void }, sameTypeItem: DeviceType | null) => {
    e.stopPropagation();
    maxSelectCountfn();
    setSameTypeItem(sameTypeItem);
    setVisible(true);
  };

  const onShowDrawer = (e: { stopPropagation: () => void }) => {
    e.stopPropagation();
    maxSelectCountfn();
    setPointDrawerOpen(true);
  };

  const onAddSameDevice = (e: { stopPropagation: () => void }, sameTypeItem: DeviceType | null) => {
    e.stopPropagation();
    maxSelectCountfn();
    setSameDeviceItem(sameTypeItem);
    setVisible(true);
  };

  const onClose = () => {
    setSameTypeItem(null);
    setSameDeviceItem(null);
    setVisible(false);
  };

  const onDeleteDevice = (e: React.MouseEvent<HTMLSpanElement, MouseEvent>, guid: string) => {
    e.stopPropagation();
    const list = selectDeviceList.filter(item => item.guid !== guid);
    const newCheckedValues = getAllCheckedValues(list, checkedValues);
    getAxisLinelist(newCheckedValues, list);
    setSelectDeviceList(list);
    saveState(list);
  };
  const onDeletePoint = (e: React.MouseEvent<HTMLSpanElement, MouseEvent>, id: string) => {
    e.stopPropagation();
    const list = selectDeviceList.map(deviceItem => {
      const { pointList = [] } = deviceItem;
      const newPointList = pointList.filter(item => deviceItem.guid + item.code !== id);
      return { ...deviceItem, pointList: newPointList };
    });
    const newCheckedValues = getAllCheckedValues(list, checkedValues);
    getAxisLinelist(newCheckedValues, list);
    setSelectDeviceList(list);
    saveState(list);
  };

  const onCheckBox = (checkedValues: string[]) => {
    getAxisLinelist(checkedValues, selectDeviceList);
  };

  const onSelecAxis = (value: string) => {
    setAxis(value);
  };

  const getAxisLinelist = (checkedValues: string[], selectDeviceList: DeviceType[]) => {
    setCheckedValues(checkedValues);

    const diffAxislist: diffAxisPoint[] = [];

    const sameAiAxispointList: pointGuid[] = [];
    const sameDiAxispointList: pointGuid[] = [];
    let idc = '';
    selectDeviceList.forEach(deviceItem => {
      const {
        pointList = [],
        guid,
        spaceGuid: { idcGuid },
      } = deviceItem;
      idc = idcGuid;
      pointList.forEach(pointItem => {
        const { code, name, dataType, unit, validLimits } = pointItem;

        if (checkedValues.includes(guid + code)) {
          if (dataType.code === 'AI') {
            //同轴ai
            sameAiAxispointList.push({
              deviceGuid: guid,
              pointCode: code,
              unit,
              serieName: `${deviceItem.name}${name}`,
            });
            //分轴
            diffAxislist.push({
              idcTag: idcGuid,
              validLimits,
              pointGuid: {
                deviceGuid: guid,
                pointCode: code,
                unit,
                serieName: `${deviceItem.name}${name}`,
                dataType,
              },
            });
          } else {
            //同轴di
            sameDiAxispointList.push({
              deviceGuid: guid,
              pointCode: code,
              serieName: `${deviceItem.name}${name}`,
              valueMapping: generatePointValueMapping(validLimits),
            });
            //分轴di
            diffAxislist.push({
              idcTag: idcGuid,
              validLimits,
              pointGuid: {
                deviceGuid: guid,
                pointCode: code,
                serieName: `${deviceItem.name}${name}`,
                dataType,
              },
            });
          }
        }
      });
    });
    setDiffAxislist(diffAxislist);
    if (sameAiAxispointList.length) {
      setSameAiAxispointList({ idcTag: idc, pointGuids: sameAiAxispointList });
    } else {
      setSameAiAxispointList(null);
    }
    if (sameDiAxispointList.length) {
      setSameDiAxispointList({ idcTag: idc, pointGuids: sameDiAxispointList });
    } else {
      setSameDiAxispointList(null);
    }
  };

  const onGetParams = ([momentStartTime, momentEndTime]: TimeRange, interval: IntervalKeyMap) => {
    setStartTime(momentStartTime.valueOf());
    setEndTime(momentEndTime.valueOf());
    setInterval(interval);
  };

  const onFileExport = async () => {
    if (startTime && endTime && idc) {
      const pointGuids: PointGuid[] = [];
      selectDeviceList.forEach(deviceItem => {
        const { pointList = [] } = deviceItem;
        pointList.forEach(pointItem => {
          if (checkedValues.includes(deviceItem.guid + pointItem.code)) {
            pointGuids.push({
              deviceGuid: deviceItem.guid,
              pointCode: pointItem.code,
              pointName: pointItem.name,
              deviceName: deviceItem.name,
            });
          }
        });
      });

      const { data, error } = await fetchChartData({
        pointGuidList: pointGuids.map(item => {
          return { deviceGuid: item.deviceGuid, pointCode: item.pointCode };
        }),
        startTime,
        endTime,
        function: 'MAX',
        interval: interval ?? 'S',
        idcTag: idc,
      });
      if (error) {
        message.error(error);
        return;
      }
      if (data.data.length) {
        const exportConfig = data.data.map((series, index) => {
          return {
            data: series.map(item => {
              return {
                deviceName: pointGuids[index]?.deviceName,
                pointName: pointGuids[index]?.pointName,
                time: dayjs(item[0]).format('YYYY-MM-DD HH:mm:ss'),
                value: item[1],
              };
            }),
            headers: [
              {
                title: '设备名称',
                dataIndex: 'deviceName',
              },
              {
                title: '测点名称',
                dataIndex: 'pointName',
              },
              {
                title: '时间',
                dataIndex: 'time',
              },
              {
                title: '数值',
                dataIndex: 'value',
              },
            ],
          };
        });
        saveJSONAsXLSX(exportConfig, '测点对比');
      }
    }
  };

  const onClearDate = () => {
    setCheckedValues([]);
    setSelectDeviceList([]);
    setSameAiAxispointList(null);
    setSameDiAxispointList(null);
    setDiffAxislist([]);
    clearState();
    clearExportState();
  };
  return (
    <Card
      style={fullScreen ? fullScreenStyle : {}}
      bordered={false}
      title={
        <Space>
          {title}
          <Tooltip placement="top" title={`参与对比的测点，不能超过${maxSelectCount}个`}>
            <QuestionCircleOutlined />
          </Tooltip>
        </Space>
      }
      extra={
        fullScreen ? (
          <FullscreenExitOutlined onClick={() => setFullScreen(false)} />
        ) : (
          <FullscreenOutlined onClick={() => setFullScreen(true)} />
        )
      }
      className={styles.container}
    >
      <Row>
        <Col span={6} className={styles.contentLeft}>
          <Card
            title={<Typography.Text>已选测点</Typography.Text>}
            extra={
              <Typography.Text type="secondary">{`${checkedValues.length}/${maxLength}`}</Typography.Text>
            }
            bodyStyle={{ height: 'calc(100vh - 220px)', overflowY: 'auto' }}
          >
            <Checkbox.Group style={{ width: '100%' }} value={checkedValues} onChange={onCheckBox}>
              <Collapse
                bordered={false}
                activeKey={collapseactiveKey}
                className={styles.collapse}
                onChange={(values: string[] | string) => setCollapseactiveKey(values)}
              >
                {selectDeviceList.map((deviceItem: DeviceType) => {
                  const { pointList = [] } = deviceItem;
                  return (
                    <Collapse.Panel
                      key={deviceItem.guid}
                      header={
                        <>
                          {deviceItem.name} <DeviceTypeText code={deviceItem.deviceType} />
                        </>
                      }
                      extra={
                        <Space>
                          <Tooltip placement="top" title="添加同类型设备测点">
                            <PlusOutlined onClick={e => onShowModel(e, deviceItem)} />
                          </Tooltip>
                          <Tooltip placement="top" title="删除设备">
                            <DeleteOutlined onClick={e => onDeleteDevice(e, deviceItem.guid)} />
                          </Tooltip>
                        </Space>
                      }
                    >
                      <div className={styles.checkbox}>
                        {pointList.map((item: PointType) => {
                          return (
                            <Row
                              key={deviceItem.guid + item.code}
                              className={classNames(
                                styles.checkboxItem,
                                checkedValues.includes(deviceItem.guid + item.code) &&
                                  styles.activeCheckboxItem
                              )}
                              justify="space-between"
                              align="middle"
                            >
                              <Checkbox
                                className={styles.checkboxItemLabel}
                                value={deviceItem.guid + item.code}
                              >
                                {item.name}
                              </Checkbox>
                              <Space className={styles.checkboxItemDelete}>
                                <Tooltip placement="top" title="删除测点">
                                  <DeleteOutlined
                                    onClick={e => onDeletePoint(e, deviceItem.guid + item.code)}
                                  />
                                </Tooltip>
                              </Space>
                            </Row>
                          );
                        })}
                      </div>
                      <Space style={{ fontSize: '12px' }}>
                        <PlusSquareOutlined
                          style={{ color: `var(--${prefixCls}-primary-color)` }}
                          onClick={e => onAddSameDevice(e, deviceItem)}
                        />
                        添加该设备下其他测点
                      </Space>
                    </Collapse.Panel>
                  );
                })}
              </Collapse>
            </Checkbox.Group>
          </Card>
          <Space
            style={{ height: '52px', display: 'flex', justifyContent: 'center' }}
            align="center"
          >
            <Button type="primary" onClick={onShowDrawer}>
              添加测点
            </Button>
            <Button onClick={onClearDate}>清空测点</Button>
          </Space>
        </Col>
        <Col span={18}>
          <Card
            bodyStyle={{
              height: 'calc(100vh - 166px)',
              overflowY: 'auto',
              padding: '24px',
              position: 'relative',
            }}
            title="数据曲线图"
            extra={
              <FileExport filename="测点对比" showExportFiltered={false} data={onFileExport} />
            }
          >
            <DurationSelect
              allowInterval
              defaultIntervalEnabled={false}
              onChange={onGetParams}
              onDidMount={onGetParams}
            />
            <Select
              style={{ width: '104px', position: 'absolute', top: '24px', right: '20px' }}
              value={axis}
              onChange={onSelecAxis}
            >
              <Select.Option value="sameAxis">同轴</Select.Option>
              <Select.Option value="diffAxis">分轴</Select.Option>
            </Select>
            {checkedValues.length ? (
              <>
                {axis === 'diffAxis' && (
                  <>
                    {diffAxislist.map((item, index) => {
                      const { idcTag, pointGuid, validLimits } = item;
                      const {
                        dataType: { code },
                      } = pointGuid;
                      if (code === 'DI') {
                        return (
                          <PointsStateLine
                            key={index}
                            durationSelectStyle={{ display: 'none' }}
                            idcTag={idcTag}
                            startTime={startTime}
                            endTime={endTime}
                            interval={interval}
                            pointGuids={[pointGuid]}
                            chartFunction="MAX"
                            validLimitsMap={validLimits || {}}
                          />
                        );
                      } else {
                        return (
                          <PointsLine
                            key={index}
                            durationSelectStyle={{ display: 'none' }}
                            idcTag={idcTag}
                            startTime={startTime}
                            endTime={endTime}
                            interval={interval}
                            pointGuids={[pointGuid]}
                            chartFunction="MAX"
                          />
                        );
                      }
                    })}
                  </>
                )}

                {axis === 'sameAxis' && sameAiAxispointList && (
                  <PointsLine
                    durationSelectStyle={{ display: 'none' }}
                    startTime={startTime}
                    endTime={endTime}
                    interval={interval}
                    idcTag={sameAiAxispointList.idcTag}
                    pointGuids={sameAiAxispointList.pointGuids}
                    chartFunction="MAX"
                    notMerge
                  />
                )}
                {axis === 'sameAxis' && sameDiAxispointList && (
                  <PointsStateLine
                    durationSelectStyle={{ display: 'none' }}
                    startTime={startTime}
                    endTime={endTime}
                    interval={interval}
                    idcTag={sameDiAxispointList.idcTag}
                    pointGuids={sameDiAxispointList.pointGuids}
                    chartFunction="MAX"
                    validLimitsMap={sameDiAxispointList.validLimits || {}}
                    notMerge
                  />
                )}
              </>
            ) : (
              <Space direction="vertical" style={{ width: '100%', textAlign: 'center' }}>
                <Empty />
                <Button type="primary" onClick={onShowDrawer}>
                  添加测点
                </Button>
              </Space>
            )}
          </Card>
        </Col>
      </Row>
      <PointModal
        visible={visible}
        alreadySelectLength={maxLength}
        sameTypeItem={sameTypeItem}
        sameDeviceItem={sameDeviceItem}
        selectDeviceList={selectDeviceList}
        setSelectDeviceList={setSelectDeviceList}
        setCollapseactiveKey={setCollapseactiveKey}
        oldCheckedValues={checkedValues}
        getAxisLinelist={getAxisLinelist}
        onClose={onClose}
      />
      <PointDiffDrawer
        open={pointDrawerOpen}
        alreadySelectLength={maxLength}
        selectDeviceList={selectDeviceList}
        setSelectDeviceList={setSelectDeviceList}
        setCollapseactiveKey={setCollapseactiveKey}
        oldCheckedValues={checkedValues}
        getAxisLinelist={getAxisLinelist}
        onClose={() => setPointDrawerOpen(false)}
      />
    </Card>
  );
}
