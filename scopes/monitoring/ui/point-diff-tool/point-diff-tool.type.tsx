import { ReactNode } from 'react';

import type { ValueMapping } from '@manyun/monitoring.model.point';

export type PointDiffToolProps = {
  title?: ReactNode;
};

export type PointObject = {
  code: string;
  name: string;
};
export interface PointType {
  key?: string;
  code: string;
  name: string;
  extCount: number | null;
  extName: string | null;
  validLimits: string[];
  unit: string | null;
  dataType: PointObject;
  pointType: PointObject;
  precision: number | null;
  deviceType: string;
  description: string | null;
  isInitialized: boolean;
  isSub: boolean;
  priority: number | null;
  spaceGuid: string | null;
  formulaJson: string | null;
  formula: string | null;
  alarmLevel: number | null;
  alarmType: PointObject | null;
  dimension: PointObject;
  filterString?: string;
  deviceGuid?: string;
}

export type pointGuid = {
  deviceGuid: string;
  pointCode: string;
  serieName: string;
  validLimits?: string[];
  valueMapping?: ValueMapping;
  unit?: string | null;
};

export type sameAxisPoint = {
  idcTag: string;
  pointGuids: pointGuid[];
  validLimits?: string[];
};

export type diffAxisPoint = {
  idcTag: string;
  validLimits: string[];
  pointGuid: {
    deviceGuid: string;
    pointCode: string;
    serieName: string;
    dataType: {
      code: string;
      name: string;
    };
    unit?: string | null;
  };
};
