import React, { Ref } from 'react';

import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import type { CheckboxGroupProps } from '@manyun/base-ui.ui.checkbox';

import { getDynamicBaselineLocales } from '@manyun/monitoring.model.dynamic-baseline-setting';

import { opsSceneModelOptions } from './dynamic-baseline-condition-common';

export type OpsSceneConditionCheckboxProps = Omit<CheckboxGroupProps, 'options' | 'ref'>;

/**
 * 动态配置模型多选
 */
export const DynamicBaselineConditionCheckbox = React.forwardRef(
  (props: OpsSceneConditionCheckboxProps, ref: Ref<HTMLDivElement> | undefined) => {
    const locales = getDynamicBaselineLocales();
    return <Checkbox.Group {...props} ref={ref} options={opsSceneModelOptions(locales)} />;
  }
);

DynamicBaselineConditionCheckbox.displayName = 'DynamicBaselineConditionCheckbox';
