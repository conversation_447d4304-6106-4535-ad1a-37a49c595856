import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

import { getDynamicBaselineLocales } from '@manyun/monitoring.model.dynamic-baseline-setting';

import { opsSceneModelOptions } from './dynamic-baseline-condition-common';

export type DynamicBaselineConditionSelectProps = Omit<SelectProps, 'options' | 'ref'>;

/**
 * 动态配置模型下拉框选择
 */
export const DynamicBaselineConditionSelect = React.forwardRef(
  (props: DynamicBaselineConditionSelectProps, ref: React.Ref<RefSelectProps>) => {
    const locales = getDynamicBaselineLocales();
    return (
      <Select
        optionFilterProp="label"
        {...props}
        ref={ref}
        options={opsSceneModelOptions(locales)}
      />
    );
  }
);

DynamicBaselineConditionSelect.displayName = 'DynamicBaselineConditionSelect';
