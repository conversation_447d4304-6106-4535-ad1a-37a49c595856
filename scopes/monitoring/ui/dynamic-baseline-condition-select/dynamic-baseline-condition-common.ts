import type {
  DynamicBaselineCondition,
  DynamicBaselineLocales,
} from '@manyun/monitoring.model.dynamic-baseline-setting';

export const opsSceneModelOptions = (
  locales: DynamicBaselineLocales
): {
  label: string;
  value: DynamicBaselineCondition;
}[] => {
  return [
    {
      label: locales['upper'],
      value: 'UPPER',
    },
    {
      label: locales['lower'],
      value: 'LOWER',
    },
  ];
};
