/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-3-4
 *
 * @packageDocumentation
 */
import React, { useMemo } from 'react';

import { Divider } from '@manyun/base-ui.ui.divider';
import { Space } from '@manyun/base-ui.ui.space';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';

import type { AlarmJSON } from '@manyun/monitoring.model.alarm';

export type AlarmLabelProps = { alarm: AlarmJSON };

export function AlarmLabel({ alarm }: AlarmLabelProps) {
  const alarmlableList = useMemo(() => {
    const list = [];
    if (alarm.mergeStatus === 'ROOT') {
      list.push(<Typography.Link key="ROOT">主告警</Typography.Link>);
    }
    if (alarm.mergeStatus === 'CHILD') {
      list.push(<Typography.Link key="CHILD">子告警</Typography.Link>);
    }
    if (alarm.expectedStatus === 'EXPECTED' && alarm.shieldBizType === 'COMMON') {
      list.push(<Typography.Link key="SHIELD">屏蔽</Typography.Link>);
    }
    if (alarm.expectedStatus === 'EXPECTED' && alarm.shieldBizType === 'CHANGE') {
      list.push(<Typography.Link key="CHANGE">变更预期内</Typography.Link>);
    }
    if (alarm.expectedStatus === 'NORMAL' && alarm.shieldBizType === 'CHANGE') {
      list.push(<Typography.Link key="changeExpected">变更预期外</Typography.Link>);
    }
    if (alarm.expectedStatus === 'EXPECTED' && alarm.shieldBizType === 'POWER') {
      list.push(<Typography.Link key="expectedPower">上下电计划内</Typography.Link>);
    }
    if (alarm.expectedStatus === 'NORMAL' && alarm.shieldBizType === 'POWER') {
      list.push(<Typography.Link key="nunexpectedPower">上下电预期外</Typography.Link>);
    }
    if (alarm.totalCount > 0) {
      list.push(<Typography.Link key="merge">频发</Typography.Link>);
    }
    if (alarm.eventId) {
      list.push(<Typography.Link key="event">事件</Typography.Link>);
    }

    return list;
  }, [
    alarm.eventId,
    alarm.expectedStatus,
    alarm.mergeStatus,
    alarm.shieldBizType,
    alarm.totalCount,
  ]);

  return (
    <>
      {!!alarmlableList.length && (
        <Tag style={{ border: 'none', padding: '2px 6px', borderRadius: '6px' }} color="processing">
          <Space split={<Divider type="vertical" />} size={0}>
            {alarmlableList}
          </Space>
        </Tag>
      )}
    </>
  );
}
