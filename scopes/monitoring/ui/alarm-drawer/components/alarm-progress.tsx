import React, { useEffect, useState } from 'react';

import { AlertOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';

import { Card } from '@manyun/base-ui.ui.card';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Space } from '@manyun/base-ui.ui.space';
import { Steps } from '@manyun/base-ui.ui.steps';
import { Typography } from '@manyun/base-ui.ui.typography';

import type { AlarmJSON } from '@manyun/monitoring.model.alarm';
import { AlarmLifecyStatusText } from '@manyun/monitoring.ui.alarm-lifecy-status-text';
import { AlarmStatusText } from '@manyun/monitoring.ui.alarm-status-text';

import styles from '../alarm-drawer.module.less';
import { getformatInterval } from '../utils';

const { Step } = Steps;

export type AlarmProgressProps = {
  alarm: AlarmJSON;
  style?: React.CSSProperties;
};

export function AlarmProgress({ alarm, style }: AlarmProgressProps) {
  const [unAcceptedTime, setUnAcceptedTime] = useState<number>(0);
  const [waitRemoveTime, setWaitRemoveTime] = useState<number>(0);

  useEffect(() => {
    const { createdAt, confirmedAt } = alarm;
    let unAcceptedTime = dayjs().diff(createdAt, 'seconds');
    let waitRemoveTime = dayjs().diff(confirmedAt, 'seconds');

    const timer = window.setInterval(function () {
      unAcceptedTime = unAcceptedTime + 1;
      waitRemoveTime = waitRemoveTime + 1;
      setUnAcceptedTime(unAcceptedTime);
      setWaitRemoveTime(waitRemoveTime);
    }, 1000);

    return () => {
      clearInterval(timer);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const {
    createdAt = 0,
    latestTriggeredAt,
    recoveredAt,
    confirmedAt,
    removedAt,
    state: { code: stateCode },
    lifecycleState: { code: lifecycleStateCode },
  } = alarm;

  const getAlarmTime = () => {
    let alarmSeconds: number;
    if (stateCode === 'RECOVER') {
      alarmSeconds = dayjs(recoveredAt).diff(createdAt, 'seconds');
    } else {
      alarmSeconds = unAcceptedTime;
    }
    return getformatInterval(alarmSeconds);
  };

  const getAlarmProgressList = () => {
    if (stateCode === 'RECOVER') {
      return (
        <Steps direction="vertical" current={3} size="small">
          <Step title="告警开始" description={dayjs(createdAt).format('YYYY-MM-DD HH:mm:ss')} />
          <Step
            title="最新告警时间"
            description={dayjs(latestTriggeredAt).format('YYYY-MM-DD HH:mm:ss')}
          />
          <Step title="告警已恢复" description={dayjs(recoveredAt).format('YYYY-MM-DD HH:mm:ss')} />
        </Steps>
      );
    } else {
      return (
        <Steps direction="vertical" current={1} size="small">
          <Step title="告警开始" description={dayjs(createdAt).format('YYYY-MM-DD HH:mm:ss')} />
          <Step
            title="最新告警时间"
            description={dayjs(latestTriggeredAt).format('YYYY-MM-DD HH:mm:ss')}
          />
          <Step title="告警已恢复" />
        </Steps>
      );
    }
  };

  //受理进度
  const getExecutionProgress = () => {
    const confirmUser = alarm.confirmUser ? alarm.confirmUser.name : '系统';
    if (lifecycleStateCode === 'ACTIVE') {
      return (
        <Steps direction="vertical" current={0} percent={60} size="small">
          <Step title="等待受理" description={getformatInterval(unAcceptedTime)} />
          <Step title="下盯屏" />
        </Steps>
      );
    }
    if (lifecycleStateCode === 'REMOVED') {
      return (
        <Steps direction="vertical" size="small" current={2}>
          <Step
            title="受理告警"
            description={
              <Space size={5} direction="vertical">
                <Space size={5}>
                  <Typography.Text type="secondary">
                    {getformatInterval(dayjs(confirmedAt).diff(createdAt, 'seconds'))}
                  </Typography.Text>
                  <Typography.Text type="secondary">{confirmUser}</Typography.Text>
                </Space>

                {dayjs(confirmedAt).format('YYYY-MM-DD HH:mm:ss')}
              </Space>
            }
          />
          <Step
            title="下盯屏"
            description={
              <Space size={5} direction="vertical">
                <Space size={5} style={{ flexWrap: 'wrap' }}>
                  <Typography.Text type="secondary">
                    {getformatInterval(dayjs(removedAt).diff(createdAt, 'seconds'))}
                  </Typography.Text>
                  <Typography.Text type="secondary">{alarm.removeUser}</Typography.Text>
                </Space>
                {dayjs(removedAt).format('YYYY-MM-DD HH:mm:ss')}
              </Space>
            }
          />
        </Steps>
      );
    } else {
      return (
        <Steps direction="vertical" current={1} percent={60} size="small">
          <Step
            title="受理告警"
            description={
              <Space size={5} direction="vertical">
                <Space size={5}>
                  <Typography.Text type="secondary">
                    {getformatInterval(dayjs(confirmedAt).diff(createdAt, 'seconds'))}
                  </Typography.Text>
                  <Typography.Text type="secondary">{confirmUser}</Typography.Text>
                </Space>
                {dayjs(confirmedAt).format('YYYY-MM-DD HH:mm:ss')}
              </Space>
            }
          />
          <Step title="等待下盯屏" description={getformatInterval(waitRemoveTime)} />
        </Steps>
      );
    }
  };

  return (
    <Card style={style}>
      <Space size={40} direction="vertical" style={{ width: '100%' }}>
        <Space>
          <AlertOutlined
            style={{ fontSize: 26 }}
            className={stateCode === 'RECOVER' ? styles.secondaryDark : styles.errorColor}
          />
          <div>
            <Typography.Text>{getAlarmTime()}</Typography.Text>
            <Space size={0} split={<Divider type="vertical" />}>
              <AlarmStatusText code={alarm.state.code} />
              <AlarmLifecyStatusText code={alarm.lifecycleState.code} />
            </Space>
          </div>
        </Space>
        <Typography.Text type="secondary">告警进度</Typography.Text>
        {getAlarmProgressList()}
        <Typography.Text type="secondary">受理进度</Typography.Text>
        {getExecutionProgress()}
      </Space>
    </Card>
  );
}
