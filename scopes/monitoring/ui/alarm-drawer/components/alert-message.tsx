import { BulbOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import { Divider } from '@manyun/base-ui.ui.divider';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { AlarmJSON } from '@manyun/monitoring.model.alarm';
import { getAlarmLocales } from '@manyun/monitoring.model.alarm';
import { generateSpecificMonitorItemConfigLocation } from '@manyun/monitoring.route.monitoring-routes';
import { fetchAlarms } from '@manyun/monitoring.service.fetch-alarms';
import { AlarmReasonText } from '@manyun/monitoring.ui.alarm-reason-text';
import {
  generateChangeTicketDetail,
  generateEventDetailRoutePath,
} from '@manyun/ticket.route.ticket-routes';

const alarmLocales = getAlarmLocales();

export type AlertMessageProps = {
  alarm: AlarmJSON;
  idc: string;
  mergeCount: number;
  description?: string;
};

export function AlertMessage({ alarm, idc, mergeCount, description }: AlertMessageProps) {
  //测点24小时出现告警数
  const [alarmPointCount, setAlarmPointCount] = useState<number>(0);
  //该设备存在告警数
  const [alarmDeviceCount, setAlarmDeviceCount] = useState<number>(0);

  useEffect(() => {
    fetchAlarms({
      idcTag: idc,
      deviceGuid: alarm.source.guid,
      pointCode: alarm.point.code,
      alarmCreateTimeStart: dayjs().subtract(1, 'day').valueOf(),
      alarmCreateTimeEnd: dayjs().valueOf(),
      triggerStatus: ['TRIGGER', 'RECOVER'],
      status: 'REMOVED',
      pageNum: 1,
      pageSize: 1,
    }).then(({ error, data }) => {
      if (!error) {
        setAlarmPointCount(data.total);
      } else {
        message.error(error.message);
      }
    });
    fetchAlarms({
      idcTag: idc,
      deviceGuid: alarm.source.guid,
      triggerStatus: ['TRIGGER'],
      pageNum: 1,
      pageSize: 1,
    }).then(({ error, data }) => {
      if (!error) {
        setAlarmDeviceCount(data.total);
      } else {
        message.error(error.message);
      }
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const linkToAlarmScreenDetail = ({
    id,
    idc,
    text,
  }: {
    id: number;
    idc: string;
    text: string;
  }) => (
    <Link to="/" target="_blank">
      {text}
    </Link>
  );

  const {
    state: { code: stateCode },
    lifecycleState: { code: lifecycleStateCode },
  } = alarm;

  const getMessage = () => {
    return (
      <>
        <Space size={6} style={{ paddingRight: '12px' }}>
          <BulbOutlined style={{ color: '#FAAD14' }} />
          <Typography.Text type="secondary">
            {lifecycleStateCode === 'REMOVED' ? '处理反馈' : '智能提示'}
          </Typography.Text>
        </Space>
        <Space split={<Divider type="vertical" />} style={{ paddingRight: 8 }}>
          {alarm.eventId && (
            <Link to={generateEventDetailRoutePath({ id: alarm.eventId })} target="_blank">
              关联事件单:{alarm.eventId}
            </Link>
          )}
          {/* 不是空间设备且变更单存在 */}
          {alarm.source.variant === 'device' && alarm.changeId && (
            <Link to={generateChangeTicketDetail({ id: alarm.changeId })} target="_blank">
              关联变更单:{alarm.changeId}({alarmLocales.expectedStatus[alarm.expectedStatus]})
            </Link>
          )}
          {lifecycleStateCode === 'REMOVED' ? (
            <Typography.Text>
              {`${alarm.removedDescriptions ?? ''} `}
              <AlarmReasonText code={alarm.reason!} />
            </Typography.Text>
          ) : (
            <>
              <Button compact disabled={alarmPointCount < 2} type="link" target="_blank">
                该测点近24小时出现{alarmPointCount}次告警
              </Button>
              <Button compact disabled={alarmDeviceCount < 2} type="link" target="_blank">
                该{alarm.source.variant === 'device' ? '设备' : '对象'}存在
                {alarmDeviceCount}条告警
              </Button>
            </>
          )}
          {mergeCount > 0 &&
            linkToAlarmScreenDetail({
              id: alarm.id,
              idc: alarm.source.spaceGuidMap.idcTag,
              text: `已合并${mergeCount}条告警`,
            })}
          {alarm.mergeCount > 0 &&
            linkToAlarmScreenDetail({
              id: alarm.id,
              idc: alarm.source.spaceGuidMap.idcTag,
              text: `已收敛${alarm.mergeCount}条告警`,
            })}
          {description && (
            <Link
              to={generateSpecificMonitorItemConfigLocation({
                configId: String(alarm.monitoringItem.id),
                deviceType: alarm.source.deviceType,
                pointCode: alarm.point.code,
              })}
              target="_blank"
            >
              处置建议：
              {description.length > 20 ? description.slice(0, 20) + '...' : description}
            </Link>
          )}
        </Space>
      </>
    );
  };

  return <Alert message={getMessage()} type="info" />;
}
