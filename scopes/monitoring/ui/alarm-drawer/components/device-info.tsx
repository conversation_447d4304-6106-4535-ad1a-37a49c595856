import type { ReactNode } from 'react';
import React, { useMemo } from 'react';
import { <PERSON> } from 'react-router-dom';

import { Badge } from '@manyun/base-ui.ui.badge';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import type { AlarmJSON } from '@manyun/monitoring.model.alarm';
import { TriggerRule, TriggerRuleType } from '@manyun/monitoring.model.trigger-rule';
import {
  generateSpaceOrDeviceRoutePath,
  generateSpecificMonitorItemConfigLocation,
} from '@manyun/monitoring.route.monitoring-routes';
import type { AlarmInfo } from '@manyun/monitoring.service.fetch-alarm-info';
import { TriggerRulesText } from '@manyun/monitoring.ui.trigger-rules-text';
import { ALARM_NOTIFICATIONS_ROUTE_PATH } from '@manyun/notification-hub.route.notification-routes';
import { DeviceTypeText } from '@manyun/resource-hub.ui.device-type-text';
import { RoomTypeText } from '@manyun/resource-hub.ui.room-type-text';
import { getSpaceGuid } from '@manyun/resource-hub.util.space-guid';

type DeviceInfoProps = {
  mergeCount: number;
  alarm: AlarmJSON;
  alarmInfo?: AlarmInfo;
  idc: string;
};

type Description = {
  key: string;
  label: string | null;
  text: ReactNode;
  span?: number;
};

export function DeviceInfo({ mergeCount, alarm, alarmInfo, idc }: DeviceInfoProps) {
  const spaceGuid = getSpaceGuid(
    idc,
    alarm.source.spaceGuidMap.blockTag,
    alarm.source.spaceGuidMap.roomTag
  );

  const getGridColumnTag = ({
    gridTag,
    columnTag,
  }: {
    gridTag: string | null;
    columnTag: string | null;
  }) => {
    if (gridTag && columnTag) {
      return `${columnTag}列.${gridTag}机柜`;
    }
    if (!gridTag && columnTag) {
      return `${columnTag}列`;
    }
    if (gridTag && !columnTag) {
      return `${gridTag}机柜`;
    }
    return '--';
  };

  const linkToAlarmScreenDetail = ({
    id,
    idc,
    count,
  }: {
    id: number;
    idc: string;
    count: number;
  }) => {
    if (count > 0) {
      return (
        <Link to="/" target="_blank">
          {`${count}个`}
        </Link>
      );
    }
    return '0个';
  };

  const deviceInfo: Description[] = useMemo(() => {
    const deviceInfoList = [
      //楼栋-区域监控 设备-设备履历
      {
        key: 'alarmDevice',
        label: '告警设备',
        text:
          alarm.source.dimension === 'COLUMN' || alarm.source.dimension === 'GRID' ? (
            <>
              {alarm.source.name} <DeviceTypeText code={alarm.source.deviceType} />
            </>
          ) : (
            <Link
              to={generateSpaceOrDeviceRoutePath({
                guid: alarm.source.guid,
                type: alarm.source.dimension,
              })}
              target="_blank"
            >
              {alarm.source.name} <DeviceTypeText code={alarm.source.deviceType} />
            </Link>
          ),
      },
      { key: 'deviceLabel', label: '设备标签', text: alarm.source.deviceLabel, span: 3 },
      {
        key: 'place',
        label: '位置',
        text: (
          <Link
            to={generateSpaceOrDeviceRoutePath({
              guid: spaceGuid!,
              type: alarm.source.spaceGuidMap.roomTag
                ? 'ROOM'
                : alarm.source.spaceGuidMap.blockTag
                  ? 'BLOCK'
                  : 'IDC',
              spotlightTarget: alarm.source.dimension === 'DEVICE' ? alarm.source.guid : '',
            })}
            target="_blank"
            onClick={e => e.stopPropagation()}
          >
            {alarm.source.spaceGuidMap.blockTag
              ? getSpaceGuid(alarm.source.spaceGuidMap.blockTag, alarm.source.spaceGuidMap.roomTag)
              : idc}
            {alarm.source.roomType && <RoomTypeText code={alarm.source.roomType} />}
          </Link>
        ),
      },
      {
        key: 'gridAndColumn',
        label: '机列机柜',
        text: getGridColumnTag({
          gridTag: alarm.source.spaceGuidMap.gridTag,
          columnTag: alarm.source.spaceGuidMap.columnTag,
        }),
      },
      {
        key: 'mergeView',
        label: '收敛告警',
        text: linkToAlarmScreenDetail({
          id: alarm.id,
          idc: alarm.source.spaceGuidMap.idcTag,
          count: alarm.mergeCount,
        }),
      },
      {
        key: 'mergeCount',
        label: '合并告警',
        text: linkToAlarmScreenDetail({
          id: alarm.id,
          idc: alarm.source.spaceGuidMap.idcTag,
          count: mergeCount,
        }),
      },
      {
        key: 'notifyCount',
        label: '告警通报',
        text: (
          <Link
            to={{ pathname: ALARM_NOTIFICATIONS_ROUTE_PATH, search: `?alarmId=${alarm.id}` }}
            target="_blank"
          >
            {alarm.notifyCount}
          </Link>
        ),
      },
    ];

    return deviceInfoList;
  }, [
    alarm.source.dimension,
    alarm.source.name,
    alarm.source.deviceType,
    alarm.source.guid,
    alarm.source.deviceLabel,
    alarm.source.spaceGuidMap.roomTag,
    alarm.source.spaceGuidMap.blockTag,
    alarm.source.spaceGuidMap.gridTag,
    alarm.source.spaceGuidMap.columnTag,
    alarm.source.spaceGuidMap.idcTag,
    alarm.source.roomType,
    alarm.mergeCount,
    alarm.id,
    alarm.notifyCount,
    spaceGuid,
    idc,
    mergeCount,
  ]);

  const alarmImpact: Description[] = useMemo(() => {
    return [
      {
        key: 'grid',
        label: '影响机柜',
        text:
          alarm.gridCount === null ? (
            '--'
          ) : (
            <Link to="\" target="_blank">
              {alarm.gridCount}
            </Link>
          ),
      },
      {
        key: 'device',
        label: '影响设备',
        text:
          alarm.customerCount === null ? (
            '--'
          ) : (
            <Link to="\" target="_blank">
              {alarm.deviceCount}
            </Link>
          ),
      },
      {
        key: 'customer',
        label: '客户影响',
        text:
          alarm.customerCount === null ? (
            '--'
          ) : (
            <Link to="\" target="_blank">
              {alarm.customerCount}
            </Link>
          ),
      },
    ];
  }, [
    alarm.customerCount,
    alarm.deviceCount,
    alarm.gridCount,
    alarm.id,
    alarm.source.spaceGuidMap.blockTag,
    alarm.source.spaceGuidMap.idcTag,
  ]);

  const alarmRules: Description[] = useMemo(() => {
    if (!alarmInfo) {
      return [];
    }

    return [
      {
        key: 'threshold',
        label: '告警条件',
        text: (
          <Link
            to={generateSpecificMonitorItemConfigLocation({
              configId: String(alarm.monitoringItem.id),
              deviceType: alarm.source.deviceType,
              pointCode: alarm.point.code,
            })}
            target="_blank"
          >
            {alarm.monitoringItem.threshold}
          </Link>
        ),
      },
      { key: 'triggerInterval', label: '触发周期', text: `${alarmInfo.triggerInterval}秒` },
      { key: 'recoverInterval', label: '恢复周期', text: `${alarmInfo.recoverInterval}秒` },
      {
        key: 'preCondition',
        label: '前置条件',
        text: (
          <TriggerRulesText
            type={TriggerRuleType.PreCondition}
            triggerRules={(alarmInfo.monitorItem?.triggerRules ?? [])
              .map(rule => TriggerRule.fromApiObject(rule))
              .map(rule => rule.toJSON())}
          />
        ),
      },
    ];
  }, [
    alarm.monitoringItem.id,
    alarm.monitoringItem.threshold,
    alarm.point.code,
    alarm.source.deviceType,
    alarmInfo,
  ]);

  return (
    <div style={{ width: '100%', height: '100%', overflowY: 'auto' }}>
      <Descriptions size="middle" bordered>
        <Descriptions.Item span={6} labelStyle={{ display: 'none' }}>
          <Badge status="processing" text="告警对象" />
        </Descriptions.Item>
        {deviceInfo.map(item => {
          const { key, label, text } = item;
          return (
            <Descriptions.Item key={key} label={label} span={3} labelStyle={{ width: 110 }}>
              {text}
            </Descriptions.Item>
          );
        })}
        <Descriptions.Item span={6} labelStyle={{ display: 'none' }}>
          <Badge status="processing" text="告警影响" />
        </Descriptions.Item>
        {alarmImpact.map(item => {
          const { key, label, text } = item;
          return (
            <Descriptions.Item key={key} label={label} span={3} labelStyle={{ width: 110 }}>
              {text}
            </Descriptions.Item>
          );
        })}
        {alarmInfo && (
          <Descriptions.Item span={6} labelStyle={{ display: 'none' }}>
            <Badge status="processing" text="告警规则" />
          </Descriptions.Item>
        )}

        {alarmRules.map(item => {
          const { key, label, text } = item;
          return (
            <Descriptions.Item key={key} label={label} span={3} labelStyle={{ width: 110 }}>
              {text}
            </Descriptions.Item>
          );
        })}
      </Descriptions>
    </div>
  );
}
