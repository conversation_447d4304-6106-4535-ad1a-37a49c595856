import React, { useEffect, useState } from 'react';

import { Badge } from '@manyun/base-ui.ui.badge';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { message } from '@manyun/base-ui.ui.message';

import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import type { AlarmJSON } from '@manyun/monitoring.model.alarm';
import { CameraList, PointModal } from '@manyun/monitoring.page.alarm-staring-screen';
import type { AlarmInfo } from '@manyun/monitoring.service.fetch-alarm-info';
import { fetchAlarmInfo } from '@manyun/monitoring.service.fetch-alarm-info';
import { fetchAlarmMerge } from '@manyun/monitoring.service.fetch-alarm-merge';
import { CorePointsCard } from '@manyun/resource-hub.ui.core-points-card';

import { AlarmProgress } from './components/alarm-progress';
import { AlertMessage } from './components/alert-message';
import { DeviceInfo } from './components/device-info';

export type AlarmDrawerProps = {
  visible: boolean;
  onClose: () => void;
  idc: string;
  alarm: AlarmJSON;
  showActions?: boolean;
  /*抽屉右上角区域*/
  extra?: React.ReactNode;
};

export function AlarmDrawer({ visible, onClose, idc, alarm, extra }: AlarmDrawerProps) {
  const spaceGuidMap = alarm.source.spaceGuidMap;
  const showCameraList = spaceGuidMap.idcTag && spaceGuidMap.blockTag && spaceGuidMap.roomTag;

  const [mergeCount, setMergeCount] = useState(0);
  const [alarmInfo, setAlarmInfo] = useState<AlarmInfo>();

  useEffect(() => {
    if (!alarm.id) {
      return;
    }
    fetchAlarmMerge({ idcTag: idc, id: alarm.id }).then(({ error, data }) => {
      if (error) {
        message.error(error.message);
        return;
      }
      setMergeCount(data.total - 1);
    });
  }, [alarm.id, idc]);

  useEffect(() => {
    fetchAlarmInfo({
      deviceType: alarm.source.deviceType,
      id: alarm.monitoringItem.id,
      pointCode: alarm.point.code,
    }).then(({ error, data }) => {
      if (error) {
        message.error(error.message);
        return;
      }
      setAlarmInfo(data);
    });
  }, [alarm.monitoringItem.id, alarm.point.code, alarm.source.deviceType]);

  return (
    <Drawer
      title={
        <Badge status="error" text={<span style={{ fontSize: '16px' }}>{alarm.message}</span>} />
      }
      extra={extra}
      destroyOnClose
      placement="right"
      open={visible}
      contentWrapperStyle={{ width: '100%', maxWidth: 1820 }}
      onClose={onClose}
    >
      <div style={{ display: 'flex', gap: 8, height: '100%' }}>
        <AlarmProgress style={{ width: 245, height: '100%', overflowY: 'auto' }} alarm={alarm} />
        <div
          style={{
            flex: 1,
            display: 'flex',
            gap: 16,
            flexDirection: 'column',
            height: '100%',
          }}
        >
          <AlertMessage
            alarm={alarm}
            idc={idc}
            mergeCount={mergeCount}
            description={alarmInfo?.description}
          />
          <div style={{ display: 'flex', gap: 8, height: 'calc(100% - 56px)' }}>
            <Row gutter={8} style={{ flex: 1, height: '100%' }}>
              <Col span={17} style={{ height: '100%' }}>
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 16,
                    height: '100%',
                    overflowY: 'hidden',
                  }}
                >
                  <PointModal idc={idc} alarm={alarm} />
                  {alarm.source.variant === 'device' && (
                    <CorePointsCard
                      style={{
                        width: '100%',
                        flex: 1,
                      }}
                      deviceGuid={alarm.source.guid}
                      deviceType={alarm.source.deviceType}
                      showPointsOnly
                      hideNoDataItems
                    />
                  )}
                </div>
              </Col>
              <Col span={7} style={{ display: 'flex', height: '100%' }}>
                <DeviceInfo alarm={alarm} idc={idc} mergeCount={mergeCount} alarmInfo={alarmInfo} />
              </Col>
            </Row>
            {showCameraList && env.FFS_CAMERAS_SERVICE === 'enabled' && (
              <div
                style={{
                  width: 225,
                  height: '100%',
                  overflowY: 'auto',
                }}
              >
                <CameraList alarm={alarm} />
              </div>
            )}
          </div>
        </div>
      </div>
    </Drawer>
  );
}
