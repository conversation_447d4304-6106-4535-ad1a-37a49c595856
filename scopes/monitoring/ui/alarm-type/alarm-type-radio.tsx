import React, { Ref } from 'react';

import { Radio } from '@manyun/base-ui.ui.radio';
import type { RadioProps } from '@manyun/base-ui.ui.radio';

import { alarmTypeOptions } from './alarm-type-common';

export type AlarmTypeRadioProps = Omit<RadioProps, 'options'>;

/**
 * 告警类型单选radio
 */
export const AlarmTypeRadio = React.forwardRef(
  (props: AlarmTypeRadioProps, ref: Ref<HTMLDivElement> | undefined) => {
    return <Radio.Group ref={ref} {...props} options={alarmTypeOptions} />;
  }
);

AlarmTypeRadio.displayName = 'AlarmTypeRadio';
