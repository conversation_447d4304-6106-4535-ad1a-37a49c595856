import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

import type { BackendAlarmType } from '@manyun/monitoring.model.alarm';

import { alarmTypeMapper, alarmTypeOptions } from './alarm-type-common';

export type AlarmTypeSelectProps = Omit<SelectProps, 'options'>;

export const AlarmTypeSelect = React.forwardRef(
  ({ ...props }: AlarmTypeSelectProps, ref: React.Ref<RefSelectProps>) => {
    return <Select ref={ref} options={alarmTypeOptions} {...props} />;
  }
);

AlarmTypeSelect.displayName = 'AlarmTypeSelect';

export type AlarmTypeTextProps = {
  value: BackendAlarmType;
};

export const AlarmTypeText = ({ value }: AlarmTypeTextProps) => {
  return <>{alarmTypeMapper[value] ?? ''}</>;
};
