import React from 'react';

import dayjs from 'dayjs';
import get from 'lodash.get';

import { FolderAddOutlined } from '@manyun/base-ui.icons';
import { message } from '@manyun/base-ui.ui.message';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';

import { useLayout } from '@manyun/dc-brain.context.layout';
import { getState, saveState } from '@manyun/monitoring.ui.device-diff-tool';
import type { DeviceType } from '@manyun/monitoring.ui.device-diff-tool';
import { fetchDeviceByGuid } from '@manyun/resource-hub.service.fetch-device-by-guid';
// import { fetchDeviceTypeTree } from '@manyun/resource-hub.service.fetch-device-type-tree';
import { fetchPointsByCondition } from '@manyun/resource-hub.service.fetch-points-by-condition';

import { treeDataService } from '@manyun/dc-brain.legacy.services';
import { specService } from '@manyun/dc-brain.legacy.services';

export type AddDeviceToDiffToolProps = {
  deviceGuid: string;
};
//import { Device } from '@manyun/resource-hub.model.device';

const DATE_FORMAT = 'YYYY-MM-DD';
export function AddDeviceToDiffTool({ deviceGuid }: AddDeviceToDiffToolProps) {
  const { setSideBarVisible, setSideBarTab } = useLayout();

  const onAddDeviceToDiffTool = async () => {
    //目前对比逻辑设计不合理后续需要修改

    const stateList: DeviceType[] = getState();
    const deviceGuidList = stateList.map(item => item.guid);

    if (stateList.length > 19) {
      message.error('添加失败，至多支持20个设备对比！');
      return;
    }
    if (deviceGuidList.includes(deviceGuid)) {
      message.error('添加失败，该设备已存于设备对比！');
      return;
    }

    const { data, error } = await fetchDeviceByGuid({ guid: deviceGuid });
    if (error) {
      message.error(error.message);
      return;
    }
    const device: DeviceType = data.toApiObject();

    const { normalizedList } = await treeDataService.fetchDeviceCategory({ numbered: null });

    const {
      topCategory,
      secondCategory,
      deviceType,
      productModel,
      spaceGuid: { idcTag, blockTag, roomTag },
      purchaseTime,
      checkTime,
      warrantyTime,
      enableTime,
      scrapTime,
      purchasePrice,
      netWorth,
      warrantyPrice,
    } = device;

    if (stateList.length && stateList[0].deviceType !== deviceType) {
      message.error('添加失败，仅支持同类型设备对比！');
      return;
    }

    const newDevice: DeviceType = {
      ...device,
      topCategoryName: get(normalizedList, [topCategory, 'metaName'], topCategory),
      secondCategoryName: get(normalizedList, [secondCategory, 'metaName'], secondCategory),
      deviceTypeName: get(normalizedList, [deviceType, 'metaName'], deviceType),
      assetNo: get(normalizedList, [device.assetNo, 'assetNo'], device.assetNo),
      //铺平数据供定制column和删除隐藏项使用
      spaceGuidName: `${idcTag}.${blockTag}.${roomTag}`,
      idcTag,
      blockTag,
      roomTag,
      operationStatusName: get(device, ['operationStatus', 'name'], null),
      assetStatusName: get(device, ['assetStatus', 'name'], null),
      purchaseTimeName: purchaseTime ? dayjs(purchaseTime).format(DATE_FORMAT) : null,
      checkTimeName: checkTime ? dayjs(checkTime).format(DATE_FORMAT) : null,
      warrantyTimeName: warrantyTime ? dayjs(warrantyTime).format(DATE_FORMAT) : null,
      enableTimeName: enableTime ? dayjs(enableTime).format(DATE_FORMAT) : null,
      scrapTimeName: scrapTime ? dayjs(scrapTime).format(DATE_FORMAT) : null,
      purchasePriceName: purchasePrice ? '￥' + purchasePrice : null,
      netWorthName: netWorth ? '￥' + netWorth : null,
      warrantyPriceName: warrantyPrice ? '￥' + warrantyPrice : null,
      warrantyStatusName: get(device, ['warrantyStatus', 'name'], null),
    };

    //添加测点
    const {
      data: { data: pointList },
    } = await fetchPointsByCondition({
      deviceType,
      isOnlyCore: true,
    });

    const {
      data: { data: otherPointList },
    } = await fetchPointsByCondition({
      deviceType,
    });

    const { response: paramList } = await specService.fetchSpecList({
      deviceType: deviceType,
      modelCode: productModel,
    });
    newDevice.paramList = [];
    if (paramList) {
      newDevice.paramList = paramList.data;
    }
    newDevice.pointList = pointList;
    newDevice.otherPointList = otherPointList.filter(item => !item.priority);
    saveState([...stateList, newDevice]);
    message.success('添加成功');
    setSideBarVisible(true);
    setSideBarTab('devicedifftool');
  };

  return (
    <Tooltip title="将该设备添加至设备对比">
      <span onClick={onAddDeviceToDiffTool}>
        <FolderAddOutlined />
      </span>
    </Tooltip>
  );
}
