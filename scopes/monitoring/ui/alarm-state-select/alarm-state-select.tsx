import React, { useMemo } from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { OptionProps, SelectProps } from '@manyun/base-ui.ui.select';

import { getAlarmLocales } from '@manyun/monitoring.model.alarm';

type Option = Omit<OptionProps, 'children'>;

export type AlarmStateSelectProps = {
  optionsFilter?: (option: Option, index: number, options: Option[]) => boolean;
} & Omit<SelectProps, 'options'>;

export const AlarmStateSelect = ({ optionsFilter, ...resetProps }: AlarmStateSelectProps) => {
  const options: SelectProps['options'] = useMemo(() => {
    const locales = getAlarmLocales();
    const newOptions = [
      { value: 'TRIGGER', label: locales.state.TRIGGER },
      { value: 'RECOVER', label: locales.state.RECOVER },
      { value: 'SUPPRESS', label: locales.state.SUPPRESS },
    ];
    if (optionsFilter) {
      return newOptions.filter(optionsFilter);
    }

    return newOptions;
  }, [optionsFilter]);

  return <Select {...resetProps} options={options} />;
};
