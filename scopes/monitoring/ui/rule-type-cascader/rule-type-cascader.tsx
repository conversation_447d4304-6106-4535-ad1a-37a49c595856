import React from 'react';

import { Cascader } from '@manyun/base-ui.ui.cascader';
import type { CascaderProps, CascaderRef } from '@manyun/base-ui.ui.cascader';
import { message } from '@manyun/base-ui.ui.message';

import { fetchTreeModeMetaData } from '@manyun/resource-hub.service.fetch-tree-mode-meta-data';
import type { BackendData } from '@manyun/resource-hub.service.fetch-tree-mode-meta-data';

export type RuleTypeCascaderProps = CascaderProps<BackendData>;

export const RuleTypeCascader = React.forwardRef<CascaderRef, RuleTypeCascaderProps>(
  ({ options: _options, loading: _loading, ...rest }, ref) => {
    const [loading, setLoading] = React.useState(false);
    const [options, setOptions] = React.useState<BackendData[]>([]);
    React.useEffect(() => {
      (async () => {
        setLoading(true);
        const { error, data } = await fetchTreeModeMetaData({
          topCategory: 'EXPERT_BIZ_TYPE',
          secondCategory: 'EXPERT_RULE_TYPE',
        });
        setLoading(false);
        if (error) {
          message.error(error.message);
        }
        setOptions(data.data);
      })();
    }, []);

    return (
      <Cascader<BackendData>
        ref={ref}
        fieldNames={{ label: 'metaName', value: 'metaCode' }}
        {...rest}
        loading={loading}
        options={options}
      />
    );
  }
);

RuleTypeCascader.displayName = 'RuleTypeCascader';
