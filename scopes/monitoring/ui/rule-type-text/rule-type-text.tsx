import React from 'react';

import { useRuleType } from '@manyun/monitoring.hook.use-rule-type';

export type RuleTypeTextProps = {
  /** 业务类型 code，仅传业务类型 code 时将返回对应的业务类型名称 */
  bizCode: string;
  /** 规则类型 code，若传值则将将返回业务类型下对应的规则类型名称 */
  ruleCode?: string;
};

export function RuleTypeText({ bizCode, ruleCode }: RuleTypeTextProps) {
  const ruleType = useRuleType(bizCode);
  if (ruleCode === undefined) {
    return <>{ruleType?.metaName || '未知'}</>;
  }
  return <>{ruleType?.children.find(item => item.metaCode === ruleCode)?.metaName || '未知'}</>;
}
