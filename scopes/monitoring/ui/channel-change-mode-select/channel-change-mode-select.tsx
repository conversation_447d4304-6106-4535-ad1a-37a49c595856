import React, { useEffect, useState } from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import type { SelectProps } from '@manyun/base-ui.ui.select';

import { fetchChannelChangeMode } from '@manyun/monitoring.service.fetch-channel-change-mode';

export type ChannelChangeModeSelectProps = Omit<SelectProps, 'options'>;

export const ChannelChangeModeSelect = (props: SelectProps) => {
  const [options, setOptions] = useState<SelectProps['options']>([]);
  useEffect(function () {
    (async () => {
      const { error, data } = await fetchChannelChangeMode();
      if (error) {
        message.error(error.message);
        return;
      }
      const list = Object.keys(data).map(item => {
        return { value: item, label: data[item] };
      });
      setOptions(list);
    })();
  }, []);

  return <Select options={options} {...props} />;
};
