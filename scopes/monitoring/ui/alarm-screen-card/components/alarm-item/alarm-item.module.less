@import (reference) '@manyun/base-ui.theme.theme/dist/theme.less';

.alarmTitleEllipsis {
  padding-left: 4px;
  max-height: 58px;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.alarmCard {
  max-height: 166px;
  margin: 12px 3px;
  box-shadow: 0px 1px 8px @shadow-color;
}

.warnBoxShadow {
  box-shadow: 0px 1px 8px @warning-color;
}

.errBoxShadow {
  // variable没有可用颜色
  box-shadow: 0px 1px 8px rgba(255, 0, 0, 0.15);
}
