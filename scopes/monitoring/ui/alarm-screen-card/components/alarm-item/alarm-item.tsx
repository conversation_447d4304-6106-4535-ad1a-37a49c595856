import React, { useState } from 'react';
import { Link } from 'react-router-dom';

import AimOutlined from '@ant-design/icons/es/icons/AimOutlined';
import EnvironmentOutlined from '@ant-design/icons/es/icons/EnvironmentOutlined';
import classNames from 'classnames';
import dayjs from 'dayjs';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import {
  DataCircleOutlined,
  EndOfTimeOutlined,
  ModelingOutlined,
  StartOfTimeOutlined,
} from '@manyun/base-ui.icons';
import { Badge } from '@manyun/base-ui.ui.badge';
import { Card } from '@manyun/base-ui.ui.card';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import type { ColumnType } from '@manyun/base-ui.ui.table';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';

import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import type { AlarmJSON } from '@manyun/monitoring.model.alarm';
import { generateSpaceOrDeviceRoutePath } from '@manyun/monitoring.route.monitoring-routes';
import { acceptAlarms } from '@manyun/monitoring.service.accept-alarms';
import { batchRemoveAlarms } from '@manyun/monitoring.service.batch-remove-alarms';
import { AlarmDrawer } from '@manyun/monitoring.ui.alarm-drawer';
import { AlarmLevelText } from '@manyun/monitoring.ui.alarm-level-text';
import { AlarmLifecyStatusText } from '@manyun/monitoring.ui.alarm-lifecy-status-text';
import { AlarmStatusText } from '@manyun/monitoring.ui.alarm-status-text';
import { DeviceTypeText } from '@manyun/resource-hub.ui.device-type-text';
import { getSpaceGuid } from '@manyun/resource-hub.util.space-guid';

import { AlarmChartPoint } from '../alarm-chart-point-text';
import styles from './alarm-item.module.less';
import { AlarmDrawerExtra } from './components/alarm-drawer-extra';

export type AlarmItemProps = {
  style?: React.CSSProperties;
  alarmItem: AlarmJSON;
  columns: ColumnType[];
  showColumnsLength: number;
  onRefresh: () => void;
};

export function AlarmItem({
  style,
  alarmItem,
  columns,
  showColumnsLength,
  onRefresh,
}: AlarmItemProps) {
  const idc = alarmItem.source.spaceGuidMap.idcTag;

  const [visible, setVisible] = useState<boolean>(false);

  const onClose = () => {
    setVisible(false);
  };

  const onRowClick = () => {
    setVisible(true);
  };

  const getCardClassName = () => {
    if (
      dayjs(new Date().valueOf()).diff(alarmItem.createdAt, 'minutes') > 2 &&
      alarmItem.lifecycleState.code === 'ACTIVE'
    ) {
      return styles.errBoxShadow;
    }

    if (
      dayjs(new Date().valueOf()).diff(alarmItem.createdAt, 'minutes') > 1 &&
      alarmItem.lifecycleState.code === 'ACTIVE'
    ) {
      return styles.warnBoxShadow;
    }
    return '';
  };

  const columnsCodeList = useDeepCompareMemo(() => {
    const columnCodes: string[] = [];
    columns.forEach(item => {
      if (item.show && item.dataIndex) {
        columnCodes.push(item.dataIndex as string);
      }
    });
    return columnCodes;
  }, [columns]);

  const alarmShowColumnList = useDeepCompareMemo(() => {
    const list = [];
    if (env.__DEBUG_MODE__) {
      list.push(
        <Col key="id" span={8}>
          <Space size={8}>
            <Typography.Text>id</Typography.Text>
            <Typography.Text>{alarmItem.id}</Typography.Text>
          </Space>
        </Col>
      );
    }

    const ellipsisStyle: React.CSSProperties = {
      flex: 1,
      display: 'block',
      whiteSpace: 'nowrap',
      overflow: 'hidden',
      textOverflow: 'ellipsis',
    };
    const flexStyle: React.CSSProperties = {
      display: 'flex',
      gap: 8,
      alignItems: 'center',
    };

    const colSpan = showColumnsLength < 3 ? 12 : 8;
    if (columnsCodeList.includes('createdAt')) {
      const createdAtText = alarmItem.createdAt
        ? dayjs(alarmItem.createdAt).format('MM-DD HH:mm:ss')
        : '--';
      list.push(
        <Col key="createdAt" span={colSpan}>
          <div style={flexStyle}>
            <Tooltip title="告警开始时间">
              <StartOfTimeOutlined />
            </Tooltip>
            <Tooltip title={createdAtText}>
              <Typography.Text style={ellipsisStyle}>{createdAtText}</Typography.Text>
            </Tooltip>
          </div>
        </Col>
      );
    }
    if (columnsCodeList.includes('recoveredAt')) {
      const recoveredAtText = alarmItem.recoveredAt
        ? dayjs(alarmItem.recoveredAt).format('MM-DD HH:mm:ss')
        : '--';
      list.push(
        <Col key="recoveredAt" span={colSpan}>
          <div style={flexStyle}>
            <Tooltip title="告警恢复时间">
              <EndOfTimeOutlined />
            </Tooltip>
            <Tooltip title={recoveredAtText}>
              <Typography.Text style={ellipsisStyle}>{recoveredAtText}</Typography.Text>
            </Tooltip>
          </div>
        </Col>
      );
    }
    if (columnsCodeList.includes('source.spaceGuidMap')) {
      const spaceGuid = getSpaceGuid(
        idc,
        alarmItem.source.spaceGuidMap.blockTag,
        alarmItem.source.spaceGuidMap.roomTag
      );

      list.push(
        <Col key="source.spaceGuidMap" span={colSpan}>
          <Space size={8}>
            <Tooltip title="位置">
              <EnvironmentOutlined />
            </Tooltip>
            <Link
              to={generateSpaceOrDeviceRoutePath({
                guid: spaceGuid!,
                type: alarmItem.source.spaceGuidMap.roomTag
                  ? 'ROOM'
                  : alarmItem.source.spaceGuidMap.blockTag
                  ? 'BLOCK'
                  : 'IDC',
                spotlightTarget:
                  alarmItem.source.dimension === 'DEVICE' ? alarmItem.source.guid : '',
              })}
              target="_blank"
              onClick={e => e.stopPropagation()}
            >
              {alarmItem.source.spaceGuidMap.blockTag
                ? getSpaceGuid(
                    alarmItem.source.spaceGuidMap.blockTag,
                    alarmItem.source.spaceGuidMap.roomTag
                  )
                : idc}
            </Link>
          </Space>
        </Col>
      );
    }
    if (columnsCodeList.includes('source')) {
      list.push(
        <Col key="source" span={colSpan}>
          <div style={flexStyle}>
            <Tooltip title="告警对象">
              <AimOutlined />
            </Tooltip>
            {alarmItem.source.dimension === 'GRID' || alarmItem.source.dimension === 'COLUMN' ? (
              alarmItem.source.name
            ) : (
              <Tooltip title={alarmItem.source.name}>
                <Link
                  style={ellipsisStyle}
                  to={generateSpaceOrDeviceRoutePath({
                    guid: alarmItem.source.guid,
                    type: alarmItem.source.dimension,
                  })}
                  target="_blank"
                  onClick={e => e.stopPropagation()}
                >
                  {alarmItem.source.name}
                </Link>
              </Tooltip>
            )}
          </div>
        </Col>
      );
    }
    if (columnsCodeList.includes('pointData.current')) {
      list.push(
        <Col key="pointData.current" span={colSpan}>
          <Space size={8}>
            <Tooltip title="现值">
              <DataCircleOutlined />
            </Tooltip>
            <AlarmChartPoint alarm={alarmItem} idc={idc} />
          </Space>
        </Col>
      );
    }
    if (columnsCodeList.includes('mergeCount')) {
      list.push(
        <Col key="mergeCount" span={colSpan}>
          <Space size={8}>
            <Tooltip title="收敛告警数">
              <ModelingOutlined />
            </Tooltip>
            <Typography.Text>{alarmItem.mergeCount}</Typography.Text>
          </Space>
        </Col>
      );
    }
    return <Row>{list}</Row>;
  }, [alarmItem, columnsCodeList, idc, showColumnsLength]);

  const alarmItemHeight = useDeepCompareMemo(() => {
    if (showColumnsLength === 0) {
      return 72;
    }
    if (showColumnsLength < 4) {
      return 96;
    }
    return 120;
  }, [showColumnsLength]);

  const alarmTitle = useDeepCompareMemo(() => {
    if (!alarmItem) {
      return <></>;
    }
    const {
      point: { validLimits },
      pointData: { snapshot },
    } = alarmItem;

    const status = validLimits && validLimits[Number(snapshot)];

    const title = (
      <span style={{ fontSize: '14px' }}>
        <AlarmLevelText code={alarmItem.level} />{' '}
        <DeviceTypeText code={alarmItem.source.deviceType} />
        {alarmItem.point.name ? `_${alarmItem.point.name}` : ''}
        {alarmItem.point.dataType.code === 'AI' ? (
          <>
            {alarmItem.cause.name ? `_${alarmItem.cause.name}` : ''}
            {alarmItem?.pointData.snapshot
              ? `_告警值:${alarmItem?.pointData.snapshot}${alarmItem.point.unit || ''}`
              : ''}{' '}
          </>
        ) : (
          <>{`_${status}告警 `} </>
        )}
        {columnsCodeList.includes('lifecycleState') && (
          <AlarmLifecyStatusText code={alarmItem.lifecycleState.code} shape="__tag__" />
        )}
        {columnsCodeList.includes('alarmState') && (
          <AlarmStatusText code={alarmItem.state.code} shape="__tag__" />
        )}
      </span>
    );

    return (
      <Tooltip title={title} className={styles.alarmTitleEllipsis}>
        {title}
      </Tooltip>
    );
  }, [alarmItem, columnsCodeList]);

  const onAcceptAlarm = async (alarmIds: number[]) => {
    const { error } = await acceptAlarms({ alarmIds });
    if (error) {
      message.error(error.message);
      return;
    }
    message.success('受理成功');
    onRefresh();
  };

  const onRemoveAlarm = async ({
    alarmIds,
    reason,
    descriptions,
    ticketNumber,
  }: {
    alarmIds: number[];
    reason: string;
    descriptions: string;
    ticketNumber?: string | null;
  }) => {
    const { error } = await batchRemoveAlarms({
      alarmIds,
      alarmReason: reason,
      removeDesc: descriptions,
      orderNo: ticketNumber,
    });
    if (error) {
      message.error(error.message);
      return;
    }
    message.success('下盯屏成功');
    onRefresh();
  };

  return (
    <>
      <Card
        style={{ ...style, width: 'calc(100% - 6px)', height: alarmItemHeight }}
        className={classNames(styles.alarmCard, getCardClassName())}
        size="small"
        bordered={false}
        onClick={onRowClick}
      >
        <div style={{ display: 'flex', paddingBottom: 8 }}>
          <Badge status={alarmItem?.type.code === 'ERROR' ? 'error' : 'warning'} />
          {alarmTitle}
        </div>
        {alarmShowColumnList}
      </Card>
      <AlarmDrawer
        visible={visible}
        idc={idc}
        alarm={alarmItem}
        extra={
          <AlarmDrawerExtra
            alarmItem={alarmItem}
            onAcceptAlarm={onAcceptAlarm}
            onRemoveAlarm={onRemoveAlarm}
          />
        }
        onClose={onClose}
      />
    </>
  );
}
