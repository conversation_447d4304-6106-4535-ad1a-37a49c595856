import React from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Space } from '@manyun/base-ui.ui.space';

import { Alarm } from '@manyun/monitoring.model.alarm';
import type { AlarmJSON } from '@manyun/monitoring.model.alarm';
import { AlarmRemoveConfirmModal } from '@manyun/monitoring.page.alarm-staring-screen';

export type AlarmDrawerExtraProps = {
  alarmItem: AlarmJSON;
  onAcceptAlarm: (alarmIds: number[]) => void;
  onRemoveAlarm: ({
    alarmIds,
    reason,
    descriptions,
    ticketNumber,
  }: {
    alarmIds: number[];
    reason: string;
    descriptions: string;
    ticketNumber?: string | null;
  }) => void;
};

export function AlarmDrawerExtra({
  alarmItem,
  onAcceptAlarm,
  onRemoveAlarm,
}: AlarmDrawerExtraProps) {
  const status = Alarm.buildSyntheticState(alarmItem.lifecycleState.code, alarmItem.state.code);

  return (
    <Space>
      {(status === 'unaccepted-n-recovered' || status === 'unaccepted-n-unrecovered') && (
        <Button
          type="primary"
          disabled={alarmItem.lifecycleState.code !== 'ACTIVE'}
          onClick={() => onAcceptAlarm([alarmItem.id])}
        >
          受理
        </Button>
      )}
      {status === 'accepted-n-recovered' && (
        <AlarmRemoveConfirmModal
          mode="button"
          buttonType="default"
          disabled={!['CONFIRMED', 'PROCESS'].includes(alarmItem.lifecycleState.code)}
          record={alarmItem}
          onOk={onRemoveAlarm}
        />
      )}
    </Space>
  );
}
