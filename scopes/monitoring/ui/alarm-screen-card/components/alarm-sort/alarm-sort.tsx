import React, { useState } from 'react';

import { CaretDownOutlined, CaretUpOutlined, SwapOutlined } from '@ant-design/icons';

import { Card } from '@manyun/base-ui.ui.card';
import { Dropdown } from '@manyun/base-ui.ui.dropdown';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';

import styles from './alarm-sort.module.less';

type SortItem = {
  title: string;
  code: string;
  showSort?: boolean;
};

export type SortOrder = 'ASCEND' | 'DESCEND' | undefined;

export type AlarmSortProps = {
  dataSource: SortItem[];
  value: string;
  sortOrder: SortOrder;
  defaultValue: string;
  defaultSortOrder: SortOrder;
  onChange: ({ value, sortOrder }: { value: string; sortOrder: SortOrder }) => void;
};

export function AlarmSort({
  value,
  sortOrder,
  defaultValue,
  defaultSortOrder,
  dataSource,
  onChange,
}: AlarmSortProps) {
  const [open, setOpen] = useState(false);

  return (
    <Dropdown
      open={open}
      dropdownRender={() => (
        <SortCard
          value={value}
          sortOrder={sortOrder}
          defaultValue={defaultValue}
          defaultSortOrder={defaultSortOrder}
          dataSource={dataSource}
          onChange={onChange}
          onHide={() => {
            setOpen(false);
          }}
        />
      )}
      placement="bottomRight"
      trigger={['click']}
      arrow={{ pointAtCenter: true }}
      onOpenChange={open => {
        setOpen(open);
      }}
    >
      <Tooltip title="排序">
        <SwapOutlined rotate={-90} />
      </Tooltip>
    </Dropdown>
  );
}

type SortCardProps = AlarmSortProps & {
  onHide: () => void;
};

function SortCard({
  onHide,
  value,
  sortOrder,
  defaultValue,
  defaultSortOrder,
  dataSource,
  onChange,
}: SortCardProps) {
  const onSortClick = (item: SortItem) => {
    const { code, showSort = true } = item;
    let newSortOrder: SortOrder;
    if (!showSort) {
      newSortOrder = undefined;
    } else if (code === value) {
      if (sortOrder === 'ASCEND') {
        newSortOrder = 'DESCEND';
      } else if (sortOrder === 'DESCEND') {
        newSortOrder = undefined;
      } else {
        newSortOrder = 'ASCEND';
      }
    } else {
      newSortOrder = 'ASCEND';
    }

    onChange({ value: code, sortOrder: newSortOrder });

    onHide();
  };

  const TooltipTitle = (code: string) => {
    if (code === value) {
      if (sortOrder === 'ASCEND') {
        return '点击降序';
      } else if (sortOrder === 'DESCEND') {
        return '取消排序';
      } else {
        return '点击升序';
      }
    } else {
      return '点击升序';
    }
  };

  const onReset = () => {
    onChange({ value: defaultValue, sortOrder: defaultSortOrder });
  };

  return (
    <Card
      style={{ width: 151 }}
      size="small"
      title="排序"
      extra={<Typography.Link onClick={onReset}>重置</Typography.Link>}
    >
      <Space size="middle" direction="vertical" style={{ width: '100%' }}>
        {dataSource.map(item => {
          const { code, title, showSort = true } = item;
          const isActiveItem = code === value;
          return (
            <Tooltip key={code} title={TooltipTitle(code)}>
              <div
                style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
                onClick={() => onSortClick(item)}
              >
                {showSort &&
                  (isActiveItem ? (
                    <Typography.Link>{title}</Typography.Link>
                  ) : (
                    <Typography.Text className={styles.textHover}>{title}</Typography.Text>
                  ))}

                <div className={styles.flexboxColumn} style={{ cursor: 'pointer' }}>
                  <CaretUpOutlined
                    className={
                      isActiveItem && sortOrder === 'ASCEND'
                        ? styles.sortIconActive
                        : styles.sortIconDefault
                    }
                    style={{ height: '12px' }}
                  />
                  <CaretDownOutlined
                    className={
                      isActiveItem && sortOrder === 'DESCEND'
                        ? styles.sortIconActive
                        : styles.sortIconDefault
                    }
                    style={{ height: '12px' }}
                  />
                </div>
              </div>
            </Tooltip>
          );
        })}
      </Space>
    </Card>
  );
}
