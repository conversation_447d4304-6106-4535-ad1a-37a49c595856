import React, { useEffect, useMemo, useState } from 'react';
import { AutoSizer, List as VList } from 'react-virtualized';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import type { CardProps } from '@manyun/base-ui.ui.card';
import { Card } from '@manyun/base-ui.ui.card';
import { Space } from '@manyun/base-ui.ui.space';
import type { ColumnType } from '@manyun/base-ui.ui.table';

import { EditColumns } from '@manyun/dc-brain.ui.edit-columns';
import type { AlarmJSON } from '@manyun/monitoring.model.alarm';
import { getAlarmLocales } from '@manyun/monitoring.model.alarm';
import type { SortField } from '@manyun/monitoring.service.fetch-alarms';

import { AlarmItem } from './components/alarm-item';
import { AlarmLoading } from './components/alarm-loading';
import { AlarmSort } from './components/alarm-sort';
import type { SortOrder } from './components/alarm-sort';

export type AlarmScreenCardProps = {
  alarmList: AlarmJSON[];
  onSortChange?: (params: { value: string; sortOrder: SortOrder }) => void;
  onRefresh: () => void;
} & CardProps;

export function AlarmScreenCard({
  alarmList,
  onSortChange,
  onRefresh,
  ...resetProps
}: AlarmScreenCardProps) {
  const alarmLocales = getAlarmLocales();

  const defaultColumnsConfig = useMemo(() => {
    const defaultColumns = [
      {
        title: alarmLocales.lifecycleState.__self,
        dataIndex: 'lifecycleState',
        show: true,
      },
      { title: alarmLocales.state.__self, dataIndex: 'alarmState', show: true },
      { title: alarmLocales.createdAt, dataIndex: 'createdAt', show: true },
      { title: alarmLocales.recoveredAt, dataIndex: 'recoveredAt', show: false },
      {
        title: alarmLocales.source.spaceGuidMap.__self,
        dataIndex: 'source.spaceGuidMap',
        show: true,
      },
      { title: alarmLocales.source.__self, dataIndex: 'source', show: true },
      {
        title: alarmLocales.pointData.current,
        dataIndex: 'pointData.current',
        show: false,
      },
      { title: alarmLocales.mergeCount, dataIndex: 'mergeCount', show: false },
    ];
    return defaultColumns;
  }, [
    alarmLocales.createdAt,
    alarmLocales.lifecycleState.__self,
    alarmLocales.mergeCount,
    alarmLocales.pointData,
    alarmLocales.recoveredAt,
    alarmLocales.source.__self,
    alarmLocales.source.spaceGuidMap.__self,
    alarmLocales.state.__self,
  ]);

  const [columnsConfig, setColumnsConfig] = useState<ColumnType[]>(defaultColumnsConfig);

  const [alarmSortField, setAlarmSortField] = useState<SortField>('gmtCreate');
  const [alarmSortOrder, setAlarmSortOrder] = useState<SortOrder>('DESCEND');

  useEffect(() => {
    onSortChange && onSortChange({ value: alarmSortField, sortOrder: alarmSortOrder });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const showColumnsLength = useDeepCompareMemo(() => {
    // 不包括告警状态和处理状态
    return columnsConfig.reduce((result, item) => {
      if (item.show && item.dataIndex !== 'lifecycleState' && item.dataIndex !== 'alarmState') {
        return result + 1;
      }
      return result;
    }, 0);
  }, [columnsConfig]);

  const rowHeight = useMemo(() => {
    if (showColumnsLength === 0) {
      return 86;
    }
    if (showColumnsLength < 4) {
      return 110;
    }
    return 134;
  }, [showColumnsLength]);

  const renderItem = ({
    index,
    key,
    style,
  }: {
    index: number;
    key: string;
    style: React.CSSProperties;
  }) => {
    return (
      <AlarmItem
        key={key}
        style={style}
        alarmItem={alarmList[index]}
        columns={columnsConfig}
        showColumnsLength={showColumnsLength}
        onRefresh={onRefresh}
      />
    );
  };

  return (
    <Card
      bordered={false}
      {...resetProps}
      bodyStyle={{ height: 300, overflow: 'hidden auto', padding: 0, ...resetProps.bodyStyle }}
      extra={
        <Space size={17} style={{ cursor: 'pointer' }}>
          <AlarmSort
            value={alarmSortField}
            sortOrder={alarmSortOrder}
            defaultValue="gmtCreate"
            defaultSortOrder="DESCEND"
            dataSource={[
              { title: '告警开始时间', code: 'gmtCreate' },
              { title: '告警恢复时间', code: 'recoverTime' },
              { title: '告警受理时间', code: 'confirmTime' },
              { title: '下盯屏时间', code: 'removeTime' },
              { title: '受理状态', code: 'alarmStatus' },
            ]}
            onChange={({ value, sortOrder }) => {
              setAlarmSortField(value as SortField);
              setAlarmSortOrder(sortOrder);
              onSortChange && onSortChange({ value, sortOrder });
            }}
          />
          <EditColumns
            uniqKey="MONITORING_ALARMS_SCREEN_CARD_COLUMNS"
            defaultValue={defaultColumnsConfig}
            draggable={false}
            listsHeight={500}
            onChange={(value: ColumnType[]) => {
              setColumnsConfig(value);
            }}
          />
          {resetProps.extra}
        </Space>
      }
    >
      {alarmList.length ? (
        <AutoSizer>
          {({ width, height }) => (
            <VList
              width={width}
              height={height}
              overscanRowCount={10}
              rowCount={alarmList.length}
              rowHeight={rowHeight}
              rowRenderer={renderItem}
            />
          )}
        </AutoSizer>
      ) : (
        <AlarmLoading />
      )}
    </Card>
  );
}
