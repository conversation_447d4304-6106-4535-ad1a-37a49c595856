import React from 'react';

import type { AlarmJSON } from '@manyun/monitoring.model.alarm';

import { AlarmScreenCard } from './alarm-screen-card';

const alarmList: AlarmJSON[] = [
  {
    activatedAt: 1689227990000,
    alarmDuration: undefined,
    cause: { code: 'UPPER', name: '超上限' },
    changeId: null,
    confirmUser: { id: 0, name: '系统' },
    confirmedAt: 1689227991000,
    createdAt: 1689227990000,
    customerCount: 0,
    deviceCount: 0,
    eventId: 71443,
    expectedStatus: 'NORMAL',
    gridCount: 0,
    id: 19,
    latestTriggeredAt: 1689227990000,
    level: '1',
    lifecycleState: { code: 'PROCESS', name: '已建单' },
    mergeCount: 0,
    mergeRuleId: 0,
    message:
      '[1级【紧急】级]告警，A楼包间出现楼栋-储油罐-油量总和接口测试告警，告警设备：EC06.A，机柜影响面：影响机柜0个',
    monitoringItem: { id: 17319, threshold: '≥50m³' },
    notifyCount: 0,
    point: {
      code: '1097000',
      name: '楼栋-储油罐-油量总和',
      validLimits: {},
      dataType: { code: 'AI', name: '模拟量读点' },
      unit: 'm³',
    },
    pointData: { current: 305.2, snapshot: '187.2' },
    reason: null,
    recoveredAt: 1689229741000,
    removeUser: null,
    removedAt: null,
    removedDescriptions: null,
    source: {
      deviceLabel: null,
      deviceType: '90102',
      deviceTypeLevel1: '9',
      dimension: 'BLOCK',
      guid: 'EC06.A',
      name: 'EC06.A',
      roomName: null,
      roomType: null,
      spaceGuidMap: {
        idcTag: 'EC06',
        blockTag: 'A',
        roomTag: null,
        columnTag: null,
        gridTag: null,
      },
      variant: 'space',
    },
    state: { code: 'RECOVER', name: '已恢复' },
    type: { code: 'ERROR', name: '告警' },
  },
];
export const BasicAlarmScreenCard = () => {
  return (
    <AlarmScreenCard
      title="告警test"
      alarmList={alarmList}
      style={{ width: 400 }}
      bodyStyle={{ height: 400, padding: 0 }}
    />
  );
};
