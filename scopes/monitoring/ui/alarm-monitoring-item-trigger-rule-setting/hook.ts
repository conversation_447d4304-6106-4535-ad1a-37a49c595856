import { useCallback } from 'react';
import { useSelector } from 'react-redux';

import type { Config } from '@manyun/dc-brain.config.base';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import {
  useCurrentMutateType,
  useMutateTplFields,
} from '@manyun/monitoring.hook.use-alarm-configuration-template';
import { AlarmTemplateType } from '@manyun/monitoring.model.alarm-template';
import { DimensionType } from '@manyun/monitoring.model.trigger-rule';

// @ts-ignore: Could not find a declaration file
import { getCurrentConfig } from '@manyun/dc-brain.legacy.redux/selectors/configSelectors';

/**
 * @type {deviceType} 监控测点的设备类型
 *
 */
export const useGetPointConditions = (deviceType?: string) => {
  const [currenMutateType] = useCurrentMutateType();
  const [{ target, type }] = useMutateTplFields(currenMutateType);
  return {
    deviceType: deviceType,
    //type === AlarmTemplateType.Device ? target : deviceType /**实例模板取 监控测点的设备类型 */,
    spaceGuid: type === AlarmTemplateType.Instance ? target : undefined,
    target,
    type,
  } as const;
};

/**
 *
 * 根据告警/前置条件的维度 返回规格选择框的参数
 * @export
 * @param {{
 *   dimension: DimensionType;
 *   deviceType: string;
 *   specUnit?: string;
 * }} {
 *   dimension, 同设备、同机柜、同机列、同包间、同楼栋、同机房
 *   deviceType, 监控测点的设备类型：空间或者设备
 *   specUnit, 监控测点的单位，用来过滤出同单位的规格
 * }
 * @return { isDevice, deviceType, onlyNumber, specUnit, required} SepcSlect需要的props
 */
export function useGetSpecParams({
  dimension,
  deviceType,
  specUnit,
}: {
  dimension: DimensionType;
  deviceType: string;
  specUnit?: string;
}) {
  const config = useSelector(getCurrentConfig) as Config;
  const configUtil = new ConfigUtil(config);
  const roomDT = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_ROOM);
  const gridDT = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_GRID);
  const columnDT = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_COLUMN);

  let params: {
    deviceType: string;
    isDevice: boolean;
    onlyNumber: boolean;
    specUnit?: string;
    required?: boolean;
  } = { isDevice: true, deviceType, onlyNumber: true, specUnit, required: true };

  switch (dimension) {
    case DimensionType.Device:
      /**根据监控测点的设备类型来判断 -- 这个逻辑应该在SepecSelect内部实现 */
      if ([roomDT, gridDT, columnDT].includes(deviceType)) {
        params.isDevice = false;
      }
      break;
    case DimensionType.Grid /**机柜 */:
      params.isDevice = false;
      params.deviceType = gridDT;
      break;
    case DimensionType.Room /**包间 */:
      params.isDevice = false;
      params.deviceType = roomDT;
      break;
    case DimensionType.Cloumn /**机列 */:
      params.isDevice = false;
      params.deviceType = columnDT;
      break;
    case DimensionType.Idc /**机房 */:
      params.isDevice = false;
      break;
    case DimensionType.Block /**楼栋 */:
      params.isDevice = false;
      break;
    default:
      break;
  }

  return params;
}

/**
 * 根据设备类型返回维度
 * @export
 * @param {string} deviceType
 * @return {*}
 */
export function useGenerateRuleDimension(deviceType: string) {
  const config = useSelector(getCurrentConfig) as Config;
  const configUtil = new ConfigUtil(config);
  const roomDT = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_ROOM);
  const gridDT = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_GRID);
  const columnDT = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_COLUMN);
  const blockDT = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_BLOCK);
  const idcDT = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_IDC);

  let dimension: DimensionType = DimensionType.Device;
  switch (deviceType) {
    case gridDT:
      dimension = DimensionType.Grid;
      break;
    case columnDT:
      dimension = DimensionType.Cloumn;
      break;
    case roomDT:
      dimension = DimensionType.Room;
      break;
    case blockDT:
      dimension = DimensionType.Block;
      break;
    case idcDT:
      dimension = DimensionType.Idc;
      break;
    default:
      break;
  }

  return dimension;
}

/**根据维度返回设备类型 */
export function useGetDeviceTypeByDimension(pointDeviceType: string) {
  const config = useSelector(getCurrentConfig) as Config;

  const transforDeviceType = useCallback(
    (dimension: DimensionType) => {
      let deviceType: string = pointDeviceType;
      const configUtil = new ConfigUtil(config);
      if (dimension) {
        switch (dimension) {
          case DimensionType.Device:
            deviceType = pointDeviceType;
            break;
          case DimensionType.Grid:
            deviceType = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_GRID);
            break;
          case DimensionType.Cloumn:
            deviceType = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_COLUMN);
            break;
          case DimensionType.Room:
            deviceType = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_ROOM);
            break;
          case DimensionType.Block:
            deviceType = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_BLOCK);
            break;
          case DimensionType.Idc:
            deviceType = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_IDC);
            break;
          default:
            break;
        }
      }
      return deviceType;
    },
    [config, pointDeviceType]
  );

  return [transforDeviceType] as const;
}
