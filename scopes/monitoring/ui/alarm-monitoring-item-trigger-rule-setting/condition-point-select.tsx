import React, { useCallback, useRef } from 'react';

import { DimensionType } from '@manyun/monitoring.model.trigger-rule';
import { PointSelect } from '@manyun/resource-hub.ui.point-select';
import type { PointSelectProps, RefTreeSelectProps } from '@manyun/resource-hub.ui.point-select';

export type ConditionPointSelectProps = {
  dimension?: DimensionType;
  pointParams?: { isQueryNon: boolean; blockGuid: string };
} & PointSelectProps;
export type RefConditionPointSelect = {
  refresh: (dimension: DimensionType) => void;
};

export const ConditionPointSelect: React.ForwardRefExoticComponent<
  ConditionPointSelectProps & React.RefAttributes<RefConditionPointSelect>
> = React.forwardRef(
  (
    { dimension, ...props }: ConditionPointSelectProps,
    ref?: React.Ref<RefConditionPointSelect>
  ) => {
    const pointSelectRef = useRef<RefTreeSelectProps>(null);

    const getPointParams = useCallback(
      (changedDimension?: DimensionType) => {
        const params: Pick<
          PointSelectProps,
          'dataTypeList' | 'deviceType' | 'pointTypeList' | 'spaceGuid' | 'disabledPoint'
        > = {
          dataTypeList: props.dataTypeList ?? ['AI', 'DI'],
        };
        const _changedDimension = changedDimension ? changedDimension : dimension;
        params.deviceType = props.deviceType ?? '';
        if (_changedDimension) {
          switch (_changedDimension) {
            case DimensionType.Grid:
            case DimensionType.Cloumn:
            case DimensionType.Room:
            case DimensionType.Block:
            case DimensionType.Idc:
              params.pointTypeList = [
                'CAL_SPACE',
                'AGG_SPACE',
                'CAL_DEVICE',
                'AGG_DEVICE',
                'CUSTOM',
              ];
              break;
          }
        }
        if (props.pointTypeList) {
          params.pointTypeList = props.pointTypeList;
        }
        if (props.pointParams) {
          return { ...params, ...props.pointParams };
        }
        return params;
      },
      [dimension, props.dataTypeList, props.deviceType, props.pointParams, props.pointTypeList]
    );

    return (
      <PointSelect
        ref={pointSelectRef}
        {...props}
        {...getPointParams()}
        style={{ width: 240 }}
        showSearch
        treeNodeFilterProp="title"
        lazy
      />
    );
  }
);
ConditionPointSelect.displayName = 'ConditionPointSelect';
