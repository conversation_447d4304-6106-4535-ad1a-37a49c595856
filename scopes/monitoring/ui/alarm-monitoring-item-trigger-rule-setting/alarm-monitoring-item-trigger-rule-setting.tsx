import React, { useCallback, useMemo, useRef } from 'react';
import { useSelector } from 'react-redux';

import { CloseOutlined, PlusSquareOutlined } from '@ant-design/icons';

import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import type { FormInstance } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';

import type { MonitoringItemJSON } from '@manyun/monitoring.model.monitoring-item';
import type { DataType, Point } from '@manyun/monitoring.model.point';
import type { TriggerRuleJSON } from '@manyun/monitoring.model.trigger-rule';
import {
  DimensionType,
  FormulaType,
  IntervalSplitSymbol,
  SignType,
  TriggerRuleType,
} from '@manyun/monitoring.model.trigger-rule';
import type { ValueData } from '@manyun/monitoring.model.trigger-rule/trigger-rule';
import { selectPoints } from '@manyun/resource-hub.state.points';

import type { RefConditionPointSelect } from './condition-point-select';
import { ConditionPointSelect } from './condition-point-select';
import { useGenerateRuleDimension, useGetDeviceTypeByDimension } from './hook';
import {
  AIPointTriggerRuleFirstOptions,
  InitalTriggerRuleValues,
  getPreTriggerRuleFormalTypeOptions,
  getPreTriggerRuleSignOptions,
  getSettingFirstOptions,
  getSettingSecondOptions,
  getSpaceGuidByDimension,
} from './utils';
import { ValueDataInput } from './value-data-input';

export type AlarmMonitoringItemTriggerRuleSettingProps = {
  monitoringPoint: Point | undefined /**主测点信息 */;
  name: 'triggerRules' | 'preTriggerRules' /**FormItem name属性 */;
  form: FormInstance<MonitoringItemJSON>;
  pointParams?: { isQueryNon: boolean; blockGuid: string };
  onDIRuleFormulaTypeChange?: (show: boolean) => void;
};

export type FieldMode = 'DEFAULT' | 'REVERSE' | 'PRE';
export type FieldName = 'sign' | 'formulaType' | 'dimension' | 'pointCode' | 'valueData';

export const AlarmMonitoringItemTriggerRuleSetting = React.forwardRef<
  unknown,
  AlarmMonitoringItemTriggerRuleSettingProps
>(({ name, form, pointParams, monitoringPoint, onDIRuleFormulaTypeChange }, ref?) => {
  /**所有测点实体 */
  const { entities } = useSelector(selectPoints());

  /**当前测点的维度：用于设置维度的默认值 */
  const pointDimension = useGenerateRuleDimension(monitoringPoint?.deviceType ?? '');

  /**维度修改后，根据维度返回设备类型 */
  const [transforDeviceType] = useGetDeviceTypeByDimension(monitoringPoint?.deviceType ?? '');

  /**用于更新前置条件 维度刷新测点选择框 */
  const preConditionPointSelectRef = useRef<RefConditionPointSelect | null>(null);

  const SettingMode = useMemo(
    () => (name === 'preTriggerRules' ? 'PRE' : monitoringPoint?.dataType.code ?? 'AI'),
    [monitoringPoint?.dataType.code, name]
  );

  const getFirstOptions = useCallback(
    (fieldName: FieldName) => {
      let options = getSettingFirstOptions({
        triggerRule:
          name === 'triggerRules' ? TriggerRuleType.AlarmCondition : TriggerRuleType.PreCondition,
        dataType: (monitoringPoint?.dataType.code ?? 'AI') as DataType,
      });
      if (fieldName === 'dimension') {
        switch (pointDimension) {
          case DimensionType.Grid:
            options = options.slice(1);
            break;
          case DimensionType.Cloumn:
            options = options.slice(2);
            break;
          case DimensionType.Room:
            options = options.slice(3);
            break;
          case DimensionType.Block:
            options = options.slice(4);
            break;
          case DimensionType.Idc:
            options = options.slice(-1);
            break;
          default:
            break;
        }
      }
      return options;
    },
    [monitoringPoint?.dataType.code, name, pointDimension]
  );

  const getSecondOptions = useCallback(
    (fieldName: FieldName) => {
      const options = getSettingSecondOptions({
        fieldType: fieldName,
        dataType: (monitoringPoint?.dataType.code ?? 'AI') as DataType,
      });
      return options;
    },
    [monitoringPoint?.dataType.code]
  );

  const getRowFieldMode = useCallback(
    (index: number) => {
      let mode: FieldMode = 'DEFAULT';
      const values: TriggerRuleJSON[] = form.getFieldValue([name]);
      switch (name) {
        case 'triggerRules':
          if (
            (values[index] && values[index]?.formulaType === FormulaType.Continuous) ||
            monitoringPoint?.dataType.code === 'DI' /**当前测点是DI测点 */
          ) {
            mode = 'REVERSE';
          }
          break;
        case 'preTriggerRules':
          mode = 'PRE';
          break;
        default:
          break;
      }
      return mode;
    },
    [form, monitoringPoint?.dataType.code, name]
  );

  const getFieldNames = useCallback((fieldMode: FieldMode) => {
    const fieldNames: FieldName[] = ['sign', 'formulaType'];
    switch (fieldMode) {
      case 'DEFAULT':
        fieldNames[0] = 'sign';
        fieldNames[1] = 'formulaType';
        fieldNames[2] = 'valueData';
        break;
      case 'REVERSE':
        fieldNames[0] = 'formulaType';
        fieldNames[1] = 'sign';
        fieldNames[2] = 'valueData';
        break;
      case 'PRE' /**前置条件 */:
        fieldNames[0] = 'dimension';
        fieldNames[1] = 'pointCode';
        fieldNames[2] = 'sign';
        fieldNames[3] = 'formulaType';
        fieldNames[4] = 'valueData';
        break;
      default:
        break;
    }
    return fieldNames;
  }, []);

  /**数值选择清空*/
  const clearValueData = useCallback((preValue: { valueData: unknown }) => {
    preValue.valueData = undefined;
  }, []);

  const _onValidatorValueData = useCallback(
    async (val: ValueData | undefined, formulaType: FormulaType) => {
      if ([FormulaType.Change].includes(formulaType)) {
        if (val === undefined) {
          return Promise.resolve();
        }
        const changeStatus: string[] | string = val.code.split(IntervalSplitSymbol);
        if (changeStatus.some((status: string) => status === '')) {
          return Promise.reject('不能为空');
        }
        return Promise.resolve();
      }
      return Promise.resolve();
    },
    []
  );

  const onChangeValues = useCallback(
    (changeValues, values) => {
      /**triggerRules、preTriggerRules */
      Object.keys(changeValues).forEach(key => {
        let name: 'triggerRules' | 'preTriggerRules' | null = null;
        if (key === 'triggerRules') {
          name = 'triggerRules';
        }
        if (key === 'preTriggerRules') {
          name = 'preTriggerRules';
        }
        /**区分两种情况下如何更新值 */
        if (name) {
          const preValues: unknown[] = values[name];
          const changeArr: unknown[] = changeValues[name];

          changeArr.forEach((d, index) => {
            const rulePointEntity =
              entities[`${preValues[index].deviceType}.${preValues[index].pointCode}`];

            /**符号修改 */
            if (d.sign) {
              /**符号取表达式 */
              if (d.sign === FormulaType.Continuous) {
                if (preValues[index]) {
                  preValues[index].sign = SignType.IncreaseThan;
                  preValues[index].formulaType = d.sign;
                  preValues[index].unit = '%';
                }
              } else {
                if ([SignType.IncreaseThan, SignType.DecreaseThan].includes(d.sign)) {
                  preValues[index].unit = '%';
                } else {
                  preValues[index].unit = rulePointEntity
                    ? rulePointEntity.unit
                    : monitoringPoint?.unit;
                }
              }

              /**数值选择清空 */
              clearValueData(preValues[index]);
            }
            /**表达式修改 */
            if (d.formulaType) {
              const options = AIPointTriggerRuleFirstOptions.filter(
                option => option.type === 'sign'
              ).map(opt => opt.value);
              /**表达式取符号 */
              if (options.includes(d.formulaType)) {
                if (preValues[index]) {
                  preValues[index].formulaType = FormulaType.Custom;
                  preValues[index].sign = d.formulaType;
                  preValues[index].unit = monitoringPoint?.unit;
                }
              }
              /**变位告警 */
              if (d.formulaType === FormulaType.Change) {
                preValues[index].sign = SignType.Change;
              }
              /**异常告警 */
              if (d.formulaType === FormulaType.Status) {
                preValues[index].sign = SignType.In;
              }

              clearValueData(preValues[index]);
            }
            /**维度修改 */
            if (d.dimension) {
              preValues[index].pointCode = '';
              preValues[index].pointName = '';
              preValues[index].sign = '';
              preValues[index].formulaType = '';
              preValues[index].unit = '';
              /**根据维度返回deviceType */
              const devicetype = transforDeviceType(d.dimension);
              preValues[index].deviceType = devicetype;

              clearValueData(preValues[index]);
            }
            /** 变位告警不配触发次数、自动恢复 */
            if (name === 'triggerRules' && onDIRuleFormulaTypeChange) {
              const isALlChange = preValues.every(
                value => value.formulaType === FormulaType.Change
              );
              onDIRuleFormulaTypeChange(!isALlChange);
            }
            /**测点值修改 前置条件 */
            if (name === 'preTriggerRules' && d.pointCode) {
              preValues[index].sign = '';
              preValues[index].formulaType = '';
              preValues[index].unit = rulePointEntity ? rulePointEntity.unit : '';
              preValues[index].pointName = rulePointEntity ? rulePointEntity.name : '';
              preValues[index].pointInfo = rulePointEntity; /**保存当前测点信息  */

              if (rulePointEntity.dataType.code === 'DI') {
                preValues[index].formulaType = FormulaType.Status;
                preValues[index].sign = SignType.In;
              }

              clearValueData(preValues[index]);
            }
          });
        }
      });
    },
    [clearValueData, entities, monitoringPoint?.unit, onDIRuleFormulaTypeChange, transforDeviceType]
  );

  return (
    <Form form={form} onValuesChange={onChangeValues}>
      <Form.List name={name}>
        {(fields, { add, remove }) => (
          <>
            {fields.map((field, index) => {
              return (
                <Space key={field.key} align="baseline">
                  <Form.Item
                    noStyle
                    shouldUpdate={(prevValues, curValues) => {
                      return prevValues[name][index] !== curValues[name][index];
                    }}
                  >
                    {() => {
                      const fieldMode = getRowFieldMode(index);
                      const fieldNames = getFieldNames(fieldMode);
                      const currentFields = form.getFieldValue(name)[index];
                      const firstValue = currentFields ? currentFields[fieldNames[0]] : '';
                      // const secondValue = currentFields ? currentFields[fieldNames[1]] : '';
                      const thirdValue = currentFields ? currentFields[fieldNames[2]] : '';
                      /**前置条件为选择的测点, 告警条件为主测点 */
                      const selectedPoint =
                        currentFields.pointCode !== '' && currentFields.pointCode
                          ? currentFields.pointInfo
                            ? currentFields.pointInfo
                            : entities[`${currentFields.deviceType}.${currentFields.pointCode}`]
                          : undefined;

                      return (
                        <Space align="start">
                          {/* 第一列 sign、formuteType、dimension */}
                          {fieldNames[0] && (
                            <Form.Item
                              {...field}
                              name={[field.name, fieldNames[0] ?? 'sign']}
                              rules={[{ required: true, message: '不能为空' }]}
                            >
                              <Select
                                style={{ width: 100 }}
                                options={getFirstOptions(fieldNames[0])}
                              />
                            </Form.Item>
                          )}

                          {/* 第二列 */}
                          <Form.Item
                            noStyle
                            shouldUpdate={(prevValues, curValues) => {
                              return prevValues[name][index] !== curValues[name][index];
                            }}
                          >
                            {() => {
                              if (fieldNames[1] !== 'pointCode') {
                                if (firstValue !== SignType.Change) {
                                  return (
                                    <Form.Item
                                      {...field}
                                      name={[field.name, fieldNames[1] ?? 'formulaType']}
                                      rules={[{ required: true, message: '不能为空' }]}
                                    >
                                      <Select
                                        style={{ width: 120 }}
                                        options={getSecondOptions(fieldNames[0] ?? 'formulaType')}
                                      />
                                    </Form.Item>
                                  );
                                }
                                return;
                              } else {
                                return (
                                  <Form.Item
                                    {...field}
                                    name={[field.name, fieldNames[1] ?? 'pointCode']}
                                    rules={[{ required: true, message: '不能为空' }]}
                                  >
                                    <ConditionPointSelect
                                      ref={preConditionPointSelectRef}
                                      dimension={firstValue ?? DimensionType.Device}
                                      deviceType={currentFields.deviceType}
                                      spaceGuid={getSpaceGuidByDimension(
                                        firstValue,
                                        monitoringPoint?.spaceGuid!
                                      )}
                                      pointParams={pointParams}
                                      filterExtPoint={!monitoringPoint?.extCount} /**过滤扩展点位 */
                                    />
                                  </Form.Item>
                                );
                              }
                            }}
                          </Form.Item>

                          {/* 第三列 告警：valueData 前置：sign */}
                          <Form.Item
                            noStyle
                            shouldUpdate={(prevValues, curValues) => {
                              return prevValues[name][index] !== curValues[name][index];
                            }}
                          >
                            {() => {
                              if (fieldNames[2] === 'valueData') {
                                return (
                                  <Form.Item
                                    {...field}
                                    name={[field.name, 'valueData']}
                                    rules={[
                                      { required: true, message: '不能为空' },
                                      {
                                        validator: (_, val) => {
                                          if (!currentFields.formulaType) {
                                            return Promise.resolve();
                                          }
                                          return _onValidatorValueData(
                                            val,
                                            currentFields.formulaType
                                          );
                                        },
                                      },
                                    ]}
                                  >
                                    <ValueDataInput
                                      form={form}
                                      currentFields={currentFields}
                                      monitoringPoint={monitoringPoint}
                                      pointParams={pointParams}
                                    />
                                  </Form.Item>
                                );
                              } else {
                                return (
                                  <Form.Item
                                    {...field}
                                    name={[field.name, fieldNames[2] ?? 'sign']}
                                    rules={[{ required: true, message: '不能为空' }]}
                                  >
                                    <Select
                                      style={{ width: 120 }}
                                      options={getPreTriggerRuleSignOptions({
                                        pointType: selectedPoint
                                          ? (selectedPoint.dataType.code as unknown)
                                          : 'AI',
                                      })}
                                    />
                                  </Form.Item>
                                );
                              }
                            }}
                          </Form.Item>

                          {/* 第四列 前置：表达式*/}
                          <Form.Item
                            noStyle
                            shouldUpdate={(prevValues, curValues) => {
                              return prevValues[name][index] !== curValues[name][index];
                            }}
                          >
                            {() => {
                              if (
                                fieldNames[3] &&
                                firstValue === DimensionType.Device &&
                                thirdValue === SignType.In
                              ) {
                                /**同设备 DI测点 状态为固定 不显示 */
                                return null;
                              } else if (fieldNames[3]) {
                                return (
                                  <Form.Item
                                    {...field}
                                    name={[field.name, fieldNames[3] ?? 'formulaType']}
                                    rules={[{ required: true, message: '不能为空' }]}
                                  >
                                    <Select
                                      style={{ width: 100 }}
                                      options={getPreTriggerRuleFormalTypeOptions(
                                        currentFields.dimension
                                      )}
                                    />
                                  </Form.Item>
                                );
                              }
                              return;
                            }}
                          </Form.Item>
                          {/* 第五列 前置： valueData*/}
                          <Form.Item
                            noStyle
                            shouldUpdate={(prevValues, curValues) => {
                              return prevValues[name][index] !== curValues[name][index];
                            }}
                          >
                            {() => {
                              if (fieldNames[4] && currentFields.formulaType) {
                                return (
                                  <Form.Item
                                    {...field}
                                    name={[field.name, fieldNames[4] ?? 'valueData']}
                                    rules={[
                                      {
                                        required: true,
                                        message: '不能为空',
                                      },
                                    ]}
                                  >
                                    <ValueDataInput
                                      form={form}
                                      currentFields={currentFields}
                                      monitoringPoint={selectedPoint}
                                      spaceGuid={getSpaceGuidByDimension(
                                        firstValue,
                                        monitoringPoint?.spaceGuid!
                                      )}
                                      pointParams={pointParams}
                                    />
                                  </Form.Item>
                                );
                              }
                              return;
                            }}
                          </Form.Item>
                        </Space>
                      );
                    }}
                  </Form.Item>
                  {((name === 'triggerRules' && form.getFieldValue([name]).length > 1) ||
                    name === 'preTriggerRules') && (
                    <CloseOutlined
                      onClick={() => {
                        const values: unknown[] = form.getFieldValue([name]);
                        const newValues = values.filter((_, idx) => idx !== index);
                        if (name === 'triggerRules') {
                          onDIRuleFormulaTypeChange &&
                            onDIRuleFormulaTypeChange(
                              !newValues.every(value => value.formulaType === FormulaType.Change)
                            );
                        }
                        form.setFieldsValue({ [name]: newValues });
                      }}
                    />
                  )}
                </Space>
              );
            })}

            <Form.Item style={{ marginBottom: 0 }}>
              <Button
                type="text"
                icon={<PlusSquareOutlined />}
                onClick={() => {
                  if (!monitoringPoint?.code) {
                    message.warning('请选择监控测点');
                    return;
                  }

                  if (form.getFieldValue([name]).length === 10) {
                    message.warning('最多支持添加10条数据');
                    return;
                  }
                  const defaultValues = {
                    ...InitalTriggerRuleValues[SettingMode],
                    dimension: pointDimension,
                    unit: monitoringPoint?.unit,
                    deviceType: monitoringPoint?.deviceType,
                    type:
                      name === 'preTriggerRules'
                        ? TriggerRuleType.PreCondition
                        : TriggerRuleType.AlarmCondition,
                    pointCode: name === 'triggerRules' ? monitoringPoint?.code : undefined,
                    pointName: name === 'triggerRules' ? monitoringPoint?.name : undefined,
                  };

                  if (name === 'triggerRules') {
                    onDIRuleFormulaTypeChange && onDIRuleFormulaTypeChange(true);
                  }
                  const values = form.getFieldValue([name])
                    ? [...form.getFieldValue([name]), { ...defaultValues }]
                    : [{ ...defaultValues }];

                  form.setFieldsValue({ [name]: values });
                }}
              >
                添加条件
              </Button>
            </Form.Item>
          </>
        )}
      </Form.List>
    </Form>
  );
});

AlarmMonitoringItemTriggerRuleSetting.displayName = 'AlarmMonitoringItemTriggerRuleSetting';
