import QuestionCircleOutlined from '@ant-design/icons/es/icons/QuestionCircleOutlined';
import React, { useCallback } from 'react';

import type { FormInstance } from '@manyun/base-ui.ui.form';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import type { MonitoringItemJSON } from '@manyun/monitoring.model.monitoring-item';
import type { Point } from '@manyun/monitoring.model.point';
import type { TriggerRuleJSON } from '@manyun/monitoring.model.trigger-rule';
import {
  DimensionType,
  FormulaType,
  IntervalSplitSymbol,
  OrSplitSymbol,
  SignType,
  SignTypeMapper,
} from '@manyun/monitoring.model.trigger-rule';
import { DiPointStateSelect } from '@manyun/resource-hub.ui.di-point-state-select';
import { SpecSelect } from '@manyun/resource-hub.ui.spec-select';

import { ConditionPointSelect } from './condition-point-select';
import { useGetPointConditions, useGetSpecParams } from './hook';
import { parseTriggerRuleValueDataForDIPoint } from './utils';

export const POINT_VALUE_LIMIT = {
  MAX: 999999,
};

export type AlarmMonitoringItemTriggerRuleSettingProps = {
  name: 'triggerRules' | 'preTriggerRules';
  form: FormInstance<MonitoringItemJSON>;
};

export type ValueDataInputValue = {
  code: string;
  name: string;
};

export type ValueDataInputProps = {
  monitoringPoint: Point | undefined;
  currentFields: TriggerRuleJSON | undefined;
  form: FormInstance<MonitoringItemJSON>;
  spaceGuid?: string;
  value?: ValueDataInputValue;
  showTooltip?: boolean;
  pointParams?: { isQueryNon: boolean; blockGuid: string };
  onChange?: (value?: ValueDataInputValue) => void;
};

export const ValueDataInput = ({
  /** 当前triggerRule字段**/ currentFields,
  spaceGuid,
  value,
  onChange,
  /**测点信息：告警同主测点、前置第二列选择框测点 */ monitoringPoint,
  showTooltip = true,
  pointParams,
}: ValueDataInputProps) => {
  const { target, ...searchPointParams } = useGetPointConditions(
    monitoringPoint?.deviceType ?? currentFields?.deviceType
  );

  const specsParms = useGetSpecParams({
    dimension: (currentFields?.dimension as DimensionType) ?? DimensionType.Device,
    deviceType: monitoringPoint?.deviceType ?? target ?? '',
    specUnit: monitoringPoint?.unit ?? undefined,
  });

  /** @type {showALL} 是否显示ALL选项*/
  const _getDIPointValidLimits = useCallback(
    (showALL?: boolean) => {
      if (showALL) {
        return ['ALL=ALL', ...(monitoringPoint?.validLimits ?? [])];
      }
      return [...(monitoringPoint?.validLimits ?? [])];
    },
    [monitoringPoint?.validLimits]
  );

  /** @type {values} 选中的状态值 */
  const _getDIPointDisabledLimits = useCallback(
    (values: string[]) => {
      return values.includes('ALL') ? monitoringPoint?.validLimits : undefined;
    },
    [monitoringPoint?.validLimits]
  );

  const _onChange = useCallback(
    (code?: string, name?: string) => {
      onChange?.(
        code &&
          name &&
          code !== '' &&
          name !== '' &&
          code !== `${IntervalSplitSymbol}` &&
          name !== `${IntervalSplitSymbol}`
          ? {
              code: code,
              name: name,
            }
          : undefined
      );
    },
    [onChange]
  );

  const _onDIStateChange = useCallback(
    ({
      selectIndex,
      preOptions,
      nextOptions,
    }: {
      selectIndex?: 'before' | 'after';
      nextOptions: { label: string; value: string }[];
      preOptions?: unknown[];
    }) => {
      let codeStr = '',
        nameStr = '';
      if (selectIndex) {
        if (preOptions && (preOptions ?? []).length === 2) {
          const beforeOptions = selectIndex === 'before' ? nextOptions : preOptions[0];
          const afterOptions = selectIndex === 'before' ? preOptions[1] : nextOptions;
          codeStr =
            beforeOptions.map((opt: { value: string }) => opt.value).join(OrSplitSymbol) +
            IntervalSplitSymbol +
            afterOptions.map((opt: { value: string }) => opt.value).join(OrSplitSymbol);
          nameStr =
            beforeOptions.map((opt: { label: string }) => opt.label).join(OrSplitSymbol) +
            IntervalSplitSymbol +
            afterOptions.map((opt: { label: string }) => opt.label).join(OrSplitSymbol);
        }
      } else {
        codeStr = nextOptions.map(opt => opt.value).join(OrSplitSymbol);
        nameStr = nextOptions.map(opt => opt.label).join(OrSplitSymbol);
      }

      _onChange(codeStr, nameStr);
    },
    [_onChange]
  );

  /**获取 自定义输入框 最大值*/
  const _getValidLimitMax = useCallback(() => {
    if (!monitoringPoint?.validLimits) {
      return POINT_VALUE_LIMIT.MAX;
    }
    const digitalDescJson: Record<string, string> = {};
    monitoringPoint?.validLimits.forEach(limit => {
      const arr = limit.split('=');
      digitalDescJson[arr[0]] = arr[1];
    });
    if (digitalDescJson.le) {
      return Number(digitalDescJson.le);
    }
    return POINT_VALUE_LIMIT.MAX;
  }, [monitoringPoint?.validLimits]);

  if (!currentFields || !monitoringPoint) {
    return null;
  }

  switch (currentFields.formulaType) {
    case FormulaType.Continuous:
      const isPersentage = [SignType.IncreaseThan, SignType.DecreaseThan].includes(
        currentFields.sign
      );
      const tooltipText = `${SignTypeMapper[currentFields.sign]} ${value?.name ?? 'X'} ${
        isPersentage ? '%' : ''
      }`;
      return (
        <Space>
          <InputNumber
            value={Number(value?.name)}
            precision={2}
            style={{ width: 140 }}
            addonAfter={currentFields.unit}
            max={isPersentage ? 100 : undefined}
            onChange={value => {
              _onChange(
                isPersentage ? (Number(value) / 100).toFixed(2) : value.toString(),
                value.toString()
              );
            }}
          />
          {showTooltip && (
            <RenderTooltip
              title={`当监控测点连续多少次(通过 “触发观察周期” 设定) ${tooltipText}时，则触发告警`}
            />
          )}
        </Space>
      );
    case FormulaType.Custom:
      return (
        <InputNumber
          precision={2}
          value={Number(value?.code)}
          addonAfter={currentFields.unit}
          style={{ width: 140 }}
          max={_getValidLimitMax()}
          onChange={value => {
            _onChange(
              value !== null && value !== undefined ? value.toString() : undefined,
              value !== null && value !== undefined ? value.toString() : undefined
            );
          }}
        />
      );
    case FormulaType.Point:
      /**该设备类型下其他AI点位信息，包含该设备的原始点位、加工点位、聚合点位 、自定义  PointSelect */
      return (
        <ConditionPointSelect
          pointParams={pointParams}
          labelInValue
          style={{ width: 200 }}
          value={{ value: value?.code, label: value?.name }}
          dimension={currentFields.dimension as DimensionType}
          deviceType={searchPointParams.deviceType}
          spaceGuid={spaceGuid ?? monitoringPoint?.spaceGuid ?? undefined}
          dataTypeList={['AI']}
          pointTypeList={['ORI', 'CAL_SPACE', 'AGG_SPACE', 'CAL_DEVICE', 'AGG_DEVICE', 'CUSTOM']}
          disabledPoint={currentFields.pointCode ? [currentFields.pointCode] : undefined}
          onChange={(value: { value: unknown; label: unknown }) => {
            _onChange(value.value, value.label);
          }}
        />
      );
    case FormulaType.Rated:
      /**该设备类型下单位相同、必填的数值类型的规格 SpecSelct */
      return (
        <SpecSelect
          labelInValue
          style={{ width: 240 }}
          value={{ value: value?.code, label: value?.name }}
          onChange={value => {
            _onChange(value.value, value.label);
          }}
          {...specsParms}
          required={false}
          specUnit={undefined}
        />
      );
    case FormulaType.Change /**变位告警 */:
      const defaultVaule = { code: `${IntervalSplitSymbol}`, name: `${IntervalSplitSymbol}` };
      const changeOptions: unknown[] = parseTriggerRuleValueDataForDIPoint(
        value?.code ? (value ?? defaultVaule) : defaultVaule
      ); /**二维数组  */
      return (
        <Space align="center">
          从
          <DiPointStateSelect
            labelInValue
            mode="multiple"
            maxTagCount={2}
            validLimits={_getDIPointValidLimits(true)}
            disabledLimits={_getDIPointDisabledLimits(
              changeOptions[0].map((opt: { value: string }) => opt.value)
            )}
            style={{ width: 200 }}
            value={changeOptions[0]}
            onChange={value => {
              let _filterValues = value;
              /**若选中了ALL，清除其他状态 */
              if ((value ?? []).map((val: { value: string }) => val.value).includes('ALL')) {
                _filterValues = [{ label: 'ALL', value: 'ALL' }];
              }
              _onDIStateChange({
                selectIndex: 'before',
                nextOptions: _filterValues,
                preOptions: changeOptions,
              });
            }}
          />
          变为
          <DiPointStateSelect
            labelInValue
            mode="multiple"
            maxTagCount={2}
            validLimits={_getDIPointValidLimits(true)}
            disabledLimits={_getDIPointDisabledLimits(
              changeOptions[1].map((opt: { value: string }) => opt.value)
            )}
            value={changeOptions[1]}
            style={{ width: 200 }}
            onChange={value => {
              let _filterValues = value;
              /**若选中了ALL，清除其他状态 */
              if ((value ?? []).map((val: { value: string }) => val.value).includes('ALL')) {
                _filterValues = [{ label: 'ALL', value: 'ALL' }];
              }
              _onDIStateChange({
                selectIndex: 'after',
                nextOptions: _filterValues,
                preOptions: changeOptions,
              });
            }}
          />
          {showTooltip && (
            <RenderTooltip title="若变位告警未选择状态,则默认任何状态之间变位均触发告警" />
          )}
        </Space>
      );
    case FormulaType.Status /**异常告警 */:
      const defaultVal = { code: '', name: '' };
      const options: unknown[] = parseTriggerRuleValueDataForDIPoint(
        value?.code ? (value ?? defaultVal) : defaultVal
      );
      return (
        <DiPointStateSelect
          labelInValue
          mode="multiple"
          maxTagCount={2}
          validLimits={_getDIPointValidLimits()}
          style={{ width: 200 }}
          value={options}
          onChange={value => {
            _onDIStateChange({ nextOptions: value });
          }}
        />
      );
    default:
      return <></>;
  }
};

const RenderTooltip = ({ title }: { title: string }) => {
  return (
    <Tooltip title={title}>
      <QuestionCircleOutlined />
    </Tooltip>
  );
};
