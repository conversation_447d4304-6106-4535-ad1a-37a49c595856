import type { DataType } from '@manyun/monitoring.model.point';
import {
  DimensionType,
  DimensionTypeMapper,
  FormulaType,
  FormulaTypeMapper,
  IntervalSplitSymbol,
  OrSplitSymbol,
  PreInSignTypeLabel,
  SignType,
  SignTypeMapper,
  TriggerRuleJSON,
  TriggerRuleType,
} from '@manyun/monitoring.model.trigger-rule';

export type Option = {
  label: string;
  value: string;
  type: 'sign' | 'formulaType' | 'dimension' | 'AI' | 'DI';
};

/** 告警条件 AI测点 第一列选项  */
export const AIPointTriggerRuleFirstOptions: Option[] = [
  {
    label: SignTypeMapper[SignType.GreaterThanEqual],
    value: SignType.GreaterThanEqual,
    type: 'sign',
  },
  {
    label: SignTypeMapper[SignType.LessThanEqual],
    value: SignType.LessThanEqual,
    type: 'sign',
  },
  {
    label: SignTypeMapper[SignType.GreaterThan],
    value: SignType.GreaterThan,
    type: 'sign',
  },
  {
    label: SignTypeMapper[SignType.LessThan],
    value: SignType.LessThan,
    type: 'sign',
  },
  {
    label: SignTypeMapper[SignType.Equal],
    value: SignType.Equal,
    type: 'sign',
  },
  {
    label: FormulaTypeMapper[FormulaType.Continuous],
    value: FormulaType.Continuous,
    type: 'formulaType',
  },
];

/** 告警条件 AI测点 第二列选项 */
export const AIPointTriggerRuleSecondOptions: Option[] = [
  {
    label: FormulaTypeMapper[FormulaType.Custom],
    value: FormulaType.Custom,
    type: 'formulaType',
  },
  {
    label: FormulaTypeMapper[FormulaType.Rated],
    value: FormulaType.Rated,
    type: 'formulaType',
  },
  {
    label: FormulaTypeMapper[FormulaType.Point],
    value: FormulaType.Point,
    type: 'formulaType',
  },
  {
    label: SignTypeMapper[SignType.IncreaseThan],
    value: SignType.IncreaseThan,
    type: 'sign',
  },
  {
    label: SignTypeMapper[SignType.DecreaseThan],
    value: SignType.DecreaseThan,
    type: 'sign',
  },
  {
    label: SignTypeMapper[SignType.RiseThan],
    value: SignType.RiseThan,
    type: 'sign',
  },
  {
    label: SignTypeMapper[SignType.ReduceThan],
    value: SignType.ReduceThan,
    type: 'sign',
  },
];

/** 告警条件 DI 测点 第一列选项 **/
export const DIPointTriggerRuleFirstOptions: Option[] = [
  {
    label: FormulaTypeMapper[FormulaType.Status],
    value: FormulaType.Status,
    type: 'formulaType',
  },
  {
    label: FormulaTypeMapper[FormulaType.Change],
    value: FormulaType.Change,
    type: 'formulaType',
  },
];
/** 告警条件 DI 测点 Sign表达式 **/
export const DIPointTriggerRuleSecondOptions: Option[] = [
  {
    label: SignTypeMapper[SignType.In],
    value: SignType.In,
    type: 'sign',
  },
];
/** 前置条件  第一列选项 **/
export const PreConditionTriggerRuleFirstOptions: Option[] = [
  {
    label: DimensionTypeMapper[DimensionType.Device],
    value: DimensionType.Device,
    type: 'dimension',
  },
  {
    label: DimensionTypeMapper[DimensionType.Grid],
    value: DimensionType.Grid,
    type: 'dimension',
  },
  {
    label: DimensionTypeMapper[DimensionType.Cloumn],
    value: DimensionType.Cloumn,
    type: 'dimension',
  },
  {
    label: DimensionTypeMapper[DimensionType.Room],
    value: DimensionType.Room,
    type: 'dimension',
  },
  {
    label: DimensionTypeMapper[DimensionType.Block],
    value: DimensionType.Block,
    type: 'dimension',
  },
  {
    label: DimensionTypeMapper[DimensionType.Idc],
    value: DimensionType.Idc,
    type: 'dimension',
  },
];

/** 前置条件 DI 测点 Sign表达式 **/
export const DIPointPreConditionTriggerRuleSecondOptions: Option[] = [
  {
    label: PreInSignTypeLabel,
    value: SignType.In,
    type: 'sign',
  },
];

/**前置条件 表达式选择 */
export const PreConditionTriggerRuleFormulaTypeOptions: Option[] = [
  {
    label: FormulaTypeMapper[FormulaType.Custom],
    value: FormulaType.Custom,
    type: 'AI',
  },
  {
    label: FormulaTypeMapper[FormulaType.Rated],
    value: FormulaType.Rated,
    type: 'AI',
  },
  {
    label: FormulaTypeMapper[FormulaType.Point],
    value: FormulaType.Point,
    type: 'AI',
  },
];

export const AlarmTriggerRuleSettingOptionsMapper: Record<string, any> = {
  [TriggerRuleType.AlarmCondition]: {
    AI: {
      options: AIPointTriggerRuleFirstOptions,
    },
    DI: {
      options: DIPointTriggerRuleFirstOptions,
    },
  },
  [TriggerRuleType.PreCondition]: {
    options: PreConditionTriggerRuleFirstOptions,
  },
};

/**新建默认值 */
export const InitalTriggerRuleValues: Record<string, Partial<TriggerRuleJSON>> = {
  AI: {
    sign: SignType.GreaterThanEqual,
    formulaType: FormulaType.Custom,
    dimension: DimensionType.Device,
  },
  DI: {
    sign: SignType.In,
    formulaType: FormulaType.Status,
    dimension: DimensionType.Device,
  },
  PRE: {
    dimension: DimensionType.Device,
  },
};

export function getSettingFirstOptions({
  triggerRule,
  dataType = 'AI',
}: {
  triggerRule: TriggerRuleType;
  dataType?: DataType;
}) {
  let options: Option[] = [];
  options =
    triggerRule === TriggerRuleType.AlarmCondition
      ? AlarmTriggerRuleSettingOptionsMapper[triggerRule][dataType].options
      : AlarmTriggerRuleSettingOptionsMapper[triggerRule].options;
  return options;
}

/**
 * 获取告警条件第二列选项
 * @param {fieldType|dataType}: 第一项类型｜测点类型(AI|DI)
 * @returns
 */
export function getSettingSecondOptions({
  fieldType,
  dataType = 'AI',
}: {
  fieldType: string;
  dataType?: DataType;
}) {
  let options: Option[] = [];
  switch (fieldType) {
    case 'sign':
      options = AIPointTriggerRuleSecondOptions.filter(opt => opt.type === 'formulaType');
      break;
    case 'formulaType':
      options =
        dataType === 'AI'
          ? AIPointTriggerRuleSecondOptions.filter(opt => opt.type === 'sign')
          : DIPointTriggerRuleSecondOptions;
      break;
    case 'dimension':
      break;
  }
  return options;
}

/**
 *  获取前置条件的符号选项
 * @param {pointType: 'AI' | 'DI'}
 */
export const getPreTriggerRuleSignOptions = ({
  pointType,
  dimension,
}: {
  pointType: 'AI' | 'DI';
  dimension?: DimensionType;
}) => {
  let options: Option[] = [];
  switch (pointType) {
    case 'AI':
      options = AIPointTriggerRuleFirstOptions.filter(opt => opt.type === 'sign');
      break;
    case 'DI':
      options = DIPointPreConditionTriggerRuleSecondOptions;
      break;
  }
  return options;
};

/**
 * 获取前置条件的表达式选项
 * @returns {options}
 */
export const getPreTriggerRuleFormalTypeOptions = (dimension: DimensionType) => {
  if ([DimensionType.Block, DimensionType.Idc].includes(dimension)) {
    return PreConditionTriggerRuleFormulaTypeOptions.map(opt =>
      opt.value === FormulaType.Rated ? { ...opt, disabled: true } : opt
    );
  }
  return PreConditionTriggerRuleFormulaTypeOptions;
};

/**
 * eg: 变位告警 {"code":"ALL;1,0","name":"ALL;关闭,闭合"}  返回二维数组
 * eg: 异常告警 {"code":"0,1","name":"断开,闭合"} 返回一维数组
 * @export
 * @param {{
 *   code: string;
 *   name: string;
 * }} valueData
 * @return {*}  {[{label:'ALL',value:'ALL'}] | [[{label:'ALL',value:'ALL'}],[{label:'1',value:'关闭'}]]}
 */
export function parseTriggerRuleValueDataForDIPoint(valueData: {
  code: string;
  name: string;
}): any[] {
  let options: any[] = [];
  const changeValue = valueData.code.split(IntervalSplitSymbol);
  const changeName = valueData.name.split(IntervalSplitSymbol);

  if (changeValue.length === 2) {
    const beforeChageVal = changeValue[0];
    const beforeChageName = changeName[0];
    const beforeOptions = getOptions(beforeChageVal, beforeChageName);

    const afterChangeVal = changeValue[1];
    const afterChangeName = changeName[1];
    const afterOptions = getOptions(afterChangeVal, afterChangeName);

    options[0] = beforeOptions;
    options[1] = afterOptions;
  } else {
    options = getOptions(valueData.code ?? '', valueData.name ?? '');
  }
  return options;
}

/**
 *
 * @param codeStr
 * @param nameStr
 * @returns {label: string,value:string}[]
 */
function getOptions(codeStr: string, nameStr: string) {
  let options: { label: string; value: string }[] = [];
  const codes = codeStr.split(OrSplitSymbol);
  const names = nameStr.split(OrSplitSymbol);
  codes.forEach((val, index) => {
    if (names[index]) {
      options.push({
        label: names[index],
        value: val,
      });
    }
  });
  return options;
}

/**
 *
 * eg: 维度为同机房, 测点spaceGuid: ECO6.A.A1 则查询测点的spaceGuid为：EC06
 * @export
 * @param {DimensionType} dimension 前置告警 维度
 * @param {(string | undefined)} spaceGuid 测点的spaceGuid
 * @return {*}  查询测点的spaceGuid
 */
export function getSpaceGuidByDimension(dimension: DimensionType, spaceGuid: string | undefined) {
  if (!spaceGuid) {
    return undefined;
  }
  const spaceGuidArr = spaceGuid.split('.');
  let newSpaceGuid = spaceGuid;
  switch (dimension) {
    case DimensionType.Block:
      newSpaceGuid = `${spaceGuidArr[0]}.${spaceGuidArr[1]}`;
      break;
    case DimensionType.Idc:
      newSpaceGuid = spaceGuidArr[0];
      break;
    default:
      break;
  }
  return newSpaceGuid;
}
