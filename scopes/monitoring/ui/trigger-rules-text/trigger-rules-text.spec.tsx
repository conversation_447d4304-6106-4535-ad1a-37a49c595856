import React from 'react';

import { render } from '@testing-library/react';

import { BasicTriggerRulesText } from './trigger-rules-text.composition';

it('should render with the correct text', () => {
  const { getByLabelText } = render(<BasicTriggerRulesText />);
  // eslint-disable-next-line testing-library/prefer-screen-queries
  const rendered = getByLabelText('前置条件');
  expect(rendered).toBeTruthy();
});
