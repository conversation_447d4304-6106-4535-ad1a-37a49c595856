import React from 'react';

import {
  DimensionType,
  FormulaType,
  SignType,
  TriggerRuleType,
} from '@manyun/monitoring.model.trigger-rule';

import { TriggerRulesText } from './trigger-rules-text';

export const BasicTriggerRulesText = () => {
  return (
    <div>
      <span>前置条件:</span>
      <TriggerRulesText
        type={TriggerRuleType.PreCondition}
        triggerRules={[
          {
            id: 1,
            type: TriggerRuleType.PreCondition,
            deviceType: '90010',
            pointCode: '100010',
            pointName: '测点名称',
            formulaType: FormulaType.Change,
            sign: SignType.DecreaseThan,
            dimension: DimensionType.Block,
            valueData: {
              code: '1',
              name: '1',
            },
          },
        ]}
      />
    </div>
  );
};

export const BasicTriggerRules2Text = () => {
  return (
    <div>
      <span>告警条件:</span>
      <TriggerRulesText
        type={TriggerRuleType.AlarmCondition}
        triggerRules={[
          {
            id: 1,
            type: TriggerRuleType.AlarmCondition,
            deviceType: '90010',
            pointCode: '100010',
            pointName: '测点名称',
            formulaType: FormulaType.Change,
            sign: SignType.DecreaseThan,
            dimension: DimensionType.Block,
            valueData: {
              code: '1',
              name: '1',
            },
          },
        ]}
      />
    </div>
  );
};
