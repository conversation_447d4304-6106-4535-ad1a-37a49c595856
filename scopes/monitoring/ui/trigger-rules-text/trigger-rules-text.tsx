import React, { useMemo } from 'react';

import {
  AndString,
  OrString,
  TriggerRule,
  TriggerRuleJSON,
} from '@manyun/monitoring.model.trigger-rule';
import { TriggerRuleType } from '@manyun/monitoring.model.trigger-rule';

export type TriggerRulesTextProps = {
  type: TriggerRuleType /**前置条件、告警条件 */;
  triggerRules: TriggerRuleJSON[];
};

export function TriggerRulesText({ type, triggerRules }: TriggerRulesTextProps) {
  const _rules = useMemo(() => {
    return triggerRules.filter(rule => rule.type === type);
  }, [triggerRules, type]);

  const _joinSymbol = useMemo(
    () => (type === TriggerRuleType.PreCondition ? AndString : OrString),
    [type]
  );

  return (
    <>
      {_rules.length ? _rules.map(rule => TriggerRule.toJSONString(rule)).join(_joinSymbol) : '--'}
    </>
  );
}
