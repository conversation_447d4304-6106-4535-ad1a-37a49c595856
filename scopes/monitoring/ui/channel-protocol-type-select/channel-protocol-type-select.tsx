import React, { useEffect, useState } from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import type { SelectProps } from '@manyun/base-ui.ui.select';

import { fetchChannelProtocolType } from '@manyun/monitoring.service.fetch-channel-protocol-type';

export type ChannelProtocolTypeSelectProps = Omit<SelectProps, 'options'>;

export const ChannelProtocolTypeSelect = (props: SelectProps) => {
  const [options, setOptions] = useState<SelectProps['options']>([]);
  useEffect(function () {
    (async () => {
      const { error, data } = await fetchChannelProtocolType();
      if (error) {
        message.error(error.message);
        return;
      }
      const list = Object.keys(data).map(item => {
        return { value: item, label: data[item] };
      });
      setOptions(list);
    })();
  }, []);

  return <Select options={options} {...props} />;
};
