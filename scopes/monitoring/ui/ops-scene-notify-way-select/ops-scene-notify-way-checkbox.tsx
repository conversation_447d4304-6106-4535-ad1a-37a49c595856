import type { Ref } from 'react';
import React from 'react';

import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import type { CheckboxGroupProps } from '@manyun/base-ui.ui.checkbox';

import type { DynamicBaselineNotifyWay } from '@manyun/monitoring.model.dynamic-baseline-setting';
import { getDynamicBaselineLocales } from '@manyun/monitoring.model.dynamic-baseline-setting';

import { getOpsSceneNotifyWayOptions } from './ops-scene-notify-way-common';

export type OpsSceneNotifyWayCheckboxProps = {
  disabledOptions?: DynamicBaselineNotifyWay[];
} & Omit<CheckboxGroupProps, 'options' | 'ref'>;

/**
 * 动态配置通报方式
 */
export const OpsSceneNotifyWayCheckbox = React.forwardRef(
  (
    { disabledOptions, ...props }: OpsSceneNotifyWayCheckboxProps,
    ref: Ref<HTMLDivElement> | undefined
  ) => {
    const locales = getDynamicBaselineLocales();

    return (
      <Checkbox.Group
        {...props}
        ref={ref}
        options={getOpsSceneNotifyWayOptions({ locales, disabledOptions })}
      />
    );
  }
);

OpsSceneNotifyWayCheckbox.displayName = 'OpsSceneNotifyWayCheckbox';
