import type {
  DynamicBaselineLocales,
  DynamicBaselineNotifyWay,
} from '@manyun/monitoring.model.dynamic-baseline-setting';

export const getOpsSceneNotifyWayOptions = ({
  locales,
  disabledOptions,
}: {
  locales: DynamicBaselineLocales;
  disabledOptions?: DynamicBaselineNotifyWay[];
}): {
  label: string;
  value: DynamicBaselineNotifyWay;
  disabled?: boolean;
}[] => {
  return [
    {
      label: locales['alarmScreen'],
      value: 'ALARM_SCREEN',
      disabled: disabledOptions?.includes('ALARM_SCREEN'),
    },
    {
      label: locales['sms'],
      value: 'SMS',
      disabled: disabledOptions?.includes('SMS'),
    },
  ];
};
