import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

import { getDynamicBaselineLocales } from '@manyun/monitoring.model.dynamic-baseline-setting';

import { getOpsSceneNotifyWayOptions } from './ops-scene-notify-way-common';

export type OpsSceneNotifyWaySelectProps = Omit<SelectProps, 'options' | 'ref'>;

/**
 * 动态配置通报方式
 */
export const OpsSceneNotifyWaySelect = React.forwardRef(
  (props: OpsSceneNotifyWaySelectProps, ref: React.Ref<RefSelectProps>) => {
    const locales = getDynamicBaselineLocales();

    return (
      <Select
        optionFilterProp="label"
        {...props}
        ref={ref}
        options={getOpsSceneNotifyWayOptions({ locales })}
      />
    );
  }
);

OpsSceneNotifyWaySelect.displayName = 'OpsSceneNotifyWaySelect';
