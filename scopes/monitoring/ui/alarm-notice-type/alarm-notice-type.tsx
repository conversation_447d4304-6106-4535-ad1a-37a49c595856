import React, { useMemo } from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { SelectProps } from '@manyun/base-ui.ui.select';
import type { BackendAlarmNotificationConfigureType } from '@manyun/monitoring.model.notification-configure-item';
import { ALARM_NOTIFICATION_CONFIGURE_TYPE_MAPPER } from '@manyun/monitoring.model.notification-configure-item';

type RefSelectProps = {
  focus: () => void;
  blur: () => void;
};

export type AlarmNoticeTypeSelectProps = {
  disabledOptions?: BackendAlarmNotificationConfigureType[];
} & Omit<SelectProps, 'options'>;
const OPTIONS: {
  label: string;
  value: BackendAlarmNotificationConfigureType;
  disabled?: boolean;
}[] = [
  {
    value: 'ALARM_NOTIFY',
    label: ALARM_NOTIFICATION_CONFIGURE_TYPE_MAPPER.ALARM_NOTIFY,
  },
  {
    value: 'RECOVER_NOTIFY',
    label: ALARM_NOTIFICATION_CONFIGURE_TYPE_MAPPER.RECOVER_NOTIFY,
  },
  {
    value: 'RESPONSE_NOTIFY',
    label: ALARM_NOTIFICATION_CONFIGURE_TYPE_MAPPER.RESPONSE_NOTIFY,
  },
  {
    value: 'RECOVERED_NOTIFY',
    label: ALARM_NOTIFICATION_CONFIGURE_TYPE_MAPPER.RECOVERED_NOTIFY,
  },
];
export const AlarmNoticeTypeSelect = React.forwardRef(
  ({ disabledOptions, ...props }: AlarmNoticeTypeSelectProps, ref?: React.Ref<RefSelectProps>) => {
    const _options = useMemo(() => {
      if (disabledOptions) {
        return OPTIONS.map(option => ({
          ...option,
          disabled: disabledOptions.includes(option.value),
        }));
      }
      return OPTIONS;
    }, [disabledOptions]);

    return <Select ref={ref as unknown} {...props} options={_options} />;
  }
);

AlarmNoticeTypeSelect.displayName = 'AlarmNoticeTypeSelect';

export const AlarmNoticeTypeText = ({ type }: { type?: BackendAlarmNotificationConfigureType }) => {
  return (
    <>
      {type
        ? ALARM_NOTIFICATION_CONFIGURE_TYPE_MAPPER[type]
          ? ALARM_NOTIFICATION_CONFIGURE_TYPE_MAPPER[type]
          : type
        : '--'}
    </>
  );
};
