import React from 'react';

import { Tag } from '@manyun/base-ui.ui.tag';

import { ChannelStatus, getChannelConfigLocales } from '@manyun/monitoring.model.channel-config';

const locales = getChannelConfigLocales();

export type ChannelStatusTextProps = {
  status: ChannelStatus['code'];
};

export function ChannelStatusText({ status }: ChannelStatusTextProps) {
  if (status === 'ON' || status === 'OFF') {
    return <Tag color={status === 'ON' ? 'green' : 'red'}>{locales['channelStatus'][status]}</Tag>;
  }

  return <span>未知</span>;
}
