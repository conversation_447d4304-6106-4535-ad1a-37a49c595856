import React from 'react';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import type { ColumnType, TableProps } from '@manyun/base-ui.ui.table';
import { Table } from '@manyun/base-ui.ui.table';

import type { ShieldScope } from '@manyun/monitoring.gql.client.monitoring';
import { RoomTypeText } from '@manyun/resource-hub.ui.room-type-text';

const alarmShieldScopeColumns: ColumnType<ShieldScope>[] = [
  {
    title: '设备',
    dataIndex: 'deviceName',
    fixed: 'left',
    render: (_, record) => {
      if (!record.deviceName && !record.deviceLabel) {
        return '--';
      }
      return `${record.deviceName ?? ''} ${record.deviceLabel ?? ''}`;
    },
  },
  {
    title: '包间',
    dataIndex: 'roomName',
    render: (_, record) => {
      if (!record.roomTag && !record.roomName) {
        return '--';
      }

      return `${record.roomTag ?? ''} ${record.roomName ?? ''}`;
    },
  },
  {
    title: '厂商型号',
    dataIndex: 'vendor',
    render: (_, record) => {
      if (record.productModel && record.vendorCode) {
        return `${record.vendorCode}-${record.productModel}`;
      }
      return '--';
    },
  },
  {
    title: '设备状态',
    dataIndex: 'deviceOperationStatus',
    render: (_, record) => {
      if (record.deviceOperationStatus === 'ON') {
        return '已启用';
      }
      if (record.deviceOperationStatus === 'OFF') {
        return '已禁用';
      }
      return '--';
    },
  },
  {
    title: '楼层',
    dataIndex: 'floor',
    render: (_, record) => {
      if (record.floorTag) {
        return record.floorTag;
      }
      return '--';
    },
  },
  {
    title: '包间类型',
    dataIndex: 'roomType',
    render: (_, record) => {
      if (record.roomType) {
        return <RoomTypeText code={record.roomType} />;
      }
      return '--';
    },
  },
];

export type ColumnDataIndex =
  | 'deviceName'
  | 'roomName'
  | 'vendor'
  | 'deviceOperationStatus'
  | 'floor'
  | 'roomType';

export type AlarmShieldScopeTableProps = {
  dataIndexs?: ColumnDataIndex[];
  operation?: ColumnType<ShieldScope>;
} & Omit<TableProps<ShieldScope>, 'columns'>;

export function AlarmShieldScopeTable({
  dataIndexs = ['deviceName', 'roomName', 'vendor', 'deviceOperationStatus'],
  operation,
  ...rest
}: AlarmShieldScopeTableProps) {
  const columns = useDeepCompareMemo(() => {
    const newColumns = dataIndexs
      .map(dataIndex => {
        return alarmShieldScopeColumns.find(item => item.dataIndex === dataIndex);
      })
      .filter((item): item is ColumnType<ShieldScope> => item !== undefined);

    if (operation) {
      return [...newColumns, operation];
    }
    return newColumns;
  }, [dataIndexs, operation]);

  return (
    <Table
      columns={columns}
      scroll={{ x: 'max-content' }}
      tableLayout="fixed"
      rowKey="guid"
      {...rest}
    />
  );
}
