import React from 'react';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import type { ColumnType, TableProps } from '@manyun/base-ui.ui.table';
import { Table } from '@manyun/base-ui.ui.table';

import type { ShieldPoint } from './index';

const shieldPointColumns: ColumnType<ShieldPoint>[] = [
  {
    title: '测点名称',
    dataIndex: 'pointName',
    fixed: 'left',
  },
  {
    title: '测点ID',
    dataIndex: 'pointCode',
  },
  {
    title: '设备',
    dataIndex: 'deviceName',
    render: (_, record) => {
      if (!record.deviceName && !record.deviceLabel) {
        return '--';
      }
      return `${record.deviceName ?? ''} ${record.deviceLabel ?? ''}`;
    },
  },
  {
    title: '包间',
    dataIndex: 'room',
    render: (_, record) => {
      if (record.roomTag && record.roomName) {
        return `${record.roomTag} ${record.roomName}`;
      }
      return '--';
    },
  },
];

export type ColumnDataIndex = 'pointName' | 'pointCode' | 'deviceName' | 'room';

export type ShieldPointTableProps = {
  dataIndexs?: ColumnDataIndex[];
  operation?: ColumnType<ShieldPoint>;
} & Omit<TableProps<ShieldPoint>, 'columns'>;

export function ShieldPointTable({
  dataIndexs = ['pointName', 'pointCode', 'deviceName', 'room'],
  operation,
  ...rest
}: ShieldPointTableProps) {
  const columns = useDeepCompareMemo(() => {
    const newColumns = dataIndexs
      .map(dataIndex => {
        return shieldPointColumns.find(item => item.dataIndex === dataIndex);
      })
      .filter((item): item is ColumnType<ShieldPoint> => item !== undefined);

    if (operation) {
      return [...newColumns, operation];
    }
    return newColumns;
  }, [dataIndexs, operation]);

  return (
    <Table
      columns={columns}
      scroll={{ x: 'max-content' }}
      tableLayout="fixed"
      rowKey="key"
      {...rest}
    />
  );
}
