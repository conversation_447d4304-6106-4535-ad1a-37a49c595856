import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { SelectProps } from '@manyun/base-ui.ui.select';

export type ChannelStatusSelectProps = Omit<SelectProps, 'options'>;

const options: SelectProps['options'] = [
  { value: 'ON', label: '启用' },
  { value: 'OFF', label: '停用' },
];

export const ChannelStatusSelect = (props: SelectProps) => {
  return <Select options={options} {...props} />;
};
