import React from 'react';

import { useMetaData } from '@manyun/resource-hub.hook.use-meta-data';
import { MetaType } from '@manyun/resource-hub.model.metadata';

export type AlarmReasonTextProps = {
  /**
   * 受理反馈原因类型code
   */
  code: string | number;
};

export function AlarmReasonText({ code }: AlarmReasonTextProps) {
  const [{ data }, { readMetaData }] = useMetaData(MetaType.ALARM_REASON);
  React.useEffect(() => {
    readMetaData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const name = data.entities[code] ? data.entities[code].name : '未知';

  return <>{name}</>;
}
