import type { ProColumnType } from '@galiojs/pro-table';
import React, { useMemo } from 'react';
import { useSelector } from 'react-redux';
import { Link } from 'react-router-dom';

import { ProTable } from '@manyun/base-ui.ui.pro-table';
import type { ProColumns, ProTableProps } from '@manyun/base-ui.ui.pro-table';
import { Tag } from '@manyun/base-ui.ui.tag';
import type { BackendAlarmType } from '@manyun/monitoring.model.alarm';
import type { MonitoringItemJSON } from '@manyun/monitoring.model.monitoring-item';
import { TriggerRuleType } from '@manyun/monitoring.model.trigger-rule';
import { generateAlarmConfigurationTemplateDetailUrl } from '@manyun/monitoring.route.monitoring-routes';
import { AlarmLevelText } from '@manyun/monitoring.ui.alarm-level-text';
import { AlarmMonitoringItemStateUpdator } from '@manyun/monitoring.ui.alarm-monitoring-item-state-updator';
import { AlarmTypeText } from '@manyun/monitoring.ui.alarm-type';
import { TriggerRulesText } from '@manyun/monitoring.ui.trigger-rules-text';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { selectMetaData } from '@manyun/resource-hub.state.meta-data';

import { RuleLockSwitch } from './components/rule-lock-switch';

export type ShowColumns =
  | 'name'
  | 'alarmType'
  | 'alarmLevel'
  | 'preTriggerRules'
  | 'triggerRules'
  | 'enable'
  | 'lock'
  | 'isCreateIncident'
  | 'incidentType'
  | 'groupName'
  | 'groupStatus'
  | 'tagEnable';

export type TablePropType = Partial<MonitoringItemJSON> & {
  groupName?: string;
  groupId?: number;
  groupStatus?: boolean;
};

export type AlarmMonitoringItemTableProps = {
  showColumns: ShowColumns[];
  operation?: ProColumnType<TablePropType>;
  loading?: boolean;
} & Omit<ProTableProps<TablePropType, unknown>, 'columns'>;

/**
 *  告警项通用列表
 * @export
 * @param {AlarmMonitoringItemTableProps} {}
 * @return {*}
 */
export function AlarmMonitoringItemTable({
  showColumns,
  operation,
  loading,
  ...props
}: AlarmMonitoringItemTableProps) {
  const { data } = useSelector(selectMetaData(MetaType.ALARM_LEVEL));
  const columns = useMemo(() => {
    return getColumns({ showColumns, operation, alarmLevels: data });
  }, [data, operation, showColumns]);

  return <ProTable loading={loading} columns={columns} {...props} />;
}

export const getColumns = ({
  showColumns,
  alarmLevels,
  operation,
}: {
  showColumns: ShowColumns[];
  alarmLevels: { label: string; value: string }[];
  operation?: ProColumnType<TablePropType>;
}) => {
  let columns: ProColumns<TablePropType>[] = [
    {
      title: '所属模板',
      fixed: 'left',
      key: 'groupName',
      dataIndex: 'groupName',
      render: (text, record) => {
        if (record.groupId) {
          return (
            <Link
              to={generateAlarmConfigurationTemplateDetailUrl({ id: record.groupId.toString() })}
            >
              {text}
            </Link>
          );
        }
        return '--';
      },
    },
    {
      title: '监控测点名称',
      fixed: 'left',
      key: 'name',
      dataIndex: 'name',
      sorter: (a, b) => {
        return (a.name ?? '').toString().localeCompare((b.name ?? '').toString(), 'zh-CN');
      },
    },
    {
      title: '告警类型',
      key: 'alarmType',
      dataIndex: 'alarmType',
      render: (_, entity) => <AlarmTypeText value={entity.alarmType as BackendAlarmType} />,
      filters: true,
      onFilter: true,
      valueType: 'select',
      width: 100,
      valueEnum: {
        WARN: { text: '预警' },
        ERROR: { text: '告警' },
      },
    },
    {
      title: '告警级别',
      key: 'alarmLevel',
      dataIndex: 'alarmLevel',
      filters: true,
      onFilter: true,
      width: 100,
      valueType: 'select',
      valueEnum: () => {
        const enums: Record<string, unknown> = {};
        alarmLevels.forEach(d => {
          enums[d.value] = { text: d.label };
        });
        return enums;
      },
      render: (_, entity) => <AlarmLevelText code={entity?.alarmLevel ?? ''} />,
    },
    {
      title: '前置条件',
      key: 'preTriggerRules',
      dataIndex: 'preTriggerRules',
      render: (_, entity) => {
        return (
          <TriggerRulesText
            type={TriggerRuleType.PreCondition}
            triggerRules={entity?.preTriggerRules ?? []}
          />
        );
      },
    },
    {
      title: '告警条件',
      key: 'triggerRules',
      dataIndex: 'triggerRules',
      render: (_, entity) => {
        return (
          <TriggerRulesText
            type={TriggerRuleType.AlarmCondition}
            triggerRules={entity?.triggerRules ?? []}
          />
        );
      },
    },
    {
      title: '启用状态',
      key: 'enable',
      dataIndex: 'enable',
      render: (_, entity) => (
        <AlarmMonitoringItemStateUpdator
          refKey={'enable' + entity.id}
          ids={entity.id as number | string}
          mode="switch"
          enable={entity.enable ?? false}
        />
      ),
      filters: true,
      onFilter: true,
      valueType: 'select',
      valueEnum: {
        true: { text: '启用' },
        false: { text: '停用' },
      },
    },
    {
      title: '规则锁',
      key: 'lock',
      dataIndex: 'lock',
      render: (_, entity) => <RuleLockSwitch mointoringItem={entity} />,
    },
    {
      title: '自动创建事件',
      key: 'isCreateIncident',
      dataIndex: 'isCreateIncident',
      render: (_, entity) => {
        if (entity.isCreateIncident) {
          return '是';
        }
        return '否';
      },
    },
    {
      title: '事件类型',
      key: 'incidentType',
      dataIndex: 'incidentType',
      render: (_, entity) => {
        return (entity.incidentTypeName ?? []).length
          ? (entity.incidentTypeName ?? []).join('/')
          : '--';
      },
    },
    {
      title: '规则状态',
      key: 'enable',
      dataIndex: 'tagEnable',
      render: (_, entity) => {
        return (
          <Tag color={entity.enable ? 'success' : 'default'}>{entity.enable ? '启用' : '停用'}</Tag>
        );
      },
    },
    {
      title: '模板状态',
      key: 'groupStatus',
      dataIndex: 'groupStatus',
      render: (_, entity) => {
        if (!entity.groupId) {
          return '--';
        }
        return (
          <Tag color={entity.groupStatus ? 'success' : 'default'}>
            {entity.groupStatus ? '启用' : '停用'}
          </Tag>
        );
      },
    },
  ];
  if (showColumns.length) {
    columns = columns.filter(column => showColumns.includes(column.dataIndex as unknown));
  }
  if (operation) {
    columns.push(operation);
  }

  return columns;
};
