import React from 'react';

import { Switch } from '@manyun/base-ui.ui.switch';

import {
  useCurrentMutateType,
  useMutateTplFields,
} from '@manyun/monitoring.hook.use-alarm-configuration-template';

import type { TablePropType } from '../../alarm-monitoring-item-table';

export type RuleLockSwitchProps = {
  mointoringItem: TablePropType;
};

export function RuleLockSwitch({ mointoringItem }: RuleLockSwitchProps) {
  const [currenMutateType] = useCurrentMutateType();
  const [, { updateMutateTplMonitorItemFields }] = useMutateTplFields(currenMutateType);

  const onChange = (value: boolean) => {
    updateMutateTplMonitorItemFields({
      ...mointoringItem,
      lock: value,
      id: mointoringItem.id!,
    });
  };
  return <Switch checked={mointoringItem.lock ?? false} onChange={onChange} />;
}
