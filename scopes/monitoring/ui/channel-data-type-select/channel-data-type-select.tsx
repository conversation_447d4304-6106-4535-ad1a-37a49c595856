import React, { useEffect, useState } from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import type { SelectProps } from '@manyun/base-ui.ui.select';

import { fetchChannelDataType } from '@manyun/monitoring.service.fetch-channel-data-type';

export type ChannelDataTypeSelectProps = Omit<SelectProps, 'options'>;

export const ChannelDataTypeSelect = (props: SelectProps) => {
  const [options, setOptions] = useState<SelectProps['options']>([]);
  useEffect(function () {
    (async () => {
      const { error, data } = await fetchChannelDataType();
      if (error) {
        message.error(error.message);
        return;
      }
      const list = Object.keys(data).map(item => {
        return { value: item, label: data[item] };
      });
      setOptions(list);
    })();
  }, []);

  return <Select options={options} {...props} />;
};
