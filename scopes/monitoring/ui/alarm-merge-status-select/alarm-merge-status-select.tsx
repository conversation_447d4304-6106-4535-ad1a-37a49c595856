import React, { useMemo } from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

import { getAlarmLocales } from '@manyun/monitoring.model.alarm';

export type AlarmMergeStatusSelectProps = Omit<SelectProps, 'options' | 'ref'>;

export const AlarmMergeStatusSelect = React.forwardRef(
  (props: SelectProps, ref: React.Ref<RefSelectProps>) => {
    const locales = getAlarmLocales();

    const options: SelectProps['options'] = useMemo(() => {
      return [
        { value: 'UNMERGED', label: locales.mergeStatus.UNMERGED },
        { value: 'CHILD', label: locales.mergeStatus.CHILD },
        { value: 'ROOT', label: locales.mergeStatus.ROOT },
      ];
    }, [locales.mergeStatus.CHILD, locales.mergeStatus.ROOT, locales.mergeStatus.UNMERGED]);

    return <Select {...props} ref={ref} options={options} />;
  }
);

AlarmMergeStatusSelect.displayName = 'AlarmMergeStatusSelect';
