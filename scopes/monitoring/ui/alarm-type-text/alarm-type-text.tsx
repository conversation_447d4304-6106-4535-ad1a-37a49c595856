import React from 'react';

import { Typography } from '@manyun/base-ui.ui.typography';

import type { BackendAlarmType } from '@manyun/monitoring.model.alarm';

export const alarmTypeMapper = {
  ERROR: '告警',
  WARN: '预警',
};

export type AlarmTypeTextProps = {
  code: BackendAlarmType;
};

export function AlarmTypeText({ code }: AlarmTypeTextProps) {
  if (code === 'WARN') {
    return <Typography.Text type="warning">{alarmTypeMapper[code]}</Typography.Text>;
  }
  if (code === 'ERROR') {
    return <Typography.Text type="danger">{alarmTypeMapper[code]}</Typography.Text>;
  }
  return <Typography.Text>未知</Typography.Text>;
}
