import React from 'react';

import { Modal } from '@manyun/base-ui.ui.modal';

import type { PointGuid } from '@manyun/monitoring.chart.points-line';
import { PointsLine } from '@manyun/monitoring.chart.points-line';

const defaultModalStyle = {
  minWidth: (1200 / 1600) * 100 + '%',
  maxWidth: 1200,
};

/**
 * 视图/拓扑 测点预览
 */
export type GraphPointLineModalProps = {
  visible: boolean;
  onVisibleChange: () => void;
  idc: string;
  pointGuids: PointGuid[];
  style?: React.CSSProperties;
  bodyStyle?: React.CSSProperties;
  modalTitle?: string;
};

export function GraphPointLineModal({
  visible,
  onVisibleChange,
  idc,
  pointGuids,
  style,
  bodyStyle,
  modalTitle,
}: GraphPointLineModalProps) {
  return (
    <Modal
      style={{
        ...defaultModalStyle,
        ...style,
      }}
      bodyStyle={bodyStyle}
      open={visible}
      footer={null}
      destroyOnClose
      maskClosable
      title={modalTitle ?? null}
      onCancel={() => onVisibleChange()}
    >
      <PointsLine
        variant="normal"
        idcTag={idc}
        pointGuids={pointGuids}
        seriesOption={pointGuids.map(point => ({
          name: point.serieName,
        }))}
        showExport
        showSetting
        defaultIntervalEnabled
        allowInterval
      />
    </Modal>
  );
}
