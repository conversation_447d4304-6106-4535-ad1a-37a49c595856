import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

export type PointCategorySelectProps = Omit<SelectProps, 'options'>;

export type PointCategory = 'NORMAL_POINT' | 'NON_POINT';

type PointCategoryOption = {
  value: PointCategory;
  label: string;
};

const options: PointCategoryOption[] = [
  { value: 'NORMAL_POINT', label: '标准测点' },
  { value: 'NON_POINT', label: '非标测点' },
];

export const PointCategorySelect = React.forwardRef(
  ({ ...rest }: PointCategorySelectProps, ref: React.Ref<RefSelectProps>) => {
    return <Select ref={ref} style={{ width: 100 }} {...rest} options={options} />;
  }
);

PointCategorySelect.displayName = 'PointCategorySelect';
