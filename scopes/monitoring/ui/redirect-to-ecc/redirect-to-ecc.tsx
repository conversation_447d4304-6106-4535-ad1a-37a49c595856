import { url } from '@teammc/core';
import React, { useEffect } from 'react';
import type { ReactNode } from 'react';
import { useParams } from 'react-router-dom';

import { message } from '@manyun/base-ui.ui.message';
import { Spin } from '@manyun/base-ui.ui.spin';

type JumpPageType = {
  key: string;
  value: string;
  eccPath: string;
};
type GetJumpPagesParams = {
  urlIdc: string;
  type?: string;
  guid?: string;
  search?: string;
  versionTab?: string;
};

const getJumpPages = ({
  urlIdc,
  type,
  guid,
  search,
  versionTab,
}: GetJumpPagesParams): JumpPageType[] => [
  {
    key: 'device_record',
    value: '设备页面',
    eccPath: `redirect-loading?guid=${guid}&search=${search}&type=${type}`,
  },
  {
    key: 'version_manage_detail',
    value: '版本管理',
    eccPath: `version-manage/detail${versionTab ? `?tab=${versionTab}` : ''}`,
  },
];
export type RedirectToEccProps = {
  jumpKey: string;
  idcTag?: string | null;
  block?: string;
  floor?: string;
  guid?: string;
  type?: string;
  search?: string;
  versionTab?: string;
  children?: ReactNode;
};

export function RedirectToEcc({
  jumpKey,
  idcTag,
  type,
  guid,
  search = '',
  versionTab,
}: RedirectToEccProps) {
  const { idc } = useParams<{ idc: string }>();
  const urlIdc = idcTag ?? idc;

  useEffect(() => {
    if (!urlIdc) {
      return;
    }
    const jumPage = getJumpPages({ urlIdc, type, guid, search, versionTab }).find(
      (item: JumpPageType) => item.key === jumpKey
    );
    if (!jumPage) {
      message.error('未匹配到跳转页面!');
      return;
    }
    window.location.replace(
      // @ts-ignore ts(2339)
      url.join(`${window.apps.monitoring.replace('[idc]', urlIdc)}`, jumPage.eccPath)
    );
  }, [urlIdc, jumpKey, type, guid, search, versionTab]);

  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        zIndex: 9999,
        background: '#fff',
        width: '100%',
        height: '100vh',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      <Spin spinning />
    </div>
  );
}
