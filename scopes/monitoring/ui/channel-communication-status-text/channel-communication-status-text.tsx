import React from 'react';

import { Badge } from '@manyun/base-ui.ui.badge';

import {
  ChannelCommunicationStatus,
  getChannelConfigLocales,
} from '@manyun/monitoring.model.channel-config';

const locales = getChannelConfigLocales();

export type ChannelCommunicationStatusTextProps = {
  status: ChannelCommunicationStatus['code'];
};

export function ChannelCommunicationStatusText({ status }: ChannelCommunicationStatusTextProps) {
  if (status === 'ONLINE' || status === 'OFFLINE' || status === 'ERR') {
    return (
      <Badge
        status={status === 'ONLINE' ? 'success' : 'error'}
        text={locales['channelCommunicationStatus'][status]}
      />
    );
  }

  return <span>未知</span>;
}
