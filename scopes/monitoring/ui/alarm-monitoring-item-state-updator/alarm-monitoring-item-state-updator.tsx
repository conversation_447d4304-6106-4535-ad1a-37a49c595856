import React from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Switch } from '@manyun/base-ui.ui.switch';

import {
  useBatchUpdateAlarmMonitorItemState,
  useCurrentMutateType,
} from '@manyun/monitoring.hook.use-alarm-configuration-template';

export type AlarmMonitoringItemStateUpdatorProps = {
  mode: 'batch' | 'switch';
  ids: (number | string)[] | string | number;
  enable: boolean;
  refKey?: React.Key;
  showConfirm?: boolean;
};

export function AlarmMonitoringItemStateUpdator({
  ids,
  mode,
  enable,
  refKey,
  showConfirm,
}: AlarmMonitoringItemStateUpdatorProps) {
  const [currenMutateType] = useCurrentMutateType();
  const [updateState] = useBatchUpdateAlarmMonitorItemState(currenMutateType);
  return (
    <>
      {mode === 'switch' && (
        <Switch
          key={refKey}
          checked={enable}
          onChange={checked => {
            updateState(ids, checked);
          }}
        />
      )}
      {mode === 'batch' && (
        <Button
          type="link"
          compact
          key={refKey}
          onClick={() => {
            if (showConfirm && !enable) {
              Modal.confirm({
                title: '是否确认批量停用告警规则？',
                content: '停用后告警将无法生效',
                onOk: () => {
                  updateState(ids, enable);
                },
              });
              return;
            }
            updateState(ids, enable);
          }}
        >
          {enable ? '批量启用' : '批量停用'}
        </Button>
      )}
    </>
  );
}
