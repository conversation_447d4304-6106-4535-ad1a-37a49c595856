import React from 'react';

import { Tag } from '@manyun/base-ui.ui.tag';
import type { TagProps } from '@manyun/base-ui.ui.tag';

import { getAlarmLocales } from '@manyun/monitoring.model.alarm';
import type { BackendAlarmLifecycleState } from '@manyun/monitoring.model.alarm';

export type AlarmLifecyStatusTextProps = {
  code: BackendAlarmLifecycleState;
  shape?: TagProps['shape'];
};

const alarmLifecyStatusTagColor: Record<BackendAlarmLifecycleState, string> = {
  CONFIRMED: 'warning',
  ACTIVE: 'error',
  PROCESS: 'success',
  INIT: 'default',
  REMOVED: 'default',
};

export function AlarmLifecyStatusText({ code, shape = 'typography' }: AlarmLifecyStatusTextProps) {
  const alarmLocales = getAlarmLocales();

  return (
    <Tag color={alarmLifecyStatusTagColor[code]} shape={shape}>
      {alarmLocales.lifecycleState[code]}
    </Tag>
  );
}
