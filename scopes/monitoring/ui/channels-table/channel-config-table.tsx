import React, { useMemo } from 'react';
import { Link } from 'react-router-dom';

import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType, TableProps } from '@manyun/base-ui.ui.table';

import type { ChannelConfigJSON } from '@manyun/monitoring.model.channel-config';
import { getChannelConfigLocales } from '@manyun/monitoring.model.channel-config';
import { generateChannelDetailRoutePath } from '@manyun/monitoring.route.monitoring-routes';
import { ChannelCommunicationStatusText } from '@manyun/monitoring.ui.channel-communication-status-text';
import { ChannelStatusText } from '@manyun/monitoring.ui.channel-status-text';
import { DeviceTypeText } from '@manyun/resource-hub.ui.device-type-text';

const locales = getChannelConfigLocales();

const BLANK = '--';

const getChannelConfigColumns = ({ fromPage }: { fromPage?: string }) => {
  const channelConfigColumns: ColumnType<ChannelConfigJSON>[] = [
    {
      title: locales['id'],
      dataIndex: 'id',
      fixed: 'left',
    },
    {
      title: locales['blockGuid'],
      dataIndex: 'blockGuid',
    },
    {
      title: locales['channelType']['__self'],
      dataIndex: 'channelType',
      render: (_, record) => {
        return record.channelType.name;
      },
    },
    {
      title: locales['name'],
      dataIndex: 'name',
      render: (text, record) => {
        if (fromPage === 'reuseModal') {
          return text;
        }
        return (
          <Link
            type="link"
            target="_blank"
            to={generateChannelDetailRoutePath({ id: record.id, tab: 'deviceInfo' })}
          >
            {text}
          </Link>
        );
      },
    },
    {
      title: locales['channelCommunicationStatus']['__self'],
      dataIndex: 'channelCommunicationStatus',
      render: text => {
        return <ChannelCommunicationStatusText status={text?.code} />;
      },
    },
    {
      title: locales['ip'],
      dataIndex: 'ip',
    },
    {
      title: locales['port'],
      dataIndex: 'port',
    },
    {
      title: locales['protocol'],
      dataIndex: 'protocol',
    },
    {
      title: locales['device']['num'],
      dataIndex: 'deviceNum',
      render: (_, record) => {
        return record.device?.num ?? BLANK;
      },
    },
    {
      title: locales['channelStatus']['__self'],
      dataIndex: 'channelStatus',
      render: text => {
        return <ChannelStatusText status={text.code} />;
      },
    },
    {
      title: locales['device']['name'],
      dataIndex: 'device',
      render: text => {
        return text.name ?? BLANK;
      },
    },
    {
      title: locales['vendor'],
      dataIndex: 'vendor',
    },
    {
      title: locales['device']['type'],
      dataIndex: 'deviceType',
      render: (_, record) => {
        return record.device?.type ? <DeviceTypeText code={record.device.type} /> : BLANK;
      },
    },
    {
      title: locales['roomTag'],
      dataIndex: 'roomTag',
    },
  ];
  return channelConfigColumns;
};

export type ColumnDataIndex =
  | 'id'
  | 'blockGuid'
  | 'channelType'
  | 'name'
  | 'channelCommunicationStatus'
  | 'ip'
  | 'port'
  | 'protocol'
  | 'deviceNum'
  | 'channelStatus'
  | 'device'
  | 'vendor'
  | 'deviceType'
  | 'roomTag';

export type ChannelConfigTableProps = {
  dataIndexs: ColumnDataIndex[];
  operation?: ColumnType<ChannelConfigJSON>;
  fromPage?: string;
} & Omit<TableProps<ChannelConfigJSON>, 'columns'>;

export function ChannelConfigTable({
  dataIndexs = [],
  operation,
  fromPage,
  ...rest
}: ChannelConfigTableProps) {
  const columns = useMemo(() => {
    const newColumns = dataIndexs
      .map(dataIndex => {
        const channelConfigColumns = getChannelConfigColumns({ fromPage });
        const columnItem = channelConfigColumns.find(item => item.dataIndex === dataIndex);
        return columnItem;
      })
      .filter((item): item is ColumnType<ChannelConfigJSON> => typeof item !== 'undefined');

    if (operation) {
      return [...newColumns, operation];
    }
    return newColumns;
  }, [dataIndexs, fromPage, operation]);

  return <Table columns={columns} scroll={{ x: 'max-content' }} rowKey="id" {...rest} />;
}
