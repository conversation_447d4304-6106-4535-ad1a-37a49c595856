import React, { useMemo } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType, TableProps } from '@manyun/base-ui.ui.table';

import type { Config } from '@manyun/monitoring.model.channel-config';
import { OperatorTypeSelect } from '@manyun/monitoring.ui.operator-type-select';

export type ParamConfig = {
  operatorType: string;
  registerNum: string;
};

export type OperationConfigProps = {
  configList: Config[] | null;
  onChange: (arg: ParamConfig[]) => void;
};

export type TableConfig = Config & {
  status: 'editing' | 'readOnly';
  id: string;
};

type Option = { label: string; value: string };

export type ChannelConfigOperationsProps = {
  tableConfigList: TableConfig[];
  fromPage: 'create' | 'detail';
  onOperatorTypeSelect?: ({ option, record }: { option: Option; record: TableConfig }) => void;
  onRegisterNumChange?: (value: string, record: TableConfig) => void;
  onSave?: (record: TableConfig) => void;
  onEdit?: (record: TableConfig) => void;
  onDelete?: (record: TableConfig) => void;
  onCancal?: () => void;
} & Omit<TableProps<TableConfig>, 'columns'>;

export function ChannelConfigOperations({
  tableConfigList,
  fromPage,
  onOperatorTypeSelect,
  onRegisterNumChange,
  onSave,
  onEdit,
  onDelete,
  onCancal,
  ...rest
}: ChannelConfigOperationsProps) {
  const columns = useMemo(() => {
    const newColumns: ColumnType<TableConfig>[] = [
      {
        title: '操作类型',
        dataIndex: 'operatorType',
        render: (_, record) => {
          if (fromPage === 'detail') {
            return record.operatorType.name;
          }
          if (record.status === 'editing') {
            return (
              <OperatorTypeSelect
                value={record.operatorType.code}
                onSelect={(value: string, option: Option) =>
                  onOperatorTypeSelect && onOperatorTypeSelect({ option, record })
                }
                style={{ width: 211 }}
              />
            );
          } else {
            return record.operatorType.name;
          }
        },
      },

      {
        title: '读取寄存器数量',
        dataIndex: 'registerNum',
        render: (_, record) => {
          if (fromPage === 'detail') {
            return record.registerNum;
          }
          if (record.status === 'editing') {
            return (
              <InputNumber
                min={0}
                style={{ width: 100 }}
                onChange={value => onRegisterNumChange && onRegisterNumChange(value, record)}
                value={record.registerNum}
              />
            );
          } else {
            return record.registerNum;
          }
        },
      },
    ];
    if (fromPage === 'create') {
      newColumns.push({
        title: '操作',
        key: '_actions',
        render: (_, record) => (
          <Space>
            {record.status === 'editing' ? (
              <>
                <Button compact type="link" onClick={() => onSave && onSave(record)}>
                  保存
                </Button>
                <Button type="link" compact onClick={onCancal}>
                  取消
                </Button>
              </>
            ) : (
              <>
                <Button compact type="link" onClick={() => onEdit && onEdit(record)}>
                  编辑
                </Button>
                <Button type="link" compact onClick={() => onDelete && onDelete(record)}>
                  删除
                </Button>
              </>
            )}
          </Space>
        ),
      });
    }
    return newColumns;
  }, [fromPage, onCancal, onDelete, onEdit, onOperatorTypeSelect, onRegisterNumChange, onSave]);

  return <Table dataSource={tableConfigList} rowKey="id" columns={columns} {...rest} />;
}
