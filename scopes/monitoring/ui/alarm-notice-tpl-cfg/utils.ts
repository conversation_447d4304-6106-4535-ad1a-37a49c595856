import type { NotifyRule } from '@manyun/monitoring.model.monitoring-item';
import { CUSTOMIZED_NOTIFICATION_TEXT } from '@manyun/monitoring.model.monitoring-item';

export function getNotifyExample(notificationTpl: NotifyRule[]) {
  return notificationTpl
    .map(({ value, description, text, label }) => {
      if (value === CUSTOMIZED_NOTIFICATION_TEXT) {
        return text || label;
      }
      return description?.split('_$$_')?.[1] || description;
    })
    .join(' ');
}

export const DefaultTemplateMess: Omit<NotifyRule, 'id'>[] = [
  {
    label: '自定义',
    value: 'CUSTOMIZED_TEXT',
    text: '[',
    description: '自定义配置告警内容',
  },
  {
    label: '告警级别',
    value: 'ALARM_LEVEL',
    description: '告警的等级_$$_2',
  },
  {
    label: '自定义',
    value: 'CUSTOMIZED_TEXT',
    text: '级]',
    description: '自定义配置告警内容',
  },
  {
    label: '告警类型',
    value: 'ALARM_TYPE',
    description: '告警的类型_$$_告警',
  },
  {
    label: '自定义',
    value: 'CUSTOMIZED_TEXT',
    text: '，',
    description: '自定义配置告警内容',
  },
  {
    label: '时间',
    value: `TIME?string("MM-dd HH:mm:ss")`,
    description: '发生告警的时间_$$_01-01 01:01:01',
  },
  { label: '楼', value: 'BLOCK!', notifyConfigMode: 'INDEPENDENT', description: '楼号_$$_A' },
  {
    label: '自定义',
    value: 'CUSTOMIZED_TEXT',
    text: '楼',
    description: '自定义配置告警内容',
  },
  { label: '包间', value: 'ROOM!', notifyConfigMode: 'INDEPENDENT', description: '包间号_$$_A1-1' },
  {
    label: '自定义',
    value: 'CUSTOMIZED_TEXT',
    text: '包间出现',
    description: '自定义配置告警内容',
  },
  {
    label: '告警测点',
    value: 'POINT',
    description: '产生告警的测点名称_$$_线电压Ubc',
  },
  {
    label: '自定义',
    value: 'CUSTOMIZED_TEXT',
    text: '告警，告警设备：',
    description: '自定义配置告警内容',
  },
  {
    label: '故障设备',
    value: 'FAULT_DEVICE!',
    description: '产生告警的设备名称_$$_B-2-AK8',
  },
  {
    label: '自定义',
    value: 'CUSTOMIZED_TEXT',
    text: '，机柜影响面：',
    description: '自定义配置告警内容',
  },
  {
    label: '机柜影响面',
    value: 'GRID_INFLUENCE',
    description: '影响的机柜数量_$$_影响机柜2个',
  },
];
