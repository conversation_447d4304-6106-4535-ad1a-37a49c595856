@import '~@manyun/base-ui.theme.theme/dist/theme.variable.less';

.ulItemCircle {
  border: 1px solid @primary-color;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 10px;
}
.dropzoneContainer {
  width: 100%;
  .dropzoneContainerInput {
    border-radius: 4px;
    border: 1px solid @border-color-split;
  }
  .dropzoneContainerInputError {
    border-color: @error-color;
  }
  .dropZoneWrapper {
    position: relative;
    display: inline-flex;
    flex-wrap: wrap;
    gap: 4px;
    width: 100%;
    overflow: auto;
    font-size: 14px;
    line-height: 1.5;
    min-height: 140px;
    padding: 8px 11px;
  }
  .dropZoneActionWrapper {
    position: relative;
    display: block;
    width: 100%;
    overflow: auto;
    font-size: 14px;
    line-height: 1.5;
    min-height: 34px;
    padding: 0 11px 4px;
  }
}

.droppableZoneContainer {
  width: 100%;
  > :global(.manyun-space-item) {
    flex: 1;
  }
}

.dndItemWrapper {
  display: inline-block;
  font-weight: 400;
  white-space: nowrap;
  text-align: center;
  background: @primary-color;
  height: 32px;
  line-height: 32px;
  padding: 0 10px;
  font-size: 14px;
  border-radius: 4px;
  cursor: pointer;
  color: @white;
}
