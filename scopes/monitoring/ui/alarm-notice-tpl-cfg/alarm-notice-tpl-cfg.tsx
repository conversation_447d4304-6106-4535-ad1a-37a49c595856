import React, { useCallback, useState } from 'react';

import shortid from 'shortid';

import { Form } from '@manyun/base-ui.ui.form';
import type { FormInstance } from '@manyun/base-ui.ui.form';
import { Select } from '@manyun/base-ui.ui.select';
import { Typography } from '@manyun/base-ui.ui.typography';

import {
  useCurrentMutateType,
  useMutateTplFields,
} from '@manyun/monitoring.hook.use-alarm-configuration-template';
import type { MonitoringItemJSON } from '@manyun/monitoring.model.monitoring-item';

import { NotificationTxtTplCfgCard } from './dragable-card';
import styles from './dragable-card.module.less';
import { DefaultTemplateMess } from './utils';

export type AlarmNoticeTplCfgFormValues = {};

export type MonitoringItemNoticeInfo = Pick<MonitoringItemJSON, 'pointName' | 'notifyRules'> & {
  name?: Pick<MonitoringItemJSON, 'name'>;
  id?: Pick<MonitoringItemJSON, 'id'> | undefined;
  pointCode?: Pick<MonitoringItemJSON, 'pointCode'>;
};

export type AlarmNoticeTplCfgProps = {
  form: FormInstance<AlarmNoticeTplCfgFormValues>;
  monitoringItem: MonitoringItemNoticeInfo;
};

export function AlarmNoticeTplCfg({ monitoringItem }: AlarmNoticeTplCfgProps) {
  const [currenMutateType] = useCurrentMutateType();
  const [, { updateMutateTplMonitorItemNotifyRules }] = useMutateTplFields(currenMutateType);
  const [noticeTpl, setNoticeTpl] = useState('');

  const setDefaultTemplate = useCallback(() => {
    const notifyRules = DefaultTemplateMess.map((item, index) => {
      return {
        ...item,
        id: shortid(),
        destinationIndex: index,
      };
    });
    updateMutateTplMonitorItemNotifyRules({
      id: monitoringItem.id as any,
      notifyRules: notifyRules,
    });
  }, [monitoringItem.id, updateMutateTplMonitorItemNotifyRules]);

  return (
    <>
      {monitoringItem.name && (
        <Form.Item>
          <Typography.Text style={{ display: 'flex', alignItems: 'center' }}>
            <span className={styles.ulItemCircle} /> 监控项名称：{monitoringItem.name}
          </Typography.Text>
        </Form.Item>
      )}
      <Form.Item label="告警通知模板">
        <Select
          onChange={value => {
            setNoticeTpl(value);
            if (value) {
              setDefaultTemplate();
            }
          }}
          style={{ width: 200 }}
          allowClear
        >
          <Select.Option key="1" value="basic">
            标准模版
          </Select.Option>
        </Select>
      </Form.Item>
      <div id={`${monitoringItem.id ?? 'global'}_$$_notifyRules`} />
      <Form.Item
        label="告警通知文案"
        name={`${monitoringItem.id ?? 'global'}_$$_notifyRules`}
        rules={[
          {
            required: true,
            message: '告警通知文案为必填项!',
            validator: async (_, val) => {
              if ((monitoringItem.notifyRules ?? []).length === 0) {
                return Promise.reject('告警通知文案为必填项');
              } else {
                return Promise.resolve();
              }
            },
          },
        ]}
      >
        <NotificationTxtTplCfgCard tplSelectVal={noticeTpl} monitoringItem={monitoringItem} />
      </Form.Item>
    </>
  );
}
