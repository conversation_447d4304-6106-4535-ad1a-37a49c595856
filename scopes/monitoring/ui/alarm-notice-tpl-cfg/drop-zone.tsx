import React, { useCallback, useMemo, useState } from 'react';
import { Draggable, Droppable } from 'react-beautiful-dnd';

import classNames from 'classnames';
import shortid from 'shortid';

import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Popover } from '@manyun/base-ui.ui.popover';
import { Space } from '@manyun/base-ui.ui.space';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';

import {
  useCurrentMutateType,
  useMutateTplFields,
} from '@manyun/monitoring.hook.use-alarm-configuration-template';
import { MonitoringItem } from '@manyun/monitoring.model.monitoring-item';
import type { NotifyRule } from '@manyun/monitoring.model.monitoring-item';

import type { MonitoringItemNoticeInfo } from './alarm-notice-tpl-cfg';
import styles from './dragable-card.module.less';
import { DefaultTemplateMess, getNotifyExample } from './utils';

export type DropZoneProps = {
  monitoringItem: MonitoringItemNoticeInfo;
  tplSelectVal?: string;
};

export const DropZone = ({ monitoringItem, tplSelectVal }: DropZoneProps) => {
  const [currenMutateType] = useCurrentMutateType();
  const [
    { validateNotifyErrorIds, copyNoticeRules },
    { updateMutateTplMonitorItemNotifyRules, setMutateTplFields },
  ] = useMutateTplFields(currenMutateType);

  const { notifyRules } = monitoringItem;

  const fieldsCode = useMemo(() => {
    return MonitoringItem.toApiNotifyRule(notifyRules);
  }, [notifyRules]);

  const fieldsExample = useMemo(() => getNotifyExample(notifyRules), [notifyRules]);

  const getCustomizedTxtChangeHandler = useCallback(
    (id: string, text?: string) => {
      const newNotifyRules: NotifyRule[] = [];
      notifyRules.forEach(rule => {
        const obj = { ...rule };
        if (rule.id === id) {
          obj.text = text;
        }
        newNotifyRules.push(obj);
      });
      updateMutateTplMonitorItemNotifyRules({
        id: monitoringItem.id,
        notifyRules: newNotifyRules,
      });
    },
    [monitoringItem.id, notifyRules, updateMutateTplMonitorItemNotifyRules]
  );

  const getOnDelete = useCallback(
    (id: string) => {
      const newNotifyRules = notifyRules.filter(rule => rule.id !== id);
      updateMutateTplMonitorItemNotifyRules({ id: monitoringItem.id, notifyRules: newNotifyRules });
    },
    [monitoringItem.id, notifyRules, updateMutateTplMonitorItemNotifyRules]
  );

  const onResetNotification = useCallback(() => {
    let newNotifyRules: NotifyRule[] = [];
    if (tplSelectVal === 'basic') {
      newNotifyRules = DefaultTemplateMess.map((item, index) => {
        return {
          ...item,
          id: shortid(),
          destinationIndex: index,
        };
      });
    }
    updateMutateTplMonitorItemNotifyRules({ id: monitoringItem.id, notifyRules: newNotifyRules });
  }, [monitoringItem.id, tplSelectVal, updateMutateTplMonitorItemNotifyRules]);

  return (
    <Space direction="vertical" className={styles.dropzoneContainer}>
      <div
        className={classNames(
          styles.dropzoneContainerInput,
          validateNotifyErrorIds.includes((monitoringItem.id ?? 'global').toString()) &&
            (notifyRules ?? []).length === 0 &&
            styles.dropzoneContainerInputError
        )}
      >
        <Droppable droppableId="notification" direction="horizontal">
          {provided => (
            <div
              ref={provided.innerRef}
              className={styles.dropZoneWrapper}
              {...provided.droppableProps}
            >
              {notifyRules?.map(({ id, label, value, text }, index) => {
                if (value === 'CUSTOMIZED_TEXT') {
                  return (
                    <Draggable key={id} draggableId={`notification_&&_${id}`} index={index}>
                      {provided => (
                        <div
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          {...provided.dragHandleProps}
                          key={id}
                        >
                          <PopoverField
                            id={id}
                            label={label}
                            text={text}
                            fieldsCode={fieldsCode}
                            onFieldTextOk={(txt: string) => getCustomizedTxtChangeHandler(id, txt)}
                            onDelete={() => getOnDelete(id)}
                          />
                        </div>
                      )}
                    </Draggable>
                  );
                }
                return (
                  <Draggable key={id} draggableId={`notification_&&_${id}`} index={index}>
                    {provided => (
                      <div
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        {...provided.dragHandleProps}
                        key={id}
                      >
                        <Tag
                          key={id}
                          style={{ cursor: 'pointer' }}
                          color="blue"
                          closable
                          onClose={() => getOnDelete(id)}
                        >
                          {label}
                        </Tag>
                      </div>
                    )}
                  </Draggable>
                );
              })}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
        <div className={styles.dropZoneActionWrapper}>
          <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
            <Button
              size="small"
              type="primary"
              data-type="reset-btn"
              onClick={() => {
                setMutateTplFields({ copyNoticeRules: notifyRules });
              }}
            >
              复制
            </Button>
            <Popconfirm
              title="粘贴操作会覆盖已选择的文案"
              onConfirm={() => {
                if (copyNoticeRules.length === 0) {
                  message.error('您还未复制内容，请先去复制');
                  return;
                }
                updateMutateTplMonitorItemNotifyRules({
                  id: monitoringItem.id,
                  notifyRules: copyNoticeRules,
                });
              }}
            >
              <Button size="small" data-type="reset-btn">
                粘贴
              </Button>
            </Popconfirm>
            <Button
              size="small"
              data-type="reset-btn"
              onClick={() => {
                onResetNotification();
              }}
            >
              重置
            </Button>
          </Space>
        </div>
      </div>
      <Typography.Text type="secondary">
        {fieldsExample && '结果示例：' + fieldsExample}
      </Typography.Text>
    </Space>
  );
};

function PopoverField({ id, label, text, onFieldTextOk, onDelete }: unknown) {
  const [visible, setVisible] = useState(false);
  return (
    <Popover
      key={id}
      open={visible}
      title={label}
      content={
        <TextEditor
          text={text}
          onOk={txt => {
            if (!txt || !txt.trim()) {
              return;
            }
            if (txt.length > 50) {
              message.error('自定义文案最大可输入50个字符，请删减输入的文案字符');
              return;
            }
            onFieldTextOk(txt);
            setVisible(false);
          }}
        />
      }
      placement="topLeft"
      // getPopupContainer={() => document.querySelector('.layout_content--root')}
    >
      <Tag
        style={{ cursor: 'pointer' }}
        closable
        onClose={() => {
          setVisible(false);
          onDelete();
        }}
        onClick={() => {
          setVisible(prevVisible => !prevVisible);
        }}
      >
        {text || label}
      </Tag>
    </Popover>
  );
}

function TextEditor({ onOk, text }: { onOk: (txt: string) => void; text: string }) {
  const [txt, setTxt] = useState(text);

  return (
    <Space>
      <Input
        defaultValue={text}
        size="small"
        onChange={({ target: { value } }) => {
          setTxt(value);
        }}
      />
      <Button
        size="small"
        onClick={() => {
          onOk(txt);
        }}
      >
        确定
      </Button>
    </Space>
  );
}

function renderTitle(description: string) {
  return description && description.indexOf('_$$_') ? description.split('_$$_')[0] : description;
}

export const DragField = ({
  label,
  value,
  index,
  description,
}: Omit<NotifyRule, 'id'> & { index: number }) => {
  return (
    <Draggable draggableId={value} index={index}>
      {provided => (
        <Tooltip title={renderTitle(description)}>
          <span
            ref={provided.innerRef}
            className={styles.dndItemWrapper}
            {...provided.draggableProps}
            {...provided.dragHandleProps}
          >
            {label}
          </span>
        </Tooltip>
      )}
    </Draggable>
  );
};
