import React, { useCallback, useEffect } from 'react';
import { DragDropContext, Droppable } from 'react-beautiful-dnd';
import { useDispatch, useSelector } from 'react-redux';

import shallowequal from 'shallowequal';
import shortid from 'shortid';

import { Card } from '@manyun/base-ui.ui.card';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';

import {
  useCurrentMutateType,
  useMutateTplFields,
} from '@manyun/monitoring.hook.use-alarm-configuration-template';
import { MonitoringItem, NotifyRule } from '@manyun/monitoring.model.monitoring-item';

// @ts-ignore
import { getNotificationTxtTplFieldsAction } from '@manyun/dc-brain.legacy.redux/actions/alarmActions';

import type { MonitoringItemNoticeInfo } from './alarm-notice-tpl-cfg';
import styles from './dragable-card.module.less';
import { DragField, DropZone } from './drop-zone';

export type NotificationTxtTplCfgCardProps = {
  monitoringItem: MonitoringItemNoticeInfo;
  tplSelectVal?: string;
};
export const NotificationTxtTplCfgCard = React.forwardRef<any, NotificationTxtTplCfgCardProps>(
  ({ monitoringItem, tplSelectVal }, ref?) => {
    const alarmCopyList = useSelector(selectAlarmCopyList);
    const dispatch = useDispatch();
    const [currenMutateType] = useCurrentMutateType();
    const [, { updateMutateTplMonitorItemNotifyRules }] = useMutateTplFields(currenMutateType);

    const dragEndHandler = useCallback(
      ({ source, destination, draggableId }) => {
        if (!destination || shallowequal(source, destination)) {
          return;
        }
        let newNotifyRules: NotifyRule[] = [...(monitoringItem.notifyRules ?? [])];
        if (source.droppableId === 'items' && destination.droppableId === 'notification') {
          const tmp = [
            ...alarmCopyList.physicalFields,
            ...alarmCopyList.faultedFields,
            ...alarmCopyList.otherFields,
          ].filter(item => item.metaCode === draggableId);
          const newRule: NotifyRule = {
            id: shortid(),
            label: tmp[0] ? tmp[0].metaName : '',
            value: draggableId,
            description: tmp[0] ? tmp[0].description : '',
          };
          if (!newNotifyRules[destination.index]) {
            newNotifyRules.push(newRule);
          } else {
            newNotifyRules.splice(destination.index, 0, newRule);
          }
          const notifyRuleString = MonitoringItem.toApiNotifyRule(newNotifyRules);
          if (notifyRuleString.length > 500) {
            message.error('文案输入字符数超长，请删减输入的文案字符');
            return;
          }
        }
        if (source.droppableId === 'notification' && destination.droppableId === 'notification') {
          if (source.index === destination.index) {
            return;
          }
          const startIndex = source.index;
          const endIndex = destination.index;
          newNotifyRules = Array.from(monitoringItem.notifyRules ?? []);
          const [removed] = newNotifyRules.splice(startIndex, 1);
          newNotifyRules.splice(endIndex, 0, removed);
        }
        updateMutateTplMonitorItemNotifyRules({
          id: monitoringItem.id,
          notifyRules: newNotifyRules,
        });
      },
      [
        alarmCopyList.faultedFields,
        alarmCopyList.otherFields,
        alarmCopyList.physicalFields,
        monitoringItem.id,
        monitoringItem.notifyRules,
        updateMutateTplMonitorItemNotifyRules,
      ]
    );

    useEffect(() => {
      if (!alarmCopyList.physicalFields.length) {
        dispatch(getNotificationTxtTplFieldsAction());
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
      <DragDropContext onDragEnd={dragEndHandler}>
        <div ref={ref}>
          <Space style={{ width: '100%' }} direction="vertical">
            <DropZone monitoringItem={monitoringItem} tplSelectVal={tplSelectVal} />
            <Droppable isDropDisabled droppableId="items" direction="horizontal">
              {provided => (
                <div ref={provided.innerRef} {...provided.droppableProps}>
                  <Space className={styles.droppableZoneContainer} align="start">
                    <Card size="small" title="物理信息" bodyStyle={{ minHeight: '160px' }}>
                      <Space direction="horizontal" size="middle" wrap>
                        {alarmCopyList.physicalFields.map(
                          ({ metaName, metaCode, description }, index) => (
                            <DragField
                              key={metaCode}
                              label={metaName}
                              value={metaCode}
                              index={index}
                              description={description}
                            />
                          )
                        )}
                      </Space>
                    </Card>
                    <Card size="small" title="故障信息" bodyStyle={{ minHeight: '160px' }}>
                      <Space direction="horizontal" size="middle" wrap>
                        {alarmCopyList.faultedFields.map(
                          ({ metaName, metaCode, description }, index) => (
                            <DragField
                              key={metaCode}
                              label={metaName}
                              value={metaCode}
                              description={description}
                              index={alarmCopyList.physicalFields.length + index}
                            />
                          )
                        )}
                      </Space>
                    </Card>
                    <Card size="small" title="其他" bodyStyle={{ minHeight: '160px' }}>
                      <Space direction="horizontal" size="middle" wrap>
                        {alarmCopyList.otherFields.map(
                          ({ metaName, metaCode, description }, index) => (
                            <DragField
                              key={metaCode}
                              label={metaName}
                              value={metaCode}
                              description={description}
                              index={
                                alarmCopyList.physicalFields.length +
                                alarmCopyList.faultedFields.length +
                                index
                              }
                            />
                          )
                        )}
                      </Space>
                    </Card>
                    {provided.placeholder}
                  </Space>
                </div>
              )}
            </Droppable>
          </Space>
        </div>
      </DragDropContext>
    );
  }
);

type AlarmCopyListProps = {
  physicalFields: any[];
  faultedFields: any[];
  otherFields: any[];
};
export const selectAlarmCopyList = ({
  alarmManage: { alarmCopyList },
}: {
  alarmManage: { alarmCopyList: AlarmCopyListProps };
}): AlarmCopyListProps => {
  return alarmCopyList;
};
