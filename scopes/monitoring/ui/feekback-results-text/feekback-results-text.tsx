import React from 'react';

import { Tag } from '@manyun/base-ui.ui.tag';

import { Feedback, getAiOpsAlarmLocales } from '@manyun/monitoring.model.ai-ops-alarm';

export type FeekbackResultsTextProps = {
  code: Feedback;
};

const alarmStatusTagColor: Record<Feedback, string> = {
  [Feedback.On]: 'green',
  [Feedback.Off]: 'error',
};

export function FeekbackResultsText({ code }: FeekbackResultsTextProps) {
  const alarmLocales = getAiOpsAlarmLocales();

  return <Tag color={alarmStatusTagColor[code]}>{alarmLocales.feedback[code]}</Tag>;
}
