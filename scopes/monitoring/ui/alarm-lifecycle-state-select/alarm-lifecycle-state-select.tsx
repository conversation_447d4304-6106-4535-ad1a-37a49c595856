import React, { useMemo } from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { OptionProps, SelectProps } from '@manyun/base-ui.ui.select';

import { getAlarmLocales } from '@manyun/monitoring.model.alarm';

type Option = Omit<OptionProps, 'children'>;

export type AlarmLifecycleStateSelectProps = {
  optionsFilter?: (option: Option, index: number, options: Option[]) => boolean;
} & Omit<SelectProps, 'options'>;

export const AlarmLifecycleStateSelect = ({
  optionsFilter,
  ...resetProps
}: AlarmLifecycleStateSelectProps) => {
  const options: SelectProps['options'] = useMemo(() => {
    const locales = getAlarmLocales();
    const newOptions = [
      { value: 'ACTIVE', label: locales.lifecycleState.ACTIVE },
      { value: 'CONFIRMED', label: locales.lifecycleState.CONFIRMED },
      { value: 'PROCESS', label: locales.lifecycleState.PROCESS },
      { value: 'REMOVED', label: locales.lifecycleState.REMOVED },
    ];
    if (optionsFilter) {
      return newOptions.filter(optionsFilter);
    }
    return newOptions;
  }, [optionsFilter]);

  return <Select {...resetProps} options={options} />;
};
