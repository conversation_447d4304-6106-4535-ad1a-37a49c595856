/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-2-21
 *
 * @packageDocumentation
 */
import React from 'react';

import { Tag } from '@manyun/base-ui.ui.tag';

import type { AlarmShieldStatusCode } from '@manyun/monitoring.gql.client.monitoring';

export type AlarmShieldStatusTextProps = { code: AlarmShieldStatusCode; name: string };

const statusTagColor: Record<AlarmShieldStatusCode, string> = {
  ACTIVE: 'processing',
  PENDING: 'warning',
  EXPIRED: 'default',
};

export function AlarmShieldStatusText({ name, code }: AlarmShieldStatusTextProps) {
  if (!code) {
    return '--';
  }
  return <Tag color={statusTagColor[code]}>{name}</Tag>;
}
