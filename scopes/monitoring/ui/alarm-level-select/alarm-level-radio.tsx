import React, { Ref, useMemo } from 'react';

import type { RadioProps } from '@manyun/base-ui.ui.radio';
import { Radio } from '@manyun/base-ui.ui.radio';
import { useMetaData } from '@manyun/resource-hub.hook.use-meta-data';
import { MetaType } from '@manyun/resource-hub.model.metadata';

export type AlarmLevelRadioProps = Omit<RadioProps, 'options'>;

/**
 * 告警等级单选
 */
export const AlarmLevelRadio = React.forwardRef(
  (props: AlarmLevelRadioProps, ref: Ref<HTMLDivElement> | undefined) => {
    const [{ data: alarmData }, { readMetaData }] = useMetaData(MetaType.ALARM_LEVEL);
    const alarmLevelOption = useMemo(
      () => alarmData.data.map(alarm => ({ label: alarm.label, value: alarm.value })),
      [alarmData]
    );

    React.useEffect(() => {
      readMetaData();
    }, [readMetaData]);

    return <Radio.Group ref={ref} {...props} options={alarmLevelOption} />;
  }
);

AlarmLevelRadio.displayName = 'AlarmLevelRadio';
