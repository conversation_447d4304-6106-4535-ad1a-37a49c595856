import React from 'react';

// import { ConfigProvider } from '@manyun/base-ui.context.config';
// import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { Form } from '@manyun/base-ui.ui.form';

// import { useRemoteMock, webRequest } from '@manyun/service.request';
import { AlarmLevelSelect } from './alarm-level-select';

export const BasicAlarmLevelSelect = () => {
  // const [ready, setReady] = React.useState(false);
  const [form] = Form.useForm();

  // React.useEffect(() => {
  //   const mockOff = useRemoteMock('web', {
  //     adapter: 'default',
  //   });
  //   setReady(true);

  //   return () => {
  //     mockOff();
  //   };
  // }, []);

  // React.useEffect(() => {
  //   webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
  //   setReady(true);
  // }, []);

  // if (!ready) {
  //   return null;
  // }

  return (
    // <ConfigProvider>
    //   <FakeStore>
    <Form form={form}>
      <Form.Item label="告警级别" name="ids">
        <AlarmLevelSelect style={{ width: 200 }} />
      </Form.Item>
    </Form>
    //   </FakeStore>
    // </ConfigProvider>
  );
};

export default BasicAlarmLevelSelect;
