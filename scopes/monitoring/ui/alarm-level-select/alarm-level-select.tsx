import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { SelectProps } from '@manyun/base-ui.ui.select';
import { useMetaData } from '@manyun/resource-hub.hook.use-meta-data';
import { MetaType } from '@manyun/resource-hub.model.metadata';

type RefSelectProps = {
  focus: () => void;
  blur: () => void;
  scrollTo: () => void;
};

export type AlarmLevelSelectProps = {
  /**
   * 禁用等级
   */
  disbaledLevels?: string[];
  trigger?: 'onFocus' | 'onDidMount';
} & SelectProps<unknown>;

export const AlarmLevelSelect = React.forwardRef(
  (
    { disbaledLevels = [], trigger = 'onFocus', onFocus, ...props }: AlarmLevelSelectProps,
    ref?: React.Ref<RefSelectProps>
  ) => {
    const [{ data }, { readMetaData }] = useMetaData(MetaType.ALARM_LEVEL);

    React.useEffect(() => {
      if (trigger === 'onDidMount') {
        readMetaData();
      }
    }, [readMetaData, trigger]);

    return (
      <Select
        ref={ref}
        {...props}
        value={typeof props?.value === 'number' ? String(props?.value) : props?.value}
        options={data.data.map(d => ({ ...d, disabled: disbaledLevels.includes(d.value) }))}
        onFocus={evt => {
          if (trigger === 'onFocus') {
            readMetaData();
          }
          onFocus?.(evt);
        }}
      />
    );
  }
);

AlarmLevelSelect.displayName = 'AlarmLevelSelect';
