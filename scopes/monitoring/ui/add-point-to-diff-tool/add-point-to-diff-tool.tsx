import React, { useEffect, useState } from 'react';

import { FolderAddOutlined } from '@manyun/base-ui.icons';
import { message } from '@manyun/base-ui.ui.message';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';

import { useLayout } from '@manyun/dc-brain.context.layout';
import type { PointGuid } from '@manyun/monitoring.chart.points-line';
import type { DeviceType } from '@manyun/monitoring.ui.device-diff-tool';
import {
  getState as getPointState,
  saveState as savePointState,
  setExportData,
} from '@manyun/monitoring.ui.point-diff-tool';
import type { PointType } from '@manyun/monitoring.ui.point-diff-tool';
import { fetchDeviceByGuid } from '@manyun/resource-hub.service.fetch-device-by-guid';
import { fetchPointsByCondition } from '@manyun/resource-hub.service.fetch-points-by-condition';

export type AddPointToDiffToolProps = {
  pointGuids: PointGuid[];
  onCloseModal?: () => void;
  style?: React.CSSProperties;
};

export function AddPointToDiffTool({ pointGuids, style, onCloseModal }: AddPointToDiffToolProps) {
  const { setSideBarVisible, setSideBarTab } = useLayout();
  const [isShow, setIsShow] = useState<boolean>(false);
  const [device, setDevice] = useState<DeviceType>();

  useEffect(() => {
    getDeviceByGuid();
    // eslint-disable-next-line
  }, []);

  const getDeviceByGuid = async () => {
    if (!pointGuids.length) {
      return;
    }
    const { deviceGuid } = pointGuids[0];

    const { data, error } = await fetchDeviceByGuid({ guid: deviceGuid });

    if (error) {
      return;
    }
    setIsShow(true);

    setDevice(data.toApiObject());
  };

  const onAddPointToDiffTool = async () => {
    if (!device) {
      return;
    }
    const { pointCode } = pointGuids[0];
    const { deviceType } = device;
    const {
      data: { data },
    } = await fetchPointsByCondition({ deviceType });
    const pointList = data.filter(item => item.code === pointCode);
    if (!pointList.length) {
      message.error('添加失败');
      return;
    }
    const pointItem = pointList[0];

    const deviceItem = { ...device, pointList: [pointItem] };
    const pointState = getPointState();
    const maxLength = pointState.reduce((result: number, item: DeviceType) => {
      const { pointList = [] } = item;
      return result + pointList.length;
    }, 0);

    if (maxLength > 19) {
      message.error('添加失败，测点比对个数已达上限');
      return;
    }

    const guids = pointState.map((item: DeviceType) => item.guid);

    if (guids.includes(deviceItem.guid)) {
      const list = pointState.map((item: DeviceType) => {
        if (item.guid === deviceItem.guid) {
          const { pointList = [] } = item;
          const pointCodes = pointList.map((item: PointType) => item.code);
          if (pointCodes.includes(pointItem.code)) {
            message.error('添加失败，该测点已存在于测点比对');
          } else {
            pointList.push(pointItem);
            message.success('添加成功');
            onCloseModal && onCloseModal();
            setSideBarVisible(true);
            setSideBarTab('pointdifftool');
          }
        }
        return item;
      });
      savePointState(list);
    } else {
      const selectDevices = [...getPointState(), { ...deviceItem, pointList: [pointItem] }];
      await setExportData({ selectDevices, idc: device.spaceGuid.idcTag });

      message.success('添加成功');
      savePointState(selectDevices);
      onCloseModal && onCloseModal();
      setSideBarVisible(true);
      setSideBarTab('pointdifftool');
    }
  };

  return (
    <>
      {isShow && (
        <Tooltip title="将该测点添加至测点对比">
          <span style={style} onClick={onAddPointToDiffTool}>
            <FolderAddOutlined />
          </span>
        </Tooltip>
      )}
    </>
  );
}
