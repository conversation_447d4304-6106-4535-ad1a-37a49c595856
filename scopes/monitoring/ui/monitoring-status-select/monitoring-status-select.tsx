import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

import { getAiOpsAlarmLocales } from '@manyun/monitoring.model.ai-ops-alarm';

export type MonitoringStatusSelectProps = Omit<SelectProps, 'options'>;

export const MonitoringStatusSelect = React.forwardRef(
  (props: SelectProps, ref: React.Ref<RefSelectProps>) => {
    const locales = getAiOpsAlarmLocales();

    const options: SelectProps['options'] = [
      { value: 'TRIGGER', label: locales.triggerStatus.TRIGGER },
      { value: 'RECOVER', label: locales.triggerStatus.RECOVER },
    ];

    return <Select {...props} ref={ref} options={options} />;
  }
);

MonitoringStatusSelect.displayName = 'MonitoringStatusSelect';
