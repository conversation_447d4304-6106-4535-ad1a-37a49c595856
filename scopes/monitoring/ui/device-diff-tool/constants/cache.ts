import type { DeviceType } from '@manyun/monitoring.ui.device-diff-tool';

const LOCAL_KEY = 'DC_BASE_DEVICE_CONTRAST_DATA';

export function getState() {
  try {
    const localData = localStorage.getItem(LOCAL_KEY);
    if (localData === null) {
      return [];
    }
    return JSON.parse(localData);
  } catch (error) {}
}

export function saveState(obj: DeviceType[]) {
  try {
    localStorage.setItem(LOCAL_KEY, JSON.stringify(obj));
  } catch (error) {}
}

export function clearState() {
  window.localStorage.removeItem(LOCAL_KEY);
}
