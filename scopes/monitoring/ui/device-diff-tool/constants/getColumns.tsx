import { SelectOutlined } from '@ant-design/icons';
import get from 'lodash.get';
import React from 'react';

import type { ColumnsType } from '@manyun/base-ui.ui.table';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import { generateRoomMonitoringUrl } from '@manyun/monitoring.route.monitoring-routes';
import type { PointType } from '@manyun/monitoring.ui.point-diff-tool';
import { SpaceOrDeviceLink } from '@manyun/resource-hub.ui.space-or-device-link';

import { PointRender } from '../components/point-render';
import type { DeviceType } from '../device-diff-tool.type';
import styles from '../device.module.less';

const BLANK_PLACEHOLDER = '--';

type ColumnCode = {
  name: string;
  code: string;
};

export const deviceModalColumns = [
  {
    title: '设备名称',
    dataIndex: 'name',
  },
  {
    title: '设备标签',
    dataIndex: 'deviceLabel',
  },
  {
    title: '设备类型',
    dataIndex: 'deviceTypeName',
  },
  {
    title: '位置',
    dataIndex: 'spaceGuid',
    render: ({
      idcTag,
      blockTag,
      roomTag,
    }: {
      idcTag: string;
      blockTag: string;
      roomTag: string;
    }) => {
      return `${idcTag}.${blockTag}.${roomTag}`;
    },
  },
];
export const getBasicColumns = (list: DeviceType[], onCloseSidebar: () => void) => {
  if (!list.length) {
    return [];
  }
  const basicColumns: ColumnsType = list.map((deviceitem: DeviceType) => {
    return {
      title: '',
      dataIndex: 'renderItem',
      width: 175,
      render: (_: unknown, record: unknown, index: number) => {
        const { list } = record;
        const renderItem = list.find((item: DeviceType) => item.guid === deviceitem.guid) || {};
        const { guid, name, deviceTypeName, spaceGuid = {} } = renderItem;
        const { idcTag, blockTag, roomTag } = spaceGuid;
        if (index === 0) {
          return (
            <div onClick={onCloseSidebar}>
              <SpaceOrDeviceLink id={guid} type="DEVICE_GUID" text={name} />
            </div>
          );
        }
        if (index === 1) {
          return deviceTypeName;
        }
        if (index === 2) {
          return (
            <Typography.Link
              onClick={() => {
                window.open(
                  generateRoomMonitoringUrl({
                    idc: idcTag,
                    block: blockTag,
                    room: roomTag,
                  })
                );
                onCloseSidebar();
              }}
            >
              {`${idcTag}.${blockTag}.${roomTag}`}
            </Typography.Link>
          );
        }
      },
    };
  });
  basicColumns.unshift({
    title: '',
    dataIndex: 'name',
    width: 175,
    fixed: 'left',
  });
  return basicColumns;
};

const basicColumnsData: ColumnCode[] = [
  {
    name: '设备分类',
    code: 'topCategoryName',
  },
  {
    name: '设备位置',
    code: 'spaceGuidName',
  },
];
export const getBasicList = (list: DeviceType[], type: string) => {
  if (!list.length) {
    return [];
  }

  const data = basicColumnsData.map((item: ColumnCode) => {
    return {
      key: item.code,
      name: item.name,
      list,
    };
  });

  data.unshift({
    key: '1',
    name: '',
    list,
  });
  return data;
};

export const basicInfoColumnsData: ColumnCode[] = [
  {
    name: '一级分类',
    code: 'topCategoryName',
  },
  {
    name: '二级分类',
    code: 'secondCategoryName',
  },
  {
    name: '三级分类',
    code: 'deviceTypeName',
  },
  {
    name: '设备名称',
    code: 'name',
  },
  {
    name: '设备标签',
    code: 'deviceLabel',
  },
  {
    name: '映射标签',
    code: 'tag',
  },
  {
    name: 'SN',
    code: 'serialNumber',
  },
  {
    name: '父设备名称',
    code: 'parentName',
  },
  {
    name: '机房编号',
    code: 'idcTag',
  },
  {
    name: '楼栋编号',
    code: 'blockTag',
  },
  {
    name: '包间编号',
    code: 'roomTag',
  },
  {
    name: '扩展位置',
    code: 'extendPosition',
  },
  {
    name: '厂商',
    code: 'vendor',
  },
  {
    name: '型号',
    code: 'productModel',
  },
  {
    name: '设备状态',
    code: 'operationStatusName',
  },
  {
    name: '所属线路',
    code: 'powerLine',
  },
  {
    name: '所属单元',
    code: 'unit',
  },
];

const getBasicInfoColumnsData = (list: DeviceType[], columnsData: ColumnCode[], type: string) => {
  if (list.length === 0 || type === 'all') {
    return columnsData;
  }
  if (list.length === 1 && type === 'same') {
    return columnsData;
  }
  if (list.length === 1 && type === 'hideall') {
    type = 'empty';
  }
  const newBasicInfoColumns = columnsData.filter(columnCodeItem => {
    const { code } = columnCodeItem;
    const codeValues = list.map((deviceItem: DeviceType) => deviceItem[code]);
    let deviceColumnswitch;
    if (type === 'hideall') {
      deviceColumnswitch =
        codeValues.every(item => item) && !codeValues.every(item => item === codeValues[0]);
    } else if (type === 'empty') {
      deviceColumnswitch = codeValues.every(item => item);
    } else if (type === 'same') {
      deviceColumnswitch = !codeValues.every(item => item === codeValues[0]);
    } else {
      return true;
    }
    return deviceColumnswitch;
  });
  return newBasicInfoColumns;
};

export const getBasicInfoList = (list: DeviceType[], type: string) => {
  if (!list.length) {
    return [];
  }
  const data = getBasicInfoColumnsData(list, basicInfoColumnsData, type).map((item: ColumnCode) => {
    return {
      key: item.code,
      name: item.name,
      list,
    };
  });
  return data;
};
export const getBasicInfoColumns = (list: DeviceType[], type: string) => {
  const columns: ColumnsType = list.map(item => {
    const { guid } = item;
    return {
      title: '',
      dataIndex: 'renderItem',
      width: 175,
      align: 'left',
      render: (_: unknown, record: unknown, index: number) => {
        const { list } = record;
        const renderItem = list.find((item: { guid: string }) => item.guid === guid);
        return (
          get(
            renderItem,
            getBasicInfoColumnsData(list, basicInfoColumnsData, type)[index].code,
            BLANK_PLACEHOLDER
          ) || BLANK_PLACEHOLDER
        );
      },
    };
  });
  columns.unshift({
    title: '',
    dataIndex: 'name',
    width: 175,
    fixed: 'left',
  });
  return columns;
};

export const assetInfoColumnsData: ColumnCode[] = [
  {
    name: '资产ID',
    code: 'assetNo',
  },
  {
    name: '资产状态',
    code: 'assetStatusName',
  },
  {
    name: '购买日期',
    code: 'purchaseTimeName',
  },
  {
    name: '验收日期',
    code: 'checkTimeName',
  },
  {
    name: '启用日期',
    code: 'enableTimeName',
  },
  {
    name: '过保日期',
    code: 'scrapTimeName',
  },
  {
    name: '报废日期',
    code: 'warrantyTimeName',
  },
  {
    name: '维保状态',
    code: 'warrantyStatusName',
  },
  {
    name: '维保厂商',
    code: 'warrantyVendor',
  },
];

export const getAssetInfoList = (list: DeviceType[], type: string) => {
  if (!list.length) {
    return [];
  }
  const data = getBasicInfoColumnsData(list, assetInfoColumnsData, type).map((item: ColumnCode) => {
    return {
      key: item.code,
      name: item.name,
      list,
    };
  });
  return data;
};

export const getAssetInfoColumns = (list: DeviceType[], type: string) => {
  const assetInfoColumns: ColumnsType = list.map(item => {
    const { guid } = item;
    return {
      title: '',
      dataIndex: 'renderItem',
      width: 175,
      align: 'left',
      render: (_: unknown, record: unknown, index: number) => {
        const { list } = record;
        const renderItem = list.find((item: { guid: string }) => item.guid === guid);
        return (
          get(
            renderItem,
            getBasicInfoColumnsData(list, assetInfoColumnsData, type)[index].code,
            BLANK_PLACEHOLDER
          ) || BLANK_PLACEHOLDER
        );
      },
    };
  });
  assetInfoColumns.unshift({
    title: '',
    dataIndex: 'name',
    width: 175,
    fixed: 'left',
  });
  return assetInfoColumns;
};

export const getParamsInfoList = (list: DeviceType[], type: string) => {
  if (!list.length) {
    return [];
  }
  const { paramList = [] } = list[0];
  if (!paramList.length) {
    return [];
  }

  const newParamListArr = paramList.filter((_: unknown, paramIndex: number) => {
    const newParamItem = list.map((deviceItem: DeviceType) => {
      return deviceItem.paramList[paramIndex].specValue + deviceItem.paramList[paramIndex].specUnit;
    });

    if (type === 'hideall') {
      return (
        !newParamItem.every((item: string) => item === newParamItem[0]) &&
        newParamItem.every(item => item)
      );
    } else if (type === 'same') {
      return !newParamItem.every((item: string) => item === newParamItem[0]);
    } else if (type === 'empty') {
      return newParamItem.every(item => item);
    } else {
      return true;
    }
  });
  const sourceData = newParamListArr.map((item: unknown) => {
    return {
      key: item.id,
      name: item.specName + (item.specUnit ? item.specUnit : ''),
      list,
    };
  });
  return sourceData;
};

export const getParamsInfoColumns = (list: DeviceType[]) => {
  const paramsInfoColumns: ColumnsType = list.map(item => {
    const { guid } = item;
    return {
      title: '',
      dataIndex: 'renderItem',
      width: 175,
      align: 'left',
      render: (_: unknown, record: unknown, index: number) => {
        const { list } = record;
        const renderItem = list.find((item: { guid: string }) => item.guid === guid);
        const { paramList = [] } = renderItem;
        const renderText = get(paramList[index], 'specValue', null);
        return renderText
          ? `${renderText}${paramList[index].specUnit ? paramList[index].specUnit : ''}`
          : '--';
      },
    };
  });
  paramsInfoColumns.unshift({
    title: '',
    dataIndex: 'name',
    width: 175,
    fixed: 'left',
  });
  return paramsInfoColumns;
};

export const getCorePointList = (deviceList: DeviceType[], pointType: string) => {
  if (!deviceList.length) {
    return [];
  }
  let newPointList: PointType[] = [];
  const { pointList = [], otherPointList = [] } = deviceList[0];
  if (!pointList.length && pointType === 'point') {
    return [];
  }
  if (!otherPointList.length && pointType === 'otherPoint') {
    return [];
  }
  if (pointType === 'point') {
    newPointList = pointList;
  } else {
    newPointList = otherPointList;
  }

  const list = newPointList.map(item => {
    const { code, name } = item;
    return {
      key: code,
      name,
      list: deviceList,
    };
  });
  return list;
};

export const getCorePointColumns = (
  deviceList: DeviceType[],
  pointType: string,
  onAddToPointDiffPage: (arg1: DeviceType, arg2: PointType) => void
) => {
  if (!deviceList.length) {
    return [];
  }
  let newPointList: PointType[] = [];
  const { pointList = [], otherPointList = [] } = deviceList[0];
  if (!pointList.length && pointType === 'point') {
    return [];
  }
  if (!otherPointList.length && pointType === 'otherPoint') {
    return [];
  }
  if (pointType === 'point') {
    newPointList = pointList;
  } else {
    newPointList = otherPointList;
  }
  const coreColumns: ColumnsType<unknown> = deviceList.map(deviceItem => {
    return {
      title: '',
      dataIndex: 'renderItem',
      width: 175,
      render: (_: unknown, record, index: number) => {
        const { list } = record;
        const renderItem =
          list.find((item: { guid: string }) => item.guid === deviceItem.guid) || {};
        return (
          <div className={styles.pointRender}>
            <PointRender device={renderItem} point={newPointList[index]} />
            <Tooltip placement="top" title="将该测点添加至测点对比">
              <SelectOutlined
                className={styles.pointRenderIcon}
                onClick={() => onAddToPointDiffPage(renderItem, newPointList[index])}
              />
            </Tooltip>
          </div>
        );
      },
    };
  });
  coreColumns.unshift({
    title: '',
    dataIndex: 'name',
    width: 175,
    fixed: 'left',
  });
  return coreColumns;
};
