import { message } from '@manyun/base-ui.ui.message';

import {
  getState as getPointState,
  saveState as savePointState,
} from '@manyun/monitoring.ui.point-diff-tool';
import type { PointType } from '@manyun/monitoring.ui.point-diff-tool';

import type { DeviceType } from '../device-diff-tool.type';
import { assetInfoColumnsData, basicInfoColumnsData } from './getColumns';

type ColumnCode = {
  name: string;
  code: string;
};

export const fullScreenStyle = {
  width: '100vw',
  height: '100vh',
  position: 'fixed',
  top: 0,
  left: 0,
  zIndex: 99,
};

export const tabList = [
  {
    key: 'point',
    tab: '设备测点对比',
  },
  {
    key: 'archives',
    tab: '设备档案对比',
  },
];

export const onAddToPointDiffPage = (deviceItem: DeviceType, pointItem: PointType) => {
  const pointState = getPointState();
  const maxLength = pointState.reduce((result: number, item: DeviceType) => {
    const { pointList = [] } = item;
    return result + pointList.length;
  }, 0);

  if (maxLength > 19) {
    message.error('添加失败，测点比对个数已达上限');
    return;
  }

  const guids = pointState.map((item: DeviceType) => item.guid);

  if (guids.includes(deviceItem.guid)) {
    const list = pointState.map((item: DeviceType) => {
      if (item.guid === deviceItem.guid) {
        const { pointList = [] } = item;
        const pointCodes = pointList.map((item: PointType) => item.code);
        if (pointCodes.includes(pointItem.code)) {
          message.error('添加失败，该测点已存在于测点比对');
        } else {
          pointList.push(pointItem);
          message.success('添加成功');
        }
      }
      return item;
    });
    savePointState(list);
  } else {
    message.success('添加成功');
    savePointState([...getPointState(), { ...deviceItem, pointList: [pointItem] }]);
  }
};

export const getExportData = (list: DeviceType[]) => {
  if (!list.length) {
    return [];
  }
  const headList = ['设备名称', '设备分类', '设备位置'];
  basicInfoColumnsData.forEach(item => {
    headList.push(item.name);
  });
  assetInfoColumnsData.forEach(item => {
    headList.push(item.name);
  });

  let { paramList = [] } = list[0];
  if (paramList.length) {
    paramList.forEach((item: { specName: string }) => {
      headList.push(item.specName);
    });
  }

  const contentList = list.map((item: DeviceType) => {
    const { name, deviceTypeName, spaceGuidName, paramList: newParamList } = item;
    const exportItem = [name, deviceTypeName, spaceGuidName];
    basicInfoColumnsData.forEach((basicitem: ColumnCode) => {
      const { code } = basicitem;
      exportItem.push(item[code]);
    });
    assetInfoColumnsData.forEach((basicitem: ColumnCode) => {
      const { code } = basicitem;
      exportItem.push(item[code]);
    });
    if (newParamList.length) {
      newParamList.forEach((paramsItem: { specValue: number; specUnit: string }) => {
        exportItem.push(paramsItem.specValue + paramsItem.specUnit);
      });
    }
    return exportItem;
  });

  return [headList, ...contentList];
};
