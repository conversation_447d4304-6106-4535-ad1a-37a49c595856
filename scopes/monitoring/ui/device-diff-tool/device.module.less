@import '~@manyun/base-ui.theme.theme/dist/theme.variable.less';

.container {
  width: calc(100vw - 212px);
  :global(.manyun-card-head) {
    font-weight: 400;
  }
  :global(.manyun-card-body) {
    padding: 0;
  }
  .content {
    :global(.manyun-card-body) {
      padding: 24px;
      color: @text-color-secondary;
    }
    :global(.manyun-card-head) {
      padding: 0 24px 0 40px;
    }
    :global(.manyun-tabs-tab) {
      height: 58px;
      font-size: 14px;
      &:hover {
        background-color: transparent;
      }
    }
    :global(.manyun-tabs-tab-active) {
      background-color: transparent;
    }
    .contentIcon {
      position: absolute;
      top: 19px;
      left: 10px;
      font-size: 18px;
      color: @text-color-secondary;
      cursor: pointer;
    }
  }
  .contentLeft {
    // & > div:nth-child(1) {
    //   height: calc(100% - 52px);
    // }
    .checkbox {
      width: 100%;
      padding: 24px;
      color: @text-color-secondary;
      .checkboxItem {
        padding: 12px 5px;
        cursor: pointer;
        &:hover {
          background-color: @primary-1;
        }
        .checkboxItemLabel {
          max-width: calc(100% - 36px);
          align-items: center;
        }
        .checkboxItemDelete {
          visibility: hidden;
        }
        &:hover > .checkboxItemDelete {
          visibility: inherit;
        }
      }
      .activeCheckboxItem {
        background-color: @primary-1;
      }
    }
  }
  :global(.manyun-collapse-content) {
    > :global(.manyun-collapse-content-box) {
      padding: 0;
    }
  }

  :global(.manyun-table-row-level-0) {
    > td:nth-child(1) {
      background-color: @background-color-light;
    }
  }
}
.pointRender {
  display: flex;
  align-items: center;
  justify-content: space-between;
  &:hover {
    background-color: @primary-1;
  }
  .pointRenderIcon {
    color: @primary-color;
    visibility: hidden;
    cursor: pointer;
  }
  &:hover .pointRenderIcon {
    visibility: inherit;
  }
}
.download {
  color: @text-color-secondary;
}
