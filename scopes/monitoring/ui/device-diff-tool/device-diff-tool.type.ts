import type { ReactNode } from 'react';

import type { PointType } from '@manyun/monitoring.ui.point-diff-tool';
import type { BackendSpec, SpecJSON } from '@manyun/resource-hub.model.spec';

export interface DeviceType {
  key?: string;
  guid: string;
  name: string;
  assetNo: string;
  deviceLabel: string;
  topCategory: string;
  secondCategory: string;
  deviceType: string;
  spaceGuid: {
    blockGuid: string;
    blockTag: string;
    idcGuid: string;
    idcTag: string;
    roomGuid: string;
    roomTag: string;
  };
  spaceGuidName?: string;
  serialNumber: string;
  vendor: string;
  productModel: string | null;
  operationStatus: { code: string; name: string };
  powerLine: string | null;
  unit: string | null;
  purchaseTime: string;
  checkTime: string;
  warrantyTime: string;
  enableTime: string;
  scrapTime: string;
  purchasePrice: number;
  warrantyPrice: number;
  warrantyStatusName: number;
  warrantyVendor: string;
  netWorth: number;
  pointList?: PointType[];
  otherPointList?: PointType[];
  topCategoryName?: string;
  secondCategoryName?: string;
  deviceTypeName?: string;
  paramList?: (BackendSpec & SpecJSON)[];
  idcTag?: string;
  blockTag?: string;
  roomTag?: string;
  operationStatusName?: string;
}

export type TableItemType = {
  key: number | string;
  name: string;
  list: DeviceType[];
};

export type DeviceDiffToolProps = {
  title?: ReactNode;
};
