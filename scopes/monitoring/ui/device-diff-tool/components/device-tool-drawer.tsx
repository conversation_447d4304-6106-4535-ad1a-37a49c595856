import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import dayjs from 'dayjs';
import get from 'lodash.get';

import { Button } from '@manyun/base-ui.ui.button';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';

import { selectMe } from '@manyun/auth-hub.state.user';
import { getDeviceTypeMetaData, syncCommonDataAction } from '@manyun/dc-brain.state.common';
import type { Device } from '@manyun/resource-hub.model.device';
import { fetchPointsByCondition } from '@manyun/resource-hub.service.fetch-points-by-condition';
import { fetchSpecs } from '@manyun/resource-hub.service.fetch-specs';
import type { TreeNode } from '@manyun/resource-hub.ui.resource-tree';
import { ResourceTree } from '@manyun/resource-hub.ui.resource-tree';

import { saveState } from '../constants/cache';
import type { DeviceType } from '../device-diff-tool.type';

const DATE_FORMAT = 'YYYY-MM-DD';

export type DeviceToolDrawerProps = {
  open: boolean;
  onClose: () => void;
  setDeviceContrastList: (arg: DeviceType[]) => void;
  deviceContrastList: DeviceType[];
  selectGuids: string[];
  setSelectGuids: (arg: string[]) => void;
  getMainData: (arg: DeviceType[]) => void;
};

export function DeviceToolDrawer({
  open,
  onClose,
  setDeviceContrastList,
  deviceContrastList,
  selectGuids,
  setSelectGuids,
  getMainData,
}: DeviceToolDrawerProps) {
  const dispatch = useDispatch();
  const { idc } = useSelector(selectMe, (left, right) => left.idc === right.idc);
  const deviceCategory = useSelector(getDeviceTypeMetaData);

  const deviceNormalizedList = deviceCategory ? deviceCategory.normalizedList : {};
  const [btnLoading, setBtnLoading] = useState<boolean>(false);

  const [checkedNodes, setCheckedNodes] = useState<TreeNode[]>([]);

  useEffect(() => {
    dispatch(syncCommonDataAction({ strategy: { deviceCategory: 'IF_NULL' } }));
  }, [dispatch]);

  useEffect(() => {
    if (open) {
      setCheckedNodes([]);
    }
  }, [open]);

  const handleOk = async () => {
    const selectedDevices: DeviceType[] = checkedNodes.map(item => {
      const device: Device = item.device;
      const backendDevice = device.toApiObject();

      const {
        topCategory,
        secondCategory,
        deviceType,
        spaceGuid: { idcTag, blockTag, roomTag },
        purchaseTime,
        checkTime,
        warrantyTime,
        enableTime,
        scrapTime,
        purchasePrice,
        netWorth,
        warrantyPrice,
      } = backendDevice;
      return {
        ...device.toApiObject(),
        topCategoryName: get(deviceNormalizedList, [topCategory, 'metaName'], topCategory),
        secondCategoryName: get(deviceNormalizedList, [secondCategory, 'metaName'], secondCategory),
        deviceTypeName: get(deviceNormalizedList, [deviceType, 'metaName'], deviceType),
        //铺平数据供定制column和删除隐藏项使用
        spaceGuidName: `${idcTag}.${blockTag}.${roomTag}`,
        idcTag,
        blockTag,
        roomTag,
        operationStatusName: get(backendDevice, ['operationStatus', 'name'], null),
        assetStatusName: get(backendDevice, ['assetStatus', 'name'], null),
        purchaseTimeName: purchaseTime ? dayjs(purchaseTime).format(DATE_FORMAT) : null,
        checkTimeName: checkTime ? dayjs(checkTime).format(DATE_FORMAT) : null,
        warrantyTimeName: warrantyTime ? dayjs(warrantyTime).format(DATE_FORMAT) : null,
        enableTimeName: enableTime ? dayjs(enableTime).format(DATE_FORMAT) : null,
        scrapTimeName: scrapTime ? dayjs(scrapTime).format(DATE_FORMAT) : null,
        purchasePriceName: purchasePrice ? '￥' + purchasePrice : null,
        netWorthName: netWorth ? '￥' + netWorth : null,
        warrantyPriceName: warrantyPrice ? '￥' + warrantyPrice : null,
        warrantyStatusName: get(backendDevice, ['warrantyStatus', 'name'], null),
      };
    });
    const selectedKeys = selectedDevices.map(item => item.guid);

    const isSelected = selectedKeys.some(selectedKey => selectGuids.includes(selectedKey));
    if (isSelected) {
      message.error('已选择过选中设备，请重新选择！');
      return;
    }
    const currentLength = deviceContrastList.length + selectedDevices.length;
    if (currentLength > 20) {
      message.error('添加失败，设备比对个数已达上限！');
      return;
    }
    if (!selectedDevices.length) {
      message.error('请选择至少一个设备！');
      return;
    }

    const deviceType = selectedDevices[0].deviceType;
    const isSameDeviceType =
      selectedDevices.every(item => item.deviceType === deviceType) &&
      deviceContrastList.every(item => item.deviceType === deviceType);
    if (!isSameDeviceType) {
      message.error('仅限于同类型设备进行对比');
      return;
    }

    setBtnLoading(true);
    //添加测点
    const {
      data: { data },
    } = await fetchPointsByCondition({
      deviceType: selectedDevices[0].deviceType,
      isOnlyCore: true,
    });

    const {
      data: { data: otherPointList },
    } = await fetchPointsByCondition({
      deviceType: selectedDevices[0].deviceType,
    });
    try {
      //添加设备参数信息
      const results = await Promise.all(
        selectedDevices.map(async (item: DeviceType) => {
          const { deviceType, productModel } = item;
          const { data: specsData } = await fetchSpecs({
            deviceType: deviceType,
            modelCode: productModel!,
          });
          item.paramList = [];
          item.otherPointList = otherPointList.filter(item => !item.priority);
          item.pointList = data;
          if (specsData) {
            item.paramList = specsData.data;
          }
          return item;
        })
      );
      setBtnLoading(false);

      const newDeviceContrastList = [...deviceContrastList, ...results];
      const newSelectGuids = [...selectedKeys, ...selectGuids];
      const selectDevices = newDeviceContrastList.filter(item => {
        return newSelectGuids.includes(item.guid);
      });

      setDeviceContrastList(newDeviceContrastList);
      saveState(newDeviceContrastList);
      setSelectGuids(newSelectGuids);
      getMainData(selectDevices);

      onClose();
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <Drawer
      bodyStyle={{ paddingTop: 0 }}
      title="添加设备"
      placement="left"
      width={400}
      open={open}
      extra={
        <Space>
          <Button onClick={onClose}>取消</Button>
          <Button type="primary" loading={btnLoading} onClick={handleOk}>
            确定
          </Button>
        </Space>
      }
      destroyOnClose
      onClose={onClose}
    >
      {idc && (
        <ResourceTree
          authorizedOnly
          showFilter
          checkable
          spaceGuid={idc}
          checkableNodes={['DEVICE']}
          fetchDevicesParams={{ operationStatus: 'ON' }}
          onCheck={(_, { checkedNodes }) => {
            setCheckedNodes(checkedNodes);
          }}
        />
      )}
    </Drawer>
  );
}
