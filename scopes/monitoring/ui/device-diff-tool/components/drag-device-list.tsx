import React from 'react';
import { DragDropContext, Draggable, Droppable } from 'react-beautiful-dnd';

import { DeleteOutlined, HolderOutlined } from '@ant-design/icons';
import classNames from 'classnames';

import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Row } from '@manyun/base-ui.ui.grid';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';

import type { DeviceType } from '@manyun/monitoring.ui.device-diff-tool';

import { saveState } from '../constants/cache';
import styles from '../device.module.less';

export function DragDeviceList({
  selectGuids,
  setSelectGuids,
  deviceList,
  setDeviceList,
  getMainData,
}: {
  selectGuids: string[];
  deviceList: DeviceType[];
  setSelectGuids: Function;
  setDeviceList: Function;
  getMainData: Function;
}) {
  const onCheckDeviceChange = (selectGuids: string[]) => {
    const selectDevices = deviceList.filter(item => {
      return selectGuids.includes(item.guid);
    });
    setSelectGuids(selectGuids);
    getMainData(selectDevices);
  };

  const onDelete = (guid: string) => {
    const newDeviceList = deviceList.filter(item => item.guid !== guid);
    const newSelectGuids = selectGuids.filter(oldGuid => oldGuid !== guid);
    setSelectGuids(newSelectGuids);

    const selectDevices = newDeviceList.filter(item => {
      return newSelectGuids.includes(item.guid);
    });
    saveState(newDeviceList);
    setDeviceList(newDeviceList);
    getMainData(selectDevices);
  };

  const onDragEnd = (result: any) => {
    const { source, destination } = result;
    if (!destination) {
      return;
    }
    const destinationIndex = destination.index ?? 0;
    const sourceIndex = source.index ?? 0;

    [deviceList[destinationIndex], deviceList[sourceIndex]] = [
      deviceList[sourceIndex],
      deviceList[destinationIndex],
    ];
    const selectDevices = deviceList.filter((item: DeviceType) => {
      return selectGuids.includes(item.guid);
    });
    saveState(deviceList);
    setDeviceList(deviceList);
    getMainData(selectDevices);
  };
  return (
    <>
      <DragDropContext onDragEnd={onDragEnd}>
        <Droppable droppableId="drag-device-list">
          {(provided: any) => (
            <Checkbox.Group
              className={styles.checkbox}
              value={selectGuids}
              onChange={onCheckDeviceChange}
              ref={provided.innerRef}
              {...provided.droppableProps}
            >
              {deviceList.map((item: DeviceType, index: number) => {
                const { guid, name } = item;
                return (
                  <Draggable draggableId={guid} index={index} key={guid}>
                    {(provided: any) => (
                      <Row
                        justify="space-between"
                        align="middle"
                        className={classNames(
                          styles.checkboxItem,
                          selectGuids.includes(guid) && styles.activeCheckboxItem
                        )}
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        {...provided.dragHandleProps}
                      >
                        <Checkbox className={styles.checkboxItemLabel} value={guid}>
                          {name}
                        </Checkbox>
                        <Space className={styles.checkboxItemDelete}>
                          <Tooltip placement="top" title="删除设备">
                            <DeleteOutlined onClick={() => onDelete(guid)} />
                          </Tooltip>
                          <Tooltip placement="top" title="拖拽可调整右侧列顺序">
                            <HolderOutlined />
                          </Tooltip>
                        </Space>
                      </Row>
                    )}
                  </Draggable>
                );
              })}
              {provided.placeholder}
            </Checkbox.Group>
          )}
        </Droppable>
      </DragDropContext>
    </>
  );
}
