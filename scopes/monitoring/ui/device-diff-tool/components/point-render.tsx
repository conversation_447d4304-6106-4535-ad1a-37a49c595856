import React from 'react';
import { useSelector } from 'react-redux';

import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { PointsLineModalButton } from '@manyun/monitoring.chart.points-line';
import { PointsStateLineModalButton } from '@manyun/monitoring.chart.points-state-line';
import { getMonitoringData } from '@manyun/monitoring.state.subscriptions';
import type { DeviceType } from '@manyun/monitoring.ui.device-diff-tool';
import { PointDataRenderer } from '@manyun/monitoring.ui.point-data-renderer';
import type { PointType } from '@manyun/monitoring.ui.point-diff-tool';
import { generateGetPointMonitoringData } from '@manyun/monitoring.util.get-monitoring-data';

import { getCurrentConfig } from '@manyun/dc-brain.legacy.redux/selectors/configSelectors';

type PointRenderProps = {
  point: PointType;
  device: DeviceType;
};

export function PointRender({ device, point }: PointRenderProps) {
  const {
    deviceType,
    guid,
    spaceGuid: { idcTag, roomGuid },
  } = device;
  const { code, name } = point;

  const {
    devicesRealtimeData,
    devicesAlarmsData,
  }: {
    devicesRealtimeData: any;
    devicesAlarmsData: any;
  } = useSelector(getMonitoringData);

  const configUtil = new ConfigUtil(useSelector(getCurrentConfig));
  const getPointData = generateGetPointMonitoringData({
    realtimeData: devicesRealtimeData,
    alarmsData: devicesAlarmsData,
    configUtil,
  });
  const isAI = point.dataType.code === 'AI';
  const roomLoadRateAvg = getPointData(
    { deviceGuid: guid, deviceType },
    isAI
      ? {
          hardCodedPointCode: code,
          formatted: true,
        }
      : {
          hardCodedPointCode: code,
          reflected: true,
        }
  );
  const pointGuids = [
    {
      serieName: name,
      deviceGuid: guid,
      pointCode: code,
      unit: point.unit,
    },
  ];

  const btnText = <PointDataRenderer key={point.code} data={roomLoadRateAvg} />;

  return (
    <>
      {isAI ? (
        <PointsLineModalButton
          idcTag={idcTag}
          btnStyle={{ fontSize: 12 }}
          btnText={btnText}
          modalText={`${roomGuid}${name}`}
          pointGuids={pointGuids}
        />
      ) : (
        <PointsStateLineModalButton
          idcTag={idcTag}
          btnText={btnText}
          modalText={`${roomGuid}${name}`}
          pointGuids={pointGuids}
          seriesOption={[{ name }]}
          validLimitsMap={point.validLimits}
        />
      )}
    </>
  );
}
