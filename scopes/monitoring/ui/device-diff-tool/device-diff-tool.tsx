import React, { useState } from 'react';
import { CSVLink } from 'react-csv';
import { useDispatch } from 'react-redux';

import {
  DownloadOutlined,
  FullscreenExitOutlined,
  FullscreenOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  MinusSquareOutlined,
  PlusSquareOutlined,
  QuestionCircleOutlined,
} from '@ant-design/icons';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Collapse } from '@manyun/base-ui.ui.collapse';
import { Empty } from '@manyun/base-ui.ui.empty';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnsType } from '@manyun/base-ui.ui.table';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';

import { useLayout } from '@manyun/dc-brain.context.layout';
import { syncCommonDataAction } from '@manyun/dc-brain.state.common';
import {
  SUBSCRIPTIONS_MODE,
  addRealtimeNAlarmsDataSubscriptionActionCreator as subscribe,
  removeRealtimeNAlarmsDataSubscriptionActionCreator as unsubscribe,
} from '@manyun/monitoring.state.subscriptions';
import type { PointType } from '@manyun/monitoring.ui.point-diff-tool';

import { DeviceToolDrawer } from './components/device-tool-drawer';
import { DragDeviceList } from './components/drag-device-list';
import { fullScreenStyle, getExportData, onAddToPointDiffPage, tabList } from './constants';
import { clearState, getState } from './constants/cache';
import {
  getAssetInfoColumns,
  getAssetInfoList,
  getBasicColumns,
  getBasicInfoColumns,
  getBasicInfoList,
  getBasicList,
  getCorePointColumns,
  getCorePointList,
  getParamsInfoColumns,
  getParamsInfoList,
} from './constants/getColumns';
import type { DeviceDiffToolProps, DeviceType, TableItemType } from './device-diff-tool.type';
import styles from './device.module.less';

export function DeviceDiffTool({ title }: DeviceDiffToolProps) {
  const dispatch = useDispatch();

  const { sideBarTab, setSideBarVisible, setSideBarTab } = useLayout();

  const [open, setOpen] = useState<boolean>(false);
  const [fullScreen, setFullScreen] = useState<boolean>(false);
  const [activeTabKey, setActiveTabKey] = useState<string>('point');

  const [fullContent, setFullContent] = useState<boolean>(false); //是否展示已选设备视图

  const [sameAndEmptys, setSameAndEmptys] = useState<string[]>();

  const [pointCollapseActiveKey, setPointCollapseActiveKey] = useState<string[] | string>([
    'corePoint',
    'otherPoint',
  ]);

  const [deviceInfoCollapseActiveKey, setDeviceInfoCollapseActiveKey] = useState<string[] | string>(
    ['basicContent', 'assetContent', 'paramsContent']
  );

  const [deviceList, setDeviceList] = useState<DeviceType[]>(getState());

  const [selectGuids, setSelectGuids] = useState<string[]>([]);

  const [selectDevices, setSelectDevices] = useState<DeviceType[]>([]);

  const [basicKey, setBasicKey] = useState<string[]>([]);
  const [pointTypeCheckValues, setPointTypeCheckValues] = useState<string[]>();

  const [basicList, setBasicList] = useState<TableItemType[]>([]);
  const [basicColumns, setBasicColumns] = useState<ColumnsType>([]);

  const [basicInfoList, setBasicInfoList] = useState<TableItemType[]>([]);
  const [basicInfoColumns, setBasicInfoColumns] = useState<ColumnsType>([]);

  const [assetInfoList, setAssetInfoList] = useState<TableItemType[]>([]);
  const [assetInfoColumns, setAssetInfoColumns] = useState<ColumnsType>([]);

  const [paramsInfoList, setParamsInfoList] = useState<TableItemType[]>([]);
  const [paramsInfoColumns, setParamsInfoColumns] = useState<ColumnsType>([]);

  //核心测点
  const [corePointList, setCorePointList] = useState<TableItemType[]>([]);
  const [corePointColumns, setCorePointColumns] = useState<ColumnsType>([]);
  //其他测点
  const [otherPointList, setOtherPointList] = useState<TableItemType[]>([]);
  const [otherPointColumns, setOtherPointColumns] = useState<ColumnsType>([]);

  React.useEffect(() => {
    if (sideBarTab === 'devicedifftool') {
      setDeviceList(getState());
    }
  }, [sideBarTab]);

  const mode = SUBSCRIPTIONS_MODE.REALTIME_N_ALARMS;
  const moduleId = 'device-diff-tool';
  const blockGuid = deviceList.length ? deviceList[0].spaceGuid.blockGuid : '';
  const targets = React.useMemo(() => {
    return [
      {
        mode,
        moduleId,
        blockGuid,
        deviceGuids: selectGuids,
      },
    ];
  }, [blockGuid, mode, selectGuids]);

  React.useEffect(() => {
    if (targets && blockGuid) {
      // @ts-ignore
      dispatch(subscribe({ targets }));
    }
    return () => {
      if (targets) {
        // @ts-ignore
        dispatch(unsubscribe({ mode, moduleId, blockGuid }));
      }
      if (deviceList.length) {
        dispatch(
          syncCommonDataAction({
            strategy: {
              deviceTypesPointsDefinition: [deviceList[0].deviceType],
            },
          })
        );
      }
    };
  }, [targets, mode, dispatch, blockGuid, deviceList]);

  const onTabChange = (key: React.SetStateAction<string>) => {
    setActiveTabKey(key);
  };

  const getMainData = (selectDevices: DeviceType[]) => {
    const basicList = getBasicList(selectDevices, 'all');
    const basicColumns = getBasicColumns(selectDevices, onCloseSidebar);

    const basicInfoList = getBasicInfoList(selectDevices, 'all');
    const basicInfoColumns = getBasicInfoColumns(selectDevices, 'all');

    const assetInfoList = getAssetInfoList(selectDevices, 'all');
    const assetInfoColumns = getAssetInfoColumns(selectDevices, 'all');

    const paramsInfoList = getParamsInfoList(selectDevices, 'all');
    const paramsInfoColumns = getParamsInfoColumns(selectDevices);

    const corePointList = getCorePointList(selectDevices, 'point');
    const corePointColumns = getCorePointColumns(selectDevices, 'point', onAddToPointDiffPage);

    const otherList = getCorePointList(selectDevices, 'otherPoint');
    const otherPointColumns = getCorePointColumns(
      selectDevices,
      'otherPoint',
      onAddToPointDiffPage
    );
    //选中设备
    setSelectDevices(selectDevices);
    //信息
    setBasicList(basicList);
    setBasicColumns(basicColumns);
    //基本信息
    setBasicInfoList(basicInfoList);
    setBasicInfoColumns(basicInfoColumns);
    //资产信息
    setAssetInfoList(assetInfoList);
    setAssetInfoColumns(assetInfoColumns);
    //设备信息
    setParamsInfoList(paramsInfoList);
    setParamsInfoColumns(paramsInfoColumns);
    //核心测点
    setCorePointList(corePointList);
    setCorePointColumns(corePointColumns);
    setOtherPointList(otherList);
    setOtherPointColumns(otherPointColumns);
    //清空
    setPointTypeCheckValues([]);
    setSameAndEmptys([]);
    //默认和每次切换打开collapse
    setDeviceInfoCollapseActiveKey(['basicContent', 'assetContent', 'paramsContent']);
    setPointCollapseActiveKey(['corePoint', 'otherPoint']);
  };

  const onChangefullContent = () => {
    if (fullContent) {
      setFullContent(false);
      return;
    }
    setFullContent(true);
  };

  const onClearDate = () => {
    setDeviceList([]);
    setSelectDevices([]);
    setSelectGuids([]);
    getMainData([]);
    clearState();
  };

  const onBasicChange = (value: string[]) => {
    setBasicKey(value);
  };

  const onCloseSidebar = () => {
    setSideBarVisible(false);
    setSideBarTab('roomview');
  };

  const onPointTypeCheck = (values: string[]) => {
    setPointTypeCheckValues(values);
    const selectDevices = deviceList.filter(item => {
      return selectGuids.includes(item.guid);
    });
    let newSelectDevices = selectDevices;
    if (values.length === 1) {
      newSelectDevices = selectDevices.map((item: DeviceType) => {
        const { pointList = [], otherPointList = [] } = item;
        const newPointList = pointList.filter((pointItem: PointType) => {
          return pointItem.dataType.code === values[0];
        });
        const newOtherPointList = otherPointList.filter((pointItem: PointType) => {
          return pointItem.dataType.code === values[0];
        });
        return { ...item, pointList: newPointList, otherPointList: newOtherPointList };
      });
    }
    const corePointList = getCorePointList(newSelectDevices, 'point');
    const corePointColumns = getCorePointColumns(newSelectDevices, 'point', onAddToPointDiffPage);
    const otherList = getCorePointList(newSelectDevices, 'otherPoint');
    const otherPointColumns = getCorePointColumns(
      newSelectDevices,
      'otherPoint',
      onAddToPointDiffPage
    );
    setCorePointList(corePointList);
    setCorePointColumns(corePointColumns);
    setOtherPointList(otherList);
    setOtherPointColumns(otherPointColumns);
  };

  const onCheckSameAndEmptyChange = (checkedValues: string[]) => {
    setSameAndEmptys(checkedValues);
    let basicList,
      basicInfoList,
      basicInfoColumns,
      assetInfoList,
      assetInfoColumns,
      paramsInfoList,
      paramsInfoColumns;
    if (!checkedValues.length) {
      basicList = getBasicList(selectDevices, 'all');
      basicInfoList = getBasicInfoList(selectDevices, 'all');
      basicInfoColumns = getBasicInfoColumns(selectDevices, 'all');
      assetInfoList = getAssetInfoList(selectDevices, 'all');
      assetInfoColumns = getAssetInfoColumns(selectDevices, 'all');
      paramsInfoList = getParamsInfoList(selectDevices, 'all');
      paramsInfoColumns = getParamsInfoColumns(selectDevices);
    } else if (checkedValues.length === 2) {
      basicList = getBasicList(selectDevices, 'hideall');
      basicInfoList = getBasicInfoList(selectDevices, 'hideall');
      basicInfoColumns = getBasicInfoColumns(selectDevices, 'hideall');
      assetInfoList = getAssetInfoList(selectDevices, 'hideall');
      assetInfoColumns = getAssetInfoColumns(selectDevices, 'hideall');
      paramsInfoList = getParamsInfoList(selectDevices, 'hideall');
      paramsInfoColumns = getParamsInfoColumns(selectDevices);
    } else if (checkedValues[1] === 'empty') {
      basicList = getBasicList(selectDevices, checkedValues[0]);
      basicInfoList = getBasicInfoList(selectDevices, checkedValues[0]);
      basicInfoColumns = getBasicInfoColumns(selectDevices, checkedValues[0]);
      assetInfoList = getAssetInfoList(selectDevices, checkedValues[0]);
      assetInfoColumns = getAssetInfoColumns(selectDevices, checkedValues[0]);
      paramsInfoList = getParamsInfoList(selectDevices, checkedValues[0]);
      paramsInfoColumns = getParamsInfoColumns(selectDevices);
    } else {
      basicList = getBasicList(selectDevices, checkedValues[0]);
      basicInfoList = getBasicInfoList(selectDevices, checkedValues[0]);
      basicInfoColumns = getBasicInfoColumns(selectDevices, checkedValues[0]);
      assetInfoList = getAssetInfoList(selectDevices, checkedValues[0]);
      assetInfoColumns = getAssetInfoColumns(selectDevices, checkedValues[0]);
      paramsInfoList = getParamsInfoList(selectDevices, checkedValues[0]);
      paramsInfoColumns = getParamsInfoColumns(selectDevices);
    }
    setBasicList(basicList);
    setBasicInfoList(basicInfoList);
    setBasicInfoColumns(basicInfoColumns);
    setAssetInfoList(assetInfoList);
    setAssetInfoColumns(assetInfoColumns);
    setParamsInfoList(paramsInfoList);
    setParamsInfoColumns(paramsInfoColumns);
  };

  const onShowDrawer = () => {
    if (deviceList.length > 19) {
      message.error('添加失败，设备比对个数已达上限');
      return;
    }
    setOpen(true);
  };

  return (
    <Card
      bordered={false}
      title={
        <Space>
          {title}
          <Tooltip placement="top" title="仅限于同类型设备进行对比,且参与对比的设备,不能超过20个">
            <QuestionCircleOutlined />
          </Tooltip>
        </Space>
      }
      extra={
        fullScreen ? (
          <FullscreenExitOutlined onClick={() => setFullScreen(false)} />
        ) : (
          <FullscreenOutlined onClick={() => setFullScreen(true)} />
        )
      }
      className={styles.container}
      style={fullScreen ? fullScreenStyle : {}}
    >
      <Row>
        <Col span={fullContent ? 0 : 6} className={styles.contentLeft}>
          <Card
            title={<Typography.Text>已选设备</Typography.Text>}
            extra={
              <Typography.Text type="secondary">{`${selectDevices.length}/${deviceList.length}`}</Typography.Text>
            }
            bodyStyle={{ height: 'calc(100vh - 220px)', overflowY: 'auto' }}
          >
            <DragDeviceList
              selectGuids={selectGuids}
              setSelectGuids={setSelectGuids}
              deviceList={deviceList}
              setDeviceList={setDeviceList}
              getMainData={getMainData}
            />
          </Card>
          <Space style={{ height: '52px', display: 'flex', justifyContent: 'center' }}>
            <Button type="primary" onClick={onShowDrawer}>
              添加设备
            </Button>
            <Button onClick={onClearDate}>清空设备</Button>
          </Space>
        </Col>
        <Col span={fullContent ? 24 : 18}>
          <Card
            className={styles.content}
            bodyStyle={{ height: 'calc(100vh - 166px)', overflowY: 'auto' }}
            tabList={tabList}
            activeTabKey={activeTabKey}
            tabBarExtraContent={
              <CSVLink data={getExportData(selectDevices)} filename="设备对比.csv" target="_blank">
                <DownloadOutlined className={styles.download} />
              </CSVLink>
            }
            onTabChange={key => {
              onTabChange(key);
            }}
          >
            <>
              {activeTabKey === 'point' ? (
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Row justify="space-between" align="middle">
                    <Col>
                      <Space>
                        {fullContent && (
                          <>
                            <Button type="primary" onClick={onShowDrawer}>
                              添加设备
                            </Button>
                            <Button onClick={onClearDate}>清空设备</Button>
                          </>
                        )}
                        <Checkbox.Group
                          className={styles.checkbox}
                          value={pointTypeCheckValues}
                          onChange={onPointTypeCheck}
                        >
                          <Checkbox value="AI">AI</Checkbox>
                          <Checkbox value="DI">DI</Checkbox>
                        </Checkbox.Group>
                      </Space>
                    </Col>
                    <Col>
                      <Checkbox.Group
                        className={styles.checkbox}
                        value={sameAndEmptys}
                        onChange={onCheckSameAndEmptyChange}
                      >
                        <Checkbox value="empty">隐藏空数据项</Checkbox>
                        <Checkbox value="same">隐藏相同项</Checkbox>
                      </Checkbox.Group>
                    </Col>
                  </Row>
                  {selectDevices.length ? (
                    <>
                      <Table
                        showHeader={false}
                        bordered
                        columns={basicColumns}
                        dataSource={basicList}
                        pagination={false}
                        scroll={{ x: 'max-content' }}
                        size="small"
                      />
                      <Collapse
                        bordered={false}
                        activeKey={pointCollapseActiveKey}
                        expandIcon={({ isActive }) =>
                          isActive ? <MinusSquareOutlined /> : <PlusSquareOutlined />
                        }
                        onChange={(values: string[] | string) => setPointCollapseActiveKey(values)}
                      >
                        <Collapse.Panel key="corePoint" header="核心测点">
                          <Table
                            showHeader={false}
                            bordered
                            columns={corePointColumns}
                            dataSource={corePointList}
                            scroll={{ x: 'max-content' }}
                            size="small"
                            pagination={false}
                          />
                        </Collapse.Panel>
                        <Collapse.Panel key="otherPoint" header="查看更多">
                          <Table
                            showHeader={false}
                            bordered
                            columns={otherPointColumns}
                            dataSource={otherPointList}
                            scroll={{ x: 'max-content' }}
                            size="small"
                            pagination={false}
                          />
                        </Collapse.Panel>
                      </Collapse>
                    </>
                  ) : (
                    <Space direction="vertical" style={{ width: '100%', textAlign: 'center' }}>
                      <Empty />
                      <Button type="primary" onClick={onShowDrawer}>
                        添加设备
                      </Button>
                    </Space>
                  )}
                </Space>
              ) : (
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Row justify="space-between" align="middle">
                    <Col>
                      <Space>
                        {fullContent && (
                          <>
                            <Button type="primary" onClick={onShowDrawer}>
                              添加设备
                            </Button>
                            <Button onClick={onClearDate}>清空设备</Button>
                          </>
                        )}
                        <Checkbox.Group
                          className={styles.checkbox}
                          value={basicKey}
                          onChange={onBasicChange}
                        >
                          <Checkbox value="basic">基本信息</Checkbox>
                          <Checkbox value="asset">资产信息</Checkbox>
                          <Checkbox value="params">设备参数</Checkbox>
                        </Checkbox.Group>
                      </Space>
                    </Col>
                    <Col>
                      <Checkbox.Group
                        className={styles.checkbox}
                        value={sameAndEmptys}
                        onChange={onCheckSameAndEmptyChange}
                      >
                        <Checkbox value="empty">隐藏空数据项</Checkbox>
                        <Checkbox value="same">隐藏相同项</Checkbox>
                      </Checkbox.Group>
                    </Col>
                  </Row>
                  {selectDevices.length ? (
                    <>
                      <Table
                        showHeader={false}
                        bordered
                        columns={basicColumns}
                        dataSource={basicList}
                        pagination={false}
                        scroll={{ x: 'max-content' }}
                        size="small"
                      />
                      <Collapse
                        bordered={false}
                        activeKey={deviceInfoCollapseActiveKey}
                        expandIcon={({ isActive }) =>
                          isActive ? <MinusSquareOutlined /> : <PlusSquareOutlined />
                        }
                        onChange={(values: string[] | string) =>
                          setDeviceInfoCollapseActiveKey(values)
                        }
                      >
                        {(!basicKey.length || basicKey.includes('basic')) && (
                          <Collapse.Panel key="basicContent" header="基本信息">
                            <Table
                              showHeader={false}
                              bordered
                              columns={basicInfoColumns}
                              dataSource={basicInfoList}
                              pagination={false}
                              scroll={{ x: 'max-content' }}
                              size="small"
                            />
                          </Collapse.Panel>
                        )}
                        {(!basicKey.length || basicKey.includes('asset')) && (
                          <Collapse.Panel key="assetContent" header="资产信息">
                            <Table
                              showHeader={false}
                              bordered
                              columns={assetInfoColumns}
                              dataSource={assetInfoList}
                              pagination={false}
                              scroll={{ x: 'max-content' }}
                              size="small"
                            />
                          </Collapse.Panel>
                        )}
                        {(!basicKey.length || basicKey.includes('params')) && (
                          <Collapse.Panel key="paramsContent" header="设备参数">
                            <Table
                              showHeader={false}
                              bordered
                              columns={paramsInfoColumns}
                              dataSource={paramsInfoList}
                              pagination={false}
                              scroll={{ x: 'max-content' }}
                              size="small"
                            />
                          </Collapse.Panel>
                        )}
                      </Collapse>
                    </>
                  ) : (
                    <Space direction="vertical" style={{ width: '100%', textAlign: 'center' }}>
                      <Empty />
                      <Button type="primary" onClick={onShowDrawer}>
                        添加设备
                      </Button>
                    </Space>
                  )}
                </Space>
              )}
              {fullContent ? (
                <MenuFoldOutlined className={styles.contentIcon} onClick={onChangefullContent} />
              ) : (
                <MenuUnfoldOutlined className={styles.contentIcon} onClick={onChangefullContent} />
              )}
            </>
          </Card>
        </Col>
      </Row>
      <DeviceToolDrawer
        open={open}
        setDeviceContrastList={setDeviceList}
        deviceContrastList={deviceList}
        selectGuids={selectGuids}
        setSelectGuids={setSelectGuids}
        getMainData={getMainData}
        onClose={() => setOpen(false)}
      />
    </Card>
  );
}
