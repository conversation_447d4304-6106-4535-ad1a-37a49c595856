import React from 'react';
import { useLatest } from 'react-use';

import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

import type { ProfessionalRuleTemplateJSON } from '@manyun/monitoring.model.professional-rule-template';
import { fetchProfessionalRuleTemplateList } from '@manyun/monitoring.service.fetch-professional-rule-template-list';

type Value = number | { label: string; value: number; id?: number };

export type ProfessionalRuleTemplateSelectProps = SelectProps<
  Value,
  ProfessionalRuleTemplateJSON
> & {
  deviceType?: string;
};

export const ProfessionalRuleTemplateSelect = React.forwardRef<
  RefSelectProps,
  ProfessionalRuleTemplateSelectProps
>(
  (
    {
      options: _options,
      loading: _loading,
      mode: _mode,
      labelInValue,
      onChange,
      deviceType,
      value,
      ...rest
    },
    ref
  ) => {
    const [loading, setLoading] = React.useState(false);
    const [templates, setTemplates] = React.useState<ProfessionalRuleTemplateJSON[]>([]);
    React.useEffect(() => {
      (async () => {
        setLoading(true);
        const { error, data } = await fetchProfessionalRuleTemplateList({
          deviceType,
          pageNum: 0,
          pageSize: 2000, // 设置一个比较大的值以便全部返回
        });
        setLoading(false);
        if (error) {
          message.error(error.message);
        }
        setTemplates(data.data);
      })();
    }, [deviceType]);

    const onChangeRef = useLatest(onChange);
    React.useEffect(() => {
      // labelInValue 初始值回填
      if (labelInValue && value && typeof value !== 'number' && typeof value.id === 'undefined') {
        const template = templates?.find(item => item.id === value?.value);
        if (!template) {
          return;
        }
        onChangeRef.current?.(
          {
            ...value,
            label: template.name,
            ...template,
          },
          {} as never
        );
      }
    }, [labelInValue, onChangeRef, templates, value]);

    const changeHandler: ProfessionalRuleTemplateSelectProps['onChange'] = (value, node) => {
      // 若 labelInValue 为 false 或清空时，直接执行 onChange
      if (!labelInValue || value === undefined || typeof value !== 'object') {
        onChangeRef.current?.(value, node);
        return;
      }
      onChangeRef.current?.(
        {
          ...value,
          ...templates.find(item => item.id === value?.value),
        },
        node
      );
    };

    return (
      <Select<Value, ProfessionalRuleTemplateJSON>
        ref={ref}
        showSearch
        optionFilterProp="name"
        {...rest}
        onChange={changeHandler}
        loading={loading}
        value={value}
        labelInValue={labelInValue}
        options={templates}
        fieldNames={{ label: 'name', value: 'id', ...rest.fieldNames }}
      />
    );
  }
);

ProfessionalRuleTemplateSelect.displayName = 'ProfessionalRuleTemplateSelect';
