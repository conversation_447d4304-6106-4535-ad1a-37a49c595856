import React, { useMemo } from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

import { getAlarmLocales } from '@manyun/monitoring.model.alarm';

export type AlarmExpectStatusSelectProps = Omit<SelectProps, 'options' | 'ref'>;

export const AlarmExpectStatusSelect = React.forwardRef(
  (props: SelectProps, ref: React.Ref<RefSelectProps>) => {
    const locales = getAlarmLocales();

    const options: SelectProps['options'] = useMemo(() => {
      return [
        { value: 'NORMAL', label: locales.expectedStatus.NORMAL },
        { value: 'EXPECTED', label: locales.expectedStatus.EXPECTED },
        { value: 'UNEXPECTED', label: locales.expectedStatus.UNEXPECTED },
      ];
    }, [
      locales.expectedStatus.EXPECTED,
      locales.expectedStatus.NORMAL,
      locales.expectedStatus.UNEXPECTED,
    ]);

    return <Select {...props} ref={ref} options={options} />;
  }
);

AlarmExpectStatusSelect.displayName = 'AlarmExpectStatusSelect';
