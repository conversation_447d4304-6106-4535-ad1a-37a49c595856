import React from 'react';

import { Tag } from '@manyun/base-ui.ui.tag';
import type { TagProps } from '@manyun/base-ui.ui.tag';

import { getAlarmLocales } from '@manyun/monitoring.model.alarm';
import type { BackendAlarmState } from '@manyun/monitoring.model.alarm';

export type AlarmStatusTextProps = {
  code: BackendAlarmState;
  shape?: TagProps['shape'];
};

const alarmStatusTagColor: Record<BackendAlarmState, string> = {
  TRIGGER: 'error',
  RECOVER: 'success',
  SUPPRESS: 'default',
};

export function AlarmStatusText({ code, shape = 'typography' }: AlarmStatusTextProps) {
  const alarmLocales = getAlarmLocales();

  return (
    <Tag color={alarmStatusTagColor[code]} shape={shape}>
      {alarmLocales.state[code]}
    </Tag>
  );
}
