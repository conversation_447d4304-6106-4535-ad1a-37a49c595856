import type { CSSProperties } from 'react';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { ReloadOutlined, UploadOutlined } from '@ant-design/icons';
import { saveAs } from 'file-saver';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import type { ColumnType as EditColumnType } from '@manyun/base-ui.ui.table';
import { Table } from '@manyun/base-ui.ui.table';

import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import { EditColumns } from '@manyun/dc-brain.ui.edit-columns';
import type {
  AlarmJSO<PERSON>,
  BackendAlarmLifecycleState,
  BackendAlarmState,
} from '@manyun/monitoring.model.alarm';
import { getAlarmLocales } from '@manyun/monitoring.model.alarm';
import { getCloumns } from '@manyun/monitoring.page.alarm-staring-screen';
import { exportAlarms } from '@manyun/monitoring.service.export-alarms';
import type { SortField, SvcQuery } from '@manyun/monitoring.service.fetch-alarms';
import { fetchAlarms } from '@manyun/monitoring.service.fetch-alarms';
import { AlarmDrawer } from '@manyun/monitoring.ui.alarm-drawer';
import { AdvancedAlarmsFilterForm } from '@manyun/monitoring.ui.alarms-filter-form';
import type { FilterFormKey, FilterFormParams } from '@manyun/monitoring.ui.alarms-filter-form';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

import type { SortOrder } from './components/alarm-sort';
import { AlarmSort } from './components/alarm-sort';
import { exportColumnFiledNamesMap, getDefaultColumnsConfig } from './utils';

type FormParams = Omit<SvcQuery, 'idcTag' | 'pageSize' | 'pageNum'>;

export type FilterAlarmsProps = {
  style?: CSSProperties;
  editColumnsKey: string;
  idc: string;
  defaultValue?: {
    alarmLevel: string | undefined;
    roomGuids: string[] | undefined;
    deviceName: string | undefined;
    deviceTypeList: string[] | undefined;
    triggerStatus: BackendAlarmState[] | undefined;
    alarmStatus: BackendAlarmLifecycleState[] | undefined;
  };
  showFormItems: FilterFormKey[];
  status?: 'REMOVED' | null;
  deviceGuid?: string;
  roomTags?: string[];
  blockTags?: string[];
  showActions?: boolean;
  bizId?: string;
  bizType?: string;
  isMerge?: boolean;
  onSearchCallback?: (params: FormParams) => void;
};

export function FilterAlarms({
  style,
  editColumnsKey,
  idc,
  showFormItems,
  defaultValue,
  status,
  deviceGuid,
  roomTags,
  blockTags,
  showActions = true,
  bizId,
  bizType,
  isMerge = true,
  onSearchCallback,
}: FilterAlarmsProps) {
  const alarmLocals = getAlarmLocales();

  const [alarmList, setAlarmList] = useState<AlarmJSON[]>([]);

  const [loading, setLoading] = useState<boolean>(false);

  const [columnsConfig, setColumnsConfig] = useState<EditColumnType[]>(
    getDefaultColumnsConfig(alarmLocals)
  );

  const [alarmSortField, setAlarmSortField] = useState<SortField>('gmtCreate');
  const [alarmSortOrder, setAlarmSortOrder] = useState<SortOrder>('DESCEND');

  const filterParamsRefDefaultValue = useDeepCompareMemo(() => {
    return {
      alarmLevel: defaultValue?.alarmLevel ? [+defaultValue.alarmLevel] : undefined,
      deviceName: defaultValue?.deviceName,
      deviceTypeList: defaultValue?.deviceTypeList,
      triggerStatus: defaultValue?.triggerStatus,
      alarmStatus: defaultValue?.alarmStatus,
      roomTags: defaultValue?.roomGuids
        ? defaultValue.roomGuids
            ?.map((item: string) => {
              return getSpaceGuidMap(item).room;
            })
            .filter((item): item is string => item !== null)
        : undefined,
      blockTags: Array.isArray(defaultValue?.roomGuids)
        ? Array.from(
            new Set(
              defaultValue?.roomGuids.map((item: string) => {
                return getSpaceGuidMap(item).block;
              })
            )
          ).filter((item): item is string => item !== null)
        : undefined,
    };
  }, [
    defaultValue?.alarmStatus,
    defaultValue?.deviceName,
    defaultValue?.deviceTypeList,
    defaultValue?.roomGuids,
    defaultValue?.triggerStatus,
  ]);

  const filterParamsRef = useRef<FormParams | undefined>(filterParamsRefDefaultValue);

  const [pagination, setPagination] = useState<{
    pageNum: number;
    pageSize: number;
    total: number;
  }>({ pageNum: 1, pageSize: 10, total: 0 });

  const [alarmInfoVisible, setAlarmInfoVisible] = useState(false);
  const [alarmInfo, setAlarmInfo] = useState<AlarmJSON>();
  const getAlarms = useCallback(
    async (params: { pageNum: number; pageSize: number }) => {
      setLoading(true);
      const { data, error } = await fetchAlarms({
        isMerge,
        status,
        isQueryData: true,
        idcTag: idc,
        deviceGuid,
        roomTags,
        blockTags,
        bizId,
        bizType,
        ...filterParamsRef.current,
        ...params,
        sortField: alarmSortField,
        sortOrder: alarmSortOrder,
      });
      setLoading(false);
      if (!error) {
        setAlarmList(data.data.map(item => item.toJSON()));
        setPagination({ pageNum: params.pageNum, pageSize: params.pageSize, total: data.total });
      } else {
        message.error(error.message);
      }
    },
    [
      alarmSortField,
      alarmSortOrder,
      blockTags,
      deviceGuid,
      idc,
      roomTags,
      status,
      bizId,
      bizType,
      isMerge,
    ]
  );

  useEffect(() => {
    getAlarms({
      pageSize: 10,
      pageNum: 1,
    });
  }, [getAlarms]);

  const onFilterForm = (params: FilterFormParams) => {
    const {
      notifyContent,
      alarmTypes,
      alarmLevels,
      alarmStates,
      alarmLifecycleStates,
      feekbackResults,
      mergeStatusList,
      expectStatusList,
      roomGuids,
      deviceName,
      deviceTypes,
      lastModifyUserName,
      alarmsTriggeredAtTimeRange,
      alarmsRecoveredAtTimeRange,
    } = params;

    const roomTags = roomGuids
      ?.map((item: string) => {
        return getSpaceGuidMap(item).room;
      })
      .filter((item): item is string => item !== null);

    const blockTags = Array.isArray(roomGuids)
      ? Array.from(
          new Set(
            roomGuids.map((item: string) => {
              return getSpaceGuidMap(item).block;
            })
          )
        ).filter((item): item is string => item !== null)
      : undefined;

    const filterParams = {
      notifyContent,
      alarmType: alarmTypes,
      alarmLevel: alarmLevels ? [+alarmLevels] : undefined,
      triggerStatus: alarmStates,
      alarmStatus: alarmLifecycleStates,
      feekbackResults,
      mergeStatusList,
      expectStatusList,
      blockTags,
      roomTags,
      deviceName,
      deviceTypeList: deviceTypes,
      confirmByName: lastModifyUserName,
      alarmCreateTimeStart: alarmsTriggeredAtTimeRange ? alarmsTriggeredAtTimeRange[0] : undefined,
      alarmCreateTimeEnd: alarmsTriggeredAtTimeRange ? alarmsTriggeredAtTimeRange[1] : undefined,
      recoverTimeStart: alarmsRecoveredAtTimeRange ? alarmsRecoveredAtTimeRange[0] : undefined,
      recoverTimeEnd: alarmsRecoveredAtTimeRange ? alarmsRecoveredAtTimeRange[1] : undefined,
    };
    filterParamsRef.current = filterParams;

    getAlarms({
      pageNum: 1,
      pageSize: pagination.pageSize,
    });
    onSearchCallback?.(filterParams);
  };

  const onChangePage = (pageNum: number, pageSize: number) => {
    getAlarms({ pageNum, pageSize });
  };

  const onSortChange = ({ value, sortOrder }: { value: string; sortOrder: SortOrder }) => {
    setAlarmSortField(value as SortField);
    setAlarmSortOrder(sortOrder);
  };

  const exportAlarm = async () => {
    const includeColumnFiledNames = columnsConfig
      .filter(item => item.show)
      .map(item => {
        if (item.dataIndex && typeof item.dataIndex === 'string') {
          return exportColumnFiledNamesMap[item.dataIndex];
        }
        return undefined;
      })
      .filter((item): item is string => typeof item !== 'undefined');

    const { data } = await exportAlarms({
      includeColumnFiledNames,
      idcTag: idc,
      status: 'REMOVED',
      deviceGuid,
      roomTags,
      blockTags,
      ...filterParamsRef.current,
      isMerge: true,
    });

    if (data) {
      saveAs(data, `告警列表.csv`);
    }
  };

  const columns = useMemo(() => {
    if (!columnsConfig) {
      return [];
    }

    const columnCodes = [];
    if (env.__DEBUG_MODE__) {
      columnCodes.push('id');
    }
    columnsConfig.forEach((item: EditColumnType) => {
      if (item.show) {
        columnCodes.push(item.dataIndex);
      }
    });

    const newColumns = columnCodes
      .map(columnCode => {
        const columnItem = getCloumns({ idc, format: 'YYYY-MM-DD HH:mm:ss' }).find(
          item => item.dataIndex === columnCode
        );
        return columnItem;
      })
      .filter((item): item is EditColumnType<AlarmJSON> => typeof item !== 'undefined');

    const operation: EditColumnType<AlarmJSON> = {
      title: '操作',
      dataIndex: 'operation',
      fixed: 'right',
      render: (_: unknown, record: AlarmJSON) => {
        return (
          <Button
            type="link"
            compact
            onClick={() => {
              setAlarmInfoVisible(true);
              setAlarmInfo(record);
            }}
          >
            详情
          </Button>
        );
      },
    };
    newColumns.push(operation);
    return newColumns;
  }, [idc, columnsConfig]);

  return (
    <Card style={style}>
      <Space style={{ width: '100%' }} direction="vertical">
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <AdvancedAlarmsFilterForm
            idc={idc}
            showFormItems={showFormItems}
            defaultValue={{
              alarmLevels: defaultValue?.alarmLevel,
              roomGuids: defaultValue?.roomGuids,
              deviceName: defaultValue?.deviceName,
              deviceTypes: defaultValue?.deviceTypeList,
              alarmStates: defaultValue?.triggerStatus,
              alarmLifecycleStates: defaultValue?.alarmStatus,
            }}
            deviceTypeSelectable={['C2']}
            onChange={onFilterForm}
          />
          {showActions && (
            <Space size={16}>
              <AlarmSort
                value={alarmSortField}
                sortOrder={alarmSortOrder}
                defaultValue="gmtCreate"
                defaultSortOrder="DESCEND"
                dataSource={[
                  { title: '告警开始时间', code: 'gmtCreate' },
                  { title: '告警最新时间', code: 'triggerTime' },
                  { title: '告警恢复时间', code: 'recoverTime' },
                  { title: '告警受理时间', code: 'confirmTime' },
                  { title: '下盯屏时间', code: 'removeTime' },
                  { title: '受理状态', code: 'alarmStatus' },
                ]}
                onChange={onSortChange}
              />
              <ReloadOutlined
                onClick={() =>
                  getAlarms({
                    pageNum: pagination.pageNum,
                    pageSize: pagination.pageSize,
                  })
                }
              />
              <UploadOutlined onClick={exportAlarm} />
              <EditColumns
                uniqKey={editColumnsKey}
                defaultValue={getDefaultColumnsConfig(alarmLocals)}
                allowSetAsFixed={false}
                listsHeight={500}
                onChange={(value: EditColumnType[]) => {
                  setColumnsConfig(value);
                }}
              />
            </Space>
          )}
        </div>
        <Table
          scroll={{ x: 'max-content' }}
          rowKey="id"
          columns={columns}
          dataSource={alarmList}
          loading={loading}
          pagination={{
            total: pagination.total,
            current: pagination.pageNum,
            pageSize: pagination.pageSize,
            onChange: onChangePage,
          }}
        />
      </Space>
      {alarmInfoVisible && alarmInfo && (
        <AlarmDrawer
          visible={alarmInfoVisible}
          idc={idc}
          alarm={alarmInfo}
          onClose={() => setAlarmInfoVisible(false)}
        />
      )}
    </Card>
  );
}
