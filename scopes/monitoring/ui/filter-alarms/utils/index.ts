import type { AlarmLocales } from '@manyun/monitoring.model.alarm';

export const exportColumnFiledNamesMap: Record<string, string> = {
  'type.name': 'alarmType',
  level: 'alarmLevel',
  'source.spaceGuidMap.blockTag': 'blockTag',
  'source.spaceGuidMap.roomTag': 'roomTag',
  'source.roomType': 'roomType',
  'source.spaceGuidMap': 'location',
  source: 'deviceName',
  alarmLabel: 'alarmLabel',
  'source.spaceGuidMap.gridTag': 'gridTag',
  'source.spaceGuidMap.columnTag': 'columnTag',
  'source.deviceType': 'deviceType',
  mergeCount: 'mergeCount',
  'point.name': 'pointName',
  'cause.name': 'triggerCondition',
  monitoringItem: 'itemId',
  'monitoringItem.threshold': 'threshold',
  'pointData.snapshot': 'triggerSnapshot',
  alarmState: 'triggerStatus',
  createdAt: 'activeTime',
  latestTriggeredAt: 'triggerTime',
  recoveredAt: 'recoverTime',
  alarmDuration: 'alarmDuration',
  confirmedAt: 'confirmTime',
  removedAt: 'removeTime',
  eventId: 'eventId',
  changeId: 'changeId',
  lifecycleState: 'alarmStatus',
  confirmUser: 'confirmByName',
  removeUser: 'removeUser',
  reason: 'alarmReason',
  notifyCount: 'notifyCount',
  gridCount: 'gridCount',
  customerCount: 'customerCount',
  deviceCount: 'deviceCount',
  'source.deviceLabel': 'deviceLabel',
  message: 'notifyContent',
};

export const getDefaultColumnsConfig = (alarmLocals: AlarmLocales) => {
  return [
    { title: alarmLocals.type.name, dataIndex: 'type.name', show: true, disable: true },
    { title: alarmLocals.level, dataIndex: 'level', show: true, disable: false },
    {
      title: alarmLocals.source.spaceGuidMap.blockTag,
      dataIndex: 'source.spaceGuidMap.blockTag',
      show: false,
      disable: false,
    },
    {
      title: alarmLocals.source.spaceGuidMap.roomTag,
      dataIndex: 'source.spaceGuidMap.roomTag',
      show: true,
      disable: false,
    },
    {
      title: alarmLocals.source.roomName,
      dataIndex: 'source.roomName',
      show: true,
      disable: false,
    },
    {
      title: alarmLocals.source.roomType,
      dataIndex: 'source.roomType',
      show: false,
      disable: false,
    },
    {
      title: alarmLocals.source.spaceGuidMap.__self,
      dataIndex: 'source.spaceGuidMap',
      show: true,
      disable: false,
    },
    { title: alarmLocals.source.__self, dataIndex: 'source', show: true, disable: false },
    { title: alarmLocals.alarmLabel, dataIndex: 'alarmLabel', show: true, disable: false }, //标签
    {
      title: alarmLocals.source.spaceGuidMap.gridTag,
      dataIndex: 'source.spaceGuidMap.gridTag',
      show: false,
      disable: false,
    },
    {
      title: alarmLocals.source.spaceGuidMap.columnTag,
      dataIndex: 'source.spaceGuidMap.columnTag',
      show: false,
      disable: false,
    },
    {
      title: alarmLocals.source.deviceType,
      dataIndex: 'source.deviceType',
      show: false,
      disable: false,
    },
    { title: alarmLocals.mergeCount, dataIndex: 'mergeCount', show: true, disable: false },
    { title: alarmLocals.mergeStatus.__self, dataIndex: 'mergeStatus', show: true, disable: false },
    { title: alarmLocals.point.name, dataIndex: 'point.name', show: true, disable: false },
    { title: alarmLocals.cause.name, dataIndex: 'cause.name', show: true, disable: false },
    {
      title: alarmLocals.monitoringItem.__self,
      dataIndex: 'monitoringItem',
      show: true,
      disable: false,
    },
    {
      title: alarmLocals.monitoringItem.threshold,
      dataIndex: 'monitoringItem.threshold',
      show: true,
      disable: false,
    },
    {
      title: alarmLocals.pointData.snapshot,
      dataIndex: 'pointData.snapshot',
      show: true,
      disable: false,
    },
    { title: alarmLocals.state.__self, dataIndex: 'alarmState', show: true, disable: false },
    { title: alarmLocals.createdAt, dataIndex: 'createdAt', show: true, disable: false },
    {
      title: alarmLocals.latestTriggeredAt,
      dataIndex: 'latestTriggeredAt',
      show: true,
      disable: false,
    },
    { title: alarmLocals.recoveredAt, dataIndex: 'recoveredAt', show: true, disable: false }, // 告警恢复时间
    {
      title: alarmLocals.alarmDuration,
      dataIndex: 'alarmDuration',
      show: false,
      disable: false,
    }, // 告警持续时长
    { title: alarmLocals.confirmedAt, dataIndex: 'confirmedAt', show: true, disable: false }, //受理时间
    { title: alarmLocals.removedAt, dataIndex: 'removedAt', show: true, disable: false }, //下盯屏时间
    { title: alarmLocals.eventId, dataIndex: 'eventId', show: true, disable: false },
    { title: alarmLocals.changeId, dataIndex: 'changeId', show: false, disable: false }, //变更单号
    {
      title: alarmLocals.expectedStatus.__self,
      dataIndex: 'expectedStatus',
      show: false,
      disable: false,
    }, //变更状态
    {
      title: alarmLocals.lifecycleState.__self,
      dataIndex: 'lifecycleState',
      show: true,
      disable: false,
    },
    {
      title: alarmLocals.confirmUser.__self,
      dataIndex: 'confirmUser',
      show: true,
      disable: false,
    },
    { title: alarmLocals.removeUser, dataIndex: 'removeUser', show: true, disable: false },
    {
      title: alarmLocals.reason,
      dataIndex: 'reason',
      show: true,
      disable: false,
    },
    { title: alarmLocals.notifyCount, dataIndex: 'notifyCount', show: false, disable: false }, //通知次数
    { title: alarmLocals.gridCount, dataIndex: 'gridCount', show: false, disable: false }, //影响机柜数
    {
      title: alarmLocals.customerCount,
      dataIndex: 'customerCount',
      show: false,
      disable: false,
    }, //影响客户数
    { title: alarmLocals.deviceCount, dataIndex: 'deviceCount', show: false, disable: false }, //影响设备数
    {
      title: alarmLocals.source.deviceLabel,
      dataIndex: 'source.deviceLabel',
      show: false,
      disable: false,
    }, //设备标签
    { title: alarmLocals.message, dataIndex: 'message', show: false, disable: false }, //影响设备数
  ];
};
