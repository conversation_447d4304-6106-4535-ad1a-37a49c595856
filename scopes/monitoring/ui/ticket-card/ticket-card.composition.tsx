import React from 'react';
import { MemoryRouter as Router } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { BackendTaskStatus } from '@manyun/ticket.model.ticket';

import { TicketCard } from './ticket-card';

export const BasicAlBasicTicketCardarmCard = () => (
  <ConfigProvider>
    <Router>
      <div style={{ padding: 64 }}>
        <TicketCard
          ticket={{
            id: '23',
            type: '',
            subType: '',
            blockGuid: '',
            title: '工单标题',
            createdAt: Date.now().toString(),
            modifiedAt: Date.now().toString(),
            state: BackendTaskStatus.PROCESSING,
            assignee: {
              id: 99,
              name: '某某某',
            },
            // taskProperties: ticket.taskProperties,
          }}
        />
      </div>
    </Router>
  </ConfigProvider>
);
