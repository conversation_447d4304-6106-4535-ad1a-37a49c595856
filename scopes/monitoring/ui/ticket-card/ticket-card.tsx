import React from 'react';
import { <PERSON> } from 'react-router-dom';

import dayjs from 'dayjs';

import { Card } from '@manyun/base-ui.ui.card';
import { Space } from '@manyun/base-ui.ui.space';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';

import { BackendTaskStatus, TaskStatusMap, Ticket } from '@manyun/ticket.model.ticket';
import { generateTicketUrl } from '@manyun/ticket.route.ticket-routes';

import styles from './ticket-card.module.less';

export type TicketCardProps = {
  ticket: Ticket;
  ticketTypeList: Record<string, Record<'taskValue', string>>;
};

const TicketStatusTagColorMapper: Record<BackendTaskStatus, string> = {
  [BackendTaskStatus.INIT]: 'default',
  [BackendTaskStatus.WAITTAKEOVER]: 'default',
  [BackendTaskStatus.FINISH]: 'success',
  [BackendTaskStatus.PROCESSING]: 'processing',
  [BackendTaskStatus.FAILURE]: 'error',
  [BackendTaskStatus.UNDO]: 'default',
  [BackendTaskStatus.CLOSE_APPROVER]: 'default',
};

export function TicketCard({ ticket, ticketTypeList }: TicketCardProps) {
  const { id, type, subType, state, assignee, createdAt } = ticket;

  return (
    <Card size="small" bordered>
      <Space style={{ width: '100%' }} direction="vertical">
        <Space className={styles.main} direction="vertical">
          <Space style={{ width: '100%', justifyContent: 'space-between' }}>
            <Link
              to={{
                pathname: generateTicketUrl({
                  ticketType: type.toLowerCase(),
                  id: id,
                }),
              }}
            >
              {id}
            </Link>
            <Tag color={TicketStatusTagColorMapper[state]}>{TaskStatusMap[state]}</Tag>
          </Space>
          <Typography.Text
            ellipsis
          >{`${ticketTypeList[type]?.taskValue} - ${ticketTypeList[subType]?.taskValue}`}</Typography.Text>
        </Space>
        <Space style={{ display: 'flex', justifyContent: 'space-between' }}>
          <Typography.Text style={{ float: 'left', fontSize: 12 }} type="secondary">
            {`处理人：${assignee?.name ?? '--'}`}
          </Typography.Text>
          <Typography.Text style={{ float: 'right', fontSize: 12 }} type="secondary">
            {dayjs(createdAt).format('YYYY-MM-DD HH:mm:ss')}
          </Typography.Text>
        </Space>
      </Space>
    </Card>
  );
}
