import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

import { Feedback, getAiOpsAlarmLocales } from '@manyun/monitoring.model.ai-ops-alarm';

export type FeekbackResultsSelectProps = Omit<SelectProps, 'options'>;

export const FeekbackResultsSelect = React.forwardRef(
  (props: SelectProps, ref: React.Ref<RefSelectProps>) => {
    const locales = getAiOpsAlarmLocales();

    const options: SelectProps['options'] = [
      { value: Feedback.On, label: locales['feedback'][Feedback.On] },
      { value: Feedback.Off, label: locales['feedback'][Feedback.Off] },
    ];

    return <Select {...props} ref={ref} options={options} />;
  }
);

FeekbackResultsSelect.displayName = 'FeekbackResultsSelect';
