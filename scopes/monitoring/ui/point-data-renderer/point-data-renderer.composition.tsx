import React from 'react';

import { PointDataRenderer } from './point-data-renderer';

export const BasicPointDataRenderer = () => {
  return (
    <PointDataRenderer
      data={{
        deviceGuid: 'fake-device-guid',
        deviceType: 'fake-device-type',
        name: 'fake-point-name',
        pointCode: '1001000',
        unit: '%',
        originalValue: 10.1,
        value: 10.1,
        formattedText: '10.1%',
        disabled: false,
        status: 'alarm',
      }}
    />
  );
};
