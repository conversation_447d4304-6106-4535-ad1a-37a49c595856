import React, { useMemo } from 'react';
import { useSelector } from 'react-redux';

import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { PointsLineModalButton } from '@manyun/monitoring.chart.points-line';
import { PointsStateLineModalButton } from '@manyun/monitoring.chart.points-state-line';
import { getMonitoringData } from '@manyun/monitoring.state.subscriptions';
import { generateGetPointMonitoringData } from '@manyun/monitoring.util.get-monitoring-data';

import { PointDataRenderer } from './point-data-renderer';

type PointRendererProps = {
  device: {
    deviceGuid: string;
    deviceType: string;
  };
  point: {
    dataType: string;
    code: string;
  };
};

export function PointRenderer({ device, point }: PointRendererProps) {
  const { devicesRealtimeData, devicesAlarmsData } = useSelector(getMonitoringData);
  const configUtil = new ConfigUtil(useSelector(selectCurrentConfig));
  const getPointMonitoringData = generateGetPointMonitoringData({
    realtimeData: devicesRealtimeData,
    alarmsData: devicesAlarmsData,
    configUtil,
  });

  const isAnalogSignal = point.dataType === 'AI' || point.dataType === 'AO';
  const isDigitalSignal = point.dataType === 'DI' || point.dataType === 'DO';
  const pointData = getPointMonitoringData(device, {
    hardCodedPointCode: point.code,
    formatted: isAnalogSignal,
    reflected: isDigitalSignal,
  });

  return <PointDataRenderer key={point.code} data={pointData} />;
}

type PointRendereModalButtonProps = {
  idc: string;
  device: {
    guid: string;
    type: string;
  };
  point: {
    name: string;
    code: string;
    dataType: 'AI' | 'DI' | 'AO' | 'DO';
    unit: string | null;
    validLimits: string[];
  };
};

export function PointRendereModalButton({ idc, device, point }: PointRendereModalButtonProps) {
  const isAnalogSignal = point.dataType === 'AI' || point.dataType === 'AO';
  const isDigitalSignal = point.dataType === 'DI' || point.dataType === 'DO';

  const pointGuids = useMemo(() => {
    return [
      {
        deviceGuid: device.guid,
        pointCode: point.code,
        unit: point.unit,
      },
    ];
  }, [device.guid, point.code, point.unit]);

  const seriesOption = useMemo(() => {
    return [{ name: point.name }];
  }, [point.name]);

  const currentText = (
    <PointRenderer
      device={{
        deviceGuid: device.guid,
        deviceType: device.type,
      }}
      point={{
        dataType: point.dataType,
        code: point.code,
      }}
    />
  );
  return (
    <>
      {isAnalogSignal && (
        <PointsLineModalButton
          idcTag={idc}
          btnText={currentText}
          modalText={point.name}
          pointGuids={pointGuids}
          seriesOption={seriesOption}
        />
      )}
      {isDigitalSignal && (
        <PointsStateLineModalButton
          idcTag={idc}
          btnText={currentText}
          modalText={point.name}
          pointGuids={pointGuids}
          seriesOption={seriesOption}
          validLimitsMap={point.validLimits || {}}
        />
      )}
    </>
  );
}
