import React from 'react';

import type { ButtonProps } from '@manyun/base-ui.ui.button';

import { PointsLineModalButton } from '@manyun/monitoring.chart.points-line';
import type { Variant } from '@manyun/monitoring.chart.points-line/points-line';
import { PointsStateLineModalButton } from '@manyun/monitoring.chart.points-state-line';
import type { DataType } from '@manyun/monitoring.model.point';
import { PointDataRenderer } from '@manyun/monitoring.ui.point-data-renderer';
import type { PointData } from '@manyun/monitoring.util.get-monitoring-data';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

/** 实时测点 */
export const PointChartText = ({
  style,
  pointData,
  spaceGuid,
  guid,
  variant = 'dashboard',
  dataType,
  showUnit,
  linkTextColorType = 'default',
}: {
  style?: React.CSSProperties;
  spaceGuid: string;
  pointData: PointData | null;
  dataType: DataType;
  guid?: string;
  variant?: Variant;
  showUnit?: boolean;
  linkTextColorType?: ButtonProps['linkTextColorType'];
}) => {
  if (!pointData) {
    return <>--</>;
  }
  const { idc } = getSpaceGuidMap(spaceGuid);
  const isAnalogSignal = dataType === 'AI' || dataType === 'AO';

  const btn = <PointDataRenderer data={pointData} style={style} showUnit={showUnit} />;

  const pointGuids = [
    {
      deviceGuid: guid || pointData.deviceGuid,
      pointCode: pointData.pointCode,
      unit: pointData.unit,
    },
  ].filter(
    (
      point
    ): point is {
      deviceGuid: string;
      pointCode: string;
      unit: string | null;
    } => typeof point.pointCode === 'string'
  );

  const seriesOption = [
    {
      name: pointData.name ?? '',
    },
  ];

  if (!pointGuids.length) {
    return null;
  }

  return isAnalogSignal ? (
    <PointsLineModalButton
      variant={variant}
      idcTag={idc!}
      btnText={btn}
      modalText={pointData?.name}
      pointGuids={pointGuids}
      seriesOption={seriesOption}
      linkTextColorType={linkTextColorType}
    />
  ) : (
    <PointsStateLineModalButton
      variant={variant}
      idcTag={idc!}
      btnText={btn}
      modalText={pointData?.name}
      pointGuids={pointGuids}
      seriesOption={seriesOption}
      validLimitsMap={pointData?.valueMapping!}
      linkTextColorType={linkTextColorType}
    />
  );
};
