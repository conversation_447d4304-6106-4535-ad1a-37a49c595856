import React from 'react';

import ExperimentTwoTone from '@ant-design/icons/es/icons/ExperimentTwoTone';

import { Popover } from '@manyun/base-ui.ui.popover';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';

import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import type { PointDataState } from '@manyun/monitoring.model.point';
import type { DiPointValue, PointData } from '@manyun/monitoring.util.get-monitoring-data';

export type PointDataRendererProps = {
  style?: React.CSSProperties;
  /**
   * 是否展示测点单位
   *
   * @defaultValue `true`
   */
  showUnit?: boolean;
  /**
   * 默认渲染成 `<Tag />` 样式，有背景色
   */
  shape?: 'typography';
  data: PointData;
};

export function PointDataRenderer({
  style,
  showUnit = true,
  shape = 'typography',
  data,
}: PointDataRendererProps) {
  const isDiPoint = data.value !== null && typeof data.value == 'object';

  const text = isDiPoint
    ? (data.value as DiPointValue).NAME
    : showUnit
    ? data.formattedText
    : data.value;
  let node: JSX.Element = (
    <PointStateRenderer style={style} shape={shape} state={data.status}>
      {text}
    </PointStateRenderer>
  );

  if (isDiPoint && (data.value as DiPointValue).ERROR_OUT_OF_RANGE) {
    node = <OutOfRangePointDataRenderer data={data} />;
  }

  if (env.__DEBUG_MODE__) {
    return (
      <Popover
        title="*** Debugging Point Data ***"
        content={
          <Typography.Text
            code
          >{`${data.deviceType}.${data.pointCode}=${data.originalValue}`}</Typography.Text>
        }
      >
        <ExperimentTwoTone /> {node}
      </Popover>
    );
  }

  return node;
}

type PointStateTagProps = {
  style?: React.CSSProperties;
  /**
   * 默认渲染成 `<Tag />` 样式，有背景色
   */
  shape?: 'typography';
  state: PointDataState;
  children: React.ReactNode;
};

const COLORS_MAPPER: Record<PointDataState, string> = {
  default: 'default',
  alarm: 'error',
  warning: 'warning',
  /** 超过配置范围 */
  'out-of-range': 'secondary',
  /** 通讯中断 */
  'comms-interrupted': 'secondary',
};

function PointStateRenderer({ style, shape = 'typography', state, children }: PointStateTagProps) {
  if (state === 'default') {
    return (
      <Tag style={{ color: 'unset', ...style }} shape={shape}>
        {children}
      </Tag>
    );
  }

  return (
    <Tag style={style} shape={shape} color={COLORS_MAPPER[state]}>
      {children}
    </Tag>
  );
}

type OutOfRangeDIPointDataRendererProps = {
  data: PointData;
};

function OutOfRangePointDataRenderer({ data }: OutOfRangeDIPointDataRendererProps) {
  return (
    <Tooltip title={String(data.originalValue)} getTooltipContainer={triggerNode => triggerNode}>
      <Typography.Text type="danger" style={{ fontSize: '12px' }}>
        错误值
      </Typography.Text>
    </Tooltip>
  );
}
