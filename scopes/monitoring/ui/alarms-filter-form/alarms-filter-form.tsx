import React, { useMemo, useState } from 'react';
import { useDeepCompareEffect } from 'react-use';

import { FilterOutlined } from '@ant-design/icons';
import camelCase from 'lodash.camelcase';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Dropdown } from '@manyun/base-ui.ui.dropdown';
import { Form } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { AlarmExpectStatusSelect } from '@manyun/monitoring.ui.alarm-expect-status-select';
import { AlarmLevelSelect } from '@manyun/monitoring.ui.alarm-level-select';
import { AlarmLifecycleStateSelect } from '@manyun/monitoring.ui.alarm-lifecycle-state-select';
import { AlarmMergeStatusSelect } from '@manyun/monitoring.ui.alarm-merge-status-select';
import { AlarmStateSelect } from '@manyun/monitoring.ui.alarm-state-select';
import { AlarmTypeSelect } from '@manyun/monitoring.ui.alarm-type';
import { DynamicBaselineConditionSelect } from '@manyun/monitoring.ui.dynamic-baseline-condition-select';
import { FeekbackResultsSelect } from '@manyun/monitoring.ui.feekback-results-select';
import { MonitoringStatusSelect } from '@manyun/monitoring.ui.monitoring-status-select';
import { DeviceTypeCascader } from '@manyun/resource-hub.ui.device-type-cascader';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import { RoomTypeSelect } from '@manyun/resource-hub.ui.room-type-select';

import type {
  AlarmsFilterFormProps,
  FilterFormKey,
  FilterFormParams,
  FilterFormValues,
} from './alarms-filter-form.type';

export function AlarmsFilterForm({
  idc,
  onChange,
  showFormItems,
  defaultValue,
  disabledItems,
  deviceTypeSelectable,
}: AlarmsFilterFormProps) {
  const [open, setOpen] = useState(false);
  const [showActiveIcon, setShowActiveIcon] = useState(false);

  useDeepCompareEffect(() => {
    if (defaultValue && Object.values(defaultValue).some(value => value)) {
      setShowActiveIcon(true);
    }
  }, [defaultValue]);

  const onHide = () => {
    setOpen(false);
  };

  const onOpenChange = (open: boolean) => {
    setOpen(open);
  };

  const onFilterFormChange = (params: FilterFormParams) => {
    onChange(params);
    if (params && Object.values(params).some(value => value)) {
      setShowActiveIcon(true);
    } else {
      setShowActiveIcon(false);
    }
  };

  return (
    <Dropdown
      open={open}
      dropdownRender={() => (
        <FilterForm
          idc={idc}
          showFormItems={showFormItems}
          defaultValue={defaultValue}
          disabledItems={disabledItems}
          deviceTypeSelectable={deviceTypeSelectable}
          onHide={onHide}
          onChange={onFilterFormChange}
        />
      )}
      placement="bottomRight"
      trigger={['click']}
      arrow={{ pointAtCenter: true }}
      onOpenChange={onOpenChange}
    >
      <Tooltip title="筛选">
        {showActiveIcon ? (
          <FilterOutlined style={{ color: `var(--${prefixCls}-primary-color)` }} />
        ) : (
          <FilterOutlined />
        )}
      </Tooltip>
    </Dropdown>
  );
}

type FilterFormProps = Omit<AlarmsFilterFormProps, 'allAlarmState'> & {
  onHide: () => void;
};

function FilterForm({
  onHide,
  onChange,
  showFormItems = [
    'alarm-types',
    'alarm-levels',
    'alarm-states',
    'alarm-lifecycle-states',
    'room-guids',
    'room-types',
    'device-types',
    'last-modify-userId',
    'alarms-triggered-at-time-range',
    'alarms-recovered-at-time-range',
  ],
  idc,
  defaultValue,
  disabledItems,
  deviceTypeSelectable,
}: FilterFormProps) {
  const [form] = Form.useForm();

  const formItems = useDeepCompareMemo(() => {
    return [
      {
        label: '告警类型',
        name: 'alarmTypes',
        colSpan: 6,
        render: <AlarmTypeSelect allowClear />,
      },
      {
        label: '告警级别',
        name: 'alarmLevels',
        colSpan: 6,
        render: <AlarmLevelSelect trigger="onDidMount" allowClear />,
      },
      {
        label: '告警状态',
        name: 'alarmStates',
        colSpan: 6,
        render: <AlarmStateSelect mode="multiple" maxTagCount="responsive" />,
      },
      {
        label: '处理状态',
        name: 'alarmLifecycleStates',
        colSpan: 6,
        render: <AlarmLifecycleStateSelect mode="multiple" maxTagCount="responsive" />,
      },
      {
        label: '监测状态',
        name: 'monitoringStatus',
        colSpan: 6,
        render: <MonitoringStatusSelect allowClear />,
      },
      {
        label: '反馈结果',
        name: 'feekbackResults',
        colSpan: 6,
        render: <FeekbackResultsSelect allowClear />,
      },
      {
        label: '收敛状态',
        name: 'mergeStatus',
        colSpan: 12,
        render: <AlarmMergeStatusSelect mode="multiple" allowClear />,
      },
      {
        label: '变更状态',
        name: 'expectStatus',
        colSpan: 12,
        render: <AlarmExpectStatusSelect mode="multiple" allowClear />,
      },
      {
        label: '包间',
        name: 'roomGuids',
        colSpan: 12,
        render: (
          <LocationTreeSelect
            idc={idc}
            nodeTypes={['BLOCK', 'ROOM']}
            maxTagCount="responsive"
            authorizedOnly
            multiple
            allowClear
            treeDefaultExpandedKeys={defaultValue?.roomGuids}
          />
        ),
      },
      {
        label: '设备名称',
        name: 'deviceName',
        colSpan: 12,
        render: <Input allowClear />,
      },
      {
        label: '包间类型',
        name: 'roomTypes',
        colSpan: 12,
        render: <RoomTypeSelect mode="multiple" />,
      },
      {
        label: '监测模型',
        name: 'monitoringModel',
        colSpan: 12,
        render: <DynamicBaselineConditionSelect allowClear />,
      },
      {
        label: '资产分类',
        name: 'deviceTypes',
        colSpan: 12,
        render: (
          <DeviceTypeCascader
            multiple
            numbered
            allowClear
            maxTagCount="responsive"
            dataType={['snDevice']}
            selectable={deviceTypeSelectable}
            treeDefaultExpandedKeys={defaultValue?.deviceTypes}
          />
        ),
      },
      {
        label: '处理人',
        name: 'lastModifyUserId',
        colSpan: 12,
        render: <UserSelect allowClear />,
      },
      {
        label: '告警开始时间',
        name: 'alarmsTriggeredAtTimeRange',
        colSpan: 24,
        render: (
          <DatePicker.RangePicker
            showTime={{ format: 'HH:mm:ss' }}
            format="YYYY-MM-DD HH:mm:ss"
            allowClear
          />
        ),
      },
      {
        label: '告警恢复时间',
        name: 'alarmsRecoveredAtTimeRange',
        colSpan: 24,
        render: (
          <DatePicker.RangePicker
            showTime={{ format: 'HH:mm:ss' }}
            format="YYYY-MM-DD HH:mm:ss"
            allowClear
          />
        ),
      },
    ];
  }, [defaultValue?.deviceTypes, idc, disabledItems]);

  const showFormItemKeys = useDeepCompareMemo(
    () => showFormItems.map(item => camelCase(item)),
    [showFormItems]
  );

  const newformItems = useMemo(
    () => formItems.filter(item => showFormItemKeys.includes(item.name)),
    [formItems, showFormItemKeys]
  );

  const onFinish = (values: FilterFormValues) => {
    const {
      alarmTypes,
      alarmLevels,
      alarmStates,
      alarmLifecycleStates,
      monitoringStatus,
      feekbackResults,
      expectStatus,
      mergeStatus,
      roomGuids,
      deviceName,
      roomTypes,
      monitoringModel,
      deviceTypes,
      lastModifyUserId,
      alarmsTriggeredAtTimeRange,
      alarmsRecoveredAtTimeRange,
    } = values;

    const params: FilterFormParams = {
      alarmTypes,
      alarmLevels,
      alarmStates: alarmStates && alarmStates.length ? alarmStates : undefined,
      alarmLifecycleStates: alarmLifecycleStates?.length ? alarmLifecycleStates : undefined,
      monitoringStatus,
      feekbackResults,
      expectStatusList: expectStatus && expectStatus.length ? expectStatus : undefined,
      mergeStatusList: mergeStatus && mergeStatus.length ? mergeStatus : undefined,
      roomGuids: roomGuids && roomGuids.length ? roomGuids : undefined,
      roomTypes: roomTypes && roomTypes.length ? roomTypes : undefined,
      deviceName,
      monitoringModel,
      deviceTypes: deviceTypes && deviceTypes.length ? deviceTypes : undefined,
      lastModifyUserId: lastModifyUserId ? lastModifyUserId.id : undefined,
      lastModifyUserName: lastModifyUserId ? lastModifyUserId.name : undefined,
      alarmsTriggeredAtTimeRange:
        Array.isArray(alarmsTriggeredAtTimeRange) && alarmsTriggeredAtTimeRange.length > 1
          ? [
              alarmsTriggeredAtTimeRange[0].millisecond(0).valueOf(),
              alarmsTriggeredAtTimeRange[1].millisecond(999).valueOf(),
            ]
          : undefined,
      alarmsRecoveredAtTimeRange:
        Array.isArray(alarmsRecoveredAtTimeRange) && alarmsRecoveredAtTimeRange.length > 1
          ? [
              alarmsRecoveredAtTimeRange[0].millisecond(0).valueOf(),
              alarmsRecoveredAtTimeRange[1].millisecond(999).valueOf(),
            ]
          : undefined,
    };

    onChange(params);
    onHide();
  };

  const onReset = async () => {
    const values = await form.validateFields();
    onFinish(values);
  };

  return (
    <Card style={{ width: 516 }}>
      <Form
        layout="vertical"
        form={form}
        initialValues={defaultValue}
        onFinish={onFinish}
        onReset={onReset}
      >
        <Row gutter={16}>
          {newformItems.map(item => {
            const { label, name, colSpan, render } = item;

            return (
              <Col key={name} span={colSpan}>
                <Form.Item label={label} name={name}>
                  {React.cloneElement(render, {
                    style: { ...render.props?.style },
                    disabled: disabledItems
                      ?.map(item => camelCase(item))
                      ?.includes(name as FilterFormKey),
                  })}
                </Form.Item>
              </Col>
            );
          })}
        </Row>
        <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
          <Space>
            <Button htmlType="reset">重置</Button>
            <Button type="primary" htmlType="submit">
              搜索
            </Button>
          </Space>
        </div>
      </Form>
    </Card>
  );
}
