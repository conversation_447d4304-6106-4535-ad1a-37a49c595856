import type { Moment } from 'moment';

import type {
  BackMergeStatus,
  BackendAlarmLifecycleState,
  BackendAlarmState,
  BackendExpectStatus,
} from '@manyun/monitoring.model.alarm';

export type FilterFormParams = {
  notifyContent?: string | undefined;
  alarmTypes?: string;
  alarmLevels?: string;
  monitoringStatus?: string;
  feekbackResults?: string;
  expectStatusList?: BackendExpectStatus[];
  mergeStatusList?: BackMergeStatus[];
  alarmStates?: BackendAlarmState[];
  alarmLifecycleStates?: BackendAlarmLifecycleState[];
  roomGuids?: string[];
  monitoringModel?: string;
  roomTypes?: string[];
  deviceName?: string;
  deviceTypes?: string[];
  lastModifyUserId?: string;
  lastModifyUserName?: string;
  alarmsTriggeredAtTimeRange?: number[];
  alarmsRecoveredAtTimeRange?: number[];
};

export type FilterFormValues = {
  alarmTypes?: string;
  alarmLevels?: string;
  monitoringStatus?: string;
  feekbackResults?: string;
  expectStatus?: BackendExpectStatus[];
  mergeStatus?: BackMergeStatus[];
  alarmStates?: BackendAlarmState[];
  alarmLifecycleStates?: BackendAlarmLifecycleState[];
  roomGuids?: string[];
  monitoringModel?: string;
  roomTypes?: string[];
  deviceName?: string;
  deviceTypes?: string[];
  lastModifyUserId?: { id: string; name: string };
  alarmsTriggeredAtTimeRange?: Moment[];
  alarmsRecoveredAtTimeRange?: Moment[];
};

export type FilterFormKey =
  | 'alarm-types'
  | 'alarm-levels'
  | 'monitoring-status'
  | 'feekback-results'
  | 'expect-status'
  | 'merge-status'
  | 'alarm-states'
  | 'alarm-lifecycle-states'
  | 'room-guids'
  | 'monitoring-model'
  | 'room-types'
  | 'device-name'
  | 'device-types'
  | 'last-modify-userId'
  | 'alarms-triggered-at-time-range'
  | 'alarms-recovered-at-time-range';

export type AlarmsFilterFormProps = {
  idc: string;
  onChange: (params: FilterFormParams) => void;
  showFormItems?: FilterFormKey[];
  defaultValue?: FilterFormParams;
  allAlarmState?: boolean;
  disabledItems?: FilterFormKey[];
  deviceTypeSelectable?: string[];
};
