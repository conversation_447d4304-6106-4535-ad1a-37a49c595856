import React, { useState } from 'react';

import type { InputProps } from '@manyun/base-ui.ui.input';
import { Input } from '@manyun/base-ui.ui.input';
import { Space } from '@manyun/base-ui.ui.space';

import { AlarmsFilterForm } from './alarms-filter-form';
import type { AlarmsFilterFormProps, FilterFormParams } from './alarms-filter-form.type';

type AdvancedAlarmsFilterFormProps = AlarmsFilterFormProps & {
  inputProps?: Omit<InputProps, 'onChange' | 'onSearch'>;
};
export const AdvancedAlarmsFilterForm = ({
  idc,
  onChange,
  showFormItems,
  defaultValue,
  inputProps,
  disabledItems,
  deviceTypeSelectable,
}: AdvancedAlarmsFilterFormProps) => {
  const [notifyContent, setNotifyContent] = useState<string>();

  const [formParams, setFormParams] = useState<FilterFormParams | undefined>(defaultValue);

  const onSearch = async (value: string) => {
    setNotifyContent(value);
    onChange({ ...formParams, notifyContent: value });
  };

  const onFilterForm = async (params: FilterFormParams) => {
    setFormParams(params);
    onChange({ ...params, notifyContent });
  };

  return (
    <Space size={16}>
      <Input.Search
        style={{ width: 216 }}
        placeholder="输入搜索内容"
        {...inputProps}
        onChange={e => setNotifyContent(e.target.value)}
        onSearch={onSearch}
      />
      <AlarmsFilterForm
        idc={idc}
        showFormItems={showFormItems}
        defaultValue={defaultValue}
        disabledItems={disabledItems}
        deviceTypeSelectable={deviceTypeSelectable}
        onChange={onFilterForm}
      />
    </Space>
  );
};
