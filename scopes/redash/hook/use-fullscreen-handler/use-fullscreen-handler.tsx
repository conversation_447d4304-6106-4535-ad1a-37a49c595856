import { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';

// import { has } from 'lodash-es';
// 确认参数的类型
export type Params = {
  fullscreen: string;
};
// function getFllscreenFromUrl() {
//   const { search } = useLocation();
//   const urlSearchParams = new URLSearchParams(search);
//   if (!urlSearchParams.get('fullscreen')) {
//     return false;
//   }
//   return true;
// }

export function useFullscreenHandler() {
  // const history = useHistory<undefined>();
  const { search } = useLocation();
  const urlSearchParams = new URLSearchParams(search);
  const [fullscreen, setFullscreenFlag] = useState(
    urlSearchParams.get('fullscreen') ? true : false
  );

  useEffect(() => {
    // setFullscreenFlag(fullscreen);
    if (fullscreen) {
      urlSearchParams.set('fullscreen', '');
      if (document.documentElement.requestFullscreen) {
        document.documentElement.requestFullscreen();
      }
    } else {
      urlSearchParams.delete('fullscreen');
      if (document.exitFullscreen) {
        if (document.fullscreenElement) {
          document.exitFullscreen();
        }
      }
    }

    // history.push(`/path/to/${fullscreen}`);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fullscreen]);

  const toggleFullscreen = () => setFullscreenFlag(!fullscreen);
  return [fullscreen, toggleFullscreen] as const;
}
