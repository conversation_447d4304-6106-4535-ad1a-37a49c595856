/* eslint-disable @typescript-eslint/no-explicit-any */
// @ts-nocheck disabled for now
import { compact, extend, includes, isEmpty } from 'lodash-es';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import QueryResultError from '@manyun/redash.entity.query';
import { useFullscreenHandler } from '@manyun/redash.hook.use-fullscreen-handler';

function getAffectedWidgets(widgets: any[], updatedParameters = [] as any[]) {
  return !isEmpty(updatedParameters)
    ? widgets.filter(widget =>
        Object.values<any>(widget.getParameterMappings())
          .filter(({ type }) => type === 'dashboard-level')
          .some(({ mapTo }) =>
            includes(
              updatedParameters.map((p: any) => p!.name),
              mapTo
            )
          )
      )
    : widgets;
}

export function useDashboard(dashboardData: any) {
  const [dashboard, setDashboard] = useState(dashboardData);
  const [filters, setFilters] = useState([]);
  const [refreshing, setRefreshing] = useState(false);
  // const [gridDisabled, setGridDisabled] = useState(false);
  // 读取的是url中的参数;
  const globalParameters = useMemo(() => dashboard.getParametersDefs(), [dashboard]);
  const loadWidget = useCallback((widget, forceRefresh = false) => {
    setDashboard((currentDashboard: any) => extend({}, currentDashboard));
    return widget
      .load(forceRefresh)
      .catch((error: any) => {
        // QueryResultErrors are expected
        if (error instanceof QueryResultError) {
          return;
        }
        return Promise.reject(error);
      })
      .finally(() => setDashboard((currentDashboard: any) => extend({}, currentDashboard)));
  }, []);

  const refreshWidget = useCallback(widget => loadWidget(widget, true), [loadWidget]);
  const dashboardRef = useRef<any>();
  dashboardRef.current = dashboard;

  const loadDashboard = useCallback(
    (forceRefresh = false, updatedParameters = []) => {
      if (dashboardRef!.current!['widgets']) {
        const affectedWidgets = getAffectedWidgets(
          dashboardRef!.current!['widgets'],
          updatedParameters
        );

        // const affectedWidgets = dashboardRef.current.widgets;
        const loadWidgetPromises = compact(
          affectedWidgets.map((widget: any) =>
            loadWidget(widget, forceRefresh).catch((error: any) => error)
          )
        );

        return Promise.all(loadWidgetPromises).then(() => {
          // const queryResults = compact(
          //   map(dashboardRef!.current!['widgets'], (widget: any) => widget.getQueryResult())
          // );
          // 查看是否是在这一层完成 queryresult的查询
          // 是的，既然如此修改这一层的内容
          // const updatedFilters = collectDashboardFilters(
          //   dashboardRef.current,
          //   queryResults,
          //   location.search
          // );
          // 先不进行筛选操作
          // setFilters(updatedFilters);
          setFilters([]);
        });
      } else {
        return;
      }
      // 先不做根据参数的affected的判断
    },
    [loadWidget]
  );

  const refreshDashboard = useCallback(
    updatedParameters => {
      if (!refreshing) {
        setRefreshing(true);
        loadDashboard(true, updatedParameters)!.finally(() => setRefreshing(false));
      }
    },
    [refreshing, loadDashboard]
  );

  //   const archiveDashboard = useCallback(() => {
  //     recordEvent('archive', 'dashboard', dashboard.id);
  //     Dashboard.delete(dashboard).then((updatedDashboard) =>
  //       setDashboard((currentDashboard) =>
  //         extend({}, currentDashboard, pick(updatedDashboard, ['is_archived']))
  //       )
  //     );
  //   }, [dashboard]); // eslint-disable-line react-hooks/exhaustive-deps

  // 不需要分享功能
  // const showShareDashboardDialog = useCallback(() => {
  //   const handleDialogClose = () => setDashboard(currentDashboard => extend({}, currentDashboard));

  //   ShareDashboardDialog.showModal({
  //     dashboard,
  //     hasOnlySafeQueries,
  //   })
  //     .onClose(handleDialogClose)
  //     .onDismiss(handleDialogClose);
  // }, [dashboard, hasOnlySafeQueries]);

  // const showAddTextboxDialog = useCallback(() => {
  //   TextboxDialog.showModal({
  //     isNew: true,
  //   }).onClose((text) =>
  //     dashboard
  //       .addWidget(text)
  //       .then(() => setDashboard((currentDashboard) => extend({}, currentDashboard)))
  //   );
  // }, [dashboard]);

  //   const showAddWidgetDialog = useCallback(() => {
  //     AddWidgetDialog.showModal({
  //       dashboard,
  //     }).onClose(({ visualization, parameterMappings }) =>
  //       dashboard
  //         .addWidget(visualization, {
  //           parameterMappings: editableMappingsToParameterMappings(parameterMappings),
  //         })
  //         .then((widget) => {
  //           const widgetsToSave = [
  //             widget,
  //             ...synchronizeWidgetTitles(widget.options.parameterMappings, dashboard.widgets),
  //           ];
  //           return Promise.all(widgetsToSave.map((w) => w.save())).then(() =>
  //             setDashboard((currentDashboard) => extend({}, currentDashboard))
  //           );
  //         })
  //     );
  //   }, [dashboard]);

  // 刷新暂时不考虑
  // const [refreshRate, setRefreshRate, disableRefreshRate] = useRefreshRateHandler(refreshDashboard);
  const [fullscreen, toggleFullscreen] = useFullscreenHandler();
  // const editModeHandler = useEditModeHandler(
  //   !gridDisabled && canEditDashboard,
  //   dashboard.widgets
  // );

  useEffect(() => {
    setDashboard(dashboardData);
    loadDashboard();
  }, [dashboardData]); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    document.title = dashboard.name;
  }, [dashboard.name]);

  // reload dashboard when filter option changes
  useEffect(() => {
    loadDashboard();
  }, [dashboard.dashboard_filters_enabled]); // eslint-disable-line react-hooks/exhaustive-deps

  return {
    dashboard,
    globalParameters,
    refreshing,
    filters,
    setFilters,
    loadDashboard,
    refreshDashboard,
    loadWidget,
    refreshWidget,
    fullscreen,
    toggleFullscreen,
  };
  // updateDashboard,
  // togglePublished,
  // archiveDashboard,
  // removeWidget,
  // canEditDashboard,
  // isDashboardOwnerOrAdmin,
  // refreshRate,
  // setRefreshRate,
  // disableRefreshRate,
  // ...editModeHandler,
  // gridDisabled,
  // setGridDisabled,
  // showShareDashboardDialog,
  // showAddWidgetDialog,
  // managePermissions,
}

export default useDashboard;
