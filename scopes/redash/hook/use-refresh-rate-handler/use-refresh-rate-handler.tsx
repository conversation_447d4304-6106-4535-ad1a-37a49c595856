import { isNaN } from 'lodash-es';
import { useEffect, useMemo, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

import { useImmutableCallback } from '@manyun/redash.hook.use-immutable-callback';

export type Params = {
  refresh: string;
};

function getRefreshRateFromUrl(refresh: string) {
  const refreshRate = parseFloat(refresh);
  return isNaN(refreshRate) ? null : refreshRate;
}

export function useRefreshRateHandler(refreshDashboard: any) {
  const { refresh = '' } = useParams<Params>();
  const [refreshRate, setRefreshRate] = useState(getRefreshRateFromUrl(refresh));
  const navigate = useNavigate();

  // `refreshDashboard` may change quite frequently (on every update of `dashboard` instance), but we
  // have to keep the same timer running, because timer will restart when re-creating, and instead of
  // running refresh every N seconds - it will run refresh every N seconds after last dashboard update
  // (which is not right obviously)
  const doRefreshDashboard = useImmutableCallback(refreshDashboard);

  // URL and timer should be updated only when `refreshRate` changes
  useEffect(() => {
    if (refreshRate) {
      setRefreshRate(refreshRate);
      navigate(`/path/to/${refreshRate}`);

      const refreshTimer = setInterval(doRefreshDashboard, refreshRate * 1000);
      return () => clearInterval(refreshTimer);
    } else {
      return;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [refreshRate, doRefreshDashboard]);

  return useMemo(
    () => [refreshRate, (rate: any) => setRefreshRate(rate), () => setRefreshRate(null)],
    [refreshRate]
  );
}

// export function useRefreshRateHandler() {}
