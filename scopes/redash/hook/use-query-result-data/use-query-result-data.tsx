import { includes, uniqBy } from 'lodash-es';
import moment from 'moment';
import { useMemo } from 'react';

const filterTypes = ['filter', 'multi-filter', 'multiFilter'];

function getColumnNameWithoutType(column: any) {
  let typeSplit;
  if (column.indexOf('::') !== -1) {
    typeSplit = '::';
  } else if (column.indexOf('__') !== -1) {
    typeSplit = '__';
  } else {
    return column;
  }

  const parts = column.split(typeSplit);
  if (parts[0] === '' && parts.length === 2) {
    return parts[1];
  }

  if (!includes(filterTypes, parts[1])) {
    return column;
  }

  return parts[0];
}

function getColumnFriendlyName(column: any) {
  return getColumnNameWithoutType(column).replace(/(?:^|\s)\S/g, (a: any) => a.toUpperCase());
}
function getFilters(columns: any, rows: any) {
  if (!columns) {
    return [];
  }

  const filters = [] as any;

  columns.forEach((col: any) => {
    const name = col.name;
    const type = name.split('::')[1] || name.split('__')[1];
    if (includes(filterTypes, type)) {
      // filter found
      const filter = {
        name,
        friendlyName: getColumnFriendlyName(name),
        column: col,
        values: [],
        multiple: type === 'multiFilter' || type === 'multi-filter',
      };
      filters.push(filter);
    }
  }, columns);

  rows.forEach((row: any) => {
    filters.forEach((filter: any) => {
      filter.values.push(row[filter.name]);
      if (filter.values.length === 1) {
        if (filter.multiple) {
          filter.current = [row[filter.name]];
        } else {
          filter.current = row[filter.name];
        }
      }
    });
  });

  filters.forEach((filter: any) => {
    filter.values = uniqBy(filter.values, v => {
      if (moment.isMoment(v)) {
        return v.unix();
      }
      return v;
    });
  });

  return filters;
}

function getQueryResultData(queryResult: any, queryResultStatus = 'done') {
  return {
    // status: queryResultStatus || invoke(queryResult, 'getStatus') || null,
    // 先默认，默认status都没问题
    status: queryResultStatus || 'done' || null,
    columns: queryResult?.query_result?.data?.columns || [],
    rows: queryResult?.query_result?.data?.rows || [],
    filters:
      getFilters(queryResult?.query_result?.data?.columns, queryResult?.query_result?.data?.rows) ||
      [],
    updatedAt: queryResult?.query_result?.retrieved_at || queryResult?.query_result?.updatedAt,
    // retrievedAt: get(queryResult, 'query_result.retrieved_at', null),
    // truncated: invoke(queryResult, 'getTruncated') || null,
    // log: invoke(queryResult, 'getLog') || [],
    // error: invoke(queryResult, 'getError') || null,
    runtime: queryResult?.query_result?.runtime || null,
    // metadata: get(queryResult, 'query_result.data.metadata', {}),
  };
}

export function useQueryResultData(queryResult: any) {
  // make sure it re-executes when queryResult status changes
  // const queryResultStatus = invoke(queryResult, 'getStatus');
  // 先默认传 done
  const queryResultStatus = 'done';
  return useMemo(
    () => getQueryResultData(queryResult, queryResultStatus),
    [queryResult, queryResultStatus]
  );
}
