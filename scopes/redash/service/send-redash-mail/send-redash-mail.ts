/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-22
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, Request } from '@glpdev/symphony.services.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './send-redash-mail.type.js';

const endpoint = '/notify/send/msg';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/92/interface/api/2672)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends Request = Request>(request: T) {
  return async (variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      businessId: variant.id,
      bizScenes: variant.bizScenes,
      secondBizScenes: variant.secondBizScenes,
      notifyCode: variant.code,
      channels: variant.channels,
      notifyUserList: variant.users,
      fieldJumpContentList: variant.fieldJumpContentList,
      params: variant.params,
      idcTag: variant.idc,
      blockGuidList: variant.blocks,
      attachmentFilePathSet: variant.fileList,
      emailSubject: variant.title,
    };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null || !data) {
      return {
        ...rest,
        error,
        data: null,
      };
    }

    return { error, data: data, ...rest };
  };
}
