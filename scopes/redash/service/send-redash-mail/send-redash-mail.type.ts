/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-22
 *
 * @packageDocumentation
 */
import type { WriteBackendResponse } from '@glpdev/symphony.services.request';

export type SvcQuery = {
  /** 业务ID */
  id: string;
  /** 业务场景，如：工单 */
  bizScenes?: string;
  /** 业务子场景 如：工单下的交接班工单*/
  secondBizScenes?: string;
  /** 通知类型编码，如作为报表邮件通知时传:FORM_REPORT */
  code?: string;
  /** 通知渠道 */
  channels?: string[];
  users?: MailToUser[];
  fieldJumpContentList?: string[];
  params?: { content: string };
  idc?: string;
  blocks?: string[];
  /** 邮件附件路径 */
  fileList?: string[];
  title?: string;
};

// 邮件通知的用户
export type MailToUser = {
  userId: string;
  email: string;
  mobile: string;
};

export type SvcRespData = boolean | null;

export type RequestRespData = boolean | null;

export type ApiQ = {
  businessId: string;
  bizScenes?: string;
  secondBizScenes?: string;
  notifyCode?: string;
  channels?: string[];
  notifyUserList?: MailToUser[];
  fieldJumpContentList?: string[];
  params?: { content: string };
  idcTag?: string;
  blockGuidList?: string[];
  attachmentFilePathSet?: string[];
  emailSubject?: string;
};

export type ApiR = WriteBackendResponse;
