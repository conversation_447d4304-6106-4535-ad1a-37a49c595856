/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-22
 *
 * @packageDocumentation
 */
import { request } from '@glpdev/symphony.services.request';
import type { EnhancedAxiosResponse } from '@glpdev/symphony.services.request';

import { getExecutor } from './send-redash-mail.js';
import type { SvcQuery, SvcRespData } from './send-redash-mail.type.js';

/**
 * @param variant
 * @returns
 */
export function sendRedashMail(variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  if (!request) {
    throw new Error('request instance expected.');
  }
  const executor = getExecutor(request);

  return executor(variant);
}
