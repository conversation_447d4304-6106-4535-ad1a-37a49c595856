/* eslint-disable @typescript-eslint/no-explicit-any */
import { isEmpty, toString } from 'lodash-es';

import Parameter from '@manyun/redash.entity.parameter';

class TextParameter extends Parameter {
  constructor(parameter: any, parentQueryId: any) {
    super(parameter, parentQueryId);
    this.setValue(parameter.value);
  }

  // eslint-disable-next-line class-methods-use-this
  normalizeValue(value: any) {
    const normalizedValue = toString(value);

    if (isEmpty(normalizedValue)) {
      return null;
    }
    return normalizedValue;
  }
}

export { TextParameter };
