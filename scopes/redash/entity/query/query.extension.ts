/* eslint-disable @typescript-eslint/no-explicit-any */
// @ts-nocheck pending refactor
import {
  each,
  extend,
  filter,
  find,
  has,
  includes,
  isArray,
  isEmpty,
  map,
  some,
  union,
  uniq,
  zipObject,
} from 'lodash-es';
import moment from 'moment';
import Mustache from 'mustache';

import { message } from '@manyun/base-ui.ui.message';
import { location } from '@manyun/redash.entity.location';
import { Parameter } from '@manyun/redash.entity.parameter';
import { createParameter } from '@manyun/redash.util.parameter-function';
import { fetchQueryAsyncWeb as fetchQueryAsync } from '@manyun/service.dcom.fetch-query-async';

function collectParams(parts: any[]) {
  let parameters = [] as any;

  parts.forEach(part => {
    if (part[0] === 'name' || part[0] === '&') {
      parameters.push(part[1].split('.')[0]);
    } else if (part[0] === '#') {
      parameters = union(parameters, collectParams(part[4]));
    }
  });
  return parameters;
}
async function fetchQueryData(id: number, parameters: any, applyAutoLimit: any, maxAge: number) {
  const { data, error } = await fetchQueryAsync({ id, parameters, max_age: maxAge || 60 });

  if (error) {
    message.error(error);
    return;
  } else {
    return data;
  }
}
export class Query {
  latest_query_data: null | undefined;
  options!: { apply_auto_limit?: any; parameters?: any };
  queryResult: { never: any } | undefined;
  query_status!: string;
  id: any;
  latest_query_data_id: null | undefined;
  $parameters: any;
  constructor(query: any) {
    extend(this, query);

    if (!has(this, 'options')) {
      this.options = {};
    }
    this.options.apply_auto_limit = !!this.options.apply_auto_limit;

    if (!isArray(this.options.parameters)) {
      this.options.parameters = [];
    }
  }

  // 获取query 的数据0

  async getByQueryId(id: number, parameters: any, applyAutoLimit: any, maxAge: number) {
    // 源码将qr 实例化为一个对象
    // const queryResult = new QueryResult();
    // 这里我们负责用json 来接收返回的拆寻结果
    this.query_status = 'loading';
    let retryTime = 1000;
    return new Promise(resolve => {
      const queryFn = () => {
        setTimeout(async () => {
          try {
            const data = await fetchQueryData(id, parameters, applyAutoLimit, maxAge);
            if (data && 'query_result' in data) {
              resolve(data);
              this.query_status = 'done';
            } else {
              if (this.query_status !== 'done' && retryTime < 5000) {
                retryTime += 1000;
                queryFn();
              }
              if (retryTime >= 5000) {
                this.query_status = 'error';
                resolve(data);
              }
            }
          } catch (error) {
            message.error('出错');
            resolve(error);
          }
        }, retryTime);
      };
      queryFn();
    });
  }

  paramsRequired() {
    return this.getParameters().isRequired();
  }

  hasParameters() {
    return this.getParametersDefs().length > 0;
  }

  setQueryResult(queryResult: any) {
    this.queryResult = queryResult;
  }
  async getQueryResult(maxAge: number) {
    const parameters = this.getParameters();

    const missingParams = parameters.getMissing();

    if (missingParams.length > 0) {
      message.error(`以下参数${missingParams.join(', ')} 缺少对应的值.`);

      return this.queryResult;
    }

    // 如果存在参数
    else if (parameters.isRequired()) {
      // Need to clear latest results, to make sure we don't use results for different params.
      this.latest_query_data = null;
      this.latest_query_data_id = null;
    }

    // 如果参数不存在的情况
    // 如果latest data存在
    else if (this.latest_query_data && maxAge !== 0) {
      // 则直接将 latest data 给queryResult（如果queryResult不存在的话）
      if (!this.queryResult) {
        // this.queryResult = new QueryResult({
        //   query_result: this.latest_query_data,
        // });
        // 在这里我就不把返回类型变为 QR，而是一个json对象
        this.queryResult = { query_result: this.latest_query_data } as any;
        return this.queryResult;
      }
    }

    return await this.getByQueryId(
      this.id,
      this.getParameters().getExecutionValues(),
      this.getAutoLimit(),
      maxAge
    );
  }

  // getQueryResultByText(maxAge, selectedQueryText) {
  //   const queryText = selectedQueryText || this.query;
  //   if (!queryText) {
  //     return new QueryResultError("Can't execute empty query.");
  //   }

  //   const parameters = this.getParameters().getExecutionValues({
  //     joinListValues: true,
  //   });
  //   const execute = () =>
  //     QueryResult.get(
  //       this.data_source_id,
  //       queryText,
  //       parameters,
  //       this.getAutoLimit(),
  //       maxAge,
  //       this.id
  //     );
  //   return this.prepareQueryResultExecution(execute, maxAge);
  // }

  // url暂不需要
  // getUrl(source: any, hash: any) {
  //   let url = `queries/${this.id}`;

  //   if (source) {
  //     url += '/source';
  //   }

  //   let params = {};
  //   if (this.getParameters().isRequired()) {
  //     this.getParametersDefs().forEach((param) => {
  //       extend(params, param.toUrlParams());
  //     });
  //   }
  //   Object.keys(params).forEach((key) => params[key] == null && delete params[key]);
  //   params = map(
  //     params,
  //     (value, name) => `${encodeURIComponent(name)}=${encodeURIComponent(value)}`
  //   ).join('&');

  //   if (params !== '') {
  //     url += `?${params}`;
  //   }

  //   if (hash) {
  //     url += `#${hash}`;
  //   }

  //   return url;
  // }

  getAutoLimit() {
    return this.options.apply_auto_limit;
  }

  // getQueryResultPromise() {
  //   return this.getQueryResult().toPromise();
  // }

  getParameters() {
    if (!this.$parameters) {
      this.$parameters = new Parameters(this, location.search);
    }

    return this.$parameters;
  }

  getParametersDefs(update = true) {
    return this.getParameters().get(update);
  }

  getStatus() {
    return this.query_status;
  }
}
class Parameters {
  query: any;
  cachedQueryText: any;
  constructor(query: any, queryString: undefined) {
    this.query = query;
    this.updateParameters();
    this.initFromQueryString(queryString);
  }

  parseQuery() {
    const fallback = () => map(this.query.options.parameters, i => i.name);

    let parameters = [];
    if (this.query.query !== undefined) {
      try {
        const parts = Mustache.parse(this.query.query);
        parameters = uniq(collectParams(parts));
      } catch (e: any) {
        message.error('Failed parsing parameters: ', e);
        // Return current parameters so we don't reset the list
        parameters = fallback();
      }
    } else {
      parameters = fallback();
    }
    return parameters;
  }

  updateParameters(update = false) {
    if (this.query.query === this.cachedQueryText) {
      const parameters = this.query.options.parameters;

      const hasUnprocessedParameters = find(parameters, p => !(p instanceof Parameter));
      if (hasUnprocessedParameters) {
        this.query.options.parameters = map(parameters, p =>
          p instanceof Parameter ? p : createParameter(p, this.query.id)
        );
      }
      return;
    }

    this.cachedQueryText = this.query.query;
    const parameterNames = update
      ? this.parseQuery()
      : map(this.query.options.parameters, p => p.name);
    this.query.options.parameters = this.query.options.parameters || [];
    const parametersMap: any = {};
    this.query.options.parameters.forEach((param: { name: string | number }) => {
      parametersMap[param.name] = param;
    });
    parameterNames.forEach(param => {
      if (!has(parametersMap, param)) {
        this.query.options.parameters.push(
          createParameter(
            {
              title: param,
              name: param,
              type: 'text',
              value: null,
              global: false,
            },
            null
          )
        );
      }
    });

    const parameterExists = (p: { name: any }) => includes(parameterNames, p.name);
    const parameters = this.query.options.parameters;

    this.query.options.parameters = parameters
      .filter(parameterExists)
      .map((p: any) => (p instanceof Parameter ? p : createParameter(p, this.query.id)));
  }

  initFromQueryString(query: undefined) {
    this.get().forEach((param: { fromUrlParams: (arg0: undefined) => void }) => {
      param.fromUrlParams(query);
    });
  }

  get(update = true) {
    this.updateParameters(update);

    return this.query.options.parameters;
  }

  add(parameterDef: { name: any }) {
    this.query.options.parameters = this.query.options.parameters.filter(
      (p: { name: any }) => p.name !== parameterDef.name
    );
    const param = createParameter(parameterDef, null);
    this.query.options.parameters.push(param);
    return param;
  }

  getMissing() {
    return map(
      filter(this.get(), p => p.isEmpty),
      i => i.title
    );
  }

  isRequired() {
    return !isEmpty(this.get());
  }

  getExecutionValues(extra = {}) {
    const params = this.get();
    return zipObject(
      map(params, i => i.name),
      map(params, i => i.getExecutionValue(extra))
    );
  }

  hasPendingValues() {
    return some(this.get(), p => p.hasPendingValue);
  }

  applyPendingValues() {
    each(this.get(), p => p.applyPendingValue());
  }

  // toUrlParams() {
  //   if (this.get().length === 0) {
  //     return '';
  //   }
  //   const params = Object.assign(
  //     ...this.get().map((p: { toUrlParams: () => any }) => p.toUrlParams())
  //   );
  //   Object.keys(params).forEach((key) => params[key] == null && delete params[key]);
  //   return Object.keys(params)
  //     .map((k) => `${encodeURIComponent(k)}=${encodeURIComponent(params[k])}`)
  //     .join('&');
  // }
}

export class QueryResultError {
  errorMessage: any;
  updatedAt: moment.Moment;
  constructor(errorMessage: any) {
    this.errorMessage = errorMessage;
    this.updatedAt = moment.utc();
  }

  getUpdatedAt() {
    return this.updatedAt;
  }

  getError() {
    return this.errorMessage;
  }

  toPromise() {
    return Promise.reject(this);
  }

  // eslint-disable-next-line class-methods-use-this
  getStatus() {
    return 'failed';
  }

  // eslint-disable-next-line class-methods-use-this
  getData() {
    return null;
  }

  // eslint-disable-next-line class-methods-use-this
  getLog() {
    return null;
  }
}
// function handleErrorResponse(queryResult: {}, error: any) {
//   const status = get(error, 'response.status');
//   switch (status) {
//     case 403:
//       alert(error);

//       return;
//     case 400:
//       if ('job' in error.response.data) {
//         alert(error);
//         return;
//       }
//       break;
//     case 404:
//       alert(error);

//       return;
//     // no default
//   }
// }
