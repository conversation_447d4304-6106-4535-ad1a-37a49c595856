/* eslint-disable @typescript-eslint/no-explicit-any */
import { findKey, has, includes, isNull, startsWith, values } from 'lodash-es';
import moment from 'moment';
import PropTypes from 'prop-types';

import { Parameter } from '@manyun/redash.entity.parameter';

const DATETIME_FORMATS = {
  date: 'YYYY-MM-DD',
  'datetime-local': 'YYYYMMDDHHmm',
  'datetime-with-seconds': 'YYYYMMDDHHmmss',
} as any;

const DYNAMIC_PREFIX = 'd_';

const DYNAMIC_DATES = {
  now: {
    name: '今天',
    value: () => moment(),
  },
  yesterday: {
    name: '昨天',
    value: () => moment().subtract(1, 'day'),
  },
} as any;

export const DynamicDateType = PropTypes.oneOf(values(DYNAMIC_DATES));

function isDynamicDateString(value: string) {
  return (
    startsWith(value, DYNAMIC_PREFIX) && has(DYNAMIC_DATES, value.substring(DYNAMIC_PREFIX.length))
  );
}

export function isDynamicDate(value: any) {
  return includes(DYNAMIC_DATES, value);
}

export function getDynamicDateFromString(value: string) {
  if (!isDynamicDateString(value)) {
    return null;
  }
  const index = value.substring(DYNAMIC_PREFIX.length);
  return DYNAMIC_DATES[index];
}

class DateParameter extends Parameter {
  useCurrentDateTime: any;
  value: string | undefined;
  type!: string;
  $$value: any;
  constructor(parameter: { useCurrentDateTime: any; value: any }, parentQueryId: any) {
    super(parameter as any, parentQueryId);
    this.useCurrentDateTime = parameter.useCurrentDateTime;
    this.setValue(parameter.value);
  }

  get hasDynamicValue() {
    return isDynamicDate(this.normalizedValue);
  }

  // eslint-disable-next-line class-methods-use-this
  normalizeValue(value: any) {
    if (isDynamicDateString(value)) {
      return getDynamicDateFromString(value);
    }

    if (isDynamicDate(value)) {
      return value;
    }

    const normalizedValue = moment(value);
    return normalizedValue.isValid() ? normalizedValue : null;
  }

  setValue(value: any) {
    const normalizedValue = this.normalizeValue(value);
    if (isDynamicDate(normalizedValue)) {
      this.value = DYNAMIC_PREFIX + findKey(DYNAMIC_DATES, normalizedValue);
    } else if (moment.isMoment(normalizedValue)) {
      this.value = normalizedValue.format(DATETIME_FORMATS[this.type]);
    } else {
      this.value = normalizedValue;
    }
    this.$$value = normalizedValue;

    this.updateLocals();
    this.clearPendingValue();
    return this;
  }

  getExecutionValue() {
    if (this.hasDynamicValue) {
      return this.normalizedValue.value().format(DATETIME_FORMATS[this.type]);
    }
    if (isNull(this.value) && this.useCurrentDateTime) {
      return moment().format(DATETIME_FORMATS[this.type]);
    }
    return this.value;
  }
}

export { DateParameter };
