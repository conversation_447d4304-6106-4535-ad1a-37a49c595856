/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  each,
  has,
  isArray,
  isEqual,
  isFunction,
  isNull,
  isObject,
  isUndefined,
  omit,
} from 'lodash-es';

class Parameter {
  title: string;
  name: string;
  type: string;
  parentQueryId: string;
  global: boolean | undefined;
  locals: never[];
  urlPrefix: string;
  pendingValue: undefined;
  $$value: any;
  value: any;
  constructor(
    parameter:
      | {
          name: string;
          title: string;
          global: boolean;
          value: string;
          // Used for meta-parameters (i.e. dashboard-level params)
          locals: never[];
          type: string;
          parentQueryId: number;
        }
      | {
          locals: never[];
          type: string;
          name: string;
          value: string;
          title: string;
          global?: undefined;
          parentQueryId?: undefined;
        }
      | {
          name: string;
          title: string;
          global: boolean;
          value: string;
          type: string;
          locals: never[];
          parentQueryId?: undefined;
        },
    parentQueryId: string
  ) {
    this.title = parameter.title;
    this.name = parameter.name;
    this.type = parameter.type;
    this.global = parameter.global; // backward compatibility in Widget service
    this.parentQueryId = parentQueryId;

    // Used for meta-parameters (i.e. dashboard-level params)
    this.locals = [] as any;

    // Used for URL serialization
    this.urlPrefix = 'p_';
  }

  static getExecutionValue(
    param: { getExecutionValue: (arg0: any) => any },
    extra = {}
  ) {
    if (!isObject(param) || !isFunction(param.getExecutionValue)) {
      return null;
    }

    return param.getExecutionValue(extra);
  }

  static setValue(param: { setValue: (arg0: any) => any }, value: any) {
    if (!isObject(param) || !isFunction(param.setValue)) {
      return null;
    }

    return param.setValue(value);
  }

  get isEmpty() {
    return isNull(this.normalizedValue);
  }

  get hasPendingValue() {
    return (
      this.pendingValue !== undefined &&
      !isEqual(this.pendingValue, this.normalizedValue)
    );
  }

  /** Get normalized value to be used in inputs */
  get normalizedValue() {
    return this.$$value;
  }

  isEmptyValue(value: any) {
    return isNull(this.normalizeValue(value));
  }

  // eslint-disable-next-line class-methods-use-this
  normalizeValue(value: any) {
    if (isUndefined(value)) {
      return null;
    }
    return value;
  }

  updateLocals() {
    if (isArray(this.locals)) {
      each(this.locals, (local: any) => {
        local.setValue(this.value);
      });
    }
  }

  setValue(value: undefined) {
    const normalizedValue = this.normalizeValue(value);
    this.value = normalizedValue;
    this.$$value = normalizedValue;

    this.updateLocals();
    this.clearPendingValue();
    return this;
  }

  /** Get execution value for a query */
  getExecutionValue() {
    return this.value;
  }

  setPendingValue(value: any) {
    this.pendingValue = this.normalizeValue(value);
  }

  applyPendingValue() {
    if (this.hasPendingValue) {
      this.setValue(this.pendingValue);
    }
  }

  clearPendingValue() {
    this.pendingValue = undefined;
  }

  /** Update URL with Parameter value */
  toUrlParams() {
    const prefix = this.urlPrefix;
    // `null` removes the parameter from the URL in case it exists
    return {
      [`${prefix}${this.name}`]: !this.isEmpty ? this.value : null,
    };
  }

  /** Set parameter value from the URL */
  fromUrlParams(query: { [x: string]: any }) {
    const prefix = this.urlPrefix;
    const key = `${prefix}${this.name}`;
    if (has(query, key)) {
      this.setValue(query[key]);
    }
  }

  toQueryTextFragment() {
    return `{{ ${this.name} }}`;
  }

  /** Get a saveable version of the Parameter by omitting unnecessary props */
  toSaveableObject(): Omit<
    Parameter,
    '$$value' | 'urlPrefix' | 'pendingValue' | 'parentQueryId'
  > {
    return omit(this, [
      '$$value',
      'urlPrefix',
      'pendingValue',
      'parentQueryId',
    ]);
  }
}

export { Parameter };
