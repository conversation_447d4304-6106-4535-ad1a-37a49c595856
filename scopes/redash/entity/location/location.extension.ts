/* eslint-disable @typescript-eslint/no-explicit-any */
// @ts-ignore types
import { createBrowserHistory } from 'history';
import {
  extend,
  isFunction,
  isNil,
  isObject,
  isUndefined,
  mapValues,
  omitBy,
  trimStart,
} from 'lodash-es';
import qs from 'query-string';

const history = createBrowserHistory();

function normalizeLocation(rawLocation: any) {
  const { pathname, search, hash }: any = rawLocation;
  const result = {} as any;

  result.path = pathname;
  result.search = mapValues(qs.parse(search), value => (isNil(value) ? true : value));
  result.hash = trimStart(hash, '#');
  result.url = `${pathname}${search}${hash}`;

  return result;
}

const location = {
  listen(
    handler: (
      arg0: {
        listen(handler: any): () => void;
        confirmChange(handler: any): () => void;
        update(newLocation: any, replace?: boolean): void;
        url: undefined;
        path: undefined;
        setPath(path: any, replace?: boolean): void;
        search: undefined;
        setSearch(search: any, replace?: boolean): void;
        hash: undefined;
        setHash(hash: any, replace?: boolean): void;
      },
      arg1: any
    ) => any
  ) {
    if (isFunction(handler)) {
      return history.listen(((unused: any, action: any) => handler(location, action)) as any);
    } else {
      return () => {};
    }
  },

  confirmChange(
    handler: (
      arg0: any,
      arg1: {
        listen(
          handler: (
            arg0: {
              listen(handler: any): () => void;
              confirmChange(handler: any): () => void;
              update(newLocation: any, replace?: boolean | undefined): void;
              url: undefined;
              path: undefined;
              setPath(path: any, replace?: boolean | undefined): void;
              search: undefined;
              setSearch(search: any, replace?: boolean | undefined): void;
              hash: undefined;
              setHash(hash: any, replace?: boolean | undefined): void;
            },
            arg1: any
          ) => any
        ): () => void;
        confirmChange(handler: any): () => void;
        update(newLocation: any, replace?: boolean): void;
        url: undefined;
        path: undefined;
        setPath(path: any, replace?: boolean): void;
        search: undefined;
        setSearch(search: any, replace?: boolean): void;
        hash: undefined;
        setHash(hash: any, replace?: boolean): void;
      }
    ) => void
  ) {
    if (isFunction(handler)) {
      // @ts-ignore types
      return history.block(nextLocation => {
        return handler(normalizeLocation(nextLocation), location);
      });
    } else {
      return () => {};
    }
  },

  update(newLocation: string | Record<string, any>, replace = false) {
    if (isObject(newLocation)) {
      // remap fields and remove undefined ones
      newLocation = omitBy(
        {
          pathname: newLocation.path,
          search: newLocation.search,
          hash: newLocation.hash,
        },
        isUndefined
      );

      // keep existing fields (!)
      newLocation = extend(
        {
          pathname: location.path,
          search: location.search,
          hash: location.hash,
        },
        newLocation
      );

      // serialize search and keep existing search parameters (!)
      if (isObject(newLocation.search)) {
        newLocation.search = omitBy(extend({}, location.search, newLocation.search), isNil);
        newLocation.search = mapValues(newLocation.search, value =>
          value === true ? null : value
        );
        newLocation.search = qs.stringify(newLocation.search);
      }
    }
    if (replace) {
      history.replace(newLocation);
    } else {
      history.push(newLocation);
    }
  },

  url: undefined,

  path: undefined,
  setPath(path: any, replace = false) {
    location.update({ path }, replace);
  },

  search: undefined,
  setSearch(search: any, replace = false) {
    location.update({ search }, replace);
  },

  hash: undefined,
  setHash(hash: any, replace = false) {
    location.update({ hash }, replace);
  },
};

function locationChanged() {
  extend(location, normalizeLocation(history.location));
}

history.listen(locationChanged);
locationChanged(); // init service

export { location };
