/* eslint-disable @typescript-eslint/no-explicit-any */
import { chain } from 'lodash';
import { each, extend, map, sortBy, values } from 'lodash-es';

import { Widget } from '@manyun/redash.entity.widget';
import { cloneParameter } from '@manyun/redash.util.parameter-function';
import { fetchDashboardWeb as fetchDashboard } from '@manyun/service.dcom.fetch-dashboard';

function prepareWidgetsForDashboard(widgets: any[]) {
  // Default height for auto-height widgets.
  // Compute biggest widget size and choose between it and some magic number.
  // This value should be big enough so auto-height widgets will not overlap other ones.
  const defaultWidgetSizeY =
    Math.max(
      chain(widgets)
        .map(w => w.options.position.sizeY)
        .max()
        .value(),
      20
    ) + 5;

  // Fix layout:
  // 1. sort and group widgets by row
  // 2. update position of widgets in each row - place it right below
  //    biggest widget from previous row
  chain(widgets)
    .sortBy(widget => widget.options.position.row)
    .groupBy(widget => widget.options.position.row)
    .reduce((row, widgetsAtRow) => {
      let height = 1;
      each(widgetsAtRow, widget => {
        height = Math.max(
          height,
          widget.options.position.autoHeight ? defaultWidgetSizeY : widget.options.position.sizeY
        );
        widget.options.position.row = row;
        if (widget.options.position.sizeY < 1) {
          widget.options.position.sizeY = defaultWidgetSizeY;
        }
      });
      return row + height;
    }, 0)
    .value();

  // Sort widgets by updated column and row value
  widgets = sortBy(widgets, widget => widget.options.position.col);
  widgets = sortBy(widgets, widget => widget.options.position.row);

  return widgets;
}

export function Dashboard(this: any, dashboard: any) {
  extend(this, dashboard);
  // Object.defineProperty(this, 'url', {
  //   get: function () {},
  // });
}

function prepareDashboardWidgets(widgets: any) {
  return prepareWidgetsForDashboard(map(widgets, widget => new Widget(widget)));
}

function transformSingle(dashboard: any) {
  dashboard = new (Dashboard as any)(dashboard);
  if (dashboard.widgets) {
    dashboard.widgets = prepareDashboardWidgets(dashboard.widgets);
  }
  dashboard.publicAccessEnabled = dashboard.public_url !== undefined;
  return dashboard;
}

function transformResponse(data: any) {
  if (data.results) {
    data = { ...data, results: map(data.results, transformSingle) };
  } else {
    data = transformSingle(data);
  }
  return data;
}

const DashboardService = {
  get: async ({ id, slug }: { id: string; slug: string }) => {
    const { data } = await fetchDashboard({ id });
    if (data) {
      return transformResponse(data);
    }
    return null;
  },
};

extend(Dashboard, DashboardService);

Dashboard.prepareDashboardWidgets = prepareDashboardWidgets;
Dashboard.prepareWidgetsForDashboard = prepareWidgetsForDashboard;

Dashboard.prototype.canEdit = function canEdit() {
  // 先不考虑编辑，所以 canedit 都是 false
  // return policy.canEdit(this);
  return false;
};

// 筛选参数先不管;
Dashboard.prototype.getParametersDefs = function getParametersDefs() {
  const globalParams = {} as any;
  const queryParams = location.search;
  each(this.widgets, widget => {
    if (widget.getQuery()) {
      const mappings = widget.getParameterMappings();
      widget
        .getQuery()
        .getParametersDefs(false)
        .forEach((param: { name: string; type: string; mapTo: string; value: null; title: '' }) => {
          const mapping: { name: string; type: string; mapTo: string; value: null; title: '' } =
            mappings[param.name];
          if (mapping.type === Widget.MappingType.DashboardLevel) {
            // create global param
            if (!globalParams[mapping.mapTo]) {
              globalParams[mapping.mapTo] = cloneParameter(param);
              globalParams[mapping.mapTo].name = mapping.mapTo;
              globalParams[mapping.mapTo].title = mapping.title || param.title;
              globalParams[mapping.mapTo].locals = [];
            }

            // add to locals list
            globalParams[mapping.mapTo].locals.push(param);
          }
        });
    }
  });
  const resultingGlobalParams = values(
    each(globalParams, param => {
      param.setValue(param.value); // apply global param value to all locals
      param.fromUrlParams(queryParams); // try to initialize from url (may do nothing)
    })
  );

  // order dashboard params using paramOrder
  // return sortBy(resultingGlobalParams, (param) =>
  //   includes(this.options.globalParamOrder, param.name)
  //     ? indexOf(this.options.globalParamOrder, param.name)
  //     : size(this.options.globalParamOrder)
  // );
  return resultingGlobalParams;
};

// 新增也先不管
// Dashboard.prototype.addWidget = function addWidget(textOrVisualization, options = {}) {
//   const props = {
//     dashboard_id: this.id,
//     options: {
//       ...options,
//       isHidden: false,
//       position: {},
//     },
//     text: '',
//     visualization_id: null,
//     visualization: null,
//   };

//   if (isString(textOrVisualization)) {
//     props.text = textOrVisualization;
//   } else if (isObject(textOrVisualization)) {
//     props.visualization_id = textOrVisualization.id;
//     props.visualization = textOrVisualization;
//   } else {
//     // TODO: Throw an error?
//   }

//   const widget = new Widget(props);

//   const position = calculateNewWidgetPosition(this.widgets, widget);
//   widget.options.position.col = position.col;
//   widget.options.position.row = position.row;

//   return widget.save().then(() => {
//     this.widgets = [...this.widgets, widget];
//     return widget;
//   });
// };
