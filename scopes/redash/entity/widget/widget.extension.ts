/* eslint-disable @typescript-eslint/no-explicit-any */
// @ts-nocheck pending refactor
import {
  difference,
  each,
  extend,
  filter,
  includes,
  indexOf,
  isObject,
  keys,
  map,
  pick,
  size,
  sortBy,
  truncate,
} from 'lodash-es';
import moment from 'moment';

import { location } from '@manyun/redash.entity.location';
import { Query } from '@manyun/redash.entity.query';
import { registeredVisualizations } from '@manyun/redash.ui.viz-lib';
import { cloneParameter } from '@manyun/redash.util.parameter-function';

export const WidgetTypeEnum = {
  TEXTBOX: 'textbox',
  VISUALIZATION: 'visualization',
  RESTRICTED: 'restricted',
};
const dashboardGridOptions = {
  columns: 6, // grid columns count
  rowHeight: 50, // grid row height (incl. bottom padding)
  margins: 15, // widget margins
  mobileBreakPoint: 800,
  // defaults for widgets
  defaultSizeX: 3,
  defaultSizeY: 3,
  minSizeX: 1,
  maxSizeX: 6,
  minSizeY: 1,
  maxSizeY: 1000,
};

function calculatePositionOptions(widget: any) {
  widget.width = 1; // Backward compatibility, user on back-end

  const visualizationOptions = {
    autoHeight: false,
    sizeX: Math.round(dashboardGridOptions.columns / 2),
    sizeY: dashboardGridOptions.defaultSizeY,
    minSizeX: dashboardGridOptions.minSizeX,
    maxSizeX: dashboardGridOptions.maxSizeX,
    minSizeY: dashboardGridOptions.minSizeY,
    maxSizeY: dashboardGridOptions.maxSizeY,
  };

  const config = widget.visualization ? registeredVisualizations[widget.visualization.type] : null;
  if (isObject(config)) {
    if (Object.prototype.hasOwnProperty.call(config, 'autoHeight')) {
      visualizationOptions.autoHeight = (config as any).autoHeight;
    }

    // Width constraints
    const minColumns = parseInt((config as any).minColumns, 10);
    if (isFinite(minColumns) && minColumns >= 0) {
      visualizationOptions.minSizeX = minColumns;
    }
    const maxColumns = parseInt((config as any).maxColumns, 10);
    if (isFinite(maxColumns) && maxColumns >= 0) {
      visualizationOptions.maxSizeX = Math.min(maxColumns, dashboardGridOptions.columns);
    }

    // Height constraints
    // `minRows` is preferred, but it should be kept for backward compatibility
    const height = parseInt((config as any).height, 10);
    if (isFinite(height)) {
      visualizationOptions.minSizeY = Math.ceil(height / dashboardGridOptions.rowHeight);
    }
    const minRows = parseInt((config as any).minRows, 10);
    if (isFinite(minRows)) {
      visualizationOptions.minSizeY = minRows;
    }
    const maxRows = parseInt((config as any).maxRows, 10);
    if (isFinite(maxRows) && maxRows >= 0) {
      visualizationOptions.maxSizeY = maxRows;
    }

    // Default dimensions
    const defaultWidth = parseInt((config as any).defaultColumns, 10);
    if (isFinite(defaultWidth) && defaultWidth > 0) {
      visualizationOptions.sizeX = defaultWidth;
    }
    const defaultHeight = parseInt((config as any).defaultRows, 10);
    if (isFinite(defaultHeight) && defaultHeight > 0) {
      visualizationOptions.sizeY = defaultHeight;
    }
  }

  return visualizationOptions;
}

export const ParameterMappingType = {
  DashboardLevel: 'dashboard-level',
  WidgetLevel: 'widget-level',
  StaticValue: 'static-value',
};

class Widget {
  static MappingType = ParameterMappingType;
  options?: any;
  visualization?: any;
  restricted?: any;
  query?: any;
  data?: any;
  text?: string | undefined;
  queryResult?: any | undefined;
  loading?: boolean | undefined;
  refreshStartedAt?: moment.Moment | undefined;
  id: any;

  constructor(data: any) {
    // Copy properties
    extend(this, data);

    const visualizationOptions = calculatePositionOptions(this);

    this.options = this.options || {};
    this.options.position = extend(
      {},
      visualizationOptions,
      pick(this.options.position, ['col', 'row', 'sizeX', 'sizeY', 'autoHeight'])
    );

    if (this.options.position.sizeY < 0) {
      this.options.position.autoHeight = true;
    }
  }

  get type() {
    if (this.visualization) {
      return WidgetTypeEnum.VISUALIZATION;
    } else if (this.restricted) {
      return WidgetTypeEnum.RESTRICTED;
    }
    return WidgetTypeEnum.TEXTBOX;
  }

  getQuery() {
    if (!this.query && this.visualization) {
      this.query = new Query(this.visualization.query);
    }
    return this.query;
  }

  getQueryResult() {
    return this.data;
  }

  getColumnTitleKeyMapper() {
    if (this.visualization) {
      const columnTitleKeyMapper: Record<string, string> = {};
      this.visualization?.options?.columns?.forEach((column: { title: string; name: string }) => {
        columnTitleKeyMapper[column.name] = column.title;
      });
      return columnTitleKeyMapper;
    } else {
      return {};
    }
  }
  getName() {
    if (this.visualization) {
      return `${this.visualization.query.name} (${this.visualization.name})`;
    }
    return truncate(this.text, { length: 20 });
  }
  getFileName(queryName: string, fileType: string) {
    return `${queryName.replace(/ /g, '_')}.${fileType}`;
  }
  async load(force: boolean, maxAge: number) {
    if (!this.visualization) {
      return Promise.resolve();
    }

    // Both `this.data` and `this.queryResult` are query result objects;
    // `this.data` is last loaded query result;
    // `this.queryResult` is currently loading query result;
    // while widget is refreshing, `this.data` !== `this.queryResult`
    if (force || this.queryResult === undefined) {
      this.loading = true;
      this.refreshStartedAt = moment();
      if (maxAge === undefined || force) {
        maxAge = force ? 0 : undefined;
      }
      const queryResult = this.getQuery().getQueryResult(maxAge);
      this.queryResult = await queryResult;
      this.data = await queryResult;
    }
    if (force && this.queryResult) {
      this.loading = true;
      this.refreshStartedAt = moment();
      if (maxAge === undefined || force) {
        maxAge = force ? 0 : undefined;
      }
      const queryResult = this.getQuery().getQueryResult(maxAge);
      this.queryResult = await queryResult;
      this.data = await queryResult;

      if (this.data.query_result?.retrieved_at) {
        this.data.query_result.retrieved_at = moment(undefined).format('YYYY-MM-DD HH:mm:ss');
      }
    }
    return this.queryResult;
  }

  isStaticParam(param: any) {
    const mappings = this.getParameterMappings();
    const mappingType = mappings[param.name].type;
    return mappingType === Widget.MappingType.StaticValue;
  }

  getParametersDefs(): any {
    const mappings = this.getParameterMappings();

    // textboxes does not have query
    const params = this.getQuery() ? this.getQuery().getParametersDefs() : [];

    // location.search
    const queryParams = location.search;

    const localTypes = [Widget.MappingType.WidgetLevel, Widget.MappingType.StaticValue];

    const localParameters = map(
      filter(params, param => localTypes.indexOf(mappings[param.name].type) >= 0),
      param => {
        const mapping = mappings[param.name];
        const result = cloneParameter(param);
        result.title = mapping.title || param.title;
        result.locals = [param] as any;
        result.urlPrefix = `p_w${this.id}_`;
        if (mapping.type === Widget.MappingType.StaticValue) {
          result.setValue(mapping.value);
        } else {
          if (queryParams) {
            result.fromUrlParams(queryParams);
          }
        }

        return result;
      }
    );

    // order widget params using paramOrder
    return sortBy(localParameters, param =>
      includes(this.options.paramOrder, param.name)
        ? indexOf(this.options.paramOrder, param.name)
        : size(this.options.paramOrder)
    );
  }

  getParameterMappings(): any {
    if (!isObject(this.options.parameterMappings)) {
      this.options.parameterMappings = {};
    }

    const existingParams: any = {};
    // textboxes does not have query
    const params = this.getQuery() ? this.getQuery().getParametersDefs(false) : [];
    each(params, param => {
      existingParams[param.name] = true;
      if (!isObject(this.options.parameterMappings[param.name])) {
        // "migration" for old dashboards: parameters with `global` flag
        // should be mapped to a dashboard-level parameter with the same name
        this.options.parameterMappings[param.name] = {
          name: param.name,
          type: param.global ? Widget.MappingType.DashboardLevel : Widget.MappingType.WidgetLevel,
          mapTo: param.name, // map to param with the same name
          value: null, // for StaticValue
          title: '', // Use parameter's title
        };
      }
    });

    // Remove mappings for parameters that do not exists anymore
    const removedParams = difference(keys(this.options.parameterMappings), keys(existingParams));
    each(removedParams, name => {
      delete this.options.parameterMappings[name];
    });

    return this.options.parameterMappings;
  }

  getLocalParameters(): any {
    return filter(this.getParametersDefs(), param => !this.isStaticParam(param));
  }
}

export { Widget };
