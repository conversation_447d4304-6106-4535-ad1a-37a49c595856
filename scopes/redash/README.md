# @manyun/redash

报表中心

## Development

### Installation

```bash
$ bit install && bit compile
```

### Run app/redash

#### 安装浏览器扩展 `Redirector`

添加 `/app/redash/*` 请求的重定向配置

举个例子：下图为将测试环境的 `/app/redash/*` 请求重定向到本地的配置

![Alt text](assets/images/redirect-redash-requests.png)

> 前提是本地已经运行了 `MF Redash APP`

启用上图配置后，打开 `http://default.manyun-local.com` 查看报表中心下的页面即可

#### 运行 `MF Redash APP`

```bash
$ bit run redash
```
