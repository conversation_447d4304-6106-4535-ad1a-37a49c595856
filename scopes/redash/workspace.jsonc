{"$schema": "https://static.bit.dev/teambit/schemas/schema.json", "teambit.workspace/workspace": {"name": "@manyun/redash", "icon": "https://static.bit.dev/bit-logo.svg", "defaultDirectory": "{name}", "defaultScope": "manyun.redash", "resolveAspectsFromNodeModules": true, "resolveEnvsFromRoots": true}, "teambit.harmony/bit": {"engine": "^1.8.52", "engineStrict": true}, "teambit.dependencies/dependency-resolver": {"packageManager": "teambit.dependencies/pnpm", "policy": {"dependencies": {"@glpdev/react.app-types.mf-rspack": "0.0.1-alpha.2", "@glpdev/react.module-federation.external-template-remotes-plugin": "0.0.1-alpha.0", "@manyun/base-ui.chart.echarts": "^1.1.0", "@manyun/base-ui.icons": "^4.3.0", "@manyun/base-ui.ui.badge": "^4.3.0", "@manyun/base-ui.ui.button": "^4.3.0", "@manyun/base-ui.ui.card": "^4.3.0", "@manyun/base-ui.ui.checkbox": "^4.3.0", "@manyun/base-ui.ui.collapse": "^4.3.0", "@manyun/base-ui.ui.date-picker": "^4.3.0", "@manyun/base-ui.ui.descriptions": "^4.3.0", "@manyun/base-ui.ui.dropdown": "^4.3.0", "@manyun/base-ui.ui.empty": "^4.3.0", "@manyun/base-ui.ui.file-export": "^4.3.0", "@manyun/base-ui.ui.form": "^4.3.0", "@manyun/base-ui.ui.input": "^4.3.0", "@manyun/base-ui.ui.input-number": "^4.3.0", "@manyun/base-ui.ui.menu": "^4.3.0", "@manyun/base-ui.ui.message": "^4.3.0", "@manyun/base-ui.ui.modal": "^4.3.0", "@manyun/base-ui.ui.radio": "^4.3.0", "@manyun/base-ui.ui.result": "^4.3.0", "@manyun/base-ui.ui.select": "^4.3.0", "@manyun/base-ui.ui.space": "^4.3.0", "@manyun/base-ui.ui.spin": "^4.3.0", "@manyun/base-ui.ui.switch": "^4.3.0", "@manyun/base-ui.ui.table": "^4.3.0", "@manyun/base-ui.ui.tabs": "^4.3.0", "@manyun/base-ui.ui.tooltip": "^4.3.0", "@manyun/base-ui.util.xlsx": "^1.1.0", "@manyun/dc-brain.model.mc-upload-file": "^1.0.2", "@manyun/dcbrain-types": "0.1.1", "@manyun/iam.hook.use-authorized": "^2.1.2", "@manyun/iam.ui.user-select": "^4.0.3", "@manyun/redash.ui.viz-lib": "^0.0.2", "@manyun/resource-hub.model.metadata": "^1.0.7", "@manyun/service.dcom.fetch-dashboard": "0.0.13", "@manyun/service.dcom.fetch-query-async": "0.0.12", "@redash/viz": "0.1.1", "@teammc/proxy-setup": "^2.1.6", "@types/chroma-js": "2.1.3", "@types/leaflet": "1.7.5", "@types/lodash": "4.14.175", "@types/lodash-es": "^4.17.12", "@types/mockjs": "1.0.4", "@types/mustache": "4.1.2", "@types/prop-types": "15.7.4", "@types/react-grid-layout": "1.1.3", "axios": "0.23.0", "babel-loader": "^9.1.3", "beautifymarker": "^1.0.7", "chroma-js": "^1.3.6", "classnames": "2.3.1", "d3": "^3.5.17", "d3-cloud": "^1.2.4", "dayjs": "1.11.6", "debug": "^3.1.0", "dompurify": "^2.0.7", "events": "3.3.0", "font-awesome": "^4.7.0", "history": "^5.3.0", "hoist-non-react-statics": "^3.3.0", "html2canvas": "1.4.1", "jspdf": "2.5.1", "leaflet": "^1.2.0", "leaflet-fullscreen": "^1.0.2", "leaflet.markercluster": "^1.1.0", "lodash": "4.17.21", "lodash-es": "^4.17.21", "markdown": "0.5.0", "mockjs": "1.1.0", "mustache": "4.2.0", "numeral": "^2.0.6", "plotly.js": "1.52.3", "react-grid-layout": "1.3.0", "react-pivottable": "^0.9.0", "react-sortable-hoc": "^1.10.1", "tinycolor2": "^1.4.1", "use-debounce": "^3.4.1", "use-media": "^1.4.0"}, "peerDependencies": {"@ant-design/icons": "^4.8.1", "@glpdev/symphony.services.request": "^1.0.1", "@manyun/base-ui.context.config": "^4.3.0", "@manyun/base-ui.theme.theme": "^4.3.0", "antd": "4.24.15", "echarts": "5.3.0", "echarts-for-react": "3.0.2", "moment": "2.29.3", "query-string": "7.0.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router": "^6.24.1", "react-router-dom": "^6.16.0", "typescript": "5.3.3"}}, "linkCoreAspects": true, "rootComponents": true, "devFilePatterns": ["**/*.spec.ts", "**/*.composition-wrapper.tsx", "**/*.snap"]}, "teambit.workspace/variants": {"*": {"teambit.pkg/pkg": {"packageJson": {"name": "@{owner}/{scope}.{name}", "private": false, "publishConfig": {"scope": "@manyun", "registry": "https://packages.aliyun.com/5f8d28b593de78251872349a/npm/npm-registry/"}}, "packageManagerPublishArgs": ["--access public"]}}}, "teambit.component/issues": {"ignoreIssues": ["ImportNonMainFiles"]}, "teambit.workspace/workspace-config-files": {"enableWorkspaceConfigWrite": true}, "teambit.generator/generator": {"envs": []}, "manyun.redash/app/redash": {}}