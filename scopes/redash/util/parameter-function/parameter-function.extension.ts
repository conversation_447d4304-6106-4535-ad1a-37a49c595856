/* eslint-disable @typescript-eslint/no-explicit-any */
// import EnumParameter from "./EnumParameter";
// import QueryBasedDropdownParameter from "./QueryBasedDropdownParameter";
import DateParameter from '@manyun/redash.entity.date-parameter';
import DateRangeParameter from '@manyun/redash.entity.date-range-parameter';
import NumberParameter from '@manyun/redash.entity.number-parameter';
import Parameter from '@manyun/redash.entity.parameter';
import TextParameter from '@manyun/redash.entity.text-parameter';

// import DateRangeParameter from "./DateRangeParameter";

function createParameter(param: any, parentQueryId: any) {
  switch (param.type) {
    case 'number':
      return new NumberParameter(param, parentQueryId);
    // case "enum":
    //   return new EnumParameter(param, parentQueryId);
    // case "query":
    //   return new QueryBasedDropdownParameter(param, parentQueryId);
    case 'date':
    case 'datetime-local':
    case 'datetime-with-seconds':
      return new DateParameter(param, parentQueryId);
    case 'date-range':
    case 'datetime-range':
    case 'datetime-range-with-seconds':
      return new DateRangeParameter(param, parentQueryId);
    default:
      return new TextParameter({ ...param, type: 'text' }, parentQueryId);
  }
}

function cloneParameter(param: any) {
  return createParameter(param, param.parentQueryId);
}

export { Parameter, TextParameter, NumberParameter, createParameter, cloneParameter };
