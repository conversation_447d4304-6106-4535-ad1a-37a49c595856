import Mock from 'mockjs';

export default Mock.mock('/api/queries', 'post', {
  status: 200,
  message: '请求query_result数据成功！',
  total: 20,

  query_result: {
    retrieved_at: '2021-10-11T16:13:16.042+08:00',
    query_hash: 'f4a808b80753316be374a36f1f9b8874',
    query:
      "select\n  toDate(time),device_guid, avg(data_value) as avg_kw,point_code from ck.point_data\nwhere time >= '2021-09-13 00:00:00' and time <= '2021-09-15 23:59:59'\nand device_guid = 'EC01.A' and point_code = '1075000'\ngroup by device_guid,toDate(time),point_code",
    runtime: 0.18723583221435547,
    data: {
      rows: [
        {
          point_code: '1075000',
          avg_kw: 7741.************,
          'toDate(time)': '2021-09-13',
          device_guid: 'EC01.A',
        },
        {
          point_code: '1075000',
          avg_kw: 0,
          'toDate(time)': '2021-09-15',
          device_guid: 'EC01.A',
        },
        {
          point_code: '1075000',
          avg_kw: 7810.************,
          'toDate(time)': '2021-09-14',
          device_guid: 'EC01.A',
        },
      ],
      columns: [
        {
          friendly_name: 'toDate(time)',
          type: 'date',
          name: 'toDate(time)',
        },
        {
          friendly_name: 'device_guid',
          type: 'string',
          name: 'device_guid',
        },
        {
          friendly_name: 'avg_kw',
          type: 'float',
          name: 'avg_kw',
        },
        {
          friendly_name: 'point_code',
          type: 'string',
          name: 'point_code',
        },
      ],
    },
    id: 237083,
    data_source_id: 7,
  },
});
