/* eslint-disable @typescript-eslint/no-explicit-any */
import { every, isArray, some } from 'lodash-es';
import moment from 'moment';

export function filterData(rows: any, filters: any[] = []) {
  if (!isArray(rows)) {
    return [];
  }

  let result = rows;

  if (isArray(filters) && filters.length > 0) {
    // "every" field's value should match "some" of corresponding filter's values
    result = result.filter(row =>
      every(filters, filter => {
        const rowValue = row[filter.name];
        const filterValues = isArray(filter.current) ? filter.current : [filter.current];
        return some(filterValues, filterValue => {
          if (moment.isMoment(rowValue)) {
            return rowValue.isSame(filterValue);
          }
          // We compare with either the value or the String representation of the value,
          // because Select2 casts true/false to "true"/"false".
          return filterValue === rowValue || String(rowValue) === filterValue;
        });
      })
    );
  }

  return result;
}
