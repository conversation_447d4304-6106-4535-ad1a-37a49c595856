export const IntervalEnum = {
  NEVER: '从不',
  SECONDS: '秒',
  MINUTES: '分钟',
  HOURS: '小时',
  DAYS: '天',
  WEEKS: '周',
  MILLISECONDS: '微秒',
};
export function secondsToInterval(count: number) {
  if (!count) {
    return { interval: IntervalEnum.NEVER };
  }

  let interval = IntervalEnum.SECONDS;
  if (count >= 60) {
    count /= 60;
    interval = IntervalEnum.MINUTES;
  }
  if (count >= 60) {
    count /= 60;
    interval = IntervalEnum.HOURS;
  }
  if (count >= 24 && interval === IntervalEnum.HOURS) {
    count /= 24;
    interval = IntervalEnum.DAYS;
  }
  if (count >= 7 && !(count % 7) && interval === IntervalEnum.DAYS) {
    count /= 7;
    interval = IntervalEnum.WEEKS;
  }
  return { count, interval };
}
