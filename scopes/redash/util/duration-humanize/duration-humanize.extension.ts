// @ts-nocheck pending refactor
import pluralize from '@manyun/redash.util.pluralize';
import secondsToInterval from '@manyun/redash.util.seconds-to-interval';

export function durationHumanize(durationInSeconds, options = {}) {
  if (!durationInSeconds) {
    return '-';
  }
  let ret = '';
  const { interval, count } = secondsToInterval(durationInSeconds);
  const rounded = Math.round(count);
  if (rounded !== 1 || !options.omitSingleValueNumber) {
    ret = `${rounded} `;
  }
  ret += pluralize(interval, rounded);
  return ret;
}
