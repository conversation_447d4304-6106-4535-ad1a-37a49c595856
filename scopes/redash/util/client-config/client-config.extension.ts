const clientConfig = {
  allowScriptsInUserInput: false,
  googleLoginEnabled: false,
  timeFormatList: ['HH:mm:ss', 'HH:mm', 'HH:mm:ss.SSS'],
  dateFormatList: ['DD/MM/YY', 'YYYY-MM-DD', 'MM/DD/YY'],
  pageSize: 20,
  showPermissionsControl: false,
  dateFormat: 'YYYY-MM-DD',
  extendedAlertOptions: false,
  mailSettingsMissing: true,
  basePath: 'http://redash.manyun-local.com/',
  floatFormat: '0,0.00',
  autoPublishNamedQueries: true,
  queryRefreshIntervals: [
    60, 300, 600, 900, 1800, 3600, 7200, 10800, 14400, 18000, 21600, 25200, 28800, 32400, 36000,
    39600, 43200, 86400, 604800, 1209600, 2592000,
  ],
  version: '8.0.0',
  dashboardRefreshIntervals: [60, 300, 600, 1800, 3600, 43200, 86400],
  integerFormat: '0,0',
  dateTimeFormat: 'DD/MM/YY HH:mm',
  newVersionAvailable: false,
  allowCustomJSVisualizations: false,
  pageSizeOptions: [5, 10, 20, 50, 100],
  tableCellMaxJSONSize: 50000,
};

export { clientConfig };
