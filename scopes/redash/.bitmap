/* THIS IS A BIT-AUTO-GENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY. */

/**
 * The Bitmap file is an auto generated file used by Bit to track all your Bit components. It maps the component to a folder in your file system.
 * This file should be committed to VCS(version control).
 * Components are listed using their component ID (https://bit.dev/reference/components/component-id).
 * If you want to delete components you can use the "bit remove <component-id>" command.
 * See the docs (https://bit.dev/reference/components/removing-components) for more information, or use "bit remove --help".
 */

{
    "app/redash": {
        "name": "app/redash",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "app/redash",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "entity/dashboard": {
        "name": "entity/dashboard",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "entity/dashboard",
        "config": {
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-esm-env"
            },
            "teammc.snowcone/node-esm-env@2.0.18": {}
        }
    },
    "entity/date-parameter": {
        "name": "entity/date-parameter",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "entity/date-parameter",
        "config": {
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-esm-env"
            },
            "teammc.snowcone/node-esm-env@2.0.18": {}
        }
    },
    "entity/date-range-parameter": {
        "name": "entity/date-range-parameter",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "entity/date-range-parameter",
        "config": {
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-esm-env"
            },
            "teammc.snowcone/node-esm-env@2.0.18": {}
        }
    },
    "entity/location": {
        "name": "entity/location",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "entity/location",
        "config": {
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-esm-env"
            },
            "teammc.snowcone/node-esm-env@2.0.18": {}
        }
    },
    "entity/number-parameter": {
        "name": "entity/number-parameter",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "entity/number-parameter",
        "config": {
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-esm-env"
            },
            "teammc.snowcone/node-esm-env@2.0.18": {}
        }
    },
    "entity/parameter": {
        "name": "entity/parameter",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "entity/parameter",
        "config": {
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-esm-env"
            },
            "teammc.snowcone/node-esm-env@2.0.18": {}
        }
    },
    "entity/query": {
        "name": "entity/query",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "entity/query",
        "config": {
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-esm-env"
            },
            "teammc.snowcone/node-esm-env@2.0.18": {}
        }
    },
    "entity/text-parameter": {
        "name": "entity/text-parameter",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "entity/text-parameter",
        "config": {
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-esm-env"
            },
            "teammc.snowcone/node-esm-env@2.0.18": {}
        }
    },
    "entity/widget": {
        "name": "entity/widget",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "entity/widget",
        "config": {
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-esm-env"
            },
            "teammc.snowcone/node-esm-env@2.0.18": {}
        }
    },
    "hook/use-dashboard": {
        "name": "hook/use-dashboard",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "hook/use-dashboard",
        "config": {
            "teambit.envs/envs": {
                "env": "teammc.snowcone/react-esm-env"
            },
            "teammc.snowcone/react-esm-env@2.0.12": {}
        }
    },
    "hook/use-fullscreen-handler": {
        "name": "hook/use-fullscreen-handler",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "hook/use-fullscreen-handler",
        "config": {
            "teambit.envs/envs": {
                "env": "teammc.snowcone/react-esm-env"
            },
            "teammc.snowcone/react-esm-env@2.0.12": {}
        }
    },
    "hook/use-immutable-callback": {
        "name": "hook/use-immutable-callback",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "hook/use-immutable-callback",
        "config": {
            "teambit.envs/envs": {
                "env": "teammc.snowcone/react-esm-env"
            },
            "teammc.snowcone/react-esm-env@2.0.12": {}
        }
    },
    "hook/use-query-result-data": {
        "name": "hook/use-query-result-data",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "hook/use-query-result-data",
        "config": {
            "teambit.envs/envs": {
                "env": "teammc.snowcone/react-esm-env"
            },
            "teammc.snowcone/react-esm-env@2.0.12": {}
        }
    },
    "hook/use-refresh-rate-handler": {
        "name": "hook/use-refresh-rate-handler",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "hook/use-refresh-rate-handler",
        "config": {
            "teambit.envs/envs": {
                "env": "teammc.snowcone/react-esm-env"
            },
            "teammc.snowcone/react-esm-env@2.0.12": {}
        }
    },
    "service/send-redash-mail": {
        "name": "service/send-redash-mail",
        "scope": "manyun.redash",
        "version": "1.0.0",
        "mainFile": "index.ts",
        "rootDir": "service/send-redash-mail",
        "config": {
            "teammc.snowcone/node-esm-env@2.0.18": {}
        }
    },
    "ui/dashboard-component": {
        "name": "ui/dashboard-component",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "ui/dashboard-component",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/dashboard-control": {
        "name": "ui/dashboard-control",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "ui/dashboard-control",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/dashboard-grid": {
        "name": "ui/dashboard-grid",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "ui/dashboard-grid",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/dashboard-header": {
        "name": "ui/dashboard-header",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "ui/dashboard-header",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/dashboard-page": {
        "name": "ui/dashboard-page",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "ui/dashboard-page",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/dashboard-page-title": {
        "name": "ui/dashboard-page-title",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "ui/dashboard-page-title",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/dashboard-widget": {
        "name": "ui/dashboard-widget",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "ui/dashboard-widget",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/date-input": {
        "name": "ui/date-input",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "ui/date-input",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/date-parameter": {
        "name": "ui/date-parameter",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "ui/date-parameter",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/date-range-input": {
        "name": "ui/date-range-input",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "ui/date-range-input",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/date-range-parameter": {
        "name": "ui/date-range-parameter",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "ui/date-range-parameter",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/date-time-input": {
        "name": "ui/date-time-input",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "ui/date-time-input",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/date-time-range-input": {
        "name": "ui/date-time-range-input",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "ui/date-time-range-input",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/download-pdf": {
        "name": "ui/download-pdf",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "ui/download-pdf",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/dynamic-button": {
        "name": "ui/dynamic-button",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "ui/dynamic-button",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/dynamic-date-picker": {
        "name": "ui/dynamic-date-picker",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "ui/dynamic-date-picker",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/dynamic-date-range-picker": {
        "name": "ui/dynamic-date-range-picker",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "ui/dynamic-date-range-picker",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/expanded-widget-dialog": {
        "name": "ui/expanded-widget-dialog",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "ui/expanded-widget-dialog",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/filters": {
        "name": "ui/filters",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "ui/filters",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/parameter-apply-button": {
        "name": "ui/parameter-apply-button",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "ui/parameter-apply-button",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/parameter-value-input": {
        "name": "ui/parameter-value-input",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "ui/parameter-value-input",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/parameters": {
        "name": "ui/parameters",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "ui/parameters",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/plain-button": {
        "name": "ui/plain-button",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "ui/plain-button",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/refresh-button": {
        "name": "ui/refresh-button",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "ui/refresh-button",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/send-report-by-email-modal": {
        "name": "ui/send-report-by-email-modal",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "ui/send-report-by-email-modal",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/textbox-widget": {
        "name": "ui/textbox-widget",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "ui/textbox-widget",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/time-ago": {
        "name": "ui/time-ago",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "ui/time-ago",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/visualization-name": {
        "name": "ui/visualization-name",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "ui/visualization-name",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/visualization-renderer": {
        "name": "ui/visualization-renderer",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "ui/visualization-renderer",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/visualization-widget": {
        "name": "ui/visualization-widget",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "ui/visualization-widget",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/visualization-widget-footer": {
        "name": "ui/visualization-widget-footer",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "ui/visualization-widget-footer",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/visualization-widget-header": {
        "name": "ui/visualization-widget-header",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "ui/visualization-widget-header",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/widget": {
        "name": "ui/widget",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "ui/widget",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/widget-delete-button": {
        "name": "ui/widget-delete-button",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "ui/widget-delete-button",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/widget-dropdown-button": {
        "name": "ui/widget-dropdown-button",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "ui/widget-dropdown-button",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "util/auto-height-controller": {
        "name": "util/auto-height-controller",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "util/auto-height-controller",
        "config": {
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-esm-env"
            },
            "teammc.snowcone/node-esm-env@2.0.18": {}
        }
    },
    "util/button-type": {
        "name": "util/button-type",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "util/button-type",
        "config": {
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-esm-env"
            },
            "teammc.snowcone/node-esm-env@2.0.18": {}
        }
    },
    "util/client-config": {
        "name": "util/client-config",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "util/client-config",
        "config": {
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-esm-env"
            },
            "teammc.snowcone/node-esm-env@2.0.18": {}
        }
    },
    "util/dashboard-grid-options": {
        "name": "util/dashboard-grid-options",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "util/dashboard-grid-options",
        "config": {
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-esm-env"
            },
            "teammc.snowcone/node-esm-env@2.0.18": {}
        }
    },
    "util/duration-humanize": {
        "name": "util/duration-humanize",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "util/duration-humanize",
        "config": {
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-esm-env"
            },
            "teammc.snowcone/node-esm-env@2.0.18": {}
        }
    },
    "util/filter-data": {
        "name": "util/filter-data",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "util/filter-data",
        "config": {
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-esm-env"
            },
            "teammc.snowcone/node-esm-env@2.0.18": {}
        }
    },
    "util/is-valid-key": {
        "name": "util/is-valid-key",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "util/is-valid-key",
        "config": {
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-esm-env"
            },
            "teammc.snowcone/node-esm-env@2.0.18": {}
        }
    },
    "util/mock-query-data": {
        "name": "util/mock-query-data",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "util/mock-query-data",
        "config": {
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-esm-env"
            },
            "teammc.snowcone/node-esm-env@2.0.18": {}
        }
    },
    "util/parameter-function": {
        "name": "util/parameter-function",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "util/parameter-function",
        "config": {
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-esm-env"
            },
            "teammc.snowcone/node-esm-env@2.0.18": {}
        }
    },
    "util/pluralize": {
        "name": "util/pluralize",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "util/pluralize",
        "config": {
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-esm-env"
            },
            "teammc.snowcone/node-esm-env@2.0.18": {}
        }
    },
    "util/resize-observe": {
        "name": "util/resize-observe",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "util/resize-observe",
        "config": {
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-esm-env"
            },
            "teammc.snowcone/node-esm-env@2.0.18": {}
        }
    },
    "util/seconds-to-interval": {
        "name": "util/seconds-to-interval",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "util/seconds-to-interval",
        "config": {
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-esm-env"
            },
            "teammc.snowcone/node-esm-env@2.0.18": {}
        }
    },
    "util/to-human": {
        "name": "util/to-human",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.redash",
        "mainFile": "index.ts",
        "rootDir": "util/to-human",
        "config": {
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-esm-env"
            },
            "teammc.snowcone/node-esm-env@2.0.18": {}
        }
    },
    "$schema-version": "16.0.0"
}