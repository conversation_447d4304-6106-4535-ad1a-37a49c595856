import moment from 'moment';
import 'moment/locale/zh-cn';
import React from 'react';
import { Link, RouterProvider, createBrowserRouter, useParams } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';

const Dashboard = React.lazy(() => import('@manyun/redash.ui.dashboard-page'));

const PageDashboard = () => {
  const { id } = useParams<{ id: string }>();

  return <Dashboard dashboardId={id ?? '__some-fake-id'} />;
};

export type AppProps = {
  basename?: string;
};

export function App(props: AppProps) {
  const { basename } = props;

  const router = createBrowserRouter(
    [
      {
        path: '/',
        element: <Link to="/dashboard/-">Go to -</Link>,
      },
      {
        path: '/dashboard/:id',
        element: <PageDashboard />,
      },
    ],
    { basename }
  );

  return (
    <ConfigProvider>
      <React.Suspense fallback="Loading...">
        <RouterProvider router={router} />
      </React.Suspense>
    </ConfigProvider>
  );
}

export default App;
