import moment from 'moment';
import 'moment/locale/zh-cn';
import React from 'react';
import { BrowserRouter } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';
import { DashboardPage as Dashboard } from '@manyun/redash.ui.dashboard-page';
import type { DashboardPageProps } from '@manyun/redash.ui.dashboard-page';

moment.locale('zh-cn');

export default function DashboardPage(props: DashboardPageProps) {
  return (
    <ConfigProvider>
      {/* @ts-ignore ignored for now */}
      <BrowserRouter>
        <Dashboard {...props} />
      </BrowserRouter>
    </ConfigProvider>
  );
}
