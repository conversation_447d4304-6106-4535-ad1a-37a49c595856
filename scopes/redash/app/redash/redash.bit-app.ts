import fs from 'node:fs';
import path from 'node:path';

import { MfReact } from '@glpdev/react.app-types.mf-rspack';

const BUILD_ENV = process.env.BUILD_ENV ?? 'local';
const PORT = 3008;
const outputPublicPath = process.env.APP_REDASH_MF_PUBLIC_PATH ?? `http://127.0.0.1:${PORT}/`;
const lessLoaderOptions = {
  additionalData: (content: string) => {
    return '@root-entry-name: variable;\n\n' + content;
  },
  lessOptions: {
    javascriptEnabled: true,
  },
};
const appDirectory = fs.realpathSync(process.cwd());
const resolveApp = (relativePath: string) => path.resolve(appDirectory, relativePath);

export default MfReact.from({
  name: 'redash',
  clientRoot: './redash.bootstrap.js',
  defaultPort: [PORT, PORT],
  outputPublicPath,
  lessLoaderOptions,
  swcSourceMaps: false,
  defineVariables: {
    NODE_ENV: process.env.NODE_ENV,
    BUILD_ENV,
  },
  transformers: [
    mutator => {
      mutator.merge({
        resolve: {
          ...mutator.config.resolve,
          alias: {
            ...mutator.config.resolve?.alias,
            'antd/lib': 'antd/es',
          },
          modules: [
            ...(mutator.config.resolve?.modules ?? []),
            'node_modules',
            resolveApp('../../node_modules'),
            resolveApp('../dc-brain/node_modules'),
            resolveApp('../resource-hub/node_modules'),
          ],
        },
      });

      return mutator;
    },
  ],
  moduleFederation: {
    name: 'manyun_redash',
    filename: 'redash.remote-entry.js',
    exposes: {
      './app': './app.js',
      './page.dashboard': './exposes/page.dashboard.js',
    },
    shared: {
      react: {
        import: 'react', // the "react" package will be used a provided and fallback module
        shareKey: 'react', // under this name the shared module will be placed in the share scope
        shareScope: 'default', // share scope with this name will be used
        singleton: true,
        requiredVersion: '^18.2.0',
      },
      'react-dom': {
        singleton: true,
        requiredVersion: '^18.2.0',
      },
      '@manyun/service.request': {
        singleton: true,
        requiredVersion: '^1.0.2',
      },
      '@glpdev/symphony.services.request': {
        singleton: true,
        requiredVersion: '^1.0.0',
      },
    },
    dev: BUILD_ENV === 'local',
    dts: BUILD_ENV === 'local',
  },
});
