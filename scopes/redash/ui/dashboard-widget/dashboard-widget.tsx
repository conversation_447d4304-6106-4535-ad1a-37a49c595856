import React from 'react';

import { TextboxWidget } from '@manyun/redash.ui.textbox-widget';
import { VisualizationWidget } from '@manyun/redash.ui.visualization-widget';

export type DashboardWidgetProps = {
  /**
   * a text to be rendered in the component.
   */
  text: string;
};
const WidgetTypeEnum = {
  TEXTBOX: 'textbox',
  VISUALIZATION: 'visualization',
  RESTRICTED: 'restricted',
};

const DashboardWidget = React.memo(
  function DashboardWidget({
    widget,
    dashboard,
    onLoadWidget,
    onRefreshWidget,
    onRemoveWidget,
    onParameterMappingsChange,
    isEditing,
    canEdit,
    isPublic,
    isLoading,
    filters,
  }: // eslint-disable-next-line @typescript-eslint/no-explicit-any
  any) {
    const { type } = widget;
    const onLoad = () => onLoadWidget(widget);
    const onRefresh = () => onRefreshWidget(widget);
    const onDelete = () => onRemoveWidget(widget.id);

    if (type === WidgetTypeEnum.VISUALIZATION) {
      return (
        <VisualizationWidget
          // @ts-ignore types
          widget={widget}
          dashboard={dashboard}
          // 先不管筛选
          filters={[]}
          isEditing={isEditing}
          canEdit={canEdit}
          isPublic={isPublic}
          isLoading={isLoading}
          onLoad={onLoad}
          onRefresh={onRefresh}
          onDelete={onDelete}
          onParameterMappingsChange={onParameterMappingsChange}
        />
      );
    }
    if (type === WidgetTypeEnum.TEXTBOX) {
      return (
        <TextboxWidget widget={widget} canEdit={canEdit} isPublic={isPublic} onDelete={onDelete} />
      );
    }
    return <>暂无</>;
  },
  (prevProps, nextProps) =>
    prevProps.widget === nextProps.widget &&
    prevProps.canEdit === nextProps.canEdit &&
    prevProps.isPublic === nextProps.isPublic &&
    prevProps.isLoading === nextProps.isLoading &&
    prevProps.filters === nextProps.filters &&
    prevProps.isEditing === nextProps.isEditing
);

export { DashboardWidget };
