import cx from 'classnames';
import React from 'react';

import { Modal } from '@manyun/base-ui.ui.modal';
import { WidgetDeleteButton } from '@manyun/redash.ui.widget-delete-button';
import { WidgetDropdownButton } from '@manyun/redash.ui.widget-dropdown-button';

class Widget extends React.Component<any> {
  static defaultProps = {
    className: '',
    children: null,
    header: null,
    footer: null,
    canEdit: false,
    isPublic: false,
    // refreshStartedAt: null,
    menuOptions: null,
    tileProps: {},
    onDelete: () => {},
  };

  componentDidMount() {
    // recordEvent('view', 'widget', widget.id);
  }

  deleteWidget = () => {
    const { widget, onDelete } = this.props;

    Modal.confirm({
      title: 'Delete Widget',
      content: 'Are you sure you want to remove this widget from the dashboard?',
      okText: 'Delete',
      okType: 'danger',
      onOk: () => widget.delete().then(onDelete),
      maskClosable: true,
      autoFocusButton: null,
    });
  };

  render() {
    const { className, children, header, footer, canEdit, menuOptions, tileProps } = this.props;
    //const showDropdownButton = !isPublic && (canEdit || !isEmpty(menuOptions));
    return (
      <div className="widget-wrapper">
        {/* <span>xxxxxxxxxxx</span> */}
        <div className={cx('tile body-container', className)} {...tileProps}>
          <div className="widget-actions">
            {/* 暂时不需要下载功能 */}
            {false && (
              <WidgetDropdownButton
                extraOptions={menuOptions}
                showDeleteOption={canEdit}
                onDelete={this.deleteWidget}
              />
            )}
            {canEdit && <WidgetDeleteButton onClick={this.deleteWidget} />}
          </div>
          {/* header 就是 这个 widget的名字 */}
          <div className="body-row widget-header" aria-label="widget name">
            {header}
          </div>
          {children}
          {footer && <div className="body-row tile__bottom-control">{footer}</div>}
        </div>
      </div>
    );
  }
}

export { Widget };
