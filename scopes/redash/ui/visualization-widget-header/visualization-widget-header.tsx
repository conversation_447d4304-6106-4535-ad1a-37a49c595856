/* eslint-disable @typescript-eslint/no-explicit-any */
import { ExpandOutlined, ReloadOutlined } from '@ant-design/icons';
import classNames from 'classnames';
import { isEmpty } from 'lodash-es';
import moment from 'moment';
import React, { useEffect, useMemo, useState } from 'react';

import { ExportOutlined } from '@manyun/base-ui.icons';
import { Button } from '@manyun/base-ui.ui.button';
import { saveJSONAsXLSX } from '@manyun/base-ui.util.xlsx';
import { useQueryResultData } from '@manyun/redash.hook.use-query-result-data';
import { Parameters } from '@manyun/redash.ui.parameters';
import { TimeAgo } from '@manyun/redash.ui.time-ago';
import { VisualizationName } from '@manyun/redash.ui.visualization-name';
import { filterData } from '@manyun/redash.util.filter-data';

export type VisualizationWidgetHeaderProps = {
  widget: any;
  parameters: any;
  onParametersUpdate: any;
  onParametersEdit: any;
  isPublic: boolean;
  onRefresh: any;
  onExpand: any;
};
// 暂时只考虑展示一个widget的名称
export function VisualizationWidgetHeader({
  widget,
  parameters,
  onParametersUpdate,
  onParametersEdit,
  isPublic,
  onRefresh,
  onExpand,
}: VisualizationWidgetHeaderProps) {
  const widgetQueryResult = widget.getQueryResult();
  const columnTitleKeyMapper = widget.getColumnTitleKeyMapper();
  const data = useQueryResultData(widgetQueryResult);

  const [rotateFlag, setRotateFlag] = useState(false);

  const [updatedAt, setUpdateAt] = useState('');
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [refreshClickButtonId, setRefreshClickButtonId] = useState();
  const refreshWidget = async () => {
    setRotateFlag(true);
    await onRefresh();
    setUpdateAt(moment(undefined).format('YYYY-MM-DD HH:mm:ss'));
    setRotateFlag(false);
  };
  useEffect(() => {
    if (widgetQueryResult && widgetQueryResult.query_result?.retrieved_at) {
      setUpdateAt(widgetQueryResult.query_result?.retrieved_at);
    }
  }, [widgetQueryResult]);
  useEffect(() => {}, [rotateFlag]);
  const filteredData = useMemo(
    () => ({
      columns: data.columns,
      rows: filterData(data.rows, []),
    }),
    [data]
  );
  return (
    <>
      <div className="t-header widget clearfix">
        <div className="th-title">
          <p>
            <VisualizationName
              visualization={widget.visualization}
              queryName={widget.getQuery().name}
            />
          </p>
          {/* {!isEmpty(widget.getQuery().description) && (
          <HtmlContent className="text-muted markdown query--description">
            {markdown.toHTML(widget.getQuery().description || '')}
          </HtmlContent>
        )} */}
          <span>
            {!isPublic && !!widgetQueryResult && (
              <Button
                type="text"
                className="refresh-button hidden-print btn btn-sm btn-default btn-transparent"
                aria-label="RefreshButton"
                onClick={() => refreshWidget()}
                data-test="RefreshButton"
              >
                <i
                  className={classNames('zmdi zmdi-refresh', {
                    'zmdi-hc-spin': refreshClickButtonId === 1,
                  })}
                  aria-hidden="true"
                />
                {rotateFlag ? <ReloadOutlined spin /> : <ReloadOutlined />}
                {/* <span className="sr-only">
         {refreshClickButtonId === 1 ? 'Refreshing, please wait. ' : 'Press to refresh. '}
       </span> */}
                {/* <TimeAgo date={moment(updatedAt).format('YYYY-MM-DD HH:mm:ss')} /> */}
                <TimeAgo date={updatedAt} />
              </Button>
            )}
            <span className="visible-print">
              <i className="zmdi zmdi-time-restore" aria-hidden="true" />
              {/* {moment(updatedAt).format('YYYY-MM-DD HH:mm:ss')} */}
            </span>

            <Button
              aria-label="ModalButton"
              className="btn btn-sm btn-default hidden-print btn-transparent btn__refresh"
              onClick={onExpand}
            >
              <i className="zmdi zmdi-fullscreen" aria-hidden="true" />
              <ExpandOutlined />
            </Button>

            <Button
              icon={<ExportOutlined />}
              onClick={() => {
                saveJSONAsXLSX(
                  [
                    {
                      headers:
                        filteredData?.columns?.map((column: { name: string }) => ({
                          dataIndex: column.name,
                          title: columnTitleKeyMapper[column.name] ?? column.name,
                        })) ?? [],

                      data: filteredData?.rows ?? [],
                    },
                  ],
                  widget.getQuery().name
                );
              }}
            >
              导出
            </Button>
          </span>
        </div>
        {!isEmpty(parameters) && (
          <div className="m-b-10">
            <Parameters
              parameters={parameters}
              sortable={false}
              appendSortableToParent={false}
              onValuesChange={onParametersUpdate}
              //参数的编辑先不考虑
              onParametersEdit={() => {}}
            />
          </div>
        )}
      </div>
    </>
  );
}
