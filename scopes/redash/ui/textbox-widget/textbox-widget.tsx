// @ts-nocheck pending refactor
import { markdown } from 'markdown';
import React, { useState } from 'react';

import { Menu } from '@manyun/base-ui.ui.menu';
import { HtmlContent } from '@manyun/redash.ui.viz-lib';
// import TextboxDialog from "@/components/dashboards/TextboxDialog";
import { Widget } from '@manyun/redash.ui.widget';

export type TextboxWidgetProps = {
  widget: any;
  canEdit: boolean;
};

export function TextboxWidget(props) {
  const { widget, canEdit } = props;
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [text, setText] = useState(widget.text);

  // 编辑先不管
  const editTextBox = () => {
    // TextboxDialog.showModal({
    //   text: widget.text,
    // }).onClose(newText => {
    //   widget.text = newText;
    //   setText(newText);
    //   return widget.save();
    // });
  };

  const TextboxMenuOptions = [
    <Menu.Item key="edit" onClick={editTextBox}>
      Edit
    </Menu.Item>,
  ];

  if (!widget.width) {
    return null;
  }

  return (
    <Widget {...props} menuOptions={canEdit ? TextboxMenuOptions : null} className="widget-text">
      <HtmlContent className="body-row-auto scrollbox t-body p-15 markdown" style={{ padding: 16 }}>
        {markdown.toHTML(text || '')}
      </HtmlContent>
      {/* <div className="body-row-auto scrollbox t-body p-15 markdown">{text}</div> */}
    </Widget>
  );
}
