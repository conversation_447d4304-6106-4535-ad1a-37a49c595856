// @ts-nocheck pending refactor
import { isEmpty } from 'lodash-es';
import React, { useState } from 'react';

import { useDashboard } from '@manyun/redash.hook.use-dashboard';
import { DashboardGrid } from '@manyun/redash.ui.dashboard-grid';
import { DashboardHeader } from '@manyun/redash.ui.dashboard-header';
import { Parameters } from '@manyun/redash.ui.parameters';

import './dashboard-component.less';

export type DashboardComponentProps = {
  dashboard: object;
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function DashboardComponent({ dashboard }: any) {
  const dashboardConfiguration = useDashboard(dashboard);

  const { refreshWidget, loadWidget } = dashboardConfiguration;
  const {
    // dashboard,
    //   filters,
    //   setFilters,
    // loadDashboard,
    //   loadWidget,
    //   removeWidget,
    //   saveDashboardLayout,
    globalParameters,
    //   updateDashboard,
    refreshDashboard,
    // refreshing,
    //   refreshWidget,
    //   editingLayout,
    //   setGridDisabled,
  } = dashboardConfiguration;
  //
  //

  const [, setPageContainer] = useState(null);
  // const [bottomPanelStyles, setBottomPanelStyles] = useState({});
  // 筛选参数有关的先注释
  // const onParametersEdit = (parameters) => {
  //   const paramOrder = map(parameters, 'name');
  //   updateDashboard({ options: { globalParamOrder: paramOrder } });
  // };

  // useEffect(() => {
  //   dashboard.widgets = dashboard.widgets.map((item: any) => new Widget(item));
  // }, []);

  // 编辑模式下的有关的容器大小改变先注释
  // useEffect(() => {
  //   if (pageContainer) {
  //     const unobserve = observe(pageContainer, () => {
  //       // 编辑模式先不管
  //       // if (editingLayout) {
  //       //   const style = window.getComputedStyle(pageContainer, null);
  //       //   const bounds = pageContainer.getBoundingClientRect();
  //       //   const paddingLeft = parseFloat(style.paddingLeft) || 0;
  //       //   const paddingRight = parseFloat(style.paddingRight) || 0;
  //       //   setBottomPanelStyles({
  //       //     left: Math.round(bounds.left) + paddingRight,
  //       //     width: pageContainer.clientWidth - paddingLeft - paddingRight,
  //       //   });
  //       // }

  //       // reflow grid when container changes its size
  //       window.dispatchEvent(new Event('resize'));
  //     });
  //     return unobserve;
  //   }
  // }, [pageContainer]);
  // 源代码在下面一行
  // }, [pageContainer, editingLayout]);
  // useEffect(() => {
  //   message.info('dashboard刷新了');
  //   setScreenRefresh(true);
  //   // setScreenRefresh(false);
  //   // if (dashboardConfiguration) setScreenRefresh(true);
  // }, [dashboardConfiguration]);

  return (
    <div
      className="container"
      ref={setPageContainer}
      data-test={`DashboardId${dashboard.id}Container`}
    >
      <DashboardHeader
        widgets={dashboard.widgets || []}
        dashboardConfiguration={dashboardConfiguration}
        headerExtra={<></>}
      />
      {/* 筛选参数有关的代码先不考虑 */}
      {!isEmpty(globalParameters) && (
        <div
          className="dashboard-parameters m-b-10 p-15 bg-white tiled light-parameter"
          data-test="DashboardParameters"
          style={{ padding: '10px', margin: '0px 0px 10px 0px' }}
        >
          <Parameters
            parameters={globalParameters}
            onValuesChange={refreshDashboard}
            sortable={false}
            onParametersEdit={() => {}}
          />
        </div>
      )}
      {/* {!isEmpty(filters) && (
        <div className="m-b-10 p-15 bg-white tiled" data-test="DashboardFilters">
          <Filters filters={filters} onChange={setFilters} />
        </div>
      )} */}
      {/* {editingLayout && <DashboardSettings dashboardConfiguration={dashboardConfiguration} />} */}
      <div id="dashboard-container">
        <DashboardGrid
          dashboard={dashboard}
          widgets={dashboard.widgets || []}
          filters={[]}
          isEditing={false}
          onLayoutChange={() => {}}
          onBreakpointChange={() => {}}
          onLoadWidget={loadWidget}
          onRefreshWidget={refreshWidget}
          onRemoveWidget={() => {}}
          onParameterMappingsChange={() => {}}
          // dashboard={dashboard}
          // widgets={dashboard.widgets}
          // filters={filters}
          // isEditing={editingLayout}
          // onLayoutChange={editingLayout ? saveDashboardLayout : () => {}}
          // onBreakpointChange={setGridDisabled}
          // onLoadWidget={loadWidget}
          // onRefreshWidget={refreshWidget}
          // onRemoveWidget={removeWidget}
          // onParameterMappingsChange={loadDashboard}
        />
      </div>

      {/* 编辑模式下的组件先不考虑 */}
      {/* {editingLayout && (
        <AddWidgetContainer dashboardConfiguration={dashboardConfiguration} style={bottomPanelStyles} />
      )} */}
    </div>
  );
}
