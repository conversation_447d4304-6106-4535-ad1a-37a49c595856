// @ts-nocheck pending refactor
import classNames from 'classnames';
import { includes, isArray, isObject } from 'lodash-es';
import moment from 'moment';
import React from 'react';

import { isDynamicDateRange } from '@manyun/redash.entity.date-range-parameter';
import { DateRangeInput } from '@manyun/redash.ui.date-range-input';
import { DateTimeRangeInput } from '@manyun/redash.ui.date-time-range-input';
import { DynamicButton } from '@manyun/redash.ui.dynamic-button';

import './dynamic-date-picker.less';

function isValidDateRangeValue(value) {
  return (
    isArray(value) && value.length === 2 && moment.isMoment(value[0]) && moment.isMoment(value[1])
  );
}

class DynamicDateRangePicker extends React.Component {
  static defaultProps = {
    type: 'date-range',
    className: '',
    value: null,
    parameter: null,
    dynamicButtonOptions: {
      options: [],
    },
    onSelect: () => {},
  };

  constructor(props) {
    super(props);
    this.dateRangeComponentRef = React.createRef();
  }

  onDynamicValueSelect = dynamicValue => {
    const { onSelect, parameter } = this.props;
    if (dynamicValue === 'static') {
      const parameterValue = parameter.getExecutionValue();
      if (isObject(parameterValue) && parameterValue.start && parameterValue.end) {
        onSelect([moment(parameterValue.start), moment(parameterValue.end)]);
      } else {
        onSelect(null);
      }
    } else {
      onSelect(dynamicValue.value);
    }
    // give focus to the DatePicker to get keyboard shortcuts to work
    this.dateRangeComponentRef.current.focus();
  };

  render() {
    const {
      type,
      value,
      onSelect,
      className,
      dynamicButtonOptions,
      dateRangeOptions,
      parameter,
      ...rest
    } = this.props;
    const isDateTimeRange = includes(type, 'datetime-range');
    const hasDynamicValue = isDynamicDateRange(value);

    const additionalAttributes = {};

    let DateRangeComponent = DateRangeInput;
    if (isDateTimeRange) {
      DateRangeComponent = DateTimeRangeInput;
      if (includes(type, 'with-seconds')) {
        additionalAttributes.withSeconds = true;
      }
    }

    if (isValidDateRangeValue(value) || value === null) {
      additionalAttributes.value = value;
    }

    if (hasDynamicValue) {
      additionalAttributes.placeholder = [value && value.name];
      additionalAttributes.value = null;
    }

    return (
      <div {...rest} className={classNames('date-range-parameter', className)}>
        <DateRangeComponent
          {...dateRangeOptions}
          ref={this.dateRangeComponentRef}
          className={classNames('redash-datepicker date-range-input', type, {
            'dynamic-value': hasDynamicValue,
          })}
          onSelect={onSelect}
          suffixIcon={null}
          {...additionalAttributes}
        />
        <DynamicButton
          options={dynamicButtonOptions.options}
          staticValueLabel={dynamicButtonOptions.staticValueLabel}
          selectedDynamicValue={hasDynamicValue ? value : null}
          enabled={hasDynamicValue}
          onSelect={this.onDynamicValueSelect}
        />
      </div>
    );
  }
}

export { DynamicDateRangePicker };
