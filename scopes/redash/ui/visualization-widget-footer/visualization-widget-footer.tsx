// @ts-nocheck pending refactor
import { ExpandOutlined, ReloadOutlined } from '@ant-design/icons';
import cx from 'classnames';
import { compact, invoke, isEmpty, map } from 'lodash-es';
import moment from 'moment';
import React, { useEffect, useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { PlainButton } from '@manyun/redash.ui.plain-button';
import { TimeAgo } from '@manyun/redash.ui.time-ago';

export type VisualizationWidgetFooterProps = {
  /**
   * a text to be rendered in the component.
   */
  widget: object;
  isPublic: boolean;
  onRefresh: any;
  onExpand: any;
};

export function VisualizationWidgetFooter({ widget, isPublic, onRefresh, onExpand }) {
  // hook写好之后用下面这一行
  const widgetQueryResult = widget.getQueryResult();
  const [rotateFlag, setRotateFlag] = useState(false);
  //测试组件所用
  // const widgetQueryResult = {};

  // hook写好之后用下面这一行
  const updatedAt = widgetQueryResult?.query_result?.qretrieved_at;
  // 下面这一行是测试用
  const [refreshClickButtonId, setRefreshClickButtonId] = useState();
  const refreshWidget = async buttonId => {
    setRotateFlag(true);

    await onRefresh();
    setRotateFlag(false);
  };
  useEffect(() => {}, [rotateFlag]);
  return widgetQueryResult ? (
    <>
      <span>
        {/* {!isPublic && (
          <Button
            type="text"
            className="btn btn-sm btn-default hidden-print btn-transparent btn__refresh"
            onClick={() => refreshWidget(2)}
            aria-label="RefreshButton2"
          >
        
            {rotateFlag ? <ReloadOutlined spin /> : <ReloadOutlined />}
       
          </Button>
        )} */}

        {!isPublic && !!widgetQueryResult && (
          <Button
            type="text"
            className="refresh-button hidden-print btn btn-sm btn-default btn-transparent"
            aria-label="RefreshButton"
            onClick={() => refreshWidget(1)}
            data-test="RefreshButton"
          >
            <i
              className={cx('zmdi zmdi-refresh', { 'zmdi-hc-spin': refreshClickButtonId === 1 })}
              aria-hidden="true"
            />
            {rotateFlag ? <ReloadOutlined spin /> : <ReloadOutlined />}
            {/* <span className="sr-only">
              {refreshClickButtonId === 1 ? 'Refreshing, please wait. ' : 'Press to refresh. '}
            </span> */}
            <TimeAgo date={updatedAt} />
          </Button>
        )}
        <span className="visible-print">
          <i className="zmdi zmdi-time-restore" aria-hidden="true" />
          {moment(updatedAt).format('M/D/YYYY HH:mm:ss')}
        </span>
        {isPublic && (
          <span className="small hidden-print">
            <i className="zmdi zmdi-time-restore" aria-hidden="true" /> <TimeAgo date={updatedAt} />
          </span>
        )}
        <Button
          aria-label="ModalButton"
          className="btn btn-sm btn-default hidden-print btn-transparent btn__refresh"
          onClick={onExpand}
        >
          <i className="zmdi zmdi-fullscreen" aria-hidden="true" />
          <ExpandOutlined />
        </Button>
      </span>
    </>
  ) : null;
}
