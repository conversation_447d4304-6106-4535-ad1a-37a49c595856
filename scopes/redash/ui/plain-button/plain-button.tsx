import classNames from 'classnames';
import React from 'react';

export interface PlainButtonProps
  extends Omit<React.ButtonHTMLAttributes<HTMLButtonElement>, 'type'> {
  /**
   * a text to be rendered in the component.
   */
  type?: 'link' | 'button';
}

export function PlainButton({ className, type, ...rest }: PlainButtonProps) {
  return (
    <button
      aria-label="plain button"
      className={classNames(
        'plain-button',
        'clickable',
        { 'plain-button-link': type === 'link' },
        className
      )}
      type="button"
      {...rest}
    />
  );
}
