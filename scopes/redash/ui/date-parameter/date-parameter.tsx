import React from 'react';

import { getDynamicDateFromString } from '@manyun/redash.entity.date-parameter';
import { DynamicDatePicker } from '@manyun/redash.ui.dynamic-date-picker';

const DYNAMIC_DATE_OPTIONS = [
  {
    name: '今天',
    value: getDynamicDateFromString('d_now'),
    label: () => getDynamicDateFromString('d_now').value().format('MMM D'),
  },
  {
    name: '昨天',
    value: getDynamicDateFromString('d_yesterday'),
    label: () => getDynamicDateFromString('d_yesterday').value().format('MMM D'),
  },
];

function DateParameter(props: any) {
  return (
    <DynamicDatePicker
      dynamicButtonOptions={{ options: DYNAMIC_DATE_OPTIONS }}
      {...props}
      dateOptions={{ 'aria-label': 'Parameter date value' }}
    />
  );
}

export { DateParameter };
