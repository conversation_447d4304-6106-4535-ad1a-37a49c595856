// @ts-nocheck pending refactor
import React from 'react';

import { registeredVisualizations } from '@manyun/redash.ui.viz-lib';

import './visualization-name.less';

export type VisualizationNameProps = {
  /**
   * a text to be rendered in the component.
   */
  visualization: object;
  queryName: string;
};

export function VisualizationName({ visualization, queryName }: any) {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const config = registeredVisualizations[visualization.type];

  return (
    <>
      <span className="visualization-name">
        {/* {config && visualization.name !== config.name ? visualization.name : null}
         */}
        {visualization.name || null}
      </span>
      <span style={{ color: '#aaaaaa' }}>{queryName || '暂无说明'}</span>
    </>
  );
}
