import html2canvas from 'html2canvas';
import { jsPDF } from 'jspdf';
import React, { useEffect, useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';

export type DownloadPdfProps = {
  pdfName: string;
};

export function DownloadPdf({ pdfName }: DownloadPdfProps) {
  const [canvasWidth, setCanvasWidth] = useState<number | undefined>(0);
  const [canvasHeight, setCanvasHeight] = useState<number | undefined>(0);

  useEffect(() => {
    let w = document.getElementById('root')?.offsetWidth;
    let h = document.getElementById('root')?.offsetHeight;
    setCanvasWidth(w);
    setCanvasHeight(h);
  }, []);
  const downloadPdf = () => {
    message.loading('pdf下载中');
    html2canvas(document.querySelector('#root') ?? document.body, {
      width: canvasWidth,
      height: canvasHeight,
      useCORS: true,
    }).then(
      (canvas: { toDataURL: (arg0: string, arg1: number) => string; height: any; width: any }) => {
        // 此为base64编码格式
        let imgUrl = canvas.toDataURL('image/png', 1).replace('image/png', 'image/octet-stream'); // 此方法可以设置截图质量（0-1）
        const pdf = new jsPDF({
          unit: 'px',
          format: [canvas.height, canvas.width],
        });
        pdf.addImage(imgUrl, 'png', 0, 0, canvas.width, canvas.height);

        pdf.save(`${pdfName}.pdf`);
        message.destroy();
      }
    );
  };
  return (
    <Button aria-label="pdf-button" type="link" compact onClick={downloadPdf}>
      下载pdf
    </Button>
  );
}
