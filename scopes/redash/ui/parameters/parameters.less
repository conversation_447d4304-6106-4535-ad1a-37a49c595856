.parameter-block {
  display: inline-block;
  // background: white;
  padding: 0 12px 6px 0;
  vertical-align: top;
  z-index: 1;
  white-space: nowrap;

  .drag-handle {
    padding: 0 5px;
    margin-left: -5px;
    height: 36px;
  }

  .parameter-container.sortable-container & {
    margin: 4px 0 0 4px;
    padding: 3px 6px 6px;
  }

  &.parameter-dragged {
    z-index: 2;
    margin: 4px 0 0 4px;
    padding: 3px 6px 6px;
    box-shadow: 0 4px 9px -3px rgba(102, 136, 153, 0.15);
  }
}

.parameter-heading {
  display: flex;
  align-items: center;
  padding-bottom: 4px;

  label {
    margin-bottom: 1px;
    overflow: hidden;
    text-overflow: ellipsis;
    min-width: 100%;
    max-width: 195px;
    white-space: nowrap;

    .parameter-block[data-editable] & {
      min-width: calc(100% - 27px); // make room for settings button
      max-width: 195px - 27px;
    }
  }
}

.parameter-container {
  position: relative;

  &.sortable-container {
    padding: 0 4px 4px 0;
  }

  .parameter-apply-button {
    display: none; // default for mobile

    // "floating" on desktop
    @media (min-width: 768px) {
      position: absolute;
      bottom: -36px;
      left: -15px;
      border-radius: 2px;
      z-index: 2;
      transition: opacity 150ms ease-out;
      box-shadow: 0 4px 9px -3px rgba(102, 136, 153, 0.15);
      padding: 4px;
      padding-left: 16px;
      opacity: 0;
      display: block;
      pointer-events: none; // so tooltip doesn't remain after button hides
    }

    &[data-show='true'] {
      opacity: 1;
      display: block;
      pointer-events: auto;
    }

    button {
      padding: 0 8px 0 6px;
      color: #2096f3;
      border-color: #50acf6;

      // smaller on desktop
      @media (min-width: 768px) {
        font-size: 12px;
        height: 27px;
      }

      &:hover,
      &:focus,
      &:active {
        background-color: #2096f3;
        color: white;
      }

      i {
        margin-right: 3px;
      }
    }

    .manyun-badge-count {
      min-width: 15px;
      height: 15px;
      padding: 0 5px;
      font-size: 10px;
      line-height: 15px;
      background: #f77b74;
      border-radius: 7px;
      box-shadow: 0 0 0 1px white, -1px 1px 0 1px #5d6f7d85;
    }
  }
}
