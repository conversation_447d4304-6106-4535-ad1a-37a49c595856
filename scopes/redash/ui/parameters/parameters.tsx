// @ts-nocheck pending refactor
import { filter, forEach, size } from 'lodash-es';
import React from 'react';

// import location from "@/services/location";
// import { Parameter, createParameter } from '@/services/parameters';
import { ParameterApplyButton } from '@manyun/redash.ui.parameter-apply-button';
import { ParameterValueInput } from '@manyun/redash.ui.parameter-value-input';
import { DragHandle, SortableContainer, SortableElement } from '@manyun/redash.ui.viz-lib';
// import PlainButton from '@/components/PlainButton';
// import EditParameterSettingsDialog from "./EditParameterSettingsDialog";
import { toHuman } from '@manyun/redash.util.to-human';

import './parameters.less';

export type ParametersProps = {
  /**
   * a text to be rendered in the component.
   */
  text: string;
};

function updateUrl(parameters: any[]) {
  // const params = extend({}, location.search);
  // parameters.forEach((param) => {
  //   extend(params, param.toUrlParams());
  // });
  // location.setSearch(params, true);
}

export class Parameters extends React.Component {
  static defaultProps = {
    parameters: [],
    editable: false,
    sortable: false,
    disableUrlUpdate: false,
    onValuesChange: () => {},
    onPendingValuesChange: () => {},
    onParametersEdit: () => {},
    appendSortableToParent: true,
  };

  constructor(props: any) {
    super(props);
    const { parameters } = props;
    this.state = { parameters };
    if (!props.disableUrlUpdate) {
      updateUrl(parameters);
    }
  }

  componentDidUpdate = (prevProps: any) => {
    const { parameters, disableUrlUpdate }: any = this.props;
    const parametersChanged = prevProps.parameters !== parameters;
    const disableUrlUpdateChanged = prevProps.disableUrlUpdate !== disableUrlUpdate;
    if (parametersChanged) {
      this.setState({ parameters });
    }
    if ((parametersChanged || disableUrlUpdateChanged) && !disableUrlUpdate) {
      updateUrl(parameters);
    }
  };

  handleKeyDown = (e: any) => {
    // Cmd/Ctrl/Alt + Enter
    if (e.keyCode === 13 && (e.ctrlKey || e.metaKey || e.altKey)) {
      e.stopPropagation();
      this.applyChanges();
    }
  };

  setPendingValue = (param: any, value: any, isDirty: any) => {
    const { onPendingValuesChange }: any = this.props;
    this.setState(({ parameters }: any) => {
      if (isDirty) {
        param.setPendingValue(value);
      } else {
        param.clearPendingValue();
      }
      onPendingValuesChange();
      return { parameters };
    });
  };

  moveParameter = ({ oldIndex, newIndex }: any) => {
    const { onParametersEdit }: any = this.props;
    if (oldIndex !== newIndex) {
      this.setState(({ parameters }: any) => {
        parameters.splice(newIndex, 0, parameters.splice(oldIndex, 1)[0]);
        onParametersEdit(parameters);
        return { parameters };
      });
    }
  };

  applyChanges = () => {
    const { onValuesChange, disableUrlUpdate }: any = this.props;
    this.setState(({ parameters }: any) => {
      const parametersWithPendingValues = parameters.filter((p: any) => p.hasPendingValue);
      forEach(parameters, p => p.applyPendingValue());
      if (!disableUrlUpdate) {
        updateUrl(parameters);
      }
      onValuesChange(parametersWithPendingValues);
      return { parameters };
    });
  };

  // 先不对参数设置进行编辑的功能
  showParameterSettings = (parameter: any, index: any) => {
    // const { onParametersEdit }: any = this.props;
    // EditParameterSettingsDialog.showModal({ parameter }).onClose((updated) => {
    //   this.setState(({ parameters }) => {
    //     const updatedParameter = extend(parameter, updated);
    //     parameters[index] = createParameter(updatedParameter, updatedParameter.parentQueryId);
    //     onParametersEdit(parameters);
    //     return { parameters };
    //   });
    // });
  };

  renderParameter(param: any, index: any) {
    // const { editable } = this.props;

    return (
      <div key={param.name} className="di-block" data-test={`ParameterName-${param.name}`}>
        <div className="parameter-heading">
          <label>{param.title || toHuman(param.name)}</label>
          {/* 暂时不考虑编辑，因为编辑的交互是出现在右上角按钮中的 */}
          {/* {editable && (
            <PlainButton
              className="btn btn-default btn-xs m-l-5"
              aria-label="Edit"
              onClick={() => this.showParameterSettings(param, index)}
              data-test={`ParameterSettings-${param.name}`}
              type="button"
            >
              <i className="fa fa-cog" aria-hidden="true" />
            </PlainButton>
          )} */}
        </div>
        <ParameterValueInput
          type={param.type}
          value={param.normalizedValue}
          parameter={param}
          enumOptions={param.enumOptions}
          queryId={param.queryId}
          onSelect={(value, isDirty) => this.setPendingValue(param, value, isDirty)}
        />
      </div>
    );
  }

  render() {
    const { parameters }: any = this.state;
    const { sortable, appendSortableToParent }: any = this.props;
    const dirtyParamCount = size(filter(parameters, 'hasPendingValue'));

    return (
      <SortableContainer
        disabled={!sortable}
        axis="xy"
        useDragHandle
        lockToContainerEdges
        helperClass="parameter-dragged"
        helperContainer={(containerEl: any) =>
          appendSortableToParent ? containerEl : document.body
        }
        updateBeforeSortStart={this.onBeforeSortStart}
        onSortEnd={this.moveParameter}
        containerProps={{
          className: 'parameter-container',
          onKeyDown: dirtyParamCount ? this.handleKeyDown : null,
        }}
      >
        {parameters.map((param: any, index: any) => (
          <SortableElement key={param.name} index={index}>
            <div
              className="parameter-block"
              data-editable={sortable || null}
              data-test={`ParameterBlock-${param.name}`}
            >
              {sortable && <DragHandle data-test={`DragHandle-${param.name}`} />}
              {this.renderParameter(param, index)}
            </div>
          </SortableElement>
        ))}
        <ParameterApplyButton onClick={this.applyChanges} paramCount={dirtyParamCount} />
      </SortableContainer>
    );
  }
}
