import React from 'react';

import { PlainButton } from '@manyun/redash.ui.plain-button';

export type WidgetDeleteButtonProps = {
  onClick: any;
};

export function WidgetDeleteButton({ onClick }: WidgetDeleteButtonProps) {
  return (
    <div className="widget-menu-remove">
      <PlainButton
        className="action"
        title="Remove From Dashboard"
        onClick={onClick}
        data-test="WidgetDeleteButton"
        aria-label="Close"
      >
        <i className="zmdi zmdi-close" aria-hidden="true" />
      </PlainButton>
    </div>
  );
}
