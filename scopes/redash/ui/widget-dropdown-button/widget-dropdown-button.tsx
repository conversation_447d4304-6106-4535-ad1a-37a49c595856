import { EllipsisOutlined } from '@ant-design/icons';
import React from 'react';

import { Dropdown } from '@manyun/base-ui.ui.dropdown';
import { Menu } from '@manyun/base-ui.ui.menu';
import { PlainButton } from '@manyun/redash.ui.plain-button';

export type WidgetDropdownButtonProps = {
  /**
   * a text to be rendered in the component.
   */
  extraOptions: any;
  showDeleteOption: any;
  onDelete: any;
};

export function WidgetDropdownButton({
  extraOptions,
  showDeleteOption,
  onDelete,
}: WidgetDropdownButtonProps) {
  const WidgetMenu = (
    <Menu data-test="WidgetDropdownButtonMenu">
      {extraOptions}
      {showDeleteOption && extraOptions && <Menu.Divider />}
      {showDeleteOption && <Menu.Item onClick={onDelete}>Remove from Dashboard</Menu.Item>}
    </Menu>
  );
  return (
    <div className="widget-menu-regular">
      <Dropdown overlay={WidgetMenu} placement="bottomRight" trigger={['click']}>
        <PlainButton
          className="action p-l-15 p-r-15"
          data-test="WidgetDropdownButton"
          aria-label="More options"
        >
          <i className="zmdi zmdi-more-vert" aria-hidden="true" /> <EllipsisOutlined />
        </PlainButton>
      </Dropdown>
    </div>
  );
}
