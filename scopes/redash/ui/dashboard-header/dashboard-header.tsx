import React from 'react';

import { DashboardControl } from '@manyun/redash.ui.dashboard-control';
import { DashboardPageTitle } from '@manyun/redash.ui.dashboard-page-title';
import { clientConfig } from '@manyun/redash.util.client-config';

import './dashboard-header.less';

export type DashboardHeaderProps = {
  /**
   * a text to be rendered in the component.
   */
  // text: string;
  widgets: any;
  dashboardConfiguration: any;
  headerExtra?: any;
};

export function DashboardHeader({ widgets, dashboardConfiguration, headerExtra }: any) {
  // const { editingLayout } = dashboardConfiguration;
  // const DashboardControlComponent = editingLayout ? DashboardEditControl : DashboardControl;

  const DashboardControlComponent = DashboardControl;
  return (
    <div className="dashboard-header">
      <DashboardPageTitle dashboardConfiguration={dashboardConfiguration} />
      <DashboardControlComponent
        widgets={widgets}
        dashboardConfiguration={dashboardConfiguration}
        headerExtra={headerExtra || {}}
        clientConfig={clientConfig}
      />
    </div>
  );
}
