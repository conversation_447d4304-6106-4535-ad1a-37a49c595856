@mobileBreakpoint: ~'(max-width: 767px)';

body #application-root {
  @topMenuHeight: 49px;

  display: flex;
  flex-direction: row;
  justify-content: stretch;
  padding-bottom: 0 !important;
  height: 100vh;

  .application-layout-side-menu {
    height: 100vh;
    position: relative;

    @media @mobileBreakpoint {
      display: none;
    }
  }

  .application-layout-top-menu {
    height: @topMenuHeight;
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    box-sizing: border-box;
    z-index: 1000;

    @media @mobileBreakpoint {
      display: block;
    }
  }

  .application-layout-content {
    display: flex;
    flex-direction: column;
    overflow-y: auto;

    flex: 1 1 auto;
    padding-bottom: 15px;

    @media @mobileBreakpoint {
      margin-top: @topMenuHeight; // compensate for app header fixed position
    }
  }
}

body.fixed-layout #application-root {
  .application-layout-content {
    padding-bottom: 0;
  }
}

body.headless #application-root {
  .application-layout-side-menu,
  .application-layout-top-menu {
    display: none !important;
  }

  .application-layout-content {
    margin-top: 0;
  }
}

// Fixes for proper snapshots in Percy (move vertical scroll to body level
// to capture entire page, otherwise it wll be cut by viewport)
@media only percy {
  body #application-root {
    height: auto;

    .application-layout-side-menu {
      height: auto;
    }

    .application-layout-content {
      overflow: visible;
    }
  }
}

.dashboard-header {
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  position: -webkit-sticky; // required for Safari
  position: sticky;
  background: #fff;
  color: '#eeeeee';
  z-index: 99;
  width: 100%;
  top: 0;
  padding-top: 10px;
  padding-bottom: 10px;
  margin-bottom: 10px;
  padding-left: 10px;
  padding-right: 10px;

  & > div {
    padding: 5px 0;
  }

  .title-with-tags {
    flex: 1 1;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin: -5px 0;

    & > div {
      padding: 5px 0;
    }

    h3 {
      margin: 0;

      @media (max-width: 767px) {
        font-size: 18px;
      }
    }
  }

  @media @mobileBreakpoint {
    & {
      position: static;
    }
  }

  .profile-image {
    width: 16px;
    height: 16px;
    border-radius: 100%;
    margin: 3px 5px 0 5px;
  }

  .tags-control > .label-tag {
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
  }

  &:hover,
  &:focus,
  &:active,
  &:focus-within {
    .tags-control > .label-tag {
      opacity: 1;
    }
  }

  .dashboard-control {
    .icon-button {
      width: 32px;
      padding: 0 0px;
    }

    .save-status {
      vertical-align: middle;
      margin-right: 7px;
      font-size: 12px;
      text-align: left;
      display: inline-block;

      &[data-saving] {
        opacity: 0.6;
        width: 45px;

        &:after {
          content: '';
          animation: saving 2s linear infinite;
        }
      }

      &[data-error] {
        color: #f44336;
      }
    }

    @media (max-width: 515px) {
      flex-basis: 100%;
    }
  }

  @keyframes saving {
    0%,
    100% {
      content: '.';
    }
    33% {
      content: '..';
    }
    66% {
      content: '...';
    }
  }
}
