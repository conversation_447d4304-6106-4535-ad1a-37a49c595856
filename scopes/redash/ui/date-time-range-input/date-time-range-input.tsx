// @ts-nocheck pending refactor
import { isArray } from 'lodash-es';
import React from 'react';

import { DatePicker, DatePickerProps } from '@manyun/base-ui.ui.date-picker';
import { clientConfig } from '@manyun/redash.util.client-config';

export type DateTimeRangeInputProps = {
  defaultValue: any;
  value: any;
  withSeconds: boolean;
  onSelect: any;
  className: string;
} & DatePickerProps;

const DateTimeRangeInput = React.forwardRef(
  (
    {
      defaultValue = null,
      value = undefined,
      withSeconds = false,
      onSelect = () => {},
      className = '',
      ...props
    }: DateTimeRangeInputProps,
    ref
  ) => {
    const format =
      (clientConfig.dateFormat || 'YYYY-MM-DD') + (withSeconds ? ' HH:mm:ss' : ' HH:mm');
    const additionalAttributes: { defaultValue: any[]; value: any[] } = {
      defaultValue: [],
      value: [],
    };
    if (isArray(defaultValue) && defaultValue[0].isValid() && defaultValue[1].isValid()) {
      additionalAttributes.defaultValue = defaultValue;
    }
    if (value === null || (isArray(value) && value[0].isValid() && value[1].isValid())) {
      additionalAttributes.value = value;
    }
    return (
      <DatePicker.RangePicker
        ref={ref}
        className={className}
        {...additionalAttributes}
        format={format}
        onChange={onSelect}
        {...props}
      />
    );
  }
);
export { DateTimeRangeInput };
