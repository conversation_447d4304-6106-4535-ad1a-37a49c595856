// @ts-nocheck pending refactor
import classNames from 'classnames';
import { includes } from 'lodash-es';
import moment from 'moment';
import React from 'react';

import { isDynamicDate } from '@manyun/redash.entity.date-parameter';
import { DateInput } from '@manyun/redash.ui.date-input';
import { DateTimeInput } from '@manyun/redash.ui.date-time-input';
import { DynamicButton } from '@manyun/redash.ui.dynamic-button';

import './dynamic-date-picker.less';

class DynamicDatePicker extends React.Component {
  static defaultProps = {
    type: '',
    className: '',
    value: null,
    parameter: null,
    dynamicButtonOptions: {
      options: [],
    },
    onSelect: () => {},
  };

  constructor(props) {
    super(props);
    this.dateComponentRef = React.createRef();
  }

  onDynamicValueSelect = dynamicValue => {
    const { onSelect, parameter } = this.props;
    if (dynamicValue === 'static') {
      const parameterValue = parameter.getExecutionValue();
      if (parameterValue) {
        onSelect(moment(parameterValue));
      } else {
        onSelect(null);
      }
    } else {
      onSelect(dynamicValue.value);
    }
    // give focus to the DatePicker to get keyboard shortcuts to work
    this.dateComponentRef.current.focus();
  };

  render() {
    const { type, value, className, dateOptions, dynamicButtonOptions, onSelect } = this.props;
    const hasDynamicValue = isDynamicDate(value);
    const isDateTime = includes(type, 'datetime');

    const additionalAttributes = {};

    let DateComponent = DateInput;
    if (isDateTime) {
      DateComponent = DateTimeInput;
      if (includes(type, 'with-seconds')) {
        additionalAttributes.withSeconds = true;
      }
    }

    if (moment.isMoment(value) || value === null) {
      additionalAttributes.value = value;
    }

    if (hasDynamicValue) {
      const dynamicDate = value;
      additionalAttributes.placeholder = dynamicDate && dynamicDate.name;
      additionalAttributes.value = null;
    }

    return (
      <div className={classNames('date-parameter', className)}>
        <DateComponent
          {...dateOptions}
          ref={this.dateComponentRef}
          className={classNames('redash-datepicker', type, { 'dynamic-value': hasDynamicValue })}
          onSelect={onSelect}
          suffixIcon={null}
          {...additionalAttributes}
        />
        <DynamicButton
          options={dynamicButtonOptions.options}
          staticValueLabel={dynamicButtonOptions.staticValueLabel}
          selectedDynamicValue={hasDynamicValue ? value : null}
          enabled={hasDynamicValue}
          onSelect={this.onDynamicValueSelect}
        />
      </div>
    );
  }
}

export { DynamicDatePicker };
