@input-color: #595959;

.date-range-parameter,
.date-parameter {
  position: relative;
}

.redash-datepicker {
  padding-right: 35px !important;

  &.date-range {
    width: 294px;
  }
  &.datetime-range {
    width: 352px;
  }
  &.datetime-range-with-seconds {
    width: 382px;
  }
  &.dynamic-value {
    width: 195px;
  }

  &.manyun-picker-range .manyun-picker-clear {
    right: 35px !important;
    background: transparent;
  }

  &.date-range-input {
    transition: width 100ms ease-in-out;
  }

  &.dynamic-value {
    & ::placeholder {
      color: @input-color !important;
    }

    &.date-range-input {
      .manyun-picker-active-bar {
        opacity: 0;
      }

      .manyun-picker-separator,
      .manyun-picker-range-separator {
        display: none;
      }

      .manyun-picker-input:not(:first-child) {
        width: 0;
      }
    }
  }
}
