// @ts-nocheck pending refactor
import { every, get, includes, indexOf, isArray, map, some, toNumber } from 'lodash-es';
import moment from 'moment';
import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import { formatColumnValue } from '@manyun/redash.ui.viz-lib';

const ALL_VALUES = '###Redash::Filters::SelectAll###';
const NONE_VALUES = '###Redash::Filters::Clear###';
export type FiltersProps = {
  /**
   * a text to be rendered in the component.
   */
  filters: any;
  onChange: any;
};

function createFilterChangeHandler(filters: any, onChange: any) {
  return (filter: { values: any[]; multiple: any; name: any }, values: any) => {
    if (isArray(values)) {
      values = map(values, value => filter.values[toNumber(value.key)] || value.key);
    } else {
      const _values = filter.values[toNumber(values.key)];
      values = _values !== undefined ? _values : values.key;
    }

    if (filter.multiple && includes(values, ALL_VALUES)) {
      values = [...filter.values];
    }
    if (filter.multiple && includes(values, NONE_VALUES)) {
      values = [];
    }
    filters = map(filters, f => (f.name === filter.name ? { ...filter, current: values } : f));
    onChange(filters);
  };
}
export function Filters({ filters, onChange }: any) {
  if (filters.length === 0) {
    return null;
  }

  onChange = createFilterChangeHandler(filters, onChange);

  return (
    <div className="filters-wrapper" data-test="Filters">
      <div className="container bg-white">
        <div className="row">
          {map(filters, filter => {
            const options = map(filter.values, (value, index) => (
              <Select.Option key={index} value={value}>
                {formatColumnValue(value, get(filter, 'column.type'))}
              </Select.Option>
            ));

            return (
              <div
                key={filter.name}
                className="col-sm-6 p-l-0 filter-container"
                data-test={`FilterName-${filter.name}`}
              >
                <label>{filter.friendlyName}</label>
                {options.length === 0 && <Select className="w-100" disabled value="No values" />}
                {options.length > 0 && (
                  <Select
                    labelInValue
                    className="w-100"
                    mode={filter.multiple ? 'multiple' : 'default'}
                    value={
                      isArray(filter.current)
                        ? map(filter.current, value => ({
                            key: `${indexOf(filter.values, value)}`,
                            label: formatColumnValue(value),
                          }))
                        : {
                            key: `${indexOf(filter.values, filter.current)}`,
                            label: formatColumnValue(filter.current),
                          }
                    }
                    allowClear={filter.multiple}
                    optionFilterProp="children"
                    showSearch
                    maxTagCount={3}
                    maxTagTextLength={10}
                    maxTagPlaceholder={(num: string | any[]) => `+${num.length} more`}
                    onChange={(values: any) => onChange(filter, values)}
                  >
                    {!filter.multiple && options}
                    {filter.multiple && [
                      <Select.Option key={NONE_VALUES} data-test="ClearOption">
                        <i className="fa fa-square-o m-r-5" aria-hidden="true" />
                        Clear
                      </Select.Option>,
                      <Select.Option key={ALL_VALUES} data-test="SelectAllOption">
                        <i className="fa fa-check-square-o m-r-5" aria-hidden="true" />
                        Select All
                      </Select.Option>,
                      <Select.OptGroup key="Values" title="Values">
                        {options}
                      </Select.OptGroup>,
                    ]}
                  </Select>
                )}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
