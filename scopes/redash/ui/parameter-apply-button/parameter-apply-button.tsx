// @ts-nocheck pending refactor
import React from 'react';

import { Badge } from '@manyun/base-ui.ui.badge';
import { Button } from '@manyun/base-ui.ui.button';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';

export type ParameterApplyButtonProps = {
  /**
   * a text to be rendered in the component.
   */
  paramCount: any;
  onClick: any;
};

export function ParameterApplyButton({ paramCount, onClick }: any) {
  // show spinner when count is empty so the fade out is consistent
  const icon = !paramCount ? (
    <span role="status" aria-live="polite" aria-relevant="additions removals">
      <i className="fa fa-spinner fa-pulse" aria-hidden="true" />
      <span className="sr-only">Loading...</span>
    </span>
  ) : (
    <i className="fa fa-check" aria-hidden="true" />
  );

  return (
    <div
      className="parameter-apply-button"
      data-show={!!paramCount}
      data-test="ParameterApplyButton"
    >
      <Badge count={paramCount}>
        <Tooltip title={`确认后进行重新查询`}>
          <span>
            <Button aria-label="restart query button" onClick={onClick}>
              {icon} 重新查询
            </Button>
          </span>
        </Tooltip>
      </Badge>
    </div>
  );
}
