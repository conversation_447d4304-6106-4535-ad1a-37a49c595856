// @ts-nocheck pending refactor
import React from 'react';

import { Empty } from '@manyun/base-ui.ui.empty';
import { Menu } from '@manyun/base-ui.ui.menu';
import { Spin } from '@manyun/base-ui.ui.spin';
import { ExpandedWidgetDialog } from '@manyun/redash.ui.expanded-widget-dialog';
import { VisualizationRenderer } from '@manyun/redash.ui.visualization-renderer';
import { VisualizationWidgetHeader } from '@manyun/redash.ui.visualization-widget-header';
import { Widget } from '@manyun/redash.ui.widget';
import '@manyun/redash.ui.widget/widget.less';

class VisualizationWidget extends React.Component {
  static defaultProps = {
    filters: [],
    isPublic: false,
    isLoading: false,
    canEdit: false,
    isEditing: false,
    onLoad: () => {},
    onRefresh: () => {},
    onDelete: () => {},
    onParameterMappingsChange: () => {},
  };

  constructor(props: any) {
    super(props);

    this.state = {
      localParameters: props.widget.getLocalParameters(),

      modalVisible: false,
    };
  }
  componentDidMount() {
    const { onRefresh } = this.props;
    onRefresh();
  }
  onClose = () => {
    //关闭 全屏展示的弹窗组件
    this.setState({ modalVisible: false });
  };
  expandWidget = () => {
    // 全屏展示的弹窗组件

    this.setState({ modalVisible: true });
  };

  render() {
    const { widget, isPublic, onRefresh, filters }: any = this.props;

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { localParameters } = this.state;
    const widgetStatus = widget.getQuery().getStatus();
    return (
      <>
        <Widget
          widget={widget}
          className="widget-visualization"
          menuOptions={[
            <Menu.Item key="download_csv" disabled={false}>
              "Download as CSV File"
            </Menu.Item>,
            <Menu.Item key="download_tsv" disabled={false}>
              "Download as TSV File"
            </Menu.Item>,
            <Menu.Item key="download_excel" disabled={false}>
              "Download as Excel File"
            </Menu.Item>,
          ]}
          header={
            <VisualizationWidgetHeader
              widget={widget}
              parameters={localParameters}
              onParametersUpdate={onRefresh}
              onParametersEdit={() => {}}
              isPublic={isPublic}
              onRefresh={onRefresh}
              onExpand={this.expandWidget}
            />
          }
          footer={<></>}
          tileProps={{ 'data-refreshing': false }}
        >
          <div className="body-row-auto scrollbox">
            <Spin spinning={widgetStatus === 'loading'}>
              {widgetStatus === 'done' && (
                <VisualizationRenderer
                  visualization={widget.visualization}
                  queryResult={widget.getQueryResult()}
                  filters={filters}
                  context="widget"
                />
              )}
              {widgetStatus === 'error' && (
                <div className="alert alert-danger m-5">
                  <Empty description="暂无数据" />
                </div>
              )}
            </Spin>
          </div>

          {this.state.modalVisible && (
            <ExpandedWidgetDialog
              widget={widget}
              filters={[]}
              visible={this.state.modalVisible}
              onClose={this.onClose}
            />
          )}
        </Widget>
      </>
    );
  }
}

export { VisualizationWidget };
