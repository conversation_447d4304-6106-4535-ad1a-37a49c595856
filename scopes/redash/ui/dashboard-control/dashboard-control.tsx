/* eslint-disable @typescript-eslint/no-explicit-any */
import { ExpandOutlined } from '@ant-design/icons';
import React, { useState } from 'react';
import useDeepCompareEffect from 'use-deep-compare-effect';

import { Button } from '@manyun/base-ui.ui.button';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { useAuthorized } from '@manyun/iam.hook.use-authorized';
import { DownloadPdf } from '@manyun/redash.ui.download-pdf';
import { RefreshButton } from '@manyun/redash.ui.refresh-button';
import { SendReportByEmailModal } from '@manyun/redash.ui.send-report-by-email-modal';
import { buttonType } from '@manyun/redash.util.button-type';

export type DashboardControlProps = {
  widgets: any;
  dashboardConfiguration: any;
  headerExtra?: any;
  clientConfig: any;
};

export function DashboardControl({
  widgets,
  dashboardConfiguration,
  headerExtra,
  clientConfig,
}: any) {
  const { dashboard, fullscreen, toggleFullscreen } = dashboardConfiguration;
  const [space, date] = dashboard.getParametersDefs();
  const [alreadyLoaded, setAlreadyLoaded] = useState<boolean>(false);
  const [, { checkCode }] = useAuthorized();

  useDeepCompareEffect(() => {
    const whetherAllWidgetsLoaded = () => {
      if (widgets?.length) {
        return !widgets?.map((widget: any) => widget.getQuery()?.getStatus())?.includes('loading');
      }
      return false;
    };
    setAlreadyLoaded(whetherAllWidgetsLoaded());
  }, [widgets?.map((widget: any) => widget.getQuery()?.getStatus())]);
  return (
    <div className="dashboard-control">
      <span className="hidden-print">
        <RefreshButton
          clientConfig={clientConfig}
          dashboardConfiguration={dashboardConfiguration}
        />
        <Tooltip className="hidden-xs" title="点击切换全屏展示">
          <Button
            type={buttonType(fullscreen)}
            className="icon-button m-l-5"
            aria-label="Toggle fullscreen display"
            onClick={toggleFullscreen}
          >
            <i className="zmdi zmdi-fullscreen" aria-hidden="true" />
            <ExpandOutlined />
          </Button>
        </Tooltip>
        {/* slug 用于区分是否是周报 还是 日报 */}
        {(dashboard.slug === 'daily' || dashboard.slug === 'weekly') && (
          <>
            {checkCode('element_send-report-email') && (
              <SendReportByEmailModal
                spaceGuid={space?.value}
                slug={dashboard.slug}
                pdfName={dashboard.name}
                isAlreadyLoaded={alreadyLoaded}
                date={date?.value}
              />
            )}
            {checkCode('element_download-report-pdf') && <DownloadPdf pdfName={dashboard.name} />}
          </>
        )}
      </span>
    </div>
  );
}
