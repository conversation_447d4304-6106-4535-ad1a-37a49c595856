import { includes } from 'lodash-es';
import PropTypes from 'prop-types';
import React from 'react';

import { getDynamicDateRangeFromString } from '@manyun/redash.entity.date-range-parameter';
import { DynamicDateRangePicker } from '@manyun/redash.ui.dynamic-date-range-picker';

const DYNAMIC_DATE_OPTIONS = [
  {
    name: '本周',
    value: getDynamicDateRangeFromString('d_this_week'),
    label: () =>
      getDynamicDateRangeFromString('d_this_week').value()[0].format('MMM D') +
      ' - ' +
      getDynamicDateRangeFromString('d_this_week').value()[1].format('MMM D'),
  },
  {
    name: '本月',
    value: getDynamicDateRangeFromString('d_this_month'),
    label: () => getDynamicDateRangeFromString('d_this_month').value()[0].format('MMMM'),
  },
  {
    name: '今年',
    value: getDynamicDateRangeFromString('d_this_year'),
    label: () => getDynamicDateRangeFromString('d_this_year').value()[0].format('YYYY'),
  },
  {
    name: '上周',
    value: getDynamicDateRangeFromString('d_last_week'),
    label: () =>
      getDynamicDateRangeFromString('d_last_week').value()[0].format('MMM D') +
      ' - ' +
      getDynamicDateRangeFromString('d_last_week').value()[1].format('MMM D'),
  },
  {
    name: '上月',
    value: getDynamicDateRangeFromString('d_last_month'),
    label: () => getDynamicDateRangeFromString('d_last_month').value()[0].format('MMMM'),
  },
  {
    name: '去年',
    value: getDynamicDateRangeFromString('d_last_year'),
    label: () => getDynamicDateRangeFromString('d_last_year').value()[0].format('YYYY'),
  },
  {
    name: '过去7天',
    value: getDynamicDateRangeFromString('d_last_7_days'),
    label: () =>
      getDynamicDateRangeFromString('d_last_7_days').value()[0].format('MMM D') + ' - 今天',
  },
  {
    name: '过去14天',
    value: getDynamicDateRangeFromString('d_last_14_days'),
    label: () =>
      getDynamicDateRangeFromString('d_last_14_days').value()[0].format('MMM D') + ' - 今天',
  },
  {
    name: '过去1个月',
    value: getDynamicDateRangeFromString('d_last_30_days'),
    label: () =>
      getDynamicDateRangeFromString('d_last_30_days').value()[0].format('MMM D') + ' - 今天',
  },
  {
    name: '过去2个月',
    value: getDynamicDateRangeFromString('d_last_60_days'),
    label: () =>
      getDynamicDateRangeFromString('d_last_60_days').value()[0].format('MMM D') + ' - 今天',
  },
  {
    name: '过去3个月',
    value: getDynamicDateRangeFromString('d_last_90_days'),
    label: () =>
      getDynamicDateRangeFromString('d_last_90_days').value()[0].format('MMM D') + ' - 今天',
  },
  {
    name: '过去1年',
    value: getDynamicDateRangeFromString('d_last_12_months'),
    label: null,
  },
];

const DYNAMIC_DATETIME_OPTIONS = [
  {
    name: '今天',
    value: getDynamicDateRangeFromString('d_today'),
    label: () => getDynamicDateRangeFromString('d_today').value()[0].format('MMM D'),
  },
  {
    name: '昨天',
    value: getDynamicDateRangeFromString('d_yesterday'),
    label: () => getDynamicDateRangeFromString('d_yesterday').value()[0].format('MMM D'),
  },
  ...DYNAMIC_DATE_OPTIONS,
];

function DateRangeParameter(props: any) {
  const options = includes(props.type, 'datetime-range')
    ? DYNAMIC_DATETIME_OPTIONS
    : DYNAMIC_DATE_OPTIONS;
  return <DynamicDateRangePicker {...props} dynamicButtonOptions={{ options }} />;
}

DateRangeParameter.propTypes = {
  type: PropTypes.string,
  className: PropTypes.string,
  value: PropTypes.any, // eslint-disable-line react/forbid-prop-types
  parameter: PropTypes.any, // eslint-disable-line react/forbid-prop-types
  onSelect: PropTypes.func,
};

DateRangeParameter.defaultProps = {
  type: '',
  className: '',
  value: null,
  parameter: null,
  onSelect: () => {},
};

export { DateRangeParameter };
