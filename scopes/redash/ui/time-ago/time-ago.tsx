// @ts-nocheck pending refactor
import { isNil } from 'lodash-es';
import moment from 'moment';
import React, { useEffect, useMemo, useState } from 'react';

import { Tooltip } from '@manyun/base-ui.ui.tooltip';

export type TimeAgoProps = {
  /**
   * a text to be rendered in the component.
   */
  date: any;
  placeholder?: string;
  autoUpdate?: boolean;
  variation?: string;
};

function toMoment(value) {
  value = !isNil(value) ? moment(value) : null;
  return value && value.isValid() ? value : null;
}

export function TimeAgo({ date, placeholder = '', autoUpdate = true, variation = '' }) {
  const startDate = toMoment(date);
  const [value, setValue] = useState(null);
  const title = useMemo(
    () => (startDate ? startDate.format('YYYY-MM-DD HH:mm:ss') : null),
    [startDate]
  );

  useEffect(() => {
    function update() {
      setValue(startDate ? startDate.fromNow() : placeholder);
    }
    update();

    if (autoUpdate) {
      const timer = setInterval(update, 30 * 1000);
      return () => clearInterval(timer);
    }
  }, [autoUpdate, startDate, placeholder]);

  if (variation === 'timeAgoInTooltip') {
    return (
      <Tooltip title={value}>
        <span data-test="TimeAgo">{title}</span>
      </Tooltip>
    );
  }
  return (
    <Tooltip title={title}>
      <span data-test="TimeAgo">{value}</span>
    </Tooltip>
  );
}
