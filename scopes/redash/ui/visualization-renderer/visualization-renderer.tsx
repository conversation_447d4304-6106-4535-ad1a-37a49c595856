import React, { useMemo } from 'react';

import { useQueryResultData } from '@manyun/redash.hook.use-query-result-data';
import { Renderer } from '@manyun/redash.ui.viz-lib';
import { filterData } from '@manyun/redash.util.filter-data';

export type VisualizationRendererProps = {
  /**
   * a text to be rendered in the component.
   */
  queryResult: object;
  visualization: any;
  context: string;
};

export function VisualizationRenderer(props: any) {
  // 源代码
  const data = useQueryResultData(props.queryResult);

  // 先忽略筛选
  // const [filters, setFilters] = useState(() => combineFilters(data.filters, props.filters)); // lazy initialization
  // const filtersRef = useRef();
  // filtersRef.current = filters;

  // 先忽略筛选
  // const handleFiltersChange = useImmutableCallback((newFilters) => {
  //   if (!areFiltersEqual(newFilters, filters)) {
  //     setFilters(newFilters);
  //     props.onFiltersChange(newFilters);
  //   }
  // });

  // 先忽略筛选
  // Reset local filters when query results updated
  // useEffect(() => {
  //   handleFiltersChange(combineFilters(data.filters, props.filters));
  // }, [data.filters, props.filters, handleFiltersChange]);

  // Update local filters when global filters changed.
  // For correct behavior need to watch only `props.filters` here,
  // therefore using ref to access current local filters
  // useEffect(() => {
  //   handleFiltersChange(combineFilters(filtersRef.current, props.filters));
  // }, [props.filters, handleFiltersChange]);

  const filteredData = useMemo(
    () => ({
      columns: data.columns,
      rows: filterData(data.rows, []),
    }),
    [data]
  );
  // 包含filter的源码如下
  // const filteredData = useMemo(
  //   () => ({
  //     columns: data.columns,
  //     rows: filterData(data.rows, filters),
  //   }),
  //   [data, filters]
  // );

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { showFilters, visualization } = props;

  let options = { ...visualization.options };

  // define pagination size based on context for Table visualization
  if (visualization.type === 'TABLE') {
    options.paginationSize = props.context === 'widget' ? 'small' : 'default';
  }

  return (
    <Renderer
      key={`visualization${visualization.id}`}
      type={visualization.type}
      options={options}
      data={filteredData}
      visualizationName={visualization.name}
      // addonBefore={showFilters && <Filters filters={filters} onChange={handleFiltersChange} />}
    />
  );
}
