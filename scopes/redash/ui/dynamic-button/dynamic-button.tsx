import {
  ArrowLeftOutlined as Arrow<PERSON>eftOutlinedIcon,
  ThunderboltOutlined as ThunderboltOutlinedIcon,
  ThunderboltTwoTone as ThunderboltTwoToneIcon,
} from '@ant-design/icons';
import { findIndex, get, isFunction } from 'lodash-es';
import React, { useRef } from 'react';

import { Dropdown } from '@manyun/base-ui.ui.dropdown';
import { Menu } from '@manyun/base-ui.ui.menu';

import './dynamic-button.less';

// const { Text } = Typography;

function DynamicButton({
  options,
  selectedDynamicValue,
  onSelect,
  enabled,
  staticValueLabel,
}: any) {
  const menu = (
    <Menu
      className="dynamic-menu"
      onClick={({ key }) => onSelect(get(options, key, 'static'))}
      selectedKeys={[`${findIndex(options, { value: selectedDynamicValue })}`]}
      data-test="DynamicButtonMenu"
    >
      {options.map((option: any, index: any) => (
        // eslint-disable-next-line react/no-array-index-key
        <Menu.Item key={index}>
          {option.name}
          {option.label && <em>{isFunction(option.label) ? option.label() : option.label}</em>}
        </Menu.Item>
      ))}
      {enabled && <Menu.Divider />}
      {enabled && (
        <Menu.Item>
          <ArrowLeftOutlinedIcon />
          {staticValueLabel}
        </Menu.Item>
      )}
    </Menu>
  );

  const containerRef = useRef(null);

  return (
    <div ref={containerRef} className="dynamic-button">
      <div role="presentation" onClick={e => e.stopPropagation()}>
        <Dropdown.Button
          overlay={menu}
          placement="bottomRight"
          trigger={['click']}
          icon={
            enabled ? (
              <ThunderboltTwoToneIcon className="dynamic-icon" />
            ) : (
              <ThunderboltOutlinedIcon className="dynamic-icon" />
            )
          }
          // @ts-ignore types
          getPopupContainer={() => containerRef.current}
          data-test="DynamicButton"
        />
      </div>
    </div>
  );
}

export { DynamicButton };
