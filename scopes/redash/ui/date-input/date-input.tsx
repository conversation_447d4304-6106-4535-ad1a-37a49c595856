// @ts-nocheck pending refactor
import React from 'react';

import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { clientConfig } from '@manyun/redash.util.client-config';

const DateInput = React.forwardRef(
  ({ defaultValue, value, onSelect, className, ...props }, ref) => {
    const format = clientConfig.dateFormat || 'YYYY-MM-DD';
    const additionalAttributes = {};
    if (defaultValue && defaultValue.isValid()) {
      additionalAttributes.defaultValue = defaultValue;
    }
    if (value === null || (value && value.isValid())) {
      additionalAttributes.value = value;
    }
    return (
      <DatePicker
        aria-label="date picker"
        ref={ref}
        className={className}
        {...additionalAttributes}
        format={format}
        placeholder="点击选择日期"
        onChange={onSelect}
        {...props}
      />
    );
  }
);

export { DateInput };
