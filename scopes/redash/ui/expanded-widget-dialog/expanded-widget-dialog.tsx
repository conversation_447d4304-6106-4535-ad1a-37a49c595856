// @ts-nocheck pending refactor
import React from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Modal } from '@manyun/base-ui.ui.modal';

import { VisualizationName } from '@manyun/redash.ui.visualization-name';
// import { FiltersType } from "@/components/Filters";
import { VisualizationRenderer } from '@manyun/redash.ui.visualization-renderer';

export type ExpandedWidgetDialogProps = {
  /**
   * a text to be rendered in the component.
   */
  widget: any;
  filters: any;
  visible: boolean;
  onClose: any;
};
function ExpandedWidgetDialog({ widget, filters, visible, onClose }: any) {
  return (
    <Modal
      onCancel={onClose}
      visible
      title={
        <>
          <VisualizationName
            visualization={widget.visualization}
            queryName={widget.getQuery().name}
          />
        </>
      }
      width="95%"
      footer={<Button onClick={onClose}>关闭</Button>}
    >
      <VisualizationRenderer
        visualization={widget.visualization}
        queryResult={widget.getQueryResult()}
        filters={[]}
        context="widget"
      />
    </Modal>
  );
}

export { ExpandedWidgetDialog };
