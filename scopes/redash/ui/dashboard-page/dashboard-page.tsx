// @ts-nocheck pending refactor
import React, { useEffect, useState } from 'react';

import { Result } from '@manyun/base-ui.ui.result';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Dashboard } from '@manyun/redash.entity.dashboard';
import { DashboardComponent } from '@manyun/redash.ui.dashboard-component';

import './dashboard-page.less';

export type DashboardPageProps = {
  dashboardId: string;
};

export function DashboardPage({ dashboardId }: DashboardPageProps) {
  const [dashboard, setDashboard] = useState(null);
  // const handleError = useImmutableCallback(onError);
  const [loading, setLoading] = useState(false);
  useEffect(() => {
    (async function () {
      setLoading(true);
      const res = await Dashboard.get({ id: dashboardId });
      if ((res && res.widgets) || (res && res.name)) {
        setDashboard(res);
      }
      setLoading(false);
    })();
  }, [dashboardId]);

  return (
    <div className="dashboard-page">
      <Spin spinning={loading}>{dashboard && <DashboardComponent dashboard={dashboard} />}</Spin>
      {!dashboard && !loading && <Result status="404" title="404" subTitle="暂无报表信息" />}
    </div>
  );
}
