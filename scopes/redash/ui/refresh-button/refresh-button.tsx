// @ts-nocheck pending refactor
import { ReloadOutlined } from '@ant-design/icons';
import React from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { buttonType } from '@manyun/redash.util.button-type';
// import { clientConfig } from "@my-scope/services/auth";
import { durationHumanize } from '@manyun/redash.util.duration-humanize';

export type RefreshButtonProps = {
  /**
   * a text to be rendered in the component.
   */
  // text: string;
  dashboardConfiguration: any;
  clientConfig: any;
};

export function RefreshButton({ dashboardConfiguration, clientConfig }: RefreshButtonProps) {
  const { refreshRate, setRefreshRate, disableRefreshRate, refreshDashboard } =
    dashboardConfiguration;
  // 之后可能会封装的函数,判断携带的该属性是否是Array
  // const allowedIntervals = policy.getDashboardRefreshIntervals();
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const allowedIntervals = clientConfig.dashboardRefreshIntervals;
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const refreshRateOptions = clientConfig.dashboardRefreshIntervals;

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const onRefreshRateSelected = ({ key }: any) => {
    const parsedRefreshRate = parseFloat(key);

    if (parsedRefreshRate) {
      setRefreshRate(parsedRefreshRate);
      refreshDashboard();
    } else {
      disableRefreshRate();
    }
  };
  return (
    <Space size={0}>
      <Tooltip title={refreshRate ? `每${durationHumanize(refreshRate)}后，自动进行刷新` : null}>
        <Button
          aria-label="Refresh"
          type={buttonType(refreshRate)}
          onClick={() => refreshDashboard()}
        >
          {/* <i
            className={cx('zmdi zmdi-refresh m-r-5', {
              'zmdi-hc-spin': refreshing,
            })}
            aria-hidden="true"
          /> */}
          {refreshRate ? durationHumanize(refreshRate) : '刷新'}
          <ReloadOutlined />
        </Button>
      </Tooltip>
      {/* <Dropdown
        trigger={['click']}
        placement="bottomRight"
        overlay={
          <Menu onClick={onRefreshRateSelected} selectedKeys={[`${refreshRate}`]}>
     
            {refreshRateOptions.map((option: any) => (
              <Menu.Item key={`${option}`} disabled={!includes(allowedIntervals, option)}>
                {durationHumanize(option)}
              </Menu.Item>
            ))}
            {refreshRate && <Menu.Item key={null}>取消自动刷新</Menu.Item>}
          </Menu>
        }
      >
        <Button className="icon-button hidden-xs" type={buttonType(refreshRate)}>
          <CaretDownOutlined />
        </Button>
      </Dropdown> */}
    </Space>
  );
}
