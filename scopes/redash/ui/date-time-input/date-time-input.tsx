// @ts-nocheck pending refactor
import React from 'react';

import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { clientConfig } from '@manyun/redash.util.client-config';

const DateTimeInput = React.forwardRef(
  ({ defaultValue, value, withSeconds, onSelect, className, ...props }: any, ref) => {
    const format =
      (clientConfig.dateFormat || 'YYYY-MM-DD') + (withSeconds ? ' HH:mm:ss' : ' HH:mm');
    const additionalAttributes = { defaultValue: '', value: '' };
    if (defaultValue && defaultValue.isValid()) {
      additionalAttributes.defaultValue = defaultValue;
    }
    if (value === null || (value && value.isValid())) {
      additionalAttributes.value = value;
    }
    return (
      <DatePicker
        aria-label="date picker"
        ref={ref}
        className={className}
        showTime
        {...additionalAttributes}
        format={format}
        placeholder="选择日期和时间"
        onChange={onSelect}
        {...props}
      />
    );
  }
);

export { DateTimeInput };
