import React from 'react';

export type DashboardPageTitleProps = {
  /**
   * a text to be rendered in the component.
   */
  // text: string;
  dashboardConfiguration: any;
};

export function DashboardPageTitle({ dashboardConfiguration }: any) {
  // const { dashboard, canEditDashboard, updateDashboard, editingLayout } = dashboardConfiguration;
  const { dashboard } = dashboardConfiguration;
  return (
    <div className="title-with-tags">
      <div className="page-title">
        {/* <FavoritesControl item={dashboard} /> */}
        <h3>
          {/* <EditInPlace
            isEditable={editingLayout}
            onDone={(name) => updateDashboard({ name })}
            value={dashboard.name}
            ignoreBlanks
          /> */}
          <span role="presentation">{dashboard.name}</span>
        </h3>
        {/* <Tooltip title={dashboard.user.name} placement="bottom">
          <img
            src={dashboard.user.profile_image_url}
            className="profile-image"
            alt={dashboard.user.name}
          />
        </Tooltip> */}
      </div>
    </div>
  );
}
