// @ts-nocheck pending refactor
import { isArray } from 'lodash-es';
import React from 'react';

import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { clientConfig } from '@manyun/redash.util.client-config';

export type DateRangeInputProps = {
  defaultValue: any;
  value: any;
  onSelect: any;
  className: string;
};

const DateRangeInput = React.forwardRef(
  (
    {
      defaultValue = null,
      value = undefined,
      onSelect = () => {},
      className = '',
      ...props
    }: DateRangeInputProps,
    ref
  ) => {
    const format = clientConfig.dateFormat || 'YYYY-MM-DD';
    const additionalAttributes: { defaultValue: any[]; value: any[] } = {
      defaultValue: [],
      value: [],
    };
    if (isArray(defaultValue) && defaultValue[0].isValid() && defaultValue[1].isValid()) {
      additionalAttributes.defaultValue = defaultValue;
    }
    if (value === null || (isArray(value) && value[0].isValid() && value[1].isValid())) {
      additionalAttributes.value = value;
    }
    return (
      <DatePicker.RangePicker
        ref={ref}
        className={className}
        {...additionalAttributes}
        format={format}
        onChange={onSelect}
        {...props}
      />
    );
  }
);
export { DateRangeInput };
