// @ts-nocheck pending refactor
import dayjs from 'dayjs';
import html2canvas from 'html2canvas';
import { jsPDF } from 'jspdf';
import React, { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import type { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { uploadFullFile } from '@manyun/dc-brain.service.upload-full-file';
import { McUpload as Upload } from '@manyun/dc-brain.ui.upload';
import type { MixedUploadFile } from '@manyun/dc-brain.ui.upload';
import { UserSelect } from '@manyun/iam.ui.user-select';
import { sendRedashMail } from '@manyun/redash.service.send-redash-mail';
import type { MailToUser } from '@manyun/redash.service.send-redash-mail';
import { fetchIdc } from '@manyun/resource-hub.service.fetch-idc';

export type SendReportByEmailModalProps = {
  spaceGuid: string;
  // 用于指明是日报还是周报
  slug: string;
  pdfName: string;
  isAlreadyLoaded: boolean;
  date: string | { start: string; end: string };
};

export function SendReportByEmailModal({
  spaceGuid,
  slug,
  pdfName,
  isAlreadyLoaded,
  date,
}: SendReportByEmailModalProps) {
  const { pathname } = useLocation();
  const [visible, setVisible] = useState<boolean>(false);
  const [form] = Form.useForm();
  const [fileList, setFileList] = useState<MixedUploadFile[]>([]);
  const [userList, setUserList] = useState<MailToUser[]>([]);
  const [modalFlag, setModalFlag] = useState<boolean>(false);
  const [idcName, setIdcName] = useState<string | undefined>('');
  const onFinish = async () => {
    const values = await form.validateFields();
    const { error } = await sendRedashMail({
      id: generateEmailId(),
      code: 'FORM_REPORT',
      channels: ['EMAIL'],
      users: userList,
      params: { content: values.content },
      idc: spaceGuid?.split('.')[0],
      blocks: [spaceGuid?.split('.')[1]],
      fileList: fileList.map(file => file.patialPath),
      title: generateReportTitle(),
    });
    if (error) {
      message.error(error.message);
      return;
    }
    message.success('发送成功！');
    setVisible(false);
  };
  const generateEmailId = () => {
    return `FORM_REPORT${dayjs().unix()}${Math.trunc(Math.random() * 10000)}`;
  };
  const generateReportTitle = () => {
    const block = `${spaceGuid.split('.')[1]}楼`;
    return `【DCBase】${idcName}${block}${generateReportType(slug)}（${generateDateByCase(
      'titleCase'
    )}）`;
  };
  const generateReportContent = (spaceName: string, spaceGuid: string) => {
    const block = `${spaceGuid.split('.')[1]}楼`;
    return `${idcName}${block}${generateReportType(slug)}已经上传附件。${generateReportLink()}`;
  };
  const generateReportLink = () => {
    const urlPrefix = `${window.location.protocol}//${window.location.host}`;
    return `平台${generateReportType(
      slug
    )}查看入口：${urlPrefix}${pathname}?p_blockGuid=${spaceGuid}&${generateDateByCase('linkDate')}`;
  };
  const generateReportType = (slug: string) => {
    return slug === 'daily' ? '日报' : '周报';
  };
  const generateDateByCase = (dateCase: string) => {
    if (dateCase === 'linkDate') {
      if (typeof date === 'object' && date) {
        return `p_week.start=${date?.start}&p_week.end=${date?.end}`;
      }
      return generateReportType(slug) === '周报' ? `p_week=${date}` : `p_date=${date}`;
    } else {
      if (typeof date === 'object' && date) {
        return `${date?.start}~${date?.end}`;
      }
      return date;
    }
  };

  const isModalDisabled = !(isAlreadyLoaded && modalFlag);

  useEffect(() => {
    form.setFieldsValue({ content: generateReportContent(spaceGuid.split('.')[0], spaceGuid) });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [spaceGuid, slug, form, isAlreadyLoaded]);

  useEffect(() => {
    const getIdcName = async () => {
      const { error, data } = await fetchIdc({ idcTag: spaceGuid.split('.')[0] });
      if (error) {
        message.error(error.message);
        return;
      }
      setIdcName(data?.name);
    };
    const generateDefaultPdfFile = () => {
      let w = document.getElementById('root')?.offsetWidth;
      let h = document.getElementById('root')?.offsetHeight;
      let redashDom = document.querySelector('#root') as HTMLElement;
      if (redashDom) {
        html2canvas(redashDom, {
          width: w,
          height: h,
          useCORS: true,
        }).then(
          async (canvas: {
            toDataURL: (arg0: string, arg1: number) => string;
            height: number;
            width: number;
          }) => {
            // 此为base64编码格式
            let imgUrl = canvas
              .toDataURL('image/png', 1)
              .replace('image/png', 'image/octet-stream'); // 此方法可以设置截图质量（0-1）
            const pdf = new jsPDF({
              unit: 'px',
              format: [canvas.height, canvas.width],
            });
            pdf.addImage(imgUrl, 'png', 0, 0, canvas.width, canvas.height);
            let blob = pdf.output('blob');
            const { data, error } = await uploadFullFile(new File([blob], `${pdfName}.pdf`));
            if (error) {
              message.error(error.message);
              return;
            }
            const defaultFile = mutateFile({}, data![0]);
            setFileList([defaultFile]);
            setModalFlag(true);
          }
        );
      }
    };
    if (isAlreadyLoaded) {
      setModalFlag(false);
      getIdcName();
      setTimeout(() => {
        generateDefaultPdfFile();
      }, 3000);
    }
  }, [spaceGuid, slug, isAlreadyLoaded, pdfName]);
  return (
    <>
      <Button
        aria-label="mail-button"
        type="link"
        disabled={isModalDisabled}
        onClick={() => {
          setVisible(true);
        }}
      >
        发送邮件
      </Button>
      <Modal
        visible={visible}
        title="发送邮件"
        onOk={onFinish}
        okText="发送"
        onCancel={() => {
          setVisible(false);
        }}
      >
        <Form form={form} labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
          <Form.Item
            label="收件人"
            name="userList"
            rules={[{ required: true, message: '收件人必选！' }]}
          >
            <UserSelect
              labelInValue
              mode="multiple"
              onChange={(_, nodeValue) => {
                setUserList(
                  nodeValue?.map(
                    (
                      node: { value: number } & {
                        [x: string]: { email: string; mobileNumber: string };
                      }
                    ) => {
                      const { email, mobileNumber } = node['data-user'];
                      return {
                        userId: node.value.toString(),
                        email: email,
                        mobile: mobileNumber,
                      };
                    }
                  )
                );
              }}
            />
          </Form.Item>
          <Form.Item label="附件" name="fileList">
            <Upload
              accept="image/*,.pdf,.xls,.xlsx"
              showUploadList
              allowDelete
              maxFileSize={20}
              maxCount={5}
              fileList={fileList}
              onChange={({ file, fileList }) => {
                if (
                  file.status === 'uploading' &&
                  fileList.filter(file => file.status !== 'uploading').length === 5
                ) {
                  message.error('上传附件数量最多5个!');
                  return;
                }
                setFileList(fileList);
              }}
            >
              <Button type="primary">添加</Button>
            </Upload>
          </Form.Item>
          <Form.Item
            label="正文内容"
            name="content"
            rules={[{ required: true, message: '正文内容必填！' }]}
          >
            <Input.TextArea></Input.TextArea>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
function mutateFile(file: any, mcUploadFile: McUploadFile): MixedUploadFile {
  return { ...mcUploadFile, type: undefined, src: mcUploadFile.src, url: mcUploadFile.src } as any;
}
