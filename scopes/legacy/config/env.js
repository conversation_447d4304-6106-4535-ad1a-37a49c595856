/* eslint-disable */

'use strict';

const fs = require('fs');
const path = require('path');
const paths = require('./paths');

// Make sure that including paths.js after env.js will read .env variables.
delete require.cache[require.resolve('./paths')];

const NODE_ENV = process.env.NODE_ENV;
if (!NODE_ENV) {
  throw new Error('The NODE_ENV environment variable is required but was not specified.');
}

// https://github.com/bkeepers/dotenv#what-other-env-files-can-i-use
const dotenvFiles = [
  `${paths.dotenv}.${NODE_ENV}.local`,
  // Don't include `.env.local` for `test` environment
  // since normally you expect tests to produce the same
  // results for everyone
  NODE_ENV !== 'test' && `${paths.dotenv}.local`,
  `${paths.dotenv}.${NODE_ENV}`,
  paths.dotenv,
].filter(Boolean);

// Load environment variables from .env* files. Suppress warnings using silent
// if this file is missing. dotenv will never modify any environment variables
// that have already been set.  Variable expansion is supported in .env files.
// https://github.com/motdotla/dotenv
// https://github.com/motdotla/dotenv-expand
dotenvFiles.forEach(dotenvFile => {
  if (fs.existsSync(dotenvFile)) {
    require('dotenv-expand')(
      require('dotenv').config({
        path: dotenvFile,
      })
    );
  }
});

// We support resolving modules according to `NODE_PATH`.
// This lets you use absolute paths in imports inside large monorepos:
// https://github.com/facebook/create-react-app/issues/253.
// It works similar to `NODE_PATH` in Node itself:
// https://nodejs.org/api/modules.html#modules_loading_from_the_global_folders
// Note that unlike in Node, only *relative* paths from `NODE_PATH` are honored.
// Otherwise, we risk importing Node.js core modules into an app instead of webpack shims.
// https://github.com/facebook/create-react-app/issues/1023#issuecomment-265344421
// We also resolve them to make sure all tools using them work consistently.
const appDirectory = fs.realpathSync(process.cwd());
process.env.NODE_PATH = (process.env.NODE_PATH || '')
  .split(path.delimiter)
  .filter(folder => folder && !path.isAbsolute(folder))
  .map(folder => path.resolve(appDirectory, folder))
  .join(path.delimiter);

// Grab NODE_ENV and REACT_APP_* environment variables and prepare them to be
// injected into the application via DefinePlugin in webpack configuration.
const REACT_APP = /^REACT_APP_/i;

function getClientEnvironment(publicUrl) {
  const raw = Object.keys(process.env)
    .filter(key => REACT_APP.test(key))
    .reduce(
      (env, key) => {
        env[key] = process.env[key];
        return env;
      },
      {
        // Useful for determining whether we’re running in production mode.
        // Most importantly, it switches React into the correct mode.
        NODE_ENV: process.env.NODE_ENV || 'development',
        // Useful for resolving the correct path to static assets in `public`.
        // For example, <img src={process.env.PUBLIC_URL + '/img/logo.png'} />.
        // This should only be used as an escape hatch. Normally you would put
        // images into the `src` and `import` them in code to get their paths.
        PUBLIC_URL: publicUrl,
        // We support configuring the sockjs pathname during development.
        // These settings let a developer run multiple simultaneous projects.
        // They are used as the connection `hostname`, `pathname` and `port`
        // in webpackHotDevClient. They are used as the `sockHost`, `sockPath`
        // and `sockPort` options in webpack-dev-server.
        WDS_SOCKET_HOST: process.env.WDS_SOCKET_HOST,
        WDS_SOCKET_PATH: process.env.WDS_SOCKET_PATH,
        // Change default WDS_SOCKET_PORT from "3000" to "5000" to fix this [issue](https://github.com/facebook/create-react-app/issues/11779#issuecomment-997410442)
        WDS_SOCKET_PORT: process.env.WDS_SOCKET_PORT ?? '5000',
        // Whether or not react-refresh is enabled.
        // It is defined here so it is available in the webpackHotDevClient.
        // FAST_REFRESH: process.env.FAST_REFRESH !== 'false',
        // @Jeery We choose to turn it on by default.
        FAST_REFRESH: true,

        BUILD_ENV: process.env.BUILD_ENV ?? 'development',
        BUILD_TIME: process.env.BUILD_TIME ?? Date.now(),
        WATERMARK: process.env.WATERMARK ?? 'on',
        LOGO_URI: process.env.LOGO_URI,
        DOCUMENT_TITLE_PREFIX: process.env.DOCUMENT_TITLE_PREFIX,
        RSA_ENCRYPTION_PUBLIC_KEY:
          process.env.RSA_ENCRYPTION_PUBLIC_KEY || '${RSA_ENCRYPTION_PUBLIC_KEY}',
        APP_NAME: process.env.APP_NAME ?? 'GLP DCBASE',
        APP_SALES: process.env.APP_SALES ?? '${APP_SALES}',
        APP_FINANCES: process.env.APP_FINANCES ?? '${APP_FINANCES}',
        APP_MONITORING: process.env.APP_MONITORING ?? '${APP_MONITORING}',
        STATIC_URL: process.env.STATIC_URL ?? '/oss/static/1.0.0',
        MF_REMOTES_DCBRAIN_LEGACY:
          process.env.MF_REMOTES_DCBRAIN_LEGACY ?? '${MF_REMOTES_DCBRAIN_LEGACY}',
        MF_REMOTES_REDASH: process.env.MF_REMOTES_REDASH ?? '${MF_REMOTES_REDASH}',
        MF_REMOTES_RESOURCES: process.env.MF_REMOTES_RESOURCES ?? '${MF_REMOTES_RESOURCES}',
        MF_REMOTES_DCBRAIN: process.env.MF_REMOTES_DCBRAIN ?? '${MF_REMOTES_DCBRAIN}',
        MF_REMOTES_MONITORING: process.env.MF_REMOTES_MONITORING ?? '${MF_REMOTES_MONITORING}',
        MF_REMOTES_REPORT: process.env.MF_REMOTES_REPORT ?? '${MF_REMOTES_REPORT}',
        MF_REMOTES_DASHBOARD: process.env.MF_REMOTES_DASHBOARD ?? '${MF_REMOTES_DASHBOARD}',
      }
    );
  // Stringify all values so we can feed into webpack DefinePlugin
  const stringified = {
    'process.env': Object.keys(raw).reduce((env, key) => {
      env[key] = JSON.stringify(raw[key]);
      return env;
    }, {}),
  };

  return { raw, stringified };
}

module.exports = getClientEnvironment;
