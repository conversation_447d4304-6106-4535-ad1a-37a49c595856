'use strict'; // eslint-disable-line

const path = require('path');
const fs = require('fs');
const getPublicUrlOrPath = require('react-dev-utils/getPublicUrlOrPath');

// Make sure any symlinks in the project folder are resolved:
// https://github.com/facebook/create-react-app/issues/637
const appDirectory = fs.realpathSync(process.cwd());
const resolveApp = relativePath => path.resolve(appDirectory, relativePath);

// We use `PUBLIC_URL` environment variable or "homepage" field to infer
// "public path" at which the app is served.
// webpack needs to know it to put the right <script> hrefs into HTML even in
// single-page apps that may serve index.html for nested URLs like /todos/42.
// We can't use a relative path in HTML because we don't want to load something
// like /todos/42/static/js/bundle.7289d.js. We have to know the root.
const publicUrlOrPath = getPublicUrlOrPath(
  process.env.NODE_ENV === 'development',
  require(resolveApp('package.json')).homepage,
  process.env.PUBLIC_URL
);

const buildPath = process.env.BUILD_PATH || 'scopes/legacy/build';

const moduleFileExtensions = [
  'web.mjs',
  'mjs',
  'web.js',
  'js',
  'web.ts',
  'ts',
  'web.tsx',
  'tsx',
  'json',
  'web.jsx',
  'jsx',
];

// Resolve file paths in the same order as webpack
const resolveModule = (resolveFn, filePath) => {
  const extension = moduleFileExtensions.find(extension =>
    fs.existsSync(resolveFn(`${filePath}.${extension}`))
  );

  if (extension) {
    return resolveFn(`${filePath}.${extension}`);
  }

  return resolveFn(`${filePath}.js`);
};

// config after eject: we're in ./config/
module.exports = {
  dotenv: resolveApp('scopes/legacy/.env'),
  appPath: resolveApp('.'),
  appBuild: resolveApp(buildPath),
  appPublic: resolveApp('scopes/legacy/public'),
  appHtml: resolveApp('scopes/legacy/public/index.html'),
  appIndexJs: resolveModule(resolveApp, 'scopes/legacy/src/index'),
  appPackageJson: resolveApp('package.json'),
  appSrc: resolveApp('.'),
  appTsConfig: resolveApp('tsconfig.json'),
  appJsConfig: resolveApp('scopes/legacy/jsconfig.json'),
  yarnLockFile: resolveApp('yarn.lock'),
  testsSetup: resolveModule(resolveApp, 'scopes/legacy/src/setupTests'),
  proxySetup: resolveApp('scopes/legacy/src/setupProxy.js'),

  dcbrain: resolveApp('src'),
  authApp: resolveApp('scopes/auth-hub'),
  bpmApp: resolveApp('scopes/bpm'),
  crmApp: resolveApp('scopes/crm'),
  hrmApp: resolveApp('scopes/hrm'),
  knowledgeHubApp: resolveApp('scopes/knowledge-hub'),
  monitoringApp: resolveApp('scopes/monitoring'),
  notificationApp: resolveApp('scopes/notification-hub'),
  redashApp: resolveApp('scopes/redash'),
  resourceApp: resolveApp('scopes/resource-hub'),
  ticketApp: resolveApp('scopes/ticket'),

  dcbrainBaseNodeModules: resolveApp('scopes/dc-brain/node_modules'),
  authAppNodeModules: resolveApp('scopes/auth-hub/node_modules'),
  bpmAppNodeModules: resolveApp('scopes/bpm/node_modules'),
  crmAppNodeModules: resolveApp('scopes/crm/node_modules'),
  hrmAppNodeModules: resolveApp('scopes/hrm/node_modules'),
  knowledgeHubAppNodeModules: resolveApp('scopes/knowledge-hub/node_modules'),
  monitoringAppNodeModules: resolveApp('scopes/monitoring/node_modules'),
  notificationAppNodeModules: resolveApp('scopes/notification-hub/node_modules'),
  redashAppNodeModules: resolveApp('scopes/redash/node_modules'),
  resourceAppNodeModules: resolveApp('scopes/resource-hub/node_modules'),
  ticketAppNodeModules: resolveApp('scopes/ticket/node_modules'),

  appNodeModules: resolveApp('node_modules'),
  appWebpackCache: resolveApp('node_modules/.cache'),
  appTsBuildInfoFile: resolveApp('node_modules/.cache/tsconfig.tsbuildinfo'),
  swSrc: resolveModule(resolveApp, 'scopes/legacy/src/service-worker'),
  publicUrlOrPath,
  legacyIconfontJS: resolveApp(
    'scopes/legacy/src/legacy/components/tiny-font/font_1595921_x1utnzs1j8q.js'
  ),
  legacyFetchWorkerJS: resolveApp('scopes/legacy/src/legacy/redux/sagas/worker.js'),
  userHolidayBalanceCardPath: resolveModule(
    resolveApp,
    'scopes/legacy/src/exposes/hrm.ui.user-holiday-balance-card'
  ),
  userShiftScheduleCardPath: resolveModule(
    resolveApp,
    'scopes/legacy/src/exposes/hrm.ui.user-shift-schedule-card'
  ),
  notificationHubRouteRath: resolveApp(
    'scopes/notification-hub/node_modules/@manyun/notification-hub.route.notification-routes/dist/index.js'
  ),
  knowledgeHubRouteRath: resolveApp(
    'scopes/knowledge-hub/node_modules/@manyun/knowledge-hub.route.knowledge-hub-routes/dist/index.js'
  ),
  bpmRouteRath: resolveApp('scopes/bpm/node_modules/@manyun/bpm.route.bpm-routes/dist/index.js'),
  ticketRouteRath: resolveApp(
    'scopes/ticket/node_modules/@manyun/ticket.route.ticket-routes/dist/index.js'
  ),
  monitoringRouteRath: resolveApp(
    'scopes/monitoring/node_modules/@manyun/monitoring.route.monitoring-routes/dist/index.js'
  ),
  fetchExamPaperUsers: resolveApp(
    'scopes/knowledge-hub/node_modules/@manyun/knowledge-hub.service.dcexam.fetch-exam-paper-users/dist/esm/index.mjs'
  ),
  fetchCourses: resolveApp(
    'scopes/knowledge-hub/node_modules/@manyun/knowledge-hub.service.dcexam.fetch-courses/dist/esm/index.mjs'
  ),
  fetchCertList: resolveApp(
    'scopes/hrm/node_modules/@manyun/hrm.service.fetch-cert-list/dist/esm/index.mjs'
  ),
  routesMapper: resolveApp(
    'scopes/dc-brain/node_modules/@manyun/dc-brain.ui.layout/dist/routes-mapper.js'
  ),
  approvalOperationButtonsPath: resolveModule(
    resolveApp,
    'scopes/legacy/src/exposes/bpm.ui.approval-operation-buttons'
  ),
  approvalRecordsDropdownPath: resolveModule(
    resolveApp,
    'scopes/legacy/src/exposes/bpm.ui.approval-records-dropdown'
  ),
  orderLinkRath: resolveModule(resolveApp, 'scopes/bpm/ui/corresponding-order-link/index'),
  fetchTaskSearchCenterList: resolveModule(
    resolveApp,
    '/scopes/dc-brain/service/fetch-task-search-center/index'
  ),
  fetchTaskSearchAgg: resolveModule(
    resolveApp,
    '/scopes/dc-brain/service/fetch-task-search-agg/index'
  ),
  ticketClientDrillOrders: resolveModule(resolveApp, '/scopes/ticket/gql/client/drill-order/index'),
  useResourceCascaderPath: resolveModule(
    resolveApp,
    'scopes/legacy/src/exposes/auth-hub.ui.gql.resource-cascader'
  ),
  mineTicketsPath: resolveApp('scopes/legacy/src/legacy/pages/mine-tickets/index.js'),
};

module.exports.moduleFileExtensions = moduleFileExtensions;
