import { loadRemote } from '@module-federation/runtime';
import { cloneDeep } from 'lodash-es';
import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Redirect, Route, Switch, useLocation, useRouteMatch } from 'react-router-dom';

import { setUserInfo } from '@manyun/auth-hub.cache.user';
import { LOGIN_ROUTH_PATH } from '@manyun/auth-hub.route.auth-routes';
import { selectMe, userSliceActions } from '@manyun/auth-hub.state.user';
import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';
import {
  BPM_INSTANCE_ROUTE_PATH,
  DEPRECATED_BPM_INSTANCE_ROUTE_PATH,
  generateBPMRoutePath,
} from '@manyun/bpm.route.bpm-routes';
import configs from '@manyun/dc-brain.config.base/configs/default.json';
import { ConfigsProvider } from '@manyun/dc-brain.context.configs';
import { LayoutProvider } from '@manyun/dc-brain.context.layout';
import type { Breadcrumb } from '@manyun/dc-brain.context.layout';
import { configSliceActions, selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { AppIdentityInterceptor } from '@manyun/dc-brain.ui.app-identity-interceptor';
import { Layout, LayoutContent } from '@manyun/dc-brain.ui.layout';
import { LoggedInUserProvider } from '@manyun/iam.context.logged-in-user';
import { PREVIEW_SPECIFIC_TOPOLOGY } from '@manyun/monitoring.route.monitoring-routes';

import Loading from '@manyun/dc-brain.legacy.components/loading';
import '@manyun/dc-brain.legacy.styles/form/index.less';

import './app.less';
import { LoggedInUserInterceptor } from './components/logged-in-user-interceptor';
import { LoginStatusInterceptor } from './components/login-status-interceptor';
import { legacyRoutes } from './legacy-routes';
import './register-custom-layout-components';
import { routes as authRoutes } from './routes/auth-routes';
import { bizBpmRoutes, routes as bpmRoutes } from './routes/bpm-routes';
import { routes as chartsRoutes } from './routes/charts-routes';
import { routes as crmRoutes } from './routes/crm-routes';
import { routes as dcBrainRoutes } from './routes/dc-brain-routes';
import { routes as hrmRoutes } from './routes/hrm-routes';
import { routes as knowledgeHubRoutes } from './routes/knowledge-hub-routes';
import { routes as monitoringRoutes } from './routes/monitoring-routes';
import { routes as notificationRoutes } from './routes/notification-routes';
import { routes as redashRoutes } from './routes/redash-routes';
import { routes as resourceHubRoutes } from './routes/resource-hub-routes';
import { routes as sentryRoutes } from './routes/sentry-routes';
import { routes as ssoRoutes } from './routes/sso-routes';
import { routes as testsRoutes } from './routes/tests-routes';
import { routes as ticketRoutes } from './routes/ticket-routes';

const LoginPage = React.lazy(() => import('@manyun/auth-hub.page.login'));
const BoundaryGraphShare = React.lazy(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/topology-graphix/boundary-graph-share')
);
// // @ts-ignore: Could not find a declaration file
// const ResourcesMfApp = React.lazy(() => import('@manyun_resources/app'));
// // @ts-ignore: Could not find a declaration file
// const DCBrainMfApp = React.lazy(() => import('@manyun_dcbrain/app'));

// // @ts-ignore types
// const AppRedash = React.lazy(() => loadRemote('@manyun_redash/app'));
// // @ts-ignore types
// const ReportCenter = React.lazy(() => import('@manyun_report/app'));

export function DcBrainApp() {
  const dispatch = useDispatch();
  const whitelistRouteMatch = useRouteMatch(PREVIEW_SPECIFIC_TOPOLOGY);
  const loginRouteMatch = useRouteMatch(LOGIN_ROUTH_PATH);

  if (whitelistRouteMatch !== null) {
    return (
      <React.Suspense fallback={<Loading containerStyle={{ height: '100vh' }} />}>
        <WhiteListApp />
      </React.Suspense>
    );
  }

  if (loginRouteMatch !== null) {
    return (
      <React.Suspense fallback={<Loading containerStyle={{ height: '100vh' }} />}>
        <UnLoginApp />
      </React.Suspense>
    );
  }

  return (
    <React.Suspense fallback={<Loading containerStyle={{ height: '100vh' }} />}>
      <LoginStatusInterceptor>
        <AppIdentityInterceptor
          app="DCBASE"
          client="BROWSER"
          // Allow users without any roles to request roles from system "DC Base".
          isRoleRequiredToGenerateAppIdentity={false}
          onCompleted={data => {
            if (!data || !data.loginByToken) {
              return;
            }
            // TODO: @Jerry drop this `sessionId`
            // We need to implement the SSO function on the BFF side at first.
            localStorage.setItem('sessionId', data.loginByToken.sessionId!);

            setUserInfo({
              // @ts-ignore hard-codeded for now
              type: 'DEFAULT',
              id: data.loginByToken.user!.id,
              login: data.loginByToken.user!.loginName,
              name: data.loginByToken.user!.userName,
              mobileNumber: data.loginByToken.user!.mobile ?? '',
              expiredTime: 0,
              company: data.loginByToken.user!.company,
              needResetPassword: false,
              deptId: data.loginByToken.user!.deptId ?? null,
              department: data.loginByToken.user!.deptName ?? null,
            });

            if (data.loginByToken.user!.roleCode) {
              localStorage.setItem('roleCode', data.loginByToken.user!.roleCode);
            }

            dispatch(
              userSliceActions.loginSuccess({
                userId: data.loginByToken.user!.id,
                username: data.loginByToken.user!.loginName,
                name: data.loginByToken.user!.userName,
                company: data.loginByToken.user!.company ?? null,
              })
            );
          }}
        >
          <ConfigsProvider
            // @ts-ignore Configs type mismatch
            configs={configs}
            onChange={_configs => {
              dispatch(configSliceActions.setCurrent(cloneDeep(_configs)));
            }}
          >
            <LoggedInUserInterceptor>
              {({ loggedInUser }) => (
                <LoggedInUserProvider user={loggedInUser}>
                  <LoggedInApp />
                </LoggedInUserProvider>
              )}
            </LoggedInUserInterceptor>
          </ConfigsProvider>
        </AppIdentityInterceptor>
      </LoginStatusInterceptor>
    </React.Suspense>
  );
}

function WhiteListApp() {
  return (
    <Switch>
      <Route path={PREVIEW_SPECIFIC_TOPOLOGY}>
        <BoundaryGraphShare />
      </Route>
    </Switch>
  );
}

function UnLoginApp() {
  const { search } = useLocation();

  return (
    <Switch>
      <Route path={LOGIN_ROUTH_PATH} exact>
        <LoginPage />
      </Route>
      <Redirect
        to={{
          pathname: LOGIN_ROUTH_PATH,
          search,
        }}
      />
    </Switch>
  );
}

function LoggedInApp() {
  const location = useLocation();
  const { search, pathname } = location;
  const { from } = getLocationSearchMap<{ from?: string }>(search);

  const {
    common: { homeUrl },
  } = useSelector(selectCurrentConfig);
  const { idc } = useSelector(selectMe, (left, right) => left.idc === right.idc);

  if (pathname.includes(BPM_INSTANCE_ROUTE_PATH) && pathname.split('/').length === 5) {
    return (
      <Switch>
        <Redirect
          from={DEPRECATED_BPM_INSTANCE_ROUTE_PATH}
          to={generateBPMRoutePath({
            id: pathname.split('/')[4],
          })}
        />
      </Switch>
    );
  }

  let redirectTo = homeUrl;
  if (typeof from == 'string' && from !== '') {
    // 若登录后要跳转到 IDC 监控中心下的页面但是用户缓存中没有选中的 `idc`，则强行回到首页
    if (!((from.includes('/monitoring/') || from.includes('/alarm-screen/')) && idc === null)) {
      redirectTo = from;
    }
  }
  //处理新建流程编辑流程的面包屑
  const searchParams = getLocationSearchMap(search);
  const currentBizBpmRoutes = bizBpmRoutes(searchParams);

  let ROUTES_LIST = [
    testsRoutes,
    ssoRoutes,
    monitoringRoutes,
    authRoutes,
    currentBizBpmRoutes,
    bpmRoutes,
    crmRoutes,
    notificationRoutes,
    redashRoutes,
    knowledgeHubRoutes,
    hrmRoutes,
    chartsRoutes,
    legacyRoutes,
    resourceHubRoutes,
    ticketRoutes,
    dcBrainRoutes,
    sentryRoutes,
  ].flat();

  switch (process.env.REACT_APP_MODULE) {
    case 'resource-hub':
      ROUTES_LIST = resourceHubRoutes;
      break;

    default:
      break;
  }

  return (
    <LayoutProvider>
      <Layout homeUrl={homeUrl as string}>
        <Switch>
          <Redirect from={LOGIN_ROUTH_PATH} to={redirectTo as string} />

          {ROUTES_LIST.map(route => (
            <Route key={route.path as string} {...route} />
          ))}

          {/* <Route path="/report">
            <LayoutContent
              pathname={pathname}
              composeBreadcrumbs={(value: Breadcrumb[]) => {
                if (pathname.includes('apply-report-access')) {
                  return [
                    ...value,
                    {
                      key: 'apply-report-access',
                      text: '申请报表权限',
                    },
                  ];
                }
                if (pathname.includes('indicator-meter-config/new')) {
                  return [...value, { key: 'detail', text: '新建' }];
                }
                if (pathname.includes('indicator-meter-config/edit')) {
                  return [...value, { key: 'detail', text: '编辑' }];
                }
                if (pathname.includes('indicator-meter-config/import')) {
                  return [...value, { key: 'detail', text: '导入' }];
                }
                if (pathname.includes('indicator-meter-config/')) {
                  return [...value, { key: 'detail', text: '详情' }];
                }
                return value;
              }}
            >
              <ReportCenter basename="/report" location={location} />
            </LayoutContent>
          </Route>

          <Route path="/resources">
            <LayoutContent pathname={pathname}>
              <ResourcesMfApp basename="/resources" />
            </LayoutContent>
          </Route>

          <Route path="/dc-brain-web">
            <DCBrainMfApp basename="/dc-brain-web" />
          </Route>

          <Route path="/redash">
            <AppRedash basename="/redash" />
          </Route> */}
        </Switch>
      </Layout>
    </LayoutProvider>
  );
}
