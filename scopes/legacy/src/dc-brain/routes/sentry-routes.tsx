import React from 'react';
// @ts-ignore: Could not find a declaration file
import DocumentTitle from 'react-document-title';

import { UserLink, type UserLinkProps } from '@manyun/auth-hub.ui.user';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import type { UserSelectProps } from '@manyun/auth-hub.ui.user-select';
import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import { LayoutContent } from '@manyun/dc-brain.ui.layout';
import {
  LocationTreeSelect,
  type LocationTreeSelectProps,
} from '@manyun/resource-hub.ui.location-tree-select';
import { SpaceText } from '@manyun/resource-hub.ui.space-text';
import type { SpaceTextProps } from '@manyun/resource-hub.ui.space-text';
import {
  ENTRANCE_GUARD_CARD_MANAGEMENT_DETAIL_ROUTE_PATH,
  ENTRANCE_GUARD_CARD_MANAGEMENT_ROUTE_PATH,
  VISITS_STATISTIC_ROUTE_AUTH_CODE,
  VISITS_STATISTIC_ROUTE_PATH,
} from '@manyun/sentry.route.routes';

import type { SimpleRouteProps } from './types';

const DOCUMENT_TITLE_PREFIX = env.DOCUMENT_TITLE_PREFIX;

const EntranceGuardCardManagements = React.lazy(
  // () => import('@manyun/sentry.page.entrance-guard-card-managements')
  () => import('../../legacy/pages/entrance-guard-card/entrance-guard-card-managements')
);
const EntranceGuardCardManagement = React.lazy(
  () => import('../../legacy/pages/entrance-guard-card/entrance-guard-card-management')
  // () => import('@manyun/sentry.page.entrance-guard-card-management')
);

const VisitStatistic = React.lazy(() => import('@manyun/sentry.page.visits-statistic'));

// @todo 需要写在 sentry-web tag 引用此包 ，还有别的visitor-application需要tag完成
const BusinessClassificationMapping = React.lazy(
  () =>
    import(
      '../../legacy/pages/ticket/configs/visitor/visitor-application/business-classification-mapping'
    )
);
// @todo 需要写在 sentry-web tag 引用此包 ，还有别的visitor-application需要tag完成
const DockingConfiguration = React.lazy(
  () =>
    import('../../legacy/pages/ticket/configs/visitor/visitor-application/docking-configurations')
);

export const routes: SimpleRouteProps[] = [
  {
    path: ENTRANCE_GUARD_CARD_MANAGEMENT_ROUTE_PATH,
    exact: true,
    children: () => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '门禁卡管理'}>
        <LayoutContent pageCode="page_sentry-entrance-guard-cards">
          <EntranceGuardCardManagements
            authorizedAreaElement={(props: LocationTreeSelectProps) => (
              <LocationTreeSelect {...props} />
            )}
            userSelectElement={(props: UserSelectProps) => <UserSelect {...props} />}
            userLinkElement={(props: UserLinkProps) => <UserLink {...props} />}
          />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: ENTRANCE_GUARD_CARD_MANAGEMENT_DETAIL_ROUTE_PATH,
    exact: true,
    children: props => {
      const id = props.match?.params.id;

      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '门禁卡管理'}>
          <LayoutContent
            pageCode="page_sentry-entrance-guard-card"
            composeBreadcrumbs={value => [
              ...value,
              {
                key: 'page_sentry-entrance-guard-card-key',
                text: id ? decodeURIComponent(id) : id,
              },
            ]}
          >
            <EntranceGuardCardManagement
              userLinkElement={(props: UserLinkProps) => <UserLink {...props} />}
              spaceTextElement={(props: SpaceTextProps) => <SpaceText {...props} />}
            />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    path: VISITS_STATISTIC_ROUTE_PATH,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '人员出入室统计'}>
          <LayoutContent pageCode={VISITS_STATISTIC_ROUTE_AUTH_CODE}>
            <VisitStatistic />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    path: '/page/sentry/business-mapping-configuration',
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '业务映射配置'}>
          <LayoutContent
            pageCode={'page_business-mapping-configuration'}
            composeBreadcrumbs={value => [
              ...value,
              { key: 'page_business-mapping-configuration', text: '业务映射配置' },
            ]}
          >
            <BusinessClassificationMapping />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    path: '/page/sentry/docking-configurations',
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '对接人配置'}>
          <LayoutContent
            pageCode={'page_docking_configurations'}
            composeBreadcrumbs={value => [
              ...value,
              { key: 'page_docking_configurations', text: '对接人配置' },
            ]}
          >
            <DockingConfiguration />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
];
