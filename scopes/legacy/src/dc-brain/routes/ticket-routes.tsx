import React from 'react';
// @ts-ignore: Could not find a declaration file
import DocumentTitle from 'react-document-title';
import { useSelector } from 'react-redux';

import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { LayoutContent } from '@manyun/dc-brain.ui.layout';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { PlanType } from '@manyun/ticket.model.task';
import {
  CHANGE_OFFLINE_EDIT_ROUTE_PATH,
  CHANGE_OFFLINE_NEW,
  CHANGE_SHIFT,
  <PERSON><PERSON><PERSON>_SHIFTS,
  CHANGE_SHIFT_EDIT,
  CHANGE_SHIFT_NEW,
  COPY_DRILL_ORDER_ROUTE_PATH,
  COPY_DRILL_TASK_CONFIGURATION_ROUTE_PATH,
  CREATE_DRILL_ORDER_ROUTE_PATH,
  CREATE_DRILL_TASK_CONFIGURATION_ROUTE_PATH,
  CREATE_INSPECTION_TASK_CONFIGURATION_ROUTE_PATH,
  CREATE_INVENTORY_TASK_CONFIGURATION_ROUTE_PATH,
  CREATE_MAINTAIN_TASK_CONFIGURATION_ROUTE_PATH,
  DRILL_LIST_ROUTE_PATH,
  DRILL_ORDER_ROUTE_PATH,
  DRILL_TASK_CONFIGURATION_DETAIL_ROUTE_PATH,
  DRILL_TASK_CONFIGURATION_LIST_ROUTE_PATH,
  DRILL_TASK_CONFIGURATION_MUTATOR_ROUTE_PATH,
  EVENT_CENTER_DETAIL,
  EVENT_CENTER_NEW,
  INSPECTION_TASK_CONFIGURATION_DETAIL_ROUTE_PATH,
  INSPECTION_TASK_CONFIGURATION_LIST_ROUTE_PATH,
  INSPECTION_TASK_CONFIGURATION_MUTATOR_ROUTE_PATH,
  INVENTORY_TASK_CONFIGURATION_DETAIL_ROUTE_PATH,
  INVENTORY_TASK_CONFIGURATION_LIST_ROUTE_PATH,
  INVENTORY_TASK_CONFIGURATION_MUTATOR_ROUTE_PATH,
  MAINTAIN_TASK_CONFIGURATION_DETAIL_ROUTE_PATH,
  MAINTAIN_TASK_CONFIGURATION_LIST_ROUTE_PATH,
  MAINTAIN_TASK_CONFIGURATION_MUTATOR_ROUTE_PATH,
  RISK_CHECK_TASK_COPY_AUTH_CODE,
  RISK_CHECK_TASK_CREATE_AUTH_CODE,
  RISK_CHECK_TASK_CREATE_ROUTE_PATH,
  RISK_CHECK_TASK_DETAIL_AUTH_CODE,
  RISK_CHECK_TASK_DETAIL_ROUTE_PATH,
  RISK_CHECK_TASK_LIST_AUTH_CODE,
  RISK_CHECK_TASK_LIST_ROUTE_PATH,
  RISK_CHECK_TASK_MUTATE_ROUTE_PATH,
  RISK_CHECK_TASK_UPDATE_AUTH_CODE,
  RISK_POOL_NEW_ROUTE_PATH,
  RISK_POOL_ROUTE_PATH,
  RISK_REGISTER,
  RISK_REGISTERS,
  RISK_REGISTERS_NEW,
  TICKET_ROUTE_PATH_AUTH_CODE_MAPPER,
  UPDATE_DRILL_TASK_CONFIGURATION_ROUTE_PATH,
} from '@manyun/ticket.route.ticket-routes';

import type { SimpleRouteProps } from './types';

const DOCUMENT_TITLE_PREFIX = env.DOCUMENT_TITLE_PREFIX;
const MaintenancePlanMutatorPage = React.lazy(() => import('./../pages/task/maintain'));
const InspectionPlanMutatorPage = React.lazy(() => import('./../pages/task/inspection'));
const InventoryPlanMutatorPage = React.lazy(() => import('./../pages/task/inventory'));
const ChangeShifts = React.lazy(() => import('@manyun/ticket.page.change-shifts'));
const ChangeShift = React.lazy(() => import('@manyun/ticket.page.change-shift'));
const ChangeShiftsNew = React.lazy(() => import('@manyun/ticket.page.change-shifts-mutator'));
const Changeoffline = React.lazy(() => import('@manyun/ticket.page.change-offline'));
// const Events = React.lazy(() => import('@manyun/ticket.page.event'));
const EventDetail = React.lazy(() => import('@manyun/ticket.page.event-detail'));
const EventCreate = React.lazy(() => import('@manyun/ticket.page.event-create'));
const PlanMutator = React.lazy(() => import('@manyun/ticket.page.plan-mutator'));
const PlanDetail = React.lazy(() => import('@manyun/ticket.page.plan-detail'));
const DrillTaskDetail = React.lazy(() => import('@manyun/ticket.page.drill-task-detail'));
const RiskCheckTaskList = React.lazy(() => import('@manyun/ticket.page.risk-check-task-list'));
const RiskCheckTaskDetail = React.lazy(() => import('@manyun/ticket.page.risk-check-task-detail'));
const RiskCheckTaskCreate = React.lazy(() => import('@manyun/ticket.page.risk-check-task-create'));
const DrillTaskCreate = React.lazy(() => import('@manyun/ticket.page.drill-task-create'));
const DrillTaskMutator = React.lazy(() => import('@manyun/ticket.page.drill-task-mutator'));

const RiskCheckTaskMutate = React.lazy(() => import('@manyun/ticket.page.risk-check-task-mutate'));
const RiskRegisters = React.lazy(() => import('@manyun/ticket.page.risk-registers'));
const RiskTools = React.lazy(() => import('@manyun/ticket.page.risk-pools'));
const RiskToolNew = React.lazy(() => import('@manyun/ticket.page.risk-pool-mutator'));
const DrillOrderList = React.lazy(() => import('@manyun/ticket.page.drill-order-list'));
const DrillOrderEditor = React.lazy(() => import('@manyun/ticket.page.drill-order-editor'));
const DrillOrder = React.lazy(() => import('@manyun/ticket.page.drill-order'));
const RiskRegisterMutator = React.lazy(() => import('@manyun/ticket.page.risk-register-mutator'));
const RiskRegisterDetail = React.lazy(() => import('@manyun/ticket.page.risk-register-detail'));
const TaskConfigurationList = React.lazy(
  () => import('@manyun/ticket.page.task-configuration-list')
);

export const routes: SimpleRouteProps[] = [
  {
    path: CREATE_MAINTAIN_TASK_CONFIGURATION_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '新建维护任务'}>
        <LayoutContent
          pageCode={
            TICKET_ROUTE_PATH_AUTH_CODE_MAPPER[CREATE_MAINTAIN_TASK_CONFIGURATION_ROUTE_PATH]
          }
          composeBreadcrumbs={value => [...value, { key: 'page_maintain-key', text: '新建计划' }]}
        >
          <PlanMutator planType={PlanType.MaintenancePlan} mode="create" />
        </LayoutContent>
      </DocumentTitle>
    ),
  },

  {
    path: CREATE_INSPECTION_TASK_CONFIGURATION_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '新建巡检任务'}>
        <LayoutContent
          pageCode={
            TICKET_ROUTE_PATH_AUTH_CODE_MAPPER[CREATE_INSPECTION_TASK_CONFIGURATION_ROUTE_PATH]
          }
          composeBreadcrumbs={value => [...value, { key: 'page_inspect-key', text: '新建计划' }]}
        >
          <PlanMutator planType={PlanType.InspectionPlan} mode="create" />
        </LayoutContent>
      </DocumentTitle>
    ),
  },

  {
    path: CREATE_INVENTORY_TASK_CONFIGURATION_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '新建盘点任务'}>
        <LayoutContent
          pageCode={
            TICKET_ROUTE_PATH_AUTH_CODE_MAPPER[CREATE_INVENTORY_TASK_CONFIGURATION_ROUTE_PATH]
          }
          composeBreadcrumbs={value => [...value, { key: 'page_inventory-key', text: '新建计划' }]}
        >
          <PlanMutator planType={PlanType.InventoryPlan} mode="create" />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: MAINTAIN_TASK_CONFIGURATION_LIST_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '维护任务'}>
        <LayoutContent
          pageCode={TICKET_ROUTE_PATH_AUTH_CODE_MAPPER[MAINTAIN_TASK_CONFIGURATION_LIST_ROUTE_PATH]}
        >
          <TaskConfigurationList planType={PlanType.MaintenancePlan} />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: DRILL_TASK_CONFIGURATION_LIST_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '演练任务'}>
        <LayoutContent
          pageCode={TICKET_ROUTE_PATH_AUTH_CODE_MAPPER[DRILL_TASK_CONFIGURATION_LIST_ROUTE_PATH]}
        >
          <TaskConfigurationList planType={PlanType.DrillPlan} />
        </LayoutContent>
      </DocumentTitle>
    ),
  },

  {
    path: INSPECTION_TASK_CONFIGURATION_LIST_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '巡检任务'}>
        <LayoutContent
          pageCode={
            TICKET_ROUTE_PATH_AUTH_CODE_MAPPER[INSPECTION_TASK_CONFIGURATION_LIST_ROUTE_PATH]
          }
        >
          <TaskConfigurationList planType={PlanType.InspectionPlan} />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: INVENTORY_TASK_CONFIGURATION_LIST_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '盘点任务'}>
        <LayoutContent
          pageCode={
            TICKET_ROUTE_PATH_AUTH_CODE_MAPPER[INVENTORY_TASK_CONFIGURATION_LIST_ROUTE_PATH]
          }
        >
          <TaskConfigurationList planType={PlanType.InventoryPlan} />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: INVENTORY_TASK_CONFIGURATION_DETAIL_ROUTE_PATH,
    exact: true,
    children: props => {
      const name = props.match?.params.name;
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '盘点计划详情'}>
          <LayoutContent
            pageCode={
              TICKET_ROUTE_PATH_AUTH_CODE_MAPPER[INVENTORY_TASK_CONFIGURATION_DETAIL_ROUTE_PATH]
            }
            composeBreadcrumbs={value => [
              ...value,
              {
                key: `page_inventory-detail-key`,
                text: name,
              },
            ]}
          >
            <PlanDetail planType={PlanType.InventoryPlan} />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },

  {
    path: MAINTAIN_TASK_CONFIGURATION_DETAIL_ROUTE_PATH,
    exact: true,
    children: props => {
      const name = props.match?.params.name;
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '维护计划详情'}>
          <LayoutContent
            pageCode={
              TICKET_ROUTE_PATH_AUTH_CODE_MAPPER[MAINTAIN_TASK_CONFIGURATION_DETAIL_ROUTE_PATH]
            }
            composeBreadcrumbs={value => [
              ...value,
              {
                key: `page_maintain-detail-key`,
                text: name,
              },
            ]}
          >
            <PlanDetail planType={PlanType.MaintenancePlan} />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    // DrillTaskDetail
    path: DRILL_TASK_CONFIGURATION_DETAIL_ROUTE_PATH,
    exact: true,
    children: props => {
      const name = props.match?.params.name;
      const pageCode =
        TICKET_ROUTE_PATH_AUTH_CODE_MAPPER[DRILL_TASK_CONFIGURATION_DETAIL_ROUTE_PATH];
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '演练计划详情'}>
          <LayoutContent
            pageCode={pageCode}
            composeBreadcrumbs={value => [
              ...value,
              {
                key: `${pageCode}-key`,
                text: name,
              },
            ]}
          >
            <DrillTaskDetail />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    path: MAINTAIN_TASK_CONFIGURATION_MUTATOR_ROUTE_PATH,
    exact: true,
    children: props => {
      const mode = props.match?.params.mode as 'edit' | 'copy';

      return <MaintenancePlanMutatorPage mode={mode} />;
    },
  },

  {
    path: INSPECTION_TASK_CONFIGURATION_MUTATOR_ROUTE_PATH,
    exact: true,
    children: props => {
      const mode = props.match?.params.mode as 'edit' | 'copy';

      return <InspectionPlanMutatorPage mode={mode} />;
    },
  },

  {
    path: INVENTORY_TASK_CONFIGURATION_MUTATOR_ROUTE_PATH,
    exact: true,
    children: props => {
      const mode = props.match?.params.mode as 'edit' | 'copy';

      return <InventoryPlanMutatorPage mode={mode} />;
    },
  },

  {
    path: INSPECTION_TASK_CONFIGURATION_DETAIL_ROUTE_PATH,
    exact: true,

    children: props => {
      const name = props.match?.params.name;

      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '巡检计划详情'}>
          <LayoutContent
            pageCode={
              TICKET_ROUTE_PATH_AUTH_CODE_MAPPER[INSPECTION_TASK_CONFIGURATION_DETAIL_ROUTE_PATH]
            }
            composeBreadcrumbs={value => [
              ...value,
              {
                key: `page_inspection-detail-key`,
                text: name,
              },
            ]}
          >
            <PlanDetail planType={PlanType.InspectionPlan} />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },

  {
    path: CHANGE_SHIFTS,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '线上交接班'}>
        <LayoutContent pageCode="page_change-shifts">
          <ChangeShifts />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: CHANGE_SHIFT,
    exact: true,
    children: props => {
      const id = props.match?.params.id;

      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '线上交接班'}>
          <LayoutContent
            pageCode="page_change-shift"
            composeBreadcrumbs={value => [...value, { key: 'page_change-shift-new', text: id }]}
          >
            <ChangeShift />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    path: CHANGE_SHIFT_NEW,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '线上交接班'}>
        <LayoutContent
          pageCode="page_change-shift-new"
          composeBreadcrumbs={value => [
            ...value,
            { key: 'page_change-shift-new', text: '新建交接班' },
          ]}
        >
          <ChangeShiftsNew />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: CHANGE_SHIFT_EDIT,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '线上交接班'}>
        <LayoutContent
          pageCode="page_change-shift-edit"
          composeBreadcrumbs={value => [
            ...value,
            { key: 'page_change-shift-edi', text: '新建交接班' },
          ]}
        >
          <ChangeShiftsNew />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: CHANGE_OFFLINE_NEW,
    exact: true,
    children: <ChangeofflineCreateWrapper />,
  },
  {
    path: CHANGE_OFFLINE_EDIT_ROUTE_PATH,
    exact: true,
    children: <ChangeofflineEditWrapper />,
  },

  // {
  //   path: EVENT_CENTER_LIST,
  //   exact: true,
  //   children: props => (
  //     <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '事件列表'}>
  //       <LayoutContent pageCode="page_events">
  //         <Events {...props} />
  //       </LayoutContent>
  //     </DocumentTitle>
  //   ),
  // },
  {
    path: EVENT_CENTER_NEW,
    exact: true,
    children: props => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '新建事件'}>
        <LayoutContent
          pageCode="page_event-create"
          composeBreadcrumbs={value => [...value, { key: 'page_event-create-key', text: '新建' }]}
        >
          <EventCreate />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: EVENT_CENTER_DETAIL,
    exact: true,
    children: props => {
      const { id } = props.match?.params || ({} as any); // eslint-disable-line
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '事件详情'}>
          <LayoutContent
            pageCode="page_event"
            composeBreadcrumbs={value => [...value, { key: 'page_event-key', text: id }]}
          >
            <EventDetail />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },

  {
    path: RISK_REGISTERS,
    exact: true,
    children: props => {
      const { id } = props.match?.params || ({} as any); // eslint-disable-line
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '风险登记册'}>
          <LayoutContent pageCode="page_ticket-risk-registers">
            <RiskRegisters />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    path: RISK_REGISTERS_NEW,
    exact: true,
    children: props => {
      const { id } = props.match?.params || ({} as any); // eslint-disable-line
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '风险登记册'}>
          <LayoutContent
            pageCode="page_ticket-risk-register-new"
            composeBreadcrumbs={value => [
              ...value,
              { key: 'page_ticket-risk-register-new-key', text: '新建风险单' },
            ]}
          >
            <RiskRegisterMutator />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    path: RISK_REGISTER,
    exact: true,
    children: props => {
      const { id } = props.match?.params || ({} as any); // eslint-disable-line
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '风险登记册'}>
          <LayoutContent
            pageCode="page_ticket-risk-register-detail"
            composeBreadcrumbs={value => [
              ...value,
              { key: 'page_ticket-risk-register-detail-key', text: id },
            ]}
          >
            <RiskRegisterDetail />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    path: RISK_CHECK_TASK_LIST_ROUTE_PATH,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '风险检查任务'}>
          <LayoutContent pageCode={RISK_CHECK_TASK_LIST_AUTH_CODE}>
            <RiskCheckTaskList />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },

  {
    path: RISK_CHECK_TASK_CREATE_ROUTE_PATH,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '新建风险检查任务'}>
          <LayoutContent
            pageCode={RISK_CHECK_TASK_CREATE_AUTH_CODE}
            composeBreadcrumbs={value => [
              ...value,
              { key: `${RISK_CHECK_TASK_CREATE_AUTH_CODE}-key`, text: '新建风险检查任务' },
            ]}
          >
            <RiskCheckTaskCreate />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    path: RISK_CHECK_TASK_DETAIL_ROUTE_PATH,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '风险检查任务详情'}>
          <LayoutContent
            pageCode={RISK_CHECK_TASK_DETAIL_AUTH_CODE}
            composeBreadcrumbs={value => [
              ...value,
              { key: `${RISK_CHECK_TASK_DETAIL_AUTH_CODE}-key`, text: '风险检查任务详情' },
            ]}
          >
            <RiskCheckTaskDetail />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    path: RISK_CHECK_TASK_MUTATE_ROUTE_PATH,
    exact: true,
    children: props => {
      const mode = props.match?.params.mode;
      const isEditPage = mode === 'edit';
      const prefix = isEditPage ? '编辑' : '复制';
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + `${prefix}风险检查任务`}>
          <LayoutContent
            pageCode={
              isEditPage ? RISK_CHECK_TASK_UPDATE_AUTH_CODE : RISK_CHECK_TASK_COPY_AUTH_CODE
            }
            composeBreadcrumbs={value => [
              ...value,
              {
                key: `${RISK_CHECK_TASK_CREATE_AUTH_CODE}-copy-key`,
                text: `${prefix}风险检查任务`,
              },
            ]}
          >
            <RiskCheckTaskMutate />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    path: DRILL_TASK_CONFIGURATION_MUTATOR_ROUTE_PATH,
    exact: true,
    children: props => {
      const mode = props.match?.params.mode;
      const isEditPage = mode === 'edit';
      const prefix = isEditPage ? '编辑' : '复制';
      const pageCode = isEditPage
        ? TICKET_ROUTE_PATH_AUTH_CODE_MAPPER[UPDATE_DRILL_TASK_CONFIGURATION_ROUTE_PATH]
        : TICKET_ROUTE_PATH_AUTH_CODE_MAPPER[COPY_DRILL_TASK_CONFIGURATION_ROUTE_PATH];

      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + `${prefix}计划`}>
          <LayoutContent
            pageCode={pageCode}
            composeBreadcrumbs={value => [
              ...value,
              {
                key: `${pageCode}-copy-key`,
                text: `${prefix}计划`,
              },
            ]}
          >
            <DrillTaskMutator />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    path: RISK_POOL_ROUTE_PATH,
    exact: true,
    children: () => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '风险库'}>
        <LayoutContent pageCode={TICKET_ROUTE_PATH_AUTH_CODE_MAPPER[RISK_POOL_ROUTE_PATH]}>
          <RiskTools />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: DRILL_LIST_ROUTE_PATH,
    exact: true,
    children: () => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '演练列表'}>
        <LayoutContent pageCode={TICKET_ROUTE_PATH_AUTH_CODE_MAPPER[DRILL_LIST_ROUTE_PATH]}>
          <DrillOrderList />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: CREATE_DRILL_ORDER_ROUTE_PATH,
    exact: true,
    children: () => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '新建演练'}>
        <LayoutContent
          pageCode={TICKET_ROUTE_PATH_AUTH_CODE_MAPPER[CREATE_DRILL_ORDER_ROUTE_PATH]}
          composeBreadcrumbs={value => [
            ...value,
            { key: 'page_drill-order-create_key', text: '新建演练' },
          ]}
        >
          <DrillOrderEditor />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: COPY_DRILL_ORDER_ROUTE_PATH,
    exact: true,
    children: () => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '新建演练'}>
        <LayoutContent
          pageCode={TICKET_ROUTE_PATH_AUTH_CODE_MAPPER[COPY_DRILL_ORDER_ROUTE_PATH]}
          composeBreadcrumbs={value => [
            ...value,
            { key: 'page_drill-order-copy_key', text: '新建演练' },
          ]}
        >
          <DrillOrderEditor />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: DRILL_ORDER_ROUTE_PATH,
    exact: true,
    children: props => {
      const id = props.match?.params.id;
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '演练详情'}>
          <LayoutContent
            pageCode={TICKET_ROUTE_PATH_AUTH_CODE_MAPPER[DRILL_ORDER_ROUTE_PATH]}
            composeBreadcrumbs={value => [
              ...value,
              { key: 'page_drill-order-detail_id', text: id },
            ]}
          >
            <DrillOrder />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    path: RISK_POOL_NEW_ROUTE_PATH,
    exact: true,
    children: () => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '风险库'}>
        <LayoutContent
          pageCode={TICKET_ROUTE_PATH_AUTH_CODE_MAPPER[RISK_POOL_NEW_ROUTE_PATH]}
          composeBreadcrumbs={value => [
            ...value,
            { key: 'page_ticket-risk-pool-new-key', text: '新建风险点' },
          ]}
        >
          <RiskToolNew />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: CREATE_DRILL_TASK_CONFIGURATION_ROUTE_PATH,
    exact: true,
    children: props => {
      const pageCode =
        TICKET_ROUTE_PATH_AUTH_CODE_MAPPER[CREATE_DRILL_TASK_CONFIGURATION_ROUTE_PATH];
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '新建计划'}>
          <LayoutContent
            pageCode={pageCode}
            composeBreadcrumbs={value => [
              ...value,
              {
                key: `${pageCode}-key`,
                text: '新建计划',
              },
            ]}
          >
            <DrillTaskCreate />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
];

function ChangeofflineEditWrapper() {
  const config = useSelector(selectCurrentConfig);
  const configUtil = new ConfigUtil(config);
  const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket');
  const changesTicketCreateChangeOffline =
    ticketScopeCommonConfigs.changes.features.createChangeOffline;

  return (
    <DocumentTitle
      title={`${DOCUMENT_TITLE_PREFIX}${
        changesTicketCreateChangeOffline === 'full' ? '变更' : '线下文档变更'
      }`}
    >
      <LayoutContent
        pageCode="page_change-offline-edit"
        composeBreadcrumbs={value => [
          ...value,
          {
            key: 'page_change-offline-edit',
            text: changesTicketCreateChangeOffline === 'full' ? '编辑变更' : '编辑线下文档变更',
          },
        ]}
      >
        <Changeoffline />
      </LayoutContent>
    </DocumentTitle>
  );
}

function ChangeofflineCreateWrapper() {
  const config = useSelector(selectCurrentConfig);
  const configUtil = new ConfigUtil(config);
  const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket');
  const changesTicketCreateChangeOffline =
    ticketScopeCommonConfigs.changes.features.createChangeOffline;

  return (
    <DocumentTitle
      title={`${DOCUMENT_TITLE_PREFIX}${
        changesTicketCreateChangeOffline === 'full' ? '变更' : '线下文档变更'
      }`}
    >
      <LayoutContent
        pageCode="page_change-offline-new"
        composeBreadcrumbs={value => [
          ...value,
          {
            key: 'page_change-offline-new',
            text: changesTicketCreateChangeOffline === 'full' ? '新建变更' : '新建线下文档变更',
          },
        ]}
      >
        <Changeoffline />
      </LayoutContent>
    </DocumentTitle>
  );
}
