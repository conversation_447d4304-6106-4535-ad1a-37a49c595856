import React from 'react';

import { message } from '@manyun/base-ui.ui.message';

import { fetchBizFileInfos } from '@manyun/dc-brain.service.fetch-biz-file-infos';

import type { SimpleRouteProps } from './types';

export const routes: SimpleRouteProps[] = [
  {
    path: '/page/_internal_/tests/crashed',
    exact: true,
    children: <MakeItCrash />,
  },
  {
    path: '/page/_internal_/tests/service/client-error',
    exact: true,
    children: <ServiceClientError />,
  },
];

function MakeItCrash() {
  React.useEffect(() => {
    throw new Error('Hello crashed app!');
  }, []);

  return null;
}

function ServiceClientError() {
  const [msg, setMessage] = React.useState<string>();
  React.useEffect(() => {
    // @ts-expect-error: TS2554 because of testing simulation
    fetchBizFileInfos({ targetId: {} }).then(({ error }) => {
      message.error(error!.message);
      setMessage(error!.message);
    });
  }, []);

  return <div>{msg}</div>;
}
