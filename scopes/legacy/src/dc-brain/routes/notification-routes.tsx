import React from 'react';
// @ts-ignore: Could not find a declaration file
import DocumentTitle from 'react-document-title';

import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import { LayoutContent } from '@manyun/dc-brain.ui.layout';
import {
  ALARM_NOTIFICATIONS_ROUTE_PATH,
  NOTIFACTION_HUB_ROUTE_PATH_AUTH_CODE_MAPPER,
  NOTIFICATION_CHANNEL_CONFIG_ROUTE_PATH,
  ON_SITE_MESSAGE_ROUTE_PATH,
  WEBHOOK_CONFIG_ROUTE_PATH,
} from '@manyun/notification-hub.route.notification-routes';

import { SimpleRouteProps } from './types';

const DOCUMENT_TITLE_PREFIX = env.DOCUMENT_TITLE_PREFIX;

const NotificationChannelsConfig = React.lazy(
  () => import('@manyun/notification-hub.page.notification-channels-config')
);
const OnSiteMessagesPage = React.lazy(
  () => import('@manyun/notification-hub.page.on-site-messages')
);

const AlarmNotifications = React.lazy(
  () => import('@manyun/notification-hub.page.alarm-notifications')
);

const WebhookConfig = React.lazy(() => import('@manyun/notification-hub.page.webhook-config'));

export const routes: SimpleRouteProps[] = [
  {
    path: NOTIFICATION_CHANNEL_CONFIG_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '消息接收配置'}>
        <LayoutContent pageCode="page_notification-channels-config">
          <NotificationChannelsConfig />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: ON_SITE_MESSAGE_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '站内消息'}>
        <LayoutContent pageCode="page_inside-notice-list">
          <OnSiteMessagesPage />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: ALARM_NOTIFICATIONS_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '告警通报记录'}>
        <LayoutContent
          pageCode={NOTIFACTION_HUB_ROUTE_PATH_AUTH_CODE_MAPPER[ALARM_NOTIFICATIONS_ROUTE_PATH]}
        >
          <AlarmNotifications />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: WEBHOOK_CONFIG_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + 'Webhook配置'}>
        <LayoutContent
          pageCode={NOTIFACTION_HUB_ROUTE_PATH_AUTH_CODE_MAPPER[WEBHOOK_CONFIG_ROUTE_PATH]}
        >
          <WebhookConfig />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
];
