import React from 'react';
// @ts-ignore: Could not find a declaration file
import DocumentTitle from 'react-document-title';

import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import { LayoutContent } from '@manyun/dc-brain.ui.layout';
import {
  COURSES_ROUTE_PATH,
  COURSEWARES_ROUTE_PATH,
  COURSE_DETAIL_ROUTE_PATH,
  COURSE_LEARNING_TEST_PAPER_ROUTE_PATH,
  EDIT_SPECIFIC_COURSE_ROUTE_PATH,
  EDIT_SPECIFIC_EXAM_ROUTE_PATH,
  EDIT_SPECIFIC_PAPER_ROUTE_PATH,
  EDIT_TRAIN_PLAN_ROUTE_PATH,
  EXAMS_ROUTE_PATH,
  EXAM_PAPER_RESULT_PAGE_URL,
  EXAM_PAPER_URL,
  EXAM_ROUTE_PATH,
  LEARNING_SPECIFIC_COURSE_ROUTE_PATH,
  <PERSON>Y_COURSES_ROUTE_PATH,
  MY_EXAMS_PAGE_URL,
  NEW_COURSE_ROUTE_PATH,
  NEW_EXAM_ROUTE_PATH,
  NEW_PAPER_ROUTE_PATH,
  NEW_TRAIN_PLAN_ROUTE_PATH,
  PAPERS_ROUTE_PATH,
  PAPER_PREVIEW_URL,
  PENDING_MARK_EXAMS_ROUTE_PATH,
  QUESTIONS_CREATOR_ROUTE_PATH,
  QUESTIONS_ROUTE_PATH,
  SKILLS_PAGE_URL,
  SKILL_PAGE_URL,
  TEST_PAPER_RESULT_ROUTE_PATH,
  TRAIN_PLANS_ROUTE_PATH,
  TRAIN_PLAN_ROUTE_PATH,
  WIKIS_ROUTE_PATH,
} from '@manyun/knowledge-hub.route.knowledge-hub-routes';

import type { SimpleRouteProps } from './types';

const DOCUMENT_TITLE_PREFIX = env.DOCUMENT_TITLE_PREFIX;

const QuestionsPage = React.lazy(() => import('@manyun/knowledge-hub.page.questions'));
const QuestionsCreator = React.lazy(() => import('@manyun/knowledge-hub.page.questions-creator'));
const PaperMutator = React.lazy(() => import('@manyun/knowledge-hub.page.paper-mutator'));
const Papers = React.lazy(() => import('@manyun/knowledge-hub.page.papers'));
const PendingMarkExams = React.lazy(() => import('@manyun/knowledge-hub.page.pending-mark-exams'));
const MyExams = React.lazy(() => import('@manyun/knowledge-hub.page.my-exams'));
const TestPaperResult = React.lazy(() => import('@manyun/knowledge-hub.page.test-paper-result'));
const ExamPaperResult = React.lazy(() => import('@manyun/knowledge-hub.page.exam-paper-result'));
const ExamPaper = React.lazy(() => import('@manyun/knowledge-hub.page.exam-paper'));
const ExamMutator = React.lazy(() => import('@manyun/knowledge-hub.page.exam-mutator'));
const Exam = React.lazy(() => import('@manyun/knowledge-hub.page.exam'));
const Exams = React.lazy(() => import('@manyun/knowledge-hub.page.exams'));
const CourseMutator = React.lazy(() => import('@manyun/knowledge-hub.page.course-mutator'));
const CourseLearning = React.lazy(() => import('@manyun/knowledge-hub.page.course-learning'));
const Courses = React.lazy(() => import('@manyun/knowledge-hub.page.courses'));
const CourseDetail = React.lazy(() => import('@manyun/knowledge-hub.page.course-detail'));
const MyCourses = React.lazy(() => import('@manyun/knowledge-hub.page.my-courses'));
const Coursewares = React.lazy(() => import('@manyun/knowledge-hub.page.coursewares'));
const Wikis = React.lazy(() => import('@manyun/knowledge-hub.page.wikis'));
const Skill = React.lazy(() => import('@manyun/knowledge-hub.page.skill'));
const Skills = React.lazy(() => import('@manyun/knowledge-hub.page.skills'));
const PaperPreview = React.lazy(() => import('@manyun/knowledge-hub.page.paper-preview'));
const TrainPlanMutator = React.lazy(() => import('@manyun/knowledge-hub.page.train-plan-mutator'));
const TrainPlanList = React.lazy(() => import('@manyun/knowledge-hub.page.train-plans'));
const TrainPlan = React.lazy(() => import('@manyun/knowledge-hub.page.train-plan'));

function SysCourseLearning(props: any) {
  return (
    <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '课程学习'}>
      <CourseLearning {...props} />
    </DocumentTitle>
  );
}

function CourseCreator(props: any) {
  const pageCode = 'page_knowledge-hub_course-creator';

  return (
    <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '创建课程'}>
      <LayoutContent
        pageCode={pageCode}
        composeBreadcrumbs={value => [...value, { key: 'create', text: '创建课程' }]}
      >
        <CourseMutator {...props} />
      </LayoutContent>
    </DocumentTitle>
  );
}

function CourseEditor(props: any) {
  const pageCode = 'page_knowledge-hub_course-updater';

  return (
    <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '编辑课程'}>
      <LayoutContent
        pageCode={pageCode}
        composeBreadcrumbs={value => [...value, { key: 'edit', text: '编辑课程' }]}
      >
        <CourseMutator {...props} />
      </LayoutContent>
    </DocumentTitle>
  );
}

function ExamPaperCreator(props: any) {
  const pageCode = 'page_knowledge-hub_paper-creator';

  return (
    <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '创建试卷'}>
      <LayoutContent
        pageCode={pageCode}
        composeBreadcrumbs={value => [...value, { key: 'create', text: '创建试卷' }]}
      >
        <div style={{ height: 'var(--content-height)' }}>
          <PaperMutator {...props} />
        </div>
      </LayoutContent>
    </DocumentTitle>
  );
}

function ExamPaperEditor(props: any) {
  const pageCode = 'page_knowledge-hub_paper-updater';

  return (
    <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '修改试卷'}>
      <LayoutContent
        pageCode={pageCode}
        composeBreadcrumbs={value => [...value, { key: 'edit', text: '修改试卷' }]}
      >
        <div style={{ height: 'var(--content-height)' }}>
          <PaperMutator {...props} />
        </div>
      </LayoutContent>
    </DocumentTitle>
  );
}

function QuestionsCreatorPage(props: any) {
  const pageCode = 'page_knowledge-hub_question-creator';

  return (
    <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '创建试题'}>
      <LayoutContent
        pageCode={pageCode}
        composeBreadcrumbs={value => [...value, { key: 'create', text: '创建试题' }]}
      >
        <div style={{ height: 'var(--content-height)' }}>
          <QuestionsCreator {...props} />
        </div>
      </LayoutContent>
    </DocumentTitle>
  );
}

function KnowledgeHubTestPaperResult(props: any) {
  const pageCode = 'page_knowledge_hub-test-result';

  return (
    <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '随堂测验结果'}>
      <LayoutContent pageCode={pageCode}>
        <TestPaperResult {...props} />
      </LayoutContent>
    </DocumentTitle>
  );
}

function KnowledgeHubExamPaperResult(props: any) {
  const pageCode = 'page_knowledge_hub-exam-result';

  return (
    <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '考试结果'}>
      <LayoutContent pageCode={pageCode}>
        <ExamPaperResult {...props} />
      </LayoutContent>
    </DocumentTitle>
  );
}

function KnowledgeHubSkills(props: any) {
  const pageCode = 'page_knowledge_hub-skill-list';

  return (
    <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '技能列表'}>
      <LayoutContent pageCode={pageCode}>
        <Skills {...props} />
      </LayoutContent>
    </DocumentTitle>
  );
}

function KnowledgeHubWikis(props: any) {
  const pageCode = 'page_knowledge_hub-sys-wikis';

  return (
    <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '知识库'}>
      <div style={{ height: 'var(--content-height)' }}>
        <LayoutContent pageCode={pageCode}>
          <div style={{ height: 'var(--content-height)' }}>
            <Wikis {...props} />
          </div>
        </LayoutContent>
      </div>
    </DocumentTitle>
  );
}

function KnowledgeHubCoursewares(props: any) {
  const pageCode = 'page_knowledge_hub-courseware-list';

  return (
    <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '课件列表'}>
      <div style={{ height: 'var(--content-height)' }}>
        <LayoutContent pageCode={pageCode}>
          <div style={{ height: 'var(--content-height)' }}>
            <Coursewares {...props} />
          </div>
        </LayoutContent>
      </div>
    </DocumentTitle>
  );
}

function KnowledgeHubMyCourses(props: any) {
  const pageCode = 'page_knowledge_hub-mine-courses';

  return (
    <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '我的课程'}>
      <LayoutContent pageCode={pageCode}>
        <MyCourses {...props} />
      </LayoutContent>
    </DocumentTitle>
  );
}

function KnowledgeHubCourses(props: any) {
  const pageCode = 'page_knowledge_hub-course-list';

  return (
    <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '课程列表'}>
      <LayoutContent pageCode={pageCode}>
        <Courses {...props} />
      </LayoutContent>
    </DocumentTitle>
  );
}

function KnowledgeHubCourseDetail(props: any) {
  return (
    <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '课程'}>
      <CourseDetail {...props} />
    </DocumentTitle>
  );
}

function KnowledgeHubExams(props: any) {
  const pageCode = 'page_knowledge_hub-exam-list';

  return (
    <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '考试列表'}>
      <LayoutContent pageCode={pageCode}>
        <Exams {...props} />
      </LayoutContent>
    </DocumentTitle>
  );
}

function KnowledgeHubMyExams(props: any) {
  const pageCode = 'page_knowledge_hub-mine-exams';

  return (
    <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '我的考试'}>
      <LayoutContent pageCode={pageCode}>
        <MyExams {...props} />
      </LayoutContent>
    </DocumentTitle>
  );
}

function KnowledgeHubPendingMarkExams(props: any) {
  const pageCode = 'page_knowledge_hub-mark-exams';

  return (
    <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '考试判卷'}>
      <LayoutContent pageCode={pageCode}>
        <PendingMarkExams {...props} />
      </LayoutContent>
    </DocumentTitle>
  );
}

function KnowledgeHubPapers(props: any) {
  const pageCode = 'page_knowledge_hub-paper-list';

  return (
    <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '试卷列表'}>
      <LayoutContent pageCode={pageCode}>
        <Papers {...props} />
      </LayoutContent>
    </DocumentTitle>
  );
}

function KnowledgeHubQuestions(props: any) {
  const pageCode = 'page_knowledge_hub-questions-manage';

  return (
    <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '题库管理'}>
      <LayoutContent pageCode={pageCode}>
        <div style={{ height: 'var(--content-height)' }}>
          <QuestionsPage {...props} />
        </div>
      </LayoutContent>
    </DocumentTitle>
  );
}

function KnowledgeHubExamPaper(props: any) {
  return (
    <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '考卷'}>
      {/* 注：ExamPaper 内置LayoutContent */}
      <ExamPaper {...props} />
    </DocumentTitle>
  );
}

function KnowledgeHubTest(props: any) {
  return (
    <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '随堂测验考卷'}>
      {/* 注：ExamPaper 内置LayoutContent */}
      <ExamPaper {...props} />
    </DocumentTitle>
  );
}

function KnowledgeHubExamUpdater(props: any) {
  const pageCode = 'page_knowledge_hub-exam-edit';

  return (
    <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '修改考试'}>
      <LayoutContent pageCode={pageCode}>
        <ExamMutator {...props} />
      </LayoutContent>
    </DocumentTitle>
  );
}

function KnowledgeHubExamCreator(props: any) {
  const pageCode = 'page_knowledge_hub-exam-create';

  return (
    <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '创建考试'}>
      <LayoutContent
        pageCode={pageCode}
        composeBreadcrumbs={value => [...value, { key: 'create', text: '创建考试' }]}
      >
        <ExamMutator {...props} />
      </LayoutContent>
    </DocumentTitle>
  );
}

function KnowledgeHubExam(props: any) {
  return (
    <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '考试'}>
      <Exam {...props} />
    </DocumentTitle>
  );
}

function KnowledgeHubSkill(props: any) {
  const pageCode = 'page-knowledge_hub-skill-info';

  return (
    <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '技能'}>
      <LayoutContent
        pageCode={pageCode}
        composeBreadcrumbs={value => [...value, { key: 'info', text: '关联考试' }]}
      >
        <Skill {...props} />
      </LayoutContent>
    </DocumentTitle>
  );
}

function KnowledgeHubPaperPreview(props: any) {
  const pageCode = 'page_knowledge_hub-paper-preview';
  return (
    <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '浏览试卷'}>
      <LayoutContent
        pageCode={pageCode}
        composeBreadcrumbs={value => [...value, { key: 'create', text: '浏览试卷' }]}
      >
        <PaperPreview {...props} />
      </LayoutContent>
    </DocumentTitle>
  );
}

function KnowledgeHubTrainPlanMutator(props: any) {
  return (
    <DocumentTitle title={DOCUMENT_TITLE_PREFIX + props.mode + '课程培训计划'}>
      <TrainPlanMutator {...props} />
    </DocumentTitle>
  );
}

function KnowledgeHubTrainPlanList(props: any) {
  const pageCode = 'page_knowledge_hub-train-plan-list';

  return (
    <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '培训计划管理'}>
      <LayoutContent pageCode={pageCode}>
        <TrainPlanList {...props} />
      </LayoutContent>
    </DocumentTitle>
  );
}

function KnowledgeHubTrainPlan(props: any) {
  return (
    <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '培训计划'}>
      <TrainPlan {...props} />
    </DocumentTitle>
  );
}

export const routes: SimpleRouteProps[] = [
  {
    path: LEARNING_SPECIFIC_COURSE_ROUTE_PATH,
    exact: true,
    children: props => <SysCourseLearning props={props} />,
  },
  {
    path: NEW_COURSE_ROUTE_PATH,
    exact: true,
    children: props => <CourseCreator props={props} />,
  },
  {
    path: EDIT_SPECIFIC_COURSE_ROUTE_PATH,
    exact: true,
    children: props => <CourseEditor props={props} />,
  },
  {
    path: NEW_PAPER_ROUTE_PATH,
    exact: true,
    children: props => <ExamPaperCreator props={props} />,
  },
  {
    path: EDIT_SPECIFIC_PAPER_ROUTE_PATH,
    exact: true,
    children: props => <ExamPaperEditor props={props} />,
  },
  {
    path: QUESTIONS_CREATOR_ROUTE_PATH,
    exact: true,
    children: props => <QuestionsCreatorPage props={props} />,
  },
  {
    path: TEST_PAPER_RESULT_ROUTE_PATH,
    exact: true,
    children: props => <KnowledgeHubTestPaperResult props={props} />,
  },
  {
    path: EXAM_PAPER_RESULT_PAGE_URL,
    exact: true,
    children: props => <KnowledgeHubExamPaperResult props={props} />,
  },
  {
    path: SKILLS_PAGE_URL,
    exact: true,
    children: props => <KnowledgeHubSkills props={props} />,
  },
  {
    path: WIKIS_ROUTE_PATH,
    exact: true,
    children: props => <KnowledgeHubWikis props={props} />,
  },
  {
    path: COURSEWARES_ROUTE_PATH,
    exact: true,
    children: props => <KnowledgeHubCoursewares props={props} />,
  },
  {
    path: MY_COURSES_ROUTE_PATH,
    exact: true,
    children: props => <KnowledgeHubMyCourses props={props} />,
  },
  {
    path: COURSES_ROUTE_PATH,
    exact: true,
    children: props => <KnowledgeHubCourses props={props} />,
  },
  {
    path: COURSE_DETAIL_ROUTE_PATH,
    exact: true,
    children: props => <KnowledgeHubCourseDetail props={props} />,
  },
  {
    path: EXAMS_ROUTE_PATH,
    exact: true,
    children: props => <KnowledgeHubExams props={props} />,
  },
  {
    path: MY_EXAMS_PAGE_URL,
    exact: true,
    children: props => <KnowledgeHubMyExams props={props} />,
  },
  {
    path: PENDING_MARK_EXAMS_ROUTE_PATH,
    exact: true,
    children: props => <KnowledgeHubPendingMarkExams props={props} />,
  },
  {
    path: PAPERS_ROUTE_PATH,
    exact: true,
    children: props => <KnowledgeHubPapers props={props} />,
  },
  {
    path: QUESTIONS_ROUTE_PATH,
    exact: true,
    children: props => <KnowledgeHubQuestions props={props} />,
  },
  {
    path: EXAM_PAPER_URL,
    exact: true,
    children: props => <KnowledgeHubExamPaper props={props} />,
  },
  {
    path: COURSE_LEARNING_TEST_PAPER_ROUTE_PATH,
    exact: true,
    children: props => <KnowledgeHubTest props={props} />,
  },
  {
    path: EDIT_SPECIFIC_EXAM_ROUTE_PATH,
    exact: true,
    children: props => <KnowledgeHubExamUpdater props={props} />,
  },
  {
    path: NEW_EXAM_ROUTE_PATH,
    exact: true,
    children: props => <KnowledgeHubExamCreator props={props} />,
  },
  {
    path: EXAM_ROUTE_PATH,
    exact: true,
    children: props => <KnowledgeHubExam props={props} />,
  },
  {
    path: SKILL_PAGE_URL,
    exact: true,
    children: props => <KnowledgeHubSkill props={props} />,
  },
  {
    path: PAPER_PREVIEW_URL,
    exact: true,
    children: props => <KnowledgeHubPaperPreview props={props} />,
  },
  {
    path: NEW_TRAIN_PLAN_ROUTE_PATH,
    exact: true,
    children: props => <KnowledgeHubTrainPlanMutator props={props} mode="新建" />,
  },
  {
    path: TRAIN_PLANS_ROUTE_PATH,
    exact: true,
    children: props => <KnowledgeHubTrainPlanList props={props} />,
  },
  {
    path: EDIT_TRAIN_PLAN_ROUTE_PATH,
    exact: true,
    children: props => <KnowledgeHubTrainPlanMutator props={props} mode="编辑" />,
  },
  {
    path: TRAIN_PLAN_ROUTE_PATH,
    exact: true,
    children: props => <KnowledgeHubTrainPlan props={props} />,
  },
];
