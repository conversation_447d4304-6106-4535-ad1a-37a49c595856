import React from 'react';
// @ts-ignore: Could not find a declaration file
import DocumentTitle from 'react-document-title';

import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import {
  NORTH_BOUND_CREATE,
  NORTH_BOUND_EDIT,
  NORTH_BOUND_MANAGE,
} from '@manyun/dc-brain.route.admin-routes';
import { LayoutContent } from '@manyun/dc-brain.ui.layout';
import {
  AI_OPS_ALARM_ROUTE_PATH,
  AI_OPS_ALARM_ROUTE_PATH_CODE,
  AI_OPS_MONITORING_STATISTICS_ROUTE_PATH,
  AI_OPS_MONITORING_STATISTICS_ROUTE_PATH_CODE,
  ALARM_CONFIGURATION_TEMPLATE_EDIT,
  ALARM_CONFIGURATION_TEMPLATE_NEW,
  ALARM_NOTICE_CONFIGURATIONS_ROUTE_PATH,
  ALARM_NOTICE_CONFIGURATION_ROUTE_PATH,
  ALARM_SHIELD_CREATE_ROUTE_AUTH_CODE,
  ALARM_SHIELD_CREATE_ROUTE_PATH,
  ALARM_SHIELD_DETAIL_ROUTE_AUTH_CODE,
  ALARM_SHIELD_DETAIL_ROUTE_PATH,
  ALARM_SHIELD_EDIT_ROUTE_AUTH_CODE,
  ALARM_SHIELD_EDIT_ROUTE_PATH,
  ALARM_SHIELD_LIST_ROUTE_AUTH_CODE,
  ALARM_SHIELD_LIST_ROUTE_PATH,
  CHANNEL_CONFIG_COPY,
  CHANNEL_CONFIG_DETAIL,
  CHANNEL_CONFIG_EDIT,
  CHANNEL_CONFIG_IMPORT,
  CHANNEL_CONFIG_LIST,
  CHANNEL_CONFIG_NEW,
  CUSTOM_POINT_ROUTE_CODE,
  CUSTOM_POINT_ROUTE_PATH,
  DYNAMIC_BASELINE_SETTING_CREATE_ROUTE_PATH,
  DYNAMIC_BASELINE_SETTING_EDIT_ROUTE_AUTH_CODE,
  DYNAMIC_BASELINE_SETTING_EDIT_ROUTE_PATH,
  DYNAMIC_BASELINE_SETTING_ROUTE_AUTH_CODE,
  DYNAMIC_BASELINE_SETTING_ROUTE_PATH,
  EDIT_PROFESSIONAL_RULE_ROUTE_AUTH_CODE,
  EDIT_PROFESSIONAL_RULE_ROUTE_PATH,
  EDIT_PROFESSIONAL_RULE_TEMPLATE_ROUTE_AUTH_CODE,
  EDIT_PROFESSIONAL_RULE_TEMPLATE_ROUTE_PATH,
  LIQUID_COOLING_ROUTE_PATH,
  LOCAL_ALARM_NOTICE_CONFIGURATIONS_ROUTE_AUTH_CODE,
  LOCAL_ALARM_NOTICE_CONFIGURATIONS_ROUTE_PATH,
  LOCAL_ALARM_NOTICE_CONFIGURATION_ROUTE_AUTH_CODE,
  LOCAL_ALARM_NOTICE_CONFIGURATION_ROUTE_PATH,
  LOCAL_ALARM_RULE_IMPORT_PATH,
  LOCAL_ALARM_RULE_IMPORT_PATH_CODE,
  LOCAL_ALARM_RULE_PATH,
  LOCAL_ALARM_RULE_PATH_CODE,
  LOCAL_CREATE_ALARM_NOTICE_CONFIGURATION_ROUTE_PATH,
  LOCAL_CREATE_ALARM_NOTICE_CONFIGURATION_ROUTE_PATH_AUTH_CODE,
  LOCAL_EDIT_ALARM_NOTICE_CONFIGURATION_ROUTE_PATH,
  LOCAL_EDIT_ALARM_NOTICE_CONFIGURATION_ROUTE_PATH_AUTH_CODE,
  MONITORING_ROUTER_PATH_AUTH_CODE_MAPPER,
  MUTATOR_ALARM_NOTICE_CONFIGURATION_ROUTE_PATH,
  NEW_PROFESSIONAL_RULE_ROUTE_AUTH_CODE,
  NEW_PROFESSIONAL_RULE_ROUTE_PATH,
  NEW_PROFESSIONAL_RULE_TEMPLATE_ROUTE_AUTH_CODE,
  NEW_PROFESSIONAL_RULE_TEMPLATE_ROUTE_PATH,
  NON_POINT_IMPORT_ROUTE_AUTH_CODE,
  NON_POINT_IMPORT_ROUTE_PATH,
  NON_POINT_ROUTE_AUTH_CODE,
  NON_POINT_ROUTE_PATH,
  PROFESSIONAL_RULE_LIST_ROUTE_AUTH_CODE,
  PROFESSIONAL_RULE_LIST_ROUTE_PATH,
  PROFESSIONAL_RULE_TEMPLATE_LIST_ROUTE_AUTH_CODE,
  PROFESSIONAL_RULE_TEMPLATE_LIST_ROUTE_PATH,
} from '@manyun/monitoring.route.monitoring-routes';

import type { SimpleRouteProps } from './types';

const DOCUMENT_TITLE_PREFIX = env.DOCUMENT_TITLE_PREFIX;

const AlarmConfigurationTemplateMutator = React.lazy(
  () => import('@manyun/monitoring.page.alarm-configuration-mutator')
);

const AlarmNoticeConfigurations = React.lazy(
  () => import('@manyun/monitoring.page.alarm-notice-configurations')
);

const AlarmNoticeConfigurationMutator = React.lazy(
  () => import('@manyun/monitoring.page.alarm-notice-configuration-mutator')
);

const AlarmNoticeConfiguration = React.lazy(
  () => import('@manyun/monitoring.page.alarm-notice-configuration')
);

const NorthBoundManage = React.lazy(() => import('@manyun/dc-brain.page.north-bound-manage'));

const NorthBoundCreate = React.lazy(() => import('@manyun/dc-brain.page.north-bound-create'));

const ProfessionalRuleList = React.lazy(
  () => import('@manyun/monitoring.page.professional-rule-list')
);

const ProfessionalRuleEditor = React.lazy(
  () => import('@manyun/monitoring.page.professional-rule-editor')
);

const ProfessionalRuleTemplateList = React.lazy(
  () => import('@manyun/monitoring.page.professional-rule-template-list')
);

const ProfessionalRuleTemplateEditor = React.lazy(
  () => import('@manyun/monitoring.page.professional-rule-template-editor')
);

const ChannelConfigList = React.lazy(() => import('@manyun/monitoring.page.channel-config-list'));

const ChannelConfigNew = React.lazy(() => import('@manyun/monitoring.page.channel-config-create'));

const ChannelConfigDetail = React.lazy(
  () => import('@manyun/monitoring.page.channel-config-detail')
);

const ChannelConfigImport = React.lazy(
  () => import('@manyun/monitoring.page.channel-config-import')
);

const AiOpsAlarms = React.lazy(() => import('@manyun/monitoring.page.ai-ops-alarms'));

const AiOpsStatistics = React.lazy(() => import('@manyun/monitoring.page.ai-ops-statistics'));
const LocalAlarmRuleList = React.lazy(
  () => import('@manyun/monitoring.page.local-alarm-rule-list')
);
const LocalAlarmRuleImport = React.lazy(
  () => import('@manyun/monitoring.page.local-alarm-rule-import')
);
const DynamicBaseLineSettings = React.lazy(
  () => import('@manyun/monitoring.page.dynamic-baseline-settings')
);

const DynamicBaseLineSettingMutator = React.lazy(
  () => import('@manyun/monitoring.page.dynamic-baseline-setting-mutator')
);
const NonPoints = React.lazy(() => import('@manyun/monitoring.page.non-points'));
const NonPointImport = React.lazy(() => import('@manyun/monitoring.page.non-point-import'));
const LiquidCooling = React.lazy(() => import('@manyun/monitoring.page.liquid-cooling'));
const LocalAlarmNoticeConfiguration = React.lazy(
  () => import('@manyun/monitoring.page.local-alarm-notice-configuration')
);
const LocalAlarmNoticeConfigurations = React.lazy(
  () => import('@manyun/monitoring.page.local-alarm-notice-configurations')
);
const LocalAlarmNoticeMutator = React.lazy(
  () => import('@manyun/monitoring.page.local-alarm-notice-mutator')
);
const AlarmShieldList = React.lazy(() => import('@manyun/monitoring.page.alarm-shield-list'));
const AlarmShield = React.lazy(() => import('@manyun/monitoring.page.alarm-shield'));

const AlarmShieldMutator = React.lazy(() => import('@manyun/monitoring.page.alarm-shield-mutator'));

const MergedProcessedPointDetail = React.lazy(
  () => import('@manyun/dc-brain.legacy.pages/merged-processed-point/detail')
);

export const routes: SimpleRouteProps[] = [
  {
    path: ALARM_CONFIGURATION_TEMPLATE_NEW,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '新建告警模板'}>
        <LayoutContent
          pageCode="page_alarm-template-create"
          composeBreadcrumbs={value => [
            ...value,
            { key: 'page_alarm-template-create', text: '新建模板' },
          ]}
        >
          <AlarmConfigurationTemplateMutator />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: ALARM_CONFIGURATION_TEMPLATE_EDIT,
    exact: true,
    children: props => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '修改告警模板'}>
        <LayoutContent
          pageCode="page_alarm-template-edit"
          composeBreadcrumbs={value => [
            ...value,
            { key: 'page_alarm-template-key', text: '编辑模板' },
          ]}
        >
          <AlarmConfigurationTemplateMutator />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: ALARM_NOTICE_CONFIGURATIONS_ROUTE_PATH,
    exact: true,
    children: props => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '告警通报配置'}>
        <LayoutContent
          pageCode={MONITORING_ROUTER_PATH_AUTH_CODE_MAPPER[ALARM_NOTICE_CONFIGURATIONS_ROUTE_PATH]}
        >
          <div style={{ height: 'var(--content-height)' }}>
            <AlarmNoticeConfigurations />
          </div>
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: ALARM_NOTICE_CONFIGURATION_ROUTE_PATH,
    exact: true,
    children: props => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '告警通报配置详情'}>
        <LayoutContent
          pageCode={MONITORING_ROUTER_PATH_AUTH_CODE_MAPPER[ALARM_NOTICE_CONFIGURATION_ROUTE_PATH]}
          composeBreadcrumbs={value => [
            ...value,
            { key: 'page_alarm-notice-configure-deatil', text: '详情' },
          ]}
        >
          <div style={{ height: 'var(--content-height)' }}>
            <AlarmNoticeConfiguration />
          </div>
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: MUTATOR_ALARM_NOTICE_CONFIGURATION_ROUTE_PATH,
    exact: true,
    children: props => {
      const isCreate =
        props.match?.params?.type === 'create' || props.match?.params?.type === 'copy';

      return (
        <DocumentTitle title={`${DOCUMENT_TITLE_PREFIX}${isCreate ? '新建' : '编辑'}告警通报配置`}>
          <LayoutContent
            pageCode={
              isCreate
                ? 'page_alarm-notice-configurations_create'
                : 'page_alarm-notice-configurations_edit'
            }
            composeBreadcrumbs={value => [
              ...value,
              {
                key: 'page_alarm-notice-configure-mutator',
                text:
                  props.match?.params?.type === 'create'
                    ? '新建'
                    : props.match?.params?.type === 'copy'
                      ? '复制'
                      : '编辑',
              },
            ]}
          >
            <AlarmNoticeConfigurationMutator />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    path: NORTH_BOUND_MANAGE,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '北向管理'}>
        <LayoutContent pageCode="page_north">
          <NorthBoundManage />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: NORTH_BOUND_CREATE,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '北向新建'}>
        <LayoutContent
          pageCode="page_north_create"
          composeBreadcrumbs={value => [...value, { key: 'page_north_create', text: '新建' }]}
        >
          <NorthBoundCreate />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: NORTH_BOUND_EDIT,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '北向编辑'}>
        <LayoutContent
          pageCode="page_north_edit"
          composeBreadcrumbs={value => [...value, { key: 'page_north_edit', text: '编辑' }]}
        >
          <NorthBoundCreate />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: PROFESSIONAL_RULE_LIST_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '专家规则列表'}>
        <LayoutContent pageCode={PROFESSIONAL_RULE_LIST_ROUTE_AUTH_CODE}>
          <ProfessionalRuleList />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: NEW_PROFESSIONAL_RULE_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '新建专家规则'}>
        <LayoutContent pageCode={NEW_PROFESSIONAL_RULE_ROUTE_AUTH_CODE}>
          <ProfessionalRuleEditor mode="new" />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: EDIT_PROFESSIONAL_RULE_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '编辑专家规则'}>
        <LayoutContent pageCode={EDIT_PROFESSIONAL_RULE_ROUTE_AUTH_CODE}>
          <ProfessionalRuleEditor mode="edit" />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: PROFESSIONAL_RULE_TEMPLATE_LIST_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '专家规则模版列表'}>
        <LayoutContent pageCode={PROFESSIONAL_RULE_TEMPLATE_LIST_ROUTE_AUTH_CODE}>
          <ProfessionalRuleTemplateList />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: NEW_PROFESSIONAL_RULE_TEMPLATE_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '新建专家规则模版'}>
        <LayoutContent pageCode={NEW_PROFESSIONAL_RULE_TEMPLATE_ROUTE_AUTH_CODE}>
          <ProfessionalRuleTemplateEditor mode="new" />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: EDIT_PROFESSIONAL_RULE_TEMPLATE_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '编辑专家规则模版'}>
        <LayoutContent pageCode={EDIT_PROFESSIONAL_RULE_TEMPLATE_ROUTE_AUTH_CODE}>
          <ProfessionalRuleTemplateEditor mode="edit" />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: CHANNEL_CONFIG_LIST,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '通道列表'}>
        <LayoutContent pageCode="page_channel-config">
          <ChannelConfigList />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: CHANNEL_CONFIG_NEW,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '新建通道'}>
        <LayoutContent
          pageCode="page_channel-config-create"
          composeBreadcrumbs={value => [
            ...value,
            { key: 'page_channel-config-create-key', text: '新建通道' },
          ]}
        >
          <ChannelConfigNew mode="new" />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: CHANNEL_CONFIG_EDIT,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '编辑通道'}>
        <LayoutContent
          pageCode="page_channel-config-edit"
          composeBreadcrumbs={value => [
            ...value,
            { key: 'page_channel-config-edit-key', text: '编辑通道' },
          ]}
        >
          <ChannelConfigNew mode="edit" />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: CHANNEL_CONFIG_COPY,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '复制通道'}>
        <LayoutContent
          pageCode="page_channel-config-copy"
          composeBreadcrumbs={value => [
            ...value,
            { key: 'page_channel-config-copy-key', text: '复制通道' },
          ]}
        >
          <ChannelConfigNew mode="copy" />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: CHANNEL_CONFIG_DETAIL,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '通道详情'}>
        <LayoutContent pageCode="page_channel-config-info">
          <ChannelConfigDetail />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: CHANNEL_CONFIG_IMPORT,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '通道导入'}>
        <LayoutContent pageCode="page_channel-import">
          <ChannelConfigImport />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: AI_OPS_ALARM_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + 'AI-OPS监测'}>
        <LayoutContent pageCode={AI_OPS_ALARM_ROUTE_PATH_CODE}>
          <AiOpsAlarms />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: AI_OPS_MONITORING_STATISTICS_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + 'AI-OPS监测状态'}>
        <LayoutContent pageCode={AI_OPS_MONITORING_STATISTICS_ROUTE_PATH_CODE}>
          <AiOpsStatistics />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: DYNAMIC_BASELINE_SETTING_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '动态基线配置'}>
        <LayoutContent pageCode={DYNAMIC_BASELINE_SETTING_ROUTE_AUTH_CODE}>
          <DynamicBaseLineSettings />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: DYNAMIC_BASELINE_SETTING_EDIT_ROUTE_PATH,
    exact: true,
    children: props => {
      const isEdit = props.match?.params?.type === 'edit';
      return (
        <DocumentTitle title={`${DOCUMENT_TITLE_PREFIX}${isEdit ? '编辑' : '复制'}动态基线配置`}>
          <LayoutContent
            pageCode={DYNAMIC_BASELINE_SETTING_EDIT_ROUTE_AUTH_CODE}
            composeBreadcrumbs={value => [
              ...value,
              {
                key: DYNAMIC_BASELINE_SETTING_EDIT_ROUTE_AUTH_CODE,
                text: `${isEdit ? '编辑' : '复制'}动态基线配置`,
              },
            ]}
          >
            <DynamicBaseLineSettingMutator />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    path: DYNAMIC_BASELINE_SETTING_CREATE_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '新建动态基线配置'}>
        <LayoutContent
          pageCode={DYNAMIC_BASELINE_SETTING_EDIT_ROUTE_AUTH_CODE}
          composeBreadcrumbs={value => [
            ...value,
            {
              key: DYNAMIC_BASELINE_SETTING_EDIT_ROUTE_AUTH_CODE,
              text: '新建动态基线配置',
            },
          ]}
        >
          <DynamicBaseLineSettingMutator />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: LOCAL_ALARM_RULE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '告警规则配置'}>
        <LayoutContent pageCode={LOCAL_ALARM_RULE_PATH_CODE}>
          <LocalAlarmRuleList />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: LOCAL_ALARM_RULE_IMPORT_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '告警规则配置'}>
        <LayoutContent pageCode={LOCAL_ALARM_RULE_IMPORT_PATH_CODE}>
          <LocalAlarmRuleImport />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: NON_POINT_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '属地设备测点配置'}>
        <LayoutContent pageCode={NON_POINT_ROUTE_AUTH_CODE}>
          <NonPoints />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: NON_POINT_IMPORT_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '属地测点导入'}>
        <LayoutContent pageCode={NON_POINT_IMPORT_ROUTE_AUTH_CODE}>
          <NonPointImport />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: LIQUID_COOLING_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '液冷监控'}>
        <LayoutContent>
          <LiquidCooling />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: LOCAL_ALARM_NOTICE_CONFIGURATIONS_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '属地告警通报配置'}>
        <LayoutContent pageCode={LOCAL_ALARM_NOTICE_CONFIGURATIONS_ROUTE_AUTH_CODE}>
          <LocalAlarmNoticeConfigurations />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: LOCAL_ALARM_NOTICE_CONFIGURATION_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '属地告警通报配置详情'}>
        <LayoutContent
          pageCode={LOCAL_ALARM_NOTICE_CONFIGURATION_ROUTE_AUTH_CODE}
          composeBreadcrumbs={value => [
            ...value,
            {
              key: LOCAL_ALARM_NOTICE_CONFIGURATION_ROUTE_AUTH_CODE,
              text: '详情',
            },
          ]}
        >
          <LocalAlarmNoticeConfiguration />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: LOCAL_EDIT_ALARM_NOTICE_CONFIGURATION_ROUTE_PATH,
    exact: true,
    children: props => {
      const isEdit = props.match?.params?.type === 'edit';
      return (
        <DocumentTitle
          title={`${DOCUMENT_TITLE_PREFIX}${
            isEdit ? '属地告警通报配置编辑' : '属地告警通报配置复制'
          }`}
        >
          <LayoutContent
            pageCode={LOCAL_EDIT_ALARM_NOTICE_CONFIGURATION_ROUTE_PATH_AUTH_CODE}
            composeBreadcrumbs={value => [
              ...value,
              {
                key: LOCAL_EDIT_ALARM_NOTICE_CONFIGURATION_ROUTE_PATH_AUTH_CODE,
                text: `${isEdit ? '编辑' : '复制'}`,
              },
            ]}
          >
            <LocalAlarmNoticeMutator />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    path: LOCAL_CREATE_ALARM_NOTICE_CONFIGURATION_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '属地告警通报配置新建'}>
        <LayoutContent
          pageCode={LOCAL_CREATE_ALARM_NOTICE_CONFIGURATION_ROUTE_PATH_AUTH_CODE}
          composeBreadcrumbs={value => [
            ...value,
            {
              key: LOCAL_CREATE_ALARM_NOTICE_CONFIGURATION_ROUTE_PATH_AUTH_CODE,
              text: '新建',
            },
          ]}
        >
          <LocalAlarmNoticeMutator />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: ALARM_SHIELD_LIST_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '告警屏蔽配置'}>
        <LayoutContent pageCode={ALARM_SHIELD_LIST_ROUTE_AUTH_CODE}>
          <AlarmShieldList />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: ALARM_SHIELD_DETAIL_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '告警屏蔽配置详情'}>
        <LayoutContent pageCode={ALARM_SHIELD_DETAIL_ROUTE_AUTH_CODE}>
          <AlarmShield />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: ALARM_SHIELD_CREATE_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '告警屏蔽配置新建'}>
        <LayoutContent pageCode={ALARM_SHIELD_CREATE_ROUTE_AUTH_CODE}>
          <AlarmShieldMutator />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: ALARM_SHIELD_EDIT_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '告警屏蔽配置编辑'}>
        <LayoutContent pageCode={ALARM_SHIELD_EDIT_ROUTE_AUTH_CODE}>
          <AlarmShieldMutator />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: CUSTOM_POINT_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '自定义测点'}>
        <LayoutContent pageCode={CUSTOM_POINT_ROUTE_CODE}>
          <MergedProcessedPointDetail isOnlyShowCustom />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
];
