import React from 'react';
// @ts-ignore: Could not find a declaration file
import DocumentTitle from 'react-document-title';

import { Result } from '@manyun/base-ui.ui.result';
import { qs } from '@manyun/base-ui.util.query-string';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import {
  AUDIT_LOG_LIST_ROUTE_AUTH_CODE,
  AUDIT_LOG_LIST_ROUTE_PATH,
  CREATE_CUSTOMER_WHITE_LIST_ROUTE_AUTH_CODE,
  CREATE_CUSTOMER_WHITE_LIST_ROUTE_PATH,
  CUSTOMER_WHITE_DETAIL_ROUTE_AUTH_CODE,
  CUSTOMER_WHITE_DETAIL_ROUTE_PATH,
  CUSTOMER_WHITE_LIST_ROUTE_AUTH_CODE,
  CUSTOMER_WHITE_LIST_ROUTE_PATH,
  UPDATE_CUSTOMER_WHITE_LIST_ROUTE_AUTH_CODE,
  UPDATE_CUSTOMER_WHITE_LIST_ROUTE_PATH,
  USERS_CREATOR_ROUTE_PATH,
  USERS_ROUTE_PATH,
  USER_PROFILE_ROUTE_PATH,
  USER_ROLE_AUTHORIZATION_ROUTE_AUTH_CODE,
  USER_ROLE_AUTHORIZATION_ROUTE_PATH,
  USER_ROUTE_PATH,
} from '@manyun/auth-hub.route.auth-routes';
import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import { LayoutContent } from '@manyun/dc-brain.ui.layout';

import type { SimpleRouteProps } from './types';

const DOCUMENT_TITLE_PREFIX = env.DOCUMENT_TITLE_PREFIX;

// -------- Pages --------

const UserProfile = React.lazy(() => import('@manyun/auth-hub.page.user-profile'));
const Users = React.lazy(() => import('@manyun/auth-hub.page.users'));
const UsersCreator = React.lazy(() => import('@manyun/auth-hub.page.users-creator'));
const UserManageDetail = React.lazy(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/user-manage/detail')
);
const AuditLogList = React.lazy(() => import('@manyun/auth-hub.page.audit-log-list'));
const CustomerWhiteList = React.lazy(() => import('@manyun/auth-hub.page.customer-white-list'));
const CustomerWhiteListMutator = React.lazy(
  () => import('@manyun/auth-hub.page.customer-white-list-mutator')
);
const CustomerWhiteDetail = React.lazy(() => import('@manyun/iam.page.customer-white-detail'));
const UserRoleAuthorization = React.lazy(
  () => import('@manyun/auth-hub.page.user-role-authorization')
);

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function AuthHubUserDetail(props: any) {
  const [authorized] = useAuthorized({ checkByCode: 'page_auth-hub_user-detail' });

  return (
    <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '用户详情'}>
      <LayoutContent pageCode="page_user">
        {authorized ? (
          <UserManageDetail {...props} />
        ) : (
          <Result status="403" title="403" subTitle="抱歉，你无权查看用户详情页面！" />
        )}
      </LayoutContent>
    </DocumentTitle>
  );
}

export const routes: SimpleRouteProps[] = [
  {
    path: USER_ROUTE_PATH,
    exact: true,
    children: props => <AuthHubUserDetail {...props} />,
  },
  {
    path: USER_PROFILE_ROUTE_PATH,
    exact: true,
    children: () => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '个人中心'}>
          <LayoutContent pageCode="__fake-page_user-profile" pageAuthorizationCheck={false}>
            <div
              style={{
                height: '100%',
                paddingTop: 'var(--breadcrumb-height)',
              }}
            >
              <UserProfile />
            </div>
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    path: USERS_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '用户管理'}>
        <LayoutContent pageCode="page_users">
          <Users />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: USERS_CREATOR_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '新建用户'}>
        <LayoutContent pageCode="page_user-create">
          <UsersCreator />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: AUDIT_LOG_LIST_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '审计日志'}>
        <LayoutContent pageCode={AUDIT_LOG_LIST_ROUTE_AUTH_CODE}>
          <AuditLogList />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: CUSTOMER_WHITE_LIST_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '合作伙伴授权'}>
        <LayoutContent pageCode={CUSTOMER_WHITE_LIST_ROUTE_AUTH_CODE}>
          <CustomerWhiteList />
        </LayoutContent>
      </DocumentTitle>
    ),
  },

  {
    path: UPDATE_CUSTOMER_WHITE_LIST_ROUTE_PATH,
    exact: true,
    children: () => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '编辑合作伙伴授权'}>
          <LayoutContent
            pageCode={UPDATE_CUSTOMER_WHITE_LIST_ROUTE_AUTH_CODE}
            composeBreadcrumbs={value => [
              ...value,
              {
                key: `${UPDATE_CUSTOMER_WHITE_LIST_ROUTE_AUTH_CODE}-key`,
                text: '编辑合作伙伴授权',
              },
            ]}
          >
            <CustomerWhiteListMutator mode="edit" />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    path: CREATE_CUSTOMER_WHITE_LIST_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '新建合作伙伴授权'}>
        <LayoutContent
          pageCode={CREATE_CUSTOMER_WHITE_LIST_ROUTE_AUTH_CODE}
          composeBreadcrumbs={value => [
            ...value,
            { key: `${CREATE_CUSTOMER_WHITE_LIST_ROUTE_AUTH_CODE}-key`, text: '新建' },
          ]}
        >
          <CustomerWhiteListMutator mode="create" />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: CUSTOMER_WHITE_DETAIL_ROUTE_PATH,
    exact: true,
    children: ({ location }) => {
      const searchParams = qs.parse(location.search);

      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '合作伙伴授权详情'}>
          <LayoutContent
            pageCode={CUSTOMER_WHITE_DETAIL_ROUTE_AUTH_CODE}
            composeBreadcrumbs={value => [
              ...value,
              {
                key: `${CUSTOMER_WHITE_DETAIL_ROUTE_AUTH_CODE}-key`,
                text: searchParams.userName ?? '',
              },
            ]}
          >
            <CustomerWhiteDetail />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    path: USER_ROLE_AUTHORIZATION_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '用户角色授权'}>
        <LayoutContent pageCode={USER_ROLE_AUTHORIZATION_ROUTE_AUTH_CODE}>
          <UserRoleAuthorization />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
];
