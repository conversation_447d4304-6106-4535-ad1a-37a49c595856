import React from 'react';

import { LoadingOutlined } from '@ant-design/icons';

import { Spin } from '@manyun/base-ui.ui.spin';
import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';

import { useLazyLoggedInUserNPermissions } from '@manyun/iam.gql.client.iam';

import type { SimpleRouteProps } from './types';

function PageSso() {
  const [getLoggedInUserNPermissions] = useLazyLoggedInUserNPermissions({
    fetchPolicy: 'no-cache',
  });
  const { redirect_url } = getLocationSearchMap<{ redirect_url?: string | null }>(
    window.location.search
  );
  React.useEffect(() => {
    if (redirect_url) {
      getLoggedInUserNPermissions().then(({ data }) => {
        if (Array.isArray(data?.permissions)) {
          window.location.replace(redirect_url);
        }
      });
    }
  }, [getLoggedInUserNPermissions, redirect_url]);

  return (
    <div
      style={{ height: '100vh', display: 'flex', justifyContent: 'center', alignItems: 'center' }}
    >
      <Spin indicator={<LoadingOutlined spin />} />
    </div>
  );
}

export const routes: SimpleRouteProps[] = [
  {
    path: '/sso',
    exact: true,
    children: <PageSso />,
  },
];
