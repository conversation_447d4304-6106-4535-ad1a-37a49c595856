import React from 'react';
// @ts-ignore: Could not find a declaration file
import DocumentTitle from 'react-document-title';

import {
  BILLS_ROUTE_PATH,
  BILL_ROUTE_PATH,
  CUSTOMERS_ROUTE_PATH,
  CUSTOMERS_SERVICE_ENTRIES_ROUTE_PATH,
  SUPPLIER_SERVICE_ENTRIES_ROUTE_PATH,
} from '@manyun/crm.route.crm-routes';
import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import { LayoutContent } from '@manyun/dc-brain.ui.layout';

import type { SimpleRouteProps } from './types';

const DOCUMENT_TITLE_PREFIX = env.DOCUMENT_TITLE_PREFIX;

const Customers = React.lazy(() => import('@manyun/crm.page.customers'));
const CustomersService = React.lazy(() => import('@manyun/crm.page.customers-service-entries'));
const SupplierServiceEntries = React.lazy(
  () => import('@manyun/crm.page.supplier-service-entries')
);
const Bills = React.lazy(() => import('@manyun/crm.page.bills'));
const BillDetail = React.lazy(() => import('@manyun/crm.page.bill-detail'));

export const routes: SimpleRouteProps[] = [
  {
    path: CUSTOMERS_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '客户列表'}>
        <LayoutContent pageCode="page_crm-customers">
          <Customers />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: CUSTOMERS_SERVICE_ENTRIES_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '服务中心 - 运维服务 - 客户服务'}>
        <LayoutContent pageCode="page_customer-service">
          <CustomersService />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: SUPPLIER_SERVICE_ENTRIES_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '服务中心 - 运维服务 - 供应商服务'}>
        <LayoutContent pageCode="page_srm-service-entries">
          <SupplierServiceEntries />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: BILLS_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '客户用量确认'}>
        <LayoutContent pageCode="page_customer-confirm">
          <Bills />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: BILL_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '客户用量确详情'}>
        <LayoutContent pageCode="page_customer-confirm-detail">
          <BillDetail />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
];
