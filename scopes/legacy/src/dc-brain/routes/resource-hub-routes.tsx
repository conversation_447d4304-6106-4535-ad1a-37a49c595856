import React from 'react';
// @ts-ignore: Could not find a declaration file
import DocumentTitle from 'react-document-title';

import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import { LayoutContent } from '@manyun/dc-brain.ui.layout';
import {
  BASIC_RESOURCES_BLOCK_CREATE_ROUTE_AUTH_CODE,
  BASIC_RESOURCES_BLOCK_CREATE_ROUTE_PATH,
  BASIC_RESOURCES_BLOCK_DETAIL_ROUTE_PATH,
  BASIC_RESOURCES_BLOCK_MUTATOR_ROUTE_AUTH_CODE,
  BASIC_RESOURCES_BLOCK_MUTATOR_ROUTE_PATH,
  BASIC_RESOURCES_FLOOR_AUTH_CODE,
  BASIC_RESOURCES_FLOOR_ROUTE_PATH,
  BORROWS_AND_RETURN_CREATE,
  BOR<PERSON>WS_AND_RETURN_CREATE_CODE,
  BORRO<PERSON>_AND_RETURN_DETAIL,
  BOR<PERSON><PERSON>_AND_RETURN_DETAIL_CODE,
  BORROWS_AND_RETURN_EDIT,
  B<PERSON>ROWS_AND_RETURN_EDIT_CODE,
  BORROWS_AND_RETURN_LIST,
  BORROWS_AND_RETURN_LIST_CODE,
  CUSTOM_POINT_IMPORT_ROUTE_AUTH_CODE,
  CUSTOM_POINT_IMPORT_ROUTE_PATH,
  DEVICE_RECORD_ROUTE_PATH,
  FLOOR_GRAPHIX_ROUTE_PATH,
  METADATA_CONFIGURATION,
  METADATA_CONFIGURATION_PAGE_CODE,
} from '@manyun/resource-hub.route.resource-routes';

import * as urls from '@manyun/dc-brain.legacy.constants/urls';

import type { SimpleRouteProps } from './types';

const ResourcehubDeviceRecordPage = React.lazy(
  () => import('@manyun/resource-hub.page.device-record')
);
const EquipmentManage = React.lazy(
  () => import('@manyun/dc-brain.legacy.pages/equipment-manage/list')
);

const BlockMutatorPage = React.lazy(() => import('@manyun/resource-hub.page.block-mutator'));
const BlockDetailPage = React.lazy(() => import('@manyun/resource-hub.page.block-detail'));
const FloorManagement = React.lazy(() => import('@manyun/resource-hub.page.floor-management'));
const FloorGraphix = React.lazy(() => import('@manyun/resource-hub.page.floor-graphix'));

const CustomPointImport = React.lazy(() => import('@manyun/resource-hub.page.custom-point-import'));
const DOCUMENT_TITLE_PREFIX = env.DOCUMENT_TITLE_PREFIX;

const MetadataConfigurationPage = React.lazy(
  () => import('@manyun/resource-hub.page.meta-data-configuration')
);

const BorrowReturn = React.lazy(() => import('@manyun/resource-hub.page.borrow-return'));

const BorrowsAndReturnCreate = React.lazy(
  () => import('@manyun/resource-hub.page.borrow-return-create')
);
const BorrowsAndReturnDetail = React.lazy(
  () => import('@manyun/resource-hub.page.borrow-return-detail')
);

export const routes: SimpleRouteProps[] = [
  {
    path: METADATA_CONFIGURATION,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '元数据配置'}>
        <LayoutContent pageCode={METADATA_CONFIGURATION_PAGE_CODE}>
          <MetadataConfigurationPage />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: DEVICE_RECORD_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '设备履历'}>
        <ResourcehubDeviceRecordPage />
      </DocumentTitle>
    ),
  },
  {
    path: urls.EQUIPMENT_LIST,
    exact: true,
    children: props => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '设备管理'}>
        <LayoutContent pageCode="page_resource-devices">
          <EquipmentManage {...props} />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: BASIC_RESOURCES_BLOCK_CREATE_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '新建楼栋'}>
        <LayoutContent
          pageCode={BASIC_RESOURCES_BLOCK_CREATE_ROUTE_AUTH_CODE}
          composeBreadcrumbs={value => [
            ...value,
            { key: BASIC_RESOURCES_BLOCK_CREATE_ROUTE_AUTH_CODE, text: '新建楼栋' },
          ]}
        >
          <BlockMutatorPage />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: BASIC_RESOURCES_BLOCK_MUTATOR_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '楼栋编辑'}>
        <LayoutContent
          pageCode={BASIC_RESOURCES_BLOCK_MUTATOR_ROUTE_AUTH_CODE}
          composeBreadcrumbs={value => [
            ...value,
            { key: BASIC_RESOURCES_BLOCK_MUTATOR_ROUTE_AUTH_CODE, text: '楼栋编辑' },
          ]}
        >
          <BlockMutatorPage />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: BASIC_RESOURCES_BLOCK_DETAIL_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '楼栋详情'}>
        <BlockDetailPage />
      </DocumentTitle>
    ),
  },
  {
    path: BASIC_RESOURCES_FLOOR_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '楼层管理'}>
        <LayoutContent pageCode={BASIC_RESOURCES_FLOOR_AUTH_CODE}>
          <FloorManagement />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: FLOOR_GRAPHIX_ROUTE_PATH,
    exact: true,
    children: <FloorGraphix />,
  },
  {
    path: BORROWS_AND_RETURN_LIST,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '借用归还'}>
        <LayoutContent pageCode={BORROWS_AND_RETURN_LIST_CODE}>
          <BorrowReturn />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: BORROWS_AND_RETURN_DETAIL,
    exact: true,
    children: props => {
      const id = props.match?.params.id;
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '借用归还详情'}>
          <LayoutContent
            pageCode={BORROWS_AND_RETURN_DETAIL_CODE}
            composeBreadcrumbs={value => [
              ...value,
              { key: 'page_borrow-return-info-key', text: id },
            ]}
          >
            <BorrowsAndReturnDetail />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    path: BORROWS_AND_RETURN_EDIT,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '借用归还编辑'}>
          <LayoutContent
            pageCode={BORROWS_AND_RETURN_EDIT_CODE}
            composeBreadcrumbs={value => [
              ...value,
              { key: 'page_borrow-return-edit-key', text: '编辑借用归还' },
            ]}
          >
            <BorrowsAndReturnCreate {...props} mode="edit" />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    path: BORROWS_AND_RETURN_CREATE,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '借用归还新建'}>
          <LayoutContent
            pageCode={BORROWS_AND_RETURN_CREATE_CODE}
            composeBreadcrumbs={value => [
              ...value,
              { key: 'page_borrow-return-create-key', text: '新建借用归还' },
            ]}
          >
            <BorrowsAndReturnCreate {...props} mode="create" />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    path: CUSTOM_POINT_IMPORT_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX}>
        <LayoutContent
          pageCode={CUSTOM_POINT_IMPORT_ROUTE_AUTH_CODE}
          composeBreadcrumbs={value => [
            ...value,
            { key: 'page_borrow-return-create-key', text: '自定义测点导入' },
          ]}
        >
          <CustomPointImport />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
];
