import React from 'react';
// @ts-ignore: Could not find a declaration file
import DocumentTitle from 'react-document-title';

import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';

import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import {
  ADMIN_ROUTER_PATH_AUTH_CODE_MAPPER,
  EXCEL_STATEMENT_DETAIL_ROUTE_PATH,
  EXCEL_STATEMENT_EDIT_ROUTE_PATH,
  EXCEL_STATEMENT_LIST_ROUTE_PATH,
  EXCEL_STATEMENT_NEW_ROUTE_PATH,
} from '@manyun/dc-brain.route.admin-routes';
import { LayoutContent } from '@manyun/dc-brain.ui.layout';

import type { SimpleRouteProps } from './types';

const DOCUMENT_TITLE_PREFIX = env.DOCUMENT_TITLE_PREFIX;

// @ts-ignore: Could not find a declaration file
const Workspace = React.lazy(() => import('@manyun_dcbrain/page.workspace'));
const ExcelStatements = React.lazy(() => import('@manyun/dc-brain.page.excel-statements'));
const ExcelStatementCreate = React.lazy(
  () => import('@manyun/dc-brain.page.excel-statement-create')
);
const ExcelStatementEdit = React.lazy(() => import('@manyun/dc-brain.page.excel-statement-edit'));
const ExcelStatementDetail = React.lazy(() => import('@manyun/dc-brain.page.excel-statement'));

export const routes: SimpleRouteProps[] = [
  {
    path: '/',
    exact: true,
    children: (
      <React.Suspense
        fallback={
          <Space
            style={{
              width: '100%',
              height: 'calc(100vh - 50px)',
              display: 'flex',
              justifyContent: 'center',
            }}
            align="center"
          >
            <Spin spinning size="large" />
          </Space>
        }
      >
        <Workspace />
      </React.Suspense>
    ),
  },
  {
    path: EXCEL_STATEMENT_LIST_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + 'Excel报表'}>
        <LayoutContent
          pageCode={ADMIN_ROUTER_PATH_AUTH_CODE_MAPPER[EXCEL_STATEMENT_LIST_ROUTE_PATH]}
        >
          <ExcelStatements />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: EXCEL_STATEMENT_NEW_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + 'Excel报表新建'}>
        <LayoutContent
          pageCode={ADMIN_ROUTER_PATH_AUTH_CODE_MAPPER[EXCEL_STATEMENT_NEW_ROUTE_PATH]}
        >
          <ExcelStatementCreate />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: EXCEL_STATEMENT_EDIT_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + 'Excel报表编辑'}>
        <LayoutContent
          pageCode={ADMIN_ROUTER_PATH_AUTH_CODE_MAPPER[EXCEL_STATEMENT_EDIT_ROUTE_PATH]}
        >
          <ExcelStatementEdit />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: EXCEL_STATEMENT_DETAIL_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + 'Excel报表详情'}>
        <LayoutContent
          pageCode={ADMIN_ROUTER_PATH_AUTH_CODE_MAPPER[EXCEL_STATEMENT_DETAIL_ROUTE_PATH]}
        >
          <ExcelStatementDetail />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
];
