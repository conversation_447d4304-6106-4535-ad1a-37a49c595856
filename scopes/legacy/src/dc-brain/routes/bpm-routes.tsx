import React from 'react';
// @ts-ignore: Could not find a declaration file
import DocumentTitle from 'react-document-title';

import {
  AUTH_REQUEST_CREATOR_ROUTE_PATH,
  CONSTRUCTION_REQUEST_CREATOR_ROUTE_PATH,
  EDIT_BPM_ROUTE_PATH,
  NEW_BPM_ROUTE_PATH,
  NEW_COMMON_REQUEST_ROUTE_AUTH_CODE,
  NEW_COMMON_REQUEST_ROUTE_PATH,
  OUT_TO_REGULAR_REQUEST_ROUTE_AUTH_CODE,
  OUT_TO_REGULAR_REQUEST_ROUTE_PATH,
  REQUEST_RNTRY_ROUTE_PATH,
} from '@manyun/bpm.route.bpm-routes';
import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import { LayoutContent } from '@manyun/dc-brain.ui.layout';

import type { SimpleRouteProps } from './types';

const DOCUMENT_TITLE_PREFIX = env.DOCUMENT_TITLE_PREFIX;

const RequestEntryPage = React.lazy(() => import('@manyun/bpm.page.request-entry'));
const AuthRequestCreatorPage = React.lazy(() => import('@manyun/bpm.page.auth-request-creator'));
const ConstructionRequest = React.lazy(() => import('@manyun/bpm.page.construction-request'));
const CommonRequestCreatorPage = React.lazy(
  () => import('@manyun/bpm.page.common-request-creator')
);
const BpmCreatePage = React.lazy(() => import('@manyun/bpm.page.bpm-editor'));

const OutSourceToRegularRequest = React.lazy(
  () => import('@manyun/bpm.page.out-to-regular-request')
);

export const bizBpmRoutes = (params: Record<string, any>) => {
  const subBizScenes = params?.subBizScenes;

  return [
    {
      path: NEW_BPM_ROUTE_PATH,
      exact: true,
      children: (
        <DocumentTitle title={`${DOCUMENT_TITLE_PREFIX}${subBizScenes || '新建流程'}`}>
          <LayoutContent
            pageCode="page_process-config-create"
            fixedContent
            composeBreadcrumbs={value => {
              return [
                ...value,
                { key: 'page_process-config-create-key', text: subBizScenes || '新建流程' },
              ];
            }}
          >
            <BpmCreatePage />
          </LayoutContent>
        </DocumentTitle>
      ),
    },
    {
      path: EDIT_BPM_ROUTE_PATH,
      exact: true,
      children: (props: Record<string, any>) => (
        <DocumentTitle title={`${DOCUMENT_TITLE_PREFIX} ${subBizScenes || '编辑流程'}`}>
          <LayoutContent
            pageCode="page_process-config-edit"
            fixedContent
            composeBreadcrumbs={value => [
              ...value,
              { key: 'page_process-config-edit-key', text: subBizScenes || '编辑流程' },
            ]}
          >
            <BpmCreatePage code={props.match?.params.code} />
          </LayoutContent>
        </DocumentTitle>
      ),
    },
  ];
};

export const routes: SimpleRouteProps[] = [
  {
    path: REQUEST_RNTRY_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '通用审批'}>
        <LayoutContent pageCode="page_bpm-requests-entry-list">
          <RequestEntryPage />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: AUTH_REQUEST_CREATOR_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '新建权限申请单'}>
        <LayoutContent
          pageCode="page_bpm-requests-entry-permissions-create"
          composeBreadcrumbs={value => [
            ...value,
            { key: 'page_bpm-requests-entry-permissions-create-key', text: '新建权限申请单' },
          ]}
        >
          <div style={{ height: 'var(--content-height)' }}>
            <AuthRequestCreatorPage />
          </div>
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: CONSTRUCTION_REQUEST_CREATOR_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '新建施工作业申请'}>
        <LayoutContent
          pageCode="page_bpm-requests-entry_construction-request-create"
          composeBreadcrumbs={value => [
            ...value,
            {
              key: 'page_bpm-requests-entry_construction-request-create-key',
              text: '新增施工作业申请',
            },
          ]}
        >
          <div style={{ height: 'var(--content-height)' }}>
            <ConstructionRequest />
          </div>
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: NEW_COMMON_REQUEST_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '新建日常通用申请'}>
        <LayoutContent
          pageCode={NEW_COMMON_REQUEST_ROUTE_AUTH_CODE}
          composeBreadcrumbs={value => [
            ...value,
            {
              key: NEW_COMMON_REQUEST_ROUTE_AUTH_CODE,
              text: '新增日常通用申请',
            },
          ]}
        >
          <CommonRequestCreatorPage />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: OUT_TO_REGULAR_REQUEST_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '新建外包转正申请'}>
        <LayoutContent
          pageCode={OUT_TO_REGULAR_REQUEST_ROUTE_AUTH_CODE}
          composeBreadcrumbs={value => [
            ...value,
            {
              key: OUT_TO_REGULAR_REQUEST_ROUTE_AUTH_CODE,
              text: '新建外包转正申请',
            },
          ]}
        >
          <OutSourceToRegularRequest />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
];
