import { loadRemote } from '@module-federation/runtime';
import React from 'react';
// @ts-ignore: Could not find a declaration file
import DocumentTitle from 'react-document-title';

import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import { LayoutContent } from '@manyun/dc-brain.ui.layout';

import type { SimpleRouteProps } from './types.js';

const DOCUMENT_TITLE_PREFIX = env.DOCUMENT_TITLE_PREFIX;

// @ts-expect-error: TS2307 because of there is no `@manyun/redash.page.dashboard`
const DashboardPage = React.lazy(() => loadRemote('@manyun_redash/page.dashboard'));

const PageStock = React.lazy(() => import('./../pages/redash/stock'));

export const routes: SimpleRouteProps[] = [
  {
    path: '/page/reports/dashboard/:id',
    exact: true,
    children: props => {
      const id = props.match?.params.id;

      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '报表'}>
          <LayoutContent pageCode={`page_reports--${id}`}>
            <DashboardPage
              dashboardId={id}
              dashboardSlug=""
              onError={() => {
                // ...
              }}
            />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    path: '/page/reports/stock',
    exact: true,
    children: <PageStock />,
  },
];
