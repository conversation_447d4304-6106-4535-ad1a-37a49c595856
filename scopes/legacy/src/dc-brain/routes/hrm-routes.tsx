import React from 'react';
// @ts-ignore: Could not find a declaration file
import DocumentTitle from 'react-document-title';

import { ThemeProvider as ChartThemeProvider } from '@manyun/base-ui.chart.theme';
import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import { LayoutContent } from '@manyun/dc-brain.ui.layout';
import {
  ANNUAL_PERFORMANCES_STATISTICS_ROUTE_PATH,
  ANNUAL_PERFORMANCE_OBJECTIVES_PREVIEW_ROUTE_PATH,
  ANNUAL_PERFORMANCE_OBJECTIVES_ROUTE_PATH,
  ANNUAL_PERFORMANCE_OBJECTIVE_CREATE_ROUTE_PATH,
  ANNUAL_PERFORMANCE_OBJECTIVE_EDITOR_ROUTE_PATH,
  ANNUAL_PERFORMANCE_PLANS_ROUTE_PATH,
  ANNUAL_PERFORMANCE_PLAN_CREATE_ROUTE_PATH,
  ANNUAL_PERFORMANCE_PLAN_EDIT_ROUTE_PATH,
  ANNUAL_PERFORMANCE_PLAN_OBJECTIVE_DETAIL_ROUTE_PATH,
  ATTENDANCE_STATISTICS_MONTHLY_AND_DAILY,
  BREAK_OFF_BALANCE_ROUTE_PATH,
  CALENDAR_CONFIGURE_ROUTE_PATH,
  GO_OUT_REQUEST_CREATOR_ROUTE_PATH,
  HOLIDAY_BALANCES_ROUTE_PATH,
  HOLIDAY_BALANCE_CALCULATE_ROUTE_PATH,
  HRM_IDC_MATCH_DEPART_ROUTE_PATH,
  HRM_PART_TIME_JOBS_MANAGE_ROUTE_PATH,
  HRM_PERFORMANCE_BLOCK_EVALUATION_ROUTE_PATH,
  HRM_PERFORMANCE_RED_LINE_CREATOR_ROUTE_PATH,
  HRM_PERFORMANCE_RED_LINE_EDITOR_ROUTE_PATH,
  HRM_PERFORMANCE_RED_LINE_RECORDS_ROUTE_PATH,
  HRM_ROUTER_PATH_AUTH_CODE_MAPPER,
  HRM_STAFF_CERTIFICATION_LIST_ROUTE_PATH,
  HRM_STAFF_CERTIFICATION_STANDARDS_LIST_ROUTE_PATH,
  HRM_STAFF_INFORMATION_LIST_ROUTE_PATH,
  HRM_STAFF_PROFILE_LIST_ROUTE_PATH,
  OT_REQUESTS_ROUTE_PATH,
  PERFORMANCE_DAILY_GRADE_CREATE_ROUTE_PATH,
  PERFORMANCE_DAILY_GRADE_EDIT_ROUTE_PATH,
  PERFORMANCE_DAILY_GRADE_RECORD_ROUTE_PATH,
  PP_PERFORMANCES_ROUTE_PATH,
  PP_PERFORMANCE_EVALUATION_DETAIL_ROUTE_PATH,
  PP_PERFORMANCE_GOALS_SETTING_ROUTE_PATH,
  PUNCH_CLOCK_IN_RECORDS_ROUTE_PATH,
  SCHEDULE_STAFF_STATISTIC_ROUTE_PATH,
  SHIFTS_ROUTE_PATH,
  SUPPLY_CHECKS_ROUTE_PATH,
  SUPPLY_CHECK_DETAIL_ROUTE_PATH,
  SUPPLY_CHECK_NEW_ROUTE_PATH,
  TEAM_ANNUAL_PERFORMANCES_ROUTE_PATH,
  TEAM_ANNUAL_PERFORMANCE_DETAIL_ROUTE_PATH,
  TEAM_ANNUAL_PERFORMANCE_OBJECTIVES_DETAIL_ROUTE_PATH,
  TEAM_PERFORMANCES_ROUTE_PATH,
  TEAM_PP_PERFORMANCES_ROUTE_PATH,
  TEAM_PP_PERFORMANCE_DETAIL_ROUTE_PATH,
  USER_ANNUAL_PERFORMANCES_DETAIL_ROUTE_PATH,
  USER_ANNUAL_PERFORMANCES_ROUTE_PATH,
  USER_ANNUAL_PERFORMANCE_OBJECTIVES_DETAIL_ROUTE_PATH,
} from '@manyun/hrm.route.hrm-routes';
import type { EvaluationDetailParams, TeamPerformancesParams } from '@manyun/hrm.route.hrm-routes';

import type { SimpleRouteProps } from './types';

const DOCUMENT_TITLE_PREFIX = env.DOCUMENT_TITLE_PREFIX;

const OtRequests = React.lazy(() => import('@manyun/hrm.page.ot-requests'));
const HolidayBalances = React.lazy(() => import('@manyun/hrm.page.holiday-balances'));
const BreakOffBalance = React.lazy(() => import('@manyun/hrm.page.break-off-balance'));
const AttStatisticsByDay = React.lazy(() => import('@manyun/hrm.page.att-statistics-by-day'));
const AttStatisticsByMonth = React.lazy(() => import('@manyun/hrm.page.att-statistics-by-month'));
const GoOutRequestCreator = React.lazy(() => import('@manyun/hrm.page.go-out-request-creator'));
const SupplyCheckCreator = React.lazy(() => import('@manyun/hrm.page.fixed-missed-punch-creator'));
const SupplyChecks = React.lazy(() => import('@manyun/hrm.page.fixed-missed-punches'));
const SupplyCheckDetail = React.lazy(() => import('@manyun/hrm.page.fixed-missed-punch'));
const PunchClockInRecords = React.lazy(() => import('@manyun/hrm.page.punch-clock-records'));
const Shifts = React.lazy(() => import('@manyun/hrm.page.shifts'));
const PPPerformanceGoalsSetting = React.lazy(
  () => import('@manyun/hrm.page.user-test-performance-goals-setting')
);
const PPUserPerformances = React.lazy(() => import('@manyun/hrm.page.user-test-performances'));
const PPPerformancesDetail = React.lazy(
  () => import('@manyun/hrm.page.user-test-performance-detail')
);

const AnnualPerformanceObjectivesPreview = React.lazy(
  () => import('@manyun/hrm.page.annual-performance-objectives-preview')
);
const AnnualPerformanceObjectives = React.lazy(
  () => import('@manyun/hrm.page.annual-performance-objectives')
);
const AnnualPerformanceObjectiveMutator = React.lazy(
  () => import('@manyun/hrm.page.annual-performance-objective-mutator')
);
const DailyPerformanceGradeRecords = React.lazy(
  () => import('@manyun/hrm.page.daily-performance-grade-records')
);
const DailyPerformanceGradeMutator = React.lazy(
  () => import('@manyun/hrm.page.daily-performance-grade-mutator')
);
const UserAnnualPerformances = React.lazy(
  () => import('@manyun/hrm.page.user-annual-performances')
);
const AnnualPerformancePlans = React.lazy(
  () => import('@manyun/hrm.page.annual-performance-plans')
);
const AnnualPerformancePlanMutator = React.lazy(
  () => import('@manyun/hrm.page.annual-performance-plan-mutator')
);
const AnnualPerformancePlanObjectives = React.lazy(
  () => import('@manyun/hrm.page.annual-performance-plan-objectives-detail')
);
const AnnualPerformanceObjectivesDetail = React.lazy(
  () => import('@manyun/hrm.page.user-annual-performance-objectives-detail')
);
const AnnualPerformanceDetail = React.lazy(
  () => import('@manyun/hrm.page.user-annal-performance-detail')
);
const TeamPerformances = React.lazy(() => import('@manyun/hrm.page.team-performances'));
const AnnualPerformanceStatistics = React.lazy(
  () => import('@manyun/hrm.page.annual-performances-statistics')
);
const HolidayBalanceCalculate = React.lazy(
  () => import('@manyun/hrm.page.holiday-balance-calculate')
);
const ScheduleStaffStatistic = React.lazy(
  () => import('@manyun/hrm.page.scheduler-staff-statistic')
);

const CalendarConfigure = React.lazy(() => import('@manyun/hrm.page.calendar-configure'));

const PerformanceBlockEvaluation = React.lazy(
  () => import('@manyun/hrm.page.performance-block-evaluation')
);

const PerformanceRedLineRecords = React.lazy(
  () => import('@manyun/hrm.page.performance-red-line-records')
);
const PerformanceRedLineMutator = React.lazy(
  () => import('@manyun/hrm.page.performance-red-line-mutator')
);

const IDCMatchDepart = React.lazy(() => import('@manyun/hrm.page.idc-match-depart'));
const StaffInformationList = React.lazy(() => import('@manyun/hrm.page.staff-information-list'));
const PartTimeJobsManage = React.lazy(() => import('@manyun/hrm.page.part-time-jobs-manage'));

const StaffProfileList = React.lazy(() => import('@manyun/hrm.page.staff-profile-list'));
const StaffCertificationStandardsList = React.lazy(
  () => import('@manyun/hrm.page.staff-certification-standards-list')
);
const StaffCertificationList = React.lazy(
  () => import('@manyun/hrm.page.staff-certifications-list')
);

export const routes: SimpleRouteProps[] = [
  {
    path: SHIFTS_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '班次列表'}>
        <LayoutContent pageCode={HRM_ROUTER_PATH_AUTH_CODE_MAPPER[SHIFTS_ROUTE_PATH]}>
          <Shifts />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: OT_REQUESTS_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '加班记录'}>
        <LayoutContent pageCode={HRM_ROUTER_PATH_AUTH_CODE_MAPPER[OT_REQUESTS_ROUTE_PATH]}>
          <OtRequests />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: HOLIDAY_BALANCES_ROUTE_PATH,
    exact: true,
    children: props => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '假期余额'}>
        <LayoutContent pageCode={HRM_ROUTER_PATH_AUTH_CODE_MAPPER[HOLIDAY_BALANCES_ROUTE_PATH]}>
          <HolidayBalances />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: BREAK_OFF_BALANCE_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '调休明细'}>
        <LayoutContent
          pageCode={HRM_ROUTER_PATH_AUTH_CODE_MAPPER[BREAK_OFF_BALANCE_ROUTE_PATH]}
          composeBreadcrumbs={value => [
            ...value,
            {
              key: HRM_ROUTER_PATH_AUTH_CODE_MAPPER[BREAK_OFF_BALANCE_ROUTE_PATH],
              text: '调休明细',
            },
          ]}
        >
          <div style={{ height: 'var(--content-height)' }}>
            <BreakOffBalance />
          </div>
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: GO_OUT_REQUEST_CREATOR_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '外勤申请'}>
        <LayoutContent
          pageCode={HRM_ROUTER_PATH_AUTH_CODE_MAPPER[GO_OUT_REQUEST_CREATOR_ROUTE_PATH]}
          composeBreadcrumbs={value => [
            ...value,
            {
              key: HRM_ROUTER_PATH_AUTH_CODE_MAPPER[GO_OUT_REQUEST_CREATOR_ROUTE_PATH],
              text: '新建外勤申请',
            },
          ]}
        >
          <GoOutRequestCreator />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: ATTENDANCE_STATISTICS_MONTHLY_AND_DAILY,
    exact: true,
    children: props => {
      const { byFunc } = props.match?.params as { byFunc: 'byDay' | 'byMonth' };
      let pageTitle = '月度汇总';
      if (byFunc === 'byDay') {
        pageTitle = '每日统计';
        return (
          <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '人员管理 - 考勤统计 - ' + pageTitle}>
            <LayoutContent pageCode="page_att-statistics-byDay">
              <AttStatisticsByDay />
            </LayoutContent>
          </DocumentTitle>
        );
      }
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '人员管理 - 考勤统计 - ' + pageTitle}>
          <LayoutContent pageCode="page_att-statistics-byMonth">
            <AttStatisticsByMonth />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    path: SUPPLY_CHECK_NEW_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '新建考勤补卡'}>
        <LayoutContent
          pageCode={HRM_ROUTER_PATH_AUTH_CODE_MAPPER[SUPPLY_CHECK_NEW_ROUTE_PATH]}
          composeBreadcrumbs={value => [
            ...value,
            {
              key: HRM_ROUTER_PATH_AUTH_CODE_MAPPER[SUPPLY_CHECK_NEW_ROUTE_PATH],
              text: '新建补卡申请',
            },
          ]}
        >
          <SupplyCheckCreator />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: SUPPLY_CHECKS_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '补卡记录列表'}>
        <LayoutContent pageCode={HRM_ROUTER_PATH_AUTH_CODE_MAPPER[SUPPLY_CHECKS_ROUTE_PATH]}>
          <SupplyChecks />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: SUPPLY_CHECK_DETAIL_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '补卡详情'}>
        <LayoutContent
          pageCode={HRM_ROUTER_PATH_AUTH_CODE_MAPPER[SUPPLY_CHECK_DETAIL_ROUTE_PATH]}
          composeBreadcrumbs={value => [
            ...value,
            { key: 'page_supply-check-info-key', text: '补卡详情' },
          ]}
        >
          <div style={{ display: 'flex' }}>
            <SupplyCheckDetail />
          </div>
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: PUNCH_CLOCK_IN_RECORDS_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '人员管理 - 考勤统计 - 原始记录'}>
        <LayoutContent
          pageCode={HRM_ROUTER_PATH_AUTH_CODE_MAPPER[PUNCH_CLOCK_IN_RECORDS_ROUTE_PATH]}
        >
          <div style={{ display: 'flex' }}>
            <PunchClockInRecords />
          </div>
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: PP_PERFORMANCE_GOALS_SETTING_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '人员管理 - 试用期绩效管理 - 个人绩效'}>
        <LayoutContent
          pageCode={HRM_ROUTER_PATH_AUTH_CODE_MAPPER[PP_PERFORMANCE_GOALS_SETTING_ROUTE_PATH]}
          composeBreadcrumbs={value => [
            ...value,
            {
              key: HRM_ROUTER_PATH_AUTH_CODE_MAPPER[PP_PERFORMANCE_GOALS_SETTING_ROUTE_PATH],
              text: '试用期目标详情',
            },
          ]}
        >
          <PPPerformanceGoalsSetting />
        </LayoutContent>
      </DocumentTitle>
    ),
  },

  {
    path: PP_PERFORMANCES_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '人员管理 - 试用期绩效管理 - 个人绩效'}>
        <LayoutContent pageCode={HRM_ROUTER_PATH_AUTH_CODE_MAPPER[PP_PERFORMANCES_ROUTE_PATH]}>
          <PPUserPerformances />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: PP_PERFORMANCE_EVALUATION_DETAIL_ROUTE_PATH,
    exact: true,
    children: props => {
      const { type } = props.match?.params as EvaluationDetailParams;
      const pageTitle = type === 'byGoal' ? '试用期目标详情' : '试用期考核详情';
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '人员管理 - 试用期绩效管理 - 个人绩效'}>
          <LayoutContent
            pageCode={HRM_ROUTER_PATH_AUTH_CODE_MAPPER[PP_PERFORMANCE_EVALUATION_DETAIL_ROUTE_PATH]}
            composeBreadcrumbs={value => [
              ...value,
              {
                key: HRM_ROUTER_PATH_AUTH_CODE_MAPPER[PP_PERFORMANCE_EVALUATION_DETAIL_ROUTE_PATH],
                text: pageTitle,
              },
            ]}
          >
            <PPPerformancesDetail />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    path: TEAM_PP_PERFORMANCE_DETAIL_ROUTE_PATH,
    exact: true,
    children: props => {
      const { type } = props.match?.params as EvaluationDetailParams;
      const pageTitle = type === 'byGoal' ? '试用期目标详情' : '试用期考核详情';
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '人员管理 - 试用期绩效管理 - 我团队的绩效'}>
          <LayoutContent
            pageCode={HRM_ROUTER_PATH_AUTH_CODE_MAPPER[TEAM_PP_PERFORMANCE_DETAIL_ROUTE_PATH]}
            composeBreadcrumbs={value => [
              ...value,
              {
                key: HRM_ROUTER_PATH_AUTH_CODE_MAPPER[TEAM_PP_PERFORMANCE_DETAIL_ROUTE_PATH],
                text: pageTitle,
              },
            ]}
          >
            <PPPerformancesDetail />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    path: ANNUAL_PERFORMANCE_OBJECTIVES_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '人员管理 - 年度绩效管理 - 指标库'}>
        <LayoutContent
          pageCode={HRM_ROUTER_PATH_AUTH_CODE_MAPPER[ANNUAL_PERFORMANCE_OBJECTIVES_ROUTE_PATH]}
        >
          <AnnualPerformanceObjectives />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: ANNUAL_PERFORMANCE_OBJECTIVE_CREATE_ROUTE_PATH,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '人员管理 - 年度绩效管理 - 指标库 - 新增'}>
          <LayoutContent
            pageCode={
              HRM_ROUTER_PATH_AUTH_CODE_MAPPER[ANNUAL_PERFORMANCE_OBJECTIVE_CREATE_ROUTE_PATH]
            }
            composeBreadcrumbs={value => [
              ...value,
              {
                key: HRM_ROUTER_PATH_AUTH_CODE_MAPPER[
                  ANNUAL_PERFORMANCE_OBJECTIVE_CREATE_ROUTE_PATH
                ],
                text: `新建指标`,
              },
            ]}
          >
            <AnnualPerformanceObjectiveMutator />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    path: ANNUAL_PERFORMANCE_OBJECTIVE_EDITOR_ROUTE_PATH,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '人员管理 - 年度绩效管理 - 指标库 - 编辑'}>
          <LayoutContent
            pageCode={
              HRM_ROUTER_PATH_AUTH_CODE_MAPPER[ANNUAL_PERFORMANCE_OBJECTIVE_EDITOR_ROUTE_PATH]
            }
            composeBreadcrumbs={value => [
              ...value,
              {
                key: HRM_ROUTER_PATH_AUTH_CODE_MAPPER[
                  ANNUAL_PERFORMANCE_OBJECTIVES_PREVIEW_ROUTE_PATH
                ],
                text: `修改指标`,
              },
            ]}
          >
            <AnnualPerformanceObjectiveMutator />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    path: ANNUAL_PERFORMANCE_OBJECTIVES_PREVIEW_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '人员管理 - 年度绩效管理 - 指标库 - 目标预览'}>
        <LayoutContent
          pageCode={
            HRM_ROUTER_PATH_AUTH_CODE_MAPPER[ANNUAL_PERFORMANCE_OBJECTIVES_PREVIEW_ROUTE_PATH]
          }
          composeBreadcrumbs={value => [
            ...value,
            {
              key: HRM_ROUTER_PATH_AUTH_CODE_MAPPER[
                ANNUAL_PERFORMANCE_OBJECTIVES_PREVIEW_ROUTE_PATH
              ],
              text: '目标预览',
            },
          ]}
        >
          <AnnualPerformanceObjectivesPreview />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: PERFORMANCE_DAILY_GRADE_RECORD_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '人员管理 - 年度绩效管理 - 日常评分记录'}>
        <LayoutContent
          pageCode={HRM_ROUTER_PATH_AUTH_CODE_MAPPER[PERFORMANCE_DAILY_GRADE_RECORD_ROUTE_PATH]}
        >
          <DailyPerformanceGradeRecords />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: PERFORMANCE_DAILY_GRADE_CREATE_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle
        title={DOCUMENT_TITLE_PREFIX + '人员管理 - 年度绩效管理 - 日常评分记录 - 创建'}
      >
        <LayoutContent
          pageCode={HRM_ROUTER_PATH_AUTH_CODE_MAPPER[PERFORMANCE_DAILY_GRADE_CREATE_ROUTE_PATH]}
          composeBreadcrumbs={value => [
            ...value,
            {
              key: HRM_ROUTER_PATH_AUTH_CODE_MAPPER[PERFORMANCE_DAILY_GRADE_CREATE_ROUTE_PATH],
              text: '添加评分记录',
            },
          ]}
        >
          <DailyPerformanceGradeMutator />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: PERFORMANCE_DAILY_GRADE_EDIT_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle
        title={DOCUMENT_TITLE_PREFIX + '人员管理 - 年度绩效管理 - 日常评分记录 - 编辑'}
      >
        <LayoutContent
          pageCode={HRM_ROUTER_PATH_AUTH_CODE_MAPPER[PERFORMANCE_DAILY_GRADE_EDIT_ROUTE_PATH]}
          composeBreadcrumbs={value => [
            ...value,
            {
              key: HRM_ROUTER_PATH_AUTH_CODE_MAPPER[PERFORMANCE_DAILY_GRADE_CREATE_ROUTE_PATH],
              text: '修改评分记录',
            },
          ]}
        >
          <DailyPerformanceGradeMutator />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: USER_ANNUAL_PERFORMANCES_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '人员管理 - 年度绩效管理 - 个人年度绩效'}>
        <LayoutContent
          pageCode={HRM_ROUTER_PATH_AUTH_CODE_MAPPER[USER_ANNUAL_PERFORMANCES_ROUTE_PATH]}
        >
          <UserAnnualPerformances />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: ANNUAL_PERFORMANCE_PLANS_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '人员管理 - 年度绩效管理 - 年度绩效目标'}>
        <LayoutContent
          pageCode={HRM_ROUTER_PATH_AUTH_CODE_MAPPER[ANNUAL_PERFORMANCE_PLANS_ROUTE_PATH]}
        >
          <AnnualPerformancePlans />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: ANNUAL_PERFORMANCE_PLAN_CREATE_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle
        title={DOCUMENT_TITLE_PREFIX + '人员管理 - 年度绩效管理 - 年度绩效目标 - 发起考核计划'}
      >
        <LayoutContent
          pageCode={HRM_ROUTER_PATH_AUTH_CODE_MAPPER[ANNUAL_PERFORMANCE_PLAN_CREATE_ROUTE_PATH]}
          composeBreadcrumbs={value => [
            ...value,
            {
              key: HRM_ROUTER_PATH_AUTH_CODE_MAPPER[ANNUAL_PERFORMANCE_PLAN_CREATE_ROUTE_PATH],
              text: '发起考核计划',
            },
          ]}
        >
          <AnnualPerformancePlanMutator />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: ANNUAL_PERFORMANCE_PLAN_EDIT_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle
        title={DOCUMENT_TITLE_PREFIX + '人员管理 - 年度绩效管理 - 年度绩效目标 - 修改配置'}
      >
        <LayoutContent
          pageCode={HRM_ROUTER_PATH_AUTH_CODE_MAPPER[ANNUAL_PERFORMANCE_PLAN_EDIT_ROUTE_PATH]}
          composeBreadcrumbs={value => [
            ...value,
            {
              key: HRM_ROUTER_PATH_AUTH_CODE_MAPPER[ANNUAL_PERFORMANCE_PLAN_EDIT_ROUTE_PATH],
              text: '修改配置',
            },
          ]}
        >
          <AnnualPerformancePlanMutator />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: ANNUAL_PERFORMANCE_PLAN_OBJECTIVE_DETAIL_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle
        title={DOCUMENT_TITLE_PREFIX + '人员管理 - 年度绩效管理 - 年度绩效目标 - 年度考核目标详情'}
      >
        <LayoutContent
          pageCode={
            HRM_ROUTER_PATH_AUTH_CODE_MAPPER[ANNUAL_PERFORMANCE_PLAN_OBJECTIVE_DETAIL_ROUTE_PATH]
          }
          composeBreadcrumbs={value => [
            ...value,
            {
              key: HRM_ROUTER_PATH_AUTH_CODE_MAPPER[
                ANNUAL_PERFORMANCE_PLAN_OBJECTIVE_DETAIL_ROUTE_PATH
              ],
              text: '年度考核目标详情',
            },
          ]}
        >
          <AnnualPerformancePlanObjectives />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: USER_ANNUAL_PERFORMANCE_OBJECTIVES_DETAIL_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle
        title={DOCUMENT_TITLE_PREFIX + '人员管理 - 年度绩效管理 - 个人年度绩效 - 年度目标详情'}
      >
        <LayoutContent
          pageCode={
            HRM_ROUTER_PATH_AUTH_CODE_MAPPER[USER_ANNUAL_PERFORMANCE_OBJECTIVES_DETAIL_ROUTE_PATH]
          }
          composeBreadcrumbs={value => [
            ...value,
            {
              key: HRM_ROUTER_PATH_AUTH_CODE_MAPPER[
                USER_ANNUAL_PERFORMANCE_OBJECTIVES_DETAIL_ROUTE_PATH
              ],
              text: '年度目标详情',
            },
          ]}
        >
          <AnnualPerformanceObjectivesDetail />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: TEAM_ANNUAL_PERFORMANCE_OBJECTIVES_DETAIL_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle
        title={DOCUMENT_TITLE_PREFIX + '人员管理 - 年度绩效管理 - 我团队的年度绩效 - 年度目标详情'}
      >
        <LayoutContent
          pageCode={
            HRM_ROUTER_PATH_AUTH_CODE_MAPPER[TEAM_ANNUAL_PERFORMANCE_OBJECTIVES_DETAIL_ROUTE_PATH]
          }
          composeBreadcrumbs={value => [
            ...value,
            {
              key: HRM_ROUTER_PATH_AUTH_CODE_MAPPER[
                TEAM_ANNUAL_PERFORMANCE_OBJECTIVES_DETAIL_ROUTE_PATH
              ],
              text: '年度目标详情',
            },
          ]}
        >
          <AnnualPerformanceObjectivesDetail />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: TEAM_PERFORMANCES_ROUTE_PATH,
    exact: true,
    children: props => {
      const { type } = props.match?.params as TeamPerformancesParams;
      const title =
        type === 'test'
          ? '人员管理 - 试用期绩效管理 - 我团队的绩效'
          : '人员管理 - 年度绩效管理 - 我团队的年度绩效';
      return (
        <DocumentTitle title={`${DOCUMENT_TITLE_PREFIX} ${title}`}>
          <LayoutContent
            pageCode={
              HRM_ROUTER_PATH_AUTH_CODE_MAPPER[
                type === 'test'
                  ? TEAM_PP_PERFORMANCES_ROUTE_PATH
                  : TEAM_ANNUAL_PERFORMANCES_ROUTE_PATH
              ]
            }
          >
            <TeamPerformances />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    path: TEAM_ANNUAL_PERFORMANCE_DETAIL_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '人员管理 - 年度绩效管理 - 我团队的绩效'}>
        <LayoutContent
          pageCode={HRM_ROUTER_PATH_AUTH_CODE_MAPPER[TEAM_ANNUAL_PERFORMANCE_DETAIL_ROUTE_PATH]}
          composeBreadcrumbs={value => [
            ...value,
            {
              key: HRM_ROUTER_PATH_AUTH_CODE_MAPPER[TEAM_ANNUAL_PERFORMANCE_DETAIL_ROUTE_PATH],
              text: '绩效考核详情',
            },
          ]}
        >
          <AnnualPerformanceDetail />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: USER_ANNUAL_PERFORMANCES_DETAIL_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle
        title={DOCUMENT_TITLE_PREFIX + '人员管理 - 年度绩效管理 -  个人年度绩效 - 绩效考核详情 '}
      >
        <LayoutContent
          pageCode={HRM_ROUTER_PATH_AUTH_CODE_MAPPER[USER_ANNUAL_PERFORMANCES_DETAIL_ROUTE_PATH]}
          composeBreadcrumbs={value => [
            ...value,
            {
              key: HRM_ROUTER_PATH_AUTH_CODE_MAPPER[USER_ANNUAL_PERFORMANCES_DETAIL_ROUTE_PATH],
              text: '绩效考核详情',
            },
          ]}
        >
          <AnnualPerformanceDetail />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: ANNUAL_PERFORMANCES_STATISTICS_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '人员管理 - 年度绩效管理 -  年度绩效报表'}>
        <LayoutContent
          pageCode={HRM_ROUTER_PATH_AUTH_CODE_MAPPER[ANNUAL_PERFORMANCES_STATISTICS_ROUTE_PATH]}
        >
          <ChartThemeProvider theme="datav_light">
            <AnnualPerformanceStatistics />
          </ChartThemeProvider>
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: HOLIDAY_BALANCE_CALCULATE_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '人员管理 - 假期余额 -  假期额度测算工具'}>
        <LayoutContent
          pageCode={HRM_ROUTER_PATH_AUTH_CODE_MAPPER[HOLIDAY_BALANCE_CALCULATE_ROUTE_PATH]}
        >
          <div style={{ height: 'var(--content-height)' }}>
            <HolidayBalanceCalculate />
          </div>
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: SCHEDULE_STAFF_STATISTIC_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '人员管理 - 排班管理 -  排班人员'}>
        <LayoutContent
          pageCode={HRM_ROUTER_PATH_AUTH_CODE_MAPPER[SCHEDULE_STAFF_STATISTIC_ROUTE_PATH]}
        >
          <div style={{ height: 'var(--content-height)' }}>
            <ScheduleStaffStatistic />
          </div>
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: CALENDAR_CONFIGURE_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '配置中心 - 基础信息配置 - 日历配置'}>
        <LayoutContent pageCode={HRM_ROUTER_PATH_AUTH_CODE_MAPPER[CALENDAR_CONFIGURE_ROUTE_PATH]}>
          <CalendarConfigure />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: HRM_PERFORMANCE_BLOCK_EVALUATION_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '人员管理 - 年度绩效管理 - 楼栋评优管理'}>
        <LayoutContent
          pageCode={HRM_ROUTER_PATH_AUTH_CODE_MAPPER[HRM_PERFORMANCE_BLOCK_EVALUATION_ROUTE_PATH]}
        >
          <PerformanceBlockEvaluation />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: HRM_PERFORMANCE_RED_LINE_RECORDS_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '人员管理 - 年度绩效管理 - 运维红线记录'}>
        <LayoutContent
          pageCode={HRM_ROUTER_PATH_AUTH_CODE_MAPPER[HRM_PERFORMANCE_RED_LINE_RECORDS_ROUTE_PATH]}
        >
          <PerformanceRedLineRecords />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: HRM_PERFORMANCE_RED_LINE_CREATOR_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle
        title={DOCUMENT_TITLE_PREFIX + '人员管理 - 年度绩效管理 - 运维红线记录 - 新增'}
      >
        <LayoutContent
          pageCode={HRM_ROUTER_PATH_AUTH_CODE_MAPPER[HRM_PERFORMANCE_RED_LINE_CREATOR_ROUTE_PATH]}
          composeBreadcrumbs={value => [
            ...value,
            {
              key: HRM_ROUTER_PATH_AUTH_CODE_MAPPER[HRM_PERFORMANCE_RED_LINE_CREATOR_ROUTE_PATH],
              text: '新增红线记录',
            },
          ]}
        >
          <PerformanceRedLineMutator />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: HRM_PERFORMANCE_RED_LINE_EDITOR_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle
        title={DOCUMENT_TITLE_PREFIX + '人员管理 - 年度绩效管理 - 运维红线记录 - 编辑'}
      >
        <LayoutContent
          pageCode={HRM_ROUTER_PATH_AUTH_CODE_MAPPER[HRM_PERFORMANCE_RED_LINE_EDITOR_ROUTE_PATH]}
          composeBreadcrumbs={value => [
            ...value,
            {
              key: HRM_ROUTER_PATH_AUTH_CODE_MAPPER[HRM_PERFORMANCE_RED_LINE_CREATOR_ROUTE_PATH],
              text: '修改红线记录',
            },
          ]}
        >
          <PerformanceRedLineMutator />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: HRM_IDC_MATCH_DEPART_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '人员管理 - 人员信息管理 - 机房与部门映射'}>
        <LayoutContent pageCode={HRM_ROUTER_PATH_AUTH_CODE_MAPPER[HRM_IDC_MATCH_DEPART_ROUTE_PATH]}>
          <IDCMatchDepart />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: HRM_STAFF_INFORMATION_LIST_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '人员管理 - 人员信息管理 - 员工信息表'}>
        <LayoutContent
          pageCode={HRM_ROUTER_PATH_AUTH_CODE_MAPPER[HRM_STAFF_INFORMATION_LIST_ROUTE_PATH]}
        >
          <StaffInformationList />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: HRM_PART_TIME_JOBS_MANAGE_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '人员管理 - 人员信息管理 - 兼岗记录管理'}>
        <LayoutContent
          pageCode={HRM_ROUTER_PATH_AUTH_CODE_MAPPER[HRM_PART_TIME_JOBS_MANAGE_ROUTE_PATH]}
        >
          <PartTimeJobsManage />
        </LayoutContent>
      </DocumentTitle>
    ),
  },

  {
    path: HRM_STAFF_PROFILE_LIST_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '人员管理 -员工管理 - 员工信息档案'}>
        <LayoutContent
          pageCode={HRM_ROUTER_PATH_AUTH_CODE_MAPPER[HRM_STAFF_PROFILE_LIST_ROUTE_PATH]}
        >
          <StaffProfileList />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: HRM_STAFF_CERTIFICATION_STANDARDS_LIST_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '人员管理 -员工管理 - 资质认证管理'}>
        <LayoutContent
          pageCode={
            HRM_ROUTER_PATH_AUTH_CODE_MAPPER[HRM_STAFF_CERTIFICATION_STANDARDS_LIST_ROUTE_PATH]
          }
        >
          <StaffCertificationStandardsList />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    path: HRM_STAFF_CERTIFICATION_LIST_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '人员管理 -员工管理 - 员工资质证书档案'}>
        <LayoutContent
          pageCode={HRM_ROUTER_PATH_AUTH_CODE_MAPPER[HRM_STAFF_CERTIFICATION_LIST_ROUTE_PATH]}
        >
          <StaffCertificationList />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
];
