import React from 'react';

// @ts-ignore: Could not find a declaration file
import { POINTS_CHART } from '@manyun/monitoring.route.monitoring-routes';

import { SimpleRouteProps } from './types';

const PointsChart = React.lazy(() => import('@manyun/monitoring.page.points-chart'));

export const routes: SimpleRouteProps[] = [
  {
    path: POINTS_CHART,
    exact: true,
    children: <PointsChart />,
  },
];
