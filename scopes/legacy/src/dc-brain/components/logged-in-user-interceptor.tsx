import React from 'react';

import { Spin } from '@manyun/base-ui.ui.spin';

import { useLoggedInUserNPermissions } from '@manyun/iam.gql.client.iam';
import type { QueryLoggedInUserNPermissionsData } from '@manyun/iam.gql.client.iam';

export type LoggedInUser = NonNullable<QueryLoggedInUserNPermissionsData['me']>;
export type LoggedInUserInterceptorProps = {
  children: (provided: { loggedInUser: LoggedInUser }) => JSX.Element;
};

export function LoggedInUserInterceptor(props: LoggedInUserInterceptorProps) {
  const [data, setData] = React.useState<QueryLoggedInUserNPermissionsData>();

  useLoggedInUserNPermissions({
    fetchPolicy: 'network-only',
    onCompleted(data) {
      setData(data);
    },
  });

  if (!data) {
    return <Spin spinning delay={500} tip="Loading logged in user and permissions..." />;
  }

  if (!data.me) {
    return <>Logged in user required.</>;
  }

  return props.children({
    loggedInUser: data.me,
  });
}
