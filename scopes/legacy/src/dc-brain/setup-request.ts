/* eslint-disable @typescript-eslint/no-explicit-any */
import { nanoid } from 'nanoid';

import { handleAuthorizationExpired, handleOtherDeviceOnline } from './utils/handle-passive-logout';

/** 因被动登出导致的请求报错 Error */
const passiveLogoutError: {
  error: {
    code?: string;
    message: string;
  };
} = {
  error: { code: undefined, message: 'should not display this message' },
};

export const setupRequest = (request: any) => {
  if (!request.axiosInstance.defaults.baseURL) {
    request.axiosInstance.defaults.baseURL = '/api';
  }

  request.useInterceptor('request', (reqConfig: any) => {
    if (!reqConfig.headers) {
      reqConfig.headers = {};
    }
    reqConfig.headers['x-app'] = 'Web/DC Base';

    const traceId = nanoid() + Date.now().toString();
    reqConfig.headers['x-trace-id'] = traceId;

    const roleCode = localStorage.getItem('roleCode');
    if (roleCode !== null) {
      reqConfig.headers['x-role'] = roleCode;
    }

    return reqConfig;
  });

  request.useInterceptor('response', undefined, (rejectedRes: any) => {
    if (rejectedRes.error?.code === 'UN_LOGIN' || rejectedRes.error?.code === 'UNAUTHENTICATED') {
      handleAuthorizationExpired();
      return passiveLogoutError;
    }
    if (rejectedRes.error?.code === 'FORCE_LOGOUT') {
      handleOtherDeviceOnline();
      return passiveLogoutError;
    }
    return Promise.reject(rejectedRes);
  });
};
