import { Link, useHistory, useLocation, useParams } from 'react-router-dom';

import type { LinkProps, RouterContextType } from '@manyun/dc-brain.navigation.link';

export const reactRouter5Adapter: RouterContextType = {
  Link: ({ href, ...props }: LinkProps) => <Link to={href!} {...props} />,
  useLocation,
  useNavigate,
  useParams,
};

function useNavigate() {
  const history = useHistory();

  return (
    target: string | number,
    options?: {
      replace?: boolean;
    }
  ) => {
    if (typeof target == 'number') {
      history.go(target);
      return;
    }

    if (typeof target == 'string') {
      if (options?.replace) {
        history.replace(target);
        return;
      }
      history.push(target);
    }
  };
}
