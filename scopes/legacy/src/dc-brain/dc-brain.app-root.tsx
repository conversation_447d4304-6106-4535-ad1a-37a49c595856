import aweLocale from '@galiojs/awesome-antd/lib/locale/zh_CN';
// import { init } from '@module-federation/runtime';
import { i18n } from '@teammc/i18n';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import moment from 'moment';
import 'moment/locale/zh-cn';
import React from 'react';
import { DndProvider } from 'react-dnd';
import Backend from 'react-dnd-html5-backend';
// Disable React 18 [automatic batching](https://react.dev/blog/2022/03/08/react-18-upgrade-guide#automatic-batching) for now
// because it could cause hidden bugs
// See http://chandao.manyun-local.com/zentao/bug-view-11752.html
import { render as ReactDom17Render } from 'react-dom';
import { initReactI18next } from 'react-i18next';
import type { PartialDeep } from 'type-fest';

// import * as requestPkg from '@glpdev/symphony.services.request';
import { WebRequest, setupRequestSingleton } from '@glpdev/symphony.services.request';
import { RouterProvider } from '@manyun/base-ui-web.context.router-context';
import { StoreProvider, history } from '@manyun/base-ui-web.context.store-context';
import { ThemeProvider as ChartThemeProvider } from '@manyun/base-ui.chart.theme';
import { ConfigProvider } from '@manyun/base-ui.context.config';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Result } from '@manyun/base-ui.ui.result';
import { Typography } from '@manyun/base-ui.ui.typography';
import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import appLocalesZhCN from '@manyun/dc-brain.config.base/src/configs/locales/zh-CN/index.mjs';
import type { AppsContextType } from '@manyun/dc-brain.context.apps';
import { AppsProvider } from '@manyun/dc-brain.context.apps';
import {
  ApolloProvider,
  CachePersistor,
  ErrorCodes,
  InMemoryCache,
  LocalStorageWrapper,
  configureBrowserClient,
} from '@manyun/dc-brain.gql.client';
import type { ApolloClient, NormalizedCacheObject } from '@manyun/dc-brain.gql.client';
import { makeDataIdFromObject } from '@manyun/dc-brain.gql.client.cache';
import { NavigationProvider } from '@manyun/dc-brain.navigation.link';
import { createRootReducer, createRootSaga, createStore } from '@manyun/dc-brain.store.store';
import { Theme } from '@manyun/dc-brain.theme.theme';
import {
  registerDataIdFromDeviceTypeTreeNodeObject,
  registerDataIdFromSpaceObject,
} from '@manyun/resource-hub.gql.client.resources';
// import * as legacyRequestPkg from '@manyun/service.request';
import { webRequest as legacyWebRequest } from '@manyun/service.request';

import { DcBrainApp } from './app';
import { reactRouter5Adapter } from './react-router-5-adapter';
import { createReducer } from './reducer';
import { createSagas } from './sagas';
import { setupRequest } from './setup-request';
import { handleAuthorizationExpired, handleOtherDeviceOnline } from './utils/handle-passive-logout';

const webRequest = WebRequest.from();
setupRequestSingleton(webRequest);
setupRequest(webRequest);
setupRequest(legacyWebRequest);

// init({
//   name: 'dc-brain-legacy',
//   remotes: [
//     {
//       name: 'redash',
//       alias: '@manyun_redash',
//       entry: `${window.mfRedashUrl}mf-manifest.json`,
//     },
//   ],
//   shared: {
//     react: {
//       version: '18.2.0',
//       scope: 'default',
//       lib: () => React,
//       shareConfig: {
//         singleton: true,
//         requiredVersion: '^18.2.0',
//       },
//     },
//     'react-dom': {
//       version: '18.2.0',
//       scope: 'default',
//       lib: () => ReactDOM,
//       shareConfig: {
//         singleton: true,
//         requiredVersion: '^18.2.0',
//       },
//     },
//     '@manyun/service.request': {
//       version: '1.0.2',
//       scope: 'default',
//       lib: () => legacyRequestPkg,
//       shareConfig: {
//         singleton: true,
//         requiredVersion: '^1.0.2',
//       },
//     },
//     '@glpdev/symphony.services.request': {
//       version: '1.0.1',
//       scope: 'default',
//       lib: () => requestPkg,
//       shareConfig: {
//         singleton: true,
//         requiredVersion: '^1.0.0',
//       },
//     },
//   },
// });

Object.keys(appLocalesZhCN).forEach(namespace => {
  i18n.current.addResourceBundle(
    'zh-CN',
    namespace,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (appLocalesZhCN as any)[namespace],
    true,
    true
  );
});

if (i18n.current) {
  //支持react-i18next
  initReactI18next.init(i18n.current);
}

moment.locale('zh-cn');
dayjs.locale('zh-cn');

const extraReducer = createReducer();
const reducer = createRootReducer({ history, extraReducer });
const store = createStore({ history, reducer });
const extraSagas = createSagas();
const saga = createRootSaga({ extraSagas });
store.runSaga(saga);

const apps: PartialDeep<AppsContextType> = {
  dcbase: {
    baseURL: '/',
  },
  sales: {
    baseURL: window.apps.sales,
  },
  finances: {
    baseURL: window.apps.finances,
  },
};

const _dataIdFromObject = makeDataIdFromObject(
  registerDataIdFromDeviceTypeTreeNodeObject,
  registerDataIdFromSpaceObject
);

const App = () => {
  const [client, setClient] = React.useState<ApolloClient<NormalizedCacheObject> | undefined>(
    undefined
  );

  React.useEffect(() => {
    (async () => {
      const cache = new InMemoryCache({
        dataIdFromObject(responseObject) {
          switch (responseObject.__typename) {
            case 'Performance':
              return `Performance:${responseObject.rowKey}`;
            case 'AnnualPerformanceObjective':
              return `AnnualPerformanceObjective:${responseObject.rowKey}`;
            case 'PerformanceUser':
              return `PerformanceUser:${responseObject.id}_${responseObject.idc}`;
            case 'AuthResource':
              return `AuthResource:${responseObject.value}`;
            default:
              return _dataIdFromObject(responseObject);
          }
        },
      });

      const client = configureBrowserClient({
        app: 'Web/DC Base',
        cache,
        onError(error) {
          if (
            window.location.pathname === '/login' &&
            (error.code === ErrorCodes.Unauthenticated ||
              error.code === ErrorCodes.ForcedToLogOut ||
              error.code === ErrorCodes.UnLogin ||
              error.code === ErrorCodes.ForceLogout)
          ) {
            return;
          }
          if (error.code === ErrorCodes.Unauthenticated) {
            handleAuthorizationExpired();
          } else if (error.code === ErrorCodes.ForcedToLogOut) {
            handleOtherDeviceOnline();
          } else if (error.code === 'VERIFY_IDENTITY') {
            Modal.error({
              content: <>登录用户不一致，请刷新页面!</>,
              okText: <>刷新</>,
              onOk: () => {
                window.location.reload();
              },
            });
            return;
          } else {
            message.error(
              <Typography.Text
                copyable={{ text: [error.message, ...(error.extraMessages ?? [])].join('\n') }}
              >
                {error.message}
              </Typography.Text>
            );
          }
        },
      });
      setClient(client);
    })().catch(console.error);
  }, []);

  if (!client) {
    // 直接白屏得了，避免闪屏
    return null;
  }

  return (
    <ApolloProvider client={client}>
      <AppsProvider apps={apps}>
        <NavigationProvider implementation={reactRouter5Adapter}>
          <ChartThemeProvider>
            <StoreProvider store={store}>
              {/* @ts-ignore because `children` prop removed in the @types/react@^18 */}
              <DndProvider backend={Backend}>
                <RouterProvider>
                  <DcBrainApp />
                </RouterProvider>
              </DndProvider>
            </StoreProvider>
          </ChartThemeProvider>
        </NavigationProvider>
      </AppsProvider>
    </ApolloProvider>
  );
};

type ErrorBoundaryState = {
  error: Error | null;
};

class ErrorBoundary extends React.Component<{}, ErrorBoundaryState> {
  state: ErrorBoundaryState = {
    error: null,
  };

  /**
   * If true, it means we encounter the `Chunk Load Error`,
   * and we are doing a reload for the user.
   */
  _isReloading = false;

  static getDerivedStateFromError(error: Error) {
    return { error };
  }

  componentDidCatch(error: Error) {
    if (isChunkLoadingError(error) && !this._isReloading) {
      this._isReloading = true;
      // Reload current page to solve the `Chunk Load Error`
      // Ref: https://rollbar.com/blog/javascript-chunk-load-error/#:~:text=If%20you%20are%20encountering%20this,this%20should%20fix%20the%20issue.
      window.location.reload();
    } else {
      console.error('Catched error: ', error.message);
    }
  }

  render() {
    const { error } = this.state;

    return (
      // @ts-expect-error: TS2322 because of mismatch peer antd version in package `@galiojs/awesome-antd`
      <ConfigProvider locale={aweLocale}>
        <Theme prefixCls="manyun">
          {error && !isChunkLoadingError(error) ? (
            <div
              style={{
                zIndex: 999999,
                position: 'fixed',
                top: 0,
                right: 0,
                bottom: 0,
                left: 0,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                height: '100vh',
                backgroundColor: 'var(--component-background)',
              }}
              data-test-id="app-crashed"
            >
              <Result
                status="500"
                title="系统出错"
                subTitle="请联系技术支持！"
                extra={
                  <Typography.Paragraph
                    copyable={{ text: [error.name, error.message, error.stack].join('\n') }}
                  >
                    {error.message}
                  </Typography.Paragraph>
                }
              />
            </div>
          ) : (
            <App />
          )}
        </Theme>
      </ConfigProvider>
    );
  }
}

const container = document.getElementById('root');
// const root = createRoot(container!);
ReactDom17Render(<ErrorBoundary />, container);

function isChunkLoadingError(error: Error) {
  // e.g. `Loading CSS chunk 876 failed`
  return (
    String(error.name).includes('ChunkLoadError') || /loading\s.+\sfailed/i.test(String(error.name))
  );
}
