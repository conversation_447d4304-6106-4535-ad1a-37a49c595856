import { useSelector } from 'react-redux';

import { selectMyResources } from '@manyun/auth-hub.state.user';
import type { MetadataJSON } from '@manyun/resource-hub.model.metadata';

export function useDefaultLocation() {
  const myResources = useSelector(selectMyResources);
  let resources = myResources;
  let idcs = resources.filter(resource => resource?.type === 'IDC');
  let blocks = resources.filter(resource => resource?.type === 'BLOCK');
  let idcBlocks = blocks.find(item => item.parentCode === idcs[0].code);
  const Idc = idcs[0]?.code;
  const Block = isVirtualBlock(idcBlocks) ? '' : idcBlocks?.code;
  const defaultIdc = Idc ?? '';
  const defaultBlock = Block ?? '';

  return [{ defaultIdc, defaultBlock }] as const;
}

const isVirtualBlock = (resource: MetadataJSON | undefined) => {
  if (resource) {
    let codes = resource.code.split('.');
    if (resource.type === 'BLOCK' && codes[1] === codes[0]) {
      return true;
    }
    return false;
  } else {
    return true;
  }
};
