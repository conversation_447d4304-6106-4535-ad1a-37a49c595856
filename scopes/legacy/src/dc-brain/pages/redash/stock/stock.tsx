/* eslint-disable @typescript-eslint/no-explicit-any */
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import { Echarts } from '@manyun/base-ui.chart.echarts';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import type { StaticDataExportFunc } from '@manyun/base-ui.ui.file-export';
import { message } from '@manyun/base-ui.ui.message';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import { exportDeviceStockData } from '@manyun/resource-hub.service.export-device-stock-data';
import { fetchDeviceStockDetails } from '@manyun/resource-hub.service.fetch-device-stock-details';
import { fetchStockRisksData } from '@manyun/resource-hub.service.fetch-stock-risks-data';
import { selectDeviceTypes } from '@manyun/resource-hub.state.device-types';
import { DeviceTypeCascader } from '@manyun/resource-hub.ui.device-type-cascader';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';

import styles from './stock.module.less';
import { useDefaultLocation } from './use-default-location';

const getColumns = (entities: any) => [
  {
    title: '机房编号',
    dataIndex: 'idcTag',
    ellipsis: true,
  },
  {
    title: '楼栋编号',
    dataIndex: 'blockTag',
    ellipsis: true,
  },
  {
    title: '三级分类',
    dataIndex: 'deviceType',
    ellipsis: true,
    render: (text: string) => {
      return <span>{text ? entities[text]?.metaName : '--'}</span>;
    },
  },
  {
    title: '厂商',
    dataIndex: 'vendor',
    ellipsis: true,
  },
  {
    title: '型号',
    dataIndex: 'productModel',
    ellipsis: true,
  },
  {
    title: '实物库存',
    dataIndex: 'count',
    ellipsis: true,
  },
  {
    title: '可用库存',
    dataIndex: 'enableNum',
    ellipsis: true,
  },
  {
    title: '不可用库存',
    dataIndex: 'unEnableNum',
    ellipsis: true,
  },
];
export function Stock() {
  const [{ defaultIdc, defaultBlock }] = useDefaultLocation();
  // tabs key
  const [tabsKey, setTabsKey] = useState('device');
  // 表示统计设备变化的时间的变量
  const [allChange, setAllChange] = useState(false);
  // 表示统计耗材变化的时间的变量
  const [allSpareChange, setAllSpareChange] = useState(false);

  // 设备楼栋
  const [idcDevice, setIdcDevice] = useState('');
  // 耗材楼栋
  const [idcSpare, setIdcSpare] = useState('');
  // 设备库存明细的三级分类
  const [deviceType, setDeviceType] = useState('');
  // 设备库存安全风险的三级分类
  const [deviceTypeRisk, setDeviceTypeRisk] = useState('');
  // 耗材三级分类
  const [spareType, setSpareType] = useState('');
  // 设备数据更新时间
  const [refreshTime, setRefreshTime] = useState('');
  // 耗材数据更新时间
  const [spareRefreshTime, setSpareRefreshTime] = useState('');
  // 设备库存明细表的数据
  const [deviceData, setDeviceData] = useState<any>([]);
  const [deviceDataLoading, setDeviceDataLoading] = useState(true);
  const [exportLoading, setExportLoading] = useState(false);
  // 设备库存安全风险
  const [deviceRiskList, setDeviceRiskList] = useState<any>([]);
  // 耗材库存安全风险数据
  const [spareRiskList, setSpareRiskList] = useState<any>([]);
  // 设备安全风险数据筛选
  const [deviceBarFilterData, setDeviceBarFilterData] = useState('DEVICE_TYPE_DIMENSION');
  // 耗材安全风险数据筛选
  const [spareBarFilterData, setSpareBarFilterData] = useState('DEVICE_TYPE_DIMENSION');
  // 设备楼栋,需要默认值
  const [deviceBlock, setDeviceBlock] = useState<string>('');
  // 耗材楼栋,需要默认值
  const [spareBlock, setSpareBlock] = useState<string>('');

  // 统计
  const [total, setTotal] = useState(0);

  // 分页数据
  const [pagination, setPagination] = useState({ pageNum: 1, pageSize: 10 });
  const handleSearch = React.useCallback(
    (idcDevice: string, deviceType: string, pageNum?: number, pageSize?: number) => {
      setPagination({ pageNum: pageNum || 1, pageSize: pageSize || 10 });
      fetchDeviceStockDetailsList(idcDevice, deviceType, pageNum, pageSize);
    },
    []
  );

  const pageHandler = React.useCallback(
    (idcDevice: string, deviceType: string, _page?: number, _pageSize?: number) => {
      handleSearch(idcDevice, deviceType, _page, _pageSize);
    },
    [handleSearch]
  );

  const barRatioOptions = [
    { label: '按设备分类', value: 'DEVICE_TYPE_DIMENSION' },
    { label: '按厂商型号', value: 'VENDOR_PRO_DIMENSION' },
  ];

  const { entities } = useSelector(selectDeviceTypes);
  useEffect(() => {
    setDeviceDataLoading(true);
    const fetchDeviceStockDetailsList = async (idcTag: string) => {
      const { error, data } = await fetchDeviceStockDetails({ idcTag });
      if (error) {
        message.error(error.message);
      } else {
        setDeviceData(data.data);
        setTotal(data.total);
        setDeviceDataLoading(false);
      }
    };

    if (defaultIdc.length) {
      setIdcDevice(defaultIdc);
      setIdcSpare(defaultIdc);
      setDeviceBlock(defaultBlock);
      setSpareBlock(defaultBlock);
      fetchDeviceStockDetailsList(defaultIdc);
      setRefreshTime(dayjs().format('YYYY-MM-DD HH:mm:ss'));
      setSpareRefreshTime(dayjs().format('YYYY-MM-DD HH:mm:ss'));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [defaultIdc, defaultBlock]);

  useEffect(() => {
    setRefreshTime(dayjs().format('YYYY-MM-DD HH:mm:ss'));
  }, [allChange]);

  useEffect(() => {
    setSpareRefreshTime(dayjs().format('YYYY-MM-DD HH:mm:ss'));
  }, [allSpareChange]);

  useEffect(() => {
    // 获取设备\耗材风险数据
    const fetchStockRisksList = async (
      assetType: string,
      blockGuid: string,
      deviceType: string,
      statisticalDimension: string
    ) => {
      const { error, data } = await fetchStockRisksData({
        assetType,
        blockGuid,
        deviceType,
        statisticalDimension,
      });
      if (error) {
        message.error(error.message);
      } else {
        if (assetType === 'DEVICE') {
          setDeviceRiskList(data.data);
        } else {
          setSpareRiskList(data.data);
        }
      }
    };
    if (idcDevice.length && tabsKey === 'device') {
      if (deviceBlock.includes('.')) {
        fetchStockRisksList('DEVICE', deviceBlock, deviceTypeRisk, deviceBarFilterData);
      } else if (deviceBlock.length) {
        message.destroy();
        message.warn('您未有该机房楼栋资源权限，请联系管理员');
        // setDeviceBlock('');
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [deviceBlock]);

  useEffect(() => {
    // 获取设备\耗材风险数据
    const fetchStockRisksList = async (
      assetType: string,
      blockGuid: string,
      deviceType: string,
      statisticalDimension: string
    ) => {
      const { error, data } = await fetchStockRisksData({
        assetType,
        blockGuid,
        deviceType,
        statisticalDimension,
      });
      if (error) {
        message.error(error.message);
      } else {
        if (assetType === 'DEVICE') {
          setDeviceRiskList(data.data);
        } else {
          setSpareRiskList(data.data);
        }
      }
    };

    if (idcDevice.length && tabsKey === 'device') {
      if (deviceBlock.includes('.')) {
        fetchStockRisksList('DEVICE', deviceBlock, deviceTypeRisk, deviceBarFilterData);
      } else if (deviceBlock.length) {
        message.warn('您未有该机房楼栋资源权限，请联系管理员');
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [deviceBarFilterData]);

  useEffect(() => {
    // 获取设备\耗材风险数据
    const fetchStockRisksList = async (
      assetType: string,
      blockGuid: string,
      deviceType: string,
      statisticalDimension: string
    ) => {
      const { error, data } = await fetchStockRisksData({
        assetType,
        blockGuid,
        deviceType,
        statisticalDimension,
      });
      if (error) {
        message.error(error.message);
      } else {
        if (assetType === 'DEVICE') {
          setDeviceRiskList(data.data);
        } else {
          setSpareRiskList(data.data);
        }
      }
    };
    if (idcSpare.length && tabsKey === 'spare') {
      if (spareBlock.includes('.')) {
        fetchStockRisksList('SPARE', spareBlock, spareType, spareBarFilterData);
      } else if (spareBlock.length) {
        message.warn('您未有该机房楼栋资源权限，请联系管理员');
        // setSpareBlock('');
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [spareBlock]);

  useEffect(() => {
    const fetchStockRisksList = async (
      assetType: string,
      blockGuid: string,
      deviceType: string,
      statisticalDimension: string
    ) => {
      const { error, data } = await fetchStockRisksData({
        assetType,
        blockGuid,
        deviceType,
        statisticalDimension,
      });
      if (error) {
        message.error(error.message);
      } else {
        if (assetType === 'DEVICE') {
          setDeviceRiskList(data.data);
        } else {
          setSpareRiskList(data.data);
        }
      }
    };
    if (idcSpare.length && tabsKey === 'spare') {
      if (spareBlock.includes('.')) {
        fetchStockRisksList('SPARE', spareBlock, spareType, spareBarFilterData);
      } else if (spareBlock.length) {
        message.warn('您未有该机房楼栋资源权限，请联系管理员');
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [spareBarFilterData]);

  // 获取设备库存明细
  const fetchDeviceStockDetailsList = async (
    idcTag: string,
    deviceType?: string,
    pageNum?: number,
    pageSize?: number
  ) => {
    setDeviceDataLoading(true);

    const { error, data } = await fetchDeviceStockDetails({
      idcTag,
      deviceType,
      pageNum,
      pageSize,
    });
    if (error) {
      message.error(error.message);
    } else {
      // :selectDeviceTypesEntities([item.deviceType])
      setDeviceData(
        data.data.map(item => {
          return item;
        })
      );
      setTotal(data.total);

      setDeviceDataLoading(false);
    }
  };

  // 获取设备\耗材风险数据
  const fetchStockRisksList = async (
    assetType: string,
    blockGuid: string | null,
    deviceType: string,
    statisticalDimension: string
  ) => {
    if (blockGuid) {
      const { error, data } = await fetchStockRisksData({
        assetType,
        blockGuid,
        deviceType,
        statisticalDimension,
      });
      if (error) {
        message.error(error.message);
      } else {
        if (assetType === 'DEVICE') {
          setDeviceRiskList(data.data);
        } else {
          setSpareRiskList(data.data);
        }
      }
    } else {
      return message.warn('您未有该机房楼栋资源权限，请联系管理员');
    }
  };

  const handleFileExport: StaticDataExportFunc = async type => {
    if (type === 'all') {
      setExportLoading(true);
      const params = {
        idcTag: idcDevice,
        deviceType: '',
      };
      const { error, data } = await exportDeviceStockData(params);
      setExportLoading(false);
      if (error) {
        message.error(error.message);
        return error.message;
      }
      return data;
    } else {
      setExportLoading(true);
      const params = {
        idcTag: idcDevice,
        deviceType: deviceType,
      };
      const { error, data } = await exportDeviceStockData(params);
      setExportLoading(false);
      if (error) {
        message.error(error.message);
        return error.message;
      }
      return data;
    }
  };

  return (
    <Tabs
      activeKey={tabsKey}
      onChange={key => {
        setTabsKey(key);
      }}
    >
      <Tabs.TabPane key="device" tab="设备">
        <div className={styles.devicePart} aria-label="device">
          <Space direction="vertical" size="large">
            <Card bordered={false}>
              <div className={styles.searchCard}>
                <div className={styles.searchBar}>
                  <Space size="large">
                    <Space>
                      <span>机房</span>
                      <LocationCascader
                        style={{ width: '200px' }}
                        nodeTypes={['IDC']}
                        authorizedOnly
                        value={[idcDevice]}
                        onChange={value => {
                          // @ts-ignore types
                          setIdcDevice(value?.[0]);
                        }}
                        allowClear={false}
                      />
                    </Space>
                    <Space>
                      <Button
                        type="primary"
                        onClick={() => {
                          fetchDeviceStockDetailsList(idcDevice, deviceType);
                          fetchStockRisksList(
                            'DEVICE',
                            deviceBlock,
                            deviceTypeRisk,
                            deviceBarFilterData
                          );
                          setAllChange(!allChange);
                        }}
                      >
                        搜索
                      </Button>
                      <Button
                        onClick={() => {
                          // todo change ec01 to default value

                          fetchDeviceStockDetailsList(defaultIdc, deviceType);

                          fetchStockRisksList(
                            'DEVICE',
                            defaultBlock,
                            deviceTypeRisk,
                            deviceBarFilterData
                          );
                          setIdcDevice(defaultIdc);
                          setDeviceBlock(defaultBlock);
                          setAllChange(!allChange);
                        }}
                      >
                        重置
                      </Button>
                    </Space>
                  </Space>
                </div>
                <span>统计时间: {refreshTime}</span>
              </div>
            </Card>
            <div className={styles.deviceStockCard}>
              <Card title="库存明细" bordered={false}>
                <Space direction="vertical" size="middle">
                  <div className={styles.searchCard}>
                    <div className={styles.searchBar}>
                      <Space size="large">
                        <Space>
                          <span>三级分类</span>
                          <DeviceTypeCascader
                            style={{ width: '200px' }}
                            numbered
                            allowClear
                            dataType={['snDevice']}
                            disabledTypeList={['C0', 'C1']}
                            value={deviceType === '' ? undefined : deviceType}
                            onChange={value => {
                              setDeviceType(value);
                            }}
                          />
                        </Space>
                        <Space>
                          <Button
                            type="primary"
                            onClick={() => fetchDeviceStockDetailsList(idcDevice, deviceType)}
                          >
                            搜索
                          </Button>
                          {/* 问下产品重置什么 */}
                          <Button
                            onClick={() => {
                              // todo change '' to default value
                              setDeviceType('');
                              fetchDeviceStockDetailsList(idcDevice, '');
                            }}
                          >
                            重置
                          </Button>
                        </Space>
                      </Space>
                    </div>
                    <FileExport
                      text="导出"
                      filename="设备库存明细表.xls"
                      disabled={exportLoading || !deviceData.length}
                      showExportFiltered
                      data={type => {
                        return handleFileExport(type);
                      }}
                    />
                  </div>
                  <Table
                    loading={deviceDataLoading}
                    columns={getColumns(entities)}
                    dataSource={deviceData}
                    rowKey={record =>
                      `${record?.deviceType}${record.idcTag}${record?.blockTag}${record?.vendor}${record?.productModel}`
                    }
                    pagination={{
                      total,
                      current: pagination.pageNum,
                      pageSize: pagination.pageSize,
                    }}
                    onChange={pagination => {
                      pageHandler(idcDevice, deviceType, pagination.current, pagination.pageSize);
                    }}
                  />
                </Space>
              </Card>
            </div>
            <div className={styles.riskCard}>
              <Card title="库存安全风险" bordered={false}>
                <Space direction="vertical" style={{ width: '100%' }} size="middle">
                  <div className={styles.searchCard}>
                    <div className={styles.searchBar}>
                      <Space size="large">
                        <Space>
                          <span>三级分类</span>
                          <DeviceTypeCascader
                            dataType={['snDevice']}
                            // @ts-ignore types
                            category="categorycode"
                            disabledTypeList={['C0', 'C1']}
                            numbered
                            value={deviceTypeRisk === '' ? undefined : deviceTypeRisk}
                            allowClear
                            style={{ width: '200px' }}
                            onChange={value => {
                              setDeviceTypeRisk(value);
                            }}
                          />
                        </Space>
                        <Space>
                          <Button
                            type="primary"
                            onClick={() =>
                              fetchStockRisksList(
                                'DEVICE',
                                deviceBlock,
                                deviceTypeRisk,
                                deviceBarFilterData
                              )
                            }
                          >
                            搜索
                          </Button>
                          <Button
                            onClick={() => {
                              // todo change '' to default value
                              setDeviceTypeRisk('');
                              fetchStockRisksList('DEVICE', deviceBlock, '', deviceBarFilterData);
                            }}
                          >
                            重置
                          </Button>
                        </Space>
                      </Space>
                    </div>
                  </div>
                  <div className={styles.deviceRiskBar}>
                    <Radio.Group
                      options={barRatioOptions}
                      value={deviceBarFilterData}
                      optionType="button"
                      onChange={e => {
                        setDeviceBarFilterData(e.target.value);
                      }}
                    />
                    <LocationCascader
                      style={{ width: '120px' }}
                      nodeTypes={['BLOCK']}
                      idc={idcDevice}
                      value={deviceBlock.split('.')}
                      authorizedOnly
                      onChange={value => {
                        const blockGuid = (value?.[0] ?? '') as string;
                        setDeviceBlock(blockGuid);
                      }}
                      onTreeDataChange={item => {
                        if (idcDevice.length) {
                          if (item?.length) {
                            setDeviceBlock(item[0].value);
                          }
                        }
                      }}
                    />
                  </div>
                  <Echarts
                    option={{
                      legend: {
                        lineStyle: {
                          // 该颜色为@manyun-error-color
                          color: '#ff4d4f',
                          type: 'solid',
                        },
                      },
                      tooltip: {
                        trigger: 'axis',
                        formatter: function (params: any) {
                          var result = '<div>' + params[0].axisValue + '</div>';
                          params.forEach(function (item: any, index: any) {
                            result += `<span style="display:inline-block;margin-right:5px;margin-bottom:2px;border-radius:10px;width:9px;height:9px;background-color:${
                              item.seriesType === 'line' ? '#ff4d4f' : item.color
                            }"></span>`;
                            result += item.seriesName + '：' + item.data + '<br>';
                          });
                          return result;
                        },
                      },

                      xAxis: {
                        type: 'category',
                        data:
                          deviceBarFilterData === 'DEVICE_TYPE_DIMENSION'
                            ? deviceRiskList.map(
                                (item: any) => `${entities[item.deviceType]?.metaName}`
                              )
                            : deviceRiskList.map(
                                (item: any) =>
                                  `${entities[item.deviceType]?.metaName}-${item.vendor ?? '--'}/${
                                    item.productModel ?? '--'
                                  }`
                              ),

                        axisLabel: {
                          show: true,
                          interval: 0,
                          formatter: function (value: any) {
                            return value.length > 6 ? value.slice(0, 6) + '...' : value;
                          },
                        },
                      },
                      yAxis: {
                        type: 'value',
                      },
                      series: [
                        {
                          data: deviceRiskList.map((item: any) => item.enableNum),
                          name: '实时值',
                          type: 'bar',
                          showBackground: true,
                          itemStyle: {
                            color: '#177ddc',
                          },
                          backgroundStyle: {
                            color: 'rgba(180, 180, 180, 0.2)',
                          },
                        },
                        {
                          name: '库存阈值',
                          type: 'line',
                          symbol: 'circle',

                          data: deviceRiskList.map((item: any) => item.level),
                          lineStyle: {
                            color: '#ff4d4f',
                            type: 'dashed',
                          },
                          itemStyle: {
                            borderColor: '#ff4d4f',
                            color: 'white',
                          },
                        },
                      ],
                    }}
                  />
                </Space>
              </Card>
            </div>
          </Space>
        </div>
      </Tabs.TabPane>
      <Tabs.TabPane key="spare" tab="耗材">
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <Card bordered={false}>
            <div className={styles.searchCard}>
              <div className={styles.searchBar}>
                <Space size="large">
                  <Space>
                    <span>机房</span>
                    <LocationCascader
                      style={{ width: '200px' }}
                      nodeTypes={['IDC']}
                      authorizedOnly
                      value={[idcSpare]}
                      onChange={value => {
                        // @ts-ignore types
                        setIdcSpare(value?.[0]);
                      }}
                      allowClear={false}
                    />
                  </Space>
                  <Space>
                    <Button
                      type="primary"
                      onClick={() => {
                        fetchStockRisksList('SPARE', spareBlock, spareType, spareBarFilterData);
                        setAllSpareChange(!allSpareChange);
                      }}
                    >
                      搜索
                    </Button>
                    <Button
                      onClick={() => {
                        // todo change ec01 to default value
                        setIdcSpare(defaultIdc);
                        setSpareBlock(defaultBlock);
                        fetchStockRisksList('SPARE', defaultBlock, '', spareBarFilterData);
                        setAllSpareChange(!allSpareChange);
                      }}
                    >
                      重置
                    </Button>
                  </Space>
                </Space>
              </div>
              <span>统计时间: {spareRefreshTime}</span>
            </div>
          </Card>
          <div className={styles.riskCard}>
            <Card title="库存安全风险" bordered={false}>
              <Space direction="vertical" style={{ width: '100%' }} size="middle">
                <div className={styles.searchCard}>
                  <div className={styles.searchBar}>
                    <Space size="large">
                      <Space>
                        <span>三级分类</span>
                        <DeviceTypeCascader
                          dataType={['noSnDevice']}
                          // @ts-ignore types
                          category="categorycode"
                          disabledTypeList={['C0', 'C1']}
                          value={spareType === '' ? undefined : spareType}
                          numbered={false}
                          allowClear
                          style={{ width: '200px' }}
                          onChange={value => {
                            setSpareType(value);
                          }}
                        />
                      </Space>
                      <Space>
                        <Button
                          type="primary"
                          onClick={() => {
                            fetchStockRisksList('SPARE', spareBlock, spareType, spareBarFilterData);
                          }}
                        >
                          搜索
                        </Button>
                        <Button
                          onClick={() => {
                            // todo change '' to default value
                            setSpareType('');
                            fetchStockRisksList('SPARE', spareBlock, '', spareBarFilterData);
                          }}
                        >
                          重置
                        </Button>
                      </Space>
                    </Space>
                  </div>
                </div>
                <div className={styles.deviceRiskBar}>
                  <Radio.Group
                    options={barRatioOptions}
                    value={spareBarFilterData}
                    optionType="button"
                    onChange={e => {
                      setSpareBarFilterData(e.target.value);
                    }}
                  />
                  <LocationCascader
                    style={{ width: '120px' }}
                    nodeTypes={['BLOCK']}
                    idc={idcSpare}
                    value={spareBlock.split('.')}
                    authorizedOnly
                    onChange={value => {
                      const blockGuid = (value?.[0] ?? '') as string;
                      setSpareBlock(blockGuid);
                    }}
                    onTreeDataChange={item => {
                      if (idcDevice.length) {
                        if (item?.length) {
                          setSpareBlock(item[0].value);
                        }
                      }
                    }}
                  />
                </div>
                <Echarts
                  option={{
                    legend: {
                      lineStyle: {
                        // 该颜色为@manyun-error-color
                        color: '#ff4d4f',
                        type: 'solid',
                      },
                    },
                    tooltip: {
                      trigger: 'axis',
                      formatter: function (params: any) {
                        var result = '<div>' + params[0].axisValue + '</div>';
                        params.forEach(function (item: any, index: any) {
                          result += `<span style="display:inline-block;margin-right:5px;margin-bottom:2px;border-radius:10px;width:9px;height:9px;background-color:${
                            item.seriesType === 'line' ? '#ff4d4f' : item.color
                          }"></span>`;
                          result += item.seriesName + '：' + item.data + '<br>';
                        });
                        return result;
                      },
                    },
                    xAxis: {
                      type: 'category',
                      data:
                        spareBarFilterData === 'DEVICE_TYPE_DIMENSION'
                          ? spareRiskList.map(
                              (item: any) => `${entities[item.deviceType]?.metaName}`
                            )
                          : spareRiskList.map(
                              (item: any) =>
                                `${entities[item.deviceType]?.metaName}-${item.vendor ?? '--'}/${
                                  item.productModel ?? '--'
                                }`
                            ),
                      axisLabel: {
                        show: true,
                        interval: 0,
                        formatter: function (value: any) {
                          return value.length > 6 ? value.slice(0, 6) + '...' : value;
                        },
                      },
                    },
                    yAxis: {
                      type: 'value',
                    },
                    series: [
                      {
                        data: spareRiskList.map((item: any) => item.enableNum),
                        name: '实时值',
                        type: 'bar',
                        showBackground: true,
                        itemStyle: {
                          color: '#177ddc',
                        },
                        backgroundStyle: {
                          color: 'rgba(180, 180, 180, 0.2)',
                        },
                      },
                      {
                        name: '库存阈值',
                        type: 'line',
                        symbol: 'circle',

                        data: spareRiskList.map((item: any) => item.level),
                        lineStyle: {
                          color: '#ff4d4f',
                          type: 'dashed',
                        },
                        itemStyle: {
                          borderColor: '#ff4d4f',
                          color: 'white',
                        },
                      },
                    ],
                  }}
                />
              </Space>
            </Card>
          </div>
        </Space>
      </Tabs.TabPane>
    </Tabs>
  );
}
