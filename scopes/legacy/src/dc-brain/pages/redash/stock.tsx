import React from 'react';
// @ts-ignore: Could not find a declaration file
import DocumentTitle from 'react-document-title';

import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import { LayoutContent } from '@manyun/dc-brain.ui.layout';

import { Stock } from './stock/index';

export default function PageStock() {
  return (
    <DocumentTitle title={`${env.DOCUMENT_TITLE_PREFIX}库存报表`}>
      <LayoutContent pageCode="page_independent-reports--stock" pageAuthorizationCheck={false}>
        <Stock />
      </LayoutContent>
    </DocumentTitle>
  );
}
