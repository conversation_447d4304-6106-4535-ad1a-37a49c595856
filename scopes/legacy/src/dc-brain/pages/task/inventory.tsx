import React from 'react';
// @ts-ignore: Could not find a declaration file
import DocumentTitle from 'react-document-title';

import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import { LayoutContent } from '@manyun/dc-brain.ui.layout';
import { PlanType } from '@manyun/ticket.model.task';
import {
  COPY_INVENTORY_TASK_CONFIGURATION_ROUTE_AUTH_CODE,
  UPDATE_INVENTORY_TASK_CONFIGURATION_ROUTE_AUTH_CODE,
} from '@manyun/ticket.route.ticket-routes';

const PlanMutator = React.lazy(() => import('@manyun/ticket.page.plan-mutator'));
const DOCUMENT_TITLE_PREFIX = env.DOCUMENT_TITLE_PREFIX;

export default function InventoryPlanMutatorPage({ mode }: { mode: 'edit' | 'copy' }) {
  return (
    <DocumentTitle title={DOCUMENT_TITLE_PREFIX + getPageTitle(mode)}>
      <LayoutContent
        pageCode={getRouteAuthCode(mode)}
        composeBreadcrumbs={value => [
          ...value,
          { key: `page_inventory-${mode}-key`, text: mode === 'edit' ? '编辑计划' : '复制计划' },
        ]}
      >
        <PlanMutator planType={PlanType.InventoryPlan} mode={mode} />
      </LayoutContent>
    </DocumentTitle>
  );
}

function getRouteAuthCode(mode: 'edit' | 'copy') {
  return mode === 'edit'
    ? UPDATE_INVENTORY_TASK_CONFIGURATION_ROUTE_AUTH_CODE
    : COPY_INVENTORY_TASK_CONFIGURATION_ROUTE_AUTH_CODE;
}

function getPageTitle(mode: 'edit' | 'copy') {
  return mode === 'edit' ? '编辑盘点计划' : '复制盘点计划';
}
