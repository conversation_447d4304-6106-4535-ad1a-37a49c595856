import React from 'react';
import { useSelector } from 'react-redux';
import { useHistory, useLocation, useRouteMatch } from 'react-router-dom';
import { useDeepCompareEffect } from 'react-use';

import { selectMe } from '@manyun/auth-hub.state.user';
import { AppAwareRoleSelect } from '@manyun/auth-hub.ui.app-aware-role-select';
import { Space } from '@manyun/base-ui.ui.space';
import { useLayout } from '@manyun/dc-brain.context.layout';
import { layoutRegistry } from '@manyun/dc-brain.ui.layout';
import type { LayoutFabricType } from '@manyun/dc-brain.ui.layout';
import { useAppIdentity, useSwitchRole } from '@manyun/iam.gql.client.iam';
import { useAuthorized } from '@manyun/iam.hook.use-authorized';
import {
  generateRoomMonitoringUrl,
  generateSpaceOrDeviceRoutePath,
} from '@manyun/monitoring.route.monitoring-routes';
import { DeviceDiffTool } from '@manyun/monitoring.ui.device-diff-tool';
import { HostIdcSelect } from '@manyun/monitoring.ui.host-idc-select';
import { PointDiffTool } from '@manyun/monitoring.ui.point-diff-tool';
import { useSpaces } from '@manyun/resource-hub.gql.client.spaces';
import { generateGraphixFloorViewRoutePath } from '@manyun/resource-hub.route.resource-routes';
import { ResourceTree, getFlatResources } from '@manyun/resource-hub.ui.resource-tree';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

type SideBartabItem = {
  Content: LayoutFabricType;
  title: string;
  key: string;
};

type SideBarProps = {
  tabList: SideBartabItem[];
};

type MonitoringHostIdcSelectProps = {
  visible: boolean;
  SideBar?: React.FC<SideBarProps>;
};

type MonitoringDropdownProps = {
  visible: boolean;
  showSeparator: boolean;
};
// const MonitoringHostIdcSelect = ({ visible }: MonitoringHostIdcSelectProps) => { 0929大屏需求：关闭右上角机房切换
//   if (!visible) {
//     return null;
//   }

//   return (
//     <Space>
//       <EnvironmentOutlined />
//       <HostIdcSelect size="small" />
//     </Space>
//   );
// };
// layoutRegistry.register('host-idc-select', MonitoringHostIdcSelect, {
//   variant: 'head-menu-item',
//   prepare: (provided): MonitoringHostIdcSelectProps => ({
//     visible: false,
//   }),
// });

const ResourceTreeTool = () => {
  const { idc } = useSelector(selectMe, (left, right) => left.idc === right.idc);

  const { search } = useLocation();
  const searchParams = new URLSearchParams(search);
  const block = searchParams.get('block');

  const history = useHistory();
  const { setSideBarVisible, currentSpaceGuid } = useLayout();

  const [expandedKeys, setExpandedKeys] = React.useState<React.Key[]>([]);
  const [selectedKeys, setSelectedKeys] = React.useState<React.Key[]>([]);

  const { data } = useSpaces({
    variables: {
      nodeTypes: ['IDC', 'BLOCK'],
      includeVirtualBlocks: false,
      authorizedOnly: true,
    },
    fetchPolicy: 'network-only',
  });

  useDeepCompareEffect(() => {
    const expandedKeys: React.Key[] = (() => {
      if (currentSpaceGuid) {
        // 因设备是异步加载的，故为了性能，只截取到楼栋
        return [currentSpaceGuid.split('.').slice(0, 2).join('.')];
      }

      if (idc && block) {
        return [`${idc}.${block}`];
      }
      //资源视图默认展开机房楼栋
      if (idc) {
        const resources = data?.spaces ? getFlatResources(data.spaces) : [];
        const currentResources = resources.filter(item => item.value.includes(idc));
        //取出机房楼栋的value用于展开机房楼栋
        return currentResources.map(item => item.value);
      }
      return [];
    })();
    setExpandedKeys(expandedKeys);
    setSelectedKeys(currentSpaceGuid ? [currentSpaceGuid] : expandedKeys);
  }, [block, currentSpaceGuid, data?.spaces, idc]);

  return (
    <ResourceTree
      style={{ height: '100vh' }}
      treeMode={[`IDC`, `BLOCK`, 'FLOOR', `ROOM`, `DEVICE`]}
      showFilter
      authorizedOnly
      spaceGuid={idc ?? undefined}
      expandedKeys={expandedKeys}
      selectedKeys={selectedKeys}
      onExpandedKeysChange={keys => setExpandedKeys(keys)}
      onSelectedKeysChange={keys => setSelectedKeys(keys)}
      onSelect={(_, info) => {
        setSideBarVisible(false);
        const { node } = info;
        const { key: spaceGuid, type } = node;
        const { idc, block, room, cabinet } = getSpaceGuidMap(spaceGuid);

        switch (type) {
          case 'IDC':
            //跳监控大屏

            break;
          case 'BLOCK':
            break;
          case 'FLOOR':
            window.open(
              generateGraphixFloorViewRoutePath({
                idc: idc!,
                block: block!,
                floor: room!,
              })
            );
            break;
          case 'ROOM':
            window.open(
              generateRoomMonitoringUrl({
                idc: idc!,
                block: block!,
                room: room!,
              })
            );
            break;
          case 'DEVICE':
            history.push(
              generateSpaceOrDeviceRoutePath({
                guid: cabinet as string,
              })
            );
            break;
          default:
            break;
        }
      }}
    />
  );
};

const useSideBartabList = (): SideBartabItem[] => {
  const tabList = [
    {
      Content: PointDiffTool,
      title: '测点对比',
      key: 'pointdifftool',
    },
    {
      Content: DeviceDiffTool,
      title: '设备对比',
      key: 'devicedifftool',
    },
    {
      Content: ResourceTreeTool,
      title: '监控视图',
      key: 'roomview',
    },
  ];
  return tabList;
};

const MonitoringHostSideBar = ({ SideBar, visible }: MonitoringHostIdcSelectProps) => {
  const [, { checkCode }] = useAuthorized();
  const sideBartabList = useSideBartabList();

  if (!visible || !SideBar || !checkCode('page_idc-workbench')) {
    return null;
  }
  return <SideBar tabList={sideBartabList} />;
};

layoutRegistry.register('side-bar', MonitoringHostSideBar, {
  variant: 'side-bar',
  prepare: (provided): MonitoringHostIdcSelectProps => ({
    SideBar: provided.SideBar,
    visible:
      provided.selectedMenuCodes[0] === 'idc_monitoring' ||
      provided.selectedMenuCodes[2] === 'basic_resource_equipment_management',
  }),
});

const MonitoringHostIdcSelect = () => {
  //后续删除这块代码
  const match = useRouteMatch('/page/monitoring/data-view/:idc');

  return (
    <Space>
      <HostIdcSelect
        showSideBarFold={!!match}
        dropdownButtonStyle={{
          marginRight: '6px',
        }}
      />
    </Space>
  );
};

// 动态注册HostIdcSelect组件
layoutRegistry.register('host-idc-select', MonitoringHostIdcSelect, {
  variant: 'breadcrumb',
  prepare: (provided): MonitoringDropdownProps => {
    return {
      visible:
        provided.selectedMenuCodes[0] === 'idc_monitoring' &&
        provided.selectedMenuCodes[1] !== 'menu_monitoring-ai-ops' &&
        provided.selectedMenuCodes[2] !== 'menu_liquid-cooling',
      showSeparator: false,
    };
  },
});

function CurrentAppRoleSelect() {
  const { data: appIdentityData } = useAppIdentity({ fetchPolicy: 'no-cache' });
  const [switchRole] = useSwitchRole();

  return (
    <AppAwareRoleSelect
      size="small"
      bordered={false}
      showArrow={false}
      dropdownMatchSelectWidth={false}
      app="DCBASE"
      value={appIdentityData?.appIdentity?.roleCode}
      onChange={(_roleCode: string, role: { id: string; code: string }) => {
        switchRole({
          variables: {
            code: role.code,
          },
          onCompleted(data) {
            if (data?.switchRole?.success) {
              window.localStorage.setItem('roleCode', role.code);
              window.location.reload();
            }
          },
        });
      }}
    />
  );
}

layoutRegistry.register('app-aware-role-select', CurrentAppRoleSelect, {
  variant: 'head-menu-item',
});
