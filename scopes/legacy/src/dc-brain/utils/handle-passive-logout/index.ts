import { message } from '@manyun/base-ui.ui.message';
import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';

import { clearLocalStorage } from '@manyun/auth-hub.cache.user';

import styles from './styles.module.less';

let logoutTimeout: number | undefined;

/**
 * 通用的处理被动登出的逻辑函数
 * @param messageContent message content
 * @param delay setTimeout delay
 */
export function handlePassiveLogout(messageContent: string, delay: number) {
  if (window.location.pathname.startsWith('/login') || typeof logoutTimeout == 'number') {
    return;
  }
  const { redirect_url } = getLocationSearchMap<{ redirect_url?: string | null }>(
    window.location.search
  );
  logoutTimeout = window.setTimeout(() => {
    let to = '/logout?redirect_url=/login';
    if (redirect_url) {
      to += `?redirect_url=${window.encodeURIComponent(redirect_url)}`;
    }
    window.location.replace(to);
    clearLocalStorage();
    logoutTimeout = undefined;
  }, delay);

  message.destroy();
  const baseUiMessageWrapper = document.createElement('div');
  baseUiMessageWrapper.className = styles.baseUiMessageWrapper;
  message.config({ getContainer: () => document.body.appendChild(baseUiMessageWrapper) });

  message.info({ content: messageContent, className: styles.content });
}

export const handleOtherDeviceOnline = () =>
  handlePassiveLogout(
    '帐号已于另一台设备登录，若非本人操作请及时更新密码，3 秒后将自动跳转到登录页面！',
    3000
  );

export const handleAuthorizationExpired = () =>
  handlePassiveLogout('检测到用户登录信息失效，2 秒后将自动跳转到登录页面！', 2000);
