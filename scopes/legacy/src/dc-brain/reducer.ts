import type { ReducersMapObject } from 'redux';

import rolesSliceReducers from '@manyun/auth-hub.state.roles';
import userSliceReducer from '@manyun/auth-hub.state.user';
import userGroupsSliceReducer from '@manyun/auth-hub.state.user-groups';
import usersSliceReducers from '@manyun/auth-hub.state.users';
import approvalCenterSliceReducer from '@manyun/bpm.state.approval-center';
import authRequestSliceReducer from '@manyun/bpm.state.auth-request';
import customersSliceReducer from '@manyun/crm.state.customers';
import staffScheduleReducer from '@manyun/hrm.state.staff-schedule';
import coursesSliceReducer from '@manyun/knowledge-hub.state.courses';
import coursewaresSliceReducer from '@manyun/knowledge-hub.state.coursewares';
import examsSliceReducer from '@manyun/knowledge-hub.state.exams';
import papersSliceReducer from '@manyun/knowledge-hub.state.papers';
import questionsSliceReducer from '@manyun/knowledge-hub.state.questions';
import skillsSliceReducer from '@manyun/knowledge-hub.state.skills';
import alarmConfigurationTemplateSliceReducer from '@manyun/monitoring.state.alarm-configuration-template';
import alarmNotificationConfigureSliceReducer from '@manyun/monitoring.state.alarm-notification-configure';
import alarmsMonitoringBoardSliceReducer from '@manyun/monitoring.state.alarms-monitoring-board';
import dashboardIdcReducer from '@manyun/monitoring.state.dashboard-idc';
import ruleTypesSliceReducer from '@manyun/monitoring.state.rule-types';
import subscriptionsSliceReducer from '@manyun/monitoring.state.subscriptions';
import topologySliceReducer from '@manyun/monitoring.state.topology';
import onSiteMessagesSliceReducer from '@manyun/notification-hub.state.on-site-messages';
import deviceTypesSliceReducer from '@manyun/resource-hub.state.device-types';
import metaDataSliceReducers from '@manyun/resource-hub.state.meta-data';
import pointsSliceReducers from '@manyun/resource-hub.state.points';
import roomTypeSliceReducer from '@manyun/resource-hub.state.room-type';
import spaceSliceReducer from '@manyun/resource-hub.state.space';
import eventSliceReducer from '@manyun/ticket.state.event';
import ticketSliceReducer from '@manyun/ticket.state.ticket-type';

import deviceSliceReducer from '@manyun/dc-brain.legacy.redux/__next/device';
import alarmConfigurationSliceReducer, {
  ALARM_CONFIGURATION_TEMPLATE_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/alarmConfigurationTemplateSlice';
import alarmScreenSliceReducer, {
  ALARM_SCREEN_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/alarmScreenSlice';
import alarmManageSliceReducer, {
  ALARM_MANAGE_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/alarmSlice';
import alarmTransmissionRecordSliceReducer, {
  ALARM_TRANSMISSION_RECORD_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/alarmTransmissionRecordSlice';
import alterInfoSliceReducer, {
  ALTER_INFO_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/alterInfoSlice';
import approvalConfigSliceReducer, {
  APPROVAL_CONFIG_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/approvalConfigSlice';
import approveCenterSliceReducer, {
  APPROVE_CENTER_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/approveCenterSlice';
import arrivalAssetSliceReducer, {
  ARRIVAL_ASSET_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/arrivalAssetSlice';
import basicResourcesBuildingSliceReducer, {
  BASIC_RESOURCES_BUILDING_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/basicResourcesBuildingSlice';
import basicResourcesIdcSliceReducer, {
  BASIC_RESOURCES_IDC_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/basicResourcesIdcSlice';
import batterySliceReducer, {
  BATTERY_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/batterySlice';
import borrowsAndReturnSliceReducer, {
  BORROW_AND_RETURN_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/borrowsAndReturnSlice';
import cabinetManageSliceReducer, {
  CABINET_MANAGE_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/cabinetSlice';
import changeSliceReducer, {
  CHANGE_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/changeSlice';
import channelConfigSliceReducer, {
  CHANNEL_CONFIG_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/channelConfigSlice';
import customAlarmConfigurationSliceReducer, {
  CUSTOM_ALARM_CONFIGURATION_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/customAlarmConfigurationSlice';
import deviceMonitoringSliceReducer, {
  DEVICE_MONITORING_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/deviceMonitoringSlice';
import deviceTypeSliceReducer, {
  DEVICE_TYPE_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/deviceTypeSlice';
import doPointManageSliceReducer, {
  DOPOINT_MANAGE_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/doPointSlice';
import equipmentManageSliceReducer, {
  EQUIPMENT_MANAGE_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/equipmentSlice';
import eventCenterListSliceReducer, {
  EVENT_CENTER_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/eventCenterSlice';
import idcWorkbenchSliceReducer, {
  IDC_WORKBENCH_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/idcWorkbenchSlice';
import infraRoomGraphixSliceReducer, {
  INFRA_ROOM_GRAPHIX_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/infraRoomGraphixSlice';
import insideNoticeSliceReducer, {
  INSIDE_NOTICE_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/insideNoticeSlice';
import inspectionConfigSliceReducer, {
  INSPECTION_CONFIG_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/inspectionConfigSlice';
import inventoryAanlyticsReducer, {
  INVENTORY_ANALYSTICS_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/inventoryAanlyticsSlice';
import inventoryConfigReducer, {
  INVENTORY_CONFIG_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/inventoryConfigSlice';
import layoutSliceReducer, {
  LAYOUT_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/layoutSlice';
import mergedMonitorConfigSliceReducer, {
  MERGED_MONITOR_CONFIG_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/mergedMonitorConfigSlice';
import mergedProcessesdPointSliceReducer, {
  MERGED_PROCESSES_POINT_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/mergedProcessesdPointSlice';
import mineTicketSliceReducer, {
  MINE_TICKET_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/mineTicketSlice';
import modelSliceReducer, {
  MODEL_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/modelSlice';
import monitoringConfigReducer, {
  MONITORING_CONFIG_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/monitoringConfigSlice';
import northUserSliceReducer, {
  NORTH_USER_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/northUserSlice';
import noticeManageSliceReducer, {
  NOTICE_MANAGE_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/noticeSlice';
import operationCheckConfigReducer, {
  OPERATION_CHECK_CONFIG_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/operationCheckConfigSlice';
import powerManageSliceReducer, {
  POWER_MANAGE_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/powerSlice';
import roleManageSliceReducer, {
  ROLE_MANAGE_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/roleSlice';
import roomMonitoringSliceReducer, {
  ROOM_MONITORING_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/roomMonitoringSlice';
import roomManageSliceReducer, {
  ROOM_MANAGE_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/roomSlice';
import serverNodeSliceReducer, {
  SERVER_NODE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/serverNodeSlice';
import spareManageSliceReducer, {
  SPARE_MANAGE_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/spareSlice';
import specSliceReducer, {
  SPEC_MANAGE_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/specSlice';
import staffShiftSliceReducer, {
  STAFF_SHIFT_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/staffShiftSlice';
import templateGroupViewSliceReducer, {
  TEMPLATE_GROUP_VIEW_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/templateGroupViewSlice';
import ticketConfigReducer, {
  TICKET_CONFIG_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/ticketConfigSlice';
import ticketReducer, {
  TICKET_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/ticketSlice';
import topologyGraphixSliceReducer, {
  TOPOLOGY_GRAPHIX_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/topologyGraphixSlice';
import legacyTopologySliceReducer, {
  TOPOLOGY_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/topologySlice';
import userGroupManageSliceReducer, {
  USER_GROUP_MANAGE_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/userGroupManageSlice';
import userManageSliceReducer, {
  USER_MANAGE_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/userManageSlice';
import vendorManageSliceReducer, {
  VENDOR_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/vendorSlice';
import versionManageReducer, {
  VERSION_MANAGE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/versionManageSlice';
import visitorBlacklistSliceReducer, {
  VISITOR_BLACKLIST_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/visitorBlacklistSlice';
import visitorRecordSliceReducer, {
  VISITOR_RECORD_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/visitorManagerSlice';
import warrantyOrderReducer, {
  WARRANTY_ORDER_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/warrantyOrderSlice';
import warrantyPoolReducer, {
  WARRANTY_POOL_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/warrantyPoolSlice';
import workdayManageSliceReducer, {
  WORKDAY_SLICE_NAME, // @ts-ignore: Could not find a declaration file
} from '@manyun/dc-brain.legacy.redux/reducers/workdayManageSlice';

export function createReducer(): ReducersMapObject {
  return {
    [USER_MANAGE_SLICE_NAME]: userManageSliceReducer,
    [ROLE_MANAGE_SLICE_NAME]: roleManageSliceReducer,
    [POWER_MANAGE_SLICE_NAME]: powerManageSliceReducer,
    [USER_GROUP_MANAGE_SLICE_NAME]: userGroupManageSliceReducer,
    [ROOM_MANAGE_SLICE_NAME]: roomManageSliceReducer,
    [CABINET_MANAGE_SLICE_NAME]: cabinetManageSliceReducer,
    [EQUIPMENT_MANAGE_SLICE_NAME]: equipmentManageSliceReducer,
    [SPARE_MANAGE_SLICE_NAME]: spareManageSliceReducer,
    [APPROVAL_CONFIG_SLICE_NAME]: approvalConfigSliceReducer,
    [BASIC_RESOURCES_IDC_SLICE_NAME]: basicResourcesIdcSliceReducer,
    [BASIC_RESOURCES_BUILDING_SLICE_NAME]: basicResourcesBuildingSliceReducer,
    [DOPOINT_MANAGE_SLICE_NAME]: doPointManageSliceReducer,
    [ALARM_MANAGE_SLICE_NAME]: alarmManageSliceReducer,
    [IDC_WORKBENCH_SLICE_NAME]: idcWorkbenchSliceReducer,
    [ROOM_MONITORING_SLICE_NAME]: roomMonitoringSliceReducer,
    [ALARM_SCREEN_SLICE_NAME]: alarmScreenSliceReducer,
    [TEMPLATE_GROUP_VIEW_SLICE_NAME]: templateGroupViewSliceReducer,
    [DEVICE_MONITORING_SLICE_NAME]: deviceMonitoringSliceReducer,
    [ALARM_CONFIGURATION_TEMPLATE_SLICE_NAME]: alarmConfigurationSliceReducer,
    [LAYOUT_SLICE_NAME]: layoutSliceReducer,
    [TOPOLOGY_SLICE_NAME]: legacyTopologySliceReducer,
    ...topologySliceReducer,
    [MERGED_MONITOR_CONFIG_SLICE_NAME]: mergedMonitorConfigSliceReducer,
    [EVENT_CENTER_SLICE_NAME]: eventCenterListSliceReducer,
    [CHANGE_SLICE_NAME]: changeSliceReducer,
    [MERGED_PROCESSES_POINT_SLICE_NAME]: mergedProcessesdPointSliceReducer,
    [STAFF_SHIFT_SLICE_NAME]: staffShiftSliceReducer,
    [NOTICE_MANAGE_SLICE_NAME]: noticeManageSliceReducer,
    [VENDOR_SLICE_NAME]: vendorManageSliceReducer,
    [SPEC_MANAGE_SLICE_NAME]: specSliceReducer,
    [DEVICE_TYPE_SLICE_NAME]: deviceTypeSliceReducer,
    [MODEL_SLICE_NAME]: modelSliceReducer,
    [MONITORING_CONFIG_SLICE_NAME]: monitoringConfigReducer,
    [INSPECTION_CONFIG_SLICE_NAME]: inspectionConfigSliceReducer,
    [TICKET_SLICE_NAME]: ticketReducer,
    [INVENTORY_CONFIG_SLICE_NAME]: inventoryConfigReducer,
    [OPERATION_CHECK_CONFIG_SLICE_NAME]: operationCheckConfigReducer,
    [TICKET_CONFIG_SLICE_NAME]: ticketConfigReducer,
    [INVENTORY_ANALYSTICS_SLICE_NAME]: inventoryAanlyticsReducer,
    [VERSION_MANAGE_NAME]: versionManageReducer,
    [SERVER_NODE_NAME]: serverNodeSliceReducer,
    [NORTH_USER_NAME]: northUserSliceReducer,
    [INFRA_ROOM_GRAPHIX_SLICE_NAME]: infraRoomGraphixSliceReducer,
    [ALTER_INFO_SLICE_NAME]: alterInfoSliceReducer,
    [MINE_TICKET_SLICE_NAME]: mineTicketSliceReducer,
    [WORKDAY_SLICE_NAME]: workdayManageSliceReducer,
    [VISITOR_RECORD_SLICE_NAME]: visitorRecordSliceReducer,
    [VISITOR_BLACKLIST_SLICE_NAME]: visitorBlacklistSliceReducer,
    [APPROVE_CENTER_SLICE_NAME]: approveCenterSliceReducer,
    [BATTERY_SLICE_NAME]: batterySliceReducer,
    [CHANNEL_CONFIG_NAME]: channelConfigSliceReducer,
    [TOPOLOGY_GRAPHIX_SLICE_NAME]: topologyGraphixSliceReducer,
    [INSIDE_NOTICE_SLICE_NAME]: insideNoticeSliceReducer,
    [WARRANTY_ORDER_SLICE_NAME]: warrantyOrderReducer,
    [WARRANTY_POOL_SLICE_NAME]: warrantyPoolReducer,
    [BORROW_AND_RETURN_SLICE_NAME]: borrowsAndReturnSliceReducer,
    [CUSTOM_ALARM_CONFIGURATION_SLICE_NAME]: customAlarmConfigurationSliceReducer,
    [ARRIVAL_ASSET_SLICE_NAME]: arrivalAssetSliceReducer,
    [ALARM_TRANSMISSION_RECORD_SLICE_NAME]: alarmTransmissionRecordSliceReducer,
    ...deviceSliceReducer,

    //#region resource-hub
    ...spaceSliceReducer,
    ...roomTypeSliceReducer,
    //#endregion

    ...skillsSliceReducer,
    ...questionsSliceReducer,
    ...papersSliceReducer,
    ...examsSliceReducer,
    ...coursesSliceReducer,
    ...coursewaresSliceReducer,
    ...customersSliceReducer,
    ...rolesSliceReducers,
    ...usersSliceReducers,
    ...userGroupsSliceReducer,
    ...authRequestSliceReducer,
    ...userSliceReducer,
    ...onSiteMessagesSliceReducer,
    ...dashboardIdcReducer,
    ...subscriptionsSliceReducer,
    ...metaDataSliceReducers,
    ...deviceTypesSliceReducer,
    ...alarmConfigurationTemplateSliceReducer,
    ...pointsSliceReducers,
    ...alarmsMonitoringBoardSliceReducer,
    ...ticketSliceReducer,
    ...alarmNotificationConfigureSliceReducer,
    ...ruleTypesSliceReducer,
    ...approvalCenterSliceReducer,
    ...eventSliceReducer,
    ...staffScheduleReducer,
  };
}
