import React from 'react';
// @ts-ignore: Could not find a declaration file
import DocumentTitle from 'react-document-title';
import type { RouteProps } from 'react-router-dom';
import { useHistory, useParams } from 'react-router-dom';

import { BPM_INSTANCE_ROUTE_PATH } from '@manyun/bpm.route.bpm-routes';
import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import { SHIFT_SYS_MANAGEMENT_LIST } from '@manyun/hrm.route.hrm-routes';
import {
  BASIC_RESOURCES_BUILDING,
  CABINET_DETAILS,
  SPECIFIC_INFRA_GRAPHIX,
  generateSpecificGraphixRoutePath,
} from '@manyun/resource-hub.route.resource-routes';
import type { GenerateSpecificGraphixRouteParams } from '@manyun/resource-hub.route.resource-routes';
import { VISITS_RECORDS_ROUTE_PATH } from '@manyun/sentry.route.routes';
import {
  CHAN<PERSON>_OFFLIENS_ROUTE_AUTH_CODE,
  CHANGE_OFFLIENS_ROUTE_PATH,
  CHANGE_OFFLIEN_CREATE_ROUTE_AUTH_CODE,
  CHANGE_OFFLIEN_CREATE_ROUTE_PATH,
  CHANGE_OFFLIEN_EDIT_ROUTE_AUTH_CODE,
  CHANGE_OFFLIEN_EDIT_ROUTE_PATH,
  CHANGE_OFFLIEN_ROUTE_PATH,
  CHANGE_ONLINE_CREATE_ROUTE_AUTH_CODE,
  CHANGE_ONLINE_CREATE_ROUTE_PATH,
  CHANGE_ONLINE_DETAIL_ROUTE_AUTH_CODE,
  CHANGE_ONLINE_DETAIL_ROUTE_PATH,
  CHANGE_ONLINE_EDIT_ROUTE_AUTH_CODE,
  CHANGE_ONLINE_EDIT_ROUTE_PATH,
  CHANGE_ONLINE_LIST_ROUTE_AUTH_CODE,
  CHANGE_ONLINE_LIST_ROUTE_PATH,
  CHANGE_TEMPLATE_CREATE_ROUTE_AUTH_CODE,
  CHANGE_TEMPLATE_CREATE_ROUTE_PATH,
  CHANGE_TEMPLATE_DETAIL_ROUTE_AUTH_CODE,
  CHANGE_TEMPLATE_DETAIL_ROUTE_PATH,
  CHANGE_TEMPLATE_EDIT_ROUTE_AUTH_CODE,
  CHANGE_TEMPLATE_EDIT_ROUTE_PATH,
  CHANGE_TEMPLATE_LIST_ROUTE_AUTH_CODE,
  CHANGE_TEMPLATE_LIST_ROUTE_PATH,
  DRILL_CONFIG_COPY_ROUTE_PATH,
  DRILL_CONFIG_CREATE_ROUTE_PATH,
  DRILL_CONFIG_DETAIL_ROUTE_PATH,
  DRILL_CONFIG_EDIT_ROUTE_PATH,
  DRILL_CONFIG_LIST_ROUTE_PATH,
  EMERGENCY_PROCEE_CREATE_ROUTE_AUTH_CODE,
  EMERGENCY_PROCEE_CREATE_ROUTE_PATH,
  EMERGENCY_PROCEE_EDIT_ROUTE_AUTH_CODE,
  EMERGENCY_PROCEE_EDIT_ROUTE_PATH,
  EMERGENCY_PROCEE_LIST_ROUTE_AUTH_CODE,
  EMERGENCY_PROCEE_LIST_ROUTE_PATH,
  EMERGENCY_PROCEE_ROUTE_AUTH_CODE,
  EMERGENCY_PROCEE_ROUTE_PATH,
  EVENT_CREATE_ROUTE_AUTH_CODE,
  EVENT_CREATE_ROUTE_PATH,
  EVENT_LIST_ROUTE_AUTH_CODE,
  EVENT_LIST_ROUTE_PATH,
  EVENT_ROUTE_AUTH_CODE,
  EVENT_ROUTE_PATH,
  INSPECTION_CONFIG_COPY_ROUTE_PATH,
  INVENTORY_CONFIG_COPY_ROUTE_PATH,
  MAINTAIN_CONFIG_COPY_ROUTE_PATH,
  STANDARD_CHANGE_LIBRARIES_ROUTE_PATH,
  STANDARD_CHANGE_LIBRARY_CREATE_ROUTE_AUTH_CODE,
  STANDARD_CHANGE_LIBRARY_CREATE_ROUTE_PATH,
  STANDARD_CHANGE_LIBRARY_EDIT_ROUTE_AUTH_CODE,
  STANDARD_CHANGE_LIBRARY_EDIT_ROUTE_PATH,
  STANDARD_CHANGE_LIBRARY_ROUTE_PATH,
  TICKET_ROUTE_PATH_AUTH_CODE_MAPPER,
} from '@manyun/ticket.route.ticket-routes';

// @ts-ignore: Could not find a declaration file
import { TEMPLATE_VIEW_MODE_TYPE } from '@manyun/dc-brain.legacy.constants/alarm';
// @ts-ignore: Could not find a declaration file
import { TICKET_TYPE_KEY_TEXT_MAP } from '@manyun/dc-brain.legacy.constants/ticket';
// @ts-ignore: Could not find a declaration file
import * as urls from '@manyun/dc-brain.legacy.constants/urls';
import { VisitorRecordNewText } from '@manyun/dc-brain.legacy.pages/visitor-manager/new';
// @ts-ignore: Could not find a declaration file
import { getLocationSearchMap } from '@manyun/dc-brain.legacy.utils';

const LayoutContent = React.lazy(() =>
  import('@manyun/dc-brain.ui.layout').then(({ LayoutContent }) => ({ default: LayoutContent }))
);

const DOCUMENT_TITLE_PREFIX = env.DOCUMENT_TITLE_PREFIX;

/* eslint-disable @typescript-eslint/no-explicit-any */
// @ts-ignore: Could not find a declaration file
const RoleList = React.lazy<any>(() => import('@manyun/dc-brain.legacy.pages/role-manage/list'));
const RoleDetail = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/role-manage/detail')
);
const PowerManageList = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/power-manage/list')
);
const UserGroupManageList = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/user-group-manage/list')
);
const UserGroupManageDetail = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/user-group-manage/detail')
);
const TopologyGraphix = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/topology-graphix')
);

const RoomManageList = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/room-manage/list')
);
const CabinetManageList = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/cabinet-manage/list')
);
const CabinetManageDetail = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/cabinet-manage/details')
);
const EquipmentManage = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/equipment-manage/list')
);
const BasicResourcesIdc = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/basic-resources-idc/list')
);
const BasicResourcesBuilding = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/basic-resources-building/list')
);
// // @ts-ignore: Could not find a declaration file
// const IdcWorkbench = React.lazy<any>(() => import('@manyun/dc-brain.legacy.pages/idc-workbench'));
const CreateEquipment = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/equipment-manage/create-equipment')
);

const SpecificAlarmConfig = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/monitor-item-config/specific-config')
);

// const RoomView = React.lazy<any>(
//   // @ts-ignore: Could not find a declaration file
//   () => import('@manyun/monitoring.page.room-view')
// );
const TemplateGroupView = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/template-group-view/list')
);
const CreateTemplateView = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/template-group-view/create')
);

const ViewTemplateGroup = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/template-group-view/view')
);
const AlarmConfigurationTemplate = React.lazy<any>(
  () =>
    // @ts-ignore: Could not find a declaration file
    import('@manyun/dc-brain.legacy.pages/alarm-configuration-template/detail')
);
const AlarmConfigurationTemplateList = React.lazy<any>(
  () =>
    // @ts-ignore: Could not find a declaration file
    import('@manyun/dc-brain.legacy.pages/alarm-configuration-template/list')
);
const EditEquipment = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/equipment-manage/edit')
);
// @ts-ignore: Could not find a declaration file
const ErrorPage403 = React.lazy<any>(() => import('@manyun/dc-brain.legacy.pages/403'));
const ConvergenceList = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/merged-monitor-config/list')
);
const NewConvergenceList = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/merged-monitor-config/new')
);
const EventCenter = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/event-center/list')
);

const SpecificMergedMonitorConfig = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/merged-monitor-config/detail')
);
// const MetadataConfiguration = React.lazy<any>(
//   // @ts-ignore: Could not find a declaration file
//   () => import('@manyun/dc-brain.legacy.pages/metadata-configuration/index')
// );

const EditCabinet = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/cabinet-manage/new')
);
const ChangeTemplateNew = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/change/template/new')
);
const ChangeTemplateDetail = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/change/template/detail')
);
const ChangeTemplateEdit = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/change/template/edit')
);
const ChangeTemplateList = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/change/template/list')
);
const ChangeTicketList = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/change/ticket/list')
);
const ChangeTicketNew = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/change/ticket/new')
);
const ChangeTicketEdit = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/change/ticket/edit')
);

const ChangeTicketView = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/change/ticket/view')
);
const MergedProcessedPointDetail = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/merged-processed-point/detail')
);
// const MergedProcessedPointEdit = React.lazy<any>(
//   // @ts-ignore: Could not find a declaration file
//   () => import('@manyun/dc-brain.legacy.pages/merged-processed-point/edit')
// );

const ShiftSysManagement = React.lazy<any>(
  () =>
    // @ts-ignore: Could not find a declaration file
    import('@manyun/dc-brain.legacy.pages/staff-shift-arrangement/shift-sys-management')
);
const DutyGroupManagement = React.lazy<any>(
  () =>
    // @ts-ignore: Could not find a declaration file
    import('@manyun/dc-brain.legacy.pages/staff-shift-arrangement/duty-group-management')
);
const GroupScheduleList = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/staff-shift-arrangement/group-schedule')
);
const AttGroupList = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/staff-shift-arrangement/attendance-group')
);
const AttGroupCreate = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/staff-shift-arrangement/attendance-group/create')
);
const AttGroupSchedule = React.lazy<any>(
  () =>
    // @ts-ignore: Could not find a declaration file
    import('@manyun/dc-brain.legacy.pages/staff-shift-arrangement/attendance-group/schedule')
);
const NoticeManageList = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/notice-manage/list')
);
const NoticeManageDetail = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/notice-manage/detail')
);
// @ts-ignore: Could not find a declaration file
const LogList = React.lazy<any>(() => import('@manyun/dc-brain.legacy.pages/log'));
const VendorList = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/vendor-manage/list')
);
const VendorDetail = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/vendor-manage/detail')
);
const ModelManage = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/model-manage/list')
);
// @ts-ignore: Could not find a declaration file
const ModelNew = React.lazy<any>(() => import('@manyun/dc-brain.legacy.pages/model-manage/new'));

const InspectionConfigList = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/inspection-config/list')
);
const InspectionConfigDetail = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/inspection-config/detail')
);
const InspectionConfigCreate = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/inspection-config/create')
);
const InventoryAnalyticsPage = React.lazy<any>(
  () =>
    // @ts-ignore: Could not find a declaration file
    import('@manyun/dc-brain.legacy.pages/ticket/configs/inventory/inventory-analytics')
);

const InventoryConfigList = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/inventory-config/list')
);
const InventoryConfigCreate = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/inventory-config/create')
);
const InventoryConfigDetail = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/inventory-config/detail')
);
const OperationCheckConfigList = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/operation-check-config/list')
);
const OperationCheckConfigDetail = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/operation-check-config/detail')
);
const OperationCheckConfigCreate = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/operation-check-config/create')
);
const Tickets = React.lazy<any>(() =>
  // @ts-ignore: Could not find a declaration file
  import('@manyun/dc-brain.legacy.pages/ticket').then(({ default: { Tickets } }) => ({
    default: Tickets,
  }))
);
const SpecificTicket = React.lazy<any>(() =>
  // @ts-ignore: Could not find a declaration file
  import('@manyun/dc-brain.legacy.pages/ticket').then(({ default: { SpecificTicket } }) => ({
    default: SpecificTicket,
  }))
);
const NewTicket = React.lazy<any>(() =>
  // @ts-ignore: Could not find a declaration file
  import('@manyun/dc-brain.legacy.pages/ticket').then(({ default: { NewTicket } }) => ({
    default: NewTicket,
  }))
);
const MaintainConfigList = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/maintain-config/list')
);
const MaintainConfigCreate = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/maintain-config/create')
);
const MaintainConfigDetail = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/maintain-config/detail')
);
const ServerNodeList = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/server-node/list')
);
const ServerNodeDetail = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/server-node/detail')
);
// @ts-ignore: Could not find a declaration file
const SpareList = React.lazy<any>(() => import('@manyun/dc-brain.legacy.pages/spare-manager/list'));
const CreateSpare = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/spare-manager/create-spare')
);
const InfraRoomGraphix = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/infra-room-graphix')
);
// @ts-ignore: Could not find a declaration file
const AlterInfo = React.lazy<any>(() => import('@manyun/dc-brain.legacy.pages/alter-info/list'));
const AlterInfoNew = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/alter-info/create')
);
const AlterInfoDetail = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/alter-info/detail')
);
// const SupplyCheck = React.lazy<any>(
//   // @ts-ignore: Could not find a declaration file
//   () => import('@manyun/dc-brain.legacy.pages/supply-check/list')
// );
// const SupplyCheckDetail = React.lazy<any>(
//   // @ts-ignore: Could not find a declaration file
//   () => import('@manyun/dc-brain.legacy.pages/supply-check/detail')
// );
const AreaConnectTemplate = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/template-group-view/area-config')
);
const VisitorRecordList = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/visitor-manager/list')
);
const VisitorRecordNew = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/visitor-manager/new')
);
const VisitorBlacklist = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/visitor-blacklist/list')
);
const ApprovalProcessConfigList = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/approval/list/approval-process')
);
const ApprovalProcessConfigDetail = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/approval/detail/process-detail')
);
const ApprovalScenesList = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/approval/list/approval-scenes')
);
const ApproveCenterList = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/approve-center/list')
);
// @ts-ignore: Could not find a declaration file
const BateryPack = React.lazy<any>(() => import('@manyun/dc-brain.legacy.pages/battery-pack'));
const ApproveCenterDetail = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/approve-center/detail')
);
const VersionManageDetail = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/version-manage/detail')
);
// @ts-ignore: Could not find a declaration file
const MineTicket = React.lazy<any>(() => import('@manyun/dc-brain.legacy.pages/mine-tickets'));
// const WorkdayManage = React.lazy<any>(
//   // @ts-ignore: Could not find a declaration file
//   () => import('@manyun/dc-brain.legacy.pages/workday-manage/list')
// );

const ChannelDeviceAssociate = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/channel-config/associate')
);
const ChannelPointImport = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/channel-config/import')
);
const WarrantyList = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/warranty-manage/list')
);
const CreateWarrantyOrder = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/warranty-manage/new')
);
const WarrantyDetail = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/warranty-manage/detail')
);
const WarrantyPool = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/warranty-manage/pool')
);
const ArrivalAssetList = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/arrival-asset/list')
);
const ArrivalAssetNew = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/arrival-asset/new')
);
const AlarmTransmissionRecord = React.lazy<any>(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun/dc-brain.legacy.pages/alarm-transmission-record/list')
);
const DrillConfigList = React.lazy<any>(() => import('@manyun/ticket.page.drill-config-list'));
const DrillConfigDetail = React.lazy<any>(() => import('@manyun/ticket.page.drill-config'));
const DrillConfigMutator = React.lazy<any>(
  () => import('@manyun/ticket.page.drill-config-mutator')
);
const StandardChangeLibraries = React.lazy<any>(
  () => import('@manyun/ticket.page.standard-change-libraries')
);

const StandardChangeLibraryMutator = React.lazy<any>(
  () => import('@manyun/ticket.page.standard-change-library-mutator')
);
const StandardChangeLibraryDetail = React.lazy<any>(
  () => import('@manyun/ticket.page.standard-change-library-detail')
);
const ChangeOfflineMutator = React.lazy<any>(
  () => import('@manyun/ticket.page.change-offline-mutator')
);
const ChangeOfflines = React.lazy<any>(() => import('@manyun/ticket.page.change-offlines'));
const EventMutatorBeta = React.lazy<any>(() => import('@manyun/ticket.page.event-mutator-beta'));
const EventsBeta = React.lazy<any>(() => import('@manyun/ticket.page.events-beta'));
const EventBeta = React.lazy<any>(() => import('@manyun/ticket.page.event-beta'));
const EmergencyProcess = React.lazy<any>(() => import('@manyun/ticket.page.emergency-process'));
const EmergencyProcessList = React.lazy<any>(
  () => import('@manyun/ticket.page.emergency-process-list')
);
const EmergencyProcessMutator = React.lazy<any>(
  () => import('@manyun/ticket.page.emergency-process-mutator')
);
const ChangeOnlineMutator = React.lazy<any>(
  () => import('@manyun/ticket.page.change-online-mutator')
);
const ChangeTemplateMutator = React.lazy<any>(
  () => import('@manyun/ticket.page.change-template-mutator')
);
const ChangeOnlineTemplateDetail = React.lazy<any>(
  () => import('@manyun/ticket.page.change-template-detail')
);
const ChangeOnlineTemplateList = React.lazy<any>(
  () => import('@manyun/ticket.page.change-templates')
);
const ChangeOnlineList = React.lazy<any>(() => import('@manyun/ticket.page.change-onlines'));
/* eslint-enable @typescript-eslint/no-explicit-any */

type LegacyRoute = RouteProps & {
  key: string;
};

function CompatibleInfraRoomGraphix() {
  const { topologyType, idc, block, room, mode } = useParams<GenerateSpecificGraphixRouteParams>();
  const history = useHistory();
  if (topologyType === 'infra-room') {
    // 兼容旧url跳转
    history.push(
      generateSpecificGraphixRoutePath({
        idc,
        block,
        room,
        topologyType: 'ROOM_FACILITY',
        mode,
      })
    );
    return null;
  } else {
    return <InfraRoomGraphix />;
  }
}

export const legacyRoutes: LegacyRoute[] = [
  {
    key: urls.ALARM_TRANSMISSION_RECORD_LIST,
    path: urls.ALARM_TRANSMISSION_RECORD_LIST,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '告警传输记录'}>
        <LayoutContent pageCode="page_alarm-transmission-record">
          <AlarmTransmissionRecord />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.CHANNEL_CONFIG_IMPORT,
    path: urls.CHANNEL_CONFIG_IMPORT,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '导入通道测点'}>
        <LayoutContent pageCode="page_channel-config-import">
          <ChannelPointImport />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.CHANNEL_CONFIG_ASSOCIATE,
    path: urls.CHANNEL_CONFIG_ASSOCIATE,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '通道关联'}>
        <LayoutContent pageCode="page_channel-config">
          <ChannelDeviceAssociate />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: BPM_INSTANCE_ROUTE_PATH,
    path: BPM_INSTANCE_ROUTE_PATH,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '审批详情'}>
          <LayoutContent
            pageCode="page_approve-center-approval"
            composeBreadcrumbs={value => [
              ...value,
              { key: 'page_approve-center-approval-info', text: '审批详情' },
            ]}
          >
            <ApproveCenterDetail {...props} mode="detail" />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: urls.BATTERY_PACK,
    path: urls.BATTERY_PACK,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '电池组'}>
          <LayoutContent pageCode="page_battery-unit-view" subscribeNotifications>
            <BateryPack {...props} />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: urls.APPROVE_CENTER_LIST,
    path: urls.APPROVE_CENTER_LIST,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '审批列表'}>
        <LayoutContent pageCode="page_approve-center-my-approvals">
          <ApproveCenterList />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.AREA_CONNECT_TEMPLATE,
    path: urls.AREA_CONNECT_TEMPLATE,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '生效域配置'}>
        <LayoutContent pageCode="page_area-connect-template">
          <AreaConnectTemplate />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.VISITOR_BLACKLIST,
    path: urls.VISITOR_BLACKLIST,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '黑名单管理'}>
        <LayoutContent pageCode="page_security-center_chores--visitor_blacklist">
          <VisitorBlacklist />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.VISITOR_RECORD_NEW,
    path: urls.VISITOR_RECORD_NEW,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '人员出入室登记 - 进入登记'}>
          <LayoutContent
            pageCode="page_security-center_chores--visitor_record-create"
            composeBreadcrumbs={value => [
              ...value,
              {
                key: 'page_security-center_chores--visitor_record-create-key',
                text: <VisitorRecordNewText />,
              },
            ]}
          >
            <VisitorRecordNew {...props} mode="new" />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: VISITS_RECORDS_ROUTE_PATH,
    path: VISITS_RECORDS_ROUTE_PATH,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '人员出入室登记'}>
        <LayoutContent pageCode="page_security-center_chores--visitor_record">
          <VisitorRecordList />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: SPECIFIC_INFRA_GRAPHIX,
    path: SPECIFIC_INFRA_GRAPHIX,
    exact: true,
    children: <CompatibleInfraRoomGraphix />,
  },
  {
    key: urls.SPARE_LIST,
    path: urls.SPARE_LIST,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '库房管理'}>
          <LayoutContent pageCode="page_spare-management">
            <SpareList {...props} />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: urls.SPECIFIC_CREATE_SPARE_ITEM,
    path: urls.SPECIFIC_CREATE_SPARE_ITEM,
    exact: true,
    children: props => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '导入耗材'}>
        <LayoutContent
          pageCode="page_spare-management-import"
          composeBreadcrumbs={value => [
            ...value,
            { key: 'page_spare-management-import-key', text: '新建' },
          ]}
        >
          <CreateSpare {...props} />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  // {
  //   key: urls.APPROVAL_PROCESS_CONFIG_LIST,
  //   path: urls.APPROVAL_PROCESS_CONFIG_LIST,
  //   exact: true,
  //   children: props => (
  //     <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '流程配置'}>
  //       <LayoutContent pageCode="page_process-config">
  //         <ApprovalProcessConfigList {...props} />
  //       </LayoutContent>
  //     </DocumentTitle>
  //   ),
  // },
  {
    key: urls.APPROVAL_PROCESS_CONFIG_DETAIL,
    path: urls.APPROVAL_PROCESS_CONFIG_DETAIL,
    exact: true,
    children: props => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '流程详情'}>
        <LayoutContent
          pageCode="page_process-config-info"
          composeBreadcrumbs={value => [
            ...value,
            { key: 'page_process-config-info', text: '流程详情' },
          ]}
        >
          <ApprovalProcessConfigDetail {...props} />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.APPROVAL_SCENES_LIST,
    path: urls.APPROVAL_SCENES_LIST,
    exact: true,
    children: props => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '业务场景'}>
        <LayoutContent pageCode="page_biz-scenes">
          <ApprovalScenesList {...props} />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.SERVER_NODE_DETAIL,
    path: urls.SERVER_NODE_DETAIL,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '节点详情'}>
        <LayoutContent
          pageCode="page_server-node-detail"
          composeBreadcrumbs={value => [
            ...value,
            { key: 'page_server-node-detail-key', text: '节点详情' },
          ]}
        >
          <ServerNodeDetail />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.SERVER_NODE_LIST,
    path: urls.SERVER_NODE_LIST,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '节点列表'}>
        <LayoutContent pageCode="page_server-node-list">
          <ServerNodeList />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.MAINTAIN_CONFIG_EDIT,
    path: urls.MAINTAIN_CONFIG_EDIT,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '配置中心 - 工单配置 - 编辑维护配置'}>
          <LayoutContent
            pageCode="page_maintain-configuration"
            composeBreadcrumbs={value => [
              ...value,
              { key: 'page_maintain-configuration-info', text: '编辑维护配置' },
            ]}
          >
            <MaintainConfigCreate {...props} mode="edit" />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  // {
  //   key: urls.SUPPLY_CHECK,
  //   path: urls.SUPPLY_CHECK,
  //   exact: true,
  //   children: props => {
  //     return (
  //       <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '-补卡记录列表'}>
  //         <LayoutContent pageCode="page_supply-check">
  //           <SupplyCheck {...props} />
  //         </LayoutContent>
  //       </DocumentTitle>
  //     );
  //   },
  // },
  // {
  //   key: urls.SUPPLY_CHECK_DETAIL,
  //   path: urls.SUPPLY_CHECK_DETAIL,
  //   exact: true,
  //   children: props => {
  //     return (
  //       <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '-补卡记录详情'}>
  //         <LayoutContent
  //           pageCode="page_supply-check-info"
  //           composeBreadcrumbs={value => [
  //             ...value,
  //             { key: 'page_supply-check-info-key', text: '补卡记录详情' },
  //           ]}
  //         >
  //           <SupplyCheckDetail {...props} mode="detail" />
  //         </LayoutContent>
  //       </DocumentTitle>
  //     );
  //   },
  // },
  {
    key: urls.ALTER_INFO,
    path: urls.ALTER_INFO,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '-调班信息列表'}>
          <LayoutContent pageCode="page_schedule-alter">
            <AlterInfo {...props} />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: urls.ALTER_INFO_NEW,
    path: urls.ALTER_INFO_NEW,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '-新建排班调整'}>
          <LayoutContent
            pageCode="page_bpm-requests-entry_schedule-creator"
            composeBreadcrumbs={value => [
              ...value,
              { key: 'page_bpm-requests-entry_schedule-creator', text: '新建排班调整申请' },
            ]}
          >
            <AlterInfoNew {...props} />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: urls.ALTER_INFO_DETAIL,
    path: urls.ALTER_INFO_DETAIL,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '-调班信息详情'}>
          <LayoutContent
            pageCode="page_schedule-alter-info"
            composeBreadcrumbs={value => [
              ...value,
              { key: 'page_schedule-alter-info-key', text: '调班信息详情' },
            ]}
          >
            <AlterInfoDetail {...props} mode="detail" />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: urls.MAINTAIN_CONFIG_DETAIL,
    path: urls.MAINTAIN_CONFIG_DETAIL,
    exact: true,
    children: props => {
      const { name } = getLocationSearchMap(props.location.search, ['name']);
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '配置中心 - 工单配置 - 维护配置 - 详情'}>
          <LayoutContent
            pageCode="page_maintain-configuration-info"
            composeBreadcrumbs={value => [
              ...value,
              { key: 'page_maintain-configuration-info-name', text: name },
              { key: 'page_maintain-configuration-info-key', text: '详情' },
            ]}
          >
            <MaintainConfigDetail {...props} mode="detail" />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: urls.MAINTAIN_CONFIG_CREATE,
    path: urls.MAINTAIN_CONFIG_CREATE,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '配置中心 - 工单配置 - 新建维护配置'}>
          <LayoutContent
            pageCode="page_maintain-configuration-creator"
            composeBreadcrumbs={value => [
              ...value,
              { key: 'page_maintain-configuration-creator', text: '新建维护配置' },
            ]}
          >
            <MaintainConfigCreate {...props} mode="new" />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: urls.MAINTAIN_CONFIG_LIST,
    path: urls.MAINTAIN_CONFIG_LIST,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '配置中心 - 工单配置 - 维护配置'}>
        <LayoutContent pageCode="page_maintain-configuration">
          <MaintainConfigList />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.OPERATION_CHECK_CONFIG_EDIT,
    path: urls.OPERATION_CHECK_CONFIG_EDIT,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '配置中心 - 工单配置 - 编辑操作检查配置'}>
          <LayoutContent
            pageCode="page_operation-check-configuration-editor"
            composeBreadcrumbs={value => [
              ...value,
              { key: 'page_operation-check-configuration-editor-key', text: '编辑操作检查' },
            ]}
          >
            <OperationCheckConfigCreate {...props} mode="edit" />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: urls.OPERATION_CHECK_CONFIG_CREATE,
    path: urls.OPERATION_CHECK_CONFIG_CREATE,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '配置中心 - 工单配置 - 新建操作检查配置'}>
          <LayoutContent
            pageCode="page_operation-check-configuration-creator"
            composeBreadcrumbs={value => [
              ...value,
              { key: 'page_operation-check-configuration-creator-key', text: '新建操作检查配置' },
            ]}
          >
            <OperationCheckConfigCreate {...props} mode="new" />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: urls.OPERATION_CHECK_CONFIG_DETAIL,
    path: urls.OPERATION_CHECK_CONFIG_DETAIL,
    exact: true,
    children: props => {
      const { name } = getLocationSearchMap(props.location.search, ['name']);

      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '配置中心 - 工单配置 - 操作检查配置详情'}>
          <LayoutContent
            pageCode="page_operation-check-configuration-info"
            composeBreadcrumbs={value => [
              ...value,
              { key: 'page_operation-check-configuration-info-key', text: name },
            ]}
          >
            <OperationCheckConfigDetail {...props} mode="detail" />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: urls.OPERATION_CHECK_CONFIG_LIST,
    path: urls.OPERATION_CHECK_CONFIG_LIST,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '配置中心 - 工单配置 - 操作检查配置'}>
          <LayoutContent pageCode="page_operation_check_configuration">
            <OperationCheckConfigList {...props} />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: urls.INVENTORY_CONFIG_EDIT,
    path: urls.INVENTORY_CONFIG_EDIT,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '配置中心 - 编辑盘点配置'}>
          <LayoutContent
            pageCode="page_inventory-configuration-edit"
            composeBreadcrumbs={value => [
              ...value,
              { key: 'page_inventory-configuration-edit-key', text: '编辑盘点配置' },
            ]}
          >
            <InventoryConfigCreate {...props} mode="edit" />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },

  {
    key: INVENTORY_CONFIG_COPY_ROUTE_PATH,
    path: INVENTORY_CONFIG_COPY_ROUTE_PATH,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '配置中心 - 复制盘点配置'}>
          <LayoutContent
            pageCode={TICKET_ROUTE_PATH_AUTH_CODE_MAPPER[INVENTORY_CONFIG_COPY_ROUTE_PATH]}
            composeBreadcrumbs={value => [
              ...value,
              { key: 'page_inventory-configuration-copy-key', text: '复制盘点配置' },
            ]}
          >
            <InventoryConfigCreate {...props} mode="copy" />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },

  {
    key: MAINTAIN_CONFIG_COPY_ROUTE_PATH,
    path: MAINTAIN_CONFIG_COPY_ROUTE_PATH,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '配置中心 - 复制维护配置'}>
          <LayoutContent
            pageCode={TICKET_ROUTE_PATH_AUTH_CODE_MAPPER[MAINTAIN_CONFIG_COPY_ROUTE_PATH]}
            composeBreadcrumbs={value => [
              ...value,
              { key: 'page_matain-configuration-copy-key', text: '复制维护配置' },
            ]}
          >
            <MaintainConfigCreate {...props} mode="copy" />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },

  {
    key: INSPECTION_CONFIG_COPY_ROUTE_PATH,
    path: INSPECTION_CONFIG_COPY_ROUTE_PATH,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '配置中心 - 复制巡检配置'}>
          <LayoutContent
            pageCode={TICKET_ROUTE_PATH_AUTH_CODE_MAPPER[INSPECTION_CONFIG_COPY_ROUTE_PATH]}
            composeBreadcrumbs={value => [
              ...value,
              { key: 'page_inspection-configuration-copy-key', text: '复制巡检配置' },
            ]}
          >
            <InspectionConfigCreate {...props} mode="copy" />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: urls.INVENTORY_CONFIG_CREATE,
    path: urls.INVENTORY_CONFIG_CREATE,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '配置中心 - 新建盘点配置'}>
          <LayoutContent
            pageCode="page_inventory-configuration-create"
            composeBreadcrumbs={value => [
              ...value,
              { key: 'page_inventory-configuration-create-key', text: '新建盘点配置' },
            ]}
          >
            <InventoryConfigCreate {...props} mode="new" />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: urls.INVENTORY_CONFIG_LIST,
    path: urls.INVENTORY_CONFIG_LIST,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '配置中心 - 盘点配置'}>
          <LayoutContent pageCode="page_inventory-configuration">
            <InventoryConfigList {...props} />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: urls.INVENTORY_CONFIG_DETAIL,
    path: urls.INVENTORY_CONFIG_DETAIL,
    exact: true,
    children: props => {
      const { name } = getLocationSearchMap(props.location.search, ['name']);
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '配置中心 - 盘点配置详情'}>
          <LayoutContent
            pageCode="page_inventory-configuration-info"
            composeBreadcrumbs={value => [
              ...value,
              { key: 'page_inventory-configuration-info-key', text: name },
            ]}
          >
            <InventoryConfigDetail {...props} mode="detail" />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: urls.INVENTORY_ANALYTICS_LIST,
    path: urls.INVENTORY_ANALYTICS_LIST,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '盘点分析'}>
        <LayoutContent pageCode="page_inventory-analysis">
          <InventoryAnalyticsPage />
        </LayoutContent>
      </DocumentTitle>
    ),
  },

  {
    key: urls.INSPECTION_CONFIG_EDIT,
    path: urls.INSPECTION_CONFIG_EDIT,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '配置中心-巡检配置-编辑巡检配置'}>
          <LayoutContent
            pageCode="page_inspection-configuration-edit"
            composeBreadcrumbs={value => [
              ...value,
              { key: 'page_inspection-configuration-edit-key', text: '编辑巡检配置' },
            ]}
          >
            <InspectionConfigCreate {...props} mode="edit" />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },

  {
    key: urls.INSPECTION_CONFIG_CREATE,
    path: urls.INSPECTION_CONFIG_CREATE,
    exact: true,
    children: props => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '新建巡检配置'}>
        <LayoutContent
          pageCode="page_inspection_configuration-create"
          composeBreadcrumbs={value => [
            ...value,
            { key: 'page_inspection_configuration-create-key', text: '新建巡检配置' },
          ]}
        >
          <InspectionConfigCreate {...props} mode="new" />
        </LayoutContent>
      </DocumentTitle>
    ),
  },

  {
    key: urls.TICKET_EDIT,
    path: urls.TICKET_EDIT,
    exact: true,
    children: props => {
      const ticketType = props.match?.params.type;
      const pageCode = getTicketPageCde({ ticketType, mode: 'edit' });
      return (
        <DocumentTitle
          title={
            DOCUMENT_TITLE_PREFIX +
            // @ts-ignore @YXW fix it
            `工单 - ${TICKET_TYPE_KEY_TEXT_MAP[props.match?.params.type]}工单 - 编辑`
          }
        >
          <LayoutContent
            pageCode={pageCode}
            composeBreadcrumbs={value => [
              ...value,
              {
                key: `page_${props.match?.params.type}-edit`,
                // @ts-ignore @YXW fix it
                text: `编辑${TICKET_TYPE_KEY_TEXT_MAP[props.match?.params.type]}工单`,
              },
            ]}
          >
            <NewTicket {...props} />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: urls.TICKET_CREATE,
    path: urls.TICKET_CREATE,
    exact: true,
    children: props => {
      const ticketType = props.match?.params.type;
      const pageCode = getTicketPageCde({ ticketType, mode: 'create' });

      return (
        <DocumentTitle
          title={
            DOCUMENT_TITLE_PREFIX +
            // @ts-ignore @YXW fix it
            `工单 - ${TICKET_TYPE_KEY_TEXT_MAP[props.match?.params.type]}工单 - 新建`
          }
        >
          <LayoutContent
            pageCode={pageCode}
            composeBreadcrumbs={value => [
              ...value,
              {
                key: `page_${props.match?.params.type}-edit`,
                // @ts-ignore @YXW fix it
                text: `新建${TICKET_TYPE_KEY_TEXT_MAP[props.match?.params.type]}工单`,
              },
            ]}
          >
            <NewTicket {...props} />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },

  {
    key: urls.INSPECTION_CONFIG_DETAIL,
    path: urls.INSPECTION_CONFIG_DETAIL,
    exact: true,
    children: props => {
      const { name } = getLocationSearchMap(props.location.search, ['name']);

      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '配置中心-巡检配置-巡检配置详情'}>
          <LayoutContent
            pageCode="page_inspection-configuration-info"
            composeBreadcrumbs={value => [
              ...value,
              {
                key: 'page_inspection-configuration-info-key',
                text: name,
              },
            ]}
          >
            <InspectionConfigDetail {...props} mode="detail" />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: urls.TICKET_VIEW,
    path: urls.TICKET_VIEW,
    exact: true,
    children: props => {
      const ticketType = props.match?.params.type as any; // eslint-disable-line
      const pageCode = getTicketPageCde({ ticketType, mode: 'info' });
      const ticketTypeName = (TICKET_TYPE_KEY_TEXT_MAP as any)[ticketType] ?? 'UNKNOWN'; // eslint-disable-line

      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + `工单 - ${ticketTypeName}工单 - 详情`}>
          <LayoutContent
            pageCode={pageCode}
            composeBreadcrumbs={value => [
              ...value,
              {
                key: `page_${props.match?.params.type}-info`,
                text: props.match?.params.id,
              },
            ]}
          >
            <SpecificTicket {...props} />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: urls.INSPECTION_CONFIG_LIST,
    path: urls.INSPECTION_CONFIG_LIST,
    exact: true,
    children: props => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '配置中心-工单配置-巡检列表'}>
        <LayoutContent pageCode="page_inspection_configuration">
          <InspectionConfigList {...props} />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.TICKET_LIST,
    path: urls.TICKET_LIST,
    exact: true,
    children: props => {
      const ticketType = props.match?.params.type;
      const pageCode = getTicketPageCde({ ticketType, mode: 'list' });
      return (
        <DocumentTitle
          title={
            DOCUMENT_TITLE_PREFIX +
            // @ts-ignore @YXW fix it
            `工单 - ${TICKET_TYPE_KEY_TEXT_MAP[props.match?.params.type]}工单 - 列表 `
          }
        >
          <LayoutContent pageCode={pageCode}>
            <Tickets {...props} />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  // {
  //   key: urls.ATT_STATISTICS_RECORD_LIST,
  //   path: urls.ATT_STATISTICS_RECORD_LIST,
  //   exact: true,
  //   children: props => (
  //     <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '人员管理 - 考勤统计 - 原始记录'}>
  //       <LayoutContent pageCode="page_staff-records">
  //         <AttStatisticsRecord {...props} />
  //       </LayoutContent>
  //     </DocumentTitle>
  //   ),
  // },
  {
    key: urls.ATT_GROUP_MANAGEMENT_SCHEDULE_EDIT,
    path: urls.ATT_GROUP_MANAGEMENT_SCHEDULE_EDIT,
    exact: true,
    children: props => {
      const { groupName } = getLocationSearchMap(props.location.search, ['groupName']);

      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '编辑排班'}>
          <LayoutContent
            pageCode="page_att-group-schedule-edit"
            composeBreadcrumbs={value => [
              ...value,
              {
                key: 'page_att-group-schedule-edit-name',
                text: groupName,
              },
              { key: 'page_att-group-schedule-edit-schedule', text: '排班' },
            ]}
          >
            <AttGroupSchedule {...props} mode="scheduleEdit" />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: urls.ATT_GROUP_MANAGEMENT_SCHEDULE_DETAIL,
    path: urls.ATT_GROUP_MANAGEMENT_SCHEDULE_DETAIL,
    exact: true,
    children: props => {
      const { groupName } = getLocationSearchMap(props.location.search, ['groupName']);

      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '排班详情'}>
          <LayoutContent
            pageCode="page_att-group-schedule-info"
            composeBreadcrumbs={value => [
              ...value,
              {
                key: 'page_att-group-schedule-info-name',
                text: groupName,
              },
              { key: 'page_att-group-schedule-info-schedule', text: '排班' },
            ]}
          >
            <AttGroupSchedule {...props} mode="scheduleDetail" />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: urls.ATT_GROUP_MANAGEMENT_SCHEDULE,
    path: urls.ATT_GROUP_MANAGEMENT_SCHEDULE,
    exact: true,
    children: props => {
      const { groupName } = getLocationSearchMap(props.location.search, ['groupName']);

      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '排班'}>
          <LayoutContent
            pageCode="page_att-group-schedule-schedule"
            composeBreadcrumbs={value => [
              ...value,
              {
                key: 'page_att-group-schedule-schedule-name',
                text: groupName,
              },
              { key: 'page_att-group-schedule-schedule-schedule', text: '排班' },
            ]}
          >
            <AttGroupSchedule {...props} mode="schedule" />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: urls.ATT_GROUP_MANAGEMENT_EDIT,
    path: urls.ATT_GROUP_MANAGEMENT_EDIT,
    exact: true,
    children: props => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '编辑考勤组'}>
        <LayoutContent
          pageCode="page_att-group-edit"
          composeBreadcrumbs={value => [
            ...value,
            { key: 'page_att-group-edit-key', text: '编辑考勤组' },
          ]}
        >
          <AttGroupCreate {...props} mode="edit" />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.ATT_GROUP_MANAGEMENT_CREATE,
    path: urls.ATT_GROUP_MANAGEMENT_CREATE,
    exact: true,
    children: props => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '新建考勤组'}>
        <LayoutContent
          pageCode="page_att-group-create"
          composeBreadcrumbs={value => [
            ...value,
            { key: 'page_att-group-create-key', text: '新建考勤组' },
          ]}
        >
          <AttGroupCreate {...props} mode="new" />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.ATT_GROUP_MANAGEMENT_LIST,
    path: urls.ATT_GROUP_MANAGEMENT_LIST,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '考勤组管理'}>
        <LayoutContent pageCode="page_att-groups">
          <AttGroupList />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.GROUP_SCHEDULE_MANAGEMENT_LIST,
    path: urls.GROUP_SCHEDULE_MANAGEMENT_LIST,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '排班规则管理'}>
        <LayoutContent pageCode="page_group-schedule-rules">
          <GroupScheduleList />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.DUTY_GROUP_MANAGEMENT_LIST,
    path: urls.DUTY_GROUP_MANAGEMENT_LIST,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '班组列表'}>
        <LayoutContent pageCode="page_duty-groups">
          <DutyGroupManagement />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: SHIFT_SYS_MANAGEMENT_LIST,
    path: SHIFT_SYS_MANAGEMENT_LIST,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '班制列表'}>
        <LayoutContent pageCode="page_shift-sys-list">
          <ShiftSysManagement />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.CHANGE_TICKET_EDIT,
    path: urls.CHANGE_TICKET_EDIT,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '变更编辑'}>
        <LayoutContent
          pageCode="page_change-ticket-edit"
          composeBreadcrumbs={value => [
            ...value,
            { key: 'page_change-ticket-edit', text: '编辑线上变更' },
          ]}
        >
          <ChangeTicketEdit />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.CHANGE_TEMPLATE_EDIT,
    path: urls.CHANGE_TEMPLATE_EDIT,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '变更模板编辑'}>
        <LayoutContent
          pageCode="page_change-template-edit"
          composeBreadcrumbs={value => [
            ...value,
            { key: 'page_change-template-edit-key', text: '编辑模板' },
          ]}
        >
          <ChangeTemplateEdit />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.CHANGE_TEMPLATE_DETAIL,
    path: urls.CHANGE_TEMPLATE_DETAIL,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '变更模板详情'}>
        <LayoutContent pageCode="page_change-template-info">
          <ChangeTemplateDetail />
        </LayoutContent>
      </DocumentTitle>
    ),
  },

  // {
  //   key: urls.MERGES_PROCESSED_POINT_EDIT,
  //   path: urls.MERGES_PROCESSED_POINT_EDIT,
  //   exact: true,
  //   children: (props) => (
  //     <DocumentTitle title={APP_NAME + '测点配置'}>
  //       <MergedProcessedPointEdit mode="edit" {...props} />
  //     </DocumentTitle>
  //   ),
  // },
  {
    key: urls.MERGES_PROCESSED_POINT_DETAIL,
    path: urls.MERGES_PROCESSED_POINT_DETAIL,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '基础管理'}>
        <LayoutContent pageCode="page_mergerd-processed-points">
          <MergedProcessedPointDetail mode="detail" />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  // {
  //   key: urls.MERGES_PROCESSED_POINT_CONFIG,
  //   path: urls.MERGES_PROCESSED_POINT_CONFIG,
  //   exact: true,
  //   children: (
  //     <DocumentTitle title={APP_NAME + '聚合加工测点配置'}>
  //       <MergedProcessedPointNew mode="new" />
  //     </DocumentTitle>
  //   ),
  // },
  {
    key: urls.CHANGE_TICKET_CONFIG,
    path: urls.CHANGE_TICKET_CONFIG,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '查看变更'}>
          <LayoutContent
            pageCode="page_change-ticket"
            composeBreadcrumbs={value => [
              ...value,
              {
                key: `page_change-ticket`,
                text: props.match?.params.id,
              },
            ]}
          >
            <ChangeTicketView {...props} />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: urls.CHANGE_TICKET_NEW,
    path: urls.CHANGE_TICKET_NEW,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '新建变更'}>
        <LayoutContent
          pageCode="page_change-ticket-create"
          composeBreadcrumbs={value => [
            ...value,
            { key: 'page_change-ticket-create-key', text: '新建线上变更' },
          ]}
        >
          <ChangeTicketNew />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.CHANGE_TICKET_LIST,
    path: urls.CHANGE_TICKET_LIST,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '变更列表'}>
        <LayoutContent pageCode="page_change-tickets">
          <ChangeTicketList />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.CHANGE_TEMPLATE_NEW,
    path: urls.CHANGE_TEMPLATE_NEW,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '新建变更模板'}>
        <LayoutContent
          pageCode="page_change-template-create"
          composeBreadcrumbs={value => [
            ...value,
            { key: 'page_change-template-create-key', text: '新建模板' },
          ]}
        >
          <ChangeTemplateNew />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.CHANGE_TEMPLATE_LIST,
    path: urls.CHANGE_TEMPLATE_LIST,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '变更模板列表'}>
        <LayoutContent pageCode="page_change-templates">
          <ChangeTemplateList />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.EVENT_CENTER_LIST,
    path: urls.EVENT_CENTER_LIST,
    exact: true,
    children: props => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '事件列表'}>
        <LayoutContent pageCode="page_events">
          <EventCenter {...props} />
        </LayoutContent>
      </DocumentTitle>
    ),
  },

  {
    key: urls.ALARM_CONFIGURATION_TEMPLATE_DETAIL,
    path: urls.ALARM_CONFIGURATION_TEMPLATE_DETAIL,
    exact: true,
    children: props => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '告警模板'}>
        <LayoutContent pageCode="page_alarm-template">
          <AlarmConfigurationTemplate mode={TEMPLATE_VIEW_MODE_TYPE.PREVIEW} {...props} />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.ALARM_CONFIGURATION_TEMPLATE_LIST,
    path: urls.ALARM_CONFIGURATION_TEMPLATE_LIST,
    exact: true,
    children: props => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '告警模板列表'}>
        <LayoutContent pageCode="page_alarm-templates">
          <AlarmConfigurationTemplateList {...props} />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.NOTICE_MANAGE_LIST,
    path: urls.NOTICE_MANAGE_LIST,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '公告通知'}>
        <LayoutContent pageCode="page_notice">
          <NoticeManageList />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.NOTICE_MANAGE_DETAIL,
    path: urls.NOTICE_MANAGE_DETAIL,
    exact: true,
    children: props => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '公告详情'}>
        <LayoutContent
          pageCode="page_notice-info"
          composeBreadcrumbs={value => [...value, { key: 'info', text: '公告详情' }]}
        >
          <NoticeManageDetail {...props} />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: BASIC_RESOURCES_BUILDING,
    path: BASIC_RESOURCES_BUILDING,
    exact: true,
    children: props => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '楼栋管理'}>
        <LayoutContent pageCode="page_resource-buildings">
          <BasicResourcesBuilding {...props} />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.BASIC_RESOURCES_IDC,
    path: urls.BASIC_RESOURCES_IDC,
    exact: true,
    children: props => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '机房管理'}>
        <LayoutContent pageCode="page_resource-idcs">
          <BasicResourcesIdc {...props} />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.USER_GROUP_MANAGE_LIST,
    path: urls.USER_GROUP_MANAGE_LIST,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '用户组管理'}>
        <LayoutContent pageCode="page_user-groups">
          <UserGroupManageList />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.USER_GROUP_MANAGE_DETAIL,
    path: urls.USER_GROUP_MANAGE_DETAIL,
    exact: true,
    children: props => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '用户组详情'}>
        <LayoutContent pageCode="page_user-group">
          <UserGroupManageDetail {...props} />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.ROLE_LIST,
    path: urls.ROLE_LIST,
    exact: true,
    children: props => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '角色管理'}>
        <LayoutContent pageCode="page_roles">
          <RoleList {...props} />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.ROLE_ITEM_CONFIG,
    path: urls.ROLE_ITEM_CONFIG,
    exact: true,
    children: props => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '角色详情'}>
        <LayoutContent pageCode="page_role">
          <RoleDetail {...props} />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.POWER_LIST,
    path: urls.POWER_LIST,
    exact: true,
    children: props => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '权限列表'}>
        <LayoutContent pageCode="page_permissions">
          <PowerManageList {...props} />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.ROOM_LIST,
    path: urls.ROOM_LIST,
    exact: true,
    children: props => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '包间管理'}>
        <LayoutContent pageCode="page_resource-rooms">
          <RoomManageList {...props} />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.CABINET_LIST,
    path: urls.CABINET_LIST,
    exact: true,
    children: props => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '机柜管理'}>
        <LayoutContent pageCode="page_resource-cabinets">
          <CabinetManageList {...props} />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: CABINET_DETAILS,
    path: CABINET_DETAILS,
    exact: true,
    children: props => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '机柜详情'}>
        <CabinetManageDetail {...props} />
      </DocumentTitle>
    ),
  },
  {
    key: urls.SPECIFIC_CREATE_CABINET_ITEM,
    path: urls.SPECIFIC_CREATE_CABINET_ITEM,
    exact: true,
    children: props => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '导入机柜'}>
        <LayoutContent
          pageCode="page_resource-cabinets-import"
          composeBreadcrumbs={value => [...value, { key: 'import', text: '新建' }]}
        >
          <EditCabinet {...props} />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.EQUIPMENT_LIST,
    path: urls.EQUIPMENT_LIST,
    exact: true,
    children: props => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '设备管理'}>
        <LayoutContent pageCode="page_resource-devices">
          <EquipmentManage {...props} />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.SPECIFIC_CREATE_EQUIPMENT_ITEM,
    path: urls.SPECIFIC_CREATE_EQUIPMENT_ITEM,
    exact: true,
    children: props => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '导入设备'}>
        <LayoutContent
          pageCode="page_resource-devices-import"
          composeBreadcrumbs={value => [...value, { key: 'devices-import', text: '新建' }]}
        >
          <CreateEquipment {...props} />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.SPECIFIC_EDIT_EQUIPMENT_ITEM,
    path: urls.SPECIFIC_EDIT_EQUIPMENT_ITEM,
    exact: true,
    children: props => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '修改设备'}>
        <LayoutContent
          pageCode="page_device-edit"
          composeBreadcrumbs={value => [...value, { key: 'page_device-edit-key', text: '编辑' }]}
        >
          <EditEquipment {...props} />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  // @FIXME 不使用的页面,需要删除路由，url,页面等未被使用的
  // {
  //   key: urls.DOPOINT_LIST,
  //   path: urls.DOPOINT_LIST,
  //   exact: true,
  //   children: (props) => (
  //     <DocumentTitle title={APP_NAME + '测点管理'}>
  //       <DopointManage {...props} />
  //     </DocumentTitle>
  //   ),
  // },
  // {
  //   key: urls.SPEC_LIST,
  //   path: urls.SPEC_LIST,
  //   exact: true,
  //   children: (
  //     <DocumentTitle title={APP_NAME + '规格管理'}>
  //       <SpecManage />
  //     </DocumentTitle>
  //   ),
  // },
  // {
  //   key: urls.DEVICE_TYPE_LIST,
  //   path: urls.DEVICE_TYPE_LIST,
  //   exact: true,
  //   children: (
  //     <DocumentTitle title={APP_NAME + '设备类型配置'}>
  //       <DeviceTypeList />
  //     </DocumentTitle>
  //   ),
  // },
  // {
  //   key: urls.DEVICE_TYPE_DETAIL,
  //   path: urls.DEVICE_TYPE_DETAIL,
  //   exact: true,
  //   children: (props) => (
  //     <DocumentTitle title={APP_NAME + '设备类型详情'}>
  //       <DeviceTypeDetail {...props} />
  //     </DocumentTitle>
  //   ),
  // },
  {
    key: urls.SPECIFIC_MONITOR_ITEM_CONFIG,
    path: urls.SPECIFIC_MONITOR_ITEM_CONFIG,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '监控项'}>
        <LayoutContent pageCode="page_alarm-template-by-id">
          <SpecificAlarmConfig />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.TEMPLATE_GROUP_VIEW_LIST,
    path: urls.TEMPLATE_GROUP_VIEW_LIST,
    exact: true,
    children: props => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '告警模板组'}>
        <LayoutContent pageCode="page_alarm-template-groups">
          <TemplateGroupView {...props} />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.CREATE_TEMPLATE_GROUP_CONFIG,
    path: urls.CREATE_TEMPLATE_GROUP_CONFIG,
    exact: true,
    children: props => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '新建告警模板组'}>
        <LayoutContent
          pageCode="page_alarm-template-group-create"
          composeBreadcrumbs={value => [
            ...value,
            { key: 'page_alarm-template-group-create-key', text: '新建模板组' },
          ]}
        >
          <CreateTemplateView mode="new" {...props} />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.EDIT_TEMPLATE_GROUP_CONFIG,
    path: urls.EDIT_TEMPLATE_GROUP_CONFIG,
    exact: true,
    children: props => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '修改告警模板组'}>
        <LayoutContent
          pageCode="page_alarm-template-group-edit"
          composeBreadcrumbs={value => [
            ...value,
            { key: 'page_alarm-template-group-edit-key', text: '编辑模板组' },
          ]}
        >
          <CreateTemplateView mode="edit" {...props} />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  // {
  //   key: urls.TEMPLATE_AREA_CONFIG,
  //   path: urls.TEMPLATE_AREA_CONFIG,
  //   exact: true,
  //   children: (props) => (
  //     <DocumentTitle title={APP_NAME + '生效域'}>
  //       <AreaConfig {...props} />
  //     </DocumentTitle>
  //   ),
  // },
  {
    key: urls.VIEW_TEMPLATE_GROUP_CONFIG,
    path: urls.VIEW_TEMPLATE_GROUP_CONFIG,
    exact: true,
    children: props => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '告警模板组'}>
        <LayoutContent pageCode="page_alarm-template-group">
          <ViewTemplateGroup {...props} />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.TOPOLOGY_GRAPHIX,
    path: urls.TOPOLOGY_GRAPHIX,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '设计专业视图'}>
        <TopologyGraphix />
      </DocumentTitle>
    ),
  },
  {
    key: urls.MERGED_MONITOR_CONFIG_LIST,
    path: urls.MERGED_MONITOR_CONFIG_LIST,
    exact: true,
    children: props => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '收敛配置列表'}>
        <LayoutContent pageCode="page_convergence-configuration-list">
          <ConvergenceList {...props} />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.NEW_MERGED_MONITOR_CONFIG,
    path: urls.NEW_MERGED_MONITOR_CONFIG,
    exact: true,
    children: props => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '新建收敛配置'}>
        <LayoutContent
          pageCode="page_convergence-configuration-create"
          composeBreadcrumbs={value => [
            ...value,
            { key: 'page_convergence-configuration-create-key', text: '新建配置' },
          ]}
        >
          <NewConvergenceList {...props} />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.SPECIFIC_MERGED_MONITOR_CONFIG,
    path: urls.SPECIFIC_MERGED_MONITOR_CONFIG,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '收敛配置'}>
        <LayoutContent pageCode="page_convergence-configuration-info">
          <SpecificMergedMonitorConfig />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.EDIT_SPECIFIC_MERGED_MONITOR_CONFIG,
    path: urls.EDIT_SPECIFIC_MERGED_MONITOR_CONFIG,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '修改收敛配置'}>
        <LayoutContent
          pageCode="page_convergence-configuration-edit"
          composeBreadcrumbs={value => [
            ...value,
            { key: 'page_convergence-configuration-edit-key', text: '编辑配置' },
          ]}
        >
          <NewConvergenceList mode="edit" />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.LOG_LIST,
    path: urls.LOG_LIST,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '操作日志'}>
        <LayoutContent pageCode="page_operational-logs">
          <LogList />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.VENDOR_LIST,
    path: urls.VENDOR_LIST,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '合作伙伴列表'}>
        <LayoutContent pageCode="page_vendors">
          <VendorList />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.VENDOR_DETAIL,
    path: urls.VENDOR_DETAIL,
    exact: true,
    children: props => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '供应商详情'}>
        <VendorDetail {...props} />
      </DocumentTitle>
    ),
  },
  {
    key: urls.MODEL_LIST,
    path: urls.MODEL_LIST,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '型号管理'}>
        <LayoutContent pageCode="page_models">
          <ModelManage />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.MODEL_NEW,
    path: urls.MODEL_NEW,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '新建型号'}>
        <LayoutContent
          pageCode="page_model-create"
          composeBreadcrumbs={value => [...value, { key: 'page_model-create', text: '新建' }]}
        >
          <ModelNew />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.VERSION_MANAGE_DEATAIL,
    path: urls.VERSION_MANAGE_DEATAIL,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '版本管理'}>
        <LayoutContent pageCode="page_version-manage">
          <VersionManageDetail />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.MINE_TICKET,
    path: urls.MINE_TICKET,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '待操作工单'}>
        <LayoutContent pageCode="page_mine-tickets">
          <MineTicket />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.WARRANTY_MANAGE_LIST,
    path: urls.WARRANTY_MANAGE_LIST,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '订单列表'}>
        <LayoutContent pageCode="page_warranty-orders">
          <WarrantyList />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.WARRANTY_MANAGE_NEW,
    path: urls.WARRANTY_MANAGE_NEW,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '新建订单'}>
          <LayoutContent
            pageCode="page_warranty-order-create"
            composeBreadcrumbs={value => [
              ...value,
              { key: 'page_warranty-order-create-key', text: '新建维保订单' },
            ]}
          >
            <CreateWarrantyOrder mode="new" {...props} />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: urls.WARRANTY_MANAGE_EDIT,
    path: urls.WARRANTY_MANAGE_EDIT,
    exact: true,
    children: props => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '修改订单'}>
        <LayoutContent
          pageCode="page_warranty-order-edit"
          composeBreadcrumbs={value => [
            ...value,
            { key: 'page_warranty-order-edit-key', text: '编辑资产维保' },
          ]}
        >
          <CreateWarrantyOrder mode="edit" {...props} />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.WARRANTY_MANAGE_DETAIL,
    path: urls.WARRANTY_MANAGE_DETAIL,
    exact: true,
    children: props => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '订单详情'}>
        <LayoutContent
          pageCode="page_warranty-order-info"
          composeBreadcrumbs={value => [
            ...value,
            { key: 'page_warranty-order-info', text: props.match?.params.id },
          ]}
        >
          <WarrantyDetail {...props} />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.WARRANTY_POOL_LIST,
    path: urls.WARRANTY_POOL_LIST,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '预维保池'}>
        <WarrantyPool />
      </DocumentTitle>
    ),
  },
  {
    key: urls.ARRIVAL_ASSET_LIST,
    path: urls.ARRIVAL_ASSET_LIST,
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '预到货池'}>
        <LayoutContent pageCode="page_arrival-asset">
          <ArrivalAssetList />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: urls.ARRIVAL_ASSET_NEW,
    path: urls.ARRIVAL_ASSET_NEW,
    exact: true,
    children: props => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '导入'}>
        <LayoutContent
          pageCode="page_arrival-asset-import"
          composeBreadcrumbs={value => [
            ...value,
            { key: 'page_arrival-asset-import-key', text: '导入' },
          ]}
        >
          <ArrivalAssetNew {...props} />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: '/403',
    path: '/403',
    exact: true,
    children: (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '无权限'}>
        <LayoutContent pageCode="__fake-page_403" pageAuthorizationCheck={false} fixedContent>
          <ErrorPage403 />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  // {
  //   key: urls.IDC_WORKBENCH,
  //   path: urls.IDC_WORKBENCH,
  //   exact: true,
  //   children: props => {
  //     return (
  //       <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '机房工作台'}>
  //         <LayoutContent pageCode="page_idc-workbench" subscribeNotifications>
  //           <IdcWorkbench {...props} />
  //         </LayoutContent>
  //       </DocumentTitle>
  //     );
  //   },
  // },
  {
    key: DRILL_CONFIG_LIST_ROUTE_PATH,
    path: DRILL_CONFIG_LIST_ROUTE_PATH,
    exact: true,
    children: props => (
      <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '配置中心 - 演练配置'}>
        <LayoutContent pageCode={TICKET_ROUTE_PATH_AUTH_CODE_MAPPER[DRILL_CONFIG_LIST_ROUTE_PATH]}>
          <DrillConfigList {...props} />
        </LayoutContent>
      </DocumentTitle>
    ),
  },
  {
    key: DRILL_CONFIG_CREATE_ROUTE_PATH,
    path: DRILL_CONFIG_CREATE_ROUTE_PATH,
    exact: true,
    children: props => {
      const code = TICKET_ROUTE_PATH_AUTH_CODE_MAPPER[DRILL_CONFIG_CREATE_ROUTE_PATH];

      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '配置中心 - 新建演练配置'}>
          <LayoutContent
            pageCode={code}
            composeBreadcrumbs={value => [...value, { key: `${code}-key`, text: '新建演练配置' }]}
          >
            <DrillConfigMutator {...props} mode="new" />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: DRILL_CONFIG_EDIT_ROUTE_PATH,
    path: DRILL_CONFIG_EDIT_ROUTE_PATH,
    exact: true,
    children: props => {
      const code = TICKET_ROUTE_PATH_AUTH_CODE_MAPPER[DRILL_CONFIG_EDIT_ROUTE_PATH];

      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '配置中心 - 编辑演练配置'}>
          <LayoutContent
            pageCode={code}
            composeBreadcrumbs={value => [...value, { key: `${code}-key`, text: '编辑演练配置' }]}
          >
            <DrillConfigMutator {...props} mode="edit" />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: DRILL_CONFIG_COPY_ROUTE_PATH,
    path: DRILL_CONFIG_COPY_ROUTE_PATH,
    exact: true,
    children: props => {
      const code = TICKET_ROUTE_PATH_AUTH_CODE_MAPPER[DRILL_CONFIG_COPY_ROUTE_PATH];

      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '配置中心 - 复制演练配置'}>
          <LayoutContent
            pageCode={code}
            composeBreadcrumbs={value => [...value, { key: `${code}-key`, text: '复制演练配置' }]}
          >
            <DrillConfigMutator {...props} mode="copy" />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: DRILL_CONFIG_DETAIL_ROUTE_PATH,
    path: DRILL_CONFIG_DETAIL_ROUTE_PATH,
    exact: true,
    children: props => {
      const { name } = getLocationSearchMap(props.location.search, ['name']);
      const code = TICKET_ROUTE_PATH_AUTH_CODE_MAPPER[DRILL_CONFIG_DETAIL_ROUTE_PATH];

      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '配置中心 - 演练配置详情'}>
          <LayoutContent
            pageCode={code}
            composeBreadcrumbs={value => [...value, { key: `${code}-key`, text: name }]}
          >
            <DrillConfigDetail {...props} />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: STANDARD_CHANGE_LIBRARIES_ROUTE_PATH,
    path: STANDARD_CHANGE_LIBRARIES_ROUTE_PATH,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '标准变更库'}>
          <LayoutContent pageCode="page_ticket_standard-change-library-list">
            <StandardChangeLibraries {...props} />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },

  {
    key: STANDARD_CHANGE_LIBRARY_CREATE_ROUTE_PATH,
    path: STANDARD_CHANGE_LIBRARY_CREATE_ROUTE_PATH,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '标准变更库'}>
          <LayoutContent
            pageCode={STANDARD_CHANGE_LIBRARY_CREATE_ROUTE_AUTH_CODE}
            composeBreadcrumbs={value => [
              ...value,
              { key: STANDARD_CHANGE_LIBRARY_CREATE_ROUTE_AUTH_CODE, text: '新建标准变更模版' },
            ]}
          >
            <StandardChangeLibraryMutator {...props} />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: STANDARD_CHANGE_LIBRARY_EDIT_ROUTE_PATH,
    path: STANDARD_CHANGE_LIBRARY_EDIT_ROUTE_PATH,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '标准变更库'}>
          <LayoutContent
            pageCode={STANDARD_CHANGE_LIBRARY_EDIT_ROUTE_AUTH_CODE}
            composeBreadcrumbs={value => [
              ...value,
              { key: STANDARD_CHANGE_LIBRARY_ROUTE_PATH, text: '编辑标准变更模版' },
            ]}
          >
            <StandardChangeLibraryMutator {...props} />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: STANDARD_CHANGE_LIBRARY_ROUTE_PATH,
    path: STANDARD_CHANGE_LIBRARY_ROUTE_PATH,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '标准变更库'}>
          <LayoutContent
            pageCode={STANDARD_CHANGE_LIBRARY_EDIT_ROUTE_AUTH_CODE}
            composeBreadcrumbs={value => [
              ...value,
              { key: STANDARD_CHANGE_LIBRARY_ROUTE_PATH, text: '模版详情' },
            ]}
          >
            <StandardChangeLibraryDetail {...props} />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: CHANGE_OFFLIEN_CREATE_ROUTE_PATH,
    path: CHANGE_OFFLIEN_CREATE_ROUTE_PATH,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '线下变更（内测版）'}>
          <LayoutContent
            pageCode={CHANGE_OFFLIEN_CREATE_ROUTE_AUTH_CODE}
            composeBreadcrumbs={value => [
              ...value,
              { key: CHANGE_OFFLIEN_CREATE_ROUTE_PATH, text: '新建线下变更' },
            ]}
          >
            <ChangeOfflineMutator {...props} />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: CHANGE_OFFLIEN_EDIT_ROUTE_PATH,
    path: CHANGE_OFFLIEN_EDIT_ROUTE_PATH,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '线下变更（内测版）'}>
          <LayoutContent
            pageCode={CHANGE_OFFLIEN_EDIT_ROUTE_AUTH_CODE}
            composeBreadcrumbs={value => [
              ...value,
              { key: CHANGE_OFFLIEN_EDIT_ROUTE_PATH, text: '编辑线下变更' },
            ]}
          >
            <ChangeOfflineMutator {...props} />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: CHANGE_OFFLIENS_ROUTE_PATH,
    path: CHANGE_OFFLIENS_ROUTE_PATH,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '线下变更（内测版）'}>
          <LayoutContent pageCode={CHANGE_OFFLIENS_ROUTE_AUTH_CODE}>
            <ChangeOfflines {...props} />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: CHANGE_OFFLIEN_ROUTE_PATH,
    path: CHANGE_OFFLIEN_ROUTE_PATH,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '线下变更（内测版）'}>
          <LayoutContent
            pageCode={CHANGE_OFFLIEN_EDIT_ROUTE_AUTH_CODE}
            composeBreadcrumbs={value => [
              ...value,
              {
                key: CHANGE_OFFLIEN_ROUTE_PATH,
                text: props.match?.params.id,
              },
            ]}
          >
            <ChangeTicketView {...props} />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: EVENT_CREATE_ROUTE_PATH,
    path: EVENT_CREATE_ROUTE_PATH,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '事件管理（内测版）'}>
          <LayoutContent
            pageCode={EVENT_CREATE_ROUTE_AUTH_CODE}
            composeBreadcrumbs={value => [
              ...value,
              {
                key: EVENT_CREATE_ROUTE_PATH,
                text: '新建',
              },
            ]}
          >
            <EventMutatorBeta {...props} />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: EVENT_LIST_ROUTE_PATH,
    path: EVENT_LIST_ROUTE_PATH,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '事件管理（内测版）'}>
          <LayoutContent pageCode={EVENT_LIST_ROUTE_AUTH_CODE}>
            <EventsBeta {...props} />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },

  {
    key: EVENT_ROUTE_PATH,
    path: EVENT_ROUTE_PATH,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '事件管理（内测版）'}>
          <LayoutContent
            pageCode={EVENT_ROUTE_AUTH_CODE}
            composeBreadcrumbs={value => [
              ...value,
              {
                key: EVENT_ROUTE_PATH,
                text: props.match?.params.id,
              },
            ]}
          >
            <EventBeta {...props} />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },

  {
    key: EMERGENCY_PROCEE_LIST_ROUTE_PATH,
    path: EMERGENCY_PROCEE_LIST_ROUTE_PATH,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '应急流程'}>
          <LayoutContent pageCode={EMERGENCY_PROCEE_LIST_ROUTE_AUTH_CODE}>
            <EmergencyProcessList {...props} />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },

  {
    key: EMERGENCY_PROCEE_ROUTE_PATH,
    path: EMERGENCY_PROCEE_ROUTE_PATH,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '应急流程'}>
          <LayoutContent
            pageCode={EMERGENCY_PROCEE_ROUTE_AUTH_CODE}
            composeBreadcrumbs={value => [
              ...value,
              {
                key: EMERGENCY_PROCEE_ROUTE_PATH,
                text: props.match?.params.id,
              },
            ]}
          >
            <EmergencyProcess {...props} />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: EMERGENCY_PROCEE_CREATE_ROUTE_PATH,
    path: EMERGENCY_PROCEE_CREATE_ROUTE_PATH,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '应急流程'}>
          <LayoutContent
            pageCode={EMERGENCY_PROCEE_CREATE_ROUTE_AUTH_CODE}
            composeBreadcrumbs={value => [
              ...value,
              {
                key: EMERGENCY_PROCEE_CREATE_ROUTE_PATH,
                text: '新建应急流程',
              },
            ]}
          >
            <EmergencyProcessMutator {...props} />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },

  {
    key: EMERGENCY_PROCEE_EDIT_ROUTE_PATH,
    path: EMERGENCY_PROCEE_EDIT_ROUTE_PATH,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '应急流程'}>
          <LayoutContent
            pageCode={EMERGENCY_PROCEE_EDIT_ROUTE_AUTH_CODE}
            composeBreadcrumbs={value => [
              ...value,
              {
                key: EMERGENCY_PROCEE_EDIT_ROUTE_PATH,
                text: '编辑应急流程',
              },
            ]}
          >
            <EmergencyProcessMutator {...props} />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: CHANGE_ONLINE_CREATE_ROUTE_PATH,
    path: CHANGE_ONLINE_CREATE_ROUTE_PATH,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '变更管理'}>
          <LayoutContent
            pageCode={CHANGE_ONLINE_CREATE_ROUTE_AUTH_CODE}
            composeBreadcrumbs={value => [
              ...value,
              {
                key: CHANGE_ONLINE_CREATE_ROUTE_AUTH_CODE,
                text: '新建变更',
              },
            ]}
          >
            <ChangeOnlineMutator {...props} />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: CHANGE_TEMPLATE_CREATE_ROUTE_PATH,
    path: CHANGE_TEMPLATE_CREATE_ROUTE_PATH,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '变更模板'}>
          <LayoutContent
            pageCode={CHANGE_TEMPLATE_CREATE_ROUTE_AUTH_CODE}
            composeBreadcrumbs={value => [
              ...value,
              {
                key: CHANGE_TEMPLATE_CREATE_ROUTE_AUTH_CODE,
                text: '新建模板',
              },
            ]}
          >
            <ChangeTemplateMutator {...props} />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: CHANGE_TEMPLATE_DETAIL_ROUTE_PATH,
    path: CHANGE_TEMPLATE_DETAIL_ROUTE_PATH,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '变更模板'}>
          <LayoutContent
            pageCode={CHANGE_TEMPLATE_DETAIL_ROUTE_AUTH_CODE}
            composeBreadcrumbs={value => [
              ...value,
              {
                key: CHANGE_TEMPLATE_DETAIL_ROUTE_AUTH_CODE,
                text: props.match?.params.id,
              },
            ]}
          >
            <ChangeOnlineTemplateDetail {...props} />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: CHANGE_TEMPLATE_LIST_ROUTE_PATH,
    path: CHANGE_TEMPLATE_LIST_ROUTE_PATH,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '变更模板'}>
          <LayoutContent pageCode={CHANGE_TEMPLATE_LIST_ROUTE_AUTH_CODE}>
            <ChangeOnlineTemplateList {...props} />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },

  {
    key: CHANGE_TEMPLATE_EDIT_ROUTE_PATH,
    path: CHANGE_TEMPLATE_EDIT_ROUTE_PATH,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '变更模板'}>
          <LayoutContent
            pageCode={CHANGE_TEMPLATE_EDIT_ROUTE_AUTH_CODE}
            composeBreadcrumbs={value => [
              ...value,
              {
                key: CHANGE_TEMPLATE_EDIT_ROUTE_AUTH_CODE,
                text: props.match?.params.id,
              },
            ]}
          >
            <ChangeTemplateMutator {...props} />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: CHANGE_ONLINE_LIST_ROUTE_PATH,
    path: CHANGE_ONLINE_LIST_ROUTE_PATH,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '变更管理'}>
          <LayoutContent pageCode={CHANGE_ONLINE_LIST_ROUTE_AUTH_CODE}>
            <ChangeOnlineList {...props} />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: CHANGE_ONLINE_EDIT_ROUTE_PATH,
    path: CHANGE_ONLINE_EDIT_ROUTE_PATH,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '变更管理'}>
          <LayoutContent
            pageCode={CHANGE_ONLINE_EDIT_ROUTE_AUTH_CODE}
            composeBreadcrumbs={value => [
              ...value,
              {
                key: CHANGE_ONLINE_EDIT_ROUTE_AUTH_CODE,
                text: props.match?.params.id,
              },
            ]}
          >
            <ChangeOnlineMutator {...props} />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
  {
    key: CHANGE_ONLINE_DETAIL_ROUTE_PATH,
    path: CHANGE_ONLINE_DETAIL_ROUTE_PATH,
    exact: true,
    children: props => {
      return (
        <DocumentTitle title={DOCUMENT_TITLE_PREFIX + '查看变更'}>
          <LayoutContent
            pageCode={CHANGE_ONLINE_DETAIL_ROUTE_AUTH_CODE}
            composeBreadcrumbs={value => [
              ...value,
              {
                key: CHANGE_ONLINE_DETAIL_ROUTE_AUTH_CODE,
                text: props.match?.params.id,
              },
            ]}
          >
            <ChangeTicketView {...props} />
          </LayoutContent>
        </DocumentTitle>
      );
    },
  },
];

/**
 *
 * 生成各个工单在列表、编辑、新建、详情下的pageCode
 */
function getTicketPageCde({
  ticketType,
  mode,
}: {
  ticketType: string | undefined;
  mode: 'create' | 'edit' | 'info' | 'list';
}) {
  let pageCode;
  if (ticketType === 'emergency_drill') {
    pageCode = `page_ticket-emergency-drill-${mode}`;
  }
  if (ticketType === 'access_card_auth') {
    pageCode = `page_ticket-access-card-${mode}`;
  }
  if (ticketType === 'device_general') {
    pageCode = `page_device-general-${mode}`;
  }
  if (ticketType === 'maintenance') {
    pageCode = `page_ticket-maintenance-${mode}`;
  }
  if (ticketType === 'power') {
    pageCode = `page_ticket-power-${mode}`;
  }
  if (ticketType === 'on_off') {
    pageCode = 'page_on-off-create';
  }
  if (ticketType === 'access') {
    pageCode = `page_ticket-access-${mode}`;
  }
  if (ticketType === 'warehouse') {
    pageCode = `page_ticket-warehouse-${mode}`;
  }
  if (ticketType === 'inventory') {
    pageCode = `page_ticket-inventory-${mode}`;
  }
  if (ticketType === 'accept') {
    pageCode = `page_ticket-accept-${mode}`;
  }
  if (ticketType === 'repair') {
    pageCode = `page_ticket-repair-${mode}`;
  }
  if (ticketType === 'inspection') {
    pageCode = `page_ticket-inspection-${mode}`;
  }
  if (ticketType === 'visitor') {
    pageCode = `page_ticket-visitor-${mode}`;
  }
  if (ticketType === 'risk_register') {
    pageCode = `page_ticket-risk-register-${mode}`;
  }
  if (ticketType === 'it_service') {
    pageCode = `page_ticket-it-service-${mode}`;
  }
  if (ticketType === 'risk_check') {
    pageCode = `page_ticket-risk-check-${mode}`;
  }
  return pageCode;
}
