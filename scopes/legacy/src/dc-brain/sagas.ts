import type { ForkEffect } from 'redux-saga/effects';

import { rolesWatchers } from '@manyun/auth-hub.state.roles';
import { userWatchers } from '@manyun/auth-hub.state.user';
import { userGroupsWatchers } from '@manyun/auth-hub.state.user-groups';
import { usersWatchers } from '@manyun/auth-hub.state.users';
import { approvalCenterWatchers } from '@manyun/bpm.state.approval-center';
import { authRequestsWatchers } from '@manyun/bpm.state.auth-request';
import { customersWatchers } from '@manyun/crm.state.customers';
import { staffScheduleWatchers } from '@manyun/hrm.state.staff-schedule';
import { coursesWatchers } from '@manyun/knowledge-hub.state.courses';
import { coursewaresWatchers } from '@manyun/knowledge-hub.state.coursewares';
import { examsWatchers } from '@manyun/knowledge-hub.state.exams';
import { papersWatchers } from '@manyun/knowledge-hub.state.papers';
import { questionsWatchers } from '@manyun/knowledge-hub.state.questions';
import { skillsWatchers } from '@manyun/knowledge-hub.state.skills';
import { alarmConfigurationTemplateWatchers } from '@manyun/monitoring.state.alarm-configuration-template';
import { alarmNotificationConfigureWatchers } from '@manyun/monitoring.state.alarm-notification-configure';
import { alarmsMonitoringBoardWatchers } from '@manyun/monitoring.state.alarms-monitoring-board';
import { ticketWatchers as dashboardIdcWatchers } from '@manyun/monitoring.state.dashboard-idc';
import { ruleTypesWatchers } from '@manyun/monitoring.state.rule-types';
import { subscriptionsWatchers as monitoringSubscriptionsWatchers } from '@manyun/monitoring.state.subscriptions';
import { topologyWatchers } from '@manyun/monitoring.state.topology';
import { onSiteMessagesWatchers } from '@manyun/notification-hub.state.on-site-messages';
import { deviceTypesWatchers } from '@manyun/resource-hub.state.device-types';
import { metaDataWatchers } from '@manyun/resource-hub.state.meta-data';
import { pointsWatchers } from '@manyun/resource-hub.state.points';
import { roomTypeWatchers } from '@manyun/resource-hub.state.room-type';
import { eventWatchers } from '@manyun/ticket.state.event';
import { ticketTypeWatchers } from '@manyun/ticket.state.ticket-type';

import { deviceWatchers } from '@manyun/dc-brain.legacy.redux/__next/device';
// @ts-ignore: Could not find a declaration file
import alarmTemplateWatchs from '@manyun/dc-brain.legacy.redux/sagas/alarmConfigurationTemplateSaga';
// @ts-ignore: Could not find a declaration file
import alarmDoPointWachers from '@manyun/dc-brain.legacy.redux/sagas/alarmSaga';
// @ts-ignore: Could not find a declaration file
import alarmScreenWatchs from '@manyun/dc-brain.legacy.redux/sagas/alarmScreenSaga';
// @ts-ignore: Could not find a declaration file
import alarmTransmissionRecordWatchers from '@manyun/dc-brain.legacy.redux/sagas/alarmTransmissionRecordSaga';
// @ts-ignore: Could not find a declaration file
import alterInfoWatchers from '@manyun/dc-brain.legacy.redux/sagas/alterInfoSaga';
// @ts-ignore: Could not find a declaration file
import approvalConfigWatchers from '@manyun/dc-brain.legacy.redux/sagas/approvalConfigSaga';
// @ts-ignore: Could not find a declaration file
import approveCenterWatchers from '@manyun/dc-brain.legacy.redux/sagas/approveCenterSaga';
// @ts-ignore: Could not find a declaration file
import arrivalAssetWatchers from '@manyun/dc-brain.legacy.redux/sagas/arrivalAssetSaga';
// @ts-ignore: Could not find a declaration file
import basicResourcesBuildingSaga from '@manyun/dc-brain.legacy.redux/sagas/basicResourcesBuildingSaga';
// @ts-ignore: Could not find a declaration file
import basicResourcesIdcSaga from '@manyun/dc-brain.legacy.redux/sagas/basicResourcesIdcSaga';
// @ts-ignore: Could not find a declaration file
import batteryWatchers from '@manyun/dc-brain.legacy.redux/sagas/batterySaga';
// @ts-ignore: Could not find a declaration file
import borrowsAndReturnWatchers from '@manyun/dc-brain.legacy.redux/sagas/borrowsAndReturnSaga';
// @ts-ignore: Could not find a declaration file
import cabinetManageWachers from '@manyun/dc-brain.legacy.redux/sagas/cabinetSaga';
// @ts-ignore: Could not find a declaration file
import changeWatchers from '@manyun/dc-brain.legacy.redux/sagas/changeSaga';
// @ts-ignore: Could not find a declaration file
import channelConfigWatchers from '@manyun/dc-brain.legacy.redux/sagas/channelConfigSaga';
// @ts-ignore: Could not find a declaration file
import customAlarmConfigurationWatchers from '@manyun/dc-brain.legacy.redux/sagas/customAlarmConfigurationSaga';
// @ts-ignore: Could not find a declaration file
import deviceMonitoringWatchers from '@manyun/dc-brain.legacy.redux/sagas/deviceMonitoringSaga';
// @ts-ignore: Could not find a declaration file
import deviceTypeWatchers from '@manyun/dc-brain.legacy.redux/sagas/deviceTypeSaga';
// @ts-ignore: Could not find a declaration file
import doPointManageWachers from '@manyun/dc-brain.legacy.redux/sagas/doPointSaga';
// @ts-ignore: Could not find a declaration file
import equipmentManageWachers from '@manyun/dc-brain.legacy.redux/sagas/equipmentSaga';
// @ts-ignore: Could not find a declaration file
import eventCenterWatchers from '@manyun/dc-brain.legacy.redux/sagas/eventCenterSaga';
// @ts-ignore: Could not find a declaration file
import handlerWatchers from '@manyun/dc-brain.legacy.redux/sagas/handlerSaga';
// @ts-ignore: Could not find a declaration file
import idcWorkbenchSaga from '@manyun/dc-brain.legacy.redux/sagas/idcWorkbenchSaga';
// @ts-ignore: Could not find a declaration file
import infraRoomGraphixWatchers from '@manyun/dc-brain.legacy.redux/sagas/infraRoomGraphixSaga';
// @ts-ignore: Could not find a declaration file
import insideNoticeWatchers from '@manyun/dc-brain.legacy.redux/sagas/insideNoticeSaga';
// @ts-ignore: Could not find a declaration file
import inspectionConfigWatchers from '@manyun/dc-brain.legacy.redux/sagas/inspectionConfigSaga';
// @ts-ignore: Could not find a declaration file
import inventoryAnalysticsWatchers from '@manyun/dc-brain.legacy.redux/sagas/inventoryAnalysticsSaga';
// @ts-ignore: Could not find a declaration file
import inventoryConfigWatchers from '@manyun/dc-brain.legacy.redux/sagas/inventoryConfigSaga';
// @ts-ignore: Could not find a declaration file
import layoutWatchers from '@manyun/dc-brain.legacy.redux/sagas/layoutSaga';
// @ts-ignore: Could not find a declaration file
import mergedMonitorConfigWatchers from '@manyun/dc-brain.legacy.redux/sagas/mergedMonitorConfigSaga';
// @ts-ignore: Could not find a declaration file
import mergedProcessesdPointWatchers from '@manyun/dc-brain.legacy.redux/sagas/mergedProcessesdPointSaga';
// @ts-ignore: Could not find a declaration file
import mineTicketWatchers from '@manyun/dc-brain.legacy.redux/sagas/mineTicketSaga';
// @ts-ignore: Could not find a declaration file
import modelWatchers from '@manyun/dc-brain.legacy.redux/sagas/modelSaga';
// @ts-ignore: Could not find a declaration file
import northUserWatchers from '@manyun/dc-brain.legacy.redux/sagas/northUserSaga';
// @ts-ignore: Could not find a declaration file
import noticeWatchers from '@manyun/dc-brain.legacy.redux/sagas/noticeSaga';
// @ts-ignore: Could not find a declaration file
import operationCheckConfigWatchers from '@manyun/dc-brain.legacy.redux/sagas/operationCheckConfigSaga';
// @ts-ignore: Could not find a declaration file
import powerManageWachers from '@manyun/dc-brain.legacy.redux/sagas/powerSaga';
// @ts-ignore: Could not find a declaration file
import roleManageWachers from '@manyun/dc-brain.legacy.redux/sagas/roleSaga';
// @ts-ignore: Could not find a declaration file
import roomMonitoringWatchers from '@manyun/dc-brain.legacy.redux/sagas/roomMonitoringSaga';
// @ts-ignore: Could not find a declaration file
import roomManageWachers from '@manyun/dc-brain.legacy.redux/sagas/roomSaga';
// @ts-ignore: Could not find a declaration file
import routerWatchers from '@manyun/dc-brain.legacy.redux/sagas/routerSaga';
// @ts-ignore: Could not find a declaration file
import serverNodeWatchers from '@manyun/dc-brain.legacy.redux/sagas/serverNodeSaga';
// @ts-ignore: Could not find a declaration file
import spareWatchers from '@manyun/dc-brain.legacy.redux/sagas/spareSaga';
// @ts-ignore: Could not find a declaration file
import specWatchers from '@manyun/dc-brain.legacy.redux/sagas/specSaga';
// @ts-ignore: Could not find a declaration file
import staffShiftWatchers from '@manyun/dc-brain.legacy.redux/sagas/staffShiftSaga';
// @ts-ignore: Could not find a declaration file
import templateGroupViewWachers from '@manyun/dc-brain.legacy.redux/sagas/templateGroupViewSaga';
// @ts-ignore: Could not find a declaration file
import ticketConfigWatchers from '@manyun/dc-brain.legacy.redux/sagas/ticketConfigSaga';
// @ts-ignore: Could not find a declaration file
import ticketWatchers from '@manyun/dc-brain.legacy.redux/sagas/ticketSaga';
// @ts-ignore: Could not find a declaration file
import topologyGraphixWatchers from '@manyun/dc-brain.legacy.redux/sagas/topologyGraphixSaga';
// @ts-ignore: Could not find a declaration file
import legacyTopologyWatchers from '@manyun/dc-brain.legacy.redux/sagas/topologySaga';
// @ts-ignore: Could not find a declaration file
import userGroupManageSaga from '@manyun/dc-brain.legacy.redux/sagas/userGroupManageSaga';
// @ts-ignore: Could not find a declaration file
import userManageSaga from '@manyun/dc-brain.legacy.redux/sagas/userManageSaga';
// @ts-ignore: Could not find a declaration file
import vendorWatchers from '@manyun/dc-brain.legacy.redux/sagas/vendorSaga';
// @ts-ignore: Could not find a declaration file
import versionManageWatchers from '@manyun/dc-brain.legacy.redux/sagas/versionManageSaga';
// @ts-ignore: Could not find a declaration file
import visitorBlacklistWatchers from '@manyun/dc-brain.legacy.redux/sagas/visitorBlacklistSaga';
// @ts-ignore: Could not find a declaration file
import visitorManagerWatchers from '@manyun/dc-brain.legacy.redux/sagas/visitorManagerSaga';
// @ts-ignore: Could not find a declaration file
import warrantyOrderSagaWatchers from '@manyun/dc-brain.legacy.redux/sagas/warrantyOrderSaga';
// @ts-ignore: Could not find a declaration file
import warrantyPoolSagaWatchers from '@manyun/dc-brain.legacy.redux/sagas/warrantyPoolSaga';
// @ts-ignore: Could not find a declaration file
import workdayManageWatchers from '@manyun/dc-brain.legacy.redux/sagas/workdayManageSaga';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function createSagas(): ForkEffect<any>[] {
  return [
    ...routerWatchers,
    ...alterInfoWatchers,
    ...basicResourcesBuildingSaga,
    ...basicResourcesIdcSaga,
    ...userManageSaga,
    ...userGroupManageSaga,
    ...idcWorkbenchSaga,
    ...alarmScreenWatchs,
    ...roleManageWachers,
    ...powerManageWachers,
    ...roomManageWachers,
    ...cabinetManageWachers,
    ...equipmentManageWachers,
    ...alarmDoPointWachers,
    ...doPointManageWachers,
    ...roomMonitoringWatchers,
    ...deviceMonitoringWatchers,
    ...alarmTemplateWatchs,
    ...templateGroupViewWachers,
    ...legacyTopologyWatchers,
    ...topologyWatchers,
    ...topologyGraphixWatchers,
    ...mergedMonitorConfigWatchers,
    ...eventCenterWatchers,
    ...changeWatchers,
    ...mergedProcessesdPointWatchers,
    ...staffShiftWatchers,
    ...noticeWatchers,
    ...vendorWatchers,
    ...specWatchers,
    ...deviceTypeWatchers,
    ...modelWatchers,
    ...inspectionConfigWatchers,
    ...ticketWatchers,
    ...inventoryConfigWatchers,
    ...operationCheckConfigWatchers,
    ...ticketConfigWatchers,
    ...inventoryAnalysticsWatchers,
    ...layoutWatchers,
    ...versionManageWatchers,
    ...serverNodeWatchers,
    ...northUserWatchers,
    ...spareWatchers,
    ...infraRoomGraphixWatchers,
    ...mineTicketWatchers,
    ...workdayManageWatchers,
    ...visitorManagerWatchers,
    ...visitorBlacklistWatchers,
    ...approvalConfigWatchers,
    ...approveCenterWatchers,
    ...batteryWatchers,
    ...channelConfigWatchers,
    ...insideNoticeWatchers,
    ...warrantyOrderSagaWatchers,
    ...warrantyPoolSagaWatchers,
    ...borrowsAndReturnWatchers,
    ...customAlarmConfigurationWatchers,
    ...handlerWatchers,
    ...arrivalAssetWatchers,
    ...alarmTransmissionRecordWatchers,
    ...deviceWatchers,
    //#region resource-hub
    ...roomTypeWatchers,
    //#endregion
    ...skillsWatchers,
    ...questionsWatchers,
    ...papersWatchers,
    ...examsWatchers,
    ...coursesWatchers,
    ...coursewaresWatchers,
    ...customersWatchers,
    ...rolesWatchers,
    ...usersWatchers,
    ...userGroupsWatchers,
    ...authRequestsWatchers,
    ...userWatchers,
    ...onSiteMessagesWatchers,
    ...dashboardIdcWatchers,
    ...monitoringSubscriptionsWatchers,
    ...metaDataWatchers,
    ...deviceTypesWatchers,
    ...alarmConfigurationTemplateWatchers,
    ...pointsWatchers,
    ...alarmsMonitoringBoardWatchers,
    ...ticketTypeWatchers,
    ...alarmNotificationConfigureWatchers,
    ...ruleTypesWatchers,
    ...approvalCenterWatchers,
    ...eventWatchers,
    ...staffScheduleWatchers,
  ];
}
