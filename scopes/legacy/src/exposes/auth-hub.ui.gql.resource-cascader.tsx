import React from 'react';

import { ResourceCascader } from '@manyun/auth-hub.ui.gql.resource-cascader';
import type { ResourceCascaderProps } from '@manyun/auth-hub.ui.gql.resource-cascader';
import { ConfigProvider } from '@manyun/base-ui.context.config';

export default function MfUserHolidayBalanceCard(props: ResourceCascaderProps) {
  return (
    <ConfigProvider>
      <ResourceCascader {...props} />
    </ConfigProvider>
  );
}
