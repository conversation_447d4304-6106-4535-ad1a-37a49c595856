import '@galiojs/awesome-antd/lib/style/component.less';
import { createBrowserHistory } from 'history';
import React from 'react';
import { Provider } from 'react-redux';
import { Router } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';
import '@manyun/base-ui.style.style/dist/variable.less';
import '@manyun/base-ui.theme.theme/dist/theme.less';
import { store } from '@manyun/dc-brain.store.store';

// 导入全局样式
import '@manyun/dc-brain.legacy.styles/form/index.less';

import MineTickets from '../legacy/pages/mine-tickets';

// 创建一个历史对象供 Router 使用
const history = createBrowserHistory();

export default function MfMineTickets() {
  return (
    <div
      className="mf-mine-tickets-container"
      style={{
        height: '100%',
        width: '100%',
        fontSize: '14px',
        color: 'rgba(0, 0, 0, 0.85)',
        fontFamily:
          '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif',
      }}
    >
      <Provider store={store}>
        <Router history={history}>
          <ConfigProvider>
            <MineTickets />
          </ConfigProvider>
        </Router>
      </Provider>
    </div>
  );
}
