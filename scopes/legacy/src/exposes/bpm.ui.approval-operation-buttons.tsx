import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';
import { ApprovalOperationButtons } from '@manyun/bpm.ui.approval-operation-buttons';
import type { ApprovalOperationButtonsProps } from '@manyun/bpm.ui.approval-operation-buttons';

export default function MfApprovalOperationButtons(props: ApprovalOperationButtonsProps) {
  return (
    <ConfigProvider>
      <ApprovalOperationButtons {...props} />
    </ConfigProvider>
  );
}
