import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { UserHolidayBalanceCard } from '@manyun/hrm.ui.user-holiday-balance-card';
import type { UserHolidayBalanceCardProps } from '@manyun/hrm.ui.user-holiday-balance-card';

export default function MfUserHolidayBalanceCard(props: UserHolidayBalanceCardProps) {
  return (
    <ConfigProvider>
      <UserHolidayBalanceCard {...props} />
    </ConfigProvider>
  );
}
