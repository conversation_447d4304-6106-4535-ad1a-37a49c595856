import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';
import { ApprovalRecordsDropdown } from '@manyun/bpm.ui.approval-records-dropdown';
import type { ApprovalRecordsDropdownProps } from '@manyun/bpm.ui.approval-records-dropdown';
import { AppsProvider } from '@manyun/dc-brain.context.apps';

export default function MfApprovalRecordsDropdown({
  baseURL = '/',
  ...dropdownProps
}: ApprovalRecordsDropdownProps & { baseURL?: string }) {
  return (
    <ConfigProvider>
      <AppsProvider apps={{ dcbase: { baseURL } }}>
        <ApprovalRecordsDropdown {...dropdownProps} />
      </AppsProvider>
    </ConfigProvider>
  );
}
