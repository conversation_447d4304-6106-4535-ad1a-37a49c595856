/* eslint-disable @typescript-eslint/no-explicit-any */
import type { Switches } from '@manyun/dc-brain.service.fetch-switches-from-apollo';

const loadAppAsync = () => {
  import('./dc-brain/dc-brain.app-root');
};

const setupEnvs = (data: Switches, env: any, envCjs: any) => {
  if (data['global.rsa-encryption-public-key'] !== '') {
    env.RSA_ENCRYPTION_PUBLIC_KEY = data['global.rsa-encryption-public-key'];
    envCjs.RSA_ENCRYPTION_PUBLIC_KEY = env.RSA_ENCRYPTION_PUBLIC_KEY;
  }
  env.GLOBAL_PAGE_AUTHORIZATION_CHECK = data['global.page-authorization-check'];
  envCjs.GLOBAL_PAGE_AUTHORIZATION_CHECK = env.GLOBAL_PAGE_AUTHORIZATION_CHECK;
  env.GLOBAL_RESOURCE_AUTHORIZATION_CHECK = data['global.resource-authorization-check'];
  envCjs.GLOBAL_RESOURCE_AUTHORIZATION_CHECK = env.GLOBAL_RESOURCE_AUTHORIZATION_CHECK;
  env.MONITORING_ALARMS_TTS_SERVICE = data['monitoring.alarms-tts-service'];
  envCjs.MONITORING_ALARMS_TTS_SERVICE = env.MONITORING_ALARMS_TTS_SERVICE;
  env.FFS_CAMERAS_SERVICE = data['monitoring.ffs-cameras-service'];
  envCjs.FFS_CAMERAS_SERVICE = env.FFS_CAMERAS_SERVICE;

  if (process.env.NODE_ENV !== 'production') {
    window.__app_envs__ = env;
    window.__app_envs__cjs = envCjs;
  }
};

import('@manyun/dc-brain.service.fetch-switches-from-apollo').then(
  ({ fetchSwitchesFromApollo }) => {
    import('@manyun/dc-brain.app-env.react-app-envs').then(({ env }) => {
      const { env: envCjs } = require('@manyun/dc-brain.app-env.react-app-envs');
      fetchSwitchesFromApollo().then(({ data }) => {
        setupEnvs(data, env, envCjs);

        loadAppAsync();
      });
    });
  }
);
