import { normalize, schema } from 'normalizr';

import { message } from '@manyun/base-ui.ui.message';

import { flattenTreeData } from '@manyun/dc-brain.legacy.utils';

import { callApi } from './api';

/**
 *
 * @param {import('normalizr').schema.EntityOptions} options
 * @return {import('normalizr').Schema}
 */
const getSchema = (
  options = {
    idAttribute: 'metaCode',
  }
) => [new schema.Entity('normalizedList', {}, options)];

/**
 * 处理树形数据
 * @param {any[]} data
 * @param {import('normalizr').schema.EntityOptions} schemaOpts
 */
export function processingReturnData(data, schemaOpts) {
  const parallelList = flattenTreeData(data);
  const normalizedList = normalize(parallelList, getSchema(schemaOpts)).entities.normalizedList;
  return { treeList: data, normalizedList, parallelList };
}

/**
 * processingReturnData 返回的数据格式举例
 * treeList: [
    {
      "metaType": "C1",
      "metaCode": "101",
      "metaName": "高压开关柜",
      "parentCode": "C01",
      "children": [
        {
          "metaType": "C2",
          "metaCode": "10101",
          "metaName": "市电进线柜",
          "parentCode": "C1101",
          "children": [
            {
              "metaType": "C3",
              "metaCode": "POINT10101",
              "metaName": "单侧点配置",
              "parentCode": "C210101",
              "children": []
            },
          ]
        }
      ]
    }
   ]

   normalizedList: {
     "101": {
        "metaType": "C1",
        "metaCode": "101",
        "metaName": "高压开关柜",
        "parentCode": "C01",
     },
     "10101": {
        "metaType": "C2",
        "metaCode": "10101",
        "metaName": "市电进线柜",
        "parentCode": "C1101",
     },
     "POINT10101": {
        "metaType": "C3",
        "metaCode": "POINT10101",
        "metaName": "单侧点配置",
        "parentCode": "C210101",
     }
   }

   parallelList: [
     {
        "metaType": "C1",
        "metaCode": "101",
        "metaName": "高压开关柜",
        "parentCode": "C01",
     },
     {
        "metaType": "C2",
        "metaCode": "10101",
        "metaName": "市电进线柜",
        "parentCode": "C1101",
     },
     {
        "metaType": "C3",
        "metaCode": "POINT10101",
        "metaName": "单侧点配置",
        "parentCode": "C210101",
     }
   ]
 */

/**
 * 获取设备类型告警配置树
 * 接口文档：https://manyun.yuque.com/ewe5b3/sbs6q1/xufoyk
 */
export const getAlarmConfigurationTree = async () => {
  const data = await callApi({
    endpoint: 'dccm/meta/query/monitor/item',
  });
  if (data.response) {
    return processingReturnData(data.response.data);
  } else {
    message.error(data.error || '请求告警配置失败');
  }
};

/**
 * 设备类型三级结构
 * 接口文档：https://manyun.yuque.com/ewe5b3/sbs6q1/bkl44a#nLPKH
 */
export const fetchDeviceCategory = async data => {
  const { response, error } = await callApi({
    endpoint: 'dccm/meta/query/device/category',
    options: {
      method: 'POST',
      data: {
        numbered: data?.numbered,
      },
    },
  });
  if (response) {
    return data?.neednotSpace
      ? processingReturnData(response.data.filter(({ metaCode }) => metaCode !== 'S'))
      : processingReturnData(response.data);
  } else {
    message.error(error || '请求设备类型列表失败');
  }
};

// 父类权限
export const fetchAllPowerApiTreeSelect = async () => {
  const data = await callApi({
    endpoint: 'pm/permission/tree',
    options: {
      method: 'GET',
      params: {},
    },
  });

  if (data.response) {
    return processingReturnData(data.response);
  } else {
    message.error(data.error || '父类权限失败');
  }
};

/**
 * 国家、区域、省、市、区树
 * 接口文档：https://manyun.yuque.com/ewe5b3/sbs6q1/zsugm7
 */
export const fetchAreaTreeMetaQuery = async () => {
  const data = await import('@manyun/dc-brain.state.common/dist/assets/cities.json');
  if (data) {
    return processingReturnData(data.default ? data.default : data);
  }
};

// 获取 告警配置中 单侧点配置树

export const fetchDevicetreeData = async () => {
  const data = await callApi({
    endpoint: 'dccm/meta/query/point/tree',
  });
  if (data.response) {
    return processingReturnData(data.response.data);
  } else {
    message.error(data.error || '请求单侧点配置失败');
  }
};

// 告警配置 新增/编辑  根据点位名称 查询点位树
export const fetchDeviceTreeBySearchValue = async name => {
  const data = await callApi({
    endpoint: 'dccm/meta/query/point/tree/by/name',
    options: {
      method: 'GET',
      params: { name },
    },
  });
  if (data.response) {
    return processingReturnData(data.response.data);
  } else {
    message.error(data.error || '查询点位树失败');
  }
};

/**
 * 查询事件类型
 * 接口文档：https://manyun.yuque.com/ewe5b3/sbs6q1/ascwi3
 */
export const fetchEventType = async () => {
  const { response, error } = await callApi({
    endpoint:
      'dccm/meta/query/category/tree?topCategory=EVENT_TOP_CATEGORY&secondCategory=EVENT_SECOND_CATEGORY',
  });
  if (response) {
    return processingReturnData(response.data, {
      idAttribute: 'metaCode',
      mergeStrategy(entityA, entityB) {
        return [entityA, entityB];
      },
    });
  } else {
    message.error(error || '请求查询事件类型失败');
  }
};

/**
 * 查询自定义测点
 * 接口文档：https://manyun.yuque.com/ewe5b3/sbs6q1/kscumb#teejB
 */
export const fetchCustomSpacePoint = async (idcTag) => {
  const { response, error } = await callApi({
    endpoint: 'dccm/meta/query/space/point',
    options: {
      method: 'GET',
      params: { idcTag },
    },
  });
  if (response) {
    return processingReturnData(response.data);
  } else {
    message.error(error || '查询点位树失败');
  }
};
