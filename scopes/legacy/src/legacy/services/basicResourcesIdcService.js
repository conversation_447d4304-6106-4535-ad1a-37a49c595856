import { callApi } from './api';

export const fetchIdcList = data =>
  callApi({
    endpoint: 'dccm/idc/query',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data: data,
    },
  });

export const fetchIdcTag = () =>
  callApi({
    endpoint: 'dccm/meta/query/idc/list',
    options: {
      method: 'GET',
    },
  });

/** @deprecated 新service在crm仓库下，这期先不tag过来 */
export const fetchGetOperationStatus = () =>
  callApi({
    endpoint: 'dccm/idc/operation/status',
    options: {
      method: 'GET',
    },
  });

export const fetchCreateIdc = param =>
  callApi({
    endpoint: 'dccm/idc/add',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data: param,
    },
  });

export const fetchEditIdc = params =>
  callApi({
    endpoint: 'dccm/idc/update',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data: params,
    },
  });

export const fetchExport = params =>
  callApi({
    endpoint: 'dccm/idc/export',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data: params,
      responseType: 'blob',
    },
  });

export const fetchGetIdcType = () =>
  callApi({
    endpoint: 'dccm/get/idc/type',
    options: {
      method: 'GET',
    },
  });
