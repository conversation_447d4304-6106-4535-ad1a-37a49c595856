import { callApi } from './api';

// 分页查询模板组
export const fetchTemplateGroupViewPage = async data =>
  await callApi({
    endpoint: 'dccm/monitor/scheme/query',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data,
    },
  });

// 启用
export const fetchchangeSwitchAvailable = ({ available, id }) =>
  callApi({
    endpoint: 'dccm/monitor/scheme/update',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data: {
        available,
        id,
      },
    },
  });

// 批量启用
export const fetchchangeSomeSwitchAvailable = async ({ available, schemeIds }) =>
  await callApi({
    endpoint: 'dccm/monitor/scheme/batch/update',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data: {
        available,
        schemeIds,
      },
    },
  });

// 告警模板树
export const fetchTemplateTree = async () =>
  await callApi({
    endpoint: 'dccm/meta/query/monitor/group',
    options: {
      method: 'GET',
      params: {},
    },
  });

// 告警模板树 treeNode
export const fetchTemplateTreeChildren = async type =>
  await callApi({
    endpoint: 'dccm/monitor/group/by/type',
    options: {
      method: 'GET',
      params: {
        type,
      },
    },
  });

// 查看所关联的模板
export const fetchConnectTemplates = async schemeId =>
  await callApi({
    endpoint: 'dccm/monitor/group/query/associated',
    options: {
      method: 'GET',
      params: {
        schemeId,
      },
    },
  });

// 查看所关联的模板
export const fetchTemplateGroupArea = async (configId, configType) =>
  await callApi({
    endpoint: 'dccm/monitor/config/query/by/config',
    options: {
      method: 'GET',
      params: {
        configId,
        configType,
      },
    },
  });

// 生效域配置
export const fetchAreaConfig = async ({
  pageNum,
  pageSize,
  available,
  lastOperatorName,
  name,
  region,
}) =>
  await callApi({
    endpoint: 'dccm/monitor/scheme/target/query',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data: {
        pageNum,
        pageSize,
        available,
        lastOperatorName,
        name,
        region,
      },
    },
  });

// 新建模板组
export const fetchCreateTemplate = async ({
  name,
  available,
  monitorConfigs,
  lastOperator,
  groupIds,
}) =>
  await callApi({
    endpoint: 'dccm/monitor/scheme/add',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data: {
        name,
        available,
        monitorConfigs,
        lastOperator,
        groupIds,
      },
    },
  });

// 查询所有模板组
export const fetchAllTemplateGroup = async () =>
  await callApi({
    endpoint: 'dccm/monitor/scheme/query/all',
  });

// 查看所关联的模板
export const fetchConnectTemplatesByTemplateGroupId = async schemeId =>
  await callApi({
    endpoint: 'dccm/monitor/group/query/associated',
    options: {
      method: 'GET',
      params: {
        schemeId,
      },
    },
  });

// 取消关联的生效域
export const fetchCancleConnectAreaConfig = async data =>
  await callApi({
    endpoint: 'dccm/monitor/config/delete',
    options: {
      params: data,
    },
  });

// 编辑模板组
export const fetchUpdateTemplateGroupConfig = async ({
  id,
  name,
  available,
  monitorConfigs,
  lastOperator,
  groupIds,
}) =>
  await callApi({
    endpoint: 'dccm/monitor/scheme/update',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data: {
        id,
        name,
        available,
        monitorConfigs,
        lastOperator,
        groupIds,
      },
    },
  });

// 区域配置生效域关联
export const fetchConnectAreaToTemplate = async monitorConfigs =>
  await callApi({
    endpoint: 'dccm/monitor/config/associate/region',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data: monitorConfigs,
    },
  });

//模板组 生效域关联
export const fetchConnectAreaToTemplateGroup = async monitorConfigs =>
  await callApi({
    endpoint: 'dccm/monitor/scheme/associate/region',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data: monitorConfigs,
    },
  });

// 更新人
export const fetchAllModifier = async () => {
  const { response, error } = await callApi({
    endpoint: 'pm/user/userIdAndNameMap',
    options: {
      method: 'GET',
      contentType: 'application/json',
    },
  });
  if (error) {
    return;
  }
  let newList = [];
  if (response) {
    Object.keys(response).map(key => {
      newList.push({ key: key, value: response[key] });

      return newList;
    });
    return newList;
  }
  return newList;
};

// 通过模板的metaCode 请求模板的列表信息
export const fetchTemplateMessByMetaCode = async id =>
  await callApi({
    endpoint: 'dccm/monitor/group/query/by/id',
    options: {
      method: 'GET',
      params: { id },
    },
  });

/**
 * 查询生效域下模板组数量
 */
export const fetchTemplateNum = async () =>
  await callApi({
    endpoint: 'dccm/monitor/scheme/get/number',
  });

// 通过模板的metaCode 请求自定义告警的列表信息
export const fetchCustomTemplateMessByMetaCode = async id =>
  await callApi({
    endpoint: 'dccm/monitor/custom/group/query/detail',
    options: {
      method: 'POST',
      data: { id },
    },
  });
