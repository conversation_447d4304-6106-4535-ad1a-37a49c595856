import { callApi } from './api';

export const fetchWorkdayList = data => {
  return callApi({
    endpoint: 'pm/statutoryHoliday/yearList',
    options: {
      method: 'POST',
      data,
    },
  });
};

export const addWorkday = data => {
  return callApi({
    endpoint: 'pm/statutoryHoliday/create',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data,
    },
  });
};

export const editWorkday = data => {
  return callApi({
    endpoint: 'pm/statutoryHoliday/update',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data,
    },
  });
};

/**
 * 查询工作日详情
 */
export const fetchWorkdayDetail = data =>
  callApi({
    endpoint: 'pm/statutoryHoliday/info',
    options: {
      method: 'POST',
      data,
    },
  });
