import { callApi } from './api';

/** @typedef BasicInfo
 * @property {'NORMAL_FIRST' | 'NORMAL_SECOND' | 'NORMAL_THIRD'|'STANDARD'|'URGENT'} riskLevel 变更等级
 * @property {string} changeType 变更类型
 * @property {string} title 标题
 * @property {string} reason 原因
 * @property {string} templateName 模板名称
 * @property {string} templateId 模板id
 *
 * @typedef StepList 步骤列表
 * @property {number} stepOrder 步骤ID
 * @property {string} stepName 步骤名称
 * @property {string} stepDesc 步骤描述
 * @property {number} ola ola
 * @property {string} stepType 步骤类型
 * @property {'LIMIT'|'CUSTOMIZE'} opType 操作步骤的操作类型
 * @property {string} operate 操作方法
 * @property {string} opObjectName 设备类型 | 跑位目标
 * @property {string} opObjectCode 设备类型 | 跑位目标
 * @property {string} pointCode 测点Code
 * @property {string} pointName 测点名称
 * @property {number} expectedValue 预判值
 * @property {string} pointValueText DI量的状态枚举
 * @property {'PERSON'|'SYSTEM'} exeWay 执行方式
 * @property {'PERSON'|'SYSTEM'} identifyWay 验证方式
 *
 * @typedef CheckItemInfoList
 * @property {string} unit 单位
 * @property {string} dataType 类型
 * @property {string} pointValueText DI量的状态枚举
 * @property {string} pointCode 测点Code
 * @property {string} pointName 测点名称
 * @property {array} operatorList 类型
 * @property {number} expectedValue 预判值
 * @property {'PERSON'|'SYSTEM'} identifyWay 验证类型
 * @property {string} exceptionHandle 异常处理
 * @property {string} maxInfluencesStep 影响周期
 * @property {string} expectedResult 自定义类型的预判值
 *
 * @typedef CheckDeviceInfoList
 * @property {CheckItemInfoList[]} checkItemInfoList
 * @property {string} deviceType 设备类型
 *
 * @typedef deviceInfoList
 * @property {string} deviceTag
 * @property {string} roomTag
 * @property {string} deviceGuid
 *
 *  @typedef MatchObjectInfoList
 * @property {string} name
 * @property {string} roomTag
 * @property {string} code
 */

/**
 * 查询变更模板列表
 * {@link http://172.16.0.87:8085/swagger-ui.html#/ API 文档}
 * @param {object} data
 * @param {string} [data.templateId] 模板ID
 * @param {string} [data.changeType] 变更类型
 * @param {string} [data.creatorId] 创建人ID
 * @param {string} [data.riskLevel] 变更等级
 * @param {string} [data.templateName] 名称
 * @param {string} [data.modifyStartTime] 修改开始时间
 * @param {string} [data.modifyEndTime] 修改结束时间
 * @param {number} data.pageNum
 * @param {number} data.pageSize
 */
export const fetchData = data =>
  callApi({
    endpoint: 'dcom/change/template/list',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 查询变更列表
 * {@link https://manyun.yuque.com/ewe5b3/pkinbk/fgawab#N9LyH API 文档}
 * @param {object} data
 * @param {string} [data.changeOrderId] 变更ID
 * @param {string} [data.idcTag] 机房
 * @param {string} [data.blockTag] 楼
 * @param {string} [data.changeType] 变更类型
 * @param {string} [data.creatorId] 提单人ID
 * @param {string} [data.planStartTime] 计划变更开始时间
 * @param {string} [data.planEndTime] 计划变更完成时间
 * @param {string} [data.realStartTime] 执行变更开始时间
 * @param {string} [data.realEndTime] 执行变更完成时间
 * @param {[array]} [data.statusList] 变更状态
 * @param {string} [data.riskLevel] 变更等级
 * @param {string} [data.title] 变更标题
 * @param {number} data.pageNum
 * @param {number} data.pageSize
 */
export const fetchTicketData = data =>
  callApi({
    endpoint: 'dcom/change/order/list',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 保存变更模板
 * {@link https://manyun.yuque.com/ewe5b3/pkinbk/fgawab#aof4D API 文档}
 * @param {BasicInfo} data
 * @param {string} data.executeRoleCode 执行角色code
 * @param {string} data.executeRoleName 执行角色name
 * @param {StepList[]} data.stepList 步骤列表
 * @param {CheckDeviceInfoList[]} data.stepList.checkDeviceInfoList
 */
export const saveTemplate = data =>
  callApi({
    endpoint: 'dcom/change/template/save',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 提交变更模板
 * {@link https://manyun.yuque.com/ewe5b3/pkinbk/fgawab#4w8Cc API 文档}
 * @param {BasicInfo} data
 * @param {string} data.executeRoleCode 执行角色code
 * @param {string} data.executeRoleName 执行角色name
 * @param {StepList[]} data.stepList 步骤列表
 * @param {CheckDeviceInfoList[]} data.stepList.checkDeviceInfoList
 */
export const submitTemplate = data =>
  callApi({
    endpoint: 'dcom/change/template/submit',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * * @deprecated use@manyun/ticket.service.fetch-change-template
 * 变更模板详情
 * {@link https://manyun.yuque.com/ewe5b3/pkinbk/fgawab#4w8Cc API 文档}
 * @param {object} data
 * @param {string} data.changeOrderId
 *
 */
export const getTemplateDetailInfo = templateId =>
  callApi({
    endpoint: 'dcom/change/template/detail',
    options: {
      method: 'POST',
      data: { templateId },
    },
  });

/**
 * 变更模板操作记录
 * {@link https://manyun.yuque.com/ewe5b3/pkinbk/fgawab#FX0UA API 文档}
 * @param {object} data
 * @param {number} data.pageNum
 * @param {number} data.pageSize
 * @param {string} data.targetId 变更id
 * @param {string} data.targetType 操作记录的类型
 */
export const fetchTemplateOperationRecord = data =>
  callApi({
    endpoint: 'dcom/modify/query/list',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 可用模板查询
 * {@link https://manyun.yuque.com/ewe5b3/pkinbk/fgawab#FX0UA API 文档}
 */
export const fetchAvailableTemplate = () =>
  callApi({
    endpoint: 'dcom/change/template/available/query',
    options: {
      method: 'POST',
    },
  });

/**
 * 保存变更
 * {@link https://manyun.yuque.com/ewe5b3/pkinbk/fgawab#aof4D API 文档}
 * @param {BasicInfo} data
 * @param {string} data.idcTag 机房
 * @param {string} data.blockTag 楼栋  机房.楼栋
 * @param {string} data.exeUserGroupCode 执行用户组code
 * @param {string} data.planStartTime 计划开始时间
 * @param {string} data.planEndTime 计划结束时间
 * @param {string} data.exeUserGroupName 执行用户组name
 * @param {StepList[]} data.stepList 步骤列表
 * @param {CheckDeviceInfoList[]} data.stepList.checkDeviceInfoList
 * @param {MatchObjectInfoList[]} data.stepList.matchObjectInfoList
 * @param {deviceInfoList[]} data.stepList.checkDeviceInfoList.deviceInfoList
 */
export const saveTicket = data =>
  callApi({
    endpoint: 'dcom/change/order/save',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 提交变更
 * {@link http://121.40.151.185:8085/swagger-ui.html#/%E5%8F%98%E6%9B%B4%E5%B7%A5%E5%8D%95%E6%8E%A7%E5%88%B6%E5%99%A8/summerySubmitUsingPOST API 文档}
 * @param {BasicInfo} data
 * @param {string} data.idcTag 机房
 * @param {string} data.blockTag 楼栋  机房.楼栋
 * @param {string} data.exeUserGroupCode 执行用户组code
 * @param {string} data.planStartTime 计划开始时间
 * @param {string} data.planEndTime 计划结束时间
 * @param {string} data.exeUserGroupName 执行用户组name
 * @param {StepList[]} data.stepList 步骤列表
 * @param {CheckDeviceInfoList[]} data.stepList.checkDeviceInfoList
 * @param {MatchObjectInfoList[]} data.stepList.matchObjectInfoList
 * @param {deviceInfoList[]} data.stepList.checkDeviceInfoList.deviceInfoList
 */
export const submitTicket = data =>
  callApi({
    endpoint: 'dcom/change/order/submit',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 变更详情
 * {@link https://manyun.yuque.com/ewe5b3/pkinbk/fgawab#4w8Cc API 文档}
 * @param {object} data
 * @param {string} data.changeOrderId
 */
export const ticketDetail = changeOrderId =>
  callApi({
    endpoint: 'dcom/change/order/detail',
    options: {
      method: 'POST',
      data: { changeOrderId },
    },
  });

/**
 * 变更复制
 * {@link https://manyun.yuque.com/ewe5b3/pkinbk/fgawab#4w8Cc API 文档}
 * @param {object} data
 * @param {string} data.changeOrderId
 */
export const ticketCopyDetail = changeOrderId =>
  callApi({
    endpoint: 'dcom/change/order/copy/query',
    options: {
      method: 'POST',
      data: { changeOrderId },
    },
  });

/**
 * 查询变更告警级别数量
 * {@link https://manyun.yuque.com/ewe5b3/pkinbk/coba8b API 文档}
 * @param {object} data
 * @param {string} data.blockTag
 * @param {string} data.changeId
 * @param {string} data.idcTag
 */
export const ticketAlarmLevelCount = data =>
  callApi({
    endpoint: 'dcim/alarm/query/alarm/level/count',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 查询变更步骤详情
 * {@link https://manyun.yuque.com/ewe5b3/pkinbk/fgawab#VTcOB API 文档}
 * @param {object} data
 * @param {string} data.changeOrderId
 *
 */
export const ticketStepDetail = changeOrderId =>
  callApi({
    endpoint: 'dcom/change/step/detail',
    options: {
      method: 'POST',
      data: { changeOrderId },
    },
  });

/**
 * 开始步骤
 * {@link https://manyun.yuque.com/ewe5b3/pkinbk/fgawab#a2Vz5 API 文档}
 * @param {object} data
 * @param {string} data.changeOrderId
 * @param {string} data.stepId 步骤id
 * @param {string} data.stepOrder
 *
 */
export const startOperation = data =>
  callApi({
    endpoint: 'dcom/change/step/start',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 步骤检查
 * {@link https://manyun.yuque.com/ewe5b3/pkinbk/fgawab#a2Vz5 API 文档}
 * @param {object} data
 * @param {string} data.changeOrderId
 * @param {string} data.stepId 步骤id
 */
export const stepChecked = data =>
  callApi({
    endpoint: 'dcom/change/step/check',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 步骤强行跳过
 * {@link https://manyun.yuque.com/ewe5b3/pkinbk/fgawab#a2Vz5 API 文档}
 * @param {object} data
 * @param {string} data.changeOrderId
 * @param {string} data.stepId 步骤id
 * @param {string} data.stepOrder
 * @param {string} data.jumpReason
 */
export const stepSkip = data =>
  callApi({
    endpoint: 'dcom/change/step/jump',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 步骤结束
 * {@link https://manyun.yuque.com/ewe5b3/pkinbk/fgawab#a2Vz5 API 文档}
 * @param {object} data
 * @param {string} data.changeOrderId
 * @param {string} data.stepId 步骤id
 * @param {string} data.stepOrder
 */
export const stepStop = data =>
  callApi({
    endpoint: 'dcom/change/step/finish',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 检查项人工验证
 * {@link https://manyun.yuque.com/ewe5b3/pkinbk/fgawab#a2Vz5 API 文档}
 * @param {object} data
 * @param {string} data.changeOrderId
 * @param {string} data.checkItemId 步骤id
 * @param {string} data.itemStatus
 */
export const checkItemArtificialValidation = data =>
  callApi({
    endpoint: 'dcom/change/step/item/person/check',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 操作项人工验证
 * {@link https://manyun.yuque.com/ewe5b3/pkinbk/fgawab#a2Vz5 API 文档}
 * @param {object} data
 * @param {string} data.changeOrderId
 * @param {string} data.opItemId 步骤id
 * @param {string} data.opResult
 */
export const opArtificialValidation = data =>
  callApi({
    endpoint: 'dcom/change/step/item/op',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 变更工单审批撤回
 * {@link https://manyun.yuque.com/ewe5b3/pkinbk/fgawab#a2Vz5 API 文档}
 * @param {object} data
 * @param {string} data.changeOrderId
 */
export const ticketApprovalOrRevert = data =>
  callApi({
    endpoint: 'dcom/change/order/approval/revert',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 审批
 * {@link https://manyun.yuque.com/ewe5b3/pkinbk/fgawab#a2Vz5 API 文档}
 * @param {object} data
 * @param {'CHANGE_ORDER'|'CHANGE_TEMPLATE'} data.approvalBusinessType
 * @param {string} data.approvalResult
 * @param {string} data.processInstanceId
 */
export const approval = data =>
  callApi({
    endpoint: 'dcom/approval',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 总结提交
 * {@link http://121.40.151.185:8085/swagger-ui.html#/%E5%8F%98%E6%9B%B4%E5%B7%A5%E5%8D%95%E6%8E%A7%E5%88%B6%E5%99%A8/summerySubmitUsingPOST API 文档}
 * @param {object} data
 * @param {string} data.changeOrderId
 * @param {array} data.fileInfoList
 * @param {string} data.summery
 */
export const summerySubmit = data =>
  callApi({
    endpoint: 'dcom/change/order/summery/submit',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 总结查询
 * {@link http://121.40.151.185:8085/swagger-ui.html#/%E5%8F%98%E6%9B%B4%E5%B7%A5%E5%8D%95%E6%8E%A7%E5%88%B6%E5%99%A8/summerySubmitUsingPOST API 文档}
 * @param {object} data
 * @param {string} data.changeOrderId
 */
export const getSummeryInfo = data =>
  callApi({
    endpoint: 'dcom/change/order/summery/query',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 变更终止
 * {@link http://121.40.151.185:8085/swagger-ui.html#/%E5%8F%98%E6%9B%B4%E5%B7%A5%E5%8D%95%E6%8E%A7%E5%88%B6%E5%99%A8/summerySubmitUsingPOST API 文档}
 * @param {object} data
 * @param {string} data.changeOrderId
 * @param {string} data.stopReason
 */
export const stopChange = data =>
  callApi({
    endpoint: 'dcom/change/order/force/stop',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 撤回变更模板审批单
 * {@link http://121.40.151.185:8085/swagger-ui.html#/ API 文档}
 * @param {object} data
 * @param {string} data.templateId
 *
 */
export const templateApprovalOrRevert = data =>
  callApi({
    endpoint: 'dcom/change/template/approval/revoke',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 关闭变更单
 * {@link http://121.40.151.185:8085/swagger-ui.html#/%E5%8F%98%E6%9B%B4%E5%B7%A5%E5%8D%95%E6%8E%A7%E5%88%B6%E5%99%A8/closeUsingPOST API 文档}
 * @param {object} data
 * @param {string} data.changeOrderId
 */
export const ticketClose = data =>
  callApi({
    endpoint: 'dcom/change/order/close',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 变更模板删除
 * @param {object} data
 * @param {string} data.templateId
 */
export const deleteTemplate = data =>
  callApi({
    endpoint: 'dcom/change/template/delete',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 查询机房下的变更单
 * @param {object} data
 * @param {string} data.idcTag
 * @param {import('@manyun/dc-brain.legacy.pages/change/constants/index').CHANGE_TICKET_STATUS_KEY_MAP[]} data.statusList
 */
export const fetchIdcChangeTickets = data =>
  callApi({
    endpoint: 'dcom/change/order/in/idc',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 查询设备关联的变更单
 * @param {*} data
 * @param {string} data.idcTag
 * @param {string} [data.blockTag] 需要传递 blockGuid
 * @param {string} data.deviceGuid 目标设备
 * @param {number} data.pageNum
 * @param {number} data.pageSize
 */
export const fetchDeviceChangeTickets = data =>
  callApi({
    endpoint: 'dcom/change/order/device/query',
    options: {
      method: 'POST',
      data,
    },
  });
