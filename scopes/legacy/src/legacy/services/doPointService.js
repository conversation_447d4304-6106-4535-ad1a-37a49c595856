import { callApi } from './api';

// 根据code 获取测点配置
export const fetchDispositionPoint = ({ deviceType }) =>
  callApi({
    endpoint: 'dccm/point/query',
    options: {
      method: 'GET',
      params: { deviceType },
    },
  });

// 添加测点
export const fetchAddPoint = ({
  deviceType,
  pointType,
  dataType,
  name,
  extCount,
  extName,
  unit,
  code,
  precision,
  priority,
  description,
  status,
  alarmType,
  alarmLevel,
  formula,
  dimension,
  digitalNum,
  // digitalDesc,
  validLimits,
}) =>
  callApi({
    endpoint: 'dccm/point/add',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data: {
        deviceType,
        pointType,
        dataType,
        name,
        code,
        extCount,
        extName,
        unit,
        precision,
        priority,
        description,
        status,
        alarmType,
        alarmLevel,
        formula,
        dimension,
        digitalNum,
        // digitalDesc,
        validLimits,
      },
    },
  });

/***
 * 删除点位
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/kag8e7 API 文档}
 * @param {object} data
 * @param {string} data.code  点位编码
 * @param {string} data.deviceType 设备类型
 */
export const deletePoint = params =>
  callApi({
    endpoint: 'dccm/point/delete',
    options: { params },
  });

// 测点类型
export const fetchGetPointType = async () => {
  const { response, error } = await callApi({
    endpoint: 'dccm/get/point/type',
    options: {
      method: 'GET',
      params: {},
    },
  });
  if (error) {
    return;
  }
  const newList = [];
  if (response) {
    Object.keys(response).map(key => {
      newList.push({ key: key, value: response[key] });

      return newList;
    });
  }
  return newList;
};

// 测点数据类型
export const fetchGetPointDataType = async () => {
  const { response, error } = await callApi({
    endpoint: 'dccm/get/point/data/type',
    options: {
      method: 'GET',
      params: {},
    },
  });
  if (error) {
    return;
  }
  const newList = [];
  if (response) {
    Object.keys(response).map(key => {
      newList.push({ key: key, value: response[key] });

      return newList;
    });
  }
  return newList;
};

/**
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/do4ag1 API 文档}
 * @return {Promise<{ response?: { [deviceType: string]: number }; error?: string }>}
 */
export const fetchDeviceTypePointCountMap = () =>
  callApi({ endpoint: 'dccm/point/get/type/number' });

// 告警类型
export const fetchAlarmType = async () => {
  const { response, error } = await callApi({
    endpoint: 'dccm/get/alarm/type',
    options: {
      method: 'GET',
      params: {},
    },
  });
  if (error) {
    return;
  }
  const newList = [];
  if (response) {
    Object.keys(response).map(key => {
      newList.push({ key: key, value: response[key] });

      return newList;
    });
  }
  return newList;
};

// 聚合方式
export const fetchDimensionType = async () => {
  const { response, error } = await callApi({
    endpoint: 'dccm/get/dimension/type',
    options: {
      method: 'GET',
      params: {},
    },
  });
  if (error) {
    return;
  }
  const newList = [];
  if (response) {
    Object.keys(response).map(key => {
      newList.push({ key: key, value: response[key] });

      return newList;
    });
  }
  return newList;
};

export const updatePoint = data =>
  callApi({
    endpoint: 'dccm/point/update',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * @deprecated 需要重构到 dccmService 里去
 */
export const fetchMetaCategory = () =>
  callApi({
    endpoint: 'dccm/meta/query/device/category',
    options: {
      method: 'POST',
      data: {
        numbered: true,
      },
    },
  });
