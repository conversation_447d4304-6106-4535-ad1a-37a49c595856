import { callApi } from './api';

export const fetchValidNoticeList = restParams => {
  return callApi({
    endpoint: 'taskcenter/base/page',
    options: {
      method: 'POST',
      data: restParams,
    },
  });
};

/**
 * @deprecated Use `@manyun/ticket.service.fetch-paged-visitors` instead
 * @param {*} params
 * @returns
 */
export const fetchVisitorList = params => {
  return callApi({
    endpoint: 'taskcenter/visitor/task/list',
    options: {
      method: 'POST',
      data: { ...params, blockGuidList: params?.blockGuidList?.blocks ?? [] },
    },
  });
};

export const takeOverTicket = data => {
  return callApi({
    endpoint: 'taskcenter/base/take',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data,
    },
  });
};

export const ticketSingleEndOf = data => {
  return callApi({
    endpoint: 'taskcenter/base/single/end',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 工单详情基本信息
 * {@link https://manyun.yuque.com/luc1u2/ilvfpi/gi0apm#NhYUr API 文档}
 * @param {object} data
 * @param {number} data.taskNo
 */
export const fetchTicketBasicInfo = data => {
  return callApi({
    endpoint: 'taskcenter/base/info',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 工单转交
 * {@link https://manyun.yuque.com/luc1u2/ilvfpi/gi0apm#NhYUr API 文档}
 * @param {object} data
 * @param {number} data.taskNo
 */
export const ticketTransfer = data => {
  return callApi({
    endpoint: 'taskcenter/base/assign',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 工单详情基本信息
 * {@link https://manyun.yuque.com/luc1u2/ilvfpi/gi0apm#NhYUr API 文档}
 * @param {object} data
 * @param {number} data.taskNo
 */
export const fetchTicketPatrolRoomInfo = data => {
  return callApi({
    endpoint: 'taskcenter/inspect/order/check/subject/query',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 巡检对象分组查询
 * {@link https://manyun.yuque.com/luc1u2/ilvfpi/utok8o#Xp30t API 文档}
 * @param {object} data
 * @param {number} data.taskNo
 * @param {number} data.roomGuid
 */
export const fetchTicketPatrolCheckSubjectGroup = data => {
  return callApi({
    endpoint: 'taskcenter/inspect/order/check/subject/group/query',
    options: {
      method: 'POST',
      data,
    },
  });
};
// Promise.resolve({
//   response: {
//     total: 2,
//     data: [
//       {
//         roomTag: 'A1-02',
//         roomGuid: 'EC01.A.A1-02',
//         roomType: 'DEVICE_ROOM',
//         exceptionNum: 0,
//         waitInspectNum: 4,
//         alreadyInspectNum: 0,
//         inspectTime: 0,
//       },
//       {
//         roomTag: 'A1-24',
//         roomGuid: 'EC01.A.A1-24',
//         roomType: 'DEVICE_ROOM',
//         exceptionNum: 0,
//         waitInspectNum: 6,
//         alreadyInspectNum: 0,
//         inspectTime: 0,
//       },
//       {
//         roomTag: 'A1-02',
//         roomGuid: 'EC01.A.A1-02',
//         roomType: 'DEVICE_ROOM',
//         exceptionNum: 0,
//         waitInspectNum: 4,
//         alreadyInspectNum: 0,
//         inspectTime: 0,
//       },
//     ],
//   },
// });

/**
 * 房间视图检查项查询
 * {@link https://manyun.yuque.com/luc1u2/ilvfpi/utok8o#Hmiz0 API 文档}
 * @param {object} data
 * @param {string} data.roomGuid
 */
export const patrolOrderCheckItem = data => {
  return callApi({
    endpoint: 'taskcenter/inspect/order/check/item/query',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 失败原因类型查询
 * {@link https://manyun.yuque.com/luc1u2/ilvfpi/gi0apm#uNkWY API 文档}
 */
export const fetchFailType = () => {
  return callApi({
    endpoint: 'taskcenter/base/fail/type',
  });
};

/**
 * 超时原因类型查询
 * {@link https://manyun.yuque.com/luc1u2/ilvfpi/gi0apm#uNkWY API 文档}
 */
export const fetchTimeoutType = () => {
  return callApi({
    endpoint: 'taskcenter/base/delay/type',
  });
};

/**
 * 测点巡检项保存
 * {@link https://manyun.yuque.com/luc1u2/ilvfpi/utok8o#iA6VC API 文档}
 */
export const patrolCheckItemPointResultSave = data => {
  return callApi({
    endpoint: 'taskcenter/inspect/check/item/sys/point/result/save',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 巡检项正常结果保存
 * {@link https://manyun.yuque.com/luc1u2/ilvfpi/utok8o#0xChi API 文档}
 */
export const patrolCheckItemNormalSave = data => {
  return callApi({
    endpoint: 'taskcenter/inspect/check/item/normal/result/save',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 检查项异常结果处理创建工单
 * {@link https://manyun.yuque.com/luc1u2/ilvfpi/utok8o#iA6VC API 文档}
 */
export const patrolCheckItemExceptionHandling = data => {
  return callApi({
    endpoint: 'taskcenter/inspect/check/item/exception/result/handle',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 自定义抄表巡检项保存
 * {@link http://172.16.0.17:13000/project/65/interface/api/18104 API 文档}
 */
export const patrolCheckItemCustomizeResultSave = data => {
  return callApi({
    endpoint: 'taskcenter/inspect/check/item/customize/result/save',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 巡检单创建
 * {@link https://manyun.yuque.com/luc1u2/ilvfpi/utok8o#3ciOQ API 文档}
 */
export const patrolInspectOrderCreate = data => {
  return callApi({
    endpoint: 'taskcenter/inspect/order/create',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 机柜上下电机柜导入
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/cc1piv#miXMJ API 文档}
 */ export const powerGridImport = file =>
  callApi({
    endpoint: 'taskcenter/power/grid/import',
    options: {
      method: 'POST',
      contentType: 'multipart/form-data',
      data: file,
      timeout: 5 * 60 * 1000,
    },
  });

/**
 * 机柜上下电机柜导入- by 机柜别名
 * {@link http://yapi.manyun-local.com/project/118/interface/api/28031 API 文档}
 */ export const powerGridImportByAlias = file =>
  callApi({
    endpoint: 'taskcenter/power/grid/alias/import',
    options: {
      method: 'POST',
      contentType: 'multipart/form-data',
      data: file,
      timeout: 5 * 60 * 1000,
    },
  });

// 机柜上下电下载机柜模板
export const downloadGridModel = () =>
  callApi({
    endpoint: 'taskcenter/power/grid/download',
    options: {
      method: 'GET',
      params: {},
      responseType: 'blob',
    },
  });

// 机柜上下电下载机柜模板 by 机柜别名
export const downloadGridModelByGridAlias = () =>
  callApi({
    endpoint: 'taskcenter/power/grid/alias/download',
    options: {
      method: 'GET',
      params: {},
      responseType: 'blob',
    },
  });

/**
 *
 * 统计包间机柜处理数量
 * @deprecated use ticket/service/fetch-power-grid-count
 * @param {*} data
 * @returns
 */
export const powerGridCount = data =>
  callApi({
    endpoint: 'taskcenter/power/grid/count',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 查询工单下的机柜
 *  @deprecated use ticket/service/fetch-paged-power-grid
 * @param {*} data
 * @returns
 */
export const powerGridList = data =>
  callApi({
    endpoint: 'taskcenter/power/grid/query',
    options: {
      method: 'POST',
      data,
    },
  });

// 检查机柜完成
export const powerGridComplete = data =>
  callApi({
    endpoint: 'taskcenter/power/grid/complete',
    options: {
      method: 'POST',
      data,
    },
  });

// 机柜上下电创建
export const powerGridCreate = data =>
  callApi({
    endpoint: 'taskcenter/power/task/create',
    options: {
      method: 'POST',
      data,
    },
  });

export const transferUserSelectList = data =>
  callApi({
    endpoint: 'taskcenter/base/list/assignee',
    options: {
      method: 'POST',
      data,
    },
  });

export const fetchTicketMaintenanceRoomInfo = data =>
  callApi({
    endpoint: 'taskcenter/maintenance/task/detail/query',
    options: {
      method: 'POST',
      data,
    },
  });

export const maintenanceOrderCheckItem = data =>
  callApi({
    endpoint: 'taskcenter/maintenance/task/item/detail/query',
    options: {
      method: 'POST',
      data,
    },
  });

export const maintenanceItemExceptionResultHandle = data =>
  callApi({
    endpoint: 'taskcenter/maintenance/task/item/exception/result/handle',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 维保维护结果
 * {@link https://manyun.yuque.com/luc1u2/ilvfpi/tgz9dr#clD7d API 文档}
 */
export const maintenanceCheckItemNormalSave = data => {
  return callApi({
    endpoint: 'taskcenter/maintenance/task/item/result/save',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 维保新建
 * {@link https://manyun.yuque.com/luc1u2/ilvfpi/tgz9dr#2oNdb API 文档}
 */
export const maintenanceCreate = data => {
  return callApi({
    endpoint: 'taskcenter/maintenance/task/create',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 人员入室申请导入
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/cc1piv#miXMJ API 文档}
 */

export const visitorStaffImport = formdata =>
  callApi({
    endpoint: 'taskcenter/visitor/staff/import',
    options: {
      method: 'POST',
      contentType: 'multipart/form-data',
      data: formdata,
      timeout: 5 * 60 * 1000,
    },
  });

// 下载人员入室申请模板
export const downloadVisitorModel = () =>
  callApi({
    endpoint: 'taskcenter/visitor/staff/download',
    options: {
      method: 'GET',
      params: {},
      responseType: 'blob',
    },
  });

/**
 * @deprecated Use `@manyun/sentry.service.fetch-visitor-types` instead
 *
 * 人员类型查询
 * {@link https://manyun.yuque.com/luc1u2/ilvfpi/gi0apm#uNkWY API 文档}
 */
export const fetchVisitorType = () => {
  return callApi({
    endpoint: 'taskcenter/visitor/staff/type',
  });
};

/**
 * 证件类型查询
 * {@link https://manyun.yuque.com/luc1u2/ilvfpi/gi0apm#uNkWY API 文档}
 */
export const fetchCertificateType = () => {
  return callApi({
    endpoint: 'taskcenter/visitor/certificate/type',
  });
};

/**
 * 工单大类查询
 * {@link https://manyun.yuque.com/luc1u2/ilvfpi/gi0apm#uNkWY API 文档}
 */
export const fetchTaskType = () => {
  return callApi({
    endpoint: 'taskcenter/base/task/type',
  });
};

/**
 * 查询被关联工单
 * @param {object} data
 * @param {string} data.relateTaskNo 被关联工单编号
 * @param {Boolean} data.taskType 查询工单类型
 * @param {*} data
 */
export const fetchRelateTask = data => {
  return callApi({
    endpoint: 'taskcenter/base/list/relate',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data,
    },
  });
};

export const singleTakeOverTicket = data => {
  return callApi({
    endpoint: 'taskcenter/base/single/take',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 资产出入门列表
 */
export const fetchAccessList = params => {
  return callApi({
    endpoint: 'taskcenter/access/page',
    options: {
      method: 'POST',
      data: params,
    },
  });
};

/**
 * 门禁卡授权创建
 */
export const accessCardCreate = params => {
  return callApi({
    endpoint: 'taskcenter/general/access_card/create',
    options: {
      method: 'POST',
      data: params,
    },
  });
};

/**
 * 撤回上下电申请工单
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/cc1piv#bRG0L API 文档}
 * @param {object} data
 * @param {string} data.taskNo 工单号
 */
export const revokePowerApprove = data =>
  callApi({
    endpoint: 'taskcenter/power/approve/revoke',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 上下电重新发送机柜列表
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/cc1piv#SNnIn API 文档}
 * @param {object} data
 * @param {string} data.taskNo 工单号
 */
export const powerResetGridList = data =>
  callApi({
    endpoint: 'taskcenter/power/tem/grid/query',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 上下电重新发送工单
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/cc1piv#qyOCS API 文档}
 * @param {object} data
 * @param {string} data.taskNo 工单号
 */
export const powerUpdate = data =>
  callApi({
    endpoint: 'taskcenter/power/task/update',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 门禁卡撤销审批
 * {@link https://manyun.yuque.com/luc1u2/ilvfpi/xvtsho#iZSaD API 文档}
 * @param {object} data
 * @param {string} data.taskNo 工单号
 */
export const accessCardRecoke = data =>
  callApi({
    endpoint: 'taskcenter/general/access_card/revoke',
    options: {
      method: 'POST',
      data,
    },
  });
