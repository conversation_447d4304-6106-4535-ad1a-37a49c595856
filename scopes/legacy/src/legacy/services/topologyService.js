import { callApi } from './api';

/**
 * @typedef {'ELECTRIC_POWER' | 'HVAC' | 'ROOM_FACILITY'} TopologyType
 *
 * @typedef Node
 * @property {string} guid 设备 guid
 * @property {string} deviceType 设备类型
 * @property {string} tag 设备名称
 * @property {boolean} isVirtual 是否是虚拟节点
 * @property {string} spaceGuid roomGuid || blockGuid || idcGuid
 * @property {string} extendPosition 扩展位置
 * @property {string} pointCode 通电状态点位编码
 * @property {string} statusValue 通电状态点位的值
 *
 * @typedef BusTieCabietNode 联络柜节点
 * @property {string} guid1 联络柜2端设备中的一个 guid
 * @property {string} guid2 联络柜2端设备中的一个 guid
 * @property {string} pointCode 联通状态点位编码
 * @property {string} statusValue 联通状态点位的值
 * @property {string} spaceGuid 联络柜空间 guid
 * @property {string} deviceGuid 联络柜 guid
 * @property {string} deviceType 联络柜 设备类型
 *
 * @typedef Edge
 * @property {string} sourceGuid 源设备 guid
 * @property {string} targetGuid 目标设备 guid
 *
 * @typedef TopologyJson
 * @property {Node[]} nodeList
 * @property {BusTieCabietNode[]} relateList
 * @property {Edge[]} flowList
 */

/**
 * @typedef Condition
 * @property {'UP' | 'DOWN'} direction 方向
 * @property {number} level 查询层级
 *
 * 查询设备拓扑结构
 * {@link https://manyun.yuque.com/ewe5b3/nyd0m7/pcum5n#gxhoE API 文档}
 * @param {object} data
 * @param {string} data.blockGuid 楼
 * @param {string} data.deviceGuid 设备唯一码
 * @param {boolean} data.checkStatus 是否查询点位联通状态
 * @param {Condition[]} data.conditionList 查询条件集合
 * @param {TopologyType} data.topologyType 拓扑图类型
 * @param {boolean} [data.containsCurrentDevice=false] 返回值是否包含当前设备
 */
export const fetchTopologyStructure = data =>
  callApi({
    endpoint: 'dccm/topology/structure/query',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 保存设备拓扑结构
 * {@link https://manyun.yuque.com/ewe5b3/nyd0m7/pcum5n#jGr1T API 文档}
 * @param {object} data
 * @param {string} data.viewJson
 * @param {TopologyJson} data.topologyJson
 * @param {string[]} data.blocks 楼栋
 * @param {TopologyType} data.topologyType
 * @deprecated Use `@manyun/resource-hub.service.create-topology` instead.
 *
 */
export const saveTopology = ({ topologyJson, ...rest }) =>
  callApi({
    endpoint: 'dccm/topology/save',
    options: {
      method: 'POST',
      data: {
        ...rest,
        topologyJson: topologyJson ? JSON.stringify(topologyJson) : null,
      },
    },
  });

/**
 * 获取设备拓扑结构
 * {@link https://manyun.yuque.com/ewe5b3/nyd0m7/pcum5n#9yIR4 API 文档}
 *
 * @deprecated Use `@manyun/monitoring.service.fetch-topology` instead.
 *
 * @param {object} data
 * @param {string} data.blockGuid
 * @param {TopologyType} data.topologyType
 */
export const fetchTopology = data =>
  callApi({
    endpoint: 'dccm/topology/view/query',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 更新拓扑
 * @param {object} data
 * @param {string} data.id
 * @param {string} data.viewJson
 * @param {TopologyJson} data.topologyJson
 * @param {string[]} data.blocks
 * @deprecated Use `@manyun/resource-hub.service.update-topology` instead.
 */
export const updateTopology = ({ topologyJson, ...rest }) =>
  callApi({
    endpoint: 'dccm/topology/update',
    options: {
      method: 'POST',
      data: {
        ...rest,
        topologyJson: topologyJson ? JSON.stringify(topologyJson) : null,
      },
    },
  });

/**
 * 获取视图编辑器配置
 */
export const fetchTopologyEditorConfigs = () =>
  callApi({
    endpoint: 'topology/editor/configs',
  });
