import { message } from '@manyun/base-ui.ui.message';

import { callApi } from './api';

export const fetchCreateUserGroup = ({ groupName, groupCode, remarks }) =>
  callApi({
    endpoint: 'pm/group/add',
    options: {
      method: 'POST',
      data: `groupName=${encodeURIComponent(groupName)}&groupCode=${groupCode}&remarks=${
        remarks ? encodeURIComponent(remarks) : ''
      }`,
    },
  });

export const fetchUpdateUserGroup = ({ groupName, groupCode, remarks, groupId }) =>
  callApi({
    endpoint: 'pm/group/update',
    options: {
      method: 'POST',
      data: `groupName=${encodeURIComponent(groupName)}&groupCode=${groupCode}&remarks=${
        remarks ? encodeURIComponent(remarks) : ''
      }&groupId=${groupId}`,
    },
  });

export const fetchSelectUserGroup = async groupName => {
  const data = await callApi({
    endpoint: 'pm/group/list',
    options: {
      method: 'GET',
      params: {
        groupName,
      },
    },
  });
  let userGroupList = [];
  if (data.response) {
    userGroupList = data.response.data.map(item => {
      return { ...item, label: item.groupName, key: item.id };
    });
  }
  return userGroupList;
};

// 添加用户
export const fetchUserGroupAddUser = async (
  selectedSources,
  selectedTargets,
  id,
  fetchUserGroupsAssociatedUser,
  needFetchList,
  pageNo,
  pageSize,
  searchName,
  searchType
) => {
  const groupIds = selectedSources.map(item => item.key);
  const userIds = selectedTargets.map(item => item.id);
  const data = await callApi({
    endpoint: 'pm/user/bindGroups',
    options: {
      method: 'POST',
      data: `userIds=${userIds}&groupIds=${groupIds}`,
    },
  });
  if (data.response) {
    message.success('关联用户成功');
    needFetchList &&
      fetchUserGroupsAssociatedUser({
        pageSize,
        pageNo,
        searchType,
        groupId: id,
        searchName,
      });
    return data.response;
  } else {
    message.error(data.error || '关联用户失败');
  }
};

// 添加角色
export const fetchAssociatedRole = async (
  selectedSources,
  selectedTargets,
  id,
  fetchUserGroupsAssociatedRoles,
  needFetchList,
  pageNo,
  pageSize,
  searchName,
  searchType
) => {
  const groupIds = selectedSources.map(item => item.key);
  const roleIds = selectedTargets.map(item => item.id);
  const data = await callApi({
    endpoint: 'pm/group/bindRoles',
    options: {
      method: 'POST',
      data: `roleIds=${roleIds}&groupIds=${groupIds}`,
    },
  });
  if (data.response) {
    message.success('关联角色成功');
    needFetchList &&
      fetchUserGroupsAssociatedRoles({
        pageSize,
        pageNo,
        searchName,
        searchType,
        groupId: id,
      });
    return data.response;
  } else {
    message.error(data.error || '关联角色失败');
  }
};

// 添加资源
export const fetchUserGroupAddResource = async (
  selectedSources,
  selectedTargets,
  id,
  fetchUserGroupsAssociatedResource,
  needFetchList,
  pageNo,
  pageSize,
  searchName,
  searchType
) => {
  const groupIds = selectedSources.map(item => item.key);
  const data = await callApi({
    endpoint: 'pm/group/bindResources',
    options: {
      method: 'POST',
      data: `resourceIds=${selectedTargets}&groupIds=${groupIds}`,
    },
  });
  if (data.response) {
    message.success('关联资源成功');
    needFetchList &&
      fetchUserGroupsAssociatedResource({
        pageSize,
        pageNo,
        searchName,
        searchType,
        groupId: id,
      });
    return data.response;
  } else {
    message.error(data.error || '关联资源失败');
  }
};

export const fetchSelectUser = async searchName => {
  const data = await callApi({
    endpoint: 'pm/user/list',
    options: {
      method: 'GET',
      params: {
        searchName,
      },
    },
  });
  let userList = [];
  if (data.response) {
    userList = data.response.data.map(item => {
      return { ...item, key: item.id, label: item.userName };
    });
  }
  return userList;
};

export const fetchSelectRole = async roleName => {
  const data = await callApi({
    endpoint: 'pm/role/list',
    options: {
      method: 'GET',
      params: {
        roleName: roleName || '',
      },
    },
  });
  let roleList = [];
  if (data.response) {
    roleList = data.response.data.map(item => {
      return { ...item, key: item.id, label: item.roleName };
    });
  }
  return roleList;
};

export const fetchDelete = groupId =>
  callApi({
    endpoint: 'pm/group/delete',
    options: {
      method: 'POST',
      data: `groupId=${groupId}`,
    },
  });

export const fetchUserGroupDetail = groupId =>
  callApi({
    endpoint: 'pm/group/detail',
    options: {
      method: 'GET',
      params: {
        groupId,
      },
    },
  });

export const fetchUserGroupsAssociatedUser = (groupId, pageNo, pageSize, searchType, searchName) =>
  callApi({
    endpoint: 'pm/group/groupUserPage',
    options: {
      method: 'GET',
      params: {
        groupId,
        pageNo,
        pageSize,
        searchType,
        searchName,
      },
    },
  });

export const fetchUserGroupsAssociatedRoles = (groupId, pageNo, pageSize, searchType, searchName) =>
  callApi({
    endpoint: 'pm/group/groupRolePage',
    options: {
      method: 'GET',
      params: {
        groupId,
        pageNo,
        pageSize,
        searchType,
        searchName,
      },
    },
  });

// 移除用户
export const fetchRemoveUser = (userId, groupId) =>
  callApi({
    endpoint: 'pm/user/unbindGroup',
    options: {
      method: 'POST',
      data: `userId=${userId}&groupId=${groupId}`,
    },
  });

// 移除角色
export const fetchRemoveRole = (roleId, groupId) =>
  callApi({
    endpoint: 'pm/group/unbindRole',
    options: {
      method: 'POST',
      data: `roleId=${roleId}&groupId=${groupId}`,
    },
  });

// 移除资源
export const fetchRemoveResource = (resourceId, groupId) =>
  callApi({
    endpoint: 'pm/group/unbindResources',
    options: {
      method: 'POST',
      data: `resourceId=${resourceId}&groupId=${groupId}`,
    },
  });

export const fetchUserGroupsAssociatedResource = (
  groupId,
  pageNo,
  pageSize,
  searchType,
  searchName
) =>
  callApi({
    endpoint: 'pm/group/groupResourcePage',
    options: {
      method: 'GET',
      params: {
        groupId,
        pageNo,
        pageSize,
        searchType,
        searchName,
      },
    },
  });

// 资源树
export const fetchSelectResourceList = async () => {
  const data = await callApi({
    endpoint: 'pm/resource/tree',
    options: {
      method: 'GET',
    },
  });
  if (data.response) {
    return data.response;
  }
  return [];
};

/**
 * 根据角色编码列表和楼栋标签查询用户组
 * 接口文档：https://manyun.yuque.com/ewe5b3/es43gk/qx1kkc
 **/
export const fetchGroupsByResourceCodeAndRoleCodes = data =>
  callApi({
    endpoint: 'pm/group/groupsByResourceCodeAndRoleCodes',
    options: {
      method: 'POST',
      data,
    },
  });
