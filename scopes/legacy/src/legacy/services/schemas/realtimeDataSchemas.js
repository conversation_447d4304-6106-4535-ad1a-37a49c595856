import { schema } from 'normalizr';

export const pointsDataByDeviceGuidSchema = [
  new schema.Entity(
    'pointsData',
    {},
    {
      idAttribute: 'deviceGuid',
      processStrategy: ({ deviceType, pointCode, dataValue }) => ({
        deviceType,
        pointValueMap: {
          [pointCode]: {
            value: dataValue,
          },
        },
      }),
      mergeStrategy: (
        { deviceType, pointValueMap: pointValueMapA },
        { pointValueMap: pointValueMapB }
      ) => ({
        deviceType,
        pointValueMap: {
          ...pointValueMapA,
          ...pointValueMapB,
        },
      }),
    }
  ),
];

export const pointsDataByDeviceTypeSchema = [
  new schema.Entity(
    'pointsData',
    {},
    {
      idAttribute: 'deviceType',
      processStrategy: ({ pointCode, dataValue }) => ({
        [pointCode]: {
          value: dataValue,
        },
      }),
    }
  ),
];
