import { schema } from 'normalizr';

import { childNodeDataFormatter } from '@manyun/dc-brain.legacy.pages/monitor-item-config/utils';

const pointData = new schema.Entity(
  'pointsData',
  {},
  {
    idAttribute: 'triggerGuid',
    processStrategy: ({ alarmType, pointCode }) => ({ [pointCode]: { alarmType } }),
    mergeStrategy: (entityA, entityB) => ({ ...entityA, ...entityB }),
  }
);
export const alarmsSchema = [pointData];

export const childMonitorItemConfigSchema = [
  new schema.Entity(
    'configs',
    {},
    {
      idAttribute: 'parentCode',
      processStrategy: config => [childNodeDataFormatter(config, config.parentCode)],
      mergeStrategy: (a, b) => [...a, ...b],
    }
  ),
];
