import { callApi } from './api';

// 查询包间信息接口
export const fetchRoomPage = data =>
  callApi({
    endpoint: 'dccm/room/query',
    options: {
      method: 'POST',
      data,
    },
  });

// 查询查询机房编号和名称
export const fetchRomeCodeAndName = async () => {
  const data = await callApi({
    endpoint: 'dccm/meta/query/idc/list',
    options: {
      method: 'GET',
      contentType: 'application/json',
      data: {},
    },
  });
  const buildGroupList = data.response.data.map(item => {
    return { key: item.metaCode, label: item.metaName };
  });
  return buildGroupList;
};

/**
 * 提交新建包间表单
 * @param {object} data
 * @param {string} data.operationStatus
 * @param {string} data.operationTime
 * @param {string} data.roomType
 * @param {string} data.refrigerationStructure
 * @param {string} data.operatorNotes
 * @param {string} data.name
 */
export const fetchCreateNewRoomConfirm = data =>
  callApi({
    endpoint: 'dccm/room/add',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data,
    },
  });

// 查询状态
export const fetchOperationStatus = async () => {
  const data = await callApi({
    endpoint: 'dccm/get/operation/status',
    // endpoint: 'dccm/get/device/status',
    options: {
      method: 'GET',
      contentType: 'application/json',
    },
  });
  const status = data.response;
  const list = [];
  for (const i in status) {
    list.push({ value: status[i], key: i });
  }
  return list;
};

/**
 * 更新包间信息
 * @param {object} data
 * @param {number} data.id
 * @param {string} data.operationStatus
 * @param {string} data.operationTime
 * @param {string} data.roomType
 * @param {string} data.refrigerationStructure
 * @param {string} data.operatorNotes
 */
export const updateRoom = data =>
  callApi({
    endpoint: 'dccm/room/update',
    options: {
      method: 'POST',
      data,
    },
  });

// 导出列表
export const downloadRoomList = params =>
  callApi({
    endpoint: 'dccm/room/export',
    options: {
      method: 'POST',
      responseType: 'blob',
      contentType: 'application/json',
      data: params,
    },
  });

// 包间类型
export const fetchRoomType = async () => {
  const { response, error } = await callApi({
    endpoint: 'dccm/get/room/type',
    options: {
      method: 'GET',
      contentType: 'application/json',
      data: {},
    },
  });
  let newList = [];
  if (error) {
    return;
  }
  if (response) {
    Object.keys(response).map(key => {
      newList.push({ key: key, value: response[key] });

      return newList;
    });
    return newList;
  }
  return newList;
};

// 根据设备查询包间信息接口
export const fetchRoomByDeviceType = data =>
  callApi({
    endpoint: 'dccm/view/count/device/room',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data,
    },
  });
