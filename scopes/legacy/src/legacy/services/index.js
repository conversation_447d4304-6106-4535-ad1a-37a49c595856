import * as alarmConfigService from './alarmConfigService';
import * as alarmDataService from './alarmDataService';
import * as alarmScreenService from './alarmScreenService';
import * as alarmService from './alarmService';
import * as approvalConfigService from './approvalConfigService';
import * as approveCenterService from './approveCenterService';
import * as basicResourcesBuildingService from './basicResourcesBuildingService';
import * as basicResourcesIdcService from './basicResourcesIdcService';
import * as borrowsAndReturnService from './borrowsAndReturnService';
import * as cabinetManageService from './cabinetService';
import * as changeService from './changeService';
import * as channelConfigService from './channelConfigService';
import * as commonService from './commonService';
import * as dcomService from './dcomService';
import * as deviceMonitoringService from './deviceMonitoringService';
import * as deviceService from './deviceService';
import * as deviceTypeService from './deviceTypeService';
import * as doPointService from './doPointService';
import * as equipmentManageService from './equipmentService';
import * as eventCenterService from './eventCenterService';
import * as idcWorkbenchServices from './idcWorkbenchService';
import * as influenceService from './influenceService';
import * as infrastructureService from './infrastructureService';
import * as insideNoticeService from './insideNoticeService';
import * as logService from './logService';
import * as mergedMonitorConfigService from './mergedMonitorConfigService';
import * as mergedProcessesdPointService from './mergedProcessesdPointService';
import * as metadataConfigurationService from './metadataConfigurationService';
import * as modelService from './modelService';
import * as northUserService from './northUserService';
import * as noticeService from './noticeService';
import * as opsService from './opsService';
import * as pmService from './pmService';
import * as powerManageService from './powerService';
import * as realtimeDataService from './realtimeDataService';
import * as repoService from './repoService';
import * as roleManageService from './roleService';
import * as roomMonitoringService from './roomMonitoringService';
import * as roomManageService from './roomService';
import * as serverNodeService from './serverNodeService';
import * as spareService from './spareService';
import * as specService from './specService';
import * as staffShiftService from './staffShiftService';
import * as taskCenterService from './taskCenterService';
import * as templateGroupViewService from './templateGroupViewService';
import * as ticketService from './ticketService';
import * as topologyService from './topologyService';
import * as treeDataService from './treeDataService';
import * as userGroupManageService from './userGroupManageService';
import * as userManageService from './userManageService';
import * as userService from './userService';
import * as vendorService from './vendorService';
import * as versionManageService from './versionManageService';
import * as visitorManagerService from './visitorManagerService';
import * as warrantyOrderService from './warrantyOrderService';
import * as workdayManageService from './workdayManageService';

export {
  userService,
  repoService,
  userManageService,
  roleManageService,
  powerManageService,
  userGroupManageService,
  roomManageService,
  cabinetManageService,
  equipmentManageService,
  basicResourcesIdcService,
  basicResourcesBuildingService,
  doPointService,
  idcWorkbenchServices,
  alarmService,
  alarmScreenService,
  roomMonitoringService,
  alarmDataService,
  realtimeDataService,
  deviceMonitoringService,
  templateGroupViewService,
  alarmConfigService,
  treeDataService,
  topologyService,
  deviceService,
  mergedMonitorConfigService,
  commonService,
  eventCenterService,
  infrastructureService,
  metadataConfigurationService,
  changeService,
  mergedProcessesdPointService,
  staffShiftService,
  noticeService,
  logService,
  vendorService,
  specService,
  deviceTypeService,
  modelService,
  influenceService,
  taskCenterService,
  ticketService,
  dcomService,
  opsService,
  pmService,
  versionManageService,
  serverNodeService,
  northUserService,
  spareService,
  workdayManageService,
  visitorManagerService,
  approvalConfigService,
  approveCenterService,
  channelConfigService,
  insideNoticeService,
  warrantyOrderService,
  borrowsAndReturnService,
};
