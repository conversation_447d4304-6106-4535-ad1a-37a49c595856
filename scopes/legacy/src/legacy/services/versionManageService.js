import { callApi } from './api';

const getType = type => {
  // TODO:  @cf重构 task:2166
  if (type === 'POINT' || type === 'NON_POINT') {
    return 'POINT';
  } else if (type === 'ALARM' || type === 'NON_ALARM') {
    return 'ALARM';
  }
  return type;
};

/**
 * 查询版本信息
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/xik54f API 文档}
 * @param {object} params 请求参数
 * @param {string} [params.type] SPACE:空间；POINT:点位；DEVICE:设施；ALARM:告警；CHANNEL:采集；SPEC:设施档案
 */
export const fetchVersionList = payload => {
  return callApi({
    endpoint: 'dccm/resource/version/query',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data: {
        ...payload,
        type: getType(payload.type),
      },
    },
  });
};

/**
 * 创建版本
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/xik54f API 文档}
 * @param {object} params 请求参数
 * @param {string} [params.type] SPACE:空间；POINT:点位；DEVICE:设施；ALARM:告警；CHANNEL:采集；SPEC:设施档案
 * @param {string} [params.ranges] 范围
 * @param {string} [params.description] 描述
 */
export const createVersion = ({ type, ranges, description }) => {
  return callApi({
    endpoint: 'dccm/resource/version/create',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data: {
        type: getType(type),
        ranges,
        description,
      },
    },
  });
};

/**
 * 删除版本
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/xik54f API 文档}
 * @param {object} params 请求参数
 * @param {string} [params.type] SPACE:空间；POINT:点位；DEVICE:设施；ALARM:告警；CHANNEL:采集；SPEC:设施档案
 * @param {string} [params.range] 范围
 * @param {string} [params.version] 版本号
 */
export const deleteVersion = ({ type, range, version }) => {
  return callApi({
    endpoint: 'dccm/resource/version/delete',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data: {
        type: getType(type),
        range,
        version,
      },
    },
  });
};

/**
 * 发布版本
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/xik54f API 文档}
 * @param {object} params 请求参数
 * @param {string} [params.type] SPACE:空间；POINT:点位；DEVICE:设施；ALARM:告警；CHANNEL:采集；SPEC:设施档案
 * @param {string} [params.description] 描述
 * @param {string} [params.versionMap] "versionMap": {"EC01.A": 1, "EC01.B": 2, "EC01.C": 3},
 */
export const deployVersion = ({ type, description, versionMap }) => {
  return callApi({
    endpoint: 'dccm/resource/version/push',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data: {
        type: getType(type),
        description,
        versionMap,
      },
    },
  });
};

/**
 * 查询版本历史
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/xik54f API 文档}
 * @param {object} params 请求参数
 * @param {string} [params.type] SPACE:空间；POINT:点位；DEVICE:设施；ALARM:告警；CHANNEL:采集；SPEC:设施档案
 * @param {string} [params.range] 范围
 */
export const versionList = ({ type, range, versionStatusList }) => {
  return callApi({
    endpoint: 'dccm/resource/version/list/query',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data: {
        type: getType(type),
        range,
        versionStatusList,
      },
    },
  });
};

/**
 * 查询发布列表
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/xik54f API 文档}
 * @param {object} params 请求参数
 * @param {string} [params.type] SPACE:空间；POINT:点位；DEVICE:设施；ALARM:告警；CHANNEL:采集；SPEC:设施档案
 * @param {string} [params.range] 范围
 */
export const deployList = ({ type, range }) => {
  return callApi({
    endpoint: 'dccm/resource/version/log/query',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data: {
        type: getType(type),
        range,
      },
    },
  });
};
