import { callApi } from './api';

/**
 * 获取单子关联的附件信息
 * 接口文档：http://yapi.manyun-local.com/project/26/interface/api/439
 * @deprecated Replaced by `@manyun/dc-brain.service.fetch-biz-file-infos`
 * @param {object} params
 * @param {number} params.targetId
 * @param {'EVENT'|'REPAIR'|'INSPECT'} params.targetType
 * @param {number} params.pageNum
 * @param {number} params.pageSize
 */
export const fetchTicketAttachmentInfos = data =>
  callApi({
    endpoint: 'dcom/file/query/list',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 删除文件
 * 接口文档: http://yapi.manyun-local.com/project/26/interface/api/459
 * @deprecated Replaced by `@manyun/dc-brain.service.delete-biz-file`
 */
export const deleteFile = data => {
  return callApi({
    endpoint: 'dcom/file/delete',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 保存/上传文件列表
 * 接口文档：http://yapi.manyun-local.com/project/26/interface/api/435
 * @deprecated Replaced by `@manyun/dc-brain.service.save-biz-files`
 */
export const saveFiles = data => {
  return callApi({
    endpoint: 'dcom/file/save',
    options: {
      method: 'POST',
      data,
    },
  });
};
