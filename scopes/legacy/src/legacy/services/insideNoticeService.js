import { callApi } from './api';

export const fetchInsideNoticeList = data => {
  return callApi({
    endpoint: 'notify/inside/page',
    options: {
      method: 'POST',
      data,
    },
  });
};

export const setInsideNoticeRead = data => {
  return callApi({
    endpoint: 'notify/inside/read_flag',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data,
    },
  });
};

export const setInsideNoticeAllRead = data => {
  return callApi({
    endpoint: 'notify/inside/read_all',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data,
    },
  });
};
