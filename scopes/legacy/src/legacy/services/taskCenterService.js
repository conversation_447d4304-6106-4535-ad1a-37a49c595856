import { callApi } from './api';

/**
 * 创建维修单
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/afmf9t#1u63j API 文档}
 * @param {object} data
 * @param {string} data.taskSubType 维修类型编码
 * @param {string} data.idcTag
 * @param {string} data.blockTag
 * @param {'BLOCK'|'ROOM'|'DEVICE'} data.targetType 目标类型
 * @param {string} data.targetGuid 目标 GUID
 * @param {string} data.targetName 目标名称
 * @param {string} data.taskTitle 工单标题
 * @param {string} data.faultDesc 故障说明
 * @param {any[]} [data.fileInfoList] 附件
 */
export const createRepairTicket = data =>
  callApi({
    endpoint: 'taskcenter/repair/create',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 查询维修单进度反馈列表
 * @param {object} data
 * @param {string} data.taskNo 工单编号
 */
export const fetchRepairTicketProgressFeedbacks = data =>
  callApi({
    endpoint: 'taskcenter/repair/feedback/query',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 维修单子任务-反馈进度
 * @param {object} data
 * @param {string} data.taskNo 工单编号
 * @param {string} data.feedbackContent 反馈内容
 */
export const addRepairTicketProgressFeedback = data =>
  callApi({ endpoint: 'taskcenter/repair/feedback', options: { method: 'POST', data } });

/**
 * 查询维修单通知厂商列表
 * @param {object} data
 * @param {string} data.taskNo 工单编号
 */
export const fetchRepairTicketNotificationRecords = data =>
  callApi({ endpoint: 'taskcenter/repair/inform/query', options: { method: 'POST', data } });

/**
 * 维修单子任务-通知厂商
 * @param {object} data
 * @param {string} data.taskNo 工单编号
 * @param {string} data.informContent 通知内容
 * @param {string} data.informBy 通知人员 ID
 * @param {string} data.informByName 通知人员
 * @param {string} data.informMethod 通知方式
 * @param {string} data.email 通知人员邮件地址
 */
export const addRepairTicketNotificationRecord = data =>
  callApi({ endpoint: 'taskcenter/repair/inform', options: { method: 'POST', data } });

/**
 * 查询入库设备
 * {@link https://manyun.yuque.com/ewe5b3/pvswsv/ud9qfk API 文档}
 * @param {object} data
 * @param {string} data.taskNo 工单编号
 * @param {number} data.pageNum
 * @param {number} data.pageSize
 */
export const fetchInWarehouseDevice = data => {
  return callApi({
    endpoint: 'taskcenter/warehouse/inWarehouse/device/page',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 查询出库列表
 * {@link https://manyun.yuque.com/ewe5b3/pvswsv/tzpg8v API 文档}
 * @param {object} data
 * @param {string} data.taskNo 工单编号
 * @param {string} [data.deviceType] 设备类型
 */
export const fetchExWarehouseDeviceCard = data => {
  return callApi({
    endpoint: 'taskcenter/warehouse/exWarehouseCard/list',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 创建出库工单
 * {@link https://manyun.yuque.com/ewe5b3/pvswsv/mlfe4f API 文档}
 * @param {object} data
 * @param {string} data.taskTitle 工单标题
 * @param {string} data.exWarehouseReason 出库原因
 * @param {string} data.idcTag 机房标签
 * @param {string} data.blockGuid 楼栋标签
 * @param {string} data.roomGuid 房间标签
 * @param {string} data.taskStaffId 领料人ID
 * @param {Array} data.deviceModelList 设备出库列表
 * @param {boolean} data.numbered 是否有编号
 */
export const createExWarehousTicket = data => {
  return callApi({
    endpoint: 'taskcenter/warehouse/exWarehouse/create',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 创建入库工单
 * {@link https://manyun.yuque.com/ewe5b3/pvswsv/gcytp2 API 文档}
 * @param {object} data
 * @param {string} data.taskTitle 工单标题
 * @param {string} data.inWarehouseReason 入库原因
 * @param {string} data.idcTag 机房标签
 * @param {string} data.blockGuid 楼栋标签
 * @param {string} data.roomGuid 房间标签
 * @param {Array} data.deviceModelList 设备入库列表
 * @param {boolean} data.numbered 是否有编号
 */
export const createInWarehousTicket = data => {
  return callApi({
    endpoint: 'taskcenter/warehouse/inWarehouse/create',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 查询出库设备
 * {@link https://manyun.yuque.com/ewe5b3/pvswsv/yg1fz2 API 文档}
 * @param {object} data
 * @param {string} data.deviceType 设备类型
 * @param {string} data.productModel 型号
 * @param {string} data.vendor 厂商
 * @param {string} data.taskNo 工单编号
 * @param {number} data.pageNum
 * @param {number} data.pageSize
 */
export const fetchExWarehouseDevice = data => {
  return callApi({
    endpoint: 'taskcenter/warehouse/exWarehouse/device/page',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 设备出库
 * {@link https://manyun.yuque.com/ewe5b3/pvswsv/gu8xl8 API 文档}
 * @param {object} data
 * @param {string} data.taskNo 工单编号
 * @param {Array} [data.assetNoList] 设备资产编号列表
 * @param {string} data.deviceType 设备类型
 * @param {string} data.productModel 型号
 * @param {string} data.vendor 厂商
 * @param {boolean} data.numbered 是否有编号
 * @param {string} [data.roomGuid] 房间标签
 * @param {string} [data.spareGuid] 无资产设备编号
 * @param {number} [data.exWarehouseCount] 出库数量
 */
export const updateExWarehouse = data => {
  return callApi({
    endpoint: 'taskcenter/warehouse/exWarehouse/device',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 设备出库撤销
 * {@link https://manyun.yuque.com/ewe5b3/pvswsv/dq8fun API 文档}
 * @param {object} data
 * @param {string} data.taskNo 工单编号
 * @param {string} [data.id] id
 * @param {boolean} data.numbered 是否有编号
 */
export const cancelExWarehouse = data => {
  return callApi({
    endpoint: 'taskcenter/warehouse/exWarehouse/cancel',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 查询任务列表
 * {@link https://manyun.yuque.com/luc1u2/ilvfpi/geo2de API 文档}
 * @param {object} data
 * @param {string} [data.name] 任务名称
 * @param {string} [data.jobType] 任务类型
 * @param {string} [data.startTime] 开始时间
 * @param {string} [data.endTime] 结束时间
 * @param {string} [data.periodUnit] 周期单位
 * @param {string} [data.taskStatus] 状态
 * @param {string} [data.creatorId] 创建人
 * @param {number} data.pageNum
 * @param {number} data.pageSize
 */
export const fetchData = data =>
  callApi({
    endpoint: 'taskcenter/schedule/page',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 批量启用/停用任务
 * {@link https://manyun.yuque.com/luc1u2/ilvfpi/geo2de API 文档}
 * @param {object} data
 * @param {boolean} data.taskStatus 1 -> 启用；0 -> 停用
 * @param {number[]} data.ids 任务列表 id 的集合
 */
export const batchUpdateTaskCenters = data =>
  callApi({
    endpoint: 'taskcenter/schedule/update/status',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 删除任务
 * @param {number}id 任务id
 */
export const deleteTaskCenters = id =>
  callApi({
    endpoint: 'taskcenter/schedule/delete',
    options: {
      method: 'POST',
      data: { id },
    },
  });

/**
 * 根据id查询任务
 * @param {object} data
 * @param {number} data.id 任务id
 */
export const fetchTaskBaseInfo = data =>
  callApi({
    endpoint: 'taskcenter/schedule/info',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 新增任务
 * @param {object} data
 * @param {string} data.name 任务名称
 * @param {string} data.jobType 任务类型
 * @param {Array} data.allowTriggerTime 允许触发时间
 * @param {number} data.endTime 结束时间
 * @param {Array} data.blocks 分发楼栋
 * @param {number} data.jobSla 时限
 * @param {string} data.slaUnit 时限单位
 * @param {Array} data.splitors 拆分规则
 * @param {string} data.cycles 周期
 * @param {string} data.periodUnit 周期单位
 * @param {Array} data.jobItems 任务项list
 */
export const fetchCreateTask = data =>
  callApi({
    endpoint: 'taskcenter/schedule/create',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 编辑任务
 * @param {object} data
 * @param {number} data.id 任务id
 * @param {string} data.name 任务名称
 * @param {string} data.jobType 任务类型
 * @param {Array} data.allowTriggerTime 允许触发时间
 * @param {number} data.endTime 结束时间
 * @param {Array} data.blocks 分发楼栋
 * @param {number} data.jobSla 时限
 * @param {string} data.slaUnit 时限单位
 * @param {Array} data.splitors 拆分规则
 * @param {string} data.cycles 周期
 * @param {string} data.periodUnit 周期单位
 * @param {Array} data.jobItems 任务项list
 */
export const fetchEditTask = data =>
  callApi({
    endpoint: 'taskcenter/schedule/update',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 复制任务
 * @param {number}id 任务id
 */
export const copyTaskCenters = id =>
  callApi({
    endpoint: 'taskcenter/schedule/copy',
    options: {
      method: 'POST',
      data: { id },
    },
  });

/**
 * 行事历 年维度
 * @param {object} data
 */
export const fetchTaskScheduleInYear = data =>
  callApi({
    endpoint: 'taskcenter/schedule/year/calendar',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 创建盘点工单
 * {@link https://manyun.yuque.com/ewe5b3/pvswsv/attpuc API 文档}
 * @param {object} data
 * @param {string} data.taskTitle 工单标题
 * @param {Array} data.targetTags 目标标签
 * @param {Array} data.roomTags 包间标签
 * @param {boolean} data.limitRoom 是否指定包间
 * @param {string} data.inventoryType 盘点类型
 * @param {string} data.inventoryTargetType 盘点目标类型
 * @param {string} data.inventorySubType 盘点子类型
 * @param {string} data.idcTag 机房标签
 * @param {string} data.blockGuid 楼栋标签
 */
export const createInventoryTicket = data => {
  return callApi({
    endpoint: 'taskcenter/inventory/create',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 查询盘点卡片列表
 * {@link https://manyun.yuque.com/ewe5b3/pvswsv/rf7ubs API 文档}
 * @param {object} data
 * @param {string} data.taskNo 工单编号
 * @param {string} [data.roomGuid] 房间号
 * @param {Array} [data.roomTypes] 包间类型
 */
export const fetchInventoryCard = data => {
  return callApi({
    endpoint: 'taskcenter/inventory/card/list',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 分页查询盘点设备
 * {@link http://172.16.0.17:13000/project/142/interface/api/17136 YAPI 文档}
 * {@link https://manyun.yuque.com/ewe5b3/pvswsv/gm0mui API 文档}
 * @param {object} data
 * @param {boolean} data.onlyException 是否只看异常
 * @param {string} data.roomGuid 包间guid
 * @param {string} data.taskNo 工单编号
 * @param {string} data.inventorySubType 盘点子类型
 * @param {boolean} data.numbered 是否有编号
 * @param {number} data.pageNum
 * @param {number} data.pageSize
 */
export const fetchInventoryDevice = data => {
  return callApi({
    endpoint: 'taskcenter/inventory/device/page',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 查询包间下盘点异常设备数量
 * {@link https://manyun.yuque.com/ewe5b3/pvswsv/myttve API 文档}
 * @param {object} data
 * @param {string} data.roomGuid 包间guid
 * @param {string} data.taskNo 工单编号
 * @param {boolean} data.numbered 是否有编号
 */
export const fetchInventoryExceptionCount = data => {
  return callApi({
    endpoint: 'taskcenter/inventory/device/exception/count',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 设备盘点
 * {@link https://manyun.yuque.com/ewe5b3/pvswsv/ybro4g API 文档}
 * @param {object} data
 * @param {string} data.assetNo 资产编号
 * @param {string} data.deviceType 设备类型
 * @param {string} data.inventoryStatus 盘点状态
 * @param {Array} data.fileInfos 图片信息列表
 * @param {string} data.productModel 型号
 * @param {string} data.remark 备注
 * @param {string} data.vendor 厂商
 * @param {string} data.roomGuid 包间guid
 * @param {string} data.taskNo 工单编号
 * @param {string} data.deviceGuid 设备guid
 */
export const updateInventoryDevice = data => {
  return callApi({
    endpoint: 'taskcenter/inventory/device',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 无资产设备盘点
 * {@link https://manyun.yuque.com/ewe5b3/pvswsv/hqkkr2 API 文档}
 * @param {object} data
 * @param {string} data.deviceType 设备类型
 * @param {string} data.productModel 型号
 * @param {number} data.inventoryCount 盘点数量
 * @param {string} data.remark 备注
 * @param {string} data.vendor 厂商
 * @param {string} data.roomGuid 包间guid
 * @param {string} data.taskNo 工单编号
 */
export const updateInventorySpareDevice = data => {
  return callApi({
    endpoint: 'taskcenter/inventory/spareDevice',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 卡片目标查询
 * {@link https://manyun.yuque.com/ewe5b3/pvswsv/oafdvh API 文档}
 * @param {object} data
 * @param {string} data.roomGuid 包间guid
 * @param {string} data.taskNo 工单编号
 * @param {string} data.inventoryTargetType 盘点目标类型
 */
export const fetchInventoryCardTarget = data => {
  return callApi({
    endpoint: 'taskcenter/inventory/card/target',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 移除盘点设备
 * {@link https://manyun.yuque.com/ewe5b3/pvswsv/alr4ut API 文档}
 * @param {object} data
 * @param {string} data.taskNo 工单编号
 * @param {string} data.assetNo 资产编号
 */
export const removeInventoryDevice = data => {
  return callApi({
    endpoint: 'taskcenter/inventory/device/removel',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 分页统计盘点设备
 * {@link https://manyun.yuque.com/ewe5b3/pvswsv/gokhmf API 文档}
 * @param {object} data
 * @param {string} [data.assetNo] 资产编号
 * @param {Array} [data.blockGuidList] 楼栋guid列表
 * @param {string} [data.countStatus] 统计状态
 * @param {Array} [data.deviceTypeList] 三级设备类型列表
 * @param {Array} [data.topCategoryList] 一级设备类型列表
 * @param {Array} [data.secondCategoryList] 二级设备类型列表
 * @param {string} [data.vendor] 厂商
 * @param {string} [data.productModel] 型号
 * @param {number} data.pageNum
 * @param {number} data.pageSize
 */
export const fetchInventoryAnalyticsDevice = data => {
  return callApi({
    endpoint: 'taskcenter/inventory/device/count/page',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 分页统计无资产盘点设备
 * {@link https://manyun.yuque.com/ewe5b3/pvswsv/ab54gg API 文档}
 * @param {object} data
 * @param {Array} [data.blockGuidList] 楼栋guid列表
 * @param {string} [data.countStatus] 统计状态
 * @param {Array} [data.deviceTypeList] 三级设备类型列表
 * @param {Array} [data.topCategoryList] 一级设备类型列表
 * @param {Array} [data.secondCategoryList] 二级设备类型列表
 * @param {string} [data.vendor] 厂商
 * @param {string} [data.productModel] 型号
 * @param {number} data.pageNum
 * @param {number} data.pageSize
 */
export const fetchInventoryAnalyticsSpareDevice = data => {
  return callApi({
    endpoint: 'taskcenter/inventory/spareDevice/count/Page',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 统计盘点设备数量
 * {@link https://manyun.yuque.com/ewe5b3/pvswsv/gokhmf API 文档}
 * @param {object} data
 * @param {string} [data.assetNo] 资产编号
 * @param {string} [data.countStatus] 统计状态
 * @param {Array} [data.blockGuidList] 楼栋guid列表
 * @param {Array} [data.deviceTypeList] 三级设备类型列表
 * @param {Array} [data.topCategoryList] 一级设备类型列表
 * @param {Array} [data.secondCategoryList] 二级设备类型列表
 * @param {string} [data.vendor] 厂商
 * @param {string} [data.productModel] 型号
 */
export const fetchInventoryAnalyticsCount = data => {
  return callApi({
    endpoint: 'taskcenter/inventory/device/count',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 统计无资产盘点设备数量
 * {@link https://manyun.yuque.com/ewe5b3/pvswsv/owi8b3 API 文档}
 * @param {object} data
 * @param {string} [data.countStatus] 统计状态
 * @param {Array} [data.blockGuidList] 楼栋guid列表
 * @param {Array} [data.deviceTypeList] 三级设备类型列表
 * @param {Array} [data.topCategoryList] 一级设备类型列表
 * @param {Array} [data.secondCategoryList] 二级设备类型列表
 * @param {string} [data.vendor] 厂商
 * @param {string} [data.productModel] 型号
 */
export const fetchInventoryAnalyticsSpareCount = data => {
  return callApi({
    endpoint: 'taskcenter/inventory/spareDevice/count',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 包间下设备（卡片）盘点完成
 * {@link https://manyun.yuque.com/ewe5b3/pvswsv/izmgo7 API 文档}
 * @param {object} data
 * @param {string} data.inventorySubType 盘点子类型
 * @param {string} data.roomGuid 包间guid
 * @param {string} data.taskNo 工单编号
 */
export const updateInventoryCard = data => {
  return callApi({
    endpoint: 'taskcenter/inventory/card/finish',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 检验包间下是否所有设备已盘点
 * {@link https://manyun.yuque.com/ewe5b3/pvswsv/iuhrfk API 文档}
 * @param {object} data
 * @param {string} data.roomGuid 包间guid
 * @param {string} data.taskNo 工单编号
 */
export const validInventoryFinished = data => {
  return callApi({
    endpoint: 'taskcenter/inventory/valid/finished',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 创建盘点工单
 * {@link http://chandao.manyun-local.com/zentao/task-view-105.html API 文档}
 * @param {object} data
 * @param {string} data.taskType 工单类型
 * @param {string} data.taskSubType 上下线类型
 * @param {string} data.idcTag 机房标签
 * @param {string} data.blockGuid 楼栋标签
 */
export const createOnOffTicket = data => {
  return callApi({
    endpoint: 'taskcenter/updownline/create',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 分页查询维保配置
 * {@link https://manyun.yuque.com/luc1u2/ilvfpi/tgz9dr#mBGu6 API 文档}
 * @param {object} data
 * @param {string} [data.configName] 维保配置名称
 * @param {string} [data.maintenanceType] 维保类型
 * @param {string} [data.deviceType] 维保设备类型
 * @param {string} [data.createStartTime]  创建开始时间
 * @param {string} [data.createEndTime]  创建结束时间
 * @param {string} [data.creatorId]  创建人ID
 * @param {number} data.pageNum
 * @param {number} data.pageSize
 */
export const fetchMaintainConfigData = data =>
  callApi({
    endpoint: 'taskcenter/maintenance/config/list',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 维保配置新建
 * {@link https://manyun.yuque.com/luc1u2/ilvfpi/tgz9dr#Io49i API 文档}
 * @param {object} data
 * @param {string} data.configName 维保配置名称
 * @param {string} data.maintenanceType 维保类型
 * @param {string} data.deviceType 维保设备类型
 * @param {object} data.maintenanceContent
 * @param {string} data.firstCategoryName  设备一级分类名称
 * @param {string} data.secondCategoryName  设备二级分类名称
 */
export const fetchCreateMaintainConfig = data =>
  callApi({
    endpoint: 'taskcenter/maintenance/config/create',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 维保配置更新
 * {@link https://manyun.yuque.com/luc1u2/ilvfpi/tgz9dr#NWjNq API 文档}
 * @param {object} data
 * @param {number} data.maintenanceConfigId 维保配置id
 * @param {string} data.configName 维保配置名称
 * @param {string} data.maintenanceType 维保类型
 * @param {string} data.deviceType 维保设备类型
 * @param {object} data.maintenanceContent
 * @param {string} data.firstCategoryName  设备一级分类名称
 * @param {string} data.secondCategoryName  设备二级分类名称
 */
export const fetchEditMaintainConfig = data =>
  callApi({
    endpoint: 'taskcenter/maintenance/config/update',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 查询维护详情
 * {@link https://manyun.yuque.com/luc1u2/ilvfpi/tgz9dr#7viWZ API 文档}
 * @param {number} maintenanceConfigId 维保配置id
 */
export const fetchMaintenanceConfigInfo = maintenanceConfigId =>
  callApi({
    endpoint: 'taskcenter/maintenance/config/detail',
    options: {
      method: 'POST',
      data: {
        maintenanceConfigId,
      },
    },
  });

/**
 * 删除维保配置
 * {@link https://manyun.yuque.com/luc1u2/ilvfpi/tgz9dr#Bbdhg API 文档}
 * @param {object} data
 * @param {number} data.maintenanceConfigId id
 */
export const fetchDeleteMaintenanceConfig = data =>
  callApi({
    endpoint: 'taskcenter/maintenance/config/delete',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 查询检查配置列表
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/cc1piv#oVnv1API 文档}
 * @param {object} data
 * @param {string} [data.configName] 配置名称
 * @param {string} [data.taskType] 工单大类
 * @param {string} [data.taskSubType] 工单子类
 * @param {string} [data.createTime]  创建时间
 * @param {string} [data.createByName]  创建人名
 * @param {number} data.pageNum
 * @param {number} data.pageSize
 */
export const fetchPowerCheckConfigData = data =>
  callApi({
    endpoint: 'taskcenter/power/check/config/page/query',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 删除检查配置
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/cc1piv#Sb9CD API 文档}
 * @param {object} data
 * @param {number} data.id 配置id
 */
export const deletePowerCheckConfig = data =>
  callApi({
    endpoint: 'taskcenter/power/check/config/delete',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 创建检查配置
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/cc1piv#dbiS9 API 文档}
 * @param {object} data
 * @param {string} data.configName 配置名称
 * @param {string} data.taskType 工单大类
 * @param {string} data.taskSubType 工单子类
 * @param {Array} data.checkItems 检查项
 */
export const fetchCreatePowerCheckConfig = data =>
  callApi({
    endpoint: 'taskcenter/power/check/config/create',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 更新盘点项
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/cc1piv#EeewU API 文档}
 * @param {object} data
 * @param {number} data.id  盘点项id
 * @param {object} data
 * @param {string} data.configName 配置名称
 * @param {string} data.taskType 工单大类
 * @param {string} data.taskSubType 工单子类
 * @param {Array} data.checkItems 检查项
 */
export const fetchEditPowerCheckConfig = data =>
  callApi({
    endpoint: 'taskcenter/power/check/config/update',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 查询盘点配置详情
 * {@link  API 文档}
 * @param {number} data.configId 盘点项id
 */
export const fetchPowerCheckConfigInfo = data =>
  callApi({
    endpoint: 'taskcenter/power/check/config/detail/query',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 查询检查项配置
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/cc1piv#PIVxS API 文档}
 * @param data
 * @param  {string} data.taskType 工单大类
 * @param  {string} data.taskSubType 工单子类
 */
export const fetchPowerCheckConfigCheckItem = data =>
  callApi({
    endpoint: 'taskcenter/power/check/item/query',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 分页查询盘点项
 * {@link https://manyun.yuque.com/ewe5b3/pvswsv/hift7q API 文档}
 * @param {object} data
 * @param {string} [data.configName] 盘点项名
 * @param {string} [data.createStartTime] 创建开始时间
 * @param {string} [data.createEndTime] 创建结束时间
 * @param {string} [data.inventoryType] 盘点类型
 * @param {string} [data.inventorySubType] 盘点子类型
 * @param {string} [data.updateStartTime]  更新开始时间
 * @param {string} [data.updateEndTime]  更新结束时间
 * @param {string} [data.modifierId]  更新人id
 * @param {number} data.pageNum
 * @param {number} data.pageSize
 */
export const fetchInventoryConfigData = data =>
  callApi({
    endpoint: 'taskcenter/inventory/config/Page',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 盘点配置删除
 * {@link https://manyun.yuque.com/ewe5b3/pvswsv/fgrf9m API 文档}
 * @param {object} data
 * @param {number} data.configId 盘点项id
 */
export const deleteInventoryConfig = data =>
  callApi({
    endpoint: 'taskcenter/inventory/config/delete',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 创建盘点项
 * {@link https://manyun.yuque.com/ewe5b3/pvswsv/fb4u60 API 文档}
 * @param {object} data
 * @param {string} data.configName 盘点项名
 * @param {string} data.deviceType 设备类型
 * @param {string} data.inventoryType 盘点类型
 * @param {string} data.inventorySubType 盘点子类型
 */
export const fetchCreateInventoryConfig = data =>
  callApi({
    endpoint: 'taskcenter/inventory/config/add',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 更新盘点项
 * {@link https://manyun.yuque.com/ewe5b3/pvswsv/fgrf9m API 文档}
 * @param {object} data
 * @param {number} data.configId  盘点项id
 * @param {string} data.configName 盘点项名
 * @param {string} data.deviceType 设备类型
 * @param {string} data.inventoryType 盘点类型
 * @param {string} data.inventorySubType 盘点子类型
 */
export const fetchEditInventoryConfig = data =>
  callApi({
    endpoint: 'taskcenter/inventory/config/update',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 查询盘点项详情
 * {@link https://manyun.yuque.com/ewe5b3/pvswsv/xpmbsf API 文档}
 * @param  {number} configId 盘点项id
 */
export const fetchInventoryConfigInfo = configId =>
  callApi({
    endpoint: 'taskcenter/inventory/config/detail',
    options: {
      params: {
        configId,
      },
    },
  });

/**
 * 巡检配置列表查询
 * {@link https://manyun.yuque.com/luc1u2/ilvfpi/utok8o#nsO6m API 文档}
 * @param {object} data
 * @param {string} [data.configName] 巡检配置名称
 * @param {string} [data.inspectType] 巡检类型
 * @param {string} [data.subTypeCode] 巡检子类型guid
 * @param {string} [data.createStartTime]  创建开始时间
 * @param {string} [data.createEndTime]  创建结束时间
 * @param {string} [data.creatorId]  创建人ID
 * @param {number} data.pageNum
 * @param {number} data.pageSize
 */
export const fetchInspectionConfigData = data =>
  callApi({
    endpoint: 'taskcenter/inspect/config/list',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 巡检配置删除
 * {@link https://manyun.yuque.com/luc1u2/ilvfpi/utok8o#zF4jK API 文档}
 * @param {object} data
 * @param {number} data.id 巡检配置id
 */
export const deleteInspectionConfig = data =>
  callApi({
    endpoint: 'taskcenter/inspect/config/delete',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 巡检配置详情查询
 * {@link https://manyun.yuque.com/luc1u2/ilvfpi/utok8o#NNdzp API 文档}
 * @param {object} data
 * @param {number} data.inspectConfigId 巡检配置id
 */
export const fetchInspectionConfigDetail = id =>
  callApi({
    endpoint: 'taskcenter/inspect/config/detail',
    options: {
      method: 'POST',
      data: {
        inspectConfigId: id,
      },
    },
  });

/**
 * 巡检配置新建
 * {@link https://manyun.yuque.com/luc1u2/ilvfpi/utok8o#nsO6m API 文档}
 * @param {object} data
 * @param {string} data.configName 巡检配置名称
 * @param {string} data.inspectType 巡检类型
 * @param {string} data.subTypeCode 巡检子类型code
 * @param {string} data.subTypeName 巡检子类型name
 * @param {Array} data.checkPoints 测点巡检项
 * @param {string} data.firstCategoryName 一级分类名称
 * @param {string} data.secondCategoryName 二级分类名称
 */
export const fetchCreateInspectionConfig = data =>
  callApi({
    endpoint: 'taskcenter/inspect/config/create',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 巡检配置更新
 * {@link https://manyun.yuque.com/luc1u2/ilvfpi/utok8o#B49AH API 文档}
 * @param {object} data
 * @param {number} data.inspectConfigId 巡检配置id
 * @param {string} data.configName 巡检配置名称
 * @param {string} data.inspectType 巡检类型
 * @param {string} data.subTypeCode 巡检子类型code
 * @param {string} data.subTypeName 巡检子类型name
 * @param {Array} data.checkPoints 测点巡检项
 * @param {string} data.firstCategoryName 一级分类名称
 * @param {string} data.secondCategoryName 二级分类名称
 */
export const fetchUpdateInspectionConfig = data =>
  callApi({
    endpoint: 'taskcenter/inspect/config/update',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 创建出入门工单
 * {@link https://manyun.yuque.com/ewe5b3/pvswsv/sep52m API 文档}
 * @param {object} data
 * @param {string} data.taskTitle 工单标题
 * @param {string} data.idcTag 机房编号
 * @param {string} data.blockGuid 资产类型
 * @param {string} data.accessDoorReason 出入门原因
 * @param {string} data.accessDoorTime 出入门时间
 * @param {string} data.accessType 出入门类型
 * @param {string} data.assetType 资产类型
 * @param {Array} data.assetDevices 有资产设备列表
 * @param {Array} data.consumables 无资产设备列表
 */
export const createAccessDoorTicket = data => {
  return callApi({
    endpoint: 'taskcenter/access/create',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 创建出入门工单
 * {@link https://manyun.yuque.com/ewe5b3/pvswsv/kbm4da API 文档}
 * @param {object} data
 * @param {string} data.taskNo 工单编号
 * @param {Boolean} data.haveSerialNo 是否有资产编号
 */
export const fetchAccessDeviceList = data => {
  return callApi({
    endpoint: 'taskcenter/access/deviceList',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 创建人员入室申请工单
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/afmf9t#1u63j API 文档}
 * @param {object} data
 * @param {string} data.taskSubType 人员入室申请类型编码
 * @param {string} data.relateTaskNo 关联工单号
 * @param {string} data.relateTaskType 关联工单了悉尼港
 * @param {string} data.taskTitle 工单标题
 * @param {string} data.enterReason 入室原因
 * @param {datatime} data.approveStartTime 授权开始时间
 * @param {datatime} data.approveEndTime 授权结束时间
 * @param {string} data.idcTag 入室机房
 * @param {any[]} [data.visitorInfos] 入室人员
 */
export const createVisitorTicket = data =>
  callApi({
    endpoint: 'taskcenter/visitor/task/create',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 分页查询人员入室申请工单下人员
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/afmf9t#1u63j API 文档}
 * @param {object} data
 * @param {string} data.relateTaskNo 工单号
 * @param {string} data.name 人员名称
 * @param {string} data.identityNo 身份证号
 * @param {string} data.companyName 公司名称
 * @param {datatime} data.operable 是否具备操作权限
 */
export const fetchVisitorStaffList = data =>
  callApi({
    endpoint: 'taskcenter/visitor/info/list',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 查询人员入室申请工单下人员姓名
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/afmf9t#1u63j API 文档}
 * @param {object} data
 * @param {string} data.taskNo 工单号
 */
export const fetchVisitorStaffNameList = data =>
  callApi({
    endpoint: 'taskcenter/visitor/staff/name',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 撤回人员入室申请工单
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/afmf9t#1u63j API 文档}
 * @param {object} data
 * @param {string} data.taskNo 工单号
 */
export const revokeVisitorApprove = data =>
  callApi({
    endpoint: 'taskcenter/visitor/approve/revoke',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 重新发起人员入室申请工单
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/afmf9t#1u63j API 文档}
 * @param {object} data
 * @param {string} data.taskNo 工单号
 */
export const createVisitorApprove = data =>
  callApi({
    endpoint: 'taskcenter/visitor/approve/create',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 查询人员入室申请工单下所有人员
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/afmf9t#1u63j API 文档}
 * @param {object} data
 * @param {string} data.relateTaskNo 工单号
 * @param {string} data.name 人员名称
 * @param {string} data.identityNo 身份证号
 * @param {string} data.companyName 公司名称
 * @param {datatime} data.operable 是否具备操作权限
 */
export const fetchVisitorInfoStaffList = data =>
  callApi({
    endpoint: 'taskcenter/visitor/info/staff/list',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 修改人员入室申请工单
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/afmf9t#1u63j API 文档}
 * @param {object} data
 * @param {string} data.taskNo 工单号
 * @param {string} data.taskSubType 人员入室申请类型编码
 * @param {string} data.relateTaskNo 关联工单号
 * @param {string} data.relateTaskType 关联工单了悉尼港
 * @param {string} data.taskTitle 工单标题
 * @param {string} data.enterReason 入室原因
 * @param {datatime} data.approveStartTime 授权开始时间
 * @param {datatime} data.approveEndTime 授权结束时间
 * @param {string} data.idcTag 入室机房
 * @param {any[]} [data.visitorInfos] 入室人员
 */
export const updateVisitorTicket = data =>
  callApi({
    endpoint: 'taskcenter/visitor/task/update',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 查询订单下的机柜
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/cc1piv#SxFWx API 文档}
 * @param {object} data
 * @param {string} data.accountNo 订单号
 * @param {string} data.gridGuid 机柜guid
 * @param {string} data.startTime 开始时间
 * @param {string} data.endTime 结束时间
 * @param {number} data.pageNum 页数
 * @param {number} data.pageSize 页码
 */
export const fetchPowerByGuidList = data => {
  return callApi({
    endpoint: 'taskcenter/power/grid/by/account/query',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 出入门工单撤销
 * {@link https://manyun.yuque.com/ewe5b3/pvswsv/ek4r7n API 文档}
 * @param {object} data
 * @param {string} data.taskNo 工单编号
 * @param {string} [data.reason]
 */
export const revokeAccessTicket = data => {
  return callApi({
    endpoint: 'taskcenter/access/revoke',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 重新发起出入门工单
 * {@link https://manyun.yuque.com/ewe5b3/pvswsv/bk5u7q API 文档}
 * @param {object} data
 * @param {string} data.taskNo 工单单号
 * @param {string} data.taskTitle 工单标题
 * @param {string} data.idcTag 机房编号
 * @param {string} data.blockGuid 楼栋guid
 * @param {string} data.accessDoorReason 出入门原因
 * @param {string} data.accessDoorTime 出入门时间
 * @param {Array} data.assetDevices 有资产设备列表
 * @param {Array} data.consumables 无资产设备列表
 */
export const rePostAccessDoorTicket = data => {
  return callApi({
    endpoint: 'taskcenter/access/rePost',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 创建通用工单
 * {@link https://manyun.yuque.com/luc1u2/ilvfpi/urzcts/edit#p0n3u API 文档}
 * @param {object} data
 * @param {string} data.generalTaskType 工单类型
 * @param {string} data.generalSubTaskType 工单子类型
 * @param {string} data.idcTag
 * @param {string} data.blockGuid
 * @param {Array} data.generalItemInfoList
 */
export const fetchCreateDeviceGeneralTicket = data => {
  return callApi({
    endpoint: 'taskcenter/general/order/create',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 *  查询通用工单项
 * {@link https://manyun.yuque.com/luc1u2/ilvfpi/urzcts#YSZXy API 文档}
 * @param {object} data
 * @param {string} data.taskNo
 */
export const fetchDeviceGeneralItems = data => {
  return callApi({
    endpoint: 'taskcenter/general/item/list',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 创建到货验收工单
 * {@link https://manyun.yuque.com/ewe5b3/bnpnl9/sz8d4p#xpTh7 API 文档}
 * @param {object} data
 * @param {string} data.acceptType 验收类型
 * @param {string} data.relateNo 关联单号
 * @param {string} data.idcTag
 * @param {string} data.blockGuid
 * @param {string} data.title
 * @param {Array} data.acceptAssetCreateInfoList 到货列表
 */
export const createAcceptTicket = data => {
  return callApi({
    endpoint: 'taskcenter/accept/create',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 根据关联单号、验收类型查询到货资产
 * {@link https://manyun.yuque.com/ewe5b3/bnpnl9/sz8d4p#xpTh7 API 文档}
 * @param {object} data
 * @param {string} data.sourceType 验收类型
 * @param {string} data.batchNo 关联单号
 */
export const fetchArrivalAssets = data => {
  return callApi({
    endpoint: 'dccm/arrival/asset/without/page/list',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 到货验收工单详情页资产列表
 * {@link https://manyun.yuque.com/ewe5b3/bnpnl9/sz8d4p#xpTh7 API 文档}
 * @param {object} data
 * @param {string} data.taskNo
 * @param {number} data.pageNum 页数
 * @param {number} data.pageSize 页码
 */
export const fetchAcceptDetailAssets = data => {
  return callApi({
    endpoint: 'taskcenter/accept/asset/list',
    options: {
      method: 'POST',
      data,
    },
  });
};
