import { callApi } from './api';

/**
 * 查询收敛配置列表
 * {@link https://manyun.yuque.com/ewe5b3/pkinbk/aaibne API 文档}
 * @param {string} type 元数据类型
 */
export const fetchMetadataByType = type =>
  callApi({
    endpoint: 'dccm/meta/query/by/type',
    options: {
      params: { type },
    },
  });
/**
 * 添加元数据
 * {@link https://manyun.yuque.com/ewe5b3/pkinbk/zs0t7v API 文档}
 * @param {object} data
 * @param {string} data.metaName
 * @param {string} data.metaType
 * @param {string} data.parentMetaCode
 * @param {string} data.parentMetaType
 */
export const fetchMetaCreate = data =>
  callApi({
    endpoint: 'dccm/meta/create',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 订阅列表  - 废弃(没有使用)
 *
 *  **/
export const fetchNotifySubscribeUsableList = data =>
  callApi({
    endpoint: 'notify/subscribe/usable/list',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 订阅详情
 * {@link https://manyun.yuque.com/ewe5b3/pkinbk/qqd62g#4kbof API 文档}
 * @param {object} data
 * @param {string} data.subscribeType//订阅类型，必传
 *
 *  **/
export const fetchNotifySubscribeDetail = data =>
  callApi({
    endpoint: 'notify/subscribe/detail',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 最新等级
 * {@link https://manyun.yuque.com/ewe5b3/pkinbk/qqd62g#bWu9m API 文档}
 *
 *  **/
export const fetchNotifySubscribeLevel = data =>
  callApi({
    endpoint: 'notify/subscribe/level',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 事件级别更新
 * {@link https://manyun.yuque.com/ewe5b3/pkinbk/qqd62g#bWu9m API 文档}
 *
 *  **/
export const fetchNotifySubscribeModify = data =>
  callApi({
    endpoint: 'notify/subscribe/modify',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 事件级别新建
 * {@link https://manyun.yuque.com/ewe5b3/pkinbk/qqd62g#bWu9m API 文档}
 *
 *  **/
export const fetchNotifySubscribeAdd = data =>
  callApi({
    endpoint: 'notify/subscribe/add',
    options: {
      method: 'POST',
      // contentType: 'applicatin/json',
      data,
    },
  });

/**
 * 启用/停用订阅
 * @param {object} data
 * @param {string} data.subscribeCode
 * @param {boolean} data.isUsable
 */
export const toggleSubscriptionDisabled = data =>
  callApi({
    endpoint: 'notify/subscribe/closeoropen',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 删除元数据
 * {@link https://manyun.yuque.com/ewe5b3/pkinbk/harsyg API 文档}
 * @param {object} data
 * @param {Number} data.id
 */
export const fetchMetaDelete = data =>
  callApi({
    endpoint: 'dccm/meta/delete',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 删除元数据
 * {@link https://manyun.yuque.com/ewe5b3/pkinbk/ef5fon API 文档}
 * @param {object} data
 * @param {Number} data.id
 * @param {string} data.metaName
 */
export const fetchMetaUpdate = data =>
  callApi({
    endpoint: 'dccm/meta/update',
    options: {
      method: 'POST',
      data,
    },
  });
