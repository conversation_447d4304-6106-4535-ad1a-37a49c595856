import { callApi } from './api';

/**
 * @typedef Expression
 * @property {string} type
 * @property {string} name
 * @property {string} value
 * @property {Expression[]} children
 */

/**
 * 新增聚合点位
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/yenmkw API 文档}
 */
export const fetchCreateMergedPoint = data =>
  callApi({
    endpoint: 'dccm/point/agg/add',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 新增加工点位
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/cv73dy API 文档}
 */
export const fetchCreatePressedPoint = data =>
  callApi({
    endpoint: 'dccm/point/cal/add',
    options: {
      method: 'POST',
      data,
    },
  });

/***
 * 查询点位是否被聚合
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/wscuun API 文档}
 */
export const fetchPointAggIsMerged = data =>
  callApi({
    endpoint: 'dccm/point/query/agg/list',
    options: {
      params: data,
    },
  });

/***
 * 批量查询点位信息
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/kbo52b API 文档}
 */
export const fetchBatchPoint = data =>
  callApi({
    endpoint: 'dccm/point/batch/query',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 修改加工点位
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/mqxqag API 文档}
 */
export const fetchUpdatePressedPoint = data =>
  callApi({
    endpoint: 'dccm/point/cal/update',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 验证表达式
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/qfcbnb API 文档}
 * @param {object} data
 * @param {string} data.dataType
 * @param {number} data.precision
 * @param {{ [pointKey: string]: number }} data.pointValue
 * @param {Expression[]} data.expressionInfos
 */
export const checkCalculatedAiPointExpression = data =>
  callApi({
    endpoint: 'dccm/point/check/express',
    options: { method: 'POST', data },
  });

/**
 * 查询设备类型关联的型号
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/rlgnwc API 文档}
 * @param {object} data
 * @param {string} data.type  设备三级分类类型
 */
export const fetchRelate = data =>
  callApi({
    endpoint: 'dccm/model/relate/query',
    options: { method: 'POST', data },
  });

/**
 * 关联型号
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/rlgnwc API 文档}
 * @param {object} data
 * @param {string} data.relateModelList
 */
export const fetchConnectRelate = data =>
  callApi({
    endpoint: 'dccm/model/relate',
    options: { method: 'POST', data },
  });

/**
 * 删除关联
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/rlgnwc API 文档}
 * @param {object} data
 * @param {string} data.deviceType 设备类型
 * @param {string} data.deviceModel 设备型号
 * @param {string} data.spareType 配件类型
 * @param {string} data.spareModel 配件型号
 */
export const fetchDeleteRelate = data =>
  callApi({
    endpoint: 'dccm/model/relate/delete',
    options: { method: 'POST', data },
  });

/**
 * 编辑关联型号
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/rlgnwc API 文档}
 * @param {object} data
 * @param {string} data.deviceType 设备类型
 * @param {string} data.deviceModel 设备型号
 * @param {string} data.spareType 配件类型
 * @param {string} data.relateModels 配件型号
 */
export const fetchUpdateRelate = data =>
  callApi({
    endpoint: 'dccm/model/relate/update',
    options: { method: 'POST', data },
  });
