import { callApi } from './api';

/**
 * @deprecated Use `@manyun/monitoring.service.fetch-points-alarm-count` instead
 * 获取统计后的告警数据
 * 接口文档：https://manyun.yuque.com/ewe5b3/front-end/mf88g6
 * @param {object} params 请求参数
 * @param {string} params.idcTag 机房名称
 * @param {string} [params.blockTag] 楼名称
 * @param {string} [params.roomTag] 包间名称
 * @param {string} [params.gridTag] 机柜名称
 */
export const fetchAlarmStatisticData = ({ idcTag, ...rest }) => {
  return callApi({
    endpoint: `dcim/alarm/count/${idcTag}`,
    options: {
      params: rest,
    },
  });
};
