import axios from 'axios';
import { camelize<PERSON>eys } from 'humps';
import { nanoid } from 'nanoid';
import { normalize } from 'normalizr';

import { message } from '@manyun/base-ui.ui.message';

import { CUSTOMIZED_ERROR_CODE_MAP, DEFAULT_ERROR_MESSAGE_MAP } from '@manyun/service.request';

import { getLocationSearchMap } from '@manyun/dc-brain.legacy.utils';

import {
  handleAuthorizationExpired,
  handleOtherDeviceOnline,
} from '../../dc-brain/utils/handle-passive-logout';

export const axiosInstance = axios.create({
  timeout: 30 * 1000, // ms
});

export const APP_NAME_MAP = {
  PM: 'pm',
};

export const API_ROOT_MAP = {
  development: {
    [APP_NAME_MAP.PM]: '/api/', // setupProxy.js 里有 proxy 配置做反向代理
  },
  production: {
    [APP_NAME_MAP.PM]: '/api/',
  },
}[process.env.NODE_ENV];

/**
 * @typedef { import('axios').AxiosRequestConfig } AxiosRequestConfig
 * @typedef { import('normalizr').Schema } Schema
 */

/**
 * API 请求工具
 * @deprecated Use `@manyun/service.request` instead
 *
 * @param {object} config API 请求配置
 * @param {string} config.appName 服务提供方
 * @param {string} config.endpoint 服务地址
 * @param {{ contentType: string; } & AxiosRequestConfig} config.options Axios 请求配置
 * @param {Schema} [schema] normalizr Schema
 * @param {boolean} [camelize=false] 是否将返回结果中的属性名称转化为驼峰格式
 * @returns {Promise<{ response?: any; filename?: string; error?: string; errorCode?: string }>} 返回 API 请求结果或者错误信息
 */
export function callApi(
  { appName = APP_NAME_MAP.PM, endpoint, options: { contentType, ...axiosOpts } = {} },
  schema,
  camelize = false
) {
  const fullUrl = /^https?:\/\//.test(endpoint) ? endpoint : API_ROOT_MAP[appName] + endpoint;

  if (!axiosOpts.headers) {
    axiosOpts.headers = {};
  }
  if (contentType) {
    axiosOpts.headers['Content-Type'] = contentType;
  }
  axiosOpts.headers['x-app'] = 'Web/DC Base';

  const traceId = nanoid() + Date.now().toString();
  axiosOpts.headers['x-trace-id'] = traceId;

  const { embed, tenantId } = getLocationSearchMap(window.location.search, ['embed', 'tenantId']);
  if (embed && axiosOpts.data && typeof axiosOpts.data == 'object') {
    axiosOpts.data.tenantId = tenantId;
  }

  // https://github.com/axios/axios
  return axiosInstance(fullUrl, axiosOpts)
    .then(response => ({ json: response.data, response }))
    .then(async ({ json, response }) => {
      const statusOk = response.status >= 200 && response.status < 300;
      if (!statusOk) {
        const defaultMsg = DEFAULT_ERROR_MESSAGE_MAP[response.status];
        if (defaultMsg !== undefined) {
          return Promise.reject(new Error(defaultMsg));
        }
        return Promise.reject(json);
      }
      if (json instanceof Blob) {
        if (json.type === 'application/json') {
          const text = await new Response(json).text();
          const resp = JSON.parse(text);
          if (!resp.success) {
            message.destroy();
            message.error(resp.errMessage || '您没有导出权限或导出失败');
            return { response: null };
          }
        }

        /**
         * @type {string}
         */
        const contentDisposition = response.headers['content-disposition'];
        if (contentDisposition && contentDisposition.includes('attachment;filename=')) {
          let [, filename] = contentDisposition.split('filename=');

          // 只要文件名包含 `%`，我们就认为 `filename` 被 encode 过
          if (filename?.includes('%')) {
            try {
              filename = window.decodeURI(filename);
            } catch (error) {
              console.error(error);
            }
          }

          return { response: json, filename };
        }

        return { response: json };
      }

      const { errCode, errMessage, success, data, total, /* redash response */ query_result } =
        json;

      if (query_result) {
        return { response: camelizeKeys(query_result.data) };
      }

      /** 因被动登出导致的请求报错返回值 */
      const passiveLogoutErrorRes = { response: null };

      if (errCode === 'UN_LOGIN') {
        handleAuthorizationExpired();
        return passiveLogoutErrorRes;
      }

      if (errCode === 'FORCE_LOGOUT') {
        handleOtherDeviceOnline();
        return passiveLogoutErrorRes;
      }

      // 无资源权限
      if (errCode === 'NO_AUTHORIZATION') {
        let msg = errMessage;
        try {
          // TODO: @Jerry should update package `@manyun/service.request` as well
          const messageData = JSON.parse(errMessage);
          msg = `无 ${messageData.join(', ')} 资源权限，请联系管理员授权！`;
        } catch (error) {
          // ignored ...
        }
        message.error(msg);

        const err = new Error(json.errMessage);
        err.$$ERROR_CODE = CUSTOMIZED_ERROR_CODE_MAP.UN_AUTHORIZED;

        return Promise.reject(err);
      }

      if (!success) {
        const message = DEFAULT_ERROR_MESSAGE_MAP[errCode] || errMessage;
        const err = new Error(message);
        if (errCode) {
          err.$$ERROR_CODE = errCode;
        }

        return Promise.reject(err);
      }

      let resp = data;
      if (data === undefined && total === undefined) {
        // 前后端格式约定，若未返回 `data,total` 字段，
        // 则认为此次 API 调用的业务操作是成功的。
        resp = true;
      }
      if (typeof total === 'number') {
        // 前后端格式约定，若返回 `total` 字段，
        // 则认为此次 API 调用时查询列表分页数据。
        // 这里将 `data,total` 封装在一个对象里供调用的 service 消费。
        // 如果 `data` 是 null，统一改为 `[]`
        resp = { data: data === null ? [] : data, total };
      }

      if (!schema && !camelize) {
        return { response: resp };
      }

      let newResp = resp;
      if (camelize) {
        newResp = camelizeKeys(newResp);
      }
      if (schema) {
        newResp = normalize(newResp, schema);
      }

      return { response: newResp };
    })
    .then(
      response => response,
      error => {
        if (
          error.isAxiosError &&
          error.code === 'ECONNABORTED' &&
          error.message.startsWith('timeout of')
        ) {
          return {
            error: DEFAULT_ERROR_MESSAGE_MAP[CUSTOMIZED_ERROR_CODE_MAP.TIMEOUT],
            errorCode: CUSTOMIZED_ERROR_CODE_MAP.TIMEOUT,
          };
        }
        if (error.isAxiosError && error.message === 'Network Error') {
          return {
            error: DEFAULT_ERROR_MESSAGE_MAP[CUSTOMIZED_ERROR_CODE_MAP.NETWORK_ERROR],
            errorCode: CUSTOMIZED_ERROR_CODE_MAP.NETWORK_ERROR,
          };
        }
        if (error.isAxiosError && error.response) {
          const defaultMsg = DEFAULT_ERROR_MESSAGE_MAP[error.response.status];
          if (defaultMsg !== undefined) {
            return { error: defaultMsg, errorCode: error.response.status };
          }
        }
        return { error: error.message, errorCode: error.$$ERROR_CODE };
      }
    );
}
