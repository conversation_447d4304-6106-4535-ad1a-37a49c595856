import { callApi } from './api';

/**
 * 查询列表
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/msya75 API 文档}
 * @param {object} params 请求参数
 * @param {string} [params.deviceType] 类型编号
 * @param {string} [params.deviceTypeName] 类型名称
 * @param {string} params.pageNum 页码
 * @param {string} params.pageSize 页数
 */
export const getDeviceTypeList = ({ deviceType, deviceTypeName, pageNum, pageSize }) => {
  return callApi({
    endpoint: 'dccm/device/type/query',
    options: {
      method: 'POST',
      data: { deviceType, deviceTypeName, pageNum, pageSize },
    },
  });
};

/**
 * 查询详情
 * @deprecated use fetchDeviceType('@manyun/resource-hub.service.fetch-device-type') instead
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/ron9py API 文档}
 *  @param {string} id
 */
export const getDeviceTypeDetail = data => {
  return callApi({
    endpoint: 'dccm/device/type/query/detail',
    options: {
      params: data,
    },
  });
};

/**
 * 新增设备类型
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/bmr2nk API 文档}
 * @param {object} params 请求参数
 * @param {string} params.name 类型名称
 * @param {string} params.type 所属级别
 * @param {string} params.code 类型编号
 * @param {string} params.parentCode 上级类型code
 * @param {string} params.parentType 上级类型级别
 * @param {string} [params.description] 备注
 */
export const addDeviceType = data =>
  callApi({
    endpoint: 'dccm/device/type/add',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 编辑设备类型
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/vb57sw API 文档}
 * @param {object} params 请求参数
 * @param {string} params.id
 * @param {string} params.name
 * @param {string} params.code
 * @param {string} [params.description]
 */
export const editDeviceType = data =>
  callApi({
    endpoint: 'dccm/device/type/update',
    options: {
      method: 'POST',
      data,
    },
  });

//删除
export const deleteDeviceType = data =>
  callApi({
    endpoint: 'dccm/device/type/delete',
    options: {
      params: data,
    },
  });
