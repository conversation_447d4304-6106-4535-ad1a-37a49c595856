import { callApi } from './api';

export const fetchMonitoringType = () =>
  callApi({
    endpoint: 'dccm/grid/export',
    options: {
      method: 'POST',
      responseType: 'blob',
      data: {},
    },
  });

export const fetchProcessingStateList = async () => {
  const data = await callApi({
    endpoint: 'dcim/alarm/get/alarm/status',
    options: {
      method: 'GET',
    },
  });
  if (data.response) {
    const list = [];
    for (let i in data.response) {
      list.push({ name: data.response[i], code: i });
    }
    return list;
  }
};

/**
 * @deprecated Should be removed when v20220630 released
 *
 * @param {*} alarmId
 * @param {*} idcTag
 * @returns
 */
export const confirmAlarm = (alarmId, idcTag) =>
  callApi({
    endpoint: 'dcim/alarm/confirm',
    options: {
      method: 'GET',
      params: {
        alarmId,
        idcTag,
      },
    },
  });

/**
 * @deprecated Should be removed when v20220630 released
 *
 * @param {*} param0
 * @returns
 */
export const batchConfirm = ({ alarmIds, idcTag }) =>
  callApi({
    endpoint: 'dcim/alarm/batch/confirm',
    options: {
      method: 'POST',
      // contentType: 'application/json',
      data: { alarmIds, idcTag },
      // data: `alarmIds=${alarmIds}`,
    },
  });

export const alarmDelete = (alarmId, idcTag) =>
  callApi({
    endpoint: 'dcim/alarm/delete',
    options: {
      method: 'GET',
      params: {
        alarmId,
        idcTag,
      },
    },
  });

export const createEventConfirm = data =>
  callApi({
    endpoint: 'dcom/event/create',
    options: {
      method: 'POST',
      data,
    },
  });

export const createAlarmAssociateEvent = data =>
  callApi({
    endpoint: 'dcim/alarm/associate/event',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * @deprecated 需要重构到 dccmService 里去
 */
export const fetchMetaCategory = () =>
  callApi({
    endpoint: 'dccm/meta/query/device/category',
    options: {
      method: 'POST',
      data: {
        numbered: true,
      },
    },
  });

// 客户想象列表
export const customerList = roomGuid =>
  callApi({
    endpoint: 'dccm/view/room/customer/query',
    options: {
      method: 'GET',
      params: {
        roomGuid,
      },
    },
  });

// 事件类型
export const createEventTypeList = () =>
  callApi({
    endpoint: 'dccm/view/room/customer/query',
    options: {
      method: 'GET',
      // params: {
      //   roomGuid,
      // },
    },
  });

// 详情
export const alarmDetail = data =>
  callApi({
    endpoint: 'dcim/alarm/query/detail',
    options: {
      method: 'GET',
      params: data,
    },
  });

// 告警类型
export const fetchAlarmTypeLsit = () =>
  callApi({
    endpoint: 'dccm/get/alarm/type',
    options: {
      method: 'GET',
    },
  });

export const fetchRoomList = idc =>
  callApi({
    endpoint: `dccm/query/room/by/${idc}`,
  });
/**
 * @deprecated 后续请相关scope owner 重构该接口
 */
export const fetchTriggerStatus = () =>
  callApi({
    endpoint: `dcim/alarm/get/trigger/status`,
  });

/**
 * 查询告警处理状态数量
 * 接口文档：：https://manyun.yuque.com/ewe5b3/sbs6q1/sca5ss
 * @param {object} data 请求参数
 * @param {string} [data.idcTag] 机房
 * @param {Array} [data.blockTags] 楼栋
 * @param {Array} [data.roomTags] 包间
 * @param {string} [data.alarmType] 告警类型
 * @param {string} [data.confirmByName] 处理人名称
 * @param {Array} [data.triggerStatus] 告警状态
 * @param {Array} [data.alarmLevel] 告警级别
 * @param {string} [data.notifyContent] 告警内容
 * @param {Array} [data.alarmStatus] 处理状态
 * @param {Array} [data.alarmCreateTimeStart] 告警开始时间
 **/

export const fetchQueryAlarmStatusCount = data => {
  return callApi({
    endpoint: 'dcim/alarm/query/alarm/status/count',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data,
    },
  });
};

/**
 * @deprecated Use `@manyun/resource-hub.service.fetch-blocks-by-permission` instead
 * 查询有权限的楼栋接口
 * 接口文档：https://manyun.yuque.com/ewe5b3/sbs6q1/dhnug8
 * @param {object} data 请求参数
 * @param {string} [data.idcTag] 机房
 **/

export const fetchBlockByPermission = data => {
  return callApi({
    endpoint: 'dccm/query/block/by/permission',
    options: {
      params: { ...data },
    },
  });
};

/**
 * @deprecated Use `@manyun/resource-hub.service.fetch-rooms-by-block` instead
 * 根据楼栋查询包间接口
 * 接口文档：https://manyun.yuque.com/ewe5b3/sbs6q1/dhnug8
 * @param {object} data 请求参数
 * @param {string} [data.idcTag] 机房
 * @param {Array} [data.blockTags] 楼栋
 **/

export const fetchRoomByPermission = data => {
  return callApi({
    endpoint: 'dccm/query/room/by/block/list',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data,
    },
  });
};

/**
 * @deprecated Use `@manyun/ticket.service.connect-event-with-alarms` instead
 *
 * 关联事件
 * 接口文档：https://manyun.yuque.com/ewe5b3/pkinbk/em7xgz
 * @param {object} data 请求参数
 * @param {string} [data.idcTag] 机房
 * @param {Array} [data.alarmIds] 告警Id
 * @param {number} [data.id] 事件Id
 **/

export const alarmAssociateEvent = data => {
  return callApi({
    endpoint: 'dcom/event/eventAssociateAlarms',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 批量下盯屏
 * 接口文档：https://manyun.yuque.com/ewe5b3/pkinbk/uhe74q
 * @param {object} data 请求参数
 **/
export const fetchAlarmBatchRemove = data =>
  callApi({
    endpoint: 'dcim/alarm/batch/remove',
    options: {
      method: 'POST',
      data,
    },
  });
