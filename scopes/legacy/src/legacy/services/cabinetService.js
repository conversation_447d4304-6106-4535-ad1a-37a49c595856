import { callApi } from './api';

/**
 * 查询机柜列表
 *
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/rzu8mg API 文档}
 * @param {*} data
 */
export const fetchGrids = ({ gridType, powerStatus, ...data } = {}) =>
  callApi({
    endpoint: 'dccm/grid/query',
    options: {
      method: 'POST',
      data: {
        ...data,
        gridTypeList: data.gridTypeList ? data.gridTypeList : gridType ? [gridType] : null,
        powerStatusList: data.powerStatusList
          ? data.powerStatusList
          : powerStatus
          ? [powerStatus]
          : null,
      },
    },
  });

// 导入
export const uploadCabinet = data =>
  callApi({
    endpoint: 'dccm/grid/import',
    options: {
      method: 'POST',
      data,
      timeout: 5 * 60 * 1000,
    },
  });

/**
 * 更新机柜信息
 * @param {object} data
 */
export const updateCabinet = data =>
  callApi({
    endpoint: 'dccm/grid/update',
    options: {
      method: 'POST',
      data,
    },
  });

// 下载模板
export const downloadGridModel = () =>
  callApi({
    endpoint: 'dccm/grid/download',
    options: {
      method: 'GET',
      params: {},
      responseType: 'blob',
    },
  });

// 导出列表
export const downloadCabinetList = ({ gridType, powerStatus, ...data } = {}) =>
  callApi({
    endpoint: 'dccm/grid/export',
    options: {
      method: 'POST',
      responseType: 'blob',
      contentType: 'application/json',
      data: {
        ...data,
        gridTypeList: data.gridTypeList ? data.gridTypeList : gridType ? [gridType] : null,
        powerStatusList: data.powerStatusList
          ? data.powerStatusList
          : powerStatus
          ? [powerStatus]
          : null,
      },
    },
  });

// 上电状态
export const fetchPowerStatusListe = async () => {
  const { response, error } = await callApi({
    endpoint: 'dccm/get/power/status',
    options: {
      method: 'GET',
      contentType: 'application/json',
      data: {},
    },
  });
  let newList = [];
  if (error) {
    return;
  }
  if (response) {
    Object.keys(response).map(key => {
      newList.push({ key: key, value: response[key] });

      return newList;
    });
    return newList;
  }
  return newList;
};

/**
 * @deprecated Use `@manyun/resource-hub.service.fetch-cabinet-type` instead
 *  机柜类型
 * @returns
 */
export const fetchCabinetType = async () => {
  const { response, error } = await callApi({
    endpoint: 'dccm/get/grid/type',
    options: {
      method: 'GET',
      contentType: 'application/json',
      data: {},
    },
  });
  let newList = [];
  if (error) {
    return;
  }
  if (response) {
    Object.keys(response).map(key => {
      newList.push({ key: key, value: response[key] });

      return newList;
    });
    return newList;
  }
  return newList;
};

/**
 * 根据guid查询机柜信息接口
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/heirar API 文档}
 * @param {object} params 请求参数
 * @param {string} params.gridGuid
 */
export const fetchCabinetDetail = data =>
  callApi({
    endpoint: 'dccm/grid/query/detail',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 批量更新机柜（机柜类型、厂商、U数、机柜尺寸、投产日期、供电模式）
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/fkvq3e API 文档}
 * @param {object} data
 * @param {string} [data.gridType] 机柜类型
 * @param {string} [data.vendor] 厂商
 * @param {string} [data.unitCount] U数
 * @param {string} [data.gridLength] 长
 * @param {string} [data.gridWidth] 宽
 * @param {string} [data.gridHeight] 高
 * @param {string} [data.operationTime] 投产日期
 * @param {string} [data.powerModel] 供电模式
 * @param {string} [data.ids] 机柜id
 * @param {string} [data.operatorNotes] 操作备注
 */
export const batchUpdateCabinet = data =>
  callApi({
    endpoint: 'dccm/grid/batch/update',
    options: {
      method: 'POST',
      data,
    },
  });
