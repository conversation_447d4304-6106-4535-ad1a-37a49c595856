import { callApi } from './api';

/**
 * 查询订单列表
 * {@link https://manyun.yuque.com/ewe5b3/pkinbk/zzl7gv API 文档}
 * @param {object} data
 * @param {string} [data.idc] 机房
 * @param {string} [data.deviceName] 设备名称
 * @param {string} [data.pointName] 测点名称
 * @param {string} [data.alarmLevel] 告警等级
 * @param {string} [data.alarmStatus] 告警状态
 * @param {number} [data.alarmStartTime] 告警开始时间
 * @param {number} [data.alarmEndTime] 告警结束时间
 * @param {number} data.pageNum
 * @param {number} data.pageSize
 */
export const fetchAlarmTransmissionRecordList = data => {
  return callApi({
    endpoint: 'dcim/alarm/offline/list',
    options: {
      method: 'POST',
      data,
    },
  });
};
