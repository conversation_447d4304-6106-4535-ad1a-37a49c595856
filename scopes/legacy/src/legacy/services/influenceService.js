import { callApi } from './api';

/**
 * @deprecated Use `@manyun/resource-hub.service.fetch-influence` instead
 * 实际影响面计算
 * 接口文档：https://manyun.yuque.com/ewe5b3/pkinbk/yypcvo
 */
export const fetchInfluence = data =>
  callApi({
    endpoint: 'dcim/alarm/query/influence',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * @deprecated Use `@manyun/resource-hub.service.fetch-budget-influence` instead
 * 预算影响面计算
 * 接口文档：https://manyun.yuque.com/ewe5b3/pkinbk/pdetwy
 */
export const fetchBudgetInfluence = data =>
  callApi({
    endpoint: 'dcim/alarm/query/estimate/influence',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * @deprecated Use `@manyun/resource-hub.service.fetch-facility-count` instead
 * 查询楼下设备机柜客户数
 * {@link https://manyun.yuque.com/ewe5b3/pkinbk/vb18lt API 文档}
 * @param {string} idcTag 机房
 * @param {string} blockTag 楼栋
 */
export const fetchFacilityCount = data =>
  callApi({
    endpoint: `dccm/view/query/facility/count`,
    options: {
      params: data,
    },
  });
