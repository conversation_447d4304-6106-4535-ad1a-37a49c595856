import { callApi } from './api';

/**
 * 查询日志信息
 * {@link https://manyun.yuque.com/ewe5b3/pkinbk/mhmth1 API 文档}
 * @param {object} data
 * @param {number} [data.id] 日志ID
 * @param {number} [data.targetId] 操作对象ID
 * @param {string} [data.modifyType] 操作类型
 * @param {string} [data.targetType] 操作模块
 * @param {string} [data.operatorName] 操作人
 * @param {string} [data.operatorTimeStart] 操作开始时间
 * @param {string} [data.operatorTimeEnd] 操作结束时间
 * @param {number} data.pageNum
 * @param {number} data.pageSize
 */
export const fetchLogList = data => {
  return callApi({
    endpoint: 'dcom/modify/query/list',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 获取操作模块
 * 接口文档：https://manyun.yuque.com/ewe5b3/pkinbk/spedgh
 */
export const fetchTargetType = async () => {
  const { response } = await callApi({
    endpoint: 'dcom/modify/get/target/type',
  });
  let newList = [];
  if (response) {
    Object.keys(response).map(item => {
      newList.push({ key: item, value: response[item] });
      return newList;
    });
  }
  return newList;
};
