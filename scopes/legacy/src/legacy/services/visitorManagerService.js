import { callApi } from './api';

/**
 * 查询人员入室申请记录
 * @deprecated Use `@manyun/sentry.service.fetch-paged-visits-records` instead
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/oqi0yb API 文档}
 * @param {object} data
 * @param {string} [data.name] 名称
 * @param {string} [data.identityNo] 证件号
 * @param {string} [data.idcTag] 证件号
 * @param {string} [data.enterStartTime] 入室开始时间
 * @param {string} [data.enterEndTime] 入室结束时间
 * @param {string} [data.leaveStartTime] 离开开始时间
 * @param {string} [data.leaveEndTime] 离开结束时间
 * @param {string} [data.entryStatus] 入室状态
 * @param {number} data.pageNum
 * @param {number} data.pageSize
 */
export const fetchVisitorRecordPage = data =>
  callApi({
    endpoint: 'taskcenter/visitor/record/list',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data,
    },
  });

/**
 * 创建人员入室申请记录
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/oqi0yb API 文档}
 * @param {object} data
 * @param {string} [data.idcTag] 机房
 * @param {string} [data.blockGuid] 楼栋（可选）
 * @param {time} [data.registerTime] 入室时间
 * @param {list} [data.visitorInfos] 入室人员
 */
export const fetchVisitorRecordCreate = data =>
  callApi({
    endpoint: 'taskcenter/visitor/record/create',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data,
    },
  });

/**
 * 查询符合条件的人员入室申请
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/oqi0yb API 文档}
 * @param {object} data
 * @param {string} [data.name] 名称
 * @param {string} [data.identityNo] 证件号
 * @param {string} [data.idcTag] 机房
 * @param {string} [data.registerTime] 入室时间
 */
export const fetchVisitorStaffList = data =>
  callApi({
    endpoint: 'taskcenter/visitor/staff/list',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data,
    },
  });

/**
 * 分页查询人员入室申请工单下人员
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/afmf9t#1u63j API 文档}
 * @param {object} data
 * @param {string} data.relateTaskNo 工单号
 * @param {string} data.name 人员名称
 * @param {string} data.identityNo 身份证号
 * @param {string} data.companyName 公司名称
 * @param {datatime} data.operable 是否具备操作权限
 */
export const fetchVisitorDistinctStaffList = data =>
  callApi({
    endpoint: 'taskcenter/visitor/distinct/staff/list',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 查询黑名单列表
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/oqi0yb API 文档}
 * @param {object} data
 * @param {string} [data.name] 名称
 * @param {string} [data.identityNo] 证件号
 * @param {string} [data.certificateType] 证件类型
 * @param {string} [data.sex] 性别
 * @param {number} data.pageNum
 * @param {number} data.pageSize
 */
export const fetchVisitorBlacklistPage = data =>
  callApi({
    endpoint: 'taskcenter/visitor/blacklist/list',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data,
    },
  });

/**
 * 移除黑名单
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/oqi0yb API 文档}
 * @param {object} data
 * @param {list} [data.ids] ids
 */
export const fetchBatchRemoveVisitorBlacklist = data =>
  callApi({
    endpoint: 'taskcenter/visitor/blacklist/batch/remove',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data,
    },
  });

/**
 * 批量添加黑名单
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/oqi0yb API 文档}
 * @param {object} data
 * @param {list} [data.ids] ids
 */
export const fetchAddVisitorBlacklist = data =>
  callApi({
    endpoint: 'taskcenter/visitor/blacklist/create',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data,
    },
  });

/**
 * 添加黑名单
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/oqi0yb API 文档}
 * @param {object} data
 * @param {list} [data.id] id
 */
export const fetchCreateVisitorBlacklist = data =>
  callApi({
    endpoint: 'taskcenter/visitor/blacklist/add',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data,
    },
  });
