import moment from 'moment';

import { callApi } from './api';

/**
 * 按照条件分页查询事件信息接口
 * 接口文档：https://manyun.yuque.com/ewe5b3/pkinbk/wotnar
 * @deprecated 请使用 ticket scope下的fetch-events组件
 * @param {object} data 请求参数
 * @param {Array} [data.idcTags] 机房
 * @param {Array} [data.blockTags] 实际要传 `blockGuid` 的集合
 * @param {string} [data.occurBeginTime] 事件开始时间
 * @param {string} [data.occurEndTime] 事件结束时间
 * @param {string} [data.eventId] 事件ID
 * @param {string} [data.eventLevel] 事件级别
 * @param {string} [data.eventSource] 事件来源
 * @param {string[]} [data.eventStatus] 事件状态
 * @param {string} [data.ownerId] 事件负责人
 * @param {string} [data.topCategory] 事件大类
 * @param {string} [data.secondCategory] 事件小类
 * @param {string} [data.key] 事件描述关键字
 * @param {number} [data.pageNum] 第几页
 * @param {number} [data.pageSize] 每页条数
 **/
export const fetchEventList = data =>
  callApi({
    endpoint: 'dcom/event/page',
    options: {
      method: 'POST',
      data: {
        ...data,
        createBeginTime: data.createBeginTime
          ? Number(moment(data.createBeginTime).unix() + '000')
          : undefined,
        createEndTime: data.createEndTime
          ? Number(moment(data.createEndTime).unix() + '000')
          : undefined,
      },
    },
  });

/**
 * 分页查询事件进展接口
 * 接口文档：https://manyun.yuque.com/ewe5b3/pkinbk/gpzegq
 * @param {object} data 请求参数
 * @param {string} [data.eventId] 事件ID
 * @param {string} [data.pageNum] 当前页数
 * @param {string} [data.pageSize] 每页大小
 **/
export const fetchProgressUpdateList = data => {
  return callApi({
    endpoint: 'dcom/eventOptLog/page',
    options: {
      params: { ...data },
    },
  });
};

/**
 * 根据楼栋查询包间接口
 * 接口文档：https://manyun.yuque.com/ewe5b3/sbs6q1/dhnug8
 * @param {object} data 请求参数
 * @param {string} [data.idcTag] 机房
 * @param {Array} [data.blockTags] 楼栋
 **/
export const fetchRoomByPermission = data => {
  return callApi({
    endpoint: 'dccm/query/room/by/block/list',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data,
    },
  });
};

/**
 * 根据id查询事件详情接口
 * 接口文档：https://manyun.yuque.com/ewe5b3/pkinbk/mbf3lg
 * @param {object} data 请求参数
 * @param {string} [data.id] 事件id
 **/
export const fetchEventDetail = id =>
  callApi({
    endpoint: `dcom/event/detail/${id}`,
  });

/**
 * 事件缓解接口
 * 接口文档：https://manyun.yuque.com/ewe5b3/pkinbk/awkals
 * @param {object} data 请求参数
 * @param {number} [data.eventId] 事件id
 * @param {number} [data.relieveTime] 缓解时间
 * @param {string} [data.relieveDesc] 缓解内容
 * @param {number} [data.relieveUserId] 缓解的用户id
 **/
export const fetchEventRelieve = data => {
  return callApi({
    endpoint: `dcom/event/relieve`,
    options: {
      method: 'POST',
      data,
    },
  });
};

export const fetchEventUpgradeLogCreate = data =>
  callApi({
    endpoint: `dcom/eventUpgradeLog/create`,
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 添加时间进展（操作）接口
 * 接口文档：https://manyun.yuque.com/ewe5b3/pkinbk/di46m4
 * @param {object} data 请求参数
 * @param {number} [data.eventId] 事件id
 * @param {number} [data.relieveTime] 缓解时间
 * @param {string} [data.optContent] 缓解内容
 * @param {string} [data.optPlan] 后续计划
 **/
export const fetchEventOptLogCreate = data =>
  callApi({
    endpoint: `dcom/eventOptLog/create`,
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 事件解决
 * 接口文档：https://manyun.yuque.com/ewe5b3/pkinbk/zafszl
 * @param {object} data 请求参数
 * @param {number} [data.eventId] 事件id
 * @param {number} [data.resolveTime] 解决时间
 * @param {string} [data.resolveDesc] 解决内容
 * @param {number} [data.resolveUserId] 解决的用户id
 **/
export const fetchEventResolve = data =>
  callApi({
    endpoint: `dcom/event/resolve`,
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 新增通报
 * 接口文档：https://manyun.yuque.com/ewe5b3/pkinbk/zafszl
 * @param {object} data 请求参数
 * @param {number} [data.eventId] 事件id
 **/
export const fetchEventReportLogCreate = data =>
  callApi({
    endpoint: `dcom/eventReportLog/create`,
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 查询事件时间线记录
 * 接口文档：https://manyun.yuque.com/ewe5b3/pkinbk/zc02hl
 * @param {object} data 请求参数
 * @param {string} [data.id] 事件id
 **/
export const fetchEventLife = id =>
  callApi({
    endpoint: `dcom/event/timeLine/${id}`,
  });

/**
 * 分页查询事件通报接口
 * 接口文档：https://manyun.yuque.com/ewe5b3/pkinbk/gezyhn
 * @param {object} data 请求参数
 * @param {Array} [data.eventId] 事件ID
 * @param {Array} [data.pageNum] 页数
 * @param {string} [data.pageSize] 每页大小
 **/
export const fetchEventNotificationList = data =>
  callApi({
    endpoint: 'dcom/eventReportLog/page',
    options: {
      params: data,
    },
  });

/**
 * 通过事件ID请求影响面
 * 接口文档：https://manyun.yuque.com/ewe5b3/pkinbk/gezyhn
 * @param {object} data 请求参数
 * @param {Array} [data.eventId] 事件ID
 **/
export const getInfluenceSurface = data =>
  callApi({
    endpoint: 'dcim/alarm/query/influence/count',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * @deprecated 后续请相关scope owner 进行接口重构
 * 查询不同类型告警数量
 * 接口文档：https://manyun.yuque.com/ewe5b3/pkinbk/npht4t
 * @param {object} data 请求参数
 * @param {string} [data.id] 事件id
 * @param {string} [data.idcTag] 机房
 * @param {string} [data.blockTag] 楼栋
 **/
export const fetchEventAlarmQueryCount = data =>
  callApi({
    endpoint: `dcim/alarm/query/recover/status/count`,
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 分页查询事件通报接口
 * 接口文档：https://manyun.yuque.com/ewe5b3/pkinbk/gezyhn
 * @param {object} data 请求参数
 * @param {Array} [data.eventId] 事件ID
 * @param {Array} [data.pageNum] 页数
 * @param {string} [data.pageSize] 每页大小
 **/
export const fetchEventUpgradeLogList = data =>
  callApi({
    endpoint: 'dcom/eventUpgradeLog/page',
    options: {
      params: data,
    },
  });

/**
 * 用户升级详情
 * 接口文档：https://manyun.yuque.com/ewe5b3/pkinbk/vi17hz
 * @param {object} data 请求参数
 * @param {Array} [data.eventId] 事件ID
 * @param {Array} [data.pageNum] 页数
 * @param {string} [data.pageSize] 每页大小
 **/
export const fetchEventUpgradeLogDetail = data =>
  callApi({
    endpoint: 'dcom/eventUpgradeLog/detail',
    options: {
      params: data,
    },
  });

/**
 * 影响面计算
 * 接口文档：https://manyun.yuque.com/ewe5b3/pkinbk/yypcvo
 * @param {object} data 请求参数
 * @param {number} [data.idcTag] 机房
 * @param {number} [data.blockTag] 楼栋
 * @param {number} [data.targetId] 事件ID
 * @param {number} [data.targetType] 事件类型
 **/
export const fetchEventInfluence = data =>
  callApi({
    endpoint: 'dcim/alarm/query/influence',
    options: {
      params: data,
    },
  });

/**
 * 查询有权限的机房
 * 接口文档：https://manyun.yuque.com/ewe5b3/sbs6q1/ootoa3
 **/
export const fetchQueryIdcByPermission = () =>
  callApi({
    endpoint: 'dccm/query/idc/by/permission',
  });

/**
 * 添加事件信息
 * 接口文档：https://manyun.yuque.com/ewe5b3/pkinbk/dkbryy
 **/
export const fetchEventCreate = data =>
  callApi({
    endpoint: 'dcom/event/create',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 更新事件信息
 * 接口文档：https://manyun.yuque.com/ewe5b3/pkinbk/dkbryy
 **/
export const fetchEventUpdate = data =>
  callApi({
    endpoint: 'dcom/event/update',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 上传文档
 * 接口文档：https://manyun.yuque.com/ewe5b3/pkinbk/ao63vs
 **/
export const uploadFile = data =>
  callApi({
    endpoint: 'dcom/file/before/upload',
    options: {
      method: 'POST',
      data,
      timeout: 3 * 60 * 1000,
    },
  });

/**
 * 查询事件状态接口
 * 接口文档：https://manyun.yuque.com/ewe5b3/pkinbk/xsmebu
 **/
export const fetchEventStatus = () =>
  callApi({
    endpoint: 'dcom/event/eventStatus',
  });

/**
 * 事件评审
 * 接口文档：https://manyun.yuque.com/ewe5b3/pkinbk/qds93d
 **/
export const fetchEventAudit = data =>
  callApi({
    endpoint: 'dcom/event/audit',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 事件评审取消
 * 接口文档：https://manyun.yuque.com/ewe5b3/pkinbk/qds93d
 * @param {object} data 请求参数
 * @param {number} [data.eventId] 事件ID
 **/
export const fetchEventRelieveCancel = data =>
  callApi({
    endpoint: 'dcom/event/relieveCancel',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 事件解决取消接口
 * 接口文档：https://manyun.yuque.com/ewe5b3/pkinbk/ffg6gw
 * @param {object} data 请求参数
 * @param {number} [data.eventId] 事件ID
 **/
export const fetchEventResolveCancel = data =>
  callApi({
    endpoint: 'dcom/event/resolveCancel',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 事件解决取消接口
 * 接口文档：https://manyun.yuque.com/ewe5b3/pkinbk/dd46zw
 * @param {object} data 请求参数
 * @param {number} [data.eventId] 事件ID
 **/
export const fetchEventAuditCancel = data =>
  callApi({
    endpoint: 'dcom/event/auditCancel',
    options: {
      method: 'POST',
      data,
      // contentType: 'application/x-www-form-urlencoded',
    },
  });

/**
 * 下载文档
 * 接口文档：https://manyun.yuque.com/ewe5b3/pkinbk/gwdqsd
 * @param {object} data 请求参数
 * @param {string} [data.filePath] 文件路径
 **/
export const downloadFile = data =>
  callApi({
    endpoint: 'dcom/file/download',

    options: {
      params: { filePath: data },
      responseType: 'blob',
    },
  });

/**
 * 操作记录
 * 接口文档：https://manyun.yuque.com/ewe5b3/pkinbk/gwdqsd
 * @param {object} data 请求参数
 **/
export const fetchModifyList = data =>
  callApi({
    endpoint: 'dcom/modify/query/list',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 事件关闭
 * 接口文档：https://manyun.yuque.com/ewe5b3/pkinbk/zczn2h
 * @param {object} data 请求参数
 **/
export const fetchEventClose = data =>
  callApi({
    endpoint: 'dcom/event/close',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 模糊查询角色
 * 接口文档：http://121.40.158.1:8080/swagger-ui.html#/
 * @param {object} data 请求参数
 **/
export const fetchFullRoleList = data =>
  callApi({
    endpoint: 'pm/role/fullRoleList',
    options: {
      params: data,
    },
  });

/**
 * 开启
 * 接口文档：https://manyun.yuque.com/ewe5b3/pkinbk/svyut7
 * @param {object} data 请求参数
 **/
export const fetchEventOpen = data =>
  callApi({
    endpoint: 'dcom/event/open',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * @deprecated 后续请相关scope owner 重构
 * 取消关联
 * 接口文档：https://manyun.yuque.com/ewe5b3/pkinbk/tgyfkx
 * @param {object} data 请求参数
 **/
export const fetchAlarmRemoveAssociateEvent = data =>
  callApi({
    endpoint: 'dcim/alarm/remove/associate/event',
    options: {
      method: 'POST',
      data,
    },
  });

// /**
//  * 缓解之后批量下盯屏
//  * 接口文档：https://manyun.yuque.com/ewe5b3/pkinbk/uhe74q
//  * @param {object} data 请求参数
//  **/
// export const fetchAlarmBatchRemove = data =>
//   callApi({
//     endpoint: 'dcim/alarm/batch/remove',
//     options: {
//       method: 'POST',
//       data,
//     },
//   });

// 导出
export const eventExport = params =>
  callApi({
    endpoint: 'dcom/event/export',
    options: {
      method: 'POST',
      responseType: 'blob',
      contentType: 'application/json',
      data: params,
    },
  });

/**
 * 校验告警状态
 * 接口文档：https://manyun.yuque.com/ewe5b3/pkinbk/bm50e5
 * @param {object} data 请求参数
 **/
export const checkTriggerStatus = data =>
  callApi({
    endpoint: 'dcim/alarm/check/trigger/status',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 *  查询过滤掉已建单的事件的列表
 * @param {object} data 请求参数
 **/
export const fetchAssociatePage = data =>
  callApi({
    endpoint: 'dcom/event/associatePage',
    options: {
      method: 'POST',
      data,
    },
  });
