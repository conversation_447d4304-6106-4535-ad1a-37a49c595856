import { callApi } from './api';

/**
 * 借用单列表
 * {@link https://manyun.yuque.com/ewe5b3/bnpnl9/vvo1ms#MIOeg API 文档}
 * @param {object} data
 * @param {string} [data.borrowNo] 借用单号
 * @param {string} [data.title] 标题
 * @param {string} [data.blockGuid] 楼guid
 * @param {string} [data.borrowerName] 借用人
 * @param {string} [data.borrower] 借用人ID
 * @param {string} [data.creatorId] 创建人ID
 * @param {string} [data.createStartTime] 创建开始日期
 * @param {string} [data.createEndTime] 创建截止日期
 * @param {string} [data.borrowStartTime] 借用开始日期
 * @param {string} [data.borrowEndTime] 借用截止日期
 * @param {Array} [data.borrowStatusList] 借用状态
 * @param {string} data.pageNum
 * @param {Array} data.pageSize
 */
export const fetchBorrowsAndReturnList = data => {
  return callApi({
    endpoint: 'dccm/borrow/list',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 转借
 * {@link https://manyun.yuque.com/ewe5b3/bnpnl9/vvo1ms#JOtLy API 文档}
 * @param {object} data
 * @param {string} [data.opId] 操作ID
 * @param {string} data.borrowNo 借用单号
 * @param {string} data.borrowType 借用类型
 * @param {string} data.borrowerType 借用人类型
 * @param {string} data.borrowerName 借用人
 * @param {string} data.borrower 借用人ID
 * @param {string} data.endDate 创建开始日期
 * @param {string} data.createEndTime 借用结束日期
 * @param {string} data.reason 原因
 * @param {string} data.personLiable
 * @param {string} data.personLiableName
 */
export const fetchLending = data => {
  return callApi({
    endpoint: 'dccm/borrow/asset/transfer',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 续借
 * {@link https://manyun.yuque.com/ewe5b3/bnpnl9/vvo1ms#ndL9A API 文档}
 * @param {object} data
 * @param {string} [data.opId] 操作ID
 * @param {string} data.endDate 续借截止日期
 * @param {string} data.borrowNo 借用单号
 * @param {string} [data.reason] 原因
 */
export const fetchRenewing = data => {
  return callApi({
    endpoint: 'dccm/borrow/asset/renew',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 可归还资产查询
 * {@link https://manyun.yuque.com/ewe5b3/bnpnl9/vvo1ms#r6K9L API 文档}
 * @param {object} data
 * @param {string} data.borrowNo 借用单号
 * @param {string} [data.assetNo] 资产编号
 * @param {string} [data.deviceType] 三级分类
 * @param {string} [data.numbered]
 */
export const fetchEnableReturn = data => {
  return callApi({
    endpoint: 'dccm/borrow/asset/can/return/list',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 归还
 * {@link https://manyun.yuque.com/ewe5b3/bnpnl9/vvo1ms#kjGOh API 文档}
 * @param {object} data
 * @param {string} data.borrowNo 借用单号
 * @param {string} data.returnRoomGuid 归还房间guid
 * @param {string} data.returnRoomTag 归还房间tag
 * @param {string} data.remark 备注
 * @param {Array} data.returnAssertInfoList 归还的资产
 */
export const fetchReturnAssert = data => {
  return callApi({
    endpoint: 'dccm/borrow/asset/return',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 新建借用单
 * {@link https://manyun.yuque.com/ewe5b3/bnpnl9/vvo1ms#Yl0fH API 文档}
 * @param {object} data
 * @param {string} data.title 标题
 * @param {string} data.idcTag 机房
 * @param {string} data.blockGuid 楼guid
 * @param {string} data.borrowType 借用类型
 * @param {string} data.borrowerType 借用类型
 * @param {string} data.borrower 借用人ID
 * @param {string} data.borrowerName 借用人名称
 * @param {string} data.personLiable 负责人ID
 * @param {string} data.personLiableName 负责人名称
 * @param {string} data.borrowStartDate 借用开始日期
 * @param {string} data.borrowEndDate 借用截止日期
 * @param {string} data.reason 借用原因
 * @param {Array} data.borrowAssetInfoList
 */
export const fetchCreateBorrow = data => {
  return callApi({
    endpoint: 'dccm/borrow/create',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 借用单详情
 * {@link https://manyun.yuque.com/ewe5b3/bnpnl9/vvo1ms#eWDEA API 文档}
 * @param {object} data
 * @param {string} data.borrowNo 借用单号
 */
export const fetchBorrowInfo = data => {
  return callApi({
    endpoint: 'dccm/borrow/detail',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 借用申请列表
 * {@link https://manyun.yuque.com/ewe5b3/bnpnl9/vvo1ms#F6dVU API 文档}
 * @param {object} data
 * @param {string} data.borrowNo 借用单号
 */
export const fetchBorrowApply = data => {
  return callApi({
    endpoint: 'dccm/borrow/apply/list',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 借用设备明细
 * {@link https://manyun.yuque.com/ewe5b3/bnpnl9/vvo1ms#WfxfS API 文档}
 * @param {object} data
 * @param {string} data.borrowNo 借用单号
 */
export const fetchBorrowAndReturnAssertInfo = data => {
  return callApi({
    endpoint: 'dccm/borrow/asset/list',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 归还记录
 * {@link https://manyun.yuque.com/ewe5b3/bnpnl9/vvo1ms#CP9c3 API 文档}
 * @param {object} data
 * @param {string} data.relateTaskNo 借用单号
 * @param {string} data.relateBizType 关联单号类型
 */
export const fetchReturnRecords = data => {
  return callApi({
    endpoint: 'taskcenter/warehouse/inWarehouse/device/relateList',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 续转记录
 * {@link https://manyun.yuque.com/ewe5b3/bnpnl9/vvo1ms#Bx4qC API 文档}
 * @param {object} data
 * @param {string} data.borrowNo 借用单号
 */
export const fetchRenewlendRecords = data => {
  return callApi({
    endpoint: 'dccm/borrow/oplog/list',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 关联出入库工单查询
 * {@link https://manyun.yuque.com/ewe5b3/bnpnl9/vvo1ms#m3YdD API 文档}
 * @param {object} data
 * @param {string} data.relateTaskNo 借用单号
 * @param {string} data.relateBizType 借用单号
 * @param {string} data.taskType 借用单号
 */
export const fetchTicketRecords = data => {
  return callApi({
    endpoint: 'taskcenter/base/list/relate',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 借用单撤回
 * {@link https://manyun.yuque.com/ewe5b3/bnpnl9/vvo1ms#iIDU4 API 文档}
 * @param {object} data
 * @param {string} data.borrowNo
 * @param {string} [data.reason] 撤回原因
 */
export const fetchBorrowRevert = data => {
  return callApi({
    endpoint: 'dccm/borrow/revert',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 借用单取消
 * {@link https://manyun.yuque.com/ewe5b3/bnpnl9/vvo1ms#GABYj API 文档}
 * @param {object} data
 * @param {string} data.borrowNo
 */
export const fetchBorrowCancel = data => {
  return callApi({
    endpoint: 'dccm/borrow/cancel',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 出库完成确认
 * {@link https://manyun.yuque.com/ewe5b3/bnpnl9/vvo1ms#qP2tt API 文档}
 * @param {object} data
 * @param {string} data.borrowNo
 */
export const fetchBorrowAll = data => {
  return callApi({
    endpoint: 'dccm/borrow/all/stock/out/confirm',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 重新发起
 * {@link https://manyun.yuque.com/ewe5b3/bnpnl9/vvo1ms#VFSl3 API 文档}
 * @param {object} data
 * @param {string} data.borrowNo
 * @param {string} data.title 标题
 * @param {string} data.idcTag 机房
 * @param {string} data.blockGuid 楼guid
 * @param {string} data.borrowType 借用类型
 * @param {string} data.borrowerType 借用类型
 * @param {string} data.borrower 借用人ID
 * @param {string} data.borrowerName 借用人名称
 * @param {string} data.personLiable 负责人ID
 * @param {string} data.personLiableName 负责人名称
 * @param {string} data.borrowStartDate 借用开始日期
 * @param {string} data.borrowEndDate 借用截止日期
 * @param {string} data.reason 借用原因
 * @param {Array} data.borrowAssetInfoList
 */
export const fetchBorrowReSubmit = data => {
  return callApi({
    endpoint: 'dccm/borrow/resubmit',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 可出库资产信息查询
 * {@link https://manyun.yuque.com/ewe5b3/bnpnl9/vvo1ms#KzQYZ API 文档}
 * @param {object} data
 * @param {string} data.borrowNo
 * @param {string} data.numbered
 * @param {string} data.roomGuid
 */
export const fetchBorrowSaaetExWarehouse = data => {
  return callApi({
    endpoint: 'dccm/borrow/asset/can/stock/out/list',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 设备出库目的包间查询
 * {@link https://manyun.yuque.com/ewe5b3/bnpnl9/vvo1ms#CpYta API 文档}
 * @param {object} data
 * @param {string} data.borrowNo
 */
export const fetchBorrowExWarehouseTargetRoom = data => {
  return callApi({
    endpoint: 'dccm/borrow/target/room/list',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 是否全部出库
 * {@link https://manyun.yuque.com/ewe5b3/bnpnl9/vvo1ms#QoDtZ API 文档}
 * @param {object} data
 * @param {string} data.borrowNo
 */
export const fetchBorrowAllOut = data => {
  return callApi({
    endpoint: 'dccm/borrow/all/stock/out/qry',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 转续借审批撤回
 * {@link https://manyun.yuque.com/ewe5b3/bnpnl9/vvo1ms#XFFqX API 文档}
 * @param {object} data
 * @param {string} data.opId 转续借单号
 * @param {string} data.reason 原因
 */
export const fetchRenewRevert = data => {
  return callApi({
    endpoint: 'dccm/borrow/asset/renew/transfer/revert',
    options: {
      method: 'POST',
      data,
    },
  });
};
