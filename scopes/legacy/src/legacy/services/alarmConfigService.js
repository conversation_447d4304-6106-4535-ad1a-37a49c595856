import { callApi } from './api';

/**
 * 获取设备类型告警配置树
 * 接口文档：https://manyun.yuque.com/ewe5b3/sbs6q1/xufoyk
 */
export const getAlarmConfigurationTree = () => {
  return callApi({
    endpoint: `dccm/meta/query/monitor/item`,
  });
};

/**
 * 获取设备类型告警配置树的最后一级数据
 * 接口文档：https://manyun.yuque.com/ewe5b3/sbs6q1/xufoyk
 * @param {object} data 请求参数
 * @param {string} data.deviceType 设备类型
 * @param {string} data.itemType 监控项类型
 * @param {string} [data.itemName] 监控项名称（用于模糊查询）
 */
export const getAlarmConfigurationLastTree = data => {
  return callApi({
    endpoint: 'dccm/monitor/item/by/type/query',
    options: {
      method: 'GET',
      params: data,
    },
  });
};

/**
 * 查询告警模板列表
 * 接口文档：https://manyun.yuque.com/ewe5b3/sbs6q1/lk842x
 * @param {object} data 请求参数
 * @param {string} [data.name] 模板名称
 * @param {string} [data.topCategory] 模板大类
 * @param {string} [data.secondCategory] 模板子类
 * @param {boolean} [data.available] 是否启用
 * @param {string} [data.lastOperator] 更新人
 * @param {number} [data.pageNum] 页码
 * @param {number} [data.pageSize] 条数
 */
export const fetchTemplateList = data => {
  return callApi({
    endpoint: 'dccm/monitor/group/query',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data,
    },
  });
};

/**
 * 查询关联的告警项
 * 接口文档：https://manyun.yuque.com/ewe5b3/sbs6q1/sb47xu
 * @param {object} data 请求参数
 * @param {string} [data.groupId] 模板名称
 */
export const fetchAssociatedItem = data => {
  return callApi({
    endpoint: 'dccm/monitor/group/query/associated/items',
    options: {
      method: 'GET',
      params: data,
    },
  });
};

/**
 * 批量修改启用状态
 * 接口文档：https://manyun.yuque.com/ewe5b3/sbs6q1/wdinkx
 * @param {object} data 请求参数
 * @param {boolean} [data.available] 启用/停用 true/false
 * @param {Array} [data.groupIds] 模板Id
 */
export const availableStatus = data => {
  return callApi({
    endpoint: 'dccm/monitor/group/batch/update',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 编辑模板
 * 接口文档：https://manyun.yuque.com/ewe5b3/sbs6q1/ksek1u
 * @param {object} data 请求参数
 * @param {boolean} [data.available] 启用/停用 true/false
 * @param {Array} [data.groupIds] 模板Id
 * @param {number} [data.id] 模板Id
 * @param {Array} [data.itemIds] 监控项ids
 * @param {string} [data.name] 模板名称
 * @param {Array} [data.schemeIds] 模板组ids
 * @param {string} [data.secondCategory] 模板小类
 * @param {string} [data.topCategory] 模板大类
 *
 */
export const monitorGroupUpdate = data => {
  return callApi({
    endpoint: 'dccm/monitor/group/update',
    options: {
      method: 'POST',
      data,
      content: 'application/json',
    },
  });
};

/**
 * 新建模板
 * 接口文档：https://manyun.yuque.com/ewe5b3/sbs6q1/gogqh9
 * @param {object} data 请求参数
 * @param {boolean} [data.available] 启用/停用 true/false
 * @param {string} [data.id] 模板Id
 * @param {Array} [data.itemIds] 监控项ids
 * @param {string} [data.name] 模板名称
 * @param {Array} [data.schemeIds] 模板组ids
 * @param {string} [data.secondCategory] 模板小类
 * @param {string} [data.topCategory] 模板大类
 *
 */
export const monitorGroupAdd = data => {
  return callApi({
    endpoint: 'dccm/monitor/group/add',
    options: {
      method: 'POST',
      data,
      contentType: 'application/json',
    },
  });
};

/**
 * 所有模板组
 * 接口文档：https://manyun.yuque.com/ewe5b3/sbs6q1/dtf3q9
 *
 */
export const getAllTemplateGroupList = () => {
  return callApi({
    endpoint: 'dccm/monitor/scheme/query/all',
    options: {
      method: 'GET',
    },
  });
};

/**
 * 已关联的模板组
 * 接口文档：https://manyun.yuque.com/ewe5b3/sbs6q1/dtf3q9
 * * @param {object} data 请求参数
 * @param {number} [data.groupId] 模板Id
 *
 */
export const getAssociatedGroupList = data => {
  return callApi({
    endpoint: 'dccm/monitor/group/query/associate/scheme',
    options: {
      method: 'GET',
      params: data,
    },
  });
};

/**
 * 取消关联
 * 接口文档：https://manyun.yuque.com/ewe5b3/sbs6q1/xylez0
 * * @param {object} data 请求参数
 * @param {number} [data.groupId] 模板Id
 * @param {number} [data.schemeId] 模板组ID
 *
 */
export const removeAssociateConfirm = data => {
  return callApi({
    endpoint: 'dccm/monitor/group/remove/associate',
    options: {
      method: 'GET',
      params: data,
    },
  });
};

/**
 * 关联模板组
 * 接口文档：https://manyun.yuque.com/ewe5b3/sbs6q1/yooxp0
 * * @param {object} data 请求参数
 * @param {number} [data.groupId] 模板Id
 * @param {Array} [data.schemeIds] 模板组ID
 *
 */
export const monitorGroupAssociateScheme = data => {
  return callApi({
    endpoint: 'dccm/monitor/group/associate/scheme',
    options: {
      method: 'POST',
      data,
      contentType: 'application/json',
    },
  });
};
/**
 * 关联模板组
 * 接口文档：https://manyun.yuque.com/ewe5b3/sbs6q1/eqfy19
 * * @param {object} data 请求参数
 * @param {number} [data.groupId] 模板Id
 *
 */
export const getEffectiveDomainList = data => {
  return callApi({
    endpoint: 'dccm/monitor/group/effective/domain/query',
    options: {
      method: 'GET',
      params: data,
    },
  });
};

/**
 * 按设备类型查询模板列表
 * 接口文档：https://manyun.yuque.com/ewe5b3/sbs6q1/pxg1h6
 * @param {number} deviceType 设备类型
 *
 */
export const getTemplateListByDeviceType = deviceType => {
  return callApi({
    endpoint: `dccm/monitor/group/query/${deviceType}`,
    options: {
      method: 'GET',
      params: {},
    },
  });
};

/**
 * 查询模板关联的模板组
 * 接口文档：https://manyun.yuque.com/ewe5b3/sbs6q1/gwbmy7
 * * @param {object} data 请求参数
 * @param {number} [data.groupId] 模板Id
 *
 */
export const getGroupListInTemplate = data => {
  return callApi({
    endpoint: 'dccm/monitor/group/query/associated/schemes',
    options: {
      method: 'GET',
      params: data,
    },
  });
};

/**
 * 保存模板
 * 接口文档：https://manyun.yuque.com/ewe5b3/sbs6q1/gk2dve
 * * @param {object} data 请求参数
 * @param {string} [data.name] 模板名称
 * @param {string} [data.deviceType] 目标设备类型
 * @param {Boolean} [data.available] 是否启用
 * @param {string} [data.description] 描述
 * @param {Array} [data.monitorSchemeList] 关联的模板组
 */
export const fetchEditTemplateModule = data => {
  return callApi({
    endpoint: 'dccm/monitor/group/save',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 批量修改告警项
 * 接口文档：https://manyun.yuque.com/ewe5b3/sbs6q1/rfd942
 * * @param {object} data 请求参数
 * @param {string} [data.groupId] 模板id
 * @param {object} [data.monitorItemList] 监控项
 */
export const fetchEditMonitorTtem = data => {
  return callApi({
    endpoint: 'dccm/monitor/item/batch/update',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 删除模板下的监控项
 * 接口文档：https://manyun.yuque.com/ewe5b3/sbs6q1/ldcewb
 * * @param {object} data 请求参数
 * @param {string} [data.groupId] 模板id
 * @param {object} [data.monitorItemList] 监控项
 */
export const fetchRemoveMonitorTtem = data => {
  return callApi({
    endpoint: 'dccm/monitor/group/remove/item',
    options: {
      method: 'GET',
      params: data,
    },
  });
};

/**
 *删除模板
 * 接口文档：https://manyun.yuque.com/ewe5b3/sbs6q1/en7cgi
 * * @param {object} data 请求参数
 * @param {string} [data.id] 模板id
 */
export const fetchDeleteMonitor = data => {
  return callApi({
    endpoint: 'dccm/monitor/group/delete',
    options: {
      method: 'GET',
      params: data,
    },
  });
};

/**
 *自定义告警列表
 * 接口文档：https://manyun.yuque.com/ewe5b3/sbs6q1/kscumb#miXMJ
 * * @param {object} data 请求参数
 */
export const fetchCustomAlarmList = data => {
  return callApi({
    endpoint: 'dccm/monitor/custom/group/query',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 批量修改自定义告警启用状态
 * 接口文档：https://manyun.yuque.com/ewe5b3/sbs6q1/kscumb#V4qWv
 * @param {object} data 请求参数
 * @param {boolean} [data.available] 启用/停用 true/false
 * @param {Array} [data.customGroupIds] 模板Id
 */
export const customAvailableStatus = data => {
  return callApi({
    endpoint: 'dccm/monitor/custom/group/batch/update',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 *删除自定义告警模板
 * 接口文档：https://manyun.yuque.com/ewe5b3/sbs6q1/kscumb#NiyM6
 * * @param {object} data 请求参数
 * @param {string} [data.id] 模板id
 */
export const fetchDeleteCustomGroup = data => {
  return callApi({
    endpoint: 'dccm/monitor/custom/group/delete',
    options: {
      method: 'POST',
      data: { id: data.groupId },
    },
  });
};

/**
 * 新建自定义告警
 * 接口文档：https://manyun.yuque.com/ewe5b3/sbs6q1/kscumb#VZdPm
 * @param {object} data 请求参数
 * @param {boolean} [data.available] 启用/停用 true/false
 *
 */
export const createCustomAlarm = data => {
  return callApi({
    endpoint: 'dccm/monitor/custom/group/add',
    options: {
      method: 'POST',
      data,
      contentType: 'application/json',
    },
  });
};

/**
 * 保存自定义告警模板
 * 接口文档：https://manyun.yuque.com/ewe5b3/sbs6q1/kscumb#RUSn0
 * * @param {object} data 请求参数
 * @param {string} [data.name] 模板名称
 * @param {string} [data.idcTag] 目标设备类型
 * @param {Boolean} [data.available] 是否启用
 * @param {string} [data.description] 描述
 */
export const fetchEditCustomTemplateModule = data => {
  return callApi({
    endpoint: 'dccm/monitor/custom/group/save',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 查询自定义告警关联的告警项
 * 接口文档：https://manyun.yuque.com/ewe5b3/sbs6q1/sb47xu
 * @param {object} data 请求参数
 * @param {string} [data.groupId] 模板名称
 */
export const fetchCustomAssociatedItem = data => {
  return callApi({
    endpoint: 'dccm/monitor/custom/group/query/associated/items',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 批量修改自定义告警项
 * 接口文档：https://manyun.yuque.com/ewe5b3/sbs6q1/kscumb#pAGZ6
 * * @param {object} data 请求参数
 * @param {string} [data.groupId] 模板id
 * @param {object} [data.monitorItemList] 监控项
 */
export const fetchEditCustomMonitorItem = data => {
  return callApi({
    endpoint: 'dccm/monitor/custom/group/item/update',
    options: {
      method: 'POST',
      data,
    },
  });
};
