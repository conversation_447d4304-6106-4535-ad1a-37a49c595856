import { callApi } from './api';

/**
 * 查询北向用户列表
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/cf1s9m API 文档}
 * @param {object} data
 * @param {string} [data.username] 用户名(模糊)
 * @param {number} data.pageNum
 * @param {number} data.pageSize
 */
export const fetchNorthUserPage = data =>
  callApi({
    endpoint: 'dccm/north/user/page/query',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data,
    },
  });

/**
 * 查询北向用户详情
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/cf1s9m API 文档}
 * @param {string} id
 */
export const fetchNorthUserDetail = id =>
  callApi({
    endpoint: 'dccm/north/user/detail/query',
    options: {
      method: 'GET',
      params: {
        id: id,
      },
    },
  });

/**
 * 批量启用/停用北向用户
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/cf1s9m API 文档}
 * @param {object} data
 * @param {boolean} data.available true -> 启用；false -> 停用
 * @param {number[]} data.ids 用户 id 的集合
 */
export const batchUpdateNorthUserStatus = data =>
  callApi({
    endpoint: 'dccm/north/user/batch/update/status',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 创建北向用户
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/cf1s9m API 文档}
 * @param {object} data
 */
export const createNorthUser = data =>
  callApi({
    endpoint: 'dccm/north/user/create',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 修改北向用户
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/cf1s9m API 文档}
 * @param {object} data
 */
export const updateNorthUser = data =>
  callApi({
    endpoint: 'dccm/north/user/update',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 查询用户策略
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/cf1s9m API 文档}
 * @param {object} data
 */
export const queryNorthStartegyList = data =>
  callApi({
    endpoint: 'dccm/north/strategy/query',
    options: {
      method: 'POST',
      data,
    },
  });
