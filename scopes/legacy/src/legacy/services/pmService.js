import { callApi } from './api';

/**
 * 获取树形的所有权限数据
 */
export const fetchPermissionsTree = () => callApi({ endpoint: 'pm/permission/tree' });

/**
 * @deprecated use service/update-clock-in-schedule
 * @param {*} data
 * @returns
 */
export const check = data =>
  callApi({
    endpoint: 'pm/check/addCheckRecord',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * @deprecated use service/create-punch-a-shift-clock
 * @param {*} data
 * @returns
 */
export const supplyCheck = data =>
  callApi({
    endpoint: 'pm/check/supplyCheck',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * @deprecated use service/fetch-paged-fixed-missed-punches
 */
export const fetchSupplyCheckPage = data =>
  callApi({
    endpoint: 'pm/check/checkPage',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * @deprecated  use service/fetch-fixed-missed-punch
 */
export const fetchCheckBasicInfo = data =>
  callApi({
    endpoint: 'pm/check/checkDetail',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * @param {object} data
 * @param {number} [data.id] ID
 */
export const fetchAlterInfoPage = data =>
  callApi({
    endpoint: 'pm/check/alterInfoPage',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 *  @deprecated  use service/create-shift-exchange-request
 */
export const commitExchangeRecord = data =>
  callApi({
    endpoint: 'pm/check/addExchangeRecord',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 *  @deprecated  use service/revoke-alter-request
 */
export const cancelAlterProcess = data =>
  callApi({
    endpoint: 'pm/check/cancelAlterProcess',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * @deprecated use service/create-leave-request
 */
export const commitLeaveRecord = data =>
  callApi({
    endpoint: 'pm/check/addLeaveRecord',
    options: {
      method: 'POST',
      data,
    },
  });

export const fetchAlterBasicInfo = data =>
  callApi({
    endpoint: 'pm/check/alterDetail',
    options: {
      method: 'POST',
      data,
    },
  });

export const fetchTaskBaseInfo = data =>
  callApi({
    endpoint: 'taskcenter/schedule/info',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * @deprecated Use `@manyun/hrm.service.fetch-shifts-for-leave-request` instead
 */
export const scheduleDutyList = data =>
  callApi({
    endpoint: 'pm/duty/scheduleDutyList',
    options: {
      method: 'POST',
      data,
    },
  });

export const supplyScheduleDutyList = data =>
  callApi({
    endpoint: 'pm/duty/supplyScheduleDutyList',
    options: {
      method: 'POST',
      data,
    },
  });

export const replaceUserList = data =>
  callApi({
    endpoint: 'pm/check/replaceUserList',
    options: {
      method: 'POST',
      data,
    },
  });

// GET

export const fetchMockGetArticles = params =>
  callApi({
    endpoint: 'pm/mock/alterInfo',
    options: {
      params,
    },
  });
