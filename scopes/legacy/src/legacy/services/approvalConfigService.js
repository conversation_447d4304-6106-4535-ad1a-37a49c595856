import { callApi } from './api';

/**
 * 查询审批流程列表
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/suy3hg API 文档}
 * @param {object} data
 */
export const fetchApprovalProcessList = data =>
  callApi({
    endpoint: 'workflow/process/info/list',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 查询审批流程详情
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/suy3hg API 文档}
 * @param {object} data
 */
export const fetchApprovalProcessDetail = data =>
  callApi({
    endpoint: 'workflow/process/info/detail',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 创建审批流程
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/suy3hg API 文档}
 * @param {object} data
 */
export const fetchApprovalProcessCreate = data =>
  callApi({
    endpoint: 'workflow/process/info/create',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 编辑审批流程
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/suy3hg API 文档}
 * @param {object} data
 */
export const fetchApprovalProcessEdit = data =>
  callApi({
    endpoint: 'workflow/process/info/edit',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 审批场景列表
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/suy3hg API 文档}
 * @param {object} data
 */
export const fetchApprovalScenesList = data =>
  callApi({
    endpoint: 'workflow/scenes/list/query',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 审批场景列表
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/suy3hg API 文档}
 * @param {object} data
 */
export const fetchApprovalScenesEdit = data =>
  callApi({
    endpoint: 'workflow/scenes/edit',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 审批流程无分页列表
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/suy3hg API 文档}
 * @param {object} data
 */
export const fetchApprovalProcessListWithoutPage = data =>
  callApi({
    endpoint: 'workflow/process/info/list/without/page',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 审批场景和流程关联关系列表
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/suy3hg API 文档}
 * @param {object} data
 */
export const fetchScenesProcesReleteList = data =>
  callApi({
    endpoint: 'workflow/scenes/process/relate/query',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 编辑审批场景和流程关联关系
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/suy3hg API 文档}
 * @param {object} data
 */
export const fetchScenesProcesReleteEdit = data =>
  callApi({
    endpoint: 'workflow/scenes/process/relate/edit',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 删除流程
 * @param {object} data
 * @param data.processCode
 */
export const fetchScenesProcesDelete = data =>
  callApi({
    endpoint: 'workflow/process/info/delete',
    options: {
      method: 'POST',
      data,
    },
  });
