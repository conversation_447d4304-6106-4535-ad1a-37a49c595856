import { callApi } from './api';

/**
 * 查询详情
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/qlggml API 文档}
 *  @param {string} id
 */
export const getModelDetail = ({ id }) => {
  return callApi({
    endpoint: 'dccm/model/query/detail',
    options: {
      params: { id },
    },
  });
};

/**
 * 编辑型号
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/hnxbbt API 文档}
 * @param {object} params 请求参数
 * @param {string} params.id
 * @param {string} params.vendorCode
 * @param {Array} params.specModels
 * @param {string} [params.description]
 */
export const editModel = ({ id, vendorCode, specModels, description }) =>
  callApi({
    endpoint: 'dccm/model/update',
    options: {
      method: 'POST',
      data: { id, vendorCode, specModels, description },
    },
  });

//删除
export const deleteModel = ({ id, operatorNotes }) =>
  callApi({
    endpoint: 'dccm/model/delete',
    options: {
      params: { id, operatorNotes },
    },
  });

/**
 * 限制范围的查找厂商型号
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/gca2x5 API 文档}
 * @param {object} params 请求参数
 * @param {string} params.deviceType
 */
export const fetchVendorModelTree = data =>
  callApi({
    endpoint: 'dccm/model/query/meta',
    options: {
      method: 'POST',
      data,
    },
  });
