import { callApi } from './api';

/**
 * 查询订单列表
 * {@link https://manyun.yuque.com/ewe5b3/bnpnl9/sz8d4p#b6U0n API 文档}
 * @param {object} data
 * @param {string} [data.topCategory] 一级分类
 * @param {string} [data.secondCategory] 二级分类
 * @param {string} [data.deviceType] 三级分类
 * @param {Array} [data.assetNo] 资产ID
 * @param {Array} [data.productModel] 型号
 * @param {string} [data.vendor] 厂商
 * @param {string} data.sourceType 类型
 * @param {string} [data.batchNo] 批次号
 * @param {string} [data.sourceNo] 采购单号/调拨单号
 * @param {string} [data.targetBlockGuid] 目的位置
 * @param {string} [data.sourceBlockGuid] 源位置
 * @param {string} [data.arrivalStartTime] 到货开始时间
 * @param {string} [data.arrivalEndTime] 到货结束时间
 * @param {number} data.pageNum
 * @param {number} data.pageSize
 */
export const fetchArrivalAssetList = data => {
  return callApi({
    endpoint: 'dccm/arrival/asset/list',
    options: {
      method: 'POST',
      data,
    },
  });
};

// 下载采购excel模板
export const downloadPurchaseAssetModel = () =>
  callApi({
    endpoint: 'dccm/arrival/purchase/download',
    options: {
      method: 'GET',
      params: {},
      responseType: 'blob',
    },
  });

// 下载搬迁excel模板
export const downloadMoveAssetModel = () =>
  callApi({
    endpoint: 'dccm/arrival/move/download',
    options: {
      method: 'GET',
      params: {},
      responseType: 'blob',
    },
  });

// 导入采购到货信息
export const importPurchaseAsset = data =>
  callApi({
    endpoint: 'dccm/purchase/asset/import',
    options: {
      method: 'POST',
      data,
      timeout: 5 * 60 * 1000,
    },
  });

// 导入搬迁到货信息
export const importWoveAsset = data =>
  callApi({
    endpoint: 'dccm/move/asset/import',
    options: {
      method: 'POST',
      data,
      timeout: 5 * 60 * 1000,
    },
  });

/**
 * 校验关联单号是否存在
 * @param {object} data
 * @param {string} data.batchNo
 * @param {string} data.sourceType
 */
export const validateRelateNo = data => {
  return callApi({
    endpoint: 'dccm/arrival/batchno/exist/qry',
    options: {
      method: 'POST',
      data,
    },
  });
};
