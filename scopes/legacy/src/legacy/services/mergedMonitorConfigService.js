import { callApi } from './api';

/**
 * 查询收敛配置列表
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/pbg9ww API 文档}
 * @param {object} data
 * @param {string} [data.name] 模板名称（模糊查询）
 * @param {string[]} [data.deviceTypeList] 设备三级分类
 * @param {boolean} [data.available] 是否启用
 * @param {string} [data.lastOperatorName] 更新人名字（精确查询）
 * @param {number} data.pageNum
 * @param {number} data.pageSize
 */
export const fetchData = data =>
  callApi({
    endpoint: 'dccm/monitor/merge/rule/query',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 批量启用/停用收敛配置
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/rb4er5 API 文档}
 * @param {object} data
 * @param {boolean} data.available true -> 启用；false -> 停用
 * @param {number[]} data.mergeRuleIds 收敛配置 id 的集合
 */
export const batchUpdateMergedMonitorConfigs = data =>
  callApi({
    endpoint: 'dccm/monitor/merge/rule/batch/update',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 删除收敛配置
 * @param {number} id 收敛配置 id
 */
export const deleteMergedMonitorConfig = id =>
  callApi({
    endpoint: 'dccm/monitor/merge/rule/delete',
    options: {
      params: {
        mergeRuleId: id,
      },
    },
  });

/**
 * 查询所有收敛配置（若传 `deviceType`，则查询此设备类型的所有收敛配置）
 * @param {string} deviceType
 */
export const fetchAllMergedMonitorConfigs = deviceType =>
  callApi({
    endpoint: 'dccm/monitor/merge/rule/query/all',
    options: {
      params: { deviceType },
    },
  });

/**
 * 创建收敛配置
 * @param {object} data
 */
export const createNewMergedMonitorConfig = data =>
  callApi({
    endpoint: 'dccm/monitor/merge/rule/add',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 查询被收敛的设备类型列表
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/vp7k11 API 文档}
 * @param {string} mergeRuleId
 */
export const fetchMergedMonitorConfigDeviceTypesData = mergeRuleId =>
  callApi({
    endpoint: 'dccm/monitor/merge/point/query/merged/device/type',
    options: {
      params: { mergeRuleId },
    },
  });

/**
 * 查询被收敛的点位
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/myr31w API 文档}
 * @param {object} params
 * @param {string} params.id
 * @param {string} [params.deviceType]
 */
export const fetchMergedMonitorConfigChildItems = ({ id, deviceType }) =>
  callApi({
    endpoint: 'dccm/monitor/merge/point/query/merged/point',
    options: {
      params: { mergeRuleId: id, deviceType },
    },
  });

/**
 * 更新收敛配置的基本信息
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/bfatd6 API 文档}
 * @param {object} data
 * @param {number} data.id
 * @param {string} data.name
 * @param {string} data.deviceType
 * @param {boolean} data.available
 * @param {string} [data.description]
 * @param {string} [data.pointName]
 * @param {string} data.pointCode
 * @param {number} data.expire
 * @param {string} data.condition
 * @param {string} [data.conditionValue]
 * @param {string} [data.validLimits]
 */
export const updateMergedMonitorConfig = data =>
  callApi({
    endpoint: 'dccm/monitor/merge/rule/save',
    options: {
      method: 'POST',
      data,
    },
  });

export const fetchMergedMonitorConfigBaseInfo = id =>
  callApi({
    endpoint: 'dccm/monitor/merge/rule/query/detail',
    options: {
      params: {
        mergeRuleId: id,
      },
    },
  });

/**
 * 删除被收敛测点或配置
 * @param {object} data
 * @param {number} data.mergeRuleId
 * @param {string} data.deviceType
 * @param {string} [data.pointCode]
 */
export const deleteChildItem = data =>
  callApi({
    endpoint: 'dccm/monitor/merge/point/delete',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * @typedef ChildItem
 * @property {number|null} id
 * @property {string} deviceType
 * @property {string} pointName
 * @property {string} mergeScope
 * @property {string|null} condition
 * @property {string|null} conditionValue
 * @property {string|null} validLimits
 */
/**
 * 删除被收敛测点或配置
 * @param {object} data
 * @param {number} data.mergeRuleId
 * @param {ChildItem[]} data.mergePointList
 */
export const updateChildItems = data =>
  callApi({
    endpoint: 'dccm/monitor/merge/point/update',
    options: {
      method: 'POST',
      data,
    },
  });
