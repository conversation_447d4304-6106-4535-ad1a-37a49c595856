import { getSpaceGuid } from '@manyun/dc-brain.legacy.utils';

import { callApi } from './api';

/**
 * 查询点位阈值
 * @param {object} data
 * @param {string} data.idc
 * @param {string} [data.block]
 * @param {string} [data.room]
 * @param {string} [data.spaceGuid] `EC01.A.A1-1`
 * @param {string} [data.deviceType]
 * @param {string} [data.pointCode]
 * @param {string} [data.deviceGuid]
 */
export const fetchPointThresholds = ({
  idc,
  block,
  room,
  spaceGuid,
  deviceType,
  pointCode,
  deviceGuid = null,
}) => {
  let spaceGuidStr =
    spaceGuid && typeof spaceGuid == 'string' ? spaceGuid : getSpaceGuid(idc, block, room);

  // 如果没传 `deviceGuid`，则认为要查询某个空间下的某个聚合点位的阈值
  if (deviceGuid === null) {
    spaceGuid += '.' + deviceType;
  }

  return callApi({
    endpoint: 'dccm/point/items',
    options: {
      method: 'POST',
      data: {
        spaceGuid: spaceGuidStr,
        deviceType,
        pointCode,
        deviceGuid,
      },
    },
  });
};

/**
 * {@link https://manyun.yuque.com/ewe5b3/front-end/wklqlf API 文档}
 * @param {object} params 请求参数
 * @param {Array} params.pointGuidList 点位
 * @param {string} params.startTime 开始时间
 * @param {string} params.endTime 结束时间
 */
export const fetchIdcWorkbenchPue = data =>
  callApi({
    endpoint: 'dcim/data/energy/query',
    options: {
      method: 'POST',
      data,
    },
  });
