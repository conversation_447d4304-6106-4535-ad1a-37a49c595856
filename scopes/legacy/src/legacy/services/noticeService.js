import { callApi } from './api';

export const getValidNoticeList = data => {
  return callApi({
    endpoint: 'pm/notice/validPage',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data,
    },
  });
};

export const getPublishNoticeList = data => {
  return callApi({
    endpoint: 'pm/notice/publisherPage',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data,
    },
  });
};

export const deleteNotice = data =>
  callApi({
    endpoint: 'pm/notice/delete',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data,
    },
  });

export const publishNotice = data =>
  callApi({
    endpoint: 'pm/notice/publish',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data,
    },
  });

export const revokeNotice = data =>
  callApi({
    endpoint: 'pm/notice/revoke',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data,
    },
  });

export const updateNotice = data => {
  return callApi({
    endpoint: 'pm/notice/update',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data,
    },
  });
};

export const updatePublishNotice = data => {
  return callApi({
    endpoint: 'pm/notice/updatePublish',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data,
    },
  });
};

export const fetchSelectResourceList = async () => {
  const data = await callApi({
    endpoint: 'pm/resource/tree',
    options: {
      method: 'GET',
    },
  });
  if (data.response) {
    return data.response;
  }
  return [];
};

/**
 * 发布公告
 * 接口文档：http://121.40.158.1:8080/swagger-ui.html#/%E5%85%AC%E5%91%8A%E7%B1%BB/addResourceUsingPOST
 * @param {object} data 请求参数
 * @param {string} data.content  公告内容
 * @param {string} data.importance 公告重要程度
 * @param {string} data.rangeType 公告发布范围
 * @param {string} data.title 公告标题
 * @param {string} [data.resourceIdList] 资源id列表
 * @param {string} data.validBeginDate 有效开始时间
 * @param {string} data.validEndDate 有效结束时间
 */
export const addNotice = data => {
  return callApi({
    endpoint: 'pm/notice/add',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data,
    },
  });
};

/**
 * 查询公告详情
 * {@link https://manyun.yuque.com/ewe5b3/kwdgqo/rqbsvp API 文档}
 *  @param {string} id
 */
export const fetchNoticeDetail = data => {
  return callApi({
    endpoint: 'pm/notice/detail',
    options: {
      method: 'POST',
      data,
    },
  });
};
