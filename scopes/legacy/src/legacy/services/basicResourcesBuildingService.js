import { callApi } from './api';

export const fetchBuildingList = data =>
  callApi({
    endpoint: 'dccm/block/query',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data: data,
    },
  });

export const fetchIdc = () =>
  callApi({
    endpoint: 'dccm/meta/query/idc/list',
    options: {
      method: 'GET',
    },
  });

export const fetchGetOperationStatus = () =>
  callApi({
    endpoint: 'dccm/get/operation/status',
    options: {
      method: 'GET',
    },
  });

export const fetchExportBuilding = params =>
  callApi({
    endpoint: 'dccm/block/export',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data: params,
      responseType: 'blob',
    },
  });

export const fetchGetBlockType = () =>
  callApi({
    endpoint: 'dccm/get/block/type',
  });
