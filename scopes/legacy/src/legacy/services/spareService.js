import { callApi } from './api';

/**
 * 查询无sn耗材列表
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/suy3hg API 文档}
 * @param {object} data
 */
export const fetchSpares = data =>
  callApi({
    endpoint: 'dccm/spare/list/query',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 删除无sn耗材
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/suy3hg API 文档}
 * @param {object} data
 */
export const deleteSpare = async data =>
  await callApi({
    endpoint: 'dccm/spare/delete',
    options: {
      method: 'POST',
      data,
    },
  });

// 下载模板
export const downloadSpareModel = () =>
  callApi({
    endpoint: 'dccm/spare/download',
    options: {
      responseType: 'blob',
    },
  });

// 导入
export const uploadSpare = file =>
  callApi({
    endpoint: 'dccm/spare/import',
    options: {
      method: 'POST',
      contentType: 'multipart/form-data',
      data: file,
      timeout: 5 * 60 * 1000,
    },
  });

// 提交编辑耗材基本信息表单
export const editSpare = async data =>
  await callApi({
    endpoint: 'dccm/spare/edit',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 楼下可用耗材数量查询
 * @param {object} data
 * @param {string} data.spareType 耗材三级分类
 * @param {string} data.locationGuid 楼栋guid
 * @param {string} data.dimension 维度
 * @param {string} data.vendor 厂商
 * @param {string} data.productModel 型号
 */

export const fetchUsableNum = async data =>
  await callApi({
    endpoint: 'dccm/spare/usable/num/query',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 包间下可用耗材数量查询
 * @param {object} data
 * @param {string} data.spareType 耗材三级分类
 * @param {string} data.blockGuid 楼栋guid
 * @param {string} data.idcTag 机房
 * @param {string} data.roomTag 包间
 * @param {string} data.vendor 厂商
 * @param {string} data.productModel 型号
 */

export const fetchUniqueNum = async data =>
  await callApi({
    endpoint: 'dccm/spare/unique/query',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 耗材导出
 * {@link http://172.16.0.17:13000/project/42/interface/api/463 API 文档}
 * @param {object} data
 * @param {string} [data.topCategory] 一级分类
 * @param {string} [data.secondCategory] 二级分类
 * @param {Array} [data.spareTypeList] 三级分类
 * @param {string} [data.modifyPersonId] 更新人
 * @param {number} [data.modifyStartTime] 更新开始时间
 * @param {number} [data.modifyEndTime] 更新结束时间
 * @param {string} [data.blockGuid] 楼栋guid
 * @param {string} [data.idcTag] 机房
 * @param {string} [data.roomTag] 包间
 * @param {string} [data.vendor] 厂商
 * @param {string} [data.productModel] 型号
 */

export const exportSpare = data =>
  callApi({
    endpoint: 'dccm/spare/export',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data,
      responseType: 'blob',
    },
  });
