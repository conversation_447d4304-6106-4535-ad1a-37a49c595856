import { callApi } from './api';

/**
 * 删除某个机房
 * @param {object} params
 * @param {string} params.guid idc guid
 * @param {string} params.operatorNotes deleting reason
 */
export const deleteIdc = params =>
  callApi({
    endpoint: 'dccm/idc/delete',
    options: { params },
  });

/**
 * 删除某个楼栋
 * @param {object} params
 * @param {string} params.guid block guid
 * @param {string} params.operatorNotes deleting reason
 */
export const deleteBlock = params =>
  callApi({
    endpoint: 'dccm/block/delete',
    options: { params },
  });

/**
 * 删除某个包间
 * @param {object} params
 * @param {string} params.guid room guid
 * @param {string} params.operatorNotes deleting reason
 */
export const deleteRoom = params =>
  callApi({
    endpoint: 'dccm/room/delete',
    options: { params },
  });

/**
 * 删除某个机柜
 * @param {object} params
 * @param {string} params.guid grid guid
 * @param {string} params.operatorNotes deleting reason
 */
export const deleteGrid = params =>
  callApi({
    endpoint: 'dccm/grid/delete',
    options: { params },
  });

/**
 * 根据父设备 guid 查询子设备
 * @param {object} params
 * @param {string} params.spaceGuid 楼栋唯一码，格式为: EC01.A
 * @param {string} params.parentGuid 父设备guid
 */
export const fetchChildDevicesByGuid = params =>
  callApi({
    endpoint: 'dccm/device/query/by/parent/guid',
    options: {
      params,
    },
  });

/**
 * 查询主点位下的子点位（aka. 扩展点位）
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/uoim0c API 文档}
 * @param {object} data
 * @param {string} data.deviceType
 * @param {string} data.pointCode
 */
export const fetchExtendedPoints = data =>
  callApi({
    endpoint: 'dccm/point/query/sub/list',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 批量更新设备（父设备、责任人、厂商、型号）
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/olo3al API 文档}
 * @param {object} data
 * @param {string[]} data.deviceGuidList 设施
 * @param {string} [data.parentGuid] 父设施
 * @param {string} [data.operator] 责任人
 * @param {string} [data.productModel] 型号
 * @param {string} [data.spaceGuid] 位置
 * @param {string} [data.purchaseTime] 购买时间
 * @param {string} [data.warrantyTime] 过保时间
 * @param {string} [data.powerLine] 所属线路
 * @param {string} [data.checkTime] 验收日期
 * @param {number} [data.purchasePrice] 购买价格
 * @param {number} [data.warrantyPrice] 维保价格
 * @param {string} [data.warrantyVendor] 维保厂商
 */
export const batchUpdateDevice = data =>
  callApi({
    endpoint: 'dccm/device/batch/update',
    options: {
      method: 'POST',
      data,
    },
  });
