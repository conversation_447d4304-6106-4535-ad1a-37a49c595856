import { callApi } from './api';

/**
 * 根据 ID 获取告警项配置
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/rpmk48 API文档}
 * @param {object} params
 * @param {string} params.id 告警项配置 ID
 * @param {string} [params.deviceType]
 * @param {string} [params.pointCode]
 */
export const fetchMonitorItemConfigById = params =>
  callApi({
    endpoint: 'dccm/monitor/item/query/info/by/id',
    options: { params },
  });
