import { callApi } from './api';

/**
 * 查询通道列表
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/cf1s9m API 文档}
 * @param {object} data
 * @param {string} [data.username] 用户名(模糊)
 * @param {number} data.pageNum
 * @param {number} data.pageSize
 */
export const fetchChannelConfigPage = data =>
  callApi({
    endpoint: 'dccm/channel/page/query',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data,
    },
  });

/**
 * 查询通道详情
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/cf1s9m API 文档}
 * @param {string} ids
 */
export const fetchChannelConfigDetail = data =>
  callApi({
    endpoint: 'dccm/channel/batch/query',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data,
    },
  });

/**
 * 查询通道下的设施列表
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/kguhhd#69sH5 API 文档}
 * @param {Integer} channelId
 * @param {Integer} deviceName
 */
export const fetchChannelDeviceList = data =>
  callApi({
    endpoint: 'dccm/channel/device/query',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data,
    },
  });

/**
 * 查询通道下的点位列表
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/kguhhd#69sH5 API 文档}
 * @param {Integer} channelId
 * @param {Integer} deviceGuid
 */
export const fetchChannelPointList = data =>
  callApi({
    endpoint: 'dccm/channel/point/query',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data,
    },
  });

/**
 * 查询通道下的点位
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/kguhhd#69sH5 API 文档}
 * @param {Integer} channelId
 * @param {Integer} deviceGuid
 */
export const fetchChannelPointResult = data =>
  callApi({
    endpoint: 'dccm/channel/point/query/result',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data,
    },
  });

/**
 * 批量启用/停用通道
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/cf1s9m API 文档}
 * @param {object} data
 * @param {boolean} data.available true -> 启用；false -> 停用
 * @param {number[]} data.ids 用户 id 的集合
 */
export const batchUpdateChannelStatus = data =>
  callApi({
    endpoint: 'dccm/channel/batch/update/status',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 创建通道
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/cf1s9m API 文档}
 * @param {object} data
 */
export const createChannelConfig = data =>
  callApi({
    endpoint: 'dccm/channel/create',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 修改通道
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/cf1s9m API 文档}
 * @param {object} data
 */
export const updateChannelConfig = data =>
  callApi({
    endpoint: 'dccm/channel/update',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 通道类型查询
 * {@link https://manyun.yuque.com/luc1u2/ilvfpi/gi0apm#uNkWY API 文档}
 */
export const fetchChannelType = () => {
  return callApi({
    endpoint: 'dccm/channel/get/type',
  });
};

/**
 * 通道操作类型查询
 * {@link https://manyun.yuque.com/luc1u2/ilvfpi/gi0apm#uNkWY API 文档}
 */
export const fetchChannelOperatorType = () => {
  return callApi({
    endpoint: 'dccm/channel/get/operator/type',
  });
};

/**
 * 通道协议类型查询
 * {@link https://manyun.yuque.com/luc1u2/ilvfpi/gi0apm#uNkWY API 文档}
 */
export const fetchChannelProtocolType = () => {
  return callApi({
    endpoint: 'dccm/channel/get/protocol/type',
  });
};

/**
 * 导入通道点位
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/cc1piv#miXMJ API 文档}
 */

export const channelPointImport = file =>
  callApi({
    endpoint: 'dccm/channel/point/import',
    options: {
      method: 'POST',
      contentType: 'multipart/form-data',
      data: file,
      timeout: 5 * 60 * 1000,
    },
  });

// 下载通道点位模板
export const downloadChannelPointModel = () =>
  callApi({
    endpoint: 'dccm/channel/point/download',
    options: {
      method: 'GET',
      params: {},
      responseType: 'blob',
    },
  });

// 下载通道点位guid模板
export const downloadChannelPointGuidModel = () =>
  callApi({
    endpoint: 'dccm/channel/point/guid/download',
    options: {
      method: 'GET',
      params: {},
      responseType: 'blob',
    },
  });

// 删除通道点位
export const deleteChannelPoint = data =>
  callApi({
    endpoint: 'dccm/channel/point/delete',
    options: {
      method: 'POST',
      data,
    },
  });
