import { callApi } from './api';

/**
 * @deprecated Use `@manyun/resource-hub.service.fetch-devices` instead.
 *
 * 查询某个机房某幢楼下的设备
 * @param {object} data
 * @param {string} data.idcTag
 * @param {string} data.blockTag
 * @param {string} [data.tag] 根据设备名称模糊查询前10条数据
 * @param {string[]} [data.deviceTypes] 设备类型集合
 * @param {string} [data.deviceGuidList] 设备guid
 * @param {Array} [data.assetNoList] 设备资产编号
 * @param {Array} [data.roomGuidList] 包间
 */
export const fetchDevicesByTypes = data =>
  callApi({
    endpoint: 'dccm/query/device/by/block',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 根据设备唯一编码查询设备信息
 * @deprecated use `fetch-device-by-guid` instead
 * @param {string} guid 设备唯一编码
 */
export const fetchDeviceByGuid = guid =>
  callApi({
    endpoint: 'dccm/device/detail',
    options: { params: { guid } },
  });

/**
 * 统计设备数量
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/aegwt7 API 文档}
 * @param {object} data
 * @param {string} data.idcTag 机房
 * @param {string} data.blockTag 楼
 * @param {string} data.deviceType 设备类型
 * @param {string} data.vendor 厂商
 * @param {string} data.productModel 型号
 * @param {Array} data.roomTypeList 包间类型
 * @param {string} data.filterRoomGuid 包间guid
 */
export const fetchDeviceNum = data =>
  callApi({
    endpoint: 'dccm/device/query/count',
    options: {
      method: 'POST',
      data,
    },
  });
