import { message } from '@manyun/base-ui.ui.message';

import { callApi } from './api';

export const fetchUser = ({ searchName, pageNo, pageSize, searchType }) =>
  callApi({
    endpoint: 'pm/user/page',
    options: {
      method: 'GET',
      params: {
        pageSize,
        searchName,
        pageNo,
        searchType,
      },
    },
  });

export const fetchSelectUserGroup = async groupName => {
  const { response, error } = await callApi({
    endpoint: 'pm/group/list',
    options: {
      method: 'GET',
      params: {
        groupName,
      },
    },
  });
  if (error) {
    return;
  }
  let userGroupList = [];
  if (response) {
    userGroupList = response.data.map(item => {
      return { ...item, label: item.groupName, key: item.id };
    });
  }
  return userGroupList;
};

export const fetchDelete = userId =>
  callApi({
    endpoint: 'pm/user/delete',
    options: {
      method: 'POST',
      data: `userId=${userId}`,
    },
  });

export const fetchDetail = (userId, loginName) =>
  callApi({
    endpoint: 'pm/user/userInfoDetail',
    options: {
      method: 'GET',
      params: {
        userId,
        loginName,
      },
    },
  });

export const fetchEditConfirm = ({ loginName, userName, remarks, userId }) =>
  callApi({
    endpoint: 'pm/user/update',
    options: {
      method: 'POST',
      data: `loginName=${encodeURIComponent(loginName)}&userName=${encodeURIComponent(
        userName
      )}&remarks=${remarks ? encodeURIComponent(remarks) : ''}&userId=${userId}`,
    },
  });

export const fetchLog = (userId, pageNo, pageSize) =>
  callApi({
    endpoint: 'pm/user/userActionLogs',
    options: {
      method: 'GET',
      params: {
        pageNo,
        pageSize,
        userId,
      },
    },
  });

export const fetchJoinUserGroup = ({ userId, pageNo, pageSize, groupName }) =>
  callApi({
    endpoint: 'pm/user/groupPage',
    options: {
      method: 'GET',
      params: {
        userId,
        groupName,
        pageNo,
        pageSize,
      },
    },
  });

export const fetchRemoveUserGroup = ({ userId, groupId }) =>
  callApi({
    endpoint: 'pm/user/unbindGroup',
    options: {
      method: 'POST',
      data: `userId=${userId}&groupId=${groupId}`,
    },
  });

export const fetchAddNewUser = (
  newUserInfos,
  multiFactorAuthentication,
  passwordType,
  resetPassword
) => {
  return callApi({
    endpoint: 'pm/user/addUsers',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data: {
        userInfos: newUserInfos,
        multiFactorAuthentication,
        passwordType,
        resetPassword,
      },
    },
  });
};

export const fetchAssociatedUserGroup = async (
  selectedSources,
  selectedTargets,
  id,
  fetchUserGroupList,
  needFetchList,
  pageNo,
  pageSize,
  filterTypeValue
) => {
  const userIds = selectedSources.map(item => item.key);
  const groupIds = selectedTargets.map(item => item.id);
  const data = await callApi({
    endpoint: 'pm/user/bindGroups',
    options: {
      method: 'POST',
      data: `userIds=${userIds}&groupIds=${groupIds}`,
    },
  });
  if (data.response) {
    message.success('关联用户组成功');
    needFetchList && fetchUserGroupList({ pageNo, pageSize, filterTypeValue, userId: id });
    return data.response;
  } else {
    message.error(data.error || '关联用户组失败');
  }
};

export const fetchSelectUser = async searchName => {
  const data = await callApi({
    endpoint: 'pm/user/list',
    options: {
      method: 'GET',
      params: {
        searchName,
      },
    },
  });
  let userList = [];
  if (data.response) {
    userList = data.response.data.map(item => {
      return { label: item.userName, key: item.id, value: item.id };
    });
  }
  return userList;
};

// 查询用户和id
export const fetchUserIdAndNameMap = () =>
  callApi({
    endpoint: 'pm/user/userIdAndNameMap',
  });

/**
 * 根据用户名或邮箱或工号查询用户信息列表
 * 接口文档：https://manyun.yuque.com/ewe5b3/pkinbk/eto76h
 **/
export const fetchUserListByKey = key =>
  callApi({
    endpoint: 'pm/user/userListByKey',
    options: {
      params: { key },
    },
  });
