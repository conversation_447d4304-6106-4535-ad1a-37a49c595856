import { callApi } from './api';

/**
 * 查询班次列表
 * @deprecated use hrm.service.fetch-paged-shifts
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/eedvop API 文档}
 * @param {object} data
 * @param {string} [data.dutyName] 班次名或创建人姓名
 * @param {number} data.pageNum
 * @param {number} data.pageSize
 */
export const fetchShiftData = data =>
  callApi({
    endpoint: 'pm/duty/page',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 添加班次
 * @deprecated use hrm.service.create-shift
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/dlmwgy API 文档}
 * @param {object} data
 * @param {string} data.dutyName  班次名
 * @param {string} data.onDutyTime 上班时间
 * @param {string} data.offDutyTime 下班时间
 * @param {boolean} data.offIsNextDay 下班时间是否为次日
 * @param {object} data.dutyProperties 班次规则
 * @param {boolean} data.dutyProperties.enableOnDutyCheckRange 是否限制上班打卡范围
 * @param {object} [data.dutyProperties.onDutyCheckRange] 上班打卡范围
 * @param {string} [data.dutyProperties.onDutyCheckRange.startTime] 开始时间
 * @param {boolean} [data.dutyProperties.onDutyCheckRange.startIsNextDay] 开始时间是否为次日
 * @param {string} [data.dutyProperties.onDutyCheckRange.endTime] 结束时间
 * @param {boolean} [data.dutyProperties.onDutyCheckRange.endIsNextDay] 结束时间是否为次日
 * @param {boolean} data.dutyProperties.enableOffDutyCheckRange 是否限制下班打卡范围
 * @param {object} [data.dutyProperties.offDutyCheckRange] 下班打卡范围
 * @param {string} [data.dutyProperties.offDutyCheckRange.startTime] 开始时间
 * @param {boolean} [data.dutyProperties.offDutyCheckRange.startIsNextDay] 开始时间是否为次日
 * @param {string} [data.dutyProperties.offDutyCheckRange.endTime] 结束时间
 * @param {boolean} [data.dutyProperties.offDutyCheckRange.endIsNextDay] 结束时间是否为次日
 * @param {boolean} [data.dutyProperties.allowRest] 是否允许休息
 * @param {object} [data.dutyProperties.restRange] 休息范围
 * @param {string} [data.dutyProperties.restRange.startTime] 开始时间
 * @param {boolean} [data.dutyProperties.restRange.startIsNextDay] 开始时间是否为次日
 * @param {string} [data.dutyProperties.restRange.endTime] 结束时间
 * @param {boolean} [data.dutyProperties.restRange.endIsNextDay] 结束时间是否为次日
 * @param {boolean} data.dutyProperties.enableOffset 是否支持上下班时间偏移，早到早走，晚到晚走
 * @param {number} [data.dutyProperties.onDutyOffsetMinutes] 上班最多可晚到多少分钟
 * @param {number} [data.dutyProperties.offDutyOffsetMinutes] 下班最多可早走多少分钟
 * @param {boolean} data.dutyProperties.enableBuffer  是否允许晚到/早走不记为异常
 * @param {number} [data.dutyProperties.onDutyBufferMinutes]  上班最多晚到多少分钟
 * @param {number} [data.dutyProperties.offDutyBufferMinutes]  下班最多早走多少分钟
 * @param {number} [data.dutyProperties.offDutySeriousMinutes]  下班严重早退分钟数
 * @param {boolean} data.dutyProperties.enableCompensate  是否支持跨班次补偿
 * @param {Array} [data.dutyProperties.compensateHours] 跨班次补偿规则
 * @param {boolean} data.dutyProperties.allowContinuousWork  是否允许连续上班
 * @param {boolean} [data.dutyProperties.notAllowContinuousTime]  不允许连续上班的分钟数
 */
export const fetchCreateShift = data =>
  callApi({
    endpoint: 'pm/duty/create',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 删除班次
 * @deprecated use hrm.service.delete-shift
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/oo10ti API 文档}
 * @param {object} data
 * @param {number} data.id  班次id
 */
export const fetchDeleteShift = data =>
  callApi({
    endpoint: 'pm/duty/delete',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 更新班次
 *  @deprecated use hrm.service.update-shift
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/zbk5g1API 文档}
 * @param {object} data
 * @param {number} data.id  班次
 * @param {string} data.dutyName  班次名
 * @param {string} data.onDutyTime 上班时间
 * @param {string} data.offDutyTime 下班时间
 * @param {boolean} data.offIsNextDay 下班时间是否为次日
 * @param {object} data.dutyProperties 班次规则
 * @param {boolean} data.dutyProperties.enableOnDutyCheckRange 是否限制上班打卡范围
 * @param {object} [data.dutyProperties.onDutyCheckRange] 上班打卡范围
 * @param {string} [data.dutyProperties.onDutyCheckRange.startTime] 开始时间
 * @param {boolean} [data.dutyProperties.onDutyCheckRange.startIsNextDay] 开始时间是否为次日
 * @param {string} [data.dutyProperties.onDutyCheckRange.endTime] 结束时间
 * @param {boolean} [data.dutyProperties.onDutyCheckRange.endIsNextDay] 结束时间是否为次日
 * @param {boolean} data.dutyProperties.enableOffDutyCheckRange 是否限制下班打卡范围
 * @param {object} [data.dutyProperties.offDutyCheckRange] 下班打卡范围
 * @param {string} [data.dutyProperties.offDutyCheckRange.startTime] 开始时间
 * @param {boolean} [data.dutyProperties.offDutyCheckRange.startIsNextDay] 开始时间是否为次日
 * @param {string} [data.dutyProperties.offDutyCheckRange.endTime] 结束时间
 * @param {boolean} [data.dutyProperties.offDutyCheckRange.endIsNextDay] 结束时间是否为次日
 * @param {boolean} [data.dutyProperties.allowRest] 是否允许休息
 * @param {object} [data.dutyProperties.restRange] 休息范围
 * @param {string} [data.dutyProperties.restRange.startTime] 开始时间
 * @param {boolean} [data.dutyProperties.restRange.startIsNextDay] 开始时间是否为次日
 * @param {string} [data.dutyProperties.restRange.endTime] 结束时间
 * @param {boolean} [data.dutyProperties.restRange.endIsNextDay] 结束时间是否为次日
 * @param {boolean} data.dutyProperties.enableOffset 是否支持上下班时间偏移，早到早走，晚到晚走
 * @param {number} [data.dutyProperties.onDutyOffsetMinutes] 上班最多可晚到多少分钟
 * @param {number} [data.dutyProperties.offDutyOffsetMinutes] 下班最多可早走多少分钟
 * @param {boolean} data.dutyProperties.enableBuffer  是否允许晚到/早走不记为异常
 * @param {number} [data.dutyProperties.onDutyBufferMinutes]  上班最多晚到多少分钟
 * @param {number} [data.dutyProperties.offDutyBufferMinutes]  下班最多早走多少分钟
 * @param {number} [data.dutyProperties.offDutySeriousMinutes]  下班严重早退分钟数
 * @param {boolean} data.dutyProperties.enableCompensate  是否支持跨班次补偿
 * @param {Array} [data.dutyProperties.compensateHours] 跨班次补偿规则
 * @param {boolean} data.dutyProperties.allowContinuousWork  是否允许连续上班
 * @param {boolean} [data.dutyProperties.notAllowContinuousTime]  不允许连续上班的分钟数
 */
export const fetchEditShift = data =>
  callApi({
    endpoint: 'pm/duty/update',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 查询班次列表信息
 *  @deprecated use hrm service/fetch-shifts
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/pyafd9 API 文档}
 * @param {string} [dutyName]  班次名
 */
export const fetchAllShift = ({ dutyName, timeLimit }) =>
  callApi({
    endpoint: 'pm/duty/list',
    options: {
      params: { dutyName, timeLimit },
    },
  });

/**
 * 分页查询班制信息
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/xl9gwy API 文档}
 * @param {object} data
 * @param {string} [data.shiftsName] 班制名
 * @param {number} data.pageNum
 * @param {number} data.pageSize
 */
export const fetchShiftSysData = data =>
  callApi({
    endpoint: 'pm/shifts/page',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 删除班制信息
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/mzspf5 API 文档}
 * @param {object} data
 * @param {number} data.id  班次id
 */
export const fetchDeleteShiftSys = data =>
  callApi({
    endpoint: 'pm/shifts/delete',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 添加班制
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/tt3enr API 文档}
 * @param {object} data
 * @param {string} data.shiftsName  班制名
 * @param {string} data.shiftsType  班制类型
 * @param {number} [data.periodDays]  循环天数
 * @param {Array} [data.dailyDuty]   每日班次列表(列表中为有序班次的id)
 * @param {Array} [data.dutyIds]   关联的班次列表
 * @param {Boolean} data.enableStatutoryHoliday  法定节假日是否自动排休
 */
export const fetchCreateShiftSys = data =>
  callApi({
    endpoint: 'pm/shifts/create',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 更新班制信息
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/qlfisd API 文档}
 * @param {object} data
 * @param {number} data.id  班制id
 * @param {string} data.shiftsName  班制名
 * @param {string} data.shiftsType  班制类型
 * @param {number} [data.periodDays]  循环天数
 * @param {Array} [data.dailyDuty]   每日班次列表(列表中为有序班次的id)
 * @param {Array} [data.dutyIds]   关联的班次列表
 * @param {Boolean} data.enableStatutoryHoliday  法定节假日是否自动排休
 */
export const fetchEditShiftSys = data =>
  callApi({
    endpoint: 'pm/shifts/update',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 分页查询班组信息
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/nxd7gi API 文档}
 * @param {object} data
 * @param {string} [data.dutyGroupName] 班组名
 * @param {number} data.pageNum
 * @param {number} data.pageSize
 */
export const fetchDutyGroupData = data =>
  callApi({
    endpoint: 'pm/dutyGroup/page',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 删除班组
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/hdfykh API 文档}
 * @param {object} data
 * @param {number} data.id  班组id
 */
export const fetchDeleteDutyGroup = data =>
  callApi({
    endpoint: 'pm/dutyGroup/delete',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 查询未被关联的用户信息
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/gc4ucl API 文档}
 * @param {object} data
 * @param {string} [data.userName]  用户名
 * @param {string} data.resourceCode 资源code
 * @param {number} [data.dutyGroupId] 班组id
 */
export const fetchUserListByResource = data =>
  callApi({
    endpoint: 'pm/dutyGroup/userListByResourceAndName',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 根据资源查询用户信息
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/ek4fiw API 文档}
 * @param {object} data
 * @param {string} [data.userName]  用户名
 * @param {string} data.resourceCode 资源code
 */
export const fetchUserListByResourceInAtt = data =>
  callApi({
    endpoint: 'pm/user/userListByResourceAndName',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 添加班组
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/hl84sc API 文档}
 * @param {object} data
 * @param {string} data.groupName  班组名
 * @param {string} data.idcTag 	机房标签
 * @param {string} data.blockTag 楼栋标签
 * @param {Array} [data.staffIds] 员工信息
 */
export const fetchCreateDutyGroup = data =>
  callApi({
    endpoint: 'pm/dutyGroup/create',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 更新班组信息
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/dona3r API 文档}
 * @param {object} data
 * @param {number} data.id 班组id
 * @param {string} data.groupName  班组名
 * @param {string} data.idcTag 	机房标签
 * @param {string} data.blockTag 楼栋标签
 * @param {Array} [data.staffIds] 员工信息
 */
export const fetchEditDutyGroup = data =>
  callApi({
    endpoint: 'pm/dutyGroup/update',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 分页查询考勤组规则信息
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/xan9p9 API 文档}
 * @param {object} data
 * @param {string} [data.ruleName] 考勤组规则名
 * @param {number} data.pageNum
 * @param {number} data.pageSize
 */
export const fetchAttRuleData = data =>
  callApi({
    endpoint: 'pm/attRule/page',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 删除班组
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/gh9i5o API 文档}
 * @param {object} data
 * @param {number} data.id  班组id
 */
export const fetchAttRuleGroup = data =>
  callApi({
    endpoint: 'pm/attRule/delete',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 查询班制列表信息
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/uyc4g0 API 文档}
 * @param {string} [shiftsName]  班制名
 */
export const fetchShiftsList = shiftsName =>
  callApi({
    endpoint: 'pm/shifts/list',
    options: {
      params: { shiftsName },
    },
  });

/**
 * 查询角色列表信息
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/dc12e8 API 文档}
 * @param {string} [roleName] 角色名
 */
export const fetchRoleList = roleName =>
  callApi({
    endpoint: 'pm/role/list',
    options: {
      params: { roleName },
    },
  });

/**
 * 添加考勤组规则
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/lgpl68 API 文档}
 * @param {object} data
 * @param {boolean} data.allowContinuousWork 是否允许连续上班
 * @param {boolean} data.allowRestReplace 是否只允许休息时顶班
 * @param {boolean} data.allowSameShiftsExchange 是否只允许同班制内换班
 * @param {boolean} data.allowSameRoleExchange 是否只允许同角色内换班
 * @param {string} data.exchangeRange 换班范围
 * @param {number} [data.commutingTimeInterval] 上下班时间间隔分钟数
 * @param {boolean} data.enableSupply 是否允许补卡
 * @param {boolean} data.enableSupplyCount 是否限制补卡次数
 * @param {boolean} data.enableSupplyTime 是否限制补卡时间
 * @param {string} data.replaceRange 请假范围
 * @param {string} data.ruleName 规则名
 * @param {number} [data.supplyCountByMonth] 每月支持的最大补卡数
 * @param {number} [data.supplyValidDays] 补卡最多的相隔时间（天）
 * @param {number} [data.priorityShifts] 优先顶班班制
 * @param {Array} [data.priorityRoles] 优先顶班角色
 */
export const fetchRuleCreate = data =>
  callApi({
    endpoint: 'pm/attRule/create',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 更新考勤组规则
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/daw647 API 文档}
 * @param {object} data
 * @param {number} data.id 规则id
 * @param {boolean} data.allowContinuousWork 是否允许连续上班
 * @param {boolean} data.allowRestReplace 是否只允许休息时顶班
 * @param {boolean} data.allowSameShiftsExchange 是否只允许同班制内换班
 * @param {boolean} data.allowSameRoleExchange 是否只允许同角色内换班
 * @param {string} data.exchangeRange 换班范围
 * @param {number} [data.commutingTimeInterval] 上下班时间间隔分钟数
 * @param {boolean} data.enableSupply 是否允许补卡
 * @param {boolean} data.enableSupplyCount 是否限制补卡次数
 * @param {boolean} data.enableSupplyTime 是否限制补卡时间
 * @param {string} data.replaceRange 请假范围
 * @param {string} data.ruleName 规则名
 * @param {number} [data.supplyCountByMonth] 每月支持的最大补卡数
 * @param {number} [data.supplyValidDays] 补卡最多的相隔时间（天）
 * @param {number} [data.priorityShifts] 优先顶班班制
 * @param {Array} [data.priorityRoles] 优先顶班角色
 */
export const fetchRuleEdit = data =>
  callApi({
    endpoint: 'pm/attRule/update',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 分页查询考勤组信息
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/div5sq API 文档}
 * @param {object} data
 * @param {string} [data.attName] 	考勤组名
 * @param {number} data.pageNum
 * @param {number} data.pageSize
 */
export const fetchAttGroupData = data =>
  callApi({
    endpoint: 'pm/attGroup/page',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 删除考勤组信息
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/kyomtp API 文档}
 * @param {object} data
 * @param {number} data.id  班次id
 */
export const fetchDeleteAttGroup = data =>
  callApi({
    endpoint: 'pm/attGroup/delete',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 查询未关联的班组
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/qd76kv API 文档}
 * @param {object} data
 * @param {string} data.groupName  班组名
 * @param {string} data.idcTag  机房标签
 * @param {string} data.blockTag  楼栋标签
 * @param {string} [data.attGroupId]  考勤组id
 */
export const fetchUnlinkedDutyGroups = data =>
  callApi({
    endpoint: 'pm/dutyGroup/unlinkedDutyGroups',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 根据楼栋标签查询用户组
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/glfgxl API 文档}
 * @param {object} data
 * @param {string} [data.blockTag]  楼栋标签
 */
export const fetchGroupsByResourceCode = data =>
  callApi({
    endpoint: 'pm/group/groupsByResourceCode',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 查询排班规则列表信息
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/bbb2om API 文档}
 * @param {string} [ruleName]  排班规则名
 */
export const fetchAttRuleList = ruleName =>
  callApi({
    endpoint: 'pm/attRule/list',
    options: {
      params: { ruleName },
    },
  });

/**
 * 添加考勤组
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/uwrsr0 API 文档}
 * @param {object} data
 * @param {string} data.attName  	考勤组名
 * @param {string} data.idcTag  	机房标签
 * @param {string} data.blockTag  	楼栋标签
 * @param {number} data.shiftsId  	班制id
 * @param {number} data.attRuleId  	考勤规则id
 * @param {Array} data.userGroupIds  	考勤组负责用户组的id列表
 * @param {Array} [data.dutyGroupIdList]  班组id列表
 */
export const fetchAttGroupCreate = data =>
  callApi({
    endpoint: 'pm/attGroup/create',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 查询考勤组详情接口
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/shhnm0 API 文档}
 * @param {object} data
 * @param {number} data.attGroupId  	考勤组id
 */
export const fetchAttGroupDetail = data =>
  callApi({
    endpoint: 'pm/attGroup/detail',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 班组排班预览
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/agd7tl API 文档}
 * @param {object} data
 * @param {number} [data.scheduleStartTime]  	排班开始时间
 * @param {number} data.attGroupId  考勤组id
 * @param {number} [data.scheduleCycleMonths]  排班周期
 */
export const fetchAttSchedulePreview = data =>
  callApi({
    endpoint: 'pm/schedule/attSchedulePreview',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 *更新考勤组排班规则接口
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/qgb34u API 文档}
 * @param {object} data
 * @param {number} data.attGroupId  考勤组id
 * @param {number} [data.attStartTime]  	排班开始时间
 * @param {number} [data.scheduleCycleMonths]  	排班周期
 * @param {Array} [data.scheduleStaffList] 班组排班对应用户列表
 */
export const fetchAttGroupScheduleRuleUpdate = data =>
  callApi({
    endpoint: 'pm/attGroup/scheduleRuleUpdate',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 更新考勤组信息
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/crezcw API 文档}
 * @param {object} data
 * @param {string} data.id  考勤组id
 * @param {string} [data.attName]  	考勤组名
 * @param {string} [data.idcTag]  	机房标签
 * @param {string} [data.blockTag]  	楼栋标签
 * @param {number} [data.shiftsId]  	班制id
 * @param {string} [data.shiftsName]  	班制名
 * @param {number} [data.attRuleId]  	考勤规则id
 * @param {Array} [data.userGroupIds] 	考勤组负责用户组的id
 * @param {Array} [data.userGroupName]  考勤组负责用户组的姓名
 * @param {Array} [data.dutyGroupIdList]  班组id列表
 */
export const fetchAttGroupEdit = data =>
  callApi({
    endpoint: 'pm/attGroup/update',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 按照条件查询班组排班
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/bweegk API 文档}
 * @param {object} data
 * @param {string} [data.idcTag]  	机房标签
 * @param {string} [data.blockTag]  	楼栋标签
 * @param {number} [data.attGroupId]  	考勤组id
 * @param {number} [data.dutyGroupId]  	班组id
 * @param {number} [data.beginDate]  	开始时间
 * @param {number} [data.endDate]  		结束时间
 */
export const fetchAttGroupScheduleInMonth = data =>
  callApi({
    endpoint: 'pm/schedule/query',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 查询考勤组列表接口
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/tfpd5g API 文档}
 *
 * @deprecated Use `@manyun/hrm.service.fetch-att-groups` instead
 *
 * @param {string} [attGroupName] 考勤组名
 */
export const fetchAttGroupList = (attGroupName, blockGuids) =>
  callApi({
    endpoint: 'pm/attGroup/attGroupList',
    options: {
      method: 'POST',
      data: { attGroupName, blockGuids },
    },
  });

/**
 * 考勤统计-月度汇总
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/ke2qwh API 文档}
 * @param {object} data
 * @param {number} data.startDate 开始时间
 * @param {number} data.endDate 结束时间
 * @param {number} [data.attGroupId] 考勤组id
 * @param {number} [data.staffId] 员工id
 * @param {string} [data.blockTag] 楼栋标签
 */
export const fetchMonthlyList = data =>
  callApi({
    endpoint: 'pm/attStatistics/monthly/page',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 考勤统计-每日统计
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/qra9r0 API 文档}
 * @param {object} data
 * @param {number} data.startDate 开始时间
 * @param {number} data.endDate 结束时间
 * @param {number} [data.attGroupId] 考勤组id
 * @param {number} [data.staffId] 员工
 * @param {string} [data.blockTag] 楼栋标签
 */
export const fetchDailyList = data =>
  callApi({
    endpoint: 'pm/attStatistics/daily/page',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 导出-月度汇总
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/kgyvkq API 文档}
 * @param {object} data
 * @param {number} data.startDate 开始时间
 * @param {number} data.endDate 结束时间
 * @param {number} [data.attGroupId] 考勤组id
 * @param {number} [data.staffId] 员工
 * @param {string} [data.blockTag] 楼栋标签
 */
export const fetchExportMonthlyList = data =>
  callApi({
    endpoint: 'pm/export/monthly_statistics/list',
    options: {
      method: 'POST',
      responseType: 'blob',
      data,
    },
  });

/**
 * 导出-每日统计
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/icinar API 文档}
 * @param {object} data
 * @param {number} data.startDate 开始时间
 * @param {number} data.endDate 结束时间
 * @param {number} [data.attGroupId] 考勤组id
 * @param {number} [data.staffId] 员工id
 * @param {string} [data.blockTag] 楼栋标签
 */
export const fetchExportDailyList = data =>
  callApi({
    endpoint: 'pm/export/daily_statistics/list',
    options: {
      method: 'POST',
      responseType: 'blob',
      data,
    },
  });

/**
 * 考勤统计-打卡时间
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/kwrrly API 文档}
 * @param {object} data
 * @param {number} data.startDate 开始时间
 * @param {number} data.endDate 结束时间
 * @param {number} [data.attGroupId] 考勤组id
 * @param {number} [data.staffId] 员工id
 * @param {string} [data.blockTag] 楼栋标签
 */
export const fetchCheckList = data =>
  callApi({
    endpoint: 'pm/attStatistics/period/check/page',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 报表导出-打卡时间
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/ipu4dz API 文档}
 * @param {object} data
 * @param {number} data.startDate 开始时间
 * @param {number} data.endDate 结束时间
 * @param {number} [data.attGroupId] 考勤组id
 * @param {number} [data.staffId] 员工id
 * @param {string} [data.blockTag] 楼栋标签
 */
export const fetchExportCheckList = data =>
  callApi({
    endpoint: 'pm/export/period_check/list',
    options: {
      method: 'POST',
      responseType: 'blob',
      data,
    },
  });

/**
 *
 * 考勤统计-原始记录
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/dxmpe9 API 文档}
 * @param {object} data
 * @param {number} data.startDate 开始时间
 * @param {number} data.endDate 结束时间
 * @param {number} data.attGroupId 考勤组id
 * @param {number} data.staffId 员工id
 * @param {string} [data.blockTag] 楼栋标签
 */
export const fetchRecordList = data =>
  callApi({
    endpoint: 'pm/check/record/list',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 报表导出-原始记录
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/vxto5f API 文档}
 * @param {object} data
 * @param {number} data.startDate 开始时间
 * @param {number} data.endDate 结束时间
 * @param {number} data.attGroupId 考勤组id
 * @param {number} data.staffId 员工id
 * @param {string} [data.blockTag] 楼栋标签
 */
export const fetchExportRecordList = data =>
  callApi({
    endpoint: 'pm/export/timely_check_record/list',
    options: {
      method: 'POST',
      responseType: 'blob',
      data,
    },
  });

/**
 * 查询班次是否被排班使用
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/we1v98 API 文档}
 * @param {object} data
 * @param {number} data.id 班次id
 */
export const fetchDutyIsBeUsed = data =>
  callApi({
    endpoint: 'pm/duty/dutyIsBeUsed',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 根据班制查询考勤组列表
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/gitq04 API 文档}
 * @param {number} shiftsId 班制id
 */
export const fetchAttGroupListByShifts = shiftsId =>
  callApi({
    endpoint: 'pm/attGroup/attGroupListByShifts',
    options: {
      params: { shiftsId },
    },
  });

/**
 * 节假日工作日查询
 * {@link https://manyun.yuque.com/ewe5b3/lgi8mf/rf40mg API 文档}
 * @param {number} year 年份
 */
export const fetchStatutoryHoliday = year =>
  callApi({
    endpoint: 'pm/statutoryHoliday/list',
    options: {
      params: { year },
    },
  });
