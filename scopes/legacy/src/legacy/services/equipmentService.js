import { callApi } from './api';

/**
 * @deprecated 需要重构到 dccmService 里去
 */
export const fetchMetaCategory = async () => {
  const { response, error } = await callApi({
    endpoint: 'dccm/meta/query/device/category',
    options: {
      method: 'POST',
      data: {
        numbered: true,
      },
    },
  });

  if (error) {
    return;
  }

  return response.data;
};

/**
 * 查询设备列表
 * @deprecated use `fetch-devices` instead
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/suy3hg API 文档}
 * @param {object} data
 */
export const fetchEquipmentListPage = data =>
  callApi({
    endpoint: 'dccm/device/query',
    options: {
      method: 'POST',
      data,
    },
  });

// 导出
export const downloadEquipment = ({ deviceType, roomTag, ...data } = {}) =>
  callApi({
    endpoint: 'dccm/device/export',
    options: {
      method: 'POST',
      responseType: 'blob',
      contentType: 'application/json',
      data: {
        ...data,
        deviceTypeList: data.deviceTypeList
          ? data.deviceTypeList
          : deviceType
          ? [deviceType]
          : null,
        roomTags: data.roomTags ? data.roomTags : roomTag ? [roomTag] : null,
      },
    },
  });

// 维保状态
export const fetchWarrantyStatus = async () => {
  const { response, error } = await callApi({
    endpoint: 'dccm/get/warranty/status',
    options: {
      method: 'GET',
      data: {},
    },
  });
  if (error) {
    return;
  }
  const newList = [];
  if (response) {
    Object.keys(response).map(key => {
      newList.push({ key: key, value: response[key] });

      return newList;
    });
  }
  return newList;
};

// 下载模板
export const downloadGridModel = () =>
  callApi({
    endpoint: 'dccm/device/download',
    options: {
      responseType: 'blob',
    },
  });

// 导入
export const uploadEquipment = file =>
  callApi({
    endpoint: 'dccm/device/import',
    options: {
      method: 'POST',
      contentType: 'multipart/form-data',
      data: file,
      timeout: 5 * 60 * 1000,
    },
  });

/**
 * @param {object} params
 * @param {string} params.guid
 * @param {string} params.operatorNotes 删除原因
 */
export const deleteDeviceInfo = async params =>
  await callApi({
    endpoint: 'dccm/device/delete',
    options: {
      params,
    },
  });

// 完成
export const importFinish = async deviceInfos =>
  await callApi({
    endpoint: 'dccm/device/add',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data: deviceInfos,
    },
  });

// 资产状态
export const fetchAssetStatusStatus = async () => {
  const { response, error } = await callApi({
    endpoint: 'dccm/get/asset/status',
    options: {
      method: 'GET',
      data: {},
    },
  });
  if (error) {
    return;
  }
  const newList = [];
  if (response) {
    Object.keys(response).map(key => {
      newList.push({ key: key, value: response[key] });

      return newList;
    });
  }
  return newList;
};

/**
 * 查询设备详情
 * 接口文档：https://manyun.yuque.com/ewe5b3/sbs6q1/kphm12
 * @param {object} data 请求参数
 * @param {string} [data.guid] 设备唯一编码
 */
export const fetchDeviceInfo = data => {
  return callApi({
    endpoint: 'dccm/device/detail',
    options: {
      method: 'GET',
      params: data,
    },
  });
};

// 设备状态
export const fetchOperationStatus = async () => {
  const data = await callApi({
    endpoint: 'dccm/get/device/status',
    options: {
      method: 'GET',
      contentType: 'application/json',
    },
  });
  const status = data.response;
  const list = [];
  for (const i in status) {
    list.push({ value: status[i], key: i });
  }
  return list;
};
