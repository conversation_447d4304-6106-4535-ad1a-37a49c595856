import { callApi } from './api';

// 分页查询角色信息
export const fetchRoleList = ({ pageNo, pageSize, searchName, searchType }) =>
  callApi({
    endpoint: 'pm/role/page',
    options: {
      method: 'GET',
      params: {
        pageNo,
        pageSize,
        searchName,
        searchType,
      },
    },
  });

// 分页查询角色关联的权限信息
export const fetchJoinRoleGroup = ({ roleId }) =>
  callApi({
    endpoint: 'pm/role/permissionsTree',
    options: {
      method: 'GET',
      params: {
        roleId,
      },
    },
  });

// 查询角色详情
export const fetchRoleDetail = (roleId, roleCode) =>
  callApi({
    endpoint: 'pm/role/roleInfoDetail',
    options: {
      method: 'GET',
      params: {
        roleId,
        roleCode,
      },
    },
  });

// 删除角色
export const fetchDeleteRole = roleId =>
  callApi({
    endpoint: 'pm/role/delete',
    options: {
      method: 'POST',
      data: `roleId=${roleId}`,
    },
  });

// 分页查询角色关联的用户组信息
export const fetchJoinRoleUserGroup = ({ pageNo, pageSize, searchName, searchType, roleId }) =>
  callApi({
    endpoint: 'pm/role/groupPage',
    options: {
      method: 'GET',
      params: {
        pageNo,
        pageSize,
        searchType,
        searchName,
        roleId,
      },
    },
  });

// 解绑用户组
export const fetchDeleteRoleUserGroup = (roleId, roleUserGroupId) =>
  callApi({
    endpoint: 'pm/group/unbindRole',
    options: {
      method: 'POST',
      data: `roleId=${roleId}&groupId=${roleUserGroupId}`,
    },
  });

// 查询所有用户组
export const fetchAllUserGroup = async groupName => {
  const { response, error } = await callApi({
    endpoint: 'pm/group/list',
    options: {
      method: 'GET',
      params: {
        groupName,
      },
    },
  });
  if (error) {
    return;
  }
  let userGroupList = [];
  if (response) {
    userGroupList = response.data.map(item => {
      return { key: item.id, name: item.groupName, label: item.groupName, remarks: item.remarks };
    });
  }

  return userGroupList;
};

/**
 * Fetch roles by a name keyword.
 * @param {string} name role name keyword
 */
export const fetchRolesByName = name =>
  callApi({
    endpoint: 'pm/role/list',
    options: {
      params: {
        roleName: name,
      },
    },
  });

export const fetchConnectRoleAndUserGroup = async (roleIds, groupIds) =>
  await callApi({
    endpoint: 'pm/group/bindRoles',
    options: {
      method: 'POST',
      data: `roleIds=${roleIds}&groupIds=${groupIds}`,
    },
  });
// 解绑菜单,权限
export const fetchUnbindPowerFromRole = (roleId, permissionId) =>
  callApi({
    endpoint: 'pm/role/unbindPermission',
    options: {
      method: 'POST',
      data: `roleId=${roleId}&permissionId=${permissionId}`,
    },
  });

// 查询菜单树或菜单列表
export const fetchAllPowerTree = async () =>
  await callApi({
    endpoint: 'pm/permission/tree',
    options: {
      method: 'GET',
      params: {},
    },
  });
