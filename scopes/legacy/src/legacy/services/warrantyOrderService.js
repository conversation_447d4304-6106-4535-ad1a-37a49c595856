import { callApi } from './api';

/**
 * 查询订单列表
 * {@link https://manyun.yuque.com/ewe5b3/tbvb5l/kqlfi9 API 文档}
 * @param {object} data
 * @param {string} [data.orderNo] 订单编号
 * @param {Array} [data.idcTagList] 机房guid列表
 * @param {Array} [data.blockGuidList] 楼栋guid列表
 * @param {string} [data.contractNo] 合同单号
 * @param {string} [data.warrantyVendor] 维保厂商
 * @param {string} [data.orderStatus] 状态
 * @param {string} [data.applyStaffId] 申请人
 * @param {string} [data.applyStartTime] 申请开始时间
 * @param {string} [data.applyEndTime] 申请结束时间
 * @param {number} data.pageNum
 * @param {number} data.pageSize
 */
export const fetchWarrantyOrderList = data => {
  return callApi({
    endpoint: 'dccm/warranty/page',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 撤销维保订单
 * {@link https://manyun.yuque.com/ewe5b3/tbvb5l/pvqrgr API 文档}
 * @param {object} data
 * @param {string} data.orderNo 订单编号
 * @param {string} [data.reason] 撤销原因
 */
export const revokeWarrantyOrder = data => {
  return callApi({
    endpoint: 'dccm/warranty/revoke',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 重新发起维保订单
 * {@link https://manyun.yuque.com/ewe5b3/tbvb5l/cfuiwc API 文档}
 * @param {object} data
 * @param {string} data.orderNo 订单编号
 * @param {string} data.orderTitle 订单标题
 * @param {string} data.contractNo 合同编号
 * @param {string} data.idcTag 机房
 * @param {string} data.blockGuid 楼栋
 * @param {number} data.warrantyFee 维保费用
 * @param {string} data.warrantyVendor 维保厂商
 * @param {string} data.startDate 维保开始时间
 * @param {string} data.endDate 维保结束时间
 * @param {string} [data.remarks] 备注
 * @param {Array} data.deviceGuidList 设备guid列表
 */
export const rePostWarrantyOrder = data => {
  return callApi({
    endpoint: 'dccm/warranty/rePost',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 新建订单
 * {@link https://manyun.yuque.com/ewe5b3/tbvb5l/xcov5u API 文档}
 * @param {object} data
 * @param {string} data.orderTitle 订单标题
 * @param {string} data.contractNo 合同编号
 * @param {string} data.idcTag 机房
 * @param {string} data.blockGuid 楼栋
 * @param {number} data.warrantyFee 维保费用
 * @param {string} data.warrantyVendor 维保厂商
 * @param {string} data.startDate 维保开始时间
 * @param {string} data.endDate 维保结束时间
 * @param {string} [data.remarks] 备注
 * @param {Array} data.deviceGuidList 设备guid列表
 */
export const createWarrantyOrder = data => {
  return callApi({
    endpoint: 'dccm/warranty/create',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 查询维保设备列表
 * {@link https://manyun.yuque.com/ewe5b3/tbvb5l/faicgx API 文档}
 * @param {object} data
 * @param {string} [data.orderNo] 订单编号
 * @param {number} data.pageNum
 * @param {number} data.pageSize
 */
export const fetchWarrantyDevicePage = data => {
  return callApi({
    endpoint: 'dccm/warranty/device/page',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 维保订单详情
 * {@link https://manyun.yuque.com/ewe5b3/tbvb5l/pvqrgr API 文档}
 * @param {object} data
 * @param {string} data.orderNo 订单编号
 */
export const fetchWarrantyDetail = data => {
  return callApi({
    endpoint: 'dccm/warranty/detail',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 查询维保清单设备列表
 * {@link https://manyun.yuque.com/ewe5b3/tbvb5l/di9xb6 API 文档}
 * @param {object} data
 * @param {string} data.orderNo 订单编号
 */
export const fetchWarrantyDeviceList = data => {
  return callApi({
    endpoint: 'dccm/warranty/device/list',
    options: {
      method: 'POST',
      data,
    },
  });
};
