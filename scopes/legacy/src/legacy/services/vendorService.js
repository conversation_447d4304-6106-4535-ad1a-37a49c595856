import moment from 'moment';

import { callApi } from './api';

/**
 * 查询厂商列表
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/occv3v API 文档}
 * @param {object} params 请求参数
 * @param {string} [params.vendorCode] 简称
 * @param {string} [params.contactName] 联系人姓名
 * @param {string} [params.contactMobile] 联系人电话
 * @param {string} [params.operatorId] 操作人
 * @param {string} [params.createTimeStart] 开始时间
 * @param {string} [params.createTimeEnd] 结束时间
 * @param {string} params.pageNum 页码
 * @param {string} params.pageSize 页数
 */
export const fetchVendorList = ({
  vendorCode,
  vendorLevel,
  contactName,
  contactMobile,
  operatorId,
  createTimeStart,
  createTimeEnd,
  pageNum,
  pageSize,
}) => {
  return callApi({
    endpoint: 'dccm/vendor/query',
    options: {
      method: 'POST',
      data: {
        vendorCode,
        vendorLevel,
        contactName,
        contactMobile,
        operatorId,
        createTimeStart: createTimeStart
          ? Number(moment(createTimeStart).unix() + '000')
          : undefined,
        createTimeEnd: createTimeEnd ? Number(moment(createTimeEnd).unix() + '000') : undefined,
        pageNum,
        pageSize,
      },
    },
  });
};

/**
 * 查询厂商详情
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/mfqrlk API 文档}
 *  @param {string} id
 */
export const getVendorDetail = data => {
  return callApi({
    endpoint: 'dccm/vendor/query/detail',
    options: {
      method: 'POST',
      data,
    },
  });
};

/**
 * 新增厂商
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/ds61ed API 文档}
 * @param {object} params 请求参数
 * @param {string} params.vendorLevel 级别
 * @param {string} params.vendorCode 简称
 * @param {string} params.vendorName 全称
 * @param {string} params.contactName 联系人姓名
 * @param {string} params.contactMobile 联系人电话
 * @param {string} params.contactEmail 联系人邮箱
 * @param {string} [params.description] 备注
 */
export const addVendor = data =>
  callApi({
    endpoint: 'dccm/vendor/add',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 编辑厂商
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/lgifi8 API 文档}
 * @param {object} params 请求参数
 * @param {string} params.id
 * @param {string} params.vendorLevel 级别
 * @param {string} params.vendorName 全称
 * @param {string} params.contactName 联系人姓名
 * @param {string} params.contactMobile 联系人电话
 * @param {string} params.contactEmail 联系人邮箱
 * @param {string} [params.description] 备注
 */
export const editVendor = data =>
  callApi({
    endpoint: 'dccm/vendor/update',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 获取厂商
 * 接口文档：https://manyun.yuque.com/ewe5b3/sbs6q1/zg98kf
 */
export const fetchVendorCode = async data => {
  const { response } = await callApi({
    endpoint: 'dccm/vendor/query/code',
    options: {
      method: 'POST',
      data,
    },
  });
  let newList = [];
  if (response) {
    Object.keys(response.data).map(item => {
      newList.push({ key: item, value: response.data[item] });
      return newList;
    });
  }
  return newList;
};

/**
 * 删除厂商
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/rsv9r2 API 文档}
 * @param {object} params 请求参数
 * @param {string} params.id
 * @param {string} params.operatorNotes 操作备注
 */
export const fetchDeleteVendor = data =>
  callApi({
    endpoint: 'dccm/vendor/delete',
    options: {
      params: data,
    },
  });

/**
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/gca2x5 API 文档}
 * @param {object} params 请求参数
 * @param {string} params.deviceType
 */
export const fetchVendoeModal = data =>
  callApi({
    endpoint: 'dccm/model/query/meta',
    options: {
      method: 'POST',
      data,
    },
  });
