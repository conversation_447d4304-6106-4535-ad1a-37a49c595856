import { qs } from '@manyun/base-ui.util.query-string';

import { callApi } from './api';

// 分页查询角色信息
export const fetchRolePage = ({ pageNo, pageSize, searchName, searchType }) =>
  callApi({
    endpoint: 'pm/permission/page',
    options: {
      method: 'GET',
      params: {
        pageNo,
        pageSize,
        searchName,
        searchType,
      },
    },
  });

// 删除菜单接口

export const fetchDeletePower = ({ permissionId }) =>
  callApi({
    endpoint: 'pm/permission/delete',
    options: {
      method: 'POST',
      data: `permissionId=${permissionId}`,
    },
  });

// 添加权限
export const fetchCreatePower = data =>
  callApi({
    endpoint: 'pm/permission/add',
    options: {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      method: 'POST',
      data: qs.stringify(data),
    },
  });

// 更新
export const fetchUpdatePower = data => {
  return callApi({
    endpoint: 'pm/permission/update',
    options: {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      method: 'POST',
      data: qs.stringify(data),
    },
  });
};
// 查询菜单树或菜单列表
export const fetchAllPowerTreeSelect = async () =>
  await callApi({
    endpoint: 'pm/permission/tree',
    options: {
      method: 'GET',
      params: {},
    },
  });
