import { callApi } from './api';

/**
 * 查询待审批列表
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/oqi0yb API 文档}
 * @param {object} data
 * @param {string} [data.condition] 节点名称/ip(模糊)
 * @param {number} data.pageNum
 * @param {number} data.pageSize
 */
export const fetchApproveCenterWaitPage = data =>
  callApi({
    endpoint: 'workflow/instance/wait/handle',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data: data,
    },
  });

/**
 * 查询已审批列表
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/oqi0yb API 文档}
 * @param {object} data
 * @param {string} [data.condition] 节点名称/ip(模糊)
 * @param {number} data.pageNum
 * @param {number} data.pageSize
 */
export const fetchApproveCenterAlreadyPage = data =>
  callApi({
    endpoint: 'workflow/instance/already/handle',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data: data,
    },
  });

/**
 * 查询我的发起列表
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/oqi0yb API 文档}
 * @param {object} data
 * @param {string} [data.condition] 节点名称/ip(模糊)
 * @param {number} data.pageNum
 * @param {number} data.pageSize
 */
export const fetchApproveCenterMinePage = data =>
  callApi({
    endpoint: 'workflow/instance/create/by/me',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data: data,
    },
  });

/**
 * 审批撤回
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/oqi0yb API 文档}
 * @param {object} data
 * @param {string} [data.processInstanceCode] 审批ID
 */
export const revokeApproveProcess = data =>
  callApi({
    endpoint: 'workflow/process/inst/revoke',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data: data,
    },
  });

/**
 * 审批
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/oqi0yb API 文档}
 * @param {object} data
 * @param {string} [data.processInstanceCode] 审批ID
 */
export const taskApproveProcess = data =>
  callApi({
    endpoint: 'workflow/task/approval',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data: data,
    },
  });

/**
 * 转交
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/oqi0yb API 文档}
 * @param {object} data
 * @param {string} [data.processInstanceCode] 审批ID
 */
export const redirectApproveProcess = data =>
  callApi({
    endpoint: 'workflow/task/redirect',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data: data,
    },
  });
