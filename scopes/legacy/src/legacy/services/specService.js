import { callApi } from './api';

/**
 * 查询规格
 * @deprecated use fetchSpecs('@manyun/resource-hub.service.fetch-specs') instead
 * {@link http://172.16.0.17:13000/project/140/interface/api/15920 API 文档}
 * @param {object} params 请求参数
 * @param {string} params.deviceType 设备类型
 * @param {string} [params.modelCode] 型号
 */
export const fetchSpecList = ({ deviceType, modelId }) => {
  return callApi({
    endpoint: 'dccm/spec/query',
    options: {
      method: 'POST',
      data: { deviceType, modelId },
    },
  });
};

/**
 * 新增规格
 * @deprecated use createSpec('@manyun/resource-hub.service.create-specs') instead
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/wby7u6 API 文档}
 * @param {object} params 请求参数
 * @param {string} params.specCode 规格code
 * @param {string} params.specName 规格名称
 * @param {string} params.specUnit 单位
 * @param {string} params.deviceType 设备类型
 * @param {string} params.required 是否必填
 * @param {string} params.valueType 规格值类型
 * @param {string} [params.description] 备注
 */
export const addSpec = data =>
  callApi({
    endpoint: 'dccm/spec/add',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 编辑规格
 * @deprecated use updateSpec('@manyun/resource-hub.service.update-specs') instead
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/px9pa0 API 文档}
 * @param {object} params 请求参数
 * @param {string} params.id
 * @param {string} params.unit 单位
 * @param {string} params.required 是否必填
 * @param {string} [params.description] 备注
 */
export const editSpec = data =>
  callApi({
    endpoint: 'dccm/spec/update',
    options: {
      method: 'POST',
      data,
    },
  });

/**
 * 删除规格
 * @deprecated Use `deleteSpec from '@resource-hub/service.delete-spec` instead
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/ilkzr5 API 文档}
 * @param {object} params 请求参数
 * @param {string} params.id
 * @param {string} params.operatorNotes 操作备注
 */
export const deleteSpec = ({ id, operatorNotes }) =>
  callApi({
    endpoint: 'dccm/spec/delete',
    options: {
      params: { id, operatorNotes },
    },
  });

// 获取规格数量
export const fetchDeviceTypePointCountMap = () => callApi({ endpoint: 'dccm/spec/query/num' });
