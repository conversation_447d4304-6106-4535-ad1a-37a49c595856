import { callApi } from './api';

/**
 * 查询采集服务节点列表
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/oqi0yb API 文档}
 * @param {object} data
 * @param {string} [data.condition] 节点名称/ip(模糊)
 * @param {number} data.pageNum
 * @param {number} data.pageSize
 */
export const fetchServerNodePage = data =>
  callApi({
    endpoint: 'dccm/server/node/page/query',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data,
    },
  });

/**
 * 查询采集服务节点详情
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/oqi0yb API 文档}
 * @param {string} id
 */
export const fetchServerNodeDetail = id =>
  callApi({
    endpoint: 'dccm/server/node/detail/query',
    options: {
      method: 'GET',
      params: {
        id: id,
      },
    },
  });

/**
 * 批量查询通道列表
 * {@link https://manyun.yuque.com/ewe5b3/sbs6q1/kguhhd#TQ1Fs API 文档}
 * @param {object} data
 * @param {string} [data.channelIds] 通道ids
 */
export const fetchChannelList = data =>
  callApi({
    endpoint: 'dccm/channel/batch/query',
    options: {
      method: 'POST',
      contentType: 'application/json',
      data,
    },
  });
