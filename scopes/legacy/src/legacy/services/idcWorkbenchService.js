import { callApi } from './api';

/**
 * 查询机房下的楼与包间
 * {@link https://manyun.yuque.com/ewe5b3/front-end/mr7h01 API 文档}
 * @param {string} idcTag
 * @returns {Promise<{ error: string; response: null|{idcTag:string;blockRoomTags:null|{[blockTag: string]: string[]}}}>}
 */
export const fetchBlockRoom = idcTag =>
  callApi({
    endpoint: `dccm/view/idc/${idcTag}`,
    options: {
      method: 'GET',
    },
  });

/**
 * 查询公告通知
 * 接口文档：http://121.40.158.1:8080/swagger-ui.html#/%E5%85%AC%E5%91%8A%E7%B1%BB/permissionTreeUsingGET
 * @param {object} params
 * @param {string} params.idcTag
 */
export const fetchNoticeList = ({ idcTag }) => {
  return callApi({
    endpoint: 'pm/notice/list',
    options: {
      method: 'GET',
      params: {
        idcTag,
        currentTime: Date.now(),
      },
    },
  });
};
