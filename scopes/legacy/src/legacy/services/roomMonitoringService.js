import uniq from 'lodash/uniq';
import { schema } from 'normalizr';

import { callApi } from './api';

const infrastructureDataSchema = {
  roomDevices: new schema.Array(
    new schema.Entity(
      'roomDevices',
      {},
      {
        idAttribute: 'extendPosition',
        processStrategy(device) {
          return [device];
        },
        mergeStrategy(deviceA, deviceB) {
          return [...deviceA, ...deviceB];
        },
      }
    )
  ),
  roomGrids: new schema.Array(
    new schema.Entity(
      'roomGrids',
      {},
      {
        idAttribute: 'columnTag',
        processStrategy: ({ columnTag, gridTag, powerModel }) => ({
          gridTags: [gridTag],
          powerModels: [powerModel],
        }),
        mergeStrategy: (
          { gridTags: gridTagsA, powerModels: powerModelsA },
          { gridTags: gridTagsB, powerModels: powerModelsB }
        ) => ({
          gridTags: [...gridTagsA, ...gridTagsB],
          powerModels: [...powerModelsA, ...powerModelsB],
        }),
      }
    )
  ),
};

export const fetchInfrastructureData = async ({ idc, block, room }) => {
  const roomGuid = `${idc}.${block}.${room}`;
  const { response, error } = await callApi(
    { endpoint: `dccm/view/room/${roomGuid}` },
    infrastructureDataSchema
  );
  if (error) {
    return { error };
  }
  const { entities, result } = response;
  result.roomGrids = uniq(result.roomGrids).sort();
  result.roomGrids.forEach(roomGrid => {
    const { gridTags, powerModels } = entities.roomGrids[roomGrid];
    entities.roomGrids[roomGrid] = {
      gridTags: gridTags.sort(),
      powerModels: uniq(powerModels),
    };
  });
  return { response: { entities, result } };
};

/**
 * 查询包间客户
 * {@link https://manyun.yuque.com/ewe5b3/front-end/old0fy API 文档}
 * @param {*} param0
 */
export const fetchRoomCustomers = async ({ idc, block, room, pageNo, pageSize }) => {
  const roomGuid = `${idc}.${block}.${room}`;

  const { response, error } = await callApi({
    endpoint: 'dccm/view/room/customer/query',
    options: {
      params: {
        roomGuid,
        pageSize,
        pageNum: pageNo,
      },
    },
  });

  if (error || !response) {
    return { error };
  }

  const { data, total } = response;
  const newData = data
    .map(({ roomGridCount, blockGridCount, ...rest }) => {
      const impactFactor = roomGridCount / blockGridCount;

      return {
        impactFactor,
        roomGridCount,
        blockGridCount,
        ...rest,
      };
    })
    .sort((a, b) => b.impactFactor - a.impactFactor);

  return { response: { data: newData, total } };
};
