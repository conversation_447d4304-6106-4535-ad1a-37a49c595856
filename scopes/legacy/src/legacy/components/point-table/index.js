import React from 'react';

import { Radio } from '@manyun/base-ui.ui.radio';

import { TinyTable } from '@manyun/dc-brain.legacy.components';

/**
 * 封装于 TinyTable 组件。
 * @typedef {object} Props
 * @property {Array} props.points 测点 数据源
 * @property {Array} props.selectedPointRowKeys 选择的测点信息
 * @property {function} props.setSelectedPoints  更新选择的测点数据方法
 */

export default function PointTable({
  points,
  deviceType,
  selectedPointRowKeys,
  setSelectedPoints,
}) {
  return (
    <TinyTable
      rowKey="pointCode"
      align="left"
      columns={getColumns({
        selectedPointRowKeys,
        setSelectedPoints,
      })}
      dataSource={points}
      pagination={false}
      rowSelection={{
        selectedRowKeys: selectedPointRowKeys.length
          ? selectedPointRowKeys.map(({ pointCode }) => pointCode)
          : [],
        onChange: (keys, rows) => {
          if (!rows.length) {
            setSelectedPoints([]);
            return;
          }
          let selectCodes = [];
          if (selectedPointRowKeys.length) {
            selectCodes = selectedPointRowKeys.map(({ pointCode }) => pointCode);
          }
          const options = rows.map(({ pointCode, pointName, dataType, unit }) => {
            const code = pointCode;
            if (selectCodes.includes(code)) {
              const row = selectedPointRowKeys.filter(({ pointCode }) => pointCode === code);
              return row[0];
            }
            return {
              pointCode: code,
              pointName: pointName,
              dataType,
              unit,
              deviceType: deviceType,
              isMeterRead: false,
            };
          });
          setSelectedPoints(options);
        },
      }}
    />
  );
}

const getColumns = ({ selectedPointRowKeys, setSelectedPoints }) => [
  {
    title: '',
    key: 'pointCode',
  },
  {
    title: '测点名称',
    dataIndex: 'pointName',
  },
  {
    title: '测点类型',
    dataIndex: 'dataType',
  },
  {
    title: '是否需要抄表',
    dataIndex: 'isMeterRead',
    render: (_, record) => {
      let radioValue = false;
      let disabled = true;
      if (selectedPointRowKeys.length) {
        selectedPointRowKeys.forEach(({ pointCode, isMeterRead }) => {
          if (pointCode === record.pointCode) {
            radioValue = isMeterRead;
            disabled = false;
          }
        });
      }

      return (
        <Radio.Group
          disabled={disabled}
          value={radioValue}
          onChange={e => {
            const newData = selectedPointRowKeys.map(item => {
              if (item.pointCode === record.pointCode) {
                return { ...item, isMeterRead: e.target.value };
              }
              return item;
            });
            setSelectedPoints(newData);
          }}
        >
          <Radio value={true}>是</Radio>
          <Radio value={false}>否</Radio>
        </Radio.Group>
      );
    },
  },
];
