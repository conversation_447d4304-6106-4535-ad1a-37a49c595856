import React from 'react';

export function Cards() {
  return (
    <svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor">
      <path d="M512 542.72a32.329143 32.329143 0 0 1-12.8-2.925714L91.355429 334.116571a28.598857 28.598857 0 0 1 0-51.931428l407.917714-205.531429a28.818286 28.818286 0 0 1 25.6 0l407.844571 205.750857a29.257143 29.257143 0 0 1 16.384 24.502858c0 10.971429-6.070857 20.992-15.798857 26.185142L525.385143 539.940571A36.571429 36.571429 0 0 1 512 542.72zM168.813714 306.980571L512 481.645714l343.186286-174.811428L512 132.096l-343.186286 174.811429z"></path>
      <path d="M512 744.96a23.771429 23.771429 0 0 1-12.8 0L91.355429 539.940571a29.184 29.184 0 1 1 25.673142-52.443428L512 683.885714l395.044571-199.241143a29.110857 29.110857 0 1 1 25.6 52.516572l-407.844571 205.019428a24.210286 24.210286 0 0 1-12.8 2.852572z"></path>
      <path d="M512 950.637714a32.329143 32.329143 0 0 1-12.8-2.925714l-407.844571-205.677714a29.184 29.184 0 1 1 26.258285-51.931429L512 889.417143l395.044571-198.656a28.379429 28.379429 0 0 1 38.985143 12.873143 29.257143 29.257143 0 0 1-12.8 39.058285l-407.844571 205.019429a38.765714 38.765714 0 0 1-13.385143 2.925714z"></path>
    </svg>
  );
}

Cards.className = 'tinyicon-cards';

export default Cards;
