import React from 'react';

export function OpenBook() {
  return (
    <svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor">
      <path d="M511.3 917.7c-11.2 0-22.4-2.2-32.8-6.6-109.8-46.3-233.7-65.9-358.3-56.6-6.9 0.5-13.8-1.9-18.9-6.6-5.1-4.7-8-11.4-8-18.3V132.2c0-13.1 10.1-24 23.1-24.9 99.3-7.4 201.2 2.9 294.8 29.8 13.3 3.8 20.9 17.7 17.1 30.9s-17.7 20.9-30.9 17.1c-80.7-23.2-168.1-33.3-254.1-29.4V803c123.4-5.3 245.4 15.9 354.6 61.9 8.4 3.5 18.4 3.5 26.8 0 109.1-46 231.2-67.3 354.6-61.9V155.4c-108.8-5.6-216.8 20-290.3 43.4-31.5 10-52.7 34.9-52.7 61.8v522.1c0 13.8-11.2 25-25 25s-25-11.2-25-25V260.6c0-24.5 8.6-48 24.8-67.8 15.7-19.2 37.3-33.5 62.7-41.6 83-26.4 208.1-55.5 332.8-43.9 12.9 1.2 22.7 12 22.7 24.9v697.4c0 7-2.9 13.6-8 18.3-5.1 4.7-11.9 7.1-18.9 6.6-124.6-9.3-248.5 10.3-358.3 56.6-10.4 4.4-21.6 6.6-32.8 6.6z"></path>
    </svg>
  );
}

OpenBook.className = 'tinyicon-open-book';

export default OpenBook;
