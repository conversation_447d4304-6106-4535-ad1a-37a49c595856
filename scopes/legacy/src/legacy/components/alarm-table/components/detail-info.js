import React from 'react';
import { Link } from 'react-router-dom';

import { generateRoomMonitoringUrl } from '@manyun/monitoring.route.monitoring-routes';
import { AlarmLevelText } from '@manyun/monitoring.ui.alarm-level-text';
import { generateDeviceRecordRoutePath } from '@manyun/resource-hub.route.resource-routes';

import { DIErrorTooltip, TinyDescriptions, TinyModal } from '@manyun/dc-brain.legacy.components';
import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';
import * as urlsUtil from '@manyun/dc-brain.legacy.utils/urls';

import { AlarmStatus, AlarmTypeText } from '../constants/index';

export default function DetailInfo({
  visible,
  alarmInfo,
  className,
  onCancel,
  idc,
  metaCategoryEntities,
}) {
  const getMergeView = () => {
    let mergeCountView = '0个';
    if (alarmInfo.mergeCount > 0) {
      mergeCountView = (
        <span>
          <Link to="/">{alarmInfo.mergeCount}</Link>个
        </span>
      );
    }
    return mergeCountView;
  };
  const getTriggerSnapshot = () => {
    // let text = DISABLED_POINT_VALUE_PLACEHOLDER;
    // if (alarmInfo.dataType && alarmInfo.dataType.code === 'DI') {
    //   text = alarmInfo.validLimits[alarmInfo.triggerSnapshot] || DISABLED_POINT_VALUE_PLACEHOLDER;
    // }
    // if (alarmInfo.dataType && alarmInfo.dataType.code === 'AI') {
    //   text = alarmInfo.triggerSnapshot
    //     ? `${alarmInfo.triggerSnapshot}${alarmInfo.unit ? alarmInfo.unit : ''}`
    //     : DISABLED_POINT_VALUE_PLACEHOLDER;
    // }
    // return text;

    let text = '';
    if (alarmInfo.triggerSnapshot === null) {
      return <span>{BLANK_PLACEHOLDER}</span>;
    }
    if (alarmInfo.dataType && alarmInfo.dataType.code === 'DI') {
      if (alarmInfo.validLimits[Number(alarmInfo.triggerSnapshot)]) {
        text = alarmInfo.validLimits[Number(alarmInfo.triggerSnapshot)];
      } else {
        return <DIErrorTooltip title={alarmInfo.triggerSnapshot} />;
      }
    }
    if (alarmInfo.dataType && alarmInfo.dataType.code === 'AI') {
      text = alarmInfo.triggerSnapshot
        ? `${alarmInfo.triggerSnapshot}${alarmInfo.unit ? alarmInfo.unit : ''}`
        : BLANK_PLACEHOLDER;
    }
    return <span>{text}</span>;
  };

  const getPointValue = () => {
    // let text = DISABLED_POINT_VALUE_PLACEHOLDER;
    // if (alarmInfo.dataType && alarmInfo.dataType.code === 'DI') {
    //   text = alarmInfo.validLimits[alarmInfo.pointValue];
    // }
    // if (alarmInfo.dataType && alarmInfo.dataType.code === 'AI') {
    //   text = alarmInfo.pointValue
    //     ? `${alarmInfo.pointValue}${alarmInfo.unit ? alarmInfo.unit : ''}`
    //     : '';
    // }
    // return text;

    let text = '';
    if (alarmInfo.dataType && alarmInfo.pointValue === null) {
      return <span>{BLANK_PLACEHOLDER}</span>;
    }
    if (alarmInfo.dataType && alarmInfo.dataType.code === 'DI') {
      if (!alarmInfo.validLimits[alarmInfo.pointValue]) {
        return <DIErrorTooltip title={alarmInfo.pointValue} />;
      }
      text = alarmInfo.validLimits[alarmInfo.pointValue];
    }
    if (alarmInfo.dataType && alarmInfo.dataType.code === 'AI') {
      text = `${alarmInfo.pointValue}${alarmInfo.unit ? alarmInfo.unit : ''}`;
    }
    return text;
  };
  return (
    <TinyModal
      visible={visible}
      footer={null}
      width="808px"
      bodyStyle={{ height: '560px', overflowX: 'hidden', overflowY: 'scroll' }}
      title={`${
        metaCategoryEntities[alarmInfo.deviceType] &&
        metaCategoryEntities[alarmInfo.deviceType].metaName
      }-${alarmInfo.pointCodeName}告警`}
      onCancel={onCancel}
      onClose={onCancel}
    >
      <TinyDescriptions
        bordered
        className={className}
        descriptionsItems={[
          {
            label: '告警类型',
            value: <AlarmTypeText alarmType={alarmInfo.alarmType} />,
          },
          {
            label: '告警级别',
            value: <AlarmLevelText code={alarmInfo?.alarmLevel} />,
          },
          {
            label: '告警状态',
            value: <AlarmStatus triggerStatus={alarmInfo.triggerStatus} type="tag" />,
          },
          { label: '机房', value: alarmInfo.idcTag },
          { label: '楼号', value: alarmInfo.blockTag },
          {
            label: '包间',
            value: alarmInfo.roomTag ? (
              <Link
                onClick={() => {
                  window.open(
                    generateRoomMonitoringUrl({
                      idc: idc,
                      block: alarmInfo.blockTag,
                      room: alarmInfo.roomTag,
                    })
                  );
                }}
              >
                {alarmInfo.roomTag}
              </Link>
            ) : (
              BLANK_PLACEHOLDER
            ),
          },
          {
            label: '机列',
            value: alarmInfo.columnTag ? (
              <Link
                onClick={() => {
                  window.open(
                    generateRoomMonitoringUrl({
                      idc: idc,
                      block: alarmInfo.blockTag,
                      room: alarmInfo.roomTag,
                    })
                  );
                }}
              >
                {alarmInfo.columnTag}
              </Link>
            ) : (
              '无'
            ),
          },
          { label: '机柜', value: alarmInfo.gridTag || '无' },
          { label: '处理人', value: alarmInfo.confirmByName },
          // {
          //   label: '告警大类',
          //   value:
          //     metaQueryAlarmTypeEntities[alarmInfo.topCategory] &&
          //     metaQueryAlarmTypeEntities[alarmInfo.topCategory].metaName,
          // },
          // {
          //   label: '告警小类',
          //   value:
          //     metaQueryAlarmTypeEntities[alarmInfo.secondCategory] &&
          //     metaQueryAlarmTypeEntities[alarmInfo.secondCategory].metaName,
          // },

          { label: '收敛告警数', value: getMergeView(), span: 2 },
          { label: '通知次数', value: alarmInfo.notifyCount },
          {
            label: '告警对象',
            value: alarmInfo.deviceName ? (
              <Link
                to={generateDeviceRecordRoutePath({
                  guid: alarmInfo.deviceGuid,
                })}
              >
                {alarmInfo.deviceName}
              </Link>
            ) : (
              BLANK_PLACEHOLDER
            ),
          },
          {
            label: '设备标签',
            value: alarmInfo.deviceLabel,
          },
          {
            label: '三级分类',
            value:
              metaCategoryEntities[alarmInfo.deviceType] &&
              metaCategoryEntities[alarmInfo.deviceType].metaName,
          },
          {
            label: '告警测点名称',
            value: alarmInfo.pointCodeName,
          },
          { label: '测点告警值', value: getTriggerSnapshot() },
          { label: '测点现值 ', value: getPointValue() },
          {
            label: '告警开始时间',
            value: alarmInfo.gmtCreate,
          },
          { label: '最新告警时间', value: alarmInfo.triggerTime },
          {
            label: '命中规则',
            value:
              alarmInfo.itemType === 'DYNAMIC_BASELINE' ? (
                alarmInfo.threshold
              ) : (
                <Link
                  to={urlsUtil.generateSpecificMonitorItemConfigLocation({
                    configId: alarmInfo.itemId,
                    deviceType: alarmInfo.deviceType,
                    pointCode: alarmInfo.pointCode,
                  })}
                >
                  查看
                </Link>
              ),
          },
          {
            label: '影响机柜数',
            value: alarmInfo.gridCount === null ? '无' : alarmInfo.gridCount,
          },
          {
            label: '影响设备数',
            value: alarmInfo.deviceCount !== null ? alarmInfo.deviceCount : '无',
          },
          {
            label: '客户影响面',
            value:
              alarmInfo.customerCount === null ? (
                '无'
              ) : (
                <Link to="\">{alarmInfo.customerCount}</Link>
              ),
          },
          { label: '告警内容', value: alarmInfo.notifyContent, span: 3 },
        ]}
      />
    </TinyModal>
  );
}
