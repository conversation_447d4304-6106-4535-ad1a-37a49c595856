import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import styled from 'styled-components';

import { Button } from '@manyun/base-ui.ui.button';
import { Modal } from '@manyun/base-ui.ui.modal';
import { PointsLine } from '@manyun/monitoring.chart.points-line';
import { PointsStateLine } from '@manyun/monitoring.chart.points-state-line';
import { generateRoomMonitoringUrl } from '@manyun/monitoring.route.monitoring-routes';
import { AlarmLevelText } from '@manyun/monitoring.ui.alarm-level-text';
import { AlarmReasonText } from '@manyun/monitoring.ui.alarm-reason-text';
import { generateDeviceRecordRoutePath } from '@manyun/resource-hub.route.resource-routes';

import { DIErrorTooltip, Ellipsis, TinyTable } from '@manyun/dc-brain.legacy.components';
import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';
import { POINT_DATA_TYPE_CODE_MAP } from '@manyun/dc-brain.legacy.constants/point';
import * as urlsUtil from '@manyun/dc-brain.legacy.utils/urls';

import DetailInfo from './components/detail-info';
import { AlarmStatus, AlarmTypeText, ProcessingState } from './constants/index';

const DEFAULT_NOTIFY_CONTENT_CELL_STYLE = { display: 'inline-block', minWidth: 500, maxWidth: 500 };

const columns = (
  metaCategoryEntities,
  operation,
  onAlarmInfoVisible,
  showColumns,
  setPointValueInfo,
  { notifyContentCellStyle, fixedColumns }
) => {
  let list = [
    {
      title: '告警ID',
      dataIndex: 'id',
      render: (text, record) => (
        <Button
          style={{ padding: 0, height: 'auto' }}
          type="link"
          onClick={() => onAlarmInfoVisible(record)}
        >
          {text}
        </Button>
      ),
    },
    {
      title: '类型',
      dataIndex: 'alarmType',
      fixed: 'left',
      render: text => <AlarmTypeText alarmType={text} />,
      visible: true,
    },

    {
      title: '级别',
      dataIndex: 'alarmLevel',
      visible: true,
      render: text => <AlarmLevelText code={text.toString()} />,
    },

    {
      title: '楼号',
      dataIndex: 'blockTag',
      visible: true,
    },
    {
      title: '包间',
      dataIndex: 'roomTag',
      visible: true,
      render: (text, record) => {
        if (!text) {
          return BLANK_PLACEHOLDER;
        }
        return (
          <Link
            onClick={() => {
              window.open(
                generateRoomMonitoringUrl({
                  idc: record.idcTag,
                  block: record.blockTag,
                  room: record.roomTag,
                })
              );
            }}
          >
            {text}
          </Link>
        );
      },
    },
    {
      title: '告警内容',
      dataIndex: 'notifyContent',
      defaultVisible: true,
      render: (text, record) => {
        return {
          children: (
            <div
              onClick={event => {
                onAlarmInfoVisible(record);
                event.stopPropagation();
                event.nativeEvent.stopImmediatePropagation();
              }}
            >
              <Ellipsis lines={3} tooltip>
                {text}
              </Ellipsis>
            </div>
          ),
          props: {
            style: {
              padding: '0 7px',
              fontSize: '12px',
              margin: '0 -7px',
              cursor: 'pointer',
              color: 'var(--color-reference)',
            },
          },
        };
      },
    },
    {
      title: '告警对象',
      dataIndex: 'deviceName',
      ellipsis: true,
      defaultVisible: true,
      render: (text, { deviceGuid }) => {
        if (text) {
          return <Link to={generateDeviceRecordRoutePath({ guid: deviceGuid })}>{text}</Link>;
        }
        return BLANK_PLACEHOLDER;
      },
    },
    {
      title: '设备标签',
      dataIndex: 'deviceLabel',
      ellipsis: true,
      defaultVisible: true,
      render: text => {
        return text || BLANK_PLACEHOLDER;
      },
    },
    {
      title: '收敛告警数',
      dataIndex: 'mergeCount',
      render: (text, record) => {
        let mergeCountView = '0个';
        if (text > 0) {
          mergeCountView = (
            <span>
              <Link to="/">{text}</Link>个
            </span>
          );
        }
        return mergeCountView;
      },
    },
    {
      title: '测点名称',
      dataIndex: 'pointCodeName',
      ellipsis: true,
      visible: true,
    },
    {
      title: '告警原因',
      dataIndex: ['triggerCondition', 'name'],
      ellipsis: true,
      defaultVisible: true,
    },
    {
      title: '触发条件',
      dataIndex: 'threshold',
      defaultVisible: true,
      // render(threshold, { dataType }) {
      //   if (dataType.code !== POINT_DATA_TYPE_CODE_MAP.AI) {
      //     return BLANK_PLACEHOLDER;
      //   }

      //   return threshold;
      // },
    },
    {
      title: '告警值',
      dataIndex: 'triggerSnapshot',
      visible: true,
      render: (value, record) => {
        let text = '';
        if (record.triggerSnapshot === null) {
          return <span>{BLANK_PLACEHOLDER}</span>;
        }
        if (record.dataType?.code === 'DI') {
          if (record.validLimits[Number(record.triggerSnapshot)]) {
            text = record.validLimits[Number(record.triggerSnapshot)];
          } else {
            return <DIErrorTooltip title={value} />;
          }
        }
        if (record.dataType?.code === 'AI') {
          text = record.triggerSnapshot
            ? `${record.triggerSnapshot}${record.unit ? record.unit : ''}`
            : BLANK_PLACEHOLDER;
        }
        return <span>{text}</span>;
      },
    },
    {
      title: '测点现值',
      dataIndex: 'pointValue',
      visible: true,
      render: (value, record) => {
        if (record.pointValue === null) {
          return BLANK_PLACEHOLDER;
        }
        const dataType = record.dataType?.code;
        let text = '';
        if (dataType === POINT_DATA_TYPE_CODE_MAP.DI) {
          if (!record.validLimits[record.pointValue]) {
            return <DIErrorTooltip title={value} />;
          }
          text = record.validLimits[record.pointValue];
        }
        if (dataType === POINT_DATA_TYPE_CODE_MAP.AI) {
          text = `${record.pointValue}${record.unit || ''}`;
        }
        const pointGuid = {
          deviceGuid: record.triggerGuid,
          pointCode: record.pointCode,
          serieName: record.pointCodeName,
          deviceType: record.deviceType,
        };

        return (
          <Button
            type="link"
            style={{ padding: 0, height: 'auto' }}
            onClick={() => {
              setPointValueInfo({ alarmInfo: record, pointGuid, visible: true });
            }}
          >
            {text}
          </Button>
        );
      },
    },
    {
      title: '告警状态',
      dataIndex: 'triggerStatus',
      visible: true,
      render: text => <AlarmStatus triggerStatus={text} type="text" />,
    },
    {
      title: '告警开始时间',
      dataIndex: 'gmtCreate',
      visible: true,
      render: text => (
        <span
          style={{
            minWidth: 100,
            maxWidth: 160,
            display: 'inline-block',
            marginLeft: -7,
            marginRight: -7,
          }}
        >
          {text}
        </span>
      ),
    },

    {
      title: '最新告警时间',
      dataIndex: 'triggerTime',
      visible: true,
      render: text => (
        <span
          style={{
            minWidth: 100,
            maxWidth: 160,
            display: 'inline-block',
            marginLeft: -7,
            marginRight: -7,
          }}
        >
          {text}
        </span>
      ),
    },
    {
      title: '告警恢复时间',
      dataIndex: 'recoverTime',
      visible: true,
      render: text => (
        <span
          style={{
            minWidth: 100,
            maxWidth: 160,
            display: 'inline-block',
            marginLeft: -7,
            marginRight: -7,
          }}
        >
          {text}
        </span>
      ),
    },
    {
      title: '受理反馈',
      dataIndex: 'alarmReason',
      visible: true,
      render: text => {
        if (text) {
          return <AlarmReasonText code={text} />;
        }
      },
    },
    {
      title: '事件单号',
      dataIndex: 'eventId',
      visible: true,
      dataType: {
        type: 'link',
        options: {
          to(text) {
            return { pathname: urlsUtil.generateEventCenterDetail({ id: text }) };
          },
        },
      },
    },
    {
      title: '处理状态',
      dataIndex: 'alarmStatus',
      visible: true,
      render: text => <ProcessingState processingState={text} />,
    },
    {
      title: '处理人',
      dataIndex: 'confirmByName',
      visible: true,
    },
    {
      title: '三级分类',
      dataIndex: 'deviceType',
      ellipsis: true,
      defaultVisible: false,
      render: (text, record) => (
        <span>
          {metaCategoryEntities[record.deviceType]
            ? metaCategoryEntities[record.deviceType].metaName
            : text}
        </span>
      ),
    },
    {
      title: '命中规则',
      dataIndex: 'rules',
      ellipsis: true,
      defaultVisible: false,
      render: (__, record) => {
        return (
          <Link
            to={urlsUtil.generateSpecificMonitorItemConfigLocation({
              configId: record.itemId,
              deviceType: record.deviceType,
              pointCode: record.pointCode,
            })}
          >
            查看
          </Link>
        );
      },
    },
    {
      title: '影响机柜数',
      dataIndex: 'gridCount',
      defaultVisible: false,
      ellipsis: true,
    },
    { title: '影响设备数', dataIndex: 'deviceCount', defaultVisible: false },
    {
      title: '客户影响面',
      dataIndex: 'customerImpact',
      defaultVisible: false,
      ellipsis: true,
    },
    {
      title: '通知次数',
      dataIndex: 'notifyCount',
      defaultVisible: false,
    },
    {
      title: '机列',
      dataIndex: 'columnTag',
      defaultVisible: false,
    },
    {
      title: '机柜',
      dataIndex: 'gridTag',
      defaultVisible: false,
    },
  ];
  if (showColumns.length) {
    list = list
      .map(item => {
        const showColumnsList = showColumns.filter(
          showColumnsItem =>
            typeof showColumnsItem === 'object' && showColumnsItem.dataIndex === item.dataIndex
        );

        if (showColumnsList.length) {
          const alarmItem = { ...item, ...showColumnsList[0] };
          return alarmItem;
        }
        if (
          Array.isArray(item.dataIndex)
            ? showColumns.includes(item.dataIndex.join('.'))
            : showColumns.includes(item.dataIndex)
        ) {
          return item;
        }
        return false;
      })
      .filter(i => i !== false);
  }
  if (Array.isArray(fixedColumns) && fixedColumns.length) {
    fixedColumns.forEach(({ dataIndex, fixed }) => {
      const columnIdx = list.findIndex(clmn => clmn.dataIndex === dataIndex);
      if (columnIdx > -1) {
        list[columnIdx].fixed = fixed;
      }
    });
  }
  if (operation) {
    list.push(operation);
  }
  return list;
};

/**
 * 告警的通用的列表
 * @param {object} props
 * @param {string[]} [props.showColumns=[]] 需要展示的列 给定dataIndex
 * @param {React.CSSProperties} [props.actionsWrapperStyle] 表格上方操作按钮容器样式
 * @param {EditColumnsButtonProps} props.editColumnsButton 列设置配置
 * @param {JSX.Element | JSX.Element[]} [props.actions=null] 表格上方的操作按钮
 * @param {JSX.Element | JSX.Element[]} [props.operation=null] 操作列
 * @param {object} [props.metaCategoryEntities={}] 设备三级分类的映射数据
 * @param {React.CSSProperties} [props.notifyContentCellStyle] 告警内容 单元格样式
 * @param {Array<{ dataIndex: string; fixed: 'left'|'right'|boolean }>} [props.fixedColumns] 锁定列配置
 */
export function AlarmTable({
  dataSource,
  loading,
  className,
  metaCategoryEntities,
  operation = null,
  showColumns = [],
  status,
  notifyContentCellStyle = DEFAULT_NOTIFY_CONTENT_CELL_STYLE,
  fixedColumns = [],
  onRowShowDetail = false,
  ...props
}) {
  const [alarmInfoVisible, setAlarmInfoVisible] = useState(false);
  const [alarmInfo, setAlarmInfo] = useState({});

  const [pointValueInfo, setPointValueInfo] = useState({
    alarmInfo: {},
    pointGuid: {},
    visible: false,
  });
  const onAlarmInfoVisible = record => {
    setAlarmInfo(record);
    setAlarmInfoVisible(true);
  };

  return (
    <>
      <TinyTable
        rowKey="id"
        tableLayout="fixed"
        scroll={{ x: 'max-content' }}
        className={className}
        columns={columns(
          metaCategoryEntities,
          operation,
          onAlarmInfoVisible,
          showColumns,
          setPointValueInfo,
          {
            notifyContentCellStyle,
            fixedColumns,
          }
        )}
        loading={loading}
        dataSource={dataSource}
        {...props}
        onRow={record => {
          return {
            onClick: event => {
              if (event?.target?.type !== 'button') {
                onRowShowDetail && onAlarmInfoVisible(record);
              }
            },
          };
        }}
      />
      <DetailInfo
        visible={alarmInfoVisible}
        className={className}
        alarmInfo={alarmInfo}
        idc={alarmInfo.idcTag}
        metaCategoryEntities={metaCategoryEntities}
        onCancel={() => setAlarmInfoVisible(false)}
      />
      {/* 测点现值 */}
      <Modal
        // compact
        // type="link"
        // text={text}
        visible={pointValueInfo.visible}
        style={{ minWidth: (1200 / 1600) * 100 + '%', maxWidth: 1200 }}
        footer={null}
        title={pointValueInfo.alarmInfo?.pointCodeName}
        onCancel={event => {
          setPointValueInfo(prev => ({ ...prev, visible: false }));
          event.stopPropagation();
          event.nativeEvent.stopImmediatePropagation();
          // return true;
        }}
      >
        {pointValueInfo.alarmInfo?.dataType?.code === POINT_DATA_TYPE_CODE_MAP.DI ? (
          <PointsStateLine
            idcTag={pointValueInfo.alarmInfo.idcTag}
            pointGuids={[pointValueInfo.pointGuid]}
            chartFunction="MAX"
            validLimitsMap={pointValueInfo.alarmInfo?.validLimits}
          />
        ) : (
          <PointsLine
            idcTag={pointValueInfo.alarmInfo.idcTag}
            pointGuids={[pointValueInfo.pointGuid]}
            chartFunction="MAX"
          />
        )}
      </Modal>
    </>
  );
}

/**
 * @type {AlarmTable}
 */
export default styled(AlarmTable)`
  .alarm-table_alarm-row--background--color {
    background-color: var(--background-color-red);
  }
  .manyun-table-row {
    cursor: ${({ onRowShowDetail }) => (onRowShowDetail ? 'pointer' : 'auto')};
  }
`;
