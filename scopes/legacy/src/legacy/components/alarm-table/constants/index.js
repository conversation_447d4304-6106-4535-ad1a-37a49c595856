import React from 'react';

import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';

export function AlarmTypeText({ alarmType = {} }) {
  if (alarmType.code === 'WARN') {
    return <Typography.Text type="warning">{alarmType.name}</Typography.Text>;
  }
  if (alarmType.code === 'ERROR') {
    return <Typography.Text type="danger">{alarmType.name}</Typography.Text>;
  }
  return <Typography.Text>{alarmType.name}</Typography.Text>;
}

export function AlarmStatus({ triggerStatus = {}, type }) {
  if (type === 'text') {
    if (triggerStatus.code === 'TRIGGER') {
      return <Typography.Text type="danger">{triggerStatus.name}</Typography.Text>;
    }
    if (triggerStatus.code === 'RECOVER') {
      return <Typography.Text type="success">{triggerStatus.name}</Typography.Text>;
    }
    return <span>{triggerStatus.name}</span>;
  }
  if (type === 'tag') {
    if (triggerStatus.code === 'TRIGGER') {
      return <Tag color="error">{triggerStatus.name}</Tag>;
    }
    if (triggerStatus.code === 'RECOVER') {
      return <Tag color="green">{triggerStatus.name}</Tag>;
    }
    return <span>{triggerStatus.name}</span>;
  }
}

export function ProcessingState({ processingState = {} }) {
  if (processingState.code === 'CONFIRMED') {
    return <Typography.Text type="warning">{processingState.name}</Typography.Text>;
  }
  if (processingState.code === 'ACTIVE') {
    return <Typography.Text type="danger">{processingState.name}</Typography.Text>;
  }
  if (processingState.code === 'PROCESS') {
    return <Typography.Text type="success">{processingState.name}</Typography.Text>;
  }
  return <span>{processingState.name}</span>;
}
