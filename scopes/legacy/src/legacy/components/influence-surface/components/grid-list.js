import React, { Component } from 'react';

import Form from '@ant-design/compatible/es/form';
import { Select } from '@galiojs/awesome-antd';

import { Button } from '@manyun/base-ui.ui.button';
import { Cascader } from '@manyun/base-ui.ui.cascader';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';

import { TinyTable } from '@manyun/dc-brain.legacy.components';
import {
  fetchBlockByPermission,
  fetchRoomByPermission,
} from '@manyun/dc-brain.legacy.services/alarmScreenService';
// import { fetchInfluence, fetchFacilityCount } from '@manyun/dc-brain.legacy.services/influenceService';
// import AddRole from './add-role';
// import { SUBSCRIPTION_TYPE } from '@manyun/dc-brain.legacy.constants';
// import { getUserInfo } from '@manyun/dc-brain.legacy.utils/user';
// import { subscribeChannels } from '../constants/index';
// import { SUBSCRIBE_CHANNELS_LABEL_KEY } from '@manyun/dc-brain.legacy.constants/index';
import { fetchCabinetType } from '@manyun/dc-brain.legacy.services/cabinetService';

const columns = (ctx, mode) => [
  {
    title: '位置',
    dataIndex: 'spaceGuid',
    exportDataIndexes: [
      { title: '楼', dataIndex: ['spaceGuid', 'blockTag'] },
      { title: '包间', dataIndex: ['spaceGuid', 'roomTag'] },
    ],
    render: spaceGuid => <span>{`${spaceGuid.blockTag} | ${spaceGuid.roomTag}`}</span>,
  },
  {
    title: '机柜',
    dataIndex: 'gridTag',
  },
  {
    title: '机柜类型',
    dataIndex: 'gridTypeName',
  },
  {
    title: '客户名称',
    dataIndex: 'gridCustomer',
  },
];

class GridList extends Component {
  state = {
    selectedRowKeys: [],
    selectedRows: [],
    gridTypeList: [],
    buildingList: [],
  };

  onSelectChange = (selectedRowKeys, selectedRows) => {
    this.setState({
      selectedRowKeys,
      selectedRows,
    });
  };

  getGridTypeList = async () => {
    const { gridTypeList } = this.state;
    if (!gridTypeList.length) {
      const data = await fetchCabinetType();
      if (data) {
        this.setState({
          gridTypeList: data,
        });
      }
    }
  };

  getBlock = async () => {
    if (!this.state.buildingList.length) {
      const { idcTag = '', blockTag = '' } = this.props;
      const { response, error } = await fetchBlockByPermission({ idcTag });
      if (response) {
        this.setState({
          buildingList: response.data
            .map(item => {
              if ((blockTag && blockTag === item) || !blockTag) {
                return {
                  label: item,
                  value: item,
                  isLeaf: false,
                };
              } else {
                return false;
              }
            })
            .filter(Boolean),
        });
      }
      if (error) {
        message.error(error);
      }
    }
  };

  loadData = async selectedOptions => {
    const targetOption = selectedOptions[selectedOptions.length - 1];
    targetOption.loading = true;
    const { idcTag = '' } = this.props;

    const { response, error } = await fetchRoomByPermission({
      idcTag,
      blockTags: [selectedOptions[0].label],
    });
    if (response) {
      targetOption.loading = false;
      targetOption.children = response.data.map(item => {
        return {
          label: item.tag,
          value: item.tag,
        };
      });
      this.setState({
        buildingList: [...this.state.buildingList],
      });
    } else {
      message.error(error);
    }
  };

  handleSearch = () => {
    // const { gridInfluence } = this.props;
    const fieldValues = this.props.form.getFieldsValue();
    this.props.searchGridInfluence(fieldValues);
  };

  render() {
    const { selectedRows, selectedRowKeys, gridTypeList, buildingList } = this.state;
    const { showGridInfluence, form, isInPdfExport } = this.props;
    const { getFieldDecorator } = form;

    return (
      <Space style={{ width: '100%' }} direction="vertical">
        <Form layout="inline">
          <Form.Item label="位置">
            {getFieldDecorator('blockRoom')(
              <Cascader
                style={{ width: 160 }}
                options={buildingList}
                allowClear
                loadData={this.loadData}
                onFocus={this.getBlock}
              />
            )}
          </Form.Item>
          <Form.Item label="机柜">
            {getFieldDecorator('gridTag')(<Input style={{ width: 120 }} allowClear />)}
          </Form.Item>
          <Form.Item label="机柜类型">
            {getFieldDecorator('gridTypeCode')(
              <Select style={{ width: 120 }} allowClear onFocus={this.getGridTypeList}>
                {Array.isArray(gridTypeList) && gridTypeList.length
                  ? gridTypeList.map(item => {
                      return (
                        <Select.Option key={item.key} value={item.key}>
                          {item.value}
                        </Select.Option>
                      );
                    })
                  : null}
              </Select>
            )}
          </Form.Item>
          <Form.Item label="客户名称">
            {getFieldDecorator('gridCustomer')(<Input style={{ width: 120 }} allowClear />)}
          </Form.Item>
          <Form.Item label=" " colon={false}>
            <Space>
              <Button type="primary" onClick={this.handleSearch}>
                搜索
              </Button>
              <Button
                onClick={() => {
                  form.resetFields();
                  this.props.resetGridInfluence();
                }}
              >
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>
        <TinyTable
          rowKey="gridGuid"
          showExport={{ filename: '机柜影响面' }}
          rowSelection={{
            selectedRowKeys,
            selectedRows,
            onChange: this.onSelectChange,
          }}
          // exportServices={() => downloadEquipment(this.getParams())}
          exportAllData
          dataSource={showGridInfluence}
          columns={columns()}
          actionsWrapperStyle={{ justifyContent: 'flex-end' }}
          pagination={
            isInPdfExport
              ? false
              : {
                  total: showGridInfluence.length,
                  // current: equipmentPageCondition.pageNum,
                  // onChange: this.onChangePageNo,
                  // pageSize: equipmentPageCondition.pageSize,
                }
          }
        />
      </Space>
    );
  }
}

export default Form.create({ name: 'influence-surface-grid-list' })(GridList);
