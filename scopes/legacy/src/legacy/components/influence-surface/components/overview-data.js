import React from 'react';

import styled from 'styled-components';

import { prefixCls } from '@manyun/base-ui.style.style';
import { Space } from '@manyun/base-ui.ui.space';

import { Upload } from '@manyun/dc-brain.ui.upload';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';

export default function OverView({ list = [] }) {
  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        height: 100,
        justifyContent: 'space-between',
      }}
    >
      {list.map((item, index) => (
        <div key={index} style={{ display: 'flex' }}>
          <div style={{ display: 'flex' }}>
            <GutterWrapper mode="vertical">
              <div style={{ fontSize: 28, color: `var(--${prefixCls}-warning-color)` }}>
                {item.count}
              </div>
              <Space align="center">
                {React.cloneElement(item.icon, { style: { fontSize: 22 } })}
                {item.text}
              </Space>
            </GutterWrapper>
          </div>
        </div>
      ))}
    </div>
  );
}

export const StyledUpload = styled(Upload)`
  .manyun-upload-list-item-card-actions {
    display: none;
  }
`;
