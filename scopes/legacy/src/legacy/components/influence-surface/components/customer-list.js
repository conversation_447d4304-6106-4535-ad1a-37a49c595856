import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';

import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { Space } from '@manyun/base-ui.ui.space';

import { TinyTable } from '@manyun/dc-brain.legacy.components';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';

const columns = ctx => [
  {
    title: '客户名',
    dataIndex: 'name',
  },
  {
    title: '影响机柜数',
    dataIndex: 'impactCabinet',
  },
  {
    title: '客户所属机柜数',
    dataIndex: 'belongsCabinet',
  },
  {
    title: '影响占比',
    dataIndex: 'proportion',
    sorter: (a, b) => a.proportion - b.proportion,
    render: text => <span>{`${text}%`}</span>,
  },
];

class CustomerList extends Component {
  state = {
    selectedRowKeys: [],
    selectedRows: [],
  };

  onSelectChange = (selectedRowKeys, selectedRows) => {
    this.setState({
      selectedRowKeys,
      selectedRows,
    });
  };

  handleSearch = () => {
    const name = this.props.form.getFieldValue('name');
    this.props.searchCustomerInfluence(name);
  };

  render() {
    const { selectedRows, selectedRowKeys } = this.state;
    const { showCustomerInfluence, form, isInPdfExport } = this.props;
    const { getFieldDecorator } = form;

    return (
      <Space style={{ width: '100%' }} direction="vertical">
        <Form layout="inline">
          <Form.Item label="客户名称">
            {getFieldDecorator('name')(<Input allowClear style={{ width: 120 }} />)}
          </Form.Item>
          <Form.Item label=" " colon={false}>
            <Space>
              <Button type="primary" onClick={this.handleSearch}>
                搜索
              </Button>
              <Button
                onClick={() => {
                  form.resetFields();
                  this.props.resetCustomerInfluence();
                }}
              >
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>

        <TinyTable
          rowKey="name"
          showExport={{ filename: '客户影响面' }}
          rowSelection={{
            selectedRowKeys,
            selectedRows,
            onChange: this.onSelectChange,
          }}
          exportAllData
          dataSource={showCustomerInfluence}
          columns={columns(this)}
          actionsWrapperStyle={{ justifyContent: 'flex-end' }}
          pagination={
            isInPdfExport
              ? false
              : {
                  total: showCustomerInfluence.length,
                  // current: equipmentPageCondition.pageNum,
                  // onChange: this.onChangePageNo,
                  // pageSize: equipmentPageCondition.pageSize,
                }
          }
        />
      </Space>
    );
  }
}

// export default Form.create({ name: 'influence-surface-grid-list' })(DevicesList);

const mapStateToProps = ({ common: { deviceCategory } }) => ({
  // deviceCategoryTreeList: deviceCategory ? deviceCategory.treeList : [],
  nomalizedDeviceCategory: deviceCategory ? deviceCategory.normalizedList : {},
});
const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(Form.create({ name: 'influence-surface-grid-list' })(CustomerList));
