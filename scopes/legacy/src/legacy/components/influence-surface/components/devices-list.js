import Form from '@ant-design/compatible/es/form';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { Cascader } from '@manyun/base-ui.ui.cascader';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';

import { AssetClassificationApiTreeSelect, TinyTable } from '@manyun/dc-brain.legacy.components';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  fetchBlockByPermission,
  fetchRoomByPermission,
} from '@manyun/dc-brain.legacy.services/alarmScreenService';
import { fetchCabinetType } from '@manyun/dc-brain.legacy.services/cabinetService';

const columns = ctx => [
  {
    title: '设备名称',
    dataIndex: 'deviceName',
    fixed: 'left',
  },
  {
    title: '资产分类',
    dataIndex: 'deviceType',
    exportedContent: text => ctx.props?.nomalizedDeviceCategory[text]?.metaName,

    render: text => (
      <span>
        {text &&
          ctx.props.nomalizedDeviceCategory[text] &&
          ctx.props.nomalizedDeviceCategory[text].metaName}
      </span>
    ),
  },
  {
    title: '位置',
    dataIndex: 'spaceGuid',
    exportDataIndexes: [
      { title: '楼', dataIndex: ['spaceGuid', 'blockTag'] },
      { title: '包间', dataIndex: ['spaceGuid', 'roomTag'] },
    ],
    render: spaceGuid => <span>{`${spaceGuid.blockTag} | ${spaceGuid.roomTag}`}</span>,
  },
  {
    title: '扩展位置',
    dataIndex: 'extendPosition',
  },
];

class DevicesList extends Component {
  state = {
    selectedRowKeys: [],
    selectedRows: [],
    gridTypeList: [],
    buildingList: [],
  };

  componentDidMount() {
    this.props.syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });
  }

  onSelectChange = (selectedRowKeys, selectedRows) => {
    this.setState({
      selectedRowKeys,
      selectedRows,
    });
  };

  getGridTypeList = async () => {
    const { gridTypeList } = this.state;
    if (!gridTypeList.length) {
      const data = await fetchCabinetType();
      if (data) {
        this.setState({
          gridTypeList: data,
        });
      }
    }
  };

  getBlock = async () => {
    if (!this.state.buildingList.length) {
      const { idcTag = '', blockTag = '' } = this.props;
      const { response, error } = await fetchBlockByPermission({ idcTag });
      if (response) {
        this.setState({
          buildingList: response.data
            .map(item => {
              if ((blockTag && blockTag === item) || !blockTag) {
                return {
                  label: item,
                  value: item,
                  isLeaf: false,
                };
              } else {
                return false;
              }
            })
            .filter(Boolean),
        });
      }
      if (error) {
        message.error(error);
      }
    }
  };

  loadData = async selectedOptions => {
    const targetOption = selectedOptions[selectedOptions.length - 1];
    targetOption.loading = true;
    const { idcTag = '' } = this.props;

    const { response, error } = await fetchRoomByPermission({
      idcTag,
      blockTags: [selectedOptions[0].label],
    });
    if (response) {
      targetOption.loading = false;
      targetOption.children = response.data.map(item => {
        return {
          label: item.tag,
          value: item.tag,
        };
      });
      this.setState({
        buildingList: [...this.state.buildingList],
      });
    } else {
      message.error(error);
    }
  };

  handleSearch = () => {
    // const { gridInfluence } = this.props;
    const fieldValues = this.props.form.getFieldsValue();
    this.props.searchDevicesInfluence(fieldValues);
  };

  render() {
    const { selectedRows, selectedRowKeys, buildingList } = this.state;
    const { showDevicesInfluence, form, isInPdfExport } = this.props;
    const { getFieldDecorator } = form;

    return (
      <Space style={{ width: '100%' }} direction="vertical">
        <Form layout="inline">
          <Form.Item label="设备分类">
            {getFieldDecorator('deviceType')(
              <AssetClassificationApiTreeSelect
                style={{ width: 220 }}
                dataType={['space', 'snDevice']}
                category="categorycode"
                disabledDepths={[0, 1]}
                requestOnDidMount
                allowClear
              />
            )}
          </Form.Item>
          <Form.Item label="设备名称">
            {getFieldDecorator('deviceName')(<Input style={{ width: 120 }} allowClear />)}
          </Form.Item>
          <Form.Item label="位置">
            {getFieldDecorator('blockRoom')(
              <Cascader
                style={{ width: 160 }}
                options={buildingList}
                allowClear
                onFocus={this.getBlock}
                loadData={this.loadData}
              />
            )}
          </Form.Item>
          <Form.Item label="扩展位置">
            {getFieldDecorator('extendPosition')(<Input style={{ width: 120 }} allowClear />)}
          </Form.Item>
          <Form.Item label=" " colon={false}>
            <Space>
              <Button type="primary" onClick={this.handleSearch}>
                搜索
              </Button>
              <Button
                onClick={() => {
                  form.resetFields();
                  this.props.resetDevicesInfluence();
                }}
              >
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>

        <TinyTable
          rowKey={({ deviceGuid, deviceName, deviceType, spacePosition }) =>
            `${deviceGuid}+${deviceName}+${deviceType}+${spacePosition}`
          }
          showExport={{ filename: '设备影响面' }}
          scroll={{ x: 'max-content' }}
          rowSelection={{
            selectedRowKeys,
            selectedRows,
            onChange: this.onSelectChange,
          }}
          // exportServices={() => downloadEquipment(this.getParams())}
          exportAllData
          dataSource={showDevicesInfluence}
          columns={columns(this)}
          actionsWrapperStyle={{ justifyContent: 'flex-end' }}
          pagination={
            isInPdfExport
              ? false
              : {
                  total: showDevicesInfluence.length,
                  // current: equipmentPageCondition.pageNum,
                  // onChange: this.onChangePageNo,
                  // pageSize: equipmentPageCondition.pageSize,
                }
          }
        />
      </Space>
    );
  }
}

// export default Form.create({ name: 'influence-surface-grid-list' })(DevicesList);

const mapStateToProps = ({ common: { deviceCategory } }) => ({
  // deviceCategoryTreeList: deviceCategory ? deviceCategory.treeList : [],
  nomalizedDeviceCategory: deviceCategory ? deviceCategory.normalizedList : {},
});
const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(Form.create({ name: 'influence-surface-grid-list' })(DevicesList));
