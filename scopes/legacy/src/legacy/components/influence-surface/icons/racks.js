import React from 'react';

import Icon from '@ant-design/icons';

export function RacksIconColorful({ style }) {
  return <Icon style={style} component={RacksIconSvg} />;
}

function RacksIconSvg() {
  return (
    <svg className="manyun-icon" viewBox="0 0 1024 1024" width="1em" height="1em">
      <path
        d="M121.904762 25.6v972.8A24.868571 24.868571 0 0 0 146.529524 1024h730.940952a24.868571 24.868571 0 0 0 24.624762-25.6V25.6A24.868571 24.868571 0 0 0 877.470476 0H146.529524A24.868571 24.868571 0 0 0 121.904762 25.6z"
        fill="#E6E6E6"
      />
      <path
        d="M846.994286 82.944v59.879619a24.868571 24.868571 0 0 1-24.624762 25.648762H201.142857a24.868571 24.868571 0 0 1-24.576-25.648762V82.992762a24.868571 24.868571 0 0 1 24.576-25.6h621.226667c13.263238 0 24.576 11.264 24.576 25.6zM846.994286 250.88v59.392a24.868571 24.868571 0 0 1-24.624762 25.6H201.142857a24.868571 24.868571 0 0 1-24.576-25.6V250.88a24.868571 24.868571 0 0 1 24.576-25.6h621.226667c13.263238 0 24.576 11.264 24.576 25.6zM176.518095 941.056V418.328381a24.868571 24.868571 0 0 1 24.624762-25.648762h621.226667a24.868571 24.868571 0 0 1 24.576 25.6V941.104762a24.868571 24.868571 0 0 1-24.576 25.6H201.142857a25.209905 25.209905 0 0 1-24.576-25.6z"
        fill="#00193F"
      />
      <path
        d="M655.506286 564.224h82.651428a24.868571 24.868571 0 0 1 24.624762 25.6v6.144a24.868571 24.868571 0 0 1-24.624762 25.6h-82.651428a24.868571 24.868571 0 0 1-24.624762-25.6v-6.144c0-14.336 11.312762-25.6 24.624762-25.6z"
        fill="#FAAD14"
      />
    </svg>
  );
}
