import React, { Component } from 'react';

import { isEqual, omitBy, pick } from 'lodash';

import { Card } from '@manyun/base-ui.ui.card';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Tabs } from '@manyun/base-ui.ui.tabs';

import { EventCustomInfluence } from '@manyun/ticket.page.event-detail';
import { fetchEventInfluenceCount } from '@manyun/ticket.service.fetch-event-influence-count';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';
import { influenceService } from '@manyun/dc-brain.legacy.services';

import { TinyCard } from '../tiny-card';
import CustomerList from './components/customer-list';
import DevicesList from './components/devices-list';
import GridList from './components/grid-list';
import Overview from './components/overview-data';
import { CustomersIconColorful } from './icons/customers';
import { DeprecatedCustomersIconColorful } from './icons/deprecated-customers';
import { DeprecatedRacksIconColorful } from './icons/deprecated-racks';
import { RacksIconColorful } from './icons/racks';

class InfluenceSurface extends Component {
  state = {
    devicesInfluence: [],
    gridInfluence: [],
    customerInfluence: [],
    overviewDataList: [
      {
        count: '--',
        text: '机柜影响数',
        icon: <RacksIconColorful />,
      },
      {
        count: '--',
        text: '客户影响数',
        icon: <CustomersIconColorful />,
      },
      {
        count: '--',
        text: '机柜影响占比',
        icon: <DeprecatedRacksIconColorful />,
      },
      {
        count: '--',
        text: '客户影响占比',
        icon: <DeprecatedCustomersIconColorful />,
      },
    ],
    showGridInfluence: [],
    showDevicesInfluence: [],
    showCustomerInfluence: [],
  };

  componentDidMount() {
    this.getInfluence();
  }

  getInfluence = async () => {
    const {
      idcTag,
      blockTag,
      targetId,
      targetType,
      deviceGuids,
      type,
      showRacksImpacts,
      eventNo,
      featureIsEventConfigWithProcessEngineRequired,
      setPdfTabList,
    } = this.props;
    let influenceData = {};
    let eventInfluenceNewCountData = {};
    const shouldFetchEventNewCount = targetType === 'EVENT' && showRacksImpacts;
    if (shouldFetchEventNewCount) {
      const { error, data } = await fetchEventInfluenceCount({
        eventId: targetId,
      });
      if (error) {
        message.error(error.message);
        return;
      }
      eventInfluenceNewCountData = data;
    }
    const isEventInfluenceNewCountDataValid = Object.keys(eventInfluenceNewCountData).length > 0;
    if (!deviceGuids && targetType !== 'ALARM') {
      if (isEventInfluenceNewCountDataValid) {
        this.setState(({ overviewDataList }) => ({
          overviewDataList: overviewDataList.map((item, idx) => {
            switch (idx) {
              // 影响机柜个数
              case 0:
                item.count =
                  typeof eventInfluenceNewCountData.gridCount === 'number'
                    ? `${eventInfluenceNewCountData.gridCount}台`
                    : '--';
                break;
              // 影响客户个数
              case 1:
                item.count =
                  typeof eventInfluenceNewCountData.customerCount === 'number'
                    ? `${eventInfluenceNewCountData.customerCount}位`
                    : '--';
                break;
              // 影响机柜占比
              case 2:
                item.count = '--';
                break;
              // 影响客户占比
              case 3:
                item.count = '--';
                break;
              default:
                break;
            }

            return item;
          }),
        }));
        setPdfTabList &&
          setPdfTabList(pdfTabList => {
            return pdfTabList.map(item => {
              if (item.key === 'influenceSuface') {
                return {
                  ...item,
                  isValid: [
                    eventInfluenceNewCountData.gridCount,
                    eventInfluenceNewCountData.customerCount,
                  ].some(item => item > 0)
                    ? true
                    : false,
                  isRendered: true,
                };
              }
              return { ...item };
            });
          });
      }
      setPdfTabList &&
        setPdfTabList(pdfTabList => {
          return pdfTabList.map(item => {
            if (item.key === 'influenceSuface') {
              return {
                ...item,
                isValid: false,
                isRendered: true,
              };
            }
            return { ...item };
          });
        });
      return;
    }

    if (type === 'budget') {
      influenceData = await influenceService.fetchBudgetInfluence({
        idcTag,
        blockTag,
        targetId,
        targetType,
        deviceGuidList: deviceGuids ? deviceGuids.split(',') : null,
      });
    } else {
      influenceData = await influenceService.fetchInfluence({
        idcTag,
        blockTag,
        targetId: featureIsEventConfigWithProcessEngineRequired ? eventNo : targetId,
        targetType,
        deviceGuidList: deviceGuids ? deviceGuids.split(',') : null,
      });
    }

    if (influenceData.response) {
      const gridList = influenceData.response.influenceGrids
        ? influenceData.response.influenceGrids.map(item => {
            return {
              ...item,
              blockTag: item.spaceGuid.blockTag,
              roomTag: item.spaceGuid.roomTag,
              gridTypeCode: item.gridType.code,
              gridTypeName: item.gridType.name,
            };
          })
        : [];
      this.setState({
        devicesInfluence: influenceData.response.influenceDevices || [],
        gridInfluence: gridList,
        showGridInfluence: gridList,
        showDevicesInfluence: influenceData.response.influenceDevices || [],
      });
      const fetchFacilityCountData = await influenceService.fetchFacilityCount({
        idcTag,
        blockTag,
        targetId,
        targetType,
      });
      if (fetchFacilityCountData.response) {
        let customerInfluence = [];
        if (influenceData.response.influenceCustomers) {
          customerInfluence = influenceData.response.influenceCustomers.map(item => {
            return {
              name: item.customerName,
              impactCabinet: item.influenceGridCount,
              belongsCabinet: fetchFacilityCountData.response.customerMap[item.customerName],
              proportion: Math.round(
                (item.influenceGridCount /
                  fetchFacilityCountData.response.customerMap[item.customerName]) *
                  100
              ),
            };
          });
        }
        if (targetType === 'EVENT') {
          if (isEventInfluenceNewCountDataValid) {
            setPdfTabList &&
              setPdfTabList(pdfTabList => {
                return pdfTabList.map(item => {
                  if (item.key === 'influenceSuface') {
                    return {
                      ...item,
                      isValid: [
                        eventInfluenceNewCountData.gridCount,
                        eventInfluenceNewCountData.customerCount,
                      ].some(item => item > 0)
                        ? true
                        : false,
                      isRendered: true,
                    };
                  }
                  return { ...item };
                });
              });
          } else {
            setPdfTabList &&
              setPdfTabList(pdfTabList => {
                return pdfTabList.map(item => {
                  if (item.key === 'influenceSuface') {
                    return {
                      ...item,
                      isValid:
                        influenceData.response.influenceGrids.length > 0 ||
                        influenceData.response.influenceCustomers.length > 0
                          ? true
                          : false,
                      isRendered: true,
                    };
                  }
                  return { ...item };
                });
              });
          }
        } else {
          setPdfTabList &&
            setPdfTabList(pdfTabList => {
              return pdfTabList.map(item => {
                if (item.key === 'influenceSuface') {
                  return {
                    ...item,
                    isValid:
                      influenceData.response.influenceGrids.length > 0 ||
                      influenceData.response.influenceCustomers.length > 0 ||
                      influenceData.response.influenceDevices.length > 0
                        ? true
                        : false,
                    isRendered: true,
                  };
                }
                return { ...item };
              });
            });
        }
        this.setState(({ overviewDataList }) => ({
          customerInfluence,
          showCustomerInfluence: customerInfluence,
          overviewDataList: overviewDataList.map((item, idx) => {
            switch (idx) {
              // 影响机柜个数
              case 0:
                if (isEventInfluenceNewCountDataValid) {
                  item.count =
                    typeof eventInfluenceNewCountData.gridCount === 'number'
                      ? `${eventInfluenceNewCountData.gridCount}台`
                      : '--';
                } else {
                  item.count = influenceData.response.influenceGrids
                    ? `${influenceData.response.influenceGrids.length}台`
                    : '--';
                }
                break;
              // 影响客户个数
              case 1:
                if (isEventInfluenceNewCountDataValid) {
                  item.count =
                    typeof eventInfluenceNewCountData.customerCount === 'number'
                      ? `${eventInfluenceNewCountData.customerCount}位`
                      : '--';
                } else {
                  item.count = influenceData.response.influenceCustomers
                    ? `${influenceData.response.influenceCustomers.length}位`
                    : '--';
                }
                break;
              // 影响机柜占比
              case 2:
                if (isEventInfluenceNewCountDataValid) {
                  item.count = eventInfluenceNewCountData.gridCount
                    ? fetchFacilityCountData.response.gridTotal === 0
                      ? '0%'
                      : `${Math.round(
                          (eventInfluenceNewCountData.gridCount /
                            fetchFacilityCountData.response.gridTotal) *
                            100
                        )}%`
                    : '--';
                } else {
                  item.count = influenceData.response.influenceGrids
                    ? fetchFacilityCountData.response.gridTotal === 0
                      ? '0%'
                      : `${Math.round(
                          (influenceData.response.influenceGrids.length /
                            fetchFacilityCountData.response.gridTotal) *
                            100
                        )}%`
                    : '--';
                }
                break;
              // 影响客户占比
              case 3:
                if (isEventInfluenceNewCountDataValid) {
                  item.count = eventInfluenceNewCountData.customerCount
                    ? fetchFacilityCountData.response.customerTotal === 0
                      ? '0%'
                      : `${Math.round(
                          (eventInfluenceNewCountData.customerCount /
                            fetchFacilityCountData.response.customerTotal) *
                            100
                        )}%`
                    : '--';
                } else {
                  item.count = influenceData.response.influenceCustomers
                    ? fetchFacilityCountData.response.customerTotal === 0
                      ? '0%'
                      : `${Math.round(
                          (influenceData.response.influenceCustomers.length /
                            fetchFacilityCountData.response.customerTotal) *
                            100
                        )}%`
                    : '--';
                }
                break;
              default:
                break;
            }

            return item;
          }),
        }));
      }
    }
  };

  searchGridInfluence = fieldValues => {
    const { gridInfluence } = this.state;
    const { blockRoom, gridTag, gridTypeCode, gridCustomer } = fieldValues || {};
    let accurateValues = {
      gridTag: gridTag && gridTag.toLowerCase().trim(),
      gridTypeCode,
      blockTag: blockRoom ? blockRoom[0] : null,
      roomTag: blockRoom ? blockRoom[1] : null,
    };
    accurateValues = omitBy(accurateValues, (value, key) => {
      if (value === null || value === undefined || value === '') {
        return key;
      }
    });
    const list = gridInfluence.filter(item => {
      const newItem = { ...item, gridTag: item.gridTag && item.gridTag.toLowerCase() };
      const required = isEqual(pick(newItem, Object.keys(accurateValues)), accurateValues);
      if (gridCustomer) {
        return (
          required &&
          item.gridCustomer &&
          item.gridCustomer.toLowerCase().includes(gridCustomer.toLowerCase())
        );
      }
      return required;
    });
    this.setState({ showGridInfluence: list });
  };

  searchDevicesInfluence = fieldValues => {
    const { devicesInfluence } = this.state;
    const { blockRoom, deviceName, extendPosition, deviceType } = fieldValues || {};
    let accurateValues = {
      deviceType: deviceType,
      blockTag: blockRoom ? blockRoom[0] : null,
      roomTag: blockRoom ? blockRoom[1] : null,
    };
    accurateValues = omitBy(accurateValues, (value, key) => {
      if (value === null || value === undefined || value === '') {
        return key;
      }
    });
    const list = devicesInfluence.filter(item => {
      const newItem = {
        ...item,
        blockTag: item.spaceGuid.blockTag,
        roomTag: item.spaceGuid.roomTag,
      };
      let required = isEqual(pick(newItem, Object.keys(accurateValues)), accurateValues);
      if (deviceName) {
        required =
          required && item.deviceName.toLowerCase().includes(deviceName.trim().toLowerCase());
      }
      if (extendPosition) {
        return (
          required &&
          item.extendPosition &&
          item.extendPosition.toLowerCase().includes(extendPosition.trim().toLowerCase())
        );
      }

      return required;
    });
    this.setState({ showDevicesInfluence: list });
  };

  searchCustomerInfluence = customer => {
    const { customerInfluence } = this.state;
    const list = customerInfluence.filter(item => item.name.includes(customer.trim()));
    this.setState({
      showCustomerInfluence: list,
    });
  };

  resetCustomerInfluence = () => {
    this.setState({ showCustomerInfluence: this.state.customerInfluence });
  };

  resetDevicesInfluence = () => {
    this.setState({ showDevicesInfluence: this.state.devicesInfluence });
  };

  resetGridInfluence = () => {
    this.setState({ showGridInfluence: this.state.gridInfluence });
  };
  render() {
    const {
      idcTag,
      tabBarStyle = {},
      defaultTab,
      blockTag,
      targetId,
      targetType,
      showRacksImpacts,
      tabsExpandAll,
    } = this.props;
    const { overviewDataList, showGridInfluence, showDevicesInfluence, showCustomerInfluence } =
      this.state;
    const tabItems = [
      {
        label: '机柜影响面',
        key: 'grid',
        children: (
          <GridList
            idcTag={idcTag}
            blockTag={blockTag}
            showGridInfluence={showGridInfluence}
            searchGridInfluence={this.searchGridInfluence}
            resetGridInfluence={this.resetGridInfluence}
            isInPdfExport={tabsExpandAll}
          />
        ),
      },
      {
        label: '客户影响面',
        key: 'customer',
        children: (
          <CustomerList
            idcTag={idcTag}
            showCustomerInfluence={showCustomerInfluence}
            searchCustomerInfluence={this.searchCustomerInfluence}
            resetCustomerInfluence={this.resetCustomerInfluence}
            isInPdfExport={tabsExpandAll}
          />
        ),
      },
      {
        label: '设备影响面',
        key: 'device',
        children: (
          <DevicesList
            idcTag={idcTag}
            blockTag={blockTag}
            showDevicesInfluence={showDevicesInfluence}
            searchDevicesInfluence={this.searchDevicesInfluence}
            resetDevicesInfluence={this.resetDevicesInfluence}
            isInPdfExport={tabsExpandAll}
          />
        ),
      },
    ];
    if (targetType === 'EVENT' && showRacksImpacts) {
      tabItems.push({
        label: '自定义影响面',
        key: 'custom',
        children: (
          <EventCustomInfluence
            eventId={targetId}
            spaceGuid={idcTag}
            callback={this.getInfluence}
          />
        ),
      });
    }

    return (
      <GutterWrapper mode="vertical">
        <TinyCard>
          <Overview list={overviewDataList} />
        </TinyCard>
        <TinyCard>
          {tabsExpandAll ? (
            <Space direction="vertical">
              {showGridInfluence.length > 0 && (
                <Card key={tabItems[0].key} title={tabItems[0].label}>
                  {tabItems[0].children}
                </Card>
              )}
              {showCustomerInfluence.length > 0 && (
                <Card key={tabItems[1].key} title={tabItems[1].label}>
                  {tabItems[1].children}
                </Card>
              )}
              {showDevicesInfluence.length > 0 && (
                <Card key={tabItems[2].key} title={tabItems[2].label}>
                  {tabItems[2].children}
                </Card>
              )}
              {showRacksImpacts && tabItems.length === 4 && (
                <Card key={tabItems[3].key} title={tabItems[3].label}>
                  {tabItems[3].children}
                </Card>
              )}
            </Space>
          ) : (
            <Tabs defaultActiveKey={defaultTab} tabBarStyle={tabBarStyle} items={tabItems} />
          )}
        </TinyCard>
      </GutterWrapper>
    );
  }
}

export default InfluenceSurface;
