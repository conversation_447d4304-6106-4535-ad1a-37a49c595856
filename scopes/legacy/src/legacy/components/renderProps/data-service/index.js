import React from 'react';

import omit from 'lodash/omit';

/**
 * @typedef Props
 * @property {boolean} [requestOnDidMount=false]
 * @property {() => Promise<any>} dataService
 * @property {(data?: any) => void} onDataUpdated
 * @property {(props: { data: any; requesting: boolean }, traversalProps?: any) => JSX.Element} children
 *
 * @typedef State
 * @property {any} data
 * @property {boolean} requesting
 */

/**
 * @augments { React.Component<Props, State> }
 *
 * @example <caption>Get data from external actions.</caption>
 * ```jsx
 * class Demo extends React.Component {
 *   _dataServiceRef = React.createRef();
 *
 *   _focusHandler = () => {
 *     this._dataServiceRef.current.getData();
 *   }
 *
 *   render() {
 *     return (
 *       <DataService ref={this._dataServiceRef} requestOnDidMount={false}>
 *         {({data = []}) => (
 *           <Select onFocus={this._focusHandler}>
 *             {data.map(({ label, value }) => (
 *               <Select.Option key={value} value={value}>{label}</Select.Option>
 *             ))}
 *           </Select>
 *         )}
 *       </DataService>
 *     );
 *   }
 * }
 * ```
 */
export class DataService extends React.Component {
  static defaultProps = {
    requestOnDidMount: false,
    dataService: null,
  };

  state = {
    requesting: false,
    data: undefined,
  };

  /**
   * Auto-increment number for every request.
   * @type {number}
   */
  _lastRequestId = 0;

  componentDidMount() {
    const { requestOnDidMount } = this.props;
    if (requestOnDidMount) {
      this._tryToGetData();
    }
  }

  _getData = async (q, callback) => {
    const query = typeof q != 'function' ? q : null;
    const cb = typeof q == 'function' ? q : callback;
    const { dataService } = this.props;
    if (typeof dataService !== 'function') {
      console.error('[DataService]: `dataService` expected.');
      return;
    }
    this._lastRequestId += 1;
    const requestId = this._lastRequestId;
    this.setState({ requesting: true });
    const data = await dataService(query);

    // There is another request that has not been fulfilled.
    // We should skip this result.
    if (requestId !== this._lastRequestId) {
      return;
    }

    this.setState({ requesting: false, data }, () => {
      if (typeof cb === 'function') {
        cb(data);
      }
      if (typeof this.props.onDataUpdated == 'function') {
        this.props.onDataUpdated(data);
      }
    });
  };

  _shouldGetData = () => {
    return !this.state.requesting && this.state.data === undefined;
  };

  _tryToGetData = () => {
    if (!this._shouldGetData()) {
      return;
    }
    this._getData();
  };

  /**
   * @param {boolean} [forced=false] Whether or not strict `this._shouldGetData()` check.
   * @param {(data: any) => void} [callback]
   */
  getData = (forced = false, callback) => {
    if (!forced) {
      this._tryToGetData();
      return;
    }
    this._getData(callback);
  };

  /**
   * @param {any} [q] query for dataService
   * @param {boolean} [forced=false]
   * @param {(data: any) => void} [callback]
   */
  getDataByQ = (q, forced = false, callback) => {
    if (!forced) {
      this._tryToGetData();
      return;
    }
    this._getData(q, callback);
  };

  render() {
    const { data, requesting } = this.state;
    const { children } = this.props;

    return children({ data, requesting }, omit(this.props, OWN_PROPS));
  }
}

export default DataService;

const OWN_PROPS = ['requestOnDidMount', 'dataService', 'children'];
