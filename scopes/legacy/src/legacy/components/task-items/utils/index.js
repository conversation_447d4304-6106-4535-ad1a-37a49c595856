import { INSPECTION_TYPE_KEY_MAP } from '@manyun/dc-brain.legacy.constants/inspection';

export function getTableData_XJ(jobType, data, ticketTypeMapping, deviceTypeMapping, roomTypes) {
  let newData = [];
  if (data.length) {
    newData = data.map(inspectItem => {
      const ticketTarget = ticketTypeMapping[inspectItem.inspectType];
      return {
        jobId: inspectItem.id,
        name: inspectItem.configName,
        scheduleType: jobType,
        jobTypeCode: inspectItem.inspectType,
        jobTypeName: ticketTarget ? ticketTarget.taskValue : inspectItem.inspectType,
        subJobTypeName: getSubJobTypeName({ inspectItem, deviceTypeMapping, roomTypes }),
        subJobTypeCode: inspectItem.subTypeCode,
      };
    });
  }
  return newData;
}

export function getTableData_PD(jobType, data, deviceTypeMapping) {
  let newData = [];
  if (data.length) {
    newData = data.map(item => {
      return {
        jobId: item.id,
        name: item.configName,
        scheduleType: jobType,
        jobTypeCode: item.inventoryType ? item.inventoryType.code : '',
        jobTypeName: item.inventoryType ? item.inventoryType.desc : '',
        subJobTypeCode: item.deviceTypes.map(code => {
          const deviceTarget = deviceTypeMapping[code];
          if (!deviceTarget) {
            return {
              code: code,
              name: code,
            };
          }
          return {
            code: code,
            name: deviceTarget.metaName,
          };
        }),
        subJobTypeName: item.deviceTypes
          .map(code => {
            const deviceTarget = deviceTypeMapping[code];
            if (!deviceTarget) {
              return code;
            }
            return deviceTarget.metaName;
          })
          .join(' | '),
      };
    });
  }
  return newData;
}

export function getTableData_WB(jobType, data, deviceTypeMapping, ticketTypeMapping) {
  let newData = [];
  if (data.length) {
    newData = data.map(item => {
      const deviceTarget = deviceTypeMapping[item.deviceType];
      const ticketTarget = ticketTypeMapping[item.maintenanceType];
      return {
        jobId: item.id,
        name: item.configName,
        scheduleType: jobType,
        subJobTypeCode: item.deviceType,
        subJobTypeName: deviceTarget ? deviceTarget.metaName : item.deviceType,
        jobTypeCode: item.maintenanceType,
        jobTypeName: ticketTarget ? ticketTarget.taskValue : item.maintenanceType,
      };
    });
  }
  return newData;
}

/**
 * 获取配置子类型的name
 * @param {object} inspectItem 巡检项
 * @param {undefined | Object} deviceTypeMapping 设备类型
 * @param {undefined | Object} roomTypes 包间类型
 */
function getSubJobTypeName({ inspectItem, deviceTypeMapping, roomTypes }) {
  if (inspectItem.inspectSubject === INSPECTION_TYPE_KEY_MAP.DEVICE) {
    const deviceTarget = deviceTypeMapping?.[inspectItem.subTypeCode];
    return deviceTarget ? deviceTarget.metaName : inspectItem.subTypeCode;
  }
  return roomTypes && roomTypes[inspectItem.subTypeCode]
    ? roomTypes[inspectItem.subTypeCode]
    : inspectItem.subTypeCode;
}
