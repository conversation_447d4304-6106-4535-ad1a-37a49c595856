import React from 'react';

import Form from '@ant-design/compatible/es/form';

import UserSelect from '@manyun/dc-brain.legacy.components/user-select';

/**
 * Demo for `UserSelect`.
 * @param {object} props
 * @param {import('antd-3/lib/form/Form').WrappedFormUtils} [props.form]
 */
export function CustomizedForm({ form: { getFieldDecorator } }) {
  return (
    <Form layout="inline">
      <Form.Item label="用户名">
        {getFieldDecorator('user')(
          <UserSelect style={{ width: 200 }} allowClear reserveSearchValue />
        )}
      </Form.Item>
    </Form>
  );
}

export default Form.create()(CustomizedForm);
