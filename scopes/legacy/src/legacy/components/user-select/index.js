import React from 'react';

import { ApiSelect } from '@galiojs/awesome-antd';
import omit from 'lodash/omit';

import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';

import * as userManageService from '@manyun/dc-brain.legacy.services/userManageService';

/**
 * @typedef {import('antd-3/lib/select').SelectProps} Props
 *
 * @typedef {(option: any) => React.ReactNode} Callback
 *
 * @typedef FieldNames
 * @property { string | Callback } [key='id'] 指定选项中哪个属性作为 `key`。
 * @property { string | Callback } [value='id'] 指定选项中哪个属性作为 `value`。
 * @property { string | Callback } [label] 指定选项中哪个属性作为 `label`。
 * @property { string | Callback } [dataLabel] 指定选项中哪个属性回填到选择框内。
 *
 * @typedef CustomizedProps
 * @property {FieldNames} [fieldNames]
 * @property {boolean} [reserveSearchValue=false] 如果为 `true`，将会在用户输入关键字时触发 `onChange`
 * @property {boolean} [showSystem=false] 如果为 `true`，将会展示 `系统` 候选项
 * @property {number[]} [disabledKeys=[]]
 * @property {import('antd-3/lib/tooltip').TooltipProps} optionDisabledTooltip
 * @property {(keyword: string) => any} dataService 自定义dataService
 */

/**
 * Select a user or multiple users.
 * It is based on antd.Select component, and it's `labelInValue` by default.
 * @augments {React.PureComponent<Props & CustomizedProps>}
 *
 * @example <caption>Controlled Components</caption>
 * ```jsx
 * <UserSelect value={{ key: 14, label: '王彦苏' }} />
 * ```
 */
export default class UserSelect extends React.PureComponent {
  /**
   * @type {Partial<CustomizedProps>}
   */
  static defaultProps = {
    reserveSearchValue: false,
    disabledKeys: [],
    dataService: userManageService.fetchUserListByKey,
  };

  static isReservedSearchValue = Symbol('isReservedSearchValue');

  _changeHandler = (value, optionNode, optionElem) => {
    const { onChange } = this.props;
    if (typeof onChange != 'function') {
      return;
    }
    // when clearing the selected value
    if (optionNode === undefined) {
      onChange(value);
      return;
    }

    // mode === 'multiple'
    if (Array.isArray(value)) {
      const vals = value.map(({ key, label }) => {
        const option = optionNode.find(option => {
          const optionKey = option.id;
          return key === optionKey;
        });

        if (!option) {
          return { key, label };
        }

        return { key, label, ...option };
      });
      onChange(vals);
      return;
    }

    onChange({ ...value, ...optionNode });
  };

  _dataServiceHandler = async keyword => {
    const { reserveSearchValue, showSystem, onChange, dataService } = this.props;

    if (reserveSearchValue && typeof onChange == 'function') {
      onChange({
        key: keyword,
        label: keyword,
        [UserSelect.isReservedSearchValue]: true,
      });
    }

    const { response } = await dataService(keyword);
    const users = response?.data || [];

    if (!showSystem) {
      return users;
    }

    return [SYSTEM_OPTION, ...users];
  };

  _renderOptionLabel = option => {
    const { id, userName, loginName } = option;
    const { disabledKeys, optionDisabledTooltip } = this.props;

    return disabledKeys.includes(id) && optionDisabledTooltip ? (
      <Tooltip placement="topLeft" mouseEnterDelay={0.5} {...optionDisabledTooltip}>
        {defaultLabelRenderer(userName, loginName)}
      </Tooltip>
    ) : (
      defaultLabelRenderer(userName, loginName)
    );
  };

  render() {
    const { disabledKeys = [] } = this.props;

    return (
      <ApiSelect
        trigger="onSearch"
        allowClear
        labelInValue
        dropdownMatchSelectWidth={false}
        optionLabelProp="data-label"
        optionWithValue
        disabledOptionValues={disabledKeys}
        fieldNames={{
          label: this._renderOptionLabel,
          value: 'id',
          dataLabel: 'userName',
          key: 'id',
        }}
        dataService={this._dataServiceHandler}
        {...omit(this.props, CUSTOMIZED_PROPS)}
        onChange={this._changeHandler}
      />
    );
  }
}

const CUSTOMIZED_PROPS = ['disabledKeys', 'dataService', 'reserveSearchValue'];

const SYSTEM_OPTION = {
  id: 0,

  // 切记：
  // `系统` 是前端把后端返回的 `SYSTEM` 翻译后的文案，不能拿这个文案当做用户名去查询
  userName: '系统',
  // 如果业务场景只能使用用户名查询，那么优先取 `_userName` 字段
  _userName: 'SYSTEM',

  loginName: null,
};

function defaultLabelRenderer(userName, loginName) {
  return (
    <div>
      <Typography.Text>{userName}</Typography.Text>
      <br />
      <Typography.Text type="secondary">{loginName}</Typography.Text>
    </div>
  );
}
