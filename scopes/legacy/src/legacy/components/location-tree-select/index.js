import React, { useEffect } from 'react';

import { TreeSelect } from '@manyun/base-ui.ui.tree-select';

import { useSpaces } from '@manyun/resource-hub.ui.location-tree-select';

// import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';

/**
 * @typedef {import('antd-3/lib/tree-select').TreeSelectProps} TreeSelectProps
 *
 *
 * @typedef Props
 * @property {string} [idc] 指定机房
 * @property {boolean} [showRoom] 是否需要选择到包间
 * @property {boolean} [currentAuthorize] 是否需要选择使用当前用户权限资源
 */

/**
 * @type {React.ForwardRefExoticComponent<Props & TreeSelectProps & React.RefAttributes<any>>}
 */
export const LocationTreeSelect = React.forwardRef(
  ({ idc, showRoom = false, currentAuthorize = false, allowClear = true, ...props }, ref) => {
    const nodeTypes = ['IDC', 'BLOCK'];
    if (showRoom) {
      nodeTypes.push('ROOM');
    }
    const [{ treeSpaces }] = useSpaces({
      authorizedOnly: currentAuthorize,
      nodeTypes,
      disabledTypes: [],
      idc,
      includeVirtualBlocks: false,
    });

    // const dispatch = useDispatch();
    useEffect(() => {
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return <TreeSelect ref={ref} allowClear={allowClear} treeData={treeSpaces} {...props} />;
  }
);

LocationTreeSelect.displayName = 'LocationTreeSelect';

export default LocationTreeSelect;
