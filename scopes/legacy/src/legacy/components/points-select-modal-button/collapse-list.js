import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';

import get from 'lodash/get';

import { Button } from '@manyun/base-ui.ui.button';
import { Collapse } from '@manyun/base-ui.ui.collapse';
import { List } from '@manyun/base-ui.ui.list';

export function CollapseList({ deviceTypeMap, deviceTypes, childItemsMap, onDeleteChildItem }) {
  const [activeKeys, setActiveKeys] = useState([]);

  useEffect(() => {
    setActiveKeys(deviceTypes);
  }, [deviceTypes]);

  return (
    <Collapse activeKey={activeKeys} onChange={key => setActiveKeys(key)}>
      {Array.isArray(deviceTypes) &&
        deviceTypes.map(deviceType => (
          <Collapse.Panel
            key={deviceType}
            header={get(deviceTypeMap, [deviceType, 'metaName'], deviceType)}
            style={customPanelStyle}
          >
            <List
              size="small"
              itemLayout="horizontal"
              dataSource={childItemsMap[deviceType]}
              renderItem={item => (
                <List.Item
                  style={{ paddingTop: 4, paddingBottom: 4 }}
                  actions={[
                    <Button
                      compact
                      type="link"
                      onClick={() => {
                        onDeleteChildItem(deviceType, item.metaCode, item.parentCode);
                      }}
                    >
                      删除
                    </Button>,
                  ]}
                >
                  {item.typeText + '-' + item.name}
                </List.Item>
              )}
            />
          </Collapse.Panel>
        ))}
    </Collapse>
  );
}

const mapStateToProps = ({ common: { deviceCategory } }) => ({
  deviceTypeMap: get(deviceCategory, 'normalizedList'),
});

export default connect(mapStateToProps)(CollapseList);

const customPanelStyle = {
  background: 'transparent',
};
