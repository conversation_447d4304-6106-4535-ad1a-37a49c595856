import React, { useRef, useState } from 'react';

import uniq from 'lodash/uniq';

import { G<PERSON>Wrapper, ModalButton, TinyCard } from '@manyun/dc-brain.legacy.components';

import CollapseList from './collapse-list';
import SearchTree from './search-tree';

/**
 * 批量选择设备类型下的测点
 * @param {object} props
 * @param {string} props.text
 * @param {string} [props.title] 若未传入，则使用 `text`
 * @param {string[]} [props.disabledTreeNodeKeys] 需要禁用复选框的节点 `key`，`key` 为节点的 `metaCode`
 * @param {(arg?: { deviceTypes: string[]; pointsMap: object }) => void} props.onOk 点击 Modal 的确定按钮的回调
 * @param {string[]} [props.displayDevice]  测点树显示的设备类型,不传该字段时显示所有的类型
 * @param {Array} [props.visibleLoadTypes] ['snDevice','noSnDevice','space' ], snDevice 表示有编号设备，noSnDevice 表示无编号类型， space 空间类型,不传默认为 所有
 */
function PointsSelectModalButton({
  text,
  title = text,
  disabledTreeNodeKeys = [],
  onOk,
  displayDevice,
  visibleLoadTypes,
  invalidPoint={}
}) {
  const [deviceTypes, setDeviceTypes] = useState([]);
  const [checkedItemKeys, setCheckedItemKeys] = useState([]);
  const childItemsMapRef = useRef({});

  function clearCache() {
    setDeviceTypes([]);
    setCheckedItemKeys([]);
    childItemsMapRef.current = {};
  }

  function deleteChildItemHandler(deviceType, childItemKey, parentKey) {
    const removeKeys = [childItemKey, parentKey];
    const childItems = childItemsMapRef.current[deviceType];
    if (!(Array.isArray(childItems) && childItems.length)) {
      return;
    }
    setCheckedItemKeys(prevCheckedKeys =>
      prevCheckedKeys.filter(checkedKey => !removeKeys.includes(checkedKey))
    );
    const newChildItems = childItems.filter(({ metaCode }) => !removeKeys.includes(metaCode));
    if (!newChildItems.length) {
      // delete `deviceType` too.
      delete childItemsMapRef.current[deviceType];
      setDeviceTypes(prevDeviceTypes => prevDeviceTypes.filter(t => t !== deviceType));
      return;
    }
    childItemsMapRef.current[deviceType] = newChildItems;
    setDeviceTypes(prevDeviceTypes => [...prevDeviceTypes]);
  }

  return (
    <ModalButton
      compact
      type="link"
      text={text}
      width={1200}
      title={title}
      destroyOnClose
      okButtonProps={{ disabled: !deviceTypes.length }}
      onOk={() => {
        onOk({
          deviceTypes,
          pointsMap: childItemsMapRef.current,
          childItemsMap: childItemsMapRef.current, // TODO: remove this deprecated argument.
        });

        clearCache();

        return Promise.resolve(true);
      }}
      onCancel={() => {
        clearCache();
      }}
    >
      <GutterWrapper flex>
        <TinyCard style={{ flex: 3 }}>
          <SearchTree
            disabledTreeNodeKeys={disabledTreeNodeKeys}
            checkedKeys={checkedItemKeys}
            onCheck={(checkedKeys, childItems) => {
              setCheckedItemKeys(checkedKeys);

              // 每次 `onCheck` 触发后都会把当前已选中的所有节点都传递过来，
              // 而这里支持临时存一下当前选中的节点，所以需要清空下上一次的缓存
              childItemsMapRef.current = {};

              if (!childItems.length) {
                setDeviceTypes([]);
                return;
              }

              const map = childItemsMapRef.current;
              const deviceTypes = [];
              childItems.forEach(item => {
                deviceTypes.push(item.deviceType);
                map[item.deviceType] = [...(map[item.deviceType] || []), item];
              });

              setDeviceTypes(uniq(deviceTypes));
            }}
            displayDevice={displayDevice}
            visibleLoadTypes={visibleLoadTypes}
            invalidPoint={invalidPoint}
          />
        </TinyCard>
        <TinyCard
          style={{
            flex: 5,
            maxHeight: (500 / 900) * window.innerHeight + 48 + 16,
            overflowY: 'auto',
          }}
        >
          <CollapseList
            deviceTypes={deviceTypes}
            childItemsMap={childItemsMapRef.current}
            onDeleteChildItem={deleteChildItemHandler}
          />
        </TinyCard>
      </GutterWrapper>
    </ModalButton>
  );
}

export default React.forwardRef((props, ref) => (
  <PointsSelectModalButton forwardedRef={ref} {...props} />
));
