import React from 'react';
import { connect } from 'react-redux';

import cloneDeep from 'lodash/cloneDeep';
import flatten from 'lodash/flatten';

import { fetchPointsByCondition } from '@manyun/resource-hub.service.fetch-points-by-condition';

import { ApiTree } from '@manyun/dc-brain.legacy.components';
import { DEVICE_SPACE_TYPE_KEY_MAP } from '@manyun/dc-brain.legacy.constants/enums/deviceType';
import { POINT_TYPE_CODE_MAP } from '@manyun/dc-brain.legacy.constants/point';
import { infrastructureService } from '@manyun/dc-brain.legacy.services';

export function SearchTree({
  baseTreeData,
  disabledTreeNodeKeys,
  checkedKeys,
  onCheck,
  defaultExpandedKeys,
  invalidPoint
}) {
  async function childNodesDataService({ metaType, metaCode, deviceType, extCount, code }) {
    // 文案为 `原始测点、自定义测点` 的节点
    if (metaType === META_TYPE_MAP.VIRTUAL_ITEM) {
      return Promise.resolve([]);
    }
  
    // 查询设备类型下的点位，排除扩展点位
    if (metaType === META_TYPE_MAP.C2) {
      const { data: points } = await fetchPointsByCondition({
        ...invalidPoint,
        deviceType: metaCode,
        dataTypeList: ['AI', 'DI'],
        isRemoveSub: true, // 排除扩展点位
      });
      const children = [];
      if (points) {
        const originalMetaCode = `${metaCode}_$$_original-points`;
        const notOriginalMetaCode = `${metaCode}_$$_not-original-points`;
        const result = points.data.map(point => point.toApiObject());
        const { originals, notOriginals } = result.reduce(
          (map, point) => {
            if (point.pointType.code === POINT_TYPE_CODE_MAP.ORI) {
              map.originals.push(mapTreeNodeProps(point, originalMetaCode, 'point', '原始测点'));
            } else {
              map.notOriginals.push(
                mapTreeNodeProps(point, notOriginalMetaCode, 'point', '自定义测点')
              );
            }
  
            return map;
          },
          { originals: [], notOriginals: [] }
        );
        children.push(
          {
            metaName: '原始测点',
            metaCode: originalMetaCode,
            metaType: META_TYPE_MAP.VIRTUAL_ITEM,
            parentCode: deviceType,
            children: originals,
          },
          {
            metaName: '自定义测点',
            metaCode: notOriginalMetaCode,
            metaType: META_TYPE_MAP.VIRTUAL_ITEM,
            parentCode: deviceType,
            children: notOriginals,
          }
        );
      }
  
      return Promise.resolve(children);
    }
  
    // 查询主点位下的扩展点位
    if (metaType === META_TYPE_MAP.REAL_ITEM && extCount) {
      const { response: points } = await infrastructureService.fetchExtendedPoints({
        deviceType,
        pointCode: code,
      });
  
      if (!(points && Array.isArray(points.data))) {
        return Promise.resolve([]);
      }
  
      return Promise.resolve(
        points.data.map(point => mapTreeNodeProps(point, code, 'point', '原始测点-扩展测点'))
      );
    }
  
    return Promise.resolve([]);
  }
  return (
    <ApiTree
      treeStyle={{ maxHeight: (500 / 900) * window.innerHeight, overflowY: 'auto' }}
      showSearch
      checkable
      selectable={false}
      checkedKeys={checkedKeys}
      defaultExpandedKeys={defaultExpandedKeys}
      fieldNames={{
        key: ({ metaType, metaCode }) => {
          if ([META_TYPE_MAP.C1, META_TYPE_MAP.C2].includes(metaType)) {
            return `${metaType}${metaCode}`;
          }
          return metaCode;
        },
        title: 'metaName',
        parentKey: 'parentCode',
        disableCheckbox: ({ metaCode, metaType, children, extCount }) => {
          if ([META_TYPE_MAP.C1, META_TYPE_MAP.C2].includes(metaType)) {
            return true;
          }
          if (metaType === META_TYPE_MAP.VIRTUAL_ITEM) {
            if (!(Array.isArray(children) && children.length)) {
              return true;
            } else {
              if (children.every(({ metaCode }) => disabledTreeNodeKeys.includes(metaCode))) {
                return true;
              }
            }
          }
          if (metaType === META_TYPE_MAP.REAL_ITEM) {
            if (extCount) {
              return !(Array.isArray(children) && children.length > 0); // 如果未展开主点位，则禁用主点位的复选框；反之，启用。
            }
            return disabledTreeNodeKeys.includes(metaCode);
          }
          return false;
        },
      }}
      treeNodeFilterProp="metaName"
      filterLeafTreeNode={({ metaType, extCount }) => {
        return metaType === META_TYPE_MAP.REAL_ITEM && !extCount;
      }}
      dataService={() => Promise.resolve(cloneDeep(baseTreeData))}
      childNodesDataService={childNodesDataService}
      onCheck={(checkedKeys, { checkedNodes }) => {
        const checkedItems = checkedNodes
          .map(checkedNode => checkedNode.dataRef)
          .filter(({ metaType }) => metaType === META_TYPE_MAP.REAL_ITEM);
        onCheck(checkedKeys, checkedItems);
      }}
    />
  );
}

const mapStateToProps = (
  {
    common: {
      deviceCategory: { treeList, parallelList },
    },
  },
  { displayDevice, visibleLoadTypes }
) => {
  let secondCategory = [];
  if (visibleLoadTypes) {
    const filteredData = treeList.filter(({ metaStyle, numbered }) => {
      if (visibleLoadTypes.includes('space') && metaStyle === DEVICE_SPACE_TYPE_KEY_MAP.SPACE) {
        return true;
      }
      if (
        visibleLoadTypes.includes('snDevice') &&
        numbered &&
        metaStyle === DEVICE_SPACE_TYPE_KEY_MAP.DEVICE
      ) {
        return true;
      }
      if (
        visibleLoadTypes.includes('noSnDevice') &&
        !numbered &&
        metaStyle === DEVICE_SPACE_TYPE_KEY_MAP.DEVICE
      ) {
        return true;
      }
      return false;
    });
    secondCategory = flatten(filteredData.map(item => item.children));
  } else {
    secondCategory = treeList;
  }
  let treeData = secondCategory;
  let defaultExpandedKeys = [];

  if (displayDevice) {
    const thirdCateorgy = flatten(secondCategory.map(item => item.children)); /* 不显示第二级节点 */
    treeData = thirdCateorgy.filter(({ metaCode }) => displayDevice.includes(metaCode));
    defaultExpandedKeys = displayDevice.map(code => `C2${code}`);
  }

  return {
    baseTreeData: {
      treeList: treeData,
      parallelList,
    },
    defaultExpandedKeys,
  };
};

export default connect(mapStateToProps)(SearchTree);

const META_TYPE_MAP = {
  C1: 'C1', // 设备一级分类
  C2: 'C2', // 设备二级分类
  VIRTUAL_ITEM: 'VIRTUAL_ITEM', // 虚拟节点：原始测点、自定义测点
  REAL_ITEM: 'REAL_ITEM', // 真实节点：测点
};

function mapTreeNodeProps(data, parentCode, type, typeText) {
  if (type === 'point') {
    return {
      metaName: data.name,
      metaCode: data.deviceType + '_$$_' + data.pointCode,
      metaType: META_TYPE_MAP.REAL_ITEM,
      parentCode, // 客户端过滤时依赖 `parentCode`
      typeText,
      type: 'POINT',
      ...data,
    };
  }
  throw new Error(`type(${type}) not supported.`);
}


