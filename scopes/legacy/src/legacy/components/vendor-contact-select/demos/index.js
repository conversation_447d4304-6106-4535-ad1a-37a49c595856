import React from 'react';

import Form from '@ant-design/compatible/es/form';

import VendorContactSelect from '@manyun/dc-brain.legacy.components/vendor-contact-select';

/**
 * Demo for `VendorContactSelect`.
 * @param {object} props
 * @param {import('antd-3/lib/form/Form').WrappedFormUtils} [props.form]
 */
export function CustomizedForm({ form: { getFieldDecorator } }) {
  return (
    <Form layout="inline">
      <Form.Item label="通知对象">
        {getFieldDecorator('person')(
          <VendorContactSelect
            style={{ width: 200 }}
            allowClear
            reserveSearchValue
            vendorCode="000"
          />
        )}
      </Form.Item>
    </Form>
  );
}

export default Form.create()(CustomizedForm);
