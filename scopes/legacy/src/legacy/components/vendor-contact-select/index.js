import React from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { Typography } from '@manyun/base-ui.ui.typography';

import UserSelect from '@manyun/dc-brain.legacy.components/user-select';
import * as vendorService from '@manyun/dc-brain.legacy.services/vendorService';

const MAIN_CONTACTS = 'mainContacts';

/**
 * @typedef CustomizedProps
 * @property {string} vendorCode 厂商简称
 */
export default class VendorContactsSelect extends React.PureComponent {
  dataService = async () => {
    const { vendorCode } = this.props;
    const { response, error } = await vendorService.getVendorDetail({ vendorCode });
    let users = [];
    if (error) {
      message.error(error);
    }
    if (response) {
      users = [
        {
          id: MAIN_CONTACTS,
          name: response.contactName,
          email: response.contactEmail,
          mobile: response.contactMobile,
        },
        ...response.vendorContacts,
      ].filter(user => user.name !== null);
    }
    return {
      response: {
        data: users,
      },
    };
  };

  labelRenderer = option => {
    const { name, email, mobile } = option;
    return (
      <div>
        <Typography.Text>{name}</Typography.Text>
        <br />
        <Typography.Text type="secondary">邮箱：{email}</Typography.Text>
        <br />
        <Typography.Text type="secondary">电话：{mobile}</Typography.Text>
      </div>
    );
  };

  render() {
    return (
      <UserSelect
        showSearch
        {...this.props}
        trigger="onFocus"
        dataService={this.dataService}
        labelRenderer={this.labelRenderer}
        fieldNames={{
          label: this.labelRenderer,
          value: 'id',
          dataLabel: 'name',
          key: 'id',
        }}
      />
    );
  }
}
