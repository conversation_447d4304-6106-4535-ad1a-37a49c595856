import React from 'react';

import { Card } from '@manyun/base-ui.ui.card';

/**
 * @typedef { import('antd-3/lib/card').CardProps } CardProps
 *
 * @typedef {object} CustomizedProps
 */

/**
 * 封装于 `antd` 的 `Card` 组件。
 * @param {CustomizedProps & CardProps} props `TinyCard` 组件的 `props`
 */
export function TinyCard({ title, ...props }) {
  return <Card title={title} {...props} />;
}

export default TinyCard;
