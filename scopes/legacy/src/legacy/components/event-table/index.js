import dayjs from 'dayjs';
import React, { useMemo, useState } from 'react';
import { Link } from 'react-router-dom';

import { useAuthorized } from '@manyun/auth-hub.hook.gql.use-authorized';
import { User } from '@manyun/auth-hub.ui.user';
import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { Button } from '@manyun/base-ui.ui.button';
import { Divider } from '@manyun/base-ui.ui.divider';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { message } from '@manyun/base-ui.ui.message';
import { Popover } from '@manyun/base-ui.ui.popover';
import { Space } from '@manyun/base-ui.ui.space';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { EditColumns } from '@manyun/dc-brain.ui.edit-columns';
import {
  BackendFalseAlarm,
  EVENT_STEP_STATUS_MAP_TEXT,
  EventSpecificProcessStatus,
  FAULT_TARGET_TYPE_TEXT,
  FalseAlarmMap,
  getEventLocales,
} from '@manyun/ticket.model.event';
import { EventConfigurationDrawerButton, EventConfigurationModal } from '@manyun/ticket.page.event';
import { FaultTargetLink } from '@manyun/ticket.page.event-detail';
import { generateEventDetailRoutePath } from '@manyun/ticket.route.ticket-routes';
import { exportEvents } from '@manyun/ticket.service.export-events';
import { fetchEventInfluences } from '@manyun/ticket.service.fetch-event-influences';
import { EventLocationDrawer } from '@manyun/ticket.ui.event-mutator';
import { TicketSlaText } from '@manyun/ticket.ui.tickets-table';

import { TinyTable } from '@manyun/dc-brain.legacy.components';

import { key } from '../svgicons';
import styles from './index.less';

const getColumns = (operation, showEditColumns, featuresListInfoIsFull) => {
  // 阳高没有操作列
  if (operation && !featuresListInfoIsFull) {
    return [...showEditColumns, operation];
  }
  return showEditColumns;
};

export const EVENT_STEP_STATUS_MAP_TAG_TYPE = {
  [EventSpecificProcessStatus.Init]: 'default',
  [EventSpecificProcessStatus.Emergency]: 'processing',
  [EventSpecificProcessStatus.Fix]: 'processing',
  [EventSpecificProcessStatus.Recovery]: 'processing',
  [EventSpecificProcessStatus.Debrief]: 'processing',
  [EventSpecificProcessStatus.Review]: 'warning',
  [EventSpecificProcessStatus.Close]: 'default',
  [EventSpecificProcessStatus.Finished]: 'processing',
};

/**
 * 变更的通用的列表
 * @param {object} props
 * @param {string[]} [props.showColumns=[]] 需要展示的列 给定dataIndex
 * @param {JSX.Element | JSX.Element[]} [props.actions=null] 表格上方的操作按钮
 * @param {JSX.Element | JSX.Element[]} [props.operation=null] 操作列
 * @param {string} [props.editColumnsPersistenceKey] 定制列的key，有则展示定制列
 * @param {Array<{ dataIndex: string; fixed: 'left'|'right'|boolean }>} [props.fixedColumns] 锁定列配置
 */
export function EventTable({
  dataSource,
  loading,
  className,
  // metaCategoryEntities,
  operation = null,
  showColumns = [],
  fixedColumns = [],
  actions = null,
  editColumnsPersistenceKey,
  exportButtonPersistenceKey,
  configurePersistenceKey,
  exportSearchParams = {},
  ...props
}) {
  const locales = useMemo(() => {
    return getEventLocales();
  }, []);
  const [authorized] = useAuthorized({ checkByCode: 'element_ticket_event-configuration' });
  const [configUtil] = useConfigUtil();
  const {
    events: { features },
  } = configUtil.getScopeCommonConfigs('ticket');
  const { isFalseAlarm } = features;
  const featureIsEventConfigWithProcessEngineRequired =
    features.isEventConfigWithProcessEngine === 'required';
  const featuresListInfoIsFull = features.listInfo === 'full';
  const featuresOnwerMultiple = features.onwerMultiple;
  const showDefaultColumns = useMemo(() => {
    const defaultColumns = [
      {
        title: '事件ID',
        dataIndex: featureIsEventConfigWithProcessEngineRequired ? 'eventNo' : 'id',
        fixed: 'left',
        disable: true,
        render: (_, { id, eventNo }) => {
          const text = featureIsEventConfigWithProcessEngineRequired ? eventNo : id;
          return (
            <Link
              key={text}
              target="_blank"
              to={generateEventDetailRoutePath({
                id: text,
              })}
            >
              {text}
            </Link>
          );
        },
      },
      {
        title: featuresListInfoIsFull ? '楼栋' : '位置',
        dataIndex: 'blockTag',
        render: (_, record) => {
          if (featuresListInfoIsFull) {
            const text = record.blockGuidList?.map((item, index) => {
              return (
                <span key={item}>
                  {item}
                  {index + 1 !== record.blockGuidList.length && (
                    <Divider type="vertical" spaceSize="mini" />
                  )}
                </span>
              );
            });
            return (
              <Typography.Text style={{ width: 216 }} ellipsis={{ tooltip: text }}>
                {text}
              </Typography.Text>
            );
          }
          return record.blockTag || record.idcTag;
        },
      },
      {
        title: '事件标题',
        dataIndex: 'eventTitle',
        render(eventTitle) {
          if (featuresListInfoIsFull) {
            return (
              <Typography.Text style={{ width: 240 }} ellipsis={{ tooltip: eventTitle }}>
                {eventTitle}
              </Typography.Text>
            );
          }
          return eventTitle;
        },
      },
      {
        title: '事件来源',
        dataIndex: 'eventSourceName',
      },
      {
        title: '事件专业',
        dataIndex: 'majorName',
        key: 'majorName',
      },
      {
        title: '事件类型',
        dataIndex: 'topCategoryName',
        key: 'topCategoryNameYG',
      },
      // {
      //   title: '事件子类型',
      //   dataIndex: 'secondCategoryName',
      //   key: 'secondCategoryNameYG',
      // },

      {
        title: '事件级别',
        dataIndex: 'eventLevelName',
        sorter: true,
      },
      {
        title: '事件状态',
        dataIndex: 'status',
        key: 'statusYG',
        render: status => (
          <Tag color={EVENT_STEP_STATUS_MAP_TAG_TYPE[status]}>
            {locales.specificProcessStatus[status]}
          </Tag>
        ),
      },
      {
        title: '故障位置',
        dataIndex: 'eventLocation',
        key: 'eventLocation',
        render: (_, { eventNo }) => <EventLocationDrawer eventId={eventNo} />,
      },

      {
        title: '是否误报',
        dataIndex: 'isFalseAlarm',
        key: isFalseAlarm === 'disabled_required' ? 'isFalseAlarm' : null,
        render: text => (
          <Typography.Text type={text === BackendFalseAlarm.None ? undefined : 'danger'}>
            {FalseAlarmMap[text]}
          </Typography.Text>
        ),
      },
      {
        title: '事件类型',
        dataIndex: 'topCategoryName',
        key: 'topCategoryNameGOC',
      },
      {
        title: '事件子类型',
        dataIndex: 'secondCategoryName',
        key: 'secondCategoryNameGOC',
      },
      {
        title: '目标类型',
        dataIndex: 'infoType',
        key: 'infoType',
        render: text => <span>{FAULT_TARGET_TYPE_TEXT[text]}</span>,
      },
      {
        title: '目标名称',
        dataIndex: 'deviceModels',
        key: 'deviceModels',
        width: featuresListInfoIsFull ? 300 : 264,
        render(_ignored, { infoType, deviceModels }) {
          return (
            <FaultTargetLink
              type={infoType}
              causeDevices={deviceModels}
              width={featuresListInfoIsFull ? 300 : 264}
            />
          );
        },
      },

      {
        title: '事件描述',
        dataIndex: 'eventDesc',
        render(title) {
          return (
            <Typography.Text
              style={{ width: featuresListInfoIsFull ? 240 : 264 }}
              ellipsis={{ tooltip: title }}
            >
              {title}
            </Typography.Text>
          );
        },
      },
      {
        title: '应急处理时长',
        dataIndex: 'emergencyDuration',
        key: 'emergencyHandlingTime',
        render: (_, { phaseRecordList }) => {
          const sla = phaseRecordList?.filter(item => item.eventPhase === 'YG_EMERGENCY')?.[0]?.sla;
          if (sla) {
            return (
              <TicketSlaText
                taskSla={sla}
                delay={sla}
                unit="SECOND"
                effectTime={null}
                endTime={null}
              />
            );
          }
          return '--';
        },
      },
      {
        title: '处理过程',
        dataIndex: 'phaseRecordList',
        key: 'phaseRecordList',
        render: (_, { phaseRecordList }) => {
          const phaseText = {
            [EventSpecificProcessStatus.Emergency]: '事件应急',
            [EventSpecificProcessStatus.Fix]: '故障修复',
            [EventSpecificProcessStatus.Recovery]: '系统修复',
          };
          return (
            <Popover
              overlayInnerStyle={{ width: 520 }}
              content={
                phaseRecordList?.length
                  ? phaseRecordList.map(item => (
                      <>
                        <div>[{phaseText[item.eventPhase]}]</div>
                        {item?.processRecordList?.length
                          ? item.processRecordList.map((item, index) => (
                              <div key={item.handleContent}>
                                {index + 1}.{item.handleContent}（
                                <User id={item.handlerId} showAvatar={false} />，
                                {dayjs(item.handleTime).format('YYYY-MM-DD HH:mm:ss')}）
                              </div>
                            ))
                          : '--'}
                      </>
                    ))
                  : '--'
              }
              title="处理过程"
            >
              <Button type="link" compact>
                查看
              </Button>
            </Popover>
          );
        },
      },
      {
        title: '定位原因',
        dataIndex: 'detectReason',
        key: 'detectReason',
        render(title) {
          return (
            <Typography.Text style={{ width: 216 }} ellipsis={{ tooltip: title }}>
              {title ?? '--'}
            </Typography.Text>
          );
        },
      },
      {
        title: '业务影响',
        dataIndex: 'eventInfluence',
        key: 'influence',
        render: (_, { id }) => {
          return <InfluencePopover eventId={id} />;
        },
      },
      {
        title: '缓解方案',
        dataIndex: 'relieveDesc',
        render(title) {
          return (
            <Typography.Text style={{ width: 264 }} ellipsis={{ tooltip: title }}>
              {title}
            </Typography.Text>
          );
        },
      },
      {
        title: '事件状态',
        dataIndex: 'status',
        key: 'statusGOC',
        render: status =>
          featureIsEventConfigWithProcessEngineRequired
            ? locales.specificProcessStatus[status]
            : EVENT_STEP_STATUS_MAP_TEXT[status],
      },
      {
        title: '解决方案',
        dataIndex: 'resolveDesc',
        render(title) {
          return (
            <Typography.Text style={{ width: 264 }} ellipsis={{ tooltip: title }}>
              {title}
            </Typography.Text>
          );
        },
      },
      {
        title: '创建时间',
        dataIndex: 'gmtCreate',
        dataType: 'datetime',
        sorter: true,
        width: 120,
        className: 'col-padding',
        render: (_, { gmtCreate }) => {
          if (gmtCreate) {
            return (
              <>
                {dayjs(gmtCreate).format('YYYY-MM-DD')}
                <br />
                {dayjs(gmtCreate).format('HH:mm:ss')}
              </>
            );
          }
          return '--';
        },
      },
      {
        title: '发生时间',
        dataIndex: 'occurTime',
        dataType: 'datetime',
        sorter: true,
        width: 120,
        className: 'col-padding',
        render: (_, { occurTime }) => {
          if (occurTime) {
            return (
              <>
                {dayjs(occurTime).format('YYYY-MM-DD')}
                <br />
                {dayjs(occurTime).format('HH:mm:ss')}
              </>
            );
          }
          return '--';
        },
      },
      {
        title: '应急首次通报',
        dataIndex: 'firstReportTime',
        key: 'firstReportTime',
        width: 120,
        className: 'col-padding',
        render: (_, { firstReportTime }) => {
          if (firstReportTime) {
            return (
              <>
                {dayjs(firstReportTime).format('YYYY-MM-DD')}
                <br />
                {dayjs(firstReportTime).format('HH:mm:ss')}
              </>
            );
          }
          return '--';
        },
      },
      {
        title: '应急结束通报',
        dataIndex: 'lastReportTime',
        key: 'lastReportTime',
        width: 120,
        className: 'col-padding',
        render: (_, { lastReportTime }) => {
          if (lastReportTime) {
            return (
              <>
                {dayjs(lastReportTime).format('YYYY-MM-DD')}
                <br />
                {dayjs(lastReportTime).format('HH:mm:ss')}
              </>
            );
          }
          return '--';
        },
      },
      {
        title: '关闭时间',
        dataIndex: 'closeTime',
        dataType: 'datetime',
        sorter: true,
        width: 120,
        className: 'col-padding',
        render: (_, { closeTime }) => {
          if (closeTime) {
            return (
              <>
                {dayjs(closeTime).format('YYYY-MM-DD')}
                <br />
                {dayjs(closeTime).format('HH:mm:ss')}
              </>
            );
          }
          return '--';
        },
      },
      {
        title: featureIsEventConfigWithProcessEngineRequired ? '责任人' : 'Owner',
        dataIndex: 'eventOwnerId',
        render: (text, { curHandlerId, curHandlerName, eventOwnerInfoList, eventOwnerName }) => {
          if (featuresOnwerMultiple) {
            return (
              <Typography.Text
                ellipsis={{
                  tooltip: eventOwnerInfoList?.map((item, index) => (
                    <>
                      <UserLink key={item.id} userName={item.userName} userId={item.id} external />
                      {index + 1 !== eventOwnerInfoList?.length && (
                        <Divider type="vertical" spaceSize="mini" />
                      )}
                    </>
                  )),
                }}
                style={{ width: 116 }}
              >
                {eventOwnerInfoList?.map((item, index) => (
                  <>
                    <UserLink key={item.id} userName={item.userName} userId={item.id} external />
                    {index + 1 !== eventOwnerInfoList?.length && (
                      <Divider type="vertical" spaceSize="mini" />
                    )}
                  </>
                ))}
              </Typography.Text>
            );
          }
          return <UserLink userId={text} userName={eventOwnerName} external />;
        },
      },
      {
        title: '处理人',
        dataIndex: 'curHandlerId',
        render: (_, { curHandlerId, curHandlerName }) =>
          curHandlerId && curHandlerName ? (
            <UserLink userId={curHandlerId} userName={curHandlerName} external />
          ) : (
            '--'
          ),
      },
      {
        title: '创建人',
        dataIndex: 'createUserId',
        render: (text, { createUserName }) => (
          <User.Link id={text} name={createUserName} external useNativeLink />
        ),
      },
    ];
    let _showColumns = defaultColumns;
    if (featureIsEventConfigWithProcessEngineRequired) {
      _showColumns = _showColumns.filter(column => column.dataIndex !== 'curHandlerId');
    }
    if (showColumns.length) {
      _showColumns = defaultColumns.filter(col => showColumns.includes(col.dataIndex));
    }
    if (featuresListInfoIsFull) {
      _showColumns = _showColumns.filter(col =>
        col.key
          ? col.key !== 'topCategoryNameGOC' &&
            col.key !== 'secondCategoryNameGOC' &&
            col.key !== 'statusGOC' &&
            col.key !== 'isFalseAlarm' &&
            col.key !== 'deviceModels' &&
            col.key !== 'infoType'
          : true
      );
    }
    if (!featuresListInfoIsFull) {
      _showColumns = _showColumns.filter(col =>
        col.key
          ? col.key !== 'topCategoryNameYG' &&
            col.key !== 'secondCategoryNameYG' &&
            col.key !== 'statusYG' &&
            col.key !== 'emergencyHandlingTime' &&
            col.key !== 'phaseRecordList' &&
            col.key !== 'detectReason' &&
            col.key !== 'firstReportTime' &&
            col.key !== 'lastReportTime' &&
            col.key !== 'influence' &&
            col.key !== 'majorName' &&
            col.key !== 'eventLocation'
          : true
      );
    }
    if (fixedColumns.length) {
      fixedColumns.forEach(({ dataIndex, fixed }) => {
        const columnIdx = _showColumns.findIndex(clmn => clmn.dataIndex === dataIndex);
        if (columnIdx > -1) {
          _showColumns[columnIdx].fixed = fixed;
        }
      });
    }
    return _showColumns;
  }, [
    showColumns,
    fixedColumns,
    featureIsEventConfigWithProcessEngineRequired,
    locales,
    featuresListInfoIsFull,
    featuresOnwerMultiple,
    isFalseAlarm,
  ]);

  const [showEditColumns, setShowEditColumns] = useState(showDefaultColumns);

  return (
    <TinyTable
      rowKey="id"
      scroll={{ x: 'max-content' }}
      className="tableStyle"
      columns={getColumns(operation, showEditColumns, featuresListInfoIsFull)}
      actions={
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            width: '100%',
            justifyContent: actions ? 'space-between' : 'flex-end',
          }}
        >
          {actions}
          <Space>
            {configurePersistenceKey && authorized ? (
              featureIsEventConfigWithProcessEngineRequired ? (
                <EventConfigurationDrawerButton />
              ) : (
                <EventConfigurationModal />
              )
            ) : null}
            {exportButtonPersistenceKey && (
              <FileExport
                text=""
                type="text"
                filename={generateExportFileName()}
                data={async () => {
                  const { data, error } = await exportEvents({
                    ...exportSearchParams,
                    exportIncludeFields: showEditColumns
                      .map(item => item.show !== false && item.dataIndex)
                      .filter(Boolean),
                  });
                  if (error) {
                    message.error(error.message);
                    return;
                  }
                  return data;
                }}
              />
            )}
            {editColumnsPersistenceKey && (
              <div style={{ marginLeft: 8 }}>
                <EditColumns
                  uniqKey={editColumnsPersistenceKey}
                  defaultValue={showDefaultColumns}
                  allowSetAsFixed={false}
                  onChange={setShowEditColumns}
                />
              </div>
            )}
          </Space>
        </div>
      }
      align="left"
      loading={loading}
      dataSource={dataSource}
      {...props}
    />
  );
}
function generateExportFileName() {
  const currentMoment = dayjs();
  const dayString = currentMoment.format('YYYY-MM-DD').split('-').join('');
  const timeString = currentMoment.format('HH:mm:ss').split(':').join('');
  return `事件列表_${dayString}_${timeString}`;
}

function InfluencePopover({ eventId }) {
  const [influences, setInfluences] = useState({});
  const [gridInfluence, setGridInfluence] = useState('');

  const getCustomInfluences = async () => {
    const { error, data } = await fetchEventInfluences({ eventId });
    if (error) {
      message.error(error.message);
      return;
    }
    const influenceMap = {};
    data.influenceScope.forEach(item => {
      if (item.influenceType === 'IDC') {
        influenceMap[item.influenceGuid] = {
          type: 'IDC',
          text: `${item.influenceGuid}  全部`,
          tags: [item.influenceGuid],
        };
      }
      if (item.influenceType === 'BLOCK') {
        const [, blockTag] = item.influenceGuid.split('.');
        influenceMap[item.influenceGuid] = {
          type: 'BLOCK',
          text: `${blockTag}  全部`,
          tags: [blockTag],
        };
      }
      if (item.influenceType === 'ROOM') {
        const [, blockTag, roomTag] = item.influenceGuid.split('.');
        influenceMap[item.influenceGuid] = {
          type: 'ROOM',
          text: `${roomTag}  全部`,
          tags: [blockTag],
        };
      }
      if (item.influenceType === 'COLUMN') {
        const [idcTag, blockTag, roomTag, columnTag] = item.influenceGuid.split('.');
        const mapKey = `${idcTag}.${blockTag}.${roomTag}.COLUMN`;
        const columnTags = influenceMap?.[mapKey]?.tags
          ? influenceMap[mapKey].tags.push(`${columnTag}列`)
          : [`${columnTag}列`];
        influenceMap[`${idcTag}.${blockTag}.${roomTag}.COLUMN`] = {
          type: 'COLUMN',
          text: `${roomTag}  ${columnTags.join('、')}`,
          tags: columnTags,
        };
      }
      if (item.influenceType === 'GRID') {
        const [idcTag, blockTag, roomTag, gridTag] = item.influenceGuid.split('.');
        const mapKey = `${idcTag}.${blockTag}.${roomTag}.GRID`;
        const gridTags = influenceMap?.[mapKey]?.tags?.length
          ? [...influenceMap[mapKey].tags, gridTag]
          : [gridTag];
        const gridMap = {};
        gridTags.forEach(g => {
          const [column] = g.split('');
          gridMap[column] = gridMap[column] ? [...gridMap[column], g] : [g];
        });
        influenceMap[mapKey] = {
          type: 'GRID',
          text: `${roomTag}  ${Object.keys(gridMap)
            .map(
              (item, index) =>
                `${item}列 ${gridMap[item].join('、')}${Object.keys(gridMap).length !== index + 1 ? '、' : ''}`
            )
            .join('')}`,
          tags: gridTags,
        };
      }
    });
    setInfluences(influenceMap);
    setGridInfluence(data?.influenceGrid ?? []);
  };

  return (
    <Popover
      overlayInnerStyle={{ width: 520 }}
      content={
        <>
          <div>{gridInfluence}</div>
          {Object.keys(influences).length
            ? Object.keys(influences).map((item, index) => {
                return `${influences[item].text}${index + 1 !== Object.keys(influences).length ? '，' : ''}`;
              })
            : null}
        </>
      }
      title="业务影响"
      onOpenChange={visible => {
        if (visible) {
          getCustomInfluences();
        }
      }}
    >
      <Button type="link" compact>
        查看
      </Button>
    </Popover>
  );
}
