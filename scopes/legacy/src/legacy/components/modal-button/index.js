import React, { useEffect, useState } from 'react';

import omitBy from 'lodash/omitBy';
import pickBy from 'lodash/pickBy';

import { Button } from '@manyun/base-ui.ui.button';
import { Modal } from '@manyun/base-ui.ui.modal';

const BUTTON_PROPS_KEYS = [
  'style',
  'className',
  'disabled',
  'ghost',
  'href',
  'htmlType',
  'icon',
  'loading',
  'shape',
  'size',
  'target',
  'type',
  'block',
];

const isButtonProps = (_value, key) => {
  // custom data-* attributes
  if (key.startsWith('data-')) {
    return true;
  }

  return BUTTON_PROPS_KEYS.includes(key);
};

const noop = () => {};

/**
 * @typedef { import('antd-3/lib/button').ButtonProps } ButtonProps
 * @typedef { import('antd-3/lib/modal').ModalProps } ModalProps
 *
 * @typedef {object} CustomizedProps
 * @property {boolean} compact 设置按钮内间距  {true} "padding:0"
 * @property {boolean} autoHeight 设置按钮的高度 {true} 将按钮高度样式设置为 "height: auto"（type="link" 时可以使用此 API 取消按钮默认的高度）
 * @property {React.ReactNode} text 设置按钮文案
 * @property {React.ReactChild | (visible: boolean) => React.ReactChild} children 弹窗内容
 * @property {React.CSSProperties} modalStyle 弹窗自定义样式
 * @property { (evt: React.MouseEvent<HTMLElement>) => void } onButtonClick 监听按钮点击事件
 * @property { (visible: boolean) => void } onVisibleChanged 监听弹窗显示状态
 */

/**
 * 封装于 antd 的 Button 和 Modal。
 * @param {ButtonProps & ModalProps & CustomizedProps} props
 */
const RefForwardedModalButton = React.forwardRef(function ModalButton(
  {
    compact,
    autoHeight,
    text,
    children,
    // onButtonClick = noop,
    onVisibleChanged = noop,
    ...props
  },
  ref
) {
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    onVisibleChanged(visible);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible]);

  function toggleVisible() {
    setVisible(prevVisible => !prevVisible);
  }

  const { bodyStyle, modalStyle, onOk, onCancel, onButtonClick, ...modalProps } = omitBy(
    props,
    isButtonProps
  );

  let modalChildren = children;
  if (typeof children === 'function') {
    modalChildren = children(visible);
  }

  return (
    <>
      <Button
        ref={ref}
        {...pickBy(props, isButtonProps)}
        compact={compact}
        // autoHeight={autoHeight}
        onClick={async evt => {
          let ok = true;
          if (typeof onButtonClick === 'function') {
            ok = await onButtonClick(evt);
          }
          if (ok !== false) {
            toggleVisible();
          }
        }}
      >
        {text}
      </Button>
      <Modal
        style={modalStyle}
        title={text}
        bodyStyle={bodyStyle}
        {...modalProps}
        open={visible}
        onOk={async evt => {
          let ok = true;
          if (typeof onOk === 'function') {
            ok = await onOk(evt);
          }
          if (ok) {
            toggleVisible();
          }
        }}
        onCancel={async evt => {
          if (typeof onCancel === 'function') {
            await onCancel(evt);
          }
          toggleVisible();
        }}
      >
        {modalChildren}
      </Modal>
    </>
  );
});

export default RefForwardedModalButton;
