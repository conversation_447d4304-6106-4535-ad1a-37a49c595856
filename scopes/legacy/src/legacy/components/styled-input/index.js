import styled from 'styled-components';

import { Input } from '@manyun/base-ui.ui.input';

/**
 * @typedef { import('antd-3/lib/input').InputProps } InputProps
 *
 * @typedef {object} CustomizedProps
 * @property {number} [prefixWidth] 前缀的宽度
 * @property {number} [suffixWidth] 后缀的宽度
 */

/**
 * 封装于 `antd` 的 `Input` 组件。
 * @param {CustomizedProps & InputProps} props `StyleInput` 组件的 `props`
 */
const StyledInput = styled(Input)`
  &.manyun-input-affix-wrapper .manyun-input:not(:last-child) {
    padding-right: ${({ suffixWidth = 30 }) => `${suffixWidth}px`};
  }
  &.manyun-input-affix-wrapper .manyun-input:not(:first-child) {
    padding-left: ${({ prefixWidth = 30 }) => `${prefixWidth}px`};
  }
`;

export default StyledInput;
