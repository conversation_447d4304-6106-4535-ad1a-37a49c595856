import React from 'react';

import { Modal } from '@manyun/base-ui.ui.modal';

/**
 * 封装于 `@galiojs/awesome-antd` 的 `Modal` 组件。
 * @param {object} props `TinyModal` 组件的 `props`
 * @param {boolean} fitContentWidth  是否需要根据内容宽度自适应
 */

export function TinyModal({ fitContentWidth = false, ...props }) {
  if (fitContentWidth) {
    // const width = witchBrowser();
    props.width = '80%';
  }
  return <Modal {...props} />;
}

export default TinyModal;

/**
 * 判断用的哪一个浏览器改变Modal的width值为 '-moz-fit-content' 或 '-webkit-fit-content'。
 * fit-content暂不支持IE以及使用Trident内核的浏览器
 * 国内主流浏览器以及内核介绍 https://blog.csdn.net/firebird_one/article/details/81272539
 */
// function witchBrowser() {
//   const ua = navigator.userAgent.toLocaleLowerCase();
//   let browserType = '-webkit-fit-content';
//   if (ua.match(/firefox/) != null) {
//     // 火狐浏览器
//     browserType = '-moz-fit-content';
//   }
//   return browserType;
// }
