import React, { useEffect, useRef } from 'react';

import PlusOutlined from '@ant-design/icons/es/icons/PlusOutlined';
import shortid from 'shortid';

import { Button } from '@manyun/base-ui.ui.button';
import { Space } from '@manyun/base-ui.ui.space';

import { TinyCard } from '@manyun/dc-brain.legacy.components';
import usePrevious from '@manyun/dc-brain.legacy.utils/useHooks/usePrevious';

import ItemCard from './components/itemCard';

function throws(error) {
  throw new Error(error);
}

/**
 * 封装于 `antd` 的 `List` 组件。
 * @typedef {object} Props
 * @property {Array} props.data List 数据源
 * @property {React.ReactNode} props.header 显示的heaer,如果不显示则为 null
 * @property {function} props.setInspectionItem  增加巡检项时 更新List数据中巡检项的方法
 * @property {object} props.standardsRefMap  所有巡检项表单的ref
 * @property {function} props.setStandardsRefMap  增加巡检项时 更新巡检项表单的ref
 */
export function InspectionList({
  data,
  // header,
  setInspectionItem,
  setStandardsRefMap,
  standardsRefMap,
  inspectType,
}) {
  const dataLength = data.length;
  const prevDataLength = usePrevious(data.length);
  const formRefsMapRef = useRef();

  useEffect(() => {
    if (prevDataLength === dataLength) {
      return;
    }
    formRefsMapRef.current = new Map(data.map(({ id }) => [id, React.createRef()]));
    setStandardsRefMap(formRefsMapRef.current);
  }, [prevDataLength, dataLength, data, setStandardsRefMap]);

  const newInspection = async () => {
    try {
      for (const [, standardsConfigRef] of standardsRefMap) {
        if (standardsConfigRef.current) {
          await standardsConfigRef.current.form.validateFieldsAndScroll().catch(throws);
        }
      }
      const modules = {
        id: shortid.generate(),
        checkItemName: { value: null },
        standards: [{ standardId: shortid.generate(), standardTxt: { value: null } }],
        inspectType: inspectType,
      };
      setInspectionItem([modules, ...data]);
    } catch (error) {
      // eslint-disable-next-line
      console.log(error);
    }
  };

  const loadMore = () => {
    return (
      <Button compact type="link" onClick={() => newInspection()}>
        <PlusOutlined />
        添加检查项
      </Button>
    );
  };

  const deleteRowByIdx = deleteId => {
    const newData = data
      .filter(({ id }) => id !== deleteId)
      .map(item => {
        return {
          ...item,
          checkItemName: {
            ...item.checkItemName,
            dirty: true,
            errors: undefined,
          },
        };
      });
    setInspectionItem(newData);
  };

  return (
    <TinyCard title="巡检项列表">
      <Space direction="vertical" style={{ width: '100%' }}>
        {loadMore()}
        <div
          style={{ width: '100%' }}
          // header={header}
          // dataSource={data}
          // loadMore={loadMore()}
          // locale={{
          //   emptyText: (
          //     <div className="has-error">
          //       <span className="manyun-form-explain">至少添加一个检查项！</span>
          //     </div>
          //   ),
          // }}
          // renderItem={(inspectItemInfo, index) => {
          //   return (
          //     <List.Item
          //     // actions={[
          //     //   <Button
          //     //     type="link"
          //     //     // disabled={data.length === 1}
          //     //     key="delete"
          //     //     onClick={() => {
          //     //       deleteRowByIdx(inspectItemInfo.id);
          //     //     }}
          //     //   >
          //     //     删除
          //     //   </Button>,
          //     // ]}
          //     ></List.Item>
          //   );
          // }}
        >
          {data.map((inspectItemInfo, index) => (
            <div key={inspectItemInfo.id}>
              <ItemCard
                inspectItemInfo={inspectItemInfo}
                index={index + 1}
                data={data}
                setInspectionItem={setInspectionItem}
                xRef={standardsRefMap.get(inspectItemInfo.id)}
                listData={data}
                extra={
                  <Button
                    type="link"
                    // disabled={data.length === 1}
                    key="delete"
                    onClick={() => {
                      deleteRowByIdx(inspectItemInfo.id);
                    }}
                  >
                    删除
                  </Button>
                }
              />
            </div>
          ))}
        </div>
      </Space>
    </TinyCard>
  );
}

export default InspectionList;

// export const StyledList = styled(List)`
//   .manyun-list-item .manyun-card {
//     // width: 85%;
//   }
//   .manyun-list-item .manyun-list-item-action {
//     width: 15%;
//     margin-left: 0px;
//   }
//   &.manyun-list-split .manyun-list-header {
//     border-bottom: 0;
//   }
//   &.manyun-list-split .manyun-list-item {
//     border-bottom: 0;
//     background: var(--color-blue-12);
//     margin-bottom: 20px;
//   }
//   .manyun-list-item .manyun-card .manyun-card-head {
//     background: var(--color-blue-12);
//   }
//   &.manyun-list-something-after-last-item
//     .manyun-spin-container
//     > .manyun-list-items
//     > .manyun-list-item:last-child {
//     border-bottom: 0;
//   }
//   .manyun-form-item-children .manyun-btn {
//     padding-left: 10px;
//     padding-right: 0;
//   }
//   .manyun-input-affix-wrapper .manyun-input {
//     background: var(--color-blue-12);
//   }
//   .has-error .manyun-input-affix-wrapper .manyun-input,
//   .has-error .manyun-input-affix-wrapper .manyun-input:hover {
//     background: var(--color-blue-12);
//   }
// `;
