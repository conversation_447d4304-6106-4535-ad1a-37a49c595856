import React, { forwardRef, useImperativeHandle } from 'react';

import Form from '@ant-design/compatible/es/form';
import MinusCircleOutlined from '@ant-design/icons/es/icons/MinusCircleOutlined';
import PlusCircleOutlined from '@ant-design/icons/es/icons/PlusCircleOutlined';
import uniq from 'lodash/uniq';
import shortid from 'shortid';

import { Button } from '@manyun/base-ui.ui.button';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';

const StandardForm = forwardRef(
  ({ inspectItemInfo, form, setInspectionItem, data, listData }, ref) => {
    useImperativeHandle(ref, () => ({
      form,
    }));

    const { getFieldDecorator } = form;

    const addStandardByIdx = () => {
      const newData = data.map(item => {
        if (item.id === inspectItemInfo.id) {
          return {
            ...item,
            standards: [
              ...item.standards,
              { standardId: shortid.generate(), standardTxt: { value: null } },
            ],
          };
        }
        return item;
      });
      setInspectionItem(newData);
    };

    const deleteStandardByIdx = idx => {
      const newData = data.map(item => {
        if (item.id === inspectItemInfo.id) {
          const newStandards = item.standards
            .filter(mock => mock.standardId !== idx)
            .map(item => {
              return {
                ...item,
                standardTxt: {
                  ...item.standardTxt,
                  dirty: true,
                  errors: undefined,
                },
              };
            });

          return {
            ...item,
            standards: newStandards,
          };
        }
        return item;
      });
      setInspectionItem(newData);
    };
    if (!inspectItemInfo) {
      return null;
    }
    return (
      <Form colon={false} labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
        <Row>
          <Col xl={24}>
            <Form.Item label="检查项" style={{ marginBottom: '0' }}>
              {getFieldDecorator(inspectItemInfo.id, {
                rules: [
                  {
                    required: true,
                    whitespace: true,
                    validator: (_, value, callback) => {
                      // 查找当前弹窗中检查项相同的数据
                      const sameInspectItems = listData
                        ? listData.filter(({ checkItemName, id }) => {
                            if (
                              checkItemName.value !== null &&
                              value !== null &&
                              checkItemName.value.trim() === value.trim() &&
                              id !== inspectItemInfo.id
                            ) {
                              return true;
                            }
                            return false;
                          })
                        : [];
                      if (value === null || value.trim() === '') {
                        callback('检查项名称为必填项！');
                      } else if (sameInspectItems.length) {
                        callback('检查项名称不可重复！');
                      } else {
                        callback();
                      }
                    },
                  },
                ],
                // rules 添加的校验规则只在控制台输出 没有真正完成校验功能 暂未找到原因 http://chandao.manyun-local.com/zentao/bug-view-10623.html
              })(<Input style={{ width: '84%' }} maxLength={50} />)}
            </Form.Item>
          </Col>
          {inspectItemInfo.standards.map(({ standardId }, index) => {
            return (
              <Col key={standardId} xl={24}>
                <Form.Item label="检查标准" style={{ marginBottom: '0' }}>
                  {getFieldDecorator(standardId, {
                    rules: [
                      {
                        required: true,
                        whitespace: true,
                        validator: (_, value, callback) => {
                          // 查找同一个检查项下相同检查标准的数据
                          const filedsValue = form.getFieldsValue();
                          const standsValue = inspectItemInfo.standards
                            .map(({ standardId }) => standardId)
                            .map(id => filedsValue[id]);
                          const tmp = uniq(standsValue);
                          if (value === null || value.trim() === '') {
                            callback('检查标准为必填项！');
                          } else if (standsValue.length !== tmp.length) {
                            callback('同一检查项下检查标准不可重复！');
                          } else {
                            callback();
                          }
                        },
                      },
                    ],
                  })(<Input style={{ width: '84%' }} maxLength={50} />)}
                  {inspectItemInfo.standards.length === index + 1 && (
                    <Button
                      type="link"
                      style={{ marginLeft: 0, paddingLeft: '8px', paddingRight: 0 }}
                      onClick={() => addStandardByIdx()}
                    >
                      <PlusCircleOutlined />
                    </Button>
                  )}
                  {inspectItemInfo.standards.length !== 1 && (
                    <Button
                      type="link"
                      style={{ marginLeft: 0, paddingLeft: '8px', paddingRight: 0 }}
                      onClick={() => deleteStandardByIdx(standardId)}
                    >
                      <MinusCircleOutlined />
                    </Button>
                  )}
                </Form.Item>
              </Col>
            );
          })}
        </Row>
      </Form>
    );
  }
);
StandardForm.displayName = 'StandardForm';
const formCreateOpts = {
  onFieldsChange(props, fieldValues) {
    const newData = props.data.map(item => {
      if (item.id === props.inspectItemInfo.id) {
        if (fieldValues[props.inspectItemInfo.id]) {
          const newStands = item.standards.map(({ standardId, standardTxt, ...resp }) => {
            if (fieldValues[standardId]) {
              return {
                ...resp,
                standardId,
                standardTxt: {
                  ...fieldValues[standardId],
                  value: fieldValues[standardId].value,
                },
              };
            } else {
              if (!standardTxt) {
                return {
                  value: standardTxt,
                  name: standardId,
                  errors: [{ message: '检查标准为必填项！', field: standardId }],
                };
              }
              return {
                ...resp,
                standardId,
                standardTxt,
              };
            }
          });
          return {
            ...item,
            checkItemName: {
              ...fieldValues[props.inspectItemInfo.id],
              name: 'checkItemName',
              value: fieldValues[props.inspectItemInfo.id].value,
            },
            standards: newStands,
          };
        }
        const newStands = item.standards.map(({ standardId, standardTxt, ...resp }) => {
          if (fieldValues[standardId]) {
            return {
              ...resp,
              standardId,
              standardTxt: {
                ...fieldValues[standardId],
                value: fieldValues[standardId].value,
              },
            };
          } else {
            if (!standardTxt) {
              return {
                value: standardTxt,
                name: standardId,
                errors: [{ message: '检查标准为必填项！', field: standardId }],
              };
            }
            return {
              ...resp,
              standardId,
              standardTxt,
            };
          }
        });
        const newStandsUpdateErrors = upDateStandErrors(newStands);
        return { ...item, standards: newStandsUpdateErrors };
      }
      return item;
    });
    const upDateCheckItemErrorsData = upDateCheckItemErrors(newData);
    props.setInspectionItem(upDateCheckItemErrorsData);
  },
  mapPropsToFields(props) {
    const formFields = props.data.filter(item => item.id === props.inspectItemInfo.id);
    if (!formFields[0] || !formFields[0].standards || !formFields[0].standards.length) {
      return;
    }
    let fields = {
      [formFields[0].id]: Form.createFormField(formFields[0].checkItemName),
    };
    formFields[0].standards.forEach(({ standardId, standardTxt }) => {
      fields = {
        ...fields,
        [standardId]: Form.createFormField(standardTxt),
      };
    });
    return fields;
  },
};

const EhancedStandardForm = Form.create(formCreateOpts)(StandardForm);

export default EhancedStandardForm;

function upDateStandErrors(standards) {
  return standards.map(item => {
    // 检查标准是否有重复
    if (item.standardTxt.value && item.standardTxt.errors) {
      const sameStands = standards.find(
        el => el.standardTxt.value === item.standardTxt.value && el.standardId !== item.standardId
      );
      if (!sameStands) {
        return {
          ...item,
          standardTxt: {
            ...item.standardTxt,
            errors: undefined,
          },
        };
      }
      return item;
    }
    return item;
  });
}

function upDateCheckItemErrors(data) {
  return data.map(item => {
    if (item.checkItemName.value && item.checkItemName.errors) {
      const sameCheckItemName = data.find(
        el => el.checkItemName.value === item.checkItemName.value && el.id !== item.id
      );
      if (!sameCheckItemName) {
        return {
          ...item,
          checkItemName: {
            ...item.checkItemName,
            errors: undefined,
          },
        };
      }
      return item;
    }
    return item;
  });
}
