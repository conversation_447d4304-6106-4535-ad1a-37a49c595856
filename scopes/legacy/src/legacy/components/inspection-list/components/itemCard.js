import React from 'react';

import { TinyCard } from '@manyun/dc-brain.legacy.components';

import EhancedStandardForm from './form';

export function ItemCard({
  inspectItemInfo,
  index,
  setInspectionItem,
  data,
  xRef,
  listData,
  extra,
}) {
  return (
    <TinyCard
      title={`检查项${index}`}
      style={{ width: '100%', marginBottom: '10px' }}
      extra={extra}
    >
      <EhancedStandardForm
        wrappedComponentRef={xRef}
        inspectItemInfo={inspectItemInfo}
        data={data}
        setInspectionItem={setInspectionItem}
        listData={listData}
      />
    </TinyCard>
  );
}
export default ItemCard;
