import React from 'react';

import LoadingOutlined from '@ant-design/icons/es/icons/LoadingOutlined';

import { Spin } from '@manyun/base-ui.ui.spin';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';

/**
 * 用于页面和区块的加载中状态。
 * @param {object} props
 * @param {React.CSSProperties} [props.containerStyle] 容器样式
 * @param {React.CSSProperties} [props.iconStyle] 图标样式
 * @param {React.ReactNode} [props.description] 描述文案
 */
export default function Loading({
  containerStyle = { width: '100%', height: '100%' },
  iconStyle = { fontSize: 24 },
  description,
}) {
  const antIcon = <LoadingOutlined style={iconStyle} spin />;

  return (
    <GutterWrapper style={containerStyle} flex center>
      <Spin indicator={antIcon} />
      {typeof description == 'string' ? <span>{description}</span> : description}
    </GutterWrapper>
  );
}
