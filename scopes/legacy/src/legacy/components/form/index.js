import React from 'react';

import Form from '@ant-design/compatible/es/form';

export function TinyForm({
  form: { getFieldDecorator },
  size,
  items = [],
  children,
  ...restProps
}) {
  return (
    <Form {...restProps}>
      {items.map(
        ({
          decorate,
          decorateOptions,
          id,
          control,
          label,
          validateStatus,
          help,
          extra = '',
          colon = true,
        }) => {
          if (!decorate) {
            return React.cloneElement(control, { key: id, size });
          }
          return (
            <Form.Item
              key={id}
              label={label}
              validateStatus={validateStatus}
              help={help}
              extra={extra}
              colon={colon}
            >
              {getFieldDecorator(id, decorateOptions)(React.cloneElement(control, { size }))}
            </Form.Item>
          );
        }
      )}
      {children}
    </Form>
  );
}

TinyForm.Item = Form.Item;
TinyForm.create = Form.create;
TinyForm.createFormField = Form.createFormField;

export default TinyForm;
