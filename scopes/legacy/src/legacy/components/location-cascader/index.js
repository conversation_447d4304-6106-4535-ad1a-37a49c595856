import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';

import { Cascader } from '@manyun/base-ui.ui.cascader';

import { useSpaces } from '@manyun/resource-hub.ui.location-tree-select';

import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';

/**
 * @typedef {import('antd-3/lib/cascader').CascaderProps} CascaderProps
 *
 * @deprecated import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';
 * @typedef Props
 * @property {boolean} [idcsOnly] 是否只能选择到机房
 * @property {boolean} [showRoom] 是否需要选择到包间
 * @property {boolean} [currentAuthorize] Deprecated: 请使用：`authorizedOnly`
 * @property {boolean} [authorizedOnly] 是否仅可选择当前用户已关联的机房楼资源
 */

/**
 * @type {React.ForwardRefExoticComponent<Props & CascaderProps & React.RefAttributes<any>>}
 */
export const LocationCascader = React.forwardRef(
  (
    {
      allowClear = true,
      changeOnSelect = true,
      showRoom = false,
      idcsOnly = false,
      includeVirtualBlocks = false,
      disabledTypes = [],
      idc,
      /** @deprecated */
      currentAuthorize = false,
      authorizedOnly = currentAuthorize,
      onReserveData,
      ...props
    },
    ref
  ) => {
    let nodeTypes = ['IDC', 'BLOCK'];
    if (idcsOnly) {
      nodeTypes = ['IDC'];
    } else if (showRoom) {
      nodeTypes.push('ROOM');
    }
    const [{ treeSpaces }] = useSpaces({
      nodeTypes,
      idc,
      authorizedOnly,
      disabledTypes,
      includeVirtualBlocks,
      nodeMutator: node => {
        const names = (node.label ?? node.value).split('.');
        const values = node.value.split('.');

        return {
          ...node,
          label: names[names.length - 1],
          value: values[values.length - 1],
        };
      },
    });

    let idcTag;
    let blockTag;
    //当在某些特殊场景下，如事件新建时，如果用户关联的资源只有唯一的机房楼可选，则默认选取
    if (
      typeof onReserveData === 'function' &&
      treeSpaces.length === 1 &&
      treeSpaces[0].children.length === 1
    ) {
      idcTag = treeSpaces[0].value;
      blockTag = treeSpaces[0].children[0].value;
    }
    const dispatch = useDispatch();
    useEffect(() => {
      if (!authorizedOnly) {
        dispatch(syncCommonDataActionCreator({ strategy: { space: 'IF_NULL' } }));
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
      if (typeof onReserveData === 'function' && idcTag && blockTag) {
        onReserveData([idcTag, blockTag]);
      }
    }, [idcTag, blockTag, onReserveData]);

    return (
      <Cascader
        ref={ref}
        allowClear={allowClear}
        changeOnSelect={changeOnSelect}
        options={treeSpaces}
        {...props}
      />
    );
  }
);

LocationCascader.displayName = 'LocationCascader';

export default LocationCascader;
