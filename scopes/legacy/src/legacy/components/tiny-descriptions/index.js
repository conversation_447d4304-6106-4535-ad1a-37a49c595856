import React from 'react';

import { Descriptions } from '@manyun/base-ui.ui.descriptions';

/**
 * 封装于 `antd` 的 `Descriptions` 组件。
 * @deprecated
 * @param {object} props `TinyDescriptions` 组件的 `props`
 * @param {Array} descriptionsItems  描述的内容
 * @param {Number} descriptionsItemsWidth  描述的内容
 * @param {Number} column  每一行有几列
 */
export function TinyDescriptions({
  column = 3,
  descriptionsItemsWidth,
  descriptionsItems = [],
  ...props
}) {
  return (
    <Descriptions column={column} {...props}>
      {descriptionsItems.map(({ label, value, span }) => (
        <Descriptions.Item key={label} span={span} label={label}>
          {value}
        </Descriptions.Item>
      ))}
    </Descriptions>
  );
}

export default TinyDescriptions;
