/* eslint-disable no-console */
import React from 'react';
import { connect } from 'react-redux';

import cloneDeep from 'lodash/cloneDeep';
import uniqBy from 'lodash/uniqBy';

import { ApiTree } from '@manyun/dc-brain.legacy.components';
import { commonActions } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  fetchDeviceCategory,
  processingReturnData,
} from '@manyun/dc-brain.legacy.services/treeDataService';

const fieldNames = {
  key: 'metaCode',
  title: 'metaName',
  parentKey: 'parentCode',
};

export function Demo({ data, updateData }) {
  return (
    <ApiTree
      showSearch
      fieldNames={fieldNames}
      dataService={() => {
        if (data) {
          return Promise.resolve(data);
        }
        return fetchDeviceCategory();
      }}
      onSearch={(value, parallelList, callback) => {
        const { response } = callSomeService(value);
        if (response) {
          let searchData = response.data;
          let treeData = cloneDeep(data.treeList);
          treeData.map(node => {
            if (searchData.length) {
              searchData.map(item => {
                if (node.metaCode === item.parentCode) {
                  node.children = uniqBy([...(node.children || []), item], 'metaCode');
                }
                return node;
              });
            }
            return node;
          });
          const newData = processingReturnData(treeData);
          // 更新 store 里的 data
          updateData(newData);
        }
      }}
      childNodesDataService={node =>
        new Promise(resolve => {
          console.log('childNodesDataService...', node);
          resolve([]);
        })
      }
    />
  );
}

const mapStateToProps = ({ 'resource.spaces': { entities, codes } }) => ({
  data: (codes || []).map(code => entities[code]),
});
const mapDispatchToProps = {
  updateData: commonActions.space,
};

export default connect(mapStateToProps, mapDispatchToProps)(Demo);

function callSomeService() {
  return {
    response: {
      data: [
        {
          metaCode: 'NE09',
          metaName: '测试',
          parentCode: 'NW',
        },
      ],
    },
  };
}
