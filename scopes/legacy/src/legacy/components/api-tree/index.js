import DownOutlined from '@ant-design/icons/es/icons/DownOutlined';
import debounce from 'lodash/debounce';
import flattenDeep from 'lodash/flattenDeep';
import get from 'lodash/get';
import uniq from 'lodash/uniq';
import React from 'react';
import shallowequal from 'shallowequal';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Input } from '@manyun/base-ui.ui.input';
import { Tree } from '@manyun/base-ui.ui.tree';

import GutterWrapper from '@manyun/dc-brain.legacy.components/gutter-wrapper';
import { DataService } from '@manyun/dc-brain.legacy.components/renderProps';
import { flattenTreeData } from '@manyun/dc-brain.legacy.utils';

const noop = () => {};

/**
 * @typedef { import('antd-3/lib/tree').TreeProps } TreeProps
 *
 * @typedef { (node: any) => string } Callback1
 * @typedef { (node: any) => React.ReactNode } Callback2
 * @typedef { (node: any) => boolean } Callback3
 *
 * @typedef FieldNames
 * @property { string | Callback1 } key
 * @property { string | Callback2 } title
 * @property { string | Callback1 } [parentKey]
 * @property {Callback3} [disableCheckbox] 用于判断是否禁用节点的复选框
 *
 * @typedef CustomizedProps
 * @property {React.RefObject<HTMLDivElement>} [wrapperRef]
 * @property {React.CSSProperties} [treeStyle]
 * @property {boolean} [showSearch=false]
 * @property {boolean} [isMatchSearch=false] 是否启用搜索匹配过滤， 如果需要开启精准匹配
 * @property {FieldNames} fieldNames
 * @property {string} [treeNodeFilterProp='title']
 * @property {(node: any) => boolean} filterLeafTreeNode
 * @property {string[]} disabledTreeNodeKeys
 * @property {string[]} hiddenTreeNodeKeys
 * @property {() => Promise<{ treeList: any[]; parallelList?: any }>} dataService
 * @property {(treeNode: any) => Promise<any[]>} childNodesDataService
 * @property {(value: string, parallelList?: any[], highlightCallback: (parallelList: any[], value: string) => void) => void} [onSearch]
 * @property {(expandedKeys: string[]) => void} [onExpandedKeysUpdate]
 * @property {(childNodeKeys: string[]) => void} [onChildNodesUpdated]
 *
 * @typedef { TreeProps & CustomizedProps } Props
 */

/**
 * @augments {React.Component<Props>}
 */
export class ApiTree extends React.Component {
  /**
   * @type {Partial<Props>}
   */
  static defaultProps = {
    showSearch: false,
    isMatchSearch: false,
    fieldNames: {
      key: 'key',
      title: 'title',
      parentKey: 'parentKey',
    },
    treeNodeFilterProp: 'title',
    filterLeafTreeNode: ({ isLeaf }) => !!isLeaf,
    disabledTreeNodeKeys: [],
    hiddenTreeNodeKeys: [],
    onExpandedKeysUpdate: noop,
    onSelect: noop,
    onChildNodesUpdated: noop,
  };

  state = {
    loadedKeys: [],
    expandedKeys: [],
    searchValue: '',
    autoExpandParent: true,
  };

  _dataServiceRef = React.createRef();

  _searchValue = '';

  /**
   * 正在等待展开的节点 key 集合
   */
  _pendingExpandedKeys = [];

  _treeData = [];
  // eslint-disable-next-line @typescript-eslint/lines-between-class-members
  _flatTreeData = [];

  /**
   * 拉平后的异步加载的节点数据
   */
  _flatChildNodes = [];

  _debouncedSearchValueChangeHandler = debounce(() => {
    this._expandAndHighlight();
  }, 666);

  _expandAndHighlight = () => {
    const value = this._searchValue;
    let expandedKeys = [];
    if (value) {
      const {
        fieldNames: { parentKey = ApiTree.defaultProps.fieldNames.parentKey },
        treeNodeFilterProp,
      } = this.props;
      expandedKeys = uniq(
        [...this._flatTreeData, ...this._flatChildNodes]
          .map(node => {
            const filterProp = get(node, treeNodeFilterProp);
            if (String(filterProp).toLowerCase().indexOf(String(value).toLowerCase()) > -1) {
              const _parentKey =
                typeof parentKey === 'function' ? parentKey(node) : get(node, parentKey);

              return _parentKey;
            }
            return null;
          })
          .filter(Boolean)
      );
    }
    this.setState(
      () => {
        const partialState = { searchValue: value, autoExpandParent: true };

        if ('expandedKeys' in this.props) {
          return partialState;
        }

        return {
          expandedKeys,
          ...partialState,
        };
      },
      () => {
        this.props.onExpandedKeysUpdate(expandedKeys);
      }
    );
  };

  _searchValueChangeHandler = ({ target: { value } }) => {
    const { onSearch } = this.props;
    if (typeof onSearch === 'function') {
      return;
    }
    if (this._searchValue === value) {
      return;
    }
    this._searchValue = value;
    this._debouncedSearchValueChangeHandler();
  };

  _searchHandler = value => {
    if (this._searchValue === value) {
      return;
    }
    this._searchValue = value;
    const { onSearch } = this.props;
    if (typeof onSearch === 'function') {
      onSearch(value, this._expandAndHighlight);
      return;
    }
    this._expandAndHighlight();
  };

  _onLoadHandler = loadedKeys => {
    this.setState({ loadedKeys });
  };

  _expandHandler = (expandedKeys, ...args) => {
    this.setState(
      ({ loadedKeys: prevLoadedKeys }) => {
        const partialState = {
          loadedKeys: prevLoadedKeys.filter(key => expandedKeys.includes(key)),
          autoExpandParent: false,
        };

        if ('expandedKeys' in this.props) {
          return partialState;
        }

        return {
          expandedKeys,
          ...partialState,
        };
      },
      () => {
        if (typeof this.props.onExpand == 'function') {
          this.props.onExpand(expandedKeys, ...args);
        }
      }
    );
  };

  _findTreeNodeData = (treeData, keys, depth = 0) => {
    let target = null;
    for (let index = 0; index < treeData.length; index++) {
      const treeNodeData = treeData[index];
      const _key = this._getKey(treeNodeData);
      if (_key === keys[depth]) {
        if (depth < keys.length - 1) {
          target = this._findTreeNodeData(treeNodeData.children, keys, depth + 1);
        } else {
          target = treeNodeData;
        }
        break;
      }
    }

    return target;
  };

  _loadDataHandler = node =>
    new Promise(resolve => {
      const sourceTreeNodeData = this._findTreeNodeData(this._treeData, [
        ...node.computedNodeProps.parentKeys,
        node.computedNodeProps.key,
      ]);
      // Note: 为什么这里不直接使用 `node.children.length` 进行判断？
      // 因为 `node` 可能是上一次渲染时 `Tree.TreeNode` 内部缓存的数据源，
      // 可能存在 `node.children.length > 0`，但是 `_sourceTreeNodeData.children.length === 0`
      // 的情况，这里还是应该以 `Tree` 最新的数据源为准
      if (Array.isArray(sourceTreeNodeData.children) && sourceTreeNodeData.children.length > 0) {
        resolve();
        return;
      }
      const { childNodesDataService, onChildNodesUpdated } = this.props;
      childNodesDataService(sourceTreeNodeData).then(childNodes => {
        if (Array.isArray(childNodes) && childNodes.length) {
          sourceTreeNodeData.children = childNodes;

          this._flatChildNodes = [...this._flatChildNodes, ...flattenTreeData(childNodes)];

          const childNodeKeys = this._getDeepFlatChildNodeKeys(childNodes);
          // 如果异步加载出来的节点的 key 在现有的 expandedKeys 中，
          // 需要再次展开，因为异步加载的原因导致上一次不会展开需要异步加载的节点
          const pendingExpandedKeys = (this.props.expandedKeys || this.state.expandedKeys).filter(
            expandedKey => childNodeKeys.includes(expandedKey)
          );
          this._pendingExpandedKeys.forEach(pendingExpandedKey => {
            if (childNodeKeys.includes(pendingExpandedKey)) {
              pendingExpandedKeys.push(pendingExpandedKey);
            }
          });
          if (pendingExpandedKeys.length) {
            this._pendingExpandedKeys = this._pendingExpandedKeys.filter(
              pendingExpandedKey => !pendingExpandedKeys.includes(pendingExpandedKey)
            );
            if ('expandedKeys' in this.props) {
              // eslint-disable-next-line no-unused-expressions
              this.props.onExpand?.([...this.state.expandedKeys, ...pendingExpandedKeys]);
              onChildNodesUpdated(childNodeKeys);
            } else {
              // eslint-disable-next-line react/no-direct-mutation-state
              this.state.expandedKeys = [...this.state.expandedKeys, ...pendingExpandedKeys];
              this.forceUpdate(() => {
                onChildNodesUpdated(childNodeKeys);
              });
            }
          }
        }
        resolve();
      });
    });

  _getFlatTreeNodes = () => {
    return [...this._flatTreeData, ...this._flatChildNodes];
  };

  _getKey = node => {
    const {
      fieldNames: { key = ApiTree.defaultProps.fieldNames.key },
    } = this.props;

    return typeof key === 'function' ? key(node) : get(node, key);
  };

  _getDeepFlatChildNodeKeys = childNodes =>
    flattenDeep(
      childNodes.map(childNode => {
        if (!(Array.isArray(childNode.children) && childNode.children.length)) {
          return this._getKey(childNode);
        }

        return this._getDeepFlatChildNodeKeys(childNode.children);
      })
    );

  _dataUpdatedHandler = ({ parallelList }) => {
    if (this._searchValue) {
      this._expandAndHighlight();
      return;
    }
    if (
      'expandedKeys' in this.props &&
      !shallowequal(this.state.expandedKeys, this.props.expandedKeys)
    ) {
      const expandedKeys = [];
      this.props.expandedKeys.forEach(expandedKey => {
        if (parallelList.some(node => this._getKey(node) === expandedKey)) {
          expandedKeys.push(expandedKey);
        } else {
          this._pendingExpandedKeys.push(expandedKey);
        }
      });
      this.setState({ expandedKeys, loadedKeys: [] });
      return;
    }
    if (
      'defaultExpandedKeys' in this.props &&
      Array.isArray(this.props.defaultExpandedKeys) &&
      !shallowequal(this.state.expandedKeys, this.props.defaultExpandedKeys)
    ) {
      const expandedKeys = [];
      this.props.defaultExpandedKeys.forEach(expandedKey => {
        if (parallelList.some(node => this._getKey(node) === expandedKey)) {
          expandedKeys.push(expandedKey);
        } else {
          this._pendingExpandedKeys.push(expandedKey);
        }
      });
      this.setState({ expandedKeys, loadedKeys: [] });
      return;
    }
    this.setState({ expandedKeys: [] });
  };

  _selectHandler = (selectedKeys, e) => {
    this.props.onSelect(selectedKeys, {
      ...e,
      flatTreeNodes: this._getFlatTreeNodes(),
    });
  };

  refreshData = () => {
    this.setState({ loadedKeys: [], expandedKeys: [] }, () => {
      this._dataServiceRef.current.getData(true);
    });
  };

  render() {
    const {
      wrapperRef,
      treeStyle,
      showSearch,
      isMatchSearch,
      fieldNames,
      treeNodeFilterProp,
      filterLeafTreeNode,
      disabledTreeNodeKeys,
      hiddenTreeNodeKeys,
      dataService,
      childNodesDataService,
      placeholder,
      ...rest
    } = this.props;
    const { loadedKeys, expandedKeys, searchValue, autoExpandParent } = this.state;
    const opts = {
      disabled: rest.disabled,
      searchValue,
      isMatchSearch,
      fieldNames,
      treeNodeFilterProp,
      filterLeafTreeNode,
      disabledTreeNodeKeys,
      hiddenTreeNodeKeys,
    };

    const extraProps = {};
    if (typeof childNodesDataService === 'function') {
      extraProps.loadData = this._loadDataHandler;
    }

    return (
      <DataService
        ref={this._dataServiceRef}
        requestOnDidMount
        dataService={dataService}
        onDataUpdated={this._dataUpdatedHandler}
      >
        {({ data: { treeList = [], parallelList = [] } = {} }) => {
          this._treeData = treeList;
          this._flatTreeData = parallelList;

          return (
            <GutterWrapper ref={wrapperRef} mode="vertical">
              {showSearch && (
                <Input.Search
                  placeholder={placeholder}
                  allowClear
                  onChange={this._searchValueChangeHandler}
                  onSearch={this._searchHandler}
                />
              )}
              <Tree
                style={treeStyle}
                showLine
                switcherIcon={<DownOutlined />}
                loadedKeys={loadedKeys}
                onLoad={this._onLoadHandler}
                expandedKeys={expandedKeys}
                {...rest}
                autoExpandParent={autoExpandParent}
                onExpand={this._expandHandler}
                onSelect={this._selectHandler}
                {...extraProps}
              >
                {loop(treeList, opts)}
              </Tree>
            </GutterWrapper>
          );
        }}
      </DataService>
    );
  }
}

export default ApiTree;

const loop = (data, opts = {}) => {
  const {
    disabled: treeDisabled,
    searchValue,
    isMatchSearch,
    fieldNames: {
      key = ApiTree.defaultProps.fieldNames.key,
      title = ApiTree.defaultProps.fieldNames.title,
      disableCheckbox = ApiTree.defaultProps.fieldNames.disableCheckbox,
    },
    treeNodeFilterProp,
    filterLeafTreeNode,
    disabledTreeNodeKeys,
    hiddenTreeNodeKeys,
    parentKeys = [],
  } = opts;

  // 添加递归检查子孙节点的函数
  const hasMatchedDescendant = node => {
    if (!node.children) {
      return false;
    }

    return node.children.some(child => {
      const childFilterProp = get(child, treeNodeFilterProp);
      const directMatch = String(childFilterProp)
        .toLowerCase()
        .includes(String(searchValue).toLowerCase());

      // 递归检查子孙节点
      return directMatch || hasMatchedDescendant(child);
    });
  };

  return data
    .map((node, index) => {
      const _key = typeof key === 'function' ? key(node) : get(node, key);
      const hidden = hiddenTreeNodeKeys.includes(_key);

      if (hidden) {
        return null;
      }

      // 根据 isMatchSearch 判断使用哪种搜索逻辑
      if (isMatchSearch && searchValue && searchValue.trim()) {
        const filterProp = get(node, treeNodeFilterProp);
        const matched = String(filterProp)
          .toLowerCase()
          .includes(String(searchValue).toLowerCase());

        // 使用递归函数检查所有子孙节点
        const hasMatchedChildren = hasMatchedDescendant(node);

        // 如果节点和所有子孙节点都不匹配,则不渲染
        if (!matched && !hasMatchedChildren) {
          return null;
        }

        // 如果当前节点匹配，则渲染它的所有子节点
        if (matched) {
          const isLeaf = filterLeafTreeNode(node);
          const disabled =
            treeDisabled === undefined ? disabledTreeNodeKeys.includes(_key) : treeDisabled;
          let checkboxDisabled = false;
          if (typeof disableCheckbox === 'function') {
            checkboxDisabled = disableCheckbox(node);
          }
          const computedProps = {
            key: _key,
            isLeaf,
            disabled,
            checkboxDisabled,
            parentKeys,
          };

          const _title = getTitle(
            title,
            { ...node, ...computedProps },
            searchValue,
            treeNodeFilterProp,
            index
          );

          return (
            <Tree.TreeNode
              key={_key}
              style={{ width: '100%' }}
              isLeaf={isLeaf}
              disabled={disabled}
              disableCheckbox={checkboxDisabled}
              title={_title}
              dataRef={node}
              computedNodeProps={computedProps}
            >
              {Array.isArray(node.children) &&
                node.children.length &&
                // 如果当前节点匹配，则渲染所有子节点，不进行搜索过滤
                loop(node.children, { ...opts, isMatchSearch: false })}
            </Tree.TreeNode>
          );
        }
      }

      // 常规渲染逻辑...
      const isLeaf = filterLeafTreeNode(node);
      const disabled =
        treeDisabled === undefined ? disabledTreeNodeKeys.includes(_key) : treeDisabled;
      let checkboxDisabled = false;
      if (typeof disableCheckbox === 'function') {
        checkboxDisabled = disableCheckbox(node);
      }
      const computedProps = {
        key: _key,
        isLeaf,
        disabled,
        checkboxDisabled,
        parentKeys,
      };

      const _title = getTitle(
        title,
        { ...node, ...computedProps },
        searchValue,
        treeNodeFilterProp,
        index
      );

      return (
        <Tree.TreeNode
          key={_key}
          style={{ width: '100%' }}
          isLeaf={isLeaf}
          disabled={disabled}
          disableCheckbox={checkboxDisabled}
          title={_title}
          dataRef={node}
          computedNodeProps={computedProps}
        >
          {Array.isArray(node.children) &&
            node.children.length &&
            loop(node.children, {
              ...opts,
              parentKeys: [...parentKeys, _key],
            })}
        </Tree.TreeNode>
      );
    })
    .filter(Boolean);
};

function getTitle(title, node, searchValue, treeNodeFilterProp, mapIdx) {
  let text;
  if (typeof title === 'string') {
    text = get(node, title);
  }
  const filterProp = get(node, treeNodeFilterProp);
  const { index, prefix, postfix } = splitSearchValue(searchValue, filterProp);
  const highlighted = index > -1;
  if (highlighted) {
    text = (
      <span>
        {prefix}
        {getHighlightedTxt(searchValue)}
        {postfix}
      </span>
    );
  }
  if (typeof title === 'function') {
    text = title(node, { highlightedText: text, index: mapIdx });
  }

  return text;
}

function getHighlightedTxt(searchValue) {
  return <span style={{ color: `var(--${prefixCls}-error-color)` }}>{searchValue}</span>;
}

function splitSearchValue(searchValue, filterProp) {
  let index = -1;
  let prefix;
  let postfix;
  if (searchValue && filterProp) {
    index = filterProp.indexOf(searchValue);
    prefix = filterProp.substr(0, index);
    postfix = filterProp.substr(index + searchValue.length);
  }

  return { index, prefix, postfix };
}
