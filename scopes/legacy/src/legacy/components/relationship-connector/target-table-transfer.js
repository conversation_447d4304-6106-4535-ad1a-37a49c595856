import React, { useEffect, useState } from 'react';

import { Col, Row } from '@manyun/base-ui.ui.grid';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';

import ObjectSelect from './object-select';
import TableTransfer from './table-transfer';

export function TargetTableTransfer({
  leftColumns,
  rightColumns,
  targetSelectTypes,
  targetSelectPlaceholder,
  targetSelectService,
  targetDatasourceService,
  onChange,
  checkedKeys,
}) {
  const [loading, setLoading] = useState(false);
  const [targetKeys, setTargetKeys] = useState([]);
  const [targetDatasource, setTargetDatasource] = useState([]);
  useEffect(() => {
    (async () => {
      setLoading(true);
      const data = await targetDatasourceService();
      setTargetDatasource(data);
      setLoading(false);
    })();
  }, [targetDatasourceService]);
  useEffect(() => {
    let list = targetDatasource;
    let targetList = [];
    list.forEach(item => {
      if (checkedKeys?.includes(parseInt(item.key))) {
        item.disabled = true;
        item.key = parseInt(item.key);
        targetList = [...targetList, parseInt(item.key)];
      }
    });
    setTargetDatasource(list);
    setTargetKeys(targetList);
  }, [targetDatasource, checkedKeys]);

  return (
    <GutterWrapper mode="vertical">
      <Row>
        <Col span={12}>
          <ObjectSelect
            types={targetSelectTypes}
            placeholder={targetSelectPlaceholder}
            onSearch={targetSelectService}
            onChange={({ key }, option) => {
              let selected = { key, ...option.props['data-option'] };
              selected.key = parseInt(selected.key);
              setTargetKeys(prevTargetKeys => [parseInt(key), ...prevTargetKeys]);
              const selecteds = targetDatasource.filter(item => targetKeys.includes(item.key));
              onChange([selected, ...selecteds]);
            }}
          />
        </Col>
      </Row>
      <TableTransfer
        loading={loading}
        leftColumns={leftColumns}
        rightColumns={rightColumns}
        dataSource={targetDatasource}
        titles={['候选项', '已选择']}
        targetKeys={targetKeys}
        onChange={newTargetKeys => {
          setTargetKeys(newTargetKeys);
          const selecteds = targetDatasource.filter(({ key }) => newTargetKeys.includes(key));
          onChange(selecteds);
        }}
      />
    </GutterWrapper>
  );
}

export default TargetTableTransfer;
