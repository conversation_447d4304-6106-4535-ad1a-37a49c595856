import React from 'react';

import DownOutlined from '@ant-design/icons/es/icons/DownOutlined';
import PropTypes from 'prop-types';

import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Tree } from '@manyun/base-ui.ui.tree';
import { Typography } from '@manyun/base-ui.ui.typography';

import { GutterWrapper, TinyCard } from '@manyun/dc-brain.legacy.components';

import { NODE_TYPE_MAP } from './constants';

const { TreeNode } = Tree;
const { Search } = Input;

// 拉平后的节点数据
const dataList = [];
// 节点全路径数据缓存
const nodePathCache = new Map();

class TargetTreeTransfer extends React.Component {
  static propTypes = {
    targetSelectPlaceholder: PropTypes.string,
    targetDatasourceService: PropTypes.func.isRequired,
    leafType: PropTypes.oneOf(Object.values(NODE_TYPE_MAP)),
    checkedKeys: PropTypes.arrayOf(PropTypes.string),
    onChange: PropTypes.func,
  };

  static defaultProps = {
    leafType: NODE_TYPE_MAP.BUILDING,
  };

  state = {
    loading: false,
    treeData: [],
    visibleKeys: [],
    checkedKeys: [],
    expandedKeys: [],
    searchValue: '',
    autoExpandParent: true,
  };

  componentDidMount() {
    this.setState({ expandedKeys: this.props.checkedKeys, checkedKeys: this.props.checkedKeys });
    this._fetchTreeData();
  }

  _initializeFieldValue = () => {
    const { value } = this.props;
    if (value === undefined) {
      return;
    }

    // if it reveives `value` prop, it could be wrapped by `getFieldDecorator()`.
    // we should handle this `initialValue`.
    const visibleKeys = getVisibleKeys(value, dataList);
    this.setState({ checkedKeys: value, visibleKeys });
  };

  _fetchTreeData = async () => {
    this.setState({ loading: true });
    const treeData = await this.props.targetDatasourceService();
    generateList(treeData);
    this._loop(treeData);
    this.setState(
      ({ checkedKeys }) => ({
        treeData,
        loading: false,
        visibleKeys: getVisibleKeys(checkedKeys, dataList),
      }),
      this._initializeFieldValue
    );
  };

  _getHandleCloseCheckedNode = nodeKey => () => {
    this.setState(
      ({ checkedKeys }) => {
        const newCheckedKeys = checkedKeys.filter(key => {
          const nodePath = nodePathCache.get(key);
          if (!nodePath) {
            return false;
          }
          if (nodePath.includes(nodeKey)) {
            return false;
          }
          return true;
        });
        const newVisibleKeys = getVisibleKeys(newCheckedKeys, dataList);

        return {
          visibleKeys: newVisibleKeys,
          checkedKeys: newCheckedKeys,
        };
      },
      () => {
        const { checkedKeys, visibleKeys } = this.state;
        this._emitChange(checkedKeys, visibleKeys);
      }
    );
  };

  _getNodeByKey = nodeKey => {
    const node = dataList.find(({ key }) => key === nodeKey);
    if (!node) {
      return {};
    }
    return node;
  };

  _getNodeTitleByKey = nodeKey => {
    const node = this._getNodeByKey(nodeKey);
    if (node.type === NODE_TYPE_MAP.BUILDING) {
      const nodePath = nodePathCache.get(nodeKey);
      const idcNodeKey = nodePath[nodePath.length - 2];
      const idcNode = this._getNodeByKey(idcNodeKey);
      return `${idcNode.title}.${node.title}`;
    }
    return node.title;
  };

  _getParentNode = checkedNode => {
    const nodePath = nodePathCache.get(checkedNode.key);
    let nodeKey;
    switch (checkedNode.type) {
      case NODE_TYPE_MAP.BUILDING:
        nodeKey = nodePath[2];
        break;
      case NODE_TYPE_MAP.IDC:
        nodeKey = nodePath[1];
        break;
      case NODE_TYPE_MAP.AREA:
        nodeKey = nodePath[0];
        break;
      default:
        nodeKey = null;
        break;
    }
    if (nodeKey === null) {
      return null;
    }
    return this._getNodeByKey(nodeKey);
  };

  _getCheckedNodes = checkedKeys => {
    return checkedKeys.map(checkedKey => {
      const checkedNode = this._getNodeByKey(checkedKey);
      const parentNode = this._getParentNode(checkedNode);

      return {
        ...checkedNode,
        parent: parentNode && {
          ...parentNode,
          parent: this._getParentNode(parentNode),
        },
      };
    });
  };

  _emitChange = (checkedKeys, visibleKeys) => {
    const { leafType, onChange } = this.props;
    if (typeof onChange != 'function') {
      return;
    }
    const checkedLeafKeys = checkedKeys.filter(checkedKey => {
      const { type } = this._getNodeByKey(checkedKey);
      return type === leafType;
    });
    onChange(checkedLeafKeys, visibleKeys, checkedKeys, this._getCheckedNodes(checkedKeys));
  };

  _handleExpand = expandedKeys => {
    this.setState({
      expandedKeys,
      autoExpandParent: false,
    });
  };

  _handleKeywordChange = e => {
    const { value } = e.target;
    const expandedKeys = dataList
      .map(item => {
        if (getHighlightIndex(item.title, value) > -1) {
          return getParentKey(item.key, this.state.treeData);
        }
        return null;
      })
      .filter((item, i, self) => item && self.indexOf(item) === i);
    this.setState({
      expandedKeys,
      searchValue: value,
      autoExpandParent: true,
    });
  };

  _handleCheck = checkedKeys => {
    const visibleKeys = getVisibleKeys(checkedKeys, dataList);
    this.setState({ checkedKeys, visibleKeys });
    this._emitChange(checkedKeys, visibleKeys);
  };

  _handleClearCheckedKeys = () => {
    this.setState({ checkedKeys: [], visibleKeys: [] });
    this._emitChange([], []);
  };

  _loop = (data, nodePath = []) => {
    const { searchValue } = this.state;

    return data.map(item => {
      const nextNodePath = [...nodePath, item.key];
      if (!nodePathCache.get(item.key)) {
        nodePathCache.set(item.key, nextNodePath);
      }
      const index = getHighlightIndex(item.title, searchValue);
      const beforeStr = item.title.substring(0, index);
      const highlightStr = item.title.substr(index, searchValue.length);
      const afterStr = item.title.substring(index + searchValue.length);
      const title =
        index > -1 ? (
          <span>
            {beforeStr}
            <Typography.Text type="danger">{highlightStr}</Typography.Text> {afterStr}
          </span>
        ) : (
          <span>{item.title}</span>
        );
      if (Array.isArray(item.children) && item.children.length) {
        return (
          <TreeNode key={item.key} title={title}>
            {this._loop(item.children, nextNodePath)}
          </TreeNode>
        );
      }
      return (
        <TreeNode key={item.key} isLeaf title={title} disabled={item.type === NODE_TYPE_MAP.IDC} />
      );
    });
  };

  render() {
    const { loading, treeData, visibleKeys, checkedKeys, expandedKeys, autoExpandParent } =
      this.state;
    const { targetSelectPlaceholder, cardBodyStyle } = this.props;

    return (
      <GutterWrapper flex>
        <TinyCard
          style={{ width: 'calc(50% - .5rem)' }}
          bodyStyle={cardBodyStyle}
          bordered
          loading={loading}
          title={
            <Search
              allowClear
              placeholder={targetSelectPlaceholder}
              onChange={this._handleKeywordChange}
            />
          }
        >
          <Tree
            showLine
            checkable
            selectable={false}
            switcherIcon={<DownOutlined />}
            checkedKeys={checkedKeys}
            expandedKeys={expandedKeys}
            autoExpandParent={autoExpandParent}
            onExpand={this._handleExpand}
            onCheck={this._handleCheck}
          >
            {this._loop(treeData)}
          </Tree>
        </TinyCard>
        <TinyCard
          style={{ width: 'calc(50% - .5rem)' }}
          bordered
          title={
            <div style={{ height: 32, lineHeight: '32px' /* 和 <Search /> 保持同等高度 */ }}>
              已选择（{visibleKeys.length}）
            </div>
          }
          extra={
            <Button type="link" onClick={this._handleClearCheckedKeys}>
              清除
            </Button>
          }
        >
          {visibleKeys.map(key => (
            <Tag closable key={key} onClose={this._getHandleCloseCheckedNode(key)}>
              {this._getNodeTitleByKey(key)}
            </Tag>
          ))}
        </TinyCard>
      </GutterWrapper>
    );
  }
}

export default TargetTreeTransfer;

/**
 * 把🌲的数据源拉平，后续方便通过节点 key 找节点数据
 * @param {Array} treeData 🌲数据源
 */
function generateList(treeData) {
  for (let i = 0; i < treeData.length; i++) {
    const node = treeData[i];
    const { key, title, type /* 节点类型，参考 NODE_TYPE */ } = node;
    dataList.push({ key, title, type });
    if (node.children) {
      generateList(node.children);
    }
  }
}

/**
 * 通过节点 key 找到它的父节点的 key
 * 这个方法可能可以废弃掉，使用 nodePathCache 实现
 * @param {string} nodeKey 节点 key
 * @param {Array} treeData 🌲数据源
 */
function getParentKey(nodeKey, treeData) {
  let parentKey;
  for (let i = 0; i < treeData.length; i++) {
    const node = treeData[i];
    if (node.children) {
      if (node.children.some(item => item.key === nodeKey)) {
        parentKey = node.key;
      } else if (getParentKey(nodeKey, node.children)) {
        parentKey = getParentKey(nodeKey, node.children);
      }
    }
  }
  return parentKey;
}

/**
 * 找到节点标题中需要开始高亮的 index
 * @param {string} title 节点标题
 * @param {string} keyword 模糊查询的关键字
 * @param {boolean} caseSensitive 是否大小写敏感。默认值：false
 */
function getHighlightIndex(title, keyword, caseSensitive = false) {
  if (!caseSensitive) {
    return String(title).toLowerCase().indexOf(String(keyword).toLowerCase());
  }
  return String(title).indexOf(String(keyword));
}

/**
 * 找出需要展示的节点 key 的集合。
 * 规则：若父节点和子节点都被选中，则不展示子节点。
 * @param {string[]} checkedKeys 被选中节点的 key 的集合
 * @param {Array} treeData 🌲数据源
 */
function getVisibleKeys(checkedKeys, treeData) {
  const rootNode = treeData[0];
  if (!rootNode) {
    throw Error('root node required.');
  }
  const rootNodeKey = rootNode.key;
  if (checkedKeys.includes(rootNodeKey)) {
    return [rootNodeKey];
  }
  const keys = [];
  let intermediateKeys = []; // 用于缓存中间路径的 node key
  checkedKeys.forEach(checkedKey => {
    if (intermediateKeys.includes(checkedKey)) {
      return;
    }
    const checkedNode = treeData.find(({ key }) => key === checkedKey);
    if (!checkedNode) {
      // console.log(`node(key: ${checkedKey}) not found, it will be ignored.`);
      return;
    }
    const nodePath = nodePathCache.get(checkedKey);
    if (!nodePath) {
      // console.log(`nodePath(key: ${checkedKey}) not found, it will be ignored.`);
      return;
    }
    for (let index = 0; index < nodePath.length; index++) {
      const nodeKey = nodePath[index];
      if (checkedKeys.includes(nodeKey)) {
        if (!keys.includes(nodeKey)) {
          keys.push(nodeKey);
        }
        intermediateKeys = [
          ...intermediateKeys,
          ...nodePath.slice(
            index + 1,
            nodePath.length - 1 /* 最后一项是 checkedKey 本身，可以在这步丢弃 */
          ),
        ];
        break;
      }
    }
  });
  return keys;
}
