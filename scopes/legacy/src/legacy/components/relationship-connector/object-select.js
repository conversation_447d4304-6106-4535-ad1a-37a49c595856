import React from 'react';

import { Select } from '@galiojs/awesome-antd';
import debounce from 'lodash/debounce';
import PropTypes from 'prop-types';

import { Spin } from '@manyun/base-ui.ui.spin';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';

const { Option } = Select;

export class ObjectSelect extends React.Component {
  static propTypes = {
    types: PropTypes.arrayOf(
      PropTypes.shape({ label: PropTypes.string.isRequired, value: PropTypes.string.isRequired })
    ),
    mode: PropTypes.oneOf(['multiple', 'tags']),
    defaultValue: PropTypes.array,
    placeholder: PropTypes.string.isRequired,
    onSearch: PropTypes.func.isRequired,
    onChange: PropTypes.func.isRequired,
  };

  static defaultProps = {
    types: [],
  };

  _lastFetchId = 0;

  state = {
    typeSelect: null,
    selectedType: this.props.types.length ? this.props.types[0].value : null,
    data: 'defaultValue' in this.props ? this.props.defaultValue : [],
    value: [],
    fetching: false,
  };

  componentDidMount() {
    const { types } = this.props;
    if (types.length) {
      this.setState({
        typeSelect: (
          <Select
            defaultValue={types[0].value}
            style={{ width: 100 }}
            onChange={this._handleTypeChange}
          >
            {types.map(({ label, value }) => (
              <Option key={value}>{label}</Option>
            ))}
          </Select>
        ),
      });
    }
  }

  fetchUser = debounce(async value => {
    if (value.trim() === '') {
      return;
    }
    const { selectedType } = this.state;
    const { onSearch } = this.props;
    this._lastFetchId += 1;
    const dataPromise = onSearch(
      value.trim(),
      selectedType,
      this._lastFetchId,
      fetchId => fetchId === this._lastFetchId
    );

    // show `loading` animation
    // when the request takes time
    // more than 250ms only.
    Promise.race([
      new Promise(resolve => {
        setTimeout(() => {
          resolve('shouldShowLoading');
        }, 250);
      }),
      dataPromise,
    ]).then(value => {
      if (value === 'shouldShowLoading') {
        this.setState({ data: [], fetching: true });
      }
    });

    const data = await dataPromise;
    if (!data) {
      return;
    }
    this.setState({ data, fetching: false });
  }, 800);

  handleChange = (value, option) => {
    this.setState({
      fetching: false,
    });
    this.props.onChange(value, option);
  };

  _handleTypeChange = selectedType => {
    this.setState({ selectedType });
  };

  render() {
    const { typeSelect, fetching, data } = this.state;
    const { mode, defaultValue, placeholder } = this.props;

    const defaultSelect = (
      <Select
        mode={mode}
        showSearch
        labelInValue
        showArrow={false}
        defaultValue={defaultValue}
        placeholder={placeholder}
        notFoundContent={fetching ? <Spin size="small" /> : undefined}
        filterOption={false}
        onSearch={this.fetchUser}
        onChange={this.handleChange}
        style={{ width: !typeSelect ? '100%' : 'calc(100% - 1rem - 100px)' }}
      >
        {data.map(({ key, label, value, ...rest }) => (
          <Option key={key || value} data-option={rest}>
            {label}
          </Option>
        ))}
      </Select>
    );

    if (!typeSelect) {
      return defaultSelect;
    }

    return (
      <GutterWrapper>
        {typeSelect}
        {defaultSelect}
      </GutterWrapper>
    );
  }
}

export default ObjectSelect;
