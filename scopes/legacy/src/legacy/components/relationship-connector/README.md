## RelationshipConnector

关系连接组件。可用于关联用户、角色、用户组、权限、资源间的关系。

> 假设要给用户关联用户组，以下称用户为被关联项，用户组为关联项。

### API

---

RelationshipConnector

---

#### visible: boolean

会透传给内部封装了 `<Drawer />` 的 `visible` ，用于控制抽屉的展示和隐藏。

#### title: string

会用 `<Typography.Title level={3} />` 包装后传给 `<Drawer />` 的 `title`，用于抽屉的标题。

#### alertType: [Alert#type](https://ant.design/components/alert-cn/#API)

会透传给内部封装了 `<Alert />` 的 `type`。

#### alertMessage: [Alert#message](https://ant.design/components/alert-cn/#API)

会透传给内部封装了 `<Alert />` 的 `message`。

#### sourceTitle: string

被关联项的标题。

#### sourceSelectPlaceholder: string

被关联项 `<Select />` 的 `placeholder`。

#### defaultSelectedSources: SelectOptionType[]

```typescript
interface BaseOptionProps {
  key: string;
  value?: string; // 如果有 `key` 的话，`value` 可以忽略
  label: string;
}

type SelectOptionType = BaseOptionProps & { [x: string]: any };
```

被关联项 `<Select />` 的 `defaultValue`。

#### sourceSelectService: SelectServiceType

```typescript
type SelectServiceType = (
  keyword: string, // 用户输入的关键字
  selectedType?: string, // 用户选择的类型，参考 `targetSelectTypes`
  fetchId?: number, // 本次请求的唯一标识
  isMatchedFetchOrder?: (fetchId: number) => boolean // 用于判断请求时序控制的回调函数
) => Promise<SelectOptionType[]>;

// 示例
const demoService = async (
  keyword,
  __, // 我们这个示例不需要用到 `selectedType`
  fetchId,
  isMatchedFetchOrder
) => {
  const data = await fetch(/* ... */); // 请求 API 拿数据
  if (!isMatchedFetchOrder(fetchId)) {
    // 本次结果非最后一次请求，需要忽略掉
    return;
  }
  return data;
};
```

组件内部将在每次模糊查询 _被关联项_ 时调用这个方法获取候选项数据源。

#### targetTitle: string

关联项的标题。

#### targetType: 'table' | 'tree'

关联项的选择形式。可选表格、树形式。

#### targetSelectTypes: SelectOptionType

关联项的类型选择器的选项数据源。

#### targetSelectPlaceholder: string

关联项 `<Select />` 的 `placeholder`。

#### targetSelectService: SelectServiceType

组件内部将在每次模糊查询 _关联项_ 时调用这个方法获取候选项数据源。

> 如果设置了 `targetSelectTypes`，那么可以在 `targetSelectService` 方法的第二参数拿到 `selectedType`。

```jsx
const targetSelectService = async (keyword, selectedType) => {
  console.log('selectedType: ', selectedType);
};
```

#### targetDatasourceService: TableDataService | TreeDataService

```typescript
type TableDataService = () => Promise<any[]>;
// 注意：数据源中每项都需要包含一个 `key` 字段

type TreeDataService = ? // TODO
```

#### targetTableLeftColumns: [Table Column](https://ant.design/components/table-cn/#Column)

关联项左侧候选项区域的表头配置。

#### targetTableRightColumns: [Table Column](https://ant.design/components/table-cn/#Column)

关联项右侧已选择区域的表头配置。

#### submitService: SubmitService

```typescript
type SubmitService = (selectedSources: any[], selectedTargets: any[]) => Promise<void>;
```

点击右下方 `提交` 按钮时触发的回调函数，用于将已选择的被关联项和关联项数据吐出供上层调用。

#### onClose: () => void

点击右上角 `X` 按钮时触发的回调函数，会透传给 `<Drawer />`。

#### onCancel: () => void

点击右下方 `取消` 按钮时触发的回调函数。

---

RelationshipConnectorButton

---

将 `<Button />` 控制 `<Drawer />` 打开/关闭的处理逻辑封装在内部，方便上层开发者调用。

#### size: [Button size](https://ant.design/components/button-cn/#API)

设置按钮大小。

#### type: [Button size](https://ant.design/components/button-cn/#API)

设置按钮类型。

#### shape: [Button size](https://ant.design/components/button-cn/#API)

设置按钮形状。

### 示例

#### 快捷使用，无需关心 `<Drawer />` 的 `visible`

<img src="demo_with-button.jpg" />

```jsx
import React from 'react';
import { Table } from '@galiojs/awesome-antd';

import { GutterWrapper, RelationshipConnectorButton } from '@manyun/dc-brain.legacy.components';

const BUTTON_TEXT = '关联用户组';
const opts = {
  text: BUTTON_TEXT,
  title: BUTTON_TEXT,
  sourceTitle: '用户',
  targetTitle: '用户组',
  targetDatasourceService: () => Promise.resolve([]),
  // ... 省略了 `RelationshipConnector` 的剩余配置
};

const mockColumns = [
  {
    title: '用户',
    dataIndex: ['user', 'name'],
  },
  {
    title: '操作',
    dataIndex: '__actions',
    render: (__, { user }) => (
      <RelationshipConnectorButton
        {...opts}
        shape="link"
        defaultSelectedSources={[{ label: user.name, key: user.id, ...user }]}
      />
    ),
  },
];

const mockData = [
  {
    user: { id: 'jerry', name: '王彦苏' },
  },
  {
    user: { id: 'hao', name: '杨中豪' },
  },
];

function Demo() {
  return (
    <GutterWrapper mode="vertical">
      <GutterWrapper>
        <RelationshipConnectorButton {...opts} />
      </GutterWrapper>
      <Table columns={mockColumns} dataSource={mockData} />
    </GutterWrapper>
  );
}
```

#### Tree 形式

<img src="target-transfer_is_tree.jpg" />

```jsx
import { RelationshipConnectorButton } from '@manyun/dc-brain.legacy.components';
// 这里 mock 数据时要用到节点类型，如果数据是从 API 拿到的话，则不需要关心节点类型，因为 API 返回的数据中就需要包含节点类型
import { NODE_TYPE_MAP } from '@manyun/dc-brain.legacy.components/relationship-connector/constants';

const opts = {
  // ...

  // 需要指定使用 `tree` 模式
  targetType: 'tree',
};

const mockData = [
  {
    key: 'root',
    type: NODE_TYPE_MAP.ROOT,
    title: '全局机房',
    children: [
      {
        key: 'huadongqu',
        type: NODE_TYPE_MAP.AREA,
        title: '华东区',
        children: [
          {
            key: 'HZ01',
            type: NODE_TYPE_MAP.IDC,
            title: 'HZ01',
            children: [
              {
                key: 'HZ01-A',
                type: NODE_TYPE_MAP.BUILDING,
                title: 'A楼',
              },
              {
                key: 'HZ01-B',
                type: NODE_TYPE_MAP.BUILDING,
                title: 'B楼',
              },
              {
                key: 'HZ01-C',
                type: NODE_TYPE_MAP.BUILDING,
                title: 'C楼',
              },
            ],
          },
          {
            key: 'HZ02',
            type: NODE_TYPE_MAP.IDC,
            title: 'HZ02',
            children: [
              {
                key: 'HZ02-A',
                type: NODE_TYPE_MAP.BUILDING,
                title: 'A楼',
              },
              {
                key: 'HZ02-B',
                type: NODE_TYPE_MAP.BUILDING,
                title: 'B楼',
              },
              {
                key: 'HZ02-C',
                type: NODE_TYPE_MAP.BUILDING,
                title: 'C楼',
              },
            ],
          },
          {
            key: 'HZ03',
            type: NODE_TYPE_MAP.IDC,
            title: 'HZ03',
            children: [
              {
                key: 'HZ03-A',
                type: NODE_TYPE_MAP.BUILDING,
                title: 'A楼',
              },
              {
                key: 'HZ03-B',
                type: NODE_TYPE_MAP.BUILDING,
                title: 'B楼',
              },
              {
                key: 'HZ03-C',
                type: NODE_TYPE_MAP.BUILDING,
                title: 'C楼',
              },
            ],
          },
        ],
      },
      {
        key: 'huabeiqu',
        type: NODE_TYPE_MAP.AREA,
        title: '华北区',
        children: [
          {
            key: 'BJ03',
            type: NODE_TYPE_MAP.IDC,
            title: 'BJ03',
            children: [
              {
                key: 'BJ03-A',
                type: NODE_TYPE_MAP.BUILDING,
                title: 'A楼',
              },
            ],
          },
          {
            key: 'BJ04',
            type: NODE_TYPE_MAP.IDC,
            title: 'BJ04',
            children: [
              {
                key: 'BJ04-Z',
                type: NODE_TYPE_MAP.BUILDING,
                title: 'Z楼',
              },
            ],
          },
        ],
      },
    ],
  },
];

// 这里 mock 3s 后返回 mockData，实际项目中这部分逻辑应在 src/services 层实现
const targetDatasourceService = () =>
  new Promise(resolve => {
    setTimeout(resolve, 3000, mockData);
  });

function Demo() {
  return (
    <RelationshipConnectorButton {...opts} targetDatasourceService={targetDatasourceService} />
  );
}
```
