import React, { useCallback, useEffect, useState } from 'react';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Alert } from '@manyun/base-ui.ui.alert';
import { Typography } from '@manyun/base-ui.ui.typography';

import { GutterWrapper, TinyDrawer } from '@manyun/dc-brain.legacy.components';

import { TARGET_TYPE_MAP } from './constants';
import ObjectSelect from './object-select';
import TargetTableTransfer from './target-table-transfer';
import TargetTreeTransfer from './target-tree-transfer';

export function RelationshipConnector({
  title,
  visible,
  destroyOnClose = true,
  alertType = 'info',
  alertMessage,
  sourceTitle,
  sourceSelectPlaceholder,
  defaultSelectedSources = [],
  sourceSelectService,
  targetType = TARGET_TYPE_MAP.TABLE,
  targetTitle,
  targetSelectTypes,
  checkedKeys,
  targetSelectPlaceholder,
  targetSelectService,
  targetDatasourceService,
  targetTableLeftColumns,
  targetTableRightColumns,
  submitService,
  onClose,
  onCancel,
  callback,
}) {
  const [submitting, setSubmitting] = useState(false);
  const [selectedSources, setSelectedSources] = useState(defaultSelectedSources);
  const [selectedTargets, setSelectedTargets] = useState([]);

  useEffect(() => {
    if (defaultSelectedSources.length) {
      setSelectedSources(defaultSelectedSources);
    }
  }, [defaultSelectedSources]);

  const handleClose = useCallback(() => {
    typeof onClose === 'function' && onClose();
    setSelectedSources(defaultSelectedSources);
    setSelectedTargets([]);
  }, [defaultSelectedSources, onClose]);

  return (
    <TinyDrawer
      width={960}
      title={<Typography.Title level={4}>{title}</Typography.Title>}
      open={visible}
      destroyOnClose={destroyOnClose}
      submitButtonDisabled={!(selectedSources.length && selectedTargets.length)}
      submitButtonLoading={submitting}
      onClose={() => {
        handleClose();
      }}
      onSubmit={async () => {
        setSubmitting(true);
        const result = await submitService(selectedSources, selectedTargets);
        if (result) {
          handleClose();
          callback && callback();
        }
        setSubmitting(false);
      }}
      onCancel={onCancel}
    >
      <GutterWrapper mode="vertical">
        {alertMessage && <Alert showIcon type={alertType} message={alertMessage} />}
        {StyledTitle(sourceTitle)}
        <ObjectSelect
          mode="multiple"
          placeholder={sourceSelectPlaceholder}
          defaultValue={defaultSelectedSources}
          onSearch={sourceSelectService}
          onChange={selectedSource => {
            if (Array.isArray(selectedSource)) {
              setSelectedSources(selectedSource);
              return;
            }
            setSelectedSources([selectedSource]);
          }}
        />
        {StyledTitle(targetTitle)}
        {targetType === TARGET_TYPE_MAP.TABLE ? (
          <TargetTableTransfer
            checkedKeys={checkedKeys}
            leftColumns={targetTableLeftColumns}
            rightColumns={targetTableRightColumns}
            targetSelectTypes={targetSelectTypes}
            targetSelectPlaceholder={targetSelectPlaceholder}
            targetSelectService={targetSelectService}
            targetDatasourceService={targetDatasourceService}
            onChange={setSelectedTargets}
          />
        ) : (
          <TargetTreeTransfer
            checkedKeys={checkedKeys}
            targetSelectPlaceholder={targetSelectPlaceholder}
            targetDatasourceService={targetDatasourceService}
            onChange={setSelectedTargets}
          />
        )}
      </GutterWrapper>
    </TinyDrawer>
  );
}

export default RelationshipConnector;

export function StyledTitle(text) {
  return (
    <div>
      <span style={{ marginRight: 4, color: `var(--${prefixCls}-error-color)` }}>*</span>
      {text}
    </div>
  );
}
