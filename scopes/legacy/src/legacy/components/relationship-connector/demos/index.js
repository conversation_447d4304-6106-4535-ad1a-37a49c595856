/* eslint-disable no-console */
import React from 'react';

import { NODE_TYPE_MAP } from '@manyun/dc-brain.legacy.components/relationship-connector/constants';
import TreeTransfer from '@manyun/dc-brain.legacy.components/relationship-connector/target-tree-transfer';

const mockData = [
  {
    key: 'root',
    type: NODE_TYPE_MAP.ROOT,
    title: '全局机房',
    children: [
      {
        key: 'huadongqu',
        type: NODE_TYPE_MAP.AREA,
        title: '华东区',
        children: [
          {
            key: 'HZ01',
            type: NODE_TYPE_MAP.IDC,
            title: 'HZ01',
            children: [
              {
                key: 'HZ01-A',
                type: NODE_TYPE_MAP.BUILDING,
                title: 'A楼',
              },
              {
                key: 'HZ01-B',
                type: NODE_TYPE_MAP.BUILDING,
                title: 'B楼',
              },
              {
                key: 'HZ01-C',
                type: NODE_TYPE_MAP.BUILDING,
                title: 'C楼',
              },
            ],
          },
          {
            key: 'HZ02',
            type: NODE_TYPE_MAP.IDC,
            title: 'HZ02',
            children: [
              {
                key: 'HZ02-A',
                type: NODE_TYPE_MAP.BUILDING,
                title: 'A楼',
              },
              {
                key: 'HZ02-B',
                type: NODE_TYPE_MAP.BUILDING,
                title: 'B楼',
              },
              {
                key: 'HZ02-C',
                type: NODE_TYPE_MAP.BUILDING,
                title: 'C楼',
              },
            ],
          },
          {
            key: 'HZ03',
            type: NODE_TYPE_MAP.IDC,
            title: 'HZ03',
            children: [
              {
                key: 'HZ03-A',
                type: NODE_TYPE_MAP.BUILDING,
                title: 'A楼',
              },
              {
                key: 'HZ03-B',
                type: NODE_TYPE_MAP.BUILDING,
                title: 'B楼',
              },
              {
                key: 'HZ03-C',
                type: NODE_TYPE_MAP.BUILDING,
                title: 'C楼',
              },
            ],
          },
        ],
      },
      {
        key: 'huabeiqu',
        type: NODE_TYPE_MAP.AREA,
        title: '华北区',
        children: [
          {
            key: 'BJ03',
            type: NODE_TYPE_MAP.IDC,
            title: 'BJ03',
            children: [
              {
                key: 'BJ03-A',
                type: NODE_TYPE_MAP.BUILDING,
                title: 'A楼',
              },
            ],
          },
          {
            key: 'BJ04',
            type: NODE_TYPE_MAP.IDC,
            title: 'BJ04',
            children: [
              {
                key: 'BJ04-Z',
                type: NODE_TYPE_MAP.BUILDING,
                title: 'Z楼',
              },
            ],
          },
        ],
      },
    ],
  },
];

const targetDatasourceService = () =>
  new Promise(resolve => {
    setTimeout(resolve, 1000, mockData);
  });

const changeHandler = (checkedLeafKeys, visibleKeys, checkedKeys, checkedNodes) => {
  console.log(
    checkedLeafKeys.map(checkedLeafKey => checkedNodes.find(({ key }) => key === checkedLeafKey))
  );
  console.log(checkedKeys);
};

export default function Demo() {
  return (
    <TreeTransfer
      checkedKeys={['HZ01']}
      targetDatasourceService={targetDatasourceService}
      onChange={changeHandler}
    />
  );
}
