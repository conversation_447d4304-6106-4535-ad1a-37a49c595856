import React, {
  useState,
  /*, useEffect, useRef*/
} from 'react';

import { Button } from '@manyun/base-ui.ui.button';

import RelationshipConnector from './';

export function RelationshipConnectorButton({
  size,
  type,
  shape,
  text,
  onClose,
  onCancel,
  buttonStyle = {},
  ...props
}) {
  const [visible, setVisible] = useState(false);
  // const prevVisible = usePrevious(visible);
  // useEffect(() => {
  //   if (prevVisible === true && visible === false) {
  //     // closing drawer
  //     onClose();
  //   }
  // }, [prevVisible, visible, onClose]);

  return (
    <>
      <Button
        size={size}
        type={type}
        shape={shape}
        onClick={getOpenDrawer(setVisible)}
        style={buttonStyle}
      >
        {text}
      </Button>
      <RelationshipConnector
        visible={visible}
        onClose={getCloseDrawer(setVisible, onClose)}
        onCancel={getCloseDrawer(setVisible, onCancel)}
        {...props}
      />
    </>
  );
}

export default RelationshipConnectorButton;

// function usePrevious(value) {
//   const ref = useRef();
//   useEffect(() => {
//     ref.current = value;
//   });
//   return ref.current;
// }

function getOpenDrawer(setVisible) {
  return () => {
    setVisible(true);
  };
}

function getCloseDrawer(setVisible) {
  return () => {
    setVisible(false);
  };
}
