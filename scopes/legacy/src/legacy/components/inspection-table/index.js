import Form from '@ant-design/compatible/es/form';
import { PlusOutlined } from '@ant-design/icons';
import cloneDeep from 'lodash.clonedeep';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useMetaData } from '@manyun/resource-hub.hook.use-meta-data';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';
import { InspectionScenariosTextMap } from '@manyun/ticket.model.ticket';

import { TinyTable } from '@manyun/dc-brain.legacy.components';
import GutterWrapper from '@manyun/dc-brain.legacy.components/gutter-wrapper';
import {
  INSPECTION_CONFIG_TYPE_KEY_MAP,
  INSPECTION_TYPE_KEY_MAP,
  INSPECTION_TYPE_TEXT_MAP,
} from '@manyun/dc-brain.legacy.constants/inspection';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import { getRowSpan } from '@manyun/dc-brain.legacy.utils';

import ConfigItemModal from './components/config-item-modal';
import EditCustomConfig from './components/edit-custom-config';
import { convertArrToObj, getTableCheckStdTxt, getTableData } from './utils';

const filterOutPoint = (obj, replaceKey) => {
  if (!obj || typeof obj !== 'object') return obj;

  const newObj = {};

  Object.keys(obj).forEach(key => {
    if (key === 'POINT') return; // 过滤掉 `POINT`

    if (key === 'DEVICE') {
      newObj['DEVICE'] = {};
      Object.keys(obj['DEVICE']).forEach(innerKey => {
        newObj['DEVICE'][replaceKey] = filterOutPoint(obj['DEVICE'][innerKey], replaceKey);
      });
    } else if (key === 'ENVIRONMENT') {
      newObj['ENVIRONMENT'] = {};
      Object.keys(obj['ENVIRONMENT']).forEach(innerKey => {
        newObj['ENVIRONMENT'][replaceKey] = filterOutPoint(
          obj['ENVIRONMENT'][innerKey],
          replaceKey
        );
      });
    } else {
      newObj[key] = Array.isArray(obj[key])
        ? obj[key].map(item => filterOutPoint(item, replaceKey))
        : filterOutPoint(obj[key], replaceKey);
    }
  });

  return newObj;
};
/**
 * @typedef {object} Props
 * @property {Array} [vaue] table 数据源
 * @property {string} inspectScope  'patrol' | 'config' 为 时，显示 选择现有配置 或 自定义 的tab
 * @property {string} mode  当mode 为detail 时，不可以进行操作,只针对 inspectScope = config 状态下，inspectScope = patrol时，不传这个参数
 * @property {object} fieldValues  当inspectScope = config时，上层组件选择的巡检类型和巡检子类型，inspectScope = patrol时，不传这个参数
 * @property {function} validate   当inspectScope = config时，点击创建巡检项按钮时 检验上层组件中的form，inspectScope = patrol时，不传这个参数
 * @property {function} [onChange] ，返回值为对象
 * @property {string} taskType  当inspectScope = patrol时，taskType为工单类型，代表只能选这一种类型的配置，添加这一种类型的配置
 */
class InspectionTable extends Component {
  state = {
    tableData: {},
    createVisible: false,
    editVisible: false,
    editData: [],
    dataSource: [], // 完整的数据
    setedTable: false, // 用于编辑或详情，初次设置table数据
    rowSpansGroupByDataIndex: {},
    pageTableData: [],
    pageSize: 10,
    pageNum: 1,
  };

  validateFields = () => {
    const { form } = this.props;
    return new Promise((resolve, reject) => {
      form.validateFields((error, values) => {
        if (error) {
          reject(error);
        }
        resolve(values);
      });
    });
  };

  componentDidMount() {
    if (this.props.roomTypes === 'patrol' || this.props.mode === 'detail') {
      this.props.syncCommonData({ strategy: { deviceCategory: 'IF_NULL', roomTypes: 'IF_NULL' } });
    }
    if (this.props.value) {
      this.setState({
        tableData: this.props.value,
        dataSource: getTableData(this.props.value),
      });
    }
  }

  componentDidUpdate(prevProps, prevState) {
    const { fieldValues } = this.props;
    const { dataSource, pageSize, pageNum, tableData } = this.state;
    const prevDataKeysStr = prevState.dataSource.map(({ id }) => id).join('@');
    const stateDataKeysStr = dataSource.map(({ id }) => id).join('@');
    const prevDataStr = JSON.stringify(prevState.dataSource);
    const newDataStr = JSON.stringify(this.state.dataSource);
    if (prevDataKeysStr !== stateDataKeysStr || prevDataStr !== newDataStr) {
      const start = pageNum === 1 ? 0 : (pageNum - 1) * pageSize;
      const end = pageSize * pageNum;
      const pageTableData = dataSource.slice(start, end);
      const rowSpansGroupByDataIndex = pageTableData.reduce((map, record) => {
        const inspectSubject = record.inspectSubject;
        if (map[inspectSubject] === undefined) {
          map[inspectSubject] = 1;
        } else {
          map[inspectSubject] += 1;
        }

        const deviceType =
          record.inspectConfigType === 'POINT'
            ? `${inspectSubject}-${record.deviceType}`
            : `${inspectSubject}-${record.subTypeCode}`;
        if (map[deviceType] === undefined) {
          map[deviceType] = 1;
        } else {
          map[deviceType] += 1;
        }

        const pointCode =
          record.inspectConfigType === 'POINT'
            ? `${deviceType}-${record.pointCode}`
            : `${deviceType}-${record.configId}`;
        if (map[pointCode] === undefined) {
          map[pointCode] = 1;
        } else {
          map[pointCode] += 1;
        }

        return map;
      }, {});

      this.setState({
        rowSpansGroupByDataIndex,
        pageTableData,
      });
      if (this.props.onChange && typeof this.props.onChange == 'function') {
        this.props.onChange(this.state.tableData);
      }
    }
    if (
      (this.props.mode === 'detail' || this.props.mode === 'edit' || this.props.mode === 'copy') &&
      Object.keys(this.state.tableData).length === 0 &&
      this.props.value &&
      !this.state.setedTable
    ) {
      this.setState({
        tableData: this.props.value,
        dataSource: getTableData(this.props.value),
        setedTable: true,
      });
    }

    //story#2707 根据表单重置tableData  SOP类型inspectType 不清空， inspectSubject 和 subTypeCode 清空测点巡检项条目，非测点巡检项全部字段的内容保持不变
    if (
      fieldValues &&
      fieldValues.inspectSubject &&
      prevProps.fieldValues.inspectSubject.value &&
      fieldValues.inspectSubject.value !== prevProps.fieldValues.inspectSubject.value
    ) {
      this.setState({
        tableData: {},
        dataSource: [],
      });
    }
    if (
      // (fieldValues &&
      //   fieldValues.inspectType &&
      //   prevProps.fieldValues.inspectType.value &&
      //   fieldValues.inspectType.value !== prevProps.fieldValues.inspectType.value) ||
      (fieldValues &&
        fieldValues.subTypeCode &&
        prevProps.fieldValues.subTypeCode.value &&
        fieldValues.subTypeCode.value !== prevProps.fieldValues.subTypeCode.value) ||
      (fieldValues &&
        fieldValues.roomType &&
        prevProps.fieldValues.roomType.value &&
        fieldValues.roomType.value !== prevProps.fieldValues.roomType.value)
    ) {
      const code =
        fieldValues.inspectSubject.value === 'ENVIRONMENT'
          ? fieldValues.roomType.value
          : fieldValues.subTypeCode.value;
      this.setState({
        tableData: filterOutPoint(tableData, code),
        dataSource: dataSource
          .filter(i => !!!i.pointCode)
          .map(i => ({
            ...i,
            inspectSubject: fieldValues.inspectSubject.value,
            subTypeCode: code,
          })),
      });
    }

    if (prevProps.taskType !== this.props.taskType) {
      this.setState({
        tableData: {},
        dataSource: [],
      });
    }
  }

  deleteStands = record => {
    this.setState(({ dataSource }) => {
      const newDataSourse = dataSource.filter(({ configId }) => configId !== record.configId);
      const tableDataObj = convertArrToObj(newDataSourse);
      return {
        tableData: tableDataObj,
        dataSource: newDataSourse,
      };
    });
  };

  edit = record => {
    const { tableData } = this.state;
    const { inspectSubject, subTypeCode, inspectConfigType, configId } = record;
    const data = tableData[inspectSubject][subTypeCode][inspectConfigType].filter(
      ({ id }) => id === configId
    );
    this.setState({ editData: data, dataUrl: [inspectSubject, subTypeCode, inspectConfigType] });
    this.setEditVisible(true);
  };

  setEditVisible = value => {
    this.setState({
      editVisible: value,
    });
  };

  updateTableData = data => {
    const { dataUrl, tableData, pageNum, pageSize } = this.state;
    if (!dataUrl) {
      return;
    }
    const [inspectSubject, subTypeCode, inspectConfigType] = dataUrl;
    const configData = tableData[inspectSubject][subTypeCode][inspectConfigType].map(item => {
      if (item.id === data[0].id) {
        return data[0];
      }
      return item;
    });
    const newData = {
      ...tableData,
      [inspectSubject]: {
        ...tableData[inspectSubject],
        [subTypeCode]: {
          ...tableData[inspectSubject][subTypeCode],
          [inspectConfigType]: configData,
        },
      },
    };
    const newDataSource = getTableData(newData);
    const start = pageNum === 1 ? 0 : (pageNum - 1) * pageSize;
    const end = pageSize * pageNum;
    const pageTableData = newDataSource.slice(start, end);
    const rowSpansGroupByDataIndex = pageTableData.reduce((map, record) => {
      const inspectSubject = record.inspectSubject;
      if (map[inspectSubject] === undefined) {
        map[inspectSubject] = 1;
      } else {
        map[inspectSubject] += 1;
      }

      const deviceType =
        record.inspectConfigType === 'POINT'
          ? `${inspectSubject}-${record.deviceType}`
          : `${inspectSubject}-${record.subTypeCode}`;
      if (map[deviceType] === undefined) {
        map[deviceType] = 1;
      } else {
        map[deviceType] += 1;
      }

      const pointCode =
        record.inspectConfigType === 'POINT'
          ? `${deviceType}-${record.pointCode}`
          : `${deviceType}-${record.configId}`;
      if (map[pointCode] === undefined) {
        map[pointCode] = 1;
      } else {
        map[pointCode] += 1;
      }

      return map;
    }, {});
    this.setState({
      tableData: newData,
      dataSource: newDataSource,
      rowSpansGroupByDataIndex,
      pageTableData,
    });
    this.props.onChange(newData);
  };

  setIsMeterReadAndUnit = (record, value, key) => {
    const { tableData, pageNum, pageSize } = this.state;
    const { inspectSubject, subTypeCode, inspectConfigType, configId } = record;
    const configData = tableData[inspectSubject][subTypeCode][inspectConfigType].map(item => {
      if (inspectConfigType === INSPECTION_CONFIG_TYPE_KEY_MAP.CUSTOM && item.id === configId) {
        return {
          ...item,
          standards: item.standards.map(standardItem => {
            if (standardItem.standardId === record.standardId) {
              if (key === 'isMeterRead' && value === 0) {
                return {
                  ...standardItem,
                  [key]: value,
                  unit: '',
                  minValue: undefined,
                  maxValue: undefined,
                  normalTextList: undefined,
                  exTextList: undefined,
                };
              }
              return {
                ...standardItem,
                [key]: value,
              };
            }
            return standardItem;
          }),
        };
      }
      if (item.metaCode === configId) {
        return {
          ...item,
          [key]: value,
        };
      }
      return item;
    });
    const newTableData = {
      ...tableData,
      [inspectSubject]: {
        ...tableData[inspectSubject],
        [subTypeCode]: {
          ...tableData[inspectSubject][subTypeCode],
          [inspectConfigType]: configData,
        },
      },
    };
    const newDataSource = getTableData(newTableData);
    const start = pageNum === 1 ? 0 : (pageNum - 1) * pageSize;
    const end = pageSize * pageNum;
    const pageTableData = newDataSource.slice(start, end);
    const rowSpansGroupByDataIndex = pageTableData.reduce((map, record) => {
      const inspectSubject = record.inspectSubject;
      if (map[inspectSubject] === undefined) {
        map[inspectSubject] = 1;
      } else {
        map[inspectSubject] += 1;
      }

      const deviceType =
        record.inspectConfigType === 'POINT'
          ? `${inspectSubject}-${record.deviceType}`
          : `${inspectSubject}-${record.subTypeCode}`;
      if (map[deviceType] === undefined) {
        map[deviceType] = 1;
      } else {
        map[deviceType] += 1;
      }

      const pointCode =
        record.inspectConfigType === 'POINT'
          ? `${deviceType}-${record.pointCode}`
          : `${deviceType}-${record.configId}`;
      if (map[pointCode] === undefined) {
        map[pointCode] = 1;
      } else {
        map[pointCode] += 1;
      }

      return map;
    }, {});
    this.setState({
      tableData: newTableData,
      dataSource: getTableData(newTableData),
      pageTableData,
      rowSpansGroupByDataIndex,
    });
    this.props.onChange(newTableData);
  };

  setScenarios = (record, value) => {
    const { tableData, pageNum, pageSize } = this.state;
    const { inspectSubject, subTypeCode, inspectConfigType, configId, defaultCheckScenes } = record;
    const configData = tableData[inspectSubject][subTypeCode][inspectConfigType].map(item => {
      if (inspectConfigType === INSPECTION_CONFIG_TYPE_KEY_MAP.CUSTOM && item.id === configId) {
        return {
          ...item,
          standards: item.standards.map(standardItem => {
            if (standardItem.standardId === record.standardId) {
              return {
                ...standardItem,
                checkScenes: value || value === '' ? value : standardItem.checkScenes,
                defaultCheckScenes,
              };
            }
            return standardItem;
          }),
        };
      }
      if (item.metaCode === configId) {
        return {
          ...item,
          checkScenes: value || value === '' ? value : item.checkScenes,
          defaultCheckScenes,
        };
      }
      return item;
    });

    const newTableData = {
      ...cloneDeep(tableData),
      [inspectSubject]: {
        ...tableData[inspectSubject],
        [subTypeCode]: {
          ...tableData[inspectSubject][subTypeCode],
          [inspectConfigType]: configData,
        },
      },
    };
    const newDataSource = getTableData(newTableData);
    const start = pageNum === 1 ? 0 : (pageNum - 1) * pageSize;
    const end = pageSize * pageNum;
    const pageTableData = newDataSource.slice(start, end);
    const rowSpansGroupByDataIndex = pageTableData.reduce((map, record) => {
      const inspectSubject = record.inspectSubject;
      if (map[inspectSubject] === undefined) {
        map[inspectSubject] = 1;
      } else {
        map[inspectSubject] += 1;
      }

      const deviceType =
        record.inspectConfigType === 'POINT'
          ? `${inspectSubject}-${record.deviceType}`
          : `${inspectSubject}-${record.subTypeCode}`;
      if (map[deviceType] === undefined) {
        map[deviceType] = 1;
      } else {
        map[deviceType] += 1;
      }

      const pointCode =
        record.inspectConfigType === 'POINT'
          ? `${deviceType}-${record.pointCode}`
          : `${deviceType}-${record.configId}`;
      if (map[pointCode] === undefined) {
        map[pointCode] = 1;
      } else {
        map[pointCode] += 1;
      }

      return map;
    }, {});
    this.setState({
      tableData: newTableData,
      dataSource: getTableData(newTableData),
      pageTableData,
      rowSpansGroupByDataIndex,
    });
    this.props.onChange(newTableData);
  };

  getNewBunttonDisabled = () => {
    const { inspectScope, taskType } = this.props;
    let tmp = false;
    if (inspectScope === 'patrol' && !taskType) {
      tmp = true;
    }
    return tmp;
  };

  paginationChangeHandler = (current, size) => {
    const { dataSource } = this.state;
    const start = current === 1 ? 0 : (current - 1) * size;
    const end = size * current;
    const pageTableData = dataSource.slice(start, end);
    const rowSpansGroupByDataIndex = pageTableData.reduce((map, record) => {
      const inspectSubject = record.inspectSubject;
      if (map[inspectSubject] === undefined) {
        map[inspectSubject] = 1;
      } else {
        map[inspectSubject] += 1;
      }

      const deviceType =
        record.inspectConfigType === 'POINT'
          ? `${inspectSubject}-${record.deviceType}`
          : `${inspectSubject}-${record.subTypeCode}`;
      if (map[deviceType] === undefined) {
        map[deviceType] = 1;
      } else {
        map[deviceType] += 1;
      }

      const pointCode =
        record.inspectConfigType === 'POINT'
          ? `${deviceType}-${record.pointCode}`
          : `${deviceType}-${record.configId}`;
      if (map[pointCode] === undefined) {
        map[pointCode] = 1;
      } else {
        map[pointCode] += 1;
      }

      return map;
    }, {});
    this.setState({
      pageSize: size,
      pageNum: current,
      pageTableData,
      rowSpansGroupByDataIndex,
    });
  };

  render() {
    const {
      inspectScope,
      fieldValues,
      deviceCategory,
      mode,
      roomTypes,
      taskType,
      form,
      supportCheckScenes,
    } = this.props;
    const {
      createVisible,
      tableData,
      editVisible,
      editData,
      dataSource,
      rowSpansGroupByDataIndex,
      pageTableData,
      pageSize,
      pageNum,
    } = this.state;
    return (
      <>
        <Form>
          <TinyTable
            rowKey="id"
            align="left"
            scroll={{ x: 'max-content' }}
            dataSource={pageTableData}
            actions={
              mode === 'detail'
                ? []
                : [
                    <Button
                      key="create"
                      type="primary"
                      disabled={this.getNewBunttonDisabled()}
                      onClick={() => {
                        if (inspectScope === 'config') {
                          const tmp = this.props.validate();
                          if (tmp) {
                            this.setState({ createVisible: true });
                          }
                        } else {
                          this.setState({ createVisible: true });
                        }
                      }}
                    >
                      创建巡检项
                    </Button>,
                  ]
            }
            columns={getColumns({
              device: deviceCategory ? deviceCategory.normalizedList : null,
              deleteStands: this.deleteStands,
              edit: this.edit,
              setIsMeterReadAndUnit: this.setIsMeterReadAndUnit,
              mode: mode,
              roomTypes,
              dataSource: pageTableData,
              rowSpansGroupByDataIndex,
              getFieldDecorator: form.getFieldDecorator,
              supportCheckScenes,
              setScenarios: this.setScenarios,
              getFieldsValue: form.getFieldsValue,
              form,
            })}
            pagination={{
              total: dataSource.length,
              current: pageNum,
              pageSize: pageSize,
              onChange: this.paginationChangeHandler,
            }}
          />
        </Form>
        {createVisible && (
          <ConfigItemModal
            mode={mode}
            inspectScope={inspectScope}
            fieldValues={fieldValues}
            visible={createVisible}
            tableData={tableData}
            dataSource={dataSource}
            setVisible={(value, selectedInspectionConfig) => {
              this.setState({
                createVisible: value,
                tableData: selectedInspectionConfig ? selectedInspectionConfig : tableData,
                dataSource: selectedInspectionConfig
                  ? getTableData(selectedInspectionConfig)
                  : dataSource,
              });
            }}
            taskType={taskType}
            supportCheckScenes={supportCheckScenes}
          />
        )}
        <EditCustomConfig
          editVisible={editVisible}
          data={editData}
          setEditVisible={this.setEditVisible}
          updateTableData={this.updateTableData}
        />
      </>
    );
  }
}

const mapStateToProps = ({ common: { deviceCategory, roomTypes } }) => {
  return {
    deviceCategory,
    roomTypes,
  };
};

const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
};

const connectOpts = { forwardRef: true };

export default connect(
  mapStateToProps,
  mapDispatchToProps,
  null,
  connectOpts
)(Form.create()(InspectionTable));

const getColumns = ({
  device,
  deleteStands,
  edit,
  setIsMeterReadAndUnit,
  mode,
  roomTypes,
  dataSource,
  rowSpansGroupByDataIndex,
  getFieldDecorator,
  supportCheckScenes,
  setScenarios,
  getFieldsValue,
  form,
}) => {
  if (mode === 'detail') {
    return [
      {
        title: '巡检对象',
        dataIndex: 'inspectSubject',
        className: 'merge-row-span',
        render: (text, record, index) => {
          if (!text) {
            return '';
          }
          const obj = {
            children: INSPECTION_TYPE_TEXT_MAP[text],
            props: {
              rowSpan: getRowSpan(text, index, {
                datasource: dataSource,
                dataIndex: 'inspectSubject',
                rowSpansGroupByDataIndex,
                currentText: text,
              }),
            },
          };
          return obj;
        },
      },
      {
        title: '巡检对象类型',
        dataIndex: 'subTypeCode',
        className: 'merge-row-span',
        render: (text, record, index) => {
          if (!text) {
            return '';
          }
          let subTypeName = '';
          if (record.inspectSubject === INSPECTION_TYPE_KEY_MAP.ENVIRONMENT && roomTypes) {
            subTypeName = roomTypes[text];
          }
          if (record.inspectSubject === INSPECTION_TYPE_KEY_MAP.DEVICE && device && device[text]) {
            subTypeName = device[text].metaName;
          }
          const tmp =
            record.inspectConfigType === 'POINT'
              ? `${record.inspectSubject}-${text}`
              : `${record.inspectSubject}-${record.subTypeCode}`;
          const obj = {
            children: subTypeName,
            props: {
              rowSpan: getRowSpan(tmp, index, {
                datasource: dataSource,
                dataIndex: 'subTypeCode',
                rowSpansGroupByDataIndex,
                currentText: record.inspectConfigType === 'POINT' ? text : record.subTypeCode,
              }),
            },
          };
          return obj;
        },
      },
      {
        title: '检查项',
        dataIndex: 'configId',
        className: 'merge-row-span',
        render: (text, record, index) => {
          if (text && record.inspectConfigType !== 'POINT') {
            const tmp = `${record.inspectSubject}-${record.subTypeCode}-${record.configId}`;
            const obj = {
              children: record.pointName,
              props: {
                rowSpan: getRowSpan(tmp, index, {
                  datasource: dataSource,
                  dataIndex: 'configId',
                  rowSpansGroupByDataIndex,
                  currentText: text,
                }),
              },
            };
            return obj;
          }
          if (record.pointCode) {
            return record.pointName;
          }
          return '';
        },
      },
      {
        title: '检查标准',
        dataIndex: 'checkStdInfoList',
        render: (_, record) => getTableCheckStdTxt(record),
      },
      {
        title: '是否需要抄表',
        dataIndex: 'isMeterRead',
        render: (_, record) => {
          // const isStatefulQuantity =
          //   record.dataType?.code === 'DI' || record.dataType?.code === 'AI'; //状态量
          if (record.inspectConfigType === INSPECTION_CONFIG_TYPE_KEY_MAP.POINT) {
            if (record.isMeterRead) {
              return (
                <>
                  是 , 正常阈值: {record.minValue ?? '--'} ~ {record.maxValue ?? '--'}
                </>
              );
            }
            const is = !![...(record.normalTextList || []), ...(record.exTextList || [])].length;
            if (is) {
              return (
                <>
                  否 {is && ' , '}
                  {record.normalTextList.length >= 1 &&
                    ` 正常选项: ${record.normalTextList.join(' , ')} `}
                  {!!(record.normalTextList.length >= 1 && record.exTextList.length >= 1) && ' , '}
                  {record.exTextList.length >= 1 && ` 异常选项: ${record.exTextList.join(' , ')}`}
                </>
              );
            }
            return '否';
          }
          if (record.inspectConfigType === INSPECTION_CONFIG_TYPE_KEY_MAP.CUSTOM) {
            if (record.isMeterRead) {
              return (
                <>
                  是 , 正常阈值: {record.minValue ?? '--'} ~ {record.maxValue ?? '--'}
                </>
              );
            }
            const is = !![...(record.normalTextList || []), ...(record.exTextList || [])].length;
            if (is) {
              return (
                <>
                  否 {is && ' , '}
                  {record.normalTextList.length >= 1 &&
                    ` 正常选项: ${record.normalTextList.join(' , ')} `}
                  {!!(record.normalTextList.length >= 1 && record.exTextList.length >= 1) && ' , '}
                  {record.exTextList.length >= 1 && ` 异常选项: ${record.exTextList.join(' , ')}`}
                </>
              );
            }
            return '否';
          }
          return '--';
        },
      },
      supportCheckScenes && {
        title: '适用巡检场景',
        dataIndex: 'checkScenes',
        render: checkScenes => {
          return (
            // @todo Wly 需要改做查询一次接口
            <>
              <GetCheckSceneText checkScenes={checkScenes?.split(',') || []} />
            </>
          );
        },
      },
    ].filter(Boolean);
  }
  return [
    {
      title: '巡检对象',
      dataIndex: 'inspectSubject',
      className: 'merge-row-span',
      render: (text, record, index) => {
        if (!text) {
          return '';
        }
        const list =
          dataSource
            ?.map(i => {
              if (i.inspectSubject === text && typeof i.checkScenes === 'string' && i.checkScenes) {
                return i.checkScenes.split(',');
              }
              return null;
            })
            .filter(Boolean)
            .flat() || [];
        const initList = [...new Set(list)];
        const defaultValue = dataSource.find(i => i.defaultCheckScenes)?.defaultCheckScenes;

        const obj = {
          children: (
            <div style={{ display: 'flex' }}>
              <div style={{ marginTop: '10px' }}>
                <Typography.Text style={{ marginRight: '8px' }}>
                  {INSPECTION_TYPE_TEXT_MAP[text]}
                </Typography.Text>
                {supportCheckScenes && (
                  <Typography.Text style={{ marginRight: '4px' }}>首选场景</Typography.Text>
                )}
              </div>
              {supportCheckScenes && (
                <Form.Item colon={false} style={{ display: 'inline-block', marginBottom: 'auto' }}>
                  {getFieldDecorator(`${record.id}-initCheckScene`, {
                    rules: [
                      // {
                      //   required: true,
                      //   message: '首选场景为必选',
                      // },
                    ],
                  })(
                    <PreferredScenario
                      initList={list}
                      defaultValue={defaultValue}
                      style={{ width: 104 }}
                      onChange={value => {
                        setScenarios({ ...record, defaultCheckScenes: value });
                      }}
                    />
                  )}
                </Form.Item>
              )}
            </div>
          ),
          props: {
            rowSpan: getRowSpan(text, index, {
              datasource: dataSource,
              dataIndex: 'inspectSubject',
              rowSpansGroupByDataIndex,
              currentText: text,
            }),
          },
        };

        return obj;
      },
    },
    {
      title: '巡检对象类型',
      dataIndex: 'subTypeCode',
      className: 'merge-row-span',
      render: (text, record, index) => {
        if (!text) {
          return '';
        }
        let subTypeName = '';
        if (record.inspectSubject === INSPECTION_TYPE_KEY_MAP.ENVIRONMENT && roomTypes) {
          subTypeName = roomTypes[text];
        }
        if (record.inspectSubject === INSPECTION_TYPE_KEY_MAP.DEVICE && device && device[text]) {
          subTypeName = device[text].metaName;
        }
        const tmp =
          record.inspectConfigType === 'POINT'
            ? `${record.inspectSubject}-${text}`
            : `${record.inspectSubject}-${record.subTypeCode}`;
        const obj = {
          children: subTypeName,
          props: {
            rowSpan: getRowSpan(tmp, index, {
              datasource: dataSource,
              dataIndex: 'subTypeCode',
              rowSpansGroupByDataIndex,
              currentText: record.inspectConfigType === 'POINT' ? text : record.subTypeCode,
            }),
          },
        };
        return obj;
      },
    },
    {
      title: '检查项',
      dataIndex: 'configId',
      className: 'merge-row-span',
      render: (text, record, index) => {
        if (text && record.inspectConfigType !== 'POINT') {
          const tmp = `${record.inspectSubject}-${record.subTypeCode}-${record.configId}`;
          const obj = {
            children: record.pointName,
            props: {
              rowSpan: getRowSpan(tmp, index, {
                datasource: dataSource,
                dataIndex: 'configId',
                rowSpansGroupByDataIndex,
                currentText: text,
              }),
            },
          };
          return obj;
        }
        if (record.pointCode) {
          return record.pointName;
        }
        return '';
      },
    },
    {
      title: '检查标准',
      dataIndex: 'checkStdInfoList',
      render: (_, record) => getTableCheckStdTxt(record),
    },
    {
      title: '是否需要抄表',
      dataIndex: 'isMeterRead',
      render: (_, record, index) => {
        const isMeterRead = record.isMeterRead;
        // const isStatefulQuantity = record.dataType?.code === 'DI' || record.dataType?.code === 'AI'; //状态量
        if (record.inspectionConfigId) {
          if (isMeterRead) {
            return '是';
          }
          return '否';
        }
        let radioValue = 0;
        if (isMeterRead) {
          radioValue = isMeterRead;
        }
        return (
          <GutterWrapper direction="horizontal">
            <Form.Item style={{ display: 'inline-block', marginBottom: 'auto' }}>
              <Radio.Group
                value={radioValue}
                onChange={e => setIsMeterReadAndUnit(record, e.target.value, 'isMeterRead')}
              >
                <Radio value={0}>否</Radio>
                <Radio value={1}>是</Radio>
              </Radio.Group>
            </Form.Item>
            {radioValue === 0 && (
              <Space>
                正常选项
                <Form.Item colon={false} style={{ display: 'inline-block', marginBottom: 'auto' }}>
                  {getFieldDecorator(`${record.id}-normalTextList`, {
                    initialValue: record.normalTextList,
                    rules: [
                      {
                        required: true,
                        message: '正常选项为必选',
                      },
                    ],
                  })(
                    <EditableInput
                      key={1}
                      radioValue={radioValue}
                      value={record.normalTextList}
                      onChange={value => {
                        setIsMeterReadAndUnit(record, value ?? undefined, 'normalTextList');
                      }}
                    />
                  )}
                </Form.Item>
                异常选项
                <Form.Item colon={false} style={{ display: 'inline-block', marginBottom: 'auto' }}>
                  {getFieldDecorator(`${record.id}-exTextList`, {
                    initialValue: record.exTextList,
                    rules: [
                      {
                        required: true,
                        message: '异常选项为必选',
                      },
                    ],
                  })(
                    <EditableInput
                      key={0}
                      radioValue={radioValue}
                      value={record.exTextList}
                      onChange={value => {
                        setIsMeterReadAndUnit(record, value ?? undefined, 'exTextList');
                      }}
                    />
                  )}
                </Form.Item>
              </Space>
            )}
            {record.inspectConfigType === INSPECTION_CONFIG_TYPE_KEY_MAP.CUSTOM &&
              radioValue === 1 && (
                <Space>
                  正常阈值
                  <Form.Item
                    colon={false}
                    style={{ display: 'inline-block', marginBottom: 'auto' }}
                  >
                    {getFieldDecorator(`${index}-min`, {
                      rules: [
                        {
                          validator: (_, __, callback) => {
                            if (
                              typeof getFieldsValue()[`${index}-max`] === 'number' &&
                              typeof getFieldsValue()[`${index}-min`] !== 'number'
                            ) {
                              callback('最小值为必选');
                              return;
                            }
                            callback();
                          },
                        },
                      ],
                      initialValue: record.minValue,
                    })(
                      <InputNumber
                        style={{ width: 90 }}
                        value={record.minValue}
                        precision={2}
                        onChange={value => {
                          setIsMeterReadAndUnit(record, value ?? undefined, 'minValue');
                        }}
                      />
                    )}
                  </Form.Item>
                  <Typography.Text>~</Typography.Text>
                  <Form.Item
                    colon={false}
                    style={{ display: 'inline-block', marginBottom: 'auto' }}
                  >
                    {getFieldDecorator(`${index}-max`, {
                      rules: [
                        {
                          validator: (_, __, callback) => {
                            const fieldsValue = getFieldsValue();
                            if (
                              typeof fieldsValue[`${index}-max`] !== 'number' &&
                              typeof fieldsValue[`${index}-min`] === 'number'
                            ) {
                              callback('最大值为必选');
                              return;
                            }
                            if (
                              typeof fieldsValue[`${index}-max`] === 'number' &&
                              typeof fieldsValue[`${index}-min`] === 'number' &&
                              fieldsValue[`${index}-max`] < fieldsValue[`${index}-min`]
                            ) {
                              callback('最大值不能小于最小值');
                              return;
                            }
                            callback();
                          },
                        },
                      ],
                      initialValue: record.maxValue,
                    })(
                      <InputNumber
                        style={{ width: 90 }}
                        min={record.minValue}
                        value={record?.maxValue}
                        precision={2}
                        onChange={value => {
                          setIsMeterReadAndUnit(record, value, 'maxValue');
                        }}
                      />
                    )}
                  </Form.Item>
                  <Form.Item
                    colon={false}
                    style={{ display: 'inline-block', marginBottom: 'auto' }}
                  >
                    {getFieldDecorator(`${index}-unit`, {
                      rules: [
                        {
                          required: true,
                          message: '单位为必选',
                        },
                      ],
                      initialValue: record.unit,
                    })(
                      <MetaTypeSelect
                        showSearch
                        value={record.unit}
                        metaType="SPEC_UNIT"
                        style={{ width: 96 }}
                        onChange={value => {
                          setIsMeterReadAndUnit(record, value, 'unit');
                        }}
                      />
                    )}
                  </Form.Item>
                </Space>
              )}
          </GutterWrapper>
        );
      },
    },
    supportCheckScenes && {
      title: '适用巡检场景',
      dataIndex: 'checkScenes',
      render: (checkScenes, record, index) => {
        return (
          <Form.Item colon={false} style={{ display: 'inline-block', marginBottom: 'auto' }}>
            {getFieldDecorator(`${record.id}-checkScenes`, {
              rules: [
                {
                  required: true,
                  message: '巡检场景为必选',
                },
              ],
              initialValue: checkScenes?.split(',').filter(i => i != null && i !== ''),
            })(
              <MultipleSelect
                style={{ width: 200 }}
                // disabled={record.inspectSubject === INSPECTION_TYPE_KEY_MAP.ENVIRONMENT}
                onChange={value => {
                  form.setFieldsValue({ [`${record.inspectSubject}-initCheckScene`]: undefined });
                  setScenarios(record, value?.join(','));
                }}
              />
            )}
          </Form.Item>
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'operation',
      className: 'merge-row-span',
      render: (_, record, index) => {
        if (record.inspectionConfigId) {
          return '--';
        }
        if (record.inspectConfigType === INSPECTION_CONFIG_TYPE_KEY_MAP.POINT) {
          return (
            <span>
              <Button
                compact
                type="link"
                style={{ padding: 0, height: 'auto' }}
                onClick={() => deleteStands(record)}
              >
                删除
              </Button>
            </span>
          );
        }
        const tmp = `${record.inspectSubject}-${record.subTypeCode}-${record.configId}`;

        return {
          children: (
            <span>
              <Button type="link" compact onClick={() => edit(record)}>
                修改
              </Button>
              <Divider type="vertical" />
              <Button type="link" compact onClick={() => deleteStands(record)}>
                删除
              </Button>
            </span>
          ),
          props: {
            rowSpan: getRowSpan(tmp, index, {
              datasource: dataSource,
              dataIndex: 'configId',
              rowSpansGroupByDataIndex,
              currentText: record.configId,
            }),
          },
        };
      },
    },
  ].filter(Boolean);
};

function EditableInput(props) {
  const { radioValue } = props;
  const [tags, setTags] = React.useState([]);
  const [inputVisible, setInputVisible] = React.useState(false);
  const [editInputIndex, setEditInputIndex] = React.useState(-1);
  const [inputValue, setInputValue] = React.useState('');
  const inputRef = React.useRef();
  const editInputRef = React.useRef();

  // 过滤特殊字符的函数
  const filterSpecialChars = str => {
    // 只允许中文、英文、数字和常见标点
    return str.replace(/[^\u4e00-\u9fa5a-zA-Z0-9.,，。!?！？:：;；()（）\-]/g, '');
  };

  const handleInputConfirm = () => {
    const filteredValue = filterSpecialChars(inputValue.trim());
    if (filteredValue && tags.indexOf(filteredValue) === -1) {
      const newTags = [...tags, filteredValue];
      setTags(newTags);
      props?.onChange(newTags);
    }
    setInputVisible(false);
    setInputValue('');
  };
  const onClose = removedTag => {
    const newTags = tags.filter(tag => tag !== removedTag);
    setTags(newTags);
    props?.onChange(newTags);
  };
  React.useEffect(() => {
    setTags(props.value || []);
  }, [props.value]);

  return (
    <>
      {tags.map((tag, index) => {
        // if (editInputIndex === index) {
        //   return (
        //     <Input
        //       ref={editInputRef}
        //       key={tag}
        //       size="small"
        //       className="tag-input"
        //       value={editInputValue}
        //       onChange={handleEditInputChange}
        //       onBlur={handleEditInputConfirm}
        //       onPressEnter={handleEditInputConfirm}
        //     />
        //   );
        // }
        const isLongTag = tag.length > 5;

        const tagElem = (
          <Tag
            key={tag}
            //closable={index !== 0}
            closable={true}
            onClose={e => {
              e.preventDefault();
              onClose(tag);
            }}
          >
            <span
              onDoubleClick={e => {
                // if (index !== 0) {
                setEditInputIndex(index);
                setEditInputValue(tag);
                e.preventDefault();
                // }
              }}
            >
              {isLongTag ? `${tag.slice(0, 5)}...` : tag}
            </span>
          </Tag>
        );
        return isLongTag ? (
          <Tooltip title={tag} key={tag}>
            {tagElem}
          </Tooltip>
        ) : (
          tagElem
        );
      })}
      {inputVisible && (
        <Input
          style={{ width: 68 }}
          ref={inputRef}
          type="text"
          value={inputValue.trim()}
          maxLength={4}
          onChange={e => {
            setInputValue(e.target.value);
          }}
          onBlur={handleInputConfirm}
          onPressEnter={handleInputConfirm}
        />
      )}
      {!inputVisible && tags.length < 4 && (
        <Tag
          onClick={() => {
            setInputVisible(true);
          }}
        >
          <PlusOutlined /> 添加
        </Tag>
      )}
    </>
  );
}

function MultipleSelect(props) {
  const [{ data }, { readMetaData }] = useMetaData(MetaType.INSPECT_SCENES);
  React.useEffect(() => {
    readMetaData();
  }, []);
  return (
    <Select
      {...props}
      mode="multiple"
      options={
        data?.data.map(item => ({
          key: item.value,
          value: item.value,
          label: item.label,
        })) || []
      }
    />
  );
}
function PreferredScenario(props) {
  const [{ data }, { readMetaData }] = useMetaData(MetaType.INSPECT_SCENES);
  const memoOptions = React.useMemo(() => {
    return data?.data
      .map(item => ({
        key: item.value,
        value: item.value,
        label: item.label,
      }))
      .filter(i => props.initList.includes(i.value));
  }, [props.initList, data?.data]);
  React.useEffect(() => {
    readMetaData();
  }, []);
  return (
    <Select
      {...props}
      value={props.value || props.defaultValue}
      allowClear
      options={memoOptions || []}
    />
  );
}
function GetCheckSceneText({ checkScenes }) {
  const [{ data }, { readMetaData }] = useMetaData(MetaType.INSPECT_SCENES);

  React.useEffect(() => {
    readMetaData();
  }, []);
  const textList = React.useMemo(() => {
    return checkScenes
      .map(item => {
        return data?.data.find(i => i.value === item)?.label;
      })
      .filter(Boolean);
  }, [data?.data, checkScenes]);

  return <>{textList.join(' , ')}</>;
}
