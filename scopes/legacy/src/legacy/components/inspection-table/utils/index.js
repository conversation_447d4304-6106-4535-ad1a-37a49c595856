import { InspectionScenarios } from '@manyun/ticket.model.ticket';

import { INSPECTION_CONFIG_TYPE_KEY_MAP } from '@manyun/dc-brain.legacy.constants/inspection';
import { POINT_DATA_TYPE_CODE_MAP } from '@manyun/dc-brain.legacy.constants/point';

import { INSPECTION_STANDARD_TEXT_AI_AND_DI } from '../constants';

export function getTableData(data) {
  let newData = [];
  Object.keys(data).forEach(item1 => {
    const inspectData = data[item1]; // item1 为巡检类型  DEVICE | ENVIRONMENT
    Object.keys(inspectData).forEach(item2 => {
      const deviceData = inspectData[item2]; // item2 为巡检子类型，就是设备类型， 10101
      Object.keys(deviceData).forEach(item3 => {
        const configData = deviceData[item3]; // item3 为巡检项类型  ，POINT | CUSTOM
        if (item3 === 'CUSTOM') {
          configData.forEach(config => {
            // config 为每一天巡检标准
            const stands = config.standards; // 巡检标准
            if (!stands || stands.length === 0) {
              return;
            }
            stands.forEach(
              ({
                standardId,
                standardTxt,
                isMeterRead,
                unit,
                minValue,
                maxValue,
                inspectionConfigId,
                checkScenes,
                normalTextList,
                exTextList,
                defaultCheckScenes,
              }) => {
                const stand = {
                  id: standardId,
                  configId: config.id, // 巡检项id
                  standardId: standardId, // 检查标准id
                  inspectSubject: item1,
                  subTypeCode: item2,
                  pointName: config.checkItemName.value ? config.checkItemName.value.trim() : null,
                  checkStdInfoList: standardTxt.value,
                  inspectConfigType: 'CUSTOM',
                  inspectionConfigId: inspectionConfigId,
                  inspectType: config.inspectType,
                  isMeterRead: isMeterRead,
                  unit,
                  minValue,
                  maxValue,
                  checkScenes,
                  normalTextList,
                  exTextList,
                  defaultCheckScenes,
                };
                newData = [...newData, stand];
              }
            );
          });
        } else {
          const points = configData.map(p => {
            return {
              ...p,
              id: `${p.deviceType}-${p.metaCode ? p.metaCode : p.pointCode}`,
              inspectSubject: item1,
              subTypeCode: item2,
              configId: p.metaCode,
              pointName: p.metaName,
              checkStdInfoList: null,
              inspectConfigType: 'POINT',
              isMeterRead: p.isMeterRead,
              inspectionConfigId: p.inspectionConfigId,
              inspectType: p.inspectType,
              checkScenes: p.checkScenes,
            };
          });
          newData = [...newData, ...points];
        }
      });
    });
  });
  return newData;
}

export function convertArrToObj(data) {
  const newObj = assemblyArrToObject(data);
  return newObj;
}

export function deleteNoChildrensKeys(tableObj) {
  let newObj = {};
  const data = getTableData(tableObj);
  if (data && data.length) {
    newObj = assemblyArrToObject(data);
  }
  return newObj;
}

export function assemblyArrToObject(data) {
  let newObj = {};
  if (data && data.length) {
    data.forEach(item => {
      const { inspectSubject, subTypeCode, inspectConfigType } = item;
      if (!newObj[inspectSubject]) {
        if (inspectConfigType === INSPECTION_CONFIG_TYPE_KEY_MAP.POINT) {
          newObj = {
            ...newObj,
            [inspectSubject]: {
              [subTypeCode]: {
                [inspectConfigType]: [item],
              },
            },
          };
        } else {
          newObj = {
            ...newObj,
            [inspectSubject]: {
              [subTypeCode]: {
                [inspectConfigType]: getCheckStands([], item),
              },
            },
          };
        }
      } else if (newObj[inspectSubject] && !newObj[inspectSubject][subTypeCode]) {
        if (inspectConfigType === INSPECTION_CONFIG_TYPE_KEY_MAP.POINT) {
          newObj = {
            ...newObj,
            [inspectSubject]: {
              ...newObj[inspectSubject],
              [subTypeCode]: {
                [inspectConfigType]: [item],
              },
            },
          };
        } else {
          newObj = {
            ...newObj,
            [inspectSubject]: {
              ...newObj[inspectSubject],
              [subTypeCode]: {
                [inspectConfigType]: getCheckStands([], item),
              },
            },
          };
        }
      } else if (
        newObj[inspectSubject] &&
        newObj[inspectSubject][subTypeCode] &&
        !newObj[inspectSubject][subTypeCode][inspectConfigType]
      ) {
        if (inspectConfigType === INSPECTION_CONFIG_TYPE_KEY_MAP.POINT) {
          newObj = {
            ...newObj,
            [inspectSubject]: {
              ...newObj[inspectSubject],
              [subTypeCode]: {
                ...newObj[inspectSubject][subTypeCode],
                [inspectConfigType]: [item],
              },
            },
          };
        } else {
          newObj = {
            ...newObj,
            [inspectSubject]: {
              ...newObj[inspectSubject],
              [subTypeCode]: {
                ...newObj[inspectSubject][subTypeCode],
                [inspectConfigType]: getCheckStands([], item),
              },
            },
          };
        }
      } else {
        if (inspectConfigType === INSPECTION_CONFIG_TYPE_KEY_MAP.POINT) {
          newObj = {
            ...newObj,
            [inspectSubject]: {
              ...newObj[inspectSubject],
              [subTypeCode]: {
                ...newObj[inspectSubject][subTypeCode],
                [inspectConfigType]: [
                  ...newObj[inspectSubject][subTypeCode][inspectConfigType],
                  item,
                ],
              },
            },
          };
        } else {
          newObj = {
            ...newObj,
            [inspectSubject]: {
              ...newObj[inspectSubject],
              [subTypeCode]: {
                ...newObj[inspectSubject][subTypeCode],
                [inspectConfigType]: getCheckStands(
                  newObj[inspectSubject][subTypeCode].CUSTOM,
                  item
                ),
              },
            },
          };
        }
      }
    });
  }
  return newObj;
}

function getCheckStands(standsArr, single) {
  let newArr = standsArr;
  const ids = standsArr.map(({ id }) => id);
  if (ids.includes(single.configId)) {
    newArr = standsArr.map(item => {
      if (item.id === single.configId) {
        const stand = [
          ...item.standards,
          {
            standardId: single.standardId,
            name: single.standardId,
            isMeterRead: single.isMeterRead,
            unit: single.unit,
            minValue: single.minValue,
            maxValue: single.maxValue,
            standardTxt: {
              value: single.checkStdInfoList,
            },
            checkScenes: single.checkScenes,
            inspectionConfigId: single.inspectionConfigId,
          },
        ];
        return { ...item, standards: stand };
      }
      return item;
    });
  } else {
    const tmp = {
      id: single.configId,
      checkItemName: {
        name: 'checkItemName',
        value: single.pointName,
      },
      standards: [
        {
          standardId: single.standardId,
          name: single.standardId,
          isMeterRead: single.isMeterRead,
          unit: single.unit,
          minValue: single.minValue,
          maxValue: single.maxValue,
          inspectionConfigId: single.inspectionConfigId,
          standardTxt: {
            value: single.checkStdInfoList,
          },
          checkScenes: single.checkScenes,
        },
      ],
      inspectionConfigId: single.inspectionConfigId,
    };
    newArr = [...newArr, tmp];
  }
  return newArr;
}

export function uniqByStandards(checkItems) {
  const tmp = {};
  // 过滤 检查项名称,检查标准相同的数据
  const newItems = checkItems
    .map(item => {
      const stands = item.standards
        .map(std => {
          const chectItemAndStandardTxt = `${item.checkItemName.value}_$$_${std.standardTxt.value}`;
          if (!tmp[chectItemAndStandardTxt]) {
            tmp[chectItemAndStandardTxt] = chectItemAndStandardTxt;
            return std;
          }
          return null;
        })
        .filter(Boolean);
      if (stands.length) {
        return {
          ...item,
          standards: stands,
        };
      }
      return null;
    })
    .filter(Boolean);
  return newItems;
}

export function uniqByPoints(points) {
  let newPoins = [];
  points.forEach(point => {
    if (!newPoins[0]) {
      newPoins = [...newPoins, point];
      return;
    }
    const tmp = newPoins.find(
      item => item.pointCode === point.pointCode && item.deviceType === point.deviceType
    );
    if (!tmp) {
      newPoins = [...newPoins, point];
    }
  });

  return newPoins;
}

export function getUniqFileds(fileds, prevFileds) {
  let changedFileds = null;
  if (!fileds && !prevFileds) {
    return changedFileds;
  }
  if (!fileds && prevFileds) {
    return prevFileds;
  }
  if (fileds && !prevFileds) {
    return fileds;
  }
  Object.keys(fileds).forEach(key => {
    if ((fileds[key] && !prevFileds[key]) || fileds[key].value !== prevFileds[key].value) {
      if (!changedFileds) {
        changedFileds = { [key]: fileds[key] };
        return;
      }
      changedFileds = { ...changedFileds, [key]: fileds[key] };
    }
  });
  return changedFileds;
}

export function changeSelectedPoinesMapToList(maps) {
  if (!maps) {
    return [];
  }
  let currentSelectedPoints = [];
  Object.keys(maps).forEach(code => {
    const points = maps[code];
    if (points && Array.isArray(points)) {
      const tmp = maps[code].map(item => ({
        ...item,
        deviceTypeAndCode: `${item.deviceType}-${item.code}`,
      }));
      currentSelectedPoints = [...currentSelectedPoints, ...tmp];
    }
  });
  return currentSelectedPoints;
}

/**
 * 获取巡检项表格中检查标准对应的文案
 * @param {Object} rowData  table中某一行的数据
 * @return {String} 返回文案字符串
 */
export function getTableCheckStdTxt(rowData) {
  if (rowData.inspectConfigType === INSPECTION_CONFIG_TYPE_KEY_MAP.POINT) {
    if (
      rowData.dataType === POINT_DATA_TYPE_CODE_MAP.AI ||
      rowData.dataType?.code === POINT_DATA_TYPE_CODE_MAP.AI
    ) {
      return INSPECTION_STANDARD_TEXT_AI_AND_DI.AI_TEXT;
    }
    return INSPECTION_STANDARD_TEXT_AI_AND_DI.DI_TEXT;
  }
  return rowData.checkStdInfoList;
}
