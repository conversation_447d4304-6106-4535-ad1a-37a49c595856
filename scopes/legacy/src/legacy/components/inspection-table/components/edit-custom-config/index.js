import React, { Component } from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';

import EhancedStandardForm from '@manyun/dc-brain.legacy.components/inspection-list/components/form';

function throws(error) {
  throw new Error(error);
}

class EditCustomConfig extends Component {
  state = {
    list: [],
  };

  editConfigFormRef = React.createRef();

  componentDidUpdate(prevProps) {
    if (!prevProps.editVisible && this.props.editVisible && this.props.data.length) {
      this.setState({
        list: this.props.data,
      });
    }
  }

  setInspectionItem = data => {
    this.setState({
      list: data,
    });
  };

  validateForm = async () => {
    try {
      await this.editConfigFormRef.current.form.validateFields();
      const values = this.editConfigFormRef.current.form.getFieldsValue();
      const _keys = Object.keys(values);
      if (_keys?.find(i => values[i].trim() === '')) {
        message.error('请输入正确数值');
        // throw new Error('请输入正确数值');
        return;
      }
      this.props.updateTableData(this.state.list);
      this.props.setEditVisible(false);
    } catch (error) {
      console.log(error);
    }
  };

  render() {
    const { editVisible } = this.props;
    const { list } = this.state;

    return (
      <Modal
        width={770}
        visible={editVisible}
        onOk={this.validateForm}
        onCancel={() => this.props.setEditVisible(false)}
      >
        <EhancedStandardForm
          wrappedComponentRef={this.editConfigFormRef}
          inspectItemInfo={list[0]}
          data={list}
          setInspectionItem={this.setInspectionItem}
        />
      </Modal>
    );
  }
}

export default EditCustomConfig;
