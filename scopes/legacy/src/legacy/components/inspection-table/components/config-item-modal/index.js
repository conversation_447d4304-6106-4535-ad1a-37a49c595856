import cloneDeep from 'lodash/cloneDeep';
import difference from 'lodash/difference';
import uniqBy from 'lodash/uniqBy';
import React, { Component } from 'react';
import shortid from 'shortid';

import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import { InspectionScenarios } from '@manyun/ticket.model.ticket';

import {
  INSPECTION_CONFIG_TYPE_KEY_MAP,
  INSPECTION_TYPE_KEY_MAP,
} from '@manyun/dc-brain.legacy.constants/inspection';

import {
  changeSelectedPoinesMapToList,
  convertArrToObj,
  getTableData,
  getUniqFileds,
  uniqByPoints,
  uniqByStandards,
} from '../../utils';
import CustomItem from './custom-item';

function throws(error) {
  throw new Error(error);
}

function getInspectTypeInite(inspectScope, dataSource, taskType) {
  let value = null;
  if (inspectScope === 'config' && dataSource.length) {
    value = dataSource[0].inspectType;
  }
  if (inspectScope === 'patrol' && taskType) {
    value = taskType;
  }
  return value;
}
class ConfigItemModal extends Component {
  state = {
    inspectionChildItems: [],
    inspectionChildRefMaps: new Map(),
    formOptions: {
      inspectType: {
        value: null,
        name: 'inspectType',
      },
      inspectConfigType: {
        value: INSPECTION_CONFIG_TYPE_KEY_MAP.CUSTOM,
        name: 'inspectConfigType',
      },
      subTypeCode: {
        value: null,
        name: 'subTypeCode',
      },
      roomType: {
        value: null,
        name: 'roomType',
      },
      points: {
        value: null,
        name: 'points',
      },
      inspectSubject: {
        value: null,
        name: 'inspectSubject',
      },
      // ...this.props.fieldValues,
    },
    selectedInspectionConfig: [],
    tabKey: 'custom',
    selectedInspectionConfigIds: [],
    selectedPointsMapInModal: null,
  };

  basicConfigFormRef = React.createRef();

  componentDidMount() {
    const { formOptions } = this.state;
    // config 状态下，当新建或者编辑状态时，巡检配置中选择了巡检对象时 将外层巡检配置中的巡检对象赋值给创建弹框中的巡检对象,外层subTypeCode改变，更新弹窗中的subTypeCode
    if (this.props.mode !== 'detail') {
      const changesFileds = getUniqFileds(this.props.fieldValues);
      let fileds = {
        ...formOptions,
        ...this.props.fieldValues,
      };

      // 如果巡检对象选择了空间，则巡检项目只能为自定义
      if (
        changesFileds &&
        changesFileds.inspectSubject &&
        changesFileds.inspectSubject.value === INSPECTION_TYPE_KEY_MAP.ENVIRONMENT
      ) {
        fileds = {
          ...fileds,
          inspectConfigType: {
            value: INSPECTION_CONFIG_TYPE_KEY_MAP.CUSTOM,
            name: 'inspectType',
            dirty: false,
            touched: true,
          },
        };
      }
      this.setState({
        formOptions: fileds,
      });
    }

    if (this.props.taskType) {
      this.setState({
        formOptions: {
          ...formOptions,
          inspectType: {
            value: getInspectTypeInite(
              this.props.inspectScope,
              this.props.dataSource,
              this.props.taskType
            ),
            name: 'inspectType',
            dirty: false,
            touched: true,
          },
        },
      });
    }

    // 改变 工单类型，清空选择的巡检配置
    if (this.props.taskType) {
      this.setState({
        selectedInspectionConfig: [],
        selectedInspectionConfigIds: [],
      });
    }
  }

  componentDidUpdate(prevProps) {
    const { formOptions } = this.state;
    // config 状态下，当新建或者编辑状态时，巡检配置中选择了巡检对象时 将外层巡检配置中的巡检对象赋值给创建弹框中的巡检对象,外层subTypeCode改变，更新弹窗中的subTypeCode
    const prevFieldValuesString = prevProps.fieldValues
      ? Object.keys(prevProps.fieldValues)
          .map(key => prevProps.fieldValues[key].value)
          .join('-')
      : '';
    const currentFieldValuesString = this.props.fieldValues
      ? Object.keys(this.props.fieldValues)
          .map(key => this.props.fieldValues[key].value)
          .join('-')
      : '';

    if (this.props.mode !== 'detail' && prevFieldValuesString !== currentFieldValuesString) {
      const changesFileds = getUniqFileds(this.props.fieldValues, prevProps.fieldValues);
      let fileds = {
        ...formOptions,
        ...this.props.fieldValues,
      };

      // 如果巡检对象选择了空间，则巡检项目只能为自定义
      if (
        changesFileds &&
        changesFileds.inspectSubject &&
        changesFileds.inspectSubject.value === INSPECTION_TYPE_KEY_MAP.ENVIRONMENT
      ) {
        fileds = {
          ...fileds,
          inspectConfigType: {
            value: INSPECTION_CONFIG_TYPE_KEY_MAP.CUSTOM,
            name: 'inspectType',
            dirty: false,
            touched: true,
          },
        };
      }
      this.setState({
        formOptions: fileds,
      });
    }

    if (!prevProps.taskType && this.props.taskType) {
      this.setState({
        formOptions: {
          ...formOptions,
          inspectType: {
            value: getInspectTypeInite(
              this.props.inspectScope,
              this.props.dataSource,
              this.props.taskType
            ),
            name: 'inspectType',
            dirty: false,
            touched: true,
          },
        },
      });
    }

    // 改变 工单类型，清空选择的巡检配置
    if (prevProps.taskType !== this.props.taskType) {
      this.setState({
        selectedInspectionConfig: [],
        selectedInspectionConfigIds: [],
      });
    }
  }

  inspectionItem = value => {
    this.setState({ inspectionChildItems: value });
  };

  handleOk = async () => {
    const { tabKey, selectedInspectionConfig, selectedInspectionConfigIds } = this.state;
    const { tableData, inspectScope } = this.props;
    if (inspectScope === 'patrol' && tabKey === '1') {
      const ids = selectedInspectionConfig.map(({ id }) => id);
      const reduced = difference(selectedInspectionConfigIds, ids); // 减少的巡检配置
      const increased = difference(ids, selectedInspectionConfigIds); // 增加的巡检配置id
      let newTableData = cloneDeep(tableData);
      if (reduced.length) {
        // 将对象转为数组
        const flatArr = getTableData(newTableData);
        const newflatArr = flatArr.filter(item => {
          if (!item.inspectionConfigId) {
            return true;
          } else {
            if (reduced.includes(item.inspectionConfigId)) {
              return false;
            }
            return true;
          }
        });
        newTableData = convertArrToObj(newflatArr);
      }

      if (increased.length) {
        const increasedInspection = selectedInspectionConfig.filter(({ id }) =>
          increased.includes(id)
        );
        const newConfig = increasedInspection.map(
          ({ id, inspectType, inspectSubject, subTypeCode, checkItems, checkPoints }) => {
            let newItems = [];
            let newPoints = [];
            if (checkItems && checkItems.length) {
              newItems = checkItems.map(({ checkItemName, checkStdInfoList }) => {
                const newStand = checkStdInfoList.map(item1 => {
                  return {
                    ...item1,
                    standardId: shortid(),
                    standardTxt: {
                      dirty: false,
                      value: item1?.checkStd,
                    },
                    inspectionConfigId: id,
                    checkScenes: item1.checkScenes ?? InspectionScenarios.All,
                  };
                });
                return {
                  id: shortid(),
                  checkItemName: {
                    dirty: false,
                    name: 'checkItemName',
                    value: checkItemName,
                  },
                  standards: newStand,
                  inspectionConfigId: id,
                  inspectType,
                };
              });
            }
            if (checkPoints && checkPoints.length) {
              newPoints = checkPoints.map(item => {
                return {
                  ...item,
                  metaName: item.pointName,
                  inspectionConfigId: id,
                  inspectType,
                };
              });
            }
            return {
              inspectSubject,
              subTypeCode,
              checkItems: newItems,
              checkPoints: newPoints,
            };
          }
        );

        newConfig.forEach(({ inspectSubject, subTypeCode, checkItems, checkPoints }) => {
          if (!newTableData[inspectSubject]) {
            newTableData = {
              ...newTableData,
              [inspectSubject]: {
                [subTypeCode]: {
                  [INSPECTION_CONFIG_TYPE_KEY_MAP.POINT]: uniqByPoints(checkPoints),
                  [INSPECTION_CONFIG_TYPE_KEY_MAP.CUSTOM]: uniqByStandards(checkItems),
                },
              },
            };
          } else if (!newTableData[inspectSubject][subTypeCode]) {
            newTableData = {
              ...newTableData,
              [inspectSubject]: {
                ...newTableData[inspectSubject],
                [subTypeCode]: {
                  [INSPECTION_CONFIG_TYPE_KEY_MAP.POINT]: uniqByPoints(checkPoints),
                  [INSPECTION_CONFIG_TYPE_KEY_MAP.CUSTOM]: uniqByStandards(checkItems),
                },
              },
            };
          } else {
            newTableData = {
              ...newTableData,
              [inspectSubject]: {
                ...newTableData[inspectSubject],
                [subTypeCode]: {
                  [INSPECTION_CONFIG_TYPE_KEY_MAP.POINT]: newTableData[inspectSubject][subTypeCode]
                    .POINT
                    ? uniqByPoints([
                        ...newTableData[inspectSubject][subTypeCode].POINT,
                        ...checkPoints,
                      ])
                    : uniqByPoints(checkPoints),
                  [INSPECTION_CONFIG_TYPE_KEY_MAP.CUSTOM]: newTableData[inspectSubject][subTypeCode]
                    .CUSTOM
                    ? uniqByStandards([
                        ...newTableData[inspectSubject][subTypeCode].CUSTOM,
                        ...checkItems,
                      ])
                    : uniqByStandards(checkItems),
                },
              },
            };
          }
        });
      }

      this.setState({
        selectedInspectionConfigIds: ids,
      });
      this.props.setVisible(false, newTableData);
    } else {
      try {
        await this.basicConfigFormRef.current.props.form
          .validateFieldsAndScroll(err => {
            if (err) {
              this.setState(({ formOptions }) => {
                return {
                  formOptions: {
                    ...formOptions,
                    inspectConfigType: {
                      value: INSPECTION_CONFIG_TYPE_KEY_MAP.CUSTOM,
                      name: 'inspectConfigType',
                    },
                  },
                };
              });
            }
          })
          .catch(throws);
        this.validateStands();
      } catch (error) {}
    }
  };

  validateStands = async () => {
    const { inspectionChildRefMaps, selectedPointsMapInModal } = this.state;
    // 校验自定义巡检项
    try {
      if (
        inspectionChildRefMaps.size <= 0 &&
        !changeSelectedPoinesMapToList(selectedPointsMapInModal).length
      ) {
        message.error('至少添加一个检查项！');
        return;
      }
      if (inspectionChildRefMaps.size) {
        for (const [, standardsConfigRef] of inspectionChildRefMaps) {
          if (standardsConfigRef.current) {
            await standardsConfigRef.current.form
              .validateFieldsAndScroll(err => {
                if (err) {
                  this.setState(({ formOptions }) => {
                    return {
                      formOptions: {
                        ...formOptions,
                        inspectConfigType: {
                          value: INSPECTION_CONFIG_TYPE_KEY_MAP.CUSTOM,
                          name: 'inspectConfigType',
                        },
                      },
                    };
                  });
                }
              })
              .catch(throws);
            const values = standardsConfigRef.current.form.getFieldsValue();
            const _keys = Object.keys(values);
            if (_keys?.find(i => values[i].trim() === '')) {
              message.error('自项义项列表，请输入正确数值');
              // throw new Error('自项义项列表，请输入正确数值');
              return;
            }
            this.assembledData();
          }
        }
      } else {
        this.assembledData();
      }
    } catch (error) {
      console.log(error);
    }
  };

  assembledData = () => {
    const { inspectScope, tableData, fieldValues } = this.props;
    const { inspectionChildItems, formOptions, selectedPointsMapInModal } = this.state;
    let inspectSubject = formOptions.inspectSubject.value
      ? formOptions.inspectSubject.value
      : INSPECTION_TYPE_KEY_MAP.ENVIRONMENT;
    let subTypeCode = formOptions.subTypeCode.value;
    if (inspectSubject === INSPECTION_TYPE_KEY_MAP.ENVIRONMENT) {
      subTypeCode = formOptions.roomType.value;
    }
    let newTableData = cloneDeep(tableData);
    if (inspectScope === 'config') {
      inspectSubject = fieldValues.inspectSubject.value;
      if (inspectSubject === INSPECTION_TYPE_KEY_MAP.DEVICE) {
        subTypeCode = fieldValues.subTypeCode.value;
      }
      if (inspectSubject === INSPECTION_TYPE_KEY_MAP.ENVIRONMENT) {
        subTypeCode = fieldValues.roomType.value;
      }
    }
    if (inspectSubject && subTypeCode) {
      if (!newTableData[inspectSubject]) {
        newTableData = {
          ...newTableData,
          [inspectSubject]: {
            [subTypeCode]: {
              [INSPECTION_CONFIG_TYPE_KEY_MAP.CUSTOM]: uniqByStandards(inspectionChildItems),
            },
          },
        };
      } else if (!newTableData[inspectSubject][subTypeCode]) {
        newTableData = {
          ...newTableData,
          [inspectSubject]: {
            ...newTableData[inspectSubject],
            [subTypeCode]: {
              [INSPECTION_CONFIG_TYPE_KEY_MAP.CUSTOM]: uniqByStandards(inspectionChildItems),
            },
          },
        };
      } else {
        newTableData = {
          ...newTableData,
          [inspectSubject]: {
            ...newTableData[inspectSubject],
            [subTypeCode]: {
              ...newTableData[inspectSubject][subTypeCode],
              [INSPECTION_CONFIG_TYPE_KEY_MAP.CUSTOM]: newTableData[inspectSubject][subTypeCode]
                .CUSTOM
                ? uniqByStandards([
                    ...newTableData[inspectSubject][subTypeCode].CUSTOM,
                    ...inspectionChildItems,
                  ])
                : uniqByStandards(inspectionChildItems),
            },
          },
        };
      }
    }

    const mergedDate = mergeModalSelectedPointToTable({
      tabelData: newTableData,
      pointsMap: selectedPointsMapInModal,
    });
    this.props.setVisible(false, mergedDate);
  };

  updateFormOptions = changedFields => {
    this.setState(({ formOptions }) => {
      if (
        changedFields.inspectSubject &&
        changedFields.inspectSubject.value === INSPECTION_TYPE_KEY_MAP.ENVIRONMENT
      ) {
        return {
          formOptions: {
            ...formOptions,
            ...changedFields,
            inspectConfigType: {
              value: INSPECTION_CONFIG_TYPE_KEY_MAP.CUSTOM,
              name: 'inspectConfigType',
              dirty: false,
              touched: true,
            },
          },
        };
      }
      return {
        formOptions: {
          ...formOptions,
          ...changedFields,
        },
      };
    });
  };

  getOkButtonProps = () => {
    const { tabKey, selectedInspectionConfig } = this.state;
    let disabled = false;
    if (
      this.props.inspectScope === 'patrol' &&
      tabKey === '1' &&
      selectedInspectionConfig.length === 0
    ) {
      disabled = true;
    }
    return disabled;
  };

  render() {
    const { visible, setVisible, inspectScope, tableData, dataSource, taskType } = this.props;
    const {
      inspectionChildItems,
      formOptions,

      tabKey,
      selectedPointsMapInModal,
    } = this.state;

    return (
      <Modal
        visible={visible}
        destroyOnClose
        title="选择巡检项"
        width={960}
        bodyStyle={{
          height: 'calc(80vh - 56px)',
          overflowY: 'auto',
        }}
        afterClose={() => {
          this.setState({
            inspectionChildItems: [],
            inspectionChildRefMaps: new Map(),
            tabKey: 'custom',
          });
        }}
        maskClosable={false}
        onCancel={() => setVisible(false)}
        onOk={this.handleOk}
      >
        {inspectScope === 'patrol' && (
          <Tabs value={tabKey} onChange={key => this.setState({ tabKey: key })}>
            {/* <Tabs.TabPane tab="现有配置" key="1">
              <ExistingConfig
                selectedInspectionConfig={selectedInspectionConfig}
                setSelectedInspectionConfig={value => {
                  this.setState({
                    selectedInspectionConfig: value,
                  });
                }}
                taskType={taskType}
                patrolSupportCheckScenes={supportCheckScenes}
              />
            </Tabs.TabPane> */}
            <Tabs.TabPane key="2" tab="自定义">
              <CustomItem
                inspectScope={inspectScope}
                inspectionChildItems={inspectionChildItems}
                inspectionItem={this.inspectionItem}
                wrappedComponentRef={this.basicConfigFormRef}
                setInspectionItemMap={value => {
                  this.setState({ inspectionChildRefMaps: value });
                }}
                inspectionChildRefMaps={this.state.inspectionChildRefMaps}
                formOptions={formOptions}
                updateFormOptions={this.updateFormOptions}
                setVisible={setVisible}
                tableData={tableData}
                dataSource={dataSource}
                taskType={taskType}
                selectedPointsMapInModal={selectedPointsMapInModal}
                setSelectedPointsMapInmodal={pointsMap =>
                  this.setState({
                    selectedPointsMapInModal: mergePointsInModal(
                      pointsMap,
                      selectedPointsMapInModal
                    ),
                  })
                }
              />
            </Tabs.TabPane>
          </Tabs>
        )}

        {inspectScope === 'config' && (
          <CustomItem
            inspectScope={inspectScope}
            inspectionChildItems={inspectionChildItems}
            inspectionItem={this.inspectionItem}
            wrappedComponentRef={this.basicConfigFormRef}
            setInspectionItemMap={value => {
              this.setState({ inspectionChildRefMaps: value });
            }}
            inspectionChildRefMaps={this.state.inspectionChildRefMaps}
            formOptions={formOptions}
            updateFormOptions={this.updateFormOptions}
            setVisible={setVisible}
            tableData={tableData}
            fieldValues={this.props.fieldValues}
            dataSource={dataSource}
            selectedPointsMapInModal={selectedPointsMapInModal}
            setSelectedPointsMapInmodal={pointsMap =>
              this.setState({
                selectedPointsMapInModal: mergePointsInModal(pointsMap, selectedPointsMapInModal),
              })
            }
          />
        )}
      </Modal>
    );
  }
}

export default ConfigItemModal;

function mergeModalSelectedPointToTable({ pointsMap, tabelData }) {
  let newTableData = cloneDeep(tabelData);
  if (pointsMap) {
    Object.keys(pointsMap).forEach(code => {
      const points = pointsMap[code];
      if (!newTableData[INSPECTION_TYPE_KEY_MAP.DEVICE]) {
        newTableData = {
          ...newTableData,
          [INSPECTION_TYPE_KEY_MAP.DEVICE]: {
            [code]: {
              [INSPECTION_CONFIG_TYPE_KEY_MAP.POINT]: points,
            },
          },
        };
      } else if (!newTableData[INSPECTION_TYPE_KEY_MAP.DEVICE][code]) {
        newTableData = {
          ...newTableData,
          [INSPECTION_TYPE_KEY_MAP.DEVICE]: {
            ...newTableData[INSPECTION_TYPE_KEY_MAP.DEVICE],
            [code]: {
              [INSPECTION_CONFIG_TYPE_KEY_MAP.POINT]: points,
            },
          },
        };
      } else {
        newTableData = {
          ...newTableData,
          [INSPECTION_TYPE_KEY_MAP.DEVICE]: {
            ...newTableData[INSPECTION_TYPE_KEY_MAP.DEVICE],
            [code]: {
              ...newTableData[INSPECTION_TYPE_KEY_MAP.DEVICE][code],
              POINT: newTableData[INSPECTION_TYPE_KEY_MAP.DEVICE][code].POINT
                ? uniqByPoints([
                    ...newTableData[INSPECTION_TYPE_KEY_MAP.DEVICE][code].POINT,
                    ...points,
                  ])
                : points,
            },
          },
        };
      }
    });
  }

  return newTableData;
}

function mergePointsInModal(pointsMap, selectedPointsMapInModal) {
  const obj = {};
  if (pointsMap) {
    Object.keys(pointsMap).forEach(code => {
      if (!obj[code]) {
        obj[code] = pointsMap[code];
      } else {
        obj[code] = uniqBy([...obj[code], ...pointsMap[code]], 'code');
      }
    });
  }
  if (selectedPointsMapInModal) {
    Object.keys(selectedPointsMapInModal).forEach(code => {
      if (!obj[code]) {
        obj[code] = selectedPointsMapInModal[code];
      } else {
        obj[code] = uniqBy([...obj[code], ...selectedPointsMapInModal[code]], 'code');
      }
    });
  }
  return obj;
}
