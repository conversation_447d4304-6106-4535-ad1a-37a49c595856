import React, { useCallback, useEffect } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import { ApiSelect, Select } from '@galiojs/awesome-antd';

import { Button } from '@manyun/base-ui.ui.button';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { message } from '@manyun/base-ui.ui.message';

import {
  AssetClassificationApiTreeSelect,
  GutterWrapper,
  TinyTable,
} from '@manyun/dc-brain.legacy.components';
import {
  INSPECTION_TYPE_KEY_MAP,
  INSPECTION_TYPE_OPTIONS,
  INSPECTION_TYPE_TEXT_MAP,
} from '@manyun/dc-brain.legacy.constants/inspection';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  deleteConfigActionCreator,
  getDataActionCreator,
  inspectionConfigActions,
  resetSearchValuesActionCreator,
  setPaginationThenGetDataActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/inspectionConfigActions';
import { roomManageService } from '@manyun/dc-brain.legacy.services';

export function ExistingConfig({
  data,
  total,
  pageNum,
  pageSize,
  getData,
  setPagination,
  form,
  onReset,
  selectedInspectionConfig,
  setSelectedInspectionConfig,
  syncCommonData,
  ticketTypes,
  inspectSubject,
  taskType,
  updateSearchValues,
  deviceTypeMapping,
  patrolSupportCheckScenes,
}) {
  const { getFieldDecorator } = form;

  useEffect(() => {
    updateSearchValues({
      inspectType: {
        value: taskType,
        name: 'inspectType',
      },
      supportCheckScenes: {
        value: patrolSupportCheckScenes,
        name: 'supportCheckScenes',
      },
    });
  }, [updateSearchValues, taskType, patrolSupportCheckScenes]);

  useEffect(() => {
    getData();
  }, [getData]);

  useEffect(() => {
    syncCommonData({ strategy: { deviceCategory: 'IF_NULL', ticketTypes: 'IF_NULL' } });
  }, [syncCommonData]);

  useEffect(() => {
    return () => {
      updateSearchValues({
        inspectSubject: {
          value: null,
        },
        subTypeCode: {
          value: null,
        },
        roomType: {
          value: null,
        },
        supportCheckScenes: {
          value: null,
        },
      });
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const getSelectedRowKeys = () => {
    let keys = [];
    if (selectedInspectionConfig.length) {
      keys = selectedInspectionConfig.map(({ id }) => id);
    }
    return keys;
  };

  const paginationChangeHandler = useCallback(
    (current, size) => {
      setPagination({ pageNum: current, pageSize: size });
    },
    [setPagination]
  );

  return (
    <>
      <Form colon={false}>
        <Row>
          <Col xl={6}>
            <Form.Item label="巡检对象" labelCol={{ xl: 7 }} wrapperCol={{ xl: 7 }}>
              {getFieldDecorator('inspectSubject')(
                <Select allowClear requestOnDidMount style={{ width: 120 }}>
                  {INSPECTION_TYPE_OPTIONS.map(item => {
                    return (
                      <Select.Option key={item.value} value={item.value}>
                        {item.label}
                      </Select.Option>
                    );
                  })}
                </Select>
              )}
            </Form.Item>
          </Col>
          {inspectSubject.value !== INSPECTION_TYPE_KEY_MAP.ENVIRONMENT && (
            <Col xl={6}>
              <Form.Item label="巡检对象类型" labelCol={{ xl: 9 }} wrapperCol={{ xl: 15 }}>
                {getFieldDecorator('subTypeCode')(
                  <AssetClassificationApiTreeSelect
                    dataType={['snDevice']}
                    category="categorycode"
                    disabledDepths={[0, 1]}
                    requestOnDidMount
                    disabled={inspectSubject.value ? false : true}
                    allowClear
                    style={{ width: 200 }}
                  />
                )}
              </Form.Item>
            </Col>
          )}
          {inspectSubject.value === INSPECTION_TYPE_KEY_MAP.ENVIRONMENT && (
            <Col xl={6}>
              <Form.Item label="巡检对象类型" labelCol={{ xl: 9 }} wrapperCol={{ xl: 15 }}>
                {getFieldDecorator('roomType')(
                  <ApiSelect
                    showSearch
                    trigger="onDidMount"
                    fieldNames={{ label: 'value', value: 'key' }}
                    dataService={async () => {
                      const data = await roomManageService.fetchRoomType();
                      if (data) {
                        return Promise.resolve(data);
                      } else {
                        return Promise.resolve([]);
                      }
                    }}
                    allowClear
                    style={{ width: 200 }}
                  />
                )}
              </Form.Item>
            </Col>
          )}
          <Col xl={12}>
            <Form.Item>
              <GutterWrapper style={{ textAlign: 'right', lineHeight: '40px' }}>
                <Button
                  type="primary"
                  onClick={() => {
                    getData(true);
                  }}
                >
                  搜索
                </Button>
                <Button
                  onClick={() => {
                    onReset();
                  }}
                >
                  重置
                </Button>
              </GutterWrapper>
            </Form.Item>
          </Col>
        </Row>
      </Form>

      <TinyTable
        rowKey="id"
        selectRowsSpreadPage={true}
        columns={getColumns({ ticketTypes, deviceTypeMapping })}
        dataSource={data}
        rowSelection={{
          selectedRowKeys: getSelectedRowKeys(),
          selectedRows: selectedInspectionConfig,
          onChange: (keys, rows) => {
            // 巡检工单选择巡检配置时，只可以选择同一种巡检类型的巡检配置
            let tmp = false;
            rows.forEach(item => {
              if (taskType && item.inspectType !== taskType) {
                tmp = true;
              }
            });
            if (tmp) {
              message.error(
                `只可以选择${
                  ticketTypes && ticketTypes.normalizedList[taskType]
                    ? ticketTypes.normalizedList[taskType].taskValue
                    : taskType
                }类型的配置`
              );
            } else {
              setSelectedInspectionConfig(rows);
            }
          },
          getCheckboxProps: record => {
            return {
              disabled: (() => {
                if (!taskType) {
                  return false;
                }
                if (record.inspectType !== taskType) {
                  return true;
                }
                return false;
              })(),
            };
          },
        }}
        pagination={{
          total,
          current: pageNum,
          pageSize,
          onChange: paginationChangeHandler,
        }}
      />
    </>
  );
}

const mapStateToProps = ({
  inspectionConfig: {
    data,
    total,
    pagination: { pageNum, pageSize },
    searchValues,
  },
  common: { ticketTypes, deviceCategory },
}) => {
  return {
    data,
    total,
    pageNum,
    pageSize,
    ...searchValues,
    ticketTypes,
    deviceTypeMapping: deviceCategory ? deviceCategory.normalizedList : {},
  };
};
const mapDispatchToProps = {
  getData: getDataActionCreator,
  setPagination: setPaginationThenGetDataActionCreator,
  deleteConfig: deleteConfigActionCreator,
  updateSearchValues: inspectionConfigActions.updateSearchValues,
  onReset: resetSearchValuesActionCreator,
  syncCommonData: syncCommonDataActionCreator,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(
  Form.create({
    onFieldsChange(props, changedFields) {
      props.updateSearchValues(changedFields);
    },
    mapPropsToFields(props) {
      return {
        inspectSubject: Form.createFormField(props.inspectSubject),
        subTypeCode: Form.createFormField(props.subTypeCode),
        roomType: Form.createFormField(props.roomType),
        supportCheckScenes: Form.createFormField(props.supportCheckScenes),
      };
    },
  })(ExistingConfig)
);

const getColumns = ({ ticketTypes, deviceTypeMapping }) => [
  {
    title: '巡检配置项名称',
    dataIndex: 'configName',
  },
  {
    title: '巡检类型',
    dataIndex: 'inspectType',
    render: inspectType => {
      if (!ticketTypes || !ticketTypes.normalizedList || !ticketTypes.normalizedList[inspectType]) {
        return inspectType;
      }
      return ticketTypes.normalizedList[inspectType].taskValue;
    },
  },
  {
    title: '巡检对象',
    dataIndex: 'inspectSubject',
    width: 126,
    render: inspectSubject => {
      if (INSPECTION_TYPE_TEXT_MAP[inspectSubject]) {
        return INSPECTION_TYPE_TEXT_MAP[inspectSubject];
      }
      return inspectSubject;
    },
  },
  {
    title: '巡检对象类型',
    dataIndex: 'subTypeName',
    render: (txt, { inspectSubject, subTypeCode }) => {
      if (inspectSubject === INSPECTION_TYPE_KEY_MAP.ENVIRONMENT) {
        return txt;
      }
      const deviceTarget = deviceTypeMapping[subTypeCode];
      if (deviceTarget) {
        return deviceTarget.metaName;
      }
      return txt;
    },
  },

  {
    title: '巡检项条数',
    dataIndex: 'itemCount',
  },
];
