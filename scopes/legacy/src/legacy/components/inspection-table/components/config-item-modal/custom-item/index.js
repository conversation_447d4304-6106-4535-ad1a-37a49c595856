import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import { Select } from '@galiojs/awesome-antd';
import styled from 'styled-components';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Empty } from '@manyun/base-ui.ui.empty';
import { Tabs } from '@manyun/base-ui.ui.tabs';

import {
  AssetClassificationApiTreeSelect,
  GutterWrapper,
  PointsSelectModalButton,
  TinyTable,
} from '@manyun/dc-brain.legacy.components';
import InspectionList from '@manyun/dc-brain.legacy.components/inspection-list';
import {
  INSPECTION_CONFIG_TYPE_KEY_MAP,
  INSPECTION_CONFIG_TYPE_OPTIONS,
  INSPECTION_TYPE_KEY_MAP,
  INSPECTION_TYPE_OPTIONS,
} from '@manyun/dc-brain.legacy.constants/inspection';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';

import { changeSelectedPoinesMapToList } from '../../../utils';

export const StyledTitleIcon = styled.span`
  width: 4px;
  height: 1em;
  border-radius: 4px;
  background-color: var(--color-reference);
  vertical-align: middle;
`;

const formItemLayout = {
  labelCol: {
    xl: 3,
  },
  wrapperCol: {
    xl: 21,
  },
};

class CustomItem extends Component {
  componentDidMount() {
    this.props.syncCommonData({
      strategy: { deviceCategory: 'IF_NULL', roomTypes: 'IF_NULL', ticketTypes: 'IF_NULL' },
    });
  }

  getInspectTypeDisabled = () => {
    const { dataSource, inspectScope, taskType } = this.props;
    return (
      (inspectScope === 'config' && dataSource.length) ||
      (inspectScope === 'patrol' && taskType !== undefined)
    );
  };

  getCustomerlistDisplay = () => {
    const {
      form: { getFieldsValue },
      formOptions,
    } = this.props;
    const { inspectConfigType, inspectSubject } = getFieldsValue();
    return (
      inspectConfigType === INSPECTION_CONFIG_TYPE_KEY_MAP.CUSTOM &&
      (formOptions.inspectSubject.value || inspectSubject)
    );
  };

  getInspectObjFormItemDisplay = () => {
    const {
      form: { getFieldsValue },
      inspectScope,
    } = this.props;
    const { inspectSubject, inspectConfigType } = getFieldsValue();
    return (
      inspectScope === 'patrol' &&
      inspectSubject === INSPECTION_TYPE_KEY_MAP.DEVICE &&
      inspectConfigType === INSPECTION_CONFIG_TYPE_KEY_MAP.CUSTOM
    );
  };

  render() {
    const {
      form: { getFieldDecorator, getFieldValue },
      inspectionChildItems,
      inspectScope,
      roomTypeList,
      inspectTypeTree,
      selectedPointsMapInModal,
      deviceCategory,
    } = this.props;
    let inspectMode = INSPECTION_CONFIG_TYPE_OPTIONS;

    if (getFieldValue('inspectSubject') === INSPECTION_TYPE_KEY_MAP.ENVIRONMENT) {
      inspectMode = INSPECTION_CONFIG_TYPE_OPTIONS.filter(
        ({ value }) => value !== INSPECTION_CONFIG_TYPE_KEY_MAP.POINT
      );
    }

    return (
      <>
        {inspectScope === 'config' &&
          this.props.formOptions.inspectType.value === INSPECTION_TYPE_KEY_MAP.ENVIRONMENT && (
            <Alert
              showIcon
              message="当前巡检类型为空间类型，检查项类型默认为自定义巡检项。"
              type="info"
              closable
            />
          )}

        <Form colon={false}>
          {inspectScope === 'patrol' && (
            <Form.Item label="巡检类型" {...formItemLayout}>
              {getFieldDecorator('inspectType', {
                rules: [{ required: true, message: '巡检为必填项！' }],
              })(
                <Select
                  trigger="onDidMount"
                  style={{ width: 200 }}
                  getPopupContainer={triggerNode => triggerNode.parentNode}
                  disabled={this.getInspectTypeDisabled()}
                >
                  {inspectTypeTree.map(item => {
                    return (
                      <Select.Option key={item.taskType} value={item.taskType}>
                        {item.taskValue}
                      </Select.Option>
                    );
                  })}
                </Select>
              )}
            </Form.Item>
          )}
          {inspectScope === 'patrol' && (
            <Form.Item label="巡检对象" {...formItemLayout}>
              {getFieldDecorator('inspectSubject', {
                rules: [{ required: true, message: '巡检对象为必填项！' }],
              })(
                <Select
                  allowClear
                  trigger="onDidMount"
                  style={{ width: 200 }}
                  getPopupContainer={triggerNode => triggerNode.parentNode}
                >
                  {INSPECTION_TYPE_OPTIONS.map(item => {
                    return (
                      <Select.Option key={item.value} value={item.value}>
                        {item.label}
                      </Select.Option>
                    );
                  })}
                </Select>
              )}
            </Form.Item>
          )}

          {inspectScope === 'config' &&
            this.props.formOptions.inspectSubject.value === INSPECTION_TYPE_KEY_MAP.ENVIRONMENT && (
              <Form.Item label="巡检项类型" {...formItemLayout}>
                自定义巡检项
              </Form.Item>
            )}

          {/* 配置模式下，inspectSubject 有值 或者 工单模式下，inspectSubject 有值*/}
          {(this.props.formOptions.inspectSubject.value || getFieldValue('inspectSubject')) && (
            <>
              <Form.Item
                style={{ marginBottom: '8px' }}
                labelCol={{ xl: 0 }}
                wrapperCol={{ xl: 24 }}
              >
                {getFieldDecorator('inspectConfigType')(
                  <Tabs
                    type="card"
                    disabled={
                      getFieldValue('inspectSubject') === INSPECTION_TYPE_KEY_MAP.ENVIRONMENT
                        ? true
                        : false
                    }
                    activeKey={getFieldValue('inspectConfigType')}
                  >
                    {inspectMode.map(({ label, value }) => {
                      return <Tabs.TabPane tab={label} key={value}></Tabs.TabPane>;
                    })}
                  </Tabs>
                )}
              </Form.Item>

              <div style={{ display: this.getInspectObjFormItemDisplay() ? 'block' : 'none' }}>
                <Form.Item label="巡检对象类型" {...formItemLayout}>
                  {getFieldDecorator('subTypeCode', {
                    rules: [
                      {
                        required: this.getInspectObjFormItemDisplay(),
                        message: '巡检对象类型为必填项！',
                      },
                    ],
                  })(
                    <AssetClassificationApiTreeSelect
                      dataType={['snDevice']}
                      category="categorycode"
                      disabledDepths={[0, 1]}
                      requestOnDidMount
                      style={{ width: 200 }}
                    />
                  )}
                </Form.Item>
              </div>
              {inspectScope === 'patrol' &&
                getFieldValue('inspectSubject') === INSPECTION_TYPE_KEY_MAP.ENVIRONMENT && (
                  <Form.Item label="巡检对象类型" {...formItemLayout}>
                    {getFieldDecorator('roomType', {
                      rules: [
                        {
                          required: inspectionChildItems.length ? true : false,
                          message: '巡检对象类型为必选项！',
                        },
                      ],
                    })(
                      <Select trigger="onDidMount" style={{ width: 200 }}>
                        {roomTypeList.map(item => {
                          return (
                            <Select.Option key={item.value} value={item.value}>
                              {item.label}
                            </Select.Option>
                          );
                        })}
                      </Select>
                    )}
                  </Form.Item>
                )}

              {getFieldValue('inspectConfigType') === INSPECTION_CONFIG_TYPE_KEY_MAP.POINT && (
                <Form.Item>
                  {getFieldDecorator('points')(
                    <>
                      <PointsSelectModalButton
                        text="+测点检查项"
                        title="添加测点巡检项"
                        displayDevice={
                          inspectScope === 'config' &&
                          this.props.formOptions &&
                          this.props.formOptions.subTypeCode &&
                          this.props.formOptions.subTypeCode.value
                            ? [this.props.formOptions.subTypeCode.value]
                            : null
                        }
                        visibleLoadTypes={['snDevice']}
                        onOk={({ pointsMap }) => {
                          let newMap = {};
                          Object.keys(pointsMap).forEach(code => {
                            const points = pointsMap[code].map(item => {
                              return {
                                ...item,
                                isMeterRead: 0,
                                inspectType: getFieldValue('inspectType'),
                              };
                            });
                            newMap[code] = points;
                          });
                          this.props.setSelectedPointsMapInmodal(newMap);
                        }}
                      />
                      {selectedPointsMapInModal && (
                        <TinyTable
                          rowKey="deviceTypeAndCode"
                          dataSource={changeSelectedPoinesMapToList(selectedPointsMapInModal)}
                          columns={getColumns({
                            deviceNormalizedList: deviceCategory
                              ? deviceCategory.normalizedList
                              : null,
                          })}
                        />
                      )}
                      {!selectedPointsMapInModal && (
                        <GutterWrapper style={{ height: '100%' }} flex center>
                          <Empty
                            image={<img alt="暂无数据" src="/images/empty.png" />}
                            description="当前暂无数据，点击添加一个检查项"
                          />
                        </GutterWrapper>
                      )}
                    </>
                  )}
                </Form.Item>
              )}
            </>
          )}
        </Form>
        <div style={{ display: this.getCustomerlistDisplay() ? 'block' : 'none' }}>
          <InspectionList
            header={
              <GutterWrapper flex alignItems="center" size=".5em">
                <StyledTitleIcon />
                <div style={{ flex: 1 }}>巡检项列表</div>
              </GutterWrapper>
            }
            setInspectionItem={this.props.inspectionItem}
            data={inspectionChildItems}
            setStandardsRefMap={this.props.setInspectionItemMap}
            standardsRefMap={this.props.inspectionChildRefMaps}
            inspectType={this.props.formOptions.inspectType.value}
          />
        </div>
      </>
    );
  }
}

const mapStateToProps = ({ common: { roomTypes, ticketTypes, deviceCategory } }) => {
  let roomTypeList = [];
  if (roomTypes) {
    roomTypeList = Object.keys(roomTypes).map(key => {
      return {
        label: roomTypes[key],
        value: key,
      };
    });
  }
  let inspectTypeTree = [];
  if (ticketTypes && ticketTypes.treeList) {
    ticketTypes.treeList.forEach(({ taskType, children }) => {
      if (taskType === 'INSPECTION') {
        inspectTypeTree = children;
      }
    });
  }
  return {
    roomTypeList,
    inspectTypeTree,
    deviceCategory,
  };
};

const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(
  Form.create({
    name: 'custom-item',
    onFieldsChange(props, changedFields) {
      props.updateFormOptions(changedFields);
    },
    mapPropsToFields(props) {
      return {
        inspectType: Form.createFormField(props.formOptions.inspectType),
        inspectConfigType: Form.createFormField(props.formOptions.inspectConfigType),
        subTypeCode: Form.createFormField(props.formOptions.subTypeCode),
        roomType: Form.createFormField(props.formOptions.roomType),
        points: Form.createFormField(props.formOptions.points),
        inspectSubject: Form.createFormField(props.formOptions.inspectSubject),
      };
    },
  })(CustomItem)
);

const getColumns = ({ deviceNormalizedList }) => [
  {
    title: '设备类型',
    dataIndex: 'deviceType',
    render: deviceType => deviceNormalizedList[deviceType]?.metaName,
  },
  {
    title: '测点名称',
    dataIndex: 'name',
    render: (name, record) => `${record.typeText}-${name}`,
  },
];
