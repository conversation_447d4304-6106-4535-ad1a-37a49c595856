import styled from 'styled-components';

import { Descriptions } from '@manyun/base-ui.ui.descriptions';

export const StyledDescriptions = styled(Descriptions)`
  &.manyun-descriptions-bordered {
    .manyun-descriptions-item-label {
      text-align: center;
    }

    .manyun-descriptions-item-content {
      padding: 0;
    }
  }
`;

const CONTENT_DESCRIPTIONS_ITEM_LABEL_WIDTH = 120;
const CONTENT_DESCRIPTIONS_ITEM_CONTENT_WIDTH = 100;
export const CONTENT_DESCRIPTIONS_ITEM_WIDTH =
  CONTENT_DESCRIPTIONS_ITEM_LABEL_WIDTH + CONTENT_DESCRIPTIONS_ITEM_CONTENT_WIDTH;

export const StyledContentDescriptions = styled(Descriptions)`
  &.manyun-descriptions-bordered {
    .manyun-descriptions-view {
      border-style: solid;
      border-color: var(--border-color-split);
      border-top-width: 0;
      border-right-width: ${({ hasBorderRight }) => (hasBorderRight ? '1px' : '0')};
      border-bottom-width: ${({ hasBorderBottom }) => (hasBorderBottom ? '1px' : '0')};
      border-left-width: 0;
    }

    .manyun-descriptions-item-label {
      min-width: ${CONTENT_DESCRIPTIONS_ITEM_LABEL_WIDTH}px;
      text-align: center;
    }

    .manyun-descriptions-item-content {
      min-width: ${CONTENT_DESCRIPTIONS_ITEM_CONTENT_WIDTH}px;
      text-align: center;
    }
  }
`;

export const PduNameWrapper = styled.div`
  text-align: center;
  height: 48px;
  line-height: 48px;
  border-style: solid;
  border-color: var(--border-color-split);
  border-top-width: 0;
  border-right-width: ${({ hasBorderRight }) => (hasBorderRight ? '1px' : '0')};
  border-bottom-width: 1px;
  border-left-width: 0;
`;
