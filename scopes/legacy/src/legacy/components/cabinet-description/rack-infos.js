import React from 'react';

import { PointsLineModalButton } from '@manyun/monitoring.chart.points-line';

import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';

import { StyledContentDescriptions, StyledDescriptions } from './styled';

/**
 * @typedef {import('@manyun/dc-brain.legacy.utils/device').PointData & { pointGuids: object[] }} EnhancedPointData
 */

/**
 * 机柜信息
 *
 * @param {object} props
 * @param {{ name: string } | null} props.gridType 机柜类型
 * @param {string | null} props.customer 签约客户
 * @param {string | null} props.signedPower 签约功率
 * @param {string | null} props.ratedPower 设计功率
 * @param {EnhancedPointData | null} props.rackPowerSum 总功率
 * @param {EnhancedPointData | null} props.rackLoadRateAvg 平均IT负载率
 * @param {string} props.idcTag 机房
 * @returns
 */
export default function RackInfos({
  gridType,
  customer,
  signedPower,
  ratedPower,
  rackPowerSum,
  rackLoadRateAvg,
  idcTag,
}) {
  return (
    <StyledDescriptions bordered layout="vertical" column={1} size="small">
      <StyledDescriptions.Item label="机柜信息">
        <StyledContentDescriptions bordered column={2}>
          <StyledContentDescriptions.Item label="机柜类型">
            {gridType?.name || BLANK_PLACEHOLDER}
          </StyledContentDescriptions.Item>
          <StyledContentDescriptions.Item label="签约客户">
            {customer || BLANK_PLACEHOLDER}
          </StyledContentDescriptions.Item>
          <StyledContentDescriptions.Item label="机柜签约功率">
            {signedPower ? `${signedPower}kW` : BLANK_PLACEHOLDER}
          </StyledContentDescriptions.Item>
          <StyledContentDescriptions.Item label="机柜设计功率">
            {ratedPower ? `${ratedPower}kW` : BLANK_PLACEHOLDER}
          </StyledContentDescriptions.Item>
          <StyledContentDescriptions.Item label="机柜总功率">
            <PointsLineModalButton
              idcTag={idcTag}
              btnText={rackPowerSum.formattedText}
              modalText="机柜总功率"
              pointGuids={rackPowerSum.pointGuids}
            />
          </StyledContentDescriptions.Item>
          <StyledContentDescriptions.Item label="机柜平均IT负载率">
            <PointsLineModalButton
              idcTag={idcTag}
              btnText={rackLoadRateAvg.formattedText}
              modalText="机柜平均IT负载率"
              pointGuids={rackLoadRateAvg.pointGuids}
            />
          </StyledContentDescriptions.Item>
        </StyledContentDescriptions>
      </StyledDescriptions.Item>
    </StyledDescriptions>
  );
}
