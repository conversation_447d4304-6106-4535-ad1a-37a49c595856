import React from 'react';
import { Link } from 'react-router-dom';

import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { PointsLineModalButton } from '@manyun/monitoring.chart.points-line';
import { PointsStateLineModalButton } from '@manyun/monitoring.chart.points-state-line';
import { generateDeviceRecordRoutePath } from '@manyun/resource-hub.route.resource-routes';

import { StatusText } from '@manyun/dc-brain.legacy.components';

import { StyledContentDescriptions } from './styled';

export default function PduInfos({
  hasBorderRight,
  hasBorderBottom,
  showTag = false,
  pduDevice,
  taskSubType,
  getMonitorData,
  idcTag: idc,
}) {
  const onOffState = getMonitorData(pduDevice, {
    pointType: ConfigUtil.constants.pointCodes.PDU_ON_OFF_STATE,
    reflected: true,
    valueMappingIncluded: true,
  });
  const current = getMonitorData(pduDevice, {
    pointType: ConfigUtil.constants.pointCodes.PDU_CURRENT,
    formatted: true,
  });
  const voltage = getMonitorData(pduDevice, {
    pointType: ConfigUtil.constants.pointCodes.PDU_VOLTAGE,
    formatted: true,
  });
  const powerSum = getMonitorData(pduDevice, {
    pointType: ConfigUtil.constants.pointCodes.PDU_POWER_SUM,
    formatted: true,
  });

  return (
    <StyledContentDescriptions
      hasBorderRight={hasBorderRight}
      hasBorderBottom={hasBorderBottom}
      bordered
      column={1}
      size="small"
    >
      {showTag && (
        <StyledContentDescriptions.Item labelStyle={{ width: 126 }} label="支路开关名称">
          <Link target="_blank" to={generateDeviceRecordRoutePath({ guid: pduDevice.deviceGuid })}>
            {pduDevice.deviceName}
          </Link>
        </StyledContentDescriptions.Item>
      )}
      <StyledContentDescriptions.Item labelStyle={{ width: 126 }} label="支路开关状态">
        <PointsStateLineModalButton
          modalText={pduDevice.deviceName}
          idcTag={idc}
          pointGuids={[
            {
              serieName: '支路开关状态',
              deviceGuid: pduDevice.deviceGuid,
              pointCode: onOffState.pointCode,
              valueMapping: onOffState.valueMapping,
            },
          ]}
          btnText={
            <StatusText background status={getOnOffStatus(onOffState.originalValue, taskSubType)}>
              {onOffState.value.NAME}
            </StatusText>
          }
        />
      </StyledContentDescriptions.Item>
      <StyledContentDescriptions.Item labelStyle={{ width: 126 }} label="支路开关电流">
        <PointsLineModalButton
          idcTag={idc}
          btnText={current.formattedText}
          modalText={pduDevice.deviceName}
          pointGuids={[
            {
              pointCode: current.pointCode,
              deviceGuid: pduDevice.deviceGuid,
              deviceType: pduDevice.deviceType,
            },
          ]}
          seriesOption={[{ name: '支路开关电流' }]}
        />
      </StyledContentDescriptions.Item>
      <StyledContentDescriptions.Item labelStyle={{ width: 126 }} label="支路开关电压">
        <PointsLineModalButton
          idcTag={idc}
          btnText={voltage.formattedText}
          modalText={pduDevice.deviceName}
          pointGuids={[
            {
              pointCode: voltage.pointCode,
              deviceGuid: pduDevice.deviceGuid,
              deviceType: pduDevice.deviceType,
            },
          ]}
          seriesOption={[{ name: '支路开关电压' }]}
        />
      </StyledContentDescriptions.Item>
      <StyledContentDescriptions.Item labelStyle={{ width: 126 }} label="支路开关功率">
        <PointsLineModalButton
          idcTag={idc}
          btnText={powerSum.formattedText}
          modalText={pduDevice.deviceName}
          pointGuids={[
            {
              pointCode: powerSum.pointCode,
              deviceGuid: pduDevice.deviceGuid,
              deviceType: pduDevice.deviceType,
            },
          ]}
          seriesOption={[{ name: '支路开关功率' }]}
        />
      </StyledContentDescriptions.Item>
    </StyledContentDescriptions>
  );
}

/**
 * @REFACTOR @Jerry W. 这个应该通过配置实现
 *
 * @param {*} value
 * @param {*} taskType
 * @returns
 */
function getOnOffStatus(value, taskType) {
  if (taskType === 'POWER_OFF') {
    if (value === 0) {
      return 'normal';
    }
    if (value === 1) {
      return 'alarm';
    }
  } else {
    if (value === 1) {
      return 'normal';
    }
    if (value === 0) {
      return 'alarm';
    }
  }
}
