import React from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import { Col, Row } from '@manyun/base-ui.ui.grid';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { generateDeviceRecordRoutePath } from '@manyun/resource-hub.route.resource-routes';

import { GutterWrapper, TinyEmpty } from '@manyun/dc-brain.legacy.components';
import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';
import { fetchCabinetDetail } from '@manyun/dc-brain.legacy.redux/actions/cabinetActions';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  cancelTicketRealtimeData,
  getTicketRealtimeData,
} from '@manyun/dc-brain.legacy.redux/actions/ticketActions';
import { getSpaceGuidMap } from '@manyun/dc-brain.legacy.utils';
import { generateGetDeviceMonitoringData } from '@manyun/dc-brain.legacy.utils/device';

import PduInfos from './pdu-infos';
import RackInfos from './rack-infos';
import {
  CONTENT_DESCRIPTIONS_ITEM_WIDTH,
  PduNameWrapper,
  StyledContentDescriptions,
  StyledDescriptions,
} from './styled';

class CabinetDescription extends React.Component {
  componentDidMount() {
    const { gridInfo, pointGuids, deviceTypes } = this.props;
    const { idc } = getSpaceGuidMap(gridInfo.gridGuid);
    this.props.fetchCabinetDetail({ gridGuid: gridInfo.gridGuid });
    this.props.getTicketRealtimeData({ idc, pointGuids });
    this.props.syncCommonData({ strategy: { deviceTypesPointsDefinition: deviceTypes } });
  }

  componentWillUnmount() {
    this.props.cancelTicketRealtimeData();
  }

  render() {
    const {
      taskSubType,
      cabinetMess,
      rackPowerSum,
      rackLoadRateAvg,
      rppAs,
      rppBs,
      pduDevicesA,
      pduDevicesB,
      getMonitorData,
      gridInfo,
    } = this.props;
    const { idc } = getSpaceGuidMap(gridInfo.gridGuid);

    return (
      <>
        <RackInfos
          {...cabinetMess}
          rackPowerSum={rackPowerSum}
          rackLoadRateAvg={rackLoadRateAvg}
          idcTag={idc}
        />
        <StyledDescriptions
          style={{ marginTop: -2 }}
          bordered
          layout="vertical"
          column={1}
          size="small"
        >
          <StyledDescriptions.Item label="列头柜信息">
            <StyledContentDescriptions bordered layout="vertical" column={2}>
              <StyledContentDescriptions.Item label="A路列头柜">
                <StyledContentDescriptions bordered>
                  <StyledContentDescriptions.Item labelStyle={{ width: 126 }} label="列头柜名称">
                    {rppAs.length > 0 ? (
                      <GutterWrapper size="2px">
                        {rppAs.map(rppA => (
                          <Link
                            key={rppA.deviceGuid}
                            to={generateDeviceRecordRoutePath({
                              guid: rppA.deviceGuid,
                            })}
                          >
                            {rppA.deviceName}
                          </Link>
                        ))}
                      </GutterWrapper>
                    ) : (
                      BLANK_PLACEHOLDER
                    )}
                  </StyledContentDescriptions.Item>
                </StyledContentDescriptions>
              </StyledContentDescriptions.Item>
              <StyledContentDescriptions.Item label="B路列头柜">
                <StyledContentDescriptions bordered>
                  <StyledContentDescriptions.Item labelStyle={{ width: 126 }} label="列头柜名称">
                    {rppBs.length > 0 ? (
                      <GutterWrapper size="2px">
                        {rppBs.map(rppB => (
                          <Link
                            key={rppB.deviceGuid}
                            to={generateDeviceRecordRoutePath({
                              guid: rppB.deviceGuid,
                            })}
                          >
                            {rppB.deviceName}
                          </Link>
                        ))}
                      </GutterWrapper>
                    ) : (
                      BLANK_PLACEHOLDER
                    )}
                  </StyledContentDescriptions.Item>
                </StyledContentDescriptions>
              </StyledContentDescriptions.Item>
            </StyledContentDescriptions>
          </StyledDescriptions.Item>
        </StyledDescriptions>
        <StyledDescriptions
          style={{ marginTop: -2 }}
          bordered
          layout="vertical"
          column={1}
          size="small"
        >
          <StyledDescriptions.Item label="空开信息">
            <StyledContentDescriptions bordered layout="vertical" column={2}>
              <StyledContentDescriptions.Item label="A路列头柜对应空开">
                {pduDevicesA?.length === 1 && (
                  <PduInfos
                    idcTag={idc}
                    showTag
                    pduDevice={pduDevicesA[0]}
                    taskSubType={taskSubType}
                    getMonitorData={getMonitorData}
                  />
                )}
                {pduDevicesA?.length > 1 && (
                  <StyledContentDescriptions bordered layout="vertical" column={1}>
                    <StyledContentDescriptions.Item label="支路开关名称">
                      <Row style={{ minWidth: CONTENT_DESCRIPTIONS_ITEM_WIDTH * 2 }}>
                        {pduDevicesA?.map((item, index) => {
                          const hasBorderRight = index % 2 === 0;
                          const hasBorderBottom = index < 2;

                          return (
                            <Col key={item.deviceGuid} span={12}>
                              <PduNameWrapper hasBorderRight={hasBorderRight}>
                                <Link
                                  target="_blank"
                                  to={generateDeviceRecordRoutePath({ guid: item.deviceGuid })}
                                >
                                  {item.deviceName}
                                </Link>
                              </PduNameWrapper>
                              <PduInfos
                                idcTag={idc}
                                hasBorderRight={hasBorderRight}
                                hasBorderBottom={hasBorderBottom}
                                pduDevice={item}
                                taskSubType={taskSubType}
                                getMonitorData={getMonitorData}
                              />
                            </Col>
                          );
                        })}
                      </Row>
                    </StyledContentDescriptions.Item>
                  </StyledContentDescriptions>
                )}
              </StyledContentDescriptions.Item>
              <StyledContentDescriptions.Item label="B路列头柜对应空开">
                {pduDevicesB?.length === 1 && (
                  <PduInfos
                    idcTag={idc}
                    showTag
                    pduDevice={pduDevicesB[0]}
                    taskSubType={taskSubType}
                    getMonitorData={getMonitorData}
                  />
                )}
                {pduDevicesB?.length > 1 && (
                  <StyledContentDescriptions bordered layout="vertical" column={1}>
                    <StyledContentDescriptions.Item label="支路开关名称">
                      <Row style={{ minWidth: CONTENT_DESCRIPTIONS_ITEM_WIDTH * 2 }}>
                        {pduDevicesB?.map((item, index) => {
                          const hasBorderRight = index % 2 === 0;
                          const hasBorderBottom = index < 2;

                          return (
                            <Col key={item.deviceGuid} span={12}>
                              <PduNameWrapper hasBorderRight={hasBorderRight}>
                                <Link
                                  target="_blank"
                                  to={generateDeviceRecordRoutePath({ guid: item.deviceGuid })}
                                >
                                  {item.deviceName}
                                </Link>
                              </PduNameWrapper>
                              <PduInfos
                                idcTag={idc}
                                hasBorderRight={hasBorderRight}
                                hasBorderBottom={hasBorderBottom}
                                pduDevice={item}
                                taskSubType={taskSubType}
                                getMonitorData={getMonitorData}
                              />
                            </Col>
                          );
                        })}
                      </Row>
                    </StyledContentDescriptions.Item>
                  </StyledContentDescriptions>
                )}
              </StyledContentDescriptions.Item>
            </StyledContentDescriptions>
          </StyledDescriptions.Item>
        </StyledDescriptions>
        {pduDevicesA?.length === 0 && pduDevicesB?.length === 0 && (
          <TinyEmpty
            style={{
              margin: '-2px 0 0',
              padding: 16,
              borderStyle: 'solid',
              borderColor: 'var(--border-color-split)',
              borderTopWidth: 0,
              borderRightWidth: 1,
              borderBottomWidth: 1,
              borderLeftWidth: 1,
            }}
          />
        )}
      </>
    );
  }
}

const mapStateToProps = (
  {
    config: { configMap },
    'monitoring.subscriptions': { devicesRealtimeData, devicesAlarmsData },
    cabinetManage: { cabinetMess },
  },
  { gridInfo: { gridGuid, gridTag }, pduDevicesA, pduDevicesB }
) => {
  const { pointsDefinitionMap, concretePointCodeMappings } = configMap.current;
  const configUtil = new ConfigUtil(configMap.current, { defaultSpaceGuid: gridGuid });
  const getMonitorData = generateGetDeviceMonitoringData(
    devicesRealtimeData,
    devicesAlarmsData,
    pointsDefinitionMap,
    concretePointCodeMappings
  );

  const gridDevice = {
    deviceGuid: gridGuid,
    deviceType: configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_GRID),
  };

  const rackPowerSumPointCode = configUtil.getPointCode(
    gridDevice.deviceType,
    ConfigUtil.constants.pointCodes.PDU_POWER_SUM
  );
  const rackPowerSum = getMonitorData(gridDevice, {
    formatted: true,
    hardCodedPointCode: rackPowerSumPointCode,
  });
  rackPowerSum.pointGuids = [
    {
      serieName: `${gridTag} 机柜总功率`,
      deviceGuid: gridDevice.deviceGuid,
      pointCode: rackPowerSumPointCode,
      unit: rackPowerSum.unit,
    },
  ];

  const rackLoadRateAvgPointCode = configUtil.getPointCode(
    gridDevice.deviceType,
    ConfigUtil.constants.pointCodes.PDU_LOAD_RATE_AVG
  );
  const rackLoadRateAvg = getMonitorData(gridDevice, {
    formatted: true,
    hardCodedPointCode: rackLoadRateAvgPointCode,
  });
  rackLoadRateAvg.pointGuids = [
    {
      serieName: `${gridTag} 机列平均IT负荷率`,
      deviceGuid: gridDevice.deviceGuid,
      pointCode: rackLoadRateAvgPointCode,
      unit: rackLoadRateAvg.unit,
    },
  ];

  let pointGuids = [
    { deviceGuid: gridGuid, pointCode: rackPowerSumPointCode },
    { deviceGuid: gridGuid, pointCode: rackLoadRateAvgPointCode },
  ];
  const deviceTypes = [gridDevice.deviceType];
  [...(pduDevicesA || []), ...(pduDevicesB || [])].forEach(pduDevice => {
    if (!deviceTypes.some(deviceType => pduDevice.deviceType === deviceType)) {
      deviceTypes.push(pduDevice.deviceType);
    }

    const pduOnOffState = configUtil.getPointCode(
      pduDevice.deviceType,
      ConfigUtil.constants.pointCodes.PDU_ON_OFF_STATE
    );
    const pduCurrent = configUtil.getPointCode(
      pduDevice.deviceType,
      ConfigUtil.constants.pointCodes.PDU_CURRENT
    );
    const pduVoltage = configUtil.getPointCode(
      pduDevice.deviceType,
      ConfigUtil.constants.pointCodes.PDU_VOLTAGE
    );
    const pduPowerSum = configUtil.getPointCode(
      pduDevice.deviceType,
      ConfigUtil.constants.pointCodes.PDU_POWER_SUM
    );

    pointGuids = [
      ...pointGuids,
      ...[pduOnOffState, pduCurrent, pduVoltage, pduPowerSum].map(pointCode => ({
        deviceType: pduDevice.deviceType,
        deviceGuid: pduDevice.deviceGuid,
        pointCode,
      })),
    ];
  });

  return {
    cabinetMess,
    pointGuids,
    deviceTypes,
    rackPowerSum,
    rackLoadRateAvg,
    configUtil,
    getMonitorData,
  };
};

const mapDispatchToProps = {
  getTicketRealtimeData: getTicketRealtimeData,
  cancelTicketRealtimeData: cancelTicketRealtimeData,
  syncCommonData: syncCommonDataActionCreator,
  fetchCabinetDetail,
};

export default connect(mapStateToProps, mapDispatchToProps)(CabinetDescription);
