import React from 'react';

import { Cascader } from '@manyun/base-ui.ui.cascader';

import { DataService } from '@manyun/dc-brain.legacy.components/renderProps';

/**
 * @typedef { import('antd-3/lib/cascader').CascaderProps } CascaderProps
 *
 * @typedef CustomizedProps
 * @property {boolean} [requestOnDidMount=false]
 * @property {() => Promise<{ treeList: any[] }>} dataService
 *
 * @typedef { CascaderProps & CustomizedProps } Props
 */

/**
 * 封装于 antd 的 Cascader 组件
 * @augments { React.Component<Props> }
 */
export class ApiCascader extends React.Component {
  /**
   * @type {Partial<CustomizedProps>}
   */
  static defaultProps = {
    requestOnDidMount: false,
  };

  /**
   * @type { React.RefObject<DataService> }
   */
  _dataServiceRef = React.createRef();

  _focusHandler = evt => {
    this._dataServiceRef.current.getData();

    const { onFocus } = this.props;
    if (typeof onFocus !== 'function') {
      return;
    }
    onFocus(evt);
  };

  render() {
    const { requestOnDidMount, dataService, ...rest } = this.props;

    return (
      <DataService
        ref={this._dataServiceRef}
        requestOnDidMount={requestOnDidMount}
        dataService={dataService}
      >
        {({ data = { treeList: [] } }) => (
          <Cascader options={data.treeList} {...rest} onFocus={this._focusHandler} />
        )}
      </DataService>
    );
  }
}

export default ApiCascader;
