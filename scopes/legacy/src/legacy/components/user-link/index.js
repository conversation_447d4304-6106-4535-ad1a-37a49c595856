import React from 'react';
import { Link } from 'react-router-dom';

import { generateUserProfileRoutePath } from '@manyun/auth-hub.route.auth-routes';

/**
 * 封装于 `react-router-dom` 的 `Link` 组件。
 * @deprecated 请使用 auth-hub 下的 ui 组件 User
 * @typedef {object} Props
 * @property {number} props.userId 用户Id
 * @property {string} props.userName  用户名称
 * @property {boolean} props.useNativeLink  用户名称

 */

/**
 * @augments {React.PureComponent<Props>}
 */
export default class UserLink extends React.PureComponent {
  render() {
    const { userId, userName, useNativeLink = true } = this.props;

    if (userName === 'SYSTEM') {
      return <span>系统</span>;
    }

    return (
      <>
        {userName &&
          userId &&
          (useNativeLink ? (
            <a href={generateUserProfileRoutePath({ id: userId })} target="_blank" rel="noreferrer">
              {userName}
            </a>
          ) : (
            <Link to={generateUserProfileRoutePath({ id: userId })}>{userName}</Link>
          ))}

        {userName && !userId && <span>{userName}</span>}
        {!userName && '--'}
      </>
    );
  }
}
