import { ExclamationCircleOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import get from 'lodash.get';
import React from 'react';

import { Badge } from '@manyun/base-ui.ui.badge';
import { Card } from '@manyun/base-ui.ui.card';
import { Space } from '@manyun/base-ui.ui.space';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { AuthInfo, AuthStatus } from '@manyun/sentry.model.entrance-guard-card-management';
import type { ChangeAuthType } from '@manyun/ticket.model.entrance-guard-auth';
import { EntranceGuardCardAuthGroupText } from '@manyun/ticket.ui.entrance-guard-card-auth-group-text';

export type EntranceGuardCardLicenseInformationProps = {
  authInfoList:
    | AuthInfo[]
    | { blockTag: string; authId: number; originAuthId: number; changeType: ChangeAuthType }[];
  idcTag: string;
  showExpire?: boolean;
};
export const statusMappings: Record<
  string,
  { status?: 'success' | 'error' | 'default'; text: string; color?: 'geekblue' | 'cyan' }
> = {
  success: { status: 'success', text: '成功' },
  error: { status: 'error', text: '失败' },
  ADD: { color: 'geekblue', text: '新增授权' },
  DELETE: { status: 'error', text: '删除授权' },
  CHANGE: { color: 'cyan', text: '变更授权' },
  UN_CHANG: { status: 'default', text: '未变更' },
};

export type InfoCardProps = {
  authInfoList: {
    blockTag: string;
    authId: string;
    status: 'success' | 'error' | 'ADD' | 'DELETE' | 'CHANGE' | 'UN_CHANG';
    tooltipText: string;
  };
};

export function transferAuthInfoList(
  authInfoList:
    | AuthInfo[]
    | { authId: number; blockGuid: string; blockTag: string; isSuccess: AuthStatus }[]
    | {
        blockTag: string;
        authIdList: number[];
        changeType: ChangeAuthType;
        originAuthIdList: number[];
      }[]
    | { blockTag: string; authId: number; originAuthId: number; changeType: ChangeAuthType }[]
) {
  const list = authInfoList.map(item => {
    let result: {
      blockTag: string;
      authId: number;
      status?: 'success' | 'error' | 'ADD' | 'DELETE' | 'CHANGE' | 'UN_CHANG';
      originAuthId?: number;
    } = { blockTag: '', authId: 0, status: 'success', originAuthId: 0 };
    if ('originAuthIdList' in item) {
      result = {
        ...item,
        blockTag: item.blockTag,
        authId: item.authIdList[0],
        status: item.changeType,
        originAuthId: item.originAuthIdList[0],
      };
    }
    if ('changeType' in item && 'originAuthId' in item) {
      result = {
        ...item,
        blockTag: item.blockTag,
        authId: item.authId,
        status: item.changeType,
        originAuthId: item.originAuthId,
      };
    }
    if ('isSuccess' in item || 'status' in item) {
      result = {
        ...item,
        blockTag: item.blockTag,
        authId: item.authId,
        status: 'status' in item ? item.status : item.isSuccess,
      };
    }
    if ('oldEntranceCardNo' in item) {
      result = {
        ...item,
        authId: get(item, 'authId')!,
        status: undefined,
        blockTag: item.blockTag,
      };
    }
    return result;
  });
  return list;
}

export function EntranceGuardCardLicenseInformation({
  idcTag,
  authInfoList,
  showExpire,
}: EntranceGuardCardLicenseInformationProps) {
  return (
    <div
      style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fill, minmax(231px, 1fr))',
        gridGap: 16,
      }}
    >
      {transferAuthInfoList(authInfoList).map(item => {
        const effectTime = get(item, 'effectTime');
        const isReplace = !!get(item, 'oldEntranceCardNo');
        const expired = effectTime ? dayjs().isAfter(effectTime) : undefined;
        const name: string = get(item, 'applyName', '');
        const authText =
          idcTag && item.blockTag ? (
            <EntranceGuardCardAuthGroupText
              blockGuid={`${idcTag}.${item.blockTag}`}
              code={item.authId}
            />
          ) : (
            '--'
          );

        return (
          <Card
            key={item.blockTag}
            style={{ height: '102px', minWidth: 231 }}
            bodyStyle={{ padding: showExpire ? 24 : '8px 12px' }}
          >
            {showExpire ? (
              <Space direction="vertical" style={{ width: '100%' }}>
                <Space style={{ justifyContent: 'space-between', width: '100%' }}>
                  <Space direction="horizontal">
                    <Typography.Title style={{ marginBottom: 0 }} level={5}>
                      {item.blockTag}楼
                    </Typography.Title>
                    {typeof expired === 'boolean' && (
                      <Tag color={expired ? 'error' : 'success'}>{!expired ? '有效' : '过期'}</Tag>
                    )}
                  </Space>
                  {item.status && (
                    <Space>
                      <Badge
                        status={statusMappings[item.status]?.status}
                        color={statusMappings[item.status]?.color}
                      />
                      {statusMappings[item.status].text}
                    </Space>
                  )}
                </Space>
                <Typography.Text
                  style={{ width: 200 }}
                  type="secondary"
                  ellipsis={{ tooltip: authText }}
                >
                  {authText}
                </Typography.Text>
              </Space>
            ) : (
              <Space direction="vertical" style={{ width: '100%' }}>
                <Space style={{ justifyContent: 'space-between', width: '100%' }}>
                  <Tooltip title={name}>
                    <Typography.Title style={{ marginBottom: 0 }} level={5}>
                      {name ? (name.length > 4 ? `${name.slice(0, 3)}...` : name) : '--'}
                    </Typography.Title>
                  </Tooltip>
                  {!isReplace && (
                    <Typography.Title style={{ marginBottom: 0 }} level={5}>
                      {item.blockTag}楼
                    </Typography.Title>
                  )}
                  <Space>
                    <Typography.Title
                      style={{ marginBottom: 0, maxWidth: 90 }}
                      level={5}
                      ellipsis={{ tooltip: true }}
                    >
                      {get(item, 'entranceCardNo')}
                    </Typography.Title>
                    {isReplace && (
                      <Tooltip title={`原门禁卡号：${get(item, 'oldEntranceCardNo')}`}>
                        <ExclamationCircleOutlined />
                      </Tooltip>
                    )}
                  </Space>
                </Space>
                <Space style={{ justifyContent: 'space-between', width: '100%' }}>
                  {isReplace && (
                    <Typography.Title style={{ marginBottom: 0 }} level={5}>
                      {item.blockTag}楼
                    </Typography.Title>
                  )}
                  <Space>
                    <Tooltip title={authText}>
                      <Typography.Text
                        style={{
                          maxWidth: item.status === 'CHANGE' && item.originAuthId ? 100 : 120,
                        }}
                        type="secondary"
                        ellipsis
                      >
                        {authText}
                      </Typography.Text>
                    </Tooltip>
                    {item.status === 'CHANGE' && item.originAuthId && (
                      <Tooltip
                        title={
                          <span>
                            由
                            <EntranceGuardCardAuthGroupText
                              blockGuid={`${idcTag}.${item.blockTag}`}
                              code={item.originAuthId}
                            />
                            变更为
                            <EntranceGuardCardAuthGroupText
                              blockGuid={`${idcTag}.${item.blockTag}`}
                              code={item.authId}
                            />
                          </span>
                        }
                      >
                        <ExclamationCircleOutlined />
                      </Tooltip>
                    )}
                  </Space>
                  {item.status && (
                    <Space>
                      <Badge
                        status={statusMappings[item.status]?.status}
                        color={statusMappings[item.status]?.color}
                      />
                      {statusMappings[item.status].text}
                    </Space>
                  )}
                </Space>
                <Typography.Text type="secondary">
                  {effectTime ? dayjs(effectTime).format('YYYY-MM-DD HH:mm:ss') : '--'}
                </Typography.Text>
              </Space>
            )}
          </Card>
        );
      })}
    </div>
  );
}
