import React from 'react';
import { <PERSON> } from 'react-router-dom';

import { generateBPMRoutePath } from '@manyun/bpm.route.bpm-routes';

import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';

/**
 * @typedef {object} Props
 * @property {number | string} props.id 审批Id
 * @property {string} props.text  link 显示的文案
 */

/**
 * 封装于 `react-router-dom` 的 `Link` 组件。
 * @param {Props}
 */
export default function ApproveLink({ id, text = id, ...resp }) {
  if (!id || id === '0') {
    return BLANK_PLACEHOLDER;
  }
  return (
    <Link
      to={generateBPMRoutePath({
        id,
      })}
      {...resp}
    >
      {text}
    </Link>
  );
}
