/**
 * @typedef {import('@/biz-types/common').FileInfo} FileInfo
 * @typedef {import('antd-3/lib/upload/interface').UploadFile<FileInfo>} UploadFile
 *
 * @typedef TinyUploadProps
 * @property {React.Ref<Upload} [forwardedRef]
 * @property {string} [accept] 接受上传的文件类型
 * @property {string} [listType] 上传列表的内建样式
 * @property {boolean} [allowDelete] 是否允许删除 `fileList` 中的文件
 * @property {boolean} [showUploadBtn] 是否展示上传按钮
 * @property {number} [maxFileSize=100] 限制文件大小（Mega byte）
 * @property {number} [maxFileCount=Number.POSITIVE_INFINITY] 限制文件数量
 * @property {FileInfo[]} [fileList]
 * @property {FileInfo[]} [value] 文件列表（功能同 `fileList`，为方便使用 `getFieldDecorator()(<TinyUpload />)`）
 * @property {(fileList: FileInfo[]) => void} [onAfterUpload]
 * @property {(file: FileInfo) => void} [onRemove]
 * @property {(fileList: FileInfo[]) => void} [onChange] 当 `onAfterUpload, onRemove` 触发后会触发此回调方法（方便使用 `getFieldDecorator()(<TinyUpload />)`）
 */
import React, { useRef, useState } from 'react';

import LeftOutlined from '@ant-design/icons/es/icons/LeftOutlined';
import PlusOutlined from '@ant-design/icons/es/icons/PlusOutlined';
import RightOutlined from '@ant-design/icons/es/icons/RightOutlined';
import styled from 'styled-components';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Typography } from '@manyun/base-ui.ui.typography';

import { Upload } from '@manyun/dc-brain.ui.upload';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';
import * as mimeTypes from '@manyun/dc-brain.legacy.constants/mimeTypes';
import { uploadFile } from '@manyun/dc-brain.legacy.services/eventCenterService';
import * as mimeTypesUtil from '@manyun/dc-brain.legacy.utils/mimeTypes';

/**
 * 文件上传、预览
 *
 * 作为 `Form Item Control` 使用
 * @example
 * ```jsx
 * <Form.Item label="...">
 *   {getFieldDecorator('...')(<TinyUpload showUploadBtn />)}
 * </Form.Item>
 * ```
 *
 * @param {TinyUploadProps} props
 */
export function TinyUpload({
  style,
  className,
  forwardedRef,
  accept,
  showAccept = false,
  listType,
  allowDelete,
  showUploadBtn,
  maxFileSize = 100,
  maxFileCount = Number.POSITIVE_INFINITY,
  fileList = [],
  value = fileList,
  onAfterUpload = noop,
  onRemove = noop,
  onChange = noop,
}) {
  const [loading, setLoading] = useState(false);
  const [preview, setPreview] = useState({ visible: false });

  const isVideoType = preview.type === mimeTypes.VIDEO_TYPE;

  /**
   * @type {React.RefObject<HTMLVideoElement}
   */
  const videoRef = useRef();

  const fileInfos = value.map(item => {
    return {
      ...item,
      uid: item.filePath,
      name: item.fileName,
      url: `/api/dcom/file/download?filePath=${item.filePath}`,
    };
  });

  const curPreviewIdx = fileInfos.findIndex(fileInfo => fileInfo.uid === preview.uid);

  /**
   * @param {UploadFile} file
   */
  const beforeUploadHandler = file => {
    const fileSizeMB = file.size / 1024 / 1024;
    if (fileSizeMB > maxFileSize) {
      message.error(`上传文件不得超过 ${maxFileSize}MB`);
      return false;
    }

    const fd = new FormData();
    fd.append('file', file);
    accept && fd.append('accept', accept);
    uploadAsync(fd);

    return false;
  };

  const uploadAsync = async fd => {
    setLoading(true);

    /**
     * @type {{ response: { data: FileInfo[]; total: number }; error: string }}
     */
    const { response, error } = await uploadFile(fd);

    setLoading(false);
    if (error) {
      message.error(error);
      return;
    }
    const files = [...fileInfos, ...response.data];
    onAfterUpload(files);
    onChange(files);
  };

  /**
   * @param {UploadFile} file
   */
  const removeHandler = file => {
    if (!allowDelete) {
      return;
    }

    onRemove(file);

    const files = fileInfos.filter(item => item.uid !== file.uid);
    onChange(files);
  };

  /**
   *
   * @param {UploadFile} file
   */
  const previewHandler = file => {
    setPreview({
      visible: true,
      uid: file.uid,
      type: mimeTypesUtil.guessMimeType(file.fileFormat),
      name: file.name,
      src: file.url,
    });
  };

  const releaseVideoSafelyAsync = async () => {
    if (isVideoType && videoRef.current) {
      videoRef.current.pause();
      // https://googlechrome.github.io/samples/picture-in-picture/
      if (videoRef.current === document.pictureInPictureElement) {
        await document.exitPictureInPicture();
      }
      videoRef.current.src = '';
      videoRef.current.load();
    }
  };

  const onPreviewPrevious = async () => {
    if (curPreviewIdx - 1 <= -1) {
      return;
    }

    const prevPreviewFileInfo = fileInfos[curPreviewIdx - 1];

    await releaseVideoSafelyAsync();

    setPreview({
      visible: true,
      uid: prevPreviewFileInfo.uid,
      type: mimeTypesUtil.guessMimeType(prevPreviewFileInfo.fileFormat),
      name: prevPreviewFileInfo.name,
      src: prevPreviewFileInfo.url,
    });
  };

  const onPreviewNext = async () => {
    if (curPreviewIdx + 1 >= fileInfos.length) {
      return;
    }

    const nxtPreviewFileInfo = fileInfos[curPreviewIdx + 1];

    await releaseVideoSafelyAsync();

    setPreview({
      visible: true,
      uid: nxtPreviewFileInfo.uid,
      type: mimeTypesUtil.guessMimeType(nxtPreviewFileInfo.fileFormat),
      name: nxtPreviewFileInfo.name,
      src: nxtPreviewFileInfo.url,
    });
  };

  const VIDEO_MAX_WIDTH = window.innerWidth > 1599 ? 1280 : 960;
  const VIDEO_MAX_HEIGHT = window.innerWidth > 1599 ? 720 : 540;
  const maxObjectHeight = VIDEO_MAX_HEIGHT - 24 * 2;

  return (
    <>
      <StyledUpload
        ref={forwardedRef}
        style={style}
        className={className}
        accept={accept}
        listType={listType}
        allowDelete={allowDelete}
        disabled={loading}
        fileList={fileInfos}
        onPreview={previewHandler}
        beforeUpload={beforeUploadHandler}
        onRemove={removeHandler}
      >
        {showUploadBtn && fileInfos.length < maxFileCount ? (
          listType !== 'picture-card' ? (
            <>
              <Button loading={loading} type="primary">
                上传
              </Button>
              <p style={{ marginTop: '10px' }}>
                {showAccept && accept && (
                  <Typography.Text type="secondary">支持扩展名：{accept}</Typography.Text>
                )}
              </p>
            </>
          ) : (
            accept && (
              <div>
                <PlusOutlined style={{ color: 'var(--color-grey)', fontSize: 20 }} />
                <div style={{ color: 'var(--color-grey)', marginTop: 8, fontSize: 8 }}>
                  格式{accept},大小≤{maxFileSize}MB
                </div>
              </div>
            )
          )
        ) : null}
      </StyledUpload>
      <Modal
        bodyStyle={{
          position: 'relative',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: VIDEO_MAX_HEIGHT,
        }}
        className="tiny-upload_preview-modal"
        width={VIDEO_MAX_WIDTH + 24 * 2}
        visible={preview.visible}
        footer={null}
        title={preview.name}
        onCancel={async () => {
          await releaseVideoSafelyAsync();
          setPreview(p => ({ ...p, src: '', visible: false }));
        }}
      >
        {fileInfos.length > 1 && curPreviewIdx > 0 && (
          <PreviewNavButton direction="left" onClick={onPreviewPrevious} />
        )}
        {preview.type === mimeTypes.IMAGE_TYPE && (
          <img
            style={{ width: VIDEO_MAX_WIDTH, maxHeight: maxObjectHeight }}
            alt={preview.name}
            src={preview.src}
          />
        )}
        {isVideoType && (
          <video
            ref={videoRef}
            style={{ outline: 'none', width: VIDEO_MAX_WIDTH, maxHeight: maxObjectHeight }}
            controls
            src={preview.src}
          />
        )}
        {preview.type === mimeTypes.UNKNOW_BINARY_FILE && (
          <GutterWrapper style={{ width: VIDEO_MAX_WIDTH, height: maxObjectHeight }} flex center>
            <span>
              文件格式暂不支持预览，请下载（
              <a rel="noopener noreferrer" href={preview.src}>
                {preview.name}
              </a>
              ）后打开查看。
            </span>
          </GutterWrapper>
        )}
        {fileInfos.length > 1 && curPreviewIdx < fileInfos.length - 1 && (
          <PreviewNavButton direction="right" onClick={onPreviewNext} />
        )}
      </Modal>
    </>
  );
}

/**
 * @type {React.ForwardRefExoticComponent<React.RefAttributes<Upload> & TinyUploadProps>}
 */
export const ForwardRefTinyUpload = React.forwardRef((props, ref) => (
  <TinyUpload forwardedRef={ref} {...props} />
));

export default ForwardRefTinyUpload;

const noop = () => {};

const PREVIEW_NAV_BUTTON_FONT_SIZE = 68;

const StyledUpload = styled(Upload)`
  .manyun-upload-list-item-card-actions {
    display: ${({ allowDelete }) => (allowDelete ? 'inline-block' : 'none')};
  }
`;

/**
 * 预览导航按钮（上一个文件，下一个文件）
 * @param {object} props
 * @param {'left'|'right'} props.direction
 * @param {() => void} props.onClick
 */
function PreviewNavButton({ direction, onClick }) {
  const DirectionIcon = direction === 'left' ? LeftOutlined : RightOutlined;

  return (
    <div
      style={{
        zIndex: 1,
        position: 'absolute',
        // To prevent the <video />'s controls from being covered.
        top: `calc(50% - ${PREVIEW_NAV_BUTTON_FONT_SIZE}px)`,
        [direction]: 0,
        padding: 8,
        width: PREVIEW_NAV_BUTTON_FONT_SIZE + 16,
      }}
      className="tiny-upload_preview-modal--nav-button"
    >
      <div
        style={{
          position: 'relative',
          // To move the icon down because the icon's `marginTop` style doesn't work somehow.
          height: `calc(50% - ${PREVIEW_NAV_BUTTON_FONT_SIZE / 2}px)`,
        }}
      ></div>
      <DirectionIcon
        style={{
          fontSize: PREVIEW_NAV_BUTTON_FONT_SIZE,
        }}
        onClick={onClick}
      />
    </div>
  );
}
