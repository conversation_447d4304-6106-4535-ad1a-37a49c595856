import React, { useEffect, useRef, useState } from 'react';
import { connect } from 'react-redux';
import { useLatest } from 'react-use';

import { Button } from '@manyun/base-ui.ui.button';

import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  getConfigCheckItemsActionCreator,
  operationCheckConfigActions,
} from '@manyun/dc-brain.legacy.redux/actions/operationCheckConfigActions';

import TicketModal from './components/ticket-modal';
import { getCheckItemChildrens } from './utils';

/**
 * 封装于 `antd` 的 `Button` 和 `Modal` 组件。
 * @typedef {object} Props
 * @property {string} props.title Button 的文案
 * @property {string} props.taskType  工单类型
 * @property {string} props.taskSubType  工单子类型
 * @property {Array} [props.customStep]  自定义步骤
 * @property {(function) => void} [props.customFooter] 自定义footer
 * @property {Boolean} props.showNextStep  是否展示下一步Button
 * @property {function} props.getCurrentStepAndVisible  当前弹框状态和前步骤
 * @property {function} props.onFinish  完成
 * @property {function} props.onFilture   失败
 */

/**
 * @augments {React.PureComponent<Props>}
 */
function TicketCheckListModalButton({
  title,
  taskType,
  taskSubType,
  customStep,
  customFooter,
  beforeValues,
  setBeforeValues,
  itemsBefore,
  afterValues,
  setAfterValues,
  itemsAfter,
  getConfigCheckItems,
  formInBeforeCreateOptions,
  formInAfterCreateOptions,
  showNextStep,
  getCurrentStepAndVisible,
  modalTitle,
  syncCommonData,
  onFinish,
  restDetail,
  onFilture,
  getVisibleFromExternal,
}) {
  const checkItemBeforeFormRef = useRef();
  const checkItemAfterFormRef = useRef();
  const [isPassedCheckAfter, setIsPassedCheckAfter] = useState(true);
  const [modalVisible, updateModalVisible] = useState(false);
  const [step, setSteps] = useState(0);
  const setBeforeValuesRef = useLatest(setBeforeValues);
  const setAfterValuesRef = useLatest(setAfterValues);

  const validateStandsInAfter = async () => {
    if (itemsAfter.length) {
      checkItemAfterFormRef.current.form.validateFields((err, values) => {
        if (err) {
          setIsPassedCheckAfter(false);
          return;
        }
        setIsPassedCheckAfter(true);
        onFinish().then(result => {
          if (result) {
            updateModalVisible(false);
          }
        });
      });
    } else {
      onFinish().then(result => {
        if (result) {
          updateModalVisible(false);
        }
      });
    }
  };
  const completeSubmission = () => {
    if (itemsAfter.length) {
      validateStandsInAfter();
      return;
    }
    onFinish().then(result => {
      if (result) {
        updateModalVisible(false);
      }
    });
  };
  useEffect(() => {
    syncCommonData({ strategy: { ticketTypes: 'IF_NULL' } });
  }, [syncCommonData]);

  useEffect(() => {
    if (!modalVisible) {
      restDetail(); // 清空数据
      setSteps(0);
      setBeforeValuesRef.current(null);
      setAfterValuesRef.current(null);
    }
  }, [modalVisible, restDetail, setAfterValuesRef, setBeforeValuesRef]);

  useEffect(() => {
    if (taskType && taskSubType && modalVisible) {
      getConfigCheckItems({
        taskType,
        taskSubType,
        successCb: () => {
          setSteps(count => count + 1);
        },
      });
    }
  }, [getConfigCheckItems, taskType, taskSubType, modalVisible]);

  useEffect(() => {
    if (!beforeValues) {
      setBeforeValuesRef.current(formInBeforeCreateOptions);
    }
  }, [formInBeforeCreateOptions, beforeValues, setBeforeValuesRef]);

  useEffect(() => {
    if (!afterValues) {
      setAfterValuesRef.current(formInAfterCreateOptions);
    }
  }, [formInAfterCreateOptions, afterValues, setAfterValuesRef]);

  return (
    <>
      <Button
        type="link"
        style={{ padding: 0, height: 'auto' }}
        onClick={() => {
          if (getVisibleFromExternal) {
            getCurrentStepAndVisible(modalVisible, step, updateModalVisible);
          } else {
            updateModalVisible(true);
            getCurrentStepAndVisible(modalVisible, step);
          }
        }}
      >
        {title}
      </Button>
      {modalVisible && (
        <TicketModal
          modalVisible={modalVisible}
          updateModalVisible={visible => updateModalVisible(visible)}
          step={step}
          setSteps={setSteps}
          customStep={customStep}
          customFooter={customFooter}
          showNextStep={showNextStep}
          beforeValues={beforeValues}
          setBeforeValues={setBeforeValues}
          itemsBefore={itemsBefore}
          itemsAfter={itemsAfter}
          setAfterValues={setAfterValues}
          afterValues={afterValues}
          getCurrentStepAndVisible={getCurrentStepAndVisible}
          modalTitle={modalTitle}
          checkItemBeforeFormRef={checkItemBeforeFormRef}
          checkItemAfterFormRef={checkItemAfterFormRef}
          isPassedCheckAfter={isPassedCheckAfter}
          completeSubmission={completeSubmission}
          onFilture={onFilture}
        />
      )}
    </>
  );
}

const mapStateToProps = (
  { operationCheckConfig: { detail }, common: { ticketTypes } },
  { taskSubType }
) => {
  let formInBeforeCreateOptions = {};
  let formInAfterCreateOptions = {};
  if (detail && detail.itemsInBefore) {
    detail.itemsInBefore.forEach(({ id, itemNormal }) => {
      const tmp = `${id}-${itemNormal}`;
      formInBeforeCreateOptions = {
        ...formInBeforeCreateOptions,
        [tmp]: {
          name: tmp,
          value: null,
        },
      };
    });
  }
  if (detail && detail.itemsInAfter) {
    detail.itemsInAfter.forEach(({ id, itemNormal }) => {
      const tmp = `${id}-${itemNormal}`;
      formInAfterCreateOptions = {
        ...formInAfterCreateOptions,
        [tmp]: {
          name: tmp,
          value: null,
        },
      };
    });
  }
  const txt =
    ticketTypes && ticketTypes.normalizedList && ticketTypes.normalizedList[taskSubType]
      ? ticketTypes.normalizedList[taskSubType].taskValue
      : '';
  return {
    itemsBefore: detail.itemsInBefore ? getCheckItemChildrens(detail.itemsInBefore) : [],
    itemsAfter: detail.itemsInAfter ? getCheckItemChildrens(detail.itemsInAfter) : [],
    formInBeforeCreateOptions: formInBeforeCreateOptions,
    formInAfterCreateOptions: formInAfterCreateOptions,
    modalTitle: `${txt}操作检查`,
  };
};

const mapDispatchToProps = {
  updateSearchValues: operationCheckConfigActions.updateSearchValues,
  getConfigCheckItems: getConfigCheckItemsActionCreator,
  syncCommonData: syncCommonDataActionCreator,
  restDetail: operationCheckConfigActions.restDetail,
};

export default connect(mapStateToProps, mapDispatchToProps)(TicketCheckListModalButton);
