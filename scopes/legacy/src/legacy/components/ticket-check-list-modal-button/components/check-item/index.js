import React, { forwardRef, useImperativeHandle } from 'react';

import Form from '@ant-design/compatible/es/form';
import styled from 'styled-components';

import { List } from '@manyun/base-ui.ui.list';

import { GutterWrapper, TinyCard } from '@manyun/dc-brain.legacy.components';
import { YES_OR_NO_KEY_MAP } from '@manyun/dc-brain.legacy.constants';

import CheckItem from './check-form-item';

const StyledList = styled(List)`
  .manyun-list-item {
    padding-top: 0;
    padding-bottom: 0;
  }
`;

const StyledFormItem = styled(Form.Item)`
  margin-bottom: 0;
`;

const StandardForm = forwardRef(({ form, title, checkData }, ref) => {
  useImperativeHandle(ref, () => ({
    form,
  }));

  const { getFieldDecorator } = form;

  return (
    <TinyCard title={title}>
      <Form>
        <GutterWrapper mode="vertical">
          {checkData &&
            checkData.map(({ itemName, children }) => {
              return (
                <StyledList
                  bordered
                  key={itemName}
                  dataSource={children}
                  header={<div>{itemName}</div>}
                  renderItem={item => {
                    const { id, itemMethod, itemNormal, inputData, dataRange } = item;
                    return (
                      <List.Item>
                        <StyledFormItem key={id}>
                          {getFieldDecorator(`${id}-${itemNormal}`, {
                            rules: [
                              {
                                required: true,
                                validator: (_, value, callback) => {
                                  let lower = null;
                                  let upper = null;
                                  if (dataRange) {
                                    upper = dataRange.split('$$')[0];
                                    lower = dataRange.split('$$')[1];
                                  }
                                  if (!value || !value.checked) {
                                    callback('必选项！');
                                  } else if (
                                    inputData === YES_OR_NO_KEY_MAP.YES &&
                                    value.num !== 0 &&
                                    !value.num
                                  ) {
                                    callback('数值必填！');
                                  } else if (
                                    (lower !== '' &&
                                      !Number.isNaN(Number(lower)) &&
                                      value.num < Number(lower)) ||
                                    (upper !== '' &&
                                      !Number.isNaN(Number(lower)) &&
                                      value.num > Number(upper))
                                  ) {
                                    callback('输入值不在预期范围内，请输入正确的数值！');
                                  } else {
                                    callback();
                                  }
                                },
                              },
                            ],
                          })(
                            <CheckItem
                              itemMethod={itemMethod}
                              itemNormal={itemNormal}
                              inputData={inputData}
                            />
                          )}
                        </StyledFormItem>
                      </List.Item>
                    );
                  }}
                />
              );
            })}
        </GutterWrapper>
      </Form>
    </TinyCard>
  );
});

const createOpts = {
  name: 'checkItems',
  onFieldsChange(props, changedFields) {
    props.setFormValues({ ...props.formValues, ...changedFields });
  },
  mapPropsToFields(props) {
    const { formValues } = props;
    let createFormField = {};
    Object.keys(formValues).forEach(key => {
      createFormField = {
        ...createFormField,
        [key]: Form.createFormField(formValues[key]),
      };
    });
    return createFormField;
  },
};

const EhancedCheckItemBefore = Form.create(createOpts)(StandardForm);

export default EhancedCheckItemBefore;
