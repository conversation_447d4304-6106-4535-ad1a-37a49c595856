import React, { Component } from 'react';

import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { InputNumber } from '@manyun/base-ui.ui.input-number';

import { YES_OR_NO_KEY_MAP } from '@manyun/dc-brain.legacy.constants';

class CheckItem extends Component {
  render() {
    const { itemMethod, itemNormal, inputData, value } = this.props;
    return (
      <>
        <Checkbox
          checked={value?.checked}
          onChange={e => {
            let tmp = {
              checked: e.target.checked,
            };
            if (value) {
              tmp = { ...value, ...tmp };
            }
            this.props.onChange(tmp);
          }}
        >
          {itemMethod}-{itemNormal}
        </Checkbox>
        {inputData === YES_OR_NO_KEY_MAP.YES && (
          <InputNumber
            min={0}
            max={999999}
            precision={4}
            value={value?.num}
            onChange={num => {
              let tmp = {
                num: num,
              };
              if (value) {
                tmp = { ...value, ...tmp };
              }
              this.props.onChange(tmp);
            }}
          />
        )}
      </>
    );
  }
}
export default CheckItem;
