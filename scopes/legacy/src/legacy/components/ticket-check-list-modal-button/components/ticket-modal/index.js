import throttle from 'lodash.throttle';
import React, { useRef, useState } from 'react';
import styled from 'styled-components';

import { Button } from '@manyun/base-ui.ui.button';
import { Modal } from '@manyun/base-ui.ui.modal';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';

import CheckItem from '../check-item';

const StyledModal = styled(Modal)`
  min-width: 700px;

  .manyun-modal-content > .manyun-modal-footer {
    text-align: center;
  }
`;
function TicketModal({
  modalVisible,
  updateModalVisible,
  step,
  setSteps,
  customStep,
  customFooter,
  showNextStep,
  beforeValues,
  afterValues,
  setBeforeValues,
  setAfterValues,
  itemsBefore,
  itemsAfter,
  getCurrentStepAndVisible,
  modalTitle,
  checkItemBeforeFormRef,
  checkItemAfterFormRef,
  isPassedCheckAfter,
  completeSubmission,
  onFilture,
}) {
  const [isPassedCheckBefore, setIsPassedCheckBefore] = useState(true);

  // 使用useRef保证throttledCompleteSubmission在组件生命周期内保持一致
  const throttledCompleteSubmission = useRef(
    throttle(() => {
      completeSubmission();
    }, 3000)
  ).current;

  const validateStandsInBefore = async () => {
    if (itemsBefore.length) {
      checkItemBeforeFormRef.current.form.validateFields((err, values) => {
        if (err) {
          setIsPassedCheckBefore(false);
          return;
        }
        setIsPassedCheckBefore(true);
        setSteps(
          count => count + 1,
          () => {
            getCurrentStepAndVisible(modalVisible, step + 1);
          }
        );
      });
    } else {
      setSteps(
        count => count + 1,
        () => {
          getCurrentStepAndVisible(modalVisible, step + 1);
        }
      );
    }
  };

  return (
    <StyledModal
      width="min-content"
      title={modalTitle}
      destroyOnClose
      visible={modalVisible}
      bodyStyle={{
        maxHeight: 'calc(80vh - 55px - 53px)',
        overflowY: 'auto',
      }}
      footer={
        <GutterWrapper>
          {getshowPrev({ step }) && (
            <Button
              type="primary"
              onClick={() => {
                setSteps(
                  count => count - 1,
                  () => {
                    getCurrentStepAndVisible(modalVisible, step);
                  }
                );
              }}
            >
              上一步
            </Button>
          )}

          {/* 自定义步骤 */}
          {showCustomStep({ step, itemsBefore, customStep }) && customFooter(updateModalVisible)}

          {getshowNext({ step, itemsBefore, itemsAfter, customStep, showNextStep }) && (
            <Button
              type="primary"
              onClick={() => {
                if (itemsBefore.length && step === 1) {
                  validateStandsInBefore();
                  return;
                }
                setSteps(
                  count => count + 1,
                  () => {
                    getCurrentStepAndVisible(modalVisible, step);
                  }
                );
              }}
            >
              下一步
            </Button>
          )}

          {getshowFinish({ step, itemsBefore, itemsAfter, customStep, showNextStep }) && (
            <Button type="primary" onClick={throttledCompleteSubmission}>
              完成
            </Button>
          )}

          {(!isPassedCheckBefore || !isPassedCheckAfter) && (
            <Button
              type="danger"
              onClick={() => {
                onFilture(updateModalVisible);
              }}
            >
              失败
            </Button>
          )}
        </GutterWrapper>
      }
      onCancel={() => updateModalVisible(false)}
    >
      {/* 可以只有操作前或者操作后检查项，如果没有则不显示 */}

      {step === 1 && itemsBefore.length > 0 && (
        <CheckItem
          title="操作前检查项"
          wrappedComponentRef={checkItemBeforeFormRef}
          formValues={beforeValues}
          setFormValues={setBeforeValues}
          checkData={itemsBefore}
        />
      )}

      {itemsBefore.length === 0 &&
        customStep.length > 0 &&
        step <= customStep.length + 1 &&
        customStep[step - 1]}

      {step > 1 &&
        itemsBefore.length > 0 &&
        customStep.length > 0 &&
        step <= customStep.length + 1 &&
        customStep[step - 2]}

      {step === Number(`${itemsBefore.length > 0 ? 1 : 0}`) + customStep.length + 1 &&
        itemsAfter.length > 0 && (
          <CheckItem
            title="操作后检查项"
            wrappedComponentRef={checkItemAfterFormRef}
            formValues={afterValues}
            setFormValues={setAfterValues}
            checkData={itemsAfter}
          />
        )}
    </StyledModal>
  );
}

export default TicketModal;

// 是否显示上一步
function getshowPrev({ step }) {
  if (!step) {
    return false;
  }
  // 除第一步外 显示上一步
  if (step && step > 1) {
    return true;
  }
  return false;
}

// 是否显示下一步
function getshowNext({ step, itemsBefore, itemsAfter, customStep, showNextStep }) {
  if (!step) {
    return false;
  }

  if (itemsBefore.length && itemsAfter.length) {
    if (step === 1) {
      return true;
    }
    if (showNextStep && step <= customStep.length + 1) {
      return true;
    }
    return false;
  }

  if (!itemsBefore.length && itemsAfter.length) {
    if (showNextStep && step <= customStep.length) {
      return true;
    }
    return false;
  }

  if (itemsBefore.length && !itemsAfter.length) {
    if (step === 1) {
      return true;
    }

    if (showNextStep && step < customStep.length + 1) {
      return true;
    }
    return false;
  }

  if (!itemsBefore.length && !itemsAfter.length) {
    if (showNextStep && step < customStep.length) {
      return true;
    }
    return false;
  }
  return false;
}

// 是否显示完成
function getshowFinish({ step, itemsBefore, itemsAfter, customStep, showNextStep }) {
  if (itemsBefore.length && itemsAfter.length) {
    if (step === customStep.length + 2) {
      return true;
    }
    return false;
  }

  if (!itemsBefore.length && itemsAfter.length) {
    if (step === customStep.length + 1) {
      return true;
    }
    return false;
  }

  if (itemsBefore.length && !itemsAfter.length) {
    if (showNextStep && step === customStep.length + 1) {
      return true;
    }
    return false;
  }

  if (!itemsBefore.length && !itemsAfter.length) {
    if (showNextStep) {
      return true;
    }
    return false;
  }
  return false;
}

// 显示自定义步骤
function showCustomStep({ step, itemsBefore, customStep }) {
  if (itemsBefore.length) {
    if (step > 1 && step <= customStep.length + 1) {
      return true;
    }
    return false;
  }
  if (!itemsBefore.length) {
    if (step <= customStep.length) {
      return true;
    }
    return false;
  }
  return false;
}
