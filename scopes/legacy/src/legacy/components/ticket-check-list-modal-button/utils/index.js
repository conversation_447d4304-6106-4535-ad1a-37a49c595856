import uniq from 'lodash/uniq';

export function getCheckItemChildrens(data) {
  let newData = [];
  const keys = uniq(data.map(({ mergeRowsKey }) => mergeRowsKey));
  newData = keys.map(key => {
    const sameMergeRowsKeyData = data.filter(({ mergeRowsKey }) => mergeRowsKey === key);
    return {
      itemName: sameMergeRowsKeyData[0].itemName,
      children: sameMergeRowsKeyData,
    };
  });
  return newData;
}
