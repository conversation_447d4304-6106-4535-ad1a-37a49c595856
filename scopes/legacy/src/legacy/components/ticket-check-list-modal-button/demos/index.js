import React from 'react';

import { Button } from '@manyun/base-ui.ui.button';

import TicketCheckListModalButton from '@manyun/dc-brain.legacy.components/ticket-check-list-modal-button';

export default function Demo() {
  return (
    <TicketCheckListModalButton
      title="执行"
      taskType="POWER"
      taskSubType="POWER_OFF"
      customStep={[<p>888888</p>, <p>22222</p>]}
      customFooter={callback => {
        return <Button onClick={() => callback(false)}>测试</Button>;
      }}
      showNextStep={true}
      getCurrentStepAndVisible={(visible, step) => {}}
      onFinish={() => {
        return true;
      }}
      onFilture={callback => {
        return true;
      }}
    />
  );
}
