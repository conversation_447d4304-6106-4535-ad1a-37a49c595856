import React from 'react';
import { useSelector } from 'react-redux';

import { Layout as DCBrainLayout } from '@manyun/dc-brain.ui.layout';
import { ConfigUtil } from '@manyun/dc-brain.util.config';

import { getCurrentConfig } from '@manyun/dc-brain.legacy.redux/selectors/configSelectors';

export function Layout(props) {
  const config = useSelector(getCurrentConfig);
  const configUtil = new ConfigUtil(config);
  const homeUrl = configUtil.getHomeUrl();

  return <DCBrainLayout homeUrl={homeUrl} {...props} />;
}

export default Layout;
