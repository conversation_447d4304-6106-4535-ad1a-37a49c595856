import React from 'react';

import { EChart } from '@manyun/dc-brain.legacy.components';

export default function Demo() {
  return <EChart {...option} />;
}

const option = {
  yAxis: [
    {
      type: 'value',
      scale: true, // 这个参数能让图表自己决定 max, min，能防止多条线重叠
    },
    {
      type: 'value',
      scale: true,
    },
  ],
  series: [
    {
      name: '温度（℃）',
      type: 'line',
      smooth: true, // 平滑曲线
      showSymbol: false, // 隐藏曲线上的圆点
    },
    {
      yAxisIndex: 1, // 指定此系列数据使用第二个 Y 轴
      name: '湿度（%）',
      type: 'line',
      smooth: true,
      showSymbol: false,
    },
  ],
  seriesData: [
    [
      [1585731120000 /* 时间格式可以是 timestamp */, 22],
      ['2020-04-01 16:53:00', 21],
      ['2020-04-01 16:54:00', 23],
      ['2020-04-01 16:55:00', 25],
      ['2020-04-01 16:56:00', 21],
      ['2020-04-01 16:57:00', 22],
      ['2020-04-01 16:58:00', 23],
    ],
    [
      ['2020-04-01 16:52:00', 55],
      ['2020-04-01 16:53:00', 52],
      ['2020-04-01 16:54:00', 56],
      ['2020-04-01 16:55:00', 57],
      ['2020-04-01 16:56:00', 58],
      ['2020-04-01 16:57:00', 53],
      ['2020-04-01 16:58:00', 51],
    ],
  ],
};
