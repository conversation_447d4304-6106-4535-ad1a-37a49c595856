export const getTimeMask = diffMinutes => {
  let timeMask;
  if (diffMinutes < 60) {
    timeMask = 'HH:mm:ss';
  } else if (diffMinutes < 60 * 24) {
    timeMask = 'HH:mm';
  } else if (diffMinutes < 60 * 24 * 2) {
    timeMask = 'MM-DD HH:mm';
  } else if (diffMinutes < 60 * 24 * 12) {
    timeMask = 'MM-DD';
  } else if (diffMinutes > 60 * 24 * 12) {
    timeMask = 'YYYY-MM';
  }

  return timeMask;
};
