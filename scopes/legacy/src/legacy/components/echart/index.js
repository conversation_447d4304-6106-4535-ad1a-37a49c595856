import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useRef } from 'react';
import { useResizeDetector } from 'react-resize-detector';

import echarts from 'echarts-4';
import moment from 'moment';
import shallowequal from 'shallowequal';

import usePrevious from '@manyun/dc-brain.legacy.utils/useHooks/usePrevious';

import { getTimeMask } from './utils';

/**
 * @typedef Props
 * @property {React.CSSProperties} [containerStyle={width:'100%',height:300}] 图标容器样式
 * @property {'line'|'pie'|'bar'} [type="line"] 图表类型
 * @property {object|false} [toolbox]
 * @property {{ top?: number; right?: number; bottom?: number; left?: number }} [grid]
 * @property {object} [legend] 图例配置
 * @property {[Date,Date]} [timeInterval] （仅在使用时间轴时需要配置）数据源时间区间，用于计算时间轴的格式化形式
 * @property {object} [tooltip] 提示框组件 {@link https://echarts.apache.org/zh/option.html#tooltip 官方文档}
 * @property {object} [xAxis] x 轴（默认为时间轴） {@link https://echarts.apache.org/zh/option.html#xAxis 官方文档}
 * @property {any} yAxis y 轴（双 y 轴需要传入数组） {@link https://echarts.apache.org/zh/option.html#yAxis 官方文档}
 * @property {object} [angleAxis] 径向轴 {@link https://echarts.apache.org/zh/option.html#angleAxis 官方文档}
 * @property {object} [radiusAxis] 角度轴 {@link https://echarts.apache.org/zh/option.html#radiusAxis 官方文档}
 * @property {object[]} series 系列配置 {@link https://echarts.apache.org/zh/option.html#series 官方文档}
 * @property {[number, number][]} seriesData 系列数据（这里把系列配置和数据分成2个API，因为配置通常是静态的，数据通常是动态的） {@link https://echarts.apache.org/zh/option.html#series-line.data 官方文档}
 * @property {Function} [onDataZoom]
 * @property {Function} [onRestore]
 * @property {string} backgroundColor 画布背景色
 * @property {string} title 标题 {@link https://echarts.apache.org/zh/option.html#title 官方文档}
 * @property {(chart: any) => void} onReady 图表初始化完成后的回调
 */

/**
 * 封装于 echarts（图标类型可在 props.series 中指定）
 * @param {Props} props
 */
export function EChart({
  forwardedRef,
  containerStyle = { width: '100%', height: 300 },
  type,
  toolbox,
  grid,
  legend,
  timeInterval,
  tooltip,
  xAxis,
  yAxis,
  series,
  seriesData,
  seriesMarkLineData,
  seriesMarkPointData,
  onDataZoom,
  onRestore,
  backgroundColor,
  title,
  onReady = noop,
}) {
  /**
   * @type {React.MutableRefObject<echarts.EtimeIntervalCharts>}
   */
  const chartRef = useRef();
  const containerRef = useRef();

  useImperativeHandle(forwardedRef, () => ({ chart: chartRef.current }));

  let timeDiff = 0;
  if (Array.isArray(timeInterval) && timeInterval.length === 2) {
    timeDiff = moment(timeInterval[1]).diff(moment(timeInterval[0]), 'minutes');
  }
  const timeMask = getTimeMask(timeDiff);

  const legendSelectChangedListener = useCallback(
    ({ selected }) => {
      if (!seriesMarkLineData) {
        return;
      }

      const serieNames = Object.keys(selected).reduce((visibleSerieNames, serieName) => {
        if (selected[serieName] === true) {
          visibleSerieNames.push(serieName);
        }

        return visibleSerieNames;
      }, []);

      // 所有系列被隐藏，不需要更新 markLine
      if (!serieNames.length) {
        return;
      }

      // 只展示一个系列的数据，同时展示这个系列的 markLine
      if (serieNames.length === 1) {
        const serieIdx = series.findIndex(({ name }) => name === serieNames[0]);
        chartRef.current.setOption({
          series: seriesMarkLineData.map((data, idx) => {
            const markLine = { data: [] };
            if (idx === serieIdx) {
              markLine.data = data;
            }

            return { markLine };
          }),
        });
        return;
      }

      // 隐藏所有系列的 markLine
      chartRef.current.setOption({
        series: seriesMarkLineData.map(() => ({ markLine: { data: [] } })),
      });
    },
    [series, seriesMarkLineData]
  );

  useEffect(() => {
    const chart = echarts.init(containerRef.current, 'DC Base');
    chartRef.current = chart;
    onReady(chart);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (!chartRef.current) {
      return;
    }
    if (typeof onDataZoom === 'function') {
      chartRef.current.on('datazoom', onDataZoom);
    }
    if (typeof onRestore === 'function') {
      chartRef.current.on('restore', onRestore);
    }
    chartRef.current.on('legendselectchanged', legendSelectChangedListener);

    return () => {
      chartRef.current.off('legendselectchanged', legendSelectChangedListener);
    };
  }, [onDataZoom, onRestore, legendSelectChangedListener]);

  useEffect(() => {
    if (!chartRef.current) {
      return;
    }
    const opts = getOpts({
      type,
      toolbox,
      grid,
      legend,
      timeMask,
      xAxis,
      tooltip,
      yAxis,
      series,
      backgroundColor,
      title,
      seriesMarkPointData,
    });
    chartRef.current.setOption(opts);
  }, [
    type,
    toolbox,
    grid,
    legend,
    timeMask,
    xAxis,
    yAxis,
    series,
    tooltip,
    backgroundColor,
    title,
    seriesMarkPointData,
  ]);

  const prevSeriesData = usePrevious(seriesData);
  const prevSeriesMarkLineData = usePrevious(seriesMarkLineData);
  const prevSeriesMarkPointData = usePrevious(seriesMarkPointData);

  useEffect(() => {
    if (!chartRef.current) {
      return;
    }
    if (
      shallowequal(prevSeriesData, seriesData) &&
      shallowequal(prevSeriesMarkLineData, seriesMarkLineData)
    ) {
      return;
    }
    const newSeries = seriesData.map(data => ({ data }));
    if (
      newSeries.length === 1 /* 当且仅当一个系列时，才显示 markLine */ &&
      Array.isArray(seriesMarkLineData)
    ) {
      seriesMarkLineData.forEach((data, serieIdx) => {
        newSeries[serieIdx].markLine = { data };
      });
    }

    chartRef.current.setOption({ series: newSeries });
  }, [seriesData, seriesMarkLineData, seriesMarkPointData, prevSeriesData, prevSeriesMarkLineData]);

  useEffect(() => {
    if (!chartRef.current) {
      return;
    }
    if (
      shallowequal(prevSeriesData, seriesData) &&
      shallowequal(prevSeriesMarkPointData, seriesMarkPointData)
    ) {
      return;
    }
    const newSeries = seriesData.map(data => ({ data }));

    if (Array.isArray(seriesMarkPointData) && seriesMarkPointData.length > 0) {
      seriesMarkPointData.forEach((data, serieIdx) => {
        if (serieIdx < newSeries.length) {
          newSeries[serieIdx].markPoint = { data, silent: true, symbolSize: 40 };
        }
      });
    }

    chartRef.current.setOption({ series: newSeries });
  }, [seriesData, seriesMarkPointData, prevSeriesData, prevSeriesMarkPointData]);
  const { width, height } = useResizeDetector({ targetRef: containerRef });
  useEffect(() => {
    chartRef.current && chartRef.current.resize();
  }, [width, height]);

  return <div ref={containerRef} style={containerStyle} />;
}

/**
 * @type {React.RefForwardingComponent<{ chart: any }, Props>}
 */
const RefForwardingEchart = forwardRef((props, ref) => <EChart {...props} forwardedRef={ref} />);

export default RefForwardingEchart;

const noop = () => {};

const getOpts = ({
  type,
  toolbox,
  grid = {},
  legend = {},
  timeMask,
  xAxis = {
    type: 'time',
  },
  tooltip = {},
  yAxis,
  series,
  backgroundColor,
  title,
} = {}) => {
  const baseOpts = {
    legend,
    toolbox,
    grid: {
      top: 40,
      right: 60,
      bottom: 60,
      left: 60,
      ...grid,
    },
    series,
    backgroundColor,
    title,
  };

  if (type === 'pie') {
    return baseOpts;
  }

  return {
    ...baseOpts,
    legend: {
      bottom: 10,
      left: 'center',
      type: 'scroll',
      ...legend,
    },
    tooltip: {
      trigger: 'axis',
      formatter: xAxis.type === 'time' ? defaultTooltipFormatter : undefined,
      ...tooltip,
    },
    toolbox:
      toolbox === false
        ? false
        : {
            feature: {
              dataZoom: {
                yAxisIndex: 'none',
              },
              restore: {},
            },
          },
    xAxis: {
      ...xAxis,
      axisLabel: getAxisLabel(xAxis, timeMask),
    },
    yAxis,
  };
};

function getAxisLabel({ type } = {}, timeMask) {
  const axisLabel = {
    formatter: function (value) {
      if (type === 'time') {
        return moment(value).format(timeMask);
      }
      return value;
    },
  };
  return axisLabel;
}

function defaultTooltipFormatter(params) {
  if (!params) {
    return;
  }
  let ps = params;
  if (!Array.isArray(params)) {
    ps = [params];
  }
  const time = moment(ps[0].data[0]).format('YYYY-MM-DD HH:mm');
  const series = ps
    .map(({ seriesName, data: [__, value], marker }) => {
      const valueTxt = [null, undefined].includes(value) ? '无数据' : value;

      return `${marker} ${seriesName}: ${valueTxt}`;
    })
    .join('<br />');

  return `${time}<br />${series}`;
}
