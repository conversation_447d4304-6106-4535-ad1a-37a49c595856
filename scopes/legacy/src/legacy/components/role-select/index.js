import React from 'react';

import { Select } from '@galiojs/awesome-antd';
import debounce from 'lodash/debounce';
import get from 'lodash/get';
import omit from 'lodash/omit';

import { DataService } from '@manyun/dc-brain.legacy.components/renderProps';
import * as roleService from '@manyun/dc-brain.legacy.services/roleService';

/**
 * @typedef {import('antd-3/lib/select').SelectProps} Props
 *
 * @typedef {(option: any) => React.ReactNode} Callback
 *
 * @typedef FieldNames
 * @property { string | Callback } [key='id'] 指定选项中哪个属性作为 `key`。
 * @property { string | Callback } [value='id'] 指定选项中哪个属性作为 `value`。
 * @property { string | Callback } [label='name'] 指定选项中哪个属性作为 `label`。
 *
 * @typedef CustomizedProps
 * @property {FieldNames} [fieldNames]
 * @property {() => Promise<any>} [dataService]
 */

/**
 * Select a role or multiple roles.
 * It is based on antd.Select component, and it's `labelInValue` by default.
 * @augments {React.PureComponent<Props & CustomizedProps>}
 */
export default class UserSelect extends React.PureComponent {
  /**
   * @type {Partial<CustomizedProps>}
   */
  static defaultProps = {
    fieldNames: {
      key: 'roleCode',
      value: 'roleCode',
      label: 'roleName',
    },
    dataService: defaultDataService,
  };

  /**
   * @type {React.RefObject<DataService>}
   */
  _dataServiceRef = React.createRef();

  _searchHandler = debounce(keyword => {
    if (keyword === '') {
      return;
    }
    this._dataServiceRef.current.getDataByQ(keyword, true);
  }, 800);

  _changeHandler = (value, optionNode) => {
    const { onChange } = this.props;
    if (typeof onChange != 'function') {
      return;
    }
    // when clearing the selected value
    if (optionNode === undefined) {
      onChange(value);
      return;
    }

    // mode === 'multiple'
    if (Array.isArray(value)) {
      const { fieldNames } = this.props;
      const vals = value.map(({ key, label }) => {
        const option = optionNode.find(option => {
          const optionKey = getField(option.props['data-ref'], fieldNames.key);

          return key === optionKey;
        });

        if (!option) {
          return { key, label };
        }

        return { key, label, ...option.props['data-ref'] };
      });
      onChange(vals);
      return;
    }

    onChange({ ...value, ...optionNode.props['data-ref'] });
  };

  render() {
    const { fieldNames, dataService } = this.props;

    return (
      <DataService ref={this._dataServiceRef} dataService={dataService}>
        {({ data = [], requesting }) => (
          <Select
            showSearch
            labelInValue
            filterOption={false}
            dropdownMatchSelectWidth={false}
            {...omit(this.props, CUSTOMIZED_PROPS)}
            loading={requesting}
            onSearch={this._searchHandler}
            onChange={this._changeHandler}
            onFocus={this._searchHandler}
          >
            {data.map(option => {
              const key = getField(option, fieldNames.key);
              const value = getField(option, fieldNames.value);
              const label = getField(option, fieldNames.label);

              return (
                <Select.Option key={key} value={value} data-ref={option}>
                  {label}
                </Select.Option>
              );
            })}
          </Select>
        )}
      </DataService>
    );
  }
}

const CUSTOMIZED_PROPS = ['fieldNames', 'dataService'];

async function defaultDataService(keyword) {
  const { response } = await roleService.fetchRolesByName(keyword);
  if (response) {
    return response.data;
  }
}

function getField(option, fieldName) {
  if (typeof fieldName == 'string') {
    return get(option, fieldName);
  }
  if (typeof fieldName == 'function') {
    return fieldName(option);
  }
  throw new Error(`[UserSelect]: fieldName(${fieldName})'s type should be string or function.`);
}
