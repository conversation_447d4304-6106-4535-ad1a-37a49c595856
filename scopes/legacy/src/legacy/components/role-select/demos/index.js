/* eslint-disable no-console */
import React from 'react';

import Form from '@ant-design/compatible/es/form';

import RoleSelect from '@manyun/dc-brain.legacy.components/role-select';

export default function Demo() {
  return (
    <Form layout="inline">
      <Form.Item label="角色（单选）">
        <RoleSelect style={{ width: 200 }} allowClear onChange={console.log} />
      </Form.Item>
      <Form.Item label="角色（多选）">
        <RoleSelect style={{ width: 200 }} allowClear mode="multiple" onChange={console.log} />
      </Form.Item>
    </Form>
  );
}
