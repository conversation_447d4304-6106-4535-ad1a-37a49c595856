import cloneDeep from 'lodash.clonedeep';
import merge from 'lodash.merge';

/**
 * 获取已缓存在 `localStorage` 中的 `columns` 配置
 * @param {string} cacheId
 * @param {any[]} [existingColumns=[]] 表格组件中当前在使用的 `columns`
 * @return {any[]}
 */
export function getCache(cacheId, existingColumns = []) {
  if (existingColumns.length <= 0) {
    window.localStorage.removeItem(cacheId);
    return;
  }

  const cacheStr = window.localStorage.getItem(cacheId);
  if (cacheStr === null) {
    return existingColumns;
  }

  let cacheColumns;
  try {
    cacheColumns = JSON.parse(cacheStr);
  } catch (error) {
    console.error(`Parsing <TinyTable>'s columns cache error: `, error);
    return existingColumns;
  }

  const columns = cloneDeep(existingColumns);
  const columnsOrders = [];
  columns.forEach((column, index) => {
    const id = getColumnId(column);
    const matchIdx = cacheColumns.findIndex(({ key, dataIndex }) => {
      if (key !== undefined && column.key !== undefined) {
        return key === column.key;
      }
      if (Array.isArray(dataIndex) && Array.isArray(column.dataIndex)) {
        return dataIndex.toString() === column.dataIndex.toString();
      }
      return dataIndex === column.dataIndex;
    });
    if (matchIdx > -1) {
      columnsOrders.push({
        id,
        order: matchIdx,
      });
      columns[index] = merge(column, cacheColumns[matchIdx]);
    } else {
      columnsOrders.push({
        id,
        order: Number.POSITIVE_INFINITY,
      });
    }
  });
  columns.sort(
    (a, b) =>
      columnsOrders.find(item => item.id === getColumnId(a)).order -
      columnsOrders.find(item => item.id === getColumnId(b)).order
  );

  return columns;
}

function getColumnId(column) {
  return (
    column.key ?? (Array.isArray(column.dataIndex) ? column.dataIndex.join('.') : column.dataIndex)
  );
}

/**
 * 将 `columns` 配置缓存到 `localStorage`
 * @param {strinng} cacheId
 * @param {any[]} cache
 */
export function setCache(cacheId, cache) {
  window.localStorage.setItem(cacheId, JSON.stringify(cache));
}
