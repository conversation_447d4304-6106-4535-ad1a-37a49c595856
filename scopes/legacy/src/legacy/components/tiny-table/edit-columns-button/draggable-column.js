import React from 'react';
import { Draggable } from 'react-beautiful-dnd';

import DragOutlined from '@ant-design/icons/es/icons/DragOutlined';
import EyeInvisibleOutlined from '@ant-design/icons/es/icons/EyeInvisibleOutlined';
import EyeOutlined from '@ant-design/icons/es/icons/EyeOutlined';

import GutterWrapper from '@manyun/dc-brain.legacy.components/gutter-wrapper';

import '../index.less';

export default function DraggableColumn({
  draggable,
  draggableId,
  index,
  title,
  defaultVisible = true,
  visible,
  onDefaultVisibleChange,
}) {
  const newVisble = visible === undefined ? defaultVisible : visible;
  const EyeIcon = defaultVisible ? EyeInvisibleOutlined : EyeOutlined;
  return (
    <Draggable isDragDisabled={!draggable} draggableId={draggableId} index={index}>
      {(provided, snapshot) => (
        <div
          className={
            newVisble ? 'draggable-container' : 'draggable-container draggable-container-disabled'
          }
          ref={provided.innerRef}
          {...provided.draggableProps}
        >
          <GutterWrapper flex justifyContent="space-between">
            <span>{title}</span>
            <GutterWrapper size="8px">
              {visible === undefined && (
                <EyeIcon
                  style={{ cursor: 'pointer' }}
                  onClick={() => {
                    onDefaultVisibleChange(draggableId, !defaultVisible);
                  }}
                />
              )}
              {draggable && <DragOutlined {...provided.dragHandleProps} />}
            </GutterWrapper>
          </GutterWrapper>
        </div>
      )}
    </Draggable>
  );
}
