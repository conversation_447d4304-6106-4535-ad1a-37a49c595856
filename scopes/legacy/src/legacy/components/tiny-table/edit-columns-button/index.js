import React, { useState } from 'react';
import { DragDropContext, Droppable } from 'react-beautiful-dnd';

import pick from 'lodash.pick';
import useDeepCompareEffect from 'use-deep-compare-effect';

import { TableColumnOutlined } from '@manyun/base-ui.icons';
import { message } from '@manyun/base-ui.ui.message';

import { ModalButton } from '@manyun/dc-brain.legacy.components';

import { getCache, setCache } from './../utils';
import DraggableColumn from './draggable-column';
import styles from './edit-columns-button.module.less';

export function EditColumnsButton({ cacheId, columns = [], onColumnsUpdate }) {
  const [okBtnDisabled, setOkBtnDisabled] = useState(true);
  const [clmns, setColumns] = useState(getCache(cacheId, columns));

  useDeepCompareEffect(() => {
    setColumns(getCache(cacheId, columns));
  }, [cacheId, columns]);

  function tryToUpdateCache(columns) {
    if (cacheId) {
      const cache = columns.map(column => pick(column, ['key', 'dataIndex', 'defaultVisible']));
      setCache(cacheId, cache);
    }
  }

  function dragEndHander(result) {
    // dropped outside the list
    if (!result.destination) {
      return;
    }

    // dropped onto the same position
    if (
      result.source.droppableId === result.destination.droppableId &&
      result.source.index === result.destination.index
    ) {
      return;
    }

    // dropped onto the fixed column
    if (![undefined, false].includes(clmns[result.destination.index].fixed)) {
      message.error('锁定列无法参与排序！');
      return;
    }

    const newColumns = reorder(clmns, result.source.index, result.destination.index);
    setColumns(newColumns);
    setOkBtnDisabled(false);
  }

  function defaultVisibleChangeHandler(draggableId, defaultVisible) {
    setColumns(prevColumns => {
      return prevColumns.map(({ key, dataIndex, ...rest }) => {
        if (key === draggableId || dataIndex === draggableId) {
          return {
            key,
            dataIndex,
            ...rest,
            defaultVisible,
          };
        }
        return { key, dataIndex, ...rest };
      });
    });
    setOkBtnDisabled(false);
  }

  return (
    <ModalButton
      text={
        <>
          <TableColumnOutlined style={{ paddingRight: 8 }} />
          定制列
        </>
      }
      title="定制列"
      bodyStyle={{ height: '450px', overflowX: 'hidden', overflowY: 'auto' }}
      okButtonProps={{
        disabled: okBtnDisabled,
      }}
      onOk={() => {
        tryToUpdateCache(clmns);
        onColumnsUpdate(clmns);

        return true;
      }}
      type="link"
    >
      <DragDropContext onDragEnd={dragEndHander}>
        <Droppable droppableId="droppable">
          {provided => (
            <div
              ref={provided.innerRef}
              className={styles.draggableContainer}
              {...provided.droppableProps}
            >
              {clmns.map(({ key, dataIndex, title, defaultVisible, visible, fixed }, index) => (
                <DraggableColumn
                  key={key ?? (Array.isArray(dataIndex) ? dataIndex.join('.') : dataIndex)}
                  draggable={[undefined, false].includes(fixed)}
                  draggableId={key || dataIndex}
                  title={title}
                  index={index}
                  defaultVisible={defaultVisible}
                  visible={visible}
                  onDefaultVisibleChange={defaultVisibleChangeHandler}
                />
              ))}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>
    </ModalButton>
  );
}

export default EditColumnsButton;

// a little function to help us with reordering the result
const reorder = (list, startIndex, endIndex) => {
  const result = Array.from(list);
  const [removed] = result.splice(startIndex, 1);
  result.splice(endIndex, 0, removed);

  return result;
};
