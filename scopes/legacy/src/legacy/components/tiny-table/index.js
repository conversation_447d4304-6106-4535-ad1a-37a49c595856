import React, { useEffect, useRef, useState } from 'react';
import { CSVLink } from 'react-csv';
import { Link, useHistory } from 'react-router-dom';

import { saveAs } from 'file-saver';
import _ from 'lodash';
import omit from 'lodash/omit';
import uniqBy from 'lodash/uniqBy';
import moment from 'moment';

import { ExportOutlined } from '@manyun/base-ui.icons/dist/outlined/file-export';
import { Button } from '@manyun/base-ui.ui.button';
import { Table } from '@manyun/base-ui.ui.table';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';

import EditColumnsButton from './edit-columns-button';
import { getCache } from './utils';

const defaultRender = text => text;

/**
 * @typedef { import('antd-3/lib/table').TableProps } TableProps
 * @typedef { import('antd-3/lib/table').ColumnProps } ColumnProps
 *
 * @typedef EditColumnsButtonProps
 * @property {boolean} [enabled=false] 启用列设置
 * @property {string} [cacheId] 是否将列设置缓存到 localStorage 中，未指定 `cacheId` 时，不缓存
 *
 * @typedef {'date' | 'datetime' | 'link' | 'function'} DataType
 *
 * @typedef DateOptions
 * @property {string} [format='YYYY-MM-DD']
 *
 * @typedef DateTimeOptions
 * @property {string} [format='YYYY-MM-DD HH:mm:ss']
 *
 * @typedef {(text?: any, record?: object, index?: number) => string} Render
 *
 * @typedef LinkOptions
 * @property {string|Render} to
 * @property {string|Render} text
 *
 * @typedef DataTypeWithOptions
 * @property {DataType} type
 * @property {DateOptions | DateTimeOptions | LinkOptions} options
 *
 * @typedef ExportDataIndex
 * @property {string} title
 * @property {string} dataIndex
 *
 * @typedef {object} CustomizedColumnProps
 * @property {DataType|DataTypeWithOptions} dataType 预置的渲染方式
 * @property {boolean} [exportable=true] 是否允许导出此列
 * @property {ExportDataIndex[]} [exportDataIndexes] 此列 `dataIndex` 对应的值是个对象，可通过此API配置需要导出的属性
 * @property {boolean} [defaultVisible=true] 【非受控】列是否显示
 * @property {boolean} [visible] 【受控】列是否显示
 *
 * @typedef {object} ShowExportOptions
 * @property {boolean} [useServiceOnly] 是否强制使用 `exportServices` 导出
 * @property {string} [filename] 自定义导出文件的文件名
 * @property {string} [exportButtonText] 导出按钮后缀文案
 *
 * @typedef {object} CustomizedProps
 * @property {string} [align] 列对齐方式
 * @property {Array<ColumnProps & CustomizedColumnProps>} columns 列配置
 * @property {React.CSSProperties} [containerStyle] 容器样式
 * @property {React.CSSProperties} [actionsWrapperStyle] 表格上方操作按钮容器样式
 * @property {JSX.Element | JSX.Element[]} [actions=null] 表格上方的操作按钮
 * @property {boolean|ShowExportOptions} [showExport=false] 是否需要导出功能
 * @property {EditColumnsButtonProps} editColumnsButton 列设置配置
 * @property {boolean} [selectRowsSpreadPage=false] 是否需要支持跨页选择
 *
 * @typedef {Omit<TableProps, 'columns'> & CustomizedProps} Props
 */

/**
 * 封装于 `antd` 的 `Table` 组件
 * @param {Props}
 */
export function TinyTable({
  forwardedRef,
  containerStyle,
  actionsWrapperStyle,
  actions = null,
  showExport = false,
  editColumnsButton = { enabled: false },
  exportServices,
  columns,
  rowSelection,
  dataSource,
  pagination = {
    total: Array.isArray(dataSource) ? dataSource.length : 0,
  },
  exportAllData = false,
  selectRowsSpreadPage = false,
  align = 'left',
  extra,
  ...props
}) {
  React.useEffect(() => {
    if (columns?.some(column => column.dataIndex?.includes('.'))) {
      console.error(
        "warning: TinyTable 内部依赖的 Table 已升级到 base-ui.table ，请修复 breaking changes： 嵌套 dataIndex 支持从 'xxx.yyy' 改成 ['xxx', 'yyy']。 参考 https://ant.design/docs/react/migration-v4-cn !!!"
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const [csvData, setCsvData] = useState([]);
  const [clmns, setColumns] = useState(columns);
  const editColumnsButtonEnabled = editColumnsButton.enabled;
  const editColumnsCacheId = editColumnsButton.cacheId;

  const visibleClmns = clmns.filter(
    ({ defaultVisible = true, visible = defaultVisible }) => visible
  );
  const { selectedRowKeys, selectedRows } = rowSelection || {};

  const initialDataRef = useRef();
  useEffect(() => {
    if (
      showExport &&
      exportAllData &&
      Array.isArray(dataSource) &&
      (!Array.isArray(selectedRows) || (Array.isArray(selectedRows) && !selectedRows.length))
    ) {
      setCsvData(getCsvData(columns, dataSource));
      return;
    }
    if (showExport && Array.isArray(selectedRows)) {
      setCsvData(getCsvData(columns, selectedRows));
    }
  }, [showExport, selectedRows, columns, exportAllData, dataSource]);

  useEffect(() => {
    setColumns(
      editColumnsButtonEnabled && editColumnsCacheId
        ? getCache(editColumnsCacheId, mergeDefaultColumnBehaviors(columns, align))
        : mergeDefaultColumnBehaviors(columns, align)
    );
  }, [editColumnsButtonEnabled, editColumnsCacheId, columns, align]);

  useEffect(() => {
    if (selectRowsSpreadPage && selectedRows) {
      initialDataRef.current = selectedRows;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const setSelectedRowKeys = ({
    selectWay,
    selected,
    selectedRows,
    changeRows,
    record,
    nativeEvent,
  }) => {
    let mergedRows = selectedRows.filter(row => row);

    if (!selected) {
      let newCurrentData;
      if (selectWay === 'onSelect') {
        const rowKey = getRowKey(props.rowKey, record);
        newCurrentData = initialDataRef.current.filter(
          item => getRowKey(props.rowKey, item) !== rowKey
        );
      }
      if (selectWay === 'onSelectAll') {
        newCurrentData = initialDataRef.current.filter(item => {
          const rowKey = getRowKey(props.rowKey, item);
          return !changeRows.find(item1 => getRowKey(props.rowKey, item1) === rowKey);
        });
      }
      mergedRows = [...newCurrentData, ...selectedRows.filter(row => row)];
    } else if (initialDataRef.current) {
      mergedRows = [...mergedRows, ...initialDataRef.current].filter(Boolean);
    }
    const rows = uniqBy(mergedRows, row => getRowKey(props.rowKey, row));
    initialDataRef.current = rows;
    const mergedKeys = rows.map(item => getRowKey(props.rowKey, item));
    if (rowSelection.onChange) {
      rowSelection.onChange(mergedKeys, rows);
    }
    if (rowSelection.onSelect && selectWay === 'onSelect') {
      rowSelection.onSelect(record, selected, rows, nativeEvent);
    }
    if (rowSelection.onSelectAll && selectWay === 'onSelectAll') {
      rowSelection.onSelectAll(selected, rows, changeRows);
    }
  };

  return (
    <GutterWrapper style={containerStyle} mode="vertical">
      {(actions || showExport || editColumnsButton.enabled || extra) && (
        <GutterWrapper style={{ justifyContent: 'space-between' }} flex>
          <GutterWrapper flex style={{ ...actionsWrapperStyle, width: '100%' }}>
            {actions}
            {showExport && (
              <ExportButton
                total={pagination.total}
                csvData={csvData}
                selectedRowKeys={selectedRowKeys}
                exportServices={exportServices}
                exportAllData={exportAllData}
                {...(showExport && typeof showExport == 'object' ? showExport : {})}
              />
            )}
          </GutterWrapper>
          {editColumnsButton.enabled && (
            <EditColumnsButton
              cacheId={editColumnsButton.cacheId}
              columns={clmns}
              onColumnsUpdate={columns => {
                setColumns(columns);
              }}
            />
          )}
          {extra ?? null}
        </GutterWrapper>
      )}
      <Table
        ref={forwardedRef}
        columns={visibleClmns}
        dataSource={dataSource}
        rowSelection={
          selectRowsSpreadPage
            ? {
                ...omit(rowSelection, 'onChange', 'onSelect', 'onSelectAll'),
                onSelect: (record, selected, selectedRows, nativeEvent) =>
                  setSelectedRowKeys({
                    selectWay: 'onSelect',
                    record,
                    selected,
                    selectedRows,
                    nativeEvent,
                  }),
                onSelectAll: (selected, selectedRows, changeRows) =>
                  setSelectedRowKeys({
                    selectWay: 'onSelectAll',
                    selected,
                    selectedRows,
                    changeRows,
                  }),
              }
            : rowSelection
        }
        pagination={pagination}
        {...props}
      />
    </GutterWrapper>
  );
}

/**
 * @type {TinyTable}
 */
export default React.forwardRef((props, ref) => <TinyTable forwardedRef={ref} {...props} />);

function ExportButton({
  selectedRowKeys,
  csvData,
  exportServices,
  exportAllData,
  exportButtonText,
  useServiceOnly,
  filename,
  total,
}) {
  const history = useHistory();
  const [loading, setLoading] = useState(false);

  if (
    !useServiceOnly &&
    ((Array.isArray(selectedRowKeys) && selectedRowKeys.length) || exportAllData)
  ) {
    return (
      <CSVLink
        data={csvData}
        filename={`${filename ? filename : history.location.pathname.slice(1)}.csv`}
        target="_blank"
      >
        <Button disabled={!total} type="link" icon={<ExportOutlined />}>
          导出{exportButtonText}
        </Button>
      </CSVLink>
    );
  }
  // if () {
  //   return (
  //     <CSVLink
  //       data={csvData}
  //       filename={`${history.location.pathname.slice(1)}.csv`}
  //       target="_blank"
  //     >
  //       <Button>导出</Button>
  //     </CSVLink>
  //   );
  // }

  return (
    <Button
      disabled={!total}
      type="link"
      style={{ lineHeight: '32px', padding: '0 15px 0 0' }}
      loading={loading}
      icon={<ExportOutlined style={{ marginRight: 8 }} />}
      onClick={async () => {
        setLoading(true);
        const data = await exportServices();
        if (data.response) {
          saveAs(data.response, `${filename ? filename : history.location.pathname.slice(1)}.csv`);
        }
        setLoading(false);
      }}
    >
      导出{exportButtonText}
    </Button>
  );
}

/**
 * 通过对 `column.dataType` 的处理，生成对应的 `render` 逻辑。
 * 简化调用时处理 `render` 的 `text` 字段。
 * @param {Array} columns 表头配置
 */
function mergeDefaultColumnBehaviors(columns = [], align = 'center') {
  return columns.map(({ dataType, ...clmnProps }) => {
    clmnProps['align'] = align;
    if (!dataType) {
      return clmnProps;
    }
    const dataTypeObj = typeof dataType == 'string' ? { type: dataType, options: {} } : dataType;
    const { type, options } = dataTypeObj;
    const { render = defaultRender, ...restClmnProps } = clmnProps;
    if (type === 'date' || type === 'datetime') {
      const { format = type === 'date' ? 'YYYY-MM-DD' : 'YYYY-MM-DD HH:mm:ss' } = options;
      return {
        render(text, ...args) {
          if (['', undefined, null].includes(text)) {
            return render(text, ...args);
          }
          return render(moment(text).format(format), ...args);
        },
        ...restClmnProps,
      };
    }
    if (type === 'link') {
      return {
        render(dataIndexText, ...args) {
          const { to, text = dataIndexText } = options;
          let link = to;
          let txt = text;
          if (typeof to == 'function') {
            link = to(dataIndexText, ...args);
          }
          if (typeof text == 'function') {
            txt = text(dataIndexText, ...args);
          }
          return render(<LinkText text={txt} to={link} />, ...args);
        },
        ...restClmnProps,
      };
    }
    return clmnProps;
  });
}

function LinkText({ text, to }) {
  if (/^http/.test(to)) {
    return <a href={to}>{text}</a>;
  }
  return <Link to={to}>{text}</Link>;
}

/**
 * 处理需要导出的数据。
 * 通过 `column.exportable` 判断是否需要导出该列。
 * @param {Array} columns 表头配置
 */
function getCsvData(columns, selectedRows) {
  const list = selectedRows.map(selectedRow => {
    const content = columns.reduce(
      (
        contentAcc,
        {
          dataType,
          exportable = true,
          exportedContent, // 自定义导出的内容
          /*这一项为对象时需要导出的每一项*/ exportDataIndexes,
          title,
          dataIndex,
        }
      ) => {
        if (!exportable) {
          return contentAcc;
        }
        if (
          typeof dataType == 'string' &&
          dataType === 'function' &&
          typeof exportedContent === 'function'
        ) {
          contentAcc[title] = exportedContent(selectedRow);
          return contentAcc;
        }
        if (typeof exportedContent === 'function') {
          contentAcc[title] = exportedContent(_.get(selectedRow, dataIndex));
          return contentAcc;
        }
        if (typeof dataType == 'string' && (dataType === 'date' || dataType === 'datetime')) {
          const date = _.get(selectedRow, dataIndex);
          contentAcc[title] =
            date && moment(date).format(dataType === 'date' ? 'YYYY-MM-DD' : 'YYYY-MM-DD HH:mm:ss');
          return contentAcc;
        }
        if (exportDataIndexes) {
          exportDataIndexes.forEach(
            ({ title, dataIndex }) => (contentAcc[title] = _.get(selectedRow, dataIndex))
          );
          return contentAcc;
        }

        contentAcc[title] = _.get(selectedRow, dataIndex);

        return contentAcc;
      },
      {}
    );
    return content;
  });
  return list;
}

function getRowKey(propsRowKey, row) {
  let rowKey;
  if (typeof propsRowKey === 'string') {
    rowKey = row[propsRowKey];
  }
  if (typeof propsRowKey === 'function') {
    rowKey = propsRowKey(row);
  }
  return rowKey;
}
