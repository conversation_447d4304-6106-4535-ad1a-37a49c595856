## TinyTable

封装于 `antd` 的 `Table` 组件。提供业务上通用的一些处理逻辑，如：

1. 时间文案的格式化；
2. 超链接文案的格式化；
3. 导出功能；
4. `列设置`功能

### API

#### columns: <ColumnProps & CustomizedColumnProps>[]

表头配置。

在 `antd` 的 `columns` 的基础上增加一下功能：

1. `dataType`：可以指定一些预置的 `render` 行为。
2. `exportable`：是否允许导出此列。
3. `exportDataIndexes`：此列 `dataIndex` 对应的值是个对象，可通过此 API 配置需要导出的属性
4. `defaultVisible`：【非受控】此列是否显示
5. `visible`: 【受控】此列是否显示

### showExport

⚠️⚠️⚠️ 开启 showExport 时，rowSelection 必须开启且需要将 selectedRows 作为参数传入 ⚠️⚠️⚠️
例

```javascript
import { TinyTable } from '@manyun/dc-brain.legacy.components';

<TinyTable
  rowSelection={{
    selectedRowKeys,
    selectedRows,
    onChange: this.onChange,
    //...other
  }}
>
```

展示导出功能，分为两种情况 1.有选中项是导出选中项。2.无选中项是导出所有数据需要调用后端接口

### actions: ReactElement[]

```javascript
import { Button } from '@galiojs/awesome-antd';
import { TinyTable } from '@manyun/dc-brain.legacy.components';

<TinyTable
  rowSelection={{
    selectedRowKeys,
    selectedRows,
    onChange: this.onChange,
    //...other
  }}
  actions={[<Button key={1}>button1</Button>, <Button key={2}>button2</Button>]}
>
```

与导出按钮在同一列的按钮，数组中的元素必须有 key 值，导出功能默认在最后一个

### exportServices

showExport 为 true 时必填

### DEMO

本地调试时访问 `/page/component/demo/tiny-table` 页面即可。
