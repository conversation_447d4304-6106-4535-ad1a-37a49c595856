import React from 'react';

import { TinyTable } from '@manyun/dc-brain.legacy.components';

export function Demo() {
  return (
    <TinyTable
      containerStyle={{ padding: 16 }}
      actionsWrapperStyle={{ justifyContent: 'flex-end' }}
      editColumnsButton={{ enabled: true, cacheId: 'page:tiny-table_demo_cache-id' }}
      columns={[
        {
          title: 'Title',
          dataIndex: 'title',
          visible: false, // 永远不显示此列
        },
        {
          title: 'Date',
          dataIndex: 'date',
          dataType: 'date',
          defaultVisible: false, // 初次渲染时不显示此列，可在列设置中修改为显示此列
        },
        {
          title: 'DateTime',
          dataIndex: 'datetime',
          dataType: 'datetime',
          visible: true, // 永远显示此列，不可在列设置中隐藏此列
        },
        {
          title: 'External Link',
          dataIndex: 'externalLink',
          dataType: {
            type: 'link',
            options: {
              to: 'https://baidu.com',
            },
          },
        },
        {
          title: 'Internal Link',
          dataIndex: 'internalLink',
          dataType: {
            type: 'link',
            options: {
              to: '/home',
            },
          },
        },
        {
          title: 'Internal Link With Params',
          dataIndex: 'internalLinkWithParams',
          dataType: {
            type: 'link',
            options: {
              to(__ /* 这里没用到 */, { title }) {
                return `/home?title=${title}`;
              },
              text: 'Custom Link Text',
            },
          },
        },
        {
          title: '创建人',
          dataIndex: 'creator',
          exportDataIndexes: [
            { title: '创建人姓名', dataIndex: ['creator', 'userName'] },
            { title: '创建人id', dataIndex: ['creator', 'id'] },
          ],
        },
      ]}
      dataSource={[
        {
          key: 'row-1',
          title: 'This is a title.',
          date: new Date('01/01/2017 20:01:02').getTime(),
          datetime: new Date('01/01/2017 20:01:02').getTime(),
          externalLink: 'BAIDU',
          internalLink: 'Home',
          internalLinkWithParams: 'Home with title',
        },
      ]}
    />
  );
}

export default Demo;
