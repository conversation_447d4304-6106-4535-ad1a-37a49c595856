import React, { Component } from 'react';

import { Card } from '@manyun/base-ui.ui.card';
import { Space } from '@manyun/base-ui.ui.space';
import { Tabs } from '@manyun/base-ui.ui.tabs';

export function Level({ color, level, number, selected }) {
  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
      }}
    >
      <div
        style={{
          display: 'inline-block',
          width: 6,
          height: 6,
          borderRadius: '50%',
          backgroundColor: color,
        }}
      />
      <div
        style={{
          display: 'inline-block',
          margin: '0 6px',
        }}
      >
        {level}
      </div>
      <div>{number || 0}</div>
    </div>
  );
}

export default class ConditionalFilterCard extends Component {
  onConditionalFilterCardChange = classificationRecord => {
    this.props.onChangeCard(classificationRecord);
  };

  render() {
    const { categoryDisplayBarList } = this.props;

    const tabList = Array.from(new Set(categoryDisplayBarList.map(item => item.deviceType)));

    return (
      <Tabs>
        {tabList.map(item => {
          const categoryDisplayBar = categoryDisplayBarList.find(
            categoryDisplayBarItem => categoryDisplayBarItem.deviceType === item
          );
          const tabPaneCategoryDisplayBarList = categoryDisplayBarList.filter(
            categoryDisplayBarItem => categoryDisplayBarItem.deviceType === item
          );
          return (
            <Tabs.TabPane
              key={item}
              style={{ width: '100%', overflowX: 'auto' }}
              tab={`${categoryDisplayBar.type}(${tabPaneCategoryDisplayBarList.length})`}
            >
              <Space>
                {tabPaneCategoryDisplayBarList.map(item => (
                  <Card
                    key={item.type}
                    style={{ width: 300 }}
                    onClick={() => this.onConditionalFilterCardChange(item)}
                  >
                    <div style={{ display: 'flex', justifyContent: 'space-between	' }}>
                      <div style={{ width: '52%' }}>
                        <div style={{ fontSize: '24px' }}>{item.totalCount || 0}</div>
                        <div style={{ fontSize: '12px' }}>{item.type}</div>
                      </div>
                      <div style={{ width: '46%' }}>
                        <Level
                          color="#E93A3A"
                          level="一级"
                          number={item[1]}
                          selected={item.selected}
                        />
                        <Level
                          color="#FB8C00"
                          level="二级"
                          number={item[2]}
                          selected={item.selected}
                        />
                        <Level
                          color="#2889EC"
                          level="三级"
                          number={item[3]}
                          selected={item.selected}
                        />
                      </div>
                    </div>
                  </Card>
                ))}
              </Space>
            </Tabs.TabPane>
          );
        })}
      </Tabs>
    );
  }
}
