import React, { useEffect, useState } from 'react';

import flatten from 'lodash.flatten';
import uniq from 'lodash.uniq';

import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';

import { importMintainConfig } from '@manyun/ticket.state.ticket';

import DataTable from '@manyun/dc-brain.legacy.pages/maintain-config/list/components/data-card/data-table';

function ConfigModal({
  visible,
  setVisible,
  maintenanceType,
  deviceType,
  setImportMaintenanceConfigTables,
  optConfigs,
  setOptConfigs,
}) {
  const [selectedMaintainConfig, setSelectedMaintainConfig] = useState([]);

  useEffect(() => {
    setSelectedMaintainConfig(optConfigs);
  }, [visible, optConfigs, setSelectedMaintainConfig]);

  return (
    <Modal
      width={800}
      title="选择维护配置"
      visible={visible}
      onCancel={() => setVisible(false)}
      destroyOnClose
      onOk={() => {
        if (!selectedMaintainConfig || !selectedMaintainConfig.length) {
          message.error('还未选择任何配置!');
          return;
        }
        if (optConfigs.length) {
          Modal.confirm({
            title: '注意：导入后会覆盖已导入数据，请谨慎导入',
            onOk() {
              setVisible(false);
              setOptConfigs(selectedMaintainConfig);
              const deviceTypeList = uniq(
                flatten(selectedMaintainConfig.map(item => item.deviceTypeList))
              );
              const tables = importMintainConfig(selectedMaintainConfig);
              setImportMaintenanceConfigTables({ ...tables, deviceTypeList });
            },
            okText: '确定',
            cancelText: '取消',
            onCancel() {
              setVisible(false);
            },
          });
        } else {
          setVisible(false);
          setOptConfigs(selectedMaintainConfig);
          const deviceTypeList = uniq(
            flatten(selectedMaintainConfig.map(item => item.deviceTypeList))
          );
          const tables = importMintainConfig(selectedMaintainConfig);
          setImportMaintenanceConfigTables({ ...tables, deviceTypeList });
        }
      }}
    >
      <DataTable
        pattern={true}
        selectedMaintainConfig={selectedMaintainConfig}
        setSelectedMaintainConfig={setSelectedMaintainConfig}
        maintenanceType={maintenanceType}
        deviceType={deviceType}
      />
    </Modal>
  );
}

export default ConfigModal;
