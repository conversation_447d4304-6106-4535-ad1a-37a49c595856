import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';

import { ticketConfigActions } from '@manyun/dc-brain.legacy.redux/actions/ticketConfigActions';

import ConfigModal from './components/config-modal';

/**
 * @typedef {object} Props
 * @property {string} maintenanceType  维护类型
 * @property {string} deviceType  设备类型
 */

function MaintainConfigButtonModal({
  maintenanceType,
  // deviceType,
  updateSearchValues,
  setImportMaintenanceConfigTables,
  setOptConfigs,
  optConfigs,
}) {
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    updateSearchValues({
      maintenanceType: {
        name: 'maintenanceType',
        value: maintenanceType,
      },
      deviceType: {
        name: 'deviceType',
        value: {
          thirdCategorycode: null,
          firstCategoryName: null,
          secondCategoryName: null,
        },
      },
    });
  }, [maintenanceType, updateSearchValues]);

  return (
    <>
      <Button
        type="primary"
        disabled={maintenanceType ? false : true}
        onClick={() => setVisible(true)}
      >
        导入配置
      </Button>
      <ConfigModal
        visible={visible}
        setVisible={setVisible}
        maintenanceType={maintenanceType}
        // deviceType={deviceType}
        setImportMaintenanceConfigTables={setImportMaintenanceConfigTables}
        optConfigs={optConfigs}
        setOptConfigs={setOptConfigs}
      />
    </>
  );
}

const mapDispatchToProps = {
  updateSearchValues: ticketConfigActions.updateMaintainSearchValues,
};

export default connect(null, mapDispatchToProps)(MaintainConfigButtonModal);
