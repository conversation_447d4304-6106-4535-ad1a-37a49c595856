import React from 'react';

import { CLICK_RENDER_TYPE_MAP } from '@manyun/dc-brain.legacy.components/statistics';
import { STATUS_MAP } from '@manyun/dc-brain.legacy.constants/statusColor';

export default [
  [
    {
      key: 'it-load',
      text: 95,
      unit: '%',
      description: 'IT 负荷率',
      status: STATUS_MAP.NORMAL,
      clickRender: props => ({
        type: CLICK_RENDER_TYPE_MAP.MODAL,
        props: {
          title: 'IT 负荷率',
          children: <pre className="code">{JSON.stringify(props, null, 2)}</pre>,
        },
      }),
    },
    {
      key: 'real-time-electric-power-by-room',
      text: 343.62,
      unit: 'kW',
      description: '包间实时功率',
      status: STATUS_MAP.ALARM,
    },
    {
      key: 'generator-status',
      text: '风险',
      description: '柴发状态',
      status: STATUS_MAP.WARNING,
    },
    {
      key: 'ups-status',
      text: '异常',
      description: 'UPS 状态',
      status: STATUS_MAP.ALARM,
    },
    {
      key: 'pump-status',
      text: '正常',
      description: '冷冻泵状态',
      status: STATUS_MAP.NORMAL,
    },
  ],
  [
    {
      key: 'pending-alarms',
      text: 21,
      description: '待处理告警',
      clickRender: () => ({
        type: CLICK_RENDER_TYPE_MAP.LINK,
        props: {
          to: '/',
        },
      }),
    },
  ],
];
