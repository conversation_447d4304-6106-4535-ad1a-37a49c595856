import React from 'react';

import { Cell, Table, Td, Tr } from './components';

export { CLICK_RENDER_TYPE_MAP } from './components';

/**
 * 用于 机房工作台、包间视图 页面的概览数据展示。
 * @param {object} props Component Props
 */
export function Statistics({ style, cellStyle, textStyle, dataSource = [] }) {
  return (
    <Table style={style}>
      <tbody>
        {dataSource.map((cells, rowIdx) => (
          // eslint-disable-next-line react/no-array-index-key
          <Tr key={rowIdx.toString()}>
            {cells.map(
              ({ key, colSpan, width, text, unit = '', description, status, clickRender }) => (
                <Td
                  key={key}
                  style={{ width, minWidth: width, maxWidth: width, overflow: 'hidden' }}
                  colSpan={colSpan}
                >
                  <Cell
                    cellStyle={cellStyle}
                    textStyle={textStyle}
                    code={key}
                    text={text}
                    unit={unit}
                    description={description}
                    status={status}
                    clickRender={clickRender}
                  />
                </Td>
              )
            )}
          </Tr>
        ))}
      </tbody>
    </Table>
  );
}

export default Statistics;
