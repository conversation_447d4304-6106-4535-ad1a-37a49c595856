import React, { useState } from 'react';
import { Link } from 'react-router-dom';

import styled from 'styled-components';

import { Modal } from '@manyun/base-ui.ui.modal';
import { Typography } from '@manyun/base-ui.ui.typography';

import StatusText from '@manyun/dc-brain.legacy.components/status-text';

export const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
`;

export const Tr = styled.tr`
  :not(:last-child) {
    border-bottom: 1px solid var(--border-color-split);
  }
`;

export const Td = styled.td`
  :not(:first-child) {
    border-left: 1px solid var(--border-color-split);
  }
`;

export const CellWrapper = styled.div`
  padding: 1em;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  > * + * {
    margin-top: 0.25em;
  }
`;

export const Text = styled(StatusText)`
  font-size: 24px;
`;

export function Cell({
  cellStyle,
  textStyle,
  code,
  text,
  unit = '',
  description,
  status,
  clickRender,
}) {
  const cell = (
    <CellWrapper style={cellStyle}>
      <Typography.Text type="secondary">{description}</Typography.Text>
      <div style={textStyle}>
        <StatusText status={status}>
          {React.isValidElement(text) ? text : String(text) + unit}
        </StatusText>
      </div>
    </CellWrapper>
  );

  if (typeof clickRender != 'function') {
    return cell;
  }

  return (
    <ClickableCell config={clickRender({ key: code, text, unit, description, status })}>
      {onClick => React.cloneElement(cell, { onClick, style: CLICKABLE_CELL_STYLE })}
    </ClickableCell>
  );
}

export const CLICK_RENDER_TYPE_MAP = {
  MODAL: 'Modal',
  LINK: 'Link',
};

/**
 * @type {React.CSSProperties}
 */
const CLICKABLE_CELL_STYLE = {
  cursor: 'pointer',
};

function ClickableCell({ config, children }) {
  const [visible, setVisible] = useState(false);

  if (!config) {
    return children();
  }

  const { type, props } = config;

  if (type === CLICK_RENDER_TYPE_MAP.LINK) {
    return <Link {...props}>{children()}</Link>;
  }

  if (type === CLICK_RENDER_TYPE_MAP.MODAL) {
    const onCancel = () => {
      setVisible(false);
    };
    const onOpen = () => {
      setVisible(true);
    };

    const { children: modalChildren, ...restModalProps } = props;

    return (
      <>
        {children(onOpen)}
        <Modal {...restModalProps} open={visible} onCancel={onCancel}>
          {React.isValidElement(modalChildren) && modalChildren}
          {typeof modalChildren == 'function' && modalChildren(visible)}
        </Modal>
      </>
    );
  }

  throw new Error(`type(${type}) not supported.`);
}
