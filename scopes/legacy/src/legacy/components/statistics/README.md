## Statistics

用于 机房工作台、包间视图 页面的概览数据展示。

<img alt="statistics-by-room" src="statistics-by-room.jpg" />

### API

#### dataSource: Array<Cell[]>

```typescript
import { STATUS_MAP } from '@manyun/dc-brain.legacy.components/status-text';
import { CLICK_RENDER_TYPE_MAP } from '@manyun/dc-brain.legacy.components/statistics';

interface CellBase {
  // React 需要使用的唯一标识
  key: string;

  // 指标文案
  text: string|number;

  // 指标单位
  unit?: string;

  // 指标描述信息
  description?: string;

  // 指标状态（正常、预警、告警、引用）
  status?: keyof Object.values(STATUS_MAP);
}

interface Cell extends CellBase {
  // 鼠标单击单元格时的渲染逻辑
  // 支持 2 种：
  // 1. 弹窗显示额外的信息，可自定义额外信息的组件
  // 2. 超链接到站内其他页面
  clickRender?(props: CellBase): {
    type: keyof Object.values(CLICK_RENDER_TYPE_MAP);
    props: object;
  };
}
```

### DEMO

本地调试时访问 `/page/component/demo/statistics` 页面即可。
