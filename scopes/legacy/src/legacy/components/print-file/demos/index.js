import React, { useEffect, useState } from 'react';

import template from 'lodash/template';

import PrintFile from '@manyun/dc-brain.legacy.components/print-file';

export default function Demo() {
  const [contents, setContents] = useState('');
  useEffect(() => {
    const obj = template(
      `<style>
      table {
        text-align: center ;
        width:700px
      }
      thead > tr {
        height:55px
      }
      .remark{
        font-weight:700
      }
      .device {
        height:35px
      }
      
      </style>
      <table border='1' cellspacing='0' cellpadding='0' align='center'>
        <thead>
            <tr>
                <th colSpan="6"><%- taskTitle %></th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td class='remark'>出门单号</td>
                <td><%- taskNo %></td>
                <td class='remark'>申请人</td>
                <td><%- user %></td>
                <td class='remark'>预计出门时间</td>
                <td><%- taskSubTypeTimeTxt %></td>
            </tr>
            <tr class='device'>
                <td colSpan="6">出门设备列表</td>
            </tr>
            <tr>
                <td colSpan="3">设备类型</td>
                <td colSpan="3">SN</td>
            </tr>
           <%  codeData.forEach((device) => { %><tr><td colSpan="3"><%- device.deviceType %></td><td colSpan="3"><%- device.guid %></td></tr><% } ); %>
        </tbody>
        <tr style='height:115px;line-heighe:115px'>
            <td colSpan="3">授权人签字：___________</td>
            <td colSpan="3">门卫签字：___________</td>
        </tr>
      </table>`
    );
    const str = obj({
      taskTitle: 'EC01机房出门单',
      taskNo: '2342315',
      user: '廖新波',
      taskSubTypeTimeTxt: '2021-01-02 10:00',
      codeData: [
        {
          deviceType: '高压',
          guid: '123',
        },
        {
          deviceType: '高压隔离柜',
          guid: 'rrrrr',
        },
        {
          deviceType: '计量柜',
          guid: '66788',
        },
      ],
    });
    setContents(str);
  }, [setContents]);
  return <PrintFile contents={contents} />;
}
