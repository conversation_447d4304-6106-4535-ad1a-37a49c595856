import React from 'react';

import { PrintOutlined } from '@manyun/base-ui.icons/dist/outlined/print';
import { Button } from '@manyun/base-ui.ui.button';

function PrintFile({ contents, renderComponent, bthProps }) {
  const print = () => {
    const a = window.open('', '');
    a.document.write(contents);
    a.document.close();
    a.print();
  };
  return (
    <Button onClick={print} type="link" {...(bthProps || {})}>
      {renderComponent && renderComponent()}
      {!renderComponent && <PrintOutlined />}
    </Button>
  );
}

export default PrintFile;
