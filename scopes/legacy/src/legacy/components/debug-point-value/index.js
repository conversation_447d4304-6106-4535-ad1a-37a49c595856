import React from 'react';

import { Typography } from '@manyun/base-ui.ui.typography';

import { env } from '@manyun/dc-brain.app-env.react-app-envs';

import GutterWrapper from '../gutter-wrapper';

/**
 * @deprecated Use `@manyun/monitoring.ui.point-data-renderer`
 *
 * @param {object} props
 * @param {'default'|'small'} [props.size='default']
 * @param {'horizontal'|'vertical'} [props.direction='horizontal'] `horizontal` -> 1 line, `vertical` -> 2 lines.
 * @param {boolean} [props.showDeviceType=true] show `deviceType` or not.
 * @param {(value: any) => any} [props.nonDebugRender]
 */
export default function DebugPointValue({
  size = 'small',
  direction = 'horizontal',
  showDeviceType = true,
  deviceType = '?',
  pointCode = '?',
  value = '?',
  originalValue = '?',
  nonDebugRender = renderValue,
}) {
  if (!env.__DEBUG__) {
    return nonDebugRender(value);
  }

  const fullPointCode = showDeviceType ? deviceType + '.' + pointCode : pointCode;
  const text = value && typeof value === 'object' ? `${originalValue}(${value.NAME})` : value;

  if (direction === 'vertical') {
    return (
      <GutterWrapper style={SIZE_STYLES_MAP[size]} direction="vertical" size="4px" flex column>
        <Typography.Text code>{fullPointCode}</Typography.Text>
        <Typography.Text code>={text}</Typography.Text>
      </GutterWrapper>
    );
  }

  return (
    <Typography.Text style={SIZE_STYLES_MAP[size]} code>
      {fullPointCode}={text}
    </Typography.Text>
  );
}

const SIZE_STYLES_MAP = {
  small: {
    fontSize: 12,
  },
};

// In case the `value` is an object.
// React can't render an object directly.
const renderValue = value => JSON.stringify(value);
