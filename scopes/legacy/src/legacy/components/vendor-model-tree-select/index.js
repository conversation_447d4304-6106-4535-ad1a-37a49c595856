import React, { useEffect, useRef, useState } from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { TreeSelect } from '@manyun/base-ui.ui.tree-select';

import { vendorService } from '@manyun/dc-brain.legacy.services';

export function VandorModalTreeSelect({ forwardedRef, ...props }) {
  const [treeData, setTreeData] = useState([]);
  const [treeExpandedKeys, setTreeExpandedKeys] = useState([]);
  const [inSearch, setInSearch] = useState(false);

  const treeSelectRef = useRef(null);

  useEffect(() => {
    if (!props.deviceType) {
      return;
    }
    (async () => {
      const { response, error } = await vendorService.fetchVendoeModal({
        deviceType: props.deviceType,
      });
      if (error) {
        message.error(error);
        return;
      }
      const treeData = getVendorModalTreeData(response.data);
      setTreeData(treeData);
    })();
  }, [props.deviceType]);

  const extraProps = { treeDefaultExpandAll: true };
  if (!inSearch) {
    extraProps.treeExpandedKeys = treeExpandedKeys;
  }

  return (
    <div ref={forwardedRef}>
      <TreeSelect
        ref={treeSelectRef}
        treeDataSimpleMode
        multiple
        labelInValue
        showSearch
        treeData={treeData}
        listHeight={200}
        style={{ width: 200 }}
        {...props}
        onTreeExpand={expandedKeys => {
          setTreeExpandedKeys(expandedKeys);
        }}
        onSearch={value => {
          setInSearch(true);
        }}
        onChange={(value, label, extra) => {
          const metaType = extra.triggerNode?.props.metaType;
          if (metaType === 'VENDOR') {
            if (!extra.triggerNode.props.expanded) {
              setTreeExpandedKeys(expandedKeys => [
                ...expandedKeys,
                extra.triggerNode.props.metaCode,
              ]);
            } else {
              setTreeExpandedKeys(expandedKeys =>
                expandedKeys.filter(key => key !== extra.triggerNode.props.dataRef?.value)
              );
            }
            if (treeSelectRef.current) {
              treeSelectRef.current.rcTreeSelect.setOpenState(true);
            }
            return;
          }
          props.onChange(value);
        }}
        {...extraProps}
      />
    </div>
  );
}

export default React.forwardRef((props, ref) => (
  <VandorModalTreeSelect forwardedRef={ref} {...props} />
));

function getVendorModalTreeData(data, vendorCode) {
  return data.map(item => {
    if (item.children && item.children.length) {
      return {
        ...item,
        value: item.metaCode,
        title: item.metaCode,
        children: getVendorModalTreeData(item.children, item.metaCode),
      };
    }
    if (!vendorCode) {
      return {
        ...item,
        value: item.metaCode,
        title: item.metaCode,
      };
    }
    return {
      ...item,
      value: `${vendorCode}_$$_${item.metaCode}`,
      title: item.metaCode,
    };
  });
}
