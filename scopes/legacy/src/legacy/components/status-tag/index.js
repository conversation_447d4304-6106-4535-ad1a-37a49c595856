import styled from 'styled-components';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';

export const STATUS_COLOR__MAP = {
  GREEN: 'green',
  RED: 'red',
  BLUE: 'blue',
  BLACK: 'black',
  GREY: 'grey',
  GREY30: 'grey-30',
  DARK_GREY: 'dark-grey',
  HIGHLIGHT: 'highlight',
  GREYWHITE: 'greywhite',
  ORANGE: 'orange',
  WHITE30: 'white-30',
  AVG: 'avg',
};

/**
 * color variables are referenced from `src/styles/variables.css`.
 */
export const COLOR_MAP = {
  [STATUS_COLOR__MAP.GREEN]: 'var(--color-normal)',
  [STATUS_COLOR__MAP.RED]: `var(--${prefixCls}-warning-color)`,
  [STATUS_COLOR__MAP.BLUE]: 'var(--color-reference)',
  [STATUS_COLOR__MAP.BLACK]: 'var(--color-grey)',
  [STATUS_COLOR__MAP.GREY]: 'var(--color-grey)',
  [STATUS_COLOR__MAP.GREY30]: 'var(--color-grey-2)',
  [STATUS_COLOR__MAP.DARK_GREY]: 'var(--color-dark-grey)',
  [STATUS_COLOR__MAP.HIGHLIGHT]: 'var(--color-white)',
  [STATUS_COLOR__MAP.GREYWHITE]: 'var(--color-white)',
  [STATUS_COLOR__MAP.ORANGE]: `var(--${prefixCls}-error-color)`,
  [STATUS_COLOR__MAP.WHITE30]: 'var(--color-white)',
  [STATUS_COLOR__MAP.AVG]: 'var(--color-avg)',
  default: 'unset',
};

export const BACKGROUND_COLOR_MAP = {
  [STATUS_COLOR__MAP.GREEN]: 'var(--background-color-green)',
  [STATUS_COLOR__MAP.RED]: 'var(--background-color-red)',
  [STATUS_COLOR__MAP.BLUE]: 'var(--background-color-blue)',
  [STATUS_COLOR__MAP.BLACK]: 'var(--background-color-black)',
  [STATUS_COLOR__MAP.GREY]: 'var(--background-color-grey)',
  [STATUS_COLOR__MAP.GREY30]: 'var(--background-color-grey-30)',
  [STATUS_COLOR__MAP.DARK_GREY]: 'var(--background-color-dark-grey)',
  [STATUS_COLOR__MAP.HIGHLIGHT]: 'var(--background-color-highLight)',
  [STATUS_COLOR__MAP.GREYWHITE]: 'var(--background-tag-grey)',
  [STATUS_COLOR__MAP.ORANGE]: 'var(--background-color-orange)',
  [STATUS_COLOR__MAP.WHITE30]: 'var(--background-color-white-30)',
  [STATUS_COLOR__MAP.AVG]: 'var(--background-color-avg)',
  default: 'unset',
};
/**
 * Given a `status`, set the text color.
 */
export const StatusText = styled.div`
  color: ${({ status = 'default' }) => COLOR_MAP[status]};
  background-color: ${({ status = 'default' }) => BACKGROUND_COLOR_MAP[status]};
  padding: 0 6px;
  text-align: center;
  width: auto;
  display: inline-block;
  border-radius: 4px;
  height: 20px;
  line-height: 20px;
`;

export default StatusText;
