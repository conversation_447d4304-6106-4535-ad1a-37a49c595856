## TinyDrawer

封装了下 `<Drawer />`，给了一个默认固定在底部的 `footer`，包含 2 个按钮：“取消”、“提交”。

### API

#### submitButtonDisabled: boolean

“提交”按钮的启禁用状态

#### submitButtonLoading: boolean

“提交”按钮的加载中状态

#### onSubmit: () => void

一般用于提交整个抽屉的内容，并关闭抽屉

#### onCancel: () => void

一般用于关闭抽屉

#### footer: null | React.ReactElement

如果不指定 `footer`，将会使用内部封装的 `<Footer />`，如果不需要 `footer`，可以直接使用 `<Drawer />` 或 `<TinyDrawer footer={null} />`。
