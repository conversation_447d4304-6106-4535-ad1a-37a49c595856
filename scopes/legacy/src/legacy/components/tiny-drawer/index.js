import React from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import { Space } from '@manyun/base-ui.ui.space';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';

import styles from './tiny-drawer.module.less';

const DEFAULT_FOOTER_HEIGHT = 55;

function Footer({
  submitButtonDisabled,
  submitButtonLoading,
  onSubmit,
  onCancel,
  cancelButtonText = '取消',
  confirmButtonText = '提交',
}) {
  return (
    <Space>
      <Button onClick={onCancel}>{cancelButtonText}</Button>
      <Button
        type="primary"
        disabled={submitButtonDisabled}
        loading={submitButtonLoading}
        onClick={onSubmit}
      >
        {confirmButtonText}
      </Button>
    </Space>
  );
}

export function TinyDrawer({
  children,
  submitButtonDisabled,
  submitButtonLoading,
  onSubmit,
  onCancel,
  hasExtra,
  footer = (
    <Footer
      submitButtonDisabled={submitButtonDisabled}
      submitButtonLoading={submitButtonLoading}
      onSubmit={onSubmit}
      onCancel={onCancel}
    />
  ),
  ...props
}) {
  const { drawerStyle = {}, ...rest } = props;
  let extra = null;
  if (hasExtra) {
    footer = null;
    extra = (
      <Footer
        submitButtonDisabled={submitButtonDisabled}
        submitButtonLoading={submitButtonLoading}
        onSubmit={onSubmit}
        onCancel={onCancel}
        cancelButtonText="返回"
        confirmButtonText="更改密码"
      />
    );
  }
  return (
    <Drawer
      drawerStyle={{
        paddingBottom:
          DEFAULT_FOOTER_HEIGHT /* 修复 Footer 所占的高度，以免 overflow 时挡住 drawer 的 body 内容 */,
        ...drawerStyle,
      }}
      extra={extra}
      {...rest}
    >
      {children}
      {footer && (
        <div className={styles.footerWrapper}>
          <GutterWrapper>{footer}</GutterWrapper>
        </div>
      )}
    </Drawer>
  );
}

TinyDrawer.FOOTER_HEIGHT = DEFAULT_FOOTER_HEIGHT;

export default TinyDrawer;
