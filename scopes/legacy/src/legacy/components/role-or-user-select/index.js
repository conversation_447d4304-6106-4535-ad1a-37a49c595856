import React from 'react';

import { ApiSelect, Select } from '@galiojs/awesome-antd';

import UserSelect from '@manyun/dc-brain.legacy.components/user-select';
import { roleManageService } from '@manyun/dc-brain.legacy.services';

export default function RoleOrUserSelect({ disabled, value = [], onChange }) {
  const type = value[0] || 'ROLE';

  return (
    <>
      <Select
        style={{ width: 100 }}
        value={type}
        onChange={type => {
          onChange([type, undefined]);
        }}
        disabled={disabled}
      >
        <Select.Option value="ROLE">角色</Select.Option>
        <Select.Option value="USER">用户</Select.Option>
      </Select>
      {type === 'ROLE' && (
        <ApiSelect
          disabled={disabled}
          style={{ marginLeft: 8, width: 'calc(100% - 100px - 8px)' }}
          showSearch
          labelInValue
          mode="multiple"
          dataService={roleDataService}
          value={value[1]}
          onChange={roleObj => {
            onChange([value[0], roleObj]);
          }}
        />
      )}
      {type === 'USER' && (
        <UserSelect
          disabled={disabled}
          style={{ marginLeft: 8, width: 'calc(100% - 100px - 8px)' }}
          labelInValue
          mode="multiple"
          value={
            value[1]
              ? value[1].map(user => ({ key: Number(user.key), label: user.label }))
              : undefined
          }
          onChange={userObj => {
            onChange([value[0], userObj]);
          }}
        />
      )}
    </>
  );
}

async function roleDataService(keyword) {
  const { response, error } = await roleManageService.fetchRoleList({
    searchType: 'ROLE_NAME',
    pageNo: 1,
    pageSize: 500,
    searchName: keyword,
  });

  if (error) {
    return [];
  }

  return response.data.map(({ roleName, roleCode }) => ({
    label: roleName,
    value: roleCode,
  }));
}
