import { useLocation } from 'react-router-dom';

import URLSearchParams from 'url-search-params';

function getSearchParams(search = window.location.search) {
  const searchParams = new URLSearchParams(search);
  const values = {};
  searchParams.forEach((value, key) => {
    values[key] = value;
  });

  return { searchParams, values };
}

export function useSearchParams() {
  const { search } = useLocation();
  const { searchParams } = getSearchParams(search);

  return searchParams;
}

export function useSearchParamValues() {
  const { search } = useLocation();
  const { values } = getSearchParams(search);

  return values;
}
