## SearchParams

通过 `react-router-dom` 的 `useLocation hook` 及 `URLSearchParams` 拿到当前页面的 `search params`。

### Hooks

#### useSearchParams

得到当前 [URLSearchParams](https://developer.mozilla.org/en-US/docs/Web/API/URLSearchParams) 的实例，可以调用实例的 `append(), delete(), set(), get()...` 等方法。

#### useSearchParamValues

得到当前 URL 的 `search params`。

> 假设 URL 是 `http://localhost:3000/login?ref=/home`，那么 `ReturnValue` 是 `{ ref: '/home' }` 对象

### 示例

##### useSearchParamValues

消费方式二：使用 `Hooks`。

```jsx
// SubComponent
import React from 'react';
import { useSearchParamValues } from '@manyun/dc-brain.legacy.components';

export default function SubComponent() {
  const values = useSearchParamValues();

  return <div>{JSON.stringify(values)}</div>;
}
```
