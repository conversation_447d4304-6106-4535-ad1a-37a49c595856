/* eslint-disable no-console */
import React from 'react';

import Form from '@ant-design/compatible/es/form';

import UserGroupSelect from '@manyun/dc-brain.legacy.components/user-group-select';

export default function Demo() {
  return (
    <Form layout="inline">
      <Form.Item label="用户组（单选）">
        <UserGroupSelect style={{ width: 200 }} allowClear onChange={console.log} />
      </Form.Item>
      <Form.Item label="用户组（多选）">
        <UserGroupSelect style={{ width: 200 }} allowClear mode="multiple" onChange={console.log} />
      </Form.Item>
    </Form>
  );
}
