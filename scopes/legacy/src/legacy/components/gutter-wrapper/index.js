import styled from 'styled-components';

const MODE_MAP = {
  HORIZONTAL: 'HORIZONTAL',
  VERTICAL: 'VERTICAL',
};

/**
 * @deprecated Use `@manyun/base-ui.ui.space` instead
 *
 * @type {import('styled-components').StyledComponent<"div", any, { direction: 'horizontal'|'vertical' }>}
 */
const GutterWrapper = styled.div`
  ${({ flexN }) => (Number.isNaN(Number(flexN)) ? '' : `flex: ${flexN};`)}
  display: ${({ flex = false }) => (flex ? 'flex' : 'block')};
  max-height: 100%;
  flex-direction: ${({ column }) => (column ? 'column' : 'row')};
  justify-content: ${({ center, justifyContent = 'unset' }) =>
    center ? 'center' : justifyContent};
  align-items: ${({ center, alignItems = 'unset' }) => (center ? 'center' : alignItems)};
  padding: ${({ padding = 0 }) => (padding ? padding : '0')};

  > * + * {
    margin-top: ${({ mode = MODE_MAP.HORIZONTAL, direction = mode, size = '1rem' }) =>
      String(direction).toUpperCase() === MODE_MAP.VERTICAL ? size : '0'};
    margin-left: ${({ mode = MODE_MAP.HORIZONTAL, direction = mode, size = '1rem' }) =>
      String(direction).toUpperCase() === MODE_MAP.HORIZONTAL ? size : '0'};
  }
`;

export default GutterWrapper;
