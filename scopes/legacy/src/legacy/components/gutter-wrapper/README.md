## GutterWrapper

排水渠间距组件

## 用法

```jsx
import { GutterWrapper } from '@manyun/dc-brain.legacy.components';

// 横向布局
function Horizontal() {
  return (
    <GutterWrapper>
      <button>BUTTON 1</button>
      <button>BUTTON 2</button>
    </GutterWrapper>
  );
}

// 纵向布局
function Vertical({ children }) {
  return <GutterWrapper mode="vertical">{children}</GutterWrapper>;
}

// 嵌套
function Nested() {
  return (
    <Vertical>
      <Horizontal />
      <Horizontal />
    </Vertical>
  );
}

// Flex
function Horizontal() {
  return (
    <GutterWrapper flex>
      <div>DIV 1</div>
      <div>DIV 2</div>
    </GutterWrapper>
  );
}

// antd Input.Group
function InputGroupDemo() {
  return (
    <GutterWrapper mode="vertical">
      <Alert message="Some message..." />
      <GutterWrapper flex>
        <Button type="primary">Button</Button>
        <Input
          style={{ width: 300 }}
          addonBefore={
            <Select defaultValue="option-1">
              <Select.Option value="option-1">Option 1</Select.Option>
              <Select.Option value="optionn-2">Option 2</Select.Option>
            </Select>
          }
        />
      </GutterWrapper>
    </GutterWrapper>
  );
}
```
