import React from 'react';

import get from 'lodash/get';
import omit from 'lodash/omit';

import { TreeSelect } from '@manyun/base-ui.ui.tree-select';

import { DataService } from '@manyun/dc-brain.legacy.components/renderProps';

/**
 * @typedef { import('antd-3/lib/tree-select').TreeSelectProps } TreeSelectProps
 *
 * @typedef {(node: any) => string} Callback
 *
 * @typedef FieldNames
 * @property { string | Callback } [key='value'] 指定节点信息中哪个属性作为 `key`。
 * @property { string | Callback } [value='value'] 指定节点信息中哪个属性作为 `value`。
 * @property { string | Callback } [title='title'] 指定节点信息中哪个属性作为 `title`。
 *
 * @typedef TreeData
 * @property {any[]} treeList 🌲
 *
 * @typedef CustomizedProps
 * @property {React.MutableRefObject<TreeSelect>} treeSelectRef
 * @property {boolean} [requestOnDidMount=false] 是否在 `componentDidMount` 阶段调用 `dataService`。
 * @property {FieldNames} fieldNames 自定义节点数据中 `key, value, title` 的字段。
 * @property {number[]} [disabledDepths=[]] 按节点深度禁用节点。
 * @property {number} [hiddenDepth] 按节点深度隐藏下一层级节点。
 * @property {string[]} [hiddenTreeNodeKeys=[]] 按key值隐藏节点
 * @property {() => Promise<TreeData>} dataService 🌲数据源服务。
 */

/**
 * 封装于 antd 的 TreeSelect
 * @augments { React.Component<TreeSelectProps & CustomizedProps> }
 */
export class ApiTreeSelect extends React.Component {
  /**
   * @type {Partial<CustomizedProps>}
   */
  static defaultProps = {
    requestOnDidMount: false,
    fieldNames: {
      key: 'value',
      value: 'value',
      title: 'title',
    },
  };

  /**
   * @type { React.RefObject<DataService> }
   */
  _dataServiceRef = React.createRef();

  _focusHandler = evt => {
    this._dataServiceRef.current.getData();

    const { onFocus } = this.props;
    if (typeof onFocus !== 'function') {
      return;
    }
    onFocus(evt);
  };

  refreshData = (forced = false, callback) => {
    this._dataServiceRef.current.getData(forced, callback);
  };

  render() {
    const {
      treeSelectRef,
      requestOnDidMount,
      dataService,
      fieldNames,
      disabledDepths,
      hiddenDepth,
      hiddenTreeNodeKeys,
    } = this.props;
    const loop = generateLoop({ fieldNames, disabledDepths, hiddenDepth, hiddenTreeNodeKeys });

    return (
      <DataService
        ref={this._dataServiceRef}
        requestOnDidMount={requestOnDidMount}
        dataService={dataService}
      >
        {({ data = { treeList: [] } }) => (
          <TreeSelect
            ref={treeSelectRef}
            style={{ width: '100%' }}
            listHeight={400}
            showSearch
            allowClear
            treeDefaultExpandAll
            treeNodeFilterProp="title"
            {...omit(this.props, CUSTOMIZED_PROPS)}
            onFocus={this._focusHandler}
          >
            {loop(data.treeList, loop)}
          </TreeSelect>
        )}
      </DataService>
    );
  }
}

export default ApiTreeSelect;

const { TreeNode } = TreeSelect;

const CUSTOMIZED_PROPS = [
  'treeSelectRef',
  'requestOnDidMount',
  'fieldNames',
  'disabledDepths',
  'dataService',
  'hiddenDepth',
  'hiddenTreeNodeKeys',
];

function generateLoop({
  fieldNames: { key, value, title },
  disabledDepths = [],
  hiddenDepth,
  hiddenTreeNodeKeys = [],
}) {
  return (data = [], loop, depth = 0) =>
    data.map(node => {
      const keyVal = getNodeProp(node, key);
      const valueVal = getNodeProp(node, value);
      const titleVal = getNodeProp(node, title);
      const disabled =
        disabledDepths && disabledDepths.length ? disabledDepths.includes(depth) : node.disabled;
      const hidden = hiddenTreeNodeKeys.includes(keyVal);

      if (hidden) {
        return null;
      }

      if (Array.isArray(node.children) && node.children.length && hiddenDepth !== depth) {
        return (
          <TreeNode
            key={keyVal}
            dataRef={node}
            value={valueVal}
            title={titleVal}
            disabled={disabled}
          >
            {loop(node.children, loop, depth + 1)}
          </TreeNode>
        );
      }

      return (
        <TreeNode
          key={keyVal}
          dataRef={node}
          isLeaf
          value={valueVal}
          title={titleVal}
          disabled={disabled}
        />
      );
    });
}

// function generateFilterTreeNode(nodeTitle) {
//   return (inputValue, node) => {
//     const title = getNodeProp(node, nodeTitle);

//     return String(title)
//       .toLowerCase()
//       .includes(String(inputValue).toLowerCase());
//   };
// }

function getNodeProp(node, prop) {
  if (typeof prop === 'function') {
    return prop(node);
  }
  return get(node, prop);
}
