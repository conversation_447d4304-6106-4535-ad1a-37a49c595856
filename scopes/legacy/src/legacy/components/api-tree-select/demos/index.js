/* eslint-disable no-console */
import React from 'react';

import Form from '@ant-design/compatible/es/form';

import { Button } from '@manyun/base-ui.ui.button';

import { ApiTreeSelect } from '@manyun/dc-brain.legacy.components';
import { getAlarmConfigurationTree } from '@manyun/dc-brain.legacy.services/treeDataService';

function Demo({ form: { getFieldDecorator, getFieldsValue } }) {
  return (
    <Form
      onSubmit={evt => {
        evt.preventDefault();
        console.log(getFieldsValue());
      }}
    >
      <Form.Item>
        {getFieldDecorator('demo')(
          <ApiTreeSelect
            fieldNames={{ key: 'metaCode', value: 'metaCode', title: 'metaName' }}
            disabledDepths={[0, 1]}
            dataService={getAlarmConfigurationTree}
          />
        )}
      </Form.Item>
      <Form.Item>
        <Button htmlType="submit">提交</Button>
      </Form.Item>
    </Form>
  );
}

export default Form.create()(Demo);
