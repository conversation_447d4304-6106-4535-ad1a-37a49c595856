import React from 'react';

import { Typography } from '@manyun/base-ui.ui.typography';

import GutterWrapper from '@manyun/dc-brain.legacy.components/gutter-wrapper';

import StatusText from './..';

export default function Demo() {
  return (
    <GutterWrapper style={{ height: '100vh' }} flex center column>
      <Typography.Title>No Background</Typography.Title>
      <GutterWrapper direction="horizontal" flex>
        <StatusText status="normal">Normal</StatusText>
        <StatusText status="alarm">Alarm</StatusText>
        <StatusText status="warning">Warning</StatusText>
        <StatusText status="disabled">Disabled</StatusText>
        <StatusText>Default</StatusText>
      </GutterWrapper>
      <Typography.Title>Background</Typography.Title>
      <GutterWrapper style={{ height: 20 }} direction="horizontal" flex>
        <StatusText background status="normal">
          Normal
        </StatusText>
        <StatusText background status="alarm">
          Alarm
        </StatusText>
        <StatusText background status="warning">
          Warning
        </StatusText>
        <StatusText background status="disabled">
          Disabled
        </StatusText>
        <StatusText background>Default</StatusText>
      </GutterWrapper>
    </GutterWrapper>
  );
}
