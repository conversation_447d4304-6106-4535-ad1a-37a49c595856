import React from 'react';

import { Tag } from '@manyun/base-ui.ui.tag';

import { getColorByStauts } from '@manyun/dc-brain.legacy.utils/statusColor';

/**
 * @deprecated Use `@manyun/monitoring.ui.point-data-renderer` instead
 *
 * Given a `status`, set the text color.
 * @type {React.FunctionComponent<Props>}
 *
 * @typedef Props
 * @property {boolean} background 是否显示 `Tag` 的背景色和边框色
 * @property {import('@manyun/dc-brain.legacy.constants/statusColor').STATUS_MAP} status
 */
export const StatusText = ({ style, status = 'default', children, background }) => {
  if (status === 'default') {
    return (
      <Tag style={{ color: 'unset', ...style }} shape={background ? undefined : 'typography'}>
        {children}
      </Tag>
    );
  }
  return (
    <Tag
      style={style}
      color={getColorByStauts(status)}
      shape={background ? undefined : 'typography'}
    >
      {children}
    </Tag>
  );
};

export default StatusText;
