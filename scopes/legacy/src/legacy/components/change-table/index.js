import dayjs from 'dayjs';
import React from 'react';

import { Divider } from '@manyun/base-ui.ui.divider';
import { Popover } from '@manyun/base-ui.ui.popover';
import { Space } from '@manyun/base-ui.ui.space';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import {
  CHANGE_EXE_WAY_TEXT_MAP,
  CHANGE_RESULT_KEY_MAP,
  CHANGE_RESULT_KEY_TEXT_MAP,
  CHANGE_RISK_LEVEL_TEXT_MAP,
} from '@manyun/ticket.model.change';
import { generateChangeOfflineLocation } from '@manyun/ticket.route.ticket-routes';

import { Ellipsis, TinyTable, UserLink } from '@manyun/dc-brain.legacy.components';
import { CHANGE_TICKET_STATUS_TEXT_MAP } from '@manyun/dc-brain.legacy.constants/change';
import { generateChangeTicketDetail } from '@manyun/dc-brain.legacy.utils/urls';

const columns = (operation, showColumns, changesTicketVersion, { fixedColumns }) => {
  let list = [
    {
      title: '变更ID',
      dataIndex: 'changeOrderId',
      // width: 60,
      fixed: true,
      dataType: {
        type: 'link',
        options: {
          to(text) {
            if (text.startsWith('N')) {
              return generateChangeOfflineLocation({
                id: text,
              });
            }
            return generateChangeTicketDetail({
              id: text,
            });
          },
        },
      },
    },
    {
      title: '位置',
      dataIndex: 'blockTag',
    },
    {
      title: '变更方式',
      dataIndex: 'exeWay',
      render: exeWay => CHANGE_EXE_WAY_TEXT_MAP[exeWay],
    },
    {
      title: '变更专业',
      dataIndex: 'reason',
      // width: 60,
      // render: reason  => <span>{CHANGE_EMERGENCY_LEVEL_TEXT_MAP[reason]}</span>,
    },

    {
      title: '变更类型',
      dataIndex: 'changeTypeName',
      // width: 120,
    },
    {
      title: '变更等级',
      dataIndex: 'riskLevel',
      // width: 80,
      render: riskLevel => CHANGE_RISK_LEVEL_TEXT_MAP[riskLevel],
    },
    {
      title: '变更标题',
      dataIndex: 'title',
      render(title) {
        return (
          <Ellipsis lines={1} tooltip>
            {title}
          </Ellipsis>
        );
      },
    },
    {
      title: '计划时间',
      dataIndex: 'planStartTime',
      width: 256,
      render: (_, { planStartTime, planEndTime, changeTimeList }) => {
        return changeTimeList?.length ? (
          <Popover
            content={
              <Space
                style={{ maxWidth: 400 }}
                split={<Divider type="vertical" spaceSize="mini" />}
                wrap
              >
                <Space direction="vertical">
                  {changeTimeList.map(item => {
                    return `${item.startDate}~${item.endDate}(${item.startHour}-${item.endHour})`;
                  })}
                </Space>
              </Space>
            }
          >
            <Typography.Paragraph
              style={{ width: 270, marginBottom: 0 }}
              ellipsis={{
                rows: 2,
              }}
            >
              {changeTimeList.map((item, index) => {
                return `${item.startDate}~${item.endDate}(${item.startHour}-${item.endHour})  ${
                  index + 1 !== changeTimeList.length ? '| ' : ''
                }`;
              })}
            </Typography.Paragraph>
          </Popover>
        ) : (
          <>
            {dayjs(planStartTime).format('YYYY-MM-DD HH:mm')} -<br />
            {dayjs(planEndTime).format('YYYY-MM-DD HH:mm')}
          </>
        );
      },
    },
    {
      title: '执行时间',
      dataIndex: 'realStartTime',
      render: (_, { realStartTime, realEndTime }) => {
        if (realStartTime && realEndTime) {
          return (
            <>
              {dayjs(realStartTime).format('YYYY-MM-DD HH:mm')} -<br />
              {dayjs(realEndTime).format('YYYY-MM-DD HH:mm')}
            </>
          );
        }
        if (realStartTime && !realEndTime) {
          return (
            <>
              {dayjs(realStartTime).format('YYYY-MM-DD HH:mm')} -<br />
              --
            </>
          );
        }
        return '--';
      },
    },
    {
      title: '变更状态',
      dataIndex: 'changeStatus',
      render: changeStatus => <span>{CHANGE_TICKET_STATUS_TEXT_MAP[changeStatus]}</span>,
    },
    {
      title: '变更结果',
      dataIndex: 'exeResult',
      render: exeResult => {
        if (!exeResult) {
          return '--';
        }
        return (
          <Tag color={exeResult === CHANGE_RESULT_KEY_MAP.Failed ? 'error' : 'success'}>
            {CHANGE_RESULT_KEY_TEXT_MAP[exeResult]}
          </Tag>
        );
      },
    },
    {
      title: '提单人',
      dataIndex: 'creatorName',
      // width: 80,
      render: (creatorName, text) => <UserLink userName={creatorName} userId={text.creatorId} />,
    },
  ];
  if (showColumns.length) {
    list = list
      .map(item => {
        const showColumnsList = showColumns.filter(
          showColumnsItem =>
            typeof showColumnsItem === 'object' && showColumnsItem.dataIndex === item.dataIndex
        );
        if (showColumnsList.length) {
          return { ...item, visible: showColumnsList[0] ? showColumnsList[0].visible : false };
        }
        if (showColumns.includes(item.dataIndex)) {
          return item;
        }
        return false;
      })
      .filter(i => i !== false);
  }
  if (Array.isArray(fixedColumns) && fixedColumns.length) {
    fixedColumns.forEach(({ dataIndex, fixed }) => {
      const columnIdx = list.findIndex(clmn => clmn.dataIndex === dataIndex);
      if (columnIdx > -1) {
        list[columnIdx].fixed = fixed;
      }
    });
  }
  if (operation) {
    list.push(operation);
  }
  if (changesTicketVersion === 'lite') {
    list = list.filter(item => item.dataIndex !== 'exeWay');
  }
  return list;
};

/**
 * 变更的通用的列表
 * @param {object} props
 * @param {string[]} [props.showColumns=[]] 需要展示的列 给定dataIndex
 * @param {JSX.Element | JSX.Element[]} [props.actions=null] 表格上方的操作按钮
 * @param {JSX.Element | JSX.Element[]} [props.operation=null] 操作列
 * @param {Array<{ dataIndex: string; fixed: 'left'|'right'|boolean }>} [props.fixedColumns] 锁定列配置
 * @param {import('antd-3/lib/table/interface').PaginationConfig} props.pagination
 */
export function ChangeTable({
  dataSource,
  loading,
  className,
  // metaCategoryEntities,
  operation = null,
  showColumns = [],
  fixedColumns = [],
  actions = null,
  size = 'small',
  changesTicketVersion,
  ...props
}) {
  return (
    <TinyTable
      rowKey="id"
      size={size}
      scroll={{ x: 'max-content' }}
      className={className}
      columns={columns(operation, showColumns, changesTicketVersion, {
        fixedColumns,
      })}
      actions={actions}
      loading={loading}
      dataSource={dataSource}
      {...props}
    />
  );
}

/**
 * @type {ChangeTable}
 */
export default ChangeTable;
