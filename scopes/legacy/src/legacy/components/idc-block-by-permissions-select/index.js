import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';

import omit from 'lodash/omit';

import { Cascader } from '@manyun/base-ui.ui.cascader';
import { message } from '@manyun/base-ui.ui.message';

import { fetchBlockByPermission } from '@manyun/dc-brain.legacy.services/alarmScreenService';
import { fetchQueryIdcByPermission } from '@manyun/dc-brain.legacy.services/eventCenterService';

export function IdcBlockByPermissionsSelect({ forwardedRef, value, ...props }) {
  const [spaceTreeData, setSpaceTreeData] = useState([]);

  useEffect(() => {
    if (!spaceTreeData.length && value && value.length) {
      getIdc(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value]);
  const getIdc = async init => {
    if (!spaceTreeData.length) {
      const { response, error } = await fetchQueryIdcByPermission();
      if (response) {
        if (init && value && value[1]) {
          const blockResponse = await fetchBlockByPermission({
            idcTag: value[0],
          });
          if (blockResponse.response) {
            setSpaceTreeData(
              response.data.map(item => {
                return {
                  label: item,
                  value: item,
                  isLeaf: false,
                  children: blockResponse.response.data.map(item => {
                    return {
                      label: item,
                      value: item,
                    };
                  }),
                };
              })
            );
          }
          // loadData([{ isLeaf: false, label: value[0], loading: false, value: value[0] }]);
        } else {
          setSpaceTreeData(
            response.data.map(item => {
              return {
                label: item,
                value: item,
                isLeaf: false,
              };
            })
          );
        }
      }
      if (error) {
        message.error(error);
      }
    }
  };

  const loadData = async selectedOptions => {
    const targetOption = selectedOptions[selectedOptions.length - 1];
    targetOption.loading = true;
    const { response, error } = await fetchBlockByPermission({
      idcTag: selectedOptions[0].label,
    });
    if (response) {
      targetOption.loading = false;
      targetOption.children = response.data.map(item => {
        return {
          label: item,
          value: item,
        };
      });
      setSpaceTreeData([...spaceTreeData]);
    } else {
      message.error(error);
    }
  };

  return (
    <Cascader
      ref={forwardedRef}
      placeholder=""
      options={spaceTreeData}
      allowClear
      changeOnSelect
      onFocus={getIdc}
      loadData={loadData}
      value={value}
      {...omit(props, 'dispatch')}
    />
  );
}

export default connect(null, null, null, { forwardRef: true })(
  React.forwardRef((props, ref) => <IdcBlockByPermissionsSelect forwardedRef={ref} {...props} />)
);
