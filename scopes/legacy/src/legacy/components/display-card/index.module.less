@import '~@manyun/base-ui.theme.theme/dist/theme.variable.less';

.displayCard {
  max-width: 244px;
  border: 1px solid @border-color-split;
  border-radius: 5px;
  padding: 8px 8px;
  cursor: pointer;
  // display: flex;
  // flex-direction: column;
  // justify-content: space-between;
  // align-items: flex-end;

  // &.normal {
  // }

  // &.portion {
  // }

  &.finish {
    background-color: @disabled-bg;
  }

  // &.recently {
  // }

  .cardSpan {
    width: 4px;
    height: 16px;
    border-radius: 4px;
    background-color: ~'var(--@{prefixCls}-primary-color)';
    margin-top: 5px;
  }

  .subDesc {
    color: @text-color-secondary;
    font-size: 12px;
  }
}

.cardNumber {
  display: flex;
  align-items: center;
  color: 'var(--text-color-1)';
  font-size: 12px;

  .numberStatus {
    display: 'inline-block';
    width: 6px;
    height: 6px;
    border-radius: 50%;

    &.warning {
      background-color: @warning-color;
    }

    &.success {
      background-color: @success-color;
    }

    &.error {
      background-color: @error-color;
    }
  }
}
