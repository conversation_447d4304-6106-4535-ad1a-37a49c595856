import React, { Component } from 'react';

import classNames from 'classnames';

import { Space } from '@manyun/base-ui.ui.space';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';

import styles from './index.module.less';

export function Number({ color, describe, number }) {
  return (
    <div className={styles.cardNumber}>
      <div className={classNames(styles.numberStatus, color && styles[color])} />
      <div
        style={{
          display: 'inline-block',
          margin: '0 6px',
        }}
      >
        {describe}
      </div>
      <div>{number || 0}</div>
    </div>
  );
}

// export const DISPLAY_CARD_COLOR_MAPS = {
//   NORMAL: 'var(--color-blue-12)',
//   PORTION: 'var(--color-blue-hovering)',
//   FINISH: 'var(--border-color-base)',
//   RECENTLY: 'var(--background-color-disabled)',
// };

export const DISPLAY_CARD_COLOR_MAPS = {
  NORMAL: 'normal',
  PORTION: 'portion',
  FINISH: 'finish',
  RECENTLY: 'recently',
};
/**
 * @typedef {object} Props
 * @property {string | ReactNode} props.title 标题
 * @property {string | ReactNode} props.describe 描述
 * @property {boolean} props.showDescribe 描述
 * @property {string | ReactNode} props.subDescribe  二级描述
 * @property {boolean} props.highlighted  高亮
 * @property {boolean} props.extra  卡片右上角
 * @property {Array<{ describe: string; number: string ;color:string}>} props.countList  右侧数据展示
 */
class DisplayCard extends Component {
  render() {
    const {
      title,
      describe,
      subDescribe,
      countList,
      onClick,
      showDescribe = true,
      backgroundColor = DISPLAY_CARD_COLOR_MAPS['Normal'],
      extra = null,
    } = this.props;

    return (
      <div
        key={title}
        className={classNames(styles.displayCard, backgroundColor && styles[backgroundColor])}
        onClick={onClick}
      >
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <Space direction="horizontal" align="flex-start">
            <div className={styles.cardSpan} />
            <div
              style={{
                fontSize: 16,
              }}
            >
              {title}
            </div>
          </Space>
          {extra && <span>{extra}</span>}
        </div>
        <div style={{ display: 'flex', paddingTop: '8px' }}>
          <div
            style={{
              flex: 1,
              display: 'flex',
              paddingRight: '12px',
            }}
          >
            {showDescribe && (
              <GutterWrapper size=".5em" mode="vertical" justifyContent="space-between">
                <div
                  style={{
                    fontSize: 16,
                  }}
                >
                  {describe}
                </div>
                <div className={styles.subDesc}>{subDescribe}</div>
              </GutterWrapper>
            )}
          </div>
          <GutterWrapper
            style={{ display: 'flex', flexDirection: 'column' }}
            size=".5em"
            mode="vertical"
          >
            {countList.map(item => (
              <Number
                key={item.describe}
                color={item.color}
                describe={item.describe}
                number={item.number}
              />
            ))}
          </GutterWrapper>
        </div>
      </div>
    );
  }
}

export default DisplayCard;
