import React, { useEffect, useState } from 'react';

import Form from '@ant-design/compatible/es/form';

import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { Modal } from '@manyun/base-ui.ui.modal';

export default function ModalConfirm({
  visible,
  max,
  title,
  reasonPlaceholder,
  children,
  canOpen,
  onOk,
  onCancel,
}) {
  const [internalVisible, setVisible] = useState(false);
  const [validationProps, setValidationProps] = useState({});
  const [reason, setReason] = useState('');
  const [okBtnLoading, setOkBtnLoading] = useState(false);
  useEffect(() => {
    if (typeof visible == 'boolean') {
      setVisible(visible);
    }
  }, [visible]);

  function toggleVisible() {
    setVisible(prev => !prev);
  }

  /**
   * 校验原因
   * 1. 原因必填
   * 2. 原因不超过 128 个字
   * @param {string|undefined} currentReason
   * @returns {any[]|undefined}
   */
  function validate(currentReason) {
    const hasText = !!currentReason;
    const textTooLong = hasText && currentReason.length > max;
    const passed = hasText && !textTooLong;

    if (passed) {
      validationProps.validateStatus !== undefined && setValidationProps({});
      return;
    }

    const error = {
      validateStatus: 'error',
      help: textTooLong ? `原因最多允许输入${max}个字！` : '原因必填！',
    };
    setValidationProps(error);

    return [error];
  }

  async function onTriggerClick() {
    const result = await canOpen();
    result && toggleVisible();
  }

  return (
    <>
      <Modal
        title={title}
        onCancel={toggleVisible}
        footer={
          <>
            <Button
              disabled={okBtnLoading}
              onClick={() => {
                toggleVisible();
                if (onCancel && typeof onCancel === 'function') {
                  onCancel();
                }
              }}
            >
              取消
            </Button>
            <Button
              type="danger"
              loading={okBtnLoading}
              onClick={async () => {
                if (validate(reason)) {
                  return;
                }
                setOkBtnLoading(true);
                const ok = await onOk({ reason });
                if (ok) {
                  setReason('');
                  if (onCancel && typeof onCancel === 'function') {
                    onCancel();
                  }
                }
                toggleVisible();

                setOkBtnLoading(false);
              }}
            >
              确定
            </Button>
          </>
        }
        visible={internalVisible}
      >
        <div>
          <Form.Item required colon={false} label="原因" {...validationProps}>
            <Input.TextArea
              autoSize={{ minRows: 3 }}
              placeholder={reasonPlaceholder}
              value={reason}
              onChange={({ target: { value } }) => {
                setReason(value);
                validate(value);
              }}
            />
          </Form.Item>
        </div>
      </Modal>
      {React.isValidElement(children) && React.cloneElement(children, { onClick: onTriggerClick })}
    </>
  );
}
