import React, { useEffect, useState } from 'react';

import QuestionCircleFilled from '@ant-design/icons/es/icons/QuestionCircleFilled';

import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';

export default function PopConfirm({ visible, title, children, canOpen, onOk, onCancel, ...rest }) {
  const [internalVisible, setVisible] = useState(false);

  useEffect(() => {
    if (typeof visible == 'boolean') {
      setVisible(visible);
    }
  }, [visible]);

  return (
    <Popconfirm
      placement="topRight"
      icon={<QuestionCircleFilled />}
      title={title}
      visible={internalVisible}
      {...rest}
      onConfirm={onOk}
      onVisibleChange={async newVisible => {
        // 隐藏气泡
        if (!newVisible) {
          setVisible(newVisible);
          if (onCancel && typeof onCancel === 'function') {
            onCancel();
          }
          return;
        }

        const result = await canOpen();
        if (!result) {
          return;
        }
        setVisible(newVisible);
      }}
    >
      {children}
    </Popconfirm>
  );
}
