import React from 'react';

import ModalConfirm from './modal-confirm';
import PopConfirm from './pop-confirm';

/**
 * 删除时二次确认框（可选类型：对话框、气泡）
 * @param {object} props
 * @param {boolean} [props.visible] Modal/Popconfirm 的 visible 受控
 * @param {'modal-confirm'|'pop-confirm'} [props.type='pop-confirm']
 * @param {number} [props.maxReasonLength=128]
 * @param {string} props.targetName
 * @param {string} [props.title] 自定义文案
 * @param {string} [props.reasonPlaceholder] 原因文本域的 placeholder
 * @param {import('antd-3/lib/popconfirm').PopconfirmProps} [props.popconfirmProps]
 * @param {() => Promise<boolean>} [props.canOpen] 返回值为 true 则打开确认框，否则不做任何操作
 * @param {(values?: { reason: string }) => Promise<boolean>} props.onOk
 * @param {function} [props.onCancel] 弹窗关闭时的回调
 */
export default function DeleteConfirm({
  visible,
  type = 'pop-confirm',
  maxReasonLength = 128,
  targetName,
  title = `你确定要删除 ${targetName} 吗？`,
  reasonPlaceholder = '例：删除测试数据',
  popconfirmProps = {},
  children,
  canOpen = () => Promise.resolve(true),
  onOk,
  onCancel,
}) {
  // https://reactjs.org/docs/react-api.html#reactchildrenonly
  React.isValidElement(children) && React.Children.only(children);

  if (type === 'modal-confirm') {
    return (
      <ModalConfirm
        visible={visible}
        max={maxReasonLength}
        title={title}
        reasonPlaceholder={reasonPlaceholder}
        canOpen={canOpen}
        onOk={onOk}
        onCancel={onCancel}
      >
        {children}
      </ModalConfirm>
    );
  }

  if (type === 'pop-confirm') {
    return (
      <PopConfirm
        visible={visible}
        title={title}
        {...popconfirmProps}
        canOpen={canOpen}
        onOk={onOk}
        onCancel={onCancel}
      >
        {children}
      </PopConfirm>
    );
  }

  console.warn(
    `Props "type(${type})" for <DeleteConfirm /> is not valid! You can use one of ['modal-confirm'|'pop-confirm'].`
  );

  return null;
}
