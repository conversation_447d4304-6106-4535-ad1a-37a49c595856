import React from 'react';

import { Button } from '@manyun/base-ui.ui.button';

/**
 * @typedef {Omit<import('antd-3/lib/button').ButtonProps, 'type'>} OmittedButtonProps
 * @typedef {import('antd-3/lib/button').ButtonType} ButtonType
 * @typedef {ButtonType | 'success' | 'warning'} MergedButtonType
 * @typedef {{type?: MergedButtonType; compact?:boolean}} CustomizedButtonProps
 */

/**
 * 封装于 `antd` 的 `Button` 组件，新增了 `type="success", type="warning"` 的类型
 * @type {import('react').FunctionComponent<OmittedButtonProps & CustomizedButtonProps>}
 */
export const StyledButton = ({ type, compact, ...rest }) => {
  React.useEffect(() => {
    console.error('warning: StyledButton 组件已废弃，请使用 base-ui.button 进行重构！！！');
  }, []);

  return <Button type={type} {...rest} />;
};

export default StyledButton;
