import React from 'react';

import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';

/**
 * DI 量错误值展示。
 * @param {object} props `DIErrorTooltip` 组件的 `props`
 * @param {string} props.title DI值
 */
function DIErrorTooltip({ title }) {
  return (
    <Tooltip title={title} getTooltipContainer={triggerNode => triggerNode}>
      <Typography.Text type="danger" style={{ fontSize: '12px' }}>
        错误值
      </Typography.Text>
    </Tooltip>
  );
}

export default DIErrorTooltip;
