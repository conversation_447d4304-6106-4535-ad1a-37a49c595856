import React, { useState } from 'react';

import DeviceApiTreeSelect from '../index';

// import ConnectButtonModal from '@manyun/dc-brain.legacy.pages/approval/components/connect-button-modal';

export default function Demo() {
  const [device, setDevice] = useState(null);
  return (
    <>
      <DeviceApiTreeSelect
        dataType={['space', 'noSnDevice']}
        category="allCategory"
        value={device}
        onChange={value => setDevice(value)}
      />
      {/* <ConnectButtonModal /> */}
    </>
  );
}
