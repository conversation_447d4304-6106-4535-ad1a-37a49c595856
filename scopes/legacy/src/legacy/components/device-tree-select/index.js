import omit from 'lodash/omit';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';

import { ApiTreeSelect } from '@manyun/dc-brain.legacy.components';
import { DEVICE_SPACE_TYPE_KEY_MAP } from '@manyun/dc-brain.legacy.constants/enums/deviceType';
import { treeDataService } from '@manyun/dc-brain.legacy.services';

/**
 * @typedef {object} Props
 * @property {Array} props.dataType 数据源  space 空间类型 | snDevice 有SN编码的资产分类 | noSnDevice 无SN编码的资产分类,不传默认为需要所有
 * @property {string} props.category  返回数据类型 categorycode 只需选择分类code | category 选择分类code和name | allCategoryCode  三级的code | allCategory 三级的类型code 和名称，不传默认为categorycode
 * @property {string | object | Array} value  没有设置 multiple 时，category=['categorycode'],value = '10101'  ;|
 *  category=['category'],value = {code:'10101',name:'高压隔离柜'，numbered:true} ; |
 *  category=['allCategoryCode'],value = {thirdCategorycode: '10101', secondCategoryCode: '101', firstCategoryCode:'1',numbered:true} ; |
 * category=['allCategory'],value = {numbered:true,thirdCategorycode: '10101',thirdCategoryName:'高压隔离柜', secondCategoryCode: '101', secondCategoryName:'高压配电',firstCategoryCode:'1',firstCategoryName:'强电'}
 * @property {function} onChange，不设置multiple，category=['categorycode']时，返回值为字符串，其余为对象,设置了 multiple 时，为数组
 */
function DeviceApiTreeSelect({
  category,
  dataType,
  needNumbered,
  value,
  onChange,
  syncCommonData,
  forwardedRef,
  ...props
}) {
  const [deviceCategory, setDeviceCategory] = useState(null);
  const [treeExpandedKeys, setTreeExpandedKeys] = useState([]);
  const [inSearch, setInSearch] = useState(
    /* See http://chandao.manyun-local.com/zentao/bug-view-6309.html */ true
  );

  const apiTreeSelectRef = useRef(null);
  const treeSelectRef = useRef(null);

  useImperativeHandle(forwardedRef, () => apiTreeSelectRef.current);

  const dataTypeStr = dataType && Array.isArray(dataType) ? dataType.join('@') : '';

  useEffect(() => {
    if (!apiTreeSelectRef.current) {
      return;
    }
    apiTreeSelectRef.current.refreshData(true);
  }, [dataTypeStr, category]);

  let apiTreeValue = value;
  if (props.multiple && Array.isArray(value)) {
    if (value && (!category || category === 'categorycode')) {
      apiTreeValue = value;
      if (needNumbered) {
        apiTreeValue = value.map(({ code }) => code);
      }
    }
    if (value && category === 'category') {
      apiTreeValue = value.map(({ code }) => code);
    }
    if (value && (category === 'allCategoryCode' || category === 'allCategory')) {
      apiTreeValue = value.map(
        ({ thirdCategorycode, secondCategoryCode, firstCategoryCode }) =>
          thirdCategorycode || secondCategoryCode || firstCategoryCode
      );
    }
  } else {
    if (value && (!category || category === 'categorycode')) {
      apiTreeValue = value;
      if (needNumbered && typeof value === 'object') {
        apiTreeValue = value.code;
      }
    }
    if (value && category === 'category') {
      apiTreeValue = value.code;
    }
    if (value && (category === 'allCategoryCode' || category === 'allCategory')) {
      apiTreeValue = value.thirdCategorycode || value.secondCategoryCode || value.firstCategoryCode;
    }
  }

  const extraProps = { treeDefaultExpandAll: true };

  return (
    <ApiTreeSelect
      ref={apiTreeSelectRef}
      treeSelectRef={treeSelectRef}
      allowClear={false}
      dataService={async () => {
        const data = await treeDataService.fetchDeviceCategory({ numbered: null });
        if (data) {
          setDeviceCategory(data);
          const treeList = getDeviceData(data.treeList, dataType);
          return Promise.resolve({
            ...data,
            treeList,
          });
        }

        return {
          normalizedList: {},
          parallelList: [],
          treeList: [],
        };
      }}
      {...omit(props, 'disabledDepths')}
      // treeExpandedKeys={treeExpandedKeys}
      value={apiTreeValue}
      fieldNames={{ value: 'metaCode', key: 'metaCode', title: 'metaName' }}
      onChange={(value, node, extra) => {
        setInSearch(false);
        if (props.disabledDepths && props.disabledDepths.length) {
          const metaType = extra.triggerNode?.props.dataRef.metaType;
          if (metaType === 'C0' || metaType === 'C1') {
            if (!extra.triggerNode.props.expanded) {
              setTreeExpandedKeys(expandedKeys => [
                ...expandedKeys,
                extra.triggerNode.props.dataRef.metaCode,
              ]);
            } else {
              setTreeExpandedKeys(expandedKeys =>
                expandedKeys.filter(key => key !== extra.triggerNode.props.dataRef.metaCode)
              );
            }
            treeSelectRef.current.rcTreeSelect?.setOpenState(true);
            return;
          }
          const params = getCallbackParams({ value, category, deviceCategory });
          onChange(params);
          return;
        }
        const params = getCallbackParams({ value, category, deviceCategory });
        onChange(params);
      }}
      {...extraProps}
      onTreeExpand={expandedKeys => {
        setTreeExpandedKeys(expandedKeys);
      }}
      onSearch={value => {
        setInSearch(true);
      }}
    />
  );
}

const DeviceApiTreeSelectRevicedRef = forwardRef((props, ref) => {
  return <DeviceApiTreeSelect {...props} forwardedRef={ref} />;
});
DeviceApiTreeSelectRevicedRef.displayName = 'DeviceApiTreeSelectRevicedRef';
export default DeviceApiTreeSelectRevicedRef;

function getDeviceData(data, dataType) {
  const newData = data.filter(item => {
    if (!dataType || !dataType.length) {
      return true;
    }
    if (dataType.includes('space') && item.metaStyle === DEVICE_SPACE_TYPE_KEY_MAP.SPACE) {
      return true;
    }
    if (
      dataType.includes('snDevice') &&
      item.numbered &&
      item.metaStyle === DEVICE_SPACE_TYPE_KEY_MAP.DEVICE
    ) {
      return true;
    }
    if (
      dataType.includes('noSnDevice') &&
      !item.numbered &&
      item.metaStyle === DEVICE_SPACE_TYPE_KEY_MAP.DEVICE
    ) {
      return true;
    }
    if (dataType.includes('it') && item.metaStyle === DEVICE_SPACE_TYPE_KEY_MAP.IT) {
      return true;
    }
    return false;
  });
  return newData;
}

function getCallbackParams({ value, category, deviceCategory }) {
  let params;
  if (Array.isArray(value)) {
    if (!category || category === 'categorycode') {
      params = value;
    }
    if (category === 'category') {
      params = value.map(code => {
        return {
          code,
          name: deviceCategory.normalizedList[code].metaName,
          numbered: deviceCategory.normalizedList[code].numbered,
        };
      });
    }

    if (category === 'allCategoryCode') {
      params = value.map(code => {
        if (deviceCategory.normalizedList[code].metaType === 'C1') {
          let firstCategoryCode = null;
          if (deviceCategory.normalizedList[code].parentCode.indexOf('C0') !== -1) {
            firstCategoryCode = deviceCategory.normalizedList[code].parentCode.replace('C0', '');
          }
          return {
            thirdCategorycode: null,
            secondCategoryCode: code,
            firstCategoryCode,
            numbered: deviceCategory.normalizedList[code].numbered,
          };
        } else if (deviceCategory.normalizedList[code].metaType === 'C2') {
          let secondCategoryCode = null;
          let firstCategoryCode = null;
          if (deviceCategory.normalizedList[code].parentCode.indexOf('C1') !== -1) {
            secondCategoryCode = deviceCategory.normalizedList[code].parentCode.replace('C1', '');
          }
          if (deviceCategory.normalizedList[secondCategoryCode].parentCode.indexOf('C0') !== -1) {
            firstCategoryCode = deviceCategory.normalizedList[
              secondCategoryCode
            ].parentCode.replace('C0', '');
          }
          return {
            thirdCategorycode: code,
            secondCategoryCode,
            firstCategoryCode,
            numbered: deviceCategory.normalizedList[code].numbered,
          };
        } else {
          return {
            thirdCategorycode: null,
            secondCategoryCode: null,
            firstCategoryCode: code,
            numbered: deviceCategory.normalizedList[code].numbered,
          };
        }
      });
    }
    if (category === 'allCategory') {
      params = value.map(code => {
        if (
          deviceCategory.normalizedList[code] &&
          deviceCategory.normalizedList[code].metaType === 'C1'
        ) {
          let firstCategoryCode = null;
          if (deviceCategory.normalizedList[code].parentCode.indexOf('C0') !== -1) {
            firstCategoryCode = deviceCategory.normalizedList[code].parentCode.replace('C0', '');
          }
          const secondCategoryName = deviceCategory.normalizedList[code].metaName;
          const firstCategoryName = deviceCategory.normalizedList[firstCategoryCode].metaName;
          return {
            secondCategoryCode: code,
            secondCategoryName,
            firstCategoryCode,
            firstCategoryName,
            thirdCategorycode: null,
            thirdCategoryName: null,
            numbered: deviceCategory.normalizedList[code].numbered,
          };
        } else if (
          deviceCategory.normalizedList[code] &&
          deviceCategory.normalizedList[code].metaType === 'C2'
        ) {
          let secondCategoryCode = null;
          let firstCategoryCode = null;
          if (deviceCategory.normalizedList[code].parentCode.indexOf('C1') !== -1) {
            secondCategoryCode = deviceCategory.normalizedList[code].parentCode.replace('C1', '');
          }
          if (deviceCategory.normalizedList[secondCategoryCode].parentCode.indexOf('C0') !== -1) {
            firstCategoryCode = deviceCategory.normalizedList[
              secondCategoryCode
            ].parentCode.replace('C0', '');
          }
          const secondCategoryName = deviceCategory.normalizedList[secondCategoryCode].metaName;
          const firstCategoryName = deviceCategory.normalizedList[firstCategoryCode].metaName;
          const thirdCategoryName = deviceCategory.normalizedList[code].metaName;
          return {
            thirdCategorycode: code,
            thirdCategoryName,
            secondCategoryCode,
            secondCategoryName,
            firstCategoryCode,
            firstCategoryName,
            numbered: deviceCategory.normalizedList[code].numbered,
          };
        } else {
          return {
            thirdCategorycode: code,
            thirdCategoryName: deviceCategory.normalizedList[code].metaName,
            secondCategoryCode: null,
            secondCategoryName: null,
            firstCategoryCode: null,
            firstCategoryName: null,
            numbered: deviceCategory.normalizedList[code].numbered,
          };
        }
      });
    }
  } else {
    if (!category || category === 'categorycode') {
      params = value;
    }
    if (category === 'category') {
      params = {
        code: value,
        name: deviceCategory.normalizedList[value].metaName,
      };
    }
    if (category === 'allCategoryCode') {
      if (deviceCategory.normalizedList[value]?.metaType === 'C1') {
        let firstCategoryCode = null;
        if (deviceCategory.normalizedList[value].parentCode.indexOf('C0') !== -1) {
          firstCategoryCode = deviceCategory.normalizedList[value].parentCode.replace('C0', '');
        }
        params = {
          thirdCategorycode: null,
          secondCategoryCode: value,
          firstCategoryCode,
        };
      } else if (deviceCategory.normalizedList[value]?.metaType === 'C2') {
        let secondCategoryCode = null;
        let firstCategoryCode = null;

        if (deviceCategory.normalizedList[value].parentCode.indexOf('C1') !== -1) {
          secondCategoryCode = deviceCategory.normalizedList[value].parentCode.replace('C1', '');
        }
        if (deviceCategory.normalizedList[secondCategoryCode].parentCode.indexOf('C0') !== -1) {
          firstCategoryCode = deviceCategory.normalizedList[secondCategoryCode].parentCode.replace(
            'C0',
            ''
          );
        }
        params = {
          thirdCategorycode: value,
          secondCategoryCode,
          firstCategoryCode,
        };
      } else {
        params = {
          thirdCategorycode: null,
          secondCategoryCode: null,
          firstCategoryCode: value,
        };
      }
    }
    if (category === 'allCategory') {
      if (
        deviceCategory.normalizedList[value] &&
        deviceCategory.normalizedList[value].metaType === 'C1'
      ) {
        const secondCategoryName = deviceCategory.normalizedList[value].metaName;
        const firstCategoryCode = deviceCategory.normalizedList[value].parentCode.slice(2);
        const firstCategoryName = deviceCategory.normalizedList[firstCategoryCode].metaName;
        params = {
          secondCategoryCode: value,
          secondCategoryName,
          firstCategoryCode,
          firstCategoryName,
          thirdCategorycode: null,
          thirdCategoryName: null,
        };
      } else if (
        deviceCategory.normalizedList[value] &&
        deviceCategory.normalizedList[value].metaType === 'C2'
      ) {
        let secondCategoryCode = null;
        let firstCategoryCode = null;
        if (deviceCategory.normalizedList[value].parentCode.indexOf('C1') !== -1) {
          secondCategoryCode = deviceCategory.normalizedList[value].parentCode.replace('C1', '');
        }
        if (deviceCategory.normalizedList[secondCategoryCode].parentCode.indexOf('C0') !== -1) {
          firstCategoryCode = deviceCategory.normalizedList[secondCategoryCode].parentCode.replace(
            'C0',
            ''
          );
        }
        const secondCategoryName = deviceCategory.normalizedList[secondCategoryCode].metaName;
        const firstCategoryName = deviceCategory.normalizedList[firstCategoryCode].metaName;
        const thirdCategoryName = deviceCategory.normalizedList[value].metaName;
        params = {
          thirdCategorycode: value,
          thirdCategoryName,
          secondCategoryCode,
          secondCategoryName,
          firstCategoryCode,
          firstCategoryName,
        };
      } else {
        params = {
          thirdCategorycode: null,
          thirdCategoryName: null,
          secondCategoryCode: null,
          secondCategoryName: null,
          firstCategoryCode: value,
          firstCategoryName: deviceCategory.normalizedList[value]?.metaName,
        };
      }
    }
    if (category && category !== 'categorycode') {
      params = {
        ...params,
        numbered: deviceCategory.normalizedList[value]?.numbered,
      };
    }
  }
  return params;
}
