import React, { forwardRef, useImperativeHandle } from 'react';

import Form from '@ant-design/compatible/es/form';
import MinusCircleOutlined from '@ant-design/icons/es/icons/MinusCircleOutlined';
import PlusCircleOutlined from '@ant-design/icons/es/icons/PlusCircleOutlined';
import shortid from 'shortid';

import { Button } from '@manyun/base-ui.ui.button';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';

const StandardForm = forwardRef(({ inspectItemInfo, form, setInspectionItem, data }, ref) => {
  useImperativeHandle(ref, () => ({
    form,
  }));

  const { getFieldDecorator } = form;

  const addStandardByIdx = () => {
    const newData = data.map(item => {
      if (item.id === inspectItemInfo.id) {
        return {
          ...item,
          standards: [
            ...item.standards,
            { standardId: shortid.generate(), standardTxt: { value: null } },
          ],
        };
      }
      return item;
    });
    setInspectionItem(newData);
  };

  const deleteStandardByIdx = idx => {
    const newData = data.map(item => {
      if (item.id === inspectItemInfo.id) {
        const newStandards = item.standards.filter(mock => mock.standardId !== idx);
        return {
          ...item,
          standards: newStandards,
        };
      }
      return item;
    });
    setInspectionItem(newData);
  };
  return (
    <Form colon={false} labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
      <Row>
        <Col xl={24}>
          <Form.Item label="班次" style={{ marginBottom: '0' }}>
            {getFieldDecorator(inspectItemInfo.id, {
              rules: [
                { required: true, whitespace: true, message: '检查项名称为必填项！' },
                {
                  max: 50,
                  message: '最多输入 50 个字符！',
                },
              ],
            })(<Input style={{ width: 446 }} />)}
          </Form.Item>
        </Col>
        {inspectItemInfo.standards.map(({ standardId }, index) => {
          return (
            <Col xl={24} key={standardId}>
              <Form.Item label="班次" style={{ marginBottom: '0' }}>
                {getFieldDecorator(standardId, {
                  rules: [
                    { required: true, whitespace: true, message: '检查标准为必填项！' },
                    {
                      max: 50,
                      message: '最多输入 50 个字符！',
                    },
                  ],
                })(<Input style={{ width: 446 }} />)}
                {inspectItemInfo.standards.length === index + 1 && (
                  <Button type="link" onClick={() => addStandardByIdx()}>
                    <PlusCircleOutlined />
                  </Button>
                )}
                {inspectItemInfo.standards.length !== 1 && (
                  <Button type="link" onClick={() => deleteStandardByIdx(standardId)}>
                    <MinusCircleOutlined />
                  </Button>
                )}
              </Form.Item>
            </Col>
          );
        })}
      </Row>
    </Form>
  );
});

const formCreateOpts = {
  onFieldsChange(props, fieldValues) {
    const newData = props.data.map(item => {
      if (item.id === props.inspectItemInfo.id) {
        if (fieldValues[props.inspectItemInfo.id]) {
          const newStands = item.standards.map(({ standardId, standardTxt, ...resp }) => {
            if (fieldValues[standardId]) {
              return {
                ...resp,
                standardId,
                standardTxt: {
                  ...fieldValues[standardId],
                  value: fieldValues[standardId].value,
                },
              };
            } else {
              if (!standardTxt) {
                return {
                  value: standardTxt,
                  name: standardId,
                  errors: [{ message: '检查标准为必填项！', field: standardId }],
                };
              }
              return { ...resp, standardId, standardTxt };
            }
          });
          return {
            ...item,
            checkItemName: {
              ...fieldValues[props.inspectItemInfo.id],
              name: 'checkItemName',
              value: fieldValues[props.inspectItemInfo.id].value,
            },
            standards: newStands,
          };
        }
        const newStands = item.standards.map(({ standardId, standardTxt, ...resp }) => {
          if (fieldValues[standardId]) {
            return {
              ...resp,
              standardId,
              standardTxt: {
                ...fieldValues[standardId],
                value: fieldValues[standardId].value,
              },
            };
          } else {
            if (!standardTxt) {
              return {
                value: standardTxt,
                name: standardId,
                errors: [{ message: '检查标准为必填项！', field: standardId }],
              };
            }
            return { ...resp, standardId, standardTxt };
          }
        });
        return { ...item, standards: newStands };
      }
      return item;
    });
    props.setInspectionItem(newData);
  },
  mapPropsToFields(props) {
    const formFields = props.data.filter(item => item.id === props.inspectItemInfo.id);
    if (!formFields[0] || !formFields[0].standards || !formFields[0].standards.length) {
      return;
    }
    let fields = {
      [formFields[0].id]: Form.createFormField(formFields[0].checkItemName),
    };
    formFields[0].standards.forEach(({ standardId, standardTxt }) => {
      fields = {
        ...fields,
        [standardId]: Form.createFormField(standardTxt),
      };
    });
    return fields;
  },
};

const EhancedStandardForm = Form.create(formCreateOpts)(StandardForm);

export default EhancedStandardForm;
