import React from 'react';

import PlusOutlined from '@ant-design/icons/es/icons/PlusOutlined';
import shortid from 'shortid';
import styled from 'styled-components';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { List } from '@manyun/base-ui.ui.list';

import ItemCard from './components/itemCard';

/**
 * 封装于 `antd` 的 `List` 组件。
 * @typedef {object} Props
 * @property {Array} props.data List 数据源
 * @property {React.ReactNode} props.header 显示的heaer,如果不显示则为 null
 * @property {function} props.setInspectionItem  增加巡检项时 更新List数据中巡检项的方法
 * @property {object} props.standardsRefMap  所有巡检项表单的ref
 * @property {function} props.setStandardsRefMap  增加巡检项时 更新巡检项表单的ref
 */
export function AlterInfoDutyList({
  data,
  header,
  setInspectionItem,
  standardsRefMap,
  inspectType,
}) {
  const newInspection = async () => {
    const modules = {
      id: shortid.generate(),
      checkItemName: { value: null },
      standards: [{ standardId: shortid.generate(), standardTxt: { value: null } }],
      inspectType: inspectType,
    };
    setInspectionItem([...data, modules]);
  };

  const loadMore = () => {
    return (
      <Button type="link" onClick={() => newInspection()}>
        <PlusOutlined />
        添加班次
      </Button>
    );
  };

  return (
    <StyledList
      itemLayout="horizontal"
      header={header}
      dataSource={data}
      loadMore={loadMore()}
      renderItem={(inspectItemInfo, index) => {
        return (
          <ItemCard
            inspectItemInfo={inspectItemInfo}
            index={index + 1}
            data={data}
            setInspectionItem={setInspectionItem}
            xRef={standardsRefMap.get(inspectItemInfo.id)}
          />
        );
      }}
    />
  );
}

export default AlterInfoDutyList;

export const StyledList = styled(List)`
  .${prefixCls}-list-item .${prefixCls}-card {
    width: 85%;
  }
  .${prefixCls}-list-item .${prefixCls}-list-item-action {
    width: 15%;
    margin-left: 0px;
  }
  &.${prefixCls}-list-split .${prefixCls}-list-header {
    border-bottom: 0;
  }
  &.${prefixCls}-list-split .${prefixCls}-list-item {
    border-bottom: 0;
    background: var(--${prefixCls}-primary-color);
    margin-bottom: 20px;
  }
  .${prefixCls}-list-item .${prefixCls}-card .${prefixCls}-card-head {
    background: var(--${prefixCls}-primary-color);
  }
  &.${prefixCls}-list-something-after-last-item
    .${prefixCls}-spin-container
    > .${prefixCls}-list-items
    > .${prefixCls}-list-item:last-child {
    border-bottom: 0;
  }
  .${prefixCls}-form-item-children .${prefixCls}-btn {
    padding-left: 10px;
    padding-right: 0;
  }
  .${prefixCls}-input-affix-wrapper .${prefixCls}-input {
    background: var(--${prefixCls}-primary-color);
  }
  .has-error
    .${prefixCls}-input-affix-wrapper
    .${prefixCls}-input,
    .has-error
    .${prefixCls}-input-affix-wrapper
    .${prefixCls}-input:hover {
    background: var(--${prefixCls}-primary-color);
  }
`;

export const StyledTitleIcon = styled.span`
  width: 4px;
  height: 1em;
  border-radius: 4px;
  background-color: var(--color-reference);
  vertical-align: middle;
`;
