import { createSlice } from '@reduxjs/toolkit';
import shortid from 'shortid';

const moduleId = shortid.generate();

const itemModule = {
  name: { value: '' },
  alarmLevel: { value: '' },
  alarmType: { value: '' },
  available: { value: 'true' },
  createIncident: { value: 'true' },
  incidentType: { value: '' },
  triggerCount: { value: 1 },
  lowerLimit: { value: { limit: null, limitCode: 'USER_DEFINED', operator: 'gt' } },
  upperLimit: { value: { limit: null, limitCode: 'USER_DEFINED', operator: 'lt' } },
  notifyRule: { value: [] },
  normalLimits: { value: [] },
  fieldsCode: '',
};

const initialState = {
  AlarmConfigurationTree: [],
  treeDataList: [],
  alarmCopyList: {},
  dragDataList: [],
  dragData: {},
  alarmTemplateList: [],
  alarmTypeData: [],
  basicConfiguration: {},
  showAlarmTemplateList: [],
  associatedTemplateGroupLoading: false,
  associatedTemplateGroupVisible: false,
  templateList: {
    allList: [],
    showList: [],
  },
  associatedGroupList: {
    allList: [],
    showList: [],
  },
  searchValues: {},
  templateListPage: {
    list: [],
    total: 0,
    pageNum: 1,
    pageSize: 10,
    loading: true,
  },
  templateGroup: {
    id: '',
    activeKey: 'all',
  },
  templateTypeListEntities: {},
  alarmConfiguration: {},
  loading: false,
  effectiveDomainList: {
    allList: [],
    showList: [],
  },
  eidtOrNewLoading: false,
  newTemplateMess: {
    module: {
      name: {
        value: '',
      }, //模板名称
      deviceType: {
        value: {
          code: '',
          name: '',
        },
      }, //目标设备类型
      available: {
        value: 'true',
      }, // 是否启用
      description: {
        value: '',
      }, // 描述,备注
      schemeIds: {
        value: [],
      }, // //关联的模板组id,可选择多个模板组
    },

    monitorItemIds: [moduleId], // 用于增加或删除的监控项
    monitorItems: [itemModule], // 更新监控项数据
    ruleTimeForm: {
      recoverInterval: {
        value: '',
      }, // 恢复观察周期
    },
    monitorItemMap: { [moduleId]: itemModule },
    notifyConfigMode: 'GLOBAL', // GLOBAL -> 全局；INDEPENDENT -> 逐条,

    globalNotificationTpl: { notifyRule: { value: [] } }, // 全局 文案

    copyNotificationTplData: null, // 拷贝的文案
    pointMess: [], // 选择的测点
  },
  pointIndevice: [],

  /**
   * 克隆模板
   */
  cloneTemplate: [],

  /**
   * 模板组
   */
  templateGroupData: [],

  /**
   * 监控项
   */

  associatedItems: [],
  reviewNotify: null,

  /**
   * 编辑的结果
   */

  resultData: null,

  /**
   * 编辑模板信息的结果
   */
  editTemplateModuleResult: false,

  /**
   * 请求的测点项配置  用来判断删除是否发请求
   */
  pintConfigInLine: null,

  /**
   * 选择的监控项 信息
   */
  selectedPointInfo: null,

  /**
   * 所有的设备档案
   */
  allDeviceParam: null,

  // 步骤
  current: 0,

  /**
   * 事件类型
   */
  eventTypeData: null,

  validateErrorId: [], // 存放表单校验失败的ID
  deviceParamData: {},
};

const alarmConfigurationTemplateSlice = createSlice({
  name: 'alarmConfigurationTemplate',
  initialState: initialState,
  reducers: {
    request: state => ({
      ...state,
      templateListPage: {
        ...state.templateListPage,
        loading: true,
      },
      loading: true,
    }),
    failure: state => ({
      ...state,
      associatedTemplateGroupLoading: false,
      templateListPage: {
        ...state.templateListPage,
        loading: false,
      },
      loading: false,
    }),
    fetchAlarmConfigurationTreeSuccess: (state, { payload: { tree, treeDataList } }) => ({
      ...state,
      AlarmConfigurationTree: tree,
      treeDataList,
    }),
    saveDragData: (state, { payload }) => ({
      ...state,
      dragData: payload,
    }),
    alarmTemplateList: (state, { payload: { list } }) => ({
      ...state,
      alarmTemplateList: list,
    }),
    saveBasicConfiguration: (state, { payload }) => ({
      ...state,
      basicConfiguration: payload,
    }),
    showAlarmTemplateList: (state, { payload }) => ({
      ...state,
      showAlarmTemplateList: payload,
    }),
    showAssociatedTemplateGroup: state => ({
      ...state,
      associatedTemplateGroupVisible: !state.associatedTemplateGroupVisible,
    }),
    associatedTemplateGroupLoading: state => ({
      ...state,
      associatedTemplateGroupLoading: !state.associatedTemplateGroupLoading,
    }),
    templateListSuccess: (state, { payload }) => ({
      ...state,
      templateList: payload,
    }),
    associatedGroupSuccess: (state, { payload }) => ({
      ...state,
      associatedGroupList: payload,
    }),
    templateListPageSuccess: (state, { payload }) => ({
      ...state,
      templateListPage: payload,
    }),
    templateGroupInfo: (state, { payload }) => ({
      ...state,
      templateGroup: payload,
    }),
    templateTypeListEntities: (state, { payload }) => ({
      ...state,
      templateTypeListEntities: payload,
    }),
    alarmConfiguration: (state, { payload }) => ({
      ...state,
      alarmConfiguration: payload,
    }),
    effectiveDomainListSuccess: (state, { payload }) => ({
      ...state,
      effectiveDomainList: payload,
    }),
    onEidtOrNewLoading: state => ({
      ...state,
      eidtOrNewLoading: !state.eidtOrNewLoading,
    }),
    saveNewTemplateMess: (state, { payload }) => ({
      ...state,
      newTemplateMess: { ...state.newTemplateMess, ...payload },
    }),
    savePointIndevice: (state, { payload }) => ({
      ...state,
      pointIndevice: payload,
    }),
    handleModuleFormChange: (state, { payload }) => {
      state.newTemplateMess.module = {
        ...state.newTemplateMess.module,
        ...payload,
      };
    },
    handleRefresTimeFormChange: (state, { payload }) => {
      state.newTemplateMess.ruleTimeForm = {
        ...state.newTemplateMess.ruleTimeForm,
        ...payload,
      };
    },
    setGlobalNotificationTpl: (state, { payload }) => {
      state.newTemplateMess.globalNotificationTpl.notifyRule.value = payload;
    },
    saveNotifyConfigMode: (state, { payload }) => {
      state.newTemplateMess.notifyConfigMode = payload;
    },
    copyNotificationTpl: (state, { payload }) => {
      state.newTemplateMess.copyNotificationTplData = payload;
    },
    handleGlobalFormChange: (state, { payload }) => {
      state.newTemplateMess.globalNotificationTpl = {
        ...state.newTemplateMess.globalNotificationTpl,
        ...payload,
      };
    },
    setSingleNotificationTpl: (state, { payload: { pointItem, newItemMap } }) => {
      state.newTemplateMess.monitorItems = pointItem;
      state.newTemplateMess.monitorItemMap = newItemMap;
    },
    saveSelectPointMess: (state, { payload }) => {
      state.newTemplateMess.pointMess = payload;
    },
    saveCloneTemplate: (state, { payload }) => {
      state.cloneTemplate = payload;
    },
    changeModule: (state, { payload }) => {
      state.newTemplateMess.module.name = {
        ...state.newTemplateMess.module.name,
        value: payload ? payload.name : null,
      };
      state.newTemplateMess.module.available = {
        ...state.newTemplateMess.module.available,
        value: payload ? String(payload.available) : 'true',
      };
      state.newTemplateMess.module.description = {
        ...state.newTemplateMess.module.description,
        value: payload ? payload.description : '',
      };
    },
    saveTemplateGroup: (state, { payload }) => ({
      ...state,
      templateGroupData: payload,
    }),
    saveAssociatedItems: (
      state,
      { payload: { data, newIds, newMap, newItems, recoverInterval, globalConfig } }
    ) => {
      state.associatedItems = data;
      state.newTemplateMess.monitorItemIds = newIds;
      state.newTemplateMess.monitorItemMap = newMap;
      state.newTemplateMess.monitorItems = newItems;
      state.newTemplateMess.ruleTimeForm.recoverInterval = {
        ...state.newTemplateMess.ruleTimeForm.recoverInterval,
        value: recoverInterval,
      };
      state.newTemplateMess.notifyConfigMode = 'INDEPENDENT';
      state.newTemplateMess.globalNotificationTpl = globalConfig;
    },
    reviewAlarmNoticifyData: (state, { payload }) => ({
      ...state,
      reviewNotify: payload,
    }),
    savefetchModuleConfiguration: (state, { payload }) => {
      state.newTemplateMess.module = {
        ...state.newTemplateMess.module,
        ...payload,
      };
    },
    saveTemplateGroupIntemplate: (state, { payload }) => {
      state.newTemplateMess.module.schemeIds = {
        ...state.newTemplateMess.module.schemeIds,
        value: payload,
      };
    },
    generateModuleName: (state, { payload }) => {
      state.newTemplateMess.module.name = {
        ...state.newTemplateMess.module.schemeIds,
        value: payload,
      };
    },
    saveResultData: (state, { payload }) => {
      state.resultData = payload;
    },
    saveEditTemplateModuleResult: (state, { payload }) => {
      state.editTemplateModuleResult = payload;
    },
    savePintConfigInLine: (state, { payload }) => {
      state.pintConfigInLine = payload;
    },
    saveSelectedPointInfo: (state, { payload }) => {
      state.selectedPointInfo = {
        ...state.selectedPointInfo,
        ...payload,
      };
    },
    saveAllDeviceParam: (state, { payload }) => {
      state.allDeviceParam = payload;
    },

    resetState4Creation(state) {
      state.newTemplateMess.module = initialState.newTemplateMess.module;
      state.newTemplateMess.ruleTimeForm = initialState.newTemplateMess.ruleTimeForm;
      state.newTemplateMess.monitorItemIds = initialState.newTemplateMess.monitorItemIds;
      state.newTemplateMess.monitorItems = initialState.newTemplateMess.monitorItems;
      state.newTemplateMess.monitorItemMap = initialState.newTemplateMess.monitorItemMap;
      state.newTemplateMess.globalNotificationTpl =
        initialState.newTemplateMess.globalNotificationTpl;
      state.newTemplateMess.copyNotificationTplData =
        initialState.newTemplateMess.copyNotificationTplData;
      state.resultData = initialState.resultData;
      state.editTemplateModuleResult = initialState.editTemplateModuleResult;
      state.current = initialState.current;
    },
    changeCurrent: (state, { payload: { current } }) => {
      state.current = current;
    },
    resetConfig(state) {
      state.newTemplateMess.ruleTimeForm = initialState.newTemplateMess.ruleTimeForm;
      state.newTemplateMess.monitorItemIds = initialState.newTemplateMess.monitorItemIds;
      state.newTemplateMess.monitorItems = initialState.newTemplateMess.monitorItems;
      state.newTemplateMess.monitorItemMap = initialState.newTemplateMess.monitorItemMap;
      state.newTemplateMess.globalNotificationTpl =
        initialState.newTemplateMess.globalNotificationTpl;
      state.newTemplateMess.copyNotificationTplData =
        initialState.newTemplateMess.copyNotificationTplData;
      state.resultData = initialState.resultData;
      state.editTemplateModuleResult = initialState.editTemplateModuleResult;
      state.current = initialState.current;
    },
    saveEventTypeData: (state, { payload }) => {
      state.eventTypeData = payload;
    },
    updateSearchValues(state, { payload }) {
      state.searchValues = {
        ...state.searchValues,
        ...payload,
      };
    },
    resetSearchValues(state) {
      state.searchValues = {};
    },
    saveValidateErrorId: (state, { payload }) => {
      state.validateErrorId = [...state.validateErrorId, payload];
    },
    saveDeviceParamData(state, { payload }) {
      state.deviceParamData[payload.key] = payload.value;
    },
    updateSingleNotificationTpl: (state, { payload }) => {
      state.newTemplateMess.monitorItemMap = payload;
    },
  },
});

export const ALARM_CONFIGURATION_TEMPLATE_SLICE_NAME = alarmConfigurationTemplateSlice.name;
export const alarmConfigurationTemplateActions = alarmConfigurationTemplateSlice.actions;
export default alarmConfigurationTemplateSlice.reducer;
