import { createSlice } from '@reduxjs/toolkit';

import { ACCEPT_TYPE_KEY_MAP } from '@manyun/dc-brain.legacy.pages/ticket/configs/accept/constants';

const INITIAL_STATE = {
  loading: false,
  activeTabKey: ACCEPT_TYPE_KEY_MAP.PURCHASE,
  totalNum: {
    [ACCEPT_TYPE_KEY_MAP.PURCHASE]: 0,
    [ACCEPT_TYPE_KEY_MAP.MOVE]: 0,
  },
  arrivalAssetList: {
    [ACCEPT_TYPE_KEY_MAP.PURCHASE]: {
      data: [],
      total: 0,
    },
    [ACCEPT_TYPE_KEY_MAP.MOVE]: {
      data: [],
      total: 0,
    },
  },
  pagination: {
    pageNum: 1,
    pageSize: 10,
  },
  searchValues: {
    [ACCEPT_TYPE_KEY_MAP.MOVE]: {},
    [ACCEPT_TYPE_KEY_MAP.PURCHASE]: {},
  },
};

const arrivalAssetSlice = createSlice({
  name: 'arrivalAsset',
  initialState: INITIAL_STATE,
  reducers: {
    request: state => ({
      ...state,
      loading: true,
    }),
    failure: state => ({
      ...state,
      loading: false,
    }),
    featchArrivalAssetListSuccess(state, { payload: { activeTabKey, data, total } }) {
      state.arrivalAssetList[activeTabKey] = {
        ...state.arrivalAssetList[activeTabKey],
        data,
        total,
      };
      state.loading = false;
    },
    setPaginationChangeHandler(state, { payload: { pageNum, pageSize } }) {
      state.pagination = { pageNum, pageSize };
    },
    resetPagination(state) {
      state.pagination = INITIAL_STATE.pagination;
    },
    updateArrivalAssetListSearchValues(state, { payload: { activeTabKey, values } }) {
      state.searchValues[activeTabKey] = {
        ...state.searchValues[activeTabKey],
        ...values,
      };
    },
    updateActiveTabKey(state, { payload }) {
      state.activeTabKey = payload;
    },
    updateTotalNum(state, { payload: { activeTabKey, value } }) {
      state.totalNum[activeTabKey] = value;
    },
    resetSearchValues(state, { payload }) {
      state.searchValues = {};
    },
  },
});

export const ARRIVAL_ASSET_SLICE_NAME = arrivalAssetSlice.name;
export const arrivalAssetActions = arrivalAssetSlice.actions;
export default arrivalAssetSlice.reducer;
