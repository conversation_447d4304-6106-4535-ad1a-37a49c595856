import { createSlice } from '@reduxjs/toolkit';

const INITIAL_STATE = {
  // 请求加载中
  loading: true,
  // 无sn耗材列表
  sparePage: {
    list: [],
    total: 0,
  },
  pagination: {
    pageNum: 1,
    pageSize: 10,
  },
  searchValues: {},
  importSpareList: {},
  importLoading: false,
  editBasicInfoLoading: false,
};
const spareSlice = createSlice({
  name: 'spareManage',
  initialState: INITIAL_STATE,
  reducers: {
    request: state => ({ ...state, loading: true }),
    failure: state => ({
      ...state,
      loading: false,
      importLoading: false,
      editBasicInfoLoading: false,
    }),

    featchSparePageSuccess: (state, { payload: { total, data } }) => ({
      ...state,
      loading: false,
      sparePage: {
        list: data,
        total: total,
      },
      editBasicInfoLoading: false,
    }),

    updateSearchValues(state, { payload }) {
      state.searchValues = {
        ...state.searchValues,
        ...payload,
      };
    },

    resetSearchValues(state, { payload }) {
      state.searchValues = {};
    },

    //导入
    importSpareSuccess: (state, { payload }) => {
      return {
        ...state,
        importSpareList: payload,
      };
    },

    importLoading: state => ({
      ...state,
      importLoading: true,
    }),

    // 编辑表单提交的加载中
    editBasicInfoLoading: state => ({
      ...state,
      editBasicInfoLoading: !state.editBasicInfoLoading,
    }),

    initializeSearchValuesAndConditions: (state, { payload: { searchValues } }) => {
      state.searchValues = searchValues;
      state.pagination = INITIAL_STATE.pagination;
    },
    setSparePagination(state, { payload }) {
      state.pagination = payload;
    },
    resetSparePagination(state) {
      state.pagination = INITIAL_STATE.pagination;
    },
  },
});

export const SPARE_MANAGE_SLICE_NAME = spareSlice.name;
export const spareActions = spareSlice.actions;
export default spareSlice.reducer;
