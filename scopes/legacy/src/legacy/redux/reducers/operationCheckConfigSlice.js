import { createSlice } from '@reduxjs/toolkit';

// import shortid from 'shortid';

// const mergeRowsKey = shortid();

const initialState = {
  loading: false,
  pagination: {
    pageNum: 1,
    pageSize: 10,
  },
  data: [],
  total: 0,
  searchValues: {
    //配置名称
    configName: {
      value: null,
    },

    // 工单大类
    taskType: {
      value: null,
    },

    // 工单子类
    taskSubType: {
      value: null,
    },

    // 创建时间
    createTime: { value: null },

    // 创建人名
    createByName: {
      value: undefined,
    },
  },
  create: {
    //配置名称
    configName: {
      value: null,
    },

    // 工单类
    taskType: {
      value: [],
    },

    // 操作前检查项
    itemsInBefore: [
      // {
      //   key: mergeRowsKey,
      //   itemName: undefined,
      //   itemMethod: undefined,
      //   itemNormal: undefined,
      //   inputData: undefined,
      //   dataRange: undefined,
      //   mergeRowsKey: mergeRowsKey,
      //   itemType: 'CHECK_BEFORE',
      // },
    ],

    // 操作后检查项
    itemsInAfter: [
      // {
      //   key: mergeRowsKey,
      //   itemName: undefined,
      //   itemMethod: undefined,
      //   itemNormal: undefined,
      //   inputData: undefined,
      //   dataRange: undefined,
      //   mergeRowsKey: mergeRowsKey,
      //   itemType: 'CHECK_AFTER',
      // },
    ],
  },
  edit: {},
  detail: {},
};

const operationCheckConfigSilice = createSlice({
  name: 'operationCheckConfig',
  initialState: initialState,
  reducers: {
    failure(state) {
      state.loading = false;
    },
    updateTableLoading(state, { payload }) {
      state.loading = payload;
    },
    resetPageNum(state) {
      state.pagination.pageNum = 1;
    },
    setDataAndTotal(state, { payload: { data, total } }) {
      state.data = data;
      state.total = total;
      state.loading = false;
    },
    updateSearchValues(state, { payload }) {
      state.searchValues = { ...state.searchValues, ...payload };
    },
    resetSearchValuesAndPagination(state) {
      state.searchValues = initialState.searchValues;
      state.pagination = initialState.pagination;
    },
    setPagination(state, { payload }) {
      state.pagination = payload;
    },
    setConfigInfos(state, { payload }) {
      state.detail = payload;
    },
    upDateCreateOptionValues(state, { payload }) {
      state.create = { ...state.create, ...payload };
    },
    upDateEditOptionValues(state, { payload }) {
      state.edit = { ...state.edit, ...payload };
    },
    restDetail(state) {
      state.detail = initialState.detail;
    },
  },
});

export const OPERATION_CHECK_CONFIG_SLICE_NAME = operationCheckConfigSilice.name;
export const operationCheckConfigActions = operationCheckConfigSilice.actions;
export default operationCheckConfigSilice.reducer;
