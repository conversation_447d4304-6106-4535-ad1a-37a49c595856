import { createSlice } from '@reduxjs/toolkit';
import cloneDeep from 'lodash/cloneDeep';
import moment from 'moment';
import shortid from 'shortid';

import { SHITF_SYS_TYPE_KEY_MAP } from '@manyun/dc-brain.legacy.pages/staff-shift-arrangement/shift-sys-management/constants';

// 班次
const shift = {
  loading: false,
  pagination: {
    pageNum: 1,
    pageSize: 10,
  },
  data: [],
  total: 0,
  searchValues: {},
  createOptions: {
    dutyName: {
      value: null,
    },
    onDutyTime: {
      value: null,
    },
    offDutyTime: {
      value: null,
    },
    offIsNextDay: {
      value: false,
    },
    isOnDutyCheck: {
      value: false,
    },
    onDutyCheckStartTime: {
      value: null,
    },
    onDutyCheckEndTime: {
      value: null,
    },
    isOffDutyCheck: {
      value: false,
    },
    offDutyCheckStartTime: {
      value: null,
    },
    offDutyCheckEndTime: {
      value: null,
    },
    allowRest: {
      value: false,
    },
    restMinutes: {
      value: null,
    },

    enableOffset: {
      value: false,
    },
    onDutyOffsetMinutes: {
      value: [60],
    },
    offDutyOffsetMinutes: {
      value: [60],
    },
    enableBuffer: {
      value: false,
    },
    onDutyBufferMinutes: {
      value: [60],
    },
    offDutyBufferMinutes: {
      value: [60],
    },
    enableCompensate: {
      value: false,
    },
    compensateHours: {
      value: [],
    },
    allowContinuousWork: {
      value: false,
    },
    notAllowContinuousTime: {
      value: 8,
    },
    onDutyCheckRange: {
      startIsNextDay: false,
      endIsNextDay: false,
    },
    offDutyCheckRange: {
      startIsNextDay: false,
      endIsNextDay: false,
    },
    restRange: {
      startIsNextDay: false,
      endIsNextDay: false,
    },
  },
  createShiftVisiable: false,
};

// 班制
const shiftSys = {
  loading: false,
  pagination: {
    pageNum: 1,
    pageSize: 10,
  },
  data: [],
  total: 0,
  searchValues: {},
  createOptions: {
    shiftsName: {
      value: null,
    },
    shiftsType: {
      value: SHITF_SYS_TYPE_KEY_MAP.WEEK,
    },
    enableStatutoryHoliday: {
      value: false,
    },
  },
  createVisiable: false,
  // 所有班次
  allShiftData: [],
  showAllShiftData: [],
  selectedShiftVisible: false,
  week: {
    checkedShiftConfirm: null,
    workTableData: [],
  },
  period: {
    periodShifts: {
      value: [],
    },
    periodDays: {
      value: 2,
    },
    workTableData: [
      {
        code: shortid(),
        shiftId: 0,
      },
      {
        code: shortid(),
        shiftId: 0,
      },
    ],
  },
  editOptions: {
    shiftsName: {
      value: null,
    },
    shiftsType: {
      value: SHITF_SYS_TYPE_KEY_MAP.WEEK,
    },
    week: {
      checkedShiftConfirm: null,
      workTableData: [],
      enableStatutoryHoliday: {
        value: false,
      },
    },
    period: {
      periodShifts: {
        value: [],
      },
      workTableData: [
        {
          code: shortid(),
          shiftId: 0,
        },
        {
          code: shortid(),
          shiftId: 0,
        },
      ],
      periodDays: {
        value: 2,
      },
    },
    attGroupListByShifts: [],
  },
};

const example = [
  {
    workTime: '星期一',
    code: 1,
    checked: true,
  },
  {
    workTime: '星期二',
    code: 2,
    checked: true,
  },
  {
    workTime: '星期三',
    code: 3,
    checked: true,
  },
  {
    workTime: '星期四',
    code: 4,
    checked: true,
  },
  {
    workTime: '星期五',
    code: 5,
    checked: true,
  },
  {
    workTime: '星期六',
    code: 6,
    checked: false,
  },
  {
    workTime: '星期天',
    code: 7,
    checked: false,
  },
];

// 班组
const dutyGroup = {
  loading: false,
  pagination: {
    pageNum: 1,
    pageSize: 10,
  },
  data: [],
  total: 0,
  searchValues: {},
  createOrEditVisible: false,
  create: {
    submitLoading: false,
    //班组名
    groupName: {
      value: null,
    },

    // 机房/楼栋
    idcBlockTag: {
      value: [],
    },

    // 员工信息
    staffIds: {
      value: [],
    },
  },
  edit: {
    submitLoading: false,
    id: null, // 班组id

    //班组名
    groupName: {
      value: null,
    },

    // 机房/楼栋
    idcBlockTag: {
      value: [],
    },

    // 员工信息
    staffIds: {
      value: [],
    },
  },
};

// 排班规则
const groupSchedule = {
  loading: false,
  pagination: {
    pageNum: 1,
    pageSize: 10,
  },
  data: [],
  total: 0,
  searchValues: {},
  createOrEditVisible: false,
  createOrEditLoading: false,
  create: {
    // 规则名
    ruleName: {
      value: null,
    },

    //上下班时间间隔分钟数
    commutingTimeInterval: {
      value: [],
    },

    // 换班范围
    exchangeRange: {
      value: 'IDC',
    },

    // 是否只允许同班制内换班
    allowSameShiftsExchange: {
      value: false,
    },

    // 是否只允许同角色内换班
    allowSameRoleExchange: {
      value: false,
    },

    // 请假范围
    replaceRange: {
      value: 'IDC',
    },

    //是否只允许休息时顶班
    allowRestReplace: {
      value: false,
    },

    //优先顶班班制
    priorityShifts: {
      value: null,
    },

    //优先顶班角色
    priorityRoles: {
      value: [],
    },

    // 是否允许补卡
    enableSupply: {
      value: false,
    },

    // 是否限制补卡次数
    enableSupplyCount: {
      value: false,
    },

    // 每月支持的最大补卡数
    supplyCountByMonth: {
      value: 3,
    },

    // 是否限制补卡时间
    enableSupplyTime: {
      value: false,
    },

    // 补卡最多的相隔时间（天）
    supplyValidDays: {
      value: 14,
    },

    /**加班可提前申请天数 */
    overtimeBeforeDays: {
      value: 3,
    },

    /**请假可提前天数 */
    leaveBeforeDays: {
      value: 3,
    },
    /**外勤可提前天数 */
    outWorkBeforeDays: {
      value: 3,
    },

    /**单次外勤最多可请时长 */
    outWorkTotalHours: {
      value: 240,
    },

    /**组合假类型 */
    combineLeaves: {
      value: ['BREAK_OFF_COMBINED_LEAVE', 'SICK_COMBINED_LEAVE'],
    },
    /** 请假不可后补请假日期*/
    leaveNotValidStartDay: {
      value: 10,
    },
    /**不可后补 （无加班申请）加班确认开始时间 */
    overtimeNotValidStartDay: {
      value: 10,
    },
    /**（有加班申请）提前加班确认天数 */
    overtimeConfirmValidDays: {
      value: 7,
    },
    /**不可后补（有加班申请）加班确认开始时间 */
    overtimeConfirmNotValidStartDay: {
      value: 10,
    },
    /**不可后补 外勤不允许的开始日期 */
    outWorkNotValidStartDay: {
      value: 10,
    },
    /**不可后补  补卡不允许的开始日期 */
    supplyNotValidStartDay: {
      value: 10,
    },
  },

  edit: {
    //考勤组规则id
    id: null,
    // 规则名
    ruleName: {
      value: null,
    },

    //上下班时间间隔分钟数
    commutingTimeInterval: {
      value: [],
    },

    // 换班范围
    exchangeRange: {
      value: 'IDC',
    },

    // 是否只允许同班制内换班
    allowSameShiftsExchange: {
      value: false,
    },

    // 是否只允许同角色内换班
    allowSameRoleExchange: {
      value: false,
    },

    // 请假范围
    replaceRange: {
      value: 'IDC',
    },

    //是否只允许休息时顶班
    allowRestReplace: {
      value: false,
    },

    //优先顶班班制
    priorityShifts: {
      value: null,
    },

    //优先顶班角色
    priorityRoles: {
      value: [],
    },

    // 是否允许补卡
    enableSupply: {
      value: false,
    },

    // 是否限制补卡次数
    enableSupplyCount: {
      value: false,
    },

    // 每月支持的最大补卡数
    supplyCountByMonth: {
      value: 5,
    },

    // 是否限制补卡时间
    enableSupplyTime: {
      value: false,
    },

    // 补卡最多的相隔时间（天）
    supplyValidDays: {
      value: 31,
    },

    /**加班可提前申请天数 */
    overtimeBeforeDays: {
      value: undefined,
    },

    /**请假可提前天数 */
    leaveBeforeDays: {
      value: undefined,
    },
    /**外勤可提前天数 */
    outWorkBeforeDays: {
      value: undefined,
    },

    /**单次外勤最多可请时长 */
    outWorkTotalHours: {
      value: undefined,
    },

    /**组合假类型 */
    combineLeaves: {
      value: [],
    },
    /** 请假不可后补请假日期*/
    leaveNotValidStartDay: {
      value: undefined,
    },
    /**不可后补 （无加班申请）加班确认开始时间 */
    overtimeNotValidStartDay: {
      value: undefined,
    },
    /**（有加班申请）提前加班确认天数 */
    overtimeConfirmValidDays: {
      value: undefined,
    },
    /**不可后补（有加班申请）加班确认开始时间 */
    overtimeConfirmNotValidStartDay: {
      value: undefined,
    },
    /**不可后补 外勤不允许的开始日期 */
    outWorkNotValidStartDay: {
      value: undefined,
    },
    /**不可后补  补卡不允许的开始日期 */
    supplyNotValidStartDay: {
      value: undefined,
    },
  },
};

// 考勤组
const attendanceGroup = {
  loading: false,
  pagination: {
    pageNum: 1,
    pageSize: 10,
  },
  data: [],
  total: 0,
  searchValues: {},
  createOrEditVisible: false,
  createOrEditLoading: false,
  create: {
    attName: {
      value: null,
    },
    // 机房/楼栋
    idcBlockTag: {
      value: [],
    },

    // 班制
    shifts: {
      value: null,
    },

    // 用户组
    userGroupIds: {
      value: [],
    },

    // 考勤规则
    attRule: {
      value: null,
    },

    // 班组
    dutyGroupIdList: {
      value: [],
      table: [],
    },
  },
  edit: {
    attName: {
      value: null,
    },
    // 机房/楼栋
    idcBlockTag: {
      value: [],
    },

    // 班制
    shifts: {
      value: null,
    },

    // 用户组
    userGroupIds: {
      value: [],
    },

    // 考勤规则
    attRule: {
      value: null,
    },

    // 班组
    dutyGroupIdList: {
      value: [],
      table: [],
    },

    // 初始班组，由于提交时判断班组是否有变更
    defaultDutyGroupIdList: {
      value: [],
      table: [],
    },
  },
  schedule: {
    timeSelectVisible: false,
    timeModalButtonLoading: false,
    scheduleStartTime: null,
    scheduleCycleMonths: null,
    scheduleUnicodeChange: [],
    scheduleStaffList: [],
    scheduleStaffJson: {},
    statutoryHolidays: [],
  },
  scheduleCalendar: null,
};

const now = new Date();

// 月度汇总
const monthly = {
  loading: false,
  pagination: {
    pageNum: 1,
    pageSize: 10,
  },
  data: [],
  total: 0,
  searchValues: {
    // 员工
    staffId: {
      name: 'staffId',
      value: undefined,
    },
    // 考勤组id
    attGroupId: {
      name: 'attGroupId',
      value: undefined,
    },

    // 机房、楼栋
    IdcBlock: {
      name: 'IdcBlock',
      value: [],
    },

    // 开始时间-结束时间
    dateRange: {
      name: 'dateRange',
      value: getDateRange(),
    },
  },
};

// 每日统计
const daily = {
  loading: false,
  pagination: {
    pageNum: 1,
    pageSize: 10,
  },
  data: [],
  total: 0,
  searchValues: {
    // 员工
    staffId: {
      name: 'staffId',
      value: undefined,
    },
    dutyGroupName: {
      name: 'dutyGroupName',
      value: '',
    },
    // 机房、楼栋
    IdcBlock: {
      name: 'IdcBlock',
      value: [],
    },

    // 开始时间-结束时间
    dateRange: {
      name: 'dateRange',
      value: getDateRange(true),
    },
  },
};

const check = {
  loading: false,
  pagination: {
    pageNum: 1,
    pageSize: 10,
  },
  data: [],
  total: 0,
  searchValues: {
    // 员工
    staffId: {
      name: 'staffId',
      value: undefined,
    },
    // 考勤组id
    attGroupId: {
      name: 'attGroupId',
      value: undefined,
    },

    // 机房、楼栋
    IdcBlock: {
      name: 'IdcBlock',
      value: [],
    },

    // 开始时间-结束时间
    dateRange: {
      name: 'dateRange',
      value: getDateRange(),
    },
  },
};

const record = {
  loading: false,
  pagination: {
    pageNum: 1,
    pageSize: 10,
  },
  data: [],
  total: 0,
  searchValues: {
    // 员工
    staffId: {
      name: 'staffId',
      value: undefined,
    },
    // 考勤组id
    attGroupId: {
      name: 'attGroupId',
      value: undefined,
    },

    checkWayList: {
      name: 'checkWayList',
      value: undefined,
    },

    isDeleted: {
      name: 'isDeleted',
      value: '0' /**默认显示有效 */,
    },

    // 机房、楼栋
    IdcBlock: {
      name: 'IdcBlock',
      value: [],
    },

    // 开始时间-结束时间
    dateRange: {
      name: 'dateRange',
      value: [moment(now, 'YYYY/MM/DD').startOf('month'), moment(now, 'YYYY/MM/DD')],
    },
  },
};

const initialState = {
  shift,
  shiftSys,
  dutyGroup,
  groupSchedule,
  attendanceGroup,
  monthly,
  daily,
  check,
  record,
};

const staffShiftSlice = createSlice({
  name: 'staffShift',
  initialState,
  reducers: {
    failure(state) {
      state.shift.loading = false;
      state.shiftSys.loading = false;
      state.dutyGroup.loading = false;
      state.groupSchedule.loading = false;
      state.groupSchedule.createOrEditLoading = false;
      state.attendanceGroup.loading = false;
      state.attendanceGroup.createOrEditLoading = false;
      state.attendanceGroup.schedule.timeModalButtonLoading = false;
      state.monthly.loading = false;
      state.daily.loading = false;
      state.check.loading = false;
      state.record.loading = false;
    },
    startShiftTableLoading(state) {
      state.shift.loading = true;
    },
    resetShiftPageNum(state) {
      state.shift.pagination.pageNum = 1;
    },
    setDataAndTotal(state, { payload: { data, total } }) {
      state.shift.data = data;
      state.shift.total = total;
      state.shift.loading = false;
    },
    updateSearchValues(state, { payload }) {
      state.shift.searchValues = payload;
    },
    updatePagination(state, { payload }) {
      state.shift.pagination = {
        ...state.shift.pagination,
        ...payload,
      };
    },
    updateCreateOptions(state, { payload }) {
      if (payload.enableCompensate && payload.enableCompensate.value) {
        const novel = {
          id: shortid.generate(),
          lateGo: 3,
          lateArrive: 1,
        };
        state.shift.createOptions = {
          ...state.shift.createOptions,
          ...payload,
          compensateHours: {
            value: [novel],
          },
        };
        return;
      }
      // 判断下班是否在次日
      if (
        (payload.onDutyTime &&
          state.shift.createOptions.onDutyTime.value !== payload.onDutyTime.value) ||
        (payload.offDutyTime &&
          state.shift.createOptions.offDutyTime.value !== payload.offDutyTime.value)
      ) {
        if (payload.onDutyTime) {
          state.shift.createOptions = {
            ...state.shift.createOptions,
            ...payload,
            offIsNextDay: {
              value: null,
            },
            offDutyCheckRange: {
              ...state.shift.createOptions.offDutyCheckRange,
              endIsNextDay: null,
            },
            offDutyTime: {
              value: null,
            },
          };
        }
        if (payload.offDutyTime) {
          const tmp = getIsTommorrow(state.shift.createOptions.onDutyTime, payload.offDutyTime);
          state.shift.createOptions = {
            ...state.shift.createOptions,
            ...payload,
            offIsNextDay: {
              value: tmp,
            },
            offDutyCheckRange: {
              ...state.shift.createOptions.offDutyCheckRange,
              endIsNextDay: tmp,
            },
          };
        }

        state.shift.createOptions = {
          ...state.shift.createOptions,
          ...payload,
          onDutyCheckStartTime: {
            value: null,
          },
          onDutyCheckEndTime: {
            value: null,
          },
          offDutyCheckStartTime: {
            value: null,
          },
          offDutyCheckEndTime: {
            value: null,
          },
        };
        return;
      }

      // 判断上班打卡结束时间是否是次日(开始时间不可小于当天0点),上班卡开始时间是不会为次日的
      if (payload.onDutyCheckEndTime) {
        const onHour = moment(state.shift.createOptions.onDutyTime.value).format('HH');
        const endHour = moment(payload.onDutyCheckEndTime.value).format('HH');
        const onMinutes = moment(state.shift.createOptions.onDutyTime.value).format('mm');
        const endMinutes = moment(payload.onDutyCheckEndTime.value).format('mm');
        if (
          Number(onHour) > Number(endHour) ||
          (Number(onHour) === Number(endHour) && Number(onMinutes) > Number(endMinutes))
        ) {
          state.shift.createOptions.onDutyCheckRange.endIsNextDay = true;
        } else {
          state.shift.createOptions.onDutyCheckRange.endIsNextDay = false;
        }
        state.shift.createOptions = {
          ...state.shift.createOptions,
          ...payload,
        };
        return;
      }

      // 判断下班打卡是否为次日
      if (payload.offDutyTime || payload.offDutyCheckStartTime || payload.offDutyCheckEndTime) {
        const offHour = moment(state.shift.createOptions.offDutyTime.value).format('HH');
        const startHour = moment(state.shift.createOptions.offDutyCheckStartTime.value).format(
          'HH'
        );
        const offMinutes = moment(state.shift.createOptions.offDutyTime.value).format('mm');
        const startMinutes = moment(state.shift.createOptions.offDutyCheckStartTime.value).format(
          'mm'
        );
        if (
          ((Number(startHour) > 0 && Number(startHour) <= Number(offHour)) ||
            (Number(startHour) === Number(offHour) &&
              Number(offMinutes) >= Number(startMinutes))) &&
          state.shift.createOptions.offIsNextDay.value
        ) {
          state.shift.createOptions.offDutyCheckRange.startIsNextDay = true;
          state.shift.createOptions.offDutyCheckRange.endIsNextDay = true;
        } else {
          if (state.shift.createOptions.offIsNextDay.value && Number(startHour) < 16) {
            state.shift.createOptions.offDutyCheckRange.startIsNextDay = true;
          } else {
            state.shift.createOptions.offDutyCheckRange.startIsNextDay = false;
          }
        }

        // 如果下班打卡开始时间 小于 下班打卡结束时间 ，则下班卡为次日
        const offDutyCheckEndTimeHour = moment(
          state.shift.createOptions.offDutyCheckEndTime.value
        ).format('HH');
        if (state.shift.createOptions.offIsNextDay.value || offDutyCheckEndTimeHour < startHour) {
          state.shift.createOptions.offDutyCheckRange.endIsNextDay = true;
        } else {
          state.shift.createOptions.offDutyCheckRange.endIsNextDay = false;
        }
        state.shift.createOptions = {
          ...state.shift.createOptions,
          ...payload,
        };
        return;
      }

      //判断休息结束时间是否为次日
      if (payload.restMinutes) {
        if (state.shift.createOptions.restMinutes.value) {
          state.shift.createOptions.restRange.endIsNextDay =
            state.shift.createOptions.restMinutes.value >= 1440 ? true : false;
        }
        state.shift.createOptions = {
          ...state.shift.createOptions,
          ...payload,
        };
        return;
      }

      state.shift.createOptions = {
        ...state.shift.createOptions,
        ...payload,
      };
    },
    changeCompensateHours(state, { payload }) {
      state.shift.createOptions = {
        ...state.shift.createOptions,
        compensateHours: {
          value: payload,
        },
      };
    },
    updateCreateShiftVisiable(state, { payload }) {
      state.shift.createShiftVisiable = payload;
    },
    resetCreateShiftOptions(state) {
      state.shift.createOptions = initialState.shift.createOptions;
    },
    editCreateOption(state, { payload }) {
      state.shift.createOptions = payload;
    },

    //  班制
    startShiftSysTableLoading(state) {
      state.shiftSys.loading = true;
    },
    resetShiftSysPageNum(state) {
      state.shiftSys.pagination.pageNum = 1;
      state.dutyGroup.pagination.pageNum = 1;
      state.groupSchedule.pagination.pageNum = 1;
    },
    setShiftSysDataAndTotal(state, { payload: { data, total } }) {
      state.shiftSys.data = data;
      state.shiftSys.total = total;
      state.shiftSys.loading = false;
    },
    updateShiftSysPagination(state, { payload }) {
      state.shiftSys.pagination = {
        ...state.shiftSys.pagination,
        ...payload,
      };
    },
    updateShiftSysSearchValues(state, { payload }) {
      state.shiftSys.searchValues = payload;
    },
    updateShiftSysCreateVisiable(state, { payload }) {
      state.shiftSys.createVisiable = payload;
    },
    updateCreateShiftSysOptions(state, { payload }) {
      state.shiftSys.createOptions = {
        ...state.shiftSys.createOptions,
        ...payload,
      };
      if (payload.periodDays) {
        const diff = payload.periodDays.value - state.shiftSys.period.periodDays.value;
        if (diff > 0) {
          for (let i = 1; i <= diff; i++) {
            const p = {
              code: shortid(),
              shiftId: 0,
            };
            state.shiftSys.period.workTableData.push(p);
          }
        } else {
          const d = state.shiftSys.period.periodDays.value - payload.periodDays.value;
          for (let i = 1; i <= d; i++) {
            state.shiftSys.period.workTableData.pop();
          }
        }

        state.shiftSys.period.periodDays = payload.periodDays;
      }
      if (payload.periodShifts) {
        state.shiftSys.period.periodShifts = {
          ...payload.periodShifts,
          validating: payload.periodShifts.value.length ? true : false,
        };
      }
    },
    resetCreateShiftSysOptions(state) {
      state.shiftSys.createOptions = initialState.shiftSys.createOptions;
      state.shiftSys.editOptions = initialState.shiftSys.editOptions;
      state.shiftSys.period = initialState.shiftSys.period;
    },
    resetCreateDutyGroupOptions(state) {
      state.dutyGroup.create = initialState.dutyGroup.create;
    },
    saveAllShiftData(state, { payload }) {
      state.shiftSys.allShiftData = payload;
      state.shiftSys.showAllShiftData = payload;
      if (payload.length === 0) {
        return;
      }
      state.shiftSys.week.checkedShiftConfirm = payload[0];

      state.shiftSys.week.workTableData = cloneDeep(example).map(item => {
        return {
          ...item,
          shift: `班次${payload[0].dutyName}: ${payload[0].onDutyTime}-${payload[0].offDutyTime}`,
        };
      });
    },
    updateWeekWorkTable(state, { payload }) {
      state.shiftSys.week.workTableData = payload;
    },
    updateShowAllShiftData(state, { payload }) {
      state.shiftSys.showAllShiftData = payload;
    },
    updateSelectedShiftVisible(state, { payload }) {
      state.shiftSys.selectedShiftVisible = payload;
    },
    updateWeekcheckedShift(state, { payload }) {
      state.shiftSys.week.checkedShiftConfirm = payload;
      state.shiftSys.week.workTableData = cloneDeep(example).map(item => {
        return {
          ...item,
          shift: `班次${payload.dutyName}: ${payload.onDutyTime}-${payload.offDutyTime}`,
        };
      });
    },
    updateperiodcheckedShift(state, { payload }) {
      state.shiftSys.period.periodShifts = {
        value: payload,
        dirty: false,
        name: 'periodShifts',
        touched: payload.length > 0 ? true : false,
        validating: payload.length > 0 ? true : false,
        errors: payload.length > 0 ? null : [{ message: '工作班次必选', field: 'periodShifts' }],
      };
    },
    updatePeriodWorkTable(state, { payload }) {
      state.shiftSys.period.workTableData = payload;
    },
    updateEditShiftSysysInfos(state, { payload }) {
      state.shiftSys.editOptions = payload;
    },
    updateEditShiftSysOptions(state, { payload }) {
      state.shiftSys.editOptions = {
        ...state.shiftSys.editOptions,
        ...payload,
      };
      if (payload.enableStatutoryHoliday) {
        state.shiftSys.editOptions.week.enableStatutoryHoliday = payload.enableStatutoryHoliday;
      }
      if (payload.periodDays) {
        const diff = payload.periodDays.value - state.shiftSys.editOptions.period.periodDays.value;
        if (diff > 0) {
          for (let i = 1; i <= diff; i++) {
            const p = {
              code: shortid(),
              shiftId: 0,
            };
            state.shiftSys.editOptions.period.workTableData.push(p);
          }
        } else {
          const d = state.shiftSys.editOptions.period.periodDays.value - payload.periodDays.value;
          for (let i = 1; i <= d; i++) {
            state.shiftSys.editOptions.period.workTableData.pop();
          }
        }
        state.shiftSys.editOptions.period.periodDays = payload.periodDays;
      }
    },
    updateEditShiftSysWeekWorkTable(state, { payload }) {
      state.shiftSys.editOptions.week.workTableData = payload;
    },
    updateEditPeriodWorkTable(state, { payload }) {
      state.shiftSys.editOptions.period.workTableData = payload;
    },
    updateEditWeekcheckedShift(state, { payload }) {
      state.shiftSys.editOptions.week.checkedShiftConfirm = payload;
      state.shiftSys.editOptions.week.workTableData = cloneDeep(example).map(item => {
        return {
          ...item,
          shift: `班次${payload.dutyName}: ${payload.onDutyTime}-${payload.offDutyTime}`,
        };
      });
    },
    updateEditperiodcheckedShift(state, { payload }) {
      state.shiftSys.editOptions.period.periodShifts = {
        value: payload,
        dirty: false,
        name: 'periodShifts',
        touched: payload.length > 0 ? true : false,
        validating: payload.length > 0 ? true : false,
        errors: payload.length > 0 ? null : [{ message: '工作班次必选', field: 'periodShifts' }],
      };
      if (state.shiftSys.editOptions.period.periodShifts.value.length) {
        const chckedShiftsIds = state.shiftSys.editOptions.period.periodShifts.value.map(
          ({ id }) => id
        );
        const newTable = cloneDeep(state.shiftSys.editOptions.period.workTableData).map(item => {
          if (chckedShiftsIds.includes(item.shiftId)) {
            return item;
          } else {
            return { ...item, shiftId: 0 };
          }
        });
        state.shiftSys.editOptions.period.workTableData = newTable;
      } else {
        const newTable = cloneDeep(state.shiftSys.editOptions.period.workTableData).map(item => {
          return { ...item, shiftId: 0 };
        });
        state.shiftSys.editOptions.period.workTableData = newTable;
      }
    },
    startDutyGroupTableLoading(state, { payload }) {
      state.dutyGroup.loading = payload;
    },
    resetDutyGroupPageNum(state) {
      state.dutyGroup.pagination.pageNum = 1;
    },
    setDutyGroupDataAndTotal(state, { payload: { data, total } }) {
      state.dutyGroup.data = data;
      state.dutyGroup.total = total;
      state.dutyGroup.loading = false;
    },
    updateDutyGroupSearchValues(state, { payload }) {
      state.dutyGroup.searchValues = payload;
    },
    updateDutyGroupPagination(state, { payload }) {
      state.dutyGroup.pagination = {
        ...state.dutyGroup.pagination,
        ...payload,
      };
    },
    updateDutyGroupCreateVisiable(state, { payload }) {
      state.dutyGroup.createOrEditVisible = payload;
    },
    updateDutyGroupEditCreateOption(state, { payload }) {
      state.dutyGroup.edit = payload;
    },
    resetEditDutyGroupOptions(state) {
      state.dutyGroup.edit = initialState.dutyGroup.edit;
    },
    updateDutyGroupCreateOptions(state, { payload }) {
      state.dutyGroup.create = {
        ...state.dutyGroup.create,
        ...payload,
      };
    },
    updateCreatedStaffIds(state, { payload }) {
      state.dutyGroup.create.staffIds = {
        value: payload,
        dirty: false,
        name: 'staffIds',
        touched: true,
        validating: payload.length > 0 ? true : false,
        errors: payload.length > 0 ? null : [{ message: '班组成员必选', field: 'staffIds' }],
      };
    },
    updateDutyGroupEditOptions(state, { payload }) {
      state.dutyGroup.edit = {
        ...state.dutyGroup.edit,
        ...payload,
      };
    },
    updateEditDutyGroupStaffIds(state, { payload }) {
      state.dutyGroup.edit.staffIds = {
        value: payload,
        dirty: false,
        name: 'staffIds',
        touched: true,
        validating: payload.length > 0 ? true : false,
        errors: payload.length > 0 ? null : [{ message: '班组成员必选', field: 'staffIds' }],
      };
    },
    startGroupScheduleTableLoading(state, { payload }) {
      state.groupSchedule.loading = payload;
    },
    setGroupScheduleDataAndTotal(state, { payload: { data, total } }) {
      state.groupSchedule.data = data;
      state.groupSchedule.total = total;
      state.groupSchedule.loading = false;
    },
    updateGroupScheduleSearchValues(state, { payload }) {
      state.groupSchedule.searchValues = payload;
    },
    updateGroupSchedulePagination(state, { payload }) {
      state.groupSchedule.pagination = {
        ...state.groupSchedule.pagination,
        ...payload,
      };
    },
    updateGroupScheduleVisiable(state, { payload }) {
      state.groupSchedule.createOrEditVisible = payload;
    },
    updateGroupScheduleCreateOptions(state, { payload }) {
      state.groupSchedule.create = {
        ...state.groupSchedule.create,
        ...payload,
      };
    },
    setSelectedRoleIds(state, { payload }) {
      state.groupSchedule.create.priorityRoles.value = payload;
    },
    updateCreateOrEditLoading(state, { payload }) {
      state.groupSchedule.createOrEditLoading = payload;
    },
    editGroupScheduleEditOption(state, { payload }) {
      state.groupSchedule.edit = payload;
    },
    resetCreateGroupScheduleOptions(state) {
      state.groupSchedule.create = initialState.groupSchedule.create;
    },
    resetEditGroupScheduleOptions(state) {
      state.groupSchedule.edit = initialState.groupSchedule.edit;
    },
    updateGroupScheduleEditOptions(state, { payload }) {
      state.groupSchedule.edit = {
        ...state.groupSchedule.edit,
        ...payload,
      };
    },
    setSelectedRoleIdsInEdit(state, { payload }) {
      state.groupSchedule.edit.priorityRoles.value = payload;
    },
    startAttGroupTableLoading(state, { payload }) {
      state.attendanceGroup.loading = payload;
    },
    resetAttGroupPageNum(state) {
      state.attendanceGroup.pagination.pageNum = 1;
    },
    setAttGroupDataAndTotal(state, { payload: { data, total } }) {
      state.attendanceGroup.data = data;
      state.attendanceGroup.total = total;
      state.attendanceGroup.loading = false;
    },
    updateAttGroupSearchValues(state, { payload }) {
      state.attendanceGroup.searchValues = payload;
    },
    updateAttGroupCreateVisiable(state, { payload }) {
      state.attendanceGroup.createOrEditVisible = payload;
    },
    updateAttGroupCreateOptions(state, { payload }) {
      state.attendanceGroup.create = {
        ...state.attendanceGroup.create,
        ...payload,
      };
    },
    updateAttGroupPagination(state, { payload }) {
      state.attendanceGroup.pagination = {
        ...state.attendanceGroup.pagination,
        ...payload,
      };
    },
    setSelectedDutyGroupIds(state, { payload: { selectedIds, tableData } }) {
      state.attendanceGroup.create.dutyGroupIdList = {
        ...state.attendanceGroup.create.dutyGroupIdList,
        value: selectedIds,
        table: tableData,
        errors: undefined,
        dirty: false,
        touched: true,
        validating: false,
      };
    },
    saveAttGroupDetail(state, { payload: { edit, schedule } }) {
      state.attendanceGroup.edit = edit;
      state.attendanceGroup.schedule = {
        ...state.attendanceGroup.schedule,
        ...schedule,
      };
    },
    updateAttGroupSchedule(state, { payload }) {
      state.attendanceGroup.schedule = {
        ...state.attendanceGroup.schedule,
        ...payload,
      };
    },
    saveAttGroupScheduleDetail(state, { payload }) {
      state.attendanceGroup.scheduleCalendar = payload;
    },
    saveAttGroupScheduleUsers(state, { payload }) {
      state.attendanceGroup.schedule.scheduleStaffJson = payload;
    },
    updateScheduleCalendar(state, { payload: { userIds, seldectUsers, scheduleUnicode } }) {
      state.attendanceGroup.schedule.scheduleStaffJson[scheduleUnicode] = seldectUsers;
      if (!state.attendanceGroup.schedule.scheduleUnicodeChange.includes(scheduleUnicode)) {
        state.attendanceGroup.schedule.scheduleUnicodeChange = [
          ...state.attendanceGroup.schedule.scheduleUnicodeChange,
          scheduleUnicode,
        ];
        state.attendanceGroup.schedule.scheduleStaffList = [
          ...state.attendanceGroup.schedule.scheduleStaffList,
          {
            scheduleUnicode: scheduleUnicode,
            staffIds: userIds,
          },
        ];
      } else {
        const newList = state.attendanceGroup.schedule.scheduleUnicodeChange
          .map(el => {
            if (el === scheduleUnicode) {
              return {
                scheduleUnicode: el,
                staffIds: userIds,
              };
            }
            return null;
          })
          .filter(Boolean);
        state.attendanceGroup.schedule.scheduleStaffList = newList;
      }
    },
    updateTimeSelectVisible(state, { payload }) {
      state.attendanceGroup.schedule.timeSelectVisible = payload;
    },
    updateTimeModalButtonLoading(state, { payload }) {
      state.attendanceGroup.schedule.timeModalButtonLoading = payload;
    },
    resetCreateAttGroupOptions(state) {
      state.attendanceGroup.create = initialState.attendanceGroup.create;
      state.attendanceGroup.schedule = initialState.attendanceGroup.schedule;
      state.attendanceGroup.scheduleCalendar = initialState.attendanceGroup.scheduleCalendar;
    },
    updateEditOptions(state, { payload }) {
      state.attendanceGroup.edit = {
        ...state.attendanceGroup.edit,
        ...payload,
      };
    },
    setSelectedIdsInEdit(state, { payload: { selectedIds, tableData } }) {
      state.attendanceGroup.edit.dutyGroupIdList.value = selectedIds;
      state.attendanceGroup.edit.dutyGroupIdList.table = tableData;
    },
    saveDateInSchedule(state, { payload }) {
      state.attendanceGroup.dateInSchedule = payload;
    },

    updateMonthlySearchValues(state, { payload }) {
      state.monthly.searchValues = {
        ...state.monthly.searchValues,
        ...payload,
      };
    },
    resetShiftMonthlyPageNum(state) {
      state.monthly.pagination.pageNum = 1;
    },
    startMonthlyTableLoading(state, { payload }) {
      state.monthly.loading = payload;
    },
    setMonthlyDataAndTotal(state, { payload: { data, total } }) {
      state.monthly.data = data;
      state.monthly.total = total;
      state.monthly.loading = false;
    },
    resetMonthySearchValues(state) {
      state.monthly.searchValues = { ...initialState.monthly.searchValues };
      state.monthly.pagination = { ...initialState.monthly.pagination };
    },
    setMonthlyPagination(state, { payload }) {
      state.monthly.pagination = payload;
    },
    startDailyTableLoading(state, { payload }) {
      state.daily.loading = payload;
    },
    resetShiftDailyPageNum(state) {
      state.daily.pagination.pageNum = 1;
    },
    setDailyDataAndTotal(state, { payload: { data, total } }) {
      state.daily.data = data;
      state.daily.total = total;
      state.daily.loading = false;
    },
    updatDailySearchValues(state, { payload }) {
      state.daily.searchValues = {
        ...state.daily.searchValues,
        ...payload,
      };
    },
    setDailyPagination(state, { payload }) {
      state.daily.pagination = payload;
    },
    resetDailySearchValues(state) {
      state.daily.searchValues = { ...initialState.daily.searchValues };
      state.daily.pagination = { ...initialState.daily.pagination };
    },
    updatCheckSearchValues(state, { payload }) {
      state.check.searchValues = {
        ...state.check.searchValues,
        ...payload,
      };
    },
    startCheckTableLoading(state, { payload }) {
      state.check.loading = payload;
    },
    resetCheckPageNum(state) {
      state.check.pagination.pageNum = 1;
    },
    setCheckDataAndTotal(state, { payload: { data, total } }) {
      state.check.data = data;
      state.check.total = total;
      state.check.loading = false;
    },
    setCheckPagination(state, { payload }) {
      state.check.pagination = payload;
    },
    resetCheckSearchValues(state) {
      state.check.searchValues = { ...initialState.check.searchValues };
      state.check.pagination = { ...initialState.check.pagination };
    },
    updatRecordSearchValues(state, { payload }) {
      state.record.searchValues = {
        ...state.record.searchValues,
        ...payload,
      };
    },
    startRecordTableLoading(state, { payload }) {
      state.record.loading = payload;
    },
    resetRecordPageNum(state) {
      state.record.pagination.pageNum = 1;
    },
    setRecordDataAndTotal(state, { payload: { data, total } }) {
      state.record.data = data;
      state.record.total = total;
      state.record.loading = false;
    },
    resetRecordSearchValues(state) {
      state.record.searchValues = { ...initialState.record.searchValues };
      state.record.pagination = { ...initialState.record.pagination };
    },
    setRecordPagination(state, { payload }) {
      state.record.pagination = payload;
    },
    resetScheduleStaffList(state, { payload }) {
      state.attendanceGroup.schedule.scheduleStaffList = payload;
    },
    saveStatutoryHolidays(state, { payload }) {
      state.attendanceGroup.schedule.statutoryHolidays = payload;
    },
    saveAttGroupListByShifts(state, { payload }) {
      state.shiftSys.editOptions.attGroupListByShifts = payload;
    },
    resetAttendanceGroupScheduleCalendar(state) {
      state.attendanceGroup.scheduleCalendar = initialState.attendanceGroup.scheduleCalendar;
      state.attendanceGroup.schedule = initialState.attendanceGroup.schedule;
    },

    setCreateDutyGroupSubmitLoading(state, { payload }) {
      state.dutyGroup.create.submitLoading = payload;
    },
    setEditDutyGroupSubmitLoading(state, { payload }) {
      state.dutyGroup.edit.submitLoading = payload;
    },
  },
});

export const STAFF_SHIFT_SLICE_NAME = staffShiftSlice.name;
export const staffShiftActions = staffShiftSlice.actions;
export default staffShiftSlice.reducer;

function getIsTommorrow(onDutyTime, offDutyTime) {
  if (onDutyTime.value && offDutyTime.value) {
    const startTime = onDutyTime.value.valueOf();
    const endTime = offDutyTime.value.valueOf();
    if (endTime <= startTime) {
      return true;
    }
    return false;
  }

  return false;
}

function getDateRange(onlyToday) {
  const now = moment();
  const yesterday = now.clone().subtract(1, 'days');
  if (onlyToday) {
    return [now, now];
  }
  // 如果昨天和今天跨月了，则取昨天的时间
  if (yesterday.format('MM') !== now.format('MM')) {
    return [yesterday, yesterday];
  } else {
    return [now.clone().startOf('month'), yesterday];
  }
}
