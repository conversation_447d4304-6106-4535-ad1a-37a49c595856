import { createSlice } from '@reduxjs/toolkit';

const INITIAL_STATE = {
  loading: true,
  vendorPage: {
    list: [],
    total: '',
  },
  vendorPageCondition: {
    pageNum: 1,
    pageSize: 10,
  },
  searchValues: {},
  viewVendorVisible: false,
  vendorDetail: {},
};

const vendorSlice = createSlice({
  name: 'vendorManage',
  initialState: INITIAL_STATE,
  reducers: {
    request: state => {
      state.loading = true;
    },
    failure: state => {
      state.loading = false;
    },
    fetchVendorListSuccess: (state, { payload: { total, data } }) => ({
      ...state,
      loading: false,
      vendorPage: {
        list: data,
        total: total,
        pageSize: '',
      },
    }),
    viewVendorVisible: state => ({
      ...state,
      viewVendorVisible: !state.viewVendorVisible,
    }),
    saveVendorPageConditionSuccess: (state, { payload }) => ({
      ...state,
      vendorPageCondition: payload,
    }),
    saveVendorDetailSuccess: (state, { payload }) => ({
      ...state,
      vendorDetail: payload,
    }),
    updateSearchValues(state, { payload }) {
      state.searchValues = {
        ...state.searchValues,
        ...payload,
      };
    },
    resetSearchValues(state) {
      state.searchValues = {};
    },
  },
});

export const VENDOR_SLICE_NAME = vendorSlice.name;
export const vendorActions = vendorSlice.actions;
export default vendorSlice.reducer;
