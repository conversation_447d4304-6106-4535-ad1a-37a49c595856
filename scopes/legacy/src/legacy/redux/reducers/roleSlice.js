import { createSlice } from '@reduxjs/toolkit';

const INITIAL_STATE = {
  // 分页查询角色信息
  roleList: {
    list: [],
    total: '',
    pageNo: 1,
    pageSize: 10,
  },
  // 根据roleID查询的角色基本信息
  basicInfo: {},
  // 请求加载中
  loading: true,
  // 查询角色选择option
  searchType: 'roleName',
  //角色下面的权限列表
  powerList: {
    list: [],
    total: 0,
    loading: false,
    pageNo: 1,
    pageSize: 10,
    searchName: '',
    searchType: 'PERMISSION_NAME',
  },

  // 所有角色
  allRoleList: [],
  searchCondition: {
    pageNo: 1,
    pageSize: 10,
    searchType: 'ROLE_NAME',
    searchName: '',
  },
  connectPowerVisible: false,
  connectPowerLoading: false,
  allPowerList: [],
  checkedTag: [],
  searchPowerCondition: {},
  allPowerJson: null,
};
const roleSlice = createSlice({
  name: 'roleManage',
  initialState: INITIAL_STATE,
  reducers: {
    request: state => ({ ...state, loading: true }),
    success: (state, { payload: { total, data } }) => ({
      ...state,
      loading: false,
      roleList: {
        list: data,
        total: total,
        pageNo: 1,
        pageSize: 10,
      },
    }),
    failure: state => ({
      ...state,
      loading: false,
    }),

    // 关联权限
    connectPowerVisible: state => ({
      ...state,
      connectPowerVisible: !state.connectPowerVisible,
    }),
    // 关联权限
    editConnectPowerLoading: state => ({
      ...state,
      connectPowerLoading: !state.connectPowerLoading,
    }),
    // 查询角色详情
    detailSuccess: (state, { payload }) => ({
      ...state,
      loading: false,
      basicInfo: payload,
    }),
    // 查询角色下面的权限列表
    rolePowerListSuccess: (state, { payload }) => ({
      ...state,
      powerList: { ...payload },
      loading: false,
    }),

    // 查询所有角色(不分页)
    allUserRoleListSuccess: (state, { payload }) => ({
      ...state,
      allRoleList: payload,
    }),
    // 删除角色
    deleteRoleSuccess: state => ({
      ...state,
      loading: false,
    }),
    saveSearchConditionSuccess: (state, { payload }) => ({
      ...state,
      searchCondition: {
        ...state.searchCondition,
        ...payload,
      },
    }),

    fetchAllPowerListTreeSuccess: (state, { payload }) => ({
      ...state,
      allPowerList: payload.treeList,
      allPowerJson: payload.normalizedList,
    }),
    saveCheckedTag: (state, { payload }) => ({
      ...state,
      checkedTag: payload,
    }),

    searchPowerByRole: (state, { payload }) => ({
      ...state,
      searchPowerCondition: payload,
    }),
  },
});

export const ROLE_MANAGE_SLICE_NAME = roleSlice.name;
export const roleActions = roleSlice.actions;
export default roleSlice.reducer;
