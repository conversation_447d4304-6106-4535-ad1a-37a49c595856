import { createSlice } from '@reduxjs/toolkit';
import uniq from 'lodash/uniq';
import uniqBy from 'lodash/uniqBy';

import { MERGE_SCOPE_KEY_MAP, YES_OR_NO_KEY_MAP } from '@manyun/dc-brain.legacy.constants';

const initialState = {
  /*** list start ***/
  searchValues: {},
  pagination: {
    pageNum: 1,
    pageSize: 10,
  },
  data: [],
  total: 0,
  selectedRowKeys: [],
  loading: false,
  /*** list end ***/

  /*** new start ***/
  stepIdx: 0,
  stepBtnLoading: false,
  step1FieldValues: {
    deviceType: { value: null },
    formattedPointValue: { value: undefined },
    conditions: { value: [] },
    conditionValues: { value: [] },
    expire: {},
    name: {},
    available: { value: YES_OR_NO_KEY_MAP.YES },
    description: {},
  },
  step2FieldValues: {
    childItemDeviceTypes: { value: [] },
  },

  /**
   * 每种设备类型对应的收敛关系
   */
  mergeScopeMap: {},

  childItemsMap: {},
  /*** new end ***/
};

const mergedMonitorConfigSlice = createSlice({
  name: 'mergedMonitorConfig',
  initialState: initialState,
  reducers: {
    request: state => ({ ...state, loading: true }),
    updateSearchValues(state, { payload }) {
      state.searchValues = {
        ...state.searchValues,
        ...payload,
      };
    },
    resetSearchValuesAndPagination(state) {
      state.searchValues = {};
      state.pagination = { ...initialState.pagination };
    },
    setPagination(state, { payload }) {
      state.pagination = payload;
    },
    setDataAndTotal(state, { payload: { data, total } }) {
      state.data = data;
      state.total = total;
      state.loading = false;
    },
    setSelectedRowKeys(state, { payload }) {
      state.selectedRowKeys = payload;
    },
    resetSelectedRowKeys(state) {
      state.selectedRowKeys = [];
    },
    updateStep1FieldValues(state, { payload }) {
      state.step1FieldValues = {
        ...state.step1FieldValues,
        ...payload,
      };
    },
    updateChildItems(state, { payload: { deviceTypes, childItemsMap } }) {
      state.step2FieldValues.childItemDeviceTypes.value = uniq([
        ...state.step2FieldValues.childItemDeviceTypes.value,
        ...deviceTypes,
      ]);
      Object.keys(childItemsMap).forEach(deviceType => {
        const childItems = childItemsMap[deviceType];
        if (state.childItemsMap[deviceType]) {
          state.childItemsMap[deviceType] = uniqBy(
            [...state.childItemsMap[deviceType], ...childItems],
            'metaCode'
          );
        } else {
          state.childItemsMap[deviceType] = childItems;
        }
      });
      deviceTypes.forEach(deviceType => {
        if (state.mergeScopeMap[deviceType] !== undefined) {
          return;
        }
        const parentDeviceType = state.step1FieldValues.deviceType.value.code;
        if (deviceType === parentDeviceType) {
          state.mergeScopeMap[deviceType] = MERGE_SCOPE_KEY_MAP.WITHIN;
          return;
        }
        state.mergeScopeMap[deviceType] = MERGE_SCOPE_KEY_MAP.DOWN;
      });
    },
    deleteDeviceType(state, { payload: deviceType }) {
      state.step2FieldValues.childItemDeviceTypes.value =
        state.step2FieldValues.childItemDeviceTypes.value.filter(dt => dt !== deviceType);
      delete state.mergeScopeMap[deviceType];
      delete state.childItemsMap[deviceType];
    },
    deleteChildItem(
      { step2FieldValues, childItemsMap, mergeScopeMap },
      { payload: { shouldDeleteDeviceType, deviceType, childItemMetaCode } }
    ) {
      if (shouldDeleteDeviceType) {
        step2FieldValues.childItemDeviceTypes.value =
          step2FieldValues.childItemDeviceTypes.value.filter(t => t !== deviceType);
        delete childItemsMap[deviceType];
        delete mergeScopeMap[deviceType];
      } else {
        const idx = childItemsMap[deviceType].findIndex(
          ({ metaCode }) => metaCode === childItemMetaCode
        );
        childItemsMap[deviceType].splice(idx, 1);
      }
    },
    updateChildItem(
      { childItemsMap },
      { payload: { deviceType, childItemMetaCode, conditions, conditionValues } }
    ) {
      const idx = childItemsMap[deviceType].findIndex(
        ({ metaCode }) => metaCode === childItemMetaCode
      );
      childItemsMap[deviceType].splice(idx, 1, {
        ...childItemsMap[deviceType][idx],
        conditions,
        conditionValues,
      });
    },
    updateMergeScope({ mergeScopeMap }, { payload: { deviceType, mergeScope } }) {
      mergeScopeMap[deviceType] = mergeScope;
    },
    resetState4Creation(state) {
      state.step1FieldValues = initialState.step1FieldValues;
      state.step2FieldValues = initialState.step2FieldValues;
      state.mergeScopeMap = initialState.mergeScopeMap;
      state.childItemsMap = initialState.childItemsMap;
      state.stepIdx = initialState.stepIdx;
    },
    updateStep2Infos(state, { payload: { deviceTypes, childItemsMap, mergeScopeMap } }) {
      state.step2FieldValues.childItemDeviceTypes.value = deviceTypes;
      state.childItemsMap = childItemsMap;
      state.mergeScopeMap = mergeScopeMap;
    },
    goToPrevStep(state) {
      if (state.stepIdx <= 0) {
        return;
      }
      state.stepIdx = state.stepIdx - 1;
    },
    goToNextStep(state) {
      if (state.stepIdx >= 2) {
        // 目前最多 3 步
        return;
      }
      state.stepIdx = state.stepIdx + 1;
    },
    setStepBtnLoading(state, { payload }) {
      state.stepBtnLoading = payload;
    },
    updateDeviceType(state, { payload: { oldDeviceType, newDeviceType } }) {
      state.step2FieldValues.childItemDeviceTypes.value =
        state.step2FieldValues.childItemDeviceTypes.value.map(deviceType => {
          if (deviceType === oldDeviceType) {
            return newDeviceType;
          }
          return deviceType;
        });
      delete state.childItemsMap[oldDeviceType];
      delete state.mergeScopeMap[oldDeviceType];
    },
    resetPageNum({ pagination }) {
      pagination.pageNum = 1;
    },
  },
});

export const MERGED_MONITOR_CONFIG_SLICE_NAME = mergedMonitorConfigSlice.name;
export const mergedMonitorConfigActions = mergedMonitorConfigSlice.actions;
export default mergedMonitorConfigSlice.reducer;
