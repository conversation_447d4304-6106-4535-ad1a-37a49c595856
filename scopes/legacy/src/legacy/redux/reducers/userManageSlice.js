import { createSlice } from '@reduxjs/toolkit';

const INITIAL_STATE = {
  userList: {
    list: [],
    total: 0,
    pageNo: 1,
    pageSize: 10,
    searchName: '',
    searchType: 'USER_NAME',
    loading: true,
  },
  userGroupLoading: false,
  basicInfo: { modifier: {}, id: null },
  editBasicInfoVisible: false,
  logVisible: false,
  total: 0,
  logList: [],
  logTotal: 0,
  logLoading: false,
  loginSettingsLoading: false,
  editBasicInfoLoading: false,
  joinUserGroupTotal: 0,
  joinUserGroupList: [],
  addNewUserLoading: false,
  associatedUserGroupVisible: false,
};

const userSlice = createSlice({
  name: 'userManage',
  initialState: INITIAL_STATE,
  reducers: {
    updateUsers: (state, { payload: { login, userList } }) => ({ ...state, login, userList }),
    updateUsersDetailBasic: (state, { payload: { basic } }) => ({ ...state, basic }),
    request: state => ({
      ...state,
      userList: {
        ...state.userList,
        loading: true,
      },
    }),
    success: (state, { payload }) => ({
      ...state,
      userList: { ...payload },
    }),
    failure: (state, { payload }) => ({
      ...state,
      userGroupLoading: false,
      logLoading: false,
      loginSettingsLoading: false,
      editBasicInfoLoading: false,
      userList: {
        ...state.userList,
        loading: false,
      },
      loading: false,
      basicInfo: {
        ...state.basicInfo,
        id: payload?.id ?? state.basicInfo.id,
      },
    }),
    reset: () => INITIAL_STATE,
    // selectUserListSuccess: (state, { payload }) => ({
    //   ...state,
    //   selectUserList: payload,
    // }),
    userGroupListRequest: state => ({ ...state, userGroupLoading: true }),
    userGroupListSuccess: (state, { payload }) => ({
      ...state,
      userGroupList: payload,
      userGroupLoading: false,
    }),
    detailSuccess: (state, { payload }) => ({
      ...state,
      basicInfo: payload,
    }),
    editBasicInfoVisible: state => ({
      ...state,
      editBasicInfoVisible: !state.editBasicInfoVisible,
    }),
    editLoginSettingVisible: state => ({
      ...state,
      editLoginSettingVisible: !state.editLoginSettingVisible,
    }),
    logVisible: state => ({
      ...state,
      logVisible: !state.logVisible,
      logLoading: !state.logLoading,
    }),
    editLoginSettingsLoading: state => ({
      ...state,
      loginSettingsLoading: !state.loginSettingsLoading,
    }),
    editBasicInfoLoading: state => ({
      ...state,
      editBasicInfoLoading: !state.editBasicInfoLoading,
    }),
    logSuccess: (state, { payload: { data, total } }) => ({
      ...state,
      logList: data,
      logTotal: total,
      logLoading: false,
    }),
    joinUserGroupSuccess: (state, { payload: { data, total } }) => ({
      ...state,
      joinUserGroupList: data,
      joinUserGroupTotal: total,
      loading: false,
    }),
    addNewUserLoading: state => ({
      ...state,
      addNewUserLoading: !state.addNewUserLoading,
    }),
    associatedUserGroupVisible: state => ({
      ...state,
      associatedUserGroupVisible: !state.associatedUserGroupVisible,
    }),
  },
});

export const USER_MANAGE_SLICE_NAME = userSlice.name;
export const userManageActions = userSlice.actions;
export default userSlice.reducer;
