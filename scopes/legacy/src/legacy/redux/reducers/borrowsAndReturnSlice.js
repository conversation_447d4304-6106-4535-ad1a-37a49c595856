import { createSlice } from '@reduxjs/toolkit';

const INITIAL_STATE = {
  list: {
    searchValues: {
      borrowNo: {},
      title: {},
      blockGuid: {},
      borrower: {},
      status: {},
      creatorId: {},
      createTimeRange: { value: [] },
      borrowTimeRange: { value: [] },
    },
    searchConditions: {},
    data: [],
    total: 0,
    pagination: {
      pageNum: 1,
      pageSize: 10,
    },
    loading: false,
  },
  create: {
    createValues: {},
  },
  detail: {
    info: {},
    borrowApplyList: [],
    borrowAndReturnRecord: {
      device: {
        list: [],
        borrowNum: 0,
        toBeReturnedNum: 0,
        initList: [],
      },
      spare: {
        list: [],
        borrowNum: 0,
        toBeReturnedNum: 0,
        initList: [],
      },
    },
    tableLoading: false,
    renewLendRecords: {
      renewTableData: [],
      transferTableData: [],
    },
    ticketRecords: {
      exWareHouseList: [],
      inWareHouseList: [],
    },
    returnRecords: {
      deviceData: [],
      spareData: [],
    },
  },
};

const borrowsAndReturnSlice = createSlice({
  name: 'borrowsAndReturn',
  initialState: INITIAL_STATE,
  reducers: {
    failure(state) {
      state.list.loading = false;
      state.detail.tableLoading = false;
    },
    updateTableLoading(state, { payload }) {
      state.list.loading = payload;
    },
    resetPageNum(state) {
      state.list.pagination.pageNum = 1;
    },
    setDataAndTotal(state, { payload: { data, total } }) {
      state.list.data = data;
      state.list.total = total;
      state.list.loading = false;
    },
    setPagination(state, { payload }) {
      state.list.pagination = payload;
    },
    setSearchConditions(state, { payload }) {
      state.list.searchConditions = payload;
    },
    setBorrowInfo(state, { payload }) {
      state.detail.info = payload;
    },
    setBorrowApplyList(state, { payload }) {
      state.detail.borrowApplyList = payload;
      state.detail.tableLoading = false;
    },
    setBorrowAndReturnAssertInfo(state, { payload }) {
      state.detail.borrowAndReturnRecord = payload;
      state.detail.tableLoading = false;
    },
    updateDetailTableLoading(state, { payload }) {
      state.detail.tableLoading = payload;
    },
    updateBorrowDeviceTableData(state, { payload }) {
      state.detail.borrowAndReturnRecord.device.list = payload;
    },
    updateBorrowSpareTableData(state, { payload }) {
      state.detail.borrowAndReturnRecord.spare.list = payload;
    },
    updateRenewLendRecords(state, { payload }) {
      state.detail.renewLendRecords = payload;
      state.detail.tableLoading = false;
    },
    updateTicketRecords(state, { payload }) {
      state.detail.ticketRecords = payload;
      state.detail.tableLoading = false;
    },
    updateSearchValues(state, { payload }) {
      state.list.searchValues = { ...state.list.searchValues, ...payload };
    },
    resetSearchValuesAndPagination(state) {
      state.list.searchValues = INITIAL_STATE.list.searchValues;
      state.list.pagination = INITIAL_STATE.list.pagination;
    },
    updateReturnRecords(state, { payload }) {
      state.detail.returnRecords = payload;
      state.detail.tableLoading = false;
    },
    resetDetail(state) {
      state.detail.info = INITIAL_STATE.detail;
    },
  },
});

export const BORROW_AND_RETURN_SLICE_NAME = borrowsAndReturnSlice.name;
export const borrowsAndReturnActions = borrowsAndReturnSlice.actions;
export default borrowsAndReturnSlice.reducer;
