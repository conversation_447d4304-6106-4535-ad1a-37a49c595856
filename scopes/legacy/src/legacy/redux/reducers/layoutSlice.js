import { createSlice } from '@reduxjs/toolkit';

const INITIAL_STATE = {
  menuList: null,
};

const layoutSlice = createSlice({
  name: 'layout',
  initialState: INITIAL_STATE,
  reducers: {
    menuListSuccess: (state, { payload }) => ({
      ...state,
      menuList: payload,
    }),
  },
});

export const LAYOUT_SLICE_NAME = layoutSlice.name;
export const layoutActions = layoutSlice.actions;
export default layoutSlice.reducer;
