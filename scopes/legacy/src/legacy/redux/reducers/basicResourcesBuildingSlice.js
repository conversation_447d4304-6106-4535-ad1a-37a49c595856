import { createSlice } from '@reduxjs/toolkit';

const INITIAL_STATE = {
  buildingList: {
    list: [],
    total: 0,
    pageNum: 1,
    pageSize: 10,
    loading: true,
  },
  metaSpaceList: [],
  createLoading: false,
  deleting: false,
  searchValues: {},
};

const basicResourcesBuildingSlice = createSlice({
  name: 'basicResourcesBuilding',
  initialState: INITIAL_STATE,
  reducers: {
    request: state => ({
      ...state,
      buildingList: {
        ...state.buildingList,
        loading: true,
      },
    }),
    success: (state, { payload }) => ({
      ...state,
      buildingList: { ...payload },
    }),
    failure: state => ({
      ...state,
      createLoading: false,
      buildingList: {
        ...state.buildingList,
        loading: false,
      },
    }),
    metaSuccess: (state, { payload }) => ({
      ...state,
      metaSpaceList: payload,
    }),
    onCreateLoading: state => ({
      ...state,
      createLoading: !state.createLoading,
    }),
    onSelectLoading: state => ({
      ...state,
      selectLoading: !state.selectLoading,
    }),

    startDelete(state) {
      state.deleting = true;
    },
    deleteSuccess(state) {
      state.deleting = false;
    },
    deleteError(state) {
      state.deleting = false;
    },
    updateSearchValues(state, { payload }) {
      state.searchValues = {
        ...state.searchValues,
        ...payload,
      };
    },
    resetSearchValues(state, { payload }) {
      state.searchValues = {};
    },
  },
});

export const BASIC_RESOURCES_BUILDING_SLICE_NAME = basicResourcesBuildingSlice.name;
export const basicResourcesBuildingActions = basicResourcesBuildingSlice.actions;
export default basicResourcesBuildingSlice.reducer;
