import { createSlice } from '@reduxjs/toolkit';
import shallowequal from 'shallowequal';

const initialState = {
  //#region 概览数据

  pendingAlarmsCount: '--',
  pendingEventsCount: '--',
  pendingChangesCount: '--',
  pendingTicketsCount: '--',

  //#endregion

  /**
   * 排班信息分组，按机房、楼、今天（或明天）分组
   * */
  userShiftsGroups: {
    /**
     * `kyes` by block GUID
     *   - `key`: `${blockGuid}`
     *   - `value`: `string[]`(keys)
     */
    today: {
      /**
       * User Shift Entities
       *   - `key`: ``${blockGuid}_$$_${staffId}``
       *   - `value`: `UserShift`
       */
      entities: {},
    },
    tomorrow: {
      entities: {},
    },
  },

  pendingMattersList: {
    ticket: {
      data: [],
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
    change: {
      data: [],
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
    event: {
      data: [],
      total: 0,
      pageNum: 1,
      pageSize: 10,
    },
    dateDataMapping: {},
  },
  overviewDataAlarmNum: [],
  // 曲线数据（按设备 `deviceGuid`）
  deviceRangeData: {},
  noticeList: [],

  // 每幢楼运行状态的个别测点的实时数据
  blockRealtimeDataMap: {
    // [blockGuid]: {
    //   [deviceType]: {
    //     deviceType: [deviceType],
    //     pointValueMap: {
    //       [pointCode]: {
    //         value: number
    //       }
    //     }
    //   }
    // }
  },

  // 每幢楼个别设备
  blockDevicesMap: {
    // [blockGuid]: {
    //   [deviceType]: [{ /* device */ }],
    // },
  },

  // 按机房维度统计的异常设备数量
  idcDevicesCount: {
    // [customized_device_type]: number
    // '10102': 2,
    // '1040A': 10,
    // '1050A': 8,
    // '1060A': 7,
    // '1090A': 6,
    // '1110A': 5,
    // '2010A': 4,
    // '2020A': 3,
    // '2020B': 9,
  },

  // 按楼维度统计的异常设备数量
  blockDevicesCountMap: {
    // 'EC01.A': {
    //   '10102': 1,
    //   '10402': 2,
    //   '10503': 3,
    //   '10901': 4,
    //   '20201': 5,
    //   '20222': 6,
    //   '20204': 7,
    //   '20207': 8,
    //   '20209': 9,
    // },
  },

  // 按包间维度统计的电力、暖通告警数量
  roomAlarmsCount: {},

  idc: null,
  blocks: [],
  runningStatesActiveBlock: null,
  roomsInfoActiveBlock: null,
  roomsMap: {},
  activeBlocksAlarms: {},
  PUEs: {
    day: null,
    month: null,
    months: null,
    year: null,
  },
};

const idcWorkbenchSlice = createSlice({
  name: 'idcWorkbench',
  initialState: initialState,
  reducers: {
    setBNRs(state, { payload: { idc, blocks, activeBlock, roomsMap } }) {
      state.idc = idc;
      state.blocks = blocks;
      state.runningStatesActiveBlock = activeBlock;
      state.roomsInfoActiveBlock = activeBlock;
      state.roomsMap = roomsMap;
    },
    updateActiveRunningStatesBlock(state, { payload }) {
      state.runningStatesActiveBlock = payload;
    },
    updateActiveRoomInfosBlock(state, { payload }) {
      state.roomsInfoActiveBlock = payload;
    },
    setActiveBlocksAlarms(state, { payload }) {
      state.activeBlocksAlarms = payload;
    },
    setDeviceRangeData: ({ deviceRangeData }, { payload: { data, pointGuids } }) => {
      let guidAndPointCodeArr = [];
      let propName = '';
      guidAndPointCodeArr = pointGuids.map(item => {
        const guid = item.deviceGuid + '-' + item.pointCode;
        return [...guidAndPointCodeArr, guid];
      });
      if (guidAndPointCodeArr.length) {
        propName = guidAndPointCodeArr.join('-');
      }
      pointGuids.map(item => {
        if (deviceRangeData[item.deviceGuid] === undefined) {
          deviceRangeData[item.deviceGuid] = {
            [propName]: data,
          };
        } else {
          deviceRangeData[item.deviceGuid][propName] = data;
        }

        return deviceRangeData;
      });
    },
    setAlarmObjectsCount(
      state,
      {
        payload: {
          pendingEventsCount,
          pendingAlarmsCount,
          pendingChangesCount,
          pendingTicketsCount,
          idcDevicesCount,
          blockDevicesCountMap,
          roomAlarmsCount,
        },
      }
    ) {
      state.pendingEventsCount = pendingEventsCount;
      state.pendingAlarmsCount = pendingAlarmsCount;
      state.pendingChangesCount = pendingChangesCount;
      state.pendingTicketsCount = pendingTicketsCount;
      state.idcDevicesCount = idcDevicesCount;
      state.blockDevicesCountMap = blockDevicesCountMap;
      state.roomAlarmsCount = roomAlarmsCount;
    },
    updateBlockRealtimeData(state, { payload: { blockGuid, deviceGuids = [], data = {} } }) {
      if (state.blockRealtimeDataMap[blockGuid] === undefined) {
        state.blockRealtimeDataMap[blockGuid] = {};
      }
      deviceGuids.forEach(deviceGuid => {
        if (data[deviceGuid] === undefined) {
          delete state.blockRealtimeDataMap[blockGuid][deviceGuid];
        } else {
          state.blockRealtimeDataMap[blockGuid][deviceGuid] = data[deviceGuid];
        }
      });
    },
    setBlockDevices(state, { payload: { blockGuid, devicesMap } }) {
      state.blockDevicesMap[blockGuid] = devicesMap;
    },
    getNoticeListSuccess: (state, { payload }) => {
      return {
        ...state,
        noticeList: payload,
      };
    },
    updateUserShifts(state, { payload: { viewMode, userShifts } }) {
      const isToday = viewMode === 'today';
      const blockUserShfitKeys = userShifts.reduce((_blockUserShfitKeys, userShift) => {
        // 接口有坑：查明日排班的接口将 `blockGuid` 存在了 `blockTag` 内
        const updateState = blockGuid => {
          // 因为同一个人在同一天可能出现多个班次，需要加上 `dutyId` 来保证唯一
          const key = `${blockGuid}_$$_${
            isToday ? `${userShift.bizId}_$$_${userShift.bizType}` : userShift.unicode
          }`;
          if (!Array.isArray(_blockUserShfitKeys[blockGuid])) {
            _blockUserShfitKeys[blockGuid] = [key];
          } else {
            _blockUserShfitKeys[blockGuid].push(key);
          }
          const existing = state.userShiftsGroups[viewMode].entities[key];
          if (shallowequal(existing, userShift)) {
            return _blockUserShfitKeys;
          }
          state.userShiftsGroups[viewMode].entities[key] = userShift;
        };

        if (isToday) {
          //今日接口变更：`blockGuid:string` 变为 `blockGuids: string[]`
          (userShift.blockGuids ?? []).forEach(blockGuid => {
            updateState(blockGuid);
          });
        } else {
          // 明日接口变更： 新增 `blockGuids: string[]` 替换`blockTag:string`字段，解决加班返回多个楼栋过滤的问题
          (userShift.blockGuids ?? []).forEach(blockGuid => {
            updateState(blockGuid);
          });
        }

        return _blockUserShfitKeys;
      }, {});
      Object.keys(blockUserShfitKeys).forEach(blockGuid => {
        state.userShiftsGroups[viewMode][blockGuid] = blockUserShfitKeys[blockGuid];
      });
    },
    setPendingMattersList(state, { payload: { dateDataMapping, ticket, change, event } }) {
      if (ticket) {
        state.pendingMattersList.ticket = ticket;
      }
      if (change) {
        state.pendingMattersList.change = change;
      }
      if (event) {
        state.pendingMattersList.event = event;
      }
      if (dateDataMapping) {
        state.pendingMattersList.dateDataMapping = {
          ...state.pendingMattersList.dateDataMapping,
          ...dateDataMapping,
        };
      }
    },
    savePUEs(state, { payload: { type, values } }) {
      state.PUEs[type] = values;
    },
    clearPUEs: state => {
      state.PUEs = initialState.PUEs;
    },
  },
});

export const idcWorkbenchInitialState = initialState;
export const IDC_WORKBENCH_SLICE_NAME = idcWorkbenchSlice.name;
export const idcWorkbenchActions = idcWorkbenchSlice.actions;
export default idcWorkbenchSlice.reducer;
