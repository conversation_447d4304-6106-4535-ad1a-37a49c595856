import { createSlice } from '@reduxjs/toolkit';

/**
 * @typedef {'error'|'no-graph'|'empty-graph'|'success'} PreviewCode
 */

const initialState = {
  isReady: false,
  devicesTree: null,
  id: null,
  graph: null,

  // 楼维度的完整电力、暖通拓扑
  blockGraphCache: {
    // EC01.A_$$_HVAC: { id: '...', graph: { ... } }
  },

  hvacPreview: {
    loading: false,

    /**
     * 用于标示 拓扑数据 请求的结果类型
     * @type {PreviewCode}
     */
    code: null,

    graph: null,
    devices: null,
    extraDevices: null,
  },

  fuelSystemPreview: {
    loading: false,

    /**
     * 用于标示 拓扑数据 请求的结果类型
     * @type {PreviewCode}
     */
    code: null,

    graph: null,
    devices: null,
    extraDevices: null,
  },
};

const topologyGraphixSlice = createSlice({
  name: 'topologyGraphix',
  initialState: initialState,
  reducers: {
    setIsReady(state, { payload }) {
      state.isReady = payload;
    },
    setDevicesTree(state, { payload }) {
      state.devicesTree = payload;
    },
    setGraph(state, { payload: { id, graph } }) {
      state.id = id;
      state.graph = graph;
    },
    reset(state) {
      state.isReady = false;
      state.devicesTree = null;
      state.id = null;
      state.graph = null;
    },

    updateBlockGraphCache(state, { payload: { blockGuid, topologyType, id, graph } }) {
      state.blockGraphCache[`${blockGuid}_$$_${topologyType}`] = {
        id,
        graph,
        updatedAt: Date.now(),
      };
    },

    setHVACGraphPreviewLoading(state, { payload: loading }) {
      state.hvacPreview.loading = loading;
      state.hvacPreview.code = null;
    },
    setHVACGraphPreviewResult(state, { payload: { code, graph, devices, extraDevices } }) {
      state.hvacPreview.loading = false;
      state.hvacPreview.code = code;
      state.hvacPreview.graph = graph;
      state.hvacPreview.devices = devices;
      state.hvacPreview.extraDevices = extraDevices;
    },

    setFuelSystemGraphPreviewLoading(state, { payload: loading }) {
      state.fuelSystemPreview.loading = loading;
      state.fuelSystemPreview.code = null;
    },
    setFuelSystemGraphPreviewResult(state, { payload: { code, graph, devices, extraDevices } }) {
      state.fuelSystemPreview.loading = false;
      state.fuelSystemPreview.code = code;
      state.fuelSystemPreview.graph = graph;
      state.fuelSystemPreview.devices = devices;
      state.fuelSystemPreview.extraDevices = extraDevices;
    },
  },
});

export const TOPOLOGY_GRAPHIX_SLICE_NAME = topologyGraphixSlice.name;
export const topologyGraphixActions = topologyGraphixSlice.actions;
export default topologyGraphixSlice.reducer;
