import { createSlice } from '@reduxjs/toolkit';

const INITIAL_STATE = {
  loading: true,
  confirmLoading: false,
  type: 'DEVICE',
  versionData: [],
  rangeVersionDataList: [],
  createVersionShow: false,
  deployVersionShow: false,
  versionListShow: false,
  batchDeployShow: false,
  rangeVersionTreeData: {},
};

const versionManageSlice = createSlice({
  name: 'versionManage',
  initialState: INITIAL_STATE,
  reducers: {
    request: state => {
      state.loading = true;
    },
    failure: state => {
      state.loading = false;
    },
    deployVersionSuccess: state => {
      state.deployVersionShow = false;
    },
    fetchVersionListSuccess: (state, { payload: { data } }) => ({
      ...state,
      loading: false,
      versionData: data,
    }),
    rangeVersionDataSuccess: (state, { payload }) => {
      Object.keys(payload).forEach(key => (state.rangeVersionTreeData[key] = payload[key].data));
    },
    clearRangeVersionData: state => {
      state.rangeVersionTreeData = {};
    },
    changeModal: (state, { payload }) => {
      state[payload] = !state[payload];
    },
    changeType: (state, { payload }) => {
      state.type = payload;
    },
    rangeVersionListSuccess: (state, { payload: { data } }) => ({
      ...state,
      loading: false,
      rangeVersionDataList: data,
    }),
    deployListSuccess: (state, { payload: { data } }) => ({
      ...state,
      loading: false,
      deployListData: data,
    }),
    confirmStart: state => {
      state.confirmLoading = true;
    },
    confirmEnd: state => {
      state.confirmLoading = false;
    },
  },
});

export const VERSION_MANAGE_NAME = versionManageSlice.name;
export const versionManageActions = versionManageSlice.actions;
export default versionManageSlice.reducer;
