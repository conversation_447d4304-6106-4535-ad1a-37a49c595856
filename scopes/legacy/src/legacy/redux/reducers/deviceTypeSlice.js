import { createSlice } from '@reduxjs/toolkit';

const INITIAL_STATE = {
  loading: true,
  deviceTypePage: {
    list: [],
    total: '',
  },
  deviceTypePageCondition: {
    pageNum: 1,
    pageSize: 10,
  },
  searchValues: {},
  deviceTypeDetail: {},
};

const deviceTypeSlice = createSlice({
  name: 'deviceTypeManage',
  initialState: INITIAL_STATE,
  reducers: {
    request: state => {
      state.loading = true;
    },
    failure: state => {
      state.loading = false;
    },
    getDeviceTypeListSuccess: (state, { payload: { total, data } }) => ({
      ...state,
      loading: false,
      deviceTypePage: {
        list: data,
        total: total,
      },
    }),
    saveDeviceTypePageConditionSuccess: (state, { payload }) => ({
      ...state,
      deviceTypePageCondition: payload,
    }),
    saveDeviceTypeDetailSuccess: (state, { payload }) => ({
      ...state,
      deviceTypeDetail: payload,
    }),
    updateSearchValues(state, { payload }) {
      state.searchValues = {
        ...state.searchValues,
        ...payload,
      };
    },
  },
});

export const DEVICE_TYPE_SLICE_NAME = deviceTypeSlice.name;
export const deviceTypeActions = deviceTypeSlice.actions;
export default deviceTypeSlice.reducer;
