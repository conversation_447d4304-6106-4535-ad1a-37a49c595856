import { createSlice } from '@reduxjs/toolkit';

import {
  CUSTOMIZED_NOTIFICATION_TEXT,
  MERGE_SCOPE_KEY_MAP,
} from '@manyun/dc-brain.legacy.constants';

const INITIAL_STATE = {
  // 告警通知文案配置时可选字段
  alarmCopyList: {
    // 物理信息
    physicalFields: [],
    // 故障信息
    faultedFields: [],
    // 其他信息
    otherFields: [
      {
        metaName: '自定义',
        metaCode: CUSTOMIZED_NOTIFICATION_TEXT,
        description: '自定义配置告警内容',
      },
    ],
  },
  // 用户拖拽生成的告警通知文案的数据源
  notificationTpl: [
    // {
    //   label: '', // 字段文案
    //   value: '', // 字段标识
    //   text: '', // 自定义字段的文案
    // }
  ],
  monitorItemTreeNodeMap: null,
  // 聚合收敛配置-聚合项
  parentMonitorItem: null,
  // 聚合收敛配置-被聚合项
  aggregatedMonitorItems: [],
  // 查看某个监控项数据
  viewingMonitorItem: null,
  alarmTypeJson: null,
};

const alarmSlice = createSlice({
  name: 'alarmManage',
  initialState: INITIAL_STATE,
  reducers: {
    setNotificationTxtTplFields: (
      state,
      { payload: { physicalFields, faultedFields, otherFields } }
    ) => ({
      ...state,
      alarmCopyList: {
        physicalFields,
        faultedFields,
        otherFields: [
          ...state.alarmCopyList.otherFields, // 需要保留前端自定义的字段
          ...otherFields,
        ],
      },
    }),
    setNotificationTpl: (state, { payload }) => ({
      ...state,
      notificationTpl: payload,
    }),
    setParentMonitorItem: (state, { payload }) => {
      state.parentMonitorItem = payload;
      state.aggregatedMonitorItems.forEach(item => {
        item.relationshipToParent = getRelationship(payload, item);
      });
    },
    setMonitorItemTreeNodeMap: (state, { payload }) => {
      state.monitorItemTreeNodeMap = payload;
    },
    setViewingMonitorItem: (state, { payload }) => {
      state.viewingMonitorItem = payload;
    },
  },
});

export const ALARM_MANAGE_SLICE_NAME = alarmSlice.name;
export const alarmActions = alarmSlice.actions;
export default alarmSlice.reducer;

function getRelationship(parentMonitorItem, monitorItem) {
  if (parentMonitorItem === null) {
    return;
  }
  if (parentMonitorItem.deviceType === monitorItem.deviceType) {
    return MERGE_SCOPE_KEY_MAP.WITHIN;
  }
  return MERGE_SCOPE_KEY_MAP.DOWN;
}
