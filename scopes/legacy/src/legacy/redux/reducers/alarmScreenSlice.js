import { createSlice } from '@reduxjs/toolkit';

const INITIAL_STATE = {
  alarmScreenList: {
    list: [],
    total: 0,
    pageNum: 1,
    pageSize: 10,
    loading: false,
    sortField: null,
    sortOrder: null, //ASCEND（正序） DESCEND(倒序),
  },
  alarmList: {
    list: [],
    total: 0,
    pageNum: 1,
    pageSize: 10,
    loading: true,
  },
  categoryDisplayBarList: [],
  eventList: {
    list: [],
    total: 0,
    pageNum: 1,
    pageSize: 10,
    loading: true,
  },
  createTicketVisible: false,
  createLoading: false,
  a: 1,
  customerList: {
    list: [],
    total: 0,
    pageNum: 1,
    pageSize: 10,
    loading: true,
  },
  eventTypeList: [],
  eventChildTypeList: [],
  createEventVisible: false,
  eventContent: {},
  alarmConvergenceList: {
    list: [],
    total: 0,
    pageNum: 1,
    pageSize: 10,
    loading: true,
    deviceTypeList: null,
  },
  metaQueryAlarmTypeEntities: {},
  metaCategoryEntities: {},
  mergeRuleName: '',
  alarmInfo: {},
  deviceRangeData: {},
  searchValues: {},
};

const alarmScreenSlice = createSlice({
  name: 'alarmScreen',
  initialState: INITIAL_STATE,
  reducers: {
    request: state => ({
      ...state,
      customerList: {
        ...state.customerList,
        loading: true,
      },
      alarmScreenList: {
        ...state.alarmScreenList,
        loading: true,
      },
      alarmList: {
        ...state.alarmList,
        loading: true,
      },
    }),
    failure: state => ({
      ...state,
      alarmConvergenceList: {
        ...state.alarmConvergenceList,
        loading: false,
      },
      // eventList: {
      //   ...state.eventList,
      //   loading: false,
      // },
      createLoading: false,
      customerList: {
        ...state.customerList,
        loading: false,
      },
      alarmScreenList: {
        ...state.alarmScreenList,
        loading: false,
      },
      alarmList: {
        ...state.alarmList,
        loading: false,
      },
      alarmInfo: {},
    }),
    featchAlarmScreenSuccess: (state, { payload }) => ({
      ...state,
      alarmScreenList: {
        ...payload,
        loading: false,
      },
    }),
    fetchAlarmSuccess: (state, { payload }) => ({
      ...state,
      alarmList: {
        ...payload,
        loading: false,
      },
    }),
    fetchEventListSuccess: (state, { payload }) => ({
      ...state,
      eventList: { ...payload, loading: false },
    }),
    createTicketVisible: state => ({
      ...state,
      createTicketVisible: !state.createTicketVisible,
    }),
    createLoading: state => ({
      ...state,
      createLoading: !state.createLoading,
    }),
    customerListRequest: state => ({
      ...state,
      customerList: { ...state.customerList, loading: true },
    }),
    customerListSuccess: (state, { payload }) => ({
      ...state,
      customerList: { ...payload, loading: false },
    }),
    createEventVisible: state => ({
      ...state,
      createEventVisible: !state.createEventVisible,
    }),
    eventTypeSuccess: (state, { payload }) => ({
      ...state,
      eventTypeList: payload,
    }),
    createEventContentSuccess: (state, { payload }) => ({
      ...state,
      eventContent: payload,
    }),
    alarmConvergenceSuccess: (state, { payload }) => ({
      ...state,
      alarmConvergenceList: { ...payload, loading: false },
    }),
    alarmDetailSuccess: (state, { payload: { list, mergeRuleName, alarmInfo } }) => ({
      ...state,
      categoryDisplayBarList: list,
      mergeRuleName,
      alarmInfo,
    }),
    metaQueryAlarmTypeEntities: (state, { payload }) => ({
      ...state,
      metaQueryAlarmTypeEntities: payload,
    }),
    metaCategoryEntities: (state, { payload }) => ({
      ...state,
      metaCategoryEntities: payload,
    }),
    setDeviceRangeData: ({ deviceRangeData }, { payload: { deviceGuid, pointCodes, data } }) => {
      const propName = pointCodes.join('-');
      if (deviceRangeData[deviceGuid] === undefined) {
        deviceRangeData[deviceGuid] = {
          [propName]: data,
        };
      } else {
        deviceRangeData[deviceGuid][propName] = data;
      }
    },
    updateSearchValues(state, { payload }) {
      state.searchValues = {
        ...state.searchValues,
        ...payload,
      };
    },
  },
});
export const ALARM_SCREEN_SLICE_NAME = alarmScreenSlice.name;
export const alarmScreenActions = alarmScreenSlice.actions;
export default alarmScreenSlice.reducer;
