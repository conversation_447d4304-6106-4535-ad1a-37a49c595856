import { createSlice } from '@reduxjs/toolkit';
import uniq from 'lodash/uniq';

const INITIAL_STATE = {
  loading: true,
  taskType: '',
  listServiceEndpoint: 'fetchValidNoticeList',
  ticketList: {
    list: [],
    total: '',
    loading: false,
    pagination: {
      pageNum: 1,
      pageSize: 10,
    },
    searchValues: {
      taskNo: {
        value: null,
      },
      taskType: {
        value: null,
      },
      taskTitle: {
        value: null,
      },
      idcTag: {
        value: null,
      },
      taskStatus: {
        value: undefined,
      },
      taskAssigneeName: {
        value: undefined,
      },
      creatorName: {
        value: undefined,
      },
      isDelay: {
        value: null,
      },
      gmtCreate: {
        value: null,
      },
      guidList: { value: [] },
      onlyByTime: {
        value: false,
      },
      onlyByEndTime: {
        value: false,
      },
      orderByTimeInfo: {
        value: undefined,
      },
    },
  },
  ticketView: {
    loading: false,
    basicInfo: {},
  },
  patrol: {
    new: {
      fieldValues: {
        title: '',
        // inspectSubType: '',
        location: [],
      },
      roomInfoList: [],
      deviceInfoList: [],
      checkItem: {},
    },
    detail: {
      roomInfo: [],
      checkSubjectGroup: [],
      checkItem: [],
      checkItemTypes: [],
      roomTypes: [],
      roomLoading: false,
      checkItemLoading: false,
      checkItemSaveValueLoading: false,
      checkItemExceptionHandlingLoading: false,
      lodaing: false,
      checkedCheckItemTypes: [],
      rowSpansGroupByDataIndex: null,
    },
  },
  power: {
    detail: {
      gridCount: [],
      gridList: [],
      gridListTotal: 0,
      checkedRoom: '',
      checkedRoomType: '',
      deviceGuids: [],
      pointValue: {},
    },
  },
  maintenance: {
    new: {
      fieldValues: {
        title: '',
        // inspectSubType: '',
        location: [],
      },
      roomInfoList: [],
      deviceInfoList: [],
      checkItem: {},
    },
    detail: {
      roomInfo: [],
      checkItem: [],
      checkItemTypes: [],
      roomTypes: [],
      roomLoading: false,
      checkItemLoading: false,
      checkItemSaveValueLoading: false,
      checkItemExceptionHandlingLoading: false,
      lodaing: false,
      rowSpansGroupByDataIndex: null,
    },
  },
  takeOverLoading: false,
  on_off: {
    create: {
      taskSubType: {
        value: null,
      },
      location: {
        value: null,
      },
      lineReason: {
        value: null,
      },
      deviceDtoList: {
        value: [],
      },
      fileInfoList: {
        value: [],
      },
    },
  },
  access_door: {
    create: {
      taskTitle: {
        value: null,
      },
      location: {
        value: null,
      },
      accessDoorTime: {
        value: null,
      },
      accessType: {
        value: null,
      },
      assetType: {
        value: null,
      },
      accessDoorReason: {
        value: null,
      },
      taskSla: {
        name: 'taskSla',
        value: { sla: 1, unit: 'HOUR' },
      },
      fileInfoList: {
        value: [],
      },
    },
    detail: {
      codeData: [],
      noCodeData: [],
    },
  },
  wareHouse: {
    detail: {
      deviceInfo: [],
      checkItem: [],
    },
  },
  inventory: {
    detail: {
      cardList: [],
      roomTypes: [],
      checkItem: [],
      targetConfig: { targetTags: [], roomTags: [] },
    },
  },
};
const ticketSlice = createSlice({
  name: 'ticket',
  initialState: INITIAL_STATE,
  reducers: {
    request: state => {
      state.ticketList.loading = true;
    },
    takeOverLoading: state => {
      state.takeOverLoading = !state.takeOverLoading;
    },
    success: (state, { payload: { total, data } }) => ({
      ...state,
      ticketList: {
        ...state.ticketList,
        list: data,
        total: total,
        loading: false,
      },
    }),
    failure: state => {
      state.ticketList.loading = false;
      state.takeOverLoading = false;
    },
    updateSearchValues: (state, { payload }) => {
      state.ticketList.searchValues = { ...state.ticketList.searchValues, ...payload };
    },
    resetSearchValuesAndPagination(state) {
      state.ticketList.searchValues = { ...INITIAL_STATE.ticketList.searchValues };
      state.ticketList.pagination = { ...INITIAL_STATE.ticketList.pagination };
    },
    setPagination(state, { payload }) {
      state.ticketList.pagination = payload;
    },
    resetPageNum(state) {
      state.ticketList.pagination = { pageNum: 1, pageSize: 10 };
    },
    setTicketBasicInfo(state, { payload }) {
      state.ticketView.basicInfo = {
        ...state.ticketView.basicInfo,
        ...payload,
      };
    },
    setTicketType(state, { payload }) {
      state.taskType = payload;
    },
    setPatrolRoomInfo(state, { payload }) {
      state.patrol.detail.roomInfo = payload;
    },
    setPatrolCheckSubjectGroup(state, { payload }) {
      state.patrol.detail.checkSubjectGroup = payload;
    },
    setPatrolCheckItem(state, { payload }) {
      state.patrol.detail.checkItem = payload;
    },
    setPatrolRoomTypes(state, { payload }) {
      state.patrol.detail.roomTypes = payload;
    },
    setPatrolCheckItemTypes(state, { payload }) {
      state.patrol.detail.checkItemTypes = payload;
    },
    setRoomLoading(state) {
      state.patrol.detail.roomLoading = !state.patrol.detail.roomLoading;
    },
    setCheckItemLoading(state) {
      state.patrol.detail.checkItemLoading = !state.patrol.detail.checkItemLoading;
    },
    setPatrolCheckItemSaveValueLoading(state) {
      state.patrol.detail.checkItemSaveValueLoading =
        !state.patrol.detail.checkItemSaveValueLoading;
    },
    setPatrolCheckItemExceptionHandlingLoading(state) {
      state.patrol.detail.checkItemExceptionHandlingLoading =
        !state.patrol.detail.checkItemExceptionHandlingLoading;
    },
    setCreatePatrolDevices(state, { payload }) {
      state.patrol.new.deviceInfoList = payload;
    },
    setCreatePatrolRooms(state, { payload }) {
      state.patrol.new.roomInfoList = payload;
    },
    setCreatePatrolCheckItem(state, { payload }) {
      state.patrol.new.checkItem = payload;
    },
    updateValues(state, { payload }) {
      state.patrol.new.fieldValues = { ...state.patrol.new.fieldValues, ...payload };
    },
    setPowerDetailCheckedRoom(state, { payload }) {
      state.power.detail.checkedRoom = payload.checkedRoom;
      state.power.detail.checkedRoomType = payload.checkedRoomType;
    },
    setPowerDetailGridCount(state, { payload }) {
      state.power.detail.gridCount = payload;
    },
    setPowerDetailGridList(state, { payload }) {
      state.power.detail.gridList = payload.list;
      state.power.detail.gridListTotal = payload.total;
    },
    setPowerDetailtLodaing(state) {
      state.power.detail.lodaing = !state.power.detail.lodaing;
    },
    setPowerDeviceGuids(state, { payload }) {
      state.power.detail.deviceGuids = payload;
    },
    setPowerPointValue(state, { payload }) {
      state.power.detail.pointValue = payload;
    },
    updateOnOffCreateFields(state, { payload }) {
      state.on_off.create = {
        ...state.on_off.create,
        ...payload,
      };
    },
    resetOnOffCreateValues(state) {
      state.on_off.create = INITIAL_STATE.on_off.create;
    },
    setMaintenanceRoomLoading(state) {
      state.maintenance.detail.roomLoading = !state.maintenance.detail.roomLoading;
    },
    setMaintenanceRoomInfo(state, { payload }) {
      state.maintenance.detail.roomInfo = payload;
    },
    setMaintenanceRoomTypes(state, { payload }) {
      state.maintenance.detail.roomTypes = payload;
    },
    setMaintenanceCheckItemLoading(state) {
      state.maintenance.detail.checkItemLoading = !state.maintenance.detail.checkItemLoading;
    },
    setMaintenanceCheckItem(state, { payload }) {
      state.maintenance.detail.checkItem = payload;
    },
    setMaintenanceCheckItemTypes(state, { payload }) {
      state.maintenance.detail.checkItemTypes = payload;
    },
    setMaintenanceCheckedTypes(state, { payload }) {
      state.maintenance.detail.checkedCheckItemTypes = payload;
    },
    setMaintenanceCheckItemSaveValueLoading(state) {
      state.maintenance.detail.checkItemSaveValueLoading =
        !state.maintenance.detail.checkItemSaveValueLoading;
    },
    setCreateMaintenanceDevices(state, { payload }) {
      state.maintenance.new.deviceInfoList = payload;
    },
    setCreateMaintenanceRooms(state, { payload }) {
      state.maintenance.new.roomInfoList = payload;
    },
    setCreateMaintenanceCheckItem(state, { payload }) {
      state.maintenance.new.checkItem = payload;
    },
    setWareHouseDeviceInfo(state, { payload }) {
      state.wareHouse.detail.deviceInfo = payload;
    },
    setWareHouseCheckItem(state, { payload }) {
      state.wareHouse.detail.checkItem = payload;
    },
    setInventoryRoomInfo(state, { payload }) {
      state.inventory.detail.cardList = payload;
      state.inventory.detail.roomTypes = uniq(payload).map(item => {
        return item.roomType;
      });
    },
    setInventoryCheckItem(state, { payload }) {
      state.inventory.detail.checkItem = payload;
    },
    setInventoryTargetConfig(state, { payload }) {
      state.inventory.detail.targetConfig = payload;
    },
    updateAccessDoorCreateFields(state, { payload }) {
      state.access_door.create = {
        ...state.access_door.create,
        ...payload,
      };
    },
    setAccsssDoorCodeDeviceData(state, { payload }) {
      state.access_door.detail.codeData = payload;
    },
    setAccsssDoorNoCodeDeviceData(state, { payload }) {
      state.access_door.detail.noCodeData = payload;
    },
    resetAccsssDoorCreateFileds(state) {
      state.access_door.create = INITIAL_STATE.access_door.create;
    },
    setListServiceEndpoint(state, { payload }) {
      state.listServiceEndpoint = payload;
    },
    setRowSpansGroupByDataIndex(state, { payload }) {
      state.patrol.detail.rowSpansGroupByDataIndex = payload;
    },
    setMaintenanceRowSpansGroupByDataIndex(state, { payload }) {
      state.maintenance.detail.rowSpansGroupByDataIndex = payload;
    },
    fetchTicketFilesStart(state) {
      state.ticketView.basicInfo = {
        ...state.ticketView.basicInfo,
        fileLoading: true,
      };
    },
    setTicketFiles(state, { payload }) {
      state.ticketView.basicInfo = {
        ...state.ticketView.basicInfo,
        fileList: payload,
        fileLoading: false,
      };
    },
    fetchTicketFilesEnd(state) {
      state.ticketView.basicInfo = {
        ...state.ticketView.basicInfo,
        fileLoading: false,
      };
    },
    resetBaicInfo(state) {
      state.ticketView.basicInfo = INITIAL_STATE.ticketView.basicInfo;
    },
    setTicketBasicInfoLoading(state, { payload }) {
      state.ticketView.loading = payload;
    },
  },
});

export const TICKET_SLICE_NAME = ticketSlice.name;
export const ticketActions = ticketSlice.actions;
export default ticketSlice.reducer;
