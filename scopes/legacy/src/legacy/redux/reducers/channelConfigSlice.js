import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  searchValues: {},
  data: [],
  total: 0,
  pagination: {
    pageNum: 1,
    pageSize: 10,
  },
  selectedRowKeys: [],
  loading: false,
  deviceInfo: {
    loading: false,
    searchValues: {},
    data: [],
    total: 0,
    pagination: {
      pageNum: 1,
      pageSize: 10,
    },
  },
};

const channelConfigSlice = createSlice({
  name: 'channelConfig',
  initialState: initialState,
  reducers: {
    request: state => ({ ...state, loading: true }),
    updateSearchValues(state, { payload }) {
      state.searchValues = {
        ...state.searchValues,
        ...payload,
      };
    },
    resetSearchValuesAndPagination(state) {
      state.searchValues = {};
      state.pagination = { ...initialState.pagination };
    },
    setPagination(state, { payload }) {
      state.pagination = payload;
    },
    setDataAndTotal(state, { payload: { data, total } }) {
      state.data = data;
      state.total = total;
      state.loading = false;
    },
    resetPageNum({ pagination }) {
      pagination.pageNum = 1;
    },
    setSelectedRowKeys(state, { payload }) {
      state.selectedRowKeys = payload;
    },
    resetSelectedRowKeys(state) {
      state.selectedRowKeys = [];
    },

    requestDevice: state => ({ ...state, loading: true }),
    updateDeviceSearchValues(state, { payload }) {
      state.searchValues = {
        ...state.searchValues,
        ...payload,
      };
    },
    resetDeviceSearchValuesAndPagination(state) {
      state.searchValues = {};
      state.pagination = { ...initialState.pagination };
    },
    setDevicePagination(state, { payload }) {
      state.pagination = payload;
    },
    setDeviceDataAndTotal(state, { payload: { data, total } }) {
      state.data = data;
      state.total = total;
      state.loading = false;
    },
    resetDevicePageNum({ pagination }) {
      pagination.pageNum = 1;
    },
  },
});

export const CHANNEL_CONFIG_NAME = channelConfigSlice.name;
export const channelConfigActions = channelConfigSlice.actions;
export default channelConfigSlice.reducer;
