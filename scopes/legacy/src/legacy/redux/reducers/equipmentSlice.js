import { createSlice } from '@reduxjs/toolkit';

const INITIAL_STATE = {
  // 请求加载中
  loading: true,
  // 设备列表
  equipmentPage: {
    list: [],
    total: '',
  },
  pagination: {
    pageNum: 1,
    pageSize: 10,
  },
  // 编辑包间信息
  editEquipmentMessList: null,

  // 导入设备列表
  importEquipmentList: {},
  importLoading: false,
  threeEquipmentType: [],
  metaCategoryJson: {},
  areaIdcBlockRoomList: [],
  deviceSNData: [],
  searchValues: {},

  // 机房下的设备
  idcDevices: {},
  // URL初始化时，不在搜索条件中的其他条件
  hiddenSearchValues: {},
};
const equipmentSlice = createSlice({
  name: 'equipmentManage',
  initialState: INITIAL_STATE,
  reducers: {
    request: state => ({ ...state, loading: true }),
    failure: state => ({
      ...state,
      loading: false,
      importLoading: false,
    }),
    featchEquipmentPageSuccess: (state, { payload: { total, data } }) => ({
      ...state,
      loading: false,
      equipmentPage: {
        list: data,
        total: total,
      },
    }),

    // 编辑包间信息
    editEquipmentMess: (state, { payload }) => ({
      ...state,
      editEquipmentMessList: payload,
    }),

    //导入
    importEquipmentSuccess: (state, { payload }) => {
      return {
        ...state,
        importEquipmentList: payload,
      };
    },
    importLoading: state => ({
      ...state,
      importLoading: true,
    }),

    // 设备三级分类
    threeEquipmentType: (state, { payload: { cascaderList, metaCategoryJson } }) => ({
      ...state,
      threeEquipmentType: cascaderList,
      metaCategoryJson: metaCategoryJson,
    }),
    saveAreaIdcBlockRoom: (state, { payload }) => ({
      ...state,
      areaIdcBlockRoomList: payload,
    }),
    searchDeviceSNWithNameSuccess: (state, { payload }) => ({
      ...state,
      deviceSNData: payload,
    }),
    updateSearchValues(state, { payload }) {
      state.searchValues = {
        ...state.searchValues,
        ...payload,
      };
    },
    resetSearchValues(state, { payload }) {
      state.searchValues = {};
    },
    initializeSearchValuesAndConditions: (
      state,
      { payload: { searchValues, hiddenSearchValues, pagination } }
    ) => {
      state.searchValues = searchValues;
      state.hiddenSearchValues = hiddenSearchValues;
      state.pagination = pagination ? pagination : INITIAL_STATE.pagination;
    },
    setEquipmentPagination(state, { payload }) {
      state.pagination = payload;
    },
    resetEquipmentPagination(state) {
      state.pagination = INITIAL_STATE.pagination;
    },
    setIdcDevices: (state, { payload }) => {
      state.idcDevices = {
        ...state.idcDevices,
        ...payload,
      };
    },
  },
});

export const EQUIPMENT_MANAGE_SLICE_NAME = equipmentSlice.name;
export const equipmentActions = equipmentSlice.actions;
export default equipmentSlice.reducer;
