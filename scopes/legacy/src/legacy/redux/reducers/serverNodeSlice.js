import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  searchValues: {},
  pagination: {
    pageNum: 1,
    pageSize: 10,
  },
  data: [],
  total: 0,
  loading: false,
};

const serverNodeSlice = createSlice({
  name: 'serverNode',
  initialState: initialState,
  reducers: {
    request: state => ({ ...state, loading: true }),
    updateSearchValues(state, { payload }) {
      state.searchValues = {
        ...state.searchValues,
        ...payload,
      };
    },
    resetSearchValuesAndPagination(state) {
      state.searchValues = {};
      state.pagination = { ...initialState.pagination };
    },
    setPagination(state, { payload }) {
      state.pagination = payload;
    },
    setDataAndTotal(state, { payload: { data, total } }) {
      state.data = data;
      state.total = total;
      state.loading = false;
    },
    resetPageNum({ pagination }) {
      pagination.pageNum = 1;
    },
  },
});

export const SERVER_NODE_NAME = serverNodeSlice.name;
export const serverNodeActions = serverNodeSlice.actions;
export default serverNodeSlice.reducer;
