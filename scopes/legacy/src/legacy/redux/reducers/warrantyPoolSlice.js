import { createSlice } from '@reduxjs/toolkit';

const INITIAL_STATE = {
  warrantyPoolList: {
    list: [],
    total: 0,
  },
  loading: false,
  pagination: {
    pageNum: 1,
    pageSize: 10,
  },
  searchValues: {},
};

const warrantyPoolSlice = createSlice({
  name: 'warrantyPool',
  initialState: INITIAL_STATE,
  reducers: {
    request: state => ({
      ...state,
      loading: true,
    }),
    failure: state => ({
      ...state,
      loading: false,
    }),
    featchWarrantyPoolSuccess: (state, { payload: { total, data } }) => ({
      ...state,
      loading: false,
      warrantyPoolList: {
        list: data,
        total,
      },
    }),
    setWarrantyPoolPagination(state, { payload }) {
      state.pagination = payload;
    },
    updateSearchValues(state, { payload }) {
      state.searchValues = {
        ...state.searchValues,
        ...payload,
      };
    },
    resetSearchValues(state) {
      state.searchValues = {};
    },
  },
});
export const WARRANTY_POOL_SLICE_NAME = warrantyPoolSlice.name;
export const warrantyPoolActions = warrantyPoolSlice.actions;
export default warrantyPoolSlice.reducer;
