import { createSlice } from '@reduxjs/toolkit';

import { ConfigRangeType } from '@manyun/ticket.model.task';

const initialState = {
  loading: false,
  pagination: {
    pageNum: 1,
    pageSize: 10,
  },
  data: [],
  total: 0,
  searchValues: {
    //盘点项名
    configName: {
      value: null,
    },

    // 设备类型
    deviceType: {
      value: null,
    },
    // 盘点类型
    inventoryType: {
      value: null,
    },
    // 更新时间
    updateTimeRange: {
      value: null,
    },

    // 更新人id
    modifierId: {
      value: undefined,
    },

    blockGuids: {
      value: [],
    },
  },
  // 添加配置信息
  create: {
    //盘点项名
    configName: {
      value: null,
    },
    //适用范围
    configType: {
      value: ConfigRangeType.Block,
    },
    // 设备类型
    deviceTypes: {
      value: null,
    },
    // 盘点类型
    inventoryType: {
      value: null,
    },
    // 盘点子类型
    inventorySubType: {
      value: 'IMPLICIT',
    },
  },
  // 编辑配置信息
  edit: {
    configName: {
      value: null,
    },
    deviceTypes: {
      value: null,
    },
    inventoryType: {
      value: null,
    },
    inventorySubType: {
      value: 'IMPLICIT',
    },
  },
  // 巡检配置详情
  detail: {},
};

const inventoryConfigSlice = createSlice({
  name: 'inventoryConfig',
  initialState: initialState,
  reducers: {
    failure(state) {
      state.loading = false;
    },
    updateTableLoading(state, { payload }) {
      state.loading = payload;
    },
    resetPageNum(state) {
      state.pagination.pageNum = 1;
    },
    setDataAndTotal(state, { payload: { data, total } }) {
      state.data = data;
      state.total = total;
      state.loading = false;
    },
    updateSearchValues(state, { payload }) {
      state.searchValues = { ...state.searchValues, ...payload };
    },
    resetSearchValuesAndPagination(state) {
      state.searchValues = initialState.searchValues;
      state.pagination = initialState.pagination;
    },
    setPagination(state, { payload }) {
      state.pagination = payload;
    },
    setConfigInfos(state, { payload }) {
      state.detail = payload;
    },
    upDateCreateOptionValues(state, { payload }) {
      state.create = { ...state.create, ...payload };
    },
    upDateEditOptionValues(state, { payload }) {
      state.edit = { ...state.edit, ...payload };
    },
    resetInventoryPageData(state) {
      state.pagination = initialState.pagination;
      state.data = initialState.data;
      state.total = initialState.total;
      state.loading = initialState.loading;
      state.searchValues = initialState.searchValues;
    },
  },
});

export const INVENTORY_CONFIG_SLICE_NAME = inventoryConfigSlice.name;
export const inventoryConfigActions = inventoryConfigSlice.actions;
export default inventoryConfigSlice.reducer;
