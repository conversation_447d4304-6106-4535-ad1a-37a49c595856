import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  loading: false,
  templateGroupViewData: {
    list: [],
    total: '',
  },
  searchTemplateGroupPagesCondition: {
    pageNum: 1,
    pageSize: 10,
  },
  selectKeys: [],
  templateTree: [],
  templateTreeChildren: [],
  electricTable: [],
  editAreaVisible: false,
  connectTemplateVisible: false,
  electricTemplates: [],
  sureElectricTemplates: [],
  viewAreaVisible: false,
  templateGroupAreaTable: [],
  sureTemplateGroupAreaTable: [],
  sureElectricTable: [],
  areaConfgTable: {
    list: [],
    total: '',
  },
  searchAreaConfigCondition: {},
  createTemplateDataName: { value: '' },
  createTemplateDataAvailable: { value: '' },
  areaIdecRoomTreeVisible: false,
  areaIdecRoomTreeTemplateVisible: false,
  addTemplateToAreaVisible: false,
  allTemplateGroupList: [],
  selectTemplateGroup: [],
  saveallTemplateGroupList: [],
  choiceIDCBlockTable: [],
  choiceIDCORBlock: {},
  sureChoiceIDCBlockTable: [],
  connectTemplates: [],
  checkedIdcAndBlock: [],
  tempSaveIacBlockData: [],
  templateEleSure: [],
  basicInfo: {},
  IdcBlockCascader: null,
  templateMessByMetaCode: null,
  createTableloading: false,
  searchValues: {},
};

const templateGroupViewSlice = createSlice({
  name: 'templateGroupView',
  initialState: initialState,
  reducers: {
    request: state => ({ ...state, loading: true }),
    changeCreateTableloading: state => ({ ...state, createTableloading: true }),
    failure: state => ({
      ...state,
      loading: false,
      createTableloading: false,
    }),
    searchTemplateGroupPagesSuccess: (state, { payload: { data, total } }) => ({
      ...state,
      loading: false,
      templateGroupViewData: {
        list: data,
        total: total,
      },
    }),
    searchTemplateGroupPagesCondition: (state, { payload }) => ({
      ...state,
      loading: false,
      searchTemplateGroupPagesCondition: { ...payload },
    }),
    saveSelectKeys: (state, { payload }) => ({
      ...state,
      selectKeys: payload,
    }),
    fetchTemplateTreeSuccess: (state, { payload }) => ({
      ...state,
      templateTree: payload,
    }),
    fetchTemplateTreeChildrenSuccess: (state, { payload }) => ({
      ...state,
      templateTreeChildren: payload,
    }),
    selectElectricTableSuccess: (state, { payload }) => ({
      ...state,
      electricTable: payload,
    }),

    sureElectricTableSuccess: (state, { payload }) => ({
      ...state,
      sureElectricTable: payload,
      electricTable: payload,
    }),

    // 编辑区域弹框状态
    editAreaVisible: state => ({
      ...state,
      editAreaVisible: !state.editAreaVisible,
    }),
    // 关联区域
    connectTemplateVisible: state => ({
      ...state,
      connectTemplateVisible: !state.connectTemplateVisible,
    }),
    changeaddTemplateToAreaVisible: state => ({
      ...state,
      addTemplateToAreaVisible: !state.addTemplateToAreaVisible,
    }),
    changeAreaIdecRoomTreeTemplateVisible: state => ({
      ...state,
      areaIdecRoomTreeTemplateVisible: !state.areaIdecRoomTreeTemplateVisible,
    }),
    // 查看生效域
    searchTemplateGroupAreaSuccess: (state, { payload }) => ({
      ...state,
      templateGroupAreaTable: payload,
      sureTemplateGroupAreaTable: payload,
      loading: false,
    }),

    fetchAreaConfigSuccess: (state, { payload: { data, total } }) => ({
      ...state,
      loading: false,
      areaConfgTable: {
        list: data,
        total: total,
      },
    }),

    saveSearchAreaConfigConditionSuccess: (state, { payload }) => ({
      ...state,
      searchAreaConfigCondition: payload,
    }),

    saveCreateTempteInputSuccess: (state, { payload }) => ({
      ...state,
      createTemplateDataName: payload,
    }),
    saveCreateTempteRadioSuccess: (state, { payload }) => ({
      ...state,
      createTemplateDataAvailable: payload,
    }),

    areaIdecRoomTreeVisible: state => ({
      ...state,
      areaIdecRoomTreeVisible: !state.areaIdecRoomTreeVisible,
    }),
    fetchAllTemplateGroupSuccess: (state, { payload }) => ({
      ...state,
      allTemplateGroupList: payload,
      loading: false,
    }),
    savefetchAllTemplateGroupSuccess: (state, { payload }) => ({
      ...state,
      saveallTemplateGroupList: payload,
      loading: false,
    }),
    SaveSelectTemplateGroupSuccess: (state, { payload }) => ({
      ...state,
      selectTemplateGroup: payload,
    }),

    choiceEreaBlockSuccess: (state, { payload }) => ({
      ...state,
      choiceIDCBlockTable: payload,
    }),

    sureChoiceEreaBlockSuccess: (state, { payload }) => ({
      ...state,
      sureChoiceIDCBlockTable: payload,
    }),
    fetchTemplateEleAndHeartSuccess: (state, { payload: { electricTemplates } }) => ({
      ...state,
      loading: false,
      electricTemplates: electricTemplates,
      sureElectricTemplates: electricTemplates,
    }),
    seacrchViewElecticData: (state, { payload }) => {
      state.electricTemplates = payload;
    },

    fetchTemplateEleSure: (state, { payload }) => ({
      ...state,
      templateEleSure: payload,
    }),

    filterTemplateElectricSuccess: (state, { payload }) => ({
      ...state,
      electricTemplates: payload,
    }),
    // 查看区域弹框状态
    viewAreaVisible: state => ({
      ...state,
      viewAreaVisible: !state.viewAreaVisible,
    }),

    getConnectTemplatesByTemplateGroupIdSuccess: (state, { payload: { electric } }) => ({
      ...state,
      sureElectricTable: electric,
      electricTable: electric,
    }),
    getAreaConfigByIdSuccess: (state, { payload }) => ({
      ...state,
      choiceIDCBlockTable: payload,
    }),
    saveCheckedIdcAndRoom: (state, { payload }) => ({
      ...state,
      checkedIdcAndBlock: payload,
    }),
    tempSaveIacBlock: (state, { payload }) => ({
      ...state,
      tempSaveIacBlockData: payload,
    }),

    saveBasicInfo: (state, { payload }) => ({
      ...state,
      basicInfo: payload,
    }),

    saveIdcBlockCascader: (state, { payload }) => ({
      ...state,
      IdcBlockCascader: payload,
    }),
    fetchTemplateMessByMetaCodeSuccess: (state, { payload }) => ({
      ...state,
      templateMessByMetaCode: payload,
    }),
    resetItems: state => {
      state.electricTable = initialState.electricTable;
      state.sureElectricTable = initialState.sureElectricTable;
      // list 表格选择
      state.selectKeys = initialState.selectKeys;
      state.templateGroupViewData = initialState.templateGroupViewData;
      state.createTableloading = initialState.createTableloading;
      state.choiceIDCBlockTable = [];
      state.createTemplateDataAvailable = initialState.createTemplateDataAvailable;
      state.createTemplateDataName = initialState.createTemplateDataName;
      state.sureChoiceIDCBlockTable = initialState.sureChoiceIDCBlockTable;
      state.areaIdecRoomTreeVisible = initialState.areaIdecRoomTreeVisible;
      state.editAreaVisible = initialState.editAreaVisible;
    },
    getFormChangedData: (state, { payload }) => {
      state.createTemplateDataAvailable = payload.available
        ? payload.available
        : state.createTemplateDataAvailable;
      state.createTemplateDataName = payload.name ? payload.name : state.createTemplateDataName;
    },
    changeTemplateGroupArea: (state, { payload }) => ({
      ...state,
      templateGroupAreaTable: payload,
      loading: false,
    }),
    updateSearchValues(state, { payload }) {
      state.searchValues = {
        ...state.searchValues,
        ...payload,
      };
    },
    resetSearchValues(state) {
      state.searchValues = {};
    },
  },
});

export const TEMPLATE_GROUP_VIEW_SLICE_NAME = templateGroupViewSlice.name;
export const templateGroupViewActions = templateGroupViewSlice.actions;
export default templateGroupViewSlice.reducer;
