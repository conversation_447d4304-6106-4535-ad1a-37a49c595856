import { createSlice } from '@reduxjs/toolkit';

const INITIAL_STATE = {
  loading: false,
  specDetail: {
    data: [],
    total: '',
  },
  deviceType: '',
  modelGuid: '',
  categoryList: [],
  categoryNum: {},
  selectCategoryPoint: {
    selectTitle: '',
    selectType: '',
    deviceType: '',
  },
};

const specSlice = createSlice({
  name: 'specManage',
  initialState: INITIAL_STATE,
  reducers: {
    request: state => ({ ...state, loading: true }),
    failure: state => ({
      ...state,
      loading: false,
    }),
    featchSpecDetailSuccess: (state, { payload: { total, data } }) => ({
      ...state,
      loading: false,
      specDetail: {
        data: data,
        total: total,
      },
    }),
    saveType: (state, { payload }) => ({
      ...state,
      deviceType: payload.code,
    }),
    fetcgMetaCategory: (state, { payload }) => ({
      ...state,
      categoryList: payload,
    }),
    fetcgMetaCategoryNumSuccess: (state, { payload }) => ({
      ...state,
      categoryNum: payload,
    }),
    selectCategoryPoint: (state, { payload }) => ({
      ...state,
      selectCategoryPoint: {
        selectTitle: payload.selectTitle,
        selectType: payload.selectType,
        deviceType: payload.deviceType,
      },
    }),
  },
});

export const SPEC_MANAGE_SLICE_NAME = specSlice.name;
export const specActions = specSlice.actions;
export default specSlice.reducer;
