import { createSlice } from '@reduxjs/toolkit';

const INITIAL_STATE = {
  ticketList: {
    fetching: false,
    loading: false,
    data: [],
    total: 0,
  },
  pagination: {
    pageNum: 1,
    pageSize: 10,
  },
  taskStatus: '3',
  initiator: '0',
  searchValues: {},
  takeOverLoading: false,
  activedKey: '3_0',
};

const mineTicketSlice = createSlice({
  name: 'mineticket',
  initialState: INITIAL_STATE,
  reducers: {
    fetchTicketListStart: state => {
      state.ticketList.fetching = true;
    },
    fetchTicketListError: state => {
      state.ticketList = {
        fetching: false,
        data: [],
        total: 0,
      };
    },
    takeOverLoading: state => {
      state.takeOverLoading = !state.takeOverLoading;
    },
    failure: state => {
      state.ticketList.loading = false;
      state.takeOverLoading = false;
    },
    setPagination(state, { payload }) {
      state.ticketList.pagination = payload;
    },
    resetPageNum(state) {
      state.ticketList.pagination = { pageNum: 1, pageSize: 10 };
    },
    changeStatus: (state, { payload }) => {
      state.taskStatus = payload;
    },
    changeInitiator: (state, { payload }) => {
      state.initiator = payload;
    },
    setActivedKey: (state, { payload }) => {
      state.activedKey = payload;
    },
    setTicketList: (state, { payload }) => {
      state.ticketList = {
        ...payload,
        fetching: false,
      };
    },
    updateSearchValues(state, { payload }) {
      state.searchValues = {
        ...state.searchValues,
        ...payload,
      };
    },
    updatePagination(state, { payload }) {
      state.pagination = payload;
    },
    resetPagination(state) {
      state.pagination = INITIAL_STATE.pagination;
    },
    resetSearchValues(state, { payload }) {
      state.searchValues = {};
    },
  },
});

export const MINE_TICKET_SLICE_NAME = mineTicketSlice.name;
export const mineTicketActions = mineTicketSlice.actions;
export default mineTicketSlice.reducer;
