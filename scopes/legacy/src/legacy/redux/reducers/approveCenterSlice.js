import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  searchValues: {},
  type: 'WAIT',
  pagination: {
    pageNum: 1,
    pageSize: 10,
  },
  data: [],
  total: 0,
  loading: false,
};

const approveCenterSlice = createSlice({
  name: 'approveCenter',
  initialState: initialState,
  reducers: {
    request: state => ({ ...state, loading: true }),
    failure: state => ({
      ...state,
      loading: false,
    }),
    updateSearchValues(state, { payload }) {
      state.searchValues = {
        ...state.searchValues,
        ...payload,
      };
    },
    resetSearchValuesAndPagination(state) {
      state.searchValues = {};
      state.pagination = { ...initialState.pagination };
    },
    setSearchValuesAndPagination(state, { payload }) {
      //debugger;
      state.searchValues = {
        ...state.searchValues,
        ...payload,
      };
      state.pagination = { ...initialState.pagination };
    },
    setPagination(state, { payload }) {
      state.pagination = payload;
    },
    setDataAndTotal(state, { payload: { data, total } }) {
      state.data = data;
      state.total = total;
      state.loading = false;
    },
    resetPageNum({ pagination }) {
      pagination.pageNum = 1;
    },
    setType(state, { payload }) {
      state.type = payload;
    },
  },
});

export const APPROVE_CENTER_SLICE_NAME = approveCenterSlice.name;
export const approveCenterActions = approveCenterSlice.actions;
export default approveCenterSlice.reducer;
