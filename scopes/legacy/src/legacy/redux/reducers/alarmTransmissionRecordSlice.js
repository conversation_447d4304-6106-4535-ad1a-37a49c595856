import { createSlice } from '@reduxjs/toolkit';
import moment from 'moment';
import shortid from 'shortid';

const INITIAL_STATE = {
  alarmTransmissionRecordOrderList: {
    list: [],
    total: 0,
  },
  loading: false,
  searchValues: {
    alarmTime: {
      name: 'alarmTime',
      value: getTime(),
    },
  },
  pagination: {
    pageNum: 1,
    pageSize: 10,
  },
};

const alarmTransmissionRecordSlice = createSlice({
  name: 'alarmTransmissionRecord',
  initialState: INITIAL_STATE,
  reducers: {
    request: state => ({
      ...state,
      loading: true,
    }),
    failure: state => ({
      ...state,
      loading: false,
    }),
    featchAlarmTransmissionRecordOrderSuccess: (state, { payload: { total, data } }) => ({
      ...state,
      loading: false,
      alarmTransmissionRecordOrderList: {
        list: data.map(record => ({ ...record, id: shortid() })),
        total,
      },
    }),
    setAlarmTransmissionRecordPagination(state, { payload }) {
      state.pagination = payload;
    },
    updateSearchValues(state, { payload }) {
      state.searchValues = {
        ...state.searchValues,
        ...payload,
      };
    },
    resetValues(state) {
      state.searchValues = INITIAL_STATE.searchValues;
      state.pagination = INITIAL_STATE.pagination;
    },
  },
});

export const ALARM_TRANSMISSION_RECORD_SLICE_NAME = alarmTransmissionRecordSlice.name;
export const alarmTransmissionRecordActions = alarmTransmissionRecordSlice.actions;
export default alarmTransmissionRecordSlice.reducer;

function getTime() {
  const now = new Date(new Date().toLocaleDateString()).getTime();
  const startTime = moment(new Date(now));
  const endTime = moment(new Date(now + 24 * 60 * 60 * 1000 - 1));
  const value = [startTime, endTime];
  return value;
}
