import { createSlice } from '@reduxjs/toolkit';
import cloneDeep from 'lodash/cloneDeep';
import uniq from 'lodash/uniq';
import shortid from 'shortid';

import {
  POINT_DATA_TYPE_CODE_MAP,
  POINT_TYPE_CODE_MAP,
} from '@manyun/dc-brain.legacy.constants/point';
import {
  EXPR_DELIMITER_ITEM,
  EXPR_ITEM_TYPE,
  TargetPointType,
} from '@manyun/dc-brain.legacy.pages/merged-processed-point/constants';
import { flattenTreeData } from '@manyun/dc-brain.legacy.utils';

const initialState = {
  // 表达式编辑器-当前激活的可以 Drop 的区域
  exprActiveDroppableId: undefined,

  expressionData: [],

  testExprResult: {},

  // 挂载对象设备类型
  parentDeviceType: null,

  // 测点类型
  pointTypeSelected: {
    pointType: {
      value: 'MERGED',
    },
  },

  // 聚合测点信息
  margedPointMaps: {},
  margedPointIds: [],
  //加工测点选择设备时
  processedDeviceInfo: {},
  //加工测点selected模版信息
  processedSelectedPoint: {},
  // 加工测点表单信息
  processedPointList: {
    name: {
      value: null,
    },
    pointType: {
      value: null,
    },
    dataType: {
      value: undefined,
    },
    unit: {
      value: null,
    },
    precision: {
      value: undefined,
    },
    priority: {
      value: 'alwaysTrue',
    },
    exprData: {
      value: null,
    },
    statusList: {
      value: [],
    },
    spaceGuid: {
      value: undefined,
    },
    targetPointType: {
      value: TargetPointType.DEVICE,
    },
    validLimits: {
      value: {
        ge: null,
        le: null,
      },
    },
    description: {
      value: null,
    },
    formula: {
      value: null,
    },
    selectedExprDataType: {
      value: 'editor',
    },
  },

  // 测点信息
  pointInfoMap: {},

  //自定义测点所属guid
  customSpaceGuid: '',
  //表达式名称
  formulaName: '',
  // 禁用叶子节点
  disabledTreeLeaf: [],
  disabledDimensionEnums: [],
  disabledLogicEnums: [],
  selectedPointKey: null,
  expandedKeys: [],
  selectedKeys: null,

  // 是否被聚合
  isMerged: false,
  mergedPointConfig: {},
  isValidate: false,
  mode: 'new',
};

const mergedProcessesdPointSlice = createSlice({
  name: 'mergedProcessesdPoint',
  initialState: initialState,
  reducers: {
    updateProcessedPointFormValues(state, { payload }) {
      if (payload.pointType && payload.pointType.value === POINT_TYPE_CODE_MAP.CAL_SPACE) {
        state.processedPointList = {
          ...state.processedPointList,
          ...payload,
          dataType: {
            value: POINT_DATA_TYPE_CODE_MAP.AI,
          },
          statusList: {
            value: [],
          },
        };
      } else {
        state.processedPointList = {
          ...state.processedPointList,
          ...payload,
        };
      }

      let newDisabledleaf = [];
      if (payload.dataType && payload.dataType.value === POINT_DATA_TYPE_CODE_MAP.DI) {
        const keys = Object.keys(state.pointInfoMap);
        if (keys.length) {
          keys.forEach(key => {
            if (state.pointInfoMap[key].dataType !== POINT_DATA_TYPE_CODE_MAP.DI) {
              const eventKey = `${state.pointInfoMap[key].metaType}${state.pointInfoMap[key].metaCode}`;
              newDisabledleaf = [...newDisabledleaf, eventKey];
            }
          });
        }
      }
      state.disabledTreeLeaf = [...state.disabledTreeLeaf, ...newDisabledleaf];
    },
    updateProcessedSelectedPoint(state, { payload }) {
      state.processedSelectedPoint = payload.processedSelectedPoint;
    },
    updateProcessedDeviceInfo(state, { payload }) {
      state.processedDeviceInfo = payload.processedDeviceInfo;
    },
    updateCustomSpaceGuid(state, { payload }) {
      state.customSpaceGuid = payload.customSpaceGuid;
    },
    updateFormulaName(state, { payload }) {
      state.formulaName = payload.formulaName;
    },
    addMergedPointForm(state) {
      const newId = shortid.generate();
      const itemModule = {
        name: { value: '' },
        dimensionEnums: { value: [] },
        logicEnums: { value: [] },
        statusMap: {},
        pointKey: '',
        disabledDimensionEnums: [],
        disabledLogicEnums: [],
        aggStatusMap: {},
        cannotSelectDimensionEnums: [],
      };
      state.margedPointIds = [...state.margedPointIds, newId];
      state.margedPointMaps = {
        ...state.margedPointMaps,
        [newId]: { ...itemModule },
      };
    },

    deleteMergedPointInForm(state, { payload: { id, type } }) {
      if (type === 'all') {
        const temp = cloneDeep(state.margedPointMaps);
        delete temp[id];
        state.disabledTreeLeaf = state.disabledTreeLeaf.filter(
          item => item !== state.margedPointMaps[id].pointKey
        );
        state.margedPointIds = state.margedPointIds.filter(item => item !== id);
        state.margedPointMaps = temp;
      } else {
        const itemModule = {
          name: { value: '' },
          dimensionEnums: { value: [] },
          logicEnums: { value: [] },
          statusMap: {},
          pointKey: '',
          disabledDimensionEnums: [],
          disabledLogicEnums: [],
          aggStatusMap: {},
          cannotSelectDimensionEnums: [],
        };
        state.disabledTreeLeaf = state.disabledTreeLeaf.filter(
          item => item !== state.margedPointMaps[id].pointKey
        );
        state.margedPointMaps = {
          ...state.margedPointMaps,
          [id]: { ...itemModule },
        };
      }
    },

    updateMergedPointSingleFormValues(state, { payload: { id, changedFields } }) {
      // 聚合维度不可删除被聚合项
      if (changedFields.dimensionEnums) {
        state.margedPointMaps = {
          ...state.margedPointMaps,
          [id]: {
            ...state.margedPointMaps[id],
            dimensionEnums: {
              ...changedFields.dimensionEnums,
              value: changedFields.dimensionEnums.value
                ? uniq([
                    ...changedFields.dimensionEnums.value,
                    ...state.margedPointMaps[id].disabledDimensionEnums,
                  ])
                : state.margedPointMaps[id].disabledDimensionEnums,
            },
          },
        };
      } else {
        state.margedPointMaps = {
          ...state.margedPointMaps,
          [id]: {
            ...state.margedPointMaps[id],
            ...changedFields,
          },
        };
      }
    },

    dropUpdateMergedPointSingleFormValues(state, { payload: { id, data } }) {
      state.margedPointMaps = {
        ...state.margedPointMaps,
        [id]: {
          ...state.margedPointMaps[id],
          ...data,
        },
      };
      state.disabledTreeLeaf = state.margedPointIds.map(id => state.margedPointMaps[id].pointKey);
    },

    updataMergePointFormStatusMap(state, { payload }) {
      state.margedPointMaps = payload;
    },

    updateExpressionData(state, { payload }) {
      state.expressionData = payload;
      state.processedPointList.exprData = {
        ...state.processedPointList.exprData,
        dirty: true,
        touched: true,
        errors: undefined,
      };
    },
    updatePointTypeSelectedFormValues(state, { payload }) {
      state.pointTypeSelected = payload;
      state.disabledTreeLeaf = [];
    },
    savePointInfoMap(state, { payload }) {
      state.pointInfoMap = {
        ...state.pointInfoMap,
        ...payload,
      };
    },
    saveSelectedPointKey(state, { payload: { code, parentKeys, selectedKeys } }) {
      state.selectedPointKey = code;
      state.expandedKeys = parentKeys;
      state.selectedKeys = selectedKeys;
      state.disabledTreeLeaf = selectedKeys;
    },

    saveDisabledTreeLeaf(state, { payload }) {
      state.disabledTreeLeaf = [...state.disabledTreeLeaf, ...payload];
    },
    addPointInStatus(state, { payload }) {
      const result = cloneDeep(state.processedPointList.statusList.value);
      const tmp = result[payload].label;
      let statusValue = '';
      result.forEach(item => {
        if (item.label === tmp && item.status) {
          statusValue = item.status;
        }
      });
      const added = {
        markedID: shortid.generate(),
        label: state.processedPointList.statusList.value[payload].label,
        status: statusValue,
        name: '',
        validLimits: [],
      };
      result.splice(payload + 1, 0, added);
      state.processedPointList.statusList.value = result;
    },
    updateStatusList(state, { payload }) {
      state.processedPointList.statusList.value = payload;
    },
    updatePointType(state, { payload }) {
      state.processedPointList.pointType.value = payload;
    },
    deleteSameLabel(state, { payload }) {
      const statusListClone = cloneDeep(state.processedPointList.statusList.value);
      const newData = statusListClone
        .map(item => {
          if (item.label === payload) {
            return null;
          }
          return item;
        })
        .filter(Boolean);
      state.processedPointList.statusList.value = newData;
    },
    deletePressedStatusListSinglePoint(state, { payload }) {
      const statusListClone = cloneDeep(state.processedPointList.statusList.value);
      statusListClone[payload] = null;
      state.processedPointList.statusList.value = statusListClone.filter(Boolean);
    },
    addStausInStatusList(state) {
      const statusListClone = cloneDeep(state.processedPointList.statusList.value);
      if (!statusListClone.length) {
        const tmp = {
          markedID: shortid.generate(),
          label: 0,
          status: '',
          name: '',
          validLimits: [],
          dataType: '',
        };
        state.processedPointList.statusList.value[0] = tmp;
      } else {
        const idx = statusListClone.length - 1;
        const tmp = {
          markedID: shortid.generate(),
          label: Number(statusListClone[idx].label) + 1,
          status: '',
          name: '',
          validLimits: [],
        };
        const lastIdx = idx + 1;
        state.processedPointList.statusList.value[lastIdx] = tmp;
      }
    },
    onChangeStatusInput(state, { payload: { status, index } }) {
      const statusListClone = cloneDeep(state.processedPointList.statusList.value);
      const newData = statusListClone.map(point => {
        if (point.label === statusListClone[index].label) {
          return {
            ...point,
            status: status,
          };
        } else {
          return point;
        }
      });
      state.processedPointList.statusList.value = newData;
    },
    onChangeDataTypeSelect(state, { payload: { dataType, index } }) {
      state.processedPointList.statusList.value[index].dataType = dataType;
    },
    changeIsMerged(state, { payload }) {
      state.isMerged = payload;
    },
    saveMergedPointConfig(state, { payload }) {
      state.mergedPointConfig = payload;
    },
    mergedAndUpdateMergedPoint(state, { payload }) {
      state.margedPointMaps = payload;
      state.disabledTreeLeaf = state.margedPointIds.map(id => state.margedPointMaps[id].pointKey);
    },
    changePointTypeSelected(state, { payload }) {
      state.pointTypeSelected.pointType.value = 'MACHINE';
      state.processedPointList = { ...state.processedPointList, ...payload };
    },

    changeIsValidate(state) {
      state.isValidate = true;
    },
    resetItems(state) {
      state.pointTypeSelected = initialState.pointTypeSelected;
      state.margedPointMaps = initialState.margedPointMaps;
      state.margedPointIds = initialState.margedPointIds;
      state.processedPointList = initialState.processedPointList;
      state.disabledTreeLeaf = initialState.disabledTreeLeaf;
      state.disabledDimensionEnums = initialState.disabledDimensionEnums;
      state.disabledLogicEnums = initialState.disabledLogicEnums;
      // state.selectedPointKey = initialState.selectedPointKey;
      state.expressionData = initialState.expressionData;
      state.exprActiveDroppableId = initialState.exprActiveDroppableId;
      state.testExprResult = initialState.testExprResult;
      state.parentDeviceType = initialState.parentDeviceType;
    },

    resetCustomPointExpr(state) {
      state.expressionData = initialState.expressionData;
      state.exprActiveDroppableId = initialState.exprActiveDroppableId;
      state.testExprResult = initialState.testExprResult;
      state.parentDeviceType = initialState.parentDeviceType;
      state.processedPointList.statusList.value = [];
    },
    setExprActiveDroppableId(state, { payload }) {
      state.exprActiveDroppableId = payload;
    },

    exprInsertIntoRoot(state, { payload: { index, item, deviceType } }) {
      if (state.parentDeviceType === null && deviceType) {
        state.parentDeviceType = deviceType;
      }
      if (item.type === EXPR_ITEM_TYPE.NUMBER) {
        item.name = item.value;
      }
      state.expressionData.splice(index, 0, item);
      if (state.processedPointList.exprData) {
        state.processedPointList.exprData.errors = undefined;
        state.processedPointList.exprData.dirty = true;
      }
    },

    exprInsertIntoNested(state, { payload: { parentItemId, index, item, deviceType } }) {
      if (state.parentDeviceType === null && deviceType) {
        state.parentDeviceType = deviceType;
      }

      if (item.type === EXPR_ITEM_TYPE.NUMBER) {
        item.name = item.value;
      }

      const recurse = data => {
        data.forEach(dataItem => {
          if (dataItem.id === parentItemId) {
            if (Array.isArray(dataItem.children)) {
              dataItem.children.splice(index, 0, item);
              if (dataItem.type === EXPR_ITEM_TYPE.STATISTICAL_FUNCTION) {
                dataItem.children = withDelimiters(dataItem.children);
              }
            } else {
              dataItem.children = [item];
            }
            return;
          }
          if (Array.isArray(dataItem.children) && dataItem.children.length) {
            recurse(dataItem.children);
          }
        });
      };

      recurse(state.expressionData);

      if (state.processedPointList.exprData) {
        state.processedPointList.exprData.errors = undefined;
        state.processedPointList.exprData.dirty = true;
      }
    },

    exprSortInRoot(state, { payload: { fromIdx, toIdx } }) {
      const item = state.expressionData[fromIdx];
      state.expressionData.splice(fromIdx, 1);
      state.expressionData.splice(toIdx, 0, item);
    },

    exprSortInNest(state, { payload: { parentItemId, fromIdx, toIdx } }) {
      const recurse = data => {
        data.forEach(dataItem => {
          if (dataItem.id === parentItemId) {
            const item = dataItem.children[fromIdx];
            dataItem.children.splice(fromIdx, 1);
            dataItem.children.splice(toIdx, 0, item);
            if (dataItem.type === EXPR_ITEM_TYPE.STATISTICAL_FUNCTION) {
              dataItem.children = withDelimiters(dataItem.children);
            }
            return;
          }
          if (Array.isArray(dataItem.children) && dataItem.children.length) {
            recurse(dataItem.children);
          }
        });
      };

      recurse(state.expressionData);
    },

    exprNumberChange(state, { payload: { itemId, value } }) {
      const recurse = data => {
        data.forEach(dataItem => {
          if (dataItem.id === itemId) {
            dataItem.name = value;
            dataItem.value = value;
            return;
          }
          if (Array.isArray(dataItem.children) && dataItem.children.length) {
            recurse(dataItem.children);
          }
        });
      };

      recurse(state.expressionData);
    },

    exprRemoveItem(state, { payload: itemId }) {
      const recurse = (data, parent) => {
        let removeAtIdx = -1;
        for (let index = 0; index < data.length; index++) {
          const dataItem = data[index];
          if (dataItem.id === itemId) {
            removeAtIdx = index;
            break;
          }
          if (Array.isArray(dataItem.children) && dataItem.children.length) {
            recurse(dataItem.children, dataItem);
          }
        }
        if (removeAtIdx > -1) {
          data.splice(removeAtIdx, 1);
        }
        if (parent && parent.type === EXPR_ITEM_TYPE.STATISTICAL_FUNCTION) {
          parent.children = withDelimiters(data);
        }
      };

      recurse(state.expressionData);

      // 判断表达式中是否还有测点，如果没有设置parentDeviceType为null
      if (state.mode === 'new') {
        let isHavePoint = false;
        flattenTreeData(state.expressionData).forEach(({ type }) => {
          if (type === 'point') {
            isHavePoint = true;
          }
        });
        if (!isHavePoint) {
          state.parentDeviceType = null;
        }
      }

      if (state.processedPointList.exprData) {
        state.processedPointList.exprData.errors = undefined;
        state.processedPointList.exprData.dirty = true;
      }
    },

    setTestExprResult(state, { payload }) {
      state.testExprResult = payload;
    },

    setParentDeviceType(state, { payload }) {
      state.parentDeviceType = payload;
    },

    updateMode(state, { payload }) {
      state.mode = payload;
    },

    updateSelectedPointKey(state, { payload: { selectedKeys, selectedPointKey } }) {
      state.selectedKeys = selectedKeys;
      state.selectedPointKey = selectedPointKey;
    },

    resetSelectedPointKey(state, { payload }) {
      state.selectedPointKey = payload;
    },

    resetCreateMergedPoints(state) {
      state.margedPointMaps = initialState.margedPointMaps;
      state.margedPointIds = initialState.margedPointIds;
    },
    setPointType(state, { payload }) {
      state.processedPointList.pointType = {
        name: 'pointType',
        value: payload,
      };
    },
    updatexpandedKeys(state, { payload }) {
      state.expandedKeys = payload;
    },
  },
});
export const MERGED_PROCESSES_POINT_SLICE_NAME = mergedProcessesdPointSlice.name;
export const mergedProcessesdPointActions = mergedProcessesdPointSlice.actions;
export default mergedProcessesdPointSlice.reducer;

function withDelimiters(items) {
  const result = [];
  const copy = items.filter(({ type }) => type !== EXPR_ITEM_TYPE.DELIMITER);
  copy.forEach((copiedItem, idx) => {
    if (idx === 0) {
      result.push(copiedItem);
      return;
    }
    result.push({
      id: EXPR_ITEM_TYPE.DELIMITER + '.' + shortid(),
      ...EXPR_DELIMITER_ITEM,
    });
    result.push(copiedItem);
  });

  return result;
}
