import { createSlice } from '@reduxjs/toolkit';

const INITIAL_STATE = {
  warrantyOrderList: {
    list: [],
    total: 0,
  },
  loading: false,
  searchValues: {},
  pagination: {
    pageNum: 1,
    pageSize: 10,
  },
  warrantyDetail: {},
  selectedRows: [],
};

const warrantyOrderSlice = createSlice({
  name: 'warrantyOrder',
  initialState: INITIAL_STATE,
  reducers: {
    request: state => ({
      ...state,
      loading: true,
    }),
    failure: state => ({
      ...state,
      loading: false,
    }),
    featchWarrantyOrderSuccess: (state, { payload: { total, data } }) => ({
      ...state,
      loading: false,
      warrantyOrderList: {
        list: data,
        total,
      },
    }),
    setWarrantyPagination(state, { payload }) {
      state.pagination = payload;
    },
    saveWarrantyDetail: (state, { payload }) => {
      state.warrantyDetail = payload;
    },
    updateSearchValues(state, { payload }) {
      state.searchValues = {
        ...state.searchValues,
        ...payload,
      };
    },
    updateRowSelected(state, { payload }) {
      state.selectedRows = payload;
    },
    resetRowSelected(state) {
      state.selectedRows = INITIAL_STATE.selectedRows;
    },
    resetSearchValues(state) {
      state.searchValues = {};
    },
  },
});
export const WARRANTY_ORDER_SLICE_NAME = warrantyOrderSlice.name;
export const warrantyOrderActions = warrantyOrderSlice.actions;
export default warrantyOrderSlice.reducer;
