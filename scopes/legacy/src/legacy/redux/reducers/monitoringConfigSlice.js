import { createSlice } from '@reduxjs/toolkit';
import merge from 'lodash/merge';

const initialState = {
  pointThresholdsMap: {
    // [deviceGuid]: {
    //   [pointCode]: [],
    // }
  },
};

const monitoringConfigSlice = createSlice({
  name: 'monitoringConfig',
  initialState: initialState,
  reducers: {
    updatePointThresholds(state, { payload: { deviceGuid, pointCode, thresholds = [] } }) {
      if (state.pointThresholdsMap[deviceGuid] === undefined) {
        state.pointThresholdsMap[deviceGuid] = {
          [pointCode.toString()]: thresholds,
        };
      } else {
        state.pointThresholdsMap[deviceGuid][pointCode.toString()] = thresholds;
      }
    },
    batchUpdatePointsThresholds(state, { payload }) {
      merge(state.pointThresholdsMap, payload);
    },
  },
});

export const MONITORING_CONFIG_SLICE_NAME = monitoringConfigSlice.name;
export const monitoringConfigActions = monitoringConfigSlice.actions;
export default monitoringConfigSlice.reducer;
