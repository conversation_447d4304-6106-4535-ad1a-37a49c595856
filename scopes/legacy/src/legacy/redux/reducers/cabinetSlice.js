import { createSlice } from '@reduxjs/toolkit';

const INITIAL_STATE = {
  // 请求加载中
  loading: true,
  createCabinetLoading: false,
  editCabinetSubmitLoading: false,
  // 分页查询机柜信息
  cabinetPage: {
    list: [],
    total: '',
  },
  // 新建机柜弹框状态
  createCabinetVisible: false,
  // 查看机柜弹框状态
  viewCabinetVisible: false,
  // 机柜信息
  cabinetMess: {},
  // 编辑状态
  editCabinetVisible: false,
  // 编辑信息
  editCabinetMess: [],
  cabinetPageCondition: {
    pageNum: 1,
    pageSize: 10,
  },
  areaIdcBlockRoomList: [],
  importLoading: false,
  searchValues: {},
};
const cabinetSlice = createSlice({
  name: 'cabinetManage',
  initialState: INITIAL_STATE,
  reducers: {
    request: state => ({ ...state, loading: true }),
    failure: state => ({
      ...state,
      loading: false,
      createCabinetLoading: false,
      editCabinetSubmitLoading: false,
      importLoading: false,
    }),
    // 新建包间弹框状态
    createCabinetVisible: state => ({
      ...state,
      createCabinetVisible: !state.createCabinetVisible,
    }),
    // 分页查询机柜
    featchCabinetPageSuccess: (state, { payload: { total, data } }) => ({
      ...state,
      loading: false,
      cabinetPage: {
        list: data,
        total: total,
        pageSize: '',
      },
    }),
    // 查看机柜弹框状态
    viewCabinetVisible: state => ({
      ...state,
      viewCabinetVisible: !state.viewCabinetVisible,
    }),
    // 查看机柜信息弹框信息
    saveCabinetDetailMess: (state, { payload }) => ({
      ...state,
      cabinetMess: payload,
    }),
    // 编辑状态
    editCabinetVisible: state => ({
      ...state,
      editCabinetVisible: !state.editCabinetVisible,
    }),
    //编辑信息
    editCabinetMess: (state, { payload }) => ({
      ...state,
      editCabinetMess: payload,
    }),

    // 提交新建机柜弹框
    createCabinetLoading: state => ({
      ...state,
      createCabinetLoading: !state.createCabinetLoading,
    }),

    saveCabinetPageConditionSuccess: (state, { payload }) => ({
      ...state,
      cabinetPageCondition: payload,
    }),
    saveAreaIdcBlockRoomCascader: (state, { payload }) => ({
      ...state,
      areaIdcBlockRoomList: payload,
    }),
    editCabinetSubmitLoading: state => {
      state.editCabinetSubmitLoading = !state.editCabinetSubmitLoading;
    },
    importLoading: state => ({
      ...state,
      importLoading: true,
    }),
    updateSearchValues(state, { payload }) {
      state.searchValues = {
        ...state.searchValues,
        ...payload,
      };
    },
    resetSearchValues(state, { payload }) {
      state.searchValues = {};
    },
  },
});
export const CABINET_MANAGE_SLICE_NAME = cabinetSlice.name;
export const cabinetActions = cabinetSlice.actions;
export default cabinetSlice.reducer;
