import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  blocks: [],

  activeBlock: null,
};

const topologySlice = createSlice({
  name: 'topology',
  initialState: initialState,
  reducers: {
    setBlocks(state, { payload: { blocks, initialBlock } }) {
      state.blocks = blocks;
      state.activeBlock = initialBlock ?? blocks[0];
    },

    setActiveBlock(state, { payload }) {
      state.activeBlock = payload;
    },
  },
});

export const TOPOLOGY_SLICE_NAME = topologySlice.name;
export const topologyActions = topologySlice.actions;
export default topologySlice.reducer;
