import { createSlice } from '@reduxjs/toolkit';

const INITIAL_STATE = {
  // 电池组的tree数据源
  blockRoomTreeData: null,

  // 包间-父设施-设备
  roomTagDevice: null,

  // 选中的树节点
  selectedTreeKey: null,

  //电池组告警列表
  batteryAlarmList: [],
};

const batterySlice = createSlice({
  name: 'battery',
  initialState: INITIAL_STATE,
  reducers: {
    setBlockRoom(state, { payload }) {
      state.blockRoomTreeData = payload;
    },
    setRoomTagDevice(state, { payload }) {
      state.roomTagDevice = payload;
    },
    setSelectedTreeKey(state, { payload }) {
      state.selectedTreeKey = payload;
    },
    setBatteryAlarmList(state, { payload }) {
      state.batteryAlarmList = payload;
    },
  },
});

export const BATTERY_SLICE_NAME = batterySlice.name;
export const batteryActions = batterySlice.actions;
export default batterySlice.reducer;
