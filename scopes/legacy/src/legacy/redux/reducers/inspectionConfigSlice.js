import { createSlice } from '@reduxjs/toolkit';

import { ConfigRangeType } from '@manyun/ticket.model.task';

const initialState = {
  loading: false,
  pagination: {
    pageNum: 1,
    pageSize: 10,
  },
  data: [],
  total: 0,
  searchValues: {
    //巡检配置名称
    configName: {
      value: null,
    },

    // 巡检对象类型
    inspectSubject: {
      value: null,
    },

    //巡检子类型guid
    subTypeCode: {
      value: null,
    },

    //创建时间
    createTime: {
      value: [],
    },

    // 创建人ID
    creatorId: {
      value: undefined,
    },
    roomType: {
      value: null,
    },
    inspectType: {
      value: null,
    },
    supportCheckScenes: {
      value: null,
    },
  },
  // 添加配置信息
  create: {
    // 巡检类型
    inspectType: {
      value: null,
    },
    effectType: {
      value: ConfigRangeType.Block,
    },
    // 设备类型
    subTypeCode: {
      value: null,
    },

    roomType: {
      value: null,
    },

    // 巡检配置项名称
    configName: {
      value: null,
    },
    inspectSubject: {
      value: null,
    },
    supportCheckScenes: {
      value: false,
    },
  },
  // 巡检配置详情
  detail: {},
  edit: {
    // 巡检类型
    inspectType: {
      value: null,
    },

    // 设备类型
    subTypeCode: {
      value: null,
    },

    roomType: {
      value: null,
    },

    // 巡检配置项名称
    configName: {
      value: null,
    },
    inspectSubject: {
      value: null,
    },
    supportCheckScenes: {
      value: false,
    },
  },
  // points: [],
  configTableDate: null,
};

const inspectionConfigSlice = createSlice({
  name: 'inspectionConfig',
  initialState: initialState,
  reducers: {
    failure(state) {
      state.loading = false;
    },
    updateTableLoading(state, { payload }) {
      state.loading = payload;
    },
    resetPageNum(state) {
      state.pagination.pageNum = 1;
      state.pagination.pageSize = 10;
    },
    setDataAndTotal(state, { payload: { data, total } }) {
      state.data = data;
      state.total = total;
      state.loading = false;
    },
    updateSearchValues(state, { payload }) {
      state.searchValues = { ...state.searchValues, ...payload };
    },
    resetSearchValuesAndPagination(state) {
      state.searchValues = initialState.searchValues;
      state.pagination = initialState.pagination;
    },
    setPagination(state, { payload }) {
      state.pagination = payload;
    },
    setConfigInfos(state, { payload }) {
      state.detail = payload;
    },
    upDateCreateOptionValues(state, { payload }) {
      state.create = { ...state.create, ...payload };
    },
    setPointsInDevice(state, { payload }) {
      state.points = payload;
    },
    setEditConfigInfos(state, { payload }) {
      state.edit = payload;
    },
    upDateEditOptionValues(state, { payload }) {
      state.edit = { ...state.edit, ...payload };
    },
    setConfigTableDate(state, { payload }) {
      state.configTableDate = payload;
    },
    restInspectionData(state) {
      state.create = initialState.create;
      state.edit = initialState.edit;
      state.detail = initialState.detail;
      state.configTableDate = initialState.configTableDate;
    },
    resetInspectionPageData(state) {
      state.pagination = initialState.pagination;
      state.data = initialState.data;
      state.total = initialState.total;
      state.loading = initialState.loading;
      state.searchValues = initialState.searchValues;
    },
  },
});

export const INSPECTION_CONFIG_SLICE_NAME = inspectionConfigSlice.name;
export const inspectionConfigActions = inspectionConfigSlice.actions;
export default inspectionConfigSlice.reducer;
