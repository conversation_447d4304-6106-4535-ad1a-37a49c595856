import { STATUS_MAP } from '@manyun/dc-brain.legacy.constants/statusColor';

export const mockCabinetArrayInfo = {
  // 机列编号
  numbers: ['A', 'B', 'C', 'D'],
  // 机列列表
  list: [
    {
      arrayCabinet: 'A',
      cabinets: [
        {
          cabinet: 'A01',
          pdus: [
            {
              deviceGuid: 'V_PDU_01_A',
              deviceType: '8080A',
            },
            {
              deviceGuid: 'V_PDU_01_B',
              deviceType: '8080A',
            },
          ],
        },
        {
          cabinet: 'A02',
          pdus: [
            {
              deviceGuid: 'V_PDU_02',
              deviceType: '8080A',
            },
          ],
        },
        {
          cabinet: 'A03',
          pdus: [
            {
              deviceGuid: 'V_PDU_03',
              deviceType: '8080A',
            },
          ],
        },
        {
          cabinet: 'A04',
          pdus: [
            {
              deviceGuid: 'V_PDU_04',
              deviceType: '8080A',
            },
          ],
        },
        {
          cabinet: 'A05',
          pdus: [
            {
              deviceGuid: 'V_PDU_05',
              deviceType: '8080A',
            },
          ],
        },
      ],
    },
    {
      arrayCabinet: 'B',
      cabinets: [
        {
          cabinet: 'B01',
          pdus: [
            {
              deviceGuid: 'V_PDU_06',
              deviceType: '8080A',
            },
          ],
        },
        {
          cabinet: 'B02',
          pdus: [
            {
              deviceGuid: 'V_PDU_07',
              deviceType: '8080A',
            },
          ],
        },
        {
          cabinet: 'B03',
          pdus: [
            {
              deviceGuid: 'V_PDU_08',
              deviceType: '8080A',
            },
          ],
        },
        {
          cabinet: 'B04',
          pdus: [
            {
              deviceGuid: 'V_PDU_09',
              deviceType: '8080A',
            },
          ],
        },
        {
          cabinet: 'B05',
          pdus: [
            {
              deviceGuid: 'V_PDU_10',
              deviceType: '8080A',
            },
          ],
        },
        {
          cabinet: 'B06',
          pdus: [
            {
              deviceGuid: 'V_PDU_11',
              deviceType: '8080A',
            },
          ],
        },
        {
          cabinet: 'B07',
          pdus: [
            {
              deviceGuid: 'V_PDU_12',
              deviceType: '8080A',
            },
          ],
        },
        {
          cabinet: 'B08',
          pdus: [
            {
              deviceGuid: 'V_PDU_13',
              deviceType: '8080A',
            },
          ],
        },
        {
          cabinet: 'B09',
          pdus: [
            {
              deviceGuid: 'V_PDU_14',
              deviceType: '8080A',
            },
          ],
        },
        {
          cabinet: 'B10',
          pdus: [
            {
              deviceGuid: 'V_PDU_15',
              deviceType: '8080A',
            },
          ],
        },
        {
          cabinet: 'B11',
          pdus: [
            {
              deviceGuid: 'V_PDU_16',
              deviceType: '8080A',
            },
          ],
        },
        {
          cabinet: 'B12',
          pdus: [
            {
              deviceGuid: 'V_PDU_17',
              deviceType: '8080A',
            },
          ],
        },
        {
          cabinet: 'B13',
          pdus: [
            {
              deviceGuid: 'V_PDU_18',
              deviceType: '8080A',
            },
          ],
        },
        {
          cabinet: 'B14',
          pdus: [
            {
              deviceGuid: 'V_PDU_19',
              deviceType: '8080A',
            },
          ],
        },
        {
          cabinet: 'B15',
          pdus: [
            {
              deviceGuid: 'V_PDU_20',
              deviceType: '8080A',
            },
          ],
        },
        {
          cabinet: 'B16',
          pdus: [
            {
              deviceGuid: 'V_PDU_21',
              deviceType: '8080A',
            },
          ],
        },
      ],
    },
    {
      arrayCabinet: 'C',
      cabinets: [
        {
          cabinet: 'C01',
          pdus: [
            {
              deviceGuid: 'V_PDU_22',
              deviceType: '8080A',
            },
          ],
        },
        {
          cabinet: 'C02',
          pdus: [
            {
              deviceGuid: 'V_PDU_23',
              deviceType: '8080A',
            },
          ],
        },
        {
          cabinet: 'C03',
          pdus: [
            {
              deviceGuid: 'V_PDU_24',
              deviceType: '8080A',
            },
          ],
        },
        {
          cabinet: 'C04',
          pdus: [
            {
              deviceGuid: 'V_PDU_25',
              deviceType: '8080A',
            },
          ],
        },
        {
          cabinet: 'C05',
          pdus: [
            {
              deviceGuid: 'V_PDU_26',
              deviceType: '8080A',
            },
          ],
        },
        {
          cabinet: 'C06',
          pdus: [
            {
              deviceGuid: 'V_PDU_27',
              deviceType: '8080A',
            },
          ],
        },
        {
          cabinet: 'C07',
          pdus: [
            {
              deviceGuid: 'V_PDU_28',
              deviceType: '8080A',
            },
          ],
        },
        {
          cabinet: 'C08',
          pdus: [
            {
              deviceGuid: 'V_PDU_29',
              deviceType: '8080A',
            },
          ],
        },
        {
          cabinet: 'C09',
          pdus: [
            {
              deviceGuid: 'V_PDU_30',
              deviceType: '8080A',
            },
          ],
        },
        {
          cabinet: 'C10',
          pdus: [
            {
              deviceGuid: 'V_PDU_31',
              deviceType: '8080A',
            },
          ],
        },
        {
          cabinet: 'C11',
          pdus: [
            {
              deviceGuid: 'V_PDU_32',
              deviceType: '8080A',
            },
          ],
        },
        {
          cabinet: 'C12',
          pdus: [
            {
              deviceGuid: 'V_PDU_33',
              deviceType: '8080A',
            },
          ],
        },
        {
          cabinet: 'C13',
          pdus: [
            {
              deviceGuid: 'V_PDU_34',
              deviceType: '8080A',
            },
          ],
        },
        {
          cabinet: 'C14',
          pdus: [
            {
              deviceGuid: 'V_PDU_35',
              deviceType: '8080A',
            },
          ],
        },
        {
          cabinet: 'C15',
          pdus: [
            {
              deviceGuid: 'V_PDU_36',
              deviceType: '8080A',
            },
          ],
        },
        {
          cabinet: 'C16',
          pdus: [
            {
              deviceGuid: 'V_PDU_37',
              deviceType: '8080A',
            },
          ],
        },
      ],
    },
  ],
  relatedDeviceList: [
    {
      deviceGuid: 'AC_01',
      deviceType: '2010A', // 自定义设备类型-空调
      extendedLocation: 'A01.N',
    },
    {
      deviceGuid: 'AC_02',
      deviceType: '2010A',
      extendedLocation: 'B16.S',
    },
    {
      deviceGuid: 'AC_03',
      deviceType: '2010A',
      extendedLocation: 'C01.N',
    },
    {
      deviceGuid: 'AC_04',
      deviceType: '2010A',
      extendedLocation: 'C16.S',
    },
    {
      deviceGuid: 'THTB_01',
      deviceType: '30118', // 冷通道温湿度传感器
      extendedLocation: 'A03.L',
    },
    {
      deviceGuid: 'THTB_02',
      deviceType: '30119', // 热通道温湿度传感器
      extendedLocation: 'A03.R',
    },
    {
      deviceGuid: 'THTB_03',
      deviceType: '30118', // 冷通道温湿度传感器
      extendedLocation: 'B03.R',
    },
    {
      deviceGuid: 'THTB_04',
      deviceType: '30118', // 冷通道温湿度传感器
      extendedLocation: 'B14.R',
    },
    {
      deviceGuid: 'THTB_05',
      deviceType: '30119', // 热通道温湿度传感器
      extendedLocation: 'C03.R',
    },
  ],
};

export const mockMonitoringInfo = {
  THTB_01: {
    1001: {
      value: 21,
      status: 'normal',
    },
    1002: {
      value: 59,
      status: STATUS_MAP.ALARM,
    },
  },
  THTB_02: {
    1001: {
      value: 30,
      status: 'alarm',
    },
    1002: {
      value: 35,
      status: 'alarm',
    },
  },

  // 空调
  AC_01: {
    '2010AD01': {
      value: 22,
      status: 'normal',
    },
  },
  AC_03: {
    '2010AD01': {
      value: 30,
      status: 'alarm',
    },
  },

  // PDU
  V_PDU_01_A: {
    '8080AD01': {
      status: STATUS_MAP.NORMAL,
    },
  },
  V_PDU_01_B: {
    '8080AD01': {
      status: STATUS_MAP.WARNING,
    },
  },
};
