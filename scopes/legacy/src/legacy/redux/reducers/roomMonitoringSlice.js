import { createSlice } from '@reduxjs/toolkit';

import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';

const initialState = {
  roomInfo: null,

  loading: false,

  // 设计架构
  designArchitecture: {
    electricity: BLANK_PLACEHOLDER, // 电力架构
  },

  // 告警
  loadingAlarmInfo: false,
  alarmInfo: {
    data: [],
    total: 0,
  },

  loadingCabinetArrayInfo: false,
  cabinetArrayInfo: {},
  infrastructureData: {
    entities: {
      roomDevices: null,
      roomGrids: null,
    },
    result: {
      // 机柜总数
      gridCount: 0,
      // 上电机柜数
      poweredGridCount: 0,
      // 制冷架构
      refStructure: BLANK_PLACEHOLDER,
      // 已排序的机列编号索引集合。如：['A','B','C']
      roomGrids: null,
    },
  },

  containerMaxWidth: 0,
  maxHeight: 0,
  // 已经处理好的机柜信息
  rackColumns: null,

  // 实时数据-原始
  rawPointsData: null,
  // 实时数据-聚合
  aggPointsData: {},

  // 曲线数据（按设备 `deviceGuid`）
  deviceRangeData: {},
  // x 秒自动刷新曲线数据
  chartDataInterval: null,

  // 包间客户类别
  roomCustomers: {
    data: [],
    total: 0,
  },

  // 设备告警数据
  alarmPointsData: {
    /**
     * [deviceGuid]: {
     *   [pointCode]: {
     *     alarmType: {
     *       code: 'WARN',
     *       name: '预警',
     *     }
     *   }
     * }
     */
  },

  // 列头柜的上联设备
  arrayCabinetsParentDeviceMap: {},
  parentDevicesMonitoringData: null,
  // `UPS`或`HVDC`CMDB信息
  deviceInfoMap: {},
  // `UPS` 或 `HVDC` 的下联设备（拓扑图中的设备），用于计算对应机列
  childDeviceMap: {},
  // `UPS` 或 `HVDC` 的下联电池组
  childBatteryUnitsMap: {},
  // `UPS` 或 `HVDC` 的下联电池组的实时数据和告警数据
  childBatteryUnitsData: {
    rawData: {},
    alarmData: null,
  },

  // 测点对应的监控项的阈值
  pointThresholdsMap: null,

  // 设施包间拓扑预览数据
  infraGraphPreview: {
    roomGuid: null,
    graph: null,
    deviceGuids: [],
    extraDevicesMap: {},
    updateAt: 0,
  },
};

const roomMonitoringSlice = createSlice({
  name: 'roomMonitoring',
  initialState: initialState,
  reducers: {
    setRawPointsData: (state, { payload }) => {
      state.rawPointsData = payload;
    },
    setAggPointsData: (state, { payload }) => {
      state.aggPointsData = payload;
    },
    requestCabinetArrayInfo: state => ({
      ...state,
      loadingCabinetArrayInfo: true,
    }),
    setInfrastructureData: (state, { payload }) => {
      state.loadingCabinetArrayInfo = false;
      state.infrastructureData = payload;
    },
    setDeviceRangeData: ({ deviceRangeData }, { payload: { deviceGuid, pointCodes, data } }) => {
      const propName = pointCodes.join('-');
      if (deviceRangeData[deviceGuid] === undefined) {
        deviceRangeData[deviceGuid] = {
          [propName]: data,
        };
      } else {
        deviceRangeData[deviceGuid][propName] = data;
      }
    },
    setRoomCustomers: (state, { payload }) => {
      state.roomCustomers = payload;
    },
    requestAlarmInfo: state => ({
      ...state,
      loadingAlarmInfo: true,
    }),
    requestAlarmInfoError(state) {
      state.loadingAlarmInfo = false;
    },
    setAlarmInfo: (state, { payload }) => {
      state.loadingAlarmInfo = false;
      state.alarmInfo = payload;
    },
    setAlarmPointsData(state, { payload }) {
      state.alarmPointsData = payload;
    },
    updateArrayCabinetsParentDeviceMap(state, { payload }) {
      state.arrayCabinetsParentDeviceMap = {
        ...state.arrayCabinetsParentDeviceMap,
        ...payload,
      };
    },
    updateParentDevicesMonitoringData(state, { payload }) {
      state.parentDevicesMonitoringData = payload;
    },
    updateChildDeviceMap(state, { payload }) {
      state.childDeviceMap = {
        ...state.childDeviceMap,
        ...payload,
      };
    },
    setChartDataInterval(state, { payload }) {
      state.chartDataInterval = payload;
    },
    updateChildBatteryUnits(state, { payload }) {
      state.childBatteryUnitsMap = {
        ...state.childBatteryUnitsMap,
        ...payload,
      };
    },
    updateBatteryUnitsData(state, { payload }) {
      state.childBatteryUnitsData = payload;
    },
    updateDeviceInfo(state, { payload }) {
      state.deviceInfoMap = {
        ...state.deviceInfoMap,
        ...payload,
      };
    },
    setPointThresholds(state, { payload: { deviceGuid, pointCode, thresholds } }) {
      if (state.pointThresholdsMap === null) {
        state.pointThresholdsMap = {
          [deviceGuid]: {
            [pointCode]: thresholds,
          },
        };
      } else if (state.pointThresholdsMap[deviceGuid] === undefined) {
        state.pointThresholdsMap[deviceGuid] = {
          [pointCode]: thresholds,
        };
      } else {
        state.pointThresholdsMap[deviceGuid][pointCode] = thresholds;
      }
    },
    setRoomInfo(state, { payload }) {
      state.roomInfo = payload;
    },
    setRackColumns(state, { payload: { containerMaxWidth, maxHeight, rackColumns } }) {
      state.containerMaxWidth = containerMaxWidth;
      state.maxHeight = maxHeight;
      state.rackColumns = rackColumns;
    },
    setInfraRoomGraphPreview(
      state,
      { payload: { roomGuid, graph, deviceGuids, extraDevicesMap, updatedAt } }
    ) {
      state.infraGraphPreview.roomGuid = roomGuid;
      state.infraGraphPreview.graph = graph;
      state.infraGraphPreview.deviceGuids = deviceGuids;
      state.infraGraphPreview.extraDevicesMap = extraDevicesMap;
      state.infraGraphPreview.updateAt = updatedAt;
    },
    resetInfraRoomGraphPreview(state) {
      state.infraGraphPreview.roomGuid = null;
      state.infraGraphPreview.graph = null;
      state.infraGraphPreview.deviceGuids = [];
      state.infraGraphPreview.extraDevicesMap = {};
      state.infraGraphPreview.updateAt = 0;
    },
  },
});

export const ROOM_MONITORING_SLICE_NAME = roomMonitoringSlice.name;
export const roomMonitoringActions = roomMonitoringSlice.actions;
export default roomMonitoringSlice.reducer;
