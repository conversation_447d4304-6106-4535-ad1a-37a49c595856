import { createSlice } from '@reduxjs/toolkit';

const INITIAL_STATE = {
  // 请求加载中
  loading: true,

  createLoading: true,

  editLoading: true,

  scenesListLoading: true,

  approvalProcessPageCondition: {
    pageNum: 1,
    pageSize: 10,
  },
  // 审批流程列表
  approvalProcessList: {
    list: [],
    total: 0,
  },

  //
  processConfigDetail: {
    processCode: null,
    processName: null,
    processXml: null,
    // userTaskNodeList: [],
  },

  scenesList: [],

  searchValue: null, // 与 input 双向绑定
  searchCondition: null, //  通过 input 搜索成功后，将 searchValue 赋值给 searchCondition
};
const approvalConfigSlice = createSlice({
  name: 'approvalProcessConfig',
  initialState: INITIAL_STATE,
  reducers: {
    request: state => ({ ...state, loading: true }),
    failure: state => ({
      ...state,
      loading: false,
    }),

    createRequest: state => ({ ...state, createLoading: true }),
    createFailure: state => ({
      ...state,
      createLoading: false,
    }),

    editRequest: state => ({ ...state, editLoading: true }),
    editFailure: state => ({
      ...state,
      editLoading: false,
    }),

    scenesListRequest: state => ({ ...state, scenesListLoading: true }),
    scenesListFailure: state => ({
      ...state,
      scenesListLoading: false,
    }),

    featchApprovalProcessListSuccess: (state, { payload: { total, data } }) => ({
      ...state,
      loading: false,
      approvalProcessList: {
        list: data,
        total: total,
      },
    }),

    featchApprovalProcessDetailSuccess: (state, { payload: data }) => ({
      ...state,
      processConfigDetail: {
        processCode: data.processCode,
        processName: data.processName,
        processXml: data.processXml,
        // userTaskNodeList: data.processJson.userTaskNodeList,
      },
    }),

    featchApprovalScenesListSuccess: (state, { payload: { total, data } }) => ({
      ...state,
      scenesListLoading: false,
      scenesList: data,
    }),

    savePageCondition: (state, { payload }) => ({
      ...state,
      approvalProcessPageCondition: { ...payload },
    }),
    setSearchValue(state, { payload }) {
      state.searchValue = payload;
    },
    setSearchCondition(state, { payload }) {
      state.searchCondition = payload;
    },
  },
});

export const APPROVAL_CONFIG_SLICE_NAME = approvalConfigSlice.name;
export const approvalConfigActions = approvalConfigSlice.actions;
export default approvalConfigSlice.reducer;
