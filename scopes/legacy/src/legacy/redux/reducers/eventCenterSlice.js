import { createSlice } from '@reduxjs/toolkit';

const INITIAL_STATE = {
  eventCenterList: {
    list: [],
    total: 0,
    pageNum: 1,
    pageSize: 10,
    loading: true,
  },
  eventCenterInfo: {},
  eventLife: {},
  progressUpdateList: {
    list: [],
    total: 0,
    pageNum: 1,
    pageSize: 10,
    loading: true,
  },
  infoType: '',
  eventSourceList: [],
  eventTopCategoryList: [],
  eventSecondCategoryList: [],
  eventResponsibleSectorList: [],
  eventReasonTypeList: [],
  specNameList: [],
  specUnitList: [],
  eventReasonTypeNormalized: null,
  eventResponsibleSectorNormalized: null,
  eventSourceNormalized: null,
  addFileInfos: [],
  searchValues: {},
};

const eventCenterSlice = createSlice({
  name: 'eventCenter',
  initialState: INITIAL_STATE,
  reducers: {
    request: state => ({
      ...state,
      eventCenterList: {
        ...state.eventCenterList,
        loading: true,
      },
    }),
    failure: state => ({
      ...state,
      eventCenterList: {
        ...state.eventCenterList,
        loading: false,
      },
      progressUpdateList: {
        ...state.progressUpdateList,
        loading: false,
      },
    }),
    fetchEventCenterSuccess: (state, { payload }) => ({
      ...state,
      eventCenterList: {
        ...payload,
        loading: false,
      },
    }),
    progressUpdateListSuccess: (state, { payload }) => ({
      ...state,
      progressUpdateList: {
        list: payload.list,
        total: payload.total,
        pageNum: payload.pageNum,
        pageSize: payload.pageNum,
        loading: false,
      },
    }),
    eventDetailSuccess: (state, { payload }) => {
      return {
        ...state,
        eventCenterInfo: payload,
      };
    },
    eventLifeSuccess: (state, { payload }) => ({
      ...state,
      eventLife: payload,
    }),
    eventSourceListSuccess: (state, { payload }) => ({
      ...state,
      eventSourceList: payload.list,
      eventSourceNormalized: payload.normalizedList,
    }),
    eventTopCategoryListSuccess: (state, { payload }) => ({
      ...state,
      eventTopCategoryList: payload,
    }),
    eventSecondCategoryListSuccess: (state, { payload }) => ({
      ...state,
      eventSecondCategoryList: payload,
    }),
    eventResponsibleSectorListSuccess: (state, { payload }) => ({
      ...state,
      eventResponsibleSectorList: payload.list,
      eventResponsibleSectorNormalized: payload.normalizedList,
    }),
    eventReasonTypeListSuccess: (state, { payload }) => ({
      ...state,
      eventReasonTypeList: payload.list,
      eventReasonTypeNormalized: payload.normalizedList,
    }),
    specNameListSuccess: (state, { payload }) => ({
      ...state,
      specNameList: payload.list,
    }),
    specUnitListSuccess: (state, { payload }) => ({
      ...state,
      specUnitList: payload.list,
    }),
    addFileInfos: (state, { payload }) => ({
      ...state,
      addFileInfos: payload,
    }),
    updateSearchValues(state, { payload }) {
      state.searchValues = {
        ...state.searchValues,
        ...payload,
      };
    },
    resetSearchValues(state) {
      state.searchValues = {};
    },
  },
});
export const EVENT_CENTER_SLICE_NAME = eventCenterSlice.name;
export const eventCenterActions = eventCenterSlice.actions;
export default eventCenterSlice.reducer;
