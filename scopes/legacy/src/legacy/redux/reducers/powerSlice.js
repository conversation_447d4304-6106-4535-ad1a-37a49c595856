import { createSlice } from '@reduxjs/toolkit';

const INITIAL_STATE = {
  // 请求加载中
  loading: true,
  // 分页查询权限信息
  powerPage: {
    list: [],
    total: '',
    pageNo: 1,
    pageSize: 10,
    searchType: 'SYS',
    searchName: '',
  },

  // 新建权限弹框状态
  createPowerVisible: false,
  // 编辑权限弹框状态
  editPowerVisible: false,
  // 编辑信息
  powerMess: {},
  powerTreeSeclect: [],
};
const powerSlice = createSlice({
  name: 'powerManage',
  initialState: INITIAL_STATE,
  reducers: {
    request: state => ({ ...state, loading: true }),
    success: (state, { payload }) => ({
      ...state,
      loading: false,
      powerPage: { ...payload },
    }),
    failure: state => ({
      ...state,
      loading: false,
      createPowerLoading: false,
    }),
    // 新建权限弹框状态
    createPowerVisible: state => ({
      ...state,
      createPowerVisible: !state.createPowerVisible,
    }),
    // 编辑权限弹框状态
    editPowerVisible: state => ({
      ...state,
      editPowerVisible: !state.editPowerVisible,
    }),
    // 编辑权限弹框信息
    editPowerMess: (state, { payload }) => ({
      ...state,
      powerMess: payload,
    }),

    // 提交新建权限弹框
    createPowerLoading: state => ({
      ...state,
      createPowerLoading: !state.createPowerLoading,
    }),

    fetchPowerTreeSeclectSuccess: (state, { payload }) => ({
      ...state,
      powerTreeSeclect: payload,
    }),
    saveSearchCondition: (state, { payload }) => ({
      ...state,
      powerPage: {
        ...state.powerPage,
        ...payload,
      },
    }),
  },
});
export const POWER_MANAGE_SLICE_NAME = powerSlice.name;
export const powerActions = powerSlice.actions;
export default powerSlice.reducer;
