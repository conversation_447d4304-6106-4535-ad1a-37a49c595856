import { createSlice } from '@reduxjs/toolkit';

const INITIAL_STATE = {
  // 请求加载中
  loading: true,
  // 分页查询权限信息
  roomPage: {
    list: [],
    total: '',
  },
  // 新建包间弹框状态
  createRoomVisible: false,
  // 查看包间
  viewRoomVisible: false,
  // 查看包间信息
  RoomDetailMess: {},
  // 编辑包间弹框状态
  editRoomVisible: false,
  // 编辑包间信息
  editRoomMessList: {},
  roomPageCondition: {
    pageNum: 1,
    pageSize: 10,
  },
  searchValues: {},
};
const roomSlice = createSlice({
  name: 'roomManage',
  initialState: INITIAL_STATE,
  reducers: {
    request: state => ({ ...state, loading: true }),
    success: (state, { payload: { total, data } }) => ({
      ...state,
      loading: false,
      powerPage: {
        list: data,
        total: total,
        pageSize: '',
      },
    }),
    failure: state => ({
      ...state,
      loading: false,
      createRoomLoading: false,
    }),
    // 新建包间弹框状态
    createRoomVisible: state => ({
      ...state,
      createRoomVisible: !state.createRoomVisible,
    }),
    // 分页查询包间列表
    featchRoomPageSuccess: (state, { payload: { total, data } }) => ({
      ...state,
      loading: false,
      roomPage: {
        list: data,
        total: total,
        pageSize: '',
      },
    }),
    // 查看包间状态
    viewRoomVisible: state => ({
      ...state,
      viewRoomVisible: !state.viewRoomVisible,
    }),
    // 查看包间信息弹框信息
    viewRoomDetailMess: (state, { payload }) => ({
      ...state,
      RoomDetailMess: payload,
    }),
    // 编辑包间弹框状态
    editRoomVisible: state => ({
      ...state,
      editRoomVisible: !state.editRoomVisible,
    }),
    // 编辑包间信息
    editRoomMess: (state, { payload }) => ({
      ...state,
      editRoomMessList: payload,
    }),
    // 提交新建包间状态
    createRoomLoading: state => ({
      ...state,
      createRoomLoading: !state.createRoomLoading,
    }),

    saveRoomPageConditionSuccess: (state, { payload }) => ({
      ...state,
      roomPageCondition: payload,
    }),
    updateSearchValues(state, { payload }) {
      state.searchValues = {
        ...state.searchValues,
        ...payload,
      };
    },
    resetSearchValues(state, { payload }) {
      state.searchValues = {};
    },
  },
});
export const ROOM_MANAGE_SLICE_NAME = roomSlice.name;
export const roomActions = roomSlice.actions;
export default roomSlice.reducer;
