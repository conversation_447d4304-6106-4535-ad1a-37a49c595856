import { createSlice } from '@reduxjs/toolkit';

const INITIAL_STATE = {
  // loading: true,
  createTicketVisible: false,
  basicInfo: {},
  createUserGroupLoading: false,
  userList: {
    list: [],
    total: 0,
    loading: false,
    pageNo: 1,
    pageSize: 10,
    searchName: '',
    searchType: 'USER_NAME',
  },
  roleList: {
    list: [],
    total: 0,
    loading: false,
    pageNo: 1,
    pageSize: 10,
    searchName: '',
    searchType: 'ROLE_NAME',
  },
  resourceList: {
    list: [],
    total: 0,
    loading: false,
    pageNo: 1,
    pageSize: 10,
    searchName: '',
    searchType: 'RESOURCE_NAME',
  },
  userGroupList: {
    list: [],
    total: 0,
    loading: false,
    pageNo: 1,
    pageSize: 10,
    searchName: '',
    searchType: 'GROUP_NAME',
  },
  createVisible: false,
};

const userGroupSlice = createSlice({
  name: 'userGroupManage',
  initialState: INITIAL_STATE,
  reducers: {
    request: state => ({
      ...state,
      userGroupList: {
        ...state.userList,
        loading: true,
      },
    }),
    success: (state, { payload }) => ({
      ...state,
      userGroupList: { ...payload },
    }),
    failure: state => ({
      ...state,
      // loading: false,
      createUserGroupLoading: false,
      userList: {
        ...state.userList,
        loading: false,
      },
      roleList: {
        ...state.roleList,
        loading: false,
      },
      resourceList: {
        ...state.resourceList,
        loading: false,
      },
      userGroupList: {
        ...state.userGroupList,
        loading: false,
      },
    }),
    createTicketVisible: state => ({
      ...state,
      createTicketVisible: !state.createTicketVisible,
    }),
    createUserGroupLoading: state => ({
      ...state,
      createUserGroupLoading: !state.createUserGroupLoading,
    }),
    userListRequest: state => ({
      ...state,
      userList: {
        ...state.userList,
        loading: true,
      },
    }),
    userListSuccess: (state, { payload }) => ({
      ...state,
      userList: { ...payload },
    }),
    roleListRequest: state => ({
      ...state,
      roleList: {
        ...state.roleList,
        loading: true,
      },
    }),
    roleListSuccess: (state, { payload }) => ({
      ...state,
      roleList: { ...payload },
    }),
    resourceListRequest: state => ({
      ...state,
      resourceList: {
        ...state.resourceList,
        loading: true,
      },
    }),
    resourceListSuccess: (state, { payload }) => ({
      ...state,
      resourceList: { ...payload },
    }),
    userGroupDetail: (state, { payload }) => ({
      ...state,
      basicInfo: payload,
    }),
    createVisible: state => ({
      ...state,
      createVisible: !state.createVisible,
    }),
  },
});

export const USER_GROUP_MANAGE_SLICE_NAME = userGroupSlice.name;
export const userGroupManageActions = userGroupSlice.actions;
export default userGroupSlice.reducer;
