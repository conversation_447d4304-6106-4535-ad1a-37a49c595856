import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  device: null,

  alarmInfoLoading: false,
  alarmInfo: {
    data: [],
    total: 0,
  },

  changeTicketInfoLoading: false,
  changeTicketInfo: {
    data: [],
    total: 0,
  },

  eventTicketInfoLoading: false,
  eventTicketInfo: {
    data: [],
    total: 0,
  },

  rawPointsData: null,

  alarmData: null,

  /**
   * 根据设备类型获取的点位数据
   */
  points: [],

  topologyBoundary: null,
  lvGraphTransformerGroupKey: undefined,

  topologyData: null,

  /**
   * @typedef {'ok'|'none'|'not-found'} TOPOLOGY_STATUS ok->当前楼栋存在电力拓扑且当前设备存在逻辑拓扑；none->当前楼栋无电力拓扑；not-found->当前楼栋存在电力拓扑且当前设备无逻辑拓扑
   * @type {TOPOLOGY_STATUS}
   */
  topologyStatus: 'none',

  // 楼栋下的所有设备
  devices: [],
};

const deviceMonitoringSlice = createSlice({
  name: 'deviceMonitoring',
  initialState,
  reducers: {
    setDevice(state, { payload }) {
      state.device = payload;
    },
    requestAlarmInfo: state => ({
      ...state,
      alarmInfoLoading: true,
    }),
    requestAlarmInfoError: state => ({
      ...state,
      alarmInfoLoading: false,
    }),
    setAlarmInfo: (state, { payload }) => ({
      ...state,
      alarmInfoLoading: false,
      alarmInfo: payload,
    }),
    setMonitoringData(state, { payload }) {
      state.rawPointsData = payload;
    },
    setAlarmData(state, { payload }) {
      state.alarmData = payload;
    },
    setPoints: (state, { payload }) => ({
      ...state,
      points: payload,
    }),

    /**
     * 根据设备 GUID 所在的拓扑设置拓扑类型
     * @param {initialState} state
     * @param {{ payload: { boundary: TOPOLOGY_BOUNDARY; transformerGroupKey?: string } }} action
     */
    setTopologyBoundary(state, { payload: { boundary, transformerGroupKey } }) {
      state.topologyBoundary = boundary;
      state.lvGraphTransformerGroupKey = transformerGroupKey;
    },

    setTopology(state, { payload }) {
      state.topologyData = payload;
      state.topologyStatus = 'ok';
    },
    resetTopology(state) {
      state.topologyData = null;
      state.topologyStatus = 'none';
      state.topologyBoundary = null;
      state.lvGraphTransformerGroupKey = undefined;
    },

    /**
     * @param {typeof initialState} state
     * @param {{ payload: TOPOLOGY_STATUS }} action
     */
    setTopologyStatus(state, { payload }) {
      state.topologyStatus = payload;
    },

    setDevices(state, { payload }) {
      state.devices = payload;
    },

    requestChangeTicketInfo(state) {
      state.changeTicketInfoLoading = true;
    },
    requestChangeTicketInfoError(state) {
      state.changeTicketInfoLoading = false;
    },
    setChangeTicketInfo(state, { payload: { data, total } }) {
      state.changeTicketInfoLoading = false;
      state.changeTicketInfo = { data, total };
    },

    requestEventTicketInfo(state) {
      state.eventTicketInfoLoading = true;
    },
    requestEventTicketInfoError(state) {
      state.eventTicketInfoLoading = false;
    },
    setEventTicketInfo(state, { payload: { data, total } }) {
      state.eventTicketInfoLoading = false;
      state.eventTicketInfo = { data, total };
    },
  },
});

export const DEVICE_MONITORING_SLICE_NAME = deviceMonitoringSlice.name;
export const deviceMonitoringActions = deviceMonitoringSlice.actions;
export default deviceMonitoringSlice.reducer;
