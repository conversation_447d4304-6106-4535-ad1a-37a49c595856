import { createSlice } from '@reduxjs/toolkit';

const INITIAL_STATE = {
  /**
   * 初始化完成标识
   */
  isReady: false,

  // 当前包间下所有设备（按设备类型聚合）
  devicesTree: null,

  // 当前包间的拓扑的 ID
  id: null,

  // 当前包间的拓扑的数据
  graph: null,
};

const infraRoomGraphixSlice = createSlice({
  name: 'infraRoomGraphix',
  initialState: INITIAL_STATE,
  reducers: {
    initialized(state, { payload: { id, graph, isReady, devicesTree } }) {
      state.id = id;
      state.graph = graph;
      state.isReady = isReady;
      state.devicesTree = devicesTree;
    },
    clearGraph(state) {
      state.isReady = false;
      state.id = null;
      state.graph = null;
      state.devicesTree = null;
    },
  },
});

export const INFRA_ROOM_GRAPHIX_SLICE_NAME = infraRoomGraphixSlice.name;
export const infraRoomGraphixActions = infraRoomGraphixSlice.actions;
export default infraRoomGraphixSlice.reducer;
