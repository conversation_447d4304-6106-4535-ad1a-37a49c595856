import { createSlice } from '@reduxjs/toolkit';

const INITIAL_STATE = {
  inventoryAnalyticsPage: {
    list: [],
    total: '',
    loading: true,
  },
  inventoryAnalyticsPageCondition: {
    pageNum: 1,
    pageSize: 10,
  },
  countList: [],
  searchValues: {},
};

const inventoryAnalyticsSlice = createSlice({
  name: 'inventoryAnalytics',
  initialState: INITIAL_STATE,
  reducers: {
    request: state => ({
      ...state,
      inventoryAnalyticsPage: {
        ...state.inventoryAnalyticsPage,
        loading: true,
      },
    }),
    failure: state => ({
      ...state,
      inventoryAnalyticsPage: {
        ...state.inventoryAnalyticsPage,
        loading: false,
      },
    }),
    fetchInventoryAnalyticsListSuccess: (state, { payload: { total, data } }) => ({
      ...state,
      inventoryAnalyticsPage: {
        list: data.map((item, index) => (item = { ...item, id: index })),
        total: total,
        loading: false,
      },
    }),
    clearInventoryAnalyticsList: state => {
      state.inventoryAnalyticsPage = INITIAL_STATE.inventoryAnalyticsPage;
    },
    setInventoryAnalyticsPageCondition: (state, { payload }) => ({
      ...state,
      inventoryAnalyticsPageCondition: payload,
    }),
    setInventoryAnalyticsCount: (state, { payload }) => ({
      ...state,
      countList: payload,
    }),
    updateSearchValues(state, { payload }) {
      state.searchValues = {
        ...state.searchValues,
        ...payload,
      };
    },
  },
});

export const INVENTORY_ANALYSTICS_SLICE_NAME = inventoryAnalyticsSlice.name;
export const inventoryAnalyticsActions = inventoryAnalyticsSlice.actions;
export default inventoryAnalyticsSlice.reducer;
