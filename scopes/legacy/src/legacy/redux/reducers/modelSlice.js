import { createSlice } from '@reduxjs/toolkit';

const INITIAL_STATE = {
  loading: true,
  modelPage: {
    list: [],
    total: '',
  },
  pagination: {
    pageNum: 1,
    pageSize: 10,
  },
  searchValues: {},
  modelDetail: {},
  viewModelVisible: false,
  normalizedList: null,
};

const modelSlice = createSlice({
  name: 'modelManage',
  initialState: INITIAL_STATE,
  reducers: {
    request: state => {
      state.loading = true;
    },
    failure: state => {
      state.loading = false;
    },
    fetchModelListSuccess: (state, { payload: { total, data } }) => ({
      ...state,
      loading: false,
      modelPage: {
        list: data,
        total: total,
      },
    }),
    viewModelVisible: state => ({
      ...state,
      viewModelVisible: !state.viewModelVisible,
    }),
    setModelPagination(state, { payload }) {
      state.pagination = payload;
    },
    resetModelPpagination(state) {
      state.pagination = INITIAL_STATE.pagination;
    },
    saveModelDetailSuccess: (state, { payload }) => ({
      ...state,
      loading: false,
      modelDetail: payload,
    }),
    updateSearchValues(state, { payload }) {
      state.searchValues = {
        ...state.searchValues,
        ...payload,
      };
    },
    initializeSearchValuesAndConditions: (state, { payload: { searchValues } }) => {
      state.searchValues = searchValues;
      state.pagination = INITIAL_STATE.pagination;
    },
    setDeviceNormalizedList: (state, { payload }) => {
      state.normalizedList = payload;
    },
    resetSearchValues(state) {
      state.searchValues = {};
    },
  },
});

export const MODEL_SLICE_NAME = modelSlice.name;
export const modelActions = modelSlice.actions;
export default modelSlice.reducer;
