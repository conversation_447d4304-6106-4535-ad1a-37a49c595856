import { createSlice } from '@reduxjs/toolkit';

import { ConfigRangeType } from '@manyun/ticket.model.task';

// 维护工单配置
const maintain = {
  loading: false,
  pagination: {
    pageNum: 1,
    pageSize: 10,
  },
  data: [],
  total: 0,
  searchValues: {
    configName: {
      value: null,
    },
    maintenanceType: {
      value: null,
    },
    deviceType: {
      value: null,
    },
    createTimeRange: {
      value: [],
    },
    creatorId: {
      value: undefined,
    },
  },
  create: {
    effectType: {
      value: ConfigRangeType.Block,
    },
    configName: {
      value: undefined,
    },
    maintenanceType: {
      value: undefined,
    },
    deviceTypeList: {
      value: undefined,
    },
  },
  createSubBtnloading: false,
  toolTypes: [{ toolCode: 'others', toolName: '其他' }],
  tools: {
    tableData: [],
    editingRowKey: null,
    deleteByCancel: false,
  },
  securityStds: {
    tableData: [],
    editingRowKey: null,
    deleteByCancel: false,
  },
  maintenanceItems: {
    tableData: [],
    editingRowKey: null,
    deleteByCancel: false,
  },
  detail: {},
  edit: {
    configName: {
      value: undefined,
    },
    maintenanceType: {
      value: undefined,
    },
    deviceTypeList: {
      value: undefined,
    },
    tools: {
      tableData: [],
      editingRowKey: null,
      deleteByCancel: false,
    },
    securityStds: {
      tableData: [],
      editingRowKey: null,
      deleteByCancel: false,
    },
    maintenanceItems: {
      tableData: [],
      editingRowKey: null,
      deleteByCancel: false,
    },
  },
};

const initialState = {
  maintain,
};

const ticketConfigSlice = createSlice({
  name: 'ticketConfig',
  initialState,
  reducers: {
    failure(state) {
      state.maintain.loading = false;
    },
    updateMaintainSearchValues(state, { payload }) {
      state.maintain.searchValues = {
        ...state.maintain.searchValues,
        ...payload,
      };
    },
    updateMaintainTableLoading(state, { payload }) {
      state.maintain.loading = payload;
    },
    resetMaintainPageNum(state) {
      state.maintain.pagination.pageNum = 1;
    },
    setMaintainDataAndTotal(state, { payload: { total, data } }) {
      state.maintain.data = data;
      state.maintain.total = total;
      state.maintain.loading = false;
    },
    setMaintainPagination(state, { payload }) {
      state.maintain.pagination = payload;
    },
    upDateMaintainCreateOptionValues(state, { payload }) {
      state.maintain.create = {
        ...state.maintain.create,
        ...payload,
      };
    },
    setToolTypes(state, { payload }) {
      state.maintain.toolTypes = [...payload, { toolCode: 'others', toolName: '其他' }];
    },
    setMaintenanceConfigInfos(
      state,
      { payload: { basic, tools, securityStds, maintenanceItems } }
    ) {
      state.maintain = {
        ...state.maintain,
        detail: basic ? basic : state.maintain.detail,
        tools: {
          tableData: tools,
          editingRowKey: null,
          deleteByCancel: false,
        },
        securityStds: {
          tableData: securityStds,
          editingRowKey: null,
          deleteByCancel: false,
        },
        maintenanceItems: {
          tableData: maintenanceItems,
          editingRowKey: null,
          deleteByCancel: false,
        },
      };
    },
    resetMaintenanceConfig(state) {
      state.maintain = {
        ...state.maintain,
        tools: initialState.maintain.tools,
        securityStds: initialState.maintain.securityStds,
        maintenanceItems: initialState.maintain.maintenanceItems,
      };
    },
    upDateMaintenanceEditOptionValues(
      state,
      { payload: { basic, tools, securityStds, maintenanceItems } }
    ) {
      state.maintain.edit = {
        ...state.maintain.edit,
        ...basic,
        tools,
        securityStds: securityStds,
        maintenanceItems,
      };
      state.maintain.tools = tools;
      state.maintain.securityStds = securityStds;
      state.maintain.maintenanceItems = maintenanceItems;
    },
    upDateEditOptionValues(state, { payload }) {
      state.maintain.edit = {
        ...state.maintain.edit,
        ...payload,
      };
    },
    resetMaintanceSearchValuesAndData(state) {
      state.maintain = {
        ...state.maintain,
        searchValues: initialState.maintain.searchValues,
        pagination: initialState.maintain.pagination,
        data: initialState.maintain.data,
        total: initialState.maintain.total,
        tools: initialState.maintain.tools,
        securityStds: initialState.maintain.securityStds,
        maintenanceItems: initialState.maintain.maintenanceItems,
      };
    },
    setMaintainTable(state, { payload }) {
      state.maintain = {
        ...state.maintain,
        ...payload,
      };
    },
    resetMaintainPageData(state) {
      state.maintain.pagination = initialState.maintain.pagination;
      state.maintain.data = initialState.maintain.data;
      state.maintain.total = initialState.maintain.total;
      state.maintain.loading = initialState.maintain.loading;
      state.maintain.searchValues = initialState.maintain.searchValues;
    },
  },
});

export const TICKET_CONFIG_SLICE_NAME = ticketConfigSlice.name;
export const ticketConfigActions = ticketConfigSlice.actions;
export default ticketConfigSlice.reducer;
