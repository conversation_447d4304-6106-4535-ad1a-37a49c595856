import { createSlice } from '@reduxjs/toolkit';

const INITIAL_STATE = {
  insideNoticeList: {
    loading: false,
    data: [],
    total: 0,
  },
  pagination: {
    pageNum: 1,
    pageSize: 10,
  },
  insideMsgPType: '',
  isRead: '0',
  searchValues: {},
  waitNum: 0,
};

const insideNoticeSlice = createSlice({
  name: 'insideNotice',
  initialState: INITIAL_STATE,
  reducers: {
    fetchInsideNoticeListError: state => {
      state.insideNoticeList = {
        fetching: false,
        data: [],
        total: 0,
      };
    },
    failure: state => {
      state.insideNoticeList.loading = false;
    },
    setPagination(state, { payload }) {
      state.insideNoticeList.pagination = payload;
    },
    resetPageNum(state) {
      state.insideNoticeList.pagination = { pageNum: 1, pageSize: 10 };
    },
    changePtype: (state, { payload }) => {
      state.insideMsgPType = payload;
    },
    changeReadFlag: (state, { payload }) => {
      state.isRead = payload;
    },
    setInsideNoticeList: (state, { payload }) => {
      state.insideNoticeList = {
        ...payload,
        loading: false,
      };
    },
    updateSearchValues(state, { payload }) {
      state.searchValues = {
        ...state.searchValues,
        ...payload,
      };
    },
    updatePagination(state, { payload }) {
      state.pagination = payload;
    },
    resetPagination(state) {
      state.pagination = INITIAL_STATE.pagination;
    },
    updateTableLoading(state, { payload }) {
      state.insideNoticeList.loading = payload;
    },
    updateWaitNum(state, { payload }) {
      state.waitNum = payload;
    },
  },
});

export const INSIDE_NOTICE_SLICE_NAME = insideNoticeSlice.name;
export const insideNoticeActions = insideNoticeSlice.actions;
export default insideNoticeSlice.reducer;
