import { createSlice } from '@reduxjs/toolkit';

const INITIAL_STATE = {
  workdayList: {
    fetching: false,
    data: [],
    total: 0,
  },
};

const workdayManageSlice = createSlice({
  name: 'workday_manage',
  initialState: INITIAL_STATE,
  reducers: {
    fetchWorkdatListStart: state => {
      state.workdayList.fetching = true;
    },
    fetchWorkdatListError: state => {
      state.workdayList = {
        fetching: false,
        data: [],
        total: 0,
      };
    },
    setWorkdatList: (state, { payload }) => {
      state.workdayList = {
        ...payload,
        fetching: false,
      };
    },
  },
});

export const WORKDAY_SLICE_NAME = workdayManageSlice.name;
export const workdayManageActions = workdayManageSlice.actions;
export default workdayManageSlice.reducer;
