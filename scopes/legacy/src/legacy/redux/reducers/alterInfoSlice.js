import { createSlice } from '@reduxjs/toolkit';
import mergeWith from 'lodash.mergewith';
import shortid from 'shortid';

import { ALTER_TYPE_KEY_MAP } from '@manyun/dc-brain.legacy.pages/alter-info/constants';

const INITIAL_STATE = {
  loading: false,
  pagination: {
    pageNum: 1,
    pageSize: 10,
  },
  data: [],
  total: 0,
  selectedRowKeys: [],
  searchValues: {
    // 业务id
    bizId: {},
    //流程状态
    bizStatus: {},

    // 调班类型
    alterType: {},

    // 资源编码
    resourceCode: {},
    creatorId: {},

    // 申请开始时间
    applyStartTime: {},
    //申请结束时间
    applyEndTime: {},
    /**销假状态 */
    rollBackAlterStatusList: {},
  },
  basicAlterInfo: {
    alterInfo: {
      alterType: null,
      bizId: null,
      startTime: null,
      endTime: null,
      finishTime: null,
      totalTime: null,
      applyStaffId: 1,
      applyStaffName: null,
      blockTag: null,
      applyReason: null,
      bizStatus: null,
      gmtCreate: null,
      gmtModified: null,
      creatorId: 1,
      creatorName: 'admin',
      alterDetail: [],
      files: null,
      operationRecords: [],
    },
    fileDetails: null,
  },
  create: {
    applyStaffId: {
      name: 'applyStaffId',
      warnings: [],
      errors: [],
      touched: false,
      validating: false,
      value: undefined,
    },
    alterType: { value: ALTER_TYPE_KEY_MAP.REST },
    leaveType: { value: undefined },
    leaveReason: { value: undefined },
    files: { value: [] },
    leaveInfoList: {
      name: 'leaveInfoList',
      warnings: [],
      errors: [],
      touched: false,
      validating: false,
      value: [
        {
          id: shortid.generate(),
          scheduleDate: null,
          dutyId: undefined,
          replaceStaffId: undefined,
        },
      ],
    },
    exchangeInfo: {
      scheduleDate: null,
      dutyId: undefined,
      replaceStaffId: undefined,
    },
    exchangeReturnInfo: {
      scheduleDate: null,
      dutyId: undefined,
    },
    applyScheduleDate: { value: undefined },
    applyDutyId: { value: undefined },
    targetStaffId: { value: undefined },
    targetScheduleDate: { value: undefined },
    targetDutyId: { value: undefined },
    exchangeReason: { value: undefined },
  },
};

const alterInfoSlice = createSlice({
  name: 'alterInfo',
  initialState: INITIAL_STATE,
  reducers: {
    failure: state => ({
      ...state,
      loading: false,
    }),
    resetStatusCanceled(state) {
      state.basicAlterInfo.bizStatus = 'CANCELED';
    },
    fetchArticlesStart: state => {
      state.loading = true;
    },
    resetPageNum(state) {
      state.pagination.pageNum = 1;
    },
    setArticles: (state, { payload: { data, total } }) => {
      state.data = data;
      state.total = total;
      state.loading = false;
    },
    setSelectedRowKeys(state, { payload }) {
      state.selectedRowKeys = payload;
    },
    setRecordPagination(state, { payload }) {
      state.pagination = { ...state.pagination, ...payload };
    },
    updateSearchValues(state, { payload }) {
      state.searchValues = {
        ...state.searchValues,
        ...payload,
      };
    },
    setTaskBaseInfo(state, { payload }) {
      state.detail = {
        ...state.detail,
        ...payload,
      };
    },
    setAlterBasicInfo(state, { payload }) {
      state.basicAlterInfo.alterInfo = payload;
    },

    setAlterFiles(state, { payload }) {
      state.basicAlterInfo.fileDetails = payload.data;
    },
    setTaskEditOptions(state, { payload }) {
      state.edit = payload;
      state.selectedRowKeys = payload.jobItems.value;
    },
    upDateCreateFieldsValue(state, { payload }) {
      Object.keys(payload).forEach(name => {
        const pendingUpdate = mergeWith(state.create[name], payload[name], (oldValue, newValue) => {
          if (Array.isArray(oldValue)) {
            return newValue;
          }
        });
        state.create[name] = pendingUpdate;
      });
    },
    resetCreateValues(state) {
      state.create = { ...INITIAL_STATE.create };
    },

    resetSearchValuesAndPagination(state) {
      state.searchValues = { ...INITIAL_STATE.searchValues };
      state.pagination = { ...INITIAL_STATE.pagination };
    },
  },
});

export const ALTER_INFO_SLICE_NAME = alterInfoSlice.name;
export const alterInfoActions = alterInfoSlice.actions;
export default alterInfoSlice.reducer;
