import { createSlice } from '@reduxjs/toolkit';

const INITIAL_STATE = {
  idcList: {
    list: [],
    total: 0,
    pageNum: 1,
    pageSize: 10,
    loading: true,
  },
  metaList: [],
  createLoading: false,
  operationStatusList: [],
  selectLoading: false,
  areaTreeMetaQuery: {},
  searchValues: {},
};

const basicResourcesIdcSlice = createSlice({
  name: 'basicResourcesIdc',
  initialState: INITIAL_STATE,
  reducers: {
    request: state => ({ ...state, idcList: { ...state.idcList, loading: true } }),
    success: (state, { payload }) => ({
      ...state,
      idcList: { ...payload },
    }),
    failure: state => ({
      ...state,
      createLoading: false,
      idcList: {
        ...state.idcList,
        loading: false,
      },
    }),
    metaSuccess: (state, { payload }) => ({
      ...state,
      metaList: payload,
    }),
    onCreateLoading: state => ({
      ...state,
      createLoading: !state.createLoading,
    }),
    operationStatusSuccess: (state, { payload }) => ({
      ...state,
      operationStatusList: payload,
    }),
    onSelectLoading: state => ({
      ...state,
      selectLoading: !state.selectLoading,
    }),
    areaTreeMetaQuery: (state, { payload }) => ({
      ...state,
      areaTreeMetaQuery: payload,
    }),
    updateSearchValues(state, { payload }) {
      state.searchValues = {
        ...state.searchValues,
        ...payload,
      };
    },
    resetSearchValues(state, { payload }) {
      state.searchValues = {};
    },
  },
});

export const BASIC_RESOURCES_IDC_SLICE_NAME = basicResourcesIdcSlice.name;
export const basicResourcesIdcActions = basicResourcesIdcSlice.actions;
export default basicResourcesIdcSlice.reducer;
