import { createSlice } from '@reduxjs/toolkit';

const INITIAL_STATE = {
  loading: true,
  noticePage: {
    list: [],
    total: '',
  },
  noticePageCondition: {
    status: 'ALL',
    published: true,
  },
  noticeVisible: false,
  noticeMess: {},
  noticeTreeSeclect: [],
  noticeDetail: {},
};
const noticeSlice = createSlice({
  name: 'noticeManage',
  initialState: INITIAL_STATE,
  reducers: {
    request: state => {
      state.loading = true;
    },
    success: (state, { payload: { total, data } }) => ({
      ...state,
      loading: false,
      noticePage: {
        list: data,
        total: total,
      },
    }),
    failure: state => {
      state.loading = false;
    },
    getNoticeListSuccess: (state, { payload: { total, data } }) => {
      return {
        ...state,
        noticePage: {
          list: data,
          total: total,
        },
        loading: false,
      };
    },
    changeNoticeVisible: state => ({
      ...state,
      noticeVisible: !state.noticeVisible,
    }),

    editNoticeMess: (state, { payload }) => ({
      ...state,
      noticeMess: payload,
    }),

    fetchNoticeTreeSeclectSuccess: (state, { payload }) => ({
      ...state,
      noticeTreeSeclect: payload,
    }),

    saveSearchCondition: (state, { payload }) => ({
      ...state,
      noticePageCondition: payload,
    }),

    saveNoticeDetailSuccess: (state, { payload }) => ({
      ...state,
      noticeDetail: payload,
    }),
  },
});

export const NOTICE_MANAGE_SLICE_NAME = noticeSlice.name;
export const noticeActions = noticeSlice.actions;
export default noticeSlice.reducer;
