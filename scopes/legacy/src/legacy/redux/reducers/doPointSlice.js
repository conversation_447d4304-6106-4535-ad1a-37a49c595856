import { createSlice } from '@reduxjs/toolkit';

const INITIAL_STATE = {
  // 请求加载中
  loading: false,
  pointDetail: {
    data: [],
    total: '',
  },
  surePointDetail: {
    data: [],
    total: '',
  },
  addPointVisible: false,
  deviceType: '',
  editMess: {},
  editVisible: false,
  categoryList: [],
  categoryNum: {},
  selectCategoryPoint: {},
  viewVisible: false,
  viewList: [],
  integratedData: [],
  expandedKey: [],
};
const doPointSlice = createSlice({
  name: 'doPointManage',
  initialState: INITIAL_STATE,
  reducers: {
    request: state => ({ ...state, loading: true }),
    failure: state => ({
      ...state,
      loading: false,
      addPointVisible: false,
    }),
    featchPointDetailSuccess: (state, { payload: { total, data } }) => ({
      ...state,
      loading: false,
      pointDetail: {
        data: data,
        total: total,
      },
    }),
    savePointListData: (state, { payload: { total, data } }) => ({
      ...state,
      loading: false,
      surePointDetail: {
        data: data,
        total: total,
      },
    }),
    addPointVisible: state => ({
      ...state,
      addPointVisible: !state.addPointVisible,
    }),
    saveType: (state, { payload }) => ({
      ...state,
      deviceType: payload.code,
    }),
    editMess: (state, { payload }) => ({
      ...state,
      editMess: payload,
    }),

    editVisible: state => ({
      ...state,
      editVisible: !state.editVisible,
    }),
    fetcgMetaCategory: (state, { payload }) => ({
      ...state,
      categoryList: payload,
    }),
    saveExpandedKey: (state, { payload }) => ({
      ...state,
      expandedKey: payload,
    }),
    fetcgMetaCategoryNumSuccess: (state, { payload }) => ({
      ...state,
      categoryNum: payload,
    }),
    selectCategoryPoint(state, { payload }) {
      state.selectCategoryPoint = payload ? { ...state.selectCategoryPoint, ...payload } : {};
    },
    ViewVisible: (state, { payload }) => ({
      ...state,
      viewVisible: !state.viewVisible,
      viewList: payload.val,
    }),
    separateData: (state, { payload }) => ({
      ...state,
      integratedData: payload,
    }),
  },
});

export const DOPOINT_MANAGE_SLICE_NAME = doPointSlice.name;
export const doPointActions = doPointSlice.actions;
export default doPointSlice.reducer;
