import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  mode: undefined,
  searchValues: {},
  data: [],
  total: 0,
  pagination: {
    pageNum: 1,
    pageSize: 10,
    sortField: undefined,
    sortOrder: undefined,
  },
  selectedRowKeys: [],
  loading: false,
};

const visitorRecordSlice = createSlice({
  name: 'visitorRecord',
  initialState: initialState,
  reducers: {
    request: state => ({ ...state, loading: true }),
    updateSearchValues(state, { payload }) {
      state.searchValues = {
        ...state.searchValues,
        ...payload,
      };
    },
    resetSearchValuesAndPagination(state) {
      state.searchValues = {};
      state.pagination = { ...initialState.pagination };
    },
    setPagination(state, { payload: { pageNum, pageSize, mode, sortOrder, sortField } }) {
      if (pageNum !== undefined) {
        state.pagination.pageNum = pageNum;
      }
      if (pageSize !== undefined) {
        state.pagination.pageSize = pageSize;
      }
      state.pagination.sortOrder = sortOrder;
      state.pagination.sortField = sortField;
      state.mode = mode;
    },
    setDataAndTotal(state, { payload: { data, total } }) {
      state.data = data;
      state.total = total;
      state.loading = false;
    },
    resetPageNum({ pagination }) {
      pagination.pageNum = 1;
    },
    setSelectedRowKeys(state, { payload }) {
      state.selectedRowKeys = payload;
    },
    resetSelectedRowKeys(state) {
      state.selectedRowKeys = [];
    },
  },
});

export const VISITOR_RECORD_SLICE_NAME = visitorRecordSlice.name;
export const visitorRecordActions = visitorRecordSlice.actions;
export default visitorRecordSlice.reducer;
