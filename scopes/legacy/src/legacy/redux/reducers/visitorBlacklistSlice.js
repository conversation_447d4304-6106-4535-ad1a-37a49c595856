import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  searchValues: {},
  data: [],
  total: 0,
  pagination: {
    pageNum: 1,
    pageSize: 10,
  },
  selectedRowKeys: [],
  loading: false,
};

const visitorBlacklistSlice = createSlice({
  name: 'visitorBlacklist',
  initialState: initialState,
  reducers: {
    request: state => ({ ...state, loading: true }),
    updateSearchValues(state, { payload }) {
      state.searchValues = {
        ...state.searchValues,
        ...payload,
      };
    },
    resetSearchValuesAndPagination(state) {
      state.searchValues = {};
      state.pagination = { ...initialState.pagination };
    },
    setPagination(state, { payload }) {
      state.pagination = payload;
    },
    setDataAndTotal(state, { payload: { data, total } }) {
      state.data = data;
      state.total = total;
      state.loading = false;
    },
    resetPageNum({ pagination }) {
      pagination.pageNum = 1;
    },
    setSelectedRowKeys(state, { payload }) {
      state.selectedRowKeys = payload;
    },
    resetSelectedRowKeys(state) {
      state.selectedRowKeys = [];
    },
  },
});

export const VISITOR_BLACKLIST_SLICE_NAME = visitorBlacklistSlice.name;
export const visitorBlacklistActions = visitorBlacklistSlice.actions;
export default visitorBlacklistSlice.reducer;
