import { createSlice } from '@reduxjs/toolkit';
import cloneDeep from 'lodash/cloneDeep';

const templateNewEdit = {
  // 当前步骤索引
  submitLoading: false,
  saveLoading: false,
  step: 0,
  customizeUploadFileLoading: {},
  step1: {
    templateName: {},
    changeType: {},
    riskLevel: {},
    executeRole: {},
    workflowId: null,
    changeTemplateId: null,
    fileInfoList: { value: [] },
  },
  step2: {
    stepCodes: [], //'TQRqFy5z7', 'TQRqFy5z71'
    stepMaps: {},
  },
  checkItemsMap: null,
};

const templateList = {
  loading: false,
  pagination: {
    pageNum: 1,
    pageSize: 10,
  },
  searchValues: {
    // 模板 ID
    templateId: {
      value: null,
    },
    changeType: {
      value: null,
    },
    changeLevelList: {
      value: undefined,
    },
    templateName: {
      value: null,
    },
    creatorId: {
      value: undefined,
    },
    modifiedTime: {
      value: null,
    },
  },
  data: [],
  total: 0,
};

const ticketList = {
  loading: false,
  pagination: {
    pageNum: 1,
    pageSize: 10,
  },
  searchValues: {
    changeOrderId: {
      value: null,
    },
    location: {
      value: null,
    },
    changeReasonList: {
      value: undefined,
    },
    changeType: {
      value: null,
    },
    creatorId: {
      value: undefined,
    },
    planTime: {
      value: null,
    },
    realTime: {
      value: null,
    },
    statusList: {
      value: undefined,
    },
    changeLevelList: {
      value: undefined,
    },
    title: {
      value: null,
    },
  },
};

const ticketNewEdit = {
  // 当前步骤索引
  submitLoading: false,
  saveLoading: false,
  step: 0,
  customizeUploadFileLoading: {},
  step1: {
    title: {},
    location: { value: [] },
    changeType: { value: { key: '', label: '' } },
    riskLevel: {},
    planStartTime: {},
    planEndTime: {},
    source: {},
    templateId: { value: { key: '', label: '' } },
    templateName: null,
    changeReason: {},
    changeOrderExtJson: { statusInfoList: [] },
    fileInfoList: { value: [] },
    reason: { value: { key: '', label: '' } },
  },
  result: {
    changeReason: null,
    id: null,
    idcTag: null,
    planEndTime: null,
    planStartTime: null,
    riskLevel: null,
    templateCode: null,
    title: null,
    reason: null,
    workflowId: null,
  },
  step2: {
    stepCodes: [], //'TQRqFy5z7', 'TQRqFy5z71'
    stepMaps: {},
  },
  checkItemsMap: null,
  checkItemDeviceMaps: {},
  matchObjectInfoMaps: {},
  batchSettingCheckedPoint: {},
};

const initialState = {
  templateNew: { id: null, ...cloneDeep(templateNewEdit) },
  templateEdit: {
    ...cloneDeep(templateNewEdit),
  },
  templateDetail: {
    ...cloneDeep(templateNewEdit),
    step1: {
      templateId: null,
      templateName: null,
      changeType: null,
      riskLevel: null,
      executeRole: null,
      workflowId: null,
    },
  },
  templateList,
  ticketList,
  ticketNew: cloneDeep(ticketNewEdit),
  ticketEdit: cloneDeep(ticketNewEdit),
  ticketDetail: {
    changeInfo: cloneDeep(ticketNewEdit),
    alarm: {
      categoryDisplayBarList: [],
      list: [],
      total: 0,
      pageSize: 10,
      pageNum: 1,
    },
    step: {
      stepOrder: 0,
      stepList: [],
      checkItemInfoMaps: {},
      opItemInfoMaps: {},
      pointValue: { pointsData: {} },
      pointsDefinition: [],
      selectedCheckItemInfoMaps: [],
      deviceGuids: [],
    },
    tabCurrent: 'ticketInfo',
  },
};

const changeSlice = createSlice({
  name: 'change',
  initialState,
  reducers: {
    setTemplateNewStep(state, { payload }) {
      state.templateNew.step = payload;
    },
    setTemplateEditStep(state, { payload }) {
      state.templateEdit.step = payload;
    },
    setTemplateNewStep1Fields(state, { payload }) {
      state.templateNew.step1 = {
        ...state.templateNew.step1,
        ...payload,
      };
    },
    setTemplateEditStep1Fields(state, { payload }) {
      state.templateEdit.step1 = {
        ...state.templateEdit.step1,
        ...payload,
      };
    },
    setTemplateNewStep2Fields(state, { payload }) {
      state.templateNew.step2 = {
        ...state.templateNew.step2,
        ...payload,
      };
    },
    setTicketNewStep2Fields(state, { payload }) {
      state.ticketNew.step2 = {
        ...state.ticketNew.step2,
        ...payload,
      };
    },
    setTicketEditStep2Fields(state, { payload }) {
      state.ticketEdit.step2 = {
        ...state.ticketEdit.step2,
        ...payload,
      };
    },
    setTemlateNewCheckItemsMap(state, { payload }) {
      state.templateNew.checkItemsMap = {
        ...state.templateNew.checkItemsMap,
        ...payload,
      };
    },
    setTicketNewCheckItemsMap(state, { payload }) {
      state.ticketNew.checkItemsMap = {
        ...state.ticketNew.checkItemsMap,
        ...payload,
      };
    },
    setTicketEditCheckItemsMap(state, { payload }) {
      state.ticketEdit.checkItemsMap = {
        ...state.ticketEdit.checkItemsMap,
        ...payload,
      };
    },
    setTemplateEditStep2Fields(state, { payload }) {
      state.templateEdit.step2 = {
        ...state.templateEdit.step2,
        ...payload,
      };
    },
    setTemlateEditCheckItemsMap(state, { payload }) {
      state.templateEdit.checkItemsMap = {
        ...state.templateEdit.checkItemsMap,
        ...payload,
      };
    },
    failure(state) {
      state.ticketList.loading = false;
      state.templateList.loading = false;
    },
    updateSearchValues(state, { payload }) {
      state.templateList.searchValues = {
        ...state.templateList.searchValues,
        ...payload,
      };
      state.templateList.loading = false;
    },
    resetSearchValuesAndPagination(state) {
      state.templateList.searchValues = {};
      state.templateList.pagination = { ...initialState.templateList.pagination };
    },
    resetPageNum(state) {
      state.templateList.pagination.pageNum = 1;
    },
    setDataAndTotal(state, { payload: { data, total } }) {
      state.templateList.data = data;
      state.templateList.total = total;
      state.templateList.loading = false;
    },
    setPagination(state, { payload }) {
      state.templateList.pagination = payload;
    },
    startTPLTableLoading(state) {
      state.templateList.loading = true;
    },

    updateTicketSearchValues(state, { payload }) {
      state.ticketList.searchValues = {
        ...state.ticketList.searchValues,
        ...payload,
      };
      state.ticketList.loading = false;
    },
    setTicketDataAndTotal(state, { payload: { data, total } }) {
      state.ticketList.data = data;
      state.ticketList.total = total;
      state.ticketList.loading = false;
    },
    resetTicketSearchValuesAndPagination(state) {
      state.ticketList.searchValues = {};
      state.ticketList.pagination = { ...initialState.ticketList.pagination };
    },
    resetTicketPageNum(state) {
      state.ticketList.pagination.pageNum = 1;
    },
    setTicketPagination(state, { payload }) {
      state.ticketList.pagination = payload;
    },
    startTicketTableLoading(state) {
      state.ticketList.loading = true;
    },
    setTicketNewStep(state, { payload }) {
      state.ticketNew.step = payload;
    },
    setTicketEditStep(state, { payload }) {
      state.ticketEdit.step = payload;
    },
    setTicketNewStep1Fields(state, { payload }) {
      state.ticketNew.step1 = {
        ...state.ticketNew.step1,
        ...payload,
      };
    },
    setTicketEditStep1Fields(state, { payload }) {
      state.ticketEdit.step1 = {
        ...state.ticketEdit.step1,
        ...payload,
      };
    },
    templateNewSaveLoading(state) {
      state.templateNew = {
        ...state.templateNew,
        saveLoading: !state.templateNew.saveLoading,
      };
    },
    templateNewSubmitLoading(state) {
      state.templateNew = {
        ...state.templateNew,
        submitLoading: !state.templateNew.submitLoading,
      };
    },
    templateEditSaveLoading(state) {
      state.templateEdit = {
        ...state.templateEdit,
        saveLoading: !state.templateEdit.saveLoading,
      };
    },
    templateEditSubmitLoading(state) {
      state.templateEdit = {
        ...state.templateEdit,
        submitLoading: !state.templateEdit.submitLoading,
      };
    },
    templateSaveNewId(state, { payload }) {
      state.templateNew.step1 = {
        ...state.templateNew.step1,
        templateId: payload.changeTemplateId,
        workflowId: payload.workflowId,
      };
    },
    templateSaveEditId(state, { payload }) {
      state.templateEdit.step1 = {
        ...state.templateEdit.step1,
        templateId: payload.changeTemplateId,
        workflowId: payload.workflowId,
      };
    },
    setTemplateDetail(state, { payload }) {
      state.templateDetail = {
        ...state.templateDetail,
        ...payload,
      };
    },
    setTemplateEditInfo(state, { payload }) {
      state.templateEdit = {
        ...state.templateEdit,
        ...payload,
      };
    },
    ticketNewSaveLoading(state) {
      state.ticketNew = {
        ...state.ticketNew,
        saveLoading: !state.ticketNew.saveLoading,
      };
    },
    ticketNewSubmitLoading(state) {
      state.ticketNew = {
        ...state.ticketNew,
        submitLoading: !state.ticketNew.submitLoading,
      };
    },
    ticketEditSaveLoading(state) {
      state.ticketEdit = {
        ...state.ticketEdit,
        saveLoading: !state.ticketEdit.saveLoading,
      };
    },
    ticketEditSubmitLoading(state) {
      state.ticketEdit = {
        ...state.ticketEdit,
        submitLoading: !state.ticketEdit.submitLoading,
      };
    },
    ticketSaveNewId(state, { payload }) {
      state.ticketNew = {
        ...state.ticketNew,
        id: payload,
      };
    },
    setTicketDetailChangeInfo(state, { payload }) {
      state.ticketDetail.changeInfo = {
        ...state.ticketDetail.changeInfo,
        ...payload,
      };
    },
    setTicketEditInfo(state, { payload }) {
      state.ticketEdit = {
        ...state.ticketEdit,
        ...payload,
      };
    },
    setTicketNewInfo(state, { payload }) {
      state.ticketNew = {
        ...state.ticketNew,
        ...payload,
      };
    },
    setTemplateDetailInfo(state, { payload }) {
      state.templateDetail = {
        ...state.templateDetail,
        ...payload,
      };
    },
    setTicketNewMatchObjectInfoMaps(state, { payload }) {
      state.ticketNew.matchObjectInfoMaps = {
        ...state.ticketNew.matchObjectInfoMaps,
        ...payload,
      };
    },
    setTicketEditMatchObjectInfoMaps(state, { payload }) {
      state.ticketEdit.matchObjectInfoMaps = {
        ...state.ticketEdit.matchObjectInfoMaps,
        ...payload,
      };
    },
    resetTicketEditMaps(state) {
      state.ticketEdit = {
        ...state.ticketEdit,
        matchObjectInfoMaps: {},
        checkItemDeviceMaps: {},
      };
    },
    resetTicketNewMaps(state) {
      state.ticketNew = {
        ...state.ticketNew,
        step1: {
          ...state.ticketNew.step1,
          exeUserGroupCode: { value: undefined },
        },
        matchObjectInfoMaps: {},
        checkItemDeviceMaps: {},
      };
    },
    setTicketNewCheckItepDeviceMaps(state, { payload }) {
      state.ticketNew.checkItemDeviceMaps = {
        ...state.ticketNew.checkItemDeviceMaps,
        ...payload,
      };
    },
    setTicketEditCheckItepDeviceMaps(state, { payload }) {
      state.ticketEdit.checkItemDeviceMaps = {
        ...state.ticketEdit.checkItemDeviceMaps,
        ...payload,
      };
    },
    setTicketEditResult(state, { payload }) {
      state.ticketEdit.result = {
        ...state.ticketEdit.result,
        ...payload,
      };
    },
    setTicketNewResult(state, { payload }) {
      state.ticketNew.result = {
        ...state.ticketNew.result,
        ...payload,
      };
    },
    setTicketDetailAlarmList(state, { payload }) {
      state.ticketDetail.alarm = {
        ...state.ticketDetail.alarm,
        ...payload,
      };
    },
    setAlarmLevelCount(state, { payload }) {
      state.ticketDetail.alarm.categoryDisplayBarList = payload;
    },
    setTicketStepDetail(state, { payload }) {
      state.ticketDetail.step = {
        ...state.ticketDetail.step,
        ...payload,
      };
    },
    setTicketStepDetailPointValue(state, { payload }) {
      state.ticketDetail.step.pointValue = payload;
    },
    setTicketInfo(state, { payload }) {
      state.ticketNew = payload;
    },
    setTicketStepItemStatus(state, { payload }) {
      state.ticketDetail.step = {
        ...state.ticketDetail.step,
        ...payload,
      };
    },
    setTemplateEditStepCodes(state, { payload }) {
      state.templateEdit.step2 = {
        ...state.templateEdit.step2,
        ...payload,
      };
    },
    setTemplateNewStepCodes(state, { payload }) {
      state.templateNew.step2 = {
        ...state.templateNew.step2,
        ...payload,
      };
    },
    setTicketNewStepCodes(state, { payload }) {
      state.ticketNew.step2 = {
        ...state.ticketNew.step2,
        ...payload,
      };
    },
    setTicketEditStepCodes(state, { payload }) {
      state.ticketEdit.step2 = {
        ...state.ticketEdit.step2,
        ...payload,
      };
    },
    setTicketViewStepOrder(state, { payload }) {
      state.ticketDetail.step.stepOrder = payload;
    },
    resetTemplateNewInfo(state) {
      state.templateNew = templateNewEdit;
    },
    resetTemplateEditInfo(state) {
      state.templateEdit = templateNewEdit;
    },
    setTabCurrent(state, { payload }) {
      state.ticketDetail.tabCurrent = payload;
    },
    setTicketStepCheckItemMaps(state, { payload }) {
      state.ticketDetail.step.selectedCheckItemInfoMaps = payload;
    },
    setTicketEditBatchSettingCheckedPoint(state, { payload }) {
      state.ticketEdit.batchSettingCheckedPoint = payload;
    },
    setTicketNewBatchSettingCheckedPoint(state, { payload }) {
      state.ticketNew.batchSettingCheckedPoint = payload;
    },
    setTemplateEditBatchSettingCheckedPoint(state, { payload }) {
      state.templateEdit.batchSettingCheckedPoint = payload;
    },
    setTemplateNewBatchSettingCheckedPoint(state, { payload }) {
      state.templateNew.batchSettingCheckedPoint = payload;
    },
    setTemplateCopyInfo(state, { payload }) {
      state.templateNew = { ...state.templateNew, ...payload };
    },
    setTemplateNewCustomizeUploadFileLoading(state, { payload }) {
      state.templateNew.customizeUploadFileLoading = payload;
    },
    setTicketNewCustomizeUploadFileLoading(state, { payload }) {
      state.ticketNew.customizeUploadFileLoading = payload;
    },
    setTemplateEditCustomizeUploadFileLoading(state, { payload }) {
      state.templateEdit.customizeUploadFileLoading = payload;
    },
    setTicketEditCustomizeUploadFileLoading(state, { payload }) {
      state.ticketEdit.customizeUploadFileLoading = payload;
    },
  },
});

export const CHANGE_SLICE_NAME = changeSlice.name;
export const changeActions = changeSlice.actions;
export default changeSlice.reducer;
