export const getSearchValuesAndPagination = ({
  change: {
    templateList: { searchValues, pagination },
  },
}) => ({ searchValues, pagination });

export const getTicketSearchValuesAndPagination = ({
  change: {
    ticketList: { searchValues, pagination },
  },
}) => ({ searchValues, pagination });

export const getTemplateNew = ({
  change: {
    templateNew: { step, step1, step2, checkItemsMap },
  },
}) => ({ step, step1, step2, checkItemsMap });

export const getTemplateEdit = ({
  change: {
    templateEdit: { step, step1, step2, checkItemsMap },
  },
}) => ({ step, step1, step2, checkItemsMap });

export const getTicketNew = ({
  change: {
    ticketNew: { step, step1, step2, checkItemsMap, checkItemDeviceMaps, matchObjectInfoMaps },
  },
}) => ({ step, step1, step2, checkItemsMap, checkItemDeviceMaps, matchObjectInfoMaps });

export const getTicketEdit = ({
  change: {
    ticketEdit: { step, step1, step2, checkItemsMap, checkItemDeviceMaps, matchObjectInfoMaps },
  },
}) => ({ step, step1, step2, checkItemsMap, checkItemDeviceMaps, matchObjectInfoMaps });

export const getTicketDetailAlarm = ({
  change: {
    ticketDetail: {
      changeInfo: { step1 },
    },
  },
}) => ({
  idcTag: step1.idcTag,
  blockTags: [step1.blockTag],
  // triggerTime: step1.realStartTime,
  // triggerTimeEnd: step1.realEndTime,
});

export const getDetailStepPointCode = ({
  change: {
    ticketDetail: {
      step: { stepOrder, checkItemInfoMaps, stepList, deviceGuids },
      changeInfo: {
        step1: { idcTag, blockTag },
      },
    },
  },
}) => ({
  stepOrder,
  checkItemInfoMaps,
  stepList,
  idcTag,
  blockTag,
  deviceGuids,
});

export const getOPCheckItems = ({
  change: {
    ticketDetail: {
      step: { stepOrder, checkItemInfoMaps, opItemInfoMaps, selectedCheckItemInfoMaps },
    },
  },
}) => ({
  stepOrder,
  checkItemInfoMaps,
  opItemInfoMaps,
  selectedCheckItemInfoMaps,
});

export const getCheckItemsStatus = ({
  change: {
    ticketDetail: {
      step: { lastCheckItemInfoList, lastOpItemInfoList },
    },
  },
}) => ({
  lastCheckItemInfoList,
  lastOpItemInfoList,
});

export const getTicketDetailStepCodes = ({
  change: {
    ticketDetail: {
      changeInfo: {
        step2: { stepCodes },
      },
    },
  },
}) => ({
  stepCodes,
});
