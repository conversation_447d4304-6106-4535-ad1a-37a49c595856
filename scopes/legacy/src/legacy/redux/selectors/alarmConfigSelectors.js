export const getDeviceInfoTreeData = ({ alarmManage: { doPointTree } }) => doPointTree;
export const getNotificationTpl = ({ alarmManage: { notificationTpl } }) => notificationTpl;
export const getMonitorItems = ({
  alarmManage: { parentMonitorItem, aggregatedMonitorItems },
}) => ({
  parent: parentMonitorItem,
  children: aggregatedMonitorItems,
});
export const getNotificationFields = ({
  alarmManage: {
    alarmCopyList: { physicalFields, faultedFields, otherFields },
  },
}) => [...physicalFields, ...faultedFields, ...otherFields];
export const getEdittingMonitorItem = ({ alarmManage: { edittingMonitorItem } }) =>
  edittingMonitorItem;
export const getMonitorItemConfigTreeData = ({ alarmManage: { monitorTreeData } }) =>
  monitorTreeData;
