let resourceCode = [];
export const getQ = ({ alterInfo: { pagination, searchValues }, user: { resourceCodes } }) => ({
  ...pagination,

  ...Object.keys(searchValues).reduce((map, key) => {
    // searchValues: { a: { value: 1 } } -> map: { a: 1 }
    if (key === 'resourceCode' && searchValues[key].value && searchValues[key].value.length) {
      if (searchValues[key].value.length > 1) {
        resourceCode = [
          ...resourceCode,
          searchValues[key].value[0] + '.' + searchValues[key].value[1],
        ];
        map['resourceCode'] = resourceCode;
      } else {
        resourceCode = resourceCodes.filter(
          code => code.includes(searchValues[key].value) && code.split('.').length === 2
        );
        map['resourceCode'] = resourceCode;
      }
    } else if (key === 'creatorTime' && searchValues[key].value && searchValues[key].value.length) {
      map['applyStartTime'] = searchValues[key].value[0].valueOf();
      map['applyEndTime'] = searchValues[key].value[1].valueOf();
    } else {
      map[key] = searchValues[key].value;
    }
    return map;
  }, {}),
});
