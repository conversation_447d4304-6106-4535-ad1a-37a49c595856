import flatten from 'lodash/flatten';

export const getRawPointsData = ({ roomMonitoring: { rawPointsData } }) => rawPointsData;
export const getRoomType = ({ roomMonitoring: { roomInfo } }) => roomInfo.roomType;
export const getInfraData = ({ roomMonitoring: { infrastructureData } }) => infrastructureData;
export const getRppParentDevices = ({ roomMonitoring: { arrayCabinetsParentDeviceMap } }) =>
  flatten(Object.values(arrayCabinetsParentDeviceMap));
export const getBatteryUnits = ({ roomMonitoring: { childBatteryUnitsMap } }) =>
  flatten(Object.values(childBatteryUnitsMap));
export const getInfraGraphPreview = ({ roomMonitoring: { infraGraphPreview } }) =>
  infraGraphPreview;
export const getAlarmInfo = ({ roomMonitoring: { alarmInfo } }) => alarmInfo;

export const getCustomersInfo = ({ roomMonitoring: { roomCustomers } }) => roomCustomers;
export const getRoomInfo = ({ roomMonitoring: { roomInfo } }) => roomInfo;
