export const getCommonDeviceInfoTreeData = ({
  common: {
    deviceCategory: { normalizedList },
  },
}) => normalizedList; // 获取commo的树数据，查找某个metaName

// 展开测点树时请求的数据
export const getfetchDeviceInfoData = ({ mergedProcessesdPoint: { pointInfoMap } }) => pointInfoMap;

export const getMergedPointFormData = ({
  mergedProcessesdPoint: { margedPointIds, margedPointMaps },
}) => ({
  margedPointIds,
  margedPointMaps,
});

export const getPressedPointDIStatusList = ({
  mergedProcessesdPoint: {
    processedPointList: { statusList },
  },
}) => statusList;

export const getPressedPointConfig = ({ mergedProcessesdPoint: { processedPointList } }) =>
  processedPointList;

export const getExpressInfos = ({ mergedProcessesdPoint: { expressionData } }) => expressionData;

export const getMergedPointData = ({ mergedProcessesdPoint: { mergedPointConfig } }) =>
  mergedPointConfig;

export const getpointTypeSelected = ({
  mergedProcessesdPoint: {
    pointTypeSelected: { pointType },
  },
}) => pointType;

export const getParentDeviceType = ({ mergedProcessesdPoint: { parentDeviceType } }) =>
  parentDeviceType;

export const getProcessedSelectedPoint = ({ mergedProcessesdPoint: { processedSelectedPoint } }) =>
  processedSelectedPoint;

export const getProcessedDeviceInfo = ({ mergedProcessesdPoint: { processedDeviceInfo } }) =>
  processedDeviceInfo;

export const getCustomSpaceGuid = ({ mergedProcessesdPoint: { customSpaceGuid } }) =>
  customSpaceGuid;

export const getFormulaName = ({ mergedProcessesdPoint: { formulaName } }) => formulaName;
