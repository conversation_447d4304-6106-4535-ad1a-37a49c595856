import moment from 'moment';

export const getQ = ({ check: { pagination, searchValues }, user: { resourceCodes } }) => ({
  ...pagination,
  ...Object.keys(searchValues).reduce((map, key) => {
    let resourcesCode = [];
    if (key === 'resourcesCodes' && searchValues[key].value && searchValues[key].value.length) {
      if (searchValues[key].value.length > 1) {
        resourcesCode = [
          ...resourcesCode,
          searchValues[key].value[0] + '.' + searchValues[key].value[1],
        ];
        map['resourcesCodes'] = resourcesCode;
      } else {
        resourcesCode = resourceCodes.filter(
          code => code.includes(searchValues[key].value) && code.split('.').length === 2
        );
        map['resourcesCodes'] = resourcesCode;
      }
    } else if (key === 'applyTime' && searchValues[key].value && searchValues[key].value.length) {
      map['applyStartTime'] = Number(searchValues[key].value[0].unix() + '000');
      map['applyEndTime'] = Number(searchValues[key].value[1].unix() + '000');
    } else if (key === 'supplyTime' && searchValues[key].value && searchValues[key].value.length) {
      map['checkStartTime'] = Number(searchValues[key].value[0].unix() + '000');
      map['checkEndTime'] = Number(searchValues[key].value[1].unix() + '000');
    } else if (key === 'staffId' && searchValues[key].value) {
      map[key] = searchValues[key].value.value;
    } else {
      map[key] = searchValues[key].value;
    }
    return map;
  }, {}),
});

export const getCheckSubmitParam = ({ check: { createInfo }, user }) => ({
  ...Object.keys(createInfo).reduce((map, key) => {
    if (key === 'scheduleDate' && createInfo[key].value) {
      map[key] = moment(createInfo[key].value).startOf('day').valueOf();
    } else if (key === 'staffId') {
      map[key] = user.userId;
    } else if (key === 'checkType' && createInfo[key].value) {
      map[key] = createInfo[key].value;
    } else if (key === 'dutyId' && createInfo[key].value) {
      map[key] = createInfo[key].value;
    } else if (key === 'reason' && createInfo[key].value) {
      map[key] = createInfo[key].value;
    } else if (key === 'checkChannel') {
      map[key] = 'LOCAL';
    } else if (key === 'files' && createInfo[key].value) {
      map[key] = createInfo[key].value.fileList.map(file => {
        return {
          fileFormat: file.ext,
          fileName: file.name,
          filePath: file.patialPath,
          fileSize: file.size,
          uploadBy: file.uploadUser.id,
          uploadByName: file.uploadUser.name,
          uploadTime: file.uploadedAt,
        };
      });
    }
    return map;
  }, {}),
});

export const getExchangeSubmitParam = ({ alterInfo: { create } }) => ({
  ...Object.keys(create).reduce((map, key) => {
    if (key === 'leaveInfoList') {
    } else if (key === 'applyStaffId' && create[key].value) {
      map[key] = create[key].value.key;
    } else {
      map[key] = create[key].value;
    }
    return map;
  }, {}),
});
