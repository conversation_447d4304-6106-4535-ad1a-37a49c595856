export const getNotificationTpl = ({
  customAlarmConfiguration: {
    newTemplateMess: { globalNotificationTpl },
  },
}) => globalNotificationTpl;

export const getSingleNotificationTpl = ({
  customAlarmConfiguration: {
    newTemplateMess: { monitorItems, monitorItemMap },
  },
}) => ({
  monitorItems,
  monitorItemMap,
});

export const getNewTemplateMess = ({ customAlarmConfiguration: { newTemplateMess } }) =>
  newTemplateMess;

export const getNotificationFields = ({
  alarmManage: {
    alarmCopyList: { physicalFields, faultedFields, otherFields },
  },
}) => [...physicalFields, ...faultedFields, ...otherFields];

// 点位信息
export const getSelectedPointInfo = ({ customAlarmConfiguration: { selectedPointInfo } }) =>
  selectedPointInfo;

export const getPointIndevice = ({ customAlarmConfiguration: { pointIndevice } }) => pointIndevice;

export const getFormStep = ({ customAlarmConfiguration: { current } }) => current;

export const getSearchValues = (
  {
    customAlarmConfiguration: {
      searchValues,
      templateListPage: { pageNum, pageSize },
    },
  },
  initialSearchValues = {}
) => ({
  searchValues: {
    ...searchValues,
    ...Object.keys(initialSearchValues).reduce((map, prop) => {
      map[prop] = {
        name: prop,
        value: initialSearchValues[prop],
      };

      return map;
    }, {}),
  },
  pagination: { pageNum, pageSize },
});
