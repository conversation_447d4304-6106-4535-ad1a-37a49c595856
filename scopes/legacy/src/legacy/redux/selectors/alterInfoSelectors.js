import moment from 'moment';

export const getQ = ({ alterInfo: { pagination, searchValues }, user: { resourceCodes } }) => ({
  ...pagination,
  ...Object.keys(searchValues).reduce((map, key) => {
    let resourcesCode = [];
    // searchValues: { a: { value: 1 } } -> map: { a: 1 }
    if (key === 'resourcesCode' && searchValues[key].value && searchValues[key].value.length) {
      if (searchValues[key].value.length > 1) {
        resourcesCode = [
          ...resourcesCode,
          searchValues[key].value[0] + '.' + searchValues[key].value[1],
        ];
        map['resourcesCode'] = resourcesCode;
      } else {
        resourcesCode = resourceCodes.filter(
          code => code.includes(searchValues[key].value) && code.split('.').length === 2
        );
        map['resourcesCode'] = resourcesCode;
      }
    } else if (key === 'creatorTime' && searchValues[key].value && searchValues[key].value.length) {
      map['applyStartTime'] = Number(searchValues[key].value[0].unix() + '000');
      map['applyEndTime'] = Number(searchValues[key].value[1].unix() + '000');
    } else if (key === 'creatorId' && searchValues[key].value) {
      map[key] = searchValues[key].value.id;
    } else {
      map[key] = searchValues[key].value;
    }
    return map;
  }, {}),
});

export const getLeaveSubmitParam = ({ alterInfo: { create } }) => ({
  ...Object.keys(create).reduce((map, key) => {
    if (key === 'leaveInfoList' && create[key].value && create[key].value.length) {
      let leaveInfoList = [];
      create[key].value.forEach(record => {
        let leaveInfo = { dutyId: null, replaceStaffId: null, scheduleDate: null };
        leaveInfo.dutyId = record.dutyId.key;
        if (record.replaceStaffId && record.replaceStaffId.key) {
          leaveInfo.replaceStaffId = record.replaceStaffId.key;
        }
        leaveInfo.scheduleDate = moment(record.scheduleDate).startOf('day').valueOf();
        leaveInfoList.push(leaveInfo);
      });
      map[key] = leaveInfoList;
    } else if (key === 'applyStaffId' && create[key].value) {
      map[key] = create[key].value.key;
    } else if (key === 'leaveType' && create[key].value) {
      map[key] = create[key].value;
    } else if (key === 'files' && create[key].value) {
      map[key] = create[key].value.map(fileInfos => {
        return fileInfos.response[0].toApiObject();
      });
    } else if (key === 'leaveReason' && create[key].value) {
      map[key] = create[key].value;
    }
    return map;
  }, {}),
});

export const getExchangeSubmitParam = ({ alterInfo: { create } }) => ({
  ...Object.keys(create).reduce((map, key) => {
    if (key === 'leaveInfoList') {
    } else if (key === 'applyStaffId' && create[key].value) {
      map[key] = create[key].value.key;
    } else if (key === 'files' && create[key].value) {
      map[key] = create[key].value.map(fileInfos => {
        return fileInfos.response[0].toApiObject();
      });
    } else {
      map[key] = create[key].value;
    }
    return map;
  }, {}),
});
