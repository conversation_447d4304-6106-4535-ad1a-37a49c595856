export const getShiftSearchValuesAndPagination = ({
  staffShift: {
    shift: { searchValues, pagination },
  },
}) => ({ searchValues, pagination });

export const getCreateShiftInfos = ({
  staffShift: {
    shift: { createOptions },
  },
}) => ({ createOptions });

export const getShiftSysSearchValuesAndPagination = ({
  staffShift: {
    shiftSys: { searchValues, pagination },
  },
}) => ({ searchValues, pagination });

export const getCreateShiftSysOptions = ({
  staffShift: {
    shiftSys: { createOptions, week, period },
  },
}) => ({ createOptions, week, period });

export const getEditShiftSysOptions = ({
  staffShift: {
    shiftSys: { editOptions },
  },
}) => ({ editOptions });

export const getDutyGroupSearchValuesAndPagination = ({
  staffShift: {
    dutyGroup: { searchValues, pagination },
  },
}) => ({ searchValues, pagination });

export const getDutyGroupCreateOptions = ({
  staffShift: {
    dutyGroup: { create },
  },
}) => ({ create });

export const getDutyGroupEditOptions = ({
  staffShift: {
    dutyGroup: { edit },
  },
}) => ({ edit });

export const getGroupScheduleSearchValuesAndPagination = ({
  staffShift: {
    groupSchedule: { searchValues, pagination },
  },
}) => ({ searchValues, pagination });

export const getGroupScheduleCreateOptions = ({
  staffShift: {
    groupSchedule: { create },
  },
}) => ({ create });

export const getGroupScheduleEditOptions = ({
  staffShift: {
    groupSchedule: { edit },
  },
}) => ({ edit });

export const getAttGroupSearchValuesAndPagination = ({
  staffShift: {
    attendanceGroup: { searchValues, pagination },
  },
}) => ({ searchValues, pagination });

export const getAttGroupCreateOptions = ({
  staffShift: {
    attendanceGroup: { create },
  },
}) => ({ create });

export const getAttGroupScheduleEdit = ({
  staffShift: {
    attendanceGroup: { edit },
  },
}) => ({ edit });

export const getAttGroupSchedulePreview = ({
  staffShift: {
    attendanceGroup: { schedule },
  },
}) => ({ schedule });

export const getAttGroupScheduleRuleParams = ({
  staffShift: {
    attendanceGroup: {
      schedule: { scheduleStaffList, scheduleStartTime, scheduleCycleMonths },
      edit: { id, attName },
    },
  },
}) => {
  const params = {
    attGroupId: id,
    scheduleCycleMonths: scheduleCycleMonths,
    scheduleStartTime: scheduleStartTime,
    scheduleStaffList,
    groupName: attName.value,
  };
  return params;
};

export const geDateInSchedule = ({
  staffShift: {
    attendanceGroup: { dateInSchedule },
  },
}) => ({ dateInSchedule });

export const getMonthlySearchValuesAndPagination = ({
  staffShift: {
    monthly: { searchValues, pagination },
  },
}) => ({ searchValues, pagination });

export const getDailySearchValuesAndPagination = ({
  staffShift: {
    daily: { searchValues, pagination },
  },
}) => ({ searchValues, pagination });

export const getCheckSearchValuesAndPagination = ({
  staffShift: {
    check: { searchValues, pagination },
  },
}) => ({ searchValues, pagination });

export const getRecordSearchValuesAndPagination = ({
  staffShift: {
    record: { searchValues, pagination },
  },
}) => ({ searchValues, pagination });
