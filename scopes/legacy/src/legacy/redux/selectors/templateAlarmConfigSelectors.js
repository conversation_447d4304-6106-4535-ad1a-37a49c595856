export const getNotificationTpl = ({
  alarmConfigurationTemplate: {
    newTemplateMess: { globalNotificationTpl },
  },
}) => globalNotificationTpl;

export const getSingleNotificationTpl = ({
  alarmConfigurationTemplate: {
    newTemplateMess: { monitorItems, monitorItemMap },
  },
}) => ({
  monitorItems,
  monitorItemMap,
});

export const getNewTemplateMess = ({ alarmConfigurationTemplate: { newTemplateMess } }) =>
  newTemplateMess;

export const getNotificationFields = ({
  alarmManage: {
    alarmCopyList: { physicalFields, faultedFields, otherFields },
  },
}) => [...physicalFields, ...faultedFields, ...otherFields];

// 点位信息
export const getSelectedPointInfo = ({ alarmConfigurationTemplate: { selectedPointInfo } }) =>
  selectedPointInfo;

export const getPointIndevice = ({ alarmConfigurationTemplate: { pointIndevice } }) =>
  pointIndevice;

export const getFormStep = ({ alarmConfigurationTemplate: { current } }) => current;

export const getSearchValues = (
  {
    alarmConfigurationTemplate: {
      searchValues,
      templateListPage: { pageNum, pageSize },
    },
  },
  initialSearchValues = {}
) => ({
  searchValues: {
    ...searchValues,
    ...Object.keys(initialSearchValues).reduce((map, prop) => {
      map[prop] = {
        name: prop,
        value: initialSearchValues[prop],
      };

      return map;
    }, {}),
  },
  pagination: { pageNum, pageSize },
});
