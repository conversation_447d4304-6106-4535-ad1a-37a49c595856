import uniq from 'lodash/uniq';

export const getBlockRooms = ({ idcWorkbench: { roomsMap } }, blockGuid) =>
  roomsMap[blockGuid] || [];
export const getCurAlarmsIntervalBlockGuids = ({ idcWorkbench: { curAlarmsIntervalBlockGuids } }) =>
  curAlarmsIntervalBlockGuids;
export const getActiveBlocks = ({
  idcWorkbench: { idc, runningStatesActiveBlock, roomsInfoActiveBlock },
}) => ({ idc, blocks: uniq([runningStatesActiveBlock, roomsInfoActiveBlock]) });
export const getRunningStatesActiveBlock = ({ idcWorkbench: { runningStatesActiveBlock } }) =>
  runningStatesActiveBlock;
export const getRoomsInfoActiveBlock = ({ idcWorkbench: { roomsInfoActiveBlock } }) =>
  roomsInfoActiveBlock;
export const getPendingMattersList = ({ idcWorkbench: { pendingMattersList } }) =>
  pendingMattersList;
export const getBlocks = ({ idcWorkbench: { blocks } }) => blocks;
