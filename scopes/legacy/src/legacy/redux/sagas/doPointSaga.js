import { call, fork, put, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { deletePointInstance } from '@manyun/resource-hub.service.delete-point-instance';
import { fetchPointsByCondition } from '@manyun/resource-hub.service.fetch-points-by-condition';

import { doPointService } from '@manyun/dc-brain.legacy.services';

import {
  MetaCategoryAction,
  MetaCategoryNumAction,
  addPointAction,
  deletePointAction,
  doPointActions,
  fetchPointDetail,
  updatePointAction,
} from '../actions/doPointActions';

/***************************** Subroutines ************************************/

function* pointDetail(payload) {
  yield put(doPointActions.request());
  const { data, error } = yield call(fetchPointsByCondition, payload);
  if (data) {
    const AIData = {
      label: '模拟量读点',
      value: 'AI',
      data: [],
      num: 0,
    };
    const AOData = {
      label: '模拟量写点',
      value: 'AO',
      data: [],
      num: 0,
    };
    const DIData = {
      label: '状态量读点',
      value: 'DI',
      data: [],
      num: 0,
    };
    const DOData = {
      label: '状态量写点',
      value: 'DO',
      data: [],
      num: 0,
    };
    const alarmData = {
      label: '告警点',
      value: 'ALARM',
      data: [],
      num: 0,
    };
    const result = data.data.map(point => point.toApiObject());
    yield put(doPointActions.featchPointDetailSuccess({ total: data.total, data: result }));
    yield put(doPointActions.savePointListData({ total: data.total, data: result }));

    result.forEach(point => {
      if (point.dataType.code === 'AI') {
        AIData.data = [...AIData.data, point];
        AIData.num = AIData.num + 1;
      } else if (point.dataType.code === 'AO') {
        AOData.data = [...AOData.data, point];
        AOData.num = AOData.num + 1;
      } else if (point.dataType.code === 'DI') {
        DIData.data = [...DIData.data, point];
        DIData.num = DIData.num + 1;
      } else if (point.dataType.code === 'DO') {
        DOData.data = [...DOData.data, point];
        DOData.num = DOData.num + 1;
      } else {
        alarmData.data = [...alarmData.data, point];
        alarmData.num = alarmData.num + 1;
      }
    });
    const total = [AIData, AOData, DIData, DOData, alarmData].map(item => {
      return {
        ...item,
        label: `${item.label}(${item.num})`,
      };
    });
    yield put(doPointActions.separateData(total));
  } else {
    message.error(error);
    yield put(doPointActions.failure());
  }
}

function* addPoint(payload) {
  yield put(doPointActions.request());
  const { deviceType } = payload;
  const { response, error } = yield call(doPointService.fetchAddPoint, payload);
  if (response) {
    message.success('新增测点成功');
    yield put(doPointActions.addPointVisible());
    yield fork(pointDetail, { deviceType: deviceType });
  } else {
    message.error(error || '新增测点请求失败');
    yield put(doPointActions.failure());
  }
}

function* deletePoint({ params, successCb, errorCb }) {
  yield put(doPointActions.request());
  if (params.type === 'custom') {
    if (!params.id) {
      return;
    }
    const { error } = yield call(deletePointInstance, { id: params.id });
    if (error) {
      errorCb();
      message.error(error.message);
      yield put(doPointActions.failure());
    } else {
      successCb();
      message.success('删除测点成功');
    }
    return;
  }
  const { error } = yield call(doPointService.deletePoint, {
    code: params.code,
    operatorNotes: params.operatorNotes,
    deviceType: params.deviceType,
  });
  if (error) {
    errorCb();
    message.error(error);
    yield put(doPointActions.failure());
  } else {
    successCb();
    message.success('删除测点成功');
  }
}

function* updatePoint(q) {
  const { deviceType } = q;
  const { response, error } = yield call(doPointService.updatePoint, q);
  if (response) {
    message.success('更新测点成功');
    yield put(doPointActions.editVisible());
    yield fork(pointDetail, { deviceType });
  } else {
    message.error(error);
    yield put(doPointActions.failure());
  }
}

function* metaCategory(payload) {
  const { response, error } = yield call(doPointService.fetchMetaCategory);
  if (response) {
    yield put(doPointActions.fetcgMetaCategory(response.data));
    if (payload.deviceType) {
      const top = payload.deviceType.substr(0, 1) + '-C0';
      const sec = payload.deviceType.substr(0, 3) + '-C1';
      yield put(doPointActions.saveExpandedKey([top, sec]));
    }
  } else {
    message.error(error);
    yield put(doPointActions.saveExpandedKey([]));
    yield put(doPointActions.failure());
  }
}

function* metaCategoryNum() {
  const { response, error } = yield call(doPointService.fetchDeviceTypePointCountMap);
  if (response) {
    yield put(doPointActions.fetcgMetaCategoryNumSuccess(response));
  } else {
    message.error(error);
    yield put(doPointActions.failure());
  }
}

/******************************************************************************/
/******************************* WATCHERS *************************************/
/******************************************************************************/

// 查询测点详情
export function* watchFetchPointDetail() {
  while (true) {
    const { payload } = yield take(fetchPointDetail.type);
    yield fork(pointDetail, payload);
  }
}

// 新增
export function* watchFetchAddPoint() {
  while (true) {
    const { payload } = yield take(addPointAction.type);
    yield fork(addPoint, payload);
  }
}

// 删除
export function* watchFetchDeletePoint() {
  while (true) {
    const { payload } = yield take(deletePointAction.type);
    yield fork(deletePoint, payload);
  }
}

// 编辑
export function* watchFetchUpdatePoint() {
  while (true) {
    const { payload } = yield take(updatePointAction.type);
    yield fork(updatePoint, payload);
  }
}

// 设备分类三级结构
export function* watchFetchMetaCategory() {
  while (true) {
    const { payload } = yield take(MetaCategoryAction.type);
    yield fork(metaCategory, payload);
  }
}

// 设备分类三级测点数量
export function* watchFetchMetaCategoryNum() {
  while (true) {
    yield take(MetaCategoryNumAction.type);
    yield fork(metaCategoryNum);
  }
}

export default [
  fork(watchFetchPointDetail),
  fork(watchFetchAddPoint),
  fork(watchFetchDeletePoint),
  fork(watchFetchUpdatePoint),
  fork(watchFetchMetaCategory),
  fork(watchFetchMetaCategoryNum),
];
