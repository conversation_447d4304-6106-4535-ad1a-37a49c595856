import { push } from 'connected-react-router';
import omit from 'lodash/omit';
import uniq from 'lodash/uniq';
import moment from 'moment';
import { normalize, schema } from 'normalizr';
import { call, cancel, delay, fork, put, race, select, take } from 'redux-saga/effects';
import shortid from 'shortid';

import { message } from '@manyun/base-ui.ui.message';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { Alarm } from '@manyun/monitoring.model.alarm';
import { fetchAlarms } from '@manyun/monitoring.service.fetch-alarms';
import { fetchPointValues } from '@manyun/monitoring.service.fetch-point-values';
import { subscriptionsSliceActions } from '@manyun/monitoring.state.subscriptions';
import { CHANGE_EXE_WAY_MAP } from '@manyun/ticket.model.change';

import {
  CHANGE_OP_ITEM_STATUS_KEY_MAP,
  CHANGE_STEP_TYPE_KEY_MAP,
} from '@manyun/dc-brain.legacy.pages/change/constants';
import {
  approvalActionCreator,
  cancelTicketRealtimeData,
  changeActions,
  checkItemArtificialValidationActionCreator,
  deleteTemplateActionCreator,
  getAlarmLevelCount,
  getChangeDataActionCreator,
  getRealtimeDataAction,
  getTemplateCopyInfoActionCreator,
  getTemplateDetailInfo,
  getTemplateEditDetailInfoActionCreator,
  getTicketCopyInfoActionCreator,
  getTicketDataActionCreator,
  getTicketDetailAlarmList,
  getTicketDetailInfo,
  getTicketStepDetail,
  opArtificialValidationActionCreator,
  resetSearchValuesActionCreator, // 变更列表
  resetTicketSearchValuesActionCreator,
  setPaginationThenGetDataActionCreator,
  setTemplateSaveActionCreator,
  setTemplateSubmitActionCreator,
  setTicketPaginationThenGetDataActionCreator, //
  setTicketSaveActionCreator,
  setTicketSubmitActionCreator,
  startOperationActionCreator,
  stepCheckedActionCreator,
  stepSkipActionCreator,
  stepStopActionCreator,
  stopChangeActionCreator,
  summerySubmitActionCreator,
  templateApprovalOrRevertActionCreator,
  ticketApprovalOrRevertActionCreator,
  ticketCloseActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/changeActions';
import { changeService } from '@manyun/dc-brain.legacy.services';
import {
  generateChangeTemplateEditLocation,
  generateChangeTicketEditLocation,
} from '@manyun/dc-brain.legacy.utils/urls';

// import * as urls from '@manyun/dc-brain.legacy.constants/urls';
import {
  getCheckItemsStatus,
  getDetailStepPointCode,
  getOPCheckItems,
  getSearchValuesAndPagination,
  getTemplateEdit,
  getTemplateNew,
  getTicketDetailAlarm,
  getTicketDetailStepCodes,
  getTicketEdit,
  getTicketNew,
  getTicketSearchValuesAndPagination,
} from './../selectors/changeSelector';

/***************************** Subroutines ************************************/

function getQ(pagination, searchValues) {
  const baseQ = { ...pagination };
  for (const [, { name, value }] of Object.entries(searchValues)) {
    const val = value === '' ? null : value;
    if (name) {
      if (name === 'modifyTime') {
        if (val && val.length === 2) {
          baseQ['modifyStartTime'] = moment(val[0].format('YYYY-MM-DD')).valueOf();
          baseQ['modifyEndTime'] = moment(`${val[1].format('YYYY-MM-DD')} 23:59:59`).valueOf();
        }
      } else if (name === 'templateId' || name === 'templateName') {
        if (val) {
          baseQ[name] = val.trim();
        }
      } else if (name === 'modifyPersonId') {
        if (val && val.value) {
          baseQ[name] = val.value;
        }
      } else {
        baseQ[name] = val;
      }
    }
  }
  return baseQ;
}

function* getChangeData(shouldResetPageNum = true) {
  yield put(changeActions.startTPLTableLoading());
  if (shouldResetPageNum) {
    yield put(changeActions.resetPageNum());
  }
  const { searchValues, pagination } = yield select(getSearchValuesAndPagination);
  const q = getQ(pagination, searchValues);
  const { response, error } = yield call(changeService.fetchData, { ...q, changeVersion: 1 });
  if (error) {
    message.error(error);
    yield put(changeActions.failure());
  } else {
    yield put(changeActions.setDataAndTotal(response));
  }
}

function* resetSearchValues() {
  yield put(changeActions.resetSearchValuesAndPagination());
  yield put(getChangeDataActionCreator(false));
}

function* setPagination(newPagination) {
  yield put(changeActions.setPagination(newPagination));
  yield put(getChangeDataActionCreator(false));
}

// 变更
function getParams(pagination, searchValues) {
  const baseQ = { ...pagination };
  for (const [name, { value }] of Object.entries(searchValues)) {
    if (name === 'planTime' && value?.length) {
      baseQ['planStartTime'] = value[0].startOf('day');
      baseQ['planEndTime'] = value[1].endOf('day');
    } else if (name === 'realTime' && value?.length) {
      baseQ['executeStartTime'] = value[0].startOf('day');
      baseQ['executeEndTime'] = value[1].endOf('day');
    } else if (name === 'location' && value) {
      const location = value.split('.');
      baseQ['idcTag'] = location[0];
      baseQ['blockTag'] = location.length === 2 ? `${location[0]}.${location[1]}` : null;
    } else if (name === 'changeOrderId' || name === 'title') {
      baseQ[name] = value?.trim();
    } else if (name === 'creatorId') {
      baseQ[name] = value;
    } else if (name === 'changeType' && value) {
      baseQ[name] = Number(value);
    } else if (name === 'changeReasonList' && value) {
      baseQ[name] = value.map(item => item.label);
    } else {
      baseQ[name] = value;
    }
  }
  return baseQ;
}

function* getTicketData(shouldResetPageNum = true) {
  yield put(changeActions.startTicketTableLoading());
  if (shouldResetPageNum) {
    yield put(changeActions.resetTicketPageNum());
  }
  const { searchValues, pagination } = yield select(getTicketSearchValuesAndPagination);
  const q = getParams(pagination, searchValues);
  const { response, error } = yield call(changeService.fetchTicketData, { ...q, changeVersion: 1 });
  if (error) {
    message.error(error);
    yield put(changeActions.failure());
  } else {
    yield put(changeActions.setTicketDataAndTotal(response));
  }
}

function* resetTicketSearchValues() {
  yield put(changeActions.startTicketTableLoading());
  yield put(changeActions.resetTicketSearchValuesAndPagination());
  yield put(getTicketDataActionCreator(false));
}

function* setTicketPagination(newPagination) {
  yield put(changeActions.setTicketPagination(newPagination));
  yield put(getTicketDataActionCreator(false));
}

function* setTemplateSave({ id, mode }) {
  if (mode === 'new') {
    yield put(changeActions.templateNewSaveLoading());
    const { step, step1, step2, checkItemsMap } = yield select(getTemplateNew);
    const { stepCodes, stepMaps } = step2;
    const params = getSaveSubmitParams({
      step1,
      stepCodes,
      stepMaps,
      checkItemsMap,
      type: 'template',
    });

    const { response, error } = yield call(changeService.saveTemplate, params);
    if (error) {
      message.error(error);
      yield put(changeActions.templateNewSaveLoading());
      // yield put(changeActions.failure());
    } else {
      message.success('保存模板成功');
      yield put(changeActions.templateSaveNewId(response));
      yield put(changeActions.resetTemplateNewInfo());
      yield put(push(generateChangeTemplateEditLocation({ id: response, stepCurrent: step })));
    }
  }
  if (mode === 'edit') {
    yield put(changeActions.templateEditSaveLoading());
    const { step1, step2, checkItemsMap } = yield select(getTemplateEdit);
    const { stepCodes, stepMaps } = step2;
    const params = getSaveSubmitParams({
      step1,
      stepCodes,
      stepMaps,
      checkItemsMap,
      type: 'template',
    });
    params.templateId = id;
    const { response, error } = yield call(changeService.saveTemplate, params);
    if (error) {
      message.error(error);
      yield put(changeActions.templateEditSaveLoading());
    } else {
      message.success('保存模板成功');
      yield put(changeActions.templateEditSaveLoading());
      yield put(changeActions.templateSaveEditId(response));
    }
  }
}

function* setTemplateSubmit({ id, mode }) {
  if (mode === 'new') {
    yield put(changeActions.templateNewSubmitLoading());
    const { step, step1, step2, checkItemsMap } = yield select(getTemplateNew);
    const { stepCodes, stepMaps } = step2;
    const params = getSaveSubmitParams({
      step1,
      stepCodes,
      stepMaps,
      checkItemsMap,
      type: 'template',
    });
    const { response, error } = yield call(changeService.submitTemplate, params);
    if (error) {
      message.error(error);
      yield put(changeActions.templateNewSubmitLoading());
      // yield put(changeActions.failure());
    } else {
      yield put(changeActions.templateNewSubmitLoading());
      yield put(changeActions.setTemplateNewStep(step + 1));
      yield put(changeActions.templateSaveNewId(response));
    }
  }
  if (mode === 'edit') {
    yield put(changeActions.templateEditSubmitLoading());
    const { step, step1, step2, checkItemsMap } = yield select(getTemplateEdit);
    const { stepCodes, stepMaps } = step2;
    const params = getSaveSubmitParams({
      step1,
      stepCodes,
      stepMaps,
      checkItemsMap,
      type: 'template',
    });

    if (id) {
      params.templateId = id;
    }
    const { response, error } = yield call(changeService.submitTemplate, params);
    if (error) {
      message.error(error);
      yield put(changeActions.templateEditSubmitLoading());
      // yield put(changeActions.failure());
    } else {
      yield put(changeActions.templateEditSubmitLoading());
      yield put(changeActions.setTemplateEditStep(step + 1));
      yield put(changeActions.templateSaveEditId(response));
    }
  }
}

function* getTmplateDetailInfo({ id, mode, stepCurrent }) {
  const { response, error } = yield call(changeService.getTemplateDetailInfo, id);
  if (error) {
    message.error(error);
  } else {
    if (mode === 'detail') {
      const detail = templateDetailInfo(response, 'template');
      yield put(changeActions.setTemplateDetailInfo(detail));
    }
    if (mode === 'edit') {
      const detail = templateDetailInfo(response, 'template');
      yield put(
        changeActions.setTemplateEditInfo({ ...detail, step: Number(stepCurrent) || detail.step })
      );
    }
    if (mode === 'ticketNew') {
      const { step1 } = yield select(getTicketNew);
      const detail = templateDetailInfo(response, 'byTemplate', step1);
      yield put(changeActions.setTicketNewInfo(detail));
    }
    if (mode === 'ticketEdit') {
      const { step1 } = yield select(getTicketEdit);
      const detail = templateDetailInfo(response, 'byTemplate', step1);
      yield put(changeActions.setTicketEditInfo({ ...detail }));
    }
  }
}

function* getTemplateEditDetailInfo({ id, stepCurrent }) {
  const { response, error } = yield call(changeService.getTemplateDetailInfo, id);
  if (error) {
    message.error(error);
  } else {
    const detail = templateDetailInfo(response, 'template');
    yield put(
      changeActions.setTemplateEditInfo({ ...detail, step: Number(stepCurrent) || detail.step })
    );
  }
}

function* ticketDetailInfo({ id, mode, stepCurrent }) {
  const { response, error } = yield call(changeService.ticketDetail, id);
  if (error) {
    message.error(error);
  } else {
    const detail = templateDetailInfo(response, 'ticket');
    if (mode === 'ticketNew') {
      yield put(changeActions.setTicketNewInfo(detail));
    }
    if (mode === 'ticketDetail') {
      yield put(changeActions.setTicketDetailChangeInfo({ ...detail }));
    }
    if (mode === 'ticketEdit') {
      yield put(
        changeActions.setTicketEditInfo({ ...detail, step: Number(stepCurrent) || detail.step })
      );
    }
  }
}

function* setTicketSave({ id, mode }) {
  if (mode === 'new') {
    yield put(changeActions.ticketNewSaveLoading());
    const { step, step1, step2, checkItemsMap, checkItemDeviceMaps, matchObjectInfoMaps } =
      yield select(getTicketNew);
    const { stepCodes, stepMaps } = step2;
    const params = getSaveSubmitParams({
      step1,
      stepCodes,
      stepMaps,
      checkItemsMap,
      checkItemDeviceMaps,
      matchObjectInfoMaps,
      type: 'ticket',
      isNewTicket: true,
    });
    const { response, error } = yield call(changeService.saveTicket, params);
    if (error) {
      message.error(error);
      yield put(changeActions.ticketNewSaveLoading());
    } else {
      message.success('保存成功');
      yield put(changeActions.ticketNewSaveLoading());
      yield put(changeActions.ticketSaveNewId(response));
      yield put(push(generateChangeTicketEditLocation({ id: response, stepCurrent: step })));
    }
  }
  if (mode === 'edit') {
    yield put(changeActions.ticketEditSaveLoading());
    const { step1, step2, checkItemsMap, checkItemDeviceMaps, matchObjectInfoMaps } =
      yield select(getTicketEdit);
    const { stepCodes, stepMaps } = step2;
    const params = getSaveSubmitParams({
      step1,
      stepCodes,
      stepMaps,
      checkItemsMap,
      checkItemDeviceMaps,
      matchObjectInfoMaps,
      type: 'ticket',
    });
    params.changeOrderId = id;
    const { error } = yield call(changeService.saveTicket, params);
    if (error) {
      message.error(error);
      yield put(changeActions.ticketEditSaveLoading());
    } else {
      message.success('保存成功');
      yield put(changeActions.ticketEditSaveLoading());
    }
  }
}

function* setTicketSubmit({ id, mode }) {
  if (mode === 'new') {
    yield put(changeActions.ticketNewSubmitLoading());
    const { step, step1, step2, checkItemsMap, checkItemDeviceMaps, matchObjectInfoMaps } =
      yield select(getTicketNew);
    const { stepCodes, stepMaps } = step2;
    const params = getSaveSubmitParams({
      step1,
      stepCodes,
      stepMaps,
      checkItemsMap,
      checkItemDeviceMaps,
      matchObjectInfoMaps,
      type: 'ticket',
    });

    const { response, error } = yield call(changeService.submitTicket, {
      ...params,
      exeWay: CHANGE_EXE_WAY_MAP.OnLine,
    });
    if (error) {
      message.error(error);
      yield put(changeActions.ticketNewSubmitLoading());
      // yield put(changeActions.failure());
    } else {
      yield put(
        changeActions.setTicketNewResult({
          id: response.changeOrderId,
          title: params.title,
          templateCode: params.templateId,
          idcTag: params.idcTag,
          blockTag: params.blockTag,
          riskLevel: params.riskLevel,
          // urgency: params.urgency,
          planStartTime: params.planStartTime,
          planEndTime: params.planEndTime,
          changeReason: params.reason,
          changeType: step1.changeType.value.label,
          workflowId: response.workflowId,
        })
      );
      yield put(changeActions.ticketNewSubmitLoading());
      yield put(changeActions.setTicketNewStep(step + 1));
    }
  }
  if (mode === 'edit') {
    const { step, step1, step2, checkItemsMap, checkItemDeviceMaps, matchObjectInfoMaps } =
      yield select(getTicketEdit);
    const { stepCodes, stepMaps } = step2;
    const params = getSaveSubmitParams({
      step1,
      stepCodes,
      stepMaps,
      checkItemsMap,
      checkItemDeviceMaps,
      matchObjectInfoMaps,
      type: 'ticket',
    });
    yield put(changeActions.ticketEditSubmitLoading());
    if (id) {
      params.changeOrderId = id;
    }
    const { response, error } = yield call(changeService.submitTicket, {
      ...params,
      exeWay: CHANGE_EXE_WAY_MAP.OnLine,
    });
    if (error) {
      message.error(error);
      yield put(changeActions.ticketEditSubmitLoading());
      // yield put(changeActions.failure());
    } else {
      yield put(changeActions.ticketEditSubmitLoading());
      yield put(changeActions.setTicketEditStep(step + 1));
      yield put(
        changeActions.setTicketEditResult({
          id,
          title: params.title,
          templateCode: params.templateId,
          idcTag: params.idcTag,
          blockTag: params.blockTag,
          riskLevel: params.riskLevel,
          // urgency: params.urgency,
          planStartTime: params.planStartTime,
          planEndTime: params.planEndTime,
          changeReason: params.reason,
          changeType: step1.changeType.value.label,
          workflowId: response.workflowId,
        })
      );
    }
  }
}

function getLimitOfEstimation({ operatorList, expectedValue, dataType }) {
  if (dataType === 'AI' && Array.isArray(operatorList) && operatorList.length > 1) {
    return true;
  }
  if (dataType === 'DI' && expectedValue === null) {
    return true;
  }
  return false;
}

function templateDetailInfo(response, type, step1 = {}) {
  const deviceGuids = [];
  const detail = { step: 0, step2: {}, checkItemDeviceMaps: {}, matchObjectInfoMaps: {} };
  if (type === 'template') {
    detail.step1 = {
      ...omit(
        response,
        'stepList',
        'templateName',
        'riskLevel',
        'changeType',
        'executeRoleCode',
        'executeRoleName'
      ),
      templateName: { value: response.templateName },
      riskLevel: { value: response.riskLevel },
      changeType: { value: { label: response.changeTypeName, key: response.changeType } },
      executeRole: { value: { key: response.executeRoleCode, label: response.executeRoleName } },
      exeUserGroupCode: {
        value: { key: response.executeRoleCode, label: response.executeRoleName },
      },
      fileInfoList: {
        value:
          Array.isArray(response.fileInfoList) && response.fileInfoList.length
            ? response.fileInfoList.map(obj => McUploadFile.fromApiObject(obj))
            : undefined,
      },
    };
  }
  if (type === 'ticket' || type === 'byTemplate') {
    if (type === 'byTemplate') {
      detail.step1 = {
        ...step1,
        ...omit(
          response,
          'stepList',
          'riskLevel',
          'changeType',
          'templateId',
          'reason',
          'fileInfoList'
        ),
        riskLevel: { value: response.riskLevel },
        templateName: response.templateName,
        changeType: { value: { label: response.changeTypeName, key: response.changeType } },
        templateId: { value: { key: response.templateId, label: response.templateName } },
        exeUserGroupCode: {
          value: { key: response.executeRoleCode, label: response.executeRoleName },
        },
        fileInfoList: {
          value:
            Array.isArray(response.fileInfoList) && response.fileInfoList.length
              ? response.fileInfoList.map(obj => McUploadFile.fromApiObject(obj))
              : [],
        },

        // reason: { value: { key: '', label: response.reason } },
      };
      // if (response.changeOrderExtJson.reasonType === 'other') {
      //   detail.step1.reason = {
      //     value: { key: 'other', label: '其他' },
      //   };
      //   detail.step1.otherReason = { value: response.reason };
      // }
    } else {
      detail.step1 = {
        ...omit(
          response,
          // 'stepList',
          'title',
          'riskLevel',
          'changeType',
          // 'urgency',
          'templateId',
          'planStartTime',
          'planEndTime',
          'reason',
          'fileInfoList',
          'customerFileInfoList'
        ),

        riskLevel: { value: response.riskLevel },
        changeType: { value: { label: response.changeTypeName, key: response.changeType } },
        title: { value: response.title },
        location: {
          value: [response.idcTag, response.blockTag.substring(response.blockTag.indexOf('.') + 1)],
        },
        // urgency: { value: response.urgency },
        templateId: { value: { key: response.templateId, label: response.templateName } },
        planStartTime: { value: moment(response.planStartTime) || null },
        planEndTime: { value: moment(response.planEndTime) || null },
        reason: { value: { key: '', label: response.reason } },
        exeUserGroupCode: {
          value: { label: response.exeUserGroupName, key: response.exeUserGroupCode },
        },
        blockTag: response.blockTag.substring(response.blockTag.indexOf('.') + 1),
        fileInfoList: {
          value:
            Array.isArray(response.fileInfoList) && response.fileInfoList.length
              ? response.fileInfoList.map(obj => McUploadFile.fromApiObject(obj))
              : [],
        },
        customerFileInfoList: {
          value:
            Array.isArray(response.customerFileInfoList) && response.customerFileInfoList.length
              ? response.customerFileInfoList.map(obj => McUploadFile.fromApiObject(obj))
              : [],
        },
      };
      // if (response.changeOrderExtJson.reasonType === 'other') {
      //   detail.step1.reason = {
      //     value: { key: 'other', label: '其他' },
      //   };
      //   detail.step1.otherReason = { value: response.reason };
      // }
    }
  }
  const stepCodes = [];
  const stepMaps = {};
  const checkItemsMap = {};
  if (Array.isArray(response.stepList) && response.stepList.length) {
    response.stepList.forEach(stepItem => {
      const id = shortid();
      stepCodes.push(id);
      stepMaps[id] = { id, ...omit(stepItem, 'checkDeviceInfoList') };
      if (
        stepItem.stepType === CHANGE_STEP_TYPE_KEY_MAP.CUSTOMIZE &&
        response.stepFileInfoList?.length
      ) {
        const idx = response.stepFileInfoList.findIndex(
          item => item.stepOrder === stepItem.stepOrder
        );
        if (idx > -1) {
          stepMaps[id]['fileInfoList'] = response.stepFileInfoList[idx].fileInfoList.map(obj =>
            McUploadFile.fromApiObject(obj)
          );
        }
      }
      if (stepItem.stepType === CHANGE_STEP_TYPE_KEY_MAP.RUN && stepItem.opObjectName) {
        stepMaps[id].opObjectName = stepItem.opObjectName.split('|');
        stepMaps[id].opObjectCode = stepItem.opObjectCode.split('|');
      }
      if (
        type === 'ticket' &&
        Array.isArray(stepItem.matchObjectInfoList) &&
        stepItem.matchObjectInfoList.length
      ) {
        if (stepItem.stepType !== 'RUN') {
          deviceGuids.push(...stepItem.matchObjectInfoList.map(item => item.code));
        }
        detail.matchObjectInfoMaps = {
          ...detail.matchObjectInfoMaps,
          [id]: stepItem.matchObjectInfoList,
        };
      }
      if (stepItem.checkDeviceInfoList) {
        checkItemsMap[id] = [];
        stepItem.checkDeviceInfoList.forEach(deviceItem => {
          if (Array.isArray(deviceItem.deviceInfoList) && deviceItem.deviceInfoList.length) {
            detail.checkItemDeviceMaps = {
              ...detail.checkItemDeviceMaps,
              [id]: {
                ...detail.checkItemDeviceMaps[id],
                [deviceItem.deviceType]: deviceItem.deviceInfoList,
              },
            };
            deviceGuids.push(...deviceItem.deviceInfoList.map(item => item.deviceGuid));
          }
          const list = deviceItem.checkItemInfoList.map((checkItem, index) => {
            if (index === 0) {
              return {
                deviceType: deviceItem.deviceType,
                ...checkItem,
                merge: deviceItem.checkItemInfoList.length,
                limitOfEstimation: getLimitOfEstimation({
                  dataType: checkItem.dataType,
                  operatorList: checkItem.operatorList,
                  expectedValue:
                    checkItem.expectedValue !== null && checkItem.dataType === 'DI'
                      ? checkItem.expectedValue.split(',')
                      : checkItem.expectedValue,
                }),
                last: index === deviceItem.checkItemInfoList.length - 1 ? true : false,
                limitOfEstimationOperatorList: checkItem.operatorList,
                metaCode: checkItem.pointCode
                  ? `${deviceItem.deviceType}_$$_${checkItem.pointCode}`
                  : `${deviceItem.deviceType}_$$_${checkItem.pointName}`,
                expectedValue:
                  checkItem.expectedValue !== null && checkItem.dataType === 'DI'
                    ? checkItem.expectedValue.split(',')
                    : checkItem.expectedValue,
              };
            }
            return {
              deviceType: deviceItem.deviceType,
              ...checkItem,
              merge: 0,
              limitOfEstimation: getLimitOfEstimation({
                dataType: checkItem.dataType,
                operatorList: checkItem.operatorList,
                expectedValue:
                  checkItem.expectedValue !== null && checkItem.dataType === 'DI'
                    ? checkItem.expectedValue.split(',')
                    : checkItem.expectedValue,
              }),
              last: index === deviceItem.checkItemInfoList.length - 1 ? true : false,
              limitOfEstimationOperatorList: checkItem.operatorList,
              metaCode: checkItem.pointCode
                ? `${deviceItem.deviceType}_$$_${checkItem.pointCode}`
                : `${deviceItem.deviceType}_$$_${checkItem.pointName}}`,
              expectedValue:
                checkItem.expectedValue !== null && checkItem.dataType === 'DI'
                  ? checkItem.expectedValue.split(',')
                  : checkItem.expectedValue,
            };
          });
          checkItemsMap[id].push(...list);
        });
      }
      // else {
      //   checkItemsMap = {...checkItemsMap};
      // }
    });
  }
  for (const i in checkItemsMap) {
    checkItemsMap[i] = checkItemsMap[i].map(item => {
      return { ...item, maxInfluencesStep: stepCodes[item.maxInfluencesStep - 1] };
    });
  }

  detail.step2.stepCodes = stepCodes;
  detail.step2.stepMaps = stepMaps;
  detail.checkItemsMap = checkItemsMap;
  detail.deviceGuids = deviceGuids;
  if (type === 'byTemplate') {
    detail.matchObjectInfoMaps = {};
    detail.checkItemDeviceMaps = {};
  }
  return detail;
}

function getSaveSubmitParams({
  step1,
  stepCodes,
  stepMaps,
  checkItemsMap,
  checkItemDeviceMaps,
  matchObjectInfoMaps,
  type,
  isNewTicket,
}) {
  let params = {};
  if (type === 'template') {
    params = {
      riskLevel: step1.riskLevel?.value,
      templateName: step1.templateName?.value,
      executeRoleCode: step1.executeRole.value?.key,
      executeRoleName: step1.executeRole.value?.label,
      changeType: step1.changeType.value?.key,
      fileInfoList:
        step1.fileInfoList &&
        Array.isArray(step1.fileInfoList.value) &&
        step1.fileInfoList.value.length
          ? step1.fileInfoList.value.map(obj => McUploadFile.toApiObject(obj))
          : [],
    };
  }
  if (type === 'ticket') {
    // 编辑时传null后端无法将templateId，需要特殊值 NULL 来删除templateId
    let templateId = 'NULL';
    let templateName = 'NULL';
    if (step1.templateId && step1.templateId.value && step1.templateId.value.key) {
      templateId = step1.templateId.value.key;
      templateName = step1.templateId.value.label;
    }
    if (!step1.templateId.value?.key && isNewTicket) {
      templateId = null;
      templateName = null;
    }
    params = {
      riskLevel: step1.riskLevel?.value,
      changeType: step1.changeType.value?.key,
      title: step1.title?.value,
      idcTag: step1.location?.value[0],
      blockTag: `${step1.location?.value?.[0]}.${step1.location?.value?.[1]}`,
      templateId,
      templateName,
      planStartTime:
        step1.planStartTime &&
        step1.planStartTime.value &&
        Number(step1.planStartTime.value.unix() + '000'),
      planEndTime:
        step1.planEndTime &&
        step1.planEndTime.value &&
        Number(step1.planEndTime.value.unix() + '000'),
      reason: step1.reason && step1.reason.value?.label,
      exeUserGroupCode: step1.exeUserGroupCode && step1.exeUserGroupCode.value?.key,
      exeUserGroupName: step1.exeUserGroupCode && step1.exeUserGroupCode.value?.label,
      fileInfoList:
        step1.fileInfoList &&
        Array.isArray(step1.fileInfoList.value) &&
        step1.fileInfoList.value.length
          ? step1.fileInfoList.value.map(obj => McUploadFile.toApiObject(obj))
          : [],
      customerFileInfoList:
        step1.customerFileInfoList &&
        Array.isArray(step1.customerFileInfoList.value) &&
        step1.customerFileInfoList.value.length
          ? step1.customerFileInfoList.value.map(obj => McUploadFile.toApiObject(obj))
          : [],
    };
  }
  let stepList = [];
  const stepFileInfoList = [];

  if (stepCodes.length) {
    stepList = stepCodes.map((item, index) => {
      const stepOrder = index + 1;
      const stepObj = {
        ...stepMaps[item],
        stepOrder,
        checkDeviceInfoList: checkItemsMap
          ? getCheckDeviceInfoList(item, checkItemsMap, checkItemDeviceMaps, stepCodes)
          : [],
      };
      if (
        stepMaps[item].stepType === CHANGE_STEP_TYPE_KEY_MAP.RUN &&
        Array.isArray(stepMaps[item].opObjectName)
      ) {
        stepObj.opObjectName = stepMaps[item].opObjectName.join('|');
        stepObj.opObjectCode = stepMaps[item].opObjectCode.join('|');
      }
      if (matchObjectInfoMaps && matchObjectInfoMaps[item]) {
        stepObj.matchObjectInfoList = matchObjectInfoMaps[item];
      }
      if (Array.isArray(stepMaps[item].fileInfoList)) {
        stepFileInfoList.push({
          stepOrder,
          fileInfoList: stepMaps[item].fileInfoList.map(obj => McUploadFile.toApiObject(obj)),
        });
      }
      return stepObj;
    });
  }
  params.stepList = stepList;
  params.stepFileInfoList = stepFileInfoList;
  return params;
}

function getMaxInfluencesStep(maxInfluencesStep, stepCodes) {
  let order = null;
  stepCodes.forEach((item, index) => {
    if (item === maxInfluencesStep) {
      order = index + 1;
    }
  });
  return order;
}

function getCheckDeviceInfoList(stepId, checkItemsMap, checkItemDeviceMaps, stepCodes) {
  const checkItems = checkItemsMap[stepId];
  if (!checkItems || (Array.isArray(checkItems) && !checkItems.length)) {
    return null;
  }
  const dataCenterCitySchema = [
    new schema.Entity(
      'normalizedList',
      {},
      {
        idAttribute: 'deviceType',
        processStrategy: data => {
          return [
            {
              ...data,
            },
          ];
        },
        mergeStrategy: (entityA, entityB) => {
          return [...entityA, ...entityB];
        },
      }
    ),
  ];
  const normalizedList = normalize(checkItems, dataCenterCitySchema).entities.normalizedList;
  const checkDeviceInfoList = Object.entries(normalizedList).map(item => {
    const checkDeviceInfoObj = {
      deviceType: item[0],
      checkItemInfoList: item[1].map(checkItem => {
        return {
          ...checkItem,
          maxInfluencesStep: getMaxInfluencesStep(checkItem.maxInfluencesStep, stepCodes),
          expectedValue:
            Array.isArray(checkItem.expectedValue) && checkItem.dataType === 'DI'
              ? checkItem.expectedValue.join(',')
              : checkItem.expectedValue,
        };
      }),
    };
    if (checkItemDeviceMaps && checkItemDeviceMaps[stepId]) {
      checkDeviceInfoObj.deviceInfoList = checkItemDeviceMaps[stepId][item[0]];
    }
    return checkDeviceInfoObj;
  });
  return checkDeviceInfoList;
}

function* ticketDetailAlarmList({
  pageNum,
  pageSize,
  deviceName,
  changeId,
  isExpected,
  onFetchFinish,
  idcTag,
  blockTag,
}) {
  const params = yield select(getTicketDetailAlarm);
  const { data, error } = yield call(fetchAlarms, {
    ...params,
    status: 'REMOVED',
    idcTag: idcTag ?? params.idcTag,
    blockTags: blockTag ? [blockTag] : params.blockTags,
    isMerge: true,
    pageNum,
    pageSize,
    deviceName,
    changeId,
    isExpected,
    isQueryData: true,
  });
  onFetchFinish && onFetchFinish(data);
  if (error) {
    message.error(error.message);
    yield put(changeActions.failure());
  } else {
    yield put(
      changeActions.setTicketDetailAlarmList({
        list: data.data.map(Alarm.toApiObject),
        total: data.total,
        pageNum,
        pageSize,
      })
    );
  }
}

function* alarmLevelCount({ changeId, idcTag, blockTag, isExpected, onFetchFinish }) {
  const params = yield select(getTicketDetailAlarm);
  const { response, error } = yield call(changeService.ticketAlarmLevelCount, {
    changeId,
    idcTag: idcTag ?? params.idcTag,
    // triggerTime: params.triggerTime,
    blockTag: blockTag ?? params.blockTags.join(','),
    // triggerTimeEnd: params.triggerTimeEnd,
  });
  if (error) {
    message.error(error);
    onFetchFinish && onFetchFinish(0);
    yield put(changeActions.failure());
  } else {
    onFetchFinish &&
      onFetchFinish((response.expect?.totalCount || 0) + (response[changeId]?.totalCount || 0));
    const list = [
      {
        expected: false,
        selected: isExpected === false,
        type: '预期外关联告警',
        ...response.expect,
      },
      {
        expected: true,
        selected: isExpected === true,
        type: '预期内告警',
        ...response[changeId],
      },
    ];
    yield put(changeActions.setAlarmLevelCount(list));
  }
}

function* ticketStepDetail({ changeOrderId }) {
  const { response, error } = yield call(changeService.ticketStepDetail, changeOrderId);
  if (error) {
    message.error(error);
    yield put(changeActions.failure());
  } else {
    const { stepCodes } = yield select(getTicketDetailStepCodes);
    const {
      stepList,
      checkItemInfoMaps,
      opItemInfoMaps,
      deviceGuids,
      pointsData,
      pointsDefinition,
    } = processStepDetailData(response.data, stepCodes);
    yield put(
      changeActions.setTicketStepDetail({
        stepList,
        stepOrder: stepList[0].stepOrder,
        checkItemInfoMaps,
        opItemInfoMaps,
        deviceGuids,
        pointValue: { pointsData },
        selectedCheckItemInfoMaps: checkItemInfoMaps,
        pointsDefinition,
      })
    );
  }
}

function processStepDetailData(data, stepCodes) {
  const opItemInfoMaps = {};
  const checkItemInfoMaps = {};
  const pointsDefinition = [];
  const deviceGuids = [];
  const pointsData = {};
  const stepList = data.map(item => {
    const checkItemInfoList = [];
    item.checkDeviceInfoList.forEach(checkDeviceInfoItem => {
      checkDeviceInfoItem.deviceInfoList.forEach(deviceInfoItem => {
        const deviceInfoList = checkDeviceInfoItem.checkItemInfoList
          .map(checkItemInfoItem => {
            if (checkItemInfoItem.deviceGuid === deviceInfoItem.deviceGuid) {
              if (checkItemInfoItem.pointCode) {
                deviceGuids.push({
                  deviceGuid: checkItemInfoItem.deviceGuid,
                  pointCode: checkItemInfoItem.pointCode,
                });
                pointsDefinition.push(
                  `${checkDeviceInfoItem.deviceType}.${checkItemInfoItem.pointCode}`
                );
              }
              let pointValueMap = {};
              if (pointsData[checkItemInfoItem.deviceGuid]) {
                pointValueMap = pointsData[checkItemInfoItem.deviceGuid]['pointValueMap'];
              }
              pointsData[checkItemInfoItem.deviceGuid] = {
                deviceType: checkDeviceInfoItem.deviceType,
                pointValueMap: {
                  ...pointValueMap,
                  [checkItemInfoItem.pointCode]: {
                    value: checkItemInfoItem.pointData,
                  },
                },
              };
              return {
                ...checkItemInfoItem,
                expectedValue:
                  checkItemInfoItem.expectedValue !== null && checkItemInfoItem.dataType === 'DI'
                    ? checkItemInfoItem.expectedValue.split(',')
                    : checkItemInfoItem.expectedValue,
                deviceTag: deviceInfoItem.deviceTag,
                deviceGuid: deviceInfoItem.deviceGuid,
                deviceType: checkDeviceInfoItem.deviceType,
              };
            }
            return null;
          })
          .filter(Boolean);
        checkItemInfoList.push(...deviceInfoList);
      });
    });
    if (item.stepType === CHANGE_STEP_TYPE_KEY_MAP.CUSTOMIZE) {
      opItemInfoMaps[item.stepOrder] = [
        {
          fileInfoList:
            Array.isArray(item.fileInfoList) && item.fileInfoList.length
              ? item.fileInfoList.map(obj => McUploadFile.fromApiObject(obj))
              : [],
          itemStatus: item.stepStatus,
          operatorName: item.operatorName,
          operatorId: item.operatorId,
          opTime: item.endTime,
        },
      ];
    } else {
      opItemInfoMaps[item.stepOrder] =
        Array.isArray(item.opItemInfoList) &&
        item.opItemInfoList.map(opItemInfoItem => {
          if (item.stepType === 'OP' && item.opType === 'LIMIT') {
            if (opItemInfoItem.pointCode) {
              deviceGuids.push({
                deviceGuid: opItemInfoItem.itemCode,
                pointCode: opItemInfoItem.pointCode,
              });
              pointsDefinition.push(`${item.opObjectCode}.${opItemInfoItem.pointCode}`);
            }
            let pointValueMap = {};
            if (pointsData[opItemInfoItem.itemCode]) {
              pointValueMap = pointsData[opItemInfoItem.itemCode]['pointValueMap'];
            }
            pointsData[opItemInfoItem.itemCode] = {
              deviceType: item.opObjectCode,
              pointValueMap: {
                ...pointValueMap,
                [opItemInfoItem.pointCode]: {
                  value: opItemInfoItem.pointData,
                },
              },
            };
          }
          return {
            ...opItemInfoItem,
            deviceType: item.opObjectCode,
          };
        });
    }
    checkItemInfoMaps[item.stepOrder] = checkItemInfoList;
    return {
      ...omit(item, 'opItemInfoList', 'checkDeviceInfoList'),
    };
  });
  return {
    stepList,
    checkItemInfoMaps,
    opItemInfoMaps,
    pointsDefinition: uniq(pointsDefinition),
    pointsData,
    deviceGuids,
  };
}

function* getRealtimeData() {
  const { idcTag, deviceGuids = [] } = yield select(getDetailStepPointCode);

  const { error, data } = yield call(fetchPointValues, {
    idc: idcTag,
    pointGuids: deviceGuids.reduce((mappings, { deviceGuid, pointCode }) => {
      if (mappings[deviceGuid] === undefined) {
        mappings[deviceGuid] = pointCode;
      } else {
        mappings[deviceGuid] += ',' + pointCode;
      }

      return mappings;
    }, {}),
  });

  if (error) {
    message.error(error.message);
    return;
  }
  yield put(subscriptionsSliceActions.receivedRealtimeData(data));

  const { timeout } = yield race({
    shouldCancel: take(cancelTicketRealtimeData.type),
    timeout: delay(5 * 1000 * (error ? 2 : 1)), // 遇到 error 时，延长一下下一次请求发起的时间
  });

  if (timeout) {
    yield put(getRealtimeDataAction());
  }
}

function* startOperation({ changeOrderId, stepOrder, stepId }) {
  const { response, error } = yield call(changeService.startOperation, {
    changeOrderId,
    stepOrder,
    stepId,
  });
  if (error) {
    message.error(error);
    // yield put(changeActions.failure());
  } else if (response) {
    yield fork(ticketStepDetail, { changeOrderId });
    yield fork(ticketDetailInfo, { id: changeOrderId, mode: 'ticketDetail' });
    // yield put(cancelTicketRealtimeData());
  }
}

function* stepChecked({ changeOrderId, stepOrder, stepId }) {
  const { checkItemInfoMaps, opItemInfoMaps } = yield select(getOPCheckItems);
  let checkItemInfoList = checkItemInfoMaps[stepOrder];
  let opItemInfoList = opItemInfoMaps[stepOrder];

  yield put(
    changeActions.setTicketStepItemStatus({
      lastCheckItemInfoList: checkItemInfoList,
      lastOpItemInfoList: opItemInfoList,
    })
  );
  if (checkItemInfoList) {
    checkItemInfoList = checkItemInfoList.map(item => {
      if (item.identifyWay === 'SYSTEM') {
        return {
          ...item,
          itemStatus: CHANGE_OP_ITEM_STATUS_KEY_MAP.CHECKING,
        };
      }
      return item;
    });
  }
  if (opItemInfoList) {
    opItemInfoList = opItemInfoList.map(item => {
      return {
        ...item,
        itemStatus: CHANGE_OP_ITEM_STATUS_KEY_MAP.CHECKING,
      };
    });
  }
  yield put(
    changeActions.setTicketStepDetail({
      checkItemInfoMaps: { ...checkItemInfoMaps, [stepOrder]: checkItemInfoList },
      opItemInfoMaps: { ...opItemInfoMaps, [stepOrder]: opItemInfoList },
    })
  );
  const { response, errorCoe, error } = yield call(changeService.stepChecked, {
    changeOrderId,
    stepOrder,
    stepId,
  });
  if (error) {
    message.error(error);
    const { lastCheckItemInfoList, lastOpItemInfoList } = yield select(getCheckItemsStatus);
    yield put(
      changeActions.setTicketStepDetail({
        checkItemInfoMaps: { ...checkItemInfoMaps, [stepOrder]: lastCheckItemInfoList },
        opItemInfoMaps: { ...opItemInfoMaps, [stepOrder]: lastOpItemInfoList },
      })
    );
  }
  if (errorCoe === 'TIMEOUT') {
    const { lastCheckItemInfoList, lastOpItemInfoList } = yield select(getCheckItemsStatus);
    yield put(
      changeActions.setTicketStepDetail({
        checkItemInfoMaps: { ...checkItemInfoMaps, [stepOrder]: lastCheckItemInfoList },
        opItemInfoMaps: { ...opItemInfoMaps, [stepOrder]: lastOpItemInfoList },
      })
    );
    message.error('检查超时，请重试！');
  } else if (response) {
    const { checkItemResultList, isOrderEnd, opItemResultList } = response;
    if (isOrderEnd) {
      yield fork(ticketStepDetail, { changeOrderId });
      yield fork(ticketDetailInfo, { id: changeOrderId, mode: 'ticketDetail' });
    } else {
      const result = getItemResult({
        checkItemInfoMaps,
        opItemInfoMaps,
        stepOrder,
        checkItemResultList,
        isOrderEnd,
        opItemResultList,
      });

      yield put(subscriptionsSliceActions.receivedRealtimeData(result.realtimeData));
      yield put(
        changeActions.setTicketStepDetail({
          checkItemInfoMaps: { ...checkItemInfoMaps, [stepOrder]: result.checkItemInfoList },
          selectedCheckItemInfoMaps: {
            ...checkItemInfoMaps,
            [stepOrder]: result.checkItemInfoList,
          },
          opItemInfoMaps: { ...opItemInfoMaps, [stepOrder]: result.opItemInfoList },
          // pointValue: { pointsData: result.pointsData },
        })
      );
    }
    yield put(cancelTicketRealtimeData());
  }
}
function getItemResult({
  checkItemInfoMaps,
  opItemInfoMaps,
  stepOrder,
  checkItemResultList,
  opItemResultList,
}) {
  let checkItemInfoList = checkItemInfoMaps[stepOrder] || [];
  let opItemInfoList = opItemInfoMaps[stepOrder] || [];
  const pointsData = [];
  if (checkItemInfoList) {
    const { infoList, newPointsData } = getItemInfoList(
      checkItemInfoList,
      checkItemResultList,
      'checkItemId',
      'deviceGuid'
    );
    checkItemInfoList = infoList;
    pointsData.push(...newPointsData);
  }
  if (opItemInfoList) {
    const { infoList, newPointsData } = getItemInfoList(
      opItemInfoList,
      opItemResultList,
      'opItemId',
      'itemCode'
    );
    opItemInfoList = infoList;
    pointsData.push(...newPointsData);

    // opItemInfoList = opItemInfoList.map(item => {
    //   const { checkResult = null, pointValue = null } =
    //     opItemNormalizedList[item.checkItemId] || {};
    //     pointsData[item.deviceGuid]={
    //       deviceType:item.deviceType,
    //       pointValueMap:{
    //         [item.pointCode]:{
    //           value:pointValue
    //         }
    //       }
    //     }
    //   return {
    //     ...item,
    //     itemStatus: checkResult,
    //     pointData: pointValue,
    //   };
    // });
  }
  const realtimeData = pointsData.reduce((mappings, { deviceGuid, pointCode, pointValue }) => {
    if (mappings[deviceGuid] === undefined) {
      mappings[deviceGuid] = {
        time: new Date().getTime(),
        pointValues: {
          [pointCode]: { value: pointValue, status: null },
        },
      };
    } else {
      mappings[deviceGuid].pointValues[pointCode] = { value: pointValue, status: null };
    }
    return mappings;
  }, {});
  return { opItemInfoList, checkItemInfoList, realtimeData };
}

function getItemInfoList(itemInfoList, itemResultList, idName, deviceName) {
  const newPointsData = [];
  const itemInfoListSchema = [
    new schema.Entity(
      'normalizedList',
      {},
      {
        idAttribute: idName,
      }
    ),
  ];
  const itemNormalizedList = itemResultList
    ? normalize(itemResultList, itemInfoListSchema).entities.normalizedList
    : {};
  const infoList = itemInfoList.map(item => {
    const { checkResult = null, pointValue = null } = itemNormalizedList[item[idName]] || {};
    if (!item.pointCode) {
      return item;
    }
    newPointsData.push({
      deviceGuid: item[deviceName],
      pointCode: item.pointCode,
      pointValue: pointValue,
    });

    return {
      ...item,
      itemStatus: checkResult,
      pointData: pointValue,
    };
  });
  return { infoList, newPointsData };
}

function* stepSkip({ param: { changeOrderId, stepOrder, stepId, jumpReason }, successCallback }) {
  const { response, error } = yield call(changeService.stepSkip, {
    changeOrderId,
    stepOrder,
    stepId,
    jumpReason,
  });
  if (error) {
    message.error(error);
  } else if (response) {
    yield put(getRealtimeDataAction());
    successCallback();
    yield fork(ticketDetailInfo, { id: changeOrderId, mode: 'ticketDetail' });
    yield fork(ticketStepDetail, { changeOrderId });
  }
}

function* stepStop(params) {
  const { response, error } = yield call(changeService.stepStop, params);
  if (error) {
    message.error(error);
  } else if (response) {
    yield put(getRealtimeDataAction());
    yield fork(ticketStepDetail, { changeOrderId: params.changeOrderId });
    yield fork(ticketDetailInfo, { id: params.changeOrderId, mode: 'ticketDetail' });
  }
}

function* checkItemArtificialValidation({ changeOrderId, itemStatus, checkItemId }) {
  const { response, error } = yield call(changeService.checkItemArtificialValidation, {
    changeOrderId,
    itemStatus,
    checkItemId,
  });
  if (error) {
    message.error(error);
  } else {
    if (response) {
      // true工单为终止状态 需要刷新详情接口
      yield fork(ticketStepDetail, { changeOrderId });
      yield fork(ticketDetailInfo, { id: changeOrderId, mode: 'ticketDetail' });
    } else {
      const { checkItemInfoMaps, stepOrder, selectedCheckItemInfoMaps } =
        yield select(getOPCheckItems);
      const checkItemInfo = checkItemInfoMaps[stepOrder];
      const list = checkItemInfo.map(item => {
        if (item.checkItemId === checkItemId) {
          return {
            ...item,
            itemStatus,
          };
        }
        return item;
      });
      yield put(
        changeActions.setTicketStepDetail({
          checkItemInfoMaps: { ...checkItemInfoMaps, [stepOrder]: list },
          selectedCheckItemInfoMaps: { ...selectedCheckItemInfoMaps, [stepOrder]: list },
        })
      );
    }
  }
}

function* opArtificialValidation({ changeOrderId, opItemId, opResult }) {
  const { response, error } = yield call(changeService.opArtificialValidation, {
    changeOrderId,
    opItemId,
    opResult,
  });
  if (error) {
    message.error(error);
  } else {
    if (response) {
      yield fork(ticketStepDetail, { changeOrderId });
      yield fork(ticketDetailInfo, { id: changeOrderId, mode: 'ticketDetail' });
    } else {
      const { opItemInfoMaps, stepOrder } = yield select(getOPCheckItems);
      const checkItemInfo = opItemInfoMaps[stepOrder];
      const list = checkItemInfo.map(item => {
        if (item.opItemId === opItemId) {
          return {
            ...item,
            itemStatus: opResult,
          };
        }
        return item;
      });
      yield put(
        changeActions.setTicketStepDetail({
          opItemInfoMaps: { ...opItemInfoMaps, [stepOrder]: list },
        })
      );
    }
  }
}

function* ticketApprovalOrRevert({ changeOrderId }) {
  const { response, error } = yield call(changeService.ticketApprovalOrRevert, {
    changeOrderId,
  });
  if (error) {
    message.error(error);
  } else if (response) {
    yield fork(getTicketData);
  }
}

function* getTicketCopyInfo({ id }) {
  const { response, error } = yield call(changeService.ticketCopyDetail, id);
  if (error) {
    message.error(error);
  } else {
    const detail = templateDetailInfo(response, 'ticket');
    yield put(changeActions.setTicketInfo(detail));
  }
}

function* getTemplateCopyInfo({ id }) {
  const { response, error } = yield call(changeService.getTemplateDetailInfo, id);
  if (error) {
    message.error(error);
  } else {
    const detail = templateDetailInfo(response, 'template');
    yield put(changeActions.setTemplateCopyInfo(detail));
  }
}

function* approval({ bizId, approvalBusinessType, approvalResult, type }) {
  const { error } = yield call(changeService.approval, {
    bizId,
    approvalBusinessType,
    approvalResult,
  });
  if (error) {
    message.error(error);
  } else {
    if (type === 'template') {
      yield fork(getTmplateDetailInfo, { id: bizId, mode: 'detail' });
    }
    if (type === 'ticket') {
      yield fork(ticketDetailInfo, { id: bizId, mode: 'ticketDetail' });
    }
  }
}

function* summerySubmit({ params, successCallback }) {
  const { response, error } = yield call(changeService.summerySubmit, params);
  if (error) {
    message.error(error);
    // yield put(changeActions.failure());
  } else if (response) {
    successCallback();

    yield fork(ticketDetailInfo, { id: params.changeOrderId, mode: 'ticketDetail' });
    yield put(changeActions.setTabCurrent('ticketInfo'));
  }
}

function* stopChange({ changeOrderId, stopReason }) {
  const { response, error } = yield call(changeService.stopChange, {
    changeOrderId,
    stopReason,
  });
  if (error) {
    message.error(error);
  } else if (response) {
    message.success('变更已终止');
    yield put(getRealtimeDataAction());
    yield fork(ticketStepDetail, { changeOrderId });
    yield fork(ticketDetailInfo, { id: changeOrderId, mode: 'ticketDetail' });
  }
}

function* templateApprovalOrRevert({ templateId }) {
  const { response, error } = yield call(changeService.templateApprovalOrRevert, {
    templateId,
  });
  if (error) {
    message.error(error);
  } else if (response) {
    yield fork(getChangeData);
  }
}

function* ticketClose({ changeOrderId, type }) {
  const { response, error } = yield call(changeService.ticketClose, {
    changeOrderId,
  });
  if (error) {
    message.error(error);
  } else if (response) {
    message.success('变更已关闭');
    if (type === 'list') {
      yield fork(getTicketData);
    }
    if (type === 'detail') {
      yield fork(ticketStepDetail, { changeOrderId });
      yield fork(ticketDetailInfo, { id: changeOrderId, mode: 'ticketDetail' });
    }
  }
}

function* deleteTemplate({ templateId }) {
  const { response, error } = yield call(changeService.deleteTemplate, {
    templateId,
  });
  if (error) {
    message.error(error);
  } else if (response) {
    message.success('变更模板已删除');
    yield fork(getChangeData);
    // yield fork(ticketDetailInfo, { id: changeOrderId, mode: 'ticketDetail' });
  }
}
/******************************************************************************/
/******************************* WATCHERS *************************************/
/******************************************************************************/

function* watchGetChangeData() {
  while (true) {
    const { payload } = yield take(getChangeDataActionCreator.type);
    const task = yield fork(getChangeData, payload);
    const [reset] = yield race([take(resetSearchValuesActionCreator.type), task]);
    if (reset) {
      cancel(task);
    }
  }
}

function* watchResetSearchValues() {
  while (true) {
    yield take(resetSearchValuesActionCreator.type);
    yield fork(resetSearchValues);
  }
}

function* watchSetPagination() {
  while (true) {
    const { payload } = yield take(setPaginationThenGetDataActionCreator.type);
    yield fork(setPagination, payload);
  }
}

function* watchResetTicketSearchValues() {
  while (true) {
    yield take(resetTicketSearchValuesActionCreator.type);
    yield fork(resetTicketSearchValues);
  }
}

function* watchGetTicketData() {
  while (true) {
    const { payload } = yield take(getTicketDataActionCreator.type);
    const task = yield fork(getTicketData, payload);
    const [reset] = yield race([take(resetTicketSearchValuesActionCreator.type), task]);
    if (reset) {
      cancel(task);
    }
  }
}

function* watchSetTicketPagination() {
  while (true) {
    const { payload } = yield take(setTicketPaginationThenGetDataActionCreator.type);
    yield fork(setTicketPagination, payload);
  }
}

function* watchTemplateSave() {
  while (true) {
    const { payload } = yield take(setTemplateSaveActionCreator.type);
    yield fork(setTemplateSave, payload);
  }
}

function* watchTemplateSubmit() {
  while (true) {
    const { payload } = yield take(setTemplateSubmitActionCreator.type);
    yield fork(setTemplateSubmit, payload);
  }
}

function* watchGetTemplateDetailInfo() {
  while (true) {
    const { payload } = yield take(getTemplateDetailInfo.type);
    yield fork(getTmplateDetailInfo, payload);
  }
}

function* watchGetTemplateEditDetailInfo() {
  while (true) {
    const { payload } = yield take(getTemplateEditDetailInfoActionCreator.type);
    yield fork(getTemplateEditDetailInfo, payload);
  }
}

function* watchTicketSave() {
  while (true) {
    const { payload } = yield take(setTicketSaveActionCreator.type);
    yield fork(setTicketSave, payload);
  }
}

function* watchTicketSubmit() {
  while (true) {
    const { payload } = yield take(setTicketSubmitActionCreator.type);
    yield fork(setTicketSubmit, payload);
  }
}

function* watchGetTicketDetailInfo() {
  while (true) {
    const { payload } = yield take(getTicketDetailInfo.type);
    yield fork(ticketDetailInfo, payload);
  }
}

function* watchGetTicketDetailAlarmList() {
  while (true) {
    const { payload } = yield take(getTicketDetailAlarmList.type);
    yield fork(ticketDetailAlarmList, payload);
  }
}

function* watchGetAlarmLevelCount() {
  while (true) {
    const { payload } = yield take(getAlarmLevelCount.type);
    yield fork(alarmLevelCount, payload);
  }
}

function* watchGetTicketStepDetail() {
  while (true) {
    const { payload } = yield take(getTicketStepDetail.type);
    yield fork(ticketStepDetail, payload);
  }
}

function* watchGetRealtimeData() {
  while (true) {
    const { payload } = yield take(getRealtimeDataAction.type);
    yield fork(getRealtimeData, payload);
  }
}

function* watchStartOperation() {
  while (true) {
    const { payload } = yield take(startOperationActionCreator.type);
    yield fork(startOperation, payload);
  }
}

function* watchStepChecked() {
  while (true) {
    const { payload } = yield take(stepCheckedActionCreator.type);
    yield fork(stepChecked, payload);
  }
}

function* watchStepSkip() {
  while (true) {
    const { payload } = yield take(stepSkipActionCreator.type);
    yield fork(stepSkip, payload);
  }
}

function* watchStepStop() {
  while (true) {
    const { payload } = yield take(stepStopActionCreator.type);
    yield fork(stepStop, payload);
  }
}

function* watchCheckItemArtificialValidation() {
  while (true) {
    const { payload } = yield take(checkItemArtificialValidationActionCreator.type);
    yield fork(checkItemArtificialValidation, payload);
  }
}

function* watchOpArtificialValidation() {
  while (true) {
    const { payload } = yield take(opArtificialValidationActionCreator.type);
    yield fork(opArtificialValidation, payload);
  }
}

function* watchTicketApprovalOrRevert() {
  while (true) {
    const { payload } = yield take(ticketApprovalOrRevertActionCreator.type);
    yield fork(ticketApprovalOrRevert, payload);
  }
}

function* watchGetTicketCopyInfo() {
  while (true) {
    const { payload } = yield take(getTicketCopyInfoActionCreator.type);
    yield fork(getTicketCopyInfo, payload);
  }
}

function* watchGetTemplateCopyInfo() {
  while (true) {
    const { payload } = yield take(getTemplateCopyInfoActionCreator.type);
    yield fork(getTemplateCopyInfo, payload);
  }
}

function* watchApproval() {
  while (true) {
    const { payload } = yield take(approvalActionCreator.type);
    yield fork(approval, payload);
  }
}

function* watchSummerySubmit() {
  while (true) {
    const { payload } = yield take(summerySubmitActionCreator.type);
    yield fork(summerySubmit, payload);
  }
}

function* watchStopChange() {
  while (true) {
    const { payload } = yield take(stopChangeActionCreator.type);
    yield fork(stopChange, payload);
  }
}

function* watchTemplateApprovalOrRevert() {
  while (true) {
    const { payload } = yield take(templateApprovalOrRevertActionCreator.type);
    yield fork(templateApprovalOrRevert, payload);
  }
}

function* watchTicketClose() {
  while (true) {
    const { payload } = yield take(ticketCloseActionCreator.type);
    yield fork(ticketClose, payload);
  }
}

function* watchDeleteTemplate() {
  while (true) {
    const { payload } = yield take(deleteTemplateActionCreator.type);
    yield fork(deleteTemplate, payload);
  }
}
export default [
  fork(watchResetSearchValues),
  fork(watchGetChangeData),
  fork(watchSetPagination),
  fork(watchResetTicketSearchValues),
  fork(watchGetTicketData),
  fork(watchSetTicketPagination),
  fork(watchTemplateSave),
  fork(watchTemplateSubmit),
  fork(watchGetTemplateDetailInfo),
  fork(watchTicketSave),
  fork(watchTicketSubmit),
  fork(watchGetTicketDetailInfo),
  fork(watchGetTicketDetailAlarmList),
  fork(watchGetAlarmLevelCount),
  fork(watchGetTicketStepDetail),
  fork(watchGetRealtimeData),
  fork(watchStartOperation),
  fork(watchStepChecked),
  fork(watchStepSkip),
  fork(watchStepStop),
  fork(watchCheckItemArtificialValidation),
  fork(watchOpArtificialValidation),
  fork(watchTicketApprovalOrRevert),
  fork(watchGetTicketCopyInfo),
  fork(watchApproval),
  fork(watchSummerySubmit),
  fork(watchStopChange),
  fork(watchTemplateApprovalOrRevert),
  fork(watchTicketClose),
  fork(watchDeleteTemplate),
  fork(watchGetTemplateEditDetailInfo),
  fork(watchGetTemplateCopyInfo),
];
