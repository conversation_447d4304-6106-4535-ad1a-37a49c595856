import { normalize, schema } from 'normalizr';
import { call, fork, put, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';

import { METADATA_TYPE } from '@manyun/dc-brain.legacy.constants/index';
import { eventCenterService } from '@manyun/dc-brain.legacy.services';
import { fetchMetadataByType } from '@manyun/dc-brain.legacy.services/metadataConfigurationService';

import {
  eventCenterActions,
  getEventDetail,
  getEventLife,
  getEventList,
  getMetadataByType,
  getProgressUpdateList,
} from '../actions/eventCenterActions';

function* eventList(payload) {
  yield put(eventCenterActions.request());
  const { response, error } = yield call(eventCenterService.fetchEventList, payload);
  if (response) {
    yield put(
      eventCenterActions.fetchEventCenterSuccess({
        total: response.total,
        list: response.data,
        pageNum: payload.pageNum,
        pageSize: payload.pageSize,
      })
    );
  } else {
    message.error(error || '获取事件列表失败');
    yield put(eventCenterActions.failure());
  }
}

function* progressUpdateList(payload) {
  yield put(eventCenterActions.request());
  const { response, error } = yield call(eventCenterService.fetchProgressUpdateList, payload);
  if (response) {
    yield put(
      eventCenterActions.progressUpdateListSuccess({
        total: response.total,
        list: response.data,
        pageNum: payload.pageNum,
        pageSize: payload.pageSize,
      })
    );
  } else {
    message.error(error || '获取事件列表失败');
    yield put(eventCenterActions.failure());
  }
}

function* eventDetail(id) {
  yield put(eventCenterActions.request());
  const { response, error } = yield call(eventCenterService.fetchEventDetail, id);
  if (response) {
    if (response.infoType === 'OTHER') {
      response.causeDevices = response.causeDevices[0].deviceGuid;
    }
    yield put(eventCenterActions.eventDetailSuccess(response));
    yield put(
      eventCenterActions.addFileInfos(
        response.addFileInfos.map(fileInfo => {
          const file = McUploadFile.fromApiObject(fileInfo);
          return {
            ...file,
            src: file.src,
            url: file.src,
          };
        })
      )
    );
  } else {
    message.error(error || '获取事件列表失败');
    yield put(eventCenterActions.failure());
  }
}

function* eventLife(id) {
  yield put(eventCenterActions.request());
  const { response, error } = yield call(eventCenterService.fetchEventLife, id);
  if (response) {
    yield put(eventCenterActions.eventLifeSuccess(response));
  } else {
    message.error(error || '获取事件时间线失败');
    yield put(eventCenterActions.failure());
  }
}

function* metadataByType(type) {
  const { response, error } = yield call(fetchMetadataByType, type);
  if (response) {
    const getSchema = (
      options = {
        idAttribute: 'metaCode',
      }
    ) => [new schema.Entity('normalizedList', {}, options)];
    const normalizedList = normalize(response.data, getSchema()).entities.normalizedList;
    switch (type) {
      case METADATA_TYPE.EVENT_SOURCE: {
        yield put(
          eventCenterActions.eventSourceListSuccess({ list: response.data, normalizedList })
        );
        break;
      }
      case METADATA_TYPE.EVENT_TOP_CATEGORY: {
        yield put(eventCenterActions.eventTopCategoryListSuccess(response.data));
        break;
      }
      case METADATA_TYPE.EVENT_SECOND_CATEGORY: {
        yield put(eventCenterActions.eventSecondCategoryListSuccess(response.data));
        break;
      }
      case METADATA_TYPE.RESPONSIBLE_SECTOR: {
        yield put(
          eventCenterActions.eventResponsibleSectorListSuccess({
            list: response.data,
            normalizedList,
          })
        );
        break;
      }
      case METADATA_TYPE.REASON_TYPE: {
        yield put(
          eventCenterActions.eventReasonTypeListSuccess({ list: response.data, normalizedList })
        );
        break;
      }
      case METADATA_TYPE.SPEC_NAME: {
        yield put(eventCenterActions.specNameListSuccess({ list: response.data }));
        break;
      }
      case METADATA_TYPE.SPEC_UNIT: {
        yield put(eventCenterActions.specUnitListSuccess({ list: response.data }));
        break;
      }
      default:
        break;
    }
  } else {
    message.error(error);
  }
}
// function getSchema() {
//  const options = {
//     idAttribute: 'metaCode',
//   }
// ) => [new schema.Entity('normalizedList', {}, options)];
// const eventSourceNormalize = normalize(response.data, getSchema()).entities.normalizedList;}
// 事件列表
function* watchFetchEventList() {
  while (true) {
    const { payload } = yield take(getEventList.type);
    yield fork(eventList, payload);
  }
}

// 事件列表
function* watchGetProgressUpdateList() {
  while (true) {
    const { payload } = yield take(getProgressUpdateList.type);
    yield fork(progressUpdateList, payload);
  }
}

// 事件详情
function* watchGetEventDetail() {
  while (true) {
    const { payload } = yield take(getEventDetail.type);
    yield fork(eventDetail, payload);
  }
}

// 事件时间线
function* watchGetEventLife() {
  while (true) {
    const { payload } = yield take(getEventLife.type);
    yield fork(eventLife, payload);
  }
}

function* watchGetMetadataByType() {
  while (true) {
    const { payload } = yield take(getMetadataByType.type);
    yield fork(metadataByType, payload);
  }
}
export default [
  fork(watchFetchEventList),
  fork(watchGetProgressUpdateList),
  fork(watchGetEventDetail),
  fork(watchGetEventLife),
  fork(watchGetMetadataByType),
];
