import trim from 'lodash/trim';
import { call, fork, put, select, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import * as alarmTransmissionRecordService from '@manyun/dc-brain.legacy.services/alarmTransmissionRecordService';

import {
  alarmTransmissionRecordActions,
  getListAction,
  resetValuesAction,
  setPaginationAction,
} from '../actions/alarmTransmissionRecordActions';
import { getSearchValuesAndPagination } from '../selectors/alarmTransmissionRecordSelectors';

function getQ(pagination, searchValues) {
  const baseQ = { ...pagination };
  for (const [, { name, value }] of Object.entries(searchValues)) {
    if ([null, undefined, ''].includes(value) || (Array.isArray(value) && value.length === 0)) {
      continue;
    }
    if (name === 'location') {
      if (value.length === 1) {
        baseQ['idc'] = value[0];
      }
      if (value.length === 2) {
        baseQ['idc'] = value[0];
        baseQ['block'] = `${value[0]}.${value[1]}`;
      }
    } else if (name === 'alarmTime') {
      baseQ['alarmStartTime'] = value[0].valueOf();
      baseQ['alarmEndTime'] = value[1].valueOf();
    } else {
      baseQ[name] = Array.isArray(value) ? value : trim(value);
    }
  }
  return baseQ;
}

function* fetchtAlarmTransmissionRecordPage() {
  yield put(alarmTransmissionRecordActions.request());
  const { pagination, searchValues } = yield select(getSearchValuesAndPagination);
  const { response, error } = yield call(
    alarmTransmissionRecordService.fetchAlarmTransmissionRecordList,
    getQ(pagination, searchValues)
  );
  if (response) {
    yield put(alarmTransmissionRecordActions.featchAlarmTransmissionRecordOrderSuccess(response));
  } else {
    message.error(error);
    yield put(alarmTransmissionRecordActions.failure());
  }
}

function* setAlarmTransmissionRecordPagePagination(newPagination) {
  yield put(alarmTransmissionRecordActions.setAlarmTransmissionRecordPagination(newPagination));
  // yield put(getListAction());
}

function* resetValues() {
  yield put(alarmTransmissionRecordActions.resetValues());
  // yield put(getListAction());
}
/******************************************************************************/
/******************************* WATCHERS *************************************/
/******************************************************************************/

export function* watchFetchAlarmTransmissionRecordOrderList() {
  while (true) {
    yield take(getListAction.type);
    yield fork(fetchtAlarmTransmissionRecordPage);
  }
}

function* watchSetAlarmTransmissionRecordPagination() {
  while (true) {
    const { payload } = yield take(setPaginationAction.type);
    yield fork(setAlarmTransmissionRecordPagePagination, payload);
  }
}

function* watchResetValuesAction() {
  while (true) {
    const { payload } = yield take(resetValuesAction.type);
    yield fork(resetValues, payload);
  }
}

export default [
  fork(watchFetchAlarmTransmissionRecordOrderList),
  fork(watchSetAlarmTransmissionRecordPagination),
  fork(watchResetValuesAction),
];
