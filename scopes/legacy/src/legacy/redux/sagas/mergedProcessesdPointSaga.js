import isNil from 'lodash.isnil';
import cloneDeep from 'lodash/cloneDeep';
import get from 'lodash/get';
import { call, delay, fork, put, select, take } from 'redux-saga/effects';
import shortid from 'shortid';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { message } from '@manyun/base-ui.ui.message';
import { generateValidLimitsDataSource } from '@manyun/monitoring.model.point';
import { createPointInstance } from '@manyun/resource-hub.service.create-point-instance';
import { updatePointInstance } from '@manyun/resource-hub.service.update-point-instance';

import {
  POINT_DATA_TYPE_CODE_MAP,
  POINT_TYPE_CODE_MAP,
} from '@manyun/dc-brain.legacy.constants/point';
import * as urls from '@manyun/dc-brain.legacy.constants/urls';
import {
  EXPR_ITEM_TYPE, // MERGE_DIRECTION_CODE_MAP,
  ORDERED_POINT_DIMENSIONS,
  TargetPointType,
} from '@manyun/dc-brain.legacy.pages/merged-processed-point/constants';
import {
  diExprToTableData,
  generateNodeKey,
  getDifferenceData,
} from '@manyun/dc-brain.legacy.pages/merged-processed-point/utils';
import { redirectActionCreator } from '@manyun/dc-brain.legacy.redux/actions/routerActions';
import { mergedProcessesdPointService } from '@manyun/dc-brain.legacy.services';
import { flattenTreeData } from '@manyun/dc-brain.legacy.utils';
import { getSpaceDeviceTypeIfNeeded } from '@manyun/dc-brain.legacy.utils/deviceType';

import { doPointActions } from '../actions/doPointActions';
import {
  changeExpressInfos,
  createMergedPoint,
  createPressedPoint,
  dropUpdateMergedPointForm,
  exprInsertPointIntoNestedActionCreator,
  exprInsertPointIntoRootActionCreator,
  getPointDetail,
  getUpdatePressedDIStatuesList,
  mergeTheMergedPointConfig,
  mergedProcessesdPointActions,
  searchPointIsMerged,
  testExprActionCreator,
  updatePressedPointActionCreator,
} from '../actions/mergedProcessesdPointActions';
import { getDeviceTypeMetaData } from '../selectors/commonSelectors';
import {
  getCommonDeviceInfoTreeData,
  getCustomSpaceGuid,
  getExpressInfos,
  getFormulaName,
  getMergedPointData,
  getMergedPointFormData,
  getParentDeviceType,
  getPressedPointConfig,
  getPressedPointDIStatusList,
  getProcessedDeviceInfo,
  getProcessedSelectedPoint,
  getfetchDeviceInfoData,
} from '../selectors/mergedProcessesdPointSelector';

/***************************** Subroutines ************************************/

function* updateMergedPointFormDrop(payload) {
  const {
    id,
    // deviceCode,
    dragValue,
  } = payload;
  // const normalizedList = yield select(getCommonDeviceInfoTreeData);
  const pointInfoMap = yield select(getfetchDeviceInfoData);
  const disabledLeaf = generateNodeKey({
    metaCode: pointInfoMap[dragValue].metaCode,
    metaType: pointInfoMap[dragValue].metaType,
    nodeType: pointInfoMap[dragValue].nodeType,
  });
  // 若拖放进右侧 List 的测点是一个真实的原始主点位，则默认选中聚合纬度的 设备聚合 ，且不可选择其他聚合纬度
  const disabledDimensionEnums = [];
  const isOnlySelectDevice = pointInfoMap[dragValue].extCount > 0;

  //  只可以向上聚合，不可向下聚合
  let cannotSelectDimensionEnums = [];
  if (
    !isOnlySelectDevice &&
    pointInfoMap[dragValue].dimension &&
    pointInfoMap[dragValue].dimension.code
  ) {
    const thisPointDimensionEnums = pointInfoMap[dragValue].dimension.code;
    const currentDimensionIdx = ORDERED_POINT_DIMENSIONS.findIndex(
      d => d === thisPointDimensionEnums
    );
    cannotSelectDimensionEnums = ORDERED_POINT_DIMENSIONS.slice(
      currentDimensionIdx,
      ORDERED_POINT_DIMENSIONS.length
    );
  }

  const data = {
    name: {
      // value: `${normalizedList[deviceCode].metaName}-${pointInfoMap[dragValue].metaName}`,
      value: dragValue,
    },
    dimensionEnums: {
      value: [],
    },
    statusList: {
      value: [],
    },
    pointKey: disabledLeaf,
    dragValue,
    disabledDimensionEnums,
    aggStatusMap: {},
    statusMap: {},
    isOnlySelectDevice,
    cannotSelectDimensionEnums,
  };

  yield put(
    mergedProcessesdPointActions.dropUpdateMergedPointSingleFormValues({
      id,
      data,
    })
  );
}

function* createMergedPointList(payload) {
  const { margedPointIds, margedPointMaps } = yield select(getMergedPointFormData);
  const pointInfoMap = yield select(getfetchDeviceInfoData);
  let pointAggInfos = [];
  margedPointIds.forEach(id => {
    const newPointChild = margedPointMaps[id];
    const singlrPointInfo = pointInfoMap[newPointChild.dragValue];
    let tmp = {
      pointInfo: {
        name: singlrPointInfo.name,
        dataType: singlrPointInfo.dataType.code,
        pointType: singlrPointInfo.pointType.code,
        dimension: singlrPointInfo.dimension,
        deviceType: singlrPointInfo.deviceType,
        priority: singlrPointInfo.priority,
        unit: singlrPointInfo.unit,
        precision: singlrPointInfo.precision,
        code: singlrPointInfo.code,
      },
    };
    if (singlrPointInfo.dataType.code === POINT_DATA_TYPE_CODE_MAP.AI) {
      // 去重
      const differenceDimensionEnums = newPointChild.dimensionEnums.value.filter(
        item => !newPointChild.disabledDimensionEnums.includes(item)
      );
      tmp = {
        ...tmp,
        aggMode: differenceDimensionEnums,
      };
    } else {
      // 去重
      const { differentStatus, differentAggStatus } = getDifferenceData(
        newPointChild.statusMap,
        newPointChild.aggStatusMap,
        newPointChild.disabledAggStatus
      );
      tmp = {
        ...tmp,
        statusMap: differentStatus,
        aggStatusMap: differentAggStatus,
      };
    }

    pointAggInfos = [...pointAggInfos, tmp];
  });
  const data = { pointAggInfos: pointAggInfos };
  const { error } = yield call(mergedProcessesdPointService.fetchCreateMergedPoint, data);
  if (error) {
    message.error(error);
  } else {
    message.success('新建成功');
    //yield put(redirectActionCreator(urls.MERGES_PROCESSED_POINT_DETAIL));
    yield put(mergedProcessesdPointActions.resetItems());
    payload.setPattern('list');
    payload.getMetaCategoryNum();
  }
}

function* updatePressedDIStatuesList(payload) {
  const { eventKey, idx } = payload;
  const normalizedList = yield select(getCommonDeviceInfoTreeData);
  const pointInfoMap = yield select(getfetchDeviceInfoData);
  const statusList = yield select(getPressedPointDIStatusList);
  const processedPointList = yield select(getPressedPointConfig);
  const pointData = pointInfoMap[eventKey];

  const statusListClone = cloneDeep(statusList.value);

  if (
    statusListClone.length &&
    statusListClone[0].value &&
    processedPointList.pointType.value === POINT_TYPE_CODE_MAP.CAL_DEVICE
  ) {
    const [, deviceType] = statusListClone[0].value.split('_');
    const [dragDeviceType] = eventKey.split('_$$_');
    if (dragDeviceType !== deviceType) {
      message.error('设备类型内加工只能拖相同类型的测点！');
      return;
    }
  }

  const sameLabelData = statusListClone.filter(
    item =>
      item.label === statusListClone[idx].label &&
      item.value === `K_${pointData.deviceType}_${pointData.pointCode}`
  );
  if (sameLabelData && sameLabelData.length) {
    message.error('不可以放置相同测点');
  } else {
    statusListClone[idx] = {
      ...statusListClone[idx],
      name: `${normalizedList[pointData.deviceType].metaName}-${pointData.metaName}`,
      validLimits: pointData.validLimits
        ? generateValidLimitsDataSource(pointData.validLimits)
        : [],
      value: `K_${pointData.deviceType}_${pointData.pointCode}`,
      children: null,
    };
  }
  yield put(mergedProcessesdPointActions.updateStatusList(statusListClone));
}

function getQ(values) {
  const baseQ = {};
  for (const [name, { value }] of Object.entries(values)) {
    const val = value === '' ? null : value;
    if (name) {
      baseQ[name] = val;
    }
    if (name === 'priority') {
      baseQ[name] = val === 'alwaysTrue' ? 1 : 0;
    }
  }
  return baseQ;
}

// function* getTopDimension(exprData) {
//   const pointInfoMap = yield select(getfetchDeviceInfoData);

//   let topDimension;

//   flattenTreeData(exprData).forEach(({ type, value }) => {
//     if (type !== EXPR_ITEM_TYPE.POINT) {
//       return;
//     }

//     const [, deviceType, pointCode] = value.split('_');

//     const dimension = get(
//       pointInfoMap,
//       [deviceType + '_$$_' + pointCode, 'dimension', 'code'],
//       MERGE_DIRECTION_CODE_MAP.DEVICE
//     );

//     if (topDimension === undefined) {
//       topDimension = dimension || MERGE_DIRECTION_CODE_MAP.DEVICE;
//       return;
//     }

//     const currentDimensionIdx = ORDERED_POINT_DIMENSIONS.findIndex(d => d === dimension);
//     const topDimensionIdx = ORDERED_POINT_DIMENSIONS.findIndex(d => d === topDimension);

//     if (currentDimensionIdx > -1 && topDimensionIdx > -1 && currentDimensionIdx < topDimensionIdx) {
//       topDimension = dimension;
//     }
//   });

//   return topDimension;
// }

function* createPressedPointConfig(payload) {
  const processedPointList = yield select(getPressedPointConfig);
  const q = getQ(processedPointList);
  q.priority = processedPointList.priority.value === 'alwaysTrue' ? 1 : 0;
  let dimension = payload.dimension;
  let expandDeviceType = '';
  const expandPointType = q.pointType;
  let params = {};
  if (expandPointType === 'CUSTOM') {
    if (q?.spaceGuid.length === 1) {
      dimension = 'IDC';
    }
    if (q?.spaceGuid.length === 2) {
      dimension = 'BLOCK';
    }
    if (q?.spaceGuid.length === 3) {
      dimension = 'ROOM';
    }
  }

  const expressInfos = yield select(getExpressInfos);

  if (q.dataType === POINT_DATA_TYPE_CODE_MAP.DI) {
    q.unit = null;
    q.precision = 1;

    if (payload.treeMode === 'device') {
      // 设备DI加工测点改造，走状态含义，原expressInfos清空,否则保持原来逻辑
      q.validLimits = [];
      for (let i = 0; i < processedPointList?.digitalNum?.value; i++) {
        const str = i + '=' + processedPointList[i]?.value;
        q.validLimits.push(str);
      }
      q.expressInfos = null;
    } else {
      q.validLimits = null;
    }

    const statusList = processedPointList.statusList.value;
    let newList = [];
    statusList.forEach((point, index) => {
      // 判断只有一个点位，或者为最后一个，或者与下一个值不同
      if (statusList.length === 1 || !statusList[index + 1]) {
        const tmp = [
          {
            type: 'point',
            value: point.value,
            name: point.name,
          },
          {
            type: 'equality-operator',
            value: '==',
            name: '==',
          },
          {
            type: 'number',
            value: point.dataType,
            name: getValidLimitsTxt(point.dataType, point.validLimits),
          },
          {
            type: 'delimiter',
            value: ',',
            name: ',',
          },
          {
            type: 'status',
            value: point.label,
            name: point.status,
          },
        ];
        newList = [...newList, ...tmp];
      } else if (point.label !== statusList[index + 1].label) {
        const tmp = [
          {
            type: 'point',
            value: point.value,
            name: point.name,
          },
          {
            type: 'equality-operator',
            value: '==',
            name: '==',
          },
          {
            type: 'number',
            value: point.dataType,
            name: getValidLimitsTxt(point.dataType, point.validLimits),
          },
          {
            type: 'delimiter',
            value: ',',
            name: ',',
          },
          {
            type: 'status',
            value: point.label,
            name: point.status,
          },
          {
            type: 'delimiter',
            value: ',',
            name: ',',
          },
        ];
        newList = [...newList, ...tmp];
      } else {
        const tmp = [
          {
            type: 'point',
            value: point.value,
            name: point.name,
          },
          {
            type: 'equality-operator',
            value: '==',
            name: '==',
          },
          {
            type: 'number',
            value: point.dataType,
            name: getValidLimitsTxt(point.dataType, point.validLimits),
          },
          {
            type: 'logic-operator',
            value: '&&',
            name: '&&',
          },
        ];
        newList = [...newList, ...tmp];
      }
    });
    const mock = {
      type: 'statistical-function',
      value: 'switch',
      name: 'SWITCH',
      children: newList,
    };
    const [, deviceType] =
      statusList.length && statusList[0]?.value ? statusList[0].value.split('_') : [];

    let status = [];
    if (payload.treeMode === 'space') {
      for (let i = 0; i < processedPointList?.digitalNum?.value; i++) {
        const str = i + '=' + processedPointList[i]?.value;
        status = [...status, str];
      }
      q.validLimits = status;
    }

    params = {
      ...q,
      dimension: dimension,
      deviceType: deviceType,
      expressInfos: payload.treeMode === 'space' ? expressInfos : [mock],
    };
    expandDeviceType = deviceType;
  }

  if (q.dataType === POINT_DATA_TYPE_CODE_MAP.AI) {
    const deviceType = yield select(getParentDeviceType);
    const expressInfos = yield select(getExpressInfos);
    // const dimension = yield getTopDimension(expressInfos);
    params = {
      ...q,
      dimension: dimension,
      deviceType: getSpaceDeviceTypeIfNeeded(dimension, deviceType),
      validLimits:
        !isNil(q.validLimits?.ge) && !isNil(q.validLimits?.le)
          ? [`ge=${q.validLimits.ge}`, `le=${q.validLimits.le}`]
          : [],
      expressInfos,
    };
    expandDeviceType = getSpaceDeviceTypeIfNeeded(dimension, deviceType);
    if (q?.selectedExprDataType === 'editor') {
      // AI测点 编辑器模式下才清空formula，（DI因为没有编辑器模式，不处理）
      params.formula = null;
    }
  }

  if (Array.isArray(q?.spaceGuid) && q?.spaceGuid.length) {
    params.spaceGuid = q?.spaceGuid.join('.');
  }
  const formulaName = yield select(getFormulaName);
  const processedSelectedPoint = yield select(getProcessedSelectedPoint);

  if (q?.selectedExprDataType === 'handler') {
    // 手动模式清空expressInfos
    params.expressInfos = null;
  } else {
    params.expressInfos = expressInfos;
  }

  let responseCb;
  let errorCb;
  const targetGuid = yield select(getCustomSpaceGuid);
  if (payload.treeMode === 'custom') {
    let instanceParams;
    if (processedSelectedPoint.targetPointType === 'DEVICE') {
      const deviceInfo = yield select(getProcessedDeviceInfo);
      instanceParams = {
        targetGuid: deviceInfo.guid,
        deviceId: deviceInfo.id,
        pointId: processedSelectedPoint.id,
        expressInfos: params.expressInfos,
      };
    } else {
      instanceParams = {
        targetGuid,
        pointId: processedSelectedPoint.id,
        expressInfos: params.expressInfos,
      };
    }

    const { error, response } = yield call(createPointInstance, {
      ...instanceParams,
      formulaName: q?.selectedExprDataType === 'handler' ? (params.formula ?? '') : formulaName, // 手动默认下取formula，否则默认取formulaName
      formula: params?.formula,
    });
    responseCb = response;
    //重新请求设备树
    payload.refresCustomDeviceTreeData();
    if (error) {
      errorCb = error.message;
    }
  } else {
    const { error, response } = yield call(mergedProcessesdPointService.fetchCreatePressedPoint, {
      ...params,
      formulaName,
      code: generatePointCode(params.code, params.dimension, params.dataType),
      deviceType:
        expandPointType === 'CUSTOM'
          ? getSpaceDeviceTypeIfNeeded(dimension, null)
          : payload.deviceType,
      formula: params.formula,
    });
    responseCb = response;
    errorCb = error;
  }
  if (errorCb) {
    message.error(errorCb);
    return;
  }

  message.success('新建成功');

  // const isVirtual = oneOfVirtualDeviceType(expandDeviceType); // 判断是否为虚拟设备， true 为虚拟设备
  // 在叶子节点上才需要判断是否是虚拟设备或真是设备，父节点展开项全部都是虚拟节点
  //const metaType = 'VIRTUAL_ITEM';

  // const secontKey = `C2${expandDeviceType}`;
  // const thirdKey = `${metaType}${expandDeviceType}${expandPointType}`;
  //const defaultExpandedKeys = [secontKey, thirdKey];
  const selectedKeys = [`REAL_ITEM${expandDeviceType}_$$_${responseCb}`];
  const selectedPointKey = `${expandDeviceType}_$$_${responseCb}`;
  if (expandPointType === 'CUSTOM') {
    payload.refresCustomDeviceTreeData && payload.refresCustomDeviceTreeData();
    yield put(
      doPointActions.selectCategoryPoint({
        selectTitle: params.spaceGuid,
        selectType: params.dimension,
        deviceType: params.spaceGuid,
        metaType: dimension,
        type: 'custom',
      })
    );
  }
  payload.setPattern('list');
  // yield put(
  //   redirectActionCreator({
  //     pathname: urls.MERGES_PROCESSED_POINT_DETAIL,
  //     state: { defaultExpandedKeys },
  //   })
  // );
  yield put(
    mergedProcessesdPointActions.updateSelectedPointKey({ selectedKeys, selectedPointKey })
  );
  yield put(mergedProcessesdPointActions.resetItems());
}

function getValidLimitsTxt(code, values) {
  const newData = values.filter(({ value }) => code === value);
  if (newData[0]) {
    return newData[0].label;
  }
}

function* onChangeExpressInfos(payload) {
  const { source, destination, dragValue, pointType, dimension } = payload;
  const [deviceType] = dragValue.split('_$$_');
  // 聚合测点
  if (source.droppableId === 'treeLest' && destination.droppableId.includes('pointDrop_$$_')) {
    const [, id] = destination.droppableId.split('_$$_');
    if (
      pointType === POINT_TYPE_CODE_MAP.AGG_DEVICE ||
      pointType === POINT_TYPE_CODE_MAP.AGG_SPACE
    ) {
      yield put(searchPointIsMerged({ id, dragValue, dimension }));
    } else {
      yield put(dropUpdateMergedPointForm({ id, deviceCode: deviceType, dragValue }));
    }
  }
  // 加工测点
  if (source.droppableId === 'treeLest' && destination.droppableId.includes('pointName_$$_')) {
    const [, idx] = destination.droppableId.split('pointName_$$_');
    yield put(getUpdatePressedDIStatuesList({ eventKey: dragValue, idx }));
  }
}

// 查询是否被聚合
function* searchPointIsOrNotMerged(payload) {
  const { id, dragValue, dimension } = payload;
  const [, deviceType, pointCode] = dragValue.split('_');
  const params = { deviceType, pointCode, dimension };
  const { error, response } = yield call(
    mergedProcessesdPointService.fetchPointAggIsMerged,
    params
  );
  if (error) {
    message.error(error);
    return;
  }
  // 没有被聚合过
  if (!response) {
    yield put(dropUpdateMergedPointForm({ id, deviceCode: deviceType, dragValue }));
    return;
  }
  // 聚合过
  const data = {
    ...response, // 存聚合的维度 {aggMode:[],aggStatusMap:{},statusMap:{}}
    eventKey: dragValue,
    id,
  };
  yield put(mergedProcessesdPointActions.changeIsMerged(true)); // 绑定是否导入之前聚合配置弹窗状态
  yield put(mergedProcessesdPointActions.saveMergedPointConfig(data));
}

// 合并聚合的测点
function* mergeTheMergedPoint() {
  const mergedPointData = yield select(getMergedPointData);
  const { margedPointMaps } = yield select(getMergedPointFormData);
  const pointInfoMap = yield select(getfetchDeviceInfoData);
  // const normalizedList = yield select(getCommonDeviceInfoTreeData);
  // const [deviceType] = mergedPointData.eventKey.split('_$$_');
  const disabledLeaf = generateNodeKey({
    metaCode: pointInfoMap[mergedPointData.eventKey].metaCode,
    metaType: pointInfoMap[mergedPointData.eventKey].metaType,
    nodeType: pointInfoMap[mergedPointData.eventKey].nodeType,
  });

  // 若拖放进右侧 List 的测点是一个真实的原始主点位，则默认选中聚合纬度的 设备聚合 ，且不可选择其他聚合纬度
  const disabledDimensionEnums = [];
  const isOnlySelectDevice = pointInfoMap[mergedPointData.eventKey].extCount > 0;

  //  只可以向上聚合，不可向下聚合
  let cannotSelectDimensionEnums = [];
  if (
    !isOnlySelectDevice &&
    pointInfoMap[mergedPointData.eventKey].dimension &&
    pointInfoMap[mergedPointData.eventKey].dimension.code
  ) {
    const thisPointDimensionEnums = pointInfoMap[mergedPointData.eventKey].dimension.code;
    const currentDimensionIdx = ORDERED_POINT_DIMENSIONS.findIndex(
      d => d === thisPointDimensionEnums
    );
    cannotSelectDimensionEnums = ORDERED_POINT_DIMENSIONS.slice(
      currentDimensionIdx,
      ORDERED_POINT_DIMENSIONS.length
    );
  }

  let newMap = {
    ...margedPointMaps,
    [mergedPointData.id]: {
      name: {
        // value: `${normalizedList[deviceType].metaName}-${
        //   pointInfoMap[mergedPointData.eventKey].metaName
        // }`,
        value: mergedPointData.eventKey,
      },
      dragValue: mergedPointData.eventKey,
      pointKey: disabledLeaf,
      isOnlySelectDevice,
      cannotSelectDimensionEnums,
      disabledDimensionEnums: [...mergedPointData.aggMode, ...disabledDimensionEnums],
      dimensionEnums: { value: [] },
    },
  };

  if (pointInfoMap[mergedPointData.eventKey].dataType.code === POINT_DATA_TYPE_CODE_MAP.AI) {
    newMap = {
      ...newMap,
      [mergedPointData.id]: {
        ...newMap[mergedPointData.id],
        dimensionEnums: {
          value: mergedPointData.aggMode,
        },
        disabledDimensionEnums: [...mergedPointData.aggMode, ...disabledDimensionEnums],
      },
    };
  } else {
    let statusMap = {};
    Object.keys(mergedPointData.aggStatusMap).forEach(item => {
      statusMap = {
        ...statusMap,
        [item]: mergedPointData.statusMap[item],
      };
    });
    newMap = {
      ...newMap,
      [mergedPointData.id]: {
        ...newMap[mergedPointData.id],
        statusMap: statusMap,
        aggStatusMap: mergedPointData.aggStatusMap,
        disabledAggStatus: mergedPointData.aggStatusMap,
        disabledDimensionEnums: [...mergedPointData.aggMode, ...disabledDimensionEnums],
      },
    };
  }

  yield put(mergedProcessesdPointActions.mergedAndUpdateMergedPoint(newMap));
  yield put(mergedProcessesdPointActions.changeIsMerged(false));
}

function* getPointDetailData(payload) {
  const { deviceType, pointCode, mode, treeMode, successCb, info } = payload;

  const params = {
    devicePointList: [`${deviceType}.${pointCode}`],
  };
  const { error, response } = yield call(mergedProcessesdPointService.fetchBatchPoint, params);
  if (error) {
    message.error(error);
  }
  if (response) {
    let data = response.data[0];
    if (treeMode === 'custom' && mode === 'edit') {
      data = { ...data, ...info };
    }
    let tableData = []; // DI的表格数据
    let pointCodes = [];
    let formulaArray = [];
    // 表达式中是否含有 空间测点、自定义空间测点，若有，则在编辑时应默认选中目标测点为空间
    let hasSpacePoint = false;
    try {
      formulaArray = data.formulaJson ? JSON.parse(data.formulaJson) : [];
    } catch {}
    //空间自定义类型使用计算表达式形式
    if (
      data.dataType.code === POINT_DATA_TYPE_CODE_MAP.DI &&
      formulaArray[0] &&
      treeMode !== 'space' &&
      treeMode !== 'custom'
    ) {
      const expressionArr = formulaArray[0]?.children || [];
      tableData = diExprToTableData(expressionArr);
      pointCodes = tableData
        .map(({ value }) => {
          if (!value) {
            return null;
          }
          if (data.pointType.code === 'CUSTOM') {
            const [, , _pointCode, _deviceType] = value.split('_');
            return `${_deviceType}.${_pointCode}`;
          }
          const [, deviceType, pointCode] = value.split('_');
          return `${deviceType}.${pointCode}`;
        })
        .filter(Boolean);
    } else {
      const expressionData = generateFormulaId(formulaArray);
      flattenTreeData(expressionData).forEach(({ type, value }) => {
        if ([EXPR_ITEM_TYPE.POINT].includes(type)) {
          const [, deviceType, pointCode] = value.split('_');
          let tmp = `${deviceType}.${pointCode}`;
          if (data.pointType.code === 'CUSTOM') {
            const [, , _pointCode, _deviceType] = value.split('_');
            tmp = `${_deviceType}.${_pointCode}`;
          }
          pointCodes = [...pointCodes, tmp];
        } else if ([EXPR_ITEM_TYPE.SPACE_POINT, EXPR_ITEM_TYPE.CUSTOM_SPACE_POINT].includes(type)) {
          hasSpacePoint = true;
          const [, , pointCode, deviceType] = value.split('_');
          pointCodes = [...pointCodes, `${deviceType}.${pointCode}`];
        }
      });
    }

    let tableAndLimits = [];
    const tmp = {};
    tmp[`${data.deviceType}_$$_${data.pointCode}`] = response.data[0]; // 存当前测点的详情
    if (pointCodes.length) {
      const tablePoints = yield call(mergedProcessesdPointService.fetchBatchPoint, {
        devicePointList: pointCodes,
      });
      if (tablePoints.error) {
        message.error(tablePoints.error);
      }
      if (tablePoints.response.data) {
        tableAndLimits = tableData
          .map(point => {
            let code = '';
            if (!point.value) {
              return null;
            }
            if (data.pointType.code === 'CUSTOM') {
              const [, , childPointCode, childDeviceType] = point.value.split('_');
              code = `${childDeviceType}${childPointCode}`;
            } else {
              const [, childDeviceType, childPointCode] = point.value.split('_');
              code = `${childDeviceType}${childPointCode}`;
            }

            let validoptions = [];
            tablePoints.response.data.forEach(point => {
              if (point.dataType.code === POINT_DATA_TYPE_CODE_MAP.DI) {
                if (`${point.deviceType}${point.code}` === code) {
                  validoptions = generateValidLimitsDataSource(point.validLimits);
                }
              }
            });
            return { ...point, validLimits: validoptions, markedID: shortid.generate() };
          })
          .filter(Boolean);
        // 存表达式中存在的测点的详情
        tablePoints.response.data.forEach(item => {
          const key = `${item.deviceType}_$$_${item.pointCode}`;
          tmp[key] = item;
        });
      }
    }
    let processedPointList = {
      name: {
        value: data.name,
      },
      pointType: {
        value: data.pointType.code,
      },
      dataType: {
        value: data.dataType.code,
      },
      unit: {
        value: data.unit,
      },
      precision: {
        value: data.precision,
      },
      priority: {
        value: data.priority === 1 ? 'alwaysTrue' : '',
      },
      statusList: {
        value: tableAndLimits,
      },
      validLimits: {
        value: data.dataType.code === 'AI' ? getValidLimitsFormValue(data.validLimits) : '',
      },
      description: {
        value: data.description,
      },
      spaceGuid: {
        value: data.pointType.code === 'CUSTOM' ? data.spaceGuid.split('.') : null,
      },
      targetPointType: {
        value: hasSpacePoint ? TargetPointType.SPACE : TargetPointType.DEVICE,
      },
      formula: {
        value: data.formula,
      },
      selectedExprDataType: {
        value: data?.formula != null && data?.formulaJson == null ? 'handler' : 'editor',
      },
    };

    if (treeMode === 'custom') {
      processedPointList = {
        name: {
          value: data.name,
        },
        pointType: {
          value: data.pointType.code,
        },
        dataType: {
          value: data.dataType.code,
        },
        unit: {
          value: data.unit,
        },
        precision: {
          value: data.precision,
        },
        priority: {
          value: data.priority === 1 ? 'alwaysTrue' : '',
        },
        statusList: {
          value: tableAndLimits,
        },
        validLimits: {
          value: data.dataType.code === 'AI' ? getValidLimitsFormValue(data.validLimits) : '',
        },
        description: {
          value: data.description,
        },
        formula: {
          value: data.formula,
        },
        selectedExprDataType: {
          value: data?.formula != null && data?.formulaJson == null ? 'handler' : 'editor',
        },
      };
      if (mode === 'edit') {
        processedPointList.targetPointType = {
          value: info?.dimension?.code === 'DEVICE' ? 'DEVICE' : 'SPACE',
        };
        processedPointList.spaceGuid = {
          value:
            info?.dimension?.code === 'DEVICE' ? info?.targetGuid : info?.targetGuid?.split('.'),
        };
      }
    }
    if (
      (data.dataType.code === POINT_DATA_TYPE_CODE_MAP.DI ||
        data.dataType.code === POINT_DATA_TYPE_CODE_MAP.DO) &&
      data.validLimits !== null &&
      data.validLimits !== undefined
    ) {
      data.validLimits.forEach(item => {
        const arr = item.split('=');
        const status = {
          value: arr[1],
        };
        processedPointList[arr[0]] = status;
      });
    }
    //此处直接请求接口 重新定义processedPointList
    const expressionData = generateFormulaId(formulaArray); // 给每个表达式加上唯一id
    yield put(mergedProcessesdPointActions.savePointInfoMap(tmp));
    yield put(mergedProcessesdPointActions.changePointTypeSelected(processedPointList));
    yield put(
      mergedProcessesdPointActions.updateExpressionData(!payload.isTemplate ? expressionData : [])
    );

    // 如果是加工点位，则需要记录设备类型
    // 因为只能使用同一种设备类型的测点加工
    if (
      [POINT_TYPE_CODE_MAP.CAL_DEVICE, POINT_TYPE_CODE_MAP.CAL_SPACE].includes(data.pointType.code)
    ) {
      yield put(mergedProcessesdPointActions.setParentDeviceType(deviceType));
    }
    //编辑时更新表达式名称
    if (data.formulaName) {
      yield put(mergedProcessesdPointActions.updateFormulaName({ formulaName: data.formulaName }));
    }

    if (successCb && typeof successCb === 'function') {
      successCb(processedPointList, expressionData);
    }

    // 更新模式
    if (!(treeMode === 'custom' && mode === 'create')) {
      //为了自定义模式新建回填模版信息
      yield put(mergedProcessesdPointActions.updateMode(mode));
    }
  }
}

function generateFormulaId(data) {
  return data.map(item => {
    if (item.children && item.children.length) {
      return {
        ...item,
        id: shortid.generate(),
        children: generateFormulaId(item.children),
      };
    } else {
      return {
        ...item,
        id: shortid.generate(),
      };
    }
  });
}

function* updatePressedPoint({ data, successCb, errorCb, treeMode, info }) {
  const processedPointList = yield select(getPressedPointConfig);
  const q = getQ(processedPointList);
  let params;
  if (q.dataType === POINT_DATA_TYPE_CODE_MAP.DI) {
    if (treeMode === 'device') {
      q.validLimits = [];
      for (let i = 0; i < processedPointList?.digitalNum?.value; i++) {
        // 设备DI加工测点改造，走状态含义，原expressInfos清空
        const str = i + '=' + processedPointList[i]?.value;
        q.validLimits.push(str);
      }
      q.expressInfos = null;
    } else {
      q.validLimits = null;
    }
    q.unit = null;
    q.precision = 1;
    q.priority = processedPointList.priority.value === 'alwaysTrue' ? 1 : 0;

    const statusList = processedPointList.statusList.value;
    let newList = [];
    statusList.forEach((point, index) => {
      // 判断只有一个点位，或者为最后一个，或者与下一个值不同
      if (statusList.length === 1 || !statusList[index + 1]) {
        const tmp = [
          {
            type: 'point',
            value: point.value,
            name: point.name,
          },
          {
            type: 'equality-operator',
            value: '==',
            name: '==',
          },
          {
            type: 'number',
            value: point.dataType,
            name: getValidLimitsTxt(point.dataType, point.validLimits),
          },
          {
            type: 'delimiter',
            value: ',',
            name: ',',
          },
          {
            type: 'status',
            value: point.label,
            name: point.status,
          },
        ];
        newList = [...newList, ...tmp];
      } else if (point.label !== statusList[index + 1].label) {
        const tmp = [
          {
            type: 'point',
            value: point.value,
            name: point.name,
          },
          {
            type: 'equality-operator',
            value: '==',
            name: '==',
          },
          {
            type: 'number',
            value: point.dataType,
            name: getValidLimitsTxt(point.dataType, point.validLimits),
          },
          {
            type: 'delimiter',
            value: ',',
            name: ',',
          },
          {
            type: 'status',
            value: point.label,
            name: point.status,
          },
          {
            type: 'delimiter',
            value: ',',
            name: ',',
          },
        ];
        newList = [...newList, ...tmp];
      } else {
        const tmp = [
          {
            type: 'point',
            value: point.value,
            name: point.name,
          },
          {
            type: 'equality-operator',
            value: '==',
            name: '==',
          },
          {
            type: 'number',
            value: point.dataType,
            name: getValidLimitsTxt(point.dataType, point.validLimits),
          },
          {
            type: 'logic-operator',
            value: '&&',
            name: '&&',
          },
        ];
        newList = [...newList, ...tmp];
      }
    });
    const mock = {
      type: 'statistical-function',
      value: 'switch',
      name: 'SWITCH',
      children: newList,
    };
    params = {
      ...q,
      ...data,
      dimension: 'DEVICE',
      expressInfos: [mock],
    };
  } else {
    const expressInfos = yield select(getExpressInfos);
    params = {
      ...q,
      ...data,
      validLimits:
        !isNil(q.validLimits?.ge) && !isNil(q.validLimits?.le)
          ? [`ge=${q.validLimits.ge}`, `le=${q.validLimits.le}`]
          : [],
      expressInfos,
    };
  }

  if (treeMode === 'space' && q.dataType === POINT_DATA_TYPE_CODE_MAP.DI) {
    const expressInfos = yield select(getExpressInfos);
    params.expressInfos = expressInfos;
    let status = [];
    for (let i = 0; i < processedPointList?.digitalNum?.value; i++) {
      const str = i + '=' + processedPointList[i]?.value;
      status = [...status, str];
    }
    params.validLimits = status;
  }
  const formulaName = yield select(getFormulaName);

  if (q?.selectedExprDataType === 'editor' && q.dataType === POINT_DATA_TYPE_CODE_MAP.AI) {
    // AI测点 编辑器模式下清空formula（DI因为没有编辑器模式，不处理）
    params.formula = null;
  }

  if (treeMode === 'custom') {
    const expressInfos = yield select(getExpressInfos);
    if (params?.selectedExprDataType === 'handler') {
      // 手动模式清空expressInfos
      params.expressInfos = null;
    } else {
      params.expressInfos = expressInfos;
    }
    //单独处理自定义测点的更新
    const { error } = yield call(updatePointInstance, {
      id: info.id,
      expressInfos: params.expressInfos,
      formulaName:
        params?.selectedExprDataType === 'handler' ? (params.formula ?? '') : formulaName, // 手动默认下取formula，否则默认取formulaName
      formula: params.formula,
    });
    if (error) {
      errorCb && errorCb();
      message.error(error.message);
    } else {
      successCb();
      message.success('更新成功');
      yield delay(500);
      //yield put(redirectActionCreator(urls.MERGES_PROCESSED_POINT_DETAIL));
      yield put(mergedProcessesdPointActions.resetItems());
    }
  } else {
    const { error } = yield call(mergedProcessesdPointService.fetchUpdatePressedPoint, {
      ...params,
      formulaName: params.expressInfos && params.expressInfos?.length > 0 ? formulaName : undefined,
    });
    if (error) {
      errorCb && errorCb();
      message.error(error);
    } else {
      successCb();
      message.success('更新成功');
      yield delay(500);
      yield put(redirectActionCreator(urls.MERGES_PROCESSED_POINT_DETAIL));
      yield put(mergedProcessesdPointActions.resetItems());
    }
  }
}

function* getNewExprPointItem(draggableId) {
  const deviceTypeMetaData = yield select(getDeviceTypeMetaData);
  const deviceTypeMap = get(deviceTypeMetaData, 'normalizedList');
  const pointInfoMap = yield select(getfetchDeviceInfoData);
  const { deviceType, name, code, metaName, specCode, type } = pointInfoMap[draggableId];
  const item = {
    type,
    id: type + '.' + draggableId + '.' + shortid(),
    name: get(deviceTypeMap, [deviceType, 'metaName'], deviceType) + '_',
  };
  if (type === EXPR_ITEM_TYPE.POINT) {
    item.name = item.name + name;
    item.value = 'K_' + deviceType + '_' + code;
  }
  if (type === EXPR_ITEM_TYPE.DEVICE_PROPERTY) {
    item.name = item.name + metaName;
    item.value = specCode;
  }

  return { item, deviceType };
}

function* shouldInsertPoint(deviceType) {
  const { pointType } = yield select(getPressedPointConfig);
  const parentDeviceType = yield select(getParentDeviceType);

  // 加工点位都必须使用同一设备类型内的点位加工
  if (
    [POINT_TYPE_CODE_MAP.CAL_DEVICE, POINT_TYPE_CODE_MAP.CAL_SPACE].includes(pointType.value) &&
    parentDeviceType &&
    parentDeviceType !== deviceType
  ) {
    message.error('只能使用同一种设备类型的测点！');
    return false;
  }

  return true;
}

function* exprInsertPointIntoRoot({ index, draggableId }) {
  const { item, deviceType } = yield getNewExprPointItem(draggableId);
  const isOk = yield shouldInsertPoint(deviceType);
  if (isOk) {
    yield put(mergedProcessesdPointActions.exprInsertIntoRoot({ index, item, deviceType }));
  }
}

function* exprInsertPointIntoNested({ parentItemId, index, draggableId }) {
  const { item, deviceType } = yield getNewExprPointItem(draggableId);
  const isOk = yield shouldInsertPoint(deviceType);
  if (isOk) {
    yield put(
      mergedProcessesdPointActions.exprInsertIntoNested({ parentItemId, index, item, deviceType })
    );
  }
}

function* testExpr(pointValue) {
  const fieldValues = yield select(getPressedPointConfig);
  const { dataType, precision, unit } = getQ(fieldValues);

  const expressInfos = yield select(getExpressInfos);

  const q = {
    dataType,
    precision,
    pointValue,
    expressInfos,
  };

  const { response, error } = yield call(
    mergedProcessesdPointService.checkCalculatedAiPointExpression,
    q
  );
  if (response) {
    const params = {
      txt: unit ? response + unit : response,
      color: `var(--${prefixCls}-success-color)`,
    };
    yield put(mergedProcessesdPointActions.setTestExprResult(params));
  } else {
    const params = {
      txt: error,
      color: `var(--${prefixCls}-error-color)`,
    };
    yield put(mergedProcessesdPointActions.setTestExprResult(params));
    // message.error(error);
  }
}

/******************************************************************************/
/******************************* WATCHERS *************************************/
/******************************************************************************/

function* watchDropUpdateMergedPointForm() {
  while (true) {
    const { payload } = yield take(dropUpdateMergedPointForm.type);
    yield fork(updateMergedPointFormDrop, payload);
  }
}

function* watchCreateMergedPoint() {
  while (true) {
    const { payload } = yield take(createMergedPoint.type);
    yield fork(createMergedPointList, payload);
  }
}

function* watchGetUpdatePressedDIStatuesList() {
  while (true) {
    const { payload } = yield take(getUpdatePressedDIStatuesList.type);
    yield fork(updatePressedDIStatuesList, payload);
  }
}

function* watchCreatePressedPoint() {
  while (true) {
    const { payload } = yield take(createPressedPoint.type);
    yield fork(createPressedPointConfig, payload);
  }
}

function* watchChangeExpressInfos() {
  while (true) {
    const { payload } = yield take(changeExpressInfos.type);
    yield fork(onChangeExpressInfos, payload);
  }
}

function* watchSearchPointIsMerged() {
  while (true) {
    const { payload } = yield take(searchPointIsMerged.type);
    yield fork(searchPointIsOrNotMerged, payload);
  }
}

function* watchMergeTheMergedPointConfig() {
  while (true) {
    const { payload } = yield take(mergeTheMergedPointConfig.type);
    yield fork(mergeTheMergedPoint, payload);
  }
}

function* watchGetPointDetail() {
  while (true) {
    const { payload } = yield take(getPointDetail.type);
    yield fork(getPointDetailData, payload);
  }
}

function* watchUpdatePressedPoint() {
  while (true) {
    const { payload } = yield take(updatePressedPointActionCreator.type);
    yield fork(updatePressedPoint, payload);
  }
}

function* watchExprInsertPointIntoRoot() {
  while (true) {
    const { payload } = yield take(exprInsertPointIntoRootActionCreator.type);
    yield fork(exprInsertPointIntoRoot, payload);
  }
}

function* watchExprInsertPointIntoNested() {
  while (true) {
    const { payload } = yield take(exprInsertPointIntoNestedActionCreator.type);
    yield fork(exprInsertPointIntoNested, payload);
  }
}

function* watchTestExpr() {
  while (true) {
    const { payload } = yield take(testExprActionCreator.type);
    yield fork(testExpr, payload);
  }
}

export default [
  fork(watchDropUpdateMergedPointForm),
  fork(watchCreateMergedPoint),
  fork(watchGetUpdatePressedDIStatuesList),
  fork(watchCreatePressedPoint),
  fork(watchChangeExpressInfos),
  fork(watchSearchPointIsMerged),
  fork(watchMergeTheMergedPointConfig),
  fork(watchGetPointDetail),
  fork(watchUpdatePressedPoint),
  fork(watchExprInsertPointIntoRoot),
  fork(watchExprInsertPointIntoNested),
  fork(watchTestExpr),
];
const generatePointCode = (pointCode, dimension, dataType) => {
  switch (dimension) {
    case 'DEVICE':
      return `7${fillZero(pointCode)}`;

    default:
      return dataType === 'AI' ? `1${fillZero(pointCode)}` : `2${fillZero(pointCode)}`;
  }
};

const fillZero = pointCode => {
  const codeLength = pointCode.toString().length;
  if (codeLength === 1) {
    return `00${pointCode}`;
  } else if (codeLength === 2) {
    return `0${pointCode}`;
  } else {
    return pointCode.toString();
  }
};

const getValidLimitsFormValue = validLimits => {
  if (!validLimits?.length) {
    return { ge: null, le: null };
  }
  return {
    ge: validLimits[0]?.split('=').length ? validLimits[0]?.split('=')[1] : null,
    le: validLimits[1].split('=').length ? validLimits[1].split('=')[1] : null,
  };
};
