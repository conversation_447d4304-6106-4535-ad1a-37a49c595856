import { call, fork, put, select, take } from 'redux-saga/effects';
import shortid from 'shortid';

import { message } from '@manyun/base-ui.ui.message';
import { ConfigRangeType } from '@manyun/ticket.model.task';
import { InspectionScenarios } from '@manyun/ticket.model.ticket';

import { INSPECTION_TYPE_KEY_MAP } from '@manyun/dc-brain.legacy.constants/inspection';
import { redirectActionCreator } from '@manyun/dc-brain.legacy.redux/actions/routerActions';
import { doPointService, taskCenterService } from '@manyun/dc-brain.legacy.services';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';

import {
  createConfigActionCreator,
  deleteConfigActionCreator,
  editConfigActionCreator,
  getChildItemsActionCreator,
  getDataActionCreator,
  getPointsActionCreator,
  inspectionConfigActions,
  resetSearchValuesActionCreator,
  setPaginationThenGetDataActionCreator,
} from '../actions/inspectionConfigActions';
import {
  getCreateOptions,
  getEditOptions,
  getRoomTypesInCommon,
  getSearchValuesAndPagination,
} from '../selectors/inspectionConfigSelector';

/***************************** Subroutines ************************************/

function getQ(pagination, searchValues) {
  const baseQ = { ...pagination };
  for (const [, { name, value }] of Object.entries(searchValues)) {
    const val = value === '' ? null : value;
    if (name === 'configName' && val) {
      baseQ[name] = val.trim();
    } else if (name === 'creatorId' && val) {
      baseQ[name] = val.value;
    } else if (name === 'createTime' && val && val.length) {
      baseQ.createStartTime = Number(val[0].unix() + '000');
      baseQ.createEndTime = Number(val[1].unix() + '000');
    } else if (
      name === 'subTypeCode' &&
      val &&
      searchValues.inspectSubject.value === INSPECTION_TYPE_KEY_MAP.DEVICE
    ) {
      if (!val.thirdCategorycode) {
        baseQ[name] = val;
      } else {
        baseQ.subTypeName = val.thirdCategoryName;
        baseQ.firstCategoryName = val.firstCategoryName;
        baseQ.secondCategoryName = val.secondCategoryName;
        baseQ[name] = val.thirdCategorycode;
      }
    } else if (
      name === 'roomType' &&
      val &&
      searchValues.inspectSubject.value === INSPECTION_TYPE_KEY_MAP.ENVIRONMENT
    ) {
      baseQ.subTypeCode = val;
    } else {
      if (name) {
        baseQ[name] = val;
      }
    }
  }
  return baseQ.effectType === ConfigRangeType.All
    ? { ...baseQ, effectDomain: ConfigRangeType.All }
    : baseQ;
}

function* getData(payload) {
  const { shouldResetPageNum = true, effectType } = payload;

  yield put(inspectionConfigActions.updateTableLoading(true));
  if (shouldResetPageNum) {
    yield put(inspectionConfigActions.resetPageNum());
  }
  const { searchValues, pagination } = yield select(getSearchValuesAndPagination);
  const q = getQ(pagination, searchValues);
  if (q.inspectType === INSPECTION_TYPE_KEY_MAP.ENVIRONMENT) {
    q.subTypeCode = q.roomType;
    delete q.roomType;
  }
  const { response, error } = yield call(taskCenterService.fetchInspectionConfigData, {
    ...q,
    effectType,
  });
  if (error) {
    message.error(error);
    yield put(inspectionConfigActions.failure());
  } else {
    yield put(inspectionConfigActions.setDataAndTotal(response));
  }
}

function* resetSearchValues(effectType) {
  yield put(inspectionConfigActions.resetSearchValuesAndPagination());
  yield put(getDataActionCreator({ shouldResetPageNum: false, effectType }));
}

function* setPagination(newPagination) {
  const { pageNum, pageSize, effectType } = newPagination;

  yield put(inspectionConfigActions.setPagination({ pageNum, pageSize }));
  yield put(getDataActionCreator({ shouldResetPageNum: false, effectType }));
}

function* deleteConfig(payload) {
  const { error } = yield call(taskCenterService.deleteInspectionConfig, payload);
  if (error) {
    message.error(error);
  } else {
    message.success('删除成功！');
    yield put(getDataActionCreator({ shouldResetPageNum: false, effectType: payload.effectType }));
  }
}

function getCheckItemsData(p, mode) {
  let data = [];
  if (p && p.length) {
    p.forEach(({ checkItemName, checkStdInfoList }) => {
      const children = checkStdInfoList.map(item1 => {
        return {
          ...item1,
          standardId: shortid(),
          standardTxt: {
            value:
              mode !== 'detail' && item1.checkStd.includes('【阈值：')
                ? item1.checkStd.substring(0, item1.checkStd.indexOf('【阈值：'))
                : item1.checkStd,
          },
        };
      });
      const sameCheckItemNames = data.filter(item => item.checkItemName.value === checkItemName);
      if (sameCheckItemNames.length) {
        data = data.map(item => {
          if (item.checkItemName.value === checkItemName) {
            return {
              ...item,
              standards: [...item.standards, ...children],
            };
          }
          return item;
        });
        return;
      }
      data = [
        ...data,
        {
          id: shortid(),
          checkItemName: {
            value: checkItemName,
            name: 'checkItemName',
          },
          standards: children,
        },
      ];
    });
  }
  return data;
}

function getPointData(p) {
  let data = [];
  if (p && p.length) {
    data = p.map(item => {
      return {
        ...item,
        metaCode: item.pointCode,
        metaName: item.pointName,
        validLimits: typeof item.diValueText === 'string' ? item.diValueText.split(',') : [],
        dataType: {
          code: item.dataType,
        },
      };
    });
  }

  return data;
}

function* getConfigsChildItems({ id, mode }) {
  const { error, response } = yield call(taskCenterService.fetchInspectionConfigDetail, id);
  if (error) {
    message.error(error);
  } else {
    const {
      id,
      inspectType,
      inspectSubject,
      subTypeCode,
      checkPoints,
      checkItems,
      configName,
      extJson,
      subTypeName,
      supportCheckScenes,
    } = response;
    const data = {
      ...response,
      config: {
        [inspectSubject]: {
          [subTypeCode]: {
            CUSTOM: getCheckItemsData(checkItems, mode),
            POINT: getPointData(checkPoints),
          },
        },
      },
    };

    const editOptions = {
      id: {
        value: id,
        name: 'id',
      },
      effectDomain: {
        name: 'effectDomain',
        value: response.effectType === ConfigRangeType.All ? undefined : response.effectDomain,
      },
      effectType: {
        name: 'effectType',
        value: response.effectType,
      },
      inspectSubject: {
        value: inspectSubject,
        name: 'inspectSubject',
      },
      inspectType: {
        value: inspectType,
        name: 'inspectType',
      },
      subTypeCode: {
        value:
          inspectSubject === INSPECTION_TYPE_KEY_MAP.DEVICE
            ? {
                thirdCategorycode: subTypeCode,
                thirdCategoryName: subTypeName,
                firstCategoryName: extJson.firstCategoryName,
                secondCategoryName: extJson.secondCategoryName,
              }
            : null,
        name: 'subTypeCode',
      },
      roomType: {
        value: inspectSubject === INSPECTION_TYPE_KEY_MAP.ENVIRONMENT ? subTypeCode : null,
        name: 'roomType',
      },
      configName: {
        value: configName,
        name: 'configName',
      },
      supportCheckScenes: {
        value: supportCheckScenes,
        name: 'supportCheckScenes',
      },
      config: data.config,
    };
    yield put(inspectionConfigActions.setConfigInfos(data));
    yield put(inspectionConfigActions.setEditConfigInfos(editOptions));
  }
}

function* getPoints(deviceType) {
  const { error, response } = yield call(doPointService.fetchDispositionPoint, { deviceType });
  if (error) {
    message.error(error);
  } else {
    let newData = [];
    if (response.data.length) {
      newData = response.data.map(({ code, name, dataType, unit, maxValue, minValue }) => {
        return {
          pointCode: code,
          pointName: name,
          dataType: dataType.code,
          unit,
          maxValue,
          minValue,
          deviceType: deviceType,
          isMeterRead: false,
        };
      });
    }
    yield put(inspectionConfigActions.setPointsInDevice(newData));
  }
}

function getCreateOrEditOptions(options, configTableDate) {
  const q = getQ({}, options);
  const { inspectSubject, subTypeCode } = q;
  let customs = [];
  let points = [];
  if (
    configTableDate &&
    configTableDate[inspectSubject] &&
    configTableDate[inspectSubject][subTypeCode] &&
    configTableDate[inspectSubject][subTypeCode].CUSTOM &&
    configTableDate[inspectSubject][subTypeCode].CUSTOM.length
  ) {
    customs = configTableDate[inspectSubject][subTypeCode].CUSTOM.map(
      ({ checkItemName, standards }) => {
        const standArr = standards.map(
          ({
            isMeterRead,
            unit,
            maxValue,
            minValue,
            standardTxt,
            checkScenes,
            // checkScenes = InspectionScenarios.All,
            normalTextList,
            exTextList,
            defaultCheckScenes,
          }) => {
            let checkStd = null;
            if (standardTxt && standardTxt.value) {
              checkStd = standardTxt.value.trim();
            }
            if (checkStd && typeof maxValue === 'number' && typeof minValue === 'number') {
              checkStd = `${checkStd}【阈值：${minValue}~${maxValue}】`;
            }

            return {
              isMeterRead,
              unit,
              maxValue,
              minValue,
              checkStd: checkStd,
              checkScenes,
              normalTextList,
              exTextList,
              defaultCheckScenes,
            };
          }
        );
        if (standArr.length) {
          return {
            // isMeterRead: isMeterRead ? isMeterRead : 0,
            // unit: unit,
            checkItemName: checkItemName && checkItemName.value ? checkItemName.value.trim() : null,
            checkStdInfoList: standArr,
          };
        } else {
          return null;
        }
      }
    ).filter(Boolean);
  }
  if (
    configTableDate &&
    configTableDate[inspectSubject] &&
    configTableDate[inspectSubject][subTypeCode] &&
    configTableDate[inspectSubject][subTypeCode].POINT &&
    configTableDate[inspectSubject][subTypeCode].POINT.length
  ) {
    points = configTableDate[inspectSubject][subTypeCode].POINT.map(p => {
      let tmp = {
        deviceType: subTypeCode,
        pointName: p.metaName,
        pointCode: p.pointCode,
        dataType: p.dataType ? p.dataType.code : null,
        isMeterRead: p.isMeterRead ? p.isMeterRead : 0,
        unit: p.unit,
        diValueText: '',
        maxValue: p.maxValue,
        minValue: p.minValue,
        checkScenes: p.checkScenes,
        normalTextList: p.normalTextList,
        exTextList: p.exTextList,
        defaultCheckScenes: p.defaultCheckScenes,
      };

      if (p.dataType && p.dataType.code === 'DI') {
        tmp = {
          ...tmp,
          diValueText: p.validLimits?.join(','),
        };
      }
      return tmp;
    });
  }

  // 接口要求 如果没有巡检项 则传递参数为null
  const params = {
    ...q,
    checkPoints: points ? points : null,
    checkItems: customs.length ? customs : null,
  };
  return params;
}

function* createConfig(payload) {
  if (payload.mode === 'new') {
    const { create, configTableDate } = yield select(getCreateOptions);
    if (!Object.keys(configTableDate).length) {
      message.error('至少选择一个巡检项');
      return;
    }
    const params = getCreateOrEditOptions(create, configTableDate);
    if (params.inspectSubject === INSPECTION_TYPE_KEY_MAP.ENVIRONMENT) {
      const { subTypeCode } = params;
      const roomTypes = yield select(getRoomTypesInCommon);
      params.subTypeName = roomTypes[subTypeCode];
    }
    const { error } = yield call(taskCenterService.fetchCreateInspectionConfig, params);
    if (error) {
      message.error(error);
    } else {
      message.success('新建巡检配置成功！');
      yield put(redirectActionCreator(urls.generateInspectionConfigListLocation()));
    }
  } else {
    const { edit, configTableDate } = yield select(getEditOptions);
    if (!Object.keys(configTableDate).length) {
      message.error('至少选择一个巡检项');
      return;
    }
    const params = getCreateOrEditOptions(edit, configTableDate);
    if (params.inspectSubject === INSPECTION_TYPE_KEY_MAP.ENVIRONMENT) {
      const { subTypeCode } = params;
      const roomTypes = yield select(getRoomTypesInCommon);
      params.subTypeName = roomTypes[subTypeCode];
    }
    const { error } = yield call(taskCenterService.fetchCreateInspectionConfig, params);
    if (error) {
      message.error(error);
    } else {
      message.success('复制巡检配置成功！');
      yield put(redirectActionCreator(urls.generateInspectionConfigListLocation()));
    }
  }
}

function* editConfig() {
  const { edit, configTableDate } = yield select(getEditOptions);
  if (!Object.keys(configTableDate).length) {
    message.error('至少选择一个巡检项');
    return;
  }
  const params = getCreateOrEditOptions(edit, configTableDate);
  if (params.inspectSubject === INSPECTION_TYPE_KEY_MAP.ENVIRONMENT) {
    const { subTypeCode } = params;

    const roomTypes = yield select(getRoomTypesInCommon);
    params.subTypeName = roomTypes[subTypeCode];
  }
  params.inspectConfigId = params.id;
  delete params.id;
  const { error } = yield call(taskCenterService.fetchUpdateInspectionConfig, params);
  if (error) {
    message.error(error);
  } else {
    message.success('编辑巡检配置成功！');
    yield put(redirectActionCreator(urls.generateInspectionConfigListLocation()));
  }
}

/******************************************************************************/
/******************************* WATCHERS *************************************/
/******************************************************************************/
function* watchGetDataAction() {
  while (true) {
    const { payload } = yield take(getDataActionCreator.type);
    yield fork(getData, payload);
  }
}

function* watchResetSearchValues() {
  while (true) {
    const { payload } = yield take(resetSearchValuesActionCreator.type);
    yield fork(resetSearchValues, payload);
  }
}

function* watchSetPagination() {
  while (true) {
    const { payload } = yield take(setPaginationThenGetDataActionCreator.type);
    yield fork(setPagination, payload);
  }
}

function* watchDeleteConfig() {
  while (true) {
    const { payload } = yield take(deleteConfigActionCreator.type);
    yield fork(deleteConfig, payload);
  }
}

function* watchGetConfigsChildItems() {
  while (true) {
    const { payload } = yield take(getChildItemsActionCreator.type);
    yield fork(getConfigsChildItems, payload);
  }
}

function* watchGetPoints() {
  while (true) {
    const { payload } = yield take(getPointsActionCreator.type);
    yield fork(getPoints, payload);
  }
}

function* watchCreateConfig() {
  while (true) {
    const { payload } = yield take(createConfigActionCreator.type);
    yield fork(createConfig, payload);
  }
}

function* watchEditConfig() {
  while (true) {
    const { payload } = yield take(editConfigActionCreator.type);
    yield fork(editConfig, payload);
  }
}

export default [
  fork(watchGetDataAction),
  fork(watchResetSearchValues),
  fork(watchSetPagination),
  fork(watchDeleteConfig),
  fork(watchGetConfigsChildItems),
  fork(watchGetPoints),
  fork(watchCreateConfig),
  fork(watchEditConfig),
];
