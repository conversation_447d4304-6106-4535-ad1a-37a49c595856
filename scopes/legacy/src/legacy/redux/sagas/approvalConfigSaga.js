import { push } from 'connected-react-router';
import { call, fork, put, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { editScenes } from '@manyun/bpm.service.edit-scenes';
import { fetchScenes } from '@manyun/bpm.service.fetch-scenes';

import { approvalConfigService } from '@manyun/dc-brain.legacy.services';

import {
  approvalConfigActions,
  approvalProcessCreate,
  approvalProcessDetail,
  approvalProcessEdit,
  approvalProcessList,
  approvalScenesEdit,
  approvalScenesList,
  deleteScenesProcessActionCreator,
  scenesProcessRelateEdit,
  scenesProcessRelateList,
} from '../actions/approvalConfigActions';

/***************************** Subroutines ************************************/
function* fetchApprovalProcessList(payload) {
  yield put(approvalConfigActions.request());
  const { response, error } = yield call(approvalConfigService.fetchApprovalProcessList, payload);
  if (response) {
    yield put(approvalConfigActions.featchApprovalProcessListSuccess(response));
    yield put(approvalConfigActions.savePageCondition(payload));
  } else {
    message.error(error);
    yield put(approvalConfigActions.failure());
  }
}

function* fetchApprovalProcessCreate(payload) {
  yield put(approvalConfigActions.createRequest());
  const { response, error } = yield call(approvalConfigService.fetchApprovalProcessCreate, payload);
  if (response) {
    yield put(push('/page/approval-config-process/list'));
  } else {
    message.error(error);
    yield put(approvalConfigActions.createFailure());
  }
}

function* fetchApprovalProcessEdit(payload) {
  yield put(approvalConfigActions.editRequest());
  const { response, error } = yield call(approvalConfigService.fetchApprovalProcessEdit, payload);
  if (response) {
    message.success('编辑成功！');
    yield put(push('/page/approval-config-process/list'));
  } else {
    message.error(error);
    yield put(approvalConfigActions.editFailure());
  }
}

function* fetchApprovalProcessDetail(payload) {
  yield put(approvalConfigActions.request());
  const { response, error } = yield call(approvalConfigService.fetchApprovalProcessDetail, payload);
  if (response) {
    yield put(approvalConfigActions.featchApprovalProcessDetailSuccess(response));
  } else {
    message.error(error);
    yield put(approvalConfigActions.failure());
  }
}

function* fetchApprovalScenesList(payload) {
  yield put(approvalConfigActions.featchApprovalScenesListSuccess({ data: [] }));
  yield put(approvalConfigActions.scenesListRequest());
  const { data, error } = yield call(fetchScenes, payload);
  if (error) {
    message.error(error.message);
    yield put(approvalConfigActions.scenesListFailure());
  }
  yield put(approvalConfigActions.featchApprovalScenesListSuccess(data));
  yield put(approvalConfigActions.setSearchCondition(payload.subBizScenes));
}

function* fetchApprovalScenesEdit({ params: payload, callback, searchCondition }) {
  yield put(approvalConfigActions.scenesListRequest());
  const { error } = yield call(editScenes, payload);

  if (error) {
    message.error(error.message);
    return;
  }
  message.success('编辑成功');
  callback();
  yield put(approvalScenesList({ subBizScenes: searchCondition }));
}

function* fetchScenesProcesReleteList({ subBizScenes, callback }) {
  const { response, error } = yield call(approvalConfigService.fetchScenesProcesReleteList, {
    subBizScenes,
  });
  if (response) {
    callback(response.data);
  } else {
    message.error(error);
  }
}

function* fetchScenesProcesReleteEdit({ params: payload, callback, searchCondition }) {
  const { response, error } = yield call(
    approvalConfigService.fetchScenesProcesReleteEdit,
    payload
  );
  if (response) {
    message.success('关联成功');
    callback();
    yield put(approvalScenesList({ subBizScenes: searchCondition }));
  } else {
    message.error(error);
  }
}

function* deleteScenesProcess({ processCode, searchParams }) {
  const { response, error } = yield call(approvalConfigService.fetchScenesProcesDelete, {
    processCode,
  });
  if (response) {
    message.success('删除成功');
    yield put(approvalProcessList(searchParams));
  } else {
    message.error(error);
  }
}

export function* watchFetchApprovalProcessList() {
  while (true) {
    const { payload } = yield take(approvalProcessList.type);
    yield fork(fetchApprovalProcessList, payload);
  }
}

export function* watchFetchApprovalProcessCreate() {
  while (true) {
    const { payload } = yield take(approvalProcessCreate.type);
    yield fork(fetchApprovalProcessCreate, payload);
  }
}

export function* watchFetchApprovalProcessDetail() {
  while (true) {
    const { payload } = yield take(approvalProcessDetail.type);
    yield fork(fetchApprovalProcessDetail, payload);
  }
}

export function* watchFetchApprovalProcessEdit() {
  while (true) {
    const { payload } = yield take(approvalProcessEdit.type);
    yield fork(fetchApprovalProcessEdit, payload);
  }
}

export function* watchFetchApprovalScenesList() {
  while (true) {
    const { payload } = yield take(approvalScenesList.type);
    yield fork(fetchApprovalScenesList, payload);
  }
}

export function* watchFetchApprovalScenesEdit() {
  while (true) {
    const { payload } = yield take(approvalScenesEdit.type);
    yield fork(fetchApprovalScenesEdit, payload);
  }
}

export function* watchFetchScenesProcesReleteList() {
  while (true) {
    const { payload } = yield take(scenesProcessRelateList.type);
    yield fork(fetchScenesProcesReleteList, payload);
  }
}

export function* watchFetchScenesProcesReleteEdit() {
  while (true) {
    const { payload } = yield take(scenesProcessRelateEdit.type);
    yield fork(fetchScenesProcesReleteEdit, payload);
  }
}

export function* watchDeleteScenesProcess() {
  while (true) {
    const { payload } = yield take(deleteScenesProcessActionCreator.type);
    yield fork(deleteScenesProcess, payload);
  }
}

export default [
  fork(watchFetchApprovalProcessList),
  fork(watchFetchApprovalProcessCreate),
  fork(watchFetchApprovalProcessDetail),
  fork(watchFetchApprovalProcessEdit),
  fork(watchFetchApprovalScenesList),
  fork(watchFetchApprovalScenesEdit),
  fork(watchFetchScenesProcesReleteList),
  fork(watchFetchScenesProcesReleteEdit),
  fork(watchDeleteScenesProcess),
];
