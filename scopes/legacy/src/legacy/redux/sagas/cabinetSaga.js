import { call, fork, put, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { cabinetManageService } from '@manyun/dc-brain.legacy.services';

import {
  batchUpdateActionCreator,
  cabinetActions,
  fetchCabinetDetail, // 分页查询机柜列表
  fetchCabinetListPage, // 编辑
  fetchUpdateCabinet,
} from '../actions/cabinetActions';

// 监听分页查询机柜列表

/***************************** Subroutines ************************************/

function* fetchCabinetPage(payload) {
  yield put(cabinetActions.request());
  const { response, error } = yield call(cabinetManageService.fetchGrids, payload);
  if (response) {
    yield put(cabinetActions.saveCabinetPageConditionSuccess(payload));
    yield put(cabinetActions.featchCabinetPageSuccess(response));
  } else {
    message.error(error || '机柜列表请求失败');
    yield put(cabinetActions.failure());
  }
}

function* updateCabinet(payload) {
  yield put(cabinetActions.editCabinetSubmitLoading());
  const { editMess, cabinetPageCondition } = payload;
  const { response, error } = yield call(cabinetManageService.updateCabinet, editMess);
  if (response) {
    yield put(cabinetActions.editCabinetSubmitLoading());
    message.success('更新机柜成功');
    yield put(cabinetActions.editCabinetVisible());
    yield fork(fetchCabinetPage, cabinetPageCondition);
  } else {
    message.error(error || '编辑机柜请求失败');
    yield put(cabinetActions.failure());
  }
}

function* fetchCabinetMess(payload) {
  const { response, error } = yield call(cabinetManageService.fetchCabinetDetail, payload);
  if (response) {
    yield put(cabinetActions.saveCabinetDetailMess(response));
  } else {
    message.error(error || '机柜详情请求失败');
    yield put(cabinetActions.failure());
  }
}

function* batchUpdate({ data, searchValues, successCallback, errorCallback }) {
  const { error } = yield call(cabinetManageService.batchUpdateCabinet, data);
  if (error) {
    errorCallback();
    message.error(error);
    return;
  }
  successCallback();
  message.success('批量更新成功！');
  yield put(fetchCabinetListPage(searchValues));
}

/******************************************************************************/
/******************************* WATCHERS *************************************/
/******************************************************************************/

export function* watchFetchCabinetPage() {
  while (true) {
    const { payload } = yield take(fetchCabinetListPage.type);
    yield fork(fetchCabinetPage, payload);
  }
}

// 编辑机柜
export function* watchFetchUpdateCabinet() {
  while (true) {
    const { payload } = yield take(fetchUpdateCabinet.type);
    yield fork(updateCabinet, payload);
  }
}

export function* watchFetchCabinetMess() {
  while (true) {
    const { payload } = yield take(fetchCabinetDetail.type);
    yield fork(fetchCabinetMess, payload);
  }
}

export function* watchBatchUpdate() {
  while (true) {
    const { payload } = yield take(batchUpdateActionCreator.type);
    yield fork(batchUpdate, payload);
  }
}

export default [
  fork(watchFetchCabinetPage),
  fork(watchFetchUpdateCabinet),
  fork(watchFetchCabinetMess),
  fork(watchBatchUpdate),
];
