import { call, cancel, fork, put, race, select, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { visitorManagerService } from '@manyun/dc-brain.legacy.services';

import {
  batchRemoveVisitorBlacklistActionCreator,
  resetSearchValuesActionCreator,
  setPaginationThenGetDataActionCreator,
  visitorBlacklistActionCreator,
  visitorBlacklistActions,
} from '../actions/visitorBlacklistActions';
import { getSearchValuesAndPagination } from './../selectors/visitorBlacklistSeletcors';

function getQ(pagination, searchValues) {
  const baseQ = { ...pagination };
  for (const [, { name, value }] of Object.entries(searchValues)) {
    const val = value === '' ? null : value;
    if (name === 'name') {
      baseQ[name] = val;
    } else if (name === 'identityNo') {
      baseQ[name] = val;
    } else if (name === 'sex') {
      baseQ[name] = val;
    } else if (name === 'certificateType') {
      baseQ[name] = val;
    }
  }
  return baseQ;
}

// workers

function* getData(shouldResetPageNum = true) {
  yield put(visitorBlacklistActions.request());
  if (shouldResetPageNum) {
    yield put(visitorBlacklistActions.resetPageNum());
  }
  const { searchValues, pagination } = yield select(getSearchValuesAndPagination);
  const q = getQ(pagination, searchValues);
  const { response, error } = yield call(visitorManagerService.fetchVisitorBlacklistPage, q);
  if (error) {
    message.error(error);
  } else {
    yield put(visitorBlacklistActions.setDataAndTotal(response));
  }
}

function* resetSearchValues() {
  yield put(visitorBlacklistActions.resetSearchValuesAndPagination());
  yield put(visitorBlacklistActionCreator(false));
}

function* setPagination(newPagination) {
  yield put(visitorBlacklistActions.setPagination(newPagination));
  yield put(visitorBlacklistActionCreator(false));
}

function* batchRemoveVisitorBlacklist({ ids }) {
  const { error } = yield call(visitorManagerService.fetchBatchRemoveVisitorBlacklist, {
    ids,
  });
  if (error) {
    message.error(error);
  } else {
    message.success(`移除成功！`);
    yield put(visitorBlacklistActions.resetSelectedRowKeys());
    yield put(visitorBlacklistActionCreator(true));
  }
}

// watchers

function* watchGetData() {
  while (true) {
    const { payload } = yield take(visitorBlacklistActionCreator.type);
    const task = yield fork(getData, payload);
    const [reset] = yield race([take(resetSearchValuesActionCreator.type), task]);
    if (reset) {
      cancel(task);
    }
  }
}

function* watchResetSearchValues() {
  while (true) {
    yield take(resetSearchValuesActionCreator.type);
    yield fork(resetSearchValues);
  }
}

function* watchSetPagination() {
  while (true) {
    const { payload } = yield take(setPaginationThenGetDataActionCreator.type);
    yield fork(setPagination, payload);
  }
}

function* watchBatchRemoveVisitorBlacklist() {
  while (true) {
    const { payload } = yield take(batchRemoveVisitorBlacklistActionCreator.type);
    yield fork(batchRemoveVisitorBlacklist, payload);
  }
}

export default [
  fork(watchGetData),
  fork(watchResetSearchValues),
  fork(watchSetPagination),
  fork(watchBatchRemoveVisitorBlacklist),
];
