import uniq from 'lodash/uniq';
import uniqBy from 'lodash/uniqBy';
import { call, fork, put, select, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { selectSpaces } from '@manyun/resource-hub.state.space';

import { equipmentManageService } from '@manyun/dc-brain.legacy.services';

import { batteryActions, getIdcBatteryActionCreator } from '../actions/batteryActions';
import { syncCommonDataActionCreator } from '../actions/commonActions';
import { getCommonDeviceCategory } from '../selectors/batterySelector';
import { getCurrentConfig } from '../selectors/configSelectors';

/***************************** Subroutines ************************************/
function* getIdcBattery(idc) {
  yield put(batteryActions.setBlockRoom());
  const { entities: space } = yield select(selectSpaces);
  const config = yield select(getCurrentConfig);
  const configUtil = new ConfigUtil(config);
  const batteryUnitTypes = configUtil.getAllDeviceTypes(
    ConfigUtil.constants.deviceTypes.BATTERY_UNIT
  );
  const deviceCategory = yield select(getCommonDeviceCategory);
  if (!deviceCategory || !batteryUnitTypes || !batteryUnitTypes.length) {
    return;
  }
  const secondCategory = deviceCategory.normalizedList[batteryUnitTypes[0]].parentCode.slice(2);
  const topCategory = deviceCategory.normalizedList[secondCategory].parentCode.slice(2);
  const params = {
    deviceTypeList: batteryUnitTypes,
    idcTag: idc,
    region: space[idc].parentCode,
    secondCategory: secondCategory,
    topCategory: topCategory,
    pageNum: 1,
    pageSize: 5000,
  };
  // 请求电池组设备类型下的所有设备
  const { response, error } = yield call(equipmentManageService.fetchEquipmentListPage, params);
  if (error) {
    message.error(error);
  }
  if (response) {
    yield put(
      syncCommonDataActionCreator({
        strategy: {
          deviceTypesPointsDefinition: uniq(response.data.map(({ deviceType }) => deviceType)),
        },
      })
    );
    yield put(batteryActions.setRoomTagDevice(response.data));
    const location = getLocations(response.data);
    yield put(batteryActions.setBlockRoom(location));
    if (location.length) {
      yield put(batteryActions.setSelectedTreeKey(location[0]));
    }
  }
}

function getLocations(data) {
  let locationObj = {};
  data.forEach(device => {
    const {
      spaceGuid: { blockGuid, blockTag, roomTag, roomGuid },
    } = device;
    if (!locationObj[blockTag]) {
      locationObj[blockTag] = [
        {
          metaType: 'ROOM',
          metaCode: roomGuid,
          metaName: roomTag,
          parentCode: blockGuid,
        },
      ];
    } else {
      locationObj[blockTag] = [
        ...locationObj[blockTag],
        {
          metaType: 'ROOM',
          metaCode: roomGuid,
          metaName: roomTag,
          parentCode: blockGuid,
        },
      ];
    }
  });
  const location = Object.keys(locationObj).map(block => {
    return {
      metaType: 'BLOCK',
      metaCode: locationObj[block][0].parentCode,
      metaName: block,
      children: uniqBy(locationObj[block], 'metaCode'),
    };
  });
  return location;
}

/******************************************************************************/
/******************************* WATCHERS *************************************/
/******************************************************************************/

export function* watchGetIdcBattery() {
  while (true) {
    const { payload } = yield take(getIdcBatteryActionCreator.type);
    yield fork(getIdcBattery, payload);
  }
}

export default [fork(watchGetIdcBattery)];
