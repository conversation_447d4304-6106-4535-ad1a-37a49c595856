import { call, fork, put, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { versionManageService } from '@manyun/dc-brain.legacy.services';

import {
  createVersionBtn,
  deleteVersionBtn,
  deployListBtn,
  deployVersionBtn,
  fetchVersionManagePage,
  versionListBtn,
  versionManageActions,
} from '../actions/versionManageActions';

function* fetchVersionList(payload) {
  yield put(versionManageActions.request());
  const { response, error } = yield call(versionManageService.fetchVersionList, payload);
  if (response) {
    yield put(versionManageActions.fetchVersionListSuccess(response));
  } else {
    message.error(error || '请求失败');
    yield put(versionManageActions.failure());
  }
}

export function* watchFetchVersionList() {
  while (true) {
    const { payload } = yield take(fetchVersionManagePage.type);
    yield fork(fetchVersionList, payload);
  }
}

function* createVersion(payload) {
  yield put(versionManageActions.confirmStart());
  const { response, error } = yield call(versionManageService.createVersion, payload);
  if (response) {
    yield put(versionManageActions.changeModal('createVersionShow'));
    yield put(versionManageActions.request());
    yield fork(fetchVersionList, payload);
  } else {
    message.error(error || '请求失败');
    yield put(versionManageActions.failure());
  }
  yield put(versionManageActions.confirmEnd());
}

export function* watchCreateVersion() {
  while (true) {
    const { payload } = yield take(createVersionBtn.type);
    yield fork(createVersion, payload);
  }
}

function* versionList(payload) {
  yield put(versionManageActions.request());
  const { response, error } = yield call(versionManageService.versionList, payload);
  if (response) {
    if (payload.from === 'batch') {
      const data = {};
      data[payload.name] = response;
      yield put(versionManageActions.rangeVersionDataSuccess(data));
    } else {
      yield put(versionManageActions.rangeVersionListSuccess(response));
    }
  } else {
    message.error(error || '请求失败');
    yield put(versionManageActions.failure());
  }
}

export function* watchVersionList() {
  while (true) {
    const { payload } = yield take(versionListBtn.type);
    yield fork(versionList, payload);
  }
}

function* deployVersion(payload) {
  yield put(versionManageActions.confirmStart());
  const { response, error } = yield call(versionManageService.deployVersion, payload);
  if (response) {
    switch (payload.from) {
      case 'record':
        yield fork(versionList, { type: payload.type, range: payload.range });
        break;
      case 'deploy':
        yield put(versionManageActions.deployVersionSuccess());
        break;
      case 'batch':
        yield put(versionManageActions.changeModal('batchDeployShow'));
        break;
      default:
        console.warn('error for deployVersion');
        yield put(versionManageActions.confirmEnd());
        break;
    }
    yield put(versionManageActions.request());
    yield fork(fetchVersionList, payload);
  } else {
    message.error(error || '请求失败');
    yield put(versionManageActions.failure());
  }
  yield put(versionManageActions.confirmEnd());
}

export function* watchDeployVersion() {
  while (true) {
    const { payload } = yield take(deployVersionBtn.type);
    yield fork(deployVersion, payload);
  }
}

//删除版本
function* deleteVersion(payload) {
  yield put(versionManageActions.request());
  const { response, error } = yield call(versionManageService.deleteVersion, payload);
  if (response) {
    yield fork(versionList, { type: payload.type, range: payload.range });
    // yield put(versionManageActions.createVersionSuccess(response));
  } else {
    message.error(error || '请求失败');
    yield put(versionManageActions.failure());
  }
}

export function* watchDeleteVersion() {
  while (true) {
    const { payload } = yield take(deleteVersionBtn.type);
    yield fork(deleteVersion, payload);
  }
}

//查询发布记录
function* deployList(payload) {
  yield put(versionManageActions.request());
  const { response, error } = yield call(versionManageService.deployList, payload);
  if (response) {
    yield put(versionManageActions.deployListSuccess(response));
  } else {
    message.error(error || '请求失败');
    yield put(versionManageActions.failure());
  }
}

export function* watchDeployList() {
  while (true) {
    const { payload } = yield take(deployListBtn.type);
    yield fork(deployList, payload);
  }
}

export default [
  fork(watchFetchVersionList),
  fork(watchCreateVersion),
  fork(watchVersionList),
  fork(watchDeleteVersion),
  fork(watchDeployVersion),
  fork(watchDeployList),
];
