import { call, fork, put, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { vendorService } from '@manyun/dc-brain.legacy.services';

import {
  deleteVendorActionCreator,
  fetchVendorDetail,
  fetchVendorListPage,
  vendorActions,
} from '../actions/vendorActions';

function* fetchVendorList(payload) {
  yield put(vendorActions.request());
  const { response, error } = yield call(vendorService.fetchVendorList, payload);
  if (response) {
    yield put(vendorActions.saveVendorPageConditionSuccess(payload));
    yield put(vendorActions.fetchVendorListSuccess(response));
  } else {
    message.error(error || '请求失败');
    yield put(vendorActions.failure());
  }
}

function* deleteVendor({ deleteparams, searchParams }) {
  const { response, error } = yield call(vendorService.fetchDeleteVendor, deleteparams);
  if (response) {
    message.success('删除成功！');
    yield put(fetchVendorListPage(searchParams));
  } else {
    message.error(error);
  }
}

function* getVendorDetail({ params }) {
  const { response, error } = yield call(vendorService.getVendorDetail, params);
  if (response) {
    yield put(vendorActions.saveVendorDetailSuccess(response));
  } else {
    message.error(error);
  }
}

export function* watchFetchVendorList() {
  while (true) {
    const { payload } = yield take(fetchVendorListPage.type);
    yield fork(fetchVendorList, payload);
  }
}

export function* watchDeleteVendor() {
  while (true) {
    const { payload } = yield take(deleteVendorActionCreator.type);
    yield fork(deleteVendor, payload);
  }
}

export function* watchGetVendorDetail() {
  while (true) {
    const { payload } = yield take(fetchVendorDetail.type);
    yield fork(getVendorDetail, payload);
  }
}

export default [fork(watchFetchVendorList), fork(watchDeleteVendor), fork(watchGetVendorDetail)];
