import { call, fork, put, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { basicResourcesIdcService, treeDataService } from '@manyun/dc-brain.legacy.services';

import {
  basicResourcesIdcActions,
  fetchBasicResourceIdcList,
  fetchGetOperationStatus,
  getAreaTreeMetaQuery,
} from '../actions/basicResourcesIdcActions';

function* basicResourceIdcList(data) {
  yield put(basicResourcesIdcActions.request());
  if (!data.operationStatus) {
    data.operationStatus = null;
  }
  const { response, error } = yield call(basicResourcesIdcService.fetchIdcList, data);
  if (response) {
    yield put(
      basicResourcesIdcActions.success({
        list: response.data,
        total: response.total,
        loading: false,
        pageNum: data.pageNum,
        pageSize: data.pageSize,
      })
    );
  } else {
    message.error(error || '机房列表请求失败');
    yield put(basicResourcesIdcActions.failure());
  }
}

function* watchFetchBasicResourceIdcList() {
  while (true) {
    const { payload } = yield take(fetchBasicResourceIdcList.type);
    yield fork(basicResourceIdcList, payload);
  }
}

function* areaTreeMetaQuery() {
  const { normalizedList } = yield call(treeDataService.fetchAreaTreeMetaQuery);
  if (normalizedList) {
    yield put(basicResourcesIdcActions.areaTreeMetaQuery(normalizedList));
  }
}

function* getOperationStatus() {
  yield put(basicResourcesIdcActions.onSelectLoading());
  const { response, error } = yield call(basicResourcesIdcService.fetchGetOperationStatus);
  if (response) {
    const list = [];
    for (const i in response) {
      list.push({ label: response[i], value: i });
    }
    yield put(basicResourcesIdcActions.operationStatusSuccess(list));
    yield put(basicResourcesIdcActions.onSelectLoading());
  } else {
    message.error(error || '获取状态失败');
    yield put(basicResourcesIdcActions.onSelectLoading());
  }
}

function* watchFetchGetOperationStatus() {
  while (true) {
    yield take(fetchGetOperationStatus.type);
    yield fork(getOperationStatus);
  }
}

function* watchFetchAreaTreeMetaQuery() {
  while (true) {
    yield take(getAreaTreeMetaQuery.type);
    yield fork(areaTreeMetaQuery);
  }
}
export default [
  fork(watchFetchBasicResourceIdcList),
  fork(watchFetchGetOperationStatus),
  fork(watchFetchAreaTreeMetaQuery),
];
