// eslint-disable-next-line no-undef
importScripts(new URL('comlink/dist/umd/comlink.min.js', import.meta.url));

class Request {
  _abortController = new AbortController();

  abort() {
    this._abortController.abort();

    // using a new controller for the next request
    this._abortController = new AbortController();
  }

  async json(url, { method, headers, body } = {}) {
    const signal = this._abortController.signal;

    return await fetch(url, { signal, method, headers, body })
      .then(response => {
        if (response.ok) {
          return response;
        }
        throw new Error(response.status);
      })
      .then(blob => blob.json())
      .catch(console.error);
  }
}

// eslint-disable-next-line no-undef
Comlink.expose(Request);
