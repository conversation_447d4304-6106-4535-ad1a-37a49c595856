import sortBy from 'lodash/sortBy';
import { call, fork, put, select, take } from 'redux-saga/effects';
import shortid from 'shortid';

import { message } from '@manyun/base-ui.ui.message';

import { YES_OR_NO_KEY_MAP } from '@manyun/dc-brain.legacy.constants';
import { redirectActionCreator } from '@manyun/dc-brain.legacy.redux/actions/routerActions';
import { taskCenterService } from '@manyun/dc-brain.legacy.services';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';

import {
  createConfigActionCreator,
  deleteConfigActionCreator,
  editConfigActionCreator,
  getConfigCheckItemsActionCreator,
  getConfigInfoActionCreator,
  getDataActionCreator,
  operationCheckConfigActions,
  resetSearchValuesActionCreator,
  setPaginationThenGetDataActionCreator,
} from '../actions/operationCheckConfigActions';
import { getSearchValuesAndPagination } from '../selectors/operationCheckConfigSelector';

function getQ(pagination, searchValues) {
  const baseQ = { ...pagination };
  for (const [, { name, value }] of Object.entries(searchValues)) {
    const val = value === '' ? null : value;
    if (name) {
      if (name === 'configName' && val) {
        baseQ[name] = val.trim();
      } else if (name === 'taskType' && val && val.length) {
        baseQ[name] = val[0];
        if (val[1]) {
          baseQ.taskSubType = val[1];
        }
      } else if (name === 'createByName' && val) {
        baseQ[name] = val.label;
      } else if (name === 'createTime' && val && val.length) {
        baseQ.createTimeStart = Number(val[0].unix() + '000');
        baseQ.createTimeEnd = Number(val[1].unix() + '000');
      } else {
        if (name !== 'taskType') {
          baseQ[name] = val;
        }
      }
    }
  }
  return baseQ;
}

function* getData(shouldResetPageNum = true) {
  yield put(operationCheckConfigActions.updateTableLoading(true));
  if (shouldResetPageNum) {
    yield put(operationCheckConfigActions.resetPageNum());
  }
  const { searchValues, pagination } = yield select(getSearchValuesAndPagination);
  const q = getQ(pagination, searchValues);
  const { response, error } = yield call(taskCenterService.fetchPowerCheckConfigData, q);
  if (error) {
    message.error(error);
    yield put(operationCheckConfigActions.failure());
  } else {
    yield put(operationCheckConfigActions.setDataAndTotal(response));
  }
}

function* resetSearchValues() {
  yield put(operationCheckConfigActions.resetSearchValuesAndPagination());
  yield put(getDataActionCreator(false));
}

function* setPagination(newPagination) {
  yield put(operationCheckConfigActions.setPagination(newPagination));
  yield put(getDataActionCreator(false));
}

function* deleteConfig(payload) {
  const { error } = yield call(taskCenterService.deletePowerCheckConfig, payload);
  if (error) {
    message.error(error);
  } else {
    message.success('删除成功！');
    yield put(getDataActionCreator(false));
  }
}

function* createConfig(payload) {
  const { error } = yield call(taskCenterService.fetchCreatePowerCheckConfig, payload);
  if (error) {
    message.error(error);
  } else {
    message.success('新建成功！');
    yield put(redirectActionCreator(urls.generateOperationCheckConfigListLocation()));
  }
}

function* editConfig(payload) {
  const { error } = yield call(taskCenterService.fetchEditPowerCheckConfig, payload);
  if (error) {
    message.error(error);
  } else {
    message.success('编辑成功！');
    yield put(redirectActionCreator(urls.generateOperationCheckConfigListLocation()));
  }
}

function splitItemsInBeforeAndAfter(data) {
  let itemsInBefore = [];
  let itemsInAfter = [];
  data.forEach(item => {
    if (item.itemType === 'CHECK_BEFORE') {
      const id = shortid();
      itemsInBefore = [
        ...itemsInBefore,
        {
          ...item,
          key: id,
          mergeRowsKey: id,
          inputData: item.inputData ? YES_OR_NO_KEY_MAP.YES : YES_OR_NO_KEY_MAP.NO,
          CHECK_BEFORE_$$_inputData: item.inputData ? YES_OR_NO_KEY_MAP.YES : YES_OR_NO_KEY_MAP.NO,
          CHECK_BEFORE_$$_itemMethod: item.itemMethod,
          CHECK_BEFORE_$$_itemName: item.itemName,
          CHECK_BEFORE_$$_itemNormal: item.itemNormal,
          CHECK_BEFORE_$$_dataRange: item.dataRange,
        },
      ];
    } else {
      const id = shortid();
      itemsInAfter = [
        ...itemsInAfter,
        {
          ...item,
          key: id,
          mergeRowsKey: id,
          inputData: item.inputData ? YES_OR_NO_KEY_MAP.YES : YES_OR_NO_KEY_MAP.NO,
          CHECK_AFTER_$$_inputData: item.inputData ? YES_OR_NO_KEY_MAP.YES : YES_OR_NO_KEY_MAP.NO,
          CHECK_AFTER_$$_itemMethod: item.itemMethod,
          CHECK_AFTER_$$_itemName: item.itemName,
          CHECK_AFTER_$$_itemNormal: item.itemNormal,
          CHECK_AFTER_$$_dataRange: item.dataRange,
        },
      ];
    }
  });
  itemsInBefore = sortBy(itemsInBefore, item => {
    return item.itemName;
  });

  itemsInAfter = sortBy(itemsInAfter, item => {
    return item.itemName;
  });
  return { itemsInBefore, itemsInAfter };
}

function* getConfigInfo(payload) {
  const { error, response } = yield call(taskCenterService.fetchPowerCheckConfigInfo, payload);
  if (error) {
    message.error(error);
  } else {
    const { itemsInBefore, itemsInAfter } = splitItemsInBeforeAndAfter(response.checkItems);
    let itemsInBeforeMerged = getMergeRowsKey(itemsInBefore);
    let itemsInAfterMerged = getMergeRowsKey(itemsInAfter);
    yield put(
      operationCheckConfigActions.setConfigInfos({
        ...response,
        itemsInBefore: itemsInBeforeMerged,
        itemsInAfter: itemsInAfterMerged,
      })
    );
    yield put(
      operationCheckConfigActions.upDateEditOptionValues({
        id: response.id,
        configName: {
          value: response.checkName,
          name: 'configName',
        },
        itemsInBefore: itemsInBeforeMerged,
        itemsInAfter: itemsInAfterMerged,
        taskType: {
          value: [response.taskType, response.taskSubType],
          name: 'taskType',
        },
      })
    );
  }
}

function getMergeRowsKey(arr) {
  let newData = [];
  newData = arr.map(item => {
    const sameNameData = arr.filter(({ itemName }) => itemName === item.itemName);
    if (sameNameData.length) {
      return {
        ...item,
        mergeRowsKey: sameNameData[0].id,
      };
    }
    return {
      ...item,
      mergeRowsKey: item.id,
    };
  });
  return newData;
}

function* getConfigCheckItems({ taskType, taskSubType, successCb }) {
  const { error, response } = yield call(taskCenterService.fetchPowerCheckConfigCheckItem, {
    taskType,
    taskSubType,
  });
  if (error) {
    message.error(error);
  } else {
    const { itemsInBefore, itemsInAfter } = splitItemsInBeforeAndAfter(response.data);
    let itemsInBeforeMerged = getMergeRowsKey(itemsInBefore);
    let itemsInAfterMerged = getMergeRowsKey(itemsInAfter);
    successCb();
    yield put(
      operationCheckConfigActions.setConfigInfos({
        itemsInBefore: itemsInBeforeMerged,
        itemsInAfter: itemsInAfterMerged,
      })
    );
  }
}

/******************************************************************************/
/******************************* WATCHERS *************************************/
/******************************************************************************/

function* watchGetDataAction() {
  while (true) {
    const { payload } = yield take(getDataActionCreator.type);
    yield fork(getData, payload);
  }
}

function* watchResetSearchValues() {
  while (true) {
    yield take(resetSearchValuesActionCreator.type);
    yield fork(resetSearchValues);
  }
}

function* watchSetPagination() {
  while (true) {
    const { payload } = yield take(setPaginationThenGetDataActionCreator.type);
    yield fork(setPagination, payload);
  }
}

function* watchDeleteConfig() {
  while (true) {
    const { payload } = yield take(deleteConfigActionCreator.type);
    yield fork(deleteConfig, payload);
  }
}

function* watchCreateConfig() {
  while (true) {
    const { payload } = yield take(createConfigActionCreator.type);
    yield fork(createConfig, payload);
  }
}

function* watchEditConfig() {
  while (true) {
    const { payload } = yield take(editConfigActionCreator.type);
    yield fork(editConfig, payload);
  }
}

function* watchGetConfigInfo() {
  while (true) {
    const { payload } = yield take(getConfigInfoActionCreator.type);
    yield fork(getConfigInfo, payload);
  }
}

function* watchGetConfigCheckItems() {
  while (true) {
    const { payload } = yield take(getConfigCheckItemsActionCreator.type);
    yield fork(getConfigCheckItems, payload);
  }
}

export default [
  fork(watchGetDataAction),
  fork(watchResetSearchValues),
  fork(watchSetPagination),
  fork(watchDeleteConfig),
  fork(watchCreateConfig),
  fork(watchEditConfig),
  fork(watchGetConfigInfo),
  fork(watchGetConfigCheckItems),
];
