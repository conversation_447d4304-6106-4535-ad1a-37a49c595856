import trim from 'lodash/trim';
import { call, fork, put, select, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { fetchModels } from '@manyun/crm.service.fetch-models';

import * as modelService from '@manyun/dc-brain.legacy.services/modelService';
import * as treeDataService from '@manyun/dc-brain.legacy.services/treeDataService';

import {
  deleteModelAction,
  fetchModelDetail,
  fetchModelListPage,
  modelActions,
  setModelPagination,
} from '../actions/modelActions';
import { getModelSearchValuesAndPagination } from '../selectors/modelSelector';

function getQ(pagination, searchValues) {
  const baseQ = { ...pagination };
  for (const [, { name, value }] of Object.entries(searchValues)) {
    if ([null, undefined, ''].includes(value) || (Array.isArray(value) && value.length === 0)) {
      continue;
    }
    if (name === 'operatorId') {
      baseQ[name] = value.key;
    } else if (name === 'createTime') {
      baseQ['createTimeRange'] = value;
    } else {
      baseQ[name] = Array.isArray(value) ? value : trim(value);
    }
  }
  return baseQ;
}

function* fetchModelList() {
  yield put(modelActions.request());
  const { searchValues, pagination } = yield select(getModelSearchValuesAndPagination);
  const { data, error } = yield call(fetchModels, getQ(pagination, searchValues));
  const { normalizedList } = yield call(treeDataService.fetchDeviceCategory, {
    neednotSpace: true,
    numbered: null,
  });
  if (error) {
    message.error(error.message || '获取列表请求失败');
    yield put(modelActions.failure());
  } else {
    yield put(modelActions.fetchModelListSuccess(data));
    yield put(modelActions.setDeviceNormalizedList(normalizedList));
  }
}

function* setModePagelPagination(newPagination) {
  yield put(modelActions.setModelPagination(newPagination));
  yield put(fetchModelListPage());
}

function* getModelDetail({ params }) {
  yield put(modelActions.request());
  const { response, error } = yield call(modelService.getModelDetail, params);
  if (response) {
    yield put(modelActions.saveModelDetailSuccess(response));
  } else {
    message.error(error || '获取详情请求失败');
    yield put(modelActions.failure());
  }
}

function* deleteModel({ params, successCb, errorCb }) {
  yield put(modelActions.request());
  const { response, error } = yield call(modelService.deleteModel, params);
  if (response) {
    successCb();
    message.success('删除型号成功');
  } else {
    errorCb();
    message.error(error);
    yield put(modelActions.failure());
  }
}

export function* watchFetchModelList() {
  while (true) {
    const { payload } = yield take(fetchModelListPage.type);
    yield fork(fetchModelList, payload);
  }
}

export function* watchGetModelDetail() {
  while (true) {
    const { payload } = yield take(fetchModelDetail.type);
    yield fork(getModelDetail, payload);
  }
}

export function* watchDeleteModel() {
  while (true) {
    const { payload } = yield take(deleteModelAction.type);
    yield fork(deleteModel, payload);
  }
}

function* watchSetModelPagination() {
  while (true) {
    const { payload } = yield take(setModelPagination.type);
    yield fork(setModePagelPagination, payload);
  }
}

export default [
  fork(watchFetchModelList),
  fork(watchGetModelDetail),
  fork(watchDeleteModel),
  fork(watchSetModelPagination),
];
