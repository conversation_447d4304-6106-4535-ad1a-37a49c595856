import { push, replace } from 'connected-react-router';
import { fork, put, take } from 'redux-saga/effects';

import { LOGIN_ROUTH_PATH } from '@manyun/auth-hub.route.auth-routes';
import { getMyPermissionsAction } from '@manyun/auth-hub.state.user';

import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';

import { redirectActionCreator } from './../actions/routerActions';

/**
 * Push/Replace location
 * @param {{ location: import('@types/history').Location, func: 'push'|'replace' }} param
 */
function* redirect({ location, func = 'push' }) {
  if (func === 'push') {
    yield put(push(location));
  } else if (func === 'replace') {
    yield put(replace(location));
  } else {
    throw new Error(`func(${func}) not supported!`);
  }
}

function* watchRedirect() {
  while (true) {
    const { payload } = yield take(redirectActionCreator.type);
    yield fork(redirect, payload);
  }
}

function* watchLocationChange() {
  while (true) {
    const { payload } = yield take('@@router/LOCATION_CHANGE');
    const { pathname } = payload.location;
    const excludeRoutePaths = [LOGIN_ROUTH_PATH];
    if (!excludeRoutePaths.includes(pathname)) {
      yield put(getMyPermissionsAction());
      yield put(syncCommonDataActionCreator({ strategy: { currentUser: 'FORCED' } }));
    }
  }
}

export default [fork(watchRedirect), fork(watchLocationChange)];
