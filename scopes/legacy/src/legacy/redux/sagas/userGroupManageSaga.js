import { call, fork, put, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { fetchPagedUserGroups } from '@manyun/auth-hub.service.fetch-paged-user-groups';

import { userGroupManageService } from '@manyun/dc-brain.legacy.services';

import {
  fetchCreateUserGroup,
  fetchDelete,
  fetchRemoveResource,
  fetchRemoveRole,
  fetchRemoveUser,
  fetchUserGroupList,
  fetchUserGroupManageDetail,
  fetchUserGroupsAssociatedResource,
  fetchUserGroupsAssociatedRoles,
  fetchUserGroupsAssociatedUser,
  userGroupManageActions,
} from '../actions/userGroupManageActions';
import { syncCommonDataActionCreator } from './../actions/commonActions';

function* watchFetchUserGroupList() {
  while (true) {
    const { payload } = yield take(fetchUserGroupList.type);
    const { pageNo, pageSize, searchName, searchType } = payload;
    yield fork(fetchUserGroup, pageNo, pageSize, searchName, searchType);
  }
}

function* fetchUserGroup(pageNo, pageSize, searchName, searchType) {
  yield put(userGroupManageActions.request());
  const { data, error } = yield call(fetchPagedUserGroups, {
    pageNum: pageNo,
    pageSize,
    searchType,
    searchName,
  });
  if (error) {
    message.error(error.message || '用户组列表请求失败');
    yield put(userGroupManageActions.failure());
  } else {
    yield put(
      userGroupManageActions.success({
        list: data.data,
        total: data.total,
        loading: false,
        pageNo,
        pageSize,
        searchName,
        searchType,
      })
    );
  }
}

function* watchFetchCreateUserGroup() {
  while (true) {
    const { payload } = yield take(fetchCreateUserGroup.type);
    const { groupName, groupCode, remarks, pageNo, pageSize, searchName, searchType, groupId } =
      payload;
    yield fork(createUserGroup, {
      groupName,
      groupCode,
      remarks,
      pageNo,
      pageSize,
      searchName,
      searchType,
      groupId,
    });
  }
}

function* createUserGroup({
  groupName,
  groupCode,
  remarks,
  pageNo,
  pageSize,
  searchName,
  searchType,
  groupId,
}) {
  yield put(userGroupManageActions.createUserGroupLoading());
  if (!groupId) {
    const { response, error } = yield call(userGroupManageService.fetchCreateUserGroup, {
      groupName,
      groupCode,
      remarks,
    });
    if (response) {
      yield put(userGroupManageActions.createUserGroupLoading());
      yield put(userGroupManageActions.createVisible());
      message.success('创建用户组成功');
      yield fork(fetchUserGroup, pageNo, pageSize, searchName, searchType);
    } else {
      message.error(error || '创建用户组失败');
      yield put(userGroupManageActions.failure());
    }
  } else {
    const { response, error } = yield call(userGroupManageService.fetchUpdateUserGroup, {
      groupName,
      groupCode: groupCode,
      remarks,
      groupId,
    });
    if (response) {
      yield put(userGroupManageActions.createUserGroupLoading());
      yield put(userGroupManageActions.createVisible());
      message.success('编辑用户组信息成功');
      yield fork(userGroupManageDetail, groupId);
    } else {
      message.error(error || '创建用户组失败');
      yield put(userGroupManageActions.failure());
    }
  }
}

// 删除用户组
function* deleteUser(groupId, pageSize, searchType, searchName) {
  yield put(userGroupManageActions.request());
  const { response, error } = yield call(userGroupManageService.fetchDelete, groupId);
  if (response) {
    message.success('删除用户组成功');
    yield fork(fetchUserGroup, 1, pageSize, searchName, searchType);
  } else {
    message.error(error || '删除用户组失败！');
    yield put(userGroupManageActions.failure());
  }
}

function* watchFetchDeleteUserGroup() {
  while (true) {
    const { payload } = yield take(fetchDelete.type);
    const { groupId, searchType, pageSize, searchName } = payload;
    yield fork(deleteUser, groupId, pageSize, searchType, searchName);
  }
}

// 用户组关联的用户
function* userGroupsAssociatedUser(groupId, pageNo, pageSize, searchType, searchName) {
  yield put(userGroupManageActions.userListRequest());
  const { response, error } = yield call(
    userGroupManageService.fetchUserGroupsAssociatedUser,
    groupId,
    pageNo,
    pageSize,
    searchType,
    searchName
  );
  if (response) {
    yield put(
      userGroupManageActions.userListSuccess({
        list: response.data,
        total: response.total,
        pageNo,
        pageSize,
        searchType,
        searchName,
        loading: false,
      })
    );
  } else {
    message.error(error || '用户列表获取失败！');
    yield put(userGroupManageActions.failure());
  }
}

function* watchFetchUserGroupsAssociatedUser() {
  while (true) {
    const { payload } = yield take(fetchUserGroupsAssociatedUser.type);
    const { groupId, searchType, pageSize, searchName, pageNo } = payload;
    yield fork(userGroupsAssociatedUser, groupId, pageNo, pageSize, searchType, searchName);
  }
}

function* userGroupManageDetail(groupId) {
  const { response, error } = yield call(userGroupManageService.fetchUserGroupDetail, groupId);
  if (response) {
    yield put(userGroupManageActions.userGroupDetail(response));
  } else {
    message.error(error || '请求用户组详细信息失败！');
    yield put(userGroupManageActions.failure());
  }
}

function* watchFetchUserGroupManageDetail() {
  while (true) {
    const { payload } = yield take(fetchUserGroupManageDetail.type);
    const { groupId } = payload;
    yield fork(userGroupManageDetail, groupId);
  }
}

// 用户组关联的角色
function* userGroupsAssociatedRoles(groupId, pageNo, pageSize, searchType, searchName) {
  yield put(userGroupManageActions.roleListRequest());
  const { response, error } = yield call(
    userGroupManageService.fetchUserGroupsAssociatedRoles,
    groupId,
    pageNo,
    pageSize,
    searchType,
    searchName
  );
  if (response) {
    yield put(
      userGroupManageActions.roleListSuccess({
        list: response.data,
        total: response.total,
        pageNo,
        pageSize,
        searchType,
        searchName,
        loading: false,
      })
    );
  } else {
    message.error(error || '用户列表获取失败！');
    yield put(userGroupManageActions.failure());
  }
}

function* watchFetchUserGroupsAssociatedRoles() {
  while (true) {
    const { payload } = yield take(fetchUserGroupsAssociatedRoles.type);
    const { groupId, searchType, pageSize, searchName, pageNo } = payload;
    yield fork(userGroupsAssociatedRoles, groupId, pageNo, pageSize, searchType, searchName);
  }
}

// 移除用户
function* removeUser(userId, groupId, pageSize, searchType, searchName) {
  yield put(userGroupManageActions.userListRequest());
  const { response, error } = yield call(userGroupManageService.fetchRemoveUser, userId, groupId);
  if (response) {
    message.success('移除用户成功！');
    yield fork(userGroupsAssociatedUser, groupId, 1, pageSize, searchType, searchName);
  } else {
    message.error(error || '移除用户失败！');
    yield put(userGroupManageActions.failure());
  }
}

function* watchFetchRemoveUser() {
  while (true) {
    const { payload } = yield take(fetchRemoveUser.type);
    const { userId, groupId, searchType, pageSize, searchName } = payload;
    yield fork(removeUser, userId, groupId, pageSize, searchType, searchName);
  }
}

// 移除角色
function* removeRole(roleId, groupId, pageSize, searchType, searchName) {
  yield put(userGroupManageActions.roleListRequest());
  const { response, error } = yield call(userGroupManageService.fetchRemoveRole, roleId, groupId);
  if (response) {
    message.success('移除角色成功！');
    yield fork(userGroupsAssociatedRoles, groupId, 1, pageSize, searchType, searchName);
  } else {
    message.error(error || '移除角色失败！');
    yield put(userGroupManageActions.failure());
  }
}

function* watchFetchRemoveRole() {
  while (true) {
    const { payload } = yield take(fetchRemoveRole.type);
    const { roleId, groupId, searchType, pageSize, searchName } = payload;
    yield fork(removeRole, roleId, groupId, pageSize, searchType, searchName);
  }
}

// 用户组关联的资源
function* userGroupsAssociatedResource(groupId, pageNo, pageSize, searchType, searchName) {
  yield put(userGroupManageActions.resourceListRequest());
  const { response, error } = yield call(
    userGroupManageService.fetchUserGroupsAssociatedResource,
    groupId,
    pageNo,
    pageSize,
    searchType,
    searchName
  );
  if (response) {
    yield put(
      userGroupManageActions.resourceListSuccess({
        list: response.data,
        total: response.total,
        pageNo,
        pageSize,
        searchType,
        searchName,
        loading: false,
      })
    );
  } else {
    message.error(error || '资源列表获取失败！');
    yield put(userGroupManageActions.failure());
  }
}

function* watchFetchUserGroupsAssociatedResource() {
  while (true) {
    const { payload } = yield take(fetchUserGroupsAssociatedResource.type);
    const { groupId, searchType, pageSize, searchName, pageNo } = payload;
    yield fork(userGroupsAssociatedResource, groupId, pageNo, pageSize, searchType, searchName);
  }
}

// 移除资源
function* removeResource(resourceId, groupId, pageNo, pageSize, searchType, searchName) {
  yield put(userGroupManageActions.resourceListRequest());
  const { response, error } = yield call(
    userGroupManageService.fetchRemoveResource,
    resourceId,
    groupId
  );
  if (response) {
    yield fork(userGroupsAssociatedResource, groupId, pageNo, pageSize, searchType, searchName);
    yield put(syncCommonDataActionCreator({ strategy: { currentUser: 'FORCED' } }));
  } else {
    message.error(error || '移除资源失败！');
    yield put(userGroupManageActions.failure());
  }
}

function* watchFetchRemoveResource() {
  while (true) {
    const { payload } = yield take(fetchRemoveResource.type);
    const { resourceId, groupId, searchType, pageSize, searchName, pageNo } = payload;
    yield fork(removeResource, resourceId, groupId, pageNo, pageSize, searchType, searchName);
  }
}

export default [
  fork(watchFetchUserGroupList),
  fork(watchFetchCreateUserGroup),
  fork(watchFetchDeleteUserGroup),
  fork(watchFetchUserGroupManageDetail),
  fork(watchFetchUserGroupsAssociatedUser),
  fork(watchFetchUserGroupsAssociatedRoles),
  fork(watchFetchRemoveUser),
  fork(watchFetchRemoveRole),
  fork(watchFetchUserGroupsAssociatedResource),
  fork(watchFetchRemoveResource),
];
