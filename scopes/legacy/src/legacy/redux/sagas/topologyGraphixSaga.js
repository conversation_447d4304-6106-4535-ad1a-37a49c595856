import { replace } from 'connected-react-router';
import cloneDeep from 'lodash/cloneDeep';
import uniq from 'lodash/uniq';
import { normalize } from 'normalizr';
import { call, fork, put, select, takeLatest } from 'redux-saga/effects';

import { getContainerRect, traverse } from '@manyun/dc-brain.aura-graphix';
import { syncCommonDataAction } from '@manyun/dc-brain.state.common';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { TopologyTypesMapper } from '@manyun/monitoring.page.topology-graphix';
import {
  filterTopologyByBoundaryType,
  relatedElementUtil,
} from '@manyun/monitoring.state.topology';
import { Colors as ThemeColors } from '@manyun/monitoring.theme.topology-theme';
import { fetchDevices } from '@manyun/resource-hub.service.fetch-devices';
import { getSpaceGuid } from '@manyun/resource-hub.util.space-guid';

import filterDeviceGuids from '@manyun/dc-brain.legacy.pages/topology-graphix/utils/filter-device-guids';
import { getClosestSpaceGuid } from '@manyun/dc-brain.legacy.pages/topology-graphix/utils/get-closest-space-guid';
import upgradeV1ToV2 from '@manyun/dc-brain.legacy.pages/topology-graphix/utils/upgrade-v1-to-v2';
import upgradeV1ToV21 from '@manyun/dc-brain.legacy.pages/topology-graphix/utils/upgrade-v2-to-v2-1';
import { deviceSliceActions } from '@manyun/dc-brain.legacy.redux/__next/device';
import { deviceService, topologyService } from '@manyun/dc-brain.legacy.services';
import { devicesByTypeSchema } from '@manyun/dc-brain.legacy.services/schemas/deviceSchemas';
import * as urlsUtil from '@manyun/dc-brain.legacy.utils/urls';

import {
  getFuelSystemGraphAction,
  getHVACGraphActionCreator as getHVACGraphAction,
  initializeActionCreator,
  topologyGraphixActions,
} from '../actions/topologyGraphixActions';
import { getSpecificBlockGraphCache } from './../selectors/topologyGraphixSelectors';

// commons

function fixProperties(snapshot) {
  traverse(snapshot.pages[0].children, node => {
    if (node.custom?.type === 'bus-way' && !node.allowAddAnchorPoints) {
      node.allowAddAnchorPoints = true;
    }
  });

  return snapshot;
}

// workers

function* initialize({ payload: { idc, block, mode, topologyType } }) {
  const { code, id, graph } = yield call(getGraph, { idc, block, topologyType });

  if (code === 'error') {
    yield put(topologyGraphixActions.setIsReady(true));
    return;
  }
  if (mode === 'new' && code === 'success') {
    yield put(
      replace(urlsUtil.generateTopologyGraphixUrl({ idc, block, mode: 'edit', topologyType }))
    );
  }
  yield put(topologyGraphixActions.setGraph({ id, graph }));
  // 查询当前楼下所有可上拓扑的设备
  yield call(getDevicesTree, { idc, block, topologyType });
}

function* getGraph({ idc, block, topologyType, tenantId }) {
  const blockGuid = getSpaceGuid(idc, block);

  const cache = yield select(getSpecificBlockGraphCache, blockGuid, topologyType);

  const now = Date.now();
  if (cache && now - (cache.updatedAt || now) <= 60 * 1000) {
    return { type: 'cache', code: 'success', id: cache.id, graph: cache.graph };
  }

  const { response, error } = yield call(topologyService.fetchTopology, {
    blockGuid,
    topologyType,

    // iframe 嵌入到 easyv 里时需要传递租户 ID
    tenantId,
  });
  if (error) {
    return { code: 'error', id: null, graph: null };
  }
  if (!response) {
    return { code: 'no-graph', id: null, graph: null };
  }
  const config = yield select(selectCurrentConfig);
  const configUtil = new ConfigUtil(config);
  const { id, viewJson } = response;

  try {
    const data = JSON.parse(viewJson);
    let graph = data;
    if (
      (data.version === undefined || data.version === 1) &&
      topologyType === TopologyTypesMapper.ELECTRIC_POWER
    ) {
      graph = upgradeV1ToV2(data, configUtil);
    } else if (data.version === 2) {
      graph = upgradeV1ToV21(data.snapshot);
    } else if (data.version === 2.1) {
      graph = fixProperties(data.snapshot);
    }
    yield put(topologyGraphixActions.updateBlockGraphCache({ blockGuid, topologyType, id, graph }));
    return { code: 'success', id, graph };
  } catch (error) {
    return { code: 'parsing-error', id: null, graph: null };
  }
}

/**
 * To generate `Group, Image, Text` element size based on a predefined element size.
 *
 * @param {{ width: number; height: number }} predefinedElementSize
 * @param {{ textFontSize?: number; textPosition?: 'left'|'bottom' }} options
 */
function generateAppropriateElementSize(
  { width, height },
  { textFontSize = 25, textPosition = 'left' } = {}
) {
  const textAttrs = {
    x: 0,
    y: 0,
    width: 140,
    height,
    align: 'center',
    fontSize: textFontSize,
  };
  const imageAttrs = {
    // Put the `Text` element on the right side.
    x: textAttrs.width,
    y: 0,
    width,
    height,
  };
  const groupAttrs = {
    width: width + textAttrs.width,
    height,
  };
  if (textPosition === 'bottom') {
    const shouldUseImageWidth = textAttrs.width < imageAttrs.width;
    const margin = 4;

    textAttrs.y = imageAttrs.height + margin;
    if (shouldUseImageWidth) {
      textAttrs.width = imageAttrs.width;
      textAttrs.height = textAttrs.fontSize;
      imageAttrs.x = 0;
      groupAttrs.width = imageAttrs.width;
    } else {
      textAttrs.width = 160;
      textAttrs.height = textAttrs.fontSize * 2;
      imageAttrs.x =
        imageAttrs.width > textAttrs.width
          ? 0
          : Math.ceil((textAttrs.width - imageAttrs.width) / 2);
      groupAttrs.width = Math.max(imageAttrs.width, textAttrs.width);
    }

    groupAttrs.height = imageAttrs.height + margin + textAttrs.height;
  }

  return { groupAttrs, imageAttrs, textAttrs };
}

function* getDevicesTree({ idc, block, topologyType }) {
  const actualTopologyType = topologyType === 'LIQUID_ELECTRIC' ? 'ELECTRIC_POWER' : topologyType;
  const blockGuid = getSpaceGuid(idc, block);
  const config = yield select(selectCurrentConfig);
  const configUtil = new ConfigUtil(config, { defaultSpaceGuid: blockGuid });
  const typeofChiller = configUtil.typeofDeviceGen(ConfigUtil.constants.deviceTypes.CHILLER);
  const visibleDeviceTypes = configUtil.getTopologyVisibleDeviceTypes(actualTopologyType);

  if (!visibleDeviceTypes.length) {
    return;
  }

  const { error, data } = yield call(fetchDevices, {
    idc: idc,
    block: block,
    deviceTypes: visibleDeviceTypes,
  });

  if (error) {
    return;
  }

  const backendDevices = data.data.map(device => device.toApiObject());
  const { entities, result } = normalize(backendDevices, devicesByTypeSchema);
  const devicesTree = [];
  uniq(result).forEach(deviceType => {
    const elementConfig = configUtil.getTopologyElementConfig(deviceType, actualTopologyType);
    const [width, height] = elementConfig?.size || [100, 100];

    const isHVAC = actualTopologyType === TopologyTypesMapper.HVAC;
    const { groupAttrs, imageAttrs, textAttrs } = generateAppropriateElementSize(
      { width, height },
      {
        textPosition: typeofChiller(deviceType) || isHVAC ? 'bottom' : 'left',
      }
    );

    const devices = entities.data[deviceType];
    const { secondCategory } = devices[0];
    const deviceTypeLevel2TreeNodeIdx = devicesTree.findIndex(({ key }) => key === secondCategory);
    const deviceTypeTreeNode = {
      key: deviceType,
      type: deviceType,
      label: deviceType,
      children: devices.map(({ guid, name, spaceGuid, extendPosition }) => {
        const remarkTextElementId = relatedElementUtil.generate(guid);
        const [, remarkTextRowKey] = relatedElementUtil.split(remarkTextElementId);
        const closestSpaceGuid = getClosestSpaceGuid(spaceGuid);

        return {
          key: guid,

          // can dropped onto canvas only once
          copyMax: 1,

          type: deviceType + '-device',
          label: [name, spaceGuid.roomTag].filter(Boolean),
          img: elementConfig?.thumbnail,
          elementConfig: {
            type: 'group',
            ...groupAttrs,
            children: [
              {
                id: guid,
                type: 'group',
                name,
                canUngroup: false,
                ...imageAttrs,
                anchorPointsConfig: elementConfig?.anchorPointsConfig,
                anchorPointsPlacementConfig: elementConfig?.anchorPointsPlacementConfig,
                custom: {
                  type: 'device',
                  deviceGuid: guid,
                  deviceType,
                  spaceGuid: closestSpaceGuid,
                  extendPosition,
                  name,
                  remarkTextRowKeys: [remarkTextRowKey],
                  visiblePointCodes: [],
                },
                locked: true,
                children: [
                  {
                    type: 'image',
                    src: elementConfig?.thumbnail,
                    id: guid + '_$$_device',
                    x: 0,
                    y: 0,
                    width,
                    height,
                    locked: true,
                    custom: {
                      type: 'device/image',
                    },
                  },
                ],
              },
              {
                custom: { type: 'remark-text' },
                id: remarkTextElementId,
                type: 'text',
                text: name,
                ...textAttrs,
                align: 'center',
                verticalAlign: 'middle',
                stroke: 'white',
                locked: true,
              },
            ],
          },
          isLeaf: true,
        };
      }),
    };
    if (deviceTypeLevel2TreeNodeIdx > -1) {
      devicesTree[deviceTypeLevel2TreeNodeIdx].children.push(deviceTypeTreeNode);
    } else {
      devicesTree.push({
        key: secondCategory,
        type: secondCategory,
        label: secondCategory,
        children: [deviceTypeTreeNode],
      });
    }
  });

  yield put(deviceSliceActions.cacheInsert(backendDevices));
  yield put(topologyGraphixActions.setDevicesTree(devicesTree));
  yield put(topologyGraphixActions.setIsReady(true));
}

function* getGraphDevices({ idc, block, graph }) {
  const deviceGuids = filterDeviceGuids(graph);
  if (!deviceGuids.length) {
    return null;
  }

  const { response, error } = yield call(deviceService.fetchDevicesByTypes, {
    idcTag: idc,
    blockTag: block,
    deviceGuidList: deviceGuids,
  });
  if (error) {
    return null;
  }

  return response.data;
}

/**
 * 查询一些额外的设备
 * 比如：柴发视图中的高压进线柜
 */
function* getExtraDevices({ idc, block, deviceTypes }) {
  if ((deviceTypes?.length || 0) <= 0) {
    return null;
  }

  const { response, error } = yield call(deviceService.fetchDevicesByTypes, {
    idcTag: idc,
    blockTag: block,
    deviceTypes,
  });
  if (error) {
    return null;
  }

  return response.data;
}

function* getDeviceTypesByTopologyData(graph, topologyType) {
  const config = yield select(selectCurrentConfig);
  const configUtil = new ConfigUtil(config);
  const psychrometerTypes =
    configUtil.getAllDeviceTypes(ConfigUtil.constants.deviceTypes.PSYCHROMETER) || [];
  const coolingUnitTypes =
    configUtil.getAllDeviceTypes(ConfigUtil.constants.deviceTypes.COOLING_SYSTEM_UNIT) || [];

  const deviceTypes = graph.pages[0].children.reduce(
    (types, { custom }) => {
      if (custom?.type !== 'device' || types.includes(custom?.deviceType)) {
        return types;
      }
      types.push(custom.deviceType);

      return types;
    },
    [configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_BLOCK)]
  );

  if (topologyType === TopologyTypesMapper.HVAC) {
    deviceTypes.push(...[...psychrometerTypes, ...coolingUnitTypes]);
  }

  return deviceTypes;
}

function* syncBlockDevicesPointDefinitions({ graph, topologyType }) {
  const deviceTypes = yield getDeviceTypesByTopologyData(graph, topologyType);
  if (!deviceTypes?.length) {
    return;
  }
  yield put(
    syncCommonDataAction({
      strategy: {
        deviceTypesPointsDefinition: deviceTypes,
      },
    })
  );
}

function* setPreviewGraph({
  idc,
  block,
  extraDeviceTypes,
  topologyType,
  tenantId,
  topologyBoundary,
  setLoadingAction,
  setResultAction,
  filterFunc = graph => graph,
  getActiveGraph = graph => graph,
}) {
  yield put(setLoadingAction(true));

  const { code, graph } = yield getGraph({ idc, block, topologyType, tenantId });

  // 为了方便在 Redux logs / devtools 中查看当前拓扑数据的相关信息
  // 以下字段不一定会在 action handler 中用到
  const basePayload = {
    idc,
    block,
    topologyType,
    topologyBoundary,
  };

  if (code === 'error') {
    yield put(
      setResultAction({
        ...basePayload,
        code: 'error',
        graph: null,
        devices: null,
        extraDevices: null,
      })
    );
    return;
  }

  if (code === 'no-graph') {
    yield put(
      setResultAction({
        ...basePayload,
        code: 'no-graph',
        graph: null,
        devices: null,
        extraDevices: null,
      })
    );
    return;
  }

  if (code === 'success') {
    const filteredGraph = yield filterFunc(graph);
    if (!filteredGraph) {
      yield put(
        setResultAction({
          ...basePayload,
          code: 'empty-graph',
          graph: null,
          devices: null,
          extraDevices: null,
        })
      );
      return;
    }
    yield syncBlockDevicesPointDefinitions({ graph: getActiveGraph(filteredGraph), topologyType });
    const devices = yield getGraphDevices({ idc, block, graph: getActiveGraph(filteredGraph) });
    const extraDevices = yield getExtraDevices({ idc, block, deviceTypes: extraDeviceTypes });
    yield put(
      setResultAction({
        ...basePayload,
        code: 'success',
        graph: filteredGraph,
        devices,
        extraDevices,
      })
    );
    return;
  }

  console.error(`code(${code}) not implemented.`);
}

const electricGraphTraverseVistor = node => {
  if (node.type === 'polyline' || node.type === 'line') {
    node.stroke = ThemeColors.LineColor;
  } else if (node.type === 'text') {
    if (node.custom?.type === 'point-text') {
      node.fill = ThemeColors.AnchorTextColor;
      node.stroke = ThemeColors.AnchorTextColor;
    } else {
      node.fill = ThemeColors.TextColor;
      node.stroke = ThemeColors.TextColor;
    }
  } else if (node.custom?.type === 'bus-way') {
    node.children[0].fill = ThemeColors.LineColor;
  }
};
function updatePreviewAttrs(
  graph,
  elements,
  { traverseVisitor = electricGraphTraverseVistor } = {}
) {
  // 根据过滤后的元素重新生成容器 Rect
  // 以便 AuraGraphix 加载数据后计算出最合适的适应画布的缩放比例
  const { x, y, width, height } = getContainerRect(elements);

  const copy = cloneDeep({
    ...graph,
    pages: [
      {
        ...graph.pages[0],
        background: 'transparent',
        padding: 16,
        x,
        y,
        width,
        height,
        children: elements,
      },
    ],
  });

  traverse(copy.pages[0].children, node => {
    if (node.type === 'polyline' || node.type === 'line') {
      node.showArrows = false;
      node.listening = false;
    } else if (node.type === 'text') {
      if (node.custom?.type === 'point-text') {
        // eslint-disable-next-line no-template-curly-in-string
        node.text = node.text.replace('${value}', '--');
      }
      node.listening = false;
    } else if (node.custom?.type === 'pipe-network' || node.custom?.type === 'bus-way') {
      node.listening = false;
    }
    traverseVisitor(node);
  });

  return copy;
}

function filterHVACGraph(graph) {
  const elements = filterTopologyByBoundaryType(graph.pages[0].children);

  return updatePreviewAttrs(graph, elements, {
    traverseVisitor: node => {
      if (node.type === 'text') {
        if (node.custom?.type === 'point-text') {
          node.fill = ThemeColors.AnchorTextColor;
          node.stroke = ThemeColors.AnchorTextColor;
        } else {
          node.fill = ThemeColors.TextColor;
          node.stroke = ThemeColors.TextColor;
        }
      }
    },
  });
}

function* getHVACGraph({ payload: { idc, block, tenantId } }) {
  const config = yield select(selectCurrentConfig);
  const configUtil = new ConfigUtil(config);

  const psychrometerTypes =
    configUtil.getAllDeviceTypes(ConfigUtil.constants.deviceTypes.PSYCHROMETER) || [];
  const coolingUnitTypes =
    configUtil.getAllDeviceTypes(ConfigUtil.constants.deviceTypes.COOLING_SYSTEM_UNIT) || [];

  yield setPreviewGraph({
    idc,
    block,
    extraDeviceTypes: [...psychrometerTypes, ...coolingUnitTypes],
    topologyType: TopologyTypesMapper.HVAC,
    tenantId,
    setLoadingAction: topologyGraphixActions.setHVACGraphPreviewLoading,
    setResultAction: topologyGraphixActions.setHVACGraphPreviewResult,
    filterFunc: filterHVACGraph,
  });
}

function filterFuelSystemGraph(graph) {
  const elements = filterTopologyByBoundaryType(graph.pages[0].children);

  return updatePreviewAttrs(graph, elements, {
    traverseVisitor: node => {
      if (node.type === 'text') {
        if (node.custom?.type === 'point-text') {
          node.fill = ThemeColors.AnchorTextColor;
          node.stroke = ThemeColors.AnchorTextColor;
        } else {
          node.fill = ThemeColors.TextColor;
          node.stroke = ThemeColors.TextColor;
        }
      }
      node.locked = true;
    },
  });
}

function* getFuelSystemGraphSaga({ payload: { idc, block, tenantId } }) {
  yield setPreviewGraph({
    idc,
    block,
    extraDeviceTypes: [],
    topologyType: TopologyTypesMapper.FUEL_SYSTEM,
    tenantId,
    setLoadingAction: topologyGraphixActions.setFuelSystemGraphPreviewLoading,
    setResultAction: topologyGraphixActions.setFuelSystemGraphPreviewResult,
    filterFunc: filterFuelSystemGraph,
  });
}

// watchers

function* watchInitialize() {
  yield takeLatest(initializeActionCreator.type, initialize);
}

function* watchGetHVACGraph() {
  yield takeLatest(getHVACGraphAction.type, getHVACGraph);
}

function* watchGetFuelSystemGraph() {
  yield takeLatest(getFuelSystemGraphAction.type, getFuelSystemGraphSaga);
}

export default [fork(watchInitialize), fork(watchGetHVACGraph), fork(watchGetFuelSystemGraph)];
