import { push } from 'connected-react-router';
import uniqBy from 'lodash/uniqBy';
import { call, fork, put, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { templateGroupViewService } from '@manyun/dc-brain.legacy.services';
import { generateTemplteGroupListUrl } from '@manyun/dc-brain.legacy.utils/urls';

import {
  cancleConnectArea,
  changeSomeSwitchAvailableAction,
  changeSwitchAvailableAction,
  connectRegionToTemplate,
  connectTemplateInArea,
  connectTemplateInAreaSearchTemplate,
  deleteTemplateInAreaAction,
  electricTableDateAction,
  fetchAllTemplateGroupAction,
  fetchConnectTemplatesAction,
  fetchCreateTemplateGroupAction,
  fetchTemplateGroupAreaAction,
  fetchTemplateMessByMetaCode,
  fetchTemplateTreeAction,
  fetchTemplateTreeChildrenAction,
  filterElectricAction,
  getAreaConfigById,
  getTemplateByTemplateGroupId,
  handeleSearchAreaAndIdcAction,
  saveCheckedKeysAction,
  saveCreateTemplateInputValueAction,
  saveCreateTemplateRadioValueAction,
  saveSelectTemplateGroupAction,
  searchAreaConfigAction,
  searchAreaIdcAction,
  searchTemplateGroupPagesActions,
  searchTemplteFromNameAction,
  selectElectricTableDateAction,
  selectKeysAction,
  templateGroupViewActions,
  updateTemplateGroupConfig,
} from '../actions/templateGroupViewActions';

/***************************** Subroutines ************************************/

function* templateGroupViewPage(payload) {
  yield put(templateGroupViewActions.request());
  const { response, error } = yield call(
    templateGroupViewService.fetchTemplateGroupViewPage,
    payload
  );
  yield put(templateGroupViewActions.searchTemplateGroupPagesCondition(payload));

  if (response) {
    yield put(templateGroupViewActions.searchTemplateGroupPagesSuccess(response));
  } else {
    message.error(error || '模板组列表请求失败');
    yield put(templateGroupViewActions.failure());
  }
}

function* changeSwitchAvailable(payload) {
  yield put(templateGroupViewActions.request());
  const { response, error } = yield call(
    templateGroupViewService.fetchchangeSwitchAvailable,
    payload
  );

  yield fork(templateGroupViewPage, payload);
  if (response) {
  } else {
    message.error(error || '修改启用状态请求失败');
    yield put(templateGroupViewActions.failure());
  }
}

function* selectKeys(payload) {
  yield put(templateGroupViewActions.saveSelectKeys(payload));
}

function* changeSomeSwitchAvailable({ params, successCb }) {
  yield put(templateGroupViewActions.request());
  const { response, error } = yield call(
    templateGroupViewService.fetchchangeSomeSwitchAvailable,
    params
  );

  if (response) {
    yield put(templateGroupViewActions.saveSelectKeys([]));
    const payloads = {
      pageNo: 1,
      pageSize: 10,
    };
    yield fork(templateGroupViewPage, payloads);
    successCb();
  } else {
    message.error(error || '批量操作请求失败');
    yield put(templateGroupViewActions.failure());
  }
}

function* fetchTemplateTree() {
  const { response, error } = yield call(templateGroupViewService.fetchTemplateTree);
  if (response) {
    yield put(templateGroupViewActions.fetchTemplateTreeSuccess(response.data));
  } else {
    message.error(error || '告警模板树请求失败');
  }
}

function* fetchTemplateTreeChildren(payload) {
  const { itemType, doPointTree } = payload;
  const { response, error } = yield call(
    templateGroupViewService.fetchTemplateTreeChildren,
    itemType
  );
  if (response) {
    response.data.map(item => {
      item.metaCode = item.id;
      item.metaName = item.name;

      return response.data;
    });
    const newData = getChildren(doPointTree, itemType, response.data);

    yield put(templateGroupViewActions.fetchTemplateTreeSuccess(newData));

    yield put(templateGroupViewActions.fetchTemplateTreeChildrenSuccess(response.data));
  } else {
    message.error(error || '告警模板树子节点请求失败');
  }
}

function getChildren(alarmConfigurationTree, metaCode, response) {
  const list = alarmConfigurationTree.map((item, index) => {
    if (item.metaCode === metaCode) {
      return { ...item, children: [...response] };
    }
    if (item.children) {
      return { ...item, children: [...getChildren(item.children, metaCode, response)] };
    }
    return item;
  });
  return list;
}

function* selectElectricTable(electricTableData, selectData, resetDropped) {
  yield put(templateGroupViewActions.changeCreateTableloading());

  yield fork(electricTable, electricTableData, selectData, resetDropped);
}

function* electricTable(electricTableData, selectData, resetDropped) {
  let list;
  if (selectData === undefined) {
    list = electricTableData;
  } else {
    list = uniqBy([selectData, ...electricTableData], 'id');
  }
  yield put(templateGroupViewActions.failure());
  yield put(templateGroupViewActions.sureElectricTableSuccess(list));
  if (typeof resetDropped == 'function') {
    resetDropped();
  }
}

function* fetchTemplateGroupDetail(payload) {
  const { response, error } = yield call(templateGroupViewService.fetchConnectTemplates, payload);
  if (response) {
    const electricTemplates = response.data;
    yield put(
      templateGroupViewActions.fetchTemplateEleAndHeartSuccess({
        electricTemplates,
      })
    );

    yield put(templateGroupViewActions.fetchTemplateEleSure(electricTemplates));
  } else {
    message.error(error || '告警模板树请求失败');
    yield put(templateGroupViewActions.failure());
  }
}

function* fetchTemplateGroupDetailElectric(payload) {
  const { electricTemplates, searchName } = payload;
  let index = '';
  let newData = [];
  electricTemplates.forEach(item => {
    index = item.name.indexOf(searchName);
    if (index > -1) {
      newData.push(item);
    }
  });
  yield put(templateGroupViewActions.filterTemplateElectricSuccess(newData));
}

function* templateGroupArea(payload) {
  const { configId, configType } = payload;
  const { response, error } = yield call(
    templateGroupViewService.fetchTemplateGroupArea,
    configId,
    configType
  );

  if (response) {
    yield put(templateGroupViewActions.searchTemplateGroupAreaSuccess(response.data));
  } else {
    message.error(error || '模板组生效域请求失败');
    yield put(templateGroupViewActions.failure());
  }
}

function* searchAreaIdc(payload) {
  const { templateGroupAreaTable, region } = payload;
  let newData = [];
  const regionLen = (region || []).length;
  if (regionLen === 0) {
    newData = templateGroupAreaTable;
  }
  if (regionLen === 1) {
    newData = templateGroupAreaTable.filter(item => item.region === region[0]);
  }
  if (regionLen === 2) {
    newData = templateGroupAreaTable.filter(
      item => item.region === region[0] && item.idcTag === region[1]
    );
  }
  if (regionLen === 3) {
    newData = templateGroupAreaTable.filter(
      item => item.region === region[0] && item.idcTag === region[1] && item.blockTag === region[2]
    );
  }
  if (regionLen === 4) {
    newData = templateGroupAreaTable.filter(
      item =>
        item.region === region[0] &&
        item.idcTag === region[1] &&
        item.blockTag === region[2] &&
        item.roomTag === region[3]
    );
  }

  yield put(templateGroupViewActions.changeTemplateGroupArea(newData));
}

function* setAreaConfig(payload) {
  yield put(templateGroupViewActions.request());
  const { response, error } = yield call(templateGroupViewService.fetchAreaConfig, payload);

  if (response) {
    yield put(templateGroupViewActions.fetchAreaConfigSuccess(response));
    yield put(templateGroupViewActions.saveSearchAreaConfigConditionSuccess(payload));
  } else {
    message.error(error || '区域配置请求失败');
    yield put(templateGroupViewActions.failure());
  }
}

function* saveCreateTempteInput(payload) {
  yield put(templateGroupViewActions.saveCreateTempteInputSuccess(payload));
}

function* saveCreateTempteRadio(payload) {
  yield put(templateGroupViewActions.saveCreateTempteRadioSuccess(payload));
}

function* saveAreaCheckedKeys(payload) {
  yield put(templateGroupViewActions.choiceEreaBlockSuccess(payload));
  yield put(templateGroupViewActions.sureChoiceEreaBlockSuccess(payload));
}

function* fetchTemplateGroup(payload) {
  const { response, error } = yield call(templateGroupViewService.fetchCreateTemplate, payload);
  if (response) {
    message.success('添加模板组成功');
    yield put(
      templateGroupViewActions.getConnectTemplatesByTemplateGroupIdSuccess({
        electric: [],
      })
    );

    yield put(push(generateTemplteGroupListUrl()));
  } else {
    message.error(error || '新建模板请求失败');
  }
}

function* fetchAllTemplateGroup() {
  yield put(templateGroupViewActions.request());

  const { response, error } = yield call(templateGroupViewService.fetchAllTemplateGroup);
  if (response) {
    yield put(templateGroupViewActions.fetchAllTemplateGroupSuccess(response.data));
    yield put(templateGroupViewActions.savefetchAllTemplateGroupSuccess(response.data));
  } else {
    message.error(error || '查询所有模板组请求失败');
  }
}

function* SaveSelectTemplateGroup(payload) {
  yield put(templateGroupViewActions.SaveSelectTemplateGroupSuccess(payload));
}

function* searchTemplateUseName(payload) {
  const { name, allTemplateGroupList } = payload;

  let index = '';
  let newData = [];
  allTemplateGroupList.forEach(item => {
    index = item.name.indexOf(name);
    if (index > -1) {
      newData.push(item);
    }
  });

  yield put(templateGroupViewActions.fetchAllTemplateGroupSuccess(newData));
}

function* deleteTemplateInArea(payload) {
  const { selectTemplateGroup, id } = payload;
  const newData = selectTemplateGroup.filter(item => item.id !== id);
  yield put(templateGroupViewActions.SaveSelectTemplateGroupSuccess(newData));
}

function* handeleSearchAreaAndIdc(payload) {
  yield put(templateGroupViewActions.choiceEreaBlockSuccess(payload));
}

function* getTemplateByTemplateGroup(payload) {
  const { response, error } = yield call(
    templateGroupViewService.fetchConnectTemplatesByTemplateGroupId,
    payload
  );
  if (response) {
    yield put(templateGroupViewActions.failure());
    const electric = response.data;
    yield put(templateGroupViewActions.getConnectTemplatesByTemplateGroupIdSuccess({ electric }));
  } else {
    message.error(error || '查询所关联的模板请求失败');
  }
}

function* getAreaConfig(payload) {
  const { temp, type } = payload;
  const { response, error } = yield call(
    templateGroupViewService.fetchTemplateGroupArea,
    temp.configId,
    temp.configType
  );
  if (response) {
    yield put(templateGroupViewActions.getAreaConfigByIdSuccess(response.data));
    if (type === 'openIdcRoomVisible') {
      yield put(templateGroupViewActions.areaIdecRoomTreeVisible());
    }

    // yield put(templateGroupViewActions.sureChoiceEreaBlockSuccess(response.data));
    // yield put(templateGroupViewActions.choiceEreaBlockSuccess(response.data));
    // if (type === 'connect') {
    //   yield put(templateGroupViewActions.areaIdecRoomTreeVisible());
    // }
    // if (type === 'connectTable') {
    //   // yield fork(templateGroupArea, temp);
    //   yield put(templateGroupViewActions.searchTemplateGroupAreaSuccess(response.data));
    //   yield put(templateGroupViewActions.viewAreaVisible());
    //   // yield put(templateGroupViewActions.editAreaVisible());
    // }
  } else {
    message.error(error || '查询所关联的区域请求失败');
  }
}

function* cancleConnectAreaConfig(payload) {
  const { response, error } = yield call(
    templateGroupViewService.fetchCancleConnectAreaConfig,
    payload.params
  );
  if (response) {
    message.success('取消关联成功');
    payload.successCb();
    // payload.page === undefined
    //   ? yield fork(getAreaConfig, { temp: payload })
    //   : yield fork(setAreaConfig, { pageNum: 1, pageSize: 10 });
  } else {
    message.error(error);
    payload.errorCb();
  }
}

function* UpdateTemplateGroup(payload) {
  const { response, error } = yield call(
    templateGroupViewService.fetchUpdateTemplateGroupConfig,
    payload
  );
  if (response) {
    message.success('更新模板组成功');
    yield put(
      templateGroupViewActions.getConnectTemplatesByTemplateGroupIdSuccess({
        electric: [],
      })
    );
    yield put(push(generateTemplteGroupListUrl()));
  } else {
    message.error(error || '编辑模板组请求失败');
  }
}

function* connectAreaToTemplate(payload) {
  const {
    tempSaveIacBlockData,
    available,
    deviceType,
    groupName,
    idcTag,
    itemName,
    lastOperator,
    name,
    pageNo,
    pageSize,
  } = payload;
  const { response, error } = yield call(
    templateGroupViewService.fetchConnectAreaToTemplateGroup,
    tempSaveIacBlockData
  );
  if (response) {
    message.success('关联生效域成功');
    yield put(templateGroupViewActions.areaIdecRoomTreeVisible());
    yield fork(templateGroupViewPage, {
      available,
      deviceType,
      groupName,
      idcTag,
      itemName,
      lastOperator,
      name,
      pageNum: pageNo,
      pageSize,
    });
  } else {
    message.error(error);
  }
}

function* connectTemplateArea(payload) {
  const { name, lastOperator, available, region, pageSize, pageNum, tempData } = payload;
  const { response, error } = yield call(
    templateGroupViewService.fetchConnectAreaToTemplate,
    tempData
  );
  if (response) {
    message.success('添加关联成功');
    yield put(templateGroupViewActions.changeAreaIdecRoomTreeTemplateVisible());
    yield fork(setAreaConfig, {
      name,
      lastOperator,
      available,
      region,
      pageSize,
      pageNum,
    });
  } else {
    message.error(error || '添加关联请求失败');
  }
}

function* editConnectTemplateArea(payload) {
  const {
    // configId, configType,
    monitorConfigs,
    callback,
  } = payload;
  const { response, error } = yield call(
    templateGroupViewService.fetchConnectAreaToTemplateGroup,
    monitorConfigs
  );
  if (response) {
    message.success('关联成功');
    yield put(templateGroupViewActions.areaIdecRoomTreeVisible());
    if (callback) {
      callback();
    }
    // yield put(templateGroupViewActions.editAreaVisible());
    // if (configId.length === 1) {
    //   yield fork(getAreaConfig, { temp: { configId: configId[0], configType } });
    // }
  } else {
    message.error(error);
  }
}

function* fetchTemplateMess({ data, resetDropped }) {
  const { id, electricTableData } = data;
  const { response, error } = yield call(templateGroupViewService.fetchTemplateMessByMetaCode, id);
  if (response) {
    const selectData = response;
    yield fork(selectElectricTable, electricTableData, selectData, resetDropped);
    yield put(templateGroupViewActions.fetchTemplateMessByMetaCodeSuccess(response));
  } else {
    yield put(templateGroupViewActions.failure());
    message.error(error);
  }
}
/******************************************************************************/
/******************************* WATCHERS *************************************/
/******************************************************************************/

export function* watchFetchTemplateGroupViewPage() {
  while (true) {
    const { payload } = yield take(searchTemplateGroupPagesActions.type);
    yield fork(templateGroupViewPage, payload);
  }
}

export function* watchChangeSwitchAvailable() {
  while (true) {
    const { payload } = yield take(changeSwitchAvailableAction.type);
    yield fork(changeSwitchAvailable, payload);
  }
}

export function* watchSelectKeys() {
  while (true) {
    const { payload } = yield take(selectKeysAction.type);
    yield fork(selectKeys, payload);
  }
}

export function* watchChangeSomeSwitchAvailable() {
  while (true) {
    const { payload } = yield take(changeSomeSwitchAvailableAction.type);
    yield fork(changeSomeSwitchAvailable, payload);
  }
}

export function* watchFetchTemplateTree() {
  while (true) {
    yield take(fetchTemplateTreeAction.type);
    yield fork(fetchTemplateTree);
  }
}

export function* watchFetchTemplateTreeChildren() {
  while (true) {
    const { payload } = yield take(fetchTemplateTreeChildrenAction.type);
    yield fork(fetchTemplateTreeChildren, payload);
  }
}

export function* watchSelectElectricTableDate() {
  while (true) {
    const { payload } = yield take(selectElectricTableDateAction.type);
    const { electricTableData, selectData } = payload;

    yield fork(selectElectricTable, electricTableData, selectData);
  }
}

// 电力数据
export function* watchfetchElectricTable() {
  while (true) {
    const { payload } = yield take(electricTableDateAction.type);
    const { electricTableData, selectData } = payload;

    yield fork(electricTable, electricTableData, selectData);
  }
}

// 请求所有模板
export function* watchFetchTemplateGroupDetail() {
  while (true) {
    const { payload } = yield take(fetchConnectTemplatesAction.type);
    yield fork(fetchTemplateGroupDetail, payload);
  }
}

// 搜索
export function* watchFetchTemplateGroupDetailElectric() {
  while (true) {
    const { payload } = yield take(filterElectricAction.type);
    yield fork(fetchTemplateGroupDetailElectric, payload);
  }
}

// 查看模板组的生效域
export function* watchFetchTemplateGroupArea() {
  while (true) {
    const { payload } = yield take(fetchTemplateGroupAreaAction.type);
    yield fork(templateGroupArea, payload);
  }
}

// 根据区域 机房搜索
export function* watchSearchAreaIdc() {
  while (true) {
    const { payload } = yield take(searchAreaIdcAction.type);
    yield fork(searchAreaIdc, payload);
  }
}

// 根据区域 机房搜索
export function* watchfetchAreaConfig() {
  while (true) {
    const { payload } = yield take(searchAreaConfigAction.type);
    yield fork(setAreaConfig, payload);
  }
}

export function* watchSaveCreateTempteInput() {
  while (true) {
    const { payload } = yield take(saveCreateTemplateInputValueAction.type);
    yield fork(saveCreateTempteInput, payload);
  }
}

export function* watchSaveCreateTempteRadio() {
  while (true) {
    const { payload } = yield take(saveCreateTemplateRadioValueAction.type);
    yield fork(saveCreateTempteRadio, payload);
  }
}

export function* watchSaveAreaCheckedKeys() {
  while (true) {
    const { payload } = yield take(saveCheckedKeysAction.type);

    yield fork(saveAreaCheckedKeys, payload);
  }
}

// 新建模板组

export function* watchFetchTemplateGroup() {
  while (true) {
    const { payload } = yield take(fetchCreateTemplateGroupAction.type);
    yield fork(fetchTemplateGroup, payload);
  }
}

// 获取所有模板组
export function* watchFetchAllTemplateGroup() {
  while (true) {
    yield take(fetchAllTemplateGroupAction.type);
    yield fork(fetchAllTemplateGroup);
  }
}

// 选择
export function* watchSaveSelectTemplateGroup() {
  while (true) {
    const { payload } = yield take(saveSelectTemplateGroupAction.type);
    yield fork(SaveSelectTemplateGroup, payload);
  }
}

// 查询模板组
export function* watchSearchTemplateUseName() {
  while (true) {
    const { payload } = yield take(searchTemplteFromNameAction.type);
    yield fork(searchTemplateUseName, payload);
  }
}

// 删除模板组
export function* watchDeleteTemplateInArea() {
  while (true) {
    const { payload } = yield take(deleteTemplateInAreaAction.type);
    yield fork(deleteTemplateInArea, payload);
  }
}

//查询

export function* watchHandeleSearchAreaAndIdc() {
  while (true) {
    const { payload } = yield take(handeleSearchAreaAndIdcAction.type);

    yield fork(handeleSearchAreaAndIdc, payload);
  }
}

// 关联的模板组
export function* watchGetTemplateByTemplateGroupId() {
  while (true) {
    const { payload } = yield take(getTemplateByTemplateGroupId.type);
    yield fork(getTemplateByTemplateGroup, payload);
  }
}

// 关联的区域
export function* watchGetAreaConfigById() {
  while (true) {
    const { payload } = yield take(getAreaConfigById.type);
    yield fork(getAreaConfig, payload);
  }
}

// 取消关联
export function* watchCancleConnectArea() {
  while (true) {
    const { payload } = yield take(cancleConnectArea.type);
    yield fork(cancleConnectAreaConfig, payload);
  }
}

// 更新模板组
export function* watchUpdateTemplateGroupConfig() {
  while (true) {
    const { payload } = yield take(updateTemplateGroupConfig.type);
    yield fork(UpdateTemplateGroup, payload);
  }
}

// 生效域
export function* watchConnectRegionToTemplate() {
  while (true) {
    const { payload } = yield take(connectRegionToTemplate.type);
    yield fork(connectAreaToTemplate, payload);
  }
}

export function* watchConnectTemplateInArea() {
  while (true) {
    const { payload } = yield take(connectTemplateInArea.type);
    yield fork(connectTemplateArea, payload);
  }
}

export function* watcheditConnectTemplateInArea() {
  while (true) {
    const { payload } = yield take(connectTemplateInAreaSearchTemplate.type);
    yield fork(editConnectTemplateArea, payload);
  }
}

// 通过模板的metaCode 请求模板的列表信息
export function* watchFetchTemplateMessByMetaCode() {
  while (true) {
    const { payload } = yield take(fetchTemplateMessByMetaCode.type);
    yield fork(fetchTemplateMess, payload);
  }
}

export default [
  fork(watchFetchTemplateGroupViewPage),
  fork(watchChangeSwitchAvailable),
  fork(watchSelectKeys),
  fork(watchChangeSomeSwitchAvailable),
  fork(watchFetchTemplateTree),
  fork(watchFetchTemplateTreeChildren),
  fork(watchSelectElectricTableDate),
  fork(watchfetchElectricTable),
  fork(watchFetchTemplateGroupDetail),
  fork(watchFetchTemplateGroupDetailElectric),
  fork(watchFetchTemplateGroupArea),
  fork(watchSearchAreaIdc),
  fork(watchfetchAreaConfig),
  fork(watchSaveCreateTempteInput),
  fork(watchSaveCreateTempteRadio),
  fork(watchFetchTemplateGroup),
  fork(watchSaveAreaCheckedKeys),
  fork(watchFetchAllTemplateGroup),
  fork(watchSearchTemplateUseName),
  fork(watchSaveSelectTemplateGroup),
  fork(watchDeleteTemplateInArea),
  fork(watchHandeleSearchAreaAndIdc),
  fork(watchGetTemplateByTemplateGroupId),
  fork(watchGetAreaConfigById),
  fork(watchCancleConnectArea),
  fork(watchUpdateTemplateGroupConfig),
  fork(watchConnectRegionToTemplate),
  fork(watchConnectTemplateInArea),
  fork(watcheditConnectTemplateInArea),
  fork(watchFetchTemplateMessByMetaCode),
];
