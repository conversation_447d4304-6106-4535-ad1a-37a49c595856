import flatten from 'lodash/flatten';
import pick from 'lodash/pick';
import uniq from 'lodash/uniq';
import { all, call, delay, fork, put, race, select, take, takeLatest } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { flattenTreeData, traverse } from '@manyun/dc-brain.aura-graphix';
import { syncCommonDataAction } from '@manyun/dc-brain.state.common';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { findServerRackPDUs } from '@manyun/dcbrain-utils';
import { Alarm } from '@manyun/monitoring.model.alarm';
import { fetchAlarms } from '@manyun/monitoring.service.fetch-alarms';
import {
  addRealtimeNAlarmsDataSubscriptionActionCreator,
  removeRealtimeNAlarmsDataSubscriptionActionCreator,
} from '@manyun/monitoring.state.subscriptions';
import { fetchRoom } from '@manyun/resource-hub.service.fetch-room';
import { getSpaceGuid } from '@manyun/resource-hub.util.space-guid';
import { typeofRacksRoom } from '@manyun/resource-hub.util.type-of-racks-room';

import { TOPOLOGY_TYPE_KEY_MAP } from '@manyun/dc-brain.legacy.constants';
import { SUBSCRIPTIONS_MODE } from '@manyun/dc-brain.legacy.constants/subscriptions';
import { ThemeColors } from '@manyun/dc-brain.legacy.pages/__next/infra-room-graphix';
import {
  AISLE_TYPE_MAP,
  AISLE_WIDTH,
  RACKS_COLUMN_GAP,
  RACKS_COLUMN_WIDTH,
  SENSOR_SIZE,
  TH_HEIGHT,
  TR_HEIGHT,
} from '@manyun/dc-brain.legacy.pages/room-monitoring/constants';
import {
  deviceService,
  infrastructureService,
  roomMonitoringService,
  topologyService,
} from '@manyun/dc-brain.legacy.services';

import {
  getBatteryUnits,
  getInfraData,
  getInfraGraphPreview,
  getRoomType,
  getRppParentDevices,
} from '../selectors/roomMonitoringSelectors';
import {
  cancelGetAlarmsActionCreator,
  cancelGetMonitoringDataActionCreator,
  cancelGetUpsOrHvdcRealtimeDataActionCreator,
  getAlarmInfoActionCreator,
  getChildBatteryUnitsActionCreator,
  getInfrastructureDataActionCreator,
  getMonitoringDataActionCreator,
  getParentDevicesActionCreator,
  getParentDevicesMonitoringDataActionCreator,
  getRelatedDevicesActionCreator,
  getRoomCustomersActionCreator,
  getRoomInfoActionCreator,
  roomMonitoringActions,
} from './../actions/roomMonitoringActions';

function* getRoomInfo({ idc, block, room, topologyType }) {
  const roomGuid = getSpaceGuid(idc, block, room);
  const { error, data } = yield call(fetchRoom, { roomGuid });
  if (error) {
    message.error(error.message);
    return;
  }
  const roomInfo = {
    name: data.name,
    guid: data.guid,
    operationStatus: data.operationStatus.code,
    roomType: data.roomType,
  };
  yield put(roomMonitoringActions.setRoomInfo(roomInfo));

  const config = yield select(selectCurrentConfig);
  const configUtil = new ConfigUtil(config);
  yield put(
    syncCommonDataAction({
      strategy: {
        deviceTypesPointsDefinition: [
          configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_BLOCK),
          configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_ROOM),
          configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_COLUMN),
          configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_GRID),
        ],
      },
    })
  );
  yield fork(getInfraRoomGraphPreview, { idc, block, room, topologyType });

  if (typeofRacksRoom(roomInfo.roomType)) {
    yield put(getInfrastructureDataActionCreator({ idc, block, room }));
  } else {
    yield put(getMonitoringDataActionCreator({ idc, block, room }));
  }
}

function* getInfraRoomGraphPreview({ idc, block, room, topologyType }) {
  const roomGuid = getSpaceGuid(idc, block, room);
  const { response, error } = yield call(topologyService.fetchTopology, {
    blockGuid: roomGuid,
    topologyType: topologyType ?? TOPOLOGY_TYPE_KEY_MAP.ROOM_FACILITY,
  });
  let shouldTryToReset = false;
  if (error || !response) {
    shouldTryToReset = true;
    if (error) {
      message.error(error);
    }
  } else {
    const { viewJson } = response;
    try {
      const graph = JSON.parse(viewJson);
      graph.pages[0].background = 'transparent';
      graph.pages[0].scale = 1;
      const elements = graph.pages[0].children;
      fixElementAttrs(elements);
      const flatElements = flattenTreeData(elements);
      const { deviceTypes, deviceGuids, extraDevicesMap } = yield call(
        getInfraRoomGraphRelatedDevices,
        flatElements,
        roomGuid
      );
      yield put(
        syncCommonDataAction({
          strategy: {
            deviceTypesPointsDefinition: deviceTypes,
          },
        })
      );
      graph.pages[0].children = flatElements;
      yield put(
        roomMonitoringActions.setInfraRoomGraphPreview({
          roomGuid,
          graph,
          deviceGuids,
          extraDevicesMap,
          updatedAt: Date.now(),
        })
      );
    } catch (err) {
      shouldTryToReset = true;
      console.error(err);
    }
  }
  if (shouldTryToReset) {
    const prevPreview = yield select(getInfraGraphPreview);
    const shouldReset =
      prevPreview.roomGuid && prevPreview.graph && prevPreview.roomGuid !== roomGuid;
    if (shouldReset) {
      yield put(roomMonitoringActions.resetInfraRoomGraphPreview());
    }
  }
}

function fixElementAttrs(elements) {
  traverse(elements, element => {
    if (element.type === 'group') {
      element.canUngroup = element.custom?.type !== 'device_group';
    }
    if (element.custom?.type === 'device') {
      element.fill = ThemeColors.ContainerBg;
    } else if (element.custom?.type === 'device_text') {
      element.fill = ThemeColors.TextColor;
      element.stroke = ThemeColors.TextColor;
    } else if (element.custom?.type === 'point-text') {
      element.fill = ThemeColors.AnchorTextColor;
      element.stroke = ThemeColors.AnchorTextColor;
      // eslint-disable-next-line no-template-curly-in-string
      element.text = element.text.replace('${value}', '--');
      element.custom = {
        ...element.custom,
        __DEFAULT_FILL: element.fill,
        __DEFAULT_STROKE: element.stroke,
      };
    }
    element.listening = true;
    element.locked = true;
  });
}

function* getInfraRoomGraphRelatedDevices(elements, roomGuid) {
  const deviceTypes = [];
  const deviceGuids = [];
  const extraDevicesMap = {};
  const calls = [];
  elements.forEach(element => {
    let device;
    if (element.custom?.type === 'device_group') {
      device = element.children.find(child => child.custom?.type === 'device');
      if (!device) {
        return;
      }
    } else if (element.custom?.type === 'device') {
      device = element;
    }
    if (!device) {
      return;
    }
    if (!deviceTypes.some(deviceType => deviceType === device.custom.deviceType)) {
      deviceTypes.push(device.custom.deviceType);
    }
    if (!deviceGuids.some(deviceGuid => deviceGuid === device.custom.deviceGuid)) {
      deviceGuids.push(device.custom.deviceGuid);
    }
    calls.push(
      call(infrastructureService.fetchChildDevicesByGuid, {
        spaceGuid: roomGuid,
        parentGuid: device.id,
      })
    );
  });
  const results = yield all(calls);
  results.forEach(result => {
    if (Array.isArray(result.response?.data) && result.response?.data.length > 0) {
      const { parentGuid } = result.response.data[0];
      extraDevicesMap[parentGuid] = result.response.data;
      result.response.data.forEach(childDevice => {
        if (!deviceGuids.some(deviceGuid => deviceGuid === childDevice.guid)) {
          deviceGuids.push(childDevice.guid);
        }
      });
    }
  });

  return { deviceTypes, deviceGuids, extraDevicesMap };
}

function* getMonitoringData({ payload: { idc, block, room } }) {
  const roomGuid = getSpaceGuid(idc, block, room);

  const mode = SUBSCRIPTIONS_MODE.REALTIME_N_ALARMS;
  const blockGuid = getSpaceGuid(idc, block);
  const moduleId = 'room-monitoring';
  const deviceGuids = [blockGuid, roomGuid];

  const roomType = yield select(getRoomType);
  if (typeofRacksRoom(roomType)) {
    const {
      entities: { roomDevices, roomGrids },
      result,
    } = yield select(getInfraData);
    if (roomDevices) {
      deviceGuids.push(
        ...flatten(Object.values(roomDevices || {})).map(({ deviceGuid }) => deviceGuid)
      );
    }
    if (result.roomGrids) {
      deviceGuids.push(result.roomGrids.map(roomGrid => roomGuid + '.' + roomGrid));
      result.roomGrids.forEach(roomGrid => {
        deviceGuids.push(...roomGrids[roomGrid].gridTags.map(gridTag => roomGuid + '.' + gridTag));
      });
    }
  }

  yield put(
    addRealtimeNAlarmsDataSubscriptionActionCreator({ mode, blockGuid, moduleId, deviceGuids })
  );
  yield take(cancelGetMonitoringDataActionCreator.type);
  yield put(removeRealtimeNAlarmsDataSubscriptionActionCreator({ mode, blockGuid, moduleId }));
}

function* getAlarmInfo(q) {
  const { idc, block, room, pageNo, pageSize } = q;
  yield put(roomMonitoringActions.requestAlarmInfo());
  const { data, error } = yield call(fetchAlarms, {
    idcTag: idc,
    blockTags: [block],
    roomTags: [room],
    alarmStatus: ['ACTIVE', 'PROCESS', 'CONFIRMED'],
    triggerStatus: Alarm.UN_RESOLVED_ALARM_STATE_KEYS,
    isQueryData: true,
    pageNum: pageNo,
    pageSize,
  });
  if (!error) {
    yield put(
      roomMonitoringActions.setAlarmInfo({
        data: data.data.map(Alarm.toApiObject),
        total: data.total,
      })
    );
  } else {
    message.error(error.message);
    yield put(roomMonitoringActions.requestAlarmInfoError());
  }
  const { timeout } = yield race({
    shouldCancel: take(cancelGetAlarmsActionCreator.type),
    timeout: delay(15 * 1000),
  });
  if (timeout && pageNo === 1) {
    yield put(getAlarmInfoActionCreator(q));
  }
}

function* getInfrastructureData(q) {
  yield put(roomMonitoringActions.requestCabinetArrayInfo());
  const { response, error } = yield call(roomMonitoringService.fetchInfrastructureData, q);
  if (response) {
    yield put(roomMonitoringActions.setInfrastructureData(response));

    const {
      entities: { roomDevices, roomGrids },
      result,
    } = response;
    const devices = flatten(Object.values(roomDevices || {}));

    const config = yield select(selectCurrentConfig);
    const configUtil = new ConfigUtil(config);
    const getAisleTypeByDeviceType = deviceType => {
      if (
        configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.COLD_AISLE_THTB_SENSOR) ===
        deviceType
      ) {
        return AISLE_TYPE_MAP.COLD;
      }

      if (
        configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.HOT_AISLE_THTB_SENSOR) ===
        deviceType
      ) {
        return AISLE_TYPE_MAP.HOT;
      }

      return null;
    };

    let containerMaxWidth = 0;
    let maxHeight = 0;
    const rackColumns = result.roomGrids.map((roomGrid, columnIndex, thisArray) => {
      const { gridTags } = roomGrids[roomGrid];

      let acNorth = null;
      let acSouth = null;
      let leftAisleType = null;
      let rightAisleType = null;
      const devicesOnLeft = [];
      const devicesOnRight = [];
      const rppAs = [];
      const rppBs = [];
      const grids = [];
      gridTags.forEach(gridTag => {
        const acN = roomDevices[`${gridTag}.N`]?.[0]; // 位于机列北面的末端空调
        if (acN) {
          acNorth = acN;
        }

        const acS = roomDevices[`${gridTag}.S`]?.[0]; // 位于机列南面的末端空调
        if (acS) {
          acSouth = acS;
        }

        const acTop = roomDevices[`${gridTag}.TOP`]?.[0]; // 位于机柜上方（非物理位置）的列间空调
        if (acTop) {
          grids.push({
            type: 'CRAC',
            roomGrid,
            ...acTop,
          });
        }

        const { A: pduAs, B: pduBs } = findServerRackPDUs(gridTag, roomDevices);
        const pduRppAs = getPduParentRPPs(pduAs, roomDevices);
        const pduRppBs = getPduParentRPPs(pduBs, roomDevices);
        pduRppAs.forEach(pduRppA => {
          if (rppAs.some(rppA => rppA.deviceGuid === pduRppA.deviceGuid)) {
            return;
          }
          rppAs.push(pduRppA);
        });
        pduRppBs.forEach(pduRppB => {
          if (rppBs.some(rppB => rppB.deviceGuid === pduRppB.deviceGuid)) {
            return;
          }
          rppBs.push(pduRppB);
        });
        grids.push({
          type: 'SPACE_GRID',
          gridTag,
          pduAs,
          pduBs,
          rppAs: pduRppAs,
          rppBs: pduRppBs,
        });

        const acBottom = roomDevices[`${gridTag}.BTM`]?.[0]; // 位于机柜下方（非物理位置）的列间空调
        if (acBottom) {
          grids.push({
            type: 'CRAC',
            roomGrid,
            ...acBottom,
          });
        }

        const leftSensor = roomDevices[`${gridTag}.L`]?.[0];
        if (leftSensor) {
          if (leftAisleType === null) {
            leftAisleType = getAisleTypeByDeviceType(leftSensor.deviceType);
          }
          devicesOnLeft.push({
            gridTag,
            ...leftSensor,
          });
        }

        const rightSensor = roomDevices[`${gridTag}.R`]?.[0];
        if (rightSensor) {
          if (rightAisleType === null) {
            rightAisleType = getAisleTypeByDeviceType(rightSensor.deviceType);
          }
          devicesOnRight.push({
            gridTag,
            ...rightSensor,
          });
        }
      });

      const width = AISLE_WIDTH + RACKS_COLUMN_GAP + RACKS_COLUMN_WIDTH;
      containerMaxWidth += width;

      const height = grids.length * TR_HEIGHT + TH_HEIGHT;
      if (height > maxHeight) {
        maxHeight = height;
      }

      devicesOnLeft.forEach(leftSensor => {
        const idx = grids.findIndex(grid => grid.gridTag === leftSensor.gridTag);
        const isFirstOne = idx === 0;
        const isLastOne = idx === grids.length - 1;
        const top = getSensorOffsetTop(idx, isFirstOne, isLastOne, height);
        leftSensor.position = {
          top,
          right: columnIndex === 0 ? undefined : '50%',
        };
      });

      devicesOnRight.forEach(rightSensor => {
        const idx = grids.findIndex(grid => grid.gridTag === rightSensor.gridTag);
        const isFirstOne = idx === 0;
        const isLastOne = idx === grids.length - 1;
        const top = getSensorOffsetTop(idx, isFirstOne, isLastOne, height);
        rightSensor.position = {
          top,
          left: columnIndex === thisArray.length - 1 ? undefined : '50%',
        };
      });

      return {
        height,
        leftAisleHeight: height,
        rightAisleHeight: height,
        acNorth,
        acSouth,
        leftAisleType,
        rightAisleType,
        leftSensors: devicesOnLeft,
        rightSensors: devicesOnRight,
        columnTag: roomGrid,
        rppAs,
        rppBs,
        grids,
      };
    });

    rackColumns.forEach((rackColumn, index, thisArray) => {
      const isFirst = index === 0;
      const isLast = index === thisArray.length - 1;

      const prevRackColumn = isFirst ? null : thisArray[index - 1];
      const nextRackColumn = isLast ? null : thisArray[index + 1];

      // fix the THTB Sensors position
      if (prevRackColumn) {
        rackColumn.leftSensors.forEach(device => {
          const overlapping = prevRackColumn.rightSensors.find(
            prevRackColumnDevice =>
              (prevRackColumnDevice.position.top >= device.position.top &&
                prevRackColumnDevice.position.top <= device.position.top + SENSOR_SIZE) ||
              prevRackColumnDevice.position.top + SENSOR_SIZE >= device.position.top
          );
          if (overlapping) {
            overlapping.position.left = 0;
            device.position.right = 0;
          }
        });
      }

      // fix the aisle type
      // ------
      if (
        prevRackColumn &&
        prevRackColumn.rightAisleType !== null &&
        rackColumn.leftAisleType === null
      ) {
        rackColumn.leftAisleType = prevRackColumn.rightAisleType;
      }
      if (
        nextRackColumn &&
        nextRackColumn.leftAisleType !== null &&
        rackColumn.rightAisleType === null
      ) {
        rackColumn.rightAisleType = nextRackColumn.leftAisleType;
      }

      // fix the aisle height
      // ------
      if (prevRackColumn && prevRackColumn.rightAisleHeight > rackColumn.leftAisleHeight) {
        rackColumn.leftAisleHeight = prevRackColumn.rightAisleHeight;
      }
      if (nextRackColumn && nextRackColumn.leftAisleHeight > rackColumn.rightAisleHeight) {
        rackColumn.rightAisleHeight = nextRackColumn.leftAisleHeight;
      }
    });

    yield put(roomMonitoringActions.setRackColumns({ containerMaxWidth, maxHeight, rackColumns }));

    const deviceTypes = uniq(devices.map(({ deviceType }) => deviceType));

    // 先获取测点定义以用于显示 AI 测点的单位及 DI 测点的文案
    yield put(
      syncCommonDataAction({
        strategy: {
          deviceTypesPointsDefinition: deviceTypes,
        },
      })
    );

    yield put(getMonitoringDataActionCreator(q));
  } else {
    message.error(error);
  }
}

function* getRoomCustomers(q) {
  const { response, error } = yield call(roomMonitoringService.fetchRoomCustomers, q);
  if (response) {
    yield put(
      roomMonitoringActions.setRoomCustomers({
        data: response.data.map(item => ({
          ...item,
          customer: item.customer || '暂无',
        })),
        total: response.total,
      })
    );
  } else {
    message.error(error);
  }
}

/**
 * 获取列头柜的上联设备
 */
function* getParentDevices({ idc, block, deviceGuids }) {
  const tasks = deviceGuids.map(childDeviceGuid =>
    call(topologyService.fetchTopologyStructure, {
      blockGuid: `${idc}.${block}`,
      deviceGuid: childDeviceGuid,
      checkStatus: false,

      // 因为列头柜上联可能是 `输出柜`，再往上才是 `UPS` 或 `HVDC`，安全起见，往上查 3 层。
      conditionList: [{ direction: 'UP', level: 3 }],

      topologyType: 'ELECTRIC_POWER',
    })
  );
  const responses = yield all(tasks);
  const config = yield select(selectCurrentConfig);
  const configUtil = new ConfigUtil(config);
  const upsTypes = configUtil.getAllDeviceTypes(ConfigUtil.constants.deviceTypes.UPS) || [];
  const parentDevicesMap = {};
  responses.forEach(({ response }, idx) => {
    if (!response) {
      return;
    }
    const { nodeList } = response;
    parentDevicesMap[deviceGuids[idx]] = nodeList.filter(node =>
      upsTypes.includes(node.deviceType)
    );
  });
  yield put(roomMonitoringActions.updateArrayCabinetsParentDeviceMap(parentDevicesMap));
}

function* getParentDevicesMonitoringData({ idc, block }) {
  const blockGuid = idc + '.' + block;
  const upses = yield select(getRppParentDevices);
  const batteryUnits = yield select(getBatteryUnits);
  const mode = SUBSCRIPTIONS_MODE.REALTIME_N_ALARMS;
  const moduleId = 'room-monitoring_upses-n-battery-units';
  yield put(
    addRealtimeNAlarmsDataSubscriptionActionCreator({
      mode,
      moduleId,
      blockGuid,
      deviceGuids: [
        ...upses.map(({ deviceGuid }) => deviceGuid),
        ...batteryUnits.map(({ guid }) => guid),
      ],
    })
  );
  yield take([
    cancelGetMonitoringDataActionCreator.type,
    cancelGetUpsOrHvdcRealtimeDataActionCreator.type,
  ]);
  yield put(removeRealtimeNAlarmsDataSubscriptionActionCreator({ mode, moduleId, blockGuid }));
}

function* getDevice(deviceGuid) {
  const { response } = yield call(deviceService.fetchDeviceByGuid, deviceGuid);
  if (response) {
    yield put(
      roomMonitoringActions.updateDeviceInfo({
        [deviceGuid]: pick(response, ['name', 'deviceType', 'operationStatus']),
      })
    );
  }
}

function* getChildDevices({ idc, block, deviceGuid }) {
  const { response } = yield call(topologyService.fetchTopologyStructure, {
    blockGuid: `${idc}.${block}`,
    deviceGuid,
    checkStatus: false,
    conditionList: [{ direction: 'DOWN', level: 3 }],
    topologyType: TOPOLOGY_TYPE_KEY_MAP.ELECTRIC_POWER,
  });
  if (response) {
    yield put(roomMonitoringActions.updateChildDeviceMap({ [deviceGuid]: response.nodeList }));
  }
}

function* getChildBatteryUnits({ idc, block, deviceGuid }) {
  const spaceGuid = `${idc}.${block}`;
  const { response } = yield call(infrastructureService.fetchChildDevicesByGuid, {
    spaceGuid,
    parentGuid: deviceGuid,
  });
  if (response) {
    yield put(roomMonitoringActions.updateChildBatteryUnits({ [deviceGuid]: response.data }));
  }
}

function* getRelatedDeviceTypePointDefinitions() {
  const upses = yield select(getRppParentDevices);
  const batteryUnits = yield select(getBatteryUnits);
  yield put(
    syncCommonDataAction({
      strategy: {
        deviceTypesPointsDefinition: uniq([
          ...upses.map(({ deviceType }) => deviceType),
          ...batteryUnits.map(({ deviceType }) => deviceType),
        ]),
      },
    })
  );
}

function* getUpsOrHvdcRelatedDevices(q) {
  yield call(getDevice, q.deviceGuid);
  yield call(getChildDevices, q);
  yield call(getChildBatteryUnits, q);
  yield call(getRelatedDeviceTypePointDefinitions);
  yield put(getParentDevicesMonitoringDataActionCreator(q));
}

/******************************************************************************/
/******************************* WATCHERS *************************************/
/******************************************************************************/

function* watchGetRoomInfo() {
  while (true) {
    const { payload } = yield take(getRoomInfoActionCreator.type);
    yield fork(getRoomInfo, payload);
  }
}

function* watchGetMonitoringData() {
  yield takeLatest(getMonitoringDataActionCreator.type, getMonitoringData);
}

function* watchGetAlarmInfo() {
  while (true) {
    const { payload } = yield take(getAlarmInfoActionCreator.type);
    yield fork(getAlarmInfo, payload);
  }
}

function* watchGetInfrastructureData() {
  while (true) {
    const { payload } = yield take(getInfrastructureDataActionCreator.type);
    yield fork(getInfrastructureData, payload);
  }
}

function* watchGetRoomCustomers() {
  while (true) {
    const { payload } = yield take(getRoomCustomersActionCreator.type);
    yield fork(getRoomCustomers, payload);
  }
}

function* watchGetParentDevices() {
  while (true) {
    const { payload } = yield take(getParentDevicesActionCreator.type);
    yield fork(getParentDevices, payload);
  }
}

function* watchGetParentDevicesMonitoringData() {
  while (true) {
    const { payload } = yield take(getParentDevicesMonitoringDataActionCreator.type);
    yield fork(getParentDevicesMonitoringData, payload);
  }
}

function* watchGetChildBatteryUnits() {
  while (true) {
    const { payload } = yield take(getChildBatteryUnitsActionCreator.type);
    yield fork(getChildBatteryUnits, payload);
  }
}

function* watchGetUpsOrHvdcRelatedDevices() {
  while (true) {
    const { payload } = yield take(getRelatedDevicesActionCreator.type);
    yield fork(getUpsOrHvdcRelatedDevices, payload);
  }
}

export default [
  fork(watchGetRoomInfo),
  fork(watchGetMonitoringData),
  fork(watchGetAlarmInfo),
  fork(watchGetInfrastructureData),
  fork(watchGetRoomCustomers),
  fork(watchGetParentDevices),
  fork(watchGetParentDevicesMonitoringData),
  fork(watchGetChildBatteryUnits),
  fork(watchGetUpsOrHvdcRelatedDevices),
];

/**
 * 找出所有 PDU 的上联列头柜
 * @param {Array<{ deviceTag: string }>} pdus
 * @param {object|null} roomDevices 设备集合对象（`key` 为设备扩展位置）
 */
function getPduParentRPPs(pdus, roomDevices) {
  if (!pdus || !roomDevices) {
    return [];
  }

  const devices = flatten(Object.values(roomDevices));
  const rpps = [];
  pdus.forEach(pdu => {
    const rppTag = pdu.deviceTag.substring(0, pdu.deviceTag.lastIndexOf('.'));
    if (rpps.some(rpp => rpp.deviceTag === rppTag)) {
      // Skip it
      return;
    }
    const rppDevice = devices.find(device => device.deviceTag === rppTag);
    if (rppDevice) {
      rpps.push(rppDevice);
    }
  });

  return rpps;
}

function getSensorOffsetTop(idx, isFirstOne, isLastOne, tableHeight) {
  if (isFirstOne) {
    return TR_HEIGHT + 1;
  }

  if (isLastOne) {
    return tableHeight - /* 传感器的高度 */ SENSOR_SIZE;
  }

  return (idx + 2) * TR_HEIGHT + /* 上侧 border width 占 1px */ 1 - SENSOR_SIZE / 2 - TR_HEIGHT / 2;
}
