import { call, cancel, fork, put, race, select, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { serverNodeService } from '@manyun/dc-brain.legacy.services';

import {
  resetSearchValuesActionCreator,
  serverNodeActionCreator,
  serverNodeActions,
  setPaginationThenGetDataActionCreator,
} from '../actions/serverNodeActions';
import { getSearchValuesAndPagination } from './../selectors/serverNodeSeletcors';

function getQ(pagination, searchValues) {
  const baseQ = { ...pagination };
  for (const [name, value] of Object.entries(searchValues)) {
    const val = value === '' ? null : value;
    if (name === 'condition') {
      baseQ[name] = val;
    }
  }
  return baseQ;
}

// workers

function* getData(shouldResetPageNum = true) {
  yield put(serverNodeActions.request());
  if (shouldResetPageNum) {
    yield put(serverNodeActions.resetPageNum());
  }
  const { searchValues, pagination } = yield select(getSearchValuesAndPagination);
  const q = getQ(pagination, searchValues);
  const { response, error } = yield call(serverNodeService.fetchServerNodePage, q);
  if (error) {
    message.error(error);
  } else {
    yield put(serverNodeActions.setDataAndTotal(response));
  }
}

function* resetSearchValues() {
  yield put(serverNodeActions.resetSearchValuesAndPagination());
  yield put(serverNodeActionCreator(false));
}

function* setPagination(newPagination) {
  yield put(serverNodeActions.setPagination(newPagination));
  yield put(serverNodeActionCreator(false));
}

// watchers

function* watchGetData() {
  while (true) {
    const { payload } = yield take(serverNodeActionCreator.type);
    const task = yield fork(getData, payload);
    const [reset] = yield race([take(resetSearchValuesActionCreator.type), task]);
    if (reset) {
      cancel(task);
    }
  }
}

function* watchResetSearchValues() {
  while (true) {
    yield take(resetSearchValuesActionCreator.type);
    yield fork(resetSearchValues);
  }
}

function* watchSetPagination() {
  while (true) {
    const { payload } = yield take(setPaginationThenGetDataActionCreator.type);
    yield fork(setPagination, payload);
  }
}

export default [fork(watchGetData), fork(watchResetSearchValues), fork(watchSetPagination)];
