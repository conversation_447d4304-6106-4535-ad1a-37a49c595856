import { call, fork, put, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import * as taskCenterService from '@manyun/dc-brain.legacy.services/taskCenterService';

import {
  fetchInventoryAnalyticsCount,
  fetchInventoryAnalyticsPage,
  inventoryAnalyticsActions,
} from '../actions/inventoryAnalysticsActions';

function* fetchInventoryAnalyticsList(payload) {
  yield put(inventoryAnalyticsActions.request());
  const { response, error } =
    payload.type === 'snDevice'
      ? yield call(taskCenterService.fetchInventoryAnalyticsDevice, payload.params)
      : yield call(taskCenterService.fetchInventoryAnalyticsSpareDevice, payload.params);
  if (response) {
    yield put(inventoryAnalyticsActions.setInventoryAnalyticsPageCondition(payload.params));
    yield put(inventoryAnalyticsActions.fetchInventoryAnalyticsListSuccess(response));
  } else {
    message.error(error || '请求失败');
    yield put(inventoryAnalyticsActions.failure());
  }
}

function* fetchInventoryAnalyticsCountList(payload) {
  const { response, error } =
    payload.type === 'snDevice'
      ? yield call(taskCenterService.fetchInventoryAnalyticsCount, payload.params)
      : yield call(taskCenterService.fetchInventoryAnalyticsSpareCount, payload.params);
  if (response) {
    yield put(inventoryAnalyticsActions.setInventoryAnalyticsCount(response));
  } else {
    message.error(error || '请求失败');
  }
}

export function* watchFetchInventoryAnalyticsList() {
  while (true) {
    const { payload } = yield take(fetchInventoryAnalyticsPage.type);
    yield fork(fetchInventoryAnalyticsList, payload);
  }
}

export function* watchFetchInventoryAnalyticsCount() {
  while (true) {
    const { payload } = yield take(fetchInventoryAnalyticsCount.type);
    yield fork(fetchInventoryAnalyticsCountList, payload);
  }
}

export default [fork(watchFetchInventoryAnalyticsList), fork(watchFetchInventoryAnalyticsCount)];
