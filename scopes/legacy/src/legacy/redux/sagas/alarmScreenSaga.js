import { all, call, fork, put, take, takeLatest } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { Alarm } from '@manyun/monitoring.model.alarm';
import { fetchAlarms } from '@manyun/monitoring.service.fetch-alarms';

import { alarmScreenService, treeDataService } from '@manyun/dc-brain.legacy.services';

import {
  alarmScreenActions,
  createEventContent,
  fetchAlarmConvergenceList,
  fetchAlarmDetail,
  fetchAlarmScreenList,
  fetchCreateEventConfirm,
  fetchCustomerList, // fetchConfirm,
  // fetchBatchConfirm,
  // fetchAlarmDelete,
  fetchEventList,
  fetchEventTypeList, // fetchMetaQueryAlarmType,
  fetchMetaCategoryNormalize, // fetchAlarmDetailInfo,
  getHistoryAlarmList,
} from '../actions/alarmScreenActions';

// 盯屏列表
function* watchFetchAlarmScreenList() {
  yield takeLatest(fetchAlarmScreenList.type, alarmScreenList);
}

function* alarmScreenList({ payload }) {
  const { successCallback = () => {} } = payload;
  yield put(alarmScreenActions.request());
  const { data, error } = yield call(fetchAlarms, {
    ...payload,
    isQueryData: true,
  });
  if (!error) {
    const result = data.data.map(Alarm.toApiObject);
    yield put(
      alarmScreenActions.featchAlarmScreenSuccess({
        total: data.total,
        list: result,
        pageNum: payload.pageNum,
        pageSize: payload.pageSize,
        sortField: payload.sortField,
        sortOrder: payload.sortOrder,
      })
    );
    successCallback(result);
  } else {
    message.error(error.message || '盯屏列表失败');
    yield put(alarmScreenActions.failure());
  }
}

// function* metaQueryAlarmType() {
//   const { normalizedList } = yield call(treeDataService.fetchMetaQueryAlarmType);
//   if (normalizedList) {
//     yield put(alarmScreenActions.metaQueryAlarmTypeEntities(normalizedList));
//   }
// }

// 告警列表
function* watchFetchAlarmList() {
  while (true) {
    const { payload } = yield take(getHistoryAlarmList.type);
    yield fork(alarmList, payload);
  }
}

function* alarmList(payload) {
  yield put(alarmScreenActions.request());
  const { data, error } = yield call(fetchAlarms, {
    status: 'REMOVED',
    isQueryData: true,
    ...payload,
  });
  if (!error) {
    yield put(
      alarmScreenActions.fetchAlarmSuccess({
        total: data.total,
        list: data.data.map(Alarm.toApiObject),
        pageNum: payload.pageNum,
        pageSize: payload.pageSize,
      })
    );
    // yield put(alarmScreenActions.metaCategoryEntities(normalizedList));
  } else {
    message.error(error.message || '告警列表请求失败');
    yield put(alarmScreenActions.failure());
  }
}

function* metaCategory() {
  const { normalizedList } = yield call(treeDataService.fetchDeviceCategory);
  if (normalizedList) {
    yield put(alarmScreenActions.metaCategoryEntities(normalizedList));
  }
}

// // 告警详情列表
// function* watchAlarmDetailInfo() {
//   while (true) {
//     const { payload } = yield take(fetchAlarmDetailInfo.type);
//     yield fork(alarmDetailInfo, payload);
//   }
// }

// function* alarmDetailInfo(payload) {
//   yield put(alarmScreenActions.request());
//   const { response, error } = yield call(alarmScreenService.alarmDetailInfo, payload);
//   if (response) {
//     yield put(
//       // alarmScreenActions.fetchAlarmDetailInfoSuccess(response)
//       alarmScreenActions.fetchAlarmDetailInfoSuccess({ type: 'aaa' })
//     );
//   } else {
//     message.error(error || '机柜列表请求失败');
//     yield put(alarmScreenActions.failure());
//   }
// }

// 告警确认
// function* watchFetchConfirm() {
//   while (true) {
//     const { payload } = yield take(fetchConfirm.type);
//     yield fork(confirmAlarm, {
//       param: payload.params,
//       alarmId: payload.alarmId,
//       idcTag: payload.idcTag,
//     });
//   }
// }

// function* confirmAlarm({ param, alarmId, idcTag }) {
//   yield put(alarmScreenActions.request());
//   const { response, error } = yield call(alarmScreenService.confirmAlarm, alarmId, idcTag);
//   if (response) {
//     message.success('确认成功');
//     yield fork(alarmScreenList, { payload: param });
//   } else {
//     message.error(error || '确认失败');
//     yield put(alarmScreenActions.failure());
//   }
// }

// // 告警批量确认
// function* watchFetchBatchConfirm() {
//   while (true) {
//     const { payload } = yield take(fetchBatchConfirm.type);
//     yield fork(batchConfirm, { param: payload.params, alarmId: payload.alarmId });
//   }
// }

// function* batchConfirm({ param, alarmId }) {
//   yield put(alarmScreenActions.request());
//   const { response, error } = yield call(alarmScreenService.batchConfirm, alarmId);
//   if (response) {
//     message.success('确认成功');
//     yield fork(alarmScreenList, { payload: param });
//   } else {
//     message.error(error || '确认失败');
//     yield put(alarmScreenActions.failure());
//   }
// }

// 下盯屏
// function* watchFetchAlarmDelete() {
//   while (true) {
//     const { payload } = yield take(fetchAlarmDelete.type);
//     yield fork(alarmDelete, {
//       param: payload.params,
//       alarmId: payload.alarmId,
//       idcTag: payload.idcTag,
//     });
//   }
// }

// function* alarmDelete({ param, alarmId, idcTag }) {
//   yield put(alarmScreenActions.request());
//   const { response, error } = yield call(alarmScreenService.alarmDelete, alarmId, idcTag);
//   if (response) {
//     message.success('下盯屏成功');
//     yield fork(alarmScreenList, { payload: param });
//   } else {
//     message.error(error || '下盯屏失败');
//     yield put(alarmScreenActions.failure());
//   }
// }

// 事件列表
function* watchFetchEventList() {
  while (true) {
    const { payload } = yield take(fetchEventList.type);
    yield fork(eventList, payload);
  }
}

function* eventList({ param, alarmId }) {
  yield put(alarmScreenActions.request());
  throw new Error('alarmScreenService.eventList was deprecated and had been removed!');
  // const { response, error } = yield call(alarmScreenService.eventList, alarmId);
  // if (response) {
  //   yield put(alarmScreenActions.fetchEventListSuccess(response));
  // } else {
  //   message.error(error || '机柜列表请求失败');
  //   yield put(alarmScreenActions.failure());
  // }
}

// // 建单
// function* watchFetchCreateConfirm() {
//   while (true) {
//     const { payload } = yield take(fetchCreateTicketConfirm.type);
//     yield fork(createConfirm, {
//       param: payload.params,
//       alarmId: payload.alarmId,
//       eventId: payload.eventId,
//     });
//   }
// }

// function* createConfirm({ param, alarmId, eventId }) {
//   yield put(alarmScreenActions.createLoading());
//   const { response, error } = yield call(alarmScreenService.createConfirm, alarmId, eventId);
//   if (response) {
//     yield fork(alarmScreenList, { payload: param });
//     yield put(alarmScreenActions.createLoading());
//     yield put(alarmScreenActions.createTicketVisible());
//   } else {
//     message.error(error || '机柜列表请求失败');
//     yield put(alarmScreenActions.failure());
//   }
// }

// 客户列表
function* watchFetchCustomerList() {
  while (true) {
    const { payload } = yield take(fetchCustomerList.type);
    yield fork(customerList, payload.idcTag);
  }
}

function* customerList(idcTag) {
  yield put(alarmScreenActions.customerListRequest());
  const { response, error } = yield call(alarmScreenService.customerList, idcTag);
  if (response) {
    yield put(alarmScreenActions.customerListSuccess(response));
  } else {
    message.error(error || '机柜列表请求失败');
    yield put(alarmScreenActions.failure());
  }
}

// 点击新建事件获取事件类型等
function* watchCreateEventContent() {
  while (true) {
    const { payload } = yield take(createEventContent.type);
    const { selecteRows } = payload;
    yield fork(eventContent, selecteRows);
  }
}

// eslint-disable-next-line require-yield
function* eventContent(selecteRows) {
  throw new Error('alarmScreenService.createEventContent was deprecated and had been removed!');
  // const { response, error } = yield call(alarmScreenService.createEventContent, selecteRows);
  // if (response) {
  //   yield put(alarmScreenActions.createEventContentSuccess(response));
  // } else {
  //   message.error(error || '机柜列表请求失败');
  //   yield put(alarmScreenActions.failure());
  // }
}

// 事件类型
function* watchFetchEventTypeList() {
  while (true) {
    yield take(fetchEventTypeList.type);
    yield fork(eventTypeList);
  }
}

function* eventTypeList() {
  const { response, error } = yield call(alarmScreenService.createEventTypeList);
  if (response) {
    yield put(alarmScreenActions.eventTypeSuccess(response));
  } else {
    message.error(error || '机柜列表请求失败');
    yield put(alarmScreenActions.failure());
  }
}

// 新建事件
function* watchFetchCreateEventConfirm() {
  while (true) {
    const { payload } = yield take(fetchCreateEventConfirm.type);
    yield fork(createEventConfirm, payload);
  }
}

function* createEventConfirm(payload) {
  const { response, error } = yield call(alarmScreenService.createEventConfirm, payload);
  if (response) {
    yield put(alarmScreenActions.createLoading());
    yield put(alarmScreenActions.createEventVisible());
  } else {
    message.error(error || '机柜列表请求失败');
    yield put(alarmScreenActions.failure());
  }
}

// 详情分类展示
function* watchFetchAlarmDetail() {
  while (true) {
    const { payload } = yield take(fetchAlarmDetail.type);
    yield fork(alarmDetail, payload);
  }
}

function* alarmDetail(payload) {
  const [
    {
      response: { deviceAlarmCount, mergeRuleName, alarmInfo },
      error,
    },
    { normalizedList },
  ] = yield all([
    call(alarmScreenService.alarmDetail, payload),
    call(treeDataService.fetchDeviceCategory),
  ]);
  if (normalizedList) {
    const list = [];
    if (deviceAlarmCount) {
      for (let i in deviceAlarmCount) {
        list.push({
          ...deviceAlarmCount[i],
          type: normalizedList[i].metaName,
          deviceType: i,
        });
      }
    }
    if (alarmInfo.mergeCount) {
      yield fork(alarmConvergenceList, {
        pageNum: 1,
        pageSize: 10,
        idcTag: payload.idcTag,
        alarmId: payload.alarmId,
      });
      yield fork(metaCategory);
    }
    yield put(
      alarmScreenActions.alarmDetailSuccess({
        list,
        mergeRuleName,
        alarmInfo: alarmInfo,
      })
    );
  } else {
    message.error(error || '详情请求失败');
    yield put(alarmScreenActions.failure());
  }
}

// 收敛列表
function* watchFetchAlarmConvergenceList() {
  while (true) {
    const { payload } = yield take(fetchAlarmConvergenceList.type);
    yield fork(alarmConvergenceList, payload);
  }
}

function* alarmConvergenceList(payload) {
  const { data, error } = yield call(fetchAlarms, {
    ...payload,
    status: 'REMOVED',
    isMerge: true,
    isQueryData: true,
  });
  if (!error) {
    yield put(
      alarmScreenActions.alarmConvergenceSuccess({
        list: data.data.map(Alarm.toApiObject),
        total: data.total,
        pageSize: payload.pageSize,
        pageNum: payload.pageNum,
        deviceTypeList: payload.deviceTypeList,
      })
    );
  } else {
    message.error(error.message || '机柜列表请求失败');
    yield put(alarmScreenActions.failure());
  }
}

// // 告警类型
// export function* watchFetchAlarmTypeList() {
//   while (true) {
//     const { payload } = yield take(fetchAlarmTypeList.type);
//     yield fork(alarmTypeList, payload);
//   }
// }

// function* alarmTypeList(payload) {
//   const { response, error } = yield call(alarmScreenService.alarmTypeList, payload);
//   if (response) {
//     const list = [];
//     for (let i in response) {
//       list.push({ name: response[i], code: i });
//     }
//     yield put(alarmScreenActions.alarmTypeListSuccess(list));
//   } else {
//     message.error(error || '机柜列表请求失败');
//     yield put(alarmScreenActions.failure());
//   }
// }

// function* watchFetchMetaQueryAlarmType() {
//   while (true) {
//     const { payload } = yield take(fetchMetaQueryAlarmType.type);
//     yield fork(metaQueryAlarmType, payload);
//   }
// }

function* watchFetchMetaCategory() {
  while (true) {
    const { payload } = yield take(fetchMetaCategoryNormalize.type);
    yield fork(metaCategory, payload);
  }
}

export default [
  fork(watchFetchAlarmScreenList),
  fork(watchFetchAlarmList),
  // fork(watchFetchConfirm),
  // fork(watchFetchBatchConfirm),
  // fork(watchFetchAlarmDelete),
  // fork(watchFetchCreateConfirm),
  fork(watchCreateEventContent),
  fork(watchFetchAlarmDetail),
  fork(watchFetchAlarmConvergenceList),
  // fork(watchAlarmDetailInfo),
  fork(watchFetchCreateEventConfirm),
  fork(watchFetchEventTypeList),
  fork(watchFetchCustomerList),
  fork(watchFetchEventList),
  // fork(watchFetchMetaQueryAlarmType),
  fork(watchFetchMetaCategory),
];
