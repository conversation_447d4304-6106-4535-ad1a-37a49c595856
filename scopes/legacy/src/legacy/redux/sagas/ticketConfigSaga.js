import omit from 'lodash/omit';
import { call, fork, put, select, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';
import { ConfigRangeType } from '@manyun/ticket.model.task';
import {
  getMaintainConfigInfoTable,
  getMaintenanceContent,
  validate,
} from '@manyun/ticket.state.ticket';

import { redirectActionCreator } from '@manyun/dc-brain.legacy.redux/actions/routerActions';
import { taskCenterService } from '@manyun/dc-brain.legacy.services';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';

import {
  createMaintainConfigActionCreator,
  deleteMaintainConfigActionCreator,
  editMaintainConfigActionCreator,
  getMaintainConfigInfoActionCreator,
  getMaintainDataActionCreator,
  resetMaintainSearchValuesActionCreator,
  setMaintainPaginationThenGetDataActionCreator,
  ticketConfigActions,
} from '../actions/ticketConfigActions';
import {
  getCreateFields,
  getEditFields,
  getMaintainSearchValuesAndPagination,
} from '../selectors/ticketConfigSelector';

function getQ(pagination, searchValues, excludeSj) {
  const baseQ = { ...pagination };
  for (const [, { name, value }] of Object.entries(searchValues)) {
    const val = value === '' ? null : value;
    if (name) {
      if (name === 'configName' && val) {
        baseQ[name] = val.trim();
      } else if (name === 'creatorId' && val) {
        baseQ[name] = val.value;
      } else if (name === 'createTimeRange' && val && val.length) {
        baseQ.createStartTime = Number(val[0].unix() + '000');
        baseQ.createEndTime = Number(val[1].unix() + '000');
      } else if (name === 'deviceTypeList' && val) {
        baseQ.deviceTypeList = val;
      } else {
        baseQ[name] = val;
      }
    }
  }
  if (excludeSj !== undefined) {
    baseQ['excludeSj'] = excludeSj;
  }
  return baseQ.effectType === ConfigRangeType.All
    ? { ...baseQ, effectDomain: ConfigRangeType.All }
    : baseQ;
}

function* getMaintainData({ shouldResetPageNum = true, excludeSj, effectType } = {}) {
  yield put(ticketConfigActions.updateMaintainTableLoading(true));
  if (shouldResetPageNum) {
    yield put(ticketConfigActions.resetMaintainPageNum());
  }
  const { searchValues, pagination } = yield select(getMaintainSearchValuesAndPagination);
  const queryParams = getQ(pagination, searchValues, excludeSj);
  const { response, error } = yield call(taskCenterService.fetchMaintainConfigData, {
    ...queryParams,
    deviceType: queryParams?.deviceType?.thirdCategorycode,
    effectType,
  });
  if (error) {
    message.error(error);
    yield put(ticketConfigActions.failure());
  } else {
    yield put(ticketConfigActions.setMaintainDataAndTotal(response));
  }
}

function* resetMaintainSearchValues(effectType) {
  yield put(ticketConfigActions.resetMaintanceSearchValuesAndData());
  yield put(getMaintainDataActionCreator({ shouldResetPageNum: false, effectType }));
}

function* setMaintainPagination(newPagination) {
  yield put(ticketConfigActions.setMaintainPagination(newPagination));
  yield put(
    getMaintainDataActionCreator({
      shouldResetPageNum: false,
      excludeSj: newPagination.excludeSj,
      effectType: newPagination.effectType,
    })
  );
}

function* deleteMaintainConfig({ maintenanceConfigId, effectType }) {
  const { error } = yield call(taskCenterService.fetchDeleteMaintenanceConfig, {
    maintenanceConfigId,
  });
  if (error) {
    message.error(error);
  } else {
    message.success('删除成功！');
    yield put(getMaintainDataActionCreator({ shouldResetPageNum: false, effectType }));
  }
}

function* createMaintainConfig(payload) {
  if (payload.mode === 'new') {
    const { create, tools, securityStds, maintenanceItems } = yield select(getCreateFields);
    if (!validate(tools, securityStds, maintenanceItems)) {
      return;
    }
    const formFields = getQ({}, create);
    const table = getMaintenanceContent(tools, securityStds, maintenanceItems);
    const { error } = yield call(taskCenterService.fetchCreateMaintainConfig, {
      ...omit(formFields, 'deviceTypeList'),
      deviceTypeInfoList: payload.deviceTypeList
        ? payload.deviceTypeList
        : formFields.deviceTypeList.map(item => {
            return {
              deviceType: item.thirdCategorycode,
              deviceTypeName: item.thirdCategoryName,
              ...item,
            };
          }),
      ...table,
    });
    if (error) {
      message.error(error);
    } else {
      message.success('新建成功！');
      yield put(redirectActionCreator(urls.generateMaintainConfigListLocation()));
    }
  } else {
    const { edit, tools, securityStds, maintenanceItems } = yield select(getEditFields);
    if (!validate(tools, securityStds, maintenanceItems)) {
      return;
    }
    const formFields = getQ({}, edit);
    const table = getMaintenanceContent(tools, securityStds, maintenanceItems);
    const { error } = yield call(taskCenterService.fetchCreateMaintainConfig, {
      ...omit(formFields, 'deviceTypeList'),
      deviceTypeInfoList: payload.deviceTypeList
        ? payload.deviceTypeList
        : formFields.deviceTypeList.map(item => {
            return {
              deviceType: item.thirdCategorycode,
              deviceTypeName: item.thirdCategoryName,
              ...item,
            };
          }),
      ...table,
    });
    if (error) {
      message.error(error);
    } else {
      message.success('复制成功！');
      yield put(redirectActionCreator(urls.generateMaintainConfigListLocation()));
    }
  }
}

function* editMaintainConfig(payload) {
  const { edit, tools, securityStds, maintenanceItems } = yield select(getEditFields);
  if (!validate(tools, securityStds, maintenanceItems)) {
    return;
  }
  const formFields = getQ({}, edit);
  const table = getMaintenanceContent(tools, securityStds, maintenanceItems);
  const { error } = yield call(taskCenterService.fetchEditMaintainConfig, {
    ...omit(formFields, 'deviceTypeList'),
    deviceTypeInfoList: payload.deviceTypeList
      ? payload.deviceTypeList
      : formFields.deviceTypeList.map(item => {
          return {
            deviceType: item.thirdCategorycode,
            deviceTypeName: item.thirdCategoryName,
            ...item,
          };
        }),
    ...table,
  });
  if (error) {
    message.error(error);
  } else {
    message.success('编辑成功！');
    yield put(redirectActionCreator(urls.generateMaintainConfigListLocation()));
  }
}

function* getMaintainConfigInfo(payload) {
  const { error, response } = yield call(taskCenterService.fetchMaintenanceConfigInfo, payload.id);
  if (error) {
    message.error(error);
  } else {
    const tables = getMaintainConfigInfoTable(
      response.maintenanceContent.tools,
      response.maintenanceContent.securityStds,
      response.maintenanceContent.maintenanceItems
    );
    if (payload.mode === 'detail') {
      const newData = {
        basic: {
          configName: response.configName,
          creatorName: response.creatorName,
          creatorId: response.creatorId,
          gmtCreate: response.gmtCreate,
          id: response.id,
          maintenanceType: response.maintenanceType,
          deviceTypeList: response.deviceTypeInfoList,
        },
        ...tables,
      };
      yield put(ticketConfigActions.setMaintenanceConfigInfos(newData));
    }

    if (payload.mode === 'edit' || payload.mode === 'copy') {
      if (payload.setDeviceTypeList) {
        payload.setDeviceTypeList(
          response.deviceTypeInfoList.map(i => ({
            ...i,
            numbered: payload.getFieldValue === 'M_SJ' ? undefined : true,
          }))
        );
      }
      const params = {
        basic: {
          effectType: {
            value: response.effectType,
            name: 'effectType',
          },
          effectDomain: {
            value: response.effectType === ConfigRangeType.All ? undefined : response.effectDomain,
            name: 'effectDomain',
          },
          configName: {
            value: response.configName,
            name: 'configName',
          },
          maintenanceType: {
            value: response.maintenanceType,
            name: 'maintenanceType',
          },
          deviceTypeList: {
            value: payload.setDeviceTypeList
              ? response.deviceTypeInfoList.map(i => i.deviceType)
              : response.deviceTypeInfoList.map(item => {
                  return {
                    ...item,
                    thirdCategorycode: item.deviceType,
                    thirdCategoryName: item.deviceTypeName,
                  };
                }),
            name: 'deviceTypeList',
          },
          maintenanceConfigId: {
            value: response.id,
            name: 'maintenanceConfigId',
          },
        },
        ...tables,
      };
      yield put(
        ticketConfigActions.upDateMaintenanceEditOptionValues({
          basic: params.basic,
          tools: {
            tableData: params.tools,
            editingRowKey: null,
            deleteByCancel: false,
          },
          securityStds: {
            tableData: params.securityStds,
            editingRowKey: null,
            deleteByCancel: false,
          },
          maintenanceItems: {
            tableData: params.maintenanceItems,
            editingRowKey: null,
            deleteByCancel: false,
          },
        })
      );
    }
  }
}

/******************************************************************************/
/******************************* WATCHERS *************************************/
/******************************************************************************/

function* watchGetMaintainDataAction() {
  while (true) {
    const { payload } = yield take(getMaintainDataActionCreator.type);
    yield fork(getMaintainData, payload);
  }
}

function* watchResetMaintainSearchValues() {
  while (true) {
    const { payload } = yield take(resetMaintainSearchValuesActionCreator.type);
    yield fork(resetMaintainSearchValues, payload);
  }
}

function* watchSetMaintainPagination() {
  while (true) {
    const { payload } = yield take(setMaintainPaginationThenGetDataActionCreator.type);
    yield fork(setMaintainPagination, payload);
  }
}

function* watchDeleteMaintainConfig() {
  while (true) {
    const { payload } = yield take(deleteMaintainConfigActionCreator.type);
    yield fork(deleteMaintainConfig, payload);
  }
}

function* watchCreateMaintainConfig() {
  while (true) {
    const { payload } = yield take(createMaintainConfigActionCreator.type);
    yield fork(createMaintainConfig, payload);
  }
}

function* watchEditMaintainConfig() {
  while (true) {
    const { payload } = yield take(editMaintainConfigActionCreator.type);
    yield fork(editMaintainConfig, payload);
  }
}

function* watchGetMaintainConfigInfo() {
  while (true) {
    const { payload } = yield take(getMaintainConfigInfoActionCreator.type);
    yield fork(getMaintainConfigInfo, payload);
  }
}

export default [
  fork(watchGetMaintainDataAction),
  fork(watchResetMaintainSearchValues),
  fork(watchSetMaintainPagination),
  fork(watchDeleteMaintainConfig),
  fork(watchCreateMaintainConfig),
  fork(watchEditMaintainConfig),
  fork(watchGetMaintainConfigInfo),
];
