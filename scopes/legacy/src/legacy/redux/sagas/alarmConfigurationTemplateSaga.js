import { push } from 'connected-react-router';
import { cloneDeep } from 'lodash';
import trim from 'lodash/trim';
import { call, fork, put, select, take } from 'redux-saga/effects';
import shortid from 'shortid';

import { message } from '@manyun/base-ui.ui.message';

import { CUSTOMIZED_NOTIFICATION_TEXT } from '@manyun/dc-brain.legacy.constants';
import { LIMIT_VALUE_MAP } from '@manyun/dc-brain.legacy.constants/alarm';
import { POINT_DATA_TYPE_CODE_MAP } from '@manyun/dc-brain.legacy.constants/point';
import * as urls from '@manyun/dc-brain.legacy.constants/urls';
import { getLimitsByNotifyRule } from '@manyun/dc-brain.legacy.pages/monitor-item-config/utils';
import { alarmConfigService, opsService, treeDataService } from '@manyun/dc-brain.legacy.services';

import { alarmActions } from '../actions/alarmActions';
import {
  alarmConfigurationTemplateActions,
  changeAvailableStatus,
  deleteNotificationTplFieldAction,
  deleteTemplateAction,
  fetchAlarmConfigurationTree,
  fetchAssociatedItem,
  fetchTemplateList,
  getAlarmConfigurationLastTree,
  getAlarmConfigurationTree,
  getAllTemplateGroupList,
  getAssociatedGroupList,
  getEffectiveDomainList,
  insertNotificationTplFieldAction,
  monitorGroupAdd,
  monitorGroupAssociateScheme,
  monitorGroupUpdate,
  pasteNoticifyAction,
  removeAssociate,
  resetNotificationTplAction,
  reviewAlarmNoticifyAction,
  saveEditCustomTemplateModuleAction,
  saveEditTemplateModuleAction,
  searchEventTypeAction,
  searchTemplatePointConfigAction,
  submitEditCustomTemplateConfigAction,
  submitEditTemplateConfigAction,
  submitNewCustomTemplateConfigAction,
  submitNewTemplateConfigAction,
  updateNotificationTplAction,
} from '../actions/alarmConfigurationTemplateActions';
import {
  getFormStep,
  getNewTemplateMess,
  getNotificationFields,
  getNotificationTpl,
  getPointIndevice,
  getSearchValues,
  getSingleNotificationTpl,
} from './../selectors/templateAlarmConfigSelectors';

function* treeData() {
  const { response, error } = yield call(alarmConfigService.getAlarmConfigurationTree);
  if (response) {
    yield put(
      alarmConfigurationTemplateActions.fetchAlarmConfigurationTreeSuccess({
        tree: response.data,
        treeDataList: loop(response.data),
      })
    );
  } else {
    message.error(error || '告警配置规则请求失败');
  }
}

function loop(data) {
  const dataList = [];
  function generateList(data) {
    for (let i = 0; i < data.length; i++) {
      const node = data[i];
      const { metaCode, metaType, metaName } = node;
      dataList.push({ key: metaCode + '-' + metaType, title: metaName });
      if (node.children) {
        generateList(node.children);
      }
    }
  }
  generateList(data);

  return dataList;
}

function* alarmConfigurationLastTree(deviceType, itemType, alarmConfigurationTree) {
  const { response, error } = yield call(alarmConfigService.getAlarmConfigurationLastTree, {
    deviceType,
    itemType,
  });
  if (response) {
    const lastList = response.data.map(item => {
      return {
        ...item,
        metaName: item.name,
        metaCode: item.deviceType + item.pointCode + item.id,
        metaType: 'C4',
      };
    });
    const list = getChildren(alarmConfigurationTree, itemType.concat(deviceType), lastList);
    yield put(
      alarmConfigurationTemplateActions.fetchAlarmConfigurationTreeSuccess({
        tree: list,
        treeDataList: loop(list),
      })
    );
  } else {
    message.error(error || '告警规则配置项请求失败');
  }
}

function getChildren(alarmConfigurationTree, metaCode, response) {
  const list = alarmConfigurationTree.map((item, index) => {
    if (item.metaCode === metaCode) {
      return { ...item, children: [...response] };
    }
    if (item.children) {
      return { ...item, children: [...getChildren(item.children, metaCode, response)] };
    }
    return item;
  });
  return list;
}

function* allTemplateGroupList() {
  const { response, error } = yield call(alarmConfigService.getAllTemplateGroupList);
  if (response) {
    yield put(
      alarmConfigurationTemplateActions.templateListSuccess({
        allList: response.data,
        showList: response.data,
      })
    );
  } else {
    message.error(error || '请求待关联模板组失败');
  }
}

function* associatedGroupList(groupId) {
  const { response, error } = yield call(alarmConfigService.getAssociatedGroupList, { groupId });
  if (response) {
    yield put(
      alarmConfigurationTemplateActions.associatedGroupSuccess({
        allList: response.data,
        showList: response.data,
      })
    );
  } else {
    message.error(error || '查询已关联的模板组列表失败');
  }
}

function* monitorGroupAssociateSchemeConfirm(groupId, schemeIds, params) {
  yield put(alarmConfigurationTemplateActions.associatedTemplateGroupLoading());
  const { response, error } = yield call(alarmConfigService.monitorGroupAssociateScheme, {
    groupId,
    schemeIds,
  });
  if (response) {
    if (params) {
      yield fork(templateList, params);
    }
    message.success('关联模板组成功');
    yield fork(associatedGroupList, groupId);
    yield put(alarmConfigurationTemplateActions.associatedTemplateGroupLoading());
    yield put(
      alarmConfigurationTemplateActions.templateGroupInfo({
        activeKey: 'associated',
        id: groupId,
      })
    );
  } else {
    message.error(error || '模板组关联失败');
  }
}

function* removeAssociateConfirm(groupId, schemeId, params) {
  yield put(alarmConfigurationTemplateActions.associatedTemplateGroupLoading());
  const { response, error } = yield call(alarmConfigService.removeAssociateConfirm, {
    groupId,
    schemeId,
  });
  if (response) {
    message.success('取消关联模板组成功');
    // yield fork(associatedGroupList, groupId);
    if (params) {
      yield fork(templateList, params);
    }
    yield put(alarmConfigurationTemplateActions.showAssociatedTemplateGroup());
    yield put(alarmConfigurationTemplateActions.associatedTemplateGroupLoading());
  } else {
    message.error(error || '取消关联失败');
    yield put(alarmConfigurationTemplateActions.failure());
  }
}

function getQ(pagination, searchValues) {
  const baseQ = { ...pagination };
  for (const [, { name, value }] of Object.entries(searchValues)) {
    const val = value === '' ? null : value;
    if (name === 'deviceTypeList') {
      if (val?.length) {
        baseQ[name] = val;
      }
    } else if (name === 'lastOperatorName' && val) {
      baseQ[name] = val.label;
    } else {
      baseQ[name] = trim(val);
    }
  }
  return baseQ;
}

function* templateList({ pageNum, pageSize, initialSearchValues }) {
  yield put(alarmConfigurationTemplateActions.request());
  const { searchValues } = yield select(getSearchValues, initialSearchValues);
  const pagination = {
    pageNum,
    pageSize,
  };
  const q = getQ(pagination, searchValues);
  const { response: templateList, error } = yield call(alarmConfigService.fetchTemplateList, {
    ...q,
    deviceTypeList: undefined,
    targetList: q.deviceTypeList,
  });
  if (templateList) {
    yield put(
      alarmConfigurationTemplateActions.templateListPageSuccess({
        list: templateList.data,
        total: templateList.total,
        pageNum,
        pageSize,
        loading: false,
      })
    );
  } else {
    message.error(error || '告警模板列表请求失败');
    yield put(alarmConfigurationTemplateActions.failure());
  }
}

function* associatedItem({ groupId, mode }) {
  let associatedItem;
  let error;

  if (mode === 'customView') {
    const { response: customRes, error: customError } = yield call(
      alarmConfigService.fetchCustomAssociatedItem,
      { id: groupId }
    );
    associatedItem = customRes;
    error = customError;
  } else {
    const { response, error: _error } = yield call(alarmConfigService.fetchAssociatedItem, {
      groupId,
    });
    associatedItem = response;
    error = _error;
  }
  if (associatedItem) {
    yield put(
      alarmConfigurationTemplateActions.alarmTemplateList({
        list: associatedItem.data,
      })
    );
    yield put(alarmConfigurationTemplateActions.showAlarmTemplateList(associatedItem.data));
  } else {
    message.error(error || '关联的告警项请求失败');
  }
}

function* alarmConfigurationTree() {
  const { normalizedList } = yield call(treeDataService.getAlarmConfigurationTree);
  if (normalizedList) {
    yield put(alarmConfigurationTemplateActions.alarmConfiguration(normalizedList));
  }
}

function* availableStatus(payload) {
  yield put(alarmConfigurationTemplateActions.request());
  const { response, error } = yield call(alarmConfigService.availableStatus, {
    available: payload.available,
    groupIds: payload.groupIds,
  });
  if (response) {
    yield fork(templateList, payload.params);
  } else {
    message.error(error || '更改启用状态失败');
    yield put(alarmConfigurationTemplateActions.failure());
  }
}

function* update(payload) {
  yield put(alarmConfigurationTemplateActions.onEidtOrNewLoading());
  const { response, error } = yield call(alarmConfigService.monitorGroupUpdate, payload);
  if (response) {
    message.success('修改模板成功');
    yield put(alarmConfigurationTemplateActions.onEidtOrNewLoading());
    yield put(push(urls.ALARM_CONFIGURATION_TEMPLATE_LIST));
  } else {
    message.error(error || '修改模板失败');
    yield put(alarmConfigurationTemplateActions.onEidtOrNewLoading());
  }
}

function* add(payload) {
  yield put(alarmConfigurationTemplateActions.onEidtOrNewLoading());
  const { response, error } = yield call(alarmConfigService.monitorGroupAdd, payload);
  if (response) {
    message.success('新建模板成功');
    yield put(alarmConfigurationTemplateActions.onEidtOrNewLoading());
    yield put(push(urls.ALARM_CONFIGURATION_TEMPLATE_LIST));
  } else {
    message.error(error || '新建模板失败');
    yield put(alarmConfigurationTemplateActions.onEidtOrNewLoading());
  }
}

function* effectiveDomainList(groupId) {
  const { response, error } = yield call(alarmConfigService.getEffectiveDomainList, { groupId });
  if (response) {
    yield put(
      alarmConfigurationTemplateActions.effectiveDomainListSuccess({
        showList: response.data,
        allList: response.data,
      })
    );
  } else {
    message.error(error || '生效域列表请求失败');
  }
}

function* insertNotificationTplField({ field, destinationIndex }) {
  if (field.notifyConfigMode === 'GLOBAL') {
    const prevTpl = yield select(getNotificationTpl);
    const rule = prevTpl.notifyRule.value ? prevTpl.notifyRule.value : [];

    const ruleCode = getNotifyRule([...rule, field]);
    if (ruleCode.length > 500) {
      message.error('文案输入字符数超长，请删减输入的文案字符');
      return;
    }
    let newRule = cloneDeep(rule);
    if (!newRule[destinationIndex]) {
      newRule = [...newRule, field];
    } else {
      newRule.splice(destinationIndex, 0, field);
    }
    yield put(alarmConfigurationTemplateActions.setGlobalNotificationTpl(newRule));
  } else {
    const { monitorItems, monitorItemMap } = yield select(getSingleNotificationTpl);
    const { pointId } = field;
    let notifyRule;
    if (monitorItemMap[pointId].notifyRule.value) {
      notifyRule = cloneDeep(monitorItemMap[pointId].notifyRule.value);
      if (!notifyRule[destinationIndex]) {
        notifyRule = [...notifyRule, field];
      } else {
        notifyRule.splice(destinationIndex, 0, field);
      }
    } else {
      notifyRule = cloneDeep(monitorItemMap[pointId].notifyRule);
      if (!notifyRule[destinationIndex]) {
        notifyRule = [...notifyRule, field];
      } else {
        notifyRule = notifyRule.splice(destinationIndex, 0, field);
      }
    }
    const ruleCode = getNotifyRule(notifyRule);
    if (ruleCode.length > 500) {
      message.error('文案输入字符数超长，请删减输入的文案字符');
      return;
    }

    const pointItem = monitorItems.map(item => {
      if (item.id === pointId || item.code === pointId) {
        const temp = item.notifyRule.value ? item.notifyRule.value : item.notifyRule;
        return {
          ...item,
          notifyRule: {
            ...item.notifyRule,
            value: [...temp, field],
          },
        };
      } else {
        return item;
      }
    });

    const newItemMap = {
      ...monitorItemMap,
      [pointId]: {
        ...monitorItemMap[pointId],
        notifyRule: {
          value: notifyRule,
        },
      },
    };
    yield put(
      alarmConfigurationTemplateActions.setSingleNotificationTpl({ pointItem, newItemMap })
    );
  }
}

function* deleteNotificationTplField(field) {
  if (field.notifyConfigMode === 'GLOBAL') {
    const prevTpl = yield select(getNotificationTpl);
    const newTpl = prevTpl.notifyRule.value.filter(({ id }) => id !== field.id);
    yield put(alarmConfigurationTemplateActions.setGlobalNotificationTpl(newTpl));
  } else {
    const { monitorItems, monitorItemMap } = yield select(getSingleNotificationTpl);
    const { pointId } = field;
    const notify = monitorItemMap[pointId].notifyRule.value
      ? monitorItemMap[pointId].notifyRule.value
      : monitorItemMap[pointId].notifyRule;
    const singlePointNotify = notify.filter(txt => txt.id !== field.id);
    const newItemMap = {
      ...monitorItemMap,
      [pointId]: {
        ...monitorItemMap[pointId],
        notifyRule: {
          value: singlePointNotify,
        },
      },
    };
    const pointItem = monitorItems.map(item => {
      if (item.id === pointId || item.code === pointId) {
        const notify = item.notifyRule.value ? item.notifyRule.value : item.notifyRule;
        const notice = notify.filter(txt => txt.id !== field.id);
        return {
          ...item,
          notifyRule: {
            value: notice,
          },
        };
      } else {
        return item;
      }
    });

    yield put(
      alarmConfigurationTemplateActions.setSingleNotificationTpl({ pointItem, newItemMap })
    );
  }
}

function* updateNotificationTpl({ fieldId, attrs, notifyConfigMode, pointId }) {
  if (notifyConfigMode === 'GLOBAL') {
    const prevTpl = yield select(getNotificationTpl);
    const newTpl = prevTpl.notifyRule.value.map(({ id, ...rest }) => {
      if (id === fieldId) {
        return {
          id,
          ...rest,
          ...attrs,
        };
      }
      return {
        id,
        ...rest,
      };
    });
    yield put(alarmConfigurationTemplateActions.setGlobalNotificationTpl(newTpl));
  } else {
    const { monitorItems, monitorItemMap } = yield select(getSingleNotificationTpl);
    const notifyRule = monitorItemMap[pointId].notifyRule.value
      ? monitorItemMap[pointId].notifyRule.value
      : monitorItemMap[pointId].notifyRule;
    const itemNotify = notifyRule.map(({ id, ...rest }) => {
      if (id === fieldId) {
        return {
          id,
          ...rest,
          ...attrs,
        };
      }
      return {
        id,
        ...rest,
      };
    });
    const newItemMap = {
      ...monitorItemMap,
      [pointId]: {
        ...monitorItemMap[pointId],
        notifyRule: {
          value: itemNotify,
        },
      },
    };
    const pointItem = monitorItems.map(item => {
      if (item.id === pointId || item.code === pointId) {
        const itemNotify = item.notifyRule.value.map(({ id, ...rest }) => {
          if (id === fieldId) {
            return {
              id,
              ...rest,
              ...attrs,
            };
          }
          return {
            id,
            ...rest,
          };
        });
        return {
          ...item,
          notifyRule: {
            value: itemNotify,
          },
        };
      } else {
        return item;
      }
    });
    yield put(
      alarmConfigurationTemplateActions.setSingleNotificationTpl({ pointItem, newItemMap })
    );
  }
}

function getTriggerRule(dataType, { lowerLimit, upperLimit, normalLimits = [] }) {
  let rule = '';
  if (dataType === POINT_DATA_TYPE_CODE_MAP.AI) {
    if (lowerLimit.operator !== LIMIT_VALUE_MAP.NULL) {
      rule += lowerLimit.operator + '=';
      if (!lowerLimit.limitCode || lowerLimit.limitCode === 'USER_DEFINED') {
        rule += String(lowerLimit.limit);
      } else {
        rule += lowerLimit.limitCode;
      }
    }
    if (upperLimit.operator !== LIMIT_VALUE_MAP.NULL) {
      if (rule !== '') {
        rule += ';';
      }
      rule += upperLimit.operator + '=';
      if (!upperLimit.limitCode || upperLimit.limitCode === 'USER_DEFINED') {
        rule += String(upperLimit.limit);
      } else {
        rule += upperLimit.limitCode;
      }
    }
  }
  if (dataType === POINT_DATA_TYPE_CODE_MAP.DI) {
    rule = 'in=' + normalLimits.join(',');
  }

  return rule;
}

function getNotifyRule(notificationTpl) {
  return notificationTpl
    .map(({ value, text }) => {
      if (value === CUSTOMIZED_NOTIFICATION_TEXT) {
        return text;
      }
      return '${' + value + '}';
    })
    .join('');
}

function* submitNewTemplateConfig({ successCb, failedCb }) {
  const {
    notifyConfigMode,
    module,
    monitorItemIds,
    ruleTimeForm,
    globalNotificationTpl,
    monitorItemMap,
  } = yield select(getNewTemplateMess);

  const pointIndevice = yield select(getPointIndevice);
  const current = yield select(getFormStep);

  const { name, deviceType, description, available, schemeIds } = module;
  const device_Type = deviceType.value.code;
  let monitorItemList = [];
  let notifyRule;
  monitorItemIds.forEach(id => {
    const item = monitorItemMap[id];
    if (notifyConfigMode === 'GLOBAL') {
      notifyRule = getNotifyRule(globalNotificationTpl.notifyRule.value);
    } else {
      const notificationTpl = item.notifyRule.value ? item.notifyRule.value : item.notifyRule;
      notifyRule = getNotifyRule(notificationTpl);
    }
    const point = item.name.value.split('_$$_');
    const singlePointInfo = pointIndevice.filter(({ pointCode }) => {
      if ((point.length === 6 && pointCode === point[0]) || item.pointCode === pointCode) {
        return true;
      } else {
        return false;
      }
    });
    let code = POINT_DATA_TYPE_CODE_MAP.AI;
    if (point.length === 6) {
      code = point[4];
    } else {
      if (singlePointInfo[0] && singlePointInfo[0].dataType) {
        code = singlePointInfo[0].dataType.code;
      }
    }
    const lowerLimit = item.lowerLimit.value;
    const upperLimit = item.upperLimit.value;
    const normalLimits = item.normalLimits.value;

    const triggerRule = getTriggerRule(code, { lowerLimit, upperLimit, normalLimits });
    const temp = {
      name:
        singlePointInfo[0] && singlePointInfo[0].pointType
          ? `${singlePointInfo[0].pointType.name}-${singlePointInfo[0].name}`
          : null,
      alarmLevel: item.alarmLevel.value,
      alarmType: item.alarmType.value,
      available: item.available.value,
      createIncident: item.createIncident.value,
      eventTopCategory: item.incidentType.value[0],
      eventSecondCategory: item.incidentType.value[1],
      pointCode: singlePointInfo[0] ? singlePointInfo[0].pointCode : null,
      deviceType: device_Type,
      triggerCount: item.triggerCount.value,
      triggerRule,
      recoverInterval: ruleTimeForm.recoverInterval.value,
      notifyRule,
    };
    monitorItemList = [...monitorItemList, temp];
  });
  // const monitorSchemeList = schemeIds.value.map(id => {
  //   return {
  //     id,
  //     available: available.value,
  //   };
  // });
  const params = {
    name: name.value,
    deviceType: device_Type,
    description: description.value,
    available: available.value,
    monitorSchemeIds: schemeIds.value,
    monitorItemList,
  };
  const { response, error } = yield call(alarmConfigService.monitorGroupAdd, params);
  if (response) {
    yield put(alarmConfigurationTemplateActions.saveResultData(response));
    yield put(alarmConfigurationTemplateActions.changeCurrent({ current: current + 1 }));
    message.success('新建模板成功');
    successCb();
  } else {
    message.error(error);
    failedCb();
  }
}

function* submitNewCustomTemplateConfig({ callback }) {
  const {
    notifyConfigMode,
    module,
    monitorItemIds,
    ruleTimeForm,
    globalNotificationTpl,
    monitorItemMap,
  } = yield select(getNewTemplateMess);

  const pointIndevice = yield select(getPointIndevice);
  const current = yield select(getFormStep);

  const { name, description, available, idcTag } = module;
  // const device_Type = deviceType.value.code; // TODO
  let monitorItemList = [];
  let notifyRule;
  monitorItemIds.forEach(id => {
    const item = monitorItemMap[id];
    if (notifyConfigMode === 'GLOBAL') {
      notifyRule = getNotifyRule(globalNotificationTpl.notifyRule.value);
    } else {
      const notificationTpl = item.notifyRule.value ? item.notifyRule.value : item.notifyRule;
      notifyRule = getNotifyRule(notificationTpl);
    }
    const point = item.name.value.split('_$$_');
    const singlePointInfo = pointIndevice.filter(({ pointCode, deviceType }) => {
      if (
        (point.length === 7 && `${deviceType}${pointCode}` === `${point[1]}${point[0]}`) ||
        `${item.deviceType}${item.pointCode}` === `${deviceType}${pointCode}`
      ) {
        return true;
      } else {
        return false;
      }
    });
    let code = POINT_DATA_TYPE_CODE_MAP.AI;
    if (point.length === 7) {
      code = point[4];
    } else {
      if (singlePointInfo[0] && singlePointInfo[0].dataType) {
        code = singlePointInfo[0].dataType.code;
      }
    }
    const lowerLimit = item.lowerLimit.value;
    const upperLimit = item.upperLimit.value;
    const normalLimits = item.normalLimits.value;
    const triggerRule = getTriggerRule(code, { lowerLimit, upperLimit, normalLimits });
    const [, deviceType, , , , , spaceGuid] = item.name.value.split('_$$_');
    const temp = {
      name:
        singlePointInfo[0] && singlePointInfo[0].pointType
          ? `${singlePointInfo[0].pointType.name}-${singlePointInfo[0].name}`
          : null,
      alarmLevel: item.alarmLevel.value,
      alarmType: item.alarmType.value,
      available: item.available.value,
      createIncident: item.createIncident.value,
      eventTopCategory: item.incidentType.value[0],
      eventSecondCategory: item.incidentType.value[1],
      pointCode: singlePointInfo[0] ? singlePointInfo[0].pointCode : null,
      deviceType: deviceType,
      triggerCount: item.triggerCount.value,
      triggerRule,
      recoverInterval: ruleTimeForm.recoverInterval.value,
      notifyRule,
      spaceGuid,
    };
    monitorItemList = [...monitorItemList, temp];
  });
  // const monitorSchemeList = schemeIds.value.map(id => {
  //   return {
  //     id,
  //     available: available.value,
  //   };
  // });
  const params = {
    name: name.value,
    idcTag: idcTag.value.join('.'),
    description: description.value,
    available: available.value,
    monitorItemList,
  };
  const { response, error } = yield call(alarmConfigService.createCustomAlarm, params);
  callback();
  if (response) {
    yield put(alarmConfigurationTemplateActions.saveResultData(response));
    yield put(alarmConfigurationTemplateActions.changeCurrent({ current: current + 1 }));
    message.success('新建自定义告警成功');
  } else {
    message.error(error || '新建自定义告警失败');
  }
}

function* resetNotificationTpl(pointId) {
  const { monitorItems, monitorItemMap } = yield select(getSingleNotificationTpl);

  const newItemMap = {
    ...monitorItemMap,
    [pointId]: {
      ...monitorItemMap[pointId],
      notifyRule: [],
    },
  };
  const pointItem = monitorItems.map(item => {
    if (item.id === pointId) {
      return {
        ...item,
        notifyRule: [],
      };
    } else {
      return item;
    }
  });
  yield put(alarmConfigurationTemplateActions.setSingleNotificationTpl({ pointItem, newItemMap }));
}

function* notificationTxt2Fields(notifyRule) {
  const notificationFields = yield select(getNotificationFields);
  const fields = [notifyRule];
  while (fields.some(item => typeof item === 'string')) {
    for (let idx = 0; idx < fields.length; idx++) {
      const item = fields[idx];
      if (typeof item !== 'string') {
        continue;
      }
      let shouldBreak = false;

      for (let index = 0; index < notificationFields.length; index++) {
        const field = notificationFields[index];
        const keyword = '${' + field.metaCode + '}';
        if (item.includes(keyword)) {
          const id = shortid();
          const tplItem = {
            id,
            label: field.metaName,
            value: field.metaCode,
            description: field.description,
          };
          shouldBreak = true;
          const keywordIdx = item.indexOf(keyword);
          const left = item.slice(0, keywordIdx);
          const right = item.slice(keywordIdx + keyword.length);
          if (!left && right) {
            fields.splice(idx, 1, tplItem, right);
          } else if (left && !right) {
            fields.splice(idx, 1, left, tplItem);
          } else if (!left && !right) {
            fields.splice(idx, 1, tplItem);
          } else {
            fields.splice(idx, 1, left, tplItem, right);
          }
          break;
        }
      }
      if (!shouldBreak) {
        fields.splice(idx, 1, {
          id: shortid(),
          label: '自定义',
          value: CUSTOMIZED_NOTIFICATION_TEXT,
          text: item,
          description: '自定义配置告警内容',
        });
      }
      break;
    }
  }

  return fields;
}

function* searchTemplatePointConfig({ groupId, mode = '' }) {
  let response;
  let error;

  if (mode === 'custom') {
    const customData = yield call(alarmConfigService.fetchCustomAssociatedItem, {
      id: groupId,
    });
    response = customData.response;
    error = customData.reeor;
  } else {
    const data = yield call(alarmConfigService.fetchAssociatedItem, { groupId });
    response = data.response;
    error = data.reeor;
  }
  if (response) {
    const data = response.data;
    const newIds = data.map(({ id }) => id);
    const newMap = {};
    let newItems = [];
    let recoverInterval;
    const globalConfig = { notifyRule: { value: [] } };
    let firstNotify = [];

    for (let i = 0; i < newIds.length; i++) {
      const fields = yield notificationTxt2Fields(data[i].notifyRule);
      if (newIds.length === 1) {
        globalConfig.notifyRule.value = fields;
      } else if (i === 0) {
        firstNotify = data[i].notifyRule;
      } else {
        if (firstNotify === data[i].notifyRule) {
          globalConfig.notifyRule.value = fields;
        }
      }

      const { triggerRule } = data[i];
      const { lowerLimit, upperLimit, normalLimits } = getLimitsByNotifyRule(triggerRule);
      newMap[data[i].id] = {
        code: data[i].id,
        name: {
          value: data[i].name,
          dirty: false,
        },
        alarmLevel: {
          value: data[i].alarmLevel,
          dirty: false,
        },
        alarmType: {
          value: data[i].alarmType ? data[i].alarmType.code : null,
          dirty: false,
        },
        available: {
          value: data[i].available.toString(),
          dirty: false,
        },
        createIncident: {
          value: data[i].createIncident.toString(),
          dirty: false,
        },
        incidentType: {
          // value: data[i].incidentType,
          value:
            data[i].eventTopCategory && data[i].eventSecondCategory
              ? [String(data[i].eventTopCategory), String(data[i].eventSecondCategory)]
              : [],
          dirty: false,
        },
        triggerCount: {
          value: data[i].triggerCount,
          dirty: false,
        },
        lowerLimit: {
          value: lowerLimit
            ? lowerLimit
            : { limit: null, limitCode: 'USER_DEFINED', operator: 'gt' },
          dirty: false,
        },
        upperLimit: {
          value: upperLimit
            ? upperLimit
            : { limit: null, limitCode: 'USER_DEFINED', operator: 'lt' },
          dirty: false,
        },
        normalLimits: {
          value: normalLimits,
          dirty: false,
        },
        notifyRule: {
          value: fields,
          dirty: false,
        },
        validLimits: data[i].validLimits,
        pointCode: data[i].pointCode,
        deviceType: data[i].deviceType,
        spaceGuid: data[i].spaceGuid,
      };
      newItems = [
        ...newItems,
        {
          code: data[i].id,
          name: {
            value: data[i].name,
            dirty: false,
          },
          alarmLevel: {
            value: data[i].alarmLevel,
            dirty: false,
          },
          alarmType: {
            value: data[i].alarmType ? data[i].alarmType.code : null,
            dirty: false,
          },
          available: {
            value: data[i].available.toString(),
            dirty: false,
          },
          createIncident: {
            value: data[i].createIncident.toString(),
            dirty: false,
          },
          incidentType: {
            value:
              data[i].eventTopCategory && data[i].eventSecondCategory
                ? [String(data[i].eventTopCategory), String(data[i].eventSecondCategory)]
                : [],
            dirty: false,
          },
          triggerCount: {
            value: data[i].triggerCount,
            dirty: false,
          },
          lowerLimit: {
            value: lowerLimit
              ? lowerLimit
              : { limit: null, limitCode: 'USER_DEFINED', operator: 'gt' },
            dirty: false,
          },
          upperLimit: {
            value: upperLimit
              ? upperLimit
              : { limit: null, limitCode: 'USER_DEFINED', operator: 'lt' },
            dirty: false,
          },
          normalLimits: {
            value: normalLimits,
            dirty: false,
          },
          notifyRule: {
            value: fields,
            dirty: false,
          },
          validLimits: data[i].validLimits,
          pointCode: data[i].pointCode,
          deviceType: data[i].deviceType,
          spaceGuid: data[i].spaceGuid,
        },
      ];
      recoverInterval = data[i].recoverInterval;
    }
    yield put(
      alarmConfigurationTemplateActions.saveAssociatedItems({
        data,
        newIds,
        newMap,
        newItems,
        recoverInterval,
        globalConfig,
      })
    );
    yield put(alarmConfigurationTemplateActions.savePintConfigInLine(newMap));
  } else {
    message.error(error);
  }
}

function* getNotificationTxtTplFields() {
  const { response, error } = yield call(opsService.fetchOpsServiceList, {
    topCategory: 'ALARM_TOP_NOTIFY',
    secondCategory: 'ALARM_SECOND_NOTIFY',
  });
  if (response) {
    const physicalFields = response.data.filter(item => item.metaCode === 'PHY_MESSAGE')[0]
      .children;
    const faultedFields = response.data.filter(item => item.metaCode === 'FAULT_MESSAGE')[0]
      .children;
    const otherFields = response.data.filter(item => item.metaCode === 'OTHER_MESSAGE')[0].children;
    yield put(
      alarmActions.setNotificationTxtTplFields({ physicalFields, faultedFields, otherFields })
    );
  } else {
    message.error(error || '告警文案请求失败');
  }
}

function* reviewAlarmNoticify(payload) {
  const fields = yield notificationTxt2Fields(payload);
  yield put(alarmConfigurationTemplateActions.reviewAlarmNoticifyData(fields));
}

function* saveEditTemplateModule(payload) {
  const { module } = yield select(getNewTemplateMess);
  const current = yield select(getFormStep);
  const list = {
    id: payload,
    name: module.name.value,
    deviceType: module.deviceType.value.code,
    available: module.available.value,
    monitorSchemeIds: module.schemeIds.value,
    description: module.description.value,
  };
  const { response, error } = yield call(alarmConfigService.fetchEditTemplateModule, list);
  if (response) {
    yield put(alarmConfigurationTemplateActions.saveEditTemplateModuleResult(response));
    yield put(alarmConfigurationTemplateActions.changeCurrent({ current: current + 1 }));
    message.success('保存模板信息成功');
  } else {
    message.error(error);
  }
}

function* saveEditCustomTemplateModule(payload) {
  const { module } = yield select(getNewTemplateMess);
  const current = yield select(getFormStep);
  const list = {
    id: payload,
    name: module.name.value,
    available: module.available.value,
    description: module.description.value,
  };
  const { response, error } = yield call(alarmConfigService.fetchEditCustomTemplateModule, list);
  if (response) {
    yield put(alarmConfigurationTemplateActions.saveEditTemplateModuleResult(response));
    yield put(alarmConfigurationTemplateActions.changeCurrent({ current: current + 1 }));
    message.success('保存自定义告警信息成功');
  } else {
    message.error(error);
  }
}

function* submitEditTemplateConfig({ id, successCb, failedCb }) {
  const { notifyConfigMode, module, monitorItems, ruleTimeForm, globalNotificationTpl } =
    yield select(getNewTemplateMess);
  const current = yield select(getFormStep);
  const pointIndevice = yield select(getPointIndevice);

  const { deviceType } = module;

  const device_Type = deviceType.value.code;
  let monitorItemList = [];
  let notifyRule;
  for (let i = 0; i < monitorItems.length; i++) {
    const item = monitorItems[i];
    if (notifyConfigMode === 'GLOBAL') {
      const notificationTpl = globalNotificationTpl.notifyRule.value;
      notifyRule = getNotifyRule(notificationTpl);
    } else {
      const notificationTpl = item.notifyRule.value ? item.notifyRule.value : item.notifyRule;
      notifyRule = getNotifyRule(notificationTpl);
    }
    const tempCode = item.name.value ? item.name.value.split('_$$_') : [];
    let singlePointInfo;
    if (tempCode.length === 6) {
      singlePointInfo = pointIndevice.filter(({ pointCode }) => pointCode === tempCode[0]);
    } else {
      singlePointInfo = pointIndevice.filter(({ pointCode }) => pointCode === item.pointCode);
    }
    let code = POINT_DATA_TYPE_CODE_MAP.AI;
    if (singlePointInfo[0] && singlePointInfo[0].dataType) {
      code = singlePointInfo[0].dataType.code;
    } else {
      code = tempCode[4];
    }
    const lowerLimit = item.lowerLimit.value;
    const upperLimit = item.upperLimit.value;
    const normalLimits = item.normalLimits.value;
    const triggerRule = getTriggerRule(code, { lowerLimit, upperLimit, normalLimits });
    const pointName =
      singlePointInfo[0] && singlePointInfo[0].pointType
        ? `${singlePointInfo[0].pointType.name}-${singlePointInfo[0].name}`
        : item.name.value;
    const temp = {
      id: item.code ? item.code : null,
      name: pointName,
      alarmLevel: item.alarmLevel.value,
      alarmType: item.alarmType.value,
      available: item.available.value,
      createIncident: item.createIncident.value,
      eventTopCategory: item.incidentType.value[0] ? item.incidentType.value[0] : null,
      eventSecondCategory: item.incidentType.value[1] ? item.incidentType.value[1] : null,
      pointCode: singlePointInfo[0] ? singlePointInfo[0].pointCode : null,
      deviceType: device_Type,
      triggerCount: item.triggerCount.value,
      triggerRule,
      recoverInterval: ruleTimeForm.recoverInterval.value,
      notifyRule,
    };
    monitorItemList = [...monitorItemList, temp];
  }
  const params = {
    groupId: id,
    monitorItems: monitorItemList,
  };
  const { response, error } = yield call(alarmConfigService.fetchEditMonitorTtem, params);
  if (response) {
    yield put(alarmConfigurationTemplateActions.saveResultData(response));
    yield put(alarmConfigurationTemplateActions.changeCurrent({ current: current + 1 }));
    message.success('修改模板成功');
    successCb();
  } else {
    message.error(error);
    failedCb();
  }
}

function* submitEditCustomTemplateConfig({ id, callback }) {
  const { notifyConfigMode, monitorItems, ruleTimeForm, globalNotificationTpl } = yield select(
    getNewTemplateMess
  );
  const current = yield select(getFormStep);
  const pointIndevice = yield select(getPointIndevice);
  let monitorItemList = [];
  let notifyRule;
  for (let i = 0; i < monitorItems.length; i++) {
    const item = monitorItems[i];
    if (notifyConfigMode === 'GLOBAL') {
      const notificationTpl = globalNotificationTpl.notifyRule.value;
      notifyRule = getNotifyRule(notificationTpl);
    } else {
      const notificationTpl = item.notifyRule.value ? item.notifyRule.value : item.notifyRule;
      notifyRule = getNotifyRule(notificationTpl);
    }
    const tempCode = item.name.value ? item.name.value.split('_$$_') : [];
    let singlePointInfo;
    if (tempCode.length === 7) {
      singlePointInfo = pointIndevice.filter(
        ({ pointCode, deviceType }) =>
          `${deviceType}${pointCode}` === `${tempCode[1]}${tempCode[0]}`
      );
    } else {
      singlePointInfo = pointIndevice.filter(
        ({ pointCode, deviceType }) =>
          `${deviceType}${pointCode}` === `${item.deviceType}${item.pointCode}`
      );
    }

    let code = POINT_DATA_TYPE_CODE_MAP.AI;
    if (singlePointInfo[0] && singlePointInfo[0].dataType) {
      code = singlePointInfo[0].dataType.code;
    } else {
      code = tempCode[4];
    }
    const lowerLimit = item.lowerLimit.value;
    const upperLimit = item.upperLimit.value;
    const normalLimits = item.normalLimits.value;
    const triggerRule = getTriggerRule(code, { lowerLimit, upperLimit, normalLimits });
    const pointName =
      singlePointInfo[0] && singlePointInfo[0].pointType
        ? `${singlePointInfo[0].pointType.name}-${singlePointInfo[0].name}`
        : item.name.value;
    const temp = {
      id: item.code ? item.code : null,
      name: pointName,
      alarmLevel: item.alarmLevel.value,
      alarmType: item.alarmType.value,
      available: item.available.value,
      createIncident: item.createIncident.value,
      eventTopCategory: item.incidentType.value[0] ? item.incidentType.value[0] : null,
      eventSecondCategory: item.incidentType.value[1] ? item.incidentType.value[1] : null,
      pointCode: singlePointInfo[0] ? singlePointInfo[0].pointCode : null,
      deviceType: item.deviceType,
      triggerCount: item.triggerCount.value,
      triggerRule,
      recoverInterval: ruleTimeForm.recoverInterval.value,
      notifyRule,
      spaceGuid: item.spaceGuid,
    };
    monitorItemList = [...monitorItemList, temp];
  }
  const params = {
    customGroupId: id,
    monitorItems: monitorItemList,
  };
  const { response, error } = yield call(alarmConfigService.fetchEditCustomMonitorItem, params);
  callback();
  if (response) {
    yield put(alarmConfigurationTemplateActions.saveResultData(response));
    yield put(alarmConfigurationTemplateActions.changeCurrent({ current: current + 1 }));
    message.success('修改建模板成功');
  } else {
    message.error(error);
  }
}

function* pasteNoticify(payload) {
  const { copyNotificationTplData, monitorItems, monitorItemMap } = yield select(
    getNewTemplateMess
  );
  if (!copyNotificationTplData) {
    message.error('您还未复制内容，请先去复制');
    return;
  }
  if (payload.notifyConfigMode === 'GLOBAL') {
    yield put(alarmConfigurationTemplateActions.setGlobalNotificationTpl(copyNotificationTplData));
  } else {
    const pointItem = monitorItems.map(item => {
      if (item.id === payload.pointId || item.code === payload.pointId) {
        return {
          ...item,
          notifyRule: {
            value: copyNotificationTplData,
          },
        };
      } else {
        return item;
      }
    });

    const newItemMap = {
      ...monitorItemMap,
      [payload.pointId]: {
        ...monitorItemMap[payload.pointId],
        notifyRule: {
          value: copyNotificationTplData,
        },
      },
    };

    yield put(
      alarmConfigurationTemplateActions.setSingleNotificationTpl({ pointItem, newItemMap })
    );
  }
}

function* deleteTemplate(payload) {
  yield put(alarmConfigurationTemplateActions.request());
  const { response, error } = yield call(alarmConfigService.fetchDeleteMonitor, payload);
  if (response) {
    message.success('删除模板成功！');
    yield fork(templateList, { pageNum: 1, pageSize: 10 });
  } else {
    yield put(alarmConfigurationTemplateActions.failure());
    message.error(error);
  }
}

function* searchEventType() {
  const data = yield call(treeDataService.fetchEventType);
  if (data) {
    yield put(alarmConfigurationTemplateActions.saveEventTypeData(data));
  }
}

/******************************************************************************/
/******************************* WATCHERS *************************************/
/******************************************************************************/

// 告警配置树
function* watchAlarmConfigurationTree() {
  while (true) {
    yield take(getAlarmConfigurationTree.type);
    yield fork(treeData);
  }
}

// 树的最后一级
function* watchGetAlarmConfigurationLastTree() {
  while (true) {
    const { payload } = yield take(getAlarmConfigurationLastTree.type);
    const { deviceType, itemType, treeData } = payload;
    yield fork(alarmConfigurationLastTree, deviceType, itemType, treeData);
  }
}

// 模板组
function* watchGetAllTemplateGroupList() {
  while (true) {
    yield take(getAllTemplateGroupList.type);
    yield fork(allTemplateGroupList);
  }
}

// 已关联的模板组
function* watchAssociatedGroupList() {
  while (true) {
    const { payload } = yield take(getAssociatedGroupList.type);
    const { groupId } = payload;
    yield fork(associatedGroupList, groupId);
  }
}

// 关联的告警项
function* watchFetchAssociatedItem() {
  while (true) {
    const { payload } = yield take(fetchAssociatedItem.type);
    yield fork(associatedItem, payload);
  }
}

// 更改启用状态
function* watchAvailableStatus() {
  while (true) {
    const { payload } = yield take(changeAvailableStatus.type);
    yield fork(availableStatus, payload);
  }
}

// 生效域
function* watchGetEffectiveDomainList() {
  while (true) {
    const { payload } = yield take(getEffectiveDomainList.type);
    yield fork(effectiveDomainList, payload.groupId);
  }
}

// 新建模板
function* watchMonitorGroupAdd() {
  while (true) {
    const { payload } = yield take(monitorGroupAdd.type);
    yield fork(add, payload);
  }
}

// 编辑模板
function* watchMonitorGroupUpdate() {
  while (true) {
    const { payload } = yield take(monitorGroupUpdate.type);
    yield fork(update, payload);
  }
}

// 分页模板列表
function* watchFetchTemplateList() {
  while (true) {
    const { payload } = yield take(fetchTemplateList.type);
    yield fork(templateList, payload);
  }
}

// 取消关联的模板组
function* watchRemoveAssociate() {
  while (true) {
    const { payload } = yield take(removeAssociate.type);
    const { groupId, schemeId, params } = payload;
    yield fork(removeAssociateConfirm, groupId, schemeId, params);
  }
}

// 关联模板组
function* watchMonitorGroupAssociateScheme() {
  while (true) {
    const { payload } = yield take(monitorGroupAssociateScheme.type);
    const { groupId, schemeIds, params } = payload;
    yield fork(monitorGroupAssociateSchemeConfirm, groupId, schemeIds, params);
  }
}

function* watchFetchAlarmConfigurationTree() {
  while (true) {
    yield take(fetchAlarmConfigurationTree.type);
    yield fork(alarmConfigurationTree);
  }
}

function* watchNotificationTplChange() {
  while (true) {
    const { type, payload } = yield take([
      insertNotificationTplFieldAction.type,
      updateNotificationTplAction.type,
      deleteNotificationTplFieldAction.type,
      resetNotificationTplAction.type,
    ]);
    if (type === insertNotificationTplFieldAction.type) {
      yield fork(insertNotificationTplField, payload);
    }
    if (type === deleteNotificationTplFieldAction.type) {
      yield fork(deleteNotificationTplField, payload);
    }
    if (type === updateNotificationTplAction.type) {
      yield fork(updateNotificationTpl, payload);
    }
    if (type === resetNotificationTplAction.type) {
      yield fork(resetNotificationTpl, payload);
    }
  }
}

function* watchSubmitNewTemplateConfig() {
  while (true) {
    const { payload } = yield take(submitNewTemplateConfigAction.type);
    yield fork(submitNewTemplateConfig, payload);
  }
}

function* watchSubmitCustomNewTemplateConfig() {
  while (true) {
    const { payload } = yield take(submitNewCustomTemplateConfigAction.type);
    yield fork(submitNewCustomTemplateConfig, payload);
  }
}
function* watchSearchTemplatePointConfig() {
  while (true) {
    const { payload } = yield take(searchTemplatePointConfigAction.type);
    yield call(getNotificationTxtTplFields);
    yield fork(searchTemplatePointConfig, payload);
  }
}

function* watchReviewAlarmNoticify() {
  while (true) {
    const { payload } = yield take(reviewAlarmNoticifyAction.type);
    yield fork(reviewAlarmNoticify, payload);
  }
}

export function* WatchSaveEditTemplateModule() {
  while (true) {
    const { payload } = yield take(saveEditTemplateModuleAction.type);
    yield fork(saveEditTemplateModule, payload);
  }
}

export function* watchSaveEditCustomTemplateModule() {
  while (true) {
    const { payload } = yield take(saveEditCustomTemplateModuleAction.type);
    yield fork(saveEditCustomTemplateModule, payload);
  }
}
function* watchSubmitEditTemplateConfig() {
  while (true) {
    const { payload } = yield take(submitEditTemplateConfigAction.type);
    yield fork(submitEditTemplateConfig, payload);
  }
}

function* watchPasteNoticify() {
  while (true) {
    const { payload } = yield take(pasteNoticifyAction.type);
    yield fork(pasteNoticify, payload);
  }
}

function* watchDeleteTemplate() {
  while (true) {
    const { payload } = yield take(deleteTemplateAction.type);
    yield fork(deleteTemplate, payload);
  }
}

function* watchSearchEventType() {
  while (true) {
    const { payload } = yield take(searchEventTypeAction.type);
    yield fork(searchEventType, payload);
  }
}

function* watchSubmitEditCustomTemplateConfig() {
  while (true) {
    const { payload } = yield take(submitEditCustomTemplateConfigAction.type);
    yield fork(submitEditCustomTemplateConfig, payload);
  }
}

export default [
  fork(watchGetAlarmConfigurationLastTree),
  fork(watchGetAllTemplateGroupList),
  fork(watchAssociatedGroupList),
  fork(watchMonitorGroupAssociateScheme),
  fork(watchRemoveAssociate),
  fork(watchFetchTemplateList),
  fork(watchFetchAssociatedItem),
  fork(watchAvailableStatus),
  fork(watchAlarmConfigurationTree),
  fork(watchMonitorGroupUpdate),
  fork(watchMonitorGroupAdd),
  fork(watchGetEffectiveDomainList),
  fork(watchFetchAlarmConfigurationTree),
  fork(watchNotificationTplChange),
  fork(watchSubmitNewTemplateConfig),
  fork(watchSearchTemplatePointConfig),
  fork(watchReviewAlarmNoticify),
  fork(WatchSaveEditTemplateModule),
  fork(watchSubmitEditTemplateConfig),
  fork(watchPasteNoticify),
  fork(watchDeleteTemplate),
  fork(watchSearchEventType),
  fork(watchSubmitCustomNewTemplateConfig),
  fork(watchSaveEditCustomTemplateModule),
  fork(watchSubmitEditCustomTemplateConfig),
];
