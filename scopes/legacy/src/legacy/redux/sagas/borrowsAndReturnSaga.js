import { call, fork, put, select, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { BORROWS_AND_RETURN_LIST } from '@manyun/resource-hub.route.resource-routes';

import {
  BORROW_CONTENT_MODE_KEY_MAP,
  RELATE_BIZ_TYPE_KEY_MAP,
  RENEW_OR_TRANSFER_TYPE_KEY_MAP,
  TASK_SUBTYPE_TYPE_KEY_MAP,
} from '@manyun/dc-brain.legacy.pages/borrows-and-return/constants';
import { redirectActionCreator } from '@manyun/dc-brain.legacy.redux/actions/routerActions';
import { borrowsAndReturnService } from '@manyun/dc-brain.legacy.services';

import {
  borrowAllActionCreator,
  borrowReSubmitActionCreator,
  borrowsAndReturnActions,
  cancelBorrowActionCreator,
  createBorrowActionCreator,
  getBorrowApplyActionCreator,
  getBorrowInfoActionCreator,
  getDataActionCreator,
  getRenewLendRecordsActionCreator,
  getReturnRecordsActionCreator,
  getTicketRecordsActionCreator,
  lendActionCreator,
  queryBorrowAndReturnAssertInfoActionCreator,
  renewActionCreator,
  renewRevertingActionCreator,
  resetSearchValuesActionCreator,
  returningActionCreator,
  revertBorrowActionCreator,
  setPaginationThenGetDataActionCreator,
} from '../actions/borrowsAndReturnActions';
import { getSearchValuesAndPagination } from '../selectors/borrowsAndReturnSelector';

/***************************** Subroutines ************************************/

function getQ(pagination, searchValues) {
  const baseQ = { ...pagination };
  for (const [, { name, value }] of Object.entries(searchValues)) {
    const val = value === '' ? null : value;
    if (name) {
      if (val && (name === 'title' || name === 'borrowNo' || name === 'assetNo')) {
        baseQ[name] = val.trim();
      } else if (val && (name === 'borrower' || name === 'creator')) {
        baseQ[name] = null;
        baseQ.borrowerName = val.label;
      } else if (name === 'createTimeRange' && val && val.length === 2) {
        baseQ.createStartTime = val[0].startOf('day').valueOf();
        baseQ.createEndTime = val[1].endOf('day').valueOf();
      } else if (name === 'borrowTimeRange' && val && val.length === 2) {
        // baseQ.borrowStartTime = Number(val[0].unix() + '000');
        // baseQ.borrowEndTime = Number(val[1].unix() + '000');
        baseQ.borrowStartTime = val[0].startOf('day').valueOf();
        baseQ.borrowEndTime = val[1].endOf('day').valueOf();
      } else if (name === 'blockGuid' && val) {
        baseQ[name] = val.join('.');
      } else if (name === 'status' && val) {
        baseQ.borrowStatusList = [val];
      } else if (name === 'creatorId' && val) {
        baseQ.creatorId = val.id;
      } else {
        baseQ[name] = val;
      }
    }
  }
  return baseQ;
}

function* getData({ shouldResetPageNum, onSearchButton }) {
  yield put(borrowsAndReturnActions.updateTableLoading(true));
  if (shouldResetPageNum) {
    yield put(borrowsAndReturnActions.resetPageNum());
  }
  const { searchValues, searchConditions, pagination } = yield select(getSearchValuesAndPagination);
  let q = {};
  if (onSearchButton) {
    q = getQ(pagination, searchValues);
  } else {
    q = getQ(pagination, searchConditions);
  }
  const { response, error } = yield call(borrowsAndReturnService.fetchBorrowsAndReturnList, q);
  if (error) {
    message.error(error);
    yield put(borrowsAndReturnActions.failure());
  } else {
    yield put(borrowsAndReturnActions.setDataAndTotal(response));
    yield put(borrowsAndReturnActions.setSearchConditions(searchValues));
  }
}

function* setPagination(newPagination) {
  yield put(borrowsAndReturnActions.setPagination(newPagination));
  yield put(getDataActionCreator({ shouldResetPageNum: false, onSearchButton: false }));
}

function* resetSearchValues() {
  yield put(borrowsAndReturnActions.resetSearchValuesAndPagination());
  yield put(getDataActionCreator({ shouldResetPageNum: true, onSearchButton: true }));
}

function* lending({ params, successCb, type, borrowContentMode }) {
  const { error } = yield call(borrowsAndReturnService.fetchLending, params);
  if (error) {
    message.error(error);
  } else {
    message.success('转借成功！');
    successCb();
    if (type === 'detail') {
      yield put(getBorrowInfoActionCreator({ borrowNo: params.borrowNo }));
      if (borrowContentMode === BORROW_CONTENT_MODE_KEY_MAP.continuationRecord) {
        yield put(getRenewLendRecordsActionCreator({ borrowNo: params.borrowNo }));
      }
      return;
    }
    yield put(getDataActionCreator({ shouldResetPageNum: false, onSearchButton: false }));
  }
}

function* renewing({ params, successCb, type, borrowContentMode }) {
  const { error } = yield call(borrowsAndReturnService.fetchRenewing, params);
  if (error) {
    message.error(error);
  } else {
    message.success('续借成功！');
    successCb();
    if (type === 'detail') {
      yield put(getBorrowInfoActionCreator({ borrowNo: params.borrowNo }));
      if (borrowContentMode === BORROW_CONTENT_MODE_KEY_MAP.continuationRecord) {
        yield put(getRenewLendRecordsActionCreator({ borrowNo: params.borrowNo }));
      }
      return;
    }
    yield put(getDataActionCreator(false, false));
  }
}

function* createBorrow(params) {
  const { error } = yield call(borrowsAndReturnService.fetchCreateBorrow, params);
  if (error) {
    message.error(error);
  } else {
    message.success('新建成功！');
    yield put(redirectActionCreator(BORROWS_AND_RETURN_LIST));
  }
}

function* getBorrowInfo(params) {
  const { error, response } = yield call(borrowsAndReturnService.fetchBorrowInfo, params);
  if (error) {
    message.error(error);
  } else {
    yield put(borrowsAndReturnActions.setBorrowInfo(response));
  }
}

function* getBorrowApply({ borrowNo, successCb }) {
  yield put(borrowsAndReturnActions.updateDetailTableLoading(true));
  const { error, response } = yield call(borrowsAndReturnService.fetchBorrowApply, { borrowNo });
  if (error) {
    message.error(error);
    yield put(borrowsAndReturnActions.failure());
  } else {
    if (successCb) {
      successCb(response.data);
    } else {
      yield put(borrowsAndReturnActions.setBorrowApplyList(response.data));
    }
  }
}

function* queryBorrowAndReturnAssertInfo(params) {
  yield put(borrowsAndReturnActions.updateDetailTableLoading(true));
  const { error, response } = yield call(
    borrowsAndReturnService.fetchBorrowAndReturnAssertInfo,
    params
  );
  if (error) {
    message.error(error);
    yield put(borrowsAndReturnActions.failure());
  } else {
    const tmp = {
      device: {
        list: [],
        borrowNum: 0,
        toBeReturnedNum: 0,
        initList: [],
      },
      spare: {
        list: [],
        borrowNum: 0,
        toBeReturnedNum: 0,
        initList: [],
      },
    };
    const responseData = response.data ? response.data : response;
    responseData.forEach(item => {
      if (item.numbered) {
        tmp.device.list.push(item);
        tmp.device.initList.push(item);
        tmp.device.borrowNum += item.borrowNum;
        const toBeReturnedNum = item.borrowNum - item.returnNum;
        tmp.device.toBeReturnedNum += toBeReturnedNum;
      } else {
        tmp.spare.list.push(item);
        tmp.spare.initList.push(item);
        tmp.spare.borrowNum += item.borrowNum;
        const toBeReturnedNum = item.borrowNum - item.returnNum;
        tmp.spare.toBeReturnedNum += toBeReturnedNum;
      }
    });
    yield put(borrowsAndReturnActions.setBorrowAndReturnAssertInfo(tmp));
  }
}

function* getReturnRecords(params) {
  yield put(borrowsAndReturnActions.updateDetailTableLoading(true));
  const { response, error } = yield call(borrowsAndReturnService.fetchReturnRecords, params);
  if (error) {
    message.error(error);
    yield put(borrowsAndReturnActions.failure());
  } else {
    const responseData = response.data ? response.data : response;
    const deviceData = responseData.filter(({ numbered }) => numbered);
    const spareData = responseData.filter(({ numbered }) => !numbered);
    yield put(borrowsAndReturnActions.updateReturnRecords({ deviceData, spareData }));
  }
}

function* getRenewLendRecords(params) {
  yield put(borrowsAndReturnActions.updateDetailTableLoading(true));
  const { error, response } = yield call(borrowsAndReturnService.fetchRenewlendRecords, params);
  if (error) {
    message.error(error);
    yield put(borrowsAndReturnActions.failure());
  } else {
    const responseData = response.data ? response.data : response;
    const renewTableData = responseData.filter(
      ({ opType }) => opType === RENEW_OR_TRANSFER_TYPE_KEY_MAP.RENEW
    );
    const transferTableData = responseData.filter(
      ({ opType }) => opType === RENEW_OR_TRANSFER_TYPE_KEY_MAP.TRANSFER
    );
    yield put(
      borrowsAndReturnActions.updateRenewLendRecords({ renewTableData, transferTableData })
    );
  }
}

function* getTicketRecords(params) {
  yield put(borrowsAndReturnActions.updateDetailTableLoading(true));
  const { error, response } = yield call(borrowsAndReturnService.fetchTicketRecords, params);
  if (error) {
    message.error(error);
    yield put(borrowsAndReturnActions.failure());
  } else {
    const exWareHouseList = response.data.filter(
      ({ taskSubType }) => taskSubType === TASK_SUBTYPE_TYPE_KEY_MAP.EX_WAREHOUSE
    );
    const inWareHouseList = response.data.filter(
      ({ taskSubType }) => taskSubType === TASK_SUBTYPE_TYPE_KEY_MAP.IN_WAREHOUSE
    );
    yield put(borrowsAndReturnActions.updateTicketRecords({ exWareHouseList, inWareHouseList }));
  }
}

function* revertBorrow({ borrowNo, type }) {
  const { error } = yield call(borrowsAndReturnService.fetchBorrowRevert, { borrowNo });
  if (error) {
    message.error(error);
  } else {
    message.success('撤回成功！');
    if (type === 'detail') {
      yield put(getBorrowInfoActionCreator({ borrowNo }));
      return;
    }
    yield put(getDataActionCreator({ shouldResetPageNum: false, onSearchButton: false }));
  }
}

function* cancelBorrow(params) {
  const { error } = yield call(borrowsAndReturnService.fetchBorrowCancel, params);
  if (error) {
    message.error(error);
  } else {
    message.success('取消成功！');
    yield put(getDataActionCreator({ shouldResetPageNum: false, onSearchButton: false }));
  }
}

function* borrowAll({ type, borrowNo }) {
  const { error } = yield call(borrowsAndReturnService.fetchBorrowAll, { borrowNo });
  if (error) {
    message.error(error);
  } else {
    message.success('完成出库成功！');
    if (type === 'detail') {
      yield put(getBorrowInfoActionCreator({ borrowNo }));
    } else {
      yield put(getDataActionCreator({ shouldResetPageNum: false, onSearchButton: false }));
    }
  }
}

function* borrowReSubmit(params) {
  const { error } = yield call(borrowsAndReturnService.fetchBorrowReSubmit, params);
  if (error) {
    message.error(error);
  } else {
    message.success('重新发起成功！');
    yield put(redirectActionCreator(BORROWS_AND_RETURN_LIST));
  }
}

function* returning({ params, successCb, type, borrowContentMode }) {
  const { error } = yield call(borrowsAndReturnService.fetchReturnAssert, params);
  if (error) {
    message.error(error);
  } else {
    message.success('归还成功！');
    successCb();
    if (type === 'detail') {
      yield put(getBorrowInfoActionCreator({ borrowNo: params.borrowNo }));
      if (borrowContentMode === BORROW_CONTENT_MODE_KEY_MAP.returnRecord) {
        yield put(
          getReturnRecordsActionCreator({
            relateTaskNo: params.borrowNo,
            relateBizType: RELATE_BIZ_TYPE_KEY_MAP.BORROW,
          })
        );
      }
      if (borrowContentMode === BORROW_CONTENT_MODE_KEY_MAP.loanReturnList) {
        yield put(
          queryBorrowAndReturnAssertInfoActionCreator({
            borrowNo: params.borrowNo,
          })
        );
      }
      return;
    }
    yield put(getDataActionCreator({ shouldResetPageNum: false, onSearchButton: false }));
  }
}

function* renewReverting({ borrowNo, opId }) {
  const { error } = yield call(borrowsAndReturnService.fetchRenewRevert, { opId });
  if (error) {
    message.error(error);
  } else {
    message.success('撤回成功！');
    yield put(getRenewLendRecordsActionCreator({ borrowNo }));
  }
}

/******************************************************************************/
/******************************* WATCHERS *************************************/
/******************************************************************************/

export function* watchGetData() {
  while (true) {
    const { payload } = yield take(getDataActionCreator.type);
    yield fork(getData, payload);
  }
}

function* watchResetSearchValues() {
  while (true) {
    yield take(resetSearchValuesActionCreator.type);
    yield fork(resetSearchValues);
  }
}

function* watchSetPagination() {
  while (true) {
    const { payload } = yield take(setPaginationThenGetDataActionCreator.type);
    yield fork(setPagination, payload);
  }
}

export function* watchLending() {
  while (true) {
    const { payload } = yield take(lendActionCreator.type);
    yield fork(lending, payload);
  }
}

export function* watchRenewing() {
  while (true) {
    const { payload } = yield take(renewActionCreator.type);
    yield fork(renewing, payload);
  }
}

export function* watchCreateBorrow() {
  while (true) {
    const { payload } = yield take(createBorrowActionCreator.type);
    yield fork(createBorrow, payload);
  }
}

export function* watchGetBorrowInfo() {
  while (true) {
    const { payload } = yield take(getBorrowInfoActionCreator.type);
    yield fork(getBorrowInfo, payload);
  }
}

export function* watchGetBorrowApply() {
  while (true) {
    const { payload } = yield take(getBorrowApplyActionCreator.type);
    yield fork(getBorrowApply, payload);
  }
}

export function* watchQueryBorrowAndReturnAssertInfo() {
  while (true) {
    const { payload } = yield take(queryBorrowAndReturnAssertInfoActionCreator.type);
    yield fork(queryBorrowAndReturnAssertInfo, payload);
  }
}

export function* watchGetReturnRecords() {
  while (true) {
    const { payload } = yield take(getReturnRecordsActionCreator.type);
    yield fork(getReturnRecords, payload);
  }
}

export function* watchGetRenewLendRecords() {
  while (true) {
    const { payload } = yield take(getRenewLendRecordsActionCreator.type);
    yield fork(getRenewLendRecords, payload);
  }
}

export function* watchGetTicketRecords() {
  while (true) {
    const { payload } = yield take(getTicketRecordsActionCreator.type);
    yield fork(getTicketRecords, payload);
  }
}

export function* watchRevertBorrow() {
  while (true) {
    const { payload } = yield take(revertBorrowActionCreator.type);
    yield fork(revertBorrow, payload);
  }
}

export function* watchCancelBorrow() {
  while (true) {
    const { payload } = yield take(cancelBorrowActionCreator.type);
    yield fork(cancelBorrow, payload);
  }
}

export function* watchBorrowAll() {
  while (true) {
    const { payload } = yield take(borrowAllActionCreator.type);
    yield fork(borrowAll, payload);
  }
}

export function* watchBorrowReSubmit() {
  while (true) {
    const { payload } = yield take(borrowReSubmitActionCreator.type);
    yield fork(borrowReSubmit, payload);
  }
}

export function* watchReturning() {
  while (true) {
    const { payload } = yield take(returningActionCreator.type);
    yield fork(returning, payload);
  }
}

export function* watchRenewReverting() {
  while (true) {
    const { payload } = yield take(renewRevertingActionCreator.type);
    yield fork(renewReverting, payload);
  }
}

export default [
  fork(watchGetData),
  fork(watchLending),
  fork(watchResetSearchValues),
  fork(watchSetPagination),
  fork(watchRenewing),
  fork(watchCreateBorrow),
  fork(watchGetBorrowInfo),
  fork(watchGetBorrowApply),
  fork(watchQueryBorrowAndReturnAssertInfo),
  fork(watchGetReturnRecords),
  fork(watchGetRenewLendRecords),
  fork(watchGetTicketRecords),
  fork(watchRevertBorrow),
  fork(watchCancelBorrow),
  fork(watchBorrowAll),
  fork(watchBorrowReSubmit),
  fork(watchReturning),
  fork(watchRenewReverting),
];
