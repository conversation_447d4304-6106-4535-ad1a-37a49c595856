import { push } from 'connected-react-router';
import { call, fork, put, select, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { createRestRequest } from '@manyun/hrm.service.create-rest-request';

import { dcomService, pmService } from '@manyun/dc-brain.legacy.services';
import * as urlsUtil from '@manyun/dc-brain.legacy.utils/urls';

import {
  alterBasicInfoActionCreator,
  alterFilesActionCreator,
  alterInfoActions,
  cancelAlterInfoActionCreator,
  detailCancelAlterInfoActionCreator,
  getAlterBaseInfoActionCreator,
  getDataActionCreator,
  redirectDetailActionCreator,
  resetSearchValuesActionCreator,
  setPaginationThenGetDataActionCreator,
  submitExchangeActionCreator,
  submitLeaveActionCreator,
} from '../actions/alterInfoActions';
import { getExchangeSubmitParam, getLeaveSubmitParam, getQ } from '../selectors/alterInfoSelectors';

// workers

function* getArticles(shouldResetPageNum = true) {
  if (shouldResetPageNum) {
    yield put(alterInfoActions.resetPageNum());
  }
  const q = yield select(getQ);
  yield put(alterInfoActions.fetchArticlesStart());
  const { response, error } = yield call(pmService.fetchAlterInfoPage, q);
  if (error) {
    message.error(error);
    yield put(alterInfoActions.failure());
    return;
  }
  yield put(alterInfoActions.setArticles(response));
}

function* watchGetTaskBaseInfo() {
  while (true) {
    yield take(getAlterBaseInfoActionCreator.type);
  }
}

function* redirectDetail(procInstanceId) {
  yield put(push(urlsUtil.generateAlterInfoDetaillocation({ procInstanceId })));
}

function* submitLeave() {
  const q = yield select(getLeaveSubmitParam);
  const { data, error } = yield call(createRestRequest, q);
  if (error) {
    message.error(error.message);
    yield put(alterInfoActions.failure());
    return;
  }
  yield put(alterInfoActions.setArticles(data));
  yield put(push(urlsUtil.generateAlterInfoDetaillocation({ id: data })));
}

function* alterBasicInfo(params) {
  const { response, error } = yield call(pmService.fetchAlterBasicInfo, params);
  if (response) {
    yield put(alterInfoActions.setAlterBasicInfo(response));
  } else {
    message.error(error);
  }
}

function* alterFiles(params) {
  const { response, error } = yield call(dcomService.fetchTicketAttachmentInfos, params);
  if (response) {
    yield put(alterInfoActions.setAlterFiles(response));
  } else {
    message.error(error);
  }
}

function* watchAlterBasicInfo() {
  while (true) {
    const { payload } = yield take(alterBasicInfoActionCreator.type);
    yield fork(alterBasicInfo, payload);
  }
}

function* watchAlterFiles() {
  while (true) {
    const { payload } = yield take(alterFilesActionCreator.type);
    yield fork(alterFiles, payload);
  }
}

function* watchRedirectDetail() {
  while (true) {
    const { payload } = yield take(redirectDetailActionCreator.type);
    yield fork(redirectDetail, payload);
  }
}

function* submitExchange() {
  const q = yield select(getExchangeSubmitParam);
  const { response, error } = yield call(pmService.commitExchangeRecord, q);
  if (error) {
    message.error(error);
    yield put(alterInfoActions.failure());
    return;
  }
  yield put(alterInfoActions.setArticles(response));
  yield put(push(urlsUtil.generateAlterInfoDetaillocation({ id: response })));
}

function* cancelAlterInfo(q) {
  const { response, error } = yield call(pmService.cancelAlterProcess, {
    procInstanceId: q.procInstanceId,
  });
  if (error) {
    message.error(error);
    yield put(alterInfoActions.failure());
    return;
  }
  yield put(alterInfoActions.setArticles(response));
  yield fork(getArticles, false);
}

function* detailCancelAlterInfo(q) {
  const { response, error } = yield call(pmService.cancelAlterProcess, {
    procInstanceId: q.procInstanceId,
  });
  if (error) {
    message.error(error);
    yield put(alterInfoActions.failure());
    return;
  }

  yield put(alterInfoActions.setArticles(response));
  yield fork(alterBasicInfo, {
    procInstanceId: q.procInstanceId,
  });
  yield fork(alterFiles, {
    targetId: q.procInstanceId,
    targetType: 'ALTER',
  });
  yield put(alterInfoActions.resetStatusCanceled());
}

function* watchLeaveSubmit() {
  while (true) {
    const { payload } = yield take(submitLeaveActionCreator.type);
    yield fork(submitLeave, payload);
  }
}

function* watchExchangeSubmit() {
  while (true) {
    const { payload } = yield take(submitExchangeActionCreator.type);
    yield fork(submitExchange, payload);
  }
}

function* setPaginationThenGetData(newPagination) {
  yield put(alterInfoActions.setRecordPagination(newPagination));
  yield put(getDataActionCreator(false));
}

function* resetSearchValues() {
  yield put(alterInfoActions.resetSearchValuesAndPagination());
  yield put(getDataActionCreator(false));
}

function* watchResetSearchValues() {
  while (true) {
    yield take(resetSearchValuesActionCreator.type);
    yield fork(resetSearchValues);
  }
}

function* watchCancelAlterInfo() {
  while (true) {
    const { payload } = yield take(cancelAlterInfoActionCreator.type);
    yield fork(cancelAlterInfo, payload);
  }
}

function* watchDetailCancelAlterInfo() {
  while (true) {
    const { payload } = yield take(detailCancelAlterInfoActionCreator.type);
    yield fork(detailCancelAlterInfo, payload);
  }
}

// watchers

function* watchGetArticles() {
  while (true) {
    const { payload } = yield take(getDataActionCreator.type);
    yield fork(getArticles, payload);
  }
}

function* watchSetPaginationThenGetData() {
  while (true) {
    const { payload } = yield take(setPaginationThenGetDataActionCreator.type);
    yield fork(setPaginationThenGetData, payload);
  }
}

export default [
  fork(watchGetTaskBaseInfo),
  fork(watchGetArticles),
  fork(watchSetPaginationThenGetData),
  fork(watchResetSearchValues),
  fork(watchLeaveSubmit),
  fork(watchExchangeSubmit),
  fork(watchAlterBasicInfo),
  fork(watchAlterFiles),
  fork(watchCancelAlterInfo),
  fork(watchRedirectDetail),
  fork(watchDetailCancelAlterInfo),
];
