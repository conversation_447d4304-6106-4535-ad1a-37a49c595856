import { replace } from 'connected-react-router';
import uniq from 'lodash.uniq';
import { normalize } from 'normalizr';
import { call, fork, put, select, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { relatedElementUtil } from '@manyun/monitoring.state.topology';
import { generateSpecificGraphixRoutePath } from '@manyun/resource-hub.route.resource-routes';
import { fetchGrids } from '@manyun/resource-hub.service.fetch-grids';
import { fetchRoom } from '@manyun/resource-hub.service.fetch-room';
import { getSpaceGuid, getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

import { ThemeColors } from '@manyun/dc-brain.legacy.pages/__next/infra-room-graphix';
import { equipmentManageService, topologyService } from '@manyun/dc-brain.legacy.services';
import { devicesByTypeSchema } from '@manyun/dc-brain.legacy.services/schemas/deviceSchemas';

import {
  getGraphActionCreator,
  infraRoomGraphixActions,
  initializeActionCreator,
  saveGraphActionCreator,
} from '../actions/infraRoomGraphixActions';
import { getGraphId } from './../selectors/infraRoomGraphixSelectors';

// workers

function generateInfraRoomTopolyTypeofCustomConfig(configUtil) {
  const typeofProximityReader = configUtil.typeofDeviceGen(
    ConfigUtil.constants.deviceTypes.DOOR_PROXIMITY_READER
  );
  const typeofBulletCamera = configUtil.typeofDeviceGen(
    ConfigUtil.constants.deviceTypes.BULLET_CCTV_CAMERA
  );
  const typeofSmokeDetector = configUtil.typeofDeviceGen(
    ConfigUtil.constants.deviceTypes.SMOKE_DETECTOR
  );
  const typeofThermalDetector = configUtil.typeofDeviceGen(
    ConfigUtil.constants.deviceTypes.THERMAL_DETECTOR
  );
  const typeofAirSampler = configUtil.typeofDeviceGen(ConfigUtil.constants.deviceTypes.AIR_SAMPLER);
  const typeofCustomConfig = deviceType =>
    typeofProximityReader(deviceType) ||
    typeofBulletCamera(deviceType) ||
    typeofSmokeDetector(deviceType) ||
    typeofThermalDetector(deviceType) ||
    typeofAirSampler(deviceType);

  return typeofCustomConfig;
}

function generateInfraRoomTopologyDevicesTree(configUtil, normalizedDevices) {
  const typeofCustomConfig = generateInfraRoomTopolyTypeofCustomConfig(configUtil);
  const ffsTopologyConfigs = configUtil.getTopologyConfigs('FFS');
  const devicesTree = [];
  const { result, data } = normalizedDevices;
  uniq(result).forEach(deviceType => {
    const width = 100;
    const height = 100;

    const devices = data[deviceType];
    const { secondCategory } = devices[0];
    const deviceTypeLevel2TreeNodeIdx = devicesTree.findIndex(({ key }) => key === secondCategory);
    const deviceTypeTreeNode = {
      key: deviceType,
      type: deviceType,
      label: deviceType,
      children: devices.map(({ guid, name, serialNumber }) => {
        const remarkTextElementId = relatedElementUtil.generate(guid);
        const [, remarkTextRowKey] = relatedElementUtil.split(remarkTextElementId);

        const base = {
          key: guid,

          // can dropped onto canvas only once
          copyMax: 1,

          type: deviceType,
          img: '/images/infra-room-topology-graphix/light/device-rect.png',
          label: name,
          elementConfig: {
            custom: { type: 'device_group' },
            type: 'group',
            name,
            width,
            height,
            canUngroup: true,
            children: [],
          },
          isLeaf: true,
        };
        if (typeofCustomConfig(deviceType)) {
          const { thumbnail, size } = ffsTopologyConfigs.elementConfigMappings[deviceType];

          base.img = thumbnail;
          // base.elementConfig.id = guid;
          // base.elementConfig.canUngroup = false;
          base.elementConfig.width = size[0];
          base.elementConfig.height = size[1];
          base.elementConfig.children.push({
            custom: {
              type: 'device',
              sn: serialNumber,
              name,
              deviceType,
              deviceGuid: guid,
            },
            type: 'image',
            src: thumbnail,
            id: guid,
            x: 0,
            y: 0,
            width: size[0],
            height: size[1],
          });
        } else {
          base.elementConfig.children.push(
            {
              custom: {
                type: 'device',
                sn: serialNumber,
                name,
                deviceType,
                deviceGuid: guid,
                remarkTextRowKeys: [remarkTextRowKey],
              },
              type: 'rect',
              id: guid,
              x: 0,
              y: 0,
              width,
              height,
              fill: ThemeColors.ContainerBg,
            },
            {
              custom: { type: 'device_text' },
              id: remarkTextElementId,
              type: 'text',
              x: 0,
              y: 0,
              width,
              height,
              align: 'center',
              verticalAlign: 'middle',
              text: name,
              fill: ThemeColors.TextColor,
              stroke: ThemeColors.TextColor,
            }
          );
        }

        return base;
      }),
    };
    if (deviceTypeLevel2TreeNodeIdx > -1) {
      devicesTree[deviceTypeLevel2TreeNodeIdx].children.push(deviceTypeTreeNode);
    } else {
      devicesTree.push({
        key: secondCategory,
        type: secondCategory,
        label: secondCategory,
        children: [deviceTypeTreeNode],
      });
    }
  });

  return devicesTree;
}

function generateLiquidMonitoringTopologyDevicesTree(configUtil, normalizedDevices, racks) {
  const { elementConfigMappings } = configUtil.getTopologyConfigs('LIQUID_MONITORING');
  const rackDeviceType = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_GRID);
  const rackElementConfig = elementConfigMappings[rackDeviceType];
  const devicesTree = [
    {
      key: ConfigUtil.constants.deviceTypes.SPACE_GRID,
      type: rackDeviceType,
      label: '机柜',
      children: racks.map(rack => {
        const remarkTextElementId = relatedElementUtil.generate(rack.guid);
        const [, remarkTextRowKey] = relatedElementUtil.split(remarkTextElementId);
        const remarkTextElementFontSize = 12;
        const containerWidth = rackElementConfig.size[0];
        const containerHeight = rackElementConfig.size[1] + remarkTextElementFontSize;

        return {
          key: rack.guid,
          copyMax: 1,
          type: rackDeviceType,
          img: rackElementConfig.thumbnail,
          label: rack.tag,
          isLeaf: true,
          elementConfig: {
            type: 'group',
            name: rack.tag,
            width: containerWidth,
            height: containerHeight,
            canUngroup: true,
            children: [
              {
                custom: {
                  type: 'device',
                  isRack: true,
                  name: rack.tag,
                  deviceType: rackDeviceType,
                  deviceGuid: rack.guid,
                  remarkTextRowKeys: [remarkTextRowKey],
                },
                type: 'group',
                canUngroup: false,
                id: rack.guid,
                name: rack.tag,
                x: 0,
                y: 0,
                width: containerWidth,
                height: rackElementConfig.size[1],
                anchorPointsConfig: rackElementConfig.anchorPointsConfig,
                anchorPointsPlacementConfig: rackElementConfig.anchorPointsPlacementConfig,
                children: [
                  {
                    custom: {
                      type: 'device_image',
                    },
                    type: 'image',
                    x: 0,
                    y: 0,
                    width: rackElementConfig.size[0],
                    height: rackElementConfig.size[1],
                    src: rackElementConfig.thumbnail,
                  },
                ],
              },
              {
                type: 'text',
                id: remarkTextRowKey,
                x: 0,
                y: rackElementConfig.size[1],
                width: containerWidth,
                height: remarkTextElementFontSize,
                fontSize: remarkTextElementFontSize,
                text: rack.tag,
              },
            ],
          },
        };
      }),
    },
  ];
  const { data, result } = normalizedDevices;
  if (!data) {
    return devicesTree;
  }
  uniq(result).forEach(deviceType => {
    const devices = data[deviceType];
    if (!devices) {
      return;
    }
    const width = 100;
    const height = 100;

    const elementConfig = elementConfigMappings[deviceType];

    const { secondCategory } = devices[0];
    const deviceTypeLevel2TreeNodeIdx = devicesTree.findIndex(({ key }) => key === secondCategory);
    const deviceTypeTreeNode = {
      key: deviceType,
      type: deviceType,
      label: deviceType,
      children: devices.map(({ guid, name, serialNumber }) => {
        const remarkTextElementId = relatedElementUtil.generate(guid);
        const [, remarkTextRowKey] = relatedElementUtil.split(remarkTextElementId);
        const remarkTextElementFontSize = 12;

        const base = {
          key: guid,

          // can dropped onto canvas only once
          copyMax: 1,

          type: deviceType,
          img: elementConfig?.thumbnail
            ? elementConfig.thumbnail
            : '/images/infra-room-topology-graphix/light/device-rect.png',
          label: name,
          elementConfig: {
            type: 'group',
            name,
            width: width,
            height: height,
            canUngroup: true,
            children: [],
          },
          isLeaf: true,
        };

        if (elementConfig) {
          const { thumbnail, size, parts, anchorPointsConfig, anchorPointsPlacementConfig } =
            elementConfig;
          base.elementConfig.children.push(
            {
              custom: {
                type: 'device',
                name,
                deviceType,
                deviceGuid: guid,
                remarkTextRowKeys: [remarkTextRowKey],
              },
              type: 'group',
              canUngroup: false,
              anchorPointsConfig,
              anchorPointsPlacementConfig,
              id: guid,
              name,
              x: 0,
              y: 0,
              width: size[0],
              height: size[1],
              children: [
                {
                  custom: {
                    type: 'device_image',
                  },
                  type: 'image',
                  x: 0,
                  y: 0,
                  width: size[0],
                  height: size[1],
                  src: thumbnail,
                },
                ...(parts?.map(({ type, thumbnail, x, y, width, height }) => {
                  return {
                    custom: {
                      type: `parts_${type}`,
                    },
                    type: 'image',
                    src: thumbnail,
                    x,
                    y,
                    width,
                    height,
                  };
                }) ?? []),
              ],
            },
            {
              type: 'text',
              id: remarkTextRowKey,
              x: 0,
              y: size[1],
              width: size[0],
              height: remarkTextElementFontSize,
              fontSize: remarkTextElementFontSize,
              text: name,
            }
          );
        } else {
          base.elementConfig.children.push(
            {
              custom: {
                type: 'device',
                sn: serialNumber,
                name,
                deviceType,
                deviceGuid: guid,
                remarkTextRowKeys: [remarkTextRowKey],
              },
              id: guid,
              type: 'group',
              width,
              height,
              canUngroup: false,
              children: [
                {
                  type: 'rect',
                  x: 0,
                  y: 0,
                  width,
                  height,
                  fill: ThemeColors.ContainerBg,
                },
              ],
            },
            {
              custom: { type: 'device_text' },
              id: remarkTextElementId,
              type: 'text',
              x: 0,
              y: 0,
              width,
              height,
              align: 'center',
              verticalAlign: 'middle',
              text: name,
              fill: ThemeColors.TextColor,
              stroke: ThemeColors.TextColor,
            }
          );
        }

        return base;
      }),
    };
    if (deviceTypeLevel2TreeNodeIdx > -1) {
      devicesTree[deviceTypeLevel2TreeNodeIdx].children.push(deviceTypeTreeNode);
    } else {
      devicesTree.push({
        key: secondCategory,
        type: secondCategory,
        label: secondCategory,
        children: [deviceTypeTreeNode],
      });
    }
  });

  return devicesTree;
}

function* initialize({ idc, block, room, mode, topologyType }) {
  const { id, graph } = yield call(getGraph, { idc, block, room, mode, topologyType });
  if (id !== null && mode === 'new') {
    yield put(
      replace(generateSpecificGraphixRoutePath({ topologyType, idc, block, room, mode: 'edit' }))
    );
    return;
  }
  const roomResp = yield call(fetchRoom, { roomGuid: getSpaceGuid(idc, block, room) });
  const rooms = [room];
  if (roomResp.data && roomResp.data.relateRooms) {
    rooms.push(
      ...Object.keys(roomResp.data.relateRooms).map(roomGuid => getSpaceGuidMap(roomGuid).room)
    );
  }
  const { response, error } = yield call(equipmentManageService.fetchEquipmentListPage, {
    idcTag: idc,
    blockTag: block,
    roomTags: rooms,
    pageNum: 1,
    pageSize: 50000,
  });
  if (error) {
    message.error(error);
    yield put(infraRoomGraphixActions.initialized({ id, graph, isReady: true, devicesTree: null }));
    return;
  }
  const {
    entities: { data },
    result,
  } = normalize(response.data, devicesByTypeSchema);
  const normalizedDevices = { data, result };
  const config = yield select(selectCurrentConfig);
  const configUtil = new ConfigUtil(config);
  let devicesTree = [];
  if (topologyType === 'ROOM_FACILITY' || topologyType === 'LIQUID_TOPOLOGY') {
    devicesTree = generateInfraRoomTopologyDevicesTree(configUtil, normalizedDevices);
  } else if (topologyType === 'LIQUID_MONITORING') {
    const racksResp = yield call(fetchGrids, {
      idcTag: idc,
      blockTag: block,
      roomTag: room,
      pageNum: 1,
      pageSize: 10, // 老代码兼容新版接口，一般ts必填，接口内部默认处理pageNum: 1和pageSize: 100, 这里按旧逻辑处理
    });
    devicesTree = generateLiquidMonitoringTopologyDevicesTree(
      configUtil,
      normalizedDevices,
      racksResp.data.data
    );
  }

  yield put(
    infraRoomGraphixActions.initialized({
      id,
      graph,
      isReady: true,
      devicesTree,
    })
  );
}

function* saveGraph({ idc, block, room, json, mode, topologyType }) {
  const payload = { idc, block, room, json, topologyType };
  if (mode === 'new') {
    yield call(createGraph, payload);
    return;
  }
  if (mode === 'edit') {
    const id = yield select(getGraphId);
    yield call(updateGraph, { id, ...payload });
    return;
  }
  console.error(`unexpected mode(${mode})`);
}

function* createGraph({ idc, block, room, json, topologyType }) {
  const { response, error } = yield call(topologyService.saveTopology, {
    viewJson: JSON.stringify(json),
    topologyJson: { nodeList: [], flowList: [] },
    blocks: [getSpaceGuid(idc, block, room)],
    topologyType,
  });
  if (response) {
    message.success('保存成功');
    yield put(
      replace(
        generateSpecificGraphixRoutePath({
          topologyType,
          idc,
          block,
          room,
          mode: 'edit',
        })
      )
    );
  } else {
    message.error(error);
  }
}

function* updateGraph({ id, idc, block, room, json, topologyType }) {
  const { response, error } = yield call(topologyService.updateTopology, {
    id,
    viewJson: JSON.stringify(json),
    topologyJson: { nodeList: [], flowList: [] },
    blocks: [getSpaceGuid(idc, block, room)],
    topologyType,
  });
  if (response) {
    message.success('保存成功');
  } else {
    message.error(error);
  }
}

function* getGraph({ idc, block, room, topologyType }) {
  const { response, error } = yield call(topologyService.fetchTopology, {
    blockGuid: getSpaceGuid(idc, block, room),
    topologyType,
  });
  if (error) {
    message.error('获取包间拓扑失败');
    return {
      id: null,
      graph: null,
    };
  }
  try {
    const { viewJson, id } = response;
    const graph = JSON.parse(viewJson);
    return { id, graph };
  } catch (error) {
    console.error(error);
    return {
      id: null,
      graph: null,
    };
  }
}

// watchers

function* watchInitialize() {
  while (true) {
    const { payload } = yield take(initializeActionCreator.type);
    yield fork(initialize, payload);
  }
}

function* watchSaveGraph() {
  while (true) {
    const { payload } = yield take(saveGraphActionCreator.type);
    yield fork(saveGraph, payload);
  }
}

function* watchGetGraph() {
  while (true) {
    const { payload } = yield take(getGraphActionCreator.type);
    yield fork(getGraph, payload);
  }
}

export default [fork(watchInitialize), fork(watchSaveGraph), fork(watchGetGraph)];
