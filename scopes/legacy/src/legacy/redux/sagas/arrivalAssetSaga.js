import trim from 'lodash/trim';
import { call, fork, put, select, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { ACCEPT_TYPE_KEY_MAP } from '@manyun/dc-brain.legacy.pages/ticket/configs/accept/constants';
import * as arrivalAssetService from '@manyun/dc-brain.legacy.services/arrivalAssetService';

import {
  arrivalAssetActions,
  getArrivalAssetListActionCreator,
  initialArrivalAssetListActionCreator,
  setPaginationThenGetDataActionCreator,
  updatePageActiveTabKeyActionCreator,
} from '../actions/arrivalAssetActions';
import { getPageConditions } from '../selectors/arrivalAssetSelectors';

function getQ(activeTabKey, pagination, searchValues) {
  const baseQ = { ...pagination, sourceType: activeTabKey };
  if (!searchValues) {
    return baseQ;
  }
  for (const [, { name, value }] of Object.entries(searchValues)) {
    if ([null, undefined, ''].includes(value) || (Array.isArray(value) && value.length === 0)) {
      continue;
    }
    if (name === 'deviceType') {
      if (value.firstCategoryCode) {
        baseQ['topCategory'] = value.firstCategoryCode;
      }
      if (value.secondCategoryCode) {
        baseQ['secondCategory'] = value.secondCategoryCode;
      }
      if (value.thirdCategorycode) {
        baseQ[name] = value.thirdCategorycode;
      }
    } else if (name === 'vendorModel') {
      baseQ['vendor'] = value[0];
      baseQ['productModel'] = value[1];
    } else if (name === 'arrivalTime') {
      baseQ['arrivalStartTime'] = value[0].clone().startOf('day').valueOf();
      baseQ['arrivalEndTime'] = value[1].clone().endOf('day').valueOf();
    } else if (name === 'targetBlockGuid' || name === 'sourceBlockGuid') {
      baseQ[name] = value.join('.');
    } else {
      baseQ[name] = Array.isArray(value) ? value : trim(value);
    }
  }
  return baseQ;
}

function* initArrivalAssetPage() {
  yield put(getArrivalAssetListActionCreator({ initActiveTabKey: ACCEPT_TYPE_KEY_MAP.PURCHASE }));
  yield put(getArrivalAssetListActionCreator({ initActiveTabKey: ACCEPT_TYPE_KEY_MAP.MOVE }));
  yield put(getArrivalAssetListActionCreator({}));
}

function* fetchArrivalAssetPage({ initActiveTabKey }) {
  yield put(arrivalAssetActions.request());
  const { pagination, searchValues, activeTabKey } = yield select(getPageConditions);
  const q = initActiveTabKey
    ? getQ(initActiveTabKey, pagination)
    : getQ(activeTabKey, pagination, searchValues);
  const { response, error } = yield call(arrivalAssetService.fetchArrivalAssetList, q);
  if (response) {
    if (initActiveTabKey) {
      yield put(
        arrivalAssetActions.updateTotalNum({
          activeTabKey: initActiveTabKey,
          value: response.total,
        })
      );
      return;
    }
    yield put(
      arrivalAssetActions.featchArrivalAssetListSuccess({
        ...response,
        activeTabKey,
      })
    );
  } else {
    message.error(error);
    yield put(arrivalAssetActions.failure());
  }
}

function* setWarrantyPagePagination(newPagination) {
  yield put(arrivalAssetActions.setPaginationChangeHandler(newPagination));
  yield put(getArrivalAssetListActionCreator({}));
}

function* updatePageActiveTabKey(activeTabKey) {
  yield put(arrivalAssetActions.updateActiveTabKey(activeTabKey));
  yield put(arrivalAssetActions.resetPagination());
  yield put(getArrivalAssetListActionCreator({}));
}

/******************************************************************************/
/******************************* WATCHERS *************************************/
/******************************************************************************/

export function* watchFetchArrivalAssetList() {
  while (true) {
    const { payload } = yield take(getArrivalAssetListActionCreator.type);
    yield fork(fetchArrivalAssetPage, payload);
  }
}

function* watchSetArrivalAssetPagination() {
  while (true) {
    const { payload } = yield take(setPaginationThenGetDataActionCreator.type);
    yield fork(setWarrantyPagePagination, payload);
  }
}

export function* watchInitialArrivalAssetList() {
  while (true) {
    yield take(initialArrivalAssetListActionCreator.type);
    yield fork(initArrivalAssetPage);
  }
}

export function* watchUpdatePageActiveTabKey() {
  while (true) {
    const { payload } = yield take(updatePageActiveTabKeyActionCreator.type);
    yield fork(updatePageActiveTabKey, payload);
  }
}

export default [
  fork(watchFetchArrivalAssetList),
  fork(watchSetArrivalAssetPagination),
  fork(watchInitialArrivalAssetList),
  fork(watchUpdatePageActiveTabKey),
];
