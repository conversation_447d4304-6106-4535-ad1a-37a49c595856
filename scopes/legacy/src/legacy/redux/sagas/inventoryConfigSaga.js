import { call, fork, put, select, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { redirectActionCreator } from '@manyun/dc-brain.legacy.redux/actions/routerActions';
import { taskCenterService } from '@manyun/dc-brain.legacy.services';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';

import {
  createConfigActionCreator,
  deleteConfigActionCreator,
  editConfigActionCreator,
  getConfigInfoActionCreator,
  getDataActionCreator,
  inventoryConfigActions,
  resetSearchValuesActionCreator,
  setPaginationThenGetDataActionCreator,
} from '../actions/inventoryConfigActions';
import { getSearchValuesAndPagination } from '../selectors/inventoryConfigSelector';

function getQ(pagination, searchValues) {
  const baseQ = { ...pagination };
  for (const [, { name, value }] of Object.entries(searchValues)) {
    const val = value === '' ? null : value;
    if (name) {
      if (name === 'configName' && val) {
        baseQ[name] = val.trim();
      } else if (name === 'modifierId' && val) {
        baseQ[name] = val.value;
      } else if (name === 'updateTimeRange' && val && val.length) {
        baseQ.updateStartTime = Number(val[0].unix() + '000');
        baseQ.updateEndTime = Number(val[1].unix() + '000');
      } else if (name === 'createTimeRange' && val && val.length) {
        baseQ.createStartTime = Number(val[0].unix() + '000');
        baseQ.createEndTime = Number(val[1].unix() + '000');
      } else {
        baseQ[name] = val;
      }
    }
  }
  return baseQ;
}

function* getData(payload) {
  const { shouldResetPageNum, configType } = payload;
  yield put(inventoryConfigActions.updateTableLoading(true));
  if (shouldResetPageNum) {
    yield put(inventoryConfigActions.resetPageNum());
  }
  const { searchValues, pagination } = yield select(getSearchValuesAndPagination);
  const q = getQ(pagination, searchValues);
  const { response, error } = yield call(taskCenterService.fetchInventoryConfigData, {
    ...q,
    configType,
  });
  if (error) {
    message.error(error);
    yield put(inventoryConfigActions.failure());
  } else {
    yield put(inventoryConfigActions.setDataAndTotal(response));
  }
}

function* resetSearchValues(configType) {
  yield put(inventoryConfigActions.resetSearchValuesAndPagination());
  yield put(getDataActionCreator({ shouldResetPageNum: false, configType }));
}

function* setPagination(newPagination) {
  const { pageNum, pageSize, configType } = newPagination;
  yield put(inventoryConfigActions.setPagination({ pageNum, pageSize }));
  yield put(getDataActionCreator({ shouldResetPageNum: false, configType }));
}

function* deleteConfig(payload) {
  const { configId, configType } = payload;
  const { error } = yield call(taskCenterService.deleteInventoryConfig, { configId });
  if (error) {
    message.error(error);
  } else {
    message.success('删除成功！');
    yield put(getDataActionCreator({ shouldResetPageNum: false, configType }));
  }
}

function* createConfig(payload) {
  const { mode, ...rest } = payload;
  const { error } = yield call(taskCenterService.fetchCreateInventoryConfig, { ...rest });
  if (error) {
    message.error(error);
  } else {
    message.success(mode === 'copy' ? '复制成功' : '新建成功！');
    yield put(redirectActionCreator(urls.generateInventoryListLocation()));
  }
}

function* editConfig(payload) {
  const { error } = yield call(taskCenterService.fetchEditInventoryConfig, payload);
  if (error) {
    message.error(error);
  } else {
    message.success('编辑成功！');
    yield put(redirectActionCreator(urls.generateInventoryListLocation()));
  }
}

function* getConfigInfo(payload) {
  const { error, response } = yield call(taskCenterService.fetchInventoryConfigInfo, payload);
  if (error) {
    message.error(error);
  } else {
    const editOption = {
      id: response.id,
      configName: {
        name: 'configName',
        value: response.configName,
      },
      blockGuid: {
        name: 'blockGuid',
        value: response.blockGuid,
      },
      configType: {
        name: 'configType',
        value: response.configType,
      },
      deviceTypes: {
        name: 'deviceTypes',
        value: response.deviceTypes.map(thirdCategorycode => {
          return {
            thirdCategorycode,
          };
        }),
      },
      inventoryType: {
        name: 'inventoryType',
        value: response.inventoryType.code,
      },
      inventorySubType: {
        name: 'inventorySubType',
        value: response.inventorySubType ? response.inventorySubType.code : 'IMPLICIT',
      },
    };
    yield put(inventoryConfigActions.setConfigInfos(response));
    yield put(inventoryConfigActions.upDateEditOptionValues(editOption));
  }
}

/******************************************************************************/
/******************************* WATCHERS *************************************/
/******************************************************************************/

function* watchGetDataAction() {
  while (true) {
    const { payload } = yield take(getDataActionCreator.type);
    yield fork(getData, payload);
  }
}

function* watchResetSearchValues() {
  while (true) {
    const { payload } = yield take(resetSearchValuesActionCreator.type);
    yield fork(resetSearchValues, payload);
  }
}

function* watchSetPagination() {
  while (true) {
    const { payload } = yield take(setPaginationThenGetDataActionCreator.type);
    yield fork(setPagination, payload);
  }
}

function* watchDeleteConfig() {
  while (true) {
    const { payload } = yield take(deleteConfigActionCreator.type);
    yield fork(deleteConfig, payload);
  }
}

function* watchCreateConfig() {
  while (true) {
    const { payload } = yield take(createConfigActionCreator.type);
    yield fork(createConfig, payload);
  }
}

function* watchEditConfig() {
  while (true) {
    const { payload } = yield take(editConfigActionCreator.type);
    yield fork(editConfig, payload);
  }
}

function* watchGetConfigInfo() {
  while (true) {
    const { payload } = yield take(getConfigInfoActionCreator.type);
    yield fork(getConfigInfo, payload);
  }
}

export default [
  fork(watchGetDataAction),
  fork(watchResetSearchValues),
  fork(watchSetPagination),
  fork(watchDeleteConfig),
  fork(watchCreateConfig),
  fork(watchEditConfig),
  fork(watchGetConfigInfo),
];
