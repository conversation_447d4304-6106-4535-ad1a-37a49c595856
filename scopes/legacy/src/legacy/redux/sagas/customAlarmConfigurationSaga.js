import { push } from 'connected-react-router';
import trim from 'lodash/trim';
import { call, fork, put, select, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import * as urls from '@manyun/dc-brain.legacy.constants/urls';
import { alarmConfigService } from '@manyun/dc-brain.legacy.services';

import {
  changeAvailableStatus,
  customAlarmConfigurationActions,
  deleteTemplateAction,
  fetchTemplateList,
  monitorGroupAdd,
  monitorGroupUpdate,
} from '../actions/customAlarmConfiguratioActions';
import { getSearchValues } from '../selectors/customAlarmConfigSelectors';

function getQ(pagination, searchValues) {
  const baseQ = { ...pagination };
  for (const [, { name, value }] of Object.entries(searchValues)) {
    const val = value === '' ? null : value;
    if (name === 'operatorId' && val) {
      baseQ[name] = val.key;
    } else {
      baseQ[name] = trim(val);
    }
  }
  return baseQ;
}

function* templateList({ pageNum, pageSize, initialSearchValues }) {
  yield put(customAlarmConfigurationActions.request());
  const { searchValues } = yield select(getSearchValues, initialSearchValues);
  const pagination = {
    pageNum,
    pageSize,
  };
  const q = getQ(pagination, searchValues);
  const { response: templateList, error } = yield call(alarmConfigService.fetchCustomAlarmList, q);
  if (templateList) {
    yield put(
      customAlarmConfigurationActions.templateListPageSuccess({
        list: templateList.data,
        total: templateList.total,
        pageNum,
        pageSize,
        loading: false,
      })
    );
  } else {
    message.error(error || '自定义告警列表请求失败');
    yield put(customAlarmConfigurationActions.failure());
  }
}

function* availableStatus(payload) {
  yield put(customAlarmConfigurationActions.request());
  const { response, error } = yield call(alarmConfigService.customAvailableStatus, {
    available: payload.available,
    customGroupIds: payload.customGroupIds,
  });
  if (response) {
    yield fork(templateList, payload.params);
    message.success(`成功${payload.available ? '启用' : '停用'}${payload.customGroupIds.length}项`);
  } else {
    message.error(error || '更改启用状态失败');
    yield put(customAlarmConfigurationActions.failure());
  }
}

function* update(payload) {
  yield put(customAlarmConfigurationActions.onEidtOrNewLoading());
  const { response, error } = yield call(alarmConfigService.monitorGroupUpdate, payload);
  if (response) {
    message.success('修改模板成功');
    yield put(customAlarmConfigurationActions.onEidtOrNewLoading());
    yield put(push(urls.ALARM_CONFIGURATION_TEMPLATE_LIST));
  } else {
    message.error(error || '修改模板失败');
    yield put(customAlarmConfigurationActions.onEidtOrNewLoading());
  }
}

function* add(payload) {
  yield put(customAlarmConfigurationActions.onEidtOrNewLoading());
  const { response, error } = yield call(alarmConfigService.monitorGroupAdd, payload);
  if (response) {
    message.success('新建模板成功');
    yield put(customAlarmConfigurationActions.onEidtOrNewLoading());
    yield put(push(urls.ALARM_CONFIGURATION_TEMPLATE_LIST));
  } else {
    message.error(error || '新建模板失败');
    yield put(customAlarmConfigurationActions.onEidtOrNewLoading());
  }
}

function* deleteTemplate(payload) {
  yield put(customAlarmConfigurationActions.request());
  const { response, error } = yield call(alarmConfigService.fetchDeleteCustomGroup, payload);
  if (response) {
    message.success('删除模板成功！');
    yield fork(templateList, { pageNum: 1, pageSize: 10 });
  } else {
    yield put(customAlarmConfigurationActions.failure());
    message.error(error);
  }
}

// 更改启用状态
function* watchAvailableStatus() {
  while (true) {
    const { payload } = yield take(changeAvailableStatus.type);
    yield fork(availableStatus, payload);
  }
}

// 新建模板
function* watchMonitorGroupAdd() {
  while (true) {
    const { payload } = yield take(monitorGroupAdd.type);
    yield fork(add, payload);
  }
}

// 编辑模板
function* watchMonitorGroupUpdate() {
  while (true) {
    const { payload } = yield take(monitorGroupUpdate.type);
    yield fork(update, payload);
  }
}

// 分页模板列表
function* watchFetchTemplateList() {
  while (true) {
    const { payload } = yield take(fetchTemplateList.type);
    yield fork(templateList, payload);
  }
}

function* watchDeleteTemplate() {
  while (true) {
    const { payload } = yield take(deleteTemplateAction.type);
    yield fork(deleteTemplate, payload);
  }
}

export default [
  fork(watchFetchTemplateList),
  fork(watchAvailableStatus),
  fork(watchMonitorGroupUpdate),
  fork(watchMonitorGroupAdd),
  fork(watchDeleteTemplate),
];
