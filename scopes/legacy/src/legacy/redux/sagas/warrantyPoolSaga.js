import trim from 'lodash/trim';
import moment from 'moment';
import { call, fork, put, select, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { equipmentManageService } from '@manyun/dc-brain.legacy.services';

import {
  fetchWarrantyPoolList,
  setWarrantyPoolPagination,
  warrantyPoolActions,
} from '../actions/warrantyPoolAactions';
import { getSearchValuesAndPagination } from '../selectors/warrantyPoolSelectors';

function getQ(pagination, searchValues) {
  const baseQ = {
    ...pagination,
    sortWarranty: 'ASCEND',
    warrantyTime: moment().add(1, 'months').startOf('day').valueOf(),
  };
  for (const [, { name, value }] of Object.entries(searchValues)) {
    if ([null, undefined, ''].includes(value) || (Array.isArray(value) && value.length === 0)) {
      continue;
    }
    if (name === 'vendorModel') {
      baseQ['vendor'] = value[0];
      baseQ['productModel'] = value[1];
    } else if (name === 'warrantyTimeRange') {
      baseQ['warrantyTimeStart'] = value[0].clone().startOf('day').valueOf();
      baseQ['warrantyTimeEnd'] = value[1].clone().endOf('day').valueOf();
    } else if (name === 'spaceGuidList') {
      baseQ[name] = [value.join('.')];
    } else if (name === 'operatorId') {
      baseQ[name] = value.id;
    } else {
      baseQ[name] = Array.isArray(value) ? value : trim(value);
    }
  }
  return baseQ;
}

function* fetchWarrantyPoolPage() {
  yield put(warrantyPoolActions.request());
  const { pagination, searchValues } = yield select(getSearchValuesAndPagination);
  const { response, error } = yield call(
    equipmentManageService.fetchEquipmentListPage,
    getQ(pagination, searchValues)
  );
  if (response) {
    yield put(warrantyPoolActions.featchWarrantyPoolSuccess(response));
  } else {
    message.error(error);
    yield put(warrantyPoolActions.failure());
  }
}

function* setWarrantyPoolPagePagination(newPagination) {
  yield put(warrantyPoolActions.setWarrantyPoolPagination(newPagination));
  yield put(fetchWarrantyPoolList());
}

/******************************************************************************/
/******************************* WATCHERS *************************************/
/******************************************************************************/

export function* watchFetchWarrantyPoolList() {
  while (true) {
    yield take(fetchWarrantyPoolList.type);
    yield fork(fetchWarrantyPoolPage);
  }
}

function* watchSetWarrantyPoolPagination() {
  while (true) {
    const { payload } = yield take(setWarrantyPoolPagination.type);
    yield fork(setWarrantyPoolPagePagination, payload);
  }
}

export default [fork(watchFetchWarrantyPoolList), fork(watchSetWarrantyPoolPagination)];
