import { push } from 'connected-react-router';
import omitBy from 'lodash/omitBy';
import trim from 'lodash/trim';
import { call, fork, put, select, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { updateDevice } from '@manyun/resource-hub.service.update-device';

import { equipmentManageService, infrastructureService } from '@manyun/dc-brain.legacy.services';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';

import {
  batchUpdateDevicesActionCreator,
  deleteDevice,
  equipmentActions, // 编辑
  fetchEditEquipment, // 分页查询设备列表
  fetchEquipmentListPage, // 导入
  fetchuploadEquipment,
  getDeviceInfo,
  importLocationHrefEquipmentList,
  initializeSearchValues,
  searchDeviceSN,
  setEquipmentPagination,
} from '../actions/equipmentActions';
import { getEquipmentPageConditions } from '../selectors/equipmentSelector';

/***************************** Subroutines ************************************/
function getQ(pagination, searchValues, hiddenSearchValues) {
  const baseQ = { ...pagination, ...hiddenSearchValues };
  for (const [, { name, value }] of Object.entries(searchValues)) {
    if ([null, undefined, ''].includes(value) || (Array.isArray(value) && value.length === 0)) {
      continue;
    }
    if (name === 'vendorModel') {
      baseQ['vendor'] = value[0];
      baseQ['productModel'] = value[1];
    } else if (name === 'cascaderList') {
      baseQ['topCategory'] = value.firstCategoryCode;
      baseQ['secondCategory'] = value.secondCategoryCode;
      if (value.thirdCategorycode) {
        baseQ['deviceTypeList'] = [value.thirdCategorycode];
      }
    } else if (name === 'warrantyStatus' || name === 'operationStatus') {
      baseQ[name] = value.key;
    } else {
      baseQ[name] = Array.isArray(value) ? value : trim(value);
    }
  }
  return baseQ;
}

function getParams(params) {
  const q = omitBy(params, (value, key) => {
    if (value === null || value === undefined || value === '') {
      return key;
    }
  });
  return q;
}

function* fetchEquipmentPage() {
  yield put(equipmentActions.request());
  const { pagination, searchValues, hiddenSearchValues } = yield select(getEquipmentPageConditions);
  const { response, error } = yield call(
    equipmentManageService.fetchEquipmentListPage,
    getQ(pagination, searchValues, hiddenSearchValues)
  );
  if (response) {
    yield put(equipmentActions.featchEquipmentPageSuccess(response));
  } else {
    message.error(error || '查询设备列表请求失败');
    yield put(equipmentActions.failure());
  }
}

function* editEquipment({ data, successCallback, errorCallback }) {
  const { error } = yield call(updateDevice, data);
  if (error) {
    message.error(error.message);
    errorCallback();
    yield put(equipmentActions.failure());
    return;
  } else {
    successCallback();
    message.success('设备信息已更新！');
  }
}

function* importEquipment(payload) {
  yield put(equipmentActions.importLoading());
  const file = payload;
  const fd = new FormData();
  fd.append('file', file);
  const { response, error } = yield call(equipmentManageService.uploadEquipment, fd);
  if (response) {
    yield put(equipmentActions.importLoading());
    if (!response.data.length) {
      message.success('导入的文件为空');
    } else {
      message.success('导入设备成功');
      yield put(equipmentActions.importEquipmentSuccess(response));
    }
  } else {
    message.error(error || '导入请求失败');
    yield put(equipmentActions.failure());
  }
}

function* importCommint(payload) {
  const { response, error } = yield call(equipmentManageService.importFinish, payload);

  if (response) {
    message.success('新增设备成功');
    yield put(push(urls.generateDeviceListUrl()));
  } else {
    message.error(error || '新增设备失败');
    yield put(equipmentActions.failure());
  }
}

function* deleteEquipment({ params, successCallback, errorCallback }) {
  const { response, error } = yield call(equipmentManageService.deleteDeviceInfo, params);
  if (response) {
    successCallback();
    message.success('删除设备成功');
    yield put(fetchEquipmentListPage());
  } else {
    errorCallback();
    message.error(error || '删除设备失败');
  }
}

function* searchDeviceSNWithName(payload) {
  const { response, error } = yield call(equipmentManageService.fetchEquipmentListPage, payload);
  if (response) {
    const data = response.data.map(sn => {
      return {
        label: sn.name,
        value: sn.guid,
      };
    });
    yield put(equipmentActions.searchDeviceSNWithNameSuccess(data));
  } else {
    message.error(error);
  }
}

function* getDeviceDetail(payload) {
  const { response, error } = yield call(equipmentManageService.fetchDeviceInfo, payload);
  if (response) {
    yield put(equipmentActions.editEquipmentMess(response));
  } else {
    message.error(error || '查询设备详情失败');
  }
}

function* batchUpdateDevices({ data, successCallback, errorCallback }) {
  const { error } = yield call(infrastructureService.batchUpdateDevice, getParams(data));
  if (error) {
    errorCallback();
    message.error(error);
    return;
  }
  successCallback();
  message.success('批量更新成功！');
  yield put(fetchEquipmentListPage());
}

function* initializeEquipmentPage(urlSearchParams) {
  const {
    name,
    deviceType,
    vendor,
    productModel,
    guid,
    insertVersion,
    warrantyVendor,
    operationStatus,
    warrantyStatus,
    pagination,
    spaceGuidList,
  } = urlSearchParams;
  const hiddenSearchValues = { guid, insertVersion };
  const searchValues = {};

  if (name) {
    searchValues['name'] = { name: 'name', value: name };
  }
  if (deviceType) {
    searchValues['cascaderList'] = {
      name: 'cascaderList',
      value: {
        thirdCategorycode: deviceType,
      },
    };
  }
  if (spaceGuidList) {
    searchValues['spaceGuidList'] = { name: 'spaceGuidList', value: spaceGuidList.split(',') };
  }

  if (vendor) {
    searchValues['vendorModel'] = {
      name: 'vendorModel',
      value: [vendor, productModel],
    };
  }
  if (warrantyStatus) {
    searchValues['warrantyStatus'] = { name: 'warrantyStatus', value: warrantyStatus };
  }
  if (operationStatus) {
    searchValues['operationStatus'] = { name: 'operationStatus', value: operationStatus };
  }
  if (warrantyVendor) {
    searchValues['warrantyVendor'] = { name: 'warrantyVendor', value: warrantyVendor };
  }
  yield put(
    equipmentActions.initializeSearchValuesAndConditions({
      searchValues,
      hiddenSearchValues,
      pagination: pagination && JSON.parse(pagination),
    })
  );
  yield put(fetchEquipmentListPage());
}

function* setEquipmentPagePagination(newPagination) {
  yield put(equipmentActions.setEquipmentPagination(newPagination));
  yield put(fetchEquipmentListPage());
}

/******************************************************************************/
/******************************* WATCHERS *************************************/
/******************************************************************************/
export function* watchFetchEquipmentPage() {
  while (true) {
    const { payload } = yield take(fetchEquipmentListPage.type);
    yield fork(fetchEquipmentPage, payload);
  }
}

// 编辑
export function* watchFetchEditEquipment() {
  while (true) {
    const { payload } = yield take(fetchEditEquipment.type);
    yield fork(editEquipment, payload);
  }
}

// 导入
export function* watchFetchImportEquipment() {
  while (true) {
    const { payload } = yield take(fetchuploadEquipment.type);
    yield fork(importEquipment, payload);
  }
}

export function* watchImportLocationHref() {
  while (true) {
    const { payload } = yield take(importLocationHrefEquipmentList.type);
    yield fork(importCommint, payload);
  }
}

// 删除
export function* watchDeleteDevice() {
  while (true) {
    const { payload } = yield take(deleteDevice.type);
    yield fork(deleteEquipment, payload);
  }
}

export function* watchSearchDeviceSN() {
  while (true) {
    const { payload } = yield take(searchDeviceSN.type);
    yield fork(searchDeviceSNWithName, payload);
  }
}

export function* watchGetDeviceInfo() {
  while (true) {
    const { payload } = yield take(getDeviceInfo.type);
    yield fork(getDeviceDetail, payload);
  }
}

function* watchBatchUpdateDevices() {
  while (true) {
    const { payload } = yield take(batchUpdateDevicesActionCreator.type);
    yield fork(batchUpdateDevices, payload);
  }
}

function* watchInitializeEquipmentPage() {
  while (true) {
    const { payload } = yield take(initializeSearchValues.type);
    yield fork(initializeEquipmentPage, payload);
  }
}

function* watchSetEquipmentPagination() {
  while (true) {
    const { payload } = yield take(setEquipmentPagination.type);
    yield fork(setEquipmentPagePagination, payload);
  }
}

export default [
  fork(watchFetchEquipmentPage),
  fork(watchFetchEditEquipment),
  fork(watchFetchImportEquipment),
  fork(watchImportLocationHref),
  fork(watchDeleteDevice),
  fork(watchSearchDeviceSN),
  fork(watchGetDeviceInfo),
  fork(watchBatchUpdateDevices),
  fork(watchInitializeEquipmentPage),
  fork(watchSetEquipmentPagination),
];
