import flatten from 'lodash/flatten';
import trim from 'lodash/trim';
import {
  call,
  cancel,
  delay,
  fork,
  join,
  put,
  race,
  select,
  take,
  takeLatest,
} from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { YES_OR_NO_KEY_MAP } from '@manyun/dc-brain.legacy.constants';
import {
  formatPointSelectValue,
  getPointTextByPointType,
  splitFormattedPointValue,
} from '@manyun/dc-brain.legacy.pages/merged-monitor-config/new/utils';
import {
  batchUpdateMergedMonitorConfigsActionCreator,
  createNewMergedMonitorConfigActionCreator,
  deleteMergedMonitorConfigActionCreator,
  getBaseInfoActionCreator,
  getChildItemsActionCreator,
  getDataActionCreator,
  mergedMonitorConfigActions,
  resetSearchValuesActionCreator,
  setPaginationThenGetDataActionCreator,
  updateChildItemsActionCreator,
  updateMergedMonitorConfigActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/mergedMonitorConfigActions';
import { mergedMonitorConfigService } from '@manyun/dc-brain.legacy.services';

import {
  getSearchValuesAndPagination,
  getState4CU,
} from './../selectors/mergedMonitorConfigSelectors';

/***************************** Subroutines ************************************/

function getQ(pagination, searchValues) {
  const baseQ = { ...pagination };
  for (const [name, { value }] of Object.entries(searchValues)) {
    if (!['', undefined, null].includes(value)) {
      if (name === 'available') {
        baseQ[name] = Boolean(value);
      } else if (name === 'deviceTypeList') {
        baseQ[name] = value;
      } else if (name === 'lastOperatorName' && typeof value == 'object') {
        baseQ[name] = value.label;
      } else {
        if (typeof value == 'string') {
          baseQ[name] = trim(value);
        } else {
          baseQ[name] = value;
        }
      }
    }
  }
  return baseQ;
}

function* getData(shouldResetPageNum = true) {
  yield put(mergedMonitorConfigActions.request());
  if (shouldResetPageNum) {
    yield put(mergedMonitorConfigActions.resetPageNum());
  }
  const { searchValues, pagination } = yield select(getSearchValuesAndPagination);
  const q = getQ(pagination, searchValues);
  const { response, error } = yield call(mergedMonitorConfigService.fetchData, q);
  if (error) {
    message.error(error);
  } else {
    yield put(mergedMonitorConfigActions.setDataAndTotal(response));
    yield put(mergedMonitorConfigActions.resetSelectedRowKeys());
  }
}

function* resetSearchValues() {
  yield put(mergedMonitorConfigActions.resetSearchValuesAndPagination());
  yield put(getDataActionCreator(false));
}

function* setPagination(newPagination) {
  yield put(mergedMonitorConfigActions.setPagination(newPagination));
  yield put(getDataActionCreator(false));
}

function* batchUpdateMergedMonitorConfigs({ available, ids }) {
  const { error } = yield call(mergedMonitorConfigService.batchUpdateMergedMonitorConfigs, {
    available,
    mergeRuleIds: ids,
  });
  if (error) {
    message.error(error);
  } else {
    const txt = available ? '启用' : '停用';
    message.success(`成功${txt} ${ids.length} 项！`);
    yield put(getDataActionCreator(false));
  }
}

function* deleteMergedMonitorConfig(id) {
  const { error } = yield call(mergedMonitorConfigService.deleteMergedMonitorConfig, id);
  if (error) {
    message.error(error);
  } else {
    message.success('删除成功！');
    yield put(getDataActionCreator(false));
  }
}

function* createNewMergedMonitorConfig() {
  const partialState = yield select(getState4CU);
  const q = getQ4Creation(partialState);
  yield put(mergedMonitorConfigActions.setStepBtnLoading(true));
  const { error } = yield call(mergedMonitorConfigService.createNewMergedMonitorConfig, q);
  yield put(mergedMonitorConfigActions.setStepBtnLoading(false));
  if (error) {
    message.error(error);
  } else {
    yield put(mergedMonitorConfigActions.goToNextStep());
  }
}

function getBaseInfoPayload(step1FieldValues) {
  const { dataType, pointCode } = splitFormattedPointValue(
    step1FieldValues.formattedPointValue.value.key
  );

  return {
    name: step1FieldValues.name.value,
    deviceType: step1FieldValues.deviceType.value.code,
    available: Boolean(step1FieldValues.available.value),
    description: step1FieldValues.description.value,
    pointName: step1FieldValues.formattedPointValue.value.label,
    pointCode,
    expire: Number(step1FieldValues.expire.value),
    condition: dataType === 'AI' ? step1FieldValues.conditions.value.join(',') : 'EQUAL',
    conditionValue: dataType === 'DI' ? step1FieldValues.conditionValues.value.join(',') : null,
    // validLimits: dataType === 'DI' ? validLimits.join(';') : null,
  };
}

function getQ4Creation({ step1FieldValues, step2FieldValues, mergeScopeMap, childItemsMap }) {
  const baseInfo = getBaseInfoPayload(step1FieldValues);
  const mergePointList = getChildItemsPayload(step2FieldValues, mergeScopeMap, childItemsMap);

  return {
    ...baseInfo,
    mergePointList,
  };
}

function getChildItemsPayload(step2FieldValues, mergeScopeMap, childItemsMap) {
  return flatten(
    step2FieldValues.childItemDeviceTypes.value.map(deviceType => {
      const mergeScope = mergeScopeMap[deviceType];
      const points = childItemsMap[deviceType];
      return points.map(
        ({
          id = null, // 编辑才可能会有这个字段
          deviceType,
          name,
          pointCode,
          dataType: { code },
          conditions,
          conditionValues,
          validLimits,
        }) => ({
          id,
          deviceType,
          pointName: name,
          pointCode,
          mergeScope,
          condition: code === 'AI' ? conditions.join(',') : 'EQUAL',
          conditionValue: code === 'DI' ? conditionValues.join(',') : null,
          // validLimits: code === 'DI' ? validLimits.join(';') : null,
        })
      );
    })
  );
}

function* updateMergedMonitorConfig({ payload: { id } }) {
  const { step1FieldValues } = yield select(getState4CU);

  // 通过 `touched` 是否是 `true` 来判断用户有木有更改基本信息表单，
  // 如果未改，那么可以跳过 API 请求
  const shouldCallApi = Object.keys(step1FieldValues).some(
    fieldId => step1FieldValues[fieldId].touched === true
  );

  if (!shouldCallApi) {
    yield put(mergedMonitorConfigActions.goToNextStep());
    return;
  }

  const payload = getBaseInfoPayload(step1FieldValues);
  // https://github.com/redux-saga/redux-saga/issues/2071#issuecomment-631995647
  const task = yield fork(mergedMonitorConfigService.updateMergedMonitorConfig, {
    id: Number(id),
    ...payload,
  });

  let { loading, result } = yield race({
    loading: delay(250),
    result: join(task),
  });

  if (loading) {
    yield put(mergedMonitorConfigActions.setStepBtnLoading(true));
    result = yield join(task);
    yield put(mergedMonitorConfigActions.setStepBtnLoading(false));
  }

  const { error } = result;

  if (error) {
    message.error(error);
  } else {
    yield put(mergedMonitorConfigActions.goToNextStep());
  }
}

function* getChildItems(id) {
  const { response, error } = yield call(
    mergedMonitorConfigService.fetchMergedMonitorConfigChildItems,
    {
      id,
    }
  );
  if (response) {
    const deviceTypes = [];
    const childItemsMap = {};
    const mergeScopeMap = {};
    response.data.forEach(
      ({
        pointType,
        deviceType,
        pointName,
        pointCode,
        mergeScope,
        condition,
        conditionValue,
        ...rest
      }) => {
        if (!deviceTypes.includes(deviceType)) {
          deviceTypes.push(deviceType);
          childItemsMap[deviceType] = [];
          mergeScopeMap[deviceType] = mergeScope.code;
        }
        childItemsMap[deviceType].push({
          ...rest,
          deviceType,
          pointCode,
          // 展示时取这个字段作为被收敛测点的名称
          name: pointName,
          // <TreeNode /> 禁用复选框的判断会用到这个字段
          metaCode: deviceType + '_$$_' + pointCode,
          typeText: getPointTextByPointType(pointType.code),
          conditions: condition,
          conditionValues: Array.isArray(conditionValue)
            ? conditionValue.join(',').split(',')
            : conditionValue,
        });
      }
    );
    yield put(
      mergedMonitorConfigActions.updateStep2Infos({ deviceTypes, childItemsMap, mergeScopeMap })
    );
  } else {
    message.error(error);
  }
}

function* updateChildItems(id) {
  const { step2FieldValues, mergeScopeMap, childItemsMap } = yield select(getState4CU);
  const mergePointList = getChildItemsPayload(step2FieldValues, mergeScopeMap, childItemsMap);
  yield put(mergedMonitorConfigActions.setStepBtnLoading(true));
  const { error } = yield call(mergedMonitorConfigService.updateChildItems, {
    mergeRuleId: Number(id),
    mergePointList,
  });
  yield put(mergedMonitorConfigActions.setStepBtnLoading(false));
  if (error) {
    message.error(error);
  } else {
    yield put(mergedMonitorConfigActions.goToNextStep());
  }
}

function* getBaseInfo(id) {
  const { response, error } = yield call(
    mergedMonitorConfigService.fetchMergedMonitorConfigBaseInfo,
    id
  );
  if (response) {
    const {
      name,
      deviceType,
      condition,
      conditionValue,
      expire,
      pointName,
      pointCode,
      validLimits,
      available,
      description,
    } = response;
    const dataType = condition[0] === 'EQUAL' ? 'DI' : 'AI';
    const formattedPointValue = formatPointSelectValue({
      dataType: { code: dataType },
      pointCode,
      validLimits,
    });
    const step1FieldValues = {
      deviceType: { value: { code: deviceType, name: null } },
      formattedPointValue: { value: { key: formattedPointValue, label: pointName } },
      conditions: {
        value: Array.isArray(condition) ? condition : undefined,
      },
      conditionValues: {
        value: Array.isArray(conditionValue)
          ? conditionValue.join(',').split(',') /* API 返回的是数字，要转成字符串 */
          : undefined,
      },
      expire: {
        value: String(expire),
      },
      name: {
        value: name,
      },
      available: { value: available === true ? YES_OR_NO_KEY_MAP.YES : YES_OR_NO_KEY_MAP.NO },
      description: {
        value: typeof description == 'string' ? description : undefined,
      },
    };
    yield put(mergedMonitorConfigActions.updateStep1FieldValues(step1FieldValues));
  } else {
    message.error(error);
  }
}

/******************************************************************************/
/******************************* WATCHERS *************************************/
/******************************************************************************/

function* watchGetData() {
  while (true) {
    const { payload } = yield take(getDataActionCreator.type);
    const task = yield fork(getData, payload);
    const [reset] = yield race([take(resetSearchValuesActionCreator.type), task]);
    if (reset) {
      cancel(task);
    }
  }
}

function* watchResetSearchValues() {
  while (true) {
    yield take(resetSearchValuesActionCreator.type);
    yield fork(resetSearchValues);
  }
}

function* watchSetPagination() {
  while (true) {
    const { payload } = yield take(setPaginationThenGetDataActionCreator.type);
    yield fork(setPagination, payload);
  }
}

function* watchBatchUpdateMergedMonitorConfigs() {
  while (true) {
    const { payload } = yield take(batchUpdateMergedMonitorConfigsActionCreator.type);
    yield fork(batchUpdateMergedMonitorConfigs, payload);
  }
}

function* watchDeleteMergedMonitorConfig() {
  while (true) {
    const { payload } = yield take(deleteMergedMonitorConfigActionCreator.type);
    yield fork(deleteMergedMonitorConfig, payload);
  }
}

function* watchCreateNewMergedMonitorConfig() {
  yield takeLatest(createNewMergedMonitorConfigActionCreator.type, createNewMergedMonitorConfig);
}

function* watchUpdateMergedMonitorConfig() {
  yield takeLatest(updateMergedMonitorConfigActionCreator.type, updateMergedMonitorConfig);
}

function* watchGetChildItems() {
  while (true) {
    const { payload } = yield take(getChildItemsActionCreator.type);
    yield fork(getChildItems, payload);
  }
}

function* watchUpdateChildItems() {
  while (true) {
    const { payload } = yield take(updateChildItemsActionCreator.type);
    yield fork(updateChildItems, payload);
  }
}

function* watchGetBaseInfo() {
  while (true) {
    const { payload } = yield take(getBaseInfoActionCreator.type);
    yield fork(getBaseInfo, payload);
  }
}

export default [
  fork(watchGetData),
  fork(watchResetSearchValues),
  fork(watchSetPagination),
  fork(watchBatchUpdateMergedMonitorConfigs),
  fork(watchDeleteMergedMonitorConfig),
  fork(watchCreateNewMergedMonitorConfig),
  fork(watchUpdateMergedMonitorConfig),
  fork(watchGetChildItems),
  fork(watchUpdateChildItems),
  fork(watchGetBaseInfo),
];
