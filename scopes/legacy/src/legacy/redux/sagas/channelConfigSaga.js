import { call, cancel, fork, put, race, select, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { channelConfigService } from '@manyun/dc-brain.legacy.services';

import {
  batchUpdateChannelStatusActionCreator,
  channelConfigActionCreator,
  channelConfigActions,
  channelDeviceActionCreator,
  createChannelConfigActionCreator,
  resetDeviceSearchValuesActionCreator,
  resetSearchValuesActionCreator,
  setDevicePaginationThenGetDataActionCreator,
  setPaginationThenGetDataActionCreator,
  updateChannelConfigActionCreator,
} from '../actions/channelConfigActions';
import { getSearchValuesAndPagination } from './../selectors/channelConfigSelectors';

function getQ(pagination, searchValues) {
  const baseQ = { ...pagination };
  for (const [name, value] of Object.entries(searchValues)) {
    const val = value === '' ? null : value;
    if (name === 'condition') {
      baseQ[name] = val;
    } else if (name === 'deviceName') {
      baseQ[name] = val;
    } else if (name === 'channelId') {
      baseQ[name] = val;
    }
  }
  return baseQ;
}

//workers

function* getData(shouldResetPageNum = true) {
  yield put(channelConfigActions.request());
  if (shouldResetPageNum) {
    yield put(channelConfigActions.resetPageNum());
  }
  const { searchValues, pagination } = yield select(getSearchValuesAndPagination);
  const q = getQ(pagination, searchValues);
  const { response, error } = yield call(channelConfigService.fetchChannelConfigPage, q);
  if (error) {
    message.error(error);
  } else {
    yield put(channelConfigActions.setDataAndTotal(response));
    yield put(channelConfigActions.resetSelectedRowKeys());
  }
}

function* resetSearchValues() {
  yield put(channelConfigActions.resetSearchValuesAndPagination());
  yield put(channelConfigActionCreator(false));
}

function* setPagination(newPagination) {
  yield put(channelConfigActions.setPagination(newPagination));
  yield put(channelConfigActionCreator(false));
}

function* batchUpdateChannelConfigStatus({ channelStatus, channelIds }) {
  const { error } = yield call(channelConfigService.batchUpdateChannelStatus, {
    channelStatus,
    channelIds,
  });
  if (error) {
    message.error(error);
  } else {
    const txt = channelStatus === 'ON' ? '启用' : '停用';
    message.success(`成功${txt} ${channelIds.length} 项！`);
    yield put(channelConfigActionCreator(false));
  }
}

function* createChannelConfig(payload) {
  yield put(channelConfigActions.request());
  const { error } = yield call(channelConfigService.createChannelConfig, payload);
  if (error) {
    message.error(error);
  } else {
    yield put(channelConfigActionCreator(false));
  }
}

function* updateChannelConfig(payload) {
  yield put(channelConfigActions.request());
  const { error } = yield call(channelConfigService.updateChannelConfig, payload);
  if (error) {
    message.error(error);
  } else {
    yield put(channelConfigActionCreator(false));
  }
}

function* getDeviceData(shouldResetPageNum = true) {
  yield put(channelConfigActions.request());
  if (shouldResetPageNum) {
    yield put(channelConfigActions.resetPageNum());
  }
  const { searchValues, pagination } = yield select(getSearchValuesAndPagination);
  const q = getQ(pagination, searchValues);
  const { response, error } = yield call(channelConfigService.fetchChannelDeviceList, q);
  if (error) {
    message.error(error);
  } else {
    yield put(channelConfigActions.setDataAndTotal(response));
  }
}

function* resetDeviceSearchValues() {
  yield put(channelConfigActions.resetSearchValuesAndPagination());
  yield put(channelDeviceActionCreator(false));
}

function* setDevicePagination(newPagination) {
  yield put(channelConfigActions.setPagination(newPagination));
  yield put(channelDeviceActionCreator(false));
}

//watchers

function* watchGetData() {
  while (true) {
    const { payload } = yield take(channelConfigActionCreator.type);
    const task = yield fork(getData, payload);
    const [reset] = yield race([take(resetSearchValuesActionCreator.type), task]);
    if (reset) {
      cancel(task);
    }
  }
}

function* watchResetSearchValues() {
  while (true) {
    yield take(resetSearchValuesActionCreator.type);
    yield fork(resetSearchValues);
  }
}

function* watchSetPagination() {
  while (true) {
    const { payload } = yield take(setPaginationThenGetDataActionCreator.type);
    yield fork(setPagination, payload);
  }
}

function* watchBatchUpdatechannelConfigStatus() {
  while (true) {
    const { payload } = yield take(batchUpdateChannelStatusActionCreator.type);
    yield fork(batchUpdateChannelConfigStatus, payload);
  }
}

function* watchCreateChannelConfig() {
  while (true) {
    const { payload } = yield take(createChannelConfigActionCreator.type);
    yield fork(createChannelConfig, payload);
  }
}

function* watchUpdateChannelConfig() {
  while (true) {
    const { payload } = yield take(updateChannelConfigActionCreator.type);
    yield fork(updateChannelConfig, payload);
  }
}

function* watchGetDeviceData() {
  while (true) {
    const { payload } = yield take(channelDeviceActionCreator.type);
    const task = yield fork(getDeviceData, payload);
    const [reset] = yield race([take(resetDeviceSearchValuesActionCreator.type), task]);
    if (reset) {
      cancel(task);
    }
  }
}

function* watchResetDeviceSearchValues() {
  while (true) {
    yield take(resetDeviceSearchValuesActionCreator.type);
    yield fork(resetDeviceSearchValues);
  }
}

function* watchSetDevicePagination() {
  while (true) {
    const { payload } = yield take(setDevicePaginationThenGetDataActionCreator.type);
    yield fork(setDevicePagination, payload);
  }
}

export default [
  fork(watchGetData),
  fork(watchResetSearchValues),
  fork(watchSetPagination),
  fork(watchBatchUpdatechannelConfigStatus),
  fork(watchCreateChannelConfig),
  fork(watchUpdateChannelConfig),
  fork(watchGetDeviceData),
  fork(watchResetDeviceSearchValues),
  fork(watchSetDevicePagination),
];
