import { call, fork, put, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { powerManageService } from '@manyun/dc-brain.legacy.services';

import {
  fetchCreatePower, //  删除权限/菜单
  fetchDeletePower, // 分页查询菜单列表
  fetchPowerMenuPage,
  fetchPowerTree,
  fetchUpdatePower,
  powerActions,
} from '../actions/powerActions';

// 监听分页查询权限列表

/***************************** Subroutines ************************************/

function* fetchPowerPage(pageNo, pageSize, searchName, searchType) {
  yield put(powerActions.request());
  const { response, error } = yield call(powerManageService.fetchRolePage, {
    pageNo,
    pageSize,
    searchName,
    searchType,
  });
  if (response) {
    yield put(
      powerActions.success({
        list: response.data,
        total: response.total,
        pageNo,
        pageSize,
        searchName,
        searchType,
      })
    );
  } else {
    message.error(error || '权限列表请求失败');
    yield put(powerActions.failure());
  }
}

function* DeletePower(payload) {
  yield put(powerActions.request());
  const { permissionId, pageNo, pageSize, searchName, searchType } = payload;
  const { response, error } = yield call(powerManageService.fetchDeletePower, { permissionId });
  if (response) {
    message.success('删除权限成功');
    yield fork(fetchPowerPage, pageNo, pageSize, searchName, searchType);
  } else {
    message.error(error || '删除权限请求失败');
    yield put(powerActions.failure());
  }
}

function* createPower(payload) {
  const { pageSize, pageNo, ...permission } = payload;
  const { response, error } = yield call(powerManageService.fetchCreatePower, permission);
  if (response) {
    message.success('添加权限成功');

    yield put(powerActions.createPowerVisible());
    yield fork(fetchPowerPage, pageNo, pageSize, '', 'SYS');
  } else {
    message.error(error || '添加权限请求失败');
    yield put(powerActions.failure());
  }
}

function* updatePower(payload) {
  const { pageSize, pageNo, searchType, searchName } = payload;
  const { response, error } = yield call(powerManageService.fetchUpdatePower, payload.params);
  if (response) {
    message.success('修改权限成功');
    yield put(powerActions.editPowerVisible());
    yield fork(fetchPowerPage, pageNo, pageSize, searchName, searchType);
  } else {
    message.error(error || '修改权限请求失败');
    yield put(powerActions.failure());
  }
}

function* fetchPowerTreeSeclect() {
  const { response, error } = yield call(powerManageService.fetchAllPowerTreeSelect);
  if (response) {
    yield put(powerActions.fetchPowerTreeSeclectSuccess(response));
  } else {
    message.error(error || '资源树请求失败');
  }
}

/******************************************************************************/
/******************************* WATCHERS *************************************/
/******************************************************************************/

export function* watchFetchPowerPage() {
  while (true) {
    const { payload } = yield take(fetchPowerMenuPage.type);
    const { pageNo, pageSize, searchName, searchType } = payload;
    yield fork(fetchPowerPage, pageNo, pageSize, searchName, searchType);
  }
}

// 监听删除权限
export function* watchFetchDeletePower() {
  while (true) {
    const { payload } = yield take(fetchDeletePower.type);
    yield fork(DeletePower, payload);
  }
}

export function* watchFetchCreatePower() {
  while (true) {
    const { payload } = yield take(fetchCreatePower.type);
    yield fork(createPower, payload);
  }
}

export function* watchFetchUpdatePower() {
  while (true) {
    const { payload } = yield take(fetchUpdatePower.type);
    yield fork(updatePower, payload);
  }
}

export function* watchFetchPowerTreeSeclect() {
  while (true) {
    yield take(fetchPowerTree.type);
    yield fork(fetchPowerTreeSeclect);
  }
}

export default [
  fork(watchFetchPowerPage),
  fork(watchFetchDeletePower),
  fork(watchFetchCreatePower),
  fork(watchFetchUpdatePower),
  fork(watchFetchPowerTreeSeclect),
];
