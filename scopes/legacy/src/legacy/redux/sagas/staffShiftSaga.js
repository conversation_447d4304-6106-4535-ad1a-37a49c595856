import moment from 'moment';
import { all, call, fork, put, select, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { selectMyResources } from '@manyun/auth-hub.state.user';

import { NODE_TYPE_MAP } from '@manyun/dc-brain.legacy.components/relationship-connector/constants';
// import { TagColor } from '@manyun/dc-brain.legacy.pages/staff-shift-arrangement/constants';
import { closedVlaue } from '@manyun/dc-brain.legacy.pages/staff-shift-arrangement/shift-management/constants';
import { SHITF_SYS_TYPE_KEY_MAP } from '@manyun/dc-brain.legacy.pages/staff-shift-arrangement/shift-sys-management/constants';
import { redirectActionCreator } from '@manyun/dc-brain.legacy.redux/actions/routerActions';
import { staffShiftService } from '@manyun/dc-brain.legacy.services';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';

import {
  attGroupScheduleRuleUpdateActionCreator,
  createAttGroupActionCreator,
  createDutyGroupActionCreator,
  createRuleActionCreator,
  createShiftActionCreator,
  createShiftSysActionCreator,
  deleteAttGroupActionCreator,
  deleteDutyGroupActionCreator,
  deleteGroupScheduleActionCreator,
  deleteShiftActionCreator,
  deleteShiftSysActionCreator,
  editAttGroupActionCreator,
  editDutyGroupActionCreator,
  editRuleActionCreator,
  editShiftActionCreator,
  editShiftSysActionCreator,
  getAllShiftDataActionCreator, // 考勤组
  getAttGroupActionCreator,
  getAttGroupDetailActionCreator,
  getAttGroupListByShiftsActionCreator, // 打卡时间
  getCheckDataActionCreator, // 每日统计
  getDailyDataActionCreator, //班组
  getDutyGroupDataActionCreator,
  getDutyIsBeUsedActionCreator, // 考勤组规则配置
  getGroupScheduleActionCreator, // 月度汇总
  getMonthyDataActionCreator, // 原始记录
  getRecordDataActionCreator,
  getScheduleActionCreator,
  getScheduleInMonthActionCreator, //班次
  getShiftDataActionCreator, // 班制
  getShiftSysDataActionCreator,
  getStatutoryHolidayActionCreator,
  resetCheckSearchValuesActionCreator,
  resetDailySearchValuesActionCreator,
  resetMonthySearchValuesActionCreator,
  resetRecordSearchValuesActionCreator,
  setCheckPaginationThenGetDataActionCreator,
  setDailyPaginationThenGetDataActionCreator,
  setMonthyPaginationThenGetDataActionCreator,
  setRecordPaginationThenGetDataActionCreator,
  staffShiftActions,
} from '../actions/staffShiftActions';
import {
  geDateInSchedule,
  getAttGroupCreateOptions,
  getAttGroupScheduleEdit,
  getAttGroupScheduleRuleParams,
  getAttGroupSearchValuesAndPagination,
  getCheckSearchValuesAndPagination,
  getCreateShiftInfos,
  getCreateShiftSysOptions,
  getDailySearchValuesAndPagination,
  getDutyGroupCreateOptions,
  getDutyGroupEditOptions,
  getDutyGroupSearchValuesAndPagination,
  getEditShiftSysOptions,
  getGroupScheduleCreateOptions,
  getGroupScheduleEditOptions,
  getGroupScheduleSearchValuesAndPagination,
  getMonthlySearchValuesAndPagination,
  getRecordSearchValuesAndPagination,
  getShiftSearchValuesAndPagination,
  getShiftSysSearchValuesAndPagination,
} from '../selectors/staffShiftSelector';

/***************************** Subroutines ************************************/
function* getShiftData(shouldResetPageNum = true) {
  yield put(staffShiftActions.startShiftTableLoading());
  if (shouldResetPageNum) {
    yield put(staffShiftActions.resetShiftPageNum());
  }
  const { searchValues, pagination } = yield select(getShiftSearchValuesAndPagination);
  const { response, error } = yield call(staffShiftService.fetchShiftData, {
    ...searchValues,
    ...pagination,
  });
  if (error) {
    message.error(error);
    yield put(staffShiftActions.failure());
  } else {
    yield put(staffShiftActions.setDataAndTotal(response));
  }
}

function getQ(data) {
  const baseQ = {};
  for (const [key, { name, value }] of Object.entries(data)) {
    const val = value === '' ? null : value;
    if (name) {
      if (
        (name === 'onDutyTime' ||
          name === 'onDutyCheckStartTime' ||
          name === 'onDutyCheckEndTime' ||
          name === 'offDutyTime' ||
          name === 'offDutyCheckStartTime' ||
          name === 'offDutyCheckEndTime') &&
        value
      ) {
        const tmp = val.format('HH:mm');
        baseQ[name] = tmp;
      } else {
        baseQ[name] = val;
      }
    } else if (key && value !== null && value !== '') {
      baseQ[key] = val;
    }
  }
  return baseQ;
}

function* getCreateOrEditParams() {
  const { createOptions } = yield select(getCreateShiftInfos);
  const q = getQ(createOptions);
  let tmpHours = [];
  if (q.compensateHours.length) {
    tmpHours = q.compensateHours.map(item => {
      return `${item.lateGo},${item.lateArrive}`;
    });
  }
  const params = {
    dutyName: q.dutyName,
    offDutyTime: q.offDutyTime,
    onDutyTime: q.onDutyTime,
    dutyProperties: {
      onDutyBufferMinutes:
        q.enableBuffer && q.onDutyBufferMinutes[0] !== closedVlaue
          ? (q.onDutyBufferMinutes && q.onDutyBufferMinutes[0] ? q.onDutyBufferMinutes[0] : 0) +
            (q.onDutyBufferMinutes && q.onDutyBufferMinutes[1] ? q.onDutyBufferMinutes[1] : 0)
          : null,
      offDutyBufferMinutes:
        q.enableBuffer && q.offDutyBufferMinutes[0] !== closedVlaue
          ? (q.offDutyBufferMinutes && q.offDutyBufferMinutes[0] ? q.offDutyBufferMinutes[0] : 0) +
            (q.offDutyBufferMinutes && q.offDutyBufferMinutes[1] ? q.offDutyBufferMinutes[1] : 0)
          : null,
      onDutyOffsetMinutes:
        q.enableOffset && q.onDutyOffsetMinutes[0] !== closedVlaue
          ? (q.onDutyOffsetMinutes && q.onDutyOffsetMinutes[0] ? q.onDutyOffsetMinutes[0] : 0) +
            (q.onDutyOffsetMinutes && q.onDutyOffsetMinutes[1] ? q.onDutyOffsetMinutes[1] : 0)
          : null,
      offDutyOffsetMinutes:
        q.enableOffset && q.offDutyOffsetMinutes[0] !== closedVlaue
          ? (q.offDutyOffsetMinutes && q.offDutyOffsetMinutes[0] ? q.offDutyOffsetMinutes[0] : 0) +
            (q.offDutyOffsetMinutes && q.offDutyOffsetMinutes[1] ? q.offDutyOffsetMinutes[1] : 0)
          : null,

      offDutyCheckRange:
        q.isOffDutyCheck && q.offDutyCheckEndTime && q.offDutyCheckStartTime
          ? {
              endTime: q.offDutyCheckEndTime,
              startTime: q.offDutyCheckStartTime,
              startIsNextDay: createOptions.offDutyCheckRange.startIsNextDay,
              endIsNextDay: createOptions.offDutyCheckRange.endIsNextDay,
            }
          : null,
      onDutyCheckRange:
        q.isOnDutyCheck && q.onDutyCheckEndTime && q.onDutyCheckStartTime
          ? {
              endTime: q.onDutyCheckEndTime,
              startTime: q.onDutyCheckStartTime,
              startIsNextDay: createOptions.onDutyCheckRange.startIsNextDay,
              endIsNextDay: createOptions.onDutyCheckRange.endIsNextDay,
            }
          : null,
      restMinutes: q.restMinutes ? q.restMinutes : null,
      restRange: q.allowRest
        ? {
            startIsNextDay: createOptions.restRange.startIsNextDay,
            endIsNextDay: createOptions.restRange.endIsNextDay,
          }
        : null,
      allowRest: q.allowRest,
      compensateHours: tmpHours,
      enableBuffer: q.enableBuffer,
      enableCompensate: q.enableCompensate,
      enableOffset: q.enableOffset,
      enableOnDutyCheckRange: q.isOnDutyCheck,
      enableOffDutyCheckRange: q.isOffDutyCheck,
      allowContinuousWork: !q.allowContinuousWork,
      notAllowContinuousTime: q.allowContinuousWork ? Number(q.notAllowContinuousTime) * 60 : null,
    },
    id: createOptions.id,
    offIsNextDay: q.offIsNextDay,
  };
  return params;
}

function* createShift() {
  const params = yield getCreateOrEditParams();
  const { error } = yield call(staffShiftService.fetchCreateShift, params);
  if (error) {
    message.error(error);
    yield put(staffShiftActions.failure());
  } else {
    message.success('新建班次成功！');
    yield put(staffShiftActions.updateCreateShiftVisiable(false));
    yield put(staffShiftActions.resetCreateShiftOptions());
    yield put(getShiftDataActionCreator(false));
  }
}

function* deleteShift(payload) {
  yield put(staffShiftActions.startShiftTableLoading());
  const { error } = yield call(staffShiftService.fetchDeleteShift, payload);
  if (error) {
    message.error(error);
    yield put(staffShiftActions.failure());
  } else {
    message.success('删除班次成功！');
    yield put(getShiftDataActionCreator(false));
  }
}

function* getDutyIsBeUsed(payload) {
  const { params, confirmDelete } = payload;
  // 判断班次是否被排班使用
  const { error, response } = yield call(staffShiftService.fetchDutyIsBeUsed, params);
  if (error) {
    message.error(error);
  }
  // 请求成功 返回true 的时候 弹出确认框
  if (response) {
    confirmDelete(params);
  }
  // 请求成功 返回false 的时候可以直接删除
  if (response === false) {
    yield put(deleteShiftActionCreator(params));
  }
}

function* editShift() {
  const params = yield getCreateOrEditParams();
  const { error } = yield call(staffShiftService.fetchEditShift, params);
  if (error) {
    message.error(error);
    yield put(staffShiftActions.failure());
  } else {
    message.success('修改班次成功！');
    yield put(staffShiftActions.updateCreateShiftVisiable(false));
    yield put(staffShiftActions.resetCreateShiftOptions());
    yield put(getShiftDataActionCreator(false));
  }
}

//  班制
function* getShiftSysData(shouldResetPageNum = true) {
  yield put(staffShiftActions.startShiftSysTableLoading());
  if (shouldResetPageNum) {
    yield put(staffShiftActions.resetShiftSysPageNum());
  }
  const { searchValues, pagination } = yield select(getShiftSysSearchValuesAndPagination);
  const { response, error } = yield call(staffShiftService.fetchShiftSysData, {
    ...searchValues,
    ...pagination,
  });
  if (error) {
    message.error(error);
    yield put(staffShiftActions.failure());
  } else {
    yield put(staffShiftActions.setShiftSysDataAndTotal(response));
  }
}

function* deleteShiftsys(payload) {
  yield put(staffShiftActions.startShiftTableLoading());
  const { error } = yield call(staffShiftService.fetchDeleteShiftSys, payload);
  if (error) {
    message.error(error);
    yield put(staffShiftActions.failure());
  } else {
    message.success('删除班制成功！');
    yield put(getShiftSysDataActionCreator(false));
  }
}

function* getAllShiftData(payload) {
  const { error, response } = yield call(staffShiftService.fetchAllShift, payload);
  if (error) {
    message.error(error);
    yield put(staffShiftActions.failure());
  } else {
    yield put(staffShiftActions.saveAllShiftData(response.data));
  }
}

function getDailyDuty(type, week, period) {
  let dailyDuty = [];
  if (type === SHITF_SYS_TYPE_KEY_MAP.WEEK) {
    dailyDuty = week.workTableData.map(({ checked }) => {
      if (checked) {
        return week.checkedShiftConfirm.id;
      } else {
        return 0;
      }
    });
  } else {
    dailyDuty = period.workTableData.map(({ shiftId }) => shiftId);
  }
  return dailyDuty;
}

function getDutyIds(type, week, period) {
  let dutyIds = [];
  if (type === SHITF_SYS_TYPE_KEY_MAP.WEEK) {
    dutyIds = [week.checkedShiftConfirm.id];
  } else {
    dutyIds = period.periodShifts.value.map(({ id }) => id);
  }
  return dutyIds;
}

function* createShiftSys() {
  const { createOptions, period, week } = yield select(getCreateShiftSysOptions);
  const dailyDuty = getDailyDuty(createOptions.shiftsType.value, week, period);
  const dutys = getDutyIds(createOptions.shiftsType.value, week, period);
  const params = {
    shiftsName: createOptions.shiftsName.value,
    shiftsType: createOptions.shiftsType.value,
    periodDays: period.periodDays.value,
    dailyDuty,
    dutys,
    enableStatutoryHoliday: createOptions.enableStatutoryHoliday.value,
  };
  const { error } = yield call(staffShiftService.fetchCreateShiftSys, params);
  if (error) {
    message.error(error);
    yield put(staffShiftActions.failure());
  } else {
    message.success('新建班制成功！');
    yield put(staffShiftActions.updateShiftSysCreateVisiable());
    yield put(staffShiftActions.resetCreateShiftSysOptions());
    yield put(getShiftSysDataActionCreator(false));
  }
}

function* editShiftSys(payload) {
  const { editOptions } = yield select(getEditShiftSysOptions);
  const dailyDuty = getDailyDuty(
    editOptions.shiftsType.value,
    editOptions.week,
    editOptions.period
  );
  const dutys = getDutyIds(editOptions.shiftsType.value, editOptions.week, editOptions.period);
  const params = {
    id: editOptions.id,
    shiftsName: editOptions.shiftsName.value,
    shiftsType: editOptions.shiftsType.value,
    periodDays: editOptions.period.periodDays.value,
    dailyDuty,
    dutys,
    enableStatutoryHoliday: editOptions.week.enableStatutoryHoliday.value,
  };
  const { error } = yield call(staffShiftService.fetchEditShiftSys, params);
  if (error) {
    message.error(error);
    yield put(staffShiftActions.failure());
  } else {
    message.success('修改班制成功！');
    yield put(staffShiftActions.updateShiftSysCreateVisiable());
    yield put(getShiftSysDataActionCreator(false));
    if (editOptions.attGroupListByShifts.length) {
      const { scheduleConfirm } = payload;
      scheduleConfirm();
    }
  }
}

function* getDutyGroupData(shouldResetPageNum = true) {
  yield put(staffShiftActions.startDutyGroupTableLoading(true));
  if (shouldResetPageNum) {
    yield put(staffShiftActions.resetShiftSysPageNum());
  }
  const { searchValues, pagination } = yield select(getDutyGroupSearchValuesAndPagination);
  const { response, error } = yield call(staffShiftService.fetchDutyGroupData, {
    ...searchValues,
    ...pagination,
  });
  if (error) {
    message.error(error);
    yield put(staffShiftActions.failure());
  } else {
    yield put(staffShiftActions.setDutyGroupDataAndTotal(response));
  }
}

function* deleteDutyGroup(payload) {
  yield put(staffShiftActions.startDutyGroupTableLoading(true));
  const { error } = yield call(staffShiftService.fetchDeleteDutyGroup, payload);
  if (error) {
    message.error(error);
    yield put(staffShiftActions.failure());
  } else {
    message.success('删除班组成功！');
    yield put(getDutyGroupDataActionCreator(false));
  }
}

function* createDutyGroup() {
  const { create } = yield select(getDutyGroupCreateOptions);
  const params = {
    groupName: create.groupName.value,
    idcTag: create.idcBlockTag.value[0],
    blockTag: `${create.idcBlockTag.value[0]}.${create.idcBlockTag.value[1]}`,
    staffs: create.staffIds.value.map(staff => ({
      staffId: staff.id,
      teamLeader: staff.teamLeader,
    })),
  };
  yield put(staffShiftActions.setCreateDutyGroupSubmitLoading(true));
  const { error } = yield call(staffShiftService.fetchCreateDutyGroup, params);
  yield put(staffShiftActions.setCreateDutyGroupSubmitLoading(false));
  if (error) {
    message.error(error);
  } else {
    message.success('新建班组成功！');
    yield put(getDutyGroupDataActionCreator(true));
    yield put(staffShiftActions.resetCreateDutyGroupOptions());
    yield put(staffShiftActions.updateDutyGroupCreateVisiable(false));
  }
}

function* editDutyGroup() {
  const { edit } = yield select(getDutyGroupEditOptions);
  const params = {
    id: edit.id,
    groupName: edit.groupName.value,
    idcTag: edit.idcBlockTag.value[0],
    blockTag: `${edit.idcBlockTag.value[0]}.${edit.idcBlockTag.value[1]}`,
    staffs: edit.staffIds.value.map(staff => ({ staffId: staff.id, teamLeader: staff.teamLeader })),
  };
  yield put(staffShiftActions.setEditDutyGroupSubmitLoading(true));
  const { error } = yield call(staffShiftService.fetchEditDutyGroup, params);
  yield put(staffShiftActions.setEditDutyGroupSubmitLoading(false));
  if (error) {
    message.error(error);
  } else {
    message.success('修改班组成功！');
    yield put(getDutyGroupDataActionCreator(false));
    yield put(staffShiftActions.resetEditDutyGroupOptions());
    yield put(staffShiftActions.updateDutyGroupCreateVisiable(false));
  }
}

function* getGroupSchedule(shouldResetPageNum = true) {
  yield put(staffShiftActions.startGroupScheduleTableLoading(true));
  if (shouldResetPageNum) {
    yield put(staffShiftActions.resetShiftSysPageNum());
  }
  const { searchValues, pagination } = yield select(getGroupScheduleSearchValuesAndPagination);
  const { response, error } = yield call(staffShiftService.fetchAttRuleData, {
    ...searchValues,
    ...pagination,
  });
  if (error) {
    message.error(error);
    yield put(staffShiftActions.failure());
  } else {
    yield put(staffShiftActions.setGroupScheduleDataAndTotal(response));
  }
}

function* deleteGroupSchedule(payload) {
  yield put(staffShiftActions.startGroupScheduleTableLoading(true));
  const { error } = yield call(staffShiftService.fetchAttRuleGroup, payload);
  if (error) {
    message.error(error);
    yield put(staffShiftActions.failure());
  } else {
    message.success('删除排班规则成功！');
    yield put(getGroupScheduleActionCreator(false));
  }
}

function* createRule() {
  yield put(staffShiftActions.updateCreateOrEditLoading(true));
  const { create } = yield select(getGroupScheduleCreateOptions);
  const q = getQ(create);
  const commutingTimeInterval = q.commutingTimeInterval[0];
  // if (commutingTimeInterval[0] === null) {
  //   q.enableTimeInterval = false;
  //   q.commutingTimeInterval = 0;
  // } else {
  //   q.enableTimeInterval = true;
  //   q.commutingTimeInterval = commutingTimeInterval[0] + commutingTimeInterval[1];
  // }
  if (!commutingTimeInterval && commutingTimeInterval !== 0) {
    q.enableTimeInterval = false;
    q.commutingTimeInterval = null;
  } else if (commutingTimeInterval === 0) {
    q.enableTimeInterval = true;
    q.commutingTimeInterval = 0;
  } else {
    q.enableTimeInterval = true;
    q.commutingTimeInterval = commutingTimeInterval;
  }

  const { error } = yield call(staffShiftService.fetchRuleCreate, q);
  yield put(staffShiftActions.updateCreateOrEditLoading(false));
  if (error) {
    message.error(error);
  } else {
    message.success('新建排班规则成功！');
    yield put(staffShiftActions.updateGroupScheduleVisiable(false));
    yield put(staffShiftActions.resetCreateGroupScheduleOptions());
    yield put(getGroupScheduleActionCreator(false));
  }
}

function* editRule() {
  yield put(staffShiftActions.updateCreateOrEditLoading(true));
  const { edit } = yield select(getGroupScheduleEditOptions);
  const q = getQ(edit);
  const commutingTimeInterval = q.commutingTimeInterval[0];
  // if (commutingTimeInterval[0] === null) {
  //   q.enableTimeInterval = false;
  //   q.commutingTimeInterval = 0;
  // } else {
  //   q.enableTimeInterval = true;
  //   q.commutingTimeInterval = commutingTimeInterval[0] + commutingTimeInterval[1];
  // }
  if (!commutingTimeInterval && commutingTimeInterval !== 0) {
    q.enableTimeInterval = false;
    q.commutingTimeInterval = null;
  } else if (commutingTimeInterval === 0) {
    q.enableTimeInterval = true;
    q.commutingTimeInterval = 0;
  } else {
    q.enableTimeInterval = true;
    q.commutingTimeInterval = commutingTimeInterval;
  }
  const { error } = yield call(staffShiftService.fetchRuleEdit, q);
  yield put(staffShiftActions.updateCreateOrEditLoading(false));
  if (error) {
    message.error(error);
  } else {
    message.success('修改排班规则成功！');
    yield put(staffShiftActions.updateGroupScheduleVisiable(false));
    yield put(staffShiftActions.resetEditGroupScheduleOptions());
    yield put(getGroupScheduleActionCreator(false));
  }
}

function* getAttGroupData(shouldResetPageNum = true) {
  yield put(staffShiftActions.startAttGroupTableLoading(true));
  if (shouldResetPageNum) {
    yield put(staffShiftActions.resetAttGroupPageNum());
  }
  const { searchValues, pagination } = yield select(getAttGroupSearchValuesAndPagination);
  const { response, error } = yield call(staffShiftService.fetchAttGroupData, {
    ...searchValues,
    ...pagination,
  });
  if (error) {
    message.error(error);
    yield put(staffShiftActions.failure());
  } else {
    yield put(staffShiftActions.setAttGroupDataAndTotal(response));
  }
}

function* deleteAttGroup(payload) {
  yield put(staffShiftActions.startAttGroupTableLoading(true));
  const { error } = yield call(staffShiftService.fetchDeleteAttGroup, payload);
  if (error) {
    message.error(error);
    yield put(staffShiftActions.failure());
  } else {
    message.success('删除考勤组成功！');
    yield put(getAttGroupActionCreator(false));
  }
}

function* createAttGroup() {
  const { create } = yield select(getAttGroupCreateOptions);
  const params = {
    attName: create.attName.value,
    idcTag: create.idcBlockTag.value[0],
    blockTag: create.idcBlockTag.value[1],
    shiftsId: create.shifts.value.id,
    attRuleId: create.attRule.value,
    // userGroupIds: create.userGroupIds.value,
    dutyGroupIds: create.dutyGroupIdList.value,
    checkChannels: [create.checkChannels.value],
  };
  const { response, error } = yield call(staffShiftService.fetchAttGroupCreate, params);
  if (error) {
    message.error(error);
    yield put(staffShiftActions.failure());
  } else {
    const params = {
      id: response,
      groupName: create.attName.value,
    };
    const scheduleUrl = urls.generateAttGroupScheduleLocation(params);
    yield put(redirectActionCreator(scheduleUrl));
    yield put(staffShiftActions.resetCreateAttGroupOptions());
  }
}

function* getAttGroupDetail(payload) {
  const { attGroupId } = payload;
  const { error, response } = yield call(staffShiftService.fetchAttGroupDetail, { attGroupId });
  if (error) {
    message.error(error);
    yield put(staffShiftActions.failure());
  } else {
    const edit = {
      id: payload.attGroupId,
      attName: {
        value: response.attName,
      },
      idcBlockTag: {
        value: [response.idcTag, response.blockTag],
      },
      shifts: {
        value: response.shifts,
      },
      userGroupIds: {
        value: response.userGroupList.map(({ id }) => id),
      },

      attRule: {
        value: response.attRuleId,
      },
      dutyGroupIdList: {
        value: response.dutyGroupList.map(({ id }) => id),
        table: response.dutyGroupList,
      },
      defaultDutyGroupIdList: {
        value: response.dutyGroupList.map(({ id }) => id),
        table: response.dutyGroupList,
      },
      checkChannels: {
        value: response.checkChannels?.length ? response.checkChannels[0] : '',
      },
    };
    const schedule = {
      scheduleStartTime: response.attStartTime ? moment(response.attStartTime) : null,
      scheduleCycleMonths: response.scheduleCycleMonths,
    };
    yield put(staffShiftActions.saveAttGroupDetail({ edit, schedule }));
  }
}

function* getAttGroupSchedule(payload) {
  yield put(staffShiftActions.updateTimeModalButtonLoading(true));
  const params = {
    attGroupId: payload.attGroupId,
    scheduleCycleMonths: payload.scheduleCycleMonths,
    scheduleStartTime: payload.scheduleStartTime,
  };
  const { response, error } = yield call(staffShiftService.fetchAttSchedulePreview, params);
  if (error) {
    message.error(error);
    yield put(staffShiftActions.failure());
  } else {
    const users = {};
    const tmp = response.data.map(item => {
      users[item.scheduleUnicode] = item.scheduleStaffInfos;
      if (item.scheduleStaffInfos.length === 0) {
        return {
          ...item,
          title: item.dutyGroup.groupName,
          start: moment(item.scheduleStartTime).format('YYYY-MM-DD HH:mm'),
          end: moment(item.scheduleEndTime).format('YYYY-MM-DD HH:mm'),
          id: item.scheduleUnicode,
          display: 'block',
          groupId: item.dutyGroup.id,
          backgroundColor: 'transparent',
          borderColor: 'transparent',
          // textColor: 'default',
        };
      }

      return {
        ...item,
        title: item.dutyGroup.groupName,
        start: moment(item.scheduleStartTime).format('YYYY-MM-DD HH:mm'),
        end: moment(item.scheduleEndTime).format('YYYY-MM-DD HH:mm'),
        id: item.scheduleUnicode,
        display: 'block',
        groupId: item.dutyGroup.id,
        backgroundColor: 'transparent',
        borderColor: 'transparent',
        // textColor: TagColor[item.duty.id % 4],
      };
    });
    yield put(staffShiftActions.saveAttGroupScheduleDetail(tmp));
    yield put(staffShiftActions.saveAttGroupScheduleUsers(users));
    yield put(staffShiftActions.updateAttGroupSchedule(payload));
    yield put(staffShiftActions.updateTimeModalButtonLoading(false));
    if (payload.mode === 'schedule' || payload.mode === 'scheduleEdit_changeTime') {
      yield put(staffShiftActions.updateTimeSelectVisible(false));
    }
  }
}

function* updateAttGroupScheduleRule(payload) {
  const params = yield select(getAttGroupScheduleRuleParams);
  let p = {};
  if (payload.mode === 'schedule') {
    p = {
      attGroupId: payload.attGroupId,
      scheduleCycleMonths: payload.scheduleCycleMonths,
      attStartTime: payload.scheduleStartTime,
      scheduleStaffList: params.scheduleStaffList,
    };
  }
  if (payload.mode === 'scheduleEdit_changeTime') {
    p = {
      attGroupId: payload.attGroupId,
      scheduleCycleMonths: payload.scheduleCycleMonths,
      attStartTime: payload.scheduleStartTime,
    };
  }
  if (payload.mode === 'scheduleEdit_changeUser') {
    p = {
      attGroupId: params.attGroupId,
      scheduleStaffList: payload.scheduleStaffList
        ? payload.scheduleStaffList
        : params.scheduleStaffList,
    };
  }
  yield put(staffShiftActions.updateTimeModalButtonLoading(true));

  const { error } = yield call(staffShiftService.fetchAttGroupScheduleRuleUpdate, p);
  if (error) {
    message.error(error);
    yield put(staffShiftActions.updateTimeModalButtonLoading(false));
    yield put(staffShiftActions.failure());
  } else {
    message.success('排班成功！');
    if (payload.mode === 'schedule') {
      const p = {
        id: params.attGroupId,
        groupName: params.groupName,
      };
      const scheduleUrl = urls.generateAttGroupScheduleDetailLocation(p);
      yield put(redirectActionCreator(scheduleUrl));
    }
    if (payload.mode === 'scheduleEdit_changeTime' || payload.mode === 'scheduleEdit_changeUser') {
      const { dateInSchedule } = yield select(geDateInSchedule);
      yield put(getScheduleInMonthActionCreator(dateInSchedule));
      yield put(getAttGroupDetailActionCreator({ attGroupId: params.attGroupId }));
      yield put(staffShiftActions.resetScheduleStaffList([]));
    }
    yield put(staffShiftActions.updateTimeSelectVisible(false));
    yield put(staffShiftActions.updateTimeModalButtonLoading(false));
  }
}

function* editAttGroup(payload) {
  const { edit } = yield select(getAttGroupScheduleEdit);
  const params = {
    id: edit.id,
    attName: edit.attName.value,
    idcTag: edit.idcBlockTag.value[0],
    blockTag: edit.idcBlockTag.value[1],
    shiftsId: edit.shifts.value.id,
    attRuleId: edit.attRule.value,
    // userGroupIds: edit.userGroupIds.value,
    dutyGroupIds: edit.dutyGroupIdList.value,
    checkChannels: [edit.checkChannels.value],
  };

  yield put(staffShiftActions.updateTimeModalButtonLoading(true));
  const { error } = yield call(staffShiftService.fetchAttGroupEdit, params);
  if (error) {
    message.error(error);
    yield put(staffShiftActions.updateTimeModalButtonLoading(false));
    yield put(staffShiftActions.failure());
  } else {
    const p = {
      id: edit.id,
      groupName: edit.attName.value,
    };
    //有参数则更新排班
    if (payload) {
      const p = {
        attGroupId: payload.attGroupId,
        scheduleCycleMonths: payload.scheduleCycleMonths,
        attStartTime: payload.scheduleStartTime,
      };
      const { error } = yield call(staffShiftService.fetchAttGroupScheduleRuleUpdate, p);
      if (error) {
        message.error(error);
        yield put(staffShiftActions.failure());
      } else {
        message.success('排班成功！');
        yield put(staffShiftActions.updateTimeSelectVisible(false));
      }
    }
    yield put(staffShiftActions.updateTimeModalButtonLoading(false));
    yield put(redirectActionCreator(urls.generateAttGroupScheduleEditLocation(p)));
  }
}

function* getScheduleInMonth(payload) {
  const { edit } = yield select(getAttGroupScheduleEdit);
  let dutys;
  const calls = [call(staffShiftService.fetchAttGroupScheduleInMonth, payload)];
  if (!edit.shifts.value || !edit.shifts.value.dutys) {
    calls.push(call(staffShiftService.fetchAttGroupDetail, { attGroupId: payload.attGroupId }));
  } else {
    dutys = edit.shifts.value.dutys;
  }
  const [schedule, attGroupInfo] = yield all(calls);
  if (attGroupInfo && attGroupInfo.response) {
    // eslint-disable-next-line no-unused-vars
    dutys = attGroupInfo.response.shifts.dutys;
  }
  const { error, response } = schedule;
  if (error) {
    message.error(error);
    yield put(staffShiftActions.failure());
  } else {
    const users = {};
    const tmp = response.data.map(item => {
      users[item.scheduleUnicode] = item.scheduleStaffInfos;
      if (item.scheduleStaffInfos.length === 0) {
        return {
          ...item,
          title: item.dutyGroup.groupName,
          start: moment(item.scheduleStartTime).format('YYYY-MM-DD HH:mm'),
          end: moment(item.scheduleEndTime).format('YYYY-MM-DD HH:mm'),
          id: item.scheduleUnicode,
          display: 'block',
          backgroundColor: 'transparent',
          borderColor: 'transparent',
          groupId: item.dutyGroup.id,
        };
      }

      return {
        ...item,
        title: item.dutyGroup.groupName,
        start: moment(item.scheduleStartTime).format('YYYY-MM-DD HH:mm'),
        end: moment(item.scheduleEndTime).format('YYYY-MM-DD HH:mm'),
        id: item.scheduleUnicode,
        display: 'block',
        backgroundColor: 'transparent',
        borderColor: 'transparent',
        groupId: item.dutyGroup.id,
      };
    });
    yield put(staffShiftActions.saveAttGroupScheduleDetail(tmp));
    yield put(staffShiftActions.saveAttGroupScheduleUsers(users));
    yield put(staffShiftActions.saveDateInSchedule(payload));
  }
}

function getMonthlyQ(pagination, searchValues) {
  const baseQ = { ...pagination };
  for (const [, { name, value }] of Object.entries(searchValues)) {
    const val = value === '' ? null : value;
    if (name === 'staffId' && value) {
      baseQ[name] = val ? val.value : null;
    } else if (name === 'IdcBlock' && Array.isArray(value)) {
      const idcGuidList = [];
      const blockGuidList = [];
      value.forEach(area => {
        if (area.includes('.')) {
          blockGuidList.push(area);
        } else {
          idcGuidList.push(area);
        }
      });
      baseQ.idcTagList = idcGuidList;
      baseQ.blockTagList = blockGuidList;
    } else if (name === 'dateRange' && value.length) {
      baseQ.startDate = val[0] ? val[0].startOf('day').valueOf() : null;
      baseQ.endDate = val[1] ? val[1].endOf('day').valueOf() : null;
    } else {
      baseQ[name] = val;
    }
  }
  return baseQ;
}

function* getMonthlyData({ shouldResetPageNum = true, authorized }) {
  yield put(staffShiftActions.startMonthlyTableLoading(true));
  if (shouldResetPageNum) {
    yield put(staffShiftActions.resetShiftMonthlyPageNum());
  }
  const { searchValues, pagination } = yield select(getMonthlySearchValuesAndPagination);
  let q = getMonthlyQ(pagination, searchValues);
  if (!authorized) {
    const currentUserResources = yield select(selectMyResources);
    if (currentUserResources) {
      const blockTagList = currentUserResources
        .filter(({ type }) => type === NODE_TYPE_MAP.BLOCK)
        .map(({ code }) => code);
      q = { ...q, blockTagList, idcTagList: [] };
    }
  }
  const { response, error } = yield call(staffShiftService.fetchMonthlyList, q);
  if (error) {
    message.error(error);
    yield put(staffShiftActions.failure());
  } else {
    yield put(staffShiftActions.setMonthlyDataAndTotal(response));
  }
}

function* setMonthyPaginationThenGetData({ authorized, ...newPagination }) {
  yield put(staffShiftActions.setMonthlyPagination(newPagination));
  yield put(getMonthyDataActionCreator({ shouldResetPageNum: false, authorized }));
}
function* resetMonthySearchValues({ authorized }) {
  yield put(staffShiftActions.resetMonthySearchValues());
  yield put(getMonthyDataActionCreator({ shouldResetPageNum: true, authorized }));
}

function* getDailyData({ shouldResetPageNum = true, authorized }) {
  yield put(staffShiftActions.startDailyTableLoading(true));
  if (shouldResetPageNum) {
    yield put(staffShiftActions.resetShiftDailyPageNum());
  }
  const { searchValues, pagination } = yield select(getDailySearchValuesAndPagination);
  let q = getMonthlyQ(pagination, searchValues);
  if (!authorized) {
    const currentUserResources = yield select(selectMyResources);
    if (currentUserResources) {
      const blockTagList = currentUserResources
        .filter(({ type }) => type === NODE_TYPE_MAP.BLOCK)
        .map(({ code }) => code);
      q = { ...q, blockTagList, idcTagList: [] };
    }
  }
  const { response, error } = yield call(staffShiftService.fetchDailyList, q);
  if (error) {
    message.error(error);
    yield put(staffShiftActions.failure());
  } else {
    let newData = [];
    response.data.forEach(item => {
      if (!newData.length) {
        newData = [...newData, { ...item, mergeRowsKey: item.id, merge: true }];
        return;
      }
      const tmp = newData.filter(
        ({ staffId, scheduleDate }) =>
          staffId === item.staffId && scheduleDate === item.scheduleDate
      );
      if (!tmp.length) {
        newData = [...newData, { ...item, mergeRowsKey: item.id, merge: true }];
        return;
      }

      newData = [...newData, { ...item, mergeRowsKey: tmp[0].mergeRowsKey }];
    });
    yield put(staffShiftActions.setDailyDataAndTotal({ ...response, data: newData }));
  }
}

function* setDailyPaginationThenGetData({ authorized, ...newPagination }) {
  yield put(staffShiftActions.setDailyPagination(newPagination));
  yield put(getDailyDataActionCreator({ shouldResetPageNum: false, authorized }));
}

function* resetDailySearchValues({ authorized }) {
  yield put(staffShiftActions.resetDailySearchValues());
  yield put(getDailyDataActionCreator({ shouldResetPageNum: true, authorized }));
}

function* getCheckData(shouldResetPageNum = true) {
  yield put(staffShiftActions.startCheckTableLoading(true));
  if (shouldResetPageNum) {
    yield put(staffShiftActions.resetCheckPageNum());
  }
  const { searchValues, pagination } = yield select(getCheckSearchValuesAndPagination);
  const q = getMonthlyQ(pagination, searchValues);
  const { response, error } = yield call(staffShiftService.fetchCheckList, q);
  if (error) {
    message.error(error);
    yield put(staffShiftActions.failure());
  } else {
    yield put(staffShiftActions.setCheckDataAndTotal(response));
  }
}

function* setCheckPaginationThenGetData(newPagination) {
  yield put(staffShiftActions.setCheckPagination(newPagination));
  yield put(getCheckDataActionCreator(false));
}

function* resetCheckSearchValues() {
  yield put(staffShiftActions.resetCheckSearchValues());
  yield put(getCheckDataActionCreator(true));
}

function* getRecordData({ shouldResetPageNum = true, authorized }) {
  yield put(staffShiftActions.startRecordTableLoading(true));
  if (shouldResetPageNum) {
    yield put(staffShiftActions.resetRecordPageNum());
  }
  const { searchValues, pagination } = yield select(getRecordSearchValuesAndPagination);
  let q = getMonthlyQ(pagination, searchValues);
  if (!authorized) {
    const currentUserResources = yield select(selectMyResources);
    if (currentUserResources) {
      const blockTagList = currentUserResources
        .filter(({ type }) => type === NODE_TYPE_MAP.BLOCK)
        .map(({ code }) => code);
      q = { ...q, blockTagList, idcTagList: [] };
    }
  }
  const { response, error } = yield call(staffShiftService.fetchRecordList, q);
  if (error) {
    message.error(error);
    yield put(staffShiftActions.failure());
  } else {
    yield put(staffShiftActions.setRecordDataAndTotal(response));
  }
}

function* setRecordPaginationThenGetData({ authorized, ...newPagination }) {
  yield put(staffShiftActions.setRecordPagination(newPagination));
  yield put(getRecordDataActionCreator({ shouldResetPageNum: false, authorized }));
}
function* resetRecordSearchValues({ authorized }) {
  yield put(staffShiftActions.resetRecordSearchValues());
  yield put(getRecordDataActionCreator({ shouldResetPageNum: true, authorized }));
}

function* getAttGroupListByShifts(payload) {
  const { response, error } = yield call(staffShiftService.fetchAttGroupListByShifts, payload);
  if (error) {
    message.error(error);
  } else {
    yield put(staffShiftActions.saveAttGroupListByShifts(response.data));
  }
}

function* getStatutoryHoliday(payload) {
  const { response, error } = yield call(staffShiftService.fetchStatutoryHoliday, payload);
  if (error) {
    message.error(error);
  } else {
    if (!response || !response.statutoryHolidays) {
      return;
    }
    yield put(staffShiftActions.saveStatutoryHolidays(response.statutoryHolidays));
  }
}

/******************************************************************************/
/******************************* WATCHERS *************************************/
/******************************************************************************/

function* watchGetShiftData() {
  while (true) {
    const { payload } = yield take(getShiftDataActionCreator.type);
    yield fork(getShiftData, payload);
  }
}

function* watchCreateShift() {
  while (true) {
    yield take(createShiftActionCreator.type);
    yield fork(createShift);
  }
}

function* watchDeleteShift() {
  while (true) {
    const { payload } = yield take(deleteShiftActionCreator.type);
    yield fork(deleteShift, payload);
  }
}

function* watchGetDutyIsBeUsed() {
  while (true) {
    const { payload } = yield take(getDutyIsBeUsedActionCreator.type);
    yield fork(getDutyIsBeUsed, payload);
  }
}

function* watchEditShift() {
  while (true) {
    const { payload } = yield take(editShiftActionCreator.type);
    yield fork(editShift, payload);
  }
}

function* watchGetShiftSysData() {
  while (true) {
    const { payload } = yield take(getShiftSysDataActionCreator.type);
    yield fork(getShiftSysData, payload);
  }
}

function* watchDeleteShiftSys() {
  while (true) {
    const { payload } = yield take(deleteShiftSysActionCreator.type);
    yield fork(deleteShiftsys, payload);
  }
}

function* watchGetAllShiftData() {
  while (true) {
    const { payload } = yield take(getAllShiftDataActionCreator.type);
    yield fork(getAllShiftData, payload);
  }
}

function* watchCreateShiftSys() {
  while (true) {
    const { payload } = yield take(createShiftSysActionCreator.type);
    yield fork(createShiftSys, payload);
  }
}

function* watchEditShiftSys() {
  while (true) {
    const { payload } = yield take(editShiftSysActionCreator.type);
    yield fork(editShiftSys, payload);
  }
}

function* watchGetDutyGroupData() {
  while (true) {
    const { payload } = yield take(getDutyGroupDataActionCreator.type);
    yield fork(getDutyGroupData, payload);
  }
}

function* watchDeleteDutyGroup() {
  while (true) {
    const { payload } = yield take(deleteDutyGroupActionCreator.type);
    yield fork(deleteDutyGroup, payload);
  }
}

function* watchCreateDutyGroup() {
  while (true) {
    const { payload } = yield take(createDutyGroupActionCreator.type);
    yield fork(createDutyGroup, payload);
  }
}

function* watchEditDutyGroup() {
  while (true) {
    const { payload } = yield take(editDutyGroupActionCreator.type);
    yield fork(editDutyGroup, payload);
  }
}

function* watchGetGroupSchedule() {
  while (true) {
    const { payload } = yield take(getGroupScheduleActionCreator.type);
    yield fork(getGroupSchedule, payload);
  }
}

function* watchDeleteGroupSchedule() {
  while (true) {
    const { payload } = yield take(deleteGroupScheduleActionCreator.type);
    yield fork(deleteGroupSchedule, payload);
  }
}

function* watchCreateRule() {
  while (true) {
    const { payload } = yield take(createRuleActionCreator.type);
    yield fork(createRule, payload);
  }
}

function* watchEditRule() {
  while (true) {
    const { payload } = yield take(editRuleActionCreator.type);
    yield fork(editRule, payload);
  }
}

function* watchGetAttGroupData() {
  while (true) {
    const { payload } = yield take(getAttGroupActionCreator.type);
    yield fork(getAttGroupData, payload);
  }
}

function* watchDeleteAttGroup() {
  while (true) {
    const { payload } = yield take(deleteAttGroupActionCreator.type);
    yield fork(deleteAttGroup, payload);
  }
}

function* watchCreateAttGroup() {
  while (true) {
    yield take(createAttGroupActionCreator.type);
    yield fork(createAttGroup);
  }
}

function* watchGetAttGroupDetail() {
  while (true) {
    const { payload } = yield take(getAttGroupDetailActionCreator.type);
    yield fork(getAttGroupDetail, payload);
  }
}

function* watchGetAttGroupSchedule() {
  while (true) {
    const { payload } = yield take(getScheduleActionCreator.type);
    yield fork(getAttGroupSchedule, payload);
  }
}

function* watchAttGroupScheduleRuleUpdate() {
  while (true) {
    const { payload } = yield take(attGroupScheduleRuleUpdateActionCreator.type);
    yield fork(updateAttGroupScheduleRule, payload);
  }
}

function* watchEditAttGroup() {
  while (true) {
    const { payload } = yield take(editAttGroupActionCreator.type);
    yield fork(editAttGroup, payload);
  }
}

function* watchGetScheduleInMonth() {
  while (true) {
    const { payload } = yield take(getScheduleInMonthActionCreator.type);
    yield fork(getScheduleInMonth, payload);
  }
}

function* watchGetMonthyData() {
  while (true) {
    const { payload } = yield take(getMonthyDataActionCreator.type);
    yield fork(getMonthlyData, payload);
  }
}

function* watchSetMonthyPaginationThenGetData() {
  while (true) {
    const { payload } = yield take(setMonthyPaginationThenGetDataActionCreator.type);
    yield fork(setMonthyPaginationThenGetData, payload);
  }
}

function* watchResetMonthySearchValues() {
  while (true) {
    const { payload } = yield take(resetMonthySearchValuesActionCreator.type);
    yield fork(resetMonthySearchValues, payload);
  }
}

function* watchGetDailyData() {
  while (true) {
    const { payload } = yield take(getDailyDataActionCreator.type);
    yield fork(getDailyData, payload);
  }
}

function* watchGetCheckData() {
  while (true) {
    const { payload } = yield take(getCheckDataActionCreator.type);
    yield fork(getCheckData, payload);
  }
}

function* watchSetDailyPaginationThenGetData() {
  while (true) {
    const { payload } = yield take(setDailyPaginationThenGetDataActionCreator.type);
    yield fork(setDailyPaginationThenGetData, payload);
  }
}

function* watchResetDailySearchValues() {
  while (true) {
    const { payload } = yield take(resetDailySearchValuesActionCreator.type);
    yield fork(resetDailySearchValues, payload);
  }
}

function* watchSetCheckPaginationThenGetData() {
  while (true) {
    const { payload } = yield take(setCheckPaginationThenGetDataActionCreator.type);
    yield fork(setCheckPaginationThenGetData, payload);
  }
}

function* watchResetCheckSearchValues() {
  while (true) {
    const { payload } = yield take(resetCheckSearchValuesActionCreator.type);
    yield fork(resetCheckSearchValues, payload);
  }
}

function* watchGetRecordData() {
  while (true) {
    const { payload } = yield take(getRecordDataActionCreator.type);
    yield fork(getRecordData, payload);
  }
}

function* watchResetRecordSearchValues() {
  while (true) {
    const { payload } = yield take(resetRecordSearchValuesActionCreator.type);
    yield fork(resetRecordSearchValues, payload);
  }
}

function* watchSetRecordPaginationThenGetData() {
  while (true) {
    const { payload } = yield take(setRecordPaginationThenGetDataActionCreator.type);
    yield fork(setRecordPaginationThenGetData, payload);
  }
}

function* watchGetAttGroupListByShifts() {
  while (true) {
    const { payload } = yield take(getAttGroupListByShiftsActionCreator.type);
    yield fork(getAttGroupListByShifts, payload);
  }
}

function* watchGetStatutoryHoliday() {
  while (true) {
    const { payload } = yield take(getStatutoryHolidayActionCreator.type);
    yield fork(getStatutoryHoliday, payload);
  }
}

export default [
  fork(watchGetShiftData),
  fork(watchCreateShift),
  fork(watchDeleteShift),
  fork(watchGetDutyIsBeUsed),
  fork(watchEditShift),
  fork(watchGetShiftSysData),
  fork(watchDeleteShiftSys),
  fork(watchGetAllShiftData),
  fork(watchCreateShiftSys),
  fork(watchEditShiftSys),
  fork(watchGetDutyGroupData),
  fork(watchDeleteDutyGroup),
  fork(watchCreateDutyGroup),
  fork(watchEditDutyGroup),
  fork(watchGetGroupSchedule),
  fork(watchDeleteGroupSchedule),
  fork(watchCreateRule),
  fork(watchEditRule),
  fork(watchGetAttGroupData),
  fork(watchDeleteAttGroup),
  fork(watchCreateAttGroup),
  fork(watchGetAttGroupDetail),
  fork(watchGetAttGroupSchedule),
  fork(watchAttGroupScheduleRuleUpdate),
  fork(watchEditAttGroup),
  fork(watchGetScheduleInMonth),
  fork(watchGetMonthyData),
  fork(watchSetMonthyPaginationThenGetData),
  fork(watchResetMonthySearchValues),
  fork(watchGetDailyData),
  fork(watchGetCheckData),
  fork(watchSetDailyPaginationThenGetData),
  fork(watchResetDailySearchValues),
  fork(watchSetCheckPaginationThenGetData),
  fork(watchResetCheckSearchValues),
  fork(watchGetRecordData),
  fork(watchResetRecordSearchValues),
  fork(watchSetRecordPaginationThenGetData),
  fork(watchGetAttGroupListByShifts),
  fork(watchGetStatutoryHoliday),
];
