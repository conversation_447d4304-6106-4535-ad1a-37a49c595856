import { call, fork, put, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';
import { generateTreeData } from '@manyun/base-ui.util.generate-tree-data';

import { fetchPermissionsByRoleId } from '@manyun/iam.service.fetch-permissions-by-role-id';

import { getMenusActionCreator, layoutActions } from '../actions/layoutActions';

function* getMenus() {
  const roleCode = localStorage.getItem('roleCode');
  const { error, data } = yield call(fetchPermissionsByRoleId, {
    webSite: 'DCBASE',
    roleCode,
    permissionTypes: ['MENU', 'PAGE'],
  });
  if (error) {
    console.warn(error);
    message.error('获取菜单数据出错，请尝试刷新页面或联系技术支持！');
    return;
  }
  if (error) {
    message.error(error.message);
    return;
  }
  const menus = generateTreeData(data, {
    key: 'id',
    parentKey: 'parentId',
    isRootNode: permission => permission.code === 'menuRoot',
    filterNode: (permission, _, depth) =>
      (depth < 3 && permission.type === 'MENU') ||
      // 显示 3 级菜单，无论第 3 级的权限类型是菜单或页面
      (depth === 3 && ['MENU', 'PAGE'].includes(permission.type)),
    getNode: (permission, children) => ({
      metaCode: permission.code,
      metaName: permission.name,
      remarks: permission.remarks,
      children: children === null ? undefined : children,
    }),
  });
  if (menus.length > 0 && menus[0].children) {
    yield put(layoutActions.menuListSuccess(menus[0].children));
  }
}

// watchers

function* watchGetMenus() {
  while (true) {
    yield take(getMenusActionCreator.type);
    yield fork(getMenus);
  }
}

export default [fork(watchGetMenus)];
