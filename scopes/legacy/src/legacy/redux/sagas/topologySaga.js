import { call, fork, put, take } from 'redux-saga/effects';

import { idcWorkbenchServices } from '@manyun/dc-brain.legacy.services';

import { getBlocksActionCreator, topologyActions } from './../actions/topologyActions';

function* getBlocks({ idc, initialBlock }) {
  const { response } = yield call(idcWorkbenchServices.fetchBlockRoom, idc);
  if (!response) {
    return;
  }
  const { blockRoomTags } = response;
  if (blockRoomTags === null) {
    return;
  }
  const blocks = Object.keys(blockRoomTags).sort();
  yield put(topologyActions.setBlocks({ blocks, initialBlock }));
}

/******************************************************************************/
/******************************* WATCHERS *************************************/
/******************************************************************************/

function* watchGetBlocks() {
  while (true) {
    const { payload } = yield take(getBlocksActionCreator.type);
    yield fork(getBlocks, payload);
  }
}

export default [fork(watchGetBlocks)];
