import { trim } from 'lodash';
import { call, cancel, fork, put, race, select, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { approveCenterService } from '@manyun/dc-brain.legacy.services';

import {
  approveCenterActions,
  getApproveCenterListActionCreator,
  resetSearchValuesActionCreator,
  setPaginationThenGetDataActionCreator,
  setSearchValuesActionCreator,
} from '../actions/approveCenterActions';
import { getSearchValuesAndPagination } from './../selectors/approveCenterSeletcors';

function getQ(pagination, searchValues) {
  const baseQ = { ...pagination };
  for (const [, { name, value }] of Object.entries(searchValues)) {
    const val = value === '' ? null : value;
    if (name === 'instId') {
      baseQ[name] = val;
    } else if (name === 'instStatusList') {
      baseQ[name] = val;
    } else if (name === 'title') {
      baseQ[name] = val;
    } else if (name === 'bizType') {
      baseQ[name] = val;
    } else if (name === 'bizId') {
      baseQ[name] = val;
    } else if (name === 'blockGuid') {
      if (val.length === 1) {
        baseQ.idcTag = val[0];
      }
      if (val.length === 2) {
        baseQ[name] = `${val[0]}.${val[1]}`;
      }
    } else if (name === 'startTime') {
      baseQ.createTimeStart = val[0]?.startOf('day');
      baseQ.createTimeEnd = val[1]?.endOf('day');
    } else if (name === 'endTime') {
      baseQ.finishTimeStart = val[0]?.startOf('day');
      baseQ.finishTimeEnd = val[1]?.endOf('day');
    } else if (name === 'handler') {
      baseQ[name] = val;
    } else if (name === 'creatorId') {
      baseQ[name] = val.id;
    } else {
      baseQ[name] = trim(val);
    }
  }
  return baseQ;
}

// workers

function* getData(shouldResetPageNum = true) {
  yield put(approveCenterActions.request());
  if (shouldResetPageNum) {
    yield put(approveCenterActions.resetPageNum());
  }
  const { searchValues, pagination, type } = yield select(getSearchValuesAndPagination);
  const q = getQ(pagination, searchValues);
  let result;
  if (type !== 'CC') {
    if (type === 'MINE') {
      result = yield call(approveCenterService.fetchApproveCenterMinePage, q);
    } else if (type === 'ALREADY') {
      result = yield call(approveCenterService.fetchApproveCenterAlreadyPage, q);
    }

    // else if (type === 'CC') {
    //   result = yield call(fetchCcToMeTodos, { ...q, isRead: '1' });
    //   result.response = result.data;
    // }
    else if (type === 'WAIT') {
      result = yield call(approveCenterService.fetchApproveCenterWaitPage, q);
    }
    const { response, error } = result;
    if (error) {
      message.error(error);
      yield put(approveCenterActions.failure());
    } else {
      yield put(approveCenterActions.setDataAndTotal(response));
    }
  }
}

function* resetSearchValues() {
  yield put(approveCenterActions.resetSearchValuesAndPagination());
  yield put(getApproveCenterListActionCreator(false));
}

function* setSearchValues(value) {
  yield put(approveCenterActions.setSearchValuesAndPagination(value));
  yield put(getApproveCenterListActionCreator(false));
}

function* setPagination(newPagination) {
  yield put(approveCenterActions.setPagination(newPagination));
  yield put(getApproveCenterListActionCreator(false));
}

// watchers

function* watchGetData() {
  while (true) {
    const { payload } = yield take(getApproveCenterListActionCreator.type);
    const task = yield fork(getData, payload);
    const [reset] = yield race([take(resetSearchValuesActionCreator.type), task]);
    if (reset) {
      cancel(task);
    }
  }
}

function* watchResetSearchValues() {
  while (true) {
    yield take(resetSearchValuesActionCreator.type);
    yield fork(resetSearchValues);
  }
}

function* watchSetSearchValues() {
  while (true) {
    const { payload } = yield take(setSearchValuesActionCreator.type);
    yield fork(setSearchValues, payload);
  }
}

function* watchSetPagination() {
  while (true) {
    const { payload } = yield take(setPaginationThenGetDataActionCreator.type);
    yield fork(setPagination, payload);
  }
}

export default [
  fork(watchGetData),
  fork(watchResetSearchValues),
  fork(watchSetSearchValues),
  fork(watchSetPagination),
];
