import { call, fork, put, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { redirectActionCreator } from '@manyun/dc-brain.legacy.redux/actions/routerActions';
import { workdayManageService } from '@manyun/dc-brain.legacy.services';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';

import {
  addWorkDayAction,
  editWorkDayAction,
  getWorkdayListActionCreator,
  workdayManageActions,
} from '../actions/workdayManageActions';

// workers

function* getWorkdayList(payload) {
  yield put(workdayManageActions.fetchWorkdatListStart());
  const { response, error } = yield call(workdayManageService.fetchWorkdayList, payload);
  if (error) {
    message.error(error);
    yield put(workdayManageActions.fetchWorkdatListError());
    return;
  }
  yield put(workdayManageActions.setWorkdatList(response));
}

function* addWorkday({ params }) {
  const { response, error } = yield call(workdayManageService.addWorkday, params);
  if (response) {
    message.success('新增成功！');
    yield put(redirectActionCreator(urls.generateWorkdayListLocation()));
  } else {
    message.error(error);
  }
}

function* editWorkday({ params }) {
  const { response, error } = yield call(workdayManageService.editWorkday, params);
  if (response) {
    message.success('编辑成功！');
    yield put(redirectActionCreator(urls.generateWorkdayListLocation()));
  } else {
    message.error(error);
  }
}

// watchers

function* watchGetWorkdayList() {
  while (true) {
    const { payload } = yield take(getWorkdayListActionCreator.type);
    yield fork(getWorkdayList, payload);
  }
}

//添加工作日submit
function* watchAddWorkday() {
  while (true) {
    const { payload } = yield take(addWorkDayAction.type);
    yield fork(addWorkday, payload);
  }
}

//编辑工作日submit
function* watchEditWorkday() {
  while (true) {
    const { payload } = yield take(editWorkDayAction.type);
    yield fork(editWorkday, payload);
  }
}

export default [fork(watchGetWorkdayList), fork(watchAddWorkday), fork(watchEditWorkday)];
