import { fork, takeEvery } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { commonErrorHandlerActionCreator } from '@manyun/dc-brain.legacy.redux/actions/handlerActions';

function* handleError({ payload: error }) {
  message.error(error.message);
  yield true;
}

function* watchHandleError() {
  yield takeEvery(commonErrorHandlerActionCreator.type, handleError);
}

export default [fork(watchHandleError)];
