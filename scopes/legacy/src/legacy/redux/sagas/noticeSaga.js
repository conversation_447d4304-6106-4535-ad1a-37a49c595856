import { call, fork, put, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { addNotice as addNoticeService } from '@manyun/notification-hub.service.add-notice';
import { updateNotice as updateNoticeService } from '@manyun/notification-hub.service.update-notice';
import { updatePublishNotice as updatePublishNoticeService } from '@manyun/notification-hub.service.update-publish-notice';

import { noticeService } from '@manyun/dc-brain.legacy.services';

import {
  addNotice,
  deleteNotice,
  fetchNoticeDetail,
  fetchPublishNoticeListPage,
  fetchValidNoticeListPage,
  noticeActions,
  publishNotice,
  revokeNotice,
  updateNotice,
  updatePublishNotice,
} from '../actions/noticeActions';

function* fetchValidNoticeList(payload) {
  yield put(noticeActions.request());
  const { pageNum, pageSize } = payload;
  const params = {
    currentTime: new Date().getTime(),
    pageNum,
    pageSize,
  };
  const { response, error } = yield call(noticeService.getValidNoticeList, params);
  if (response) {
    yield put(noticeActions.saveSearchCondition(payload));
    yield put(noticeActions.getNoticeListSuccess(response));
  } else {
    message.error(error || '公告列表请求失败');
    yield put(noticeActions.failure());
  }
}

function* fetchPublishNoticeList(payload) {
  yield put(noticeActions.request());
  const { pageNum, pageSize, status, published } = payload;
  const params = {
    currentTime: new Date().getTime(),
    pageNum,
    pageSize,
    published: status === 'ALL' ? null : status === 'REVALID' ? !published : published,
    status: status === 'REVALID' || status === 'ALL' ? null : status,
  };
  const { response, error } = yield call(noticeService.getPublishNoticeList, params);
  if (response) {
    yield put(noticeActions.saveSearchCondition(payload));
    yield put(noticeActions.getNoticeListSuccess(response));
  } else {
    message.error(error || '公告列表请求失败');
    yield put(noticeActions.failure());
  }
}

function* DeleteNotice(payload) {
  yield put(noticeActions.request());
  const { permissionId, pageSize, published, status } = payload;
  const params = {
    id: permissionId,
    pageNum: 1,
    pageSize,
    published,
    status,
    currentTime: new Date().getTime(),
  };
  const { response, error } = yield call(noticeService.deleteNotice, { id: permissionId });
  if (response) {
    message.success('删除公告成功');
    yield fork(fetchPublishNoticeList, params);
  } else {
    message.error(error || '删除公告请求失败');
    yield put(noticeActions.failure());
  }
}

function* PublishNotice(payload) {
  yield put(noticeActions.request());
  const { permissionId, pageSize, published, status } = payload;
  const params = {
    id: permissionId,
    pageNum: 1,
    pageSize,
    published,
    status,
    currentTime: new Date().getTime(),
  };
  const { response, error } = yield call(noticeService.publishNotice, { id: permissionId });
  if (response) {
    message.success('发布公告成功');
    yield fork(fetchPublishNoticeList, params);
  } else {
    message.error(error || '发布公告请求失败');
    yield put(noticeActions.failure());
  }
}

function* RevokeNotice(payload) {
  yield put(noticeActions.request());
  const { permissionId, pageSize, published, status } = payload;
  const params = {
    id: permissionId,
    pageNum: 1,
    pageSize,
    published,
    status,
    currentTime: new Date().getTime(),
  };
  const { response, error } = yield call(noticeService.revokeNotice, { id: permissionId });
  if (response) {
    message.success('撤回公告成功');
    yield fork(fetchPublishNoticeList, params);
  } else {
    message.error(error || '撤回公告请求失败');
    yield put(noticeActions.failure());
  }
}

function* UpdateNotice(payload) {
  yield put(noticeActions.request());
  const { noticeMess, noticePageCondition } = payload;
  const { error } = yield call(updateNoticeService, noticeMess);
  if (!error) {
    message.success('更新公告成功');
    yield put(noticeActions.changeNoticeVisible());
    yield fork(fetchPublishNoticeList, noticePageCondition);
  } else {
    message.error(error.message || '更新公告请求失败');
    yield put(noticeActions.failure());
  }
}

function* UpdatePublishNotice(payload) {
  yield put(noticeActions.request());
  const { noticeMess, noticePageCondition } = payload;
  const { error } = yield call(updatePublishNoticeService, noticeMess);
  if (!error) {
    message.success('更新并发布成功');
    yield put(noticeActions.changeNoticeVisible());
    yield fork(fetchPublishNoticeList, noticePageCondition);
  } else {
    message.error(error.message || '更新、发布公告请求失败');
    yield put(noticeActions.failure());
  }
}

function* AddNotice(payload) {
  yield put(noticeActions.request());
  const { noticeMess, noticePageCondition } = payload;
  const { error } = yield call(addNoticeService, noticeMess);
  if (!error) {
    message.success('发布公告成功');
    yield fork(fetchPublishNoticeList, noticePageCondition);
  } else {
    message.error(error.message || '发布公告请求失败');
    yield put(noticeActions.failure());
  }
}

function* getNoticeDetail(payload) {
  const { response, error } = yield call(noticeService.fetchNoticeDetail, payload);
  if (response) {
    yield put(noticeActions.saveNoticeDetailSuccess(response));
  } else {
    message.error(error);
  }
}

export function* watchFetchValidNoticeList() {
  while (true) {
    const { payload } = yield take(fetchValidNoticeListPage.type);
    yield fork(fetchValidNoticeList, payload);
  }
}

export function* watchFetchPublishNoticeList() {
  while (true) {
    const { payload } = yield take(fetchPublishNoticeListPage.type);
    yield fork(fetchPublishNoticeList, payload);
  }
}

export function* watchDeleteNotice() {
  while (true) {
    const { payload } = yield take(deleteNotice.type);
    yield fork(DeleteNotice, payload);
  }
}

export function* watchPublishNotice() {
  while (true) {
    const { payload } = yield take(publishNotice.type);
    yield fork(PublishNotice, payload);
  }
}

export function* watchRevokeNotice() {
  while (true) {
    const { payload } = yield take(revokeNotice.type);
    yield fork(RevokeNotice, payload);
  }
}

export function* watchUpdateNotice() {
  while (true) {
    const { payload } = yield take(updateNotice.type);
    yield fork(UpdateNotice, payload);
  }
}

export function* watchUpdatePublishNotice() {
  while (true) {
    const { payload } = yield take(updatePublishNotice.type);
    yield fork(UpdatePublishNotice, payload);
  }
}

export function* watchAddNotice() {
  while (true) {
    const { payload } = yield take(addNotice.type);
    yield fork(AddNotice, payload);
  }
}

export function* watchGetNoticeDetail() {
  while (true) {
    const { payload } = yield take(fetchNoticeDetail.type);
    yield fork(getNoticeDetail, payload);
  }
}

export default [
  fork(watchFetchValidNoticeList),
  fork(watchDeleteNotice),
  fork(watchPublishNotice),
  fork(watchRevokeNotice),
  fork(watchUpdateNotice),
  fork(watchFetchPublishNoticeList),
  fork(watchUpdatePublishNotice),
  fork(watchAddNotice),
  fork(watchGetNoticeDetail),
];
