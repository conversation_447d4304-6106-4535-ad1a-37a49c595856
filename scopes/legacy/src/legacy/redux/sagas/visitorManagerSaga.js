import { call, cancel, fork, put, race, select, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { visitorManagerService } from '@manyun/dc-brain.legacy.services';

import {
  resetSearchValuesActionCreator,
  setPaginationThenGetDataActionCreator,
  visitorRecordActionCreator,
  visitorRecordActions,
} from '../actions/visitorManagerActions';
import { getSearchValuesAndPagination } from './../selectors/visitorManagerSeletcors';

function getQ(pagination, searchValues) {
  const baseQ = { ...pagination };
  for (const [name, { value }] of Object.entries(searchValues)) {
    if (!['', undefined, null].includes(value)) {
      if (name === 'name' && typeof value == 'string') {
        baseQ[name] = value.trim();
      } else if (name === 'identityNo' && typeof value == 'string') {
        baseQ[name] = value.trim();
      } else if (name === 'idcTag' && Array.isArray(value) && value.length >= 1) {
        /**选了楼栋就不传idcTag */
        baseQ[name] = value[1] ? undefined : value[0];
        baseQ.blockGuidList = value[1] ? [value[1]] : undefined;
      } else if (name === 'enterTime' && Array.isArray(value) && value.length >= 2) {
        baseQ.enterStartTime = value[0].clone().milliseconds(0).valueOf();
        baseQ.enterEndTime = value[1].clone().milliseconds(999).valueOf();
      } else if (name === 'leaveTime' && Array.isArray(value) && value.length >= 2) {
        baseQ.leaveStartTime = value[0].clone().milliseconds(0).valueOf();
        baseQ.leaveEndTime = value[1].clone().milliseconds(999).valueOf();
      } else if (name === 'entryStatus') {
        baseQ[name] = value;
      } else if (name === 'taskNo') {
        baseQ[name] = value;
      } else if (name === 'usher') {
        baseQ[name] = value;
      }
    }
  }
  return baseQ;
}

// workers

function* getData(shouldResetPageNum = true) {
  yield put(visitorRecordActions.request());
  if (shouldResetPageNum) {
    yield put(visitorRecordActions.resetPageNum());
  }
  const { searchValues, pagination, mode } = yield select(getSearchValuesAndPagination);

  const q = getQ(pagination, searchValues);
  const param = {
    ...q,
    bizTag: mode,
    name: !!mode ? q.usher?.label : q.name,
  };
  delete param.mode;
  delete param.usher;
  const { response, error } = yield call(visitorManagerService.fetchVisitorRecordPage, param);
  if (error) {
    message.error(error);
  } else {
    yield put(visitorRecordActions.setDataAndTotal(response));
  }
}

function* resetSearchValues() {
  yield put(visitorRecordActions.resetSearchValuesAndPagination());
  yield put(visitorRecordActionCreator(false));
}

function* setPagination(newPagination) {
  yield put(visitorRecordActions.setPagination(newPagination));
  yield put(visitorRecordActionCreator(false));
}

// watchers

function* watchGetData() {
  while (true) {
    const { payload } = yield take(visitorRecordActionCreator.type);
    const task = yield fork(getData, payload);
    const [reset] = yield race([take(resetSearchValuesActionCreator.type), task]);
    if (reset) {
      cancel(task);
    }
  }
}

function* watchResetSearchValues() {
  while (true) {
    yield take(resetSearchValuesActionCreator.type);
    yield fork(resetSearchValues);
  }
}

function* watchSetPagination() {
  while (true) {
    const { payload } = yield take(setPaginationThenGetDataActionCreator.type);
    yield fork(setPagination, payload);
  }
}

export default [fork(watchGetData), fork(watchResetSearchValues), fork(watchSetPagination)];
