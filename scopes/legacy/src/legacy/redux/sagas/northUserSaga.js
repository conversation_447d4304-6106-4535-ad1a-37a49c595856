import { call, cancel, fork, put, race, select, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { northUserService } from '@manyun/dc-brain.legacy.services';

import {
  batchUpdateNorthUserStatusActionCreator,
  createNorthUserActionCreator,
  northUserActionCreator,
  northUserActions,
  resetSearchValuesActionCreator,
  setPaginationThenGetDataActionCreator,
  updateNorthUserActionCreator,
} from '../actions/northUserActions';
import { getSearchValuesAndPagination } from './../selectors/northUserSelectors';

function getQ(pagination, searchValues) {
  const baseQ = { ...pagination };
  for (const [name, value] of Object.entries(searchValues)) {
    const val = value === '' ? null : value;
    if (name === 'username') {
      baseQ[name] = val;
    }
  }
  return baseQ;
}

//workers

function* getData(shouldResetPageNum = true) {
  yield put(northUserActions.request());
  if (shouldResetPageNum) {
    yield put(northUserActions.resetPageNum());
  }
  const { searchValues, pagination } = yield select(getSearchValuesAndPagination);
  const q = getQ(pagination, searchValues);
  const { response, error } = yield call(northUserService.fetchNorthUserPage, q);
  if (error) {
    message.error(error);
  } else {
    yield put(northUserActions.setDataAndTotal(response));
  }
}

function* resetSearchValues() {
  yield put(northUserActions.resetSearchValuesAndPagination());
  yield put(northUserActionCreator(false));
}

function* setPagination(newPagination) {
  yield put(northUserActions.setPagination(newPagination));
  yield put(northUserActionCreator(false));
}

function* batchUpdateNorthUserStatus({ available, ids }) {
  const { error } = yield call(northUserService.batchUpdateNorthUserStatus, { available, ids });
  if (error) {
    message.error(error);
  } else {
    const txt = available ? '启用' : '停用';
    message.success(`成功${txt} ${ids.length} 项！`);
    yield put(northUserActionCreator(false));
  }
}

function* createNorthUser(payload) {
  yield put(northUserActions.request());
  const { error } = yield call(northUserService.createNorthUser, payload);
  if (error) {
    message.error(error);
  } else {
    yield put(northUserActionCreator(false));
  }
}

function* updateNorthUser(payload) {
  yield put(northUserActions.request());
  const { error } = yield call(northUserService.updateNorthUser, payload);
  if (error) {
    message.error(error);
  } else {
    yield put(northUserActionCreator(false));
  }
}

//watchers

function* watchGetData() {
  while (true) {
    const { payload } = yield take(northUserActionCreator.type);
    const task = yield fork(getData, payload);
    const [reset] = yield race([take(resetSearchValuesActionCreator.type), task]);
    if (reset) {
      cancel(task);
    }
  }
}

function* watchResetSearchValues() {
  while (true) {
    yield take(resetSearchValuesActionCreator.type);
    yield fork(resetSearchValues);
  }
}

function* watchSetPagination() {
  while (true) {
    const { payload } = yield take(setPaginationThenGetDataActionCreator.type);
    yield fork(setPagination, payload);
  }
}

function* watchBatchUpdateNorthUserStatus() {
  while (true) {
    const { payload } = yield take(batchUpdateNorthUserStatusActionCreator.type);
    yield fork(batchUpdateNorthUserStatus, payload);
  }
}

function* watchCreateNorthUser() {
  while (true) {
    const { payload } = yield take(createNorthUserActionCreator.type);
    yield fork(createNorthUser, payload);
  }
}

function* watchUpdateNorthUser() {
  while (true) {
    const { payload } = yield take(updateNorthUserActionCreator.type);
    yield fork(updateNorthUser, payload);
  }
}

export default [
  fork(watchGetData),
  fork(watchResetSearchValues),
  fork(watchSetPagination),
  fork(watchBatchUpdateNorthUserStatus),
  fork(watchCreateNorthUser),
  fork(watchUpdateNorthUser),
];
