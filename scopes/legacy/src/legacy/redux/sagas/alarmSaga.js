import { call, fork, put, select, take } from 'redux-saga/effects';
import shortid from 'shortid';

import { message } from '@manyun/base-ui.ui.message';

import { CUSTOMIZED_NOTIFICATION_TEXT } from '@manyun/dc-brain.legacy.constants';
import { ALARM_CONFIG_ITEM_TYPE_MAP } from '@manyun/dc-brain.legacy.pages/monitor-item-config/constants';
import { getLimitsByNotifyRule } from '@manyun/dc-brain.legacy.pages/monitor-item-config/utils';
import { alarmService, opsService } from '@manyun/dc-brain.legacy.services';

import {
  alarmActions,
  getNotificationTxtTplFieldsAction,
  getViewingMonitorItemActionCreator,
} from '../actions/alarmActions';
import { getNotificationFields } from './../selectors/alarmConfigSelectors';

function* getNotificationTxtTplFields() {
  const { response, error } = yield call(opsService.fetchOpsServiceList, {
    topCategory: 'ALARM_TOP_NOTIFY',
    secondCategory: 'ALARM_SECOND_NOTIFY',
  });
  if (response) {
    const physicalFields = response.data.filter(item => item.metaCode === 'PHY_MESSAGE')[0]
      .children;
    const faultedFields = response.data.filter(item => item.metaCode === 'FAULT_MESSAGE')[0]
      .children;
    const otherFields = response.data.filter(item => item.metaCode === 'OTHER_MESSAGE')[0].children;
    yield put(
      alarmActions.setNotificationTxtTplFields({ physicalFields, faultedFields, otherFields })
    );
  } else {
    message.error(error || '告警文案请求失败');
  }
}

function* notificationTxt2Fields(notifyRule) {
  const notificationFields = yield select(getNotificationFields);
  const fields = [notifyRule];
  while (fields.some(item => typeof item === 'string')) {
    for (let idx = 0; idx < fields.length; idx++) {
      const item = fields[idx];
      if (typeof item !== 'string') {
        continue;
      }
      let shouldBreak = false;

      for (let index = 0; index < notificationFields.length; index++) {
        const field = notificationFields[index];
        const keyword = '${' + field.metaCode + '}';
        if (item.includes(keyword)) {
          let id = shortid();
          const tplItem = {
            id,
            label: field.metaName,
            value: field.metaCode,
            description: field.description,
          };
          shouldBreak = true;
          const keywordIdx = item.indexOf(keyword);
          const left = item.slice(0, keywordIdx);
          const right = item.slice(keywordIdx + keyword.length);
          if (!left && right) {
            fields.splice(idx, 1, tplItem, right);
          } else if (left && !right) {
            fields.splice(idx, 1, left, tplItem);
          } else if (!left && !right) {
            fields.splice(idx, 1, tplItem);
          } else {
            fields.splice(idx, 1, left, tplItem, right);
          }
          break;
        }
      }
      if (!shouldBreak) {
        fields.splice(idx, 1, {
          id: shortid(),
          label: '自定义',
          value: CUSTOMIZED_NOTIFICATION_TEXT,
          text: item,
          description: '自定义配置告警内容',
        });
      }
      break;
    }
  }

  return fields;
}

function* setNotificationTpl(notifyRule) {
  const fields = yield notificationTxt2Fields(notifyRule);
  yield put(alarmActions.setNotificationTpl(fields));
}

function* getViewingMonitorItem(data) {
  const { response, error } = yield call(alarmService.fetchMonitorItemConfigById, data);
  if (response) {
    const { itemType, triggerRule, notifyRule } = response;
    yield setNotificationTpl(notifyRule);
    if (
      itemType.code === ALARM_CONFIG_ITEM_TYPE_MAP.SINGLE_POINT ||
      itemType.code === ALARM_CONFIG_ITEM_TYPE_MAP.CUSTOM
    ) {
      const { lowerLimit, upperLimit, normalLimits } = getLimitsByNotifyRule(triggerRule);
      yield put(
        alarmActions.setViewingMonitorItem({
          ...response,
          lowerLimit,
          upperLimit,
          normalLimits,
        })
      );
    }
  } else {
    message.error(error);
  }
}

/******************************************************************************/
/******************************* WATCHERS *************************************/
/******************************************************************************/

export function* WatchFetchAlarmCopy() {
  while (true) {
    yield take(getNotificationTxtTplFieldsAction.type);
    yield fork(getNotificationTxtTplFields);
  }
}

function* watchGetViewingMonitorItem() {
  while (true) {
    const { payload } = yield take(getViewingMonitorItemActionCreator.type);
    yield fork(getViewingMonitorItem, payload);
  }
}

export default [fork(WatchFetchAlarmCopy), fork(watchGetViewingMonitorItem)];
