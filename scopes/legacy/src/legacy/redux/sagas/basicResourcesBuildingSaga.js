import { call, fork, put, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import {
  basicResourcesBuildingService,
  infrastructureService,
} from '@manyun/dc-brain.legacy.services';

import {
  basicResourcesBuildingActions,
  deleteBlockActionCreator,
  fetchBasicResourceBuildingList,
  fetchMetaSpaceList,
} from '../actions/basicResourcesBuildingActions';

function* basicResourceBuildingList(data) {
  yield put(basicResourcesBuildingActions.request());
  if (!data.operationStatus) {
    data.operationStatus = null;
  }
  const { response, error } = yield call(basicResourcesBuildingService.fetchBuildingList, data);
  if (response) {
    yield put(
      basicResourcesBuildingActions.success({
        list: response.data,
        total: response.total,
        loading: false,
        pageNum: data.pageNum,
        pageSize: data.pageSize,
      })
    );
  } else {
    message.error(error || '楼栋列表请求失败');
    yield put(basicResourcesBuildingActions.failure());
  }
}

function* metaList() {
  yield put(basicResourcesBuildingActions.onSelectLoading());
  throw new Error('basicResourcesBuildingService.fetchMetaSpaceList was removed!');
  // const { response, error } = yield call(basicResourcesBuildingService.fetchMetaSpaceList);
  // if (response) {
  //   yield put(basicResourcesBuildingActions.onSelectLoading());
  //   // yield put(basicResourcesBuildingActions.metaSuccess(response.data));
  // } else {
  //   yield put(basicResourcesBuildingActions.onSelectLoading());
  //   message.error(error || '用户地区列表失败');
  // }
}

function* deleteBlock({ blockGuid, searchValues }) {
  yield put(basicResourcesBuildingActions.startDelete());
  const { response, error } = yield call(infrastructureService.deleteBlock, blockGuid);
  if (response) {
    message.success(`${blockGuid} 已被删除！`);
    yield put(fetchBasicResourceBuildingList(searchValues));
  } else {
    message.error(error);
    yield put(basicResourcesBuildingActions.deleteError(error));
  }
}

function* watchFetchBasicResourceBuildingList() {
  while (true) {
    const { payload } = yield take(fetchBasicResourceBuildingList.type);
    yield fork(basicResourceBuildingList, payload);
  }
}

function* watchFetchMetaList() {
  while (true) {
    yield take(fetchMetaSpaceList.type);
    yield fork(metaList);
  }
}

function* watchDeleteBlock() {
  while (true) {
    const { payload } = yield take(deleteBlockActionCreator.type);
    yield fork(deleteBlock, payload);
  }
}

export default [
  fork(watchFetchBasicResourceBuildingList),
  fork(watchFetchMetaList),
  fork(watchDeleteBlock),
];
