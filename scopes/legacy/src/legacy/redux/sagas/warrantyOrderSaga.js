import trim from 'lodash/trim';
import { call, fork, put, select, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import * as warrantyOrderService from '@manyun/dc-brain.legacy.services/warrantyOrderService';

import {
  fetchWarrantyDetail,
  fetchWarrantyOrderList,
  revokeWarrantyOrder,
  setWarrantyPagination,
  warrantyOrderActions,
} from '../actions/warrantyOrderActions';
import { getSearchValuesAndPagination } from '../selectors/warrantySelectors';

function getQ(pagination, searchValues) {
  const baseQ = { ...pagination };
  for (const [, { name, value }] of Object.entries(searchValues)) {
    if ([null, undefined, ''].includes(value) || (Array.isArray(value) && value.length === 0)) {
      continue;
    }
    if (name === 'location') {
      if (value.length === 2) {
        baseQ['blockGuidList'] = [value.join('.')];
      } else {
        baseQ['idcTagList'] = value;
      }
    } else if (name === 'applyTime') {
      baseQ['applyStartTime'] = value[0].clone().startOf('day').valueOf();
      baseQ['applyEndTime'] = value[1].clone().endOf('day').valueOf();
    } else if (name === 'applyStaff') {
      baseQ['applyStaffId'] = value.key;
    } else {
      baseQ[name] = Array.isArray(value) ? value : trim(value);
    }
  }
  return baseQ;
}

function* fetchWarrantyOrderPage() {
  yield put(warrantyOrderActions.request());
  const { pagination, searchValues } = yield select(getSearchValuesAndPagination);
  const { response, error } = yield call(
    warrantyOrderService.fetchWarrantyOrderList,
    getQ(pagination, searchValues)
  );
  if (response) {
    yield put(warrantyOrderActions.featchWarrantyOrderSuccess(response));
  } else {
    message.error(error);
    yield put(warrantyOrderActions.failure());
  }
}

function* revoke({ orderNo, type }) {
  const { response, error } = yield call(warrantyOrderService.revokeWarrantyOrder, { orderNo });
  if (response) {
    message.success('撤回成功！');
    // 维保订单调用撤销操作的地方有两处，列表上按钮type为link，撤销成功后保留上次的列表筛选条件刷新数据；详情页按钮样式为primary，撤销成功后后刷新详情数据
    type === 'link'
      ? yield put(setWarrantyPagination({ pageNum: 1, pageSize: 10 }))
      : yield put(fetchWarrantyDetail({ orderNo }));
  } else {
    message.error(error);
  }
}

function* getWarrantyDetail({ orderNo }) {
  const { response, error } = yield call(warrantyOrderService.fetchWarrantyDetail, { orderNo });
  if (response) {
    yield put(warrantyOrderActions.saveWarrantyDetail(response));
  } else {
    message.error(error);
  }
}

function* setWarrantyPagePagination(newPagination) {
  yield put(warrantyOrderActions.setWarrantyPagination(newPagination));
  yield put(fetchWarrantyOrderList());
}

/******************************************************************************/
/******************************* WATCHERS *************************************/
/******************************************************************************/

export function* watchFetchWarrantyOrderList() {
  while (true) {
    yield take(fetchWarrantyOrderList.type);
    yield fork(fetchWarrantyOrderPage);
  }
}

function* watchRevokeOrder() {
  while (true) {
    const { payload } = yield take(revokeWarrantyOrder.type);
    yield fork(revoke, payload);
  }
}

function* watchFetchDetail() {
  while (true) {
    const { payload } = yield take(fetchWarrantyDetail.type);
    yield fork(getWarrantyDetail, payload);
  }
}

function* watchSetWarrantyPagination() {
  while (true) {
    const { payload } = yield take(setWarrantyPagination.type);
    yield fork(setWarrantyPagePagination, payload);
  }
}

export default [
  fork(watchFetchWarrantyOrderList),
  fork(watchRevokeOrder),
  fork(watchFetchDetail),
  fork(watchSetWarrantyPagination),
];
