import trim from 'lodash/trim';
import { call, fork, put, select, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { spareService } from '@manyun/dc-brain.legacy.services';

import {
  //删除无sn耗材
  deleteSpare, //编辑耗材
  editSpare, //初始化列表
  initializeSearchValues,
  setPagination,
  spareActions, // 分页查耗材列表
  spareListPage, //上传耗材
  uploadSpare,
} from '../actions/spareActions';
import { getSparePageConditions } from '../selectors/spareSelector';

/***************************** Subroutines ************************************/
function getQ(pagination, searchValues) {
  const baseQ = { ...pagination };
  for (const [, { name, value }] of Object.entries(searchValues)) {
    if ([null, undefined, ''].includes(value) || (Array.isArray(value) && value.length === 0)) {
      continue;
    }
    if (name === 'vendorModel') {
      baseQ['vendor'] = value[0];
      baseQ['productModel'] = value[1];
    } else if (name === 'cascaderList') {
      baseQ['topCategory'] = value.firstCategoryCode;
      baseQ['secondCategory'] = value.secondCategoryCode;
      if (value.thirdCategorycode) {
        baseQ['spareTypeList'] = [value.thirdCategorycode];
      }
    } else if (name === 'areaIdcBlockRoom') {
      baseQ['idcTag'] = value[0];
      baseQ['blockGuid'] = value[1] && value[0] + '.' + value[1];
      baseQ['roomTag'] = value[2];
    } else if (name === 'modifiedPerson') {
      baseQ['modifyPersonId'] = value.id;
    } else if (name === 'modifiedTime') {
      baseQ['modifyEndTime'] = Number(value[1].unix() + '000');
      baseQ['modifyStartTime'] = Number(value[0].unix() + '000');
    } else {
      baseQ[name] = Array.isArray(value) ? value : trim(value);
    }
  }
  return baseQ;
}

function* fetchSparePage() {
  yield put(spareActions.request());
  const { pagination, searchValues } = yield select(getSparePageConditions);
  const { response, error } = yield call(spareService.fetchSpares, getQ(pagination, searchValues));
  if (response) {
    yield put(spareActions.featchSparePageSuccess(response));
  } else {
    message.error(error || '查询耗材列表请求失败');
    yield put(spareActions.failure());
  }
}

function* deleteNoSnSpare({ data, successCallback, errorCallback }) {
  const { response, error } = yield call(spareService.deleteSpare, data);
  if (response) {
    successCallback();
    message.success('删除耗材成功');
    yield put(spareListPage());
  } else {
    errorCallback();
    message.error(error || '删除耗材失败');
  }
}

function* importSpare(payload) {
  yield put(spareActions.importLoading());
  const file = payload;
  const fd = new FormData();
  fd.append('file', file);
  const { response, error } = yield call(spareService.uploadSpare, fd);
  if (response) {
    yield put(spareActions.importLoading());
    if (!response.data.length) {
      message.success('导入的文件为空');
    } else {
      message.success('导入耗材成功');
      yield put(spareActions.importSpareSuccess(response));
    }
  } else {
    message.error(error || '导入请求失败');
    yield put(spareActions.failure());
  }
}

function* spareEdit({ callback, ...payload }) {
  yield put(spareActions.editBasicInfoLoading());
  const { response, error } = yield call(spareService.editSpare, payload);
  callback();
  if (response) {
    message.success('修改基本信息成功');
    yield put(spareActions.failure());
    yield put(spareListPage());
  } else {
    message.error(error || '修改基本信息失败');
    yield put(spareActions.failure());
  }
}

function* initializeSparePage(urlSearchParams) {
  const { deviceType, vendor, productModel, blockGuid } = urlSearchParams;
  let searchValues = {};
  if (deviceType) {
    searchValues = {
      ...searchValues,
      cascaderList: {
        name: 'cascaderList',
        value: { thirdCategorycode: deviceType },
      },
    };
  }
  if (blockGuid) {
    searchValues = {
      ...searchValues,
      areaIdcBlockRoom: {
        name: 'areaIdcBlockRoom',
        value: [blockGuid.split('.')[0], blockGuid.split('.')[1]],
      },
    };
  }

  if (vendor) {
    searchValues = {
      ...searchValues,
      vendorModel: {
        name: 'vendorModel',
        value: [vendor, productModel],
      },
    };
  }
  yield put(spareActions.initializeSearchValuesAndConditions({ searchValues }));
  yield put(spareListPage());
}

function* setSparePagePagination(newPagination) {
  yield put(spareActions.setSparePagination(newPagination));
  yield put(spareListPage());
}

export function* watchFetchSparePage() {
  while (true) {
    const { payload } = yield take(spareListPage.type);
    yield fork(fetchSparePage, payload);
  }
}

export function* watchDeleteNoSnSpare() {
  while (true) {
    const { payload } = yield take(deleteSpare.type);
    yield fork(deleteNoSnSpare, payload);
  }
}

export function* watchImportSpare() {
  while (true) {
    const { payload } = yield take(uploadSpare.type);
    yield fork(importSpare, payload);
  }
}

export function* watchSpareEdit() {
  while (true) {
    const { payload } = yield take(editSpare.type);
    yield fork(spareEdit, payload);
  }
}

function* watchInitializeSparePage() {
  while (true) {
    const { payload } = yield take(initializeSearchValues.type);
    yield fork(initializeSparePage, payload);
  }
}

function* watchSetSparePagination() {
  while (true) {
    const { payload } = yield take(setPagination.type);
    yield fork(setSparePagePagination, payload);
  }
}

export default [
  fork(watchFetchSparePage),
  fork(watchDeleteNoSnSpare),
  fork(watchImportSpare),
  fork(watchSpareEdit),
  fork(watchInitializeSparePage),
  fork(watchSetSparePagination),
];
