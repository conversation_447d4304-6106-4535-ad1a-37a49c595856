import { call, fork, put, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { roomManageService } from '@manyun/dc-brain.legacy.services';

import {
  // 新建包间
  fetchCreateRoomConfirm, // 分页查询包间列表
  fetchRoomListPage, //修改包间信息接口
  fetchUpdateRoom,
  roomActions,
} from '../actions/roomActions';
import { syncCommonDataActionCreator } from './../actions/commonActions';

/***************************** Subroutines ************************************/

function* fetchRoomPage(payload) {
  yield put(roomActions.request());
  const { response, error } = yield call(roomManageService.fetchRoomPage, payload);
  if (response) {
    yield put(roomActions.saveRoomPageConditionSuccess(payload));
    yield put(roomActions.featchRoomPageSuccess(response));
  } else {
    message.error(error || '包间列表请求失败');
    yield put(roomActions.failure());
  }
}

function* createRoom(q) {
  yield put(roomActions.createRoomLoading());
  yield put(roomActions.request());
  const { response, error } = yield call(roomManageService.fetchCreateNewRoomConfirm, q);
  if (response) {
    yield put(roomActions.createRoomVisible());
    yield put(roomActions.createRoomLoading());
    message.success(`新建包间 ${q.idcTag}.${q.blockTag}.${q.tag} 成功！`);
    const payload = {
      pageNum: 1,
      pageSize: 10,
      idcTag: '',
      blockTag: '',
      operationTimeEnd: '',
      operationTimeStart: '',
      operationStatus: null,
    };
    // 请求机房楼栋
    yield fork(fetchRoomPage, payload);
    yield put(syncCommonDataActionCreator({ strategy: { space: 'FORCED' } }));
  } else {
    message.error(error);
    yield put(roomActions.failure());
  }
}

function* editRoom(payload) {
  yield put(roomActions.request());
  const { editMess, roomPageCondition, successCb } = payload;
  const { response, error } = yield call(roomManageService.updateRoom, editMess);
  if (response) {
    message.success('修改包间信息成功！');
    if (successCb) {
      successCb();
    } else {
      yield put(roomActions.editRoomVisible());
    }
    yield fork(fetchRoomPage, roomPageCondition);
  } else {
    message.error(error);
    yield put(roomActions.failure());
  }
}

/******************************************************************************/
/******************************* WATCHERS *************************************/
/******************************************************************************/

// 监听分页查询包间列表
export function* watchFetchRoomPage() {
  while (true) {
    const { payload } = yield take(fetchRoomListPage.type);
    yield fork(fetchRoomPage, payload);
  }
}

// 新建包间
export function* watchFetchCreateRoom() {
  while (true) {
    const { payload } = yield take(fetchCreateRoomConfirm.type);
    yield fork(createRoom, payload);
  }
}

// 修改包间信息接口
export function* watchFetchEditRoom() {
  while (true) {
    const { payload } = yield take(fetchUpdateRoom.type);
    yield fork(editRoom, payload);
  }
}

export default [fork(watchFetchRoomPage), fork(watchFetchCreateRoom), fork(watchFetchEditRoom)];
