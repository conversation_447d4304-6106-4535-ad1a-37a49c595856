import { call, delay, fork, put, race, select, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { Alarm } from '@manyun/monitoring.model.alarm';
import { fetchAlarms } from '@manyun/monitoring.service.fetch-alarms';
import {
  addRealtimeNAlarmsDataSubscriptionActionCreator,
  removeRealtimeNAlarmsDataSubscriptionActionCreator,
} from '@manyun/monitoring.state.subscriptions';
import {
  changeTransformerGroupAction,
  getAllBoundaryGraphsSaga,
  selectBlockGensetsGraph,
  selectBlockHVGraph,
  selectBlockLVGraph,
} from '@manyun/monitoring.state.topology';
import { fetchPointsByCondition } from '@manyun/resource-hub.service.fetch-points-by-condition';
import { getSpaceGuid } from '@manyun/resource-hub.util.space-guid';
import { CUSTOMIZED_ERROR_CODE_MAP } from '@manyun/service.request';

import { SUBSCRIPTIONS_MODE } from '@manyun/dc-brain.legacy.constants/subscriptions';
import { getClosestSpaceGuid } from '@manyun/dc-brain.legacy.pages/topology-graphix/utils/get-closest-space-guid';
import { TOPOLOGY_BOUNDARY_MAP } from '@manyun/dc-brain.legacy.pages/topology/constants';
import { getChildrenActionCreator } from '@manyun/dc-brain.legacy.redux/__next/device';
import { changeService, deviceService, eventCenterService } from '@manyun/dc-brain.legacy.services';
import { oneOfVirtualDeviceType } from '@manyun/dc-brain.legacy.utils/deviceType';

import { syncCommonDataActionCreator } from '../actions/commonActions';
import {
  cancelGetDeviceAlarmsActionCreator,
  cancelGetMonitorDataActionCreator,
  deviceMonitoringActions,
  getDeviceActionCreator,
  getDeviceAlarmInfoActionCreator,
  getDeviceChangeTicketsActionCreator,
  getDeviceEventTicketsActionCreator,
  getRawMonitoringDataActionCreator,
  getTopologyActionCreator,
} from './../actions/deviceMonitoringActions';

function* getDevice(deviceGuid) {
  const { response: device, error } = yield call(deviceService.fetchDeviceByGuid, deviceGuid);
  if (error) {
    message.error(error);
    return;
  }
  yield put(deviceMonitoringActions.setDevice(device));

  const config = yield select(selectCurrentConfig);
  const configUtil = new ConfigUtil(config);
  const typeofUPS = configUtil.typeofDeviceGen(ConfigUtil.constants.deviceTypes.UPS);
  if (typeofUPS(device.deviceType)) {
    yield put(
      getChildrenActionCreator({
        spaceGuid: getClosestSpaceGuid(device.spaceGuid),
        deviceGuid,
      })
    );
  }

  yield put(
    syncCommonDataActionCreator({ strategy: { deviceTypesPointsDefinition: [device.deviceType] } })
  );
  yield put(
    getRawMonitoringDataActionCreator({
      shouldGetPoints: true,
      deviceType: device.deviceType,
      deviceGuid,
      idc: device.spaceGuid.idcTag,
      block: device.spaceGuid.blockTag,
      room: device.spaceGuid.roomTag,
    })
  );
}

function* getDeviceAlarmInfo(q) {
  yield put(deviceMonitoringActions.requestAlarmInfo(q));
  const { data, error } = yield call(fetchAlarms, q);
  if (!error) {
    yield put(
      deviceMonitoringActions.setAlarmInfo({
        data: data.data.map(Alarm.toApiObject),
        total: data.total,
      })
    );
  } else {
    message.error(error.message);
    yield put(deviceMonitoringActions.requestAlarmInfoError());
  }

  // 如果当前用户请求API的结果是无权限，则不轮询
  if (error && error.code === CUSTOMIZED_ERROR_CODE_MAP.UN_AUTHORIZED) {
    return;
  }

  const { timeout } = yield race({
    shouldCancel1: take(cancelGetDeviceAlarmsActionCreator.type),
    shouldCancel2: take(cancelGetMonitorDataActionCreator.type),
    timeout: delay(15 * 1000),
  });
  // 因为在其他页时可能刷新后的结果是无数据
  // 而且告警是倒排的，最新的数据会在第1页
  // 所以仅在第1页时触发定时刷新
  if (timeout && q.pageNum === 1) {
    yield put(getDeviceAlarmInfoActionCreator(q));
  }
}

function* getRawMonitoringData({ shouldGetPoints, idc, block, spaceGuid, deviceType, deviceGuid }) {
  const mode = SUBSCRIPTIONS_MODE.REALTIME_N_ALARMS;
  const blockGuid = idc + '.' + block;
  const moduleId = 'device-monitoring';
  yield put(
    addRealtimeNAlarmsDataSubscriptionActionCreator({
      mode,
      blockGuid,
      moduleId,
      deviceGuids: [deviceGuid],
    })
  );
  if (shouldGetPoints) {
    yield fork(getPointsByDeviceType, { spaceGuid, deviceType });
  }
  yield take(cancelGetMonitorDataActionCreator.type);
  yield put(removeRealtimeNAlarmsDataSubscriptionActionCreator({ mode, blockGuid, moduleId }));
}

function* getPointsByDeviceType({ spaceGuid, deviceType }) {
  const { data, error } = yield call(fetchPointsByCondition, {
    spaceGuid,
    deviceType,
    pointTypeList: oneOfVirtualDeviceType(deviceType)
      ? ['CAL_SPACE', 'AGG_SPACE', 'CUSTOM']
      : ['ORI', 'CAL_DEVICE', 'AGG_DEVICE'],
    isRemoveMain: true,
  });
  if (data) {
    yield put(deviceMonitoringActions.setPoints(data.data.map(point => point.toApiObject())));
  } else {
    message.error(error);
  }
}

function hasDevice(graph, deviceGuid) {
  // const elements = flattenDeepElements(graph.pages[0].children);
  return graph.pages[0].children.some(element => element.custom?.deviceGuid === deviceGuid);
}

function* getTopology({ idc, block, deviceGuid }) {
  yield call(getAllBoundaryGraphsSaga, { payload: { idc, block } });

  const blockGuid = getSpaceGuid(idc, block);
  const gensetGraph = yield select(selectBlockGensetsGraph(blockGuid));
  const hvGraph = yield select(selectBlockHVGraph(blockGuid));
  const lvGraph = yield select(selectBlockLVGraph(blockGuid));

  if (gensetGraph?.code === 'success' && hasDevice(gensetGraph.graph, deviceGuid)) {
    yield put(
      deviceMonitoringActions.setTopologyBoundary({ boundary: TOPOLOGY_BOUNDARY_MAP.GENERATOR })
    );
    return;
  }

  if (hvGraph?.code === 'success' && hasDevice(hvGraph.graph, deviceGuid)) {
    yield put(deviceMonitoringActions.setTopologyBoundary({ boundary: TOPOLOGY_BOUNDARY_MAP.HV }));
    return;
  }

  if (lvGraph?.code === 'success') {
    let transformerGroupKey;
    let graph;
    for (let idx = 0; idx < lvGraph.custom.lvGraphs.length; idx++) {
      const transformerGroup = lvGraph.custom.lvGraphs[idx];
      if (hasDevice(transformerGroup.graph, deviceGuid)) {
        transformerGroupKey = transformerGroup.key;
        graph = transformerGroup.graph;
        break;
      }
    }
    if (transformerGroupKey !== undefined && graph !== undefined) {
      yield put(
        deviceMonitoringActions.setTopologyBoundary({
          boundary: TOPOLOGY_BOUNDARY_MAP.LV,
          transformerGroupKey,
        })
      );
      yield put(changeTransformerGroupAction({ idc, block, key: transformerGroupKey }));
      return;
    }
  }

  yield put(deviceMonitoringActions.setTopologyBoundary({ boundary: null }));
}

function* getDeviceChangeTickets(q) {
  yield put(deviceMonitoringActions.requestChangeTicketInfo());
  const { response, error } = yield call(changeService.fetchDeviceChangeTickets, q);
  if (error) {
    message.error(error);
    yield put(deviceMonitoringActions.requestChangeTicketInfoError());
    return;
  }
  const { data, total } = response;
  yield put(deviceMonitoringActions.setChangeTicketInfo({ data, total }));

  // TODO add loop requests
}

function* getDeviceEventTickets(q) {
  yield put(deviceMonitoringActions.requestEventTicketInfo());
  const { response, error } = yield call(eventCenterService.fetchEventList, q);
  if (error) {
    message.error(error);
    yield put(deviceMonitoringActions.requestEventTicketInfoError());
    return;
  }
  const { data, total } = response;
  yield put(deviceMonitoringActions.setEventTicketInfo({ data, total }));

  // TODO add loop requests
}

/******************************************************************************/
/******************************* WATCHERS *************************************/
/******************************************************************************/

function* watchGetDevice() {
  while (true) {
    const { payload } = yield take(getDeviceActionCreator.type);
    yield fork(getDevice, payload);
  }
}

function* watchGetDeviceAlarmInfo() {
  while (true) {
    const { payload } = yield take(getDeviceAlarmInfoActionCreator.type);
    yield fork(getDeviceAlarmInfo, payload);
  }
}

function* watchGetDeviceRawMonitoringData() {
  while (true) {
    const { payload } = yield take(getRawMonitoringDataActionCreator.type);
    yield fork(getRawMonitoringData, payload);
  }
}

function* watchGetTopology() {
  while (true) {
    const { payload } = yield take(getTopologyActionCreator.type);
    yield fork(getTopology, payload);
  }
}

function* watchGetDeviceChangeTickets() {
  while (true) {
    const { payload } = yield take(getDeviceChangeTicketsActionCreator.type);
    yield fork(getDeviceChangeTickets, payload);
  }
}

function* watchGetDeviceEventTickets() {
  while (true) {
    const { payload } = yield take(getDeviceEventTicketsActionCreator.type);
    yield fork(getDeviceEventTickets, payload);
  }
}

export default [
  fork(watchGetDevice),
  fork(watchGetDeviceAlarmInfo),
  fork(watchGetDeviceRawMonitoringData),
  fork(watchGetTopology),
  fork(watchGetDeviceChangeTickets),
  fork(watchGetDeviceEventTickets),
];
