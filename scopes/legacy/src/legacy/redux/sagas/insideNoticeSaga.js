import { call, fork, put, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { insideNoticeService } from '@manyun/dc-brain.legacy.services';

import {
  getInsideNoticeListActionCreator,
  insideNoticeActions,
  setAlreadyRead,
  setReadAll,
} from '../actions/insideNoticeActions';

// workers
function* getList(payload) {
  yield put(insideNoticeActions.updateTableLoading(true));
  const { response, error } = yield call(insideNoticeService.fetchInsideNoticeList, payload);
  if (error) {
    message.error(error);
    yield put(insideNoticeActions.fetchInsideNoticeListError());
    return;
  }
  if (payload.insideMsgPType === '' && payload.isRead === '0') {
    yield put(insideNoticeActions.updateWaitNum(response.total));
  }
  yield put(insideNoticeActions.setInsideNoticeList(response));
}

function* setRead({ params, successCallback }) {
  yield put(insideNoticeActions.updateTableLoading(true));
  const { response, error } = yield call(insideNoticeService.setInsideNoticeRead, params);
  if (error) {
    message.error(error);
    yield put(insideNoticeActions.failure());
    return;
  }
  successCallback(true);
  yield put(insideNoticeActions.updateWaitNum(response));
  yield put(insideNoticeActions.updateTableLoading(false));
}

function* setReadAllInside({ params, successCallback }) {
  yield put(insideNoticeActions.updateTableLoading());
  const { response, error } = yield call(insideNoticeService.setInsideNoticeAllRead, params);
  if (error) {
    message.error(error);
    yield put(insideNoticeActions.failure());
    return;
  }
  successCallback(true);
  yield put(insideNoticeActions.updateWaitNum(response));
  yield put(insideNoticeActions.updateTableLoading(false));
}

// watchers
function* watchInsideNoticeList() {
  while (true) {
    const { payload } = yield take(getInsideNoticeListActionCreator.type);
    yield fork(getList, payload);
  }
}

function* watchReadFlag() {
  while (true) {
    const { payload } = yield take(setAlreadyRead.type);
    yield fork(setRead, payload);
  }
}

function* watchAllRead() {
  while (true) {
    const { payload } = yield take(setReadAll.type);
    yield fork(setReadAllInside, payload);
  }
}

export default [fork(watchInsideNoticeList), fork(watchReadFlag), fork(watchAllRead)];
