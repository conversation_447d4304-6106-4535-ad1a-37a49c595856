import { call, fork, put, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { doPointService, specService } from '@manyun/dc-brain.legacy.services';

import {
  MetaCategoryAction,
  MetaCategoryNumAction,
  deleteSpecAction,
  fetchSpecDetail,
  specActions,
} from '../actions/specActions';

function* specDetail(payload) {
  yield put(specActions.request());
  const { response, error } = yield call(specService.fetchSpecList, payload);
  if (response) {
    yield put(specActions.featchSpecDetailSuccess(response));
  } else {
    message.error(error);
    yield put(specActions.failure());
  }
}

function* deleteSpec({ params, deviceType, successCb, errorCb }) {
  yield put(specActions.request());
  const { response, error } = yield call(specService.deleteSpec, params);
  if (response) {
    message.success('删除规格成功');
    yield fork(specDetail, { deviceType: deviceType });
    successCb();
  } else {
    message.error(error);
    yield put(specActions.failure());
    errorCb();
  }
}

function* metaCategory() {
  const { response, error } = yield call(doPointService.fetchMetaCategory);
  if (response) {
    yield put(specActions.fetcgMetaCategory(response.data));
  } else {
    message.error(error);
    yield put(specActions.failure());
  }
}

function* metaCategoryNum() {
  const { response, error } = yield call(specService.fetchDeviceTypePointCountMap);
  if (response) {
    yield put(specActions.fetcgMetaCategoryNumSuccess(response));
  } else {
    message.error(error);
    yield put(specActions.failure());
  }
}

// 查询规格详情
export function* watchFetchSpecDetail() {
  while (true) {
    const { payload } = yield take(fetchSpecDetail.type);
    yield fork(specDetail, payload);
  }
}

// 删除规格
export function* watchDeleteSpec() {
  while (true) {
    const { payload } = yield take(deleteSpecAction.type);
    yield fork(deleteSpec, payload);
  }
}

// 设备分类三级结构
export function* watchFetchMetaCategory() {
  while (true) {
    yield take(MetaCategoryAction.type);
    yield fork(metaCategory);
  }
}

// 设备分类三级测点数量
export function* watchFetchMetaCategoryNum() {
  while (true) {
    yield take(MetaCategoryNumAction.type);
    yield fork(metaCategoryNum);
  }
}

export default [
  fork(watchFetchSpecDetail),
  fork(watchDeleteSpec),
  fork(watchFetchMetaCategory),
  fork(watchFetchMetaCategoryNum),
];
