import { push } from 'connected-react-router';
import sha256 from 'crypto-js/sha256';
import trim from 'lodash/trim';
import { call, fork, put, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { mutateUser } from '@manyun/auth-hub.service.mutate-user';
import { updateUserLoginSettings } from '@manyun/auth-hub.service.update-user-login-settings';

import * as urls from '@manyun/dc-brain.legacy.constants/urls';
import { userManageService } from '@manyun/dc-brain.legacy.services';

import {
  fetchAddNewUser,
  fetchDelete,
  fetchEditConfirm,
  fetchEditLoginSettingsConfirm,
  fetchJoinUserGroup,
  fetchLog,
  fetchRemoveUserGroup,
  fetchUserManageDetail,
  loadUserManageInfo,
  userManageActions,
} from '../actions/userManageActions';

function getParams(values, allowNull = false) {
  const params = Object.keys(values).reduce((map, fieldName) => {
    const value = values[fieldName];
    if (allowNull && value === null) {
      map[fieldName] = value;
      return map;
    }
    if ([null, undefined].includes(value) || (Array.isArray(value) && value.length === 0)) {
      return map;
    } else if (typeof value === 'number') {
      map[fieldName] = value;
    } else {
      map[fieldName] = trim(value);
    }
    return map;
  }, {});
  return params;
}

function* fetchUser(pageNo, pageSize, searchType, searchName) {
  yield put(userManageActions.request());
  const { response, error } = yield call(userManageService.fetchUser, {
    pageNo,
    pageSize,
    searchType,
    searchName,
  });
  if (response) {
    yield put(
      userManageActions.success({
        list: response.data,
        total: response.total,
        pageNo,
        pageSize,
        searchType,
        searchName,
        loading: false,
      })
    );
  } else {
    message.error(error || '用户列表请求失败');
    yield put(userManageActions.failure());
  }
}

function* deleteUser(userId, pageNo, pageSize, searchType, searchName) {
  yield put(userManageActions.request());
  const { response, error } = yield call(userManageService.fetchDelete, userId);
  if (response) {
    message.success('删除用户成功');
    yield fork(fetchUser, pageNo, pageSize, searchType, searchName);
  } else {
    message.error(error || '删除用户失败！');
    yield put(userManageActions.failure());
  }
}

function* userManageDetail({ userId, pageNo, pageSize, filterTypeValue, loginName }) {
  const { response, error } = yield call(userManageService.fetchDetail, userId, loginName);
  if (response) {
    if (pageNo) {
      yield fork(joinUserGroup, response.id, pageNo, pageSize, filterTypeValue);
    }
    yield put(userManageActions.detailSuccess(response));
  } else {
    message.error(error || '用户详情请求失败');
    yield put(userManageActions.failure({ id: userId }));
  }
}

function* editConfirm(payload) {
  yield put(userManageActions.editBasicInfoLoading());
  const { error } = yield call(mutateUser, getParams(payload, true));
  if (error) {
    message.error(error.message || '修改基本信息失败');
    yield put(userManageActions.failure());
  } else {
    message.success('修改基本信息成功');
    yield fork(userManageDetail, { userId: payload.id });
    yield put(userManageActions.editBasicInfoLoading());
    yield put(userManageActions.editBasicInfoVisible());
  }
}

function* editLoginSettingsConfirm(payload) {
  yield put(userManageActions.editLoginSettingsLoading());
  const { error } = yield call(updateUserLoginSettings, payload);
  if (error) {
    message.error(error.message || '修改登录设置失败');
    yield put(userManageActions.failure());
  } else {
    message.success('修改密码成功');
    yield fork(userManageDetail, { userId: payload.userId });
    yield put(userManageActions.editLoginSettingVisible());
    yield put(userManageActions.editLoginSettingsLoading());
  }
}

function* log(userId, pageNo, pageSize, callback) {
  const { response, error } = yield call(userManageService.fetchLog, userId, pageNo, pageSize);
  callback && callback(!error);
  if (response) {
    yield put(userManageActions.logSuccess(response));
  } else {
    message.error(error);
    yield put(userManageActions.failure());
  }
}

function* joinUserGroup(userId, pageNo, pageSize, filterTypeValue) {
  yield put(userManageActions.request());
  const { response, error } = yield call(userManageService.fetchJoinUserGroup, {
    pageNo,
    pageSize,
    groupName: filterTypeValue,
    userId,
  });
  if (response) {
    yield put(userManageActions.joinUserGroupSuccess(response));
  } else {
    message.error(error || '加入的用户组列表请求失败');
    yield put(userManageActions.failure());
  }
}

function* removeUserGroup(userId, groupId, pageNo, pageSize, searchType) {
  yield put(userManageActions.request());
  const { response, error } = yield call(userManageService.fetchRemoveUserGroup, {
    groupId,
    userId,
  });
  if (response) {
    message.success('移除用户组成功');
    yield fork(joinUserGroup, userId, pageNo, pageSize, searchType);
  } else {
    message.error(error || '移除用户组失败失败');
    yield put(userManageActions.failure());
  }
}

function* addNewUser({
  userInfos,
  multiFactorAuthentication,
  passwordType,
  resetPassword,
  password,
}) {
  const encryptedPassword = sha256(password).toString();
  const newUserInfos = userInfos.map(item => {
    return { ...item, password: encryptedPassword };
  });
  yield put(userManageActions.addNewUserLoading());
  const { response, error } = yield call(
    userManageService.fetchAddNewUser,
    newUserInfos,
    multiFactorAuthentication,
    passwordType,
    resetPassword,
    password
  );
  if (response) {
    message.success('新建用户成功');
    yield put(userManageActions.addNewUserLoading());
    yield put(push(urls.USER_MANAGE_LIST));
  } else {
    message.error(error || '新建用户失败');
    yield put(userManageActions.addNewUserLoading());
  }
}

export function* watchLoadUserManagePage() {
  while (true) {
    const { payload } = yield take(loadUserManageInfo.type);
    const { pageNo, pageSize, searchType, searchName } = payload;
    yield fork(fetchUser, pageNo, pageSize, searchType, searchName);
  }
}

export function* watchFetchDelete() {
  while (true) {
    const { payload } = yield take(fetchDelete.type);
    const { userId, searchType, pageSize, searchName, pageNo } = payload;
    yield fork(deleteUser, userId, pageNo, pageSize, searchType, searchName);
  }
}

export function* watchFetchUserManageDetail() {
  while (true) {
    const { payload } = yield take(fetchUserManageDetail.type);
    const { userId, filterType, pageNo, pageSize, filterTypeValue, loginName } = payload;
    yield fork(userManageDetail, {
      userId,
      filterType,
      pageNo,
      pageSize,
      filterTypeValue,
      loginName,
    });
  }
}

export function* watchFetchEditLoginSettingsConfirm() {
  while (true) {
    const { payload } = yield take(fetchEditLoginSettingsConfirm.type);
    yield fork(editLoginSettingsConfirm, payload);
  }
}

export function* watchFetchEditConfirm() {
  while (true) {
    const { payload } = yield take(fetchEditConfirm.type);
    yield fork(editConfirm, payload);
  }
}

export function* watchFetchLog() {
  while (true) {
    const { payload } = yield take(fetchLog.type);
    const { userId, pageNo, pageSize, callback } = payload;
    yield fork(log, userId, pageNo, pageSize, callback);
  }
}

export function* watchFetchJoinUserGroup() {
  while (true) {
    const { payload } = yield take(fetchJoinUserGroup.type);
    const { userId, pageNo, pageSize, filterTypeValue } = payload;
    yield fork(joinUserGroup, userId, pageNo, pageSize, filterTypeValue);
  }
}

export function* watchFetchRemoveUserGroup() {
  while (true) {
    const { payload } = yield take(fetchRemoveUserGroup.type);
    const { userId, groupId, pageNo, pageSize, filterTypeValue } = payload;
    yield fork(removeUserGroup, userId, groupId, pageNo, pageSize, filterTypeValue);
  }
}

export function* watchFetchAddNewUser() {
  while (true) {
    const { payload } = yield take(fetchAddNewUser.type);
    const { userInfos, multiFactorAuthentication, passwordType, resetPassword, password } = payload;
    yield fork(addNewUser, {
      userInfos,
      multiFactorAuthentication,
      passwordType,
      resetPassword,
      password,
    });
  }
}

export default [
  fork(watchLoadUserManagePage),
  // fork(watchSelectUserList),
  fork(watchFetchRemoveUserGroup),
  fork(watchFetchDelete),
  fork(watchFetchUserManageDetail),
  fork(watchFetchEditConfirm),
  fork(watchFetchLog),
  fork(watchFetchEditLoginSettingsConfirm),
  fork(watchFetchJoinUserGroup),
  fork(watchFetchAddNewUser),
];
