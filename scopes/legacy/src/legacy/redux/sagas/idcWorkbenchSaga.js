import sortBy from 'lodash/sortBy';
import uniqBy from 'lodash/uniqBy';
import moment from 'moment';
import { normalize } from 'normalizr';
import { all, call, delay, fork, put, race, select, take, takeLatest } from 'redux-saga/effects';

// import { push } from 'connected-react-router';
import { message } from '@manyun/base-ui.ui.message';

import {
  userSliceActions,
  /*, selectMe*/
} from '@manyun/auth-hub.state.user';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { fetchPagedUsersShiftSchedule } from '@manyun/hrm.service.fetch-paged-users-shift-schedule';
import { fetchStaffSchedule } from '@manyun/hrm.service.fetch-staff-schedule';
import { Alarm } from '@manyun/monitoring.model.alarm';
import { fetchAlarms } from '@manyun/monitoring.service.fetch-alarms';
import {
  addRealtimeNAlarmsDataSubscriptionActionCreator,
  removeRealtimeNAlarmsDataSubscriptionActionCreator,
} from '@manyun/monitoring.state.subscriptions';

// import { MONITORING_IDC_ROUTE_PATH } from '@manyun/monitoring.route.monitoring-routes';
// import { generateUserProfileRoutePath } from '@manyun/auth-hub.route.auth-routes';
import { SUBSCRIPTIONS_MODE } from '@manyun/dc-brain.legacy.constants/subscriptions';
import {
  UN_RESOLVED_CHANGE_STATES,
  UN_RESOLVED_EVENT_STATES,
} from '@manyun/dc-brain.legacy.pages/idc-workbench/constants';
import {
  alarmDataService,
  changeService,
  deviceService,
  eventCenterService,
  idcWorkbenchServices,
  realtimeDataService,
  ticketService,
} from '@manyun/dc-brain.legacy.services';
import { alarmsSchema } from '@manyun/dc-brain.legacy.services/schemas/alarmSchemas';
import { devicesByTypeSchema } from '@manyun/dc-brain.legacy.services/schemas/deviceSchemas';
import { getSpaceGuid } from '@manyun/dc-brain.legacy.utils';

import { syncCommonDataActionCreator } from '../actions/commonActions';
import {
  fetchIdcWorkbenchPUE,
  getAttStaffSchedulesActionCreator,
  getBaseTasksInIdcActionCreator,
  getBlockDevicesActionCreator,
  getEventActionCreator,
  getNoticesActionCreator,
  getTicketDataActionCreator,
  getToDoListActionCreator,
  idcWorkbenchActions,
  initializeActionCreator,
  initializeIdcActionCreator,
  roomsInfoActiveBlockChangeActionCreator,
  runningStatesActiveBlockChangeActionCreator,
  subUnsubRoomsDataActionCreator,
  subUnsubRunningStatesDataActionCreator,
  terminateGetBlocksAlarmsIntervalActionCreator,
  terminateGetRoomRealtimeDataIntervalActionCreator,
  terminateGetRunningStatesRealtimeDataIntervalActionCreator,
  terminateGetStatisticsIntervalActionCreator,
} from '../actions/idcWorkbenchActions';
import { idcWorkbenchInitialState } from '../reducers/idcWorkbenchSlice';
import {
  getActiveBlocks,
  getBlockRooms,
  getPendingMattersList,
  getRoomsInfoActiveBlock,
  getRunningStatesActiveBlock,
} from '../selectors/idcWorkbenchSelectors';

/**
 * 获取机房下的楼栋和包间，设置默认选中的楼栋，并开始轮询包间实时数据
 */
function* getBNRs(idc) {
  if (!idc) {
    return;
  }

  const { error, response } = yield call(idcWorkbenchServices.fetchBlockRoom, idc);
  if (error) {
    // 这里的功能是为了实现 Task [#688](http://chandao.manyun-local.com/zentao/task-view-688.html)
    // 但是暂且注释掉这个无机房资源权限时跳转到用户个人档案页面的功能，
    // 因为无资源权限时，UI 层面会报一个错误，从用户体验上来讲不合适，
    // 另外，UI 层面已经对没有机房工作台页面权限的情况实现了此功能

    // const config = yield select(selectCurrentConfig);
    // const configUtil = new ConfigUtil(config);
    // const homeUrl = configUtil.getHomeUrl();
    // if (homeUrl.startsWith(MONITORING_IDC_ROUTE_PATH.replace('/:idc', ''))) {
    //   const { userId } = yield select(selectMe);
    //   yield put(push(generateUserProfileRoutePath({ id: userId })));
    // }
    return;
  }

  if (!response) {
    console.error('Blocks and rooms not found by %s', idc);
    return;
  }

  // 当前用户有访问当前机房工作台的接口权限，需要将此机房缓存到 `user.idc` 中
  yield put(userSliceActions.setUserIdc(idc));

  const { blockRoomTags } = response;

  // 此机房下没有楼
  if (blockRoomTags === null) {
    yield put(
      idcWorkbenchActions.setBNRs({
        idc,
        blocks: [],
        activeBlock: null,
        roomsMap: {},
      })
    );
    return;
  }

  const blocks = Object.keys(blockRoomTags);
  // 首先展示第一个楼的数据
  const activeBlock = blocks[0];
  const roomsMap = blocks.reduce((map, block) => {
    map[getSpaceGuid(idc, block)] = blockRoomTags[block];

    return map;
  }, {});

  yield put(
    idcWorkbenchActions.setBNRs({
      idc,
      blocks,
      activeBlock,
      roomsMap,
    })
  );
}

/**
 * 轮询所有 active 的楼的告警数据
 */
function* getBlocksAlarmsInterval(idc) {
  const { idc: currentIdc, blocks: activeBlocks } = yield select(getActiveBlocks);

  if (idc !== currentIdc) {
    return;
  }

  const validBlocks = activeBlocks.filter(Boolean);

  if (validBlocks.length > 0) {
    const { error, data } = yield call(fetchAlarms, { idcTag: idc, blockTags: validBlocks });

    if (error) {
      // 这里获取告警数据是作为后台程序运行的
      // 所以不把错误信息直接弹出提示给用户
      console.error('Failed to fetch alarm data for ', idc, validBlocks.join(','));
      yield put(idcWorkbenchActions.setActiveBlocksAlarms({}));
    } else {
      const result = normalize(data.data.map(Alarm.toApiObject), alarmsSchema);
      const { pointsData } = result.entities;
      yield put(idcWorkbenchActions.setActiveBlocksAlarms(pointsData));
    }
  }

  const { timeout } = yield race({
    cancelAction: take(terminateGetBlocksAlarmsIntervalActionCreator.type),
    timeout: delay(15 * 1000),
  });

  if (timeout) {
    yield getBlocksAlarmsInterval(idc);
  }
}

function* getIdcStatisticsInterval(idc) {
  const [alarmStatisticData, events, changes, tickets] = yield all([
    call(alarmDataService.fetchAlarmStatisticData, { idcTag: idc }),
    call(eventCenterService.fetchEventList, {
      idcTags: [idc],
      eventStatus: UN_RESOLVED_EVENT_STATES,
      pageNum: 1,
      pageSize: 1,
    }),
    call(changeService.fetchIdcChangeTickets, {
      idcTag: idc,
      statusList: UN_RESOLVED_CHANGE_STATES,
    }),
    call(ticketService.fetchValidNoticeList, {
      idcTag: idc,
      taskStatusList: ['2', '3'],
    }),
  ]);

  let {
    pendingAlarmsCount,
    pendingEventsCount,
    pendingChangesCount,
    pendingTicketsCount,
    idcDevicesCount,
    blockDevicesCountMap,
    roomAlarmsCount,
  } = idcWorkbenchInitialState;

  if (alarmStatisticData.response) {
    const { totalAlarmCount, deviceCount, blockDeviceCount, roomAlarmCount } =
      alarmStatisticData.response;

    if (typeof totalAlarmCount == 'number') {
      pendingAlarmsCount = totalAlarmCount;
    } else {
      pendingAlarmsCount = 0; // 本次API请求是成功的，这种情况下认为此机房下无任何告警
    }

    if (deviceCount) {
      idcDevicesCount = deviceCount;
    }

    if (blockDeviceCount) {
      blockDevicesCountMap = blockDeviceCount;
    }

    if (roomAlarmCount) {
      roomAlarmsCount = Object.keys(roomAlarmCount).reduce((map, key) => {
        map[idc + '.' + key] = roomAlarmCount[key];

        return map;
      }, {});
    }
  }
  if (events.response) {
    pendingEventsCount = events.response.total;
  }
  if (typeof changes.response == 'number') {
    pendingChangesCount = changes.response;
  } else {
    pendingChangesCount = 0; // 本次API请求是成功的，这种情况下认为此机房下无任何变更
  }
  if (tickets.response) {
    pendingTicketsCount = tickets.response.total;
  }

  yield put(
    idcWorkbenchActions.setAlarmObjectsCount({
      pendingEventsCount,
      pendingAlarmsCount,
      pendingChangesCount,
      pendingTicketsCount,
      idcDevicesCount,
      blockDevicesCountMap,
      roomAlarmsCount,
    })
  );

  const { timeout } = yield race({
    shouldCancel: take(terminateGetStatisticsIntervalActionCreator.type),
    timeout: delay(15 * 1000),
  });
  if (timeout) {
    yield fork(getIdcStatisticsInterval, idc);
  }
}

function* getNotices(idc) {
  const { response, error } = yield call(idcWorkbenchServices.fetchNoticeList, { idcTag: idc });
  if (response) {
    yield put(idcWorkbenchActions.getNoticeListSuccess(response.data));
  } else {
    message.error(error);
  }
}

function* subUnsubRunningStatesData({ payload: idc }) {
  const block = yield select(getRunningStatesActiveBlock);

  if (block === null) {
    return;
  }

  const mode = SUBSCRIPTIONS_MODE.REALTIME_N_ALARMS;
  const blockGuid = getSpaceGuid(idc, block);
  const moduleId = 'idc-workbench_running-states';
  const deviceGuid = blockGuid;
  yield put(
    addRealtimeNAlarmsDataSubscriptionActionCreator({
      mode,
      blockGuid,
      moduleId,
      deviceGuids: [deviceGuid],
    })
  );
  yield take(terminateGetRunningStatesRealtimeDataIntervalActionCreator.type);
  yield put(removeRealtimeNAlarmsDataSubscriptionActionCreator({ blockGuid, mode, moduleId }));
}

function* subUnsubRoomsData({ payload: idc }) {
  const block = yield select(getRoomsInfoActiveBlock);

  if (block === null) {
    return;
  }

  const blockGuid = getSpaceGuid(idc, block);

  const rooms = yield select(getBlockRooms, blockGuid);

  if (rooms.length <= 0) {
    return;
  }

  const mode = SUBSCRIPTIONS_MODE.REALTIME_N_ALARMS;
  const moduleId = 'idc-workbench_rooms';
  const deviceGuids = rooms.map(room => blockGuid + '.' + room.roomTag);
  yield put(
    addRealtimeNAlarmsDataSubscriptionActionCreator({
      mode,
      blockGuid,
      moduleId,
      deviceGuids,
    })
  );
  yield take(terminateGetRoomRealtimeDataIntervalActionCreator.type);
  yield put(removeRealtimeNAlarmsDataSubscriptionActionCreator({ blockGuid, mode, moduleId }));
}

function* getBlockDevices({ idc, block }) {
  const blockGuid = idc + '.' + block;
  const config = yield select(selectCurrentConfig);
  const configUtil = new ConfigUtil(config, { defaultSpaceGuid: blockGuid });
  const { response } = yield call(deviceService.fetchDevicesByTypes, {
    idcTag: idc,
    blockTag: block,
    deviceTypes: [
      configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.HV_INCOMING_SWITCHGEAR),
      configUtil.getOneDeviceType(
        ConfigUtil.constants.deviceTypes.CHILLED_WATER_PIPE_BRANCH_WATER_SUPPLY_THERMOMETER
      ),
      configUtil.getOneDeviceType(
        ConfigUtil.constants.deviceTypes.CHILLED_WATER_PIPE_BRANCH_RETURN_WATER_THERMOMETER
      ),
      configUtil.getOneDeviceType(
        ConfigUtil.constants.deviceTypes.CHILLED_WATER_PIPE_BRANCH_WATER_SUPPLY_MANOMETER
      ),
      configUtil.getOneDeviceType(
        ConfigUtil.constants.deviceTypes.CHILLED_WATER_PIPE_BRANCH_RETURN_WATER_MANOMETER
      ),
      configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.TES_TANK),
      configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.WATER_TANK),
    ],
  });
  if (response) {
    const devicesMap = normalize(response.data, devicesByTypeSchema).entities.data;
    yield put(idcWorkbenchActions.setBlockDevices({ blockGuid, devicesMap }));
  }
}

/**
 * 初始化工作台需要的数据
 */
function* initialize({ idc }) {
  // 1. 概览数据
  yield fork(getIdcStatisticsInterval, idc);

  // 2. PUE 能耗
  // TODO

  // 3. 代办事项
  // TODO

  // 4. 公告通知
  yield fork(getNotices, idc);

  // 5. 运行状态 & 包间信息
  // 5.1 机房下的楼栋、包间数据
  yield call(getBNRs, idc);
  // 获取必要的测点定义
  yield call(syncPointsDefinitions);
  // 5.2 运行状态实时数据
  yield put(subUnsubRunningStatesDataActionCreator(idc));
  // 5.3 包间信息实时数据
  yield put(subUnsubRoomsDataActionCreator(idc));
  // 5.4 楼栋告警数据
  yield fork(getBlocksAlarmsInterval, idc);

  // 7. 排班信息
  yield fork(getAttStaffSchedules, idc);
}

/**
 * 机房下的楼栋、包间数据
 */
function* initializeIdc({ idc }) {
  yield call(getBNRs, idc);
}

function* syncPointsDefinitions() {
  const config = yield select(selectCurrentConfig);
  const configUtil = new ConfigUtil(config);
  yield put(
    syncCommonDataActionCreator({
      strategy: {
        deviceTypesPointsDefinition: [
          configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_BLOCK),
          configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_ROOM),
        ],
      },
    })
  );
}

function* getBaseTasksInIdc(payload) {
  const { error, response } = yield call(ticketService.fetchValidNoticeList, payload);
  if (error) {
    message.error(error);
    yield put(
      idcWorkbenchActions.setPendingMattersList({
        ticket: { data: [], total: 0, pageNum: 1, pageSize: 10 },
      })
    );
  } else {
    let newData = [];
    if (response.data.length) {
      const data = response.data.map(item => {
        return {
          ...item,
          type: '工单',
          time: moment(item.gmtCreate).valueOf(),
          title: item.taskTitle,
          id: item.taskNo,
        };
      });
      newData = sortBy(data, ({ time }) => {
        return time;
      });
    }
    yield put(
      idcWorkbenchActions.setPendingMattersList({
        ticket: {
          data: newData,
          total: response.total,
          pageNum: payload.pageNum,
          pageSize: payload.pageSize,
        },
      })
    );
  }
}

function* getChange(payload) {
  const { error, response } = yield call(changeService.fetchTicketData, payload);
  if (error) {
    message.error(error);
    yield put(
      idcWorkbenchActions.setPendingMattersList({
        change: { data: [], total: 0, pageNum: 1, pageSize: 10 },
      })
    );
  } else {
    let newData = [];
    if (response.data.length) {
      const data = response.data.map(item => {
        return {
          ...item,
          type: '变更',
          time: item.planStartTime,
          title: item.title,
          pageNum: payload.pageNum,
          pageSize: payload.pageSize,
        };
      });
      newData = sortBy(data, ['time']);
    }
    yield put(
      idcWorkbenchActions.setPendingMattersList({
        change: { data: newData, total: response.total },
      })
    );
  }
}

function* handleRunningStatesActiveBlockChange({ payload: { idc, activeBlock } }) {
  const prevActiveBlock = yield select(getRunningStatesActiveBlock);
  if (prevActiveBlock === activeBlock) {
    return;
  }
  yield put(idcWorkbenchActions.updateActiveRunningStatesBlock(activeBlock));
  // 先取消订阅
  yield put(terminateGetRunningStatesRealtimeDataIntervalActionCreator());
  yield put(subUnsubRunningStatesDataActionCreator(idc));
}

function* handleRoomsInfoActiveBlockChange({ payload: { idc, activeBlock } }) {
  const prevActiveBlock = yield select(getRoomsInfoActiveBlock);
  if (prevActiveBlock === activeBlock) {
    return;
  }
  yield put(idcWorkbenchActions.updateActiveRoomInfosBlock(activeBlock));
  // 先取消订阅
  yield put(terminateGetRoomRealtimeDataIntervalActionCreator());
  yield put(subUnsubRoomsDataActionCreator(idc));
}

function* fetchPUEData({
  payload: { idc, ITPowerPointCode, totalPowerPointCode, startTime, endTime, type },
}) {
  const params = {
    pointGuidList: [
      {
        deviceGuid: idc,
        pointCode: ITPowerPointCode,
      },
      {
        deviceGuid: idc,
        pointCode: totalPowerPointCode,
      },
    ],
    startTime,
    endTime,
  };
  const { response, error } = yield call(realtimeDataService.fetchIdcWorkbenchPue, params);
  if (response) {
    yield put(
      idcWorkbenchActions.savePUEs({
        type,
        values: {
          IT: response?.[`${idc}.${ITPowerPointCode}`],
          total: response?.[`${idc}.${totalPowerPointCode}`],
        },
      })
    );
  } else {
    message.error(error);
  }
}

function* getToDoList({ startTime, endTime, idcTag }) {
  // 获取工单，变更,事件
  const [tickets, changes, events] = yield all([
    call(ticketService.fetchValidNoticeList, {
      pageNum: 1,
      pageSize: 10,
      taskStatusList: ['2', '3'], // 2:处理中 3：待接单
      idcTag,
      startTime: startTime,
      endTime: endTime,
    }),
    call(changeService.fetchTicketData, {
      pageNum: 1,
      pageSize: 10,
      planStartTime: startTime,
      planEndTime: endTime,
      statusList: ['WAITING_CHANGE', 'CHANGING', 'IN_SUMMARY', 'WAITING_CLOSE'],
      idcTag,
    }),
    call(eventCenterService.fetchEventList, {
      idcTags: [idcTag],
      eventStatus: ['CREATED', 'PROCESSING', 'RELIEVED', 'RESOLVED', 'AUDITED'],
      pageNum: 1,
      pageSize: 10,
      occurBeginTime: startTime,
      occurEndTime: endTime,
    }),
  ]);
  let newTickets = [];
  if (tickets?.response?.data?.length) {
    newTickets = tickets.response.data.map(item => {
      return {
        ...item,
        type: '工单',
        time: moment(item.gmtCreate).valueOf(),
        title: item.taskTitle,
        id: item.taskNo,
      };
    });
  }
  let newChanges = [];
  if (changes?.response?.data?.length) {
    newChanges = changes.response.data.map(item => {
      return {
        ...item,
        type: '变更',
        time: item.planStartTime,
        title: item.title,
        pageNum: 1,
        pageSize: 10,
      };
    });
  }
  let newEvents = [];
  if (events?.response?.data?.length) {
    newEvents = events.response.data.map(item => {
      return {
        ...item,
        type: '事件',
        time: item.occurTime,
        title: item.eventDesc,
        pageNum: 1,
        pageSize: 10,
      };
    });
  }
  const pending = yield select(getPendingMattersList);
  const dateDataMapping = {};
  for (let i = startTime; i < endTime + 7 * 24 * 60 * 60 * 1000; i = i + 1 * 24 * 60 * 60 * 1000) {
    dateDataMapping[i] = [];
  }
  newChanges.forEach(singleTicket => {
    const time = moment(singleTicket.planStartTime).clone().startOf('day').valueOf();
    dateDataMapping[time] = uniqBy(
      [...(dateDataMapping[time] || []), singleTicket, ...(pending.dateDataMapping[time] || [])],
      'id'
    );
  });
  newEvents.forEach(singleEvent => {
    const time = moment(singleEvent.occurTime).clone().startOf('day').valueOf();
    dateDataMapping[time] = uniqBy(
      [...(dateDataMapping[time] || []), singleEvent, ...(pending.dateDataMapping[time] || [])],
      'id'
    );
  });
  newTickets.forEach(singleTicket => {
    const time = moment(singleTicket.effectTime).clone().startOf('day').valueOf();
    dateDataMapping[time] = uniqBy(
      [...(dateDataMapping[time] || []), singleTicket, ...(pending.dateDataMapping[time] || [])],
      'taskNo'
    );
  });
  yield put(
    idcWorkbenchActions.setPendingMattersList({
      ticket: {
        data: newTickets,
        total: tickets?.response?.total,
        pageNum: 1,
        pageSize: 10,
      },
      change: {
        data: newChanges,
        total: changes?.response?.total,
        pageNum: 1,
        pageSize: 10,
      },
      event: {
        data: newEvents,
        total: events?.response?.total,
        pageNum: 1,
        pageSize: 10,
      },
      dateDataMapping,
    })
  );
}

function* getEvent(payload) {
  const { error, response } = yield call(eventCenterService.fetchEventList, payload);
  if (error) {
    message.error(error);
  } else {
    let newData = [];
    if (response.data.length) {
      const data = response.data.map(item => {
        return {
          ...item,
          type: '事件',
          time: item.occurTime,
          title: item.eventDesc,
          pageNum: payload.pageNum,
          pageSize: payload.pageSize,
        };
      });
      newData = sortBy(data, ['eventLevel']);
    }
    yield put(
      idcWorkbenchActions.setPendingMattersList({
        event: {
          data: newData,
          total: response.total,
          pageNum: payload.pageNum,
          pageSize: payload.pageSize,
        },
      })
    );
  }
}

/**
 * 查询今日排班信息
 *
 * @param {string} idc
 */
function* getAttStaffSchedules(idc) {
  /** @type {import('redux-saga/effects').SagaReturnType<typeof fetchStaffSchedule>} */
  const { error, data } = yield call(fetchStaffSchedule, { idcTag: idc });
  if (error) {
    message.error(error.message);
  } else {
    yield put(
      idcWorkbenchActions.updateUserShifts({ viewMode: 'today', userShifts: data.data || [] })
    );
  }
}

/**
 * 查询明日排班信息
 *
 * @param {object} params
 */
function* getPagedUsersShiftSchedule(params) {
  const { error, data } = yield call(fetchPagedUsersShiftSchedule, params);
  if (error) {
    message.error(error.message);
  } else {
    yield put(
      idcWorkbenchActions.updateUserShifts({ viewMode: 'tomorrow', userShifts: data.data || [] })
    );
  }
}
/**
 * @deprecated 逐渐废弃，请使用请使用 hrm.state.staff-schedule： getStaffScheduleAction ｜ hook use-block-schedule
 */
function* getStaffSchedules({ idc, type, params }) {
  if (type === 'today') {
    yield fork(getAttStaffSchedules, idc);
  }

  if (type === 'tomorrow') {
    yield fork(getPagedUsersShiftSchedule, params);
  }
}

/** watchers */

function* watchGetNotices() {
  while (true) {
    const { payload } = yield take(getNoticesActionCreator.type);
    yield fork(getNotices, payload);
  }
}

function* watchGetBlockDevices() {
  while (true) {
    const { payload } = yield take(getBlockDevicesActionCreator.type);
    yield fork(getBlockDevices, payload);
  }
}

function* watchInitialize() {
  while (true) {
    const { payload } = yield take(initializeActionCreator.type);
    yield fork(initialize, payload);
  }
}

function* watchGetBaseTasksInIdc() {
  while (true) {
    const { payload } = yield take(getBaseTasksInIdcActionCreator.type);
    yield fork(getBaseTasksInIdc, payload);
  }
}

function* watchGetChange() {
  while (true) {
    const { payload } = yield take(getTicketDataActionCreator.type);
    yield fork(getChange, payload);
  }
}

function* watchRunningStatesActiveBlockChange() {
  yield takeLatest(
    runningStatesActiveBlockChangeActionCreator.type,
    handleRunningStatesActiveBlockChange
  );
}

function* watchRoomsInfoActiveBlockChange() {
  yield takeLatest(roomsInfoActiveBlockChangeActionCreator.type, handleRoomsInfoActiveBlockChange);
}

function* watchSubUnsubRunningStatesData() {
  yield takeLatest(subUnsubRunningStatesDataActionCreator.type, subUnsubRunningStatesData);
}

function* watchSubUnsubRoomsData() {
  yield takeLatest(subUnsubRoomsDataActionCreator.type, subUnsubRoomsData);
}

export function* watchFetchPUEData() {
  while (true) {
    const { payload } = yield take(fetchIdcWorkbenchPUE.type);
    yield fork(fetchPUEData, payload);
  }
}

export function* watchGetToDoList() {
  while (true) {
    const { payload } = yield take(getToDoListActionCreator.type);
    yield fork(getToDoList, payload);
  }
}

export function* watchGetEvent() {
  while (true) {
    const { payload } = yield take(getEventActionCreator.type);
    yield fork(getEvent, payload);
  }
}

export function* watchGetAttStaffSchedules() {
  while (true) {
    const { payload } = yield take(getAttStaffSchedulesActionCreator.type);
    yield fork(getStaffSchedules, payload);
  }
}

function* watchInitializeIdc() {
  while (true) {
    const { payload } = yield take(initializeIdcActionCreator.type);
    yield fork(initializeIdc, payload);
  }
}

export default [
  fork(watchGetNotices),
  fork(watchGetBlockDevices),
  fork(watchInitialize),
  fork(watchGetBaseTasksInIdc),
  fork(watchGetChange),
  fork(watchRunningStatesActiveBlockChange),
  fork(watchRoomsInfoActiveBlockChange),
  fork(watchSubUnsubRunningStatesData),
  fork(watchSubUnsubRoomsData),
  fork(watchFetchPUEData),
  fork(watchGetToDoList),
  fork(watchGetEvent),
  fork(watchGetAttStaffSchedules),
  fork(watchInitializeIdc),
];
