import { call, fork, put, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { deviceTypeService } from '@manyun/dc-brain.legacy.services';

import {
  deleteDeviceTypeAction,
  deviceTypeActions,
  fetchDeviceTypeDetail,
  fetchDeviceTypeListPage,
} from '../actions/deviceTypeActions';

function* fetchDeviceTypeList(payload) {
  yield put(deviceTypeActions.request());
  const { response, error } = yield call(deviceTypeService.getDeviceTypeList, payload);
  if (response) {
    yield put(deviceTypeActions.saveDeviceTypePageConditionSuccess(payload));
    yield put(deviceTypeActions.getDeviceTypeListSuccess(response));
  } else {
    message.error(error || '请求失败');
    yield put(deviceTypeActions.failure());
  }
}

function* getDeviceTypeDetail({ params }) {
  yield put(deviceTypeActions.request());
  const { response, error } = yield call(deviceTypeService.getDeviceTypeDetail, params);
  if (response) {
    yield put(deviceTypeActions.saveDeviceTypeDetailSuccess(response));
  } else {
    message.error(error || '请求失败');
    yield put(deviceTypeActions.failure());
  }
}

function* deleteDeviceType({ params, successCb, errorCb }) {
  yield put(deviceTypeActions.request());
  const { response, error } = yield call(deviceTypeService.deleteDeviceType, params);
  if (response) {
    successCb();
    message.success('删除设备类型成功');
  } else {
    errorCb();
    message.error(error);
    yield put(deviceTypeActions.failure());
  }
}

export function* watchFetchDeviceTypeList() {
  while (true) {
    const { payload } = yield take(fetchDeviceTypeListPage.type);
    yield fork(fetchDeviceTypeList, payload);
  }
}

export function* watchGetDeviceTypeDetail() {
  while (true) {
    const { payload } = yield take(fetchDeviceTypeDetail.type);
    yield fork(getDeviceTypeDetail, payload);
  }
}

export function* watchDeleteDeviceType() {
  while (true) {
    const { payload } = yield take(deleteDeviceTypeAction.type);
    yield fork(deleteDeviceType, payload);
  }
}

export default [
  fork(watchFetchDeviceTypeList),
  fork(watchDeleteDeviceType),
  fork(watchGetDeviceTypeDetail),
];
