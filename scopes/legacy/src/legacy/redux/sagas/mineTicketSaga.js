import { call, cancel, fork, put, select, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { ticketBatchStatement } from '@manyun/ticket.service.ticket-batch-statement';

import { ticketService } from '@manyun/dc-brain.legacy.services';

import {
  endAction,
  getTicketListActionCreator,
  mineTicketActions,
  takeOverAction,
} from '../actions/mineTicketActions';
import { getMineTicketLoading } from '../selectors/ticketSelectors';

// workers

function* getMineTicketList(payload) {
  yield put(mineTicketActions.fetchTicketListStart());
  const { response, error } = yield call(ticketService.fetchValidNoticeList, payload);
  if (error) {
    message.error(error);
    yield put(mineTicketActions.fetchTicketListError());
    return;
  }
  yield put(mineTicketActions.setTicketList(response));
}

function* takeOver({ params, successCallback }) {
  // yield put(mineTicketActions.takeOverLoading());

  const { response, error } = yield call(ticketService.takeOverTicket, params);
  if (response) {
    successCallback(response);
  } else {
    message.error(error);
    yield put(mineTicketActions.failure());
  }
}

function* end({ params, successCallback }) {
  const { data, error } = yield call(ticketBatchStatement, params);
  if (data) {
    successCallback(data);
  } else {
    message.error(error || '关单失败');
  }
}

// watchers

function* watchGetMineTicketList() {
  let lastTask = undefined;
  while (true) {
    const { payload } = yield take(getTicketListActionCreator.type);
    const existing = yield select(getMineTicketLoading);
    if (!existing.loading) {
      if (lastTask) {
        yield cancel(lastTask);
      }
      lastTask = yield fork(getMineTicketList, payload);
    }
  }
}

//接单
function* watchTakeOver() {
  while (true) {
    const { payload } = yield take(takeOverAction.type);
    yield fork(takeOver, payload);
  }
}

//关单
function* watchTicketEnd() {
  while (true) {
    const { payload } = yield take(endAction.type);
    yield fork(end, payload);
  }
}

export default [fork(watchGetMineTicketList), fork(watchTakeOver), fork(watchTicketEnd)];
