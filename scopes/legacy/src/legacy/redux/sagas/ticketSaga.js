import sortBy from 'lodash/sortBy';
import uniq from 'lodash/uniq';
import { call, cancel, delay, fork, put, race, select, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';
import { fetchBizFileInfos } from '@manyun/dc-brain.service.fetch-biz-file-infos';
import { fetchPointValues } from '@manyun/monitoring.service.fetch-point-values';
import { subscriptionsSliceActions } from '@manyun/monitoring.state.subscriptions';
import { fetchInventoryTaskConfig } from '@manyun/ticket.service.fetch-inventory-task-config';
import { fetchRiskTickets } from '@manyun/ticket.service.fetch-risk-tickets';
import { ticketBatchStatement } from '@manyun/ticket.service.ticket-batch-statement';
import { withdrawOnOffTicket } from '@manyun/ticket.service.withdraw-on-off-ticket';
import { withdrawRepairApproval } from '@manyun/ticket.service.withdraw-repair-approval';
import { withdrawWarehouseOutTicket } from '@manyun/ticket.service.withdraw-warehouse-out-ticket';

import { taskCenterService, ticketService } from '@manyun/dc-brain.legacy.services';

import {
  cancelTicketRealtimeData,
  getTicketFilesActionCreator,
  getTicketListAction,
  getTicketRealtimeData,
  powerGridCountActionCreator,
  powerGridListActionCreator,
  resetTicketSearchValuesActionCreator,
  revokeAction,
  setPaginationThenGetDataActionCreator,
  singleTakeOverActionCreator,
  takeOverActionCreator,
  ticketActions,
  ticketBasicInfoActionCreator,
  ticketEndOfActionCreator,
  ticketFailureEndOfActionCreator,
  ticketInventoryTaskConfigActionCreator,
  ticketMaintenanceCheckItemActionCreator,
  ticketMaintenanceRoomInfoActionCreator,
  ticketPatrolCheckItemActionCreator,
  ticketPatrolCheckItemPointResultSaveActionCreator,
  ticketPatrolCheckSubjectGroupActionCreator,
  ticketPatrolRoomInfoActionCreator,
  ticketSingleEndOfActionCreator,
  ticketTransferActionCreator,
} from '../actions/ticketActions';
import {
  getListServiceEndpoint, // getPowerCheckedRoom,
  getSearchValuesAndPagination,
  getTicketType,
} from './../selectors/ticketSelectors';

function* fetchTicketList({ shouldResetPageNum = true, taskType }) {
  yield put(ticketActions.request());
  if (shouldResetPageNum) {
    yield put(ticketActions.resetPageNum());
  }
  const { searchValues, pagination } = yield select(getSearchValuesAndPagination);
  const { listServiceEndpoint } = yield select(getListServiceEndpoint);
  const q = getParams(searchValues, taskType);
  const params = {
    ...q,
    ...pagination,
    taskType,
  };
  if (['ACCESS', 'POWER', 'ACCESS_CARD_AUTH', 'WAREHOUSE', 'ON_OFF'].includes(taskType)) {
    params.openInitWithdraw = true;
  }
  // console.log('taskTypetaskType', taskType);
  if (taskType === 'RISK_REGISTER') {
    const { error, data } = yield call(fetchRiskTickets, params);
    if (error) {
      message.error(error.message);
      yield put(ticketActions.failure());
      return;
    }
    yield put(ticketActions.success(data));
    return;
  }
  const { response, error } = yield call(ticketService[listServiceEndpoint], params);
  if (response) {
    yield put(ticketActions.success(response));
  } else {
    message.error(error || '工单列表请求失败');
    yield put(ticketActions.failure());
  }
}

export function getParams(searchValues, taskType) {
  const baseQ = {};
  for (const [name, { value }] of Object.entries(searchValues)) {
    if (!['', undefined, null].includes(value)) {
      if (name === 'effectTime' && Array.isArray(value) && value.length >= 2) {
        const isEffectTimeOnlyInDayFormat = taskType === 'RISK_CHECK';
        baseQ['effectStartTime'] = isEffectTimeOnlyInDayFormat
          ? value[0].startOf('day').valueOf()
          : value[0].valueOf();
        baseQ['effectEndTime'] = isEffectTimeOnlyInDayFormat
          ? value[1].endOf('day').valueOf()
          : value[1].valueOf();
      } else if (name === 'endTime' && Array.isArray(value) && value.length >= 2) {
        baseQ['checkStartTime'] = value[0].startOf('day').valueOf();
        baseQ['checkEndTime'] = value[1].endOf('day').valueOf();
      } else if (name === 'location' && Array.isArray(value) && value.length >= 1) {
        if (value.length === 1) {
          baseQ.idcTag = searchValues.idcTag.value ? searchValues.idcTag.value : value[0];
        }
        if (value.length === 2) {
          baseQ.idcTag = value[0];
          baseQ.blockTag = `${value[0]}.${value[1]}`;
        }
        if (value.length === 3) {
          baseQ.idcTag = value[0];
          baseQ.blockTag = `${value[0]}.${value[1]}`;
          baseQ.roomGuid = `${value[0]}.${value[1]}.${value[2]}`;
        }
      } else if (name === 'taskAssignee' && typeof value == 'object') {
        baseQ['taskAssignee'] = value.id;
      } else if (name === 'creatorName' && typeof value == 'object') {
        baseQ['creatorName'] = value._name || value.name;
      } else if (name === 'applyFlag' && typeof value === 'object') {
        baseQ['applyFlag'] = value.value;
      } else {
        baseQ[name] = value;
      }
    }
  }
  return baseQ;
}

function* takeOver({ params, successCallback, ticketPageType }) {
  const { taskType } = yield select(getTicketType);
  yield put(ticketActions.takeOverLoading());

  const { response, error } = yield call(ticketService.takeOverTicket, { ...params, taskType });
  if (response) {
    yield put(ticketActions.takeOverLoading());
    if (successCallback) {
      successCallback();
    }
    if (ticketPageType === 'tickets') {
      yield put(getTicketListAction({ shouldResetPageNum: false, taskType }));
    }
    if (ticketPageType === 'specific-ticket') {
      yield fork(ticketBasicInfo, { taskNo: params.taskNos.join(',') });
    }
    message.success(`已成功接单${response.data.length}条`);
  } else {
    message.error(error);
    yield put(ticketActions.failure());
  }
}

function* revoke({ taskNo, successCallback, type, ticketPageType, revokeType }) {
  const { taskType } = yield select(getTicketType);
  let res = null;
  let err = null;
  const compareType = type.toLowerCase();

  if (compareType === 'access') {
    const { response, error } = yield call(taskCenterService.revokeAccessTicket, { taskNo });
    res = response;
    err = error;
  }
  if (compareType === 'visitor') {
    const { response, error } = yield call(taskCenterService.revokeVisitorApprove, { taskNo });
    res = response;
    err = error;
  }
  if (compareType === 'power' && revokeType === 'create') {
    const { response, error } = yield call(ticketService.revokePowerApprove, { taskNo });
    res = response;
    err = error;
  }
  if (compareType === 'access_card_auth') {
    const { response, error } = yield call(ticketService.accessCardRecoke, { taskNo });
    res = response;
    err = error;
  }
  if (
    compareType === 'repair' ||
    compareType === 'inspection' ||
    compareType === 'maintenance' ||
    (revokeType === 'end' && compareType === 'power')
  ) {
    const { data, error } = yield call(withdrawRepairApproval, { taskNo });
    res = data;
    err = error?.message;
  }
  if (compareType === 'warehouse') {
    const { data, error } = yield call(withdrawWarehouseOutTicket, { taskNo });
    res = data;
    err = error?.message;
  }
  if (compareType === 'on_off') {
    const { data, error } = yield call(withdrawOnOffTicket, { taskNo });
    res = data;
    err = error?.message;
  }
  if (res) {
    if (taskType) {
      if (ticketPageType === 'tickets') {
        yield put(getTicketListAction({ shouldResetPageNum: true, taskType }));
      }
      if (ticketPageType === 'specific-ticket') {
        yield fork(ticketBasicInfo, { taskNo: taskNo });
      }
    }
    if (successCallback) {
      successCallback();
    }
    message.success(`已成功撤销`);
  } else {
    message.error(err);
    yield put(ticketActions.failure());
  }
}

function* singleTakeOver({ taskNo, successCallback, ticketPageType }) {
  const { taskType } = yield select(getTicketType);
  yield put(ticketActions.takeOverLoading());

  const { response, error } = yield call(ticketService.singleTakeOverTicket, { taskNo, taskType });
  if (response) {
    yield put(ticketActions.takeOverLoading());
    if (successCallback) {
      successCallback();
    }
    if (ticketPageType === 'tickets') {
      yield put(getTicketListAction({ shouldResetPageNum: false, taskType }));
    }
    if (ticketPageType === 'specific-ticket') {
      yield fork(ticketBasicInfo, { taskNo: taskNo });
    }
    message.success(`接单成功`);
  } else {
    message.error(error);
    yield put(ticketActions.failure());
  }
}

function* resetSearchValues(shouldGetTicket) {
  yield put(ticketActions.resetSearchValuesAndPagination());
  const { taskType } = yield select(getTicketType);
  if (shouldGetTicket) {
    yield put(getTicketListAction({ shouldResetPageNum: false, taskType }));
  }
}

function* setPagination(newPagination) {
  yield put(ticketActions.setPagination(newPagination));

  const { taskType } = yield select(getTicketType);
  yield put(getTicketListAction({ shouldResetPageNum: false, taskType }));
}

function* endOf({ params, successCallback, ticketPageType }) {
  const { data, error } = yield call(ticketBatchStatement, params);

  if (data) {
    const { approveNum, endNum } = data;
    if (approveNum === 0 && endNum === 0) {
      message.warning('批量关单全部失败!请尝试单个关单。');
    } else {
      successCallback(data);
      if (ticketPageType === 'tickets') {
        const { taskType } = yield select(getTicketType);
        yield put(getTicketListAction({ shouldResetPageNum: true, taskType }));
      }
      if (ticketPageType === 'specific-ticket') {
        yield fork(ticketBasicInfo, { taskNo: params.taskNos.join(',') });
      }
    }
  } else {
    message.error(error || '关单失败');
  }
}

function* singleEndOf({ params, successCallback, ticketPageType }) {
  const { error, errorCode, response } = yield call(ticketService.ticketSingleEndOf, params);
  successCallback(error, errorCode, response);

  if (!errorCode) {
    if (ticketPageType === 'tickets') {
      const { taskType } = yield select(getTicketType);
      yield put(getTicketListAction({ shouldResetPageNum: true, taskType }));
    }
    if (ticketPageType === 'specific-ticket') {
      yield fork(ticketBasicInfo, { taskNo: params.taskNo });
    }
  }
}

function* ticketBasicInfo(params) {
  yield put(ticketActions.setTicketBasicInfoLoading(true));
  const { response, error } = yield call(ticketService.fetchTicketBasicInfo, params);
  yield put(ticketActions.setTicketBasicInfoLoading(false));

  if (response) {
    yield put(ticketActions.setTicketBasicInfo(response));
  } else {
    message.error(error);
  }
}

function* getTicketFiles({ callback, ...reset }) {
  yield put(ticketActions.fetchTicketFilesStart());
  const { data, error } = yield call(fetchBizFileInfos, reset);
  let list = [];
  if (data && Array.isArray(data.data)) {
    list = data.data.map(mcUploadFile => ({
      ...mcUploadFile,
      src: mcUploadFile.src,
      url: mcUploadFile.src,
    }));
  } else {
    yield put(ticketActions.fetchTicketFilesEnd());
    message.error(error);
  }
  yield put(ticketActions.setTicketFiles(list));
  if (typeof callback === 'function') {
    callback(list);
  }
}

function* ticketPatrolRoomInfo(params) {
  yield put(ticketActions.setRoomLoading());
  const { response, error } = yield call(ticketService.fetchTicketPatrolRoomInfo, params);
  if (response) {
    yield put(ticketActions.setPatrolRoomInfo(response.data));
    yield put(ticketActions.setPatrolRoomTypes(response.data.map(item => item.roomType)));
  } else {
    message.error(error);
  }
  yield put(ticketActions.setRoomLoading());
}

function* ticketPatrolCheckSubjectGroup(params) {
  yield put(ticketActions.setRoomLoading());
  const { response, error } = yield call(ticketService.fetchTicketPatrolCheckSubjectGroup, params);
  if (response) {
    yield put(ticketActions.setPatrolCheckSubjectGroup(response.data));
  } else {
    message.error(error);
  }
  yield put(ticketActions.setRoomLoading());
}

function* ticketTransfer({ params, successCallback, errorCallback, ticketPageType }) {
  const { taskType } = yield select(getTicketType);

  const { response, error } = yield call(ticketService.ticketTransfer, { ...params, taskType });
  if (response) {
    successCallback();
    if (ticketPageType === 'tickets') {
      const { taskType } = yield select(getTicketType);
      yield put(getTicketListAction({ shouldResetPageNum: true, taskType }));
    }
    if (ticketPageType === 'specific-ticket') {
      yield fork(ticketBasicInfo, { taskNo: params.taskNo });
    }
  } else {
    message.error(error);
    errorCallback();
  }
}

function* ticketPatrolCheckItem(params) {
  // yield put(ticketActions.setCheckItemLoading());

  const { response, error } = yield call(ticketService.patrolOrderCheckItem, params);
  if (response) {
    const { data } = response;
    const responseList = data.map(item => {
      return {
        ...item,
        metaCode: `${item.subTypeName}$$${item.checkSubjectTag}$$${item.checkSubjectGuid}$$${item.checkItemName}`,
      };
    });
    // const checkSubjectTypeSchema = [
    //   new schema.Entity(
    //     'subTypeName',
    //     {},
    //     // {
    //     //   idAttribute: 'subTypeName',
    //     //   processStrategy: ({ checkSubjectGuid }) => {
    //     //     return {
    //     //       [checkSubjectGuid]: normalize(responseList, checkSubjectGuidSchema).entities
    //     //         .checkSubjectGuid[checkSubjectGuid],
    //     //     };
    //     //   },
    //     // }
    //     {
    //       idAttribute: 'subTypeName',
    //       processStrategy: data => {
    //         return [data];
    //       },
    //       mergeStrategy: (entityA, entityB) => {
    //         return [...entityA, ...entityB];
    //       },
    //     }
    //   ),
    // ];

    // const checkSubjectGuidSchema = [
    //   new schema.Entity(
    //     'checkSubjectGuid',
    //     {},
    //     {
    //       idAttribute: 'checkSubjectGuid',
    //       processStrategy: ({ metaCode, checkItemName }) => {
    //         return {
    //           [checkItemName]: normalize(responseList, checkStdSchema).entities.checkItemName[
    //             metaCode
    //           ],
    //         };
    //       },
    //     }
    //   ),
    // ];

    // const checkStdSchema = [
    //   new schema.Entity(
    //     'checkItemName',
    //     {},
    //     {
    //       idAttribute: 'metaCode',
    //       processStrategy: data => {
    //         return [data];
    //       },
    //       mergeStrategy: (entityA, entityB) => {
    //         return [...entityA, ...entityB];
    //       },
    //     }
    //   ),
    // ];
    // const rawData = normalize(responseList, checkSubjectTypeSchema).entities.subTypeName;
    // const listByRoomType = [];
    // const list = [];
    // for (const subTypeNameItem in rawData) {
    //   const subTypeName = rawData[subTypeNameItem];
    //   for (const constcheckSubjectGuidItem in subTypeName) {
    //     const constcheckSubjectGuid = subTypeName[constcheckSubjectGuidItem];
    //     for (const checkStdItem in constcheckSubjectGuid) {
    //       constcheckSubjectGuid[checkStdItem].forEach(item => {
    //         if (item.checkValueJson?.value !== null) {
    //           if (item.checkSubjectType === INSPECTION_TYPE_KEY_MAP.ENVIRONMENT) {
    //             listByRoomType.push(item);
    //           } else {
    //             list.push(item);
    //           }
    //         } else {
    //           if (item.checkSubjectType === INSPECTION_TYPE_KEY_MAP.ENVIRONMENT) {
    //             listByRoomType.push({ ...item, editable: true });
    //           }
    //           list.push({ ...item, editable: true });
    //         }
    //       });
    //     }
    //   }
    // }

    const datasource = sortBy(responseList, 'metaCode');
    const rowSpansGroupByDataIndex = datasource.reduce((map, record) => {
      const subTypeName = record.subTypeName;
      if (map[subTypeName] === undefined) {
        map[subTypeName] = 1;
      } else {
        map[subTypeName] += 1;
      }

      const checkSubjectGuid = record.checkSubjectGuid;
      if (map[checkSubjectGuid] === undefined) {
        map[checkSubjectGuid] = 1;
      } else {
        map[checkSubjectGuid] += 1;
      }

      const metaCode = record.metaCode;
      if (map[metaCode] === undefined) {
        map[metaCode] = 1;
      } else {
        map[metaCode] += 1;
      }

      return map;
    }, {});
    yield put(ticketActions.setPatrolCheckItem(datasource)); //房间环境要在上边展示
    yield put(ticketActions.setRowSpansGroupByDataIndex(rowSpansGroupByDataIndex));
    yield put(ticketActions.setPatrolCheckItemTypes(data.map(item => item.subTypeName)));
  } else {
    message.error(error);
    yield put(ticketActions.failure());
  }
  // yield put(ticketActions.setCheckItemLoading());
}

function* ticketPatrolCheckItemResultSave({ params, successCallback, fetchPatrolCheckItemParams }) {
  yield put(ticketActions.setPatrolCheckItemSaveValueLoading());

  const { response, error } = yield call(ticketService.patrolCheckItemPointResultSave, params);
  yield put(ticketActions.setPatrolCheckItemSaveValueLoading());
  if (error) {
    message.error(error);
    return;
  }
  /**
   * response.result
   * 0:正常
   * 1:异常
   * 2:非法值，无效数据
   */
  if (response.result === 1) {
    successCallback(response.errorMsg);
    return;
  }
  if (response.result === 2) {
    successCallback('INVALID_VALUE');
    return;
  }
  yield fork(ticketPatrolCheckItem, fetchPatrolCheckItemParams);
}

function* powerGridCount(params) {
  yield put(ticketActions.setPowerDetailtLodaing());
  const { response, error } = yield call(ticketService.powerGridCount, params);
  yield put(ticketActions.setPowerDetailtLodaing());

  if (error) {
    message.error(error);
    return;
  }
  const list = [];
  for (const i in response) {
    list.push({ roomTag: i, ...response[i] });
  }
  yield put(ticketActions.setPowerDetailGridCount(list));
}

function* powerGridList(params) {
  yield put(ticketActions.setPowerDetailtLodaing());
  const { response, error } = yield call(ticketService.powerGridList, params);
  yield put(ticketActions.setPowerDetailtLodaing());

  if (error) {
    message.error(error);
    return;
  }
  yield put(ticketActions.setPowerDetailGridList({ list: response.data, total: response.total }));
}

function* ticketRealtimeData({ idc, pointGuids }) {
  const { error, data } = yield call(fetchPointValues, {
    idc,
    pointGuids: pointGuids.reduce((mappings, { deviceGuid, pointCode }) => {
      if (mappings[deviceGuid] === undefined) {
        mappings[deviceGuid] = pointCode;
      } else {
        mappings[deviceGuid] += ',' + pointCode;
      }

      return mappings;
    }, {}),
  });
  if (error) {
    message.error(error);
  } else {
    yield put(subscriptionsSliceActions.receivedRealtimeData(data));
  }

  const { timeout } = yield race({
    shouldCancel: take(cancelTicketRealtimeData.type),
    timeout: delay(15 * 1000 * (error ? 2 : 1)), // 遇到 error 时，延长一下下一次请求发起的时间
  });

  if (timeout) {
    yield put(getTicketRealtimeData({ idc, pointGuids }));
  }
}

function* ticketMaintenanceRoomInfo({ params, successCallback }) {
  yield put(ticketActions.setMaintenanceRoomLoading());
  const { response, error } = yield call(ticketService.fetchTicketMaintenanceRoomInfo, params);
  if (response) {
    successCallback(response.data);
    yield put(ticketActions.setMaintenanceRoomInfo(response.data));
    yield put(ticketActions.setMaintenanceRoomTypes(response.data.map(item => item.roomType)));
  } else {
    message.error(error);
  }
  yield put(ticketActions.setMaintenanceRoomLoading());
}

function* ticketMaintenanceCheckItem(params) {
  yield put(ticketActions.setMaintenanceCheckItemLoading());

  const { response, error } = yield call(ticketService.maintenanceOrderCheckItem, params);
  if (response) {
    const { data } = response;
    const checkItemTypes = [];
    data.forEach(item =>
      item.maintenanceItemList.forEach(maintenanceItem => {
        checkItemTypes.push(maintenanceItem.deviceType);
      })
    );
    const rowSpansGroupByDataIndex = {};
    yield put(
      ticketActions.setMaintenanceCheckItem(
        data.map(item => {
          return {
            securityStds: item.securityStds,
            maintenanceItemList: item.maintenanceItemList.map(maintenanceItem => {
              const mergeCode = {
                maintenanceTypeMerge: `${maintenanceItem.roomTag}_$$_${maintenanceItem.deviceGuid}_$$_${maintenanceItem.maintenanceType}`,
                itemNameMerge: `${maintenanceItem.roomTag}_$$_${maintenanceItem.deviceGuid}_$$_${maintenanceItem.maintenanceType}_$$_${maintenanceItem.itemName}`,
                remark: maintenanceItem.remark,
                fileInfoList: maintenanceItem.fileInfoList,

                maintenanceMethodMerge: `${maintenanceItem.roomTag}_$$_${maintenanceItem.deviceGuid}_$$_${maintenanceItem.maintenanceType}_$$_${maintenanceItem.itemName}_$$_${maintenanceItem.maintenanceMethod}`,
              };
              if (rowSpansGroupByDataIndex[maintenanceItem.roomTag] === undefined) {
                rowSpansGroupByDataIndex[maintenanceItem.roomTag] = 1;
              } else {
                rowSpansGroupByDataIndex[maintenanceItem.roomTag] += 1;
              }

              if (rowSpansGroupByDataIndex[maintenanceItem.deviceType] === undefined) {
                rowSpansGroupByDataIndex[maintenanceItem.deviceType] = 1;
              } else {
                rowSpansGroupByDataIndex[maintenanceItem.deviceType] += 1;
              }

              if (rowSpansGroupByDataIndex[maintenanceItem.deviceGuid] === undefined) {
                rowSpansGroupByDataIndex[maintenanceItem.deviceGuid] = 1;
              } else {
                rowSpansGroupByDataIndex[maintenanceItem.deviceGuid] += 1;
              }

              if (rowSpansGroupByDataIndex[mergeCode.maintenanceTypeMerge] === undefined) {
                rowSpansGroupByDataIndex[mergeCode.maintenanceTypeMerge] = 1;
              } else {
                rowSpansGroupByDataIndex[mergeCode.maintenanceTypeMerge] += 1;
              }

              if (rowSpansGroupByDataIndex[mergeCode.itemNameMerge] === undefined) {
                rowSpansGroupByDataIndex[mergeCode.itemNameMerge] = 1;
              } else {
                rowSpansGroupByDataIndex[mergeCode.itemNameMerge] += 1;
              }
              if (rowSpansGroupByDataIndex[mergeCode.remark] === undefined) {
                rowSpansGroupByDataIndex[mergeCode.remark] = 1;
              } else {
                rowSpansGroupByDataIndex[mergeCode.remark] += 1;
              }

              if (rowSpansGroupByDataIndex[mergeCode.maintenanceMethodMerge] === undefined) {
                rowSpansGroupByDataIndex[mergeCode.maintenanceMethodMerge] = 1;
              } else {
                rowSpansGroupByDataIndex[mergeCode.maintenanceMethodMerge] += 1;
              }

              if (maintenanceItem.maintenanceValue === null) {
                return { ...maintenanceItem, editable: true, ...mergeCode };
              }
              return {
                ...maintenanceItem,
                ...mergeCode,
              };
            }),
          };
        })
      )
    );

    yield put(ticketActions.setMaintenanceCheckedTypes(uniq(checkItemTypes)));
    yield put(ticketActions.setMaintenanceCheckItemTypes(uniq(checkItemTypes)));
    yield put(ticketActions.setMaintenanceRowSpansGroupByDataIndex(rowSpansGroupByDataIndex));
  } else {
    message.error(error);
    yield put(ticketActions.failure());
  }
  yield put(ticketActions.setMaintenanceCheckItemLoading());
}

function* ticketInventoryTaskConfig(params) {
  const { data, error } = yield call(fetchInventoryTaskConfig, params);
  if (error) {
    message.error(error);
  }
  yield put(ticketActions.setInventoryTargetConfig(data));
}

function* watchTicketList() {
  while (true) {
    const { payload } = yield take(getTicketListAction.type);
    const task = yield fork(fetchTicketList, payload);
    const [reset] = yield race([take(resetTicketSearchValuesActionCreator.type), task]);
    if (reset) {
      cancel(task);
    }
  }
}

function* watchSetPagination() {
  while (true) {
    const { payload } = yield take(setPaginationThenGetDataActionCreator.type);
    yield fork(setPagination, payload);
  }
}

function* watchResetTicketSearchValues() {
  while (true) {
    const { payload } = yield take(resetTicketSearchValuesActionCreator.type);
    yield fork(resetSearchValues, payload);
  }
}

function* watchResetTakeOver() {
  while (true) {
    const { payload } = yield take(takeOverActionCreator.type);
    yield fork(takeOver, payload);
  }
}

function* watchRevokeTicket() {
  while (true) {
    const { payload } = yield take(revokeAction.type);
    yield fork(revoke, payload);
  }
}

function* watchTicketEndOf() {
  while (true) {
    const { payload } = yield take(ticketEndOfActionCreator.type);
    yield fork(endOf, payload);
  }
}

function* watchTicketSingleEndOf() {
  while (true) {
    const { payload } = yield take(ticketSingleEndOfActionCreator.type);
    yield fork(singleEndOf, payload);
  }
}

function* watchTicketFailureEndOf() {
  while (true) {
    const { payload } = yield take(ticketFailureEndOfActionCreator.type);
    yield fork(singleEndOf, payload);
  }
}

function* watchTicketBasicInfo() {
  while (true) {
    const { payload } = yield take(ticketBasicInfoActionCreator.type);
    yield fork(ticketBasicInfo, payload);
  }
}

function* watchTicketTransfer() {
  while (true) {
    const { payload } = yield take(ticketTransferActionCreator.type);
    yield fork(ticketTransfer, payload);
  }
}

function* watchTicketPatrolRoomInfo() {
  while (true) {
    const { payload } = yield take(ticketPatrolRoomInfoActionCreator.type);
    yield fork(ticketPatrolRoomInfo, payload);
  }
}

function* watchTicketPatrolCheckSubjectGroup() {
  while (true) {
    const { payload } = yield take(ticketPatrolCheckSubjectGroupActionCreator.type);
    yield fork(ticketPatrolCheckSubjectGroup, payload);
  }
}
function* watchTicketPatrolCheckItem() {
  while (true) {
    const { payload } = yield take(ticketPatrolCheckItemActionCreator.type);
    yield fork(ticketPatrolCheckItem, payload);
  }
}

function* watchTicketPatrolCheckItemPointResultSave() {
  while (true) {
    const { payload } = yield take(ticketPatrolCheckItemPointResultSaveActionCreator.type);
    yield fork(ticketPatrolCheckItemResultSave, payload);
  }
}

function* watchPowerGridCount() {
  while (true) {
    const { payload } = yield take(powerGridCountActionCreator.type);
    yield fork(powerGridCount, payload);
  }
}

function* watchPowerGridList() {
  while (true) {
    const { payload } = yield take(powerGridListActionCreator.type);
    yield fork(powerGridList, payload);
  }
}

function* watchGetTicketRealtimeData() {
  while (true) {
    const { payload } = yield take(getTicketRealtimeData.type);
    yield fork(ticketRealtimeData, payload);
  }
}

function* watchTicketMaintenanceRoomInfo() {
  while (true) {
    const { payload } = yield take(ticketMaintenanceRoomInfoActionCreator.type);
    yield fork(ticketMaintenanceRoomInfo, payload);
  }
}

function* watchTicketMaintenanceCheckItem() {
  while (true) {
    const { payload } = yield take(ticketMaintenanceCheckItemActionCreator.type);
    yield fork(ticketMaintenanceCheckItem, payload);
  }
}

function* watchSingleTakeOver() {
  while (true) {
    const { payload } = yield take(singleTakeOverActionCreator.type);
    yield fork(singleTakeOver, payload);
  }
}

function* watchGetTicketFiles() {
  while (true) {
    const { payload } = yield take(getTicketFilesActionCreator.type);
    yield fork(getTicketFiles, payload);
  }
}

function* watchTicketInventoryTaskConfigs() {
  while (true) {
    const { payload } = yield take(ticketInventoryTaskConfigActionCreator.type);
    yield fork(ticketInventoryTaskConfig, payload);
  }
}

export default [
  fork(watchTicketList),
  fork(watchSetPagination),
  fork(watchResetTicketSearchValues),
  fork(watchResetTakeOver),
  fork(watchTicketEndOf),
  fork(watchTicketSingleEndOf),
  fork(watchTicketBasicInfo),
  fork(watchTicketTransfer),
  fork(watchTicketPatrolRoomInfo),
  fork(watchTicketPatrolCheckItem),
  fork(watchTicketPatrolCheckItemPointResultSave),
  fork(watchPowerGridCount),
  fork(watchPowerGridList),
  fork(watchGetTicketRealtimeData),
  fork(watchTicketMaintenanceRoomInfo),
  fork(watchTicketMaintenanceCheckItem),
  fork(watchSingleTakeOver),
  fork(watchRevokeTicket),
  fork(watchTicketPatrolCheckSubjectGroup),
  fork(watchGetTicketFiles),
  fork(watchTicketInventoryTaskConfigs),
  fork(watchTicketFailureEndOf),
];
