import { call, fork, put, take } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { addRolePermissions } from '@manyun/auth-hub.service.add-role-permissions';
import { bindRolePermissions } from '@manyun/auth-hub.service.bind-role-permissions';

import { roleManageService } from '@manyun/dc-brain.legacy.services';
import { fetchAllPowerApiTreeSelect } from '@manyun/dc-brain.legacy.services/treeDataService';

import {
  fetchAllPowerTree, // 查询菜单树或菜单列表
  fetchDeleteRole, // 删除角色
  fetchPowerJoinRole, // 查询角色下面的权限列表
  fetchRoleManageDetail, // 查询角色详情
  fetchUnbindPowerFromRole, // 解绑权限
  loadRoleInfo,
  roleActions,
  submitConnectPower,
} from '../actions/roleActions';

/***************************** Subroutines ************************************/

function* fetchRoleList(pageNo, pageSize, searchName, searchType) {
  yield put(roleActions.request());
  const { response, error } = yield call(roleManageService.fetchRoleList, {
    pageNo,
    pageSize,
    searchName,
    searchType,
  });
  if (response) {
    yield put(roleActions.success(response));
    yield put(roleActions.saveSearchConditionSuccess({ pageNo, pageSize, searchName, searchType }));
  } else {
    message.error(error || '角色列表请求失败');
    yield put(roleActions.failure());
  }
}

function* PowerJoinRoleGroup(roleId) {
  yield put(roleActions.request());
  const { response, error } = yield call(roleManageService.fetchJoinRoleGroup, {
    roleId,
  });
  if (response) {
    yield put(
      roleActions.rolePowerListSuccess({
        list: response.data,
      })
    );
  } else {
    message.error(error || '角色权限请求失败');
    yield put(roleActions.failure());
  }
}

function* roleManageDetail(roleId, roleCode) {
  yield put(roleActions.request());
  const { response, error } = yield call(roleManageService.fetchRoleDetail, roleId, roleCode);

  if (response) {
    yield put(roleActions.detailSuccess(response));
    yield fork(PowerJoinRoleGroup, response.id, 1, 20, '', 'PERMISSION_NAME');
  } else {
    message.error(error || '角色详情请求失败');
    yield put(roleActions.failure());
  }
}

function* deleteRole(payload) {
  yield put(roleActions.request());
  const { roleId, pageNo, pageSize, searchName, searchType } = payload;
  const { response, error } = yield call(roleManageService.fetchDeleteRole, roleId);
  if (response) {
    message.success('删除角色成功');
    yield fork(fetchRoleList, pageNo, pageSize, searchName, searchType);
  } else {
    message.error(error || '删除角色请求失败');
    yield put(roleActions.failure());
  }
}

function* unbindPowerFromRole(roleId, permissionId, pageNo, pageSize, searchType, searchName) {
  yield put(roleActions.request());
  const { response, error } = yield call(
    roleManageService.fetchUnbindPowerFromRole,
    roleId,
    permissionId,
    pageNo,
    pageSize,
    searchType,
    searchName
  );
  if (response) {
    yield fork(PowerJoinRoleGroup, roleId, pageNo, pageSize, searchName, searchType);
  } else {
    message.error(error || '解绑权限请求失败');
    yield put(roleActions.failure());
  }
}

function* fetchAllPowerListTree(payload) {
  const data = yield call(fetchAllPowerApiTreeSelect);
  yield put(roleActions.fetchAllPowerListTreeSuccess(data));
}

function* submitConnectPowerToRole(payload) {
  const {
    pageNo,
    pageSize,
    searchName,
    permissionIds,
    searchType,
    roleIds,
    roleId,
    mode,
    operationType,
  } = payload;
  yield put(roleActions.editConnectPowerLoading());
  let apiError;
  if (operationType === 'single') {
    const { error } = yield call(bindRolePermissions, { roleIds, permissionIds });
    apiError = error;
  } else {
    const { error } = yield call(addRolePermissions, { roleIds, permissionIds });
    apiError = error;
  }

  yield put(roleActions.editConnectPowerLoading());
  if (apiError) {
    message.error(apiError.message || '关联权限请求失败');
    yield put(roleActions.failure());
    return;
  }
  yield put(roleActions.connectPowerVisible());
  message.success('关联权限成功');
  if (mode === 'edit') {
    yield fork(PowerJoinRoleGroup, roleId, pageNo, pageSize, searchName, searchType);
  }
}

/******************************************************************************/
/******************************* WATCHERS *************************************/
/******************************************************************************/

// 监听分页查询角色列表
export function* watchFetchRoleList() {
  while (true) {
    const { payload } = yield take(loadRoleInfo.type);
    const { pageNo, pageSize, searchName, searchType } = payload;
    yield fork(fetchRoleList, pageNo, pageSize, searchName, searchType);
  }
}

// 监听请求角色下面的权限列表action
export function* watchFetchPowerJoinRoleGroup() {
  while (true) {
    const { payload } = yield take(fetchPowerJoinRole.type);
    const { roleId, pageNo, pageSize, searchName, searchType } = payload;
    yield fork(PowerJoinRoleGroup, roleId, pageNo, pageSize, searchName, searchType);
  }
}

// 监听查询角色详情的action
export function* watchFetchRoleManageDetail() {
  while (true) {
    const { payload } = yield take(fetchRoleManageDetail.type);
    const { roleId, roleCode } = payload;
    yield fork(roleManageDetail, roleId, roleCode);
  }
}

// 删除角色
export function* watchFetchDeleteRole() {
  while (true) {
    const { payload } = yield take(fetchDeleteRole.type);
    yield fork(deleteRole, payload);
  }
}

// 解绑权限
export function* watchFetchUnbindPowerFromRole() {
  while (true) {
    const { payload } = yield take(fetchUnbindPowerFromRole.type);
    const { roleId, permissionId, pageNo, pageSize, searchType, searchName } = payload;
    yield fork(unbindPowerFromRole, roleId, permissionId, pageNo, pageSize, searchType, searchName);
  }
}

// 查询菜单树或菜单列表
export function* watchFetchAllPowerTree() {
  while (true) {
    const { payload } = yield take(fetchAllPowerTree.type);
    yield fork(fetchAllPowerListTree, payload);
  }
}

// 绑定权限
export function* watchSubmitConnectPower() {
  while (true) {
    const { payload } = yield take(submitConnectPower.type);
    yield fork(submitConnectPowerToRole, payload);
  }
}

export default [
  fork(watchFetchRoleManageDetail),
  fork(watchFetchRoleList),
  fork(watchFetchPowerJoinRoleGroup),
  fork(watchFetchDeleteRole),
  fork(watchFetchUnbindPowerFromRole),
  fork(watchFetchAllPowerTree),
  fork(watchSubmitConnectPower),
];
