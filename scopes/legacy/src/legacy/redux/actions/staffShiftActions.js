import { createAction } from '@reduxjs/toolkit';

import { STAFF_SHIFT_SLICE_NAME } from './../reducers/staffShiftSlice';

export { staffShiftActions } from './../reducers/staffShiftSlice';

export const getShiftDataActionCreator = createAction(`${STAFF_SHIFT_SLICE_NAME}/shift/getData`);
export const createShiftActionCreator = createAction(`${STAFF_SHIFT_SLICE_NAME}/shift/createShift`);
export const getDutyIsBeUsedActionCreator = createAction(
  `${STAFF_SHIFT_SLICE_NAME}/shift/getdutyIsBeUsed`
);
export const deleteShiftActionCreator = createAction(`${STAFF_SHIFT_SLICE_NAME}/shift/deleteShift`);
export const editShiftActionCreator = createAction(`${STAFF_SHIFT_SLICE_NAME}/shift/editShift`);
export const getAllShiftDataActionCreator = createAction(
  `${STAFF_SHIFT_SLICE_NAME}/shift/getAllshiftData`
);
export const getShiftSysDataActionCreator = createAction(
  `${STAFF_SHIFT_SLICE_NAME}/shift-sys/getData`
);
export const deleteShiftSysActionCreator = createAction(
  `${STAFF_SHIFT_SLICE_NAME}/shift-sys/deleteShiftSys`
);
export const createShiftSysActionCreator = createAction(
  `${STAFF_SHIFT_SLICE_NAME}/shift-sys/createShiftSys`
);
export const editShiftSysActionCreator = createAction(
  `${STAFF_SHIFT_SLICE_NAME}/shift-sys/edithiftSys`
);
export const getDutyGroupDataActionCreator = createAction(
  `${STAFF_SHIFT_SLICE_NAME}/duty-group/getData`
);
export const deleteDutyGroupActionCreator = createAction(
  `${STAFF_SHIFT_SLICE_NAME}/duty-group/deleteDutyGroup`
);
export const createDutyGroupActionCreator = createAction(
  `${STAFF_SHIFT_SLICE_NAME}/duty-group/createDutyGroup`
);
export const editDutyGroupActionCreator = createAction(
  `${STAFF_SHIFT_SLICE_NAME}/duty-group/editDutyGroup`
);
export const getGroupScheduleActionCreator = createAction(
  `${STAFF_SHIFT_SLICE_NAME}/group-schedule/getData`
);
export const deleteGroupScheduleActionCreator = createAction(
  `${STAFF_SHIFT_SLICE_NAME}/group-schedule/delete`
);
export const searchRoleActionCreator = createAction(
  `${STAFF_SHIFT_SLICE_NAME}/group-schedule/searchRole`
);
export const createRuleActionCreator = createAction(
  `${STAFF_SHIFT_SLICE_NAME}/group-schedule/createRule`
);
export const editRuleActionCreator = createAction(
  `${STAFF_SHIFT_SLICE_NAME}/group-schedule/editRule`
);
export const getAttGroupActionCreator = createAction(`${STAFF_SHIFT_SLICE_NAME}/attGroup/getData`);
export const deleteAttGroupActionCreator = createAction(
  `${STAFF_SHIFT_SLICE_NAME}/attGroup/delete`
);
export const createAttGroupActionCreator = createAction(
  `${STAFF_SHIFT_SLICE_NAME}/attGroup/createAttGroup`
);
export const getAttGroupDetailActionCreator = createAction(
  `${STAFF_SHIFT_SLICE_NAME}/attGroup/getAttGroupDetail`
);
export const getScheduleActionCreator = createAction(
  `${STAFF_SHIFT_SLICE_NAME}/attGroup/getSchedule`
);
export const attGroupScheduleRuleUpdateActionCreator = createAction(
  `${STAFF_SHIFT_SLICE_NAME}/attGroup/scheduleRuleUpdate`
);
export const editAttGroupActionCreator = createAction(
  `${STAFF_SHIFT_SLICE_NAME}/attGroup/editAttGroup`
);
export const getScheduleInMonthActionCreator = createAction(
  `${STAFF_SHIFT_SLICE_NAME}/attGroup/getScheduleInMonth`
);
export const getMonthyDataActionCreator = createAction(`${STAFF_SHIFT_SLICE_NAME}/monthy/getData`);
export const setMonthyPaginationThenGetDataActionCreator = createAction(
  `${STAFF_SHIFT_SLICE_NAME}/monthy/setMonthyPagination`
);
export const resetMonthySearchValuesActionCreator = createAction(
  `${STAFF_SHIFT_SLICE_NAME}/monthy/reset`
);

export const getDailyDataActionCreator = createAction(`${STAFF_SHIFT_SLICE_NAME}/daily/getData`);
export const setDailyPaginationThenGetDataActionCreator = createAction(
  `${STAFF_SHIFT_SLICE_NAME}/daily/setDailyPagination`
);
export const resetDailySearchValuesActionCreator = createAction(
  `${STAFF_SHIFT_SLICE_NAME}/daily/reset`
);

export const getCheckDataActionCreator = createAction(`${STAFF_SHIFT_SLICE_NAME}/check/getData`);
export const resetCheckSearchValuesActionCreator = createAction(
  `${STAFF_SHIFT_SLICE_NAME}/check/reset`
);
export const setCheckPaginationThenGetDataActionCreator = createAction(
  `${STAFF_SHIFT_SLICE_NAME}/check/setCheckPagination`
);

export const getRecordDataActionCreator = createAction(`${STAFF_SHIFT_SLICE_NAME}/record/getData`);
export const resetRecordSearchValuesActionCreator = createAction(
  `${STAFF_SHIFT_SLICE_NAME}/record/reset`
);
export const setRecordPaginationThenGetDataActionCreator = createAction(
  `${STAFF_SHIFT_SLICE_NAME}/record/setRecordPagination`
);
export const getAttGroupListByShiftsActionCreator = createAction(
  `${STAFF_SHIFT_SLICE_NAME}/shift-sys/getAttGroupListByShifts`
);
export const getStatutoryHolidayActionCreator = createAction(
  `${STAFF_SHIFT_SLICE_NAME}/schedule/getStatutoryHoliday`
);
