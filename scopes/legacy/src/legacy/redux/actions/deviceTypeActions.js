import { createAction } from '@reduxjs/toolkit';

export { deviceTypeActions } from '../reducers/deviceTypeSlice';

export const fetchDeviceTypeListPage = createAction('device-type-management/DEVICE_TYPE_PAGE');
export const fetchDeviceTypeDetail = createAction('device-type-management/DEVICE_TYPE_DETAIL');
export const deleteDeviceTypeAction = createAction(
  'device-type-management/DELETE_DEVICE_TYPE_ACTION'
);
