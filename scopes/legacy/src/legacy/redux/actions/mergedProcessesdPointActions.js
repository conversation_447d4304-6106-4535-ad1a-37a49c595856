import { createAction } from '@reduxjs/toolkit';

export { mergedProcessesdPointActions } from '../reducers/mergedProcessesdPointSlice';

// 配置聚合测点时，拖放测点树更新聚合测点表单的值
export const dropUpdateMergedPointForm = createAction(
  'mergedProcessedPoint/DROP_UPDATE_MERGED_POINT_FORM'
);

export const createMergedPoint = createAction('mergedProcessedPoint/CREATE_MERGED_POINT');

export const getUpdatePressedDIStatuesList = createAction(
  'mergedProcessedPoint/UPDATE_PRESSES_DI_STATUS_LIST'
);

export const createPressedPoint = createAction('mergedProcessedPoint/CREATE_PRESSED_POINT');

export const changeExpressInfos = createAction('mergedProcessedPoint/CHANGE_EXPRESS_INFOS');

export const searchPointIsMerged = createAction('mergedProcessedPoint/SEARCH_POINT_IS_MERGED');

export const mergeTheMergedPointConfig = createAction(
  'mergedProcessedPoint/MERGE_THE_MERGED_POINT_CONFIG'
);

export const getPointDetail = createAction('mergedProcessedPoint/GET_POINT_DETAIL');

export const updatePressedPointActionCreator = createAction(
  'mergedProcessedPoint/UPDATE_PRESSED_POINT'
);

export const exprInsertPointIntoRootActionCreator = createAction(
  'mergedProcessedPoint/EXPR_INSERT_POINT_INTO_ROOT_ACTION_CREATOR'
);

export const exprInsertPointIntoNestedActionCreator = createAction(
  'mergedProcessedPoint/EXPR_INSERT_POINT_INTO_NESTED_ACTION_CREATOR'
);

export const testExprActionCreator = createAction('mergedProcessedPoint/TEST_EXPR');
