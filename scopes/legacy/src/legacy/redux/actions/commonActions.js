import { createAction } from '@reduxjs/toolkit';

import { commonSliceActions } from '@manyun/dc-brain.state.common';

/**
 * @deprecated Use `commonSliceActions` from `@manyun/dc-brain.state.common`
 * */
export const commonActions = commonSliceActions;

/**
 * @deprecated Use `syncCommonDataAction` from `@manyun/dc-brain.state.common`
 */
export const syncCommonDataActionCreator = createAction('common/syncCommonDataActionCreator');
