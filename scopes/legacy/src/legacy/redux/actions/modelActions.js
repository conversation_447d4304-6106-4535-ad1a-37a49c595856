import { createAction } from '@reduxjs/toolkit';

export { modelActions } from '../reducers/modelSlice';

export const fetchModelListPage = createAction('model-management/MODEL_PAGE');
export const fetchModelDetail = createAction('model-management/MODEL_DETAIL');
export const deleteModelAction = createAction('model-management/DELETE_MODEL_ACTION');
export const setModelPagination = createAction('model-management/SET_MODEL_PAGINATION');
