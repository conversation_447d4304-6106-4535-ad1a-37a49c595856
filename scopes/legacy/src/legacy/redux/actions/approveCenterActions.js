import { createAction } from '@reduxjs/toolkit';

import { APPROVE_CENTER_SLICE_NAME, approveCenterActions } from '../reducers/approveCenterSlice';

export { approveCenterActions };

export const getApproveCenterListActionCreator = createAction(
  APPROVE_CENTER_SLICE_NAME + '/GET_APPROVE_CENTER_LIST'
);
export const resetSearchValuesActionCreator = createAction(
  APPROVE_CENTER_SLICE_NAME + 'RESET_SEARCH_VALUES'
);
export const setSearchValuesActionCreator = createAction(
  APPROVE_CENTER_SLICE_NAME + 'SET_SEARCH_VALUES'
);
export const setPaginationThenGetDataActionCreator = createAction(
  APPROVE_CENTER_SLICE_NAME + 'SET_PAGINATION_THEN_GET_DATA'
);
