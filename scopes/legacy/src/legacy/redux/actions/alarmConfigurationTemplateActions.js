import { createAction } from '@reduxjs/toolkit';

export { alarmConfigurationTemplateActions } from '../reducers/alarmConfigurationTemplateSlice';
export const alarmTypeAction = createAction('FETCH_ALARM_TYPE');
export const alarmLargeClassAction = createAction('FETCH_ALARM_LARGE_CLASS');
export const alarmItemTypeAction = createAction('FETCH_ALARM_ITEM_TYPE');
//告警配置树
export const getAlarmConfigurationTree = createAction('GET_ALARM_CONFIGURATION_TREE');
// 告警配置最后一级
export const getAlarmConfigurationLastTree = createAction('GET_ALARM_CONFIGURATION_LAST_TREE');
export const fetchDragAndDropDataAction = createAction('FETCH_DRAG_AND_DROP_DATA');
// 所有模板组
export const getAllTemplateGroupList = createAction('GET_ALL_TEMPLATE_GROUP_LIST');
// 已关联模板组
export const getAssociatedGroupList = createAction('GET_ASSOCIATED_GROUP_LIST');
// 关联模板组
export const monitorGroupAssociateScheme = createAction('MONITOR_GROUP_ASSOCIATE_SCHEME');
// 取消关联
export const removeAssociate = createAction('REMOVE_ASSOCIATE');
// 模板列表分页
export const fetchTemplateList = createAction('FETCH_TEMPLATE_LIST');
// 关联的告警项
export const fetchAssociatedItem = createAction('FETCH_ASSOCIATED_ITEM');
// 批量修改启用状态
export const changeAvailableStatus = createAction('CHANGE_AVAILABLE_STATUS');
// 编辑模板
export const monitorGroupUpdate = createAction('MONITOR_GROUP_UPDATE');
// 新建模板
export const monitorGroupAdd = createAction('MONITOR_GROUP_ADD');
// 新建模板
export const getEffectiveDomainList = createAction('GET_EFFECTIVE_DOMAIN_LIST');
export const fetchAlarmConfigurationTree = createAction('FETCH_ALARM_CONFIGURATION_TREE');

// 将用户拖拽的告警文案配置字段暂存到 Store
export const insertNotificationTplFieldAction = createAction(
  'templateAlarmConfig/insertNotificationTplField'
);
// 更新 Store 中暂存的用户编辑的告警文案配置
export const updateNotificationTplAction = createAction('alarmConfig/updateNotificationTpl');
// 删除 Store 中暂存的用户编辑的告警文案配置的某项
export const deleteNotificationTplFieldAction = createAction(
  'alarmConfig/deleteNotificationTplField'
);
// 重置 Store 中暂存的告警文案配置
export const resetNotificationTplAction = createAction('alarmConfig/resetNotificationTpl');

// 提交新建模板配置
export const submitNewTemplateConfigAction = createAction('alarmConfig/submitNewTemplateConfig');

// 查询监控项
export const searchTemplatePointConfigAction = createAction(
  'templateAlarmConfig/searchTemplatePointConfig'
);

// 查看模板 解析文案
export const reviewAlarmNoticifyAction = createAction('templateAlarmConfig/reviewAlarmNoticify');

// 保存编辑模板的模板信息
export const saveEditTemplateModuleAction = createAction(
  'templateAlarmConfig/saveEditTemplateModule'
);

// 提交编辑模板配置
export const submitEditTemplateConfigAction = createAction('alarmConfig/submitEditTemplateConfig');

// 粘贴模板
export const pasteNoticifyAction = createAction('alarmConfig/pasteNoticify');

// 删除监控项
export const deletePointConfigAction = createAction('alarmConfig/deletePointConfig');

// 删除模板
export const deleteTemplateAction = createAction('deleteTemplate');

// 查询事件类型
export const searchEventTypeAction = createAction('searchEventType');

// 新建自定义告警
export const submitNewCustomTemplateConfigAction = createAction(
  'alarmConfig/submitNewCustomTemplateConfigAction'
);

// 保存自定义告警
export const saveEditCustomTemplateModuleAction = createAction(
  'alarmConfig/saveEditCustomTemplateModuleAction'
);

// 更新自定义告警项
export const submitEditCustomTemplateConfigAction = createAction(
  'alarmConfig/submitEditCustomTemplateConfigAction'
);
