import { createAction } from '@reduxjs/toolkit';

import { OPERATION_CHECK_CONFIG_SLICE_NAME } from '../reducers/operationCheckConfigSlice';

export { operationCheckConfigActions } from '../reducers/operationCheckConfigSlice';

export const getDataActionCreator = createAction(`${OPERATION_CHECK_CONFIG_SLICE_NAME}/getData`);
export const setPaginationThenGetDataActionCreator = createAction(
  `${OPERATION_CHECK_CONFIG_SLICE_NAME}/setPaginationThenGetData`
);
export const resetSearchValuesActionCreator = createAction(
  `${OPERATION_CHECK_CONFIG_SLICE_NAME}/resetSearchValues`
);
export const deleteConfigActionCreator = createAction(
  `${OPERATION_CHECK_CONFIG_SLICE_NAME}/deleteConfig`
);
export const createConfigActionCreator = createAction(
  `${OPERATION_CHECK_CONFIG_SLICE_NAME}/createConfig`
);
export const editConfigActionCreator = createAction(
  `${OPERATION_CHECK_CONFIG_SLICE_NAME}/editConfig`
);
export const getConfigInfoActionCreator = createAction(
  `${OPERATION_CHECK_CONFIG_SLICE_NAME}/getConfigInfo`
);
export const getConfigCheckItemsActionCreator = createAction(
  `${OPERATION_CHECK_CONFIG_SLICE_NAME}/getConfigCheckItems`
);
