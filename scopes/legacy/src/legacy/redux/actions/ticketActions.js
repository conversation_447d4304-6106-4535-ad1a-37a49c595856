import { createAction } from '@reduxjs/toolkit';

export { ticketActions } from '../reducers/ticketSlice';

//
export const getTicketListAction = createAction('ticket/getTicketListAction');
export const takeOverAction = createAction('ticket/takeOverAction');
export const revokeAction = createAction('ticket/revokeAction');
export const resetTicketSearchValuesActionCreator = createAction(
  'ticket/resetTicketSearchValuesActionCreator'
);
export const setPaginationThenGetDataActionCreator = createAction(
  'ticket/setPaginationThenGetDataActionCreator'
);
export const takeOverActionCreator = createAction('ticket/takeOverActionCreator');
export const ticketEndOfActionCreator = createAction('ticket/ticketEndOfActionCreator');
export const ticketFailureEndOfActionCreator = createAction(
  'ticket/ticketFailureEndOfActionCreator'
);
export const ticketBasicInfoActionCreator = createAction('ticket/ticketBasicInfoActionCreator');
export const ticketTransferActionCreator = createAction('ticket/ticketTransferActionCreator');
export const ticketPatrolRoomInfoActionCreator = createAction(
  'ticket/ticketPatrolRoomInfoActionCreator'
);
export const ticketPatrolCheckItemActionCreator = createAction(
  'ticket/ticketPatrolCheckItemActionCreator'
);
export const ticketPatrolCheckItemPointResultSaveActionCreator = createAction(
  'ticket/ticketPatrolCheckItemPointResultSaveActionCreator'
);
export const powerGridCountActionCreator = createAction('ticket/powerGridCountActionCreator');
export const powerGridListActionCreator = createAction('ticket/powerGridListActionCreator');
export const getTicketRealtimeData = createAction('ticket/getTicketRealtimeData');
export const cancelTicketRealtimeData = createAction('ticket/cancelTicketRealtimeData');

export const ticketMaintenanceRoomInfoActionCreator = createAction(
  'ticket/ticketMaintenanceRoomInfoActionCreator'
);
export const ticketMaintenanceCheckItemActionCreator = createAction(
  'ticket/ticketMaintenanceCheckItemActionCreator'
);
export const ticketSingleEndOfActionCreator = createAction('ticket/ticketSingleEndOfActionCreator');
export const singleTakeOverActionCreator = createAction('ticket/singleTakeOverActionCreator');
export const ticketPatrolCheckSubjectGroupActionCreator = createAction(
  'ticket/ticketPatrolCheckSubjectGroupActionCreator'
);
export const getTicketFilesActionCreator = createAction('ticket/getTicketFile');
export const ticketInventoryTaskConfigActionCreator = createAction(
  'ticket/ticketInventoryTaskConfigActionCreator'
);
