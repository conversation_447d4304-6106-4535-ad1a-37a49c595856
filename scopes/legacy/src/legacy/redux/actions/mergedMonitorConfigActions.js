import { createAction } from '@reduxjs/toolkit';

import { MERGED_MONITOR_CONFIG_SLICE_NAME } from './../reducers/mergedMonitorConfigSlice';

export { mergedMonitorConfigActions } from './../reducers/mergedMonitorConfigSlice';

export const getDataActionCreator = createAction(`${MERGED_MONITOR_CONFIG_SLICE_NAME}/getData`);
export const setPaginationThenGetDataActionCreator = createAction(
  `${MERGED_MONITOR_CONFIG_SLICE_NAME}/setPaginationThenGetData`
);
export const resetSearchValuesActionCreator = createAction(
  `${MERGED_MONITOR_CONFIG_SLICE_NAME}/resetSearchValues`
);
export const batchUpdateMergedMonitorConfigsActionCreator = createAction(
  `${MERGED_MONITOR_CONFIG_SLICE_NAME}/batchUpdateMergedMonitorConfigs`
);
export const deleteMergedMonitorConfigActionCreator = createAction(
  `${MERGED_MONITOR_CONFIG_SLICE_NAME}/deleteMergedMonitorConfig`
);
export const createNewMergedMonitorConfigActionCreator = createAction(
  `${MERGED_MONITOR_CONFIG_SLICE_NAME}/createNewMergedMonitorConfig`
);
export const updateMergedMonitorConfigActionCreator = createAction(
  `${MERGED_MONITOR_CONFIG_SLICE_NAME}/updateMergedMonitorConfig`
);
export const getChildItemsActionCreator = createAction(
  `${MERGED_MONITOR_CONFIG_SLICE_NAME}/getChildItems`
);
export const updateChildItemsActionCreator = createAction(
  `${MERGED_MONITOR_CONFIG_SLICE_NAME}/UPDATE_CHILD_ITEMS`
);
export const getBaseInfoActionCreator = createAction(
  `${MERGED_MONITOR_CONFIG_SLICE_NAME}/GET_BASE_INFO`
);
