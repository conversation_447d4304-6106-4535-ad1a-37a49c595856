import { createAction } from '@reduxjs/toolkit';

import { INSPECTION_CONFIG_SLICE_NAME } from './../reducers/inspectionConfigSlice';

export { inspectionConfigActions } from './../reducers/inspectionConfigSlice';

export const getDataActionCreator = createAction(`${INSPECTION_CONFIG_SLICE_NAME}/getData`);
export const setPaginationThenGetDataActionCreator = createAction(
  `${INSPECTION_CONFIG_SLICE_NAME}/setPaginationThenGetData`
);
export const resetSearchValuesActionCreator = createAction(
  `${INSPECTION_CONFIG_SLICE_NAME}/resetSearchValues`
);
export const deleteConfigActionCreator = createAction(
  `${INSPECTION_CONFIG_SLICE_NAME}/deleteConfig`
);
export const getChildItemsActionCreator = createAction(
  `${INSPECTION_CONFIG_SLICE_NAME}/getChildItems`
);
export const getPointsActionCreator = createAction(`${INSPECTION_CONFIG_SLICE_NAME}/getPoints`);
export const createConfigActionCreator = createAction(
  `${INSPECTION_CONFIG_SLICE_NAME}/createConfig`
);
export const editConfigActionCreator = createAction(`${INSPECTION_CONFIG_SLICE_NAME}/editConfig`);
