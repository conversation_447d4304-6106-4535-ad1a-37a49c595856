import { createAction } from '@reduxjs/toolkit';

import { ARRIVAL_ASSET_SLICE_NAME, arrivalAssetActions } from '../reducers/arrivalAssetSlice';

export { arrivalAssetActions };

export const getArrivalAssetListActionCreator = createAction(
  ARRIVAL_ASSET_SLICE_NAME + '/GET_ARRIVAL_ASSET_LIST'
);

export const setPaginationThenGetDataActionCreator = createAction(
  `${ARRIVAL_ASSET_SLICE_NAME}/SET_PAGINATION_THEN_GET_DATA`
);

export const initialArrivalAssetListActionCreator = createAction(
  ARRIVAL_ASSET_SLICE_NAME + '/INITIAL_ARRIVAL_ASSET_LIST'
);

export const updatePageActiveTabKeyActionCreator = createAction(
  ARRIVAL_ASSET_SLICE_NAME + '/UPDATE_PAGE_ACTIVE_TAB_KEY'
);
