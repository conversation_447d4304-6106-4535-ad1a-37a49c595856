import { createAction } from '@reduxjs/toolkit';

export { alarmScreenActions } from '../reducers/alarmScreenSlice';

export const fetchAlarmScreenList = createAction('FETCH_ALARM_SCREEN_LIST');
export const fetchConfirm = createAction('FETCH_CONFIRM');
export const fetchCreate = createAction('FETCH_CREATE');
export const fetchWithdrawScreen = createAction('FETCH_WITHDRAW_SCREEN');
export const getHistoryAlarmList = createAction('FETCH_HISTORY_ALARM_LIST');
// export const fetchAlarmDetailInfo = createAction('FETCH_ALARM_DETAIL_INFO');
export const fetchBatchConfirm = createAction('FETCH_BATCH_CONFIRM');
export const fetchAlarmDelete = createAction('FETCH_ALARM_DELETE');
export const fetchEventList = createAction('FETCH_EVENT_LIST');
// export const fetchCreateTicketConfirm = createAction('FETCH_CREATE_TICKET_CONFIRM');
export const fetchCustomerList = createAction('FETCH_CUSTOMER_LIST');
export const fetchEventTypeList = createAction('FETCH_EVENT_TYPE_LIST');
export const createEventContent = createAction('CREATE_EVENT_CONTENT');
export const fetchCreateEventConfirm = createAction('FETCH_CREATE_EVENT_CONFIRM');
export const fetchAlarmDetail = createAction('FETCH_ALARM_DETAIL');
export const fetchAlarmConvergenceList = createAction('FETCH_ALARM_CONVERGENCE_LIST');
export const fetchMetaCategoryNormalize = createAction('FETCH_META_CATEGORY_NORMALIZE');
export const getAlarmScreenChartData = createAction('GET_ALARM_SCREEN_CHAR_DATA');
