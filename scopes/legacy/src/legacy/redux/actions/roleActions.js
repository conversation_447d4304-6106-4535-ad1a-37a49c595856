import { createAction } from '@reduxjs/toolkit';

export { roleActions } from './../reducers/roleSlice';

// 分页查询角色信息
export const loadRoleInfo = createAction('ROLE_LIST');

// 分页查询角色关联的权限信息
export const fetchPowerJoinRole = createAction('FETCH_POWER_JOIN_ROLE');

// 查询角色基本信息
export const fetchRoleManageDetail = createAction('FETCH_ROLE_MANAGE_DETAIL');

// 删除角色
export const fetchDeleteRole = createAction('DELETE_ROLE');

// 解绑权限
export const fetchUnbindPowerFromRole = createAction('UNBIND_POWER_FROM_ROLE');

// 查询菜单树或菜单列表
export const fetchAllPowerTree = createAction('FETCH_ALL_POWER_TREE');

// 绑定权限
export const submitConnectPower = createAction('SUBMIT_CONNECT_POWER');
