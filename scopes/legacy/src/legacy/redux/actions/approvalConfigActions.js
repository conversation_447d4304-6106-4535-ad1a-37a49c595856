import { createAction } from '@reduxjs/toolkit';

export { approvalConfigActions } from './../reducers/approvalConfigSlice';

// 查询审批流程列表
export const approvalProcessList = createAction('APPROVAL_PROCESS_LIST');

// 创建审批流程配置
export const approvalProcessCreate = createAction('APPROVAL_PROCESS_CREATE');

// 审批流程详情
export const approvalProcessDetail = createAction('APPROVAL_PROCESS_DETAIL');

// 审批流程编辑
export const approvalProcessEdit = createAction('APPROVAL_PROCESS_EDIT');

// 审批场景列表
export const approvalScenesList = createAction('APPROVAL_SCENES_LIST');

// 审批场景编辑
export const approvalScenesEdit = createAction('APPROVAL_SCENES_EDIT');

// 审批流程无分页查询
export const approvalProcessListWithoutPage = createAction('APPROVAL_PROCESS_LIST_WITHOUT_PAGE');

// 审批流程场景关联关系查询
export const scenesProcessRelateList = createAction('SCENES_PROCESS_RELATE_LIST');

// 审批流程场景关联关系编辑
export const scenesProcessRelateEdit = createAction('SCENES_PROCESS_RELATE_EDIT');

export const deleteScenesProcessActionCreator = createAction('SCENES_PROCESS_RELATE_DELETE');
