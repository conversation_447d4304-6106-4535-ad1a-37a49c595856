import { createAction } from '@reduxjs/toolkit';

import {
  INFRA_ROOM_GRAPHIX_SLICE_NAME,
  infraRoomGraphixActions,
} from './../reducers/infraRoomGraphixSlice';

export { infraRoomGraphixActions };

export const initializeActionCreator = createAction(INFRA_ROOM_GRAPHIX_SLICE_NAME + '/INITIALIZE');
export const saveGraphActionCreator = createAction(INFRA_ROOM_GRAPHIX_SLICE_NAME + '/SAVE_GRAPH');
export const getGraphActionCreator = createAction(INFRA_ROOM_GRAPHIX_SLICE_NAME + '/GET_GRAPH');
