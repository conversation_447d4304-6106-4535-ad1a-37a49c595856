import { createAction } from '@reduxjs/toolkit';

export { userManageActions } from './../reducers/userManageSlice';

export const loadUserManageInfo = createAction('USER_MANAGE');
export const fetchDelete = createAction('FETCH_DELETE');
export const fetchUserManageDetail = createAction('FETCH_USER_MANAGE_DETAIL');
export const fetchEditConfirm = createAction('EDIT_CONFIRM');
export const fetchLog = createAction('FETCH_LOG');
export const fetchEditLoginSettingsConfirm = createAction('FETCH_EDIT_LOGIN_SETTINGS_CONFIRM');
export const fetchJoinUserGroup = createAction('FETCH_JOIN_USER_GROUP');
export const fetchRemoveUserGroup = createAction('FETCH_REMOVE_USER_GROUP');
export const fetchAddNewUser = createAction('FETCH_ADD_NEW_USER');
