import { createAction } from '@reduxjs/toolkit';

import { INVENTORY_CONFIG_SLICE_NAME } from './../reducers/inventoryConfigSlice';

export { inventoryConfigActions } from './../reducers/inventoryConfigSlice';

export const getDataActionCreator = createAction(`${INVENTORY_CONFIG_SLICE_NAME}/getData`);
export const setPaginationThenGetDataActionCreator = createAction(
  `${INVENTORY_CONFIG_SLICE_NAME}/setPaginationThenGetData`
);
export const resetSearchValuesActionCreator = createAction(
  `${INVENTORY_CONFIG_SLICE_NAME}/resetSearchValues`
);
export const deleteConfigActionCreator = createAction(
  `${INVENTORY_CONFIG_SLICE_NAME}/deleteConfig`
);
export const createConfigActionCreator = createAction(
  `${INVENTORY_CONFIG_SLICE_NAME}/createConfig`
);
export const editConfigActionCreator = createAction(`${INVENTORY_CONFIG_SLICE_NAME}/editConfig`);
export const getConfigInfoActionCreator = createAction(
  `${INVENTORY_CONFIG_SLICE_NAME}/getConfigInfo`
);
