import { createAction } from '@reduxjs/toolkit';

export { roomActions } from './../reducers/roomSlice';

// 分页查询包间信息
export const fetchRoomListPage = createAction('ROOM_PAGE');
// 查询查询机房编号和名称
export const fetchRomeCodeAndName = createAction('FETCH_ROOM_CODE_AND_NAME');
// 提交新建包间表单
export const fetchCreateRoomConfirm = createAction('FETCH_CREATE_ROOM_CONFIRM');
// 获取机房、楼栋、包间三级结构
export const fetchMetaSpace = createAction('FETCH_META_SPACE');
//修改包间信息接口
export const fetchUpdateRoom = createAction('FETCH_UPDATE_Room');

export const deleteRoomActionCreator = createAction('infrastructure_room/DELETE_ROOM');
