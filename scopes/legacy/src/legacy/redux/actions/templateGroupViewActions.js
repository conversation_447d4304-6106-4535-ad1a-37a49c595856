import { createAction } from '@reduxjs/toolkit';

export { templateGroupViewActions } from './../reducers/templateGroupViewSlice';

export const searchTemplateGroupPagesActions = createAction('FETCH_TEMPLATE_GROUP_VIEW_PAGES');
export const changeSwitchAvailableAction = createAction('CHANGE_SWITCH_AVAILABLE');
export const changeSomeSwitchAvailableAction = createAction('CHANGE_SOME_SWITCH_AVAILABLE');
export const selectKeysAction = createAction('SELECT_KEYS');
export const fetchTemplateTreeAction = createAction('FETCH_TEMPLATE_TREE');
export const fetchTemplateTreeChildrenAction = createAction('FETCH_TEMPLATE_TREE_CHILDREN');

// 拖拽的电力数据
export const selectElectricTableDateAction = createAction('SELECT_ELECTRIC_TABLE_DATA');
// 电力数据
export const electricTableDateAction = createAction('ELECTRIC_TABLE_DATA');

// 关联的模板
export const fetchConnectTemplatesAction = createAction('FETCH_CONNECT_TEMPLATES');

// 电力搜索
export const filterElectricAction = createAction('FILTER_ELECTRIC_TEMPLATES');

// 查看模板组的生效域
export const fetchTemplateGroupAreaAction = createAction('FETCH_TEMPLATE_GROUP_AREA');

export const searchAreaIdcAction = createAction('SEARCH_AREA_IDC');

// 生效域配置
export const searchAreaConfigAction = createAction('SEARCH_AREA_CONFIG');

// 新建模板组  存 input value

export const saveCreateTemplateInputValueAction = createAction('SAVE_CREATE_TEMPLATE_INPUT');

export const saveCreateTemplateRadioValueAction = createAction('SAVE_CREATE_TEMPLATE_RADIO');

export const fetchCreateTemplateGroupAction = createAction('FETCH_CREATE_TEMPLATE_GROUP');

// 关联生效域
export const saveCheckedKeysAction = createAction('SAVE_CHECK_KEYS');

// 查询所有模板组
export const fetchAllTemplateGroupAction = createAction('FETCH_ALL_TEMPLATE_GROUP');

// 查询所有模板组后选择
export const saveSelectTemplateGroupAction = createAction('SAVE_SELECT_TEMPLATE_GROUP');

export const searchTemplteFromNameAction = createAction('SEARCH_TEMPLATE_GROUP_USE_NAME');

// 删除模板组
export const deleteTemplateInAreaAction = createAction('DELETE_TEMPLATE_IN_AREA');

// 手动搜索
export const handeleSearchAreaAndIdcAction = createAction('HANDLE_SEARCH_AREA_IDC');

// 根据模板组id 请求模板
export const getTemplateByTemplateGroupId = createAction('GET_TEMPLATE_BY_TEMPLATE_ID');

// 编辑区域
export const getAreaConfigById = createAction('GET_AREA_CONFIG_BY_ID');

// 取消关联生效域
export const cancleConnectArea = createAction('CANCLE_CONNECT_AREA');

// 更新模板组
export const updateTemplateGroupConfig = createAction('UPDATE_TEMPLATE_GROUP_CONFIG');
export const connectRegionToTemplate = createAction('CONNENT_REGION_TO_TEMPLATE');
export const connectTemplateInArea = createAction('CONNENT_TEMPLATE_IN_AREA');

export const connectTemplateInAreaSearchTemplate = createAction(
  'CONNENT_TEMPLATE_IN_AREA_SEARCH_TEMPLATE'
);

export const fetchTemplateMessByMetaCode = createAction('FETCH_TEMPLATE_MESS_BY_METACODE');
