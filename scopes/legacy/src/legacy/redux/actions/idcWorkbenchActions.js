import { createAction } from '@reduxjs/toolkit';

export { idcWorkbenchActions } from '../reducers/idcWorkbenchSlice';

const prefix = 'monitoring_idc/';

export const initializeActionCreator = createAction(prefix + 'INITIALIZE');
export const getChartDataActionCreator = createAction('GET_IDC_CHAR_DATA');
export const getNoticesActionCreator = createAction(prefix + 'GET_NOTICES');
export const terminateGetStatisticsIntervalActionCreator = createAction(
  prefix + 'TERMINATE_GET_STATISTICS_INTERVAL'
);
export const terminateGetRunningStatesRealtimeDataIntervalActionCreator = createAction(
  prefix + 'TERMINATE_GET_RUNNING_STATES_REALTIME_DATA_INTERVAL'
);
export const terminateGetRoomRealtimeDataIntervalActionCreator = createAction(
  prefix + 'TERMINATE_GET_ROOM_REALTIME_DATA_INTERVAL'
);
export const terminateGetBlocksAlarmsIntervalActionCreator = createAction(
  prefix + 'TERMINATE_GET_BLOCKS_ALARMS_INTERVAL'
);
export const getBaseTasksInIdcActionCreator = createAction(prefix + 'getBaseTasksInIdc');
export const getTicketDataActionCreator = createAction(prefix + 'getTicketData');
export const getBlockDevicesActionCreator = createAction(prefix + 'GET_BLOCK_DEVICES');
export const runningStatesActiveBlockChangeActionCreator = createAction(
  prefix + 'RUNNING_STATES_ACTIVE_BLOCK_CHANGE'
);
export const roomsInfoActiveBlockChangeActionCreator = createAction(
  prefix + 'ROOMS_INFO_ACTIVE_BLOCK_CHANGE'
);
export const subUnsubRunningStatesDataActionCreator = createAction(
  prefix + 'SUB_UNSUB_RUNNING_STATES_DATA'
);
export const subUnsubRoomsDataActionCreator = createAction(prefix + 'SUB_UNSUB_ROOMS_DATA');
export const fetchIdcWorkbenchPUE = createAction(prefix + 'GET_PUE');

export const getEventActionCreator = createAction(prefix + 'GET_EVENT');
export const getToDoListActionCreator = createAction(prefix + 'GRT_TO_DO_LIST');
export const getAttStaffSchedulesActionCreator = createAction(prefix + 'GRT_ATT_STAFF_SCHEDULES');
export const initializeIdcActionCreator = createAction(prefix + 'INITIALIZE_IDC');
