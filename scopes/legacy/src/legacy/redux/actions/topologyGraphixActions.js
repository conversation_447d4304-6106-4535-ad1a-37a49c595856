import { createAction } from '@reduxjs/toolkit';

import {
  TOPOLOGY_GRAPHIX_SLICE_NAME,
  topologyGraphixActions,
} from './../reducers/topologyGraphixSlice';

export { topologyGraphixActions };

const prefix = TOPOLOGY_GRAPHIX_SLICE_NAME + '/';

export const initializeActionCreator = createAction(prefix + 'INITIALIZE');
export const getHVACGraphActionCreator = createAction(prefix + 'GET_HVAC_GRAPH');
export const getFuelSystemGraphAction = createAction(prefix + 'getFuelSystemGraph--async');
