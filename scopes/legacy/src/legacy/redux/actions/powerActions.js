import { createAction } from '@reduxjs/toolkit';

export { powerActions } from './../reducers/powerSlice';

// 分页查询菜单信息
export const fetchPowerMenuPage = createAction('POWER_PAGE');
// 删除菜单
export const fetchDeletePower = createAction('FETCH_DELETE_POWER');
// 添加
export const fetchCreatePower = createAction('FETCH_CREATE_POWER');

// 跟新
export const fetchUpdatePower = createAction('FETCH_UPDATE_POWER');

// 资源树
export const fetchPowerTree = createAction('FETCH_POWER_TREE_SELECT');
