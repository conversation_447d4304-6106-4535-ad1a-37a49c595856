import { createAction } from '@reduxjs/toolkit';

export { equipmentActions } from './../reducers/equipmentSlice';

// 分页查询设备信息
export const fetchEquipmentListPage = createAction('EQUIPMENT_LIST_PAGE');

// 编辑
export const fetchEditEquipment = createAction('EDIT_EQUIPMENT');

// 导入
export const fetchuploadEquipment = createAction('IMPORT_EQUIPMENT');

// 设备分类
// export const threeEquipmentType = createAction('THREE_EQUIPMENT_TYPE');

// 跳转
export const importLocationHrefEquipmentList = createAction('IMPORT_EQUIPMENT_LIST_FROM_CREATE');

// 删除设备
export const deleteDevice = createAction('DELETE_DEVICE');

// 设备SN 搜索
export const searchDeviceSN = createAction('SEARCH_DEVICE_SN');

// 请求设备详情
export const getDeviceInfo = createAction('GET_DEVICE_INFO');

export const batchUpdateDevicesActionCreator = createAction('devices/BATCH_UPDATE_DEVICES');

//初始化列表页搜索条件及数据
export const initializeSearchValues = createAction('devices/INITIALIZE_SEARCH_VALUES');

export const setEquipmentPagination = createAction('devices/SET_PAGINATION');
