import { createAction } from '@reduxjs/toolkit';

export {
  CUSTOM_ALARM_CONFIGURATION_SLICE_NAME,
  customAlarmConfigurationActions,
} from '../reducers/customAlarmConfigurationSlice';

export const alarmTypeAction = createAction('FETCH_ALARM_TYPE');
export const alarmLargeClassAction = createAction('FETCH_ALARM_LARGE_CLASS');
export const alarmItemTypeAction = createAction('FETCH_ALARM_ITEM_TYPE');

// 模板列表分页
export const fetchTemplateList = createAction(
  `${'CUSTOM_ALARM_CONFIGURATION_SLICE_NAME'}/FETCH_TEMPLATE_LIST`
);
// 批量修改启用状态
export const changeAvailableStatus = createAction(
  'CUSTOM_ALARM_CONFIGURATION_SLICE_NAME/CHANGE_AVAILABLE_STATUS'
);
// 编辑模板
export const monitorGroupUpdate = createAction('MONITOR_GROUP_UPDATE');
// 新建模板
export const monitorGroupAdd = createAction('MONITOR_GROUP_ADD');

// 模板大小类映射关系
export const fetchTemplateTypeNormalizeList = createAction('FETCH_TEMPLATE_TYPE_NORMALIZE_LIST');
export const fetchAlarmConfigurationTree = createAction('FETCH_ALARM_CONFIGURATION_TREE');

// 将用户拖拽的告警文案配置字段暂存到 Store
export const insertNotificationTplFieldAction = createAction(
  'templateAlarmConfig/insertNotificationTplField'
);
// 更新 Store 中暂存的用户编辑的告警文案配置
export const updateNotificationTplAction = createAction('alarmConfig/updateNotificationTpl');
// 删除 Store 中暂存的用户编辑的告警文案配置的某项
export const deleteNotificationTplFieldAction = createAction(
  'alarmConfig/deleteNotificationTplField'
);
// 重置 Store 中暂存的告警文案配置
export const resetNotificationTplAction = createAction('alarmConfig/resetNotificationTpl');

// 提交新建模板配置
export const submitNewTemplateConfigAction = createAction('alarmConfig/submitNewTemplateConfig');

// 查询监控项
export const searchTemplatePointConfigAction = createAction(
  'templateAlarmConfig/searchTemplatePointConfig'
);

// 查看模板 解析文案
export const reviewAlarmNoticifyAction = createAction('templateAlarmConfig/reviewAlarmNoticify');

// 保存编辑模板的模板信息
export const saveEditTemplateModuleAction = createAction(
  'templateAlarmConfig/saveEditTemplateModule'
);

// 提交编辑模板配置
// export const submitEditTemplateConfigAction = createAction('alarmConfig/submitEditTemplateConfig');

// 粘贴模板
// export const pasteNoticifyAction = createAction('alarmConfig/pasteNoticify');

// 删除监控项
// export const deletePointConfigAction = createAction('alarmConfig/deletePointConfig');

// 删除模板
export const deleteTemplateAction = createAction(
  'CUSTOM_ALARM_CONFIGURATION_SLICE_NAME/deleteTemplate'
);
