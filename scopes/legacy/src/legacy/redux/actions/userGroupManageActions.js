import { createAction } from '@reduxjs/toolkit';

export { userGroupManageActions } from '../reducers/userGroupManageSlice';
export const fetchUserGroupList = createAction('FETCH_USER_GROUP_LIST');
export const fetchCreateUserGroup = createAction('FETCH_CREATE_USER_GROUP');
export const fetchDelete = createAction('FETCH_DELETE_USER_GROUP');
export const fetchUserGroupsAssociatedUser = createAction('FETCH_USER_GROUP_ASSOCIATED_USER');
export const fetchUserGroupsAssociatedRoles = createAction('FETCH_USER_GROUP_ASSOCIATED_ROLES');
export const fetchUserGroupManageDetail = createAction('FETCH_USER_GROUP_MANAGE_DETAIL');
export const fetchRemoveUser = createAction('FETCH_REMOVE_USER');
export const fetchRemoveRole = createAction('FETCH_REMOVE_ROLE');
export const fetchRemoveResource = createAction('FETCH_REMOVE_RESOURCE');
export const fetchUserGroupsAssociatedResource = createAction(
  'FETCH_USER_GROUP_ASSOCIATED_RESOURCE'
);
