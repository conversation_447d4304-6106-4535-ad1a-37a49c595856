import { createAction } from '@reduxjs/toolkit';

export { warrantyOrderActions } from '../reducers/warrantyOrderSlice';

export const fetchWarrantyOrderList = createAction('warranty-order/fetchOrderList');

export const revokeWarrantyOrder = createAction('warranty-order/revokeOrder');

export const rePostWarrantyOrder = createAction('warranty-order/rePostOrder');

export const fetchWarrantyDetail = createAction('warranty-order/fetchOrderDetail');

export const setWarrantyPagination = createAction('warranty-order/setPagination');
