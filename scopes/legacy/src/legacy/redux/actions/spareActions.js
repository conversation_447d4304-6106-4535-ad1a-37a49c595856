import { createAction } from '@reduxjs/toolkit';

export { spareActions } from './../reducers/spareSlice';

// 分页查询无sn耗材信息
export const spareListPage = createAction('SPARE_LIST_PAGE');

// 删除设备
export const deleteSpare = createAction('DELETE_SPARE');

// 导入
export const uploadSpare = createAction('IMPORT_SPARE');

//编辑
export const editSpare = createAction('EDIT_SPARE');

//初始化列表页搜索条件及数据
export const initializeSearchValues = createAction('spares/INITIALIZE_SEARCH_VALUES');

export const setPagination = createAction('spares/SET_PAGINATION');
