import { createAction } from '@reduxjs/toolkit';

export { eventCenterActions } from './../reducers/eventCenterSlice';

export const getEventList = createAction('GET_EVENT_LIST');
export const getProgressUpdateList = createAction('GET_PROGRESS_UODATE_LIST');
export const getEventDetail = createAction('GET_EVENT_DETAIL');
export const getEventLife = createAction('GET_EVENT_LIFE');
export const getMetadataByType = createAction('GET_METADATA_BY_TYPE');
