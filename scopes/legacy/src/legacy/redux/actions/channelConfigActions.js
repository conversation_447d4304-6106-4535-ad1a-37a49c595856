import { createAction } from '@reduxjs/toolkit';

import { CHANNEL_CONFIG_NAME, channelConfigActions } from '../reducers/channelConfigSlice';

export { channelConfigActions };

export const channelConfigActionCreator = createAction(CHANNEL_CONFIG_NAME + '/GET_CHANNEL_CONFIG');
export const resetSearchValuesActionCreator = createAction(
  CHANNEL_CONFIG_NAME + '/RESET_SEARCH_VALUES'
);
export const setPaginationThenGetDataActionCreator = createAction(
  CHANNEL_CONFIG_NAME + '/SET_PAGINATION_THEN_GET_DATA'
);
export const batchUpdateChannelStatusActionCreator = createAction(
  CHANNEL_CONFIG_NAME + '/BATCH_UPDATE_CHANNEL_STATUS'
);
export const createChannelConfigActionCreator = createAction(
  CHANNEL_CONFIG_NAME + '/CREATE_CHANNEL_CONFIG'
);
export const updateChannelConfigActionCreator = createAction(
  CHANNEL_CONFIG_NAME + '/UPDATE_CHANNEL_CONFIG'
);

export const channelDeviceActionCreator = createAction(CHANNEL_CONFIG_NAME + '/GET_CHANNEL_DEVICE');
export const resetDeviceSearchValuesActionCreator = createAction(
  CHANNEL_CONFIG_NAME + '/RESET_DEVICE_SEARCH_VALUES'
);
export const setDevicePaginationThenGetDataActionCreator = createAction(
  CHANNEL_CONFIG_NAME + '/SET_DEVICE_PAGINATION_THEN_GET_DATA'
);
