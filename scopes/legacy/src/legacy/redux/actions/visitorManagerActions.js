import { createAction } from '@reduxjs/toolkit';

import { VISITOR_RECORD_SLICE_NAME, visitorRecordActions } from '../reducers/visitorManagerSlice';

export { visitorRecordActions };

export const visitorRecordActionCreator = createAction(
  VISITOR_RECORD_SLICE_NAME + '/GET_VISITOR_RECORD'
);
export const resetSearchValuesActionCreator = createAction(
  VISITOR_RECORD_SLICE_NAME + 'VISITOR_RECORD_VALUES'
);
export const setPaginationThenGetDataActionCreator = createAction(
  VISITOR_RECORD_SLICE_NAME + 'SET_PAGINATION_THEN_GET_DATA'
);
