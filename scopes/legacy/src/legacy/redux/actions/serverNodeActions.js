import { createAction } from '@reduxjs/toolkit';

import { SERVER_NODE_NAME, serverNodeActions } from '../reducers/serverNodeSlice';

export { serverNodeActions };

export const serverNodeActionCreator = createAction(SERVER_NODE_NAME + '/GET_SERVER_NODE');
export const resetSearchValuesActionCreator = createAction(
  SERVER_NODE_NAME + 'RESET_SEARCH_VALUES'
);
export const setPaginationThenGetDataActionCreator = createAction(
  SERVER_NODE_NAME + 'SET_PAGINATION_THEN_GET_DATA'
);
