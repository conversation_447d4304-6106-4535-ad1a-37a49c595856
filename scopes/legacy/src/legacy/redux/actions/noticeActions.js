import { createAction } from '@reduxjs/toolkit';

export { noticeActions } from './../reducers/noticeSlice';

export const fetchValidNoticeListPage = createAction('notice/VALID_NOTICE_PAGE');

export const fetchPublishNoticeListPage = createAction('notice/PUBLISH_NOTICE_PAGE');

export const deleteNotice = createAction('notice/DELETE_NOTICE');

export const publishNotice = createAction('notice/PUBLISH_NOTICE');

export const revokeNotice = createAction('notice/REVOKE_NOTICE');

export const updateNotice = createAction('notice/UPDATE_NOTICE');

export const updatePublishNotice = createAction('notice/UPDATE_PUBLISH_NOTICE');

export const fetchNoticeTree = createAction('notice/FETCH_NOTICE_TREE_SELECT');

export const addNotice = createAction('notice/ADD_NOTICE');

export const fetchNoticeDetail = createAction('notice/FETCH_DETAIL');
