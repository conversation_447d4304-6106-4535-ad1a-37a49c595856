import { createAction } from '@reduxjs/toolkit';

export { alarmTransmissionRecordActions } from './../reducers/alarmTransmissionRecordSlice';

export const getListAction = createAction('alarmTransmissionRecord/getList');

export const setPaginationAction = createAction('alarmTransmissionRecord/setPagination');

export const resetValuesAction = createAction('alarmTransmissionRecord/resetValuesAction');
