import { createAction } from '@reduxjs/toolkit';

import { INSIDE_NOTICE_SLICE_NAME, insideNoticeActions } from '../reducers/insideNoticeSlice';

export { insideNoticeActions };

export const getInsideNoticeListActionCreator = createAction(
  INSIDE_NOTICE_SLICE_NAME + '/GET_INSIDE_NOTICE'
);

export const setAlreadyRead = createAction(INSIDE_NOTICE_SLICE_NAME + '/SET_READ');

export const setReadAll = createAction(INSIDE_NOTICE_SLICE_NAME + '/SET_ALL_READ');
