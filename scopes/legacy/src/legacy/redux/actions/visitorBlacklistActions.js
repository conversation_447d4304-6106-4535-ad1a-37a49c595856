import { createAction } from '@reduxjs/toolkit';

import {
  VISITOR_BLACKLIST_SLICE_NAME,
  visitorBlacklistActions,
} from '../reducers/visitorBlacklistSlice';

export { visitorBlacklistActions };

export const visitorBlacklistActionCreator = createAction(
  VISITOR_BLACKLIST_SLICE_NAME + '/GET_VISITOR_BLACKLIST'
);
export const resetSearchValuesActionCreator = createAction(
  VISITOR_BLACKLIST_SLICE_NAME + 'VISITOR_BLACKLIST_VALUES'
);
export const setPaginationThenGetDataActionCreator = createAction(
  VISITOR_BLACKLIST_SLICE_NAME + 'SET_PAGINATION_THEN_GET_DATA'
);

export const batchRemoveVisitorBlacklistActionCreator = createAction(
  VISITOR_BLACKLIST_SLICE_NAME + '/BATCH_REMOVE_VISITOR_BLACKLIST'
);
