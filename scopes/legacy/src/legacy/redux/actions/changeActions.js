import { createAction } from '@reduxjs/toolkit';

import { CHANGE_SLICE_NAME } from './../reducers/changeSlice';

export { changeActions } from './../reducers/changeSlice';

export const getChangeDataActionCreator = createAction(`${CHANGE_SLICE_NAME}/getData`);
export const resetSearchValuesActionCreator = createAction(
  `${CHANGE_SLICE_NAME}/resetSearchValues`
);
export const setPaginationThenGetDataActionCreator = createAction(
  `${CHANGE_SLICE_NAME}/setPaginationThenGetData`
);

// 变更列表
export const getTicketDataActionCreator = createAction(`${CHANGE_SLICE_NAME}/getTricketData`);
export const resetTicketSearchValuesActionCreator = createAction(
  `${CHANGE_SLICE_NAME}/resetTicketSearchValues`
);

export const setTicketPaginationThenGetDataActionCreator = createAction(
  `${CHANGE_SLICE_NAME}/setTicketPaginationThenGetData`
);

// 变更模板保存
export const setTemplateSaveActionCreator = createAction(`${CHANGE_SLICE_NAME}/setTemplateSave`);

// 变更模板提交
export const setTemplateSubmitActionCreator = createAction(
  `${CHANGE_SLICE_NAME}/setTemplateSubmitActionCreator`
);

// 变更模板详情
export const getTemplateDetailInfo = createAction(`${CHANGE_SLICE_NAME}/getTemplateDetailInfo`);

// 变更保存
export const setTicketSaveActionCreator = createAction(
  `${CHANGE_SLICE_NAME}/setTicketSaveActionCreator`
);

// 变更提交
export const setTicketSubmitActionCreator = createAction(
  `${CHANGE_SLICE_NAME}/setTicketSubmitActionCreator`
);

// 变更详情
export const getTicketDetailInfo = createAction(`${CHANGE_SLICE_NAME}/getTicketDetailInfo`);

// 变更详情告警看板
export const getTicketDetailAlarmList = createAction(
  `${CHANGE_SLICE_NAME}/getTicketDetailAlarmList`
);

//查询变更告警级别数量
export const getAlarmLevelCount = createAction(`${CHANGE_SLICE_NAME}/getAlarmLevelCount`);

//查询变更步骤看板详情
export const getTicketStepDetail = createAction(`${CHANGE_SLICE_NAME}/getTicketStepDetail`);

//查询变更步骤看板详情测点值
export const getRealtimeDataAction = createAction(`${CHANGE_SLICE_NAME}/getTicketRealtimeData`);

//取消请求变更步骤看板详情测点值
export const cancelTicketRealtimeData = createAction(
  `${CHANGE_SLICE_NAME}/cancelTicketRealtimeData`
);

//变更步骤开始
export const startOperationActionCreator = createAction(
  `${CHANGE_SLICE_NAME}/startOperationActionCreator`
);

//变更步骤检查
export const stepCheckedActionCreator = createAction(
  `${CHANGE_SLICE_NAME}/stepCheckedActionCreator`
);

//变更步骤强行跳过
export const stepSkipActionCreator = createAction(`${CHANGE_SLICE_NAME}/stepSkipActionCreator`);

//变更步骤强行跳过
export const stepStopActionCreator = createAction(`${CHANGE_SLICE_NAME}/stepStopActionCreator`);

// 检查项人工验证
export const checkItemArtificialValidationActionCreator = createAction(
  `${CHANGE_SLICE_NAME}/checkItemArtificialValidationActionCreator`
);

// 操作项人工验证
export const opArtificialValidationActionCreator = createAction(
  `${CHANGE_SLICE_NAME}/opArtificialValidationActionCreator`
);

// 工单撤回、审批
export const ticketApprovalOrRevertActionCreator = createAction(
  `${CHANGE_SLICE_NAME}/ticketApprovalOrRevertActionCreator`
);

// 工单复制查询工单信息
export const getTicketCopyInfoActionCreator = createAction(
  `${CHANGE_SLICE_NAME}/getTicketCopyInfoActionCreator`
);

// 审批
export const approvalActionCreator = createAction(`${CHANGE_SLICE_NAME}/approvalActionCreator`);

// 提交总结
export const summerySubmitActionCreator = createAction(
  `${CHANGE_SLICE_NAME}/summerySubmitActionCreator`
);

// 变更终止
export const stopChangeActionCreator = createAction(`${CHANGE_SLICE_NAME}/stopChangeActionCreator`);

// 撤回变更模板审批
export const templateApprovalOrRevertActionCreator = createAction(
  `${CHANGE_SLICE_NAME}/templateApprovalOrRevertActionCreator`
);

// 变更工单关闭
export const ticketCloseActionCreator = createAction(
  `${CHANGE_SLICE_NAME}/ticketCloseActionCreator`
);

// 变更模板删除
export const deleteTemplateActionCreator = createAction(
  `${CHANGE_SLICE_NAME}/deleteTemplateActionCreator`
);

// 变更模板修改详情
export const getTemplateEditDetailInfoActionCreator = createAction(
  `${CHANGE_SLICE_NAME}/getTemplateEditDetailInfoActionCreator`
);

// 模板复制
export const getTemplateCopyInfoActionCreator = createAction(
  `${CHANGE_SLICE_NAME}/getTemplateCopyInfoActionCreator`
);
