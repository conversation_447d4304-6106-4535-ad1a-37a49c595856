import { createAction } from '@reduxjs/toolkit';

export { doPointActions } from './../reducers/doPointSlice';
// 查询测点详情
export const fetchPointDetail = createAction('FETCH_POINT_DETAIL');

// 新增
export const addPointAction = createAction('ADD_POINT_ACTION');

// 删除
export const deletePointAction = createAction('DELETE_POINT_ACTION');

// 编辑
export const updatePointAction = createAction('UPDATE_POINT_ACTION');

// 获取设备分类三级结构
export const MetaCategoryAction = createAction('MRTA_CATEGORY_ACTION');

// 获取设备测点数量
export const MetaCategoryNumAction = createAction('MRTA_CATEGORY_NUM_ACTION');
