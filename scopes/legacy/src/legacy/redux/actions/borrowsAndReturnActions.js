import { createAction } from '@reduxjs/toolkit';

import { BORROW_AND_RETURN_SLICE_NAME } from './../reducers/borrowsAndReturnSlice';

export { borrowsAndReturnActions } from '../reducers/borrowsAndReturnSlice';

export const getDataActionCreator = createAction(`${BORROW_AND_RETURN_SLICE_NAME}/getData`);
export const setPaginationThenGetDataActionCreator = createAction(
  `${BORROW_AND_RETURN_SLICE_NAME}/setPaginationThenGetData`
);
export const resetSearchValuesActionCreator = createAction(
  `${BORROW_AND_RETURN_SLICE_NAME}/resetSearchValues`
);
export const lendActionCreator = createAction(`${BORROW_AND_RETURN_SLICE_NAME}/lending`);
export const renewActionCreator = createAction(`${BORROW_AND_RETURN_SLICE_NAME}/renewing`);
export const createBorrowActionCreator = createAction(
  `${BORROW_AND_RETURN_SLICE_NAME}/createBorrow`
);
export const getBorrowInfoActionCreator = createAction(
  `${BORROW_AND_RETURN_SLICE_NAME}/getBorrowInfo`
);
export const getBorrowApplyActionCreator = createAction(
  `${BORROW_AND_RETURN_SLICE_NAME}/getBorrowApply`
);
export const queryBorrowAndReturnAssertInfoActionCreator = createAction(
  `${BORROW_AND_RETURN_SLICE_NAME}/getBorrowAndReturnAssertInfo`
);
export const getReturnRecordsActionCreator = createAction(
  `${BORROW_AND_RETURN_SLICE_NAME}/getReturnRecord`
);
export const getRenewLendRecordsActionCreator = createAction(
  `${BORROW_AND_RETURN_SLICE_NAME}/getRenewLendRecords`
);
export const getTicketRecordsActionCreator = createAction(
  `${BORROW_AND_RETURN_SLICE_NAME}/getTicketRecords`
);
export const revertBorrowActionCreator = createAction(`${BORROW_AND_RETURN_SLICE_NAME}/revert`);
export const cancelBorrowActionCreator = createAction(
  `${BORROW_AND_RETURN_SLICE_NAME}/cancelBorrow`
);
export const borrowAllActionCreator = createAction(`${BORROW_AND_RETURN_SLICE_NAME}/borrowAll`);
export const borrowReSubmitActionCreator = createAction(
  `${BORROW_AND_RETURN_SLICE_NAME}/BorrowReSubmit`
);
export const returningActionCreator = createAction(`${BORROW_AND_RETURN_SLICE_NAME}/returning`);
export const renewRevertingActionCreator = createAction(
  `${BORROW_AND_RETURN_SLICE_NAME}/renewReverting`
);
