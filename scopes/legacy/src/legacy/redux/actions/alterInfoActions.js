import { createAction } from '@reduxjs/toolkit';

import { ALTER_INFO_SLICE_NAME, alterInfoActions } from '../reducers/alterInfoSlice';

export { alterInfoActions };

export const getDataActionCreator = createAction(ALTER_INFO_SLICE_NAME + '/getData');

export const getAlterBaseInfoActionCreator = createAction(
  ALTER_INFO_SLICE_NAME + '/getAlterBaseInfo'
);

export const setPaginationThenGetDataActionCreator = createAction(
  ALTER_INFO_SLICE_NAME + '/setPaginationThenGetData'
);

export const cancelAlterInfoActionCreator = createAction(
  ALTER_INFO_SLICE_NAME + '/cancelAlterInfo'
);

export const detailCancelAlterInfoActionCreator = createAction(
  ALTER_INFO_SLICE_NAME + '/detailCancelAlterInfo'
);

export const resetSearchValuesActionCreator = createAction(
  ALTER_INFO_SLICE_NAME + '/resetSearchValues'
);

export const submitLeaveActionCreator = createAction(ALTER_INFO_SLICE_NAME + '/submitLeave');

export const submitExchangeActionCreator = createAction(ALTER_INFO_SLICE_NAME + '/submitExchange');

export const alterBasicInfoActionCreator = createAction(ALTER_INFO_SLICE_NAME + '/alterBasicInfo');

export const alterFilesActionCreator = createAction(ALTER_INFO_SLICE_NAME + '/alterFiles');

export const redirectDetailActionCreator = createAction(ALTER_INFO_SLICE_NAME + '/redirect');
