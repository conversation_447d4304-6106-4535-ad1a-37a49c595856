import { createAction } from '@reduxjs/toolkit';

import { WORKDAY_SLICE_NAME, workdayManageActions } from '../reducers/workdayManageSlice';

export { workdayManageActions };

export const getWorkdayListActionCreator = createAction(WORKDAY_SLICE_NAME + '/GET_WORKDAY_LIST');

export const addWorkDayAction = createAction(WORKDAY_SLICE_NAME + '/ADD_WORKDAY');

export const editWorkDayAction = createAction(WORKDAY_SLICE_NAME + '/EDIT_WORKDAY');
