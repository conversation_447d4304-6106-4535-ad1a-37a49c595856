import { createAction } from '@reduxjs/toolkit';

import { TASK_CENTER_SLICE_NAME } from './../reducers/taskCenterSlice';

export { taskCenterActions } from './../reducers/taskCenterSlice';

export const getDataActionCreator = createAction(`${TASK_CENTER_SLICE_NAME}/getData`);
export const setPaginationThenGetDataActionCreator = createAction(
  `${TASK_CENTER_SLICE_NAME}/setPaginationThenGetData`
);
export const resetSearchValuesActionCreator = createAction(
  `${TASK_CENTER_SLICE_NAME}/resetSearchValues`
);
export const batchUpdateTaskCentersActionCreator = createAction(
  `${TASK_CENTER_SLICE_NAME}/batchUpdateTaskCenters`
);
export const deleteTaskCenterActionCreator = createAction(
  `${TASK_CENTER_SLICE_NAME}/deleteTaskCenter`
);
export const getTaskBaseInfoActionCreator = createAction(
  `${TASK_CENTER_SLICE_NAME}/getTaskBaseInfo`
);
export const createTaskActionCreator = createAction(`${TASK_CENTER_SLICE_NAME}/createTask`);
export const editTaskActionCreator = createAction(`${TASK_CENTER_SLICE_NAME}/editTask`);
export const copyTaskActionCreator = createAction(`${TASK_CENTER_SLICE_NAME}/copyTask`);
export const getTaskscheduleInYearActionCreator = createAction(
  `${TASK_CENTER_SLICE_NAME}/scheduleInYear`
);
export const getTaskscheduleInMonthActionCreator = createAction(
  `${TASK_CENTER_SLICE_NAME}/scheduleInMonth`
);

/** 用年纬度拓展月行事历，旧接口无法用deviceType拓展，保证稳定旧接口相关业务不动 */
export const getTaskscheduleInMonthByYearApiActionCreator = createAction(
  `${TASK_CENTER_SLICE_NAME}/scheduleInMonthByYearApi`
);

export const resetScheduleYearSearchValuesActionCreator = createAction(
  `${TASK_CENTER_SLICE_NAME}/resetScheduleYearSearchValues`
);

export const resetScheduleMonthSearchValuesActionCreator = createAction(
  `${TASK_CENTER_SLICE_NAME}/resetScheduleMonthSearchValues`
);
export const resetScheduleMonthSearchValuesByYearApiActionCreator = createAction(
  `${TASK_CENTER_SLICE_NAME}/resetScheduleMonthSearchValuesByYearApi`
);

export const setScheduleInYearPaginationThenGetDataActionCreator = createAction(
  `${TASK_CENTER_SLICE_NAME}/setScheduleInYearPaginationThenGetData`
);
