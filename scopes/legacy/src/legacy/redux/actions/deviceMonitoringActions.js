import { createAction } from '@reduxjs/toolkit';

export { deviceMonitoringActions } from './../reducers/deviceMonitoringSlice';

export const getDeviceActionCreator = createAction('deviceMonitoring/GET_DEVICE');
export const getDeviceAlarmInfoActionCreator = createAction('deviceMonitoring/getDeviceAlarmInfo');
export const getRawMonitoringDataActionCreator = createAction(
  'deviceMonitoring/getRawMonitoringData'
);
export const getTopologyActionCreator = createAction('deviceMonitoring/getTopology');
export const getBlockRealtimeDataActionCreator = createAction(
  'deviceMonitoring/getBlockRealtimeData'
);
export const cancelGetMonitorDataActionCreator = createAction(
  'deviceMonitoring/CANCEL_GET_MONITOR_DATA'
);
export const cancelGetDeviceAlarmsActionCreator = createAction(
  'deviceMonitoring/CANCEL_GET_DEVICE_ALARMS'
);
export const getDeviceChangeTicketsActionCreator = createAction(
  'deviceMonitoring/GET_DEVICE_CHANGE_TICKETS'
);
// export const cancelGetDeviceChangeTicketsActionCreator = createAction(
//   'deviceMonitoring/CANCEL_GET_DEVICE_CHANGE_TICKETS'
// );
export const getDeviceEventTicketsActionCreator = createAction(
  'deviceMonitoring/GET_DEVICE_EVENT_TICKETS'
);
