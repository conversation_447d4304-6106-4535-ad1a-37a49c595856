import { createAction } from '@reduxjs/toolkit';

import { NORTH_USER_NAME, northUserActions } from '../reducers/northUserSlice';

export { northUserActions };

export const northUserActionCreator = createAction(NORTH_USER_NAME + '/GET_NORTH_USER');
export const resetSearchValuesActionCreator = createAction(
  NORTH_USER_NAME + '/RESET_SEARCH_VALUES'
);
export const setPaginationThenGetDataActionCreator = createAction(
  NORTH_USER_NAME + '/SET_PAGINATION_THEN_GET_DATA'
);
export const batchUpdateNorthUserStatusActionCreator = createAction(
  NORTH_USER_NAME + '/BATCH_UPDATE_USER_STATUS'
);
export const createNorthUserActionCreator = createAction(NORTH_USER_NAME + '/CREATE_NORTH_USER');
export const updateNorthUserActionCreator = createAction(NORTH_USER_NAME + '/UPDATE_NORTH_USER');
