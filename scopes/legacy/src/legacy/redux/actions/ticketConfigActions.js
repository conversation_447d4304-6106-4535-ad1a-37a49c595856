import { createAction } from '@reduxjs/toolkit';

import { TICKET_CONFIG_SLICE_NAME } from './../reducers/ticketConfigSlice';

export { ticketConfigActions } from './../reducers/ticketConfigSlice';

const maintain = 'maintain';

export const getMaintainDataActionCreator = createAction(
  `${TICKET_CONFIG_SLICE_NAME}/${maintain}/getData`
);
export const setMaintainPaginationThenGetDataActionCreator = createAction(
  `${TICKET_CONFIG_SLICE_NAME}/${maintain}/setPaginationThenGetData`
);
export const resetMaintainSearchValuesActionCreator = createAction(
  `${TICKET_CONFIG_SLICE_NAME}/${maintain}/resetSearchValues`
);
export const deleteMaintainConfigActionCreator = createAction(
  `${TICKET_CONFIG_SLICE_NAME}/${maintain}/deleteConfig`
);
export const createMaintainConfigActionCreator = createAction(
  `${TICKET_CONFIG_SLICE_NAME}/${maintain}/createConfig`
);
export const editMaintainConfigActionCreator = createAction(
  `${TICKET_CONFIG_SLICE_NAME}/${maintain}/editConfig`
);
export const getMaintainConfigInfoActionCreator = createAction(
  `${TICKET_CONFIG_SLICE_NAME}/${maintain}/getConfigInfo`
);
