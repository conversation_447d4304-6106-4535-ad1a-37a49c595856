import { createAction } from '@reduxjs/toolkit';

import { MINE_TICKET_SLICE_NAME, mineTicketActions } from './../reducers/mineTicketSlice';

export { mineTicketActions };

export const getTicketListActionCreator = createAction(MINE_TICKET_SLICE_NAME + '/GET_TICKETS');

export const takeOverAction = createAction(MINE_TICKET_SLICE_NAME + '/TAKE_OVER');

export const endAction = createAction(MINE_TICKET_SLICE_NAME + '/TASK_END');
