import { createAction } from '@reduxjs/toolkit';

export { versionManageActions } from '../reducers/versionManageSlice';
//配置信息
export const fetchVersionManagePage = createAction('version-manage/VERSION_MANAGE_PAGE');
//创建版本
export const createVersionBtn = createAction('version-manage/CREATE_VERSION');
//版本列表
export const versionListBtn = createAction('version-manage/VERSION_LIST');
//删除版本
export const deleteVersionBtn = createAction('version-manage/DELETE_VERSION');
//发布版本
export const deployVersionBtn = createAction('version-manage/DEPLOY_VERSION');
//发布记录
export const deployListBtn = createAction('version-manage/DEPLOY_LIST');
