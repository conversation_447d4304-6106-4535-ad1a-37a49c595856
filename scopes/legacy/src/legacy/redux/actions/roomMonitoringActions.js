import { createAction } from '@reduxjs/toolkit';

import {
  ROOM_MONITORING_SLICE_NAME,
  roomMonitoringActions,
} from './../reducers/roomMonitoringSlice';

export { roomMonitoringActions };

export const getRoomInfoActionCreator = createAction(ROOM_MONITORING_SLICE_NAME + '/GET_ROOM_INFO');
export const getMonitoringDataActionCreator = createAction('GET_ROOM_MONITORING_DATA');
export const cancelGetMonitoringDataActionCreator = createAction(
  'monitoring_room/cancelGetMonitoringData'
);
export const getAlarmInfoActionCreator = createAction('GET_ALARM_INFO');
export const getInfrastructureDataActionCreator = createAction('GET_INFRASTRUCTURE_DATA');
export const getRoomCustomersActionCreator = createAction('GET_ROOM_CUSTOMERS');
export const getParentDevicesActionCreator = createAction('monitoring_room/getParentDevices');
export const getParentDevicesMonitoringDataActionCreator = createAction(
  'monitoring_room/getParentDevicesMonitoringData'
);
export const getChildBatteryUnitsActionCreator = createAction(
  'monitoring_room/GET_CHILD_BATTERY_UNITS'
);
export const cancelGetBatteryUnitsDataActionCreator = createAction(
  'monitoring_room/CANCEL_GET_BATTERY_UNITS_DATA'
);
export const cancelGetAlarmsActionCreator = createAction('monitoring_room/CANCEL_GET_ALARMS');
export const getRelatedDevicesActionCreator = createAction('monitoring_room/GET_RELATED_DEVICES');
export const cancelGetUpsOrHvdcRealtimeDataActionCreator = createAction(
  'monitoring_room/CANCEL_GET_UPS_OR_HVDC_REALTIME_DATA'
);
