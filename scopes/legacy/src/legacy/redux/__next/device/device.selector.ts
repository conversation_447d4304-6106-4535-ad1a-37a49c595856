import { deviceSlice } from './device.slice';
import type { DeviceSliceState } from './device.slice';

type StoreState = {
  [deviceSlice.name]: DeviceSliceState;
};

export const selectDevicesCache = (storeState: StoreState) => storeState[deviceSlice.name].cache;

export const selectDeviceCache = (deviceGuid: string) => (storeState: StoreState) =>
  storeState[deviceSlice.name].cache[deviceGuid];

export const selectChildDevices = (deviceGuid: string) => (storeState: StoreState) =>
  storeState[deviceSlice.name].relativeChildGuidsMappings[deviceGuid];
