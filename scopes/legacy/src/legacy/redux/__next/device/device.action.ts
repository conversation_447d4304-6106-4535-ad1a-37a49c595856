import { createAction } from '@reduxjs/toolkit';
import type { ActionCreatorWithPayload } from '@reduxjs/toolkit';

import { deviceSlice } from './device.slice';

const prefix = deviceSlice.name;

export const deviceSliceActions = deviceSlice.actions;

export const getChildrenActionCreator = createAction<{
  spaceGuid: string;
  deviceGuid: string;
  errorHandlerAction?: ActionCreatorWithPayload<any>;
}>(`${prefix}/get-child-devices`);
