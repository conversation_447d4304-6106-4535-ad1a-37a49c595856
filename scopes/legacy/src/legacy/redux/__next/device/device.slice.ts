import { createSlice } from '@reduxjs/toolkit';
import type { CaseReducer, PayloadAction } from '@reduxjs/toolkit';

// TODO @Jerry: use `Device model type`
type Device = {
  guid: string;
  [x: string]: any;
};

export type DeviceSliceState = {
  /**
   * An object which it's `key` is a `device GUID`,
   * and the `value` is a `device object`.
   */
  cache: Record<string, Device | undefined>;

  /**
   * An object which it's `key` is a `parent device GUID`,
   * and the `value` is a list of the parent's `children GUIDs`.
   */
  relativeChildGuidsMappings: Record<string, string[] | undefined>;
};

type CustomersSliceCaseReducers = {
  cacheInsert: CaseReducer<DeviceSliceState, PayloadAction<Device[]>>;
  cacheUpdate: CaseReducer<DeviceSliceState, PayloadAction<Device[]>>;
  cacheDelete: CaseReducer<DeviceSliceState, PayloadAction<string[]>>;
  relativeChildrenInsert: CaseReducer<
    DeviceSliceState,
    PayloadAction<{ parentGuid: string; children: Device[] }>
  >;
};

export const deviceSlice = createSlice<DeviceSliceState, CustomersSliceCaseReducers, 'device'>({
  name: 'device',
  initialState: {
    cache: {},
    relativeChildGuidsMappings: {},
  },
  reducers: {
    /**
     * Insert a list of devices.
     */
    cacheInsert(state, { payload: devices }) {
      devices.forEach(device => {
        state.cache[device.guid] = device;
      });
    },

    cacheUpdate(state, { payload: devices }) {
      throw new Error('Use `cacheInsert` for now.');
    },

    /**
     * Delete a list of devices by `device GUIDs`.
     */
    cacheDelete(state, { payload: deviceGuids }) {
      deviceGuids.forEach(deviceGuid => {
        delete state.cache[deviceGuid];
      });
    },

    /**
     * Insert a list of child devices of a specific parent device.
     */
    relativeChildrenInsert(state, { payload: { parentGuid, children } }) {
      const childGuids: string[] = [];
      children.forEach(device => {
        childGuids.push(device.guid);
        state.cache[device.guid] = device;
      });
      state.relativeChildGuidsMappings[parentGuid] = childGuids;
    },
  },
});
