import { call, fork, put, takeEvery } from 'redux-saga/effects';

import { commonErrorHandlerActionCreator } from '@manyun/dc-brain.legacy.redux/actions/handlerActions';
import * as infrastructureService from '@manyun/dc-brain.legacy.services/infrastructureService';

import { deviceSliceActions, getChildrenActionCreator } from './device.action';

function* getChildren({
  payload: { spaceGuid, deviceGuid, errorHandlerAction = commonErrorHandlerActionCreator as any },
}: ReturnType<typeof getChildrenActionCreator>) {
  const { response, error } = yield call(infrastructureService.fetchChildDevicesByGuid, {
    spaceGuid,
    parentGuid: deviceGuid,
  });
  if (error) {
    yield put(errorHandlerAction({ payload: { message: error } }));
  } else {
    yield put(
      deviceSliceActions.relativeChildrenInsert({
        parentGuid: deviceGuid,
        children: response.data,
      })
    );
  }
}

function* watchGetChildren() {
  yield takeEvery<any>(getChildrenActionCreator.type, getChildren);
}

export default [fork(watchGetChildren)];
