import { FiltersForm, Form, Select } from '@galiojs/awesome-antd';
import omit from 'lodash/omit';
import omitBy from 'lodash/omitBy';
import trim from 'lodash/trim';
import React, { Component } from 'react';
import { connect, useDispatch } from 'react-redux';
import { Link } from 'react-router-dom';

// Deprecated, replace with "useAuthorized" hook
// import { Authorize } from '@manyun/base-ui.ui.authorize';
import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { UserCompanyConnector } from '@manyun/auth-hub.ui.user-company-connector';
import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Input } from '@manyun/base-ui.ui.input';
import { VendorSelect } from '@manyun/crm.ui.vendor-select';

import { GutterWrapper, TinyCard, TinyTable, UserSelect } from '@manyun/dc-brain.legacy.components';
import { LEVEL_OPTIONS } from '@manyun/dc-brain.legacy.pages/vendor-manage/constants';
import {
  deleteVendorActionCreator,
  fetchVendorListPage,
  vendorActions,
} from '@manyun/dc-brain.legacy.redux/actions/vendorActions';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';

import CreateOrEdit from '../components/create-or-edit';

const CheckUserIdCreateOrEdit = ({ record }) => {
  const [authorized] = useAuthorized({ checkByCode: 'ele_vendor_update' });

  return (
    <>{authorized && <CreateOrEdit text="编辑" title="编辑厂商" type="link" info={record} />}</>
  );
};

const CheckUserIdCreateOrEdit2 = () => {
  const [authorized] = useAuthorized({ checkByCode: 'ele_vendor_add' });

  return authorized && <CreateOrEdit text="新建" title="新建厂商" type="primary" />;
};
const getColumns = ctx => [
  {
    title: '全称',
    dataIndex: 'vendorName',
    render: (text, record) => (
      <Link
        to={urls.generateVendorDetailLocation({
          id: record.id,
          vendorCode: record.vendorCode,
        })}
      >
        {text}
      </Link>
    ),
  },
  {
    title: '简称',
    dataIndex: 'vendorCode',
  },
  {
    title: '级别',
    dataIndex: 'vendorLevel',
  },
  {
    title: '主联系人名称',
    dataIndex: 'contactName',
  },
  {
    title: '主联系人电话',
    dataIndex: 'contactMobile',
  },
  {
    title: '主联系人邮箱',
    dataIndex: 'contactEmail',
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
  },
  {
    title: '更新时间',
    dataIndex: 'gmtModified',
  },
  {
    title: '更新人',
    dataIndex: 'operatorName',
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    render: (__, record) => (
      <>
        {/* <Authorize code="ele_vendor_update">
          {(authorized) =>
            authorized && <CreateOrEdit text="编辑" title="编辑厂商" type="link" info={record} />
          }
        </Authorize> */}
        <CheckUserIdCreateOrEdit record={record} />
        <Divider type="vertical" />
        <UserCompanyConnector userType="VENDOR" company={record.vendorCode} />
        <Divider type="vertical" />
        <DeleteConfirm
          maxReasonLength={50}
          targetName={record.vendorName}
          reasonPlaceholder={'请输入删除' + record.vendorName + '的原因'}
          onOk={({ reason }) =>
            ctx.props.deleteVendor({
              deleteparams: { operatorNotes: reason, id: record.id },
              searchParams: ctx.props.vendorPageCondition,
            })
          }
        >
          <Button type="link" compact>
            删除
          </Button>
        </DeleteConfirm>
      </>
    ),
  },
];

class VendorListLegacy extends Component {
  state = {
    pageNo: 1,
    pageSize: 10,
  };

  componentDidMount() {
    const { vendorPageCondition } = this.props;
    this.props.fetchVendorListPage({
      ...vendorPageCondition,
    });
  }

  onChangePage = (pageNo, pageSize) => {
    const { vendorPageCondition } = this.props;
    this.props.fetchVendorListPage({
      ...vendorPageCondition,
      pageNum: pageNo,
      pageSize,
    });
  };

  getFormData = fieldsValue => {
    fieldsValue.operatorId = fieldsValue.operatorId?.key;
    const params = omit(fieldsValue, 'createTime', 'contactName', 'contactMobile');
    params.createTimeEnd = fieldsValue.createTime?.[1]?.valueOf();
    params.createTimeStart = fieldsValue.createTime?.[0]?.valueOf();
    params.contactName = trim(fieldsValue.contactName);
    params.contactMobile = trim(fieldsValue.contactMobile);
    const p = omitBy(params, (value, key) => {
      if (value === null || value === undefined || value === '') {
        return key;
      }
    });
    return p;
  };

  searchVendorList = fieldsValue => {
    const params = this.getFormData(fieldsValue);
    this.props.fetchVendorListPage({
      ...params,
      pageNum: 1,
      pageSize: 10,
    });
  };

  handleReset = () => {
    this.props.dispatch(vendorActions.resetSearchValues());
    this.props.fetchVendorListPage({
      pageNum: 1,
      pageSize: 10,
    });
  };
  render() {
    const { form, vendorPage, loading, vendorPageCondition, fields, updateSearchValues } =
      this.props;

    return (
      <GutterWrapper mode="vertical">
        <TinyCard>
          <FiltersForm
            form={form}
            fields={Object.keys(fields).map(name => {
              const field = fields[name];
              return {
                ...field,
                name: name.split('.'),
              };
            })}
            items={[
              {
                label: '简称',
                name: 'vendorCode',
                control: <VendorSelect allowClear />,
              },
              {
                label: '级别',
                name: 'vendorLevel',
                control: (
                  <Select allowClear>
                    {LEVEL_OPTIONS.map(item => (
                      <Select.Option key={item.value} value={item.value}>
                        {item.label}
                      </Select.Option>
                    ))}
                  </Select>
                ),
              },
              {
                label: '主联系人名称',
                name: 'contactName',
                control: <Input allowClear />,
              },
              {
                label: '主联系人电话',
                name: 'contactMobile',
                control: <Input allowClear />,
              },
              {
                label: '更新人',
                name: 'operatorId',
                control: <UserSelect allowClear />,
              },
              {
                label: '创建时间',
                name: 'createTime',
                span: 2,
                control: (
                  <DatePicker.RangePicker
                    allowClear
                    showTime
                    format="YYYY-MM-DD HH:mm:ss"
                    placeholder={['开始时间', '结束时间']}
                  />
                ),
              },
            ]}
            onFieldsChange={changedFields => {
              updateSearchValues(
                changedFields.reduce((mapper, field) => {
                  const name = field.name.join('.');
                  mapper[name] = {
                    ...field,
                    name,
                  };
                  return mapper;
                }, {})
              );
            }}
            onSearch={this.searchVendorList}
            onReset={this.handleReset}
          />
        </TinyCard>
        <TinyCard>
          <TinyTable
            rowKey="id"
            columns={getColumns(this)}
            dataSource={vendorPage.list}
            loading={loading}
            scroll={{ x: 'max-content' }}
            actions={<CheckUserIdCreateOrEdit2 />}
            pagination={{
              total: vendorPage.total,
              current: vendorPageCondition.pageNum,
              pageSize: vendorPageCondition.pageSize,
              onChange: this.onChangePage,
            }}
          />
        </TinyCard>
      </GutterWrapper>
    );
  }
}

function VendorList(props) {
  const [form] = Form.useForm();
  const dispatch = useDispatch();

  return <VendorListLegacy form={form} dispatch={dispatch} {...props} />;
}

const mapStateToProps = ({
  vendorManage: { loading, vendorPage, searchValues, vendorPageCondition },
}) => ({
  loading,
  vendorPage,
  vendorPageCondition,
  fields: searchValues,
});
const mapDispatchToProps = {
  vendorActions,
  fetchVendorListPage,
  updateSearchValues: vendorActions.updateSearchValues,
  deleteVendor: deleteVendorActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(VendorList);
