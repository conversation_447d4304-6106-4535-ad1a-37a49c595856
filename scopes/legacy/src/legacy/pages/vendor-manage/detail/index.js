import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import { Space } from '@manyun/base-ui.ui.space';

import { LayoutContent } from '@manyun/dc-brain.ui.layout';

import {
  G<PERSON>Wrapper,
  TinyCard,
  TinyDescriptions,
  TinyTable,
} from '@manyun/dc-brain.legacy.components';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  fetchModelListPage,
  modelActions,
  setModelPagination,
} from '@manyun/dc-brain.legacy.redux/actions/modelActions';
import { fetchVendorDetail } from '@manyun/dc-brain.legacy.redux/actions/vendorActions';
import { getLocationSearchMap } from '@manyun/dc-brain.legacy.utils';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';

const getColumns = ctx => [
  {
    title: '型号名称',
    dataIndex: 'modelCode',
  },
  {
    title: '三级分类',
    dataIndex: 'deviceType',
    render: text => ctx.props.normalizedList?.[text]?.metaName,
  },
  {
    title: '对应规格数',
    dataIndex: 'specNum',
    render: text => (typeof text === 'number' ? text : 0),
  },
  {
    title: '对应设备数',
    dataIndex: 'deviceNum',
    render: (text, record) => {
      const params = {
        deviceType: record.deviceType,
        productModel: record.modelCode,
        vendor: record.vendorCode,
      };
      return typeof text === 'number' ? (
        <Link
          to={
            record.isNumbered
              ? urls.generateDeviceListUrl(params)
              : urls.generateSpareListUrl(params)
          }
        >
          {text}
        </Link>
      ) : (
        0
      );
    },
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
  },
  {
    title: '更新时间',
    dataIndex: 'gmtModified',
  },
  {
    title: '更新人',
    dataIndex: 'operatorName',
  },
];

class VendorDetail extends Component {
  componentDidMount() {
    const { match } = this.props;
    const { vendorCode } = this._getUrlSearchParams();
    this.props.syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });
    this.props.fetchVendorDetail({ params: { id: match.params.id } });
    this.props.initializeSearchValuesAndConditions({
      searchValues: { vendorCode: { name: 'vendorCode', value: vendorCode } },
    });
    this.props.fetchModelListPage();
  }

  componentWillUnmount() {
    this.props.initializeSearchValuesAndConditions({ searchValues: {} });
  }

  _getUrlSearchParams = () => {
    const { search } = this.props.history.location;
    return getLocationSearchMap(search, ['vendorCode']);
  };

  render() {
    const { vendorDetail, modelPage, pagination, loading, setModelPagination } = this.props;

    return (
      <LayoutContent
        pageCode="page_vendor"
        composeBreadcrumbs={value => [
          ...value,
          { key: 'page_vendor-key', text: vendorDetail.vendorCode },
        ]}
      >
        <GutterWrapper mode="vertical">
          <TinyCard title="基本信息">
            <TinyDescriptions
              column={4}
              descriptionsItems={[
                { label: '厂商全称', value: vendorDetail.vendorName },
                { label: '厂商简称', value: vendorDetail.vendorCode },
                { label: '厂商级别', value: vendorDetail.vendorLevel },
                { label: '备注', value: vendorDetail.description },
              ]}
            />
          </TinyCard>
          <TinyCard title="联系人信息">
            <Space direction="vertical">
              <TinyDescriptions
                column={4}
                descriptionsItems={[
                  { label: '主联系人名称', value: vendorDetail.contactName },
                  { label: '主联系人电话', value: vendorDetail.contactMobile },
                  { label: '主联系人邮箱', value: vendorDetail.contactEmail },
                ]}
              />
              <TinyTable
                rowKey="id"
                align="left"
                columns={[
                  {
                    title: '分区联系人姓名',
                    dataIndex: 'name',
                  },
                  {
                    title: '分区联系人电话',
                    dataIndex: 'mobile',
                  },
                  {
                    title: '分区联系人邮箱',
                    dataIndex: 'email',
                  },
                  {
                    title: '对接区域',
                    dataIndex: 'area',
                    render: area => {
                      if (!area) {
                        return area;
                      }
                      return area.split('_$$_').join('，');
                    },
                  },
                ]}
                dataSource={vendorDetail.vendorContacts}
                loading={loading}
                pagination={false}
              />
            </Space>
          </TinyCard>
          <TinyCard title="型号信息">
            <TinyTable
              rowKey="id"
              align="left"
              columns={getColumns(this)}
              dataSource={modelPage.list}
              loading={loading}
              pagination={{
                total: modelPage.total,
                current: pagination.pageNum,
                pageSize: pagination.pageSize,
                onChange: (current, size) =>
                  setModelPagination({ pageNum: current, pageSize: size }),
              }}
            />
          </TinyCard>
        </GutterWrapper>
      </LayoutContent>
    );
  }
}

const mapStateToProps = ({
  common: { deviceCategory },
  vendorManage: { vendorDetail },
  modelManage: { modelPage, pagination, loading },
}) => ({
  vendorDetail,
  modelPage,
  pagination,
  loading,
  normalizedList: deviceCategory?.normalizedList,
});
const mapDispatchToProps = {
  fetchVendorDetail,
  fetchModelListPage,
  syncCommonData: syncCommonDataActionCreator,
  setModelPagination,
  initializeSearchValuesAndConditions: modelActions.initializeSearchValuesAndConditions,
};

export default connect(mapStateToProps, mapDispatchToProps)(VendorDetail);
