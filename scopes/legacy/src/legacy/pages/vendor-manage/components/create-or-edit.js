import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import shortid from 'shortid';

import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Typography } from '@manyun/base-ui.ui.typography';

import { TinyDrawer } from '@manyun/dc-brain.legacy.components';
import { StyledFormItem } from '@manyun/dc-brain.legacy.pages/vendor-manage/styled';
import {
  fetchVendorDetail,
  fetchVendorListPage,
  vendorActions,
} from '@manyun/dc-brain.legacy.redux/actions/vendorActions';
import * as vendorService from '@manyun/dc-brain.legacy.services/vendorService';

import PrimaryContact from './components/primary-contact';
import RegionalContactList from './components/regional-contact-list';
import VendorInfo from './components/vendor-info';

const initId = shortid();

class CreateOrEdit extends Component {
  state = {
    loading: false,
    regionalIds: [initId],
  };

  componentDidUpdate(prevProps, prevState) {
    if (!prevState.createVisible && this.state.createVisible && this.props.info) {
      this.props.fetchVendorDetail({ params: { id: this.props.info.id } });
    }
    if (prevProps.vendorDetail.id !== this.props.vendorDetail.id) {
      const vendorContacts = this.props.vendorDetail.vendorContacts;
      if (Array.isArray(vendorContacts) && vendorContacts.length) {
        const newIds = vendorContacts.map(({ id }) => id);
        this.setState({ regionalIds: newIds });
      }
    }
  }

  changeVisible = async () => {
    this.setState({
      createVisible: !this.state.createVisible,
      regionalIds: [initId],
    });
    this.props.saveVendorDetailSuccess({});
  };

  handleSubmit = () => {
    const { info, form } = this.props;
    form.validateFields(async (err, values) => {
      if (err) {
        return;
      }
      const { regionalIds } = this.state;
      const vendorContacts = regionalIds.map(id => {
        const areas = values[`${id}_$$_area`];

        return {
          name: trimStr(values[`${id}_$$_name`]),
          mobile: trimStr(values[`${id}_$$_mobile`]),
          email: trimStr(values[`${id}_$$_email`]),
          area: Array.isArray(areas) && areas.length > 0 ? areas.join('_$$_') : null,
        };
      });
      const params = {
        vendorCode: trimStr(values.vendorCode),
        vendorName: trimStr(values.vendorName),
        vendorLevel: values.vendorLevel,
        contactName: trimStr(values.contactName),
        contactMobile: trimStr(values.contactMobile),
        contactEmail: trimStr(values.contactEmail),
        vendorContacts,
        description: values.description,
      };
      this.setState({ loading: true });
      if (info) {
        params.id = info.id;
        const { error } = await vendorService.editVendor(params);
        this.setState({ loading: false });
        if (error) {
          message.error(error);
          return;
        }
        message.success('编辑厂商成功');
        this.changeVisible();
        this.props.fetchVendorListPage({
          ...this.props.searchParams,
          pageNum: 1,
          pageSize: 10,
        });
        return;
      }
      const { error } = await vendorService.addVendor(params);
      this.setState({ loading: false });
      if (error) {
        message.error(error);
        return;
      }
      this.changeVisible();
      this.props.fetchVendorListPage({
        ...this.props.searchParams,
        pageNum: 1,
        pageSize: 10,
      });
      message.success('添加厂商成功');
    });
  };

  render() {
    const { form, text, type, title, info = {}, vendorDetail } = this.props;
    const { createVisible, regionalIds, loading } = this.state;
    const { getFieldDecorator, getFieldsValue } = form;

    return (
      <>
        <Button key="btn" type={type} compact={type !== 'primary'} onClick={this.changeVisible}>
          {text}
        </Button>
        {createVisible && (
          <TinyDrawer
            key="drawer"
            width={948}
            title={<Typography.Title level={4}>{title}</Typography.Title>}
            visible={createVisible}
            destroyOnClose
            submitButtonLoading={loading}
            onClose={this.changeVisible}
            onSubmit={this.handleSubmit}
            onCancel={this.changeVisible}
          >
            <Form colon={false}>
              <StyledFormItem label="厂商全称、简称、级别" required>
                <VendorInfo
                  getFieldDecorator={getFieldDecorator}
                  info={vendorDetail}
                  mode={text === '编辑' ? 'edit' : 'new'}
                />
              </StyledFormItem>
              <StyledFormItem label="主联系人姓名、电话、邮箱">
                <PrimaryContact getFieldDecorator={getFieldDecorator} info={vendorDetail} />
              </StyledFormItem>
              <StyledFormItem label="分区人姓名、电话、邮箱、对接区域">
                <RegionalContactList
                  getFieldDecorator={getFieldDecorator}
                  getFieldsValue={getFieldsValue}
                  regionalIds={regionalIds}
                  info={vendorDetail}
                  onAdd={() => {
                    const id = shortid();
                    this.setState({
                      regionalIds: [...regionalIds, id],
                    });
                    form.setFieldsValue({
                      keys: [...regionalIds, id],
                    });
                  }}
                  onDelete={id => {
                    const newRegionalIds = regionalIds.filter(key => key !== id);
                    this.setState({
                      regionalIds: newRegionalIds,
                    });
                    form.setFieldsValue({
                      keys: newRegionalIds,
                    });
                  }}
                />
              </StyledFormItem>
              <Form.Item label="备注">
                {getFieldDecorator('description', {
                  rules: [
                    {
                      max: 50,
                      message: '最多输入 50 个字符！',
                    },
                  ],
                  initialValue: info.description,
                })(<Input.TextArea autoSize={{ minRows: 3 }} />)}
              </Form.Item>
            </Form>
          </TinyDrawer>
        )}
      </>
    );
  }
}
const mapStateToProps = ({ vendorManage: { vendorDetail } }) => ({
  vendorDetail,
});
const mapDispatchToProps = {
  fetchVendorListPage,
  fetchVendorDetail,
  saveVendorDetailSuccess: vendorActions.saveVendorDetailSuccess,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(
  Form.create({
    name: 'vendor_add_edit',
  })(CreateOrEdit)
);

function trimStr(str) {
  return typeof str === 'string' && str !== '' ? str.trim() : null;
}
