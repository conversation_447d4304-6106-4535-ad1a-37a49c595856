import React from 'react';

import Form from '@ant-design/compatible/es/form';
import { Select } from '@galiojs/awesome-antd';

import { Input } from '@manyun/base-ui.ui.input';

import { LEVEL_OPTIONS } from '@manyun/dc-brain.legacy.pages/vendor-manage/constants';

function VendorInfo({ getFieldDecorator, info, mode }) {
  return (
    <Input.Group compact>
      <Form.Item>
        {getFieldDecorator('vendorName', {
          rules: [
            { required: true, message: '厂商全称必填！' },
            {
              max: 32,
              message: '最多输入 32 个字符！',
            },
          ],
          initialValue: info.vendorName,
        })(<Input style={{ width: 200 }} />)}
      </Form.Item>
      <Form.Item>
        {getFieldDecorator('vendorCode', {
          rules: [
            { required: true, message: '厂商简称必填！' },
            {
              pattern: /[\u4e00-\u9fa5a-zA-Z0-9_]+/s,
              message: '厂商简称只能包含中文字符、字母、数字、下划线',
            },
            {
              max: 10,
              message: '最多输入 10 个字符！',
            },
          ],
          initialValue: info.vendorCode,
        })(<Input style={{ width: 200 }} disabled={mode === 'edit'} />)}
      </Form.Item>
      <Form.Item>
        {getFieldDecorator('vendorLevel', {
          rules: [{ required: true, message: '厂商级别必选！' }],
          initialValue: info.vendorLevel,
        })(
          <Select style={{ width: 200 }}>
            {LEVEL_OPTIONS.map(item => (
              <Select.Option key={item.value} value={item.value} title={item.label}>
                {item.label}
              </Select.Option>
            ))}
          </Select>
        )}
      </Form.Item>
    </Input.Group>
  );
}
export default React.forwardRef((props, ref) => <VendorInfo forwardedRef={ref} {...props} />);
