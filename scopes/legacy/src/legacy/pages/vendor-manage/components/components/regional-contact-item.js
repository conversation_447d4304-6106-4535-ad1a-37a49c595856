import React from 'react';

import Form from '@ant-design/compatible/es/form';

import { Input } from '@manyun/base-ui.ui.input';

import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';

function RegionalContactItem({ getFieldDecorator, id, current, getFieldsValue, regionalIds }) {
  return (
    <Input.Group compact>
      <Form.Item>
        {getFieldDecorator(`${id}_$$_name`, {
          rules: [
            {
              max: 50,
              whitespace: true,
              message: '最多输入 50 个字符！',
            },
          ],
          initialValue: current?.name,
        })(<Input style={{ width: 200 }} />)}
      </Form.Item>
      <Form.Item>
        {getFieldDecorator(`${id}_$$_mobile`, {
          validateFirst: true,
          rules: [
            {
              whitespace: true,
              pattern: /^1[3456789]\d{9}$/,
              message: '分区人电话格式不正确',
            },
            {
              validator: (_, value, callback) => {
                const fieldNames = getFieldNames(regionalIds, 'mobile');
                const fieldNamesValue = getFieldsValue(fieldNames);
                let tmp = false;
                fieldNames.forEach(key => {
                  if (value && fieldNamesValue[key] === value && key !== `${id}_$$_mobile`) {
                    tmp = true;
                  }
                });
                if (tmp) {
                  callback('分区人电话不可重复！');
                  return;
                }
                callback();
              },
            },
          ],
          initialValue: current?.mobile,
        })(<Input style={{ width: 200 }} />)}
      </Form.Item>
      <Form.Item>
        {getFieldDecorator(`${id}_$$_email`, {
          validateFirst: true,
          rules: [
            {
              type: 'email',
              whitespace: true,
              message: '分区联系人邮箱格式不正确',
            },
            {
              max: 50,
              message: '最多输入 50 个字符！',
            },
            {
              validator: (_, value, callback) => {
                const fieldNames = getFieldNames(regionalIds, 'email');
                const fieldNamesValue = getFieldsValue(fieldNames);
                let tmp = false;
                fieldNames.forEach(key => {
                  if (value && fieldNamesValue[key] === value && key !== `${id}_$$_email`) {
                    tmp = true;
                  }
                });
                if (tmp) {
                  callback('分区联系人邮箱不可重复！');
                  return;
                }
                callback();
              },
            },
          ],
          initialValue: current?.email,
        })(<Input style={{ width: 200 }} />)}
      </Form.Item>
      <Form.Item>
        {getFieldDecorator(`${id}_$$_area`, {
          initialValue: current?.area?.split('_$$_'),
        })(
          <LocationTreeSelect
            currentAuthorize
            multiple
            treeCheckable="true"
            showCheckedStrategy="SHOW_PARENT"
            getPopupContainer={trigger => trigger.parentNode.parentNode}
            style={{ width: 200 }}
            treeNodeLabelProp="metaCode"
          />
        )}
      </Form.Item>
    </Input.Group>
  );
}
export default RegionalContactItem;

function getFieldNames(ids, suffix) {
  let names = [];
  ids.forEach(id => {
    names.push(`${id}_$$_${suffix}`);
  });
  return names;
}
