import React from 'react';

import Form from '@ant-design/compatible/es/form';
import MinusCircleFilled from '@ant-design/icons/es/icons/MinusCircleFilled';
import PlusCircleFilled from '@ant-design/icons/es/icons/PlusCircleFilled';
import Styled from 'styled-components';

import { Button } from '@manyun/base-ui.ui.button';
import { List } from '@manyun/base-ui.ui.list';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';

import RegionalContactItem from './regional-contact-item';

const StyledListItem = Styled(List.Item)`
&.manyun-list-item {
  border:0
}
`;

function RegionalContactList({
  regionalIds = [],
  onAdd,
  onDelete,
  getFieldDecorator,
  info,
  getFieldsValue,
}) {
  return (
    <List
      dataSource={regionalIds}
      renderItem={(id, index) => {
        const current = info.vendorContacts?.find(vendor => vendor.id === id);
        return (
          <StyledListItem
            actions={[
              <Form.Item key="action">
                <GutterWrapper size="8px">
                  {index + 1 === regionalIds.length && (
                    <Button type="link" onClick={onAdd} compact>
                      <PlusCircleFilled style={{ color: 'var(--color-blue-7)' }} />
                    </Button>
                  )}
                  {regionalIds.length > 1 && (
                    <Button type="link" onClick={() => onDelete(id)} compact>
                      <MinusCircleFilled style={{ color: 'var(--color-blue-7)' }} />
                    </Button>
                  )}
                </GutterWrapper>
              </Form.Item>,
            ]}
          >
            <RegionalContactItem
              getFieldDecorator={getFieldDecorator}
              id={id}
              current={current}
              getFieldsValue={getFieldsValue}
              regionalIds={regionalIds}
            />
          </StyledListItem>
        );
      }}
    ></List>
  );
}
export default RegionalContactList;
