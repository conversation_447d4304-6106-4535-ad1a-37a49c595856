import React from 'react';

import Form from '@ant-design/compatible/es/form';

import { Input } from '@manyun/base-ui.ui.input';

function PrimaryContact({ getFieldDecorator, info }) {
  return (
    <Input.Group compact>
      <Form.Item>
        {getFieldDecorator('contactName', {
          rules: [
            {
              max: 50,
              whitespace: true,
              message: '最多输入 50 个字符！',
            },
          ],
          initialValue: info.contactName,
        })(<Input style={{ width: 200 }} />)}
      </Form.Item>
      <Form.Item>
        {getFieldDecorator('contactMobile', {
          rules: [
            {
              whitespace: true,
              pattern: /^1[3456789]\d{9}$/,
              message: '主联系人电话格式不正确',
            },
          ],
          initialValue: info.contactMobile,
        })(<Input style={{ width: 200 }} />)}
      </Form.Item>
      <Form.Item>
        {getFieldDecorator('contactEmail', {
          rules: [
            {
              type: 'email',
              message: '主联系人邮箱格式不正确',
            },
            {
              max: 50,
              whitespace: true,
              message: '最多输入 50 个字符！',
            },
          ],
          initialValue: info.contactEmail,
        })(<Input style={{ width: 200 }} />)}
      </Form.Item>
    </Input.Group>
  );
}
export default PrimaryContact;
