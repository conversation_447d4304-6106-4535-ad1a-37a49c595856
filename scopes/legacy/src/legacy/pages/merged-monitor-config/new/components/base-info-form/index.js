import React from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import { ApiSelect, Select } from '@galiojs/awesome-antd';
import get from 'lodash/get';

import { Input } from '@manyun/base-ui.ui.input';
import { Radio } from '@manyun/base-ui.ui.radio';

import { fetchPointsByCondition } from '@manyun/resource-hub.service.fetch-points-by-condition';

import { AssetClassificationApiTreeSelect } from '@manyun/dc-brain.legacy.components';
import { ENABLED_OR_DISABLED_OPTIONS, YES_OR_NO_KEY_MAP } from '@manyun/dc-brain.legacy.constants';
import { mergedMonitorConfigActions } from '@manyun/dc-brain.legacy.redux/actions/mergedMonitorConfigActions';
import { mergedMonitorConfigService } from '@manyun/dc-brain.legacy.services';

import { CONDITION_OPTIONS } from '../../../constants';
import { formatPointSelectValue, splitFormattedPointValue } from './../../utils';

/**
 * @typedef BaseInfoFormProps
 * @property {import('antd-3/lib/form/Form').WrappedFormUtils} form
 * @property {object} deviceCategory
 * @property {object} step1FieldValues
 * @property {(fieldValues: any) => void} onUpdateFieldValues
 */

/**
 *
 * @param {BaseInfoFormProps} props
 */
export function BaseInfoForm({
  mode,
  form,
  deviceCategory,
  clonedConfigId,
  onUpdateFieldValues,
  onSelectClonedConfig,
  resetState4Creation,
}) {
  const { getFieldDecorator, getFieldValue } = form;

  const selectedDeviceType = getFieldValue('deviceType');
  const selectedPointValue = getFieldValue('formattedPointValue');
  const { dataType, validLimits } = splitFormattedPointValue(selectedPointValue?.key);

  return (
    <Form
      style={{ maxWidth: 450, marginLeft: 'auto', marginRight: 'auto' }}
      labelCol={{ xl: 6 }}
      wrapperCol={{ xl: 18 }}
      colon={false}
    >
      {mode === 'new' && (
        <Form.Item label="克隆配置">
          <ApiSelect
            style={{ width: '100%' }}
            showSearch
            trigger="onDidMount"
            optionWithValue
            dataService={async () => {
              const { response } = await mergedMonitorConfigService.fetchAllMergedMonitorConfigs();
              if (response) {
                return response.data;
              }
              return [];
            }}
            fieldNames={{ label: 'name', value: 'id' }}
            value={clonedConfigId}
            allowClear
            onChange={(configId, option) => {
              if (!configId) {
                resetState4Creation();
              }
              onSelectClonedConfig(configId);
              onUpdateFieldValues(getFieldValues(option, deviceCategory));
            }}
          />
        </Form.Item>
      )}
      <Form.Item label="根因设备类型">
        {getFieldDecorator('deviceType', {
          rules: [
            {
              required: true,
              message: '根因设备类型必选！',
            },
          ],
        })(
          <AssetClassificationApiTreeSelect
            dataType={['space', 'snDevice']}
            category="category"
            disabledDepths={[0, 1]}
            requestOnDidMount
          />
        )}
      </Form.Item>
      <Form.Item label="根因测点名称">
        {getFieldDecorator('formattedPointValue', {
          rules: [
            {
              required: true,
              transform: value => value?.key,
              message: '根因测点名称必选！',
            },
          ],
        })(
          <ApiSelect
            style={{ width: '100%' }}
            labelInValue
            placeholder={!selectedDeviceType?.code ? '请选择根因设备类型' : undefined}
            fieldNames={{
              label: 'name',
              value: formatPointSelectValue,
            }}
            disabled={!selectedDeviceType?.code}
            serviceQueries={[selectedDeviceType?.code]}
            dataService={pointSelectDataService}
          />
        )}
      </Form.Item>
      {dataType === 'AI' && (
        <Form.Item label="触发条件">
          {getFieldDecorator('conditions', {
            rules: [{ required: true, message: '触发条件必选！' }],
          })(<Radio.Group options={CONDITION_OPTIONS} />)}
        </Form.Item>
      )}
      {dataType === 'DI' && (
        <Form.Item label="触发条件">
          {getFieldDecorator('conditionValues', {
            rules: [{ required: true, message: '至少选择 1 项触发条件！' }],
          })(
            <Select mode="multiple">
              {validLimits?.map(validLimitStr => {
                const [value, label] = validLimitStr.split('=');

                return (
                  <Select.Option key={value} value={value}>
                    {label}
                  </Select.Option>
                );
              })}
            </Select>
          )}
        </Form.Item>
      )}
      <Form.Item label="收敛有效期">
        {getFieldDecorator('expire', {
          rules: [{ required: true, message: '收敛有效期必选！' }],
        })(
          <Select style={{ width: '100%' }}>
            {[0, 1, 5, 10, 15, 20, 30].map(expire => (
              <Select.Option key={String(expire)} value={String(expire * 60)}>
                {expire === 0 ? '永久生效' : String(expire) + '分钟'}
              </Select.Option>
            ))}
          </Select>
        )}
      </Form.Item>
      <Form.Item label="收敛配置名称">
        {getFieldDecorator('name', {
          rules: [
            { required: true, message: '收敛配置名称必填！' },
            {
              max: 32,
              message: '最多输入 32 个字符！',
            },
          ],
        })(<Input placeholder="选择根因设备类型和根因测点名称后可自动生成" />)}
      </Form.Item>
      <Form.Item label="状态">
        {getFieldDecorator('available')(
          <Radio.Group>
            {ENABLED_OR_DISABLED_OPTIONS.map(({ label, value }) => (
              <Radio key={value} value={value}>
                {label}
              </Radio>
            ))}
          </Radio.Group>
        )}
      </Form.Item>
      <Form.Item label="备注">
        {getFieldDecorator('description', {
          rules: [
            {
              max: 67,
              message: '最多输入 67 个字符！',
            },
          ],
        })(<Input.TextArea />)}
      </Form.Item>
    </Form>
  );
}

export const FCBaseInfoForm = React.forwardRef((props, ref) => {
  React.useImperativeHandle(ref, () => ({ form: props.form }));

  return <BaseInfoForm {...props} />;
});

/**
 * @type {import('antd-3/lib/form/Form').FormCreateOption<BaseInfoFormProps>}
 */
const formCreateOpts = {
  onFieldsChange(props, fieldValues) {
    const propsCount = Object.keys(fieldValues).length;

    // 因为在调用 form.validateFields() 时，也会触发 onnFieldsChange，设置校验状态和错误信息，
    // 此时 fieldValues 包含多个被校验的字段，不能重置其他字段
    if (propsCount === 1 && fieldValues['deviceType']) {
      const { formattedPointValue, name, conditions, conditionValues } = props.step1FieldValues;
      if (formattedPointValue?.value?.key) {
        props.onUpdateFieldValues({
          formattedPointValue: {
            ...formattedPointValue,
            value: undefined,
          },
          name: {
            ...name,
            value: '',
          },
          conditions: {
            ...conditions,
            value: [],
          },
          conditionValues: {
            ...conditionValues,
            value: [],
          },
          ...fieldValues,
        });
        return;
      }
    }

    if (propsCount === 1 && fieldValues['formattedPointValue']) {
      const { value: deviceType } = props.step1FieldValues.deviceType;
      const { label: pointName } = fieldValues['formattedPointValue'].value;
      props.onUpdateFieldValues({
        name: {
          value: `${deviceType.name}-${pointName}收敛配置`,
        },
        ...fieldValues,
      });
      return;
    }

    // 以前触发条件是可以多选的，为了不修改其他代码逻辑，故此仍保持数组格式
    if (fieldValues['conditions']) {
      props.onUpdateFieldValues({
        ...fieldValues,
        conditions: {
          ...fieldValues.conditions,
          value: [fieldValues.conditions.value],
        },
      });
      return;
    }

    props.onUpdateFieldValues(fieldValues);
  },
  mapPropsToFields({ step1FieldValues }) {
    return Object.keys(step1FieldValues).reduce((fields, fieldId) => {
      let fieldValue = step1FieldValues[fieldId];

      // 触发条件控件从 `Checkbox` 改为 `Radio` 需要转换数据
      if (fieldId === 'conditions') {
        fieldValue = {
          ...fieldValue,
          value: fieldValue?.value?.[0],
        };
      }

      fields[fieldId] = Form.createFormField(fieldValue);

      return fields;
    }, {});
  },
};

export const EhancedFCBaseInfoForm = Form.create(formCreateOpts)(FCBaseInfoForm);

const mapStateToProps = ({
  common: { deviceCategory },
  mergedMonitorConfig: { step1FieldValues },
}) => ({ deviceCategory, step1FieldValues });
const mapDispatchToProps = {
  onUpdateFieldValues: mergedMonitorConfigActions.updateStep1FieldValues,
  resetState4Creation: mergedMonitorConfigActions.resetState4Creation,
};

export default connect(mapStateToProps, mapDispatchToProps)(EhancedFCBaseInfoForm);

function getFieldValues(option, deviceCategory) {
  if (!option) {
    return {};
  }
  const pointDataType = option.dataType?.code;
  const fieldValues = {
    deviceType: {
      value: {
        code: option.deviceType,
        name: get(
          deviceCategory,
          ['normalizedList', option.deviceType, 'metaName'],
          option.deviceType
        ),
      },
    },
    formattedPointValue: {
      value: {
        key: formatPointSelectValue({
          dataType: { code: pointDataType },
          pointCode: option.pointCode,
          validLimits: option.validLimits,
        }),
        label: option.pointName,
      },
    },
    conditions: {
      value: option.condition,
    },
    conditionValues: {
      value: option.conditionValue ? option.conditionValue.join(',').split(',') : undefined,
    },
    expire: {
      value: String(option.expire),
    },
    name: {
      value: option.name,
    },
    available: {
      value: option.available === true ? YES_OR_NO_KEY_MAP.YES : YES_OR_NO_KEY_MAP.NO,
    },
    description: {
      value: option.description,
    },
  };
  return fieldValues;
}

async function pointSelectDataService(selectedDeviceType) {
  const { data } = await fetchPointsByCondition({
    deviceType: selectedDeviceType,
    dataTypeList: ['AI', 'DI'],
    isRemoveSub: true,
  });
  if (data) {
    return data.data.map(point => point.toApiObject());
  }
  return [];
}
