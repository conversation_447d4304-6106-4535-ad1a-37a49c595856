import React from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import DeleteOutlined from '@ant-design/icons/es/icons/DeleteOutlined';
import { Select } from '@galiojs/awesome-antd';
import get from 'lodash/get';

import { Button } from '@manyun/base-ui.ui.button';
import { Collapse } from '@manyun/base-ui.ui.collapse';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Table } from '@manyun/base-ui.ui.table';

import {
  AssetClassificationApiTreeSelect,
  GutterWrapper,
  PointsSelectModalButton,
} from '@manyun/dc-brain.legacy.components';
import { MERGE_SCOPE_KEY_MAP, MERGE_SCOPE_OPTIONS } from '@manyun/dc-brain.legacy.constants';
import { mergedMonitorConfigActions } from '@manyun/dc-brain.legacy.redux/actions/mergedMonitorConfigActions';

import { splitFormattedPointValue } from './../../utils';
import { StyledFormItem } from './styled';

/**
 * @param {object} props
 * @param {'AI'|'DI'} props.pointDataType 测点数据类型
 */
export function MergedItemsCollapse({
  xRef,
  value,
  childItemsMap,
  mergeScopeMap,
  disabledTreeNodeKeys,
  pointDataType,
  conditionValue,
  parentDeviceType,
  deleteDeviceType,
  deleteChildItem,
  updateChildItem,
  updateMergeScope,
  updateDeviceType,
  updateChildItems,
}) {
  return (
    <GutterWrapper ref={xRef} mode="vertical">
      {Array.isArray(value) &&
        value.map(deviceType => (
          <div key={deviceType}>
            <Row style={{ maxWidth: 1200 }}>
              <Col xl={8}>
                <Form.Item
                  label="被收敛设备类型"
                  labelCol={{ xl: 8 }}
                  wrapperCol={{ xl: 16 }}
                  validateStatus={deviceType ? 'success' : 'error'}
                >
                  {/* <ApiTreeSelect
                    requestOnDidMount
                    disabledDepths={[0, 1]}
                    fieldNames={{ value: 'metaCode', key: 'metaCode', title: 'metaName' }}
                    dataService={() => {
                      if (deviceCategory) {
                        return Promise.resolve(deviceCategory);
                      }
                      // call the exact service as a fallback
                      return treeDataService.fetchDeviceCategory();
                    }}
                    value={deviceType}
                    onChange={newDeviceType => {
                      updateDeviceType({ oldDeviceType: deviceType, newDeviceType });
                    }}
                  /> */}
                  <AssetClassificationApiTreeSelect
                    dataType={['space', 'snDevice']}
                    category="categorycode"
                    disabledDepths={[0, 1]}
                    requestOnDidMount
                    value={deviceType}
                    onChange={newDeviceType => {
                      updateDeviceType({ oldDeviceType: deviceType, newDeviceType });
                    }}
                  />
                </Form.Item>
              </Col>
              <Col xl={5}>
                <Form.Item
                  label="收敛关系"
                  labelCol={{ xl: 10 }}
                  wrapperCol={{ xl: 12 }}
                  validateStatus={mergeScopeMap[deviceType] ? 'success' : 'error'}
                  help={!mergeScopeMap[deviceType] ? '收敛关系必选' : undefined}
                >
                  <Select
                    value={mergeScopeMap[deviceType]}
                    onChange={mergeScope => {
                      updateMergeScope({ deviceType, mergeScope });
                    }}
                  >
                    {MERGE_SCOPE_OPTIONS.map(({ label, value }) => (
                      <Select.Option
                        key={value}
                        value={value}
                        disabled={getMergeScopeOptionDisabled(value, deviceType, parentDeviceType)}
                      >
                        {label}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col xl={1}>
                <DeleteConfirm
                  variant="popconfirm"
                  targetName="被收敛项的所有测点"
                  onOk={() => deleteDeviceType(deviceType)}
                >
                  <Button type="link" compact>
                    <DeleteOutlined />
                  </Button>
                </DeleteConfirm>
              </Col>
            </Row>
            <Collapse bordered={false} defaultActiveKey={['child-items-table']}>
              <Collapse.Panel key="child-items-table" header="被收敛测点" style={customPanelStyle}>
                <Table
                  rowKey="metaCode"
                  size="small"
                  columns={getColumns(childItemsMap[deviceType], deleteChildItem, updateChildItem)}
                  dataSource={childItemsMap[deviceType]}
                  pagination={false}
                  locale={{
                    emptyText: (
                      <div className="has-error">
                        <span className="manyun-form-explain">至少添加一条被收敛测点</span>
                      </div>
                    ),
                  }}
                />
              </Collapse.Panel>
            </Collapse>
          </div>
        ))}
      <PointsSelectModalButton
        text="+添加被收敛项"
        title="添加被收敛项"
        disabledTreeNodeKeys={disabledTreeNodeKeys}
        onOk={({ deviceTypes, pointsMap }) => {
          updateChildItems({
            deviceTypes,
            childItemsMap:
              pointDataType === 'AI'
                ? Object.keys(pointsMap).reduce((map, deviceType) => {
                    map[deviceType] = pointsMap[deviceType].map(point => ({
                      ...point,
                      conditions: [conditionValue], // 默认跟随步骤一种的触发条件
                    }));

                    return map;
                  }, {})
                : pointsMap,
          });
        }}
        visibleLoadTypes={['snDevice', 'space']}
      />
    </GutterWrapper>
  );
}

const mapStateToProps = ({
  common: { deviceCategory },
  mergedMonitorConfig: {
    childItemsMap,
    mergeScopeMap,
    step1FieldValues: { deviceType, formattedPointValue, conditions },
  },
}) => {
  const childItemKeys = Object.keys(childItemsMap).reduce((acc, deviceType) => {
    acc.push(...childItemsMap[deviceType].map(({ metaCode }) => metaCode));

    return acc;
  }, []);

  const { dataType, pointCode } = splitFormattedPointValue(formattedPointValue.value.key);
  const parentPointTreeNodeKey = `${deviceType.value.code}_$$_${pointCode}`;

  const disabledTreeNodeKeys = [...childItemKeys, parentPointTreeNodeKey];

  return {
    deviceCategory,
    childItemsMap,
    mergeScopeMap,
    parentDeviceType: get(deviceType, ['value', 'code']),
    disabledTreeNodeKeys,
    pointDataType: dataType,
    conditionValue: conditions.value?.[0],
  };
};
const mapDispatchToProps = {
  deleteDeviceType: mergedMonitorConfigActions.deleteDeviceType,
  deleteChildItem: mergedMonitorConfigActions.deleteChildItem,
  updateChildItem: mergedMonitorConfigActions.updateChildItem,
  updateMergeScope: mergedMonitorConfigActions.updateMergeScope,
  updateDeviceType: mergedMonitorConfigActions.updateDeviceType,
  updateChildItems: mergedMonitorConfigActions.updateChildItems,
};

const ConnectedMergedItemsCollapse = connect(
  mapStateToProps,
  mapDispatchToProps
)(MergedItemsCollapse);

export default React.forwardRef((props, ref) => (
  <ConnectedMergedItemsCollapse xRef={ref} {...props} />
));

const customPanelStyle = {
  background: 'transparent',
};

const getColumns = (data, deleteChildItem, updateChildItem) => [
  {
    align: 'center',
    title: '名称',
    dataIndex: 'name',
  },
  {
    align: 'center',
    title: '类型',
    dataIndex: 'typeText',
  },
  {
    align: 'center',
    title: '收敛逻辑',
    key: 'mergeLogic',
    render(__, { dataType, validLimits, deviceType, metaCode, conditions, conditionValues }) {
      if (dataType.code === 'AI') {
        const validationProps = {};
        if (!(Array.isArray(conditions) && conditions.length)) {
          validationProps.validateStatus = 'error';
          validationProps.help = '收敛逻辑必选！';
        }
        return (
          <StyledFormItem {...validationProps}>
            <Radio.Group
              options={[
                { label: '超下限告警', value: 'LOWER' },
                { label: '超上限告警', value: 'UPPER' },
              ]}
              value={conditions?.[0]}
              onChange={({ target: { value: conditions } }) => {
                updateChildItem({
                  deviceType,
                  childItemMetaCode: metaCode,
                  conditions: [conditions],
                });
              }}
            />
          </StyledFormItem>
        );
      }
      if (dataType.code !== 'DI') {
        console.error(`Point data type(${dataType.code}) not supported.`);
        return;
      }
      const validationProps = {};
      if (!(Array.isArray(conditionValues) && conditionValues.length)) {
        validationProps.validateStatus = 'error';
        validationProps.help = '收敛逻辑必选！';
      }
      return (
        <StyledFormItem
          label="状态为"
          labelCol={{ sm: 8 }}
          wrapperCol={{ sm: 10 }}
          {...validationProps}
        >
          <Select
            mode="multiple"
            maxTagCount={2}
            size="small"
            value={conditionValues}
            onChange={conditionValues => {
              updateChildItem({
                deviceType,
                childItemMetaCode: metaCode,
                conditions: 'EQUAL',
                conditionValues,
              });
            }}
          >
            {validLimits.map(validLimit => {
              const [value, label] = validLimit.split('=');

              return (
                <Select.Option key={value} value={value}>
                  {label}
                </Select.Option>
              );
            })}
          </Select>
        </StyledFormItem>
      );
    },
  },
  {
    align: 'center',
    title: '操作',
    dataIndex: 'actions',
    render(__, { deviceType, metaCode }) {
      return (
        <Button
          compact
          type="link"
          onClick={() => {
            const shouldDeleteDeviceType = data.length <= 1;
            const payload = {
              shouldDeleteDeviceType,
              deviceType,
              childItemMetaCode: metaCode,
            };
            deleteChildItem(payload);
          }}
        >
          删除
        </Button>
      );
    },
  },
];

function getMergeScopeOptionDisabled(optionVal, deviceType, parentDeviceType) {
  if (deviceType === parentDeviceType && optionVal === MERGE_SCOPE_KEY_MAP.DOWN) {
    return true;
  }
  if (deviceType !== parentDeviceType && optionVal === MERGE_SCOPE_KEY_MAP.WITHIN) {
    return true;
  }

  return false;
}
