import React from 'react';
import { connect } from 'react-redux';

import get from 'lodash/get';

import { Button } from '@manyun/base-ui.ui.button';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';

import {
  MERGED_MONITOR_CONFIG_LIST,
  NEW_MERGED_MONITOR_CONFIG,
} from '@manyun/dc-brain.legacy.constants/urls';
import { mergedMonitorConfigActions } from '@manyun/dc-brain.legacy.redux/actions/mergedMonitorConfigActions';
import { redirectActionCreator } from '@manyun/dc-brain.legacy.redux/actions/routerActions';

import { CONDITION_TEXT_MAP } from '../../../constants';
import { splitFormattedPointValue } from '../../utils';
import { StyledResult } from './styled';

export function ResultStep({
  mode,
  deviceTypeMap,
  name,
  deviceType,
  pointName,
  triggerPrerequisites,
  expire,
  itemCount,
  redirect,
  resetState,
}) {
  const title = mode === 'new' ? '创建成功' : '修改成功';
  const expireTxt = Number(expire) === 0 ? '永久生效' : `${Number(expire) / 60}分钟`;

  return (
    <StyledResult
      status="success"
      title={title}
      subTitle="收敛配置生效后，将根据规则进行告警收敛"
      extra={[
        <Button
          key="refresh-button"
          type="primary"
          onClick={() => {
            if (mode === 'new') {
              window.location.reload();
            } else if (mode === 'edit') {
              resetState();
              redirect(NEW_MERGED_MONITOR_CONFIG);
            } else {
              throw new Error(`mode(${mode}) not supported.`);
            }
          }}
        >
          新增配置
        </Button>,
        <Button
          key="go-back-button"
          onClick={() => {
            redirect(MERGED_MONITOR_CONFIG_LIST);
          }}
        >
          返回列表
        </Button>,
      ]}
    >
      <Descriptions style={{ width: 400, marginLeft: 'auto', marginRight: 'auto' }} column={1}>
        <Descriptions.Item label="配置名称">{name}</Descriptions.Item>
        <Descriptions.Item label="根因设备类型">
          {get(deviceTypeMap, [deviceType, 'metaName'])}
        </Descriptions.Item>
        <Descriptions.Item label="根因测点">{pointName}</Descriptions.Item>
        <Descriptions.Item label="触发条件">{triggerPrerequisites}</Descriptions.Item>
        <Descriptions.Item label="收敛有效期">{expireTxt}</Descriptions.Item>
        <Descriptions.Item label="包含规则数">{itemCount}条</Descriptions.Item>
      </Descriptions>
    </StyledResult>
  );
}

const mapStateToProps = ({
  common: { deviceCategory },
  mergedMonitorConfig: {
    step1FieldValues: {
      name,
      deviceType,
      formattedPointValue,
      conditions,
      conditionValues,
      expire,
    },
    step2FieldValues: { childItemDeviceTypes },
    childItemsMap,
  },
}) => {
  const { dataType, validLimits } = splitFormattedPointValue(formattedPointValue.value.key);
  let triggerPrerequisites;
  if (dataType === 'AI' && Array.isArray(conditions.value) && conditions.value.length) {
    triggerPrerequisites = conditions.value
      .map(condition => CONDITION_TEXT_MAP[condition])
      .join('，');
  }
  if (
    dataType === 'DI' &&
    Array.isArray(validLimits) &&
    validLimits.length &&
    Array.isArray(conditionValues.value) &&
    conditionValues.value.length
  ) {
    const validLimitsMap = validLimits.reduce((map, expression) => {
      const [key, value] = expression.split('=');
      map[key] = value;

      return map;
    }, {});
    triggerPrerequisites = conditionValues.value
      .map(conditionValue => validLimitsMap[conditionValue])
      .join('，');
  }

  const itemCount = childItemDeviceTypes.value.reduce((c, deviceType) => {
    c += childItemsMap[deviceType].length;

    return c;
  }, 0);

  return {
    deviceTypeMap: deviceCategory ? deviceCategory.normalizedList : null,
    name: name.value,
    deviceType: deviceType.value.code,
    pointName: formattedPointValue.value.label,
    triggerPrerequisites,
    expire: expire.value,
    itemCount,
  };
};
const mapDispatchToProps = {
  redirect: redirectActionCreator,
  resetState: mergedMonitorConfigActions.resetState4Creation,
};

export default connect(mapStateToProps, mapDispatchToProps)(ResultStep);
