import React from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';

import MergedItemsCollapse from './collapse';

/**
 * @typedef MergedItemsFormProps
 * @property {import('antd-3/lib/form/Form').WrappedFormUtils} form
 */

/**
 * @param {MergedItemsFormProps} props
 */
export function MergedItemsForm({ mode, form, childItemsMap, mergeScopeMap }) {
  const { getFieldDecorator } = form;

  function validateChildItems(__, deviceTypes, callback) {
    if (!(Array.isArray(deviceTypes) && deviceTypes.length)) {
      callback('至少添加 1 条被收敛项！');
    } else {
      const passed = deviceTypes.every(deviceType => {
        const childItems = childItemsMap[deviceType];
        const mergeLogics =
          Array.isArray(childItems) &&
          childItems.length > 0 &&
          childItems.every(({ type, ...rest }) => {
            if (type === 'MERGED_MONITOR_CONFIG') {
              // 收敛配置默认为通过校验
              return true;
            }
            const {
              dataType: { code },
              conditions,
              conditionValues,
            } = rest;
            if (code === 'AI') {
              return Array.isArray(conditions) && conditions.length;
            }
            if (code === 'DI') {
              return Array.isArray(conditionValues) && conditionValues.length;
            }
            console.error(`dataType(${code}) is neither 'AI' nor 'DI'.`);
            return false;
          });
        const mergeScope = mergeScopeMap[deviceType];

        // 所有被收敛测点或配置都选择了收敛逻辑且选择了收敛关系
        return mergeLogics && mergeScope;
      });
      if (!passed) {
        callback(' ');
      } else {
        callback();
      }
    }
  }

  return (
    <Form
      style={{ width: (1342 / 1600) * 100 + '%', marginLeft: 'auto', marginRight: 'auto' }}
      colon={false}
    >
      <Form.Item label="被收敛项" labelCol={{ xl: 24 }} wrapperCol={{ xl: 24 }}>
        {getFieldDecorator('childItemDeviceTypes', {
          rules: [{ required: true, validator: validateChildItems }],
        })(<MergedItemsCollapse mode={mode} />)}
      </Form.Item>
    </Form>
  );
}

export const FCMergedItemsForm = React.forwardRef((props, ref) => {
  React.useImperativeHandle(ref, () => ({ form: props.form }));

  return <MergedItemsForm {...props} />;
});

export const EhancedFCMergedItemsForm = Form.create({
  mapPropsToFields({ step2FieldValues }) {
    return Object.keys(step2FieldValues).reduce((fields, fieldId) => {
      fields[fieldId] = Form.createFormField(step2FieldValues[fieldId]);

      return fields;
    }, {});
  },
})(FCMergedItemsForm);

const mapStateToProps = ({
  mergedMonitorConfig: { step2FieldValues, childItemsMap, mergeScopeMap },
}) => ({
  step2FieldValues,
  childItemsMap,
  mergeScopeMap,
});

export default connect(mapStateToProps)(EhancedFCMergedItemsForm);
