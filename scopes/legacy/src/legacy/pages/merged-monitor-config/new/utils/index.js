import { POINT_TYPE_CODE_MAP } from '@manyun/dc-brain.legacy.constants/point';

const FIELD_DELIMITER = '_$$_';
const VALID_LIMITS_DELIMITER = ';';

export function formatPointSelectValue({ dataType: { code }, pointCode, validLimits }) {
  const vlStr = (validLimits || []).join(VALID_LIMITS_DELIMITER);

  return [code, pointCode, vlStr].join(FIELD_DELIMITER);
}

/**
 * 拆分点位信息，得到点位类型、编码、DI类型点位的正常取值范围
 * @param {string} [pointValue]
 * @returns {{dataType?:'DI'|'AI';pointCode?:string;validLimits?:string}}
 */
export function splitFormattedPointValue(pointValue) {
  if (!pointValue) {
    return {};
  }
  const [dataType, pointCode, vlStr] = pointValue.split(FIELD_DELIMITER);
  let validLimits;
  if (vlStr === '') {
    validLimits = null;
  } else {
    validLimits = vlStr.split(VALID_LIMITS_DELIMITER);
  }

  return {
    dataType,
    pointCode,
    validLimits,
  };
}

export function getPointTextByPointType(pointType) {
  if (pointType === POINT_TYPE_CODE_MAP.ORI) {
    return '原始测点';
  }
  return '自定义测点';
}
