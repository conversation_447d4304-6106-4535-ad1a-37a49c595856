import React, { useEffect, useRef, useState } from 'react';
import { connect } from 'react-redux';
import { useParams } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Steps } from '@manyun/base-ui.ui.steps';
import { Typography } from '@manyun/base-ui.ui.typography';

import { FooterToolBar, GutterWrapper, TinyCard } from '@manyun/dc-brain.legacy.components';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  createNewMergedMonitorConfigActionCreator,
  getBaseInfoActionCreator,
  getChildItemsActionCreator,
  mergedMonitorConfigActions,
  updateChildItemsActionCreator,
  updateMergedMonitorConfigActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/mergedMonitorConfigActions';

import BaseInfoForm from './components/base-info-form';
import MergedItemsForm from './components/merged-items-form';
import ResultStep from './components/result';

export function MergedMonitorConfigNew({
  mode = 'new',
  stepIdx,
  stepBtnLoading,
  isStep1FieldsTouched,
  syncCommonData,
  create,
  update,
  updateChildItems,
  getBaseInfo,
  getChildItems,
  goToPrevStep,
  goToNextStep,
  resetState,
}) {
  const [clonedConfigId, setClonedConfigId] = useState();

  const { id } = useParams();

  useEffect(() => {
    return () => {
      resetState();
    };
  }, [resetState]);

  useEffect(() => {
    syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });

    if (mode === 'edit') {
      getBaseInfo(id);
      getChildItems(id);
    }
  }, [syncCommonData, mode, id, getBaseInfo, getChildItems]);

  useEffect(() => {
    if (clonedConfigId === undefined) {
      return;
    }
    getChildItems(clonedConfigId);
  }, [clonedConfigId, getChildItems]);

  const baseInfoFormRef = useRef();
  const mergedItemsFormRef = useRef();

  function submitHandler() {
    if (stepIdx === 0) {
      if (baseInfoFormRef.current && baseInfoFormRef.current.form) {
        baseInfoFormRef.current.form.validateFields(errs => {
          if (errs) {
            return;
          }
          if (mode === 'new') {
            goToNextStep();
          } else if (mode === 'edit') {
            update({ id });
          }
        });
      }
    } else if (stepIdx === 1) {
      if (mergedItemsFormRef.current && mergedItemsFormRef.current.form) {
        mergedItemsFormRef.current.form.validateFields(errs => {
          if (errs) {
            return;
          }
          if (mode === 'new') {
            create();
          } else if (mode === 'edit') {
            updateChildItems(id);
          } else {
            throw new Error(`mode(${mode}) not supported.`);
          }
        });
      }
    }
  }

  const nextStepBtn = (
    <Button type="primary" loading={stepBtnLoading} onClick={submitHandler}>
      {stepIdx === 1 ? '提交' : '下一步'}
    </Button>
  );

  const popoverNextStepBtn = (
    <Popconfirm title="配置信息已被修改，确认保存吗？" onConfirm={submitHandler}>
      {React.cloneElement(nextStepBtn, { onClick: undefined })}
    </Popconfirm>
  );

  return (
    <>
      <GutterWrapper
        style={{ paddingBottom: /* FooterToolBar's height */ 48 }}
        direction="vertical"
      >
        <Typography.Title level={4}>告警收敛配置</Typography.Title>
        <Typography.Paragraph>
          通过配置收敛规则，将不同设备类型的告警按照规则进行聚合，以收敛规则作为故障根因定位逻辑，收敛告警风暴。
        </Typography.Paragraph>
        <TinyCard>
          <GutterWrapper direction="vertical" size="2rem">
            <Steps style={{ paddingLeft: 100, paddingRight: 100 }} current={stepIdx}>
              <Steps.Step title="配置信息"></Steps.Step>
              <Steps.Step title="规则配置"></Steps.Step>
              <Steps.Step title="配置完成"></Steps.Step>
            </Steps>
            {stepIdx === 0 && (
              <BaseInfoForm
                wrappedComponentRef={baseInfoFormRef}
                mode={mode}
                clonedConfigId={clonedConfigId}
                onSelectClonedConfig={setClonedConfigId}
              />
            )}
            {stepIdx === 1 && (
              <MergedItemsForm mode={mode} wrappedComponentRef={mergedItemsFormRef} />
            )}
            {stepIdx === 2 && <ResultStep mode={mode} />}
          </GutterWrapper>
        </TinyCard>
      </GutterWrapper>
      {stepIdx < 2 && (
        <FooterToolBar>
          <GutterWrapper>
            {stepIdx === 1 && (
              <Button
                loading={stepBtnLoading}
                onClick={() => {
                  goToPrevStep();
                }}
              >
                上一步
              </Button>
            )}
            {mode === 'edit' && isStep1FieldsTouched && stepIdx === 0
              ? popoverNextStepBtn
              : nextStepBtn}
          </GutterWrapper>
        </FooterToolBar>
      )}
    </>
  );
}

const mapStateToProps = ({
  mergedMonitorConfig: { stepIdx, stepBtnLoading, step1FieldValues },
}) => ({
  stepIdx,
  stepBtnLoading,
  isStep1FieldsTouched: checkFieldsTouched(step1FieldValues),
});
const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
  create: createNewMergedMonitorConfigActionCreator,
  update: updateMergedMonitorConfigActionCreator,
  updateChildItems: updateChildItemsActionCreator,
  getBaseInfo: getBaseInfoActionCreator,
  getChildItems: getChildItemsActionCreator,
  goToPrevStep: mergedMonitorConfigActions.goToPrevStep,
  goToNextStep: mergedMonitorConfigActions.goToNextStep,
  resetState: mergedMonitorConfigActions.resetState4Creation,
};

export default connect(mapStateToProps, mapDispatchToProps)(MergedMonitorConfigNew);

function checkFieldsTouched(fields) {
  return Object.keys(fields).some(fieldId => {
    const { touched } = fields[fieldId];

    return touched;
  });
}
