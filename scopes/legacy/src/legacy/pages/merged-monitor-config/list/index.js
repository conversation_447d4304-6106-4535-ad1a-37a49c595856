import React, { useEffect } from 'react';
import { connect } from 'react-redux';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';

import DataCard from './components/data-card';
import SearchCard from './components/search-card';

export function MergedMonitorConfigList({ syncCommonData }) {
  useEffect(() => {
    syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });
  }, [syncCommonData]);

  return (
    <GutterWrapper mode="vertical">
      <SearchCard />
      <DataCard />
    </GutterWrapper>
  );
}

const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
};

export default connect(null, mapDispatchToProps)(MergedMonitorConfigList);
