import React from 'react';
import { connect } from 'react-redux';

import { FiltersForm, Form, Select } from '@galiojs/awesome-antd';

import { Input } from '@manyun/base-ui.ui.input';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';

import { AssetClassificationApiTreeSelect } from '@manyun/dc-brain.legacy.components';
import { ENABLED_OR_DISABLED_OPTIONS } from '@manyun/dc-brain.legacy.constants';
import {
  getDataActionCreator,
  mergedMonitorConfigActions,
  resetSearchValuesActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/mergedMonitorConfigActions';

export function SearchForm({ fields, updateSearchValues, onSearch, onReset }) {
  const [form] = Form.useForm();

  return (
    <FiltersForm
      form={form}
      fields={fields}
      items={[
        {
          label: '根因设备类型',
          name: 'deviceTypeList',
          control: (
            <AssetClassificationApiTreeSelect
              dataType={['space', 'snDevice']}
              category="categorycode"
              disabledDepths={[0, 1]}
              allowClear
              multiple
              maxTagCount={1}
            />
          ),
        },
        {
          label: '状态',
          name: 'available',
          control: (
            <Select allowClear>
              {ENABLED_OR_DISABLED_OPTIONS.map(({ label, value }) => {
                return (
                  <Select.Option key={value} value={value}>
                    {label}
                  </Select.Option>
                );
              })}
            </Select>
          ),
        },
        {
          label: '收敛配置名称',
          name: 'name',
          control: <Input allowClear />,
        },
        {
          label: '更新人',
          name: 'lastOperatorName',
          control: <UserSelect allowClear />,
        },
      ]}
      onFieldsChange={changedFields => {
        updateSearchValues(
          changedFields.reduce((mapper, field) => {
            const name = field.name.join('.');
            mapper[name] = {
              ...field,
              name,
            };

            return mapper;
          }, {})
        );
      }}
      onSearch={onSearch}
      onReset={onReset}
    />
  );
}

const mapStateToProps = ({ mergedMonitorConfig: { searchValues } }) => ({
  fields: Object.keys(searchValues).map(name => {
    const field = searchValues[name];

    return {
      ...field,
      name: name.split('.'),
    };
  }),
});
const mapDispatchToProps = {
  updateSearchValues: mergedMonitorConfigActions.updateSearchValues,
  onSearch: getDataActionCreator,
  onReset: resetSearchValuesActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(SearchForm);
