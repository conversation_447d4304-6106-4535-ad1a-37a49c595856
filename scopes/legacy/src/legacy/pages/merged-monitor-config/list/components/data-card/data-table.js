import React, { useCallback, useEffect } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import get from 'lodash/get';

import { Button } from '@manyun/base-ui.ui.button';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Tag } from '@manyun/base-ui.ui.tag';

import { generateSpecificMergedMonitorConfig } from '@manyun/monitoring.route.monitoring-routes';

import { Ellipsis, TinyTable } from '@manyun/dc-brain.legacy.components';
import {
  deleteMergedMonitorConfigActionCreator,
  getDataActionCreator,
  mergedMonitorConfigActions,
  setPaginationThenGetDataActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/mergedMonitorConfigActions';
import { generateEditSpecificMonitorConfigUrl } from '@manyun/dc-brain.legacy.utils/urls';

import { CONDITION_TEXT_MAP } from '../../../constants';
import TableActions from './table-actions';

export function DataTable({
  nomalizedDeviceCategory,
  data,
  total,
  pageNum,
  pageSize,
  selectedRowKeys,
  loading,
  getData,
  setPagination,
  setSelectedRowKeys,
  deleteMergedMonitorConfig,
}) {
  useEffect(() => {
    getData();
  }, [getData]);

  const paginationChangeHandler = useCallback(
    (current, size) => {
      setPagination({ pageNum: current, pageSize: size });
    },
    [setPagination]
  );

  function getTriggerPrerequisites({ condition, conditionValue, validLimits }) {
    if (!(Array.isArray(condition) && condition.length)) {
      return;
    }
    const isDi = condition[0] === 'EQUAL';

    let triggerPrerequisites;
    if (!isDi) {
      triggerPrerequisites = condition.map(condition => CONDITION_TEXT_MAP[condition]).join('，');
    }
    if (
      isDi &&
      Array.isArray(validLimits) &&
      validLimits.length &&
      Array.isArray(conditionValue) &&
      conditionValue.length
    ) {
      const validLimitsMap = validLimits.reduce((map, expression) => {
        const [key, value] = expression.split('=');
        map[key] = value;

        return map;
      }, {});
      triggerPrerequisites = conditionValue
        .map(conditionValue => validLimitsMap[conditionValue])
        .join('，');
    }

    return triggerPrerequisites;
  }

  return (
    <TinyTable
      rowKey="id"
      actions={<TableActions selectedRowKeys={selectedRowKeys} />}
      columns={getColumns(
        nomalizedDeviceCategory,
        deleteMergedMonitorConfig,
        getTriggerPrerequisites
      )}
      scroll={{ x: 'max-content' }}
      dataSource={data}
      loading={loading}
      rowSelection={{
        selectedRowKeys,
        onChange: keys => {
          setSelectedRowKeys(keys);
        },
      }}
      pagination={{
        total,
        current: pageNum,
        pageSize,
        onChange: paginationChangeHandler,
      }}
    />
  );
}

const mapStateToProps = ({
  common: { deviceCategory },
  mergedMonitorConfig: {
    data,
    total,
    pagination: { pageNum, pageSize },
    selectedRowKeys,
    loading,
  },
}) => ({
  nomalizedDeviceCategory: deviceCategory ? deviceCategory.normalizedList : {},
  data,
  total,
  loading,
  pageNum,
  pageSize,
  selectedRowKeys,
});
const mapDispatchToProps = {
  getData: getDataActionCreator,
  setPagination: setPaginationThenGetDataActionCreator,
  setSelectedRowKeys: mergedMonitorConfigActions.setSelectedRowKeys,
  deleteMergedMonitorConfig: deleteMergedMonitorConfigActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(DataTable);

const getColumns = (
  nomalizedDeviceCategory,
  deleteMergedMonitorConfig,
  getTriggerPrerequisites
) => [
  {
    title: '收敛配置名称',
    dataIndex: 'name',
    ellipsis: true,
    render(name, record) {
      return (
        <Link to={generateSpecificMergedMonitorConfig({ id: record.id })}>
          <Button type="link" compact>
            {name}
          </Button>
        </Link>
      );
    },
  },
  {
    title: '根因设备类型',
    dataIndex: 'deviceType',
    render(deviceType) {
      return get(nomalizedDeviceCategory, [deviceType, 'metaName'], deviceType);
    },
  },
  {
    title: '根因测点名称',
    dataIndex: 'pointName',
    render(name) {
      return (
        <Ellipsis lines={1} tooltip>
          {name}
        </Ellipsis>
      );
    },
  },
  {
    title: '触发条件',
    dataIndex: 'condition',
    render: (_, record) => {
      return getTriggerPrerequisites(record);
    },
  },
  {
    title: '包含规则数',
    dataIndex: 'pointCount',
  },
  {
    title: '收敛有效期',
    dataIndex: 'expire',
    render(expire) {
      if (Number(expire) === 0) {
        return '永久有效';
      }
      return `${Number(expire) / 60 /* `expire` is seconds */}分钟`;
    },
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    dataType: 'datetime',
  },
  {
    title: '更新时间',
    dataIndex: 'gmtModified',
    dataType: 'datetime',
  },
  {
    title: '更新人',
    dataIndex: 'lastOperatorName',
  },
  {
    title: '状态',
    dataIndex: 'available',
    render: available => (
      <Tag color={available ? 'green' : 'default'}>{available ? '启用' : '停用'}</Tag>
    ),
  },
  {
    title: '操作',
    dataIndex: 'id',
    fixed: 'right',
    render: (id, record) => (
      <span>
        <Link to={generateEditSpecificMonitorConfigUrl({ id })}>
          <Button type="link" compact>
            编辑
          </Button>
        </Link>
        <Divider type="vertical" />
        <DeleteConfirm
          variant="popconfirm"
          targetName={record.name}
          onOk={() => deleteMergedMonitorConfig(id)}
        >
          <Button type="link" compact>
            删除
          </Button>
        </DeleteConfirm>
      </span>
    ),
  },
];
