import React from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Button as BaseButton } from '@manyun/base-ui.ui.button';

import { batchUpdateMergedMonitorConfigsActionCreator } from '@manyun/dc-brain.legacy.redux/actions/mergedMonitorConfigActions';
import { generateNewMonitorConvergenceConfigUrl } from '@manyun/dc-brain.legacy.utils/urls';

export function TableActions({ selectedRowKeys, batchUpdateMergedMonitorConfigs }) {
  return [
    <Link key="new-config-link" to={generateNewMonitorConvergenceConfigUrl()}>
      <Button type="primary">新建配置</Button>
    </Link>,
    <BaseButton
      key="disable-button"
      type="success"
      disabled={!selectedRowKeys.length}
      onClick={() => {
        batchUpdateMergedMonitorConfigs({
          available: true,
          ids: selectedRowKeys,
        });
      }}
    >
      批量启用
    </BaseButton>,
    <Button
      key="enabled-button"
      type="danger"
      disabled={!selectedRowKeys.length}
      onClick={() => {
        batchUpdateMergedMonitorConfigs({
          available: false,
          ids: selectedRowKeys,
        });
      }}
    >
      批量停用
    </Button>,
  ];
}

const mapDispatchToProps = {
  batchUpdateMergedMonitorConfigs: batchUpdateMergedMonitorConfigsActionCreator,
};

export default connect(null, mapDispatchToProps)(TableActions);
