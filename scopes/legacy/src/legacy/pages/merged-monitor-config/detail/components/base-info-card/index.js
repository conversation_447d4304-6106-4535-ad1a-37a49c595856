import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { useParams } from 'react-router-dom';

import get from 'lodash/get';

import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Tag } from '@manyun/base-ui.ui.tag';

import { TinyCard } from '@manyun/dc-brain.legacy.components';
import { CONDITION_TEXT_MAP } from '@manyun/dc-brain.legacy.pages/merged-monitor-config/constants';
import { mergedMonitorConfigService } from '@manyun/dc-brain.legacy.services';

export function BaseInfoCard({ deviceTypeMap }) {
  const [baseInfo, setBaseInfo] = useState(null);

  const { id } = useParams();
  useEffect(() => {
    (async () => {
      const { response } = await mergedMonitorConfigService.fetchMergedMonitorConfigBaseInfo(id);
      if (response) {
        setBaseInfo(response);
      }
    })();
  }, [id]);

  function getTriggerPrerequisites() {
    const condition = get(baseInfo, 'condition');
    if (!(Array.isArray(condition) && condition.length)) {
      return;
    }

    const isDi = condition[0] === 'EQUAL';
    const conditionValue = get(baseInfo, 'conditionValue');
    const validLimits = get(baseInfo, 'validLimits');

    let triggerPrerequisites;
    if (!isDi) {
      triggerPrerequisites = condition.map(condition => CONDITION_TEXT_MAP[condition]).join('，');
    }
    if (
      isDi &&
      Array.isArray(validLimits) &&
      validLimits.length &&
      Array.isArray(conditionValue) &&
      conditionValue.length
    ) {
      const validLimitsMap = validLimits.reduce((map, expression) => {
        const [key, value] = expression.split('=');
        map[key] = value;

        return map;
      }, {});
      triggerPrerequisites = conditionValue
        .map(conditionValue => validLimitsMap[conditionValue])
        .join('，');
    }

    return triggerPrerequisites;
  }

  const expire = get(baseInfo, 'expire');
  const available = get(baseInfo, 'available');

  return (
    <TinyCard title="基本配置">
      <Descriptions column={5}>
        <Descriptions.Item label="配置名称">{get(baseInfo, 'name')}</Descriptions.Item>
        <Descriptions.Item label="根因设备类型">
          {get(deviceTypeMap, [get(baseInfo, 'deviceType'), 'metaName'])}
        </Descriptions.Item>
        <Descriptions.Item label="根因测点名称">{get(baseInfo, 'pointName')}</Descriptions.Item>
        <Descriptions.Item label="触发条件">{getTriggerPrerequisites()}</Descriptions.Item>
        <Descriptions.Item label="收敛有效期">
          {Number(expire) === 0 ? '永久生效' : `${Number(expire) / 60}分钟`}
        </Descriptions.Item>
        <Descriptions.Item label="创建时间">{get(baseInfo, 'gmtCreate')}</Descriptions.Item>
        <Descriptions.Item label="更新时间">{get(baseInfo, 'gmtModified')}</Descriptions.Item>
        <Descriptions.Item label="更新人">{get(baseInfo, 'lastOperatorName')}</Descriptions.Item>
        <Descriptions.Item label="是否启用">
          <Tag color={available ? 'green' : 'default'}>{available ? '启用' : '停用'}</Tag>
        </Descriptions.Item>
        <Descriptions.Item label="备注">{get(baseInfo, 'description')}</Descriptions.Item>
      </Descriptions>
    </TinyCard>
  );
}

const mapStateToProps = ({ common: { deviceCategory } }) => ({
  deviceTypeMap: get(deviceCategory, 'normalizedList'),
});

export default connect(mapStateToProps)(BaseInfoCard);
