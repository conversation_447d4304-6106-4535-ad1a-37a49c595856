import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { useParams } from 'react-router-dom';

import get from 'lodash/get';

import { Table } from '@manyun/base-ui.ui.table';

import { TinyCard, TinyTable } from '@manyun/dc-brain.legacy.components';
import { CONDITION_TEXT_MAP } from '@manyun/dc-brain.legacy.pages/merged-monitor-config/constants';
import { getPointTextByPointType } from '@manyun/dc-brain.legacy.pages/merged-monitor-config/new/utils';
import { mergedMonitorConfigService } from '@manyun/dc-brain.legacy.services';

export function ChildItemsCard({ deviceTypeMap }) {
  const [data, setData] = useState([]);
  const [childItemsMap, setChildItemsMap] = useState({});

  const { id } = useParams();
  useEffect(() => {
    (async () => {
      const { response } = await mergedMonitorConfigService.fetchMergedMonitorConfigDeviceTypesData(
        id
      );
      setData(response.data);
    })();
  }, [id]);

  const expandedRowRender = ({ deviceType }) => {
    const columns = [
      { align: 'center', title: '被收敛测点名称', dataIndex: 'pointName' },
      {
        align: 'center',
        title: '类型',
        dataIndex: ['pointType', 'code'],
        render(pointType) {
          return getPointTextByPointType(pointType);
        },
      },
      {
        align: 'center',
        title: '收敛逻辑',
        key: 'mergeLogic',
        render(__, { condition, conditionValue, validLimits }) {
          if (Array.isArray(conditionValue) && conditionValue.length) {
            const limitMap = validLimits.reduce((map, validLimit) => {
              const [key, text] = validLimit.split('=');
              map[key] = text;

              return map;
            }, {});
            return conditionValue.map(cv => limitMap[cv]).join(',');
          }
          // 因为当测点是 DI 类型时， condition 是 ['EQUAL']，但需要展示 conditionValues，
          // 所以把这个判断放在最后执行
          if (Array.isArray(condition) && condition.length) {
            return condition.map(c => CONDITION_TEXT_MAP[c]).join(',');
          }
        },
      },
    ];

    const data = childItemsMap[deviceType];

    return (
      <Table size="middle" rowKey="id" columns={columns} dataSource={data} pagination={false} />
    );
  };

  const columns = [
    {
      title: '被收敛设备类型',
      dataIndex: 'deviceType',
      render(deviceType) {
        return get(deviceTypeMap, [deviceType, 'metaName'], deviceType);
      },
    },
    { title: '收敛关系', dataIndex: ['mergeScope', 'name'] },
    { title: '被收敛测点数', dataIndex: 'mergePointCount' },
  ];

  return (
    <TinyCard title="收敛配置规则">
      <TinyTable
        rowKey="deviceType"
        columns={columns}
        expandedRowRender={expandedRowRender}
        dataSource={data}
        pagination={{ total: data.length }}
        onExpand={(expanded, { deviceType }) => {
          if (!expanded || childItemsMap[deviceType]) {
            return;
          }
          mergedMonitorConfigService
            .fetchMergedMonitorConfigChildItems({ id, deviceType })
            .then(({ response }) => {
              if (response) {
                setChildItemsMap(prev => ({
                  ...prev,
                  [deviceType]: response.data,
                }));
              }
            });
        }}
      />
    </TinyCard>
  );
}

const mapStateToProps = ({ common: { deviceCategory } }) => ({
  deviceTypeMap: get(deviceCategory, 'normalizedList'),
});

export default connect(mapStateToProps)(ChildItemsCard);
