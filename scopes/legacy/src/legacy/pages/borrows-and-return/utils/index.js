import moment from 'moment';

import { BORROW_TYPE_KEY_MAP } from '@manyun/dc-brain.legacy.pages/borrows-and-return/constants';

/***
 * 判断借用类型是否为外部借用
 * @param {String} type 借用类型
 * @return {Boolean}
 */
export function judgeBorrowTypeIsOut(type) {
  return type === BORROW_TYPE_KEY_MAP.OUT;
}

/**
 * 续借或者转借时，判断选择结束日期的disabled,续借结束日期要大于借用结束日期，大于今天;转借结束日期大于等于借用结束日期
 * @param {Number} minDate  借用结束日期的最小日期
 * @param {Object} endTime
 */
export function getRenewOrLendTimeDisabled(minDate, current, variant) {
  const minEndDate = moment(minDate).clone().endOf('day').valueOf();
  const today = moment().endOf('day').valueOf();
  const currentTime = current.endOf('day').valueOf();
  if (variant === 'lend') {
    return currentTime < minEndDate;
  }
  return current <= minEndDate || current < today;
}
