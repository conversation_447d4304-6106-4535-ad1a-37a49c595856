import React from 'react';
import { connect } from 'react-redux';

import QuestionCircleFilled from '@ant-design/icons/es/icons/QuestionCircleFilled';

import { Button } from '@manyun/base-ui.ui.button';
import { Modal } from '@manyun/base-ui.ui.modal';

import { cancelBorrowActionCreator } from '@manyun/dc-brain.legacy.redux/actions/borrowsAndReturnActions';

/**
 * 撤回弹窗
 * @param {object} props
 * @param {object} [props.buttonProps] Button 的props
 * @param {string} [props.borrowNo] 借用单号
 * @param {string} [props.type] 默认list,为取消借用归还
 * @param {string} [props.buttonText] button 显示的文案
 */
function CancleBorrowButtonModal({
  buttonProps = {
    type: 'link',
  },
  borrowNo,
  cancelBorrow,
  modalTitle = `确认要取消${borrowNo}的借用归还吗？`,
  modalContent = '取消后数据将不可恢复。',
  type,
  buttonText = '取消',
}) {
  return (
    <Button
      {...buttonProps}
      onClick={() => {
        Modal.confirm({
          icon: <QuestionCircleFilled />,
          title: modalTitle,
          content: modalContent,
          okText: '确认',
          cancelText: '取消',
          onOk: () => {
            if (type === 'list') {
              cancelBorrow({ borrowNo });
            }
          },
        });
      }}
    >
      {buttonText}
    </Button>
  );
}

const mapDispatchToProps = {
  cancelBorrow: cancelBorrowActionCreator,
};
export default connect(null, mapDispatchToProps)(CancleBorrowButtonModal);
