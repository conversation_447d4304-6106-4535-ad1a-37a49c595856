import React from 'react';
import { connect } from 'react-redux';

import ExclamationCircleFilled from '@ant-design/icons/es/icons/ExclamationCircleFilled';

import { Button } from '@manyun/base-ui.ui.button';
import { Modal } from '@manyun/base-ui.ui.modal';

import {
  renewRevertingActionCreator,
  revertBorrowActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/borrowsAndReturnActions';

/**
 * 撤回弹窗
 * @param {object} props
 * @param {object} [props.buttonProps] Button 的props
 * @param {string} [props.borrowNo] 借用单号
 * @param {string} [props.type] 弹窗类型 renew 表示 撤回续借申请，默认为撤回借用归还
 * @param {string} [props.opId] 转续借单号
 * @param {string} [props.buttonText] button 显示的文案
 */
function WithdrawButtonModal({
  buttonProps = {
    type: 'link',
  },
  borrowNo,
  revertBorrow,
  modalTitle = `确认要撤回吗？`,
  modalContent = '撤回后数据将不可恢复。',
  type,
  renewReverting,
  opId,
  buttonText = '撤回',
}) {
  return (
    <Button
      {...buttonProps}
      onClick={() => {
        Modal.confirm({
          icon: <ExclamationCircleFilled />,
          title: modalTitle,
          content: modalContent,
          okText: '确认',
          cancelText: '取消',
          onOk: () => {
            if (type === 'renew') {
              renewReverting({ borrowNo, opId });
              return;
            }
            revertBorrow({ borrowNo, type });
          },
        });
      }}
    >
      {buttonText}
    </Button>
  );
}

const mapDispatchToProps = {
  revertBorrow: revertBorrowActionCreator,
  renewReverting: renewRevertingActionCreator,
};
export default connect(null, mapDispatchToProps)(WithdrawButtonModal);
