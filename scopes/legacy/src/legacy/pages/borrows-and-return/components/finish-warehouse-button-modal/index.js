import React from 'react';
import { connect } from 'react-redux';

import ExclamationCircleFilled from '@ant-design/icons/es/icons/ExclamationCircleFilled';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';

import { borrowAllActionCreator } from '@manyun/dc-brain.legacy.redux/actions/borrowsAndReturnActions';
import { borrowsAndReturnService } from '@manyun/dc-brain.legacy.services';

/**
 * 完成出库弹窗
 * @param {object} props
 * @param {object} [props.buttonProps] Button 的props
 * @param {string} [props.borrowNo] 借用单号
 * @param {string} [props.type] type = 'detail' 表示详情中的完成出库弹窗
 */
function FinishWarehouse({
  buttonProps = {
    type: 'link',
  },
  borrowNo,
  borrowAll,
  type,
}) {
  const onFinishWarehouse = async () => {
    const { response, error } = await borrowsAndReturnService.fetchBorrowAllOut({ borrowNo });
    if (error) {
      message.error(error);
      return;
    }
    if (response) {
      borrowAll({ borrowNo, type });
      return;
    }
    if (error) {
      message.error(error);
      return;
    }
    Modal.confirm({
      icon: <ExclamationCircleFilled />,
      title: '还有资产未借出，确定继续吗？',
      content: '完成出库后数据将不可恢复。',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        borrowAll({ borrowNo, type });
      },
    });
  };

  return (
    <Button
      {...buttonProps}
      onClick={() => {
        onFinishWarehouse();
      }}
    >
      完成出库
    </Button>
  );
}

const mapDispatchToProps = {
  borrowAll: borrowAllActionCreator,
};
export default connect(null, mapDispatchToProps)(FinishWarehouse);
