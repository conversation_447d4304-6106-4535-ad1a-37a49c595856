import React, { useState } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import moment from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Input } from '@manyun/base-ui.ui.input';
import { Modal } from '@manyun/base-ui.ui.modal';

import { renewActionCreator } from '@manyun/dc-brain.legacy.redux/actions/borrowsAndReturnActions';

import { getRenewOrLendTimeDisabled } from '../../utils';

/**
 * 续借弹窗
 * @param {object} props
 * @param {object} [props.buttonProps] Button 的props
 * @param {string} [props.borrowNo] 借用单号
 * @param {string} [props.minDate] 最小日期
 * @param {string} [props.type] 续借弹窗类型，type = 'detail' 表示 详情中的续借弹窗
 * @param {string} [props.borrowContentMode] 借还内容，tab
 */
function RenewButtonModal({
  buttonProps = {
    type: 'link',
  },
  form: { getFieldDecorator, validateFields },
  borrowNo,
  renewing,
  minDate,
  buttonText = '续借',
  modalTitle = '续借',
  opId,
  type,
  borrowContentMode,
  initValues,
}) {
  const [visible, setVisible] = useState(false);

  return (
    <>
      <Button {...buttonProps} onClick={() => setVisible(value => !value)}>
        {buttonText}
      </Button>
      {visible && (
        <Modal
          title={modalTitle}
          visible={visible}
          width={480}
          onCancel={() => setVisible(false)}
          onOk={() => {
            validateFields(async (err, values) => {
              if (err) {
                return;
              }
              renewing({
                params: {
                  borrowNo,
                  endDate: Number(values.endDate.clone().endOf('day').unix() + '000'),
                  reason: values.reason.trim(),
                  opId,
                },
                type,
                successCb: () => {
                  setVisible(false);
                },
                borrowContentMode,
              });
            });
          }}
        >
          <Form colon={false} labelCol={{ xl: 6 }} wrapperCol={{ xl: 18 }}>
            <Form.Item label="续借结束日期">
              {getFieldDecorator('endDate', {
                validateFirst: true,
                rules: [
                  {
                    required: true,
                    message: '续借结束日期必选',
                  },
                  {
                    validator: (_, value, callback) => {
                      const tmp = getRenewOrLendTimeDisabled(minDate, value, 'renew');
                      if (tmp) {
                        callback('借用结束日期设置不正确！');
                        return;
                      }
                      callback();
                    },
                  },
                ],
                initialValue: initValues && initValues.endDate ? moment(initValues.endDate) : null,
              })(
                <DatePicker
                  allowClear
                  format="YYYY-MM-DD"
                  disabledDate={current => getRenewOrLendTimeDisabled(minDate, current, 'renew')}
                  style={{ width: 200 }}
                  defaultPickerValue={minDate ? moment(minDate) : moment()}
                />
              )}
            </Form.Item>
            <Form.Item label="原因">
              {getFieldDecorator('reason', {
                rules: [
                  {
                    required: true,
                    whitespace: true,
                    message: '原因必填！',
                  },
                  {
                    max: 120,
                    message: '最多输入 120 个字符！',
                  },
                ],
                initialValue: initValues?.reason,
              })(<Input.TextArea allowClear style={{ width: 200 }} />)}
            </Form.Item>
          </Form>
        </Modal>
      )}
    </>
  );
}

const mapDispatchToProps = {
  renewing: renewActionCreator,
};
export default connect(null, mapDispatchToProps)(Form.create()(RenewButtonModal));
