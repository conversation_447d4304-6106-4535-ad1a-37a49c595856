import React, { useState } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import { ApiSelect } from '@galiojs/awesome-antd';
import omit from 'lodash/omit';
import moment from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Input } from '@manyun/base-ui.ui.input';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Radio } from '@manyun/base-ui.ui.radio';

import { UserSelect } from '@manyun/dc-brain.legacy.components';
import {
  BORROWER_TYPE_OPTIONS,
  BORROW_TYPE_KEY_MAP,
  BORROW_TYPE_OPTIONS,
} from '@manyun/dc-brain.legacy.pages/borrows-and-return/constants';
import { lendActionCreator } from '@manyun/dc-brain.legacy.redux/actions/borrowsAndReturnActions';

import { getRenewOrLendTimeDisabled, judgeBorrowTypeIsOut } from '../../utils';

/**
 * 转借弹窗
 * @param {object} props
 * @param {object} [props.buttonProps] Button 的props
 * @param {string} [props.borrowNo] 借用单号
 * @param {string} [props.borrowContentMode] 借还内容，tab
 * @param {string} [props.type] 续借弹窗类型，type = 'detail' 表示 详情中的续借弹窗
 * @param {string} [props.minDate] 最小日期
 */
function LendButtonModal({
  buttonProps,
  form: { getFieldDecorator, getFieldValue, validateFields, setFieldsValue },
  borrowNo,
  lending,
  buttonText = '转借',
  modalTitle = '转借',
  type,
  borrowContentMode,
  minDate,
  initValues,
  opId,
}) {
  const [visible, setVisible] = useState(false);
  return (
    <>
      <Button {...buttonProps} onClick={() => setVisible(value => !value)}>
        {buttonText}
      </Button>
      {visible && (
        <Modal
          title={modalTitle}
          visible={visible}
          width={750}
          onCancel={() => setVisible(false)}
          onOk={() => {
            validateFields(async (err, values) => {
              if (err) {
                return;
              }
              let params = omit(values, 'borrower', 'personLiable', 'endDate');
              const endDate = values.endDate.clone().endOf('day');
              params.endDate = Number(endDate.unix() + '000');
              params.reason = values.reason.trim();
              if (values.borrowType === BORROW_TYPE_KEY_MAP.INNER) {
                params.borrower = values.borrower.id || values.borrower.key;
                params.borrowerName = values.borrower.userName || values.borrower.label;
              }
              if (judgeBorrowTypeIsOut(values.borrowType)) {
                params.personLiable = values.personLiable.id || values.borrower.key;
                params.personLiableName = values.personLiable.userName || values.borrower.label;
                params.borrower = '';
                params.borrowerName = values.borrower.label;
              }
              lending({
                params: { ...params, borrowNo, opId },
                type,
                successCb: () => {
                  setVisible(false);
                },
                borrowContentMode,
              });
            });
          }}
        >
          <Form colon={false} labelCol={{ xl: 4 }} wrapperCol={{ xl: 20 }}>
            <Form.Item label="借用类型">
              {getFieldDecorator('borrowType', {
                initialValue: initValues?.borrowType || BORROW_TYPE_KEY_MAP.INNER,
              })(
                <Radio.Group
                  onChange={({ target: { value } }) => {
                    if (value === BORROW_TYPE_KEY_MAP.INNER) {
                      const borrower = getFieldValue('borrower');
                      if (borrower && !borrower.id && borrower.label) {
                        setFieldsValue({ borrower: undefined });
                      }
                    }
                  }}
                >
                  {BORROW_TYPE_OPTIONS.map(({ label, value }) => {
                    return (
                      <Radio value={value} key={value}>
                        {label}
                      </Radio>
                    );
                  })}
                </Radio.Group>
              )}
            </Form.Item>
            {judgeBorrowTypeIsOut(getFieldValue('borrowType')) && (
              <Form.Item label="借用人类型">
                {getFieldDecorator('borrowerType', {
                  rules: [
                    {
                      required: true,
                      message: '借用人类型必选',
                    },
                  ],
                  initialValue: initValues?.borrowerType,
                })(
                  <ApiSelect
                    allowClear
                    showSearch
                    dataService={async () => Promise.resolve(BORROWER_TYPE_OPTIONS)}
                    style={{ width: 200 }}
                    trigger="onDidMount"
                  />
                )}
              </Form.Item>
            )}
            <Form.Item label="借用人">
              {getFieldDecorator('borrower', {
                rules: [
                  {
                    required: true,
                    message: '借用人必选',
                  },
                ],
                initialValue: initValues?.borrower
                  ? {
                      key: initValues?.borrower,
                      label: initValues?.borrowerName,
                    }
                  : undefined,
              })(
                <UserSelect
                  allowClear
                  reserveSearchValue={judgeBorrowTypeIsOut(getFieldValue('borrowType'))}
                  style={{ width: 200 }}
                />
              )}
            </Form.Item>
            {judgeBorrowTypeIsOut(getFieldValue('borrowType')) && (
              <Form.Item label="负责人">
                {getFieldDecorator('personLiable', {
                  rules: [
                    {
                      required: true,
                      message: '负责人必选',
                    },
                  ],
                  initialValue: initValues?.personLiable
                    ? {
                        key: initValues?.personLiable,
                        label: initValues?.personLiable ? initValues.personLiableName : '', // initValues.personLiable为0时，表示负责人为null
                      }
                    : undefined,
                })(<UserSelect allowClear style={{ width: 200 }} />)}
              </Form.Item>
            )}
            <Form.Item label="借用结束日期">
              {getFieldDecorator('endDate', {
                validateFirst: true,
                rules: [
                  {
                    required: true,
                    message: '借用结束日期必选',
                  },
                  {
                    validator: (_, value, callback) => {
                      const tmp = getRenewOrLendTimeDisabled(minDate, value, 'lend');
                      if (tmp) {
                        callback('借用结束日期设置不正确！');
                        return;
                      }
                      callback();
                    },
                  },
                ],
                initialValue: initValues && initValues.endDate ? moment(initValues.endDate) : null,
              })(
                <DatePicker
                  allowClear
                  format="YYYY-MM-DD"
                  disabledDate={current => getRenewOrLendTimeDisabled(minDate, current, 'lend')}
                  style={{ width: 200 }}
                />
              )}
            </Form.Item>
            <Form.Item label="原因">
              {getFieldDecorator('reason', {
                rules: [
                  {
                    required: true,
                    whitespace: true,
                    message: '原因必填',
                  },
                  {
                    max: 120,
                    message: '最多输入 120 个字符！',
                  },
                ],
                initialValue: initValues?.reason,
              })(<Input.TextArea allowClear style={{ width: 300 }} />)}
            </Form.Item>
          </Form>
        </Modal>
      )}
    </>
  );
}

const mapDispatchToProps = {
  lending: lendActionCreator,
};
export default connect(null, mapDispatchToProps)(Form.create()(LendButtonModal));
