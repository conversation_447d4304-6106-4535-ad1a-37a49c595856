import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import { ApiSelect } from '@galiojs/awesome-antd';

import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';

import { TinyCard, TinyTable } from '@manyun/dc-brain.legacy.components';
import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';
import { returningActionCreator } from '@manyun/dc-brain.legacy.redux/actions/borrowsAndReturnActions';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import { borrowsAndReturnService, roomManageService } from '@manyun/dc-brain.legacy.services';

/**
 * 归还弹窗
 * @param {object} props
 * @param {object} [props.buttonProps] Button 的props
 * @param {string} [props.borrowNo] 借用单号
 * @param {string} [props.type] 弹出类型  type = detail 表示详情中的弹窗
 * @param {string} [props.borrowContentMode] 借还内容，tab
 */
function ReturnButtonModal({
  buttonProps = {
    type: 'link',
    compact: false,
  },
  form: { getFieldDecorator, validateFields },
  borrowNo,
  returning,
  borrower,
  deviceNormalizedList,
  syncCommonData,
  info = {},
  type,
  borrowContentMode,
}) {
  const [visible, setVisible] = useState(false);
  const [enableAsset, setEnableAsset] = useState([]);
  const [selectedRows, setSelectedRows] = useState([]);
  const [idcTag, blockTag] = info.blockGuid ? info.blockGuid.split('.') : [];
  const [returnAssetCountsMap, setAeturnAssetCountsMap] = useState({});

  useEffect(() => {
    syncCommonData({
      strategy: { deviceCategory: 'IF_NULL' },
    });
  }, [syncCommonData]);

  useEffect(() => {
    if (!visible) {
      return;
    }
    (async () => {
      const { response, error } = await borrowsAndReturnService.fetchEnableReturn({
        borrowNo,
      });
      if (error) {
        message.error(error);
        return;
      }
      setEnableAsset(response.data);
    })();
  }, [visible, borrowNo]);

  return (
    <>
      <Button {...buttonProps} onClick={() => setVisible(value => !value)}>
        归还
      </Button>
      {visible && (
        <Modal
          title="归还"
          visible={visible}
          width={1000}
          onCancel={() => setVisible(false)}
          onOk={() => {
            validateFields(async (err, values) => {
              if (err) {
                return;
              }
              if (!selectedRows.length) {
                message.error('还未选择要归还的资产！');
                return;
              }
              // 查找是否有选择的资产未填写归还数量
              const checkedAssetNoNum = selectedRows.find(
                ({ id, numbered }) => !returnAssetCountsMap[id] && !numbered
              );
              if (checkedAssetNoNum) {
                return;
              }
              const params = {
                borrowNo: borrowNo,
                returnRoomGuid: values.returnRoomGuid.key,
                returnRoomTag: values.returnRoomGuid.label,
                returnAssertInfoList: selectedRows.map(item => ({
                  ...item,
                  deviceTypeName: deviceNormalizedList[item.deviceType]?.metaName,
                  returnNum: !item.numbered ? returnAssetCountsMap[item.id] : 1,
                })),
                remark: values.remark?.trim(),
              };
              returning({
                params,
                type,
                successCb: () => {
                  setVisible(false);
                },
                borrowContentMode,
              });
            });
          }}
        >
          <Form colon={false} labelCol={{ xl: 2 }} wrapperCol={{ xl: 22 }}>
            <Form.Item label="归还人">
              {getFieldDecorator('borrower', {
                initialValue: borrower,
              })(<Input disabled={true} style={{ width: 200 }} />)}
            </Form.Item>
            <Form.Item label="归还至">
              {getFieldDecorator('returnRoomGuid', {
                rules: [
                  {
                    required: true,
                    message: '归还至必选',
                  },
                ],
              })(
                <ApiSelect
                  fieldNames={{ value: 'guid', label: 'tag' }}
                  allowClear
                  trigger="onDidMount"
                  labelInValue
                  style={{ width: 200 }}
                  dataService={async () => {
                    const { response } = await roomManageService.fetchRoomPage({
                      pageNum: 1,
                      pageSize: 200,
                      idcTag,
                      blockTag,
                      roomType: 'WAREHOUSE',
                    });
                    if (response) {
                      return response.data;
                    }
                  }}
                />
              )}
            </Form.Item>
            <Form.Item label="备注">
              {getFieldDecorator('remark', {
                rules: [
                  {
                    max: 120,
                    message: '最多输入 120 个字符！',
                  },
                ],
              })(<Input.TextArea style={{ width: 300 }} />)}
            </Form.Item>
          </Form>
          <TinyCard title="待还资产">
            <TinyTable
              rowKey="id"
              dataSource={enableAsset}
              columns={getColumns({
                deviceNormalizedList,
                onChangeInputNumber: value =>
                  setAeturnAssetCountsMap(maps => ({ ...maps, ...value })),
                returnAssetCountsMap,
                selectedRows,
              })}
              rowSelection={{
                selectedRowKeys: selectedRows.map(({ id }) => id),
                selectedRows: selectedRows,
                onChange: (keys, rows) => {
                  setSelectedRows(rows.map(item => ({ ...item })));
                },
              }}
            />
          </TinyCard>
        </Modal>
      )}
    </>
  );
}
const mapStateToProps = ({ common: { deviceCategory } }) => {
  return {
    deviceNormalizedList: deviceCategory ? deviceCategory.normalizedList : {},
  };
};
const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
  returning: returningActionCreator,
};
export default connect(mapStateToProps, mapDispatchToProps)(Form.create()(ReturnButtonModal));

const getColumns = ({
  deviceNormalizedList,
  onChangeInputNumber,
  returnAssetCountsMap,
  selectedRows,
}) => [
  {
    title: '资产ID',
    dataIndex: 'assetNo',
    render: assetNo => assetNo || BLANK_PLACEHOLDER,
  },
  {
    title: '三级分类',
    dataIndex: 'deviceType',
    render: txt => (deviceNormalizedList[txt] ? deviceNormalizedList[txt].metaName : txt),
  },
  {
    title: '厂商',
    dataIndex: 'vendor',
  },
  {
    title: '型号',
    dataIndex: 'productModel',
  },
  {
    title: '待还数量',
    dataIndex: 'waitReturnNum',
    render: (_, { borrowNum, returnNum }) => Number(borrowNum) - Number(returnNum),
  },
  {
    title: '归还数量',
    dataIndex: 'returnNum',
    render: (txt, record) => {
      if (!record.numbered) {
        const checked = selectedRows.find(({ id }) => id === record.id);
        const validateStatus = !checked
          ? 'success'
          : !returnAssetCountsMap[record.id]
          ? 'error'
          : 'success';
        const help = !checked
          ? undefined
          : !returnAssetCountsMap[record.id]
          ? '归还数量必填！'
          : undefined;

        let max;
        let min = 1;
        if (!record.borrowNum) {
          max = 0;
          min = 0;
        } else if (!record.returnNum) {
          max = record.borrowNum;
        } else {
          max = Number(record.borrowNum) - Number(record.returnNum);
        }

        return (
          <Form.Item validateStatus={validateStatus} help={help} style={{ marginBottom: 0 }}>
            <InputNumber
              min={min}
              max={max}
              value={returnAssetCountsMap[record.id]}
              onChange={value => {
                if (value > max) {
                  message.error('不可大于待还数量');
                  return;
                }
                onChangeInputNumber({ [record.id]: value });
              }}
              precision={0}
            />
          </Form.Item>
        );
      }
      return 1;
    },
  },
];
