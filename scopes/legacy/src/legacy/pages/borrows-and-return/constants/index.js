import { STATUS_MAP } from '@manyun/dc-brain.legacy.constants/statusColor';

/**
 * 状态
 */
export const BORROW_RETURN_STATUS_TYPE_KEY_MAP = {
  WAIT_STOCK_OUT: 'WAIT_STOCK_OUT',
  BORROWING: 'BORROWING',
  RETURN: 'RETURN',
  DRAFT: 'DRAFT',
  APPROVING: 'APPROVING',
  CANCEL: 'CANCELED',
};

export const BORROW_RETURN_STATUS_TYPE_TEXT_MAP = {
  [BORROW_RETURN_STATUS_TYPE_KEY_MAP.WAIT_STOCK_OUT]: '待出库',
  [BORROW_RETURN_STATUS_TYPE_KEY_MAP.BORROWING]: '已借用',
  [BORROW_RETURN_STATUS_TYPE_KEY_MAP.RETURN]: '已归还',
  [BORROW_RETURN_STATUS_TYPE_KEY_MAP.DRAFT]: '草稿',
  [BORROW_RETURN_STATUS_TYPE_KEY_MAP.APPROVING]: '待审批',
  [BORROW_RETURN_STATUS_TYPE_KEY_MAP.CANCEL]: '已取消',
};

export const BORROW_RETURN_STATUS_TYPE_OPTIONS = [
  {
    value: BORROW_RETURN_STATUS_TYPE_KEY_MAP.WAIT_STOCK_OUT,
    label: BORROW_RETURN_STATUS_TYPE_TEXT_MAP[BORROW_RETURN_STATUS_TYPE_KEY_MAP.WAIT_STOCK_OUT],
  },
  {
    value: BORROW_RETURN_STATUS_TYPE_KEY_MAP.BORROWING,
    label: BORROW_RETURN_STATUS_TYPE_TEXT_MAP[BORROW_RETURN_STATUS_TYPE_KEY_MAP.BORROWING],
  },
  {
    value: BORROW_RETURN_STATUS_TYPE_KEY_MAP.RETURN,
    label: BORROW_RETURN_STATUS_TYPE_TEXT_MAP[BORROW_RETURN_STATUS_TYPE_KEY_MAP.RETURN],
  },
  {
    value: BORROW_RETURN_STATUS_TYPE_KEY_MAP.DRAFT,
    label: BORROW_RETURN_STATUS_TYPE_TEXT_MAP[BORROW_RETURN_STATUS_TYPE_KEY_MAP.DRAFT],
  },
  {
    value: BORROW_RETURN_STATUS_TYPE_KEY_MAP.APPROVING,
    label: BORROW_RETURN_STATUS_TYPE_TEXT_MAP[BORROW_RETURN_STATUS_TYPE_KEY_MAP.APPROVING],
  },
  {
    value: BORROW_RETURN_STATUS_TYPE_KEY_MAP.CANCEL,
    label: BORROW_RETURN_STATUS_TYPE_TEXT_MAP[BORROW_RETURN_STATUS_TYPE_KEY_MAP.CANCEL],
  },
];

/**
 * 借用人类型
 * 合作伙伴、客户、客户供应商、其他
 */
export const BORROWER_TYPE_KEY_MAP = {
  PARTNER: 'PARTNER',
  CUSTOMER: 'CUSTOMER',
  SUPPLIER: 'SUPPLIER',
  OTHER: 'OTHER',
};

export const BORROWER_TYPE_TEXT_MAP = {
  [BORROWER_TYPE_KEY_MAP.PARTNER]: '合作伙伴',
  [BORROWER_TYPE_KEY_MAP.CUSTOMER]: '客户',
  [BORROWER_TYPE_KEY_MAP.SUPPLIER]: '客户供应商',
  [BORROWER_TYPE_KEY_MAP.OTHER]: '其他',
};

export const BORROWER_TYPE_OPTIONS = [
  {
    value: BORROWER_TYPE_KEY_MAP.PARTNER,
    label: BORROWER_TYPE_TEXT_MAP[BORROWER_TYPE_KEY_MAP.PARTNER],
  },
  {
    value: BORROWER_TYPE_KEY_MAP.CUSTOMER,
    label: BORROWER_TYPE_TEXT_MAP[BORROWER_TYPE_KEY_MAP.CUSTOMER],
  },
  {
    value: BORROWER_TYPE_KEY_MAP.SUPPLIER,
    label: BORROWER_TYPE_TEXT_MAP[BORROWER_TYPE_KEY_MAP.SUPPLIER],
  },
  {
    value: BORROWER_TYPE_KEY_MAP.OTHER,
    label: BORROWER_TYPE_TEXT_MAP[BORROWER_TYPE_KEY_MAP.OTHER],
  },
];

/**
 * 借用类型
 */
export const BORROW_TYPE_KEY_MAP = {
  INNER: 'INNER',
  OUT: 'OUT',
};

export const BORROW_TYPE_TEXT_MAP = {
  [BORROW_TYPE_KEY_MAP.INNER]: '内部员工',
  [BORROW_TYPE_KEY_MAP.OUT]: '外部人员',
};

export const BORROW_TYPE_OPTIONS = [
  {
    value: BORROW_TYPE_KEY_MAP.INNER,
    label: BORROW_TYPE_TEXT_MAP[BORROW_TYPE_KEY_MAP.INNER],
  },
  {
    value: BORROW_TYPE_KEY_MAP.OUT,
    label: BORROW_TYPE_TEXT_MAP[BORROW_TYPE_KEY_MAP.OUT],
  },
];

export const RENEW_OR_TRANSFER_TYPE_KEY_MAP = {
  RENEW: 'RENEW', // 续借
  TRANSFER: 'TRANSFER', // 转借
};

// 关联单号类型
export const RELATE_BIZ_TYPE_KEY_MAP = {
  BORROW: 'BORROW', // 转借
  REPAIR: 'REPAIR', // 维修
};

export const RELATE_BIZ_TYPE_TEXT_MAP = {
  [RELATE_BIZ_TYPE_KEY_MAP.BORROW]: '借用归还',
  [RELATE_BIZ_TYPE_KEY_MAP.REPAIR]: '维修工单',
};

export const RELATE_BIZ_TYPE_OPTIONS = [
  {
    value: RELATE_BIZ_TYPE_KEY_MAP.BORROW,
    label: RELATE_BIZ_TYPE_TEXT_MAP[RELATE_BIZ_TYPE_KEY_MAP.BORROW],
  },
  {
    value: RELATE_BIZ_TYPE_KEY_MAP.REPAIR,
    label: RELATE_BIZ_TYPE_TEXT_MAP[RELATE_BIZ_TYPE_KEY_MAP.REPAIR],
  },
];

// 出入库工单子类型
export const TASK_SUBTYPE_TYPE_KEY_MAP = {
  EX_WAREHOUSE: 'EX_WAREHOUSE', // 出库
  IN_WAREHOUSE: 'IN_WAREHOUSE', // 入库
};

// 续转记录状态
export const RENEW_LEND_RECORD_TYPE_KEY_MAP = {
  APPROVING: 'APPROVING',
  PASS: 'PASS',
  REFUSE: 'REFUSE',
  REVOKE: 'REVOKE',
};

export const RENEW_LEND_RECORD_TYPE_TEXT_MAP = {
  [RENEW_LEND_RECORD_TYPE_KEY_MAP.APPROVING]: '待审批',
  [RENEW_LEND_RECORD_TYPE_KEY_MAP.PASS]: '已通过',
  [RENEW_LEND_RECORD_TYPE_KEY_MAP.REFUSE]: '已驳回',
  [RENEW_LEND_RECORD_TYPE_KEY_MAP.REVOKE]: '草稿',
};

export const RENEW_LEND_RECORD_MAP = {
  [RENEW_LEND_RECORD_TYPE_KEY_MAP.APPROVING]: {
    value: RENEW_LEND_RECORD_TYPE_KEY_MAP.APPROVING,
    label: RENEW_LEND_RECORD_TYPE_TEXT_MAP[RENEW_LEND_RECORD_TYPE_KEY_MAP.APPROVING],
    status: STATUS_MAP.WARNING,
  },
  [RENEW_LEND_RECORD_TYPE_KEY_MAP.PASS]: {
    value: RENEW_LEND_RECORD_TYPE_KEY_MAP.PASS,
    label: RENEW_LEND_RECORD_TYPE_TEXT_MAP[RENEW_LEND_RECORD_TYPE_KEY_MAP.PASS],
    status: STATUS_MAP.NORMAL,
  },
  [RENEW_LEND_RECORD_TYPE_KEY_MAP.REFUSE]: {
    value: RENEW_LEND_RECORD_TYPE_KEY_MAP.REFUSE,
    label: RENEW_LEND_RECORD_TYPE_TEXT_MAP[RENEW_LEND_RECORD_TYPE_KEY_MAP.REFUSE],
    status: STATUS_MAP.ALARM,
  },
  [RENEW_LEND_RECORD_TYPE_KEY_MAP.REVOKE]: {
    value: RENEW_LEND_RECORD_TYPE_KEY_MAP.REVOKE,
    label: RENEW_LEND_RECORD_TYPE_TEXT_MAP[RENEW_LEND_RECORD_TYPE_KEY_MAP.REVOKE],
    status: STATUS_MAP.DISABLED,
  },
};

// 借还内容 tab
export const BORROW_CONTENT_MODE_KEY_MAP = {
  borrowApply: 'borrowApply',
  loanReturnList: 'loanReturnList',
  returnRecord: 'returnRecord',
  continuationRecord: 'continuationRecord',
  ticketRecord: 'ticketRecord',
};

export const BORROW_CONTENT_MODE_TEXT_MAP = {
  [BORROW_CONTENT_MODE_KEY_MAP.borrowApply]: '借用申请',
  [BORROW_CONTENT_MODE_KEY_MAP.loanReturnList]: '借还清单',
  [BORROW_CONTENT_MODE_KEY_MAP.returnRecord]: '归还记录',
  [BORROW_CONTENT_MODE_KEY_MAP.continuationRecord]: '续转记录',
  [BORROW_CONTENT_MODE_KEY_MAP.ticketRecord]: '工单记录',
};

export const BORROW_CONTENT_MODE_OPTIONS = [
  {
    value: BORROW_CONTENT_MODE_KEY_MAP.borrowApply,
    label: BORROW_CONTENT_MODE_TEXT_MAP[BORROW_CONTENT_MODE_KEY_MAP.borrowApply],
  },
  {
    value: BORROW_CONTENT_MODE_KEY_MAP.loanReturnList,
    label: BORROW_CONTENT_MODE_TEXT_MAP[BORROW_CONTENT_MODE_KEY_MAP.loanReturnList],
  },
  {
    value: BORROW_CONTENT_MODE_KEY_MAP.returnRecord,
    label: BORROW_CONTENT_MODE_TEXT_MAP[BORROW_CONTENT_MODE_KEY_MAP.returnRecord],
  },
  {
    value: BORROW_CONTENT_MODE_KEY_MAP.continuationRecord,
    label: BORROW_CONTENT_MODE_TEXT_MAP[BORROW_CONTENT_MODE_KEY_MAP.continuationRecord],
  },
  {
    value: BORROW_CONTENT_MODE_KEY_MAP.ticketRecord,
    label: BORROW_CONTENT_MODE_TEXT_MAP[BORROW_CONTENT_MODE_KEY_MAP.ticketRecord],
  },
];
