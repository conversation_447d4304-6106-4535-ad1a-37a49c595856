import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import ExclamationCircleFilled from '@ant-design/icons/es/icons/ExclamationCircleFilled';
import shortid from 'shortid';

import { Button } from '@manyun/base-ui.ui.button';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';

import { VendorModelSelect } from '@manyun/crm.ui.vendor-model-select';
import { RoomTypes } from '@manyun/resource-hub.model.room';
import { RoomSelect } from '@manyun/resource-hub.ui.room-select';

import {
  AssetClassificationApiTreeSelect,
  GutterWrapper,
} from '@manyun/dc-brain.legacy.components';
import { deviceService, spareService } from '@manyun/dc-brain.legacy.services';
import { getSpaceGuidMap } from '@manyun/dc-brain.legacy.utils';

function AddBorrowButttonModal({
  form: { getFieldDecorator, validateFields, getFieldValue, setFieldsValue },
  blockGuid,
  setAssetInfo,
  deviceNormalizedList,
}) {
  const [visible, setVisible] = useState(false);
  const [count, setCount] = useState(0);

  const assetType = getFieldValue('assetType');
  const vendorModel = getFieldValue('vendorModel');
  const targetRoom = getFieldValue('targetRoom');

  useEffect(() => {
    setCount(0);
    if (!assetType || !assetType.thirdCategorycode || !vendorModel || vendorModel.length !== 2) {
      return;
    }

    if (!assetType.numbered) {
      (async () => {
        const { response, error } = await spareService.fetchUsableNum({
          spareType: assetType.thirdCategorycode,
          locationGuid: blockGuid,
          vendor: vendorModel[0],
          productModel: vendorModel[1],
          dimension: 'BLOCK',
        });
        if (error) {
          message.error(error);
          return;
        }
        setCount(response);
      })();
    }
  }, [assetType, vendorModel, blockGuid]);

  useEffect(() => {
    // 每次请求数量时，先初始化数量，避免数量接口失败，数量显示为上一次api返回的数量
    setCount(0);
    if (
      !assetType ||
      !assetType.thirdCategorycode ||
      !targetRoom ||
      !targetRoom.key ||
      !vendorModel ||
      vendorModel.length !== 2
    ) {
      return;
    }

    if (assetType.numbered) {
      (async () => {
        const { idc, block } = getSpaceGuidMap(targetRoom.key);
        const { error, response } = await deviceService.fetchDeviceNum({
          idcTag: idc,
          blockTag: block,
          deviceType: assetType.thirdCategorycode,
          vendor: vendorModel[0],
          productModel: vendorModel[1],
          roomTypeList: [RoomTypes.Warehouse],
          filterRoomGuid: targetRoom.key,
        });
        if (error) {
          message.error(error);
          return;
        }
        setCount(response);
      })();
    }
  }, [assetType, targetRoom, vendorModel]);

  return (
    <>
      <Button
        type="primary"
        disabled={!blockGuid}
        onClick={() => {
          setVisible(value => !value);
        }}
      >
        添加
      </Button>
      {visible && (
        <Modal
          title="添加"
          visible={visible}
          width={640}
          onCancel={() => setVisible(false)}
          onOk={() => {
            validateFields(async (err, values) => {
              if (err) {
                return;
              }
              if (count === 0) {
                Modal.confirm({
                  title: '以下资产当前库存为0，确定继续申请？',
                  content: `${deviceNormalizedList[values.assetType.thirdCategorycode].metaName}`,
                  icon: <ExclamationCircleFilled />,
                  okText: '确认',
                  cancelText: '取消',
                  onOk() {
                    const row = {
                      topCategory: values.assetType.firstCategoryCode,
                      secondCategory: values.assetType.secondCategoryCode,
                      deviceType: values.assetType.thirdCategorycode,
                      vendor: values.vendorModel[0],
                      productModel: values.vendorModel[1],
                      targetRoomGuid: values.targetRoom?.key,
                      targetRoomTag: values.targetRoom?.label,
                      applyNum: values.applyNum,
                      numbered: values.assetType.numbered,
                      id: shortid(),
                    };
                    setAssetInfo(row);
                    setVisible(false);
                  },
                });
                return;
              }
              const row = {
                topCategory: values.assetType.firstCategoryCode,
                secondCategory: values.assetType.secondCategoryCode,
                deviceType: values.assetType.thirdCategorycode,
                vendor: values.vendorModel[0],
                productModel: values.vendorModel[1],
                targetRoomGuid: values.targetRoom?.key,
                targetRoomTag: values.targetRoom?.label,
                applyNum: values.applyNum,
                numbered: values.assetType.numbered,
                id: shortid(),
              };
              setAssetInfo(row);
              setVisible(false);
            });
          }}
        >
          <Form colon={false} labelCol={{ xl: 6 }} wrapperCol={{ xl: 18 }}>
            <Form.Item label="资产分类">
              {getFieldDecorator('assetType', {
                rules: [
                  {
                    required: true,
                    message: '资产分类必选',
                  },
                ],
              })(
                <AssetClassificationApiTreeSelect
                  dataType={['noSnDevice', 'snDevice']}
                  category="allCategoryCode"
                  disabledDepths={[0, 1]}
                  requestOnDidMount
                  allowClear
                  onChange={() => {
                    setFieldsValue({ vendorModel: undefined });
                  }}
                  style={{ width: 200 }}
                />
              )}
            </Form.Item>
            <Form.Item label="厂商、型号">
              {getFieldDecorator('vendorModel', {
                rules: [
                  {
                    required: true,
                    message: '厂商、型号必选',
                    type: 'number',
                    transform: value => (value?.length === 2 ? 2 : false),
                  },
                ],
              })(
                <VendorModelSelect
                  style={{ width: 200 }}
                  deviceType={getFieldValue('assetType')?.thirdCategorycode}
                  allowClear
                  numbered={getFieldValue('assetType')?.numbered}
                />
              )}
            </Form.Item>
            {getFieldValue('assetType')?.numbered && (
              <Form.Item label="借用至包间">
                {getFieldDecorator('targetRoom', {
                  rules: [
                    {
                      required: true,
                      message: '借用至包间必选',
                    },
                  ],
                })(
                  <RoomSelect
                    allowClear
                    showSearch
                    labelInValue
                    blockGuid={blockGuid}
                    style={{ width: 200 }}
                  />
                )}
              </Form.Item>
            )}
            <Form.Item label="申请数量">
              <GutterWrapper>
                {getFieldDecorator('applyNum', {
                  rules: [
                    {
                      required: true,
                      message: '申请数量必填',
                    },
                    {
                      type: 'number',
                      min: 1,
                      message: '申请数量必填最小值为1',
                    },
                  ],
                })(
                  <InputNumber min={Number(count) > 0 ? 1 : 0} max={Number(count)} precision={0} />
                )}
                <span>库存数量：{count}</span>
              </GutterWrapper>
            </Form.Item>
          </Form>
        </Modal>
      )}
    </>
  );
}
const mapStateToProps = ({ common: { deviceCategory } }, { blockGuid }) => {
  return {
    deviceNormalizedList: deviceCategory ? deviceCategory.normalizedList : {},
  };
};
export default connect(mapStateToProps)(Form.create()(AddBorrowButttonModal));
