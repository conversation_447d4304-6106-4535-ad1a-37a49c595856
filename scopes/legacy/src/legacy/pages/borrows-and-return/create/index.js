import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import { ApiSelect } from '@galiojs/awesome-antd';
import cloneDeep from 'lodash/cloneDeep';
import omit from 'lodash/omit';
import moment from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Radio } from '@manyun/base-ui.ui.radio';

import {
  FooterToolBar,
  GutterWrapper,
  LocationCascader,
  TinyCard,
  TinyTable,
  UserSelect,
} from '@manyun/dc-brain.legacy.components';
import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';
import {
  BORROWER_TYPE_OPTIONS,
  BORRO<PERSON>_TYPE_KEY_MAP,
  BORROW_TYPE_OPTIONS,
} from '@manyun/dc-brain.legacy.pages/borrows-and-return/constants';
import {
  borrowReSubmitActionCreator,
  createBorrowActionCreator,
  getBorrowApplyActionCreator,
  getBorrowInfoActionCreator,
  queryBorrowAndReturnAssertInfoActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/borrowsAndReturnActions';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';

import { judgeBorrowTypeIsOut } from '../utils';
import AddBorrowButttonModal from './components/addBorrowButtonModal';

function Create({
  form: { getFieldDecorator, getFieldValue, validateFields, setFieldsValue },
  syncCommonData,
  mode,
  createBorrow,
  history,
  deviceTypeMapping,
  match,
  getBorrowInfo,
  info,
  borrowReSubmit,
  queryBorrowAndReturnAssertInfo,
  getBorrowApply,
}) {
  const [assetData, setAssetData] = useState([]);
  const borrowNo = match?.params.id;

  useEffect(() => {
    syncCommonData({
      strategy: { space: 'IF_NULL', deviceCategory: 'IF_NULL' },
    });
  }, [syncCommonData]);

  useEffect(() => {
    if (!borrowNo) {
      return;
    }
    getBorrowInfo({ borrowNo });
  }, [borrowNo, getBorrowInfo]);

  useEffect(() => {
    if (mode === 'create' || !borrowNo) {
      return;
    }
    queryBorrowAndReturnAssertInfo({ borrowNo });
  }, [mode, borrowNo, queryBorrowAndReturnAssertInfo]);

  useEffect(() => {
    if (mode === 'edit' && borrowNo) {
      getBorrowApply({
        borrowNo,
        successCb: data => {
          setAssetData(data);
        },
      });
    }
  }, [mode, borrowNo, getBorrowApply]);

  const onSubmit = () => {
    validateFields(async (err, values) => {
      if (err) {
        return;
      }
      if (!assetData.length) {
        message.error('借用信息必选！');
        return;
      }
      const params = getBaseQ(values);
      if (mode === 'create') {
        createBorrow(params);
      } else {
        borrowReSubmit({ ...params, borrowNo });
      }
    });
  };

  const getBaseQ = values => {
    const params = omit(values, 'borrower', 'borrowingDate', 'location');
    params.idcTag = values.location[0];
    params.blockGuid = values.location.join('.');
    const startDate = values.borrowingDate[0].endOf('day').clone();
    const endDate = values.borrowingDate[1].endOf('day').clone();
    params.borrowStartDate = Number(startDate.unix() + '000');
    params.borrowEndDate = Number(endDate.unix() + '000');
    params.borrowAssetInfoList = assetData;
    params.borrowerName = values.borrower.label;
    if (values.borrowType === BORROW_TYPE_KEY_MAP.INNER) {
      params.borrower = values.borrower.key;
    }
    if (values.borrowType === BORROW_TYPE_KEY_MAP.OUT) {
      params.personLiable = values.personLiable.key;
      params.personLiableName = values.personLiable.label;
    }
    return params;
  };

  return (
    <GutterWrapper mode="vertical">
      <TinyCard title="基本信息">
        <Form labelCol={{ xl: 2 }} wrapperCol={{ xl: 22 }} colon={false}>
          <Form.Item label="标题">
            {getFieldDecorator('title', {
              rules: [
                {
                  required: true,
                  whitespace: true,
                  message: '标题必填！',
                },
                {
                  max: 20,
                  message: '最多输入 20 个字符！',
                },
              ],
              initialValue: info.title,
            })(<Input allowClear style={{ width: 468 }} />)}
          </Form.Item>

          <Form.Item label="位置">
            {getFieldDecorator('location', {
              rules: [
                {
                  required: true,
                  message: '位置必选！',
                },
              ],
              initialValue: info.blockGuid ? info.blockGuid.split('.') : [],
            })(
              <LocationCascader
                style={{ width: 200 }}
                changeOnSelect={false}
                onChange={() => {
                  setAssetData([]);
                }}
                authorizedOnly
              />
            )}
          </Form.Item>

          <Form.Item label="借用类型">
            {getFieldDecorator('borrowType', {
              initialValue:
                info.borrowType === undefined ? BORROW_TYPE_KEY_MAP.INNER : info.borrowType,
            })(
              <Radio.Group
                onChange={() => {
                  setFieldsValue({ borrower: { key: '', label: '' } });
                }}
              >
                {BORROW_TYPE_OPTIONS.map(({ value, label }) => {
                  return (
                    <Radio value={value} key={value}>
                      {label}
                    </Radio>
                  );
                })}
              </Radio.Group>
            )}
          </Form.Item>

          {judgeBorrowTypeIsOut(getFieldValue('borrowType')) && (
            <Form.Item label="借用人类型">
              {getFieldDecorator('borrowerType', {
                rules: [
                  {
                    required: true,
                    message: '借用人类型必选!',
                  },
                ],
                initialValue: info.borrowerType,
              })(
                <ApiSelect
                  showSearch
                  allowClear
                  dataService={() => Promise.resolve(BORROWER_TYPE_OPTIONS)}
                  style={{ width: 200 }}
                  trigger="onDidMount"
                />
              )}
            </Form.Item>
          )}

          <Form.Item label="借用人">
            {getFieldDecorator('borrower', {
              rules: [
                {
                  required: true,
                  message: '借用人必选',
                },
                {
                  validator: (_, value, callback) => {
                    if (value && !value.label) {
                      callback('借用人必选！');
                      return;
                    }
                    if (value && value.label && value.label.length > 24) {
                      callback('借用人名称不超过24个字符！');
                      return;
                    }
                    callback();
                  },
                },
              ],
              initialValue: { key: info.borrower || '', label: info.borrowerName || '' },
            })(
              <UserSelect
                allowClear
                reserveSearchValue={judgeBorrowTypeIsOut(getFieldValue('borrowType'))}
                style={{ width: 200 }}
              />
            )}
          </Form.Item>
          {judgeBorrowTypeIsOut(getFieldValue('borrowType')) && (
            <Form.Item label="负责人">
              {getFieldDecorator('personLiable', {
                rules: [
                  {
                    required: true,
                    message: '负责人必选',
                  },
                  {
                    validator: (_, value, callback) => {
                      if (value && !value.label) {
                        callback('负责人必选！');
                        return;
                      }
                      callback();
                    },
                  },
                ],
                initialValue: info.personLiable
                  ? { key: info.personLiable, label: info.personLiableName }
                  : { key: '', label: '' },
              })(<UserSelect allowClear style={{ width: 200 }} />)}
            </Form.Item>
          )}

          <Form.Item label="借用起止日期">
            {getFieldDecorator('borrowingDate', {
              rules: [
                {
                  required: true,
                  message: '借用起止日期必选！',
                },
              ],
              initialValue:
                info.borrowStartDate && info.borrowEndDate
                  ? [moment(info.borrowStartDate), moment(info.borrowEndDate)]
                  : [],
            })(
              <DatePicker.RangePicker
                format="YYYY-MM-DD"
                disabledDate={current => current < moment().startOf('day')}
              />
            )}
          </Form.Item>

          <Form.Item label="原因">
            {getFieldDecorator('reason', {
              rules: [
                {
                  required: true,
                  whitespace: true,
                  message: '原因必填！',
                },
                {
                  max: 120,
                  message: '最多输入 120 个字符！',
                },
              ],
              initialValue: info.reason,
            })(<Input.TextArea allowClear style={{ width: 468 }} />)}
          </Form.Item>
        </Form>
      </TinyCard>
      <TinyCard title="借用信息" style={{ marginBottom: 40 }}>
        <TinyTable
          rowKey="id"
          align="left"
          dataSource={assetData}
          columns={tableColumns({
            deviceTypeMapping,
            remove: id => setAssetData(value => value.filter(item => id !== item.id)),
          })}
          actions={[
            <GutterWrapper key="action">
              <AddBorrowButttonModal
                blockGuid={getFieldValue('location') ? getFieldValue('location').join('.') : null}
                setAssetInfo={row => {
                  setAssetData(value => {
                    if (!value.length) {
                      return [row];
                    }
                    // 判断添加设备的信息是否已存在，如果存在则覆盖以前添加的设备
                    const idx = value.findIndex(
                      device =>
                        device.deviceType === row.deviceType &&
                        device.productModel === row.productModel &&
                        device.targetRoomGuid === row.targetRoomGuid
                    );
                    if (idx !== -1) {
                      let newAssetData = cloneDeep(value);
                      newAssetData[idx] = row;
                      return newAssetData;
                    } else {
                      return [row, ...value];
                    }
                  });
                }}
              />
              <Button onClick={() => setAssetData([])}>全部移除</Button>
            </GutterWrapper>,
          ]}
        />
      </TinyCard>
      <FooterToolBar>
        <GutterWrapper>
          <Button type="primary" onClick={onSubmit}>
            提交
          </Button>
          <Button onClick={() => history.goBack()}>取消</Button>
        </GutterWrapper>
      </FooterToolBar>
    </GutterWrapper>
  );
}
const mapStateToProps = ({
  common: { deviceCategory },
  borrowsAndReturn: {
    detail: { info },
  },
}) => {
  return {
    deviceTypeMapping:
      deviceCategory && deviceCategory.normalizedList ? deviceCategory.normalizedList : {},
    info,
  };
};
const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
  createBorrow: createBorrowActionCreator,
  getBorrowInfo: getBorrowInfoActionCreator,
  borrowReSubmit: borrowReSubmitActionCreator,
  queryBorrowAndReturnAssertInfo: queryBorrowAndReturnAssertInfoActionCreator,
  getBorrowApply: getBorrowApplyActionCreator,
};
export default connect(mapStateToProps, mapDispatchToProps)(Form.create()(Create));

//借用信息表格
const tableColumns = ({ deviceTypeMapping, remove }) => [
  {
    title: '一级分类',
    dataIndex: 'topCategory',
    render: topCategory => {
      if (deviceTypeMapping[topCategory]) {
        return deviceTypeMapping[topCategory].metaName;
      }
      return topCategory;
    },
  },
  {
    title: '二级分类',
    dataIndex: 'secondCategory',
    render: secondCategory => {
      if (deviceTypeMapping[secondCategory]) {
        return deviceTypeMapping[secondCategory].metaName;
      }
      return secondCategory;
    },
  },
  {
    title: '三级分类',
    dataIndex: 'deviceType',
    render: deviceType => {
      if (deviceTypeMapping[deviceType]) {
        return deviceTypeMapping[deviceType].metaName;
      }
      return deviceType;
    },
  },
  {
    title: '厂商',
    dataIndex: 'vendor',
  },
  {
    title: '型号',
    dataIndex: 'productModel',
  },
  {
    title: '借用至包间',
    dataIndex: 'targetRoomTag',
    render: targetRoomTag => {
      if (!targetRoomTag) {
        return BLANK_PLACEHOLDER;
      }
      return targetRoomTag;
    },
  },
  {
    title: '申请数量',
    dataIndex: 'applyNum',
  },
  {
    title: '操作',
    dataIndex: 'id',
    render: id => (
      <Button type="link" onClick={() => remove(id)}>
        移除
      </Button>
    ),
  },
];
