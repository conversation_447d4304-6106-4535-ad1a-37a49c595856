import React, { useEffect } from 'react';
import { connect } from 'react-redux';

import moment from 'moment';

import { SpaceOrDeviceLink } from '@manyun/resource-hub.ui.space-or-device-link';

import { GutterWrapper, TinyTable, UserLink } from '@manyun/dc-brain.legacy.components';
import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';
import { RELATE_BIZ_TYPE_KEY_MAP } from '@manyun/dc-brain.legacy.pages/borrows-and-return/constants';
import { getReturnRecordsActionCreator } from '@manyun/dc-brain.legacy.redux/actions/borrowsAndReturnActions';

import { SplitLine } from './split-line';

function ReturnRecord({
  getReturnRecord,
  borrowNo,
  tableLoading,
  returnRecords,
  deviceTypeMapping,
}) {
  useEffect(() => {
    if (borrowNo) {
      getReturnRecord({ relateTaskNo: borrowNo, relateBizType: RELATE_BIZ_TYPE_KEY_MAP.BORROW });
    }
  }, [borrowNo, getReturnRecord]);

  return (
    <GutterWrapper mode="vertical">
      <div style={{ marginTop: 34 }}>
        <GutterWrapper mode="vertical">
          <span>归还设备</span>
          <TinyTable
            rowKey="taskNo"
            align="left"
            dataSource={returnRecords.deviceData}
            columns={[
              {
                title: '资产ID',
                dataIndex: 'assetNo',
                render: assetNo => {
                  if (!assetNo) {
                    return BLANK_PLACEHOLDER;
                  }
                  return <SpaceOrDeviceLink id={assetNo} type="DEVICE_ASSET_NO" />;
                },
              },

              {
                title: '三级分类',
                dataIndex: 'deviceType',
                render: txt => deviceTypeMapping[txt]?.metaName,
              },

              {
                title: '归还人',
                dataIndex: 'relateUserId',
                render: (text, { relateUserName }) => (
                  <UserLink userId={text} userName={relateUserName} />
                ),
              },
              {
                title: '归还日期',
                dataIndex: 'gmtModified',
                dataType: 'dateTime',
                render: gmtModified => moment(gmtModified).format('YYYY-MM-DD'),
              },
              {
                title: '归还至包间',
                dataIndex: 'targetRoomGuid',
                render: targetRoomGuid => {
                  const tmp = targetRoomGuid?.split('.');
                  if (Array.isArray(tmp) && tmp[2]) {
                    return tmp[2];
                  }
                  return targetRoomGuid;
                },
              },
              {
                title: '创建人',
                dataIndex: 'creatorId',
                render: (text, record) => <UserLink userId={text} userName={record.creatorName} />,
              },
              {
                title: '创建时间',
                dataIndex: 'gmtCreate',
                dataType: 'dateTime',
                render: gmtCreate => moment(gmtCreate).format('YYYY-MM-DD HH:mm:ss'),
              },
            ]}
            pagination={false}
            loading={tableLoading}
          />
        </GutterWrapper>
      </div>
      <SplitLine />
      <div style={{ marginTop: 34 }}>
        <GutterWrapper mode="vertical">
          <span>归还耗材</span>
          <TinyTable
            rowKey="taskNo"
            align="left"
            dataSource={returnRecords.spareData}
            columns={[
              {
                title: '三级分类',
                dataIndex: 'deviceType',
                render: txt => deviceTypeMapping[txt]?.metaName,
              },
              {
                title: '厂商',
                dataIndex: 'vendor',
              },
              {
                title: '型号',
                dataIndex: 'productModel',
              },
              {
                title: '归还人',
                dataIndex: 'relateUserId',
                render: (text, { relateUserName }) => (
                  <UserLink userId={text} userName={relateUserName} />
                ),
              },
              {
                title: '归还日期',
                dataIndex: 'gmtModified',
                dataType: 'dateTime',
                render: gmtModified => moment(gmtModified).format('YYYY-MM-DD'),
              },
              {
                title: '归还至包间',
                dataIndex: 'targetRoomGuid',
                render: targetRoomGuid => {
                  const tmp = targetRoomGuid?.split('.');
                  if (Array.isArray(tmp) && tmp[2]) {
                    return tmp[2];
                  }
                  return targetRoomGuid;
                },
              },
              {
                title: '归还数量',
                dataIndex: 'warehouseCount',
              },
              {
                title: '创建人',
                dataIndex: 'creatorId',
                render: (text, record) => <UserLink userId={text} userName={record.creatorName} />,
              },
              {
                title: '创建时间',
                dataIndex: 'gmtCreate',
                dataType: 'dateTime',
                render: gmtCreate => moment(gmtCreate).format('YYYY-MM-DD HH:mm:ss'),
              },
            ]}
            pagination={false}
            loading={tableLoading}
          />
        </GutterWrapper>
      </div>
    </GutterWrapper>
  );
}

const mapStateToProps = ({
  common: { deviceCategory },
  borrowsAndReturn: {
    detail: { returnRecords, tableLoading },
  },
}) => {
  return {
    returnRecords,
    tableLoading,
    deviceTypeMapping:
      deviceCategory && deviceCategory.normalizedList ? deviceCategory.normalizedList : {},
  };
};
const mapDispatchToProps = {
  getReturnRecord: getReturnRecordsActionCreator,
};
export default connect(mapStateToProps, mapDispatchToProps)(ReturnRecord);
