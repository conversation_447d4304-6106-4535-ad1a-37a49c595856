import React, { useEffect } from 'react';
import { connect } from 'react-redux';

import moment from 'moment';

import {
  ApproveLink,
  GutterWrapper,
  StatusText,
  TinyTable,
  UserLink,
} from '@manyun/dc-brain.legacy.components';
import LendButtonModal from '@manyun/dc-brain.legacy.pages/borrows-and-return/components/lend-button-modal';
import RenewButtonModal from '@manyun/dc-brain.legacy.pages/borrows-and-return/components/renew-button-modal';
import WithdrawButtonModal from '@manyun/dc-brain.legacy.pages/borrows-and-return/components/withdraw-button-modal';
import {
  BORROW_RETURN_STATUS_TYPE_KEY_MAP,
  BORROW_TYPE_KEY_MAP,
  BORROW_TYPE_TEXT_MAP,
  RENEW_LEND_RECORD_MAP,
  RENEW_LEND_RECORD_TYPE_KEY_MAP,
} from '@manyun/dc-brain.legacy.pages/borrows-and-return/constants';
import { getRenewLendRecordsActionCreator } from '@manyun/dc-brain.legacy.redux/actions/borrowsAndReturnActions';

import { SplitLine } from './split-line';

const defaultButtonProps = { compact: true, type: 'link' };

function RenewLendRecords({
  getRenewLendRecords,
  borrowNo,
  tableLoading,
  renewLendRecords,
  info,
  borrowContentMode,
}) {
  useEffect(() => {
    if (borrowNo) {
      getRenewLendRecords({ borrowNo });
    }
  }, [borrowNo, getRenewLendRecords]);

  return (
    <GutterWrapper mode="vertical">
      <div style={{ marginTop: 34 }}>
        <GutterWrapper mode="vertical">
          <span>续借记录</span>
          <TinyTable
            rowKey="opId"
            align="left"
            dataSource={renewLendRecords.renewTableData}
            columns={[
              {
                title: '编号ID',
                dataIndex: 'opId',
              },
              {
                title: '续借结束日期',
                dataIndex: 'opJson',
                render: opJson => moment(opJson.endDate).format('YYYY-MM-DD'),
              },
              {
                title: '创建人',
                dataIndex: 'operator',
                render: (operator, record) => (
                  <UserLink userId={operator} userName={record.operatorName} />
                ),
              },
              {
                title: '创建时间',
                dataIndex: 'gmtCreate',
                render: gmtCreate => moment(gmtCreate).format('YYYY-MM-DD HH:mm:ss'),
              },
              {
                title: '审批',
                dataIndex: 'approvalId',
                render: approvalId => <ApproveLink id={approvalId} />,
              },
              {
                title: '状态',
                dataIndex: 'approvalStatus',
                render: txt =>
                  RENEW_LEND_RECORD_MAP[txt] ? (
                    <StatusText status={RENEW_LEND_RECORD_MAP[txt].status}>
                      {RENEW_LEND_RECORD_MAP[txt].label}
                    </StatusText>
                  ) : (
                    txt
                  ),
              },
              {
                title: '操作',
                dataIndex: 'action',
                render: (_, { approvalStatus, opId, approvalId, opJson }) => {
                  if (
                    approvalStatus === RENEW_LEND_RECORD_TYPE_KEY_MAP.REVOKE &&
                    info.borrowStatus === BORROW_RETURN_STATUS_TYPE_KEY_MAP.BORROWING
                  ) {
                    return (
                      <RenewButtonModal
                        buttonProps={defaultButtonProps}
                        borrowNo={info.borrowNo}
                        minDate={info.borrowEndDate}
                        opId={opId}
                        buttonText="重新发起"
                        modalTitle="重新发起续借"
                        type="detail"
                        borrowContentMode={borrowContentMode}
                        initValues={{
                          endDate: opJson?.endDate,
                          reason: opJson?.reason,
                        }}
                      />
                    );
                  }
                  if (approvalStatus === RENEW_LEND_RECORD_TYPE_KEY_MAP.APPROVING) {
                    return (
                      <WithdrawButtonModal
                        buttonProps={defaultButtonProps}
                        borrowNo={info.borrowNo}
                        modalTitle={`确认要撤回${approvalId}吗？`}
                        type="renew"
                        opId={opId}
                        buttonText="撤回"
                      />
                    );
                  }
                  return '--';
                },
              },
            ]}
            pagination={false}
            loading={tableLoading}
          />
        </GutterWrapper>
      </div>
      <SplitLine />
      <div style={{ marginTop: 34 }}>
        <GutterWrapper mode="vertical">
          <span>转借记录</span>
          <TinyTable
            rowKey="opId"
            align="left"
            dataSource={renewLendRecords.transferTableData}
            columns={[
              {
                title: '编号ID',
                dataIndex: 'opId',
              },
              {
                title: '借用类型',
                dataIndex: ['opJson', 'borrowType'],
                render: borrowType => BORROW_TYPE_TEXT_MAP[borrowType],
              },
              {
                title: '借用人',
                dataIndex: ['opJson', 'borrowerName'],
                render: (borrowerName, { opJson }) => {
                  if (opJson.borrowType === BORROW_TYPE_KEY_MAP.OUT) {
                    return borrowerName;
                  }
                  return <UserLink userId={opJson.borrower} userName={opJson.borrowerName} />;
                },
              },
              {
                title: '转借结束日期',
                dataIndex: 'opJson',
                render: opJson => moment(opJson.endDate).format('YYYY-MM-DD'),
              },
              {
                title: '创建人',
                dataIndex: 'operator',
                render: (operator, record) => (
                  <UserLink userId={operator} userName={record.operatorName} />
                ),
              },
              {
                title: '创建时间',
                dataIndex: 'gmtCreate',
                render: text => moment(text).format('YYYY-MM-DD HH:mm:ss'),
              },
              {
                title: '审批',
                dataIndex: 'approvalId',
                render: approvalId => <ApproveLink id={approvalId} />,
              },
              {
                title: '原因',
                dataType: ['opJson', 'reason'],
                ellipsis: true,
                render: (_, { opJson }) => opJson.reason,
              },
              {
                title: '状态',
                dataIndex: 'approvalStatus',
                render: txt =>
                  RENEW_LEND_RECORD_MAP[txt] ? (
                    <StatusText status={RENEW_LEND_RECORD_MAP[txt].status}>
                      {RENEW_LEND_RECORD_MAP[txt].label}
                    </StatusText>
                  ) : (
                    txt
                  ),
              },
              {
                title: '操作',
                dataIndex: 'action',
                render: (_, { approvalStatus, opId, approvalId, opJson }) => {
                  if (
                    approvalStatus === RENEW_LEND_RECORD_TYPE_KEY_MAP.REVOKE &&
                    info.borrowStatus === BORROW_RETURN_STATUS_TYPE_KEY_MAP.BORROWING
                  ) {
                    return (
                      <LendButtonModal
                        buttonProps={defaultButtonProps}
                        borrowNo={info.borrowNo}
                        minDate={info.borrowEndDate}
                        opId={opId}
                        buttonText="重新发起"
                        modalTitle="重新发起转借"
                        type="detail"
                        borrowContentMode={borrowContentMode}
                        initValues={opJson}
                      />
                    );
                  }
                  if (approvalStatus === RENEW_LEND_RECORD_TYPE_KEY_MAP.APPROVING) {
                    return (
                      <WithdrawButtonModal
                        buttonProps={defaultButtonProps}
                        borrowNo={info.borrowNo}
                        modalTitle={`确认要撤回${approvalId}吗？`}
                        type="renew"
                        opId={opId}
                        buttonText="撤回"
                      />
                    );
                  }
                  return '--';
                },
              },
            ]}
            pagination={false}
            loading={tableLoading}
          />
        </GutterWrapper>
      </div>
    </GutterWrapper>
  );
}

const mapStateToProps = ({
  borrowsAndReturn: {
    detail: { info, renewLendRecords, tableLoading },
  },
}) => {
  return {
    renewLendRecords,
    tableLoading,
    info,
  };
};
const mapDispatchToProps = {
  getRenewLendRecords: getRenewLendRecordsActionCreator,
};
export default connect(mapStateToProps, mapDispatchToProps)(RenewLendRecords);
