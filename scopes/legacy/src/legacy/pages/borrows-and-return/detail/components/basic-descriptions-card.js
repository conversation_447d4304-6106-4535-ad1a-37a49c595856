import React, { useEffect } from 'react';
import { connect } from 'react-redux';

import moment from 'moment';

import {
  ApproveLink,
  TinyCard,
  TinyDescriptions,
  UserLink,
} from '@manyun/dc-brain.legacy.components';
import {
  BORROWER_TYPE_TEXT_MAP,
  BORROW_RETURN_STATUS_TYPE_TEXT_MAP,
  BORROW_TYPE_KEY_MAP,
  BORROW_TYPE_TEXT_MAP,
} from '@manyun/dc-brain.legacy.pages/borrows-and-return/constants';
import { getBorrowInfoActionCreator } from '@manyun/dc-brain.legacy.redux/actions/borrowsAndReturnActions';

function BasicDescriptions({ info, getBorrowInfo, borrowNo }) {
  useEffect(() => {
    if (borrowNo) {
      getBorrowInfo({ borrowNo });
    }
  }, [borrowNo, getBorrowInfo]);

  const getDescriptionsItems = () => {
    let tmp = [
      {
        label: '编号ID',
        value: info.borrowNo,
      },
      {
        label: '标题',
        value: info.title,
      },
      {
        label: '位置',
        value: info.blockGuid,
      },
      {
        label: '状态',
        value: BORROW_RETURN_STATUS_TYPE_TEXT_MAP[info.borrowStatus],
      },
      {
        label: '借用类型',
        value: BORROW_TYPE_TEXT_MAP[info.borrowType],
      },
      {
        label: '借用人',
        value: (() => {
          if (info.borrowType === BORROW_TYPE_KEY_MAP.INNER) {
            return info.borrowerName;
          }
          return info.borrowerName && info.borrowerType
            ? `${info.borrowerName}(${BORROWER_TYPE_TEXT_MAP[info.borrowerType]})`
            : null;
        })(),
      },
      {
        label: '创建人',
        value: <UserLink userId={info.creator} userName={info.creatorName} />,
      },
      {
        label: '创建时间',
        value: moment(info.gmtCreate).format('YYYY-MM-DD HH:mm:ss'),
      },
      {
        label: '借用起止日期',
        value:
          moment(info.borrowStartDate).format('YYYY-MM-DD') +
          '~' +
          moment(info.borrowEndDate).format('YYYY-MM-DD'),
      },
      {
        label: '借用审批',
        value: <ApproveLink id={info.approvalId} />,
      },
      {
        label: '原因',
        span: info.borrowType === BORROW_TYPE_KEY_MAP.OUT ? 1 : 2,
        value: info.reason,
      },
    ];
    if (info.borrowType === BORROW_TYPE_KEY_MAP.OUT) {
      tmp.splice(8, 0, {
        label: '负责人',
        value: <UserLink userId={info.personLiable} userName={info.personLiableName} />,
      });
    }
    return tmp;
  };
  return (
    <TinyCard title="基本信息">
      <TinyDescriptions column={4} descriptionsItems={getDescriptionsItems()} />
    </TinyCard>
  );
}

const mapStateToProps = ({
  borrowsAndReturn: {
    detail: { info },
  },
}) => {
  return {
    info,
  };
};
const mapDispatchToProps = {
  getBorrowInfo: getBorrowInfoActionCreator,
};
export default connect(mapStateToProps, mapDispatchToProps)(BasicDescriptions);
