import React, { useEffect } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import moment from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';

import { TicketSlaText } from '@manyun/ticket.ui.tickets-table';

import { GutterWrapper, TinyTable, UserLink } from '@manyun/dc-brain.legacy.components';
import {
  BORROW_RETURN_STATUS_TYPE_KEY_MAP,
  RELATE_BIZ_TYPE_KEY_MAP,
} from '@manyun/dc-brain.legacy.pages/borrows-and-return/constants';
import { TICKET_STATUS_KEY_TEXT_MAP } from '@manyun/dc-brain.legacy.pages/ticket/constants';
import GetSlaText from '@manyun/dc-brain.legacy.pages/ticket/views/components';
import { getTicketRecordsActionCreator } from '@manyun/dc-brain.legacy.redux/actions/borrowsAndReturnActions';
import { redirectActionCreator } from '@manyun/dc-brain.legacy.redux/actions/routerActions';
import * as generateUrls from '@manyun/dc-brain.legacy.utils/urls';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';

import { SplitLine } from './split-line';

function TicketRecords({
  getTicketRecords,
  borrowNo,
  tableLoading,
  ticketRecords,
  ticketNormalizedList,
  info,
  redirect,
}) {
  useEffect(() => {
    if (borrowNo) {
      getTicketRecords({
        relateTaskNo: borrowNo,
        relateBizType: RELATE_BIZ_TYPE_KEY_MAP.BORROW,
        taskType: 'WAREHOUSE',
      });
    }
  }, [borrowNo, getTicketRecords]);

  return (
    <GutterWrapper mode="vertical">
      {info.borrowStatus === BORROW_RETURN_STATUS_TYPE_KEY_MAP.WAIT_STOCK_OUT && (
        <Button
          type="primary"
          onClick={() => {
            if (!info.canStockOut) {
              message.error('已满足借用申请，无法创建出库工单');
              return;
            }
            const currentDate = Number(moment().startOf('day').unix() + '000');
            if (info.borrowEndDate < currentDate) {
              message.error('已过借用有效日期，若想借用请重新申请借用');
              return;
            }
            redirect(
              generateUrls.generateTicketCreateUrl({
                ticketType: 'warehouse',
                variables: {
                  relateTaskNo: borrowNo,
                  relateTaskType: 'BORROW',
                },
              })
            );
          }}
        >
          申请出库
        </Button>
      )}
      <div>
        <GutterWrapper mode="vertical">
          <div>
            <span>出库记录</span>
            {/* &nbsp; &nbsp;
            <Tooltip title="同意借用后，系统会自动创建出库工单对借用资产进行出库。">
              <Icon type="question-circle-o" />
            </Tooltip> */}
          </div>

          <TinyTable
            rowKey="taskNo"
            align="left"
            dataSource={ticketRecords.exWareHouseList}
            columns={[
              {
                title: '工单单号',
                dataIndex: 'taskNo',
                render: taskNo => (
                  <Link
                    to={urls.generateTicketDetailLocation({
                      ticketType: 'warehouse',
                      id: taskNo,
                    })}
                  >
                    {taskNo}
                  </Link>
                ),
              },
              {
                title: '工单标题',
                dataIndex: 'taskTitle',
                ellipsis: true,
              },
              {
                title: '工单类型',
                dataIndex: 'taskType',
                render: txt =>
                  txt && ticketNormalizedList[txt] ? ticketNormalizedList[txt].taskValue : txt,
              },
              // {
              //   title: '工单子类型',
              //   dataIndex: 'taskSubType',
              //   render: txt =>
              //     ticketNormalizedList[txt] ? ticketNormalizedList[txt].taskValue : txt,
              // },
              {
                title: '创建时间',
                dataIndex: 'gmtCreate',
                dataType: 'dateTime',
                render: txt => moment(txt).format('YYYY-MM-DD HH:mm:ss'),
              },
              {
                title: 'SLA时效',
                dataIndex: 'taskSla',
                render: (text, record) => (
                  <TicketSlaText
                    taskSla={text}
                    delay={record.delay}
                    unit="SECOND"
                    effectTime={record.effectTime}
                    endTime={record.endTime}
                    shouldLimitShow
                  />
                ),
              },
              {
                title: '工单状态',
                dataIndex: 'taskStatus',
                render: text => TICKET_STATUS_KEY_TEXT_MAP[text],
              },
              {
                title: '处理人',
                dataIndex: 'taskAssigneeName',
                render: (text, record) => <UserLink userId={record.taskAssignee} userName={text} />,
              },
            ]}
            pagination={false}
            loading={tableLoading}
          />
        </GutterWrapper>
      </div>
      <SplitLine />
      <div style={{ marginTop: 34 }}>
        <GutterWrapper mode="vertical">
          <div>
            <span>入库记录</span>
            {/* &nbsp; &nbsp;
            <Tooltip title="发起归还后，系统会自动创建入库工单对归还资产进行入库。">
              <Icon type="exclamation-circle" />
            </Tooltip> */}
          </div>
          <TinyTable
            rowKey="taskNo"
            align="left"
            dataSource={ticketRecords.inWareHouseList}
            columns={[
              {
                title: '工单单号',
                dataIndex: 'taskNo',
                render: taskNo => (
                  <Link
                    to={urls.generateTicketDetailLocation({
                      ticketType: 'warehouse',
                      id: taskNo,
                    })}
                  >
                    {taskNo}
                  </Link>
                ),
              },
              {
                title: '工单类型',
                dataIndex: 'taskType',
                render: txt =>
                  ticketNormalizedList[txt] ? ticketNormalizedList[txt].taskValue : txt,
              },
              {
                title: '工单子类型',
                dataIndex: 'taskSubType',
                render: txt =>
                  ticketNormalizedList[txt] ? ticketNormalizedList[txt].taskValue : txt,
              },
              {
                title: '创建时间',
                dataIndex: 'gmtCreate',
                dataType: 'dateTime',
                render: txt => moment(txt).format('YYYY-MM-DD HH:mm:ss'),
              },
              {
                title: 'SLA计时/标准',
                dataIndex: 'taskSla',
                render: (text, record) => (
                  <GetSlaText
                    taskSla={text}
                    delay={record.delay}
                    unit={record.unit}
                    effectTime={record.effectTime}
                    endTime={record.endTime}
                  />
                ),
              },
              {
                title: '工单状态',
                dataIndex: 'taskStatus',
                render: text => TICKET_STATUS_KEY_TEXT_MAP[text],
              },
              {
                title: '处理人',
                dataIndex: 'taskAssigneeName',
                render: (text, record) => <UserLink userId={record.taskAssignee} userName={text} />,
              },
            ]}
            pagination={false}
            loading={tableLoading}
          />
        </GutterWrapper>
      </div>
    </GutterWrapper>
  );
}

const mapStateToProps = ({
  common: { ticketTypes },
  borrowsAndReturn: {
    detail: { ticketRecords, tableLoading, info },
  },
}) => {
  return {
    ticketRecords,
    tableLoading,
    ticketNormalizedList: ticketTypes ? ticketTypes.normalizedList : {},
    info,
  };
};
const mapDispatchToProps = {
  getTicketRecords: getTicketRecordsActionCreator,
  redirect: redirectActionCreator,
};
export default connect(mapStateToProps, mapDispatchToProps)(TicketRecords);
