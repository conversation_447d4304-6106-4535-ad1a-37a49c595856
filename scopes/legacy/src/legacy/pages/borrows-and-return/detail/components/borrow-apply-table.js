import React, { useEffect } from 'react';
import { connect } from 'react-redux';

import { TinyTable } from '@manyun/dc-brain.legacy.components';
import { getBorrowApplyActionCreator } from '@manyun/dc-brain.legacy.redux/actions/borrowsAndReturnActions';

function BorrowApplyTable({
  borrowApplyList,
  getBorrowApply,
  borrowNo,
  tableLoading,
  deviceTypeMaping,
}) {
  useEffect(() => {
    if (borrowNo) {
      getBorrowApply({ borrowNo });
    }
  }, [borrowNo, getBorrowApply]);

  return (
    <TinyTable
      rowKey="id"
      align="left"
      dataSource={borrowApplyList}
      columns={[
        {
          title: '一级分类',
          dataIndex: 'topCategory',
          render: txt => (deviceTypeMaping[txt] ? deviceTypeMaping[txt].metaName : txt),
        },
        {
          title: '二级分类',
          dataIndex: 'secondCategory',
          render: txt => (deviceTypeMaping[txt] ? deviceTypeMaping[txt].metaName : txt),
        },
        {
          title: '三级分类',
          dataIndex: 'deviceType',
          render: txt => (deviceTypeMaping[txt] ? deviceTypeMaping[txt].metaName : txt),
        },
        {
          title: '厂商',
          dataIndex: 'vendor',
        },
        {
          title: '型号',
          dataIndex: 'productModel',
        },
        {
          title: '借用至包间',
          dataIndex: 'targetRoomTag',
          render: targetRoomTag => (targetRoomTag ? targetRoomTag : '--'),
        },
        {
          title: '申请数量',
          dataIndex: 'applyNum',
        },
      ]}
      pagination={false}
      loading={tableLoading}
    />
  );
}

const mapStateToProps = ({
  common: { deviceCategory },
  borrowsAndReturn: {
    detail: { borrowApplyList, tableLoading },
  },
}) => {
  return {
    borrowApplyList,
    tableLoading,
    deviceTypeMaping:
      deviceCategory && deviceCategory.normalizedList ? deviceCategory.normalizedList : {},
  };
};
const mapDispatchToProps = {
  getBorrowApply: getBorrowApplyActionCreator,
};
export default connect(mapStateToProps, mapDispatchToProps)(BorrowApplyTable);
