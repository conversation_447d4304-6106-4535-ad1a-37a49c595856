@import '~@manyun/base-ui.theme.theme/dist/theme.variable.less';
.tinyCard {
  display: inline-block;
  width: 244px;
  height: 112px;
  cursor: pointer;
  background: @component-background;
  border-color: @border-color-split;
  border-radius: 7px;

  .styleDiv {
    display: flex;
    padding-left: 48px;
    padding-right: 16px;
    justify-content: space-between;
  }

  .selected {
    background: @primary-color-hover;
  }

  .numSpan {
    display: flex;
    align-items: center;
    &.normal {
      color: @text-color;
    }

    &.error {
      color: @error-color;
    }
  }
}
