import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';

import Icon from '@ant-design/icons';
import omit from 'lodash/omit';
import moment from 'moment';

import { SpaceOrDeviceLink } from '@manyun/resource-hub.ui.space-or-device-link';

import { GutterWrapper, TinyTable } from '@manyun/dc-brain.legacy.components';
import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';
import { Borrow, Return } from '@manyun/dc-brain.legacy.pages/borrows-and-return/icons';
import {
  borrowsAndReturnActions,
  queryBorrowAndReturnAssertInfoActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/borrowsAndReturnActions';

import { SplitLine } from '../split-line';
import StatisticsCard from './components/statistics-card';

function BorrowReturnRecord({
  borrowNo,
  tableLoading,
  borrowAndReturnRecord: { device, spare },
  getInfo,
  isOvertime,
  updateBorrowDeviceTableData,
  updateBorrowSpareTableData,
  deviceTypeMapping,
}) {
  const [filterFileds, setFilterFileds] = useState({
    deviceBorrow: true,
    spareBorrow: true,
  });

  useEffect(() => {
    if (borrowNo) {
      getInfo({ borrowNo });
    }
  }, [borrowNo, getInfo]);

  return (
    <GutterWrapper mode="vertical">
      <div style={{ marginTop: 34 }}>
        <GutterWrapper mode="vertical">
          <span>借用设备</span>
          <GutterWrapper>
            {[
              {
                txt: '借出数量',
                component: <Icon component={Borrow} />,
                numTxtColor: 'normal',
                number: device.borrowNum,
                onFilter: () => {
                  if (filterFileds.deviceBorrow) {
                    return;
                  }
                  if (filterFileds.deviceNoReturn) {
                    setFilterFileds(fleds => ({
                      ...omit(fleds, 'deviceNoReturn'),
                      deviceBorrow: true,
                    }));
                    updateBorrowDeviceTableData(device.initList);
                  }
                },
                selected: filterFileds.deviceBorrow,
              },
              {
                txt: '未归还数量',
                component: <Icon component={Return} />,
                numTxtColor: isOvertime && device.toBeReturnedNum ? 'error' : 'normal',
                number: device.toBeReturnedNum,
                onFilter: () => {
                  if (filterFileds.deviceNoReturn) {
                    return;
                  }
                  if (filterFileds.deviceBorrow) {
                    setFilterFileds(fleds => ({
                      ...omit(fleds, 'deviceBorrow'),
                      deviceNoReturn: true,
                    }));
                    if (device.toBeReturnedNum) {
                      const newData = device.list.filter(
                        ({ borrowNum, returnNum }) => borrowNum !== returnNum
                      );
                      updateBorrowDeviceTableData(newData);
                      return;
                    }
                    updateBorrowDeviceTableData([]);
                  }
                },
                selected: filterFileds.deviceNoReturn,
              },
            ].map(({ txt, component, numTxtColor, number, onFilter, selected }) => {
              return (
                <StatisticsCard
                  title={txt}
                  component={component}
                  key={txt}
                  numTxtColor={numTxtColor}
                  number={number}
                  onFilter={onFilter}
                  selected={selected}
                />
              );
            })}
          </GutterWrapper>
          <TinyTable
            rowKey="id"
            align="left"
            dataSource={device.list}
            columns={[
              {
                title: '资产ID',
                dataIndex: 'assetNo',
                render: assetNo => {
                  if (!assetNo) {
                    return BLANK_PLACEHOLDER;
                  }
                  return <SpaceOrDeviceLink id={assetNo} type="DEVICE_ASSET_NO" />;
                },
              },
              {
                title: '三级分类',
                dataIndex: 'deviceType',
                render: txt => deviceTypeMapping[txt]?.metaName,
              },
              {
                title: '借用至包间',
                dataIndex: 'roomTag',
              },
              {
                title: '厂商',
                dataIndex: 'vendor',
              },
              {
                title: '型号',
                dataIndex: 'productModel',
              },
              {
                title: '资产状态',
                dataIndex: 'borrowNum',
                render: (borrowNum, { returnNum }) => {
                  if (borrowNum === returnNum) {
                    return '已归还';
                  }
                  return '已借用';
                },
              },
            ]}
            pagination={false}
            loading={tableLoading}
          />
        </GutterWrapper>
      </div>
      <SplitLine />
      <div style={{ marginTop: 34 }}>
        <GutterWrapper mode="vertical">
          <span>借用耗材</span>
          <GutterWrapper>
            {[
              {
                txt: '借出数量',
                component: <Icon component={Borrow} />,
                numTxtColor: 'normal',
                number: spare.borrowNum,
                onFilter: () => {
                  if (filterFileds.spareBorrow) {
                    return;
                  }
                  if (filterFileds.spareNoReturn) {
                    setFilterFileds(fleds => ({
                      ...omit(fleds, 'spareNoReturn'),
                      spareBorrow: true,
                    }));
                    updateBorrowSpareTableData(spare.initList);
                    return;
                  }
                },
                selected: filterFileds.spareBorrow,
              },
              {
                txt: '未归还数量',
                component: <Icon component={Return} />,
                numTxtColor: isOvertime && spare.toBeReturnedNum ? 'error' : 'normal',
                number: spare.toBeReturnedNum,
                onFilter: () => {
                  if (filterFileds.spareNoReturn) {
                    return;
                  }
                  if (filterFileds.spareBorrow) {
                    setFilterFileds(fleds => ({
                      ...omit(fleds, 'spareBorrow'),
                      spareNoReturn: true,
                    }));
                    if (spare.toBeReturnedNum) {
                      const newData = spare.list.filter(
                        ({ borrowNum, returnNum }) => borrowNum !== returnNum
                      );
                      updateBorrowSpareTableData(newData);
                      return;
                    }
                    updateBorrowSpareTableData([]);
                  }
                },
                selected: filterFileds.spareNoReturn,
              },
            ].map(({ txt, component, numTxtColor, number, onFilter, selected }) => {
              return (
                <StatisticsCard
                  title={txt}
                  component={component}
                  key={txt}
                  numTxtColor={numTxtColor}
                  number={number}
                  onFilter={onFilter}
                  selected={selected}
                />
              );
            })}
          </GutterWrapper>
          <TinyTable
            rowKey="id"
            align="left"
            dataSource={spare.list}
            columns={[
              {
                title: '三级分类',
                dataIndex: 'deviceType',
                render: txt => deviceTypeMapping[txt]?.metaName,
              },
              {
                title: '厂商',
                dataIndex: 'vendor',
              },
              {
                title: '型号',
                dataIndex: 'productModel',
              },
              {
                title: '借出数量',
                dataIndex: 'borrowNum',
              },
              {
                title: '归还数量',
                dataIndex: 'returnNum',
              },
              {
                title: '未归还数量',
                dataIndex: 'waitReturnNum',
                render: (_, { borrowNum, returnNum }) => {
                  if (!borrowNum) {
                    return 0;
                  }
                  if (!returnNum) {
                    return borrowNum;
                  }
                  return borrowNum - returnNum;
                },
              },
            ]}
            pagination={false}
            loading={tableLoading}
          />
        </GutterWrapper>
      </div>
    </GutterWrapper>
  );
}

const mapStateToProps = ({
  common: { deviceCategory },
  borrowsAndReturn: {
    detail: { borrowAndReturnRecord, tableLoading, info },
  },
}) => {
  const isOvertime = moment().startOf('day').valueOf() > info.borrowEndDate ? true : false;
  return {
    borrowAndReturnRecord,
    tableLoading,
    isOvertime,
    deviceTypeMapping:
      deviceCategory && deviceCategory.normalizedList ? deviceCategory.normalizedList : {},
  };
};
const mapDispatchToProps = {
  getInfo: queryBorrowAndReturnAssertInfoActionCreator,
  updateBorrowDeviceTableData: borrowsAndReturnActions.updateBorrowDeviceTableData,
  updateBorrowSpareTableData: borrowsAndReturnActions.updateBorrowSpareTableData,
};
export default connect(mapStateToProps, mapDispatchToProps)(BorrowReturnRecord);
