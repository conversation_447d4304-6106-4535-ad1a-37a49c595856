import React from 'react';

import classNames from 'classnames';

import { TinyCard } from '@manyun/dc-brain.legacy.components';

import styles from './statistics-card.module.less';

function StatisticsCard({ title, component, numTxtColor, number, onFilter, selected }) {
  return (
    <TinyCard
      className={classNames(styles.tinyCard)}
      title={title}
      bordered
      headStyle={{ border: 0, padding: '0 8px' }}
      bodyStyle={{ paddingTop: 0 }}
      onClick={onFilter}
      selected={selected}
    >
      <div className={styles.styleDiv}>
        <span className={classNames(styles.numSpan, numTxtColor && styles[numTxtColor])}>
          {number}
        </span>
        <span>{component}</span>
      </div>
    </TinyCard>
  );
}

export default StatisticsCard;
