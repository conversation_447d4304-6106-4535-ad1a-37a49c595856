import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { Radio } from '@manyun/base-ui.ui.radio';

import { OperationLogTable } from '@manyun/auth-hub.ui.operation-log-table';
import { generateBorrowAndReturnEditLocation } from '@manyun/resource-hub.route.resource-routes';

import { <PERSON>er<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Tiny<PERSON>ard } from '@manyun/dc-brain.legacy.components';
import FinishWarehouse from '@manyun/dc-brain.legacy.pages/borrows-and-return/components/finish-warehouse-button-modal';
import LendButtonModal from '@manyun/dc-brain.legacy.pages/borrows-and-return/components/lend-button-modal';
import RenewButtonModal from '@manyun/dc-brain.legacy.pages/borrows-and-return/components/renew-button-modal';
import ReturnButtonModal from '@manyun/dc-brain.legacy.pages/borrows-and-return/components/return-button-modal';
import WithdrawButtonModal from '@manyun/dc-brain.legacy.pages/borrows-and-return/components/withdraw-button-modal';
import {
  BORROW_CONTENT_MODE_KEY_MAP,
  BORROW_CONTENT_MODE_OPTIONS,
  BORROW_RETURN_STATUS_TYPE_KEY_MAP,
} from '@manyun/dc-brain.legacy.pages/borrows-and-return/constants';
import { borrowsAndReturnActions } from '@manyun/dc-brain.legacy.redux/actions/borrowsAndReturnActions';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';

import BasicDescriptions from './components/basic-descriptions-card';
import BorrowApplyTable from './components/borrow-apply-table';
import BorrowReturnRecord from './components/borrow-return-record';
import RenewLendRecords from './components/renew-lend-records';
import ReturnRecords from './components/return-records';
import TicketRecords from './components/ticket-records';

function Detail({ match, syncCommonData, history, info, reset }) {
  const borrowNo = match.params.id;
  const [borrowContentMode, setBorrowContentMode] = useState('borrowApply');

  useEffect(() => {
    syncCommonData({
      strategy: { space: 'IF_NULL', deviceCategory: 'IF_NULL', ticketTypes: 'IF_NULL' },
    });
  }, [syncCommonData]);

  useEffect(() => {
    return () => {
      reset();
    };
  }, [reset]);

  return (
    <GutterWrapper mode="vertical">
      <BasicDescriptions borrowNo={borrowNo} />
      <TinyCard title="借还内容">
        <GutterWrapper mode="vertical">
          <Radio.Group
            value={borrowContentMode}
            onChange={({ target: { value } }) => setBorrowContentMode(value)}
          >
            {BORROW_CONTENT_MODE_OPTIONS.map(({ label, value }) => {
              return (
                <Radio.Button key={value} value={value}>
                  {label}
                </Radio.Button>
              );
            })}
          </Radio.Group>
          {borrowContentMode === BORROW_CONTENT_MODE_KEY_MAP.borrowApply && (
            <BorrowApplyTable borrowNo={borrowNo} />
          )}
          {borrowContentMode === BORROW_CONTENT_MODE_KEY_MAP.loanReturnList && (
            <BorrowReturnRecord borrowNo={borrowNo} />
          )}
          {borrowContentMode === BORROW_CONTENT_MODE_KEY_MAP.returnRecord && (
            <ReturnRecords borrowNo={borrowNo} />
          )}
          {borrowContentMode === BORROW_CONTENT_MODE_KEY_MAP.continuationRecord && (
            <RenewLendRecords borrowNo={borrowNo} borrowContentMode={borrowContentMode} />
          )}
          {borrowContentMode === BORROW_CONTENT_MODE_KEY_MAP.ticketRecord && (
            <TicketRecords borrowNo={borrowNo} />
          )}
        </GutterWrapper>
      </TinyCard>
      <TinyCard title="操作记录" style={{ marginBottom: 40 }}>
        <OperationLogTable
          defaultSearchParams={{ targetType: 'BORROW', targetId: borrowNo }}
          isTargetIdEqual={targetId => {
            return targetId === borrowNo;
          }}
          showColumns={['modifyType']}
        />
      </TinyCard>
      {!(
        info.borrowStatus === BORROW_RETURN_STATUS_TYPE_KEY_MAP.RETURN ||
        info.borrowStatus === BORROW_RETURN_STATUS_TYPE_KEY_MAP.CANCEL
      ) && (
        <FooterToolBar>
          <GutterWrapper>
            {info.borrowStatus === BORROW_RETURN_STATUS_TYPE_KEY_MAP.BORROWING &&
              info.hasPermission && (
                <>
                  <ReturnButtonModal
                    buttonProps={{ type: 'primary' }}
                    borrowNo={info.borrowNo}
                    borrower={info.borrowerName}
                    type="detail"
                    borrowContentMode={borrowContentMode}
                    info={info}
                  />
                  <RenewButtonModal
                    buttonProps={{
                      type: 'warning',
                    }}
                    borrowNo={info.borrowNo}
                    minDate={info.borrowEndDate}
                    type="detail"
                    borrowContentMode={borrowContentMode}
                    // initValues={{ endDate: info.borrowEndDate }}
                  />
                  <LendButtonModal
                    borrowNo={info.borrowNo}
                    type="detail"
                    borrowEndDate={info.borrowEndDate}
                    borrowContentMode={borrowContentMode}
                    minDate={info.borrowEndDate}
                    initValues={{ endDate: info.borrowEndDate }}
                  />
                </>
              )}
            {info.borrowStatus === BORROW_RETURN_STATUS_TYPE_KEY_MAP.WAIT_STOCK_OUT &&
              info.hasPermission && (
                <FinishWarehouse
                  buttonProps={{ type: 'primary' }}
                  borrowNo={info.borrowNo}
                  type="detail"
                />
              )}
            {info.borrowStatus === BORROW_RETURN_STATUS_TYPE_KEY_MAP.APPROVING &&
              info.hasPermission && (
                <WithdrawButtonModal
                  buttonProps={{
                    type: 'primary',
                  }}
                  borrowNo={info.borrowNo}
                  modalTitle={`确认要撤回${info.borrowNo}吗？`}
                  type="detail"
                />
              )}
            {info.borrowStatus === BORROW_RETURN_STATUS_TYPE_KEY_MAP.DRAFT && info.hasPermission && (
              <Button
                type="primary"
                href={generateBorrowAndReturnEditLocation({ id: info.borrowNo })}
              >
                重新发起
              </Button>
            )}
          </GutterWrapper>
        </FooterToolBar>
      )}
    </GutterWrapper>
  );
}
const mapStateToProps = ({
  borrowsAndReturn: {
    detail: { info },
  },
}) => {
  return {
    info,
  };
};
const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
  reset: borrowsAndReturnActions.resetDetail,
};
export default connect(mapStateToProps, mapDispatchToProps)(Detail);
