import React from 'react';
import { connect } from 'react-redux';

import { ApiSelect, FiltersForm, Form } from '@galiojs/awesome-antd';

import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Input } from '@manyun/base-ui.ui.input';

import { LocationCascader, UserSelect } from '@manyun/dc-brain.legacy.components';
import { BORROW_RETURN_STATUS_TYPE_OPTIONS } from '@manyun/dc-brain.legacy.pages/borrows-and-return/constants';
import {
  borrowsAndReturnActions,
  getDataActionCreator,
  resetSearchValuesActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/borrowsAndReturnActions';

export function SearchForm({ onReset, onSearch, updateSearchValues, searchValues }) {
  const [form] = Form.useForm();
  const items = [
    {
      label: '编号ID',
      name: 'borrowNo',
      control: <Input allowClear />,
    },
    {
      label: '资产ID',
      name: 'assetNo',
      control: <Input allowClear />,
    },
    {
      label: '标题',
      name: 'title',
      control: <Input allowClear />,
    },
    {
      label: '位置',
      name: 'blockGuid',
      control: <LocationCascader currentAuthorize />,
    },
    {
      label: '借用人',
      name: 'borrower',
      control: <UserSelect allowClear reserveSearchValue />,
    },
    {
      label: '状态',
      name: 'status',
      control: (
        <ApiSelect
          allowClear
          showSearch
          // mode="multiple"
          dataService={() => Promise.resolve(BORROW_RETURN_STATUS_TYPE_OPTIONS)}
        />
      ),
    },
    {
      label: '创建人',
      name: 'creatorId',
      control: <UserSelect allowClear />,
    },
    {
      label: '创建日期',
      name: 'createTimeRange',
      span: 2,
      control: <DatePicker.RangePicker format="YYYY-MM-DD" />,
    },
    {
      label: '借用结束日期',
      name: 'borrowTimeRange',
      span: 2,
      control: <DatePicker.RangePicker format="YYYY-MM-DD" />,
    },
  ];
  return (
    <FiltersForm
      form={form}
      items={items}
      onFieldsChange={changedFields => {
        updateSearchValues(
          changedFields.reduce((mapper, field) => {
            // field.name 为数组形式，老代码需要的是字符串形式
            const name = field.name.join('.');
            mapper[name] = {
              ...field,
              name,
            };
            return mapper;
          }, {})
        );
      }}
      onSearch={() => onSearch({ shouldResetPageNum: true, onSearchButton: true })}
      onReset={onReset}
      fields={Object.keys(searchValues).map(name => {
        const field = searchValues[name];
        return {
          ...field,
          // name 为数组形式
          name: name.split('.'),
        };
      })}
    />
  );
}

const mapStateToProps = ({
  borrowsAndReturn: {
    list: { searchValues },
  },
}) => {
  return { searchValues };
};
const mapDispatchToProps = {
  updateSearchValues: borrowsAndReturnActions.updateSearchValues,
  onReset: resetSearchValuesActionCreator,
  onSearch: getDataActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(SearchForm);
