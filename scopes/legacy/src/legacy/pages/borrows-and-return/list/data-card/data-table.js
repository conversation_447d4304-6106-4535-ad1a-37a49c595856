import React, { useCallback, useEffect } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import moment from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Space } from '@manyun/base-ui.ui.space';

import {
  BORROWS_AND_RETURN_CREATE,
  generateBorrowAndReturnDetailLocation,
  generateBorrowAndReturnEditLocation,
} from '@manyun/resource-hub.route.resource-routes';

import { StatusText, TinyTable, UserLink } from '@manyun/dc-brain.legacy.components';
import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';
import { STATUS_MAP } from '@manyun/dc-brain.legacy.constants/statusColor';
import CancleBorrowButtonModal from '@manyun/dc-brain.legacy.pages/borrows-and-return/components/cancle-button-modal';
import FinishWarehouse from '@manyun/dc-brain.legacy.pages/borrows-and-return/components/finish-warehouse-button-modal';
import LendButtonModal from '@manyun/dc-brain.legacy.pages/borrows-and-return/components/lend-button-modal';
import RenewButtonModal from '@manyun/dc-brain.legacy.pages/borrows-and-return/components/renew-button-modal';
import ReturnButtonModal from '@manyun/dc-brain.legacy.pages/borrows-and-return/components/return-button-modal';
import WithdrawButtonModal from '@manyun/dc-brain.legacy.pages/borrows-and-return/components/withdraw-button-modal';
import {
  BORROW_RETURN_STATUS_TYPE_KEY_MAP,
  BORROW_RETURN_STATUS_TYPE_TEXT_MAP,
  BORROW_TYPE_KEY_MAP,
  BORROW_TYPE_TEXT_MAP, // RELATE_BIZ_TYPE_TEXT_MAP,
} from '@manyun/dc-brain.legacy.pages/borrows-and-return/constants';
import {
  cancelBorrowActionCreator,
  getDataActionCreator,
  setPaginationThenGetDataActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/borrowsAndReturnActions';

const defaultButtonProps = { compact: true, type: 'link' };

export function DataTable({
  data,
  total,
  pageNum,
  pageSize,
  getData,
  setPagination,
  loading,
  cancelBorrow,
}) {
  useEffect(() => {
    getData({
      shouldResetPageNum: true,
      onSearchButton: false,
    });
  }, [getData]);

  const paginationChangeHandler = useCallback(
    (current, size) => {
      setPagination({ pageNum: current, pageSize: size });
    },
    [setPagination]
  );

  return (
    <TinyTable
      rowKey="borrowNo"
      loading={loading}
      columns={getColumns({ cancelBorrow })}
      align="left"
      dataSource={data}
      scroll={{ x: 'max-content' }}
      actions={[
        <Button key="create" type="primary" href={BORROWS_AND_RETURN_CREATE}>
          新建
        </Button>,
      ]}
      pagination={{
        total,
        current: pageNum,
        pageSize,
        onChange: paginationChangeHandler,
      }}
    />
  );
}

const mapStateToProps = ({
  borrowsAndReturn: {
    list: {
      data,
      total,
      pagination: { pageNum, pageSize },
      loading,
    },
  },
}) => {
  return {
    data,
    total,
    pageNum,
    pageSize,
    loading,
  };
};
const mapDispatchToProps = {
  getData: getDataActionCreator,
  setPagination: setPaginationThenGetDataActionCreator,
  cancelBorrow: cancelBorrowActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(DataTable);

const getColumns = ({ cancelBorrow }) => [
  {
    title: '编号ID',
    dataIndex: 'borrowNo',
    render: borrowNo => (
      <Link to={generateBorrowAndReturnDetailLocation({ id: borrowNo })}>{borrowNo}</Link>
    ),
  },
  {
    title: '标题',
    dataIndex: 'title',
    ellipsis: true,
  },
  {
    title: '位置',
    dataIndex: 'blockGuid',
  },
  {
    title: '借用类型',
    dataIndex: 'borrowType',
    render: borrowType => BORROW_TYPE_TEXT_MAP[borrowType],
  },
  {
    title: '借用人',
    dataIndex: 'borrowerName',
    ellipsis: true,
    render: (borrowerName, record) => {
      if (record.borrowType === BORROW_TYPE_KEY_MAP.OUT) {
        return borrowerName;
      }
      return <UserLink userId={record.borrower} userName={borrowerName} />;
    },
  },
  {
    title: '借用起止日期',
    dataType: 'date',
    dataIndex: 'borrowStartDate',
    render: (borrowStartDate, { borrowEndDate }) =>
      moment(borrowStartDate).format('YYYY-MM-DD') +
      '~' +
      moment(borrowEndDate).format('YYYY-MM-DD'),
  },
  {
    title: '创建人',
    dataIndex: 'creator',
    render: (creator, record) => <UserLink userId={creator} userName={record.creatorName} />,
  },
  {
    title: '创建时间',
    dataType: 'dateTime',
    dataIndex: 'gmtCreate',
    render: txt => moment(txt).format('YYYY-MM-DD HH:mm:ss'),
  },
  {
    title: '状态',
    dataIndex: 'borrowStatus',
    render: borrowStatus => {
      const txt = BORROW_RETURN_STATUS_TYPE_TEXT_MAP[borrowStatus]
        ? BORROW_RETURN_STATUS_TYPE_TEXT_MAP[borrowStatus]
        : borrowStatus;
      // 待出库 / 待审批
      if (
        borrowStatus === BORROW_RETURN_STATUS_TYPE_KEY_MAP.WAIT_STOCK_OUT ||
        borrowStatus === BORROW_RETURN_STATUS_TYPE_KEY_MAP.APPROVING
      ) {
        return (
          <StatusText status={STATUS_MAP.WARNING} fontSize={14}>
            {txt}
          </StatusText>
        );
      }
      // 已借用
      if (borrowStatus === BORROW_RETURN_STATUS_TYPE_KEY_MAP.BORROWING) {
        return (
          <StatusText status={STATUS_MAP.ALARM} fontSize={14}>
            {txt}
          </StatusText>
        );
      }
      // 已归还
      if (borrowStatus === BORROW_RETURN_STATUS_TYPE_KEY_MAP.RETURN) {
        return (
          <StatusText status={STATUS_MAP.NORMAL} fontSize={14}>
            {txt}
          </StatusText>
        );
      }
      // 草稿
      if (borrowStatus === BORROW_RETURN_STATUS_TYPE_KEY_MAP.DRAFT) {
        return (
          <StatusText status={STATUS_MAP.DISABLED} fontSize={14}>
            {txt}
          </StatusText>
        );
      }
      return txt;
    },
  },
  {
    title: '操作',
    dataIndex: '_actions',
    fixed: 'right',
    width: 150,
    render: (_, record) => {
      // 判断权限
      if (!record.hasPermission) {
        return BLANK_PLACEHOLDER;
      }
      // 草稿
      if (record.borrowStatus === BORROW_RETURN_STATUS_TYPE_KEY_MAP.DRAFT) {
        return (
          <>
            <CancleBorrowButtonModal
              buttonProps={defaultButtonProps}
              borrowNo={record.borrowNo}
              type="list"
            />
            <Divider type="vertical" />
            <Link
              to={generateBorrowAndReturnEditLocation({
                id: record.borrowNo,
              })}
            >
              重新发起
            </Link>
          </>
        );
      }
      // 待出库,跳转出库页面，创建出库单
      if (record.borrowStatus === BORROW_RETURN_STATUS_TYPE_KEY_MAP.WAIT_STOCK_OUT) {
        return (
          <>
            {/* <Link
              to={generateUrls.generateTicketCreateUrl({
                ticketType: 'warehouse',
                variables: {
                  relateTaskNo: record.borrowNo,
                  relateTaskType: RELATE_BIZ_TYPE_TEXT_MAP.BORROW,
                },
              })}
            >
              创建出库单
            </Link>

            <Divider type="vertical" /> */}
            <FinishWarehouse buttonProps={defaultButtonProps} borrowNo={record.borrowNo} />
          </>
        );
      }
      // 待审批
      if (record.borrowStatus === BORROW_RETURN_STATUS_TYPE_KEY_MAP.APPROVING) {
        return (
          <WithdrawButtonModal
            buttonProps={defaultButtonProps}
            borrowNo={record.borrowNo}
            modalTitle={`确认要撤回${record.borrowNo}吗？`}
          />
        );
      }
      // 已取消 已归还
      if (
        record.borrowStatus === BORROW_RETURN_STATUS_TYPE_KEY_MAP.CANCEL ||
        record.borrowStatus === BORROW_RETURN_STATUS_TYPE_KEY_MAP.RETURN
      ) {
        return BLANK_PLACEHOLDER;
      }
      return (
        <Space align="center" size={0}>
          <RenewButtonModal
            buttonProps={defaultButtonProps}
            borrowNo={record.borrowNo}
            minDate={record.borrowEndDate}
            // initValues={{
            //   endDate: record.borrowEndDate,
            // }}
          />
          <Divider type="vertical" />
          <LendButtonModal
            buttonProps={defaultButtonProps}
            borrowNo={record.borrowNo}
            minDate={record.borrowEndDate}
            initValues={{
              endDate: record.borrowEndDate,
            }}
          />
          <Divider type="vertical" />
          <ReturnButtonModal
            buttonProps={defaultButtonProps}
            borrowNo={record.borrowNo}
            borrower={record.borrowerName}
            info={record}
          />
        </Space>
      );
    },
  },
];
