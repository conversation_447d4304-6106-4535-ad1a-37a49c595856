import React, { useEffect } from 'react';
import { connect } from 'react-redux';

import moment from 'moment';

import { Divider } from '@manyun/base-ui.ui.divider';
import { Space } from '@manyun/base-ui.ui.space';

import {
  MaintenancesTable,
  SecurityStandsTable,
  ToolsTable,
} from '@manyun/ticket.ui.maintenance-items';

import {
  GutterWrapper,
  TinyCard,
  TinyDescriptions,
  UserLink,
} from '@manyun/dc-brain.legacy.components';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  getMaintainConfigInfoActionCreator,
  ticketConfigActions,
} from '@manyun/dc-brain.legacy.redux/actions/ticketConfigActions';

function MaintainDetail({
  detail,
  match,
  getInfo,
  mode,
  syncCommonData,
  history,
  resetMaintenanceConfig,
  tools,
  securityStds,
  maintenanceItems,
}) {
  useEffect(() => {
    syncCommonData({ strategy: { deviceCategory: 'IF_NULL', ticketTypes: 'IF_NULL' } });
  }, [syncCommonData]);

  useEffect(() => {
    getInfo({ id: match.params.id, mode });
  }, [getInfo, match.params.id, mode]);

  useEffect(() => {
    return () => {
      resetMaintenanceConfig();
    };
  }, [resetMaintenanceConfig]);

  return (
    <>
      <GutterWrapper mode="vertical" style={{ paddingBottom: 48 }}>
        <TinyCard title="基本信息">
          <TinyDescriptions
            column={5}
            descriptionsItems={[
              {
                label: '所属专业',
                value: detail.maintenanceTypeName,
              },
              {
                label: '设备类型',
                value: (
                  <Space
                    size={0}
                    wrap
                    split={<Divider type="vertical" spaceSize="mini" emphasis />}
                  >
                    {detail?.deviceTypeList?.map((info, index) => (
                      <span key={info.deviceTypeName}>{info.deviceTypeName}</span>
                    ))}
                  </Space>
                ),
              },
              {
                label: 'MOP名称',
                value: detail.configName,
              },
              {
                label: '创建时间',
                value: detail.gmtCreate
                  ? moment(detail.gmtCreate).format('YYYY-MM-DD HH:mm:ss')
                  : '',
              },
              {
                label: '创建人',
                value: <UserLink userId={detail.creatorId} userName={detail.creatorName} />,
              },
            ]}
          />
        </TinyCard>
        <TinyCard title="工具仪器">
          <ToolsTable showActionsColumn={false} mode={mode} dataSource={tools.tableData} />
        </TinyCard>
        <TinyCard title="安全标准">
          <SecurityStandsTable
            showActionsColumn={false}
            mode={mode}
            dataSource={securityStds.tableData}
          />
        </TinyCard>
        <TinyCard title="维护内容">
          <MaintenancesTable
            showActionsColumn={false}
            mode={mode}
            dataSource={maintenanceItems.tableData}
            maintenanceType={detail.maintenanceType}
          />
        </TinyCard>
      </GutterWrapper>
    </>
  );
}

const mapStateToProps = ({
  ticketConfig: {
    maintain: { detail, tools, securityStds, maintenanceItems },
  },
  common: { deviceCategory, ticketTypes },
}) => {
  const info = {
    ...detail,
    maintenanceTypeName: detail.maintenanceType,
    deviceName: detail.deviceType,
  };
  if (
    deviceCategory &&
    deviceCategory.normalizedList &&
    deviceCategory.normalizedList[detail.deviceType]
  ) {
    info.deviceName = deviceCategory.normalizedList[detail.deviceType].metaName;
  }
  if (
    ticketTypes &&
    ticketTypes.normalizedList &&
    ticketTypes.normalizedList[detail.maintenanceType]
  ) {
    info.maintenanceTypeName = ticketTypes.normalizedList[detail.maintenanceType].taskValue;
  }
  return {
    detail: info,
    tools,
    securityStds,
    maintenanceItems,
  };
};

const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
  getInfo: getMaintainConfigInfoActionCreator,
  resetMaintenanceConfig: ticketConfigActions.resetMaintenanceConfig,
};

export default connect(mapStateToProps, mapDispatchToProps)(MaintainDetail);
