import React, { useCallback, useEffect } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Divider } from '@manyun/base-ui.ui.divider';
import { message } from '@manyun/base-ui.ui.message';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';

import { DeviceTypeText } from '@manyun/resource-hub.ui.device-type-text';
import { ConfigRangeType } from '@manyun/ticket.model.task';
import {
  generateMaintainConfigDetailRoutePath,
  generateMaintainCopyLocation,
} from '@manyun/ticket.route.ticket-routes';

import { Ellipsis, TinyTable, UserLink } from '@manyun/dc-brain.legacy.components';
import {
  deleteMaintainConfigActionCreator,
  getMaintainDataActionCreator,
  setMaintainPaginationThenGetDataActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/ticketConfigActions';
import * as generateUrls from '@manyun/dc-brain.legacy.utils/urls';

export function DataTable({
  loading,
  data,
  total,
  pageNum,
  pageSize,
  getData,
  setPagination,
  deleteConfig,
  deviceCategory,
  pattern,
  selectedMaintainConfig,
  setSelectedMaintainConfig,
  maintenanceType,
  deviceType,
  searchValues,
  ticketTypes,
  effectType,
}) {
  useEffect(() => {
    getData({ effectType });
  }, [getData, effectType]);

  useEffect(() => {
    if (pattern) {
      if (
        maintenanceType === searchValues.maintenanceType.value &&
        deviceType === searchValues.deviceType.value
      ) {
        getData({ effectType });
      }
    }
  }, [
    getData,
    maintenanceType,
    deviceType,
    pattern,
    searchValues.maintenanceType.value,
    searchValues.deviceType.value,
    effectType,
  ]);

  const paginationChangeHandler = useCallback(
    (current, size) => {
      setPagination({ pageNum: current, pageSize: size, effectType });
    },
    [setPagination, effectType]
  );

  return (
    <TinyTable
      rowKey="id"
      columns={getColumns({ deleteConfig, deviceCategory, pattern, ticketTypes, effectType })}
      align="left"
      dataSource={data}
      loading={loading}
      actions={
        pattern
          ? null
          : [
              <Button
                key="create"
                type="primary"
                href={generateUrls.generateMaintainConfigCreateLocation()}
              >
                新建MOP
              </Button>,
            ]
      }
      pagination={{
        total,
        current: pageNum,
        pageSize,
        onChange: paginationChangeHandler,
      }}
      selectRowsSpreadPage={pattern ? true : false}
      rowSelection={
        pattern
          ? {
              type: 'radio',
              selectedRowKeys:
                selectedMaintainConfig.length > 0 ? selectedMaintainConfig.map(({ id }) => id) : [],
              onChange: (keys, rows) => {
                let tmp = false;
                let subJob = false;

                if (rows.length >= 2) {
                  rows.forEach((item, index) => {
                    if (item.maintenanceType !== maintenanceType) {
                      tmp = true;
                    }
                    if (index > 0 && item.deviceType !== rows[0].deviceType) {
                      subJob = true;
                    }
                  });
                }
                if (tmp) {
                  message.error('只可以选择同一种维护类型的配置！');
                } else if (subJob) {
                  message.error('只可以选择同一种设备类型的配置！');
                } else {
                  setSelectedMaintainConfig(rows);
                }
              },
              getCheckboxProps: record => {
                return {
                  disabled: (() => {
                    if (record.maintenanceType !== maintenanceType) {
                      return false;
                    }
                    if (
                      selectedMaintainConfig.length &&
                      record.deviceType !== selectedMaintainConfig[0].deviceType
                    ) {
                      return true;
                    }
                    return false;
                  })(),
                };
              },
            }
          : null
      }
    />
  );
}

const mapStateToProps = ({
  ticketConfig: {
    maintain: {
      loading,
      data,
      total,
      pagination: { pageNum, pageSize },
      searchValues,
    },
  },
  common: { deviceCategory, ticketTypes },
}) => {
  return {
    loading,
    data,
    total,
    pageNum,
    pageSize,
    searchValues,
    deviceCategory,
    ticketTypes,
  };
};
const mapDispatchToProps = {
  getData: getMaintainDataActionCreator,
  setPagination: setMaintainPaginationThenGetDataActionCreator,
  deleteConfig: deleteMaintainConfigActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(DataTable);

const getColumns = ({ deleteConfig, deviceCategory, pattern, ticketTypes, effectType }) => {
  let columns = [];
  if (pattern) {
    columns = [
      {
        title: 'MOP名称',
        dataIndex: 'configName',
      },
      {
        title: '所属专业',
        dataIndex: 'maintenanceType',
        render: maintenanceType => {
          if (
            ticketTypes &&
            ticketTypes.normalizedList &&
            ticketTypes.normalizedList[maintenanceType]
          ) {
            return ticketTypes.normalizedList[maintenanceType].taskValue;
          }

          return maintenanceType;
        },
      },
      {
        title: '三级分类',
        dataIndex: 'deviceTypeList',
        maxWidth: '560',
        ellipsis: true,
        render: deviceTypeList => {
          return deviceTypeList.map(item => <DeviceTypeText key={item} code={item} />);
        },
      },
      {
        title: '维护项条数',
        dataIndex: 'itemCount',
      },
    ];
  } else {
    columns = [
      {
        title: 'MOP名称',
        dataIndex: 'configName',
        render(configName, record) {
          return (
            <Link
              type="link"
              target="_blank"
              to={generateMaintainConfigDetailRoutePath({
                id: record.id,
                name: record.configName,
              })}
            >
              <Ellipsis lines={1} tooltip>
                {configName}
              </Ellipsis>
            </Link>
          );
        },
      },
      {
        title: '所属专业',
        dataIndex: 'maintenanceType',
        render: maintenanceType => {
          if (
            ticketTypes &&
            ticketTypes.normalizedList &&
            ticketTypes.normalizedList[maintenanceType]
          ) {
            return ticketTypes.normalizedList[maintenanceType].taskValue;
          }

          return maintenanceType;
        },
      },
      {
        title: '资产分类',
        dataIndex: 'deviceTypeList',
        width: 160,
        ellipsis: true,
        render: deviceTypeList => {
          const text = deviceTypeList?.map((code, index) => (
            <span key={code}>
              <DeviceTypeText code={code} />
              {index + 1 !== deviceTypeList.length && (
                <Divider type="vertical" spaceSize="mini" emphasis />
              )}
            </span>
          ));
          return (
            <Tooltip placement="topLeft" title={text}>
              {text}
            </Tooltip>
          );
        },
      },
      {
        title: '创建时间',
        dataIndex: 'gmtCreate',
        dataType: 'datetime',
      },
      {
        title: '维护项条数',
        dataIndex: 'itemCount',
      },
      {
        title: '创建人',
        dataIndex: 'creatorId',
        render: (creatorId, record) => (
          <UserLink userId={creatorId} userName={record.creatorName} />
        ),
      },
      {
        title: '操作',
        dataIndex: 'inspectId',
        width: 160,
        fixed: 'right',
        render: (__, record) => (
          <span>
            <Link
              target="_blank"
              to={generateUrls.generateMaintainConfigEditlocation({
                id: record.id,
                name: record.configName,
              })}
            >
              编辑
            </Link>
            <Divider type="vertical" />
            <Link
              target="_blank"
              to={generateMaintainCopyLocation({
                id: record.id,
                name: record.configName,
              })}
            >
              复制
            </Link>
            <Divider type="vertical" />
            <DeleteConfirm
              variant="popconfirm"
              targetName={record.configName}
              onOk={() => deleteConfig({ maintenanceConfigId: record.id, effectType })}
            >
              <Button type="link" compact>
                删除
              </Button>
            </DeleteConfirm>
          </span>
        ),
      },
    ];
  }

  return effectType === ConfigRangeType.Block
    ? [{ title: '楼栋', dataIndex: 'effectDomain' }, ...columns]
    : columns;
};
