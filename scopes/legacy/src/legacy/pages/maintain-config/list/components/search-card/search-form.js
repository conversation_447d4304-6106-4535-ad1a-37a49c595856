import React from 'react';
import { connect } from 'react-redux';

import { FiltersForm, Form, Select } from '@galiojs/awesome-antd';

import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Input } from '@manyun/base-ui.ui.input';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import { ConfigRangeType } from '@manyun/ticket.model.task';

import { AssetClassificationApiTreeSelect } from '@manyun/dc-brain.legacy.components';
import {
  getMaintainDataActionCreator,
  resetMaintainSearchValuesActionCreator,
  ticketConfigActions,
} from '@manyun/dc-brain.legacy.redux/actions/ticketConfigActions';

export function SearchForm({
  onReset,
  onSearch,
  updateSearchValues,
  searchValues,
  maintenanceTypeTreeData,
  effectType,
}) {
  const [form] = Form.useForm();
  const items = [
    { label: 'MOP名称', name: 'configName', control: <Input allowClear /> },
    {
      label: '所属专业',
      name: 'maintenanceType',
      control: (
        <Select allowClear>
          {maintenanceTypeTreeData.map(({ taskType, taskValue }) => {
            return (
              <Select.Option key={taskType} value={taskType}>
                {taskValue}
              </Select.Option>
            );
          })}
        </Select>
      ),
    },
    {
      label: '资产分类',
      name: 'deviceType',
      control: (
        <AssetClassificationApiTreeSelect
          dataType={['space', 'snDevice']}
          category="allCategory"
          requestOnDidMount
          allowClear
          disabledDepths={[0, 1]}
        />
      ),
    },
    {
      label: '创建时间',
      name: 'createTimeRange',
      span: 2,
      control: (
        <DatePicker.RangePicker
          format="YYYY-MM-DD HH:mm:ss"
          showTime
          placeholder={['开始时间', '结束时间']}
        />
      ),
    },
    {
      label: '创建人',
      name: 'creatorId',
      control: <UserSelect allowClear />,
    },
  ];

  return (
    <FiltersForm
      form={form}
      items={
        effectType === ConfigRangeType.Block
          ? [
              {
                label: '机房楼栋',
                name: 'effectDomainList',
                control: (
                  <LocationTreeSelect
                    style={{ width: 218 }}
                    multiple
                    authorizedOnly
                    disabledTypes={['IDC']}
                    allowClear
                  />
                ),
              },
              ...items,
            ]
          : items
      }
      fields={Object.keys(searchValues).map(name => {
        const field = searchValues[name];
        return {
          ...field,
          // name 为数组形式
          name: name.split('.'),
        };
      })}
      onFieldsChange={changedFields => {
        updateSearchValues(
          changedFields.reduce((mapper, field) => {
            // field.name 为数组形式，老代码需要的是字符串形式
            const name = field.name.join('.');
            mapper[name] = {
              ...field,
              name,
            };
            return mapper;
          }, {})
        );
      }}
      onSearch={() => onSearch({ shouldResetPageNum: true, effectType })}
      onReset={() => onReset(effectType)}
    />
  );
}

const mapStateToProps = ({
  ticketConfig: {
    maintain: { searchValues },
  },
}) => {
  return { searchValues };
};

const mapDispatchToProps = {
  updateSearchValues: ticketConfigActions.updateMaintainSearchValues,
  onReset: resetMaintainSearchValuesActionCreator,
  onSearch: getMaintainDataActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(SearchForm);
