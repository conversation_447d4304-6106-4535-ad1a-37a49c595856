import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';

import { Card } from '@manyun/base-ui.ui.card';
import { Space } from '@manyun/base-ui.ui.space';

import { ConfigRangeType } from '@manyun/ticket.model.task';
import { ConfigRangeTypeRadioButton } from '@manyun/ticket.ui.config-range-type-radio-button';

import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';

import DataCard from './components/data-card';
import SearchCard from './components/search-card';

export function MaintainConfigList({ syncCommonData, maintenanceTypeTreeData }) {
  useEffect(() => {
    syncCommonData({ strategy: { deviceCategory: 'IF_NULL', ticketTypes: 'IF_NULL' } });
  }, [syncCommonData]);
  const [effectType, setEffectType] = useState(ConfigRangeType.Block);

  return (
    <Card
      title="维护MOP配置"
      extra={
        <ConfigRangeTypeRadioButton
          configRangeType={effectType}
          ticketPrefix="MOP"
          onChange={setEffectType}
        />
      }
    >
      <Space direction="vertical">
        <SearchCard maintenanceTypeTreeData={maintenanceTypeTreeData} effectType={effectType} />
        <DataCard maintenanceTypeTreeData={maintenanceTypeTreeData} effectType={effectType} />
      </Space>
    </Card>
  );
}

const mapStateToProps = ({ common: { deviceCategory, ticketTypes } }) => {
  let maintenanceTypeTreeData = [];
  if (ticketTypes && ticketTypes.treeList && ticketTypes.treeList.length) {
    ticketTypes.treeList.forEach(({ taskType, children }) => {
      if (taskType === 'MAINTENANCE' && children && children.length) {
        maintenanceTypeTreeData = children;
      }
    });
  }
  return { deviceCategory, maintenanceTypeTreeData };
};

const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(MaintainConfigList);
