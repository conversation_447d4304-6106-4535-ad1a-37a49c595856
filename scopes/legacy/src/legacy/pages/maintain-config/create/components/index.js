import React, { useCallback, useEffect, useState } from 'react';
import { connect } from 'react-redux';
import useDeepCompareEffect from 'use-deep-compare-effect';

import { Radio } from '@manyun/base-ui.ui.radio';
import { Space } from '@manyun/base-ui.ui.space';
import {
  MaintenancesTable,
  SecurityStandsTable,
  ToolsTable,
} from '@manyun/ticket.ui.maintenance-items';

import { MaintainConfigButtonModal } from '@manyun/dc-brain.legacy.components';
import { ticketConfigActions } from '@manyun/dc-brain.legacy.redux/actions/ticketConfigActions';

import { MAINTAIN_CONFIG_CONTENT_KEY_MAP, MAINTAIN_CONFIG_CONTENT_OPTIONS } from '../../constants';

/**
 * @typedef {object} Props
 * @property {string} showInportButton  是否显示 导入设备按钮，showInportButton = false 时 不显示。showInportButton = true 时显示
 * @property {string} maintenanceType  维护类型
 * @property {string} deviceType  设备类型
 * @property {function} recoverConfigTableContent  返回配置表格数据
 */

function RadioTables({
  showInportButton,
  reset,
  maintenanceType,
  deviceType,
  toolsTableData,
  securityStandsTableData,
  clearData,
  maintenanceItemsTableData,
  recoverConfigTableContent,
}) {
  const [contentModule, setContentModule] = useState(MAINTAIN_CONFIG_CONTENT_KEY_MAP.tools);

  const [tools, setTools] = useState({ tableData: [], editingRowKey: null, deleteByCancel: false });

  const [securityStds, setSecurityStands] = useState({
    tableData: [],
    editingRowKey: null,
    deleteByCancel: false,
  });

  const [maintenanceItems, setMaintenanceItems] = useState({
    tableData: [],
    editingRowKey: null,
    deleteByCancel: false,
  });

  const [importTables, setImportTables] = useState({
    tools: [],
    securityStds: [],
    maintenanceItems: [],
  });

  const [optConfigs, setOptConfigs] = useState([]);

  const _clearMaintenanceTable = useCallback(() => {
    setOptConfigs([]);
    setTools({
      tableData: [],
      editingRowKey: null,
      deleteByCancel: false,
    });
    setMaintenanceItems({
      tableData: [],
      editingRowKey: null,
      deleteByCancel: false,
    });
    setSecurityStands({
      tableData: [],
      editingRowKey: null,
      deleteByCancel: false,
    });
  }, []);

  useEffect(() => {
    _clearMaintenanceTable();
  }, [_clearMaintenanceTable, clearData]);

  useDeepCompareEffect(() => {
    /**维护类型切换、三级分类切换， 清空选中的导入数据、清空 维护项表格所有数据 */
    // _clearMaintenanceTable();
    setMaintenanceItems({
      tableData: maintenanceItems.tableData.map(i => ({ ...i, isLimitModel: '', models: [] })),
      editingRowKey: null,
      deleteByCancel: false,
    });
  }, [_clearMaintenanceTable, deviceType, maintenanceType]);

  useEffect(() => {
    const { tools, securityStds, maintenanceItems } = importTables;
    setTools(value => {
      const filterImportData = value.tableData.filter(source => !source);
      return { ...value, tableData: [...filterImportData, ...tools], editingRowKey: null };
    });
    setSecurityStands(value => {
      const filterImportData = value.tableData.filter(source => !source);
      return { ...value, tableData: [...filterImportData, ...securityStds], editingRowKey: null };
    });
    setMaintenanceItems(value => {
      const filterImportData = value.tableData.filter(source => !source);
      return {
        ...value,
        tableData: [...filterImportData, ...maintenanceItems],
        editingRowKey: null,
      };
    });
  }, [importTables]);

  useEffect(() => {
    return () => {
      reset();
    };
  }, [reset]);

  useEffect(() => {
    if (!toolsTableData || !toolsTableData.length) {
      return;
    }
    setTools(value => {
      return {
        tableData: toolsTableData,
        editingRowKey: value.editingRowKey,
        deleteByCancel: value.deleteByCancel,
      };
    });
  }, [toolsTableData]);

  useEffect(() => {
    if (!securityStandsTableData || !securityStandsTableData.length) {
      return;
    }
    setSecurityStands(value => {
      return {
        tableData: securityStandsTableData,
        editingRowKey: value.editingRowKey,
        deleteByCancel: value.deleteByCancel,
      };
    });
  }, [securityStandsTableData]);

  useEffect(() => {
    if (!maintenanceItemsTableData || !maintenanceItemsTableData.length) {
      return;
    }
    setMaintenanceItems(value => {
      return {
        tableData: maintenanceItemsTableData,
        editingRowKey: value.editingRowKey,
        deleteByCancel: value.deleteByCancel,
      };
    });
  }, [maintenanceItemsTableData]);

  useEffect(() => {
    if (contentModule === MAINTAIN_CONFIG_CONTENT_KEY_MAP.maintenanceItems) {
      setMaintenanceItems(value => {
        const { tableData, ...rest } = value;
        return {
          ...rest,
          tableData:
            Array.isArray(tableData) && tableData.length > 0
              ? tableData.filter(item => item.std)
              : tableData,
        };
      });
    }
  }, [contentModule]);
  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      {showInportButton && (
        <div>
          <MaintainConfigButtonModal
            maintenanceType={maintenanceType}
            setImportMaintenanceConfigTables={table => {
              setImportTables({
                tools: table.tools,
                securityStds: table.securityStds,
                maintenanceItems: table.maintenanceItems,
              });
              recoverConfigTableContent({
                tools: {
                  tableData: table.tools,
                  editingRowKey: null,
                  deleteByCancel: false,
                },
                securityStds: {
                  tableData: table.securityStds,
                  editingRowKey: null,
                  deleteByCancel: false,
                },
                maintenanceItems: {
                  tableData: table.maintenanceItems,
                  editingRowKey: null,
                  deleteByCancel: false,
                },
                deviceTypeList: table.deviceTypeList,
              });
            }}
            optConfigs={optConfigs}
            setOptConfigs={setOptConfigs}
          />
        </div>
      )}

      <Radio.Group
        value={contentModule}
        onChange={({ target: { value } }) => {
          setContentModule(value);
          recoverConfigTableContent({
            tools: { ...tools, editingRowKey: undefined },
            securityStds: { ...securityStds, editingRowKey: undefined },
            maintenanceItems: { ...maintenanceItems, editingRowKey: undefined },
          });
        }}
      >
        {MAINTAIN_CONFIG_CONTENT_OPTIONS.map(({ value, label }) => {
          return (
            <Radio.Button key={value} value={value}>
              {label}
            </Radio.Button>
          );
        })}
      </Radio.Group>

      {contentModule === MAINTAIN_CONFIG_CONTENT_KEY_MAP.tools && (
        <>
          <ToolsTable
            dataSource={tools.tableData}
            maintenancesData={maintenanceItems.tableData}
            editableKey={tools.editingRowKey}
            onChange={(data, newMaintenancesData, editableKey) => {
              const value = {
                tableData: data ? data : tools.tableData,
                editingRowKey: editableKey !== undefined ? editableKey : tools.editingRowKey,
                deleteByCancel: tools.deleteByCancel,
              };
              setTools(value);
              recoverConfigTableContent({
                tools: value,
                securityStds: { ...securityStds, editingRowKey: undefined },
                maintenanceItems: {
                  tableData: newMaintenancesData ? newMaintenancesData : maintenanceItems.tableData,
                  editingRowKey: maintenanceItems.editingRowKey,
                  deleteByCancel: maintenanceItems.value,
                },
              });
              if (newMaintenancesData) {
                setMaintenanceItems({
                  tableData: newMaintenancesData,
                  editingRowKey: maintenanceItems.editingRowKey,
                  deleteByCancel: maintenanceItems.value,
                });
              }
            }}
          />
        </>
      )}
      {contentModule === MAINTAIN_CONFIG_CONTENT_KEY_MAP.securityStds && (
        <>
          <SecurityStandsTable
            dataSource={securityStds.tableData}
            editableKey={securityStds.editingRowKey}
            onChange={(data, editableKey) => {
              const value = {
                tableData: data ? data : securityStds.tableData,
                editingRowKey: editableKey !== undefined ? editableKey : securityStds.editingRowKey,
                deleteByCancel: false,
              };
              setSecurityStands(value);
              recoverConfigTableContent({
                tools: { ...tools, editingRowKey: undefined },
                securityStds: value,
                maintenanceItems: { ...maintenanceItems, editingRowKey: undefined },
              });
            }}
          />
        </>
      )}
      {contentModule === MAINTAIN_CONFIG_CONTENT_KEY_MAP.maintenanceItems && (
        <>
          <MaintenancesTable
            maintenanceType={maintenanceType}
            dataSource={maintenanceItems.tableData}
            editableKey={maintenanceItems.editingRowKey}
            deviceType={deviceType}
            toolsData={tools.tableData}
            onChange={(data, editableKey) => {
              const value = {
                tableData: data ? data : maintenanceItems.tableData,
                editingRowKey:
                  editableKey !== undefined ? editableKey : maintenanceItems.editingRowKey,
                deleteByCancel: false,
              };
              setMaintenanceItems(value);
              recoverConfigTableContent({
                tools: { ...tools, editingRowKey: undefined },
                securityStds: { ...securityStds, editingRowKey: undefined },
                maintenanceItems: value,
              });
            }}
          />
        </>
      )}
    </Space>
  );
}
const mapDispatchToProps = {
  reset: ticketConfigActions.resetMaintanceSearchValuesAndData,
};
export default connect(null, mapDispatchToProps)(RadioTables);
