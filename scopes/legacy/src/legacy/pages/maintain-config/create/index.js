import Form from '@ant-design/compatible/es/form';
import { Select } from '@galiojs/awesome-antd';
import React, { useEffect } from 'react';
import { connect } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { Explanation } from '@manyun/base-ui.ui.explanation';
import { Input } from '@manyun/base-ui.ui.input';
import { Radio } from '@manyun/base-ui.ui.radio';
import { DeviceTypeCascader } from '@manyun/resource-hub.ui.device-type-cascader';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import { CONFIG_RANGE_KEY_MAP, ConfigRangeType } from '@manyun/ticket.model.task';

import { FooterToolBar, GutterWrapper, TinyCard } from '@manyun/dc-brain.legacy.components';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  createMaintainConfigActionCreator,
  editMaintainConfigActionCreator,
  getMaintainConfigInfoActionCreator,
  ticketConfigActions,
} from '@manyun/dc-brain.legacy.redux/actions/ticketConfigActions';

import RadioTables from './components';

/**
 * 从树形数据中查找给定叶子节点值的所有祖先节点路径信息。
 *
 * @param {Array<Object>} treeNodes - 完整的树形数据数组，每个节点应包含 value, title 和 children 属性。
 * @param {string} targetValue - 要查找的叶子节点的 value。
 * @param {Array<Object>} [currentPath=[]] - 递归过程中积累的当前路径上的节点信息 (e.g., {value, title})。
 * @returns {Object|null} 如果找到，返回一个包含 leafNode, firstCategory, secondCategory 的对象；否则返回 null。
 * {
 * leafNode: { value: '...', title: '...' },
 * firstCategory: { value: '...', title: '...' } | null,
 * secondCategory: { value: '...', title: '...' } | null,
 * }
 */
const getDetailedNodePathInfo = (treeNodes, targetValue, currentPath = []) => {
  for (const node of treeNodes) {
    const newPath = [...currentPath, { value: node.value, title: node.title }];

    if (node.value === targetValue) {
      // 找到目标叶子节点
      const pathInfo = {
        leafNode: { value: node.value, title: node.title },
        firstCategory: null,
        secondCategory: null,
      };

      // 根据路径长度填充一级和二级分类信息
      if (newPath.length >= 2) {
        pathInfo.firstCategory = newPath[0]; // 路径的第一个是第一级
      }
      if (newPath.length >= 3) {
        pathInfo.secondCategory = newPath[1]; // 路径的第二个是第二级
      }
      return pathInfo;
    }

    if (node.children && node.children.length > 0) {
      const foundInfo = getDetailedNodePathInfo(node.children, targetValue, newPath);
      if (foundInfo) {
        return foundInfo;
      }
    }
  }
  return null;
};
function MaintainCreate({
  form: { setFieldsValue, getFieldDecorator, validateFields, getFieldValue },
  syncCommonData,
  maintenanceTypeTreeData,
  createSubBtnloading,
  tools,
  securityStds,
  maintenanceItems,
  mode,
  create,
  edit,
  resetMaintenanceConfig,
  match,
  getInfo,
  history,
  setMaintainTable,
  fieldValues,
}) {
  const [deviceTypeList, setDeviceTypeList] = React.useState([
    {
      deviceType: undefined,
      deviceTypeName: undefined,
      numbered: undefined,
    },
  ]);

  useEffect(() => {
    syncCommonData({ strategy: { deviceCategory: 'IF_NULL', ticketTypes: 'IF_NULL' } });
  }, [syncCommonData]);

  useEffect(() => {
    if (mode === 'edit' || mode === 'copy') {
      getInfo({ id: match.params.id, mode, setDeviceTypeList, getFieldValue });
    }
  }, [getInfo, match.params.id, mode, getFieldValue]);

  useEffect(() => {
    return () => {
      resetMaintenanceConfig();
      setDeviceTypeList([
        {
          deviceType: undefined,
          deviceTypeName: undefined,
          numbered: undefined,
        },
      ]);
    };
  }, [resetMaintenanceConfig]);

  const submit = () => {
    validateFields(async (error, value) => {
      if (error) {
        return;
      }
      if (mode === 'new' || mode === 'copy') {
        create({ mode, deviceTypeList });
      }
      if (mode === 'edit') {
        edit({ deviceTypeList });
      }
    });
  };

  return (
    <GutterWrapper mode="vertical">
      <TinyCard title="基本信息">
        <Form colon={false} labelCol={{ xl: 3 }} wrapperCol={{ xl: 21 }} style={{ width: 848 }}>
          <Form.Item label="适用范围">
            {getFieldDecorator('effectType', {
              rules: [{ required: true, message: '适用范围为必选项！' }],
            })(
              <Radio.Group
                style={{ width: 218 }}
                onChange={() => {
                  setFieldsValue({ effectDomain: undefined });
                }}
              >
                {CONFIG_RANGE_KEY_MAP.map(({ label, value }) => {
                  return (
                    <Radio key={value} value={value}>
                      {label}
                    </Radio>
                  );
                })}
              </Radio.Group>
            )}
          </Form.Item>
          {getFieldValue('effectType') === ConfigRangeType.Block && (
            <Form.Item label="属地楼栋">
              {getFieldDecorator('effectDomain', {
                rules: [{ required: true, message: '属地楼栋为必选项！' }],
              })(
                <LocationTreeSelect
                  style={{ width: 218 }}
                  authorizedOnly
                  disabledTypes={['IDC']}
                  allowClear
                />
              )}
            </Form.Item>
          )}
          <Form.Item label="所属专业">
            {getFieldDecorator('maintenanceType', {
              rules: [{ required: true, message: '所属专业为必选项！' }],
            })(
              <Select
                disabled={mode === 'copy'}
                style={{ width: 200 }}
                onChange={v => {
                  setDeviceTypeList([]);
                  setFieldsValue({ deviceTypeList: [] });
                }}
              >
                {maintenanceTypeTreeData.map(({ taskType, taskValue }) => {
                  return (
                    <Select.Option key={taskType} value={taskType}>
                      {taskValue}
                    </Select.Option>
                  );
                })}
              </Select>
            )}
            <Explanation
              iconType="question"
              tooltip={{ title: '配置只有在确保已录入维护对象情况下才可生效' }}
            />
          </Form.Item>
          <Form.Item label="资产分类">
            {getFieldDecorator('deviceTypeList', {
              rules: [{ required: true, message: '资产分类为必选项！' }],
            })(
              <DeviceTypeCascader
                style={{ width: 200 }}
                treeDefaultExpandAll
                dataType={
                  getFieldValue('maintenanceType') === 'M_SJ' ? ['noSnDevice'] : ['snDevice']
                }
                numbered={!(getFieldValue('maintenanceType') === 'M_SJ')}
                disabledTypeList={['C0', 'C1']}
                multiple
                maxTagCount={2}
                onChange={(codes, names, op, treeData) => {
                  const newDeviceTypeList = codes.map((code, index) => {
                    // 获取当前三级节点及其父节点（一级、二级）的详细信息
                    const pathInfo = getDetailedNodePathInfo(treeData, code);

                    let firstCategoryName = '';
                    let firstCategoryCode = '';
                    let secondCategoryName = '';
                    let secondCategoryCode = '';

                    if (pathInfo) {
                      if (pathInfo.firstCategory) {
                        firstCategoryName = pathInfo.firstCategory.title;
                        firstCategoryCode = pathInfo.firstCategory.value;
                      }
                      if (pathInfo.secondCategory) {
                        secondCategoryName = pathInfo.secondCategory.title;
                        secondCategoryCode = pathInfo.secondCategory.value;
                      }
                    }

                    return {
                      deviceType: code, // 三级节点的 value
                      deviceTypeName: names[index], // 三级节点的 label
                      firstCategoryName: firstCategoryName, // 一级分类名称
                      firstCategoryCode,
                      secondCategoryName: secondCategoryName, // 二级分类名称
                      secondCategoryCode,
                      numbered: getFieldValue('maintenanceType') === 'M_SJ' ? undefined : true,
                    };
                  });
                  setDeviceTypeList(newDeviceTypeList);
                }}
              />
            )}
          </Form.Item>

          <Form.Item label="MOP名称">
            {getFieldDecorator('configName', {
              rules: [
                { required: true, whitespace: true, message: 'MOP名称为必填项！' },
                {
                  max: 30,
                  message: '最多输入 30 个字符！',
                },
              ],
            })(<Input allowClear style={{ width: 500 }} />)}
          </Form.Item>
        </Form>
      </TinyCard>
      <TinyCard style={{ marginBottom: '40px' }}>
        <RadioTables
          // showInportButton={true}
          // maintenanceType="设施维护"
          deviceType={fieldValues.deviceTypeList?.value?.map(item =>
            item.thirdCategorycode
              ? item.thirdCategorycode
              : typeof +item === 'number'
                ? item
                : item?.value
          )}
          toolsTableData={mode === 'edit' || mode === 'copy' ? tools.tableData : null}
          securityStandsTableData={
            mode === 'edit' || mode === 'copy' ? securityStds.tableData : null
          }
          maintenanceItemsTableData={
            mode === 'edit' || mode === 'copy' ? maintenanceItems.tableData : null
          }
          recoverConfigTableContent={value => {
            setMaintainTable(value);
          }}
          maintenanceType={getFieldValue('maintenanceType')}
        />
      </TinyCard>
      <FooterToolBar>
        <GutterWrapper>
          <Button type="primary" loading={createSubBtnloading} onClick={submit}>
            提交
          </Button>
        </GutterWrapper>
      </FooterToolBar>
    </GutterWrapper>
  );
}

const createFileds = Form.create({
  onFieldsChange(props, changedFields) {
    const fields = changedFields;
    if (props.mode === 'new') {
      props.upDateCreateOptionValues(fields);
    }

    if (props.mode === 'edit' || props.mode === 'copy') {
      props.upDateEditOptionValues(fields);
    }
  },
  mapPropsToFields(props) {
    return {
      configName: Form.createFormField(props.fieldValues.configName),
      deviceTypeList: Form.createFormField(props.fieldValues.deviceTypeList),
      maintenanceType: Form.createFormField(props.fieldValues.maintenanceType),
      effectDomain: Form.createFormField(props.fieldValues.effectDomain),
      effectType: Form.createFormField(props.fieldValues.effectType),
    };
  },
});

const mapStateToProps = (
  {
    ticketConfig: {
      maintain: { create, edit, createSubBtnloading },
    },
    common: { deviceCategory, ticketTypes },
  },
  { mode }
) => {
  let fieldValues = create;
  if (mode === 'edit' || mode === 'copy') {
    fieldValues = edit;
  }
  let maintenanceTypeTreeData = [];
  if (ticketTypes && ticketTypes.treeList && ticketTypes.treeList.length) {
    ticketTypes.treeList.forEach(({ taskType, children }) => {
      if (taskType === 'MAINTENANCE' && children && children.length) {
        maintenanceTypeTreeData = children;
      }
    });
  }
  return {
    maintenanceTypeTreeData,
    deviceCategory,
    fieldValues,
    createSubBtnloading,
    tools: fieldValues.tools,
    securityStds: fieldValues.securityStds,
    maintenanceItems: fieldValues.maintenanceItems,
  };
};

const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
  upDateCreateOptionValues: ticketConfigActions.upDateMaintainCreateOptionValues,
  create: createMaintainConfigActionCreator,
  edit: editMaintainConfigActionCreator,
  resetMaintenanceConfig: ticketConfigActions.resetMaintenanceConfig,
  getInfo: getMaintainConfigInfoActionCreator,
  upDateEditOptionValues: ticketConfigActions.upDateEditOptionValues,
  setMaintainTable: ticketConfigActions.setMaintainTable,
};

export default connect(mapStateToProps, mapDispatchToProps)(createFileds(MaintainCreate));
