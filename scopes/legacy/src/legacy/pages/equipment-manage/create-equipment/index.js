import QuestionCircleOutlined from '@ant-design/icons/es/icons/QuestionCircleOutlined';
import { saveAs } from 'file-saver';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Upload } from '@manyun/dc-brain.ui.upload';

import { GutterWrapper, StatusText, TinyCard, TinyTable } from '@manyun/dc-brain.legacy.components';
import { EQUIPMENT_LIST } from '@manyun/dc-brain.legacy.constants/urls';
import { equipmentActions } from '@manyun/dc-brain.legacy.redux/actions/equipmentActions';
import { redirectActionCreator } from '@manyun/dc-brain.legacy.redux/actions/routerActions';
import {
  downloadGridModel,
  uploadEquipment,
} from '@manyun/dc-brain.legacy.services/equipmentService';

const columns = ctx => [
  {
    title: '行号',
    dataIndex: 'rowTag',
    fixed: 'left',
  },
  {
    title: '设备编号',
    dataIndex: ['errDto', 'name'],
    render: (guid, { errMessage }) => getToolTilp(guid, errMessage, 'name'),
  },
  {
    title: '编码',
    dataIndex: ['errDto', 'guid'],
    render: (guid, { errMessage }) => getToolTilp(guid, errMessage, 'guid'),
  },
  {
    title: 'SN',
    dataIndex: ['errDto', 'serialNumber'],
    render: (serialNumber, { errMessage }) => getToolTilp(serialNumber, errMessage, 'serialNumber'),
  },
  {
    title: '设备名称',
    dataIndex: ['errDto', 'deviceLabel'],
    render: (deviceLabel, { errMessage }) => getToolTilp(deviceLabel, errMessage, 'deviceLabel'),
  },
  {
    title: '映射标签',
    dataIndex: ['errDto', 'tag'],
    render: (tag, { errMessage }) => getToolTilp(tag, errMessage, 'tag'),
  },
  {
    title: '集团设备编号',
    dataIndex: ['errDto', 'groupName'],
    render: (groupName, { errMessage }) => getToolTilp(groupName, errMessage, 'groupName'),
  },
  {
    title: '机房编号',
    dataIndex: ['errDto', 'idcTag'],
    render: (idcTag, { errMessage }) => getToolTilp(idcTag, errMessage, 'idcTag'),
  },
  {
    title: '楼栋编号',
    dataIndex: ['errDto', 'blockTag'],
    render: (blockTag, { errMessage }) => getToolTilp(blockTag, errMessage, 'blockTag'),
  },
  {
    title: '包间编号',
    dataIndex: ['errDto', 'roomTag'],
    render: (roomTag, { errMessage }) => getToolTilp(roomTag, errMessage, 'roomTag'),
  },
  {
    title: '厂商',
    dataIndex: ['errDto', 'vendor'],
    render: (vendor, { errMessage }) => getToolTilp(vendor, errMessage, 'vendor'),
  },

  {
    title: '型号',
    dataIndex: ['errDto', 'productModel'],
    render: (productModel, { errMessage }) => getToolTilp(productModel, errMessage, 'productModel'),
  },
  {
    title: '三级分类',
    dataIndex: ['errDto', 'deviceType'],
    render: (deviceType, { errMessage }) => getToolTilp(deviceType, errMessage, 'deviceType'),
  },
  {
    title: '购买日期',
    dataIndex: ['errDto', 'purchaseTime'],
    render: (purchaseTime, { errMessage }) => getToolTilp(purchaseTime, errMessage, 'purchaseTime'),
  },
  {
    title: '供应商简称',
    dataIndex: ['errDto', 'supplyVendor'],
    render: (supplyVendor, { errMessage }) => getToolTilp(supplyVendor, errMessage, 'supplyVendor'),
  },
  {
    title: '过保日期',
    dataIndex: ['errDto', 'warrantyTime'],
    render: (warrantyTime, { errMessage }) => getToolTilp(warrantyTime, errMessage, 'warrantyTime'),
  },

  {
    title: '维保厂商',
    dataIndex: ['errDto', 'warrantyVendor'],
    render: (warrantyVendor, { errMessage }) =>
      getToolTilp(warrantyVendor, errMessage, 'warrantyVendor'),
  },
  {
    title: '设备状态',
    dataIndex: ['errDto', 'operationStatus'],
    render: (operationStatus, { errMessage }) =>
      getToolTilp(operationStatus, errMessage, 'operationStatus'),
  },
  {
    title: '资产状态',
    dataIndex: ['errDto', 'assetStatus'],
    render: (assetStatus, { errMessage }) => getToolTilp(assetStatus, errMessage, 'assetStatus'),
  },
  {
    title: '验收日期',
    dataIndex: ['errDto', 'checkTime'],
    render: (checkTime, { errMessage }) => getToolTilp(checkTime, errMessage, 'checkTime'),
  },
  {
    title: '启用日期',
    dataIndex: ['errDto', 'enableTime'],
    render: (enableTime, { errMessage }) => getToolTilp(enableTime, errMessage, 'enableTime'),
  },
  {
    title: '报废日期',
    dataIndex: ['errDto', 'scrapTime'],
    render: (scrapTime, { errMessage }) => getToolTilp(scrapTime, errMessage, 'scrapTime'),
  },
  {
    title: '所属线路',
    dataIndex: ['errDto', 'powerLine'],
    render: (powerLine, { errMessage }) => getToolTilp(powerLine, errMessage, 'powerLine'),
  },
  {
    title: '所属单元',
    dataIndex: ['errDto', 'unit'],
    render: (unit, { errMessage }) => getToolTilp(unit, errMessage, 'unit'),
  },
  {
    title: '扩展位置',
    dataIndex: ['errDto', 'extendPosition'],
    render: (extendPosition, { errMessage }) =>
      getToolTilp(extendPosition, errMessage, 'extendPosition'),
  },
  {
    title: '排序序号',
    dataIndex: ['errDto', 'sort'],
    render: (sort, { errMessage }) => getToolTilp(sort, errMessage, 'sort'),
  },
];
class CreateEquipMent extends Component {
  state = {
    selectedRowKeys: [],
    selectedRows: [],
    importButtonLoading: false,
    pageNo: 1,
    pageSize: 10,
  };

  onSelectChange = (selectedRowKeys, selectedRows) => {
    this.setState({ selectedRowKeys, selectedRows });
  };

  downloadDemo = async () => {
    const { response, filename = 'template.csv' } = await downloadGridModel();
    if (!response) {
      return;
    }
    saveAs(response, filename);
  };

  beforeUpload = (file, fileList) => {
    this.props.importEquipmentSuccess({});
    const fd = new FormData();
    fd.append('file', file);
    this.loadFetchData(fd);
    return false;
  };

  loadFetchData = async fd => {
    this.setState({
      importButtonLoading: true,
    });
    const { response, error } = await uploadEquipment(fd);
    this.props.startimportLoading();
    if (error) {
      message.error(error);
      this.props.stopImportLoading();
      this.setState({
        importButtonLoading: false,
      });
      return;
    }
    if (response) {
      this.props.stopImportLoading();
      this.setState({
        importButtonLoading: false,
      });
      if (response.faultTotal === 0) {
        message.success('导入成功！');
        this.props.redirect(EQUIPMENT_LIST);
      } else {
        this.props.importEquipmentSuccess(response);
      }
    }
  };

  deleteEquip = (row, index) => {
    const { importEquipmentList } = this.props;
    const list = [...importEquipmentList.list];
    list.splice(index, 1);
    this.props.changedatalist({
      data: list,
      total: list.length,
    });
  };

  onChangePage = (pageNo, pageSize) => {
    this.setState({
      pageNo,
      pageSize,
    });
  };

  render() {
    const { importLoading, importEquipmentList } = this.props;
    const { importButtonLoading, pageNo, pageSize } = this.state;
    return (
      <>
        <TinyCard>
          <TinyTable
            rowKey={({ errDto }) => errDto.guid}
            actionsWrapperStyle={{ justifyContent: 'space-between' }}
            actions={[
              <GutterWrapper key="left" flex>
                <Upload
                  key="1"
                  beforeUpload={this.beforeUpload}
                  showUploadList={false}
                  accept=".csv,.xls,.xlsx"
                >
                  <Button type="primary" loading={importButtonLoading}>
                    导入
                  </Button>
                </Upload>

                <Button key="2" onClick={this.downloadDemo}>
                  下载模板
                </Button>
              </GutterWrapper>,
              <div key="right">
                {importEquipmentList.excelCheckErrDtos && (
                  <GutterWrapper flex>
                    <StatusText status="normal">
                      导入总数&nbsp;{importEquipmentList.checkTotal}
                    </StatusText>
                    <StatusText status="normal">
                      正确总数&nbsp;{importEquipmentList.correctTotal}
                    </StatusText>
                    <StatusText status="alarm">
                      错误行数&nbsp;{importEquipmentList.faultTotal}
                    </StatusText>
                  </GutterWrapper>
                )}
              </div>,
            ]}
            dataSource={importEquipmentList.excelCheckErrDtos}
            loading={importLoading}
            columns={columns(this)}
            scroll={{ x: 'max-content' }}
            pagination={{
              total: importEquipmentList.excelCheckErrDtos
                ? importEquipmentList.excelCheckErrDtos.length
                : 0,
              current: pageNo,
              pageSize: pageSize,
              onChange: this.onChangePage,
            }}
          />
        </TinyCard>
      </>
    );
  }
}

const mapStateToProps = ({ equipmentManage: { importLoading, importEquipmentList } }) => ({
  importLoading,
  importEquipmentList,
});

const mapDispatchToProps = {
  changedatalist: equipmentActions.importEquipmentSuccess,
  startimportLoading: equipmentActions.importLoading,
  importEquipmentSuccess: equipmentActions.importEquipmentSuccess,
  stopImportLoading: equipmentActions.failure,
  redirect: redirectActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(CreateEquipMent);

function getToolTilp(value, errMessage, dataType) {
  if (Object.prototype.hasOwnProperty.call(errMessage, dataType)) {
    return (
      <Tooltip title={errMessage[dataType]}>
        <GutterWrapper size="2px" flex center>
          <StatusText style={{ display: 'inline-block' }} status="alarm">
            {value ? value : '--'}
          </StatusText>
          <QuestionCircleOutlined />
        </GutterWrapper>
      </Tooltip>
    );
  }
  return value;
}
