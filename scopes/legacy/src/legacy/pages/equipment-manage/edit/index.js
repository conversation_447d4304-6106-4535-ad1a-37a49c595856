import Form from '@ant-design/compatible/es/form';
import { ApiSelect, Select } from '@galiojs/awesome-antd';
import debounce from 'lodash/debounce';
import omit from 'lodash/omit';
import omitBy from 'lodash/omitBy';
import moment from 'moment';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { Collapse } from '@manyun/base-ui.ui.collapse';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { VendorModelSelect } from '@manyun/crm.ui.vendor-model-select';
import { VendorSelect } from '@manyun/crm.ui.vendor-select';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { fetchDeviceAssetInfo } from '@manyun/resource-hub.service.fetch-device-asset-info';
import { fetchMetaDataByTypeWeb } from '@manyun/resource-hub.service.fetch-meta-data-by-type';
import { getComponent, getInitialValue } from '@manyun/resource-hub.ui.spec-form-items';
import { VALUE_TYPE_KEY_MAP } from '@manyun/resource-hub.ui.spec-select';

import {
  AssetClassificationApiTreeSelect,
  FooterToolBar,
  GutterWrapper,
  LocationCascader,
  TinyEmpty,
} from '@manyun/dc-brain.legacy.components';
import { TOPOLOGY_TYPE_KEY_MAP } from '@manyun/dc-brain.legacy.constants';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  equipmentActions,
  fetchEditEquipment,
  getDeviceInfo,
  searchDeviceSN,
} from '@manyun/dc-brain.legacy.redux/actions/equipmentActions';
import { redirectActionCreator } from '@manyun/dc-brain.legacy.redux/actions/routerActions';
import { equipmentManageService, specService } from '@manyun/dc-brain.legacy.services';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';

const { Panel } = Collapse;
const { Option } = Select;

class EditEquipMent extends Component {
  state = {
    specInfo: [],
    /** 设备是否转固定资产 */
    isDeviceBelongtoFixedAssets: false,
    /** 设备属于转固定资产信息 */
    deviceFixedAssets: {
      /** 报废日期 */
      scrapDate: null,
      /** 含税金额-购买价格 */
      priceWithTax: undefined,
      /** 购买/采购日期 */
      purchaseDate: undefined,
    },
  };

  componentDidMount() {
    this.props.syncCommonData({ strategy: { space: 'FORCED', deviceCategory: 'IF_NULL' } });
    const {
      match: { params },
    } = this.props;
    this.props.getDeviceInfo({ guid: params.id });
    this.fetchDeviceAssets(params.id);
  }

  componentDidUpdate(prevProps) {
    const { editEquipmentMessList } = this.props;
    if (!prevProps.editEquipmentMessList && editEquipmentMessList) {
      this.fetchSpecInfo();
    }
  }

  componentWillUnmount() {
    this.props.editEquipmentMess(null);
  }

  fetchSpecInfo = async () => {
    const { editEquipmentMessList } = this.props;
    const { deviceType, guid } = editEquipmentMessList;

    const { response } = await specService.fetchSpecList({
      deviceType,
      modelId: guid,
    });

    if (response) {
      this.setState({
        specInfo: response.data,
      });
    }
  };

  fetchDeviceAssets = guid => {
    fetchDeviceAssetInfo({
      deviceGuid: guid,
    }).then(({ data, error }) => {
      if (error) {
        message.error(error.message);
        return;
      }
      if (data) {
        this.setState({
          ...this.state,
          isDeviceBelongtoFixedAssets: !!data.scrapDate,
          deviceFixedAssets: {
            scrapDate: data.scrapDate,
            priceWithTax: data.priceWithTax,
            purchaseDate: data.purchaseDate,
          },
        });
      }
    });
  };

  goBack = () => {
    this.props.redirect(urls.generateDeviceListUrl());
  };

  canOpenDoubleCheckConfirmModal = () =>
    new Promise(resolve => {
      this.props.form.validateFields(errs => {
        if (errs) {
          resolve(false);
          return;
        }
        resolve(true);
      });
    });

  submintEditFrom = ({ reason }) =>
    new Promise(resolve => {
      const { form, redirect } = this.props;
      const shouldUpdateTopology = form.isFieldsTouched([
        'name',
        'tag',
        'categoryCascaderList',
        'areaIdcBlockRoom',
        'extendPosition',
      ]);
      const values = form.getFieldsValue();

      const params = this.getParams();
      if (!reason.trim()) {
        message.error('不允许只输入空格！');
        resolve(true);
        return;
      }

      const payload = {
        data: {
          ...values,
          ...params,
          assetNo: values.assetNo.trim(),
          parentGuid: values.parentGuid ? values.parentGuid.key : null,
          operatorNotes: reason,
          operator: values.operator?.label,
          operatorId: values.operator?.key,
          serialNumber: values.serialNumber ? values.serialNumber.trim() : null,
        },
        successCallback: () => {
          resolve(true);
          if (shouldUpdateTopology) {
            Modal.confirm({
              title: '请注意：本次修改需要更新电力拓扑！',
              content: '若不及时更新，可能导致预览拓扑图/告警异常！',
              cancelText: '返回设备列表',
              okText: '现在去',
              onCancel: this.goBack,
              onOk: () => {
                redirect(
                  urls.generateTopologyGraphixUrl({
                    idc: values.areaIdcBlockRoom[0],
                    block: values.areaIdcBlockRoom[1],
                    topologyType: TOPOLOGY_TYPE_KEY_MAP.ELECTRIC_POWER,
                    mode: 'edit',
                  })
                );
              },
            });
          } else {
            setTimeout(this.goBack, 2 * 1000);
          }
        },
        errorCallback: () => {
          resolve(false);
        },
      };
      this.props.fetchEditEquipment(payload);
    });

  getParams = () => {
    const data = this.props.form.getFieldsValue();
    const { specInfo } = this.state;

    const { match } = this.props;
    const params = omit(
      data,
      'guid',
      'tag',
      'deviceLabel',
      'categoryCascaderList',
      'topCategory',
      'secondCategory',
      'deviceType',
      'areaIdcBlockRoom',
      'idcTag',
      'blockTag',
      'roomTag',
      'serialNumber',
      'extendPosition',
      'operationStatus',
      'vendorModel',
      'assetStatus',
      'purchasePrice',
      'warrantyTime',
      'checkTime',
      'scrapTime',
      'warrantyStatus',
      'warrantyPrice',
      'warrantyVendor',
      'enableTime',
      'purchaseTime',
      'operator',
      'supplyVendor'
    );
    params.guid = match.params.id;
    if (data.tag) {
      params.tag = data.tag;
    }
    if (data.deviceLabel) {
      params.deviceLabel = data.deviceLabel;
    }
    if (data.warrantyStatus) {
      params.warrantyStatus = data.warrantyStatus.key;
    }
    if (data.assetStatus) {
      params.assetStatus = data.assetStatus.key;
    }
    if (data.operationStatus) {
      params.operationStatus = data.operationStatus.key;
    }
    if (data.warrantyTime) {
      params.warrantyTime = data.warrantyTime.startOf('day').valueOf();
    }
    if (data.checkTime) {
      params.checkTime = data.checkTime.startOf('day').valueOf();
    }
    if (data.scrapTime) {
      params.scrapTime = data.scrapTime.startOf('day').valueOf();
    }
    if (data.enableTime) {
      params.enableTime = data.enableTime.startOf('day').valueOf();
    }
    if (data.purchaseTime) {
      params.purchaseTime = data.purchaseTime.startOf('day').valueOf();
    }

    const specParams = {};
    Object.keys(data)
      .filter(key => key.indexOf('Spec_') > -1)
      .forEach(key => {
        specParams[key] = moment.isMoment(data[key])
          ? moment(data[key]).format('HH:mm')
          : data[key]; // 兼容属性信息的时间选择
      });
    params.specParams = Object.keys(specParams)
      .map(item => {
        const spec = specInfo.find(spec => spec.specCode === item.replace('Spec_', ''));

        const specValue = specParams[item];
        return {
          specId: spec?.id,
          specName: spec?.specName,
          specValue: Array.isArray(specValue) ? specValue[specValue.length - 1] : specValue,
        };
      })
      .filter(item => !!item.specValue && !!item.specId);
    const p = omitBy(params, (value, key) => {
      if (value === null || value === undefined || value === '') {
        return key;
      }
    });

    return p;
  };

  debouncedHandleSearchDevices = debounce(value => {
    if (value === '') {
      return;
    }
    const location = this.props.form.getFieldValue('areaIdcBlockRoom');
    this.props.searchDeviceSN({ name: value, idcTag: location?.[0], blockTag: location?.[1] });
  }, 500);

  render() {
    const form = this.props.form;
    const { getFieldDecorator, getFieldValue, setFieldsValue } = form;
    const { specInfo, deviceFixedAssets } = this.state;
    const { className, editEquipmentMessList } = this.props;

    let initlabelValue = {
      key: '',
      label: '',
    };
    if (editEquipmentMessList && editEquipmentMessList.parentGuid) {
      initlabelValue = {
        key: editEquipmentMessList.parentGuid,
        label: editEquipmentMessList.parentName,
      };
    }

    return (
      <Form style={{ marginBottom: 48 }} className={className} colon={false}>
        <GutterWrapper mode="vertical">
          <Collapse defaultActiveKey={['deviceParam', 'assetsInfo', 'basicInfo']} bordered={false}>
            <Panel key="basicInfo" header="基本信息">
              <Row>
                <Col xl={4} sm={6}>
                  <Form.Item label="资产ID" labelCol={{ flex: '75px' }} wrapperCol={{ xl: 16 }}>
                    {getFieldDecorator('assetNo', {
                      initialValue: editEquipmentMessList ? editEquipmentMessList.assetNo : null,
                    })(<Input disabled />)}
                  </Form.Item>
                </Col>
                <Col xl={5} sm={6}>
                  <Form.Item label="设备编号" labelCol={{ xl: 7 }} wrapperCol={{ xl: 17 }}>
                    {getFieldDecorator('name', {
                      rules: [
                        { required: true, whitespace: true, message: '设备编号必填！' },
                        {
                          max: 32,
                          message: '最多输入 32 个字符！',
                        },
                      ],
                      initialValue: editEquipmentMessList ? editEquipmentMessList.name : null,
                    })(<Input />)}
                  </Form.Item>
                </Col>
                <Col xl={5} sm={6}>
                  <Form.Item label="设备名称" labelCol={{ xl: 7 }} wrapperCol={{ xl: 17 }}>
                    {getFieldDecorator('deviceLabel', {
                      rules: [
                        {
                          max: 32,
                          message: '最多输入 32 个字符！',
                        },
                      ],
                      initialValue: editEquipmentMessList
                        ? editEquipmentMessList.deviceLabel
                        : null,
                    })(<Input />)}
                  </Form.Item>
                </Col>
                <Col xl={5} sm={6}>
                  <Form.Item label="映射标签" labelCol={{ xl: 7 }} wrapperCol={{ xl: 17 }}>
                    {getFieldDecorator('tag', {
                      rules: [
                        {
                          max: 48,
                          message: '最多输入 48 个字符！',
                        },
                      ],
                      initialValue: editEquipmentMessList ? editEquipmentMessList.tag : null,
                    })(<Input />)}
                  </Form.Item>
                </Col>
                <Col xl={5} sm={6}>
                  <Form.Item label="集团设备编号" labelCol={{ xl: 10 }} wrapperCol={{ xl: 14 }}>
                    {getFieldDecorator('groupName', {
                      rules: [
                        {
                          max: 32,
                          message: '最多输入 32 个字符！',
                        },
                        { required: true, whitespace: true, message: '集团设备编号必填！' },
                      ],
                      initialValue: editEquipmentMessList ? editEquipmentMessList.groupName : null,
                    })(<Input />)}
                  </Form.Item>
                </Col>
                <Col xl={4} sm={6}>
                  <Form.Item label="资产分类" labelCol={{ flex: '75px' }} wrapperCol={{ xl: 16 }}>
                    {getFieldDecorator('categoryCascaderList', {
                      rules: [{ required: true, message: '资产分类必选！' }],
                      initialValue: editEquipmentMessList
                        ? {
                            thirdCategorycode: editEquipmentMessList.deviceType,
                            secondCategoryCode: editEquipmentMessList.secondCategory,
                            firstCategoryCode: editEquipmentMessList.topCategory,
                          }
                        : null,
                    })(
                      <AssetClassificationApiTreeSelect
                        dataType={['snDevice']}
                        category="allCategoryCode"
                        requestOnDidMount
                        disabledDepths={[0, 1]}
                        onChange={() => {
                          setFieldsValue({ vendorModel: undefined });
                        }}
                      />
                    )}
                  </Form.Item>
                </Col>
                <Col xl={10} sm={12}>
                  <Form.Item label="厂商、型号" labelCol={{ xl: 6 }} wrapperCol={{ xl: 18 }}>
                    {getFieldDecorator('vendorModel', {
                      rules: [
                        { required: true, message: '请选择厂商、型号' },
                        {
                          len: 2,
                          type: 'array',
                          message: '型号必选！',
                        },
                      ],
                      initialValue: editEquipmentMessList
                        ? [editEquipmentMessList.vendor, editEquipmentMessList.productModel]
                        : undefined,
                    })(
                      <VendorModelSelect
                        style={{ width: 200 }}
                        deviceType={getFieldValue('categoryCascaderList')?.thirdCategorycode}
                        allowClear
                      />
                    )}
                  </Form.Item>
                </Col>
                <Col xl={5} sm={6}>
                  <Form.Item label="位置" labelCol={{ xl: 7 }} wrapperCol={{ xl: 17 }}>
                    {getFieldDecorator('areaIdcBlockRoom', {
                      rules: [
                        { required: true, message: '位置必选' },
                        {
                          len: 3,
                          type: 'array',
                          message: '包间必选！',
                        },
                      ],
                      initialValue: editEquipmentMessList
                        ? [
                            editEquipmentMessList.spaceGuid.idcTag,
                            editEquipmentMessList.spaceGuid.blockTag,
                            editEquipmentMessList.spaceGuid.roomTag,
                          ]
                        : null,
                    })(<LocationCascader showRoom currentAuthorize />)}
                  </Form.Item>
                </Col>
                <Col xl={5} sm={6}>
                  <Form.Item label="设备状态" labelCol={{ xl: 10 }} wrapperCol={{ xl: 14 }}>
                    {getFieldDecorator('operationStatus', {
                      rules: [{ required: true, message: '设备状态必选' }],
                      initialValue: editEquipmentMessList
                        ? {
                            key: editEquipmentMessList.operationStatus.code,
                            label: editEquipmentMessList.operationStatus.name,
                          }
                        : { key: '', label: '' },
                    })(
                      <ApiSelect
                        style={{ width: '100%' }}
                        labelInValue
                        trigger="onDidMount"
                        fieldNames={{ label: 'value', value: 'key' }}
                        dataService={async () => {
                          const response = await equipmentManageService.fetchOperationStatus();
                          if (response) {
                            return Promise.resolve(response);
                          } else {
                            return Promise.resolve([]);
                          }
                        }}
                      />
                    )}
                  </Form.Item>
                </Col>
                <Col xl={4} sm={6}>
                  <Form.Item label="排序序号" labelCol={{ flex: '75px' }} wrapperCol={{ xl: 16 }}>
                    {getFieldDecorator('sort', {
                      initialValue: editEquipmentMessList ? editEquipmentMessList.sort : undefined,
                    })(
                      <InputNumber
                        style={{ width: '100%' }}
                        min={1}
                        precision={0}
                        parser={value => value && Math.floor(value)}
                      />
                    )}
                  </Form.Item>
                </Col>
                <Col xl={5} sm={6}>
                  <Form.Item label="父设备名称" labelCol={{ xl: 7 }} wrapperCol={{ xl: 17 }}>
                    {getFieldDecorator('parentGuid', {
                      initialValue: initlabelValue,
                    })(
                      <Select
                        style={{ width: '100%' }}
                        showSearch
                        allowClear
                        labelInValue
                        defaultActiveFirstOption={false}
                        showArrow={false}
                        filterOption={false}
                        onSearch={this.debouncedHandleSearchDevices}
                      >
                        {this.props.deviceSNData.map(item => {
                          return (
                            <Option key={item.value} value={item.value}>
                              {item.label}
                            </Option>
                          );
                        })}
                      </Select>
                    )}
                  </Form.Item>
                </Col>
                <Col xl={5} sm={6}>
                  <Form.Item label="设备SN" labelCol={{ xl: 7 }} wrapperCol={{ xl: 17 }}>
                    {getFieldDecorator('serialNumber', {
                      initialValue: editEquipmentMessList
                        ? editEquipmentMessList.serialNumber
                        : null,
                      rules: [
                        {
                          max: 46,
                          message: '最多输入 46 个字符！',
                        },
                      ],
                    })(<Input allowClear />)}
                  </Form.Item>
                </Col>
                <Col xl={5} sm={6}>
                  <Form.Item label="扩展位置" labelCol={{ xl: 7 }} wrapperCol={{ xl: 17 }}>
                    {getFieldDecorator('extendPosition', {
                      initialValue: editEquipmentMessList
                        ? editEquipmentMessList.extendPosition
                        : null,
                      rules: [
                        {
                          max: 32,
                          message: '最多输入 32 个字符！',
                        },
                      ],
                    })(<Input allowClear />)}
                  </Form.Item>
                </Col>

                <Col xl={5} sm={6}>
                  <Form.Item label="所属线路" labelCol={{ xl: 10 }} wrapperCol={{ xl: 14 }}>
                    {getFieldDecorator('powerLine', {
                      initialValue: editEquipmentMessList
                        ? editEquipmentMessList.powerLine
                        : undefined,
                    })(
                      <ApiSelect
                        style={{ width: '100%' }}
                        trigger="onDidMount"
                        fieldNames={{ label: 'code', value: 'code' }}
                        dataService={async () => {
                          const { data, error } = await fetchMetaDataByTypeWeb({
                            type: MetaType.DEVICE_POWER_LINE,
                          });
                          if (error) {
                            message.error(error.message);
                            return Promise.resolve([]);
                          }
                          return Promise.resolve(data.data);
                        }}
                      />
                    )}
                  </Form.Item>
                </Col>

                <Col xl={5} sm={6}>
                  <Form.Item label="所属单元" labelCol={{ flex: '75px' }} wrapperCol={{ xl: 16 }}>
                    {getFieldDecorator('unit', {
                      initialValue: editEquipmentMessList ? editEquipmentMessList.unit : undefined,
                    })(
                      <ApiSelect
                        style={{ width: '100%' }}
                        trigger="onDidMount"
                        fieldNames={{ label: 'code', value: 'code' }}
                        dataService={async () => {
                          const { data, error } = await fetchMetaDataByTypeWeb({
                            type: MetaType.DEVICE_UNIT,
                          });
                          if (error) {
                            message.error(error.message);
                            return Promise.resolve([]);
                          }
                          return Promise.resolve(data.data);
                        }}
                      />
                    )}
                  </Form.Item>
                </Col>
              </Row>
            </Panel>
            <Panel key="assetsInfo" header="资产信息">
              <Row>
                <Col xl={4}>
                  <Form.Item label="资产状态" labelCol={{ xl: 9 }} wrapperCol={{ xl: 15 }}>
                    {getFieldDecorator('assetStatus', {
                      rules: [{ required: true, message: '资产状态必选' }],
                      initialValue: editEquipmentMessList
                        ? {
                            key: editEquipmentMessList.assetStatus.code,
                            label: editEquipmentMessList.assetStatus.name,
                          }
                        : { key: '', label: '' },
                    })(
                      <ApiSelect
                        style={{ width: '100%' }}
                        labelInValue
                        trigger="onDidMount"
                        fieldNames={{ label: 'value', value: 'key' }}
                        dataService={async () => {
                          const response = await equipmentManageService.fetchAssetStatusStatus();
                          if (response) {
                            return Promise.resolve(response);
                          } else {
                            return Promise.resolve([]);
                          }
                        }}
                      />
                    )}
                  </Form.Item>
                </Col>
                <Col xl={5}>
                  <Form.Item label="购买日期" labelCol={{ xl: 9 }} wrapperCol={{ xl: 15 }}>
                    {getFieldDecorator('purchaseTime', {
                      initialValue: deviceFixedAssets.purchaseDate
                        ? moment(deviceFixedAssets.purchaseDate)
                        : editEquipmentMessList && editEquipmentMessList.purchaseTime
                          ? moment(editEquipmentMessList.purchaseTime)
                          : null,
                    })(
                      <DatePicker
                        style={{ width: '100%' }}
                        showToday={false}
                        format="YYYY-MM-DD"
                        disabled={this.state.isDeviceBelongtoFixedAssets}
                      />
                    )}
                  </Form.Item>
                </Col>

                <Col xl={5}>
                  <Form.Item label="过保日期" labelCol={{ xl: 9 }} wrapperCol={{ xl: 15 }}>
                    {getFieldDecorator('warrantyTime', {
                      initialValue:
                        editEquipmentMessList && editEquipmentMessList.warrantyTime
                          ? moment(editEquipmentMessList.warrantyTime)
                          : null,
                    })(
                      <DatePicker style={{ width: '100%' }} showToday={false} format="YYYY-MM-DD" />
                    )}
                  </Form.Item>
                </Col>
                <Col xl={5}>
                  <Form.Item label="启用日期" labelCol={{ xl: 9 }} wrapperCol={{ xl: 15 }}>
                    {getFieldDecorator('enableTime', {
                      initialValue:
                        editEquipmentMessList && editEquipmentMessList.enableTime
                          ? moment(editEquipmentMessList.enableTime)
                          : null,
                    })(
                      <DatePicker style={{ width: '100%' }} showToday={false} format="YYYY-MM-DD" />
                    )}
                  </Form.Item>
                </Col>
                <Col xl={5}>
                  <Form.Item label="验收日期" labelCol={{ xl: 9 }} wrapperCol={{ xl: 15 }}>
                    {getFieldDecorator('checkTime', {
                      initialValue:
                        editEquipmentMessList && editEquipmentMessList.checkTime
                          ? moment(editEquipmentMessList.checkTime)
                          : null,
                    })(
                      <DatePicker style={{ width: '100%' }} showToday={false} format="YYYY-MM-DD" />
                    )}
                  </Form.Item>
                </Col>
                <Col xl={4}>
                  <Form.Item label="购买价格" labelCol={{ xl: 9 }} wrapperCol={{ xl: 15 }}>
                    {getFieldDecorator('purchasePrice', {
                      rules: [
                        {
                          pattern: /^([1-9]\d{0,7}|0)(\.\d{1,2})?$/,
                          message: '请输入非负数，整数部分不超过8位，小数部分不超过2位',
                        },
                      ],
                      initialValue: deviceFixedAssets.priceWithTax
                        ? (deviceFixedAssets.priceWithTax / 100).toFixed(2)
                        : editEquipmentMessList
                          ? editEquipmentMessList.purchasePrice
                          : null,
                    })(<Input suffix="￥" disabled={this.state.isDeviceBelongtoFixedAssets} />)}
                  </Form.Item>
                </Col>
                <Col xl={5}>
                  <Form.Item label="供应商简称" labelCol={{ xl: 9 }} wrapperCol={{ xl: 15 }}>
                    {getFieldDecorator('supplyVendor', {
                      rules: [{ required: true, message: '供应商简称必填' }],
                      initialValue: editEquipmentMessList
                        ? editEquipmentMessList.supplyVendor
                        : null,
                    })(<VendorSelect />)}
                  </Form.Item>
                </Col>
                <Col xl={5}>
                  <Form.Item label="报废日期" labelCol={{ xl: 9 }} wrapperCol={{ xl: 15 }}>
                    {getFieldDecorator('scrapTime', {
                      initialValue: deviceFixedAssets.scrapDate
                        ? moment(deviceFixedAssets.scrapDate)
                        : editEquipmentMessList && editEquipmentMessList.scrapTime
                          ? moment(editEquipmentMessList.scrapTime)
                          : null,
                    })(
                      <DatePicker
                        style={{ width: '100%' }}
                        showToday={false}
                        format="YYYY-MM-DD"
                        disabled={this.state.isDeviceBelongtoFixedAssets}
                      />
                    )}
                  </Form.Item>
                </Col>
                <Col xl={5}>
                  <Form.Item label="维保价格" labelCol={{ xl: 9 }} wrapperCol={{ xl: 15 }}>
                    {getFieldDecorator('warrantyPrice', {
                      rules: [
                        {
                          pattern: /^([1-9]\d{0,7}|0)(\.\d{1,2})?$/,
                          message: '请输入非负数，整数部分不超过8位，小数部分不超过2位',
                        },
                      ],
                      initialValue: editEquipmentMessList
                        ? editEquipmentMessList.warrantyPrice
                        : null,
                    })(<Input suffix="￥" />)}
                  </Form.Item>
                </Col>
                <Col xl={5}>
                  <Form.Item label="维保厂商" labelCol={{ xl: 9 }} wrapperCol={{ xl: 15 }}>
                    {getFieldDecorator('warrantyVendor', {
                      rules: [{ required: true, message: '维保厂商必填' }],
                      initialValue: editEquipmentMessList
                        ? editEquipmentMessList.warrantyVendor
                        : null,
                    })(<VendorSelect />)}
                  </Form.Item>
                </Col>
                <Col xl={5}>
                  <Form.Item label="维保状态：" labelCol={{ xl: 8 }} wrapperCol={{ xl: 16 }}>
                    {editEquipmentMessList &&
                    editEquipmentMessList.warrantyStatus &&
                    editEquipmentMessList.warrantyStatus.name
                      ? editEquipmentMessList.warrantyStatus.name
                      : '--'}
                  </Form.Item>
                </Col>
                <Col xl={5}>
                  <Form.Item label="净值：" labelCol={{ xl: 5 }} wrapperCol={{ xl: 19 }}>
                    {editEquipmentMessList && editEquipmentMessList.netWorth
                      ? editEquipmentMessList.netWorth + '￥'
                      : '--'}
                  </Form.Item>
                </Col>
              </Row>
            </Panel>
            <Panel key="deviceParam" header="设备级参数设置">
              {specInfo.length ? (
                <Row>
                  {specInfo.map(item => (
                    <Col key={item.id} xl={5}>
                      <Form.Item key={item.id} label={item.specName}>
                        {getFieldDecorator(`Spec_${item.specCode}`, {
                          rules:
                            item.valueType === VALUE_TYPE_KEY_MAP.CHARACTER &&
                            item.inputWay === 'INPUT'
                              ? [
                                  {
                                    max: 200,
                                    message: '最多输入 200 个字符！',
                                  },
                                ]
                              : undefined,
                          initialValue: getInitialValue(item),
                        })(getComponent(item, { inputNumberStyle: { marginTop: 5 } }))}
                      </Form.Item>
                    </Col>
                  ))}
                </Row>
              ) : (
                <TinyEmpty />
              )}
            </Panel>
          </Collapse>
          <FooterToolBar>
            <GutterWrapper>
              <DeleteConfirm
                title="请填写此次修改的原因"
                reasonPlaceholder=""
                canOpen={this.canOpenDoubleCheckConfirmModal}
                onOk={this.submintEditFrom}
              >
                <Button type="primary">提交</Button>
              </DeleteConfirm>
              <Button onClick={this.goBack}>取消</Button>
            </GutterWrapper>
          </FooterToolBar>
        </GutterWrapper>
      </Form>
    );
  }
}
const mapStateToProps = ({ equipmentManage: { editEquipmentMessList, deviceSNData } }) => {
  return {
    editEquipmentMessList,
    deviceSNData,
  };
};

const mapDispatchToProps = {
  fetchEditEquipment,
  searchDeviceSN,
  syncCommonData: syncCommonDataActionCreator,
  getDeviceInfo,
  redirect: redirectActionCreator,
  editEquipmentMess: equipmentActions.editEquipmentMess,
};

export const EditEquipMentConnect = connect(
  mapStateToProps,
  mapDispatchToProps
)(Form.create({ name: 'edit_equipment' })(EditEquipMent));

export default EditEquipMentConnect;
