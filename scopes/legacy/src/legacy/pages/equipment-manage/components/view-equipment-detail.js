import get from 'lodash/get';
import moment from 'moment';
import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import { generateDeviceRecordRoutePath } from '@manyun/resource-hub.route.resource-routes';

import {
  GutterWrapper,
  TinyDescriptions,
  TinyEmpty,
  TinyModal,
} from '@manyun/dc-brain.legacy.components';
import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';

const { TabPane } = Tabs;
class equipMentInfo extends Component {
  state = {
    visible: false,
  };

  onVisible = async () => {
    this.setState({
      visible: true,
    });
  };

  onClose = () => {
    this.setState({
      visible: false,
    });
  };

  render() {
    const { text, type, record, metaCategoryJson } = this.props;
    const { visible } = this.state;

    return [
      <Button key="1" type={type} style={{ padding: 0, height: 'auto' }} onClick={this.onVisible}>
        {text}
      </Button>,
      <TinyModal
        key="2"
        title={
          <GutterWrapper>
            <span>{record.tag}</span>
            <Link
              style={{ fontSize: 14 }}
              to={generateDeviceRecordRoutePath({ guid: record.guid })}
            >
              查看设备视图
            </Link>
          </GutterWrapper>
        }
        visible={visible}
        footer={null}
        fitContentWidth
        onCancel={this.onClose}
        onClose={this.onClose}
      >
        <Tabs style={{ marginTop: -12 }} defaultActiveKey="1">
          <TabPane key="1" tab="基本信息">
            <TinyDescriptions
              descriptionsItems={[
                {
                  label: '一级分类',
                  value: get(
                    metaCategoryJson,
                    [record.topCategory, 'metaName'],
                    record.topCategory
                  ),
                },
                {
                  label: '二级分类',
                  value: get(
                    metaCategoryJson,
                    [record.secondCategory, 'metaName'],
                    record.secondCategory
                  ),
                },
                {
                  label: '三级分类',
                  value: get(metaCategoryJson, [record.deviceType, 'metaName'], record.deviceType),
                },
                { label: '设备名称', value: record.tag },
                { label: 'SN', value: record.serialNumber },
                {
                  label: '父设备SN',
                  value: record.parentGuid ? record.parentGuid : BLANK_PLACEHOLDER,
                },
                { label: '机房', value: record.spaceGuid.idcTag },
                { label: '包间', value: record.spaceGuid.roomTag },
                { label: '位置', value: record.extendPosition },
                { label: '厂商', value: record.vendor },
                { label: '型号', value: record.productModel },
                { label: '设备状态', value: record.operationStatus.name },
                // { label: '在线状态', value: record.onlineStatus.name },
                { label: '所属线路', value: record.powerLine },
              ]}
            />
          </TabPane>
          <TabPane key="2" tab="资产信息">
            <TinyDescriptions
              descriptionsItems={[
                { label: '资产状态', value: record.assetStatus.name },
                {
                  label: '购买日期',
                  value: record.purchaseTime && moment(record.purchaseTime).format('YYYY-MM-DD'),
                },
                {
                  label: '验收日期',
                  value: record.checkTime && moment(record.checkTime).format('YYYY-MM-DD'),
                },
                {
                  label: '启用日期',
                  value: record.enableTime && moment(record.enableTime).format('YYYY-MM-DD'),
                },
                {
                  label: '过保日期',
                  value: record.warrantyTime && moment(record.warrantyTime).format('YYYY-MM-DD'),
                },
                {
                  label: '报废日期',
                  value: record.scrapTime && moment(record.scrapTime).format('YYYY-MM-DD'),
                },
                { label: '购买价格', value: record.purchasePrice + '￥' },
                { label: '净值', value: record.netWorth + '￥' },
                { label: '维保价格', value: record.warrantyPrice + '￥' },
                { label: '维保状态', value: record.warrantyStatus.name },
                { label: '维保厂商', value: record.warrantyVendor },
              ]}
            />
          </TabPane>
          <TabPane key="3" tab="设备参数">
            <TinyEmpty />
          </TabPane>
        </Tabs>
      </TinyModal>,
    ];
  }
}

const mapStateToProps = ({ common: { deviceCategory } }) => {
  return {
    metaCategoryJson: deviceCategory ? deviceCategory.normalizedList : null,
  };
};

export default connect(mapStateToProps, null)(equipMentInfo);
