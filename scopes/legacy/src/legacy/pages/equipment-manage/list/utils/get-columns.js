import get from 'lodash/get';
import React from 'react';
import { Link } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Space } from '@manyun/base-ui.ui.space';
import { SpaceOrDeviceLink } from '@manyun/resource-hub.ui.space-or-device-link';

import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';

export const getColumns = (metaCategory, ctx) => [
  {
    ellipsis: true,
    title: '资产ID',
    dataIndex: 'assetNo',
    show: true,
    render: (assetNo, { guid }) => {
      if (!assetNo) {
        return BLANK_PLACEHOLDER;
      }
      return <SpaceOrDeviceLink id={guid} type="DEVICE_GUID" text={assetNo} />;
    },
  },
  {
    ellipsis: true,
    title: 'SN',
    dataIndex: 'serialNumber',
    show: false,
  },
  {
    ellipsis: true,
    title: '设备编号',
    dataIndex: 'name',
    show: true,
  },
  {
    ellipsis: true,
    title: '设备名称',
    dataIndex: 'deviceLabel',
    show: false,
  },
  {
    ellipsis: true,
    title: '映射标签',
    dataIndex: 'tag',
    show: false,
  },
  {
    ellipsis: true,
    title: '集团设备编号',
    dataIndex: 'groupName',
    show: false,
  },
  {
    ellipsis: true,
    title: '一级分类',
    dataIndex: 'topCategory',
    show: false,
    render(category) {
      return get(metaCategory, [category, 'metaName'], category);
    },
  },
  {
    ellipsis: true,
    title: '二级分类',
    dataIndex: 'secondCategory',
    show: false,
    render(category) {
      return get(metaCategory, [category, 'metaName'], category);
    },
  },
  {
    ellipsis: true,
    title: '三级分类',
    dataIndex: 'deviceType',
    show: true,
    render(category) {
      return get(metaCategory, [category, 'metaName'], category);
    },
  },
  {
    title: '机房编号',
    dataIndex: ['spaceGuid', 'idcTag'],
    show: true,
  },
  {
    title: '楼栋编号',
    dataIndex: ['spaceGuid', 'blockTag'],
    show: true,
  },
  {
    title: '包间编号',
    dataIndex: ['spaceGuid', 'roomTag'],
    show: true,
  },
  {
    title: '包间名称',
    dataIndex: 'roomName',
    show: true,
  },
  {
    title: '扩展位置',
    dataIndex: 'extendPosition',
    show: true,
  },
  {
    title: '厂商',
    dataIndex: 'vendor',
    show: true,
  },
  {
    title: '型号',
    dataIndex: 'productModel',
    show: true,
  },
  {
    title: '购买日期',
    dataIndex: 'purchaseTime',
    dataType: 'date',
    show: true,
  },
  {
    title: '过保日期',
    dataIndex: 'warrantyTime',
    dataType: 'date',
    show: true,
  },
  {
    ellipsis: true,
    title: '维保厂商',
    dataIndex: 'warrantyVendor',
    show: true,
  },
  {
    title: '维保状态',
    dataIndex: ['warrantyStatus', 'name'],
    show: true,
  },
  {
    title: '状态',
    dataIndex: ['operationStatus', 'name'],
    show: true,
  },
  {
    title: '排序序号',
    dataIndex: 'sort',
    show: false,
  },
  {
    title: '操作',
    key: '__actions',
    fixed: 'right',
    show: true,
    render: (__, record) => (
      <Space align="center" size={0}>
        <Link
          to={{
            pathname: urls.generateEditEquipmentConfigUrl({ id: record.guid }),
            state: { editEquipmentMessList: record },
          }}
        >
          编辑
        </Link>
        <Divider type="vertical" />
        <DeleteConfirm
          targetName={record.assetNo ? record.assetNo : ''}
          onOk={({ reason }) => ctx.deleteEquipment(record, reason)}
        >
          <Button type="link" compact>
            删除
          </Button>
        </DeleteConfirm>
      </Space>
    ),
  },
];
