import Form from '@ant-design/compatible/es/form';
import { Select } from '@galiojs/awesome-antd';
import uniq from 'lodash/uniq';
import React, { useEffect, useState } from 'react';

import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { VendorModelSelect } from '@manyun/crm.ui.vendor-model-select';
import { VendorSelect } from '@manyun/crm.ui.vendor-select';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { fetchMetaDataByTypeWeb } from '@manyun/resource-hub.service.fetch-meta-data-by-type';
import { DeviceSelect } from '@manyun/resource-hub.ui.device-select';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';

import { LocationCascader, ModalButton } from '@manyun/dc-brain.legacy.components';

const formItemLayout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 18 },
};

/**
 * @param {object} props
 * @param {import('antd-3/lib/form/Form').WrappedFormUtils<any>} props.form
 * @param {boolean} props.vendorModelDisabled
 * @param {string} [props.deviceType]
 * @param {string[]} [props.deviceGuids]
 */
export function BatchUpdateForm({
  form: { getFieldDecorator },
  vendorModelDisabled,
  deviceType,
  deviceGuids,
}) {
  const [pointLineData, setPointLineData] = useState([]);

  useEffect(() => {
    async function fetchData() {
      const { data } = await fetchMetaDataByTypeWeb({ type: MetaType['DEVICE_POWER_LINE'] });
      if (data) {
        setPointLineData(data.data);
      }
    }
    fetchData();
  }, []);

  return (
    <Form colon={false}>
      <Row>
        <Col span={24}>
          <Form.Item label="父设备" {...formItemLayout}>
            {getFieldDecorator('parentDevice', {
              rules: [
                {
                  validator: (__, value, callback) => {
                    if (deviceGuids.includes(value)) {
                      callback('父设备不可选择表格中已选的设备！');
                      return;
                    }
                    callback();
                  },
                },
              ],
            })(<DeviceSelect allowClear />)}
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item
            label="厂商、型号"
            {...formItemLayout}
            help={vendorModelDisabled ? '选择相同设备类型的设备才可以批量修改此项' : undefined}
          >
            {getFieldDecorator('vendorModel', {
              rules: [
                {
                  len: 2,
                  type: 'array',
                  message: '型号必选！',
                },
              ],
            })(
              <VendorModelSelect
                style={{ width: '50%' }}
                allowClear
                disabled={vendorModelDisabled}
                deviceType={deviceType}
              />
            )}
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label="位置" {...formItemLayout}>
            {getFieldDecorator('location', {
              rules: [
                {
                  len: 3,
                  type: 'array',
                  message: '请选至包间！',
                },
              ],
            })(<LocationCascader currentAuthorize showRoom />)}
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label="所属线路" {...formItemLayout}>
            {getFieldDecorator('powerLine')(
              <Select style={{ width: '100%' }} allowClear>
                {pointLineData.map(({ code }) => {
                  return (
                    <Select.Option value={code} key={code}>
                      {code}
                    </Select.Option>
                  );
                })}
              </Select>
            )}
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label="购买日期" {...formItemLayout}>
            {getFieldDecorator('purchaseDate')(
              <DatePicker style={{ width: '100%' }} showToday={false} format="YYYY-MM-DD" />
            )}
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label="验收日期" {...formItemLayout}>
            {getFieldDecorator('checkTime')(
              <DatePicker style={{ width: '100%' }} showToday={false} format="YYYY-MM-DD" />
            )}
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label="过保日期" {...formItemLayout}>
            {getFieldDecorator('expirationDate')(
              <DatePicker style={{ width: '100%' }} showToday={false} format="YYYY-MM-DD" />
            )}
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label="购买价格" {...formItemLayout}>
            {getFieldDecorator('purchasePrice', {
              rules: [
                {
                  pattern: /^(\d{1,8}(\.\d{1,2})?|100)$/,
                  message: '限制整数位最大8位，两位小数',
                },
              ],
            })(<Input suffix="￥" />)}
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label="维保价格" {...formItemLayout}>
            {getFieldDecorator('warrantyPrice', {
              rules: [
                {
                  pattern: /^(\d{1,8}(\.\d{1,2})?|100)$/,
                  message: '限制整数位最大8位，两位小数',
                },
              ],
            })(<Input suffix="￥" />)}
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label="维保厂商" {...formItemLayout}>
            {getFieldDecorator('warrantyVendor')(<VendorSelect />)}
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label="成本归属" {...formItemLayout}>
            {getFieldDecorator('costBlock')(
              <LocationTreeSelect
                disabledTypes={['IDC']}
                showSearch
                authorizedOnly
                style={{ width: 265 }}
              />
            )}
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label="操作备注" {...formItemLayout}>
            {getFieldDecorator('operatorNotes', {
              rules: [
                { required: true, message: '操作备注必填！' },
                {
                  max: 128,
                  message: '最多输入 128 个字符！',
                },
              ],
            })(<Input.TextArea autoSize={{ minRows: 3 }} placeholder="请输入" />)}
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
}

/**
 * @param {object} props
 * @param {any[]} props.devices
 * @param {(valueMap: { parentDevice?: string; operator?: { key: string; label: string }; vendorModel?: [string, string] }) => Promise<boolean>} props.onOk
 */
export function BatchUpdateModalButton({ form, devices, onOk }) {
  const text = '批量更新';
  const disabled = !(Array.isArray(devices) && devices.length);
  const deviceGuids = Array.isArray(devices) ? devices.map(({ guid }) => guid) : [];
  const deviceTypes = Array.isArray(devices)
    ? uniq(devices.map(({ deviceType }) => deviceType))
    : [];
  const vendorModelDisabled = deviceTypes.length !== 1; // 同一设备类型时才可以批量更新厂商、型号信息
  const deviceType = deviceTypes.length > 0 ? deviceTypes[0] : undefined;

  return (
    <ModalButton
      destroyOnClose
      text={text}
      disabled={disabled}
      title={text}
      onOk={() =>
        new Promise(resolve => {
          form.validateFields(async (errMap, valueMap) => {
            if (errMap) {
              resolve(false);
              return;
            }

            // 未修改父设备、责任人、厂商、型号、位置、购买日期、过保日期、所属线路、验收日期、购买价格、维保价格、维保厂商，则认为用户期望什么都不修改然后关闭弹窗
            if (
              valueMap.parentDevice === undefined &&
              valueMap.operator === undefined &&
              !(Array.isArray(valueMap.vendorModel) && valueMap.vendorModel.length === 2) &&
              !(Array.isArray(valueMap.location) && valueMap.location.length === 3) &&
              valueMap.purchaseDate === undefined &&
              valueMap.expirationDate === undefined &&
              valueMap.powerLine === undefined &&
              valueMap.checkTime === undefined &&
              valueMap.purchasePrice === undefined &&
              valueMap.warrantyPrice === undefined &&
              valueMap.warrantyVendor === undefined &&
              valueMap.costBlock === undefined
            ) {
              resolve(true);
              return;
            }

            const result = await onOk(valueMap);
            resolve(result);
          });
        })
      }
    >
      {visible => {
        if (!visible || disabled) {
          return null;
        }

        return (
          <BatchUpdateForm
            form={form}
            vendorModelDisabled={vendorModelDisabled}
            deviceGuids={deviceGuids}
            deviceType={deviceType}
          />
        );
      }}
    </ModalButton>
  );
}

export default Form.create()(BatchUpdateModalButton);
