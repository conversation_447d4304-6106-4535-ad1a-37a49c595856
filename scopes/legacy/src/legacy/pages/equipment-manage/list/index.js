import { ApiSelect, FiltersForm, Form } from '@galiojs/awesome-antd';
import get from 'lodash/get';
import trim from 'lodash/trim';
import React, { Component } from 'react';
import { connect, useDispatch } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Table } from '@manyun/base-ui.ui.table';
import { setLocationSearch } from '@manyun/base-ui.util.query-string';
import { VendorModelSelect } from '@manyun/crm.ui.vendor-model-select';
import { VendorSelect } from '@manyun/crm.ui.vendor-select';
import { EditColumns } from '@manyun/dc-brain.ui.edit-columns';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';

import {
  AssetClassificationApiTreeSelect,
  GutterWrapper,
  TinyCard,
} from '@manyun/dc-brain.legacy.components';
import {
  DEVICE_SPACE_TYPE_KEY_MAP,
  DEVICE_TYPE_META_TYPE_KEY_MAP,
} from '@manyun/dc-brain.legacy.constants/enums/deviceType';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  batchUpdateDevicesActionCreator,
  deleteDevice,
  equipmentActions,
  fetchEquipmentListPage,
  initializeSearchValues,
  setEquipmentPagination,
} from '@manyun/dc-brain.legacy.redux/actions/equipmentActions';
import * as equipmentService from '@manyun/dc-brain.legacy.services/equipmentService';
import { generateTreeData, getLocationSearchMap } from '@manyun/dc-brain.legacy.utils';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';

import BatchUpdateModalButton from './components/batch-update-modal-button';
import { getColumns } from './utils/get-columns';

const defaultPagination = { pageNum: 1, pageSize: 10 };

class EquipmentManage extends Component {
  state = {
    pageNo: 1,
    pageSize: 10,
    secondCategory: [],
    selectedRowKeys: [],
    selectedRows: [],
    metaCategory: {},
    selectLoading: false,
    equipmentListColumns: [],
  };

  componentDidMount() {
    this.props.syncCommonData();
    this.props.initializeSearchValues(this._getUrlSearchParams());
  }

  componentDidUpdate(prevProps) {
    if (!prevProps.metaCategory && this.props.metaCategory) {
      const { deviceType } = this._getUrlSearchParams();
      if (deviceType) {
        this.props.updateSearchValues({
          cascaderList: {
            name: 'cascaderList',
            value: {
              thirdCategorycode: deviceType,
            },
          },
        });
      }
    }
  }

  componentWillUnmount() {
    this.props.initializeSearchValues(this._getUrlSearchParams());
  }

  _getUrlSearchParams = () => {
    const { search } = this.props.history.location;
    return getLocationSearchMap(search, [
      'guid',
      'name',
      'insertVersion',
      'deviceType',
      'productModel',
      'vendor',
      'warrantyVendor',
      'operationStatus',
      'warrantyStatus',
      'pagination',
      'spaceGuidList',
    ]);
  };

  search = () => {
    this._clearSelection();
    this.props.resetEquipmentPagination();
    this.props.fetchEquipmentListPage();
  };

  deleteEquipment = ({ guid }, operatorNotes) =>
    new Promise(resolve => {
      this.props.deleteDevice({
        params: { guid, operatorNotes },
        successCallback: () => {
          resolve(true);
        },
        errorCallback: () => {
          resolve(false);
        },
      });
    });

  handleReset = () => {
    this.props.dispatch(this.props.resetSearchValues());
    this._clearSelection();
    this.props.setEquipmentPagination({ pageNum: 1, pageSize: 10 });
  };

  getParams = type => {
    const fieldsValue = this.props.searchValues;
    const { selectedRowKeys } = this.state;
    const params = Object.keys(fieldsValue).reduce((map, fieldName) => {
      const value = fieldsValue[fieldName]?.value;
      if ([null, undefined, ''].includes(value) || (Array.isArray(value) && value.length === 0)) {
        return map;
      } else if (fieldName === 'vendorModel') {
        map.vendor = value[0];
        map.productModel = value[1];
      } else if (fieldName === 'cascaderList') {
        map.topCategory = value.firstCategoryCode;
        map.secondCategory = value.secondCategoryCode;
        map.deviceTypeList = value.thirdCategorycode && [value.thirdCategorycode];
      } else if (fieldName === 'warrantyStatus' || fieldName === 'operationStatus') {
        map[fieldName] = value.key;
      } else if (fieldName === 'spaceGuidList') {
        map[fieldName] = value;
      } else {
        map[fieldName] = trim(value);
      }
      return map;
    }, {});
    if (type === 'selected') {
      params.ids = selectedRowKeys;
    }
    return params;
  };

  onSelectChange = (selectedRowKeys, selectedRows) => {
    this.setState({ selectedRowKeys, selectedRows });
  };

  _batchUpdateModalOkHandler = ({
    parentDevice,
    operator,
    vendorModel,
    location,
    purchaseDate,
    expirationDate,
    powerLine,
    checkTime,
    purchasePrice,
    warrantyPrice,
    warrantyVendor,
    operatorNotes,
    costBlock,
  }) => {
    const { batchUpdateDevices } = this.props;
    const { selectedRowKeys } = this.state;
    const data = {
      deviceGuidList: selectedRowKeys,
      parentGuid: parentDevice || null,
      operator: operator?.label || null,
      operatorById: operator?.id || null,
      productModel: vendorModel?.[1] || null,
      vendor: vendorModel?.[0] || null,
      warrantyTime: expirationDate ? expirationDate.startOf('day').valueOf() : null,
      purchaseTime: purchaseDate ? purchaseDate.startOf('day').valueOf() : null,
      spaceGuid: location?.join('.'),
      powerLine: powerLine || null,
      checkTime: checkTime ? checkTime.startOf('day').valueOf() : null,
      purchasePrice,
      warrantyPrice,
      warrantyVendor,
      operatorNotes,
      costBlock,
    };

    return new Promise(resolve => {
      const successCallback = () => resolve(true);
      const errorCallback = () => resolve(false);

      batchUpdateDevices({ data, successCallback, errorCallback });
    });
  };

  _clearSelection = () => {
    this.setState({ selectedRowKeys: [], selectedRows: [] });
  };

  setLocationSearchValues(searchValues, pagination) {
    const searchObj = Object.keys(searchValues ?? {}).reduce((mapper, key) => {
      if (key === 'cascaderList') {
        const cascaderList = searchValues[key].value;

        mapper['deviceType'] = cascaderList.thirdCategorycode;
      } else if (key === 'vendorModel' && searchValues[key].value) {
        mapper['vendor'] = searchValues[key].value[0];

        mapper['productModel'] = searchValues[key].value[1];
      } else if (key === 'operationStatus' && searchValues[key].value?.value) {
        mapper['operationStatus'] = searchValues[key].value.value;
      } else if (key === 'warrantyStatus' && searchValues[key].value?.value) {
        mapper['warrantyStatus'] = searchValues[key].value.value;
      } else {
        mapper[key] = searchValues[key].value;
      }

      return mapper;
    }, {});

    setLocationSearch({
      ...searchObj,
      pagination: JSON.stringify(pagination),
    });
  }

  render() {
    const {
      loading,
      equipmentPage,
      pagination,
      updateSearchValues,
      searchValues,
      setEquipmentPagination,
      form,
    } = this.props;
    const { selectedRowKeys, selectedRows, selectLoading, equipmentListColumns } = this.state;

    return (
      <GutterWrapper mode="vertical">
        <TinyCard>
          <FiltersForm
            form={form}
            items={[
              {
                label: '设备',
                name: 'nameOrLabel',
                control: <Input allowClear placeholder='请输入设备编号或名称'/>,
              },
              {
                label: '资产分类',
                name: 'cascaderList',
                control: (
                  <AssetClassificationApiTreeSelect
                    dataType={['snDevice']}
                    category="allCategoryCode"
                    allowClear
                  />
                ),
              },
              {
                label: '位置',
                name: 'spaceGuidList',
                control: (
                  <LocationTreeSelect
                    nodeTypes={['IDC', 'BLOCK', 'ROOM']}
                    treeCheckable="true"
                    showCheckedStrategy="SHOW_PARENT"
                    authorizedOnly
                    dropdownMatchSelectWidth={400}
                    maxTagCount={1}
                    multiple
                  />
                ),
              },
              { label: '维保厂商', name: 'warrantyVendor', control: <VendorSelect allowClear /> },
              {
                label: '设备状态',
                name: 'operationStatus',
                control: (
                  <ApiSelect
                    style={{ width: 200 }}
                    allowClear
                    labelInValue
                    loading={selectLoading}
                    fieldNames={{ label: 'value', value: 'key' }}
                    dataService={async () => {
                      this.setState({
                        selectLoading: true,
                      });
                      const response = await equipmentService.fetchOperationStatus();
                      this.setState({
                        selectLoading: false,
                      });
                      if (response) {
                        return Promise.resolve(response);
                      } else {
                        return Promise.resolve([]);
                      }
                    }}
                    notFoundContent={<Spin size="small" />}
                    trigger="onDidMount"
                  />
                ),
              },
              {
                label: '维保状态',
                name: 'warrantyStatus',
                control: (
                  <ApiSelect
                    style={{ width: 200 }}
                    allowClear
                    labelInValue
                    loading={selectLoading}
                    fieldNames={{ label: 'value', value: 'key' }}
                    dataService={async () => {
                      this.setState({
                        selectLoading: true,
                      });
                      const response = await equipmentService.fetchWarrantyStatus();
                      this.setState({
                        selectLoading: false,
                      });
                      if (response) {
                        return Promise.resolve(response);
                      } else {
                        return Promise.resolve([]);
                      }
                    }}
                    notFoundContent={<Spin size="small" />}
                    trigger="onDidMount"
                  />
                ),
              },
              {
                label: '厂商、型号',
                name: 'vendorModel',
                control: (
                  <VendorModelSelect
                    numbered
                    deviceType={searchValues.cascaderList?.value?.thirdCategorycode}
                    allowClear
                  />
                ),
              },
            ]}
            fields={Object.keys(searchValues).map(name => {
              const field = searchValues[name];
              return {
                ...field,
                name: name.split('.'),
              };
            })}
            onFieldsChange={changedFields => {
              let fields = changedFields.reduce((mapper, field) => {
                const name = field.name.join('.');
                mapper[name] = {
                  ...field,
                  name,
                };
                return mapper;
              }, {});
              if (fields.cascaderList) {
                fields = {
                  ...fields,
                  vendorModel: {
                    value: [],
                  },
                };
              }
              updateSearchValues(fields);
            }}
            onSearch={() => {
              this.setLocationSearchValues(searchValues, defaultPagination);
              this.search();
            }}
            onReset={() => {
              this.setLocationSearchValues({}, defaultPagination);
              this.handleReset();
            }}
          />
        </TinyCard>
        <TinyCard>
          <Space style={{ width: '100%', justifyContent: 'space-between', marginBottom: 16 }}>
            <Space size={16}>
              <Button
                key="new-device-btn"
                type="primary"
                href={urls.generateCreateEquipmentConfigUrl()}
              >
                新建
              </Button>
              <BatchUpdateModalButton
                key="batch-update-modal-btn"
                devices={selectedRows}
                onOk={this._batchUpdateModalOkHandler}
              />
            </Space>
            <Space size={16}>
              <FileExport
                filename="设备数据.csv"
                data={async type => {
                  const { response, error } = await equipmentService.downloadEquipment(
                    this.getParams(type)
                  );
                  if (error) {
                    message.error(error.message);
                    return error.message;
                  }
                  return response;
                }}
                showExportFiltered
                showExportSelected={!!selectedRowKeys.length}
              />
              {this.props.metaCategory && (
                <EditColumns
                  uniqKey="RESOURCE_HUB_PAGE_EQUIPMENT_LIST"
                  defaultValue={getColumns(this.props.metaCategory, this)}
                  listsHeight={300}
                  allowSetAsFixed={false}
                  onChange={value => {
                    this.setState({
                      equipmentListColumns: value,
                    });
                  }}
                />
              )}
            </Space>
          </Space>
          <Table
            rowKey={record => record.guid}
            columns={equipmentListColumns}
            dataSource={equipmentPage.list}
            loading={loading || this.props.metaCategory === null}
            scroll={{ x: 'max-content' }}
            pagination={{
              total: equipmentPage.total,
              current: pagination.pageNum,
              pageSize: pagination.pageSize,
              onChange: (current, size) => {
                const { searchValues } = this.props;
                const currentPagination = { pageNum: current, pageSize: size };
                this.setState({ selectedRowKeys: [], selectedRows: [] });
                this.setLocationSearchValues(searchValues, currentPagination);
                setEquipmentPagination(currentPagination);
              },
            }}
            rowSelection={{
              selectedRowKeys,
              selectedRows,
              onChange: this.onSelectChange,
            }}
          />
        </TinyCard>
      </GutterWrapper>
    );
  }
}
const mapStateToProps = ({
  equipmentManage: { loading, equipmentPage, pagination, searchValues },
  common: { deviceCategory },
}) => {
  const flatData = get(deviceCategory, 'parallelList', []);
  const treeData = generateTreeData(flatData, {
    key: ({ metaType, metaCode }) => `${metaType}${metaCode}`,
    typeKey: 'metaType',
    parentKey: 'parentCode',
    nodeTypes: [
      DEVICE_TYPE_META_TYPE_KEY_MAP.C0,
      DEVICE_TYPE_META_TYPE_KEY_MAP.C1,
      DEVICE_TYPE_META_TYPE_KEY_MAP.C2,
    ],
    getNode: node => {
      return {
        ...node,
        label: node.metaName,
        value: node.metaCode,
      };
    },
  });

  return {
    loading,
    equipmentPage,
    pagination,
    deviceCategoryTreeList: treeData.filter(
      item => item.metaStyle !== DEVICE_SPACE_TYPE_KEY_MAP.SPACE
    ),
    metaCategory: deviceCategory ? deviceCategory.normalizedList : null,
    searchValues,
  };
};
const mapDispatchToProps = {
  equipmentActions,
  fetchEquipmentListPage,
  getthreeEquipmentType: equipmentActions.threeEquipmentType,
  saveAreaIdcBlockRoom: equipmentActions.saveAreaIdcBlockRoom,
  deleteDevice,
  syncCommonData: syncCommonDataActionCreator,
  updateSearchValues: equipmentActions.updateSearchValues,
  resetSearchValues: equipmentActions.resetSearchValues,
  batchUpdateDevices: batchUpdateDevicesActionCreator,
  initializeSearchValues,
  setEquipmentPagination,
  resetEquipmentPagination: equipmentActions.resetEquipmentPagination,
};

function EquipmentManageList(props) {
  const [form] = Form.useForm();
  const dispatch = useDispatch();

  return <EquipmentManage form={form} dispatch={dispatch} {...props} />;
}

export default connect(mapStateToProps, mapDispatchToProps)(EquipmentManageList);
