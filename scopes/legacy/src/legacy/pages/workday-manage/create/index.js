import React, { useState } from 'react';
import { connect } from 'react-redux';

import { remove } from 'lodash';
import last from 'lodash/last';
import reduce from 'lodash/reduce';
import moment from 'moment';
import styled from 'styled-components';

import { Badge } from '@manyun/base-ui.ui.badge';
import { Button } from '@manyun/base-ui.ui.button';
import { Calendar } from '@manyun/base-ui.ui.calendar';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Col, Row } from '@manyun/base-ui.ui.grid';

import { FooterToolBar, GutterWrapper } from '@manyun/dc-brain.legacy.components';
import { addWorkDayAction } from '@manyun/dc-brain.legacy.redux/actions/workdayManageActions';

import { MONTH_TAB_LIST } from '../constants';

const StyledCalendar = styled(Calendar)`
  .manyun-fullcalendar-cell {
    text-align: center;
  }
`;

//节假日
const festivalList = [];

//工作日
const workdayList = [];

const submitData = (addWorkDayAction, selectYear) => {
  addWorkDayAction({
    params: {
      year: selectYear,
      statutoryHolidays: festivalList,
      statutoryWorkdays: workdayList,
    },
  });
};

function addFestivalDay(value) {
  festivalList.push(value.format('YYYY-MM-DD'));
}

function deleteFestivalDay(value) {
  const index = festivalList.indexOf(value.format('YYYY-MM-DD'));
  if (index > -1) {
    remove(festivalList, function (v, i) {
      return index === i;
    });
  }
}

function addWorkDay(value) {
  // console.log('addWorkDay-value',value)
  workdayList.push(value.format('YYYY-MM-DD'));
}

function delateWorkDay(value) {
  // console.log('delateWorkDay-value',value)
  const index = workdayList.indexOf(value.format('YYYY-MM-DD'));
  if (index > -1) {
    remove(workdayList, function (v, i) {
      return index === i;
    });
  }
}

//清空節假日以及工作日
function onChange() {
  festivalList.splice(0, festivalList.length);
  workdayList.splice(0, workdayList.length);
}

//去除每月置灰日期
function dateFullCellRender(value, start, end) {
  //   console.log('start',start.format('YYYY-MM-DD'),value.format('YYYY-MM-DD'),end.format('YYYY-MM-DD'),value.isBetween(start,end, undefined, '[]'))
  if (!value.isBetween(start, end, undefined, '[]')) {
    return null;
  } else {
    const currentDay = value.format('YYYY-MM-DD');
    // console.log('currentDay',currentDay,workdayList.includes(currentDay))

    //工作日
    if (workdayList.includes(currentDay)) {
      return (
        <Button
          type="primary"
          shape="circle"
          onClick={e => {
            delateWorkDay(value, e);
          }}
        >
          {value.format('DD')}
        </Button>
      );
    }

    //节假日
    if (festivalList.includes(currentDay)) {
      return (
        <Button
          type="primary"
          shape="circle"
          onClick={() => {
            deleteFestivalDay(value);
          }}
        >
          {value.format('DD')}
        </Button>
      );
    }

    //默认周六周末
    if (value.day() === 0 || value.day() === 6) {
      return (
        <Button
          type="primary"
          shape="circle"
          onClick={() => {
            addWorkDay(value);
          }}
        >
          {value.format('DD')}
        </Button>
      );
    } else {
      return (
        <Button
          ghost={true}
          type="primary"
          shape="circle"
          onClick={() => {
            addFestivalDay(value);
          }}
        >
          {value.format('DD')}
        </Button>
      );
    }
  }
}

//日历头部为空
function headerRender(name) {
  return name;
}

function CreateWorkday({ addWorkDayAction, history }) {
  const [time, setTime] = useState(moment(new Date()));
  const [selectYear, setYear] = useState(moment(new Date()).format('YYYY'));
  const [isOpen, setIsOpen] = useState(false);

  const pd = reduce(
    MONTH_TAB_LIST,
    (result, v, indx) => {
      const index = indx % 4;
      if (index === 0) {
        result.push([v]);
      } else {
        last(result).push(v);
      }
      return result;
    },
    []
  );

  const rowList = pd.map((row, index) => {
    const rows = (
      <Row key={index + '1'} gutter={[8, 10]}>
        {row.map(item => {
          const defaultDay = moment(selectYear + item.key + '01');

          const cols = (
            <Col key={item.key} span={6}>
              <Card bordered={true} hoverable={false} size={'small'}>
                <StyledCalendar
                  headerRender={() => headerRender(item.name)}
                  fullscreen={false}
                  value={defaultDay}
                  dateFullCellRender={v =>
                    dateFullCellRender(
                      v,
                      defaultDay,
                      moment(selectYear + item.key + '01').endOf('month')
                    )
                  }
                />
              </Card>
            </Col>
          );
          return cols;
        })}
      </Row>
    );
    return rows;
  });

  return (
    <GutterWrapper mode="vertical">
      <DatePicker
        placeholder="请选择年份"
        value={time}
        open={isOpen}
        onBlur={() => setIsOpen(false)}
        onFocus={() => setIsOpen(true)}
        format="YYYY"
        onPanelChange={v => {
          setTime(v);
          setYear(v.format('YYYY'));
          setIsOpen(false);
          onChange();
        }}
        mode="year"
      />
      <GutterWrapper mode="horizontal">
        <Badge color="orange" text="法定周末工作日" />
        <Badge color="green" text="法定节假日" />
      </GutterWrapper>
      <GutterWrapper style={{ margin: '10px 0 40px 0' }}>{rowList}</GutterWrapper>
      <FooterToolBar>
        <GutterWrapper>
          <Button type="primary" onClick={() => submitData(addWorkDayAction, selectYear)}>
            提交
          </Button>
          <Button onClick={() => history.goBack()}>取消</Button>
        </GutterWrapper>
      </FooterToolBar>
    </GutterWrapper>
  );
}

const mapDispatchToProps = {
  addWorkDayAction: addWorkDayAction,
};
export default connect(null, mapDispatchToProps)(CreateWorkday);
