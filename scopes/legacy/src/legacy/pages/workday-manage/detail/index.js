import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';

import get from 'lodash/get';
import last from 'lodash/last';
import reduce from 'lodash/reduce';
import moment from 'moment';
import styled from 'styled-components';

import { Badge } from '@manyun/base-ui.ui.badge';
import { Button } from '@manyun/base-ui.ui.button';
import { Calendar } from '@manyun/base-ui.ui.calendar';
import { Card } from '@manyun/base-ui.ui.card';
import { Col, Row } from '@manyun/base-ui.ui.grid';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';
import { workdayManageService } from '@manyun/dc-brain.legacy.services';

import { MONTH_TAB_LIST } from '../constants';

const StyledCalendar = styled(Calendar)`
  .manyun-fullcalendar-cell {
    text-align: center;
  }
`;

//去除每月置灰日期
function dateFullCellRender(value, start, end, festivalList, workdayList) {
  //   console.log('start',start.format('YYYY-MM-DD'),value.format('YYYY-MM-DD'),end.format('YYYY-MM-DD'),value.isBetween(start,end, undefined, '[]'))
  if (!value.isBetween(start, end, undefined, '[]')) {
    return null;
  } else {
    const currentDay = value.format('YYYY-MM-DD');

    //工作日
    if (workdayList.includes(currentDay)) {
      return (
        <Button type="primary" shape="circle">
          {value.format('DD')}
        </Button>
      );
    }

    //节假日
    if (festivalList.includes(currentDay)) {
      return (
        <Button type="primary" shape="circle">
          {value.format('DD')}
        </Button>
      );
    }

    //默认周六周末
    if (value.day() === 0 || value.day() === 6) {
      return (
        <Button type="primary" shape="circle">
          {value.format('DD')}
        </Button>
      );
    } else {
      return (
        <Button ghost={true} type="primary" shape="circle">
          {value.format('DD')}
        </Button>
      );
    }
  }
}

//日历头部月份
function headerRender(year, month) {
  return year + '年 ' + month;
}

function CreateWorkday(value, mode) {
  const [workdayInfo, setworkdayInfo] = useState(null);
  const { id } = useParams();

  useEffect(() => {
    (async () => {
      const { response } = await workdayManageService.fetchWorkdayDetail({ id: Number(id) });
      if (response) {
        setworkdayInfo(response);
      }
    })();
  }, [id]);

  //节假日
  const festivalList = get(workdayInfo, 'statutoryHolidays');

  //工作日
  const workdayList = get(workdayInfo, 'statutoryWorkdays');

  //获取年
  const year = get(workdayInfo, 'year');

  const pd = reduce(
    MONTH_TAB_LIST,
    (result, v, indx) => {
      const index = indx % 4;
      if (index === 0) {
        result.push([v]);
      } else {
        last(result).push(v);
      }
      return result;
    },
    []
  );

  const rowList = pd.map((row, index) => {
    const rows = (
      <Row key={index + '1'} gutter={[8, 10]}>
        {row.map(item => {
          const defaultDay = moment(year + item.key + '01');

          // console.log('defaultDay.endOf("month")', moment(year+item.key+'01').endOf("month"))

          const cols = (
            <Col key={item.key} span={6}>
              <Card bordered={true} hoverable={false}>
                <StyledCalendar
                  headerRender={() => headerRender(year, item.name)}
                  fullscreen={false}
                  value={defaultDay}
                  dateFullCellRender={v =>
                    dateFullCellRender(
                      v,
                      defaultDay,
                      moment(year + item.key + '01').endOf('month'),
                      festivalList,
                      workdayList
                    )
                  }
                  //  dateCellRender ={dateCellRender}
                />
              </Card>
            </Col>
          );
          return cols;
        })}
      </Row>
    );
    return rows;
  });

  return (
    <GutterWrapper mode="vertical">
      <GutterWrapper mode="horizontal">
        <Badge color="orange" text="法定周末工作日" />
        <Badge color="green" text="法定节假日" />
      </GutterWrapper>
      <GutterWrapper style={{ margin: '10px 0 0 0' }}>{rowList}</GutterWrapper>
    </GutterWrapper>
  );
}

export default CreateWorkday;
