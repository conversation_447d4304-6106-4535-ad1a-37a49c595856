import React, { useEffect } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';

import { TinyTable } from '@manyun/dc-brain.legacy.components';
import * as urls from '@manyun/dc-brain.legacy.constants/urls';
import { getWorkdayListActionCreator } from '@manyun/dc-brain.legacy.redux/actions/workdayManageActions';
import * as urlsUtil from '@manyun/dc-brain.legacy.utils/urls';

function WorkdayManage({ workdayList, getWorkdayList }) {
  useEffect(() => {
    getWorkdayList({});
  }, [getWorkdayList]);

  return (
    <TinyTable
      rowKey="id"
      align={'left'}
      columns={columns}
      loading={workdayList.fetching}
      dataSource={workdayList.data}
      pagination={false}
      actions={[
        <div key="create">
          <Link key="new-workday-link" to={urls.WORKDAY_CREATE}>
            <Button type="primary">新增</Button>
          </Link>
        </div>,
      ]}
    />
  );
}

const mapStateToProps = ({ workday_manage: { workdayList } }) => ({ workdayList });
const mapDispatchToProps = { getWorkdayList: getWorkdayListActionCreator };

export default connect(mapStateToProps, mapDispatchToProps)(WorkdayManage);

const columns = [
  {
    title: '年',
    dataIndex: 'year',
    dataType: {
      type: 'link',
      options: {
        to(text, record) {
          return urlsUtil.generateWorkdayUrl({ id: record.id });
        },
      },
    },
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    dataType: 'datetime',
  },
  {
    title: '修改时间',
    dataIndex: 'gmtModified',
    dataType: 'datetime',
  },
  {
    title: '操作',
    dataIndex: 'id',
    fixed: 'right',
    render: (id, record) => (
      <span>
        <Link to={urlsUtil.generateEditWorkdayUrl({ id: id })}>
          <Button style={buttonStyle} type="link" size="small">
            编辑
          </Button>
        </Link>
      </span>
    ),
  },
];

const buttonStyle = { padding: 0, height: 'auto' };
