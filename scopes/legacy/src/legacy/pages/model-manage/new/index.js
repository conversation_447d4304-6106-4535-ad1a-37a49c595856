import React, { Component, useEffect } from 'react';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';

import Form from '@ant-design/compatible/es/form';
import groupBy from 'lodash/groupBy';
import isEqual from 'lodash/isEqual';
import trim from 'lodash/trim';
import uniqWith from 'lodash/uniqWith';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';

import { createModels } from '@manyun/crm.service.create-models';
import { VendorSelect } from '@manyun/crm.ui.vendor-select';
import { DeviceTypeCascader } from '@manyun/resource-hub.ui.device-type-cascader';

import { <PERSON>er<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Wrapper, TinyTable } from '@manyun/dc-brain.legacy.components';
import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';
import { MODEL_LIST } from '@manyun/dc-brain.legacy.constants/urls';
import { VALUE_TYPE_KEY_MAP } from '@manyun/dc-brain.legacy.pages/merged-processed-point/constants';
import { redirectActionCreator } from '@manyun/dc-brain.legacy.redux/actions/routerActions';
import * as specService from '@manyun/dc-brain.legacy.services/specService';

function throws(error) {
  throw new Error(error);
}

const childColumns = [
  { title: '规格名称', dataIndex: 'specName' },
  { title: '规格单位', dataIndex: 'specUnit' },
  {
    title: '规格值',
    dataIndex: 'specValue',
    editable: true,
  },
];

const EditableContext = React.createContext();

const EditableRow = ({ form, index, saveForm, type, outsideKey, ...props }) => {
  useEffect(() => {
    saveForm && saveForm(form, index, type, outsideKey);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <EditableContext.Provider value={form}>
      <tr {...props} />
    </EditableContext.Provider>
  );
};

const EditableFormRow = Form.create({
  name: 'new_model',
})(EditableRow);

class EditableCell extends Component {
  constructor(props) {
    super(props);
    this.state = {
      inputEmpty: true,
      isValid: true,
    };
  }

  async save(e, dataIndex, type) {
    const { record, handleSave, expand } = this.props;
    if (dataIndex === 'specValue') {
      record[dataIndex] = type === 'number' ? e : e.target.value;
    }
    if (dataIndex === 'modelCode') {
      record[dataIndex] = e.target.value;
    }
    if (dataIndex === 'vendorCode' || dataIndex === 'deviceType') {
      record[dataIndex] = e;
    }
    await handleSave(record);
    dataIndex === 'deviceType' && expand(record);
  }

  renderCell = (form, data) => {
    this.form = form;
    const { dataIndex, record, handleExpand } = this.props;

    return dataIndex === 'modelCode' ? (
      <Form.Item style={{ margin: 0 }}>
        <span style={{ marginRight: 4, color: `var(--${prefixCls}-error-color)` }}>*</span>
        {form.getFieldDecorator('modelCode', {
          rules: [
            {
              required: true,
              message: '型号名称必填',
            },
            {
              max: 32,
              message: '最多输入 32 个字符！',
            },
          ],
        })(
          <Input
            style={{ width: 200 }}
            onChange={async e => {
              e.persist();
              await this.save(e, 'modelCode');
            }}
          />
        )}
      </Form.Item>
    ) : dataIndex === 'deviceType' ? (
      <Form.Item style={{ margin: 0 }}>
        <span style={{ marginRight: 4, color: `var(--${prefixCls}-error-color)` }}>*</span>
        {form.getFieldDecorator('deviceType', {
          rules: [
            {
              required: true,
              message: '资产分类必填',
            },
          ],
        })(
          <DeviceTypeCascader
            style={{ width: 300 }}
            dataType={['snDevice', 'noSnDevice', 'it']}
            disabledTypeList={['C0', 'C1']}
            onChange={e => {
              handleExpand(record.key);
              this.save(e, 'deviceType');
            }}
          />
        )}
      </Form.Item>
    ) : dataIndex === 'vendorCode' ? (
      <Form.Item style={{ margin: 0 }}>
        <span style={{ marginRight: 4, color: `var(--${prefixCls}-error-color)` }}>*</span>
        {form.getFieldDecorator('vendorCode', {
          rules: [
            {
              required: true,
              message: '厂商简称必填',
            },
          ],
        })(<VendorSelect style={{ width: 200 }} onChange={e => this.save(e, 'vendorCode')} />)}
      </Form.Item>
    ) : (
      <Form.Item style={{ margin: 0 }}>
        <span
          style={{ marginRight: 4, whiteSpace: 'pre', color: `var(--${prefixCls}-error-color)` }}
        >
          {record.required ? '*' : ' '}
        </span>
        {form.getFieldDecorator('specValue', {
          rules:
            record.valueType === VALUE_TYPE_KEY_MAP.NUMBER
              ? [
                  {
                    required: record.required,
                    message: '规格值必填',
                  },
                ]
              : [
                  {
                    required: record.required,
                    message: '规格值必填',
                  },
                  {
                    max: 30,
                    message: '最多输入 30 个字符！',
                  },
                ],
        })(
          record.valueType === VALUE_TYPE_KEY_MAP.NUMBER ? (
            <InputNumber
              precision={2}
              style={{ width: 220 }}
              min={0.01}
              max={999999.99}
              onChange={e => this.save(e, 'specValue', 'number')}
            />
          ) : (
            <Input
              allowClear
              style={{ width: 220 }}
              onChange={e => this.save(e, 'specValue', 'text')}
            />
          )
        )}
      </Form.Item>
    );
  };

  render() {
    const {
      editable,
      dataIndex,
      record,
      index,
      handleSave,
      expand,
      children,
      data,
      handleExpand,
      ...restProps
    } = this.props;
    return (
      <td {...restProps}>
        {editable ? (
          <EditableContext.Consumer>{form => this.renderCell(form, data)}</EditableContext.Consumer>
        ) : (
          children
        )}
      </td>
    );
  }
}

class NewModel extends Component {
  constructor(props) {
    super(props);
    this.columns = [
      {
        title: '型号名称',
        dataIndex: 'modelCode',
        editable: true,
      },
      {
        title: '资产分类',
        dataIndex: 'deviceType',
        editable: true,
      },
      {
        title: '厂商简称',
        dataIndex: 'vendorCode',
        editable: true,
      },
      {
        title: '操作',
        dataIndex: 'action',
        render: (_, record) =>
          this.state.dataSource.length > 1 ? (
            <Button type="link" compact onClick={() => this.handleDelete(record.key)}>
              删除
            </Button>
          ) : (
            BLANK_PLACEHOLDER
          ),
      },
    ];
    this.state = {
      dataSource: [
        {
          key: 0,
          modelCode: '',
          deviceType: '',
          vendorCode: '',
        },
      ],
      count: 1,
      childItemsMap: [],
      expandedRowKeys: [],
      formsMap: new Map(),
      insideFormsMap: new Map(),
      loading: false,
    };
  }

  components = {
    body: {
      row: EditableFormRow,
      cell: EditableCell,
    },
  };

  handleAdd = () => {
    const { count, dataSource, formsMap } = this.state;
    formsMap.get(count - 1).validateFields(err => {
      if (err) {
        return;
      }
      if (dataSource.length < 500) {
        const newData = {
          key: count,
          modelCode: '',
          deviceType: '',
          vendorCode: '',
        };
        this.setState({
          dataSource: [...dataSource, newData],
          count: count + 1,
        });
      } else {
        message.error('单次批量新增数量上限为500');
      }
    });
  };

  handleDelete = key => {
    const { dataSource } = this.state;
    // if (dataSource.length === 1) {
    //   message.error('型号配置至少有一条');
    //   return;
    // }
    this.setState({ dataSource: dataSource.filter(item => item.key !== key) });
  };

  handleOk = (err, row) => {
    const newData = [...this.state.dataSource];
    const index = newData.findIndex(item => row.key === item.key);
    const item = newData[index];
    item.isOk = err ? false : true;
    newData.splice(index, 1, {
      ...item,
    });
    this.setState({ dataSource: newData });
  };

  handleSave = row => {
    const { childItemsMap, dataSource } = this.state;
    if (row.specCode) {
      const newData = [...childItemsMap[row.key]];
      const index = newData.findIndex(item => row.id === item.id);
      const item = newData[index];
      newData.splice(index, 1, {
        ...item,
      });
      this.setState(({ childItemsMap }) => ({
        childItemsMap: {
          ...childItemsMap,
          [row.key]: newData,
        },
      }));
      return;
    }
    const newData = [...dataSource];
    const index = newData.findIndex(item => row.key === item.key);
    const item = newData[index];
    newData.splice(index, 1, {
      ...item,
    });
    this.setState({ dataSource: newData });
  };

  handleSubmit = async () => {
    const { dataSource, childItemsMap, formsMap, insideFormsMap, expandedRowKeys } = this.state;
    try {
      for (const [, form] of formsMap) {
        await form.validateFieldsAndScroll({ scroll: { offsetTop: 100 } }).catch(throws);
      }
      for (const [rowKey, form] of insideFormsMap) {
        this.setState({ expandedRowKeys: [...expandedRowKeys, Number(rowKey.split('_')[0])] });
        await form.validateFieldsAndScroll({ scroll: { offsetTop: 100 } }).catch(throws);
      }
      const list = dataSource.map(data => ({ ...data, modelCode: trim(data.modelCode) }));
      const productModels = list.map(item => ({
        modelCode: item.modelCode,
        vendorCode: item.vendorCode,
        deviceType: item.deviceType,
      }));
      this.setState({ loading: true });
      const uniqArr = uniqWith(productModels, isEqual);
      if (uniqArr.length !== productModels.length) {
        let multipleModelCode = null;
        const mapper = groupBy(productModels, 'modelCode');
        Object.keys(mapper).forEach(modelCode => {
          const innerMapper = groupBy(mapper[modelCode], 'vendorCode');
          Object.keys(innerMapper).forEach(vendorCode => {
            if (innerMapper[vendorCode].length > 1) {
              multipleModelCode = innerMapper[vendorCode][0].modelCode;
              return;
            }
          });
          if (multipleModelCode) {
            return;
          }
        });
        if (multipleModelCode) {
          message.error(`型号${multipleModelCode}信息重复`);
          this.setState({ loading: false });
          return;
        }
      }
      const { error } = await createModels({
        productModels: list.map(({ modelCode, vendorCode, deviceType, key }) => {
          const specs = childItemsMap[key]
            ? childItemsMap[key].map(spec => ({
                specId: spec.id,
                specName: spec.specName,
                modelCode,
                specValue: spec.specValue,
              }))
            : [];

          return {
            modelCode,
            vendorCode: vendorCode,
            deviceType: deviceType,
            specModels: specs,
          };
        }),
      });
      this.setState({ loading: false });
      if (error) {
        message.error(error.message);
        return;
      }
      message.success('批量新增完成');
      this.props.redirect(MODEL_LIST);
    } catch (error) {}
  };

  expandedRowRender = ({ key }) => {
    const childData = this.state.childItemsMap[key];

    return (
      <TinyTable
        size="small"
        align="left"
        rowKey={row => row.id}
        columns={this.getColumns(childColumns)}
        dataSource={childData}
        components={this.components}
        pagination={false}
        onRow={(record, index) => {
          return {
            record,
            index,
            saveForm: this.saveForm,
            type: 'inside',
            outsideKey: key,
          };
        }}
      />
    );
  };

  getColumns = columns =>
    columns.map(col => {
      if (!col.editable) {
        return col;
      }
      return {
        ...col,
        onCell: record => ({
          record,
          editable: col.editable,
          dataIndex: col.dataIndex,
          handleSave: this.handleSave,
          expand: this.expand,
          data: this.state.dataSource,
          handleExpand: this.handleExpand,
        }),
      };
    });

  expand = async ({ deviceType, key }) => {
    const { response } = await specService.fetchSpecList({ deviceType });
    if (response) {
      const { data } = response;
      data.forEach(item => {
        item.key = key;
        item.inputEmpty = item.required ? true : null;
      });
      this.setState(({ childItemsMap }) => ({
        childItemsMap: {
          ...childItemsMap,
          [key]: data,
        },
      }));
    }
  };

  handleExpand = async key => {
    // if (type === 'remove') {
    //   this.setState(({ expandedRowKeys }) => ({
    //     expandedRowKeys: expandedRowKeys.filter(item => item !== key),
    //   }));
    // }
    this.setState(({ expandedRowKeys }) => ({ expandedRowKeys: [...expandedRowKeys, key] }));
  };

  onExpand = (expanded, record) => {
    const { expandedRowKeys } = this.state;
    const filtered = expandedRowKeys;
    if (expandedRowKeys.includes(record.key)) {
      filtered.splice(
        filtered.findIndex(item => item === record.key),
        1
      );
    } else {
      filtered.push(record.key);
    }
    this.setState({ expandedRowKeys: filtered });
  };

  saveForm = (form, rowKey, type, key) => {
    if (type === 'outside') {
      this.setState({
        formsMap: this.state.formsMap.set(rowKey, form),
      });
    } else {
      const itemKey = key + '_' + rowKey;
      const newMap = this.state.insideFormsMap;
      newMap.delete(itemKey);
      this.setState({
        insideFormsMap: newMap.set(itemKey, form),
      });
    }
  };

  render() {
    const { dataSource, expandedRowKeys, loading } = this.state;

    return (
      <GutterWrapper>
        <TinyTable
          style={{ marginTop: 0, marginBottom: 40 }}
          align="left"
          rowKey={row => row.key}
          components={this.components}
          rowClassName={() => 'editable-row'}
          dataSource={dataSource}
          columns={this.getColumns(this.columns)}
          expandedRowRender={this.expandedRowRender}
          expandedRowKeys={expandedRowKeys}
          pagination={false}
          footer={() => (
            <Button type="link" compact onClick={this.handleAdd}>
              +&nbsp;&nbsp;&nbsp;添加
            </Button>
          )}
          onExpand={this.onExpand}
          onRow={(record, index) => {
            return {
              record,
              index,
              saveForm: this.saveForm,
              type: 'outside',
            };
          }}
        />
        <FooterToolBar>
          <Space>
            <Button type="primary" loading={loading} onClick={this.handleSubmit}>
              提交
            </Button>
            <Button
              onClick={() => {
                this.props.history.goBack();
              }}
            >
              取消
            </Button>
          </Space>
        </FooterToolBar>
      </GutterWrapper>
    );
  }
}

const mapDispatchToProps = {
  redirect: redirectActionCreator,
};

export default connect(null, mapDispatchToProps)(withRouter(NewModel));
