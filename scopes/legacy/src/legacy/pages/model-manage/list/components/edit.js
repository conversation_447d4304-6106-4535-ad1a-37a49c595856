import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import QuestionCircleOutlined from '@ant-design/icons/es/icons/QuestionCircleOutlined';

import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';

import { VendorSelect } from '@manyun/crm.ui.vendor-select';
import { fetchSpecs } from '@manyun/resource-hub.service.fetch-specs';
import { DeviceTypeText } from '@manyun/resource-hub.ui.device-type-text';

import { <PERSON><PERSON><PERSON>, TinyDrawer, TinyModal, TinyTable } from '@manyun/dc-brain.legacy.components';
import { VALUE_TYPE_KEY_MAP } from '@manyun/dc-brain.legacy.pages/merged-processed-point/constants';
import { fetchModelListPage } from '@manyun/dc-brain.legacy.redux/actions/modelActions';
import * as equipmentService from '@manyun/dc-brain.legacy.services/equipmentService';
import * as modelService from '@manyun/dc-brain.legacy.services/modelService';
import * as spareService from '@manyun/dc-brain.legacy.services/spareService';
import { getDeviceTypeFullMetaName } from '@manyun/dc-brain.legacy.utils/deviceType';

const getColumns = ctx => {
  const items = [
    {
      title: '三级分类',
      dataIndex: 'deviceType',
      render: deviceType => <DeviceTypeText code={deviceType} />,
    },
    {
      title: '机房',
      dataIndex: 'idcTag',
      render: (text, record) => record.spaceGuid?.idcTag || text,
    },
    {
      title: '楼栋',
      dataIndex: 'blockTag',
      render: (_, record) => record.spaceGuid?.blockTag || record.blockGuid.split('.')[1],
    },
    {
      title: '包间',
      dataIndex: 'roomTag',
      render: (text, record) => record.spaceGuid?.roomTag || text,
    },
    {
      title: '厂商',
      dataIndex: 'vendor',
    },
    {
      title: '型号',
      dataIndex: 'productModel',
    },
  ];
  if (ctx.props.record.isNumbered) {
    items.splice(0, 0, {
      title: '设备名称',
      dataIndex: 'name',
    });
  } else {
    items.push({
      title: '数量',
      dataIndex: 'totalNum',
    });
  }
  return items;
};

class EditModel extends Component {
  state = {
    loading: false,
    editInfo: [],
    vendorCodeList: [],
    vendorCodeLoading: false,
    total: 0,
    pageNum: 1,
    pageSize: 10,
  };

  onVisible = async () => {
    const { deviceType, modelCode, vendorCode } = this.props.record;
    const { data, error } = await fetchSpecs({ deviceType, modelCode, vendor: vendorCode });
    if (error) {
      message.error(error.message);
      return;
    }
    this.setState({
      specInfo: data.data,
      editVisible: true,
    });
  };

  onChangePage = async (pageNum, pageSize) => {
    const response = await this.fetchDevicePage(pageNum, pageSize);
    this.setState({
      editInfo: response.data,
      pageNum,
      pageSize,
    });
  };

  fetchDevicePage = async (pageNum, pageSize) => {
    const { isNumbered, modelCode, deviceType, vendorCode } = this.props.record;
    const { response } = isNumbered
      ? await equipmentService.fetchEquipmentListPage({
          vendor: vendorCode,
          deviceTypeList: [deviceType],
          productModel: modelCode,
          pageNum,
          pageSize,
        })
      : await spareService.fetchSpares({
          productModel: modelCode,
          pageNum,
          pageSize,
        });
    return response;
  };

  onQueryVisible = async () => {
    const { record, form } = this.props;
    form.validateFields(async err => {
      if (err) {
        return;
      }
      //暂时不实现修改耗材联动提示 http://chandao.manyun-local.com/zentao/bug-view-3196.html
      if (!record.isNumbered) {
        this.handleSubmit();
        return;
      }
      const response = await this.fetchDevicePage(1, 10);
      this.setState(
        {
          visible: response.data.length ? true : false,
          editInfo: response.data,
          total: response.total,
          pageNum: 1,
          pageSize: 10,
        },
        () => {
          !this.state.visible && this.handleSubmit();
        }
      );
    });
  };

  onClose = () => {
    this.setState({ editVisible: false, visible: false });
  };

  handleSubmit = async () => {
    const { form, record } = this.props;
    const { editInfo, specInfo } = this.state;
    const data = form.getFieldsValue();
    const list = [];
    data.id = record.id;
    if (data.specModels) {
      data.specModels.forEach((item, index) => {
        item.specId = item ? index : '';
        item.modelCode = record.modelCode;
      });
      specInfo.forEach(item => {
        data.specModels[item.id].specName = item.specName;
      });
      data.specModels.forEach(item => list.push(item));
      data.specModels = list;
    } else {
      data.specModels = [];
    }
    const { error } = await modelService.editModel(data);
    if (error) {
      message.error(error || '型号编辑失败');
      return;
    }
    editInfo.length
      ? message.success('型号编辑成功且同步更新设备')
      : message.success('型号编辑成功');
    this.onClose();
    this.props.fetchModelListPage();
  };

  render() {
    const { form, record, normalizedList } = this.props;
    const { editVisible, visible, editInfo, specInfo, total, pageNum, pageSize } = this.state;
    const { getFieldDecorator } = form;

    return [
      <Button key="btn-edit" type="link" compact onClick={this.onVisible}>
        编辑
      </Button>,
      <TinyDrawer
        key="drawer"
        width={416}
        title={<Typography.Title level={4}>编辑型号</Typography.Title>}
        visible={editVisible}
        destroyOnClose
        zIndex={998}
        onClose={this.onClose}
        onSubmit={this.onQueryVisible}
        onCancel={this.onClose}
      >
        <Form colon={false}>
          <TinyCard title="基本配置">
            <Form.Item label="型号名称">
              {getFieldDecorator('modelCode', {
                initialValue: record.modelCode,
              })(<Input disabled={!!record.id} />)}
            </Form.Item>
            <Form.Item label="所属厂商">
              {getFieldDecorator('vendorCode', {
                initialValue: record.vendorCode,
                rules: [{ required: true, message: '所属厂商必选！' }],
              })(<VendorSelect />)}
            </Form.Item>
            <Form.Item label="所属设备类型">
              {getFieldDecorator('deviceType', {
                initialValue: normalizedList
                  ? getDeviceTypeFullMetaName(record.deviceType, normalizedList)
                  : '',
              })(<Input disabled={!!record.id} />)}
            </Form.Item>
            <Form.Item label="备注">
              {getFieldDecorator('description', {
                initialValue: record.description,
                rules: [
                  {
                    max: 50,
                    message: '最多输入 50 个字符！',
                  },
                ],
              })(<Input.TextArea autoSize={{ minRows: 3 }} />)}
            </Form.Item>
          </TinyCard>
          <TinyCard
            title={
              <>
                规格信息
                <Tooltip title="规格显示为空时代表当前型号无对应规格">
                  <QuestionCircleOutlined />
                </Tooltip>
              </>
            }
          >
            {specInfo ? (
              <>
                {specInfo.map(item => (
                  <Form.Item key={item.id} label={item.specName}>
                    {getFieldDecorator(`specModels[${item.id}].specValue`, {
                      rules:
                        item.valueType === VALUE_TYPE_KEY_MAP.NUMBER
                          ? [{ required: item.required, message: `${item.specName}必填！` }]
                          : [
                              { required: item.required, message: `${item.specName}必填！` },
                              {
                                max: 30,
                                message: '最多输入 30 个字符！',
                              },
                            ],
                      initialValue: item.specValue,
                    })(
                      item.valueType === VALUE_TYPE_KEY_MAP.NUMBER ? (
                        <InputNumber
                          formatter={value =>
                            item.specUnit ? `${value}${item.specUnit}` : `${value}`
                          }
                          parser={value => {
                            if (!item.specUnit) {
                              return `${value}`;
                            }
                            return value.replace(item.specUnit, '');
                          }}
                          min={0.01}
                          max={999999.99}
                          precision={2}
                          style={{ width: 220, marginLeft: 8 }}
                        />
                      ) : (
                        <Input allowClear style={{ width: 220, marginLeft: 8 }} />
                      )
                    )}
                  </Form.Item>
                ))}
              </>
            ) : (
              ''
            )}
          </TinyCard>
        </Form>
      </TinyDrawer>,
      <TinyModal
        key="model"
        width="900px"
        title="确定同步更新对应设备类型下设备的规格信息吗？"
        // title={
        //   record.isNumbered
        //     ? '确定同步更新对应设备类型下设备的规格信息吗？'
        //     : '确定同步更新对应资产分类下耗材的规格信息吗？'
        // }
        visible={visible}
        zIndex={999}
        centered
        onOk={this.handleSubmit}
        onCancel={this.onClose}
        onClose={this.onClose}
      >
        <TinyTable
          rowKey={({ id, guid }) => `${id}+${guid}`}
          columns={getColumns(this)}
          dataSource={editInfo}
          pagination={{
            total,
            current: pageNum,
            pageSize,
            onChange: this.onChangePage,
          }}
        />
      </TinyModal>,
    ];
  }
}

const mapStateToProps = ({ modelManage: { pagination, normalizedList } }) => ({
  pagination,
  normalizedList,
});

const mapDispatchToProps = {
  fetchModelListPage,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(Form.create({ name: 'mode_edit' })(EditModel));
