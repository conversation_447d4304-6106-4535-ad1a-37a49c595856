import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Tag } from '@manyun/base-ui.ui.tag';

import { TinyModal } from '@manyun/dc-brain.legacy.components';
import * as modelService from '@manyun/dc-brain.legacy.services/modelService';
import { getDeviceTypeFullMetaName } from '@manyun/dc-brain.legacy.utils/deviceType';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';

class ModelInfo extends Component {
  state = {
    visible: false,
    modelInfo: [],
  };

  onVisible = async () => {
    const { record } = this.props;
    const { response } = await modelService.getModelDetail({ id: record.id });
    this.setState({
      visible: true,
      modelInfo: response,
    });
  };

  onClose = () => {
    this.setState({
      visible: false,
    });
  };

  render() {
    const { text, type, normalizedList } = this.props;
    const { visible, modelInfo } = this.state;
    const params = {
      deviceType: modelInfo.deviceType,
      productModel: modelInfo.modelCode,
      vendor: modelInfo.vendorCode,
    };

    return [
      <Button key="btn-view" type={type} onClick={this.onVisible} compact>
        {text}
      </Button>,
      <TinyModal
        width="960px"
        title="型号管理"
        key="model"
        visible={visible}
        onCancel={this.onClose}
        onClose={this.onClose}
        footer={null}
      >
        <Descriptions bordered>
          <Descriptions.Item label="型号名称" labelStyle={{ width: 105 }}>
            {modelInfo.modelCode}
          </Descriptions.Item>
          <Descriptions.Item
            label="所属厂商"
            labelStyle={{ width: 105 }}
            contentStyle={{ minWidth: 120 }}
          >
            {modelInfo.vendorCode}
          </Descriptions.Item>
          <Descriptions.Item label="所属设备类型" labelStyle={{ width: 135 }}>
            {modelInfo.deviceType
              ? getDeviceTypeFullMetaName(modelInfo.deviceType, normalizedList)
              : ''}
          </Descriptions.Item>
          <Descriptions.Item label="对应设备数" labelStyle={{ width: 120 }}>
            {typeof modelInfo.deviceNum === 'number' ? (
              <Link
                to={
                  modelInfo.isNumbered
                    ? urls.generateDeviceListUrl(params)
                    : urls.generateSpareListUrl(params)
                }
              >
                {modelInfo.deviceNum}
              </Link>
            ) : (
              0
            )}
          </Descriptions.Item>
          <Descriptions.Item label="备注" span={2}>
            {modelInfo.description}
          </Descriptions.Item>
          <Descriptions.Item label="规格">
            {modelInfo.specInfos?.map(item => (
              <Tag key={item.id}>{`${item.specName}:${item.specValue}${item.specUnit ?? ''}`}</Tag>
            ))}
          </Descriptions.Item>
        </Descriptions>
      </TinyModal>,
    ];
  }
}

const mapStateToProps = ({ modelManage: { normalizedList } }) => {
  return {
    normalizedList,
  };
};

export default connect(mapStateToProps, null)(ModelInfo);
