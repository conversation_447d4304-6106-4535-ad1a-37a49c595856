import React, { Component } from 'react';
import { connect, useDispatch } from 'react-redux';
import { Link } from 'react-router-dom';

import { FiltersForm, Form } from '@galiojs/awesome-antd';

import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Input } from '@manyun/base-ui.ui.input';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { VendorSelect } from '@manyun/crm.ui.vendor-select';
import { DeviceTypeCascader } from '@manyun/resource-hub.ui.device-type-cascader';

import { GutterWrapper, TinyCard, TinyTable, UserSelect } from '@manyun/dc-brain.legacy.components';
import {
  deleteModelAction,
  fetchModelListPage,
  modelActions,
  setModelPagination,
} from '@manyun/dc-brain.legacy.redux/actions/modelActions';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';

import EditModel from '../list/components/edit';
import ModelInfo from '../list/components/model-info';

const getColumns = (ctx, updateAuthorized, deleteAuthorized) => {
  return [
    {
      title: '型号名称',
      dataIndex: 'modelCode',
    },
    {
      title: '三级分类',
      dataIndex: 'deviceType',
      render: text => ctx.props.normalizedList?.[text]?.metaName,
    },
    {
      title: '厂商简称',
      dataIndex: 'vendorCode',
    },
    {
      title: '对应规格数',
      dataIndex: 'specNum',
      render: text => (typeof text === 'number' ? text : 0),
    },
    {
      title: '对应设备数',
      dataIndex: 'deviceNum',
      render: (text, record) => {
        const params = {
          deviceType: record.deviceType,
          productModel: record.modelCode,
          vendor: record.vendorCode,
        };
        return typeof text === 'number' ? (
          <Link
            to={
              record.isNumbered
                ? urls.generateDeviceListUrl(params)
                : urls.generateSpareListUrl(params)
            }
          >
            {text}
          </Link>
        ) : (
          0
        );
      },
    },
    {
      title: '创建时间',
      dataIndex: 'gmtCreate',
    },
    {
      title: '更新时间',
      dataIndex: 'gmtModified',
    },
    {
      title: '更新人',
      dataIndex: 'operatorName',
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      render: (__, record) => {
        return (
          <>
            <ModelInfo text="查看" type="link" record={record} />
            {updateAuthorized && (
              <>
                <Divider type="vertical" />
                <EditModel record={record} />
              </>
            )}
            {deleteAuthorized && (
              <>
                <Divider type="vertical" />
                <DeleteConfirm
                  targetName={record.modelCode + '的型号'}
                  reasonPlaceholder={'请输入删除' + record.modelCode + '的型号的原因'}
                  onOk={({ reason }) => ctx.delete(record, reason)}
                >
                  <Button type="link" compact>
                    删除
                  </Button>
                </DeleteConfirm>
              </>
            )}
          </>
        );
      },
    },
  ];
};

class ModelListLegacy extends Component {
  componentDidMount() {
    this.props.fetchModelListPage();
  }

  searchModelList = () => {
    this.props.resetModelPpagination();
    this.props.fetchModelListPage();
  };

  handleReset = () => {
    this.props.dispatch(modelActions.resetSearchValues());
    this.props.setModelPagination({ pageNum: 1, pageSize: 10 });
  };

  delete = ({ id }, operatorNotes) => {
    const { deleteModelAction, fetchModelListPage } = this.props;

    return new Promise(resolve => {
      deleteModelAction({
        params: { id, operatorNotes },
        successCb: () => {
          resolve(true);
          fetchModelListPage(true);
        },
        errorCb: () => {
          resolve(false);
        },
      });
    });
  };

  render() {
    const {
      form,
      modelPage,
      loading,
      pagination,
      fields,
      updateSearchValues,
      setModelPagination,
      authorized,
      updateAuthorized,
      deleteAuthorized,
    } = this.props;

    return (
      <GutterWrapper mode="vertical">
        <TinyCard>
          <FiltersForm
            form={form}
            fields={Object.keys(fields).map(name => {
              const field = fields[name];
              return {
                ...field,
                name: name.split('.'),
              };
            })}
            items={[
              {
                label: '型号名称',
                name: 'modelCode',
                control: <Input allowClear />,
              },
              {
                label: '资产分类',
                name: 'deviceTypeList',
                control: (
                  <DeviceTypeCascader
                    dataType={['snDevice', 'noSnDevice']}
                    disabledTypeList={['C0', 'C1']}
                    allowClear
                    multiple
                    maxTagCount={1}
                  />
                ),
              },
              {
                label: '厂商简称',
                name: 'vendorCode',
                control: <VendorSelect allowClear />,
              },
              {
                label: '更新人',
                name: 'operatorId',
                control: <UserSelect allowClear />,
              },
              {
                label: '创建时间',
                name: 'createTime',
                span: 2,
                control: (
                  <DatePicker.RangePicker
                    showTime
                    allowClear
                    format="YYYY-MM-DD HH:mm:ss"
                    placeholder={['开始时间', '结束时间']}
                  />
                ),
              },
            ]}
            onFieldsChange={changedFields => {
              updateSearchValues(
                changedFields.reduce((mapper, field) => {
                  const name = field.name.join('.');
                  mapper[name] = {
                    ...field,
                    name,
                  };
                  return mapper;
                }, {})
              );
            }}
            onSearch={this.searchModelList}
            onReset={this.handleReset}
          />
        </TinyCard>
        <TinyCard>
          <TinyTable
            rowKey="id"
            columns={getColumns(this, updateAuthorized, deleteAuthorized)}
            scroll={{ x: 'max-content' }}
            dataSource={modelPage.list}
            loading={loading}
            actions={
              authorized && (
                <Link to={urls.generateModelNewUrl()}>
                  <Button type="primary">新建</Button>
                </Link>
              )
            }
            pagination={{
              total: modelPage.total,
              current: pagination.pageNum,
              pageSize: pagination.pageSize,
              onChange: (current, size) => setModelPagination({ pageNum: current, pageSize: size }),
            }}
          />
        </TinyCard>
      </GutterWrapper>
    );
  }
}
function ModelList(props) {
  const [form] = Form.useForm();
  const dispatch = useDispatch();

  return <ModelListLegacy form={form} dispatch={dispatch} {...props} />;
}

const mapStateToProps = ({
  modelManage: { loading, modelPage, searchValues, pagination, normalizedList },
}) => ({
  loading,
  modelPage,
  pagination,
  normalizedList,
  fields: searchValues,
});
const mapDispatchToProps = {
  modelActions,
  fetchModelListPage,
  deleteModelAction,
  updateSearchValues: modelActions.updateSearchValues,
  setModelPagination,
  resetModelPpagination: modelActions.resetModelPpagination,
};

const ConnectModelList = connect(mapStateToProps, mapDispatchToProps)(ModelList);

export default function AuthorizedModelList(props) {
  const [authorized] = useAuthorized({ checkByCode: 'ele_model_batch_add' });
  const [updateAuthorized] = useAuthorized({ checkByCode: 'ele_model_update' });
  const [deleteAuthorized] = useAuthorized({ checkByCode: 'ele_model_delete' });

  return (
    <ConnectModelList
      authorized={authorized}
      updateAuthorized={updateAuthorized}
      deleteAuthorized={deleteAuthorized}
      {...props}
    />
  );
}
