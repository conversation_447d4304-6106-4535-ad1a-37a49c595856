import React from 'react';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import type { Position } from '@manyun/base-ui.ui.spotlight';

export type Values = {
  position: Position | null;
};

export type EventType = 'didClose';
export type ListenerTypes = {
  didClose: () => void;
};

export type Handlers = {
  register<E extends keyof ListenerTypes = EventType>(
    eventType: E,
    listener: ListenerTypes[E]
  ): void;
  moveTo(position: Position): void;
  close(): void;
};

export type SpotlightContextProps = [Values, Handlers];

const noop = () => {};
const SpotlightContext = React.createContext<[Values, Handlers]>([
  { position: null },
  {
    register: noop,
    moveTo: noop,
    close: noop,
  },
]);

export const SpotlightProvider: React.FC = ({ children }) => {
  const listenersMapRef = React.useRef<Map<EventType, ListenerTypes[keyof ListenerTypes]>>(
    new Map()
  );
  const register = React.useCallback<Handlers['register']>((eventType, listener) => {
    listenersMapRef.current.set(eventType, listener);
  }, []);
  const [position, setPosition] = React.useState<Position | null>(null);
  const moveTo = React.useCallback((_position: Position) => {
    setPosition(_position);
  }, []);
  const close = React.useCallback(() => {
    setPosition(null);
    listenersMapRef.current.get('didClose')?.();
  }, []);
  const memoizedPosition = useDeepCompareMemo(() => position, [position]);
  const value = React.useMemo<[Values, Handlers]>(
    () => [{ position: memoizedPosition }, { register, moveTo, close }],
    [memoizedPosition, register, moveTo, close]
  );

  return <SpotlightContext.Provider value={value}>{children}</SpotlightContext.Provider>;
};

export const useSpotlight = () => React.useContext(SpotlightContext);
