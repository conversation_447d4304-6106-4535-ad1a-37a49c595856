import React from 'react';

import { useSpotlight } from './spotlight-context-provider';

export function useElementSpotlight({ focus }: { focus: boolean }) {
  const elementRef = React.useRef<HTMLElement | null>(null);
  const [, { register, moveTo, close }] = useSpotlight();
  React.useEffect(() => {
    let waitUntilRenderedTimer: number | null = null;
    let spotlightMovingTimer: number | null = null;
    if (focus) {
      if (elementRef.current !== null) {
        waitUntilRenderedTimer = window.setTimeout(() => {
          const element = elementRef.current!;
          if (element) {
            element.scrollIntoView({ behavior: 'smooth' });
            element.style.zIndex = '1100';
            register('didClose', () => {
              element.style.zIndex = 'auto';
            });
            element.onclick = () => {
              close();
            };
            spotlightMovingTimer = window.setTimeout(() => {
              const { left, top, width, height } = element.getBoundingClientRect();
              moveTo({
                x: (left + width / 2) / window.innerWidth,
                y: (top + height / 2) / window.innerHeight,
              });
            }, 1500);
          }
        }, 500);
      }
    }

    return () => {
      if (waitUntilRenderedTimer !== null) {
        window.clearTimeout(waitUntilRenderedTimer);
      }
      if (spotlightMovingTimer !== null) {
        window.clearTimeout(spotlightMovingTimer);
      }
    };
  }, [focus, register, moveTo, close]);

  return [elementRef] as const;
}
