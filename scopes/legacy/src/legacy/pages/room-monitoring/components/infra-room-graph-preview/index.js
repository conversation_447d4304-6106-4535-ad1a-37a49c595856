import React, { useEffect, useState } from 'react';
import { connect, useSelector } from 'react-redux';

import debug from 'debug';
// import Konva from 'konva';
import cloneDeep from 'lodash.clonedeep';
import get from 'lodash.get';
import template from 'lodash.template';

import { usePrevious } from '@manyun/base-ui.hook.use-previous';
import { Card } from '@manyun/base-ui.ui.card';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import {
  AuraGraphixPreview as Preview,
  ThemeCompositions,
  calculatePointOnLine,
  createStore,
  getLineLength,
  getPoints2d,
} from '@manyun/dc-brain.aura-graphix';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import '@manyun/dc-brain.ui.custom-shapes/alerting-marker';
import '@manyun/dc-brain.ui.custom-shapes/highlight';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { OutletCabinet, RelatedRooms } from '@manyun/monitoring.page.room-view';
import { useBulletCameraPreview } from '@manyun/monitoring.page.room-view/dist/components/bullet-camera';
import { CamerasLink } from '@manyun/monitoring.page.room-view/dist/components/cameras-link';
import { generateSpaceOrDeviceRoutePath } from '@manyun/monitoring.route.monitoring-routes';
import {
  addRealtimeNAlarmsDataSubscriptionActionCreator,
  getMonitoringData,
  removeRealtimeNAlarmsDataSubscriptionActionCreator,
} from '@manyun/monitoring.state.subscriptions';
import { GraphPointLineModal } from '@manyun/monitoring.ui.graph-point-line-modal';
import { CorePointsCard } from '@manyun/resource-hub.ui.core-points-card';

import { STATUS_MAP } from '@manyun/dc-brain.legacy.constants/statusColor';
import { SUBSCRIPTIONS_MODE } from '@manyun/dc-brain.legacy.constants/subscriptions';
import { tryEvaluate } from '@manyun/dc-brain.legacy.pages/topology-graphix/utils/expr-eval';
import { roomMonitoringActions } from '@manyun/dc-brain.legacy.redux/actions/roomMonitoringActions';
import { getSpaceGuid } from '@manyun/dc-brain.legacy.utils';
import { generateGetDeviceAlarmStatus } from '@manyun/dc-brain.legacy.utils/device';

import waterLeakMarkerPng from './assets/marker.png';

const logger = debug('room-monitoring:infra-room-graph-preview');

// NOTE: can not use CSS Variable here
const ALERTING_COLOR = '#ff4d4f';
const WATER_LEAK_MARKER_HIGHLIGHT_ALERTING_COLOR = 'rgb(245, 34, 45)';

function InfraRoomGraphPreview({
  roomInfo,
  spaceGuids,
  highlights = [],
  graph,
  deviceGuids,
  subscribe,
  unsubscribe,
  resetInfraRoomGraphPreview,
  relateRooms,
  hasCard,
  isRacksRoom,
}) {
  // External hooks
  // ------
  const [, { open, setDevice }] = useBulletCameraPreview();
  const config = useSelector(selectCurrentConfig);
  const configUtil = React.useMemo(() => new ConfigUtil(config), [config]);
  const upsOutgoingCabinetDeviceType = configUtil.getOneDeviceType(
    ConfigUtil.constants.deviceTypes.UPS_OUTGOING_CABINET
  );
  const lvOutgoingCabinetDeviceType = configUtil.getOneDeviceType(
    ConfigUtil.constants.deviceTypes.LV_OUTGOING_CABINET
  );
  const {
    typeofProximityReader,
    typeofBulletCamera,
    typeofSmokeDetector,
    typeofThermalDetector,
    typeofAirSampler,
    typeofWaterLeakDetector,
    typeofPositioningWaterLeakDetector,

    typeofCustomDevice,
  } = useTypeofFuncs(configUtil);

  // Internal hooks
  // ------
  const [store, setStore] = useState(null);
  const [idc, block, room] = spaceGuids;
  const highlighsStr = highlights.join('_$$_');
  const checkHighlighting = React.useCallback(
    deviceGuid => highlighsStr.split('_$$_').includes(deviceGuid),
    [highlighsStr]
  );
  const blockGuid = getSpaceGuid(idc, block);
  React.useEffect(() => {
    return () => {
      !isRacksRoom && resetInfraRoomGraphPreview();
    };
  }, [isRacksRoom, resetInfraRoomGraphPreview]);

  const [pointGuids, setPointGuids] = useState([]);
  const [pointLineModalVisible, setPointLineModalVisible] = useState(false);

  useEffect(() => {
    let _store;

    const mode = SUBSCRIPTIONS_MODE.REALTIME_N_ALARMS;
    const moduleId = 'infra-room-graph-preview';

    if (graph) {
      subscribe({ mode, blockGuid, moduleId, deviceGuids });
      const __store = createStore();
      const clone = cloneDeep(graph);
      const waterLeakMarkers = [];
      clone.pages[0].children.forEach(elem => {
        if (elem.custom?.type === 'device_group') {
          const deviceRect = elem.children.find(child => child.custom?.type === 'device');
          if (deviceRect) {
            if (
              typeofWaterLeakDetector(deviceRect.custom.deviceType) ||
              typeofPositioningWaterLeakDetector(deviceRect.custom.deviceType)
            ) {
              logger(
                'Found a water leak detector which is not type of a line: ',
                deviceRect,
                ' under group: ',
                elem
              );
            }
            const isCustomDevice = typeofCustomDevice(deviceRect.custom.deviceType);
            if (!isCustomDevice) {
              const animating = checkHighlighting(deviceRect.custom.deviceGuid);
              elem.children.push(
                {
                  id: elem.id + '_highlight',
                  type: 'highlight',
                  width: elem.width,
                  height: elem.height,
                  locked: true,
                  animating,
                },
                {
                  id: `${elem.id}_$$_alerting-marker`,
                  type: 'alerting-marker',
                  x: elem.width - 13,
                  y: 13,
                  locked: true,
                  animating: false,
                }
              );
            }
          }
        }
        if (elem.custom?.type === 'device') {
          if (typeofPositioningWaterLeakDetector(elem.custom.deviceType)) {
            if (elem.type === 'line') {
              waterLeakMarkers.push({
                id: `${elem.id}_$$_water-leak-marker_group`,
                type: 'group',
                x: 0,
                y: 0,
                width: 36,
                height: 36,
                locked: true,
                visible: false,
                children: [
                  {
                    id: `${elem.id}_$$_water-leak-marker_highlight`,
                    type: 'highlight',
                    shape: 'circle',
                    x: 18,
                    y: 18,
                    radius: 36,
                    fill: WATER_LEAK_MARKER_HIGHLIGHT_ALERTING_COLOR,
                    stroke: WATER_LEAK_MARKER_HIGHLIGHT_ALERTING_COLOR,
                    shadowColor: WATER_LEAK_MARKER_HIGHLIGHT_ALERTING_COLOR,
                    locked: true,
                    animating: true,
                  },
                  {
                    id: `${elem.id}_$$_water-leak-marker`,
                    type: 'image',
                    src: waterLeakMarkerPng,
                    x: 0,
                    y: 0,
                    width: 36,
                    height: 36,
                    locked: true,
                  },
                ],
              });
            } else {
              logger('Found a PositioningWaterLeakDetector which is not type of a line: ', elem);
            }
          }
        }
      });
      if (waterLeakMarkers.length > 0) {
        clone.pages[0].children.push(...waterLeakMarkers);
      }
      __store.loadJSON(clone).then(() => {
        _store = __store;
        setStore(__store);
      });
    }
    return () => {
      if (_store) {
        unsubscribe({ mode, blockGuid, moduleId });
        setStore(null);
      }
    };
  }, [
    blockGuid,
    graph,
    deviceGuids,
    subscribe,
    unsubscribe,
    typeofCustomDevice,
    typeofWaterLeakDetector,
    typeofPositioningWaterLeakDetector,
    checkHighlighting,
  ]);

  const { devicesRealtimeData, devicesAlarmsData } = useSelector(getMonitoringData);
  const getDeviceAlarmStatus = generateGetDeviceAlarmStatus(devicesAlarmsData);
  const checkAlerting = React.useCallback(
    deviceGuid => getDeviceAlarmStatus(deviceGuid) !== STATUS_MAP.DEFAULT,
    [getDeviceAlarmStatus]
  );
  const prevAlarmsData = usePrevious(devicesAlarmsData);
  const prevReltimeData = usePrevious(devicesRealtimeData);
  React.useEffect(() => {
    if (!store) {
      return;
    }

    const proximityReaderElements = [];
    const bulletCameraElements = [];
    const smokeDetectorElements = [];
    const thermalDetectorElements = [];
    const airSamplerElements = [];
    const waterLeakDetectors = [];
    const positioningWaterLeakDetectors = [];
    const otherDeviceElements = [];
    const pointTextElements = [];
    store.activePage.children.forEach(child => {
      const isDevice = child.custom?.type === 'device';
      const isDeviceGroup = child.custom?.type === 'device_group';
      if (isDevice || isDeviceGroup) {
        let deviceElement = child;
        if (isDeviceGroup) {
          deviceElement = child.children.find(el => el.custom?.type === 'device');
        }
        const deviceType = deviceElement.custom.deviceType;
        if (typeofProximityReader(deviceType)) {
          proximityReaderElements.push(deviceElement);
        } else if (typeofBulletCamera(deviceType)) {
          bulletCameraElements.push(deviceElement);
        } else if (typeofSmokeDetector(deviceType)) {
          smokeDetectorElements.push(deviceElement);
        } else if (typeofThermalDetector(deviceType)) {
          thermalDetectorElements.push(deviceElement);
        } else if (typeofAirSampler(deviceType)) {
          airSamplerElements.push(deviceElement);
        } else if (typeofWaterLeakDetector(deviceType)) {
          waterLeakDetectors.push(deviceElement);
        } else if (typeofPositioningWaterLeakDetector(deviceType)) {
          positioningWaterLeakDetectors.push(deviceElement);
        } else {
          otherDeviceElements.push(deviceElement);
        }
      }
      if (child.custom?.type === 'point-text') {
        pointTextElements.push(child);
      }
    });
    const getPrevValue = generateGetPointValue(prevReltimeData, prevAlarmsData);
    const getValue = generateGetPointValue(devicesRealtimeData, devicesAlarmsData);
    proximityReaderElements.forEach(proximityReaderElement => {
      const { deviceGuid, deviceType } = proximityReaderElement.custom;
      const { statePointsExpressions } = configUtil.getTopologyElementConfig(deviceType, 'FFS');
      const getPointValue = getValue(deviceGuid);
      const openedExpr = replacePointValue(statePointsExpressions.opened, getPointValue);
      const closedExpr = replacePointValue(statePointsExpressions.closed, getPointValue);
      const isOpened = tryEvaluate(openedExpr);
      const isClosed = tryEvaluate(closedExpr);
      const isAlerting = checkAlerting(deviceGuid);
      updateDoorProximityReaderImage(proximityReaderElement, {
        isOpened,
        isClosed,
        isAlerting,
      });
      const isHighlighting = checkHighlighting(deviceGuid);
      updateDeviceAlertingAnimation(proximityReaderElement, { isHighlighting, isAlerting });
    });
    [...smokeDetectorElements, ...thermalDetectorElements, ...airSamplerElements].forEach(
      element => {
        const { deviceGuid } = element.custom;
        const isAlerting = checkAlerting(deviceGuid);
        updateDeviceImage(element, { isAlerting });
        const isHighlighting = checkHighlighting(deviceGuid);
        updateDeviceAlertingAnimation(element, { isHighlighting, isAlerting });
      }
    );
    waterLeakDetectors.forEach(waterLeakDetector => {
      const { deviceGuid } = waterLeakDetector.custom;
      const isAlerting = checkAlerting(deviceGuid);
      waterLeakDetector.set({
        animation: {
          blink: isAlerting,
        },
      });
    });
    positioningWaterLeakDetectors.forEach(positioningWaterLeakDetector => {
      const { deviceGuid } = positioningWaterLeakDetector.custom;

      const isAlerting = checkAlerting(deviceGuid);
      positioningWaterLeakDetector.set({
        animation: {
          blink: isAlerting,
        },
      });

      if (positioningWaterLeakDetector.type !== 'line') {
        logger(
          'Found a PositioningWaterLeakDetector which is not type of a line, the id is %s',
          positioningWaterLeakDetector.id
        );
        return;
      }

      const markerGroupElementId = `${positioningWaterLeakDetector.id}_$$_water-leak-marker_group`;
      const markerGroupElement = store.activePage?.findOne(markerGroupElementId);

      // TODO: @Jerry replace `'1001000'` with the corresponding predefined one
      const { value: leakPosition } = getValue(deviceGuid)('1001000');
      if (leakPosition <= 0) {
        if (markerGroupElement) {
          markerGroupElement.set({
            visible: false,
          });
        }
        return;
      }
      // TODO: @Jerry replace `'1001000'` with the corresponding predefined one
      const { value: length } = getValue(deviceGuid)('1002000');
      const { length: abstractLength, segments } = getLineLength(
        positioningWaterLeakDetector.points
      );
      const abstractLeakPosition = (leakPosition / length) * abstractLength;
      let segmentIdx = -1;
      let leftLength = abstractLeakPosition;
      segments.reduce((sum, segmentLength, idx) => {
        const next = sum + segmentLength;
        if (abstractLeakPosition > sum && abstractLeakPosition <= next) {
          segmentIdx = idx;
        }
        if (segmentIdx <= -1) {
          leftLength -= segmentLength;
        }

        return next;
      }, 0);
      if (segmentIdx <= -1) {
        return;
      }
      const points2d = getPoints2d(positioningWaterLeakDetector.points);
      const refPointStart = points2d[segmentIdx];
      const refPointEnd = points2d[segmentIdx + 1];
      const point = calculatePointOnLine([...refPointStart, ...refPointEnd], leftLength);
      if (!point) {
        return;
      }
      logger('Found leak position @ point: ', point);
      if (markerGroupElement) {
        markerGroupElement.set({
          x: point[0] - markerGroupElement.width / 2,
          y: point[1] - markerGroupElement.height / 2,
          visible: true,
        });
      }
    });
    otherDeviceElements.forEach(element => {
      const { deviceGuid } = element.custom;
      const isAlerting = checkAlerting(deviceGuid);
      const isHighlighting = checkHighlighting(deviceGuid);
      updateDeviceAlertingAnimation(element, { isHighlighting, isAlerting });
    });
    pointTextElements.forEach(pointTextElement => {
      const { point, textTemplate: tpl } = pointTextElement.custom;
      const { isAlerting: prevIsAlerting } = getPrevValue(point.deviceGuid)(point.code);
      const { value, isAlerting } = getValue(point.deviceGuid)(point.code);
      const attrs = { text: '--' };
      if (Number.isNaN(value)) {
        attrs.text = template(tpl)({ value: '--' });
      } else if (point.dataType === 'DI' || point.dataType === 'DO') {
        const mappings = point.validLimits.map(validLimit => validLimit.split('='));
        const t = mappings.find(([val]) => Number(val) === value);
        if (t) {
          attrs.text = template(tpl)({ value: t[1] });
        }
      } else {
        attrs.text = template(tpl)({ value });
      }
      if (prevIsAlerting !== isAlerting) {
        if (isAlerting) {
          if (pointTextElement.fill !== ALERTING_COLOR) {
            attrs.fill = ALERTING_COLOR;
          }
        } else {
          if (pointTextElement.fill !== pointTextElement.custom.__DEFAULT_FILL) {
            attrs.fill = pointTextElement.custom.__DEFAULT_FILL;
          }
        }
      }
      pointTextElement.set(attrs);
    });
  }, [
    prevReltimeData,
    prevAlarmsData,
    devicesRealtimeData,
    devicesAlarmsData,
    store,
    typeofProximityReader,
    typeofBulletCamera,
    typeofSmokeDetector,
    typeofThermalDetector,
    typeofAirSampler,
    configUtil,
    checkHighlighting,
    checkAlerting,
    typeofWaterLeakDetector,
    typeofPositioningWaterLeakDetector,
  ]);

  let preview = null;
  if (store) {
    preview = (
      <>
        <GraphPointLineModal
          visible={pointLineModalVisible}
          pointGuids={pointGuids}
          idc={idc}
          modalTitle={pointGuids[0]?.serieName}
          onVisibleChange={() => setPointLineModalVisible(!pointLineModalVisible)}
        />
        <Preview
          width="100%"
          height="100%"
          store={store}
          tooltipRender={({ currentTarget }, { Container }) => {
            const element = currentTarget.getAttr('element');
            if (!element) {
              return;
            }

            let deviceElement;
            // 设备和设备名称的编组
            const isDeviceGroup = element.custom?.type === 'device_group';
            const isDevice = element.custom?.type === 'device';
            if (isDeviceGroup) {
              deviceElement = element.children.find(el => el.custom?.type === 'device');
            } else if (isDevice) {
              deviceElement = element;
            }

            if (!deviceElement) {
              return;
            }

            if (typeofBulletCamera(deviceElement.custom.deviceType)) {
              return null;
            }

            if (
              deviceElement.custom?.deviceType === upsOutgoingCabinetDeviceType ||
              deviceElement.custom?.deviceType === lvOutgoingCabinetDeviceType
            ) {
              return (
                <Container>
                  <OutletCabinet deviceGuid={deviceElement.id} />
                </Container>
              );
            } else {
              return (
                <Container innerStyle={{ padding: 0 }}>
                  <CorePointsCard
                    deviceGuid={deviceElement.id}
                    deviceType={deviceElement.custom.deviceType}
                    bordered={false}
                  />
                </Container>
              );
            }
          }}
          onElementClick={element => {
            if (element.custom?.type === 'point-text') {
              const { point } = element.custom;
              const pointGuids = [point].map(() => ({
                ...point,
                deviceGuid: point.deviceGuid,
                unit: point.unit,
                pointCode: point.code,
                serieName: point.name,
              }));
              setPointLineModalVisible(true);
              setPointGuids(pointGuids);
            }
            if (!(element?.custom?.type === 'device_group' || element?.custom?.type === 'device')) {
              return;
            }
            let deviceElement = element;
            if (element.custom.type === 'device_group') {
              deviceElement = element.children.find(child => child.custom?.type === 'device');
            }
            const { name, deviceGuid, deviceType } = deviceElement.custom;
            if (typeofBulletCamera(deviceType)) {
              setDevice({
                name,
                guid: deviceGuid,
                type: deviceType,
              });
              open();
              return null;
            }
            window.open(generateSpaceOrDeviceRoutePath({ guid: deviceGuid }));
          }}
        />
      </>
    );
  }

  return (
    <ThemeCompositions theme={document.body.dataset.theme === 'dark' ? 'dark' : 'light'}>
      {hasCard ? (
        <Card
          title={
            <Space size="middle">
              <Typography.Text>
                {room} {roomInfo.name}
              </Typography.Text>
              <CamerasLink idc={idc} block={block} room={room} />
              {relateRooms && <RelatedRooms rooms={relateRooms} />}
            </Space>
          }
          bodyStyle={{ height: 'calc(var(--content-height) - 65px)', overflowY: 'auto' }}
        >
          {preview}
        </Card>
      ) : (
        preview
      )}
    </ThemeCompositions>
  );
}

const mapStateToProps = ({
  roomMonitoring: {
    roomInfo,
    infraGraphPreview: { graph, deviceGuids },
  },
}) => ({
  roomInfo,
  graph,
  deviceGuids,
});

const mapDispatchToProps = {
  subscribe: addRealtimeNAlarmsDataSubscriptionActionCreator,
  unsubscribe: removeRealtimeNAlarmsDataSubscriptionActionCreator,
  resetInfraRoomGraphPreview: roomMonitoringActions.resetInfraRoomGraphPreview,
};

export default connect(mapStateToProps, mapDispatchToProps)(InfraRoomGraphPreview);

const NO_DATA_POINT_VALUE = Number.NaN;

/**
 * 从实时数据中获取某个设备下某个点位的测点值的方法生成器
 *
 * @param {Record<string, object>} realtimeData
 * @param {Record<string, object>} alarmsData
 * @returns
 */
function generateGetPointValue(realtimeData, alarmsData) {
  /**
   * 获取某个设备下某个点位的测点值
   *
   * > `undefined, null` 会被处理成 `Number.NaN`
   *
   * @param {string} deviceGuid
   * @returns {(pointCode: string) => number}
   */
  const getPointValue = deviceGuid => {
    const deviceRealtimeData = get(realtimeData, [deviceGuid, 'pointValueMap']);
    const devicePointsAlarmsCount = get(alarmsData, [deviceGuid, 'pointsCount']);

    return pointCode => {
      let value = get(deviceRealtimeData, [pointCode, 'value'], NO_DATA_POINT_VALUE);
      if (value === undefined || value === null) {
        value = NO_DATA_POINT_VALUE;
      }

      const alarmsCount = get(devicePointsAlarmsCount, pointCode);
      const isAlerting = alarmsCount && (alarmsCount.ERROR > 0 || alarmsCount.WARN > 0);

      return { value, isAlerting };
    };
  };

  return getPointValue;
}

/**
 * 将表达式中的测点转为测点值
 *
 * @param {string} expression `expr-eval` 可解析的表达式
 * @param {(pointCode: string) => number} getValueByPointCode 获取测点实时数据的方法
 * @returns {string}
 */
function replacePointValue(expression, getValueByPointCode) {
  const regex = /\$\{[A-Z]*[0-9]+\}/;
  const m = regex.exec(expression);
  if (m === null) {
    return expression;
  }
  const firstMatch = m[0];
  const [prefix, postfix] = expression.split(firstMatch);
  const pointCode = firstMatch.replace('${', '').replace('}', '');

  // 保证测点值被转换成 `string` 进行字符串拼接
  // [prefix, null, postfix].join() 的方式将会把 `undefined, null, []` 转换成 `""`（空字符串）
  // See [Array#join#description](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/join#description)
  const pointValueString = String(getValueByPointCode(pointCode).value);

  const newExpression = [prefix, pointValueString, postfix].join('');

  return replacePointValue(newExpression, getValueByPointCode);
}

function updateDeviceImage(deviceElement, { isAlerting }) {
  const path = deviceElement.src.substring(0, deviceElement.src.lastIndexOf('/') + 1);
  const extension = deviceElement.src.substring(deviceElement.src.lastIndexOf('.'));
  let filename = 'working';
  if (isAlerting) {
    filename += '-n-alerting';
  }
  const newImgSrc = path + filename + extension;
  deviceElement.set({ src: newImgSrc });
}

function updateDoorProximityReaderImage(deviceElement, { isOpened, isClosed, isAlerting }) {
  const path = deviceElement.src.substring(0, deviceElement.src.lastIndexOf('/') + 1);
  const extension = deviceElement.src.substring(deviceElement.src.lastIndexOf('.'));
  let filename = 'closed';
  if (isOpened) {
    filename = 'opened';
  } else if (isClosed) {
    filename = 'closed';
  }
  if (isAlerting) {
    filename += '-n-alerting';
  }
  const newImgSrc = path + filename + extension;
  deviceElement.set({ src: newImgSrc });
}

function updateDeviceAlertingAnimation(deviceElement, { isHighlighting, isAlerting }) {
  const group = deviceElement.group;
  if (!group) {
    return;
  }
  const highlight = group.findOne(child => child.type === 'highlight');
  const alertingMarker = group.findOne(child => child.type === 'alerting-marker');
  const text = group.findOne(child => child.type === 'text');
  if (isAlerting) {
    if (deviceElement.type === 'image' && deviceElement.animation.blink === false) {
      deviceElement.set({
        animation: {
          blink: true,
        },
      });
    }
    if (highlight && highlight.animating === false) {
      highlight.set({
        animating: true,
        fill: ALERTING_COLOR,
      });
    }
    if (alertingMarker && alertingMarker.animating === false) {
      alertingMarker.set({
        animating: true,
      });
    }
    if (text) {
      if (text.custom === undefined) {
        text.custom = {};
        text.set({ custom: {} });
      }
      const __DEFAULT_FILL = text.stroke;
      text.set({
        custom: {
          ...text.custom,
          __DEFAULT_FILL,
        },
        fill: ALERTING_COLOR,
      });
    }
  } else {
    if (deviceElement.type === 'image' && deviceElement.animation.blink) {
      deviceElement.set({
        animation: {
          blink: false,
        },
      });
    }
    if (highlight) {
      highlight.set({
        animating: isHighlighting,
        fill: highlight.__DEFAULT_COLOR,
      });
    }
    if (alertingMarker && alertingMarker.animating) {
      alertingMarker.set({
        animating: false,
      });
    }
    if (text) {
      if (text.custom.__DEFAULT_FILL !== text.fill) {
        text.set({
          fill: text.custom.__DEFAULT_FILL,
        });
      }
    }
  }
}

function useTypeofFuncs(configUtil) {
  return React.useMemo(() => {
    const typeofProximityReader = configUtil.typeofDeviceGen(
      ConfigUtil.constants.deviceTypes.DOOR_PROXIMITY_READER
    );
    const typeofBulletCamera = configUtil.typeofDeviceGen(
      ConfigUtil.constants.deviceTypes.BULLET_CCTV_CAMERA
    );
    const typeofSmokeDetector = configUtil.typeofDeviceGen(
      ConfigUtil.constants.deviceTypes.SMOKE_DETECTOR
    );
    const typeofThermalDetector = configUtil.typeofDeviceGen(
      ConfigUtil.constants.deviceTypes.THERMAL_DETECTOR
    );
    const typeofAirSampler = configUtil.typeofDeviceGen(
      ConfigUtil.constants.deviceTypes.AIR_SAMPLER
    );
    const typeofWaterLeakDetector = configUtil.typeofDeviceGen(
      ConfigUtil.constants.deviceTypes.WATER_LEAK_DETECTOR
    );
    const typeofPositioningWaterLeakDetector = configUtil.typeofDeviceGen(
      ConfigUtil.constants.deviceTypes.POSITIONING_WATER_LEAK_DETECTOR
    );

    // Synthetics
    const typeofCustomDevice = deviceType =>
      typeofProximityReader(deviceType) ||
      typeofBulletCamera(deviceType) ||
      typeofSmokeDetector(deviceType) ||
      typeofThermalDetector(deviceType) ||
      typeofAirSampler(deviceType);

    return {
      typeofProximityReader,
      typeofBulletCamera,
      typeofSmokeDetector,
      typeofThermalDetector,
      typeofAirSampler,
      typeofWaterLeakDetector,
      typeofPositioningWaterLeakDetector,

      // Synthetics
      typeofCustomDevice,
    };
  }, [configUtil]);
}
