import React from 'react';

import { Statistics } from '@manyun/dc-brain.legacy.components';
import { CLICK_RENDER_TYPE_MAP } from '@manyun/dc-brain.legacy.components/statistics';

import { MODEL_STYLE } from '../constants';
import DurationChart from './chart-modal-button/duration-chart';

const getDataSource = ({
  itLoadRate,
  totalPowerByRoom,
  avgT,
  avgRH,
  powerOnRate,
  totalOfCabinet,
  itLoadRatePointGuid,
  totalPowerByRoomPointGuid,
  avgTPointGuid,
  avgRHPointGuid,
  spaceRoomDeviceType,
}) => [
  [
    {
      key: 'it-load',
      width: 100,
      text: itLoadRate.formattedText,
      description: 'IT 负荷率',
      status: itLoadRate.status,
      clickRender() {
        return {
          type: CLICK_RENDER_TYPE_MAP.MODAL,
          props: {
            title: 'IT 负荷率',
            footer: null,
            style: MODEL_STYLE,
            destroyOnClose: true,
            children: visible => (
              <DurationChart
                visible={visible}
                deviceType={spaceRoomDeviceType}
                pointGuid={itLoadRatePointGuid}
                echartTitle="IT 负荷率"
              />
            ),
          },
        };
      },
    },
    {
      key: 'real-time-electric-power-by-room',
      width: 110,
      text: totalPowerByRoom.formattedText,
      description: '实时功率',
      status: totalPowerByRoom.status,
      clickRender() {
        return {
          type: CLICK_RENDER_TYPE_MAP.MODAL,
          props: {
            title: '包间实时功率',
            footer: null,
            style: MODEL_STYLE,
            destroyOnClose: true,
            children: visible => (
              <DurationChart
                visible={visible}
                deviceType={spaceRoomDeviceType}
                pointGuid={totalPowerByRoomPointGuid}
                echartTitle="包间实时功率"
              />
            ),
          },
        };
      },
    },
    {
      key: 'average-temperature-by-room',
      width: 100,
      text: avgT.formattedText,
      description: '平均温度',
      status: avgT.status,
      clickRender() {
        return {
          type: CLICK_RENDER_TYPE_MAP.MODAL,
          props: {
            title: '平均温度',
            footer: null,
            style: MODEL_STYLE,
            destroyOnClose: true,
            children: visible => (
              <DurationChart
                visible={visible}
                deviceType={spaceRoomDeviceType}
                pointGuid={avgTPointGuid}
                echartTitle="平均温度"
              />
            ),
          },
        };
      },
    },
    {
      key: 'average-relative-humidity-by-room',
      width: 95,
      text: avgRH.formattedText,
      description: '平均湿度',
      status: avgRH.status,
      clickRender() {
        return {
          type: CLICK_RENDER_TYPE_MAP.MODAL,
          props: {
            title: '平均湿度',
            footer: null,
            style: MODEL_STYLE,
            destroyOnClose: true,
            children: visible => (
              <DurationChart
                visible={visible}
                deviceType={spaceRoomDeviceType}
                pointGuid={avgRHPointGuid}
                echartTitle="平均湿度"
              />
            ),
          },
        };
      },
    },
    {
      key: 'power-on-rate',
      width: 95,
      text: powerOnRate.value,
      unit: '%',
      description: '上电率',
    },
    {
      key: 'total-of-cabinet',
      width: 100,
      text: totalOfCabinet.value,
      unit: '个',
      description: '总机柜数',
    },
  ],
];

export function RoomStatistics({ statisticsData }) {
  return <Statistics dataSource={getDataSource(statisticsData)} />;
}

export default RoomStatistics;
