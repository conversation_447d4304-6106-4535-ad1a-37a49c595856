import uniq from 'lodash/uniq';
import React from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Space } from '@manyun/base-ui.ui.space';

export function DesignArchitecture({ data }) {
  return (
    <Space size="large">
      {data?.map(({ title, description, link }) => (
        <Space key={description}>
          <div>{description}</div>
          <div style={{ color: `var(--${prefixCls}-error-color)` }}>
            {Array.isArray(title) ? title.map(t => <div key={t}>{t}</div>) : <div>{title}</div>}
          </div>
          {link && <AuthorizedLink {...link} />}
        </Space>
      ))}
    </Space>
  );
}

function AuthorizedLink({ to, text, authorizationCode }) {
  const [authorized] = useAuthorized({ checkByCode: authorizationCode });

  return authorized && <Link to={to}>{text}</Link>;
}

const mapStateToProps = (
  {
    roomMonitoring: {
      designArchitecture: { electricity },
      infrastructureData: {
        entities: { roomGrids },
        result: { refStructure, roomGrids: roomGridIdxes },
      },
    },
  },
  { idc, block }
) => {
  const powerModels = [];
  // eslint-disable-next-line no-unused-expressions
  roomGridIdxes?.forEach(roomGridIdx => {
    powerModels.push(...roomGrids[roomGridIdx].powerModels);
  });
  const uniqPowerModels = uniq(powerModels);
  const electricStructure = uniqPowerModels.length ? uniqPowerModels : electricity;

  const data = [];
  if (electricStructure) {
    data.push({
      title: electricStructure,
      description: '电力架构',
    });
  }

  if (refStructure) {
    data.push({
      title: refStructure,
      description: '制冷架构',
    });
  }

  return { data };
};

export default connect(mapStateToProps)(DesignArchitecture);
