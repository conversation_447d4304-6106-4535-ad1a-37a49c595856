import React from 'react';

import { Bar } from '@manyun/base-ui.chart.bar';

import getTextsWidth from '@manyun/dc-brain.legacy.components/echart/utils/getTextsWidth';
import { toPercentText } from '@manyun/dc-brain.legacy.utils';

function RoomCustomersBarChart({ data }) {
  const serieData = data.reverse().map(({ impactFactor, customer }) => [impactFactor, customer]);

  const containerHeight = 70 * serieData.length + 48;
  const startTextsWidth = getTextsWidth(serieData.map(([_, name]) => name.slice(0, 10)));
  const textsWidth = getTextsWidth(serieData.map(([_, name]) => name));
  const gridLeft = Math.max(...startTextsWidth);

  const yAxis = {
    type: 'category',
    verticalAlign: 'middle',
    axisLabel: {
      formatter: function (value, index) {
        const text = String(value);

        if (textsWidth[index] <= gridLeft) {
          return text;
        }

        const len = text.length;
        const txts = [];
        let startIdx = 0;
        let endIdx = 1;
        while (endIdx <= len) {
          const partial = text.slice(startIdx, endIdx);
          const [width] = getTextsWidth([partial]);
          if (width > gridLeft) {
            txts.push(text.slice(startIdx, endIdx - 1));
            startIdx = endIdx - 1;
          }
          if (endIdx === len) {
            txts.push(text.slice(startIdx));
          }
          endIdx++;
        }

        return txts.join('\n');
      },
    },
  };

  return (
    <Bar
      style={{ width: 600, height: containerHeight }}
      option={{
        grid: { top: 40, right: 8, bottom: 8, left: gridLeft + 8 },
        tooltip: tooltip,
        xAxis: xAxis,
        yAxis: yAxis,
        series: { ...series, data: serieData },
      }}
    />
  );
}

export default RoomCustomersBarChart;

const xAxis = {
  type: 'value',
};

const label = {
  show: true,
  position: 'insideRight',
  formatter: params => {
    if (!params || !params.data) {
      return;
    }
    const {
      data: [value],
    } = params;

    return toPercentText(value, 2);
  },
};

const series = [
  {
    type: 'bar',
    barWidth: 32,
    label,
  },
];

const tooltip = {
  trigger: 'none',
  formatter(params) {
    if (!params) {
      return;
    }
    const {
      data: [value, name],
    } = params[0];

    return `${name}: ${toPercentText(value, 2)}`;
  },
};
