import React from 'react';
import { useParams } from 'react-router-dom';

import { PointsLine } from '@manyun/monitoring.chart.points-line';

import { getSpaceGuid } from '@manyun/dc-brain.legacy.utils';

export default function DurationChart({ visible, deviceType, pointGuid, echartTitle }) {
  const { idc, block, room } = useParams();

  const { deviceGuid, pointCode } = pointGuid;

  const series = [
    {
      name: echartTitle,
      type: 'line',
      smooth: true, // 平滑曲线
      showSymbol: false, // 隐藏曲线上的圆点
      markLine: {
        symbol: 'none',
        label: {
          show: true,
          position: 'start',
          formatter: '{b}({c})',
        },
      },
    },
  ];

  return (
    <PointsLine
      idcTag={idc}
      pointGuids={[
        {
          deviceGuid,
          pointCode: pointCode[0],
          deviceType,
          spaceGuid: getSpaceGuid(idc, block, room),
        },
      ]}
      allowInterval={visible}
      echartStyle={{ width: '100%', height: 400 }}
      seriesOption={series}
      showExport
      showSetting
    />
  );
}
