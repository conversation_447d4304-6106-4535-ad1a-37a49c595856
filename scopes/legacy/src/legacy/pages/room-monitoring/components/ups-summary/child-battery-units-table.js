import get from 'lodash/get';
import React from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { generateDeviceRecordRoutePath } from '@manyun/resource-hub.route.resource-routes';

import { StatusText } from '@manyun/dc-brain.legacy.components';
import { STATUS_MAP } from '@manyun/dc-brain.legacy.constants/statusColor';
import {
  generateGetDeviceAlarmStatus,
  generateGetDeviceMonitoringData,
} from '@manyun/dc-brain.legacy.utils/device';

import { StyledTable } from './styled';

export function ChildBatteryUnitsTable({ currentConfigMap, data, rawData, alarmData }) {
  const { pointsDefinitionMap, concretePointCodeMappings } = currentConfigMap;
  const getRawData = generateGetDeviceMonitoringData(
    rawData,
    alarmData,
    pointsDefinitionMap,
    concretePointCodeMappings
  );
  const getAlarmStatus = generateGetDeviceAlarmStatus(alarmData);

  return (
    <StyledTable
      bordered
      rowKey="guid"
      columns={[
        {
          title: '电池组名称',
          dataIndex: 'name',
          render(name, { guid }) {
            return <Link to={generateDeviceRecordRoutePath({ guid })}>{name}</Link>;
          },
          onHeaderCell: getCellProps,
          onCell: getCellProps,
        },
        {
          title: '电池组状态',
          key: '__status',
          render(__, { guid, deviceType }) {
            const { value } = getRawData(
              { deviceGuid: guid, deviceType },
              {
                pointType: ConfigUtil.constants.pointCodes.BATTERY_UNIT_WORKING_MODE,
                reflected: true,
              }
            );

            return (
              <StatusText background status={value.STATUS_CODE}>
                {value.NAME}
              </StatusText>
            );
          },
        },
        {
          title: '电池组电压',
          key: '__voltage',
          render(__, { guid, deviceType }) {
            return getRawData(
              { deviceGuid: guid, deviceType },
              { pointType: ConfigUtil.constants.pointCodes.BATTERY_UNIT_VOLTAGE, formatted: true }
            ).formattedText;
          },
        },
        {
          title: '告警状态',
          key: '__alarm-status',
          render(__, { guid }) {
            const alarmStatus = getAlarmStatus(guid);
            if (alarmStatus === STATUS_MAP.ALARM) {
              return (
                <StatusText background status="alarm">
                  告警
                </StatusText>
              );
            }
            if (alarmStatus === STATUS_MAP.WARNING) {
              return (
                <StatusText background status="warning">
                  预警
                </StatusText>
              );
            }
            return (
              <StatusText background status="normal">
                正常
              </StatusText>
            );
          },
        },
      ]}
      pagination={false}
      dataSource={data}
    />
  );
}

const mapStateToProps = (
  {
    config: { configMap },
    'monitoring.subscriptions': { devicesRealtimeData, devicesAlarmsData },
    roomMonitoring: { childBatteryUnitsMap },
  },
  { ups }
) => {
  if (!ups) {
    return { currentConfigMap: configMap.current, data: [] };
  }

  const childBatteryUnits = get(childBatteryUnitsMap, ups.deviceGuid, []);

  return {
    currentConfigMap: configMap.current,
    data: childBatteryUnits,
    rawData: devicesRealtimeData,
    alarmData: devicesAlarmsData,
  };
};

export default connect(mapStateToProps)(ChildBatteryUnitsTable);

function getCellProps() {
  return { style: { padding: '16px 14px' } };
}
