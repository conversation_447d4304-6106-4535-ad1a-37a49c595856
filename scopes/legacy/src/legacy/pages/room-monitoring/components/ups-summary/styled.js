import styled from 'styled-components';

import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Table } from '@manyun/base-ui.ui.table';

export const StyledTable = styled(Table)`
  .manyun-table {
    font-size: 12px;

    table {
      border-radius: 0;
    }
  }

  .manyun-table-thead > tr > th {
    background: var(--background-descriptions-label);
  }

  .manyun-table-thead > tr:first-child > th:last-child {
    border-top-right-radius: 0;
  }

  .manyun-table-bordered .manyun-table-header > table,
  .manyun-table-bordered .manyun-table-body > table,
  .manyun-table-bordered .manyun-table-fixed-left table,
  .manyun-table-bordered .manyun-table-fixed-right table {
    border-top-width: 0;
  }
`;

export const StyledDescriptions = styled(Descriptions)`
  .manyun-descriptions-view {
    border-radius: 0;
  }
`;
