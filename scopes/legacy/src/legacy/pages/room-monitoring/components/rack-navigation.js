import React from 'react';

import styled from 'styled-components';

import { Tabs } from '@manyun/base-ui.ui.tabs';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';

export function RackNavigation({ className, dataSource = ['A', 'B', 'C', 'D'] }) {
  return (
    <Tabs className={className}>
      {dataSource.map(rackIdx => (
        <Tabs.TabPane key={rackIdx} tab={rackIdx}></Tabs.TabPane>
      ))}
    </Tabs>
  );
}

const StyledRackNavigation = styled(RackNavigation)`
  .manyun-tabs-bar {
    // border: 0;
    margin: 0;
  }

  .manyun-tabs-nav {
    float: none;
  }

  .manyun-tabs-nav .manyun-tabs-tab {
    margin: 0 4px 0 0;
    padding: 4px 6px;
  }
`;

function LabeledRackNavigation() {
  return (
    <GutterWrapper flex alignItems="center" size=".5rem" padding="4px 0">
      <div>机列</div>
      <StyledRackNavigation />
    </GutterWrapper>
  );
}

export default LabeledRackNavigation;
