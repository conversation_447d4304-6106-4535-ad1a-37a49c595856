import React from 'react';
import { connect } from 'react-redux';
import { useParams } from 'react-router-dom';

import {
  G<PERSON>Wrapper,
  ModalButton,
  TinyCard,
  TinyTable,
} from '@manyun/dc-brain.legacy.components';
import { getRoomCustomersActionCreator } from '@manyun/dc-brain.legacy.redux/actions/roomMonitoringActions';
import { toPercentText } from '@manyun/dc-brain.legacy.utils';

import RoomCustomersChart from './room-customers-chart';

function RoomCustomersModalButton({ data = [], total = 0, getRoomCustomers }) {
  const { idc, block, room } = useParams();

  function handleVisibleChanged(visible) {
    if (visible) {
      getRoomCustomers({ idc, block, room, pageNo: 1, pageSize: 10 });
    }
  }

  return (
    <ModalButton
      autoHeight
      compact
      text="包间客户类别"
      type="link"
      title="包间客户类别"
      footer={null}
      width={600 + 500 + 48 /* Card body 有 24px 的 padding */}
      onVisibleChanged={handleVisibleChanged}
    >
      <GutterWrapper flex>
        <TinyCard
          style={{ marginTop: -12 }}
          headStyle={{ borderWidth: 0 }}
          bodyStyle={{ overflow: 'hidden', padding: 0 }}
          title="Top10客户占比"
        >
          <RoomCustomersChart data={data.slice(0, 10)} />
        </TinyCard>
        <TinyTable
          size="small"
          rowKey="customer"
          columns={columns}
          dataSource={data}
          pagination={{
            total,
            onChange: (page, pageSize) => {
              getRoomCustomers({ idc, block, room, pageNo: page, pageSize });
            },
          }}
        />
      </GutterWrapper>
    </ModalButton>
  );
}

const mapStateToProps = ({ roomMonitoring: { roomCustomers } }) => roomCustomers;
const mapDispatchToProps = { getRoomCustomers: getRoomCustomersActionCreator };

export default connect(mapStateToProps, mapDispatchToProps)(RoomCustomersModalButton);

const columns = [
  {
    title: '客户名称',
    dataIndex: 'customer',
  },
  {
    title: '本包间影响指数',
    dataIndex: 'impactFactor',
    render(impactFactor) {
      return toPercentText(impactFactor, 2);
    },
  },
  {
    title: '本楼机柜总数',
    dataIndex: 'blockGridCount',
  },
  {
    title: '本包间机柜数',
    dataIndex: 'roomGridCount',
  },
];
