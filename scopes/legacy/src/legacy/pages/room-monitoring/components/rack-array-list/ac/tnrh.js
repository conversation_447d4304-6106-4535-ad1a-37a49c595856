import { useParams } from 'react-router-dom';

import { PointsLine } from '@manyun/monitoring.chart.points-line';

import { getSpaceGuid } from '@manyun/dc-brain.legacy.utils';

export default function Tnrh({ visible, deviceType, deviceGuid, pointCodes }) {
  const { idc, block, room } = useParams();
  const pointGuids = pointCodes.map(pointCode => ({
    pointCode,
    deviceGuid,
    deviceType,
    spaceGuid: getSpaceGuid(idc, block, room),
  }));

  return (
    <PointsLine
      pointGuids={pointGuids}
      echartStyle={{ width: '100%', height: 400 }}
      allowInterval={visible}
      basicOption={{
        grid: { top: 70, right: 70, left: 70 },
        yAxis: generateYAxises(),
      }}
      seriesOption={series}
      idcTag={idc}
    />
  );
}

const generateYAxises = () => [generateAxis('温度（℃）'), generateAxis('湿度（%）')];

function generateAxis(name) {
  return {
    name,
    type: 'value',
    min: function (value) {
      return Math.ceil(value.min) - 1;
    },
    max: function (value) {
      return Math.floor(value.max) + 1;
    },
  };
}

const series = [
  {
    name: '温度',
    type: 'line',
    smooth: true, // 平滑曲线
    showSymbol: false, // 隐藏曲线上的圆点
    markLine: {
      symbol: 'none',
      label: {
        show: true,
        position: 'start',
        formatter: '{b}({c})',
      },
    },
  },
  {
    yAxisIndex: 1, // 指定此系列数据使用第二个 Y 轴
    name: '湿度',
    type: 'line',
    smooth: true,
    showSymbol: false,
    markLine: {
      symbol: 'none',
      label: {
        show: true,
        position: 'end',
        formatter: '{b}({c})',
      },
    },
  },
];
