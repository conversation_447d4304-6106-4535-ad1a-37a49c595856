import React from 'react';

import { AirConditionerOutlined } from '@manyun/base-ui.icons/dist/outlined/air-conditioner';
import { Popover } from '@manyun/base-ui.ui.popover';

import { Blink } from '@manyun/monitoring.page.room-view/dist/components/blink';
import { CorePointsCard } from '@manyun/resource-hub.ui.core-points-card';

const SummaryModalButton = React.forwardRef(
  ({ style, deviceGuid, deviceType, isAlerting = false }, ref) => {
    return (
      <Popover
        content={
          <CorePointsCard
            style={{ margin: '-12px -16px' }}
            deviceGuid={deviceGuid}
            deviceType={deviceType}
            bordered={false}
            hideNoDataItems
          />
        }
      >
        <div ref={ref} style={style}>
          <Blink blinking={isAlerting}>
            <AirConditionerOutlined style={{ fontSize: 24 }} />
          </Blink>
        </div>
      </Popover>
    );
  }
);

SummaryModalButton.displayName = 'SummaryModalButton';

export default SummaryModalButton;
