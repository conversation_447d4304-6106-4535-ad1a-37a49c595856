import React from 'react';
import { connect } from 'react-redux';
import { Link, useParams } from 'react-router-dom';

import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { PointsLineModalButton } from '@manyun/monitoring.chart.points-line';
import { PointsStateLine } from '@manyun/monitoring.chart.points-state-line';
import { generateDeviceRecordRoutePath } from '@manyun/resource-hub.route.resource-routes';

import { DebugPointValue, ModalButton } from '@manyun/dc-brain.legacy.components';
import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';
import { generateGetDeviceMonitoringData } from '@manyun/dc-brain.legacy.utils/device';

export function Summary({
  rawData: {
    onOffStatus,
    fanRpm,
    runningStatus,
    maxDeliveryT,
    maxReturnT,
    deliveryAndReturnTDiff,
    inputWaterT,
    waterSwitchOnOffPercent,
  },
  aggData: { waterChillerOutputT, waterChillerInputT },
  pByColumn,
  roomGrid,
  deviceGuid,
  deviceName,
  deviceType,
  room,
}) {
  const { idc, block } = useParams();

  return (
    <Descriptions colon={false}>
      <Descriptions.Item label="系统运行状态">
        <DebugPointValue
          direction="vertical"
          {...onOffStatus}
          nonDebugRender={() =>
            onOffStatus.disabled ? (
              BLANK_PLACEHOLDER
            ) : (
              <ModalButton
                compact
                autoHeight
                type="link"
                text={onOffStatus.value.NAME}
                title={`${deviceName}.系统运行状态`}
                modalStyle={{ minWidth: (1200 / 1600) * 100 + '%', maxWidth: 1200 }}
                footer={null}
              >
                {visible => (
                  <PointsStateLine
                    idcTag={idc}
                    allowInterval={visible}
                    pointGuids={[{ ...onOffStatus, deviceGuid }]}
                    seriesOption={[{ name: onOffStatus.name }]}
                    chartFunction="MAX"
                    validLimitsMap={onOffStatus.valueMapping || {}}
                  />
                )}
              </ModalButton>
            )
          }
        />
      </Descriptions.Item>
      <Descriptions.Item label="空调风机转速百分比">
        <DebugPointValue
          direction="vertical"
          {...fanRpm}
          nonDebugRender={() => (
            <PointsLineModalButton
              idcTag={idc}
              btnText={fanRpm.formattedText}
              modalText={`${deviceName}.空调风机转速百分比`}
              pointGuids={[
                {
                  serieName: '空调风机转速百分比',
                  deviceGuid,
                  pointCode: fanRpm.pointCode,
                  deviceType: fanRpm.deviceType,
                },
              ]}
            />
          )}
        />
      </Descriptions.Item>
      <Descriptions.Item label="风机运行状态">
        <DebugPointValue
          direction="vertical"
          {...runningStatus}
          nonDebugRender={() => (
            <PointsLineModalButton
              idcTag={idc}
              btnText={runningStatus.value.NAME}
              modalText={`${deviceName}.风机运行状态`}
              pointGuids={[
                {
                  serieName: '风机运行状态',
                  deviceGuid,
                  pointCode: runningStatus.pointCode,
                  deviceType: runningStatus.deviceType,
                },
              ]}
            />
          )}
        />
      </Descriptions.Item>
      <Descriptions.Item label="送风温度">
        <DebugPointValue
          direction="vertical"
          {...maxDeliveryT}
          nonDebugRender={() => (
            <PointsLineModalButton
              idcTag={idc}
              btnText={maxDeliveryT.formattedText}
              modalText={`${deviceName}.送风温度`}
              pointGuids={[
                {
                  serieName: '送风温度',
                  deviceGuid,
                  pointCode: maxDeliveryT.pointCode,
                  deviceType: maxDeliveryT.deviceType,
                },
              ]}
            />
          )}
        />
      </Descriptions.Item>
      <Descriptions.Item label="回风温度">
        <DebugPointValue
          direction="vertical"
          {...maxReturnT}
          nonDebugRender={() => (
            <PointsLineModalButton
              idcTag={idc}
              btnText={maxReturnT.formattedText}
              modalText={`${deviceName}.回风温度`}
              pointGuids={[
                {
                  serieName: '回风温度',
                  deviceGuid,
                  pointCode: maxReturnT.pointCode,
                  deviceType: maxReturnT.deviceType,
                },
              ]}
            />
          )}
        />
      </Descriptions.Item>
      <Descriptions.Item label="送回风温差">
        <DebugPointValue
          direction="vertical"
          {...deliveryAndReturnTDiff}
          nonDebugRender={() => (
            <PointsLineModalButton
              idcTag={idc}
              btnText={deliveryAndReturnTDiff.formattedText}
              modalText={`${deviceName}.送回风温差`}
              pointGuids={[
                {
                  serieName: '送回风温差',
                  deviceGuid,
                  pointCode: deliveryAndReturnTDiff.pointCode,
                  deviceType: deliveryAndReturnTDiff.deviceType,
                },
              ]}
            />
          )}
        />
      </Descriptions.Item>
      {/* <Descriptions.Item label="额定制冷量">需求不明确</Descriptions.Item>
      <Descriptions.Item label="实时制冷量">需求不明确</Descriptions.Item>
      <Descriptions.Item label="制冷量利用率">需求不明确</Descriptions.Item> */}
      <Descriptions.Item label="水路进水温度">
        <DebugPointValue
          direction="vertical"
          {...inputWaterT}
          nonDebugRender={() => (
            <PointsLineModalButton
              idcTag={idc}
              btnText={inputWaterT.formattedText}
              modalText={`${deviceName}.水路进水温度`}
              pointGuids={[
                {
                  serieName: '水路进水温度',
                  deviceGuid,
                  pointCode: inputWaterT.pointCode,
                  deviceType: inputWaterT.deviceType,
                },
              ]}
            />
          )}
        />
      </Descriptions.Item>
      <Descriptions.Item label="冷冻水供水温度">
        <DebugPointValue
          direction="vertical"
          {...waterChillerOutputT}
          nonDebugRender={() => (
            <PointsLineModalButton
              idcTag={idc}
              btnText={waterChillerOutputT.formattedText}
              modalText={`${deviceName}.冷冻水供水温度`}
              pointGuids={[
                {
                  serieName: '冷冻水供水温度',
                  deviceGuid,
                  pointCode: waterChillerOutputT.pointCode,
                  deviceType: waterChillerOutputT.deviceType,
                },
              ]}
            />
          )}
        />
      </Descriptions.Item>
      <Descriptions.Item label="冷冻水回水温度">
        <DebugPointValue
          direction="vertical"
          {...waterChillerInputT}
          nonDebugRender={() => (
            <PointsLineModalButton
              idcTag={idc}
              btnText={waterChillerInputT.formattedText}
              modalText={`${deviceName}.冷冻水回水温度`}
              pointGuids={[
                {
                  serieName: '冷冻水回水温度',
                  deviceGuid,
                  pointCode: waterChillerInputT.pointCode,
                  deviceType: waterChillerInputT.deviceType,
                },
              ]}
            />
          )}
        />
      </Descriptions.Item>
      <Descriptions.Item label="水阀开度百分比反馈">
        <DebugPointValue
          direction="vertical"
          {...waterSwitchOnOffPercent}
          nonDebugRender={() => (
            <PointsLineModalButton
              idcTag={idc}
              btnText={waterSwitchOnOffPercent.formattedText}
              modalText={`${deviceName}.水阀开度百分比反馈`}
              pointGuids={[
                {
                  serieName: '水阀开度百分比反馈',
                  deviceGuid,
                  pointCode: waterSwitchOnOffPercent.pointCode,
                  deviceType: waterSwitchOnOffPercent.deviceType,
                },
              ]}
            />
          )}
        />
      </Descriptions.Item>
      <Descriptions.Item label="对应机列功耗">
        <DebugPointValue
          direction="vertical"
          {...pByColumn}
          nonDebugRender={() => (
            <PointsLineModalButton
              idcTag={idc}
              btnText={`${roomGrid}列${pByColumn.formattedText}`}
              modalText={`${deviceName}.对应机列功耗`}
              pointGuids={[
                {
                  serieName: `${roomGrid}机列功耗`,
                  deviceGuid: pByColumn.deviceGuid,
                  pointCode: pByColumn.pointCode,
                  deviceType: pByColumn.deviceType,
                },
              ]}
            />
          )}
        />
      </Descriptions.Item>
      {/* <Descriptions.Item label="COP">需求不明确</Descriptions.Item> */}
      <Descriptions.Item />
      <Descriptions.Item
        label={<Link to={generateDeviceRecordRoutePath({ guid: deviceGuid })}>详细参数</Link>}
      />
      <Descriptions.Item label="电力拓扑" />
      <Descriptions.Item label="暖通拓扑" />
      <Descriptions.Item label={<Link to="/">历史告警</Link>} />
      {/* <Descriptions.Item label={<Link to="/">事件记录（缺少页面链接）</Link>} />
      <Descriptions.Item label={<Link to="/">维保记录（缺少页面链接）</Link>} /> */}
    </Descriptions>
  );
}

const mapStateToProps = (
  { 'monitoring.subscriptions': { devicesRealtimeData, devicesAlarmsData }, config: { configMap } },
  { spaceGuids, spaceGuid, deviceGuid, deviceType, roomGrid }
) => {
  const blockGuid = spaceGuids.slice(0, 2).join('.');
  const configUtil = new ConfigUtil(configMap.current, { defaultSpaceGuid: blockGuid });
  const { pointsDefinitionMap } = configMap.current;
  const spaceBlockDeviceType = configUtil.getOneDeviceType(
    ConfigUtil.constants.deviceTypes.SPACE_BLOCK
  );
  const spaceColumnDeviceType = configUtil.getOneDeviceType(
    ConfigUtil.constants.deviceTypes.SPACE_COLUMN
  );
  const device = { deviceGuid, deviceType };
  const getData = generateGetDeviceMonitoringData(
    devicesRealtimeData,
    devicesAlarmsData,
    pointsDefinitionMap
  );

  const rawData = {
    onOffStatus: getData(device, {
      hardCodedPointCode: configUtil.getPointCode(
        deviceType,
        ConfigUtil.constants.pointCodes.CRAC_ON_OFF_STATE
      ),
      reflected: true,
      valueMappingIncluded: true,
      defaults: {
        value: {
          NAME: BLANK_PLACEHOLDER,
        },
      },
    }),
    fanRpm: getData(device, {
      formatted: true,
      hardCodedPointCode: configUtil.getPointCode(
        deviceType,
        ConfigUtil.constants.pointCodes.CRAC_FAN_RPM
      ),
    }),
    runningStatus: getData(device, {
      reflected: true,
      hardCodedPointCode: configUtil.getPointCode(
        deviceType,
        ConfigUtil.constants.pointCodes.CRAC_RUNNING_STATE
      ),
    }),
    maxDeliveryT: getData(device, {
      formatted: true,
      hardCodedPointCode: configUtil.getPointCode(
        deviceType,
        ConfigUtil.constants.pointCodes.CRAC_T_OF_AIR_SUPPLY
      ),
    }),
    maxReturnT: getData(device, {
      formatted: true,
      hardCodedPointCode: configUtil.getPointCode(
        deviceType,
        ConfigUtil.constants.pointCodes.CRAC_T_OF_RETURN_AIR
      ),
    }),
    deliveryAndReturnTDiff: getData(device, {
      formatted: true,
      hardCodedPointCode: configUtil.getPointCode(
        deviceType,
        ConfigUtil.constants.pointCodes.CRAC_DIFF_T_OF_SNR_AIR
      ),
    }),
    inputWaterT: getData(device, {
      formatted: true,
      hardCodedPointCode: configUtil.getPointCode(
        deviceType,
        ConfigUtil.constants.pointCodes.CRAC_T_OF_INCOMING_WATER
      ),
    }),
    waterSwitchOnOffPercent: getData(device, {
      formatted: true,
      hardCodedPointCode: configUtil.getPointCode(
        deviceType,
        ConfigUtil.constants.pointCodes.CRAC_OPENING_PERCENTAGE_OF_WATER_VALVE
      ),
    }),
  };

  const spaceBlockDevice = {
    deviceGuid: blockGuid,
    deviceType: spaceBlockDeviceType,
  };
  const aggData = {
    waterChillerOutputT: getData(spaceBlockDevice, {
      formatted: true,
      hardCodedPointCode: configUtil.getPointCode(
        spaceBlockDeviceType,
        ConfigUtil.constants.pointCodes.PIPE_T_OF_CHILLED_WATER_SUPPLY_MAX
      ),
    }),
    waterChillerInputT: getData(spaceBlockDevice, {
      formatted: true,
      hardCodedPointCode: configUtil.getPointCode(
        spaceBlockDeviceType,
        ConfigUtil.constants.pointCodes.PIPE_T_OF_RETURN_CHILLED_WATER_MAX
      ),
    }),
  };

  const spaceColumnDevice = {
    deviceGuid: `${spaceGuid}.${roomGrid}`,
    deviceType: spaceColumnDeviceType,
  };
  const pByColumn = getData(spaceColumnDevice, {
    formatted: true,
    hardCodedPointCode: configUtil.getPointCode(
      spaceColumnDeviceType,
      ConfigUtil.constants.pointCodes.PDU_POWER_SUM
    ),
  });

  return { rawData, aggData, pByColumn };
};

export default connect(mapStateToProps)(Summary);
