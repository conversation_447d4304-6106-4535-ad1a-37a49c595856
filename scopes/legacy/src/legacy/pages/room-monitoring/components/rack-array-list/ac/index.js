import React from 'react';
import { useSelector } from 'react-redux';

import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { BlinkWarningIcon } from '@manyun/monitoring.page.room-view/dist/components/blink';
import { getMonitoringData } from '@manyun/monitoring.state.subscriptions';
import {
  generateGetDeviceAlarmStatus,
  generateGetPointMonitoringData,
} from '@manyun/monitoring.util.get-monitoring-data';

import { StatusText } from '@manyun/dc-brain.legacy.components';
import { useElementSpotlight } from '@manyun/dc-brain.legacy.pages/room-monitoring/context/spotlight';
import { useSpotlightTarget } from '@manyun/dc-brain.legacy.pages/room-monitoring/hook/use-spotlight-target';

import SummaryModalButton from './summary-modal-button';
import TnrhModalButton from './tnrh-modal-button';

function AirConditioner({ spaceGuids, deviceGuid, deviceType, deviceName, roomGrid }) {
  const spotlightTarget = useSpotlightTarget();
  const [ref] = useElementSpotlight({ focus: deviceGuid === spotlightTarget });

  const config = useSelector(selectCurrentConfig);
  const configUtil = React.useMemo(() => new ConfigUtil(config), [config]);
  const { devicesRealtimeData, devicesAlarmsData } = useSelector(getMonitoringData);
  const device = { deviceGuid, deviceType };
  const getDeviceAlarmStatus = React.useMemo(
    () => generateGetDeviceAlarmStatus(devicesAlarmsData),
    [devicesAlarmsData]
  );
  const { isAlerting } = React.useMemo(
    () => getDeviceAlarmStatus(deviceGuid),
    [getDeviceAlarmStatus, deviceGuid]
  );
  const getPointData = generateGetPointMonitoringData({
    realtimeData: devicesRealtimeData,
    alarmsData: devicesAlarmsData,
    configUtil,
  });
  const onOffStatus = getPointData(device, {
    pointType: ConfigUtil.constants.pointCodes.CRAC_ON_OFF_STATE,
    reflected: true,
    defaults: {
      value: {
        NAME: '--',
      },
    },
  });

  return (
    <>
      <div
        ref={ref}
        style={{ position: 'absolute', visibility: 'hidden', width: 72, height: 49 }}
      />
      <Space style={{ width: '100%' }} size={0} direction="vertical">
        <Typography.Text type={isAlerting ? 'danger' : undefined}>
          {deviceName}
          {isAlerting && <BlinkWarningIcon />}
        </Typography.Text>
        <Space>
          <SummaryModalButton
            spaceGuids={spaceGuids}
            deviceGuid={deviceGuid}
            deviceType={deviceType}
            deviceName={deviceName}
            roomGrid={roomGrid}
            isAlerting={isAlerting}
          />
          {onOffStatus.originalValue === 0 ? (
            <StatusText style={{ fontSize: 12 }} background status="alarm">
              关闭
            </StatusText>
          ) : (
            <TnrhModalButton
              spaceGuids={spaceGuids}
              deviceGuid={deviceGuid}
              deviceType={deviceType}
              deviceName={deviceName}
            />
          )}
        </Space>
      </Space>
    </>
  );
}

export default AirConditioner;
