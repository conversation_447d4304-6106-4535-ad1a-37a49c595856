import React from 'react';
import { connect } from 'react-redux';

import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { PointsLineModalButton } from '@manyun/monitoring.chart.points-line';

import { DebugPointValue, StatusText } from '@manyun/dc-brain.legacy.components';
import { getSpaceGuidMap } from '@manyun/dc-brain.legacy.utils';
import { generateGetDeviceMonitoringData } from '@manyun/dc-brain.legacy.utils/device';

function PduPowerSum({ p, spaceGuid }) {
  return (
    <PointsLineModalButton
      idcTag={getSpaceGuidMap(spaceGuid).idc}
      btnText={
        <DebugPointValue
          direction="vertical"
          {...p}
          nonDebugRender={() => <StatusText status={p.status}>{p.value}</StatusText>}
        />
      }
      modalText="机柜总功率"
      pointGuids={p.pointGuids}
      seriesOption={p.seriesOption}
    />
  );
}

const mapStateToProps = (
  { 'monitoring.subscriptions': { devicesRealtimeData, devicesAlarmsData }, config: { configMap } },
  { spaceGuid, gridTag }
) => {
  const configUtil = new ConfigUtil(configMap.current);
  const spaceGridDeviceType = configUtil.getOneDeviceType(
    ConfigUtil.constants.deviceTypes.SPACE_GRID
  );
  const spaceGridDevice = {
    deviceGuid: `${spaceGuid}.${gridTag}`,
    deviceType: spaceGridDeviceType,
  };
  const getData = generateGetDeviceMonitoringData(
    devicesRealtimeData,
    devicesAlarmsData,
    configMap.current.pointsDefinitionMap
  );

  const p = getData(spaceGridDevice, {
    hardCodedPointCode: configUtil.getPointCode(
      spaceGridDeviceType,
      ConfigUtil.constants.pointCodes.PDU_POWER_SUM
    ),
  });
  p.pointGuids = [
    {
      deviceGuid: spaceGridDevice.deviceGuid,
      pointCode: p.pointCode,
      unit: p.unit,
    },
  ];
  p.seriesOption = [{ name: `${gridTag} 机列总功率` }];

  return { p, spaceGuid };
};

export default connect(mapStateToProps)(PduPowerSum);
