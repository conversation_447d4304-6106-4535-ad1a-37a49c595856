import React from 'react';
import { connect } from 'react-redux';
import { Link, useParams } from 'react-router-dom';

import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { PointsLineModalButton } from '@manyun/monitoring.chart.points-line';
import { PointsStateLineModalButton } from '@manyun/monitoring.chart.points-state-line';
import { generateDeviceRecordRoutePath } from '@manyun/resource-hub.route.resource-routes';

import { GutterWrapper, ModalButton, StatusText } from '@manyun/dc-brain.legacy.components';
import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';
import { generateGetDeviceMonitoringData } from '@manyun/dc-brain.legacy.utils/device';

import UpsOrHvdc from './ups-or-hvdc';

function RPP({
  // 机列编号
  arrayCabinetIdx: columnNumber,
  // A 路数据
  dataA,
  rppAPointGuidsMap,
  // B 路数据
  dataB,
  rppBPointGuidsMap,
  // 机列总功率
  acSmartPduPowerSum,
  // 机列平均 IT 负荷率
  acSmartPduLoadRateAvg,
  // A 路列头柜
  rppAs,
  // B 路列头柜
  rppBs,
}) {
  const { idc, block } = useParams();

  return (
    <ModalButton
      className={`rpp-${columnNumber}`}
      type="link"
      compact
      autoHeight
      footer={null}
      text={columnNumber}
      title={
        <GutterWrapper>
          <span>{columnNumber} 机列</span>
          <UpsOrHvdc idc={idc} block={block} rppAs={rppAs} rppBs={rppBs} />
        </GutterWrapper>
      }
      width={600}
      data-column-tag={columnNumber}
    >
      <GutterWrapper mode="vertical">
        <Descriptions
          style={{ textAlign: 'center' }}
          title="机列信息"
          size="small"
          bordered
          column={2}
        >
          <Descriptions.Item label="机列总功率">
            <PointsLineModalButton
              idcTag={idc}
              btnText={acSmartPduPowerSum.formattedText}
              modalText="机列总功率"
              pointGuids={acSmartPduPowerSum.pointGuids}
            />
          </Descriptions.Item>
          <Descriptions.Item label="机列平均IT负荷率">
            <PointsLineModalButton
              idcTag={idc}
              btnText={acSmartPduLoadRateAvg.formattedText}
              modalText="机列平均IT负荷率"
              pointGuids={acSmartPduLoadRateAvg.pointGuids}
            />
          </Descriptions.Item>
        </Descriptions>
        <Descriptions
          style={{ textAlign: 'center' }}
          title="列头柜信息"
          layout="vertical"
          size="small"
          bordered
          // column={[arrayCabinetA, arrayCabinetB].filter(Boolean).length}
        >
          {rppAs.length > 0 && (
            <Descriptions.Item label="A路列头柜">
              <Descriptions style={{ margin: '-9px -17px' }} bordered size="small" column={1}>
                {rppAs.map(arrayCabinetA => (
                  <React.Fragment key={arrayCabinetA.deviceGuid}>
                    <Descriptions.Item label="名称">
                      <Link
                        to={generateDeviceRecordRoutePath({
                          guid: arrayCabinetA.deviceGuid,
                        })}
                      >
                        {arrayCabinetA.deviceName}
                      </Link>
                    </Descriptions.Item>
                    <Descriptions.Item label="输入开关状态">
                      <PointsStateLineModalButton
                        modalText={`${arrayCabinetA.deviceName} 输入开关状态`}
                        idcTag={idc}
                        btnText={
                          <StatusText
                            background
                            status={dataA[arrayCabinetA.deviceGuid].onOffStatus.value.STATUS_CODE}
                          >
                            {dataA[arrayCabinetA.deviceGuid].onOffStatus.value.NAME}
                          </StatusText>
                        }
                        pointGuids={rppAPointGuidsMap[arrayCabinetA.deviceGuid]?.inputOnOffState}
                      />
                    </Descriptions.Item>
                    <Descriptions.Item label="电量使用率" span={2}>
                      <PointsLineModalButton
                        idcTag={idc}
                        btnText={dataA[arrayCabinetA.deviceGuid]?.loadRate?.formattedText}
                        modalText={`${arrayCabinetA.deviceName} 电量使用率`}
                        pointGuids={rppAPointGuidsMap[arrayCabinetA.deviceGuid]?.loadRate}
                      />
                    </Descriptions.Item>
                    <Descriptions.Item label="输入功率">
                      <PointsLineModalButton
                        idcTag={idc}
                        btnText={dataA[arrayCabinetA.deviceGuid].activePowerAbc.formattedText}
                        modalText={`${arrayCabinetA.deviceName} 输入功率`}
                        pointGuids={rppAPointGuidsMap[arrayCabinetA.deviceGuid].activePowerAbc}
                      />
                    </Descriptions.Item>
                    <Descriptions.Item label="最高功率">
                      <PointsLineModalButton
                        idcTag={idc}
                        btnText={dataA[arrayCabinetA.deviceGuid].maxP.formattedText}
                        modalText={`${arrayCabinetA.deviceName} 最高功率`}
                        pointGuids={rppAPointGuidsMap[arrayCabinetA.deviceGuid].activePowerMax}
                      />
                    </Descriptions.Item>
                    <Descriptions.Item label="最高电压">
                      <PointsLineModalButton
                        idcTag={idc}
                        btnText={dataA[arrayCabinetA.deviceGuid].maxU.formattedText}
                        modalText={`${arrayCabinetA.deviceName} 最高电压`}
                        pointGuids={
                          rppAPointGuidsMap[arrayCabinetA.deviceGuid].inputPhaseVoltageMax
                        }
                      />
                    </Descriptions.Item>
                    <Descriptions.Item label="最高电流">
                      <PointsLineModalButton
                        idcTag={idc}
                        btnText={dataA[arrayCabinetA.deviceGuid].maxI.formattedText}
                        modalText={`${arrayCabinetA.deviceName} 最高电流`}
                        pointGuids={
                          rppAPointGuidsMap[arrayCabinetA.deviceGuid].inputPhaseCurrentMax
                        }
                      />
                    </Descriptions.Item>
                  </React.Fragment>
                ))}
              </Descriptions>
            </Descriptions.Item>
          )}
          {rppBs.length > 0 && (
            <Descriptions.Item label="B路列头柜">
              <Descriptions style={{ margin: '-9px -17px' }} bordered size="small" column={1}>
                {rppBs.map(arrayCabinetB => (
                  <React.Fragment key={arrayCabinetB.deviceGuid}>
                    <Descriptions.Item label="列头柜名称">
                      <Link
                        to={generateDeviceRecordRoutePath({
                          guid: arrayCabinetB.deviceGuid,
                        })}
                      >
                        {arrayCabinetB.deviceName}
                      </Link>
                    </Descriptions.Item>
                    <Descriptions.Item label="输入开关状态">
                      <PointsStateLineModalButton
                        modalText={`${arrayCabinetB.deviceName} 输入开关状态`}
                        idcTag={idc}
                        btnText={
                          <StatusText
                            background
                            status={dataB[arrayCabinetB.deviceGuid].onOffStatus.value.STATUS_CODE}
                          >
                            {dataB[arrayCabinetB.deviceGuid].onOffStatus.value.NAME}
                          </StatusText>
                        }
                        pointGuids={rppBPointGuidsMap[arrayCabinetB.deviceGuid]?.inputOnOffState}
                      />
                    </Descriptions.Item>
                    <Descriptions.Item label="电量使用率" span={2}>
                      <PointsLineModalButton
                        idcTag={idc}
                        btnText={dataB[arrayCabinetB.deviceGuid]?.loadRate?.formattedText}
                        modalText={`${arrayCabinetB.deviceName} 电量使用率`}
                        pointGuids={rppBPointGuidsMap[arrayCabinetB.deviceGuid]?.loadRate}
                      />
                    </Descriptions.Item>
                    <Descriptions.Item label="输入功率">
                      <PointsLineModalButton
                        idcTag={idc}
                        btnText={dataB[arrayCabinetB.deviceGuid].activePowerAbc.formattedText}
                        modalText={`${arrayCabinetB.deviceName} 输入功率`}
                        pointGuids={rppBPointGuidsMap[arrayCabinetB.deviceGuid].activePowerAbc}
                      />
                    </Descriptions.Item>
                    <Descriptions.Item label="最高功率">
                      <PointsLineModalButton
                        idcTag={idc}
                        btnText={dataB[arrayCabinetB.deviceGuid].maxP.formattedText}
                        modalText={`${arrayCabinetB.deviceName} 最高功率`}
                        pointGuids={rppBPointGuidsMap[arrayCabinetB.deviceGuid].activePowerMax}
                      />
                    </Descriptions.Item>
                    <Descriptions.Item label="最高电压">
                      <PointsLineModalButton
                        idcTag={idc}
                        btnText={dataB[arrayCabinetB.deviceGuid].maxU.formattedText}
                        modalText={`${arrayCabinetB.deviceName} 最高电压`}
                        pointGuids={
                          rppBPointGuidsMap[arrayCabinetB.deviceGuid].inputPhaseVoltageMax
                        }
                      />
                    </Descriptions.Item>
                    <Descriptions.Item label="最高电流">
                      <PointsLineModalButton
                        idcTag={idc}
                        btnText={dataB[arrayCabinetB.deviceGuid].maxI.formattedText}
                        modalText={`${arrayCabinetB.deviceName} 最高电流`}
                        pointGuids={
                          rppBPointGuidsMap[arrayCabinetB.deviceGuid].inputPhaseCurrentMax
                        }
                      />
                    </Descriptions.Item>
                  </React.Fragment>
                ))}
              </Descriptions>
            </Descriptions.Item>
          )}
          {!rppAs.length && !rppBs.length && (
            <Descriptions.Item label="A,B路列头柜">{BLANK_PLACEHOLDER}</Descriptions.Item>
          )}
        </Descriptions>
      </GutterWrapper>
    </ModalButton>
  );
}

const getPointsData = (device, getData, pointCodeMap) => {
  if (!(device && device.deviceType)) {
    return {};
  }
  return {
    onOffStatus: getData(device, {
      reflected: true,
      valueMappingIncluded: true,
      defaults: { value: { NAME: BLANK_PLACEHOLDER } },
      hardCodedPointCode: pointCodeMap.inputOnOffState(device.deviceType),
    }),
    activePowerAbc: getData(device, {
      formatted: true,
      hardCodedPointCode: pointCodeMap.activePowerAbc(device.deviceType),
    }),

    loadRate: getData(device, {
      formatted: true,
      hardCodedPointCode: pointCodeMap.loadRate(device.deviceType),
    }),

    maxP: getData(device, {
      formatted: true,
      hardCodedPointCode: pointCodeMap.activePowerMax(device.deviceType),
    }),
    maxU: getData(device, {
      formatted: true,
      hardCodedPointCode: pointCodeMap.inputPhaseVoltageMax(device.deviceType),
    }),
    maxI: getData(device, {
      formatted: true,
      hardCodedPointCode: pointCodeMap.inputPhaseCurrentMax(device.deviceType),
    }),
  };
};

const mapStateToProps = (
  { 'monitoring.subscriptions': { devicesRealtimeData, devicesAlarmsData }, config: { configMap } },
  { spaceGuid, arrayCabinetIdx, rppAs, rppBs }
) => {
  const { expressionVariablePointsMap, pointsDefinitionMap } = configMap.current;
  const configUtil = new ConfigUtil(configMap.current);

  const pointCodeMap = {
    inputOnOffState: deviceType =>
      configUtil.getPointCode(deviceType, ConfigUtil.constants.pointCodes.RPP_INPUT_ON_OFF_STATE),
    activePowerMax: deviceType =>
      configUtil.getPointCode(deviceType, ConfigUtil.constants.pointCodes.RPP_ACTIVE_POWER_MAX),
    loadRate: deviceType =>
      configUtil.getPointCode(deviceType, ConfigUtil.constants.pointCodes.RPP_LOAD_RATE),
    inputPhaseVoltageMax: deviceType =>
      configUtil.getPointCode(
        deviceType,
        ConfigUtil.constants.pointCodes.RPP_INPUT_PHASE_VOLTAGE_MAX
      ),
    inputPhaseCurrentMax: deviceType =>
      configUtil.getPointCode(
        deviceType,
        ConfigUtil.constants.pointCodes.RPP_INPUT_PHASE_CURRENT_MAX
      ),
    activePowerAbc: deviceType =>
      configUtil.getPointCode(deviceType, ConfigUtil.constants.pointCodes.RPP_ACTIVE_POWER_ABC),
  };

  const rppAPointGuidsMap = getPointSeries(
    rppAs,
    expressionVariablePointsMap,
    pointsDefinitionMap,
    pointCodeMap
  );
  const rppBPointGuidsMap = getPointSeries(
    rppBs,
    expressionVariablePointsMap,
    pointsDefinitionMap,
    pointCodeMap
  );

  const getData = generateGetDeviceMonitoringData(
    devicesRealtimeData,
    devicesAlarmsData,
    pointsDefinitionMap
  );
  const dataA = rppAs.reduce((acc, arrayCabinetA) => {
    acc[arrayCabinetA.deviceGuid] = getPointsData(arrayCabinetA, getData, pointCodeMap);

    return acc;
  }, {});
  Object.keys(dataA).forEach(deviceGuid => {
    rppAPointGuidsMap[deviceGuid].inputOnOffState[0].valueMapping =
      dataA[deviceGuid].onOffStatus.valueMapping;
  });
  const dataB = rppBs.reduce((acc, arrayCabinetB) => {
    acc[arrayCabinetB.deviceGuid] = getPointsData(arrayCabinetB, getData, pointCodeMap);

    return acc;
  }, {});
  Object.keys(dataB).forEach(deviceGuid => {
    rppBPointGuidsMap[deviceGuid].inputOnOffState[0].valueMapping =
      dataB[deviceGuid].onOffStatus.valueMapping;
  });

  const columnDeviceType = configUtil.getOneDeviceType(
    ConfigUtil.constants.deviceTypes.SPACE_COLUMN
  );
  const spaceColumnDevice = {
    deviceGuid: `${spaceGuid}.${arrayCabinetIdx}`,
    deviceType: columnDeviceType,
  };
  const acSmartPduLoadRateAvgPointCode = configUtil.getPointCode(
    columnDeviceType,
    ConfigUtil.constants.pointCodes.PDU_LOAD_RATE_AVG
  );
  const acSmartPduLoadRateAvg = getData(spaceColumnDevice, {
    formatted: true,
    hardCodedPointCode: acSmartPduLoadRateAvgPointCode,
  });
  acSmartPduLoadRateAvg.pointGuids = [
    {
      serieName: `${arrayCabinetIdx} 机列平均IT负荷率`,
      unit: acSmartPduLoadRateAvg.unit,
      deviceGuid: spaceColumnDevice.deviceGuid,
      pointCode: acSmartPduLoadRateAvgPointCode,
    },
  ];

  return {
    dataA,
    rppAPointGuidsMap,
    dataB,
    rppBPointGuidsMap,
    acSmartPduLoadRateAvg,
  };
};

export default connect(mapStateToProps)(RPP);

function getPointSeries(rpps, expressionVariablePointsMap, pointsDefinitionMap, pointCodeMap) {
  const pointSeriesMapper = {};

  if (rpps.length <= 0) {
    return pointSeriesMapper;
  }

  rpps.forEach(rpp => {
    const inputOnOffStatePointCode = pointCodeMap.inputOnOffState(rpp.deviceType);
    const powerPointCode = pointCodeMap.activePowerAbc(rpp.deviceType);
    const powerMaxPointCode = pointCodeMap.activePowerMax(rpp.deviceType);
    const voltageMaxPointCode = pointCodeMap.inputPhaseVoltageMax(rpp.deviceType);
    const currentMaxPointCode = pointCodeMap.inputPhaseCurrentMax(rpp.deviceType);
    const loadRateCode = pointCodeMap.loadRate(rpp.deviceType);
    const pointsDefinitionMapper = pointsDefinitionMap[rpp.deviceType];
    const inputOnOffStatePointDefinition = pointsDefinitionMapper?.[inputOnOffStatePointCode];
    const loadRateDefinition = pointsDefinitionMapper?.[loadRateCode];
    const powerPointDefinition = pointsDefinitionMapper?.[powerPointCode];
    const powerMaxPointDefinition = pointsDefinitionMapper?.[powerMaxPointCode];
    const voltageMaxPointDefinition = pointsDefinitionMapper?.[voltageMaxPointCode];
    const currentMaxPointDefinition = pointsDefinitionMapper?.[currentMaxPointCode];

    pointSeriesMapper[rpp.deviceGuid] = {
      inputOnOffState: [
        getPointSerie(inputOnOffStatePointDefinition, rpp.deviceGuid, inputOnOffStatePointCode),
      ],
      activePowerAbc: [getPointSerie(powerPointDefinition, rpp.deviceGuid, powerPointCode)],
      activePowerMax: [getPointSerie(powerMaxPointDefinition, rpp.deviceGuid, powerMaxPointCode)],
      loadRate: [getPointSerie(loadRateDefinition, rpp.deviceGuid, loadRateCode)],
      inputPhaseVoltageMax: [
        getPointSerie(voltageMaxPointDefinition, rpp.deviceGuid, voltageMaxPointCode),
      ],
      inputPhaseCurrentMax: [
        getPointSerie(currentMaxPointDefinition, rpp.deviceGuid, currentMaxPointCode),
      ],
    };

    const exprVarPointsMap = expressionVariablePointsMap[rpp.deviceType];

    if (exprVarPointsMap === undefined) {
      return;
    }

    pointSeriesMapperMutator(
      'activePowerAbc',
      exprVarPointsMap,
      powerPointCode,
      pointSeriesMapper,
      rpp.deviceGuid,
      pointsDefinitionMapper
    );

    pointSeriesMapperMutator(
      'loadRate',
      exprVarPointsMap,
      loadRateCode,
      pointSeriesMapper,
      rpp.deviceGuid,
      pointsDefinitionMapper
    );
    pointSeriesMapperMutator(
      'activePowerMax',
      exprVarPointsMap,
      powerMaxPointCode,
      pointSeriesMapper,
      rpp.deviceGuid,
      pointsDefinitionMapper
    );
    pointSeriesMapperMutator(
      'inputPhaseVoltageMax',
      exprVarPointsMap,
      voltageMaxPointCode,
      pointSeriesMapper,
      rpp.deviceGuid,
      pointsDefinitionMapper
    );
    pointSeriesMapperMutator(
      'inputPhaseCurrentMax',
      exprVarPointsMap,
      currentMaxPointCode,
      pointSeriesMapper,
      rpp.deviceGuid,
      pointsDefinitionMapper
    );
  });

  return pointSeriesMapper;
}

function pointSeriesMapperMutator(
  id,
  exprVarPointsMap,
  pointCode,
  pointSeriesMapper,
  deviceGuid,
  pointsDefinitionMapper
) {
  const powerExprVarPoints = exprVarPointsMap[pointCode];
  if (Array.isArray(powerExprVarPoints)) {
    pointSeriesMapper[deviceGuid][id] = powerExprVarPoints.map(varPoint =>
      getPointSerie(pointsDefinitionMapper?.[varPoint], deviceGuid, varPoint)
    );
  }
}

function getPointSerie(pointDefinition, deviceGuid, pointCode) {
  return {
    serieName: pointDefinition?.name ?? '未知测点',
    deviceGuid,
    pointCode,
    unit: pointDefinition?.unit,
  };
}
