import React from 'react';
import { connect } from 'react-redux';

import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { PointsStateLineModalButton } from '@manyun/monitoring.chart.points-state-line';

import { generateGetDeviceMonitoringData } from '@manyun/dc-brain.legacy.utils/device';

import { StyledPduStatusBar } from './styled';

export function PduStatusBar({ idc, pduAsStatus, pduBsStatus }) {
  const pointGuids = React.useMemo(() => {
    const tmp = [];
    [...pduAsStatus, ...pduBsStatus].forEach(
      ({ deviceName, deviceGuid, pointCode, valueMapping }) => {
        if (pointCode !== undefined) {
          tmp.push({
            serieName: deviceName,
            deviceGuid,
            pointCode,
            valueMapping,
          });
        }
      }
    );

    return tmp;
  }, [pduAsStatus, pduBsStatus]);

  if (!pduAsStatus.length && !pduBsStatus.length) {
    return null;
  }

  const height = `${1 / pduAsStatus.length}em`;

  return (
    <PointsStateLineModalButton
      modalText="开关状态"
      idcTag={idc}
      pointGuids={pointGuids}
      btnText={
        <>
          {pduAsStatus.map((pduA, idx) => (
            <StyledPduStatusBar
              key={pduA.deviceGuid + '_$$_' + pduBsStatus[idx]?.deviceGuid}
              height={height}
              isFirst={idx === 0}
              isLast={idx === pduAsStatus.length - 1}
              status={[pduA.status, pduBsStatus[idx]?.status]}
            />
          ))}
        </>
      }
    />
  );
}

const mapStateToProps = (
  { 'monitoring.subscriptions': { devicesRealtimeData, devicesAlarmsData }, config: { configMap } },
  { pduAs, pduBs }
) => {
  const { pointsDefinitionMap } = configMap.current;
  const configUtil = new ConfigUtil(configMap.current);

  const getData = generateGetDeviceMonitoringData(
    devicesRealtimeData,
    devicesAlarmsData,
    pointsDefinitionMap
  );

  let pduAsStatus = [];
  if (Array.isArray(pduAs) && pduAs.length) {
    pduAsStatus = getPdusStatus(pduAs, { getData, configUtil });
  }

  let pduBsStatus = [];
  if (Array.isArray(pduBs) && pduBs.length) {
    pduBsStatus = getPdusStatus(pduBs, { getData, configUtil });
  }

  return { pduAsStatus, pduBsStatus };
};

export default connect(mapStateToProps)(PduStatusBar);

const options = {
  reflected: true,
  valueMappingIncluded: true,
  defaults: {
    value: {
      STATUS_CODE: undefined,
    },
  },
};

function getPdusStatus(pdus, { getData, configUtil }) {
  return pdus.map(pdu => {
    if (!pdu) {
      return { ...pdu, status: undefined, pointCode: undefined };
    }

    const { value, pointCode, valueMapping } = getData(pdu, {
      ...options,
      hardCodedPointCode: configUtil.getPointCode(
        pdu.deviceType,
        ConfigUtil.constants.pointCodes.PDU_ON_OFF_STATE
      ),
    });

    return { ...pdu, status: value.STATUS_CODE, pointCode, valueMapping };
  });
}
