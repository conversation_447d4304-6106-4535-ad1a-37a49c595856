import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';

import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { BulletCamera } from '@manyun/monitoring.page.room-view/dist/components/bullet-camera';
import { SmokeDetector } from '@manyun/monitoring.page.room-view/dist/components/smoke-detector';

import { AISLE_TYPE_MAP, AISLE_WIDTH, RACKS_COLUMN_GAP, RACKS_COLUMN_WIDTH } from '../../constants';
import AirConditioner from './ac';
import CabinetsTable from './rack-array';
import { AcWrapper, Aisle, CabinetArrayInnerColumn, CabinetArrayOuterColumn } from './styled';
import ThtbSensor from './thtb-sensor';

function AisleWithDevices({ aisleType, width, height, devices }) {
  const config = useSelector(selectCurrentConfig);
  const configUtil = new ConfigUtil(config);
  // const typeofProximityReader = configUtil.typeofDeviceGen(
  //   ConfigUtil.constants.deviceTypes.DOOR_PROXIMITY_READER
  // );
  const typeofBulletCamera = configUtil.typeofDeviceGen(
    ConfigUtil.constants.deviceTypes.BULLET_CCTV_CAMERA
  );
  const typeofSmokeDetector = configUtil.typeofDeviceGen(
    ConfigUtil.constants.deviceTypes.SMOKE_DETECTOR
  );
  const typeofThermalDetector = configUtil.typeofDeviceGen(
    ConfigUtil.constants.deviceTypes.THERMAL_DETECTOR
  );
  const typeofAirSampler = configUtil.typeofDeviceGen(ConfigUtil.constants.deviceTypes.AIR_SAMPLER);

  return (
    <Aisle type={aisleType} calcedHeight={height}>
      {devices.map(device => {
        if (typeofBulletCamera(device.deviceType)) {
          return <BulletCamera key={device.deviceGuid} {...device} />;
        } else if (
          typeofSmokeDetector(device.deviceType) ||
          typeofThermalDetector(device.deviceType) ||
          typeofAirSampler(device.deviceType)
        ) {
          return <SmokeDetector key={device.deviceGuid} {...device} />;
        }
        return <ThtbSensor key={device.deviceGuid} aisleWidth={width} {...device} />;
      })}
    </Aisle>
  );
}

function CabinetArrayItem({
  className,
  spaceGuids,
  // the `height` of the longest item.
  height,
  roomGrid, // 机列编号
  rackColumn,
  isFirst,
  isLast,
}) {
  const aisleWidth = isFirst || isLast ? AISLE_WIDTH / 2 : AISLE_WIDTH;

  const { idc, block, room } = useParams();

  const [paddingLeft, setPaddingLeft] = useState(0);

  const {
    acNorth,
    acSouth,
    grids,
    leftAisleType,
    rightAisleType,
    leftAisleHeight,
    rightAisleHeight,
    leftSensors,
    rightSensors,
    rppAs,
    rppBs,
  } = rackColumn;
  usePaddingLeft(leftAisleType, rightAisleType, setPaddingLeft);
  const columnReversed = rightAisleType === AISLE_TYPE_MAP.HOT;

  return (
    <CabinetArrayOuterColumn className={className}>
      <AcWrapper paddingLeft={paddingLeft}>
        {acNorth && <AirConditioner spaceGuids={spaceGuids} roomGrid={roomGrid} {...acNorth} />}
      </AcWrapper>
      <CabinetArrayInnerColumn calcedHeight={height}>
        <div>
          <AisleWithDevices
            aisleType={leftAisleType}
            width={aisleWidth}
            height={leftAisleHeight}
            devices={leftSensors}
          />
        </div>
        <CabinetsTable
          spaceGuids={spaceGuids}
          spaceGuid={`${idc}.${block}.${room}`}
          roomGrid={roomGrid}
          rppAs={rppAs}
          rppBs={rppBs}
          dataSource={grids}
          columnReversed={columnReversed}
        />
        <div>
          <AisleWithDevices
            aisleType={rightAisleType}
            width={aisleWidth}
            height={rightAisleHeight}
            devices={rightSensors}
          />
        </div>
      </CabinetArrayInnerColumn>
      <AcWrapper paddingLeft={paddingLeft}>
        {acSouth && <AirConditioner spaceGuids={spaceGuids} roomGrid={roomGrid} {...acSouth} />}
      </AcWrapper>
    </CabinetArrayOuterColumn>
  );
}

export default CabinetArrayItem;

function usePaddingLeft(leftAisleType, rightAisleType, setPaddingLeft) {
  useEffect(() => {
    let paddingLeft;
    if (leftAisleType !== null && rightAisleType !== null) {
      const width =
        (AISLE_WIDTH / 2 +
          RACKS_COLUMN_GAP +
          RACKS_COLUMN_WIDTH +
          RACKS_COLUMN_GAP +
          AISLE_WIDTH / 2) /
        3;
      paddingLeft = width + width / 3;
    } else if (leftAisleType !== null && rightAisleType === null) {
      const width = (AISLE_WIDTH / 2 + RACKS_COLUMN_GAP + RACKS_COLUMN_WIDTH) / 2;
      paddingLeft = width + width / 3;
    } else if (leftAisleType === null && rightAisleType !== null) {
      const width = (RACKS_COLUMN_WIDTH + RACKS_COLUMN_GAP + AISLE_WIDTH / 2) / 2;
      paddingLeft = width / 3;
    } else {
      paddingLeft = RACKS_COLUMN_WIDTH / 3;
    }
    /**
     * 设置空调 `paddingLeft` 以和 <RackArray /> 表格的第二列对齐
     */
    setPaddingLeft(paddingLeft);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [leftAisleType, rightAisleType]);
}
