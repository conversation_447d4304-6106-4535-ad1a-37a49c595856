import React, { useEffect } from 'react';
import { connect } from 'react-redux';
import { useParams } from 'react-router-dom';

import flatten from 'lodash/flatten';

import { ConfigUtil } from '@manyun/dc-brain.util.config';

import { getParentDevicesActionCreator } from '@manyun/dc-brain.legacy.redux/actions/roomMonitoringActions';

import HvdcSummary from './../hvdc-summary';
import UpsSummary from './../ups-summary';

function UpsOrHvdc({ visibleType, rppAs, rppBs, upsesA, hvdcsA, upsesB, hvdcsB, getParentDevice }) {
  const { idc, block } = useParams();

  const rppADeviceGuids = rppAs.map(rppA => rppA.deviceGuid);
  const rppBDeviceGuids = rppBs.map(rppB => rppB.deviceGuid);
  const rppDeviceGuidsStr = [...rppADeviceGuids, ...rppBDeviceGuids].join(',');

  useEffect(() => {
    if (!rppDeviceGuidsStr) {
      return;
    }
    getParentDevice({
      idc,
      block,
      deviceGuids: rppDeviceGuidsStr.split(','),
    });
  }, [idc, block, rppDeviceGuidsStr, getParentDevice]);

  if (visibleType === VISIBLE_TYPE_MAP.UPS) {
    return <UpsSummary upsesA={upsesA} upsesB={upsesB} />;
  }

  if (visibleType === VISIBLE_TYPE_MAP.HVDC) {
    return <HvdcSummary />;
  }

  return null;
}

const mapStateToProps = (
  { roomMonitoring: { arrayCabinetsParentDeviceMap }, config: { configMap } },
  { rppAs, rppBs }
) => {
  const configUtil = new ConfigUtil(configMap.current);

  const upsTypes = configUtil.getAllDeviceTypes(ConfigUtil.constants.deviceTypes.UPS) || [];
  const hvdcTypes =
    configUtil.getAllDeviceTypes(ConfigUtil.constants.deviceTypes.HVDC, { logLevel: 'silent' }) ||
    [];

  let visibleType;

  if (!rppAs?.length && !rppBs?.length) {
    return { visibleType };
  }

  const parentDevicesA = flatten(
    rppAs?.map(rppA => arrayCabinetsParentDeviceMap[rppA.deviceGuid])?.filter(Boolean) || []
  );
  const parentDevicesB = flatten(
    rppBs?.map(rppB => arrayCabinetsParentDeviceMap[rppB.deviceGuid])?.filter(Boolean) || []
  );
  if (!parentDevicesA.length && !parentDevicesB.length) {
    return { visibleType };
  }

  const { upses: upsesA, hvdcs: hvdcsA } = getParentUpsesOrHvdcs(
    parentDevicesA,
    upsTypes,
    hvdcTypes
  );
  const { upses: upsesB, hvdcs: hvdcsB } = getParentUpsesOrHvdcs(
    parentDevicesB,
    upsTypes,
    hvdcTypes
  );
  if (upsesA.length || upsesB.length) {
    visibleType = VISIBLE_TYPE_MAP.UPS;
  }
  if (hvdcsA.length || hvdcsB.length) {
    visibleType = VISIBLE_TYPE_MAP.HVDC;
  }
  if (!visibleType) {
    // 既不是 UPS，也不是 HVDC
    console.error(`[Device's parent device is not UPS and neither HVDC.`);
  }

  return { visibleType, upsesA, hvdcsA, upsesB, hvdcsB };
};

const mapDispatchToProps = {
  getParentDevice: getParentDevicesActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(UpsOrHvdc);

/**
 * 用于判断展示 UPS 还是 HVDC
 */
const VISIBLE_TYPE_MAP = {
  UPS: 'ups',
  HVDC: 'hvdc',
};

function getParentUpsesOrHvdcs(parentDevices = [], upsTypes, hvdcTypes) {
  const upses = [];
  const hvdcs = [];
  parentDevices.forEach(parentDevice => {
    const isUps = upsTypes.includes(parentDevice.deviceType);
    const isHvdc = hvdcTypes.includes(parentDevice.deviceType);
    if (isUps) {
      upses.push(parentDevice);
    } else if (isHvdc) {
      hvdcs.push(parentDevice);
    }
  });

  return { upses, hvdcs };
}
