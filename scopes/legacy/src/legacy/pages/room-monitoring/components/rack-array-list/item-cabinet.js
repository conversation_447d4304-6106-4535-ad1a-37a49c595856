import React from 'react';
import { useSelector } from 'react-redux';

import { Typography } from '@manyun/base-ui.ui.typography';

import { BlinkWarningIcon } from '@manyun/monitoring.page.room-view/dist/components/blink';
import { getMonitoringData } from '@manyun/monitoring.state.subscriptions';
import { generateGetDeviceAlarmStatus } from '@manyun/monitoring.util.get-monitoring-data';

import { CabinetDescription, ModalButton } from '@manyun/dc-brain.legacy.components';

export function ItemCabinet({
  gridTag,
  rppAs,
  rppBs,
  pduAs: pduDevicesA = [],
  pduBs: pduDevicesB = [],
  spaceGuid,
}) {
  const gridGuid = `${spaceGuid}.${gridTag}`;

  const { devicesAlarmsData } = useSelector(getMonitoringData);
  const { cabinetMess } = useSelector(state => state.cabinetManage);

  const getDeviceAlarmStatus = React.useMemo(
    () => generateGetDeviceAlarmStatus(devicesAlarmsData),
    [devicesAlarmsData]
  );
  const { isAlerting } = React.useMemo(
    () => getDeviceAlarmStatus(gridGuid),
    [getDeviceAlarmStatus, gridGuid]
  );

  return (
    <ModalButton
      modalStyle={{ minWidth: 700 }}
      width="min-content"
      compact
      autoHeight
      destroyOnClose
      footer={null}
      type="link"
      text={
        <>
          {isAlerting ? <Typography.Text type="danger">{gridTag}</Typography.Text> : gridTag}
          {isAlerting && <BlinkWarningIcon />}
        </>
      }
      title={`${gridTag} 机柜(${cabinetMess.powerStatus?.name})`}
    >
      <CabinetDescription
        gridInfo={{
          gridGuid,
          gridTag,
        }}
        rppAs={rppAs}
        rppBs={rppBs}
        pduDevicesA={pduDevicesA}
        pduDevicesB={pduDevicesB}
      />
    </ModalButton>
  );
}

export default ItemCabinet;
