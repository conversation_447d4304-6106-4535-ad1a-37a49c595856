import styled from 'styled-components';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';

import { TinyCard } from '@manyun/dc-brain.legacy.components';

import { AISLE_WIDTH, SENSOR_SIZE } from './../../../constants';

const STATUS_MAP = {
  alarm: `var(--${prefixCls}-error-color)`,
  normal: `var(--${prefixCls}-success-color)`,
  disabled: 'var(--disabled-bg)',
};

function getColorByStauts(status) {
  return STATUS_MAP[status];
}

export const StyledTinyCard = styled(TinyCard)`
  .manyun-tabs-nav .manyun-tabs-tab {
    margin: 0 8px 0 0;
    padding: ${window.innerWidth > 1920 ? 16 : 8}px;
  }
`;

export const CabinetArrayWrapper = styled.div`
  display: grid;
  align-items: center;
  overflow: hidden;
`;

export default CabinetArrayWrapper;

export const CabinetArrayOuterColumn = styled.div`
  grid-row: 1;
`;

const padding = 'calc(37px / 2)';

export const CabinetArrayInnerColumn = styled.div`
  display: grid;
  font-size: 16px;
  padding: ${padding} 0;
  border-top-width: 1px;
  border-right-width: 0;
  border-bottom-width: 1px;
  border-left-width: 0;
  border-style: solid;
  border-color: var(--border-color-split);

  height: ${({ calcedHeight = '100%' }) =>
    typeof calcedHeight === 'number' ? `calc(${calcedHeight}px + 2 * ${padding})` : calcedHeight};

  > * {
    grid-row: 1;
  }
`;

export const AcWrapper = styled.div`
  position: relative;
  height: 60px;
  margin-left: ${({ paddingLeft }) => paddingLeft + 'px'};

  padding-top: 0.5em;
  padding-bottom: 0.5em;
`;

export const StyledPduStatusBar = styled.div`
  margin: 0 auto;
  display: flex;
  width: 30px;
  height: ${({ height = '1em' }) => height};

  ::before {
    content: '';
    flex: 1;
    border-top-left-radius: ${({ isFirst = false }) => (isFirst ? '8px' : '0')};
    border-bottom-left-radius: ${({ isLast = false }) => (isLast ? '8px' : '0')};

    background: ${({ status: [statusA = 'disabled'] }) => getColorByStauts(statusA)};
  }

  ::after {
    content: '';
    flex: 1;
    border-top-right-radius: ${({ isFirst = false }) => (isFirst ? '8px' : '0')};
    border-bottom-right-radius: ${({ isLast = false }) => (isLast ? '8px' : '0')};

    background: ${({ status: [, statusB = 'disabled'] }) => getColorByStauts(statusB)};
  }
`;

export const Aisle = styled.div`
  position: relative;
  width: ${AISLE_WIDTH / 2}px;
  height: ${({ calcedHeight = '100%' }) =>
    typeof calcedHeight === 'number' ? `${calcedHeight}px` : calcedHeight};
  background: ${({ type = 'cold' }) =>
    type === 'cold' ? `var(--${prefixCls}-primary-1)` : 'transparent'};
  display: flex;
  flex-direction: column;
  align-items: center;
`;

export const SENSOR_NAME_WRAPPER_WIDTH = 75;
export const SENSOR_NAME_WRAPPER_OFFSET = (SENSOR_NAME_WRAPPER_WIDTH - SENSOR_SIZE) / 2;

export const SensorWrapper = styled.div`
  border-radius: 50%;
  width: ${SENSOR_SIZE}px;
  height: ${SENSOR_SIZE}px;
  padding: 4px;
  background: var(--background-color-base);

  z-index: 1;
  position: absolute;
  ${({ position = {} }) => {
    const { top = -9999, right, left } = position;
    const styles = [`top: ${top}px;`, right && `right: ${right};`, left && `left: ${left};`].filter(
      Boolean
    );

    return styles.join('\n');
  }};
  cursor: ${({ disabled = false }) => (disabled ? 'unset' : 'pointer')};

  > * {
    width: 100%;
  }
`;
