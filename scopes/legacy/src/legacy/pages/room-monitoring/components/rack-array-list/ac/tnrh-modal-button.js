import React from 'react';
import { useSelector } from 'react-redux';

import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { getMonitoringData } from '@manyun/monitoring.state.subscriptions';
import { generateGetPointMonitoringData } from '@manyun/monitoring.util.get-monitoring-data';

import { ModalButton, StatusText } from '@manyun/dc-brain.legacy.components';

import Tnrh from './tnrh';

function AirConditioner({ spaceGuids, deviceGuid, deviceType, deviceName }) {
  const config = useSelector(selectCurrentConfig);
  const blockGuid = spaceGuids.slice(0, 2).join('.');
  const configUtil = React.useMemo(
    () =>
      new ConfigUtil(config, {
        defaultSpaceGuid: blockGuid,
      }),
    [config, blockGuid]
  );
  const { devicesRealtimeData, devicesAlarmsData } = useSelector(getMonitoringData);
  const getPointData = generateGetPointMonitoringData({
    realtimeData: devicesRealtimeData,
    alarmsData: devicesAlarmsData,
    configUtil,
  });
  const tPointCode = configUtil.getPointCode(
    deviceType,
    ConfigUtil.constants.pointCodes.CRAC_T_OF_AIR_SUPPLY
  );
  const rhPointCode = configUtil.getPointCode(
    deviceType,
    ConfigUtil.constants.pointCodes.CRAC_RH_OF_AIR_SUPPLY
  );

  const device = { deviceGuid, deviceType };
  const { formattedText, disabled, status } = getPointData(device, {
    formatted: true,
    hardCodedPointCode: tPointCode,
  });

  const stautsText = <StatusText status={status}>{formattedText}</StatusText>;

  if (disabled) {
    return stautsText;
  }

  return (
    <ModalButton
      compact
      type="link"
      text={stautsText}
      modalStyle={{ minWidth: (1200 / 1600) * 100 + '%', maxWidth: 1200 }}
      footer={null}
      destroyOnClose
      title={`${deviceName}送风温湿度`}
    >
      {visible => (
        <Tnrh
          visible={visible}
          deviceType={deviceType}
          deviceGuid={deviceGuid}
          pointCodes={[tPointCode, rhPointCode]}
        />
      )}
    </ModalButton>
  );
}

export default AirConditioner;
