import React from 'react';
import { connect, useSelector } from 'react-redux';

import { Table } from '@manyun/base-ui.ui.table';

import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { PointsLineModalButton } from '@manyun/monitoring.chart.points-line';
import { getMonitoringData } from '@manyun/monitoring.state.subscriptions';
import { generateGetDeviceAlarmStatus } from '@manyun/monitoring.util.get-monitoring-data';

import { getSpaceGuidMap } from '@manyun/dc-brain.legacy.utils';
import { generateGetDeviceMonitoringData } from '@manyun/dc-brain.legacy.utils/device';

import { useElementSpotlight } from '../../context/spotlight';
import { useSpotlightTarget } from '../../hook/use-spotlight-target';
import { RACKS_COLUMN_WIDTH } from './../../constants';
import ACSummaryModalButton from './ac/summary-modal-button';
import ACTnrhModalButton from './ac/tnrh-modal-button';
import RPP from './array-cabinet';
import ItemCabinet from './item-cabinet';
import PduPowerSum from './pdu-power-sum';
import PduStatusBar from './pdu-status-bar';

export function CabinetsTable({
  spaceGuids,
  spaceGuid,
  acSmartPduPowerSum,
  roomGrid,
  rppAs,
  rppBs,
  dataSource = [],
  columnReversed,
  className,
}) {
  if (!(Array.isArray(dataSource) && dataSource.length)) {
    return null;
  }
  const columns = generateColumns(
    spaceGuids,
    spaceGuid,
    acSmartPduPowerSum,
    roomGrid,
    rppAs,
    rppBs,
    columnReversed
  );

  return (
    <Table
      style={{ width: RACKS_COLUMN_WIDTH }}
      bordered
      size="small"
      rowKey={gridTagOrCRAC => {
        return gridTagOrCRAC.deviceGuid || gridTagOrCRAC.gridTag;
      }}
      pagination={false}
      columns={columns}
      dataSource={dataSource}
      className={className}
    />
  );
}

const mapStateToProps = (
  { 'monitoring.subscriptions': { devicesRealtimeData, devicesAlarmsData }, config: { configMap } },
  { spaceGuid, roomGrid }
) => {
  const { pointsDefinitionMap } = configMap.current;
  const configUtil = new ConfigUtil(configMap.current, { defaultSpaceGuid: spaceGuid });
  const spaceColumnDeviceType = configUtil.getOneDeviceType(
    ConfigUtil.constants.deviceTypes.SPACE_COLUMN
  );
  const getMonitorData = generateGetDeviceMonitoringData(
    devicesRealtimeData,
    devicesAlarmsData,
    pointsDefinitionMap
  );
  const virtualGuid = `${spaceGuid}.${roomGrid}`;
  const device = {
    deviceGuid: virtualGuid,
    deviceType: spaceColumnDeviceType,
  };
  const pointCode = configUtil.getPointCode(
    spaceColumnDeviceType,
    ConfigUtil.constants.pointCodes.PDU_POWER_SUM
  );
  const acSmartPduPowerSum = getMonitorData(device, {
    formatted: true,
    hardCodedPointCode: pointCode,
  });
  acSmartPduPowerSum.pointGuids = [
    {
      serieName: `${roomGrid} 机列总功率`,
      unit: acSmartPduPowerSum.unit,
      deviceGuid: virtualGuid,
      pointCode,
    },
  ];

  return { acSmartPduPowerSum };
};

export const CabinetsTableConnect = connect(mapStateToProps)(CabinetsTable);

export default CabinetsTableConnect;

function generateColumns(
  spaceGuids,
  spaceGuid,
  acSmartPduPowerSum,
  roomGrid,
  rppAs,
  rppBs,
  reverse = false
) {
  const { idc } = getSpaceGuidMap(spaceGuid);

  const columns = [
    {
      width: 60,
      align: 'center',
      title: 'A B',
      dataIndex: 'pdus',
      onCell: cabinet => {
        if (cabinet.type === 'CRAC') {
          // 表示这行是列间空调
          return {
            colSpan: 2,
            style: {
              padding: 0,
            },
          };
        }
        return {};
      },
      render: (__, cabinet) => {
        if (cabinet.type === 'CRAC') {
          // 表示这行是列间空调
          return <ACTnrhModalButton spaceGuids={spaceGuids} {...cabinet} />;
        }
        return <PduStatusBar idc={idc} pduAs={cabinet.pduAs} pduBs={cabinet.pduBs} />;
      },
    },
    {
      width: 90,
      align: 'center',
      title: (
        <PointsLineModalButton
          idcTag={idc}
          btnText={acSmartPduPowerSum.formattedText}
          modalText="机列总功率"
          pointGuids={acSmartPduPowerSum.pointGuids}
        />
      ),
      dataIndex: 'electricPower',
      onCell: cabinet => {
        if (cabinet.type === 'CRAC') {
          // 表示这行是列间空调
          return {
            colSpan: 0,
          };
        }
        return {};
      },
      render: (__, cabinet) => {
        if (cabinet.type === 'CRAC') {
          // 表示这行是列间空调
          return '';
        }

        return (
          <PduPowerSum
            spaceGuids={spaceGuids}
            spaceGuid={spaceGuid}
            roomGrid={roomGrid}
            gridTag={cabinet.gridTag}
          />
        );
      },
    },
    {
      width: 180,
      align: 'center',
      title: (
        <RPP
          spaceGuids={spaceGuids}
          spaceGuid={spaceGuid}
          arrayCabinetIdx={roomGrid}
          rppAs={rppAs}
          rppBs={rppBs}
          acSmartPduPowerSum={acSmartPduPowerSum}
        />
      ),
      dataIndex: 'cabinet',
      onCell: cabinet => {
        if (cabinet.type === 'CRAC') {
          // 表示这行是列间空调
          return {
            style: {
              padding: 0,
            },
          };
        }
        return {};
      },
      render: (__, cabinet) => {
        if (cabinet.type === 'CRAC') {
          // 表示这行是列间空调
          return (
            <InterColumnAirConditoner
              style={{ marginTop: 9.5, marginBottom: 3.5 }}
              spaceGuids={spaceGuids}
              {...cabinet}
            />
          );
        }

        return (
          <ItemCabinet
            spaceGuid={spaceGuid}
            gridTag={cabinet.gridTag}
            rppAs={cabinet.rppAs}
            rppBs={cabinet.rppBs}
            pduAs={cabinet.pduAs}
            pduBs={cabinet.pduBs}
            acSmartPduPowerSum={acSmartPduPowerSum}
          />
        );
      },
    },
  ];
  if (reverse) {
    return columns.reverse();
  }
  return columns;
}

function InterColumnAirConditoner({ deviceGuid, ...restProps }) {
  const spotlightTarget = useSpotlightTarget();
  const [ref] = useElementSpotlight({ focus: deviceGuid === spotlightTarget });

  const { devicesAlarmsData } = useSelector(getMonitoringData);
  const getDeviceAlarmStatus = React.useMemo(
    () => generateGetDeviceAlarmStatus(devicesAlarmsData),
    [devicesAlarmsData]
  );
  const { isAlerting } = React.useMemo(
    () => getDeviceAlarmStatus(deviceGuid),
    [getDeviceAlarmStatus, deviceGuid]
  );

  return (
    <ACSummaryModalButton
      ref={ref}
      deviceGuid={deviceGuid}
      {...restProps}
      isAlerting={isAlerting}
    />
  );
}
