import React from 'react';
import { useSelector } from 'react-redux';

import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { BlinkWarningIcon } from '@manyun/monitoring.page.room-view/dist/components/blink';
import { getMonitoringData } from '@manyun/monitoring.state.subscriptions';
import {
  generateGetDeviceAlarmStatus,
  generateGetPointMonitoringData,
} from '@manyun/monitoring.util.get-monitoring-data';

import { StatusText } from '@manyun/dc-brain.legacy.components';
import { STATUS_MAP } from '@manyun/dc-brain.legacy.constants/statusColor';
import { useElementSpotlight } from '@manyun/dc-brain.legacy.pages/room-monitoring/context/spotlight';
import { useSpotlightTarget } from '@manyun/dc-brain.legacy.pages/room-monitoring/hook/use-spotlight-target';

import Tnrh from './ac/tnrh';
import { SENSOR_NAME_WRAPPER_OFFSET, SENSOR_NAME_WRAPPER_WIDTH, SensorWrapper } from './styled';

/**
 * 冷（或热）通道温湿度传感器
 * @param {object} props
 */
function ThtbSensor({ deviceGuid, deviceType, deviceName = 'UNKNOW', position, aisleWidth }) {
  const config = useSelector(selectCurrentConfig);
  const configUtil = React.useMemo(() => new ConfigUtil(config), [config]);
  const thtbSensorTypes = React.useMemo(
    () => configUtil.getAllDeviceTypes(ConfigUtil.constants.deviceTypes.THTB_SENSOR) || [],
    [configUtil]
  );
  const isThtbSensor = React.useMemo(
    () => thtbSensorTypes.includes(deviceType),
    [thtbSensorTypes, deviceType]
  );

  const { devicesRealtimeData, devicesAlarmsData } = useSelector(getMonitoringData);
  const getDeviceAlarmStatus = React.useMemo(
    () => generateGetDeviceAlarmStatus(devicesAlarmsData),
    [devicesAlarmsData]
  );
  const { isAlerting } = React.useMemo(
    () => getDeviceAlarmStatus(deviceGuid),
    [getDeviceAlarmStatus, deviceGuid]
  );
  const getData = React.useMemo(
    () =>
      generateGetPointMonitoringData({
        realtimeData: devicesRealtimeData,
        alarmsData: devicesAlarmsData,
        configUtil,
      }),
    [devicesRealtimeData, devicesAlarmsData, configUtil]
  );
  const device = { deviceGuid, deviceType };
  const temperature = getData(device, {
    pointType: ConfigUtil.constants.pointCodes.THTB_SENSOR_T,
    formatted: true,
  });
  const humidity = getData(device, {
    pointType: ConfigUtil.constants.pointCodes.THTB_SENSOR_RH,
    formatted: true,
  });
  const pointCodes = [temperature.pointCode, humidity.pointCode];

  const spotlightTarget = useSpotlightTarget();
  const [ref] = useElementSpotlight({ focus: isThtbSensor && deviceGuid === spotlightTarget });

  const [visible, setVisible] = React.useState(false);

  if (!isThtbSensor) {
    console.warn(
      `检测到设备 ${deviceName}(${deviceGuid}) 的扩展位置不符合要求，此设备不是冷（或热）通道温湿度传感器，扩展位置不能是 {X}.L 或 {X}.R(X 为机柜编号)。`
    );
    return null;
  }

  function toggleVisible() {
    if (disabled) {
      return;
    }
    setVisible(prevVisible => !prevVisible);
  }

  const disabled = temperature.disabled && humidity.disabled;

  return (
    <>
      <div
        style={{
          position: 'absolute',
          zIndex: 1,
          width: SENSOR_NAME_WRAPPER_WIDTH,
          textAlign: 'center',
          top: position.top - 24,
          left: position.left
            ? `calc(${position.left} - ${SENSOR_NAME_WRAPPER_OFFSET}px)`
            : undefined,
          right: position.right
            ? `calc(${position.right} - ${SENSOR_NAME_WRAPPER_OFFSET}px)`
            : undefined,
        }}
      >
        <Space style={{ width: '100%' }} size={4}>
          <Typography.Text
            style={{ fontSize: 14, width: isAlerting ? 55 : 75 }}
            ellipsis={{ tooltip: deviceName }}
            type={isAlerting ? 'danger' : undefined}
          >
            {deviceName}
          </Typography.Text>
          {isAlerting && <BlinkWarningIcon />}
        </Space>
      </div>
      <SensorWrapper ref={ref} position={position} disabled={disabled} onClick={toggleVisible}>
        <Space direction="vertical" size={4} align="center">
          <StatusText style={{ fontSize: 12 }} status={getStatus(temperature.status, disabled)}>
            {temperature.formattedText}
          </StatusText>
          <StatusText style={{ fontSize: 12 }} status={getStatus(humidity.status, disabled)}>
            {humidity.formattedText}
          </StatusText>
        </Space>
      </SensorWrapper>
      {!disabled && (
        <Modal
          style={{ minWidth: (1200 / 1600) * 100 + '%', maxWidth: 1200 }}
          destroyOnClose
          footer={null}
          title={`${deviceName}.温湿度`}
          open={visible}
          onCancel={toggleVisible}
        >
          <Tnrh
            visible={visible}
            deviceType={deviceType}
            deviceGuid={deviceGuid}
            pointCodes={pointCodes}
          />
        </Modal>
      )}
    </>
  );
}

export default ThtbSensor;

function getStatus(status, disabled) {
  if (status !== STATUS_MAP.DEFAULT) {
    return status;
  }
  if (disabled) {
    return STATUS_MAP.DEFAULT;
  }
  return STATUS_MAP.REFERENCE;
}
