import React, { useCallback, useEffect, useRef, useState } from 'react';
import { connect } from 'react-redux';
import { useLocation } from 'react-router-dom';
import { useShallowCompareEffect } from 'react-use';

import uniq from 'lodash/uniq';
import without from 'lodash/without';

import { Card } from '@manyun/base-ui.ui.card';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Typography } from '@manyun/base-ui.ui.typography';

import { RelatedRooms } from '@manyun/monitoring.page.room-view';
import { CamerasLink } from '@manyun/monitoring.page.room-view/dist/components/cameras-link';

import { TinyEmpty } from '@manyun/dc-brain.legacy.components';
import { roomMonitoringActions } from '@manyun/dc-brain.legacy.redux/reducers/roomMonitoringSlice';
import { getLocationSearchMap } from '@manyun/dc-brain.legacy.utils';

import { useSpotlightTarget } from '../hook/use-spotlight-target';
import InfraRoomGraphPreview from './infra-room-graph-preview';
import CabinetArrayItem from './rack-array-list/rack-array-item';
import CabinetArrayWrapper from './rack-array-list/styled';

const { Option } = Select;

export function RackColumnsInfo({
  roomInfo,
  spaceGuids,
  loading,
  containerMaxWidth,
  maxHeight,
  rackColumns,
  relateRooms,
  graph,
  resetInfraRoomGraphPreview,
}) {
  const [idc, block, room] = spaceGuids;

  /**
   * @type {React.MutableRefObject<HTMLDivElement}
   */
  const containerRef = useRef(null);

  /**
   * TODO: @Jerry detect user scrolls & programatically scrolls
   */
  const userScrollRef = useRef(true);

  const { search } = useLocation();
  const { activeRoomGrid } = getLocationSearchMap(search, ['activeRoomGrid']);

  const [visibleColumnTags, setVisibleColumnTags] = useState([]);
  const [activeColumnTag, setActiveColumnTag] = useState();
  const [viewTab, setViewTab] = useState('roomView');

  const tabList = rackColumns?.map(({ columnTag }) => ({ key: columnTag, tab: columnTag })) || [];
  const spotlightTarget = useSpotlightTarget();

  const getRackColumn = number => {
    const clsName = getRackColumnClassName(number);
    /** @type {HTMLDivElement} */
    const rackColumn = containerRef.current.querySelector(`.${clsName}`);

    return rackColumn;
  };

  const tabChangeHandler = useCallback(columnTag => {
    userScrollRef.current = false;

    setTimeout(() => {
      const rackColumn = getRackColumn(columnTag);
      const offsetLeft = rackColumn.offsetLeft - 16;
      const scrollLeft = Math.min(Math.max(0, offsetLeft), containerRef.current.scrollWidth);

      containerRef.current.scrollTo({ left: scrollLeft, behavior: 'smooth' });

      setActiveColumnTag(columnTag);
    });
  }, []);

  useEffect(() => {
    return () => {
      resetInfraRoomGraphPreview();
    };
  }, [resetInfraRoomGraphPreview]);

  useEffect(() => {
    setViewTab(graph ? 'roomView' : 'dataView');
  }, [graph]);

  useShallowCompareEffect(() => {
    if (!rackColumns?.length || !containerRef.current) {
      return;
    }
    const rppBtns = rackColumns.map(rackColumn => {
      return containerRef.current.querySelector(`.rpp-${rackColumn.columnTag}`);
    });
    if (rppBtns.some(target => target === null)) {
      return;
    }

    if (activeRoomGrid && rackColumns.some(({ columnTag }) => columnTag === activeRoomGrid)) {
      tabChangeHandler(activeRoomGrid);
    }

    const observer = new IntersectionObserver(
      entries => {
        entries.forEach(entry => {
          const { columnTag } = entry.target.dataset;
          if (entry.intersectionRatio > 0) {
            // console.log('visible...', columnTag);
            setVisibleColumnTags(prev => uniq([...prev, columnTag]));
          } else {
            // console.log('hidden...', columnTag);
            setVisibleColumnTags(prev => without(prev, columnTag));
          }
        });
      },
      {
        root: containerRef.current,
      }
    );
    rppBtns.forEach(target => {
      observer.observe(target);
    });

    return () => {
      if (observer) {
        observer.disconnect();
      }
    };
  }, [rackColumns, activeRoomGrid, tabChangeHandler, viewTab]);

  useEffect(() => {
    const userScroll = userScrollRef.current;

    let timer;
    if (userScroll) {
      timer = window.setTimeout(() => {
        if (!visibleColumnTags.length) {
          return;
        }
        const sorted = visibleColumnTags.sort();
        setActiveColumnTag(sorted[0]);
      }, 500);
    }

    return () => {
      if (userScroll) {
        window.clearTimeout(timer);
      }
    };
  }, [visibleColumnTags]);

  return (
    <Card
      title={
        <Space size="middle">
          <Typography.Text>
            {room} {roomInfo.name}
          </Typography.Text>
          <CamerasLink idc={idc} block={block} room={room} />
          {relateRooms && <RelatedRooms rooms={relateRooms} />}
        </Space>
      }
      extra={
        <Space>
          {tabList.length > 0 && viewTab === 'dataView' && (
            <Select value={activeColumnTag} onChange={tabChangeHandler}>
              {tabList.map(option => (
                <Option key={option.key} value={option.key}>
                  {option.tab}
                </Option>
              ))}
            </Select>
          )}
          {graph && (
            <Radio.Group
              value={viewTab}
              onChange={e => {
                setViewTab(e.target.value);
              }}
            >
              <Radio.Button value="dataView">数据视图</Radio.Button>
              <Radio.Button value="roomView">平面视图</Radio.Button>
            </Radio.Group>
          )}
        </Space>
      }
      bodyStyle={{ height: 'calc(var(--content-height) - 65px)', overflowY: 'auto' }}
    >
      {viewTab === 'roomView' && (
        <InfraRoomGraphPreview
          spaceGuids={[idc, block, room]}
          highlights={spotlightTarget ? [spotlightTarget] : []}
          relateRooms={relateRooms}
          isRacksRoom
        />
      )}
      {viewTab === 'dataView' && (
        <Spin spinning={loading}>
          {(rackColumns?.length || 0) > 0 ? (
            <CabinetArrayWrapper
              ref={containerRef}
              style={{
                maxWidth: containerMaxWidth,
                overflowX: 'auto',
              }}
            >
              {rackColumns.map((rackColumn, idx, thisArray) => (
                <CabinetArrayItem
                  key={rackColumn.columnTag}
                  className={getRackColumnClassName(rackColumn.columnTag)}
                  spaceGuids={spaceGuids}
                  idx={idx}
                  roomGrid={rackColumn.columnTag}
                  rackColumn={rackColumn}
                  height={maxHeight}
                  isFirst={idx === 0}
                  isLast={idx === thisArray.length - 1}
                />
              ))}
            </CabinetArrayWrapper>
          ) : (
            <TinyEmpty />
          )}
        </Spin>
      )}
    </Card>
  );
}

const mapStateToProps = ({
  roomMonitoring: {
    roomInfo,
    loadingCabinetArrayInfo,
    containerMaxWidth,
    maxHeight,
    rackColumns,
    infraGraphPreview: { graph },
  },
}) => ({
  roomInfo,
  loading: loadingCabinetArrayInfo,
  containerMaxWidth,
  maxHeight,
  rackColumns,
  graph,
});

const mapDispatchToProps = {
  resetInfraRoomGraphPreview: roomMonitoringActions.resetInfraRoomGraphPreview,
};
export default connect(mapStateToProps, mapDispatchToProps)(RackColumnsInfo);

/**
 * @param {string} number 机列编号
 * @returns
 */
function getRackColumnClassName(number) {
  return `rack-column_${number}`;
}
