import React from 'react';
import { connect } from 'react-redux';

// import { Cell } from '@manyun/dc-brain.legacy.components/statistics/components';

export function AlarmStatistic({ count }) {
  return <span>{count} 数量</span>;
  // return <Cell code="ALARM_COUNT" text={count} description="数量" />;
}

const mapStateToProps = ({
  roomMonitoring: {
    alarmInfo: { total },
  },
}) => ({
  count: total,
});

export default connect(mapStateToProps)(AlarmStatistic);
