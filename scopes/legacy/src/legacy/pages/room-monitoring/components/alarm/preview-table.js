import React, { useEffect } from 'react';
import { connect } from 'react-redux';
import { useParams } from 'react-router-dom';

import get from 'lodash/get';
import styled from 'styled-components';

import { AlarmTable } from '@manyun/dc-brain.legacy.components';
import {
  cancelGetAlarmsActionCreator,
  getAlarmInfoActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/roomMonitoringActions';

export function AlarmPreviewTable({
  style,
  className,
  loading,
  data,
  total,
  deviceTypeMap,
  getAlarmInfo,
  cancelGetAlarms,
}) {
  const { idc, block, room } = useParams();

  useEffect(() => {
    getAlarmInfo({ idc, block, room, pageNo: 1, pageSize: 2 });

    return () => {
      cancelGetAlarms();
    };
  }, [idc, block, room, getAlarmInfo, cancelGetAlarms]);

  return (
    <AlarmTable
      style={style}
      className={className}
      size="small"
      showColumns={[
        'alarmType',
        'alarmLevel',
        'deviceName',
        'pointCodeName',
        'pointValue',
        'triggerStatus',
        'triggerTime',
      ]}
      onRowShowDetail
      metaCategoryEntities={deviceTypeMap}
      loading={loading}
      dataSource={data}
      pagination={{
        total,
        pageSize: 2,
        onChange: (page, pageSize) => {
          cancelGetAlarms();
          getAlarmInfo({ idc, block, room, pageNo: page, pageSize });
        },
        showSizeChanger: false,
      }}
      scroll={{ x: false }}
    />
  );
}

const StyledAlarmPreviewTable = styled(AlarmPreviewTable)`
  .manyun-table-small {
    border: 0;
  }
`;

const mapStateToProps = ({
  roomMonitoring: { loadingAlarmInfo, alarmInfo },
  common: { deviceCategory },
}) => ({
  loading: loadingAlarmInfo,
  ...alarmInfo,
  deviceTypeMap: get(deviceCategory, 'normalizedList', {}),
});

const mapDispatchToProps = {
  getAlarmInfo: getAlarmInfoActionCreator,
  cancelGetAlarms: cancelGetAlarmsActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(StyledAlarmPreviewTable);
