import DownOutlined from '@ant-design/icons/es/icons/DownOutlined';
import get from 'lodash/get';
import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { Link, useParams } from 'react-router-dom';

import { Dropdown } from '@manyun/base-ui.ui.dropdown';
import { Menu } from '@manyun/base-ui.ui.menu';
import { Radio } from '@manyun/base-ui.ui.radio';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { generateRoomMonitoringUrl } from '@manyun/monitoring.route.monitoring-routes';
import { generateDeviceRecordRoutePath } from '@manyun/resource-hub.route.resource-routes';

import {
  GutterWrapper,
  ModalButton,
  StatusTag,
  StatusText,
} from '@manyun/dc-brain.legacy.components';
import {
  cancelGetBatteryUnitsDataActionCreator,
  cancelGetUpsOrHvdcRealtimeDataActionCreator, // getParentDevicesMonitoringDataActionCreator,
  // getChildBatteryUnitsActionCreator,
  getRelatedDevicesActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/roomMonitoringActions';
import {
  generateGetDeviceAlarmStatus,
  generateGetDeviceMonitoringData,
  getCurrentVisibleDeviceStatus,
} from '@manyun/dc-brain.legacy.utils/device';

import { MODEL_STYLE } from '../constants';
import DurationChart from './chart-modal-button/duration-chart';
import ChildBatteryUnitsTable from './ups-summary/child-battery-units-table';
import { StyledDescriptions } from './ups-summary/styled';

function Summary({
  upses,
  ups,
  data,
  loadRatePointGuid,
  totalOuputPointGuid,
  columns,
  visibleStatus,
  onActiveDeviceGuidChange,
}) {
  const maxOutputLoadRateTxt = get(data, ['maxOutputLoadRate', 'formattedText']);
  const maxOutputLoadRateDisabled = get(data, ['maxOutputLoadRate', 'disabled'], false);
  const totalOuputActivePowerTxt = get(data, ['totalOuputActivePower', 'formattedText']);
  const totalOuputActivePowerDisabled = get(data, ['totalOuputActivePower', 'disabled'], false);
  const powerSupplyMode = get(data, ['powerSupplyMode', 'value'], {});
  const inputOnOffStatus = get(data, ['inputOnOffStatus', 'value'], {});

  return (
    <StyledDescriptions bordered colon={false} column={2}>
      <StyledDescriptions.Item label="UPS名称">
        {Array.isArray(upses) && upses.length > 1 ? (
          <Dropdown
            overlay={
              <Menu>
                {upses.map(({ deviceGuid, deviceName }) => (
                  <Menu.Item key={deviceGuid}>
                    <span
                      onClick={evt => {
                        evt.preventDefault();
                        onActiveDeviceGuidChange(deviceGuid);
                      }}
                    >
                      {deviceName}
                    </span>
                  </Menu.Item>
                ))}
              </Menu>
            }
          >
            <span
              onClick={e => {
                e.preventDefault();
              }}
            >
              {ups?.deviceName} <DownOutlined />
            </span>
          </Dropdown>
        ) : (
          ups?.deviceName
        )}
      </StyledDescriptions.Item>
      <StyledDescriptions.Item label="供电模式">
        {data.powerSupplyMode.disabled ? (
          powerSupplyMode.NAME
        ) : (
          <StatusText background status={powerSupplyMode.STATUS_CODE}>
            {powerSupplyMode.NAME}
          </StatusText>
        )}
      </StyledDescriptions.Item>
      <StyledDescriptions.Item label="交流输入状态">
        {data.inputOnOffStatus.disabled ? (
          inputOnOffStatus.NAME
        ) : (
          <StatusText background status={inputOnOffStatus.STATUS_CODE}>
            {inputOnOffStatus.NAME}
          </StatusText>
        )}
      </StyledDescriptions.Item>
      <StyledDescriptions.Item label="电池工作状态">
        {get(data, ['batteryChargingStatus', 'value', 'NAME'])}
      </StyledDescriptions.Item>
      <StyledDescriptions.Item label="输出负载率">
        {maxOutputLoadRateDisabled ? (
          maxOutputLoadRateTxt
        ) : (
          <ModalButton
            compact
            autoHeight
            type="link"
            text={maxOutputLoadRateTxt}
            title={ups?.deviceName}
            modalStyle={MODEL_STYLE}
            footer={null}
            destroyOnClose
          >
            {visible => (
              <DurationChart
                visible={visible}
                deviceType={ups.deviceType}
                pointGuid={loadRatePointGuid}
                echartTitle="输出负载率"
              />
            )}
          </ModalButton>
        )}
      </StyledDescriptions.Item>
      <StyledDescriptions.Item label="故障状态">
        <StatusTag status={visibleStatus.theme}>{visibleStatus.text}</StatusTag>
      </StyledDescriptions.Item>
      <StyledDescriptions.Item span={2} label="输出总有功功率">
        {totalOuputActivePowerDisabled ? (
          totalOuputActivePowerTxt
        ) : (
          <ModalButton
            compact
            autoHeight
            type="link"
            text={totalOuputActivePowerTxt}
            title={ups && ups.deviceName}
            modalStyle={MODEL_STYLE}
            footer={null}
            destroyOnClose
          >
            {visible => (
              <DurationChart
                visible={visible}
                deviceType={ups.deviceType}
                pointGuid={totalOuputPointGuid}
                echartTitle="输出总有功功率"
              />
            )}
          </ModalButton>
        )}
      </StyledDescriptions.Item>
      <StyledDescriptions.Item span={2} label="对应机列">
        <GutterWrapper size="4px">
          {columns.map(column => {
            return (
              <Link
                key={column.idc + column.block + column.room + column.column}
                rel="noopener noreferrer"
                target="_blank"
                onClick={() => {
                  window.open(
                    generateRoomMonitoringUrl({
                      idc: column.idc,
                      block: column.block,
                      room: column.room,
                    })
                  );
                }}
              >
                {column.room} {column.column}列
              </Link>
            );
          })}
        </GutterWrapper>
      </StyledDescriptions.Item>
    </StyledDescriptions>
  );
}

const mapStateToProps = (
  {
    config: { configMap },
    'monitoring.subscriptions': { devicesRealtimeData, devicesAlarmsData },
    roomMonitoring: { childDeviceMap, deviceInfoMap },
  },
  { ups }
) => {
  if (!(ups && ups.deviceGuid)) {
    return { columns: [] };
  }

  const { pointsDefinitionMap, concretePointCodeMappings } = configMap.current;
  const configUtil = new ConfigUtil(configMap.current);
  const rppTypes = configUtil.getAllDeviceTypes(ConfigUtil.constants.deviceTypes.RPP) || [];
  const childDevices = get(childDeviceMap, ups.deviceGuid, []);
  const columns = []; // 机列
  childDevices.forEach(({ deviceGuid, deviceType, spaceGuid, extendPosition }) => {
    if (!rppTypes.includes(deviceType)) {
      return;
    }
    if (!spaceGuid) {
      console.error(`Device(${deviceGuid}) spaceGuid(${spaceGuid}) expected!`);
      return;
    }
    if (!extendPosition) {
      console.error(`Device(${deviceGuid}) extendPosition(${extendPosition}) expected!`);
      return;
    }
    const [idc, block, room] = spaceGuid.split('.');
    const [column] = extendPosition.split('.');
    if (
      !columns.some(
        g => g.idc === idc && g.block === block && g.room === room && g.column === column
      )
    ) {
      columns.push({ idc, block, room, column });
    }
  });

  const getData = generateGetDeviceMonitoringData(
    devicesRealtimeData,
    devicesAlarmsData,
    pointsDefinitionMap,
    concretePointCodeMappings
  );
  const getAlarmStatus = generateGetDeviceAlarmStatus(devicesAlarmsData);

  const powerSupplyMode = getData(ups, {
    pointType: ConfigUtil.constants.pointCodes.UPS_POWER_SUPPLY_MODE,
    reflected: true,
  });
  const inputOnOffStatus = getData(ups, {
    pointType: ConfigUtil.constants.pointCodes.UPS_INPUT_ON_OFF_STATE,
    reflected: true,
  });
  const batteryChargingStatus = getData(ups, {
    pointType: ConfigUtil.constants.pointCodes.UPS_BATTERY_CHARGING_STATE,
    reflected: true,
  });
  const maxOutputLoadRate = getData(ups, {
    pointType: ConfigUtil.constants.pointCodes.UPS_OUTPUT_LOAD_RATE_MAX,
    formatted: true,
  });
  const loadRatePointGuid = {
    deviceGuid: ups.deviceGuid,
    pointCode: [maxOutputLoadRate.pointCode],
  };
  const totalOuputActivePower = getData(ups, {
    pointType: ConfigUtil.constants.pointCodes.UPS_ACTIVE_POWER_OUTPUT_SUM,
    formatted: true,
  });
  const totalOuputPointGuid = {
    deviceGuid: ups.deviceGuid,
    pointCode: [totalOuputActivePower.pointCode],
  };

  const data = {
    powerSupplyMode,
    inputOnOffStatus,
    batteryChargingStatus,
    maxOutputLoadRate,
    totalOuputActivePower,
  };

  const alarmStatus = getAlarmStatus(ups.deviceGuid);
  const deviceInfo = get(deviceInfoMap, ups.deviceGuid, {});
  const visibleStatus = getCurrentVisibleDeviceStatus(deviceInfo, alarmStatus);

  return { data, loadRatePointGuid, totalOuputPointGuid, columns, visibleStatus };
};

const ConnectedSummary = connect(mapStateToProps)(Summary);

function UpsSelect({
  branchADisabled,
  branchBDisabled,
  branch,
  upses,
  ups,
  deviceGuid,
  setBranch,
  onActiveDeviceGuidChange,
  getRelatedDevices,
  cancelGetUpsOrHvdcRealtimeData,
  cancelGetBatteryUnitsData,
  // getDeviceMonitoringData,
  // getChildBatteryUnits,
}) {
  const { idc, block } = useParams();

  useEffect(() => {
    if (!deviceGuid) {
      return;
    }
    cancelGetUpsOrHvdcRealtimeData();
    cancelGetBatteryUnitsData();
    // getDeviceMonitoringData({ idc, block, deviceGuid });
    getRelatedDevices({ idc, block, deviceGuid });
    // getChildBatteryUnits({ idc, block, deviceGuid });
  }, [
    idc,
    block,
    deviceGuid,
    getRelatedDevices,
    cancelGetUpsOrHvdcRealtimeData,
    cancelGetBatteryUnitsData,
  ]);

  return (
    <GutterWrapper mode="vertical">
      <Radio.Group
        size="small"
        buttonStyle="solid"
        defaultValue={branch}
        onChange={({ target: { value } }) => {
          setBranch(value);
        }}
      >
        <Radio.Button disabled={branchADisabled} value="A">
          A路
        </Radio.Button>
        <Radio.Button disabled={branchBDisabled} value="B">
          B路
        </Radio.Button>
      </Radio.Group>
      <div>
        <ConnectedSummary
          upses={upses}
          ups={ups}
          onActiveDeviceGuidChange={onActiveDeviceGuidChange}
        />
        <ChildBatteryUnitsTable ups={ups} />
      </div>
    </GutterWrapper>
  );
}

const mapDispatchToProps = {
  // getDeviceMonitoringData: getParentDevicesMonitoringDataActionCreator,
  getRelatedDevices: getRelatedDevicesActionCreator,
  cancelGetUpsOrHvdcRealtimeData: cancelGetUpsOrHvdcRealtimeDataActionCreator,
  cancelGetBatteryUnitsData: cancelGetBatteryUnitsDataActionCreator,
  // getChildBatteryUnits: getChildBatteryUnitsActionCreator,
};

const ConnectedUpsSelect = connect(null, mapDispatchToProps)(UpsSelect);

export function UpsSummary({
  upsesA,
  upsesB,
  cancelGetBatteryUnitsData,
  cancelGetUpsOrHvdcRealtimeData,
}) {
  const branchADisabled = !(Array.isArray(upsesA) && upsesA.length > 0);
  const branchBDisabled = !(Array.isArray(upsesB) && upsesB.length > 0);
  const defaultBranch = branchADisabled ? 'B' : 'A';

  const [branch, setBranch] = useState(defaultBranch);
  const [deviceGuid, setDeviceGuid] = useState();

  function selectUpses(branch) {
    return branch === 'A' ? upsesA : upsesB;
  }

  const upses = selectUpses(branch);
  const ups = upses.find(ups => ups.deviceGuid === deviceGuid);

  useEffect(() => {
    if (!(Array.isArray(upses) && upses.length)) {
      return;
    }
    setDeviceGuid(upses[0].deviceGuid);
    // set initial active device guid
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  function branchChangeHandler(branch) {
    setBranch(branch);
    const upses = selectUpses(branch);
    if (!(Array.isArray(upses) && upses.length)) {
      return;
    }
    setDeviceGuid(upses[0].deviceGuid);
  }

  function closeModalHandler() {
    cancelGetUpsOrHvdcRealtimeData();
    cancelGetBatteryUnitsData();
  }

  const title = (
    <GutterWrapper>
      <span>UPS运行参数</span>
      {ups && (
        <Link style={{ fontSize: 14 }} to={generateDeviceRecordRoutePath({ guid: ups.deviceGuid })}>
          档案与参数
        </Link>
      )}
    </GutterWrapper>
  );

  return (
    <ModalButton
      compact
      footer={null}
      type="link"
      text="UPS运行参数"
      title={title}
      width={744}
      onCancel={closeModalHandler}
    >
      <ConnectedUpsSelect
        branchADisabled={branchADisabled}
        branchBDisabled={branchBDisabled}
        branch={branch}
        upses={upses}
        ups={ups}
        deviceGuid={deviceGuid}
        setBranch={branchChangeHandler}
        onActiveDeviceGuidChange={setDeviceGuid}
      />
    </ModalButton>
  );
}

export default connect(null, {
  cancelGetBatteryUnitsData: cancelGetBatteryUnitsDataActionCreator,
  cancelGetUpsOrHvdcRealtimeData: cancelGetUpsOrHvdcRealtimeDataActionCreator,
})(UpsSummary);
