import React from 'react';
import { useParams } from 'react-router-dom';

//页面已不使用 但有引用
function RoomMonitoring({ roomType, getRoomInfo, cancelGetMonitoringData }) {
  const { idc, block, room: urlRoom } = useParams();
  const room = decodeURIComponent(urlRoom);

  const spaceGuids = React.useMemo(() => [idc, block, room], [block, idc, room]);

  return spaceGuids.join('.');
}

export default RoomMonitoring;
