export const AISLE_TYPE_MAP = {
  COLD: 'cold', // 冷通道
  HOT: 'hot', // 热通道
};

export const MODEL_STYLE = { minWidth: (1200 / 1600) * 100 + '%', maxWidth: 1200 };

export const TH_HEIGHT = 40; // px

export const TR_HEIGHT = 41; // px

export const AISLE_WIDTH = 150; // px

export const SENSOR_SIZE = 64; // px

export const RACKS_COLUMN_GAP = 0; // px

export const RACKS_COLUMN_WIDTH = 330 + /* 左右两侧的 border width 各占 1px */ 2; // px
