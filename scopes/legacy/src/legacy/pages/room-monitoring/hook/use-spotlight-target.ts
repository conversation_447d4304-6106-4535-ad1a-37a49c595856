import { useLocation } from 'react-router-dom';

import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';

export function useSpotlightTarget(): string | null {
  const { search } = useLocation();
  const { spotlightTarget } = getLocationSearchMap<{ spotlightTarget?: string }>(search);

  if (spotlightTarget === undefined || spotlightTarget === '') {
    return null;
  }

  return spotlightTarget;
}
