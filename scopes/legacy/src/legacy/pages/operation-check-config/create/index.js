import React, { Component } from 'react';
import { connect } from 'react-redux';
import { <PERSON> } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';

import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Wrapper, TinyCard } from '@manyun/dc-brain.legacy.components';
import { YES_OR_NO_KEY_MAP } from '@manyun/dc-brain.legacy.constants';
import {
  createConfigActionCreator,
  editConfigActionCreator,
  getConfigInfoActionCreator,
  operationCheckConfigActions,
} from '@manyun/dc-brain.legacy.redux/actions/operationCheckConfigActions';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';

import BasicInfo from './components/basicInfo';
import CheckItems from './components/checkItems';
import styles from './styles.module.less';

function throws(error) {
  throw new Error(error);
}

class ConfigCreate extends Component {
  state = {
    editingRowKeyInBefore: null,
    editingRowKeyInAfter: null,
  };

  basicInfoFormRef = React.createRef();

  componentDidMount() {
    if (this.props.mode === 'edit') {
      this.props.getConfigInfo({ configId: this.props.match.params.id });
    }
  }

  handleSubmit = async () => {
    try {
      await this.basicInfoFormRef.current.props.form.validateFields().catch(throws);
      const { editingRowKeyInBefore, editingRowKeyInAfter } = this.state;
      if (!editingRowKeyInBefore && !editingRowKeyInAfter) {
        // 两个表格中至少有一条数据
        if (
          !this.props.formOptions.itemsInBefore.length &&
          !this.props.formOptions.itemsInAfter.length
        ) {
          message.error('至少添加一条操作前检查项或者一条操作后检查项！');
          return;
        }
        const { mode, formOptions } = this.props;
        const params = {
          taskType: formOptions.taskType.value[0],
          taskSubType: formOptions.taskType.value[1],
          checkItems: [...formOptions.itemsInBefore, ...formOptions.itemsInAfter].map(item => {
            return {
              dataRange: item.dataRange,
              inputData: item.inputData === YES_OR_NO_KEY_MAP.YES ? true : false,
              itemName: item.itemName ? item.itemName.trim() : null,
              itemMethod: item.itemMethod ? item.itemMethod.trim() : null,
              itemNormal: item.itemNormal ? item.itemNormal.trim() : null,
              itemType: item.itemType,
            };
          }),
          configName: formOptions.configName.value ? formOptions.configName.value.trim() : null,
        };
        if (mode === 'new') {
          this.props.create(params);
        } else {
          this.props.edit({ ...params, id: formOptions.id });
        }
      } else {
        message.error('您还有未保存的检查项，请先保存检查项再提交');
      }
    } catch (error) {}
  };

  render() {
    const { formOptions, mode } = this.props;
    const { editingRowKeyInBefore, editingRowKeyInAfter } = this.state;

    return (
      <GutterWrapper mode="vertical" style={{ paddingBottom: '40px' }}>
        <TinyCard bordered={false}>
          <BasicInfo
            mode={mode}
            formOptions={formOptions}
            wrappedComponentRef={this.basicInfoFormRef}
          />
          <CheckItems
            title="操作前检查项"
            itemType="CHECK_BEFORE"
            showActionsColumn={true}
            tableClassName={mode === 'edit' ? styles.checkItemsTable : undefined} // 编辑时隐藏编辑按钮
            defaultData={formOptions.itemsInBefore}
            editingRowKey={editingRowKeyInBefore}
            setEditingRowKey={value => this.setState({ editingRowKeyInBefore: value })}
            setData={data => {
              const options = { ...formOptions, itemsInBefore: data };
              if (mode === 'new') {
                this.props.upDateCreateOptionValues(options);
              } else {
                this.props.upDateEditOptionValues(options);
              }
            }}
            mode={mode}
          />
          <CheckItems
            title="操作后检查项"
            itemType="CHECK_AFTER"
            showActionsColumn={true}
            tableClassName={mode === 'edit' ? styles.checkItemsTable : undefined} // 编辑时隐藏编辑按钮
            defaultData={formOptions.itemsInAfter}
            editingRowKey={editingRowKeyInAfter}
            setEditingRowKey={value => this.setState({ editingRowKeyInAfter: value })}
            setData={data => {
              const options = { ...formOptions, itemsInAfter: data };
              if (mode === 'new') {
                this.props.upDateCreateOptionValues(options);
              } else {
                this.props.upDateEditOptionValues(options);
              }
            }}
            mode={mode}
          />
        </TinyCard>
        <FooterToolBar>
          <GutterWrapper>
            <Button type="primary" onClick={this.handleSubmit}>
              提交
            </Button>
            <Link to={urls.generateOperationCheckConfigListLocation()}>
              <Button>取消</Button>
            </Link>
          </GutterWrapper>
        </FooterToolBar>
      </GutterWrapper>
    );
  }
}

const mapStateToProps = (
  { operationCheckConfig: { create, edit }, common: { ticketTypes } },
  { mode }
) => {
  let formOptions = create;
  if (mode === 'edit') {
    formOptions = edit;
  }
  return {
    formOptions,
    ticketTypes,
  };
};
const mapDispatchToProps = {
  upDateCreateOptionValues: operationCheckConfigActions.upDateCreateOptionValues,
  upDateEditOptionValues: operationCheckConfigActions.upDateEditOptionValues,
  getConfigInfo: getConfigInfoActionCreator,
  create: createConfigActionCreator,
  edit: editConfigActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(ConfigCreate);
