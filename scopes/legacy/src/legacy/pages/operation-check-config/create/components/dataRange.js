import React, { Component } from 'react';

import Form from '@ant-design/compatible/es/form';

import { InputNumber } from '@manyun/base-ui.ui.input-number';

class DataRange extends Component {
  render() {
    const {
      form: { getFieldDecorator, getFieldsValue },
      value,
      size,
      itemType,
    } = this.props;
    const [upper, lower] = value ? value.split('$$') : ['', ''];
    return (
      <Form layout="inline">
        <Form.Item label="下限">
          {getFieldDecorator(`${itemType}_$$_lower`, {
            initialValue: lower === 'null' ? '' : lower,
          })(
            <InputNumber
              style={{ width: 100 }}
              onChange={value => {
                let tmp = '';
                if ((!Number(value) && value !== 0) || value === '') {
                  if (Number(getFieldsValue()[`${itemType}_$$_upper`]) >= 0) {
                    tmp = `${getFieldsValue()[`${itemType}_$$_upper`]}$$`;
                  } else {
                    tmp = '';
                  }
                } else {
                  if (Number(getFieldsValue()[`${itemType}_$$_upper`]) >= 0) {
                    tmp = `${getFieldsValue()[`${itemType}_$$_upper`]}$$${value}`;
                  } else {
                    tmp = `$$${value}`;
                  }
                }
                this.props.onChange(tmp);
              }}
              min={0}
              max={999999}
              precision={4}
              size={size}
            />
          )}
        </Form.Item>
        <Form.Item label="上限">
          {getFieldDecorator(`${itemType}_$$_upper`, {
            initialValue: upper === 'null' ? '' : upper,
          })(
            <InputNumber
              style={{ width: 100 }}
              onChange={value => {
                let tmp = '';
                if ((!Number(value) && value !== 0) || value === '') {
                  if (Number(getFieldsValue()[`${itemType}_$$_lower`]) >= 0) {
                    tmp = `$$${getFieldsValue()[`${itemType}_$$_lower`]}`;
                  } else {
                    tmp = '';
                  }
                } else {
                  if (Number(getFieldsValue()[`${itemType}_$$_lower`]) >= 0) {
                    tmp = `${value}$$${getFieldsValue()[`${itemType}_$$_lower`]}`;
                  } else {
                    tmp = `${value}$$`;
                  }
                }
                this.props.onChange(tmp);
              }}
              min={0}
              max={999999}
              precision={4}
              size={size}
            />
          )}
        </Form.Item>
      </Form>
    );
  }
}

export default Form.create({ name: 'dataRange' })(DataRange);
