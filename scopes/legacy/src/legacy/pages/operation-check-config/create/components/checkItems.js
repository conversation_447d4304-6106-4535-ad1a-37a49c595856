import React, { Component } from 'react';

import PlusCircleFilled from '@ant-design/icons/es/icons/PlusCircleFilled';
import { EditableTable, Form } from '@galiojs/awesome-antd';
import { generateGetRowSpan } from '@galiojs/awesome-antd/lib/table/utils';
import cloneDeep from 'lodash.clonedeep';
import shortid from 'shortid';

import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Radio } from '@manyun/base-ui.ui.radio';

import { GutterWrapper, TinyCard } from '@manyun/dc-brain.legacy.components';
import {
  YES_OR_NO_KEY_MAP,
  YES_OR_NO_OPTIONS,
  YES_OR_NO_TEXT_MAP,
} from '@manyun/dc-brain.legacy.constants';

import DataRange from './dataRange';

class CheckItemsLegacy extends Component {
  state = {
    deleteByCancel: false,
    initialSaved: this.props.editingRowKey,
  };

  addRow = (rowData, insertIdx) => {
    const { defaultData } = this.props;
    const nextData = [...defaultData];
    nextData.splice(insertIdx, 0, rowData);
    this.props.setData(nextData);
    this.setState({ deleteByCancel: true });
    this.props.setEditingRowKey(rowData.key);
  };

  render() {
    const {
      form,
      title,
      defaultData,
      editingRowKey,
      itemType,
      showActionsColumn,
      mode,
      tableClassName,
    } = this.props;
    const { deleteByCancel, initialSaved } = this.state;
    return (
      <TinyCard title={title} bordered={false}>
        <GutterWrapper mode="vertical">
          {mode !== 'detail' && (
            <Button
              type="primary"
              disabled={editingRowKey ? true : false}
              onClick={() => {
                const key = shortid();
                this.addRow({
                  key,
                  mergeRowsKey: key,
                  itemType: itemType,
                  itemName: '',
                  itemMethod: '',
                  itemNormal: '',
                  inputData: YES_OR_NO_KEY_MAP.YES,
                  dataRange: '',
                  [`${itemType}_$$_itemName`]: '',
                  [`${itemType}_$$_inputData`]: YES_OR_NO_KEY_MAP.YES,
                  [`${itemType}_$$_dataRange`]: '',
                  [`${itemType}_$$_itemMethod`]: '',
                  [`${itemType}_$$_itemNormal`]: '',
                });
              }}
            >
              添加操作{itemType === 'CHECK_BEFORE' ? '前' : '后'}检查项
            </Button>
          )}
          <EditableTable
            form={form}
            rowKey="key"
            mergeProp="mergeRowsKey"
            showActionsColumn={showActionsColumn}
            dataSource={defaultData}
            columns={getColumns({
              addRow: this.addRow,
              editingRowKey: editingRowKey,
              itemType: itemType,
              getRowSpan: generateGetRowSpan(defaultData),
              showActionsColumn,
            })}
            editingRowKey={editingRowKey}
            onSave={(currentKey, records) => {
              const newRecords = getNewItemKey(records, itemType);
              const currentRecords = newRecords.filter(({ key }) => key === currentKey);
              const smmeNameDate = defaultData.filter(({ itemName, mergeRowsKey }) => {
                if (
                  itemName === currentRecords[0].itemName &&
                  mergeRowsKey !== currentRecords[0].mergeRowsKey
                ) {
                  return true;
                }
                return false;
              });
              // 判断检查项名称、检查方法、检查标准中有无空字符串
              let tmp = false;
              newRecords.forEach(item => {
                if (
                  item[`${itemType}_$$_itemName`].trim() === '' ||
                  item[`${itemType}_$$_itemMethod`].trim() === '' ||
                  item[`${itemType}_$$_itemNormal`].trim() === ''
                ) {
                  tmp = true;
                }
              });
              if (smmeNameDate.length) {
                message.error('检查项名称已经存在!');
              } else if (tmp) {
                message.error('检查项名称、检查方法、检查标准均不可以为空字符串！');
              } else {
                this.props.setData(newRecords);
                this.setState({ deleteByCancel: false, initialSaved: false });
                this.props.setEditingRowKey(null);
              }
            }}
            onEdit={rowKey => {
              this.props.setEditingRowKey(rowKey);
            }}
            onCancel={() => {
              if (deleteByCancel) {
                const nextData = cloneDeep(defaultData);
                const idx = nextData.findIndex(record => record.key === editingRowKey);
                if (idx > -1) {
                  nextData.splice(idx, 1);
                }
                this.props.setData(nextData);
                this.props.setEditingRowKey(null);
                this.setState({ deleteByCancel: false });
              }
              if (!initialSaved) {
                this.props.setEditingRowKey(null);
              } else {
                message.error('当前数据还未保存，不可以直接取消！');
              }
            }}
            onDelete={(rowKey, data) => {
              this.props.setData(data);
              if (rowKey === editingRowKey) {
                this.props.setEditingRowKey(null);
                this.setState({ deleteByCancel: false });
              }
            }}
            className={tableClassName}
          />
        </GutterWrapper>
      </TinyCard>
    );
  }
}

function CheckItems(props) {
  const [form] = Form.useForm();

  return <CheckItemsLegacy form={form} {...props} />;
}

export default CheckItems;

const getColumns = ({ addRow, editingRowKey, itemType, getRowSpan, showActionsColumn }) => [
  {
    title: '检查项名称',
    dataIndex: 'itemName',
    editable: true,
    mergeStrategy: {
      rows: true,
    },
    editingId: `${itemType}_$$_itemName`,
    editingCtrl: <Input allowClear style={{ width: 200 }} />,
    formItemProps: {
      rules: [
        { required: true, whitespace: true, message: '检查项名称必填！' },
        {
          max: 15,
          message: '最多输入 15 个字符！',
        },
      ],
    },
    render(name, record, idx) {
      return {
        children: name,
        props: {
          rowSpan: getRowSpan(record, idx),
        },
      };
    },
  },
  {
    title: '检查方法',
    dataIndex: 'itemMethod',
    editable: true,
    editingId: `${itemType}_$$_itemMethod`,
    editingCtrl: <Input allowClear style={{ width: 200 }} />,
    formItemProps: {
      rules: [
        { required: true, whitespace: true, message: '检查方法必填！' },
        {
          max: 20,
          message: '最多输入 20 个字符！',
        },
      ],
    },
    render(itemMethod, record, idx) {
      const { mergeRowsKey, itemName } = record;
      if (editingRowKey || !itemName) {
        return itemMethod;
      }
      const rowSpan = getRowSpan(record, idx);
      if (rowSpan >= 1 && showActionsColumn && itemMethod) {
        return (
          <span>
            <PlusCircleFilled
              style={{ marginRight: 8, color: 'var(--color-blue-7)' }}
              onClick={() => {
                addRow(
                  {
                    key: shortid(),
                    mergeRowsKey,
                    itemName,
                    itemType,
                    itemNormal: '',
                    itemMethod: '',
                    inputData: YES_OR_NO_KEY_MAP.YES,
                    [`${itemType}_$$_itemName`]: itemName,
                    [`${itemType}_$$_inputData`]: YES_OR_NO_KEY_MAP.YES,
                    [`${itemType}_$$_dataRange`]: '',
                    [`${itemType}_$$_itemMethod`]: '',
                    [`${itemType}_$$_itemNormal`]: '',
                  },
                  idx
                );
              }}
            />
            {itemMethod}
          </span>
        );
      }
      return itemMethod;
    },
  },
  {
    title: '检查标准',
    dataIndex: 'itemNormal',
    editable: true,
    editingId: `${itemType}_$$_itemNormal`,
    editingCtrl: <Input allowClear style={{ width: 200 }} />,
    formItemProps: {
      rules: [
        { required: true, whitespace: true, message: '检查方法必填！' },
        {
          max: 50,
          message: '最多输入 50 个字符！',
        },
      ],
    },
  },
  {
    title: '是否录入数据',
    dataIndex: 'inputData',
    editable: true,
    editingId: `${itemType}_$$_inputData`,
    editingCtrl: (
      <Radio.Group>
        {YES_OR_NO_OPTIONS.map(({ label, value }) => {
          return (
            <Radio value={value} key={value}>
              {label}
            </Radio>
          );
        })}
      </Radio.Group>
    ),
    formItemProps: {
      rules: [
        {
          validator: (_rule, inputData) => {
            if (inputData !== YES_OR_NO_KEY_MAP.NO && inputData !== YES_OR_NO_KEY_MAP.YES) {
              return Promise.reject('是否录入数据必选！');
            }
            return Promise.resolve();
          },
        },
      ],
    },
    render: inputData => {
      if (inputData === YES_OR_NO_KEY_MAP.NO) {
        return YES_OR_NO_TEXT_MAP.NO;
      }
      if (inputData === YES_OR_NO_KEY_MAP.YES) {
        return YES_OR_NO_TEXT_MAP.YES;
      }
      return '';
    },
  },
  {
    title: '录入数据校验范围',
    dataIndex: 'dataRange',
    width: 400,
    editable: true,
    editingId: `${itemType}_$$_dataRange`,
    editingCtrl: (_fieldsValue, { form }) => {
      const inputData = form.getFieldValue(`${itemType}_$$_inputData`);
      if (!inputData) {
        return '--';
      }
      return <DataRange itemType={itemType} />;
    },
    formItemProps: {
      dependencies: [`${itemType}_$$_inputData`],
      rules: [
        form => ({
          validator: (_rule, dataRange) => dataRangeValidator(_rule, dataRange, form, itemType),
        }),
      ],
    },
    render: (dataRange, record) => {
      if (record.inputData === YES_OR_NO_KEY_MAP.NO || !record.inputData || !dataRange) {
        return '否';
      }
      const [upper, lower] = dataRange ? dataRange.split('$$') : ['', ''];
      let children = `${lower !== 'null' ? lower : ''} - ${upper !== 'null' ? upper : ''}`;
      return children;
    },
  },
];

const dataRangeValidator = (_rule, dataRange, form, itemType) => {
  const [upper, lower] = dataRange.split('$$');
  const inputData = form.getFieldValue(`${itemType}_$$_inputData`);
  if (
    inputData &&
    inputData === YES_OR_NO_KEY_MAP.YES &&
    ((!dataRange && dataRange !== 0) || dataRange === '$$')
  ) {
    return Promise.reject('数据校验范围为必填项');
  } else if (upper === lower || Number(upper) < Number(lower)) {
    return Promise.reject('下限要小于上限！');
  }

  return Promise.resolve();
};

const getNewItemKey = (data, itemType) => {
  const newData = data.map(item => {
    return {
      ...item,
      itemName: item[`${itemType}_$$_itemName`],
      itemMethod: item[`${itemType}_$$_itemMethod`],
      itemNormal: item[`${itemType}_$$_itemNormal`],
      inputData: item[`${itemType}_$$_inputData`],
      dataRange: item[`${itemType}_$$_dataRange`],
    };
  });
  return newData;
};
