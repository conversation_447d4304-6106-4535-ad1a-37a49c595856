import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';

import { Cascader } from '@manyun/base-ui.ui.cascader';
import { Input } from '@manyun/base-ui.ui.input';

import { TinyCard } from '@manyun/dc-brain.legacy.components';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import { operationCheckConfigActions } from '@manyun/dc-brain.legacy.redux/actions/operationCheckConfigActions';

class BasicInfo extends Component {
  componentDidMount() {
    this.props.syncCommonData({ strategy: { ticketTypes: 'IF_NULL' } });
  }

  getCascaderData = data => {
    // 帅选上下电类型
    const newData = data
      .filter(({ taskType }) => taskType === 'POWER')
      .map(item => {
        if (!item.children || !item.children.length) {
          return {
            ...item,
            disabled: true,
          };
        }
        // 过滤测试电转正式电
        const filterPowerTestToNo = item.children.filter(
          ({ taskType }) => taskType !== 'POWER_TEST_TO_ON'
        );
        return {
          ...item,
          children: filterPowerTestToNo,
        };
      });
    return newData;
  };

  render() {
    const {
      form: { getFieldDecorator },
      ticketTypes,
      mode,
    } = this.props;
    return (
      <TinyCard title="基本信息" bordered={false}>
        <Form labelCol={{ xl: 3 }} wrapperCol={{ xl: 21 }} style={{ width: 848 }} colon={false}>
          <Form.Item label="工单类型">
            {getFieldDecorator('taskType', {
              rules: [
                {
                  required: true,
                  type: 'number',
                  transform: vaue => (vaue[1] ? 2 : undefined),
                  message: '工单类型为必选项！',
                },
              ],
            })(
              <Cascader
                options={ticketTypes ? this.getCascaderData(ticketTypes.treeList) : []}
                fieldNames={{ value: 'taskType', children: 'children', label: 'taskValue' }}
                style={{ width: 200 }}
                disabled={mode === 'edit'}
              />
            )}
          </Form.Item>
          <Form.Item label="检查配置名称">
            {getFieldDecorator('configName', {
              rules: [
                { required: true, whitespace: true, message: '检查配置名称必填项！' },
                {
                  max: 20,
                  message: '最多输入 20 个字符！',
                },
              ],
            })(<Input allowClear style={{ width: 468 }} />)}
          </Form.Item>
        </Form>
      </TinyCard>
    );
  }
}

const mapStateToProps = ({ common: { ticketTypes } }) => {
  return {
    ticketTypes,
  };
};
const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
  upDateCreateOptionValues: operationCheckConfigActions.upDateCreateOptionValues,
  upDateEditOptionValues: operationCheckConfigActions.upDateEditOptionValues,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(
  Form.create({
    onFieldsChange(props, changedFields) {
      let fileds = changedFields;
      if (props.mode === 'new') {
        if (
          changedFields.taskType &&
          changedFields.taskType.value &&
          changedFields.taskType.value.length
        ) {
          const code = changedFields.taskType.value[1];
          const taskValue =
            props.ticketTypes &&
            props.ticketTypes.normalizedList &&
            props.ticketTypes.normalizedList[code]
              ? props.ticketTypes.normalizedList[code].taskValue
              : code;
          const configName = `${taskValue}-操作检查配置`;
          fileds = {
            ...fileds,
            configName: {
              value: configName,
              name: 'configName',
            },
          };
        }
        props.upDateCreateOptionValues(fileds);
      } else {
        props.upDateEditOptionValues(fileds);
      }
    },
    mapPropsToFields(props) {
      return {
        configName: Form.createFormField(props.formOptions.configName),
        taskType: Form.createFormField(props.formOptions.taskType),
      };
    },
  })(BasicInfo)
);
