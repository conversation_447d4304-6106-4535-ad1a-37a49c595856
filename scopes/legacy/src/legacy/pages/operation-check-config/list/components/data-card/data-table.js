import React, { useCallback, useEffect } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Button as BaseButton } from '@manyun/base-ui.ui.button';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Divider } from '@manyun/base-ui.ui.divider';

import { Ellipsis, TinyTable, UserLink } from '@manyun/dc-brain.legacy.components';
import {
  deleteConfigActionCreator,
  getDataActionCreator,
  setPaginationThenGetDataActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/operationCheckConfigActions';
import * as generateUrls from '@manyun/dc-brain.legacy.utils/urls';

export function DataTable({
  data,
  total,
  pageNum,
  pageSize,
  getData,
  setPagination,
  deleteConfig,
  ticketTypes,
  loading,
}) {
  useEffect(() => {
    getData();
  }, [getData]);

  const paginationChangeHandler = useCallback(
    (current, size) => {
      setPagination({ pageNum: current, pageSize: size });
    },
    [setPagination]
  );

  return (
    <TinyTable
      rowKey="id"
      loading={loading}
      columns={getColumns({ deleteConfig, ticketTypes })}
      align={'left'}
      dataSource={data}
      actions={[
        <Button
          key="create"
          type="primary"
          href={generateUrls.generateOperationCheckConfigCreateLocation()}
        >
          新建操作检查配置
        </Button>,
      ]}
      pagination={{
        total,
        current: pageNum,
        pageSize,
        onChange: paginationChangeHandler,
      }}
    />
  );
}

const mapStateToProps = ({
  operationCheckConfig: {
    data,
    total,
    pagination: { pageNum, pageSize },
    loading,
  },
  common: { ticketTypes },
}) => {
  return {
    data,
    total,
    pageNum,
    pageSize,
    ticketTypes,
    loading,
  };
};
const mapDispatchToProps = {
  getData: getDataActionCreator,
  setPagination: setPaginationThenGetDataActionCreator,
  deleteConfig: deleteConfigActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(DataTable);

const getColumns = ({ deleteConfig, ticketTypes }) => [
  {
    title: '检查配置名称',
    dataIndex: 'checkName',
    render(checkName, record) {
      return (
        <Link
          type="link"
          to={generateUrls.generateOperationCheckConfigDetailocation({
            id: record.id,
            name: checkName,
          })}
        >
          <Ellipsis lines={1} tooltip>
            {checkName}
          </Ellipsis>
        </Link>
      );
    },
  },
  {
    title: '工单类型',
    dataIndex: 'taskType',
    render: taskType => {
      if (ticketTypes && ticketTypes.normalizedList) {
        return ticketTypes.normalizedList[taskType].taskValue;
      }
      return taskType;
    },
  },
  {
    title: '工单子类型',
    dataIndex: 'taskSubType',
    render: taskSubType => {
      if (ticketTypes && ticketTypes.normalizedList) {
        return ticketTypes.normalizedList[taskSubType].taskValue;
      }
      return taskSubType;
    },
  },
  {
    title: '创建人',
    dataIndex: 'operatorBy',
    render: (operatorBy, record) => <UserLink userId={operatorBy} userName={record.operatorName} />,
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    dataType: 'datetime',
  },
  {
    title: '操作',
    dataIndex: 'id',
    width: 120,
    fixed: 'right',
    render: (__, record) => (
      <span>
        <Link
          to={generateUrls.generateOperationCheckConfigEditLocation({
            id: record.id,
            name: record.checkName,
          })}
        >
          编辑
        </Link>
        <Divider type="vertical" />
        <DeleteConfirm
          variant="popconfirm"
          targetName={record.checkName}
          onOk={() => deleteConfig({ id: record.id })}
        >
          <BaseButton type="link" compact>
            删除
          </BaseButton>
        </DeleteConfirm>
      </span>
    ),
  },
];
