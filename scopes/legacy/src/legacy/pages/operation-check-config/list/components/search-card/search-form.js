import React, { useEffect } from 'react';
import { connect } from 'react-redux';

import { FiltersForm, Form } from '@galiojs/awesome-antd';

import { Cascader } from '@manyun/base-ui.ui.cascader';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Input } from '@manyun/base-ui.ui.input';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';

import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  getDataActionCreator,
  operationCheckConfigActions,
  resetSearchValuesActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/operationCheckConfigActions';

export function SearchForm({
  onReset,
  onSearch,
  ticketTypes,
  syncCommonData,
  updateSearchValues,
  searchValues,
}) {
  const [form] = Form.useForm();
  useEffect(() => {
    syncCommonData({ strategy: { ticketTypes: 'IF_NULL' } });
  }, [syncCommonData]);

  const getCascaderData = data => {
    // 帅选上下电类型
    const newData = data
      .filter(({ taskType }) => taskType === 'POWER')
      .map(item => {
        if (!item.children || !item.children.length) {
          return {
            ...item,
            disabled: true,
          };
        }
        // 过滤测试电转正式电
        const filterPowerTestToNo = item.children.filter(
          ({ taskType }) => taskType !== 'POWER_TEST_TO_ON'
        );
        return {
          ...item,
          children: filterPowerTestToNo,
        };
      });
    return newData;
  };

  const items = [
    { label: '检查配置名称', name: 'configName', control: <Input allowClear /> },
    {
      label: '工单类型',
      name: 'taskType',
      control: (
        <Cascader
          changeOnSelect
          options={ticketTypes ? getCascaderData(ticketTypes.treeList) : []}
          fieldNames={{ value: 'taskType', children: 'children', label: 'taskValue' }}
        />
      ),
    },
    {
      label: '创建时间',
      name: 'createTime',
      span: 2,
      control: (
        <DatePicker.RangePicker
          format="YYYY-MM-DD HH:mm:ss"
          showTime
          placeholder={['开始时间', '结束时间']}
        />
      ),
    },
    {
      label: '创建人',
      name: 'createByName',
      control: <UserSelect allowClear />,
    },
  ];
  return (
    <FiltersForm
      form={form}
      items={items}
      fields={Object.keys(searchValues).map(name => {
        const field = searchValues[name];
        return {
          ...field,
          // name 为数组形式
          name: name.split('.'),
        };
      })}
      onFieldsChange={changedFields => {
        updateSearchValues(
          changedFields.reduce((mapper, field) => {
            // field.name 为数组形式，老代码需要的是字符串形式
            const name = field.name.join('.');
            mapper[name] = {
              ...field,
              name,
            };
            return mapper;
          }, {})
        );
      }}
      onSearch={onSearch}
      onReset={onReset}
    />
  );
}

const mapStateToProps = ({ operationCheckConfig: { searchValues }, common: { ticketTypes } }) => {
  return { searchValues, ticketTypes };
};

const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
  updateSearchValues: operationCheckConfigActions.updateSearchValues,
  onReset: resetSearchValuesActionCreator,
  onSearch: getDataActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(SearchForm);
