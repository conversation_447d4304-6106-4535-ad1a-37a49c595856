import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';

import {
  <PERSON>er<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>rapper,
  TinyCard,
  TinyDescriptions,
} from '@manyun/dc-brain.legacy.components';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  getConfigInfoActionCreator,
  operationCheckConfigActions,
} from '@manyun/dc-brain.legacy.redux/actions/operationCheckConfigActions';
import { getLocationSearchMap } from '@manyun/dc-brain.legacy.utils';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';

import CheckItems from '../create/components/checkItems';

class ConfigDetail extends Component {
  componentDidMount() {
    this.props.syncCommonData({ strategy: { ticketTypes: 'IF_NULL' } });
    this.props.getConfigInfo({ configId: this.props.match.params.id });
  }

  getDeviceTypesName = deviceTypes => {
    if (!deviceTypes || !deviceTypes.length) {
      return '';
    }
    const { deviceCategory } = this.props;
    if (!deviceCategory) {
      return deviceTypes.join(' | ');
    }
    const nameArr = deviceTypes.map(code => {
      return deviceCategory.normalizedList[code].metaName;
    });
    return nameArr.join(' | ');
  };

  componentWillUnmount() {
    this.props.restDetail();
  }

  render() {
    const { detail, match, location, ticketTypes, mode } = this.props;
    const { name } = getLocationSearchMap(location.search, ['name']);
    const id = match.params.id;
    return (
      <GutterWrapper mode="vertical" style={{ paddingBottom: '40px' }}>
        <TinyCard bordered={false}>
          <TinyCard title="基本信息" bordered={false}>
            <TinyDescriptions
              column={3}
              descriptionsItems={[
                {
                  label: '工单类型',
                  value:
                    ticketTypes && ticketTypes.normalizedList[detail.taskType]
                      ? ticketTypes.normalizedList[detail.taskType].taskValue
                      : detail.taskType,
                },
                {
                  label: '工单子类型',
                  value:
                    ticketTypes && ticketTypes.normalizedList[detail.taskSubType]
                      ? ticketTypes.normalizedList[detail.taskSubType].taskValue
                      : detail.taskSubType,
                },
                {
                  label: '检查配置名称',
                  value: detail.checkName,
                },
              ]}
            />
          </TinyCard>
          <CheckItems
            title="操作前检查项"
            defaultData={detail.itemsInBefore ? detail.itemsInBefore : []}
            editingRowKey={null}
            showActionsColumn={false}
            mode={mode}
          />
          <CheckItems
            title="操作后检查项"
            defaultData={detail.itemsInAfter ? detail.itemsInAfter : []}
            editingRowKey={null}
            showActionsColumn={false}
            mode={mode}
          />
        </TinyCard>
        <FooterToolBar>
          <GutterWrapper>
            <Link
              to={urls.generateOperationCheckConfigEditLocation({
                id,
                name,
              })}
            >
              <Button type="primary">编辑</Button>
            </Link>
          </GutterWrapper>
        </FooterToolBar>
      </GutterWrapper>
    );
  }
}

const mapStateToProps = ({ operationCheckConfig: { detail }, common: { ticketTypes } }) => {
  return {
    detail,
    ticketTypes,
  };
};
const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
  getConfigInfo: getConfigInfoActionCreator,
  restDetail: operationCheckConfigActions.restDetail,
};
export default connect(mapStateToProps, mapDispatchToProps)(ConfigDetail);
