import React, { useEffect } from 'react';
import { connect } from 'react-redux';

import { FiltersForm, Form, Select } from '@galiojs/awesome-antd';

import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Input } from '@manyun/base-ui.ui.input';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import { RoomTypeSelect } from '@manyun/resource-hub.ui.room-type-select';
import { ConfigRangeType } from '@manyun/ticket.model.task';
import { TicketPlanType } from '@manyun/ticket.model.ticket';
import { TaskConfigurationSelect } from '@manyun/ticket.ui.task-configuration-select';

import { AssetClassificationApiTreeSelect } from '@manyun/dc-brain.legacy.components';
import {
  INSPECTION_TYPE_KEY_MAP,
  INSPECTION_TYPE_OPTIONS,
} from '@manyun/dc-brain.legacy.constants/inspection';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  getDataActionCreator,
  inspectionConfigActions,
  resetSearchValuesActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/inspectionConfigActions';

export function SearchForm({
  onReset,
  onSearch,
  syncCommonData,
  updateSearchValues,
  searchValues,
  effectType,
}) {
  const [form] = Form.useForm();
  useEffect(() => {
    syncCommonData({ strategy: { deviceCategory: 'IF_NULL', ticketTypes: 'IF_NULL' } });
  }, [syncCommonData]);

  const items = [
    { label: 'SOP名称', name: 'configName', control: <Input allowClear /> },
    {
      label: 'SOP类型',
      name: 'inspectType',

      control: (
        <TaskConfigurationSelect
          ticketPlanType={TicketPlanType.Inspection}
          allowClear
          isAllMaintenance
        />
      ),
    },
    {
      label: '巡检对象',
      name: 'inspectSubject',
      control: (
        <Select allowClear trigger="onDidMount" style={{ width: 200 }}>
          {INSPECTION_TYPE_OPTIONS.map(item => {
            return (
              <Select.Option key={item.value} value={item.value}>
                {item.label}
              </Select.Option>
            );
          })}
        </Select>
      ),
    },
    {
      label: '巡检对象类型',
      name:
        searchValues.inspectSubject.value !== INSPECTION_TYPE_KEY_MAP.ENVIRONMENT
          ? 'subTypeCode'
          : 'roomType',
      control:
        searchValues.inspectSubject.value !== INSPECTION_TYPE_KEY_MAP.ENVIRONMENT ? (
          <AssetClassificationApiTreeSelect
            dataType={['snDevice']}
            category="categorycode"
            disabledDepths={[0, 1]}
            requestOnDidMount
            disabled={searchValues.inspectSubject.value ? false : true}
            allowClear
          />
        ) : (
          <RoomTypeSelect allowClear />
        ),
    },
    {
      label: '创建时间',
      name: 'createTime',
      span: 2,
      control: (
        <DatePicker.RangePicker
          format="YYYY-MM-DD HH:mm:ss"
          showTime
          placeholder={['开始时间', '结束时间']}
        />
      ),
    },
    {
      label: '创建人',
      name: 'creatorId',
      control: <UserSelect allowClear />,
    },
  ];
  return (
    <FiltersForm
      form={form}
      items={
        effectType === ConfigRangeType.Block
          ? [
              {
                label: '机房楼栋',
                name: 'effectDomainList',
                control: (
                  <LocationTreeSelect
                    style={{ width: 218 }}
                    multiple
                    authorizedOnly
                    disabledTypes={['IDC']}
                    allowClear
                  />
                ),
              },
              ...items,
            ]
          : items
      }
      onFieldsChange={changedFields => {
        let fileds = changedFields.reduce((mapper, field) => {
          // field.name 为数组形式，老代码需要的是字符串形式
          const name = field.name.join('.');
          mapper[name] = {
            ...field,
            name,
          };
          return mapper;
        }, {});
        if (changedFields.inspectSubject && !changedFields.inspectSubject.value) {
          fileds = {
            ...fileds,
            roomType: {
              value: null,
            },
            subTypeCode: {
              value: null,
            },
          };
        }
        updateSearchValues(fileds);
      }}
      onSearch={() => onSearch({ shouldResetPageNum: true, effectType })}
      onReset={() => onReset(effectType)}
      fields={Object.keys(searchValues).map(name => {
        const field = searchValues[name];
        return {
          ...field,
          // name 为数组形式
          name: name.split('.'),
        };
      })}
    />
  );
}

const mapStateToProps = ({ inspectionConfig: { searchValues }, common: { deviceCategory } }) => {
  return { searchValues, deviceCategory };
};
const mapDispatchToProps = {
  updateSearchValues: inspectionConfigActions.updateSearchValues,
  onReset: resetSearchValuesActionCreator,
  onSearch: getDataActionCreator,
  syncCommonData: syncCommonDataActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(SearchForm);
