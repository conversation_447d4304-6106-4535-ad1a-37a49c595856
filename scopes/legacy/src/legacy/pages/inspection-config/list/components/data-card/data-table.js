import React, { useCallback, useEffect } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Divider } from '@manyun/base-ui.ui.divider';

import { ConfigRangeType } from '@manyun/ticket.model.task';
import {
  generateInspectionConfigDetailRoutePath,
  generateInspectionCopyLocation,
} from '@manyun/ticket.route.ticket-routes';

import { Ellipsis, TinyTable, UserLink } from '@manyun/dc-brain.legacy.components';
import {
  INSPECTION_TYPE_KEY_MAP,
  INSPECTION_TYPE_TEXT_MAP,
} from '@manyun/dc-brain.legacy.constants/inspection';
import {
  deleteConfigActionCreator,
  getDataActionCreator,
  setPaginationThenGetDataActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/inspectionConfigActions';
import * as generateUrls from '@manyun/dc-brain.legacy.utils/urls';

export function DataTable({
  data,
  total,
  pageNum,
  pageSize,
  getData,
  setPagination,
  deleteConfig,
  ticketTypeMapping,
  loading,
  deviceTypeMapping,
  effectType,
}) {
  useEffect(() => {
    getData({ shouldResetPageNum: true, effectType });
  }, [effectType, getData]);

  const paginationChangeHandler = useCallback(
    (current, size) => {
      setPagination({ pageNum: current, pageSize: size, effectType: effectType });
    },
    [setPagination, effectType]
  );

  return (
    <TinyTable
      rowKey="id"
      loading={loading}
      columns={getColumns({ deleteConfig, ticketTypeMapping, deviceTypeMapping, effectType })}
      align="left"
      dataSource={data}
      actions={[
        <Button
          key="create"
          type="primary"
          href={generateUrls.generateInspectionConfigCreateLocation()}
        >
          新建SOP
        </Button>,
      ]}
      pagination={{
        total,
        current: pageNum,
        pageSize,
        onChange: paginationChangeHandler,
      }}
    />
  );
}

const mapStateToProps = ({
  inspectionConfig: {
    data,
    total,
    pagination: { pageNum, pageSize },
    loading,
  },
  common: { ticketTypes, deviceCategory },
}) => {
  return {
    data,
    total,
    pageNum,
    pageSize,
    loading,
    deviceTypeMapping: deviceCategory ? deviceCategory.normalizedList : {},
    ticketTypeMapping: ticketTypes ? ticketTypes.normalizedList : {},
  };
};
const mapDispatchToProps = {
  getData: getDataActionCreator,
  setPagination: setPaginationThenGetDataActionCreator,
  deleteConfig: deleteConfigActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(DataTable);

const getColumns = ({ deleteConfig, ticketTypeMapping, deviceTypeMapping, effectType }) => {
  const columns = [
    {
      title: 'SOP名称',
      dataIndex: 'configName',
      render(configName, record) {
        return (
          <Link
            type="link"
            target="_blank"
            to={generateInspectionConfigDetailRoutePath({
              id: record.id,
              name: record.configName,
            })}
          >
            <Ellipsis lines={1} tooltip>
              {configName}
            </Ellipsis>
          </Link>
        );
      },
    },
    {
      title: 'SOP类型',
      dataIndex: 'inspectType',
      render: inspectType => {
        const ticketTarget = ticketTypeMapping[inspectType];
        if (!ticketTarget) {
          return inspectType;
        }
        return ticketTarget.taskValue;
      },
    },
    {
      title: '巡检对象',
      dataIndex: 'inspectSubject',
      render: inspectSubject => {
        if (INSPECTION_TYPE_TEXT_MAP[inspectSubject]) {
          return INSPECTION_TYPE_TEXT_MAP[inspectSubject];
        }
        return inspectSubject;
      },
    },
    {
      title: '巡检对象类型',
      dataIndex: 'subTypeName',
      render: (txt, { inspectSubject, subTypeCode }) => {
        if (inspectSubject === INSPECTION_TYPE_KEY_MAP.ENVIRONMENT) {
          return txt;
        }
        const deviceTarget = deviceTypeMapping[subTypeCode];
        if (deviceTarget) {
          return deviceTarget.metaName;
        }
        return txt;
      },
    },
    {
      title: '巡检项条数',
      dataIndex: 'itemCount',
    },
    {
      title: '创建时间',
      dataIndex: 'gmtCreate',
      dataType: 'datetime',
    },

    {
      title: '创建人',
      dataIndex: 'creatorId',
      render: (creatorId, record) => <UserLink userId={creatorId} userName={record.creatorName} />,
    },
    {
      title: '操作',
      dataIndex: 'inspectId',
      width: 160,
      fixed: 'right',
      render: (__, record) => (
        <span>
          <Link
            target="_blank"
            to={generateUrls.generateInspectionConfigEditLocation({
              id: record.id,
              name: record.configName,
            })}
          >
            编辑
          </Link>
          <Divider type="vertical" />
          <Link
            target="_blank"
            to={generateInspectionCopyLocation({
              id: record.id,
              name: record.configName,
            })}
          >
            复制
          </Link>
          <Divider type="vertical" />
          <DeleteConfirm
            variant="popconfirm"
            targetName={record.configName}
            onOk={() => deleteConfig({ inspectConfigId: record.id, effectType })}
          >
            <Button type="link" compact>
              删除
            </Button>
          </DeleteConfirm>
        </span>
      ),
    },
  ];
  return effectType === ConfigRangeType.Block
    ? [{ title: '楼栋', dataIndex: 'effectDomain' }, ...columns]
    : columns;
};
