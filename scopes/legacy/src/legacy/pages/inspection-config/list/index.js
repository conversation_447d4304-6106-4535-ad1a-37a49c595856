import React, { useState } from 'react';

import { Card } from '@manyun/base-ui.ui.card';
import { Space } from '@manyun/base-ui.ui.space';

import { ConfigRangeType } from '@manyun/ticket.model.task';
import { ConfigRangeTypeRadioButton } from '@manyun/ticket.ui.config-range-type-radio-button';

import DataCard from './components/data-card';
import SearchCard from './components/search-card';

export function InspectionConfigList() {
  const [effectType, setEffectType] = useState(ConfigRangeType.Block);

  return (
    <Card
      title="巡检SOP配置"
      extra={
        <ConfigRangeTypeRadioButton
          configRangeType={effectType}
          onChange={setEffectType}
          ticketPrefix="SOP"
        />
      }
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        <SearchCard effectType={effectType} />
        <DataCard effectType={effectType} />
      </Space>
    </Card>
  );
}

export default InspectionConfigList;
