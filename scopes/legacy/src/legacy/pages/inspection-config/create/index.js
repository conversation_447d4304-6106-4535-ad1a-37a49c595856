import Form from '@ant-design/compatible/es/form';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Explanation } from '@manyun/base-ui.ui.explanation';
import { Input } from '@manyun/base-ui.ui.input';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Select } from '@manyun/base-ui.ui.select';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import { CONFIG_RANGE_KEY_MAP, ConfigRangeType } from '@manyun/ticket.model.task';

import {
  AssetClassificationApiTreeSelect,
  FooterToolBar,
  GutterWrapper,
} from '@manyun/dc-brain.legacy.components';
import InspectionTable from '@manyun/dc-brain.legacy.components/inspection-table';
import {
  INSPECTION_TYPE_KEY_MAP,
  INSPECTION_TYPE_OPTIONS,
} from '@manyun/dc-brain.legacy.constants/inspection';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  createConfigActionCreator,
  editConfigActionCreator,
  getChildItemsActionCreator,
  inspectionConfigActions,
} from '@manyun/dc-brain.legacy.redux/actions/inspectionConfigActions';

class InspectionConfigCreate extends Component {
  inspectionTableRef = React.createRef();

  componentDidMount() {
    this.props.syncCommonData({
      strategy: { deviceCategory: 'IF_NULL', roomTypes: 'IF_NULL', ticketTypes: 'IF_NULL' },
    });
    if (this.props.mode === 'edit' || this.props.mode === 'copy') {
      this.props.getChildItems({ id: this.props.match.params.id, mode: this.props.mode });
    }
  }

  componentWillUnmount() {
    this.props.restInspectionData();
  }

  handleOk = event => {
    event.preventDefault();
    if (this.inspectionTableRef.current) {
      this.inspectionTableRef.current.validateFields((errs, value) => {
        if (errs) {
          return;
        }
        this.props.form.validateFields(async (err, values) => {
          if (err) {
            return;
          }
          const { mode } = this.props;
          if (mode === 'new' || mode === 'copy') {
            this.props.createConfig({ mode });
          }
          if (mode === 'edit') {
            this.props.editConfig();
          }
        });
      });
    }
  };

  validate = () => {
    let tmp = null;
    this.props.form.validateFields(async (err, values) => {
      if (err) {
        tmp = false;
      } else {
        tmp = true;
      }
    });
    return tmp;
  };

  render() {
    const {
      form: { getFieldDecorator, getFieldValue, setFieldsValue },
      fieldValues,
      mode,
      roomTypeList,
      inspectTypeTree,
    } = this.props;

    return (
      <GutterWrapper mode="vertical">
        <Card title="基本信息" bordered={false}>
          <Form labelCol={{ xl: 4 }} wrapperCol={{ xl: 20 }} style={{ width: 848 }} colon={false}>
            <Form.Item label="适用范围">
              {getFieldDecorator('effectType', {
                rules: [{ required: true, message: '适用范围为必选项！' }],
              })(
                <Radio.Group
                  style={{ width: 304 }}
                  onChange={() => {
                    setFieldsValue({ effectDomain: undefined });
                  }}
                >
                  {CONFIG_RANGE_KEY_MAP.map(({ label, value }) => {
                    return (
                      <Radio key={value} value={value}>
                        {label}
                      </Radio>
                    );
                  })}
                </Radio.Group>
              )}
            </Form.Item>
            {getFieldValue('effectType') === ConfigRangeType.Block && (
              <Form.Item label="属地楼栋">
                {getFieldDecorator('effectDomain', {
                  rules: [{ required: true, message: '属地楼栋为必选项！' }],
                })(
                  <LocationTreeSelect
                    style={{ width: 304 }}
                    authorizedOnly
                    disabledTypes={['IDC']}
                    allowClear
                  />
                )}
              </Form.Item>
            )}
            <Form.Item label="SOP类型">
              {getFieldDecorator('inspectType', {
                rules: [{ required: true, message: 'SOP类型为必选项！' }],
              })(
                <Select style={{ width: 304 }} disabled={mode === 'copy'}>
                  {inspectTypeTree.map(item => {
                    return (
                      <Select.Option key={item.taskType} value={item.taskType}>
                        {item.taskValue}
                      </Select.Option>
                    );
                  })}
                </Select>
              )}
              <Explanation
                iconType="question"
                tooltip={{ title: '配置只有在确保已录入巡检对象情况下才可生效' }}
              />
            </Form.Item>
            <Form.Item label="巡检对象">
              {getFieldDecorator('inspectSubject', {
                rules: [{ required: true, message: '巡检对象为必选项！' }],
              })(
                <Select
                  allowClear
                  disabled={mode === 'edit' || mode === 'copy'}
                  style={{ width: 304 }}
                >
                  {INSPECTION_TYPE_OPTIONS.map(item => {
                    return (
                      <Select.Option key={item.value} value={item.value}>
                        {item.label}
                      </Select.Option>
                    );
                  })}
                </Select>
              )}
            </Form.Item>
            {fieldValues.inspectSubject.value === INSPECTION_TYPE_KEY_MAP.DEVICE && (
              <Form.Item label="巡检对象类型">
                {getFieldDecorator('subTypeCode', {
                  rules: [{ required: true, message: '巡检对象类型为必选项！' }],
                })(
                  <AssetClassificationApiTreeSelect
                    dataType={['snDevice']}
                    showSearch
                    category="allCategory"
                    disabledDepths={[0, 1]}
                    requestOnDidMount
                    disabled={mode === 'copy'}
                    style={{ width: 304 }}
                  />
                )}
              </Form.Item>
            )}
            {fieldValues.inspectSubject.value === INSPECTION_TYPE_KEY_MAP.ENVIRONMENT && (
              <Form.Item label="巡检对象类型">
                {getFieldDecorator('roomType', {
                  rules: [{ required: true, message: '巡检对象类型为必选项！' }],
                })(
                  <Select
                    optionFilterProp="label"
                    showSearch
                    allowClear
                    disabled={mode === 'copy'}
                    style={{ width: 304 }}
                    options={roomTypeList}
                  />
                )}
              </Form.Item>
            )}
            <Form.Item label="SOP名称">
              {getFieldDecorator('configName', {
                rules: [
                  { required: true, whitespace: true, message: 'SOP名称为必填项！' },
                  {
                    max: 20,
                    message: '最多输入 20 个字符！',
                  },
                ],
              })(<Input style={{ width: 304 }} allowClear />)}
            </Form.Item>
            {/* {fieldValues.inspectSubject.value === INSPECTION_TYPE_KEY_MAP.DEVICE && ( */}

            <Form.Item label="是否配置巡检场景" style={{ minWidth: 1580 }}>
              {getFieldDecorator('supportCheckScenes', {
                rules: [{ required: true, message: '是否配置巡检场景为必选项！' }],
                initialValue: false,
              })(
                <Radio.Group
                  options={[
                    { label: '否', value: false },
                    { label: '是', value: true },
                  ]}
                />
              )}
            </Form.Item>
          </Form>
        </Card>
        <div style={{ marginBottom: '40px' }}>
          <Card bordered={false}>
            <InspectionTable
              // inspectScope="patrol"
              // taskType="IT"
              ref={this.inspectionTableRef}
              inspectScope="config"
              value={fieldValues.config}
              fieldValues={{
                ...fieldValues,
                subTypeCode: {
                  value: fieldValues.subTypeCode.value
                    ? fieldValues.subTypeCode.value.thirdCategorycode
                    : null,
                },
              }}
              mode={mode}
              validate={this.validate}
              supportCheckScenes={fieldValues.supportCheckScenes.value}
              onChange={value => this.props.setConfigTableDate(value)}
            />
          </Card>
        </div>
        <FooterToolBar>
          <GutterWrapper>
            <Button type="primary" onClick={this.handleOk}>
              确认
            </Button>
          </GutterWrapper>
        </FooterToolBar>
      </GutterWrapper>
    );
  }
}

const mapStateToProps = (
  { inspectionConfig: { create, edit }, common: { deviceCategory, roomTypes, ticketTypes } },
  { mode }
) => {
  let fieldValues = create;
  if (mode === 'edit' || mode === 'copy') {
    fieldValues = edit;
  }
  let roomTypeList = [];
  if (roomTypes) {
    roomTypeList = Object.keys(roomTypes).map(key => {
      return {
        label: roomTypes[key],
        value: key,
      };
    });
  }
  let inspectTypeTree = [];
  if (ticketTypes && ticketTypes.treeList) {
    ticketTypes.treeList.forEach(({ taskType, children }) => {
      if (taskType === 'INSPECTION') {
        inspectTypeTree = children;
      }
    });
  }
  return {
    fieldValues: fieldValues,
    deviceCategory,
    roomTypeList,
    inspectTypeTree,
    roomTypes,
  };
};

const mapDispatchToProps = {
  upDateCreateOptionValues: inspectionConfigActions.upDateCreateOptionValues,
  setSelectedPointsInEdit: inspectionConfigActions.setSelectedPointsInEdit,
  getChildItems: getChildItemsActionCreator,
  upDateEditOptionValues: inspectionConfigActions.upDateEditOptionValues,
  createConfig: createConfigActionCreator,
  editConfig: editConfigActionCreator,
  setConfigTableDate: inspectionConfigActions.setConfigTableDate,
  syncCommonData: syncCommonDataActionCreator,
  restInspectionData: inspectionConfigActions.restInspectionData,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(
  Form.create({
    onFieldsChange(props, fieldValues) {
      let options = fieldValues;
      // 设备类型改变
      if (props.mode === 'new' && fieldValues.subTypeCode && fieldValues.subTypeCode.value) {
        options = {
          ...options,
          configName: {
            value: `${fieldValues.subTypeCode.value.thirdCategoryName}巡检配置`,
            name: 'configName',
          },
        };
      }

      // 空间类型改变
      if (props.mode === 'new' && fieldValues.roomType && fieldValues.roomType.value) {
        options = {
          ...options,
          configName: {
            value: `${props.roomTypes[fieldValues.roomType.value]}巡检配置`,
            name: 'configName',
          },
        };
      }

      if (props.mode === 'new' && fieldValues.inspectSubject && fieldValues.inspectSubject.value) {
        if (fieldValues.inspectSubject.value === INSPECTION_TYPE_KEY_MAP.DEVICE) {
          if (props.fieldValues.subTypeCode.value) {
            options = {
              ...options,
              configName: {
                value: `${props.fieldValues.subTypeCode.value.thirdCategoryName}巡检配置`,
                name: 'configName',
              },
            };
          } else {
            options = {
              ...options,
              configName: {
                value: null,
              },
            };
          }
        }
        if (
          props.mode === 'new' &&
          fieldValues.inspectSubject.value === INSPECTION_TYPE_KEY_MAP.ENVIRONMENT
        ) {
          if (props.fieldValues.roomType.value) {
            options = {
              ...options,
              configName: {
                value: `${props.roomTypes[props.fieldValues.roomType.value]}巡检配置`,
                name: 'configName',
              },
            };
          } else {
            options = {
              ...options,
              configName: {
                value: null,
              },
            };
          }
        }
      }
      if (props.mode === 'edit' || props.mode === 'copy') {
        props.upDateEditOptionValues(options);
      } else {
        props.upDateCreateOptionValues(options);
      }
    },
    mapPropsToFields({ fieldValues }) {
      return {
        inspectType: Form.createFormField(fieldValues.inspectType),
        subTypeCode: Form.createFormField(fieldValues.subTypeCode),
        effectType: Form.createFormField(fieldValues.effectType),
        configName: Form.createFormField(fieldValues.configName),
        effectDomain: Form.createFormField(fieldValues.effectDomain),
        roomType: Form.createFormField(fieldValues.roomType),
        inspectSubject: Form.createFormField(fieldValues.inspectSubject),
        supportCheckScenes: Form.createFormField(fieldValues.supportCheckScenes),
      };
    },
  })(InspectionConfigCreate)
);
