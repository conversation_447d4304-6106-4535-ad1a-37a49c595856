import React from 'react';

import { TinyDescriptions } from '@manyun/dc-brain.legacy.components';
import { INSPECTION_TYPE_TEXT_MAP } from '@manyun/dc-brain.legacy.constants/inspection';

export function BasicInfos({
  inspectTypeName,
  inspectSubject,
  subTypeName,
  configName,
  supportCheckScenes,
}) {
  return (
    <TinyDescriptions
      column={5}
      descriptionsItems={[
        {
          label: 'SOP类型',
          value: inspectTypeName,
        },
        {
          label: '巡检对象',
          value: INSPECTION_TYPE_TEXT_MAP[inspectSubject]
            ? INSPECTION_TYPE_TEXT_MAP[inspectSubject]
            : inspectSubject,
        },
        {
          label: '巡检对象类型',
          value: subTypeName,
        },
        {
          label: 'SOP名称',
          value: configName,
        },
        {
          label: '是否配置巡检场景',
          value: supportCheckScenes ? '是' : '否',
        },
      ]}
    />
  );
}

export default BasicInfos;
