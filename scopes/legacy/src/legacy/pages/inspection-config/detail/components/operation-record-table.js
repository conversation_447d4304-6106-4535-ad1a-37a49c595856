import React from 'react';

import { OperationLogTable } from '@manyun/auth-hub.ui.operation-log-table';

export function OperationRecordTable({ id }) {
  return (
    <OperationLogTable
      showColumns={['serialNumber', 'targetType', 'modifyType']}
      defaultSearchParams={{ targetType: 'INSPECTION_CONFIG', targetId: id }}
      isTargetIdEqual={targetId => {
        return targetId === id;
      }}
    />
  );
}
export default OperationRecordTable;
