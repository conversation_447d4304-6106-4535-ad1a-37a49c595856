import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { useParams } from 'react-router-dom';

import { Radio } from '@manyun/base-ui.ui.radio';

import { GutterWrapper, TinyCard } from '@manyun/dc-brain.legacy.components';
import InspectionTable from '@manyun/dc-brain.legacy.components/inspection-table';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  getChildItemsActionCreator,
  inspectionConfigActions,
} from '@manyun/dc-brain.legacy.redux/actions/inspectionConfigActions';
import { redirectActionCreator } from '@manyun/dc-brain.legacy.redux/actions/routerActions';

import BasicInfos from './components/basic-infos';
import OperationRecordTable from './components/operation-record-table';

export function InspectionConfigDetail({
  detail,
  getChildItems,
  mode,
  location,
  redirect,
  syncCommonData,
  ticketTypes,
  setConfigInfos,
}) {
  const { id } = useParams();
  const [tabs, setData] = useState('config');

  useEffect(() => {
    syncCommonData({ strategy: { ticketTypes: 'IF_NULL' } });
  }, [syncCommonData]);

  useEffect(() => {
    getChildItems({ id, mode });
  }, [id, getChildItems, mode]);

  useEffect(() => {
    return () => {
      setConfigInfos({});
    };
  }, [setConfigInfos]);

  const changeTabs = e => setData(e.target.value);

  return (
    <GutterWrapper mode="vertical">
      <TinyCard title="基本信息" bordered={false}>
        <BasicInfos
          inspectTypeName={
            ticketTypes &&
            detail.inspectType &&
            ticketTypes.normalizedList &&
            ticketTypes.normalizedList[detail.inspectType]
              ? ticketTypes.normalizedList[detail.inspectType].taskValue
              : detail.inspectType
          }
          inspectSubject={detail.inspectSubject}
          subTypeName={detail.subTypeName}
          configName={detail.configName}
          supportCheckScenes={detail.supportCheckScenes}
        />
      </TinyCard>

      <Radio.Group value={tabs} onChange={changeTabs}>
        <Radio.Button value="config">巡检项</Radio.Button>
        <Radio.Button value="record">操作记录</Radio.Button>
      </Radio.Group>
      <div style={{ marginBottom: '40px' }}>
        {tabs === 'config' && (
          <InspectionTable
            mode={mode}
            value={detail.config}
            supportCheckScenes={detail.supportCheckScenes}
          />
        )}
        {tabs === 'record' && <OperationRecordTable id={id} />}
      </div>
    </GutterWrapper>
  );
}

const mapStateToProps = ({ inspectionConfig: { detail }, common: { ticketTypes } }) => {
  return { detail, ticketTypes };
};
const mapDispatchToProps = {
  getChildItems: getChildItemsActionCreator,
  redirect: redirectActionCreator,
  syncCommonData: syncCommonDataActionCreator,
  setConfigInfos: inspectionConfigActions.setConfigInfos,
};

export default connect(mapStateToProps, mapDispatchToProps)(InspectionConfigDetail);
