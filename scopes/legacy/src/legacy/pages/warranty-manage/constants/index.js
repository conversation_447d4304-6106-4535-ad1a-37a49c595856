import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Typography } from '@manyun/base-ui.ui.typography';

// 订单状态
export const BIZ_STATUS_KEY_MAP = {
  APPROVING: 'APPROVING',
  PASS: 'PASS',
  REFUSE: 'REFUSE',
  REVOKE: 'REVOKE',
};

export const BIZ_STATUS_MAP = {
  [BIZ_STATUS_KEY_MAP.APPROVING]: { label: '待审批', color: `var(--${prefixCls}-error-color)` },
  [BIZ_STATUS_KEY_MAP.PASS]: { label: '审批通过', color: 'var(--color-normal)' },
  [BIZ_STATUS_KEY_MAP.REFUSE]: { label: '审批拒绝', color: `var(--${prefixCls}-error-color)` },
  [BIZ_STATUS_KEY_MAP.REVOKE]: { label: '草稿', color: 'var(--color-grey)' },
};

export const BIZ_STATUS_OPTIONS = [
  {
    value: BIZ_STATUS_KEY_MAP.APPROVING,
    labels: BIZ_STATUS_MAP[BIZ_STATUS_KEY_MAP.APPROVING],
  },
  {
    value: BIZ_STATUS_KEY_MAP.PASS,
    labels: BIZ_STATUS_MAP[BIZ_STATUS_KEY_MAP.PASS],
  },
  {
    value: BIZ_STATUS_KEY_MAP.REFUSE,
    labels: BIZ_STATUS_MAP[BIZ_STATUS_KEY_MAP.REFUSE],
  },
  {
    value: BIZ_STATUS_KEY_MAP.REVOKE,
    labels: BIZ_STATUS_MAP[BIZ_STATUS_KEY_MAP.REVOKE],
  },
];

export function BizStatus(status) {
  if (status === BIZ_STATUS_KEY_MAP.APPROVING) {
    return <Typography.Text type="warning">待审批</Typography.Text>;
  }
  if (status === BIZ_STATUS_KEY_MAP.PASS) {
    return <Typography.Text type="success">审批通过</Typography.Text>;
  }
  if (status === BIZ_STATUS_KEY_MAP.REFUSE) {
    return <Typography.Text type="danger">审批拒绝</Typography.Text>;
  }
  if (status === BIZ_STATUS_KEY_MAP.REFUSE) {
    return <Typography.Text>已撤销</Typography.Text>;
  }
}
//维保状态
export const WARRANTY_STATUS_KEY_MAP = {
  VALID: 'VALID',
  EXPIRED: 'EXPIRED',
};

export const WARRANTY_STATUS_MAP = {
  [WARRANTY_STATUS_KEY_MAP.VALID]: { label: '在保', color: 'var(--color-normal)' },
  [WARRANTY_STATUS_KEY_MAP.EXPIRED]: { label: '超期', color: `var(--${prefixCls}-error-color)` },
};
export function WarrantyStatus(status = {}) {
  if (!status) {
    return '';
  }
  const { code } = status;
  if (code === 'VALID') {
    return <Typography.Text type="success">在保</Typography.Text>;
  }
  if (code === 'EXPIRED') {
    return <Typography.Text type="danger">超期</Typography.Text>;
  }
  return 1;
}

export const WARRANTY_STATUS_OPTIONS = [
  {
    value: WARRANTY_STATUS_KEY_MAP.VALID,
    labels: WARRANTY_STATUS_MAP[WARRANTY_STATUS_KEY_MAP.VALID],
  },
  {
    value: WARRANTY_STATUS_KEY_MAP.EXPIRED,
    labels: WARRANTY_STATUS_MAP[WARRANTY_STATUS_KEY_MAP.EXPIRED],
  },
];
