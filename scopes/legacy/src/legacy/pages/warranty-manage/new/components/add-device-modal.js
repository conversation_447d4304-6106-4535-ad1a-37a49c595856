import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';

import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';

import { VendorSelect } from '@manyun/crm.ui.vendor-select';

import {
  AssetClassificationApiTreeSelect,
  GutterWrapper,
  TinyModal,
  TinyTable,
} from '@manyun/dc-brain.legacy.components';
import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import { equipmentManageService } from '@manyun/dc-brain.legacy.services';
import { getDeviceTypeName } from '@manyun/dc-brain.legacy.utils/ticket';

import { WARRANTY_STATUS_MAP } from '../../constants';

const getColumns = ctx => [
  {
    title: '资产ID',
    dataIndex: 'assetNo',
    render: txt => txt || BLANK_PLACEHOLDER,
  },
  {
    title: '三级分类',
    dataIndex: 'deviceType',
    render: text => getDeviceTypeName(text, ctx.props.normalizedList),
  },
  {
    title: '维保厂商',
    dataIndex: 'warrantyVendor',
  },
  {
    title: '过保日期',
    dataIndex: 'warrantyTime',
  },
  {
    title: '维保状态',
    dataIndex: 'warrantyStatus',
    render: text => (
      <span style={{ color: WARRANTY_STATUS_MAP[text?.code]?.color }}>{text?.name}</span>
    ),
  },
];

const initialState = {
  selectedRowKeys: [],
  selectedRows: [],
};

class AddConfig extends Component {
  state = {
    modelVisible: false,
    equipmentPage: {
      total: 0,
      list: [],
    },
    pagination: {
      pageNum: 1,
      pageSize: 10,
    },
    loading: false,
    ...initialState,
  };

  componentDidMount() {
    this.props.syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });
  }

  componentDidUpdate(prevProps) {
    const { dataList } = this.props;
    if (prevProps.dataList !== dataList) {
      this.setState({ ...initialState });
    }
  }

  editModelVisible = () => {
    this.setState({ modelVisible: !this.state.modelVisible });
  };

  handleAdd = () => {
    const { selectedRows } = this.state;
    this.props.setDataList(selectedRows);

    this.editModelVisible();
  };

  onSelectChange = (selectedRowKeys, selectedRows) => {
    this.setState({ selectedRows, selectedRowKeys });
  };

  fetchDeviceList = async () => {
    const { pagination } = this.state;
    this.setState({ loading: true });
    const { response, error } = await equipmentManageService.fetchEquipmentListPage({
      ...this.getParams(),
      ...pagination,
    });
    this.setState({ loading: false });
    if (error) {
      message.error(error);
      return;
    }
    this.setState({
      equipmentPage: {
        list: response.data,
        total: response.total,
      },
    });
  };

  getParams = () => {
    const { location, form } = this.props;
    const data = form.getFieldsValue();
    const params = {
      sortWarranty: 'ASCEND',
      spaceGuidList: [location.join('.')],
    };
    for (const [name, value] of Object.entries(data)) {
      const val = value === '' ? null : value;
      if (name === 'deviceType' && val) {
        params['topCategory'] = val.firstCategoryCode;
        params['secondCategory'] = val.secondCategoryCode;
        params['deviceTypeList'] = val.thirdCategorycode && [val.thirdCategorycode];
      } else if (name === 'warrantyTime' && val) {
        params['warrantyTimeStart'] = val[0].clone().startOf('day').valueOf();
        params['warrantyTimeEnd'] = val[1].clone().endOf('day').valueOf();
      } else if (val) {
        params[name] = val;
      }
    }
    return params;
  };

  handleSearch = () => {
    this.setPaginationChangeHandler({ pageNum: 1, pageSize: 10 });
  };

  handleReset = () => {
    this.props.form.resetFields();
    this.handleSearch();
  };

  setPaginationChangeHandler = ({ pageNum, pageSize }) => {
    this.setState({ pagination: { pageNum, pageSize } }, () => this.fetchDeviceList());
  };

  render() {
    const { modelVisible, selectedRowKeys, selectedRows, equipmentPage, loading, pagination } =
      this.state;
    const { form, dataList, location } = this.props;
    const { getFieldDecorator } = form;

    return (
      <GutterWrapper mode="vertical">
        <Button
          type="primary"
          onClick={() => {
            if (!modelVisible) {
              this.handleSearch();
            }
            this.editModelVisible();
          }}
          disabled={!location?.[1]}
        >
          添加设备
        </Button>
        <TinyModal
          width="1000px"
          title="添加"
          maskClosable={false}
          destroyOnClose
          visible={modelVisible}
          onCancel={this.editModelVisible}
          onOk={this.handleAdd}
          bodyStyle={{ maxHeight: '452px', overflowY: 'auto' }}
        >
          <Space style={{ width: '100%' }} direction="vertical">
            <Form colon={false} layout="inline">
              <Form.Item label="三级分类">
                {getFieldDecorator('deviceType')(
                  <AssetClassificationApiTreeSelect
                    dataType={['snDevice']}
                    category="allCategoryCode"
                    allowClear
                    style={{ width: 200 }}
                  />
                )}
              </Form.Item>
              <Form.Item label="过保日期">
                {getFieldDecorator('warrantyTime')(
                  <DatePicker.RangePicker allowClear format="YYYY-MM-DD" />
                )}
              </Form.Item>
              <Form.Item label="维保厂商">
                {getFieldDecorator('warrantyVendor')(
                  <VendorSelect style={{ width: 200 }} allowClear />
                )}
              </Form.Item>
              <Form.Item>
                <Space>
                  <Button onClick={this.handleSearch} type="primary">
                    搜索
                  </Button>
                  <Button onClick={this.handleReset}>重置</Button>
                </Space>
              </Form.Item>
            </Form>
            <TinyTable
              rowKey="guid"
              dataSource={equipmentPage.list}
              loading={loading}
              columns={getColumns(this)}
              align="left"
              selectRowsSpreadPage
              rowSelection={{
                selectedRowKeys,
                selectedRows,
                onChange: this.onSelectChange,
                getCheckboxProps: item => ({
                  disabled: dataList.map(({ guid }) => guid).includes(item.guid),
                }),
              }}
              pagination={{
                total: equipmentPage.total,
                current: pagination.pageNum,
                pageSize: pagination.pageSize,
                onChange: (current, size) =>
                  this.setPaginationChangeHandler({ pageNum: current, pageSize: size }),
              }}
            />
          </Space>
        </TinyModal>
      </GutterWrapper>
    );
  }
}

const mapStateToProps = ({ common: { deviceCategory } }) => ({
  normalizedList: deviceCategory?.normalizedList,
});
const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(Form.create()(AddConfig));
