import React, { Component } from 'react';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';

import Form from '@ant-design/compatible/es/form';
import QuestionCircleOutlined from '@ant-design/icons/es/icons/QuestionCircleOutlined';
import moment from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';

import { VendorSelect } from '@manyun/crm.ui.vendor-select';

import {
  FooterToolBar,
  GutterWrapper,
  LocationCascader,
  TinyCard,
  TinyModal,
  TinyTable,
} from '@manyun/dc-brain.legacy.components';
import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import { redirectActionCreator } from '@manyun/dc-brain.legacy.redux/actions/routerActions';
import {
  fetchWarrantyDetail,
  warrantyOrderActions,
} from '@manyun/dc-brain.legacy.redux/actions/warrantyOrderActions';
import * as warrantyOrderService from '@manyun/dc-brain.legacy.services/warrantyOrderService';
import { getDeviceTypeName } from '@manyun/dc-brain.legacy.utils/ticket';
import * as urlsUtil from '@manyun/dc-brain.legacy.utils/urls';

import { WARRANTY_STATUS_MAP } from '../constants';
import AddConfig from './components/add-device-modal';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 2 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 22 },
  },
};

const getColumns = ctx => [
  {
    title: '资产ID',
    dataIndex: 'assetNo',
    render: txt => txt || BLANK_PLACEHOLDER,
  },
  {
    title: '三级分类',
    dataIndex: 'deviceType',
    render: (_, record) => getDeviceTypeName(record.deviceType, ctx.props.normalizedList),
  },
  {
    title: '维保厂商',
    dataIndex: 'warrantyVendor',
  },
  {
    title: '过保日期',
    dataIndex: 'warrantyTime',
    dataType: 'date',
  },
  {
    title: '维保状态',
    dataIndex: 'warrantyStatus',
    render: warrantyStatus => (
      <span style={{ color: WARRANTY_STATUS_MAP[warrantyStatus?.code]?.color }}>
        {WARRANTY_STATUS_MAP[warrantyStatus?.code]?.label}
      </span>
    ),
  },
  {
    title: '操作',
    dataIndex: 'action',
    render: (_, record) => (
      <Button compact type="link" onClick={() => ctx.handleDelete(record.guid)}>
        移除
      </Button>
    ),
  },
];

export class CreateWarrantyOrder extends Component {
  state = {
    loading: false,
    deviceList: [],
    modelVisible: false,
  };

  componentDidMount() {
    const {
      match: { params },
      selectedRows,
    } = this.props;
    this.props.syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });
    if (selectedRows?.length) {
      this.setState({ deviceList: selectedRows });
      window.addEventListener('beforeunload', this._handleEvent);
      return;
    }
    if (params.id) {
      this.setState({ orderNo: params.id });
      this.fetchDevices({ orderNo: params.id });
      this.props.fetchWarrantyDetail({ orderNo: params.id });
    }
  }

  componentWillUnmount() {
    const { selectedRows } = this.props;
    if (selectedRows?.length) {
      this.props.resetRowSelected();
      window.removeEventListener('beforeunload', this._handleEvent);
    }
  }

  _handleEvent = e => {
    e.returnValue = '';
  };

  fetchDevices = async params => {
    const { response, error } = await warrantyOrderService.fetchWarrantyDeviceList(params);
    if (error) {
      message.error(error);
      return;
    }
    this.setState({ deviceList: response.data });
  };

  setDeviceList = values => {
    this.setState(prevState => ({
      deviceList: [...prevState.deviceList, ...values],
    }));
  };

  handleDelete = key => {
    key
      ? this.setState(prevState => ({
          deviceList: prevState.deviceList.filter(item => item.guid !== key),
        }))
      : this.setState({ deviceList: [] });
  };

  editModelVisible = () => {
    this.setState({ modelVisible: !this.state.modelVisible });
  };

  handleAdd = async () => {
    const { form, mode } = this.props;
    const { deviceList, orderNo, modelVisible } = this.state;
    this.setState({ loading: true });
    const q = getQ(deviceList, form.getFieldsValue());
    const warrantyOrderNo =
      mode === 'new'
        ? await createOrder(q)
        : await rePostOrder({
            ...q,
            orderNo,
          });
    if (warrantyOrderNo) {
      modelVisible && this.editModelVisible();
      setTimeout(() => {
        this.props.redirect(
          urlsUtil.generateWarrantyOrderDetailUrl({
            id: warrantyOrderNo,
          })
        );
      }, 1.5 * 1000);
    }
    this.setState({ loading: false });
  };

  render() {
    const {
      form,
      history,
      warrantyDetail: {
        orderTitle,
        blockGuid,
        contractNo,
        warrantyFee,
        warrantyVendor,
        startDate,
        endDate,
        remarks,
      },
    } = this.props;
    const { loading, deviceList, modelVisible, num } = this.state;
    const { getFieldDecorator, validateFields, getFieldValue } = form;

    return (
      <GutterWrapper mode="vertical">
        <TinyCard title="基本信息">
          <Form colon={false} {...formItemLayout}>
            <Form.Item label="订单标题">
              {getFieldDecorator('orderTitle', {
                rules: [
                  { required: true, message: '订单标题为必填！' },
                  {
                    max: 20,
                    message: '最多输入 20 个字符！',
                  },
                ],
                initialValue: orderTitle,
              })(<Input allowClear style={{ width: 468 }} />)}
            </Form.Item>
            <Form.Item label="位置">
              {getFieldDecorator('location', {
                rules: [
                  {
                    required: true,
                    message: '位置需选至楼栋！',
                  },
                ],
                initialValue: blockGuid?.split('.'),
              })(
                <LocationCascader
                  changeOnSelect={false}
                  currentAuthorize
                  onChange={value => {
                    value.join('.') !== getFieldValue('location')?.join('.') &&
                      this.setState({ deviceList: [] });
                  }}
                  style={{ width: 205 }}
                />
              )}
            </Form.Item>
            <Form.Item label="维保合同号">
              {getFieldDecorator('contractNo', {
                rules: [
                  { required: true, message: '维保合同号为必填！' },
                  {
                    max: 20,
                    message: '最多输入 20 个字符！',
                  },
                ],
                initialValue: contractNo,
              })(<Input allowClear style={{ width: 468 }} />)}
            </Form.Item>
            <Form.Item label="维保价格">
              {getFieldDecorator('warrantyFee', {
                initialValue: warrantyFee && warrantyFee / 100,
              })(
                <InputNumber
                  min={0}
                  max={999999999.99}
                  precision={2}
                  formatter={val => `￥${val}`}
                  parser={value => {
                    let number = value;
                    Array.from('￥').forEach(char => {
                      number = number.replace(char, '');
                    });
                    return number;
                  }}
                  style={{ width: 205 }}
                />
              )}
              <Tooltip title="若知晓请务必如实填写">
                <QuestionCircleOutlined style={{ paddingLeft: 8 }} />
              </Tooltip>
            </Form.Item>
            <Form.Item label="维保厂商">
              {getFieldDecorator('warrantyVendor', {
                rules: [{ required: true, message: '维保厂商为必选！' }],
                initialValue: warrantyVendor,
              })(<VendorSelect style={{ width: 205 }} />)}
            </Form.Item>
            <Form.Item label="维保起止日期">
              {getFieldDecorator('warrantyTime', {
                rules: [
                  {
                    required: true,
                    message: '维保起止日期为必选！',
                  },
                ],
                initialValue: startDate && [moment(startDate), moment(endDate)],
              })(<DatePicker.RangePicker allowClear format="YYYY-MM-DD" style={{ width: 468 }} />)}
            </Form.Item>
            <Form.Item label="备注">
              {getFieldDecorator('remarks', {
                initialValue: remarks,
                rules: [
                  {
                    max: 120,
                    message: '最多输入 120 个字符！',
                  },
                ],
              })(<Input.TextArea autoSize={{ minRows: 3 }} style={{ width: 468 }} />)}
            </Form.Item>
          </Form>
        </TinyCard>
        <TinyCard title="维保清单" bordered={false} bodyStyle={{ marginBottom: 40 }}>
          <TinyTable
            rowKey="guid"
            columns={getColumns(this)}
            align="left"
            dataSource={deviceList}
            actions={[
              <AddConfig
                key="add"
                dataList={deviceList}
                setDataList={this.setDeviceList}
                location={getFieldValue('location')}
              />,
              <Button
                key="remove"
                disabled={!deviceList?.length}
                onClick={e => {
                  e.persist();
                  this.handleDelete();
                }}
              >
                全部移除
              </Button>,
            ]}
          />
        </TinyCard>
        <FooterToolBar>
          <GutterWrapper>
            <Button
              loading={loading}
              type="primary"
              onClick={() => {
                validateFields(async (error, valueMap) => {
                  if (error) {
                    return;
                  }
                  if (!deviceList.length) {
                    message.error('维保清单不可为空');
                    return;
                  }
                  const num = deviceList.filter(item =>
                    moment(valueMap.warrantyTime[0].format('YYYY-MM-DD')).isBefore(
                      item.warrantyTime
                    )
                  ).length;
                  if (num) {
                    this.setState({ modelVisible: true, num });
                    return;
                  }
                  this.handleAdd();
                });
              }}
            >
              提交
            </Button>
            <Button loading={loading} onClick={() => history.goBack()}>
              取消
            </Button>
          </GutterWrapper>
        </FooterToolBar>
        <TinyModal
          width="40%"
          title="添加"
          maskClosable={false}
          destroyOnClose
          visible={modelVisible}
          onCancel={this.editModelVisible}
          onOk={this.handleAdd}
        >
          有{num}
          条记录未达合同的维保开始日期，维保订单生效后将更新过保日期和维保厂商，确认继续添加吗？
        </TinyModal>
      </GutterWrapper>
    );
  }
}
const mapStateToProps = ({
  common: { deviceCategory },
  warrantyOrder: { warrantyDetail, selectedRows },
}) => {
  return {
    warrantyDetail,
    normalizedList: deviceCategory?.normalizedList,
    selectedRows,
  };
};
const mapDispatchToProps = {
  redirect: redirectActionCreator,
  syncCommonData: syncCommonDataActionCreator,
  fetchWarrantyDetail,
  resetRowSelected: warrantyOrderActions.resetRowSelected,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(Form.create()(withRouter(CreateWarrantyOrder)));

async function createOrder(data) {
  const { response, error } = await warrantyOrderService.createWarrantyOrder({
    ...data,
    startDate: moment(data.startDate).startOf('day').valueOf(),
    endDate: moment(data.endDate).endOf('day').valueOf(),
  });
  if (error) {
    message.error(error);
    return;
  }
  message.success('创建成功！');
  return response;
}

async function rePostOrder(data) {
  const { response, error } = await warrantyOrderService.rePostWarrantyOrder(data);
  if (error) {
    message.error(error);
    return;
  }
  message.success('重新发起成功！');
  return response;
}

function getQ(
  deviceList,
  { orderTitle, contractNo, location, warrantyFee, warrantyVendor, warrantyTime, remarks }
) {
  return {
    orderTitle,
    contractNo,
    idcTag: location[0],
    blockGuid: location.join('.'),
    warrantyFee: warrantyFee && warrantyFee * 100,
    warrantyVendor,
    startDate: warrantyTime[0].format('YYYY-MM-DD'),
    endDate: warrantyTime[1].format('YYYY-MM-DD'),
    remarks,
    deviceGuidList: deviceList.map(item => item.guid),
  };
}
