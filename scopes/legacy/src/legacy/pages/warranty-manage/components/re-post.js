import React, { Component } from 'react';
import { connect } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';

import { redirectActionCreator } from '@manyun/dc-brain.legacy.redux/actions/routerActions';
import * as urlsUtil from '@manyun/dc-brain.legacy.utils/urls';

function CheckUserIdButton(props) {
  const [authorized] = useAuthorized({ checkByUserId: props.data.applyStaffId });

  return (
    authorized && (
      <Button
        compact={props.compact}
        type={props.type}
        key="rePost"
        onClick={() => {
          props.redirect(
            urlsUtil.generateWarrantyOrderEditUrl({
              id: props.data.orderNo,
            })
          );
        }}
      >
        重新发起
      </Button>
    )
  );
}

export class RePostButton extends Component {
  render() {
    return <CheckUserIdButton {...this.props} />;
  }
}

const mapDispatchToProps = {
  redirect: redirectActionCreator,
};

export default connect(null, mapDispatchToProps)(RePostButton);
