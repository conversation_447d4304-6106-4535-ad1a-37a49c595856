import React, { Component } from 'react';
import { connect } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { Modal } from '@manyun/base-ui.ui.modal';

// Deprecated, replace with "useAuthorized" hook
// import { Authorize } from '@manyun/base-ui.ui.authorize';
import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';

import { revokeWarrantyOrder } from '@manyun/dc-brain.legacy.redux/actions/warrantyOrderActions';

function CheckUserIdButton({ userId, orderNo, type, revoke, compact }) {
  const [authorized] = useAuthorized({ checkByUserId: userId });

  return (
    authorized && (
      <Button
        compact={compact}
        type={type}
        key="revoke"
        onClick={() => {
          Modal.confirm({
            title: `确认要撤回${orderNo}的订单吗？`,
            content: '撤回后数据将不可恢复。',
            okText: '确定',
            cancelText: '取消',
            onOk() {
              revoke({
                orderNo,
                type,
              });
            },
          });
        }}
      >
        撤回
      </Button>
    )
  );
}

export class RevokeButton extends Component {
  render() {
    const {
      type,
      data: { applyStaffId, orderNo },
      revokeWarrantyOrder,
    } = this.props;

    return (
      <CheckUserIdButton
        userId={applyStaffId}
        type={type}
        orderNo={orderNo}
        revoke={revokeWarrantyOrder}
      />
    );
  }
}

const mapDispatchToProps = {
  revokeWarrantyOrder,
};

export default connect(null, mapDispatchToProps)(RevokeButton);
