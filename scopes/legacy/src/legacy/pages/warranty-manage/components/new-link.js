import React, { Component } from 'react';
import { connect } from 'react-redux';

import uniq from 'lodash/uniq';

import { Button } from '@manyun/base-ui.ui.button';
import { notification } from '@manyun/base-ui.ui.notification';

import * as urls from '@manyun/dc-brain.legacy.constants/urls';
import { redirectActionCreator } from '@manyun/dc-brain.legacy.redux/actions/routerActions';
import { warrantyOrderActions } from '@manyun/dc-brain.legacy.redux/actions/warrantyOrderActions';

class NewLink extends Component {
  handleClick = () => {
    const { selectedRows, saveWarrantyDetail, redirect, updateRowSelected } = this.props;
    const blocks = selectedRows?.map(row => row.spaceGuid.blockGuid);
    if (uniq(blocks).length > 1) {
      notification.warning({
        message: '新建维保订单失败',
        description: '请勾选相同楼栋的资产记录',
      });
    } else {
      saveWarrantyDetail({ blockGuid: blocks?.[0] });
      updateRowSelected(selectedRows);
      redirect(urls.WARRANTY_MANAGE_NEW);
    }
  };

  render() {
    return (
      <Button type="primary" onClick={this.handleClick}>
        新建维保订单
      </Button>
    );
  }
}

const mapDispatchToProps = {
  updateRowSelected: warrantyOrderActions.updateRowSelected,
  saveWarrantyDetail: warrantyOrderActions.saveWarrantyDetail,
  redirect: redirectActionCreator,
};

export default connect(null, mapDispatchToProps)(NewLink);
