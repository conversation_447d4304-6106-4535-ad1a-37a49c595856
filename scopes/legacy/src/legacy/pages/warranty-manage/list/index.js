import React, { Component } from 'react';
import { connect, useDispatch } from 'react-redux';
import { Link } from 'react-router-dom';

import { FiltersForm, Form, Select } from '@galiojs/awesome-antd';
import moment from 'moment';

import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Input } from '@manyun/base-ui.ui.input';

import { VendorSelect } from '@manyun/crm.ui.vendor-select';

import {
  GutterWrapper,
  LocationCascader,
  TinyCard,
  TinyTable,
  UserLink,
  UserSelect,
} from '@manyun/dc-brain.legacy.components';
import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';
import {
  fetchWarrantyOrderList,
  setWarrantyPagination,
  warrantyOrderActions,
} from '@manyun/dc-brain.legacy.redux/actions/warrantyOrderActions';
import * as urlsUtil from '@manyun/dc-brain.legacy.utils/urls';

import NewLink from '../components/new-link';
import RePostButton from '../components/re-post';
import RevokeButton from '../components/revoke';
import { BIZ_STATUS_KEY_MAP, BIZ_STATUS_OPTIONS, BizStatus } from '../constants';

const getColumns = () => [
  {
    title: '订单ID',
    dataIndex: 'orderNo',
    fixed: 'left',
    render: text => <Link to={urlsUtil.generateWarrantyOrderDetailUrl({ id: text })}>{text}</Link>,
  },
  {
    title: '订单标题',
    dataIndex: 'orderTitle',
  },
  {
    title: '位置',
    dataIndex: 'blockGuid',
  },
  {
    title: '维保资产数量',
    dataIndex: 'deviceCount',
  },
  {
    title: '维保合同号',
    dataIndex: 'contractNo',
  },
  {
    title: '维保厂商',
    dataIndex: 'warrantyVendor',
  },
  {
    title: '维保起止日期',
    dataIndex: 'warrantyTime',
    render: (_, record) =>
      `${moment(record.startDate).format('YYYY-MM-DD')} ~ ${moment(record.endDate).format(
        'YYYY-MM-DD'
      )}`,
  },
  {
    title: '创建人',
    dataIndex: 'applyStaffId',
    render: (text, { applyStaffName }) => {
      return <UserLink userId={text} userName={applyStaffName} />;
    },
  },
  {
    title: '创建时间',
    dataIndex: 'gmtModified',
    dataType: 'datetime',
  },
  {
    title: '状态',
    dataIndex: 'orderStatus',
    render: text => {
      return BizStatus(text);
    },
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    render: (_, record) => {
      if (record.orderStatus === BIZ_STATUS_KEY_MAP.REVOKE) {
        return <RePostButton data={record} type="link" compact={true} />;
      } else if (record.orderStatus === BIZ_STATUS_KEY_MAP.APPROVING) {
        return <RevokeButton data={record} type="link" compact={true} />;
      }
      return BLANK_PLACEHOLDER;
    },
  },
];

class WarrantyListLegacy extends Component {
  componentDidMount() {
    this.props.fetchWarrantyOrderList();
  }

  handleSearch = () => {
    this.props.setWarrantyPagination({ pageNum: 1, pageSize: 10 });
  };

  handleReset = () => {
    this.props.dispatch(warrantyOrderActions.resetSearchValues());
    this.props.setWarrantyPagination({ pageNum: 1, pageSize: 10 });
  };

  render() {
    const {
      form,
      orderPage,
      updateSearchValues,
      fields,
      pagination,
      loading,
      setWarrantyPagination,
    } = this.props;
    return (
      <GutterWrapper mode="vertical">
        <TinyCard>
          <FiltersForm
            form={form}
            fields={Object.keys(fields).map(name => {
              const field = fields[name];
              return {
                ...field,
                name: name.split('.'),
              };
            })}
            onFieldsChange={changedFields => {
              updateSearchValues(
                changedFields.reduce((mapper, field) => {
                  const name = field.name.join('.');
                  mapper[name] = {
                    ...field,
                    name,
                  };
                  return mapper;
                }, {})
              );
            }}
            onSearch={this.handleSearch}
            onReset={this.handleReset}
            items={[
              {
                label: '订单ID',
                name: 'orderNo',
                control: <Input allowClear />,
              },
              {
                label: '位置',
                name: 'location',
                control: <LocationCascader currentAuthorize />,
              },
              {
                label: '维保合同号',
                name: 'contractNo',
                control: <Input allowClear />,
              },
              {
                label: '维保厂商',
                name: 'warrantyVendor',
                control: <VendorSelect allowClear />,
              },
              {
                label: '状态',
                name: 'orderStatus',
                control: (
                  <Select allowClear>
                    {BIZ_STATUS_OPTIONS.map(item => {
                      return (
                        <Select.Option key={item.value} value={item.value}>
                          {item.labels.label}
                        </Select.Option>
                      );
                    })}
                  </Select>
                ),
              },
              {
                label: '创建人',
                name: 'applyStaff',
                control: <UserSelect allowClear />,
              },
              {
                label: '创建时间',
                name: 'applyTime',
                span: 2,
                control: <DatePicker.RangePicker allowClear format="YYYY-MM-DD" />,
              },
            ]}
          />
        </TinyCard>
        <TinyCard>
          <TinyTable
            rowKey="orderNo"
            columns={getColumns()}
            align="left"
            scroll={{ x: 'max-content' }}
            dataSource={orderPage.list}
            loading={loading}
            actions={<NewLink />}
            pagination={{
              total: orderPage.total,
              current: pagination.pageNum,
              pageSize: pagination.pageSize,
              onChange: (current, size) =>
                setWarrantyPagination({ pageNum: current, pageSize: size }),
            }}
          />
        </TinyCard>
      </GutterWrapper>
    );
  }
}
function WarrantyList(props) {
  const [form] = Form.useForm();
  const dispatch = useDispatch();

  return <WarrantyListLegacy form={form} dispatch={dispatch} {...props} />;
}

const mapStateToProps = ({
  warrantyOrder: { warrantyOrderList, loading, searchValues, pagination },
}) => ({
  orderPage: warrantyOrderList,
  loading,
  pagination,
  fields: searchValues,
});
const mapDispatchToProps = {
  updateSearchValues: warrantyOrderActions.updateSearchValues,
  setWarrantyPagination,
  fetchWarrantyOrderList,
};

export default connect(mapStateToProps, mapDispatchToProps)(WarrantyList);
