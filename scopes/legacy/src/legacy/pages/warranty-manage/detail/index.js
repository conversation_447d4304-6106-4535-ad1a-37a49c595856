import React, { Component } from 'react';
import { connect } from 'react-redux';

import moment from 'moment';

import { message } from '@manyun/base-ui.ui.message';

import { SpaceOrDeviceLink } from '@manyun/resource-hub.ui.space-or-device-link';

import {
  ApproveLink,
  Footer<PERSON>ool<PERSON><PERSON>,
  GutterWrapper,
  TinyCard,
  TinyDescriptions,
  TinyTable,
} from '@manyun/dc-brain.legacy.components';
import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  fetchWarrantyDetail,
  warrantyOrderActions,
} from '@manyun/dc-brain.legacy.redux/actions/warrantyOrderActions';
import * as warrantyOrderService from '@manyun/dc-brain.legacy.services/warrantyOrderService';
import { getDeviceTypeName } from '@manyun/dc-brain.legacy.utils/ticket';

import RePostButton from '../components/re-post';
import RevokeButton from '../components/revoke';
import { BIZ_STATUS_KEY_MAP, BIZ_STATUS_MAP, WarrantyStatus } from '../constants';

const getColumns = ctx => [
  {
    title: '资产ID',
    dataIndex: 'assetNo',
    render: (assetNo, { guid }) => {
      if (!assetNo) {
        return BLANK_PLACEHOLDER;
      }
      return <SpaceOrDeviceLink id={guid} type="DEVICE_GUID" text={assetNo} />;
    },
  },
  {
    title: '三级分类',
    dataIndex: 'deviceType',
    render: text => getDeviceTypeName(text, ctx.props.normalizedList),
  },
  {
    title: '维保厂商',
    dataIndex: 'warrantyVendor',
  },
  {
    title: '过保日期',
    dataIndex: 'warrantyTime',
    dataType: 'date',
  },
  {
    title: '维保状态',
    dataIndex: 'warrantyStatus',
    render: warrantyStatus => {
      return WarrantyStatus(warrantyStatus);
    },
  },
];

class WarrantyDetail extends Component {
  state = {
    orderNo: '',
    pagination: {
      pageNum: 1,
      pageSize: 10,
    },
    deviceList: [],
    total: 0,
  };

  componentDidMount() {
    const { match } = this.props;
    this.props.syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });
    this.setState({ orderNo: match.params.id }, () => this.fetchDevices());
    this.props.fetchWarrantyDetail({ orderNo: match.params.id });
  }

  componentWillUnmount() {
    this.props.saveWarrantyDetail({});
  }

  fetchDevices = async () => {
    const { pagination, orderNo } = this.state;
    this.setState({ loading: true });
    const { response, error } = await warrantyOrderService.fetchWarrantyDevicePage({
      orderNo,
      ...pagination,
    });
    this.setState({ loading: false });
    if (error) {
      message.error(error);
      return;
    }
    this.setState({ deviceList: response.data, total: response.total });
  };

  paginationChangeHandler = (current, pageSize) => {
    this.setState({ pagination: { pageNum: current, pageSize } }, () => {
      this.fetchDevices();
    });
  };

  render() {
    const { warrantyDetail } = this.props;
    const { loading, deviceList, pagination, total } = this.state;

    return (
      <GutterWrapper mode="vertical">
        <TinyCard title="基本信息">
          <TinyDescriptions
            column={4}
            descriptionsItems={[
              { label: '订单ID', value: warrantyDetail.orderNo },
              { label: '订单标题', value: warrantyDetail.orderTitle },
              { label: '位置', value: warrantyDetail.blockGuid },
              { label: '状态', value: BIZ_STATUS_MAP[warrantyDetail.orderStatus]?.label },
              { label: '维保合同号', value: warrantyDetail.contractNo },
              {
                label: '维保价格',
                value:
                  typeof warrantyDetail.warrantyFee === 'number'
                    ? `${warrantyDetail.warrantyFee / 100} 元`
                    : BLANK_PLACEHOLDER,
              },
              { label: '维保厂商', value: warrantyDetail.warrantyVendor },
              {
                label: '维保起止日期',
                value:
                  warrantyDetail.startDate &&
                  `${moment(warrantyDetail.startDate).format('YYYY-MM-DD')} ~ ${moment(
                    warrantyDetail.endDate
                  ).format('YYYY-MM-DD')}`,
              },
              {
                label: '审批',
                value: <ApproveLink id={warrantyDetail.procInstanceId} />,
              },
              { label: '备注', value: warrantyDetail.remarks },
            ]}
          />
        </TinyCard>
        <TinyCard title="维保清单" bodyStyle={{ marginBottom: 40 }}>
          <TinyTable
            rowKey="guid"
            columns={getColumns(this)}
            dataSource={deviceList}
            loading={loading}
            align="left"
            pagination={{
              total,
              current: pagination.pageNum,
              pageSize: pagination.pageSize,
              onChange: this.paginationChangeHandler,
            }}
          />
        </TinyCard>
        <FooterToolBar>
          {warrantyDetail.orderStatus === BIZ_STATUS_KEY_MAP.REVOKE && (
            <RePostButton data={warrantyDetail} type="primary" />
          )}
          {warrantyDetail.orderStatus === BIZ_STATUS_KEY_MAP.APPROVING && (
            <RevokeButton data={warrantyDetail} type="primary" />
          )}
        </FooterToolBar>
      </GutterWrapper>
    );
  }
}

const mapStateToProps = ({ common: { deviceCategory }, warrantyOrder: { warrantyDetail } }) => ({
  warrantyDetail,
  normalizedList: deviceCategory?.normalizedList,
});
const mapDispatchToProps = {
  fetchWarrantyDetail,
  syncCommonData: syncCommonDataActionCreator,
  saveWarrantyDetail: warrantyOrderActions.saveWarrantyDetail,
};

export default connect(mapStateToProps, mapDispatchToProps)(WarrantyDetail);
