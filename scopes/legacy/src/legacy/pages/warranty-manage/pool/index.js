import { FiltersForm, Form, Select } from '@galiojs/awesome-antd';
import React, { Component } from 'react';
import { connect, useDispatch } from 'react-redux';
import { Link } from 'react-router-dom';

import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Input } from '@manyun/base-ui.ui.input';
import { VendorModelSelect } from '@manyun/crm.ui.vendor-model-select';
import { VendorSelect } from '@manyun/crm.ui.vendor-select';
import { LayoutContent } from '@manyun/dc-brain.ui.layout';
import { generateDeviceRecordRoutePath } from '@manyun/resource-hub.route.resource-routes';

import {
  GutterWrapper,
  LocationCascader,
  TinyCard,
  TinyTable,
} from '@manyun/dc-brain.legacy.components';
import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';
import {
  fetchWarrantyPoolList,
  setWarrantyPoolPagination,
  warrantyPoolActions,
} from '@manyun/dc-brain.legacy.redux/actions/warrantyPoolAactions';

import NewLink from '../components/new-link';
import { WARRANTY_STATUS_MAP, WARRANTY_STATUS_OPTIONS } from '../constants';

const getColumns = () => [
  {
    title: '资产ID',
    dataIndex: 'assetNo',
    render: (text, record) => {
      if (!text) {
        return BLANK_PLACEHOLDER;
      }

      return <Link to={generateDeviceRecordRoutePath({ guid: record.guid })}>{text}</Link>;
    },
  },
  {
    title: '机房编号',
    dataIndex: ['spaceGuid', 'idcTag'],
  },
  {
    title: '楼栋编号',
    dataIndex: ['spaceGuid', 'blockTag'],
  },
  {
    title: '包间编号',
    dataIndex: ['spaceGuid', 'roomTag'],
  },
  {
    title: '厂商',
    dataIndex: 'vendor',
  },
  {
    title: '型号',
    dataIndex: 'productModel',
  },
  {
    title: '维保厂商',
    dataIndex: 'warrantyVendor',
  },
  {
    title: '过保日期',
    dataIndex: 'warrantyTime',
    dataType: 'date',
  },
  {
    title: '维保状态',
    dataIndex: 'warrantyStatus',
    render: text => (
      <span style={{ color: WARRANTY_STATUS_MAP[text?.code]?.color }}>{text.name}</span>
    ),
  },
];

class WarrantyPoolnLegacy extends Component {
  state = {
    selectedRows: [],
  };

  componentDidMount() {
    this.props.fetchWarrantyPoolList();
  }

  handleSearch = () => {
    this.props.setWarrantyPoolPagination({
      pageNum: 1,
      pageSize: 10,
    });
  };

  handleReset = () => {
    this.props.dispatch(warrantyPoolActions.resetSearchValues());
    this.props.setWarrantyPoolPagination({
      pageNum: 1,
      pageSize: 10,
    });
  };

  onSelectChange = (_, selectedRows) => {
    this.setState({ selectedRows });
  };

  render() {
    const {
      form,
      poolPage,
      updateSearchValues,
      fields,
      pagination,
      loading,
      setWarrantyPoolPagination,
    } = this.props;
    const { selectedRows } = this.state;

    return (
      <LayoutContent pageCode="page_pre-wrranty-pool">
        <GutterWrapper mode="vertical">
          <TinyCard>
            <FiltersForm
              form={form}
              fields={Object.keys(fields).map(name => {
                const field = fields[name];
                return {
                  ...field,
                  name: name.split('.'),
                };
              })}
              items={[
                {
                  label: '资产ID',
                  name: 'assetNo',
                  control: <Input allowClear />,
                },
                {
                  label: '位置',
                  name: 'spaceGuidList',
                  control: <LocationCascader currentAuthorize showRoom allowClear />,
                },
                {
                  label: '维保厂商',
                  name: 'warrantyVendor',
                  control: <VendorSelect allowClear />,
                },
                {
                  label: '维保状态',
                  name: 'warrantyStatus',
                  control: (
                    <Select allowClear>
                      {WARRANTY_STATUS_OPTIONS.map(item => {
                        return (
                          <Select.Option key={item.value} value={item.value}>
                            {item.labels.label}
                          </Select.Option>
                        );
                      })}
                    </Select>
                  ),
                },
                {
                  label: '过保日期',
                  name: 'warrantyTimeRange',
                  span: 2,
                  control: <DatePicker.RangePicker allowClear format="YYYY-MM-DD" />,
                },
                {
                  label: '厂商、型号',
                  name: 'vendorModel',
                  control: <VendorModelSelect numbered allowClear />,
                },
              ]}
              onFieldsChange={changedFields => {
                updateSearchValues(
                  changedFields.reduce((mapper, field) => {
                    const name = field.name.join('.');
                    mapper[name] = {
                      ...field,
                      name,
                    };
                    return mapper;
                  }, {})
                );
              }}
              onSearch={this.handleSearch}
              onReset={this.handleReset}
            />
          </TinyCard>
          <TinyCard>
            <TinyTable
              rowKey="guid"
              columns={getColumns()}
              align="left"
              dataSource={poolPage.list}
              loading={loading}
              actions={<NewLink selectedRows={selectedRows} />}
              selectRowsSpreadPage
              rowSelection={{
                selectedRows,
                onChange: this.onSelectChange,
              }}
              pagination={{
                total: poolPage.total,
                current: pagination.pageNum,
                pageSize: pagination.pageSize,
                onChange: (current, size) =>
                  setWarrantyPoolPagination({ pageNum: current, pageSize: size }),
              }}
            />
          </TinyCard>
        </GutterWrapper>
      </LayoutContent>
    );
  }
}

function WarrantyPool(props) {
  const [form] = Form.useForm();
  const dispatch = useDispatch();

  return <WarrantyPoolnLegacy form={form} dispatch={dispatch} {...props} />;
}

const mapStateToProps = ({
  warrantyPool: { warrantyPoolList, searchValues, loading, pagination },
}) => ({
  poolPage: warrantyPoolList,
  fields: searchValues,
  loading,
  pagination,
});
const mapDispatchToProps = {
  updateSearchValues: warrantyPoolActions.updateSearchValues,
  fetchWarrantyPoolList,
  setWarrantyPoolPagination,
};

export default connect(mapStateToProps, mapDispatchToProps)(WarrantyPool);
