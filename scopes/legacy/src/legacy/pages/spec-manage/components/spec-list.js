import React, { Component } from 'react';
import { connect } from 'react-redux';

import { Input } from '@manyun/base-ui.ui.input';
import { Tree } from '@manyun/base-ui.ui.tree';

import { TinyCard } from '@manyun/dc-brain.legacy.components';
import { DEVICE_SPACE_TYPE_KEY_MAP } from '@manyun/dc-brain.legacy.constants/enums/deviceType';
import {
  MetaCategoryAction,
  MetaCategoryNumAction,
  fetchSpecDetail,
  specActions,
} from '@manyun/dc-brain.legacy.redux/actions/specActions';

const dataList = [];
const getParentKey = (key, tree) => {
  let parentKey;
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i];
    if (node.children) {
      if (node.children.some(item => item.metaCode + '-' + item.metaType === key)) {
        parentKey = node.metaCode + '-' + node.metaType;
      } else if (getParentKey(key, node.children)) {
        parentKey = getParentKey(key, node.children);
      }
    }
  }
  return parentKey;
};

class SpecList extends Component {
  state = {
    expandedKeys: [],
    searchValue: '',
    autoExpandParent: true,
  };

  componentDidMount() {
    this.props.MetaCategoryAction();
    this.props.MetaCategoryNumAction();
  }

  onExpand = expandedKeys => {
    this.setState({
      expandedKeys,
      autoExpandParent: false,
    });
  };

  generateList = (data = []) => {
    for (let i = 0; i < data.length; i++) {
      const node = data[i];
      const { metaCode, metaName, metaType } = node;
      dataList.push({ key: metaCode + '-' + metaType, title: metaName });
      if (node.children) {
        this.generateList(node.children);
      }
    }
  };

  selectTreeNode = (selectedKeys, info) => {
    const selected = selectedKeys.join('-').split('-');
    const code = selected[0];
    const type = selected[1];
    const dataTitle = info.node.props['data-title'];
    if (type === 'C2') {
      this.props.fetchSpecDetail({ deviceType: code });
      this.props.saveType({ code });
      this.props.selectCategory({
        selectTitle: dataTitle,
        selectType: type,
        deviceType: code,
      });
    }
  };

  onChange = e => {
    const { value } = e.target;
    const { categoryList } = this.props;
    this.generateList(categoryList);

    const expandedKeys = dataList
      .map(item => {
        if (item.title.indexOf(value) > -1) {
          return getParentKey(item.key, categoryList);
        }
        return null;
      })
      .filter((item, i, self) => item && self.indexOf(item) === i);
    this.setState({
      expandedKeys,
      searchValue: value,
      autoExpandParent: true,
    });
  };
  loop = data => {
    const { categoryNum } = this.props;
    const { searchValue } = this.state;
    const list = data.map(item => {
      let index = -1;
      let beforeStr;
      let afterStr;
      let num = '';
      if (categoryNum[item.metaCode] === undefined) {
        num = 0;
      } else {
        num = categoryNum[item.metaCode];
      }
      if (searchValue) {
        index = item.metaName.indexOf(searchValue);
        beforeStr = item.metaName.substr(0, index);
        afterStr = item.metaName.substr(index + searchValue.length);
      }

      const title =
        index > -1 ? (
          <span>
            {beforeStr}
            <span>{searchValue}</span>
            {afterStr}
            {item.metaType === 'C2' && ' ' + num}
          </span>
        ) : item.metaType === 'C2' ? (
          <span>{item.metaName + ' ' + num}</span>
        ) : (
          <span>{item.metaName}</span>
        );

      if (item.children) {
        return (
          <Tree.TreeNode
            key={item.metaCode + '-' + item.metaType}
            title={title}
            data-title={item.metaName}
          >
            {this.loop(item.children)}
          </Tree.TreeNode>
        );
      }

      return (
        <Tree.TreeNode
          key={item.metaCode + '-' + item.metaType}
          title={item.metaName}
          data-title={item.metaName}
        />
      );
    });
    return list;
  };

  render() {
    const { autoExpandParent, expandedKeys } = this.state;
    const { categoryList } = this.props;

    return (
      <TinyCard
        title="设备类型查询"
        bodyStyle={{ height: 'calc(var(--content-height) - 57px)', overflow: 'auto' }}
      >
        <Input.Search
          style={{ marginBottom: 8 }}
          placeholder="请输入关键字"
          onChange={this.onChange}
        />
        <Tree
          onExpand={this.onExpand}
          expandedKeys={expandedKeys}
          autoExpandParent={autoExpandParent}
          onSelect={this.selectTreeNode}
        >
          {this.loop(categoryList)}
        </Tree>
      </TinyCard>
    );
  }
}
const mapStateToProps = ({ specManage: { categoryList, categoryNum } }) => {
  const treeData = categoryList.filter(
    ({ metaStyle }) => metaStyle !== DEVICE_SPACE_TYPE_KEY_MAP.SPACE
  );
  return {
    categoryList: treeData,
    categoryNum,
  };
};

const mapDispatchToProps = {
  MetaCategoryAction,
  MetaCategoryNumAction,
  fetchSpecDetail,
  saveType: specActions.saveType,
  selectCategory: specActions.selectCategoryPoint,
};

export default connect(mapStateToProps, mapDispatchToProps)(SpecList);
