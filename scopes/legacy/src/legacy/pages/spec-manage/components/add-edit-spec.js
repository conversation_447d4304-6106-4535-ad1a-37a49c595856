import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import { Select } from '@galiojs/awesome-antd';

import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Typography } from '@manyun/base-ui.ui.typography';

import { TinyDrawer } from '@manyun/dc-brain.legacy.components';
import { METADATA_TYPE } from '@manyun/dc-brain.legacy.constants';
import { getMetadataByType } from '@manyun/dc-brain.legacy.redux/actions/eventCenterActions';
import {
  MetaCategoryNumAction,
  fetchSpecDetail,
} from '@manyun/dc-brain.legacy.redux/actions/specActions';
import * as specService from '@manyun/dc-brain.legacy.services/specService';

class AddEditSpec extends Component {
  state = {
    loading: false,
    specCode: '',
  };

  changeVisible = async () => {
    this.setState({ createVisible: !this.state.createVisible });
  };

  getSpecName = () => {
    if (this.props.specNameList.length) {
      return false;
    }
    this.props.getMetadataByType(METADATA_TYPE['SPEC_NAME']);
  };

  getSpecUnit = () => {
    if (this.props.specUnitList.length) {
      return false;
    }
    this.props.getMetadataByType(METADATA_TYPE['SPEC_UNIT']);
  };

  handleSubmit = () => {
    const { deviceType, info } = this.props;
    this.props.form.validateFields(async (err, values) => {
      if (err) {
        return;
      }
      const data = this.props.form.getFieldsValue();
      if (info) {
        data.id = info.id;
        data.unit = data.specUnit;
        const { error } = await specService.editSpec(data);
        if (error) {
          message.error(error);
          return;
        }
        message.success('编辑规格成功');
        this.changeVisible();
        this.props.fetchSpecDetail({ deviceType });
        return;
      }
      data.specCode = data.spec.key;
      data.specName = data.spec.label;
      const { error } = await specService.addSpec({ deviceType, ...data });
      if (error) {
        message.error(error);
        return;
      }
      this.changeVisible();
      this.props.MetaCategoryNumAction();
      this.props.fetchSpecDetail({ deviceType });
      message.success('添加规格成功');
    });
  };

  render() {
    const { form, text, type, title, info = {}, specNameList, specUnitList } = this.props;
    const { createVisible } = this.state;
    const { getFieldDecorator } = form;

    return [
      <Button key="btn" type={type} onClick={this.changeVisible}>
        {text}
      </Button>,
      <TinyDrawer
        width={416}
        title={<Typography.Title level={4}>{title}</Typography.Title>}
        visible={createVisible}
        onClose={this.changeVisible}
        onSubmit={this.handleSubmit}
        onCancel={this.changeVisible}
        key="drawer"
        destroyOnClose
      >
        <Form colon={false}>
          <Form.Item label="规格名称">
            {getFieldDecorator('spec', {
              rules: [{ required: true, message: '规格名称必选！' }],
              initialValue: info.specName && {
                label: info.specName,
                key: info.specCode,
              },
            })(
              <Select
                labelInValue
                onFocus={this.getSpecName}
                style={{ width: 200 }}
                disabled={!!info.specName}
              >
                {specNameList.map(item => (
                  <Select.Option key={item.metaCode}>{item.metaName}</Select.Option>
                ))}
              </Select>
            )}
          </Form.Item>
          <Form.Item label="规格单位">
            {getFieldDecorator('specUnit', {
              rules: [{ required: true, message: '规格单位必填！' }],
              initialValue: info.specUnit,
            })(
              <Select onFocus={this.getSpecUnit} style={{ width: 200 }} showSearch>
                {specUnitList.map(item => (
                  <Select.Option key={item.metaCode}>{item.metaName}</Select.Option>
                ))}
              </Select>
            )}
          </Form.Item>
          <Form.Item label="是否必选">
            {getFieldDecorator('required', {
              rules: [{ required: true, message: '请选择是否必选' }],
              initialValue: info.required,
            })(
              <Radio.Group>
                <Radio value={true}>是</Radio>
                <Radio value={false}>否</Radio>
              </Radio.Group>
            )}
          </Form.Item>
          <Form.Item label="备注">
            {getFieldDecorator('description', {
              initialValue: info.description,
              rules: [
                {
                  max: 50,
                  message: '最多输入 50 个字符！',
                },
              ],
            })(<Input.TextArea autoSize={{ minRows: 3 }} />)}
          </Form.Item>
        </Form>
      </TinyDrawer>,
    ];
  }
}

const mapStateToProps = ({
  eventCenter: { specNameList, specUnitList },
  specManage: { createVisible, deviceType },
}) => ({
  specNameList,
  specUnitList,
  createVisible,
  deviceType,
});
const mapDispatchToProps = {
  fetchSpecDetail,
  getMetadataByType: getMetadataByType,
  MetaCategoryNumAction,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(Form.create({ name: 'spec_add_edit' })(AddEditSpec));
