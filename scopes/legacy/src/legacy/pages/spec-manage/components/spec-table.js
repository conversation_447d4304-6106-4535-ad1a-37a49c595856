import React, { Component } from 'react';
import { connect } from 'react-redux';

import { But<PERSON> } from '@manyun/base-ui.ui.button';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Divider } from '@manyun/base-ui.ui.divider';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';

import { Ellip<PERSON>, GutterWrapper, TinyCard, TinyTable } from '@manyun/dc-brain.legacy.components';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  MetaCategoryNumAction,
  deleteSpecAction,
  fetchSpecDetail,
  specActions,
} from '@manyun/dc-brain.legacy.redux/actions/specActions';
import { getDeviceTypeFullMetaName } from '@manyun/dc-brain.legacy.utils/deviceType';

import AddEditSpec from '../components/add-edit-spec';

const GetColumns = ctx => {
  const [updateAuthorized] = useAuthorized({ checkByCode: 'ele_spec_update' });
  const [deleteAuthorized] = useAuthorized({ checkByCode: 'ele_spec_delete' });

  return [
    {
      title: '规格名称',
      dataIndex: 'specName',
    },
    {
      title: '单位',
      dataIndex: 'specUnit',
    },
    {
      title: '是否必填',
      dataIndex: 'required',
      render: required => (required ? '是' : '否'),
    },
    {
      title: '更新时间',
      dataIndex: 'gmtModified',
    },
    {
      title: '更新人',
      dataIndex: 'operatorName',
    },
    {
      title: '备注',
      dataIndex: 'description',
      render: description => (
        <Ellipsis lines={1} tooltip>
          {description}
        </Ellipsis>
      ),
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 100,
      fixed: 'right',
      render: (__, record) => (
        <>
          {updateAuthorized && (
            <AddEditSpec text="编辑" title="编辑规格" type="link" info={record} />
          )}
          {deleteAuthorized && (
            <>
              <Divider type="vertical" />
              <DeleteConfirm
                targetName={record.specName}
                onOk={({ reason }) => ctx.delete(record, reason)}
              >
                <Button type="link" compact>
                  删除
                </Button>
              </DeleteConfirm>
            </>
          )}
        </>
      ),
    },
  ];
};

class SpecTable extends Component {
  state = {
    pageNo: 1,
  };

  componentDidMount() {
    this.props.syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });
  }

  componentDidUpdate(prevProps) {
    if (!this.props.selectCategoryPoint || !prevProps.selectCategoryPoint) {
      return;
    }
    if (this.props.selectCategoryPoint.deviceType !== prevProps.selectCategoryPoint.deviceType) {
      this.setState({ pageNo: 1 });
    }
  }

  delete = ({ id }, operatorNotes) => {
    const {
      selectCategoryPoint: { deviceType },
      deleteSpecAction,
      fetchSpecs,
    } = this.props;

    return new Promise(resolve => {
      deleteSpecAction({
        params: { id, operatorNotes },
        successCb: () => {
          resolve(true);
          this.props.MetaCategoryNumAction();
          fetchSpecs({ deviceType });
        },
        errorCb: () => {
          resolve(false);
        },
      });
    });
  };

  onChangePageNo = pageNum => {
    this.setState({
      pageNo: pageNum,
    });
  };

  render() {
    const { loading, specDetail, selectCategoryPoint, normalizedList, authorized } = this.props;
    const { pageNo } = this.state;
    return (
      <TinyCard
        title={'基本信息'}
        bodyStyle={{
          height: 'calc(var(--content-height) - 57px)',
          overflowY: 'auto',
        }}
      >
        <GutterWrapper mode="vertical">
          {selectCategoryPoint.selectType === 'C2' ? (
            <>
              <Descriptions>
                <Descriptions.Item label="设备类型">
                  {getDeviceTypeFullMetaName(selectCategoryPoint.deviceType, normalizedList)}
                </Descriptions.Item>
              </Descriptions>
              {authorized && <AddEditSpec text="新建" title="新建规格" type="primary" />}
            </>
          ) : null}

          <TinyTable
            rowKey="id"
            loading={loading}
            dataSource={specDetail.data}
            columns={GetColumns(this)}
            scroll={{ x: true }}
            align={'left'}
            pagination={{
              total: specDetail.data.length,
              showTotal: () => `共${specDetail.data.length}条`,
              current: pageNo,
              onChange: this.onChangePageNo,
            }}
          />
        </GutterWrapper>
      </TinyCard>
    );
  }
}

const mapStateToProps = ({
  specManage: { loading, specDetail, selectCategoryPoint },
  common: { deviceCategory },
}) => ({
  loading,
  specDetail,
  selectCategoryPoint,
  normalizedList: deviceCategory?.normalizedList,
});

const mapDispatchToProps = {
  deleteSpecAction,
  ViewVisible: specActions.ViewVisible,
  fetchSpecs: fetchSpecDetail,
  fetchSpecDetailSuccess: specActions.fetchSpecDetailSuccess,
  syncCommonData: syncCommonDataActionCreator,
  MetaCategoryNumAction,
};

const ConnectSpecTable = connect(mapStateToProps, mapDispatchToProps)(SpecTable);

export default function AuthorizedSpecTable() {
  const [authorized] = useAuthorized({ checkByCode: 'ele_spec_add' });

  return <ConnectSpecTable authorized={authorized} />;
}
