import React, { Component } from 'react';

import { Col, Row } from '@manyun/base-ui.ui.grid';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';

import SpecList from './components/spec-list';
import SpecTable from './components/spec-table';

export default class SpecManage extends Component {
  render() {
    return (
      <GutterWrapper mode="vertical">
        <Row gutter={16}>
          <Col span={5}>
            <SpecList />
          </Col>
          <Col span={19}>
            <SpecTable />
          </Col>
        </Row>
      </GutterWrapper>
    );
  }
}
