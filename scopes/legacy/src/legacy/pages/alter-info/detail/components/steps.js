import React from 'react';

import moment from 'moment';

import { Steps } from '@manyun/base-ui.ui.steps';

import { TinyCard } from '@manyun/dc-brain.legacy.components/tiny-card';

import { TICKET_STEP_STATUS_KEY_MAP } from '../../constants';

const { Step } = Steps;

export function TicketSteps({ basicInfo = {} }) {
  const stepCurrent = TICKET_STEP_STATUS_KEY_MAP[basicInfo.taskStatus] || 0;
  return (
    <TinyCard bodyStyle={{ padding: '16px 20px' }}>
      <Steps current={stepCurrent === 4 ? 10 : stepCurrent} direction="vertical">
        {basicInfo.alterInfo.operationRecords.map(element => (
          <Step
            title={
              element.operationName === null
                ? element.userName + '的' + element.operationType
                : element.operationName
            }
            description={
              <div style={{ display: 'flex', justifyContent: 'space-between	' }}>
                <span>
                  {element.userName === null
                    ? element.operationResult
                    : element.userName + '(' + element.operationResult + ')'}
                </span>
                {/* <div style={{ fontSize: '12px' }}>{basicInfo.createName}</div> */}
                <span style={{ fontSize: '12px' }}>
                  {moment(basicInfo.gmtCreate).format('YYYY-MM-DD HH:mm:ss')}
                </span>
              </div>
            }
          />
        ))}
      </Steps>
    </TinyCard>
  );
}

export default TicketSteps;
