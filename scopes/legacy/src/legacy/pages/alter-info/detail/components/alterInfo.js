import React from 'react';

import { ApproveLink, TinyDescriptions, UserLink } from '@manyun/dc-brain.legacy.components';

import { ALTER_TYPE_MAP, BIZ_STATUS_MAP } from '../../constants';

export function AlterInfo({ basicInfo = {}, ticketTypes }) {
  const alterInfo = basicInfo.alterInfo;
  return (
    <TinyDescriptions
      column={4}
      descriptionsItems={[
        { label: '编号ID', value: alterInfo.bizId },
        {
          label: '审批标题',
          value:
            alterInfo.applyStaffName && alterInfo.alterType
              ? alterInfo.applyStaffName + '发起的' + ALTER_TYPE_MAP[alterInfo.alterType]
              : null,
        },

        {
          label: '发起人',
          value: <UserLink userId={alterInfo.applyStaffId} userName={alterInfo.applyStaffName} />,
        },
        {
          label: '状态',
          value: alterInfo.bizStatus && BIZ_STATUS_MAP[alterInfo.bizStatus],
        },
        {
          label: '对应审批单号',
          value: <ApproveLink id={alterInfo.procInstanceId} />,
        },
        { label: '机房', value: alterInfo.roomGuid ? alterInfo.roomGuid : alterInfo.blockTag },
        {
          label: '审批类型',
          value: alterInfo.alterType && ALTER_TYPE_MAP[alterInfo.alterType],
        },
        { label: '原因', value: alterInfo.applyReason },
      ]}
    />
  );
}

export default AlterInfo;
