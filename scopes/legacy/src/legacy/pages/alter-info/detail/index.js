import React, { createContext } from 'react';
import { connect } from 'react-redux';

import dayjs from 'dayjs';

import { Card } from '@manyun/base-ui.ui.card';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { FileList } from '@manyun/base-ui.ui.file-list';
import { Space } from '@manyun/base-ui.ui.space';

import { OperationLogTable } from '@manyun/auth-hub.ui.operation-log-table';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import {
  ShiftAdjustmentTicket,
  getShiftAdjustmentTicketLocales,
} from '@manyun/hrm.model.shift-adjustment-ticket';
import { ParentalLeaveRecordCard } from '@manyun/hrm.ui.parental-leave-record-card';
import { ResumptionAlterSchedlueRequest } from '@manyun/hrm.ui.resumption-alter-schedlue-request';
import { ShiftAdjustment } from '@manyun/hrm.ui.shift-adjustment';
import { ShiftAdjustmentUserTable } from '@manyun/hrm.ui.shift-adjustment-user-table';

import {
  alterBasicInfoActionCreator,
  alterFilesActionCreator,
  detailCancelAlterInfoActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/alterInfoActions';

import { RevokeButton } from '../component/revoke-button';
import AlterInfo from './components/alterInfo';

const StateContext = createContext(null);

/**
 * @augments {React.PureComponent<any, State>}
 */
export class SpecificTicket extends React.PureComponent {
  onSubmit = () => {};

  componentDidMount() {
    this.initData();
  }

  initData = () => {
    this.props.alterBasicInfoActionCreator({
      bizId: this.props.match.params.bizId,
    });
    this.props.alterFilesActionCreator({
      targetId: this.props.match.params.bizId,
      targetType: 'ALTER',
    });
  };
  render() {
    const { basicAlterInfo } = this.props;
    const locales = getShiftAdjustmentTicketLocales();

    return (
      <StateContext.Provider value="">
        <Space direction="vertical" style={{ display: 'flex' }}>
          <Card title="基本信息">
            <AlterInfo basicInfo={basicAlterInfo} />
            {basicAlterInfo.alterInfo && (
              <Descriptions column={4}>
                <Descriptions.Item
                  label={`${locales.type.enum[basicAlterInfo.alterInfo.alterType]}内容`}
                  span={3}
                >
                  <ShiftAdjustment
                    shiftAdjustment={ShiftAdjustmentTicket.fromApiObject(
                      basicAlterInfo.alterInfo
                    ).toJSON()}
                  />
                </Descriptions.Item>
              </Descriptions>
            )}
            {['LEAVE', 'REST'].includes(basicAlterInfo.alterInfo.alterType) && (
              <ShiftAdjustmentUserTable
                data={(basicAlterInfo.alterInfo.alterDetail ?? []).map(detail => ({
                  scheduleDate: dayjs(detail.replaceDate).valueOf(),
                  dutyName: detail.dutyName,
                  staffId: detail.inUserId,
                  staffName: detail.inUserName,
                }))}
              />
            )}
          </Card>
          {(basicAlterInfo.fileDetails ?? []).length > 0 && (
            <Card title="附件信息">
              <FileList
                files={basicAlterInfo.fileDetails.map(file => McUploadFile.fromApiObject(file))}
                groups={[
                  {
                    title: '文件',
                    fileTypes: ['others', 'pdf'],
                  },
                  {
                    title: '图片',
                    fileTypes: ['image', 'video'],
                    previewable: true,
                    showName: true,
                  },
                ]}
              />
            </Card>
          )}
          <ParentalLeaveRecordCard bizId={this.props.match.params.bizId} />
          <Card title="操作记录">
            <OperationLogTable
              defaultSearchParams={{
                targetId: this.props.match.params.bizId,
                targetType: 'ALTER',
              }}
              showColumns={['serialNumber', 'modifyType']}
              isTargetIdEqual={targetId => {
                return targetId === this.props.match.params.bizId;
              }}
            />
          </Card>
        </Space>
        <RevokeButton
          record={basicAlterInfo.alterInfo}
          type="primary"
          compact={false}
          footer
          onSuccess={() => {
            this.initData();
          }}
        />
        <ResumptionAlterSchedlueRequest
          type="primary"
          compact={false}
          footer
          alterInfo={basicAlterInfo.alterInfo}
          onSuccess={() => {
            this.initData();
          }}
        />
      </StateContext.Provider>
    );
  }
}

const mapStateToProps = ({ alterInfo: { basicAlterInfo } }) => {
  return {
    basicAlterInfo,
    operationRecords: basicAlterInfo?.operationRecords,
  };
};

const mapDispatchToProps = {
  alterBasicInfoActionCreator: alterBasicInfoActionCreator,
  alterFilesActionCreator: alterFilesActionCreator,
  cancelAlterInfo: detailCancelAlterInfoActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(SpecificTicket);
