import { Typography } from '@manyun/base-ui.ui.typography';

// 重复周期枚举
export const CYCLES_TYPE_KEY_MAP = {
  NONE: 'NONE',
  DAY: 'DAY', // 日维度
  WEEK: 'WEEK', // 周维度
  MONTH: 'MONTH', // 月维度
  YEAR: 'YEAR', // 年维度
};

export const BIZ_STATUS_KEY_MAP = {
  PENDING: 'APPROVING',
  AGREE: 'PASS',
  REFUSE: 'REFUSE',
  CANCELED: 'REVOKE',
};

/** @deprecated Use `model/shift-adjustment-ticket ShiftAdjustmentTicketType` instead. */
export const ALTER_TYPE_KEY_MAP = {
  LEAVE: 'LEAVE',
  REST: 'REST',
  EXCHANGE: 'EXCHANGE',
};

/** @deprecated Use `model/shift-adjustment-ticket LeaveRequestType` instead. */
export const ALTER_SUB_TYPE_KEY_MAP = {
  ANNUAL_LEAVE: 'ANNUAL_LEAVE',
  PERSONAL_LEAVE: 'PERSONAL_LEAVE',
  SICK_LEAVE: 'SICK_LEAVE',
  BREAK_OFF: 'BREAK_OFF',
  MATERNITY_LEAVE: 'MATERNITY_LEAVE',
  PATERNITY_LEAVE: 'PATERNITY_LEAVE',
  MARRIAGE_HOLIDAY: 'MARRIAGE_HOLIDAY',
  FUNERAL_LEAVE: 'FUNERAL_LEAVE',
  INJURY_LEAVE: 'INJURY_LEAVE',
  ROAD_LEAVE: 'ROAD_LEAVE',
};

/** @deprecated Use `model/shift-adjustment-ticket LeaveRequestType` instead. */
export const LEAVE_TYPE_KEY_MAP = {
  ANNUAL_LEAVE: 'ANNUAL_LEAVE',
  PERSONAL_LEAVE: 'PERSONAL_LEAVE',
  SICK_LEAVE: 'SICK_LEAVE',
  BREAK_OFF: 'BREAK_OFF',
  MATERNITY_LEAVE: 'MATERNITY_LEAVE',
  PATERNITY_LEAVE: 'PATERNITY_LEAVE',
  MARRIAGE_HOLIDAY: 'MARRIAGE_HOLIDAY',
  FUNERAL_LEAVE: 'FUNERAL_LEAVE',
  INJURY_LEAVE: 'INJURY_LEAVE',
  ROAD_LEAVE: 'ROAD_LEAVE',
};

/** @deprecated Use `model/shift-adjustment-ticket getShiftAdjustmentTicketLocales().leaveType.enum` instead. */
export const LEAVE_TYPE_MAP = {
  [LEAVE_TYPE_KEY_MAP.ANNUAL_LEAVE]: '年假',
  [LEAVE_TYPE_KEY_MAP.PERSONAL_LEAVE]: '事假',
  [LEAVE_TYPE_KEY_MAP.SICK_LEAVE]: '病假',
  [LEAVE_TYPE_KEY_MAP.BREAK_OFF]: '调休',
  [LEAVE_TYPE_KEY_MAP.MATERNITY_LEAVE]: '产假',
  [LEAVE_TYPE_KEY_MAP.PATERNITY_LEAVE]: '陪产假',
  [LEAVE_TYPE_KEY_MAP.MARRIAGE_HOLIDAY]: '婚假',
  [LEAVE_TYPE_KEY_MAP.FUNERAL_LEAVE]: '丧假',
  [LEAVE_TYPE_KEY_MAP.INJURY_LEAVE]: '工伤假',
  [LEAVE_TYPE_KEY_MAP.ROAD_LEAVE]: '路途假',
};

/** @deprecated Use `model/shift-adjustment-ticket getShiftAdjustmentTicketLocales().type.enum` instead. */
export const APPLY_TYPE_KEY_MAP = {
  RETURN_EXCHANGE: 'RETURN_EXCHANGE',
  EXCHANGE: 'EXCHANGE',
  REPLACE: 'REPLACE',
};

export const TICKET_STATUS_KEY_MAP = {
  INIT: '4',
  WAITTAKEOVER: '3',
  FINISH: '1',
  PROCESSING: '2',
  FAILURE: '0',
  UNDO: '5',
};

export const TICKET_STATUS_KEY_TEXT_MAP = {
  [TICKET_STATUS_KEY_MAP.INIT]: '初始化',
  [TICKET_STATUS_KEY_MAP.WAITTAKEOVER]: '待接单',
  [TICKET_STATUS_KEY_MAP.FINISH]: '已关单',
  [TICKET_STATUS_KEY_MAP.PROCESSING]: '处理中',
  [TICKET_STATUS_KEY_MAP.FAILURE]: '失败',
  [TICKET_STATUS_KEY_MAP.UNDO]: '撤销',
};

export const TICKET_STEP_STATUS_KEY_MAP = {
  [TICKET_STATUS_KEY_MAP.INIT]: 0,
  [TICKET_STATUS_KEY_MAP.WAITTAKEOVER]: 1,
  [TICKET_STATUS_KEY_MAP.PROCESSING]: 2,
  [TICKET_STATUS_KEY_MAP.FINISH]: 10, // step结点需要超过3
  [TICKET_STATUS_KEY_MAP.FAILURE]: 4,
  [TICKET_STATUS_KEY_MAP.UNDO]: 5,
};

export const BIZ_STATUS_MAP = {
  [BIZ_STATUS_KEY_MAP.PENDING]: '审批中',
  [BIZ_STATUS_KEY_MAP.AGREE]: '审批通过',
  [BIZ_STATUS_KEY_MAP.REFUSE]: '审批拒绝',
  [BIZ_STATUS_KEY_MAP.CANCELED]: '已撤回',
};

export const ROLL_BACK_ALTER_STATUS = {
  APPROVING: 'APPROVING',
  PASS: 'PASS',
};

export const ROLL_BACK_ALTER_STATUS_MAP = {
  [ROLL_BACK_ALTER_STATUS.APPROVING]: '销假中',
  [ROLL_BACK_ALTER_STATUS.PASS]: '已销假',
};

export const ROLL_BACK_ALTER_STATUS_OPTIONS = [
  {
    label: ROLL_BACK_ALTER_STATUS_MAP[ROLL_BACK_ALTER_STATUS.APPROVING],
    value: ROLL_BACK_ALTER_STATUS.APPROVING,
  },
  {
    label: ROLL_BACK_ALTER_STATUS_MAP[ROLL_BACK_ALTER_STATUS.PASS],
    value: ROLL_BACK_ALTER_STATUS.PASS,
  },
];

export function BizStatus(status) {
  if (status) {
    let type = undefined;
    switch (status) {
      case BIZ_STATUS_KEY_MAP.PENDING:
        type = 'warning';
        break;
      case BIZ_STATUS_KEY_MAP.AGREE:
        type = 'success';
        break;
      case BIZ_STATUS_KEY_MAP.REFUSE:
        type = 'danger';
        break;
      default:
        break;
    }
    return <Typography.Text type={type}>{BIZ_STATUS_MAP[status]}</Typography.Text>;
  } else {
    return '--';
  }
}

export const ALTER_TYPE_MAP = {
  [ALTER_TYPE_KEY_MAP.LEAVE]: '请假',
  [ALTER_TYPE_KEY_MAP.REST]: '顶班',
  [ALTER_TYPE_KEY_MAP.EXCHANGE]: '换班',
};

/**
 * @deprecated use getLeaveTypeByValue from hrm.ui.leave-type instead
 */
export const ALTER_SUB_TYPE_MAP = {
  [ALTER_SUB_TYPE_KEY_MAP.ANNUAL_LEAVE]: '年假',
  [ALTER_SUB_TYPE_KEY_MAP.PERSONAL_LEAVE]: '事假',
  [ALTER_SUB_TYPE_KEY_MAP.SICK_LEAVE]: '病假',
  [ALTER_SUB_TYPE_KEY_MAP.BREAK_OFF]: '调休',
  [ALTER_SUB_TYPE_KEY_MAP.MATERNITY_LEAVE]: '产假',
  [ALTER_SUB_TYPE_KEY_MAP.PATERNITY_LEAVE]: '陪产假',
  [ALTER_SUB_TYPE_KEY_MAP.MARRIAGE_HOLIDAY]: '婚假',
  [ALTER_SUB_TYPE_KEY_MAP.FUNERAL_LEAVE]: '丧假',
  [ALTER_SUB_TYPE_KEY_MAP.INJURY_LEAVE]: '工伤假',
  [ALTER_SUB_TYPE_KEY_MAP.ROAD_LEAVE]: '路途假',
};

export const APPLY_TYPE_MAP = {
  [APPLY_TYPE_KEY_MAP.RETURN_EXCHANGE]: '还班',
  [APPLY_TYPE_KEY_MAP.EXCHANGE]: '换班',
  [APPLY_TYPE_KEY_MAP.REPLACE]: '顶班',
};

export const BIZ_STATUS_OPTIONS = [
  {
    value: BIZ_STATUS_KEY_MAP.PENDING,
    label: BIZ_STATUS_MAP[BIZ_STATUS_KEY_MAP.PENDING],
  },
  {
    value: BIZ_STATUS_KEY_MAP.AGREE,
    label: BIZ_STATUS_MAP[BIZ_STATUS_KEY_MAP.AGREE],
  },
  {
    value: BIZ_STATUS_KEY_MAP.REFUSE,
    label: BIZ_STATUS_MAP[BIZ_STATUS_KEY_MAP.REFUSE],
  },
  {
    value: BIZ_STATUS_KEY_MAP.CANCELED,
    label: BIZ_STATUS_MAP[BIZ_STATUS_KEY_MAP.CANCELED],
  },
];

export const ALTER_TYPE_OPTIONS = [
  {
    value: ALTER_TYPE_KEY_MAP.LEAVE,
    label: ALTER_TYPE_MAP[ALTER_TYPE_KEY_MAP.LEAVE],
  },
  {
    value: ALTER_TYPE_KEY_MAP.REST,
    label: ALTER_TYPE_MAP[ALTER_TYPE_KEY_MAP.REST],
  },
  {
    value: ALTER_TYPE_KEY_MAP.EXCHANGE,
    label: ALTER_TYPE_MAP[ALTER_TYPE_KEY_MAP.EXCHANGE],
  },
];

export const LEAVE_TYPE_OPTIONS = [
  {
    value: LEAVE_TYPE_KEY_MAP.ANNUAL_LEAVE,
    label: LEAVE_TYPE_MAP[LEAVE_TYPE_KEY_MAP.ANNUAL_LEAVE],
  },
  {
    value: LEAVE_TYPE_KEY_MAP.PERSONAL_LEAVE,
    label: LEAVE_TYPE_MAP[LEAVE_TYPE_KEY_MAP.PERSONAL_LEAVE],
  },
  {
    value: LEAVE_TYPE_KEY_MAP.SICK_LEAVE,
    label: LEAVE_TYPE_MAP[LEAVE_TYPE_KEY_MAP.SICK_LEAVE],
  },
  {
    value: LEAVE_TYPE_KEY_MAP.BREAK_OFF,
    label: LEAVE_TYPE_MAP[LEAVE_TYPE_KEY_MAP.BREAK_OFF],
  },
  {
    value: LEAVE_TYPE_KEY_MAP.MATERNITY_LEAVE,
    label: LEAVE_TYPE_MAP[LEAVE_TYPE_KEY_MAP.MATERNITY_LEAVE],
  },
  {
    value: LEAVE_TYPE_KEY_MAP.PATERNITY_LEAVE,
    label: LEAVE_TYPE_MAP[LEAVE_TYPE_KEY_MAP.PATERNITY_LEAVE],
  },
  {
    value: LEAVE_TYPE_KEY_MAP.MARRIAGE_HOLIDAY,
    label: LEAVE_TYPE_MAP[LEAVE_TYPE_KEY_MAP.MARRIAGE_HOLIDAY],
  },
  {
    value: LEAVE_TYPE_KEY_MAP.FUNERAL_LEAVE,
    label: LEAVE_TYPE_MAP[LEAVE_TYPE_KEY_MAP.FUNERAL_LEAVE],
  },
  {
    value: LEAVE_TYPE_KEY_MAP.INJURY_LEAVE,
    label: LEAVE_TYPE_MAP[LEAVE_TYPE_KEY_MAP.INJURY_LEAVE],
  },
];
