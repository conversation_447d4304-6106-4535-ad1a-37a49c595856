import React, { useCallback } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Modal } from '@manyun/base-ui.ui.modal';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { useRevokeAlterRequest } from '@manyun/hrm.hook.use-revoke-alter-request';

import { BIZ_STATUS_KEY_MAP } from '../constants';

const ELEMENT_REVOKE_ALTER_CODE = 'element_revoke-change-user-shift-schedule-request';

export const RevokeButton = ({ record, onSuccess, ...props }) => {
  const [, { checkUserId, checkCode }] = useAuthorized();
  const [loading, revokeRequest] = useRevokeAlterRequest();

  const _onHandleRevoke = useCallback(() => {
    Modal.confirm({
      title: `确认要撤回吗？`,
      content: '撤回之后需重新发起申请！',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        revokeRequest({ bizId: record.bizId, type: record.alterType }, () => {
          if (onSuccess) {
            onSuccess();
          }
        });
      },
      onCancel() {},
    });
  }, [onSuccess, record.alterType, record.bizId, revokeRequest]);

  if (
    (checkUserId(record.creatorId) ||
      checkUserId(record.applyStaffId) ||
      checkCode(ELEMENT_REVOKE_ALTER_CODE)) &&
    record.bizStatus === BIZ_STATUS_KEY_MAP.PENDING
  ) {
    if (props.footer) {
      return (
        <FooterToolBar>
          <Button
            compact={props.compact ?? true}
            type={props.type ?? 'link'}
            loading={loading}
            onClick={() => {
              _onHandleRevoke();
            }}
          >
            撤回
          </Button>
        </FooterToolBar>
      );
    }
    return (
      <Button
        loading={loading}
        compact={props.compact ?? true}
        type={props.type ?? 'link'}
        onClick={() => {
          _onHandleRevoke();
        }}
      >
        撤回
      </Button>
    );
  }

  return null;
};
