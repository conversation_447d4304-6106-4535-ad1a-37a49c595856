import React, { Component } from 'react';

import { TinyCard } from '@manyun/dc-brain.legacy.components';

import BaseInfoForm from './basic-form';

export default class BaseInfoCard extends Component {
  render() {
    const { mode, xRef, validation } = this.props;
    return (
      <TinyCard title="基本信息">
        <BaseInfoForm
          wrappedComponentRef={xRef}
          mode={mode}
          validation={validation}
          history={this.props.history}
        />
      </TinyCard>
    );
  }
}
