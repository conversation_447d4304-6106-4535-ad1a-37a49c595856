import React, { useEffect, useState } from 'react';

import { Select } from '@galiojs/awesome-antd';
import moment from 'moment';

import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { message } from '@manyun/base-ui.ui.message';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';
import { pmService } from '@manyun/dc-brain.legacy.services';

function ReturnDuty({ forwardedRef, exchangeReturnInfo, alterType, applyStaffId, onChangeValues }) {
  return (
    <GutterWrapper ref={forwardedRef} style={{ verticalAlign: 'middle' }}>
      {
        <ChildItem
          key={exchangeReturnInfo.id}
          onChange={onChangeValues}
          applyStaffId={applyStaffId}
          alterType={alterType}
          single={exchangeReturnInfo}
          exchangeReturnInfo={exchangeReturnInfo}
        />
      }
    </GutterWrapper>
  );
}

export default React.forwardRef((props, ref) => <ReturnDuty forwardedRef={ref} {...props} />);

function ChildItem({ single, alterType, applyStaffId, exchangeReturnInfo, onChange }) {
  const [dutys, setDutys] = useState([]);

  useEffect(() => {
    (async function () {
      if (!(exchangeReturnInfo.scheduleDate && applyStaffId && alterType)) {
        return;
      }
      let params = {
        staffId: applyStaffId,
        scheduleDate: exchangeReturnInfo.scheduleDate,
        alterType,
      };
      const { response, error } = await pmService.scheduleDutyList(params);
      if (error) {
        message.error(error);
      }
      if (response) {
        setDutys(response.data);
      }
    })();
  }, [single, applyStaffId, alterType, exchangeReturnInfo]);

  return (
    <GutterWrapper size="8px">
      <DatePicker
        placeholder={'时间'}
        disabled={applyStaffId ? false : true}
        format="YYYY-MM-DD"
        style={{ width: 200 }}
        onChange={date => {
          const newExchangeInfo = {
            ...exchangeReturnInfo,
            scheduleDate: moment(date).startOf('day').valueOf(),
            dutyId: null,
            exchangeReturnInfo: {
              ...exchangeReturnInfo,
              scheduleDate: moment(date).startOf('day').valueOf(),
              dutyId: null,
            },
          };
          onChange(newExchangeInfo);
        }}
      />
      <Select
        disabled={applyStaffId && single.scheduleDate ? false : true}
        style={{ width: 200 }}
        value={single.dutyId}
        placeholder={'班次'}
        onChange={duty => {
          const newExchangeInfo = {
            ...exchangeReturnInfo,
            dutyId: duty,
            exchangeReturnInfo: {
              ...exchangeReturnInfo,
              dutyId: duty,
            },
          };
          onChange(newExchangeInfo);
        }}
      >
        {dutys.map(item => {
          return (
            <Select.Option key={item.id} value={item.id}>
              {item.dutyName}
            </Select.Option>
          );
        })}
      </Select>
    </GutterWrapper>
  );
}
