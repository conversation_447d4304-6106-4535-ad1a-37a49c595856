import React, { forwardRef, useEffect, useState } from 'react';

import { Select } from '@galiojs/awesome-antd';

import { pmService } from '@manyun/dc-brain.legacy.services';

const ReplaceUserSelect = forwardRef(({ params, value, onChange }, ref) => {
  const [replaceUsers, setReplaceUsers] = useState([]);
  useEffect(() => {
    async function getReplaceUsers() {
      const { response } = await pmService.replaceUserList(params);
      if (response) {
        setReplaceUsers(response.data);
      }
    }
    if (params) {
      getReplaceUsers();
    }
  }, [params]);

  console.log('bbb', params);
  let disabled = params?.scheduleDate && params?.staffId && params?.alterType ? false : true;
  return (
    <Select
      ref={ref}
      style={{ width: 200 }}
      disabled={disabled}
      labelInValue
      value={value}
      onChange={onChange}
    >
      {replaceUsers.map(({ id, userName }) => {
        return (
          <Select.Option key={id} value={id}>
            {userName}
          </Select.Option>
        );
      })}
    </Select>
  );
});

export default ReplaceUserSelect;
