import React, { useEffect, useState } from 'react';

import { Select } from '@galiojs/awesome-antd';
import moment from 'moment';

import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { message } from '@manyun/base-ui.ui.message';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';
import { pmService } from '@manyun/dc-brain.legacy.services';

function ExchangeDuty({ forwardedRef, exchangeInfo, alterType, applyStaffId, onChangeValues }) {
  return (
    <GutterWrapper ref={forwardedRef} style={{ verticalAlign: 'middle' }}>
      {
        <ChildItem
          onChange={onChangeValues}
          applyStaffId={applyStaffId}
          alterType={alterType}
          single={exchangeInfo}
          exchangeInfo={exchangeInfo}
        />
      }
    </GutterWrapper>
  );
}

export default React.forwardRef((props, ref) => <ExchangeDuty forwardedRef={ref} {...props} />);

function ChildItem({ single, alterType, applyStaffId, exchangeInfo, onChange }) {
  const [dutys, setDutys] = useState([]);
  const [replaceUsers, setReplaceUsers] = useState([]);

  useEffect(() => {
    (async function () {
      if (!(single.scheduleDate && applyStaffId && alterType)) {
        return;
      }
      let params = {
        staffId: applyStaffId.value,
        scheduleDate: single.scheduleDate,
        alterType,
      };
      const { response, error } = await pmService.scheduleDutyList(params);
      if (error) {
        message.error(error);
      }
      if (response) {
        setDutys(response.data);
      }
    })();

    (async function () {
      if (!(single.scheduleDate && applyStaffId && alterType && single.dutyId)) {
        return;
      }
      let params = {
        alterType: alterType,
        staffId: applyStaffId.value,
        scheduleDate: single.scheduleDate,
        dutyId: single.dutyId,
      };
      const { response, error } = await pmService.replaceUserList(params);
      if (error) {
        message.error(error);
      }
      if (response) {
        setReplaceUsers(response.data);
      }
    })();
  }, [single, applyStaffId, alterType]);

  return (
    <GutterWrapper size="8px">
      <DatePicker
        placeholder={'时间'}
        disabled={!applyStaffId}
        format="YYYY-MM-DD"
        style={{ width: 200 }}
        onChange={date => {
          const newExchangeInfo = {
            ...exchangeInfo,
            scheduleDate: moment(date).startOf('day').valueOf(),
            dutyId: null,
            replaceStaffId: null,
            exchangeInfo: {
              ...exchangeInfo,
              scheduleDate: moment(date).startOf('day').valueOf(),
              dutyId: null,
              replaceStaffId: null,
            },
          };
          onChange(newExchangeInfo);
        }}
      />
      <Select
        placeholder={'班次'}
        disabled={applyStaffId && single.scheduleDate ? false : true}
        style={{ width: 200 }}
        value={single.dutyId}
        onChange={duty => {
          const newExchangeInfo = {
            ...exchangeInfo,
            dutyId: duty,
            replaceStaffId: null,
            exchangeInfo: {
              ...exchangeInfo,
              dutyId: duty,
              replaceStaffId: null,
            },
          };
          onChange(newExchangeInfo);
        }}
      >
        {dutys.map(item => {
          return (
            <Select.Option key={item.id} value={item.id}>
              {item.dutyName}
            </Select.Option>
          );
        })}
      </Select>
      <Select
        placeholder={'替班人'}
        disabled={applyStaffId && single.scheduleDate && single.dutyId ? false : true}
        style={{ width: 200 }}
        value={single.replaceStaffId}
        allowClear
        onChange={staff => {
          const newExchangeInfo = {
            ...exchangeInfo,
            replaceStaffId: staff,
            targetDutyId: null,
            exchangeInfo: {
              ...exchangeInfo,
              replaceStaffId: staff,
            },
          };
          onChange(newExchangeInfo);
        }}
      >
        {replaceUsers.map(item => {
          return (
            <Select.Option key={item.id} value={item.id}>
              {item.userName}
            </Select.Option>
          );
        })}
      </Select>
    </GutterWrapper>
  );
}
