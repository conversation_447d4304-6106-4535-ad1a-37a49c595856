import React, { Component } from 'react';
import { connect } from 'react-redux';

import PlusCircleFilled from '@ant-design/icons/es/icons/PlusCircleFilled';
import { ApiSelect, EditableTable, Form, Select } from '@galiojs/awesome-antd';
import { generateGetRowSpan } from '@galiojs/awesome-antd/lib/table/utils';
import cloneDeep from 'lodash/cloneDeep';
import moment from 'moment';
import shortid from 'shortid';
import styled from 'styled-components';

import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { McUpload } from '@manyun/dc-brain.ui.upload';

import {
  alterInfoActions,
  getAlterBaseInfoActionCreator,
  redirectDetailActionCreator,
  submitExchangeActionCreator,
  submitLeaveActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/alterInfoActions';
import { pmService } from '@manyun/dc-brain.legacy.services';
import { getLocationSearchMap } from '@manyun/dc-brain.legacy.utils';

import { ALTER_TYPE_KEY_MAP, ALTER_TYPE_OPTIONS } from '../../constants';
import ExchangeDuty from './exchange-duty';
import ReturnDuty from './return-duty';

const StyledEditableTable = styled(EditableTable)`
  margin-top: -8px;
  margin-left: -8px;

  .manyun-table {
    border: 0;

    .manyun-table-thead
      > tr.manyun-table-row-hover:not(.manyun-table-expanded-row):not(.manyun-table-row-selected)
      > td,
    .manyun-table-tbody
      > tr.manyun-table-row-hover:not(.manyun-table-expanded-row):not(.manyun-table-row-selected)
      > td,
    .manyun-table-thead
      > tr:hover:not(.manyun-table-expanded-row):not(.manyun-table-row-selected)
      > td,
    .manyun-table-tbody
      > tr:hover:not(.manyun-table-expanded-row):not(.manyun-table-row-selected)
      > td {
      background: transparent;
    }

    .manyun-table-tbody {
      > tr:last-child {
        > td {
          border-bottom-width: 0;
        }
      }
    }
  }
  .manyun-table-small > .manyun-table-content > .manyun-table-body {
    margin: 0;
  }
  .manyun-table-small
    > .manyun-table-content
    > .manyun-table-body
    > table
    > .manyun-table-tbody
    > tr
    > td {
    padding-left: 0;
    padding-top: 0;
    padding-bottom: 14px;
    vertical-align: top;
    border-bottom: none;
    &:nth-child(4) {
      vertical-align: middle;
    }
  }
`;

class BaseInfoFormLegacy extends Component {
  _initialSearchParams = {
    scheduleDate: null,
    dutyId: null,
    type: null,
  };

  state = {
    editingRowKey: this.props.formOptions.leaveInfoList.value[0].id,
    tableData: this.props.formOptions.leaveInfoList.value,
    deleteByCancel: false,
    beSaved: false,
  };

  componentWillUnmount() {
    this.props.resetCreateValues();
  }

  componentDidMount() {
    this._initializeUrlSearchParams();
    this._initApplayInformation();
  }

  _initApplayInformation = async () => {
    const { user, upDateCreateFieldsValue } = this.props;
    const { type, scheduleDate, dutyId } = this._initialSearchParams;
    if (user.userId !== null && scheduleDate != null) {
      const id = shortid.generate();
      this.setState({ editingRowKey: id });
      upDateCreateFieldsValue({
        applyStaffId: {
          name: 'applyStaffId',
          value: {
            id: user.userId,
            key: user.userId,
            label: user.name,
          },
        },
        alterType: { value: type || ALTER_TYPE_KEY_MAP.REST },
        leaveInfoList: {
          name: 'leaveInfoList',
          value: [
            {
              id: id,
              scheduleDate: moment(parseInt(scheduleDate)),
              dutyId: undefined,
              replaceStaffId: undefined,
            },
          ],
        },
      });
      const { response } = await pmService.scheduleDutyList({
        alterType: type || ALTER_TYPE_KEY_MAP.REST,
        scheduleDate: parseInt(scheduleDate),
        staffId: user.userId,
      });
      if ((response.data || []).length > 0) {
        let selectedDuty = { key: response.data[0].id, label: response.data[0].dutyName };
        if (dutyId != null) {
          const dutyInfo = response.data.find(item => Number(item.id) === Number(dutyId));
          if (dutyInfo) {
            selectedDuty.key = dutyInfo.id;
            selectedDuty.label = dutyInfo.dutyName;
          }
        }
        upDateCreateFieldsValue({
          leaveInfoList: {
            name: 'leaveInfoList',
            value: [
              {
                id: id,
                scheduleDate: moment(parseInt(scheduleDate)),
                dutyId: selectedDuty,
                replaceStaffId: undefined,
              },
            ],
          },
        });
      }
    }
  };

  _initializeUrlSearchParams = () => {
    const { search } = this.props.history.location;

    this._initialSearchParams = {
      ...this._initialSearchParams,
      ...getLocationSearchMap(search, ['type', 'scheduleDate', 'dutyId']),
    };
  };

  validateLeaveInfoList = async () => {
    let errorMessage;
    const existingErrors = this.props.formOptions.leaveInfoList.errors;
    if (this.state.editingRowKey !== EditableTable.UNSET_EDITING_ROW_KEY) {
      errorMessage = '当前还有未保存顶班信息，请先保存！';
      this.props.upDateCreateFieldsValue({
        leaveInfoList: {
          errors: [errorMessage],
        },
      });
    } else {
      if (existingErrors) {
        this.props.upDateCreateFieldsValue({
          leaveInfoList: {
            errors: [],
          },
        });
      }
    }
    if (errorMessage !== undefined) {
      throw new Error(errorMessage);
    }
  };

  onSubmit = async () => {
    const { formOptions, form } = this.props;

    try {
      await form.validateFields();

      if (formOptions.alterType.value === ALTER_TYPE_KEY_MAP.REST) {
        await this.validateLeaveInfoList();
        this.props.submitLeave({
          params: {
            tableData: this.state.tableData,
            formOptions: formOptions,
          },
        });
      }

      if (formOptions.alterType.value === ALTER_TYPE_KEY_MAP.EXCHANGE) {
        if (!formOptions.applyScheduleDate.value) {
          message.error('换班时间不能为空');
          return;
        }
        if (!formOptions.applyDutyId.value) {
          message.error('换班班次不能为空');
          return;
        }
        if (!formOptions.targetStaffId.value) {
          message.error('替班人不能为空');
          return;
        }
        if (!formOptions.targetScheduleDate.value) {
          message.error('还班时间不能为空');
          return;
        }
        if (!formOptions.targetDutyId.value) {
          message.error('还班班次不能为空');
          return;
        }

        this.props.submitExchange({
          params: {
            tableData: this.state.tableData,
            formOptions: formOptions,
          },
        });
      }
    } catch (error) {}
  };

  addRow = (rowData, insertIdx) => {
    const nextData = cloneDeep(this.state.tableData);
    nextData.splice(insertIdx, 0, rowData);

    this.setState({
      editingRowKey: rowData.id,
      deleteByCancel: true,
    });
    this.props.upDateCreateFieldsValue({
      leaveInfoList: {
        value: nextData,
        name: 'leaveInfoList',
      },
    });
  };

  render() {
    const {
      form,
      editableTableForm,
      formOptions,
      mode,
      showActionsColumn,
      deleteByCancel,
      upDateCreateFieldsValue,
    } = this.props;
    const leaveInfoListErrors = formOptions.leaveInfoList.errors;
    const showLeaveInfoListErrors = leaveInfoListErrors.length > 0;
    const { editingRowKey, tableData, beSaved } = this.state;

    return (
      <Form
        style={{ width: 880 }}
        colon={false}
        labelCol={{ span: 3 }}
        wrapperCol={{ span: 21 }}
        form={form}
        // initialValues={{
        //   alterType: formOptions.alterType.value,
        // }}
        fields={Object.keys(formOptions).map(name => {
          const field = formOptions[name];

          return {
            ...field,
            name: name.split('.'),
          };
        })}
        onFieldsChange={changedFields => {
          upDateCreateFieldsValue(
            changedFields.reduce((mapper, field) => {
              const name = field.name.join('.');
              mapper[name] = {
                ...field,
                name,
              };

              return mapper;
            }, {})
          );
        }}
      >
        <Form.Item
          label="申请人"
          name="applyStaffId"
          rules={[{ required: true, message: '申请人必填' }]}
        >
          <UserSelect
            style={{ width: 240 }}
            onChange={() => {
              const id = shortid.generate();
              this.props.upDateCreateFieldsValue({
                applyDutyId: { value: null },
                targetStaffId: { value: null },
                targetDutyId: { value: null },
                leaveInfoList: {
                  value: [
                    {
                      id: id,
                      scheduleDate: null,
                      dutyId: undefined,
                      replaceStaffId: undefined,
                    },
                  ],
                },
              });
              this.setState(
                {
                  editingRowKey: null,
                  tableData: [],
                },
                () => {
                  this.setState({
                    editingRowKey: id,
                    tableData: [
                      {
                        id: id,
                        scheduleDate: null,
                        dutyId: undefined,
                        replaceStaffId: undefined,
                      },
                    ],
                  });
                }
              );
            }}
          />
        </Form.Item>
        <Form.Item
          label="申请类型"
          name="alterType"
          rules={[{ required: true, message: '申请类型必选' }]}
        >
          <Select
            style={{ width: 200 }}
            trigger="onDidMount"
            disabled={mode === 'edit' || !!this._initialSearchParams.type}
            options={ALTER_TYPE_OPTIONS.filter(opt => {
              if (this._initialSearchParams.type) {
                return opt.value === this._initialSearchParams.type;
              }
              return opt.value !== 'LEAVE';
            })}
          />
        </Form.Item>
        {formOptions.alterType.value === ALTER_TYPE_KEY_MAP.EXCHANGE && (
          <Form.Item
            label="换班班次"
            name="exchangeInfo"
            rules={[{ required: true, message: '换班班次必填！' }]}
          >
            <ExchangeDuty
              alterType={formOptions.alterType.value}
              exchangeInfo={{
                scheduleDate: formOptions.applyScheduleDate.value,
                dutyId: formOptions.applyDutyId.value,
                replaceStaffId: formOptions.targetStaffId.value,
              }}
              applyStaffId={formOptions.applyStaffId.value}
              onChangeValues={data => {
                this.props.upDateCreateFieldsValue({
                  applyScheduleDate: { value: data.scheduleDate },
                  applyDutyId: { value: data.dutyId },
                  targetStaffId: { value: data.replaceStaffId },
                  targetDutyId: { value: data.targetDutyId },
                  exchangeInfo: { value: data.exchangeInfo },
                });
              }}
            />
          </Form.Item>
        )}
        {formOptions.alterType.value === ALTER_TYPE_KEY_MAP.EXCHANGE && (
          <Form.Item
            label="还班班次"
            name="exchangeReturnInfo"
            rules={[{ required: true, message: '还班班次必填！' }]}
          >
            <ReturnDuty
              alterType={formOptions.alterType.value}
              exchangeReturnInfo={{
                scheduleDate: formOptions.targetScheduleDate.value,
                dutyId: formOptions.targetDutyId.value,
              }}
              applyStaffId={formOptions.targetStaffId.value}
              onChangeValues={data => {
                this.props.upDateCreateFieldsValue({
                  targetScheduleDate: { value: data.scheduleDate },
                  targetDutyId: { value: data.dutyId },
                  exchangeReturnInfo: { value: data.exchangeReturnInfo },
                });
              }}
            />
          </Form.Item>
        )}
        {formOptions.alterType.value === ALTER_TYPE_KEY_MAP.REST && (
          <Form.Item
            label="顶班班次"
            validateStatus={showLeaveInfoListErrors ? 'error' : undefined}
            help={showLeaveInfoListErrors ? leaveInfoListErrors[0] : undefined}
          >
            <StyledEditableTable
              form={editableTableForm}
              bordered={false}
              showHeader={false}
              rowKey="id"
              size="small"
              showActionsColumn={showActionsColumn}
              dataSource={formOptions.leaveInfoList.value}
              canDelete={(_records, idx) => idx > 0}
              columns={getColumns({
                addRow: this.addRow,
                getRowSpan: generateGetRowSpan(formOptions.leaveInfoList.value),
                showActionsColumn,
                editingRowKey,
                leaveInfoList: formOptions.leaveInfoList.value,
                applyStaffId: formOptions.applyStaffId.value,
                alterTypeOut: formOptions.alterType.value, //ALTER_TYPE_KEY_MAP.LEAVE,
              })}
              editingRowKey={editingRowKey}
              onEdit={rowKey => {
                this.setState({ editingRowKey: rowKey });
              }}
              onCancel={() => {
                if (this.state.deleteByCancel) {
                  const nextData = tableData;
                  const idx = nextData.findIndex(record => record.id === editingRowKey);
                  if (idx > -1) {
                    nextData.splice(idx, 1);
                  }
                  this.props.upDateCreateFieldsValue({
                    leaveInfoList: {
                      value: nextData,
                    },
                  });
                  this.setState({
                    tableData: nextData,
                    editingRowKey: null,
                    deleteByCancel: false,
                  });
                } else if (!beSaved) {
                  message.error('数据还未保存，不可以直接取消！');
                } else {
                  this.setState({ editingRowKey: null });
                }
              }}
              onSave={(_, records) => {
                this.setState({
                  tableData: records,
                  editingRowKey: null,
                  deleteByCancel: false,
                  beSaved: true,
                });
                this.props.upDateCreateFieldsValue({
                  leaveInfoList: {
                    errors: [],
                    value: records,
                  },
                });
              }}
              onDelete={(rowKey, records) => {
                let editKey = editingRowKey;
                if (rowKey === editingRowKey) {
                  editKey = null;
                }
                this.setState({
                  tableData: records,
                  editingRowKey: editKey,
                  deleteByCancel: deleteByCancel,
                });
                this.props.upDateCreateFieldsValue({
                  leaveInfoList: {
                    value: records,
                    name: 'leaveInfoList',
                  },
                });
              }}
            />
          </Form.Item>
        )}
        {formOptions.alterType.value === ALTER_TYPE_KEY_MAP.EXCHANGE && (
          <Form.Item
            label="换班原因"
            name="exchangeReason"
            rules={[
              {
                required: true,
                message: '换班原因必填！',
              },
              {
                type: 'string',
                max: 200,
                message: '最多输入 200个字符！',
              },
            ]}
          >
            <Input.TextArea style={{ width: 300 }} />
          </Form.Item>
        )}
        {formOptions.alterType.value === ALTER_TYPE_KEY_MAP.REST && (
          <Form.Item
            label="顶班原因"
            name="leaveReason"
            rules={[
              {
                required: true,
                message: '顶班原因必填！',
              },
              {
                type: 'string',
                max: 200,
                message: '最多输入 200个字符！',
              },
            ]}
          >
            <Input.TextArea style={{ width: 300 }} />
          </Form.Item>
        )}
        {formOptions.alterType.value && (
          <Form.Item
            label="上传图片"
            name="files"
            rules={[
              {
                required: false,
              },
            ]}
            valuePropName="fileList"
            getValueFromEvent={evt => evt.fileList}
          >
            <McUpload showAccept accept=".jpg,.png" allowDelete maxFileSize={5} maxFileCount={1}>
              <Button type="primary">上传</Button>
            </McUpload>
          </Form.Item>
        )}
        <FooterToolBar>
          <Space>
            <Button type="primary" onClick={this.onSubmit}>
              提交
            </Button>
            <Button onClick={() => this.props.history.goBack()}>取消</Button>
          </Space>
        </FooterToolBar>
      </Form>
    );
  }
}

function BaseInfoForm(props) {
  const [form] = Form.useForm();
  const [editableTableForm] = Form.useForm();

  return <BaseInfoFormLegacy form={form} editableTableForm={editableTableForm} {...props} />;
}

const mapStateToProps = ({ alterInfo: { create }, user }) => {
  let formOptions = create;
  return { formOptions, user };
};

const mapDispatchToProps = {
  getTaskBaseInfo: getAlterBaseInfoActionCreator,
  upDateCreateFieldsValue: alterInfoActions.upDateCreateFieldsValue,
  resetCreateValues: alterInfoActions.resetCreateValues,
  submitLeave: submitLeaveActionCreator,
  submitExchange: submitExchangeActionCreator,
  redirectDetail: redirectDetailActionCreator,
};

const getColumns = ({ addRow, editingRowKey, applyStaffId, alterTypeOut }) => [
  {
    title: '日期',
    dataIndex: 'scheduleDate',
    width: 236,
    editable: true,
    editingCtrl: (_fieldsValue, { form }) => {
      return (
        <DatePicker
          style={{ width: 200 }}
          placeholder="日期"
          disabled={!applyStaffId}
          format="YYYY-MM-DD"
          onChange={() => {
            form.setFieldsValue({ dutyId: undefined, replaceStaffId: undefined });
          }}
        />
      );
    },
    render(scheduleDate, _record, idx) {
      if (!scheduleDate) {
        return;
      }
      return (
        <Space>
          {idx === 0 && !editingRowKey && (
            <PlusCircleFilled
              onClick={() => {
                let rowKey = shortid.generate();
                addRow({
                  id: rowKey,
                  scheduleDate: null,
                  dutyId: undefined,
                  replaceStaffId: undefined,
                });
              }}
            />
          )}
          <DatePicker
            showTime
            placeholder={scheduleDate?.format('YYYY-MM-DD')}
            disabled={true}
            format="YYYY-MM-DD"
            style={{ width: 200 }}
          />
        </Space>
      );
    },
    formItemProps: {
      rules: [
        {
          validator: (_rule, scheduleDate) => {
            if (!scheduleDate) {
              return Promise.reject('顶班日期不可为空');
            }
            return Promise.resolve();
          },
        },
      ],
    },
  },
  {
    title: '班次',
    dataIndex: ['dutyId', 'label'],
    width: 216,
    editable: true,
    editingId: 'dutyId',
    editingCtrl: (fieldsValue, { form }) => {
      let staffId;
      let scheduleDate;
      if (fieldsValue.scheduleDate && applyStaffId) {
        staffId = applyStaffId.value;
        scheduleDate = moment(fieldsValue.scheduleDate).startOf('day').valueOf();
      }
      return (
        <ApiSelect
          style={{ width: 200 }}
          disabled={!(scheduleDate && staffId)}
          placeholder="班次"
          labelInValue
          onChange={() => {
            form.setFieldsValue({ replaceStaffId: undefined });
          }}
          fieldNames={{ label: 'dutyName', value: 'id' }}
          serviceQueries={[staffId, scheduleDate]}
          dataService={async function (staffId, scheduleDate) {
            if (!staffId || !scheduleDate || !alterTypeOut) {
              return [];
            }
            const { response } = await pmService.scheduleDutyList({
              staffId,
              scheduleDate,
              alterType: alterTypeOut,
            });
            if (!response) {
              return [];
            }
            return response.data || [];
          }}
        />
      );
    },
    render(dutyId) {
      return <ApiSelect style={{ width: 200 }} disabled={true} placeholder={dutyId} labelInValue />;
    },
    formItemProps: {
      rules: [
        {
          validator: (_rule, dutyId) => {
            if (!dutyId) {
              return Promise.reject('顶班班次不可为空');
            }
            return Promise.resolve();
          },
        },
      ],
    },
  },
  {
    title: '顶班人',
    dataIndex: ['replaceStaffId', 'label'],
    width: 216,
    editable: true,
    editingId: 'replaceStaffId',
    editingCtrl: fieldsValue => {
      let staffId;
      let scheduleDate;
      let dutyId;
      let alterType;
      if (fieldsValue.scheduleDate && applyStaffId && fieldsValue.dutyId && alterTypeOut) {
        staffId = applyStaffId.value;
        scheduleDate = moment(fieldsValue.scheduleDate).startOf('day').valueOf();
        dutyId = fieldsValue.dutyId.key;
        alterType = alterTypeOut;
      }
      return (
        <ApiSelect
          style={{ width: 200 }}
          disabled={!(scheduleDate && staffId && dutyId && alterType)}
          placeholder="顶班人"
          labelInValue
          allowClear
          fieldNames={{ label: 'userName', value: 'id' }}
          serviceQueries={[staffId, scheduleDate, dutyId, alterType]}
          dataService={async function (staffId, scheduleDate) {
            if (!staffId || !scheduleDate) {
              return [];
            }
            const { response } = await pmService.replaceUserList({
              staffId,
              scheduleDate,
              dutyId,
              alterType,
            });
            if (!response) {
              return [];
            }
            return response.data || [];
          }}
        />
      );
    },
    render(replaceStaffId) {
      return (
        <ApiSelect
          style={{ width: 200 }}
          disabled={true}
          placeholder={replaceStaffId}
          labelInValue
        />
      );
    },
    decorateOptions: {
      rules: [
        {
          validator: (_rule, replaceStaffId) => {
            if (!replaceStaffId) {
              return Promise.reject('顶班人不可为空');
            }
            return Promise.resolve();
          },
        },
      ],
    },
  },
];

export default connect(mapStateToProps, mapDispatchToProps)(BaseInfoForm);
