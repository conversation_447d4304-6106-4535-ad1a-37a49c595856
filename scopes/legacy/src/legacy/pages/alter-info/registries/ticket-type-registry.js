import get from 'lodash/get';
import set from 'lodash/set';

/**
 * @typedef {'tickets'|'specific-ticket'|'new-ticket'} PageType
 *
 * @typedef Options
 * @property {PageType} type page type
 */

class TicketTypeRegistry {
  /**
   * @private
   * @type {string[]} type codes
   */
  _ticketTypes = [];

  /**
   * @private
   * @type {{ [ticketType: string]: { tickets: {}, specificTicket: {}, newTicket: {} } }} ticket type config by type code & page type
   */
  _configMap = {};

  /**
   * @private
   * @param {string} ticketType ticket type code
   */
  _getTicketTypeInstance = ticketType => {
    const ticketTypeInst = {
      getConfig: pageType => this.getTicketTypeConfig(ticketType, pageType),
      registerConfig: options => {
        this.registerTicketTypeConfig(ticketType, options);

        return ticketTypeInst;
      },
      hasConfig: pageType => this.hasTicketConfig(ticketType, pageType),
    };

    return ticketTypeInst;
  };

  /**
   * @param {string} ticketType ticket type code
   */
  getTicketTypeInstance = ticketType => {
    if (!this.hasTicketType(ticketType)) {
      return null;
    }

    return this._getTicketTypeInstance(ticketType);
  };

  /**
   * @param {string} ticketType ticket type code
   * @param {PageType} page type
   */
  getTicketTypeConfig = (ticketType, pageType) => {
    return get(this._configMap, [ticketType, pageType], null);
  };

  /**
   * @param {string} ticketType ticket type code
   */
  hasTicketType = ticketType => {
    return this._ticketTypes.includes(ticketType);
  };

  /**
   * @param {string} ticketType ticket type code
   * @param {PageType} page type
   */
  hasTicketConfig = (ticketType, pageType) => {
    return this.getTicketTypeConfig(ticketType, pageType) !== null;
  };

  /**
   * @param {string} ticketType ticket type code
   */
  registerTicketType = ticketType => {
    if (!this.hasTicketType(ticketType)) {
      this._ticketTypes.push(ticketType);
    }

    return this._getTicketTypeInstance(ticketType);
  };

  /**
   * @param {string} ticketType ticket type
   * @param {Options} options set config options
   */
  registerTicketTypeConfig = (ticketType, options) => {
    if (this.hasTicketConfig(ticketType, options.type)) {
      return this._getTicketTypeInstance(ticketType);
    }

    return this.updateTicketTypeConfig(ticketType, options);
  };

  /**
   * @param {string} ticketType ticket type
   * @param {Options} options set config options
   */
  updateTicketTypeConfig = (ticketType, { type, ...config }) => {
    set(this._configMap, [ticketType, type], config);

    return this;
  };
}

let ticketTypeRegistry = new TicketTypeRegistry();

export default ticketTypeRegistry;
