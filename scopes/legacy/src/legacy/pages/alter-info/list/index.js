import React, { Component } from 'react';
import { connect } from 'react-redux';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';
import { alterInfoActions } from '@manyun/dc-brain.legacy.redux/actions/alterInfoActions';

import DataCard from './data-card';
import SearchCard from './search-card';

class List extends Component {
  render() {
    return (
      <GutterWrapper mode="vertical">
        <SearchCard />
        <DataCard />
      </GutterWrapper>
    );
  }
}

const mapStateToProps = ({ alterInfo: { searchValues, pagination } }) => {
  return {
    searchValues,
    pagination,
  };
};

const mapDispatchToProps = {
  updateSearchValues: alterInfoActions.updateSearchValues,
  setPagination: alterInfoActions.setRecordPagination,
};

export default connect(mapStateToProps, mapDispatchToProps)(List);
