import React, { Component } from 'react';
import { connect } from 'react-redux';

import { FiltersForm, Form, Select } from '@galiojs/awesome-antd';

import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Input } from '@manyun/base-ui.ui.input';

import { LocationCascader, UserSelect } from '@manyun/dc-brain.legacy.components';
import {
  alterInfoActions,
  getDataActionCreator,
  resetSearchValuesActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/alterInfoActions';

import {
  ALTER_TYPE_OPTIONS,
  BIZ_STATUS_OPTIONS,
  ROLL_BACK_ALTER_STATUS_OPTIONS,
} from '../../constants';
import { getSearchValuesAndUpdateValues, setLocationSearchValues } from '../utils';

class SearchForm extends Component {
  componentWillUnmount() {
    this.props.reset();
  }

  componentDidMount() {
    const { setPagination, updateSearchValues } = this.props;
    getSearchValuesAndUpdateValues({ setPagination, updateSearchValues });
  }

  items = () => [
    {
      label: '编号ID',
      name: 'bizId',
      control: <Input allowClear />,
    },
    {
      label: '调班类型',
      name: 'alterType',
      control: (
        <Select allowClear>
          {ALTER_TYPE_OPTIONS.map(item => {
            return (
              <Select.Option key={item.value} value={item.value}>
                {item.label}
              </Select.Option>
            );
          })}
        </Select>
      ),
    },
    {
      label: '状态',
      name: 'bizStatus',
      control: (
        <Select allowClear>
          {BIZ_STATUS_OPTIONS.map(item => {
            return (
              <Select.Option key={item.value} value={item.value}>
                {item.label}
              </Select.Option>
            );
          })}
        </Select>
      ),
    },
    {
      label: '位置',
      name: 'resourcesCode',
      control: <LocationCascader currentAuthorize />,
    },
    {
      label: '申请人',
      name: 'creatorId',
      control: <UserSelect allowClear />,
    },
    {
      label: '销假状态',
      id: 'rollBackAlterStatusList',
      control: <Select allowClear mode="multiple" options={ROLL_BACK_ALTER_STATUS_OPTIONS} />,
    },
    {
      label: '申请时间',
      name: 'creatorTime',
      span: 2,
      control: (
        <DatePicker.RangePicker
          format="YYYY-MM-DD HH:mm:ss"
          placeholder={['开始时间', '结束时间']}
          showTime
        />
      ),
    },
  ];

  render() {
    const { searchValues, getData, updateSearchValues, reset } = this.props;

    const defaultPagination = { pageNum: 1, pageSize: 10 };

    return (
      <FiltersFormWarp
        items={this.items()}
        onSearch={() => {
          setLocationSearchValues(searchValues, defaultPagination);
          getData();
        }}
        onReset={() => {
          setLocationSearchValues({}, defaultPagination);
          reset();
        }}
        fields={Object.keys(searchValues).map(name => {
          const field = searchValues[name];

          return {
            ...field,
            name: name.split('.'),
          };
        })}
        onFieldsChange={changedFields => {
          updateSearchValues(
            changedFields.reduce((mapper, field) => {
              const name = field.name.join('.');
              mapper[name] = {
                ...field,
                name,
              };
              return mapper;
            }, {})
          );
        }}
      />
    );
  }
}

const mapStateToProps = ({ alterInfo: { searchValues, pagination } }) => {
  return {
    searchValues,
    pagination,
  };
};
const mapDispatchToProps = {
  getData: getDataActionCreator,
  updateSearchValues: alterInfoActions.updateSearchValues,
  reset: resetSearchValuesActionCreator,
  setPagination: alterInfoActions.setRecordPagination,
};

export default connect(mapStateToProps, mapDispatchToProps)(SearchForm);

const FiltersFormWarp = ({ ...props }) => {
  const [form] = Form.useForm();
  return <FiltersForm form={form} {...props} />;
};
