import React, { Component } from 'react';
import { <PERSON> } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';

import * as urls from '@manyun/dc-brain.legacy.constants/urls';

class TableActions extends Component {
  render() {
    return [
      <Link key="new-config-link" to={urls.ALTER_INFO_NEW}>
        <Button type="primary">新建</Button>
      </Link>,
    ];
  }
}
export default TableActions;
