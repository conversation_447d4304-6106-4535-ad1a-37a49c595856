import React, { Component } from 'react';
import { connect } from 'react-redux';

import { Space } from '@manyun/base-ui.ui.space';
import { Tag } from '@manyun/base-ui.ui.tag';

import { ShiftAdjustmentTicket } from '@manyun/hrm.model.shift-adjustment-ticket';
import { ResumptionAlterSchedlueRequest } from '@manyun/hrm.ui.resumption-alter-schedlue-request';
import { ShiftAdjustment } from '@manyun/hrm.ui.shift-adjustment';

import { TinyTable, UserLink } from '@manyun/dc-brain.legacy.components';
import {
  cancelAlterInfoActionCreator,
  getDataActionCreator,
  setPaginationThenGetDataActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/alterInfoActions';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import { generateAlterInfoDetaillocation } from '@manyun/dc-brain.legacy.utils/urls';

import { RevokeButton } from '../../component/revoke-button';
import {
  ALTER_TYPE_MAP,
  BizStatus,
  ROLL_BACK_ALTER_STATUS,
  ROLL_BACK_ALTER_STATUS_MAP,
} from '../../constants';
import { setLocationSearchValues } from '../utils';
import TableActions from './table-actions';

class DataTable extends Component {
  componentDidMount() {
    this.props.getData(false);
    const { syncCommonData } = this.props;
    syncCommonData({ strategy: { currentUser: 'IF_NULL' } });
  }

  paginationChangeHandler = (current, size) => {
    const { searchValues } = this.props;
    const currentPagination = { pageNum: current, pageSize: size };
    setLocationSearchValues(searchValues, currentPagination);
    this.props.setPagination(currentPagination);
  };

  render() {
    const { pagination, loading, data, total, selectedRowKeys } = this.props;

    return (
      <>
        <TinyTable
          rowKey="bizId"
          actions={
            <TableActions selectedRowKeys={selectedRowKeys} batchUpdate={this.props.batchUpdate} />
          }
          columns={getColumns(this.props.getData)}
          align={'left'}
          scroll={{ x: 'max-content' }}
          dataSource={data}
          loading={loading}
          pagination={{
            total,
            current: pagination.pageNum,
            pageSize: pagination.pageSize,
            onChange: this.paginationChangeHandler,
          }}
        />
      </>
    );
  }
}

const mapStateToProps = ({
  alterInfo: { loading, pagination, data, total, selectedRowKeys, searchValues },
}) => {
  return {
    pagination,
    loading,
    data,
    total,
    selectedRowKeys,
    searchValues,
  };
};
const mapDispatchToProps = {
  getData: getDataActionCreator,
  setPagination: setPaginationThenGetDataActionCreator,
  syncCommonData: syncCommonDataActionCreator,
  cancelAlterInfo: cancelAlterInfoActionCreator,
};
export default connect(mapStateToProps, mapDispatchToProps)(DataTable);
const getColumns = getData => [
  {
    title: '编号ID',
    dataIndex: 'bizId',
    fixed: 'left',
    dataType: {
      type: 'link',
      options: {
        to(text, record) {
          return generateAlterInfoDetaillocation({
            id: text,
          });
        },
      },
    },
  },
  {
    title: '标题',
    dataIndex: 'title',
    render: (_, record) => {
      return record.applyStaffName + '发起的' + ALTER_TYPE_MAP[record.alterType];
    },
  },
  {
    title: '调班类型',
    dataIndex: 'alterType',
    render: jobType => ALTER_TYPE_MAP[jobType],
  },
  {
    title: '内容',
    dataIndex: 'alterDetail',
    render: (_, record) => {
      return (
        <ShiftAdjustment shiftAdjustment={ShiftAdjustmentTicket.fromApiObject(record).toJSON()} />
      );
    },
  },
  {
    title: '位置',
    dataIndex: 'blockTag',
  },
  {
    title: '申请人',
    dataIndex: 'creatorName',
    render: (creatorName, record) => <UserLink userId={record.creatorId} userName={creatorName} />,
  },
  {
    title: '申请时间',
    dataIndex: 'gmtCreate',
    dataType: 'datetime',
  },
  {
    title: '完成时间',
    dataIndex: 'finishTime',
    dataType: 'datetime',
  },
  {
    title: '状态',
    dataIndex: 'bizStatus',
    render: (bizStatus, record) => {
      return (
        <Space size={6}>
          {BizStatus(bizStatus)}
          {[ROLL_BACK_ALTER_STATUS.PASS, ROLL_BACK_ALTER_STATUS.APPROVING].includes(
            record.rollBackAlter
          ) && (
            <Tag color={record.rollBackAlter === ROLL_BACK_ALTER_STATUS.PASS ? 'error' : 'warning'}>
              {ROLL_BACK_ALTER_STATUS_MAP[record.rollBackAlter]}
            </Tag>
          )}
        </Space>
      );
    },
  },
  {
    title: '操作',
    key: '_actions',
    fixed: 'right',
    render: (_, record) => {
      if (record.inspectionConfigId) {
        return '--';
      }
      return (
        <Space>
          <RevokeButton
            record={record}
            onSuccess={() => {
              getData(false);
            }}
          />
          <ResumptionAlterSchedlueRequest
            type="link"
            compact
            alterInfo={record}
            onSuccess={() => {
              getData(false);
            }}
          />
        </Space>
      );
    },
  },
];
