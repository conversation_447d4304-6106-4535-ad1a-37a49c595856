import React, { Component } from 'react';
import { connect, useDispatch } from 'react-redux';

import { ApiSelect, FiltersForm, Form } from '@galiojs/awesome-antd';
import get from 'lodash/get';

import { Button } from '@manyun/base-ui.ui.button';
import { Cascader } from '@manyun/base-ui.ui.cascader';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Divider } from '@manyun/base-ui.ui.divider';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';

import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';

import {
  <PERSON>lip<PERSON>,
  <PERSON><PERSON><PERSON>rapper,
  <PERSON>Card,
  TinyTable,
  UserLink,
} from '@manyun/dc-brain.legacy.components';
import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';
import {
  basicResourcesIdcActions,
  fetchBasicResourceIdcList,
  getAreaTreeMetaQuery,
} from '@manyun/dc-brain.legacy.redux/actions/basicResourcesIdcActions';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import * as basicResourcesIdcService from '@manyun/dc-brain.legacy.services/basicResourcesIdcService';
import * as infrastructureService from '@manyun/dc-brain.legacy.services/infrastructureService';
import {
  generateTreeData,
  getLocationSearchMap,
  getObjectOwnProps,
} from '@manyun/dc-brain.legacy.utils';

import AddIdc from './component/add-idc';
import ShowIdcInfo from './component/idc-info';

const columns = ctx => [
  {
    title: '机房编号',
    dataIndex: 'guid',
    render: (text, record) => <ShowIdcInfo text={text} type="link" record={record} />,
  },
  {
    title: '机房名称',
    dataIndex: 'name',
    render: (name, record) => {
      return (
        <Ellipsis lines={1} tooltip>
          {name}
        </Ellipsis>
      );
    },
  },
  {
    title: '国家',
    dataIndex: 'nationCode',
    exportedContent: text => ctx.props.areaTreeMetaQuery[text]?.metaName,
    render: text => (
      <span>{ctx.props.areaTreeMetaQuery[text] && ctx.props.areaTreeMetaQuery[text].metaName}</span>
    ),
  },
  {
    title: '区域',
    dataIndex: 'regionCode',
    exportedContent: text => ctx.props.areaTreeMetaQuery[text]?.metaName,
    render: text => (
      <span>{ctx.props.areaTreeMetaQuery[text] && ctx.props.areaTreeMetaQuery[text].metaName}</span>
    ),
  },
  {
    title: '省',
    dataIndex: 'provinceCode',
    exportedContent: text => ctx.props.areaTreeMetaQuery[text]?.metaName,
    render: text => (
      <span>{ctx.props.areaTreeMetaQuery[text] && ctx.props.areaTreeMetaQuery[text].metaName}</span>
    ),
  },
  {
    title: '城市',
    dataIndex: 'cityCode',
    exportedContent: text => ctx.props.areaTreeMetaQuery[text]?.metaName,
    render: text => (
      <span>{ctx.props.areaTreeMetaQuery[text] && ctx.props.areaTreeMetaQuery[text].metaName}</span>
    ),
  },
  {
    title: '区',
    dataIndex: 'districtCode',
    exportedContent: text => ctx.props.areaTreeMetaQuery[text]?.metaName,
    render: text => (
      <span>{ctx.props.areaTreeMetaQuery[text] && ctx.props.areaTreeMetaQuery[text].metaName}</span>
    ),
  },
  {
    title: '地址',
    dataIndex: 'address',
    ellipsis: true,
  },
  {
    title: '建设日期',
    dataIndex: 'constructTime',
  },
  {
    title: '投产日期',
    dataIndex: 'operationTime',
  },
  {
    title: '类别',
    dataIndex: ['idcType', 'name'],
  },
  {
    title: '状态',
    dataIndex: ['operationStatus', 'name'],
  },
  {
    title: '责任人',
    dataIndex: 'operatorName',
    render(_, { operatorId, operatorName }) {
      return <UserLink userId={operatorId} userName={operatorName} />;
    },
  },
  {
    title: '备用责任人',
    dataIndex: 'backupOperator',
    exportedContent: text =>
      !text
        ? BLANK_PLACEHOLDER
        : JSON.parse(text)
            .map(({ userName }) => userName)
            .join(','),
    render(backupOperator) {
      if (!backupOperator) {
        return BLANK_PLACEHOLDER;
      }
      try {
        return (
          <Ellipsis lines={1} tooltip>
            <GutterWrapper size="4px">
              {JSON.parse(backupOperator).map(({ userId, userName }) => (
                <UserLink key={userId} userId={userId} userName={userName} />
              ))}
            </GutterWrapper>
          </Ellipsis>
        );
      } catch (error) {
        console.error(error);
      }
    },
  },
  {
    title: '额外属性值',
    dataIndex: 'specDataList',
    visible: false,
    exportedContent(specDataList) {
      if (specDataList && Object.keys(specDataList).length) {
        return specDataList.reduce(
          (accumulator, currentValue) =>
            accumulator +
            `${currentValue.specName}:${currentValue.specValue}${currentValue.specUnit ?? ''}\n`,
          ''
        );
      } else {
        return [];
      }
    },
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 120,
    fixed: 'right',
    exportable: false,
    render: (_, record) => (
      <Space size={0}>
        <AddIdc
          buttonStyle={{ padding: 0, height: 'auto' }}
          mode="edit"
          text="编辑"
          title="编辑机房"
          type="link"
          info={record}
          searchParams={ctx.getParams()}
        />
        <Divider type="vertical" />
        <DeleteConfirm targetName={record.guid} onOk={ctx._getDeleteHandler(record.guid)}>
          <Button type="link" compact>
            删除
          </Button>
        </DeleteConfirm>
      </Space>
    ),
  },
];

class BasicResourcesIdc extends Component {
  state = {
    visible: false,
    guidList: [],
    loadingSelect: false,
    selectedRowKeys: [],
    selectedRows: [],
  };

  componentDidMount() {
    const { search } = this.props.location;
    const { idc = '', name = '' } = getLocationSearchMap(search, ['idc', 'name']);
    const p = this.getParams();
    const params = {
      pageNum: 1,
      pageSize: 10,
      ...p,
    };
    if (idc) {
      params.guid = idc;
      this.props.updateSearchValues({ guid: { name: 'guid', value: idc } });
    }
    if (name) {
      params.name = name;
      this.props.updateSearchValues({ name: { name: 'name', value: name } });
    }
    this.props.fetchBasicResourceIdcList(params);
    this.props.getAreaTreeMetaQuery();
    this.props.syncCommonData({ strategy: { cities: 'IF_NULL' } });
  }

  onChangePageNo = pageNum => {
    const { idcList } = this.props;
    const { pageSize } = idcList;
    const params = this.getParams();
    this.props.fetchBasicResourceIdcList({ pageNum, pageSize, ...params });
  };

  onChangePageSize = (current, pageSize) => {
    const params = this.getParams();
    this.props.fetchBasicResourceIdcList({ pageNum: 1, pageSize, ...params });
  };

  handleReset = () => {
    this.props.resetSearchValues();
    this.props.fetchBasicResourceIdcList({
      pageNum: 1,
      pageSize: 10,
    });
  };

  handleSearch = () => {
    const params = this.getParams();
    this.props.fetchBasicResourceIdcList({
      pageNum: 1,
      pageSize: 10,
      ...params,
    });
  };

  getParams = () => {
    const fieldsValue = this.props.fields;
    const params = Object.keys(fieldsValue).reduce((map, fieldName) => {
      const value = fieldsValue[fieldName]?.value;
      if ([null, undefined, ''].includes(value) || (Array.isArray(value) && value.length === 0)) {
        return map;
      } else if (fieldName === 'name' && Array.isArray(value?.label?.split(' '))) {
        map[fieldName] = value.label.split(' ')[1];
      } else if (fieldName === 'constructTime') {
        map.constructTimeEnd = value[1]?.endOf('day').valueOf();
        map.constructTimeStart = value[0]?.startOf('day').valueOf();
      } else if (fieldName === 'operationTime') {
        map.operationTimeEnd = value[1]?.endOf('day').valueOf();
        map.operationTimeStart = value[0]?.startOf('day').valueOf();
      } else if (fieldName === 'location') {
        map.nationCode = value[0];
        map.regionCode = value[1];
        map.provinceCode = value[2];
        map.cityCode = value[3];
        map.districtCode = value[4];
      } else {
        map[fieldName] = value;
      }
      return map;
    }, {});

    return params;
  };

  handleCancel = () => {
    this.setState({ visible: false });
  };

  exportIdc = () => {
    const params = this.getParams();
    basicResourcesIdcService.fetchExport(params);
  };

  onSelectChange = (selectedRowKeys, selectedRows) => {
    this.setState({
      selectedRowKeys,
      selectedRows,
    });
  };

  _getDeleteHandler =
    idcGuid =>
    async ({ reason }) => {
      if (!idcGuid) {
        console.warn(`block guid expected!`);
        return;
      }

      const { error } = await infrastructureService.deleteIdc({
        guid: idcGuid,
        operatorNotes: reason,
      });

      if (error) {
        message.error(error);
        return false;
      }
      message.success(`${idcGuid} 已成功删除！`);

      const {
        idcList: { pageNum, pageSize },
        fetchBasicResourceIdcList,
      } = this.props;
      const searchValues = {
        ...this.getParams(),
        pageNum,
        pageSize,
      };
      fetchBasicResourceIdcList(searchValues);

      return true;
    };

  render() {
    const { idcList, metaList, fields, updateSearchValues, form } = this.props;
    const { selectedRowKeys, selectedRows } = this.state;

    return (
      <GutterWrapper mode="vertical">
        <TinyCard>
          <FiltersForm
            form={form}
            items={[
              {
                label: '位置',
                name: 'location',
                control: (
                  <Cascader
                    changeOnSelect
                    options={metaList}
                    placeholder=""
                    notFoundContent={<Spin size="small" />}
                    fieldNames={{ label: 'metaName', value: 'metaCode', children: 'children' }}
                  />
                ),
              },
              {
                label: '状态',
                name: 'operationStatus',
                control: (
                  <ApiSelect
                    allowClear
                    showSearch
                    fieldNames={{ label: 'label', value: 'value' }}
                    dataService={async () => {
                      const { response } = await basicResourcesIdcService.fetchGetOperationStatus();
                      if (response) {
                        return Promise.resolve(getObjectOwnProps(response));
                      } else {
                        return Promise.resolve([]);
                      }
                    }}
                    trigger="onDidMount"
                  />
                ),
              },
              {
                label: '机房名称',
                name: 'name',
                control: (
                  <LocationTreeSelect
                    nodeTypes={['IDC']}
                    authorizedOnly
                    allowClear
                    treeNodeLabelProp="title"
                    labelInValue
                  />
                ),
              },
              {
                span: 2,
                label: '建设日期',
                name: 'constructTime',
                control: <DatePicker.RangePicker />,
              },
              {
                span: 2,
                label: '投产日期',
                name: 'operationTime',
                control: <DatePicker.RangePicker />,
              },
            ]}
            fields={Object.keys(fields).map(name => {
              const field = fields[name];
              return {
                ...field,
                name: name.split('.'),
              };
            })}
            onFieldsChange={changedFields => {
              const fileds = changedFields.reduce((mapper, field) => {
                const name = field.name.join('.');
                mapper[name] = {
                  ...field,
                  name,
                };
                return mapper;
              }, {});
              updateSearchValues(fileds);
            }}
            onSearch={this.handleSearch}
            onReset={this.handleReset}
          />
        </TinyCard>
        <TinyCard>
          <GutterWrapper mode="vertical">
            <TinyTable
              rowKey="id"
              columns={columns(this)}
              dataSource={idcList.list}
              loading={idcList.loading}
              scroll={{ x: 'max-content' }}
              actions={
                <AddIdc
                  text="新建"
                  title="新建机房"
                  type="primary"
                  searchParams={this.getParams()}
                />
              }
              exportAllData
              rowSelection={{
                selectedRowKeys,
                selectedRows,
                onChange: this.onSelectChange,
              }}
              showExport={{
                filename: '机房数据',
              }}
              exportServices={() =>
                basicResourcesIdcService.fetchExport({ ...this.getParams(), ids: selectedRowKeys })
              }
              pagination={{
                total: idcList.list.length,
                showTotal: () => `共 ${idcList.list.length} 条`,
              }}
            />
          </GutterWrapper>
        </TinyCard>
      </GutterWrapper>
    );
  }
}

const mapStateToProps = ({
  basicResourcesIdc: { idcList, areaTreeMetaQuery, searchValues },
  common: { regionCityTree },
}) => {
  const flatData = get(regionCityTree, 'parallelList', []);
  const treeData = generateTreeData(flatData, {
    key: ({ metaType, metaCode }) => `${metaType}${metaCode}`,
    typeKey: 'metaType',
    parentKey: 'parentCode',
    nodeTypes: ['R0', 'R1', 'R2', 'R3'],
    getNode: node => {
      return {
        ...node,
        label: node.metaName,
        value: node.metaCode,
      };
    },
  });

  return {
    idcList,
    metaList: treeData,
    areaTreeMetaQuery,
    fields: searchValues,
  };
};
const mapDispatchToProps = {
  fetchBasicResourceIdcList,
  onCreate: basicResourcesIdcActions.onCreate,
  updateSearchValues: basicResourcesIdcActions.updateSearchValues,
  resetSearchValues: basicResourcesIdcActions.resetSearchValues,
  getAreaTreeMetaQuery,
  syncCommonData: syncCommonDataActionCreator,
};

function BasicResourcesIdcList(props) {
  const [form] = Form.useForm();
  const dispatch = useDispatch();

  return <BasicResourcesIdc form={form} dispatch={dispatch} {...props} />;
}

export default connect(mapStateToProps, mapDispatchToProps)(BasicResourcesIdcList);
