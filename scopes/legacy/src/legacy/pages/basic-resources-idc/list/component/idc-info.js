import React, { Component } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Modal } from '@manyun/base-ui.ui.modal';

import { Idc } from '@manyun/resource-hub.model.idc';
import { IdcInfos } from '@manyun/resource-hub.ui.idc-infos';

class IdcInfo extends Component {
  state = {
    visible: false,
  };

  onVisible = async () => {
    this.setState({
      visible: true,
    });
  };

  onClose = () => {
    this.setState({
      visible: false,
    });
  };

  render() {
    const { text, type, record } = this.props;
    const { visible } = this.state;

    return (
      <>
        <Button compact type={type} onClick={this.onVisible}>
          {text}
        </Button>
        <Modal
          width={900}
          title="机房信息"
          footer={null}
          visible={visible}
          onCancel={this.onClose}
          onClose={this.onClose}
        >
          <IdcInfos instance={Idc.fromApiObject(record)} />
        </Modal>
      </>
    );
  }
}

export default IdcInfo;
