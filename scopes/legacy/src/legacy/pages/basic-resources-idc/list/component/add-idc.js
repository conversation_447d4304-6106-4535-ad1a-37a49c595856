import Form from '@ant-design/compatible/es/form';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { ApiSelect } from '@galiojs/awesome-antd';
import get from 'lodash/get';
import omit from 'lodash/omit';
import moment from 'moment';
import React, { Component } from 'react';
import { connect, useSelector } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import { Spin } from '@manyun/base-ui.ui.spin';
import { TimePicker } from '@manyun/base-ui.ui.time-picker';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { flattenTreeData } from '@manyun/dc-brain.util.flatten-tree-data';
import { fetchSpecs } from '@manyun/resource-hub.service.fetch-specs';
import { getComponent, getInitialValue } from '@manyun/resource-hub.ui.spec-form-items';
import { VALUE_TYPE_KEY_MAP } from '@manyun/resource-hub.ui.spec-select';

import { TinyDrawer, UserSelect } from '@manyun/dc-brain.legacy.components';
import {
  basicResourcesIdcActions,
  fetchBasicResourceIdcList,
} from '@manyun/dc-brain.legacy.redux/actions/basicResourcesIdcActions';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import * as basicResourcesIdcService from '@manyun/dc-brain.legacy.services/basicResourcesIdcService';
import { getObjectOwnProps } from '@manyun/dc-brain.legacy.utils';

const options = [
  { value: 'AUTH_PERIOD', label: '授权有效期内' },
  { value: 'REAL', label: '实时' },
];

class AddCreateIdc extends Component {
  state = {
    regionList: [],
    provinceList: [],
    cityList: [],
    tagList: [],
    districtList: [],
    Spec_ACS_PERIOD: 'custom',
    accessControlFace: false,
  };

  getOptions = (list = this.props.metaList) => {
    const element = (list || []).map(item => (
      <Select.Option key={item.metaCode}>{item.metaName}</Select.Option>
    ));
    return element;
  };

  processingAreaData = (list, value) => {
    let returnList = [];
    list.forEach(item => {
      if (item.metaCode === value && Array.isArray(item.children) && item.children.length) {
        returnList = item.children;
      }
    });
    return returnList;
  };

  onChangeNation = value => {
    const { form } = this.props;
    const regionList = this.processingAreaData(this.props.metaList, value);
    this.setState({
      regionList,
    });
    form.setFieldsValue({ regionCode: '', provinceCode: '', cityCode: '', districtCode: '' });
  };

  onChangeRegion = value => {
    const { regionList } = this.state;
    const { form } = this.props;
    const provinceList = this.processingAreaData(regionList, value);
    this.setState({
      provinceList,
    });
    form.setFieldsValue({ provinceCode: '', cityCode: '', districtCode: '' });
  };

  onChangeProvince = value => {
    const { provinceList } = this.state;
    const { form } = this.props;
    const cityList = this.processingAreaData(provinceList, value);
    this.setState({
      cityList,
    });
    form.setFieldsValue({ cityCode: '', districtCode: '' });
  };

  onChangeCity = value => {
    const { cityList } = this.state;
    const { form } = this.props;
    const districtList = this.processingAreaData(cityList, value);
    this.setState({
      districtList,
    });
    form.setFieldsValue({ districtCode: '' });
  };

  getParams = () => {
    const data = this.props.form.getFieldsValue();
    const { specInfo, regionList } = this.state;

    const params = omit(
      {
        ...data,
        regionName: flattenTreeData(regionList).find(region => region.metaCode === data.regionCode)
          ?.metaName,
      },
      [
        'constructTime',
        'operationTime',
        'operationStatus',
        'idcType',
        FORM_ITEM_ID_MAP.RESPONSIBLE_STAFF,
        FORM_ITEM_ID_MAP.BACKUP_RESPONSIBLE_STAFFS,
      ]
    );
    if (data.constructTime) {
      params.constructTime = data.constructTime.valueOf();
    }
    if (data.operationTime) {
      params.operationTime = data.operationTime.valueOf();
    }
    if (data.operationStatus) {
      params.operationStatus = data.operationStatus.key;
    }
    if (data.idcType) {
      params.idcType = data.idcType.key;
    }
    if (data[FORM_ITEM_ID_MAP.RESPONSIBLE_STAFF]) {
      params.operatorId = data[FORM_ITEM_ID_MAP.RESPONSIBLE_STAFF].key;
      params.operatorName = data[FORM_ITEM_ID_MAP.RESPONSIBLE_STAFF].label;
    }
    if (data[FORM_ITEM_ID_MAP.BACKUP_RESPONSIBLE_STAFFS]) {
      params.backupOperator = data[FORM_ITEM_ID_MAP.BACKUP_RESPONSIBLE_STAFFS].map(
        ({ key, label }) => ({ userId: key, userName: label })
      );
    }

    const specParams = {};
    Object.keys(data)
      .filter(key => key.indexOf('Spec_') > -1)
      .forEach(key => {
        specParams[key] = moment.isMoment(data[key])
          ? moment(data[key]).format('HH:mm')
          : data[key]; // 兼容属性信息的时间选择
      });
    const arr = [];

    Object.keys(specParams).forEach(item => {
      const spec = specInfo.find(spec => spec.code === item.replace('Spec_', ''));
      const specValue = specParams[item];

      if (spec?.specCode === 'ACS_PERIOD' && this.props.showControlFaceCustom) {
        if (data?.Spec_ACS_PERIOD === 'custom' && data?.Spec_ACS_PERIOD_VALUE) {
          arr.push({
            specId: spec?.id,
            specName: spec?.specName,
            specValue: moment(data?.Spec_ACS_PERIOD_VALUE).format('HH:mm'),
          });
        } else {
          arr.push({
            specId: spec?.id,
            specName: spec?.specName,
            specValue: data?.Spec_ACS_PERIOD_VALUE,
          });
        }

        return;
      }

      if (Array.isArray(specValue) && spec?.optionType) {
        specValue.forEach(val => {
          arr.push({
            specId: spec?.id,
            specName: spec?.specName,
            specCode: spec?.specCode,
            specValue: val,
          });
        });

        return;
      }

      arr.push({
        specId: spec?.id,
        specName: spec?.specName,
        specCode: spec?.specCode,
        specValue: Array.isArray(specValue) ? specValue[specValue.length - 1] : specValue,
      });
    });

    params.specParams = arr.filter(item => !!item.specValue && !!item.specId);

    return params;
  };

  onVisible = async () => {
    const params = { deviceType: this.props.idcDeviceType };
    if (this.props.info?.guid) {
      params.modelId = this.props.info.guid;
    }
    const { data, error } = await fetchSpecs(params);
    if (error) {
      message.error(error.message);
      return;
    }
    this.setState({ specInfo: data.data, createVisible: true });
    if (this.props.info) {
      // 初始化地区下拉选择的数据
      if (this.props.metaList) {
        this.meta(this.props.metaList, 0);
      }
    }
  };

  meta = (list, num) => {
    const info = {
      0: this.props.info.nationCode,
      1: this.props.info.regionCode,
      2: this.props.info.provinceCode,
      3: this.props.info.cityCode,
    };
    let newList = [];
    list.forEach(item => {
      if (item.children.length && item.metaCode === info[num]) {
        this.meta(item.children, Number(num) + 1);
        newList = item.children;
      }
    });
    if (num === 0) {
      this.setState({
        regionList: newList,
      });
    }
    if (num === 1) {
      this.setState({
        provinceList: newList,
      });
    }
    if (num === 2) {
      this.setState({
        cityList: newList,
      });
    }
    if (num === 3) {
      this.setState({
        districtList: newList,
      });
    }
  };

  onClose = () => {
    this.setState({ createVisible: false });
  };

  handleSubmit = () => {
    this.props.form.validateFields((err, values) => {
      const errs = Object.keys(err);
      if (Array.isArray(errs) && errs[0]) {
        let errorElement = document.querySelector(`.${errs[0]}`);
        if (errorElement) {
          // 滚动到该字段的位置
          errorElement.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
          });
        }
      }
    });
    this.props.form.validateFieldsAndScroll(
      { scroll: { offsetBottom: TinyDrawer.FOOTER_HEIGHT } },
      async (err, values) => {
        if (err) {
          return;
        }
        this.props.onCreateLoading();
        const data = this.getParams();

        if (this.props.info) {
          data.guid = this.props?.info?.guid;
          const res = await basicResourcesIdcService.fetchEditIdc(data);
          if (res.response) {
            this.onClose();
            this.props.onCreateLoading();
            message.success('修改机房信息成功');
            this.props.fetchBasicResourceIdcList({
              ...this.props.searchParams,
              pageNum: 1,
              pageSize: 10,
            });
          } else {
            this.props.onCreateLoading();
            message.error(res.error);
          }
        } else {
          const res = await basicResourcesIdcService.fetchCreateIdc(data);
          if (res.response) {
            message.success('新建机房成功');
            this.props.onCreateLoading();
            this.props.syncCommonData({ strategy: { space: 'FORCED' } });
            this.onClose();
            this.props.fetchBasicResourceIdcList({
              ...this.props.searchParams,
              pageNum: 1,
              pageSize: 10,
            });
          } else {
            this.props.onCreateLoading();
            message.error(res.error);
          }
        }
      }
    );
  };

  render() {
    const {
      mode,
      form,
      createLoading,
      text,
      title,
      type,
      info = {},
      buttonStyle = {},
    } = this.props;
    const {
      regionList,
      provinceList,
      cityList,
      districtList,
      createVisible,
      selectLoading,
      specInfo,
      accessControlFace,
    } = this.state;
    const { getFieldDecorator, getFieldValue } = form;

    const isEditMode = mode === 'edit';

    return [
      <Button key="1" style={buttonStyle} type={type} onClick={this.onVisible}>
        {text}
      </Button>,
      <TinyDrawer
        key="2"
        width={416}
        title={title}
        visible={createVisible}
        submitButtonLoading={createLoading}
        destroyOnClose
        onClose={this.onClose}
        onSubmit={this.handleSubmit}
        onCancel={this.onClose}
      >
        <Form colon={false} layout="vertical">
          <Form.Item className="guid" label="机房编号">
            {getFieldDecorator('guid', {
              rules: [
                { required: true, message: '机房编号必填！' },
                {
                  max: 8,
                  message: '最多输入 8 个字符！',
                },
                {
                  pattern: /^[a-zA-Z0-9]+$/,
                  message: '机房编号必须是英文或数字',
                },
              ],
              initialValue: info.guid,
            })(<Input style={{ width: 200 }} placeholder="例如 EC01" disabled={isEditMode} />)}
          </Form.Item>
          <Form.Item className="name" label="机房名称">
            {getFieldDecorator('name', {
              rules: [
                { required: true, whitespace: true, message: '机房名称为必填' },
                {
                  max: 30,
                  message: '最多输入 30 个字符！',
                },
              ],
              initialValue: info.name,
            })(<Input style={{ width: 200 }} />)}
          </Form.Item>
          <Form.Item className="nationCode" label="国家">
            {getFieldDecorator('nationCode', {
              rules: [{ required: true, whitespace: true, message: '国家为必填' }],
              initialValue: info.nationCode,
            })(
              <Select
                style={{ width: 200 }}
                getPopupContainer={trigger => trigger.parentNode}
                showSearch
                onChange={this.onChangeNation}
              >
                {this.getOptions()}
              </Select>
            )}
          </Form.Item>
          <Form.Item className="regionCode" label="区域">
            {getFieldDecorator('regionCode', {
              rules: [{ required: true, whitespace: true, message: '区域为必填' }],
              initialValue: info.regionCode,
            })(
              <Select
                style={{ width: 200 }}
                showSearch
                disabled={!form.getFieldValue('nationCode')}
                getPopupContainer={trigger => trigger.parentNode}
                onChange={this.onChangeRegion}
              >
                {this.getOptions(regionList)}
              </Select>
            )}
          </Form.Item>
          <Form.Item className="provinceCode" label="省">
            {getFieldDecorator('provinceCode', {
              rules: [{ required: true, whitespace: true, message: '省为必填' }],
              initialValue: info.provinceCode,
            })(
              <Select
                style={{ width: 200 }}
                showSearch
                disabled={!form.getFieldValue('regionCode')}
                getPopupContainer={trigger => trigger.parentNode}
                onChange={this.onChangeProvince}
              >
                {this.getOptions(provinceList)}
              </Select>
            )}
          </Form.Item>
          <Form.Item className="cityCode" label="城市">
            {getFieldDecorator('cityCode', {
              rules: [{ required: true, whitespace: true, message: '城市为必填' }],
              initialValue: info.cityCode,
            })(
              <Select
                style={{ width: 200 }}
                showSearch
                disabled={!form.getFieldValue('provinceCode')}
                getPopupContainer={trigger => trigger.parentNode}
                onChange={this.onChangeCity}
              >
                {this.getOptions(cityList)}
              </Select>
            )}
          </Form.Item>
          <Form.Item className="districtCode" label="区">
            {getFieldDecorator('districtCode', {
              rules: [{ required: true, whitespace: true, message: '区为必填' }],
              initialValue: info.districtCode,
            })(
              <Select
                style={{ width: 200 }}
                showSearch
                getPopupContainer={trigger => trigger.parentNode}
                disabled={!form.getFieldValue('cityCode')}
              >
                {this.getOptions(districtList)}
              </Select>
            )}
          </Form.Item>
          <Form.Item className="address" label="地址">
            {getFieldDecorator('address', {
              rules: [
                { required: true, whitespace: true, message: '地址为必填' },
                {
                  max: 100,
                  message: '最多输入 100 个字符！',
                },
              ],
              initialValue: info.address,
            })(<Input.TextArea />)}
          </Form.Item>
          <Form.Item className="idcType" label="类别">
            {getFieldDecorator('idcType', {
              rules: [{ required: true, message: '类别为必填' }],
              initialValue: info.idcType
                ? {
                    key: info.idcType.code,
                    label: info.idcType.name,
                  }
                : undefined,
            })(
              <ApiSelect
                style={{ width: 200 }}
                showSearch
                notFoundContent={selectLoading ? <Spin size="small" /> : null}
                fieldNames={{ label: 'label', value: 'value' }}
                getPopupContainer={trigger => trigger.parentNode}
                labelInValue
                dataService={async () => {
                  const { response } = await basicResourcesIdcService.fetchGetIdcType();
                  if (response) {
                    return Promise.resolve(getObjectOwnProps(response));
                  } else {
                    return Promise.resolve([]);
                  }
                }}
              />
            )}
          </Form.Item>
          <Form.Item className="constructTime" label="建设日期">
            {getFieldDecorator('constructTime', {
              rules: [{ required: true, message: '建设日期为必填' }],
              initialValue: info.constructTime && moment(info.constructTime),
            })(
              <DatePicker
                style={{ width: 200 }}
                format="YYYY-MM-DD"
                getCalendarContainer={trigger => trigger.parentNode}
              />
            )}
          </Form.Item>
          <Form.Item className="operationTime" label="投产日期">
            {getFieldDecorator('operationTime', {
              rules: [{ required: true, message: '投产日期为必填' }],
              initialValue: info.operationTime && moment(info.operationTime),
            })(
              <DatePicker
                style={{ width: 200 }}
                getCalendarContainer={trigger => trigger.parentNode}
                format="YYYY-MM-DD"
              />
            )}
          </Form.Item>
          <Form.Item className="operationStatus" label="状态">
            {getFieldDecorator('operationStatus', {
              rules: [{ required: true, message: '状态为必填' }],
              initialValue: info.operationStatus && {
                label: info.operationStatus.name,
                key: info.operationStatus.code,
              },
            })(
              <ApiSelect
                style={{ width: 200 }}
                getPopupContainer={trigger => trigger.parentNode}
                showSearch
                labelInValue
                fieldNames={{ label: 'label', value: 'value' }}
                dataService={async () => {
                  const { response } = await basicResourcesIdcService.fetchGetOperationStatus();
                  if (response) {
                    return Promise.resolve(getObjectOwnProps(response));
                  } else {
                    return Promise.resolve([]);
                  }
                }}
                notFoundContent={selectLoading ? <Spin size="small" /> : null}
              />
            )}
          </Form.Item>
          <Form.Item className={FORM_ITEM_ID_MAP.RESPONSIBLE_STAFF} label="责任人">
            {getFieldDecorator(FORM_ITEM_ID_MAP.RESPONSIBLE_STAFF, {
              rules: [{ required: true, message: '责任人必选！' }],
              initialValue:
                info.operatorId && info.operatorName
                  ? {
                      key: info.operatorId,
                      label: info.operatorName,
                    }
                  : undefined,
            })(
              <UserSelect
                disabledKeys={get(
                  { v: getFieldValue(FORM_ITEM_ID_MAP.BACKUP_RESPONSIBLE_STAFFS) },
                  'v',
                  []
                ).map(({ key }) => key)}
                style={{ width: 200 }}
                getPopupContainer={trigger => trigger.parentNode}
                placeholder="仅可选择1位"
                optionDisabledTooltip={{
                  title: '已选为备用责任人的用户不可选',
                }}
              />
            )}
          </Form.Item>
          <Form.Item className={FORM_ITEM_ID_MAP.BACKUP_RESPONSIBLE_STAFFS} label="备用责任人">
            {getFieldDecorator(FORM_ITEM_ID_MAP.BACKUP_RESPONSIBLE_STAFFS, {
              initialValue: getInitialBackupResponsibleStaffsValue(info.backupOperator),
            })(
              <UserSelect
                disabledKeys={[
                  Number(
                    get({ v: getFieldValue(FORM_ITEM_ID_MAP.RESPONSIBLE_STAFF) }, ['v', 'key'])
                  ),
                ]}
                style={{ width: 200 }}
                mode="multiple"
                getPopupContainer={trigger => trigger.parentNode}
                placeholder="可选择多位"
                optionDisabledTooltip={{
                  title: '已选为责任人的用户不可选',
                }}
              />
            )}
          </Form.Item>
          {Array.isArray(specInfo) &&
            specInfo.map((item, index) => (
              <Form.Item
                key={item.id}
                className={`Spec_${item.specCode}`}
                label={
                  <div
                    style={{
                      display: 'flex',
                      alignItem: 'center',
                    }}
                  >
                    {item.specName}
                    {item.description && (
                      <Tooltip title={item.description}>
                        <QuestionCircleOutlined
                          style={{
                            marginLeft: 4,
                          }}
                        />
                      </Tooltip>
                    )}
                  </div>
                }
              >
                {this.props.showControlFaceCustom && item.specCode === 'ACS_PERIOD' ? ( // 旧form单独处理门禁人脸
                  <Input.Group compact>
                    <Form.Item className="Spec_ACS_PERIOD">
                      {getFieldDecorator('Spec_ACS_PERIOD', {
                        initialValue: !item.specValue
                          ? 'custom'
                          : item.specValue?.includes(':') // 单独处理
                            ? 'custom'
                            : 'valid',
                      })(
                        <Select
                          style={{
                            minWidth: 100,
                          }}
                          options={[
                            {
                              value: 'custom',
                              label: !accessControlFace ? '自定义' : '当天',
                            },
                            {
                              value: 'valid',
                              label: '有效条件',
                            },
                          ]}
                          onSelect={value => {
                            if (value === 'valid') {
                              form.setFieldsValue({ Spec_ACS_PERIOD_VALUE: 'AUTH_PERIOD' });
                            } else if (value === 'custom') {
                              form.setFieldsValue({ Spec_ACS_PERIOD_VALUE: undefined });
                            }
                          }}
                        />
                      )}
                    </Form.Item>
                    <Form.Item className="Spec_ACS_PERIOD_VALUE">
                      {getFieldDecorator('Spec_ACS_PERIOD_VALUE', {
                        initialValue: getInitialValue(item)
                          ? getInitialValue(item)
                          : form.getFieldValue('Spec_ACS_PERIOD') === 'custom'
                            ? undefined
                            : item.value
                              ? item.value
                              : 'AUTH_PERIOD',
                      })(
                        form.getFieldValue('Spec_ACS_PERIOD') === 'custom' ? (
                          <TimePicker format="HH:mm" />
                        ) : (
                          <Select
                            style={{
                              minWidth: 120,
                            }}
                            options={
                              !accessControlFace
                                ? options.filter(option => option.value !== 'REAL')
                                : options
                            }
                          />
                        )
                      )}
                    </Form.Item>
                  </Input.Group>
                ) : (
                  getFieldDecorator(`Spec_${item.specCode}`, {
                    rules:
                      item.valueType === VALUE_TYPE_KEY_MAP.CHARACTER && item.inputWay === 'INPUT'
                        ? [
                            { required: item.required, message: `${item.specName}必填！` },
                            {
                              max: 200,
                              message: '最多输入 200 个字符！',
                            },
                          ]
                        : [{ required: item.required, message: `${item.specName}必填！` }],
                    initialValue: getInitialValue(item),
                  })(
                    getComponent(item, {
                      showControlFaceCustom: this.props.showControlFaceCustom,
                    })
                  )
                )}
              </Form.Item>
            ))}

          {mode === 'edit' && (
            <Form.Item className="operatorNotes" label="操作备注">
              {getFieldDecorator('operatorNotes', {
                rules: [
                  { required: true, message: '操作备注必填！' },
                  {
                    max: 128,
                    message: '最多输入 128 个字符！',
                  },
                ],
              })(
                <Input.TextArea
                  autoSize={{ minRows: 3 }}
                  placeholder={`请输入修改 ${info.guid} 信息的原因！`}
                />
              )}
            </Form.Item>
          )}
        </Form>
        <DidMount
          setState={v => {
            this.setState({ ...this.state, accessControlFace: v });
          }}
        />
      </TinyDrawer>,
    ];
  }
}
function DidMount({ setState }) {
  const [configUtil] = useConfigUtil();

  const { accessControlFace = false } = configUtil.getScopeCommonConfigs('resources')?.specs ?? {
    accessControlFace: false,
  };
  React.useEffect(() => {
    setState(accessControlFace);
  }, [accessControlFace]);
  return <></>;
}

const FORM_ITEM_ID_MAP = {
  RESPONSIBLE_STAFF: 'responsibleStaff',
  BACKUP_RESPONSIBLE_STAFFS: 'backupResponsibleStaffs',
};

const mapStateToProps = ({
  basicResourcesIdc: { idcList, createLoading, createVisible, operationStatusList, selectLoading },
  common: { regionCityTree },
  config: { configMap },
}) => {
  const configUtil = new ConfigUtil(configMap.current);
  const idcDeviceType = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_IDC);
  const resourcesConfig = configUtil.getScopeCommonConfigs('resources');
  const showControlFaceCustom = resourcesConfig?.specs?.showControlFaceCustom; // 阳高环境走额外组件
  return {
    idcDeviceType,
    idcList,
    metaList: get(regionCityTree, 'treeList', []),
    createLoading,
    createVisible,
    operationStatusList,
    selectLoading,
    showControlFaceCustom,
  };
};
const mapDispatchToProps = {
  fetchBasicResourceIdcList,
  onCreateLoading: basicResourcesIdcActions.onCreateLoading,
  syncCommonData: syncCommonDataActionCreator,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(Form.create({ name: 'basic_resource_idc_create_edit' })(AddCreateIdc));

function getInitialBackupResponsibleStaffsValue(backupOperatorJsonStr) {
  if (!(backupOperatorJsonStr && typeof backupOperatorJsonStr == 'string')) {
    return;
  }
  let backupResponsibleStaffs;
  try {
    backupResponsibleStaffs = JSON.parse(backupOperatorJsonStr);
  } catch (error) {
    console.error(error);
    return;
  }
  if (!Array.isArray(backupResponsibleStaffs)) {
    return;
  }

  return backupResponsibleStaffs.map(({ userId, userName }) => ({
    key: userId,
    label: userName,
  }));
}
