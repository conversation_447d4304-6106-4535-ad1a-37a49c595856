import trim from 'lodash/trim';
import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import { SpaceOrDeviceLink } from '@manyun/resource-hub.ui.space-or-device-link';
import { purchasePoolExport } from '@manyun/ticket.service.purchase-pool-export';
import { transferPoolExport } from '@manyun/ticket.service.transfer-pool-export';

import { GutterWrapper, TinyCard, TinyTable } from '@manyun/dc-brain.legacy.components';
import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';
import {
  ACCEPT_TYPE_KEY_MAP,
  ACCEPT_TYPE_OPTIONS,
} from '@manyun/dc-brain.legacy.pages/ticket/configs/accept/constants';
import {
  initialArrivalAssetListActionCreator,
  setPaginationThenGetDataActionCreator,
  updatePageActiveTabKeyActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/arrivalAssetActions';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import { getDeviceTypeName } from '@manyun/dc-brain.legacy.utils/ticket';
import * as urlsUtil from '@manyun/dc-brain.legacy.utils/urls';

import NewLink from './components/new-link';
import SearchForm from './components/search-form';

function getQ(activeTabKey, pagination, searchValues) {
  const baseQ = { ...pagination, sourceType: activeTabKey };
  if (!searchValues) {
    return baseQ;
  }
  for (const [, { name, value }] of Object.entries(searchValues)) {
    if ([null, undefined, ''].includes(value) || (Array.isArray(value) && value.length === 0)) {
      continue;
    }
    if (name === 'deviceType') {
      if (value.firstCategoryCode) {
        baseQ['topCategory'] = value.firstCategoryCode;
      }
      if (value.secondCategoryCode) {
        baseQ['secondCategory'] = value.secondCategoryCode;
      }
      if (value.thirdCategorycode) {
        baseQ[name] = value.thirdCategorycode;
      }
    } else if (name === 'vendorModel') {
      baseQ['vendor'] = value[0];
      baseQ['productModel'] = value[1];
    } else if (name === 'arrivalTime') {
      baseQ['arrivalStartTime'] = value[0].clone().startOf('day').valueOf();
      baseQ['arrivalEndTime'] = value[1].clone().endOf('day').valueOf();
    } else if (name === 'targetBlockGuid' || name === 'sourceBlockGuid') {
      baseQ[name] = value.join('.');
    } else {
      baseQ[name] = Array.isArray(value) ? value : trim(value);
    }
  }
  return baseQ;
}
function ArrivalAssetList({
  activeTabKey,
  loading,
  dataSource,
  total,
  pagination,
  setPagination,
  totalNum,
  updateActiveTabKey,
  initData,
  syncCommonData,
  normalizedList,
  searchValues,
}) {
  const [selectedRows, setSelectedRows] = useState({});

  useEffect(() => {
    initData();
    syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  useEffect(() => {
    setSelectedRows({});
  }, [activeTabKey]);

  return (
    <GutterWrapper mode="vertical">
      <TinyCard>
        <Tabs defaultActiveKey={activeTabKey} onChange={tabKey => updateActiveTabKey(tabKey)}>
          {ACCEPT_TYPE_OPTIONS.map(item => {
            return (
              <Tabs.TabPane tab={`${item.label}预到货(${totalNum[item.value]})`} key={item.value} />
            );
          })}
        </Tabs>
        <SearchForm key={activeTabKey} />
      </TinyCard>
      <TinyCard>
        <TinyTable
          key={activeTabKey}
          rowKey="id"
          align="left"
          loading={loading}
          dataSource={dataSource}
          columns={getColumns(activeTabKey, normalizedList)}
          actions={
            <div style={{ width: '100%', display: 'flex', justifyContent: 'space-between' }}>
              <GutterWrapper>
                <NewLink selectedRows={selectedRows[activeTabKey]} />
                <Link to={urlsUtil.generateArrivalAssetNewLocation({ type: activeTabKey })}>
                  <Button>导入</Button>
                </Link>
              </GutterWrapper>
              <>
                <FileExport
                  showExportFiltered
                  showExportSelected={selectedRows[activeTabKey]?.length}
                  filename={
                    activeTabKey === 'PURCHASE'
                      ? '采购预到货'
                      : activeTabKey === 'MOVE'
                        ? '调拨预到货'
                        : ''
                  }
                  data={async type => {
                    const params = getQ(activeTabKey, pagination, searchValues[activeTabKey]);
                    if (type === 'selected') {
                      params['selectedIdList'] = selectedRows[activeTabKey].map(item => item.id);
                    }
                    // export-risk-check-ticket.ts
                    if (activeTabKey === 'PURCHASE') {
                      const { error, data } = await purchasePoolExport(params);
                      if (error) {
                        message.error(error.message);
                        return;
                      }
                      return data;
                    }
                    if (activeTabKey === 'MOVE') {
                      const { error, data } = await transferPoolExport(params);
                      if (error) {
                        message.error(error.message);
                        return;
                      }
                      return data;
                    }
                  }}
                />
              </>
            </div>
          }
          selectRowsSpreadPage
          rowSelection={{
            selectedRowKeys: selectedRows[activeTabKey]?.map(row => row.id) || [],
            selectedRows: selectedRows[activeTabKey] || [],
            onChange: (_, selectedRows) =>
              setSelectedRows({ ...selectedRows, [activeTabKey]: selectedRows }),
          }}
          pagination={{
            total,
            current: pagination.pageNum,
            pageSize: pagination.pageSize,
            onChange: (current, size) => setPagination({ pageNum: current, pageSize: size }),
          }}
        />
      </TinyCard>
    </GutterWrapper>
  );
}

const mapStateToProps = ({
  common: { deviceCategory },
  arrivalAsset: { arrivalAssetList, loading, activeTabKey, pagination, totalNum, searchValues },
}) => {
  return {
    loading,
    activeTabKey,
    dataSource: arrivalAssetList[activeTabKey].data,
    total: arrivalAssetList[activeTabKey].total,
    pagination,
    totalNum,
    normalizedList: deviceCategory?.normalizedList,
    searchValues,
  };
};
const mapDispatchToProps = {
  setPagination: setPaginationThenGetDataActionCreator,
  updateActiveTabKey: updatePageActiveTabKeyActionCreator,
  initData: initialArrivalAssetListActionCreator,
  syncCommonData: syncCommonDataActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(ArrivalAssetList);

const getColumns = (activeTabKey, normalizedList) => {
  const defaultColumns = [
    {
      title: '资产ID',
      dataIndex: 'assetNo',
      type: [ACCEPT_TYPE_KEY_MAP.MOVE],
      render: assetNo => {
        if (!assetNo) {
          return BLANK_PLACEHOLDER;
        }
        return <SpaceOrDeviceLink id={assetNo} type="DEVICE_ASSET_NO" />;
      },
    },
    {
      title: '三级分类',
      dataIndex: 'deviceType',
      type: [ACCEPT_TYPE_KEY_MAP.PURCHASE, ACCEPT_TYPE_KEY_MAP.MOVE],
      render: text => getDeviceTypeName(text, normalizedList),
    },
    {
      title: '厂商',
      dataIndex: 'vendor',
      type: [ACCEPT_TYPE_KEY_MAP.PURCHASE, ACCEPT_TYPE_KEY_MAP.MOVE],
    },
    {
      title: '型号',
      dataIndex: 'productModel',
      type: [ACCEPT_TYPE_KEY_MAP.PURCHASE, ACCEPT_TYPE_KEY_MAP.MOVE],
    },
    {
      title: '采购订单号',
      dataIndex: 'sourceNo',
      type: [ACCEPT_TYPE_KEY_MAP.PURCHASE],
    },
    {
      title: '调拨编号',
      dataIndex: 'sourceNo',
      type: [ACCEPT_TYPE_KEY_MAP.MOVE],
    },
    {
      title: '到货批次号',
      dataIndex: 'batchNo',
      type: [ACCEPT_TYPE_KEY_MAP.PURCHASE],
    },
    {
      title: '调出位置',
      dataIndex: 'sourceBlockGuid',
      type: [ACCEPT_TYPE_KEY_MAP.MOVE],
    },
    {
      title: '目的位置',
      dataIndex: 'targetBlockGuid',
      type: [ACCEPT_TYPE_KEY_MAP.PURCHASE, ACCEPT_TYPE_KEY_MAP.MOVE],
    },
    {
      title: '预计到货日期',
      dataIndex: 'planArrivalTime',
      dataType: 'date',
      type: [ACCEPT_TYPE_KEY_MAP.PURCHASE, ACCEPT_TYPE_KEY_MAP.MOVE],
    },
    {
      title: '到货数量',
      dataIndex: 'arrivalNum',
      type: [ACCEPT_TYPE_KEY_MAP.PURCHASE, ACCEPT_TYPE_KEY_MAP.MOVE],
    },
    {
      title: '拒收数量',
      dataIndex: 'refuseNum',
      type: [ACCEPT_TYPE_KEY_MAP.PURCHASE, ACCEPT_TYPE_KEY_MAP.MOVE],
    },
    {
      title: '实收数量',
      dataIndex: 'acceptNum',
      type: [ACCEPT_TYPE_KEY_MAP.PURCHASE, ACCEPT_TYPE_KEY_MAP.MOVE],
    },
  ];

  return defaultColumns.filter(column => column.type.includes(activeTabKey));
};
