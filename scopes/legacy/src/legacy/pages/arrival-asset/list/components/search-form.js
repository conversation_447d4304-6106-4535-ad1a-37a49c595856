import React from 'react';
import { connect, useDispatch } from 'react-redux';

import { FiltersForm, Form } from '@galiojs/awesome-antd';
import omit from 'lodash/omit';

import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Input } from '@manyun/base-ui.ui.input';

import { VendorModelSelect } from '@manyun/crm.ui.vendor-model-select';

import {
  AssetClassificationApiTreeSelect,
  LocationCascader,
} from '@manyun/dc-brain.legacy.components';
import { ACCEPT_TYPE_KEY_MAP } from '@manyun/dc-brain.legacy.pages/ticket/configs/accept/constants';
import {
  arrivalAssetActions,
  getArrivalAssetListActionCreator,
  setPaginationThenGetDataActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/arrivalAssetActions';

export function SearchForm({
  onReset,
  onSearch,
  fields,
  updateSearchValues,
  activeTabKey,
  resetSearchValues,
}) {
  const [form] = Form.useForm();
  const dispatch = useDispatch();

  return (
    <FiltersForm
      form={form}
      items={getItems(activeTabKey, fields)}
      onSearch={onSearch}
      onReset={() => {
        dispatch(resetSearchValues());
        onReset({ pageNum: 1, pageSize: 10 });
      }}
      fields={
        typeof fields === 'object'
          ? Object.keys(fields).map(name => {
              const field = fields[name];
              return {
                ...field,
                name: name.split('.'),
              };
            })
          : []
      }
      onFieldsChange={changedFields => {
        let fields = changedFields.reduce((mapper, field) => {
          const name = field.name.join('.');
          mapper[name] = {
            ...field,
            name,
          };
          return mapper;
        }, {});
        updateSearchValues({ activeTabKey, values: fields });
      }}
    />
  );
}

const mapStateToProps = ({ arrivalAsset: { activeTabKey, searchValues } }) => {
  return {
    activeTabKey,
    fields: searchValues[activeTabKey],
  };
};
const mapDispatchToProps = {
  onSearch: getArrivalAssetListActionCreator,
  onReset: setPaginationThenGetDataActionCreator,
  updateSearchValues: arrivalAssetActions.updateArrivalAssetListSearchValues,
  resetSearchValues: arrivalAssetActions.resetSearchValues,
};

export default connect(mapStateToProps, mapDispatchToProps)(SearchForm);

const getItems = (activeTabKey, fields) => {
  const defaultItems = [
    {
      label: '资产ID',
      name: 'assetNo',
      control: <Input allowClear />,
      type: [ACCEPT_TYPE_KEY_MAP.MOVE],
    },
    {
      label: '资产分类',
      name: 'deviceType',
      control: (
        <AssetClassificationApiTreeSelect
          dataType={['snDevice', 'noSnDevice']}
          category="allCategoryCode"
          allowClear
        />
      ),
      type: [ACCEPT_TYPE_KEY_MAP.PURCHASE, ACCEPT_TYPE_KEY_MAP.MOVE],
    },
    {
      label: '采购订单号',
      name: 'sourceNo',
      control: <Input allowClear />,
      type: [ACCEPT_TYPE_KEY_MAP.PURCHASE],
    },
    {
      label: '到货批次号',
      name: 'batchNo',
      control: <Input allowClear />,
      type: [ACCEPT_TYPE_KEY_MAP.PURCHASE],
    },
    {
      label: '调拨编号',
      name: 'sourceNo',
      control: <Input allowClear />,
      type: [ACCEPT_TYPE_KEY_MAP.MOVE],
    },
    {
      label: '调出位置',
      name: 'sourceBlockGuid',
      control: <LocationCascader allowClear currentAuthorize changeOnSelect={false} />,
      type: [ACCEPT_TYPE_KEY_MAP.MOVE],
    },
    {
      label: '目的位置',
      name: 'targetBlockGuid',
      control: <LocationCascader allowClear currentAuthorize changeOnSelect={false} />,
      type: [ACCEPT_TYPE_KEY_MAP.PURCHASE, ACCEPT_TYPE_KEY_MAP.MOVE],
    },
    {
      label: '预计到货日期',
      name: 'arrivalTime',
      span: 2,
      control: <DatePicker.RangePicker format="YYYY-MM-DD" />,
      type: [ACCEPT_TYPE_KEY_MAP.PURCHASE, ACCEPT_TYPE_KEY_MAP.MOVE],
    },
    {
      label: '厂商型号',
      name: 'vendorModel',
      control: <VendorModelSelect allowClear />,
      type: [ACCEPT_TYPE_KEY_MAP.PURCHASE, ACCEPT_TYPE_KEY_MAP.MOVE],
    },
  ];
  const filterItems = defaultItems.filter(item => item.type.includes(activeTabKey));

  return filterItems.map(item => omit(item, ['type']));
};
