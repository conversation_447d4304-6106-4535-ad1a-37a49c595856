import React, { Component } from 'react';
import { connect } from 'react-redux';

import uniqBy from 'lodash/uniqBy';

import { Button } from '@manyun/base-ui.ui.button';
import { notification } from '@manyun/base-ui.ui.notification';

import { ACCEPT_TYPE_KEY_MAP } from '@manyun/dc-brain.legacy.pages/ticket/configs/accept/constants';
import { redirectActionCreator } from '@manyun/dc-brain.legacy.redux/actions/routerActions';
import * as urlsUtil from '@manyun/dc-brain.legacy.utils/urls';

class NewLink extends Component {
  handleClick = () => {
    const { selectedRows, activeTabKey, redirect } = this.props;
    const text = activeTabKey === ACCEPT_TYPE_KEY_MAP.PURCHASE ? '到货批次号' : '调拨编号';
    const relateNo = activeTabKey === ACCEPT_TYPE_KEY_MAP.PURCHASE ? 'batchNo' : 'sourceNo';
    const relateNos = uniqBy(selectedRows, relateNo);
    const ids = selectedRows.map(row => row.id);
    if (relateNos.length > 1) {
      notification.warning({
        message: '无法收货',
        description: `存在多个${text}，请按每个${text}收货`,
      });
    } else {
      const params = { ticketType: 'accept', variables: { acceptType: activeTabKey } };
      if (ids.length) {
        params.variables = { ...params.variables, relateNo: relateNos[0][relateNo], ids };
      }
      redirect(urlsUtil.generateTicketCreateUrl(params));
    }
  };

  render() {
    return (
      <Button type="primary" onClick={this.handleClick}>
        收货
      </Button>
    );
  }
}

const mapStateToProps = ({ arrivalAsset: { activeTabKey } }) => {
  return {
    activeTabKey,
  };
};
const mapDispatchToProps = {
  redirect: redirectActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(NewLink);
