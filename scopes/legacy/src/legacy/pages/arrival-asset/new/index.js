import React, { Component } from 'react';
import { connect } from 'react-redux';

import QuestionCircleOutlined from '@ant-design/icons/es/icons/QuestionCircleOutlined';
import { saveAs } from 'file-saver';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';

import { Upload } from '@manyun/dc-brain.ui.upload';

import { <PERSON><PERSON>Wrapper, StatusText, TinyCard, TinyTable } from '@manyun/dc-brain.legacy.components';
import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';
import { ARRIVAL_ASSET_LIST } from '@manyun/dc-brain.legacy.constants/urls';
import { ACCEPT_TYPE_KEY_MAP } from '@manyun/dc-brain.legacy.pages/ticket/configs/accept/constants';
import { redirectActionCreator } from '@manyun/dc-brain.legacy.redux/actions/routerActions';
import * as arrivalAssetService from '@manyun/dc-brain.legacy.services/arrivalAssetService';
import { getLocationSearchMap } from '@manyun/dc-brain.legacy.utils';

const getColumns = activeTabKey => {
  const defaultColumns = [
    {
      title: '行号',
      dataIndex: ['errDto', 'rowTag'],
      fixed: 'left',
      type: [ACCEPT_TYPE_KEY_MAP.PURCHASE, ACCEPT_TYPE_KEY_MAP.MOVE],
    },
    {
      title: '资产ID',
      dataIndex: ['errDto', 'assetNo'],
      type: [ACCEPT_TYPE_KEY_MAP.MOVE],
      render: txt => txt || BLANK_PLACEHOLDER,
    },
    {
      title: '三级分类',
      dataIndex: ['errDto', 'deviceType'],
      type: [ACCEPT_TYPE_KEY_MAP.PURCHASE, ACCEPT_TYPE_KEY_MAP.MOVE],
    },
    {
      title: '厂商',
      dataIndex: ['errDto', 'vendor'],
      type: [ACCEPT_TYPE_KEY_MAP.PURCHASE, ACCEPT_TYPE_KEY_MAP.MOVE],
    },
    {
      title: '型号',
      dataIndex: ['errDto', 'productModel'],
      type: [ACCEPT_TYPE_KEY_MAP.PURCHASE, ACCEPT_TYPE_KEY_MAP.MOVE],
    },
    {
      title: '采购订单号',
      dataIndex: ['errDto', 'sourceNo'],
      type: [ACCEPT_TYPE_KEY_MAP.PURCHASE],
    },
    {
      title: '调拨编号',
      dataIndex: ['errDto', 'sourceNo'],
      type: [ACCEPT_TYPE_KEY_MAP.MOVE],
    },
    {
      title: '到货批次号',
      dataIndex: ['errDto', 'batchNo'],
      type: [ACCEPT_TYPE_KEY_MAP.PURCHASE],
    },
    {
      title: '调出位置',
      dataIndex: ['errDto', 'sourceBlockGuid'],
      type: [ACCEPT_TYPE_KEY_MAP.MOVE],
    },
    {
      title: '目的位置',
      dataIndex: ['errDto', 'targetBlockGuid'],
      type: [ACCEPT_TYPE_KEY_MAP.PURCHASE, ACCEPT_TYPE_KEY_MAP.MOVE],
    },
    {
      title: '预计到货日期',
      dataIndex: ['errDto', 'planArrivalTime'],
      dataType: 'date',
      type: [ACCEPT_TYPE_KEY_MAP.PURCHASE, ACCEPT_TYPE_KEY_MAP.MOVE],
    },
    {
      title: '到货数量',
      dataIndex: ['errDto', 'arrivalNum'],
      type: [ACCEPT_TYPE_KEY_MAP.PURCHASE, ACCEPT_TYPE_KEY_MAP.MOVE],
    },
  ].filter(column => column.type.includes(activeTabKey));
  const assetsColumns = defaultColumns.map(column => {
    return {
      ...column,
      render: (text, { errMessage, rowTag }) => {
        return getToolTilp(
          column.dataIndex[1] === 'rowTag' ? rowTag : text,
          errMessage,
          column.dataIndex[1]
        );
      },
    };
  });

  return assetsColumns;
};

class NewAsset extends Component {
  state = {
    type: ACCEPT_TYPE_KEY_MAP.PURCHASE,
    importList: {},
    importLoading: false,
  };

  componentDidMount() {
    const { location } = this.props.history;
    const { type } = getLocationSearchMap(location.search, ['type']);
    this.setState({ type });
  }

  changeImportLoading = () => {
    this.setState({ importLoading: !this.state.importLoading });
  };

  downloadDemo = async () => {
    const { type } = this.state;
    const { response, filename = 'import-assets-template.csv' } =
      type === ACCEPT_TYPE_KEY_MAP.PURCHASE
        ? await arrivalAssetService.downloadPurchaseAssetModel()
        : await arrivalAssetService.downloadMoveAssetModel();
    if (!response) {
      return;
    }
    saveAs(response, filename);
  };

  beforeUpload = file => {
    const fd = new FormData();
    fd.append('file', file);
    this.loadFetchData(fd);
    return false;
  };

  loadFetchData = async fd => {
    const { type } = this.state;
    this.changeImportLoading();
    const { response, error } =
      type === ACCEPT_TYPE_KEY_MAP.PURCHASE
        ? await arrivalAssetService.importPurchaseAsset(fd)
        : await arrivalAssetService.importWoveAsset(fd);
    this.changeImportLoading();
    if (error) {
      message.error(error || '导入请求失败');
      return;
    }
    if (response) {
      if (response.faultTotal === 0) {
        message.success('导入成功！');
        this.props.redirect(ARRIVAL_ASSET_LIST);
      } else {
        this.setState({
          importList: response,
        });
      }
    }
  };

  render() {
    const { importList, importLoading, type } = this.state;

    return (
      <>
        <TinyCard>
          <TinyTable
            align="left"
            rowKey="rowTag"
            actionsWrapperStyle={{ justifyContent: 'space-between' }}
            actions={[
              <GutterWrapper flex key="left">
                <Upload
                  key="import"
                  beforeUpload={this.beforeUpload}
                  showUploadList={false}
                  accept=".csv,.xls,.xlsx"
                >
                  <Button type="primary" loading={importLoading}>
                    导入
                  </Button>
                </Upload>
                <Button key="download" onClick={this.downloadDemo}>
                  下载模板
                </Button>
              </GutterWrapper>,
              <div key="total">
                {importList.excelCheckErrDtos && (
                  <Space>
                    <StatusText status="normal">导入总数&nbsp;{importList.checkTotal}</StatusText>
                    <StatusText status="normal">正确总数&nbsp;{importList.correctTotal}</StatusText>
                    <StatusText status="alarm">错误行数&nbsp;{importList.faultTotal}</StatusText>
                  </Space>
                )}
              </div>,
            ]}
            dataSource={importList.excelCheckErrDtos}
            loading={importLoading}
            columns={getColumns(type)}
            scroll={{ x: 'max-content' }}
            pagination={{
              total: importList.excelCheckErrDtos ? importList.excelCheckErrDtos.length : 0,
            }}
          />
        </TinyCard>
      </>
    );
  }
}

const mapDispatchToProps = {
  redirect: redirectActionCreator,
};

export default connect(null, mapDispatchToProps)(NewAsset);

function getToolTilp(value, errMessage, dataType) {
  if (Object.prototype.hasOwnProperty.call(errMessage, dataType)) {
    return (
      <Tooltip title={errMessage[dataType]} placement="topLeft">
        <GutterWrapper size="2px" flex alignItems="center">
          <StatusText status="alarm" style={{ display: 'inline-block' }}>
            {value ? value : '--'}
          </StatusText>
          <QuestionCircleOutlined />
        </GutterWrapper>
      </Tooltip>
    );
  }
  return value || '--';
}
