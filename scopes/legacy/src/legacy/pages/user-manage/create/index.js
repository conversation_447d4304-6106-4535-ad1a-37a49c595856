import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import CloseOutlined from '@ant-design/icons/es/icons/CloseOutlined';
import PlusOutlined from '@ant-design/icons/es/icons/PlusOutlined';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Typography } from '@manyun/base-ui.ui.typography';

import { DEFAULT_USER_PASSWORD_VALIDATION_RULES } from '@manyun/auth-hub.model.user';

import { GutterWrapper, StyledInput, TinyCard } from '@manyun/dc-brain.legacy.components';
import { fetchAddNewUser } from '@manyun/dc-brain.legacy.redux/actions/userManageActions';

import { DEFAULT_PASSWORD } from '../constants';

class UserManageCreate extends Component {
  state = {
    userInfoList: [
      { id: new Date().getTime(), userName: '', loginName: '', mobile: '', email: '', remarks: '' },
    ],
  };

  deleteUserInfo = record => {
    const { userInfoList } = this.state;
    const newList = userInfoList.filter(item => record.id !== item.id);
    this.setState({
      userInfoList: newList,
    });
  };

  addUser = () => {
    const { userInfoList } = this.state;
    this.props.form.validateFields(err => {
      if (!err) {
        this.setState({
          userInfoList: userInfoList.concat([
            { id: new Date().getTime(), userName: '', loginName: '', mobile: '', email: '' },
          ]),
        });
      }
    });
  };

  confirm = () => {
    this.props.form.validateFields(err => {
      if (err) {
        return;
      }
      const { userInfoList } = this.state;
      const userInfos = userInfoList.map(item => {
        return {
          userName: item.userName,
          loginName: item.loginName,
          mobile: item.mobile,
          email: item.email,
          remarks: item.remarks,
        };
      });
      const {
        passwordType,
        password = DEFAULT_PASSWORD,
        //  resetPassword, multiFactorAuthentication
      } = this.props.form.getFieldsValue();
      this.props.fetchAddNewUser({
        userInfos,
        passwordType,
        password,
        resetPassword: false,
        multiFactorAuthentication: false,
      });
    });
  };

  saveUsers = (e, type, index) => {
    const { userInfoList } = this.state;
    const newList = [...userInfoList];
    newList[index][type] = e.target.value;
    this.setState({ userInfoList: newList });
  };

  // onPasswordTypeChange = ({ target: { value } }) => {
  //   if (value === 'CUSTOM') {
  //     this.props.form.setFieldsValue({ password: '' });
  //   }
  // };

  render() {
    const { userInfoList } = this.state;
    const { form, addNewUserLoading } = this.props;
    const { getFieldDecorator } = form;
    const radioStyle = {
      display: 'block',
      height: '30px',
      lineHeight: '30px',
    };

    return (
      <TinyCard>
        <GutterWrapper mode="vertical">
          <Typography.Title level={4}>新建用户</Typography.Title>
          <Form colon={false}>
            <Form.Item
              label={
                <span>
                  用户账户信息
                  {/* <Button type="link">同步钉钉账户</Button> */}
                </span>
              }
            />
            <Form.Item>
              <GutterWrapper flex style={{ marginRight: 30 }}>
                <Typography.Text style={{ width: '20%' }}>
                  用户ID<span style={{ color: `var(--${prefixCls}-error-color)` }}>*</span>
                </Typography.Text>
                <Typography.Text style={{ width: '15%' }}>
                  用户名称<span style={{ color: `var(--${prefixCls}-error-color)` }}>*</span>
                </Typography.Text>
                <Typography.Text style={{ width: '15%' }}>
                  手机号码<span style={{ color: `var(--${prefixCls}-error-color)` }}>*</span>
                </Typography.Text>
                <Typography.Text style={{ width: '15%' }}>
                  邮箱<span style={{ color: `var(--${prefixCls}-error-color)` }}>*</span>
                </Typography.Text>
                <Typography.Text style={{ width: '35%' }}>备注</Typography.Text>
              </GutterWrapper>
              {userInfoList.map((item, index) => (
                <GutterWrapper flex key={index}>
                  <Form.Item style={{ width: '20%' }}>
                    {getFieldDecorator(`loginName_${index}`, {
                      rules: [
                        { required: true, whitespace: true, message: '用户名称必须输入' },
                        { max: 64, message: '不可超过64位' },
                        {
                          pattern: /^[a-zA-Z0-9_-]+$/,
                          message: '用户ID必须是英文、数字、“-”或“_”',
                        },
                      ],
                      initialValue: item.loginName,
                    })(<StyledInput onChange={e => this.saveUsers(e, 'loginName', index)} />)}
                  </Form.Item>
                  <Form.Item style={{ width: '15%' }}>
                    {getFieldDecorator(`userName_${index}`, {
                      rules: [
                        { required: true, whitespace: true, message: '显示名称必须输入' },
                        { max: 10, message: '最多输入 10 个字符！' },
                      ],
                      initialValue: item.userName,
                    })(<Input onChange={e => this.saveUsers(e, 'userName', index)} />)}
                    {/*  http://chandao.manyun-local.com/zentao/bug-view-4015.html */}
                  </Form.Item>
                  <Form.Item style={{ width: '15%' }}>
                    {getFieldDecorator(`mobile_${index}`, {
                      rules: [
                        { required: true, whitespace: true, message: '手机号码必须输入' },
                        {
                          pattern: /^1[3456789]\d{9}$/, // 需要和后端的校验规则保持一致
                          message: '手机号码格式不正确，请重新填写',
                        },
                      ],
                      initialValue: item.mobile,
                    })(<Input onChange={e => this.saveUsers(e, 'mobile', index)} />)}
                  </Form.Item>
                  <Form.Item style={{ width: '15%' }}>
                    {getFieldDecorator(`email_${index}`, {
                      rules: [{ required: true, whitespace: true, message: '邮件地址必须输入' }],
                      initialValue: item.email,
                    })(<Input onChange={e => this.saveUsers(e, 'email', index)} />)}
                  </Form.Item>
                  <Form.Item style={{ width: '35%' }}>
                    {getFieldDecorator(`remark_${index}`, {
                      rules: [{ max: 128, message: '最多输入 128 个字符！' }],
                      // rules: [{ required: true, whitespace: true, message: '邮件地址必须输入' }],
                      initialValue: item.remarks,
                    })(<Input onChange={e => this.saveUsers(e, 'remarks', index)} />)}
                  </Form.Item>
                  {index !== 0 ? (
                    <span>
                      <CloseOutlined onClick={() => this.deleteUserInfo(item)} />
                    </span>
                  ) : (
                    <span style={{ width: 14 }}></span>
                  )}
                </GutterWrapper>
              ))}
              <Button type="link" onClick={this.addUser} icon={<PlusOutlined />}>
                新建用户
              </Button>
            </Form.Item>
            <Form.Item label="登录密码">
              {getFieldDecorator('passwordType', {
                initialValue: 'DEFAULT',
              })(
                <Radio.Group>
                  <Radio style={radioStyle} value="DEFAULT">
                    使用默认密码 <Typography.Text code>{DEFAULT_PASSWORD}</Typography.Text>
                  </Radio>
                  <Radio style={radioStyle} value="CUSTOM">
                    自定义密码
                  </Radio>
                </Radio.Group>
              )}
            </Form.Item>
            {form.getFieldValue('passwordType') === 'CUSTOM' && (
              <Form.Item>
                {getFieldDecorator('password', {
                  validateFirst: true,
                  rules: DEFAULT_USER_PASSWORD_VALIDATION_RULES,
                })(<Input.Password style={{ width: 320 }} />)}
              </Form.Item>
            )}
            {/* <Form.Item label="要求重置密码">
            {getFieldDecorator('resetPassword', {
              initialValue: true,
            })(
              <Radio.Group>
                <Radio style={radioStyle} value={true}>
                  用户再下次登录时必须重置密码
                </Radio>
                <Radio style={radioStyle} value={false}>
                  无需重置
                </Radio>
              </Radio.Group>
            )}
          </Form.Item>
          <Form.Item label="是否开启多因素认证">
            {getFieldDecorator('multiFactorAuthentication', {
              initialValue: true,
            })(
              <Radio.Group>
                <Radio style={radioStyle} value={true}>
                  要求开启多因素认证
                </Radio>
                <Radio style={radioStyle} value={false}>
                  不要求
                </Radio>
              </Radio.Group>
            )}
          </Form.Item> */}
            <Form.Item>
              <Button type="primary" onClick={this.confirm} loading={addNewUserLoading}>
                提交
              </Button>
            </Form.Item>
          </Form>
        </GutterWrapper>
      </TinyCard>
    );
  }
}

const mapStateToProps = ({ userManage: { addNewUserLoading } }) => ({
  addNewUserLoading,
});
const mapDispatchToProps = { fetchAddNewUser };

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(Form.create({ name: 'user_manage_create' })(UserManageCreate));
