import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import { Select } from '@galiojs/awesome-antd';
import trim from 'lodash/trim';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Typography } from '@manyun/base-ui.ui.typography';

import { generateUserManageDetailUrlLocation } from '@manyun/auth-hub.route.auth-routes';
import { User } from '@manyun/auth-hub.ui.user';

import { GutterWrapper, TinyCard, TinyTable } from '@manyun/dc-brain.legacy.components';
import {
  fetchDelete,
  loadUserManageInfo,
  userManageActions,
} from '@manyun/dc-brain.legacy.redux/actions/userManageActions';
import { generateUserManageNewUrl } from '@manyun/dc-brain.legacy.utils/urls';

import AssociatedUserGroup from '../component/associated-user-group';

const { Title } = Typography;
const InputGroup = Input.Group;
const { Option } = Select;
const { Search } = Input;

const columns = ctx => [
  {
    title: '用户名称',
    dataIndex: 'userName',
    render: (text, { id }) => <User.Link id={id} name={text} />,
  },
  {
    title: '用户ID',
    dataIndex: 'loginName',
    ellipsis: true,
  },
  {
    title: '关联用户组',
    dataIndex: 'userGroup',
    width: 100,
    dataType: {
      type: 'link',
      options: {
        to(__, { id }) {
          // return `/page/auth/user-manage/detail/${id}/2`;
          return generateUserManageDetailUrlLocation({ id, defaultTab: '2' });
        },
        text: '查看',
      },
    },
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    dataType: 'datetime',
    width: 164,
  },
  {
    title: '更新时间',
    dataIndex: 'gmtModified',
    dataType: 'datetime',
    width: 164,
  },
  {
    title: '创建人',
    dataIndex: 'creatorName',
    render: (text, { creatorId }) => <User.Link id={creatorId} name={text} />,
  },
  {
    title: '备注',
    dataIndex: 'remarks',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    fixed: 'right',
    width: 150,
    render: (_, record) => (
      <span>
        <AssociatedUserGroup
          needFetchList={false}
          type="link"
          defaultSelectedSources={[{ label: record.userName, key: record.id }]}
          style={{ padding: 0, height: 'auto' }}
        />
        <Divider type="vertical" />
        <DeleteConfirm
          variant="popconfirm"
          title={`确认删除用户： ${record.userName}吗？`}
          onOk={() => ctx.onDelete(record)}
        >
          <Button type="link" style={{ padding: 0, height: 'auto' }}>
            删除
          </Button>
        </DeleteConfirm>
        {/* <Popover
          content={
            <GutterWrapper>
              <p>{`确认删除用户： ${record.userName}吗？`}</p>
              <Button
                style={{ padding: 0, height: 'auto', marginLeft: 0 }}
                type="link"
                onClick={() => ctx.onDelete(record)}
              >
                确认
              </Button>
            </GutterWrapper>
          }
          trigger="focus"
        >
          <Button type="link" style={{ padding: 0, height: 'auto' }}>
            删除
          </Button>
        </Popover> */}
      </span>
    ),
  },
];

class UserManageList extends Component {
  state = {
    selectedRowsList: [],
    selectedRowKeys: [],
    visible: false,
  };

  componentDidMount() {
    this.props.loadUserManageInfo({
      pageNo: 1,
      pageSize: 10,
      searchType: 'USER_NAME',
      searchName: '',
    });
  }

  onParamKey = paramKey => {
    const { userList } = this.props;
    this.props.success({
      ...userList,
      searchType: paramKey,
    });
  };

  onParamValue = value => {
    const { userList } = this.props;
    const { searchType, pageSize } = userList;
    this.props.loadUserManageInfo({
      searchType,
      searchName: trim(value),
      pageNo: 1,
      pageSize,
    });
  };

  onSelectChange = (selectedRowKeys, selectedRows) => {
    const selectedRowsList = selectedRows.map(item => {
      return {
        label: item.userName,
        key: item.id,
      };
    });
    this.setState({ selectedRowsList, selectedRowKeys });
  };

  onChangePage = (pageNo, pageSize) => {
    const { userList } = this.props;
    const { searchType, searchName } = userList;
    this.props.loadUserManageInfo({ searchType, searchName, pageNo, pageSize });
  };

  onDelete = row => {
    const { userList } = this.props;
    const { searchType, pageSize, searchName, pageNo } = userList;
    if (row.userName === 'admin') {
      message.error('禁止删除admin用户');
      return;
    }
    this.props.fetchDelete({
      userId: row.id,
      searchType,
      pageSize,
      searchName,
      pageNo,
    });
  };

  render() {
    const { selectedRowKeys, selectedRowsList } = this.state;
    const { userList } = this.props;

    return (
      <GutterWrapper mode="vertical">
        <TinyCard>
          <GutterWrapper mode="vertical">
            <Title level={4}>用户</Title>
            <Alert message="用户的账号来源于钉钉企业组织架构，使用单独密码进行登录" type="info" />
          </GutterWrapper>
        </TinyCard>
        <TinyCard>
          <TinyTable
            rowKey="id"
            columns={columns(this)}
            align="left"
            dataSource={userList.list}
            loading={userList.loading}
            // scroll={{ x: true }}
            rowSelection={{
              selectedRowKeys,
              onChange: this.onSelectChange,
            }}
            pagination={{
              total: userList.total,
              current: userList.pageNo,
              onChange: this.onChangePage,
              pageSize: userList.pageSize,
            }}
            actions={
              <GutterWrapper flex justifyContent="flex-start">
                <Link key="add" to={generateUserManageNewUrl()}>
                  <Button type="primary">新建用户</Button>
                </Link>
                <AssociatedUserGroup
                  key="connect-user-group"
                  needFetchList={false}
                  type="primary"
                  defaultSelectedSources={selectedRowsList}
                />
                <InputGroup key="search" compact style={{ width: '20%', minWidth: 400 }}>
                  <Select
                    value={userList.searchType}
                    onChange={this.onParamKey}
                    style={{ width: '40%' }}
                  >
                    <Option value="USER_NAME">用户名称</Option>
                    <Option value="LOGIN_NAME">登录名称</Option>
                    <Option value="CREATOR_NAME">创建人名称</Option>
                  </Select>
                  <Search allowClear onSearch={this.onParamValue} style={{ width: '50%' }} />
                </InputGroup>
              </GutterWrapper>
            }
          />
        </TinyCard>
      </GutterWrapper>
    );
  }
}

const mapStateToProps = ({
  userManage: { userList, total, loading, associatedUserGroupVisible },
}) => ({
  userList,
  total,
  loading,
  userGroupVisible: associatedUserGroupVisible,
});
const mapDispatchToProps = {
  loadUserManageInfo,
  selectUserListSuccess: userManageActions.selectUserListSuccess,
  selectedUser: userManageActions.selectedUser,
  fetchDelete,
  associatedUserGroupVisible: userManageActions.associatedUserGroupVisible,
  success: userManageActions.success,
};

export default connect(mapStateToProps, mapDispatchToProps)(UserManageList);
