/* eslint-disable @typescript-eslint/no-explicit-any */
import { useApolloClient } from '@apollo/client';
import PageHeader from 'antd/es/page-header';
import moment from 'moment';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { User } from '@manyun/auth-hub.model.user';
import { UserContact } from '@manyun/auth-hub.page.user-profile';
import { USERS_ROUTE_PATH } from '@manyun/auth-hub.route.auth-routes';
import { AuthorizationRecordTable } from '@manyun/auth-hub.ui.authorization-record-table';
import { DeptText } from '@manyun/auth-hub.ui.dept-text';
import { UserLink } from '@manyun/auth-hub.ui.user';
import { UserGenderText } from '@manyun/auth-hub.ui.user-gender';
import { UserTypeText } from '@manyun/auth-hub.ui.user-type';
import { Button } from '@manyun/base-ui.ui.button';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Divider } from '@manyun/base-ui.ui.divider';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import { Typography } from '@manyun/base-ui.ui.typography';
import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { fetchCertList } from '@manyun/hrm.service.fetch-cert-list';
import { CoverEditCardList } from '@manyun/hrm.ui.cover-edit-card';
import { UserShiftsText } from '@manyun/hrm.ui.user-shifts';
import { fetchUserSkills } from '@manyun/knowledge-hub.service.dcexam.fetch-user-skills';
import { Skill as SkillCard } from '@manyun/knowledge-hub.ui.skill';
import { readSpace } from '@manyun/resource-hub.gql.client.spaces';
import { MetaTypeText } from '@manyun/resource-hub.ui.meta-type-text';
import { SpaceText } from '@manyun/resource-hub.ui.space-text';

import { GutterWrapper, TinyCard } from '@manyun/dc-brain.legacy.components';
import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';
import { redirectActionCreator } from '@manyun/dc-brain.legacy.redux/actions/routerActions';
import {
  fetchJoinUserGroup,
  fetchLog,
  fetchRemoveUserGroup,
  fetchUserManageDetail,
  userManageActions,
} from '@manyun/dc-brain.legacy.redux/actions/userManageActions';

import EditBasicInfo from '../component/edit-basicInfo';
import EditLoginSetting from '../component/edit-login-settings';
import Log from '../component/log';
import { OperationLogPopup } from '../component/operation-log-popup';

const { Paragraph } = Typography;
function RenderBlockLabel({ blockGuid }) {
  const client = useApolloClient();
  const blockLabel = readSpace(client, blockGuid);
  return <>{blockLabel ? blockLabel?.label : (blockGuid ?? '--')}</>;
}

function EditingBasicInformation({ editBasic }) {
  const [, { checkCode }] = useAuthorized();
  return (
    checkCode('element_user_editing_has_configuration_information') && (
      <Button type="link" style={{ padding: 0, height: 'auto' }} onClick={editBasic}>
        编辑基本信息
      </Button>
    )
  );
}
function AccountLoginManagement({ loginManage }) {
  const [, { checkCode }] = useAuthorized();
  return (
    checkCode('element_change_login_password') && (
      <Button type="link" size="small" onClick={loginManage}>
        修改登录密码
      </Button>
    )
  );
}
class UserManageDetail extends Component {
  state = {
    editVisible: false,
    associatedVisible: false,
    filterTypeValue: '',
    pageNo: 1,
    pageSize: 10,
    filterType: 'userGroup',
    // district: '',
    // city: '',
    certDataList: [],
    operationLogOpen: false,
  };

  componentDidMount() {
    const { filterType, pageNo, pageSize, filterTypeValue } = this.state;
    const { id, loginName } = getLocationSearchMap(this.props.location.search);
    this.props.fetchUserManageDetail({
      userId: id,
      loginName: loginName,
      filterType,
      pageNo,
      pageSize,
      filterTypeValue,
    });
    const { showDrawer } = getLocationSearchMap(this.props.location.search);
    if (showDrawer === 'true') {
      this.editBasic();
    }
    // this.props.fetchJoinUserGroup({
    //   userId: match.params.id,
    //   filterType,
    //   pageNo,
    //   pageSize,
    //   filterTypeValue,
    // });
  }

  componentDidUpdate(prevProps) {
    const { id } = getLocationSearchMap(prevProps.location.search);
    const nowData = getLocationSearchMap(this.props.location.search);
    if (id !== nowData.id) {
      const { filterType, pageNo, pageSize, filterTypeValue } = this.state;
      this.props.fetchUserManageDetail({
        userId: nowData.id,
        loginName: nowData.loginName,
        filterType,
        pageNo,
        pageSize,
        filterTypeValue,
      });
    }
  }

  componentWillUnmount() {
    this.props.reset();
  }

  editBasic = () => {
    this.props.editBasicInfoVisible();
  };

  loginManage = () => {
    this.props.editLoginSettingVisible();
  };

  onView = () => {
    this.props.fetchLog({
      userId: this.props.basicInfo.id,
      pageNo: 1,
      pageSize: 10,
      callback: canOpen => canOpen && this.props.logVisible(),
    });
  };

  onFilterType = filterType => {
    this.setState({ filterType });
  };

  onParamValue = value => {
    const { filterType, pageNo, pageSize } = this.state;
    this.setState({ filterTypeValue: value });
    this.props.fetchJoinUserGroup({
      filterType,
      filterTypeValue: value,
      pageNo,
      pageSize,
      userId: this.props.basicInfo.id,
    });
  };

  onChangePage = (pageNo, pageSize) => {
    const { filterType, filterTypeValue } = this.state;
    this.props.fetchJoinUserGroup({
      filterType,
      filterTypeValue,
      pageNo,
      pageSize,
      userId: this.props.basicInfo.id,
    });
    this.setState({ pageNo, pageSize });
  };

  onChangeTabs = tabs => {
    if (tabs === '2') {
      const { filterType, pageNo, pageSize, filterTypeValue } = this.state;

      this.props.fetchJoinUserGroup({
        userId: this.props.basicInfo.id,
        filterType,
        pageNo,
        pageSize,
        filterTypeValue,
      });
    }

    if (tabs === '3') {
      this._fetchCertList();
      this._fetchSkillList();
    }
  };

  remove = groupId => {
    const { filterType, pageNo, pageSize, filterTypeValue } = this.state;

    this.props.fetchRemoveUserGroup({
      userId: this.props.basicInfo.id,
      filterType,
      pageNo,
      pageSize,
      filterTypeValue,
      groupId,
    });
  };

  // onComplete = data => {
  //   this.setState({ district: data.district, city: data.city });
  // };

  _fetchCertList = async () => {
    const info = {
      userId: this.props.basicInfo.id,
    };
    const { error, data } = await fetchCertList(info);

    if (error) {
      message.error(error.message);
      return;
    }

    if (data) {
      this.setState({ certDataList: data.data });
    }
  };

  _fetchSkillList = async () => {
    const { error, data } = await fetchUserSkills({ userId: this.props.basicInfo.id, own: true });

    if (error) {
      message.error(error.message);
      return;
    }

    if (data) {
      this.setState({ skillsData: data.data });
    }
  };

  render() {
    const { certDataList, skillsData } = this.state;
    const { joinUserGroupList, basicInfo, location, redirect } = this.props;
    const { defaultTab } = getLocationSearchMap(location.search);
    let list = [];
    joinUserGroupList.forEach(item => (list = [...list, item.id]));
    const user = User.fromApiObject(basicInfo);

    const tabsItems = [
      {
        key: '1',
        label: '用户管理',
        children: (
          <Descriptions>
            <Descriptions.Item label="账号登录管理">
              <AccountLoginManagement loginManage={this.loginManage} />
            </Descriptions.Item>
            <Descriptions.Item label="最近一次登录时间">
              {user.lastLoginTime
                ? moment(user.lastLoginTime).format('YYYY-MM-DD HH:mm:ss')
                : BLANK_PLACEHOLDER}
            </Descriptions.Item>
            <Descriptions.Item label="下次登录重置密码">
              {basicInfo && (basicInfo.resetPassword ? '是' : '否')}
            </Descriptions.Item>
            <Descriptions.Item label="用户状态">
              {basicInfo && (basicInfo.enable ? '在职' : '离职')}
            </Descriptions.Item>
            {basicInfo.resignDate && (
              <Descriptions.Item label="离职日期">
                {user.resignDate ? moment(user.resignDate).format('YYYY-MM-DD') : BLANK_PLACEHOLDER}
              </Descriptions.Item>
            )}
            <Descriptions.Item label="登录日志">
              <Button type="link" size="small" onClick={this.onView}>
                查看
              </Button>
            </Descriptions.Item>
            <Descriptions.Item label="操作日志">
              <Button
                type="link"
                size="small"
                onClick={() => {
                  this.setState({ ...this.state, operationLogOpen: true });
                }}
              >
                查看
              </Button>
            </Descriptions.Item>
          </Descriptions>
        ),
      },
      {
        key: 'authorization-record',
        label: '授权记录',
        children: <AuthorizationRecordTable userId={user.id} />,
      },
      {
        key: '3',
        label: '资质认证',
        children: (
          <Space style={{ width: '100%' }} direction="vertical" size={24}>
            <Space style={{ width: '100%' }} direction="vertical">
              <Typography.Title level={5} showBadge>
                资质证书
              </Typography.Title>
              <CoverEditCardList
                userId={this.props.basicInfo.id}
                certLength={certDataList.length}
                fetchCertList={this._fetchCertList}
                certDataList={certDataList}
                isAuthEdit
              />
            </Space>
            <Space style={{ width: '100%' }} direction="vertical">
              <Typography.Title level={5} showBadge>
                技能认证
              </Typography.Title>
              <Space size={16} wrap>
                {Array.isArray(skillsData) &&
                  skillsData.length > 0 &&
                  skillsData.map(item => (
                    <SkillCard
                      key={item.id}
                      style={{ height: 172, width: 290 }}
                      id={item.id}
                      userSkillsData={item}
                      type="userManagement"
                    />
                  ))}
              </Space>
            </Space>
          </Space>
        ),
      },
    ];
    return (
      <GutterWrapper mode="vertical">
        <TinyCard style={{ width: '100%' }}>
          <PageHeader
            style={{
              padding: '0 0 16px 0',
            }}
            title={user.name}
            onBack={() => redirect(USERS_ROUTE_PATH)}
          />
          <Descriptions column={4}>
            <Descriptions.Item span={4} label="用户基本信息">
              <EditingBasicInformation editBasic={this.editBasic} />
            </Descriptions.Item>
            <Descriptions.Item label="姓名">{user.name}</Descriptions.Item>
            <Descriptions.Item label="用户ID">
              <Paragraph
                style={{ color: 'inherit', marginBottom: 0 }}
                copyable={{ text: user.login }}
              >
                {user.login}
              </Paragraph>
            </Descriptions.Item>
            <Descriptions.Item label="英文名">{user.nameEn || BLANK_PLACEHOLDER}</Descriptions.Item>
            <Descriptions.Item label="性别">
              {user.gender ? <UserGenderText userGender={user.gender} /> : BLANK_PLACEHOLDER}
            </Descriptions.Item>
            <Descriptions.Item label="生日">{user.birthday || BLANK_PLACEHOLDER}</Descriptions.Item>

            <Descriptions.Item label="类型">
              {user.type ? <UserTypeText userType={user.type} /> : BLANK_PLACEHOLDER}
            </Descriptions.Item>
            <Descriptions.Item label="所属机房">
              {user.idc ? <SpaceText guid={user.idc} /> : BLANK_PLACEHOLDER}
            </Descriptions.Item>
            <Descriptions.Item label="主楼栋">
              <RenderBlockLabel blockGuid={user.blockGuid} />
            </Descriptions.Item>
            <Descriptions.Item label="办公地点">
              {user.workplace || BLANK_PLACEHOLDER}
            </Descriptions.Item>
            <Descriptions.Item label="所属公司">
              {user.company || BLANK_PLACEHOLDER}
            </Descriptions.Item>
            <Descriptions.Item label="部门">
              <DeptText deptId={user.departmentId} fromMdm={basicInfo.createChannel === 'MDM'} />
            </Descriptions.Item>
            <Descriptions.Item label="岗位">
              <RenderUserTitle title={user.title} />
            </Descriptions.Item>
            <Descriptions.Item label="直线经理1">
              <UserLink id={user.supervisorUid} />
            </Descriptions.Item>
            <Descriptions.Item label="直线经理2">
              <UserLink id={user.subSupervisorUid} />
            </Descriptions.Item>
            <Descriptions.Item label="工作职责">
              {user.jobDescriptions || BLANK_PLACEHOLDER}
            </Descriptions.Item>
            <Descriptions.Item label="联系方式">
              <UserContact user={user} />
            </Descriptions.Item>
            <Descriptions.Item label="入职日期">
              {basicInfo.hiredDate
                ? moment(basicInfo.hiredDate).format('YYYY-MM-DD')
                : BLANK_PLACEHOLDER}
            </Descriptions.Item>
            <Descriptions.Item label="参加工作日期">
              {basicInfo.joinWorkingTime
                ? moment(basicInfo.joinWorkingTime).format('YYYY-MM-DD')
                : BLANK_PLACEHOLDER}
            </Descriptions.Item>
            <Descriptions.Item label="工时类型">
              {basicInfo.userShifts ? (
                <UserShiftsText userShifts={basicInfo.userShifts} />
              ) : (
                BLANK_PLACEHOLDER
              )}
            </Descriptions.Item>
            <Descriptions.Item label="家乡">
              {user.birthPlace || BLANK_PLACEHOLDER}
            </Descriptions.Item>
            <Descriptions.Item label="备注">
              {basicInfo.remarks || BLANK_PLACEHOLDER}
            </Descriptions.Item>
          </Descriptions>
          <Divider />
          <Descriptions column={4}>
            <Descriptions.Item label="创建人">
              <UserLink name={user.createUser?.name} id={user.createUser?.id} />
            </Descriptions.Item>
            <Descriptions.Item label="创建时间">
              {moment(user.gmtCreate).format('YYYY-MM-DD HH:mm:ss')}
            </Descriptions.Item>
            <Descriptions.Item label="更新人">
              <UserLink name={user.modifyUser?.name} id={user.modifyUser?.id} />
            </Descriptions.Item>
            <Descriptions.Item label="更新时间">
              {user.gmtModified
                ? moment(user.gmtModified).format('YYYY-MM-DD HH:mm:ss')
                : BLANK_PLACEHOLDER}
            </Descriptions.Item>
          </Descriptions>
        </TinyCard>
        <TinyCard>
          <Tabs defaultActiveKey={defaultTab} items={tabsItems} onChange={this.onChangeTabs} />
        </TinyCard>
        <EditBasicInfo />
        <EditLoginSetting />
        <Log />
        <OperationLogPopup
          userId={user.id}
          operationLogOpen={this.state.operationLogOpen}
          setOperationLogOpen={v => {
            this.setState({ ...this.state, operationLogOpen: v });
          }}
        />
      </GutterWrapper>
    );
  }
}

const mapStateToProps = ({
  userManage: { basicInfo, loading, joinUserGroupList, joinUserGroupTotal },
}) => ({
  basicInfo,
  loading,
  joinUserGroupList,
  joinUserGroupTotal,
});
const mapDispatchToProps = {
  fetchUserManageDetail,
  editBasicInfoVisible: userManageActions.editBasicInfoVisible,
  editLoginSettingVisible: userManageActions.editLoginSettingVisible,
  fetchLog,
  fetchJoinUserGroup,
  fetchRemoveUserGroup,
  selectedUser: userManageActions.selectedUser,
  logVisible: userManageActions.logVisible,
  redirect: redirectActionCreator,
  reset: userManageActions.reset,
};

export default connect(mapStateToProps, mapDispatchToProps)(UserManageDetail);

const RenderUserTitle = ({ title }) => {
  const [configUtil] = useConfigUtil();
  const isYGUser = configUtil.getScopeCommonConfigs('iam')?.userProfile?.version === 'plus';
  return isYGUser ? <MetaTypeText metaType="POSITION_YG" code={title} /> : (title ?? '--');
};
