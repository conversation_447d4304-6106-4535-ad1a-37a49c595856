import Form from '@ant-design/compatible/es/form';
import React from 'react';
import { connect } from 'react-redux';

import { DEFAULT_USER_PASSWORD_VALIDATION_RULES } from '@manyun/auth-hub.model.user';
import { Input } from '@manyun/base-ui.ui.input';

import { TinyDrawer } from '@manyun/dc-brain.legacy.components';
import {
  fetchEditLoginSettingsConfirm,
  userManageActions,
} from '@manyun/dc-brain.legacy.redux/actions/userManageActions';

import { DEFAULT_PASSWORD } from '../constants';

const formItemLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 20 },
};

function onClose(editLoginSettingVisible) {
  return () => editLoginSettingVisible();
}

function handleSubmit(form, fetchEditLoginSettingsConfirm, id) {
  return e => {
    e.preventDefault();
    form.validateFields((err, { password = DEFAULT_PASSWORD }) => {
      if (err) {
        return;
      }
      fetchEditLoginSettingsConfirm({
        password,
        passwordType: 'CUSTOM',
        userId: id,
        resetPassword: false,
        multiFactorAuthentication: false,
      });
    });
  };
}

export function EditLoginSettings({
  basicInfo,
  editVisible,
  editLoginSettingVisible,
  form,
  fetchEditLoginSettingsConfirm,
  loginSettingsLoading,
}) {
  const { getFieldDecorator, getFieldValue } = form;
  const { id } = basicInfo;

  return (
    <TinyDrawer
      width={600}
      hasExtra
      title="修改登录密码"
      open={editVisible}
      submitButtonLoading={loginSettingsLoading}
      onClose={onClose(editLoginSettingVisible)}
      onSubmit={handleSubmit(form, fetchEditLoginSettingsConfirm, id)}
      onCancel={onClose(editLoginSettingVisible)}
    >
      <Form
        colon={false}
        {...formItemLayout}
        onSubmit={handleSubmit(form, fetchEditLoginSettingsConfirm, id)}
      >
        <Form.Item
          label="新密码"
          extra={
            !getFieldValue('password')
              ? '长度至少 8 位，至多 64 位，必须同时包含大小写字母、特殊字符和数字，不允许有空格'
              : undefined
          }
        >
          {getFieldDecorator('password', {
            validateFirst: true,
            rules: DEFAULT_USER_PASSWORD_VALIDATION_RULES,
          })(<Input.Password style={{ width: 216 }} />)}
        </Form.Item>
        <Form.Item label="确认密码">
          {getFieldDecorator('confirmPassword', {
            validateFirst: true,
            rules: [
              { required: true, message: '确认密码必填!' },
              {
                validator: (_rule, value, callback) => {
                  if (value !== form.getFieldValue('password')) {
                    callback('确认密码必须和新密码相同！');
                    return;
                  }
                  callback();
                },
              },
            ],
          })(<Input.Password style={{ width: 216 }} />)}
        </Form.Item>
      </Form>
    </TinyDrawer>
  );
}

const mapStateToProps = ({
  userManage: { basicInfo, editLoginSettingVisible, loginSettingsLoading },
}) => ({
  basicInfo,
  editVisible: editLoginSettingVisible,
  loginSettingsLoading,
});

const mapDispatchToProps = {
  editLoginSettingVisible: userManageActions.editLoginSettingVisible,
  fetchEditLoginSettingsConfirm,
};
// (...args) => dispatch(saveFilterType(...args))

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(Form.create({ name: 'edit_login_setting' })(EditLoginSettings));
