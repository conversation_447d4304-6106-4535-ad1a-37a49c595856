import React, { Component } from 'react';
import { connect } from 'react-redux';

import { Modal } from '@manyun/base-ui.ui.modal';
import { App } from '@manyun/dc-brain.model.app';
import { translateClient } from '@manyun/iam.services.login';

import { TinyTable } from '@manyun/dc-brain.legacy.components';
import {
  fetchLog,
  userManageActions,
} from '@manyun/dc-brain.legacy.redux/actions/userManageActions';

const columns = [
  {
    title: '用户名称',
    dataIndex: 'userName',
    // dataType: {
    //   type: 'link',
    //   options: {
    //     to(__, { id }) {
    //       return `/page/auth/user-manage/detail/${id}/1`;
    //     },
    //   },
    // },
  },
  {
    title: '站点',
    dataIndex: 'webSite',
    render: (_, option) => {
      if (option?.webSite) {
        const _list = option.webSite.split(',');
        return (
          <div>
            {_list
              .map(webSite =>
                webSite === 'MONITOR'
                  ? `${option.idcTag ?? ''} ${App.t(`code.${webSite}`)}`
                  : App.t(`code.${webSite}`)
              )
              .join(',')}
          </div>
        );
      }
    },
  },
  {
    title: '用户端',
    dataIndex: 'client',
    render: text => {
      return <div>{text ? (text === 'H5' ? text : translateClient(text)) : '--'}</div>;
    },
  },
  {
    title: '行为',
    dataIndex: 'action',
  },
  {
    title: '时间',
    dataIndex: 'gmtCreate',
    dataType: 'datetime',
  },
  {
    title: 'IP地址',
    dataIndex: 'location',
  },
];

class Log extends Component {
  state = {
    pageNo: 1,
    pageSize: 10,
  };

  onChangePage = (pageNo, pageSize) => {
    this.props.fetchLog({ userId: this.props.userId, pageNo, pageSize });
    this.setState({ pageNo, pageSize });
  };

  onClose = () => {
    this.props.visible();
    this.setState({ pageNo: 1, pageSize: 10 });
  };

  render() {
    const { logLoading, logVisible, logTotal, logList } = this.props;
    const { pageSize, pageNo } = this.state;

    return (
      <Modal title="登录日志" width={720} open={logVisible} footer={null} onCancel={this.onClose}>
        <TinyTable
          rowKey="id"
          columns={columns}
          dataSource={logList}
          loading={logLoading}
          scroll={{ x: 'max-content' }}
          pagination={{
            total: logTotal,
            showTotal: () => `共 ${logTotal} 条`,
            current: pageNo,
            onChange: this.onChangePage,
            pageSize: pageSize,
            showSizeChanger: true,
          }}
        />
      </Modal>
    );
  }
}

const mapStateToProps = ({
  userManage: { logVisible, logLoading, logList, logTotal, basicInfo },
}) => ({
  logVisible,
  logLoading,
  logList,
  logTotal,
  userId: basicInfo.id,
});

const mapDispatchToProps = {
  visible: userManageActions.logVisible,
  fetchLog: fetchLog,
};

export default connect(mapStateToProps, mapDispatchToProps)(Log);
