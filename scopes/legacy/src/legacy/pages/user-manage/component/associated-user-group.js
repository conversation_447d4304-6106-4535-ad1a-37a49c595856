import React from 'react';
import { connect } from 'react-redux';

import { RelationshipConnectorButton } from '@manyun/dc-brain.legacy.components';
import { fetchJoinUserGroup } from '@manyun/dc-brain.legacy.redux/actions/userManageActions';
import { userManageService } from '@manyun/dc-brain.legacy.services';
import { fetchAssociatedUserGroup } from '@manyun/dc-brain.legacy.services/userManageService';

import { userGroupColumns } from './columns';

export function EditBasicInfo({
  defaultSelectedSources,
  type,
  fetchJoinUserGroup = undefined,
  needFetchList,
  data = {},
  style = {},
  id = undefined,
  checkedKeys = undefined,
  callback,
}) {
  const { pageNo, pageSize, filterTypeValue } = data;
  return (
    <RelationshipConnectorButton
      text="关联用户组"
      title="关联用户组"
      alertMessage="用户加入到用户组后，将拥有该组所有权限。"
      type={type}
      sourceTitle="用户"
      targetTitle="用户组"
      sourceSelectPlaceholder="用户 ID | 名称 支持多选 支持模糊搜索"
      targetSelectPlaceholder="搜索某个用户组并添加到一选择列表中"
      defaultSelectedSources={defaultSelectedSources}
      targetSelectService={userManageService.fetchSelectUserGroup}
      targetTableLeftColumns={userGroupColumns}
      targetTableRightColumns={userGroupColumns}
      targetDatasourceService={userManageService.fetchSelectUserGroup}
      sourceSelectService={userManageService.fetchSelectUser}
      buttonStyle={style}
      checkedKeys={checkedKeys}
      submitService={(selectedSources, selectedTargets) =>
        fetchAssociatedUserGroup(
          selectedSources,
          selectedTargets,
          id,
          fetchJoinUserGroup,
          needFetchList,
          pageNo,
          pageSize,
          filterTypeValue
        )
      }
      callback={callback}
    />
  );
}

const mapStateToProps = ({ userManage: { users } }) => ({
  users,
});

const mapDispatchToProps = {
  fetchJoinUserGroup,
};

export default connect(mapStateToProps, mapDispatchToProps)(EditBasicInfo);
