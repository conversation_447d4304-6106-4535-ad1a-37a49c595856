import get from 'lodash/get';
import moment from 'moment';
import React from 'react';
import { connect } from 'react-redux';

import { User } from '@manyun/auth-hub.model.user';
import { DeptTreeSelect } from '@manyun/auth-hub.ui.dept-tree-select';
import { UserEmailInput } from '@manyun/auth-hub.ui.user-email-input';
import { UserGenderSelect } from '@manyun/auth-hub.ui.user-gender';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { UserTypeSelect } from '@manyun/auth-hub.ui.user-type';
import { Cascader } from '@manyun/base-ui.ui.cascader';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { usePerformanceDefaultDepartments } from '@manyun/hrm.gql.client.hrm';
import { UserShiftsSelect } from '@manyun/hrm.ui.user-shifts';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';
import { MetatypeSelect } from '@manyun/resource-hub.ui.metatype-select';

import { TinyDrawer } from '@manyun/dc-brain.legacy.components';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  fetchEditConfirm,
  userManageActions,
} from '@manyun/dc-brain.legacy.redux/actions/userManageActions';
import { generateTreeData } from '@manyun/dc-brain.legacy.utils';

function onClose(editBasicInfoVisible, form) {
  return () => {
    if (form) {
      form.resetFields();
    }
    return editBasicInfoVisible();
  };
}

const scrollToErrorField = (errorFields, formRef) => {
  if (Array.isArray(errorFields) && errorFields.length >= 1) {
    const firstErrorField = errorFields[0].name;
    formRef.current.scrollToField(firstErrorField, {
      behavior: 'smooth',
      block: 'center',
    });
  }
};
function getSubmitHandler(form, fetchEditConfirm, user, formRef) {
  return e => {
    e.preventDefault();
    form
      .validateFields()
      .then(value => {
        const { email, ...rest } = value;
        const data = email === user.email ? rest : { email, ...rest };

        fetchEditConfirm({
          id: user.id,
          ...data,
          birthday: data.birthday ? moment(data.birthday).format('YYYY-MM-DD') : undefined,
          company: data.type === 'STAFF' ? null : data.company,
          birthPlace: data.birthPlace ? data.birthPlace.join('/') : undefined,
          supervisorUid: data.supervisorUid?.value,
          subSupervisorUid: data.subSupervisorUid?.value,
          userShifts: data.userShifts !== undefined ? data.userShifts : null,
          nameEn: data.nameEn !== undefined ? data.nameEn : null,
          idc:
            data.idc !== undefined ? (Array.isArray(data.idc) ? data.idc.at(-1) : data.idc) : null,
          blockGuid:
            data.blockGuid !== undefined
              ? Array.isArray(data.blockGuid)
                ? data.blockGuid.at(-1)
                : data.blockGuid
              : null,
        });
      })
      .catch(error => {
        scrollToErrorField(error.errorFields, formRef);
      });
  };
}

export function EditBasicInfo({
  basicInfo,
  editBasicInfoVisible,
  visible,
  form,
  editBasicInfoLoading,
  fetchEditConfirm,
  metaList,
  syncCommonData,
}) {
  const isFromMDM = basicInfo.createChannel === 'MDM';
  const disabledEditIdc = !basicInfo.canEditIdc;

  const formRef = React.useRef(null);
  const type = Form.useWatch('type', form);
  const email = Form.useWatch('email', form);
  const idc = Form.useWatch('idc', form);
  const departmentId = Form.useWatch('departmentId', form);

  const user = User.fromApiObject(basicInfo);
  const { data: performanceDefaultDepartments } = usePerformanceDefaultDepartments();
  const [configUtil] = useConfigUtil();
  const isYGUser = configUtil.getScopeCommonConfigs('iam')?.userProfile?.version === 'plus';

  const emailRules = React.useMemo(() => {
    // email 初始值为 undefined（不为 initialValue），此时也不进行校验
    if (email === undefined || email === user.email) {
      return [];
    }
    return [
      { required: true, message: '请输入邮箱' },
      {
        type: 'email',
        message: '请输入正确邮箱',
      },
      { max: 40, message: '最多输入 40 个字符！' },
    ];
  }, [email, user.email]);

  const spaceIsRequired = React.useMemo(
    () =>
      !!departmentId &&
      (performanceDefaultDepartments?.performanceDefaultDepartments?.data ?? []).includes(
        departmentId
      ) &&
      type === 'STAFF',
    [departmentId, performanceDefaultDepartments?.performanceDefaultDepartments?.data, type]
  );

  React.useEffect(() => {
    syncCommonData({ strategy: { cities: 'IF_NULL' } });
  }, [syncCommonData]);
  return (
    <TinyDrawer
      width={350}
      destroyOnClose
      title={<Typography.Title level={4}>编辑基本信息</Typography.Title>}
      open={editBasicInfoVisible}
      submitButtonLoading={editBasicInfoLoading}
      onClose={onClose(visible, form)}
      onSubmit={getSubmitHandler(form, fetchEditConfirm, user, formRef)}
      onCancel={onClose(visible, form)}
    >
      <Form ref={formRef} form={form} layout="vertical">
        <Form.Item
          label="姓名"
          name="name"
          rules={[
            { max: 10, message: '最多输入 10 个字符！' },
            { required: true, whitespace: true, message: '请输入姓名' },
          ]}
          initialValue={user.name}
        >
          <Input allowClear />
        </Form.Item>
        <Form.Item label="用户 ID">
          <Input value={user.login} disabled />
        </Form.Item>
        <Form.Item
          label="英文名"
          name="nameEn"
          rules={[{ max: 30, message: '最多输入 30 个字符！' }]}
          initialValue={user.nameEn}
        >
          <Input allowClear />
        </Form.Item>
        <Form.Item label="性别" name="gender" initialValue={user.gender}>
          <UserGenderSelect style={{ width: '100%' }} />
        </Form.Item>
        <Form.Item
          label="生日"
          name="birthday"
          initialValue={
            user.birthday && user.birthday !== 'Invalid date' ? moment(user.birthday) : undefined
          }
        >
          <DatePicker style={{ width: '100%' }} allowClear={false} />
        </Form.Item>

        <Form.Item label="类型" name="type" initialValue={user.type}>
          <UserTypeSelect
            style={{ width: '100%' }}
            onChange={value => {
              form.setFieldValue('company', undefined);
              if (value === 'CUSTOMER' || value === 'VENDOR') {
                form.setFieldValue('idc', undefined);
                form.setFieldValue('blockGuid', undefined);
              }
            }}
          />
        </Form.Item>
        <Form.Item
          label="所属机房"
          name="idc"
          initialValue={user.idc}
          dependencies={['departmentId', 'type']}
          rules={[
            {
              required: spaceIsRequired,
              message: '请选择所属机房',
            },
          ]}
        >
          <LocationCascader
            nodeTypes={['IDC']}
            disabled={type === 'CUSTOMER' || type === 'VENDOR' || disabledEditIdc}
            allowClear
            onChange={() => {
              form.setFieldValue('blockGuid', undefined);
            }}
          />
        </Form.Item>
        <Form.Item
          label="主楼栋"
          name="blockGuid"
          initialValue={user.blockGuid}
          // rules={[
          //   {
          //     required: spaceIsRequired,
          //     message: '请选择所属楼栋',
          //   },
          // ]}
        >
          <LocationCascader
            idc={idc !== undefined ? (Array.isArray(idc) ? idc.at(-1) : idc) : null}
            nodeTypes={['BLOCK']}
            disabled={type === 'CUSTOMER' || type === 'VENDOR' || !idc}
            allowClear
          />
        </Form.Item>
        <Form.Item
          label="办公地点"
          name="workplace"
          rules={[{ max: 32, message: '最多输入 32 个字符！' }]}
          initialValue={user.workplace}
        >
          <Input />
        </Form.Item>
        <Form.Item
          label="所属公司"
          name="company"
          rules={[{ max: 16, message: '最多输入 16 个字符！' }]}
          // rules={[
          //   {
          //     validator: (_, value) => {
          //       if (!value && type !== 'STAFF') {
          //         return Promise.reject('请选择所属公司');
          //       }
          //       return Promise.resolve();
          //     },
          //   },
          // ]}
          initialValue={user.company}
        >
          <Input disabled={type === 'STAFF'} />
          {/* {type === 'CUSTOMER' ? (
            <CustomersOnRacksSelect style={{ width: '100%' }} fieldNames={{ value: 'name' }} />
          ) : (
            <VendorSelect disabled={type === 'STAFF'} style={{ width: '100%' }} />
          )} */}
        </Form.Item>
        <Form.Item label="部门" name="departmentId" initialValue={user.departmentId}>
          <DeptTreeSelect
            allowClear={false}
            treeDefaultExpandedKeys={[user.departmentId]}
            showSearch
            filterTreeNode={(input, treeNode) =>
              (treeNode?.title || treeNode?.label || '').toLowerCase().includes(input.toLowerCase())
            }
            disabled={isFromMDM}
          />
        </Form.Item>
        <Form.Item
          label="岗位"
          name="title"
          rules={[
            isYGUser
              ? { required: true, message: '请选择岗位' }
              : { max: 20, message: '最多输入 20 个字符！' },
          ]}
          initialValue={user.title}
        >
          {isYGUser ? <MetatypeSelect metaType="POSITION_YG" /> : <Input />}
        </Form.Item>
        <Form.Item
          label="直线经理1"
          name="supervisorUid"
          initialValue={{ value: user.supervisorUid }}
        >
          <UserSelect style={{ width: '100%' }} />
        </Form.Item>
        <Form.Item
          label="直线经理2"
          name="subSupervisorUid"
          initialValue={{ value: user.subSupervisorUid }}
        >
          <UserSelect style={{ width: '100%' }} />
        </Form.Item>
        <Form.Item
          label="工作职责"
          name="jobDescriptions"
          rules={[{ max: 120, message: '最多输入 120 个字符！' }]}
          initialValue={user.jobDescriptions}
        >
          <Input.TextArea rows={4} />
        </Form.Item>
        <Form.Item
          label="入职日期"
          name="hiredDate"
          initialValue={
            user.hiredDate && user.hiredDate !== 'Invalid date' ? moment(user.hiredDate) : undefined
          }
        >
          <DatePicker style={{ width: '100%' }} disabled allowClear={false} />
        </Form.Item>
        <Form.Item
          label="参加工作日期"
          name="joinWorkingDate"
          initialValue={
            user.joinWorkingDate && user.joinWorkingDate !== 'Invalid date'
              ? moment(user.joinWorkingDate)
              : undefined
          }
        >
          <DatePicker style={{ width: '100%' }} allowClear={false} disabled={isFromMDM} />
        </Form.Item>
        <Form.Item label="工时类型" name="userShifts" initialValue={user.userShifts}>
          <UserShiftsSelect style={{ width: '100%' }} allowClear disabled={isFromMDM} />
        </Form.Item>
        <Form.Item
          label="家乡"
          name="birthPlace"
          initialValue={user.birthPlace ? user.birthPlace.split('/') : undefined}
        >
          <Cascader
            options={metaList}
            placeholder=""
            notFoundContent={<Spin size="small" />}
            fieldNames={{ label: 'metaName', value: 'metaName', children: 'children' }}
            allowClear={false}
          />
        </Form.Item>
        <Form.Item
          label="手机号码"
          name="mobileNumber"
          rules={[
            { required: true, message: '请输入手机号码' },
            {
              pattern: /^1[3456789]\d{9}$/,
              message: '手机号格式错误，请重新输入',
            },
            { max: 11, message: '最多输入 11 个字符！' },
          ]}
          initialValue={user.mobileNumber}
        >
          <Input />
        </Form.Item>
        <Form.Item
          label="邮箱"
          name="email"
          rules={emailRules}
          validateFirst
          initialValue={user.email}
        >
          <UserEmailInput userId={user.id} />
        </Form.Item>
        <Form.Item
          label="备注"
          name="remarks"
          rules={[{ max: 128, message: '最多输入 128 个字符！' }]}
          initialValue={basicInfo.remarks}
        >
          <Input.TextArea rows={4} />
        </Form.Item>
      </Form>
    </TinyDrawer>
  );
}

function EditBasicInfoWrapper(props) {
  const [form] = Form.useForm();
  return <EditBasicInfo form={form} {...props} />;
}

const mapStateToProps = ({
  common: { regionCityTree },
  userManage: { basicInfo, editBasicInfoVisible, editBasicInfoLoading },
}) => {
  const flatData = get(regionCityTree, 'parallelList', []);
  const treeData = generateTreeData(flatData, {
    key: ({ metaType, metaCode }) => `${metaType}${metaCode}`,
    typeKey: 'metaType',
    parentKey: 'parentCode',
    nodeTypes: ['R0', 'R1', 'R2', 'R3'],
    getNode: node => {
      return {
        ...node,
        label: node.metaName,
        value: node.metaCode,
      };
    },
  });

  return {
    metaList: treeData,
    basicInfo,
    editBasicInfoVisible,
    editBasicInfoLoading,
  };
};

const mapDispatchToProps = {
  visible: userManageActions.editBasicInfoVisible,
  fetchEditConfirm,
  syncCommonData: syncCommonDataActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(EditBasicInfoWrapper);
