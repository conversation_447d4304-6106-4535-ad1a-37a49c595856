import React from 'react';

import { fetchOperationLogList } from '@manyun/auth-hub.service.fetch-operation-log-list';
import { OperationLogTable } from '@manyun/auth-hub.ui.operation-log-table';
import { Modal } from '@manyun/base-ui.ui.modal';

export function OperationLogPopup({
  userId,
  operationLogOpen,
  setOperationLogOpen,
}: {
  userId: string;
  operationLogOpen: boolean;
  setOperationLogOpen: (v: boolean) => void;
}) {
  return (
    <Modal
      title="操作日志"
      width="1280px"
      destroyOnClose
      open={operationLogOpen}
      onCancel={() => {
        setOperationLogOpen(false);
      }}
      footer={null}
    >
      <OperationLogTable
        showColumns={['serialNumber', 'targetType', 'modifyType']}
        defaultSearchParams={{
          targetType: 'USER',
          targetId: userId,
        }}
        isTargetIdEqual={(targetId: string) => {
          return targetId === userId;
        }}
      />
    </Modal>
  );
}
