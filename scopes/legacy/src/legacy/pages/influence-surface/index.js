import React from 'react';
// import {} from 'react-redux';
import { useLocation } from 'react-router-dom';

// import { omitBy, pick, isEqual } from 'lodash';
import { InfluenceSurface } from '@manyun/dc-brain.legacy.components';
// import { fetchInfluence, fetchFacilityCount } from '@manyun/dc-brain.legacy.services/influenceService';
import { getLocationSearchMap } from '@manyun/dc-brain.legacy.utils/index';

export default function InfluenceSurfaceEntry() {
  // const history = useHistory();
  const { search } = useLocation();
  const { idcTag, blockTag, targetType, targetId, deviceGuids, defaultTab } = getLocationSearchMap(
    search,
    ['idcTag', 'blockTag', 'targetType', 'targetId', 'deviceGuids', 'defaultTab']
  );

  return (
    <InfluenceSurface
      idcTag={idcTag}
      blockTag={blockTag}
      targetType={targetType}
      targetId={targetId}
      defaultTab={defaultTab}
      deviceGuids={deviceGuids}
    />
  );
}
