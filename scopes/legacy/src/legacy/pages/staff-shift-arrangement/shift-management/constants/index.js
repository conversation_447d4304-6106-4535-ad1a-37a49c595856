export const TIME_HALF_AN_HOUR_INTERVAL = [
  0.5, 1, 1.5, 2, 2.5, 3, 3.5, 4, 4.5, 5, 5.5, 6, 6.5, 7, 7.5, 8, 8.5, 9, 9.5, 10,
];

function getMinutes() {
  let minutes = [];
  for (let i = 1; i <= 59; i++) {
    const tmp = {
      value: i,
      label: `${i}分钟`,
    };
    minutes = [...minutes, tmp];
  }
  return minutes;
}

export const closedVlaue = 'closed';
export const TIME_LATE_GO_ARRIVE = [
  {
    value: closedVlaue,
    label: '关闭',
  },
  {
    value: 0,
    label: '0小时',
    children: getMinutes(),
  },
  {
    value: 60,
    label: '1小时',
    children: getMinutes(),
  },
  {
    value: 120,
    label: '2小时',
    children: getMinutes(),
  },
  {
    value: 180,
    label: '3小时',
    children: getMinutes(),
  },
  {
    value: 240,
    label: '4小时',
    children: getMinutes(),
  },
  {
    value: 300,
    label: '5小时',
    children: getMinutes(),
  },
  {
    value: 360,
    label: '6小时',
    children: getMinutes(),
  },
  {
    value: 420,
    label: '7小时',
    children: getMinutes(),
  },
  {
    value: 480,
    label: '8小时',
    children: getMinutes(),
  },
  {
    value: 540,
    label: '9小时',
    children: getMinutes(),
  },
];
