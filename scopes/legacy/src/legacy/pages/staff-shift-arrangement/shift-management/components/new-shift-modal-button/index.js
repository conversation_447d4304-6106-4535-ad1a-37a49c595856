import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import PlusOutlined from '@ant-design/icons/es/icons/PlusOutlined';
import QuestionCircleOutlined from '@ant-design/icons/es/icons/QuestionCircleOutlined';
import { Select } from '@galiojs/awesome-antd';
import moment from 'moment';
import shortid from 'shortid';

import { Button } from '@manyun/base-ui.ui.button';
import { Cascader } from '@manyun/base-ui.ui.cascader';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { TimePicker } from '@manyun/base-ui.ui.time-picker';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';

import { StatusText, TinyCard } from '@manyun/dc-brain.legacy.components';
import {
  createShiftActionCreator,
  editShiftActionCreator,
  staffShiftActions,
} from '@manyun/dc-brain.legacy.redux/actions/staffShiftActions';

import { TIME_HALF_AN_HOUR_INTERVAL, TIME_LATE_GO_ARRIVE, closedVlaue } from '../../constants';
import { StyledButton } from './styled.js';

class ShiftCreate extends Component {
  onChangeHourSelect = (value, id, key) => {
    const {
      createOptions: { compensateHours },
    } = this.props;
    const tmp = compensateHours.value.map(item => {
      if (item.id === id) {
        return {
          ...item,
          [key]: value,
        };
      }
      return item;
    });
    this.props.changeCompensateHours(tmp);
  };

  newLateGoLateArrive = () => {
    const {
      createOptions: { compensateHours },
    } = this.props;
    const last = compensateHours.value[compensateHours.value.length - 1];
    if (last.lateGo === 10 || last.lateArrive === 10) {
      message.error('已经设置了晚走晚到得最大时间，无需再新增时段');
    } else {
      const novel = {
        id: shortid.generate(),
        lateGo: Number(last.lateGo) + 0.5,
        lateArrive: Number(last.lateArrive) + 0.5,
      };
      const tmp = [...compensateHours.value, novel];
      this.props.changeCompensateHours(tmp);
    }
  };

  deleteLateGoLateArrive = id => {
    const {
      createOptions: { compensateHours },
    } = this.props;
    const tmp = compensateHours.value.filter(item => item.id !== id);
    this.props.changeCompensateHours(tmp);
  };

  onChangeEnableBuffer = value => {
    const {
      createOptions: { enableOffset },
    } = this.props;
    if (!enableOffset.value) {
      const tmp = {
        enableBuffer: {
          value: value,
          dirty: false,
          name: 'enableBuffer',
          touched: true,
        },
      };
      this.props.updateCreateOptions(tmp);
    }
    if (enableOffset.value) {
      const txt = '「允许晚到晚走、早到早走」';
      this.confirm(txt, 'enableBuffer');
    }
  };

  onChangeEnableOffset = value => {
    const {
      createOptions: { enableBuffer },
    } = this.props;
    if (!enableBuffer.value) {
      const tmp = {
        enableOffset: {
          value: value,
          dirty: false,
          name: 'enableOffset',
          touched: true,
        },
      };
      this.props.updateCreateOptions(tmp);
    }
    if (enableBuffer.value) {
      const txt = '「晚到、早走几分钟不记为异常」';
      this.confirm(txt, 'enableOffset');
    }
  };

  confirm = (txt, key) => {
    const {
      createOptions: { enableBuffer, enableOffset },
    } = this.props;
    const text = `开启后，已设置的${txt}将被取消，因为这两种弹性规则，只能选择一种。`;
    Modal.confirm({
      title: '确认开启',
      content: text,
      okText: '开启',
      cancelText: '取消',
      onCancel: () => {
        if (key === 'enableBuffer') {
          const tmp = {
            enableBuffer: {
              value: enableBuffer.value,
              dirty: false,
              name: 'enableBuffer',
              touched: true,
            },
          };
          this.props.updateCreateOptions(tmp);
        }
        if (key === 'enableOffset') {
          const tmp = {
            enableOffset: {
              value: enableOffset.value,
              dirty: false,
              name: 'enableOffset',
              touched: true,
            },
          };
          this.props.updateCreateOptions(tmp);
        }
      },
      onOk: () => {
        if (key === 'enableBuffer') {
          const tmp = {
            enableBuffer: {
              value: true,
              dirty: false,
              name: 'enableBuffer',
              touched: true,
            },
            enableOffset: {
              value: false,
              dirty: false,
              name: 'enableOffset',
              touched: true,
            },
          };
          this.props.updateCreateOptions(tmp);
        }
        if (key === 'enableOffset') {
          const tmp = {
            enableBuffer: {
              value: false,
              dirty: false,
              name: 'enableBuffer',
              touched: true,
            },
            enableOffset: {
              value: true,
              dirty: false,
              name: 'enableOffset',
              touched: true,
            },
          };
          this.props.updateCreateOptions(tmp);
        }
      },
    });
  };

  handleOk = event => {
    event.preventDefault();
    this.props.form.validateFields(async (err, values) => {
      if (err) {
        return;
      }
      const { mode } = this.props;
      if (mode === 'new') {
        this.props.createShift();
      } else {
        this.props.editShift();
      }
    });
  };

  getOffDutyTimeDisabledMinutes = (selectedHour, onDutyTime) => {
    if (!onDutyTime.value) {
      return [];
    }
    const onTime = moment(onDutyTime.value).valueOf();
    const endTime = onTime + 24 * 60 * 60 * 1000;
    const endHour = Number(moment(endTime).format('HH'));
    const onHour = Number(moment(onDutyTime.value).format('HH'));
    const onMinutes = Number(moment(onDutyTime.value).format('mm'));
    if (selectedHour === onHour) {
      return range(0, onMinutes + 1);
    } else if (selectedHour === endHour) {
      return range(onMinutes + 1, 60);
    }
    return [];
  };

  getOnDutyCheckStartTimeDisabledHours = onDutyTime => {
    if (!onDutyTime.value) {
      return [];
    }
    const onTime = moment(onDutyTime.value).valueOf();
    const startTime = onTime - 8 * 60 * 60 * 1000;
    const startHour = moment(startTime).format('HH');
    const onHour = moment(onDutyTime.value).format('HH');
    if (onHour >= 8) {
      const last = range(Number(onHour) + 1, 24);
      const first = range(0, Number(startHour));
      return [...last, ...first];
    } else {
      return range(0, 24).splice(Number(onHour) + 1, 24 - Number(onHour));
    }
  };

  getOnDutyCheckStartTimeDisabledMinutes = (selectedHour, onDutyTime) => {
    if (!onDutyTime.value) {
      return [];
    }
    const onHour = moment(onDutyTime.value).format('HH');
    if (selectedHour === Number(onHour)) {
      const onMinutes = moment(onDutyTime.value).format('mm');
      return range(Number(onMinutes) + 1, 60);
    }
    return [];
  };

  getOnDutyCheckEndTimeDisabledHours = createOptions => {
    const { offIsNextDay, onDutyTime, offDutyTime } = createOptions;

    const onDutyTimeParse = moment(onDutyTime.value).valueOf();
    const onDutyTimeRangeEndTime = onDutyTimeParse + 8 * 60 * 60 * 1000; // 8小时之后的时间
    const onDutyTimeRangeEndTimeHour = Number(moment(onDutyTimeRangeEndTime).format('HH')); //  8小时之后的时间 小时
    const onDutyTimeHour = Number(moment(onDutyTime.value).format('HH')); // 上班时间 小时
    const offDutyTimeHour = Number(moment(offDutyTime.value).format('HH')); // 下班时间 小时
    let disablesHours = [];

    // 判断是否填写上班时间
    if (!onDutyTime.value) {
      disablesHours = [];
    } else {
      // 判断是否填写下班时间
      if (!offDutyTime.value) {
        // 没填下班时间

        // 上班打卡结束时间范围是否跨天
        if (onDutyTimeRangeEndTimeHour < onDutyTimeHour) {
          // 跨天
          const forbidden = range(onDutyTimeRangeEndTimeHour - 1, onDutyTimeHour);
          disablesHours = [...disablesHours, ...forbidden];
        } else {
          const first = range(0, onDutyTimeHour);
          const second = range(onDutyTimeRangeEndTimeHour + 1, 24);
          disablesHours = [...disablesHours, ...first, ...second];
        }
      } else {
        // 填写了下班时间

        // 判断下班时间是否为次日
        if (offIsNextDay.value) {
          // 次日

          // 判断上班打卡结束时间范围跨天
          if (onDutyTimeRangeEndTimeHour < onDutyTimeHour) {
            //跨天
            // 判断上班打卡结束时间是否大于下班时间
            if (onDutyTimeRangeEndTimeHour > offDutyTimeHour) {
              const forbidden = range(offDutyTimeHour + 1, onDutyTimeHour);
              disablesHours = [...disablesHours, ...forbidden];
            } else {
              const forbidden = range(onDutyTimeRangeEndTimeHour + 1, onDutyTimeHour);
              disablesHours = [...disablesHours, ...forbidden];
            }
          } else {
            // 当日
            const first = range(0, onDutyTimeHour);
            const second = range(onDutyTimeRangeEndTimeHour, 24);
            disablesHours = [...disablesHours, ...first, ...second];
          }
        } else {
          // 当日
          // 上班打卡结束时间范围是否跨天
          if (onDutyTimeRangeEndTimeHour < onDutyTimeHour) {
            // 跨天
            // 上班卡结束时间不能大于下班时间
            const first = range(0, onDutyTimeHour);
            const second = range(offDutyTimeHour + 1, 24);
            disablesHours = [...disablesHours, ...first, ...second];
          } else {
            // 判断上班结束时间是否大于下班时间
            if (onDutyTimeRangeEndTimeHour > offDutyTimeHour) {
              const first = range(0, onDutyTimeHour);
              const second = range(offDutyTimeHour + 1, 24);
              disablesHours = [...disablesHours, ...first, ...second];
            } else {
              const first = range(0, onDutyTimeHour);
              const second = range(onDutyTimeRangeEndTimeHour + 1, 24);
              disablesHours = [...disablesHours, ...first, ...second];
            }
          }
        }
      }
    }
    return disablesHours;
  };

  getOnDutyCheckEndTimeDisabledMinutes = (selectedHour, createOptions) => {
    const { onDutyTime, offDutyTime, offIsNextDay, offDutyCheckStartTime, isOffDutyCheck } =
      createOptions;

    const onDutyTimeParse = moment(onDutyTime.value).valueOf();
    const onDutyendRangeEndTimeParse = onDutyTimeParse + 8 * 60 * 60 * 1000;
    const onDutyendRangeEndTimeHour = Number(moment(onDutyendRangeEndTimeParse).format('HH'));
    const onDutyendRangeEndTimeMinutes = Number(moment(onDutyendRangeEndTimeParse).format('mm'));
    const onDutyTimeHour = Number(moment(onDutyTime.value).format('HH'));
    const onDutyTimeMinutes = Number(moment(onDutyTime.value).format('mm'));
    const offDutyTimeHour = Number(moment(offDutyTime.value).format('HH'));
    const offDutyTimeMinutes = Number(moment(offDutyTime.value).format('mm'));
    let onDutyStartdisabledMinutes = [];

    // 下班不发起打卡 或者 下班时间， 发起打卡时间开始时间未填
    if (!isOffDutyCheck.value || !offDutyCheckStartTime.value || !offDutyTime.value) {
      // 如果选择的小时与上班卡一样的小时
      if (selectedHour === onDutyTimeHour) {
        const forbidden = range(0, onDutyTimeMinutes);
        onDutyStartdisabledMinutes = [...onDutyStartdisabledMinutes, ...forbidden];
      }
      if (selectedHour === onDutyendRangeEndTimeHour) {
        const forbidden = range(onDutyendRangeEndTimeMinutes + 1, 60);
        onDutyStartdisabledMinutes = [...onDutyStartdisabledMinutes, ...forbidden];
      }
      if (selectedHour === offDutyTimeHour) {
        const forbidden = range(offDutyTimeMinutes + 1, 60);
        onDutyStartdisabledMinutes = [...onDutyStartdisabledMinutes, ...forbidden];
      }
    } else {
      // 有下班卡开始时间
      // 判断下班卡是否为次日
      if (offIsNextDay.value) {
        // 次日
        // 如果选择的时间等于上班小时
        if (selectedHour === offDutyTimeHour) {
          const forbidden = range(offDutyTimeMinutes, 60);
          onDutyStartdisabledMinutes = [...onDutyStartdisabledMinutes, ...forbidden];
        }
        if (selectedHour === onDutyTimeHour) {
          const forbidden = range(0, onDutyTimeMinutes);
          onDutyStartdisabledMinutes = [...onDutyStartdisabledMinutes, ...forbidden];
        }

        if (selectedHour === onDutyendRangeEndTimeHour) {
          const forbidden = range(onDutyendRangeEndTimeMinutes, 60);
          onDutyStartdisabledMinutes = [...onDutyStartdisabledMinutes, ...forbidden];
        }
      } else {
        // 今日
        // 如果选择的时间等于下班时间的小时,等于上班小时
        if (selectedHour === offDutyTimeHour && selectedHour === onDutyTimeHour) {
          const first = range(0, onDutyTimeHour);
          const second = range(offDutyTimeMinutes, 60);
          onDutyStartdisabledMinutes = [...onDutyStartdisabledMinutes, ...first, ...second];
        } else {
          if (selectedHour === onDutyTimeHour) {
            const forbidden = range(0, onDutyTimeMinutes);
            onDutyStartdisabledMinutes = [...onDutyStartdisabledMinutes, ...forbidden];
          }
          if (selectedHour === offDutyTimeHour) {
            const forbidden = range(offDutyTimeMinutes, 60);
            onDutyStartdisabledMinutes = [...onDutyStartdisabledMinutes, ...forbidden];
          }
          if (selectedHour === onDutyendRangeEndTimeHour) {
            const forbidden = range(onDutyendRangeEndTimeMinutes + 1, 60);
            onDutyStartdisabledMinutes = [...onDutyStartdisabledMinutes, ...forbidden];
          }
        }
      }
    }
    return onDutyStartdisabledMinutes;
  };

  getOffDutyCheckStartTimeDisabledHours = createOptions => {
    const {
      onDutyTime,
      offDutyTime,
      onDutyCheckEndTime,
      offIsNextDay,
      onDutyCheckRange,
      isOnDutyCheck,
    } = createOptions;
    const offDutyTimeParse = moment(offDutyTime.value).valueOf(); // 下班时间戳
    const OffDutyCheckStartTimeRangeStart = offDutyTimeParse - 8 * 60 * 60 * 1000; // 下班前8小时的时间
    const OffDutyCheckStartTimeRangeStartHour = Number(
      moment(OffDutyCheckStartTimeRangeStart).format('HH')
    ); // 上班前8小时的时间的小时
    const offDutyTimeHour = Number(moment(offDutyTime.value).format('HH'));
    const onDutyTimeHour = Number(moment(onDutyTime.value).format('HH'));
    const onDutyCheckEndTimeHour = Number(moment(onDutyCheckEndTime.value).format('HH')); // 上班打卡结束时间的小时
    let disabledHour = [];
    // 上班不发起打卡或者未填写上班打卡结束时间
    if (!isOnDutyCheck.value || !onDutyCheckEndTime.value || !onDutyTime.value) {
      // 下班时间为当天
      if (!offIsNextDay.value) {
        if (onDutyTime.value && OffDutyCheckStartTimeRangeStartHour < onDutyTimeHour) {
          const first = range(0, onDutyTimeHour);
          const second = range(offDutyTimeHour + 1, 24);
          disabledHour = [...disabledHour, ...first, ...second];
        } else {
          if (
            offDutyTime.value &&
            OffDutyCheckStartTimeRangeStart < offDutyTime.value.clone().startOf('day').valueOf()
          ) {
            const first = range(offDutyTimeHour + 1, OffDutyCheckStartTimeRangeStartHour);
            disabledHour = [...disabledHour, ...first];
          } else {
            const first = range(0, OffDutyCheckStartTimeRangeStartHour);
            const second = range(offDutyTimeHour + 1, 24);
            disabledHour = [...disabledHour, ...first, ...second];
          }
        }
      } else {
        //下班时间为次日

        // 开始时间范围跨天是否
        if (OffDutyCheckStartTimeRangeStartHour > offDutyTimeHour) {
          const forbidden = range(offDutyTimeHour + 1, OffDutyCheckStartTimeRangeStartHour + 1);
          disabledHour = [...disabledHour, ...forbidden];
        } else {
          const first = range(0, OffDutyCheckStartTimeRangeStartHour + 1);
          const second = range(offDutyTimeHour + 1, 24);
          disabledHour = [...disabledHour, ...first, ...second];
        }
      }
    } else {
      //  上班发起打卡

      // 如果上班打卡结束时间为当天，下班时间也为当天
      if (!onDutyCheckRange.endIsNextDay && !offIsNextDay.value) {
        // 如果上班打卡结束时间大于下班打卡开始时间，0-上班打卡结束时间 禁用
        if (OffDutyCheckStartTimeRangeStartHour <= onDutyCheckEndTimeHour) {
          const first = range(0, onDutyCheckEndTimeHour);
          const second = range(offDutyTimeHour + 1, 24);
          disabledHour = [...disabledHour, ...first, ...second];
        } else {
          const first = range(0, onDutyCheckEndTimeHour);
          const second = range(offDutyTimeHour + 1, 24);
          disabledHour = [...disabledHour, ...first, ...second];
        }
      }

      // 如果上班打卡结束时间为当天，下班时间为次日(下班打卡结束时间一定为次日)
      if (!onDutyCheckRange.endIsNextDay && offIsNextDay.value) {
        // 下班时间(6:00) 开始时间0-6:00,22:00-24:00,如果上班打卡结束时间(23:00)大于下班打卡开始时间(22:00)，
        if (OffDutyCheckStartTimeRangeStartHour <= onDutyCheckEndTimeHour) {
          const first = range(offDutyTimeHour + 1, onDutyCheckEndTimeHour);
          disabledHour = first;
        } else {
          // 上班时间(6:00) 开始时间0-6:00,22:00-24:00,如果上班打卡结束时间(21:00)大于下班打卡开始时间(22:00)，
          // 判断上班卡开始时间是否跨天
          if (offDutyTimeHour <= 8) {
            // 跨天
            const first = range(offDutyTimeHour + 1, OffDutyCheckStartTimeRangeStartHour);
            disabledHour = first;
          } else {
            const first = range(0, OffDutyCheckStartTimeRangeStartHour - 1);
            const second = range(offDutyTimeHour + 1, 24);
            const third = range(offDutyTimeHour + 1, onDutyCheckEndTimeHour);
            disabledHour = [...first, ...second, ...third];
          }
        }
      }

      // 如果上班打卡结束时间为次日，下班时间为当天
      if (onDutyCheckRange.endIsNextDay && !offIsNextDay.value) {
        const first = range(0, OffDutyCheckStartTimeRangeStartHour);
        const second = range(offDutyTimeHour + 1, 24);
        disabledHour = [...disabledHour, ...first, ...second];
      }

      // 如果上班打卡结束时间为次日，下班时间为次日
      if (onDutyCheckRange.endIsNextDay && offIsNextDay.value) {
        //  开始时间范围跨天是否
        if (OffDutyCheckStartTimeRangeStartHour > offDutyTimeHour) {
          // 跨天，
          if (OffDutyCheckStartTimeRangeStartHour > onDutyCheckEndTimeHour) {
            const first = range(0, onDutyCheckEndTimeHour);
            const second = range(offDutyTimeHour + 1, 24);
            disabledHour = [...disabledHour, ...first, ...second];
          } else {
            const first = range(0, onDutyCheckEndTimeHour);
            const second = range(offDutyTimeHour + 1, 24);
            disabledHour = [...disabledHour, ...first, ...second];
          }
        } else {
          //上班卡结束时间是否大于 下班开始时间
          if (onDutyCheckEndTimeHour > OffDutyCheckStartTimeRangeStartHour) {
            const first = range(0, onDutyCheckEndTimeHour);
            const second = range(offDutyTimeHour + 1, 24);
            disabledHour = [...disabledHour, ...first, ...second];
          } else {
            const first = range(0, OffDutyCheckStartTimeRangeStartHour);
            const second = range(offDutyTimeHour + 1, 24);
            disabledHour = [...disabledHour, ...first, ...second];
          }
        }
      }
    }
    return disabledHour;
  };

  getOffDutyCheckStartTimeDisabledMinutes = (selectedHour, createOptions) => {
    const { onDutyTime, offDutyTime, onDutyCheckEndTime, isOnDutyCheck } = createOptions;

    const offDutyTimeMinutes = Number(moment(offDutyTime.value).format('mm')); // 下班时间的分钟
    const offDutyTimeHour = Number(moment(offDutyTime.value).format('HH')); // 下班时间的小时
    const onDutyTimeHour = Number(moment(onDutyTime.value).format('HH')); // 上班时间的小时
    const onDutyTimeMinutes = Number(moment(onDutyTime.value).format('mm')); // 上班时间的分钟
    const onDutyCheckEndTimeMinutes = Number(moment(onDutyCheckEndTime.value).format('mm')); // 上班打卡结束时间的分钟
    const onDutyCheckEndTimeHour = Number(moment(onDutyCheckEndTime.value).format('HH')); // 上班打卡结束时间的小时
    const OffDutyCheckStartTimeRangeStart =
      moment(offDutyTime.value).valueOf() - 8 * 60 * 60 * 1000; // 上班前8小时的时间
    const OffDutyCheckStartTimeRangeStartHour = Number(
      moment(OffDutyCheckStartTimeRangeStart).format('HH')
    ); // 上班前8小时的时间的小时
    let disabledMinutes = [];

    // 上班不发起打卡 或者 发起打卡时间结束未填 或者上班时间未填
    if (!isOnDutyCheck.value || !onDutyCheckEndTime.value || !onDutyTime.value) {
      if (selectedHour === offDutyTimeHour && selectedHour === onDutyTimeHour) {
        const offDisabledMinutes = range(offDutyTimeMinutes + 1, 60);
        disabledMinutes = [...disabledMinutes, ...offDisabledMinutes];
      } else {
        // 如果选择的小时跟下班时间一样的小时，分钟要小于下班时间的分钟
        if (selectedHour === offDutyTimeHour) {
          const offDisabledMinutes = range(offDutyTimeMinutes + 1, 60);
          disabledMinutes = [...disabledMinutes, ...offDisabledMinutes];
        }
        // 如果选择的小时跟上班时间一样的小时，分钟要大于上班时间的分钟
        if (selectedHour === onDutyTimeHour) {
          const offDisabledMinutes = range(0, onDutyTimeMinutes + 1);
          disabledMinutes = [...disabledMinutes, ...offDisabledMinutes];
        }
        // 如果选择了打卡时间开始的的小时
        if (selectedHour === OffDutyCheckStartTimeRangeStartHour) {
          const offDisabledMinutes = range(0, offDutyTimeMinutes);
          disabledMinutes = [...disabledMinutes, ...offDisabledMinutes];
        }
      }
    } else {
      //上班发起打卡
      // 如果选择的小时 与下班时间的小时，上班打卡结束时间小时一样
      if (selectedHour === offDutyTimeHour && selectedHour === onDutyCheckEndTimeHour) {
        //判断下班时间的分钟，上班打卡结束时间分钟大小
        if (onDutyCheckEndTimeMinutes > offDutyTimeMinutes) {
          const first = range(0, offDutyTimeMinutes + 1);
          const second = range(onDutyCheckEndTimeMinutes + 1, 60);
          disabledMinutes = [...disabledMinutes, ...first, ...second];
        } else {
          const first = range(0, onDutyCheckEndTimeMinutes);
          const second = range(offDutyTimeMinutes + 1, 60);
          disabledMinutes = [...disabledMinutes, ...first, ...second];
        }
      } else {
        if (selectedHour === offDutyTimeHour) {
          // // 如果选择的小时跟下班时间一样，分钟要小于下班时间的分钟
          const offDisabledMinutes = range(offDutyTimeMinutes + 1, 60);
          disabledMinutes = [...disabledMinutes, ...offDisabledMinutes];
        }
        if (selectedHour === onDutyCheckEndTimeHour) {
          //  // 如果选择的小时跟上班打卡结束时间一样的小时，分钟要大于上班打卡结束时间的分钟
          const offDisabled = range(0, onDutyCheckEndTimeMinutes);
          disabledMinutes = [...disabledMinutes, ...offDisabled];
        }
      }
    }

    return disabledMinutes;
  };

  getOffDutyCheckEndTimeDisabledHours = createOptions => {
    const { offDutyTime, onDutyTime } = createOptions;
    const offDutyTimeParse = moment(offDutyTime.value).valueOf();
    const onDutyTimeParse = moment(onDutyTime.value).valueOf();
    const offDutyCheckEndTimeRangeEndParse = offDutyTimeParse + 8 * 60 * 60 * 1000;
    const offDutyTimeLimit = onDutyTimeParse + 24 * 60 * 60 * 1000; // 最大下班时间
    const offDutyTimeLimitHour = Number(moment(offDutyTimeLimit).format('HH'));
    const offDutyCheckEndTimeRangeEndHour = Number(
      moment(offDutyCheckEndTimeRangeEndParse).format('HH')
    );
    const offDutyTimeHour = Number(moment(offDutyTime.value).format('HH'));
    let disabledHours = [];
    // 下班打卡结束时间范围是否跨天
    if (offDutyCheckEndTimeRangeEndHour < offDutyTimeHour) {
      // 跨天，此时最大结束世家为上班时间后的24小时的时间
      const first = range(0, offDutyTimeLimitHour);
      const second = range(0, offDutyTimeHour);
      disabledHours = [...disabledHours, ...first, ...second];
    } else {
      const first = range(0, offDutyTimeHour);
      const second = range(offDutyCheckEndTimeRangeEndHour + 1, 24);
      disabledHours = [...disabledHours, ...first, ...second];
    }
    return disabledHours;
  };

  getOffDutyCheckEndTimeDisabledMinutes = (selectedHour, createOptions) => {
    const { offDutyTime, onDutyTime } = createOptions;
    const offDutyTimeParse = moment(offDutyTime.value).valueOf(); // 上班时间戳
    const OffDutyCheckStartTimeRangeEndParse = offDutyTimeParse + 8 * 60 * 60 * 1000; // 上班后8小时的时间
    const OffDutyCheckStartTimeRangeEndHour = Number(
      moment(OffDutyCheckStartTimeRangeEndParse).format('HH')
    ); // 上班前8小时的时间的小时
    const OffDutyCheckStartTimeRangeEndMinutes = Number(
      moment(OffDutyCheckStartTimeRangeEndParse).format('mm')
    ); // 上班前8小时的时间分钟

    const onDutyTimeParse = moment(onDutyTime.value).valueOf();
    const offDutyTimeLimit = onDutyTimeParse + 24 * 60 * 60 * 1000; // 最大下班时间
    const offDutyTimeLimitHour = Number(moment(offDutyTimeLimit).format('HH'));
    const onDutyTimeMinutes = Number(moment(onDutyTimeParse).format('mm')); // 上班时间的分钟

    const offDutyTimeHour = Number(moment(offDutyTime.value).format('HH'));
    const offDutyTimeMinutes = Number(moment(offDutyTime.value).format('mm'));
    let disabledMinutes = [];
    if (selectedHour === offDutyTimeHour && selectedHour === OffDutyCheckStartTimeRangeEndHour) {
      if (OffDutyCheckStartTimeRangeEndMinutes < offDutyTimeMinutes) {
        const first = range(0, offDutyTimeMinutes);
        disabledMinutes = [...disabledMinutes, ...first];
      } else {
        const first = range(0, OffDutyCheckStartTimeRangeEndMinutes);
        disabledMinutes = [...disabledMinutes, ...first];
      }
    }
    if (selectedHour === offDutyTimeHour) {
      const first = range(0, offDutyTimeMinutes);
      disabledMinutes = [...disabledMinutes, ...first];
    }
    if (selectedHour === OffDutyCheckStartTimeRangeEndHour) {
      const first = range(OffDutyCheckStartTimeRangeEndMinutes + 1, 60);
      disabledMinutes = [...disabledMinutes, ...first];
    }
    if (selectedHour === offDutyTimeLimitHour) {
      const first = range(onDutyTimeMinutes + 1, 60);
      disabledMinutes = [...disabledMinutes, ...first];
    }
    return disabledMinutes;
  };

  render() {
    const {
      mode,
      form: { getFieldDecorator },
      createOptions: {
        isOnDutyCheck,
        isOffDutyCheck,
        allowRest,
        enableCompensate,
        compensateHours,
        enableBuffer,
        enableOffset,
        allowContinuousWork,
        onDutyTime,
        offIsNextDay,
        onDutyCheckRange,
        offDutyCheckRange,
        restRange,
        onDutyOffsetMinutes,
        offDutyOffsetMinutes,
        onDutyBufferMinutes,
        offDutyBufferMinutes,
      },
      createShiftVisiable,
    } = this.props;
    const text = mode === 'new' ? '新建班次' : '编辑班次';

    let TIME_LATE_GO_AND_ARRIVE = TIME_LATE_GO_ARRIVE;
    let TIME_EARLY_GO_AND_ARRIVE = TIME_LATE_GO_ARRIVE;
    let LATE_ARRIVAL_ALLOWED = TIME_LATE_GO_ARRIVE;
    let EARLY_DEPARTURE_ALLOWED = TIME_LATE_GO_ARRIVE;

    if (enableOffset.value) {
      // 开启了允许晚到晚走/早到早走， 如果晚到晚走选择了关闭，则早到早走不可选择关闭
      if (
        onDutyOffsetMinutes.value &&
        Array.isArray(onDutyOffsetMinutes.value) &&
        onDutyOffsetMinutes.value[0] === closedVlaue
      ) {
        TIME_EARLY_GO_AND_ARRIVE = TIME_LATE_GO_ARRIVE.map(item => {
          if (item.value === closedVlaue) {
            return {
              ...item,
              disabled: true,
            };
          }
          return item;
        });
      }

      // 开启了允许晚到晚走/早到早走， 如果早到早走选择了关闭，则晚到晚走不可选择关闭
      if (
        offDutyOffsetMinutes.value &&
        Array.isArray(offDutyOffsetMinutes.value) &&
        offDutyOffsetMinutes.value[0] === closedVlaue
      ) {
        TIME_LATE_GO_AND_ARRIVE = TIME_LATE_GO_ARRIVE.map(item => {
          if (item.value === closedVlaue) {
            return {
              ...item,
              disabled: true,
            };
          }
          return item;
        });
      }
    }

    if (enableBuffer.value) {
      // 开启了允许晚到/早走不记为异常， 如果 上班最多晚到 选择了关闭，则 下班最多早走 不可选择关闭
      if (
        onDutyBufferMinutes.value &&
        Array.isArray(onDutyBufferMinutes.value) &&
        onDutyBufferMinutes.value[0] === closedVlaue
      ) {
        EARLY_DEPARTURE_ALLOWED = TIME_LATE_GO_ARRIVE.map(item => {
          if (item.value === closedVlaue) {
            return {
              ...item,
              disabled: true,
            };
          }
          return item;
        });
      }

      // 如果 下班最多早走 选择了关闭，则 上班最多晚到 不可选择关闭
      if (
        offDutyBufferMinutes.value &&
        Array.isArray(offDutyBufferMinutes.value) &&
        offDutyBufferMinutes.value[0] === closedVlaue
      ) {
        LATE_ARRIVAL_ALLOWED = TIME_LATE_GO_ARRIVE.map(item => {
          if (item.value === closedVlaue) {
            return {
              ...item,
              disabled: true,
            };
          }
          return item;
        });
      }
    }

    return (
      <Modal
        destroyOnClose={true}
        width={960}
        bodyStyle={{
          height: 452,
          overflowY: 'scroll',
        }}
        title={text}
        visible={createShiftVisiable}
        onOk={this.handleOk}
        onCancel={() => this.props.updateCreateShiftVisiable(false)}
      >
        <Form colon={false} layout="inline">
          <Row>
            <Col xl={24}>
              <Form.Item label="班次名称" style={{ marginBottom: '0' }}>
                {getFieldDecorator('dutyName', {
                  rules: [
                    { required: true, message: '班次名称必填' },
                    {
                      type: 'string',
                      max: 20,
                      message: '最多输入 20 个字符！',
                    },
                  ],
                })(<Input style={{ width: 340 }} allowClear />)}
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <TinyCard
              bordered={false}
              title="班次设置"
              headStyle={{ border: 0, padding: 0 }}
              bodyStyle={{ padding: 0 }}
            >
              <Col xl={24}>
                <Form.Item label="上班" style={{ marginBottom: '0' }}>
                  {getFieldDecorator('onDutyTime', {
                    rules: [{ required: true, message: '上班时间必选' }],
                  })(
                    <TimePicker
                      showNow={false}
                      format="HH:mm"
                      inputReadOnly
                      allowClear={false}
                      style={{ width: 170 }}
                      getPopupContainer={trigger => trigger.parentNode.parentNode}
                    />
                  )}
                </Form.Item>

                <Form.Item style={{ marginBottom: '0' }}>
                  {getFieldDecorator('isOnDutyCheck')(
                    <Checkbox checked={isOnDutyCheck.value}>发起打卡</Checkbox>
                  )}
                </Form.Item>

                {isOnDutyCheck.value && (
                  <>
                    <Form.Item
                      label="打卡时间范围"
                      style={{ marginBottom: '0', marginRight: '11px' }}
                    >
                      {getFieldDecorator('onDutyCheckStartTime', {
                        rules: [{ required: true, message: '上班打卡开始时间必选' }],
                      })(
                        <TimePicker
                          showNow={false}
                          format="HH:mm"
                          style={{ width: 170 }}
                          inputReadOnly
                          allowClear={false}
                          disabledHours={() =>
                            this.getOnDutyCheckStartTimeDisabledHours(onDutyTime)
                          }
                          disabledMinutes={selectedHour =>
                            this.getOnDutyCheckStartTimeDisabledMinutes(selectedHour, onDutyTime)
                          }
                          getPopupContainer={() => document.querySelector('.manyun-modal-body')}
                        />
                      )}
                    </Form.Item>
                    <Form.Item label="至" style={{ marginBottom: '0' }}>
                      {getFieldDecorator('onDutyCheckEndTime', {
                        rules: [{ required: true, message: '上班打卡结束时间必选' }],
                      })(
                        <TimePicker
                          showNow={false}
                          format="HH:mm"
                          style={{ width: 170 }}
                          allowClear={false}
                          inputReadOnly
                          disabledHours={() =>
                            this.getOnDutyCheckEndTimeDisabledHours(this.props.createOptions)
                          }
                          disabledMinutes={selectedHour =>
                            this.getOnDutyCheckEndTimeDisabledMinutes(
                              selectedHour,
                              this.props.createOptions
                            )
                          }
                          getPopupContainer={() => document.querySelector('.manyun-modal-body')}
                        />
                      )}
                    </Form.Item>
                    {onDutyCheckRange.endIsNextDay && (
                      <Form.Item>
                        <StatusText status="alarm" background style={{ margin: '10px 0' }}>
                          次日
                        </StatusText>
                      </Form.Item>
                    )}
                    <Form.Item label="可打卡"></Form.Item>
                  </>
                )}
              </Col>
              <Col xl={24}>
                <Form.Item label="下班" style={{ marginBottom: '0' }}>
                  {getFieldDecorator('offDutyTime', {
                    rules: [{ required: true, message: '下班时间必选' }],
                  })(
                    <TimePicker
                      showNow={false}
                      format="HH:mm"
                      inputReadOnly
                      allowClear={false}
                      style={{ width: 170 }}
                      getPopupContainer={trigger => trigger.parentNode.parentNode}
                      disabledTime={() => ({
                        disabledMinutes: selectedHour =>
                          this.getOffDutyTimeDisabledMinutes(selectedHour, onDutyTime),
                      })}
                    />
                  )}
                </Form.Item>

                {offIsNextDay.value && (
                  <Form.Item>
                    <StatusText status="alarm" background style={{ margin: '10px 0' }}>
                      次日
                    </StatusText>
                  </Form.Item>
                )}
                <Form.Item style={{ marginBottom: '0' }}>
                  {getFieldDecorator('isOffDutyCheck')(
                    <Checkbox checked={isOffDutyCheck.value}>发起打卡</Checkbox>
                  )}
                </Form.Item>

                {isOffDutyCheck.value && (
                  <>
                    <Form.Item
                      label="打卡时间范围"
                      style={{ marginBottom: '0', marginRight: '11px' }}
                    >
                      {getFieldDecorator('offDutyCheckStartTime', {
                        rules: [{ required: true, message: '下班打卡开始时间必选' }],
                      })(
                        <TimePicker
                          showNow={false}
                          format="HH:mm"
                          style={{ width: 170 }}
                          allowClear={false}
                          inputReadOnly
                          disabledHours={() =>
                            this.getOffDutyCheckStartTimeDisabledHours(this.props.createOptions)
                          }
                          disabledMinutes={selectedHour =>
                            this.getOffDutyCheckStartTimeDisabledMinutes(
                              selectedHour,
                              this.props.createOptions
                            )
                          }
                          getPopupContainer={() => document.querySelector('.manyun-modal-body')}
                        />
                      )}
                    </Form.Item>
                    {offDutyCheckRange.startIsNextDay && (
                      <Form.Item>
                        <StatusText status="alarm" background style={{ margin: '10px 0' }}>
                          次日
                        </StatusText>
                      </Form.Item>
                    )}

                    <Form.Item label="至" style={{ marginBottom: '0' }}>
                      {getFieldDecorator('offDutyCheckEndTime', {
                        rules: [{ required: true, message: '下班打卡结束时间必选' }],
                      })(
                        <TimePicker
                          showNow={false}
                          format="HH:mm"
                          style={{ width: 170 }}
                          inputReadOnly
                          allowClear={false}
                          disabledHours={() =>
                            this.getOffDutyCheckEndTimeDisabledHours(this.props.createOptions)
                          }
                          disabledMinutes={selectedHour =>
                            this.getOffDutyCheckEndTimeDisabledMinutes(
                              selectedHour,
                              this.props.createOptions
                            )
                          }
                          getPopupContainer={() => document.querySelector('.manyun-modal-body')}
                        />
                      )}
                    </Form.Item>
                    {offDutyCheckRange.endIsNextDay && (
                      <Form.Item>
                        <StatusText status="alarm" background style={{ margin: '10px 0' }}>
                          次日
                        </StatusText>
                      </Form.Item>
                    )}
                    <Form.Item label="可打卡"></Form.Item>
                  </>
                )}
              </Col>
              <Col xl={24}>
                <Form.Item style={{ marginBottom: '0' }}>
                  {getFieldDecorator('allowRest')(
                    <Checkbox checked={allowRest.value}>包含休息时长</Checkbox>
                  )}
                </Form.Item>
                <>
                  {allowRest.value && (
                    <Form.Item label="休息时长" style={{ marginBottom: '0' }}>
                      {getFieldDecorator('restMinutes', {
                        rules: [
                          {
                            required: true,
                            validator: (_rule, restMinutes, callback) => {
                              const rexp = /^\d+$/;
                              if (restMinutes === '' || restMinutes === null) {
                                callback('请输入休息时长');
                              }

                              if (
                                restMinutes <= 0 ||
                                !rexp.test(restMinutes) ||
                                restMinutes === '0'
                              ) {
                                callback('请输入大于0的整数');
                              } else {
                                callback();
                              }
                            },
                          },
                        ],
                      })(
                        <InputNumber
                          style={{ width: 90 }}
                          formatter={value => `${value}分钟`}
                          parser={value => {
                            let number = value;
                            Array.from('分钟').forEach(char => {
                              number = number.replace(char, '');
                            });
                            return number;
                          }}
                          min={0}
                          max={1440}
                        />
                      )}
                    </Form.Item>
                  )}
                  {restRange.endIsNextDay && (
                    <Form.Item>
                      <StatusText status="alarm" background style={{ margin: '10px 0' }}>
                        次日
                      </StatusText>
                    </Form.Item>
                  )}
                </>
              </Col>

              <Col xl={24}>
                <Form.Item style={{ marginBottom: '0', marginRight: '0' }}>
                  {getFieldDecorator('allowContinuousWork')(
                    <Checkbox checked={allowContinuousWork.value}>不允许连续上班</Checkbox>
                  )}
                </Form.Item>
                <Form.Item style={{ marginBottom: '0' }}>
                  <Tooltip title="针对换班和请假顶班生效">
                    <QuestionCircleOutlined />
                  </Tooltip>
                </Form.Item>

                {allowContinuousWork.value && (
                  <Form.Item label="强制休息时长：" style={{ marginBottom: '0' }}>
                    {getFieldDecorator('notAllowContinuousTime', {
                      rules: [{ required: true, message: '强制休息时长必填' }],
                    })(
                      <InputNumber
                        formatter={value => `${value}小时`}
                        parser={value => {
                          let number = value;
                          Array.from('小时').forEach(char => {
                            number = number.replace(char, '');
                          });
                          return number;
                        }}
                        style={{ width: 90 }}
                        min={1}
                        max={24}
                        precision={1}
                      />
                    )}
                  </Form.Item>
                )}
              </Col>
            </TinyCard>
          </Row>
          <Row>
            <TinyCard
              bordered={false}
              title="弹性设置"
              headStyle={{ border: 0, padding: 0 }}
              bodyStyle={{ padding: 0 }}
            >
              <Col xl={24}>
                <Form.Item style={{ marginBottom: '0' }}>
                  <Checkbox
                    checked={enableOffset.value}
                    onChange={e => this.onChangeEnableOffset(e.target.checked)}
                  >
                    允许晚到晚走/早到早走
                  </Checkbox>
                </Form.Item>
                {enableOffset.value && (
                  <>
                    <Form.Item
                      label="上班最多晚到"
                      style={{ marginBottom: '0', marginRight: '10px' }}
                    >
                      {getFieldDecorator('onDutyOffsetMinutes', {
                        rules: [{ required: true, message: '上班最多晚时间必选' }],
                      })(
                        <Cascader
                          options={TIME_LATE_GO_AND_ARRIVE}
                          allowClear={false}
                          changeOnSelect
                          style={{ width: 200 }}
                          getPopupContainer={() => document.querySelector('.manyun-modal-body')}
                        />
                      )}
                    </Form.Item>
                    <Form.Item style={{ marginBottom: '0' }}>
                      <Tooltip title="上班晚到几分钟，下班须晚走几分钟">
                        <QuestionCircleOutlined />
                      </Tooltip>
                    </Form.Item>

                    <Form.Item
                      label="下班最多早走"
                      style={{ marginBottom: '0', marginRight: '10px' }}
                    >
                      {getFieldDecorator('offDutyOffsetMinutes', {
                        rules: [{ required: true, message: '下班最多早走时间必选' }],
                      })(
                        <Cascader
                          options={TIME_EARLY_GO_AND_ARRIVE}
                          allowClear={false}
                          changeOnSelect
                          style={{ width: 200 }}
                          getPopupContainer={() => document.querySelector('.manyun-modal-body')}
                        />
                      )}
                    </Form.Item>
                    <Form.Item style={{ marginBottom: '0' }}>
                      <Tooltip title="上班早到几分钟，下班可早走几分钟">
                        <QuestionCircleOutlined />
                      </Tooltip>
                    </Form.Item>
                  </>
                )}
              </Col>
              <Col xl={24}>
                <Form.Item style={{ marginBottom: '0' }}>
                  <Checkbox
                    checked={enableBuffer.value}
                    onChange={e => this.onChangeEnableBuffer(e.target.checked)}
                  >
                    允许晚到/早走不记为异常
                  </Checkbox>
                </Form.Item>
                {enableBuffer.value && (
                  <>
                    <Form.Item
                      label="上班最多晚到"
                      style={{ marginBottom: '0', marginRight: '10px' }}
                    >
                      {getFieldDecorator('onDutyBufferMinutes', {
                        rules: [{ required: true, message: '上班最多晚到时间必选' }],
                      })(
                        <Cascader
                          options={LATE_ARRIVAL_ALLOWED}
                          allowClear={false}
                          changeOnSelect
                          style={{ width: 200 }}
                          getPopupContainer={() => document.querySelector('.manyun-modal-body')}
                        />
                      )}
                    </Form.Item>
                    <Form.Item style={{ marginBottom: '0' }}>
                      <Tooltip title="设置的时间内晚到不算迟到">
                        <QuestionCircleOutlined />
                      </Tooltip>
                    </Form.Item>
                    <Form.Item
                      label="下班最多早走"
                      style={{ marginBottom: '0', marginRight: '10px' }}
                    >
                      {getFieldDecorator('offDutyBufferMinutes', {
                        rules: [{ required: true, message: '下班最多早走时间必选' }],
                      })(
                        <Cascader
                          options={EARLY_DEPARTURE_ALLOWED}
                          allowClear={false}
                          changeOnSelect
                          style={{ width: 200 }}
                          getPopupContainer={() => document.querySelector('.manyun-modal-body')}
                        />
                      )}
                    </Form.Item>
                    <Form.Item style={{ marginBottom: '0' }}>
                      <Tooltip title="设置的时间内早走不算早退">
                        <QuestionCircleOutlined />
                      </Tooltip>
                    </Form.Item>
                  </>
                )}
              </Col>
              <Col xl={24}>
                <Form.Item style={{ marginBottom: '0' }}>
                  {getFieldDecorator('enableCompensate')(
                    <Checkbox checked={enableCompensate.value}>下班晚走，第二天可晚到</Checkbox>
                  )}
                </Form.Item>
              </Col>
              {enableCompensate.value &&
                compensateHours.value.map(({ id, lateGo, lateArrive }, index) => {
                  return (
                    <Col xl={24} key={id}>
                      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                      <Form.Item style={{ marginBottom: '0' }}>规则{index + 1}</Form.Item>
                      <Form.Item label="当日下班后晚走" style={{ marginBottom: '0' }}>
                        <Select
                          style={{ width: 71 }}
                          value={lateGo}
                          onChange={value => this.onChangeHourSelect(value, id, 'lateGo')}
                          getPopupContainer={() => document.querySelector('.manyun-modal-body')}
                        >
                          {TIME_HALF_AN_HOUR_INTERVAL.map(hour => {
                            return (
                              <Select.Option key={hour} value={hour}>
                                {hour}
                              </Select.Option>
                            );
                          })}
                        </Select>
                      </Form.Item>
                      <Form.Item label="小时，次日上班可以晚到" style={{ marginBottom: '0' }}>
                        <Select
                          style={{ width: 71 }}
                          value={lateArrive}
                          onChange={value => this.onChangeHourSelect(value, id, 'lateArrive')}
                          getPopupContainer={() => document.querySelector('.manyun-modal-body')}
                        >
                          {TIME_HALF_AN_HOUR_INTERVAL.map(hour => {
                            return (
                              <Select.Option key={hour} value={hour}>
                                {hour}
                              </Select.Option>
                            );
                          })}
                        </Select>
                      </Form.Item>
                      {/* 最后一个不可以删除 */}
                      <Form.Item style={{ marginBottom: '0' }}>小时</Form.Item>
                      {compensateHours.value.length > 1 && (
                        <Form.Item style={{ marginBottom: '0' }}>
                          <Button type="link" onClick={() => this.deleteLateGoLateArrive(id)}>
                            删除
                          </Button>
                        </Form.Item>
                      )}
                    </Col>
                  );
                })}

              {enableCompensate.value && compensateHours.value.length < 3 && (
                <Col xl={24}>
                  <Form.Item style={{ marginBottom: '0' }}>
                    <StyledButton type="link" onClick={this.newLateGoLateArrive}>
                      <PlusOutlined />
                      添加晚走晚到
                    </StyledButton>
                  </Form.Item>
                </Col>
              )}
            </TinyCard>
          </Row>
        </Form>
      </Modal>
    );
  }
}

const createOpts = {
  onFieldsChange(props, changedFields) {
    let fileds = changedFields;
    if (
      fileds.onDutyCheckStartTime &&
      fileds.onDutyCheckStartTime.value &&
      props.createOptions.onDutyTime.value
    ) {
      const middleTime = props.createOptions.onDutyTime.value.valueOf();
      const minTime = middleTime - 8 * 60 * 60 * 1000;
      const maxTime = middleTime;
      const rightTime = validateAndGetRightTime({
        selectedTime: fileds.onDutyCheckStartTime.value.valueOf(),
        maxTime,
        minTime,
      });
      fileds = {
        ...fileds,
        onDutyCheckStartTime: {
          ...fileds.onDutyCheckStartTime,
          value: moment(rightTime),
        },
      };
    }
    if (
      fileds.onDutyCheckEndTime &&
      fileds.onDutyCheckEndTime.value &&
      props.createOptions.onDutyTime.value
    ) {
      const middleTime = props.createOptions.onDutyTime.value.valueOf();
      const minTime = middleTime;
      const maxTime = middleTime + 8 * 60 * 60 * 1000;
      const rightTime = validateAndGetRightTime({
        selectedTime: fileds.onDutyCheckEndTime.value.valueOf(),
        maxTime,
        minTime,
      });
      fileds = {
        ...fileds,
        onDutyCheckEndTime: {
          ...fileds.onDutyCheckEndTime,
          value: moment(rightTime),
        },
      };
    }
    if (
      fileds.offDutyCheckStartTime &&
      fileds.offDutyCheckStartTime.value &&
      props.createOptions.offDutyTime.value
    ) {
      const middleTime = props.createOptions.offDutyTime.value.clone().startOf('minute').valueOf();
      const minTime = middleTime - 8 * 60 * 60 * 1000;
      const maxTime = middleTime;
      const yesterdayTime =
        fileds.offDutyCheckStartTime.value.clone().startOf('minute').valueOf() -
        24 * 60 * 60 * 1000;
      const rightTime = validateAndGetRightTime({
        selectedTime:
          fileds.offDutyCheckStartTime.value.valueOf() > maxTime
            ? yesterdayTime
            : fileds.offDutyCheckStartTime.value.valueOf(),
        maxTime,
        minTime,
      });
      fileds = {
        ...fileds,
        offDutyCheckStartTime: {
          ...fileds.offDutyCheckStartTime,
          value: moment(rightTime),
        },
      };
    }
    if (
      fileds.offDutyCheckEndTime &&
      fileds.offDutyCheckEndTime.value &&
      props.createOptions.offDutyTime.value
    ) {
      const middleTime = props.createOptions.offDutyTime.value.valueOf();
      const minTime = middleTime;
      const endDay = props.createOptions.offDutyTime.value.clone().endOf('day').valueOf();
      const maxTime =
        endDay > middleTime + 8 * 60 * 60 * 1000 ? middleTime + 8 * 60 * 60 * 1000 : endDay;
      const rightTime = validateAndGetRightTime({
        selectedTime: fileds.offDutyCheckEndTime.value.valueOf(),
        maxTime,
        minTime,
      });
      fileds = {
        ...fileds,
        offDutyCheckEndTime: {
          ...fileds.offDutyCheckEndTime,
          value: moment(rightTime),
        },
      };
    }
    props.updateCreateOptions(fileds);
  },
  mapPropsToFields(props) {
    return {
      dutyName: Form.createFormField(props.createOptions.dutyName),
      onDutyTime: Form.createFormField(props.createOptions.onDutyTime),
      offDutyTime: Form.createFormField(props.createOptions.offDutyTime),
      isOnDutyCheck: Form.createFormField(props.createOptions.isOnDutyCheck),
      onDutyCheckStartTime: Form.createFormField(props.createOptions.onDutyCheckStartTime),
      onDutyCheckEndTime: Form.createFormField(props.createOptions.onDutyCheckEndTime),
      isOffDutyCheck: Form.createFormField(props.createOptions.isOnDutyCheck),
      offDutyCheckStartTime: Form.createFormField(props.createOptions.offDutyCheckStartTime),
      offDutyCheckEndTime: Form.createFormField(props.createOptions.offDutyCheckEndTime),
      allowRest: Form.createFormField(props.createOptions.allowRest),
      restMinutes: Form.createFormField(props.createOptions.restMinutes),
      enableOffset: Form.createFormField(props.createOptions.enableOffset),
      onDutyOffsetMinutes: Form.createFormField(props.createOptions.onDutyOffsetMinutes),
      offDutyOffsetMinutes: Form.createFormField(props.createOptions.offDutyOffsetMinutes),
      enableBuffer: Form.createFormField(props.createOptions.enableBuffer),
      onDutyBufferMinutes: Form.createFormField(props.createOptions.onDutyBufferMinutes),
      offDutyBufferMinutes: Form.createFormField(props.createOptions.offDutyBufferMinutes),
      enableCompensate: Form.createFormField(props.createOptions.enableCompensate),
      allowContinuousWork: Form.createFormField(props.createOptions.allowContinuousWork),
      notAllowContinuousTime: Form.createFormField(props.createOptions.notAllowContinuousTime),
    };
  },
};

const EhanceShiftCreate = Form.create(createOpts)(ShiftCreate);

const mapStateToProps = ({
  staffShift: {
    shift: { createOptions, createShiftVisiable },
  },
}) => {
  return {
    createOptions,
    createShiftVisiable,
  };
};
const mapDispatchToProps = {
  updateCreateOptions: staffShiftActions.updateCreateOptions,
  changeCompensateHours: staffShiftActions.changeCompensateHours,
  createShift: createShiftActionCreator,
  updateCreateShiftVisiable: staffShiftActions.updateCreateShiftVisiable,
  editShift: editShiftActionCreator,
};
export default connect(mapStateToProps, mapDispatchToProps)(EhanceShiftCreate);

function range(start, end) {
  const result = [];
  for (let i = start; i < end; i++) {
    result.push(i);
  }
  return result;
}

function validateAndGetRightTime({ selectedTime, maxTime, minTime }) {
  // debugger;
  if (selectedTime > maxTime) {
    return maxTime;
  }
  if (selectedTime < minTime) {
    // 跨天
    if (
      selectedTime > moment(maxTime).startOf('day').valueOf() &&
      moment(maxTime).startOf('day').valueOf() !== moment(minTime).startOf('day').valueOf()
    ) {
      return selectedTime;
    }
    return minTime;
  }
  return selectedTime;
}
