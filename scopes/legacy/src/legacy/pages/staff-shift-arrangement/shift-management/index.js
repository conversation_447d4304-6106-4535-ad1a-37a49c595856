import React, { Component } from 'react';
import { connect } from 'react-redux';

import moment from 'moment';
import shortid from 'shortid';

import { Button } from '@manyun/base-ui.ui.button';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Input } from '@manyun/base-ui.ui.input';
import { Modal } from '@manyun/base-ui.ui.modal';

import { TinyCard, TinyTable, UserLink } from '@manyun/dc-brain.legacy.components';
import {
  deleteShiftActionCreator,
  getDutyIsBeUsedActionCreator,
  getShiftDataActionCreator,
  staffShiftActions,
} from '@manyun/dc-brain.legacy.redux/actions/staffShiftActions';

import EhanceShiftCreate from './components/new-shift-modal-button';
import { closedVlaue } from './constants';

class ShiftManageList extends Component {
  state = {
    mode: 'new',
    editId: null,
  };

  componentWillUnmount() {
    const { updateSearchValues, updatePagination } = this.props;
    updateSearchValues({});
    updatePagination({ pageNum: 1, pageSize: 10 });
  }

  componentDidMount() {
    this.props.getData();
  }

  onChangeValues = ({ target: { value } }) => {
    if (!value.trim()) {
      this.props.updateSearchValues({ dutyName: null });
    }
    this.props.updateSearchValues({ dutyName: value.trim() });
  };

  onChangePage = (pageNum, pageSize) => {
    this.props.updatePagination({ pageNum, pageSize });
    this.props.getData(false);
  };

  editShift = value => {
    this.setState({ mode: 'edit' });
    this.props.updateCreateShiftVisiable(true);
    let infos = {
      id: value.id,
      dutyName: {
        value: value.dutyName,
      },
      onDutyTime: {
        value: moment(value.onDutyTime, 'HH:mm'),
      },
      offDutyTime: {
        value: moment(value.offDutyTime, 'HH:mm'),
      },
      offIsNextDay: {
        value: value.offIsNextDay,
      },
      isOnDutyCheck: {
        value: value.dutyProperties.enableOnDutyCheckRange || false,
      },
      onDutyCheckStartTime: {
        value:
          value.dutyProperties.onDutyCheckRange && value.dutyProperties.onDutyCheckRange.startTime
            ? moment(value.dutyProperties.onDutyCheckRange.startTime, 'HH:mm')
            : undefined,
      },
      onDutyCheckEndTime: {
        value:
          value.dutyProperties.onDutyCheckRange && value.dutyProperties.onDutyCheckRange.endTime
            ? moment(value.dutyProperties.onDutyCheckRange.endTime, 'HH:mm')
            : undefined,
      },
      isOffDutyCheck: {
        value: value.dutyProperties.enableOffDutyCheckRange || false,
      },
      offDutyCheckStartTime: {
        value:
          value.dutyProperties.offDutyCheckRange && value.dutyProperties.offDutyCheckRange.startTime
            ? moment(value.dutyProperties.offDutyCheckRange.startTime, 'HH:mm')
            : undefined,
      },
      offDutyCheckEndTime: {
        value:
          value.dutyProperties.offDutyCheckRange && value.dutyProperties.offDutyCheckRange.endTime
            ? moment(value.dutyProperties.offDutyCheckRange.endTime, 'HH:mm')
            : undefined,
      },
      allowRest: {
        value: value.dutyProperties.allowRest || false,
      },
      restMinutes: {
        value: value.dutyProperties.restMinutes,
      },

      enableOffset: {
        value: value.dutyProperties.enableOffset || false,
      },
      onDutyOffsetMinutes: {
        value: value.dutyProperties.enableOffset
          ? this.getHourAndMinutes(value.dutyProperties.onDutyOffsetMinutes)
          : [60],
      },
      offDutyOffsetMinutes: {
        value: value.dutyProperties.enableOffset
          ? this.getHourAndMinutes(value.dutyProperties.offDutyOffsetMinutes)
          : [60],
      },
      enableBuffer: {
        value: value.dutyProperties.enableBuffer || false,
      },
      onDutyBufferMinutes: {
        value: value.dutyProperties.enableBuffer
          ? this.getHourAndMinutes(value.dutyProperties.onDutyBufferMinutes)
          : [60],
      },
      offDutyBufferMinutes: {
        value: value.dutyProperties.enableBuffer
          ? this.getHourAndMinutes(value.dutyProperties.offDutyBufferMinutes)
          : [60],
      },
      enableCompensate: {
        value: value.dutyProperties.enableCompensate || false,
      },
      compensateHours: {
        value: this.getCompensateHours(value.dutyProperties.compensateHours),
      },
      allowContinuousWork: {
        value: !value.dutyProperties.allowContinuousWork,
      },
      notAllowContinuousTime: {
        value: value.dutyProperties.notAllowContinuousTime
          ? Number(value.dutyProperties.notAllowContinuousTime) / 60
          : 8,
      },
      onDutyCheckRange: value.dutyProperties.onDutyCheckRange
        ? {
            startIsNextDay: value.dutyProperties.onDutyCheckRange.startIsNextDay,
            endIsNextDay: value.dutyProperties.onDutyCheckRange.endIsNextDay,
          }
        : {
            startIsNextDay: false,
            endIsNextDay: false,
          },
      offDutyCheckRange: value.dutyProperties.offDutyCheckRange
        ? {
            startIsNextDay: value.dutyProperties.offDutyCheckRange.startIsNextDay,
            endIsNextDay: value.dutyProperties.offDutyCheckRange.endIsNextDay,
          }
        : {
            startIsNextDay: false,
            endIsNextDay: false,
          },
      restRange: value.dutyProperties.restRange
        ? {
            startIsNextDay: value.dutyProperties.restRange.startIsNextDay,
            endIsNextDay: value.dutyProperties.restRange.endIsNextDay,
          }
        : {
            startIsNextDay: false,
            endIsNextDay: false,
          },
    };
    this.props.editCreateOption(infos);
  };

  getHourAndMinutes = time => {
    let tmp = [];
    if (typeof time === 'number') {
      const [merchant] = String(time / 60).split('.');
      const surplus = time % 60;
      tmp = [Number(merchant) * 60, surplus];
    } else {
      tmp = [closedVlaue];
    }
    return tmp;
  };

  getCompensateHours = hours => {
    let tmp = [];
    if (hours && hours.length) {
      tmp = hours.map(item => {
        const [lateGo, lateArrive] = item.split(',');
        return {
          id: shortid.generate(),
          lateGo,
          lateArrive,
        };
      });
    }
    return tmp;
  };

  confirmDelete = id => {
    Modal.confirm({
      title: '当前班次已被排班使用，请谨慎删除！',
      okText: '确定',
      cancelText: '取消',
      onOk: () => this.props.deleteShift(id),
    });
  };

  render() {
    const { pagination, loading, data, total, getData } = this.props;
    const { mode, editId } = this.state;

    return (
      <TinyCard>
        <TinyTable
          rowKey="id"
          dataSource={data}
          loading={loading}
          columns={columns(this, this.confirmDelete)}
          align={'left'}
          scroll={{ x: 'max-content' }}
          pagination={{
            total: total,
            current: pagination.pageNum,
            pageSize: pagination.pageSize,
            onChange: this.onChangePage,
          }}
          actionsWrapperStyle={{ justifyContent: 'space-between' }}
          actions={[
            <Button
              key="create-shift"
              type="primary"
              onClick={() => {
                this.setState({ mode: 'new' });
                this.props.updateCreateShiftVisiable(true);
                this.props.resetCreateShiftOptions();
              }}
            >
              新建班次
            </Button>,
            <Input.Search
              key="seach"
              allowClear
              placeholder="请输入班次名称"
              style={{ width: 211, height: 32 }}
              onSearch={getData}
              onChange={this.onChangeValues}
            />,
          ]}
        />

        <EhanceShiftCreate mode={mode} editId={editId} />
      </TinyCard>
    );
  }
}

const mapStateToProps = ({
  staffShift: {
    shift: { pagination, loading, data, total },
  },
}) => {
  return {
    pagination,
    loading,
    data,
    total,
  };
};
const mapDispatchToProps = {
  getData: getShiftDataActionCreator,
  setDataAndTotal: staffShiftActions.setDataAndTotal,
  resetShiftPageNum: staffShiftActions.resetShiftPageNum,
  updateSearchValues: staffShiftActions.updateSearchValues,
  updatePagination: staffShiftActions.updatePagination,
  deleteShift: deleteShiftActionCreator,
  getDutyIsBeUsed: getDutyIsBeUsedActionCreator,
  updateCreateShiftVisiable: staffShiftActions.updateCreateShiftVisiable,
  editCreateOption: staffShiftActions.editCreateOption,
  resetCreateShiftOptions: staffShiftActions.resetCreateShiftOptions,
};
export default connect(mapStateToProps, mapDispatchToProps)(ShiftManageList);

const columns = (ctx, confirmDelete) => [
  {
    title: '班次名称',
    dataIndex: 'dutyName',
    render: (text, record) => (
      <Button type="link" onClick={() => ctx.editShift(record)}>
        {text}
      </Button>
    ),
  },
  {
    title: '班次时间',
    key: 'onDutyTime',
    render: (_, record) => {
      return `${record.onDutyTime} - ${record.offIsNextDay ? '次日' : ''} ${record.offDutyTime}`;
    },
  },
  {
    title: '修改时间',
    dataIndex: 'gmtModified',
    dataType: 'dateTime',
    render: gmtModified => {
      return moment(gmtModified).format('YYYY-MM-DD HH:mm:ss');
    },
  },
  {
    title: '修改人',
    key: 'modifierId',
    render: (_, record) => <UserLink userId={record.modifierId} userName={record.modifierName} />,
  },
  {
    title: '操作',
    key: '__actions',
    fixed: 'right',
    render: (__, record) => (
      <span>
        <Button compact type="link" onClick={() => ctx.editShift(record)}>
          编辑
        </Button>
        <Divider type="vertical" />
        <DeleteConfirm
          variant="popconfirm"
          targetName={record.dutyName}
          onOk={() => {
            const p = {
              params: { id: record.id },
              confirmDelete: confirmDelete,
            };
            ctx.props.getDutyIsBeUsed(p);
          }}
        >
          <Button compact type="link">
            删除
          </Button>
        </DeleteConfirm>
      </span>
    ),
  },
];
