import React, { Component } from 'react';

import { Button } from '@manyun/base-ui.ui.button';

import { TinyModal, TinyTable } from '@manyun/dc-brain.legacy.components';

const getColumns = ctx => [
  {
    title: '序号',
    dataIndex: '',
    render: (_, __, index) => index + 1,
  },
  {
    title: '班组名称',
    dataIndex: 'groupName',
  },
  {
    title: '位置',
    dataIndex: 'blockTag',
  },
];

class TeamModal extends Component {
  state = {
    visible: false,
  };

  changeModalVisible = () => {
    this.setState({
      visible: !this.state.visible,
    });
  };

  render() {
    const { text, type, record } = this.props;
    const { visible } = this.state;

    return [
      <Button key="btn" type={type} onClick={this.changeModalVisible}>
        {text}
      </Button>,
      <TinyModal
        title="班组数量"
        visible={visible}
        onCancel={this.changeModalVisible}
        onClose={this.changeModalVisible}
        footer={null}
        key="modal"
      >
        <TinyTable
          rowKey="id"
          columns={getColumns(this)}
          align={'left'}
          dataSource={record.dutyGroupList}
        />
      </TinyModal>,
    ];
  }
}

export default TeamModal;
