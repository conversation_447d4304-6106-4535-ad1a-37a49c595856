/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { Radio } from '@manyun/base-ui.ui.radio';

import type { Block } from '@manyun/resource-hub.model.block';
import { fetchBlock } from '@manyun/resource-hub.service.fetch-block';

interface IProps {
  onChange?: (values: any) => void;
  value?: string;
  id?: string;
  /** 重置表单字段 */
  resetFields: (fields?: string[]) => void;
  /** 楼栋/机房数据 */
  idcBlockTag: Record<string, any>;
}

enum EClockIdcSelect {
  /** 考勤机打卡 */
  'MACHINE' = 'MACHINE',
  /** 手机打卡 */
  'MOBILE' = 'MOBILE',
  /** 系统登录打卡 */
  'LOCAL' = 'LOCAL',
}

function ClockRadio(props: IProps) {
  const { value: clockValue, onChange, resetFields, idcBlockTag, id: fieldName } = props || {};
  /** gps是否可选 */
  const [canGps, setCanGps] = useState(false);
  /** 楼栋数据 */
  const blockTag = !!idcBlockTag?.value?.length ? idcBlockTag?.value[1] : undefined;

  const handleResetRadio = React.useCallback(
    (data: Block) => {
      /** 表单数值与楼栋配置互斥则清空重选 */
      if (
        clockValue === EClockIdcSelect.MOBILE &&
        ![!!data.coordinates ? EClockIdcSelect.MOBILE : undefined].includes(
          clockValue as EClockIdcSelect
        )
      ) {
        resetFields && resetFields([fieldName as string]);
      }
    },
    [clockValue, fieldName]
  );

  const getBlockDetail = async (props: { blockTag: string }) => {
    const { data, error } = await fetchBlock({
      guid: props.blockTag,
    });
    if (error) {
      message.error(error.message);
    } else if (data) {
      setCanGps(!!data.coordinates);
      handleResetRadio(data);
    }
  };

  useEffect(() => {
    if (blockTag) {
      getBlockDetail({ blockTag });
    }
  }, [blockTag]);

  useEffect(() => {
    // 表单如果选中机房，需重置打卡方式，gps不可选
    if (idcBlockTag && idcBlockTag?.value?.length === 1 && fieldName) {
      setCanGps(false);
      resetFields && resetFields([fieldName]);
    }
  }, [idcBlockTag, fieldName]);

  return (
    <Radio.Group value={clockValue} onChange={e => onChange && onChange(e.target.value)}>
      <Radio value={EClockIdcSelect.MACHINE}>考勤机打卡</Radio>
      <Radio value={EClockIdcSelect.MOBILE} disabled={!canGps}>
        GPS打卡
      </Radio>
      <Radio value={EClockIdcSelect.LOCAL}>系统登录打卡</Radio>
    </Radio.Group>
  );
}

export default ClockRadio;
