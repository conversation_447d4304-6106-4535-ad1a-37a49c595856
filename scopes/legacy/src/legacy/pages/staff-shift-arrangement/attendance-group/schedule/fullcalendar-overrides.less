@import '~@manyun/base-ui-web.style.style/dist/prefix.less';
@import '~@manyun/base-ui.theme.theme/dist/theme.variable.less';

.fc-daygrid-day-number {
  color: @text-color;
}

.fc-col-header-cell-cushion {
  color: @text-color;
}

.fc-daygrid-day-frame .fc-daygrid-day-top {
  height: 30px;
}

.fc-scrollgrid-sync-table .fc-daygrid-day-events {
  max-height: 120px;
  overflow: auto;
}
.fc-scrollgrid-section-liquid .fc-scroller-liquid-absolute {
  overflow: visible;
}
.fc-daygrid-event {
  cursor: text;
}

// .fc-theme-standard td {
//   border: 1px solid var(--border-color-fullcalendar);
// }

// .fc-theme-standard th {
//   border: none;
// }

// .fc-theme-standard .fc-scrollgrid {
//   border: none;
// }
// .fc .fc-scrollgrid-section-liquid > td {
//   border: none;
// }

// .fc-daygrid-day-number {
//   color: var(--text-color);
// }

// .fc-col-header-cell-cushion {
//   color: var(--text-color);
// }

// /* .fc .fc-daygrid-event-harness {
//   padding: 0 8px 8px 8px;
// } */

// .fc-direction-ltr .fc-daygrid-event.fc-event-end,
// .fc-direction-rtl .fc-daygrid-event.fc-event-start {
//   margin: 0 8px 8px 0px;
// }
// .fc-direction-ltr .fc-daygrid-event.fc-event-start,
// .fc-direction-rtl .fc-daygrid-event.fc-event-end {
//   margin-left: 8px;
// }

// .fc-event-main p {
//   margin: 0;
//   padding: 5px;
// }

// .fc .fc-button {
//   line-height: 1.499;
//   position: relative;
//   display: inline-block;
//   font-weight: 400;
//   white-space: nowrap;
//   text-align: center;
//   background-image: none;
//   border: 1px solid transparent;
//   box-shadow: 0 2px 0;
//   cursor: pointer;
//   transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
//   -webkit-user-select: none;
//   user-select: none;
//   touch-action: manipulation;
//   height: 32px;
//   padding: 0 15px;
//   font-size: 14px;
//   border-radius: 4px;
//   color: var(--text-color);
//   background-color: ~'var(--@{prefix}-primary-color)';
//   border-color: var(--border-color-base);
// }

// .fc .fc-button-primary:disabled {
//   border-color: var(--border-color-base);
//   text-shadow: none;
//   box-shadow: none;
// }

// .fc .fc-button.fc-button-primary.fc-today-button {
//   padding: 0 8px;
//   height: 26px;
// }

// .fc-header-toolbar.fc-toolbar.fc-toolbar-ltr .fc-button-group .fc-prev-button,
// .fc-header-toolbar.fc-toolbar.fc-toolbar-ltr .fc-button-group .fc-next-button {
//   background-color: ~'var(--@{prefix}-primary-color)';
//   height: 26px;
//   padding: 0 6.5px;
// }

// .fc-header-toolbar.fc-toolbar.fc-toolbar-ltr .fc-button-group .fc-prev-button > span.fc-icon,
// .fc-header-toolbar.fc-toolbar.fc-toolbar-ltr .fc-button-group .fc-next-button > span.fc-icon {
//   font-size: 13px;
// }

// .fc-scrollgrid-section-header .fc-scrollgrid-sync-inner {
//   text-align: right;
// }
// .fc-daygrid-day-frame {
//   min-height: 150px;
//   height: auto;
// }

// .fc .fc-daygrid-day.fc-day-today {
//   background-color: transparent;
// }

// .fc .fc-daygrid-day-number {
//   padding: 0 8px;
// }

// .fc .fc-scrollgrid-section {
//   height: 35px;
//   padding-bottom: 10px;
// }

// .fc .fc-scroller-liquid-absolute {
//   overflow: auto !important;
// }

// .fc-daygrid-day-number {
//   width: 100%;
//   height: 39px;
//   line-height: 39px;
//   text-align: right;
// }

// .fc .fc-view-harness {
//   height: 950px !important;
// }
