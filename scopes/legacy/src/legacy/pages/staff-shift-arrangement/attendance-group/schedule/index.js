import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import uniqBy from 'lodash.uniqby';
import moment from 'moment';
import styled from 'styled-components';

import { Button } from '@manyun/base-ui.ui.button';
import { FullCalendar } from '@manyun/base-ui.ui.full-calendar';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Tag } from '@manyun/base-ui.ui.tag';

import { FooterToolBar, GutterWrapper, TinyCard } from '@manyun/dc-brain.legacy.components';
import { TagColor } from '@manyun/dc-brain.legacy.pages/staff-shift-arrangement/constants';
import { redirectActionCreator } from '@manyun/dc-brain.legacy.redux/actions/routerActions';
import {
  attGroupScheduleRuleUpdateActionCreator,
  getAttGroupDetailActionCreator,
  getScheduleActionCreator,
  getScheduleInMonthActionCreator,
  getStatutoryHolidayActionCreator,
  staffShiftActions,
} from '@manyun/dc-brain.legacy.redux/actions/staffShiftActions';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';

import EhanceTimeSelect from './components/time-select';
import UserSelectPopover from './components/user-select-popover';

const HolidayDiv = styled.div`
  width: 100%;
  display: flex;
`;
const HolidaySpan = styled.span`
  width: 60px;
  text-align: left;
`;
const DateSpan = styled.span`
  width: 50%;
  text-align: right;
`;

class Schedule extends Component {
  state = {
    startTimeInCalendar: null,
    endTimeInCalendar: null,
    attGroupId: null,
  };

  componentDidMount() {
    const { match, mode } = this.props;
    this.props.getAttGroupDetail({ attGroupId: match.params.id, mode: mode });
  }

  componentDidUpdate(prevProps, preState) {
    if (
      (prevProps.mode === 'scheduleDetail' && this.props.mode === 'scheduleEdit') ||
      (prevProps.mode === 'schedule' && this.props.mode === 'scheduleDetail')
    ) {
      this.props.getAttGroupDetail({
        attGroupId: this.props.match.params.id,
        mode: this.props.mode,
      });
    }

    const preYear = moment(preState.startTimeInCalendar).format('YYYY');
    const nextYear = moment(this.state.startTimeInCalendar).format('YYYY');
    if (preYear !== nextYear) {
      this.props.getStatutoryHoliday(nextYear);
    }

    if (prevProps.match.path !== this.props.match.path && this.state.startTimeInCalendar) {
      this.props.getScheduleInMonth({
        beginDate: this.state.startTimeInCalendar,
        endDate: this.state.endTimeInCalendar,
        attGroupId: this.state.attGroupId,
      });
    }
  }

  componentWillUnmount() {
    this.props.resetAttendanceGroupScheduleCalendar();
  }

  onSubmit = event => {
    event.preventDefault();
    this.props.form.validateFieldsAndScroll(async (err, values) => {
      if (err) {
        return;
      }
      if (!err) {
        const { schedule } = this.props;
        const params = {
          mode: this.props.mode,
          attGroupId: this.props.match.params.id,
          scheduleCycleMonths: schedule.scheduleCycleMonths,
          scheduleStartTime: schedule.scheduleStartTime,
        };
        this.props.attGroupScheduleRuleUpdate(params);
      }
    });
  };

  renderHistoryShiftsDescripte = () => {
    const { shiftsDescribe, scheduleCalendar } = this.props;
    let historyShifts = [];
    if ((shiftsDescribe ?? []).length === 0 || (scheduleCalendar ?? []).length === 0) {
      return;
    }
    const shiftsIds = (shiftsDescribe ?? []).map(shift => shift.id);
    (scheduleCalendar ?? []).forEach(schedule => {
      if (!shiftsIds.includes(schedule.duty.id)) {
        historyShifts.push(schedule.duty);
      }
    });
    historyShifts = uniqBy(historyShifts, 'id');
    if (historyShifts.length > 0) {
      return <Tag color="default">排班调整班次信息前的排班记录</Tag>;
    }
  };

  getTagColor = (dutyId, duty) => {
    const { shiftsDescribe } = this.props;
    const index = (shiftsDescribe ?? []).findIndex(shift => shift?.id === dutyId);
    if (index > -1) {
      return TagColor[index % 4];
    }
    return 'default';
  };

  render() {
    const {
      shiftsDescribe,
      periodTxt,
      schedule,
      scheduleCalendar,
      options,
      mode,
      form: { getFieldDecorator },
    } = this.props;

    return (
      <>
        <TinyCard bordered={false} bodyStyle={{ paddingBottom: '50px' }}>
          <Form layout="inline" colon={false}>
            <Row>
              <Col xl={24}>
                <Form.Item label="班次说明" style={{ marginBottom: '0' }}>
                  <GutterWrapper>
                    {shiftsDescribe &&
                      shiftsDescribe.map((item, index) => {
                        return (
                          <Tag key={item.id} color={TagColor[index % 4]}>
                            {item.text}
                          </Tag>
                        );
                      })}
                    {this.renderHistoryShiftsDescripte()}
                  </GutterWrapper>
                </Form.Item>
              </Col>
              <Col xl={24}>
                <Form.Item label="排班周期" style={{ marginBottom: '0' }}>
                  {periodTxt && <Tag color={TagColor[0]}>{periodTxt}</Tag>}
                </Form.Item>
              </Col>
            </Row>
            {schedule.scheduleStartTime && this.props.mode === 'schedule' && (
              <Form.Item label="排班开始时间" style={{ marginBottom: '0' }}>
                {moment(schedule.scheduleStartTime).format('YYYY-MM-DD')}
              </Form.Item>
            )}
            {schedule.scheduleStartTime &&
              (this.props.mode === 'scheduleEdit' || this.props.mode === 'scheduleDetail') && (
                <Form.Item label="下次排班开始时间" style={{ marginBottom: '0' }}>
                  {moment(schedule.scheduleStartTime).format('YYYY-MM-DD')}
                </Form.Item>
              )}
            {schedule.scheduleCycleMonths && (
              <Form.Item label="排班周期" style={{ marginBottom: '0' }}>
                {schedule.scheduleCycleMonths}月
              </Form.Item>
            )}
            {mode === 'schedule' && (
              <Form.Item label="" style={{ marginBottom: '0' }}>
                {getFieldDecorator('scheduleCalendar', {
                  rules: [
                    {
                      required: true,
                      validator: (_, value, callback) => {
                        if (!scheduleCalendar) {
                          callback('开始排班事件和排班周期必选');
                        } else {
                          callback();
                        }
                      },
                    },
                  ],
                })(
                  <Button type="primary" onClick={() => this.props.updateTimeSelectVisible(true)}>
                    选择
                  </Button>
                )}
              </Form.Item>
            )}
            {mode === 'scheduleEdit' && (
              <Form.Item style={{ marginBottom: '0' }}>
                <Button
                  type="primary"
                  size="small"
                  onClick={() => this.props.updateTimeSelectVisible(true)}
                >
                  重新排班
                </Button>
              </Form.Item>
            )}
          </Form>
          {(scheduleCalendar || mode !== 'schedule') && (
            <FullCalendar
              initialView="dayGridMonth"
              editable={false}
              headerToolbar={{
                left: 'prev,next today',
                center: 'title',
                right: '',
              }}
              locale="zh-cn"
              buttonText={{
                today: '今日',
              }}
              dayCellContent={arg => {
                const date = moment(arg.date).format('YYYY-MM-DD');
                if (schedule.statutoryHolidays.includes(date)) {
                  if (arg.isToday) {
                    return (
                      <HolidayDiv>
                        <HolidaySpan>节假日</HolidaySpan>
                        <DateSpan>{arg.dayNumberText}</DateSpan>
                      </HolidayDiv>
                    );
                  }

                  return (
                    <HolidayDiv>
                      <HolidaySpan>节假日</HolidaySpan>
                      <DateSpan>{arg.dayNumberText}</DateSpan>
                    </HolidayDiv>
                  );
                } else {
                  if (arg.isToday) {
                    return <span>{arg.dayNumberText}</span>;
                  }

                  return <span>{arg.dayNumberText}</span>;
                }
              }}
              aspectRatio={2} //设置日历的宽高比，值为浮点型，默认1.35。日历是块级元素，会尽量撑满宽度，日程表的高度则有aspectRatio决定（提示：aspectRatio的值越大，高度越小
              events={(scheduleCalendar ?? []).map(schedule => ({
                ...schedule,
                textColor: this.getTagColor(schedule.duty?.id),
              }))}
              eventContent={eventInfo => {
                const idcBlock = options.idcBlockTag.value;
                const params = {
                  resourceCode: idcBlock[1],
                };
                return (
                  <UserSelectPopover eventInfo={eventInfo} selectUserParams={params} mode={mode} />
                );
              }}
              datesSet={async ({ start, end }) => {
                // 从新建到排班 到详情  到编辑   datesSet只执行一次
                this.setState({
                  startTimeInCalendar: start.getTime(),
                  endTimeInCalendar: end.getTime(),
                  attGroupId: this.props.match.params.id,
                });
                if (mode !== 'schedule') {
                  const params = {
                    attGroupId: this.props.match.params.id,
                    beginDate: start.getTime(),
                    endDate: end.getTime(),
                  };
                  this.props.getScheduleInMonth(params);
                }
              }}
            />
          )}
        </TinyCard>
        <FooterToolBar>
          {mode === 'schedule' && (
            <Button type="primary" onClick={this.onSubmit}>
              提交
            </Button>
          )}
          {mode === 'scheduleDetail' && (
            <Button
              type="primary"
              onClick={() => {
                const params = {
                  id: this.props.match.params.id,
                  groupName: options.attName.value,
                };
                const scheduleUrl = urls.generateAttGroupScheduleEditLocation(params);

                this.props.redirect(scheduleUrl);
              }}
            >
              编辑
            </Button>
          )}
          {mode === 'scheduleEdit' && (
            <Button
              type="primary"
              onClick={() => {
                const params = {
                  id: this.props.match.params.id,
                  groupName: options.attName.value,
                };
                const scheduleUrl = urls.generateAttGroupScheduleDetailLocation(params);

                this.props.redirect(scheduleUrl);
              }}
            >
              返回
            </Button>
          )}
        </FooterToolBar>

        <EhanceTimeSelect
          attGroupId={this.props.match.params.id}
          mode={mode}
          enableStatutoryHoliday={options?.shifts?.value?.enableStatutoryHoliday}
        />
      </>
    );
  }
}

const createOpts = {
  mapPropsToFields(props) {
    return {
      scheduleCalendar: Form.createFormField(props.scheduleCalendar),
    };
  },
};

const EhanceSchedule = Form.create(createOpts)(Schedule);

const mapStateToProps = ({
  staffShift: {
    attendanceGroup: { edit, schedule, scheduleCalendar },
  },
}) => {
  let shiftsDescribe = [];
  let periodTxtArr = [];
  if (edit.shifts.value && edit.shifts.value.dutys) {
    const timeArr = edit.shifts.value.dutys;
    shiftsDescribe = timeArr.map(item => {
      const { offIsNextDay, dutyName, onDutyTime, offDutyTime, id } = item;
      if (offIsNextDay) {
        return { id, text: `${dutyName}：${onDutyTime}-次日${offDutyTime}` };
      }
      return { id, text: `${dutyName}：${onDutyTime}-${offDutyTime}` };
    });
  }
  if (edit.shifts.value && edit.shifts.value.dailyDuty) {
    const dailyDuty = edit.shifts.value.dailyDuty;
    dailyDuty.forEach(item => {
      if (!item) {
        periodTxtArr = [...periodTxtArr, '休'];
        return;
      }
      periodTxtArr = [...periodTxtArr, item.dutyName];
    });
  }

  return {
    options: edit,
    schedule,
    shiftsDescribe,
    periodTxt: periodTxtArr.join('-'),
    scheduleCalendar,
  };
};
const mapDispatchToProps = {
  getAttGroupDetail: getAttGroupDetailActionCreator,
  updateAttGroupSchedule: staffShiftActions.updateAttGroupSchedule,
  getSchedule: getScheduleActionCreator,
  attGroupScheduleRuleUpdate: attGroupScheduleRuleUpdateActionCreator,
  updateTimeSelectVisible: staffShiftActions.updateTimeSelectVisible,
  redirect: redirectActionCreator,
  getScheduleInMonth: getScheduleInMonthActionCreator,
  getStatutoryHoliday: getStatutoryHolidayActionCreator,
  resetAttendanceGroupScheduleCalendar: staffShiftActions.resetAttendanceGroupScheduleCalendar,
};
export default connect(mapStateToProps, mapDispatchToProps)(EhanceSchedule);
