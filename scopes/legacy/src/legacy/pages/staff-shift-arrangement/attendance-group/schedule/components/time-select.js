import Form from '@ant-design/compatible/es/form';
import QuestionCircleOutlined from '@ant-design/icons/es/icons/QuestionCircleOutlined';
import { message } from 'antd';
import dayjs from 'dayjs';
import moment from 'moment';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import { Alert } from '@manyun/base-ui.ui.alert';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import { generateAttGroupManagementScheduleDetailRoutePath } from '@manyun/hrm.route.hrm-routes';
import { fetchHolidayConfiguration } from '@manyun/hrm.service.fetch-holiday-configuration';
import { validCanUpdateScheduleRule } from '@manyun/hrm.service.valid-can-update-schedule-rule';

import {
  attGroupScheduleRuleUpdateActionCreator,
  editAttGroupActionCreator,
  getScheduleActionCreator,
  staffShiftActions,
} from '@manyun/dc-brain.legacy.redux/actions/staffShiftActions';

const formItemLayout = {
  labelCol: {
    xl: { span: 6 },
  },
  wrapperCol: {
    xl: { span: 18 },
  },
};

class TimeSelect extends Component {
  state = {
    scheduleStartTime: null,
    scheduleCycleMonths: null,
    validErrorMessage: null,
    disabledNextYear: false,
  };

  handleOk = event => {
    event.preventDefault();
    this.props.form.validateFieldsAndScroll(async (err, values) => {
      if (err) {
        return;
      }
      if (this.state.validErrorMessage) {
        return;
      }
      if (!err) {
        if (this.props.mode === 'schedule') {
          const params = {
            attGroupId: this.props.attGroupId,
            ...values,
            mode: this.props.mode,
            scheduleStartTime: moment(values.scheduleStartTime).valueOf(),
          };
          this.props.getSchedule(params);
        } else {
          // const { schedule } = this.props;
          // const pastTime = moment(schedule.scheduleStartTime).valueOf();
          const newTime = moment(values.scheduleStartTime).startOf('day').valueOf();
          //如果排班开始时间和排班周期没改变 不发请求
          // if (pastTime === newTime && schedule.scheduleCycleMonths === values.scheduleCycleMonths) {
          //   this.props.updateTimeSelectVisible(false);
          //   return;
          // }
          const params = {
            attGroupId: this.props.attGroupId,
            ...values,
            mode: `${this.props.mode}_changeTime`,
            scheduleStartTime: newTime,
            type: this.props.type,
          };
          //编辑考勤组时 需要检验是否能提交 在更新考勤组信息 更新成功后在更新排班信息
          if (this.props.type === 'attendance') {
            const { error } = await validCanUpdateScheduleRule({
              attGroupId: params.attGroupId,
              attStartTime: params.scheduleStartTime,
              scheduleCycleMonths: params.scheduleCycleMonths,
            });
            if (error) {
              message.error(error.message);
              return;
            }
            // 先保存班组信息，再更新排班信息
            this.props.editAttGroup(params);
          } else {
            this.props.updateSchedule(params);
          }
        }
      }
    });
  };

  componentDidUpdate = async (prevProps, preState) => {
    const {
      enableStatutoryHoliday,
      attGroupId,
      schedule: { timeSelectVisible },
    } = this.props;
    const { scheduleStartTime, scheduleCycleMonths } = this.state;
    if (
      enableStatutoryHoliday &&
      preState.scheduleStartTime &&
      preState.scheduleCycleMonths &&
      scheduleStartTime &&
      scheduleCycleMonths &&
      (preState.scheduleStartTime !== scheduleStartTime ||
        preState.scheduleCycleMonths !== scheduleCycleMonths)
    ) {
      //调用校验的接口
      const { error } = await validCanUpdateScheduleRule({
        attGroupId: attGroupId,
        attStartTime: scheduleStartTime,
        scheduleCycleMonths: scheduleCycleMonths,
      });
      if (error) {
        this.setState({ validErrorMessage: error.message });
      } else {
        this.setState({ validErrorMessage: null });
      }
    }
    if (
      enableStatutoryHoliday &&
      timeSelectVisible &&
      prevProps.schedule.timeSelectVisible !== timeSelectVisible
    ) {
      const { error, data } = await fetchHolidayConfiguration({
        year: dayjs().add(1, 'year').get('year'),
      });
      if (error) {
        message.error(error.message);
        return;
      }
      this.setState({
        disabledNextYear: (data?.statutoryHolidays ?? []).length < 11,
      });
    }
  };

  render() {
    const {
      form: { getFieldDecorator },
      schedule: {
        timeSelectVisible,
        // scheduleStartTime,
        // scheduleCycleMonths,
        timeModalButtonLoading,
      },
      type,
      attGroupId,
      groupName,
      enableStatutoryHoliday,
    } = this.props;
    const { validErrorMessage, disabledNextYear } = this.state;
    return (
      <Modal
        width={572}
        open={timeSelectVisible}
        okButtonProps={{
          disabled: timeModalButtonLoading,
          loading: timeModalButtonLoading,
        }}
        cancelButtonProps={{
          disabled: timeModalButtonLoading,
        }}
        destroyOnClose
        title="重新排班"
        okText={type === 'attendance' ? '确认重新排班' : '确定'}
        afterClose={() => {
          this.setState({
            scheduleStartTime: null,
            scheduleCycleMonths: null,
            validErrorMessage: null,
            disabledNextYear: false,
          });
        }}
        onOk={this.handleOk}
        onCancel={() => this.props.updateTimeSelectVisible(false)}
      >
        {(type === 'attendance' || enableStatutoryHoliday) && (
          <Space style={{ paddingBottom: 24 }} direction="vertical" size="large">
            <Alert
              style={{ width: 524 }}
              description={
                <Space style={{ width: '100%' }}>
                  <ul style={{ margin: 0, padding: '0 16px', listStyleType: 'circle' }}>
                    {type === 'attendance' && (
                      <li>参与考勤班组或班组顺序存在改动，需重新排班后才可生效</li>
                    )}
                    {enableStatutoryHoliday && (
                      <li>当前考勤组为法定排休班制，仅支持对已公布法定节假日的年份进行排班</li>
                    )}
                  </ul>
                </Space>
              }
              type="warning"
            />
            {type === 'attendance' && (
              <Space size={0}>
                <Typography.Text strong>温馨提醒：</Typography.Text>
                <Typography.Text>
                  请参照
                  <Typography.Link
                    href={generateAttGroupManagementScheduleDetailRoutePath({
                      id: attGroupId,
                      groupName,
                    })}
                    target="_blank"
                  >
                    当前排班表
                  </Typography.Link>
                  进行排班，避免出现同一班组连续排班情况。
                </Typography.Text>
              </Space>
            )}
          </Space>
        )}
        <Form {...formItemLayout}>
          <Form.Item label="开始排班时间" style={{ marginBottom: '24px' }}>
            {getFieldDecorator('scheduleStartTime', {
              rules: [{ required: true, message: '开始排班时间必选' }],
              // initialValue: scheduleStartTime,
            })(
              <DatePicker
                style={{ width: 228 }}
                disabledDate={current => {
                  return (
                    current < moment().startOf('day') ||
                    (enableStatutoryHoliday
                      ? disabledNextYear
                        ? current > moment().add(1, 'year').startOf('year')
                        : current > moment().add(2, 'year').startOf('year')
                      : false)
                  );
                }}
                onChange={value => {
                  this.setState({
                    scheduleStartTime: value ? moment(value).valueOf() : value,
                  });
                }}
              />
            )}
          </Form.Item>

          <Form.Item
            style={{ marginBottom: '0' }}
            label={
              <span>
                排班周期&nbsp;
                <Tooltip title={<span>排班最小周期1个月，最大周期12个月。</span>}>
                  <QuestionCircleOutlined />
                </Tooltip>
              </span>
            }
            validateStatus={validErrorMessage ? 'error' : undefined}
            help={validErrorMessage ?? undefined}
          >
            {getFieldDecorator('scheduleCycleMonths', {
              rules: [{ required: true, message: '排班周期必选' }],
              // initialValue: scheduleCycleMonths,
            })(
              <InputNumber
                style={{ width: 120 }}
                formatter={value => `${value}个月`}
                parser={value => {
                  let number = value;
                  Array.from('个月').forEach(char => {
                    number = number.replace(char, '');
                  });
                  return number;
                }}
                min={1}
                max={12}
                onChange={value => {
                  this.setState({
                    scheduleCycleMonths: value,
                  });
                }}
              />
            )}
          </Form.Item>
        </Form>
      </Modal>
    );
  }
}

const createOpts = {};

const EhanceTimeSelect = Form.create(createOpts)(TimeSelect);

const mapStateToProps = ({
  staffShift: {
    attendanceGroup: { schedule },
  },
}) => {
  return {
    schedule,
  };
};
const mapDispatchToProps = {
  updateAttGroupSchedule: staffShiftActions.updateAttGroupSchedule,
  getSchedule: getScheduleActionCreator,
  updateTimeSelectVisible: staffShiftActions.updateTimeSelectVisible,
  updateSchedule: attGroupScheduleRuleUpdateActionCreator,
  editAttGroup: editAttGroupActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(EhanceTimeSelect);
