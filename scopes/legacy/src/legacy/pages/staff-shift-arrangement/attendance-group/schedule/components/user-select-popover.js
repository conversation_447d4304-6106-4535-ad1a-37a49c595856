import React, { Component } from 'react';
import { connect } from 'react-redux';

import { ApiSelect } from '@galiojs/awesome-antd';
import uniqBy from 'lodash/uniqBy';
import moment from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { Popover } from '@manyun/base-ui.ui.popover';
import { Tag } from '@manyun/base-ui.ui.tag';

import { fetchAvailableShiftUsers } from '@manyun/hrm.service.fetch-available-shift-users';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';
import {
  attGroupScheduleRuleUpdateActionCreator,
  staffShiftActions,
} from '@manyun/dc-brain.legacy.redux/actions/staffShiftActions';

class UserSelectPopover extends Component {
  state = {
    visible: false,
    userIds: [],
    users: [],
  };

  componentDidUpdate(preProps) {
    const { userIds, users } = this.props;
    if (preProps.userIds !== userIds || preProps.users !== users) {
      this.setState({ userIds: userIds, users });
    }
  }

  getEvent = () => {
    const { eventInfo } = this.props;
    return (
      <div>
        <span style={{ float: 'left' }}>{eventInfo.event.title}</span>
        <span style={{ float: 'right' }}>{eventInfo.event.extendedProps.timeInterval}</span>
      </div>
    );
  };

  render() {
    const { eventInfo, selectUserParams, mode, scheduleStaffJson } = this.props;
    const { visible, userIds, users } = this.state;
    const startParse = moment(eventInfo.event.start).valueOf();

    const timestamp = Date.parse(new Date());

    let disadled = false;
    if (mode === 'scheduleDetail' || startParse < timestamp) {
      disadled = true;
    }

    return (
      <Popover
        trigger="click"
        visible={visible}
        onVisibleChange={visible => {
          this.setState({
            visible: visible,
          });
        }}
        content={
          <>
            {disadled &&
              users &&
              users.map(({ userName }, index) => {
                return <Tag key={index}>{userName}</Tag>;
              })}
            {!disadled && (
              <GutterWrapper>
                <ApiSelect
                  style={{ width: 200 }}
                  mode="multiple"
                  showArrow={false}
                  showSearch
                  disabled={disadled}
                  getPopupContainer={trigger => trigger.parentNode}
                  trigger="onDidMount"
                  optionWithValue
                  fieldNames={{ label: 'userName', value: 'id' }}
                  value={userIds}
                  dataService={async () => {
                    const { error, data } = await fetchAvailableShiftUsers({
                      ...selectUserParams,
                      dutyGroupId: eventInfo.event.groupId,
                    });
                    if (error) {
                      return Promise.resolve([]);
                    }
                    const id = eventInfo.event.id;
                    const users = scheduleStaffJson[id];
                    const fetchData = data.data.map(d => ({ ...d, userName: d.name }));
                    const allData = uniqBy([...users, ...fetchData], 'id');
                    return Promise.resolve(allData);
                  }}
                  onChange={(id, option) => {
                    this.setState({ userIds: id, users: option });
                  }}
                />
                {mode === 'scheduleEdit' && (
                  <Button
                    type="primary"
                    size="small"
                    onClick={() => {
                      // const params = {
                      //   userIds: userIds,
                      //   seldectUsers: users,
                      //   scheduleUnicode: eventInfo.event.id,
                      // };
                      // this.props.updateScheduleCalendar(params);
                      this.props.attGroupScheduleRuleUpdate({
                        mode: `${mode}_changeUser`,
                        scheduleStaffList: [
                          { scheduleUnicode: eventInfo.event.id, staffIds: userIds },
                        ],
                      });
                      this.setState({ visible: false });
                    }}
                  >
                    确定
                  </Button>
                )}
              </GutterWrapper>
            )}
          </>
        }
      >
        <Tag
          color={eventInfo.textColor === '' ? 'default' : eventInfo.textColor ?? 'default'}
          onClick={() => {
            this.setState({ visible: true, userIds: this.props.userIds, users: this.props.users });
          }}
          style={{ width: '100%' }}
        >
          {this.getEvent()}
        </Tag>
      </Popover>
    );
  }
}

const mapStateToProps = (
  {
    staffShift: {
      attendanceGroup: {
        schedule: { scheduleStaffJson },
      },
    },
  },
  { eventInfo }
) => {
  let users = null;
  let userIds = [];
  if (eventInfo) {
    const unicode = eventInfo.event.id;
    if (scheduleStaffJson[unicode]) {
      users = scheduleStaffJson[unicode];
      userIds = scheduleStaffJson[unicode].map(({ id }) => id);
    }
  }
  return {
    scheduleStaffJson,
    users,
    userIds,
  };
};
const mapDispatchToProps = {
  updateScheduleCalendar: staffShiftActions.updateScheduleCalendar,
  attGroupScheduleRuleUpdate: attGroupScheduleRuleUpdateActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(UserSelectPopover);
