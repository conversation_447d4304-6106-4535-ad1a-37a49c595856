import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import moment from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Input } from '@manyun/base-ui.ui.input';
import { Space } from '@manyun/base-ui.ui.space';
import { getLocationSearchMap, setLocationSearch } from '@manyun/base-ui.util.query-string';

import { TinyCard, TinyTable, UserLink } from '@manyun/dc-brain.legacy.components';
import * as urls from '@manyun/dc-brain.legacy.constants/urls';
import { redirectActionCreator } from '@manyun/dc-brain.legacy.redux/actions/routerActions';
import {
  deleteAttGroupActionCreator,
  getAttGroupActionCreator,
  staffShiftActions,
} from '@manyun/dc-brain.legacy.redux/actions/staffShiftActions';
import * as generateUrls from '@manyun/dc-brain.legacy.utils/urls';

import TeamModal from './components/team-modal';

class AttGroupList extends Component {
  componentWillUnmount() {
    const { updatePagination, updateSearchValues } = this.props;
    /**清空store内的数据 */
    updateSearchValues({});
    updatePagination({ pageNum: 1, pageSize: 10 });
  }
  componentDidMount() {
    const { getData, updatePagination, updateSearchValues } = this.props;
    const { pageSize, pageNum, attGroupName, dutyGroupName } = getLocationSearchMap(
      window.location.search,
      {
        parseNumbers: true,
      }
    );
    updateSearchValues({ attGroupName, dutyGroupName });
    updatePagination({ pageSize: pageSize ?? 10, pageNum: pageNum ?? 1 });
    getData();
  }

  onChangeValues = ({ target: { value } }) => {
    this.props.updateSearchValues({ attGroupName: value.trim() });
  };

  onChangeDutyGroupValues = ({ target: { value } }) => {
    this.props.updateSearchValues({ dutyGroupName: value.trim() });
  };

  onChangePage = (pageNum, pageSize) => {
    this.props.updatePagination({ pageNum, pageSize });
    this.props.getData(false);
    this.onChangeUrlSearch();
  };

  onChangeUrlSearch = () => {
    const { pagination, searchValues } = this.props;
    setLocationSearch({
      ...pagination,
      attGroupName: searchValues.attGroupName ?? undefined,
      dutyGroupName: searchValues.dutyGroupName ?? undefined,
    });
  };

  render() {
    const { pagination, loading, data, total, getData, searchValues } = this.props;

    return (
      <TinyCard>
        <TinyTable
          rowKey="id"
          dataSource={data}
          loading={loading}
          columns={columns(this)}
          scroll={{ x: 'max-content' }}
          align="left"
          pagination={{
            total: total,
            current: pagination.pageNum,
            pageSize: pagination.pageSize,
            onChange: this.onChangePage,
          }}
          actionsWrapperStyle={{ justifyContent: 'space-between' }}
          actions={[
            <Button
              key="create-shift"
              type="primary"
              onClick={() => this.props.redirect(urls.ATT_GROUP_MANAGEMENT_CREATE)}
            >
              新建考勤组
            </Button>,
            <Space key="search" size="middle">
              <Input.Search
                style={{ width: 211, height: 32 }}
                placeholder="请输入班组名称"
                allowClear
                value={searchValues.dutyGroupName}
                onSearch={() => {
                  this.onChangeUrlSearch();
                  getData();
                }}
                onChange={this.onChangeDutyGroupValues}
              />
              <Input.Search
                style={{ width: 211, height: 32 }}
                allowClear
                placeholder="请输入考勤组名称"
                value={searchValues.attGroupName}
                onSearch={() => {
                  this.onChangeUrlSearch();
                  getData();
                }}
                onChange={this.onChangeValues}
              />
            </Space>,
          ]}
        />
      </TinyCard>
    );
  }
}

const mapStateToProps = ({
  staffShift: {
    attendanceGroup: { pagination, loading, data, total, searchValues },
  },
}) => {
  return {
    pagination,
    loading,
    data,
    total,
    searchValues,
  };
};
const mapDispatchToProps = {
  getData: getAttGroupActionCreator,
  setAttGroupDataAndTotal: staffShiftActions.setAttGroupDataAndTotal,
  updateSearchValues: staffShiftActions.updateAttGroupSearchValues,
  updatePagination: staffShiftActions.updateAttGroupPagination,
  delete: deleteAttGroupActionCreator,
  updateEditCreateOption: staffShiftActions.updateDutyGroupEditCreateOption,
  redirect: redirectActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(AttGroupList);

const columns = ctx => [
  {
    title: '考勤组名称',
    dataIndex: 'attName',
    fixed: 'left',
    render: (text, record) => (
      <Link
        type="link"
        to={generateUrls.generateEditAttGroupConfigUrl({
          id: record.id,
          attName: record.attName,
        })}
      >
        {text}
      </Link>
    ),
  },
  {
    title: '机房',
    dataIndex: 'idcTag',
  },
  {
    title: '楼栋',
    dataIndex: 'blockTag',
    render: blockTag => {
      if (!blockTag) {
        return '';
      }
      const tags = blockTag.split('.');
      return tags[1];
    },
  },
  {
    title: '班组数量',
    dataIndex: 'dutyGroupList',
    render: (_, record) => (
      <TeamModal text={record.dutyGroupList?.length} type="link" record={record} />
    ),
  },
  {
    title: '班制名称',
    dataIndex: 'shifts',
    render: (_, record) => {
      return record.shifts.shiftsName;
    },
  },
  {
    title: '班次时间',
    dataIndex: ['shifts', 'dutys'],
    render: (_, record) => {
      if (record.shifts && record.shifts.dutys) {
        const timeArr = record.shifts.dutys;
        const timeTxt = timeArr.map(item => {
          if (!item) {
            return null;
          }
          const { offIsNextDay, dutyName, onDutyTime, offDutyTime } = item;
          if (!offIsNextDay) {
            return `${dutyName}：${onDutyTime}-${offDutyTime}`;
          }
          return `${dutyName}：${onDutyTime}-次日${offDutyTime}`;
        });
        const txt = timeTxt.join('，');
        return txt;
      }
      return null;
    },
  },
  {
    title: '修改时间',
    dataIndex: 'gmtModified',
    dataType: 'dateTime',
    render: gmtModified => {
      return moment(gmtModified).format('YYYY-MM-DD HH:mm:ss');
    },
  },
  {
    title: '修改人',
    dataIndex: 'modifierId',
    render: (_, record) => <UserLink userId={record.modifierId} userName={record.modifierName} />,
  },

  {
    title: '操作',
    key: '__actions',
    fixed: 'right',
    render: (__, record) => (
      <span>
        <Link
          type="link"
          to={generateUrls.generateEditAttGroupConfigUrl({
            id: record.id,
            attName: record.attName,
          })}
        >
          编辑
        </Link>
        <Divider type="vertical" />
        <Link
          type="link"
          to={generateUrls.generateAttGroupScheduleDetailLocation({
            id: record.id,
            groupName: record.attName,
          })}
        >
          排班
        </Link>
        <Divider type="vertical" />
        <DeleteConfirm
          variant="popconfirm"
          targetName={record.attName}
          onOk={() => ctx.props.delete({ id: record.id })}
        >
          <Button compact type="link">
            删除
          </Button>
        </DeleteConfirm>
      </span>
    ),
  },
];
