import React, { Component } from 'react';
import { DndProvider } from 'react-dnd';
import Backend from 'react-dnd-html5-backend';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import { ApiSelect } from '@galiojs/awesome-antd';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';

import { TinyTable } from '@manyun/dc-brain.legacy.components';
import { staffShiftActions } from '@manyun/dc-brain.legacy.redux/actions/staffShiftActions';
import { staffShiftService } from '@manyun/dc-brain.legacy.services';

import { DragableBodyRow } from '../../../components/drag-table';

class SelectDutyGroupModal extends Component {
  state = {
    tableData: [],
    selectedIds: [],
  };

  components = {
    body: {
      row: DragableBodyRow,
    },
  };

  componentDidUpdate(prevProps) {
    if (!prevProps.selectDutyGroupVisible && this.props.selectDutyGroupVisible) {
      const { formOptions } = this.props;
      this.setState({
        tableData: formOptions.dutyGroupIdList.table,
        selectedIds: formOptions.dutyGroupIdList.value,
      });
    }
  }

  delete = currentId => {
    const { tableData, selectedIds } = this.state;
    const newTable = tableData.filter(({ id }) => id !== currentId);
    const newIds = selectedIds.filter(id => id !== currentId);
    this.setState({
      tableData: newTable,
      selectedIds: newIds,
    });
  };

  moveRow = (dragIndex, hoverIndex) => {
    const { tableData } = this.state;
    const arr = Array.from(tableData);
    const [remove] = arr.splice(dragIndex, 1);
    arr.splice(hoverIndex, 0, remove);
    this.setState({
      tableData: arr,
      selectedIds: arr.map(({ id }) => id),
    });
  };

  render() {
    const { selectDutyGroupVisible, mode, formOptions, needOrderDutyGroup, periodDays } =
      this.props;
    const { tableData, selectedIds } = this.state;
    return (
      <Modal
        width={794}
        destroyOnClose
        visible={selectDutyGroupVisible}
        onOk={() => {
          if (
            needOrderDutyGroup &&
            selectedIds.length !== Number(periodDays) &&
            selectedIds.length !== 1
          ) {
            message.error(`请选择1个或${periodDays}个考勤班组!`);
          } else if (!needOrderDutyGroup && !selectedIds.length) {
            message.error(`请至少选择1个考勤班组!`);
          } else {
            if (mode === 'new') {
              this.props.setSelectedIds({ selectedIds, tableData });
            } else {
              this.props.setSelectedIdsInEdit({ selectedIds, tableData });
            }
            this.props.onSelectRoleVisibleChange(false);
          }
        }}
        onCancel={() => this.props.onSelectRoleVisibleChange(false)}
      >
        <DndProvider backend={Backend}>
          <TinyTable
            rowKey="id"
            columns={getColumns(this)}
            size="small"
            dataSource={tableData}
            components={this.components}
            onRow={(record, index) => ({
              index,
              moveRow: this.moveRow,
            })}
            actions={[
              <Form.Item
                key="seach"
                label="参与考勤班组"
                style={{ marginBottom: '0', width: '100%' }}
                labelCol={{ xl: 3 }}
                wrapperCol={{ xl: 21 }}
              >
                <ApiSelect
                  style={{ width: 200 }}
                  showArrow={false}
                  showSearch
                  optionWithValue
                  fieldNames={{ label: 'groupName', value: 'id' }}
                  value="" // 保持 value 为空字符串，选择过的值显示在表格中，可以在表格中执行删除操作，ApiSelect只执行添加操作
                  disabledOptionValues={selectedIds}
                  dataService={async () => {
                    let params = {
                      idcTag: formOptions.idcBlockTag.value[0],
                      blockTag: formOptions.idcBlockTag.value[1],
                    };
                    if (mode === 'edit') {
                      params = {
                        ...params,
                        attGroupId: formOptions.id,
                      };
                    }
                    const { response } = await staffShiftService.fetchUnlinkedDutyGroups(params);
                    if (response) {
                      return Promise.resolve(response.data);
                    } else {
                      return Promise.resolve([]);
                    }
                  }}
                  serviceQueries={formOptions.idcBlockTag.value[1]}
                  onChange={(id, option) => {
                    const newData = [...tableData, option];
                    const newId = [...selectedIds, id];
                    this.setState({
                      tableData: newData,
                      selectedIds: newId,
                    });
                  }}
                />
              </Form.Item>,
            ]}
          />
        </DndProvider>
      </Modal>
    );
  }
}

const mapStateToProps = (
  {
    staffShift: {
      attendanceGroup: { create, edit },
    },
  },
  { mode }
) => {
  let formOptions = create;
  if (mode === 'edit') {
    formOptions = edit;
  }
  return {
    formOptions,
  };
};
const mapDispatchToProps = {
  setSelectedIds: staffShiftActions.setSelectedDutyGroupIds,
  setSelectedRoleIdsInEdit: staffShiftActions.setSelectedRoleIdsInEdit,
  setSelectedIdsInEdit: staffShiftActions.setSelectedIdsInEdit,
};

export default connect(mapStateToProps, mapDispatchToProps)(SelectDutyGroupModal);

const getColumns = ctx => [
  {
    title: '序号',
    key: 'index',
    render: (_, record, index) => {
      return index + 1;
    },
  },
  {
    title: '班组名称',
    dataIndex: 'groupName',
  },
  {
    title: '机房',
    dataIndex: 'idcTag',
  },
  {
    title: '楼栋',
    dataIndex: 'blockTag',
    render: (__, record) => {
      if (record.blockTag) {
        const [, block] = record.blockTag.split('.');
        return block;
      } else {
        return null;
      }
    },
  },
  {
    title: '操作',
    key: '_actions',
    render: (__, record) => (
      <Button
        type="link"
        style={{ padding: 0, height: 'auto' }}
        onClick={() => ctx.delete(record.id)}
      >
        删除
      </Button>
    ),
  },
];
