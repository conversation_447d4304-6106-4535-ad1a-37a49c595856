import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import QuestionCircleOutlined from '@ant-design/icons/es/icons/QuestionCircleOutlined';
import { ApiSelect } from '@galiojs/awesome-antd';

import { Button } from '@manyun/base-ui.ui.button';
import { Cascader } from '@manyun/base-ui.ui.cascader';
import { Input } from '@manyun/base-ui.ui.input';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';

import { useSpaces } from '@manyun/resource-hub.ui.location-tree-select';

import { FooterToolBar, GutterWrapper, TinyCard } from '@manyun/dc-brain.legacy.components';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  createAttGroupActionCreator,
  editAttGroupActionCreator,
  getAttGroupDetailActionCreator,
  staffShiftActions,
} from '@manyun/dc-brain.legacy.redux/actions/staffShiftActions';
import { staffShiftService } from '@manyun/dc-brain.legacy.services';

import ClockRadio from '../components/clock-radio';
import EhanceTimeSelect from '../schedule/components/time-select';
import SelectDutyGroupModal from './components/select-group-modal';

const formItemLayout = {
  labelCol: {
    xl: { span: 3 },
    xxl: { span: 2 },
  },
  wrapperCol: {
    xl: { span: 21 },
    xxl: { span: 22 },
  },
};

class CreateAttGroupForm extends Component {
  state = {
    userTableData: [],
    pageNum: 1,
    pageSize: 10,
    selectDutyGroupVisible: false,
    exitUserGroupIds: [],
  };

  componentDidMount() {
    this.props.syncCommonData({ strategy: { space: 'FORCED' } });
    if (this.props.mode === 'edit') {
      this.props.getAttGroupDetail({ attGroupId: this.props.match.params.id });
    }
  }

  finish = event => {
    event.preventDefault();
    this.props.form.validateFieldsAndScroll(async (err, values) => {
      if (err) {
        return;
      }
      const { mode } = this.props;
      if (mode === 'new') {
        this.props.createAttGroup();
      } else {
        const { dutyGroupIdList, defaultDutyGroupIdList } = this.props.formOptions;
        if (
          JSON.stringify(dutyGroupIdList.value) !== JSON.stringify(defaultDutyGroupIdList.value)
        ) {
          this.props.updateTimeSelectVisible(true);
        } else {
          this.props.editAttGroup();
        }
      }
    });
  };

  getDutyGrouDp = (dutyGroupIdList, needOrderDutyGroup, periodDays) => {
    if (dutyGroupIdList.value.length) {
      return `已选择${dutyGroupIdList.value.length}个考勤班组`;
    } else if (needOrderDutyGroup) {
      return `请选择1个或${periodDays}个考勤班组`;
    } else {
      return `请至少选择1个考勤班组`;
    }
  };

  // updateUserGroups = value => {
  //   const { mode, updateCreateOptions, updateEditOptions } = this.props;
  //   const fields = { userGroupIds: { value } };
  //   if (mode === 'edit') {
  //     updateEditOptions(fields);
  //   } else {
  //     updateCreateOptions(fields);
  //   }
  // };

  render() {
    const {
      mode,
      form: { getFieldDecorator, resetFields },
      formOptions: { shifts, idcBlockTag, dutyGroupIdList, attName },
      idcAndBlockData,
    } = this.props;
    const { selectDutyGroupVisible } = this.state;

    // const blockTag = idcBlockTag.value[1];
    // const searchGroupsparams = { blockTag };

    return (
      <>
        <TinyCard bordered={false} title="基本信息">
          <Form {...formItemLayout} colon={false}>
            <Form.Item label="考勤组名称">
              {getFieldDecorator('attName', {
                rules: [
                  { required: true, message: '考勤组名称必填' },
                  {
                    type: 'string',
                    max: 20,
                    message: '最多输入 20 个字符！',
                  },
                ],
              })(<Input style={{ width: 340 }} allowClear />)}
            </Form.Item>
            <Form.Item label="所属机房/楼栋">
              {getFieldDecorator('idcBlockTag', {
                rules: [{ required: true, message: '所属机房/楼栋必选' }],
              })(
                <SpacesCascader
                  style={{ width: 200 }}
                  allowClear={false}
                  options={idcAndBlockData}
                />
              )}
            </Form.Item>
            <Form.Item label="考勤班制">
              {getFieldDecorator('shifts', {
                rules: [{ required: true, message: '考勤班制必选' }],
                getValueFromEvent: (id, option) => option,
              })(
                <ApiSelect
                  style={{ width: 200 }}
                  showArrow={false}
                  trigger="onDidMount"
                  showSearch
                  optionWithValue
                  fieldNames={{ label: 'shiftsName', value: 'id' }}
                  dataService={async () => {
                    const { response } = await staffShiftService.fetchShiftsList();
                    if (response) {
                      return Promise.resolve(response.data);
                    } else {
                      return Promise.resolve([]);
                    }
                  }}
                />
              )}
            </Form.Item>
            {idcBlockTag.value[0] && shifts.value && (
              <Form.Item
                label={
                  <span>
                    参与考勤班组&nbsp;
                    <Tooltip title={<span>对应班组人员添加完成，将自动添加进考勤班组</span>}>
                      <QuestionCircleOutlined />
                    </Tooltip>
                  </span>
                }
              >
                {getFieldDecorator('dutyGroupIdList', {
                  rules: [
                    {
                      required: true,
                      validator: (_, value, callback) => {
                        if (!dutyGroupIdList || dutyGroupIdList?.value.length === 0) {
                          callback('参与考勤班组必选');
                        } else if (
                          shifts.value.needOrderDutyGroup &&
                          value.length !== shifts.value.periodDays &&
                          value.length !== 1
                        ) {
                          callback(`请选择1个或${shifts.value.periodDays}个考勤班组!`);
                        } else if (!shifts.value.needOrderDutyGroup && !value.length) {
                          callback('至少选择1个考勤班组');
                        } else {
                          callback();
                        }
                      },
                    },
                  ],
                })(
                  <Button
                    type="link"
                    onClick={() => this.setState({ selectDutyGroupVisible: true })}
                  >
                    {this.getDutyGrouDp(
                      dutyGroupIdList,
                      shifts.value.needOrderDutyGroup,
                      shifts.value.periodDays
                    )}
                  </Button>
                )}
              </Form.Item>
            )}

            {/* {idcBlockTag.value[1] && (
              <Form.Item
                label="考勤组负责组"
                rules={[{ required: true, message: '考勤组负责组必选' }]}
              >
                <ApiSelect
                  style={{ width: 340 }}
                  trigger="onDidMount"
                  showArrow={false}
                  showSearch
                  optionWithValue
                  mode="multiple"
                  value={(userGroupIds.value ?? []).filter(v => exitUserGroupIds.includes(v))}
                  onChange={value => {
                    // console.log('value', value);
                    this.updateUserGroups(value);
                  }}
                  fieldNames={{ label: 'groupName', value: 'id' }}
                  dataService={async () => {
                    const { response } = await staffShiftService.fetchGroupsByResourceCode(
                      searchGroupsparams
                    );
                    if (response) {
                      this.setState({ exitUserGroupIds: (response.data ?? []).map(d => d.id) });
                      return Promise.resolve(response.data);
                    } else {
                      return Promise.resolve([]);
                    }
                  }}
                  serviceQueries={[blockTag]}
                />
              </Form.Item>
            )} */}
            <Form.Item label="考勤规则">
              {getFieldDecorator('attRule', {
                rules: [{ required: true, message: '考勤规则必选' }],
              })(
                <ApiSelect
                  style={{ width: 200 }}
                  showArrow={false}
                  trigger="onDidMount"
                  showSearch
                  optionWithValue
                  fieldNames={{ label: 'ruleName', value: 'id' }}
                  dataService={async () => {
                    const { response } = await staffShiftService.fetchAttRuleList();
                    if (response) {
                      return Promise.resolve(response.data);
                    } else {
                      return Promise.resolve([]);
                    }
                  }}
                />
              )}
            </Form.Item>

            <Form.Item label="打卡方式">
              {getFieldDecorator('checkChannels', {
                rules: [{ required: true, message: '打卡方式必选' }],
              })(<ClockRadio idcBlockTag={idcBlockTag} resetFields={resetFields} />)}
            </Form.Item>

            <SelectDutyGroupModal
              mode={mode}
              needOrderDutyGroup={shifts.value ? shifts.value.needOrderDutyGroup : false}
              periodDays={shifts.value ? shifts.value.periodDays : 1}
              selectDutyGroupVisible={selectDutyGroupVisible}
              onSelectRoleVisibleChange={value => this.setState({ selectDutyGroupVisible: value })}
            />
          </Form>
        </TinyCard>
        <FooterToolBar>
          <GutterWrapper>
            <Button onClick={() => this.props.history.goBack()}>取消</Button>

            {mode === 'new' && (
              <Button type="primary" onClick={this.finish}>
                保存设置,开始排班
              </Button>
            )}
            {mode === 'edit' && (
              <Button type="primary" onClick={this.finish}>
                保存设置
              </Button>
            )}
          </GutterWrapper>
        </FooterToolBar>
        {mode === 'edit' && (
          <EhanceTimeSelect
            attGroupId={this.props.match.params.id}
            type="attendance"
            groupName={attName.value}
            mode="scheduleEdit"
            enableStatutoryHoliday={mode === 'edit' && shifts?.value?.enableStatutoryHoliday}
          />
        )}
      </>
    );
  }
}

const createOpts = {
  onFieldsChange(props, changedFields) {
    let changes = changedFields;
    if (changedFields.shifts) {
      // 编辑时 如果不改变任何值直接提交，这时 shifts.value 会是班制的id,所以要判断 changedFields.shifts.value 类型
      if (typeof changedFields.shifts.value === 'object') {
        changes = {
          ...changedFields,
          shifts: {
            ...changedFields.shifts,
            dirty: false,
            validating: false,
          },
        };
      } else {
        changes = {
          ...changedFields,
          shifts: {
            ...changedFields.shifts,
            dirty: false,
            validating: false,
            value: props.formOptions.shifts.value,
          },
        };
      }
    }
    if (
      changedFields.idcBlockTag &&
      changedFields.idcBlockTag.value !== props.formOptions.idcBlockTag.value
    ) {
      changes = {
        ...changedFields,
        dutyGroupIdList: {
          value: [],
          table: [],
        },
        userGroupIds: {
          value: [],
        },
      };
    }
    if (props.mode === 'new') {
      props.updateCreateOptions(changes);
    } else {
      props.updateEditOptions(changes);
    }
  },
  mapPropsToFields(props) {
    return {
      attName: Form.createFormField(props.formOptions.attName),
      idcBlockTag: Form.createFormField(props.formOptions.idcBlockTag),
      dutyGroupList: Form.createFormField(props.formOptions.dutyGroupList),
      userGroupIds: Form.createFormField(props.formOptions.userGroupIds),
      attRule: Form.createFormField(props.formOptions.attRule),
      checkChannels: Form.createFormField(props.formOptions.checkChannels),
      dutyGroupIdList: Form.createFormField({
        ...props.formOptions.dutyGroupIdList,
        errors:
          props.formOptions.dutyGroupIdList.value.length > 0
            ? undefined
            : props.formOptions.dutyGroupIdList.errors,
      }),
      shifts: Form.createFormField(
        props.formOptions.shifts && props.formOptions.shifts.value
          ? { ...props.formOptions.shifts, value: props.formOptions.shifts.value.id }
          : props.formOptions.shifts
      ),
    };
  },
};

const EhanceAttGroupCreate = Form.create(createOpts)(CreateAttGroupForm);

const mapStateToProps = (
  {
    staffShift: {
      attendanceGroup: { create, edit },
    },
  },
  { mode }
) => {
  let formOptions = create;
  if (mode === 'edit') {
    formOptions = edit;
  }
  return {
    formOptions,
  };
};
const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
  updateCreateOptions: staffShiftActions.updateAttGroupCreateOptions,
  createAttGroup: createAttGroupActionCreator,
  getAttGroupDetail: getAttGroupDetailActionCreator,
  editAttGroup: editAttGroupActionCreator,
  updateEditOptions: staffShiftActions.updateEditOptions,
  updateTimeSelectVisible: staffShiftActions.updateTimeSelectVisible,
};
export default connect(mapStateToProps, mapDispatchToProps)(EhanceAttGroupCreate);

const SpacesCascader = React.forwardRef(({ ...props }, ref) => {
  const [{ treeSpaces }] = useSpaces({
    nodeTypes: ['IDC', 'BLOCK'],
    idc: false,
    authorizedOnly: true,
    disabledTypes: [],
    includeVirtualBlocks: false,
    nodeMutator: node => {
      const names = node.label.split('.');
      return {
        ...node,
        label: names[names.length - 1],
        value: node.value,
      };
    },
  });
  return <Cascader ref={ref} {...props} options={treeSpaces} />;
});

SpacesCascader.displayName = 'SpacesCascader';
