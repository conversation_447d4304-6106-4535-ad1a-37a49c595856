import React, { Component } from 'react';
import { connect } from 'react-redux';

import moment from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Input } from '@manyun/base-ui.ui.input';

import { TinyCard, TinyTable, UserLink } from '@manyun/dc-brain.legacy.components';
import {
  deleteGroupScheduleActionCreator,
  getGroupScheduleActionCreator,
  staffShiftActions,
} from '@manyun/dc-brain.legacy.redux/actions/staffShiftActions';

import CreateGroupScheduleModal from './components/create-modal';

class GroupSchedule extends Component {
  state = {
    mode: 'new',
  };

  componentWillUnmount() {
    const { updatePagination, updateSearchValues } = this.props;
    updateSearchValues({});
    updatePagination({ pageNum: 1, pageSize: 10 });
  }

  componentDidMount() {
    this.props.getData();
  }

  onChangeValues = ({ target: { value } }) => {
    this.props.updateSearchValues({ ruleName: value.trim() });
  };

  onChangePage = (pageNum, pageSize) => {
    this.props.updatePagination({ pageNum, pageSize });
    this.props.getData(false);
  };

  // getHourMinute = (enableTimeInterval, time) => {
  //   if (!enableTimeInterval) {
  //     return [null, null];
  //   }
  //   if (time === 0) {
  //     return [0, 0];
  //   }
  //   const [merchant] = String(time / 60).split('.');
  //   const surplus = time % 60;
  //   return [Number(merchant) * 60, surplus];
  // };

  edit = record => {
    this.setState({ mode: 'edit' });
    const infos = {
      id: { value: record.id },
      ruleName: {
        value: record.ruleName,
      },
      commutingTimeInterval: {
        // value: this.getHourMinute(record.enableTimeInterval, record.commutingTimeInterval),
        value: [record.commutingTimeInterval],
      },
      exchangeRange: {
        value: record.exchangeRange,
      },
      allowSameShiftsExchange: {
        value: record.allowSameShiftsExchange,
      },
      allowSameRoleExchange: {
        value: record.allowSameRoleExchange,
      },
      replaceRange: {
        value: record.replaceRange,
      },
      allowRestReplace: {
        value: record.allowRestReplace,
      },
      priorityShifts: {
        value: record.priorityShifts ? record.priorityShifts.id : null,
      },
      priorityRoles: {
        value: record.priorityRoles.map(({ id }) => id),
      },
      priorityRolesTable: record.priorityRoles,
      enableSupply: {
        value: record.enableSupply,
      },
      enableSupplyCount: {
        value: record.enableSupplyCount,
      },
      supplyCountByMonth: {
        value: record.supplyCountByMonth,
      },
      enableSupplyTime: {
        value: record.enableSupplyTime,
      },
      supplyValidDays: {
        value: record.supplyValidDays,
      },
      /**加班可提前申请天数 */
      overtimeBeforeDays: {
        value: record.overtimeBeforeDays ?? undefined,
      },
      /**请假可提前天数 */
      leaveBeforeDays: {
        value: record.leaveBeforeDays ?? undefined,
      },
      /**外勤可提前天数 */
      outWorkBeforeDays: {
        value: record.outWorkBeforeDays ?? undefined,
      },
      /**单次外勤最多可请时长 */
      outWorkTotalHours: {
        value: record.outWorkTotalHours ?? undefined,
      },
      combineLeaves: {
        value: record.combineLeaves ?? [],
      },
      leaveNotValidStartDay: {
        value: record.leaveNotValidStartDay ?? undefined,
      },
      overtimeNotValidStartDay: {
        value: record.overtimeNotValidStartDay ?? undefined,
      },
      overtimeConfirmValidDays: {
        value: record.overtimeConfirmValidDays ?? undefined,
      },
      overtimeConfirmNotValidStartDay: {
        value: record.overtimeConfirmNotValidStartDay ?? undefined,
      },
      outWorkNotValidStartDay: {
        value: record.outWorkNotValidStartDay ?? undefined,
      },
      supplyNotValidStartDay: {
        value: record.supplyNotValidStartDay ?? undefined,
      },
    };
    this.props.editCreateOption(infos);
    this.props.updateGroupScheduleVisiable(true);
  };

  render() {
    const { pagination, loading, data, total, getData } = this.props;
    const { mode } = this.state;
    return (
      <TinyCard>
        <TinyTable
          rowKey="id"
          dataSource={data}
          loading={loading}
          columns={columns(this)}
          align="left"
          scroll={{ x: 'max-content' }}
          pagination={{
            total: total,
            current: pagination.pageNum,
            pageSize: pagination.pageSize,
            onChange: this.onChangePage,
          }}
          actionsWrapperStyle={{ justifyContent: 'space-between' }}
          actions={[
            <Button
              key="create-shift"
              type="primary"
              onClick={() => {
                this.setState({
                  mode: 'new',
                });
                this.props.updateGroupScheduleVisiable(true);
              }}
            >
              新建排班规则
            </Button>,
            <Input.Search
              key="seach"
              allowClear
              placeholder="请输入排班规则名称"
              style={{ width: 211, height: 32 }}
              onSearch={getData}
              onChange={this.onChangeValues}
            />,
          ]}
        />
        <CreateGroupScheduleModal mode={mode} />
      </TinyCard>
    );
  }
}

const mapStateToProps = ({
  staffShift: {
    groupSchedule: { pagination, loading, data, total },
  },
}) => {
  return {
    pagination,
    loading,
    data,
    total,
  };
};
const mapDispatchToProps = {
  getData: getGroupScheduleActionCreator,
  setGroupScheduleDataAndTotal: staffShiftActions.setGroupScheduleDataAndTotal,
  updateSearchValues: staffShiftActions.updateGroupScheduleSearchValues,
  updatePagination: staffShiftActions.updateGroupSchedulePagination,
  delete: deleteGroupScheduleActionCreator,
  updateGroupScheduleVisiable: staffShiftActions.updateGroupScheduleVisiable,
  editCreateOption: staffShiftActions.editGroupScheduleEditOption,
};
export default connect(mapStateToProps, mapDispatchToProps)(GroupSchedule);

const columns = ctx => [
  {
    title: '排班规则名称',
    dataIndex: 'ruleName',
    render: (text, record) => (
      <Button compact type="link" onClick={() => ctx.edit(record)}>
        {text}
      </Button>
    ),
  },
  {
    title: '修改时间',
    dataType: 'dateTime',
    dataIndex: 'gmtModified',
    render: gmtModified => {
      return moment(gmtModified).format('YYYY-MM-DD HH:mm:ss');
    },
  },
  {
    title: '修改人',
    dataIndex: 'modifierId',
    render: (_, record) => <UserLink userId={record.modifierId} userName={record.modifierName} />,
  },
  {
    title: '操作',
    key: '__actions',
    fixed: 'right',
    render: (__, record) => (
      <span>
        <Button compact type="link" onClick={() => ctx.edit(record)}>
          编辑
        </Button>
        <Divider type="vertical" />
        <DeleteConfirm
          variant="popconfirm"
          targetName={record.ruleName}
          onOk={() => ctx.props.delete({ id: record.id })}
        >
          <Button compact type="link">
            删除
          </Button>
        </DeleteConfirm>
      </span>
    ),
  },
];
