import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import { ApiSelect, Select } from '@galiojs/awesome-antd';

import { Button } from '@manyun/base-ui.ui.button';
import { Cascader } from '@manyun/base-ui.ui.cascader';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Explanation } from '@manyun/base-ui.ui.explanation';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { Space } from '@manyun/base-ui.ui.space';

import { TinyCard } from '@manyun/dc-brain.legacy.components';
import { staffShiftActions } from '@manyun/dc-brain.legacy.redux/actions/staffShiftActions';
import { staffShiftService } from '@manyun/dc-brain.legacy.services';

import { SHIFT_CHANGES_SCOPE_TYPE_OPTIONS, TIME_INTERVAL } from '../constants';
import SelectRoleModal from './select-role-modal';

const formLayout = { labelCol: { span: 4 }, wrapperCol: { span: 20 } };

class CreateGroupScheduleForm extends Component {
  state = {
    selectRoleVisible: false,
  };

  onSelectRoleVisibleChange = visible => {
    this.setState({ selectRoleVisible: visible });
  };

  render() {
    const {
      mode,
      form: { getFieldDecorator },
      formOptions: {
        allowSameShiftsExchange,
        allowSameRoleExchange,
        allowRestReplace,
        enableSupply,
        priorityRoles,
        priorityRolesTable,
        combineLeaves,
      },
    } = this.props;
    const { selectRoleVisible } = this.state;

    return (
      <Form wrapperCol={{ span: 12 }} {...formLayout} style={{ width: '920px' }}>
        <TinyCard
          bordered={false}
          title="基本信息"
          headStyle={{ border: 0, padding: 0 }}
          bodyStyle={{ padding: 0 }}
        >
          <Form.Item label="规则名称" style={{ marginBottom: '0' }}>
            {getFieldDecorator('ruleName', {
              rules: [
                { required: true, message: '规则名称必填' },
                {
                  type: 'string',
                  max: 20,
                  message: '最多输入 20 个字符！',
                },
              ],
            })(<Input style={{ width: 340 }} allowClear />)}
          </Form.Item>
        </TinyCard>
        <TinyCard
          bordered={false}
          title="换班规则"
          headStyle={{ border: 0, padding: 0 }}
          bodyStyle={{ padding: 0 }}
        >
          <Space style={{ width: '100%', paddingLeft: 84 }} size="middle">
            <Form.Item label="换班范围" style={{ marginBottom: '0' }} labelCol={6} wrapperCol={10}>
              {getFieldDecorator('exchangeRange')(
                <Select
                  style={{ width: 132 }}
                  getPopupContainer={trigger => trigger.parentNode.parentNode}
                >
                  {SHIFT_CHANGES_SCOPE_TYPE_OPTIONS.map(({ value, label }) => {
                    return (
                      <Select.Option key={value} value={value}>
                        {label}
                      </Select.Option>
                    );
                  })}
                </Select>
              )}
            </Form.Item>
            <Form.Item style={{ marginBottom: '0' }} wrapperCol={24}>
              {getFieldDecorator('allowSameShiftsExchange')(
                <Checkbox checked={allowSameShiftsExchange.value}>只允许同班制内换班</Checkbox>
              )}
            </Form.Item>
            <Form.Item style={{ marginBottom: '0' }} wrapperCol={24}>
              {getFieldDecorator('allowSameRoleExchange')(
                <Checkbox checked={allowSameRoleExchange.value}>只允许同角色内换班</Checkbox>
              )}
            </Form.Item>
          </Space>
        </TinyCard>
        <TinyCard
          bordered={false}
          title="请假规则"
          headStyle={{ border: 0, padding: 0 }}
          bodyStyle={{ padding: 0 }}
        >
          <Space style={{ width: '100%', paddingLeft: 17 }} size={0} align="center">
            <Form.Item
              label="限制后补请假时间"
              // style={{ marginBottom: '0' }}
              labelCol={6}
              wrapperCol={18}
            >
              固定班制可申请过去 &nbsp;
              {getFieldDecorator('leaveBeforeDays', {
                rules: [{ required: true, message: '限制后补请假时间必填' }],
              })(
                <InputNumber
                  style={{ width: 60 }}
                  min={0}
                  max={180}
                  formatter={value => value}
                  parser={value => value.replace('.', '')}
                />
              )}
            </Form.Item>
            <Space style={{ marginBottom: 24, padding: '0 4px' }} align="center">
              <Explanation
                style={{ marginTop: 5 }}
                iconType="question"
                tooltip={{ title: '填写0天，则只能发起当天及未来的请假' }}
              />
              天内的请假，
            </Space>
            <Form.Item wrapperCol={24}>
              且当月&nbsp;
              {getFieldDecorator('leaveNotValidStartDay', {
                rules: [{ required: true, message: '限制后补请假日期必填' }],
              })(
                <InputNumber
                  style={{ width: 60 }}
                  min={1}
                  max={28}
                  formatter={value => value}
                  parser={value => value.replace('.', '')}
                />
              )}
              &nbsp;日起不可再后补上月请假
            </Form.Item>
          </Space>
          <Form.Item label="排班制支持组合假类型">
            {getFieldDecorator('combineLeaves')(
              <Checkbox.Group style={{ width: '100%' }}>
                <Checkbox
                  value="BREAK_OFF_COMBINED_LEAVE"
                  disabled={(combineLeaves?.value ?? []).includes('ANNUAL_COMBINED_LEAVE')}
                >
                  年假+调休
                </Checkbox>
                <Checkbox
                  value="ANNUAL_COMBINED_LEAVE"
                  disabled={(combineLeaves?.value ?? []).includes('BREAK_OFF_COMBINED_LEAVE')}
                >
                  年假+调休+事假
                </Checkbox>
                <Checkbox value="SICK_COMBINED_LEAVE">全薪病假+病假</Checkbox>
              </Checkbox.Group>
            )}
          </Form.Item>
          <Space style={{ width: '100%', paddingLeft: 84 }} size="middle">
            <Form.Item label="顶班范围" labelCol={6} wrapperCol={10}>
              {getFieldDecorator('replaceRange')(
                <Select
                  style={{ width: 132 }}
                  getPopupContainer={trigger => trigger.parentNode.parentNode}
                >
                  {SHIFT_CHANGES_SCOPE_TYPE_OPTIONS.map(({ value, label }) => {
                    return (
                      <Select.Option key={value} value={value}>
                        {label}
                      </Select.Option>
                    );
                  })}
                </Select>
              )}
            </Form.Item>
            <Form.Item wrapperCol={24}>
              {getFieldDecorator('allowRestReplace')(
                <Checkbox checked={allowRestReplace.value}>只允许休息时间顶班</Checkbox>
              )}
            </Form.Item>
          </Space>
          <Form.Item label="顶班优先选择班制">
            {getFieldDecorator('priorityShifts')(
              <ApiSelect
                style={{ width: 132 }}
                showSearch
                allowClear
                trigger="onDidMount"
                optionWithValue
                fieldNames={{ label: 'shiftsName', value: 'id' }}
                dataService={getShiftsDataService()}
                getPopupContainer={trigger => trigger.parentNode.parentNode}
              />
            )}
          </Form.Item>
          <Form.Item style={{ marginBottom: 0 }} label="顶班角色优先级设置">
            {getFieldDecorator('priorityRoles')(
              <Button
                type="link"
                compact
                onClick={() => {
                  this.setState({ selectRoleVisible: true });
                }}
              >
                设置
              </Button>
            )}
          </Form.Item>
        </TinyCard>
        <TinyCard
          title="加班规则"
          headStyle={{ border: 0, padding: 0 }}
          bodyStyle={{ padding: 0 }}
          bordered={false}
        >
          {/* <Form.Item style={{ marginBottom: 0 }} label="限制后补加班时间">
            可申请过去 &nbsp;
            {getFieldDecorator('overtimeBeforeDays', {
              rules: [{ required: true, message: '限制后补加班时间必填' }],
            })(
              <InputNumber
                style={{ width: 60 }}
                min={0}
                max={180}
                formatter={value => value}
                parser={value => value.replace('.', '')}
              />
            )}
            &nbsp;天内的加班（填写0天，则只能发起当天及未来的加班）
          </Form.Item> */}
          <Space style={{ width: '100%', paddingLeft: 32 }} size={0}>
            <Form.Item label="未提交加班申请" labelCol={6} wrapperCol={10}>
              可后补过去 &nbsp;
              {getFieldDecorator('overtimeBeforeDays', {
                rules: [{ required: true, message: '限制后补加班确认时间必填' }],
              })(
                <InputNumber
                  style={{ width: 60 }}
                  min={0}
                  max={180}
                  formatter={value => value}
                  parser={value => value.replace('.', '')}
                />
              )}
            </Form.Item>
            <Space style={{ marginBottom: 24, padding: '0 4px' }} align="center">
              <Explanation
                style={{ marginTop: 5 }}
                iconType="question"
                tooltip={{ title: '填写0天，则只能后补当天的加班确认' }}
              />
              天内的加班确认,
            </Space>
            <Form.Item wrapperCol={24}>
              且当月&nbsp;
              {getFieldDecorator('overtimeNotValidStartDay', {
                rules: [{ required: true, message: '限制后补加班确认日期必填' }],
              })(
                <InputNumber
                  style={{ width: 60 }}
                  min={1}
                  max={28}
                  formatter={value => value}
                  parser={value => value.replace('.', '')}
                />
              )}
              &nbsp;日起不可再后补上月加班确认
            </Form.Item>
          </Space>
          <Space style={{ width: '100%', paddingLeft: 32 }} size={0} align="center">
            <Form.Item label="已提交加班申请" labelCol={6} wrapperCol={10}>
              操作加班确认时，实际加班开始时间需在 &nbsp;
              {getFieldDecorator('overtimeConfirmValidDays', {
                rules: [{ required: true, message: '限制确认加班时间必填' }],
              })(
                <InputNumber
                  style={{ width: 60 }}
                  min={0}
                  max={180}
                  formatter={value => value}
                  parser={value => value.replace('.', '')}
                />
              )}
            </Form.Item>
            <Space style={{ marginBottom: 24, padding: '0 4px' }} align="center">
              <Explanation
                style={{ marginTop: 5 }}
                iconType="question"
                tooltip={{ title: '填写0天，则只能操作开始时间为当天的加班确认' }}
              />
              天内，
            </Space>
            <Form.Item wrapperCol={24}>
              且当月&nbsp;
              {getFieldDecorator('overtimeConfirmNotValidStartDay', {
                rules: [{ required: true, message: '限制确认加班日期必填' }],
              })(
                <InputNumber
                  style={{ width: 60 }}
                  min={1}
                  max={28}
                  formatter={value => value}
                  parser={value => value.replace('.', '')}
                />
              )}
              &nbsp;日起不可再操作上月的加班确认
            </Form.Item>
          </Space>
        </TinyCard>
        <TinyCard
          title="外勤规则"
          headStyle={{ border: 0, padding: 0 }}
          bodyStyle={{ padding: 0 }}
          bordered={false}
        >
          <Space style={{ width: '100%', paddingLeft: 20 }} size={0} align="center">
            <Form.Item label="限制后补外勤时间" labelCol={6} wrapperCol={18}>
              可申请过去 &nbsp;
              {getFieldDecorator('outWorkBeforeDays', {
                rules: [{ required: true, message: '限制后补外勤时间必填' }],
              })(
                <InputNumber
                  style={{ width: 60 }}
                  min={0}
                  max={180}
                  formatter={value => value}
                  parser={value => value.replace('.', '')}
                />
              )}
            </Form.Item>
            <Space style={{ marginBottom: 24, padding: '0 4px' }} align="center">
              <Explanation
                style={{ marginTop: 5 }}
                iconType="question"
                tooltip={{ title: '填写0天，则只能发起当天及未来的外勤' }}
              />
              天内的外勤,
            </Space>
            <Form.Item wrapperCol={24}>
              且当月&nbsp;
              {getFieldDecorator('outWorkNotValidStartDay', {
                rules: [{ required: true, message: '限制后补外勤日期必填' }],
              })(
                <InputNumber
                  style={{ width: 60 }}
                  min={1}
                  max={28}
                  formatter={value => value}
                  parser={value => value.replace('.', '')}
                />
              )}
              &nbsp;日起不可再后补上月外勤
            </Form.Item>
          </Space>
          <Space style={{ width: '100%', paddingLeft: 17 }} size={0} align="center">
            <Form.Item label="限制单次外勤时长" labelCol={6} wrapperCol={18}>
              单次最多可申请 &nbsp;
              {getFieldDecorator('outWorkTotalHours', {
                rules: [{ required: true, message: '限制单次外勤时长必填' }],
              })(
                <InputNumber
                  style={{ width: 60 }}
                  min={0}
                  max={240}
                  formatter={value => value}
                  parser={value => value.replace('.', '')}
                />
              )}
            </Form.Item>
            <Space style={{ marginBottom: 24, padding: '0 4px' }} align="center">
              <Explanation
                style={{ marginTop: 5 }}
                iconType="question"
                tooltip={{ title: '填写0小时，则不支持发起外勤申请' }}
              />
              小时的外勤
            </Space>
          </Space>
        </TinyCard>
        <TinyCard
          bordered={false}
          title="考勤规则"
          headStyle={{ border: 0, padding: 0 }}
          bodyStyle={{ padding: 0 }}
        >
          <Form.Item label="打卡规则">
            上班打卡后&nbsp;&nbsp;
            {getFieldDecorator('commutingTimeInterval', {
              rules: [{ required: true, message: '打卡规则必选' }],
            })(
              <Cascader
                options={TIME_INTERVAL}
                style={{ width: 125 }}
                getPopupContainer={trigger => trigger.parentNode.parentNode}
              />
            )}
            &nbsp;&nbsp;可打下班卡
          </Form.Item>

          <Form.Item label="补卡">
            {getFieldDecorator('enableSupply')(
              <Checkbox checked={enableSupply.value}>允许补卡（勾选后，员工可发起补卡）</Checkbox>
            )}
          </Form.Item>
          {enableSupply.value && (
            <>
              {/* <div style={{ display: 'flex' }}> */}
              {/* <Form.Item
                  style={{ marginBottom: '0', flex: 0.66, textAlign: 'right' }}
                  label="限制补卡次数"
                  {...formItemLayout}
                >
                  {getFieldDecorator('enableSupplyCount')(
                    <Checkbox checked={enableSupplyCount.value}>限制补卡次数: 每月可提交</Checkbox>
                  )}
                  
                </Form.Item> */}
              <Space style={{ width: '100%', paddingLeft: 154 }} size={0}>
                <Form.Item label="限制补卡次数" labelCol={6} wrapperCol={10}>
                  每月可提交 &nbsp;
                  {getFieldDecorator('supplyCountByMonth', {
                    rules: [{ required: true, message: '限制补卡次数必填' }],
                  })(
                    <InputNumber
                      min={1}
                      max={99}
                      style={{ width: 60 }}
                      formatter={value => value}
                      parser={value => value.replace('.', '')}
                    />
                  )}
                  &nbsp;次补卡（按自然月计算）
                </Form.Item>
              </Space>
              {/* <div style={{ display: 'flex' }}> */}
              {/* <Form.Item
                  style={{ marginBottom: '0', flex: 0.66, textAlign: 'right' }}
                  {...formItemLayout}
                >
                  {getFieldDecorator('enableSupplyTime')(
                    <Checkbox checked={enableSupplyTime.value}>限制补卡时间: 可申请过去</Checkbox>
                  )}
                </Form.Item> */}
              <Space style={{ width: '100%', paddingLeft: 154 }} size={0} align="center">
                <Form.Item label="限制补卡时间" labelCol={6} wrapperCol={10}>
                  可申请过去&nbsp;
                  {getFieldDecorator('supplyValidDays', {
                    rules: [{ required: true, message: '限制补卡时间必填' }],
                  })(
                    <InputNumber
                      min={0}
                      max={180}
                      style={{ width: 60 }}
                      formatter={value => value}
                      parser={value => value.replace('.', '')}
                    />
                  )}
                </Form.Item>
                <Space style={{ marginBottom: 24, padding: '0 4px' }} align="center">
                  <Explanation
                    style={{ marginTop: 5 }}
                    iconType="question"
                    tooltip={{ title: '填写0天，则只能发起当天的补卡' }}
                  />
                  天内的补卡,
                </Space>
                <Form.Item wrapperCol={24}>
                  且当月&nbsp;
                  {getFieldDecorator('supplyNotValidStartDay', {
                    rules: [{ required: true, message: '限制补卡日期必填' }],
                  })(
                    <InputNumber
                      style={{ width: 60 }}
                      min={1}
                      max={28}
                      formatter={value => value}
                      parser={value => value.replace('.', '')}
                    />
                  )}
                  &nbsp;日起不可再提交上月补卡
                </Form.Item>
              </Space>
            </>
          )}
        </TinyCard>
        <SelectRoleModal
          mode={mode}
          selectRoleVisible={selectRoleVisible}
          editRoleTable={priorityRolesTable}
          selectIds={priorityRoles.value}
          onSelectRoleVisibleChange={this.onSelectRoleVisibleChange}
        />
      </Form>
    );
  }
}

const createOpts = {
  onFieldsChange(props, changedFields) {
    let _changedFields = changedFields;
    if ('enableSupply' in changedFields) {
      _changedFields = {
        ...changedFields,
        enableSupplyCount: { value: _changedFields.enableSupply.value },
        enableSupplyTime: { value: _changedFields.enableSupply.value },
      };
    }
    if (props.mode === 'new') {
      props.updateCreateOptions(_changedFields);
    } else {
      props.updateEditOptions(_changedFields);
    }
  },
  mapPropsToFields(props) {
    return {
      ruleName: Form.createFormField(props.formOptions.ruleName),
      commutingTimeInterval: Form.createFormField(props.formOptions.commutingTimeInterval),
      exchangeRange: Form.createFormField(props.formOptions.exchangeRange),
      allowSameShiftsExchange: Form.createFormField(props.formOptions.allowSameShiftsExchange),
      allowSameRoleExchange: Form.createFormField(props.formOptions.allowSameRoleExchange),
      replaceRange: Form.createFormField(props.formOptions.replaceRange),
      allowRestReplace: Form.createFormField(props.formOptions.allowRestReplace),
      priorityShifts: Form.createFormField(props.formOptions.priorityShifts),
      enableSupply: Form.createFormField(props.formOptions.enableSupply),
      enableSupplyCount: Form.createFormField(props.formOptions.enableSupplyCount),
      supplyCountByMonth: Form.createFormField(props.formOptions.supplyCountByMonth),
      enableSupplyTime: Form.createFormField(props.formOptions.enableSupplyTime),
      supplyValidDays: Form.createFormField(props.formOptions.supplyValidDays),
      leaveBeforeDays: Form.createFormField(props.formOptions.leaveBeforeDays),
      overtimeBeforeDays: Form.createFormField(props.formOptions.overtimeBeforeDays),
      outWorkBeforeDays: Form.createFormField(props.formOptions.outWorkBeforeDays),
      outWorkTotalHours: Form.createFormField(props.formOptions.outWorkTotalHours),
      combineLeaves: Form.createFormField(props.formOptions.combineLeaves),
      leaveNotValidStartDay: Form.createFormField(props.formOptions.leaveNotValidStartDay),
      overtimeNotValidStartDay: Form.createFormField(props.formOptions.overtimeNotValidStartDay),
      overtimeConfirmValidDays: Form.createFormField(props.formOptions.overtimeConfirmValidDays),
      overtimeConfirmNotValidStartDay: Form.createFormField(
        props.formOptions.overtimeConfirmNotValidStartDay
      ),
      outWorkNotValidStartDay: Form.createFormField(props.formOptions.outWorkNotValidStartDay),
      supplyNotValidStartDay: Form.createFormField(props.formOptions.supplyNotValidStartDay),
    };
  },
};

const EhanceGroupScheduleCreate = Form.create(createOpts)(CreateGroupScheduleForm);

const mapStateToProps = (
  {
    staffShift: {
      groupSchedule: { create, edit },
    },
  },
  { mode }
) => {
  let formOptions = create;
  if (mode === 'edit') {
    formOptions = edit;
  }

  return {
    formOptions,
  };
};
const mapDispatchToProps = {
  updateCreateVisiable: staffShiftActions.updateGroupScheduleVisiable,
  updateCreateOptions: staffShiftActions.updateGroupScheduleCreateOptions,
  updateCreatedStaffIds: staffShiftActions.updateCreatedStaffIds,
  updateEditOptions: staffShiftActions.updateGroupScheduleEditOptions,
  updateEditStaffIds: staffShiftActions.updateEditGroupScheduleStaffIds,
};
export default connect(mapStateToProps, mapDispatchToProps)(EhanceGroupScheduleCreate);

function getShiftsDataService() {
  return async () => {
    const { response } = await staffShiftService.fetchShiftsList();
    if (response) {
      return response.data;
    } else {
      return [];
    }
  };
}
