import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';

import { Modal } from '@manyun/base-ui.ui.modal';

import {
  createRuleActionCreator,
  editRuleActionCreator,
  staffShiftActions,
} from '@manyun/dc-brain.legacy.redux/actions/staffShiftActions';

import EhanceGroupScheduleCreate from './form';

class CreateGroupScheduleModal extends Component {
  createRef = React.createRef();

  handleSubmit = async () => {
    await this.createRef.current.props.form.validateFields(errs => {
      if (errs) {
        return;
      }
      const { mode } = this.props;
      if (mode === 'new') {
        this.props.create();
      } else {
        this.props.edit();
      }
    });
  };
  render() {
    const { mode, createOrEditVisible, createOrEditLoading } = this.props;
    let title = '新建排班规则';
    if (mode === 'edit') {
      title = '编辑排班规则';
    }
    return (
      <Modal
        title={title}
        width={980}
        bodyStyle={{
          maxHeight: `calc(80vh - 109px)`,
          overflowY: 'auto',
        }}
        open={createOrEditVisible}
        destroyOnClose
        okButtonProps={{
          loading: createOrEditLoading,
        }}
        okText="保存"
        cancelButtonProps={{
          disabled: createOrEditLoading,
        }}
        onOk={this.handleSubmit}
        onCancel={() => this.props.updateCreateVisiable(false)}
      >
        <EhanceGroupScheduleCreate mode={mode} wrappedComponentRef={this.createRef} />
      </Modal>
    );
  }
}

const mapStateToProps = ({
  staffShift: {
    groupSchedule: { createOrEditVisible, createOrEditLoading },
  },
}) => {
  return {
    createOrEditVisible,
    createOrEditLoading,
  };
};
const mapDispatchToProps = {
  updateCreateVisiable: staffShiftActions.updateGroupScheduleVisiable,
  create: createRuleActionCreator,
  edit: editRuleActionCreator,
};
export default connect(
  mapStateToProps,
  mapDispatchToProps
)(Form.create({ name: 'create_duty_group' })(CreateGroupScheduleModal));
