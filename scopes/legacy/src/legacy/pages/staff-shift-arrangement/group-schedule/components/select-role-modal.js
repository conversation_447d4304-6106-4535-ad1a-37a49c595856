import React, { Component } from 'react';
import { Dnd<PERSON>rovider } from 'react-dnd';
import Backend from 'react-dnd-html5-backend';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import { ApiSelect } from '@galiojs/awesome-antd';

import { Button } from '@manyun/base-ui.ui.button';
import { Modal } from '@manyun/base-ui.ui.modal';

import { TinyTable } from '@manyun/dc-brain.legacy.components';
import { staffShiftActions } from '@manyun/dc-brain.legacy.redux/actions/staffShiftActions';
import { staffShiftService } from '@manyun/dc-brain.legacy.services';

import { DragableBodyRow } from '../../components/drag-table';

class SelectRoleModal extends Component {
  state = {
    roleTableData: [],
    selectedRoleIds: [],
  };

  components = {
    body: {
      row: DragableBodyRow,
    },
  };

  componentDidMount() {
    const { mode, editRoleTable, selectIds } = this.props;
    if (mode === 'edit') {
      this.setState({
        roleTableData: editRoleTable,
        selectedRoleIds: selectIds,
      });
    }
  }

  delete = currentId => {
    const { roleTableData, selectedRoleIds } = this.state;
    const newTable = roleTableData.filter(({ id }) => id !== currentId);
    const newIds = selectedRoleIds.filter(id => id !== currentId);
    this.setState({
      roleTableData: newTable,
      selectedRoleIds: newIds,
    });
  };

  moveRow = (dragIndex, hoverIndex) => {
    const { roleTableData } = this.state;
    const arr = Array.from(roleTableData);
    const [remove] = arr.splice(dragIndex, 1);
    arr.splice(hoverIndex, 0, remove);
    this.setState({
      roleTableData: arr,
      selectedRoleIds: arr.map(({ id }) => id),
    });
  };

  render() {
    const { selectRoleVisible, mode } = this.props;
    const { roleTableData, selectedRoleIds } = this.state;

    return (
      <Modal
        width={794}
        destroyOnClose
        visible={selectRoleVisible}
        onOk={() => {
          if (mode === 'new') {
            this.props.setSelectedRoleIds(selectedRoleIds);
          } else {
            this.props.setSelectedRoleIdsInEdit(selectedRoleIds);
          }
          this.props.onSelectRoleVisibleChange(false);
        }}
        onCancel={() => this.props.onSelectRoleVisibleChange(false)}
      >
        <DndProvider backend={Backend}>
          <TinyTable
            rowKey="id"
            size="small"
            columns={getColumns(this)}
            dataSource={roleTableData}
            components={this.components}
            onRow={(record, index) => ({
              index,
              moveRow: this.moveRow,
            })}
            actions={[
              <Form.Item
                key="seach"
                label="顶班角色选择"
                style={{ marginBottom: '0' }}
                labelCol={{ xl: 8 }}
                wrapperCol={{ xl: 16 }}
              >
                <div className="select-role-modal-select">
                  <ApiSelect
                    style={{ width: 186 }}
                    showArrow={false}
                    showSearch
                    trigger="onDidMount"
                    optionWithValue
                    fieldNames={{ label: 'roleName', value: 'id' }}
                    dataService={getShiftsDataService()}
                    value=""
                    disabledOptionValues={selectedRoleIds}
                    onChange={(id, option) => {
                      const newData = [...roleTableData, option];
                      const newId = [...selectedRoleIds, id];
                      this.setState({
                        roleTableData: newData,
                        selectedRoleIds: newId,
                      });
                    }}
                    getPopupContainer={() => document.querySelector('.select-role-modal-select')}
                  />
                </div>
              </Form.Item>,
            ]}
          />
        </DndProvider>
      </Modal>
    );
  }
}

const mapDispatchToProps = {
  setSelectedRoleIds: staffShiftActions.setSelectedRoleIds,
  setSelectedRoleIdsInEdit: staffShiftActions.setSelectedRoleIdsInEdit,
};

export default connect(null, mapDispatchToProps)(SelectRoleModal);

const getColumns = ctx => [
  {
    title: '序号',
    key: 'index',
    render: (_, record, index) => {
      return index + 1;
    },
  },
  {
    title: '角色名称',
    dataIndex: 'roleName',
  },
  {
    title: 'ID',
    dataIndex: 'roleCode',
  },
  {
    title: '操作',
    key: '__actions',
    render: (__, record) => (
      <Button
        type="link"
        style={{ padding: 0, height: 'auto' }}
        onClick={() => ctx.delete(record.id)}
      >
        删除
      </Button>
    ),
  },
];

function getShiftsDataService() {
  return async () => {
    const { response } = await staffShiftService.fetchRoleList();
    if (response) {
      return response.data;
    } else {
      return [];
    }
  };
}
