import React, { useMemo } from 'react';

import { Select } from '@galiojs/awesome-antd';

export const REMIND_MINUTES_KEY = {
  Early30M: -30,
  Early15M: -15,
  Early10M: -10,
  Early5M: -5,
  Late5M: 5,
  Late10M: 10,
  Late15M: 15,
  Late30M: 30,
};

export const REMIND_MINUTES_KEY_TEXT = {
  [REMIND_MINUTES_KEY.Early30M]: '提前30分钟',
  [REMIND_MINUTES_KEY.Early15M]: '提前15分钟',
  [REMIND_MINUTES_KEY.Early10M]: '提前10分钟',
  [REMIND_MINUTES_KEY.Early5M]: '提前5分钟',
  [REMIND_MINUTES_KEY.Late5M]: '超时5分钟',
  [REMIND_MINUTES_KEY.Late10M]: '超时10分钟',
  [REMIND_MINUTES_KEY.Late15M]: '超时15分钟',
  [REMIND_MINUTES_KEY.Late30M]: '超时30分钟',
};

export const REMIND_MINUTES_OPTIONS = [
  {
    value: REMIND_MINUTES_KEY.Early30M,
    label: REMIND_MINUTES_KEY_TEXT[REMIND_MINUTES_KEY.Early30M],
  },
  {
    value: REMIND_MINUTES_KEY.Early15M,
    label: REMIND_MINUTES_KEY_TEXT[REMIND_MINUTES_KEY.Early15M],
  },
  {
    value: REMIND_MINUTES_KEY.Early10M,
    label: REMIND_MINUTES_KEY_TEXT[REMIND_MINUTES_KEY.Early10M],
  },
  {
    value: REMIND_MINUTES_KEY.Early5M,
    label: REMIND_MINUTES_KEY_TEXT[REMIND_MINUTES_KEY.Early5M],
  },
  {
    value: REMIND_MINUTES_KEY.Late5M,
    label: REMIND_MINUTES_KEY_TEXT[REMIND_MINUTES_KEY.Late5M],
  },
  {
    value: REMIND_MINUTES_KEY.Late10M,
    label: REMIND_MINUTES_KEY_TEXT[REMIND_MINUTES_KEY.Late10M],
  },
  {
    value: REMIND_MINUTES_KEY.Late15M,
    label: REMIND_MINUTES_KEY_TEXT[REMIND_MINUTES_KEY.Late15M],
  },
  {
    value: REMIND_MINUTES_KEY.Late30M,
    label: REMIND_MINUTES_KEY_TEXT[REMIND_MINUTES_KEY.Late30M],
  },
];

export const ClockInRemindSettingSelect = React.forwardRef((props, ref) => {
  const options = useMemo(() => {
    if (Array.isArray(props.value)) {
      if (props.value.length === 4) {
        return REMIND_MINUTES_OPTIONS.map(opt => ({
          ...opt,
          disabled: props.value.includes(opt.value) ? false : true,
        }));
      }
      return REMIND_MINUTES_OPTIONS;
    }
    return REMIND_MINUTES_OPTIONS;
  }, [props.value]);

  return (
    <Select ref={ref} maxTagCount={4} style={{ width: '470px' }} {...props} options={options} />
  );
});
