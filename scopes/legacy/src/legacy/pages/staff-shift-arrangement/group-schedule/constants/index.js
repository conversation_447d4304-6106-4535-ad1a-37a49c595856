function getMinutes() {
  let minutes = [
    {
      value: 0,
      label: '不限',
    },
  ];
  for (let i = 1; i <= 59; i++) {
    const tmp = {
      value: i,
      label: `${i}分钟`,
    };
    minutes = [...minutes, tmp];
  }
  return minutes;
}
export const TIME_INTERVAL = getMinutes();
// export const TIME_INTERVAL = [
//   {
//     value: null,
//     label: '不限',
//     children: [
//       {
//         value: null,
//         label: '不限',
//       },
//     ],
//   },

//   {
//     value: 0,
//     label: '0小时',
//     children: getMinutes(),
//   },
//   {
//     value: 60,
//     label: '1小时',
//     children: getMinutes(),
//   },
//   {
//     value: 120,
//     label: '2小时',
//     children: getMinutes(),
//   },
//   {
//     value: 180,
//     label: '3小时',
//     children: getMinutes(),
//   },
//   {
//     value: 240,
//     label: '4小时',
//     children: getMinutes(),
//   },
//   {
//     value: 300,
//     label: '5小时',
//     children: getMinutes(),
//   },
//   {
//     value: 360,
//     label: '6小时',
//     children: getMinutes(),
//   },
//   {
//     value: 420,
//     label: '7小时',
//     children: getMinutes(),
//   },
//   {
//     value: 480,
//     label: '8小时',
//     children: getMinutes(),
//   },
// ];

export const SHIFT_CHANGES_SCOPE_KEY_MAP = {
  // 同机房
  SAME_IDC: 'IDC',
  // 同楼栋
  SAME_BLOCK: 'BLOCK',
};

export const SHIFT_CHANGES_SCOPE_TEXT_MAP = {
  [SHIFT_CHANGES_SCOPE_KEY_MAP.SAME_IDC]: '同机房',
  [SHIFT_CHANGES_SCOPE_KEY_MAP.SAME_BLOCK]: '同楼栋',
};

export const SHIFT_CHANGES_SCOPE_TYPE_OPTIONS = [
  {
    value: SHIFT_CHANGES_SCOPE_KEY_MAP.SAME_IDC,
    label: SHIFT_CHANGES_SCOPE_TEXT_MAP[SHIFT_CHANGES_SCOPE_KEY_MAP.SAME_IDC],
  },
  {
    value: SHIFT_CHANGES_SCOPE_KEY_MAP.SAME_BLOCK,
    label: SHIFT_CHANGES_SCOPE_TEXT_MAP[SHIFT_CHANGES_SCOPE_KEY_MAP.SAME_BLOCK],
  },
];
