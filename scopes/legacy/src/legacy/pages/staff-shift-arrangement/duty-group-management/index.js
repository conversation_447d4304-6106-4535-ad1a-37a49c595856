import React, { Component } from 'react';
import { connect } from 'react-redux';

import moment from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Input } from '@manyun/base-ui.ui.input';
import { Space } from '@manyun/base-ui.ui.space';

import { TinyCard, TinyTable, UserLink } from '@manyun/dc-brain.legacy.components';
import {
  deleteDutyGroupActionCreator,
  getDutyGroupDataActionCreator,
  staffShiftActions,
} from '@manyun/dc-brain.legacy.redux/actions/staffShiftActions';

import CreateDutyGroupModal from './components/create-modal';

class DutyGroupManageList extends Component {
  state = {
    mode: 'new',
  };

  componentWillUnmount() {
    const { updatePagination, updateSearchValues } = this.props;
    updateSearchValues({});
    updatePagination({ pageNum: 1, pageSize: 10 });
  }

  componentDidMount() {
    this.props.getData();
  }

  onChangeValues = ({ target: { value } }) => {
    this.props.updateSearchValues({ dutyGroupName: value.trim() });
  };

  onChangeUserName = ({ target: { value } }) => {
    this.props.updateSearchValues({ staffName: value.trim() });
  };

  onChangePage = (pageNum, pageSize) => {
    this.props.updatePagination({ pageNum, pageSize });
    this.props.getData(false);
  };

  edit = value => {
    this.setState({ mode: 'edit' });
    this.props.updateCreateVisiable(true);
    const [, block] = value.blockTag.split('.');
    const infos = {
      id: value.id,
      groupName: {
        value: value.groupName,
        dirty: false,
        name: 'groupName',
        touched: true,
      },
      idcBlockTag: {
        value: [value.idcTag, block],
        dirty: false,
        name: 'idcBlockTag',
        touched: true,
      },
      staffIds: {
        value:
          value.staffList && value.staffList.length && value.staffList[0] ? value.staffList : [],
        dirty: false,
        name: 'staffIds',
        touched: true,
        validating: true,
        errors: null,
      },
      staffIdsTable: value.staffList ? value.staffList : [],
    };
    this.props.updateEditCreateOption(infos);
  };

  render() {
    const { pagination, loading, data, total, getData } = this.props;
    const { mode } = this.state;
    return (
      <TinyCard>
        <TinyTable
          rowKey="id"
          dataSource={data}
          loading={loading}
          columns={getColumns(this)}
          align="left"
          scroll={{ x: 'max-content' }}
          pagination={{
            total: total,
            current: pagination.pageNum,
            pageSize: pagination.pageSize,
            onChange: this.onChangePage,
          }}
          actionsWrapperStyle={{ justifyContent: 'space-between' }}
          actions={[
            <Button
              key="create-shift"
              type="primary"
              onClick={() => {
                this.setState({ mode: 'new' });
                this.props.updateCreateVisiable(true);
              }}
            >
              新建班组
            </Button>,
            <Space key="search" size="middle">
              <Input.Search
                style={{ width: 211, height: 32 }}
                placeholder="请输入用户名称"
                allowClear
                onSearch={getData}
                onChange={this.onChangeUserName}
              />
              <Input.Search
                style={{ width: 211, height: 32 }}
                placeholder="请输入班组名称"
                allowClear
                onSearch={getData}
                onChange={this.onChangeValues}
              />
            </Space>,
          ]}
        />
        <CreateDutyGroupModal mode={mode} />
      </TinyCard>
    );
  }
}

const mapStateToProps = ({
  staffShift: {
    dutyGroup: { pagination, loading, data, total },
  },
}) => {
  return {
    pagination,
    loading,
    data,
    total,
  };
};
const mapDispatchToProps = {
  getData: getDutyGroupDataActionCreator,
  setDutyGroupDataAndTotal: staffShiftActions.setDutyGroupDataAndTotal,
  updateSearchValues: staffShiftActions.updateDutyGroupSearchValues,
  updatePagination: staffShiftActions.updateDutyGroupPagination,
  updateCreateVisiable: staffShiftActions.updateDutyGroupCreateVisiable,
  delete: deleteDutyGroupActionCreator,
  updateEditCreateOption: staffShiftActions.updateDutyGroupEditCreateOption,
};

export default connect(mapStateToProps, mapDispatchToProps)(DutyGroupManageList);

const getColumns = ctx => [
  {
    title: '班组名称',
    dataIndex: 'groupName',
    render: (text, record) => (
      <Button type="link" onClick={() => ctx.edit(record)}>
        {text}
      </Button>
    ),
  },
  {
    title: '机房',
    dataIndex: 'idcTag',
  },
  {
    title: '楼栋',
    dataIndex: 'blockTag',
    render: blockTag => {
      if (!blockTag) {
        return '';
      }
      const tags = blockTag.split('.');
      return tags[1];
    },
  },

  {
    title: '修改时间',
    dataIndex: 'gmtModified',
    dataType: 'dateTime',
    render: gmtModified => {
      return moment(gmtModified).format('YYYY-MM-DD HH:mm:ss');
    },
  },
  {
    title: '修改人',
    dataIndex: 'modifierId',
    render: (_, record) => <UserLink userId={record.modifierId} userName={record.modifierName} />,
  },

  {
    title: '操作',
    key: '__actions',
    fixed: 'right',
    render: (__, record) => (
      <span>
        <Button compact type="link" onClick={() => ctx.edit(record)}>
          编辑
        </Button>
        <Divider type="vertical" />
        <DeleteConfirm
          variant="popconfirm"
          targetName={record.groupName}
          onOk={() => ctx.props.delete({ id: record.id })}
        >
          <Button compact type="link">
            删除
          </Button>
        </DeleteConfirm>
      </span>
    ),
  },
];
