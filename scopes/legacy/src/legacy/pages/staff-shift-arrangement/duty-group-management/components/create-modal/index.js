import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';

import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';

import {
  createDutyGroupActionCreator,
  editDutyGroupActionCreator,
  staffShiftActions,
} from '@manyun/dc-brain.legacy.redux/actions/staffShiftActions';

import EhanceShiftSysCreate from './form';

class CreateDutyGroupModal extends Component {
  createGroupRef = React.createRef();

  handleSubmit = async () => {
    await this.createGroupRef.current.props.form.validateFields(errs => {
      if (errs || this.props.formOptions.staffIds.value.length === 0) {
        return;
      }
      const {
        mode,
        formOptions: { staffIds },
      } = this.props;

      if (staffIds.value.every(staff => !staff.teamLeader)) {
        message.warn('请先设置一位值班长');
        return;
      }
      if (mode === 'new') {
        this.props.createDutyGroup();
      } else {
        this.props.editDutyGroup();
      }
    });
  };
  render() {
    const { mode, createOrEditVisible, submitLoading } = this.props;
    let title = '新建班组';
    if (mode === 'edit') {
      title = '编辑班组';
    }
    return (
      <Modal
        title={title}
        width={600}
        bodyStyle={{
          maxHeight: 'calc(80vh - 109px)',
          overflowY: 'scroll',
        }}
        destroyOnClose
        okButtonProps={{
          loading: submitLoading,
        }}
        cancelButtonProps={{
          disabled: submitLoading,
        }}
        open={createOrEditVisible}
        onOk={this.handleSubmit}
        onCancel={() => this.props.updateCreateVisiable(false)}
      >
        <EhanceShiftSysCreate mode={mode} wrappedComponentRef={this.createGroupRef} />
      </Modal>
    );
  }
}

const mapStateToProps = (
  {
    staffShift: {
      dutyGroup: { createOrEditVisible, create, edit },
    },
  },
  { mode }
) => {
  let formOptions = create;
  if (mode === 'edit') {
    formOptions = edit;
  }
  return {
    createOrEditVisible,
    formOptions,
    submitLoading: mode === 'edit' ? edit.submitLoading : create.submitLoading,
  };
};
const mapDispatchToProps = {
  updateCreateVisiable: staffShiftActions.updateDutyGroupCreateVisiable,
  createDutyGroup: createDutyGroupActionCreator,
  editDutyGroup: editDutyGroupActionCreator,
};
export default connect(
  mapStateToProps,
  mapDispatchToProps
)(Form.create({ name: 'create_duty_group' })(CreateDutyGroupModal));
