import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import { ApiSelect } from '@galiojs/awesome-antd';
import uniqBy from 'lodash/uniqBy';

import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';

import { LocationCascader, TinyTable } from '@manyun/dc-brain.legacy.components';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import { staffShiftActions } from '@manyun/dc-brain.legacy.redux/actions/staffShiftActions';
import { staffShiftService } from '@manyun/dc-brain.legacy.services';

class CreateDutyGroupForm extends Component {
  state = {
    userTableData: [],
    pageNum: 1,
    pageSize: 10,
    selectedDutyUsers: [] /**选中的班组成员 */,
  };

  componentDidMount() {
    this.props.syncCommonData({ strategy: { space: 'FORCED' } });
    const { formOptions, mode } = this.props;
    if (mode === 'edit') {
      this.setState({
        userTableData: formOptions.staffIdsTable,
      });
    }
  }

  delateUser = currentId => {
    const {
      formOptions: { staffIds },
      mode,
    } = this.props;
    const { userTableData } = this.state;
    const newIds = staffIds.value.filter(({ id }) => id !== currentId);
    const newTable = userTableData.filter(({ id }) => id !== currentId);
    this.setState({
      userTableData: newTable,
    });
    if (mode === 'new') {
      this.props.updateCreatedStaffIds(newIds);
    } else {
      this.props.updateEditStaffIds(newIds);
    }
  };

  updateUserTeamLeader = currentId => {
    const { mode } = this.props;
    const { userTableData } = this.state;
    const newTable = userTableData.map(user => ({
      ...user,
      teamLeader: user.id === currentId,
    }));
    const newIds = newTable.map(user => ({ id: user.id, teamLeader: user.teamLeader }));
    this.setState({
      userTableData: newTable,
    });
    if (mode === 'new') {
      this.props.updateCreatedStaffIds(newIds);
    } else {
      this.props.updateEditStaffIds(newIds);
    }
  };

  onChangePage = (pageNum, pageSize) => {
    this.setState({
      pageNum,
      pageSize,
    });
  };

  render() {
    const {
      mode,
      form: { getFieldDecorator },
      formOptions: { idcBlockTag, staffIds, staffIdsTable },
    } = this.props;
    const { userTableData, pageNum, pageSize } = this.state;

    const idcAndBlock = idcBlockTag.value;
    const resourceCode = `${idcAndBlock[0]}.${idcAndBlock[1]}`;
    const searchUsersParams = { resourceCode };

    return (
      <Space direction="vertical" style={{ width: '100%' }}>
        <Form colon={false} labelCol={{ xl: 5 }} wrapperCol={{ xl: 18 }}>
          <Form.Item label="班组名称">
            {getFieldDecorator('groupName', {
              rules: [
                { required: true, message: '班组名称必填' },
                {
                  type: 'string',
                  max: 20,
                  message: '最多输入 20 个字符！',
                },
              ],
            })(<Input style={{ width: 340 }} allowClear />)}
          </Form.Item>
          <Form.Item label="所属机房/楼栋" style={{ marginBottom: 0 }}>
            {getFieldDecorator('idcBlockTag', {
              rules: [{ required: true, message: '所属机房/楼栋必选' }],
            })(
              <LocationCascader
                style={{ width: 340 }}
                authorizedOnly
                allowClear={false}
                fieldNames={{ label: 'label', value: 'value', children: 'children' }}
                notFoundContent={<Spin size="small" />}
                onChange={async value => {
                  this.setState({ userTableData: [] });
                  if (mode === 'new') {
                    this.props.updateCreatedStaffIds([]);
                  } else {
                    this.props.updateEditStaffIds([]);
                  }
                }}
              />
            )}
          </Form.Item>
        </Form>
        {idcBlockTag.value.length > 0 && (
          <>
            <Typography.Title level={5}>班组成员</Typography.Title>
            <TinyTable
              rowKey="id"
              columns={getColumns(this)}
              dataSource={userTableData}
              actions={[
                <Space key="actions">
                  {/*Fix issue: http://chandao.manyun-local.com/zentao/bug-view-8499.html */}
                  <ApiSelect
                    style={{ width: 344 }}
                    showArrow={false}
                    mode="multiple"
                    optionWithValue
                    showSearch
                    allowClear
                    maxTagCount={3}
                    fieldNames={{ label: 'userName', value: 'id' }}
                    dataService={getShiftsDataService(
                      searchUsersParams,
                      mode === 'edit' ? staffIdsTable : [] //该接口查询的是未被关联的用户，编辑时查询的不包括当前已经选择的用户
                    )}
                    serviceQueries={[resourceCode]}
                    disabledOptionValues={staffIds.value.map(staff => staff.id)}
                    value={this.state.selectedDutyUsers.map(user => user.id)}
                    placeholder="请选择所要添加的班组成员"
                    onChange={(id, option) => {
                      this.setState({
                        selectedDutyUsers: option.map(opt => ({ ...opt, teamLeader: false })),
                      });
                    }}
                  />
                  <Button
                    type="primary"
                    onClick={() => {
                      const slectedUsers = this.state.selectedDutyUsers;
                      const newData = [...userTableData, ...slectedUsers];
                      const newIds = [
                        ...staffIds.value,
                        ...slectedUsers.map(user => ({ id: user.id, teamLeader: false })),
                      ];
                      this.setState({
                        userTableData: newData,
                        selectedDutyUsers: [],
                      });
                      if (mode === 'new') {
                        this.props.updateCreatedStaffIds(newIds);
                      } else {
                        this.props.updateEditStaffIds(newIds);
                      }
                    }}
                  >
                    添加
                  </Button>
                </Space>,
              ]}
              pagination={{
                total: userTableData.length,
                current: pageNum,
                pageSize: pageSize,
                onChange: this.onChangePage,
              }}
              locale={{
                emptyText: (
                  <div className="has-error">
                    <span className="ant-form-explain">至少添加1个班组成员</span>
                  </div>
                ),
              }}
            />
          </>
        )}
      </Space>
    );
  }
}

const createOpts = {
  onFieldsChange(props, changedFields) {
    if (changedFields.staffIds) {
      return;
    }
    if (props.mode === 'new') {
      props.updateCreateOptions(changedFields);
    } else {
      props.updateEditOptions(changedFields);
    }
  },
  mapPropsToFields(props) {
    return {
      groupName: Form.createFormField(props.formOptions.groupName),
      idcBlockTag: Form.createFormField(props.formOptions.idcBlockTag),
      staffIds: Form.createFormField(props.formOptions.staffIds),
    };
  },
};

const EhanceShiftSysCreate = Form.create(createOpts)(CreateDutyGroupForm);

const mapStateToProps = (
  {
    staffShift: {
      dutyGroup: { create, edit },
    },
  },
  { mode }
) => {
  let formOptions = create;
  if (mode === 'edit') {
    formOptions = edit;
  }
  return {
    formOptions,
  };
};
const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
  updateCreateVisiable: staffShiftActions.updateDutyGroupCreateVisiable,
  updateCreateOptions: staffShiftActions.updateDutyGroupCreateOptions,
  updateCreatedStaffIds: staffShiftActions.updateCreatedStaffIds,
  updateEditOptions: staffShiftActions.updateDutyGroupEditOptions,
  updateEditStaffIds: staffShiftActions.updateEditDutyGroupStaffIds,
};
export default connect(mapStateToProps, mapDispatchToProps)(EhanceShiftSysCreate);

const getColumns = ctx => [
  {
    title: '序号',
    key: 'index',
    render: (__, record, index) => {
      return index + 1;
    },
  },
  {
    title: '用户名',
    dataIndex: 'userName',
    render: (val, record) => {
      return (
        <>
          {val} {record.teamLeader && <Tag color="processing">值班长</Tag>}
        </>
      );
    },
  },
  {
    title: '用户登录名',
    dataIndex: 'loginName',
  },
  {
    title: '操作',
    key: '__actions',
    width: 120,
    render: (__, record) => (
      <Space>
        <Button
          disabled={record.teamLeader}
          compact
          type="link"
          onClick={() => {
            ctx.updateUserTeamLeader(record.id);
          }}
        >
          设为值班长
        </Button>
        <Button compact type="link" onClick={() => ctx.delateUser(record.id)}>
          删除
        </Button>
      </Space>
    ),
  },
];

function getShiftsDataService(params, initData) {
  return async () => {
    const { response } = await staffShiftService.fetchUserListByResource(params);
    if (response) {
      return uniqBy([...initData, ...response.data], 'id');
    } else {
      return [];
    }
  };
}
