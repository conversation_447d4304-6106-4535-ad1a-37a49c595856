import React, { Component } from 'react';
import { connect } from 'react-redux';

import ExclamationCircleFilled from '@ant-design/icons/es/icons/ExclamationCircleFilled';
import difference from 'lodash/difference';

import { Input } from '@manyun/base-ui.ui.input';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Radio } from '@manyun/base-ui.ui.radio';

import { TinyTable } from '@manyun/dc-brain.legacy.components';
import { staffShiftActions } from '@manyun/dc-brain.legacy.redux/actions/staffShiftActions';

import { SHITF_SYS_TYPE_KEY_MAP } from '../../constants';

class AllShiftTable extends Component {
  state = {
    selectedShift: null,
    periodSelectedRowKeys: [],
    periodSelectedRows: [],
  };

  componentDidUpdate(prevProps) {
    if (
      prevProps.selectedShiftVisible === false &&
      this.props.selectedShiftVisible === true &&
      this.props.shiftsType === SHITF_SYS_TYPE_KEY_MAP.PERIOD &&
      this.props.periodRowSelected.length > 0
    ) {
      const { periodRowSelected } = this.props;
      const rowSelectedKey = periodRowSelected.map(({ id }) => id);

      this.setState({
        periodSelectedRowKeys: rowSelectedKey,
        periodSelectedRows: periodRowSelected,
      });
    }
  }

  onChangeRadio = (checked, record) => {
    if (checked) {
      this.setState({ selectedShift: record });
    }
  };

  Search = value => {
    const { allShiftData } = this.props;
    let newData = [];
    if (value.trim()) {
      newData = allShiftData.filter(({ dutyName }) => {
        if (dutyName.includes(value.trim())) {
          return true;
        } else {
          return false;
        }
      });
    } else {
      newData = allShiftData;
    }
    this.props.updateShowAllShiftData(newData);
  };

  onSelectChangePeriod = (selectedRowKeys, selectedRows) => {
    this.setState({
      periodSelectedRowKeys: selectedRowKeys,
      periodSelectedRows: selectedRows,
      selectedShift: selectedRowKeys,
    });
  };

  onOk = (defaultChecked, periodSelectedRows) => {
    const { periodRowSelected, showAllShiftData } = this.props;
    const { periodSelectedRowKeys } = this.state;
    const rowSelectedKey = periodRowSelected.map(({ id }) => id);
    const del = difference(rowSelectedKey, periodSelectedRowKeys);
    if (del.length > 0) {
      const nameArr = del.map(id => {
        const shift = showAllShiftData.filter(item => item.id === id);
        if (shift[0]) {
          return shift[0].dutyName;
        }
        return null;
      });
      const txt = nameArr.join('-');
      Modal.confirm({
        icon: <ExclamationCircleFilled />,
        title: `确定从工作班次移除${txt}`,
        content: '移除后，排班周期将自动去掉该班次，但不影响排班表中已排的班次',
        okText: '确认',
        cancelText: '取消',
        onOk: () => {
          this.props.onChangeShift(defaultChecked, periodSelectedRows);
          this.props.updateSelectedShiftVisible(false);
          this.setState({ selectedShift: null, periodSelectedRowKeys: [], periodSelectedRows: [] });
        },
        onCancel: () => {
          this.props.updateSelectedShiftVisible(false);
          this.setState({ selectedShift: null, periodSelectedRowKeys: [], periodSelectedRows: [] });
        },
      });
    } else {
      this.props.onChangeShift(defaultChecked, periodSelectedRows);
      this.props.updateSelectedShiftVisible(false);
      this.setState({ selectedShift: null, periodSelectedRowKeys: [], periodSelectedRows: [] });
    }
  };

  render() {
    const { showAllShiftData, checkedShift, shiftsType, selectedShiftVisible } = this.props;
    const { selectedShift, periodSelectedRowKeys, periodSelectedRows } = this.state;

    let defaultChecked = checkedShift;
    if (selectedShift) {
      defaultChecked = selectedShift;
    }

    let rowSelection = null;
    if (shiftsType === SHITF_SYS_TYPE_KEY_MAP.PERIOD) {
      rowSelection = {
        selectedRowKeys: periodSelectedRowKeys,
        selectedRows: periodSelectedRows,
        onChange: this.onSelectChangePeriod,
      };
    }

    return (
      <Modal
        title="选择班次"
        visible={selectedShiftVisible}
        destroyOnClose
        onCancel={() => {
          this.props.updateSelectedShiftVisible(false);
          this.setState({ selectedShift: null, periodSelectedRowKeys: [], periodSelectedRows: [] });
        }}
        onOk={() => this.onOk(defaultChecked, periodSelectedRows)}
      >
        <TinyTable
          size="small"
          rowKey="id"
          columns={columns(this, defaultChecked, shiftsType)}
          dataSource={showAllShiftData}
          actionsWrapperStyle={{ justifyContent: 'flex-end' }}
          pagination={false}
          actions={[
            <Input.Search
              placeholder="请输入班次名称"
              allowClear
              key="search"
              style={{ width: 200 }}
              onSearch={this.Search}
            />,
          ]}
          rowSelection={rowSelection}
        />
      </Modal>
    );
  }
}

const mapStateToProps = ({
  staffShift: {
    shiftSys: {
      allShiftData,
      showAllShiftData,
      PERIOD_selectedRowKeys,
      PERIOD_selectedRows,
      selectedShiftVisible,
    },
  },
}) => {
  return {
    allShiftData,
    showAllShiftData,
    PERIOD_selectedRowKeys,
    PERIOD_selectedRows,
    selectedShiftVisible,
  };
};

const mapDispatchToProps = {
  saveCheckedShift: staffShiftActions.saveCheckedShift,
  updateShowAllShiftData: staffShiftActions.updateShowAllShiftData,
  updateSelectedShiftVisible: staffShiftActions.updateSelectedShiftVisible,
};

export default connect(mapStateToProps, mapDispatchToProps)(AllShiftTable);

const columns = (ctx, defaultChecked, shiftsType) => [
  {
    title: '',
    key: 'dutyIsSelected',
    render: (_, record) => {
      if (shiftsType === SHITF_SYS_TYPE_KEY_MAP.WEEK) {
        const checked = defaultChecked && defaultChecked.id === record.id ? true : false;
        return (
          <Radio
            checked={checked}
            onChange={({ target: { checked } }) => ctx.onChangeRadio(checked, record)}
          />
        );
      }
    },
  },
  {
    title: '班次名称',
    dataIndex: 'dutyName',
  },
  {
    title: '班次时间',
    dataIndex: 'onDutyTime',
    render: (_, record) => {
      if (record.offIsNextDay) {
        return `${record.onDutyTime}-次日${record.offDutyTime}`;
      }
      return `${record.onDutyTime}-${record.offDutyTime}`;
    },
  },
];
