import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';

import { Button } from '@manyun/base-ui.ui.button';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Explanation } from '@manyun/base-ui.ui.explanation';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Radio } from '@manyun/base-ui.ui.radio';

import { TinyTable } from '@manyun/dc-brain.legacy.components';
import {
  createShiftSysActionCreator,
  editShiftSysActionCreator,
  getAllShiftDataActionCreator,
  staffShiftActions,
} from '@manyun/dc-brain.legacy.redux/actions/staffShiftActions';

import { SHITF_SYS_TYPE_KEY_MAP, SHITF_SYS_TYPE_OPTIONS } from '../../constants';
import WorkDdayAndShift from '../period-workday-shift';
import AllShiftTable from '../select-shift-modal';
import WorkShift from '../work-shift';

class ShiftSysCreate extends Component {
  state = {
    weekSelectedRows: null,
  };

  componentDidUpdate(prevProps) {
    if (!prevProps.createVisiable && this.props.createVisiable && this.props.mode === 'new') {
      this.props.resetCreateShiftSysOptions();
    }
    if (prevProps.createVisiable !== this.props.createVisiable && this.props.createVisiable) {
      const {
        mode,
        createOptions: { shiftsType },
        editOptions,
      } = this.props;
      let shiftType = mode === 'edit' ? editOptions.shiftsType.value : shiftsType.value;
      this.props.getAllShiftData({
        timeLimit: shiftType === SHITF_SYS_TYPE_KEY_MAP.WEEK,
      });
    }
  }

  handleOk = event => {
    event.preventDefault();
    this.props.form.validateFields(async (err, values) => {
      if (err) {
        return;
      }
      const { mode } = this.props;
      if (mode === 'new') {
        this.props.createShiftSys();
      } else {
        this.props.editShiftSys({ scheduleConfirm: this.scheduleConfirm });
      }
    });
  };

  onChangeShift = (weekShift, periodShift) => {
    const {
      createOptions: { shiftsType },
      editOptions,
      mode,
    } = this.props;
    if (mode === 'new') {
      if (shiftsType.value === SHITF_SYS_TYPE_KEY_MAP.WEEK) {
        this.props.updateWeekcheckedShift(weekShift);
      } else {
        this.props.updateperiodcheckedShift(periodShift);
      }
    } else {
      if (editOptions.shiftsType.value === SHITF_SYS_TYPE_KEY_MAP.WEEK) {
        this.props.updateEditWeekcheckedShift(weekShift);
      } else {
        this.props.updateEditperiodcheckedShift(periodShift);
      }
    }
  };

  onSelectChange_week = (selectedRowKeys, selectedRows) => {
    const { weekWorkTableData, mode } = this.props;
    const newData = weekWorkTableData.map(item => {
      if (selectedRowKeys.includes(item.code)) {
        return {
          ...item,
          checked: true,
        };
      } else {
        return {
          ...item,
          checked: false,
        };
      }
    });
    if (mode === 'new') {
      this.props.updateWeekWorkTable(newData);
    } else {
      this.props.updateEditWeekWorkTable(newData);
    }
  };

  scheduleConfirm = () => {
    Modal.confirm({
      title: `是否需要重新排班？`,
      content: '当前班制关联考勤组，班次发生变更后需要重新排班。',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        this.props.updateAttGroupTableVisiable(true);
      },
    });
  };

  render() {
    const {
      createVisiable,
      mode,
      form: { getFieldDecorator },
      createOptions: { shiftsType, enableStatutoryHoliday },
      weekCheckedShift,
      weekWorkTableData,
      weekSelectedRowKeys,
      period,
      editOptions,
    } = this.props;
    const { weekSelectedRows } = this.state;

    let text = '新建班制';
    let enableHoliday = enableStatutoryHoliday.value;
    let shiftType = shiftsType.value;
    let periodRowSelected = period.periodShifts.value;
    let checkedShift = weekCheckedShift;

    if (mode === 'edit') {
      text = '编辑班制';
      enableHoliday = editOptions.week.enableStatutoryHoliday.value;
      shiftType = editOptions.shiftsType.value;
      periodRowSelected = editOptions.period.periodShifts.value;
      checkedShift = editOptions.week.checkedShiftConfirm;
    }
    let shiftInfo = '';
    if (weekCheckedShift) {
      if (weekCheckedShift.offIsNextDay) {
        shiftInfo = `${weekCheckedShift.dutyName}: ${weekCheckedShift.onDutyTime}-次日${weekCheckedShift.offDutyTime}`;
      } else {
        shiftInfo = `${weekCheckedShift.dutyName}: ${weekCheckedShift.onDutyTime}-${weekCheckedShift.offDutyTime}`;
      }
    }
    const defaultChenkedData = weekSelectedRows ? weekSelectedRows : checkedShift;

    return (
      <Modal
        bodyStyle={{ maxHeight: 'calc(80vh - 55px)', overflowY: 'auto' }}
        width={778}
        destroyOnClose
        title={text}
        visible={createVisiable}
        onOk={this.handleOk}
        onCancel={() => this.props.updateShiftSysCreateVisiable(false)}
      >
        <Form colon={false} labelCol={{ span: 3 }}>
          <Form.Item style={{ marginBottom: 8 }} label="班制名称">
            {getFieldDecorator('shiftsName', {
              rules: [
                { required: true, message: '班制名称必填' },
                {
                  type: 'string',
                  max: 20,
                  message: '最多输入 20 个字符！',
                },
              ],
            })(<Input style={{ width: 340 }} allowClear />)}
          </Form.Item>
          <Form.Item style={{ marginBottom: 0 }} label="班制设置"></Form.Item>
          <Form.Item style={{ marginBottom: 8 }} label="班制类型">
            {getFieldDecorator('shiftsType')(
              <Radio.Group disabled={mode === 'edit'}>
                {SHITF_SYS_TYPE_OPTIONS.map(({ value, label, txt }, index) => {
                  return (
                    <Radio
                      value={value}
                      key={index}
                      style={{ marginBottom: index === 0 ? '10px' : 0 }}
                    >
                      {`${label} ${txt}`}
                    </Radio>
                  );
                })}
              </Radio.Group>
            )}
          </Form.Item>
          {shiftType === SHITF_SYS_TYPE_KEY_MAP.WEEK && (
            <>
              <Form.Item style={{ marginBottom: 8 }} label="工作日设置">
                <>
                  快捷设置班次：班次&nbsp;{shiftInfo}
                  &nbsp;&nbsp;
                  <Button
                    campact
                    type="link"
                    onClick={() => {
                      this.props.getAllShiftData({ timeLimit: true });
                      this.props.updateSelectedShiftVisible(true);
                    }}
                  >
                    更改班次
                  </Button>
                </>
              </Form.Item>
              <TinyTable
                rowKey="code"
                columns={columns(this, defaultChenkedData, weekSelectedRowKeys)}
                dataSource={weekWorkTableData}
                pagination={false}
                rowSelection={{
                  selectedRowKeys: weekSelectedRowKeys,
                  onChange: this.onSelectChange_week,
                }}
              />
              <Form.Item style={{ marginBottom: 8 }}>
                {getFieldDecorator('enableStatutoryHoliday')(
                  <Checkbox checked={enableHoliday}>法定节假日（含法定调休日）自动排休</Checkbox>
                )}
              </Form.Item>
            </>
          )}
          {shiftType === SHITF_SYS_TYPE_KEY_MAP.PERIOD && (
            <>
              <Form.Item style={{ marginBottom: 8 }} label="工作班次">
                {getFieldDecorator('periodShifts', {
                  rules: [{ required: true, message: '工作班次必选' }],
                })(
                  <WorkShift
                    changeVisible={() => {
                      this.props.getAllShiftData({ timeLimit: false });
                      this.props.updateSelectedShiftVisible(true);
                    }}
                  />
                )}
              </Form.Item>
              {(period.periodShifts.value.length > 0 ||
                editOptions.period.periodShifts.value.length > 0) && (
                <>
                  <Form.Item
                    style={{ marginBottom: 8 }}
                    label={
                      <Explanation
                        style={{ color: 'inherit' }}
                        iconType="question"
                        tooltip={{
                          title: '以天为周期进行循环，最大周期天数为31天，最小周期天数2天',
                        }}
                      >
                        循环天数
                      </Explanation>
                    }
                  >
                    {getFieldDecorator('periodDays')(
                      <InputNumber
                        min={2}
                        max={31}
                        disabled={
                          editOptions.attGroupListByShifts &&
                          editOptions.attGroupListByShifts.length
                            ? true
                            : false
                        }
                      />
                    )}
                  </Form.Item>
                  <WorkDdayAndShift mode={mode} />
                </>
              )}
            </>
          )}
        </Form>
        <AllShiftTable
          checkedShift={defaultChenkedData}
          periodRowSelected={periodRowSelected}
          onChangeShift={this.onChangeShift}
          shiftsType={shiftType}
        />
      </Modal>
    );
  }
}

const createOpts = {
  onFieldsChange(props, changedFields) {
    if (props.mode === 'new') {
      props.updateCreateOptions(changedFields);
    } else {
      props.updateEditOptions(changedFields);
    }
  },
  mapPropsToFields(props) {
    if (props.mode === 'new') {
      return {
        shiftsName: Form.createFormField(props.createOptions.shiftsName),
        shiftsType: Form.createFormField(props.createOptions.shiftsType),
        periodShifts: Form.createFormField(props.period.periodShifts),
        periodDays: Form.createFormField(props.period.periodDays),
        enableStatutoryHoliday: Form.createFormField(props.createOptions.enableStatutoryHoliday),
      };
    } else {
      return {
        shiftsName: Form.createFormField(props.editOptions.shiftsName),
        shiftsType: Form.createFormField(props.editOptions.shiftsType),
        periodShifts: Form.createFormField(props.editOptions.period.periodShifts),
        periodDays: Form.createFormField(props.editOptions.period.periodDays),
        enableStatutoryHoliday: Form.createFormField(props.editOptions.week.enableStatutoryHoliday),
      };
    }
  },
};

const EhanceShiftSysCreate = Form.create(createOpts)(ShiftSysCreate);

const mapStateToProps = (
  {
    staffShift: {
      shiftSys: { createOptions, createVisiable, week, period, editOptions },
    },
  },
  { mode }
) => {
  let workTableData = week.workTableData;
  let weekCheckedShift = week.checkedShiftConfirm;
  if (mode === 'edit') {
    workTableData = editOptions.week.workTableData;
    weekCheckedShift = editOptions.week.checkedShiftConfirm;
  }
  const weekSelectedRowKeys = workTableData
    .map(({ code, checked }) => {
      if (checked) {
        return code;
      } else {
        return null;
      }
    })
    .filter(Boolean);
  return {
    createOptions,
    createVisiable,
    weekCheckedShift: weekCheckedShift,
    weekWorkTableData: week.workTableData,
    weekSelectedRowKeys,
    period,
    editOptions,
  };
};
const mapDispatchToProps = {
  updateCreateOptions: staffShiftActions.updateCreateShiftSysOptions,
  updateShiftSysCreateVisiable: staffShiftActions.updateShiftSysCreateVisiable,
  getAllShiftData: getAllShiftDataActionCreator,
  updateWeekWorkTable: staffShiftActions.updateWeekWorkTable,
  updateSelectedShiftVisible: staffShiftActions.updateSelectedShiftVisible,
  updateWeekcheckedShift: staffShiftActions.updateWeekcheckedShift,
  updateperiodcheckedShift: staffShiftActions.updateperiodcheckedShift,
  createShiftSys: createShiftSysActionCreator,
  updateEditOptions: staffShiftActions.updateEditShiftSysOptions,
  updateEditWeekWorkTable: staffShiftActions.updateEditShiftSysWeekWorkTable,
  editShiftSys: editShiftSysActionCreator,
  updateEditWeekcheckedShift: staffShiftActions.updateEditWeekcheckedShift,
  updateEditperiodcheckedShift: staffShiftActions.updateEditperiodcheckedShift,
  resetCreateShiftSysOptions: staffShiftActions.resetCreateShiftSysOptions,
};

export default connect(mapStateToProps, mapDispatchToProps)(EhanceShiftSysCreate);

const columns = (ctx, defaultChenkedData, weekSelectedRowKeys) => [
  {
    title: '工作日',
    dataIndex: 'workTime',
  },
  {
    title: '班次时间段',
    key: 'shiftTimeRange',
    render: (_, record) => {
      let txt = '休息';
      if (weekSelectedRowKeys.includes(record.code)) {
        if (defaultChenkedData.offIsNextDay) {
          txt = `班次 ${defaultChenkedData.dutyName}: ${defaultChenkedData.onDutyTime}-次日${defaultChenkedData.offDutyTime}`;
        } else {
          txt = `班次 ${defaultChenkedData.dutyName}: ${defaultChenkedData.onDutyTime}-${defaultChenkedData.offDutyTime}`;
        }
      }
      return txt;
    },
  },
];
