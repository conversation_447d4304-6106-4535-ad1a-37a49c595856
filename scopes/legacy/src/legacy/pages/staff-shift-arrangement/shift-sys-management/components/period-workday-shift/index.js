import React, { Component } from 'react';
import { connect } from 'react-redux';

import { Select } from '@galiojs/awesome-antd';

import { TinyTable } from '@manyun/dc-brain.legacy.components';
import { staffShiftActions } from '@manyun/dc-brain.legacy.redux/actions/staffShiftActions';

class WorkDdayAndShift extends Component {
  onChangeShift = (value, record) => {
    const { workTableData, mode } = this.props;
    const newData = workTableData.map(item => {
      if (item.code === record.code) {
        return {
          ...item,
          shiftId: value,
        };
      } else {
        return item;
      }
    });
    if (mode === 'new') {
      this.props.updatePeriodWorkTable(newData);
    } else {
      this.props.updateEditPeriodWorkTable(newData);
    }
  };

  render() {
    const { workTableData, selectedShifts } = this.props;
    return (
      <TinyTable
        rowKey="code"
        columns={columns(this, selectedShifts)}
        dataSource={workTableData}
        pagination={false}
      />
    );
  }
}

const mapStateToProps = (
  {
    staffShift: {
      shiftSys: {
        period: { workTableData, periodShifts },
        editOptions,
      },
    },
  },
  { mode }
) => {
  let options = [];
  let tableData = [];
  if (mode === 'new') {
    options = [...periodShifts.value, { dutyName: '休息', id: 0 }];
    tableData = workTableData;
  } else {
    tableData = editOptions.period.workTableData;
    options = [...editOptions.period.periodShifts.value, { dutyName: '休息', id: 0 }];
  }
  return {
    workTableData: tableData,
    selectedShifts: options,
  };
};

const mapDispatchToProps = {
  updatePeriodWorkTable: staffShiftActions.updatePeriodWorkTable,
  updateEditPeriodWorkTable: staffShiftActions.updateEditPeriodWorkTable,
};
export default connect(mapStateToProps, mapDispatchToProps)(WorkDdayAndShift);

const columns = (ctx, selectedShifts) => [
  {
    title: '天数',
    key: 'days',
    render: (_, record, index) => {
      return `第${index + 1}天`;
    },
  },
  {
    title: '班次名称',
    dataIndex: 'dutyName',
    render: (_, record) => {
      return (
        <Select value={record.shiftId} onChange={value => ctx.onChangeShift(value, record)}>
          {selectedShifts.map(({ dutyName, id }, index) => {
            return (
              <Select.Option key={index} value={id} label={dutyName}>
                {dutyName}
              </Select.Option>
            );
          })}
        </Select>
      );
    },
  },
  {
    title: '班次时间',
    dataIndex: 'shiftId',
    render: (_, record) => {
      const shiftInfo = selectedShifts.filter(({ id }) => id === record.shiftId);
      if (record.shiftId === 0) {
        return '--';
      } else {
        if (shiftInfo[0].offIsNextDay) {
          return `${shiftInfo[0].onDutyTime}-次日${shiftInfo[0].offDutyTime}`;
        }
        return `${shiftInfo[0].onDutyTime}-${shiftInfo[0].offDutyTime}`;
      }
    },
  },
];
