import React, { Component } from 'react';

import { Modal } from '@manyun/base-ui.ui.modal';

import { TinyTable } from '@manyun/dc-brain.legacy.components';
import * as generateUrls from '@manyun/dc-brain.legacy.utils/urls';

class AttGroupTable extends Component {
  render() {
    const { attGroupTableVisiable, attGroupListByShifts } = this.props;
    return (
      <Modal
        destroyOnClose
        width={480}
        title="考勤组列表"
        visible={attGroupTableVisiable}
        footer={null}
        onCancel={() => this.props.updateAttGroupTableVisiable(false)}
      >
        <TinyTable
          rowKey="id"
          dataSource={attGroupListByShifts}
          columns={getShiftsColumns(this)}
          pagination={false}
        />
      </Modal>
    );
  }
}
export default AttGroupTable;

const getShiftsColumns = ctx => [
  {
    title: '考勤组名称',
    dataIndex: 'attName',
  },
  {
    title: '操作',
    key: '__actions',
    width: 150,
    render: (__, record) => {
      const { pathname, search } = generateUrls.generateAttGroupScheduleEditLocation({
        id: record.id,
        groupName: record.attName,
      });
      return (
        <a href={pathname + search} rel="noopener noreferrer" target="_blank">
          重新排班
        </a>
      );
    },
  },
];
