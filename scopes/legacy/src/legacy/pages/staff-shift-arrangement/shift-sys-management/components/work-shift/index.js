import React from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Tag } from '@manyun/base-ui.ui.tag';

import { TagColor } from '@manyun/dc-brain.legacy.pages/staff-shift-arrangement/constants';

export default function WorkShift({ value, changeVisible }) {
  return (
    <>
      {(!value || !value.length) && (
        <Button
          type="primary"
          onClick={() => {
            changeVisible(true);
          }}
        >
          选择班次
        </Button>
      )}
      {value && value.length > 0 && (
        <div
          style={{
            display: 'inline-flex',
            flexWrap: 'wrap',
            gap: '8px 0',
            alignItems: 'center',
            minHeight: 32,
            width: '100%',
            padding: 4,
            cursor: 'pointer',
            borderRadius: 4,
            border: '1px solid var(--border-color-base)',
          }}
          onClick={() => {
            changeVisible(true);
          }}
        >
          {value.map(({ id, dutyName, onDutyTime, offDutyTime, offIsNextDay }, index) => {
            if (offIsNextDay) {
              return (
                <Tag
                  key={id}
                  color={TagColor[index % 5]}
                >{`班次${dutyName}：${onDutyTime}-次日${offDutyTime}`}</Tag>
              );
            }
            return (
              <Tag
                key={id}
                color={TagColor[index % 5]}
              >{`班次${dutyName}：${onDutyTime}-${offDutyTime}`}</Tag>
            );
          })}
        </div>
      )}
    </>
  );
}
