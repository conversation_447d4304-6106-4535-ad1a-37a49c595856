import React, { Component } from 'react';
import { connect } from 'react-redux';

import moment from 'moment';
import shortid from 'shortid';

import { Button } from '@manyun/base-ui.ui.button';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Input } from '@manyun/base-ui.ui.input';

import { TinyCard, TinyTable, UserLink } from '@manyun/dc-brain.legacy.components';
import {
  deleteShiftSysActionCreator,
  getAttGroupListByShiftsActionCreator,
  getShiftSysDataActionCreator,
  staffShiftActions,
} from '@manyun/dc-brain.legacy.redux/actions/staffShiftActions';

import AttGroupTable from './components/att-group-modal';
import EhanceShiftSysCreate from './components/new-shift-sys-modal';
import { SHITF_SYS_TYPE_KEY_MAP, SHITF_SYS_TYPE_TEXT_MAP } from './constants';

class ShiftSysManageList extends Component {
  state = {
    mode: 'new',
    attGroupTableVisiable: false,
  };

  componentWillUnmount() {
    const { updateSearchValues, updatePagination } = this.props;
    updatePagination({ pageNum: 1, pageSize: 10 });
    updateSearchValues({});
  }

  componentDidMount() {
    this.props.getData();
  }

  onChangeValues = ({ target: { value } }) => {
    this.props.updateSearchValues({ shiftsName: value.trim() });
  };

  onChangePage = (pageNum, pageSize) => {
    this.props.updatePagination({ pageNum, pageSize });
    this.props.getData(false);
  };

  editShift = record => {
    this.setState({
      mode: 'edit',
    });
    this.props.getAttGroupListByShifts(record.id);
    const workDay = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期天'];
    const weekTable =
      record.shiftsType === SHITF_SYS_TYPE_KEY_MAP.WEEK
        ? record.dailyDuty.map((item, index) => {
            if (item) {
              return {
                checked: true,
                code: index + 1,
                shift: `班次${item.dutyName}: ${item.onDutyTime}-${item.offDutyTime}`,
                workTime: workDay[index],
              };
            } else {
              return {
                checked: false,
                code: index + 1,
                shift: `休息`,
                workTime: workDay[index],
              };
            }
          })
        : [];
    const periodTable =
      record.shiftsType === SHITF_SYS_TYPE_KEY_MAP.PERIOD
        ? record.dailyDuty.map(item => {
            if (item) {
              return {
                code: shortid(),
                shiftId: item.id,
              };
            } else {
              return {
                code: shortid(),
                shiftId: 0,
              };
            }
          })
        : [];
    let edit = {
      id: record.id,
      shiftsName: {
        value: record.shiftsName,
        name: 'shiftsName',
      },
      shiftsType: {
        value: record.shiftsType,
      },
      week: {
        checkedShiftConfirm:
          record.shiftsType === SHITF_SYS_TYPE_KEY_MAP.WEEK ? record.dutys[0] : null,
        workTableData: weekTable,
        enableStatutoryHoliday: {
          value: record.enableStatutoryHoliday,
        },
      },
      period: {
        periodShifts:
          record.shiftsType === SHITF_SYS_TYPE_KEY_MAP.PERIOD
            ? {
                value: record.dutys,
                dirty: false,
                name: 'periodShifts',
                touched: false,
              }
            : { value: [] },
        workTableData: periodTable,
        periodDays: {
          value: record.periodDays,
        },
      },
    };
    this.props.updateEditInfos(edit);
    this.props.updateCreateVisiable(true);
  };

  getHourAndMinutes = time => {
    let tmp = [];
    if (time) {
      const [merchant] = String(time / 60).split('.');
      const surplus = time % 60;
      tmp = [Number(merchant) * 60, surplus];
    }
    return tmp;
  };

  getCompensateHours = hours => {
    let tmp = [];
    if (hours && hours.length) {
      tmp = hours.map(item => {
        const [lateGo, lateArrive] = item.split(',');
        return {
          id: shortid.generate(),
          lateGo,
          lateArrive,
        };
      });
    }
    return tmp;
  };

  render() {
    const { pagination, loading, data, total, editOptions, getData } = this.props;
    const { mode, attGroupTableVisiable } = this.state;
    return (
      <TinyCard>
        <TinyTable
          rowKey="id"
          dataSource={data}
          loading={loading}
          columns={columns(this)}
          align={'left'}
          scroll={{ x: 'max-content' }}
          pagination={{
            total: total,
            current: pagination.pageNum,
            pageSize: pagination.pageSize,
            onChange: this.onChangePage,
          }}
          actionsWrapperStyle={{ justifyContent: 'space-between' }}
          actions={[
            <Button
              key="create-shift"
              type="primary"
              onClick={() => {
                this.props.resetCreateOptions();
                this.props.updateCreateVisiable(true);
                this.setState({ mode: 'new' });
              }}
            >
              新建班制
            </Button>,
            <Input.Search
              key="seach"
              style={{ width: 211, height: 32 }}
              allowClear
              placeholder="请输入班制名称"
              onSearch={getData}
              onChange={this.onChangeValues}
            />,
          ]}
        />
        <EhanceShiftSysCreate
          mode={mode}
          updateAttGroupTableVisiable={value => this.setState({ attGroupTableVisiable: value })}
        />
        <AttGroupTable
          attGroupTableVisiable={attGroupTableVisiable}
          attGroupListByShifts={editOptions.attGroupListByShifts}
          updateAttGroupTableVisiable={value => this.setState({ attGroupTableVisiable: value })}
        />
      </TinyCard>
    );
  }
}

const mapStateToProps = ({
  staffShift: {
    shiftSys: { pagination, loading, data, total, editOptions },
  },
}) => {
  return {
    pagination,
    loading,
    data,
    total,
    editOptions,
  };
};
const mapDispatchToProps = {
  getData: getShiftSysDataActionCreator,
  setShiftSysDataAndTotal: staffShiftActions.setShiftSysDataAndTotal,
  updateSearchValues: staffShiftActions.updateShiftSysSearchValues,
  updatePagination: staffShiftActions.updateShiftSysPagination,
  delete: deleteShiftSysActionCreator,
  updateCreateVisiable: staffShiftActions.updateShiftSysCreateVisiable,
  resetCreateOptions: staffShiftActions.resetCreateShiftSysOptions,
  updateEditInfos: staffShiftActions.updateEditShiftSysysInfos,
  getAttGroupListByShifts: getAttGroupListByShiftsActionCreator,
};
export default connect(mapStateToProps, mapDispatchToProps)(ShiftSysManageList);

const columns = ctx => [
  {
    title: '班制名称',
    dataIndex: 'shiftsName',
    render: (text, record) => (
      <Button type="link" onClick={() => ctx.editShift(record)}>
        {text}
      </Button>
    ),
  },
  {
    title: '班制类型',
    dataIndex: 'shiftsType',
    render: shiftsType => {
      return SHITF_SYS_TYPE_TEXT_MAP[shiftsType];
    },
  },

  {
    title: '修改时间',
    dataIndex: 'gmtModified',
    dataType: 'dateTime',
    render: gmtModified => {
      return moment(gmtModified).format('YYYY-MM-DD HH:mm:ss');
    },
  },
  {
    title: '修改人',
    key: 'modifierId',
    render: (_, record) => <UserLink userId={record.modifierId} userName={record.modifierName} />,
  },
  {
    title: '操作',
    key: '__actions',
    fixed: 'right',
    render: (__, record) => (
      <span>
        <Button compact type="link" onClick={() => ctx.editShift(record)}>
          编辑
        </Button>
        <Divider type="vertical" />
        <DeleteConfirm
          variant="popconfirm"
          targetName={record.shiftsName}
          onOk={() => ctx.props.delete({ id: record.id })}
        >
          <Button compact type="link">
            删除
          </Button>
        </DeleteConfirm>
      </span>
    ),
  },
];
