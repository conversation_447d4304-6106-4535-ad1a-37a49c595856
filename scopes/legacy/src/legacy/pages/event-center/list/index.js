import { ApiSelect, FiltersForm, Form } from '@galiojs/awesome-antd';
import moment from 'moment';
import React, { Component, useMemo } from 'react';
import { connect, useDispatch } from 'react-redux';
import { Link } from 'react-router-dom';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import { getLocationSearchMap, setLocationSearch } from '@manyun/base-ui.util.query-string';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { DeviceSelect } from '@manyun/resource-hub.ui.device-select';
import { DeviceTypeCascader } from '@manyun/resource-hub.ui.device-type-cascader';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';
import { RoomSelect } from '@manyun/resource-hub.ui.room-select';
import { RoomTypeSelect } from '@manyun/resource-hub.ui.room-type-select';
import {
  BackendFalseAlarm,
  EventSpecificProcessStatus,
  EventStatusValueMap,
  getEventLocales,
} from '@manyun/ticket.model.event';
import { FalseAlarmTypeSelect } from '@manyun/ticket.page.event';

import {
  ApiTreeSelect,
  EventTable,
  GutterWrapper,
  TinyCard,
} from '@manyun/dc-brain.legacy.components';
import { METADATA_TYPE } from '@manyun/dc-brain.legacy.constants';
import * as urls from '@manyun/dc-brain.legacy.constants/urls';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  eventCenterActions,
  getEventList,
  getMetadataByType,
} from '@manyun/dc-brain.legacy.redux/actions/eventCenterActions';
import {
  fetchEventClose,
  fetchEventOpen,
  fetchEventStatus,
} from '@manyun/dc-brain.legacy.services/eventCenterService';
import { fetchEventType } from '@manyun/dc-brain.legacy.services/treeDataService';
import { fetchUserListByKey } from '@manyun/dc-brain.legacy.services/userManageService';

// 解析curHandler参数的函数
const parseCurHandler = handlerArray => {
  if (!Array.isArray(handlerArray) || handlerArray.length !== 2) {
    return null; // 如果不是长度为2的数组，返回null或者抛出错误
  }

  // 假设数组格式为 ['label=admin', 'value=1']
  const [labelPart, valuePart] = handlerArray;
  const label = labelPart.split('=')[1]; // 获取等号后面的部分
  const value = parseInt(valuePart.split('=')[1], 10); // 转换为数字

  // 检查转换是否成功
  if (typeof label === 'string' && !isNaN(value)) {
    return { label, value };
  }

  return null; // 如果解析失败，返回null或者抛出错误
};
class EventCenterList extends Component {
  state = {
    liablePersonList: [],
    notifySubscribeList: [],
  };

  componentDidMount() {
    const { form, updateSearchValues } = this.props;
    this.props.form.resetFields();
    updateSearchValues({ ...form.getFieldsValue() });
    const { idcs, blocks, eventStatus, curHandler, owner, pageNum, pageSize } =
      getLocationSearchMap(window.location.search);
    const initialSearchValue = getParseValuesFromQueryString();
    const initialPagination = {
      pageNum: pageNum ? Number(pageNum) : 1,
      pageSize: pageSize ? Number(pageSize) : 10,
    };
    const idcSearchParams = Array.isArray(idcs) ? idcs : [idcs];
    const blocksSearchParams = Array.isArray(blocks) ? blocks : [blocks];
    this.props.syncCommonData({ strategy: { eventTypes: 'IF_NULL' } });
    this.getEventSource();

    if (idcs || blocks || curHandler || owner) {
      const searchParams = {
        pageNum: 1,
        pageSize: 10,
        eventStatus: eventStatus,
      };

      if (curHandler) {
        searchParams.curHandlerId = parseCurHandler(curHandler).value;
        initialSearchValue.curHandlerId = parseCurHandler(curHandler);
      }
      if (owner) {
        searchParams.ownerName = parseCurHandler(owner).label;
        initialSearchValue.ownerName = parseCurHandler(owner);
      }
      if (idcs) {
        searchParams.idcTags = idcSearchParams;
        initialSearchValue.location = [idcs];
        initialSearchValue.idcTags = idcSearchParams;
      }
      if (blocks) {
        searchParams.blockTags = blocksSearchParams;
        initialSearchValue.blockTags = blocksSearchParams;
        initialSearchValue.location = [blocks];
      }

      if (initialSearchValue.occurBeginTime) {
        searchParams.occurBeginTime = initialSearchValue.occurBeginTime?.[0];
        searchParams.occurEndTime = initialSearchValue.occurBeginTime?.[1];
      }
      if (initialSearchValue.eventLevelList) {
        searchParams.eventLevelList = initialSearchValue.eventLevelList;
      }
      // 外部通过route方法跳转进列表页的，将url格式进行重置
      setLocationSearch(
        {
          location: idcs ? [idcs] : blocks && [blocks],
          eventStatus: eventStatus ? JSON.stringify(eventStatus) : undefined,
          curHandlerId: curHandler,
          ownerName: owner,
          pageNum: initialPagination.pageNum,
          pageSize: initialPagination.pageSize,
        },
        { skipNull: true, skipEmptyString: true }
      );
      this.props.getEventList(searchParams);
      form.setFieldsValue({
        ...initialSearchValue,
        curHandlerId: parseCurHandler(curHandler),
        ownerName: parseCurHandler(owner),
        occurBeginTime: initializeDatePicker(initialSearchValue.occurBeginTime),
        eventStatus,
      });
      return;
    } else {
      const apiQ = getApiQ({ ...initialSearchValue });
      this.props.getEventList({
        ...apiQ,
        ...initialPagination,
      });
      form.setFieldsValue({
        ...initialSearchValue,
        location: initialSearchValue.location,
        blockTags: [],
        idcTags: [],
        occurBeginTime: initializeDatePicker(initialSearchValue.occurBeginTime),
        createBeginTime: initializeDatePicker(initialSearchValue.createBeginTime),
        closeBeginTime: initializeDatePicker(initialSearchValue.closeBeginTime),
      });
    }
  }

  componentWillUnmount() {
    this.props.form.resetFields();
    this.props.updateSearchValues({ ...this.props.form.getFieldsValue() });
  }

  getUserList = async value => {
    const data = await fetchUserListByKey(value);
    if (data.response) {
      this.setState({ userList: data.response.data });
    }
  };

  changeEventTable = (pagination, _, sorter) => {
    const { current, pageSize } = pagination;
    const { field, order } = sorter;

    const { pageNum, pageSize: urlSize, ...rest } = getLocationSearchMap(window.location.search);

    setLocationSearch(
      { ...rest, pageNum: current, pageSize },
      {
        skipNull: true,
        skipEmptyString: true,
      }
    );
    if (field === 'eventLevelName') {
      const params = this.getParams();
      this.props.getEventList({
        ...params,
        pageNum: current,
        pageSize,
        orderByEventLevelDesc: order ? order === 'descend' : undefined,
      });
    } else if (field === 'closeTime') {
      const params = this.getParams();
      this.props.getEventList({
        ...params,
        pageNum: current,
        pageSize,
        orderByCloseTimeDesc: order ? order === 'descend' : undefined,
      });
    } else if (field === 'occurTime') {
      const params = this.getParams();
      this.props.getEventList({
        ...params,
        pageNum: current,
        pageSize,
        orderByOccurTimeDesc: order ? order === 'descend' : undefined,
      });
    } else {
      const params = this.getParams();
      this.props.getEventList({
        ...params,
        pageNum: current,
        pageSize,
        orderByGmtCreateDesc: order ? order === 'descend' : undefined,
      });
    }
  };

  handleReset = () => {
    this.props.dispatch(eventCenterActions.resetSearchValues());
    setLocationSearch({
      pageNum: 1,
      pageSize: 10,
    });
    this.props.getEventList({
      pageNum: 1,
      pageSize: 10,
    });
  };

  handleSearch = fields => {
    const {
      eventStatus,
      categorys,
      falseAlarms,
      ownerName,
      createUserId,
      curHandlerId,
      closeBeginTime,
      createBeginTime,
      occurBeginTime,
      ...rest
    } = fields;
    const arrayDataHandler = data =>
      Array.isArray(data) && data.length > 0 ? JSON.stringify(data) : undefined;
    const mergedParams = {
      ...rest,
      createUserId: createUserId
        ? `label=${encodeURIComponent(createUserId.label)},value=${encodeURIComponent(
            createUserId.value
          )}`
        : undefined,
      curHandlerId: curHandlerId
        ? `label=${encodeURIComponent(curHandlerId.label)},value=${encodeURIComponent(
            curHandlerId.value
          )}`
        : undefined,
      ownerName: ownerName
        ? `label=${encodeURIComponent(ownerName.label)},value=${encodeURIComponent(
            ownerName.value
          )}`
        : undefined,
      eventStatus: arrayDataHandler(eventStatus),
      categorys: arrayDataHandler(categorys),
      falseAlarms: arrayDataHandler(falseAlarms),
      closeBeginTime: arrayDataHandler(closeBeginTime),
      createBeginTime: arrayDataHandler(createBeginTime),
      occurBeginTime: arrayDataHandler(occurBeginTime),
      // majorCodeList: arrayDataHandler(majorCodeList),
      pageNum: 1,
      pageSize: 10,
    };
    setLocationSearch(
      { ...mergedParams },
      {
        skipNull: true,
        skipEmptyString: true,
      }
    );
    this.props.getEventList({
      pageNum: 1,
      pageSize: 10,
      ...getApiQ(fields),
    });
  };

  getParams = () => {
    const initialSearchValue = getParseValuesFromQueryString();
    return getApiQ(initialSearchValue);
  };

  getOperationStatus = () => {
    this.props.fetchGetOperationStatus();
  };

  getEventSource = () => {
    if (this.props.eventSourceList.length) {
      return false;
    }
    this.props.getMetadataByType(METADATA_TYPE.EVENT_SOURCE);
  };

  onClose = async id => {
    const { response, error } = await fetchEventClose({ eventId: id });
    if (response) {
      const params = this.getParams();
      this.props.getEventList({ pageNum: 1, pageSize: 10, ...params });
    }
    if (error) {
      message.error(error);
    }
  };

  onOpen = async id => {
    const { response, error } = await fetchEventOpen({ eventId: id });
    if (response) {
      const params = this.getParams();
      this.props.getEventList({ pageNum: 1, pageSize: 10, ...params });
    }
    if (error) {
      message.error(error);
    }
  };

  shouldFilterItemNotShowInGOC = name =>
    !(
      (this.props.featuresTitle === 'disabled' && name === 'eventTitle') ||
      (this.props.featuresNorthbound === 'disabled' && name === 'northSync') ||
      (this.props.featuresIsEventConfigWithProcessEngine === 'required' &&
        name === 'relieveDesc') ||
      (this.props.featuresIsEventConfigWithProcessEngine === 'required' &&
        name === 'resolveDesc') ||
      (this.props.featuresIsEventConfigWithProcessEngine === 'required' && name === 'id') ||
      (this.props.featuresIsEventConfigWithProcessEngine === 'disabled' && name === 'eventNo') ||
      (this.props.featuresIsEventConfigWithProcessEngine === 'required' && name === 'curHandlerId')
    );

  render() {
    const { eventCenterList, eventSourceList, updateSearchValues, searchValues, featuresListInfo } =
      this.props;
    const isEventConfigWithProcessEngineRequired =
      this.props.featuresIsEventConfigWithProcessEngine === 'required';
    return (
      <GutterWrapper mode="vertical">
        <TinyCard>
          <FiltersForm
            form={this.props.form}
            fields={Object.keys(searchValues).map(name => {
              const field = searchValues[name];
              return {
                ...field,
                // name 为数组形式
                name: name.split('.'),
              };
            })}
            items={
              featuresListInfo
                ? [
                    {
                      label: '事件标题',
                      name: 'eventTitle',
                      control: <Input allowClear />,
                    },
                    {
                      label: '楼栋',
                      name: 'location',
                      control: (
                        <LocationTreeSelect
                          authorizedOnly
                          allowClear
                          includeVirtualBlocks
                          multiple
                        />
                      ),
                    },
                    {
                      label: '事件状态',
                      name: 'eventStatus',
                      control: (
                        <Select
                          showSearch
                          allowClear
                          mode="multiple"
                          maxTagCount={1}
                          options={this.props.eventStatusOptions}
                        />
                      ),
                    },
                    {
                      label: '事件级别',
                      name: 'eventLevelList', //eventLevel
                      control: (
                        <MetaTypeSelect
                          showSearch
                          metaType="EVENT_LEVEL"
                          mode="multiple"
                          allowClear
                          maxTagCount="responsive"
                        />
                      ),
                    },
                    {
                      label: '事件ID',
                      name: 'eventId',

                      control: <Input allowClear />,
                    },
                    {
                      label: '事件来源',
                      name: 'eventSourceList',
                      control: (
                        <Select allowClear showSearch mode="multiple" onFocus={this.getEventSource}>
                          {eventSourceList.map(item => (
                            <Select.Option key={item.metaCode}>{item.metaName}</Select.Option>
                          ))}
                        </Select>
                      ),
                    },
                    {
                      label: '事件专业',
                      name: 'majorCodeList',
                      control: (
                        <MetaTypeSelect
                          showSearch
                          metaType="EVENT_MAJOR"
                          mode="multiple"
                          allowClear
                        />
                      ),
                    },
                    {
                      label: '事件类型',
                      name: 'categorys',
                      control: (
                        <ApiTreeSelect
                          dataService={fetchEventType}
                          fieldNames={{
                            value: ({ metaCode, metaType }) => metaType + '_$_' + metaCode,
                            key: ({ metaCode, metaType }) => metaType + '_$_' + metaCode,
                            title: 'metaName',
                          }}
                          multiple
                          requestOnDidMount
                        />
                      ),
                    },
                    {
                      label: '设备类型',
                      name: 'deviceType',
                      control: (
                        <DeviceTypeCascader
                          numbered
                          dataType={['snDevice']}
                          category="categorycode"
                          disabledTypeList={['C0', 'C1']}
                          // placeholder="请选择设备类型"
                          // onChange={(value: string) => {
                          //   onSelectDevice(value);
                          // }}
                          allowClear
                          // style={{ width: 216 }}
                        />
                      ),
                    },

                    {
                      label: '故障设备',
                      name: 'deviceGuid',
                      control: (
                        <DeviceSelect
                          fieldNames={{
                            label: 'name',
                            value: 'guid',
                          }}
                          style={{ width: 200 }}
                          allowClear
                        />
                      ),
                    },
                    {
                      label: '包间类型',
                      name: 'roomType',
                      control: <RoomTypeSelect allowClear showSearch mode="multiple" />,
                    },
                    {
                      label: '故障包间',
                      name: 'roomGuid',
                      control: <RoomSelect allowClear showSearch />,
                    },
                    {
                      label: '责任人',
                      name: 'ownerId',
                      control: <UserSelect allowClear labelInValue={false} />,
                    },
                    // {
                    //   label: '是否误报',
                    //   name: 'falseAlarms',
                    //   control: <FalseAlarmTypeSelect mode="multiple" allowClear />,
                    // },
                    // {
                    //   label: '事件描述',
                    //   name: 'key',
                    //   control: <Input allowClear placeholder="请输入内容" />,
                    // },

                    {
                      label: '创建人',
                      name: 'createUserId',
                      control: <UserSelect allowClear />,
                    },

                    // {
                    //   label: '处理人',
                    //   name: 'curHandlerId',
                    //   control: <UserSelect allowClear />,
                    // },
                    {
                      label: '创建时间',
                      name: 'createBeginTime',
                      control: (
                        <DatePicker.RangePicker
                          showTime={{ format: 'HH:mm' }}
                          format="YYYY-MM-DD HH:mm"
                          placeholder={['开始时间', '结束时间']}
                        />
                      ),
                      span: 2,
                    },
                    {
                      label: '发生时间',
                      name: 'occurBeginTime',
                      control: (
                        <DatePicker.RangePicker
                          showTime={{ format: 'HH:mm' }}
                          format="YYYY-MM-DD HH:mm"
                          placeholder={['开始时间', '结束时间']}
                        />
                      ),
                      span: 2,
                    },
                    {
                      label: '关闭时间',
                      name: 'closeBeginTime',
                      control: (
                        <DatePicker.RangePicker
                          showTime={{ format: 'HH:mm' }}
                          format="YYYY-MM-DD HH:mm"
                          placeholder={['开始时间', '结束时间']}
                        />
                      ),
                      span: 2,
                    },
                  ]
                : [
                    {
                      label: '事件ID',
                      name: 'eventId',
                      rules: !isEventConfigWithProcessEngineRequired && [
                        {
                          pattern: /^[0-9]+$/,
                          message: '事件ID必须为数字',
                        },
                      ],
                      control: <Input allowClear />,
                    },
                    {
                      label: '位置',
                      name: 'location',
                      control: (
                        <LocationTreeSelect
                          authorizedOnly
                          allowClear
                          includeVirtualBlocks
                          multiple
                        />
                      ),
                    },
                    {
                      label: '事件状态',
                      name: 'eventStatus',
                      control: isEventConfigWithProcessEngineRequired ? (
                        <Select
                          showSearch
                          allowClear
                          mode="multiple"
                          maxTagCount={1}
                          options={this.props.eventStatusOptions}
                        />
                      ) : (
                        <ApiSelect
                          showSearch
                          allowClear
                          mode="multiple"
                          maxTagCount={1}
                          trigger="onDidMount"
                          fieldNames={{ label: 'desc', value: 'code' }}
                          dataService={eventStatusOptionsDataService}
                        />
                      ),
                    },
                    {
                      label: '事件级别',
                      name: 'eventLevelList', //eventLevel
                      control: (
                        <MetaTypeSelect
                          showSearch
                          metaType="EVENT_LEVEL"
                          mode="multiple"
                          allowClear
                          maxTagCount="responsive"
                        />
                      ),
                    },
                    {
                      label: '事件来源',
                      name: 'eventSourceList',
                      control: (
                        <Select allowClear showSearch mode="multiple" onFocus={this.getEventSource}>
                          {eventSourceList.map(item => (
                            <Select.Option key={item.metaCode}>{item.metaName}</Select.Option>
                          ))}
                        </Select>
                      ),
                    },

                    {
                      label: '事件类型',
                      name: 'categorys',
                      control: (
                        <ApiTreeSelect
                          dataService={fetchEventType}
                          fieldNames={{
                            value: ({ metaCode, metaType }) => metaType + '_$_' + metaCode,
                            key: ({ metaCode, metaType }) => metaType + '_$_' + metaCode,
                            title: 'metaName',
                          }}
                          multiple
                          requestOnDidMount
                        />
                      ),
                    },
                    {
                      label: '北向同步',
                      name: 'northSync',
                      control: (
                        <Select
                          allowClear
                          options={[
                            { label: '是', value: true, key: true },
                            { label: '否', value: false, key: false },
                          ]}
                        />
                      ),
                    },
                    {
                      label: '是否误报',
                      name: 'falseAlarms',
                      control: <FalseAlarmTypeSelect mode="multiple" allowClear />,
                    },
                    {
                      label: '事件标题',
                      name: 'eventTitle',
                      control: <Input allowClear />,
                    },
                    {
                      label: '事件描述',
                      name: 'key',
                      control: <Input allowClear placeholder="请输入内容" />,
                    },
                    {
                      label: '设备名称',
                      name: 'deviceGuid',
                      control: (
                        <DeviceSelect
                          fieldNames={{
                            label: 'name',
                            value: 'guid',
                          }}
                          style={{ width: 200 }}
                          allowClear
                        />
                      ),
                    },
                    {
                      label: '包间及其他',
                      name: 'relateName',
                      control: <Input style={{ width: 200 }} allowClear />,
                    },
                    {
                      label: '创建人',
                      name: 'createUserId',
                      control: <UserSelect allowClear />,
                    },

                    {
                      label: isEventConfigWithProcessEngineRequired ? '负责人' : 'Owner',
                      name: 'ownerName',
                      control: <UserSelect allowClear />,
                    },
                    {
                      label: '处理人',
                      name: 'curHandlerId',
                      control: <UserSelect allowClear />,
                    },
                    {
                      label: '创建时间',
                      name: 'createBeginTime',
                      control: (
                        <DatePicker.RangePicker
                          showTime={{ format: 'HH:mm:ss' }}
                          format="YYYY-MM-DD HH:mm:ss"
                          placeholder={['开始时间', '结束时间']}
                        />
                      ),
                      span: 2,
                    },
                    {
                      label: '发生时间',
                      name: 'occurBeginTime',
                      control: (
                        <DatePicker.RangePicker
                          showTime={{ format: 'HH:mm:ss' }}
                          format="YYYY-MM-DD HH:mm:ss"
                          placeholder={['开始时间', '结束时间']}
                        />
                      ),
                      span: 2,
                    },
                    {
                      label: '关闭时间',
                      name: 'closeBeginTime',
                      control: (
                        <DatePicker.RangePicker
                          showTime={{ format: 'HH:mm:ss' }}
                          format="YYYY-MM-DD HH:mm:ss"
                          placeholder={['开始时间', '结束时间']}
                        />
                      ),
                      span: 2,
                    },
                  ].filter(item => this.shouldFilterItemNotShowInGOC(item.name))
            }
            onSearch={fields => {
              this.handleSearch(fields);
            }}
            onReset={this.handleReset}
            onFieldsChange={changedFields => {
              if (changedFields.some(field => field.name.join('.') === 'idcTags')) {
                updateSearchValues({ blockTags: { name: 'blockTags', value: [] } });
              }
              updateSearchValues(
                changedFields.reduce((mapper, field) => {
                  // field.name 为数组形式，老代码需要的是字符串形式
                  const name = field.name.join('.');
                  mapper[name] = {
                    value: field.value,
                    name,
                  };
                  return mapper;
                }, {})
              );
            }}
          />
        </TinyCard>
        <TinyCard>
          <EventTable
            editColumnsPersistenceKey="LEGACY_PAGE_EVENT_CENTER_LIST"
            exportSearchParams={this.getParams()}
            exportButtonPersistenceKey
            configurePersistenceKey
            showColumns={[
              'id',
              'eventNo',
              'blockTag',
              'eventSourceName',
              'eventLevelName',
              'topCategoryName',
              'secondCategoryName',
              'infoType',
              'deviceModels',
              'eventDesc',
              'status',
              'relieveDesc',
              'resolveDesc',
              'gmtCreate',
              'closeTime',
              'isFalseAlarm',
              'eventOwnerId',
              'curHandlerId',
              'createUserId',
              'northSync',
              'eventTitle',
              'occurTime',
              'emergencyDuration',
              'phaseRecordList',
              'detectReason',
              'eventInfluence',
              'firstReportTime',
              'lastReportTime',
              'majorName',
              'eventLocation',
            ].filter(item => this.shouldFilterItemNotShowInGOC(item))}
            operation={{
              title: '操作',
              dataIndex: 'operation',
              width: 100,
              fixed: 'right',
              render: (_, record) => {
                const status = record.eventStatus
                  ? Number(EventStatusValueMap[record.eventStatus.code])
                  : null;
                let content = null;
                let btnButton = null;
                let onOk = null;
                let disabled = true;
                let cancelText = null;
                let okText = null;
                if (status <= 5) {
                  return '--';
                }

                if (status > 5) {
                  if (record.isFalseAlarm === BackendFalseAlarm.None) {
                    content = (
                      <div>
                        重启后事件将回退至“已解决”状态，需
                        <br />
                        重新提交复盘，是否确认重启？
                      </div>
                    );
                    onOk = this.onOpen;
                    btnButton = '重启';
                    disabled = false;
                    cancelText = '我在想想';
                    okText = '确认重启';
                  } else {
                    return '--';
                  }
                }
                return (
                  <span>
                    <DeleteConfirm
                      canOpen={() => Promise.resolve(!disabled)}
                      variant="popconfirm"
                      title={content}
                      popconfirmProps={{
                        okText: okText,
                        cancelText: cancelText,
                      }}
                      onOk={() => onOk(record.id)}
                    >
                      <Button
                        type="link"
                        disabled={disabled}
                        style={{ padding: 0, height: 'auto' }}
                      >
                        {btnButton}
                      </Button>
                    </DeleteConfirm>
                  </span>
                );
              },
            }}
            dataSource={eventCenterList.list}
            loading={eventCenterList.loading}
            actions={
              <Link target="_blank" to={{ pathname: urls.EVENT_CENTER_NEW }}>
                <Button type="primary">新建</Button>
              </Link>
            }
            pagination={{
              total: eventCenterList.total,
              current: eventCenterList.pageNum,
              pageSize: eventCenterList.pageSize,
            }}
            onChange={(pagination, filters, sorter) => {
              this.changeEventTable(pagination, filters, sorter);
            }}
          />
        </TinyCard>
      </GutterWrapper>
    );
  }
}

function EventCenterListWrapper(props) {
  const locales = useMemo(() => {
    return getEventLocales();
  }, []);
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  // const config = useSelector(selectCurrentConfig);

  // const configUtil = new ConfigUtil(config);
  const [configUtil] = useConfigUtil();
  const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket');
  const featuresTitle = ticketScopeCommonConfigs.events.features.title;
  const featuresNorthbound = ticketScopeCommonConfigs.events.features.northbound;
  const featuresIsEventConfigWithProcessEngine =
    ticketScopeCommonConfigs.events.features.isEventConfigWithProcessEngine;
  const featuresListInfo = ticketScopeCommonConfigs.events.features.listInfo;
  const eventStatusOptions = useMemo(() => {
    if (locales) {
      return Object.keys(locales.specificProcessStatus)
        .filter(item => item !== '__self' && item !== EventSpecificProcessStatus.Init)
        .map(item => ({
          label: locales.specificProcessStatus[item],
          value: item,
        }));
    }
    return [];
  }, [locales]);
  return (
    <EventCenterList
      featuresNorthbound={featuresNorthbound}
      featuresTitle={featuresTitle}
      featuresIsEventConfigWithProcessEngine={featuresIsEventConfigWithProcessEngine}
      form={form}
      dispatch={dispatch}
      eventStatusOptions={eventStatusOptions}
      featuresListInfo={featuresListInfo}
      {...props}
    />
  );
}

const mapStateToProps = ({
  eventCenter: {
    eventCenterList,
    eventSourceList,
    eventResponsibleSectorList,
    eventSourceNormalized,
    searchValues,
  },
  common: { eventTypes },
}) => ({
  eventCenterList,
  eventSourceList,
  eventResponsibleSectorList,
  eventSourceNormalized: eventSourceNormalized || {},
  eventTypes: eventTypes ? eventTypes.normalizedList : {},
  searchValues,
});
const mapDispatchToProps = {
  getEventList: getEventList,
  syncCommonData: syncCommonDataActionCreator,
  getMetadataByType: getMetadataByType,
  updateSearchValues: eventCenterActions.updateSearchValues,
};

export default connect(mapStateToProps, mapDispatchToProps)(EventCenterListWrapper);

async function eventStatusOptionsDataService() {
  const { response } = await fetchEventStatus();
  if (response) {
    return response.data;
  } else {
    return [];
  }
}

function getApiQ(filedValues) {
  const {
    location,
    categorys,
    createBeginTime,
    closeBeginTime,
    eventId,
    ownerName,
    curHandlerId,
    createUserId,
    occurBeginTime,
    ...rest
  } = filedValues;
  const topCategorys = [];
  const secondCategorys = [];
  categorys?.forEach(item => {
    const categorysValue = item.split('_$_');
    if (categorysValue[0] === METADATA_TYPE.EVENT_TOP_CATEGORY) {
      topCategorys.push(categorysValue[1]);
    }
    if (categorysValue[0] === METADATA_TYPE.EVENT_SECOND_CATEGORY) {
      secondCategorys.push(categorysValue[1]);
    }
  });
  // const spaceGuid = location?.split('.');
  const idcTags = location ? location.filter(item => item.split('.').length === 1) : [];
  const blockTags = location ? location.filter(item => item.split('.').length === 2) : [];

  return {
    ...rest,
    createUserId: createUserId?.value,
    ownerName: ownerName?.label,
    curHandlerId: curHandlerId?.value,
    eventId: eventId,
    createBeginTime: createBeginTime?.[0],
    createEndTime: createBeginTime?.[1],
    closeBeginTime: closeBeginTime?.[0],
    closeEndTime: closeBeginTime?.[1],
    topCategorys: topCategorys,
    secondCategorys: secondCategorys,
    blockTags: blockTags,
    idcTags: idcTags,
    occurBeginTime: occurBeginTime?.[0],
    occurEndTime: occurBeginTime?.[1],
  };
}

function initializeDatePicker(timeValue) {
  return Array.isArray(timeValue) && timeValue.length > 1
    ? timeValue.map(date => moment(date))
    : [];
}

function getParseValuesFromQueryString() {
  const {
    pageNum,
    pageSize,
    eventStatus,
    categorys,
    falseAlarms,
    ownerName,
    curHandlerId,
    createUserId,
    closeBeginTime,
    createBeginTime,
    occurBeginTime,
    ...rest
  } = getLocationSearchMap(window.location.search, {
    arrayKeys: [
      'eventLevelList',
      'location',
      'eventSourceList',
      'majorCodeList',
      'roomType',
      // 'secondCategorys',
      // 'topCategorys',
    ],
  });

  let initialSearchValue = {};
  try {
    const searchValuesJSON = {
      ...rest,
      eventStatus: eventStatus ? JSON.parse(eventStatus) : undefined,
      categorys: categorys ? JSON.parse(categorys) : undefined,
      falseAlarms: falseAlarms ? JSON.parse(falseAlarms) : undefined,
      ownerName: ownerName
        ? Array.isArray(ownerName) && ownerName.length === 2
          ? parseCurHandler(ownerName)
          : JSON.parse(ownerName)
        : undefined,
      curHandlerId: curHandlerId
        ? Array.isArray(curHandlerId) && curHandlerId.length === 2
          ? parseCurHandler(curHandlerId)
          : JSON.parse(curHandlerId)
        : undefined,
      createUserId: createUserId
        ? Array.isArray(createUserId) && createUserId.length === 2
          ? parseCurHandler(createUserId)
          : JSON.parse(createUserId)
        : undefined,
      closeBeginTime: closeBeginTime ? JSON.parse(closeBeginTime) : undefined,
      createBeginTime: createBeginTime ? JSON.parse(createBeginTime) : undefined,
      occurBeginTime: occurBeginTime ? JSON.parse(occurBeginTime) : undefined,
    };
    initialSearchValue = searchValuesJSON;
  } catch (error) {}
  return initialSearchValue;
}
