import React from 'react';
import { Draggable, Droppable } from 'react-beautiful-dnd';

import CloseOutlined from '@ant-design/icons/es/icons/CloseOutlined';
import DragOutlined from '@ant-design/icons/es/icons/DragOutlined';
import styled from 'styled-components';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { InputNumber } from '@manyun/base-ui.ui.input-number';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';
import CustomNameDropped from '@manyun/dc-brain.legacy.pages/merged-processed-point/new/components/config-card/merge-point/custom-name-drop';
import NameDropped from '@manyun/dc-brain.legacy.pages/merged-processed-point/new/components/config-card/merge-point/name-drop';

import { EXPR_ITEM_TYPE } from '../../constants';
import ActiveDropHandle from './active-drop-handle';

const Container = styled(GutterWrapper)`
  display: inline-block;
  padding: 4px 8px;
  text-align: ${({ textAlign = 'center' }) => textAlign};
  vertical-align: middle;
  min-height: 32px;
  min-width: ${({ minWidth = 32 }) => (typeof minWidth == 'string' ? minWidth : minWidth + 'px')};
  border-radius: 4px;
  border: 1px solid var(--${prefixCls}-primary-color);
  background: ${({ background }) =>
    background ? background : `var(--${prefixCls}-primary-color)`};

  .expr_draggable-item--trash-handle {
    display: none;
  }

  :hover {
    .expr_draggable-item--trash-handle {
      display: inline-block;
    }
  }
`;

const NestedDropZone = styled(GutterWrapper)`
  display: inline-block;
  min-width: 32px;
  min-height: 16px;

  background: ${({ dragOver }) => (dragOver ? `var(--${prefixCls}-success-color)` : 'transparent')};
`;

export function ConditionalDraggable({
  type,
  id,
  value,
  name = value,
  childItems = [],
  pointCode,
  index,
  disabledDrag,
  showRemoveHandle,
  activeDroppableId,
  onActiveHandleClick,
  onNumberChange,
  onRemoveHandleClick,
  processedType,
  treeMode,
  customSpacePointHost,
}) {
  let droppableId;
  let disabledDrop;
  switch (type) {
    case EXPR_ITEM_TYPE.STATISTICAL_FUNCTION:
    case EXPR_ITEM_TYPE.PARENTHESES:
      droppableId = 'nested-droppable_$$_' + id;
      disabledDrop = activeDroppableId !== droppableId;

      return (
        <DefaultDraggable
          background="transparent"
          disabledDrag={disabledDrag}
          showRemoveHandle={showRemoveHandle}
          id={id}
          index={index}
          onRemoveHandleClick={onRemoveHandleClick}
        >
          {provided => (
            <>
              <ActiveDropHandle
                clsPrefix="expr_draggable-item"
                locked={disabledDrop}
                onClick={() => {
                  onActiveHandleClick(droppableId);
                }}
              />
              {type === EXPR_ITEM_TYPE.STATISTICAL_FUNCTION && (
                <span {...provided.dragHandleProps}>{name}</span>
              )}
              {type === EXPR_ITEM_TYPE.PARENTHESES && !disabledDrag && (
                <span
                  style={{ transform: 'scale(.75)' }}
                  className="manyun-btn manyun-btn-sm manyun-btn-circle expr_draggable-item--drag-handle"
                  {...provided.dragHandleProps}
                >
                  <DragOutlined />
                </span>
              )}
              (
              <Droppable
                isDropDisabled={disabledDrop}
                droppableId={droppableId}
                direction="horizontal"
              >
                {(provided, { isDraggingOver }) => (
                  <NestedDropZone
                    ref={provided.innerRef}
                    {...provided.droppableProps}
                    dragOver={isDraggingOver}
                    size="4px"
                  >
                    {childItems.map(({ children, ...item }, index) => (
                      <ConditionalDraggable
                        key={item.id}
                        {...item}
                        treeMode={treeMode}
                        childItems={children}
                        pointCode={pointCode}
                        index={index}
                        disabledDrag={disabledDrop}
                        showRemoveHandle={showRemoveHandle}
                        activeDroppableId={activeDroppableId}
                        processedType={processedType}
                        customSpacePointHost={customSpacePointHost}
                        onActiveHandleClick={onActiveHandleClick}
                        onNumberChange={onNumberChange}
                        onRemoveHandleClick={onRemoveHandleClick}
                      />
                    ))}
                    {provided.placeholder}
                  </NestedDropZone>
                )}
              </Droppable>
              )
            </>
          )}
        </DefaultDraggable>
      );
    case EXPR_ITEM_TYPE.NUMBER:
      return (
        <DefaultDraggable
          disabledDrag={disabledDrag}
          showRemoveHandle={showRemoveHandle}
          id={id}
          index={index}
          onRemoveHandleClick={onRemoveHandleClick}
        >
          {provided => (
            <>
              {!disabledDrag && (
                <span
                  style={{ transform: 'scale(.75)' }}
                  className="manyun-btn manyun-btn-sm manyun-btn-circle expr_draggable-item--drag-handle"
                  {...provided.dragHandleProps}
                >
                  <DragOutlined />
                </span>
              )}
              <InputNumber
                style={{ width: 102 }}
                size="small"
                value={value}
                max={10000000}
                min={0.01}
                precision={2}
                onChange={num => {
                  onNumberChange(id, num);
                }}
              />
            </>
          )}
        </DefaultDraggable>
      );
    case EXPR_ITEM_TYPE.DELIMITER:
      return (
        <DefaultDraggable disabledDrag id={id} index={index}>
          {() => <span>{name}</span>}
        </DefaultDraggable>
      );
    case EXPR_ITEM_TYPE.ARITHMETIC_OPERATOR:
      return (
        <DefaultDraggable
          selfDragHandle
          disabledDrag={disabledDrag}
          showRemoveHandle={showRemoveHandle}
          id={id}
          index={index}
          onRemoveHandleClick={onRemoveHandleClick}
        >
          {() => <span>{name}</span>}
        </DefaultDraggable>
      );
    case EXPR_ITEM_TYPE.PARAMETER:
      return (
        <PointDraggable
          selfDragHandle
          disabledDrag={disabledDrag}
          id={id}
          pointCode={pointCode}
          type={type}
          isParameter
          index={index}
          showRemoveHandle={showRemoveHandle}
          processedType={processedType}
          treeMode={treeMode}
          value={value}
          name={name}
          customSpacePointHost={customSpacePointHost}
          onRemoveHandleClick={onRemoveHandleClick}
        />
      );
    case 'device-property':
      return (
        <PointDraggable
          selfDragHandle
          disabledDrag={disabledDrag}
          id={id}
          pointCode={pointCode}
          type={type}
          isParameter
          index={index}
          showRemoveHandle={showRemoveHandle}
          processedType={processedType}
          treeMode={treeMode}
          value={value}
          name={name}
          customSpacePointHost={customSpacePointHost}
          onRemoveHandleClick={onRemoveHandleClick}
        />
      );
    default:
      return (
        <PointDraggable
          selfDragHandle
          disabledDrag={disabledDrag}
          id={id}
          pointCode={pointCode}
          //和后端确认 测点前俩位是'K_'，目前这是测点和参数的唯一区别，只能通过此方式判断
          isParameter={value ? !(value.slice(0, 2) === 'K_') : false}
          type={type}
          index={index}
          showRemoveHandle={showRemoveHandle}
          processedType={processedType}
          treeMode={treeMode}
          value={value}
          name={name}
          customSpacePointHost={customSpacePointHost}
          onRemoveHandleClick={onRemoveHandleClick}
        />
      );
  }
}

export default ConditionalDraggable;

function DefaultDraggable({
  background,
  disabledDrag,
  showRemoveHandle,
  selfDragHandle,
  id,
  index,
  onRemoveHandleClick,
  children,
}) {
  return (
    <Draggable isDragDisabled={disabledDrag} draggableId={id} index={index}>
      {provided => (
        <Container
          ref={provided.innerRef}
          {...provided.draggableProps}
          {...(selfDragHandle ? provided.dragHandleProps : {})}
          size="4px"
          background={background}
        >
          {children(provided)}
          {showRemoveHandle && (
            <Button
              style={{ transform: 'scale(.63333)' }}
              className="expr_draggable-item--trash-handle"
              size="small"
              shape="circle"
              ghost={background !== 'transparent'}
              onClick={() => {
                onRemoveHandleClick(id);
              }}
            >
              <CloseOutlined />
            </Button>
          )}
        </Container>
      )}
    </Draggable>
  );
}

function PointDraggable({
  disabledDrag,
  selfDragHandle,
  id,
  pointCode,
  type,
  index,
  isParameter,
  showRemoveHandle,
  background,
  onRemoveHandleClick,
  processedType,
  value,
  treeMode,
  name,
  customSpacePointHost,
}) {
  return (
    <Draggable isDragDisabled={disabledDrag} draggableId={id} index={index}>
      {provided => (
        <Container
          ref={provided.innerRef}
          textAlign="left"
          {...provided.draggableProps}
          {...(selfDragHandle ? provided.dragHandleProps : {})}
        >
          {treeMode === 'custom' ? (
            <CustomNameDropped
              style={{ width: 200 }}
              pointCode={pointCode}
              name={name}
              idx={index}
              value={value}
              id={id}
              type={type}
              dataType="AI"
              customSpacePointHost={customSpacePointHost}
              showAllNode
            />
          ) : (
            <NameDropped
              style={{ width: 200 }}
              pointType={processedType}
              idx={index}
              isParameter={isParameter}
              value={{ key: value, label: name }}
              type={type}
              id={id}
              showAllNode
            />
          )}
          {showRemoveHandle && (
            <Button
              style={{ transform: 'scale(.63333)' }}
              className="expr_draggable-item--trash-handle"
              size="small"
              shape="circle"
              ghost={background !== 'transparent'}
              onClick={() => {
                onRemoveHandleClick(id);
              }}
            >
              <CloseOutlined />
            </Button>
          )}
        </Container>
      )}
    </Draggable>
  );
}
