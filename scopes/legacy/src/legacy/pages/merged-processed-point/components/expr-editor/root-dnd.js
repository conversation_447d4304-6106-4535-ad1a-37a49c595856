import React, { useState } from 'react';
import { Droppable } from 'react-beautiful-dnd';

import DeleteOutlined from '@ant-design/icons/es/icons/DeleteOutlined';
import styled from 'styled-components';

import { UnTrashableTwoTone } from '@manyun/base-ui.icons';
import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Switch } from '@manyun/base-ui.ui.switch';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';

import styles from '../../styles.module.less';
import ActiveDropHandle from './active-drop-handle';
import ConditionalDraggable from './conditional-draggable';
import { ROOT_DROPPABLE_ID } from './constants';

const commonStyle = `
  display: block;
  width: 100%;
  overflow-x: auto;
  white-space: nowrap;
  font-size: 14px;
  line-height: 1.5;
  border-left-width: 1px;
  border-right-width: 1px;
  border-style: solid;
`;

const RootDropZoneWrapper = styled.div`
  ${commonStyle}
  min-height: 64px;
  padding: 4px 11px 0;
  border-top-width: 1px;
  border-bottom-width: 0;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;

  background: ${({ dragOver }) => (dragOver ? `var(--${prefixCls}-success-color)` : 'transparent')};

  > * + * {
    margin-left: 4px;
  }
`;

const RootDropZoneActionWrapper = styled.div`
  ${commonStyle}
  text-align: right;
  min-height: 32px;
  padding: 4px;
  border-top-width: 0;
  border-bottom-width: 1px;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;

  > * + * {
    margin-left: 4px;
  }
`;

export function RootDnd({
  extra,
  pointCode,
  activeDroppableId = ROOT_DROPPABLE_ID,
  data = [],
  onActiveHandleClick,
  onNumberChange,
  onRemoveHandleClick,
  processedType,
  treeMode,
  customSpacePointHost,
}) {
  const [showRemoveHandle, setShowRemoveHandle] = useState(false);
  const isRootDroppableActivated = activeDroppableId === ROOT_DROPPABLE_ID;

  return (
    <div>
      <Droppable
        isDropDisabled={!isRootDroppableActivated}
        droppableId={ROOT_DROPPABLE_ID}
        direction="horizontal"
      >
        {(provided, { isDraggingOver }) => (
          <RootDropZoneWrapper
            className={styles.border}
            ref={provided.innerRef}
            {...provided.droppableProps}
            dragOver={isDraggingOver}
          >
            {data.map(({ children, ...item }, index) => (
              <ConditionalDraggable
                key={item.id}
                {...item}
                treeMode={treeMode}
                childItems={children}
                pointCode={pointCode}
                index={index}
                disabledDrag={!isRootDroppableActivated}
                showRemoveHandle={showRemoveHandle}
                activeDroppableId={activeDroppableId}
                onActiveHandleClick={onActiveHandleClick}
                onNumberChange={onNumberChange}
                onRemoveHandleClick={onRemoveHandleClick}
                processedType={processedType}
                customSpacePointHost={customSpacePointHost}
              />
            ))}
            {provided.placeholder}
          </RootDropZoneWrapper>
        )}
      </Droppable>
      <RootDropZoneActionWrapper className={styles.border}>
        <ActiveDropHandle
          clsPrefix="expr_root-droppable"
          locked={!isRootDroppableActivated}
          onClick={() => {
            onActiveHandleClick(ROOT_DROPPABLE_ID);
          }}
        />
        <GutterWrapper style={{ display: 'inline-block' }}>
          <Switch
            checkedChildren={
              <Tooltip placement="topLeft" title="已启用删除功能">
                <DeleteOutlined />
              </Tooltip>
            }
            unCheckedChildren={
              <Tooltip placement="topLeft" title="已禁用删除功能">
                <UnTrashableTwoTone />
              </Tooltip>
            }
            onChange={setShowRemoveHandle}
          />
        </GutterWrapper>
        {extra}
      </RootDropZoneActionWrapper>
    </div>
  );
}

export default RootDnd;
