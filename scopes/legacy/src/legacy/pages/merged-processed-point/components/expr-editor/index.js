import React from 'react';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';

import { DndItems } from './dnd-items';
import { RootDnd } from './root-dnd';

/**
 * @typedef {import('./dnd-items').DndItemsProps} DndItemsProps
 * @typedef {import('./../../new/components/config-card/merge-point/custom-name-drop').CustomNameDroppedProps} CustomNameDroppedProps
 *
 * @typedef {DndItemsProps & Pick<CustomNameDroppedProps, 'customSpacePointHost'>} ExprEditorProps
 *
 * @augments {React.PureComponent<ExprEditorProps>}
 */
export class ExprEditor extends React.PureComponent {
  render() {
    const {
      extraAction,
      activeDroppableId,
      onActiveHandleClick,
      onNumberChange,
      onRemoveHandleClick,
      info,
      value,
      processedType,
      treeMode,
      customSpacePointHost,
      showParameterPointDraggable,
      showDevicePointDraggable,
      showSpacePointDraggable,
      showCustomSpacePointDraggable,
      showBlockAttribute,
      draggableDescriptorMutator,
    } = this.props;
    const { pointCode } = info;
    return (
      <GutterWrapper direction="vertical">
        <RootDnd
          treeMode={treeMode}
          extra={extraAction}
          pointCode={pointCode}
          activeDroppableId={activeDroppableId}
          data={value}
          processedType={processedType}
          customSpacePointHost={customSpacePointHost}
          onActiveHandleClick={onActiveHandleClick}
          onNumberChange={onNumberChange}
          onRemoveHandleClick={onRemoveHandleClick}
        />
        <DndItems
          showParameterPointDraggable={showParameterPointDraggable}
          showDevicePointDraggable={showDevicePointDraggable}
          showSpacePointDraggable={showSpacePointDraggable}
          showCustomSpacePointDraggable={showCustomSpacePointDraggable}
          showBlockAttribute={showBlockAttribute}
          draggableDescriptorMutator={draggableDescriptorMutator}
        />
      </GutterWrapper>
    );
  }
}

export default ExprEditor;
