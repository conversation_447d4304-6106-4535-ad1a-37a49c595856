import React from 'react';

import { UnLockFilled } from '@manyun/base-ui.icons';
import { LockFilled } from '@manyun/base-ui.icons';
import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';

/**
 * @param {object} props
 * @param {string} props.clsPrefix className prefix
 * @param {boolean} props.locked true -> 上锁（红色）；false -> 解锁（绿色）
 * @param {Function} props.onClick
 */
export default function ActiveDropHandle({ clsPrefix, locked, onClick }) {
  const color = locked ? `var(--${prefixCls}-error-color)` : `var(--${prefixCls}-success-color)`;
  const title = locked ? '此区域已锁定，无法拖放元素' : '此区域已解锁，可以拖放元素';

  return (
    <Tooltip title={title} mouseEnterDelay={1}>
      <Button
        style={{ transform: 'scale(.83333)', color, borderColor: color }}
        className={clsPrefix + '--active-drop-handle'}
        size="small"
        shape="circle"
        onClick={onClick}
      >
        {locked ? <LockFilled /> : <UnLockFilled />}
      </Button>
    </Tooltip>
  );
}
