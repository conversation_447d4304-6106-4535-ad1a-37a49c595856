import cloneDeep from 'lodash/cloneDeep';
import uniq from 'lodash/uniq';
import React from 'react';
import { Draggable, Droppable } from 'react-beautiful-dnd';
import styled from 'styled-components';

import { Tag } from '@manyun/base-ui.ui.tag';

import { GutterWrapper, TinyCard } from '@manyun/dc-brain.legacy.components';

import {
  ARITHMETIC_OPERATORS,
  EXPR_ITEM_TYPE,
  OTHERS,
  STATISTICAL_FUNCTIONS,
} from '../../constants';
import { TPL_ITEMS_DROPPABLE_ID } from './constants';

const cards = [
  {
    key: 'arithmetic-operators',
    title: '算术运算符',
    data: ARITHMETIC_OPERATORS,
  },
  {
    key: 'statistical-functions',
    title: '统计函数',
    data: STATISTICAL_FUNCTIONS,
  },
  {
    key: 'others',
    title: '其他',
    data: OTHERS,
  },
];

const StyledTinyCard = styled(TinyCard)`
  flex: 1;
`;

function DndItem({ id, text, index }) {
  return (
    <Draggable draggableId={id} index={index}>
      {provided => (
        <div ref={provided.innerRef} {...provided.draggableProps} {...provided.dragHandleProps}>
          <Tag> {text}</Tag>
        </div>
      )}
    </Draggable>
  );
}

function CardItem({ title, data, prevIdx }) {
  return (
    <StyledTinyCard bordered size="small" title={title}>
      {/* todo fix text color */}
      <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8 }}>
        {data.map(({ id, value, name = value }, index) => (
          <DndItem key={id} id={id} text={name} index={prevIdx + index} />
        ))}
      </div>
    </StyledTinyCard>
  );
}

/**
 * @typedef DraggableDescriptor
 * @property {string} id
 * @property {string} type
 * @property {string} value
 * @property {string} name
 * 
 * @typedef DndItemsProps
 * @property {boolean} [showDevicePointDraggable=true] 是否显示设备测点拖拽组件
 * @property {boolean} [showSpacePointDraggable=false] 是否显示空间测点拖拽组件
 * @property {boolean} [showCustomSpacePointDraggable=false] 是否显示自定义空间测点
 * @property {(draggableDescriptor: DraggableDescriptor) => draggableDescriptor} [draggableDescriptorMutator] 用于改变 `draggableDescriptor`，需要 memoize
 * /

/** 
 * @param {DndItemsProps} props
 * @returns 
 */
export function DndItems({
  showDevicePointDraggable = true,
  showSpacePointDraggable = false,
  showCustomSpacePointDraggable = false,
  showParameterPointDraggable = false,
  showBlockAttribute = false,
  draggableDescriptorMutator,
}) {
  const [_cards, setCards] = React.useState(cards);
  React.useEffect(() => {
    setCards(() =>
      cloneDeep(cards).map(card => {
        if (card.key === 'others') {
          if (showParameterPointDraggable === false) {
            card.data = card.data.filter(
              draggableDescriptor => draggableDescriptor.type !== EXPR_ITEM_TYPE.PARAMETER
            );
          }
          if (showDevicePointDraggable === false) {
            card.data = card.data.filter(
              draggableDescriptor => draggableDescriptor.type !== EXPR_ITEM_TYPE.POINT
            );
          }
          if (showSpacePointDraggable === false) {
            card.data = card.data.filter(
              draggableDescriptor => draggableDescriptor.type !== EXPR_ITEM_TYPE.SPACE_POINT
            );
          }
          if (showCustomSpacePointDraggable === false) {
            card.data = card.data.filter(
              draggableDescriptor => draggableDescriptor.type !== EXPR_ITEM_TYPE.CUSTOM_SPACE_POINT
            );
          }
          if (showBlockAttribute === false) {
            card.data = card.data.filter(
              draggableDescriptor => draggableDescriptor.type !== EXPR_ITEM_TYPE.BLOCK_ATTRIBUTE
            );
          }
        }

        return {
          ...card,
          data: card.data.map(draggableDescriptorMutator),
        };
      })
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    showDevicePointDraggable,
    showSpacePointDraggable,
    showCustomSpacePointDraggable,
    draggableDescriptorMutator,
  ]);

  return (
    <Droppable isDropDisabled droppableId={TPL_ITEMS_DROPPABLE_ID} direction="horizontal">
      {provided => (
        <GutterWrapper ref={provided.innerRef} {...provided.droppableProps} flex>
          {_cards.map((item, index) => (
            <CardItem
              {...item}
              key={item.key}
              prevIdx={
                index === 0
                  ? 0
                  : uniq([0, index - 1]).reduce((len, cardIdx) => {
                      len += cards[cardIdx].data.length;

                      return len;
                    }, 0)
              }
            />
          ))}
          {provided.placeholder}
        </GutterWrapper>
      )}
    </Droppable>
  );
}

export default DndItems;
