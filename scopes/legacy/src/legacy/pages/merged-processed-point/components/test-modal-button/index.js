import React, { useState } from 'react';
import { connect } from 'react-redux';

import get from 'lodash/get';

import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { List } from '@manyun/base-ui.ui.list';
import { Typography } from '@manyun/base-ui.ui.typography';

import { ModalButton } from '@manyun/dc-brain.legacy.components';
import { mergedProcessesdPointActions } from '@manyun/dc-brain.legacy.redux/reducers/mergedProcessesdPointSlice';

const text = '验证';

/**
 * @param {object} props
 * @param {boolean} props.disabled Button `disabled`
 * @param {Array<{ pointKey: string; pointName: string }>} props.data
 * @param {string} props.result
 * @param {(valueMap?: {[poineKey: string]: number }) => void} props.onOk
 */
export function TestModalButton({ disabled, data = [], result, onOk, resetTestResult }) {
  const [valueMap, setValueMap] = useState(null);

  const okBtnDisabled =
    valueMap === null ||
    data.some(({ pointKey }) => [undefined, null].includes(valueMap[pointKey]));

  return (
    <ModalButton
      size="small"
      disabled={disabled}
      text={text}
      title={text}
      width={400}
      cancelText="关闭"
      okButtonProps={{ disabled: okBtnDisabled }}
      okText="执行计算"
      onCancel={() => {
        setValueMap(null);
        resetTestResult();
      }}
      onOk={() => {
        onOk(valueMap);

        return false;
      }}
    >
      {() => (
        <List
          dataSource={data}
          renderItem={({ pointKey, pointName }) => (
            <List.Item>
              <Typography.Text>{pointName}</Typography.Text>
              <InputNumber
                size="small"
                max={100000}
                formatter={value => {
                  if (value && String(value).length > 100) {
                    return Number(String(value).slice(0, 100));
                  }
                  return value;
                }}
                parser={value => {
                  if (value && String(value).length > 100) {
                    return Number(String(value).slice(0, 100));
                  }
                  return value;
                }}
                value={get(valueMap, pointKey)}
                onChange={num => {
                  setValueMap(prev => {
                    if (prev === null) {
                      return { [pointKey]: num };
                    }
                    return { ...prev, [pointKey]: num };
                  });
                }}
              />
            </List.Item>
          )}
          footer={<span style={{ color: result.color }}>{result.txt}</span>}
        />
      )}
    </ModalButton>
  );
}

const mapDispatchToProps = dispatch => ({
  resetTestResult: () =>
    dispatch({ type: mergedProcessesdPointActions.setTestExprResult.type, payload: '' }),
});

export default connect(null, mapDispatchToProps)(TestModalButton);
