import React from 'react';

import { InputNumber } from '@manyun/base-ui.ui.input-number';

export class ValidLimits extends React.Component {
  render() {
    const { value, onChange, disabled } = this.props;
    let ge = null;
    let le = null;
    if (value) {
      ge = value.ge;
    }
    if (value) {
      le = value.le;
    }
    return (
      <>
        <InputNumber
          disabled={disabled}
          value={ge || typeof ge === 'number' ? Number(ge) : null}
          max={100000000}
          min={-100000000}
          onChange={num => {
            let limits = { ge: num };
            if (value) {
              limits = { ...value, ...limits };
            }
            onChange(limits);
          }}
        />
        &nbsp;&nbsp;-&nbsp;&nbsp;
        <InputNumber
          disabled={disabled}
          value={le || typeof le === 'number' ? Number(le) : null}
          max={100000000}
          min={-100000000}
          onChange={num => {
            let limits = { le: num };
            if (value) {
              limits = { ...value, ...limits };
            }
            onChange(limits);
          }}
        />
      </>
    );
  }
}

export default React.forwardRef((props, ref) => <ValidLimits forwardedRef={ref} {...props} />);
