import saveAs from 'file-saver';
import React, { useEffect, useRef, useState } from 'react';
import { DragDropContext } from 'react-beautiful-dnd';
import { connect } from 'react-redux';
import { useLocation, useParams } from 'react-router-dom';
import shallowequal from 'shallowequal';
import shortid from 'shortid';

import { useAuthorized } from '@manyun/auth-hub.hook.gql.use-authorized';
import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';
import { CUSTOM_POINT_IMPORT_ROUTE_PATH } from '@manyun/resource-hub.route.resource-routes';
import { exportCustomPoint } from '@manyun/resource-hub.service.export-custom-point';
import { exportPoint } from '@manyun/resource-hub.service.export-point';

import { <PERSON><PERSON><PERSON><PERSON>per, TinyC<PERSON> } from '@manyun/dc-brain.legacy.components';
import { POINT_TYPE_CODE_MAP } from '@manyun/dc-brain.legacy.constants/point';
import { doPointActions } from '@manyun/dc-brain.legacy.redux/actions/doPointActions';
import {
  changeExpressInfos,
  exprInsertPointIntoNestedActionCreator,
  exprInsertPointIntoRootActionCreator,
  mergedProcessesdPointActions,
} from '@manyun/dc-brain.legacy.redux/actions/mergedProcessesdPointActions';

import { ROOT_DROPPABLE_ID, TPL_ITEMS_DROPPABLE_ID } from '../components/expr-editor/constants';
import { EXPR_ITEM_TYPE, POINT_TREE_DROPPABLE_ID, TPL_ITEMS } from '../constants';
import DeviceCard from './components/points-card';
import TreeCard from './tree-card';

export function MergedProcessPointDetail({
  pointData,
  changeExpressInfos,
  exprInsertIntoRoot,
  exprInsertIntoNested,
  exprSortInRoot,
  exprSortInNest,
  exprInsertPointIntoRoot,
  exprInsertPointIntoNested,
  selectCategory,
  deviceCategory,
  updateFormValues,
  /*自定义测点页面*/
  isOnlyShowCustom = false,
}) {
  const [, { checkCode }] = useAuthorized();
  const { search, state } = useLocation();
  const { idc } = useParams();
  const { tab = 'device' } = getLocationSearchMap(search);
  const [exportCustomLoading, setExportCustomLoading] = useState(false);
  const [exportOriLoading, setExportOriLoading] = useState(false);
  const [exportSpaceCalAggLoading, setExportSpaceCalAggLoading] = useState(false);
  const [exportDeviceCalAggLoading, setExportDeviceCalAggLoading] = useState(false);

  const deviceApiTreeRef = useRef(); // 设备类型树的ref
  const customApiTreeRef = useRef(); // 设备类型树的ref
  const [treeMode, setTreeMode] = useState(tab); // 左侧树所处模式，device 设备类型/space 空间类型,为空间类型时，右侧表格不可查看规格表格
  const [cardMode, setCardMode] = useState('basic'); // 右侧表格模式 ，basic 基本信息/point 测点列表/ specs 规格列表，当 cardMode === specs 时，左侧树只能时device
  const [selectedTab, setSelectedTab] = useState('orginal'); //原始测点，加工测点，聚合测点，规格，基本信息
  const [pattern, setPattern] = useState('list'); // 记录当前模式 ,list - 列表，create - 新增，edit - 编辑，detail - 详情
  const [deviceReRequest, setDeviceReRequest] = useState(0); // 是否重新请求设备树得数据
  const [customerTreeData, setCustomerTreeData] = useState(null);

  const defaultExpandedKeys = state ? state.defaultExpandedKeys : [];
  const cardHeight = `calc(var(--content-height) - 58.14px ${
    pointData && (cardMode === 'basic' || pattern !== 'list') ? '- 32px' : ''
  })`;

  function refreshDeviceTreeData() {
    if (!deviceApiTreeRef.current) {
      return;
    }
    deviceApiTreeRef.current.refreshData();
  }

  function refresCustomDeviceTreeData() {
    if (!customApiTreeRef.current) {
      return;
    }
    customApiTreeRef.current.refreshData();
  }

  useEffect(() => {
    //地址栏带tab参数或自定义测点页面时处理
    if (isOnlyShowCustom) {
      setTreeMode('custom');
      setCardMode('point');
      setPattern('list');
      return;
    }
    if (tab === 'space' || tab === 'custom') {
      setCardMode('point');
      setPattern('list');
    } else {
      setCardMode('basic');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  useEffect(() => {
    // 设备类型，空间类型切换时，清空缓存的选择的设备类型（切换了，类型树中会不存在该类型）
    selectCategory({
      selectTitle: '',
      selectType: '',
      deviceType: '',
    });
    if (treeMode === 'device') {
      setSelectedTab('orginal');
    }
  }, [treeMode, selectCategory, setSelectedTab]);

  useEffect(() => {
    refreshDeviceTreeData();
  }, [deviceCategory]);

  const dragEndHandler = ({ source, destination, draggableId }) => {
    // console.log({ source, destination, draggableId });
    if (!destination || shallowequal(source, destination)) {
      return;
    }
    if (destination.droppableId === ROOT_DROPPABLE_ID) {
      _rootDndHandler({ source, destination, draggableId });
      return;
    }

    if (destination.droppableId.startsWith('nested-droppable_$$_')) {
      _nestedDndHandler({ source, destination, draggableId });
      return;
    }

    changeExpressInfos({ source, destination, dragValue: draggableId });
  };

  const _rootDndHandler = ({ source, destination, draggableId }) => {
    switch (source.droppableId) {
      // 新增元素(算术运算符、统计函数、其他)
      case TPL_ITEMS_DROPPABLE_ID:
        _insertIntoRoot(destination.index, draggableId);
        break;
      case ROOT_DROPPABLE_ID:
        _sortInRoot(source.index, destination.index);
        break;
      case POINT_TREE_DROPPABLE_ID:
        _insertPointIntoRoot(destination.index, draggableId);
        break;
      default:
        break;
    }
  };

  const _nestedDndHandler = ({ source, destination, draggableId }) => {
    switch (source.droppableId) {
      // 新增元素(算术运算符、统计函数、其他)
      case TPL_ITEMS_DROPPABLE_ID:
        _insertIntoNested(destination, draggableId);
        break;
      case destination.droppableId:
        _sortInNest(destination.droppableId, source.index, destination.index);
        break;
      case POINT_TREE_DROPPABLE_ID:
        _insertPointIntoNested(destination, draggableId);
        break;
      default:
        break;
    }
  };

  /** @type {import('./../../../../components/expr-editor/dnd-items').DndItemsProps['draggableDescriptorMutator']} */
  const draggableDescriptorMutator = React.useCallback(
    draggableDescriptor => {
      if (treeMode !== 'custom' && draggableDescriptor.type === EXPR_ITEM_TYPE.POINT) {
        draggableDescriptor.name = '测点';
      }

      return draggableDescriptor;
    },
    [treeMode]
  );

  const _getNewTplItem = draggableId => {
    const { type, value, name } = TPL_ITEMS.map(draggableDescriptorMutator).find(
      ({ id }) => id === draggableId
    );
    return {
      id: draggableId + '.' + shortid(),
      type,
      value,
      name,
    };
  };

  const _insertIntoRoot = (index, draggableId) => {
    const item = _getNewTplItem(draggableId);
    exprInsertIntoRoot({ index, item });
  };

  const _sortInRoot = (fromIdx, toIdx) => {
    exprSortInRoot({ fromIdx, toIdx });
  };

  const _insertPointIntoRoot = (index, draggableId) => {
    exprInsertPointIntoRoot({ index, draggableId });
  };

  const _insertIntoNested = ({ droppableId, index }, draggableId) => {
    const item = _getNewTplItem(draggableId);
    const [, parentItemId] = droppableId.split('_$$_');
    exprInsertIntoNested({ parentItemId, index, item });
  };

  const _sortInNest = (parentDroppableId, fromIdx, toIdx) => {
    const [, parentItemId] = parentDroppableId.split('_$$_');
    exprSortInNest({ parentItemId, fromIdx, toIdx });
  };

  const _insertPointIntoNested = ({ droppableId, index }, draggableId) => {
    const [, parentItemId] = droppableId.split('_$$_');
    exprInsertPointIntoNested({ parentItemId, index, draggableId });
  };

  const onHandleExportPoint = async ({ pointTypeList, exportFileName, loading }) => {
    if (loading === 'ori') {
      setExportOriLoading(true);
    }
    if (loading === 'deviceCalAgg') {
      setExportDeviceCalAggLoading(true);
    }
    if (loading === 'spaceCalAgg') {
      setExportSpaceCalAggLoading(true);
    }
    const { error, data } = await exportPoint({ pointTypeList });
    if (loading === 'ori') {
      setExportOriLoading(false);
    }
    if (loading === 'deviceCalAgg') {
      setExportDeviceCalAggLoading(false);
    }
    if (loading === 'spaceCalAgg') {
      setExportSpaceCalAggLoading(false);
    }

    if (error) {
      message.error(error.message);
      return;
    }
    saveAs(data, `${exportFileName}.xlsx`);
  };

  const onHandleExportCustomPoint = async () => {
    setExportCustomLoading(true);
    const { error, data } = await exportCustomPoint({ targetGuid: idc, idcTag: idc });
    setExportCustomLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    saveAs(data, '自定义测点.xlsx');
  };

  return (
    <DragDropContext onDragEnd={dragEndHandler}>
      <GutterWrapper flex>
        <div style={{ width: '330px', height: cardHeight }}>
          <TreeCard
            height={cardHeight}
            defaultExpandedKeys={defaultExpandedKeys}
            deviceApiTreeRef={deviceApiTreeRef}
            customApiTreeRef={customApiTreeRef}
            refreshDeviceTreeData={refreshDeviceTreeData}
            treeMode={treeMode}
            selectCategory={selectCategory}
            setTreeMode={setTreeMode}
            cardMode={cardMode}
            setCardMode={setCardMode}
            setPattern={setPattern}
            setSelectedTab={setSelectedTab}
            deviceReRequest={deviceReRequest}
            refresCustomDeviceTreeData={refresCustomDeviceTreeData}
            setCustomerTreeData={setCustomerTreeData}
            isOnlyShowCustom={isOnlyShowCustom}
            idc={idc}
          />
        </div>

        {pointData || (treeMode === 'custom' && !customerTreeData) ? (
          <DeviceCard
            height={cardHeight}
            treeMode={treeMode}
            setTreeMode={setTreeMode}
            cardMode={cardMode}
            setCardMode={setCardMode}
            pattern={pattern}
            setPattern={setPattern}
            refreshDeviceTreeData={refreshDeviceTreeData}
            refresCustomDeviceTreeData={refresCustomDeviceTreeData}
            selectedTab={selectedTab}
            setSelectedTab={setSelectedTab}
            setDeviceReRequest={() => setDeviceReRequest(count => count + 1)}
            draggableDescriptorMutator={draggableDescriptorMutator}
          />
        ) : (
          <GutterWrapper flexN={1} mode="vertical">
            <TinyCard bodyStyle={{ height: `calc(${cardHeight} + 58.14px - 1px)` }}>
              <GutterWrapper style={{ height: '100%' }} flex center>
                {treeMode === 'custom' ? (
                  <Space>
                    <Button
                      type="primary"
                      onClick={() => {
                        setPattern('create');
                        setCardMode('point');
                        setSelectedTab('process');
                        selectCategory({
                          selectTitle: '新建',
                          selectType: 'IDC',
                          deviceType: '',
                          type: 'custom',
                        });
                        updateFormValues({
                          pointType: {
                            name: 'pointType',
                            value: POINT_TYPE_CODE_MAP.CUSTOM,
                          },
                        });
                      }}
                    >
                      新建
                    </Button>
                    <Button
                      href={`${CUSTOM_POINT_IMPORT_ROUTE_PATH}${idc ? `?idc=${idc}` : ''}`}
                      target="_blank"
                    >
                      导入
                    </Button>
                    {checkCode('element_mergerd-processed-points-export-btn') && (
                      <Button loading={exportCustomLoading} onClick={onHandleExportCustomPoint}>
                        导出
                      </Button>
                    )}
                  </Space>
                ) : treeMode === 'device' &&
                  checkCode('element_mergerd-processed-points-export-btn') ? (
                  <Space>
                    <Button
                      type="primary"
                      loading={exportOriLoading}
                      onClick={() =>
                        onHandleExportPoint({
                          pointTypeList: ['ORI'],
                          exportFileName: '原始测点',
                          loading: 'ori',
                        })
                      }
                    >
                      导出原始测点
                    </Button>
                    <Button
                      loading={exportDeviceCalAggLoading}
                      onClick={() =>
                        onHandleExportPoint({
                          pointTypeList: ['CAL_DEVICE', 'AGG_DEVICE'],
                          exportFileName: '加工聚合测点',
                          loading: 'deviceCalAgg',
                        })
                      }
                    >
                      导出加工聚合测点
                    </Button>
                  </Space>
                ) : treeMode === 'space' &&
                  checkCode('element_mergerd-processed-points-export-btn') ? (
                  <Button
                    type="primary"
                    loading={exportSpaceCalAggLoading}
                    onClick={() =>
                      onHandleExportPoint({
                        pointTypeList: ['CAL_SPACE', 'AGG_SPACE'],
                        exportFileName: '加工聚合测点',
                        loading: 'spaceCalAgg',
                      })
                    }
                  >
                    导出加工聚合测点
                  </Button>
                ) : (
                  ''
                )}
              </GutterWrapper>
            </TinyCard>
          </GutterWrapper>
        )}
      </GutterWrapper>
    </DragDropContext>
  );
}

const mapStateToProps = ({
  doPointManage: { selectCategoryPoint },
  common: { deviceCategory },
}) => {
  let pointData = null;
  if (selectCategoryPoint && selectCategoryPoint.selectType) {
    pointData = true;
  }
  return {
    pointData,
    deviceCategory,
  };
};
const mapDispatchToProps = {
  changeExpressInfos,
  exprInsertIntoRoot: mergedProcessesdPointActions.exprInsertIntoRoot,
  exprInsertIntoNested: mergedProcessesdPointActions.exprInsertIntoNested,
  exprSortInRoot: mergedProcessesdPointActions.exprSortInRoot,
  exprSortInNest: mergedProcessesdPointActions.exprSortInNest,
  exprInsertPointIntoRoot: exprInsertPointIntoRootActionCreator,
  exprInsertPointIntoNested: exprInsertPointIntoNestedActionCreator,
  selectCategory: doPointActions.selectCategoryPoint,
  updateFormValues: mergedProcessesdPointActions.updateProcessedPointFormValues,
};

export default connect(mapStateToProps, mapDispatchToProps)(MergedProcessPointDetail);
