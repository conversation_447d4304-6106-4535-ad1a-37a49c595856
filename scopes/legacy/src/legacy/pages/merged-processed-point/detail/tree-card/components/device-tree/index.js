import React, { Component } from 'react';
import { connect } from 'react-redux';
import styled from 'styled-components';

import { message } from '@manyun/base-ui.ui.message';

import { ApiTree } from '@manyun/dc-brain.legacy.components';
import { DEVICE_SPACE_TYPE_KEY_MAP } from '@manyun/dc-brain.legacy.constants/enums/deviceType';
import {
  commonActions,
  syncCommonDataActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  MetaCategoryNumAction,
  doPointActions,
  fetchPointDetail,
} from '@manyun/dc-brain.legacy.redux/actions/doPointActions';
import { deviceTypeService, treeDataService } from '@manyun/dc-brain.legacy.services';

import AddTypeModal from './add-type-modal';
import TreeTitle from './title-leaf';

function generateNodeKey(metaCode, metaType) {
  return `${metaCode}-${metaType}`;
}

export const StyledApiTree = styled(ApiTree)`
  .manyun-tree-node-content-wrapper {
    width: 94%;
    padding: 0;
  }
  .manyun-tree-title {
    display: inline-block;
    width: 98%;
    cursor: text;
  }
  .manyun-tree-treenode-switcher-open {
    height: 100%;
  }
`;

class DoPointList extends Component {
  state = {
    expandedKeys: [],
    selectKey: [],
    searchValue: '',
    autoExpandParent: true,
    addTreeNode: null,
    treeData: null,
    createVisible: false,
    info: {},
    okButtonLoading: false,
    increasedNodeKey: null,
  };

  wrapperRef = React.createRef();

  componentDidMount() {
    if (this.props.expandedKey.length) {
      this.setState({
        expandedKeys: this.props.expandedKey,
        selectKey: [this.props.deviceType],
      });
    }
    this.getDeviceCategory();
  }

  componentDidUpdate(prevProps) {
    if (!prevProps.expandedKey.length && this.props.expandedKey.length) {
      this.setState({
        expandedKeys: this.props.expandedKey,
        selectKey: [this.props.deviceType],
      });
    }
    if (prevProps.deviceReRequest !== this.props.deviceReRequest) {
      this.getDeviceCategory();
      this.setState({ addTreeNode: null });
    }
    const { treeData, increasedNodeKey } = this.state;
    const increasedNodeKeyInTree = getIncreasedNodeKeyInTree(increasedNodeKey, treeData);
    if (increasedNodeKeyInTree && this.wrapperRef.current) {
      // eslint-disable-next-line no-unused-expressions
      this.wrapperRef.current
        .querySelector('.manyun-tree-treenode-selected')
        ?.scrollIntoView({ behavior: 'auto' });
    }
    if (prevProps.treeMode !== this.props.treeMode && this.state.selectKey.length) {
      this.setState({
        expandedKeys: [],
        selectKey: [],
      });
    }
  }

  getDeviceCategory = async () => {
    const data = await treeDataService.fetchDeviceCategory({ numbered: null }); // numbered = true,请求有编号的设备类型，numbered = false,请求无编号的设备类型，numbered = null请求所有的设备类型，不传默认为true
    if (data) {
      const deviceTypes = data.treeList.filter(
        ({ metaStyle }) => metaStyle !== DEVICE_SPACE_TYPE_KEY_MAP.SPACE
      ); // 过滤空间类型
      this.setState(
        {
          treeData: {
            ...data,
            treeList: getTreeData(deviceTypes),
          },
        },
        () => {
          this.props.refreshDeviceTreeData();
        }
      );
    }
  };

  selectTreeNode = selectedKeys => {
    const { treeData } = this.state;
    const selected = selectedKeys.split('-');
    const code = selected[0];
    const type = selected[1];
    const dataTitle =
      treeData.normalizedList && treeData.normalizedList[code]
        ? treeData.normalizedList[code].metaName
        : code;
    const numbered =
      treeData.normalizedList && treeData.normalizedList[code]
        ? treeData.normalizedList[code].numbered
        : null;
    this.props.saveType({ code });
    // 保存选择得测点信息
    this.props.selectCategory({
      selectTitle: dataTitle,
      selectType: type,
      deviceType: code,
      type: 'device',
      numbered,
    });
    this.setState({
      selectKey: [selectedKeys],
      parallelList: {},
      increasedNodeKey: null,
    });
    // 当点击的设备类型不是第三级时，cardMode 切换至basic
    if (type !== 'C2' || !numbered) {
      this.props.setCardMode('basic');
    }
  };

  addDevice = async params => {
    this.setState({ okButtonLoading: true });
    const { response, error } = await deviceTypeService.addDeviceType(params);
    if (error) {
      message.error(error);
      this.setState({ okButtonLoading: false });
    }
    if (response) {
      this.setState({
        createVisible: false,
        addTreeNode: null,
        okButtonLoading: false,
        selectKey: [`${response.code}-${response.level}`],
        increasedNodeKey: response.code,
      });
      // 新建设备类型成功后，重新请求设备类型数据
      this.getDeviceCategory();
      this.props.syncCommonData({ strategy: { deviceCategory: 'FORCED' } });
      this.props.selectCategory({
        selectTitle: response.name,
        selectType: response.level,
        deviceType: response.code,
        type: 'device',
        numbered: response.numbered,
      });
    }
  };

  cancleAddDevice = (type, code) => {
    this.setState(
      ({ treeData }) => {
        const newTreeList = deleteDeviceByCode(treeData.treeList, type, code);
        return {
          treeData: {
            ...treeData,
            treeList: newTreeList,
            parallelList: treeData.parallelList,
          },
          addTreeNode: null,
          okButtonLoading: false,
        };
      },
      () => {
        this.props.refreshDeviceTreeData();
      }
    );
  };

  deleteDevice = async ({ params, successCb, errorCb }) => {
    const { response, error } = await deviceTypeService.deleteDeviceType(params);
    if (error) {
      errorCb();
      message.error(error);
    }
    if (response) {
      successCb();
      this.props.setDeviceCategory(null);
      this.getDeviceCategory();
      this.props.syncCommonData({ strategy: { deviceCategory: 'FORCED' } });
    }
  };

  render() {
    const {
      autoExpandParent,
      expandedKeys,
      selectKey,
      treeData,
      createVisible,
      addTreeNode,
      info,
      okButtonLoading,
    } = this.state;
    const { cardMode } = this.props;
    return (
      <>
        <StyledApiTree
          wrapperRef={this.wrapperRef}
          ref={this.props.deviceApiTreeRef}
          isMatchSearch={this.props.isMatchSearch}
          showSearch
          autoExpandParent={autoExpandParent}
          placeholder="请输入"
          expandedKeys={expandedKeys}
          selectedKeys={selectKey}
          treeNodeFilterProp="metaName"
          onExpandedKeysUpdate={expandedKeys => this.setState({ expandedKeys })}
          onExpand={expandedKeys => this.setState({ expandedKeys })}
          fieldNames={{
            key: ({ metaCode, metaType }) => generateNodeKey(metaCode, metaType),
            title: (
              { metaType, metaName, metaCode, children, parentCode },
              { highlightedText }
            ) => {
              return (
                <TreeTitle
                  title={metaName}
                  highlightedText={highlightedText}
                  addTreeNode={addTreeNode}
                  treeNodeKey={generateNodeKey(metaCode, metaType)}
                  metaName={metaName}
                  metaType={metaType}
                  metaCode={metaCode}
                  children={children}
                  parentCode={parentCode}
                  setPattern={this.props.setPattern}
                  setAddTreeNode={nodes => {
                    this.setState({
                      addTreeNode: generateNodeKey(nodes.idx, metaType === 'C0' ? 'C1' : 'C2'),
                      expandedKeys: [...expandedKeys, metaCode + '-' + metaType],
                    });
                    if (nodes.level === 'same') {
                      this.setState({
                        createVisible: true,
                      });
                    } else {
                      const newTreeList = getNewCategory(
                        treeData.treeList,
                        { metaType, metaCode },
                        nodes.idx
                      );
                      const newParallelList = getNewParallelList(
                        treeData.parallelList,
                        { metaType, metaCode },
                        nodes.idx
                      );
                      this.setState(
                        {
                          treeData: {
                            ...treeData,
                            treeList: newTreeList,
                            parallelList: newParallelList,
                          },
                        },
                        () => {
                          this.props.refreshDeviceTreeData();
                        }
                      );
                    }
                  }}
                  addDevice={this.addDevice}
                  cancleAddDevice={this.cancleAddDevice}
                  deleteDevice={this.deleteDevice}
                  cardMode={cardMode}
                  onSelect={this.selectTreeNode}
                  setSelectedTab={this.props.setSelectedTab}
                  showAddSameLevelButton={treeData && treeData.treeList.length < 8}
                  okButtonLoading={okButtonLoading}
                  childrenLength={children ? children.length : 0}
                />
              );
            },
            parentKey: ({ metaCode, metaType }) => generateNodeKey(metaCode, metaType),
          }}
          dataService={async () => {
            if (treeData) {
              return Promise.resolve(treeData);
            }
            return Promise.resolve({
              treeList: [],
            });
          }}
        />
        <AddTypeModal
          visible={createVisible}
          updateVisible={value => this.setState({ createVisible: value })}
          initiateAddDeviceService={this.initiateAddDeviceService}
          addTreeNode={addTreeNode}
          addDevice={this.addDevice}
          info={info}
          okButtonLoading={okButtonLoading}
          setAddTreeNode={value =>
            this.setState({
              addTreeNode: value,
            })
          }
        />
      </>
    );
  }
}

const mapStateToProps = ({ doPointManage: { expandedKey, deviceType } }) => {
  return {
    expandedKey,
    deviceType,
  };
};

const mapDispatchToProps = {
  MetaCategoryNumAction,
  fetchPointDetail,
  saveType: doPointActions.saveType,
  selectCategory: doPointActions.selectCategoryPoint,
  setMetaCategory: doPointActions.fetcgMetaCategory,
  syncCommonData: syncCommonDataActionCreator,
  setDeviceCategory: commonActions.setDeviceCategory,
};

export default connect(mapStateToProps, mapDispatchToProps)(DoPointList);

function getNewCategory(data, each, code) {
  return data.map(item => {
    if (item.metaCode === each.metaCode) {
      return {
        ...item,
        isLeaf: false,
        children: item.children
          ? [
              {
                metaCode: code,
                metaName: '',
                metaType: item.metaType === 'C0' ? 'C1' : 'C2',
                parentCode: item.metaType + item.metaCode,
                isLeaf: true,
              },
              ...item.children,
            ]
          : [
              {
                metaCode: code,
                metaName: '',
                metaType: each.metaType === 'C0' ? 'C1' : 'C2',
                parentCode: item.metaType + item.metaCode,
                isLeaf: true,
              },
            ],
      };
    } else if (!item.children || !item.children.length) {
      return item;
    } else {
      return {
        ...item,
        children: getNewCategory(item.children, each, code),
      };
    }
  });
}

function getNewParallelList(data, each, code) {
  return [
    {
      metaCode: code,
      metaName: '',
      metaType: each.metaType === 'C0' ? 'C1' : 'C2',
      parentCode: each.metaType + each.metaCode,
    },
    ...data,
  ];
}

function getTreeData(data) {
  return data.map(item => {
    if (item.metaType === 'C2') {
      return {
        ...item,
        isLeaf: true,
      };
    } else if (!item.children || !item.children.length) {
      return {
        ...item,
        isLeaf: true,
      };
    } else {
      return {
        ...item,
        children: getTreeData(item.children),
      };
    }
  });
}

function deleteDeviceByCode(treeList, type, code) {
  if (!treeList) {
    return [];
  }
  return treeList
    .map(item => {
      if (item.metaType === type && item.metaCode === code) {
        return null;
      }
      if (item.children && item.children.length) {
        const data = deleteDeviceByCode(item.children, type, code);
        return {
          ...item,
          children: data,
          isLeaf: !data.length,
        };
      }
      return {
        ...item,
        children: null,
        isLeaf: true,
      };
    })
    .filter(Boolean);
}

function getIncreasedNodeKeyInTree(increasedNodeKey, treeData) {
  if (!increasedNodeKey || !treeData) {
    return;
  }
  if (treeData.normalizedList[increasedNodeKey]) {
    return true;
  }
  return false;
}
