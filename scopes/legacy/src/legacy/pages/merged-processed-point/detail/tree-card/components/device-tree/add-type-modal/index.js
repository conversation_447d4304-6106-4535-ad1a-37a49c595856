import React from 'react';

import Form from '@ant-design/compatible/es/form';

import { Input } from '@manyun/base-ui.ui.input';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Radio } from '@manyun/base-ui.ui.radio';

import { TypeAttributionRadio } from '@manyun/resource-hub.ui.type-attribution';

function AddTypeModal({
  visible,
  form: { getFieldDecorator, validateFields },
  updateVisible,
  addTreeNode,
  addDevice,
  info,
  okButtonLoading,
  setAddTreeNode,
}) {
  const handleOk = () => {
    validateFields((err, values) => {
      if (err) {
        return;
      }
      if (addTreeNode) {
        const params = {
          ...values,
          level: 'C0',
          parentCode: 0,
        };
        addDevice(params);
      }
    });
  };
  return (
    <Modal
      title="创建一级分类"
      visible={visible}
      okButtonProps={{
        loading: okButtonLoading,
      }}
      onOk={handleOk}
      onCancel={() => {
        setAddTreeNode(null);
        updateVisible(false);
      }}
    >
      <Form colon={false} labelCol={{ span: 5 }} wrapperCol={{ span: 19 }}>
        <Form.Item label="类型名称">
          {getFieldDecorator('name', {
            rules: [
              { required: true, whitespace: true, message: '类型名称必填' },
              {
                max: 16,
                message: '最多输入 16 个字符！',
              },
            ],
            initialValue: info.name,
          })(<Input style={{ width: 200 }} />)}
        </Form.Item>
        <Form.Item label="类型归属">
          {getFieldDecorator('type', {
            rules: [{ required: true, message: '类型归属必选！' }],
          })(<TypeAttributionRadio />)}
        </Form.Item>
        <Form.Item label="是否有编号">
          {getFieldDecorator('numbered', {
            rules: [{ required: true, message: '是否有编号必选！' }],
            initialValue: info.numbered,
          })(
            <Radio.Group>
              <Radio value>是</Radio>
              <Radio value={false}>否</Radio>
            </Radio.Group>
          )}
        </Form.Item>
      </Form>
    </Modal>
  );
}

export default Form.create()(AddTypeModal);
