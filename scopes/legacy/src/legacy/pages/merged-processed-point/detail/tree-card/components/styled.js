import styled from 'styled-components';

import { Button } from '@manyun/base-ui.ui.button';

export const StyledButton = styled(Button)`
  padding: 0 0 0 2px;
  height: auto;
`;

export const StyledTitleWapper = styled.span`
  display: inline-block;
  position: relative;
  width: 100%;
`;

export const StyledTitleDevice = styled.span`
  float: left;
  cursor: pointer;
  max-width: ${({ maxWidth }) => maxWidth};
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

export const StyledTitleOpertion = styled.span`
  float: right;
  padding-right: 2px;
`;
