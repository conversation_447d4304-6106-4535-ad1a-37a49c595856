import React, { useEffect, useState } from 'react';

import CheckOutlined from '@ant-design/icons/es/icons/CheckOutlined';
import CloseOutlined from '@ant-design/icons/es/icons/CloseOutlined';
import DeleteOutlined from '@ant-design/icons/es/icons/DeleteOutlined';
import EditOutlined from '@ant-design/icons/es/icons/EditOutlined';
import PlusSquareOutlined from '@ant-design/icons/es/icons/PlusSquareOutlined';
import shortid from 'shortid';

import { Button } from '@manyun/base-ui.ui.button';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';

import { DEVICE_TYPE_META_TYPE_KEY_MAP } from '@manyun/dc-brain.legacy.constants/enums/deviceType';

import { StyledTitleDevice, StyledTitleOpertion, StyledTitleWapper } from '../../styled';

export default function TreeTitle({
  title,
  metaName,
  metaCode,
  treeNodeKey,
  setAddTreeNode,
  highlightedText,
  metaType,
  addDevice,
  deleteDevice,
  cancleAddDevice,
  addTreeNode, // 当前新增的node
  children,
  parentCode,
  cardMode,
  setPattern,
  onSelect,
  showAddSameLevelButton,
  okButtonLoading,
  childrenLength,
}) {
  const [monsetOver, setMonsetOver] = useState(false);
  const [inputValue, setInputValue] = useState('');

  useEffect(() => {
    if (addTreeNode === treeNodeKey) {
      setInputValue(metaName);
    }
  }, [metaName, addTreeNode, treeNodeKey]);

  if (addTreeNode === treeNodeKey) {
    return (
      <Input
        maxLength={16}
        value={inputValue}
        size="small"
        disabled={okButtonLoading}
        // prefix={<Icon type="plus-circle" style={{ color: 'var(--color-green-6)' }} />}
        suffix={
          <Space>
            <CheckOutlined
              onClick={() => {
                if (inputValue === '') {
                  message.error('设备类型名称不可为空！');
                  return;
                }
                const params = {
                  name: inputValue.trim(),
                  level: metaType,
                  parentCode: parentCode,
                  parentType: metaType === 'C1' ? 'C0' : 'C1',
                  numbered: false,
                };
                addDevice(params);
              }}
            />
            <CloseOutlined
              onClick={() => {
                cancleAddDevice(metaType, metaCode);
              }}
            />
          </Space>
        }
        onChange={({ target: { value } }) => setInputValue(value)}
      />
    );
  }

  const showDeleteButton = !children || !children.length;

  return (
    <>
      <StyledTitleWapper
        onMouseEnter={() => setMonsetOver(true)}
        onMouseLeave={() => setMonsetOver(false)}
      >
        <Tooltip title={title} mouseEnterDelay={2}>
          <StyledTitleDevice
            maxWidth={
              metaType === DEVICE_TYPE_META_TYPE_KEY_MAP.C0
                ? '90px'
                : metaType === DEVICE_TYPE_META_TYPE_KEY_MAP.C1
                ? '140px'
                : '160px'
            }
            onClick={() => {
              setPattern('list');
              onSelect(`${metaCode}-${metaType}`);
            }}
          >
            {highlightedText ? <Typography.Text type="warning">{title}</Typography.Text> : title}
          </StyledTitleDevice>
        </Tooltip>
        <StyledTitleOpertion
          style={{
            display: monsetOver && cardMode === 'basic' ? 'block' : 'none',
          }}
        >
          <Space>
            <Button
              type="link"
              compact
              onClick={() => {
                setPattern('edit');
                onSelect(`${metaCode}-${metaType}`);
              }}
            >
              <Tooltip title="编辑">
                <EditOutlined />
              </Tooltip>
            </Button>
            {showDeleteButton && (
              <DeleteConfirm
                targetName={metaName}
                onOk={({ reason }) => {
                  const params = {
                    code: metaCode,
                    level: metaType,
                    operatorNotes: reason,
                  };
                  return new Promise(resolve => {
                    deleteDevice({
                      params,
                      successCb: () => {
                        resolve(true);
                      },
                      errorCb: () => {
                        resolve(false);
                      },
                    });
                  });
                }}
              >
                <Button type="link" compact>
                  <Tooltip title="删除">
                    <DeleteOutlined />
                  </Tooltip>
                </Button>
              </DeleteConfirm>
            )}
            {metaType === DEVICE_TYPE_META_TYPE_KEY_MAP.C0 &&
              !addTreeNode &&
              showAddSameLevelButton && (
                <Button
                  compact
                  type="link"
                  onClick={() => {
                    const idx = shortid();
                    setAddTreeNode({ metaType, idx, level: 'same' });
                  }}
                >
                  <Tooltip title="新增同级">
                    <PlusSquareOutlined />
                  </Tooltip>
                </Button>
              )}
            {metaType !== DEVICE_TYPE_META_TYPE_KEY_MAP.C2 &&
              !addTreeNode &&
              childrenLength <
                (metaType === DEVICE_TYPE_META_TYPE_KEY_MAP.C0 ? 99 : Number.POSITIVE_INFINITY) && (
                <Button
                  compact
                  type="link"
                  onClick={() => {
                    const idx = shortid();
                    setAddTreeNode({ metaType, idx, level: 'next' });
                  }}
                >
                  <Tooltip title="新增子级">
                    <PlusSquareOutlined />
                  </Tooltip>
                </Button>
              )}
          </Space>
        </StyledTitleOpertion>
      </StyledTitleWapper>
    </>
  );
}
