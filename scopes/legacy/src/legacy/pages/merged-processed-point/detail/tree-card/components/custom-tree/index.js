import uniq from 'lodash.uniq';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import { Empty } from '@manyun/base-ui.ui.empty';
import { generateTreeData } from '@manyun/base-ui.util.generate-tree-data';

import { ApiTree } from '@manyun/dc-brain.legacy.components';
import {
  DEVICE_SPACE_TYPE_KEY_MAP,
  DEVICE_TYPE_META_TYPE_KEY_MAP,
} from '@manyun/dc-brain.legacy.constants/enums/deviceType';
import {
  MetaCategoryNumAction,
  doPointActions,
} from '@manyun/dc-brain.legacy.redux/actions/doPointActions';
import { mergedProcessesdPointActions } from '@manyun/dc-brain.legacy.redux/actions/mergedProcessesdPointActions';
import { treeDataService } from '@manyun/dc-brain.legacy.services';

class CustomPointTree extends Component {
  state = {
    disabledTreeNodeKeys: [],
    // treeData: null,
    isEmpty: false,
  };

  interval = null;

  componentDidMount() {
    // this.getData();
  }

  componentDidUpdate(prevProps) {
    if (this.props.idc !== prevProps.idc && prevProps.idc) {
      this.props.customApiTreeRef.current.refreshData();
    }
  }

  componentWillUnmount() {
    if (this.interval) {
      clearTimeout(this.interval);
    }
  }

  onSelectTree = (selectedKeys, { node }) => {
    if (
      node.dataRef.metaType === DEVICE_TYPE_META_TYPE_KEY_MAP.C0 ||
      node.dataRef.metaType === DEVICE_TYPE_META_TYPE_KEY_MAP.C1
    ) {
      return;
    }
    this.props.selectCategory({
      selectTitle: node.dataRef.metaName,
      selectType: node.dataRef.metaType,
      metaType: node.dataRef.metaType,
      // @FIXME @cw 这里把 spaceGuid 当成 deviceType 了
      deviceType: node.dataRef.metaCode,
      parentCode: node.dataRef.parentCode,
      nodeKey: selectedKeys,
      type: 'custom',
      // numbered,
    });
    this.props.setPattern('list');
  };

  getData = async () => {
    const { expandedKeys, selectedKeys, idc, isOnlyShowCustom } = this.props;
    if (!idc && isOnlyShowCustom) {
      return {
        treeList: [],
        parallelList: [],
      };
    }
    const data = await treeDataService.fetchCustomSpacePoint(idc);
    const keysWithCount = [];
    if (data) {
      const spaceData = {
        treeList: generateTreeData(data.treeList, {
          key: 'metaCode',
          typeKey: 'metaType',
          parentKey: 'parentCode',
          nodeTypes: ['IDC', 'BLOCK', 'ROOM', 'DEVICE'],
          getNode(node, children) {
            if (node.count === 0) {
              keysWithCount.push(node.metaCode);
            }
            return {
              ...node,
              children,
              isLeaf: !(Array.isArray(children) && children.length),
            };
          },
        }),
        parallelList: data.parallelList,
      };
      if (Array.isArray(spaceData.treeList) && spaceData.treeList.length) {
        this.props.setCustomerTreeData(spaceData.treeList);
      }
      this.setState({
        disabledTreeNodeKeys: keysWithCount,
      });
      const selectedExpandedKyes = [];
      if (selectedKeys) {
        const keys = selectedKeys.split('.');
        if (keys[0]) {
          selectedExpandedKyes.push(keys[0]);
        }
        if (keys[1]) {
          selectedExpandedKyes.push(`${keys[0]}.${keys[1]}`);
        }
        if (keys[2]) {
          selectedExpandedKyes.push(`${keys[0]}.${keys[1]}.${keys[2]}`);
        }
      }
      /**fix expandedKeys 不生效*/
      this.interval = setTimeout(() => {
        this.props.updatexpandedKeys(
          uniq([...selectedExpandedKyes, ...expandedKeys, ...keysWithCount])
        );
      }, 300);
      if (!spaceData.treeList?.length) {
        this.setState({ isEmpty: true });
      } else {
        this.setState({ isEmpty: false });
      }
      return spaceData;
    }
    this.setState({ isEmpty: true });
    return {
      treeList: [],
      parallelList: [],
    };
  };

  render() {
    const { expandedKeys, selectedKeys, customApiTreeRef } = this.props;
    const { disabledTreeNodeKeys, isEmpty } = this.state;

    return (
      <>
        {/*customApiTreeRef会丢失*/}
        <div style={{ display: isEmpty ? 'none' : 'block' }}>
          <ApiTree
            ref={customApiTreeRef}
            showSearch
            placeholder="请输入"
            selectedKeys={[selectedKeys]}
            fieldNames={{
              key: 'metaCode',
              title: 'metaName',
              parentKey: 'parentCode',
            }}
            treeNodeFilterProp="metaName"
            dataService={async () => {
              const treeData = this.getData();
              return Promise.resolve(treeData ?? { treeList: [], parallelList: [] });
            }}
            disabledTreeNodeKeys={disabledTreeNodeKeys}
            // defaultExpandedKeys={expandedKeys}
            expandedKeys={expandedKeys}
            onExpand={keys => {
              this.props.updatexpandedKeys(keys);
            }}
            onSelect={this.onSelectTree}
          />
        </div>
        {isEmpty && <Empty description="暂无数据" />}
      </>
    );
  }
}

const mapStateToProps = ({
  common: { deviceCategory },
  mergedProcessesdPoint: { expandedKeys },
  doPointManage: { categoryNum, selectCategoryPoint },
}) => {
  let baseTreeData;
  if (deviceCategory && deviceCategory.treeList) {
    baseTreeData = {
      treeList: deviceCategory.treeList.filter(
        item => item.metaStyle === DEVICE_SPACE_TYPE_KEY_MAP.SPACE
      ),
      parallelList: deviceCategory.parallelList,
    };
  }
  return {
    baseTreeData,
    expandedKeys,
    selectedKeys: selectCategoryPoint.deviceType,
    categoryNum,
    deviceCategory,
  };
};

const mapDispatchToProps = {
  savePointInfoMap: mergedProcessesdPointActions.savePointInfoMap,
  selectCategory: doPointActions.selectCategoryPoint,
  getMetaCategoryNum: MetaCategoryNumAction,
  updatexpandedKeys: mergedProcessesdPointActions.updatexpandedKeys,
};

export default connect(mapStateToProps, mapDispatchToProps)(CustomPointTree);
