import React from 'react';

import { StyledTitleDevice } from '../../styled';

export class TreeTitle extends React.Component {
  render() {
    const { highlightedText, title, num } = this.props;
    if (num) {
      return (
        <StyledTitleDevice highlightedText={highlightedText}>
          {title}&nbsp;&nbsp;&nbsp;&nbsp;{num}
        </StyledTitleDevice>
      );
    }
    return <StyledTitleDevice highlightedText={highlightedText}>{title}</StyledTitleDevice>;
  }
}

export default TreeTitle;
