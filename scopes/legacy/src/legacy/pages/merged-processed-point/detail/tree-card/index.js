import React, { Component } from 'react';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';

import { Tabs } from '@manyun/base-ui.ui.tabs';
import { generateCustomPointRoutePath } from '@manyun/monitoring.route.monitoring-routes';
import { IdcSelect } from '@manyun/resource-hub.ui.idc-select';

import { TinyCard } from '@manyun/dc-brain.legacy.components';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import { doPointActions } from '@manyun/dc-brain.legacy.redux/actions/doPointActions';
import { mergedProcessesdPointActions } from '@manyun/dc-brain.legacy.redux/actions/mergedProcessesdPointActions';

import CustomTree from '../tree-card/components/custom-tree';
import SpaceTree from '../tree-card/components/space-tree';
import DeviceTree from './components/device-tree';

class DetailTreeCard extends Component {
  constructor(props) {
    super(props);
    this.state = {
      idc: props.idc,
    };
  }

  componentDidMount() {
    this.props.syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });
  }

  render() {
    const {
      treeMode,
      cardMode,
      setPattern,
      deviceApiTreeRef,
      customApiTreeRef,
      deviceReRequest,
      setSelectedTab,
      selectCategory,
      updatexpandedKeys,
      refresCustomDeviceTreeData,
      isOnlyShowCustom,
    } = this.props;
    const { idc } = this.state;

    return (
      <>
        {isOnlyShowCustom ? (
          <TinyCard
            title={
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                自定义测点列表
                <IdcSelect
                  style={{ width: 86 }}
                  value={idc}
                  onSelect={value => {
                    this.setState({ idc: value });
                    updatexpandedKeys([]);
                    setPattern('list');
                    selectCategory({
                      selectTitle: '',
                      selectType: '',
                      deviceType: '',
                    });
                    this.props.history.push(generateCustomPointRoutePath({ idc: value }));
                  }}
                />
              </div>
            }
            bodyStyle={{
              height: this.props.height,
              overflow: 'auto',
              paddingBottom: '20px',
            }}
          >
            <CustomTree
              setPattern={setPattern}
              customApiTreeRef={customApiTreeRef}
              refresCustomDeviceTreeData={refresCustomDeviceTreeData}
              setCustomerTreeData={this.props.setCustomerTreeData}
              isOnlyShowCustom={isOnlyShowCustom}
              idc={idc}
            />
          </TinyCard>
        ) : (
          <TinyCard
            title="类型列表"
            bodyStyle={{
              height: this.props.height,
              overflow: 'auto',
              paddingBottom: '20px',
            }}
          >
            <Tabs
              type="card"
              activeKey={treeMode}
              onChange={value => {
                this.props.setTreeMode(value);
                this.props.selectCategory();
                if (value === 'space' || value === 'custom') {
                  this.props.setCardMode('point');
                  setPattern('list');
                } else {
                  this.props.setCardMode('basic');
                }
              }}
            >
              <Tabs.TabPane key="device" tab="资产类型">
                <DeviceTree
                  setPattern={setPattern}
                  cardMode={cardMode}
                  setCardMode={this.props.setCardMode}
                  deviceApiTreeRef={deviceApiTreeRef}
                  refreshDeviceTreeData={this.props.refreshDeviceTreeData}
                  setSelectedTab={setSelectedTab}
                  deviceReRequest={deviceReRequest}
                  treeMode={treeMode}
                  isMatchSearch
                />
              </Tabs.TabPane>
              <Tabs.TabPane key="space" tab="空间类型">
                <SpaceTree setPattern={setPattern} />
              </Tabs.TabPane>
              <Tabs.TabPane key="custom" tab="自定义">
                <CustomTree
                  setPattern={setPattern}
                  customApiTreeRef={customApiTreeRef}
                  refresCustomDeviceTreeData={refresCustomDeviceTreeData}
                  setCustomerTreeData={this.props.setCustomerTreeData}
                />
              </Tabs.TabPane>
            </Tabs>
          </TinyCard>
        )}
      </>
    );
  }
}

const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
  selectCategory: doPointActions.selectCategoryPoint,
  updatexpandedKeys: mergedProcessesdPointActions.updatexpandedKeys,
};
export default connect(null, mapDispatchToProps)(withRouter(DetailTreeCard));
