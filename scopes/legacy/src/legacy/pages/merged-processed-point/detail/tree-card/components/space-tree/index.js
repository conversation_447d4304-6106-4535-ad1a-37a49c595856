import React, { Component } from 'react';
import { connect } from 'react-redux';

import { ApiTree } from '@manyun/dc-brain.legacy.components';
import {
  DEVICE_SPACE_TYPE_KEY_MAP,
  DEVICE_TYPE_META_TYPE_KEY_MAP,
} from '@manyun/dc-brain.legacy.constants/enums/deviceType';
import { generateNodeKey } from '@manyun/dc-brain.legacy.pages/merged-processed-point/utils';
import {
  MetaCategoryNumAction,
  doPointActions,
} from '@manyun/dc-brain.legacy.redux/actions/doPointActions';
import { mergedProcessesdPointActions } from '@manyun/dc-brain.legacy.redux/actions/mergedProcessesdPointActions';
import { treeDataService } from '@manyun/dc-brain.legacy.services';

import TreeTitle from './coponents/tree-title';

class MergedPointTree extends Component {
  componentDidMount() {
    this.props.getMetaCategoryNum();
  }

  onSelectTree = (selectedKeys, { node }) => {
    if (
      node.dataRef.metaType === DEVICE_TYPE_META_TYPE_KEY_MAP.C0 ||
      node.dataRef.metaType === DEVICE_TYPE_META_TYPE_KEY_MAP.C1
    ) {
      return;
    }
    const { deviceCategory } = this.props;
    const numbered =
      deviceCategory &&
      deviceCategory.normalizedList &&
      deviceCategory.normalizedList[node.dataRef.metaCode]
        ? deviceCategory.normalizedList[node.dataRef.metaCode].numbered
        : null;
    this.props.selectCategory({
      selectTitle: node.dataRef.metaName,
      selectType: node.dataRef.metaType,
      metaType: node.dataRef.metaType,
      deviceType: node.dataRef.metaCode,
      nodeKey: selectedKeys,
      type: 'space',
      numbered,
    });
    this.props.setPattern('list');
  };

  render() {
    const {
      baseTreeData,
      expandedKeys,
      selectedKeys,
      defaultExpandedKeys,
      apiTreeRef,
      categoryNum,
    } = this.props;
    const expKeys = expandedKeys.length ? expandedKeys : defaultExpandedKeys;
    return (
      <ApiTree
        showSearch
        placeholder="请输入"
        ref={apiTreeRef}
        defaultExpandedKeys={expKeys}
        selectedKeys={selectedKeys}
        fieldNames={{
          key: ({ metaCode, metaType }) => generateNodeKey({ metaCode, metaType }),
          title: ({ metaType, metaName, metaCode }, { highlightedText }) => {
            if (metaType === 'C2') {
              return (
                <TreeTitle
                  title={metaName}
                  highlightedText={highlightedText}
                  num={categoryNum[metaCode]}
                />
              );
            }
            return highlightedText || metaName;
          },
          parentKey: ({ metaCode, metaType, nodeType }) =>
            generateNodeKey({ metaCode, metaType, nodeType }),
        }}
        treeNodeFilterProp="metaName"
        dataService={async () => {
          if (baseTreeData) {
            return Promise.resolve({
              treeList: getSpaceTree(baseTreeData.treeList),
              parallelList: baseTreeData.parallelList,
            });
          }
          const data = await treeDataService.fetchDeviceCategory();
          if (data) {
            const newData = data.treeList.filter(item => item.metaStyle === 'SPACE');
            const spaceData = {
              treeList: getSpaceTree(newData),
              parallelList: data.parallelList,
            };
            return Promise.resolve(spaceData);
          }
          return { treeList: [], parallelList: {} };
        }}
        onSelect={this.onSelectTree}
      />
    );
  }
}

const mapStateToProps = ({
  common: { deviceCategory },
  mergedProcessesdPoint: { expandedKeys },
  doPointManage: { categoryNum, selectCategoryPoint },
}) => {
  let baseTreeData;
  if (deviceCategory && deviceCategory.treeList) {
    baseTreeData = {
      treeList: deviceCategory.treeList.filter(
        item => item.metaStyle === DEVICE_SPACE_TYPE_KEY_MAP.SPACE
      ),
      parallelList: deviceCategory.parallelList,
    };
  }
  return {
    baseTreeData,
    expandedKeys,
    selectedKeys: selectCategoryPoint.nodeKey,
    categoryNum,
    deviceCategory,
  };
};

const mapDispatchToProps = {
  savePointInfoMap: mergedProcessesdPointActions.savePointInfoMap,
  selectCategory: doPointActions.selectCategoryPoint,
  getMetaCategoryNum: MetaCategoryNumAction,
};

export default connect(mapStateToProps, mapDispatchToProps)(MergedPointTree);

function getSpaceTree(data) {
  return data.map(item => {
    if (!item.children || !item.children.length) {
      return {
        ...item,
        children: null,
        isLeaf: true,
      };
    }
    return {
      ...item,
      children: getSpaceTree(item.children),
    };
  });
}
