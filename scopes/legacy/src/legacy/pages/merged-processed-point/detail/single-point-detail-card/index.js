import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import { But<PERSON> } from '@manyun/base-ui.ui.button';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';

import { <PERSON>er<PERSON><PERSON><PERSON><PERSON>, GutterWrapper, TinyCard } from '@manyun/dc-brain.legacy.components';
import {
  POINT_DATA_TYPE_CODE_MAP,
  POINT_TYPES,
  POINT_TYPE_CODE_MAP,
} from '@manyun/dc-brain.legacy.constants/point';
import { deletePointAction } from '@manyun/dc-brain.legacy.redux/actions/doPointActions';
import { mergedProcessesdPointActions } from '@manyun/dc-brain.legacy.redux/actions/mergedProcessesdPointActions';
import { generateEditMergedPreesedPointConfigUrl } from '@manyun/dc-brain.legacy.utils/urls';

import { diExprToTableData, validateExpr } from '../../utils';
import DITable from './di-table';

class SinglePointDetailCard extends Component {
  state = {
    formulaTxt: '',
  };

  componentDidMount() {
    this.getTxt();
  }

  componentDidUpdate(prevProps) {
    if (prevProps.pointData.formulaJson !== this.props.pointData.formulaJson) {
      this.getTxt();
    }
  }

  getPointType = () => {
    const { pointData } = this.props;
    const data = POINT_TYPES.filter(
      ({ key }) => key === pointData.pointType.code || key === pointData.pointType
    );
    if (data && data[0]) {
      return <span>{data[0].value}</span>;
    } else {
      return;
    }
  };

  getTxt = async () => {
    const { pointData, isPressed } = this.props;
    if (
      isPressed &&
      (pointData.dataType === POINT_DATA_TYPE_CODE_MAP.AI ||
        pointData.dataType.code === POINT_DATA_TYPE_CODE_MAP.AI)
    ) {
      const { exprTxt } = await validateExpr(pointData);
      this.setState({
        formulaTxt: exprTxt,
      });
    } else {
      this.setState({
        formulaTxt: '',
      });
    }
  };

  deleteConfirm = ({ reason }) => {
    const {
      pointData: { code, deviceType },
      refreshTreeData,
      deletePoint,
    } = this.props;
    const params = { code, deviceType, operatorNotes: reason };
    return new Promise(resolve => {
      deletePoint({
        params,
        successCb: () => {
          resolve(true);
          refreshTreeData();
          this.props.resetSelectedPointKey(null);
        },
        errorCb: () => {
          resolve(false);
        },
      });
    });
  };

  render() {
    const { height, pointData, tableData, isPressed } = this.props;
    const { formulaTxt } = this.state;

    return (
      <TinyCard
        bodyStyle={{
          height,
          overflowY: 'auto',
        }}
        title={'  '}
      >
        <Descriptions column={1}>
          <Descriptions.Item label="测点名称">{pointData.name}</Descriptions.Item>
          <Descriptions.Item label="测点ID">{pointData.code}</Descriptions.Item>
          <Descriptions.Item label="测点分类">{this.getPointType()}</Descriptions.Item>
          <Descriptions.Item label="测点类型">
            {pointData.dataType.name ? pointData.dataType.name : pointData.dataType}
          </Descriptions.Item>
          {(pointData.dataType === POINT_DATA_TYPE_CODE_MAP.AI ||
            pointData.dataType.code === POINT_DATA_TYPE_CODE_MAP.AI) && (
            <>
              <Descriptions.Item label="测点单位">{pointData.unit}</Descriptions.Item>
              <Descriptions.Item label="测点精度">{pointData.precision}</Descriptions.Item>
            </>
          )}
          {isPressed &&
            (pointData.dataType === POINT_DATA_TYPE_CODE_MAP.AI ||
              pointData.dataType.code === POINT_DATA_TYPE_CODE_MAP.AI) && (
              <Descriptions.Item label="表达式">{formulaTxt}</Descriptions.Item>
            )}
        </Descriptions>

        {isPressed &&
          (pointData.dataType === POINT_DATA_TYPE_CODE_MAP.DI ||
            pointData.dataType.code === POINT_DATA_TYPE_CODE_MAP.DI) && (
            <DITable tableData={tableData} />
          )}

        <FooterToolBar>
          <GutterWrapper>
            {/* 聚合测点不能被编辑 */}
            {!(
              pointData.pointType === POINT_TYPE_CODE_MAP.AGG_SPACE ||
              pointData.pointType === POINT_TYPE_CODE_MAP.AGG_DEVICE
            ) && (
              <Link
                to={generateEditMergedPreesedPointConfigUrl({
                  deviceType: pointData.deviceType,
                  pointCode: pointData.pointCode,
                })}
              >
                <Button type="primary">编辑</Button>
              </Link>
            )}
            <DeleteConfirm
              targetName={`${pointData.name}（${pointData.code}）`}
              onOk={this.deleteConfirm}
            >
              <Button danger>删除</Button>
            </DeleteConfirm>
          </GutterWrapper>
        </FooterToolBar>
      </TinyCard>
    );
  }
}

const mapStateToProps = ({ mergedProcessesdPoint: { selectedPointKey, pointInfoMap } }) => {
  let pointData = {};
  let tableData = [];
  if (selectedPointKey && pointInfoMap && pointInfoMap[selectedPointKey]) {
    pointData = pointInfoMap[selectedPointKey];
  }

  // DI 量，解析table数据
  if (pointData.formulaJson) {
    try {
      const formulaArray = JSON.parse(pointData.formulaJson);
      const expressionArr = formulaArray[0].children;
      tableData = diExprToTableData(expressionArr);
    } catch {}
  }

  // 判断是否是加工类型
  let isPressed = false;
  if (
    pointData.pointType &&
    (pointData.pointType === POINT_TYPE_CODE_MAP.CAL_SPACE ||
      pointData.pointType.code === POINT_TYPE_CODE_MAP.CAL_SPACE ||
      pointData.pointType === POINT_TYPE_CODE_MAP.CAL_DEVICE ||
      pointData.pointType.code === POINT_TYPE_CODE_MAP.CAL_DEVICE)
  ) {
    isPressed = true;
  }

  return {
    pointData,
    tableData,
    isPressed,
  };
};

const mapDispatchToProps = {
  resetSelectedPointKey: mergedProcessesdPointActions.resetSelectedPointKey,
  deletePoint: deletePointAction,
};

export default connect(mapStateToProps, mapDispatchToProps)(SinglePointDetailCard);
