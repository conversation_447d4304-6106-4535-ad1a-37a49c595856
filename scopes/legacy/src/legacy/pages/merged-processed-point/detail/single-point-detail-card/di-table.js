import React, { Component } from 'react';
import { connect } from 'react-redux';

import shortid from 'shortid';

import { TinyTable } from '@manyun/dc-brain.legacy.components';

class DITable extends Component {
  render() {
    const { statusList } = this.props;
    return (
      <TinyTable
        rowKey="markedID"
        size="small"
        bordered
        pagination={false}
        columns={columns(this)}
        dataSource={statusList}
      />
    );
  }
}

const mapStateToProps = (__, { tableData }) => {
  let ids = [];
  const statusData = tableData;
  let newData = [];
  if (statusData && statusData.length) {
    newData = statusData
      .map((point, index) => {
        let sameData = [];
        if (!ids.includes(point.label)) {
          statusData.forEach(item => {
            if (item.label === point.label) {
              sameData = [...sameData, item];
            }
          });
        }
        if (sameData.length > 1) {
          ids = [...ids, point.label];
          return {
            ...point,
            rowSpan: sameData.length,
            markedID: shortid.generate(),
          };
        } else {
          if (ids.includes(point.label)) {
            if (
              !statusData[index + 1] ||
              (statusData[index + 1] && point.label !== statusData[index + 1].label)
            ) {
              return {
                ...point,
                rowSpan: 0,
                isLast: true,
                markedID: shortid.generate(),
              };
            }
            return {
              ...point,
              rowSpan: 0,
              markedID: shortid.generate(),
            };
          } else {
            return {
              ...point,
              rowSpan: 1,
              isLast: true,
              markedID: shortid.generate(),
            };
          }
        }
      })
      .filter(Boolean);
  }
  return {
    statusList: newData,
  };
};

export default connect(mapStateToProps, null)(DITable);

const columns = ctx => [
  {
    title: '值',
    dataIndex: 'label',
    width: 50,
    render: (label, record) => {
      return {
        children: label,
        props: {
          rowSpan: record.rowSpan,
        },
      };
    },
  },
  {
    title: '状态含义',
    dataIndex: 'status',
    render: (label, record) => {
      return {
        children: label,
        props: {
          rowSpan: record.rowSpan,
        },
      };
    },
  },
  {
    title: '测点名称',
    dataIndex: 'name',
  },
  {
    title: '测点状态',
    dataIndex: 'dataType',
    render: (txt, { validLimits }) => {
      const tmp = validLimits.find(item => String(txt) === String(item.value));
      if (tmp) {
        return tmp.label;
      }
      return txt;
    },
  },
];
