import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import { Select } from '@galiojs/awesome-antd';

import { Input } from '@manyun/base-ui.ui.input';
import { Radio } from '@manyun/base-ui.ui.radio';

import { METADATA_TYPE } from '@manyun/dc-brain.legacy.constants';
import { VALUE_TYPE_OPTIONS } from '@manyun/dc-brain.legacy.pages/merged-processed-point/constants';
import { getMetadataByType } from '@manyun/dc-brain.legacy.redux/actions/eventCenterActions';
import { fetchSpecDetail } from '@manyun/dc-brain.legacy.redux/actions/specActions';

class AddEditSpec extends Component {
  componentWillUnmount() {
    this.props.resetPointInfo();
  }

  getSpecUnit = () => {
    if (this.props.specUnitList.length) {
      return false;
    }
    this.props.getMetadataByType(METADATA_TYPE['SPEC_UNIT']);
  };

  render() {
    const { form, info, specUnitList, pattern } = this.props;
    const { getFieldDecorator } = form;

    return (
      <Form colon={false} labelCol={{ span: 3 }} wrapperCol={{ span: 21 }}>
        <Form.Item label="规格名称">
          {getFieldDecorator('specName', {
            rules: [
              { required: true, message: '请输入规格名称！' },
              {
                max: 25,
                message: '最多输入 25 个字符！',
              },
            ],
            initialValue: info.specName,
          })(<Input style={{ width: 200 }} allowClear />)}
        </Form.Item>
        <Form.Item label="规格单位">
          {getFieldDecorator('specUnit', {
            initialValue: info.specUnit,
          })(
            <Select onFocus={this.getSpecUnit} style={{ width: 200 }} allowClear showSearch>
              {specUnitList.map(item => (
                <Select.Option key={item.metaCode}>{item.metaName}</Select.Option>
              ))}
            </Select>
          )}
        </Form.Item>
        <Form.Item label="类型">
          {getFieldDecorator('valueType', {
            rules: [{ required: true, message: '规格值类型为必选项！' }],
            initialValue: info.valueType,
          })(
            <Select allowClear style={{ width: 200 }} disabled={pattern === 'edit'}>
              {VALUE_TYPE_OPTIONS.map(item => {
                return (
                  <Select.Option key={item.value} value={item.value}>
                    {item.label}
                  </Select.Option>
                );
              })}
            </Select>
          )}
        </Form.Item>
        <Form.Item label="是否必填">
          {getFieldDecorator('required', {
            rules: [{ required: true, message: '是否必填为必选项！' }],
            initialValue: info.required,
          })(
            <Radio.Group>
              <Radio value={true}>是</Radio>
              <Radio value={false}>否</Radio>
            </Radio.Group>
          )}
        </Form.Item>
        <Form.Item label="备注">
          {getFieldDecorator('description', {
            initialValue: info.description,
            rules: [
              {
                max: 50,
                message: '最多输入 50 个字符！',
              },
            ],
          })(<Input.TextArea autoSize={{ minRows: 3 }} style={{ width: 200 }} allowClear />)}
        </Form.Item>
      </Form>
    );
  }
}

const mapStateToProps = ({
  eventCenter: { specUnitList },
  specManage: { createVisible },
  doPointManage: { deviceType },
}) => ({
  specUnitList,
  createVisible,
  deviceType,
});
const mapDispatchToProps = {
  fetchSpecDetail,
  getMetadataByType: getMetadataByType,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(Form.create({ name: 'spec_add_edit' })(AddEditSpec));
