import QuestionCircleOutlined from '@ant-design/icons/es/icons/QuestionCircleOutlined';
import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import {
  ResourceSpaceAttribute,
  ResourceSpaceAttributeEditor,
} from '@manyun/resource-hub.page.basic-resource-configuration';
import { CoursewareRelation } from '@manyun/resource-hub.page.courseware-relation';
import { StockThresholdsEditableTable } from '@manyun/resource-hub.ui.stock-thresholds-editable-table';

import { GutterWrapper, TinyCard } from '@manyun/dc-brain.legacy.components';
import { mergedProcessesdPointActions } from '@manyun/dc-brain.legacy.redux/actions/mergedProcessesdPointActions';

import ConfigSelection from '../config-selection-table';
import CoreTable from '../core-table';
import CreatePoint from '../create';
import Detail from '../detail';
import MergedTable from '../marged-table';
import OriginalTable from '../original-table';
import ProcessedTable from '../processed-table';
import SpecsTable from '../specs-table';
import './style.css';

const StyledTabs = Tabs;

function DeviceCard({
  selectCategoryPoint,
  height,
  cardMode,
  setCardMode,
  setTreeMode,
  treeMode,
  pattern,
  setPattern,
  refreshDeviceTreeData,
  refresCustomDeviceTreeData,
  selectedTab,
  setSelectedTab,
  setDeviceReRequest,
  resetItems,
  draggableDescriptorMutator,
}) {
  const [, { checkCode }] = useAuthorized();
  const [detail, setDetail] = useState({}); // 详情弹框
  const [attributeInfo, setEditAttributeInfo] = useState();
  const [pointTypeMap, setPointMap] = useState([
    {
      value: 'orginal',
      label: '原始测点',
    },
    {
      value: 'merge',
      label: '聚合测点',
    },
    {
      value: 'process',
      label: '加工测点',
    },
    {
      value: 'core',
      label: '核心测点',
    },
  ]);

  const handleUpdateClick = (pattern, btnType) => {
    setPattern(pattern);
    if (btnType === 'add') {
      setEditAttributeInfo(undefined);
    }
  };

  useEffect(() => {
    if (treeMode === 'space') {
      setPointMap([
        {
          value: 'merge',
          label: '聚合测点',
        },
        {
          value: 'process',
          label: '加工测点',
        },
        {
          value: 'attribute',
          label: '属性维护',
        },
      ]);
      setSelectedTab('merge');
    } else if (treeMode === 'device') {
      setPointMap([
        {
          value: 'orginal',
          label: '原始测点',
        },
        {
          value: 'merge',
          label: '聚合测点',
        },
        {
          value: 'process',
          label: '加工测点',
        },
        {
          value: 'core',
          label: '核心测点',
        },
      ]);
    } else {
      setSelectedTab('process');
      setPointMap([]);
    }
  }, [treeMode, setPointMap, setSelectedTab]);

  useEffect(() => {
    if (pattern === 'list') {
      setDetail({});
    }
  }, [pattern]);

  return (
    <TinyCard
      title={selectCategoryPoint.selectTitle || selectCategoryPoint.deviceType}
      bodyStyle={{
        height: `calc(${height} - 40px)`,
        overflowY: 'auto',
        marginBottom: '40px',
      }}
      style={{ width: `calc(100% - 330px - 1rem)` }}
    >
      {treeMode === 'device' && pattern === 'list' && (
        <>
          {selectCategoryPoint.selectType !== 'C2' && (
            <Detail
              info={detail}
              pattern={pattern}
              setPattern={setPattern}
              cardMode={cardMode}
              setSelectedTab={setSelectedTab}
              selectedTab={selectedTab}
              treeMode={treeMode}
            />
          )}
          {selectCategoryPoint.selectType === 'C2' && (
            <StyledTabs
              type="card"
              activeKey={cardMode}
              onChange={value => {
                setCardMode(value);
                if (value === 'specs') {
                  setTreeMode('device');
                }
              }}
            >
              <Tabs.TabPane key="basic" tab="基本信息">
                {selectCategoryPoint.selectType && (
                  <Detail
                    info={detail}
                    pattern={pattern}
                    setPattern={setPattern}
                    cardMode={cardMode}
                    setSelectedTab={setSelectedTab}
                    selectedTab={selectedTab}
                    treeMode={treeMode}
                  />
                )}
              </Tabs.TabPane>

              {selectCategoryPoint.numbered && selectCategoryPoint.selectType === 'C2' && (
                <Tabs.TabPane key="point" tab="测点列表">
                  <GutterWrapper mode="vertical">
                    <>
                      <Radio.Group
                        value={selectedTab}
                        onChange={({ target: { value } }) => setSelectedTab(value)}
                      >
                        {pointTypeMap.map(item => {
                          return (
                            <Radio.Button key={item.value} value={item.value}>
                              {item.label}
                            </Radio.Button>
                          );
                        })}
                      </Radio.Group>
                      &nbsp;&nbsp;&nbsp;
                      <Tooltip
                        overlayClassName="point-tooltip"
                        title={
                          <>
                            <p>聚合测点：以单测点按照合计操作符与规范统计</p>
                            <p>
                              加工测点：自定义公式以新增某一种测点（若测点包含聚合与加工，请先聚合再进行加工）
                            </p>
                            <p>
                              核心测点：从原始测点、聚合测点、加工测点当中选出的核心测点，至多12个
                            </p>
                          </>
                        }
                      >
                        <QuestionCircleOutlined />
                      </Tooltip>
                    </>
                    {selectedTab === 'orginal' && (
                      <OriginalTable
                        selectedTab={selectedTab}
                        cardMode={cardMode}
                        treeMode={treeMode}
                        setDetail={setDetail}
                        setPattern={setPattern}
                      />
                    )}
                    {selectedTab === 'merge' && (
                      <MergedTable
                        selectedTab={selectedTab}
                        cardMode={cardMode}
                        treeMode={treeMode}
                        setDetail={setDetail}
                        setPattern={setPattern}
                      />
                    )}
                    {selectedTab === 'process' && (
                      <ProcessedTable
                        selectedTab={selectedTab}
                        cardMode={cardMode}
                        treeMode={treeMode}
                        setDetail={setDetail}
                        setPattern={setPattern}
                        refresCustomDeviceTreeData={refresCustomDeviceTreeData}
                      />
                    )}
                    {selectedTab === 'core' && (
                      <CoreTable setDetail={setDetail} setPattern={setPattern} />
                    )}
                  </GutterWrapper>
                </Tabs.TabPane>
              )}

              {selectCategoryPoint.numbered && selectCategoryPoint.selectType === 'C2' && (
                <Tabs.TabPane key="specs" tab="规格列表">
                  <SpecsTable
                    cardMode={cardMode}
                    setDetail={setDetail}
                    setPattern={setPattern}
                    setSelectedTab={setSelectedTab}
                  />
                </Tabs.TabPane>
              )}

              {selectCategoryPoint.numbered &&
                treeMode === 'device' &&
                selectCategoryPoint.selectType === 'C2' && (
                  <Tabs.TabPane key="configSelection" tab="配置选型">
                    <ConfigSelection cardMode={cardMode} />
                  </Tabs.TabPane>
                )}

              {selectCategoryPoint.selectType === 'C2' && (
                <Tabs.TabPane key="stockThresholds" tab="库存阈值">
                  <StockThresholdsEditableTable
                    deviceType={selectCategoryPoint.deviceType}
                    numbered={selectCategoryPoint.numbered}
                  />
                </Tabs.TabPane>
              )}
              {checkCode('element_coursewares-tab') &&
                selectCategoryPoint.numbered &&
                selectCategoryPoint.selectType === 'C2' && (
                  <Tabs.TabPane key="coursewares" tab="知识课件">
                    <CoursewareRelation deviceType={selectCategoryPoint.deviceType} />
                  </Tabs.TabPane>
                )}
            </StyledTabs>
          )}
        </>
      )}

      {treeMode === 'space' &&
        pattern === 'list' &&
        selectCategoryPoint.numbered &&
        selectCategoryPoint.selectType === 'C2' && (
          <GutterWrapper mode="vertical">
            <>
              <Radio.Group
                value={selectedTab}
                onChange={({ target: { value } }) => setSelectedTab(value)}
              >
                {pointTypeMap.map(item => {
                  return (
                    <Radio.Button key={item.value} value={item.value}>
                      {item.label}
                    </Radio.Button>
                  );
                })}
              </Radio.Group>
              &nbsp;&nbsp;&nbsp;
              <Tooltip
                overlayClassName="point-tooltip"
                title={
                  <>
                    <p>聚合测点：以单测点按照合计操作符与规范统计</p>
                    <p>加工测点：自定义公式以新增某一种测点</p>
                    <p>若测点包含聚合与加工，请先聚合再进行加工</p>
                  </>
                }
              >
                <QuestionCircleOutlined />
              </Tooltip>
            </>
            {selectedTab === 'merge' && (
              <MergedTable
                selectedTab={selectedTab}
                cardMode={cardMode}
                treeMode={treeMode}
                setDetail={setDetail}
                setPattern={setPattern}
              />
            )}
            {selectedTab === 'process' && (
              <ProcessedTable
                selectedTab={selectedTab}
                cardMode={cardMode}
                treeMode={treeMode}
                setDetail={setDetail}
                setPattern={setPattern}
                refresCustomDeviceTreeData={refresCustomDeviceTreeData}
              />
            )}
            {selectedTab === 'attribute' && (
              <ResourceSpaceAttribute
                deviceType={selectCategoryPoint.deviceType}
                onAttributeInfoChange={setEditAttributeInfo}
                onPatternChange={handleUpdateClick}
              />
            )}
          </GutterWrapper>
        )}
      {pattern === 'list' && treeMode === 'custom' && (
        <ProcessedTable
          selectedTab={selectedTab}
          cardMode={cardMode}
          treeMode={treeMode}
          setDetail={setDetail}
          setPattern={setPattern}
          refresCustomDeviceTreeData={refresCustomDeviceTreeData}
        />
      )}
      {pattern === 'create' && (
        <CreatePoint
          cardMode={cardMode}
          setPattern={setPattern}
          selectedTab={selectedTab}
          pattern={pattern}
          info={detail}
          treeMode={treeMode}
          refresCustomDeviceTreeData={refresCustomDeviceTreeData}
          resetPointInfo={() => {
            setDetail({});
            resetItems();
          }}
          draggableDescriptorMutator={draggableDescriptorMutator}
        />
      )}
      {/* 测点详情 */}
      {pattern === 'detail' && (
        <Detail
          info={detail}
          cardMode={cardMode}
          pattern={pattern}
          setPattern={setPattern}
          selectedTab={selectedTab}
          treeMode={treeMode}
        />
      )}
      {pattern === 'edit' && (
        <CreatePoint
          cardMode={cardMode}
          pattern={pattern}
          info={detail}
          setPattern={setPattern}
          selectedTab={selectedTab}
          treeMode={treeMode}
          refreshDeviceTreeData={refreshDeviceTreeData}
          setDeviceReRequest={setDeviceReRequest}
          resetPointInfo={() => {
            setDetail({});
            resetItems();
          }}
          draggableDescriptorMutator={draggableDescriptorMutator}
        />
      )}
      {pattern === 'attributeEdit' && (
        <ResourceSpaceAttributeEditor
          deviceType={selectCategoryPoint.deviceType}
          attributeInfo={attributeInfo}
          onAttributeInfoChange={setEditAttributeInfo}
          onPatternChange={setPattern}
        />
      )}
    </TinyCard>
  );
}
const mapStateToProps = ({ doPointManage: { selectCategoryPoint } }) => ({
  selectCategoryPoint,
});
const mapDispatchToProps = {
  resetItems: mergedProcessesdPointActions.resetItems,
};
export default connect(mapStateToProps, mapDispatchToProps)(DeviceCard);
