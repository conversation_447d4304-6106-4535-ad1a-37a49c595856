import React, { useEffect, useRef, useState } from 'react';
import { connect } from 'react-redux';

import { ApiTreeSelect } from '@manyun/dc-brain.legacy.components';
import { DEVICE_SPACE_TYPE_KEY_MAP } from '@manyun/dc-brain.legacy.constants/enums/deviceType';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';

function SpareTypeSelect({ syncCommonData, deviceCategory, disabledOptions, ...props }) {
  const [treeExpandedKeys, setTreeExpandedKeys] = useState([]);
  const [inSearch, setInSearch] = useState(false);

  const apiTreeSelectRef = React.createRef();
  const treeSelectRef = useRef(null);

  useEffect(() => {
    if (!deviceCategory) {
      syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });
    }
  }, [deviceCategory, syncCommonData]);

  const disabledOptionsStr = disabledOptions.join('$$');
  useEffect(() => {
    apiTreeSelectRef.current.props.dataService();
  }, [disabledOptionsStr, apiTreeSelectRef]);

  if (!deviceCategory) {
    return null;
  }

  const extraProps = { treeDefaultExpandAll: true };
  if (!inSearch) {
    extraProps.treeExpandedKeys = treeExpandedKeys;
  }

  return (
    <ApiTreeSelect
      {...props}
      requestOnDidMount
      ref={apiTreeSelectRef}
      treeSelectRef={treeSelectRef}
      fieldNames={{ value: 'metaCode', key: 'metaCode', title: 'metaName' }}
      dataService={() => {
        // 放开限制
        const treeList = deviceCategory.treeList
          .map(item => {
            if (item.metaStyle === DEVICE_SPACE_TYPE_KEY_MAP.DEVICE) {
              return item;
            }
            return null;
          })
          .filter(Boolean);
        const newData = getDisabled(treeList, disabledOptions);
        return Promise.resolve({
          ...deviceCategory,
          treeList: newData,
        });
      }}
      style={{ width: '218px' }}
      value={props.value ? props.value.code : props.value}
      onChange={(code, label, extra) => {
        setInSearch(false);
        const metaType = extra.triggerNode?.props.dataRef.metaType;
        if (metaType === 'C0' || metaType === 'C1') {
          if (!extra.triggerNode.props.expanded) {
            setTreeExpandedKeys(expandedKeys => [
              ...expandedKeys,
              extra.triggerNode.props.dataRef.metaCode,
            ]);
          } else {
            setTreeExpandedKeys(expandedKeys =>
              expandedKeys.filter(key => key !== extra.triggerNode.props.dataRef.metaCode)
            );
          }
          treeSelectRef.current.rcTreeSelect.setOpenState(true);
          return;
        }
        props.onChange({ code: code, name: deviceCategory.normalizedList[code]?.metaName });
      }}
      {...extraProps}
      onTreeExpand={expandedKeys => {
        setTreeExpandedKeys(expandedKeys);
      }}
      onSearch={value => {
        setInSearch(true);
      }}
    />
  );
}

const mapStateToProps = ({ common: { deviceCategory } }) => {
  return {
    deviceCategory,
  };
};
const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps, null, { forwardRef: true })(
  React.forwardRef((props, ref) => <SpareTypeSelect forwardedRef={ref} {...props} />)
);

function getDisabled(tree, disabledOptions) {
  return tree.map(item => {
    if (item.metaType === 'C2') {
      return {
        ...item,
        disabled: disabledOptions.includes(item.metaCode),
      };
    }
    if (item.children && item.children.length) {
      return {
        ...item,
        children: getDisabled(item.children, disabledOptions),
      };
    }
    return item;
  });
}
