import React from 'react';

import EhanceProcessedPointForm from '@manyun/dc-brain.legacy.pages/merged-processed-point/new/components/config-card/processed-point';

function Processed({
  pressedPointRef,
  processedType,
  pattern,
  info,
  treeMode,
  draggableDescriptorMutator,
}) {
  return (
    <EhanceProcessedPointForm
      wrappedComponentRef={pressedPointRef}
      processedType={processedType}
      info={info}
      mode={pattern}
      treeMode={treeMode}
      draggableDescriptorMutator={draggableDescriptorMutator}
    />
  );
}

export default Processed;
