import PageHeader from 'antd/es/page-header';
import omit from 'lodash/omit';
import trim from 'lodash/trim';
import React from 'react';
import { connect } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { deleteBizFile } from '@manyun/dc-brain.service.delete-biz-file';
import { fetchBizFileInfos } from '@manyun/dc-brain.service.fetch-biz-file-infos';

import { FooterToolBar, GutterWrapper } from '@manyun/dc-brain.legacy.components';
import { SPACE_DEVICE_TYPE_KEY_MAP } from '@manyun/dc-brain.legacy.constants/enums/deviceType';
import {
  POINT_DATA_TYPE_CODE_MAP,
  POINT_TYPE_CODE_MAP,
} from '@manyun/dc-brain.legacy.constants/point';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  MetaCategoryNumAction,
  doPointActions,
} from '@manyun/dc-brain.legacy.redux/actions/doPointActions';
import {
  createMergedPoint,
  createPressedPoint,
  getPointDetail,
  mergedProcessesdPointActions,
  updatePressedPointActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/mergedProcessesdPointActions';
import { deviceTypeService, doPointService, specService } from '@manyun/dc-brain.legacy.services';

import Basic from './basic';
import Merged from './merged';
import Orginal from './orginal';
import Processed from './processed';
import Specs from './specs';

function throws(error) {
  throw new Error(error);
}

class CreatePoint extends React.Component {
  state = {
    _pointConfigRefMap: new Map(),
    orginalCreatefieldsValues: {},
  };

  _pressedPointRef = React.createRef();

  orginalPointRef = React.createRef();

  basicPointRef = React.createRef();

  specsRef = React.createRef();

  componentDidMount() {
    const { info, pattern, treeMode } = this.props;
    if (pattern === 'edit') {
      if (info.pointCode && info.deviceType) {
        const params = {
          deviceType: info.deviceType,
          pointCode: info.pointCode,
          treeMode,
          info,
          mode: 'edit',
        };
        this.props.getPointDetail(params);
      }
    }
  }

  componentDidUpdate(prevProps) {
    const { margedPointIds, info, selectedTab, pattern } = this.props;
    if (
      (selectedTab === 'merge' && margedPointIds.length !== prevProps.margedPointIds.length) ||
      margedPointIds.some(id => !prevProps.margedPointIds.includes(id))
    ) {
      const newMap = new Map(margedPointIds.map(id => [id, React.createRef()]));
      this.setState({
        _pointConfigRefMap: newMap,
      });
    }
    if (prevProps.pattern !== 'edit' && pattern === 'edit' && info.pointCode && info.deviceType) {
      const params = {
        deviceType: info.deviceType,
        pointCode: info.pointCode,
        mode: 'edit',
      };
      this.props.getPointDetail(params);
    }
  }

  submitAddPoint = () =>
    new Promise(async resolve => {
      const { selectedTab, pattern, cardMode, treeMode, selectCategoryPoint, info } = this.props;

      //基本信息
      if (cardMode === 'basic' && pattern === 'edit') {
        try {
          await this.basicPointRef.current.validateFields().catch(throws);
          const values = this.basicPointRef.current.getFieldsValue();

          const { fileInfoList } = values;
          if (fileInfoList && !fileInfoList.length && selectCategoryPoint.deviceType) {
            //selectCategoryPoint.deviceType 阻止修改测点时调用
            const { data: fileInfoData } = await fetchBizFileInfos({
              targetId: selectCategoryPoint.deviceType,
              targetType: 'DEVICE_TYPE',
            });

            if (fileInfoData.data.length) {
              const fileInfo = fileInfoData.data[0];
              await deleteBizFile({
                targetId: fileInfo.targetId,
                targetType: fileInfo.targetType,
                fileType: fileInfo.type,
                filePath: fileInfo.id.toString(),
              });
            }
          }

          const { response, error } = await deviceTypeService.editDeviceType(values);
          if (error) {
            message.error(error);
          }
          if (response) {
            message.success('编辑成功！');
            this.props.selectCategory({ selectTitle: values.name });
            this.props.setPattern('list');
            this.props.syncCommonData({ strategy: { deviceCategory: 'FORCED' } });
            this.props.setDeviceReRequest();
          }
        } catch (error) {}
        return;
      }
      if (cardMode === 'point') {
        // 原始测点
        if (selectedTab === 'orginal') {
          this.orginalPointRef.current.props.form.validateFieldsAndScroll(async (errs, values) => {
            if (errs) {
              return resolve(false);
            }

            let status = [];
            if (values.dataType === 'DI' || values.dataType === 'DO') {
              for (let i = 0; i < values.digitalNum; i++) {
                const str = i + '=' + values[i];
                status = [...status, str];
              }
            }
            if (values.validLimits && (values.dataType === 'AI' || values.dataType === 'AO')) {
              status =
                typeof values.validLimits.ge === 'number' &&
                typeof values.validLimits.le === 'number' // 兼容0的判断
                  ? [`ge=${values.validLimits.ge}`, `le=${values.validLimits.le}`]
                  : [];
            }

            const params = {
              ...values,
              validLimits: status,
              deviceType: this.props.selectCategoryPoint.deviceType,
              pointType: POINT_TYPE_CODE_MAP.ORI,
            };

            if (pattern === 'create') {
              const { response, error } = await doPointService.fetchAddPoint({
                ...params,
                code: generatePointCode(values.code, values.dataType),
              });
              if (error) {
                message.error(error);
              }
              if (response) {
                message.success('新增测点成功');
                this.props.setPattern('list');
              }
            }
            if (pattern === 'edit') {
              this.setState({
                editParams: { ...params, code: this.props.info.code },
              });
              return resolve(true);
            }
          });
        }
        // 聚合测点
        if (selectedTab === 'merge') {
          try {
            if (this.state._pointConfigRefMap.size <= 0) {
              throws('至少添加一个测点！');
            }
            for (const [, pointConfigRef] of this.state._pointConfigRefMap) {
              if (pointConfigRef.current) {
                await pointConfigRef.current.props.form.validateFieldsAndScroll().catch(throws);
              }
            }
            // 新增
            if (pattern === 'create') {
              this.props.createMergedPoint({
                setPattern: this.props.setPattern,
                getMetaCategoryNum: this.props.getMetaCategoryNum,
                dimension: getDimension(selectCategoryPoint.deviceType),
              });
            }
          } catch (error) {}
        }
        // 加工测点
        if (selectedTab === 'process') {
          await this._pressedPointRef.current.props.form.validateFieldsAndScroll((err, values) => {
            if (err) {
              return resolve(false);
            }
            // DI加工测点改造原判断validLimits逻辑移除
            if (pattern === 'create') {
              this.props.createPressedPoint({
                setPattern: this.props.setPattern,
                deviceType: selectCategoryPoint.deviceType,
                dimension: getDimension(selectCategoryPoint.deviceType),
                refresCustomDeviceTreeData: this.props.refresCustomDeviceTreeData,
                treeMode,
              });
            }
            //仅仅是编辑点击确认前更新下editParams ...
            if (pattern === 'edit') {
              this.setState({
                editParams: {
                  deviceType: this.props.info.deviceType,
                  pointCode: this.props.info.pointCode,
                },
              });
              return resolve(true);
            }
          });
        }
      }
      // 规格
      if (cardMode === 'specs') {
        try {
          await this.specsRef.current.props.form.validateFieldsAndScroll().catch(throws);
          const values = this.specsRef.current.props.form.getFieldsValue();
          const params = {
            ...omit(values, ['specUnit']),
            deviceType: selectCategoryPoint.deviceType,
            specName: trim(values.specName),
          };
          if (pattern === 'edit') {
            params.id = info.id;
            params.unit = values.specUnit;
            const { error } = await specService.editSpec(params);
            if (error) {
              message.error(error);
              return;
            }
            message.success('编辑规格成功');
            this.props.setPattern('list');
          }
          if (pattern === 'create') {
            const { error } = await specService.addSpec({ ...params, specUnit: values.specUnit });
            if (error) {
              message.error(error);
              return;
            }
            message.success('添加规格成功');
            this.props.setPattern('list');
          }
        } catch (error) {}
      }
    });

  editOrginalPoint = async operatorNotes => {
    const { editParams } = this.state;
    const { response, error } = await doPointService.updatePoint({
      ...editParams,
      operatorNotes: operatorNotes,
    });
    if (error) {
      message.error(error);
    }
    if (response) {
      message.success('编辑测点成功');
      this.props.setPattern('list');
    }
  };

  editProcessedPoint = async operatorNotes => {
    const { editParams } = this.state;
    const { info, treeMode } = this.props;
    this.props.editPressedPoint({
      data: {
        ...editParams,
        operatorNotes,
      },
      treeMode,
      info,
      successCb: () => {
        this.props.setPattern('list');
        this.props.getMetaCategoryNum();
      },
    });
  };

  render() {
    const {
      selectedTab,
      info,
      pattern,
      treeMode,
      draggableDescriptorMutator,
      cardMode,
      selectCategoryPoint,
    } = this.props;
    const { orginalCreatefieldsValues } = this.state;
    return (
      <GutterWrapper style={{ marginBottom: '40px' }} mode="vertical">
        <PageHeader
          title=" "
          onBack={() => {
            if (treeMode === 'custom' && !selectCategoryPoint.deviceType) {
              this.props.selectCategory();
            }
            this.props.setPattern('list');
          }}
        />
        {cardMode === 'point' && selectedTab === 'orginal' && (
          <Orginal
            setPattern={this.props.setPattern}
            info={info}
            pattern={pattern}
            wrappedComponentRef={this.orginalPointRef}
            resetPointInfo={this.props.resetPointInfo}
            orginalCreatefieldsValues={orginalCreatefieldsValues}
            updateOrginalCreatefieldsValues={values =>
              this.setState(({ orginalCreatefieldsValues }) => ({
                orginalCreatefieldsValues: { ...orginalCreatefieldsValues, ...values },
              }))
            }
          />
        )}
        {cardMode === 'point' && selectedTab === 'merge' && (
          <Merged
            pointConfigRefMap={this.state._pointConfigRefMap}
            info={info}
            pattern={pattern}
            mergedType={
              treeMode === 'device' ? POINT_TYPE_CODE_MAP.AGG_DEVICE : POINT_TYPE_CODE_MAP.AGG_SPACE
            }
          />
        )}
        {cardMode === 'point' && selectedTab === 'process' && (
          <Processed
            pressedPointRef={this._pressedPointRef}
            info={info}
            pattern={pattern}
            treeMode={treeMode}
            processedType={
              treeMode === 'device' ? POINT_TYPE_CODE_MAP.CAL_DEVICE : POINT_TYPE_CODE_MAP.CAL_SPACE
            }
            draggableDescriptorMutator={draggableDescriptorMutator}
          />
        )}
        {cardMode === 'specs' && (
          <Specs
            wrappedComponentRef={this.specsRef}
            info={info}
            pattern={pattern}
            setPattern={this.props.setPattern}
            resetPointInfo={this.props.resetPointInfo}
          />
        )}
        {cardMode === 'basic' && pattern === 'edit' && (
          <Basic
            wrappedComponentRef={this.basicPointRef}
            pattern={pattern}
            setPattern={this.props.setPattern}
          />
        )}
        <FooterToolBar>
          <Space>
            <Button onClick={() => this.props.setPattern('list')}>取消</Button>
            {cardMode === 'point' && selectedTab !== 'merge' && pattern === 'edit' ? (
              <DeleteConfirm
                targetName={'测点'}
                title="你确定要修改此测点吗？"
                reasonPlaceholder={'请输入修改此测点的原因'}
                canOpen={this.submitAddPoint}
                onOk={({ reason }) => {
                  if (selectedTab === 'orginal') {
                    this.editOrginalPoint(reason);
                  } else {
                    this.editProcessedPoint(reason);
                  }
                }}
                onCancel={() => this.setState({ deleteConfirmVisible: false })}
              >
                <Button type="primary">提交</Button>
              </DeleteConfirm>
            ) : (
              <Button type="primary" onClick={this.submitAddPoint}>
                提交
              </Button>
            )}
          </Space>
        </FooterToolBar>
      </GutterWrapper>
    );
  }
}

const mapStateToProps = ({
  mergedProcessesdPoint: {
    margedPointIds,
    processedPointList: { statusList },
  },
  doPointManage: { selectCategoryPoint },
}) => {
  return {
    margedPointIds,
    selectCategoryPoint,
    statusList: statusList.value,
  };
};
const mapDispatchToProps = {
  createMergedPoint,
  getPointDetail,
  createPressedPoint,
  editPressedPoint: updatePressedPointActionCreator,
  syncCommonData: syncCommonDataActionCreator,
  getMetaCategoryNum: MetaCategoryNumAction,
  selectCategory: doPointActions.selectCategoryPoint,
  updateFormValues: mergedProcessesdPointActions.updateProcessedPointFormValues,
};
export default connect(mapStateToProps, mapDispatchToProps)(CreatePoint);

function getDimension(deviceType) {
  const deviceAndDimension = {};
  Object.keys(SPACE_DEVICE_TYPE_KEY_MAP).forEach(key => {
    deviceAndDimension[SPACE_DEVICE_TYPE_KEY_MAP[key]] = key;
  });
  if (deviceAndDimension[deviceType]) {
    return deviceAndDimension[deviceType];
  }
  return 'DEVICE';
}
const generatePointCode = (pointCode, pointType) => {
  switch (pointType) {
    case POINT_DATA_TYPE_CODE_MAP.AI:
      return `1${fillZero(pointCode)}`;
    case POINT_DATA_TYPE_CODE_MAP.AO:
      return `3${fillZero(pointCode)}`;
    case POINT_DATA_TYPE_CODE_MAP.DI:
      return `2${fillZero(pointCode)}`;

    case POINT_DATA_TYPE_CODE_MAP.DO:
      return `4${fillZero(pointCode)}`;

    default:
      return `5${fillZero(pointCode)}`;
  }
};

const fillZero = pointCode => {
  const codeLength = pointCode.toString().length;
  if (codeLength === 1) {
    return `00${pointCode}`;
  } else if (codeLength === 2) {
    return `0${pointCode}`;
  } else {
    return pointCode.toString();
  }
};
