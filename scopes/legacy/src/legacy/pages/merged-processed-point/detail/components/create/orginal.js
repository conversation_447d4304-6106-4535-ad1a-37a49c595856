import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import ExclamationCircleOutlined from '@ant-design/icons/es/icons/ExclamationCircleOutlined';
import { ApiSelect, Select } from '@galiojs/awesome-antd';
import isNil from 'lodash.isnil';

import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';

import { AlarmLevelSelect } from '@manyun/monitoring.ui.alarm-level-select';
import { fetchPointsByCondition } from '@manyun/resource-hub.service.fetch-points-by-condition';

import { METADATA_TYPE } from '@manyun/dc-brain.legacy.constants';
import { POINT_TYPE_CODE_MAP } from '@manyun/dc-brain.legacy.constants/point';
import { POINT_PRECISION } from '@manyun/dc-brain.legacy.constants/point';
import { addPointAction } from '@manyun/dc-brain.legacy.redux/actions/doPointActions';
import { getMetadataByType } from '@manyun/dc-brain.legacy.redux/actions/eventCenterActions';
import { doPointService } from '@manyun/dc-brain.legacy.services';

import ValidLimits from '../../../components/valid-limits-inputNumber';

const statusErrors = {
  repeat: '不可重复！',
  blank: '状态名称必填！',
};

// export const ALARM_LEVEL = [
//   {
//     key: 1,
//     value: 1,
//   },
//   {
//     key: 2,
//     value: 2,
//   },
//   {
//     key: 3,
//     value: 3,
//   },
//   {
//     key: 4,
//     value: 4,
//   },
//   {
//     key: 5,
//     value: 5,
//   },
// ];

class Orginal extends Component {
  state = {
    digitalNum: '',
    PointDataType: [],
    currentMaxPointCode: 0,
    // 测点数据类型
    selectPointType: POINT_TYPE_CODE_MAP.ORI,
  };

  componentDidMount() {
    const { info, updateOrginalCreatefieldsValues } = this.props;

    if (
      (info.dataType?.code === 'DI' || info.dataType?.code === 'DO') &&
      info.validLimits !== null &&
      info.validLimits !== undefined
    ) {
      const statusFieldsValues = {};
      info.validLimits.forEach(item => {
        const arr = item.split('=');
        const status = {
          value: arr[1],
        };
        statusFieldsValues[[arr[0]]] = status;
      });
      updateOrginalCreatefieldsValues(statusFieldsValues);
    }
  }

  componentWillUnmount() {
    this.props.resetPointInfo();
  }

  getFields() {
    const {
      form: { getFieldDecorator, getFieldsValue },
      info,
      digitalNum,
      digitalNumToArr,
    } = this.props;
    const children = [];
    const validLimits = info.validLimits;
    const digitalDescJson = {};

    if (validLimits !== null && validLimits !== undefined) {
      validLimits.forEach(item => {
        const arr = item.split('=');
        digitalDescJson[arr[0]] = arr[1];
      });
    }

    for (let i = 0; i < (digitalNum ?? getFieldsValue().digitalNum); i++) {
      children.push(
        <Col span={4} key={i}>
          <Form.Item label={`${i}`} labelCol={{ xxl: 6, xl: 7 }} wrapperCol={{ xxl: 18, xl: 17 }}>
            {getFieldDecorator(`${i}`, {
              rules: [
                {
                  required: true,
                  validator: ({ filed }, value, callback) => {
                    if (!value || !value.trim()) {
                      callback(statusErrors.blank);
                      return;
                    }
                    const statusFiledsValues = getFieldsValue();
                    const filedsValueArr = digitalNumToArr.map(item => ({
                      name: item,
                      value: statusFiledsValues[item],
                    }));
                    let tmp = judgeStatusIsRepeated(filedsValueArr, { name: filed, value });
                    if (tmp) {
                      callback(statusErrors.repeat);
                      return;
                    }
                    callback();
                  },
                },
                { max: 8, message: '最多输入 8 个字符！' },
              ],
              initialValue: digitalDescJson[i],
            })(<Input />)}
          </Form.Item>
        </Col>
      );
    }
    return children;
  }

  getPointDataType = async () => {
    const data = await doPointService.fetchGetPointDataType();
    if (data) {
      this.setState({
        PointDataType: data,
      });
    }
  };

  changeDataType = val => {
    this.setState({
      dataType: val,
    });
  };

  getDimensionType = async () => {
    const data = await doPointService.fetchDimensionType();
    if (data) {
      this.setState({
        dimensionTypeData: data,
      });
    }
  };

  getSpecUnit = () => {
    if (this.props.specUnitList.length) {
      return false;
    }
    this.props.getMetadataByType(METADATA_TYPE['SPEC_UNIT']);
  };

  render() {
    const form = this.props.form;
    const { currentMaxPointCode } = this.state;

    const { getFieldDecorator, getFieldsValue, getFieldValue } = form;
    const { specUnitList, info, pattern } = this.props;
    return (
      <Form colon={false} labelCol={{ span: 3 }} wrapperCol={{ span: 21 }} autoComplete="off">
        <Form.Item label="测点名称">
          {getFieldDecorator('name', {
            rules: [
              { required: true, whitespace: true, message: '请输入测点名称' },
              {
                max: 32,
                message: '最多输入 32 个字符！',
              },
            ],
            initialValue: info.name,
          })(<Input style={{ width: 468 }} />)}
        </Form.Item>
        <Form.Item label="测点类型">
          {getFieldDecorator('dataType', {
            rules: [{ required: true, message: '请选择测点类型' }],
            initialValue: info.dataType?.code,
          })(
            <ApiSelect
              disabled={pattern === 'edit' ? true : false}
              fieldNames={{ label: 'value', value: 'key' }}
              dataService={async () => {
                const response = await doPointService.fetchGetPointDataType();
                if (response) {
                  return Promise.resolve(response);
                } else {
                  return Promise.resolve([]);
                }
              }}
              onChange={dataType => {
                const that = this;
                (async function () {
                  const { error, data } = await fetchPointsByCondition({
                    pointTypeList: ['ORI'],
                    dataTypeList: [dataType],
                    deviceType: that.props.deviceType,
                  });
                  if (error) {
                    message.error(error.message);
                    return;
                  }
                  if (data.data.length) {
                    that.setState({
                      currentMaxPointCode: data.data
                        .map(item => item.code)
                        .sort((pre, post) => post - pre)[0],
                    });
                  } else {
                    that.setState({
                      currentMaxPointCode: 0,
                    });
                  }
                })();
              }}
              style={{ width: 120 }}
              trigger="onDidMount"
            />
          )}
        </Form.Item>
        {pattern !== 'edit' && (
          <Form.Item label="测点序号">
            {getFieldDecorator('code', {
              rules: [
                { required: true, message: '请输入测点序号' },

                {
                  type: 'number',
                  max: 999,
                  min: 1,
                  message: '请输入3位正整数',
                },
              ],
              initialValue: formatPointCode(currentMaxPointCode),
            })(<InputNumber style={{ width: 70 }} />)}
          </Form.Item>
        )}
        {getFieldValue('dataType') === 'ALARM' && (
          <>
            <Form.Item label="告警类型">
              {getFieldDecorator('alarmType', {
                rules: [{ required: true, message: '请选择告警类型' }],
                initialValue: info.alarmType?.code,
              })(
                <ApiSelect
                  trigger="onDidMount"
                  fieldNames={{ label: 'value', value: 'key' }}
                  dataService={async () => {
                    const response = await doPointService.fetchAlarmType();
                    if (response) {
                      return Promise.resolve(response);
                    } else {
                      return Promise.resolve([]);
                    }
                  }}
                  style={{ width: 200 }}
                />
              )}
            </Form.Item>
            <Form.Item label="告警水平">
              {getFieldDecorator('alarmLevel', {
                rules: [{ required: true, message: '请选择告警水平' }],
                initialValue: info.alarmLevel,
              })(<AlarmLevelSelect trigger="onDidMount" style={{ width: 200 }} />)}
            </Form.Item>
          </>
        )}
        <Form.Item label="扩展测点数量">
          {getFieldDecorator('extCount', {
            rules: [
              {
                type: 'number',
                max: 250,
                message: '请输入小于250的正整数',
              },
            ],
            initialValue: info.extCount,
          })(<InputNumber min={0} max={251} style={{ width: 70 }} />)}
        </Form.Item>
        <Form.Item label="扩展测点名称">
          {getFieldDecorator('extName', {
            rules: [
              {
                required: form.getFieldsValue().extCount ? true : false,
                whitespace: true,
                message: '请输入扩展测点名称',
              },
              {
                max: 30,
                message: '最多输入 30 个字符！',
              },
            ],
            initialValue: info.extName,
          })(<Input style={{ width: 200 }} />)}
          &nbsp;&nbsp;
          <Tooltip title="存在多个扩展点位时，系统会在扩展测点名称上自动生成序号">
            <ExclamationCircleOutlined />
          </Tooltip>
        </Form.Item>
        <Form.Item label="精度">
          {getFieldDecorator('precision', {
            rules: [
              {
                required:
                  getFieldValue('dataType') === 'AI' || getFieldValue('dataType') === 'AO'
                    ? true
                    : false,
                message: '请选择精度!',
              },
            ],
            initialValue: info.precision,
          })(
            <Select style={{ width: 200 }} allowClear>
              {POINT_PRECISION.map(precision => (
                <Select.Option key={String(precision)} value={precision}>
                  {String(precision)}
                </Select.Option>
              ))}
            </Select>
          )}
        </Form.Item>
        <Form.Item label="单位">
          {getFieldDecorator('unit', {
            initialValue: info.unit,
          })(
            <Select showSearch={true} onFocus={this.getSpecUnit} style={{ width: 200 }}>
              {specUnitList.map(item => (
                <Select.Option key={item.metaCode}>{item.metaName}</Select.Option>
              ))}
            </Select>
          )}
        </Form.Item>
        {(getFieldValue('dataType') === 'AI' || getFieldValue('dataType') === 'AO') && (
          <Form.Item label="工作区间">
            {getFieldDecorator('validLimits', {
              rules: [
                {
                  validator: dataRangeValidator,
                },
              ],
              initialValue:
                info.validLimits?.ge && info.validLimits?.le
                  ? { ge: +info.validLimits.ge, le: +info.validLimits.le }
                  : info.validLimits,
            })(<ValidLimits />)}
          </Form.Item>
        )}
        {(getFieldValue('dataType') === 'DI' || getFieldValue('dataType') === 'DO') && (
          <>
            <Form.Item label="状态数量">
              {getFieldDecorator('digitalNum', {
                rules: [
                  {
                    required: true,
                    message: '状态数量为必填项',
                  },
                  {
                    type: 'number',
                    max: 50,
                    message: '状态数量最大值为50',
                  },
                  { pattern: /^\d+$/, message: '状态数量必须为整数' },
                ],
                initialValue:
                  info.validLimits && Array.isArray(info.validLimits)
                    ? info.validLimits.length
                    : null,
              })(<InputNumber min={1} max={51} style={{ width: 70 }} />)}
            </Form.Item>

            {getFieldsValue().digitalNum && (
              <Form.Item label="状态含义">
                {getFieldDecorator('validLimits')(<Row gutter={16}>{this.getFields()}</Row>)}
              </Form.Item>
            )}
          </>
        )}
        <Form.Item label="备注">
          {getFieldDecorator('description', {
            initialValue: info.description,
            rules: [
              {
                max: 60,
                message: '最多输入 60 个字符！',
              },
            ],
          })(<Input.TextArea rows={4} style={{ width: 200 }} />)}
        </Form.Item>
      </Form>
    );
  }
}
const mapStateToProps = (
  { doPointManage: { deviceType }, eventCenter: { specUnitList } },
  { orginalCreatefieldsValues }
) => {
  const digitalNum =
    orginalCreatefieldsValues.digitalNum && orginalCreatefieldsValues.digitalNum.value
      ? orginalCreatefieldsValues.digitalNum.value > 50
        ? 50
        : orginalCreatefieldsValues.digitalNum.value
      : null;
  const digitalNumToArr = digitalNum ? getStatusNumArr(digitalNum) : [];

  return {
    specUnitList,
    deviceType,
    digitalNum,
    digitalNumToArr,
  };
};

const mapDispatchToProps = {
  getMetadataByType: getMetadataByType,
  addPointAction,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(
  Form.create({
    name: 'add_point',
    mapPropsToFields({ orginalCreatefieldsValues }) {
      return Object.keys(orginalCreatefieldsValues).reduce((formFields, fieldKey) => {
        formFields[fieldKey] = Form.createFormField(orginalCreatefieldsValues[fieldKey]);
        return formFields;
      }, {});
    },
    onFieldsChange(props, changedFields, allFields) {
      let fileds = changedFields;
      const digitalNum = props.digitalNum;
      const digitalNumToArr = props.digitalNumToArr;
      if (digitalNum && digitalNumToArr.length) {
        // 状态含义改变时 判断含义是否重复
        const filedsValueArr = digitalNumToArr.map(item =>
          allFields[item] ? allFields[item] : { name: item, value: '' }
        );
        const filterOnlySameFiledsError = filedsValueArr.map(currentStatus => {
          // 查找value相同，但是name 不同的数据
          const repeated = judgeStatusIsRepeated(filedsValueArr, currentStatus);
          if (repeated) {
            return {
              ...currentStatus,
              errors: [
                {
                  field: currentStatus.name,
                  message: statusErrors.repeat,
                },
              ],
            };
          }
          return {
            ...currentStatus,
            errors:
              !currentStatus.value || !currentStatus.value.trim()
                ? [
                    {
                      name: currentStatus.name,
                      field: statusErrors.blank,
                    },
                  ]
                : undefined,
            dirty: false,
          };
        });
        let newFieldsValue = {};
        filterOnlySameFiledsError.forEach(filed => {
          newFieldsValue[filed.name] = filed;
        });
        fileds = { ...fileds, ...newFieldsValue };
      }
      props.updateOrginalCreatefieldsValues(fileds);
    },
  })(Orginal)
);

function dataRangeValidator(__, validLimits, callback) {
  let ge = null;
  let le = null;
  if (validLimits) {
    ge = validLimits.ge;
    le = validLimits.le;
  }
  if (ge && !le && typeof le !== 'number') {
    callback('最大值必填！');
  } else if (ge !== 0 && isNil(ge) && le) {
    callback('最小值必填！');
  } else if (typeof le === 'number' && typeof ge === 'number' && Number(le) <= Number(ge)) {
    callback('最小值要小于最大值！');
  } else {
    callback();
  }
}

function getStatusNumArr(num) {
  let arr = [];
  for (let i = 0; i < num; i++) {
    arr.push(String(i));
  }
  return arr;
}

function judgeStatusIsRepeated(data, current) {
  return !!data.find(
    single =>
      single.value &&
      current.value &&
      single.value === current.value &&
      single.name !== current.name
  );
}
const formatPointCode = pointCode => {
  if (typeof pointCode === 'string') {
    const pointCodeNumber = parseInt(pointCode.split('').slice(1, 4).join(''));
    return pointCodeNumber < 999 ? pointCodeNumber + 1 : pointCodeNumber;
  } else {
    return 1;
  }
};
