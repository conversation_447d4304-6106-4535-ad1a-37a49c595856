import React from 'react';

import PlusCircleFilled from '@ant-design/icons/es/icons/PlusCircleFilled';
import { EditableTable, Form } from '@galiojs/awesome-antd';
import { generateGetRowSpan } from '@galiojs/awesome-antd/lib/table/utils';
import cloneDeep from 'lodash.clonedeep';
import shortid from 'shortid';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Tag } from '@manyun/base-ui.ui.tag';

import { ModelSelect } from '@manyun/crm.ui.model-select';

import SpareTypeSelect from './spare-type-select';

function ConfigSelectionTable({
  showActionsColumn,
  tableData,
  deviceType,
  editingRowKey,
  deleteByCancel,
  setEditingRowKey,
  setDeleteByCancel,
  setTableData,
  loading,
  onSave,
  onDelete,
  onCancel,
  onEdit,
}) {
  const [form] = Form.useForm();

  const addRow = (rowData, insertIdx) => {
    const nextData = cloneDeep(tableData);
    nextData.splice(insertIdx, 0, rowData);
    setTableData(nextData);
    setDeleteByCancel(true);
    setEditingRowKey(rowData.id);
  };

  return (
    <EditableTable
      form={form}
      rowKey="id"
      size="small"
      mergeProp="mergeRowsKey"
      loading={loading}
      showActionsColumn={showActionsColumn}
      editingRowKey={editingRowKey}
      dataSource={tableData}
      columns={getColumns({
        deviceType,
        addRow,
        editingRowKey,
        showActionsColumn,
        getRowSpan: generateGetRowSpan(tableData),
        tableData,
        deleteByCancel,
      })}
      onEdit={onEdit}
      onSave={onSave}
      onCancel={onCancel}
      onDelete={onDelete}
    />
  );
}

export default ConfigSelectionTable;

const getColumns = ({
  deviceType,
  addRow,
  editingRowKey,
  showActionsColumn,
  getRowSpan,
  tableData,
  deleteByCancel,
}) => [
  {
    title: '设备型号',
    dataIndex: 'deviceModel',
    editable: true,
    mergeStrategy: {
      rows: true,
    },
    editingCtrl: () => {
      let disabledOptions = tableData.map(({ deviceModel }) => deviceModel);
      return (
        <ModelSelect
          style={{ width: '150px' }}
          trigger="onDidMount"
          deviceTypes={[deviceType]}
          disabledOptions={disabledOptions}
          disabled={!deleteByCancel}
        />
      );
    },
    decorateOptions: {
      rules: [{ required: true, message: '设备型号必选！' }],
    },
    render(deviceModel, record, idx) {
      let children = deviceModel;
      if (idx === 0 && !editingRowKey && showActionsColumn) {
        children = (
          <span>
            <PlusCircleFilled
              style={{ color: `var(--${prefixCls}-primary-color)`, marginRight: 8 }}
              onClick={() => {
                const key = shortid();
                addRow({
                  id: key,
                  mergeRowsKey: key,
                  deviceModel: undefined,
                  spareType: undefined,
                  spareModelList: [],
                });
              }}
            />
            {deviceModel}
          </span>
        );
      }
      return {
        children,
        props: {
          rowSpan: getRowSpan(record, idx),
        },
      };
    },
  },
  {
    title: '配置类型',
    dataIndex: 'spareType',
    editable: true,
    editingCtrl: () => {
      const currentRow = tableData.find(({ id }) => id === editingRowKey);
      let disabledOptions = [];
      if (currentRow) {
        disabledOptions = tableData
          .map(({ spareType, deviceModel }) => {
            if (currentRow.deviceModel && deviceModel === currentRow.deviceModel) {
              return spareType?.code;
            }
            return null;
          })
          .filter(Boolean);
      }
      return <SpareTypeSelect disabledOptions={disabledOptions} disabled={!deleteByCancel} />;
    },
    decorateOptions: {
      rules: [{ required: true, message: '配置类型必选！' }],
    },
    render(spareType, record, idx) {
      const rowSpan = getRowSpan(record, idx);
      if (rowSpan >= 1 && showActionsColumn && spareType && !editingRowKey) {
        return (
          <span>
            <PlusCircleFilled
              style={{ color: `var(--${prefixCls}-primary-color)`, marginRight: 8 }}
              onClick={() => {
                addRow(
                  {
                    ...record,
                    id: shortid(),
                    spareType: undefined,
                    spareModelList: [],
                  },
                  idx
                );
              }}
            />
            {spareType?.name}
          </span>
        );
      }
      return spareType?.name;
    },
  },
  {
    title: '适配型号',
    dataIndex: 'spareModelList',
    editable: true,
    editingCtrl: fieldsValue => {
      let disabled = false;
      if (!fieldsValue.spareType) {
        disabled = true;
      }

      return (
        <ModelSelect
          style={{ width: '150px' }}
          trigger="onDidMount"
          mode="multiple"
          disabled={disabled}
          deviceTypes={fieldsValue.spareType?.code ? [fieldsValue.spareType.code] : undefined}
        />
      );
    },
    render: spareModelList => {
      return (
        <>
          {spareModelList.length
            ? spareModelList.map(item => {
                return <Tag key={item}>{item}</Tag>;
              })
            : '全部'}
        </>
      );
    },
  },
];
