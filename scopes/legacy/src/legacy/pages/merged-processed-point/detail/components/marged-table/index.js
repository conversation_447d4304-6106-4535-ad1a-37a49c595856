import saveAs from 'file-saver';
import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';

import { useAuthorized } from '@manyun/auth-hub.hook.gql.use-authorized';
import { Button } from '@manyun/base-ui.ui.button';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { exportPoint } from '@manyun/resource-hub.service.export-point';
import { fetchPointsByCondition } from '@manyun/resource-hub.service.fetch-points-by-condition';

import { Ellipsis, TinyTable } from '@manyun/dc-brain.legacy.components';
import { POINT_TYPE_CODE_MAP } from '@manyun/dc-brain.legacy.constants/point';
import { deletePointAction } from '@manyun/dc-brain.legacy.redux/actions/doPointActions';

function MergedTable({
  setPattern,
  selectedTab,
  selectedPane,
  selectCategoryPoint,
  treeMode,
  deletePointInTable,
  setDetail,
}) {
  const [, { checkCode }] = useAuthorized();
  const [alltableData, setAllTableData] = useState([]);
  const [tableData, setTableData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [count, setCount] = useState(1);
  const [exportLoading, setExportLoading] = useState(false);

  useEffect(() => {
    if (selectCategoryPoint.deviceType && selectCategoryPoint.selectType === 'C2') {
      if (treeMode === 'device' && selectCategoryPoint.type === 'device') {
        // 设备类型下
        (async function getMergedPoinsInDevice() {
          setLoading(true);
          const { data, error } = await fetchPointsByCondition({
            deviceType: selectCategoryPoint.deviceType,
            dataTypeList: ['AI', 'DI'],
            pointTypeList: [POINT_TYPE_CODE_MAP.AGG_DEVICE],
            isRemoveSub: true,
          });
          if (error) {
            message.error(error);
            setLoading(false);
          }
          if (data) {
            const result = data.data.map(point => point.toApiObject());
            setTableData(result);
            setAllTableData(result);
            setLoading(false);
          }
        })();
      }
      if (treeMode === 'space' && selectCategoryPoint.type === 'space') {
        // 空间类型下
        (async function getMergePointsInSpace() {
          setLoading(true);
          const { data, error } = await fetchPointsByCondition({
            deviceType: selectCategoryPoint.deviceType,
            dataTypeList: ['AI', 'DI'],
            pointTypeList: [POINT_TYPE_CODE_MAP.AGG_SPACE],
            isRemoveSub: true,
          });
          if (error) {
            message.error(error);
            setLoading(false);
          }
          if (data) {
            const result = data.data.map(point => point.toApiObject());
            setTableData(result);
            setAllTableData(result);
            setLoading(false);
          }
        })();
      }
    }
  }, [selectCategoryPoint, selectedTab, selectedPane, treeMode, count]);

  const deletePoint = ({ code }, operatorNotes) => {
    return new Promise(resolve => {
      deletePointInTable({
        params: { code, operatorNotes, deviceType: selectCategoryPoint.deviceType },
        successCb: () => {
          resolve(true);
          setCount(count => count + 1);
        },
        errorCb: () => {
          resolve(false);
        },
      });
    });
  };

  const searchPoint = value => {
    if (!value) {
      setTableData(alltableData);
      return;
    }
    let index = '';
    const newData = alltableData.filter(({ name, code }) => {
      index = name.indexOf(value) !== -1 ? name.indexOf(value) : code.indexOf(value);
      if (index > -1) {
        return true;
      } else {
        return false;
      }
    });
    setTableData(newData);
  };

  const onExportPointChange = async () => {
    setExportLoading(true);
    const { error, data } = await exportPoint({
      pointTypeList: treeMode === 'space' ? ['AGG_SPACE'] : ['AGG_DEVICE'],
      deviceType: selectCategoryPoint.deviceType,
    });

    setExportLoading(false);

    if (error) {
      message.error(error.message);
      return;
    }
    saveAs(data, '聚合测点.xlsx');
  };

  return (
    <>
      {selectCategoryPoint.selectType === 'C2' && (
        <TinyTable
          rowKey="code"
          loading={loading}
          actionsWrapperStyle={{ justifyContent: 'space-between' }}
          dataSource={tableData}
          columns={getColumns({ deletePoint, setDetail, setPattern })}
          scroll={{ x: 'max-content' }}
          actions={[
            <Space key="left-content">
              <Button
                type="primary"
                onClick={() => {
                  setPattern('create');
                }}
              >
                新建
              </Button>
              {checkCode('element_mergerd-processed-points-export-btn') && (
                <Button loading={exportLoading} onClick={onExportPointChange}>
                  导出
                </Button>
              )}
            </Space>,
            <Input.Search
              key="search"
              placeholder="搜索测点名称或编码"
              style={{ width: 250 }}
              onSearch={searchPoint}
            />,
          ]}
        />
      )}
    </>
  );
}
const mapStateToProps = ({ doPointManage: { selectCategoryPoint } }) => ({
  selectCategoryPoint,
});
const mapDispatchToProps = {
  deletePointInTable: deletePointAction,
};
export default connect(mapStateToProps, mapDispatchToProps)(MergedTable);

const getColumns = ({ deletePoint, setDetail, setPattern }) => [
  {
    title: '测点ID',
    dataIndex: 'code',
    render: code => (
      <Ellipsis lines={1} tooltip>
        {code}
      </Ellipsis>
    ),
  },
  {
    title: '测点名称',
    dataIndex: 'name',
    render: (name, record) => (
      <Button
        type="link"
        compact
        onClick={() => {
          setDetail(record);
          setPattern('detail');
        }}
      >
        {name}
      </Button>
    ),
  },
  {
    title: '测点类型',
    dataIndex: '',
    render: (text, record) => {
      if (record.dataType) {
        return (
          <Ellipsis lines={1} tooltip>
            {record.dataType.name}
          </Ellipsis>
        );
      } else {
        return null;
      }
    },
  },
  {
    title: '单位',
    dataIndex: 'unit',
  },
  {
    title: '精度',
    dataIndex: 'precision',
  },
  {
    title: '操作',
    key: '__actions',
    fixed: 'right',
    render: (__, record) => {
      if (record.isInitialized) {
        return null;
      }
      return (
        <span>
          <DeleteConfirm
            targetName={record.name}
            onOk={({ reason }) => deletePoint(record, reason)}
          >
            <Button type="link" compact>
              删除
            </Button>
          </DeleteConfirm>
        </span>
      );
    },
  },
];
