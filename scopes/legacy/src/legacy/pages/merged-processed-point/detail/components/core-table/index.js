import React from 'react';
import { connect } from 'react-redux';

import { DragSortTable } from '@galiojs/pro-table';

import { Button } from '@manyun/base-ui.ui.button';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';

import { usePoints } from '@manyun/resource-hub.hook.use-points';
import { updateCorePoints } from '@manyun/resource-hub.service.update-core-points';
import { CorePointsCreator } from '@manyun/resource-hub.ui.core-points-creator';

function CoreTable({ selectCategoryPoint, setDetail, setPattern }) {
  const [{ loading, data }, getPoints] = usePoints({ lazy: true });
  const { deviceType } = selectCategoryPoint;

  const handleDragSortEnd = newDataSource => {
    mutateCorePoints(newDataSource, true);
  };

  const fetchCorePoints = React.useCallback(() => {
    getPoints({ deviceType, isOnlyCore: true, isRemoveSub: true });
  }, [deviceType, getPoints]);

  const mutateCorePoints = async (points, isDrag) => {
    const { error } = await updateCorePoints({ pointList: points, variant: 'delete' });
    if (error) {
      message.error(error);
      return;
    }
    message.success(isDrag ? '修改列表排序成功' : '删除测点成功');
    fetchCorePoints();
  };

  React.useEffect(() => {
    fetchCorePoints();
  }, [fetchCorePoints]);

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <CorePointsCreator
        deviceType={selectCategoryPoint.deviceType}
        cores={data}
        callback={fetchCorePoints}
      />
      <DragSortTable
        search={false}
        toolBarRender={false}
        loading={loading}
        columns={getColumns({ setDetail, setPattern, mutateCorePoints, data })}
        rowKey="code"
        pagination={false}
        dataSource={data}
        dragSortKey="sort"
        onDragSortEnd={handleDragSortEnd}
        scroll={{ x: 'max-content' }}
      />
    </Space>
  );
}

const mapStateToProps = ({ doPointManage: { selectCategoryPoint } }) => ({
  selectCategoryPoint,
});

export default connect(mapStateToProps)(CoreTable);

const getColumns = ({ setDetail, setPattern, mutateCorePoints, data }) => [
  { title: '', dataIndex: 'sort' },
  {
    title: '测点ID',
    dataIndex: 'code',
  },
  {
    title: '测点名称',
    dataIndex: 'name',
    render: (name, record) => (
      <Button
        type="link"
        compact
        onClick={() => {
          setDetail(record.toApiObject());
          setPattern('detail');
        }}
      >
        {name}
      </Button>
    ),
  },
  {
    title: '测点类型',
    dataIndex: 'dataType',
    render: dataType => dataType?.name,
  },
  {
    title: '单位',
    dataIndex: 'unit',
  },
  {
    title: '精度',
    dataIndex: 'precision',
  },
  {
    title: '操作',
    key: '__actions',
    fixed: 'right',
    render: (__, record) => {
      return (
        <DeleteConfirm
          variant="popconfirm"
          targetName={record.name}
          onOk={() => {
            const idx = data.findIndex(item => item.code === record.code);
            const item = data[idx];
            if (idx !== -1) {
              const arr = [...data];
              arr.splice(idx, 1);
              mutateCorePoints([...arr, { ...item, priority: null }]);
            }
          }}
        >
          <Button type="link" compact>
            删除
          </Button>
        </DeleteConfirm>
      );
    },
  },
];
