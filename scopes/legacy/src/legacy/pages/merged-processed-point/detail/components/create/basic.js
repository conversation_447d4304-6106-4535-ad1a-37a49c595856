import React, { useCallback, useEffect, useState } from 'react';
import { connect } from 'react-redux';

import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Radio } from '@manyun/base-ui.ui.radio';

import { fetchBizFileInfos } from '@manyun/dc-brain.service.fetch-biz-file-infos';
import { TypeAttributionRadio } from '@manyun/resource-hub.ui.type-attribution';
import { UnitMetaDataSelect } from '@manyun/resource-hub.ui.unit-meta-data-select';

import { deviceTypeService } from '@manyun/dc-brain.legacy.services';

import UploadDeviceImage from './components/upload-device-image';

const getFileInfo = (file, targetId) => ({
  fileFormat: file.ext,
  fileName: file.name,
  filePath: file.patialPath,
  fileSize: file.size,
  fileTime: file.uploadedAt,
  fileType: file.type,
  gmtCreate: file.uploadedAt?.toString(),
  gmtModified: file.lastModified?.toString(),
  targetId,
  targetType: 'DEVICE_TYPE',
  uploadTime: file.uploadedAt?.toString(),
});

const EditBasic = ({ pattern, wrappedComponentRef, selectCategoryPoint }) => {
  const [category, setCategory] = useState();
  const [fileInfoList, setFileInfoList] = useState([]);
  const [focus, setFocus] = useState(false);

  const getDeviceDetail = useCallback(async () => {
    if (!selectCategoryPoint.deviceType) {
      return;
    }
    const { response, error } = await deviceTypeService.getDeviceTypeDetail({
      code: selectCategoryPoint.deviceType,
    });

    if (error) {
      message.error(error);
      return;
    }

    const { data } = await fetchBizFileInfos({
      targetId: selectCategoryPoint.deviceType,
      targetType: 'DEVICE_TYPE',
    });

    const fileList = data.data.map(data => ({ ...data, src: data.src }));

    setFileInfoList(fileList);

    if (response) {
      const { name, code, numbered, unitCode, type } = response;
      const formData = {
        ...wrappedComponentRef.current.getFieldsValue(),
        name,
        code,
        numbered,
        unitCode,
        type,
      };
      wrappedComponentRef.current.setFieldsValue(formData);
    }
  }, [selectCategoryPoint.deviceType, wrappedComponentRef]);

  const handleUploadChange = useCallback(
    fileList => {
      if (!fileList.length) {
        getFileInfo({}, selectCategoryPoint.deviceType);
      }
      const list = (fileList || []).map(file =>
        getFileInfo(file.originFileObj, selectCategoryPoint.deviceType)
      );
      wrappedComponentRef.current.setFields([
        {
          name: 'fileInfoList',
          value: list,
        },
      ]);
    },
    [selectCategoryPoint.deviceType, wrappedComponentRef]
  );

  useEffect(() => {
    if (
      !category ||
      `${category?.selectType}_${category?.deviceType}` !==
        `${selectCategoryPoint.selectType}_${selectCategoryPoint.deviceType}`
    ) {
      setCategory(selectCategoryPoint);
      getDeviceDetail();
    }
  }, [category, getDeviceDetail, selectCategoryPoint]);

  return (
    <Form
      ref={ref => (wrappedComponentRef.current = ref)}
      colon={false}
      labelCol={{ span: 3 }}
      wrapperCol={{ span: 21 }}
    >
      <Form.Item
        label="类型名称"
        name="name"
        extra={
          focus && '温馨提示：修改类型名称，将会影响到设备和工单内的设备类型信息，请谨慎操作！'
        }
        rules={[
          { required: true, whitespace: true, message: '类型名称为必填项' },
          {
            max: 16,
            message: '最多输入 16 个字符！',
          },
        ]}
      >
        <Input style={{ width: 200 }} onFocus={() => setFocus(true)} />
      </Form.Item>
      <Form.Item
        label="类型编号"
        name="code"
        rules={[{ required: true, whitespace: true, message: '类型编号为必填项' }]}
      >
        <Input style={{ width: 200 }} disabled={pattern === 'edit' ? true : false} />
      </Form.Item>
      <Form.Item label="类型归属" name="type">
        <TypeAttributionRadio disabled />
      </Form.Item>

      <Form.Item label="是否有编号" name="numbered">
        <Radio.Group disabled>
          <Radio value>是</Radio>
          <Radio value={false}>否</Radio>
        </Radio.Group>
      </Form.Item>

      {selectCategoryPoint?.selectType === 'C2' && (
        <>
          <Form.Item label="类型单位" name="unitCode">
            <UnitMetaDataSelect style={{ width: 200 }} />
          </Form.Item>
          <Form.Item label="拟物图" name="fileInfoList">
            <UploadDeviceImage
              handleUploadChange={handleUploadChange}
              fileInfoList={fileInfoList}
            />
          </Form.Item>
        </>
      )}
    </Form>
  );
};

const mapStateToProps = ({ doPointManage: { selectCategoryPoint } }) => {
  return {
    selectCategoryPoint,
  };
};
export default connect(mapStateToProps)(EditBasic);
