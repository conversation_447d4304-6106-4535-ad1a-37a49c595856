import React from 'react';
import { connect } from 'react-redux';

import { Select } from '@galiojs/awesome-antd';

import { METADATA_TYPE } from '@manyun/dc-brain.legacy.constants';
import { getMetadataByType } from '@manyun/dc-brain.legacy.redux/actions/eventCenterActions';

export class UnitSelect extends React.Component {
  getSpecUnit = () => {
    if (this.props.specUnitList.length) {
      return;
    }
    this.props.getMetadataByType(METADATA_TYPE['SPEC_UNIT']);
  };

  render() {
    const { specUnitList, forwardedRef } = this.props;
    return (
      <Select showSearch={true} onFocus={this.getSpecUnit} {...this.props} ref={forwardedRef}>
        {specUnitList.map(item => (
          <Select.Option key={item.metaCode}>{item.metaName}</Select.Option>
        ))}
      </Select>
    );
  }
}

const mapStateToProps = ({ eventCenter: { specUnitList } }) => {
  return {
    specUnitList,
  };
};
const mapDispatchToProps = {
  getMetadataByType: getMetadataByType,
};

const ConnectedUnitSelect = connect(mapStateToProps, mapDispatchToProps)(UnitSelect);

export default React.forwardRef((props, ref) => (
  <ConnectedUnitSelect forwardedRef={ref} {...props} />
));
