import React, { useEffect, useState } from 'react';

import { PlusOutlined } from '@ant-design/icons';

import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import { MixedUploadFile, McUpload as Upload } from '@manyun/dc-brain.ui.upload';

import styles from './index.module.less';

interface UploadDeviceImageProps {
  handleUploadChange: (fileList: MixedUploadFile[]) => void;
  fileInfoList: MixedUploadFile[];
}

const UploadDeviceImage = (
  { handleUploadChange, fileInfoList }: UploadDeviceImageProps,
  ref?: React.Ref<{}>
) => {
  const [fileList, setFileList] = useState<MixedUploadFile[]>([]);

  useEffect(() => {
    setFileList(fileInfoList);
  }, [fileInfoList]);

  return (
    <div className={styles.uploadDeviceImg}>
      <Upload
        accept=".png,.jpg,.jpeg"
        maxCount={1}
        maxFileSize={2}
        allowDelete
        fileList={fileList}
        onChange={info => {
          setFileList(info?.fileList || []);
          handleUploadChange(info?.fileList);
        }}
      >
        {fileList.length >= 1 ? null : (
          <Space direction="vertical" size="middle">
            <div className="upload-device-content">
              <PlusOutlined />
              <div style={{ marginTop: 8 }}>上传图片</div>
            </div>
            <Typography.Text type="secondary">
              图片格式支持： png、jpg、jpeg；大小上限： 2M
            </Typography.Text>
          </Space>
        )}
      </Upload>
    </div>
  );
};

export default UploadDeviceImage;
