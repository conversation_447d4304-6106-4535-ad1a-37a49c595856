import React, { useEffect } from 'react';
import { connect } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { Button as BaseButton } from '@manyun/base-ui.ui.button';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Divider } from '@manyun/base-ui.ui.divider';

// Deprecated, replace with "useAuthorized" hook
import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';

import { Ellipsis, TinyTable, UserLink } from '@manyun/dc-brain.legacy.components';
import { VALUE_TYPE_TEXT_MAP } from '@manyun/dc-brain.legacy.pages/merged-processed-point/constants';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  MetaCategoryNumAction,
  deleteSpecAction,
  fetchSpecDetail,
  specActions,
} from '@manyun/dc-brain.legacy.redux/actions/specActions';

const getColumns = ctx => [
  {
    title: '规格名称',
    dataIndex: 'specName',
  },
  {
    title: '类型',
    dataIndex: 'valueType',
    render: txt => VALUE_TYPE_TEXT_MAP[txt],
  },
  {
    title: '单位',
    dataIndex: 'specUnit',
  },
  {
    title: '是否必填',
    dataIndex: 'required',
    render: required => (required ? '是' : '否'),
  },
  {
    title: '更新时间',
    dataIndex: 'gmtModified',
  },
  {
    title: '更新人',
    dataIndex: 'operatorName',
    render: (operatorName, record) => (
      <UserLink userId={record.operatorId} userName={operatorName} />
    ),
  },
  {
    title: '备注',
    dataIndex: 'description',
    render: description => {
      if (!description) {
        return '--';
      }
      return (
        <Ellipsis lines={1} tooltip>
          {description}
        </Ellipsis>
      );
    },
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    render: (__, record) => (
      <>
        {ctx.updateAuthorized && (
          <Button
            key="edit"
            type="link"
            style={{ height: 'auto', padding: '0' }}
            onClick={() => {
              ctx.setPattern('edit');
              ctx.setDetail(record);
            }}
          >
            编辑
          </Button>
        )}
        {ctx.deleteAuthorized && (
          <>
            <Divider key="divider" type="vertical" />
            <DeleteConfirm
              key="confirm"
              targetName={record.specName}
              onOk={({ reason }) => ctx.deleteSpec(record, reason)}
            >
              <BaseButton key="delete" type="link" compact>
                删除
              </BaseButton>
            </DeleteConfirm>
          </>
        )}
      </>
    ),
  },
];

function SpecsTable({
  selectCategoryPoint,
  getSpecsTableData,
  specDetail,
  setPattern,
  cardMode,
  setDetail,
  loading,
  deleteSpecData,
}) {
  const [updateAuthorized] = useAuthorized({ checkByCode: 'ele_spec_update' });
  const [deleteAuthorized] = useAuthorized({ checkByCode: 'ele_spec_delete' });
  const [addAuthorized] = useAuthorized({ checkByCode: 'ele_spec_add' });

  useEffect(() => {
    if (selectCategoryPoint.selectType === 'C2' && cardMode === 'specs') {
      getSpecsTableData({ deviceType: selectCategoryPoint.deviceType });
    }
  }, [selectCategoryPoint, cardMode, getSpecsTableData]);

  const deleteSpec = ({ id }, operatorNotes) => {
    const { deviceType } = selectCategoryPoint;

    return new Promise(resolve => {
      deleteSpecData({
        params: { id, operatorNotes },
        deviceType,
        successCb: () => {
          resolve(true);
        },
        errorCb: () => {
          resolve(false);
        },
      });
    });
  };

  if (selectCategoryPoint.selectType !== 'C2') {
    return null;
  }

  return (
    <TinyTable
      rowKey="id"
      loading={loading}
      dataSource={specDetail.data}
      columns={getColumns({
        deleteSpec,
        setPattern,
        setDetail,
        updateAuthorized,
        deleteAuthorized,
      })}
      scroll={{ x: 'max-content' }}
      actions={
        addAuthorized
          ? [
              <Button
                key="add"
                type="primary"
                onClick={() => {
                  setPattern('create');
                }}
              >
                新建
              </Button>,
            ]
          : []
      }
    />
  );
}

const mapStateToProps = ({
  doPointManage: { selectCategoryPoint },
  specManage: { loading, specDetail },
  common: { deviceCategory },
}) => ({
  loading,
  specDetail,
  selectCategoryPoint,
  normalizedList: deviceCategory?.normalizedList,
});

const mapDispatchToProps = {
  deleteSpecData: deleteSpecAction,
  ViewVisible: specActions.ViewVisible,
  getSpecsTableData: fetchSpecDetail,
  fetchSpecDetailSuccess: specActions.fetchSpecDetailSuccess,
  syncCommonData: syncCommonDataActionCreator,
  MetaCategoryNumAction,
};

export default connect(mapStateToProps, mapDispatchToProps)(SpecsTable);
