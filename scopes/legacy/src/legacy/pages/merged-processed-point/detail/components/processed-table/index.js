import dayjs from 'dayjs';
import saveAs from 'file-saver';
import React, { useEffect, useMemo, useState } from 'react';
import { connect, useDispatch } from 'react-redux';
import { useHistory, useParams } from 'react-router-dom';

import { useAuthorized } from '@manyun/auth-hub.hook.gql.use-authorized';
import { Button } from '@manyun/base-ui.ui.button';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { configSliceActions as configActions } from '@manyun/dc-brain.state.config';
import { usePointsData } from '@manyun/monitoring.hook.use-points-data';
import { PointChartText } from '@manyun/monitoring.ui.point-data-renderer';
import { CUSTOM_POINT_IMPORT_ROUTE_PATH } from '@manyun/resource-hub.route.resource-routes';
import { exportCustomPoint } from '@manyun/resource-hub.service.export-custom-point';
import { exportPoint } from '@manyun/resource-hub.service.export-point';
import { fetchPointInstanceList } from '@manyun/resource-hub.service.fetch-point-instance-list';
import { fetchPointsByCondition } from '@manyun/resource-hub.service.fetch-points-by-condition';

import { GutterWrapper, TinyTable } from '@manyun/dc-brain.legacy.components';
import { POINT_TYPE_CODE_MAP } from '@manyun/dc-brain.legacy.constants/point';
import { deletePointAction } from '@manyun/dc-brain.legacy.redux/actions/doPointActions';
import { mergedProcessesdPointActions } from '@manyun/dc-brain.legacy.redux/actions/mergedProcessesdPointActions';

const moduleId = 'processed-table-points';

function ProcessedTable({
  setPattern,
  selectedTab,
  selectedPane,
  selectCategoryPoint,
  treeMode,
  deletePointInTable,
  updateFormValues,
  setDetail,
  refresCustomDeviceTreeData,
}) {
  const [, { checkCode }] = useAuthorized();
  const history = useHistory();
  const { idc } = useParams();

  const dispatch = useDispatch();
  const [alltableData, setAllTableData] = useState([]);
  const [tableData, setTableData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [checkboxValue, setCheckboxValue] = useState([]);
  const [searchValue, setSearchValue] = useState('');
  const [count, setCount] = useState(1);
  const [exportLoading, setExportLoading] = useState(false);

  const blockGuid = useMemo(() => {
    let _blockGuid = selectCategoryPoint?.parentCode;

    if (selectCategoryPoint?.selectType === 'IDC' || selectCategoryPoint?.selectType === 'BLOCK') {
      _blockGuid = selectCategoryPoint.deviceType;
    }
    return _blockGuid;
  }, [
    selectCategoryPoint.deviceType,
    selectCategoryPoint?.parentCode,
    selectCategoryPoint?.selectType,
  ]);

  const [pointDatas] = usePointsData({
    blockGuid: treeMode === 'custom' ? blockGuid : undefined, //只自定义下获取实时值
    moduleId: moduleId,
    targets:
      tableData?.length && treeMode === 'custom'
        ? [
            {
              guid: selectCategoryPoint.deviceType,
              type: tableData[0].deviceType,
              hardCodedPoints: tableData.map(item => {
                return {
                  pointCode: item.pointCode,
                  dataType: item.dataType.code,
                };
              }),
            },
          ]
        : [],
  });

  useEffect(() => {
    if (selectCategoryPoint.deviceType && selectCategoryPoint.selectType === 'C2') {
      if (treeMode === 'device' && selectCategoryPoint.type === 'device') {
        // 设备类型下
        (async function getProcessedPointsInDevice() {
          setLoading(true);
          const { data, error } = await fetchPointsByCondition({
            deviceType: selectCategoryPoint.deviceType,
            dataTypeList: ['AI', 'DI'],
            pointTypeList: [POINT_TYPE_CODE_MAP.CAL_DEVICE],
            isRemoveSub: true,
          });
          if (error) {
            message.error(error);
            setLoading(false);
          }
          if (data) {
            setLoading(false);
            splitDataInType(data.data.map(point => point.toApiObject()));
          }
        })();
      }
      if (treeMode === 'space' && selectCategoryPoint.type === 'space') {
        // 空间类型下
        (async function getProcessedPointsInSpace() {
          setLoading(true);
          const { data, error } = await fetchPointsByCondition({
            deviceType: selectCategoryPoint.deviceType,
            dataTypeList: ['AI', 'DI'],
            pointTypeList: [POINT_TYPE_CODE_MAP.CAL_SPACE],
            isRemoveSub: true,
          });
          if (error) {
            message.error(error);
            setLoading(false);
          }
          if (data) {
            setLoading(false);
            splitDataInType(data.data.map(point => point.toApiObject()));
          }
        })();
      }
    }
    if (treeMode === 'custom') {
      if (!selectCategoryPoint.deviceType) {
        return;
      }
      // 自定义类型下
      (async function getProcessedPointsInCustom() {
        setLoading(true);

        const { data, error } = await fetchPointInstanceList({
          targetGuid: selectCategoryPoint.deviceType,
        });
        setLoading(false);
        if (!error) {
          splitDataInType(
            data.data.map(item => {
              return { ...item, code: item.pointCode };
            })
          );

          const pointsMap = data.data.reduce((result, item) => {
            if (item.dataType.code === 'AI' || item.dataType.code === 'AO') {
              return result;
            } else {
              result[item.pointCode] = {
                name: item.name,
                dataType: item.dataType.code,
                unit: item.unit,
                validLimits: item.validLimits,
              };
              return result;
            }
          }, {});

          const pointsDefinitionMap = {};
          if (data.data.length && data.data[0].deviceType) {
            pointsDefinitionMap[data.data[0].deviceType] = pointsMap;
          }
          dispatch(configActions.updatePointsDefinitionMap(pointsDefinitionMap));
        } else {
          message.error(error.message);
          setLoading(false);
        }
      })();
    }
  }, [selectCategoryPoint, selectedTab, selectedPane, treeMode, count, dispatch]);

  useEffect(() => {
    setCheckboxValue([]);
  }, [selectCategoryPoint.deviceType]);

  const splitDataInType = data => {
    const AIData = {
      label: '模拟量读点',
      value: 'AI',
      data: [],
      num: 0,
    };
    const DIData = {
      label: '状态量读点',
      value: 'DI',
      data: [],
      num: 0,
    };
    data.forEach(item => {
      if (item.dataType.code === 'AI') {
        AIData.data = [...AIData.data, item];
        AIData.num = AIData.num + 1;
      } else if (item.dataType.code === 'DI') {
        DIData.data = [...DIData.data, item];
        DIData.num = DIData.num + 1;
      }
    });
    const total = [AIData, DIData].map(item => {
      return {
        ...item,
        label: `${item.label}(${item.num})`,
      };
    });
    setTableData(data);
    setAllTableData(total);
  };

  const deletePoint = ({ code, id, deviceType }, operatorNotes) => {
    return new Promise(resolve => {
      let _deviceType = selectCategoryPoint.deviceType;
      if (selectCategoryPoint.type === 'custom') {
        if (!id) {
          return;
        }
        _deviceType = deviceType;
      }

      deletePointInTable({
        params: {
          code,
          operatorNotes,
          deviceType: _deviceType,
          type: selectCategoryPoint.type,
          id,
        },
        successCb: () => {
          resolve(true);
          //story#2813重新请求树数据量大的时候会卡，暂时注释掉
          //refresCustomDeviceTreeData();
          reset();
          setCount(count => count + 1);
        },
        errorCb: () => {
          resolve(false);
        },
      });
    });
  };

  const edit = record => {
    setPattern('edit');
    setDetail(record);
  };

  const onChangeCheckbox = checkedValues => {
    setCheckboxValue(checkedValues);
    if (!checkedValues.length && !searchValue) {
      setTableData(getFlatData(alltableData));
    } else if (!checkedValues.length) {
      const newData = alltableData.map(item => {
        const data = item.data.filter(point => {
          const idx =
            point.name.indexOf(searchValue) !== -1
              ? point.name.indexOf(searchValue)
              : point.code.indexOf(searchValue);
          if (idx > -1) {
            return true;
          }
          return false;
        });
        return {
          ...item,
          data,
        };
      });
      setTableData(getFlatData(newData));
    } else {
      const newData = alltableData
        .map(item => {
          if (!checkedValues.includes(item.value)) {
            return null;
          }
          const data = item.data.filter(point => {
            const idx = point.name.indexOf(searchValue);
            if (idx > -1) {
              return true;
            }
            return false;
          });
          return {
            ...item,
            data,
          };
        })
        .filter(Boolean);
      setTableData(getFlatData(newData));
    }
  };

  const searchPoint = () => {
    onChangeCheckbox(checkboxValue);
  };

  const reset = () => {
    setCheckboxValue([]);
    if (searchValue) {
      const newData = alltableData
        .map(item => {
          const data = item.data.filter(point => {
            const idx = point.name.indexOf(searchValue);
            if (idx > -1) {
              return true;
            }
            return false;
          });
          return {
            ...item,
            data,
          };
        })
        .filter(Boolean);
      setTableData(getFlatData(newData));
    } else {
      setTableData(getFlatData(alltableData));
    }
  };

  if (selectCategoryPoint.selectType !== 'C2' && treeMode !== 'custom') {
    return null;
  }

  const onHandleExportPoint = async () => {
    setExportLoading(true);
    if (treeMode === 'custom') {
      const { error, data } = await exportCustomPoint({
        targetGuid: selectCategoryPoint.deviceType,
        idcTag: idc,
      });

      setExportLoading(false);

      if (error) {
        message.error(error.message);
        return;
      }
      saveAs(data, '自定义测点.xlsx');
      return;
    }

    const { error, data } = await exportPoint({
      pointTypeList: treeMode === 'space' ? ['CAL_SPACE'] : ['CAL_DEVICE'],
      deviceType: selectCategoryPoint.deviceType,
    });

    setExportLoading(false);

    if (error) {
      message.error(error.message);
      return;
    }
    saveAs(data, '加工测点.xlsx');
  };

  return (
    <TinyTable
      rowKey="code"
      loading={loading}
      actionsWrapperStyle={{ justifyContent: 'space-between' }}
      dataSource={tableData}
      columns={getColumns({
        deletePoint,
        edit,
        setDetail,
        setPattern,
        treeMode,
        selectCategoryPoint,
        pointDatas,
        refresCustomDeviceTreeData,
      })}
      scroll={{ x: 'max-content' }}
      actions={[
        <GutterWrapper key="create" mode="horizontal">
          <Button
            type="primary"
            onClick={() => {
              setPattern('create');
              // 设置加工类型
              if (treeMode === 'device') {
                updateFormValues({
                  pointType: {
                    name: 'pointType',
                    value: POINT_TYPE_CODE_MAP.CAL_DEVICE,
                  },
                });
              }
              if (treeMode === 'space') {
                updateFormValues({
                  pointType: {
                    name: 'pointType',
                    value: POINT_TYPE_CODE_MAP.CAL_SPACE,
                  },
                });
              }
              if (treeMode === 'custom') {
                updateFormValues({
                  pointType: {
                    name: 'pointType',
                    value: POINT_TYPE_CODE_MAP.CUSTOM,
                  },
                });
              }
            }}
          >
            新建
          </Button>
          {treeMode === 'custom' && (
            <Button onClick={() => history.push(CUSTOM_POINT_IMPORT_ROUTE_PATH)}>导入</Button>
          )}
          {checkCode('element_mergerd-processed-points-export-btn') && (
            <Button loading={exportLoading} onClick={onHandleExportPoint}>
              导出
            </Button>
          )}
          <Checkbox.Group
            options={alltableData}
            value={checkboxValue}
            onChange={onChangeCheckbox}
          />
          <Button size="small" onClick={reset}>
            重置
          </Button>
        </GutterWrapper>,

        <Input.Search
          key="search"
          placeholder="搜索测点名称或编码"
          value={searchValue}
          style={{ width: 250 }}
          onSearch={searchPoint}
          onChange={({ target: { value } }) => setSearchValue(value)}
        />,
      ]}
    />
  );
}
const mapStateToProps = ({ doPointManage: { selectCategoryPoint } }) => ({
  selectCategoryPoint,
});
const mapDispatchToProps = {
  deletePointInTable: deletePointAction,
  updateFormValues: mergedProcessesdPointActions.updateProcessedPointFormValues,
};

export default connect(mapStateToProps, mapDispatchToProps)(ProcessedTable);

const getColumns = ({
  deletePoint,
  edit,
  setDetail,
  setPattern,
  treeMode,
  selectCategoryPoint,
  pointDatas,
}) => {
  const columns = [
    {
      title: '测点ID',
      dataIndex: 'code',
    },
    {
      title: '测点名称',
      dataIndex: 'name',
      render: (name, record) => (
        <Button
          type="link"
          compact
          onClick={() => {
            setDetail(record);
            setPattern('detail');
          }}
        >
          {name}
        </Button>
      ),
    },
    {
      title: '测点类型',
      dataIndex: 'dataType',
      render: dataType => {
        if (dataType) {
          return dataType.name;
        }
        return null;
      },
    },
    {
      title: '单位',
      dataIndex: 'unit',
    },
    {
      title: '精度',
      dataIndex: 'precision',
    },
    {
      title: '操作',
      key: '__actions',
      fixed: 'right',
      render: (__, record) => {
        return (
          <span>
            {/* 现在所有表达式，都可编辑 */}
            <Button compact type="link" onClick={() => edit(record)}>
              编辑
            </Button>
            {!record.isInitialized && (
              <>
                <Divider type="vertical" />
                <DeleteConfirm
                  targetName={record.name}
                  onOk={({ reason }) => deletePoint(record, reason)}
                >
                  <Button type="link" compact>
                    删除
                  </Button>
                </DeleteConfirm>
              </>
            )}
          </span>
        );
      },
    },
  ];

  if (treeMode === 'custom') {
    columns.splice(columns.length - 1, 0, {
      title: '实时值',
      dataIndex: 'pointValue',
      render: (_, record) => {
        const pointData = pointDatas[selectCategoryPoint.deviceType]?.get(record.pointCode);

        return (
          <PointChartText
            pointData={pointData}
            spaceGuid={
              selectCategoryPoint?.selectType === 'IDC' ||
              selectCategoryPoint?.selectType === 'BLOCK'
                ? selectCategoryPoint.deviceType
                : selectCategoryPoint.parentCode
            }
            dataType={record.dataType.code}
            linkTextColorType="primary"
            showUnit
          />
        );
      },
    });
    columns.splice(columns.length - 1, 0, {
      title: '更新时间',
      dataIndex: 'gmtModified',
      render: gmtModified => {
        return gmtModified ? dayjs(gmtModified).format('YYYY-MM-DD HH:mm:ss') : '--';
      },
    });
  }
  return columns;
};

function getFlatData(list) {
  let newData = [];
  list.forEach(item => {
    newData = [...newData, ...item.data];
  });
  return newData;
}
