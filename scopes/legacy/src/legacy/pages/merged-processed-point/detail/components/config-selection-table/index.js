import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';

import cloneDeep from 'lodash.clonedeep';
import shortid from 'shortid';

import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';
import { mergedProcessesdPointService } from '@manyun/dc-brain.legacy.services';

import ConfigSelectionTable from './components/config-selection-table';

// import CreateModalButton from './components/create-modal-button';

function ConfigSelection({ selectCategoryPoint, cardMode }) {
  const [tableData, setTableData] = useState([]);
  const [allTableData, setAllTableData] = useState([]);
  const [editingRowKey, setEditingRowKey] = useState(null);
  const [deleteByCancel, setDeleteByCancel] = useState(false);
  const [refresh, setRefresh] = useState(false);
  const [loading, setLoading] = useState(false);

  React.useEffect(() => {
    return () => {
      // fix http://chandao.manyun-local.com/zentao/bug-view-7582.html
      setEditingRowKey(null);
    };
  }, [selectCategoryPoint.deviceType]);

  useEffect(() => {
    if (selectCategoryPoint.selectType === 'C2' && cardMode === 'configSelection') {
      getConfigSelectionData({ deviceType: selectCategoryPoint.deviceType });
    }
  }, [selectCategoryPoint, cardMode]);

  useEffect(() => {
    if (refresh) {
      getConfigSelectionData({ deviceType: selectCategoryPoint.deviceType });
    }
  }, [selectCategoryPoint, cardMode, refresh]);

  async function getConfigSelectionData(params) {
    setLoading(true);
    const { response, error } = await mergedProcessesdPointService.fetchRelate(params);
    if (error) {
      message.error(error);
      setLoading(false);
    }
    if (response) {
      const newData = response.data.map(item => {
        return {
          ...item,
          id: shortid(),
          spareType: {
            code: item.spareType,
            name: item.spareName,
          },
        };
      });
      const mergedData = getMergeRowsKeyInTable(newData);
      setTableData(mergedData);
      setAllTableData(mergedData);
      setRefresh(false);
      setLoading(false);
    }
  }

  const onSearch = value => {
    setLoading(true);
    if (value.trim() === '') {
      setTableData(allTableData);
      setLoading(false);
      return;
    }
    const newData = tableData.filter(item => {
      const typeIdx = item.spareType.name.indexOf(value);
      let modelIdex = -1;
      item.spareModelList.forEach(model => {
        if (model.indexOf(value) > -1) {
          modelIdex = model.indexOf(value);
          return;
        }
      });
      if (typeIdx > -1 || modelIdex > -1) {
        return true;
      } else {
        return false;
      }
    });
    setTableData(newData);
    setLoading(false);
  };

  const onSave = async (currentKey, records) => {
    if (!deleteByCancel) {
      // 编辑
      const current = records.filter(({ id }) => id === currentKey);
      const params = {
        deviceType: selectCategoryPoint.deviceType,
        deviceModel: current[0].deviceModel,
        spareType: current[0].spareType?.code,
        relateModels: current[0].spareModelList?.length ? current[0].spareModelList : null,
      };
      const { response, error } = await mergedProcessesdPointService.fetchUpdateRelate(params);
      if (error) {
        message.error(error);
      }
      if (response) {
        message.success('编辑成功！');
        setRefresh(true);
        setEditingRowKey(null);
        setDeleteByCancel(false);
      }
    } else {
      // 新建
      const add = records.filter(({ id }) => id === currentKey);

      const relateModels = add[0].spareModelList.length
        ? add[0].spareModelList.map(item => {
            return {
              deviceType: selectCategoryPoint.deviceType,
              deviceModel: add[0].deviceModel,
              spareType: add[0].spareType?.code,
              spareModel: item,
            };
          })
        : [
            {
              deviceType: selectCategoryPoint.deviceType,
              deviceModel: add[0].deviceModel,
              spareType: add[0].spareType?.code,
              spareModel: null,
            },
          ];
      const { response, error } = await mergedProcessesdPointService.fetchConnectRelate({
        relateModels,
      });
      if (error) {
        message.error(error);
      }
      if (response) {
        message.success('新建成功！');
        setRefresh(true);
        setEditingRowKey(null);
        setDeleteByCancel(false);
      }
    }
  };

  const onDelete = async rowKey => {
    const data = tableData.filter(({ id }) => id === rowKey);
    const params = {
      deviceType: data[0].deviceType,
      deviceModel: data[0].deviceModel,
      spareType: data[0].spareType.code,
    };
    const { response, error } = await mergedProcessesdPointService.fetchDeleteRelate(params);
    if (error) {
      message.error(error);
    }
    if (response) {
      setRefresh(true);
    }
  };

  const onCancel = () => {
    if (deleteByCancel) {
      const nextData = cloneDeep(tableData);
      const idx = nextData.findIndex(record => record.id === editingRowKey);
      if (idx > -1) {
        nextData.splice(idx, 1);
      }
      setTableData(nextData);
      setDeleteByCancel(false);
      setEditingRowKey(null);
    } else {
      setEditingRowKey(null);
    }
  };

  const onEdit = rowKey => {
    setEditingRowKey(rowKey);
  };

  return (
    <GutterWrapper mode="vertical">
      <GutterWrapper
        style={{ justifyContent: tableData.length === 0 ? 'space-between' : 'flex-end' }}
        flex
      >
        {tableData.length === 0 && (
          <Button
            type="primary"
            onClick={() => {
              const editingRowKey = shortid();
              setDeleteByCancel(true);
              setEditingRowKey(editingRowKey);
              setTableData([
                {
                  id: editingRowKey,
                  mergeRowsKey: editingRowKey,
                  deviceModel: undefined,
                  spareType: undefined,
                  spareModelList: [],
                },
              ]);
            }}
          >
            新建
          </Button>
        )}

        <Input.Search
          allowClear
          placeholder="输入配置类型或型号后回车搜索"
          style={{ width: '250px' }}
          onSearch={onSearch}
        />
      </GutterWrapper>
      <ConfigSelectionTable
        tableData={tableData}
        showActionsColumn
        deviceType={selectCategoryPoint.deviceType}
        editingRowKey={editingRowKey}
        deleteByCancel={deleteByCancel}
        setEditingRowKey={setEditingRowKey}
        setDeleteByCancel={setDeleteByCancel}
        setTableData={setTableData}
        partten="list"
        setRefresh={setRefresh}
        loading={loading}
        onSave={onSave}
        onDelete={onDelete}
        onCancel={onCancel}
        onEdit={onEdit}
      />
    </GutterWrapper>
  );
}

const mapStateToProps = ({ doPointManage: { selectCategoryPoint } }) => ({
  selectCategoryPoint,
});
export default connect(mapStateToProps, null)(ConfigSelection);

function getMergeRowsKeyInTable(data) {
  let ids = {};
  return data.map(item => {
    if (ids[item.deviceModel]) {
      return {
        ...item,
        mergeRowsKey: ids[item.deviceModel],
      };
    }
    ids[item.deviceModel] = item.id;
    return {
      ...item,
      mergeRowsKey: item.id,
    };
  });
}
