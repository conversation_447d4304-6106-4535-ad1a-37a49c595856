import React from 'react';
import { connect } from 'react-redux';

import PageHeader from 'antd/es/page-header';

import { Button } from '@manyun/base-ui.ui.button';
import { Image } from '@manyun/base-ui.ui.image';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';

import { fetchBizFileInfos } from '@manyun/dc-brain.service.fetch-biz-file-infos';
import { AlarmLevelText } from '@manyun/monitoring.ui.alarm-level-text';
import { TypeAttributionText } from '@manyun/resource-hub.ui.type-attribution';
import { UnitMetaDataText } from '@manyun/resource-hub.ui.unit-meta-data-text';

import { FooterToolBar, TinyDescriptions } from '@manyun/dc-brain.legacy.components';
import {
  POINT_DATA_TYPE_CODE_MAP,
  POINT_TYPE_CODE_MAP,
} from '@manyun/dc-brain.legacy.constants/point';
import { validateExpr } from '@manyun/dc-brain.legacy.pages/merged-processed-point/utils';
import {
  getPointDetail,
  mergedProcessesdPointActions,
} from '@manyun/dc-brain.legacy.redux/actions/mergedProcessesdPointActions';
import { deviceTypeService } from '@manyun/dc-brain.legacy.services';

import DITable from '../../single-point-detail-card/di-table';

export class Detail extends React.Component {
  state = {
    deviceInfo: {},
    pointInfo: {},
    formulaTxt: '',
  };

  componentDidMount() {
    const { pattern, selectCategoryPoint, cardMode } = this.props;
    if (
      selectCategoryPoint.deviceType &&
      selectCategoryPoint.type === 'device' &&
      pattern === 'list' &&
      cardMode === 'basic'
    ) {
      this.getDeviceDetail();
    }
    if (pattern === 'detail' && cardMode === 'point') {
      this.getPointsInfo();
      this.getTxt();
    }
  }

  componentDidUpdate(prevProps) {
    const { pattern, selectCategoryPoint, cardMode } = this.props;
    if (selectCategoryPoint.type === 'custom') {
      return;
    }
    if (
      pattern === 'list' &&
      cardMode === 'basic' &&
      selectCategoryPoint.deviceType &&
      (!prevProps.selectCategoryPoint.deviceType ||
        prevProps.selectCategoryPoint.deviceType !== selectCategoryPoint.deviceType ||
        prevProps.cardMode !== 'basic')
    ) {
      this.getDeviceDetail();
    }
  }

  componentWillUnmount() {
    this.props.resetItems();
  }
  getDeviceDetail = async () => {
    if (!this.props.selectCategoryPoint.deviceType) {
      return;
    }
    // 请求设备详情
    const { response, error } = await deviceTypeService.getDeviceTypeDetail({
      code: this.props.selectCategoryPoint.deviceType,
    });
    const { data, error: fileError } = await fetchBizFileInfos({
      targetId: this.props.selectCategoryPoint.deviceType,
      targetType: 'DEVICE_TYPE',
    });

    fetchBizFileInfos({
      targetId: this.props.selectCategoryPoint.deviceType,
      targetType: 'DEVICE_TYPE',
    }).then(({ data, error }) => {
      if (error) {
        message.error(error.message);
        return;
      }
    });
    if (error) {
      message.error(error);
    }
    if (fileError) {
      message.error(fileError.message);
    }
    if (response) {
      const deviceInfo = {
        ...response,
        deviceImgUrl: (data?.data || []).length ? data.data[0].src : '',
      };
      this.setState({ deviceInfo });
    }
  };

  getPointsInfo = () => {
    // 请求测点详情
    const { info } = this.props;
    const params = {
      deviceType: info.deviceType,
      pointCode: info.pointCode,
      successCb: async value => {
        this.setState({
          pointInfo: value,
        });
      },
    };
    this.props.getPointsInfo(params);
  };

  getTxt = async () => {
    const { info, treeMode } = this.props;
    if (
      ((info.pointType.code === POINT_TYPE_CODE_MAP.CAL_SPACE ||
        info.pointType.code === POINT_TYPE_CODE_MAP.CAL_DEVICE ||
        info.pointType.code === POINT_TYPE_CODE_MAP.CUSTOM) &&
        info.dataType.code === POINT_DATA_TYPE_CODE_MAP.AI) ||
      treeMode === 'custom'
    ) {
      // 如果是一些系统不能配置的特殊表达式，直接显示表达式字符串
      if (info.formula && !info.formulaJson) {
        this.setState({
          formulaTxt: info.formula,
        });
        return;
      }
      const { exprTxt } = await validateExpr(info);
      this.setState({
        formulaTxt: exprTxt,
      });
    } else {
      this.setState({
        formulaTxt: '',
      });
    }
  };

  getDescriptionsItems = () => {
    const { info, cardMode, selectCategoryPoint, treeMode } = this.props;
    const { deviceInfo, pointInfo, formulaTxt } = this.state;
    let descriptionsItems = [];
    //自定义测点
    if (treeMode === 'custom') {
      if (info.dataType.code === POINT_DATA_TYPE_CODE_MAP.DI) {
        descriptionsItems = [
          {
            label: '测点ID',
            value: info.code,
          },
          {
            label: '测点名称',
            value: info.name,
          },
          {
            label: '测点类型',
            value: info.dataType?.name,
          },
          {
            label: '计算表达式',
            value: formulaTxt,
            span: 4,
          },
        ];
      } else {
        descriptionsItems = [
          {
            label: '测点ID',
            value: info.code,
          },
          {
            label: '测点名称',
            value: info.name,
          },
          {
            label: '测点类型',
            value: info.dataType?.name,
          },
          {
            label: '测点单位',
            value: info.unit,
          },
          {
            label: '测点精度',
            value: info.precision,
            span: 4,
          },
          {
            label: '计算表达式',
            value: formulaTxt,
            span: 4,
          },
        ];
      }
      return descriptionsItems;
    }
    if (cardMode === 'basic') {
      descriptionsItems = [
        {
          label: '类型名称',
          value: deviceInfo.name,
        },
        {
          label: '类型编号',
          value: deviceInfo.code,
        },
        {
          label: '类型归属',
          value: <TypeAttributionText value={deviceInfo.type} />,
        },
        {
          label: '创建时间',
          value: deviceInfo.gmtCreate,
        },
        {
          label: '更新时间',
          value: deviceInfo.gmtModified,
        },
        {
          label: '更新人',
          value: deviceInfo.operatorName,
        },
        {
          label: '是否有编号',
          value: deviceInfo.numbered ? '是' : '否',
        },
      ];
      if (selectCategoryPoint?.selectType === 'C2') {
        descriptionsItems.push(
          {
            label: '类型单位',
            value: deviceInfo.unitCode ? <UnitMetaDataText unitCode={deviceInfo.unitCode} /> : '--',
          },
          {
            label: '拟物图',
            value: deviceInfo.deviceImgUrl ? (
              <Image width={102} height={102} src={deviceInfo.deviceImgUrl} alt="deviceType-img" />
            ) : (
              '--'
            ),
          }
        );
      }
    }
    // 原始测点
    if (cardMode !== 'basic' && info.pointType && info.pointType.code === POINT_TYPE_CODE_MAP.ORI) {
      if ([POINT_DATA_TYPE_CODE_MAP.AI, POINT_DATA_TYPE_CODE_MAP.AO].includes(info.dataType.code)) {
        descriptionsItems = [
          {
            label: '测点ID',
            value: info.code,
          },
          {
            label: '测点名称',
            value: info.name,
          },
          {
            label: '测点类型',
            value: info.dataType?.name,
          },
          {
            label: '精度',
            value: info.precision,
          },
          {
            label: '单位',
            value: info.unit,
          },
          {
            label: '工作区间',
            value: info.validLimits ? `${info.validLimits.ge} - ${info.validLimits.le}` : null,
          },
          {
            label: '扩展测点数量',
            value: info.extCount,
          },
          {
            label: '扩展测点名称',
            value: info.extName,
          },
          {
            label: '备注',
            value: info.description,
            span: 3,
          },
        ];
      }
      if ([POINT_DATA_TYPE_CODE_MAP.DI, POINT_DATA_TYPE_CODE_MAP.DO].includes(info.dataType.code)) {
        descriptionsItems = [
          {
            label: '测点ID',
            value: info.code,
          },
          {
            label: '测点名称',
            value: info.name,
          },
          {
            label: '测点类型',
            value: info.dataType?.name,
          },
          {
            label: '精度',
            value: info.precision,
          },
          {
            label: '单位',
            value: info.unit,
          },
          {
            label: '扩展测点数量',
            value: info.extCount,
          },
          {
            label: '扩展测点名称',
            value: info.extName,
          },
          {
            label: '状态含义',
            value: getValidLimitsTxt(info.validLimits),
            span: 4,
          },
          {
            label: '备注',
            value: info.description,
            span: 4,
          },
        ];
      }

      if ([POINT_DATA_TYPE_CODE_MAP.ALARM].includes(info.dataType.code)) {
        descriptionsItems = [
          {
            label: '测点ID',
            value: info.code,
          },
          {
            label: '测点名称',
            value: info.name,
          },
          {
            label: '测点类型',
            value: info.dataType?.name,
          },
          {
            label: '精度',
            value: info.precision,
          },
          {
            label: '单位',
            value: info.unit,
          },
          {
            label: '扩展测点数量',
            value: info.extCount,
          },
          {
            label: '扩展测点名称',
            value: info.extName,
          },
          {
            label: '告警类型',
            value: info.alarmType?.code,
          },
          {
            label: '告警水平',
            value: <AlarmLevelText code={info.alarmLevel} />,
            span: 3,
          },
          {
            label: '备注',
            value: info.description,
            span: 4,
          },
        ];
      }
    }
    // 聚合测点
    if (
      cardMode !== 'basic' &&
      info.pointType &&
      (info.pointType.code === POINT_TYPE_CODE_MAP.AGG_SPACE ||
        info.pointType.code === POINT_TYPE_CODE_MAP.AGG_DEVICE)
    ) {
      descriptionsItems = [
        {
          label: '测点ID',
          value: info.code,
        },
        {
          label: '测点名称',
          value: info.name,
        },
        {
          label: '测点类型',
          value: info.dataType?.name,
        },
        {
          label: '精度',
          value: info.precision,
        },
        {
          label: '单位',
          value: info.unit,
          span: 4,
        },
        {
          label: '备注',
          value: info.description,
          span: 4,
        },
      ];
    }
    // 加工点位
    if (
      cardMode !== 'basic' &&
      info.pointType &&
      (info.pointType.code === POINT_TYPE_CODE_MAP.CAL_SPACE ||
        info.pointType.code === POINT_TYPE_CODE_MAP.CAL_DEVICE ||
        info.pointType.code === POINT_TYPE_CODE_MAP.CUSTOM)
    ) {
      // 如果是DI量
      if (
        info.dataType &&
        info.dataType.code === POINT_DATA_TYPE_CODE_MAP.DI &&
        pointInfo.statusList &&
        pointInfo.statusList.value
      ) {
        descriptionsItems = [
          {
            label: '测点ID',
            value: info.code,
          },
          {
            label: '测点名称',
            value: info.name,
          },
          {
            label: '测点类型',
            value: info.dataType?.name,
          },
        ];

        if (info.formulaJson) {
          // descriptionsItems.push({
          //   label: '',
          //   value: <DITable tableData={pointInfo.statusList.value} />,
          // });
          descriptionsItems = [
            {
              label: '测点ID',
              value: info.code,
            },
            {
              label: '测点名称',
              value: info.name,
            },
            {
              label: '测点类型',
              value: info.dataType?.name,
              span: 2,
            },
            {
              label: '',
              value: <DITable tableData={pointInfo.statusList.value} />,
            },
          ];
        }
        // 如果是一些系统不能配置的特殊表达式，直接显示表达式字符串
        if (info.formula && !info.formulaJson) {
          descriptionsItems.push({
            label: '表达式',
            value: info.formula,
          });
        }
      }
      if (info.dataType && info.dataType.code === POINT_DATA_TYPE_CODE_MAP.AI) {
        descriptionsItems = [
          {
            label: '测点ID',
            value: info.code,
          },
          {
            label: '测点名称',
            value: info.name,
          },
          {
            label: '测点类型',
            value: info.dataType?.name,
          },
          {
            label: '测点单位',
            value: info.unit,
          },
          {
            label: '测点精度',
            value: info.precision,
            span: 4,
          },
          {
            label: '计算表达式',
            value: formulaTxt,
            span: 4,
          },
        ];
      }
    }
    return descriptionsItems;
  };

  render() {
    const { cardMode, selectedTab } = this.props;
    return (
      <>
        <Space style={{ marginBottom: '40px' }} direction="vertical">
          {cardMode !== 'basic' && (
            <PageHeader
              style={{ padding: 0 }}
              title=" "
              onBack={() => {
                this.props.setPattern('list');
              }}
            />
          )}
          <TinyDescriptions
            style={{ paddingLeft: '20px' }}
            column={cardMode === 'basic' ? 1 : 4}
            descriptionsItems={this.getDescriptionsItems()}
          />
        </Space>
        {selectedTab !== 'merge' && (
          <FooterToolBar>
            <Button
              type="primary"
              onClick={() => {
                this.props.setPattern('edit');
              }}
            >
              编辑
            </Button>
          </FooterToolBar>
        )}
      </>
    );
  }
}

const mapStateToProps = ({ doPointManage: { selectCategoryPoint } }) => {
  return {
    selectCategoryPoint,
  };
};
const mapDispatchToProps = {
  getPointsInfo: getPointDetail,
  resetItems: mergedProcessesdPointActions.resetItems,
};
export default connect(mapStateToProps, mapDispatchToProps)(Detail);

function getValidLimitsTxt(validLimits) {
  if (!validLimits || !validLimits.length) {
    return '';
  }
  const newValidLimits = validLimits.map(item => {
    return item.replace('=', '：');
  });
  return newValidLimits.join(' | ');
}
