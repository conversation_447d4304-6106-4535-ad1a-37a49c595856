import saveAs from 'file-saver';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import { useAuthorized } from '@manyun/auth-hub.hook.gql.use-authorized';
import { Button } from '@manyun/base-ui.ui.button';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { exportPoint } from '@manyun/resource-hub.service.export-point';

import { GutterWrapper, TinyTable } from '@manyun/dc-brain.legacy.components';
import { POINT_TYPE_CODE_MAP } from '@manyun/dc-brain.legacy.constants/point';
import {
  deletePointAction,
  doPointActions,
  fetchPointDetail,
} from '@manyun/dc-brain.legacy.redux/actions/doPointActions';

const { Search } = Input;

const columns = ctx => [
  {
    title: '测点ID',
    dataIndex: 'code',
  },
  {
    title: '测点名称',
    dataIndex: 'name',
    render: (name, record) => (
      <Button
        type="link"
        style={{ height: 'auto', padding: '0' }}
        onClick={() => {
          let validLimits = null;
          if (
            record.validLimits &&
            Array.isArray(record.validLimits) &&
            record.validLimits.length === 2 &&
            (record.dataType.code === 'AI' || record.dataType.code === 'AO')
          ) {
            validLimits = {
              ge: record.validLimits[0].split('=')[1],
              le: record.validLimits[1].split('=')[1],
            };
          }
          if (validLimits) {
            ctx.props.setDetail({ ...record, validLimits });
          } else {
            ctx.props.setDetail({ ...record });
          }
          ctx.props.setPattern('detail');
        }}
      >
        {name}
      </Button>
    ),
  },
  {
    title: '测点类型',
    dataIndex: 'dataType',
    render: dataType => {
      if (dataType) {
        return dataType.name;
      } else {
        return null;
      }
    },
  },
  {
    title: '扩展测点名称',
    dataIndex: 'extName',
  },
  {
    title: '单位',
    dataIndex: 'unit',
  },
  {
    title: '工作区间',
    dataIndex: 'validLimit',
    render: (_, record) => {
      if (record.dataType && (record.dataType.code === 'DI' || record.dataType.code === 'DO')) {
        if (Array.isArray(record.validLimits) && record.validLimits.length) {
          const tmp = record.validLimits.map(item => {
            return item.split('=')[1];
          });
          if (tmp.length <= 2) {
            return tmp.join('，');
          }
          if (tmp.length > 2) {
            return (
              <Button type="link" onClick={() => ctx.showDigitalDesc(tmp, '工作区间')}>
                查看
              </Button>
            );
          }
        }
        return null;
      }
      if (record.validLimits && record.validLimits.length === 2) {
        const min = ['', null, undefined].includes(record.validLimits[0])
          ? null
          : record.validLimits[0].split('=')[1];
        const max = ['', null, undefined].includes(record.validLimits[1])
          ? null
          : record.validLimits[1].split('=')[1];
        return (
          <span>
            {min}~{max}
          </span>
        );
      }
      return null;
    },
  },
  {
    title: '精度',
    dataIndex: 'precision',
  },

  {
    title: '状态含义',
    dataIndex: 'validLimits',
    render: (validLimits, record) => {
      if (validLimits === null || record.dataType.code === 'AI' || record.dataType.code === 'AO') {
        return null;
      }
      if (validLimits && validLimits.length <= 2) {
        const data = validLimits.join(';');
        return <span>{data}</span>;
      }
      if (validLimits && validLimits.length > 2) {
        return (
          <Button type="link" onClick={() => ctx.showDigitalDesc(validLimits, '状态含义')}>
            查看
          </Button>
        );
      }
    },
  },
  {
    title: '备注',
    dataIndex: 'description',
    render: description => {
      if (!description) {
        return null;
      }
      if (description.length <= 15) {
        return <span>{description}</span>;
      } else {
        return (
          <Tooltip placement="top" title={description}>
            <span>{description.slice(0, 15)}...</span>
          </Tooltip>
        );
      }
    },
  },
  {
    title: '操作',
    key: '__actions',
    fixed: 'right',
    render: (__, record) => {
      return (
        <span>
          <Button
            compact
            type="link"
            onClick={() => {
              let validLimits = null;
              if (
                record.validLimits &&
                Array.isArray(record.validLimits) &&
                record.validLimits.length === 2 &&
                (record.dataType.code === 'AI' || record.dataType.code === 'AO')
              ) {
                validLimits = {
                  ge: record.validLimits[0].split('=')[1],
                  le: record.validLimits[1].split('=')[1],
                };
              }
              if (validLimits) {
                ctx.props.setDetail({ ...record, validLimits });
              } else {
                ctx.props.setDetail({ ...record });
              }
              ctx.props.setPattern('edit');
            }}
          >
            编辑
          </Button>
          {!record.isInitialized && (
            <>
              <Divider type="vertical" />
              <DeleteConfirm
                targetName={record.name}
                onOk={({ reason }) => ctx.delete(record, reason)}
              >
                <Button type="link" compact>
                  删除
                </Button>
              </DeleteConfirm>
            </>
          )}
        </span>
      );
    },
  },
];

class pointTable extends Component {
  state = {
    pageNo: 1,
    searchValue: '',
    checkboxValue: [],
    exportLoading: false,
  };

  componentDidMount() {
    if (
      this.props.selectCategoryPoint.deviceType &&
      this.props.selectCategoryPoint.selectType === 'C2'
    ) {
      this.props.fetchPoints({
        deviceType: this.props.selectCategoryPoint.deviceType,
        dataTypeList: ['AI', 'DI', 'AO', 'DO', 'ALARM'],
        pointTypeList: [POINT_TYPE_CODE_MAP.ORI],
        isRemoveSub: true,
      });
    }
    this.setState({ pageNo: 1, searchValue: '', checkboxValue: [] });
  }

  componentDidUpdate(prevProps) {
    if (!this.props.selectCategoryPoint || !prevProps.selectCategoryPoint) {
      return;
    }
    if (this.props.treeMode === 'device') {
      // 设备类型下

      // 设备类型改变 重新请求列表
      if (
        this.props.selectCategoryPoint.deviceType &&
        this.props.selectCategoryPoint.deviceType !== prevProps.selectCategoryPoint.deviceType &&
        this.props.selectCategoryPoint.selectType === 'C2' &&
        this.props.cardMode === 'point'
      ) {
        this.props.fetchPoints({
          deviceType: this.props.selectCategoryPoint.deviceType,
          dataTypeList: ['AI', 'DI', 'AO', 'DO', 'ALARM'],
          pointTypeList: [POINT_TYPE_CODE_MAP.ORI],
          isRemoveSub: true,
        });
        this.setState({ pageNo: 1, searchValue: '', checkboxValue: [] });
      }

      //选择了设备类型，切换测点列表tab 重新请求列表
      if (
        this.props.selectCategoryPoint.selectType === 'C2' &&
        this.props.selectCategoryPoint.deviceType &&
        this.props.cardMode === 'point' &&
        (prevProps.selectedTab !== this.props.selectedTab ||
          prevProps.cardMode !== this.props.cardMode)
      ) {
        this.props.fetchPoints({
          deviceType: this.props.selectCategoryPoint.deviceType,
          dataTypeList: ['AI', 'DI', 'AO', 'DO', 'ALARM'],
          pointTypeList: [POINT_TYPE_CODE_MAP.ORI],
          isRemoveSub: true,
        });
        this.setState({ pageNo: 1, searchValue: '', checkboxValue: [] });
      }
    }
  }

  // addPoint = () => {
  //   // this.props.closeVisible();
  //   this.props.setCreateStatus('orginal');
  // };

  delete = ({ code }, operatorNotes) => {
    const {
      selectCategoryPoint: { deviceType },
      deletePointAction,
      fetchPoints,
    } = this.props;

    return new Promise(resolve => {
      deletePointAction({
        params: { code, operatorNotes, deviceType },
        successCb: () => {
          resolve(true);
          fetchPoints({
            deviceType,
            dataTypeList: ['AI', 'DI', 'AO', 'DO', 'ALARM'],
            pointTypeList: [POINT_TYPE_CODE_MAP.ORI],
            isRemoveSub: true,
          });
        },
        errorCb: () => {
          resolve(false);
        },
      });
    });
  };

  showDigitalDesc = (validLimits, title) => {
    Modal.info({
      title: title,
      okText: '确定',
      footer: null,
      content: (
        <div>
          {validLimits.map((item, index) => {
            return <p key={index}>{item}</p>;
          })}
        </div>
      ),
    });
  };

  onChangePageNo = pageNum => {
    this.setState({
      pageNo: pageNum,
    });
  };

  searchPoint = () => {
    this.setState({
      pageNo: 1,
    });
  };

  changeSearchValue = e => {
    this.setState({
      searchValue: e.target.value,
    });
  };

  onChangeCheckbox = checkedValues => {
    this.setState({
      checkboxValue: checkedValues,
      pageNo: 1,
    });
  };

  getChangeAndCheckBoxedData = () => {
    const { searchValue, checkboxValue } = this.state;
    const { surePointDetail } = this.props;

    if (!checkboxValue.length && !searchValue) {
      return surePointDetail.data;
    } else if (!checkboxValue.length) {
      let index = '';
      let newData = [];
      newData = surePointDetail.data.filter(point => {
        index =
          point.name.indexOf(searchValue) !== -1
            ? point.name.indexOf(searchValue)
            : point.code.indexOf(searchValue);
        if (index > -1) {
          return true;
        } else {
          return false;
        }
      });
      return newData;
    } else {
      let totalData = [];
      checkboxValue.forEach(code => {
        const newData = surePointDetail.data.filter(item => {
          let index;
          if (!searchValue) {
            index = 0;
          } else {
            index = item.name.indexOf(searchValue);
          }

          if (item.dataType.code === code && index > -1) {
            return true;
          } else {
            return false;
          }
        });
        totalData = [...totalData, ...newData];
      });
      return totalData;
    }
  };

  reset = () => {
    this.setState({
      checkboxValue: [],
      pageNo: 1,
    });
  };

  onHandleExportPoint = async () => {
    this.setState({ exportLoading: true });
    const { error, data } = await exportPoint({
      pointTypeList: ['ORI'],
      deviceType: this.props.selectCategoryPoint.deviceType,
    });

    this.setState({ exportLoading: false });

    if (error) {
      message.error(error.message);
      return;
    }
    saveAs(data, '原始测点.xlsx');
  };

  render() {
    const { loading, selectCategoryPoint, integratedData } = this.props;
    const { searchValue, checkboxValue, exportLoading } = this.state;

    return (
      <GutterWrapper mode="vertical">
        {selectCategoryPoint.selectType === 'C2' && (
          <TinyTable
            rowKey="code"
            loading={loading}
            dataSource={this.getChangeAndCheckBoxedData()}
            columns={columns(this)}
            scroll={{ x: 'max-content' }}
            actionsWrapperStyle={{ justifyContent: 'space-between' }}
            actions={[
              <Space key="create" style={{ width: '100%' }} direction="vertical">
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Space>
                    <Button
                      type="primary"
                      onClick={() => {
                        this.props.setPattern('create');
                      }}
                    >
                      新增
                    </Button>
                    <RenderAuthorized>
                      <Button loading={exportLoading} onClick={this.onHandleExportPoint}>
                        导出
                      </Button>
                    </RenderAuthorized>
                  </Space>
                  <Search
                    key="search"
                    allowClear
                    value={searchValue}
                    placeholder="搜索测点名称或编码"
                    style={{ width: 200 }}
                    onSearch={this.searchPoint}
                    onChange={this.changeSearchValue}
                  />
                </div>
                <Space>
                  <Checkbox.Group
                    options={integratedData}
                    value={checkboxValue}
                    onChange={this.onChangeCheckbox}
                  />
                  <Button size="small" onClick={this.reset}>
                    重置
                  </Button>
                </Space>
              </Space>,
            ]}
          />
        )}
      </GutterWrapper>
    );
  }
}
const mapStateToProps = ({
  doPointManage: { loading, pointDetail, surePointDetail, selectCategoryPoint, integratedData },
}) => ({
  loading,
  pointDetail,
  surePointDetail,
  selectCategoryPoint,
  integratedData,
});

const mapDispatchToProps = {
  closeVisible: doPointActions.addPointVisible,
  deletePointAction,
  fetchPoints: fetchPointDetail,
};

export default connect(mapStateToProps, mapDispatchToProps)(pointTable);

function RenderAuthorized({ children }) {
  const [, { checkCode }] = useAuthorized();

  return <>{checkCode('element_mergerd-processed-points-export-btn') && children}</>;
}
