import difference from 'lodash/difference';

import { mergedProcessesdPointService } from '@manyun/dc-brain.legacy.services';
import { flattenTreeData, isInvalidRequest } from '@manyun/dc-brain.legacy.utils';

import { EXPR_ITEM_TYPE } from '../constants';

export function generateNodeKey({ metaCode, metaType, nodeType }) {
  if (nodeType) {
    return `${metaType}${metaCode}${nodeType}`;
  }
  return `${metaType}${metaCode}`;
}

export function diExprToTableData(exprItems = []) {
  // 记录需要更新 label, status 字段的行号
  let rowIdx = -1;

  return exprItems.reduce((data, item, index) => {
    // 过滤掉无效项
    if (['logic-operator', 'equality-operator', 'delimiter'].includes(item.type)) {
      return data;
    }

    // 表示这是第一个被解析的测点或上一个测点数据解析完成，要新增一行数据
    if (item.type === 'point' && (data.length === 0 || data[data.length - 1].value)) {
      if (rowIdx === -1) {
        rowIdx = data.length > 0 ? data.length : 0;
      }

      data.push({
        name: item.name,
        value: item.value,
      });

      return data;
    }

    // 更新当前测点的点位状态数据
    if (item.type === 'number') {
      // TODO: 需要请求 API 将当前点位的 validLimits 拿过来用
      data[data.length - 1].validLimits = [{ label: item.name, value: item.value }];

      data[data.length - 1].dataType = item.value;

      return data;
    }

    // 表示一行状态含义解析完成
    if (item.type === 'status') {
      rowIdx > -1 &&
        getSameLabelData(rowIdx, data.length - 1).forEach(idx => {
          data[idx].label = item.value;
          data[idx].status = item.name;
        });

      // 重置行号
      rowIdx = -1;

      return data;
    }

    return data;
  }, []);
}

export async function validateExpr(single) {
  const result = { invalidReq: false, exprTxt: null };

  let exprs;
  try {
    exprs = JSON.parse(single.formulaJson);
  } catch (err) {
    console.error(err);
  }

  if (!Array.isArray(exprs)) {
    return result;
  }

  // 如果表达式中有测点，批量设置测点值为 1
  const codeMap = flattenTreeData(exprs).reduce((map, { type, value }) => {
    if (
      [
        EXPR_ITEM_TYPE.POINT,
        EXPR_ITEM_TYPE.SPACE_POINT,
        EXPR_ITEM_TYPE.CUSTOM_SPACE_POINT,
        EXPR_ITEM_TYPE.DEVICE_PROPERTY,
      ].includes(type)
    ) {
      map[value] = 1;
    }

    return map;
  }, {});

  const params = {
    dataType: single.dataType,
    precision: single.precision,
    pointValue: codeMap,
    expressInfos: exprs,
  };

  const { error, errorCode, response } =
    await mergedProcessesdPointService.checkCalculatedAiPointExpression(params);

  if (error) {
    // message.error(error);

    if (isInvalidRequest(errorCode)) {
      result.invalidReq = true;
    }

    return result;
  }

  const [txt] = String(response).split('=');
  result.exprTxt = txt;

  return result;
}

export function getDifferenceData(status, aggstatus, disabled) {
  let differentStatus = null;
  let differentAggStatus = null;
  const statuskeys = Object.keys(status);
  const disabledKeys = disabled ? Object.keys(disabled) : [];
  const differenceKeys = difference(statuskeys, disabledKeys);
  // 如果状态改变了 取出status 改变了的值
  if (differenceKeys.length) {
    differenceKeys.forEach(key => {
      differentStatus = { ...differentStatus, [key]: status[key] };
      differentAggStatus = { ...differentAggStatus, [key]: aggstatus[key] };
    });
  }
  // 状态值不变 比较维度
  disabledKeys.forEach(key => {
    const status_agg_l = aggstatus[key];
    const status_agg_S = disabled[key];
    const differenc_status_agg = difference(status_agg_l, status_agg_S);
    if (differenc_status_agg.length) {
      differentAggStatus = {
        ...differentAggStatus,
        [key]: differenc_status_agg,
      };
      differentStatus = {
        ...differentStatus,
        [key]: status[key],
      };
    }
  });

  return {
    differentStatus,
    differentAggStatus,
  };
}

function getSameLabelData(start, end) {
  const result = [];
  for (let i = start; i <= end; i++) {
    result.push(i);
  }
  return result;
}
