import { SPACE_DEVICE_TYPE_KEY_MAP } from '@manyun/dc-brain.legacy.constants/enums/deviceType';

export const POINT_TREE_DROPPABLE_ID = 'treeLest';

export const EXPR_ITEM_TYPE = {
  ARITHMETIC_OPERATOR: 'arithmetic-operator',
  STATISTICAL_FUNCTION: 'statistical-function',
  NUMBER: 'number',
  PARENTHESES: 'parentheses',
  DELIMITER: 'delimiter',
  POINT: 'point',
  SPACE_POINT: 'space-point',
  CUSTOM_SPACE_POINT: 'custom-space-point',
  DEVICE_PROPERTY: 'device-property',
  PARAMETER: 'parameter',
  BLOCK_ATTRIBUTE: 'block-attribute',
};

export const EXPR_DELIMITER_ITEM = {
  type: EXPR_ITEM_TYPE.DELIMITER,
  value: ',',
  name: ',',
};

export const ARITHMETIC_OPERATORS = [
  {
    id: 'arithmetic-operator.addition',
    type: EXPR_ITEM_TYPE.ARITHMETIC_OPERATOR,
    value: '+',
    name: '+',
  },
  {
    id: 'arithmetic-operator.subtraction',
    type: EXPR_ITEM_TYPE.ARITHMETIC_OPERATOR,
    value: '-',
    name: '-',
  },
  {
    id: 'arithmetic-operator.multiplication',
    type: EXPR_ITEM_TYPE.ARITHMETIC_OPERATOR,
    name: 'x',
    value: '*',
  },
  {
    id: 'arithemetic-operator.division',
    type: EXPR_ITEM_TYPE.ARITHMETIC_OPERATOR,
    name: '÷',
    value: '/',
  },
];

export const STATISTICAL_FUNCTIONS = [
  {
    id: 'statistical-funtion.sum',
    type: EXPR_ITEM_TYPE.STATISTICAL_FUNCTION,
    value: 'sum',
    name: 'SUM',
  },
  {
    id: 'statistical-funtion.avg',
    type: EXPR_ITEM_TYPE.STATISTICAL_FUNCTION,
    value: 'avg',
    name: 'AVG',
  },
  {
    id: 'statistical-funtion.max',
    type: EXPR_ITEM_TYPE.STATISTICAL_FUNCTION,
    value: 'mymax',
    name: 'MAX',
  },
  {
    id: 'statistical-funtion.min',
    type: EXPR_ITEM_TYPE.STATISTICAL_FUNCTION,
    value: 'mymin',
    name: 'MIN',
  },
];

export const OTHERS = [
  { id: 'other.number', type: EXPR_ITEM_TYPE.NUMBER, value: 1, name: 'Num' },
  { id: 'other.parentheses', type: EXPR_ITEM_TYPE.PARENTHESES, value: '()', name: '(  )' },
  { id: 'other.point', type: EXPR_ITEM_TYPE.POINT, value: '', name: '测点' },
  { id: 'other.parameter', type: EXPR_ITEM_TYPE.PARAMETER, value: '', name: '参数' },
  {
    id: 'other.block-attribute',
    type: EXPR_ITEM_TYPE.BLOCK_ATTRIBUTE,
    value: '',
    name: '空间属性',
  },
  {
    id: 'other.custom-space-point',
    type: EXPR_ITEM_TYPE.CUSTOM_SPACE_POINT,
    value: '',
    name: '空间自定义测点',
  },
];

export const TPL_ITEMS = [...ARITHMETIC_OPERATORS, ...STATISTICAL_FUNCTIONS, ...OTHERS];

// 表达式类型
export const EXPRESSION_TYPE = {
  arithmeticOperators: ARITHMETIC_OPERATORS,
  statisticalFunctions: STATISTICAL_FUNCTIONS,
  others: OTHERS,
};

export const expressionType = {
  arithmeticOperators: 'arithmetic-operator',
  statisticalFunctions: 'statistical-function',
  others: 'others',
};

export const OPERATOR_TYPE_TEXT_OPTION = {
  '+': '+',
  '-': '-',
  x: '*',
  '÷': '/',
  SUM: 'sum',
  AVG: 'avg',
  MAx: 'mymax',
  MIN: 'mymin',
  Num: 'number',
  parentheses: 'parentheses',
};

export const MERGE_DIRECTION_CODE_MAP = {
  IDC: 'IDC',
  BLOCK: 'BLOCK',
  ROOM: 'ROOM',
  COLUMN: 'COLUMN',
  GRID: 'GRID',
  DEVICE: 'DEVICE',
};

/**
 * 从大到小排列好的测点维度
 */
export const ORDERED_POINT_DIMENSIONS = [
  MERGE_DIRECTION_CODE_MAP.IDC,
  MERGE_DIRECTION_CODE_MAP.BLOCK,
  MERGE_DIRECTION_CODE_MAP.ROOM,
  MERGE_DIRECTION_CODE_MAP.COLUMN,
  MERGE_DIRECTION_CODE_MAP.GRID,
  MERGE_DIRECTION_CODE_MAP.DEVICE,
];

export const YES_OR_NO_KEY_MAP = {
  YES: 'alwaysTrue',
  NO: '',
};

export const YES_OR_NO_TEXT_MAP = {
  YES: '是',
  NO: '否',
};

export const YES_OR_NO_OPTIONS = [
  {
    label: YES_OR_NO_TEXT_MAP.YES,
    value: YES_OR_NO_KEY_MAP.YES,
  },
  {
    label: YES_OR_NO_TEXT_MAP.NO,
    value: YES_OR_NO_KEY_MAP.NO,
  },
];

export const DIMENSION_ENUMS_SELECT_AI = [
  {
    title: '机房',
    value: MERGE_DIRECTION_CODE_MAP.IDC,
    key: MERGE_DIRECTION_CODE_MAP.IDC,
    children: [
      {
        title: '最大值',
        text: '机房最大值',
        value: 'IDC.MAX',
        key: 'IDC.MAX',
      },
      {
        title: '最小值',
        text: '机房最小值',
        value: 'IDC.MIN',
        key: 'IDC.MIN',
      },
      {
        title: '平均值',
        text: '机房平均值',
        value: 'IDC.AVG',
        key: 'IDC.AVG',
      },
      {
        title: '求和值',
        text: '机房求和值',
        value: 'IDC.SUM',
        key: 'IDC.SUM',
      },
    ],
  },
  {
    title: '楼栋',
    value: MERGE_DIRECTION_CODE_MAP.BLOCK,
    key: MERGE_DIRECTION_CODE_MAP.BLOCK,
    children: [
      {
        title: '最大值',
        text: '楼栋最大值',
        value: 'BLOCK.MAX',
        key: 'BLOCK.MAX',
      },
      {
        title: '最小值',
        text: '楼栋最小值',
        value: 'BLOCK.MIN',
        key: 'BLOCK.MIN',
      },
      {
        title: '平均值',
        text: '楼栋平均值',
        value: 'BLOCK.AVG',
        key: 'BLOCK.AVG',
      },
      {
        title: '求和值',
        text: '楼栋求和值',
        value: 'BLOCK.SUM',
        key: 'BLOCK.SUM',
      },
    ],
  },

  {
    title: '包间',
    value: MERGE_DIRECTION_CODE_MAP.ROOM,
    key: MERGE_DIRECTION_CODE_MAP.ROOM,
    children: [
      {
        title: '最大值',
        text: '包间最大值',
        value: 'ROOM.MAX',
        key: 'ROOM.MAX',
      },
      {
        title: '最小值',
        text: '包间最小值',
        value: 'ROOM.MIN',
        key: 'ROOM.MIN',
      },
      {
        title: '平均值',
        text: '包间平均值',
        value: 'ROOM.AVG',
        key: 'ROOM.AVG',
      },
      {
        title: '求和值',
        text: '包间求和值',
        value: 'ROOM.SUM',
        key: 'ROOM.SUM',
      },
    ],
  },
  {
    title: '机列',
    value: MERGE_DIRECTION_CODE_MAP.COLUMN,
    key: MERGE_DIRECTION_CODE_MAP.COLUMN,
    children: [
      {
        title: '最大值',
        text: '机列最大值',
        value: 'COLUMN.MAX',
        key: 'COLUMN.MAX',
      },
      {
        title: '最小值',
        text: '机列最小值',
        value: 'COLUMN.MIN',
        key: 'COLUMN.MIN',
      },
      {
        title: '平均值',
        text: '机列平均值',
        value: 'COLUMN.AVG',
        key: 'COLUMN.AVG',
      },
      {
        title: '求和值',
        text: '机列求和值',
        value: 'COLUMN.SUM',
        key: 'COLUMN.SUM',
      },
    ],
  },
  {
    title: '机柜',
    value: MERGE_DIRECTION_CODE_MAP.GRID,
    key: MERGE_DIRECTION_CODE_MAP.GRID,
    children: [
      {
        title: '最大值',
        text: '机柜最大值',
        value: 'GRID.MAX',
        key: 'GRID.MAX',
      },
      {
        title: '最小值',
        text: '机柜最小值',
        value: 'GRID.MIN',
        key: 'GRID.MIN',
      },
      {
        title: '平均值',
        text: '机柜平均值',
        value: 'GRID.AVG',
        key: 'GRID.AVG',
      },
      {
        title: '求和值',
        text: '机柜求和值',
        value: 'GRID.SUM',
        key: 'GRID.SUM',
      },
    ],
  },
  {
    title: '设备',
    value: MERGE_DIRECTION_CODE_MAP.DEVICE,
    key: MERGE_DIRECTION_CODE_MAP.DEVICE,
    children: [
      {
        title: '最大值',
        text: '设备最大值',
        value: 'DEVICE.MAX',
        key: 'DEVICE.MAX',
      },
      {
        title: '最小值',
        text: '设备最小值',
        value: 'DEVICE.MIN',
        key: 'DEVICE.MIN',
      },
      {
        title: '平均值',
        text: '设备平均值',
        value: 'DEVICE.AVG',
        key: 'DEVICE.AVG',
      },
      {
        title: '求和值',
        text: '设备求和值',
        value: 'DEVICE.SUM',
        key: 'DEVICE.SUM',
      },
    ],
  },
];

export const DIMENSION_ENUMS_SELECT_DI = [
  {
    title: '机房计数值',
    value: `${MERGE_DIRECTION_CODE_MAP.IDC}.COUNT`,
    key: `${MERGE_DIRECTION_CODE_MAP.IDC}.COUNT`,
    type: SPACE_DEVICE_TYPE_KEY_MAP.IDC,
  },
  {
    title: '楼栋计数值',
    value: `${MERGE_DIRECTION_CODE_MAP.BLOCK}.COUNT`,
    key: `${MERGE_DIRECTION_CODE_MAP.BLOCK}.COUNT`,
    type: SPACE_DEVICE_TYPE_KEY_MAP.BLOCK,
  },

  {
    title: '包间计数值',
    value: `${MERGE_DIRECTION_CODE_MAP.ROOM}.COUNT`,
    key: `${MERGE_DIRECTION_CODE_MAP.ROOM}.COUNT`,
    type: SPACE_DEVICE_TYPE_KEY_MAP.ROOM,
  },
  {
    title: '机列计数值',
    value: `${MERGE_DIRECTION_CODE_MAP.COLUMN}.COUNT`,
    key: `${MERGE_DIRECTION_CODE_MAP.COLUMN}.COUNT`,
    type: SPACE_DEVICE_TYPE_KEY_MAP.COLUMN,
  },
  {
    title: '机柜计数值',
    value: `${MERGE_DIRECTION_CODE_MAP.GRID}.COUNT`,
    key: `${MERGE_DIRECTION_CODE_MAP.GRID}.COUNT`,
    type: SPACE_DEVICE_TYPE_KEY_MAP.GRID,
  },
  {
    title: '设备计数值',
    value: `${MERGE_DIRECTION_CODE_MAP.DEVICE}.COUNT`,
    key: `${MERGE_DIRECTION_CODE_MAP.DEVICE}.COUNT`,
    type: 'device', // 无具体类型，占位用
  },
];

// 规格值类型 枚举
export const VALUE_TYPE_KEY_MAP = {
  NUMBER: 'NUMBER', // 数值
  CHARACTER: 'CHARACTER', // 文本
};

export const VALUE_TYPE_TEXT_MAP = {
  [VALUE_TYPE_KEY_MAP.NUMBER]: '数值',
  [VALUE_TYPE_KEY_MAP.CHARACTER]: '文本',
};

export const VALUE_TYPE_OPTIONS = [
  {
    value: VALUE_TYPE_KEY_MAP.NUMBER,
    label: VALUE_TYPE_TEXT_MAP[VALUE_TYPE_KEY_MAP.NUMBER],
  },
  {
    value: VALUE_TYPE_KEY_MAP.CHARACTER,
    label: VALUE_TYPE_TEXT_MAP[VALUE_TYPE_KEY_MAP.CHARACTER],
  },
];

/**
 * 创建 AI 自定义测点时的目标测点 Radio Group 的数据源
 */
export const TargetPointType = {
  /** 选择设备时，计算表达式中其他卡片中仅展示“设备测点”拖拽组件 */
  DEVICE: 'DEVICE',
  /** 选择设备时，计算表达式中其他卡片中仅展示“空间测点、自定义空间测点”拖拽组件 */
  SPACE: 'SPACE',
};
