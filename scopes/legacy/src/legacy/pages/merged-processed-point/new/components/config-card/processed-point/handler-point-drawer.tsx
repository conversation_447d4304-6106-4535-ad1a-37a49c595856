import React, { useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { ResourcePointDrawer } from '@manyun/resource-hub.ui.resource-point-drawer';
import { getSpaceGuid, getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

export function HandlerPointDrawer({
  customSpaceGuid,
  onChange,
}: {
  customSpaceGuid: string;
  onChange: (value: { deviceId: string; pointCode: string; deviceType: string }) => void;
}) {
  const { idc, block } = getSpaceGuidMap(customSpaceGuid);
  const spaceGuid = getSpaceGuid(idc!, block)!;
  const [open, setOpen] = useState(false);
  return (
    <span>
      <Button
        onClick={() => {
          setOpen(true);
        }}
      >
        查询测点
      </Button>
      <ResourcePointDrawer
        open={open}
        destroyOnClose
        maxCheckedCount={1}
        resourceTreeProps={{
          spaceGuid,
          treeMode:
            spaceGuid?.split('.').length === 1
              ? ['IDC', 'BLOCK', 'ROOM', 'DEVICE', 'COLUMN', 'GRID']
              : ['BLOCK', 'ROOM', 'DEVICE', 'COLUMN', 'GRID'],
        }}
        onClose={() => setOpen(false)}
        onChange={treePoints => {
          const treePoint = treePoints?.length ? treePoints[0] : undefined;
          if (!treePoint) {
            return;
          }
          if (treePoint.target.type === 'COLUMN' || treePoint.target.type === 'GRID') {
            //机柜机列暂不处理 目前没这个数据
            return;
          }
          const isSpace =
            treePoint.target.type === 'IDC' ||
            treePoint.target.type === 'BLOCK' ||
            treePoint.target.type === 'ROOM';
          onChange?.({
            deviceId: (isSpace ? treePoint.target.guid : treePoint.deviceId) as string,
            pointCode: treePoint.code,
            deviceType: treePoint.deviceType,
          });
        }}
      />
    </span>
  );
}
