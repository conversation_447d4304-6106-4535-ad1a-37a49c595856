import React, { Component, useState } from 'react';
import { Draggable, Droppable } from 'react-beautiful-dnd';
import { connect } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { Popover } from '@manyun/base-ui.ui.popover';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';
import { mergedProcessesdPointActions } from '@manyun/dc-brain.legacy.redux/actions/mergedProcessesdPointActions';

import { EXPRESSION_TYPE, expressionType } from '../../../constants';
import { ConditionalDraggable } from './conditional-draggable';
import { DragSpanWrapper } from './styled';

export class ArithmeticOperators extends Component {
  render() {
    return (
      <Droppable droppableId={expressionType.arithmeticOperators} direction="horizontal">
        {provided => (
          <GutterWrapper ref={provided.innerRef} {...provided.droppableProps}>
            {EXPRESSION_TYPE.arithmeticOperators.map(({ value, name }, index) => (
              <ArithmeticDragFileds
                className="manyun-btn manyun-btn-primary"
                value={name || value}
                index={index}
                key={index}
                draggableId={name || value}
              />
            ))}
            {provided.placeholder}
          </GutterWrapper>
        )}
      </Droppable>
    );
  }
}

export class StatisticalFunctions extends Component {
  render() {
    return (
      <Droppable droppableId={expressionType.statisticalFunctions} direction="horizontal">
        {provided => (
          <GutterWrapper ref={provided.innerRef} {...provided.droppableProps}>
            {EXPRESSION_TYPE.statisticalFunctions.map(({ value, name }, i) => (
              <FunctionDragFileds
                className="manyun-btn manyun-btn-primary"
                key={value}
                draggableId={name}
                index={i}
                value={name}
              />
            ))}
            {provided.placeholder}
          </GutterWrapper>
        )}
      </Droppable>
    );
  }
}

export class OtherOptions extends Component {
  render() {
    return (
      <Droppable droppableId={expressionType.others} direction="horizontal">
        {provided => (
          <GutterWrapper ref={provided.innerRef} {...provided.droppableProps}>
            {EXPRESSION_TYPE.others.map(({ value, type }, index) => {
              if (type === 'number') {
                return (
                  <ArithmeticDragFileds
                    className="manyun-btn manyun-btn-primary"
                    value={value}
                    index={index}
                    key={index}
                    draggableId={value}
                  />
                );
              }
              if (type === 'parentheses') {
                return (
                  <FunctionDragFileds
                    className="manyun-btn manyun-btn-primary"
                    key={type}
                    draggableId={type}
                    index={index}
                    value={null}
                  />
                );
              }
              return null;
            })}
            {provided.placeholder}
          </GutterWrapper>
        )}
      </Droppable>
    );
  }
}

export class ArithmeticDragFileds extends Component {
  render() {
    const { value, className, draggableId, index } = this.props;
    return (
      <Draggable draggableId={draggableId} index={index} key={draggableId}>
        {dragProvided => (
          <DragSpanWrapper
            ref={dragProvided.innerRef}
            className={className}
            {...dragProvided.draggableProps}
            {...dragProvided.dragHandleProps}
          >
            {value}
          </DragSpanWrapper>
        )}
      </Draggable>
    );
  }
}

function NumPopoverField({
  text,
  value,
  index,
  draggableId,
  className,
  expressionData,
  updateExpressionData,
}) {
  const [visible, setVisible] = useState(false);

  function onFieldTextOk(id, text) {
    let result = Array.from(expressionData);
    const newData = findNum(result, id, text);
    updateExpressionData(newData);
  }

  function findNum(data, id, text) {
    return data.map(item => {
      if (item.draggableId === id) {
        return {
          ...item,
          text: text,
        };
      } else {
        if (item.children && item.children.length) {
          findNum(item.children, id, text);
        }
        return item;
      }
    });
  }

  return (
    <Draggable
      draggableId={draggableId}
      index={index}
      key={draggableId}
      disableInteractiveElementBlocking={false}
    >
      {p => {
        return (
          <DragSpanWrapper
            ref={p.innerRef}
            {...p.draggableProps}
            {...p.dragHandleProps}
            key={draggableId}
          >
            <Popover
              visible={visible}
              title={value}
              content={
                <TextEditor
                  text={text}
                  onOk={txt => {
                    onFieldTextOk(draggableId, txt);
                    setVisible(false);
                  }}
                />
              }
              placement="topLeft"
            >
              <DragSpanWrapper
                className={className}
                onClick={() => {
                  setVisible(prevVisible => !prevVisible);
                }}
              >
                {text || value}
              </DragSpanWrapper>
            </Popover>
          </DragSpanWrapper>
        );
      }}
    </Draggable>
  );
}

const mapStateToProps = ({ mergedProcessesdPoint: { expressionData } }) => {
  return {
    expressionData,
  };
};
const mapDispatchToProps = {
  updateExpressionData: mergedProcessesdPointActions.updateExpressionData,
};

export const ConnectNumPopoverField = connect(mapStateToProps, mapDispatchToProps)(NumPopoverField);

function TextEditor({ onOk, text }) {
  const [txt, setTxt] = useState(text);

  return (
    <GutterWrapper flex>
      <InputNumber
        size="small"
        defaultValue={text}
        onChange={value => {
          setTxt(value);
        }}
      />
      <Button
        size="small"
        onClick={() => {
          onOk(txt);
        }}
      >
        确定
      </Button>
    </GutterWrapper>
  );
}

const getItemStyle = (isDragging, data) => {
  if (isDragging) {
    if (!data) {
      return {
        border: '1px solid red',
        width: '100px',
        height: '50px',
      };
    } else {
      return {
        border: '1px solid red',
        width: `${(data.length + 1) * 100}px`,
        height: '50px',
      };
    }
  }
};

export class FunctionDragFileds extends Component {
  render() {
    const { className, draggableId, index, value, data, closable, treeMode } = this.props;
    return (
      <Draggable draggableId={draggableId} index={index} disableInteractiveElementBlocking={false}>
        {dragProvided => (
          <DragSpanWrapper
            ref={dragProvided.innerRef}
            className={className}
            {...dragProvided.draggableProps}
            {...dragProvided.dragHandleProps}
            bordered={closable}
          >
            {value}({' '}
            <Droppable droppableId={'function-fileds_$$_' + draggableId} direction="horizontal">
              {(provided, snapshot) => (
                <DragSpanWrapper
                  ref={provided.innerRef}
                  {...provided.droppableProps}
                  minWidth={closable ? 300 : 0}
                  minHeight={closable ? 300 : 0}
                  style={getItemStyle(snapshot.isDraggingOver, data)}
                >
                  {Array.isArray(data) &&
                    data.length &&
                    data.map((item, index) => (
                      <ConditionalDraggable
                        key={item.draggableId}
                        {...item}
                        idx={index}
                        index={index}
                        treeMode={treeMode}
                      />
                    ))}
                  {provided.placeholder}
                </DragSpanWrapper>
              )}
            </Droppable>{' '}
            )
          </DragSpanWrapper>
        )}
      </Draggable>
    );
  }
}
