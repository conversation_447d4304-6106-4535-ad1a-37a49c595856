import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';

import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Typography } from '@manyun/base-ui.ui.typography';

// import {
//     mergedProcessesdPointActions,
// } from '@manyun/dc-brain.legacy.redux/actions/mergedProcessesdPointActions';

const formItemLayout = {
  labelCol: {
    xl: { span: 4 },
  },
  wrapperCol: {
    xl: { span: 20 },
  },
};

class PointValidate extends Component {
  handleOk = () => {};

  handleCancel = () => {};

  render() {
    const {
      form: { getFieldDecorator },
      isValidate,
    } = this.props;
    return (
      <Modal title="验证" visible={isValidate} onOk={this.handleOk} onCancel={this.handleCancel}>
        <Typography.Text disabled>请输入测点数值进行测点验证：</Typography.Text>
        <Form {...formItemLayout}>
          <Form.Item label="电压">
            {getFieldDecorator('username', {
              rules: [{ required: true, message: 'Please input your username!' }],
            })(<InputNumber />)}
          </Form.Item>
        </Form>
      </Modal>
    );
  }
}

const mapStateToProps = ({ mergedProcessesdPoint: { isValidate, expressionData } }) => {
  return {
    isValidate,
  };
};

export default connect(mapStateToProps, null)(Form.create({ name: 'validate' })(PointValidate));
