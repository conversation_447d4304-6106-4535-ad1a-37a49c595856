@import '~@manyun/base-ui.theme.theme/dist/theme.variable.less';

.border {
  border-color: @border-color-split;
}

.commonStyle {
  position: relative;
  display: block;
  width: 100%;
  overflow: auto;
  font-size: 14px;
  line-height: 1.5;
  border-left-width: 1px;
  border-right-width: 1px;
  border-style: solid;
  border-color: @border-color-split;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

.dropZoneWrapper {
  min-height: 300px;
  padding: 4px 11px 0;
  border-top-width: 1px;
  border-bottom-width: 0;

  > * + * {
    margin-left: 8px;
  }
}

.dropZoneActionWrapper {
  min-height: 32px;
  padding: 0 11px 4px;
  border-top-width: 0;
  border-bottom-width: 1px;
}
