import React, { useState } from 'react';
import { useParams } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { TreePoint } from '@manyun/resource-hub.ui.resource-point-drawer';
import { ResourcePointDrawer } from '@manyun/resource-hub.ui.resource-point-drawer';

export function PointDrawer({
  disabled = false,
  value = '测点',
  onChange,
}: {
  disabled: boolean;
  value: string;
  onChange: (value?: TreePoint) => void;
}) {
  const { idc } = useParams<{ idc: string }>();
  const [open, setOpen] = useState(false);
  return (
    <span>
      <Button
        style={{ width: 200 }}
        disabled={disabled}
        onClick={() => {
          setOpen(true);
        }}
      >
        <Tooltip title={value ?? '测点'}>
          <Typography.Text ellipsis>{value ?? '测点'}</Typography.Text>
        </Tooltip>
      </Button>
      <ResourcePointDrawer
        open={open}
        destroyOnClose
        maxCheckedCount={1}
        resourceTreeProps={{
          spaceGuid: idc,
          treeMode: ['IDC', 'BLOCK', 'ROOM', 'DEVICE', 'COLUMN', 'GRID'],
        }}
        onClose={() => setOpen(false)}
        onChange={treePoint => {
          onChange(treePoint?.length ? treePoint[0] : undefined);
        }}
      />
    </span>
  );
}
