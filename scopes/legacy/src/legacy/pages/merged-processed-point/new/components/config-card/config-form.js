import React from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import QuestionCircleOutlined from '@ant-design/icons/es/icons/QuestionCircleOutlined';

import { Radio } from '@manyun/base-ui.ui.radio';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';

import { mergedProcessesdPointActions } from '@manyun/dc-brain.legacy.redux/actions/mergedProcessesdPointActions';

import MergePoint from './merge-point';
import EhanceProcessedPointForm from './processed-point';

export function ConfigForm({
  form,
  pointTypeSelected,
  pointConfigRefMap,
  pressedPointRef,
  mode,
  draggableDescriptorMutator,
}) {
  const { getFieldDecorator } = form;
  return (
    <>
      {mode !== 'edit' && (
        <Form colon={false}>
          <Form.Item
            labelCol={{ xl: 3 }}
            wrapperCol={{ xl: 21 }}
            label={
              <span>
                测点分类&nbsp;
                <Tooltip
                  title={
                    <>
                      <span>聚合测点：以单测点按照合计操作符与范围统计</span>
                      <br />
                      <span>加工测点：自定义公式以新增某一种测点</span>
                      <br />
                      <span>若测点包含聚合与加工，请先聚合再进行加工</span>
                    </>
                  }
                >
                  <QuestionCircleOutlined />
                </Tooltip>
              </span>
            }
          >
            {getFieldDecorator('pointType')(
              <Radio.Group buttonStyle="solid">
                <Radio.Button value="MERGED">聚合测点</Radio.Button>
                <Radio.Button value="MACHINE">加工测点</Radio.Button>
              </Radio.Group>
            )}
          </Form.Item>
        </Form>
      )}

      {mode !== 'edit' && pointTypeSelected && pointTypeSelected.pointType.value === 'MERGED' && (
        <MergePoint pointConfigRefMap={pointConfigRefMap} />
      )}
      {pointTypeSelected && pointTypeSelected.pointType.value === 'MACHINE' && (
        <EhanceProcessedPointForm
          wrappedComponentRef={pressedPointRef}
          mode={mode}
          draggableDescriptorMutator={draggableDescriptorMutator}
        />
      )}
    </>
  );
}

const createOpts = {
  onFieldsChange(props, changedFields) {
    props.updateFormValues(changedFields);
  },
  mapPropsToFields(props) {
    return {
      pointType: Form.createFormField(props.pointTypeSelected.pointType),
    };
  },
};

const EhanceConfigForm = Form.create(createOpts)(ConfigForm);

const mapStateToProps = ({ mergedProcessesdPoint: { pointTypeSelected } }) => {
  return {
    pointTypeSelected,
  };
};
const mapDispatchToProps = {
  updateFormValues: mergedProcessesdPointActions.updatePointTypeSelectedFormValues,
};

export default connect(mapStateToProps, mapDispatchToProps)(EhanceConfigForm);
