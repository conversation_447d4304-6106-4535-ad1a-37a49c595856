import Form from '@ant-design/compatible/es/form';
import QuestionCircleOutlined from '@ant-design/icons/es/icons/QuestionCircleOutlined';
import cloneDeep from 'lodash/cloneDeep';
import difference from 'lodash/difference';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { TreeSelect } from '@manyun/base-ui.ui.tree-select';
import { generateValidLimitsDataSource } from '@manyun/monitoring.model.point';

import { TinyTable } from '@manyun/dc-brain.legacy.components';
import { SPACE_DEVICE_TYPE_KEY_MAP } from '@manyun/dc-brain.legacy.constants/enums/deviceType';
import { POINT_DATA_TYPE_CODE_MAP } from '@manyun/dc-brain.legacy.constants/point';
import { getDifferenceData } from '@manyun/dc-brain.legacy.pages/merged-processed-point/utils';
import { mergedProcessesdPointActions } from '@manyun/dc-brain.legacy.redux/actions/mergedProcessesdPointActions';

import {
  DIMENSION_ENUMS_SELECT_AI,
  DIMENSION_ENUMS_SELECT_DI,
  MERGE_DIRECTION_CODE_MAP,
} from '../../../../constants';
import NameDropped from './name-drop';

class MergeForm extends Component {
  onChangeCheckbox = ({ checked, record }) => {
    const { id, margedPointMaps } = this.props;
    let data = cloneDeep(margedPointMaps[id].statusMap);
    if (checked) {
      data = {
        ...data,
        [record.value]: record.label,
      };
    } else {
      delete data[record.value];
    }
    const newMaps = {
      ...margedPointMaps,
      [id]: {
        ...margedPointMaps[id],
        statusMap: data,
        statusList: {
          ...margedPointMaps[id].statusList,
          errors: undefined,
          dirty: true,
        },
      },
    };

    this.props.updataMergePointFormStatusMap(newMaps);
  };

  onSelectDimensionEnums = ({ value, record }) => {
    const { id, margedPointMaps } = this.props;
    const { disabledAggStatus } = margedPointMaps[id];
    const aggStatusMap = {};
    let tmp = [...value];
    if (disabledAggStatus && disabledAggStatus[record.value]) {
      tmp = [...tmp, ...disabledAggStatus[record.value]];
    }

    aggStatusMap[record.value] = [...new Set(tmp)];
    const newMaps = {
      ...margedPointMaps,
      [id]: {
        ...margedPointMaps[id],
        aggStatusMap: {
          ...margedPointMaps[id].aggStatusMap,
          ...aggStatusMap,
        },
        statusList: {
          ...margedPointMaps[id].statusList,
          errors: undefined,
          dirty: true,
        },
      },
    };
    this.props.updataMergePointFormStatusMap(newMaps);
  };

  loop = data => {
    const { singleInfo } = this.props;
    const list = data.map(item => {
      if (item.children) {
        let isdisabled = false;
        const disabledSingleArr = singleInfo.disabledDimensionEnums
          ? singleInfo.disabledDimensionEnums.filter(key => key.includes(item.value))
          : [];
        if (disabledSingleArr.length === item.children.length) {
          isdisabled = true;
        }
        return (
          <TreeSelect.TreeNode
            key={item.key}
            value={item.value}
            text={item.text}
            title={item.title}
            disabled={isdisabled}
          >
            {this.loop(item.children)}
          </TreeSelect.TreeNode>
        );
      }

      return (
        <TreeSelect.TreeNode
          key={item.key}
          value={item.value}
          text={item.text}
          title={item.title}
          disabled={singleInfo.disabledDimensionEnums.includes(item.value) ? true : false}
        />
      );
    });
    return list;
  };

  render() {
    const {
      form,
      id,
      margedPointMaps,
      pointInfoMap,
      statueList,
      singleInfo,
      pointType,
      selectCategoryPoint,
    } = this.props;
    const { getFieldDecorator } = form;
    const data = margedPointMaps[id];
    if (!data) {
      return null;
    }
    let singlePointInfo = {};
    if (data && data.dragValue && pointInfoMap[data.dragValue]) {
      singlePointInfo = pointInfoMap[data.dragValue];
    }
    let DIMENSION_ENUMS_SELECT = DIMENSION_ENUMS_SELECT_AI;
    if (singleInfo.isOnlySelectDevice) {
      DIMENSION_ENUMS_SELECT = DIMENSION_ENUMS_SELECT_AI.filter(
        ({ value }) => value === MERGE_DIRECTION_CODE_MAP.DEVICE
      );
    }
    // 原始点位 不能选device
    if (singlePointInfo && !singlePointInfo.dimension && !singlePointInfo.extCount) {
      DIMENSION_ENUMS_SELECT = DIMENSION_ENUMS_SELECT_AI.filter(
        ({ value }) => value !== MERGE_DIRECTION_CODE_MAP.DEVICE
      );
    }
    // 只能向上聚合
    if (
      Array.isArray(singleInfo.cannotSelectDimensionEnums) &&
      singleInfo.cannotSelectDimensionEnums.length
    ) {
      DIMENSION_ENUMS_SELECT = DIMENSION_ENUMS_SELECT_AI.filter(
        ({ value }) => !singleInfo.cannotSelectDimensionEnums.includes(value)
      );
    }

    if (selectCategoryPoint.deviceType === SPACE_DEVICE_TYPE_KEY_MAP.ROOM) {
      DIMENSION_ENUMS_SELECT = DIMENSION_ENUMS_SELECT_AI.filter(
        ({ value }) => value === MERGE_DIRECTION_CODE_MAP.ROOM
      );
    }

    if (selectCategoryPoint.deviceType === SPACE_DEVICE_TYPE_KEY_MAP.BLOCK) {
      DIMENSION_ENUMS_SELECT = DIMENSION_ENUMS_SELECT_AI.filter(
        ({ value }) => value === MERGE_DIRECTION_CODE_MAP.BLOCK
      );
    }

    if (selectCategoryPoint.deviceType === SPACE_DEVICE_TYPE_KEY_MAP.IDC) {
      DIMENSION_ENUMS_SELECT = DIMENSION_ENUMS_SELECT_AI.filter(
        ({ value }) => value === MERGE_DIRECTION_CODE_MAP.IDC
      );
    }

    if (selectCategoryPoint.deviceType === SPACE_DEVICE_TYPE_KEY_MAP.COLUMN) {
      DIMENSION_ENUMS_SELECT = DIMENSION_ENUMS_SELECT_AI.filter(
        ({ value }) => value === MERGE_DIRECTION_CODE_MAP.COLUMN
      );
    }

    if (selectCategoryPoint.deviceType === SPACE_DEVICE_TYPE_KEY_MAP.GRID) {
      DIMENSION_ENUMS_SELECT = DIMENSION_ENUMS_SELECT_AI.filter(
        ({ value }) => value === MERGE_DIRECTION_CODE_MAP.GRID
      );
    }

    return (
      <Form
        colon={false}
        style={{ width: '100%', paddingBottom: '0', marginBottom: '0' }}
        ref={this.props.forwardedRef}
      >
        <Row type="flex" align="middle">
          <Col xl={12}>
            <Form.Item
              label="聚合测点"
              labelCol={{ xxl: 8, xl: 9 }}
              wrapperCol={{ xxl: 16, xl: 15 }}
            >
              {getFieldDecorator('name', {
                rules: [{ required: true, message: '聚合测点名称为必填项' }],
              })(<NameDropped markId={id} pointType={pointType} style={{ width: 280 }} />)}
            </Form.Item>
          </Col>
          <Col xl={12}>
            {((singlePointInfo &&
              singlePointInfo.dataType &&
              singlePointInfo.dataType.code !== POINT_DATA_TYPE_CODE_MAP.DI) ||
              !singlePointInfo) && (
              <Form.Item
                label={
                  <span>
                    聚合维度&nbsp;
                    {data.disabledDimensionEnums && data.disabledDimensionEnums.length > 0 && (
                      <Tooltip
                        title={<span>不可选择的维度表示此测点已在该维度创建了聚合测点</span>}
                      >
                        <QuestionCircleOutlined />
                      </Tooltip>
                    )}
                  </span>
                }
                labelCol={{ xl: 5 }}
                wrapperCol={{ xl: 19 }}
              >
                {getFieldDecorator('dimensionEnums', {
                  rules: [
                    {
                      required: true,
                      validator: validateDimensionEnums(singleInfo, DIMENSION_ENUMS_SELECT),
                    },
                  ],
                })(
                  <TreeSelect
                    disabled={!data.name.value}
                    showSearch
                    treeNodeLabelProp="text"
                    treeCheckable={true}
                    maxTagCount={4}
                    getPopupContainer={() => document.querySelector('.manyun-list-item')}
                    notFoundContent={
                      DIMENSION_ENUMS_SELECT.length ? undefined : (
                        <span>机房维度下的加工测点无法被聚合</span>
                      )
                    }
                  >
                    {this.loop(DIMENSION_ENUMS_SELECT)}
                  </TreeSelect>
                )}
              </Form.Item>
            )}
          </Col>
        </Row>
        <div>
          {singlePointInfo &&
            singlePointInfo.dataType &&
            singlePointInfo.dataType.code === POINT_DATA_TYPE_CODE_MAP.DI && (
              <Form.Item>
                {getFieldDecorator('statusList', {
                  rules: [{ required: true, validator: validateChildItems(singleInfo) }],
                })(
                  <TinyTable
                    rowKey="value"
                    size="small"
                    bordered={false}
                    pagination={false}
                    columns={columns(this, singleInfo)}
                    dataSource={statueList}
                  />
                )}
              </Form.Item>
            )}
        </div>
      </Form>
    );
  }
}

const createOpts = {
  onFieldsChange(props, changedFields) {
    props.updateMergedPointSingleFormValues({ id: props.id, changedFields });
  },
  mapPropsToFields(props) {
    const { id, margedPointMaps, selectCategoryPoint } = props;
    const data = margedPointMaps[id];
    let newDimensionEnums = cloneDeep(data.dimensionEnums);
    if (selectCategoryPoint.deviceType === SPACE_DEVICE_TYPE_KEY_MAP.IDC) {
      // 过滤其他空间维度
      newDimensionEnums.value = data.dimensionEnums.value.filter(item => {
        if (item.indexOf(MERGE_DIRECTION_CODE_MAP.IDC) === -1) {
          return false;
        }
        return true;
      });
    }
    if (selectCategoryPoint.deviceType === SPACE_DEVICE_TYPE_KEY_MAP.BLOCK) {
      newDimensionEnums.value = data.dimensionEnums.value.filter(item => {
        if (item.indexOf(MERGE_DIRECTION_CODE_MAP.BLOCK) === -1) {
          return false;
        }
        return true;
      });
    }
    if (selectCategoryPoint.deviceType === SPACE_DEVICE_TYPE_KEY_MAP.ROOM) {
      newDimensionEnums.value = data.dimensionEnums.value.filter(item => {
        if (item.indexOf(MERGE_DIRECTION_CODE_MAP.ROOM) === -1) {
          return false;
        }
        return true;
      });
    }
    if (selectCategoryPoint.deviceType === SPACE_DEVICE_TYPE_KEY_MAP.COLUMN) {
      newDimensionEnums.value = data.dimensionEnums.value.filter(item => {
        if (item.indexOf(MERGE_DIRECTION_CODE_MAP.COLUMN) === -1) {
          return false;
        }
        return true;
      });
    }
    if (selectCategoryPoint.deviceType === SPACE_DEVICE_TYPE_KEY_MAP.GRID) {
      newDimensionEnums.value = data.dimensionEnums.value.filter(item => {
        if (item.indexOf(MERGE_DIRECTION_CODE_MAP.GRID) === -1) {
          return false;
        }
        return true;
      });
    }

    if (data) {
      return {
        name: Form.createFormField(data.name),
        dimensionEnums: Form.createFormField(newDimensionEnums),
        statusList: Form.createFormField(data.statusList),
      };
    }
  },
};

const EhancedMergeForm = Form.create(createOpts)(MergeForm);

const mapStateToProps = (
  {
    mergedProcessesdPoint: { margedPointMaps, pointInfoMap },
    common: { deviceCategory },
    doPointManage: { selectCategoryPoint },
  },
  { id }
) => {
  const data = margedPointMaps[id];
  let statueList = [];
  if (
    data &&
    pointInfoMap &&
    pointInfoMap[data.dragValue] &&
    deviceCategory &&
    deviceCategory.normalizedList
  ) {
    let statueOption = [];
    if (pointInfoMap[data.dragValue].validLimits) {
      statueOption = generateValidLimitsDataSource(pointInfoMap[data.dragValue].validLimits);
    }
    const [, deviceCode] = data.dragValue.split('_');
    const normalizedList = deviceCategory.normalizedList;
    const tmp = statueOption.map(item => {
      return {
        ...item,
        statusPointName: `${normalizedList[deviceCode]?.metaName}-${item.label}计数`,
      };
    });
    statueList = [...statueList, ...tmp];
  }
  return {
    margedPointMaps,
    pointInfoMap,
    statueList,
    singleInfo: data,
    selectCategoryPoint,
  };
};
const mapDispatchToProps = {
  updateMergedPointSingleFormValues: mergedProcessesdPointActions.updateMergedPointSingleFormValues,
  updataMergePointFormStatusMap: mergedProcessesdPointActions.updataMergePointFormStatusMap,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(React.forwardRef((props, ref) => <EhancedMergeForm forwardedRef={ref} {...props} />));

const columns = (ctx, singleInfo) => [
  {
    title: '是否创建',
    dataIndex: 'value',
    render: (_, record) => {
      let defaultChecked = false;
      let disabled = false;
      const { disabledAggStatus, statusMap } = singleInfo;
      if (disabledAggStatus && disabledAggStatus[record.value]) {
        defaultChecked = true;
        disabled = true;
      }

      // 判断 如果选中数据中有该状态，则显示选中
      const { value } = record;
      if (statusMap[value]) {
        defaultChecked = true;
      }

      return (
        <Checkbox
          checked={defaultChecked}
          disabled={disabled}
          onChange={e => ctx.onChangeCheckbox({ checked: e.target.checked, record })}
        ></Checkbox>
      );
    },
  },
  {
    title: '状态名称',
    dataIndex: 'label',
  },
  {
    title: '测点名称',
    dataIndex: 'statusPointName',
  },
  {
    title: getTitle(singleInfo),
    dataIndex: '',
    render: (_, record) => {
      const { statusMap, aggStatusMap, disabledAggStatus } = singleInfo;
      const { selectCategoryPoint } = ctx.props;
      const validationProps = { validateStatus: 'success' };
      let disabled = false;
      let selectData = DIMENSION_ENUMS_SELECT_DI;
      if (statusMap[record.value] && !aggStatusMap[record.value]) {
        validationProps.validateStatus = 'error';
        validationProps.help = '聚合维度为必选项！';
      }
      if (!statusMap[record.value]) {
        disabled = true;
      }
      if (singleInfo.isOnlySelectDevice) {
        selectData = DIMENSION_ENUMS_SELECT_DI.filter(value =>
          value.key.includes(MERGE_DIRECTION_CODE_MAP.DEVICE)
        );
      } else {
        selectData = DIMENSION_ENUMS_SELECT_DI.filter(
          value => !value.key.includes(MERGE_DIRECTION_CODE_MAP.DEVICE)
        );
      }
      if (selectCategoryPoint?.deviceType) {
        selectData = selectData.filter(item => item.type === selectCategoryPoint.deviceType);
      }
      return (
        <Form.Item {...validationProps} style={{ marginBottom: '0' }}>
          <TreeSelect
            size="small"
            disabled={disabled}
            treeCheckable={true}
            value={aggStatusMap[record.value]}
            style={{ width: 178 }}
            onChange={value => ctx.onSelectDimensionEnums({ value, record })}
          >
            {selectData.map(({ title, value, key }) => {
              return (
                <TreeSelect.TreeNode
                  value={value}
                  title={title}
                  key={key}
                  disabled={
                    disabledAggStatus &&
                    disabledAggStatus[record.value] &&
                    disabledAggStatus[record.value].includes(value)
                      ? true
                      : false
                  }
                />
              );
            })}
          </TreeSelect>
        </Form.Item>
      );
    },
  },
];

function validateChildItems(data) {
  const { statusMap, aggStatusMap, disabledAggStatus } = data;
  const keys = Object.keys(statusMap);
  const codes = Object.keys(aggStatusMap);
  const { differentStatus, differentAggStatus } = getDifferenceData(
    statusMap,
    aggStatusMap,
    disabledAggStatus
  );

  return (__, statusList, callback) => {
    if (!keys.length) {
      callback('至少选择一个状态！');
    } else if (keys.length && !codes.length) {
      callback(' ');
    } else if (!differentStatus && !differentAggStatus) {
      callback('请选择未被选择的状态或者未被聚合过的聚合维度！');
    } else {
      callback();
    }
  };
}

function validateDimensionEnums(data, DIMENSION_ENUMS_SELECT) {
  const dimensions = DIMENSION_ENUMS_SELECT[0]
    ? DIMENSION_ENUMS_SELECT[0].children.map(({ value }) => value)
    : [];
  return (__, dimensionEnums, callback) => {
    let value = [];
    if (data.disabledDimensionEnums.length) {
      value = difference(dimensionEnums, data.disabledDimensionEnums);
      const differenceData = difference(dimensions, data.disabledDimensionEnums);
      if (value.length) {
        callback();
      } else if (differenceData.length === 0) {
        callback('该测点的所有聚合维度都已被聚合，不可以聚合该测点！');
      } else {
        callback('请选择没有聚合过的聚合维度！');
      }
    } else {
      if (dimensionEnums.length) {
        callback();
      } else {
        callback('聚合维度为必选项！');
      }
    }
  };
}

function getTitle(data) {
  const keys = data.disabledAggStatus ? Object.keys(data.disabledAggStatus) : [];
  if (keys.length) {
    return (
      <span>
        聚合维度&nbsp;
        <Tooltip title={<span>不可选择的维度表示此测点已在该维度创建了聚合测点</span>}>
          <QuestionCircleOutlined />
        </Tooltip>
      </span>
    );
  }
  return '聚合维度';
}
