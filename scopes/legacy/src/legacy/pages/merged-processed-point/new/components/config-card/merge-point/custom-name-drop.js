import React, { Component } from 'react';
import { connect } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import { ResourcePointDrawer } from '@manyun/resource-hub.ui.resource-point-drawer';
import { getSpaceGuid, getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

import {
  changeExpressInfos,
  mergedProcessesdPointActions,
} from '@manyun/dc-brain.legacy.redux/actions/mergedProcessesdPointActions';

import { getFormulaName } from './utils';

class CustomNameDropped extends Component {
  state = {
    open: false,
  };

  render() {
    const {
      markId,
      pointInfoMap,
      id,
      expressionData,
      dataType,
      statusList,
      name,
      customSpaceGuid,
      updateFormulaName,
    } = this.props;
    const { idc, block } = getSpaceGuidMap(customSpaceGuid);
    const spaceGuid = getSpaceGuid(idc, block);

    const { open } = this.state;

    const onChange = points => {
      if (!points?.length) {
        return;
      }
      const pointInfo = points[0];
      let value = '';
      let label = '';

      if (pointInfo.dimension?.code === 'DEVICE') {
        value = `K_${pointInfo.target.device.id}_${pointInfo.code}_${pointInfo.deviceType}`;
        label = `${pointInfo.target.device.spaceGuid.roomGuid.replace(/\./g, '_')}_${
          pointInfo.target.device.name
        }_${pointInfo.name}`;
      } else {
        value = `K_${transformToKSpaceGuid(pointInfo.spaceGuid)}_${pointInfo.code}_${
          pointInfo.deviceType
        }`;
        label = `${pointInfo.spaceGuid.replace(/\./g, '_')}_${pointInfo.name}`;
      }

      const parseResult = parseExpressionValue(value);
      const pointInfoMapKey = `${parseResult.deviceType}_${parseResult.pointCode}`;

      if (dataType === 'DI') {
        const newStatusList = statusList.value.map(item => {
          if (item.markedID === markId) {
            return {
              ...item,
              name: label,
              value,
              validLimits:
                pointInfoMap[pointInfoMapKey] &&
                pointInfoMap[pointInfoMapKey].validLimits &&
                Array.isArray(pointInfoMap[pointInfoMapKey].validLimits)
                  ? pointInfoMap[pointInfoMapKey].validLimits.map(item => {
                      const [value, label] = item.split('=');
                      return {
                        value,
                        label,
                      };
                    })
                  : [],
            };
          }
          return item;
        });
        this.props.updateStatusList(newStatusList);
        this.setState({ spaceTreeData: { label, value } });

        return;
      }
      let point = value;
      if (value.includes('$$')) {
        point = `${value.split('$$')[0]}_${value.split('$$')[1].split('_').slice(1).join('_')}`;
      }
      const name = label;
      this.setState({ spaceTreeData: { label, value } });
      const newData = setPointDroppableValue(expressionData, id, point, name);
      updateFormulaName({ formulaName: getFormulaName(newData) });
      this.props.updateExpressionData(newData);
    };

    return (
      <span>
        <Button style={{ width: 200 }} onClick={() => this.setState({ open: true })}>
          <Tooltip title={name}>
            <Typography.Text ellipsis>{name}</Typography.Text>
          </Tooltip>
        </Button>

        {spaceGuid && (
          <ResourcePointDrawer
            open={open}
            destroyOnClose
            maxCheckedCount={1}
            resourceTreeProps={{
              spaceGuid,
              treeMode:
                spaceGuid?.split('.').length === 1
                  ? ['IDC', 'BLOCK', 'ROOM', 'DEVICE', 'COLUMN', 'GRID']
                  : ['BLOCK', 'ROOM', 'DEVICE', 'COLUMN', 'GRID'],
            }}
            onClose={() => this.setState({ open: false })}
            onChange={onChange}
          />
        )}
      </span>
    );
  }
}

const mapStateToProps = ({
  common: { deviceCategory },
  'resource.spaces': { entities, codes },
  mergedProcessesdPoint: {
    margedPointIds,
    margedPointMaps,
    pointInfoMap,
    expressionData,
    processedPointList: { statusList, spaceGuid },
    processedSelectedPoint,
    customSpaceGuid,
  },
  doPointManage: { selectCategoryPoint },
}) => {
  const spaceEntities = {};
  const flatSpaces = [];
  Object.keys(entities).forEach(code => {
    const entity = entities[code];
    if (entity.metaStyle === 'VIRTUAL_BLOCK' && entity.type === 'BLOCK') {
      return;
    }
    spaceEntities[code] = {
      ...entity,
      metaCode: entity.code,
      metaName: entity.name,
      metaType: entity.type,
    };
    flatSpaces.push(spaceEntities[code]);
  });

  return {
    spaceEntities,
    /** 用于空间测点的可拖拽组件 */
    flatSpaces: flatSpaces,

    deviceCategory: deviceCategory
      ? { ...deviceCategory, treeList: deviceCategory.treeList.filter(({ numbered }) => numbered) }
      : null,
    margedPointIds,
    margedPointMaps,
    selectCategoryPoint,
    expressionData,
    pointInfoMap,
    statusList,
    spaceGuid,
    processedSelectedPoint,
    customSpaceGuid,
  };
};
const mapDispatchToProps = {
  deleteMergedPointInForm: mergedProcessesdPointActions.deleteMergedPointInForm,
  savePointInfoMap: mergedProcessesdPointActions.savePointInfoMap,
  changeExpressInfos,
  updateExpressionData: mergedProcessesdPointActions.updateExpressionData,
  updateStatusList: mergedProcessesdPointActions.updateStatusList,
  addPointInStatus: mergedProcessesdPointActions.addPointInStatus,
  updateFormulaName: mergedProcessesdPointActions.updateFormulaName,
};
export default connect(mapStateToProps, mapDispatchToProps, null, { forwardRef: true })(
  CustomNameDropped
);

function setPointDroppableValue(expressionData, id, value, name) {
  return expressionData.map(item => {
    if (item.id === id) {
      return {
        ...item,
        value,
        name,
      };
    }
    if (item.children && item.children.length) {
      return {
        ...item,
        children: setPointDroppableValue(item.children, id, value, name),
      };
    }
    return item;
  });
}

const parseExpressionValue = value => {
  if (value && typeof value === 'string') {
    const parseResultList = value.split('_');
    const parseResultListLength = parseResultList.length;
    return {
      pointCode: parseResultList[parseResultListLength - 2],
      deviceType: parseResultList[parseResultListLength - 1],
    };
  } else {
    return null;
  }
};

/**
 * 将一般的空间 GUID 转换成后端需要的表达式中的 GUID
 *
 * 1. "." 替换为 "#"
 * 2. "-" 替换为 "@"
 *
 * @param {string} spaceGuid 一般的空间 GUID，以 "." 分隔
 */
function transformToKSpaceGuid(spaceGuid) {
  return spaceGuid.replace(/\./g, '#').replace(/-/g, '@').replace(/_/g, '$');
}
