export function getDisabledData(pointData, selectedPoints) {
  return pointData.map(item => {
    if (!selectedPoints || !selectedPoints.length) {
      return item;
    }
    if (item.children && item.children.length) {
      return {
        ...item,
        children: getDisabledData(item.children, selectedPoints),
      };
    }
    if (item.code && selectedPoints.includes(item.value)) {
      return {
        ...item,
        disabled: true,
      };
    }
    return item;
  });
}

export function getTreeSelectValue(value, pointInfoMap) {
  if (value && value.label) {
    return value;
  }

  if (!value) {
    return undefined;
  }

  let label = '';
  if (pointInfoMap[value]) {
    label = pointInfoMap[value].name;
  }

  return {
    key: value,
    label,
  };
}

export function getCustomTreeSelectValue(value, pointInfoMap) {
  if (!value) {
    return { value, label: value };
  }
  const [, , pointCode, deviceType] = value.split('_');
  let label = '';
  if (pointInfoMap[`${deviceType}_$$_${pointCode}`]) {
    label = pointInfoMap[`${deviceType}_$$_${pointCode}`].name;
  }

  return {
    value,
    label,
  };
}

export function getFormulaName(expressionData, title) {
  let flatTitle = '';
  expressionData?.forEach(item => {
    if (Array.isArray(item.children)) {
      flatTitle = flatTitle + `${item.name}(${getFormulaName(item.children, title)})`;
    } else if ((item.type === 'device-property' || item.type === 'point') && title) {
      flatTitle = flatTitle + `${title}_${item.name}`;
    } else {
      flatTitle = flatTitle + item.name;
    }
  });
  return flatTitle;
}
