import React, { Component } from 'react';
import { Draggable } from 'react-beautiful-dnd';
import { connect } from 'react-redux';

import CloseOutlined from '@ant-design/icons/es/icons/CloseOutlined';

import { Button } from '@manyun/base-ui.ui.button';

import { mergedProcessesdPointActions } from '@manyun/dc-brain.legacy.redux/actions/mergedProcessesdPointActions';

import { ConnectNumPopoverField, FunctionDragFileds } from './droppable-component';
import { DragSpanWrapper } from './styled';

class ConditionalDraggableContent extends Component {
  handleDeleta = draggableId => {
    const { expressionData } = this.props;
    const newData = this.findData(expressionData, draggableId);
    this.props.updateExpressionData(newData);
  };

  findData = (data, id) => {
    return data
      .map(item => {
        if (item.draggableId === id) {
          return null;
        } else {
          if (item.children && item.children.length) {
            return { ...item, children: this.findData(item.children, id) };
          }
          return item;
        }
      })
      .filter(Boolean);
  };

  render() {
    const { draggableId, index, name, value, type, text, children } = this.props;
    if (type === 'statistical-function') {
      return (
        <FunctionDragFileds
          key={draggableId}
          className="manyun-btn manyun-btn-default"
          draggableId={draggableId}
          index={index}
          value={name}
          data={children}
          closable={true}
        />
      );
    }

    if (type === 'delimiter') {
      return <span>{' ' + value + ' '}</span>;
    }

    if (value === 'number') {
      return (
        <ConnectNumPopoverField
          index={index}
          text={text}
          value={name}
          draggableId={draggableId}
          className="manyun-btn manyun-btn-primary"
        />
      );
    }

    if (value === 'parentheses') {
      return (
        <FunctionDragFileds
          key={draggableId}
          className="manyun-btn manyun-btn-default"
          draggableId={draggableId}
          index={index}
          value={null}
          data={children}
          closable={true}
        />
      );
    }

    const deleteOption = (
      <Button
        type="link"
        icon={<CloseOutlined />}
        onClick={() => this.handleDeleta(draggableId)}
        style={{ width: '20px', height: '20px', fontSize: '12px' }}
      />
    );

    return (
      <Draggable
        draggableId={draggableId}
        index={index}
        key={draggableId}
        disableInteractiveElementBlocking={false}
      >
        {p => {
          return (
            <DragSpanWrapper
              key={draggableId}
              ref={p.innerRef}
              {...p.draggableProps}
              {...p.dragHandleProps}
              className="manyun-btn manyun-btn-primary"
            >
              {name || value}
              {deleteOption}
            </DragSpanWrapper>
          );
        }}
      </Draggable>
    );
  }
}

const mapStateToProps = ({ mergedProcessesdPoint: { expressionData } }) => {
  return {
    expressionData,
  };
};

const mapDispatchToProps = {
  updateExpressionData: mergedProcessesdPointActions.updateExpressionData,
};

export const ConditionalDraggable = connect(
  mapStateToProps,
  mapDispatchToProps
)(ConditionalDraggableContent);
