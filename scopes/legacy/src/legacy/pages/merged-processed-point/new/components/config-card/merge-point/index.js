import React, { Component } from 'react';
import { connect } from 'react-redux';

import PlusOutlined from '@ant-design/icons/es/icons/PlusOutlined';

import { Button } from '@manyun/base-ui.ui.button';
import { List } from '@manyun/base-ui.ui.list';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';
import { mergedProcessesdPointActions } from '@manyun/dc-brain.legacy.redux/actions/mergedProcessesdPointActions';

import CueModal from './cue-modal';
import EhancedMergeForm from './form';
import { StyledList } from './styled';

class MergePoint extends Component {
  componentWillUnmount() {
    //清空聚合测点信息
    this.props.resetCreateMergedPoints();
  }

  _newRowHandler = () => {
    this.props.addMergedPointForm();
  };

  _deleteRowByIdx = id => {
    this.props.deleteMergedPointInForm({ id: id, type: 'all' });
  };

  loadMore() {
    return (
      <Button type="link" onClick={this._newRowHandler}>
        <PlusOutlined />
        添加聚合测点
      </Button>
    );
  }

  render() {
    const { margedPointIds, pointConfigRefMap, pointType } = this.props;
    return (
      <GutterWrapper mode="vertical">
        <StyledList
          itemLayout="horizontal"
          dataSource={margedPointIds}
          loadMore={this.loadMore()}
          locale={{
            emptyText: (
              <div className="has-error">
                <span className="manyun-form-explain">至少添加一个聚合测点！</span>
              </div>
            ),
          }}
          renderItem={(id, index) => {
            return (
              <List.Item
                actions={[
                  <Button
                    style={{ marginBottom: 24 }}
                    type="link"
                    key="delete"
                    onClick={() => this._deleteRowByIdx(id)}
                  >
                    删除
                  </Button>,
                ]}
              >
                <EhancedMergeForm
                  id={id}
                  wrappedComponentRef={pointConfigRefMap.get(id)}
                  pointType={pointType}
                />
              </List.Item>
            );
          }}
        />

        <CueModal />
      </GutterWrapper>
    );
  }
}

const mapStateToProps = ({ mergedProcessesdPoint: { margedPointIds, margedPointMaps } }) => {
  return {
    margedPointIds,
    margedPointMaps,
  };
};
const mapDispatchToProps = {
  addMergedPointForm: mergedProcessesdPointActions.addMergedPointForm,
  deleteMergedPointInForm: mergedProcessesdPointActions.deleteMergedPointInForm,
  resetCreateMergedPoints: mergedProcessesdPointActions.resetCreateMergedPoints,
};

export default connect(mapStateToProps, mapDispatchToProps)(MergePoint);
