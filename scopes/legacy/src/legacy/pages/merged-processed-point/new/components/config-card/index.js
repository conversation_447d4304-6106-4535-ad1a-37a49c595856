import React, { Component } from 'react';
import { connect } from 'react-redux';

import { But<PERSON> } from '@manyun/base-ui.ui.button';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';

import { Footer<PERSON>ool<PERSON><PERSON>, GutterWrapper, TinyCard } from '@manyun/dc-brain.legacy.components';
import { MERGES_PROCESSED_POINT_DETAIL } from '@manyun/dc-brain.legacy.constants/urls';
import {
  createMergedPoint,
  createPressedPoint,
  updatePressedPointActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/mergedProcessesdPointActions';
import { redirectActionCreator } from '@manyun/dc-brain.legacy.redux/actions/routerActions';

import ConfigForm from './config-form';

function throws(error) {
  throw new Error(error);
}

class ConfigCard extends Component {
  state = {
    _pointConfigRefMap: new Map(),
  };

  _pressedPointRef = React.createRef();

  componentDidUpdate(prevProps) {
    const { margedPointIds } = this.props;

    if (
      margedPointIds.length !== prevProps.margedPointIds.length ||
      margedPointIds.some(id => !prevProps.margedPointIds.includes(id))
    ) {
      const newMap = new Map(margedPointIds.map(id => [id, React.createRef()]));
      this.setState({
        _pointConfigRefMap: newMap,
      });
    }
  }

  finish = async (__, operatorNotes) => {
    const { pointType, mode, paramsData } = this.props;
    if (mode === 'edit') {
      try {
        await this._pressedPointRef.current.props.form.validateFields().catch(throws);

        const result = await new Promise(resolve => {
          this.props.updatePressedPoint({
            data: {
              deviceType: paramsData.deviceType,
              pointCode: paramsData.pointCode,
              operatorNotes,
            },
            successCb: () => {
              resolve(true);
            },
            errorCb: () => {
              resolve(false);
            },
          });
        });

        return result;
      } catch (error) {}
    } else {
      if (pointType === 'MERGED') {
        try {
          if (this.state._pointConfigRefMap.size <= 0) {
            throws('至少添加一个测点！');
          }
          for (const [, pointConfigRef] of this.state._pointConfigRefMap) {
            if (pointConfigRef.current) {
              await pointConfigRef.current.props.form.validateFields().catch(throws);
            }
          }
          this.props.createMergedPoint();
        } catch (error) {}
      } else {
        try {
          await this._pressedPointRef.current.props.form.validateFields().catch(throws);
          this.props.createPressedPoint();
        } catch (error) {}
      }
    }
  };

  render() {
    const { height, mode, draggableDescriptorMutator } = this.props;
    const submitBtn = (
      <Button type="primary" onClick={this.finish}>
        提交
      </Button>
    );

    return (
      <TinyCard
        title={'  '}
        bodyStyle={{
          height,
          overflowY: 'auto',
          paddingBottom: 0,
        }}
      >
        <ConfigForm
          pointConfigRefMap={this.state._pointConfigRefMap}
          pressedPointRef={this._pressedPointRef}
          mode={mode}
          draggableDescriptorMutator={draggableDescriptorMutator}
        />
        <FooterToolBar>
          <GutterWrapper>
            {mode === 'edit' ? (
              <DeleteConfirm
                title="请输入修改此测点的原因"
                reasonPlaceholder=""
                onOk={async ({ reason }) => await this.finish(null, reason)} // expect the 1st param is HTML DOM Event.
              >
                {submitBtn}
              </DeleteConfirm>
            ) : (
              submitBtn
            )}
            <Button
              onClick={() => {
                this.props.redirect(MERGES_PROCESSED_POINT_DETAIL);
              }}
            >
              取消
            </Button>
          </GutterWrapper>
        </FooterToolBar>
      </TinyCard>
    );
  }
}

const mapStateToProps = ({
  mergedProcessesdPoint: {
    margedPointIds,
    pointTypeSelected: { pointType },
  },
}) => {
  return {
    margedPointIds,
    pointType: pointType.value,
  };
};
const mapDispatchToProps = {
  createMergedPoint,
  createPressedPoint,
  updatePressedPoint: updatePressedPointActionCreator,
  redirect: redirectActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(ConfigCard);
