import Form from '@ant-design/compatible/es/form';
import DeleteOutlined from '@ant-design/icons/es/icons/DeleteOutlined';
import { Select } from '@galiojs/awesome-antd';
import cloneDeep from 'lodash.clonedeep';
import isNil from 'lodash.isnil';
import debounce from 'lodash/debounce';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { Radio } from '@manyun/base-ui.ui.radio';
import { fetchDeviceByGuid } from '@manyun/resource-hub.service.fetch-device-by-guid';
import { fetchPoints } from '@manyun/resource-hub.service.fetch-points';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

import { METADATA_TYPE } from '@manyun/dc-brain.legacy.constants';
import { SPACE_DEVICE_TYPE_KEY_MAP } from '@manyun/dc-brain.legacy.constants/enums/deviceType';
import {
  POINT_DATA_TYPE_CODE_MAP,
  POINT_PRECISION,
  POINT_TYPE_CODE_MAP,
} from '@manyun/dc-brain.legacy.constants/point';
import { getMetadataByType } from '@manyun/dc-brain.legacy.redux/actions/eventCenterActions';
import {
  getPointDetail,
  mergedProcessesdPointActions,
  testExprActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/mergedProcessesdPointActions';
import { flattenTreeData } from '@manyun/dc-brain.legacy.utils';
import { getSpaceDeviceTypeIfNeeded } from '@manyun/dc-brain.legacy.utils/deviceType';

import TestModalButton from '../../../../components/test-modal-button';
import ValidLimits from '../../../../components/valid-limits-inputNumber';
import { EXPR_ITEM_TYPE, TargetPointType } from '../../../../constants';
import { validateExpr } from '../../../../utils';
import NameDropped from '../merge-point/name-drop';
import ExprEditor from './../../../../components/expr-editor';
import { HandlerPointDrawer } from './handler-point-drawer';
import { PointDrawer } from './point-drawer';

const statusErrors = {
  repeat: '不可重复！',
  blank: '状态名称必填！',
};
class ProcessedPointForm extends Component {
  state = {
    currentMaxPointCode: 0,
    deviceType: null,
    customIdcTag: '',
    targetPointType: 'DEVICE',
  };

  componentDidMount() {
    this.setDefaultSpaceGuid();
    this.setDefaultMaxPointCode();
    this.props.resetCustomPointExpr();
    this.props.updatePointType(this.props.processedType);
  }

  setDefaultSpaceGuid = () => {
    const { selectedTreeNode, selectMetaType, form, treeMode } = this.props;

    if (treeMode === 'custom') {
      if (selectMetaType === TargetPointType.DEVICE) {
        fetchDeviceByGuid({ guid: selectedTreeNode }).then(({ error, data }) => {
          if (error) {
            message.error(error.message);
            return;
          }

          if (data) {
            this.setState({
              deviceType: data.deviceCategory.level3,
              customIdcTag: data.spaceGuid.idcTag,
            });
            this.props.updateCustomSpaceGuid({
              customSpaceGuid: `${data.spaceGuid.roomGuid}.${data.guid}`,
            });
          }
        });

        form.setFieldsValue({
          spaceGuid: selectedTreeNode,
          targetPointType: TargetPointType.DEVICE,
        });

        return;
      }

      let spaceGuid = [];
      let deviceType = null;
      if (selectedTreeNode) {
        spaceGuid = selectedTreeNode.split('.');
      }
      if (spaceGuid.length === 1) {
        deviceType = '90101';
      }
      if (spaceGuid.length === 2) {
        deviceType = '90102';
      }
      if (spaceGuid.length === 3) {
        deviceType = '90103';
      }
      this.setState({ deviceType, customIdcTag: spaceGuid.length ? spaceGuid[0] : '' });
      form.setFieldsValue({ spaceGuid, targetPointType: TargetPointType.SPACE });
      this.props.updateCustomSpaceGuid({ customSpaceGuid: spaceGuid.join('.') });
    }
  };

  //设置测点序号最大
  setDefaultMaxPointCode = async (dataType = 'AI') => {
    const { treeMode, selectedTreeNode, selectMetaType, codes } = this.props;

    const { error, data } = await fetchPoints(
      generateFetchConditionParams({ treeMode, selectedTreeNode, codes, selectMetaType, dataType })
    );
    if (error) {
      message.error(error.message);
      return;
    }
    if (data.data.length) {
      this.setState({
        currentMaxPointCode: data.data.map(item => item.code).sort((pre, post) => post - pre)[0],
      });
      return;
    }
    this.setState({
      currentMaxPointCode: 1,
    });
  };

  componentWillUnmount() {
    this.props.resetItems();
  }

  addStatus = () => {
    this.props.addStausInStatusList();
  };

  debouncedHandleInputDevices = debounce(value => {
    if (value === '') {
      return;
    }
    this.props.searchDeviceSN({ tag: value });
  }, 500);

  deleteWithSameStatus = row => {
    this.props.deleteSameLabel(row.label);
  };

  deleteSinglePoint = idx => {
    this.props.deletePressedStatusListSinglePoint(idx);
  };

  getSpecUnit = () => {
    if (this.props.specUnitList.length) {
      return false;
    }
    this.props.getMetadataByType(METADATA_TYPE.SPEC_UNIT);
  };

  judeStatusListIsValidate = () => {
    const {
      processedPointList: { statusList },
    } = this.props;
    return statusList.validate;
  };

  setStatusListFormItemvValidateStatus = () => {
    if (!this.judeStatusListIsValidate()) {
      return 'success';
    }
    const { statusList } = this.props;
    const dataIsCorrect = statusList.find(
      ({ dataType, name, status, validLimits }) =>
        !dataType || !name || !status || !validLimits || !validLimits.length
    );
    if (dataIsCorrect) {
      return 'error';
    }
    return 'success';
  };

  isParameterPointDraggable = (targetPointType, treeMode) => {
    const { info } = this.props;

    if (targetPointType === TargetPointType.DEVICE && treeMode !== 'custom') {
      return true;
    } else {
      if (
        info.deviceType ===
        (SPACE_DEVICE_TYPE_KEY_MAP.ROOM &&
          SPACE_DEVICE_TYPE_KEY_MAP.COLUMN &&
          SPACE_DEVICE_TYPE_KEY_MAP.GRID)
      ) {
        return true;
      } else {
        return false;
      }
    }
  };

  getFields() {
    const {
      form: { getFieldDecorator, getFieldsValue },
      info,
      digitalNum,
      digitalNumToArr,
    } = this.props;
    const children = [];
    const validLimits = info.validLimits;
    const digitalDescJson = {};

    if (validLimits !== null && validLimits !== undefined) {
      validLimits.forEach(item => {
        const arr = item.split('=');
        digitalDescJson[arr[0]] = arr[1];
      });
    }

    for (let i = 0; i < (digitalNum ?? getFieldsValue().digitalNum); i++) {
      children.push(
        <Col key={i} span={4}>
          <Form.Item>
            {getFieldDecorator(`${i}`, {
              rules: [
                {
                  required: true,
                  validator: ({ filed }, value, callback) => {
                    if (!value || !value.trim()) {
                      callback(statusErrors.blank);
                      return;
                    }
                    const statusFiledsValues = getFieldsValue();
                    const filedsValueArr = digitalNumToArr.map(item => ({
                      name: item,
                      value: statusFiledsValues[item],
                    }));
                    const tmp = judgeStatusIsRepeated(filedsValueArr, { name: filed, value });
                    if (tmp) {
                      callback(statusErrors.repeat);
                      return;
                    }
                    callback();
                  },
                },
                { max: 8, message: '最多输入 8 个字符！' },
              ],
              initialValue: digitalDescJson[i],
            })(<Input addonBefore={<span>{i}</span>} width={200} />)}
          </Form.Item>
        </Col>
      );
    }
    return children;
  }

  onDeviceSelect = (_, option) => {
    const {
      deviceCategory: { level3 },
      spaceGuid: { idcTag, roomGuid },
      guid,
    } = option;
    this.props.updateCustomSpaceGuid({ customSpaceGuid: `${roomGuid}.${guid}` });
    this.props.updateProcessedDeviceInfo({ processedDeviceInfo: option });
    this.props.form.setFieldsValue({ name: null });
    this.setState({ deviceType: level3, customIdcTag: idcTag });
  };

  onSelectPoint = value => {
    if (value) {
      const {
        spaceGuid,
        deviceType,
        target: { type, device },
      } = value;
      const { idc } = getSpaceGuidMap(spaceGuid);

      this.setState({ deviceType, customIdcTag: idc });

      if (type === 'DEVICE') {
        this.setState({ targetPointType: 'DEVICE' });

        this.props.updateCustomSpaceGuid({ customSpaceGuid: spaceGuid });
        this.props.updateProcessedDeviceInfo({ processedDeviceInfo: device });
      } else {
        this.setState({ targetPointType: 'SPACE' });
        this.props.updateCustomSpaceGuid({ customSpaceGuid: spaceGuid });
        this.props.resetCustomPointExpr();
      }
      /*原选择测点时操作*/
      const copyPoint = cloneDeep(value);

      this.props.updateProcessedSelectedPoint({
        processedSelectedPoint: {
          ...copyPoint,
          spaceGuid: type === 'DEVICE' ? device.guid : spaceGuid.split('.'),
          targetPointType: type === 'DEVICE' ? 'DEVICE' : 'SPACE',
        },
      });
      if (value.code && value.deviceType) {
        const params = {
          deviceType: value.deviceType,
          pointCode: value.code,
          mode: 'create',
          treeMode: this.props.treeMode,
          isTemplate: true,
        };
        this.props.getPointDetail(params);
      }
    }
  };

  render() {
    const {
      form,
      exprActiveDroppableId,
      exprPoints,
      testExprResult,
      processedPointList,
      customSpaceGuid,
      statusList,
      mode,
      onExprActiveHandleClick,
      onExprNumberChange,
      onExprRemoveItem,
      onTestExpr,
      validateParams,
      specUnitList,
      treeMode,
      draggableDescriptorMutator,
      info,
    } = this.props;

    const { currentMaxPointCode, customIdcTag } = this.state;
    //targetPointType
    const { getFieldDecorator, getFieldValue } = form;

    const targetPointType = getFieldValue('targetPointType');
    const statusListFiledIsTouched = this.judeStatusListIsValidate();
    const { isParameterPointDraggable } = this;
    const digitalNum = getFieldValue('digitalNum');

    return (
      <Form colon={false} autoComplete="off">
        {treeMode === 'custom' && (
          <Form.Item label="测点名称" labelCol={{ xl: 3 }} wrapperCol={{ xl: 21 }}>
            {getFieldDecorator('name', {
              rules: [
                {
                  required: true,
                  message: '测点名称为必填项',
                },
              ],
            })(<PointDrawer disabled={mode === 'edit'} onChange={this.onSelectPoint} />)}
          </Form.Item>
        )}

        {treeMode !== 'custom' && (
          <Form.Item label="测点名称" labelCol={{ xl: 3 }} wrapperCol={{ xl: 21 }}>
            {getFieldDecorator('name', {
              rules: [
                {
                  required: true,
                  message: '测点名称为必填项',
                },
                {
                  max: 32,
                  message: '最多输入 32 个字符！',
                },
              ],
            })(<Input allowClear style={{ width: 468 }} />)}
          </Form.Item>
        )}

        <Form.Item label="测点类型" labelCol={{ xl: 3 }} wrapperCol={{ xl: 21 }}>
          {getFieldDecorator('dataType', {
            rules: [
              {
                required: true,
                message: '测点类型为必选项',
              },
            ],
          })(
            <Select
              disabled={
                mode === 'edit' || treeMode === 'custom'
                //空间类型可以选择状态量||processedPointList.pointType.value === POINT_TYPE_CODE_MAP.CAL_SPACE
              }
              style={{ width: 200 }}
              onChange={value => {
                this.setDefaultMaxPointCode(value);
              }}
            >
              {[
                { label: '模拟量读点', value: POINT_DATA_TYPE_CODE_MAP.AI },
                { label: '状态量读点', value: POINT_DATA_TYPE_CODE_MAP.DI },
              ].map(item => {
                return (
                  <Select.Option
                    key={item.value}
                    value={item.value}
                    label={item.label}
                    disabled={
                      processedPointList.pointType.value === POINT_TYPE_CODE_MAP.CAL_SPACE &&
                      item === POINT_DATA_TYPE_CODE_MAP.DI
                    }
                  >
                    {item.label}
                  </Select.Option>
                );
              })}
            </Select>
          )}
        </Form.Item>
        {mode === 'create' && (
          <Form.Item label="测点序号" labelCol={{ xl: 3 }} wrapperCol={{ xl: 21 }}>
            {getFieldDecorator('code', {
              rules: [
                { required: true, message: '请输入测点序号' },

                {
                  type: 'number',
                  max: 999,
                  min: 1,
                  message: '请输入3位正整数',
                },
              ],
              initialValue: formatPointCode(currentMaxPointCode),
            })(<InputNumber style={{ width: 70 }} disabled={treeMode === 'custom'} />)}
          </Form.Item>
        )}
        {processedPointList.dataType.value === POINT_DATA_TYPE_CODE_MAP.AI && (
          <>
            <Form.Item label="测点单位" labelCol={{ xl: 3 }} wrapperCol={{ xl: 21 }}>
              {getFieldDecorator('unit', {
                rules: [
                  {
                    required: false,
                    message: '测点单位为必填项',
                  },
                ],
              })(
                <Select
                  allowClear
                  style={{ width: 200 }}
                  disabled={treeMode === 'custom'}
                  onFocus={this.getSpecUnit}
                >
                  {specUnitList.map(item => (
                    <Select.Option key={item.metaCode}>{item.metaName}</Select.Option>
                  ))}
                </Select>
              )}
            </Form.Item>
            <Form.Item label="测点精度" labelCol={{ xl: 3 }} wrapperCol={{ xl: 21 }}>
              {getFieldDecorator('precision', {
                rules: [
                  {
                    required: true,
                    message: '测点精度为必填项',
                  },
                ],
              })(
                <Select style={{ width: 200 }} disabled={treeMode === 'custom'}>
                  {POINT_PRECISION.map(item => {
                    return (
                      <Select.Option key={item} value={item} label={item}>
                        {item}
                      </Select.Option>
                    );
                  })}
                </Select>
              )}
            </Form.Item>
            {processedPointList.dataType.value === POINT_DATA_TYPE_CODE_MAP.AI && (
              <Form.Item label="工作区间" labelCol={{ xl: 3 }} wrapperCol={{ xl: 21 }}>
                {getFieldDecorator('validLimits', {
                  rules: [
                    {
                      validator: dataRangeValidator,
                    },
                  ],
                })(<ValidLimits disabled={treeMode === 'custom'} />)}
              </Form.Item>
            )}
          </>
        )}
        {(treeMode === 'space' || treeMode === 'device') &&
          processedPointList.dataType.value === POINT_DATA_TYPE_CODE_MAP.DI && (
            <>
              <Form.Item label="状态数量" labelCol={{ xl: 3 }} wrapperCol={{ xl: 21 }}>
                {getFieldDecorator('digitalNum', {
                  rules: [
                    {
                      required: true,
                      message: '状态数量为必填项',
                    },
                    {
                      type: 'number',
                      max: 50,
                      message: '状态数量最大值为50',
                    },
                    { pattern: /^\d+$/, message: '状态数量必须为整数' },
                  ],
                  initialValue:
                    info.validLimits && Array.isArray(info.validLimits)
                      ? info.validLimits.length
                      : null,
                })(<InputNumber min={1} max={51} style={{ width: 70 }} />)}
              </Form.Item>

              {!!digitalNum && (
                <Form.Item label="状态含义" labelCol={{ xl: 3 }} wrapperCol={{ xl: 21 }}>
                  {getFieldDecorator('validLimits')(<Row gutter={16}>{this.getFields()}</Row>)}
                </Form.Item>
              )}
            </>
          )}

        {/* 设备&空间DI量加工测点只支持输入，不允许编辑器设置（需求变更）,默认只要手动输入 */}
        {this.props.processedPointList.dataType.value === POINT_DATA_TYPE_CODE_MAP.DI &&
        (this.props.processedPointList.pointType.value === POINT_TYPE_CODE_MAP.CAL_DEVICE ||
          this.props.processedPointList.pointType.value === POINT_TYPE_CODE_MAP.CAL_SPACE) ? (
          <Form.Item
            label={
              <div
                style={{
                  margin: treeMode === 'custom' ? '0px' : '-10px 10px 10px 10px',
                }}
              >
                <span>计算表达式</span>
              </div>
            }
            labelCol={{ xl: 3 }}
            wrapperCol={{ xl: 21 }}
          >
            {treeMode === 'custom' && (
              <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
                <HandlerPointDrawer
                  customSpaceGuid={customSpaceGuid}
                  onChange={value => {
                    if (value) {
                      const formula = getFieldValue('formula');

                      form.setFieldsValue({
                        formula: `${formula ? formula : ''}K_${value.deviceId}_${value.pointCode}_${value.deviceType}`,
                      });
                    }
                  }}
                />
              </div>
            )}
            <Form.Item>
              {getFieldDecorator('formula', {
                rules: [
                  {
                    required: true,
                    message: '请输入计算表达式',
                    whitespace: true,
                  },
                  {
                    validator: (rule, value, callback) => {
                      if (value && /(^\s+)|(\s+$)/.test(value)) {
                        callback('首尾不能包含空格');
                      } else {
                        callback();
                      }
                    },
                  },
                ],
              })(
                <Input.TextArea
                  rows={10}
                  style={{ width: '100%' }}
                  maxLength={10000}
                  placeholder="请输入计算表达式"
                />
              )}
            </Form.Item>
          </Form.Item>
        ) : (
          <Form.Item label="计算表达式" labelCol={{ xl: 3 }} wrapperCol={{ xl: 21 }}>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Form.Item>
                {getFieldDecorator('selectedExprDataType')(
                  <Radio.Group>
                    <Radio value="editor">编辑器</Radio>
                    <Radio value="handler">手动输入</Radio>
                  </Radio.Group>
                )}
              </Form.Item>
              {treeMode === 'custom' && getFieldValue('selectedExprDataType') === 'handler' && (
                <HandlerPointDrawer
                  customSpaceGuid={customSpaceGuid}
                  onChange={value => {
                    if (value) {
                      const formula = getFieldValue('formula');

                      form.setFieldsValue({
                        formula: `${formula ? formula : ''}K_${value.deviceId}_${value.pointCode}_${value.deviceType}`,
                      });
                    }
                  }}
                />
              )}
            </div>

            {getFieldValue('selectedExprDataType') === 'editor' && (
              <Form.Item>
                {getFieldDecorator('exprData', {
                  rules: [
                    {
                      required: treeMode === 'custom',
                      message: '计算表达式为必填项',
                    },
                    {
                      validator: (__, statusList, callback) =>
                        validateExprData(__, statusList, callback, validateParams, treeMode),
                    },
                  ],
                })(
                  <ExprEditor
                    extraAction={
                      <TestModalButton
                        disabled={!exprPoints.length}
                        data={exprPoints}
                        result={testExprResult}
                        onOk={onTestExpr}
                      />
                    }
                    activeDroppableId={exprActiveDroppableId}
                    treeMode={treeMode}
                    processedType={this.props.processedType}
                    customSpacePointHost={customIdcTag}
                    showParameterPointDraggable={isParameterPointDraggable(
                      targetPointType,
                      treeMode
                    )}
                    showDevicePointDraggable={
                      targetPointType === TargetPointType.DEVICE || treeMode === 'custom'
                    }
                    showSpacePointDraggable={
                      targetPointType === TargetPointType.SPACE || treeMode === 'custom'
                    }
                    showBlockAttribute={this.props.processedType === 'CAL_DEVICE'}
                    draggableDescriptorMutator={draggableDescriptorMutator}
                    info={info}
                    onActiveHandleClick={onExprActiveHandleClick}
                    onNumberChange={(id, num) => {
                      onExprNumberChange({ itemId: id, value: num });
                    }}
                    onRemoveHandleClick={onExprRemoveItem}
                  />
                )}
              </Form.Item>
            )}

            {getFieldValue('selectedExprDataType') === 'handler' && (
              <Form.Item>
                {getFieldDecorator('formula', {
                  rules: [
                    {
                      required: true,
                      message: '请输入计算表达式',
                      whitespace: true,
                    },
                    {
                      validator: (rule, value, callback) => {
                        if (value && /(^\s+)|(\s+$)/.test(value)) {
                          callback('首尾不能包含空格');
                        } else {
                          callback();
                        }
                      },
                    },
                  ],
                })(
                  <Input.TextArea
                    rows={10}
                    style={{ width: '100%' }}
                    maxLength={10000}
                    placeholder="请输入计算表达式"
                  />
                )}
              </Form.Item>
            )}
          </Form.Item>
        )}

        <Form.Item
          label={
            <div
              style={{
                margin: '-10px 10px 10px 10px',
              }}
            >
              <span>备注</span>
            </div>
          }
          labelCol={{ xl: 3 }}
          wrapperCol={{ xl: 21 }}
        >
          {getFieldDecorator('description', {
            rules: [
              {
                max: 60,
                message: '最多输入 60 个字符！',
              },
            ],
          })(
            <Input.TextArea disabled={treeMode === 'custom'} rows={5} style={{ width: '100%' }} />
          )}
        </Form.Item>
      </Form>
    );
  }
}

const createOpts = {
  onFieldsChange(props, changedFields, allFields) {
    let fields = changedFields;
    if (changedFields.exprData) {
      delete fields.exprData.value;
    }
    if (changedFields.pointType) {
      if (fields.pointType.value !== props.processedPointList.pointType.value) {
        props.updateExpressionData([]);
        props.setParentDeviceType(null);
      }
    }

    const digitalNum = props.digitalNum;
    const digitalNumToArr = props.digitalNumToArr;
    if (digitalNum && digitalNumToArr.length) {
      // 状态含义改变时 判断含义是否重复
      const filedsValueArr = digitalNumToArr.map(item =>
        allFields[item] ? allFields[item] : { name: item, value: '' }
      );
      const filterOnlySameFiledsError = filedsValueArr.map(currentStatus => {
        // 查找value相同，但是name 不同的数据
        const repeated = judgeStatusIsRepeated(filedsValueArr, currentStatus);
        if (repeated) {
          return {
            ...currentStatus,
            errors: [
              {
                field: currentStatus.name,
                message: statusErrors.repeat,
              },
            ],
          };
        }
        return {
          ...currentStatus,
          errors:
            !currentStatus.value || !currentStatus.value.trim()
              ? [
                  {
                    name: currentStatus.name,
                    field: statusErrors.blank,
                  },
                ]
              : undefined,
          dirty: false,
        };
      });
      const newFieldsValue = {};
      filterOnlySameFiledsError.forEach(filed => {
        newFieldsValue[filed.name] = filed;
      });
      fields = { ...fields, ...newFieldsValue };
    }
    props.updateFormValues(fields);
  },
  mapPropsToFields(props) {
    const processedPointFields = Object.keys(props.processedPointList).reduce(
      (formFields, fieldKey) => {
        formFields[fieldKey] = Form.createFormField(props.processedPointList[fieldKey]);
        return formFields;
      },
      {}
    );

    return {
      ...processedPointFields,
      name: Form.createFormField(props.processedPointList.name),
      unit: Form.createFormField(props.processedPointList.unit),
      precision: Form.createFormField(props.processedPointList.precision),
      priority: Form.createFormField(props.processedPointList.priority),
      dataType: Form.createFormField(props.processedPointList.dataType),
      statusList: Form.createFormField(props.processedPointList.statusList),
      exprData: Form.createFormField({
        ...(props.processedPointList.exprData || {}),
        value: props.exprData,
      }),
      code: Form.createFormField(props.processedPointList.code),
      spaceGuid: Form.createFormField(props.processedPointList.spaceGuid),
      targetPointType: Form.createFormField(props.processedPointList.targetPointType),
      validLimits: Form.createFormField(props.processedPointList.validLimits),
      description: Form.createFormField(props.processedPointList.description),
      digitalNum: Form.createFormField(props.processedPointList.digitalNum),
    };
  },
};

const EhanceProcessedPointForm = Form.create(createOpts)(ProcessedPointForm);

const mapStateToProps = ({
  mergedProcessesdPoint: {
    processedPointList,
    exprActiveDroppableId,
    expressionData,
    testExprResult,
    parentDeviceType,
    customSpaceGuid,
  },
  common: { deviceCategory },
  eventCenter: { specUnitList },
  doPointManage: { selectCategoryPoint },
  'resource.spaces': { codes },
}) => {
  let ids = [];
  const statusData = processedPointList.statusList.value;
  let newData = [];
  if (statusData && statusData.length) {
    newData = statusData.map((point, index) => {
      let sameData = [];
      if (!ids.includes(point.label)) {
        statusData.forEach(item => {
          if (item.label === point.label) {
            sameData = [...sameData, item];
          }
        });
      }
      if (sameData.length > 1) {
        ids = [...ids, point.label];
        return {
          ...point,
          rowSpan: sameData.length,
        };
      } else {
        if (ids.includes(point.label)) {
          if (
            !statusData[index + 1] ||
            (statusData[index + 1] && point.label !== statusData[index + 1].label)
          ) {
            return {
              ...point,
              rowSpan: 0,
              isLast: true,
            };
          }
          return {
            ...point,
            rowSpan: 0,
          };
        } else {
          return {
            ...point,
            rowSpan: 1,
            isLast: true,
          };
        }
      }
    });
  }

  // 表达式数据源中的测点数据
  const exprPoints = flattenTreeData(expressionData).reduce((acc, item) => {
    if (
      [
        EXPR_ITEM_TYPE.POINT,
        EXPR_ITEM_TYPE.SPACE_POINT,
        EXPR_ITEM_TYPE.CUSTOM_SPACE_POINT,
        EXPR_ITEM_TYPE.DEVICE_PROPERTY,
        EXPR_ITEM_TYPE.PARAMETER,
      ].includes(item.type)
    ) {
      acc.push({
        pointKey: item.value,
        pointName: item.name,
      });
    }

    return acc;
  }, []);

  const validateParams = {
    dataType: processedPointList.dataType.value,
    precision: processedPointList.precision.value,
    formulaJson: JSON.stringify(expressionData),
  };

  let deviceType = '';
  if (deviceCategory && deviceCategory.normalizedList && parentDeviceType) {
    deviceType = deviceCategory.normalizedList[parentDeviceType].metaName;
  }
  const digitalNum =
    processedPointList.digitalNum && processedPointList.digitalNum.value
      ? processedPointList.digitalNum.value > 50
        ? 50
        : processedPointList.digitalNum.value
      : null;
  const digitalNumToArr = digitalNum ? getStatusNumArr(digitalNum) : [];

  return {
    processedPointList,
    statusList: newData,
    exprActiveDroppableId,
    exprData: expressionData,
    testExprResult,
    exprPoints,
    parentDeviceType,
    customSpaceGuid,
    validateParams,
    deviceType,
    specUnitList,
    selectedTreeNode: selectCategoryPoint.deviceType,
    selectMetaType: selectCategoryPoint.metaType,
    codes,
    digitalNum,
    digitalNumToArr,
  };
};
const mapDispatchToProps = {
  updateFormValues: mergedProcessesdPointActions.updateProcessedPointFormValues,
  updateProcessedSelectedPoint: mergedProcessesdPointActions.updateProcessedSelectedPoint,
  updateProcessedDeviceInfo: mergedProcessesdPointActions.updateProcessedDeviceInfo,
  deleteSameLabel: mergedProcessesdPointActions.deleteSameLabel,
  deletePressedStatusListSinglePoint:
    mergedProcessesdPointActions.deletePressedStatusListSinglePoint,
  addStausInStatusList: mergedProcessesdPointActions.addStausInStatusList,
  onChangeStatusInput: mergedProcessesdPointActions.onChangeStatusInput,
  onChangeDataTypeSelect: mergedProcessesdPointActions.onChangeDataTypeSelect,
  onExprActiveHandleClick: mergedProcessesdPointActions.setExprActiveDroppableId,
  onExprNumberChange: mergedProcessesdPointActions.exprNumberChange,
  onExprRemoveItem: mergedProcessesdPointActions.exprRemoveItem,
  onTestExpr: testExprActionCreator,
  getMetadataByType: getMetadataByType,
  updateExpressionData: mergedProcessesdPointActions.updateExpressionData,
  setParentDeviceType: mergedProcessesdPointActions.setParentDeviceType,
  resetItems: mergedProcessesdPointActions.resetItems,
  resetCustomPointExpr: mergedProcessesdPointActions.resetCustomPointExpr,
  getPointDetail,
  updatePointType: mergedProcessesdPointActions.updatePointType,
  updateCustomSpaceGuid: mergedProcessesdPointActions.updateCustomSpaceGuid,
};

export default connect(mapStateToProps, mapDispatchToProps)(EhanceProcessedPointForm);

const columns = (ctx, statusList, treeMode, statusListFiledIsTouched) => [
  {
    title: '',
    dataIndex: '',
    width: 50,
    render: (_, record, index) => {
      const same = statusList.filter(item => item.label === record.label);
      const idx = index + same.length;
      let isLast = false;
      if (!statusList[idx]) {
        isLast = true;
      }
      return {
        children: isLast ? <DeleteOutlined onClick={() => ctx.deleteWithSameStatus(record)} /> : {},
        props: {
          rowSpan: record.rowSpan,
        },
      };
    },
  },
  {
    title: '值',
    dataIndex: 'label',
    width: 50,
    render: (label, record) => {
      return {
        children: label,
        props: {
          rowSpan: record.rowSpan,
        },
      };
    },
  },
  {
    title: '状态含义',
    dataIndex: 'status',
    render: (status, record, index) => {
      const validationProps = {};
      if (!status && statusListFiledIsTouched) {
        validationProps.validateStatus = 'error';
        validationProps.help = '状态含义必填！';
        validationProps.required = true;
      }

      return {
        children: (
          <Form.Item {...validationProps}>
            <Input
              allowClear
              value={status}
              maxLength={10}
              style={{ width: '200px' }}
              size="small"
              onChange={e => {
                ctx.props.onChangeStatusInput({ status: e.target.value, index });
              }}
            />
          </Form.Item>
        ),
        props: {
          rowSpan: record.rowSpan,
        },
      };
    },
  },
  {
    title: '测点名称',
    dataIndex: 'name',
    render: (name, record, index) => {
      const validationProps = {};
      if (!name && statusListFiledIsTouched) {
        validationProps.validateStatus = 'error';
        validationProps.help = '测点必选!';
        validationProps.required = true;
      }
      // 判断为最后一个状态
      let isLast = false;
      if (
        (statusList[index + 1] && statusList[index + 1].label !== record.label) ||
        !statusList[index + 1]
      ) {
        isLast = true;
      }
      // 需要禁用的节点
      const disabledLeaf = statusList
        .map(({ label, value }) => {
          if (label === record.label && value !== record.value) {
            return value;
          }
          return null;
        })
        .filter(Boolean);
      return (
        <Form.Item {...validationProps}>
          {/* <Input style={{ width: '200px' }} size="small" /> */}
          <NameDropped
            style={{ width: 200 }}
            pattern="CAL_DEVICE"
            pointType="CAL_DEVICE"
            dataType="DI"
            value={{ key: record.value, label: record.name }}
            markId={record.markedID}
            isLast={isLast}
            statusIdx={index}
            disabledLeaf={disabledLeaf}
          />
        </Form.Item>
      );
    },
  },
  {
    title: '测点状态',
    dataIndex: 'dataType',
    render: (dataType, record, index) => {
      const validationProps = {};
      if (!dataType && record.name && statusListFiledIsTouched) {
        validationProps.validateStatus = 'error';
        validationProps.help = '测点状态必选！';
        validationProps.required = true;
      }
      return (
        <Form.Item {...validationProps}>
          <Select
            value={dataType}
            style={{ width: '100px' }}
            size="small"
            disabled={record.name ? false : true}
            onChange={value => ctx.props.onChangeDataTypeSelect({ dataType: value, index })}
          >
            {record.validLimits.length &&
              record.validLimits.map(({ label, value }) => {
                return (
                  <Select.Option key={value} value={value} label={label}>
                    {label}
                  </Select.Option>
                );
              })}
          </Select>
        </Form.Item>
      );
    },
  },
  {
    title: '',
    dataIndex: '',
    width: 50,
    render: (_, record, index) => {
      const same = statusList.filter(item => item.label === record.label);
      if (same.length === 1) {
        return <Button type="link" icon={<DeleteOutlined />} disabled />;
      } else {
        return <DeleteOutlined onClick={() => ctx.deleteSinglePoint(index)} />;
      }
    },
  },
];

async function validateExprData(__, exprData, callback, validateParams, treeMode) {
  if (treeMode !== 'custom') {
    return;
  }
  if (!exprData.length) {
    callback('表达式必填！');
    return;
  }

  const hasPoint = flattenTreeData(exprData).some(
    ({ type }) =>
      type === EXPR_ITEM_TYPE.POINT ||
      type === EXPR_ITEM_TYPE.DEVICE_PROPERTY ||
      type === EXPR_ITEM_TYPE.SPACE_POINT ||
      type === EXPR_ITEM_TYPE.CUSTOM_SPACE_POINT
  );
  if (!hasPoint) {
    callback('表达式中至少要选择一个测点');
    return;
  }
  const nullPoint = flattenTreeData(exprData).some(
    ({ type, value }) =>
      (type === EXPR_ITEM_TYPE.POINT || type === EXPR_ITEM_TYPE.DEVICE_PROPERTY) && !value
  );
  if (nullPoint) {
    callback('测点不能为空');
    return;
  }
  const { invalidReq, exprTxt } = await validateExpr(validateParams);
  if (invalidReq) {
    callback('表达式解析遇到异常，请重试！');
    // TODO: clear this error in order to allow user to retry submission.
  }
  if (!exprTxt) {
    callback('表达式解析出错，请修正！');
    return;
  }

  callback();
}
const formatPointCode = pointCode => {
  if (typeof pointCode === 'string') {
    const pointCodeNumber = parseInt(pointCode.toString().split('').slice(1, 4).join(''));
    return pointCodeNumber < 999 ? pointCodeNumber + 1 : pointCodeNumber;
  } else {
    return 1;
  }
};

const generateFetchConditionParams = ({
  treeMode,
  selectedTreeNode,
  codes,
  selectMetaType,
  dataType,
}) => {
  const param = {
    deviceType: selectedTreeNode,
  };
  if (treeMode === 'custom') {
    return {
      deviceType: getSpaceDeviceTypeIfNeeded(selectMetaType, null) ?? SPACE_DEVICE_TYPE_KEY_MAP.IDC,
    };
  }
  return treeMode === 'device' ? param : { ...param, dataType };
};

function dataRangeValidator(__, validLimits, callback) {
  const ge = validLimits?.ge ?? null;
  const le = validLimits?.le ?? null;

  if (ge && !le && typeof le !== 'number') {
    callback('最大值必填！');
  } else if (ge !== 0 && isNil(ge) && le) {
    callback('最小值必填！');
  } else if (typeof le === 'number' && typeof ge === 'number' && Number(le) <= Number(ge)) {
    callback('最小值要小于最大值！');
  } else {
    callback();
  }
}

function judgeStatusIsRepeated(data, current) {
  return !!data.find(
    single =>
      single.value &&
      current.value &&
      single.value === current.value &&
      single.name !== current.name
  );
}

function getStatusNumArr(num) {
  const arr = [];
  for (let i = 0; i < num; i++) {
    arr.push(String(i));
  }
  return arr;
}
