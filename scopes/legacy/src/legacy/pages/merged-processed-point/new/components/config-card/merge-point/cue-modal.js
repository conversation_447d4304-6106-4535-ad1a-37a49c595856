import React, { Component } from 'react';
import { connect } from 'react-redux';

import { Modal } from '@manyun/base-ui.ui.modal';

import {
  mergeTheMergedPointConfig,
  mergedProcessesdPointActions,
} from '@manyun/dc-brain.legacy.redux/actions/mergedProcessesdPointActions';

class CueModal extends Component {
  handleOk = () => {
    this.props.mergeTheMergedPointConfig();
  };

  handleCancel = () => {
    this.props.changeIsMerged(false);
  };

  render() {
    const { isMerged } = this.props;
    return (
      <Modal title="提示" visible={isMerged} onOk={this.handleOk} onCancel={this.handleCancel}>
        <p>该测点已存在相同聚合配置，是否导入之前配置？</p>
      </Modal>
    );
  }
}

const mapStateToProps = ({ mergedProcessesdPoint: { isMerged } }) => {
  return {
    isMerged,
  };
};
const mapDispatchToProps = {
  changeIsMerged: mergedProcessesdPointActions.changeIsMerged,
  mergeTheMergedPointConfig,
};

export default connect(mapStateToProps, mapDispatchToProps)(CueModal);
