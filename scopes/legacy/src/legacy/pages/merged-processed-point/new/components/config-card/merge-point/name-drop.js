import React, { Component } from 'react';
import { connect } from 'react-redux';

import PlusCircleOutlined from '@ant-design/icons/es/icons/PlusCircleOutlined';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { message } from '@manyun/base-ui.ui.message';
import { TreeSelect } from '@manyun/base-ui.ui.tree-select';

import { useSpacesType } from '@manyun/resource-hub.hook.use-spaces-type';
import { fetchPageDevices } from '@manyun/resource-hub.service.fetch-page-devices';
import { fetchPointsByCondition } from '@manyun/resource-hub.service.fetch-points-by-condition';
import { SpecSelect } from '@manyun/resource-hub.ui.spec-select';

import { METADATA_TYPE } from '@manyun/dc-brain.legacy.constants';
import {
  DEVICE_SPACE_TYPE_KEY_MAP,
  DEVICE_TYPE_META_TYPE_KEY_MAP,
  DIMENSION_TYPE,
} from '@manyun/dc-brain.legacy.constants/enums/deviceType';
import { POINT_TYPE_CODE_MAP } from '@manyun/dc-brain.legacy.constants/point';
import { EXPR_ITEM_TYPE } from '@manyun/dc-brain.legacy.pages/merged-processed-point/constants';
// import { getCurrentConfig } from '@manyun/dc-brain.legacy.redux/selectors/configSelectors';
// import { ConfigUtil } from '@manyun/dc-brain.util.config';
import {
  changeExpressInfos,
  mergedProcessesdPointActions,
} from '@manyun/dc-brain.legacy.redux/actions/mergedProcessesdPointActions';

import { getDisabledData, getFormulaName, getTreeSelectValue } from './utils';

// 不止一处地方使用了已经被废弃的枚举类型进行判断，目前实现了0331迭代的优化需求，而对于废弃类型的整个优化todo
const NameDropped = props => {
  const { spaceKeyMap } = useSpacesType();

  return (
    <>
      <NameDroppedChildren spaceKeyMap={spaceKeyMap} {...props} />
    </>
  );
};

class NameDroppedChildren extends Component {
  state = {
    pointData: [], // treeSelect 数据源
    treeExpandedKeys: [],
  };

  treeSelectRef = React.createRef();

  componentDidMount() {
    if (this.props.deviceCategory) {
      const data = this.getInitePointData();
      this.setState({
        pointData: data,
      });
    }
    this.props.setPointType(this.props.pointType);
  }

  componentDidUpdate(prevProps) {
    if (
      (!prevProps.deviceCategory && this.props.deviceCategory) ||
      (!prevProps.selectCategoryPoint.deviceType && this.props.selectCategoryPoint.deviceType) ||
      prevProps.selectCategoryPoint.deviceType !== this.props.selectCategoryPoint.deviceType
    ) {
      const data = this.getInitePointData();
      this.setState({
        pointData: data,
      });
    }
  }

  getInitePointData = () => {
    const { deviceCategory, selectCategoryPoint, pointType, spaceKeyMap } = this.props;
    let dataSource = [];
    if (deviceCategory && selectCategoryPoint.deviceType) {
      // 设备内聚合   其他加工
      if (
        pointType === POINT_TYPE_CODE_MAP.AGG_DEVICE ||
        pointType === POINT_TYPE_CODE_MAP.CAL_SPACE
      ) {
        const tmp = deviceCategory.normalizedList[selectCategoryPoint.deviceType];
        dataSource = [
          {
            ...tmp,
            id: tmp.metaType + tmp.metaCode,
            pId: tmp.parentCode,
            value: tmp.metaCode,
            title: tmp.metaName,
            isLeaf: false,
            // disabled: true,
          },
        ];
      }
      // 其它聚合
      if (pointType === POINT_TYPE_CODE_MAP.AGG_SPACE) {
        const list = filterDataSource(
          this.props.deviceCategory.treeList,
          selectCategoryPoint.deviceType,
          spaceKeyMap
        );
        dataSource = getPointsCascaderData(list);
      }
      if (pointType === POINT_TYPE_CODE_MAP.CAL_DEVICE) {
        const tmp = deviceCategory.normalizedList[selectCategoryPoint.deviceType];
        dataSource = [
          {
            ...tmp,
            id: tmp.metaType + tmp.metaCode,
            pId: tmp.parentCode,
            value: tmp.metaCode,
            title: tmp.metaName,
            isLeaf: false,
            // disabled: true,
            children: [],
          },
        ];
      }
    }
    return dataSource;
  };

  onSearch = async value => {
    this.getAllDeivceList(value);
  };

  getAllDeivceList = async deviceName => {
    if (!deviceName) {
      this.setState({ pointData: [] });
    }
    const { data, error } = await fetchPageDevices({
      name: deviceName,
      pageNum: 1,
      pageSize: 50,
    });
    if (error) {
      this.setState({ pointData: [] });
      message.error(error.message);
      return;
    }
    if (data?.data.length) {
      this.setState({
        pointData: getDisabledData(data.data),
      });
    } else {
      this.setState({ pointData: [] });
    }
  };

  close = () => {
    const { markId } = this.props;
    this.props.deleteMergedPointInForm({ id: markId, type: 'tag' });
  };

  loadData = selectedOptions => {
    const { margedPointIds, margedPointMaps, pointType, disabledLeaf } = this.props;
    const node = selectedOptions;
    // 请求测点
    return new Promise(async resolve => {
      if (node.metaType === 'C2') {
        const deviceType = node.metaCode.replace('K_C2', '');
        const { data, error } = await fetchPointsByCondition({
          deviceType: deviceType,
          dataTypeList: ['AI', 'DI'],
          pointTypeList: getPointTypeList(pointType),
          isRemoveSub: true,
        });
        if (error) {
          message.error(error);
        }
        if (data && data.data.length) {
          // 需要给包间类型的测点加一个常量选项

          // console.log('pointData', treeList);
          let cache = {};
          let countData = [];
          const result = data.data.map(point => point.toApiObject());
          if (pointType === POINT_TYPE_CODE_MAP.AGG_DEVICE) {
            // 设备类型内聚合，筛选扩展点位数大于0的点位
            countData = result.filter(({ extCount }) => extCount && Number(extCount) > 0);
          }
          if (pointType === POINT_TYPE_CODE_MAP.AGG_SPACE) {
            // 其他聚合，筛选除了扩展点位数大于0的点位外的所有点位
            countData = result.filter(({ extCount }) => !extCount);
          }
          if (pointType === POINT_TYPE_CODE_MAP.CAL_DEVICE) {
            // 设备内加工 ，筛选除了扩展点位数大于0的点位外的所有点位
            countData = result.filter(
              ({ extCount, dataType }) =>
                !extCount && (!this.props.dataType || dataType.code === this.props.dataType)
            );
            const existenceChilren = node.children.map(({ props }) => props);
            countData = [...existenceChilren, ...countData];
          }
          if (pointType === POINT_TYPE_CODE_MAP.CAL_SPACE) {
            countData = result.filter(
              ({ extCount, dataType }) =>
                !extCount && (!this.props.dataType || dataType.code === this.props.dataType)
            );
          }
          const newData = countData.map(item => {
            if (item.value === 'SPEC_UNIT') {
              cache = {
                ...cache,
                [item.id]: {
                  ...item,
                  type: EXPR_ITEM_TYPE.DEVICE_PROPERTY,
                },
              };
              return item;
            } else {
              cache = {
                ...cache,
                [`K_${item.deviceType}_${item.pointCode}`]: {
                  ...item,
                  metaCode: `K_${item.deviceType}_${item.pointCode}`,
                  metaType: 'REAL_ITEM',
                  nodeType: pointType,
                  type: EXPR_ITEM_TYPE.POINT,
                },
              };
              return {
                ...item,
                pId: node.id,
                id: item.pointCode,
                value: `K_${item.deviceType}_${item.pointCode}`,
                title: item.name,
                isLeaf: true,
                disabled:
                  disabledLeaf && disabledLeaf.length
                    ? disabledLeaf.includes(`K_${item.deviceType}_${item.pointCode}`)
                      ? true
                      : false
                    : isSelected(item, margedPointIds, margedPointMaps),
                deviceName: node.metaName,
              };
            }
          });

          this.setState({
            pointData: getNewLoadData(this.state.pointData, node, newData),
          });
          this.props.savePointInfoMap(cache);
          resolve();
        }
      }
      resolve();
    });
  };

  render() {
    const {
      markId,
      value,
      forwardedRef,
      pointType,
      pointInfoMap,
      id,
      expressionData,
      dataType,
      statusList,
      isLast,
      isParameter,
      type,
      statusIdx,
      selectCategoryPoint,
      margedPointMaps,
    } = this.props;

    const { treeExpandedKeys } = this.state;
    const selectedPoints = Object.keys(margedPointMaps).map(key => {
      const value = margedPointMaps[key].name.value;
      return value;
    });
    const pointData = getDisabledData(this.state.pointData, selectedPoints);
    return (
      <span ref={forwardedRef}>
        {pointType === POINT_TYPE_CODE_MAP.CAL_DEVICE && dataType === 'DI' && isLast && (
          <>
            <PlusCircleOutlined
              style={{ color: `var(--${prefixCls}-success-color)` }}
              onClick={() => this.props.addPointInStatus(statusIdx)}
            />
            &nbsp;&nbsp;
          </>
        )}
        {isParameter || type === EXPR_ITEM_TYPE.BLOCK_ATTRIBUTE ? (
          <SpecSelect
            placeholder="请选择参数"
            value={getTreeSelectValue(value, pointInfoMap)}
            labelInValue
            style={{ width: 200 }}
            isDevice={
              type === EXPR_ITEM_TYPE.BLOCK_ATTRIBUTE
                ? false
                : pointType !== POINT_TYPE_CODE_MAP.CAL_SPACE
            }
            deviceType={
              type === EXPR_ITEM_TYPE.BLOCK_ATTRIBUTE ? '90102' : selectCategoryPoint.deviceType
            }
            onChange={e => {
              const { value, label } = e;
              if (
                pointType === POINT_TYPE_CODE_MAP.CAL_SPACE ||
                pointType === POINT_TYPE_CODE_MAP.CAL_DEVICE
              ) {
                const isBlockAttribute = this.props.type === EXPR_ITEM_TYPE.BLOCK_ATTRIBUTE;
                const point = isBlockAttribute ? `AT_${value}_90102` : value;
                const name = label;
                const type = isBlockAttribute
                  ? EXPR_ITEM_TYPE.BLOCK_ATTRIBUTE
                  : pointType === POINT_TYPE_CODE_MAP.CAL_SPACE
                    ? 'point'
                    : 'device-property';
                const newData = setPointDroppableValue(expressionData, id, point, name, type);
                this.props.updateExpressionData(newData);
                this.props.updateFormulaName({
                  formulaName: `${selectCategoryPoint.selectTitle}.${name}`,
                });
              }
            }}
          />
        ) : (
          <TreeSelect
            {...this.props}
            ref={this.treeSelectRef}
            placeholder="请输入设备类型搜索"
            labelInValue
            value={getTreeSelectValue(value, pointInfoMap)}
            showSearch
            treeDataSimpleMode
            loadData={this.loadData}
            treeData={pointData}
            size={
              pointType === POINT_TYPE_CODE_MAP.AGG_DEVICE ||
              pointType === POINT_TYPE_CODE_MAP.AGG_SPACE
                ? 'default'
                : 'small'
            }
            treeNodeFilterProp="title"
            treeExpandedKeys={treeExpandedKeys}
            listHeight={400}
            onChange={({ value }, label, extra) => {
              const metaType = extra.triggerNode.props.metaType;

              if (
                metaType === 'C0' ||
                metaType === 'C1' ||
                metaType === 'C2' ||
                metaType === 'SPEC_UNIT'
              ) {
                if (!extra.triggerNode.props.expanded) {
                  this.setState(({ treeExpandedKeys }) => ({
                    treeExpandedKeys: [...treeExpandedKeys, extra.triggerNode.props.eventKey],
                  }));
                } else {
                  this.setState(({ treeExpandedKeys }) => ({
                    treeExpandedKeys: treeExpandedKeys.filter(
                      key => key !== extra.triggerNode.props.eventKey
                    ),
                  }));
                }
                this.treeSelectRef.current.rcTreeSelect.setOpenState(true);
                return;
              }
              if (
                pointType === POINT_TYPE_CODE_MAP.AGG_DEVICE ||
                pointType === POINT_TYPE_CODE_MAP.AGG_SPACE
              ) {
                this.props.changeExpressInfos({
                  source: {
                    droppableId: 'treeLest',
                  },
                  destination: {
                    droppableId: `pointDrop_$$_${markId}`,
                  },
                  dragValue: value,
                  pointType,
                  dimension: DIMENSION_TYPE[selectCategoryPoint.deviceType]
                    ? DIMENSION_TYPE[selectCategoryPoint.deviceType]
                    : 'DEVICE',
                });
              }
              if (
                pointType === POINT_TYPE_CODE_MAP.CAL_SPACE ||
                pointType === POINT_TYPE_CODE_MAP.CAL_DEVICE
              ) {
                if (dataType === 'DI') {
                  const newStatusList = statusList.value.map(item => {
                    if (item.markedID === markId && item.value !== value) {
                      return {
                        ...item,
                        name: pointInfoMap[value]
                          ? pointInfoMap[value].specName || pointInfoMap[value].name
                          : '',
                        value,
                        validLimits:
                          pointInfoMap[value] &&
                          pointInfoMap[value].validLimits &&
                          Array.isArray(pointInfoMap[value].validLimits)
                            ? pointInfoMap[value].validLimits.map(item => {
                                const [value, label] = item.split('=');
                                return {
                                  value,
                                  label,
                                };
                              })
                            : [],
                        dataType: null,
                      };
                    }
                    return item;
                  });
                  this.props.updateStatusList(newStatusList);
                  return;
                }
                const point = value.includes('RATED_POWER') ? 'RATED_POWER' : value;
                const name = pointInfoMap[value]
                  ? pointInfoMap[value].specName || pointInfoMap[value].name
                  : '';

                const type = pointInfoMap[value] ? pointInfoMap[value].type : '';
                const newData = setPointDroppableValue(expressionData, id, point, name, type);

                this.props.updateExpressionData(newData);
                this.props.updateFormulaName({
                  formulaName: getFormulaName(newData, selectCategoryPoint.selectTitle),
                });
              }
            }}
            onTreeExpand={expandedKeys => {
              this.setState({ treeExpandedKeys: expandedKeys });
            }}
          />
        )}
      </span>
    );
  }
}

const mapStateToProps = ({
  common: { deviceCategory },
  mergedProcessesdPoint: {
    margedPointIds,
    margedPointMaps,
    pointInfoMap,
    expressionData,
    processedPointList: { statusList },
  },
  doPointManage: { selectCategoryPoint },
}) => {
  return {
    deviceCategory: deviceCategory
      ? { ...deviceCategory, treeList: deviceCategory.treeList.filter(({ numbered }) => numbered) }
      : null,
    margedPointIds,
    margedPointMaps,
    selectCategoryPoint,
    expressionData,
    pointInfoMap,
    statusList,
  };
};
const mapDispatchToProps = {
  deleteMergedPointInForm: mergedProcessesdPointActions.deleteMergedPointInForm,
  savePointInfoMap: mergedProcessesdPointActions.savePointInfoMap,
  changeExpressInfos,
  updateExpressionData: mergedProcessesdPointActions.updateExpressionData,
  updateStatusList: mergedProcessesdPointActions.updateStatusList,
  addPointInStatus: mergedProcessesdPointActions.addPointInStatus,
  setPointType: mergedProcessesdPointActions.setPointType,
  updateFormulaName: mergedProcessesdPointActions.updateFormulaName,
};
const NameConnectedProps = connect(mapStateToProps, mapDispatchToProps)(NameDropped);
export default React.forwardRef((props, ref) => (
  <NameConnectedProps forwardedRef={ref} {...props} />
));

function getNewLoadData(treeData, node, points) {
  return treeData.map(item => {
    if (node.value === METADATA_TYPE['SPEC_UNIT']) {
      if (item.value === node.value) {
        return {
          ...item,
          children: points,
        };
      }
      if (!item.children) {
        return item;
      }
      return {
        ...item,
        children: getNewLoadData(item.children, node, points),
      };
    } else {
      if (item.id === node.id) {
        return {
          ...item,
          children: points,
        };
      }
      if (!item.children) {
        return item;
      }
      return {
        ...item,
        children: getNewLoadData(item.children, node, points),
      };
    }
  });
}

function isSelected(tmp, selectedIds, selectedOptions) {
  let isSelected = false;
  selectedIds.forEach(id => {
    if (selectedOptions[id].name.value === `${tmp.deviceType}_${tmp.pointCode}`) {
      isSelected = true;
      return;
    }
  });
  return isSelected;
}

function getPointTypeList(pointType) {
  if (pointType === POINT_TYPE_CODE_MAP.AGG_DEVICE) {
    return ['ORI'];
  } else {
    return ['ORI', 'AGG_DEVICE', 'AGG_SPACE', 'CAL_DEVICE', 'CAL_SPACE'];
  }
}

/**
 * 动态加载数据源
 * @param {Array} data 树数据源
 */
function getPointsCascaderData(data) {
  return data.map(treeNode => {
    if (treeNode.children.length) {
      return {
        ...treeNode,
        id: treeNode.metaType + treeNode.metaCode,
        pId: treeNode.parentCode,
        value: treeNode.metaCode,
        title: treeNode.metaName,
        isLeaf: false,
        // disabled: true,
        children: getPointsCascaderData(treeNode.children),
        metaCode: 'K_' + treeNode.metaType + treeNode.metaCode,
      };
    }
    return {
      ...treeNode,
      id: treeNode.metaType + treeNode.metaCode,
      pId: treeNode.parentCode,
      value: treeNode.metaCode,
      title: treeNode.metaName,
      isLeaf: false,
      // disabled: true,
      children: treeNode.children.length ? treeNode.children : null,
      metaCode: 'K_' + treeNode.metaType + treeNode.metaCode,
    };
  });
}

function setPointDroppableValue(expressionData, id, value, name, type) {
  return expressionData.map(item => {
    if (item.id === id) {
      return {
        ...item,
        value,
        name,
        type,
      };
    }
    if (item.children && item.children.length) {
      return {
        ...item,
        children: setPointDroppableValue(item.children, id, value, name, type),
      };
    }
    return item;
  });
}

/**
 * 根据传入的deviceType，过滤出可选择的虚拟类型数据
 * @param {Array} data 树数据源
 * @param {String} deviceType 设备类型
 */
function filterDataSource(data, deviceType, spaceKeyMap) {
  return data
    .map(treeNode => {
      // 筛选空间类型，只能向上聚合 ,如选择空间聚合时，当前选择设备类型为包间，则只有设备类型和空间类型下的机柜类型，机列类型可参与聚合
      if (
        treeNode.metaType === DEVICE_TYPE_META_TYPE_KEY_MAP.C1 &&
        treeNode.metaStyle === DEVICE_SPACE_TYPE_KEY_MAP.SPACE
      ) {
        if (deviceType === spaceKeyMap.IDC) {
          const children = treeNode.children.filter(({ metaCode }) => metaCode !== spaceKeyMap.IDC);
          return {
            ...treeNode,
            children,
          };
        }
        if (deviceType === spaceKeyMap.BLOCK) {
          const children = treeNode.children.filter(
            ({ metaCode }) => metaCode !== spaceKeyMap.IDC && metaCode !== spaceKeyMap.BLOCK
          );
          return {
            ...treeNode,
            children,
          };
        }
        if (deviceType === spaceKeyMap.ROOM) {
          const children = treeNode.children.filter(
            ({ metaCode }) => metaCode === spaceKeyMap.COLUMN || metaCode === spaceKeyMap.GRID
          );
          return {
            ...treeNode,
            children,
          };
        }
        if (deviceType === spaceKeyMap.COLUMN) {
          const children = treeNode.children.filter(
            ({ metaCode }) => metaCode === spaceKeyMap.GRID
          );
          return {
            ...treeNode,
            children,
          };
        }
        if (deviceType === spaceKeyMap.GRID) {
          const children = treeNode.children.filter(
            ({ metaCode }) =>
              metaCode !== spaceKeyMap.IDC &&
              metaCode !== spaceKeyMap.BLOCK &&
              metaCode !== spaceKeyMap.ROOM &&
              metaCode !== spaceKeyMap.COLUMN &&
              metaCode !== spaceKeyMap.GRID
          );
          return {
            ...treeNode,
            children,
          };
        }
        return treeNode;
      } else {
        return {
          ...treeNode,
          children: filterDataSource(treeNode.children, deviceType, spaceKeyMap),
        };
      }
    })
    .filter(Boolean);
}
