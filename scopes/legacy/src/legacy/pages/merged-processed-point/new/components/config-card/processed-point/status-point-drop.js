import React, { Component } from 'react';
import { Droppable } from 'react-beautiful-dnd';
import { connect } from 'react-redux';

import PlusCircleOutlined from '@ant-design/icons/es/icons/PlusCircleOutlined';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Tag } from '@manyun/base-ui.ui.tag';

import { mergedProcessesdPointActions } from '@manyun/dc-brain.legacy.redux/actions/mergedProcessesdPointActions';

import { DropPointNameWrapper } from './styled';

const getItemStyle = isDragging => {
  if (isDragging) {
    return {
      background: `var(--${prefixCls}-success-color)`,
    };
  }
};

export class StatusPointDrop extends Component {
  addNewPoint = index => {
    this.props.addPointInStatus(index);
  };

  render() {
    const { droppableId, index, name, isLast } = this.props;
    return (
      <Droppable droppableId={`pointName_$$_${droppableId}`} direction="horizontal">
        {(provided, snapshot) => {
          return (
            <DropPointNameWrapper
              ref={provided.innerRef}
              {...provided.droppableProps}
              style={getItemStyle(snapshot.isDraggingOver)}
            >
              {isLast && (
                <PlusCircleOutlined
                  style={{ color: `var(--${prefixCls}-success-color)` }}
                  onClick={() => this.addNewPoint(index)}
                />
              )}
              &nbsp;&nbsp;
              {name && <Tag color="#1890ff">{name}</Tag>}
              {provided.placeholder}
            </DropPointNameWrapper>
          );
        }}
      </Droppable>
    );
  }
}

const mapDispatchToProps = {
  addPointInStatus: mergedProcessesdPointActions.addPointInStatus,
};

export default connect(null, mapDispatchToProps)(StatusPointDrop);
