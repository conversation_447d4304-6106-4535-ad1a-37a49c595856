import React from 'react';
import { Droppable } from 'react-beautiful-dnd';
import { connect } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';
import { mergedProcessesdPointActions } from '@manyun/dc-brain.legacy.redux/actions/mergedProcessesdPointActions';

import { ConditionalDraggable } from './conditional-draggable';
import { DropZoneActionWrapper, DropZoneWrapper } from './styled';
import PointValidate from './validate';

const getItemStyle = isDragging => {
  if (isDragging) {
    return {
      background: 'var(--border-color-base)',
    };
  }
};

export function DropZone({ fields, changeIsValidate, precision, treeMode }) {
  function getPointArr(data) {
    return data
      .map(item => {
        if (item.type === 'point') {
          return item.value;
        } else {
          if (item.children) {
            return getPointArr(item.children);
          }
          return null;
        }
      })
      .filter(Boolean);
  }

  function getDisabled(fields) {
    const data = fields ? getPointArr(fields) : [];
    if (data.length && fields.length && precision) {
      return false;
    }
    return true;
  }
  return (
    <div style={{ borderColor: 'var(--border-color-special)' }}>
      <Droppable droppableId="fileds" direction="horizontal">
        {(provided, snapshot) => (
          <DropZoneWrapper
            {...provided.droppableProps}
            ref={provided.innerRef}
            style={getItemStyle(snapshot.isDraggingOver)}
          >
            {fields.map((field, index) => (
              <ConditionalDraggable
                key={field.draggableId}
                index={index}
                {...field}
                treeMode={treeMode}
              />
            ))}
            {provided.placeholder}
          </DropZoneWrapper>
        )}
      </Droppable>

      <DropZoneActionWrapper>
        <GutterWrapper flex justifyContent="flex-end">
          <Button
            disabled={getDisabled()}
            size="small"
            type="primary"
            data-type="reset-btn"
            onClick={() => changeIsValidate()}
          >
            验证
          </Button>
        </GutterWrapper>
      </DropZoneActionWrapper>

      <PointValidate />
    </div>
  );
}

const mapStateToProps = ({
  mergedProcessesdPoint: {
    expressionData,
    processedPointList: { precision },
  },
}) => {
  return {
    fields: expressionData,
    precision: precision.value,
  };
};
const mapDispatchToProps = {
  changeIsValidate: mergedProcessesdPointActions.changeIsValidate,
};
export const ConnectedDropZone = connect(mapStateToProps, mapDispatchToProps)(DropZone);
