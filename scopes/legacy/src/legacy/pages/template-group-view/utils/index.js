import { useSelector } from 'react-redux';

import { selectSpaceEntities } from '@manyun/resource-hub.state.space';

import { getCitiesTree } from '@manyun/dc-brain.legacy.redux/selectors/commonSelectors';
import { getEquipmentIdcDevices } from '@manyun/dc-brain.legacy.redux/selectors/equipmentSelector';
import { generateTreeData } from '@manyun/dc-brain.legacy.utils';

export function getDeviceChildData(space, idcDevices) {
  let allIdcDevices = [];
  Object.keys(idcDevices).forEach(item => {
    allIdcDevices = [...allIdcDevices, ...idcDevices[item]];
  });
  const tree = getTreeLeaf(space.treeList);
  return {
    ...space,
    treeList: getDevceTreeList(tree, idcDevices),
    parallelList: [...space.parallelList, ...allIdcDevices],
  };
}

function getTreeLeaf(tree) {
  return tree.map(item => {
    if (item.metaType === 'ROOM') {
      return item;
    } else {
      if (!item.children || !item.children.length) {
        return { ...item, isLeaf: true };
      }
      return {
        ...item,
        children: getTreeLeaf(item.children),
      };
    }
  });
}

export function getDevceTreeList(treeList, idcDevices) {
  return treeList.map(item => {
    if (item.metaType === 'ROOM' && idcDevices[item.metaCode]) {
      return {
        ...item,
        children: idcDevices[item.metaCode],
      };
    }
    if (!item.children) {
      return item;
    }
    return {
      ...item,
      children: getDevceTreeList(item.children, idcDevices),
    };
  });
}

/**
 * 找出需要展示的节点 key 的集合。
 * 规则：若父节点和子节点都被选中，则不展示子节点。
 * @param {string[]} checkedKeys 被选中节点的 key 的集合
 * @param {Array} treeData 🌲数据源 (拉平后的数组)
 */
export function getVisibleKeys(checkedKeys, treeData, nodePathCache) {
  const rootNode = treeData[0];
  if (!rootNode) {
    throw Error('root node required.');
  }
  const rootNodeKey = rootNode.key;
  if (checkedKeys.includes(rootNodeKey)) {
    return [rootNodeKey];
  }
  const keys = [];
  let intermediateKeys = []; // /用于缓存中间路径的 node key
  checkedKeys.forEach(checkedKey => {
    if (intermediateKeys.includes(checkedKey)) {
      return;
    }
    const checkedNode = treeData.find(({ metaCode }) => metaCode === checkedKey);
    if (!checkedNode) {
      // eslint-disable-next-line
      console.log(`node(key: ${checkedKey}) not found, it will be ignored.`);
      return;
    }
    const nodePath = nodePathCache[checkedKey];
    if (!nodePath) {
      // eslint-disable-next-line
      console.log(`nodePath(key: ${checkedKey}) not found, it will be ignored.`);
      return;
    }
    for (let index = 0; index < nodePath.length; index++) {
      const nodeKey = nodePath[index];
      if (checkedKeys.includes(nodeKey)) {
        if (!keys.includes(nodeKey)) {
          keys.push(nodeKey);
        }
        intermediateKeys = [
          ...intermediateKeys,
          ...nodePath.slice(
            index + 1,
            nodePath.length - 1 /* 最后一项是 checkedKey 本身，可以在这步丢弃 */
          ),
        ];
        break;
      }
    }
  });
  return keys;
}

/**
 * 自定义hook
 * 返回所有空间数据(区域、机房、楼栋、包间、设备)
 * @returns  treeList parallelList
 */
export function useRegionSpaces({ nodeTypes, needVirtualBlock = true }) {
  const cities = useSelector(getCitiesTree);
  const _spaces = useSelector(selectSpaceEntities());
  const spaces = _spaces.filter(resource => {
    if (!needVirtualBlock) {
      const codes = resource.code.split('.');
      if (resource.type === 'BLOCK' && codes[1] === codes[0]) {
        return false;
      }
    }
    return true;
  });

  const idcDevices = useSelector(getEquipmentIdcDevices);

  let region = [];
  (cities || []).forEach(city => {
    region = region.concat(
      (city.children || []).map(item => ({
        metaCode: item.value,
        metaName: item.label,
        metaType: 'REGION',
        parentCode: null,
      }))
    );
  });
  const allIdcDevices = Object.keys(idcDevices).reduce((arr, roomCode) => {
    arr = [
      ...arr,
      ...idcDevices[roomCode].map(device => ({
        metaType: 'DEVICE',
        metaCode: device.metaCode,
        metaName: device.metaName,
        parentCode: roomCode,
      })),
    ];
    return arr;
  }, []);

  const parallelList = [
    ...region,
    ...spaces.map(space => ({
      ...space,
      metaCode: space.code,
      metaName: space.name,
      metaType: space.type,
    })),
    ...allIdcDevices,
  ];

  const treeList = generateTreeData(parallelList, {
    key: 'metaCode',
    typeKey: 'metaType',
    parentKey: 'parentCode',
    nodeTypes: nodeTypes,
    getNode: data => {
      let node = {};
      if (data.metaType === 'IDC') {
        node = {
          ...data,
          label: data.metaCode,
          value: data.metaCode,
        };
      }
      node = {
        ...data,
        label: data.metaName,
        value: data.metaCode,
      };
      return node;
    },
  });

  return { treeList, parallelList };
}

/**
 * @param {*} cities
 * @param {*} spaces
 * @param {*} idcDevices
 * @returns treeList parallelList  返回所有空间数据(区域、机房、楼栋、包间、设备)
 */
export function mergeRegionSpaces(cities, spaces, idcDevices) {
  let region = [];
  (cities || []).forEach(city => {
    region = region.concat(
      (city.children || []).map(item => ({
        metaCode: item.value,
        metaName: item.label,
        metaType: 'REGION',
        parentCode: null,
      }))
    );
  });
  const allIdcDevices = Object.keys(idcDevices).reduce((arr, roomCode) => {
    arr = [
      ...arr,
      ...idcDevices[roomCode].map(device => ({
        metaType: 'DEVICE',
        metaCode: device.metaCode,
        metaName: device.metaName,
        parentCode: roomCode,
      })),
    ];
    return arr;
  }, []);
  const parallelList = [
    ...region,
    ...spaces.map(space => ({
      ...space,
      metaCode: space.code,
      metaName: space.name,
      metaType: space.type,
    })),
    ...allIdcDevices,
  ];
  const treeList = generateTreeData(parallelList, {
    key: 'metaCode',
    typeKey: 'metaType',
    parentKey: 'parentCode',
    nodeTypes: ['REGION', 'IDC', 'BLOCK', 'ROOM', 'DEVICE'],
    getNode: data => {
      let node = {};
      if (data.metaType === 'IDC') {
        node = {
          ...data,
          label: data.metaCode,
          value: data.metaCode,
        };
      }
      node = {
        ...data,
        label: data.metaName,
        value: data.metaCode,
      };
      return node;
    },
  });
  return { parallelList, treeList };
}
