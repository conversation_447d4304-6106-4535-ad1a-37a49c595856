import React, { Component } from 'react';

import styled from 'styled-components';

import { Empty } from '@manyun/base-ui.ui.empty';

import { GutterWrapper, TinyCard } from '@manyun/dc-brain.legacy.components';

import LocationDeviceTree from './components/location-device-tree';
import ConnectTemplateCard from './components/template-card';

const ScrollWrapper = styled.div`
  /* 32px: “完成按钮”的高度；1rem: "GutterWrapper" 相邻元素的间距 */
  height: ${({ height }) => height};
  overflow-y: auto;
  min-width: 300px;
`;
const cardHeight = `calc(var(--content-height) - 48px)`;

class AreaConfig extends Component {
  state = {
    selectedNode: null,
  };

  render() {
    const { selectedNode } = this.state;
    return (
      <GutterWrapper flex>
        <ScrollWrapper>
          <TinyCard
            title="生效域"
            bodyStyle={{
              height: cardHeight,
              overflow: 'auto',
            }}
          >
            <LocationDeviceTree setSelectedNode={value => this.setState({ selectedNode: value })} />
          </TinyCard>
        </ScrollWrapper>
        {selectedNode ? (
          <GutterWrapper flexN={1} mode="vertical">
            <ScrollWrapper height={`calc(${cardHeight} + 48px)`}>
              <ConnectTemplateCard height={cardHeight} selectedNode={selectedNode} />
            </ScrollWrapper>
          </GutterWrapper>
        ) : (
          <GutterWrapper flexN={1} mode="vertical">
            <TinyCard bodyStyle={{ height: `calc(${cardHeight} + 48px)` }}>
              <GutterWrapper style={{ height: '100%' }} flex center>
                <Empty
                  image={<img alt="暂无数据" src="/images/empty.png" />}
                  // description=""
                />
              </GutterWrapper>
            </TinyCard>
          </GutterWrapper>
        )}
      </GutterWrapper>
    );
  }
}
export default AreaConfig;
