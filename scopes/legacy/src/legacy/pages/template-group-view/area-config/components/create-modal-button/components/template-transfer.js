import React from 'react';

import LeftOutlined from '@ant-design/icons/es/icons/LeftOutlined';
import RightOutlined from '@ant-design/icons/es/icons/RightOutlined';
import differenceBy from 'lodash/differenceBy';

import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';

import { GutterWrapper, TinyCard, TinyTable } from '@manyun/dc-brain.legacy.components';

function TemplateTransfer({
  templateTableData,
  pagination,
  paginationChangeHandler,
  selectedTemplates,
  setSelectedTemplates,
  selectedTemplatesInleft,
  setSelectedTemplatesInLeft,
  selectedTemplatesInRight,
  setSelectedTemplatesInRight,
  searchTemplateTableData,
  setAllSelectedTemplates,
  searchSelectedTemplateTableData,
  connectTempates,
}) {
  const columns = [
    {
      title: '模板组名称',
      dataIndex: 'name',
    },
    {
      title: '状态',
      dataIndex: 'available',
      fixed: 'right',
      render: available => (available ? '启用' : '停用'),
    },
  ];

  return (
    <GutterWrapper mode="horizontal" flex style={{ overflowY: 'auto', padding: '0 4px' }}>
      <TinyCard
        bordered
        style={{ width: '46%', height: '100%' }}
        title={`${selectedTemplatesInleft.length}/${templateTableData.total}条`}
      >
        <GutterWrapper mode="vertical">
          <Input.Search onSearch={value => searchTemplateTableData(value)} />
          <TinyTable
            rowKey="id"
            selectRowsSpreadPage={true}
            dataSource={templateTableData.data}
            columns={columns}
            scroll={{ x: 'max-content' }}
            rowSelection={{
              getCheckboxProps: item => ({
                disabled:
                  selectedTemplates.map(({ id }) => id).includes(item.id) ||
                  connectTempates.map(({ id }) => id).includes(item.id),
              }),
              selectedRowKeys: selectedTemplatesInleft.map(({ id }) => id),
              selectedRows: selectedTemplatesInleft,
              onChange: (keys, rows) => {
                setSelectedTemplatesInLeft(rows);
              },
            }}
            pagination={{
              total: templateTableData.total,
              current: pagination.pageNum,
              pageSize: pagination.pageSize,
              onChange: paginationChangeHandler,
            }}
          />
        </GutterWrapper>
      </TinyCard>
      <GutterWrapper mode="vertical" style={{ width: '4%', alignSelf: 'center' }}>
        <Button
          icon={<RightOutlined />}
          size="small"
          type={selectedTemplatesInleft.length ? 'primary' : 'default'}
          onClick={() => {
            setSelectedTemplates([...selectedTemplates, ...selectedTemplatesInleft]);
            setAllSelectedTemplates([...selectedTemplates, ...selectedTemplatesInleft]);
            setSelectedTemplatesInLeft([]);
          }}
        />
        <Button
          icon={<LeftOutlined />}
          size="small"
          type={selectedTemplatesInRight.length ? 'primary' : 'default'}
          onClick={() => {
            const newData = differenceBy(selectedTemplates, selectedTemplatesInRight, 'id'); // 取消选择的模板组
            setSelectedTemplates(newData);
            setAllSelectedTemplates(newData);
            setSelectedTemplatesInRight([]);
          }}
        />
      </GutterWrapper>
      <TinyCard
        bordered
        style={{ width: '46%', height: '100%' }}
        extra={
          <Button
            type="link"
            size="small"
            onClick={() => {
              setSelectedTemplates([]);
              setAllSelectedTemplates([]);
              setSelectedTemplatesInRight([]);
            }}
          >
            清除
          </Button>
        }
        title={`${selectedTemplatesInRight.length}/${selectedTemplates.length}条`}
      >
        <GutterWrapper mode="vertical">
          <Input.Search onSearch={value => searchSelectedTemplateTableData(value)} />
          <TinyTable
            rowKey="id"
            selectRowsSpreadPage={true}
            dataSource={selectedTemplates}
            columns={columns}
            scroll={{ x: 'max-content' }}
            rowSelection={{
              selectedRowKeys: selectedTemplatesInRight.map(({ id }) => id),
              selectedRows: selectedTemplatesInRight,
              onChange: (keys, rows) => {
                setSelectedTemplatesInRight(rows);
              },
            }}
          />
        </GutterWrapper>
      </TinyCard>
    </GutterWrapper>
  );
}

export default TemplateTransfer;
