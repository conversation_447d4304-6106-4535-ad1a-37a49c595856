import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import styled from 'styled-components';

import { Input } from '@manyun/base-ui.ui.input';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Tag } from '@manyun/base-ui.ui.tag';

import { User } from '@manyun/auth-hub.ui.user';

import { GutterWrapper, TinyTable } from '@manyun/dc-brain.legacy.components';
import {
  saveSelectTemplateGroupAction,
  searchTemplteFromNameAction,
  templateGroupViewActions,
} from '@manyun/dc-brain.legacy.redux/actions/templateGroupViewActions';
import { generateViewTemplteGroupConfigLocation } from '@manyun/dc-brain.legacy.utils/urls';

const { Search } = Input;

const columns = ctx => [
  {
    title: '模板组名称',
    dataIndex: 'name',
    render: (text, record) => (
      <Link
        type="link"
        to={generateViewTemplteGroupConfigLocation({
          id: record.id,
          name: record.name,
          available: record.available,
          lastOperatorName: record.lastOperatorName,
        })}
        onClick={() => ctx.review(record)}
      >
        {record.name}
      </Link>
    ),
  },
  {
    title: '子模版数',
    dataIndex: 'groupNum',
  },
  {
    title: '更新人',
    dataIndex: 'lastOperatorName',
    render: (__, { lastOperatorName, lastOperator }) => (
      <User.Link id={lastOperator} name={lastOperatorName} />
    ),
  },
  {
    title: '状态',
    dataIndex: 'available',
    render: available => <Tag color="green">{available === true ? '启用' : '停用'}</Tag>,
  },
  {
    title: '操作',
    key: '__actions',
    render: (__, record) => (
      <span>
        <Link
          type="link"
          to={generateViewTemplteGroupConfigLocation({
            id: record.id,
            name: record.name,
            available: record.available,
            lastOperatorName: record.lastOperatorName,
          })}
          onClick={() => ctx.review(record)}
        >
          查看
        </Link>
      </span>
    ),
  },
];

class AreaIdcRoom extends Component {
  state = {
    selectedRowKeys: [],
    selectedRows: [],
    pageNo: 1,
    pageSize: 10,
  };

  handleCancel = () => {
    this.props.closeaddTemplateToAreaVisible();
    this.props.closeIdcRoomTemplateVisible();
  };

  onSelectChange = (selectedRowKeys, selectedRows) => {
    this.setState({
      selectedRowKeys,
      selectedRows,
    });
    this.props.saveSelectTemplateGroupAction(selectedRows);
  };

  searchTemplate = value => {
    const { allTemplateGroupList, saveallTemplateGroupList } = this.props;
    if (value) {
      this.props.searchTemplteFromNameAction({ name: value, allTemplateGroupList });
    } else {
      this.props.fetchAllTemplateGroupSuccess(saveallTemplateGroupList);
    }
    this.setState({
      pageNo: 1,
    });
  };

  review = row => {
    this.props.saveBasicInfo({ ...row, configId: row.id });
  };

  onChangePage = (pageNo, pageSize) => {
    this.setState({
      pageNo,
      pageSize,
    });
  };

  render() {
    const { selectedRowKeys, selectedRows, pageNo, pageSize } = this.state;
    const { selectTemplateGroup, className } = this.props;
    return (
      <Modal
        title="添加模板组"
        width="60%"
        visible={this.props.addTemplateToAreaVisible}
        onOk={this.handleCancel}
        onCancel={this.handleCancel}
        onClose={this.handleCancel}
        className={className}
        destroyOnClose={true}
      >
        <GutterWrapper mode="vertical">
          <TinyTable
            rowKey="id"
            dataSource={this.props.allTemplateGroupList}
            loading={this.props.loading}
            columns={columns(this)}
            rowSelection={{
              selectedRowKeys,
              selectedRows,
              onChange: this.onSelectChange,
            }}
            size="small"
            pagination={{
              total: this.props.allTemplateGroupList.length,
              current: pageNo,
              pageSize: pageSize,
              onChange: this.onChangePage,
            }}
            actionsWrapperStyle={{ justifyContent: 'space-between' }}
            actions={[
              <span key="choiced">
                已选择：{selectTemplateGroup.length ? selectTemplateGroup.length : 0}
              </span>,
              <span key="search">
                <Search
                  allowClear
                  placeholder="请输入"
                  onSearch={this.searchTemplate}
                  style={{ width: 200 }}
                />
              </span>,
            ]}
          />
        </GutterWrapper>
      </Modal>
    );
  }
}

const mapStateToProps = ({
  templateGroupView: {
    loading,
    addTemplateToAreaVisible,
    allTemplateGroupList,
    selectTemplateGroup,
    saveallTemplateGroupList,
  },
}) => ({
  loading,
  addTemplateToAreaVisible,
  allTemplateGroupList,
  selectTemplateGroup,
  saveallTemplateGroupList,
});

const mapDispatchToProps = {
  closeaddTemplateToAreaVisible: templateGroupViewActions.changeaddTemplateToAreaVisible,
  saveSelectTemplateGroupAction,
  searchTemplteFromNameAction,
  closeIdcRoomTemplateVisible: templateGroupViewActions.changeAreaIdecRoomTreeTemplateVisible,
  fetchAllTemplateGroupSuccess: templateGroupViewActions.fetchAllTemplateGroupSuccess,
  saveBasicInfo: templateGroupViewActions.saveBasicInfo,
};

export const AreaIdcRoomConnect = connect(mapStateToProps, mapDispatchToProps)(AreaIdcRoom);
export const StyledAreaIdcRoomConnect = styled(AreaIdcRoomConnect)`
  .manyun-table-small {
    border: 0;
  }
`;

export default StyledAreaIdcRoomConnect;
