import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Popover } from '@manyun/base-ui.ui.popover';
import { Tag } from '@manyun/base-ui.ui.tag';

import { User } from '@manyun/auth-hub.ui.user';

import { GutterWrapper, TinyCard, TinyTable } from '@manyun/dc-brain.legacy.components';
import { NODE_TYPE_MAP } from '@manyun/dc-brain.legacy.components/relationship-connector/constants';
import TargetTreeTransfer from '@manyun/dc-brain.legacy.components/relationship-connector/target-tree-transfer';
import {
  connectTemplateInArea,
  deleteTemplateInAreaAction,
  fetchAllTemplateGroupAction,
  saveCheckedKeysAction,
  templateGroupViewActions,
} from '@manyun/dc-brain.legacy.redux/actions/templateGroupViewActions';
import { generateViewTemplteGroupConfigLocation } from '@manyun/dc-brain.legacy.utils/urls';

const columns = ctx => [
  {
    title: '模板组名称',
    dataIndex: 'name',
    render: (text, record) => (
      <Link
        type="link"
        to={generateViewTemplteGroupConfigLocation({
          id: record.id,
          name: record.name,
          available: record.available,
          lastOperatorName: record.lastOperatorName,
        })}
        onClick={() => ctx.review(record)}
      >
        {record.name}
      </Link>
    ),
  },
  {
    title: '子模版数',
    dataIndex: 'groupNum',
  },
  {
    title: '更新人',
    dataIndex: 'lastOperatorName',
    render: (__, { lastOperatorName, lastOperator }) => (
      <User.Link id={lastOperator} name={lastOperatorName} />
    ),
  },
  {
    title: '状态',
    dataIndex: 'available',
    render: available => (
      <Tag color={available ? 'green' : 'default'}>{available ? '启用' : '停用'}</Tag>
    ),
  },
  {
    title: '操作',
    dataIndex: '',
    render: (text, record) => (
      <span>
        <Link
          type="link"
          to={generateViewTemplteGroupConfigLocation({
            id: record.id,
            name: record.name,
            available: record.available,
            lastOperatorName: record.lastOperatorName,
          })}
          onClick={() => ctx.review(record)}
        >
          查看
        </Link>
        <Divider type="vertical" />
        <Popover
          content={
            <GutterWrapper>
              <p>{`确认删除模板组： ${record.name}吗？`}</p>
              <Button
                type="link"
                onClick={() => ctx.deleteTemplate(record)}
                style={{ padding: 0, height: 'auto', marginLeft: 0 }}
              >
                确认
              </Button>
            </GutterWrapper>
          }
          trigger="focus"
        >
          <Button type="link" style={{ padding: 0, height: 'auto' }}>
            删除
          </Button>
        </Popover>
      </span>
    ),
  },
];

class AreaIdcRoom extends Component {
  state = {
    pageNo: 1,
    pageSize: 10,
  };

  handleCancel = () => {
    this.props.closeIdcRoomTemplateVisible();
  };

  addTemplate = () => {
    this.props.fetchAllTemplateGroupAction();
    this.props.closeaddTemplateToAreaVisible();
    this.props.closeIdcRoomTemplateVisible();
  };

  deleteTemplate = row => {
    const { selectTemplateGroup } = this.props;
    this.props.deleteTemplateInAreaAction({ id: row.id, selectTemplateGroup });
  };

  choiceSelect = (checkedKeys, checkedNodes) => {
    if (checkedKeys && checkedKeys.length) {
      const NOAREA = checkedNodes.filter(
        item => item.type !== NODE_TYPE_MAP.AREA && item.type !== NODE_TYPE_MAP.ROOT
      );
      const IDC = NOAREA.filter(item => item.type !== NODE_TYPE_MAP.BUILDING);
      const BUILDING = NOAREA.filter(item => item.type !== NODE_TYPE_MAP.IDC);

      const newbuil = BUILDING.filter(item => {
        if (IDC.filter(i => item.parent && item.parent.key === i.key).length) {
          return false;
        }
        return true;
      });
      const list = IDC.concat(newbuil);
      this.props.saveCheckedIdcAndRoom(list);
    }
  };

  submitAreaAndTemppate = () => {
    const { selectTemplateGroup, checkedIdcAndBlock, searchAreaConfigCondition } = this.props;

    if (selectTemplateGroup.length && checkedIdcAndBlock.length) {
      let tempData = [];
      checkedIdcAndBlock.forEach(item => {
        selectTemplateGroup.forEach(items => {
          let temp = {
            configId: items.id,
            configType: 'SCHEME',
            targetId: item.key,
            targetType: item.type === 'BUILDING' ? 'BLOCK' : item.type,
          };

          tempData = [...tempData, temp];
        });
      });
      this.props.connectTemplateInArea({
        ...searchAreaConfigCondition,
        tempData,
      });
    } else {
      message.error('请选择区域和模板组');
    }
  };

  review = row => {
    this.props.saveBasicInfo({ ...row, configId: row.id });
  };

  onChangePage = (pageNo, pageSize) => {
    this.setState({
      pageNo,
      pageSize,
    });
  };

  render() {
    const { pageNo, pageSize } = this.state;
    return (
      <Modal
        title="生效域关联"
        width="80%"
        visible={this.props.areaIdecRoomTreeTemplateVisible}
        onOk={this.submitAreaAndTemppate}
        onCancel={this.handleCancel}
        onClose={this.handleCancel}
        destroyOnClose={true}
      >
        <Row>
          <Col xl={9}>
            <TargetTreeTransfer
              checkedKeys={[]}
              targetSelectPlaceholder="请输入"
              onChange={(checkedLeafKeys, visibleKeys, checkedKeys, checkedNodes) =>
                this.choiceSelect(checkedKeys, checkedNodes)
              }
              cardBodyStyle={{ maxHeight: 'calc(100vh - 385px)', overflow: 'auto' }}
            />
          </Col>
          <Col xl={1}></Col>
          <Col xl={14}>
            <TinyCard
              style={{ height: '100%', maxHeight: 'calc(100vh - 320px)', overflow: 'auto' }}
            >
              <TinyTable
                rowKey="id"
                size="small"
                dataSource={this.props.selectTemplateGroup}
                columns={columns(this)}
                actions={
                  <Button type="primary" onClick={this.addTemplate}>
                    添加模板组
                  </Button>
                }
                pagination={{
                  total: this.props.selectTemplateGroup.length,
                  current: pageNo,
                  pageSize: pageSize,
                  onChange: this.onChangePage,
                }}
              />
            </TinyCard>
          </Col>
        </Row>
      </Modal>
    );
  }
}

const mapStateToProps = ({
  templateGroupView: {
    loading,
    areaIdecRoomTreeTemplateVisible,
    selectTemplateGroup,
    checkedIdcAndBlock,
    searchAreaConfigCondition,
  },
}) => ({
  loading,
  areaIdecRoomTreeTemplateVisible,
  selectTemplateGroup,
  checkedIdcAndBlock,
  searchAreaConfigCondition,
});

const mapDispatchToProps = {
  closeIdcRoomTemplateVisible: templateGroupViewActions.changeAreaIdecRoomTreeTemplateVisible,
  saveCheckedKeysAction,
  closeaddTemplateToAreaVisible: templateGroupViewActions.changeaddTemplateToAreaVisible,
  fetchAllTemplateGroupAction,
  deleteTemplateInAreaAction,
  saveCheckedIdcAndRoom: templateGroupViewActions.saveCheckedIdcAndRoom,
  connectTemplateInArea,
  saveBasicInfo: templateGroupViewActions.saveBasicInfo,
};

export default connect(mapStateToProps, mapDispatchToProps)(AreaIdcRoom);
