import React, { useEffect, useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';

import { templateGroupViewService } from '@manyun/dc-brain.legacy.services';

import TemplateTransfer from './components/template-transfer';

function CreateModalButton({
  selectedNode,
  getTableDataIntree,
  getTableDataParams,
  selectedTreeNode,
  connectTempates,
}) {
  const [visible, setVisible] = useState(false);
  const [templateTableData, setTemplateTableData] = useState({ data: [], total: 0 });
  const [pagination, setPagination] = useState({ pageSize: 10, pageNum: 1 });
  const [selectedTemplates, setSelectedTemplates] = useState([]);
  const [selectedTemplatesInleft, setSelectedTemplatesInLeft] = useState([]);
  const [allSelectedTemplates, setAllSelectedTemplates] = useState([]);
  const [selectedTemplatesInRight, setSelectedTemplatesInRight] = useState([]);

  useEffect(() => {
    if (!visible) {
      return;
    }
    (async () => {
      const { response, error } = await templateGroupViewService.fetchTemplateGroupViewPage(
        pagination
      );
      if (error) {
        message.error(error);
      }
      if (response) {
        setTemplateTableData({
          ...response,
          data: response.data.map(item => {
            return {
              ...item,
              key: item.id,
            };
          }),
        });
      }
    })();
  }, [visible, pagination]);

  useEffect(() => {
    if (!visible) {
      setSelectedTemplates([]);
      setAllSelectedTemplates([]);
      setSelectedTemplatesInLeft([]);
      setSelectedTemplatesInRight([]);
    }
  }, [
    selectedTreeNode,
    setSelectedTemplates,
    setAllSelectedTemplates,
    setSelectedTemplatesInLeft,
    setSelectedTemplatesInRight,
    visible,
  ]);

  const paginationChangeHandler = (current, size) => {
    setPagination({ pageNum: current, pageSize: size });
  };

  const searchTemplateTableData = value => {
    setPagination({ ...pagination, name: value.trim() });
  };

  const searchSelectedTemplateTableData = value => {
    if (value.trim() === '') {
      setSelectedTemplates(allSelectedTemplates);
      return;
    }
    const newData = allSelectedTemplates.filter(({ name }) => {
      let index = name.indexOf(value);
      if (index > -1) {
        return true;
      } else {
        return false;
      }
    });
    setSelectedTemplates(newData);
  };

  const onOk = async () => {
    const params = allSelectedTemplates.map(({ id }) => {
      return {
        configId: id,
        configType: 'SCHEME',
        ...selectedNode,
      };
    });
    const { response, error } = await templateGroupViewService.fetchConnectAreaToTemplate(params);
    if (error) {
      message.error(error);
    }
    if (response) {
      message.success('关联成功');
      setVisible(false);
      getTableDataIntree(getTableDataParams);
    }
  };

  return (
    <>
      <Button type="primary" onClick={() => setVisible(!visible)}>
        关联
      </Button>
      <CreateModal
        visible={visible}
        setVisible={setVisible}
        templateTableData={templateTableData}
        pagination={pagination}
        paginationChangeHandler={paginationChangeHandler}
        selectedTemplates={selectedTemplates}
        setSelectedTemplates={setSelectedTemplates}
        selectedTemplatesInleft={selectedTemplatesInleft}
        setSelectedTemplatesInLeft={setSelectedTemplatesInLeft}
        selectedTemplatesInRight={selectedTemplatesInRight}
        setSelectedTemplatesInRight={setSelectedTemplatesInRight}
        searchTemplateTableData={searchTemplateTableData}
        setAllSelectedTemplates={setAllSelectedTemplates}
        searchSelectedTemplateTableData={searchSelectedTemplateTableData}
        onOk={onOk}
        allSelectedTemplates={allSelectedTemplates}
        connectTempates={connectTempates}
      />
    </>
  );
}
export default CreateModalButton;

function CreateModal({
  visible,
  setVisible,
  templateTableData,
  pagination,
  paginationChangeHandler,
  selectedTemplates,
  setSelectedTemplates,
  selectedTemplatesInleft,
  setSelectedTemplatesInLeft,
  selectedTemplatesInRight,
  setSelectedTemplatesInRight,
  searchTemplateTableData,
  setAllSelectedTemplates,
  searchSelectedTemplateTableData,
  onOk,
  allSelectedTemplates,
  connectTempates,
}) {
  return (
    <Modal
      title="关联模板组"
      visible={visible}
      width={800}
      onCancel={() => setVisible(false)}
      onOk={() => onOk()}
      okButtonProps={{
        disabled: !allSelectedTemplates.length,
      }}
      bodyStyle={{ height: 452 }}
    >
      <TemplateTransfer
        style={{ width: '100%' }}
        templateTableData={templateTableData}
        pagination={pagination}
        paginationChangeHandler={paginationChangeHandler}
        selectedTemplates={selectedTemplates}
        setSelectedTemplates={setSelectedTemplates}
        selectedTemplatesInleft={selectedTemplatesInleft}
        setSelectedTemplatesInLeft={setSelectedTemplatesInLeft}
        selectedTemplatesInRight={selectedTemplatesInRight}
        setSelectedTemplatesInRight={setSelectedTemplatesInRight}
        searchTemplateTableData={searchTemplateTableData}
        setAllSelectedTemplates={setAllSelectedTemplates}
        searchSelectedTemplateTableData={searchSelectedTemplateTableData}
        connectTempates={connectTempates}
      />
    </Modal>
  );
}
