import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Tag } from '@manyun/base-ui.ui.tag';

import { TinyCard, TinyTable, UserLink } from '@manyun/dc-brain.legacy.components';
import { cancleConnectArea } from '@manyun/dc-brain.legacy.redux/actions/templateGroupViewActions';
import { templateGroupViewService } from '@manyun/dc-brain.legacy.services';

import CreateModalButton from '../create-modal-button';

function ConnectTemplateCard({ height, selectedNode, cancleConnectArea }) {
  const [dataAndTotal, setDataAndTotal] = useState({ data: [], total: 0 });
  const [pagination, setPagination] = useState({ pageSize: 10, pageNum: 1 });
  const [searchValue, setSearchValue] = useState('');

  useEffect(() => {
    if (selectedNode) {
      getTableData({
        ...selectedNode,
        ...pagination,
      });
    }
  }, [selectedNode, pagination]);

  const searchPoint = value => {
    getTableData({
      ...selectedNode,
      ...pagination,
      name: value.trim(),
    });
  };

  const paginationChangeHandler = (current, size) => {
    setPagination({ pageNum: current, pageSize: size });
  };

  async function getTableData(params) {
    const { response, error } = await templateGroupViewService.fetchTemplateGroupViewPage(params);
    if (error) {
      message.error(error);
    }
    if (response) {
      setDataAndTotal(response);
    }
  }

  const cancleConnect = row => {
    return new Promise(resolve => {
      cancleConnectArea({
        params: { ...selectedNode, configId: row.id, configType: 'SCHEME' },
        successCb: () => {
          resolve(true);
          setPagination({ pageSize: 10, pageNum: 1 });
          setSearchValue('');
        },
        errorCb: () => {
          resolve(false);
        },
      });
    });
  };

  return (
    <TinyCard
      title="关联模板组"
      bodyStyle={{
        height: height,
        overflow: 'auto',
      }}
    >
      <TinyTable
        rowKey="id"
        dataSource={dataAndTotal.data}
        columns={getColumns({ cancleConnect })}
        scroll={{ x: 'max-content' }}
        actionsWrapperStyle={{ justifyContent: 'space-between' }}
        actions={[
          <CreateModalButton
            key="create"
            selectedNode={selectedNode}
            getTableDataParams={{
              ...selectedNode,
              ...pagination,
            }}
            getTableDataIntree={getTableData}
            selectedTreeNode={selectedNode}
            connectTempates={dataAndTotal.data}
          />,
          <Input.Search
            key="search"
            placeholder="输入模板组名称关键字后回车搜索"
            value={searchValue}
            onChange={({ target: { value } }) => setSearchValue(value)}
            onSearch={searchPoint}
            style={{ width: 260 }}
          />,
        ]}
        pagination={{
          total: dataAndTotal.total,
          current: pagination.pageNum,
          pageSize: pagination.pageSize,
          onChange: paginationChangeHandler,
        }}
      />
    </TinyCard>
  );
}
const mapDispatchToProps = {
  cancleConnectArea,
};
export default connect(null, mapDispatchToProps)(ConnectTemplateCard);

const getColumns = ({ cancleConnect }) => [
  {
    title: '模板组名称',
    dataIndex: 'name',
  },
  {
    title: '子模板数',
    dataIndex: 'groupNum',
  },
  {
    title: '状态',
    dataIndex: 'available',
    render: available => (
      <Tag color={available ? 'green' : 'default'}>{available ? '启用' : '停用'}</Tag>
    ),
  },
  {
    title: '更新人',
    dataIndex: 'lastOperator',
    render: (lastOperator, record) => (
      <UserLink userName={record.lastOperatorName} userId={lastOperator} />
    ),
  },
  {
    title: '更新时间',
    dataIndex: 'gmtModified',
    dataType: 'datetime',
  },
  {
    title: '操作',
    dataIndex: 'actions',
    fixed: 'right',
    render: (_, record) => (
      <DeleteConfirm
        variant="popconfirm"
        title={`你确定要取消关联${record.name}吗？`}
        onOk={() => cancleConnect(record)}
      >
        <Button type="link" compact>
          取消关联
        </Button>
      </DeleteConfirm>
    ),
  },
];
