import React, { useEffect, useRef, useState } from 'react';
import { connect } from 'react-redux';

import debounce from 'lodash.debounce';

import { AutoComplete } from '@manyun/base-ui.ui.auto-complete';
import { Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Typography } from '@manyun/base-ui.ui.typography';

import { fetchPageDevices } from '@manyun/resource-hub.service.fetch-page-devices';

import { ApiTree } from '@manyun/dc-brain.legacy.components';
import { useRegionSpaces } from '@manyun/dc-brain.legacy.pages/template-group-view/utils';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import { equipmentActions } from '@manyun/dc-brain.legacy.redux/actions/equipmentActions';
import { equipmentManageService } from '@manyun/dc-brain.legacy.services';

function LocationDeviceTree({
  syncCommonData,
  setSelectedNode,
  defaultExpandedKeys,
  setIdcDevices,
  checkable,
  onChecked,
  checkedKeys,
  disabled,
  treeStyle,
  needVirtualBlock,
}) {
  const apiTreeRef = useRef();
  const wrapperRef = useRef();
  const [selectedKeys, setSelectedKeys] = useState([]);
  const { treeList, parallelList } = useRegionSpaces({
    nodeTypes: ['REGION', 'IDC', 'BLOCK'],
    needVirtualBlock,
  });
  const [searchValue, setSearchValue] = useState('');
  const [searchOptions, setSearchOptions] = useState([]);

  const [spinning, setSpinning] = useState(false);
  useEffect(() => {
    syncCommonData({ strategy: { space: 'IF_NULL', citiesTree: 'IF_NULL' } });
  }, [syncCommonData]);

  const onSelectTree = (selectedKeys, { node }) => {
    if (node.dataRef.metaType === 'REGION') {
      return;
    }
    if (typeof setSelectedNode === 'function') {
      setSelectedNode({
        targetId: node.dataRef.metaCode,
        targetType: node.dataRef.metaType,
      });
    }
    setSelectedKeys(selectedKeys);
  };

  const onCheck = (checkedKeys, info) => {
    onChecked(checkedKeys, info);
  };

  const onSelect = (value, options) => {
    const { metaType } = options;
    if (metaType === 'DEVICE') {
      const parentCode = value.split('.').slice(0, 3).join('.');
      apiTreeRef?.current?.setState({
        expandedKeys: Array.from(
          new Set([...apiTreeRef?.current?.state?.expandedKeys, parentCode])
        ),
        autoExpandParent: true,
      });
    } else {
      apiTreeRef?.current?.setState({
        expandedKeys: Array.from(
          new Set([...apiTreeRef?.current?.state?.expandedKeys, options.parentCode])
        ),
        autoExpandParent: true,
      });
    }
    setSpinning(true);
    const timer = setTimeout(
      () => {
        setSpinning(false);
        setSelectedKeys([value]);
        document.querySelector('.manyun-tree-node-selected')?.scrollIntoView();
        clearTimeout(timer);
      },
      metaType === 'DEVICE' ? 2000 : 300
    );
  };

  const handleSearchValueChange = async value => {
    if (!value) {
      setSearchValue(value);
      setSearchOptions([]);
      return;
    }
    const { error, data } = await fetchPageDevices({ name: value, pageNum: 1, pageSize: 2000 });
    if (error) {
      message.error(error.message);
      return;
    }
    setSearchOptions(getSearchOptions({ parallelList, deviceList: data.data, value }));
  };

  if (!treeList || !treeList.length) {
    return null;
  }

  return (
    <>
      <AutoComplete
        style={{ width: 418 }}
        options={searchOptions}
        value={searchValue}
        onSelect={onSelect}
        onChange={debounce(handleSearchValueChange, 200)}
      >
        <Input.Search value={searchValue} onChange={e => setSearchValue(e.target.value)} />
      </AutoComplete>
      <Spin spinning={spinning}>
        <ApiTree
          ref={apiTreeRef}
          disabled={disabled}
          wrapperRef={wrapperRef}
          checkable={checkable}
          defaultExpandedKeys={defaultExpandedKeys}
          selectedKeys={selectedKeys}
          checkedKeys={checkedKeys}
          fieldNames={{
            key: 'metaCode',
            title: ({ metaName, metaType }, { highlightedText }) => {
              if (metaType === 'REGION') {
                return (
                  <span
                    style={{
                      cursor: 'text',
                      color: highlightedText ? 'inherit' : 'var(--text-color)',
                    }}
                  >
                    {metaName}
                  </span>
                );
              }
              return highlightedText || metaName;
            },
            parentKey: 'metaCode',
          }}
          treeNodeFilterProp="metaName"
          dataService={() => Promise.resolve({ parallelList, treeList })}
          childNodesDataService={node => childNodesDataService(node, setIdcDevices)}
          placeholder="请输入位置信息搜索"
          treeStyle={{
            ...(treeStyle ? treeStyle : {}),
            overflow: 'auto',
          }}
          onSelect={onSelectTree}
          onCheck={onCheck}
        />
      </Spin>
    </>
  );
}

const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
  setIdcDevices: equipmentActions.setIdcDevices,
};

export default connect(undefined, mapDispatchToProps)(LocationDeviceTree);

async function childNodesDataService({ metaType, metaCode }, setIdcDevices) {
  let children = [];
  if (metaType === 'ROOM') {
    const [idcTag, blockTag, roomTags] = metaCode.split('.');
    const { response: devices } = await equipmentManageService.fetchEquipmentListPage({
      idcTag,
      blockTag,
      roomTags: [roomTags],
      pageNum: 1,
      pageSize: 5000,
    });
    if (devices) {
      const newDevices = devices.data.map(item => {
        return {
          ...item,
          metaName: item.name,
          metaCode: `${metaCode}.${item.guid}`,
          metaType: 'DEVICE',
          isLeaf: true,
        };
      });
      children = newDevices;
      setIdcDevices({
        [metaCode]: newDevices,
      });
    }
  }
  return Promise.resolve(children);
}

const getSearchOptions = ({ parallelList, deviceList, value }) => {
  const options = [];
  //设备
  const deviceListOptions = deviceList.map(device => {
    const { spaceGuid, guid, name } = device;
    const { roomGuid } = spaceGuid;

    return {
      ...device,
      label: (
        <Row justify="space-between">
          <Typography.Text>{name}</Typography.Text>
          <Typography.Text type="secondary">{roomGuid?.replace(/\./g, '/') || ''}</Typography.Text>
        </Row>
      ),
      value: `${roomGuid}.${guid}`,
      isLeaf: true,
      metaCode: `${roomGuid}.${guid}`,
      metaType: 'DEVICE',
    };
  });

  const blockList = [];
  const roomList = [];
  if (parallelList.length) {
    parallelList.forEach(item => {
      if (item.metaType === 'BLOCK' && item.name.includes(value)) {
        blockList.push({
          ...item,
          label: item.metaCode.replace(/\./g, '/'),
          value: item.metaCode,
        });
      }

      if (item.metaType === 'ROOM' && item.name.includes(value)) {
        roomList.push({
          ...item,
          label: item.metaCode.replace(/\./g, '/'),
          value: item.metaCode,
        });
      }
    });
  }

  if (blockList.length) {
    options.push({
      label: '楼栋',
      options: blockList,
    });
  }

  if (roomList.length) {
    options.push({
      label: '包间',
      options: roomList,
    });
  }

  if (deviceListOptions.length) {
    options.push({
      label: '设备',
      options: deviceListOptions,
    });
  }
  return options;
};
