import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import styled from 'styled-components';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';

import { FooterToolBar, GutterWrapper } from '@manyun/dc-brain.legacy.components';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  fetchCreateTemplateGroupAction,
  templateGroupViewActions,
  updateTemplateGroupConfig,
} from '@manyun/dc-brain.legacy.redux/actions/templateGroupViewActions';
import { generateTemplteGroupListUrl } from '@manyun/dc-brain.legacy.utils/urls';

import Option from './component/option';
import Table from './component/table';
import Tree from './component/tree';

const ScrollWrapper = styled.div`
  /* 32px: “完成按钮”的高度；1rem: "GutterWrapper" 相邻元素的间距 */
  height: ${({ height }) => height};
  overflow-y: auto;
`;

function throws(error) {
  throw new Error(error);
}

class CreateTemplateView extends Component {
  _templateInfoRef = React.createRef();

  componentWillUnmount() {
    this.props.resetItems();
  }
  componentDidMount() {
    // 目标设备类型
    this.props.syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });
  }

  submit = async () => {
    const { mode, sureElectricTable } = this.props;
    try {
      await this._templateInfoRef.current.props.form.validateFields().catch(throws);
      if (!sureElectricTable.length) {
        message.error('至少关联一个模板');
        return;
      }
      if (mode === 'new') {
        this._commitContent();
      } else {
        this.updataTemplate();
      }
    } catch (error) {}
  };

  _commitContent = () => {
    const {
      createTemplateDataAvailable,
      createTemplateDataName,
      sureElectricTable,
      sureChoiceIDCBlockTable,
    } = this.props;
    let groupIds = [];
    if (sureElectricTable.length) {
      sureElectricTable.forEach(item => {
        groupIds = [...groupIds, item.id];
      });
    }
    this.props.fetchCreateTemplateGroupAction({
      name: createTemplateDataName.value,
      available: Boolean(createTemplateDataAvailable.value),
      groupIds,
      monitorConfigs: sureChoiceIDCBlockTable,
    });
  };

  updataTemplate = () => {
    const { createTemplateDataAvailable, createTemplateDataName, sureElectricTable, match } =
      this.props;
    let groupIds = [];
    if (sureElectricTable.length) {
      sureElectricTable.forEach(item => {
        groupIds = [...groupIds, item.id];
      });
    }
    this.props.updateTemplateGroupConfig({
      id: match.params.id,
      name: createTemplateDataName.value,
      available: Boolean(createTemplateDataAvailable.value),
      groupIds,
    });
  };

  render() {
    const { mode } = this.props;

    return (
      <>
        <GutterWrapper flex style={{ height: '100%' }}>
          <Tree />
          <GutterWrapper flexN={1} mode="vertical">
            <ScrollWrapper height={'calc(100% - 48px - 16px + var(--content-padding-bottom))'}>
              <GutterWrapper mode="vertical" className="monitor-item-config__scroll-wrapper">
                <Option mode={mode} {...this.props} wrappedComponentRef={this._templateInfoRef} />
                <Table mode={mode} {...this.props} />
              </GutterWrapper>
            </ScrollWrapper>
          </GutterWrapper>
        </GutterWrapper>
        <FooterToolBar>
          <GutterWrapper>
            <Button type="primary" onClick={this.submit}>
              确认
            </Button>

            <Link to={generateTemplteGroupListUrl()}>
              <Button>取消</Button>
            </Link>
          </GutterWrapper>
        </FooterToolBar>
      </>
    );
  }
}

const mapStateToProps = ({
  templateGroupView: {
    loading,
    createTemplateDataAvailable,
    createTemplateDataName,
    sureElectricTable,
    sureChoiceIDCBlockTable,
  },
}) => ({
  loading,
  createTemplateDataAvailable,
  createTemplateDataName,
  sureElectricTable,
  sureChoiceIDCBlockTable,
});
const mapDispatchToProps = {
  updateTemplateGroupConfig,
  fetchCreateTemplateGroupAction,
  syncCommonData: syncCommonDataActionCreator,
  resetItems: templateGroupViewActions.resetItems,
};

export default connect(mapStateToProps, mapDispatchToProps)(CreateTemplateView);
