import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import Form from '@ant-design/compatible/es/form';

import { Button } from '@manyun/base-ui.ui.button';
import { Cascader } from '@manyun/base-ui.ui.cascader';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Popover } from '@manyun/base-ui.ui.popover';
import { Spin } from '@manyun/base-ui.ui.spin';

import { User } from '@manyun/auth-hub.ui.user';

import { GutterWrapper, TinyTable } from '@manyun/dc-brain.legacy.components';
import { mergeRegionSpaces } from '@manyun/dc-brain.legacy.pages/template-group-view/utils';
import { commonActions } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  cancleConnectArea,
  getAreaConfigById,
  handeleSearchAreaAndIdcAction,
  saveCheckedKeysAction,
  templateGroupViewActions,
} from '@manyun/dc-brain.legacy.redux/actions/templateGroupViewActions';
import {
  generateBasicResourcesBuildingUrlLocation,
  generateBasicResourcesIdcUrl,
} from '@manyun/dc-brain.legacy.utils/urls';

const configType = 'SCHEME';

const columns = ctx => [
  {
    title: '区域',
    dataIndex: 'region',
  },
  {
    title: '机房',
    dataIndex: 'idcTag',
    render: idcTag => (
      <Link type="link" to={generateBasicResourcesIdcUrl({ idc: idcTag })}>
        {idcTag}
      </Link>
    ),
  },

  {
    title: '楼栋',
    dataIndex: 'blockTag',
    render: (blockTag, record) => {
      if (!blockTag || blockTag === '-') {
        return null;
      } else {
        return (
          <Link
            type="link"
            to={generateBasicResourcesBuildingUrlLocation({
              idc: record.idcTag,
              block: record.blockTag,
            })}
          >
            {blockTag}
          </Link>
        );
      }
    },
  },

  {
    title: '操作',
    dataIndex: '',
    fixed: 'right',
    render: (text, record) => (
      <Button
        style={{ padding: 0, height: 'auto' }}
        type="link"
        onClick={() => ctx.deleteIdc(record)}
      >
        删除
      </Button>
    ),
  },
];

const columnedit = ctx => [
  {
    title: '区域',
    dataIndex: 'region',
  },
  {
    title: '机房',
    dataIndex: 'idcTag',
    render: idcTag => (
      <Link type="link" to={generateBasicResourcesIdcUrl({ idc: idcTag })}>
        {idcTag}
      </Link>
    ),
  },

  {
    title: '楼栋',
    dataIndex: 'blockTag',
    render: (blockTag, record) => {
      if (!blockTag) {
        return null;
      } else {
        return (
          <Link
            type="link"
            to={generateBasicResourcesBuildingUrlLocation({
              idc: record.idcTag,
              block: record.blockTag,
            })}
          >
            {blockTag}
          </Link>
        );
      }
    },
  },
  {
    title: '关联时间',
    dataIndex: 'gmtCreate',
  },

  {
    title: '关联人',
    dataIndex: 'lastOperatorName',
    render: (__, { lastOperatorName, lastOperator }) => (
      <User.Link id={lastOperator} name={lastOperatorName} />
    ),
  },

  {
    title: '操作',
    key: '__actions',
    fixed: 'right',
    render: (__, record) => (
      <span>
        <Popover
          content={
            <GutterWrapper>
              <p>{`确认取消关联生效域：${record.idcTag}吗？`}</p>
              <Button
                style={{ padding: 0, height: 'auto', marginLeft: 0 }}
                type="link"
                onClick={() => ctx.cancleConnect(record)}
              >
                确认
              </Button>
            </GutterWrapper>
          }
          trigger="focus"
        >
          <Button style={{ padding: 0, height: 'auto' }} type="link">
            取消关联
          </Button>
        </Popover>
      </span>
    ),
  },
];

class EditArea extends Component {
  state = {
    pageNo: 1,
    pageSize: 10,
  };

  componentDidMount() {
    this.props.syncCommonData({ strategy: { space: 'FORCED', citiesTree: 'FORCED' } });
  }

  handleCancel = () => {
    const { sureChoiceIDCBlockTable } = this.props;
    this.props.form.resetFields();
    this.props.resetSearch(sureChoiceIDCBlockTable);
    this.props.closeEditAreaVisible();
  };

  handleReset = () => {
    const { mode, editMess, sureChoiceIDCBlockTable } = this.props;
    this.props.form.resetFields();
    this.setState({
      pageNo: 1,
    });
    if (mode === 'edit') {
      let temp = {
        configId: editMess.id,
        configType: configType,
      };
      this.props.getAreaConfigById({ temp });
    }
    if (mode === 'new') {
      this.props.resetSearch(sureChoiceIDCBlockTable);
    }
  };

  AddConnect = () => {
    this.props.closeIdcRoomModal();
    // this.props.closeEditAreaVisible();
  };

  search = (event, form) => {
    this.props.form.validateFields(async (err, values) => {
      const { choiceIDCBlockTable, sureChoiceIDCBlockTable } = this.props;
      if (!values.areaIdc.length) {
        this.props.handeleSearchAreaAndIdcAction(sureChoiceIDCBlockTable);
      } else {
        const region = values.areaIdc ? values.areaIdc[0] : null;
        const idcTag = values.areaIdc ? values.areaIdc[1] : null;
        const list = choiceIDCBlockTable.filter(item => {
          if (region && idcTag) {
            if (item.region === region && item.idcTag === idcTag) {
              return true;
            }
            return false;
          } else {
            if (item.region === region) {
              return true;
            }
            return false;
          }
        });
        this.setState({
          pageNo: 1,
        });
        this.props.handeleSearchAreaAndIdcAction(list);
      }
    });
  };

  deleteIdc = row => {
    const { choiceIDCBlockTable } = this.props;
    const list = choiceIDCBlockTable.filter(item => {
      if (
        item.region === row.region &&
        item.idcTag === row.idcTag &&
        item.blockTag === row.blockTag
      ) {
        return false;
      }
      return true;
    });
    this.props.saveCheckedKeysAction(list);
  };

  cancleConnect = row => {
    const { mode } = this.props;
    if (mode === 'edit') {
      this.props.cancleConnectArea({
        configId: row.configId,
        configType: configType,
        targetId: row.targetId,
      });
    }
  };

  onChangePage = (pageNo, pageSize) => {
    this.setState({
      pageNo,
      pageSize,
    });
  };

  render() {
    const form = this.props.form;
    const { getFieldDecorator } = form;
    const { choiceIDCBlockTable, mode, spaceTreeData } = this.props;
    const { pageNo, pageSize } = this.state;
    return (
      <Modal
        title="生效域关联"
        width="60%"
        visible={this.props.editAreaVisible}
        onOk={this.handleCancel}
        onCancel={this.handleCancel}
        onClose={this.handleCancel}
      >
        {mode === 'new' && (
          <TinyTable
            rowKey="targetId"
            scroll={{ x: 'max-content' }}
            columns={columns(this)}
            dataSource={choiceIDCBlockTable}
            pagination={{
              total: choiceIDCBlockTable.length,
              current: pageNo,
              pageSize: pageSize,
              onChange: this.onChangePage,
            }}
            actions={[
              <Form key="option" colon={false} style={{ width: '100%' }}>
                <Row>
                  <Col xl={3}>
                    <Form.Item style={{ marginBottom: 0 }}>
                      <Button type="primary" onClick={this.AddConnect}>
                        添加关联
                      </Button>
                    </Form.Item>
                  </Col>
                  <Col xl={7}>
                    <Form.Item
                      label="位置"
                      labelCol={{ xl: 8 }}
                      wrapperCol={{ xl: 16 }}
                      style={{ marginBottom: 0 }}
                    >
                      {getFieldDecorator('areaIdc')(
                        <Cascader
                          placeholder=""
                          options={spaceTreeData}
                          changeOnSelect
                          notFoundContent={<Spin size="small" />}
                        />
                      )}
                    </Form.Item>
                  </Col>
                  <Col xl={14} style={{ textAlign: 'right', lineHeight: '40px' }}>
                    <Form.Item style={{ marginBottom: 0 }}>
                      <GutterWrapper>
                        <Button type="primary" onClick={this.search}>
                          搜索
                        </Button>
                        <Button onClick={this.handleReset}>重置</Button>
                      </GutterWrapper>
                    </Form.Item>
                  </Col>
                </Row>
              </Form>,
            ]}
          />
        )}
        {mode === 'edit' && (
          <TinyTable
            rowKey="targetId"
            scroll={{ x: 'max-content' }}
            columns={columnedit(this)}
            dataSource={choiceIDCBlockTable}
            pagination={{
              total: choiceIDCBlockTable.length,
              current: pageNo,
              pageSize: pageSize,
              onChange: this.onChangePage,
            }}
            actions={[
              <Form key="option" colon={false} style={{ width: '100%' }}>
                <Row>
                  <Col xl={3}>
                    <Form.Item>
                      <Button type="primary" onClick={this.AddConnect}>
                        添加关联
                      </Button>
                    </Form.Item>
                  </Col>
                  <Col xl={7}>
                    <Form.Item label="区域/机房" labelCol={{ xl: 8 }} wrapperCol={{ xl: 16 }}>
                      {getFieldDecorator('areaIdc')(
                        <Cascader
                          placeholder=""
                          options={spaceTreeData}
                          changeOnSelect
                          notFoundContent={<Spin size="small" />}
                        />
                      )}
                    </Form.Item>
                  </Col>
                  <Col xl={14} style={{ textAlign: 'right', lineHeight: '40px' }}>
                    <Form.Item>
                      <GutterWrapper>
                        <Button type="primary" onClick={this.search}>
                          搜索
                        </Button>
                        <Button onClick={this.handleReset}>重置</Button>
                      </GutterWrapper>
                    </Form.Item>
                  </Col>
                </Row>
              </Form>,
            ]}
          />
        )}
      </Modal>
    );
  }
}

const mapStateToProps = ({
  templateGroupView: { loading, editAreaVisible, choiceIDCBlockTable, sureChoiceIDCBlockTable },
  common: { citiesTree },
  'resource.spaces': { entities, codes },
  equipmentManage: { idcDevices },
}) => {
  let spaceTreeData = [];
  if (codes && citiesTree) {
    const treeData = mergeRegionSpaces(
      citiesTree,
      codes.map(code => entities[code]),
      idcDevices
    );
    spaceTreeData = treeData.treeList;
  }

  return {
    loading,
    editAreaVisible,
    choiceIDCBlockTable,
    sureChoiceIDCBlockTable,
    spaceTreeData,
  };
};

const mapDispatchToProps = {
  closeEditAreaVisible: templateGroupViewActions.editAreaVisible,
  closeIdcRoomModal: templateGroupViewActions.areaIdecRoomTreeVisible,
  handeleSearchAreaAndIdcAction,
  saveCheckedKeysAction,
  cancleConnectArea,
  getAreaConfigById,
  resetSearch: templateGroupViewActions.choiceEreaBlockSuccess,
  getSpace: commonActions.space,
  sureChoiceEreaBlockSuccess: templateGroupViewActions.sureChoiceEreaBlockSuccess,
  choiceEreaBlockSuccess: templateGroupViewActions.choiceEreaBlockSuccess,
  syncCommonData: syncCommonDataActionCreator,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(Form.create({ name: 'edit_area' })(EditArea));
