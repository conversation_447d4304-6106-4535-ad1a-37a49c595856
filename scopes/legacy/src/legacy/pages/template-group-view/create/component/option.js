import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';

import { Button } from '@manyun/base-ui.ui.button';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { Radio } from '@manyun/base-ui.ui.radio';

import { TinyCard } from '@manyun/dc-brain.legacy.components';
import { YES_OR_NO_KEY_MAP, YES_OR_NO_OPTIONS } from '@manyun/dc-brain.legacy.constants';
import {
  getAreaConfigById,
  templateGroupViewActions,
} from '@manyun/dc-brain.legacy.redux/actions/templateGroupViewActions';
import { getLocationSearchMap } from '@manyun/dc-brain.legacy.utils';

// import EditArea from './edit-area';
// import AreaIdcRoom from '../../components/area-idc-room';
import ConnectAreaModal from '../../components/connect-area-modal';

const configType = 'SCHEME';

class Option extends Component {
  state = {
    idcBlockCascader: [],
    configProps: null,
    editMess: null,
  };

  componentDidMount() {
    const { location, match, mode } = this.props;

    if (mode === 'edit') {
      const { name, available } = getLocationSearchMap(location.search, ['name', 'available']);
      this.setState({
        editMess: {
          id: match.params.id,
          name: name,
          available: available,
        },
      });
      let temp = {
        configId: match.params.id,
        configType: configType,
      };
      this.setState({ configProps: temp });
      this.props.getAreaConfigById({ temp });
      this.props.onChange({
        name: { value: name },
        available: {
          value: available === 'true' ? YES_OR_NO_KEY_MAP.YES : YES_OR_NO_KEY_MAP.NO,
        },
      });
    } else {
      this.props.onChange({
        available: {
          value: YES_OR_NO_KEY_MAP.YES,
        },
      });
    }
  }

  editArea = () => {
    if (this.props.mode === 'edit') {
      let temp = {
        configId: this.props.match.params.id,
        configType: configType,
      };
      this.props.getAreaConfigById({ temp, type: 'openIdcRoomVisible' });
    } else {
      this.props.openIdcRoomVisible();
    }
  };

  areaIdcBlockRoomCascader = value => {
    return value.map(item => {
      if (item.metaType === 'IDC') {
        item = {
          label: item.metaName + '(' + item.metaCode + ')',
          value: item.metaCode,
        };
      } else {
        item = {
          ...item,
          label: item.metaName,
          value: item.metaName,
          children: Array.isArray(item.children) && item.children.length ? item.children : null,
        };
      }

      if (item.children && item.children.length) {
        return { ...item, children: [...this.areaIdcBlockRoomCascader(item.children)] };
      }
      return item;
    });
  };

  render() {
    const form = this.props.form;
    const { getFieldDecorator } = form;
    const { choiceIDCBlockTable, mode } = this.props;
    // const { configProps, editMess } = this.state;
    let keyArr = [];
    choiceIDCBlockTable.forEach(item => {
      keyArr = [...keyArr, item.targetId];
    });

    return (
      <TinyCard title="基本配置">
        <Form style={{ width: 576 }} colon={false}>
          <Row>
            <Col span={24}>
              <Form.Item label="模板组名称" labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
                {getFieldDecorator('name', {
                  rules: [
                    { required: true, message: '模板组名称必填！' },
                    {
                      max: 32,
                      message: '最多输入 32 个字符！',
                    },
                  ],
                })(<Input />)}
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="是否启用" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
                {getFieldDecorator('available')(
                  <Radio.Group>
                    {YES_OR_NO_OPTIONS.map(({ label, value }) => {
                      return (
                        <Radio key={value} value={value}>
                          {label}
                        </Radio>
                      );
                    })}
                  </Radio.Group>
                )}
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="生效域" labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
                {getFieldDecorator('user')(
                  <Button type="link" onClick={this.editArea}>
                    编辑
                  </Button>
                )}
              </Form.Item>
            </Col>
          </Row>

          {/* 编辑生效域弹框 */}
          {/* <EditArea mode={mode} editMess={editMess} /> */}
          {/* 生效域关联 */}
          {/* <AreaIdcRoom
            mode={mode}
            editMess={editMess}
            checkedKeys={keyArr}
            checkMess={choiceIDCBlockTable}
            configProps={configProps}
          /> */}
          <ConnectAreaModal
            checkedKeys={keyArr}
            mode={mode}
            selectedTemplateIds={mode === 'edit' ? [this.props.match.params.id] : null}
            treeDisabled={false}
            tagClosable={true}
            showFooter={true}
          />
        </Form>
      </TinyCard>
    );
  }
}

const mapStateToProps = ({
  templateGroupView: {
    choiceIDCBlockTable,
    choiceIDCORBlock,
    createTemplateDataAvailable,
    createTemplateDataName,
  },
}) => ({
  choiceIDCBlockTable,
  choiceIDCORBlock,
  createTemplateDataAvailable,
  createTemplateDataName,
});
const mapDispatchToProps = {
  closeEditAreaVisible: templateGroupViewActions.editAreaVisible,
  getAreaConfigById,
  onChange: templateGroupViewActions.getFormChangedData,
  resetItems: templateGroupViewActions.resetItems,
  openIdcRoomVisible: templateGroupViewActions.areaIdecRoomTreeVisible,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(
  Form.create({
    name: 'template_group_config',
    onFieldsChange({ onChange }, fields) {
      onChange(fields);
    },
    mapPropsToFields(props) {
      return {
        name: Form.createFormField(props.createTemplateDataName),
        available: Form.createFormField(props.createTemplateDataAvailable),
      };
    },
  })(Option)
);
