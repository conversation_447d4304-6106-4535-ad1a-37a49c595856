import React, { Component, useState } from 'react';
import { useDrop } from 'react-dnd';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';
import { animated, useSpring } from 'react-spring';

import differenceBy from 'lodash/differenceBy';
import get from 'lodash/get';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Tag } from '@manyun/base-ui.ui.tag';

import { User } from '@manyun/auth-hub.ui.user';
import {
  generateAlarmConfigurationTemplateDetailUrl,
  generateAlarmConfigurationTemplateEditUrl,
} from '@manyun/monitoring.route.monitoring-routes';

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>Wrapper, TinyCard, TinyTable } from '@manyun/dc-brain.legacy.components';
import { TEMPLATE_VIEW_ELECTRIC } from '@manyun/dc-brain.legacy.constants/templateGroupView';
import { alarmConfigurationTemplateActions } from '@manyun/dc-brain.legacy.redux/actions/alarmConfigurationTemplateActions';
import {
  electricTableDateAction,
  fetchCreateTemplateGroupAction,
  fetchTemplateMessByMetaCode,
  getTemplateByTemplateGroupId,
  selectElectricTableDateAction,
  templateGroupViewActions,
  updateTemplateGroupConfig,
} from '@manyun/dc-brain.legacy.redux/actions/templateGroupViewActions';
import { getLocationSearchMap } from '@manyun/dc-brain.legacy.utils';

import { ELECTRIC_HEAT_TYPE } from '../../components/temp';

const TPL_NAME_CLMN_DATA_IDX = 'name';

const getColumns = ({ metaCategory, onDeleteElectric, saveBasicConfiguration }) => {
  const clmns = [
    {
      title: '目标设备类型',
      dataIndex: 'target',
      render: (target, { dropZone, isOver }) => {
        const deviceTypeName = get(metaCategory, [target, 'metaName'], target);

        if (!dropZone) {
          return deviceTypeName;
        }

        return {
          children: isOver ? '释放以添加' : '拖拽模板到这里',
          props: {
            style: {
              background: isOver
                ? `var(--${prefixCls}-success-color)`
                : `var(--${prefixCls}-warning-color)`,
            },
            colSpan: 7,
          },
        };
      },
    },
    {
      title: '模板名称',
      dataIndex: TPL_NAME_CLMN_DATA_IDX,
      render: (text, { dropZone }) => {
        if (dropZone) {
          return { props: { colSpan: 0 } };
        }

        return (
          <Ellipsis lines={1} tooltip>
            {text}
          </Ellipsis>
        );
      },
    },
    {
      title: '创建时间',
      dataIndex: 'gmtCreate',
      render(gmtCreate, { dropZone }) {
        if (dropZone) {
          return { props: { colSpan: 0 } };
        }

        return gmtCreate;
      },
    },
    {
      title: '更新时间',
      dataIndex: 'gmtModified',
      render(gmtModified, { dropZone }) {
        if (dropZone) {
          return { props: { colSpan: 0 } };
        }

        return gmtModified;
      },
    },
    {
      title: '更新人',
      dataIndex: 'lastOperatorName',
      render: (__, { dropZone, lastOperatorName, lastOperator }) => {
        if (dropZone) {
          return { props: { colSpan: 0 } };
        }

        return <User.Link id={lastOperator} name={lastOperatorName} />;
      },
    },
    {
      title: '状态',
      dataIndex: 'available',
      render: (available, { dropZone }) => {
        if (dropZone) {
          return { props: { colSpan: 0 } };
        }

        return <Tag color={available ? 'green' : 'default'}>{available ? '启用' : '停用'}</Tag>;
      },
    },
    {
      title: '操作',
      key: '__actions',
      fixed: 'right',
      render: (__, { dropZone, ...record }) => {
        if (dropZone) {
          return { props: { colSpan: 0 } };
        }

        return (
          <span>
            <DeleteConfirm
              variant="popconfirm"
              title={`确认删除模板： ${record.name}吗？`}
              onOk={() => onDeleteElectric(record)}
            >
              <Button type="link" compact>
                移除
              </Button>
            </DeleteConfirm>

            <Divider type="vertical" />
            <Link
              type="link"
              to={generateAlarmConfigurationTemplateEditUrl({ id: record.id, record })}
            >
              <Button type="link" compact onClick={() => saveBasicConfiguration(record)}>
                编辑
              </Button>
            </Link>
            <Divider type="vertical" />
            <Link
              type="link"
              to={generateAlarmConfigurationTemplateDetailUrl({ id: record.id, record })}
            >
              <Button type="link" compact onClick={() => saveBasicConfiguration(record)}>
                查看
              </Button>
            </Link>
          </span>
        );
      },
    },
  ];

  let tplNameClmnIdx = -1;
  const othersWidth = clmns.reduce((w, { dataIndex, width }, idx) => {
    if (dataIndex === TPL_NAME_CLMN_DATA_IDX) {
      tplNameClmnIdx = idx;

      return w;
    }

    if (!(typeof width == 'number' && !Number.isNaN(width))) {
      return w;
    }

    return w + width;
  }, 0);

  if (tplNameClmnIdx <= -1) {
    return clmns;
  }

  clmns[tplNameClmnIdx].width = `calc(100% - 60px - ${othersWidth}px)`; // `60px` is the width of the checkbox column.

  return clmns;
};

function BodyRow({ allowAnimateDroppedTarget, dropRef, ...restProps }) {
  const dropZoneStyle = useSpring({
    to: [{ opacity: 1 }],
    from: { opacity: 0 },
  });

  const rowStyle = useSpring({
    config: {
      tension: 400,
    },
    to: { opacity: 1 },
    from: { opacity: 0 },
  });

  return (
    <animated.tr
      ref={dropRef}
      {...restProps}
      style={dropRef ? dropZoneStyle : allowAnimateDroppedTarget ? rowStyle : undefined}
    />
  );
}

// 电力
function TableElectric({
  selectedRowKeys,
  selectedRows,
  onSelectChange,
  electricTable,
  onDeleteElectric,
  metaCategory,
  fetchTemplateMessByMetaCode,
  loading,
  request,
  saveBasicConfiguration,
  deleteSomeElectric,
  seacrchElectic,
  electricPageNo,
  onChangeElectricPageNo,
  pageSize,
}) {
  const [dropped, setDropped] = useState(false);

  const [{ isOver, canDrop }, dropRef] = useDrop({
    accept: TEMPLATE_VIEW_ELECTRIC,
    drop: ({ itemMess }) => {
      setDropped(true);
      request();
      const id = itemMess.metaCode;
      fetchTemplateMessByMetaCode({
        data: {
          id,
          electricTableData: electricTable,
          type: ELECTRIC_HEAT_TYPE.ELECTRIC,
        },
        resetDropped: () => {
          setTimeout(setDropped, 16, false);
        },
      });
    },
    collect: monitor => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
    }),
  });

  return (
    <TinyTable
      rowKey="id"
      actionsWrapperStyle={{ justifyContent: 'space-between' }}
      actions={[
        <GutterWrapper key="batch-delete">
          <Button danger disabled={!selectedRowKeys.length} onClick={deleteSomeElectric}>
            批量移除
          </Button>
          <span>请将左侧模板拖入至此卡片内！</span>
        </GutterWrapper>,
        <Input.Search
          key="search-tpl-name"
          style={{ width: 200 }}
          placeholder="模板名称"
          onSearch={seacrchElectic}
        />,
      ]}
      loading={loading}
      scroll={{ x: 'max-content' }}
      columns={getColumns({ metaCategory, onDeleteElectric, saveBasicConfiguration })}
      rowSelection={{
        selectedRowKeys,
        selectedRows,
        onChange: onSelectChange,
      }}
      components={{ body: { row: BodyRow } }}
      dataSource={
        canDrop ? [{ id: 'drop-zone', dropZone: true, isOver }, ...electricTable] : electricTable
      }
      pagination={{
        total: electricTable.length,
        current: electricPageNo,
        pageSize: pageSize,
        onChange: onChangeElectricPageNo,
      }}
      onRow={({ id }, index) =>
        id === 'drop-zone' ? { dropRef } : { allowAnimateDroppedTarget: index === 0 && dropped }
      }
    />
  );
}

class Table extends Component {
  state = {
    electricTableData: [],
    selectData: {},
    selectedRowKeys: [],
    selectedRows: [],
    electricPageNo: 1,
    editMess: null,
    pageSize: 10,
  };

  componentDidMount() {
    const { location, match, mode } = this.props;
    if (mode === 'edit') {
      const { name, available } = getLocationSearchMap(location.search, ['name', 'available']);
      this.setState({
        editMess: {
          id: match.params.id,
          name: name,
          available: available,
        },
      });

      const schemeId = match.params.id;
      this.props.getTemplateByTemplateGroupId(schemeId);
    }
  }

  onDeleteElectric = row => {
    const { sureElectricTable } = this.props;
    const newData = sureElectricTable.filter(item => item.id !== row?.id);
    this.props.sureElectricTableSuccess(newData);
  };

  onSelectChange = (selectedRowKeys, selectedRows) => {
    this.setState({ selectedRowKeys, selectedRows });
  };

  deleteSomeElectric = () => {
    const { selectedRows } = this.state;
    const { sureElectricTable } = this.props;
    const newData = differenceBy(sureElectricTable, selectedRows, 'id');
    this.props.sureElectricTableSuccess(newData);
    this.setState({ selectedRowKeys: [], selectedRows: [] });
  };

  seacrchElectic = value => {
    const searchName = value;
    const { sureElectricTable } = this.props;
    this.setState({
      electricPageNo: 1,
    });
    if (sureElectricTable.length === 0) {
      message.error('当前没有数据');
    } else {
      if (searchName === '') {
        this.props.selectElectricTableSuccess(sureElectricTable);
      } else {
        const newData = sureElectricTable.filter(item => {
          const idx = item.name.indexOf(searchName);
          if (idx > -1) {
            return true;
          } else {
            return false;
          }
        });
        this.props.selectElectricTableSuccess(newData);
      }
    }
  };

  onChangeElectricPageNo = (pageNum, pageSize) => {
    this.setState({
      electricPageNo: pageNum,
      pageSize,
      selectedRowKeys: [],
      selectedRows: [],
    });
  };

  render() {
    const { electricTable, createTableloading, nomalizedDeviceCategory } = this.props;
    const { selectedRowKeys, selectedRows, electricPageNo, pageSize } = this.state;
    return (
      <GutterWrapper mode="vertical">
        <TinyCard>
          <TableElectric
            selectedRowKeys={selectedRowKeys}
            selectedRows={selectedRows}
            selectElectricTableDateAction={this.props.selectElectricTableDateAction}
            electricTable={electricTable}
            metaCategory={nomalizedDeviceCategory}
            fetchTemplateMessByMetaCode={this.props.fetchTemplateMessByMetaCode}
            loading={createTableloading}
            request={this.props.request}
            saveBasicConfiguration={this.props.saveBasicConfiguration}
            deleteSomeElectric={this.deleteSomeElectric}
            seacrchElectic={this.seacrchElectic}
            electricPageNo={electricPageNo}
            pageSize={pageSize}
            onDeleteElectric={this.onDeleteElectric}
            onSelectChange={this.onSelectChange}
            onChangeElectricPageNo={this.onChangeElectricPageNo}
          />
        </TinyCard>
      </GutterWrapper>
    );
  }
}

const mapStateToProps = ({
  templateGroupView: {
    createTableloading,
    electricTable,
    sureElectricTable,
    createTemplateDataName,
    createTemplateDataAvailable,
    sureChoiceIDCBlockTable,
    connectTemplates,
  },
  common: { deviceCategory },
}) => ({
  createTableloading,
  electricTable,
  sureElectricTable,
  createTemplateDataName,
  createTemplateDataAvailable,
  sureChoiceIDCBlockTable,
  connectTemplates,
  nomalizedDeviceCategory: deviceCategory ? deviceCategory.normalizedList : {},
});

const mapDispatchToProps = {
  selectElectricTableDateAction,
  electricTableDateAction,
  fetchCreateTemplateGroupAction,
  getTemplateByTemplateGroupId,
  updateTemplateGroupConfig,
  selectElectricTableSuccess: templateGroupViewActions.selectElectricTableSuccess,
  fetchTemplateMessByMetaCode,
  request: templateGroupViewActions.request,
  saveBasicConfiguration: alarmConfigurationTemplateActions.saveBasicConfiguration,
  sureElectricTableSuccess: templateGroupViewActions.sureElectricTableSuccess,
};

export default connect(mapStateToProps, mapDispatchToProps)(Table);
