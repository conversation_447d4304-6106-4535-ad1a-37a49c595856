import React, { Component } from 'react';
import { useDrag } from 'react-dnd';
import { connect } from 'react-redux';

import DownOutlined from '@ant-design/icons/es/icons/DownOutlined';

import { Input } from '@manyun/base-ui.ui.input';
import { Tree } from '@manyun/base-ui.ui.tree';

import { TinyCard } from '@manyun/dc-brain.legacy.components';
import { TEMPLATE_VIEW_ELECTRIC } from '@manyun/dc-brain.legacy.constants/templateGroupView';
import {
  fetchTemplateTreeAction,
  fetchTemplateTreeChildrenAction,
  templateGroupViewActions,
} from '@manyun/dc-brain.legacy.redux/actions/templateGroupViewActions';

const { Search } = Input;
const { TreeNode } = Tree;

const dataList = [];

const getParentKey = (key, tree) => {
  let parentKey;
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i];

    if (node.children) {
      if (node.children.some(item => item.metaCode + '-' + item.metaType === key)) {
        parentKey = node.metaCode + '-' + node.metaType;
      } else if (getParentKey(key, node.children)) {
        parentKey = getParentKey(key, node.children);
      }
    }
  }
  return parentKey;
};

function TreeNodeTitle({ info, title, disabled }) {
  let dragType = TEMPLATE_VIEW_ELECTRIC;
  const [{ isDragging }, dragRef] = useDrag({
    item: { type: dragType, itemMess: info },
    collect: monitor => ({
      isDragging: monitor.isDragging(),
    }),
    canDrag: !disabled,
  });

  return (
    <div ref={dragRef} style={{ opacity: isDragging ? 0.5 : 1 }}>
      {title}
    </div>
  );
}

export function DragTree({
  doPointTree,
  onExpand,
  autoExpandParent,
  expandedKeys,
  loop,
  searchValue,
  electricTable,
}) {
  loop = doPointTree => {
    const list = doPointTree.map(item => {
      let index = -1;
      let beforeStr;
      let afterStr;
      if (searchValue) {
        index = item.metaName.indexOf(searchValue);
        beforeStr = item.metaName.substr(0, index);
        afterStr = item.metaName.substr(index + searchValue.length);
      }
      const tableData = [...electricTable];
      let checkId = [];
      tableData.forEach(checkItem => {
        checkId.push(checkItem.id);
      });
      let disabled = checkId.includes(Number(item.metaCode)) ? true : false;

      const title =
        index > -1 ? (
          <span>
            {beforeStr}
            <span>{searchValue}</span>
            {afterStr}
            {item.metaType === 'C1' && ' '}
          </span>
        ) : item.metaType === 'C1' ? (
          <span>{item.metaName}</span>
        ) : (
          <span>{item.metaName}</span>
        );

      if (['C1', 'C2'].includes(item.metaType)) {
        return (
          <TreeNode
            key={item.metaCode + '-' + item.metaType}
            title={
              item.metaType !== 'C1' && item.metaType !== 'C2' ? (
                <TreeNodeTitle info={item} title={title} disabled={disabled} />
              ) : (
                title
              )
            }
            data-title={item.metaName}
            data={item}
          >
            {Array.isArray(item.children) && item.children.length && loop(item.children)}
          </TreeNode>
        );
      }
      return (
        <TreeNode
          isLeaf
          key={item.metaCode + '-' + item.metaType}
          title={<TreeNodeTitle info={item} title={title} disabled={disabled} />}
          data-title={item.metaName}
          data={item}
          disabled={disabled}
        />
      );
    });
    return list;
  };

  return (
    <Tree
      showLine
      switcherIcon={<DownOutlined />}
      onExpand={onExpand}
      expandedKeys={expandedKeys}
      autoExpandParent={autoExpandParent}
    >
      {loop(doPointTree)}
    </Tree>
  );
}

class TemplateTree extends Component {
  state = {
    expandedKeys: [],
    searchValue: '',
    autoExpandParent: true,
  };

  componentDidMount() {
    this.props.fetchTemplateTreeAction();
  }

  onExpand = expandedKeys => {
    this.setState({
      expandedKeys,
      autoExpandParent: false,
    });
  };

  onChange = e => {
    const { value } = e.target;
    const { templateTree } = this.props;

    this.generateList(templateTree);
    const expandedKeys = dataList
      .map(item => {
        if (item.title.indexOf(value) > -1) {
          return getParentKey(item.key, templateTree);
        }
        return null;
      })
      .filter((item, i, self) => item && self.indexOf(item) === i);
    this.setState({
      expandedKeys,
      searchValue: value,
      autoExpandParent: true,
    });
  };

  generateList = data => {
    for (let i = 0; i < data.length; i++) {
      const node = data[i];
      const { metaCode, metaType, metaName } = node;
      dataList.push({ key: metaCode + '-' + metaType, title: metaName });
      if (node.children) {
        this.generateList(node.children);
      }
    }
  };

  render() {
    const { autoExpandParent, expandedKeys, searchValue } = this.state;
    const { templateTree, templateTreeChildren, fetchTemplateTreeChildrenAction } = this.props;

    return (
      <TinyCard
        style={{
          width: 300,
          height: 'calc(100% - 48px - 16px + var(--content-padding-bottom))',
        }}
        bodyStyle={{
          height: 'calc(100% - 48px)',
          overflowY: 'auto',
        }}
        title="告警模板查询"
      >
        <Search
          allowClear
          style={{ marginBottom: 8 }}
          placeholder="请输入关键字"
          onChange={this.onChange}
        />
        <DragTree
          searchValue={searchValue}
          doPointTree={templateTree}
          onExpand={this.onExpand}
          expandedKeys={expandedKeys}
          autoExpandParent={autoExpandParent}
          templateTreeChildren={templateTreeChildren}
          fetchTemplateTreeChildrenAction={fetchTemplateTreeChildrenAction}
          props={this.props}
          electricTable={this.props.electricTable}
          request={this.props.request}
        ></DragTree>
      </TinyCard>
    );
  }
}

const mapStateToProps = ({
  templateGroupView: { loading, templateTree, templateTreeChildren, electricTable },
}) => ({
  loading,
  templateTree,
  templateTreeChildren,
  electricTable,
});

const mapDispatchToProps = {
  fetchTemplateTreeAction,
  fetchTemplateTreeChildrenAction,
  request: templateGroupViewActions.request,
};

export default connect(mapStateToProps, mapDispatchToProps)(TemplateTree);
