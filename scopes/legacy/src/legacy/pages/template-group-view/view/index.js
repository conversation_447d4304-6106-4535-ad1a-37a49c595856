import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import get from 'lodash/get';

import { Button } from '@manyun/base-ui.ui.button';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Tag } from '@manyun/base-ui.ui.tag';

import { User } from '@manyun/auth-hub.ui.user';
import { generateAlarmConfigurationTemplateEditUrl } from '@manyun/monitoring.route.monitoring-routes';

import { GutterWrapper, TinyCard, TinyTable } from '@manyun/dc-brain.legacy.components';
import { alarmConfigurationTemplateActions } from '@manyun/dc-brain.legacy.redux/actions/alarmConfigurationTemplateActions';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  fetchConnectTemplatesAction,
  fetchTemplateGroupAreaAction,
  templateGroupViewActions,
} from '@manyun/dc-brain.legacy.redux/actions/templateGroupViewActions';
import { getLocationSearchMap } from '@manyun/dc-brain.legacy.utils';
import { generateAlarmConfigurationTemplateDetailUrl } from '@manyun/dc-brain.legacy.utils/urls';

import ViewArea from './component/view-area';

const columns = (ctx, nomalizedDeviceCategory) => [
  // {
  //   title: '模板子类',
  //   dataIndex: 'metaName',
  // },
  {
    title: '模板名称',
    dataIndex: '',
    render: (text, record) => (
      <Link type="link" to={generateAlarmConfigurationTemplateDetailUrl({ id: record.id, record })}>
        <Button
          type="link"
          style={{ padding: 0, height: 'auto' }}
          onClick={() => ctx.props.saveBasicConfiguration(record)}
        >
          {record.name}
        </Button>
      </Link>
    ),
  },
  {
    title: '目标设备类型',
    dataIndex: 'target',
    render(target) {
      return get(nomalizedDeviceCategory, [target, 'metaName'], target);
    },
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
  },
  {
    title: '更新时间',
    dataIndex: 'gmtModified',
  },

  {
    title: '更新人',
    dataIndex: 'lastOperatorName',
    render: (__, { lastOperatorName, lastOperator }) => (
      <User.Link id={lastOperator} name={lastOperatorName} />
    ),
  },
  {
    title: '状态',
    dataIndex: 'available',
    render: available => (
      <Tag color={available ? 'green' : 'default'}>{available ? '启用' : '停用'}</Tag>
    ),
  },
  {
    title: '操作',
    dataIndex: '',
    render: (_, record) => (
      <Link to={generateAlarmConfigurationTemplateEditUrl({ id: record.id })}>
        <Button type="link" compact onClick={() => ctx.props.saveBasicConfiguration(record)}>
          编辑
        </Button>
      </Link>
    ),
  },
];

class ViewTemplateGroup extends Component {
  state = {
    configType: 'SCHEME',
    heatPageNo: 1,
    electricPageNo: 1,
    basicInfo: {},
  };

  componentDidMount() {
    const { match, location } = this.props;
    const schemeId = match.params.id;
    this.props.fetchConnectTemplatesAction(schemeId);
    // 目标设备类型
    this.props.syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });
    const { name, available, lastOperatorName } = getLocationSearchMap(location.search, [
      'name',
      'available',
      'lastOperatorName',
    ]);
    this.setState({
      basicInfo: {
        id: match.params.id,
        name: name,
        available: available,
        lastOperatorName: lastOperatorName,
      },
    });
  }

  seacrchElectic = value => {
    const { sureElectricTemplates } = this.props;
    const searchName = value;
    this.setState({
      electricPageNo: 1,
    });
    if (!sureElectricTemplates.length) {
      message.error('当前没有数据');
    } else {
      if (searchName === '') {
        const { match } = this.props;
        const schemeId = match.params.id;
        this.props.fetchConnectTemplatesAction(schemeId);
      } else {
        const newData = sureElectricTemplates.filter(item => {
          const idx = item.name.indexOf(searchName);
          if (idx > -1) {
            return true;
          } else {
            return false;
          }
        });
        this.props.seacrchViewElecticData(newData);
      }
    }
  };

  viewAreaModel = () => {
    const { match } = this.props;

    const { configType } = this.state;
    this.props.fetchTemplateGroupAreaAction({ configId: match.params.id, configType });
    this.props.showViewAreaVisible();
  };

  onChangeHeatPageNo = pageNum => {
    this.setState({
      heatPageNo: pageNum,
    });
  };

  onChangeElectricPageNo = pageNum => {
    this.setState({
      electricPageNo: pageNum,
    });
  };

  render() {
    const { electricTemplates, loading, nomalizedDeviceCategory } = this.props;
    const { configType, electricPageNo, basicInfo } = this.state;

    const tplNameColSpan = Math.floor(String(basicInfo.name).length / 10);
    const isEnable = basicInfo.available === 'true';

    return (
      <GutterWrapper mode="vertical">
        <TinyCard title="基本配置">
          <Descriptions column={3 + tplNameColSpan}>
            <Descriptions.Item span={tplNameColSpan} label="模板组名称">
              {basicInfo.name}
            </Descriptions.Item>
            <Descriptions.Item label="更新人">{basicInfo.lastOperatorName}</Descriptions.Item>
            <Descriptions.Item label="生效域">
              <Button type="link" compact onClick={this.viewAreaModel}>
                查看
              </Button>
            </Descriptions.Item>
            <Descriptions.Item label="是否启用">
              <Tag color={isEnable ? 'green' : 'default'}>{isEnable ? '启用' : '停用'}</Tag>
            </Descriptions.Item>
          </Descriptions>
        </TinyCard>
        {/* table */}
        <TinyCard title="关联模板">
          <TinyTable
            rowKey="id"
            loading={loading}
            dataSource={electricTemplates}
            columns={columns(this, nomalizedDeviceCategory)}
            pagination={{
              total: electricTemplates.length,
              current: electricPageNo,
              onChange: this.onChangeElectricPageNo,
            }}
            actionsWrapperStyle={{ justifyContent: 'flex-end' }}
            actions={[
              <Input.Search
                key="search"
                allowClear
                style={{ width: '200px' }}
                placeholder="模板名称"
                onSearch={this.seacrchElectic}
              />,
            ]}
          />
        </TinyCard>
        {/* 查看区域弹框 */}
        <ViewArea configId={basicInfo.id} configType={configType} />
      </GutterWrapper>
    );
  }
}

const mapStateToProps = ({
  templateGroupView: {
    templateGroupDetail,
    electricTemplates,
    sureElectricTemplates,
    templateEleSure,
    loading,
  },
  common: { deviceCategory },
}) => ({
  templateGroupDetail,
  electricTemplates,
  sureElectricTemplates,
  templateEleSure,
  loading,
  nomalizedDeviceCategory: deviceCategory ? deviceCategory.normalizedList : {},
});
const mapDispatchToProps = {
  fetchConnectTemplatesAction,
  showViewAreaVisible: templateGroupViewActions.viewAreaVisible,
  fetchTemplateGroupAreaAction,
  fetchTemplateEleAndHeartSuccess: templateGroupViewActions.fetchTemplateEleAndHeartSuccess,
  saveBasicConfiguration: alarmConfigurationTemplateActions.saveBasicConfiguration,
  syncCommonData: syncCommonDataActionCreator,
  seacrchViewElecticData: templateGroupViewActions.seacrchViewElecticData,
};

export default connect(mapStateToProps, mapDispatchToProps)(ViewTemplateGroup);
