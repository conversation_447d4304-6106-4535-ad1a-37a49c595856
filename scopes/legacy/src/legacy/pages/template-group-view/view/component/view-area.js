import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';

import { Button } from '@manyun/base-ui.ui.button';
import { Modal } from '@manyun/base-ui.ui.modal';

import { User } from '@manyun/auth-hub.ui.user';

import { GutterWrapper, TinyTable } from '@manyun/dc-brain.legacy.components';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  fetchTemplateGroupAreaAction,
  searchAreaIdcAction,
  templateGroupViewActions,
} from '@manyun/dc-brain.legacy.redux/actions/templateGroupViewActions';

import AreaLOcationCascader from '../../components/area-location-cascader';

const columns = ctx => [
  {
    title: '区域',
    dataIndex: 'region',
    fixed: 'left',
  },
  {
    title: '机房',
    dataIndex: 'idcTag',
    // render: idcTag => (
    //   <Link type="link" to={urls.generateBasicResourcesIdcUrl({ idc: idcTag })}>
    //     {idcTag}
    //   </Link>
    // ),
  },
  {
    title: '楼栋',
    dataIndex: 'blockTag',
    // render: (text, record) => (
    //   <Link
    //     type="link"
    //     to={urls.generateBasicResourcesBuildingUrlLocation({
    //       idc: record.idcTag,
    //       block: record.blockTag,
    //     })}
    //   >
    //     {record.blockTag}
    //   </Link>
    // ),
  },
  {
    title: '包间',
    dataIndex: 'roomTag',
  },
  {
    title: '设备',
    dataIndex: 'deviceName',
  },
  {
    title: '关联时间',
    dataIndex: 'gmtCreate',
  },
  {
    title: '关联人',
    dataIndex: 'lastOperator',
    render: (__, { lastOperatorName, lastOperator }) => (
      <User.Link id={lastOperator} name={lastOperatorName} />
    ),
  },
];

class ViewArea extends Component {
  componentDidMount() {
    this.props.syncCommonData({ strategy: { deviceCategory: 'IF_NULL', space: 'IF_NULL' } });
  }

  handleCancel = () => {
    this.props.closeViewAreaVisible();
  };

  handleReset = () => {
    const { sureTemplateGroupAreaTable } = this.props;
    this.props.form.resetFields();
    this.props.changeTemplateGroupArea(sureTemplateGroupAreaTable);
  };

  searchArea = () => {
    this.props.form.validateFields(async (err, values) => {
      const { sureTemplateGroupAreaTable } = this.props;
      this.props.searchAreaIdcAction({
        templateGroupAreaTable: sureTemplateGroupAreaTable,
        region: values.region,
      });
    });
  };

  render() {
    const {
      loading,
      templateGroupAreaTable,
      form: { getFieldDecorator },
    } = this.props;
    return (
      <Modal
        title="生效域关联"
        width="60%"
        visible={this.props.viewAreaVisible}
        onOk={this.handleCancel}
        onCancel={this.handleCancel}
        onClose={this.handleCancel}
        footer={null}
        destroyOnClose={true}
        bodyStyle={{ maxHeight: '452px', overflowY: 'auto' }}
      >
        <GutterWrapper mode="vertical" style={{ overflowX: 'auto' }}>
          <TinyTable
            rowKey="id"
            columns={columns(this)}
            scroll={{ x: 'max-content' }}
            dataSource={templateGroupAreaTable}
            loading={loading}
            actionsWrapperStyle={{ justifyContent: 'space-between' }}
            actions={[
              <Form.Item
                key="area-block"
                colon={false}
                label="位置"
                style={{ marginBottom: 0, width: '100%' }}
                labelCol={{ xl: 2 }}
                wrapperCol={{ xl: 22 }}
              >
                {getFieldDecorator('region')(
                  <AreaLOcationCascader
                    nodeTypes={['REGION', 'IDC', 'BLOCK', 'ROOM']}
                    nodeMutator={node => {
                      if (node.metaType === 'IDC') {
                        return {
                          ...node,
                          label: node.code,
                          value: node.code,
                        };
                      }
                      return {
                        ...node,
                        label: node.name,
                        value: node.name,
                      };
                    }}
                    changeOnSelect
                    style={{ width: 250 }}
                  />
                )}
              </Form.Item>,
              <Form.Item key="search" style={{ marginBottom: 0 }}>
                <GutterWrapper flex style={{ justifyContent: 'flex-end', margin: '4px' }}>
                  <Button type="primary" onClick={this.searchArea}>
                    搜索
                  </Button>
                  <Button onClick={this.handleReset}>重置</Button>
                </GutterWrapper>
              </Form.Item>,
            ]}
          />
        </GutterWrapper>
      </Modal>
    );
  }
}

const mapStateToProps = ({
  templateGroupView: {
    loading,
    viewAreaVisible,
    templateGroupAreaTable,
    sureTemplateGroupAreaTable,
  },
}) => {
  return {
    loading,
    viewAreaVisible,
    templateGroupAreaTable,
    sureTemplateGroupAreaTable,
  };
};

const mapDispatchToProps = {
  closeViewAreaVisible: templateGroupViewActions.viewAreaVisible,
  searchAreaIdcAction,
  fetchTemplateGroupAreaAction,
  syncCommonData: syncCommonDataActionCreator,
  changeTemplateGroupArea: templateGroupViewActions.changeTemplateGroupArea,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(Form.create({ name: 'view_area' })(ViewArea));
