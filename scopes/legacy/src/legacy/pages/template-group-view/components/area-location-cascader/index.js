import React from 'react';
import { useSelector } from 'react-redux';

import { Cascader } from '@manyun/base-ui.ui.cascader';

import { selectSpaceEntities } from '@manyun/resource-hub.state.space';

import { getCitiesTree } from '@manyun/dc-brain.legacy.redux/selectors/commonSelectors';
import { generateTreeData } from '@manyun/dc-brain.legacy.utils';

/**
 * @typedef {import('antd-3/lib/cascader').CascaderProps} CascaderProps
 *
 *
 * @typedef Props
 * @property {string[]} [nodeTypes] 节点信息
 * @property {string[]} [nodeMutator] 节点可控信息
 */

export const AreaLocationCascader = React.forwardRef(
  ({ nodeTypes = ['REGION', 'IDC'], nodeMutator = null, ...props }, ref) => {
    const cities = useSelector(getCitiesTree);
    const spaces = useSelector(selectSpaceEntities());
    let region = [];
    (cities || []).forEach(city => {
      region = region.concat(
        (city.children || []).map(item => ({
          code: item.value,
          name: item.label,
          type: 'REGION',
          parentCode: null,
        }))
      );
    });

    const areaSpaces = region.concat(spaces);
    const spaceTreeData = generateTreeData(areaSpaces, {
      key: 'code',
      typeKey: 'type',
      parentKey: 'parentCode',
      nodeTypes: nodeTypes,
      getNode: data => {
        let node = {};
        if (data.type === 'IDC') {
          node = {
            ...data,
            label: data.code,
            value: data.code,
          };
        }
        node = {
          ...data,
          label: data.name,
          value: data.code,
        };
        const _node = typeof nodeMutator == 'function' ? nodeMutator(node) : node;
        return _node;
      },
    });

    return <Cascader ref={ref} options={spaceTreeData} {...props} />;
  }
);

AreaLocationCascader.displayName = 'AreaLocationCascader';

export default AreaLocationCascader;
