import React, { Component } from 'react';
import { connect } from 'react-redux';

import difference from 'lodash/difference';

import { Modal } from '@manyun/base-ui.ui.modal';

import { NODE_TYPE_MAP } from '@manyun/dc-brain.legacy.components/relationship-connector/constants';
import TargetTreeTransfer from '@manyun/dc-brain.legacy.components/relationship-connector/target-tree-transfer';
import {
  connectRegionToTemplate,
  connectTemplateInAreaSearchTemplate,
  saveCheckedKeysAction,
  templateGroupViewActions,
} from '@manyun/dc-brain.legacy.redux/actions/templateGroupViewActions';

import { CONFIGTYPE } from './temp';

class AreaIdcRoom extends Component {
  state = {
    targetIdArr: [],
    okButtonDisabled: true,
  };
  componentWillUnmount() {
    this.props.resetItems();
  }

  handleCancel = () => {
    this.props.closeIdcRoomModal();
  };

  choiceSelect = (checkedKeys, checkedNodes) => {
    const { configProps, mode } = this.props;
    if (checkedKeys && checkedKeys.length) {
      const NOAREA = checkedNodes.filter(
        item => item.type !== NODE_TYPE_MAP.AREA && item.type !== NODE_TYPE_MAP.ROOT
      );
      const IDC = NOAREA.filter(item => item.type !== NODE_TYPE_MAP.BUILDING);
      const BUILDING = NOAREA.filter(item => item.type !== NODE_TYPE_MAP.IDC);

      const newbuil = BUILDING.filter(item => {
        if (IDC.filter(i => item.parent && item.parent.key === i.key).length) {
          return false;
        }
        return true;
      });
      const list = IDC.concat(newbuil);
      this.props.saveCheckedIdcAndRoom(list);
      this.setState({ targetId: list });
      let monitorConfigs = [];
      if (mode === 'new') {
        list.forEach(node => {
          if (node.type === NODE_TYPE_MAP.BUILDING) {
            let configs = {
              configType: 'SCHEME',

              targetId: node.key,
              targetType: 'BLOCK',
              region: node.parent.parent.title,
              idcTag: node.parent.title,
              blockTag: node.title,
              gmtCreate: null,
              lastOperator: null,
            };
            monitorConfigs.push(configs);
          }

          if (node.type === NODE_TYPE_MAP.IDC) {
            let configs = {
              configType: 'SCHEME',

              targetId: node.key,
              targetType: node.type,
              region: node.parent.title,
              idcTag: node.title,
              blockTag: '-',
              gmtCreate: null,
              lastOperator: null,
            };
            monitorConfigs.push(configs);
          }
        });
      } else {
        list.forEach(node => {
          if (node.type === NODE_TYPE_MAP.BUILDING) {
            let connectConfig = {
              targetId: node.key,
              targetType: 'BLOCK',
              configId: configProps.configId,
              configType: configProps.configType,
            };
            monitorConfigs.push(connectConfig);
          }

          if (node.type === NODE_TYPE_MAP.IDC) {
            let connectConfig = {
              targetId: node.key,
              targetType: 'IDC',
              configId: configProps.configId,
              configType: configProps.configType,
            };
            monitorConfigs.push(connectConfig);
          }
        });
      }
      this.props.tempSaveIacBlock(monitorConfigs);

      // 判断生效域是否发生改变，若没有改变，则不请求接口
      const targetIds = monitorConfigs.map(({ targetId }) => targetId);
      const differencetaTgetIds = difference(targetIds, this.props.checkedKeys);
      this.setState({ okButtonDisabled: !differencetaTgetIds.length });
    } else {
      this.setState({ okButtonDisabled: checkedNodes.length ? false : true });
      this.props.tempSaveIacBlock(checkedNodes);
    }
  };

  handleSubmit = () => {
    const { editMess, tempSaveIacBlockData, mode } = this.props;
    // 新建或编辑
    if (mode === 'new') {
      this.props.saveCheckedKeysAction(tempSaveIacBlockData);
      this.props.closeIdcRoomModal();
      // this.props.closeEditAreaVisible();
    } else {
      if (tempSaveIacBlockData.length) {
        let newData = [];
        tempSaveIacBlockData.forEach(item => {
          let temp = {
            ...item,
            configId: editMess.id,
            configType: CONFIGTYPE.SCHEME,
          };
          newData = [...newData, temp];
        });
        this.props.connectTemplateInAreaSearchTemplate({
          monitorConfigs: newData,
          configId: editMess.id,
          configType: CONFIGTYPE.SCHEME,
        });
      } else {
        this.props.closeIdcRoomModal();
      }
    }
  };

  render() {
    const { checkedKeys } = this.props;
    const { okButtonDisabled } = this.state;
    return (
      <Modal
        title="生效域关联"
        width="60%"
        destroyOnClose
        visible={this.props.areaIdecRoomTreeVisible}
        onOk={this.handleSubmit}
        onCancel={this.handleCancel}
        onClose={this.handleCancel}
        okButtonProps={{ disabled: okButtonDisabled }}
      >
        <TargetTreeTransfer
          checkedKeys={checkedKeys}
          targetSelectPlaceholder="请输入"
          onChange={(checkedLeafKeys, visibleKeys, checkedKeys, checkedNodes) =>
            this.choiceSelect(checkedKeys, checkedNodes)
          }
          cardBodyStyle={{ maxHeight: 'calc(100vh - 385px)', overflow: 'auto' }}
        />
      </Modal>
    );
  }
}

const mapStateToProps = ({
  templateGroupView: {
    loading,
    areaIdecRoomTreeVisible,
    sureChoiceIDCBlockTable,
    searchTemplateGroupPagesCondition,
    checkedIdcAndBlock,
    tempSaveIacBlockData,
  },
}) => ({
  loading,
  areaIdecRoomTreeVisible,
  sureChoiceIDCBlockTable,
  searchTemplateGroupPagesCondition,
  checkedIdcAndBlock,
  tempSaveIacBlockData,
});

const mapDispatchToProps = {
  closeIdcRoomModal: templateGroupViewActions.areaIdecRoomTreeVisible,
  saveCheckedKeysAction,
  closeEditAreaVisible: templateGroupViewActions.editAreaVisible,
  connectRegionToTemplate,
  saveCheckedIdcAndRoom: templateGroupViewActions.saveCheckedIdcAndRoom,
  connectTemplateInAreaSearchTemplate,
  tempSaveIacBlock: templateGroupViewActions.tempSaveIacBlock,
  resetItems: templateGroupViewActions.resetItems,
};

export default connect(mapStateToProps, mapDispatchToProps)(AreaIdcRoom);
