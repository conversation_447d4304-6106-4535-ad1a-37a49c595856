import React, { Component } from 'react';
import { connect } from 'react-redux';

import difference from 'lodash/difference';

import { Button } from '@manyun/base-ui.ui.button';
import { Modal } from '@manyun/base-ui.ui.modal';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';
import { NODE_TYPE_MAP } from '@manyun/dc-brain.legacy.components/relationship-connector/constants';
// import TargetTreeTransfer from './target-tree-transfer';
import { mergeRegionSpaces } from '@manyun/dc-brain.legacy.pages/template-group-view/utils';
import {
  connectRegionToTemplate,
  connectTemplateInAreaSearchTemplate,
  saveCheckedKeysAction,
  templateGroupViewActions,
} from '@manyun/dc-brain.legacy.redux/actions/templateGroupViewActions';

import IdcDeviceTreeTransfer from '../idc-device-tree-transfer';
import { CONFIGTYPE } from '../temp';

/**
 * mode  'create' | 'edit'
 * connectType 表格中批量或者逐个关联 'batch' | 'one' | 'view'
 */

class ConnectAreaModal extends Component {
  state = {
    targetIdArr: [],
    okButtonDisabled: true,
  };
  componentWillUnmount() {
    this.props.resetItems();
  }

  handleCancel = () => {
    this.props.closeIdcRoomModal();
  };

  choiceSelect = (visibleKeys, checkedNodes) => {
    const { treeData } = this.props;
    const checkedKeys = checkedNodes
      .filter(node => node?.metaType !== 'IDC')
      .map(item => item?.code)
      .filter(Boolean);
    if (checkedKeys && checkedKeys.length) {
      let list = [];
      checkedKeys.forEach(nodeKey => {
        const node = treeData.parallelList.find(({ metaCode }) => metaCode === nodeKey);
        // 选择了区域时，筛选出所有机房
        if (node.metaType === NODE_TYPE_MAP.REGION) {
          const idcs = treeData.parallelList
            .filter(({ parentCode }) => parentCode === node.metaCode)
            .map(({ metaCode }) => {
              return {
                targetId: metaCode,
                targetType: NODE_TYPE_MAP.IDC,
                configType: CONFIGTYPE.SCHEME,
              };
            });

          list = [...list, ...idcs];
        }
        if (node.metaType === NODE_TYPE_MAP.IDC) {
          list = [
            ...list,
            {
              targetId: node.metaCode,
              targetType: NODE_TYPE_MAP.IDC,
              configType: CONFIGTYPE.SCHEME,
            },
          ];
        }
        if (node.metaType === NODE_TYPE_MAP.BLOCK) {
          list = [
            ...list,
            {
              targetId: node.metaCode,
              targetType: NODE_TYPE_MAP.BLOCK,
              configType: CONFIGTYPE.SCHEME,
            },
          ];
        }
        if (node.metaType === NODE_TYPE_MAP.ROOM) {
          list = [
            ...list,
            {
              targetId: node.metaCode,
              targetType: NODE_TYPE_MAP.ROOM,
              configType: CONFIGTYPE.SCHEME,
            },
          ];
        }
        if (node.metaType === NODE_TYPE_MAP.DEVICE) {
          list = [
            ...list,
            {
              targetId: node.metaCode,
              targetType: NODE_TYPE_MAP.DEVICE,
              configType: CONFIGTYPE.SCHEME,
            },
          ];
        }
      });
      // // 判断生效域是否发生改变，若没有改变，则不请求接口
      const differencetaTgetIds = difference(list, this.props.checkedKeys);
      this.setState({ okButtonDisabled: !differencetaTgetIds.length });
      this.props.tempSaveIacBlock(list);
    } else {
      this.setState({ okButtonDisabled: visibleKeys.length ? false : true });
      this.props.tempSaveIacBlock([]);
    }
  };

  handleSubmit = () => {
    const { tempSaveIacBlockData, mode, selectedTemplateIds, onSuccess } = this.props;
    // 新建或编辑
    if (mode === 'new') {
      this.props.saveCheckedKeysAction(tempSaveIacBlockData);
      this.props.closeIdcRoomModal();
    } else {
      if (tempSaveIacBlockData.length) {
        let newData = [];
        tempSaveIacBlockData.forEach(item => {
          selectedTemplateIds.forEach(id => {
            const temp = {
              ...item,
              configId: id,
            };
            newData = [...newData, temp];
          });
        });
        this.props.connectTemplateInAreaSearchTemplate({
          monitorConfigs: newData,
          configId: selectedTemplateIds,
          configType: CONFIGTYPE.SCHEME,
          callback: onSuccess,
        });
      } else {
        const newData = selectedTemplateIds.map(id => {
          return {
            configId: id,
            configType: 'SCHEME',
            targetId: null,
            targetType: null,
          };
        });
        this.props.connectTemplateInAreaSearchTemplate({
          monitorConfigs: newData,
          configId: selectedTemplateIds,
          configType: CONFIGTYPE.SCHEME,
          callback: onSuccess,
        });
      }
    }
  };

  render() {
    const { checkedKeys, areaIdecRoomTreeVisible, treeDisabled, tagClosable, showFooter } =
      this.props;
    const { okButtonDisabled } = this.state;

    return (
      <Modal
        title="生效域关联"
        width={1000}
        bodyStyle={{
          height: 492,
        }}
        destroyOnClose
        visible={areaIdecRoomTreeVisible}
        okButtonProps={{ disabled: okButtonDisabled }}
        footer={
          showFooter ? (
            <GutterWrapper mode="horizontal">
              <Button onClick={this.handleCancel}>取消</Button>
              <Button type="primary" onClick={this.handleSubmit}>
                确定
              </Button>
            </GutterWrapper>
          ) : null
        }
        onOk={this.handleSubmit}
        onCancel={this.handleCancel}
        onClose={this.handleCancel}
      >
        <IdcDeviceTreeTransfer
          initCheckedKeys={checkedKeys}
          treeDisabled={treeDisabled}
          tagClosable={tagClosable}
          needVirtualBlock={false}
          onChange={(visibleKeys, checkedKeys, checkedNodes) =>
            this.choiceSelect(visibleKeys, checkedNodes)
          }
        />
      </Modal>
    );
  }
}

const mapStateToProps = ({
  templateGroupView: {
    loading,
    areaIdecRoomTreeVisible,
    sureChoiceIDCBlockTable,
    searchTemplateGroupPagesCondition,
    checkedIdcAndBlock,
    tempSaveIacBlockData,
  },
  common: { citiesTree },
  'resource.spaces': { entities, codes },
  equipmentManage: { idcDevices },
}) => {
  let treeData = {
    treeList: [],
    parallelList: [],
  };

  if (codes && citiesTree) {
    treeData = mergeRegionSpaces(
      citiesTree,
      codes.map(code => entities[code]),
      idcDevices
    );
  }

  return {
    loading,
    areaIdecRoomTreeVisible,
    sureChoiceIDCBlockTable,
    searchTemplateGroupPagesCondition,
    checkedIdcAndBlock,
    tempSaveIacBlockData,
    treeData,
  };
};

const mapDispatchToProps = {
  closeIdcRoomModal: templateGroupViewActions.areaIdecRoomTreeVisible,
  saveCheckedKeysAction,
  closeEditAreaVisible: templateGroupViewActions.editAreaVisible,
  connectRegionToTemplate,
  saveCheckedIdcAndRoom: templateGroupViewActions.saveCheckedIdcAndRoom,
  connectTemplateInAreaSearchTemplate,
  tempSaveIacBlock: templateGroupViewActions.tempSaveIacBlock,
  resetItems: templateGroupViewActions.resetItems,
};

export default connect(mapStateToProps, mapDispatchToProps)(ConnectAreaModal);
