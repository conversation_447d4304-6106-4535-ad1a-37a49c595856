import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';

import uniq from 'lodash/uniq';

import { Button } from '@manyun/base-ui.ui.button';
import { Tag } from '@manyun/base-ui.ui.tag';

import { GutterWrapper, TinyCard } from '@manyun/dc-brain.legacy.components';
import {
  getVisibleKeys,
  mergeRegionSpaces,
} from '@manyun/dc-brain.legacy.pages/template-group-view/utils';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import { equipmentActions } from '@manyun/dc-brain.legacy.redux/actions/equipmentActions';
import { equipmentManageService } from '@manyun/dc-brain.legacy.services';

import LocationDeviceTree from '../../area-config/components/location-device-tree';

function IdcDeviceTreeTransfer({
  treeData,
  nodePathCache,
  onChange,
  initCheckedKeys,
  treeDisabled,
  tagClosable,
  syncCommonData,
  setIdcDevices,
  needVirtualBlock,
}) {
  const [checkedKeys, setCheckedKeys] = useState(initCheckedKeys);
  const [visibleKeys, setVisibleKeys] = useState(
    treeData.parallelList.length
      ? getVisibleKeys(initCheckedKeys, treeData.parallelList, nodePathCache)
      : []
  );
  const [defaultExpandedKeys, setDefaultExpandedKeys] = useState(initCheckedKeys);

  useEffect(() => {
    syncCommonData({ strategy: { space: 'IF_NULL', citiesTree: 'IF_NULL' } });
  }, [syncCommonData]);

  useEffect(() => {
    if (treeData.parallelList.length) {
      setVisibleKeys(getVisibleKeys(checkedKeys, treeData.parallelList, nodePathCache));
    }
  }, [treeData, checkedKeys, nodePathCache]);

  useEffect(() => {
    if (initCheckedKeys && initCheckedKeys.length) {
      // 筛选出所有得包间，
      let rooms = [];
      initCheckedKeys.forEach(code => {
        const [idcTag, blockTag, roomTags, guid] = code.split('.');
        if (guid) {
          rooms = uniq([...rooms, `${idcTag}.${blockTag}.${roomTags}`]);
        } else {
          rooms = uniq([...rooms, code]);
        }
      });
      setDefaultExpandedKeys(defaultExpandedKeys => uniq([...defaultExpandedKeys, ...rooms]));
      rooms.forEach(code => {
        const [idcTag, blockTag, roomTags] = code.split('.');
        (async function getDevices() {
          const { response: devices } = await equipmentManageService.fetchEquipmentListPage({
            idcTag,
            blockTag,
            roomTags: [roomTags],
            pageNum: 1,
            pageSize: 5000,
          });
          if (devices) {
            const newDevices = devices.data.map(item => {
              return {
                ...item,
                metaName: item.name,
                metaCode: `${code}.${item.guid}`,
                metaType: 'DEVICE',
                isLeaf: true,
              };
            });
            setIdcDevices({
              [code]: newDevices,
            });
          }
        })();
      });
    }
  }, [initCheckedKeys, setIdcDevices]);

  const onChecked = Keys => {
    if (!Keys.length) {
      setCheckedKeys([]);
      setVisibleKeys([]);
      emitChange([], []);
      return;
    }
    const newVisibleKeys = getVisibleKeys(Keys, treeData.parallelList, nodePathCache);
    setCheckedKeys(Keys);
    setVisibleKeys(newVisibleKeys);
    emitChange(Keys, newVisibleKeys);
  };

  const getHandleCloseCheckedNode = nodeKey => {
    const newCheckedKeys = checkedKeys.filter(key => {
      const nodePath = nodePathCache[key];
      if (!nodePath) {
        return false;
      }
      if (nodePath.includes(nodeKey)) {
        return false;
      }
      return true;
    });
    const newVisibleKeys = getVisibleKeys(newCheckedKeys, treeData.parallelList, nodePathCache);
    setCheckedKeys(newCheckedKeys);
    setVisibleKeys(newVisibleKeys);
    emitChange(newCheckedKeys, newVisibleKeys);
  };

  const handleClearCheckedKeys = () => {
    setCheckedKeys([]);
    setVisibleKeys([]);
    emitChange([], []);
  };

  const getNodeByKey = nodeKey => {
    const node = treeData.parallelList.find(({ metaCode }) => metaCode === nodeKey);
    if (!node) {
      return {};
    }
    return node;
  };

  const getNodeTitleByKey = nodeKey => {
    const node = getNodeByKey(nodeKey);
    const { metaType, metaName, parentCode } = node;
    if (metaType === 'DEVICE' || metaType === 'ROOM' || metaType === 'BLOCK') {
      return `${parentCode}.${metaName}`;
    }
    return metaName;
  };

  const getCheckedNodes = checkedKeys => {
    return checkedKeys.map(checkedKey => {
      const checkedNode = getNodeByKey(checkedKey);
      return checkedNode;
    });
  };

  const emitChange = (checkedKeys, visibleKeys) => {
    if (typeof onChange === 'function') {
      onChange(visibleKeys, checkedKeys, getCheckedNodes(checkedKeys));
    }
  };
  return (
    <GutterWrapper mode="horizontal" flex>
      <TinyCard bordered style={{ width: '50%' }}>
        <LocationDeviceTree
          checkable
          checkedKeys={checkedKeys}
          defaultExpandedKeys={defaultExpandedKeys}
          disabled={treeDisabled ? treeDisabled : false}
          treeStyle={{
            maxHeight: 'calc(380px - 1rem)',
          }}
          needVirtualBlock={needVirtualBlock}
          onChecked={onChecked}
        />
      </TinyCard>
      <TinyCard
        bordered
        style={{ width: '50%' }}
        title={
          <div style={{ height: 32, lineHeight: '32px' /* 和 <Search /> 保持同等高度 */ }}>
            已选择（{visibleKeys.length}）
          </div>
        }
        extra={
          treeDisabled ? null : (
            <Button
              type="link"
              disabled={!tagClosable && visibleKeys.length}
              onClick={handleClearCheckedKeys}
            >
              清除
            </Button>
          )
        }
      >
        <div
          style={{
            maxHeight: 362,
            overflow: 'scroll',
            gap: '5px',
            display: 'flex',
            flexWrap: 'wrap',
          }}
        >
          {visibleKeys.map(key => (
            <Tag key={key} closable={tagClosable} onClose={() => getHandleCloseCheckedNode(key)}>
              {getNodeTitleByKey(key)}
            </Tag>
          ))}
        </div>
      </TinyCard>
    </GutterWrapper>
  );
}

const mapStateToProps = ({
  common: { citiesTree },
  'resource.spaces': { entities, codes },
  equipmentManage: { idcDevices },
}) => {
  let treeData = {
    treeList: [],
    parallelList: [],
  };
  // 节点全路径数据缓存
  let nodePathCache = {};

  if (citiesTree && codes) {
    treeData = mergeRegionSpaces(
      citiesTree,
      codes.map(code => entities[code]),
      idcDevices
    );
    nodePathCache = getNodePathCache(treeData.treeList);
  }
  return {
    treeData,
    nodePathCache,
  };
};

const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
  setIdcDevices: equipmentActions.setIdcDevices,
};

export default connect(mapStateToProps, mapDispatchToProps)(IdcDeviceTreeTransfer);

function getNodePathCache(data) {
  const obj = {};
  loop(data);
  function loop(data, parentCode) {
    if (data && data.length) {
      data.map(item => {
        obj[item.metaCode] = parentCode ? [...parentCode, item.metaCode] : [item.metaCode];
        loop(item.children, obj[item.metaCode]);
        return obj;
      });
    }
    return obj;
  }
  return obj;
}
