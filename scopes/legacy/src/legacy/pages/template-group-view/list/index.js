import React, { Component } from 'react';
import { connect } from 'react-redux';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';

import TemplateGroupViewOption from './component/option';
import TemplateGroupViewTable from './component/table';

class TemplateGroupView extends Component {
  render() {
    return (
      <GutterWrapper mode="vertical">
        <TemplateGroupViewOption {...this.props} />
        <TemplateGroupViewTable />
      </GutterWrapper>
    );
  }
}

const mapStateToProps = ({ templateGroupView: { loading } }) => ({ loading });

export default connect(mapStateToProps, null)(TemplateGroupView);
