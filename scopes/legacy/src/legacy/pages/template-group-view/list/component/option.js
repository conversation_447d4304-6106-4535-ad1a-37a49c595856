import React, { Component } from 'react';
import { connect, useDispatch } from 'react-redux';

import { FiltersForm, Form, Select } from '@galiojs/awesome-antd';
import trim from 'lodash/trim';

import { Card } from '@manyun/base-ui.ui.card';
import { Input } from '@manyun/base-ui.ui.input';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';

import { AVAI_LABLE } from '@manyun/dc-brain.legacy.constants/alarm';
import {
  commonActions,
  syncCommonDataActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  searchTemplateGroupPagesActions,
  templateGroupViewActions,
} from '@manyun/dc-brain.legacy.redux/actions/templateGroupViewActions';
import { getLocationSearchMap } from '@manyun/dc-brain.legacy.utils';

import AreaLOcationCascader from '../../components/area-location-cascader';

class TemplateGroupViewOptionLegacy extends Component {
  state = {
    pageNum: 1,
    pageSize: 10,
  };

  componentDidMount() {
    const { location, space, searchTemplateGroupPagesCondition } = this.props;
    const { idc } = getLocationSearchMap(location.search, ['idc']);
    this.props.syncCommonData({ strategy: { space: 'FORCED', citiesTree: 'FORCED' } });
    if (idc && space) {
      const areaIdcValue = [space.normalizedList[idc]?.parentCode || 'UNKNOW', idc];
      this.props.updateSearchValues({ areaIdc: { value: areaIdcValue } });
      this.props.searchTemplateGroupPagesActions({
        ...searchTemplateGroupPagesCondition,
      });
    } else {
      this.props.searchTemplateGroupPagesActions({
        ...searchTemplateGroupPagesCondition,
      });
    }
  }

  componentDidUpdate(prevProps) {
    const { location, space } = this.props;
    const { idc } = getLocationSearchMap(location.search, ['idc']);
    if (!prevProps.space && this.props.space && idc) {
      const areaIdcValue = [space.normalizedList[idc]?.parentCode || 'UNKNOW', idc];
      this.props.updateSearchValues({ areaIdc: { value: areaIdcValue } });
      this.props.searchTemplateGroupPagesActions({
        pageNum: 1,
        pageSize: 10,
        idcTag: areaIdcValue[1],
        region: areaIdcValue[0],
      });
    }
  }

  _searchHandler = params => {
    const { areaIdc, deviceCascader, name, lastOperatorName, available } = params;
    this.props.searchTemplateGroupPagesActions({
      pageSize: 10,
      pageNum: 1,
      deviceType: deviceCascader?.[2],
      idcTag: areaIdc?.[1],
      region: areaIdc?.[0],
      name: trim(name),
      lastOperatorName: lastOperatorName?.label,
      available,
    });
  };

  _resetHandler = () => {
    this.props.dispatch(templateGroupViewActions.resetSearchValues());
    this.props.searchTemplateGroupPagesActions({
      pageNum: 1,
      pageSize: 10,
    });
  };

  render() {
    const { form, fields, updateSearchValues } = this.props;

    return (
      <Card>
        <FiltersForm
          form={form}
          fields={Object.keys(fields).map(name => {
            const field = fields[name];

            return {
              ...field,
              name: name.split('.'),
            };
          })}
          items={[
            {
              label: '模板组名称',
              name: 'name',
              control: <Input allowClear />,
            },
            {
              label: '位置',
              name: 'areaIdc',
              control: <AreaLOcationCascader allowClear changeOnSelect />,
            },
            {
              label: '状态',
              name: 'available',
              control: (
                <Select allowClear>
                  {AVAI_LABLE.map(item => {
                    return <Select.Option key={item.key}>{item.value}</Select.Option>;
                  })}
                </Select>
              ),
            },
            {
              label: '更新人',
              name: 'lastOperatorName',
              control: <UserSelect allowClear />,
            },
          ]}
          onFieldsChange={changedFields => {
            updateSearchValues(
              changedFields.reduce((mapper, field) => {
                const name = field.name.join('.');
                mapper[name] = {
                  ...field,
                  name,
                };

                return mapper;
              }, {})
            );
          }}
          onSearch={this._searchHandler}
          onReset={this._resetHandler}
        />
      </Card>
    );
  }
}

function TemplateGroupViewOption(props) {
  const [form] = Form.useForm();
  const dispatch = useDispatch();

  return <TemplateGroupViewOptionLegacy form={form} dispatch={dispatch} {...props} />;
}

const mapStateToProps = ({
  templateGroupView: { loading, selectKeys, searchValues, searchTemplateGroupPagesCondition },
  'resource.spaces': { entities, codes },
}) => {
  return {
    loading,
    selectKeys,
    space: codes ? { normalizedList: entities } : null,
    fields: searchValues,
    searchTemplateGroupPagesCondition,
  };
};
const mapDispatchToProps = {
  searchTemplateGroupPagesActions,
  getSpace: commonActions.space,
  saveIdcBlockCascader: templateGroupViewActions.saveIdcBlockCascader,
  syncCommonData: syncCommonDataActionCreator,
  updateSearchValues: templateGroupViewActions.updateSearchValues,
};

export default connect(mapStateToProps, mapDispatchToProps)(TemplateGroupViewOption);
