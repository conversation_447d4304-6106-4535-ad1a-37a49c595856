import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Divider } from '@manyun/base-ui.ui.divider';
import { message } from '@manyun/base-ui.ui.message';
import { Tag } from '@manyun/base-ui.ui.tag';

import { User } from '@manyun/auth-hub.ui.user';
import { deleteMonitorScheme } from '@manyun/monitoring.service.delete-monitor-scheme';

import { Ellipsis, TinyCard, TinyTable } from '@manyun/dc-brain.legacy.components';
import {
  changeSomeSwitchAvailableAction,
  changeSwitchAvailableAction,
  fetchTemplateGroupAreaAction,
  getAreaConfigById,
  searchTemplateGroupPagesActions,
  selectKeysAction,
  templateGroupViewActions,
} from '@manyun/dc-brain.legacy.redux/actions/templateGroupViewActions';
import {
  generateCreateTemplteGroupConfigUrl,
  generateEditTemplteGroupConfigLocation,
  generateViewTemplteGroupConfigLocation,
} from '@manyun/dc-brain.legacy.utils/urls';

import ConnectAreaModal from '../../components/connect-area-modal';
// import SIZE from '@manyun/dc-brain.legacy.constants/size';
import ViewArea from '../../view/component/view-area';

const getColumns = ctx => [
  {
    title: '模板组名称',
    dataIndex: 'name',
    render: (__, record) => (
      <Ellipsis lines={1} tooltip>
        <Link
          type="link"
          to={generateViewTemplteGroupConfigLocation(record)}
          onClick={() => ctx.review(record)}
        >
          {record.name}
        </Link>
      </Ellipsis>
    ),
  },
  {
    title: '子模板数',
    dataIndex: 'groupNum',
  },
  {
    title: '生效域',
    dataIndex: '',
    render: (text, record) => (
      <Button type="link" onClick={() => ctx.showIds(record)} compact>
        查看
      </Button>
    ),
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
  },
  {
    title: '更新时间',
    dataIndex: 'gmtModified',
  },
  {
    title: '更新人',
    dataIndex: '',
    render: (__, { lastOperator, lastOperatorName }) => (
      <User.Link id={lastOperator} name={lastOperatorName} />
    ),
  },
  {
    title: '状态',
    dataIndex: 'available',
    render: available => (
      <Tag color={available ? 'green' : 'default'}>{available ? '启用' : '停用'}</Tag>
    ),
  },
  {
    title: '操作',
    key: '__actions',
    fixed: 'right',
    render: (__, record) => (
      <span>
        <Link
          type="link"
          to={generateEditTemplteGroupConfigLocation({
            id: record.id,
            name: record.name,
            available: record.available,
          })}
        >
          编辑
        </Link>
        <Divider type="vertical" />
        <Button type="link" onClick={() => ctx.connectArea(record)} compact>
          关联生效域
        </Button>
        <Divider type="vertical" />

        <DeleteConfirm
          variant="popconfirm"
          targetName="该客户"
          title={`你确定要删除 ${record.name} 吗`}
          onOk={async () => {
            const { error } = await deleteMonitorScheme({ id: record.id });
            if (!error) {
              message.success('删除成功');
              ctx.onDelete();
            } else {
              message.error(error.message);
            }
          }}
        >
          <Button aria-label="button: delete" type="link" compact>
            删除
          </Button>
        </DeleteConfirm>
      </span>
    ),
  },
];

const configType = 'SCHEME';

class TemplateGroupViewTable extends Component {
  state = {
    selectedRowKeys: '',
    selectedRows: '',
    pageNo: 1,
    pageSize: 10,
    configProps: {},
    editMess: '',
    checkedKeys: [],
    selectedTemplateIds: [],
    connectType: null,
    treeDisabled: false,
    tagClosable: true,
    showFooter: true,
  };

  onChangePage = (pageNum, pageSize) => {
    const { searchTemplateGroupPagesCondition } = this.props;
    this.setState({ selectedRowKeys: [], selectedRows: [] });
    this.props.searchTemplateGroupPagesActions({
      ...searchTemplateGroupPagesCondition,
      pageNum: pageNum,
      pageSize,
    });
  };
  onDelete = () => {
    const { searchTemplateGroupPagesCondition } = this.props;
    this.props.searchTemplateGroupPagesActions({
      ...searchTemplateGroupPagesCondition,
      pageNum: 1,
      pageSize: searchTemplateGroupPagesCondition.pageSize,
    });
  };

  onChangeSwitch = row => {
    this.props.changeSwitchAvailableAction({ id: row.id, available: !row.available });
  };

  onSelectChange = (selectedRowKeys, selectedRows) => {
    this.setState({ selectedRowKeys, selectedRows });
    this.props.selectKeysAction(selectedRowKeys);
  };

  connectArea = row => {
    let temp = {
      configId: row.id,
      configType: configType,
    };
    this.setState({
      editMess: row,
      treeDisabled: false,
      tagClosable: true,
      showFooter: true,
      selectedTemplateIds: [row.id],
      connectType: 'one',
    });
    this.props.getAreaConfigById({ temp, type: 'openIdcRoomVisible' });
  };

  showIds = row => {
    let temp = {
      configId: row.id,
      configType: 'SCHEME',
    };
    this.setState({
      editMess: row,
    });
    this.props.fetchTemplateGroupArea(temp);
    this.props.viewAreaVisible();
  };

  review = row => {
    this.props.saveBasicInfo({ ...row, configId: row.id });
  };

  startUsing = () => {
    const { selectKeys } = this.props;
    this.props.changeSomeSwitchAvailableAction({
      params: {
        schemeIds: selectKeys,
        available: true,
      },
      successCb: () => {
        message.success(`成功启用${selectKeys.length}项`);
        this.setState({ selectedRowKeys: [] });
      },
    });
  };

  stopUsing = () => {
    const { selectKeys } = this.props;
    this.props.changeSomeSwitchAvailableAction({
      params: {
        schemeIds: selectKeys,
        available: false,
      },
      successCb: () => {
        message.success(`成功停用${selectKeys.length}项`);
        this.setState({ selectedRowKeys: [] });
      },
    });
  };

  batchConnect = () => {
    const { selectKeys } = this.props;
    this.setState({
      selectedTemplateIds: selectKeys,
      treeDisabled: false,
      tagClosable: true,
      showFooter: true,
      checkedKeys: [],
      connectType: 'batch',
    });
    this.props.openIdcRoomVisible();
  };

  render() {
    const {
      loading,
      templateGroupViewData,
      searchTemplateGroupPagesCondition,
      selectKeys,
      choiceIDCBlockTable,
    } = this.props;
    const {
      selectedRows,
      selectedRowKeys,
      editMess,
      selectedTemplateIds,
      connectType,
      treeDisabled,
      tagClosable,
      showFooter,
      checkedKeys,
    } = this.state;

    return (
      <TinyCard>
        <TinyTable
          rowKey="id"
          dataSource={templateGroupViewData.list}
          loading={loading}
          columns={getColumns(this)}
          scroll={{ x: 'max-content' }}
          rowSelection={{
            selectedRowKeys: selectKeys,
            selectedRows,
            onChange: this.onSelectChange,
          }}
          pagination={{
            total: templateGroupViewData.total,
            current: searchTemplateGroupPagesCondition.pageNum,
            pageSize: searchTemplateGroupPagesCondition.pageSize,
            showTotal: () => `共 ${templateGroupViewData.total} 条`,
            showSizeChanger: true,
            onChange: this.onChangePage,
          }}
          actions={[
            <Link key="new-template-group" to={generateCreateTemplteGroupConfigUrl()}>
              <Button type="primary">新建模板组</Button>
            </Link>,
            <Button
              disabled={!selectedRowKeys.length}
              type="primary"
              key="new-template-area"
              onClick={this.batchConnect}
            >
              批量关联
            </Button>,
            <Button
              disabled={!selectedRowKeys.length}
              key="batch-enable"
              type="success"
              onClick={this.startUsing}
            >
              批量启用
            </Button>,
            <Button
              disabled={!selectedRowKeys.length}
              key="batch-disable"
              type="danger"
              onClick={this.stopUsing}
            >
              批量停用
            </Button>,
          ]}
        />

        {/* 查看 作用域 */}
        <ViewArea editMess={editMess} />
        <ConnectAreaModal
          selectedTemplateIds={selectedTemplateIds}
          checkedKeys={
            connectType === 'batch'
              ? checkedKeys
              : choiceIDCBlockTable.map(({ targetId }) => targetId)
          }
          treeDisabled={treeDisabled}
          tagClosable={tagClosable}
          showFooter={showFooter}
          onSuccess={() => {
            const { searchTemplateGroupPagesCondition } = this.props;
            this.props.searchTemplateGroupPagesActions({
              ...searchTemplateGroupPagesCondition,
            });
          }}
        />
      </TinyCard>
    );
  }
}

const mapStateToProps = ({
  templateGroupView: {
    loading,
    templateGroupViewData,
    searchTemplateGroupPagesCondition,
    selectKeys,
    IdcBlockCascader,
    choiceIDCBlockTable,
  },
}) => ({
  loading,
  templateGroupViewData,
  searchTemplateGroupPagesCondition,
  selectKeys,
  IdcBlockCascader,
  choiceIDCBlockTable,
});
const mapDispatchToProps = {
  changeSwitchAvailableAction,
  searchTemplateGroupPagesActions,
  selectKeysAction,
  openIdcRoomVisible: templateGroupViewActions.areaIdecRoomTreeVisible,
  getAreaConfigById,
  saveBasicInfo: templateGroupViewActions.saveBasicInfo,
  changeSomeSwitchAvailableAction,
  fetchTemplateGroupArea: fetchTemplateGroupAreaAction,
  viewAreaVisible: templateGroupViewActions.viewAreaVisible,
  closeEditAreaVisible: templateGroupViewActions.editAreaVisible,
};

export default connect(mapStateToProps, mapDispatchToProps)(TemplateGroupViewTable);
