// import Form from '@ant-design/compatible/es/form';
import { ApiSelect } from '@galiojs/awesome-antd';
import React, { useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';

import { GutterWrapper, TinyTable } from '@manyun/dc-brain.legacy.components';
import { ticketService, visitorManagerService } from '@manyun/dc-brain.legacy.services';
import { getObjectOwnProps } from '@manyun/dc-brain.legacy.utils';

const columns = [
  {
    title: '姓名',
    dataIndex: 'name',
  },
  {
    title: '证件类型',
    dataIndex: ['certificateType', 'name'],
  },
  {
    title: '证件号码',
    dataIndex: 'identityNo',
  },
];

export function AddBlacklist({ getData, checks }) {
  const [visible, setVisible] = useState(false);
  const [tableData, setDataSource] = useState({ list: [], total: 0, pageNum: 1, pageSize: 10 });
  const [selectedRow, setSelectedRow] = useState({ selectedRowKeys: [], selectedRows: [] });
  const [form] = Form.useForm();
  const [reasonForm] = Form.useForm();
  const description = Form.useWatch('description', reasonForm);

  const getBlacklist = async (pageNum, pageSize) => {
    const { response, error } = await visitorManagerService.fetchVisitorDistinctStaffList({
      pageNum,
      pageSize,
      ...form.getFieldsValue(),
    });
    if (error) {
      message.error(error);
      return;
    }
    setDataSource({
      list: response.data?.map(item => {
        if (checks?.some(i => item.identityNo === i.identityNo)) {
          return { ...item, disabled: true };
        }
        return item;
      }),
      total: response.total,
      pageNum,
      pageSize,
    });
  };

  const onSelectChange = (selectedRowKeys, selectedRows) => {
    setSelectedRow({ selectedRowKeys, selectedRows });
  };

  const onChangePage = (pageNum, pageSize) => {
    getBlacklist(pageNum, pageSize);
  };

  const handleSearch = () => {
    getBlacklist(1, tableData.pageSize);
  };

  return (
    <GutterWrapper>
      <Button
        type="primary"
        onClick={() => {
          if (!visible) {
            getBlacklist(1, 10);
          }
          form.resetFields();
          reasonForm.resetFields();
          setVisible(true);
        }}
        onCancel={() => {
          form.resetFields();
          reasonForm.resetFields();
        }}
      >
        添加黑名单
      </Button>
      <Modal
        width="1101px"
        title="添加黑名单"
        open={visible}
        okButtonProps={{ disabled: !selectedRow.selectedRows.length || !description }}
        onOk={() => {
          if (
            selectedRow.selectedRows.some(
              (item, index) =>
                selectedRow.selectedRows.findIndex(obj => obj.identityNo === item.identityNo) !==
                index
            )
          ) {
            message.error('同证件号只能添加一位人员');
            return;
          }
          createBlacklist({ ids: selectedRow.selectedRowKeys, description }, () => {
            getData();
          });
          form.resetFields();
          reasonForm.resetFields();
          setVisible(false);
          setSelectedRow({ selectedRowKeys: [], selectedRows: [] });
        }}
        onCancel={() => {
          form.resetFields();
          reasonForm.resetFields();
          setVisible(false);
          setSelectedRow({ selectedRowKeys: [], selectedRows: [] });
        }}
      >
        <GutterWrapper mode="vertical">
          <Form form={form} layout="inline">
            <Form.Item label="姓名" name="name">
              <Input allowClear style={{ width: 200 }} />
            </Form.Item>

            <Form.Item label="证件类型" name="certificateType">
              <ApiSelect
                style={{ width: 200 }}
                showSearch
                allowClear
                dataService={async () => {
                  const { response } = await ticketService.fetchCertificateType();
                  if (response) {
                    return Promise.resolve(getObjectOwnProps(response));
                  } else {
                    return Promise.resolve([]);
                  }
                }}
              />
            </Form.Item>
            <Form.Item label="证件号" name="identityNo">
              <Input allowClear style={{ width: 200 }} />
            </Form.Item>
            <Form.Item>
              <GutterWrapper>
                <Button type="primary" onClick={handleSearch}>
                  搜索
                </Button>
                <Button
                  onClick={() => {
                    form.resetFields();
                    getBlacklist(tableData.pageNo, tableData.pageSize);
                  }}
                >
                  重置
                </Button>
              </GutterWrapper>
            </Form.Item>
          </Form>
          <TinyTable
            scroll={{ y: 'calc(48vh)' }}
            rowKey="id"
            columns={columns}
            rowSelection={{
              selectedRowKeys: selectedRow.selectedRowKeys,
              selectedRows: selectedRow.selectedRows,
              onChange: onSelectChange,
              getCheckboxProps: record => ({
                disabled: record.disabled, // 根据 `disabled` 字段禁用
              }),
            }}
            pagination={{
              total: tableData.total,
              current: tableData.pageNum,
              onChange: onChangePage,
              pageSize: tableData.pageSize,
            }}
            dataSource={tableData.list}
          />
          <Form form={reasonForm}>
            <Form.Item
              label="原因"
              name="description"
              rules={[
                {
                  required: true,
                  whitespace: true,
                  message: '原因必填',
                },
                {
                  type: 'string',
                  max: 200,
                  message: '最多输入 200 个字符！',
                },
              ]}
            >
              <Input.TextArea />
            </Form.Item>
          </Form>
        </GutterWrapper>
      </Modal>
    </GutterWrapper>
  );
}

async function createBlacklist(data, callback) {
  const { response, error } = await visitorManagerService.fetchAddVisitorBlacklist(data);

  callback();

  if (error) {
    message.error(error);
    return;
  }

  message.success('添加成功！');
  return response;
}

export default AddBlacklist;
