import dayjs from 'dayjs';
import React, { useCallback, useEffect } from 'react';
import { connect } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { UserLink } from '@manyun/iam.ui.user-link';

import { TinyTable } from '@manyun/dc-brain.legacy.components';
import {
  batchRemoveVisitorBlacklistActionCreator,
  setPaginationThenGetDataActionCreator,
  visitorBlacklistActionCreator,
  visitorBlacklistActions,
} from '@manyun/dc-brain.legacy.redux/actions/visitorBlacklistActions';

import AddBlacklist from './table-actions';

export function DataTable({
  data,
  total,
  pageNum,
  pageSize,
  selectedRowKeys,
  loading,
  getData,
  setPagination,
  setSelectedRowKeys,
  removeBlacklist,
}) {
  useEffect(() => {
    getData();
  }, [getData]);

  const paginationChangeHandler = useCallback(
    (current, size) => {
      setPagination({ pageNum: current, pageSize: size });
    },
    [setPagination]
  );

  return (
    <TinyTable
      rowKey="id"
      actions={[
        <AddBlacklist key="add" checks={data} getData={() => getData()} />,
        <Button
          key="leave-button"
          type="danger"
          disabled={!selectedRowKeys.length}
          onClick={() => {
            Modal.confirm({
              title: '确认要将人员移除黑名单吗？',
              content: '移除黑名单之后人员将可以入室，如需入室需发起申请。',
              okText: '确认',
              cancelText: '取消',
              onOk() {
                removeBlacklist({ ids: selectedRowKeys, entryStatus: 'LEAVED' });
              },
            });
          }}
        >
          移除黑名单
        </Button>,
      ]}
      columns={getColumns({ removeBlacklist })}
      scroll={{ x: 'max-context' }}
      align="left"
      dataSource={data}
      loading={loading}
      rowSelection={{
        selectedRowKeys,
        onChange: keys => {
          setSelectedRowKeys(keys);
        },
      }}
      pagination={{
        total,
        current: pageNum,
        pageSize,
        onChange: paginationChangeHandler,
      }}
    />
  );
}

const mapStateToProps = ({
  visitorBlacklist: {
    data,
    total,
    pagination: { pageNum, pageSize },
    selectedRowKeys,
    loading,
  },
}) => ({
  data,
  total,
  loading,
  pageNum,
  pageSize,
  selectedRowKeys,
});
const mapDispatchToProps = {
  getData: visitorBlacklistActionCreator,
  removeBlacklist: batchRemoveVisitorBlacklistActionCreator,
  setPagination: setPaginationThenGetDataActionCreator,
  setSelectedRowKeys: visitorBlacklistActions.setSelectedRowKeys,
};

export default connect(mapStateToProps, mapDispatchToProps)(DataTable);

const getColumns = ctx => [
  {
    title: '姓名',
    dataIndex: 'name',
    width: 240,
    fixed: 'left',
  },
  {
    title: '证件类型',
    dataIndex: ['certificateType', 'name'],
    width: 130,
  },
  {
    title: '证件号码',
    dataIndex: 'identityNo',
    width: 260,
  },
  {
    title: '操作人',
    dataIndex: 'operatorId',
    width: 130,
    render: (_, record) => (
      <UserLink userId={record.operatorId} userName={record.operatorName} native target="_blank" />
    ),
  },
  {
    title: '操作时间',
    dataIndex: 'gmtModified',
    width: 200,
    render: val => (val ? dayjs(val).format('YYYY-MM-DD HH:mm:ss') : '--'),
  },
  {
    title: '原因',
    dataIndex: 'description',
    ellipsis: {
      showTitle: false,
    },
    render: val => (
      <Tooltip placement="topLeft" title={val}>
        {val}
      </Tooltip>
    ),
  },
  {
    title: '操作',
    dataIndex: 'id',
    width: 100,
    fixed: 'right',
    render: (id, record) => (
      <span>
        <Button
          style={buttonStyle}
          type="link"
          size="small"
          onClick={() => {
            Modal.confirm({
              title: '确认要将' + record.name + '移除黑名单吗？',
              content: '移除黑名单之后' + record.name + '将可以入室，如需入室需发起申请。',
              okText: '确认',
              cancelText: '取消',
              onOk() {
                ctx.removeBlacklist({ ids: [record.id] });
              },
            });
          }}
        >
          移除黑名单
        </Button>
      </span>
    ),
  },
];

const buttonStyle = { padding: 0, height: 'auto' };
