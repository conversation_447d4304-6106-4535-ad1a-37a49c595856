import React from 'react';
import { connect } from 'react-redux';

// import Form from '@ant-design/compatible/es/form';
import { ApiSelect, FiltersForm, Form } from '@galiojs/awesome-antd';

import { Input } from '@manyun/base-ui.ui.input';

import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  resetSearchValuesActionCreator,
  visitorBlacklistActionCreator,
  visitorBlacklistActions,
} from '@manyun/dc-brain.legacy.redux/actions/visitorBlacklistActions';
import { ticketService } from '@manyun/dc-brain.legacy.services';
import { getObjectOwnProps } from '@manyun/dc-brain.legacy.utils';

export function SearchForm({ fields, updateSearchValues, onSearch, onReset }) {
  const [form] = Form.useForm();

  return (
    <FiltersForm
      fields={Object.keys(fields).map(name => {
        const field = fields[name];

        return {
          ...field,
          name: name.split('.'),
        };
      })}
      form={form}
      items={[
        {
          label: '姓名',
          name: 'name',
          control: <Input allowClear />,
        },
        {
          label: '证件类型',
          name: 'certificateType',
          control: (
            <ApiSelect
              showSearch
              allowClear
              fieldNames={{ label: 'label', value: 'value' }}
              dataService={async () => {
                const { response } = await ticketService.fetchCertificateType();
                if (response) {
                  return Promise.resolve(getObjectOwnProps(response));
                } else {
                  return Promise.resolve([]);
                }
              }}
              style={{ width: 200 }}
            />
          ),
        },
        {
          label: '证件号码',
          name: 'identityNo',
          // decorateOptions: ({ fields }) => ({
          //   initialValue: fields.identityNo?.value,
          // }),
          control: <Input allowClear />,
        },
      ]}
      // updateSearchValues={updateSearchValues}
      onSearch={onSearch}
      onReset={onReset}
      onFieldsChange={changedFields => {
        updateSearchValues(
          changedFields.reduce((mapper, field) => {
            const name = field.name.join('.');
            mapper[name] = {
              ...field,
              name,
            };

            return mapper;
          }, {})
        );
      }}
    />
  );
}

const mapStateToProps = ({ visitorBlacklist: { searchValues } }) => ({ fields: searchValues });
const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
  updateSearchValues: visitorBlacklistActions.updateSearchValues,
  onSearch: visitorBlacklistActionCreator,
  onReset: resetSearchValuesActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(SearchForm);
