import { ApiSelect, FiltersForm, Form } from '@galiojs/awesome-antd';
import React, { Component } from 'react';
import { connect, useDispatch } from 'react-redux';
import { Link } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Empty } from '@manyun/base-ui.ui.empty';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { useAuthorized } from '@manyun/iam.hook.use-authorized';
import { generateRoomMonitoringUrl } from '@manyun/monitoring.route.monitoring-routes';
import { generateSpecificGraphixRoutePath } from '@manyun/resource-hub.route.resource-routes';
import { fetchSpecs } from '@manyun/resource-hub.service.fetch-specs';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';

import { GutterWrapper, TinyCard, TinyTable } from '@manyun/dc-brain.legacy.components';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import { fetchRoomListPage, roomActions } from '@manyun/dc-brain.legacy.redux/actions/roomActions';
import { infrastructureService, roomManageService } from '@manyun/dc-brain.legacy.services';
import { getSpaceGuidMap } from '@manyun/dc-brain.legacy.utils';

import CreatePower from '../components/create-room';
import EditRoom from '../components/edit-room';
import ViewRoomDetail from '../components/view-room-detail';
import AssociatedRoom from './components/associated-room';

const { RangePicker } = DatePicker;
const columns = ctx => [
  {
    title: '包间编号',
    dataIndex: 'tag',
    render: (tag, record) => (
      <Link
        target="_blank"
        onClick={() => {
          window.open(
            generateRoomMonitoringUrl({
              idc: record.idcTag,
              block: record.blockTag,
              room: tag,
            })
          );
        }}
      >
        {tag}
      </Link>
    ),
  },
  {
    title: '包间名称',
    dataIndex: 'name',
  },
  {
    title: '机房编号',
    dataIndex: 'idcTag',
  },
  {
    title: '机房名称',
    dataIndex: 'idcName',
  },
  {
    title: '楼栋编号',
    dataIndex: 'blockTag',
  },
  {
    title: '楼层',
    dataIndex: 'floorTag',
  },
  {
    title: '包间类别',
    dataIndex: 'roomTypeName',
  },
  {
    title: '投产日期',
    dataIndex: 'operationTime',
  },

  {
    title: '状态',
    dataIndex: ['operationStatus', 'name'],
  },
  {
    title: '设计总功率',
    dataIndex: 'ratedPower',
    render: text => <span>{text ? `${text}kW` : '--'}</span>,
  },
  {
    title: '制冷架构',
    dataIndex: 'refrigerationStructure',
  },
  {
    title: '操作',
    key: '__actions',
    fixed: 'right',
    exportable: false,
    width: 168,
    render: (__, record) => {
      return <Operator record={record} ctx={ctx} />;
    },
  },
];
function Operator({ record, ctx }) {
  const [, { checkCode }] = useAuthorized();
  const { idc, block, room } = getSpaceGuidMap(record.guid);

  return (
    <>
      <Space align="center" size={0}>
        {checkCode('topology:save') && (
          <>
            <Button type="link" compact>
              <Link
                target="_blank"
                to={generateSpecificGraphixRoutePath({
                  topologyType: 'ROOM_FACILITY',
                  idc,
                  block,
                  room,
                  mode: 'new',
                })}
              >
                包间视图
              </Link>
            </Button>
            <Divider type="vertical" />
          </>
        )}

        {checkCode('room:update') && (
          <>
            <Button type="link" compact onClick={() => ctx.editRoom(record)}>
              编辑
            </Button>
            <Divider type="vertical" />
          </>
        )}

        {checkCode('room:update') && (
          <>
            <AssociatedRoom
              currentRoomGuid={record.guid}
              blockGuid={record.idcTag + '.' + record.blockTag}
              id={record.id}
              relateRoom={record.relateRoom}
            />
            <Divider type="vertical" />
          </>
        )}
        {checkCode('room:delete') && (
          <DeleteConfirm targetName={record.guid} onOk={ctx._getDeleteHandler(record.guid)}>
            <Button type="link" compact>
              删除
            </Button>
          </DeleteConfirm>
        )}
      </Space>
    </>
  );
}
function NewBuilt({ ctx }) {
  const [, { checkCode }] = useAuthorized();
  return (
    <>
      {checkCode('room:add') && (
        <Button key={1} type="primary" onClick={ctx.createRoom}>
          新建
        </Button>
      )}
    </>
  );
}
function NewTinyTable(props) {
  const [, { checkCode }] = useAuthorized();

  return (
    <TinyTable
      {...props}
      columns={props.columns.filter(item => {
        if (item.key === '__actions') {
          return ['topology:save', 'room:update', 'room:delete'].every(i => checkCode(i));
        } else {
          return true;
        }
      })}
      showExport={
        checkCode('room:export')
          ? {
              filename: '包间数据',
              useServiceOnly: true,
            }
          : false
      }
    />
  );
}

class RoomManage extends Component {
  state = {
    operationStatus: null,
    buildList: [],
    pageNo: 1,
    pageSize: 10,
    selectedRowKeys: [],
    selectedRows: [],
    operationStatusLoading: false,
    roomTypeLoading: false,
  };

  componentDidMount() {
    this.props.syncCommonData({ strategy: { space: 'FORCED', roomTypes: 'IF_NULL' } });
    // 分页查询包间信息
    const { roomPageCondition } = this.props;
    this.props.fetchRoomListPage({
      ...roomPageCondition,
    });
  }

  getFormData = () => {
    const fieldsValue = this.props.searchValues;
    const params = Object.keys(fieldsValue).reduce((map, fieldName) => {
      const value = fieldsValue[fieldName]?.value;
      if ([null, undefined, ''].includes(value) || (Array.isArray(value) && value.length === 0)) {
        return map;
      } else if (fieldName === 'IdcBlock') {
        map.idcTag = value.split('.')?.[0];
        map.blockTag = value.split('.')?.[1];
      } else if (fieldName === 'operationTime') {
        map.operationTimeEnd = value?.[1]?.endOf('day').valueOf();
        map.operationTimeStart = value?.[0]?.startOf('day').valueOf();
      } else if (fieldName === 'name' || fieldName === 'roomTag') {
        map[fieldName] = value.trim();
      } else {
        map[fieldName] = value;
      }
      return map;
    }, {});
    return params;
  };

  onSelectChange = (selectedRowKeys, selectedRows) => {
    this.setState({ selectedRowKeys, selectedRows });
  };

  onChangePage = (pageNo, pageSize) => {
    const { roomPageCondition } = this.props;
    this.setState({ selectedRowKeys: [], selectedRows: [] });
    this.props.fetchRoomListPage({
      ...roomPageCondition,
      pageNum: pageNo,
      pageSize,
    });
  };

  // 新建包间
  createRoom = () => {
    this.props.createRoomVisible();
  };

  searchRoomList = () => {
    const params = this.getFormData();
    this.props.fetchRoomListPage({
      ...params,
      pageNum: 1,
      pageSize: 10,
    });
  };

  // 查看
  showRoomDetail = row => {
    this.props.viewRoomVisible();
    this.props.viewRoomMess(row);
  };

  // 编辑
  editRoom = async row => {
    const params = { deviceType: this.props.roomDeviceType, modelId: row.guid };

    const { data, error } = await fetchSpecs(params);
    if (error) {
      message.error(error.message);
      return;
    }
    // 编辑弹框状态
    this.props.editRoomVisible();
    // 编辑信息
    this.props.editRoomMess({ ...row, specInfo: data.data });
  };

  handleReset = () => {
    this.props.dispatch(this.props.resetSearchValues());
    this.props.fetchRoomListPage({
      pageNum: 1,
      pageSize: 10,
    });
  };

  _getDeleteHandler =
    roomGuid =>
    async ({ reason }) => {
      if (!roomGuid) {
        console.warn(`room guid expected!`);
        return;
      }

      const { error } = await infrastructureService.deleteRoom({
        guid: roomGuid,
        operatorNotes: reason,
      });

      if (error) {
        message.error(error);
        return false;
      }
      message.success(`${roomGuid} 已成功删除！`);

      const {
        roomPageCondition: { pageNum, pageSize },
      } = this.props;
      const searchValues = {
        ...this.getFormData(),
        pageNum,
        pageSize,
      };
      this.props.fetchRoomListPage(searchValues);

      return true;
    };

  render() {
    const {
      roomPage,
      loading,
      roomPageCondition,
      updateSearchValues,
      searchValues,
      form,
      roomDeviceType,
    } = this.props;
    const { selectedRowKeys, selectedRows, operationStatusLoading, roomTypeLoading } = this.state;

    return (
      <GutterWrapper mode="vertical">
        {/* 操作 */}
        <TinyCard>
          <FiltersForm
            form={form}
            items={[
              {
                label: '包间编号',
                name: 'roomTag',
                control: <Input allowClear />,
              },
              {
                label: '包间名称',
                name: 'name',
                control: <Input allowClear />,
              },
              {
                label: '位置',
                name: 'IdcBlock',
                control: <LocationTreeSelect authorizedOnly allowClear />,
              },
              {
                label: '包间类型',
                name: 'roomType',
                control: (
                  <ApiSelect
                    showSearch
                    allowClear
                    loading={roomTypeLoading}
                    fieldNames={{ label: 'value', value: 'key' }}
                    dataService={async () => {
                      this.setState({
                        roomTypeLoading: true,
                      });
                      const response = await roomManageService.fetchRoomType();
                      this.setState({
                        roomTypeLoading: false,
                      });
                      if (response) {
                        return Promise.resolve(response);
                      } else {
                        return Promise.resolve([]);
                      }
                    }}
                    notFoundContent={roomTypeLoading ? <Spin size="small" /> : <Empty />}
                    trigger="onDidMount"
                  />
                ),
              },
              {
                label: '状态',
                name: 'operationStatus',
                control: (
                  <ApiSelect
                    allowClear
                    loading={operationStatusLoading}
                    fieldNames={{ label: 'value', value: 'key' }}
                    dataService={async () => {
                      this.setState({
                        operationStatusLoading: true,
                      });
                      const response = await roomManageService.fetchOperationStatus();
                      this.setState({
                        operationStatusLoading: false,
                      });
                      if (response) {
                        return Promise.resolve(response);
                      } else {
                        return Promise.resolve([]);
                      }
                    }}
                    notFoundContent={<Spin size="small" />}
                    trigger="onDidMount"
                  />
                ),
              },
              {
                label: '投产日期',
                name: 'operationTime',
                span: 2,
                decorateOptions: ({ fields }) => ({
                  initialValue: fields.operationTime?.value,
                }),
                control: <RangePicker format="YYYY-MM-DD" allowClear />,
              },
            ]}
            fields={Object.keys(searchValues).map(name => {
              const field = searchValues[name];
              return {
                ...field,
                name: name.split('.'),
              };
            })}
            onFieldsChange={changedFields => {
              const fields = changedFields.reduce((mapper, field) => {
                const name = field.name.join('.');
                mapper[name] = {
                  ...field,
                  name,
                };
                return mapper;
              }, {});
              updateSearchValues(fields);
            }}
            onSearch={this.searchRoomList}
            onReset={this.handleReset}
          />
        </TinyCard>
        {/* 列表 */}
        <TinyCard>
          <NewTinyTable
            rowKey="id"
            exportServices={() =>
              roomManageService.downloadRoomList({ ...this.getFormData(), ids: selectedRowKeys })
            }
            actions={<NewBuilt ctx={this} />}
            loading={loading}
            dataSource={roomPage.list}
            columns={columns(this)}
            scroll={{ x: 'max-content' }}
            rowSelection={{
              selectedRowKeys,
              selectedRows,
              onChange: this.onSelectChange,
            }}
            pagination={{
              total: roomPage.total,
              current: roomPageCondition.pageNum,
              pageSize: roomPageCondition.pageSize,
              onChange: this.onChangePage,
            }}
            showExport={{
              filename: '包间数据',
              useServiceOnly: true,
            }}
          />
        </TinyCard>

        {/* 新建弹窗 */}
        <CreatePower roomDeviceType={roomDeviceType} />
        {/* 查看弹框 */}
        <ViewRoomDetail />
        {/* 编辑弹框 */}
        <EditRoom />
      </GutterWrapper>
    );
  }
}

const mapStateToProps = ({
  roomManage: { loading, roomPage, roomAndBuildList, roomPageCondition, searchValues },
  common: { roomTypes },
  config: { configMap },
}) => {
  const configUtil = new ConfigUtil(configMap.current);
  const roomDeviceType = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_ROOM);
  return {
    roomDeviceType,
    loading,
    roomPage,
    roomAndBuildList,
    roomPageCondition,
    searchValues,
    roomTypes,
  };
};

const mapDispatchToProps = {
  roomActions,
  fetchRoomListPage,
  createRoomVisible: roomActions.createRoomVisible,
  viewRoomVisible: roomActions.viewRoomVisible,
  viewRoomMess: roomActions.viewRoomDetailMess,
  editRoomVisible: roomActions.editRoomVisible,
  editRoomMess: roomActions.editRoomMess,
  syncCommonData: syncCommonDataActionCreator,
  updateSearchValues: roomActions.updateSearchValues,
  resetSearchValues: roomActions.resetSearchValues,
};

function RoomManageList(props) {
  const [form] = Form.useForm();
  const dispatch = useDispatch();

  return <RoomManage form={form} dispatch={dispatch} {...props} />;
}

export default connect(mapStateToProps, mapDispatchToProps)(RoomManageList);
