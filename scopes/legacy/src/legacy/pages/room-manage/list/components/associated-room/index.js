import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';

import { Button } from '@manyun/base-ui.ui.button';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Typography } from '@manyun/base-ui.ui.typography';

import { RoomSelect } from '@manyun/resource-hub.ui.room-select';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';
import { fetchUpdateRoom } from '@manyun/dc-brain.legacy.redux/actions/roomActions';

const explainInfo = '关联精密空调所在的包间号，包间视图即可显示该IT包间的空调信息';

function AssociatedRoom({
  blockGuid,
  currentRoomGuid,
  id,
  fetchUpdateRoomAction,
  roomPageCondition,
  relateRoom,
}) {
  const [visible, setVisible] = useState(false);
  const [relateRooms, setRelateRooms] = useState([]);

  useEffect(() => {
    if (relateRoom) {
      const rooms = relateRoom?.split(',');
      setRelateRooms(rooms);
    }
  }, [relateRoom, visible]);

  const onOk = () => {
    const params = {
      id,
      relateRoom: relateRooms.join(','),
      operatorNotes: 'blank',
    };
    fetchUpdateRoomAction({
      editMess: params,
      roomPageCondition,
      successCb: () => setVisible(false),
    });
  };

  return (
    <>
      <Button type="link" style={{ padding: 0, height: 'auto' }} onClick={() => setVisible(true)}>
        关联包间
      </Button>
      <AssociatedRoomModal
        visible={visible}
        blockGuid={blockGuid}
        currentRoomGuid={currentRoomGuid}
        updateVisible={value => setVisible(value)}
        relateRooms={relateRooms}
        setRelateRooms={setRelateRooms}
        onOk={onOk}
      />
    </>
  );
}
const mapStateToProps = ({ roomManage: { roomPageCondition } }) => ({
  roomPageCondition,
});

const mapDispatchToProps = {
  fetchUpdateRoomAction: fetchUpdateRoom,
};
export default connect(mapStateToProps, mapDispatchToProps)(AssociatedRoom);

function AssociatedRoomModal({
  visible,
  blockGuid,
  currentRoomGuid,
  updateVisible,
  onOk,
  relateRooms,
  setRelateRooms,
}) {
  return (
    <Modal
      width={480}
      visible={visible}
      title="关联包间"
      onCancel={() => updateVisible(false)}
      onOk={onOk}
    >
      <GutterWrapper mode="vertical">
        <Typography.Text>{explainInfo}</Typography.Text>
        <Form.Item label="关联包间" colon={false} labelCol={{ span: 4 }}>
          <RoomSelect
            blockGuid={blockGuid}
            disabledRoomGuids={currentRoomGuid}
            mode="multiple"
            showSearch
            showArrow
            style={{ width: 200 }}
            maxTagCount="responsive"
            value={relateRooms}
            onChange={value => setRelateRooms(value)}
          />
        </Form.Item>
      </GutterWrapper>
    </Modal>
  );
}
