import React, { Component } from 'react';
import { connect } from 'react-redux';

import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Modal } from '@manyun/base-ui.ui.modal';

import { roomActions } from '@manyun/dc-brain.legacy.redux/actions/roomActions';

import { WAREHOUSE } from '../constants';

class ViewRoomDetail extends Component {
  hideModal = () => {
    this.props.viewVisible();
  };

  render() {
    const { viewRoomVisible, RoomDetailMess } = this.props;
    return (
      <div>
        <Modal
          width="900px"
          title="包间"
          visible={viewRoomVisible}
          onOk={this.hideModal}
          onCancel={this.hideModal}
          footer={null}
        >
          <Descriptions>
            <Descriptions.Item label="机房名称">{RoomDetailMess.idcName}</Descriptions.Item>
            <Descriptions.Item label="机房编号">{RoomDetailMess.idcTag}</Descriptions.Item>
            <Descriptions.Item label="楼栋编号">{RoomDetailMess.blockTag}</Descriptions.Item>
            <Descriptions.Item label="楼层">{RoomDetailMess.floor}</Descriptions.Item>
            <Descriptions.Item label="包间编号">{RoomDetailMess.tag}</Descriptions.Item>
            <Descriptions.Item label="包间类别">{RoomDetailMess.roomTypeName}</Descriptions.Item>

            <Descriptions.Item label="投产日期">{RoomDetailMess.operationTime}</Descriptions.Item>
            <Descriptions.Item label="状态">
              {RoomDetailMess.operationStatus ? RoomDetailMess.operationStatus.name : null}
            </Descriptions.Item>
            <Descriptions.Item label="制冷架构">
              {RoomDetailMess.refrigerationStructure}
            </Descriptions.Item>
            {RoomDetailMess.roomType === WAREHOUSE.code && (
              <Descriptions.Item label="库房面积">{RoomDetailMess.area}㎡</Descriptions.Item>
            )}
          </Descriptions>
        </Modal>
      </div>
    );
  }
}
const mapStateToProps = ({ roomManage: { viewRoomVisible, RoomDetailMess } }) => ({
  viewRoomVisible,
  RoomDetailMess,
});

const mapDispatchToProps = {
  roomActions,
  viewVisible: roomActions.viewRoomVisible,
};

export default connect(mapStateToProps, mapDispatchToProps)(ViewRoomDetail);
