import Form from '@ant-design/compatible/es/form';
import { ApiSelect, Select } from '@galiojs/awesome-antd';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { fetchSpecs } from '@manyun/resource-hub.service.fetch-specs';
import { FloorSelect } from '@manyun/resource-hub.ui.floor-select';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';
import { VALUE_TYPE_KEY_MAP } from '@manyun/resource-hub.ui.spec-select';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

import { TinyDrawer } from '@manyun/dc-brain.legacy.components';
import {
  fetchCreateRoomConfirm,
  roomActions,
} from '@manyun/dc-brain.legacy.redux/actions/roomActions';
import * as roomService from '@manyun/dc-brain.legacy.services/roomService';

import { WAREHOUSE } from '../constants';

class CreatePower extends Component {
  state = {
    operationTime: '',
    blockGuid: '',
  };

  componentDidUpdate(prevProps) {
    if (
      this.props.createRoomVisible &&
      this.props.createRoomVisible !== prevProps.createRoomVisible
    ) {
      this.getSpecList();
    }
  }

  getSpecList = async () => {
    const { data, error } = await fetchSpecs({ deviceType: this.props.roomDeviceType });
    if (error) {
      message.error(error.message);
      return;
    }
    this.setState({ specInfo: data.data });
  };

  onChange = (value, dateString) => {
    this.setState({
      operationTime: dateString,
    });
  };

  closeCreateRoom = () => {
    this.props.closeVisible();
  };

  submitCreateRoom = event => {
    event.preventDefault();
    this.props.form.validateFieldsAndScroll(
      { scroll: { offsetBottom: TinyDrawer.FOOTER_HEIGHT } },

      async (err, values) => {
        if (err) {
          return;
        }

        const { operationTime, specInfo } = this.state;
        const params = {
          ...values,
          idcTag: values.IdcBlock[0],
          blockTag: getSpaceGuidMap(values.IdcBlock[1]).block,
          operationTime,
          area: values.roomType === WAREHOUSE.code ? values.area : null,
        };

        if (values.specParams) {
          specInfo.forEach((item, index) => {
            values.specParams[index].specId = item.id;
            values.specParams[index].specName = item.specName;
          });

          const arr = values.specParams.filter(item => !!item.specValue);
          const res = [];

          arr.forEach(item => {
            const roomItem = specInfo.find(val => val.id === item.specId);
            if (Array.isArray(item.specValue) && roomItem?.optionType) {
              item.specValue.map(valItem => {
                res.push({
                  specId: item.specId,
                  specName: item.specName,
                  specValue: Array.isArray(valItem) ? valItem[valItem.length - 1] : valItem,
                });
              });

              return;
            }

            res.push({
              specId: item.specId,
              specName: item.specName,
              specValue: Array.isArray(item.specValue)
                ? item.specValue[item.specValue.length - 1]
                : item.specValue,
            });
          });

          params.specParams = res;
        } else {
          params.specParams = [];
        }

        this.props.fetchCreateRoomConfirm(params);
      }
    );
  };

  getOptions = options => {
    let newOptions = [];
    const optionsArr = options.split(',');
    newOptions = optionsArr.map(option => ({ label: option, value: option }));
    return newOptions;
  };

  getSpecComponent = item => {
    const optComponent = item.inputWay === 'COMPONENT' && item.options && JSON.parse(item.options);

    switch (item.inputWay) {
      case 'INPUT':
        return item.valueType === VALUE_TYPE_KEY_MAP.NUMBER ? (
          <InputNumber
            style={{ width: 200 }}
            addonAfter={item.specUnit}
            min={0.01}
            max={999999.99}
            precision={2}
          />
        ) : (
          <Input style={{ width: 200 }} allowClear addonAfter={item.specUnit} />
        );
      case 'OPT':
        return (
          <Select
            style={{ width: 200 }}
            allowClear
            options={item.options && this.getOptions(item.options ?? '')}
            mode={item.optionType ? 'multiple' : undefined}
          />
        );
      case 'COMPONENT':
        return optComponent?.components === 'location' ? (
          <LocationCascader
            style={{ width: 200 }}
            nodeTypes={optComponent?.nodes}
            authorizedOnly={
              Array.isArray(optComponent?.componentProps) &&
              optComponent?.componentProps.indexOf('authorized') > -1
            }
            includeVirtualBlocks={
              Array.isArray(optComponent?.componentProps) &&
              optComponent?.componentProps.indexOf('virtual') > -1
            }
            changeOnSelect={false}
            multiple={item.optionType}
          />
        ) : (
          <MetaTypeSelect
            style={{ width: 200 }}
            metaType={optComponent?.componentProps}
            allowClear
            mode={item.optionType ? 'multiple' : undefined} //只处理multiple,其他类型不需要
          />
        );
      default:
        return null;
    }
  };

  getLocationArr = value => {
    if (!value) {
      return;
    }
    const arr = value.split('.');
    const data = [arr[0]];
    for (let i = 1; i < arr.length; i++) {
      data[i] = data[i - 1] + '.' + arr[i];
    }
    return data;
  };

  render() {
    const { form, createRoomVisible, createRoomLoading } = this.props;
    const { specInfo } = this.state;
    const { getFieldDecorator, getFieldValue } = form;

    return (
      <div>
        <TinyDrawer
          width={416}
          title="新建包间"
          destroyOnClose
          visible={createRoomVisible}
          submitButtonLoading={createRoomLoading}
          onClose={this.closeCreateRoom}
          onCancel={this.closeCreateRoom}
          onSubmit={this.submitCreateRoom}
        >
          <Form layout="vertical" onSubmit={this.submitCreateRoom}>
            <Form.Item label="位置">
              {getFieldDecorator('IdcBlock', {
                rules: [{ required: true, message: '请选择位置' }],
              })(
                <LocationCascader
                  style={{ width: 200 }}
                  authorizedOnly
                  placeholder=""
                  changeOnSelect={false}
                  getPopupContainer={trigger => trigger.parentNode}
                  onChange={value => {
                    if (value?.length > 1) {
                      this.setState({ blockGuid: value[1] });
                    } else {
                      this.setState({ blockGuid: '' });
                    }
                  }}
                />
              )}
            </Form.Item>
            {this.state.blockGuid && (
              <Form.Item label="楼层">
                {getFieldDecorator('floor', {
                  rules: [{ required: true, message: '请选择楼层' }],
                })(<FloorSelect blockGuidList={[this.state.blockGuid]} style={{ width: 200 }} />)}
              </Form.Item>
            )}
            <Form.Item label="包间编号">
              {getFieldDecorator('tag', {
                rules: [
                  { required: true, whitespace: true, message: '请填写包间编号' },
                  {
                    pattern: /^[\u4e00-\u9fa5A-Za-z0-9-_]+$/,
                    message: '仅支持数字、字母、横线或下划线',
                  },
                  {
                    max: 16,
                    message: '最多输入 16 个字符！',
                  },
                ],
              })(<Input style={{ width: 200 }} placeholder="例如 A1-01" />)}
            </Form.Item>
            <Form.Item label="包间名称">
              {getFieldDecorator('name', {
                rules: [
                  {
                    max: 32,
                    message: '最多输入 32 个字符！',
                  },
                ],
              })(<Input style={{ width: 200 }} />)}
            </Form.Item>
            <Form.Item label="包间类型">
              {getFieldDecorator('roomType', {
                rules: [{ required: true, message: '选择包间类别' }],
              })(
                <ApiSelect
                  style={{ width: 200 }}
                  showSearch
                  fieldNames={{ label: 'value', value: 'key' }}
                  dataService={async () => {
                    const response = await roomService.fetchRoomType();
                    if (response) {
                      return Promise.resolve(response);
                    } else {
                      return Promise.resolve([]);
                    }
                  }}
                  getPopupContainer={trigger => trigger.parentNode}
                />
              )}
            </Form.Item>
            {getFieldValue('roomType') === WAREHOUSE.code && (
              <Form.Item label="库房面积">
                {getFieldDecorator('area', {
                  rules: [{ required: true, message: '库房面积必填' }],
                })(
                  <InputNumber
                    style={{ width: 200 }}
                    min={0}
                    max={99999.99}
                    precision={2}
                    formatter={value => `${value}${WAREHOUSE.unit}`}
                    parser={value => value.replace(WAREHOUSE.unit, '')}
                  />
                )}
              </Form.Item>
            )}
            <Form.Item label="投产日期">
              {getFieldDecorator('operationTime', {
                rules: [{ required: true, message: '请选择投产日期' }],
              })(
                <DatePicker
                  style={{ width: 200 }}
                  format="YYYY-MM-DD"
                  getCalendarContainer={trigger => trigger.parentNode}
                  onChange={this.onChange}
                />
              )}
            </Form.Item>
            <Form.Item label="投产状态">
              {getFieldDecorator('operationStatus', {
                rules: [{ required: true, message: '请选择投产状态' }],
              })(
                <ApiSelect
                  style={{ width: 200 }}
                  fieldNames={{ label: 'value', value: 'key' }}
                  dataService={async () => {
                    const response = await roomService.fetchOperationStatus();
                    if (response) {
                      return Promise.resolve(response);
                    } else {
                      return Promise.resolve([]);
                    }
                  }}
                  getPopupContainer={trigger => trigger.parentNode}
                />
              )}
            </Form.Item>
            <Form.Item label="设计总功率">
              {getFieldDecorator('ratedPower', {
                rules: [
                  {
                    pattern: /^(\d{1,8}(\.\d{1,2})?|100)$/,
                    message: '限制整数位最大8位，两位小数',
                  },
                ],
              })(
                <InputNumber
                  style={{ width: 200 }}
                  min={0}
                  step={0.01}
                  precision={2}
                  formatter={value => `${value}kW`}
                  parser={value => {
                    if (value.length >= 2) {
                      return value.replace('k', '').replace('W', '');
                    } else {
                      return 0;
                    }
                  }}
                />
              )}
            </Form.Item>
            <Form.Item label="制冷架构">
              {getFieldDecorator('refrigerationStructure')(
                <Select
                  style={{ width: 200 }}
                  allowClear
                  getPopupContainer={trigger => trigger.parentNode}
                >
                  <Select.Option value="N+1">N+1</Select.Option>
                  <Select.Option value="N+2">N+2</Select.Option>
                </Select>
              )}
            </Form.Item>
            <Form.Item label="排序序号">
              {getFieldDecorator('sort')(
                <InputNumber
                  style={{ width: 200 }}
                  min={1}
                  precision={0}
                  parser={value => value && Math.floor(value)}
                />
              )}
            </Form.Item>
            {Array.isArray(specInfo) &&
              specInfo.map((item, index) => (
                <Form.Item key={item.id} label={item.specName}>
                  {getFieldDecorator(`specParams[${index}].specValue`, {
                    rules:
                      item.valueType === VALUE_TYPE_KEY_MAP.CHARACTER && item.inputWay === 'INPUT'
                        ? [
                            { required: item.required, message: `${item.specName}必填！` },
                            {
                              max: 200,
                              message: '最多输入 200 个字符！',
                            },
                          ]
                        : [{ required: item.required, message: `${item.specName}必填！` }],
                  })(this.getSpecComponent(item))}
                </Form.Item>
              ))}
          </Form>
        </TinyDrawer>
      </div>
    );
  }
}
const mapStateToProps = ({ roomManage: { createRoomVisible, createRoomLoading } }) => ({
  createRoomVisible,
  createRoomLoading,
});

const mapDispatchToProps = {
  closeVisible: roomActions.createRoomVisible,
  fetchCreateRoomConfirm,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(Form.create({ name: 'create_room' })(CreatePower));
