import Form from '@ant-design/compatible/es/form';
import { ApiSelect, Select } from '@galiojs/awesome-antd';
import moment from 'moment';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { Typography } from '@manyun/base-ui.ui.typography';
import { FloorSelect } from '@manyun/resource-hub.ui.floor-select';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';
import { getInitialValue } from '@manyun/resource-hub.ui.spec-form-items';
import { getComponent } from '@manyun/resource-hub.ui.spec-form-items';
import { VALUE_TYPE_KEY_MAP } from '@manyun/resource-hub.ui.spec-select';
import { getSpaceGuid } from '@manyun/resource-hub.util.space-guid';

import { TinyDrawer } from '@manyun/dc-brain.legacy.components';
import { fetchUpdateRoom, roomActions } from '@manyun/dc-brain.legacy.redux/actions/roomActions';
import * as roomService from '@manyun/dc-brain.legacy.services/roomService';

import { WAREHOUSE } from '../constants';

const { Option } = Select;
class CreatePower extends Component {
  state = {
    operationTime: '',
  };

  closeEditRoom = () => {
    this.props.closeVisible();
  };

  submitEditRoom = event => {
    event.preventDefault();
    this.props.form.validateFields((err, values) => {
      const errs = Object.keys(err);
      if (Array.isArray(errs) && errs[0]) {
        let errorElement = document.querySelector(`.${errs[0]}`);
        if (!errorElement) {
          const _key = err[errs[0]]
            .filter(i => Object.keys(i)[0])
            .map(i =>
              i[Object.keys(i)[0]]?.errors?.[0]?.field.replace(/\]./g, '-').replace(/\[/g, '-')
            );
          errorElement = document.querySelector(`.${_key[0]}`);
        }

        if (errorElement) {
          // 滚动到该字段的位置
          errorElement.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
          });
        }
      }
    });
    this.props.form.validateFieldsAndScroll(
      { scroll: { offsetBottom: TinyDrawer.FOOTER_HEIGHT } },
      async (err, values) => {
        if (err) {
          return;
        }

        const { editRoomMessList, roomPageCondition } = this.props;
        const { id, relateRoom } = editRoomMessList;
        const { operationTime } = this.state;
        const editMess = {
          ...values,
          operationTime: operationTime === '' ? editRoomMessList.operationTime : operationTime,
          id,
          roomType: values.roomType?.key,
          operationStatus: values.operationStatus?.key,
          area: values.roomType && values.roomType.key === WAREHOUSE.code ? values.area : null,
          relateRoom,
        };

        if (values.specParams) {
          editRoomMessList.specInfo.forEach((item, index) => {
            values.specParams[index].specId = item.id;
            values.specParams[index].specName = item.specName;
          });

          const arr = values.specParams.filter(item => !!item.specValue);
          const res = [];
          arr.forEach(item => {
            const roomItem = editRoomMessList.specInfo.find(val => val.id === item.specId);
            if (moment.isMoment(item.specValue)) {
              res.push({
                specId: item.specId,
                specName: item.specName,
                specValue: moment(item.specValue).format('HH:mm'),
              });
              return;
            }

            if (Array.isArray(item.specValue) && roomItem?.optionType) {
              item.specValue.map(valItem => {
                res.push({
                  specId: item.specId,
                  specName: item.specName,
                  specValue: Array.isArray(valItem) ? valItem[valItem.length - 1] : valItem,
                });
              });

              return;
            }

            res.push({
              specId: item.specId,
              specName: item.specName,
              specValue: Array.isArray(item.specValue)
                ? item.specValue[item.specValue.length - 1]
                : item.specValue,
            });
          });

          editMess.specParams = res;
        } else {
          editMess.specParams = [];
        }

        this.props.fetchUpdateRoom({
          editMess: editMess,
          roomPageCondition: roomPageCondition,
        });
      }
    );
  };

  onChange = (value, dateString) => {
    this.setState({
      operationTime: dateString,
    });
  };

  getOptions = options => {
    let newOptions = [];
    const optionsArr = options.split(',');
    newOptions = optionsArr.map(option => ({ label: option, value: option }));
    return newOptions;
  };

  getLocationArr = value => {
    if (!value) {
      return;
    }
    const arr = value.split('.');
    const data = [arr[0]];
    for (let i = 1; i < arr.length; i++) {
      data[i] = data[i - 1] + '.' + arr[i];
    }
    return data;
  };

  render() {
    const form = this.props.form;
    const { getFieldDecorator, getFieldValue } = form;
    const { editRoomVisible, editRoomMessList, createRoomLoading } = this.props;

    return (
      <div>
        <TinyDrawer
          width={416}
          title={<Typography.Title level={4}>编辑包间</Typography.Title>}
          destroyOnClose
          visible={editRoomVisible}
          submitButtonLoading={createRoomLoading}
          onClose={this.closeEditRoom}
          onCancel={this.closeEditRoom}
          onSubmit={this.submitEditRoom}
        >
          <Form layout="vertical" onSubmit={this.submitEditRoom}>
            <Form.Item className="idcTag" label="所属机房">
              {getFieldDecorator('idcTag', {
                rules: [
                  {
                    required: true,
                    message: '请选择机房!',
                  },
                ],
                initialValue: editRoomMessList.idcTag,
              })(<Input style={{ width: 200 }} disabled />)}
            </Form.Item>
            <Form.Item className="blockTag" label="所属楼栋">
              {getFieldDecorator('blockTag', {
                rules: [{ required: true, message: '选择所属楼栋' }],
                initialValue: editRoomMessList.blockTag,
              })(<Input style={{ width: 200 }} disabled />)}
            </Form.Item>
            {editRoomMessList.idcTag && editRoomMessList.blockTag && (
              <Form.Item className="floor" label="所属楼层">
                {getFieldDecorator('floor', {
                  initialValue: editRoomMessList.floor,
                })(
                  <FloorSelect
                    style={{ width: 200 }}
                    disabled
                    blockGuidList={[
                      getSpaceGuid(editRoomMessList.idcTag, editRoomMessList.blockTag),
                    ]}
                  />
                )}
              </Form.Item>
            )}
            <Form.Item className="tag" label="包间编号">
              {getFieldDecorator('tag', {
                initialValue: editRoomMessList.tag,
              })(<Input style={{ width: 200 }} disabled />)}
            </Form.Item>
            <Form.Item className="name" label="包间名称">
              {getFieldDecorator('name', {
                initialValue: editRoomMessList.name,
                rules: [
                  {
                    max: 32,
                    message: '最多输入 32 个字符！',
                  },
                ],
              })(<Input style={{ width: 200 }} />)}
            </Form.Item>
            <Form.Item className="roomType" label="包间类型">
              {getFieldDecorator('roomType', {
                rules: [{ required: true, message: '选择包间类别' }],
                initialValue: {
                  key: editRoomMessList.roomType,
                  label: editRoomMessList.roomTypeName ? editRoomMessList.roomTypeName : '',
                },
              })(
                <ApiSelect
                  style={{ width: 200 }}
                  labelInValue
                  showSearch
                  fieldNames={{ label: 'value', value: 'key' }}
                  dataService={async () => {
                    const response = await roomService.fetchRoomType();
                    if (response) {
                      return Promise.resolve(response);
                    } else {
                      return Promise.resolve([]);
                    }
                  }}
                  getPopupContainer={trigger => trigger.parentNode}
                />
              )}
            </Form.Item>

            {getFieldValue('roomType').key === WAREHOUSE.code && (
              <Form.Item className="area" label="库房面积">
                {getFieldDecorator('area', {
                  rules: [{ required: true, message: '库房面积必填' }],
                  initialValue: editRoomMessList.area,
                })(
                  <InputNumber
                    style={{ width: 200 }}
                    min={0}
                    max={99999.99}
                    precision={2}
                    formatter={value => `${value}${WAREHOUSE.unit}`}
                    parser={value => value.replace(WAREHOUSE.unit, '')}
                  />
                )}
              </Form.Item>
            )}

            <Form.Item className="operationTime" label="投产日期">
              {getFieldDecorator('operationTime', {
                rules: [{ required: true, message: '请选择投产日期' }],
                initialValue: moment(editRoomMessList.operationTime),
              })(
                <DatePicker
                  style={{ width: 200 }}
                  format="YYYY-MM-DD"
                  getCalendarContainer={trigger => trigger.parentNode}
                  onChange={this.onChange}
                />
              )}
            </Form.Item>
            <Form.Item className="operationStatus" label="状态">
              {getFieldDecorator('operationStatus', {
                rules: [{ required: true, message: '请选择投状态' }],
                initialValue: {
                  key: editRoomMessList.operationStatus?.code,
                  label: editRoomMessList.operationStatus?.name,
                },
              })(
                <ApiSelect
                  style={{ width: 200 }}
                  labelInValue
                  fieldNames={{ label: 'value', value: 'key' }}
                  dataService={async () => {
                    const response = await roomService.fetchOperationStatus();
                    if (response) {
                      return Promise.resolve(response);
                    } else {
                      return Promise.resolve([]);
                    }
                  }}
                  getPopupContainer={trigger => trigger.parentNode}
                />
              )}
            </Form.Item>

            <Form.Item className="ratedPower" label="设计总功率">
              {getFieldDecorator('ratedPower', {
                initialValue: editRoomMessList.ratedPower,
                rules: [
                  {
                    pattern: /^(\d{1,8}(\.\d{1,2})?|100)$/,
                    message: '限制整数位最大8位，两位小数',
                  },
                ],
              })(
                <InputNumber
                  style={{ width: 200 }}
                  min={0}
                  step={0.01}
                  precision={2}
                  formatter={value => `${value}kW`}
                  parser={value => {
                    if (value.length >= 2) {
                      return value.replace('k', '').replace('W', '');
                    } else {
                      return 0;
                    }
                  }}
                />
              )}
            </Form.Item>
            <Form.Item className="refrigerationStructure" label="制冷架构">
              {getFieldDecorator('refrigerationStructure', {
                initialValue: editRoomMessList.refrigerationStructure,
              })(
                <Select
                  style={{ width: 200 }}
                  allowClear
                  getPopupContainer={trigger => trigger.parentNode}
                >
                  <Option value="N+1">N+1</Option>
                  <Option value="N+2">N+2</Option>
                </Select>
              )}
            </Form.Item>
            <Form.Item label="排序序号">
              {getFieldDecorator('sort', {
                initialValue: editRoomMessList.sort,
              })(
                <InputNumber
                  style={{ width: 200 }}
                  min={1}
                  precision={0}
                  parser={value => value && Math.floor(value)}
                />
              )}
            </Form.Item>
            {Array.isArray(editRoomMessList.specInfo) &&
              editRoomMessList.specInfo.map((item, index) => (
                <Form.Item
                  key={item.id}
                  className={`specParams-${index}-specValue`}
                  label={item.specName}
                >
                  {getFieldDecorator(`specParams[${index}].specValue`, {
                    rules:
                      item.valueType === VALUE_TYPE_KEY_MAP.CHARACTER && item.inputWay === 'INPUT'
                        ? [
                            { required: item.required, message: `${item.specName}必填！` },
                            {
                              max: 200,
                              message: '最多输入 200 个字符！',
                            },
                          ]
                        : [{ required: item.required, message: `${item.specName}必填！` }],
                    initialValue: getInitialValue(item),
                  })(getComponent(item))}
                </Form.Item>
              ))}

            <Form.Item className="operatorNotes" label="操作备注">
              {getFieldDecorator('operatorNotes', {
                rules: [
                  { required: true, message: '操作备注必填！' },
                  {
                    pattern: /^[^\s]{1}/,
                    message: '开头不允许为空格',
                  },
                  {
                    max: 128,
                    message: '最多输入 128 个字符！',
                  },
                ],
              })(
                <Input.TextArea
                  autoSize={{ minRows: 3 }}
                  placeholder={`请输入修改 ${editRoomMessList.idcTag}.${editRoomMessList.blockTag}.${editRoomMessList.tag} 信息的原因！`}
                />
              )}
            </Form.Item>
          </Form>
        </TinyDrawer>
      </div>
    );
  }
}
const mapStateToProps = ({
  roomManage: { editRoomVisible, editRoomMessList, createRoomLoading, roomPageCondition },
}) => ({
  editRoomVisible,
  editRoomMessList,
  createRoomLoading,
  roomPageCondition,
});

const mapDispatchToProps = {
  closeVisible: roomActions.editRoomVisible,
  fetchUpdateRoom,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(Form.create({ name: 'create_room' })(CreatePower));
