import { ApiSelect, FiltersForm, Form } from '@galiojs/awesome-antd';
import split from 'lodash/split';
import trim from 'lodash/trim';
import moment from 'moment';
import React, { Component } from 'react';
import { connect, useDispatch } from 'react-redux';
import { Link } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Divider } from '@manyun/base-ui.ui.divider';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { VendorSelect } from '@manyun/crm.ui.vendor-select';
import { EditColumns } from '@manyun/dc-brain.ui.edit-columns';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { generateDetailCabinetUrl } from '@manyun/resource-hub.route.resource-routes';
import { fetchSpecs } from '@manyun/resource-hub.service.fetch-specs';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';
import { MetaTypeText } from '@manyun/resource-hub.ui.meta-type-text';
import { RackTypesSelect } from '@manyun/resource-hub.ui.rack-types-select';
import { SpecSearchSelect } from '@manyun/resource-hub.ui.spec-search-select';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

import { GutterWrapper, StatusText, TinyCard } from '@manyun/dc-brain.legacy.components';
import { STATUS_MAP } from '@manyun/dc-brain.legacy.constants/statusColor';
import {
  batchUpdateActionCreator,
  cabinetActions,
  fetchCabinetListPage,
} from '@manyun/dc-brain.legacy.redux/actions/cabinetActions';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import { infrastructureService } from '@manyun/dc-brain.legacy.services';
import * as cabinetService from '@manyun/dc-brain.legacy.services/cabinetService';
import { getLocationSearchMap } from '@manyun/dc-brain.legacy.utils/index';
import { generateEditCabinetUrl } from '@manyun/dc-brain.legacy.utils/urls';

import EditCabinet from '../components/edit-cabinet';
import ViewCabinetDetail from '../components/view-cabinet-detail';
import { isValidJSON } from '../details/archives';
import BatchUpdateModalButton from './components/batch-update-modal-button';
import { SelfBatchUpdateModalButton } from './components/self-update-modal-button';

const { RangePicker } = DatePicker;

class CabinetManage extends Component {
  state = {
    expand: false,
    idcTag: '',
    selectedRowKeys: [],
    selectedRows: [],
    selectLoading: false,
    specColumns: [],
    tableColumns: [],
  };

  componentDidMount() {
    const { syncCommonData, fetchCabinetListPage } = this.props;
    syncCommonData({ strategy: { space: 'FORCED' } });
    const { guid, insertVersion } = this._getUrlSearchParams();
    const p = this.getParams();
    const params = {
      pageNum: 1,
      pageSize: 10,
      ...p,
    };
    if (guid) {
      const areaIdcBlockRoom = split(guid, '.', 3);
      params.idcTag = areaIdcBlockRoom[0];
      params.blockTag = areaIdcBlockRoom[1];
      params.roomTag = areaIdcBlockRoom[2];
      params.tag = split(guid, '.')[3];
      this.props.updateSearchValues({
        areaIdcBlockRoom: { name: 'areaIdcBlockRoom', value: areaIdcBlockRoom },
        tag: { name: 'tag', value: params.tag },
      });
    }
    params.insertVersion = insertVersion;
    fetchCabinetListPage(params);
    this.getSpecs();
  }

  _getUrlSearchParams = () => {
    const { search } = this.props.history.location;
    return getLocationSearchMap(search, ['insertVersion', 'guid']);
  };

  // 编辑
  editCabinet = async row => {
    const params = { deviceType: this.props.gridDeviceType, modelId: row.guid };

    const { data, error } = await fetchSpecs(params);
    if (error) {
      message.error(error.message);
      return;
    }
    // 编辑弹框状态
    this.props.editCabinetVisible();
    // 编辑信息
    this.props.editCabinetMess({ ...row, specInfo: data.data });
  };

  // 新建机柜
  createCabinet = () => {
    this.props.createCabinetVisible();
  };

  // 查看
  showCabinetDetail = async row => {
    const params = { deviceType: this.props.gridDeviceType, modelId: row.guid };

    const { data, error } = await fetchSpecs(params);
    if (error) {
      message.error(error.message);
      return;
    }
    this.props.viewCabinetVisible();
    this.props.viewCabinetMess({ ...row, specInfo: data.data.filter(item => !!item.specValue) });
  };

  onSelectChange = (selectedRowKeys, selectedRows) => {
    this.setState({ selectedRowKeys, selectedRows });
  };

  onChangePage = (pageNo, pageSize) => {
    const { cabinetPageCondition } = this.props;
    this.setState({ selectedRowKeys: [], selectedRows: [] });
    this.props.fetchCabinetListPage({
      ...cabinetPageCondition,
      pageNum: pageNo,
      pageSize,
    });
  };

  searchCabinetList = () => {
    const params = this.getParams();
    this.props.fetchCabinetListPage({
      ...params,
      pageNum: 1,
      pageSize: 10,
    });
  };

  getParams = extraExportColumns => {
    const fieldsValue = this.props.searchValues;
    const params = Object.keys(fieldsValue).reduce((map, fieldName) => {
      const value = fieldsValue[fieldName]?.value;
      if ([null, undefined, ''].includes(value) || (Array.isArray(value) && value.length === 0)) {
        return map;
      } else if (fieldName === 'areaIdcBlockRoom') {
        map.idcTag = getSpaceGuidMap(value[0]).idc;
        map.blockTag = getSpaceGuidMap(value[1]).block;
        map.roomTag = getSpaceGuidMap(value[2]).room;
      } else if (fieldName === 'operationTime') {
        map.operationTimeEnd = value[1]?.endOf('day').valueOf();
        map.operationTimeStart = value[0]?.startOf('day').valueOf();
      } else if (fieldName === 'specInfo') {
        // 属性值额外处理，包含时间选择，普通输入框，楼栋选择等
        if (moment.isMoment(value)) {
          map.specValueList = [moment(value).format('HH:mm')];
        } else {
          map.specValueList = Array.isArray(value)
            ? value.some(item => item instanceof Array)
              ? value.flat() // 兼容二维数组
              : value
            : [value];
        }
      } else {
        map[fieldName] = trim(value);
      }
      return map;
    }, {});
    const list = this.state.tableColumns
      .filter(item => item.show)
      .map(item => {
        if (item.dataIndex) {
          if (Array.isArray(item.dataIndex)) {
            return {
              [item.dataIndex[0]]: item.title,
            };
          }
          return {
            [item.dataIndex]: item.title,
          };
        }
        return undefined;
      })
      .filter(Boolean);
    const excelHeadList = extraExportColumns ? list.concat(extraExportColumns) : list;
    return {
      ...params,
      excelHeadList,
    };
  };

  handleReset = () => {
    this.props.dispatch(this.props.resetSearchValues());
    this.props.fetchCabinetListPage({
      pageNum: 1,
      pageSize: 10,
    });
  };

  _getDeleteHandler =
    gridGuid =>
    async ({ reason }) => {
      if (!gridGuid) {
        console.warn(`grid guid expected!`);
        return;
      }

      const { error } = await infrastructureService.deleteGrid({
        guid: gridGuid,
        operatorNotes: reason,
      });

      if (error) {
        message.error(error);
        return false;
      }
      message.success(`${gridGuid} 已成功删除！`);

      const {
        cabinetPageCondition: { pageNum, pageSize },
      } = this.props;
      const searchValues = {
        ...this.getParams(),
        pageNum,
        pageSize,
      };
      this.props.fetchCabinetListPage(searchValues);

      return true;
    };

  _batchUpdateModalOkHandler = params => {
    const {
      batchUpdate,
      cabinetPageCondition: { pageNum, pageSize },
    } = this.props;
    const { selectedRowKeys } = this.state;
    const searchValues = { ...this.getParams(), pageNum, pageSize };
    const data = { ...params, ids: selectedRowKeys };
    if (params.operationTime) {
      data.operationTime = params.operationTime.format('YYYY-MM-DD');
    }
    return new Promise(resolve => {
      const successCallback = () => resolve(true);
      const errorCallback = () => resolve(false);
      batchUpdate({ data, searchValues, successCallback, errorCallback });
    });
  };

  getSpecs = async () => {
    const params = { deviceType: this.props.gridDeviceType };

    const { data, error } = await fetchSpecs(params);
    if (error) {
      message.error(error.message);
      return;
    }

    const specs = data.data.map(item => ({
      code: item.specCode,
      label: item.specName,
    }));
    this.setState({
      specColumns: specs.map(item => {
        return {
          title: item.label,
          width: 120,
          dataIndex: item.code,
          show: false,
          render: (value, record) => {
            const _str = record.specDataList?.find(i => i.specCode === item.code)?.options;
            const _v = _str && isValidJSON(_str);
            if (_v?.componentProps && Object.keys(_v.componentProps)?.length > 1 && value) {
              return <MetaTypeText metaType={_v.componentProps} code={value} />;
            }
            return <div>{value}</div>;
          },
        };
      }),
    });
  };

  getColumns = ctx => [
    {
      title: '机柜编号',
      dataIndex: 'tag',
      exportDataIndexes: [
        { title: '机柜编号', dataIndex: 'tag' },
        { title: '机房名称', dataIndex: 'idcName' },
        { title: '楼栋编号', dataIndex: 'blockTag' },
      ],
      show: true,
      render: (text, record) => (
        <Link
          to={() => {
            return generateDetailCabinetUrl({
              id: text,
              state: { ...record, deviceType: ctx.props.gridDeviceType },
            });
          }}
        >
          {text}
        </Link>
      ),
    },
    {
      title: '机房编号',
      dataIndex: 'idcTag',
      show: true,
    },
    {
      title: '楼栋编号',
      dataIndex: 'blockTag',
      show: true,
    },
    {
      title: '包间编号',
      dataIndex: 'roomTag',
      show: true,
    },

    {
      title: '机柜类型',
      dataIndex: ['gridType', 'name'],
      show: true,
    },
    {
      title: '厂商',
      dataIndex: 'vendor',
      show: true,
    },
    {
      title: 'U数',
      dataIndex: 'unitCount',
      show: true,
      render: unitCount => unitCount + 'U',
    },

    {
      title: '设计功率',
      dataIndex: 'ratedPower',
      show: true,
      render: ratedPower => ratedPower + 'kW',
    },
    {
      title: '签约功率',
      dataIndex: 'signedPower',
      show: true,
      render: signedPower => (signedPower || signedPower === 0 ? `${signedPower}kW` : null),
    },
    {
      title: '额定承重',
      dataIndex: 'ratedLoad',
      show: true,
      render: ratedLoad => ratedLoad + 'kg',
    },
    {
      title: '上电状态',
      dataIndex: ['powerStatus', 'name'],
      show: true,
      render: (__, record) => {
        if (record.powerStatus?.code === 'OFF') {
          return (
            <StatusText status={STATUS_MAP.DISABLED} background>
              {record.powerStatus.name}
            </StatusText>
          );
        }
        if (record.powerStatus?.code === 'ON') {
          return (
            <StatusText status={STATUS_MAP.NORMAL} background>
              {record.powerStatus.name}
            </StatusText>
          );
        }
        return (
          <StatusText status={STATUS_MAP.WARNING} background>
            {record.powerStatus.name}
          </StatusText>
        );
      },
    },
    {
      title: '机柜尺寸',
      dataIndex: 'gridSize',
      show: true,
      exportDataIndexes: [
        { title: '长度', dataIndex: 'gridLength' },
        { title: '宽度', dataIndex: 'gridWidth' },
        { title: '高度', dataIndex: 'gridHeight' },
        { title: '建设日期', dataIndex: 'constructTime' },
      ],
      render: (__, record) => (
        <span>
          {record.gridLength}mm*{record.gridWidth}mm*{record.gridHeight}mm
        </span>
      ),
    },
    {
      title: '投产日期',
      dataIndex: 'operationTime',
      show: true,
    },
    {
      title: '签约客户',
      dataIndex: 'customer',
      show: true,
    },
    {
      title: '启用状态',
      dataIndex: ['operationStatus', 'name'],
      show: false,
    },
    {
      title: '是否计费',
      dataIndex: 'charge',
      render: charge => {
        if (typeof charge !== 'boolean') {
          return '--';
        }
        if (charge) {
          return '是';
        } else {
          return '否';
        }
      },
      show: true,
    },
    {
      title: '供电模式',
      dataIndex: 'powerModel',
      show: true,
    },
    {
      title: '供应商',
      dataIndex: 'supplyVendor',
      show: true,
    },
    {
      title: '操作',
      key: '__actions',
      exportable: false,
      fixed: 'right',
      width: 126,
      show: true,
      render: (__, record) => (
        <Space align="center" size={0}>
          <Button compact type="link" onClick={() => ctx.editCabinet(record)}>
            编辑
          </Button>
          <Divider type="vertical" />
          <DeleteConfirm targetName={record.guid} onOk={ctx._getDeleteHandler(record.guid)}>
            <Button compact type="link">
              删除
            </Button>
          </DeleteConfirm>
        </Space>
      ),
    },
  ];

  render() {
    const { cabinetPage, loading, cabinetPageCondition, updateSearchValues, searchValues, form } =
      this.props;
    const { selectedRowKeys, selectedRows, selectLoading, specColumns, tableColumns } = this.state;

    return (
      <GutterWrapper mode="vertical">
        <TinyCard>
          <FiltersForm
            form={form}
            items={[
              {
                label: '位置',
                name: 'areaIdcBlockRoom',
                control: (
                  <LocationCascader
                    showSearch
                    authorizedOnly
                    nodeTypes={['IDC', 'BLOCK', 'ROOM']}
                  />
                ),
              },
              {
                label: '设计功率',
                name: 'ratedPower',
                control: <InputNumber min={0} max={999999999} precision={2} />,
              },
              {
                label: '签约功率',
                name: 'signedPower',
                control: <InputNumber min={0.01} max={999999999} precision={2} />,
              },
              {
                label: '额定承重',
                name: 'ratedLoad',
                control: <InputNumber min={1} max={999999999} precision={0} />,
              },
              {
                label: '签约客户',
                name: 'customer',
                control: <Input allowClear />,
              },
              {
                label: '上电状态',
                name: 'powerStatus',
                control: (
                  <ApiSelect
                    allowClear
                    trigger="onDidMount"
                    fieldNames={{ label: 'value', value: 'key' }}
                    loading={selectLoading}
                    dataService={async () => {
                      this.setState({ selectLoading: true });
                      const response = await cabinetService.fetchPowerStatusListe();
                      if (response) {
                        this.setState({ selectLoading: false });
                        return Promise.resolve(response);
                      } else {
                        return Promise.resolve([]);
                      }
                    }}
                  />
                ),
              },
              {
                label: '机柜类型',
                name: 'gridType',
                control: <RackTypesSelect allowClear trigger="onDidMount" />,
              },
              {
                label: '机柜编号',
                name: 'tag',
                control: <Input allowClear />,
              },
              {
                label: '投产日期',
                name: 'operationTime',
                control: <RangePicker format="YYYY-MM-DD" />,
              },
              {
                label: '厂商',
                name: 'vendorCode',
                control: <VendorSelect allowClear />,
              },
              {
                span: 2,
                label: '属性',
                name: 'spec',
                control: <SpecSearchSelect searchType="grid" form={form} />,
              },
            ]}
            fields={Object.keys(searchValues).map(name => {
              const field = searchValues[name];
              return {
                ...field,
                name: name.split('.'),
              };
            })}
            onFieldsChange={changedFields => {
              const fields = changedFields.reduce((mapper, field) => {
                const name = field.name.join('.');
                mapper[name] = {
                  ...field,
                  name,
                };
                return mapper;
              }, {});
              if (fields?.specId) {
                updateSearchValues({
                  ...fields,
                  specInfo: undefined,
                });
              } else {
                updateSearchValues(fields);
              }
            }}
            onSearch={this.searchCabinetList}
            onReset={this.handleReset}
          />
        </TinyCard>
        <TinyCard>
          <Space style={{ width: '100%', justifyContent: 'space-between', marginBottom: 16 }}>
            <Space size={16}>
              <Link key="new" to={generateEditCabinetUrl}>
                <Button type="primary">新建</Button>
              </Link>
              <BatchUpdateModalButton
                key="batch-update-modal-btn"
                selectedRowKeys={selectedRowKeys}
                onOk={this._batchUpdateModalOkHandler}
              />
              <SelfBatchUpdateModalButton
                gridDeviceType={this.props.gridDeviceType}
                columns={tableColumns}
                onOk={() => {
                  const {
                    cabinetPageCondition: { pageNum, pageSize },
                  } = this.props;
                  const searchValues = {
                    ...this.getParams(),
                    pageNum,
                    pageSize,
                  };
                  this.props.fetchCabinetListPage(searchValues);
                }}
              />
            </Space>
            <Space size={16}>
              <FileExport
                filename="机柜数据.csv"
                data={async type => {
                  const params = {
                    // 后端处理，每次都要导出该字段
                    ...this.getParams([
                      {
                        realPower: '实时功率', // 后端处理，每次都要导出该字段
                      },
                      {
                        rules: '二维码规则',
                      },
                    ]),
                  };
                  if (type === 'selected' && selectedRowKeys.length > 0) {
                    params.ids = selectedRowKeys;
                  }
                  const index = params.excelHeadList?.findIndex(
                    item => Object.keys(item)[0] === 'powerStatus'
                  );
                  // 插入在目标的“后面”
                  if (index !== -1) {
                    params.excelHeadList.splice(index + 1, 0, {
                      powerTime: '最近上下电时间',
                    });
                  }
                  const { response, error } = await cabinetService.downloadCabinetList(
                    type === 'all'
                      ? {
                          excelHeadList: params.excelHeadList,
                        }
                      : params
                  );
                  if (error) {
                    message.error(error.message);
                    return error.message;
                  }
                  return response;
                }}
                showExportFiltered
                showExportSelected={!!selectedRowKeys.length}
              />
              {specColumns.length && (
                <EditColumns
                  uniqKey="RESOURCE_HUB_PAGE_CABINET_LIST"
                  allowSetAsFixed={false}
                  defaultValue={[...this.getColumns(this), ...specColumns]}
                  listsHeight={300}
                  onChange={value => {
                    this.setState({
                      tableColumns: value,
                    });
                  }}
                />
              )}
            </Space>
          </Space>
          <Table
            rowKey={record => record.id}
            scroll={{ x: 'max-content' }}
            dataSource={cabinetPage.list.map(item => ({
              ...item,
              ...Object.fromEntries(
                item?.specDataList?.map(item => ({
                  ...[item.specCode, (item.specValue ?? '--') + (item.specUnit ?? '')],
                })) ?? []
              ),
            }))}
            loading={loading}
            columns={tableColumns}
            rowSelection={{
              selectedRowKeys,
              selectedRows,
              onChange: this.onSelectChange,
            }}
            pagination={{
              total: cabinetPage.total,
              current: cabinetPageCondition.pageNum,
              pageSize: cabinetPageCondition.pageSize,
              showSizeChanger: true,
              onChange: this.onChangePage,
            }}
          />
        </TinyCard>
        {/* 新建弹窗 */}
        {/* <CreateCabinet createCascaderData={createCascaderData} /> */}
        {/* 查看弹框 */}
        <ViewCabinetDetail />
        {/* 编辑弹窗 */}
        <EditCabinet />
      </GutterWrapper>
    );
  }
}
const mapStateToProps = ({
  cabinetManage: { loading, cabinetPage, cabinetPageCondition, searchValues },
  config: { configMap },
}) => {
  const configUtil = new ConfigUtil(configMap.current);
  const gridDeviceType = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_GRID);
  return {
    gridDeviceType,
    loading,
    cabinetPage,
    cabinetPageCondition,
    searchValues,
  };
};
const mapDispatchToProps = {
  cabinetActions,
  createCabinetVisible: cabinetActions.createCabinetVisible,
  fetchCabinetListPage,
  viewCabinetVisible: cabinetActions.viewCabinetVisible,
  viewCabinetMess: cabinetActions.saveCabinetDetailMess,
  editCabinetVisible: cabinetActions.editCabinetVisible,
  editCabinetMess: cabinetActions.editCabinetMess,
  saveAreaIdcBlockRoomCascader: cabinetActions.saveAreaIdcBlockRoomCascader,
  syncCommonData: syncCommonDataActionCreator,
  updateSearchValues: cabinetActions.updateSearchValues,
  resetSearchValues: cabinetActions.resetSearchValues,
  batchUpdate: batchUpdateActionCreator,
};

function CabinetManageList(props) {
  const [form] = Form.useForm();
  const dispatch = useDispatch();

  return <CabinetManage form={form} dispatch={dispatch} {...props} />;
}

export default connect(mapStateToProps, mapDispatchToProps)(CabinetManageList);
