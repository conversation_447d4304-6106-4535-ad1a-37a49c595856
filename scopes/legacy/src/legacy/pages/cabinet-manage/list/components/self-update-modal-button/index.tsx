import { DownloadOutlined } from '@ant-design/icons';
import { saveAs } from 'file-saver';
import React, { useCallback, useState } from 'react';
import { Link } from 'react-router-dom';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { saveJSONAsXLSX } from '@manyun/base-ui.util.xlsx';
import { Upload } from '@manyun/dc-brain.ui.upload';
import { generateDetailCabinetUrl } from '@manyun/resource-hub.route.resource-routes';
import { confirmImportGrids } from '@manyun/resource-hub.service.confirm-import-grids';
import { fetchSpecs } from '@manyun/resource-hub.service.fetch-specs';
import { importExistsGrids } from '@manyun/resource-hub.service.import-exists-grids';
import type { ExistsGridsImport } from '@manyun/resource-hub.service.import-exists-grids';

import { downloadGridModel } from '@manyun/dc-brain.legacy.services/cabinetService';

const defaultPg = { page: 1, pageSize: 10 };

/**
 * @param {object} props
 * @param {[]} props.selectedRowKeys
 */
export function SelfBatchUpdateModalButton({
  gridDeviceType,
  onOk,
  columns,
}: {
  gridDeviceType: string;
  onOk: () => void;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  columns: any[];
}) {
  const title = '手动更新';
  const [uploadLoading, setUploadLoading] = useState(false);
  const [downloadLoading, setDownloadLoading] = useState(false);

  const downloadDemo = useCallback(async () => {
    setDownloadLoading(true);
    const { response } = await downloadGridModel();
    setDownloadLoading(false);
    if (!response) {
      return;
    }
    saveAs(response, '手动批量更新机柜信息.csv');
  }, []);

  const [confirmVisible, setConfirmVisible] = useState(false);
  const [selfModalVisible, setSelfModalVisible] = useState(false);
  const [importResult, setImportResult] = useState<ExistsGridsImport>({
    excelCheckErrDtos: [],
    correctDtoList: [],
    checkTotal: 0,
    correctTotal: 0,
    faultTotal: 0,
  });
  const [form] = Form.useForm();

  const onResetSelfUpload = () => {
    setImportResult({
      excelCheckErrDtos: [],
      correctDtoList: [],
      checkTotal: 0,
      correctTotal: 0,
      faultTotal: 0,
    });
  };

  const [selfBatchGridColumns, setSelfBatchGridColumns] = useState(getColumns(gridDeviceType));

  const getSpecs = useCallback(async () => {
    if (selfModalVisible) {
      const params = { deviceType: gridDeviceType };

      const { data, error } = await fetchSpecs(params);
      if (error) {
        message.error(error.message);
        return;
      }

      const specs = data.data.map(item => ({
        code: item.id,
        label: item.specName,
      }));
      setSelfBatchGridColumns(pre => {
        const dd = pre.concat(
          specs.map(item => {
            return {
              title: item.label,
              width: 120,
              dataIndex: item.code as unknown as string,
              show: true,
              render: (value: string | React.ReactNode) => {
                return <div>{value}</div>;
              },
            };
          })
        );
        return dd;
      });
    }
  }, [gridDeviceType, selfModalVisible]);

  React.useEffect(() => {
    getSpecs();
  }, [getSpecs]);

  const [pagination, setPagination] = useState(defaultPg);
  const paginationConfig = {
    current: pagination.page,
    pageSize: pagination.pageSize,
    onChange: (page: number, pageSize: number) => {
      setPagination({
        ...pagination,
        page,
        pageSize,
      });
    },
  };
  return (
    <>
      <Button
        onClick={() => {
          setSelfModalVisible(true);
        }}
      >
        {title}
      </Button>
      {selfModalVisible && (
        <Modal
          maskStyle={{ zIndex: 99 }}
          style={{ zIndex: 99 }}
          destroyOnClose
          width={900}
          open={selfModalVisible}
          bodyStyle={{ maxHeight: `calc(80vh - 109px)`, overflowY: 'auto' }}
          title={title}
          footer={
            <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Space>
                <Button
                  onClick={() => {
                    setSelfModalVisible(false);
                    onResetSelfUpload();
                  }}
                >
                  取消
                </Button>
                <Button
                  type="primary"
                  disabled={importResult?.correctDtoList?.length === 0}
                  onClick={() => {
                    setConfirmVisible(true);
                  }}
                >
                  提交
                </Button>
              </Space>
            </div>
          }
          onCancel={() => {
            setSelfModalVisible(false);
            onResetSelfUpload();
          }}
        >
          <Space style={{ width: '100%' }} direction="vertical" size={16}>
            <Alert
              message="模板字段里不填写表示保持不变，若需要多个值请用空格分隔"
              type="warning"
              showIcon
            />
            <Space>
              <Upload
                showUploadList={false}
                accept=".csv,.xls,.xlsx"
                maxCount={1}
                customRequest={async ({ file }) => {
                  const fd = new FormData();
                  fd.append('file', file);
                  setUploadLoading(true);
                  const { data, error } = await importExistsGrids(fd);
                  setUploadLoading(false);
                  if (error) {
                    message.error(error.message);
                  } else {
                    // @ts-ignore ts()
                    setImportResult(data);
                  }
                }}
              >
                <Button loading={uploadLoading}>模版导入</Button>
              </Upload>
              <Button
                loading={downloadLoading}
                type="link"
                icon={<DownloadOutlined />}
                onClick={() => downloadDemo()}
              >
                模版下载
              </Button>
            </Space>
            {typeof importResult?.checkTotal === 'number' && importResult.checkTotal > 0 && (
              <Alert
                type="info"
                message={
                  <Space style={{ justifyContent: 'space-between', width: '100%' }}>
                    <Space>
                      <Typography.Text>导入：{importResult.checkTotal}条</Typography.Text>
                      <Typography.Text>成功：{importResult.correctTotal}条</Typography.Text>
                      <Typography.Text>失败：{importResult.faultTotal}条</Typography.Text>
                    </Space>
                    {typeof importResult.faultTotal === 'number' && importResult.faultTotal > 0 && (
                      <Button
                        type="link"
                        compact
                        onClick={() => {
                          saveJSONAsXLSX(
                            [
                              {
                                headers: columns
                                  .filter(item => item.dataIndex !== 'operation')
                                  .map(column => ({
                                    title: column.title,
                                    dataIndex: column.dataIndex,
                                  }))
                                  .concat([
                                    {
                                      title: '失败原因',
                                      dataIndex: 'totalErrMessage',
                                    },
                                  ]),
                                data: Array.isArray(importResult?.excelCheckErrDtos)
                                  ? importResult?.excelCheckErrDtos.map(item => ({
                                      ...item.errDto,
                                      totalErrMessage: item.totalErrMessage,
                                    }))
                                  : [],
                              },
                            ],
                            `机柜手动导入失败`
                          );
                        }}
                      >
                        失败文件下载
                      </Button>
                    )}
                  </Space>
                }
              />
            )}
            <Table
              pagination={{
                total: importResult?.correctDtoList?.length ?? 0,
                ...paginationConfig,
              }}
              dataSource={
                importResult.correctDtoList
                  ? importResult.correctDtoList.map(item => ({
                      ...item,
                      ...item.propertiesMap,
                    }))
                  : []
              }
              columns={selfBatchGridColumns}
              rowKey="tag"
              tableLayout="fixed"
              scroll={{ x: 'max-content' }}
            />
          </Space>
        </Modal>
      )}

      {confirmVisible && (
        <Modal
          maskStyle={{ zIndex: 100 }}
          style={{ zIndex: 100 }}
          title="备注"
          width={500}
          open={confirmVisible}
          onOk={async () => {
            form.validateFields().then(async val => {
              const { data, error } = await confirmImportGrids({
                operatorNotes: val.operatorNotes,
                gridInfoUpdateModelList: importResult.correctDtoList!,
              });
              if (error) {
                message.error(error.message);
                return;
              }
              if (data) {
                message.success('更新成功');
                form.resetFields();
                onResetSelfUpload();
                setConfirmVisible(false);
                setSelfModalVisible(false);
                onOk();
                setPagination({
                  ...defaultPg,
                });
              }
            });
          }}
          onCancel={() => {
            setConfirmVisible(false);
            setPagination({
              ...defaultPg,
            });
          }}
        >
          <Form form={form} colon={false} labelCol={{ xs: 4 }} wrapperCol={{ xs: 20 }}>
            <Form.Item
              required
              label="操作原因"
              name="operatorNotes"
              rules={[
                {
                  type: 'string',
                  max: 500,
                  message: '操作原因必填',
                  required: true,
                },
              ]}
            >
              <Input.TextArea style={{ width: 395 }} rows={4} />
            </Form.Item>
          </Form>
        </Modal>
      )}
    </>
  );
}

export default SelfBatchUpdateModalButton;

function getColumns(gridDeviceType: string) {
  return [
    {
      title: '机柜编号',
      dataIndex: 'tag',
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      render: (text: string, record: any) => (
        <Link
          target="_blank"
          to={() => {
            return generateDetailCabinetUrl({
              id: text,
              state: { ...record, deviceType: gridDeviceType },
            });
          }}
        >
          {text}
        </Link>
      ),
    },
    {
      title: '机房编号',
      dataIndex: 'idcTag',
    },
    {
      title: '楼栋编号',
      dataIndex: 'blockTag',
    },
    {
      title: '包间编号',
      dataIndex: 'roomTag',
    },

    {
      title: '机柜类型',
      dataIndex: 'gridTypeName',
    },
    {
      title: '厂商',
      dataIndex: 'vendor',
      show: true,
    },
    {
      title: 'U数',
      dataIndex: 'unitCount',
      show: true,
      render: (unitCount: string) =>
        typeof unitCount === 'number' || typeof unitCount === 'string' ? unitCount + 'U' : '--',
    },
    {
      title: '设计功率',
      dataIndex: 'ratedPower',
      show: true,
      render: (ratedPower: string) =>
        typeof ratedPower === 'number' || typeof ratedPower === 'string' ? ratedPower + 'kW' : '--',
    },
    {
      title: '额定承重',
      dataIndex: 'ratedLoad',
      show: true,
      render: (ratedLoad: string) =>
        typeof ratedLoad === 'number' || typeof ratedLoad === 'string' ? ratedLoad + 'kg' : '--',
    },
    {
      title: '机柜尺寸',
      dataIndex: 'gridLength',
      show: true,
      // @ts-ignore ts()
      render: (__, record) => (
        <span>
          {record.gridLength}mm*{record.gridWidth}mm*{record.gridHeight}mm
        </span>
      ),
    },
    {
      title: '投产日期',
      dataIndex: 'operationTime',
      show: true,
    },
    {
      title: '启用状态',
      dataIndex: 'operationStatusName',
      show: false,
    },
    {
      title: '供电模式',
      dataIndex: 'powerModel',
      show: true,
    },
    {
      title: '供应商',
      dataIndex: 'supplyVendor',
      show: true,
    },
  ];
}
