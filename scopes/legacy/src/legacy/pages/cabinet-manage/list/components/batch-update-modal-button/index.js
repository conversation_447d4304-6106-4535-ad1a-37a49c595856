import Form from '@ant-design/compatible/es/form';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';

import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import { VendorSelect } from '@manyun/crm.ui.vendor-select';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { fetchSpecs } from '@manyun/resource-hub.service.fetch-specs';
import { BlockStatusSelect } from '@manyun/resource-hub.ui.block-status-select';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';
import { PowerModelSelect } from '@manyun/resource-hub.ui.power-model-select';
import { RackTypesSelect } from '@manyun/resource-hub.ui.rack-types-select';
import { VALUE_TYPE_KEY_MAP } from '@manyun/resource-hub.ui.spec-select';

import { ModalButton } from '@manyun/dc-brain.legacy.components';

/**
 * @param {object} props
 * @param {import('antd-3/lib/form/Form').WrappedFormUtils<any>} props.form
 */
export function BatchUpdateForm({ form: { getFieldDecorator }, specInfo }) {
  const getOptions = options => {
    let newOptions = [];
    const optionsArr = options.split(',');
    newOptions = optionsArr.map(option => ({ label: option, value: option }));
    return newOptions;
  };

  const getSpecComponent = item => {
    const optComponent = item.inputWay === 'COMPONENT' && item.options && JSON.parse(item.options);

    switch (item.inputWay) {
      case 'INPUT':
        return item.valueType === VALUE_TYPE_KEY_MAP.NUMBER ? (
          <InputNumber
            style={{ width: 200 }}
            addonAfter={item.specUnit}
            min={0.01}
            max={999999.99}
            precision={2}
          />
        ) : (
          <Input style={{ width: 200 }} allowClear addonAfter={item.specUnit} />
        );
      case 'OPT':
        return (
          <Select
            style={{ width: 200 }}
            allowClear
            options={item.options && getOptions(item.options ?? '')}
          />
        );
      case 'COMPONENT':
        return optComponent?.components === 'location' ? (
          <LocationCascader
            style={{ width: 200 }}
            nodeTypes={Array.isArray(optComponent?.nodes) ? optComponent?.nodes : ['IDC', 'BLOCK']}
            authorizedOnly={
              Array.isArray(optComponent?.componentProps) &&
              optComponent?.componentProps.indexOf('authorized') > -1
            }
            includeVirtualBlocks={
              Array.isArray(optComponent?.componentProps) &&
              optComponent?.componentProps.indexOf('virtual') > -1
            }
            changeOnSelect={false}
          />
        ) : (
          <MetaTypeSelect style={{ width: 200 }} metaType={optComponent?.componentProps} />
        );
      default:
        return null;
    }
  };

  return (
    <Form colon={false} layout="vertical">
      <Row>
        <Col span={24}>
          <Form.Item label="机柜类型">
            {getFieldDecorator('gridType')(
              <RackTypesSelect style={{ width: '100%' }} trigger="onDidMount" />
            )}
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label="厂商">{getFieldDecorator('vendor')(<VendorSelect />)}</Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label="U数">
            {getFieldDecorator('unitCount')(<InputNumber min={1} max={999999999} precision={0} />)}
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label="设计功率">
            {getFieldDecorator('ratedPower')(
              <InputNumber
                style={{ width: 200 }}
                min={0}
                max={999999999}
                formatter={val => `${val}kW`}
                parser={value => {
                  let number = value;
                  Array.from('kW').forEach(char => {
                    number = number.replace(char, '');
                  });
                  return number;
                }}
                precision={2}
              />
            )}
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label="额定承重">
            {getFieldDecorator('ratedLoad', {
              rules: [{ pattern: /^\d+$/, message: '额定承重必须为整数' }],
            })(
              <InputNumber
                style={{ width: 200 }}
                min={1}
                max={999999999}
                formatter={val => `${val}kg`}
                parser={value => {
                  let number = value;
                  Array.from('kg').forEach(char => {
                    number = number.replace(char, '');
                  });
                  return number;
                }}
              />
            )}
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label="机柜长度">
            {getFieldDecorator('gridLength')(
              <InputNumber
                min={1}
                max={999999999}
                formatter={val => `${val}mm`}
                parser={value => {
                  let number = value;
                  Array.from('mm').forEach(char => {
                    number = number.replace(char, '');
                  });
                  return number;
                }}
                precision={0}
              />
            )}
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label="机柜宽度">
            {getFieldDecorator('gridWidth')(
              <InputNumber
                min={1}
                max={999999999}
                formatter={val => `${val}mm`}
                parser={value => {
                  let number = value;
                  Array.from('mm').forEach(char => {
                    number = number.replace(char, '');
                  });
                  return number;
                }}
                precision={0}
              />
            )}
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label="机柜高度">
            {getFieldDecorator('gridHeight')(
              <InputNumber
                min={1}
                max={999999999}
                formatter={val => `${val}mm`}
                parser={value => {
                  let number = value;
                  Array.from('mm').forEach(char => {
                    number = number.replace(char, '');
                  });
                  return number;
                }}
                precision={0}
              />
            )}
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label="投产日期">
            {getFieldDecorator('operationTime')(
              <DatePicker style={{ width: '100%' }} showToday={false} format="YYYY-MM-DD" />
            )}
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label="启用状态">
            {getFieldDecorator('operationStatus')(
              <BlockStatusSelect
                style={{ width: 200 }}
                getPopupContainer={trigger => trigger.parentNode}
              />
            )}
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label="供电模式">
            {getFieldDecorator('powerModel')(<PowerModelSelect style={{ width: '100%' }} />)}
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item label="供应商">
            {getFieldDecorator('supplyVendor')(<VendorSelect />)}
          </Form.Item>
        </Col>
        {Array.isArray(specInfo) &&
          specInfo.map((item, index) => (
            <Col key={item.id} span={24}>
              <Form.Item label={item.specName}>
                {getFieldDecorator(`specParams[${index}].specValue`, {
                  rules:
                    item.valueType === VALUE_TYPE_KEY_MAP.CHARACTER && item.inputWay === 'INPUT'
                      ? [
                          {
                            max: 200,
                            message: '最多输入 200 个字符！',
                          },
                        ]
                      : [],
                })(getSpecComponent(item))}
              </Form.Item>
            </Col>
          ))}
        <Col span={24}>
          <Form.Item label="操作备注">
            {getFieldDecorator('operatorNotes', {
              rules: [
                { required: true, message: '操作备注必填！' },
                {
                  max: 128,
                  message: '最多输入 128 个字符！',
                },
              ],
            })(<Input.TextArea autoSize={{ minRows: 3 }} placeholder="请输入" />)}
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
}

/**
 * @param {object} props
 * @param {[]} props.selectedRowKeys
 */
export function BatchUpdateModalButton({ form, selectedRowKeys, onOk }) {
  const text = '在线更新';
  const disabled = !(Array.isArray(selectedRowKeys) && selectedRowKeys.length);

  const config = useSelector(selectCurrentConfig);
  const configUtil = useMemo(() => {
    return new ConfigUtil(config);
  }, [config]);
  const gridDeviceType = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_GRID);
  const [specInfo, setSpecInfo] = useState([]);

  const getSpecs = useCallback(async () => {
    if (!gridDeviceType) {
      return;
    }
    const params = { deviceType: gridDeviceType };

    const { data, error } = await fetchSpecs(params);
    if (error) {
      message.error(error.message);
      return;
    }
    setSpecInfo(data.data);
  }, [gridDeviceType]);

  useEffect(() => {
    getSpecs();
  }, [getSpecs]);

  return (
    <ModalButton
      destroyOnClose
      text={text}
      bodyStyle={{ maxHeight: `calc(80vh - 109px)`, overflowY: 'auto' }}
      disabled={disabled}
      title={text}
      onOk={() =>
        new Promise(resolve => {
          form.validateFields(async (errMap, valueMap) => {
            if (errMap) {
              resolve(false);
              return;
            }

            if (Array.isArray(valueMap.specParams)) {
              specInfo.forEach((item, index) => {
                valueMap.specParams[index].specId = item.id;
                valueMap.specParams[index].specName = item.specName;
              });
              valueMap.specParams = valueMap.specParams
                .filter(item => !!item.specValue)
                .reduce((pre, cur) => {
                  if (Array.isArray(cur.specValue)) {
                    cur.specValue = cur.specValue[cur.specValue.length - 1];
                  }
                  pre.push(cur);
                  return pre;
                }, []);
            } else {
              valueMap.specParams = [];
            }

            const result = await onOk(valueMap);
            resolve(result);
          });
        })
      }
    >
      {visible => {
        if (!visible) {
          return null;
        }

        return <BatchUpdateForm form={form} specInfo={specInfo} />;
      }}
    </ModalButton>
  );
}

export default Form.create()(BatchUpdateModalButton);
