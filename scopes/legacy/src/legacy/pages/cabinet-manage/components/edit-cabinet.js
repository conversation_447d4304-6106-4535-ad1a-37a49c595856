import Form from '@ant-design/compatible/es/form';
import ExclamationCircleFilled from '@ant-design/icons/es/icons/ExclamationCircleFilled';
import { ApiSelect, Select } from '@galiojs/awesome-antd';
import moment from 'moment';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Typography } from '@manyun/base-ui.ui.typography';
import { VendorSelect } from '@manyun/crm.ui.vendor-select';
import { fetchGridCustomerCheck } from '@manyun/resource-hub.service.fetch-grid-customer-check';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';
import { PowerModelSelect } from '@manyun/resource-hub.ui.power-model-select';
import { RackTypesSelect } from '@manyun/resource-hub.ui.rack-types-select';
import { getComponent } from '@manyun/resource-hub.ui.spec-form-items';
import { getInitialValue } from '@manyun/resource-hub.ui.spec-form-items';
import { VALUE_TYPE_KEY_MAP } from '@manyun/resource-hub.ui.spec-select';

import { TinyDrawer } from '@manyun/dc-brain.legacy.components';
import {
  cabinetActions,
  fetchUpdateCabinet,
} from '@manyun/dc-brain.legacy.redux/actions/cabinetActions';
import * as cabinetService from '@manyun/dc-brain.legacy.services/cabinetService';
import * as roomService from '@manyun/dc-brain.legacy.services/roomService';

class EditCabinet extends Component {
  state = {
    operationTime: '',
    canEditPowerStatus: true,
  };

  closeEditCabinet = () => {
    this.props.closeVisible();
  };

  componentDidUpdate(prevProps) {
    if (this.props.editCabinetMess !== prevProps.editCabinetMess && this.props.editCabinetMess.id) {
      this.getCustomerGridCheck(
        this.props.editCabinetMess.idcTag,
        this.props.editCabinetMess.blockTag
      );
    }
  }

  getCustomerGridCheck = async (idcTag, blockTag) => {
    const { data, error } = await fetchGridCustomerCheck({
      idcTag,
      blockTag,
    });
    if (error) {
      message.error(error.message);
      return;
    }
    this.setState({
      canEditPowerStatus: !!data,
    });
  };

  submitEditCabinet = event => {
    event.preventDefault();
    this.props.form.validateFields((err, values) => {
      const errs = Object.keys(err);
      if (Array.isArray(errs) && errs[0]) {
        let errorElement = document.querySelector(`.${errs[0]}`);
        if (!errorElement) {
          const _key = err[errs[0]]
            .filter(i => Object.keys(i)[0])
            .map(i =>
              i[Object.keys(i)[0]]?.errors?.[0]?.field.replace(/\]./g, '-').replace(/\[/g, '-')
            );
          errorElement = document.querySelector(`.${_key[0]}`);
        }

        if (errorElement) {
          // 滚动到该字段的位置
          errorElement.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
          });
        }
      }
    });
    this.props.form.validateFieldsAndScroll(
      { scroll: { offsetBottom: TinyDrawer.FOOTER_HEIGHT } },
      async (err, values) => {
        if (err) {
          return;
        }
        const {
          editCabinetMess: { signedPower },
        } = this.props;
        const that = this;
        if (values.ratedPower < signedPower) {
          Modal.confirm({
            title: '确认要变更机柜设计功率吗？',
            content: '当前变更会导致该机柜的设计功率小于签约功率。',
            okText: '确认',
            cancelText: '取消',
            icon: <ExclamationCircleFilled />,
            onOk() {
              that.edit(values);
            },
          });
        } else {
          that.edit(values);
        }
      }
    );
  };

  edit = values => {
    const {
      editCabinetMess: { id },
      cabinetPageCondition,
      editCabinetMess,
    } = this.props;
    const editMess = {
      ...values,
      id,
      operationTime: values.operationTime.valueOf(),
      operatorNotes: values.operatorNotes.trim(),
    };

    if (!editMess.operatorNotes) {
      message.error('操作备注不可为空！');
      return;
    }

    if (values.specParams) {
      editCabinetMess.specInfo.forEach((item, index) => {
        values.specParams[index].specId = item.id;
        values.specParams[index].specName = item.specName;
      });

      const arr = values.specParams.filter(item => !!item.specValue);
      const res = [];

      //处理多选的specValue
      arr.forEach(item => {
        const cabinetItem = editCabinetMess.specInfo.find(val => val.id === item.specId);
        if (moment.isMoment(item.specValue)) {
          res.push({
            specId: item.specId,
            specName: item.specName,
            specValue: moment(item.specValue).format('HH:mm'),
          });
          return;
        }

        if (Array.isArray(item.specValue) && cabinetItem?.optionType) {
          item.specValue.map(valItem => {
            res.push({
              specId: item.specId,
              specName: item.specName,
              specValue: Array.isArray(valItem) ? valItem[valItem.length - 1] : valItem,
            });
          });

          return;
        }

        res.push({
          specId: item.specId,
          specName: item.specName,
          specValue: Array.isArray(item.specValue)
            ? item.specValue[item.specValue.length - 1]
            : item.specValue,
        });
      });

      editMess.specParams = res;
    } else {
      editMess.specParams = [];
    }

    this.props.fetchUpdateCabinet({
      editMess: editMess,
      cabinetPageCondition: cabinetPageCondition,
    });
  };

  getOptions = options => {
    let newOptions = [];
    const optionsArr = options.split(',');
    newOptions = optionsArr.map(option => ({ label: option, value: option }));
    return newOptions;
  };

  getLocationArr = value => {
    if (!value) {
      return;
    }
    const arr = value.split('.');
    const data = [arr[0]];
    for (let i = 1; i < arr.length; i++) {
      data[i] = data[i - 1] + '.' + arr[i];
    }
    return data;
  };

  render() {
    const form = this.props.form;
    const { getFieldDecorator } = form;
    const { editCabinetVisible, editCabinetMess, editCabinetSubmitLoading } = this.props;

    return (
      <div>
        <TinyDrawer
          width={416}
          title={<Typography.Title level={4}>编辑机柜</Typography.Title>}
          destroyOnClose
          visible={editCabinetVisible}
          submitButtonLoading={editCabinetSubmitLoading}
          onClose={this.closeEditCabinet}
          onCancel={this.closeEditCabinet}
          onSubmit={this.submitEditCabinet}
        >
          <Form layout="vertical" onSubmit={this.submitEditCabinet}>
            <Form.Item className="Tag" label="机柜编号">
              {getFieldDecorator('Tag', {
                rules: [
                  {
                    required: true,
                    message: '机柜为必填项',
                  },
                ],
                initialValue: editCabinetMess.tag,
              })(<Input style={{ width: 200 }} disabled />)}
            </Form.Item>
            <Form.Item className="idcTag" label="机房编号">
              {getFieldDecorator('idcTag', {
                rules: [
                  {
                    required: true,
                    message: '机房为必填项',
                  },
                ],
                initialValue: editCabinetMess.idcTag,
              })(<Input style={{ width: 200 }} disabled />)}
            </Form.Item>
            <Form.Item className="gridType" label="机柜类型">
              {getFieldDecorator('gridType', {
                rules: [{ required: true, message: '机柜类型为必填项' }],
                initialValue: editCabinetMess.gridType?.code,
              })(
                <RackTypesSelect
                  style={{ width: 200 }}
                  getPopupContainer={trigger => trigger.parentNode}
                  trigger="onDidMount"
                />
              )}
            </Form.Item>

            <Form.Item className="vendor" label="厂商">
              {getFieldDecorator('vendor', {
                initialValue: editCabinetMess.vendor,
              })(<VendorSelect style={{ width: 200 }} />)}
            </Form.Item>

            <Form.Item className="unitCount" label="U数">
              {getFieldDecorator('unitCount', {
                rules: [
                  { required: true, message: 'U数为必填项' },
                  {
                    type: 'number',
                    max: 999999999,
                    message: 'U数最大值为999999999',
                  },
                  { pattern: /^\d+$/, message: 'U数必须为整数' },
                ],
                initialValue: editCabinetMess.unitCount,
              })(<InputNumber style={{ width: 200 }} min={1} max={999999999} />)}
            </Form.Item>

            <Form.Item className="ratedPower" label="设计功率">
              {getFieldDecorator('ratedPower', {
                rules: [
                  { required: true, message: '设计功率为必填项' },
                  {
                    type: 'number',
                    max: 999999999,
                    message: '设计功率最大值为999999999',
                  },
                ],
                initialValue: editCabinetMess.ratedPower,
              })(
                <InputNumber
                  style={{ width: 200 }}
                  min={0}
                  max={999999999}
                  formatter={val => `${val}kW`}
                  parser={value => {
                    let number = value;
                    Array.from('kW').forEach(char => {
                      number = number.replace(char, '');
                    });
                    return number;
                  }}
                  precision={2}
                />
              )}
            </Form.Item>
            <Form.Item className="ratedLoad" label="额定承重">
              {getFieldDecorator('ratedLoad', {
                rules: [
                  { required: true, message: '额定承重为必填项' },
                  {
                    type: 'number',
                    max: 999999999,
                    message: '额定承重最大值为999999999',
                  },
                  { pattern: /^\d+$/, message: '额定承重必须为整数' },
                ],
                initialValue: editCabinetMess.ratedLoad,
              })(
                <InputNumber
                  style={{ width: 200 }}
                  min={1}
                  max={999999999}
                  formatter={val => `${val}kg`}
                  parser={value => {
                    let number = value;
                    Array.from('kg').forEach(char => {
                      number = number.replace(char, '');
                    });
                    return number;
                  }}
                />
              )}
            </Form.Item>

            <Form.Item className="powerStatus" label="上电状态">
              {getFieldDecorator('powerStatus', {
                rules: [{ required: true, message: '上电状态为必填项' }],
                initialValue: editCabinetMess.powerStatus?.code,
              })(
                <ApiSelect
                  style={{ width: 200 }}
                  disabled={!this.state.canEditPowerStatus}
                  trigger="onDidMount"
                  fieldNames={{ label: 'value', value: 'key' }}
                  dataService={async () => {
                    const response = await cabinetService.fetchPowerStatusListe();
                    if (response) {
                      return Promise.resolve(response);
                    } else {
                      return Promise.resolve([]);
                    }
                  }}
                  getPopupContainer={trigger => trigger.parentNode}
                />
              )}
            </Form.Item>
            <Form.Item className="gridLength" label="机柜长度">
              {getFieldDecorator('gridLength', {
                rules: [
                  { required: true, message: '机柜长度为必填项' },
                  {
                    type: 'number',
                    max: 999999999,
                    message: '机柜长度最大值为999999999',
                  },
                  { pattern: /^\d+$/, message: '机柜长度必须为整数' },
                ],
                initialValue: editCabinetMess.gridLength,
              })(
                <InputNumber
                  style={{ width: 200 }}
                  min={1}
                  max={999999999}
                  formatter={val => `${val}mm`}
                  parser={value => {
                    let number = value;
                    Array.from('mm').forEach(char => {
                      number = number.replace(char, '');
                    });
                    return number;
                  }}
                />
              )}
            </Form.Item>
            <Form.Item className="gridWidth" label="机柜宽度">
              {getFieldDecorator('gridWidth', {
                rules: [
                  { required: true, message: '机柜宽度为必填项' },
                  {
                    type: 'number',
                    max: 999999999,
                    message: '机柜宽度最大值为999999999',
                  },
                  { pattern: /^\d+$/, message: '机柜宽度必须为整数' },
                ],
                initialValue: editCabinetMess.gridWidth,
              })(
                <InputNumber
                  style={{ width: 200 }}
                  max={999999999}
                  formatter={val => `${val}mm`}
                  parser={value => {
                    let number = value;
                    Array.from('mm').forEach(char => {
                      number = number.replace(char, '');
                    });
                    return number;
                  }}
                />
              )}
            </Form.Item>
            <Form.Item className="gridHeight" label="机柜高度">
              {getFieldDecorator('gridHeight', {
                rules: [
                  { required: true, message: '机柜高度为必填项' },
                  {
                    type: 'number',
                    max: 999999999,
                    message: '机柜高度最大值为999999999',
                  },
                  { pattern: /^\d+$/, message: '机柜高度必须为整数' },
                ],
                initialValue: editCabinetMess.gridHeight,
              })(
                <InputNumber
                  style={{ width: 200 }}
                  max={999999999}
                  formatter={val => `${val}mm`}
                  parser={value => {
                    let number = value;
                    Array.from('mm').forEach(char => {
                      number = number.replace(char, '');
                    });
                    return number;
                  }}
                />
              )}
            </Form.Item>

            <Form.Item className="operationTime" label="投产日期">
              {getFieldDecorator('operationTime', {
                rules: [{ required: true, message: '投产日期为必填项' }],
                initialValue: moment(editCabinetMess.operationTime),
              })(
                <DatePicker
                  style={{ width: 200 }}
                  format="YYYY-MM-DD "
                  getCalendarContainer={trigger => trigger.parentNode}
                />
              )}
            </Form.Item>
            <Form.Item className="operationStatus" label="启用状态">
              {getFieldDecorator('operationStatus', {
                rules: [{ required: true, message: '状态为必填项' }],
                initialValue: editCabinetMess.operationStatus?.code,
              })(
                <ApiSelect
                  style={{ width: 200 }}
                  trigger="onDidMount"
                  fieldNames={{ label: 'value', value: 'key' }}
                  dataService={async () => {
                    const response = await roomService.fetchOperationStatus();
                    if (response) {
                      return Promise.resolve(response);
                    } else {
                      return Promise.resolve([]);
                    }
                  }}
                  getPopupContainer={trigger => trigger.parentNode}
                />
              )}
            </Form.Item>
            <Form.Item className="powerModel" label="供电模式">
              {getFieldDecorator('powerModel', {
                rules: [{ required: true, whitespace: true, message: '供电模式为必填项' }],
                initialValue: editCabinetMess.powerModel,
              })(
                <PowerModelSelect
                  style={{ width: 200 }}
                  getPopupContainer={trigger => trigger.parentNode}
                />
              )}
            </Form.Item>
            <Form.Item className="supplyVendor" label="供应商">
              {getFieldDecorator('supplyVendor', {
                rules: [
                  {
                    required: true,
                    message: '供应商简称为必填项',
                  },
                ],
                initialValue: editCabinetMess.supplyVendor,
              })(<VendorSelect style={{ width: 200 }} />)}
            </Form.Item>
            {editCabinetMess.specInfo &&
              editCabinetMess.specInfo.map((item, index) => (
                <Form.Item
                  key={item.id}
                  className={`specParams-${index}-specValue`}
                  label={item.specName}
                >
                  {getFieldDecorator(`specParams[${index}].specValue`, {
                    rules:
                      item.valueType === VALUE_TYPE_KEY_MAP.CHARACTER && item.inputWay === 'INPUT'
                        ? [
                            { required: item.required, message: `${item.specName}必填！` },
                            {
                              max: 200,
                              message: '最多输入 200 个字符！',
                            },
                          ]
                        : [{ required: item.required, message: `${item.specName}必填！` }],
                    initialValue: getInitialValue(item),
                  })(getComponent(item))}
                </Form.Item>
              ))}

            <Form.Item className="operatorNotes" label="操作备注">
              {getFieldDecorator('operatorNotes', {
                rules: [
                  { required: true, message: '操作备注必填！' },
                  {
                    max: 128,
                    message: '最多输入 128 个字符！',
                  },
                ],
              })(
                <Input.TextArea
                  autoSize={{ minRows: 3 }}
                  placeholder={`请输入修改 ${editCabinetMess.idcTag}.${editCabinetMess.blockTag}.${editCabinetMess.roomTag}.${editCabinetMess.tag} 信息的原因！`}
                />
              )}
            </Form.Item>
          </Form>
        </TinyDrawer>
      </div>
    );
  }
}
const mapStateToProps = ({
  cabinetManage: {
    editCabinetVisible,
    editCabinetMess,
    editCabinetSubmitLoading,
    cabinetPageCondition,
  },
}) => ({
  editCabinetVisible,
  editCabinetMess,
  editCabinetSubmitLoading,
  cabinetPageCondition,
});

const mapDispatchToProps = {
  closeVisible: cabinetActions.editCabinetVisible,
  fetchUpdateCabinet,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(Form.create({ name: 'edit_cabinet' })(EditCabinet));
