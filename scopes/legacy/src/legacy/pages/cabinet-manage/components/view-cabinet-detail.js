import React, { Component } from 'react';
import { connect } from 'react-redux';

import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Modal } from '@manyun/base-ui.ui.modal';

import { useMetaData } from '@manyun/resource-hub.hook.use-meta-data';
import { SpaceText } from '@manyun/resource-hub.ui.space-text';

import { cabinetActions } from '@manyun/dc-brain.legacy.redux/actions/cabinetActions';

class ViewCabinetDetail extends Component {
  hideModal = () => {
    this.props.viewVisible();
  };

  render() {
    const { viewCabinetVisible, cabinetMess } = this.props;
    return (
      <div>
        <Modal
          width="900px"
          title="机柜"
          visible={viewCabinetVisible}
          footer={null}
          onOk={this.hideModal}
          onCancel={this.hideModal}
        >
          <Descriptions>
            <Descriptions.Item label="机房名称">{cabinetMess.idcName}</Descriptions.Item>
            <Descriptions.Item label="机房编号">{cabinetMess.idcTag}</Descriptions.Item>
            <Descriptions.Item label="楼栋编号">{cabinetMess.blockTag}</Descriptions.Item>
            <Descriptions.Item label="包间编号">{cabinetMess.roomTag}</Descriptions.Item>
            <Descriptions.Item label="机柜编号">{cabinetMess.tag}</Descriptions.Item>
            <Descriptions.Item label="机柜类型">
              {cabinetMess.gridType ? cabinetMess.gridType.name : null}
            </Descriptions.Item>
            <Descriptions.Item label="启用状态">
              {cabinetMess.operationStatus ? cabinetMess.operationStatus.name : null}
            </Descriptions.Item>
            <Descriptions.Item label="厂商">{cabinetMess.vendor}</Descriptions.Item>
            <Descriptions.Item label="U数">{cabinetMess.unitCount + 'U'}</Descriptions.Item>
            <Descriptions.Item label="设计功率">{cabinetMess.ratedPower + 'kW'}</Descriptions.Item>
            <Descriptions.Item label="签约功率">
              {cabinetMess.signedPower ? cabinetMess.signedPower + 'kW' : '--'}
            </Descriptions.Item>
            <Descriptions.Item label="签约客户">{cabinetMess.customer}</Descriptions.Item>
            <Descriptions.Item label="额定承重">{cabinetMess.ratedLoad + 'kg'}</Descriptions.Item>
            <Descriptions.Item label="上电状态">
              {cabinetMess.powerStatus ? cabinetMess.powerStatus.name : null}
            </Descriptions.Item>
            <Descriptions.Item label="供电模式">{cabinetMess.powerModel}</Descriptions.Item>

            <Descriptions.Item label="机柜尺寸">
              {cabinetMess.gridLength}mm*{cabinetMess.gridWidth}mm*{cabinetMess.gridHeight}mm
            </Descriptions.Item>
            <Descriptions.Item label="投产日期">{cabinetMess.operationTime}</Descriptions.Item>
            <Descriptions.Item label="供应商">{cabinetMess.supplyVendor}</Descriptions.Item>
            {Array.isArray(cabinetMess.specInfo) &&
              cabinetMess.specInfo.map((item, index) => {
                const specOption =
                  item.inputWay === 'COMPONENT' && item.options && JSON.parse(item.options);

                return (
                  <Descriptions.Item key={item.specCode} label={item.specName}>
                    {specOption?.components === 'metaData' ? (
                      <MetaDataText
                        metaType={specOption.componentProps}
                        metaCode={item.specValue}
                      />
                    ) : (
                      <SpaceText guid={item.specValue} />
                    )}
                  </Descriptions.Item>
                );
              })}
          </Descriptions>
        </Modal>
      </div>
    );
  }
}
const mapStateToProps = ({ cabinetManage: { viewCabinetVisible, cabinetMess } }) => ({
  viewCabinetVisible,
  cabinetMess,
});

const mapDispatchToProps = {
  cabinetActions,
  viewVisible: cabinetActions.viewCabinetVisible,
};

const MetaDataText = ({ metaType, metaCode }) => {
  const [{ data }, { readMetaData }] = useMetaData(metaType);
  React.useEffect(() => {
    readMetaData(metaType);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [metaType]);

  return Array.isArray(data.data) && data.data.find(item => item.value === metaCode)
    ? data.data.find(item => item.value === metaCode).label
    : metaCode;
};

export default connect(mapStateToProps, mapDispatchToProps)(ViewCabinetDetail);
