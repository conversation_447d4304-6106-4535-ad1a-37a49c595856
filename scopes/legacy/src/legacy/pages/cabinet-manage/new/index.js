import QuestionCircleOutlined from '@ant-design/icons/es/icons/QuestionCircleOutlined';
import { saveAs } from 'file-saver';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Upload } from '@manyun/dc-brain.ui.upload';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { fetchSpecs } from '@manyun/resource-hub.service.fetch-specs';

import { GutterWrapper, StatusText, TinyCard, TinyTable } from '@manyun/dc-brain.legacy.components';
import { CABINET_LIST } from '@manyun/dc-brain.legacy.constants/urls';
import { cabinetActions } from '@manyun/dc-brain.legacy.redux/actions/cabinetActions';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import { redirectActionCreator } from '@manyun/dc-brain.legacy.redux/actions/routerActions';
import { downloadGridModel, uploadCabinet } from '@manyun/dc-brain.legacy.services/cabinetService';

class CreateCabinet extends Component {
  state = {
    selectedRowKeys: [],
    selectedRows: [],
    importCabinetList: {},
    importButtonLoading: false,
    specs: [],
  };

  componentDidMount() {
    this.props.syncCommonData({ strategy: { space: 'FORCED' } });
    this.getSpecs();
  }

  downloadDemo = async () => {
    const { response, filename = 'import-grids-template.csv' } = await downloadGridModel();
    if (!response) {
      return;
    }
    saveAs(response, filename);
  };

  beforeUpload = (file, fileList) => {
    this.setState({
      importCabinetList: {},
    });
    const fd = new FormData();
    fd.append('file', file);
    this.loadFetchData(fd);
    return false;
  };

  loadFetchData = async fd => {
    this.setState({
      importButtonLoading: true,
    });
    const { response, error } = await uploadCabinet(fd);
    this.props.startimportLoading();
    if (error) {
      message.error(error || '导入请求失败');
      this.setState({
        importButtonLoading: false,
      });
      this.props.stopImportLoading();
      return;
    }
    if (response) {
      this.setState({
        importButtonLoading: false,
      });
      this.props.stopImportLoading();
      if (response.faultTotal === 0) {
        message.success('导入成功！');
        this.props.redirect(CABINET_LIST);
      } else {
        this.setState({
          importCabinetList: response,
        });
      }
    }
  };

  getSpecs = async () => {
    const params = { deviceType: this.props.gridDeviceType };

    const { data, error } = await fetchSpecs(params);
    if (error) {
      message.error(error.message);
      return;
    }

    this.setState({
      specs: data.data.map(item => ({
        id: item.id,
        label: item.specName,
      })),
    });
  };

  columns = ctx => {
    return [
      {
        title: '行号',
        dataIndex: 'rowTag',
        fixed: 'left',
        width: 60,
      },
      {
        title: '机房编号',
        dataIndex: ['errDto', 'idcTag'],
        render: (tag, { errMessage }) => getToolTilp(tag, errMessage, 'idcTag'),
        width: 120,
      },
      {
        title: '楼栋编号',
        dataIndex: ['errDto', 'blockTag'],
        render: (tag, { errMessage }) => getToolTilp(tag, errMessage, 'blockTag'),
        width: 120,
      },
      {
        title: '包间编号',
        dataIndex: ['errDto', 'roomTag'],
        render: (tag, { errMessage }) => getToolTilp(tag, errMessage, 'roomTag'),
        width: 120,
      },
      {
        title: '机柜编号',
        dataIndex: ['errDto', 'tag'],
        render: (tag, { errMessage }) => getToolTilp(tag, errMessage, 'tag'),
        width: 120,
      },
      {
        title: '机柜类型',
        dataIndex: ['errDto', 'gridType'],
        render: (gridType, { errMessage }) => getToolTilp(gridType, errMessage, 'gridType'),
        width: 120,
      },
      {
        title: '厂商',
        dataIndex: ['errDto', 'vendor'],
        render: (vendor, { errMessage }) => getToolTilp(vendor, errMessage, 'vendor'),
        width: 120,
      },
      {
        title: 'U数(U)',
        dataIndex: ['errDto', 'unitCount'],
        width: 120,
        render: (unitCount, { errMessage }) => getToolTilp(unitCount, errMessage, 'unitCount'),
      },

      {
        title: '设计功率(kW)',
        width: 120,
        dataIndex: ['errDto', 'ratedPower'],
        render: (ratedPower, { errMessage }) => getToolTilp(ratedPower, errMessage, 'ratedPower'),
      },
      {
        title: '额定承重(kg)',
        width: 120,
        dataIndex: ['errDto', 'ratedLoad'],
        render: (ratedLoad, { errMessage }) => getToolTilp(ratedLoad, errMessage, 'ratedLoad'),
      },

      {
        title: '长度(mm)',
        width: 120,
        dataIndex: ['errDto', 'gridLength'],
        render: (gridLength, { errMessage }) => getToolTilp(gridLength, errMessage, 'gridLength'),
      },
      {
        title: '宽度(mm)',
        width: 120,
        dataIndex: ['errDto', 'gridWidth'],
        render: (gridWidth, { errMessage }) => getToolTilp(gridWidth, errMessage, 'gridWidth'),
      },
      {
        title: '高度(mm)',
        width: 120,
        dataIndex: ['errDto', 'gridHeight'],
        render: (gridHeight, { errMessage }) => getToolTilp(gridHeight, errMessage, 'gridHeight'),
      },
      {
        title: '供电模式',
        width: 120,
        dataIndex: ['errDto', 'powerModel'],
        render: (powerModel, { errMessage }) => getToolTilp(powerModel, errMessage, 'powerModel'),
      },
      {
        title: '启用状态',
        width: 120,
        dataIndex: ['errDto', 'operationStatus'],
        render: (operationStatus, { errMessage }) =>
          getToolTilp(operationStatus, errMessage, 'operationStatus'),
      },
      // {
      //   title: '建设日期',
      //   dataIndex: 'errDto.constructTime',
      //   render: (constructTime, { errMessage }) =>
      //     getToolTilp(constructTime, errMessage, 'constructTime'),
      // },
      {
        title: '投产日期',
        width: 120,
        dataIndex: ['errDto', 'operationTime'],
        render: (operationTime, { errMessage }) =>
          getToolTilp(operationTime, errMessage, 'operationTime'),
      },
      {
        title: '供应商',
        width: 120,
        dataIndex: ['errDto', 'supplyVendor'],
        render: (supplyVendor, { errMessage }) =>
          getToolTilp(supplyVendor, errMessage, 'supplyVendor'),
      },
    ].concat(
      ctx.state.specs.map(item => {
        return {
          title: item.label,
          width: 120,
          dataIndex: ['errDto', item.id],
          render: (_, { errMessage }) => getToolTilp(_, errMessage, item.id),
        };
      })
    );
  };

  render() {
    const { importLoading } = this.props;
    const { importCabinetList, importButtonLoading } = this.state;
    return (
      <>
        <TinyCard>
          <TinyTable
            rowKey={({ errDto }) => errDto.tag}
            actionsWrapperStyle={{ justifyContent: 'space-between' }}
            actions={[
              <GutterWrapper key="left" flex>
                <Upload
                  key="import"
                  beforeUpload={this.beforeUpload}
                  showUploadList={false}
                  accept=".csv,.xls,.xlsx"
                >
                  <Button type="primary" loading={importButtonLoading}>
                    导入
                  </Button>
                </Upload>
                <Button key="download" onClick={this.downloadDemo}>
                  下载模板
                </Button>
              </GutterWrapper>,
              <div key="total">
                {importCabinetList.excelCheckErrDtos && (
                  <GutterWrapper flex>
                    <StatusText status="normal">
                      导入总数&nbsp;{importCabinetList.checkTotal}
                    </StatusText>
                    <StatusText status="normal">
                      正确总数&nbsp;{importCabinetList.correctTotal}
                    </StatusText>
                    <StatusText status="alarm">
                      错误行数&nbsp;{importCabinetList.faultTotal}
                    </StatusText>
                  </GutterWrapper>
                )}
              </div>,
            ]}
            dataSource={importCabinetList.excelCheckErrDtos}
            loading={importLoading}
            columns={this.columns(this)}
            tableLayout="fixed"
            scroll={{ x: 'max-content' }}
            pagination={{
              total: importCabinetList.excelCheckErrDtos
                ? importCabinetList.excelCheckErrDtos.length
                : 0,
              showTotal: () =>
                `共 ${
                  importCabinetList.excelCheckErrDtos
                    ? importCabinetList.excelCheckErrDtos.length
                    : 0
                } 条`,
            }}
          />
        </TinyCard>
      </>
    );
  }
}

const mapStateToProps = ({ cabinetManage: { importLoading }, config: { configMap } }) => {
  const configUtil = new ConfigUtil(configMap.current);
  const gridDeviceType = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_GRID);
  return {
    importLoading,
    gridDeviceType,
  };
};

const mapDispatchToProps = {
  startimportLoading: cabinetActions.importLoading,
  stopImportLoading: cabinetActions.failure,
  syncCommonData: syncCommonDataActionCreator,
  redirect: redirectActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(CreateCabinet);

function getToolTilp(value, errMessage, dataType) {
  const newValue = value;
  if (Object.prototype.hasOwnProperty.call(errMessage, dataType)) {
    return (
      <Tooltip title={errMessage[dataType]}>
        <GutterWrapper size="2px" flex center>
          <StatusText status="alarm" style={{ display: 'inline-block' }}>
            {newValue ? newValue : '--'}
          </StatusText>
          <QuestionCircleOutlined />
        </GutterWrapper>
      </Tooltip>
    );
  }
  return newValue;
}

// function getDisabledCascader(value) {
//   return value.map((item) => {
//     if (item.metaType === 'ROOM') {
//       item = {
//         ...item,
//         children: Array.isArray(item.children) && item.children.length ? item.children : null,
//       };
//     } else {
//       item = {
//         ...item,
//         disabled: item.children ? false : true,
//         children: Array.isArray(item.children) && item.children ? item.children : null,
//       };
//     }
//     if (item.children && item.children.length) {
//       return { ...item, children: [...getDisabledCascader(item.children)] };
//     }

//     return item;
//   });
// }
