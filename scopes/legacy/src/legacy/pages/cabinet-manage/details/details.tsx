import React, { useEffect, useState } from 'react';
import { useLocation, useParams } from 'react-router-dom';

import { Card } from '@manyun/base-ui.ui.card';
import { message } from '@manyun/base-ui.ui.message';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import { useApps } from '@manyun/dc-brain.context.apps';
import type { Breadcrumb as LayoutBreadcrumb } from '@manyun/dc-brain.context.layout';
import { LayoutContent } from '@manyun/dc-brain.ui.layout';
import { useLoggedInUser } from '@manyun/iam.context.logged-in-user';
import { fetchSpecs } from '@manyun/resource-hub.service.fetch-specs';

import { Archives } from './archives';

const CabinetPowerOnOffRecord = React.lazy(
  // @ts-ignore: Could not find a declaration file
  () => import('@manyun_monitoring/page.cabinet-power-on-off-record')
);

export function CabinetManageDetail() {
  const { id } = useParams<{ id: string }>();
  const { state }: { state: any } = useLocation();
  const { user } = useLoggedInUser();
  const apps = useApps();
  const [activeKey, setActiveKey] = useState('archives');
  const [specInfo, setSpecInfo] = useState();
  if (!state?.guid) {
    return;
  }
  useEffect(() => {
    (async () => {
      if (state.deviceType && state.guid) {
        const params = { deviceType: state.deviceType, modelId: state.guid };
        const { data, error } = await fetchSpecs(params);
        if (error) {
          message.error(error.message);
          return;
        }
        setSpecInfo({ ...state, ...data.data.filter(item => !!item.specValue) });
      }
    })();
  }, [state.guid, state.deviceType, id]);
  return (
    <LayoutContent
      pageCode="page_resource-cabinets-details"
      composeBreadcrumbs={(v: LayoutBreadcrumb[]) => {
        // @ts-ignore
        return [...v, { key: 'basic_resource_cabinet_management-details', text: specInfo?.tag }];
      }}
    >
      <Card bordered={false} bodyStyle={{ height: '88vh', overflowY: 'auto' }}>
        <Tabs
          activeKey={activeKey}
          items={[
            {
              key: 'archives',
              label: '机柜档案',
              children: <Archives cabinetMess={specInfo} />,
            },
            {
              key: 'grid_power_record',
              label: '上下电记录',
              children: (
                <CabinetPowerOnOffRecord
                  deviceInfo={specInfo}
                  gridGuid={state.guid}
                  user={user}
                  apps={apps}
                />
              ),
            },
          ]}
          onChange={key => {
            setActiveKey(key);
          }}
        />
      </Card>
    </LayoutContent>
  );
}
