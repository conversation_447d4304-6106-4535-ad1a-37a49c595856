import React, { useEffect, useState } from 'react';

import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useMetaData } from '@manyun/resource-hub.hook.use-meta-data';
import { MetaTypeText } from '@manyun/resource-hub.ui.meta-type-text';
import { SpaceText } from '@manyun/resource-hub.ui.space-text';

export function isValidJSON(str: string) {
  try {
    const _v = JSON.parse(str);
    return _v;
  } catch (e) {
    return undefined;
  }
}
// @ts-ignore
const MetaDataText = ({ metaType, metaCode }) => {
  const [{ data }, { readMetaData }] = useMetaData(metaType);
  React.useEffect(() => {
    readMetaData(metaType);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [metaType]);

  return Array.isArray(data.data) && data.data.find(item => item.value === metaCode)
    ? // @ts-ignore
      data.data.find(item => item.value === metaCode).label
    : metaCode;
};
export function Archives({ cabinetMess }: { cabinetMess: any }) {
  const [causalityInfo, setCausalityInfo] = useState<
    {
      name: string;
      value: string;
      unit: string | null;
      options: any;
    }[]
  >();
  useEffect(() => {
    if (cabinetMess) {
      const _data = cabinetMess.specDataList?.map((info: any) => ({
        name: info.specName,
        value: info.specValue ?? '--',
        unit: info.specValue && info.specUnit,
        options: isValidJSON(info.options),
      }));
      setCausalityInfo(_data);
    }
  }, [cabinetMess]);

  return (
    <Space style={{ width: '100%' }} direction="vertical" size="middle">
      <Space direction="vertical">
        <Typography.Title showBadge level={5}>
          基本信息
        </Typography.Title>
        {cabinetMess && (
          <Descriptions>
            <Descriptions.Item label="机房名称">{cabinetMess.idcName}</Descriptions.Item>
            <Descriptions.Item label="机房编号">{cabinetMess.idcTag}</Descriptions.Item>
            <Descriptions.Item label="楼栋编号">{cabinetMess.blockTag}</Descriptions.Item>
            <Descriptions.Item label="包间编号">{cabinetMess.roomTag}</Descriptions.Item>
            <Descriptions.Item label="机柜编号">{cabinetMess.tag}</Descriptions.Item>
            <Descriptions.Item label="机柜类型">
              {cabinetMess.gridType ? cabinetMess.gridType.name : null}
            </Descriptions.Item>
            <Descriptions.Item label="启用状态">
              {cabinetMess.operationStatus ? cabinetMess.operationStatus.name : null}
            </Descriptions.Item>
            <Descriptions.Item label="厂商">{cabinetMess.vendor}</Descriptions.Item>
            <Descriptions.Item label="U数">{cabinetMess.unitCount + 'U'}</Descriptions.Item>
            <Descriptions.Item label="设计功率">{cabinetMess.ratedPower + 'kW'}</Descriptions.Item>
            <Descriptions.Item label="签约功率">
              {cabinetMess.signedPower ? cabinetMess.signedPower + 'kW' : '--'}
            </Descriptions.Item>
            <Descriptions.Item label="签约客户">{cabinetMess.customer}</Descriptions.Item>
            <Descriptions.Item label="额定承重">{cabinetMess.ratedLoad + 'kg'}</Descriptions.Item>
            <Descriptions.Item label="上电状态">
              {cabinetMess.powerStatus ? cabinetMess.powerStatus.name : null}
            </Descriptions.Item>
            <Descriptions.Item label="供电模式">{cabinetMess.powerModel}</Descriptions.Item>

            <Descriptions.Item label="机柜尺寸">
              {cabinetMess.gridLength}mm*{cabinetMess.gridWidth}mm*{cabinetMess.gridHeight}mm
            </Descriptions.Item>
            <Descriptions.Item label="投产日期">{cabinetMess.operationTime}</Descriptions.Item>
            <Descriptions.Item label="供应商">{cabinetMess.supplyVendor}</Descriptions.Item>
            {Array.isArray(cabinetMess.specInfo) &&
              cabinetMess.specInfo.map((item: any) => {
                const specOption =
                  item.inputWay === 'COMPONENT' && item.options && JSON.parse(item.options);

                return (
                  <Descriptions.Item key={item.specCode} label={item.specName}>
                    {specOption?.components === 'metaData' ? (
                      <MetaDataText
                        metaType={specOption.componentProps}
                        metaCode={item.specValue}
                      />
                    ) : (
                      <SpaceText guid={item.specValue} />
                    )}
                  </Descriptions.Item>
                );
              })}
          </Descriptions>
        )}
      </Space>
      <Space direction="vertical">
        <Typography.Title showBadge level={5}>
          机柜属性
        </Typography.Title>
        <Descriptions>
          {causalityInfo?.map(info => {
            if (
              info.options &&
              info.options.componentProps &&
              Object.keys(info.options.componentProps)?.length > 1 &&
              info.value !== '--'
            ) {
              return (
                <Descriptions.Item key={info.name} label={info.name}>
                  <MetaTypeText metaType={info.options.componentProps} code={info.value} />
                </Descriptions.Item>
              );
            }

            return (
              <Descriptions.Item key={info.name} label={info.name}>
                {info.value}
                {info.unit}
              </Descriptions.Item>
            );
          })}
        </Descriptions>
      </Space>
    </Space>
  );
}
