import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import { <PERSON>lip<PERSON>, G<PERSON>Wrapper, StatusText, TinyTable } from '@manyun/dc-brain.legacy.components';
import {
  LEVEL_STATUS_MAP,
  LEVEL_TEXT_MAP,
} from '@manyun/dc-brain.legacy.pages/notice-manage/constants';
import {
  fetchValidNoticeListPage,
  noticeActions,
} from '@manyun/dc-brain.legacy.redux/actions/noticeActions';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';

import Notice from '../edit-notice';

const NOTICE_CONTENT_DATA_INDEX = 'content';

const getColumns = ctx => {
  const columns = [
    {
      title: '公告标题123',
      dataIndex: 'title',
      width: 210,
      render: (text, record) => (
        <Link
          to={urls.generateNoticeDetailLocation({
            id: record.id,
          })}
        >
          <Ellipsis lines={1} tooltip>
            {text}
          </Ellipsis>
        </Link>
      ),
    },
    {
      title: '公告内容',
      dataIndex: NOTICE_CONTENT_DATA_INDEX,
      width: 350,
      render: content => {
        return (
          <Ellipsis lines={1} tooltip>
            {content}
          </Ellipsis>
        );
      },
    },
    {
      title: '重要程度',
      dataIndex: 'importance',
      width: 80,
      render: (__, record) => {
        return (
          <StatusText status={LEVEL_STATUS_MAP[record.importance]}>
            {LEVEL_TEXT_MAP[record.importance]}
          </StatusText>
        );
      },
    },
    {
      title: '更新时间',
      dataIndex: 'gmtModified',
      dataType: 'datetime',
      width: 164,
    },
    {
      title: '更新人',
      dataIndex: 'modifierName',
      width: 80,
    },
  ];
  let noticeContentClmnIdx = -1;
  const otherClmnsWidth = columns.reduce((sum, { dataIndex, width }, idx) => {
    if (dataIndex === NOTICE_CONTENT_DATA_INDEX) {
      noticeContentClmnIdx = idx;
      return sum;
    }
    if (typeof width == 'number' && !Number.isNaN(width)) {
      return sum + width;
    }
    return sum;
  }, 0);
  if (noticeContentClmnIdx > -1) {
    const noticeContentWidth = `calc(100% - ${otherClmnsWidth}px)`;
    columns[noticeContentClmnIdx].width = noticeContentWidth;
  }
  return columns;
};

class PublicTable extends Component {
  state = {
    pageNo: 1,
    pageSize: 10,
  };
  componentDidMount() {
    const { noticePageCondition } = this.props;
    this.props.fetchValidNoticeListPage({
      ...noticePageCondition,
    });
  }

  onChangePage = (pageNo, pageSize) => {
    const { noticePageCondition } = this.props;
    this.props.fetchValidNoticeListPage({
      ...noticePageCondition,
      pageNum: pageNo,
      pageSize,
    });
  };

  render() {
    const { noticePage, loading, noticePageCondition } = this.props;
    return (
      <GutterWrapper mode="vertical">
        <TinyTable
          rowKey="id"
          columns={getColumns(this)}
          dataSource={noticePage.list}
          loading={loading}
          pagination={{
            total: noticePage.total,
            current: noticePageCondition.pageNum,
            pageSize: noticePageCondition.pageSize,
            onChange: this.onChangePage,
          }}
        />
        <Notice />
      </GutterWrapper>
    );
  }
}

const mapStateToProps = ({ noticeManage: { noticePage, loading, noticePageCondition } }) => ({
  noticePage,
  loading,
  noticePageCondition,
});
const mapDispatchToProps = {
  noticeActions,
  fetchValidNoticeListPage,
  saveSearchCondition: noticeActions.saveSearchCondition,
  getNoticeListSuccess: noticeActions.getNoticeListSuccess,
};
export default connect(mapStateToProps, mapDispatchToProps)(PublicTable);
