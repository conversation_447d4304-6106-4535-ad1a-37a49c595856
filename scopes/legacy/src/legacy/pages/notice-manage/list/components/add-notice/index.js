import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import moment from 'moment';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Switch } from '@manyun/base-ui.ui.switch';
import { Typography } from '@manyun/base-ui.ui.typography';

import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { McUpload as Upload } from '@manyun/dc-brain.ui.upload';

import { TinyModal } from '@manyun/dc-brain.legacy.components';
import TargetTreeTransfer from '@manyun/dc-brain.legacy.components/relationship-connector/target-tree-transfer';
import { addNotice } from '@manyun/dc-brain.legacy.redux/actions/noticeActions';
import { fetchSelectResourceList } from '@manyun/dc-brain.legacy.services/noticeService';

import { RANGE_TYPE_KEY_MAP } from '../../../constants';

const dateFormat = 'YYYY-MM-DD HH:mm:ss';
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};

class AddNotice extends Component {
  state = {
    noticeVisible: false,
    fileList: [],
    rangeInfo: '',
  };

  publishNotice = () => {
    const { fileList } = this.state;
    this.props.form.validateFields((err, values) => {
      if (!err) {
        const { content, importance, rangeType, resourceIdList, title, immediately, popUp } =
          values;
        const params = {
          content,
          importance,
          rangeType,
          resourceIdList,
          title,
          files: fileList.map(file => ({ ...McUploadFile.toApiObject(file), fileType: null })),
          validBeginDate: values.date[0].valueOf(),
          validEndDate: values.date[1].valueOf(),
          immediately,
          popUp,
        };
        const noticePageCondition = {
          status: 'ALL',
          published: true,
        };
        this.props.addNotice({
          noticeMess: params,
          noticePageCondition,
        });
        this.changeNoticeVisible();
      }
    });
  };

  changeNoticeVisible = () => {
    this.setState({ noticeVisible: !this.state.noticeVisible, fileList: [] });
  };

  render() {
    const { getFieldDecorator, getFieldValue } = this.props.form;
    const { permissions } = this.props;
    const { fileList, rangeInfo } = this.state;

    const disabledDate = current => {
      if (rangeInfo === 'end') {
        return false;
      }
      if (rangeInfo === 'start') {
        return current && current <= moment(moment().format('YYYY/MM/DD'));
      }
    };

    const range = (start, end) => {
      const result = [];
      for (let i = start; i < end; i++) {
        result.push(i);
      }
      return result;
    };

    const disabledTime = (current, type) => {
      const hours = moment().hours();
      const minute = moment().minute();
      const second = moment().second();
      const choseHour = moment(current).hours();
      const choseMinute = moment(current).minute();
      const isToday = moment(current).isSame(moment(), 'day');
      if (isToday && type === 'end') {
        if (choseHour === hours) {
          return {
            disabledHours: () => range(0, hours),
            disabledMinutes: () => range(0, minute),
            disabledSeconds: choseMinute === minute ? () => range(0, second) : undefined,
          };
        }
        return {
          disabledHours: () => range(0, hours),
        };
      }
      return;
    };

    return [
      permissions.includes('notice:add') && (
        <Button type="primary" onClick={this.changeNoticeVisible} key="btn">
          发布公告
        </Button>
      ),
      <TinyModal
        title="发布公告"
        bodyStyle={{ height: '450px', overflow: 'hidden', overflowY: 'scroll' }}
        key="modal"
        width={720}
        destroyOnClose
        visible={this.state.noticeVisible}
        onCancel={this.changeNoticeVisible}
        onOk={this.publishNotice}
      >
        <Form {...formItemLayout} colon={false}>
          <Form.Item label="公告标题">
            {getFieldDecorator('title', {
              rules: [
                { required: true, message: '公告标题必填！' },
                {
                  max: 24,
                  message: '最多输入 24 个字符！',
                },
              ],
            })(<Input />)}
          </Form.Item>
          <Form.Item label="公告内容">
            {getFieldDecorator('content', {
              rules: [
                { required: true, message: '公告内容必填！' },
                {
                  max: 128,
                  message: '最多输入 128 个字符！',
                },
              ],
            })(<Input.TextArea />)}
          </Form.Item>
          <Form.Item label="公告范围">
            {getFieldDecorator('rangeType', {
              rules: [{ required: true, message: '公告范围必选！' }],
            })(
              <Radio.Group>
                <Radio value="ALL">全部</Radio>
                <Radio value="PART">部分</Radio>
              </Radio.Group>
            )}
          </Form.Item>
          {getFieldValue('rangeType') === RANGE_TYPE_KEY_MAP.PART && (
            <Form.Item label=" ">
              {getFieldDecorator('resourceIdList', {
                initialValue: [],
                rules: [{ required: true, message: '公告范围必选！' }],
              })(
                <TargetTreeTransfer
                  checkedKeys={[]}
                  targetSelectPlaceholder="请输入区域关键字"
                  targetDatasourceService={fetchSelectResourceList}
                ></TargetTreeTransfer>
              )}
            </Form.Item>
          )}
          <Form.Item label="有效期限">
            {getFieldDecorator('date', {
              validateFirst: true,
              rules: [
                { required: true, message: '有效期限必选！' },
                {
                  validator: (_, value, callback) => {
                    if (value[1] >= moment(moment().format('YYYY-MM-DD HH:mm:ss'))) {
                      callback();
                    } else {
                      callback('结束时间不可选择历史时间!');
                    }
                  },
                },
              ],
            })(
              <DatePicker.RangePicker
                showTime
                format={dateFormat}
                disabledDate={disabledDate}
                disabledTime={disabledTime}
                onCalendarChange={(values, _, info) => this.setState({ rangeInfo: info.range })}
              />
            )}
          </Form.Item>
          <Form.Item label="重要程度">
            {getFieldDecorator('importance', {
              rules: [{ required: true, message: '重要程度必选！' }],
            })(
              <Radio.Group name="radiogroup">
                <Radio value="HIGH" style={{ color: `var(--${prefixCls}-error-color)` }}>
                  重要
                </Radio>
                <Radio value="MID" style={{ color: `var(--${prefixCls}-warning-color)` }}>
                  中等
                </Radio>
                <Radio value="LOW" style={{ color: `var(--${prefixCls}-success-color)` }}>
                  普通
                </Radio>
              </Radio.Group>
            )}
          </Form.Item>
          <Form.Item label="通知时间">
            {getFieldDecorator('immediately', {
              rules: [{ required: true, message: '通知时间必选！' }],
              initialValue: true,
            })(
              <Radio.Group>
                <Radio value={true}>立即通知</Radio>
                <Radio value={false}>生效通知</Radio>
              </Radio.Group>
            )}
          </Form.Item>
          <Form.Item label="弹窗提醒">
            {getFieldDecorator('popUp', {
              valuePropName: 'checked',
              initialValue: false,
            })(<Switch />)}
          </Form.Item>
          <Form.Item label="附件">
            <Upload
              accept="image/*,.pdf,.xls,.xlsx,.doc,.docx"
              showUploadList
              allowDelete
              maxFileSize={20}
              maxCount={5}
              fileList={fileList}
              onChange={({ file, fileList }) => {
                if (
                  file.status === 'uploading' &&
                  fileList.filter(file => file.status !== 'uploading').length === 5
                ) {
                  message.error('上传附件数量最多5个!');
                  return;
                }
                this.setState({ fileList });
              }}
            >
              <Button type="primary">上传</Button> <br />
              <Typography.Text type="secondary" style={{ marginTop: '8px' }}>
                支持扩展名：image/*,.pdf,.xls,.xlsx,.doc,.docx
              </Typography.Text>
            </Upload>
          </Form.Item>
        </Form>
      </TinyModal>,
    ];
  }
}

const mapStateToProps = ({ user: { permissions } }) => ({
  permissions,
});
const mapDispatchToProps = {
  addNotice,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(Form.create({ name: 'add_notice' })(AddNotice));
