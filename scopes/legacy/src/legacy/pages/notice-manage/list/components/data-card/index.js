import React, { Component } from 'react';
import { connect } from 'react-redux';

import { TinyCard } from '@manyun/dc-brain.legacy.components';

import DataTable from './data-table';
import PublicTable from './public-table';

class DataCard extends Component {
  render() {
    const { permissions } = this.props;
    return (
      <TinyCard>{permissions.includes('notice:add') ? <DataTable /> : <PublicTable />}</TinyCard>
    );
  }
}

const mapStateToProps = ({ user: { permissions } }) => ({ permissions });

export default connect(mapStateToProps)(DataCard);
