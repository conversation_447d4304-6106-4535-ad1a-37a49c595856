import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import get from 'lodash/get';
import moment from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Switch } from '@manyun/base-ui.ui.switch';
import { Typography } from '@manyun/base-ui.ui.typography';

import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { deleteBizFile } from '@manyun/dc-brain.service.delete-biz-file';
import { fetchBizFileInfos } from '@manyun/dc-brain.service.fetch-biz-file-infos';
import { McUpload as Upload } from '@manyun/dc-brain.ui.upload';

import TargetTreeTransfer from '@manyun/dc-brain.legacy.components/relationship-connector/target-tree-transfer';
import TinyModal from '@manyun/dc-brain.legacy.components/tiny-modal';
import {
  noticeActions,
  updateNotice,
  updatePublishNotice,
} from '@manyun/dc-brain.legacy.redux/actions/noticeActions';
import { fetchSelectResourceList } from '@manyun/dc-brain.legacy.services/noticeService';

import { RANGE_TYPE_KEY_MAP } from '../../../constants';

const dateFormat = 'YYYY-MM-DD HH:mm:ss';
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};

class EditNotice extends Component {
  state = {
    fileList: [],
    noticeMessId: '',
    deleteFileList: [],
    rangeInfo: '',
  };
  componentDidUpdate(preProps, preState) {
    const { noticeMess, noticeVisible } = this.props;
    if (
      preProps.noticeMess &&
      // preProps.noticeMess.id !== noticeMess.id &&
      preProps.noticeVisible !== noticeVisible
    ) {
      this.handleNoticeMessChange(noticeMess.id);
    }
  }

  handleNoticeMessChange = noticeId => {
    this.initialFileList(noticeId);
  };

  initialFileList = async noticeId => {
    if (!noticeId) {
      return;
    }
    const { error, data } = await fetchBizFileInfos({
      targetId: noticeId,
      targetType: 'NOTICE',
    });
    if (error) {
      this.setState({ fileList: [] });
      return;
    }
    const fileList = data.data.map(data => ({ ...data, src: data.src }));

    this.setState({ fileList: fileList });
  };
  // 630先使用单独删除的接口，之后会提供文件批量删除的接口(阳哥负责)，todo
  deleteNoticeFile = fileList => {
    fileList
      ?.filter(file => file.targetId && file.targetType)
      ?.forEach(async file => {
        const { error } = await deleteBizFile({
          targetId: file.targetId,
          targetType: file.targetType,
          fileType: file.type,
          filePath: file.id.toString(),
        });
        if (error) {
          message.error(error.message);
          return;
        }
      });
  };

  eidtAndPublish = () => {
    this.props.form.validateFields((err, values) => {
      if (!err) {
        const { content, importance, rangeType, resourceIdList, title, immediately, popUp } =
          values;
        const { noticeMess, noticePageCondition } = this.props;
        // 还得区分有没有targetID 和 targetType
        const { fileList, deleteFileList } = this.state;
        this.deleteNoticeFile(deleteFileList);
        const params = {
          id: noticeMess.id,
          content,
          importance,
          rangeType,
          resourceIdList,
          title,
          files: fileList.map(file => ({ ...McUploadFile.toApiObject(file), fileType: null })),
          validBeginDate: values.date[0].valueOf(),
          validEndDate: values.date[1].valueOf(),
          immediately,
          popUp,
        };
        this.props.updatePublishNotice({
          noticeMess: params,
          noticePageCondition: noticePageCondition,
        });
      }
    });
  };

  submitEditNotice = () => {
    this.props.form.validateFields((err, values) => {
      if (!err) {
        const { content, importance, rangeType, resourceIdList, title, immediately, popUp } =
          values;
        const { noticeMess, noticePageCondition } = this.props;
        const { fileList, deleteFileList } = this.state;
        this.deleteNoticeFile(deleteFileList);

        const params = {
          id: noticeMess.id,
          content,
          importance,
          rangeType,
          resourceIdList: resourceIdList === undefined ? [] : resourceIdList,
          title,
          files: fileList.map(file => ({ ...McUploadFile.toApiObject(file), fileType: null })),
          validBeginDate: values.date[0].valueOf(),
          validEndDate: values.date[1].valueOf(),
          immediately,
          popUp,
        };
        this.props.updateNotice({
          noticeMess: params,
          noticePageCondition: noticePageCondition,
        });
      }
    });
  };

  render() {
    const { getFieldDecorator, getFieldValue } = this.props.form;
    const { noticeVisible, noticeMess, changeNoticeVisible } = this.props;
    const { fileList, rangeInfo } = this.state;

    const disabledDate = current => {
      if (rangeInfo === 'end') {
        return false;
      }
      if (rangeInfo === 'start') {
        return current && current <= moment(moment().format('YYYY/MM/DD'));
      }
    };

    const range = (start, end) => {
      const result = [];
      for (let i = start; i < end; i++) {
        result.push(i);
      }
      return result;
    };

    const disabledTime = (current, type) => {
      const hours = moment().hours();
      const minute = moment().minute();
      const second = moment().second();
      const choseHour = moment(current).hours();
      const choseMinute = moment(current).minute();
      const isToday = moment(current).isSame(moment(), 'day');
      if (isToday && type === 'end') {
        if (choseHour === hours) {
          return {
            disabledHours: () => range(0, hours),
            disabledMinutes: () => range(0, minute),
            disabledSeconds: choseMinute === minute ? () => range(0, second) : undefined,
          };
        }
        return {
          disabledHours: () => range(0, hours),
        };
      }
      return;
    };

    return (
      <TinyModal
        title="更新、发布公告"
        destroyOnClose
        bodyStyle={{ height: '450px', overflow: 'hidden', overflowY: 'scroll' }}
        width={720}
        visible={noticeVisible}
        onCancel={changeNoticeVisible}
        footer={
          <>
            <Button onClick={this.eidtAndPublish}>更新并发布</Button>
            <Button type="primary" onClick={this.submitEditNotice}>
              更新
            </Button>
          </>
        }
      >
        <Form {...formItemLayout} colon={false}>
          <Form.Item label="公告标题">
            {getFieldDecorator('title', {
              rules: [
                { required: true, message: '公告标题必填！' },
                {
                  max: 24,
                  message: '最多输入 24 个字符！',
                },
              ],
              initialValue: noticeMess.title,
            })(<Input />)}
          </Form.Item>
          <Form.Item label="公告内容">
            {getFieldDecorator('content', {
              rules: [
                { required: true, message: '公告内容必填！' },
                {
                  max: 128,
                  message: '最多输入 128 个字符！',
                },
              ],
              initialValue: noticeMess.content,
            })(<Input.TextArea />)}
          </Form.Item>
          <Form.Item label="公告范围">
            {getFieldDecorator('rangeType', {
              rules: [{ required: true, message: '公告范围必选！' }],
              initialValue: noticeMess.rangeType,
            })(
              <Radio.Group>
                <Radio value="ALL">全部</Radio>
                <Radio value="PART">部分</Radio>
              </Radio.Group>
            )}
          </Form.Item>
          {getFieldValue('rangeType') === RANGE_TYPE_KEY_MAP.PART && (
            <Form.Item label=" ">
              {getFieldDecorator('resourceIdList', {
                initialValue: get(noticeMess, 'resourceList', []).map(({ id }) => String(id)),
                rules: [{ required: true, message: '公告范围必选！' }],
              })(
                <TargetTreeTransfer
                  checkedKeys={get(noticeMess, 'resourceList', []).map(({ id }) => String(id))}
                  targetDatasourceService={fetchSelectResourceList}
                ></TargetTreeTransfer>
              )}
            </Form.Item>
          )}
          <Form.Item label="有效期限">
            {getFieldDecorator('date', {
              validateFirst: true,
              rules: [
                { required: true, message: '有效期限必选！' },
                {
                  validator: (_, value, callback) => {
                    if (value[1] >= moment(moment().format('YYYY-MM-DD HH:mm:ss'))) {
                      callback();
                    } else {
                      callback('结束时间不可选择历史时间!');
                    }
                  },
                },
              ],
              initialValue: [moment(noticeMess.validBeginDate), moment(noticeMess.validEndDate)],
            })(
              <DatePicker.RangePicker
                showTime
                format={dateFormat}
                disabledDate={disabledDate}
                disabledTime={disabledTime}
                onCalendarChange={(values, _, info) => this.setState({ rangeInfo: info.range })}
              />
            )}
          </Form.Item>
          <Form.Item label="重要程度">
            {getFieldDecorator('importance', {
              rules: [{ required: true, message: '重要程度必选！' }],
              initialValue: noticeMess.importance,
            })(
              <Radio.Group name="radiogroup">
                <Radio value="HIGH">
                  <Typography.Text type="danger">重要</Typography.Text>
                </Radio>
                <Radio value="MID">
                  <Typography.Text type="warning">中等</Typography.Text>
                </Radio>
                <Radio value="LOW">
                  <Typography.Text type="success">普通</Typography.Text>
                </Radio>
              </Radio.Group>
            )}
          </Form.Item>
          <Form.Item label="通知时间">
            {getFieldDecorator('immediately', {
              rules: [{ required: true, message: '通知时间必选！' }],
              initialValue: noticeMess.immediatelyNotify,
            })(
              <Radio.Group>
                <Radio value={true}>立即通知</Radio>
                <Radio value={false}>生效通知</Radio>
              </Radio.Group>
            )}
          </Form.Item>
          <Form.Item label="弹窗提醒">
            {getFieldDecorator('popUp', {
              valuePropName: 'checked',
              initialValue: noticeMess.popUp,
            })(<Switch />)}
          </Form.Item>
          <Form.Item label="附件">
            <Upload
              accept="image/*,.pdf,.xls,.xlsx,.doc,.docx"
              showUploadList
              allowDelete
              maxFileSize={20}
              maxCount={5}
              fileList={fileList}
              onChange={({ file, fileList }) => {
                if (file.status === 'removed') {
                  const { deleteFileList } = this.state;
                  this.setState({ deleteFileList: [...deleteFileList, file] });
                }
                if (
                  file.status === 'uploading' &&
                  fileList.filter(file => file.status !== 'uploading').length === 5
                ) {
                  message.error('上传附件数量最多5个!');
                  return;
                }
                this.setState({ fileList });
              }}
            >
              <Button type="primary">上传</Button> <br />
              <Typography.Text type="secondary" style={{ marginTop: '8px' }}>
                支持扩展名：image/*,.pdf,.xls,.xlsx,.doc,.docx
              </Typography.Text>
            </Upload>
          </Form.Item>
        </Form>
      </TinyModal>
    );
  }
}

const mapStateToProps = ({
  noticeManage: { noticePage, noticePageCondition, noticeVisible, noticeMess, noticeTreeSeclect },
}) => ({
  noticePage,
  noticePageCondition,
  noticeVisible,
  noticeMess,
  noticeTreeSeclect,
});
const mapDispatchToProps = {
  changeNoticeVisible: noticeActions.changeNoticeVisible,
  updateNotice,
  updatePublishNotice,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(Form.create({ name: 'edit_notice' })(EditNotice));
