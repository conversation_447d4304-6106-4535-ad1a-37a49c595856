import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import { Select } from '@galiojs/awesome-antd';
import moment from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import { Ellipsis, GutterWrapper, TinyTable } from '@manyun/dc-brain.legacy.components';
import {
  NOTICE_KEY_MAP,
  NOTICE_OPTIONS,
  NOTICE_TEXT_MAP,
} from '@manyun/dc-brain.legacy.pages/notice-manage/constants';
import {
  LEVEL_TEXT_MAP,
  LEVEL_TEXT_TYPE_MAP,
  RANGE_TYPE_TEXT_MAP,
} from '@manyun/dc-brain.legacy.pages/notice-manage/constants';
import {
  deleteNotice,
  fetchPublishNoticeListPage,
  fetchValidNoticeListPage,
  noticeActions,
  revokeNotice,
} from '@manyun/dc-brain.legacy.redux/actions/noticeActions';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';

import AddNotice from '../add-notice';
import EditNotice from '../edit-notice';

const { Option } = Select;
const NOTICE_CONTENT_DATA_INDEX = 'content';

const getColumns = ctx => {
  const columns = [
    {
      title: '公告标题',
      dataIndex: 'title',
      fixed: 'left',
      render: (text, record) => (
        <Link
          to={urls.generateNoticeDetailLocation({
            id: record.id,
          })}
        >
          <Ellipsis lines={1} tooltip>
            {text}
          </Ellipsis>
        </Link>
      ),
    },
    {
      title: '公告内容',
      dataIndex: NOTICE_CONTENT_DATA_INDEX,
      render: content => {
        return (
          <Ellipsis lines={1} tooltip>
            {content}
          </Ellipsis>
        );
      },
    },
    {
      title: '公告范围',
      dataIndex: 'rangeType',
      render: rangeType => RANGE_TYPE_TEXT_MAP[rangeType],
    },
    {
      title: '有效期限',
      dataIndex: 'validTime',
      render: (__, record) => (
        <span>
          {moment(record.validBeginDate).format('YYYY-MM-DD HH:mm:ss')}
          <span> ~ </span>
          {moment(record.validEndDate).format('YYYY-MM-DD HH:mm:ss')}
        </span>
      ),
      dataType: 'datetime',
    },
    {
      title: '重要程度',
      dataIndex: 'importance',
      render: (__, record) => {
        return (
          <Typography.Text type={LEVEL_TEXT_TYPE_MAP[record.importance]}>
            {LEVEL_TEXT_MAP[record.importance]}
          </Typography.Text>
        );
      },
    },
    {
      title: '更新时间',
      dataIndex: 'gmtModified',
      dataType: 'datetime',
    },
    {
      title: '更新人',
      dataIndex: 'modifierName',
    },
    {
      title: '状态',
      dataIndex: 'published',
      render: (__, record) => {
        const current = new Date().getTime();
        if (!record.published) {
          return <span>{NOTICE_TEXT_MAP[NOTICE_KEY_MAP.REVALID]}</span>;
        }
        if (current < record.validBeginDate) {
          return <span>{NOTICE_TEXT_MAP[NOTICE_KEY_MAP.UN_VALID]}</span>;
        }
        if (current > record.validEndDate) {
          return <span>{NOTICE_TEXT_MAP[NOTICE_KEY_MAP.INVALID]}</span>;
        } else {
          return <span>{NOTICE_TEXT_MAP[NOTICE_KEY_MAP.VALID]}</span>;
        }
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      width: 100,
      render: (__, record) => (
        <>
          {!record.published && (
            <Space align="center" size={0}>
              <Button type="link" compact onClick={() => ctx.editNotice(record)}>
                编辑
              </Button>
              <Divider type="vertical" />
              <Popconfirm
                title="确认删除吗？"
                onConfirm={() => ctx.deleteNotice(record)}
                placement="topRight"
              >
                <Button type="link" compact>
                  删除
                </Button>
              </Popconfirm>
            </Space>
          )}
          {record.published && (
            <span>
              <Popconfirm title="确认撤回吗？" onConfirm={() => ctx.revokeNotice(record)}>
                <Button type="link" compact>
                  撤回
                </Button>
              </Popconfirm>
            </span>
          )}
        </>
      ),
    },
  ];

  let noticeContentClmnIdx = -1;
  const otherClmnsWidth = columns.reduce((sum, { dataIndex, width }, idx) => {
    if (dataIndex === NOTICE_CONTENT_DATA_INDEX) {
      noticeContentClmnIdx = idx;
      return sum;
    }
    if (typeof width == 'number' && !Number.isNaN(width)) {
      return sum + width;
    }
    return sum;
  }, 0);

  if (noticeContentClmnIdx > -1) {
    const noticeContentWidth = `calc(100% - ${otherClmnsWidth}px)`;
    columns[noticeContentClmnIdx].width = noticeContentWidth;
  }

  return columns;
};

class DataTable extends Component {
  componentDidMount() {
    const { noticePageCondition } = this.props;
    this.props.fetchPublishNoticeListPage({
      ...noticePageCondition,
    });
  }

  onChangePage = (pageNo, pageSize) => {
    const { noticePageCondition } = this.props;
    this.props.fetchPublishNoticeListPage({
      ...noticePageCondition,
      pageNum: pageNo,
      pageSize,
    });
  };

  deleteNotice = row => {
    const noticeId = row.id;
    const { noticePageCondition } = this.props;
    this.props.deleteNotice({
      ...noticePageCondition,
      permissionId: noticeId,
    });
  };

  revokeNotice = row => {
    const noticeId = row.id;
    const { noticePageCondition } = this.props;
    this.props.revokeNotice({
      ...noticePageCondition,
      permissionId: noticeId,
    });
  };

  editNotice = row => {
    this.props.changeNoticeVisible();
    this.props.editNoticeMess(row);
  };

  onParamKey = paramKey => {
    this.props.saveSearchCondition({ status: paramKey });
    this.props.fetchPublishNoticeListPage({
      pageNum: 1,
      pageSize: 10,
      published: true,
      status: paramKey,
    });
  };

  render() {
    const { noticePage, loading, noticePageCondition } = this.props;
    return (
      <GutterWrapper mode="vertical">
        <TinyTable
          rowKey="id"
          columns={getColumns(this)}
          dataSource={noticePage.list}
          loading={loading}
          pagination={{
            total: noticePage.total,
            current: noticePageCondition.pageNum,
            pageSize: noticePageCondition.pageSize,
            onChange: this.onChangePage,
          }}
          scroll={{ x: 'max-content' }}
          actions={[
            <AddNotice key="add" />,
            <Select
              value={noticePageCondition.status}
              onChange={this.onParamKey}
              style={{ width: '200px' }}
              key="select"
            >
              {NOTICE_OPTIONS.map(item => (
                <Option key={item.value} value={item.value}>
                  {item.label}
                </Option>
              ))}
            </Select>,
          ]}
        />
        <EditNotice />
      </GutterWrapper>
    );
  }
}

const mapStateToProps = ({ noticeManage: { noticePage, loading, noticePageCondition } }) => ({
  noticePage,
  loading,
  noticePageCondition,
});
const mapDispatchToProps = {
  noticeActions,
  fetchValidNoticeListPage,
  fetchPublishNoticeListPage,
  deleteNotice,
  revokeNotice,
  getNoticeListSuccess: noticeActions.getNoticeListSuccess,
  changeNoticeVisible: noticeActions.changeNoticeVisible,
  editNoticeMess: noticeActions.editNoticeMess,
  saveSearchCondition: noticeActions.saveSearchCondition,
  success: noticeActions.success,
};

export default connect(mapStateToProps, mapDispatchToProps)(DataTable);
