import { STATUS_MAP } from '@manyun/dc-brain.legacy.constants/statusColor';

export const NOTICE_KEY_MAP = {
  ALL: 'ALL',
  VALID: 'VALID',
  UN_VALID: 'UN_VALID',
  INVALID: 'INVALID',
  REVALID: 'REVALID',
};
export const NOTICE_TEXT_MAP = {
  [NOTICE_KEY_MAP.ALL]: '全部',
  [NOTICE_KEY_MAP.VALID]: '生效中',
  [NOTICE_KEY_MAP.UN_VALID]: '未生效',
  [NOTICE_KEY_MAP.INVALID]: '已失效',
  [NOTICE_KEY_MAP.REVALID]: '已撤回',
};
export const NOTICE_OPTIONS = [
  { value: NOTICE_KEY_MAP.ALL, label: NOTICE_TEXT_MAP[NOTICE_KEY_MAP.ALL] },
  { value: NOTICE_KEY_MAP.VALID, label: NOTICE_TEXT_MAP[NOTICE_KEY_MAP.VALID] },
  { value: NOTICE_KEY_MAP.UN_VALID, label: NOTICE_TEXT_MAP[NOTICE_KEY_MAP.UN_VALID] },
  { value: NOTICE_KEY_MAP.REVALID, label: NOTICE_TEXT_MAP[NOTICE_KEY_MAP.REVALID] },
  { value: NOTICE_KEY_MAP.INVALID, label: NOTICE_TEXT_MAP[NOTICE_KEY_MAP.INVALID] },
];

export const LEVEL_KEY_MAP = {
  HIGH: 'HIGH',
  MID: 'MID',
  LOW: 'LOW',
};
export const LEVEL_TEXT_MAP = {
  [LEVEL_KEY_MAP.HIGH]: '重要',
  [LEVEL_KEY_MAP.MID]: '中等',
  [LEVEL_KEY_MAP.LOW]: '普通',
};
export const LEVEL_TEXT_TYPE_MAP = {
  [LEVEL_KEY_MAP.HIGH]: 'danger',
  [LEVEL_KEY_MAP.MID]: 'warning',
  [LEVEL_KEY_MAP.LOW]: 'success',
};
export const LEVEL_STATUS_MAP = {
  [LEVEL_KEY_MAP.HIGH]: STATUS_MAP.ALARM,
  [LEVEL_KEY_MAP.MID]: STATUS_MAP.WARNING,
  [LEVEL_KEY_MAP.LOW]: STATUS_MAP.NORMAL,
};

export const RANGE_TYPE_KEY_MAP = {
  ALL: 'ALL',
  PART: 'PART',
};
export const RANGE_TYPE_TEXT_MAP = {
  [RANGE_TYPE_KEY_MAP.ALL]: '全部',
  [RANGE_TYPE_KEY_MAP.PART]: '部分',
};
