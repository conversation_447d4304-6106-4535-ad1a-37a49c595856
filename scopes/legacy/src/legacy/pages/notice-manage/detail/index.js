import React, { Component } from 'react';
import { connect } from 'react-redux';

import moment from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Divider } from '@manyun/base-ui.ui.divider';
import { SimpleFileList } from '@manyun/base-ui.ui.file-list';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import { fetchBizFileInfos } from '@manyun/dc-brain.service.fetch-biz-file-infos';

import { fetchNoticeDetail } from '@manyun/dc-brain.legacy.redux/actions/noticeActions';

import { LEVEL_TEXT_MAP, NOTICE_TEXT_MAP, RANGE_TYPE_TEXT_MAP } from '../constants';

const cardHeight = `calc(var(--content-height) - 48px)`;

class NoticeDetail extends Component {
  state = {
    fileList: [],
  };
  componentDidMount() {
    const { match } = this.props;
    this.props.fetchNoticeDetail({ id: match.params.id });
    this.initialFileList(match.params.id);
  }
  initialFileList = async noticeId => {
    if (!noticeId) {
      return;
    }
    const { error, data } = await fetchBizFileInfos({
      targetId: noticeId,
      targetType: 'NOTICE',
    });
    if (error) {
      this.setState({ fileList: [] });
      return;
    }
    const fileList = data.data.map(data => ({ ...data, src: data.src, ext: data.ext }));
    this.setState({ fileList: fileList });
  };
  render() {
    const { noticeDetail } = this.props;
    const { fileList } = this.state;
    return (
      <Card bodyStyle={{ height: `calc(${cardHeight} + 48px)` }}>
        <div style={{ fontSize: 16, paddingBottom: 26, textAlign: 'center' }}>
          {noticeDetail.title}
        </div>
        <Descriptions column={4}>
          <Descriptions.Item label="更新人">{noticeDetail.modifierName}</Descriptions.Item>
          <Descriptions.Item label="更新时间">
            {moment(noticeDetail.gmtModified).format('YYYY-MM-DD HH:mm:ss')}
          </Descriptions.Item>
          <Descriptions.Item label="公告范围">
            {RANGE_TYPE_TEXT_MAP[noticeDetail.rangeType]}
          </Descriptions.Item>
          <Descriptions.Item label="重要程度">
            {LEVEL_TEXT_MAP[noticeDetail.importance]}
          </Descriptions.Item>
          <Descriptions.Item label="状态">{NOTICE_TEXT_MAP[noticeDetail.status]}</Descriptions.Item>
          <Descriptions.Item label="有效期限">
            <Typography.Text>
              {moment(noticeDetail.validBeginDate).format('YYYY-MM-DD HH:mm:ss')}~
              {moment(noticeDetail.validEndDate).format('YYYY-MM-DD HH:mm:ss')}
            </Typography.Text>
          </Descriptions.Item>
          <Descriptions.Item label="弹窗提醒">{noticeDetail.popUp ? '开' : '关'}</Descriptions.Item>
          {fileList.length > 0 && (
            <Descriptions.Item label="附件">
              <Space direction="vertical">
                <SimpleFileList files={fileList}>
                  <Button type="link" compact>
                    预览
                  </Button>
                </SimpleFileList>
              </Space>
            </Descriptions.Item>
          )}
        </Descriptions>
        <Divider />
        <Descriptions>
          <Descriptions.Item label="公告内容">{noticeDetail.content} </Descriptions.Item>
        </Descriptions>
      </Card>
    );
  }
}

const mapStateToProps = ({ noticeManage: { noticeDetail } }) => ({
  noticeDetail,
});
const mapDispatchToProps = {
  fetchNoticeDetail,
};

export default connect(mapStateToProps, mapDispatchToProps)(NoticeDetail);
