import moment from 'moment';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Link } from 'react-router-dom';

import { UserEmailInput } from '@manyun/auth-hub.ui.user-email-input';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import {
  CertificateTypeSelect,
  isIdCard,
  isPassport,
  isPermit,
} from '@manyun/base-ui.ui.certificate-type-select';
import { Container } from '@manyun/base-ui.ui.container';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { FileExport } from '@manyun/base-ui.ui.file-export';
// import type { ExportType } from '@manyun/base-ui.ui.file-export';
import { Form } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { QueryFilter } from '@manyun/base-ui.ui.query-filter';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import { getEntranceGuardCardManagementLocales } from '@manyun/sentry.model.entrance-guard-card-management';
import type {
  AuthInfo,
  CardStatus,
  EntranceGuardCardManagementJSON,
} from '@manyun/sentry.model.entrance-guard-card-management';
import { generateEntranceGuardCardManagementRoutePath } from '@manyun/sentry.route.routes';
import { createEntranceGuardCard } from '@manyun/sentry.service.create-entrance-guard-card';
import type { ApiQ as createApiQ } from '@manyun/sentry.service.create-entrance-guard-card';
import { deleteEntranceGuardCard } from '@manyun/sentry.service.delete-entrance-guard-card';
import type { ApiQ } from '@manyun/sentry.service.export-entrance-guard-card-status';
import { exportEntranceGuardCardStatus } from '@manyun/sentry.service.export-entrance-guard-card-status';
import type { SvcQuery } from '@manyun/sentry.service.fetch-entrance-guard-cards';
import { fetchEntranceGuardCards } from '@manyun/sentry.service.fetch-entrance-guard-cards';
import { syncEntranceGuardCard } from '@manyun/sentry.service.sync-entrance-guard-card';
import { updateEntranceGuardCard } from '@manyun/sentry.service.update-entrance-guard-card';
import { updateEntranceGuardCardStatus } from '@manyun/sentry.service.update-entrance-guard-card-status';
import { EntranceGuardCardAuthGroupsSelect } from '@manyun/ticket.ui.entrance-guard-card-auth-groups-select';

type SearchField = SvcQuery;
export type Action = 'add' | 'edit' | 'delete' | 'updateStatus' | 'sync';
export type EntranceGuardCardManagementsProps = {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  authorizedAreaElement: (props: any) => React.ReactElement;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  userSelectElement: (props: any) => React.ReactElement;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  userLinkElement: (props: any) => React.ReactElement;
};

export default function EntranceGuardCardManagements({
  authorizedAreaElement,
  userSelectElement,
  userLinkElement,
}: EntranceGuardCardManagementsProps) {
  const [form] = Form.useForm<SearchField>();
  const [modalForm] = Form.useForm<createApiQ>();
  const [editModalForm] = Form.useForm<EntranceGuardCardManagementJSON>();
  const cardType = Form.useWatch('cardType', editModalForm);
  const authInfoList = Form.useWatch('authInfoList', editModalForm);
  const email = Form.useWatch('email', editModalForm);
  const applyUser = Form.useWatch('applyUser', editModalForm);

  const [loading, setLoading] = useState(false);
  const [addVisible, setAddVisible] = useState(false);
  const [editVisible, setEditVisible] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [eidtId, setEidtId] = useState<number | null>(null);
  const [editInitUserInfo, setEditInitUserInfo] = useState<{
    label?: string;
    value?: number;
    id?: string | number;
    department?: string | null;
    company?: string | null;
    cardNo?: string | null;
    mobileNumber?: string | number | null;
    email?: string | null;
    cardType?: string | null;
  } | null>(null);

  const [data, setData] = useState<{
    data: EntranceGuardCardManagementJSON[];
    total: number;
  }>({
    data: [],
    total: 0,
  });

  const [fields, setFields] = useState<SearchField>({
    pageNum: 1,
    pageSize: 10,
  });
  const locales = useMemo(() => getEntranceGuardCardManagementLocales(), []);

  const getEntranceGuardCards = useCallback(async () => {
    setLoading(true);
    const { error, data } = await fetchEntranceGuardCards(fields);
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    setData(data);
  }, [fields]);

  const reloadData = useCallback((action: Action, total: number) => {
    if (action === 'delete') {
      setFields(pre => {
        return {
          ...pre,
          page:
            (pre.pageNum - 1) * pre.pageSize + 1 === total && pre.pageNum > 1
              ? pre.pageNum - 1
              : pre.pageNum,
        };
      });
    } else {
      setFields(pre => ({ ...pre }));
    }
  }, []);

  const onExport = async (type: string) => {
    let _params: ApiQ = {};
    if (type === 'selected') {
      _params.entranceCardNos = selectedRowKeys as number[];
    } else if (type === 'filtered') {
      _params = fields;
    }
    const { error, data } = await exportEntranceGuardCardStatus(_params);
    if (error) {
      message.error(error.message);
      return error.message;
    }
    return data;
  };

  const onAddEntranceGuardCard = () => {
    modalForm.validateFields().then(async values => {
      setLoading(true);
      const { error } = await createEntranceGuardCard(values);
      setLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      reloadData('add', data.total);
      message.success('添加成功！');
      setAddVisible(false);
      modalForm.resetFields();
    });
  };
  const onUpdateEntranceGuardCard = () => {
    editModalForm.validateFields().then(async values => {
      setLoading(true);
      if (!eidtId) {
        return;
      }
      const { error } = await updateEntranceGuardCard({
        ...values,
        id: eidtId,
      });
      setLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      reloadData('edit', data.total);
      message.success('编辑成功！');
      setEditVisible(false);
      setEidtId(null);
      setEditInitUserInfo(null);
      modalForm.resetFields();
    });
  };

  const updateCardStatus = async (record: EntranceGuardCardManagementJSON, status: string) => {
    setLoading(true);
    const { error } = await updateEntranceGuardCardStatus({
      id: record.id,
      status,
    });
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    reloadData('updateStatus', data.total);
  };

  const syncCard = async (id: number) => {
    setLoading(true);
    const { error } = await syncEntranceGuardCard({
      id,
    });
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    reloadData('sync', data.total);
  };

  const deleteCardStatus = async (record: EntranceGuardCardManagementJSON) => {
    setLoading(true);
    const { error } = await deleteEntranceGuardCard({
      id: record.id,
    });
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    reloadData('delete', data.total);
  };

  useEffect(() => {
    getEntranceGuardCards();
  }, [getEntranceGuardCards]);

  return (
    <>
      <Space style={{ width: '100%' }} direction="vertical">
        <Card>
          <QueryFilter<SearchField>
            form={form}
            items={[
              {
                label: '门禁卡编号',
                name: 'entranceCardNo',
                control: <Input aria-label="entranceCardNo" allowClear />,
              },
              {
                label: '持卡人',
                name: 'applyName',
                control: <Input aria-label="bizId" allowClear />,
              },
              {
                label: '状态',
                name: 'cardStatus',
                control: (
                  <Select
                    allowClear
                    options={[
                      { value: '1', label: locales.cardStatus.enable },
                      { value: '0', label: locales.cardStatus.disabled },
                    ]}
                  />
                ),
              },
              {
                label: '归属地',
                name: 'blockGuidList',
                getValueFromEvent: (value, _, { dataRef }) => {
                  let idcTags: string[] | undefined = [],
                    blockTags: string[] = [];
                  if (typeof dataRef === 'object') {
                    if (dataRef.type === 'IDC') {
                      if (dataRef.value) {
                        idcTags.push(dataRef.value);
                      }
                      if (dataRef.children) {
                        blockTags = dataRef.children.map((child: { value: string }) => child.value);
                      }
                    }
                    if (dataRef.type === 'BLOCK' && dataRef.value) {
                      blockTags.push(dataRef.value);
                      idcTags = undefined;
                    }
                  }
                  return {
                    value,
                    idcTags,
                    blockTags,
                  };
                },
                getValueProps: value => {
                  return { value: value?.value };
                },
                control: authorizedAreaElement({
                  dropdownMatchSelectWidth: 60,
                  listHeight: 400,
                  allowClear: true,
                  authorizedOnly: true,
                }),
              },
              {
                label: '授权位置',
                name: 'authBlockGuidList',
                getValueFromEvent: (value, _, { dataRef }) => {
                  let idcTags: string[] | undefined = [],
                    blockTags: string[] = [];
                  if (typeof dataRef === 'object') {
                    if (dataRef.type === 'IDC') {
                      if (dataRef.value) {
                        idcTags.push(dataRef.value);
                      }
                      if (dataRef.children) {
                        blockTags = dataRef.children.map((child: { value: string }) => child.value);
                      }
                    }
                    if (dataRef.type === 'BLOCK' && dataRef.value) {
                      blockTags.push(dataRef.value);
                      idcTags = undefined;
                    }
                  }
                  return {
                    value,
                    idcTags,
                    blockTags,
                  };
                },
                getValueProps: value => {
                  return { value: value?.value };
                },
                control: authorizedAreaElement({
                  dropdownMatchSelectWidth: 60,
                  listHeight: 400,
                  allowClear: true,
                  authorizedOnly: true,
                  showCheckedStrategy: 'SHOW_PARENT',
                }),
              },

              {
                label: '是否空卡',
                name: 'blankCard',
                control: (
                  <Select
                    allowClear
                    options={[
                      { value: true, label: '是' },
                      { value: false, label: '否' },
                    ]}
                  />
                ),
              },
            ]}
            defaultExpanded
            onSearch={() => {
              setFields({
                ...form.getFieldsValue(),
                blockGuidList: form.getFieldValue('blockGuidList')?.blockTags,
                authBlockGuidList: form.getFieldValue('authBlockGuidList')?.blockTags,
                pageNum: 1,
                pageSize: 10,
              });
            }}
            onReset={() => {
              form.resetFields();
              setFields({ pageNum: 1, pageSize: 10 });
            }}
          />
        </Card>
        <Card>
          <Space style={{ width: '100%' }} direction="vertical" size="middle">
            <Space
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                width: '100%',
              }}
            >
              <Button type="primary" onClick={() => setAddVisible(true)}>
                添加门禁卡
              </Button>
              <FileExport
                filename="门禁卡管理"
                data={type => {
                  return onExport(type);
                }}
                showExportFiltered
                showExportSelected
              />
            </Space>
            <Table
              rowKey="id"
              scroll={{ x: 'max-content' }}
              loading={loading}
              dataSource={data.data}
              rowSelection={{
                selectedRowKeys: selectedRowKeys,
                onChange: selectedRowKeys => {
                  setSelectedRowKeys(selectedRowKeys);
                },
              }}
              columns={[
                {
                  title: '门禁卡编号',
                  dataIndex: 'accessCardNo',
                  fixed: 'left',
                  render: (text: string) => {
                    if (text) {
                      const id = encodeURIComponent(text);
                      return (
                        <Link to={generateEntranceGuardCardManagementRoutePath(id)}>{text}</Link>
                      );
                    }
                    return '--';
                  },
                },
                {
                  title: '归属地',
                  dataIndex: 'attributionBlockGuid',
                },
                {
                  title: '授权位置',
                  dataIndex: 'authInfoList',
                  render: text =>
                    text ? (
                      <Space wrap size={0} split="|">
                        {text.map((auth: { authId: number; blockGuid: string }) => (
                          <span key={auth.authId}>{auth.blockGuid}</span>
                        ))}
                      </Space>
                    ) : (
                      '--'
                    ),
                },
                {
                  title: '持卡人',
                  dataIndex: 'applyName',
                  render: (value: string, record) =>
                    record.applyUser?.id
                      ? userLinkElement({
                          id: Number(record.applyUser?.id),
                          name: record.applyUser?.name,
                        })
                      : record.applyUser?.name,
                },
                {
                  title: '持卡人联系电话',
                  dataIndex: 'phone',
                  render: text => (text ? `${text.substring(0, 3)}****${text.substring(7)}` : '--'),
                },
                {
                  title: '持卡人部门',
                  dataIndex: 'dept',
                },
                {
                  title: '持卡人公司',
                  dataIndex: 'company',
                },
                {
                  title: '状态',
                  dataIndex: 'cardStatus',
                  render: (text: CardStatus) => (
                    <Tag color={text === 'disabled' ? 'error' : 'success'}>
                      {locales.cardStatus[text]}
                    </Tag>
                  ),
                },
                {
                  title: '操作',
                  dataIndex: '_action',
                  fixed: 'right',
                  render: (_, record: EntranceGuardCardManagementJSON) => {
                    return (
                      <Space size={16}>
                        {record.cardStatus === 'enable' && record.applyUser?.name && (
                          <Button
                            compact
                            type="link"
                            onClick={() => {
                              editModalForm.setFieldsValue({
                                ...record,
                                authInfoList: record.authInfoList?.map(
                                  ({ effectTime, ...rest }) => ({
                                    ...rest,
                                    effectTime: effectTime
                                      ? moment(effectTime.split('T')[0])
                                      : undefined,
                                  })
                                ),
                                applyUser: {
                                  ...record.applyUser,
                                  label: record.applyUser?.name,
                                  value: record.applyUser?.id ?? record.applyUser?.name,
                                  name: record.applyUser?.name ?? '',
                                  id: record.applyUser?.id ?? undefined,
                                  type: record.applyUser?.id ? null : 'external',
                                },
                              });
                              setEidtId(record.id);
                              setEditVisible(true);
                              setEditInitUserInfo({
                                department: record.dept,
                                mobileNumber: record.phone,
                                label: record.applyUser?.name,
                                value: record.applyUser?.id,
                                id: record.applyUser?.id,
                                company: record.company,
                                cardNo: record.cardNo,
                                email: record.email,
                                cardType: record.cardType,
                              });
                            }}
                          >
                            编辑
                          </Button>
                        )}
                        {record.applyUser?.name && (
                          <Button compact type="link" onClick={() => syncCard(record.id)}>
                            同步
                          </Button>
                        )}
                        {record.cardStatus === 'enable' && (
                          <Popconfirm
                            title="确认禁用吗？"
                            onConfirm={() => updateCardStatus(record, '0')}
                          >
                            <Button compact type="link">
                              禁用
                            </Button>
                          </Popconfirm>
                        )}
                        {record.cardStatus === 'disabled' && (
                          <Button compact type="link" onClick={() => updateCardStatus(record, '1')}>
                            启用
                          </Button>
                        )}
                        <Popconfirm title="确认删除？" onConfirm={() => deleteCardStatus(record)}>
                          <Button compact type="link">
                            删除
                          </Button>
                        </Popconfirm>
                      </Space>
                    );
                  },
                },
              ]}
              pagination={{
                total: data.total,
                current: fields.pageNum,
                pageSize: fields.pageSize,
              }}
              onChange={(pagination, _, __, { action }) => {
                if (action === 'paginate') {
                  setFields(pre => ({
                    ...pre,
                    pageNum: pagination.current!,
                    pageSize: pagination.pageSize!,
                  }));
                }
              }}
            />
          </Space>
        </Card>
      </Space>

      <Modal
        title="添加门禁卡"
        open={addVisible}
        width={834}
        confirmLoading={loading}
        destroyOnClose
        okText="确定"
        cancelText="取消"
        onOk={onAddEntranceGuardCard}
        onCancel={() => setAddVisible(false)}
      >
        <Form form={modalForm} labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
          <Row>
            <Col span={12}>
              <Form.Item
                label="门禁卡编号"
                name="entranceCardNo"
                rules={[
                  { required: true, message: '门禁卡编号必填！' },
                  { max: 20, message: '最多输入 20 个字符！' },
                ]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="归属地"
                name="blockGuid"
                rules={[{ required: true, message: '归属地必选！' }]}
              >
                {authorizedAreaElement({
                  dropdownMatchSelectWidth: 60,
                  listHeight: 400,
                  allowClear: true,
                  authorizedOnly: true,
                  disabledTypes: ['IDC'],
                })}
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
      <Modal
        title="编辑门禁卡"
        open={editVisible}
        width={1112}
        confirmLoading={loading}
        destroyOnClose
        okText="确定"
        bodyStyle={{ padding: 0, maxWidth: '85vw', maxHeight: '80vh', overflowY: 'auto' }}
        cancelText="取消"
        onOk={onUpdateEntranceGuardCard}
        onCancel={() => {
          setEditVisible(false);
          setEidtId(null);
          setEditInitUserInfo(null);
          editModalForm.resetFields();
        }}
      >
        <Form form={editModalForm} labelCol={{ span: 10 }} wrapperCol={{ span: 14 }}>
          <Space style={{ display: 'flex' }} direction="vertical">
            <Container style={{ padding: 24 }}>
              <Typography.Title showBadge level={5}>
                基本信息
              </Typography.Title>
              <Row>
                <Col span={8}>
                  <Form.Item
                    label="门禁卡编号"
                    name="accessCardNo"
                    rules={[
                      { required: true, message: '门禁卡编号必填！' },
                      { max: 20, message: '最多输入 20 个字符！' },
                    ]}
                  >
                    <Input disabled />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="归属地"
                    name="attributionBlockGuid"
                    rules={[{ required: true, message: '归属地必选！' }]}
                  >
                    {authorizedAreaElement({
                      dropdownMatchSelectWidth: 60,
                      listHeight: 400,
                      allowClear: true,
                      authorizedOnly: true,
                      disabledTypes: ['IDC'],
                    })}
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="持卡人"
                    name="applyUser"
                    rules={[{ required: true, message: '持卡人必选！' }]}
                  >
                    {userSelectElement({
                      labelInValue: true,
                      reserveSearchValue: true,
                      onChange: (value: {
                        department: string | null;
                        company: string | null;
                        mobileNumber: string | null;
                        email: string | null;
                        label: string;
                        value: number;
                        id: number;
                      }) => {
                        editModalForm.setFieldsValue({
                          dept: value.department ?? undefined,
                          company: value.company ?? undefined,
                          phone: value.mobileNumber
                            ? `${value.mobileNumber.substring(0, 3)}****${value.mobileNumber.substring(7)}`
                            : undefined,
                          email: value.email ?? undefined,
                          cardType: undefined,
                          cardNo: undefined,
                          applyUser: {
                            label: value.label,
                            value: value.value,
                            name: value.label,
                            id: value.id,
                          },
                        });
                        setEditInitUserInfo(value);
                      },
                    })}
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="持卡人部门"
                    name="dept"
                    rules={[{ required: true, message: '持卡人部门必填！' }]}
                  >
                    <Input
                      disabled={!!(applyUser?.id && editInitUserInfo?.department)}
                      maxLength={8}
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="持卡人公司"
                    name="company"
                    rules={[{ required: true, message: '持卡人公司必填！' }]}
                  >
                    <Input
                      disabled={!!(applyUser?.id && editInitUserInfo?.company)}
                      maxLength={16}
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="持卡人联系电话"
                    name="phone"
                    rules={
                      isSystemUserPhone({
                        id: applyUser?.id,
                        phone: editInitUserInfo?.mobileNumber,
                      })
                        ? [{ required: true, message: '持卡人联系电话必填！' }]
                        : [
                            { required: true, message: '持卡人联系电话必填！' },
                            {
                              pattern: /^1[3456789]\d{9}$/,
                              message: '联系电话格式不正确',
                            },
                          ]
                    }
                  >
                    <Input
                      disabled={isSystemUserPhone({
                        id: applyUser?.id,
                        phone: editInitUserInfo?.mobileNumber,
                      })}
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item<EntranceGuardCardManagementJSON>
                    label="电子邮箱"
                    name="email"
                    rules={
                      applyUser?.id && email
                        ? []
                        : [
                            {
                              type: 'email',
                              message: '请输入正确邮箱',
                            },
                            { max: 40, message: '最多输入 40 个字符！' },
                          ]
                    }
                    shouldUpdate={(prevValues, curValues) =>
                      prevValues.applyUser !== curValues.applyUser
                    }
                  >
                    {applyUser?.id && !email ? (
                      <UserEmailInput userId={editModalForm.getFieldValue('applyUser')?.id} />
                    ) : (
                      <Input disabled={!!(applyUser?.id && editInitUserInfo?.email)} />
                    )}
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item required label="持卡人证件">
                    <div style={{ display: 'flex' }}>
                      <Form.Item noStyle>
                        <Form.Item
                          name="cardType"
                          rules={[{ required: true, message: '证件必选！' }]}
                        >
                          <CertificateTypeSelect
                            hiddenTypes={['OTHER']}
                            disabled={!!(applyUser?.id && editInitUserInfo?.cardType)}
                            style={{ width: 115 }}
                            onChange={() => editModalForm.setFieldValue('cardNo', undefined)}
                          />
                        </Form.Item>
                      </Form.Item>
                      <Form.Item
                        name="cardNo"
                        rules={[
                          {
                            validator: async (_, cardNo) => {
                              if (!cardNo) {
                                return Promise.reject('证件编号必填！');
                              }
                              if (
                                (cardType === 'ID_CARD' && !isIdCard(cardNo)) ||
                                (cardType === 'PASSPORT' && !isPassport(cardNo)) ||
                                (cardType === 'PERMIT' && !isPermit(cardNo))
                              ) {
                                return Promise.reject('格式不正确');
                              } else {
                                return Promise.resolve();
                              }
                            },
                          },
                        ]}
                      >
                        <Input
                          disabled={!!(applyUser?.id && editInitUserInfo?.cardNo)}
                          style={{ width: 190 }}
                        />
                      </Form.Item>
                    </div>
                  </Form.Item>
                </Col>
              </Row>
              <Typography.Title showBadge level={5}>
                授权信息
              </Typography.Title>
              <Row>
                <Form.List name="authInfoList">
                  {fields => (
                    <>
                      {fields.map(({ key, name, ...restField }, index) => (
                        <>
                          <Col span={8}>
                            <Form.Item<AuthInfo>
                              {...restField}
                              // @ts-ignore pending fix
                              name={[name, 'blockGuid']}
                              label="授权位置"
                              required
                              shouldUpdate={(prevValues, curValues) =>
                                prevValues.blockGuid !== curValues.blockGuid ||
                                prevValues.authId !== curValues.authId ||
                                prevValues.effectTime !== curValues.effectTime
                              }
                            >
                              <Select disabled />
                            </Form.Item>
                          </Col>
                          <Col span={8}>
                            <Form.Item
                              {...restField}
                              label="门禁权限组角色"
                              name={[name, 'authId']}
                              rules={[{ required: true, message: '门禁权限组角色必选' }]}
                            >
                              <EntranceGuardCardAuthGroupsSelect
                                trigger="onDidMount"
                                blockGuid={authInfoList?.[index]?.blockGuid}
                              />
                            </Form.Item>
                          </Col>
                          <Col span={8}>
                            <Form.Item
                              label="门禁卡有效期限"
                              name={[name, 'effectTime']}
                              rules={[{ required: true, message: '门禁卡有效期限必选！' }]}
                            >
                              <DatePicker
                                style={{ width: 216 }}
                                format="YYYY-MM-DD 23:59:59"
                                disabledDate={current => {
                                  return (
                                    current && current < moment().subtract(1, 'day').endOf('day')
                                  );
                                }}
                              />
                            </Form.Item>
                          </Col>
                        </>
                      ))}
                    </>
                  )}
                </Form.List>
              </Row>
            </Container>
          </Space>
        </Form>
      </Modal>
    </>
  );
}

function isSystemUserPhone({ id, phone }: { id?: number; phone?: string | null | number }) {
  return id && phone ? true : false;
}
