import dayjs from 'dayjs';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Link, useParams } from 'react-router-dom';

import { UserEmailText } from '@manyun/auth-hub.ui.user-email-text';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { CERTIFICATE_TYPE_TEXT_MAP } from '@manyun/base-ui.ui.certificate-type-select';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Form } from '@manyun/base-ui.ui.form';
import type { FormInstance } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnsType } from '@manyun/base-ui.ui.table';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import { Tag } from '@manyun/base-ui.ui.tag';
import { getEntranceGuardCardManagementLocales } from '@manyun/sentry.model.entrance-guard-card-management';
import type {
  EntranceGuardCardManagementJSON,
  EntranceGuardCardManagementLocales,
} from '@manyun/sentry.model.entrance-guard-card-management';
import { generateEntranceGuardCardManagementRoutePath } from '@manyun/sentry.route.routes';
import { fetchEntranceGuardCard } from '@manyun/sentry.service.fetch-entrance-guard-card';
import { fetchEntranceGuardCardLogs } from '@manyun/sentry.service.fetch-entrance-guard-card-logs';
import type {
  BackendEntranceLog,
  OperateType,
} from '@manyun/sentry.service.fetch-entrance-guard-card-logs';
import {
  type BackendTrafficRecord,
  fetchTrafficRecords,
} from '@manyun/sentry.service.fetch-traffic-records';
import { generateTicketLocation } from '@manyun/ticket.route.ticket-routes';

import { EntranceGuardCardLicenseInformation } from '../../components/entrance-guard-card-license-information/entrance-guard-card-license-information.js';

export type EntranceGuardCardManagementProps = {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  userLinkElement: (props: any) => React.ReactElement;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  spaceTextElement: (props: any) => React.ReactElement;
};

const operationLogColumns = (
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  userLinkElement: (props: any) => React.ReactElement,
  locales: EntranceGuardCardManagementLocales
): ColumnsType<BackendEntranceLog> => [
  {
    title: '操作类型',
    dataIndex: 'operateType',
    width: 125,
    render: (text: OperateType) => locales.operationType[text],
  },
  {
    title: '操作内容',
    dataIndex: 'operateType',
    width: 157,
    render: (text: OperateType) => locales.operationContent[text],
  },
  {
    title: '操作备注',
    dataIndex: 'operateContent',
    render: (text, record) => {
      const listRegex = /\{\d+\}/g;
      const numberRegex = /\d+/g;
      const mainText = text.split(listRegex);
      if (!text.match(listRegex)?.length) {
        return text;
      }
      const numberList = text.match(listRegex)!.join().match(numberRegex)!;
      return numberList.map((item: string, index: number) => {
        const name = record.targetContentList[parseInt(item)]?.targetName;
        const id = record.targetContentList[parseInt(item)]?.targetId;
        const typeCode = record.targetContentList[parseInt(item)]?.targetType;
        if (index === numberList.length - 1) {
          return (
            <span key={id}>
              {mainText[index]}
              <SwitchLinkToUrl
                targetName={name}
                targetId={id}
                targetTypeCode={typeCode}
                userLinkElement={userLinkElement}
              />
              {mainText[index + 1]}
            </span>
          );
        }
        return (
          <span key={id}>
            {mainText[index]}
            <SwitchLinkToUrl
              targetName={name}
              targetId={id}
              targetTypeCode={typeCode}
              userLinkElement={userLinkElement}
            />
          </span>
        );
      });
    },
  },
  {
    title: '操作时间',
    dataIndex: 'gmtCreate',
    width: 252,
    render: text => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
  },
];
const traffiRecordsColumns = (
  userLinkElement: (props: unknown) => React.ReactElement,
  spaceTextElement: (props: unknown) => React.ReactElement
): ColumnsType<BackendTrafficRecord> => [
  {
    title: '机房楼栋',
    dataIndex: 'blockGuid',
    render: text => spaceTextElement({ guid: text }),
  },
  {
    title: '包间',
    dataIndex: 'roomGuid',
  },
  {
    title: '门点名称',
    dataIndex: 'doorName',
  },

  {
    title: '设备名称',
    dataIndex: 'deviceName',
  },
  {
    title: '持卡人',
    dataIndex: 'userName',
    render: (text, record) => (record.applyId ? userLinkElement({ id: record.applyId }) : text),
  },
  {
    title: '通行时间',
    dataIndex: 'passTime',
    render: text => dayjs(text).format('YYYY.MM.DD HH:mm:ss'),
  },
  {
    title: '通行状态',
    dataIndex: 'passState',
  },
  {
    title: '备注',
    dataIndex: 'remark',
  },
];
export default function EntranceGuardCardManagement({
  userLinkElement,
  spaceTextElement,
}: EntranceGuardCardManagementProps) {
  const { id } = useParams<{ id: string }>();
  const [traffiRecordsForm] = Form.useForm();
  const [operationLogForm] = Form.useForm();
  const locales = useMemo(() => getEntranceGuardCardManagementLocales(), []);

  const [state, setState] = useState<{
    loading: boolean;
    data: EntranceGuardCardManagementJSON | null;
  }>({
    loading: false,
    data: null,
  });

  const [traffiRecords, setTraffiRecords] = useState<{
    loading: boolean;
    data: BackendTrafficRecord[];
    total: number;
    pageNum: number;
    pageSize: number;
  }>({
    loading: false,
    data: [],
    total: 0,
    pageNum: 1,
    pageSize: 10,
  });

  const [operationLog, setOperationLog] = useState<{
    loading: boolean;
    data: BackendEntranceLog[];
    total: number;
    pageNum: number;
    pageSize: number;
  }>({
    loading: false,
    data: [],
    total: 0,
    pageNum: 1,
    pageSize: 10,
  });
  const loadData = useCallback(async () => {
    setState(pre => ({ ...pre, loading: true }));
    const { error, data } = await fetchEntranceGuardCard({
      entranceCardNo: decodeURIComponent(id),
    });
    setState(pre => ({ ...pre, loading: false }));
    if (error) {
      message.error(error.message);
      return;
    }

    if (data) {
      setState(pre => ({ ...pre, data }));
    }
  }, [id, setState]);

  const getTraffiRecords = useCallback(async () => {
    setTraffiRecords(pre => ({ ...pre, loading: true }));
    const [startPassTime, endTime] = traffiRecordsForm.getFieldValue('time') ?? [];
    const startDate = startPassTime ? dayjs(startPassTime).startOf('day').valueOf() : null;
    const endDate = endTime ? dayjs(startPassTime).endOf('day').valueOf() : null;
    const { error, data } = await fetchTrafficRecords({
      entranceCardNo: decodeURIComponent(id),
      pageNum: traffiRecords.pageNum,
      pageSize: traffiRecords.pageSize,
      startPassTime: startDate,
      endTime: endDate,
    });
    setTraffiRecords(pre => ({ ...pre, loading: false }));
    if (error) {
      message.error(error.message);
      return;
    }

    if (data) {
      setTraffiRecords(pre => ({ ...pre, ...data }));
    }
  }, [id, traffiRecords.pageNum, traffiRecords.pageSize, traffiRecordsForm]);

  const getOperationLog = useCallback(async () => {
    setOperationLog(pre => ({ ...pre, loading: true }));
    const [startTime, endTime] = operationLogForm.getFieldValue('time') ?? [];
    const startDate = startTime ? dayjs(startTime).startOf('day').valueOf() : null;
    const endDate = endTime ? dayjs(endTime).endOf('day').valueOf() : null;
    const { error, data } = await fetchEntranceGuardCardLogs({
      entranceCardNo: decodeURIComponent(id),
      pageNum: operationLog.pageNum,
      pageSize: operationLog.pageSize,
      startTime: startDate,
      endTime: endDate,
    });
    setOperationLog(pre => ({ ...pre, loading: false }));
    if (error) {
      message.error(error.message);
      return;
    }

    if (data) {
      setOperationLog(pre => ({ ...pre, ...data }));
    }
  }, [id, operationLog.pageNum, operationLog.pageSize, operationLogForm]);

  useEffect(() => {
    loadData();
    getTraffiRecords();
    getOperationLog();
  }, [loadData, getTraffiRecords, getOperationLog]);

  const descriptionItems = useMemo(() => {
    return [
      {
        label: '门禁卡编号',
        value: state.data?.accessCardNo,
      },
      {
        label: '归属地',
        value: state.data?.attributionBlockGuid,
      },
      {
        label: '状态',
        value: state.data?.cardStatus ? (
          <Tag color={state.data?.cardStatus === 'disabled' ? 'error' : 'success'}>
            {locales.cardStatus[state.data?.cardStatus]}
          </Tag>
        ) : (
          '--'
        ),
      },
      {
        label: '持卡人',
        value: state.data?.applyUser?.id
          ? userLinkElement({ id: state.data?.applyUser?.id })
          : state.data?.applyUser?.name,
      },
      {
        label: '持卡人联系电话',
        value: state.data?.phone ?? '--',
      },
      {
        label: '持卡人邮箱',
        value: state.data?.email ? (
          <UserEmailText email={state.data?.email} applyId={state.data.applyUser?.id} />
        ) : (
          '--'
        ),
      },
      {
        label: '持卡人部门',
        value: state.data?.dept,
      },
      {
        label: '持卡人公司',
        value: state.data?.company ?? '--',
      },
      {
        label: '持卡人证件',
        value: state.data?.cardType
          ? `${CERTIFICATE_TYPE_TEXT_MAP[state.data.cardType]}-${state.data?.cardNo}`
          : '--',
      },
    ] as {
      label: string;
      value: React.ReactNode;
    }[];
  }, [state.data, locales.cardStatus, userLinkElement]);

  return (
    <>
      <Space direction="vertical" style={{ display: 'flex' }} size="middle">
        <Card title="基本信息" loading={state.loading}>
          <Descriptions column={4}>
            {descriptionItems.map(item => (
              <Descriptions.Item key={item.label} label={item.label}>
                {item.value}
              </Descriptions.Item>
            ))}
          </Descriptions>
        </Card>
        <Card title="授权信息" loading={state.loading}>
          <EntranceGuardCardLicenseInformation
            idcTag={
              state.data?.attributionBlockGuid ? state.data?.attributionBlockGuid.split('.')[0] : ''
            }
            authInfoList={state.data?.authInfoList ?? []}
            showExpire
          />
        </Card>
        <Card>
          <Tabs defaultActiveKey="traffi-records">
            <Tabs.TabPane key="traffi-records" tab="通行记录">
              <Space style={{ width: '100%' }} direction="vertical" size="middle">
                <FiltersForm
                  loading={traffiRecords.loading}
                  form={traffiRecordsForm}
                  labelText="通行时间"
                  onSearch={() => {
                    if (traffiRecords.pageNum === 1) {
                      getTraffiRecords();
                    } else {
                      setTraffiRecords(prev => ({ ...prev, pageNum: 1 }));
                    }
                  }}
                  onReset={() => {
                    if (traffiRecords.pageNum === 1 && traffiRecords.pageSize === 10) {
                      getTraffiRecords();
                    } else {
                      setTraffiRecords(prev => ({ ...prev, pageNum: 1, pageSize: 10 }));
                    }
                  }}
                />
                <Table
                  rowKey="id"
                  loading={traffiRecords.loading}
                  columns={traffiRecordsColumns(userLinkElement, spaceTextElement)}
                  dataSource={traffiRecords.data}
                  pagination={{
                    total: traffiRecords.total,
                    current: traffiRecords.pageNum,
                    pageSize: traffiRecords.pageSize,
                  }}
                  onChange={pagination => {
                    setTraffiRecords(prev => ({
                      ...prev,
                      pageNum: pagination.current!,
                      pageSize: pagination.pageSize!,
                    }));
                  }}
                />
              </Space>
            </Tabs.TabPane>
            <Tabs.TabPane key="operation-records" tab="操作记录">
              <Space style={{ width: '100%' }} direction="vertical" size="middle">
                <FiltersForm
                  loading={operationLog.loading}
                  form={operationLogForm}
                  labelText="操作时间"
                  onSearch={() => {
                    if (operationLog.pageNum === 1) {
                      getOperationLog();
                    } else {
                      setOperationLog(prev => ({ ...prev, pageNum: 1 }));
                    }
                  }}
                  onReset={() => {
                    if (operationLog.pageNum === 1 && operationLog.pageSize === 10) {
                      getOperationLog();
                    } else {
                      setOperationLog(prev => ({ ...prev, pageNum: 1, pageSize: 10 }));
                    }
                  }}
                />
                <Table
                  rowKey="id"
                  loading={operationLog.loading}
                  columns={operationLogColumns(userLinkElement, locales)}
                  dataSource={operationLog.data}
                  pagination={{
                    total: operationLog.total,
                    current: operationLog.pageNum,
                    pageSize: operationLog.pageSize,
                  }}
                  onChange={pagination => {
                    setOperationLog(prev => ({
                      ...prev,
                      pageNum: pagination.current!,
                      pageSize: pagination.pageSize!,
                    }));
                  }}
                />
              </Space>
            </Tabs.TabPane>
          </Tabs>
        </Card>
      </Space>
    </>
  );
}

type FiltersFormProps = {
  loading: boolean;
  form: FormInstance<{ time?: [number, number] }>;
  labelText: string;
  onSearch(values: { time?: [number, number] }): void;
  onReset(): void;
};

function FiltersForm({ loading, form, labelText, onSearch, onReset }: FiltersFormProps) {
  return (
    <Form
      layout="inline"
      form={form}
      onFinish={onSearch}
      onReset={e => {
        e.preventDefault();
        e.stopPropagation();
        onReset();
      }}
    >
      <Form.Item name="time" label={labelText}>
        {/* @ts-ignore because of TS2786*/}
        <DatePicker.RangePicker style={{ width: '100%' }} allowClear />
      </Form.Item>
      <Form.Item>
        <Space>
          <Button type="primary" htmlType="submit" loading={loading}>
            搜索
          </Button>
          <Button htmlType="reset" disabled={loading}>
            重置
          </Button>
        </Space>
      </Form.Item>
    </Form>
  );
}

type SwitchLinkToUrlProps = {
  targetName: string;
  targetId: string;
  targetTypeCode: TargetTypeCode;
  userLinkElement: (props: unknown) => React.ReactElement;
};
type TargetTypeCode = 'PERSON' | 'ENTRANCE_CARD' | 'ACCESS_CARD_AUTH';

function SwitchLinkToUrl(props: SwitchLinkToUrlProps) {
  const { targetName, targetId, targetTypeCode, userLinkElement } = props;
  switch (targetTypeCode) {
    case 'PERSON':
      return userLinkElement({ id: Number(targetId), name: targetName });
    case 'ENTRANCE_CARD':
      return <Link to={generateEntranceGuardCardManagementRoutePath(targetId)}>{targetName}</Link>;
    case 'ACCESS_CARD_AUTH':
      return (
        <Link
          to={generateTicketLocation({
            ticketType: targetTypeCode.toLowerCase(),
            id: targetId,
          })}
        >
          {targetName}
        </Link>
      );
    default:
      return <>{targetName}</>;
  }
}
