import { FiltersForm, Form, Select } from '@galiojs/awesome-antd';
import trim from 'lodash/trim';
import React, { useEffect } from 'react';
import { connect, useDispatch } from 'react-redux';
import { Link } from 'react-router-dom';

// Deprecated, replace with "useAuthorized" hook
import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Input } from '@manyun/base-ui.ui.input';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import { getLocationSearchMap, setLocationSearch } from '@manyun/base-ui.util.query-string';
import { BackendTaskStatus } from '@manyun/ticket.model.ticket';
import { Resend } from '@manyun/ticket.ui.resend';
import { TicketStatusText } from '@manyun/ticket.ui.ticket-status-text';
import { TicketTypeSelect } from '@manyun/ticket.ui.ticket-type-select';
import { TicketSlaText } from '@manyun/ticket.ui.tickets-table';

import { LocationCascader, TinyTable } from '@manyun/dc-brain.legacy.components';
import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  endAction,
  getTicketListActionCreator,
  mineTicketActions,
  takeOverAction,
} from '@manyun/dc-brain.legacy.redux/actions/mineTicketActions';
import { revokeAction } from '@manyun/dc-brain.legacy.redux/actions/ticketActions';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';
import { generateTicketEditUrl } from '@manyun/dc-brain.legacy.utils/urls';

import {
  INITIATOR_TAB_KEY_MAP,
  INITIATOR_TAB_LIST,
  TICKET_IS_DELAY_MAP,
  getLocationMenuKeyBySubTab,
  getLocationSubTabKey,
  getSubTabKeyValue,
} from '../constants';

const unFinishedStatus = [
  BackendTaskStatus.PROCESSING,
  BackendTaskStatus.WAITTAKEOVER,
  BackendTaskStatus.INIT,
  BackendTaskStatus.UNDO,
  BackendTaskStatus.CLOSE_APPROVER,
];
const taskStatusList = taskStatus =>
  taskStatus === INITIATOR_TAB_KEY_MAP.FINISHED
    ? [BackendTaskStatus.FAILURE, BackendTaskStatus.FINISH]
    : unFinishedStatus;

function MineInitatorTicket({
  ticketList,
  taskStatus,
  getMineTicketList,
  changeStatus,
  searchValues,
  updateSearchValues,
  syncCommonData,
  ticketTypeList,
  pagination,
  updatePagination,
  ticketTypes,
  resetPagination,
  resetSearchValues,
  revokeAction,
}) {
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const { menuKey, subTabKey } = getLocationSearchMap(window.location.search);
  useEffect(() => {
    if (subTabKey) {
      changeStatus(getSubTabKeyValue(subTabKey));
    }
  }, [subTabKey, changeStatus]);
  useEffect(() => {
    getMineTicketList({
      taskStatusList: taskStatusList(taskStatus),
      openInitWithdraw: taskStatus !== INITIATOR_TAB_KEY_MAP.FINISHED,
      initiator: true,
    });
    syncCommonData({ strategy: { ticketTypes: 'IF_NULL', space: 'FORCED' } });
  }, [taskStatus, getMineTicketList, syncCommonData]);

  const params = Object.keys(searchValues).reduce((map, fieldName) => {
    const value = searchValues[fieldName]?.value;
    if ([null, undefined, ''].includes(value) || (Array.isArray(value) && value.length === 0)) {
      return map;
    } else if (fieldName === 'localtion') {
      map.idcTag = value[0];
      map.blockTag = value.length === 2 ? value.join('.') : null;
    } else if (fieldName === 'gmtCreate') {
      map.startTime = value[0].valueOf();
      map.endTime = value[1].valueOf();
    } else if (fieldName === 'taskNo') {
      map[fieldName] = trim(value);
    } else if (fieldName === 'taskType' && value) {
      map.taskType = value[0];
      map.taskSubType = value[1];
    } else if (fieldName === 'endTime') {
      map.checkStartTime = value[0].valueOf();
      map.checkEndTime = value[1].valueOf();
    } else {
      map[fieldName] = value;
    }
    return map;
  }, {});

  const searchRoomList = (params, taskStatus, getMineTicketList) => {
    getMineTicketList({
      ...params,
      taskStatusList: taskStatusList(taskStatus),
      openInitWithdraw: taskStatus !== INITIATOR_TAB_KEY_MAP.FINISHED,
      initiator: true,
      pageNum: 1,
      pageSize: 10,
    });
  };

  const revoke = (taskNo, taskType) => {
    new Promise(() => {
      revokeAction({
        taskNo,
        type: taskType,
        successCallback: () => {
          resetPagination();
          getMineTicketList({
            taskStatusList: unFinishedStatus,
            openInitWithdraw: true,
            initiator: true,
            pageNum: 1,
            pageSize: 10,
          });
        },
      });
    });
  };

  const onChangePageSize = (page, params, taskStatus, getMineTicketList) => {
    getMineTicketList({
      ...params,
      pageNum: page.current,
      pageSize: page.pageSize,
      taskStatusList: taskStatusList(taskStatus),
      openInitWithdraw: taskStatus !== INITIATOR_TAB_KEY_MAP.FINISHED,
      initiator: true,
    });
    updatePagination({
      pageNum: page.current,
      pageSize: page.pageSize,
    });
  };

  const onTabChange = key => {
    changeStatus(key);
    resetPagination();
    setLocationSearch({
      menuKey: menuKey ?? getLocationMenuKeyBySubTab(key),
      subTabKey: getLocationSubTabKey(key),
    });
    updateSearchValues({
      localtion: {
        value: null,
      },
      taskNo: {
        value: null,
      },
      isDelay: {
        value: null,
      },
      gmtCreate: {
        value: null,
      },
      taskType: {
        value: null,
      },
      endTime: {
        value: null,
      },
    });
  };

  const getItems = taskStatus => {
    let items = [];
    if (taskStatus === INITIATOR_TAB_KEY_MAP.FINISHED) {
      items = [
        {
          label: '位置',
          name: 'localtion',
          control: <LocationCascader style={{ width: 200 }} currentAuthorize />,
        },
        {
          label: '工单类型',
          name: 'taskType',
          control: <TicketTypeSelect />,
        },
        {
          label: '工单单号',
          name: 'taskNo',
          control: <Input allowClear />,
        },
        {
          label: '是否超时',
          name: 'isDelay',
          control: (
            <Select key="1" allowClear>
              {TICKET_IS_DELAY_MAP.map(item => {
                return (
                  <Select.Option key={item.value} value={item.value}>
                    {item.label}
                  </Select.Option>
                );
              })}
            </Select>
          ),
        },
        {
          label: '创建时间',
          name: 'gmtCreate',
          span: 2,
          control: (
            <DatePicker.RangePicker
              format="YYYY-MM-DD HH:mm:ss"
              showTime
              placeholder={['开始时间', '结束时间']}
            />
          ),
        },
        {
          label: '关单时间',
          name: 'endTime',
          span: 2,
          control: (
            <DatePicker.RangePicker
              format="YYYY-MM-DD HH:mm:ss"
              showTime
              placeholder={['开始时间', '结束时间']}
            />
          ),
        },
      ];
    } else {
      items = [
        {
          label: '位置',
          name: 'localtion',
          control: <LocationCascader style={{ width: 200 }} currentAuthorize />,
        },
        {
          label: '工单类型',
          name: 'taskType',
          control: <TicketTypeSelect />,
        },
        {
          label: '工单单号',
          name: 'taskNo',
          control: <Input allowClear />,
        },
        {
          label: '是否超时',
          name: 'isDelay',
          control: (
            <Select key="1" allowClear>
              {TICKET_IS_DELAY_MAP.map(item => {
                return (
                  <Select.Option key={item.value} value={item.value}>
                    {item.label}
                  </Select.Option>
                );
              })}
            </Select>
          ),
        },
        {
          label: '创建时间',
          name: 'gmtCreate',
          span: 2,
          control: (
            <DatePicker.RangePicker
              format="YYYY-MM-DD HH:mm:ss"
              showTime
              placeholder={['开始时间', '结束时间']}
            />
          ),
        },
      ];
    }
    return items;
  };
  const getList = () => {
    resetPagination();
    getMineTicketList({
      taskStatusList: unFinishedStatus,
      openInitWithdraw: true,
      initiator: true,
      pageNum: 1,
      pageSize: 10,
    });
  };
  const tabs = INITIATOR_TAB_LIST.map(item => (
    <Tabs.TabPane key={item.key} tab={item.name}>
      <FiltersForm
        form={form}
        items={getItems(item.key)}
        fields={Object.keys(searchValues).map(name => {
          const field = searchValues[name];
          return {
            ...field,
            name: name.split('.'),
          };
        })}
        onFieldsChange={changedFields => {
          updateSearchValues(
            changedFields.reduce((mapper, field) => {
              const name = field.name.join('.');
              mapper[name] = {
                ...field,
                name,
              };

              return mapper;
            }, {})
          );
        }}
        onSearch={() => {
          resetPagination();
          searchRoomList(params, item.key, getMineTicketList);
        }}
        onReset={() => {
          updateSearchValues({
            localtion: {
              value: null,
            },
            taskNo: {
              value: null,
            },
            isDelay: {
              value: null,
            },
            gmtCreate: {
              value: null,
            },
          });
          resetPagination();
          dispatch(resetSearchValues());
          getMineTicketList({
            taskStatusList: taskStatusList(taskStatus),
            initiator: true,
            openInitWithdraw: taskStatus !== INITIATOR_TAB_KEY_MAP.FINISHED,
            pageNum: 1,
            pageSize: 10,
          });
        }}
      />

      <TinyTable
        rowKey="taskNo"
        align="left"
        scroll={{ x: 'max-content' }}
        columns={columns(ticketTypeList, taskStatus, revoke, getList)}
        loading={ticketList.fetching}
        dataSource={ticketList.data}
        pagination={{
          total: ticketList.total,
          current: pagination.pageNum,
          pageSize: pagination.pageSize,
        }}
        onChange={page => onChangePageSize(page, params, item.key, getMineTicketList)}
      />
    </Tabs.TabPane>
  ));

  return (
    <Tabs activeKey={taskStatus} onChange={onTabChange}>
      {tabs}
    </Tabs>
  );
}

const mapStateToProps = ({
  mineticket: { ticketList, searchValues, taskStatus, pagination },
  common: { ticketTypes },
}) => {
  return {
    ticketList,
    searchValues,
    ticketTypeList: ticketTypes?.normalizedList || [],
    taskStatus,
    pagination,
    ticketTypes,
  };
};

const mapDispatchToProps = {
  getMineTicketList: getTicketListActionCreator,
  changeStatus: mineTicketActions.changeStatus,
  takeOverTask: takeOverAction,
  syncCommonData: syncCommonDataActionCreator,
  endTask: endAction,
  updateSearchValues: mineTicketActions.updateSearchValues,
  updatePagination: mineTicketActions.updatePagination,
  resetPagination: mineTicketActions.resetPagination,
  resetSearchValues: mineTicketActions.resetSearchValues,
  revokeAction,
};

export default connect(mapStateToProps, mapDispatchToProps)(MineInitatorTicket);

const columns = (ticketTypeList, taskStatus, revoke, getList) => [
  {
    title: '工单单号',
    dataIndex: 'taskNo',
    fixed: true,
    dataType: {
      type: 'link',
      options: {
        to(text, record) {
          return urls.generateTicketDetailLocation({
            id: text,
            ticketType: record.taskType.toLowerCase(),
            idcTag: record.idcTag,
            blockTag: record.blockTag,
          });
        },
      },
    },
  },
  {
    title: '工单类型',
    dataIndex: 'taskType',
    render: text => ticketTypeList?.[text]?.taskValue,
  },
  {
    title: '工单子类型',
    dataIndex: 'taskSubType',
    render: (text, { taskType, taskProperties }) => {
      return taskType === 'IT_SERVICE'
        ? `${ticketTypeList?.[text]?.taskValue}/${JSON.parse(taskProperties)?.thirdOrderTypeName}`
        : ticketTypeList?.[text]?.taskValue;
    },
  },
  {
    title: '工单标题',
    dataIndex: 'taskTitle',
  },
  {
    title: '位置',
    dataIndex: 'blockTag',
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    dataType: 'datetime',
  },
  {
    title: '关单时间',
    dataIndex: 'endTime',
    dataType: 'datetime',
    visible: taskStatus === INITIATOR_TAB_KEY_MAP.FINISHED,
  },
  {
    title: '响应时效',
    dataIndex: 'responseSla',
    render: (_, record) => (
      <TicketSlaText
        taskSla={record.responseSla}
        delay={record.currentResponse}
        unit="SECOND"
        effectTime={null}
        endTime={null}
        shouldLimitShow
      />
    ),
  },
  {
    title: 'SLA时效',
    dataIndex: 'taskSla',
    render: (text, record) => (
      <TicketSlaText
        taskSla={text}
        delay={record.delay}
        unit="SECOND"
        effectTime={record.effectTime}
        endTime={record.endTime}
        shouldLimitShow
      />
    ),
  },
  {
    title: '工单状态',
    dataIndex: 'taskStatus',
    render: text => <TicketStatusText status={text} />,
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    visible: taskStatus !== INITIATOR_TAB_KEY_MAP.FINISHED,
    render: (_, record) => {
      if (
        record.taskStatus === BackendTaskStatus.INIT ||
        // 只有维修工单的关单审批能够“撤回”，后续可能会增加关单审批的工单类型，目前仅这样实现
        (record.taskStatus === BackendTaskStatus.CLOSE_APPROVER &&
          (record.taskType.toLowerCase() === 'repair' ||
            record.taskType.toLowerCase() === 'maintenance'))
      ) {
        return <AuthorizeUser record={record} revoke={revoke} type="revoke" />;
      }
      if (record.taskStatus === BackendTaskStatus.UNDO) {
        return <AuthorizeUser record={record} revoke={revoke} type="create" />;
      }
      if (record?.assigneeList?.length && record.taskStatus === BackendTaskStatus.WAITTAKEOVER) {
        return (
          <Resend
            compact
            assigneeId={record.assigneeList.map(item => item.id)}
            style={{ padding: 0, height: 'auto' }}
            btnType="link"
            taskNo={record.taskNo}
            taskType={record.taskType}
            blockGuid={record.blockTag}
            onResend={result => result && getList()}
          />
        );
      }
      return BLANK_PLACEHOLDER;
    },
  },
];

function AuthorizeUser({ type, record, revoke }) {
  const [authorize] = useAuthorized({ checkByUserId: record.creatorId });
  if (type === 'revoke' && authorize) {
    return (
      <Button
        key="revoke"
        type="link"
        compact
        onClick={() => {
          Modal.confirm({
            title: '确认要撤回' + record.taskNo + '的工单吗？',
            content: '撤回后数据将不可恢复。',
            okText: '确认',
            cancelText: '取消',
            onOk() {
              revoke(record.taskNo, record.taskType);
            },
          });
        }}
      >
        撤回
      </Button>
    );
  }
  if (type === 'create' && authorize) {
    return (
      <Link
        to={generateTicketEditUrl({
          ticketType: record.taskType.toLowerCase(),
          id: record.taskNo,
        })}
      >
        重新发起
      </Link>
    );
  }

  return BLANK_PLACEHOLDER;
}
