import React from 'react';
import { Link } from 'react-router-dom';

import { Space } from '@manyun/base-ui.ui.space';
import { Tag } from '@manyun/base-ui.ui.tag';

import { MineTicketSubTabKeyMap } from '@manyun/ticket.model.ticket';
import { generateTicketLocation } from '@manyun/ticket.route.ticket-routes';
import { TicketSlaText } from '@manyun/ticket.ui.tickets-table';

import { UserLink } from '@manyun/dc-brain.legacy.components';

import { TICKET_STATUS_KEY_TEXT_MAP } from '../../ticket/constants';

export const TICKET_IS_DELAY_MAP = [
  {
    value: '0',
    label: '否',
  },
  {
    value: '1',
    label: '是',
  },
];

export const PROCESSED_TAB_LIST = [
  {
    name: '成功关单',
    key: '1',
  },
  {
    name: '失败关单',
    key: '0',
  },
];

export const PENDING_TAB_LIST = [
  {
    name: '待接单',
    key: '3',
  },
  {
    name: '已接单',
    key: '2',
  },
];

export const INITIATOR_TAB_KEY_MAP = {
  UNFINISHED: 'UNFINISHED',
  FINISHED: 'FINISHED',
};

export const INITIATOR_TAB_LIST = [
  {
    name: '未关单',
    key: INITIATOR_TAB_KEY_MAP.UNFINISHED,
  },
  {
    name: '已关单',
    key: INITIATOR_TAB_KEY_MAP.FINISHED,
  },
];

export const FORM_ITEM_LAYOUT = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};

export const PROCESSED_COLUMS = ticketTypeList => [
  {
    title: '工单单号',
    dataIndex: 'taskNo',
    fixed: true,
    render: (text, record) => (
      <Space>
        <Link
          to={generateTicketLocation({
            id: text,
            ticketType: record.taskType.toLowerCase(),
            idcTag: record.idcTag,
            blockTag: record.blockTag,
          })}
        >
          {text}
        </Link>
        {record.sourceOfOrder === 'CUSTOMER' && <Tag color="blue">客户</Tag>}
      </Space>
    ),
  },
  {
    title: '工单类型',
    dataIndex: 'taskType',
    render: text => ticketTypeList?.[text]?.taskValue,
  },
  {
    title: '工单子类型',
    dataIndex: 'taskSubType',
    render: (text, { taskType, taskProperties }) => {
      return taskType === 'IT_SERVICE'
        ? `${ticketTypeList?.[text]?.taskValue}/${JSON.parse(taskProperties)?.thirdOrderTypeName}`
        : ticketTypeList?.[text]?.taskValue;
    },
  },
  {
    title: '工单标题',
    dataIndex: 'taskTitle',
  },
  {
    title: '位置',
    dataIndex: 'blockTag',
  },
  {
    title: '提单人',
    dataIndex: 'creatorName',
    render: (text, record) =>
      record.outerCreatorName ? (
        record.outerCreatorName
      ) : (
        <UserLink userId={record.creatorId} userName={text} />
      ),
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    dataType: 'datetime',
  },
  {
    title: '关单时间',
    dataIndex: 'endTime',
    dataType: 'datetime',
  },
  {
    title: '响应时效',
    dataIndex: 'responseSla',
    render: (_, record) => (
      <TicketSlaText
        taskSla={record.responseSla}
        delay={record.currentResponse}
        effectTime={null}
        endTime={null}
        shouldLimitShow
      />
    ),
  },
  {
    title: 'SLA时效',
    dataIndex: 'taskSla',
    render: (text, record) => (
      <TicketSlaText
        taskSla={text}
        delay={record.delay}
        unit="SECOND"
        effectTime={record.effectTime}
        endTime={record.endTime}
        shouldLimitShow
      />
    ),
  },
  {
    title: '工单状态',
    dataIndex: 'taskStatus',
    render: text => TICKET_STATUS_KEY_TEXT_MAP[text],
  },
];
export function getSubTabKeyValue(key) {
  if (key) {
    return MineTicketSubTabKeyMap[key];
  } else {
    return MineTicketSubTabKeyMap['awaiting'];
  }
}
export function getLocationSubTabKey(value) {
  switch (value) {
    case '2':
      return 'taking';
    case '1':
      return 'success-completed';
    case '0':
      return 'fail-completed';
    case 'UNFINISHED':
      return 'uncompleted';
    case 'FINISHED':
      return 'completed';
    case '3':
    default:
      return 'awaiting';
  }
}

export function getLocationMenuKeyBySubTab(value) {
  switch (value) {
    case '1':
    case '0':
      return 'processed';
    case 'UNFINISHED':
    case 'FINISHED':
      return 'initiated';
    case '3':
    case '2':
    default:
      return 'awaiting';
  }
}
