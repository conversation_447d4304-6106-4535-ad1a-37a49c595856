import React, { useEffect } from 'react';
import { connect, useDispatch } from 'react-redux';

import { FiltersForm, Form, Select } from '@galiojs/awesome-antd';
import trim from 'lodash/trim';

import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Input } from '@manyun/base-ui.ui.input';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import { getLocationSearchMap, setLocationSearch } from '@manyun/base-ui.util.query-string';

import { TicketTypeSelect } from '@manyun/ticket.ui.ticket-type-select';

import { LocationCascader, TinyTable, UserSelect } from '@manyun/dc-brain.legacy.components';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  getTicketListActionCreator,
  mineTicketActions,
} from '@manyun/dc-brain.legacy.redux/actions/mineTicketActions';

import {
  PROCESSED_COLUMS,
  PROCESSED_TAB_LIST,
  TICKET_IS_DELAY_MAP,
  getLocationMenuKeyBySubTab,
  getLocationSubTabKey,
  getSubTabKeyValue,
} from '../constants';

function onTabChange(changeStatus, updateSearchValues, resetPagination, successCallback) {
  return key => {
    updateSearchValues({
      localtion: {
        value: null,
      },
      taskNo: {
        value: null,
      },
      isDelay: {
        value: null,
      },
      gmtCreate: {
        value: null,
      },
      endTime: {
        value: null,
      },
      taskType: {
        value: null,
      },
    });
    resetPagination();
    changeStatus(key);
    successCallback && successCallback(key);
  };
}

//查询条件转Map
function toQueryMap(searchValues) {
  const params = Object.keys(searchValues).reduce((map, fieldName) => {
    const value = searchValues[fieldName]?.value;
    if ([null, undefined, ''].includes(value) || (Array.isArray(value) && value.length === 0)) {
      return map;
    } else if (fieldName === 'localtion') {
      map.idcTag = value[0];
      map.blockTag = value.length === 2 ? value.join('.') : null;
      // map.roomTag = value[0]+"."+value[1]+value[2];
    } else if (fieldName === 'gmtCreate') {
      map.startTime = value[1].valueOf();
      map.endTime = value[0].valueOf();
    } else if (fieldName === 'taskNo') {
      map[fieldName] = trim(value);
    } else if (fieldName === 'endTime') {
      map.checkStartTime = value[0].valueOf();
      map.checkEndTime = value[1].valueOf();
    } else if (fieldName === 'creatorName') {
      map[fieldName] = value._userName || value.userName;
    } else if (fieldName === 'taskType') {
      map.taskType = value[0];
      map.taskSubType = value[1];
    } else {
      map[fieldName] = value;
    }
    return map;
  }, {});

  return params;
}

//获取Tabs
function getTabs({
  searchValues,
  updateSearchValues,
  resetSearchValues,
  params,
  getMineTicketList,
  ticketTypeList,
  ticketList,
  taskStatus,
  ticketTypes,
  pagination,
  updatePagination,
  resetPagination,
  form,
  dispatch,
}) {
  const searchRoomList = (params, taskStatus) => {
    getMineTicketList({
      ...params,
      taskStatus,
      currentTaskAssignee: taskStatus !== '3',
      excludeTaskTypes: ['RISK_CHECK'],
      pageNum: 1,
      pageSize: 10,
    });
  };

  const onChangePageSize = (page, params, taskStatus) => {
    getMineTicketList({
      ...params,
      pageNum: page.current,
      pageSize: page.pageSize,
      taskStatus,
      currentTaskAssignee: taskStatus !== '3',
      excludeTaskTypes: ['RISK_CHECK'],
    });
    updatePagination({
      pageNum: page.current,
      pageSize: page.pageSize,
    });
  };

  const tabs = PROCESSED_TAB_LIST.map(item => (
    <Tabs.TabPane key={item.key} tab={item.name}>
      <FiltersForm
        items={[
          {
            label: '位置',
            name: 'localtion',
            control: <LocationCascader style={{ width: 200 }} currentAuthorize />,
          },
          {
            label: '工单类型',
            name: 'taskType',
            control: <TicketTypeSelect />,
          },
          {
            label: '工单单号',
            name: 'taskNo',
            control: <Input allowClear />,
          },

          {
            label: '提单人',
            name: 'creatorName',
            control: <UserSelect allowClear showSystem />,
          },
          {
            label: '是否超时',
            name: 'isDelay',
            control: (
              <Select key="1" allowClear>
                {TICKET_IS_DELAY_MAP.map(item => {
                  return (
                    <Select.Option key={item.value} value={item.value}>
                      {item.label}
                    </Select.Option>
                  );
                })}
              </Select>
            ),
          },
          {
            label: '创建时间',
            name: 'gmtCreate',
            span: 2,
            control: (
              <DatePicker.RangePicker
                format="YYYY-MM-DD HH:mm:ss"
                showTime
                placeholder={['开始时间', '结束时间']}
              />
            ),
          },
          {
            label: '关单时间',
            name: 'endTime',
            span: 2,
            control: (
              <DatePicker.RangePicker
                format="YYYY-MM-DD HH:mm:ss"
                showTime
                placeholder={['开始时间', '结束时间']}
              />
            ),
          },
        ]}
        form={form}
        fields={Object.keys(searchValues).map(name => {
          const field = searchValues[name];
          return {
            ...field,
            name: name.split('.'),
          };
        })}
        onFieldsChange={changedFields => {
          updateSearchValues(
            changedFields.reduce((mapper, field) => {
              const name = field.name.join('.');
              mapper[name] = {
                ...field,
                name,
              };

              return mapper;
            }, {})
          );
        }}
        onSearch={() => {
          resetPagination();
          searchRoomList(params, item.key);
        }}
        onReset={() => {
          dispatch(resetSearchValues());
          updateSearchValues({
            localtion: {
              value: null,
            },
            taskNo: {
              value: null,
            },
            isDelay: {
              value: null,
            },
            gmtCreate: {
              value: null,
            },
          });
          resetPagination();
          getMineTicketList({
            taskStatus: taskStatus,
            currentTaskAssignee: taskStatus !== '3',
            excludeTaskTypes: ['RISK_CHECK'],
            pageNum: 1,
            pageSize: 10,
          });
        }}
      />

      <TinyTable
        rowKey="taskNo"
        align="left"
        scroll={{ x: 'max-content' }}
        columns={columns(ticketTypeList)}
        loading={ticketList.fetching}
        dataSource={ticketList.data}
        pagination={{
          total: ticketList.total,
          current: pagination.pageNum,
          pageSize: pagination.pageSize,
        }}
        onChange={page => onChangePageSize(page, params, item.key)}
      />
    </Tabs.TabPane>
  ));
  return tabs;
}

function MineProcessTicket({
  ticketList,
  taskStatus,
  getMineTicketList,
  changeStatus,
  searchValues,
  updateSearchValues,
  resetSearchValues,
  syncCommonData,
  ticketTypeList,
  spaceTreeData,
  ticketTypes,
  pagination,
  updatePagination,
  resetPagination,
}) {
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const { menuKey, subTabKey } = getLocationSearchMap(window.location.search);
  useEffect(() => {
    if (subTabKey) {
      changeStatus(getSubTabKeyValue(subTabKey));
    }
  }, [subTabKey, changeStatus]);
  useEffect(() => {
    syncCommonData({ strategy: { ticketTypes: 'IF_NULL', space: 'FORCED' } });
    getMineTicketList({
      taskStatus: taskStatus,
      excludeTaskTypes: ['RISK_CHECK'],
      currentTaskAssignee: taskStatus !== '3',
    });
  }, [taskStatus, syncCommonData, getMineTicketList]);

  const params = toQueryMap(searchValues);

  const tabs = getTabs({
    spaceTreeData,
    searchValues,
    updateSearchValues,
    resetSearchValues,
    params,
    getMineTicketList,
    ticketTypeList,
    ticketList,
    taskStatus,
    ticketTypes,
    pagination,
    updatePagination,
    resetPagination,
    form,
    dispatch,
  });

  return (
    <Tabs
      activeKey={taskStatus}
      onChange={onTabChange(changeStatus, updateSearchValues, resetPagination, key => {
        setLocationSearch({
          menuKey: menuKey ?? getLocationMenuKeyBySubTab(key),
          subTabKey: getLocationSubTabKey(key),
        });
      })}
    >
      {tabs}
    </Tabs>
  );
}

const mapStateToProps = ({
  mineticket: { ticketList, searchValues, taskStatus, pagination },
  common: { ticketTypes },
}) => {
  return {
    ticketList,
    searchValues,
    ticketTypeList: ticketTypes?.normalizedList || [],
    taskStatus,
    ticketTypes,
    pagination,
  };
};

const mapDispatchToProps = {
  getMineTicketList: getTicketListActionCreator,
  changeStatus: mineTicketActions.changeStatus,
  syncCommonData: syncCommonDataActionCreator,
  updateSearchValues: mineTicketActions.updateSearchValues,
  updatePagination: mineTicketActions.updatePagination,
  resetPagination: mineTicketActions.resetPagination,
  resetSearchValues: mineTicketActions.resetSearchValues,
};

export default connect(mapStateToProps, mapDispatchToProps)(MineProcessTicket);

const columns = ticketTypeList => PROCESSED_COLUMS(ticketTypeList);
