import { FiltersForm, Form, Select } from '@galiojs/awesome-antd';
// import get from 'lodash/get';
import trim from 'lodash/trim';
import moment from 'moment';
import React, { useEffect, useState } from 'react';
import { connect, useDispatch } from 'react-redux';
import { Link } from 'react-router-dom';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import { Tag } from '@manyun/base-ui.ui.tag';
import { getLocationSearchMap, setLocationSearch } from '@manyun/base-ui.util.query-string';
import { BackendTaskStatus } from '@manyun/ticket.model.ticket';
import { generateTicketLocation } from '@manyun/ticket.route.ticket-routes';
import { Resend } from '@manyun/ticket.ui.resend';
import { TicketTypeSelect } from '@manyun/ticket.ui.ticket-type-select';
import { TicketSlaText } from '@manyun/ticket.ui.tickets-table';
import { handleBatchEndFeedback } from '@manyun/ticket.util.handle-batch-end-feedback';

import {
  GutterWrapper,
  LocationCascader,
  StatusText,
  TinyTable,
  UserLink,
  UserSelect,
} from '@manyun/dc-brain.legacy.components';
import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';
// import { generateTreeData } from '@manyun/dc-brain.legacy.utils';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  endAction,
  getTicketListActionCreator,
  mineTicketActions,
  takeOverAction,
} from '@manyun/dc-brain.legacy.redux/actions/mineTicketActions';
import { ticketService } from '@manyun/dc-brain.legacy.services';

// import { TICKET_STATUS_KEY_TEXT_MAP } from '../../ticket/constants';
import {
  FORM_ITEM_LAYOUT,
  PENDING_TAB_LIST,
  TICKET_IS_DELAY_MAP,
  getLocationMenuKeyBySubTab,
  getLocationSubTabKey,
  getSubTabKeyValue,
} from '../constants';

function MinePendingTicket({
  ticketList,
  taskStatus,
  getMineTicketList,
  changeStatus,
  searchValues,
  updateSearchValues,
  resetSearchValues,
  takeOverTask,
  endTask,
  syncCommonData,
  ticketTypeList,
  pagination,
  updatePagination,
  ticketTypes,
  resetPagination,
}) {
  const { menuKey, subTabKey } = getLocationSearchMap(window.location.search);
  useEffect(() => {
    changeStatus(getSubTabKeyValue(subTabKey));
  }, [subTabKey, changeStatus]);
  useEffect(() => {
    getMineTicketList({
      taskStatusList: taskStatus !== '3' ? [taskStatus, 6] : [taskStatus],
      currentTaskAssignee: taskStatus !== '3',
      excludeTaskTypes: ['RISK_CHECK'],
    });
    syncCommonData({ strategy: { ticketTypes: 'IF_NULL', space: 'FORCED' } });
  }, [taskStatus, getMineTicketList, syncCommonData]);
  const [, { checkUserId }] = useAuthorized();
  const [form] = Form.useForm();
  const dispatch = useDispatch();

  const params = Object.keys(searchValues).reduce((map, fieldName) => {
    const value = searchValues[fieldName]?.value;
    if ([null, undefined, ''].includes(value) || (Array.isArray(value) && value.length === 0)) {
      return map;
    } else if (fieldName === 'localtion') {
      map.idcTag = value[0];
      map.blockTag = value.length === 2 ? value.join('.') : null;
    } else if (fieldName === 'gmtCreate') {
      map.startTime = value[0].valueOf();
      map.endTime = value[1].valueOf();
    } else if (fieldName === 'taskNo') {
      map[fieldName] = trim(value);
    } else if (fieldName === 'creatorName') {
      map[fieldName] = value._userName || value.userName;
    } else if (fieldName === 'taskType') {
      map.taskType = value[0];
      map.taskSubType = value[1];
    } else {
      map[fieldName] = value;
    }
    return map;
  }, {});

  const [records, setSelect] = useState({ selectedRowKeys: [], selectedRows: [] });

  //结单参数
  const [visible, setVisible] = useState(false);
  const [isModalVisible, setTakeVisible] = useState(false);

  const [timeout, setTimeout] = useState([]);
  const [rows, setRows] = useState([]);

  const searchRoomList = (params, taskStatus, getMineTicketList) => {
    getMineTicketList({
      ...params,
      taskStatusList: taskStatus !== '3' ? [taskStatus, 6] : [taskStatus],
      currentTaskAssignee: taskStatus !== '3',
      excludeTaskTypes: ['RISK_CHECK'],
      pageNum: 1,
      pageSize: 10,
    });
  };

  const onOk = (taskStatus, searchValues, getMineTicketList) => {
    new Promise(() => {
      endTask({
        params: {
          ticketNumbers: rows.map(item => item.taskNo),
        },
        successCallback: data => {
          setVisible(false);
          setSelect({ selectedRowKeys: [], selectedRows: [] });
          getMineTicketList({
            ...searchValues,
            taskStatusList: [2, 6],
            currentTaskAssignee: true,
            excludeTaskTypes: ['RISK_CHECK'],
            ...pagination,
          });
          const { endNum, approveNum } = data;
          const sumNum = rows.length;

          handleBatchEndFeedback({ sumNum, approveNum, endNum });
        },
      });
    });
  };

  const takeOver = (selectParam, taskStatus, params, getMineTicketList, takeOverTask) => {
    takeOverTask({
      params: {
        taskNos: selectParam,
      },
      successCallback: data => {
        getMineTicketList({
          ...params,
          taskStatus: 3,
          currentTaskAssignee: false,
          excludeTaskTypes: ['RISK_CHECK'],
          ...pagination,
        });
        message.success(`已成功接单${data.data.length}条`);
      },
    });
    setTakeVisible(false);
    setSelect({ selectedRowKeys: [], selectedRows: [] });
  };

  const getNormalselectedList = () => {
    return records.selectedRows.filter(item => item.taskStatus === '2');
  };

  const onClick = () => {
    const processing = getNormalselectedList();
    setRows(processing);
    setTimeout(getTimeoutList(processing));
    setVisible(true);
  };

  const onClickBatchTake = () => {
    setTakeVisible(true);
  };

  const getTimeoutList = selectedRows => {
    const list = selectedRows.filter(item => {
      const gmtTime = moment(item.gmtCreate);
      const now = moment(new Date().getTime());
      const delay = now.diff(gmtTime, 'minutes');
      if (item.taskSla !== 0 && delay > item.taskSla) {
        return true;
      }
      return false;
    });
    return list;
  };

  const onChangePageSize = (page, params, taskStatus, getMineTicketList) => {
    getMineTicketList({
      ...params,
      pageNum: page.current,
      pageSize: page.pageSize,
      taskStatusList: taskStatus !== '3' ? [taskStatus, 6] : [taskStatus],
      currentTaskAssignee: taskStatus !== '3',
      excludeTaskTypes: ['RISK_CHECK'],
    });
    updatePagination({
      pageNum: page.current,
      pageSize: page.pageSize,
    });
  };

  //获取选中的值
  const selectParam = records.selectedRows.map(row => {
    return row.taskNo;
  });

  const onTabChange = key => {
    setLocationSearch({
      menuKey: menuKey ?? getLocationMenuKeyBySubTab(key),
      subTabKey: getLocationSubTabKey(key),
    });
    changeStatus(key);
    updateSearchValues({
      localtion: {
        value: null,
      },
      taskNo: {
        value: null,
      },
      isDelay: {
        value: null,
      },
      gmtCreate: {
        value: null,
      },
      taskType: {
        value: null,
      },
    });
    resetPagination();
    setSelect({ selectedRowKeys: [], selectedRows: [] });
  };

  const getList = () => {
    resetPagination();
    getMineTicketList({
      ...params,
      pageNum: 1,
      pageSize: 10,
      taskStatusList: [3],
      currentTaskAssignee: false,
      excludeTaskTypes: ['RISK_CHECK'],
    });
  };

  const singleTakeOver = async (taskNo, taskType) => {
    const { response, error } = await ticketService.singleTakeOverTicket({ taskNo, taskType });
    if (response) {
      getList();
      message.success(`接单成功`);
    } else {
      message.error(error);
    }
  };

  const tabs = PENDING_TAB_LIST.map(item => (
    <Tabs.TabPane key={item.key} tab={item.name}>
      <FiltersForm
        form={form}
        items={[
          {
            label: '位置',
            name: 'localtion',
            control: <LocationCascader style={{ width: 200 }} currentAuthorize />,
          },
          {
            label: '工单类型',
            name: 'taskType',
            control: <TicketTypeSelect />,
          },
          {
            label: '工单单号',
            name: 'taskNo',
            control: <Input allowClear />,
          },
          {
            label: '提单人',
            name: 'creatorName',
            control: <UserSelect allowClear showSystem />,
          },
          {
            label: '是否超时',
            name: 'isDelay',
            control: (
              <Select key="1" allowClear>
                {TICKET_IS_DELAY_MAP.map(item => {
                  return (
                    <Select.Option key={item.value} value={item.value}>
                      {item.label}
                    </Select.Option>
                  );
                })}
              </Select>
            ),
          },
          {
            label: '创建时间',
            name: 'gmtCreate',
            span: 2,
            control: (
              <DatePicker.RangePicker
                format="YYYY-MM-DD HH:mm:ss"
                showTime
                placeholder={['开始时间', '结束时间']}
              />
            ),
          },
        ]}
        fields={Object.keys(searchValues).map(name => {
          const field = searchValues[name];
          return {
            ...field,
            name: name.split('.'),
          };
        })}
        onFieldsChange={changedFields => {
          updateSearchValues(
            changedFields.reduce((mapper, field) => {
              const name = field.name.join('.');
              mapper[name] = {
                ...field,
                name,
              };

              return mapper;
            }, {})
          );
        }}
        onSearch={() => {
          resetPagination();
          searchRoomList(params, item.key, getMineTicketList);
        }}
        onReset={() => {
          dispatch(resetSearchValues());
          updateSearchValues({
            localtion: {
              value: null,
            },
            taskNo: {
              value: null,
            },
            isDelay: {
              value: null,
            },
            gmtCreate: {
              value: null,
            },
          });
          resetPagination();
          getMineTicketList({
            taskStatusList: taskStatus !== '3' ? [taskStatus, 6] : [taskStatus],
            currentTaskAssignee: taskStatus !== '3',
            excludeTaskTypes: ['RISK_CHECK'],
            pageNum: 1,
            pageSize: 10,
          });
        }}
      />

      <TinyTable
        rowKey="taskNo"
        align="left"
        scroll={{ x: 'max-content' }}
        columns={columns(ticketTypeList, taskStatus, checkUserId, getList, singleTakeOver)}
        loading={ticketList.fetching}
        dataSource={ticketList.data}
        pagination={{
          total: ticketList.total,
          current: pagination.pageNum,
          pageSize: pagination.pageSize,
        }}
        rowSelection={{
          selectedRowKeys: records.selectedRowKeys,
          selectedRows: records.selectedRows,
          onChange: (selectedRowKeys, selectedRows) => {
            setSelect({ selectedRowKeys, selectedRows });
          },
        }}
        actions={[
          item.key === '3' && (
            <div key="batch-take">
              <Button
                type="danger"
                disabled={!(item.key === '3' && records.selectedRowKeys.length !== 0)}
                onClick={onClickBatchTake}
              >
                批量接单
              </Button>

              <Modal
                title="批量接单"
                centered
                visible={isModalVisible}
                onOk={() =>
                  takeOver(selectParam, item.key, params, getMineTicketList, takeOverTask)
                }
                onCancel={() => setTakeVisible(false)}
              >
                <p>是否确定批量接单?</p>
              </Modal>
            </div>
          ),
          item.key === '2' && (
            <div key="batch-end">
              <Button
                type="danger"
                disabled={records.selectedRowKeys.length === 0}
                onClick={onClick}
              >
                批量关单
              </Button>
              <Modal
                title="批量关单"
                visible={visible}
                destroyOnClose
                okButtonProps={{ disabled: !getNormalselectedList().length }}
                onCancel={() => setVisible(false)}
                onOk={() => onOk(item.key, params, getMineTicketList)}
              >
                <Form colon={false} {...FORM_ITEM_LAYOUT}>
                  <Form.Item label="选中:">
                    <div>
                      {records.selectedRows.length}条
                      {timeout.length > 0 ? (
                        <span>
                          , 其中超时
                          <StatusText style={{ display: 'inline-block' }} status="alarm">
                            {timeout.length}
                          </StatusText>
                          条。
                        </span>
                      ) : (
                        ''
                      )}
                    </div>
                  </Form.Item>
                </Form>
              </Modal>
            </div>
          ),
        ].filter(Boolean)}
        onChange={page => onChangePageSize(page, params, item.key, getMineTicketList)}
      />
    </Tabs.TabPane>
  ));

  return (
    <Tabs activeKey={taskStatus} onChange={onTabChange}>
      {tabs}
    </Tabs>
  );
}

const mapStateToProps = ({
  mineticket: { ticketList, searchValues, taskStatus, pagination },
  common: { ticketTypes },
}) => {
  return {
    ticketList,
    searchValues,
    ticketTypeList: ticketTypes?.normalizedList || [],
    taskStatus,
    pagination,
    ticketTypes,
  };
};

const mapDispatchToProps = {
  getMineTicketList: getTicketListActionCreator,
  changeStatus: mineTicketActions.changeStatus,
  takeOverTask: takeOverAction,
  syncCommonData: syncCommonDataActionCreator,
  endTask: endAction,
  updateSearchValues: mineTicketActions.updateSearchValues,
  updatePagination: mineTicketActions.updatePagination,
  resetPagination: mineTicketActions.resetPagination,
  resetSearchValues: mineTicketActions.resetSearchValues,
};

export default connect(mapStateToProps, mapDispatchToProps)(MinePendingTicket);

const columns = (ticketTypeList, taskStatus, checkUserId, getList, singleTakeOver) => [
  {
    title: '工单单号',
    dataIndex: 'taskNo',
    fixed: true,
    render: (text, record) => (
      <Space>
        <Link
          to={generateTicketLocation({
            id: text,
            ticketType: record.taskType.toLowerCase(),
            idcTag: record.idcTag,
            blockTag: record.blockTag,
          })}
        >
          {text}
        </Link>
        {record.sourceOfOrder === 'CUSTOMER' && <Tag color="blue">客户</Tag>}
      </Space>
    ),
  },
  {
    title: '工单类型',
    dataIndex: 'taskType',
    render: text => ticketTypeList?.[text]?.taskValue,
  },
  {
    title: '工单子类型',
    dataIndex: 'taskSubType',
    render: (text, { taskType, taskProperties }) => {
      return taskType === 'IT_SERVICE'
        ? `${ticketTypeList?.[text]?.taskValue}/${JSON.parse(taskProperties)?.thirdOrderTypeName}`
        : ticketTypeList?.[text]?.taskValue;
    },
  },
  {
    title: '工单标题',
    dataIndex: 'taskTitle',
  },
  {
    title: '位置',
    dataIndex: 'blockTag',
  },
  {
    title: '提单人',
    dataIndex: 'creatorName',
    render: (text, record) =>
      record.outerCreatorName ? (
        record.outerCreatorName
      ) : (
        <UserLink userId={record.creatorId} userName={text} />
      ),
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    dataType: 'datetime',
  },
  {
    title: '响应时效',
    dataIndex: 'responseSla',
    render: (_, record) => (
      <TicketSlaText
        taskSla={record.responseSla}
        delay={record.currentResponse}
        unit="SECOND"
        effectTime={null}
        endTime={null}
        shouldLimitShow
      />
    ),
  },
  {
    title: 'SLA时效',
    dataIndex: 'taskSla',
    render: (text, record) => (
      <TicketSlaText
        taskSla={text}
        delay={record.delay}
        unit="SECOND"
        effectTime={record.effectTime}
        endTime={record.endTime}
        shouldLimitShow
      />
    ),
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    visible: taskStatus === '3',
    render: (_, record) => {
      if (record.taskStatus === BackendTaskStatus.WAITTAKEOVER) {
        let _authorized = true;
        if (record.assigneeList?.length) {
          _authorized = record.assigneeList.filter(item => checkUserId(item.id)).length;
        }
        return (
          <GutterWrapper>
            {!!_authorized && (
              <Button
                key="takeover"
                compact
                type="link"
                onClick={() => singleTakeOver(record.taskNo, record.taskType)}
              >
                接单
              </Button>
            )}
            {(record.assigneeList?.length ?? 0) > 0 && (
              <Resend
                compact
                assigneeId={record.assigneeList.map(item => item.id)}
                style={{ padding: 0, height: 'auto' }}
                btnType="link"
                taskNo={record.taskNo}
                taskType={record.taskType}
                blockGuid={record.blockTag}
                onResend={result => result && getList()}
              />
            )}
          </GutterWrapper>
        );
      }
      return BLANK_PLACEHOLDER;
    },
  },
  // {
  //   title: '工单状态',
  //   dataIndex: 'taskStatus',
  //   render: text => TICKET_STATUS_KEY_TEXT_MAP[text],
  // },
];
