import Icon from '@ant-design/icons';
import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { Menu } from '@manyun/base-ui.ui.menu';
import { message } from '@manyun/base-ui.ui.message';
import { getLocationSearchMap, setLocationSearch } from '@manyun/base-ui.util.query-string';
import { MineTicketMenuItemKeyMap } from '@manyun/ticket.model.ticket';

import { GutterWrapper, TinyCard } from '@manyun/dc-brain.legacy.components';
import {
  getTicketListActionCreator,
  mineTicketActions,
} from '@manyun/dc-brain.legacy.redux/actions/mineTicketActions';
import * as ticketService from '@manyun/dc-brain.legacy.services/ticketService';

import { getSubTabKeyValue } from './constants';
import { Finish, Launch, Wait } from './icon';
import MineInitatorTicket from './initiator';
import MinePendingTicket from './pending';
import MineProcessTicket from './process';

function isLocationMenuKeyValid(key, judgeAwaiting, judgeProcessed, judgeInitiated) {
  switch (key) {
    case '1_0':
      return judgeProcessed;
    case 'UNFINISHED_1':
      return judgeInitiated;
    case '3_0':
    default:
      return judgeAwaiting;
  }
}

function getMenuKeyValue(key) {
  if (key) {
    return MineTicketMenuItemKeyMap[key];
  } else {
    return MineTicketMenuItemKeyMap.awaiting;
  }
}

function getLocationMenuKey(value) {
  switch (value) {
    case '1_0':
      return 'processed';
    case 'UNFINISHED_1':
      return 'initiated';
    case '3_0':
    default:
      return 'awaiting';
  }
}
function isSubTabCorrectWithMenu(menuKey, subTabKey) {
  let correctSubTabKeys;

  switch (menuKey) {
    case '1_0':
      correctSubTabKeys = ['success-completed', 'fail-completed'];
      break;
    case 'UNFINISHED_1':
      correctSubTabKeys = ['uncompleted', 'completed'];
      break;
    case '3_0':
    default:
      correctSubTabKeys = ['awaiting', 'taking'];
      break;
  }
  return correctSubTabKeys.includes(subTabKey);
}
function getDefaultSubTabKey(value) {
  switch (value) {
    case '1_0':
      return 'success-completed';
    case 'UNFINISHED_1':
      return 'uncompleted';
    case '3_0':
    default:
      return 'awaiting';
  }
}
function MineTicket({
  changeStatus,
  changeInitiator,
  setActivedKey,
  updateSearchValues,
  taskStatus,
  initiator,
  activedKey,
  ticketLaunchAuthorized,
  ticketWaitAuthorized,
  ticketFinishAuthorized,
  resetPagination,
}) {
  const [waitNum, setWaitNum] = useState(null);

  const { menuKey, subTabKey } = getLocationSearchMap(window.location.search);
  useEffect(() => {
    async function fetchListNum(params) {
      const { error, response } = await ticketService.fetchValidNoticeList(params);
      if (error) {
        message.error(error);
        return;
      }
      setWaitNum(num => num + response.total);
    }
    fetchListNum({ currentTaskAssignee: false, excludeTaskTypes: ['RISK_CHECK'], taskStatus: '3' });
    fetchListNum({ currentTaskAssignee: true, excludeTaskTypes: ['RISK_CHECK'], taskStatus: '2' });
  }, []);

  const onTabChange = key => {
    setActivedKey(key);
    var splitted = key.split('_', 2);
    const trueSubTabKey = isSubTabCorrectWithMenu(key, subTabKey)
      ? subTabKey
      : getDefaultSubTabKey(key);
    changeStatus(getSubTabKeyValue(trueSubTabKey));
    setLocationSearch({
      menuKey: getLocationMenuKey(key),
      subTabKey: trueSubTabKey,
    });
    resetPagination();
    changeInitiator(splitted[1]);
    updateSearchValues({
      localtion: {
        value: null,
      },
      taskNo: {
        value: null,
      },
      isDelay: {
        value: null,
      },
      gmtCreate: {
        value: null,
      },
      endTime: {
        value: null,
      },
      creatorName: {
        value: undefined,
      },
      taskType: {
        value: null,
      },
    });
  };

  // 注意：这里只是为了解决下初始化的 `activeKey` 不一定是有权限的 BUG
  // 代码就先这么恶心的写了，等待页面代码整体重构吧
  useEffect(() => {
    const defaultActiveKey = ticketWaitAuthorized
      ? '3_0'
      : ticketFinishAuthorized
        ? '1_0'
        : 'UNFINISHED_1';
    if (menuKey) {
      const defaultActiveKeyByMenuLocation = getMenuKeyValue(menuKey);
      onTabChange(
        isLocationMenuKeyValid(
          defaultActiveKeyByMenuLocation,
          ticketWaitAuthorized,
          ticketFinishAuthorized,
          ticketLaunchAuthorized
        )
          ? defaultActiveKeyByMenuLocation
          : defaultActiveKey
      );
    } else {
      if (activedKey !== defaultActiveKey) {
        onTabChange(defaultActiveKey);
      }
      resetPagination();
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [menuKey]);

  return (
    <GutterWrapper flex>
      <TinyCard
        bodyStyle={{
          padding: '16px 0',
          display: 'flex',
          flexDirection: 'column',
          minWidth: 193,
        }}
      >
        <Menu mode="vertical" selectedKeys={[activedKey]} style={{ width: '200px' }}>
          {ticketWaitAuthorized && (
            <Menu.Item key="3_0" onClick={() => onTabChange('3_0')}>
              <Icon component={Wait} />
              <span> 待我处理{`${waitNum ? `(${waitNum})` : ''}`}</span>
            </Menu.Item>
          )}
          {ticketFinishAuthorized && (
            <Menu.Item key="1_0" onClick={() => onTabChange('1_0')}>
              <Icon component={Finish} />
              <span> 已办</span>
            </Menu.Item>
          )}
          {ticketLaunchAuthorized && (
            <Menu.Item key="UNFINISHED_1" onClick={() => onTabChange('UNFINISHED_1')}>
              <Icon component={Launch} />
              <span> 已发起</span>
            </Menu.Item>
          )}
        </Menu>
      </TinyCard>
      <GutterWrapper mode="vertical" flexN={1} style={{ width: 'calc(100% - 1rem - 193px)' }}>
        <TinyCard>
          {ticketWaitAuthorized &&
            (taskStatus === '3' || taskStatus === '2') &&
            initiator === '0' &&
            activedKey === '3_0' && <MinePendingTicket setWaitNum={setWaitNum} />}
          {ticketLaunchAuthorized &&
            (taskStatus === '1' || taskStatus === '0') &&
            initiator === '0' &&
            activedKey === '1_0' && <MineProcessTicket />}
          {ticketFinishAuthorized && initiator === '1' && activedKey === 'UNFINISHED_1' && (
            <MineInitatorTicket />
          )}
        </TinyCard>
      </GutterWrapper>
    </GutterWrapper>
  );
}

const mapStateToProps = ({ mineticket: { taskStatus, initiator, activedKey } }) => {
  return {
    taskStatus,
    initiator,
    activedKey,
  };
};

const mapDispatchToProps = {
  getMineTicketList: getTicketListActionCreator,
  changeStatus: mineTicketActions.changeStatus,
  changeInitiator: mineTicketActions.changeInitiator,
  setActivedKey: mineTicketActions.setActivedKey,
  updateSearchValues: mineTicketActions.updateSearchValues,
  resetPagination: mineTicketActions.resetPagination,
};

const ConnectedMineTicket = connect(mapStateToProps, mapDispatchToProps)(MineTicket);

export default function TodosAuthWrapper() {
  const [ticketWaitAuthorized] = useAuthorized({ checkByCode: 'ele_ticket_wait' });
  const [ticketFinishAuthorized] = useAuthorized({ checkByCode: 'ele_ticket_finish' });
  const [ticketLaunchAuthorized] = useAuthorized({ checkByCode: 'ele_ticket_launch' });
  if (ticketWaitAuthorized || ticketFinishAuthorized || ticketLaunchAuthorized) {
    return (
      <ConnectedMineTicket
        ticketWaitAuthorized={ticketWaitAuthorized}
        ticketFinishAuthorized={ticketFinishAuthorized}
        ticketLaunchAuthorized={ticketLaunchAuthorized}
      />
    );
  }
  return '暂无权限';
}
