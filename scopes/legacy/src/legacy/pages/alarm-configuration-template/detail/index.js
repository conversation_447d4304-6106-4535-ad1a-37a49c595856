import React, { Component } from 'react';
import { connect } from 'react-redux';

import { Radio } from '@manyun/base-ui.ui.radio';

import { GutterWrapper, TinyCard } from '@manyun/dc-brain.legacy.components';
import { TEMPLATE_VIEW_MODE_TYPE } from '@manyun/dc-brain.legacy.constants/alarm';
import {
  alarmConfigurationTemplateActions,
  fetchAssociatedItem,
} from '@manyun/dc-brain.legacy.redux/actions/alarmConfigurationTemplateActions';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import { getAllTemplateGroupList } from '@manyun/dc-brain.legacy.services/alarmConfigService';
import {
  fetchCustomTemplateMessByMetaCode,
  fetchTemplateMessByMetaCode,
} from '@manyun/dc-brain.legacy.services/templateGroupViewService';

import EditCollection from '../edit';
import NewCollection from '../new';
import AssociatedTemplateGroup from './components/associated-template-list';
import BasicConfiguration from './components/basic-configuration';
import ConfigurationRulesList from './components/configuration-rules-list';

class CreateEditDetail extends Component {
  state = {
    tab: 'rule',
  };

  componentDidMount() {
    const { mode } = this.props;
    // 目标数据类型对应的数据
    this.props.syncCommonData({ strategy: { eventTypes: 'IF_NULL' } });

    // 请求所有模板组
    this.getAllTemplateCroup();
    if (mode === TEMPLATE_VIEW_MODE_TYPE.PREVIEW) {
      // 查询模板信息，从list 存的basicConfiguration 中没有恢复观察周期
      this.getTemplate(this.props.match.params.id);
      // 查询模板下得监控项信息
      this.props.fetchAssociatedItem({ groupId: this.props.match.params.id });
    }
    if (mode === TEMPLATE_VIEW_MODE_TYPE.CUSTOM_VIEW) {
      this.getCustomTemplate();
      this.props.fetchAssociatedItem({ groupId: this.props.match.params.id, mode: 'customView' });
    }
  }

  getAllTemplateCroup = async () => {
    const data = await getAllTemplateGroupList();
    if (!data || !data.response) {
      return;
    }
    this.props.saveTemplateGroup(data.response.data);
  };

  getTemplate = async () => {
    const data = await fetchTemplateMessByMetaCode(this.props.match.params.id);
    if (!data || !data.response) {
      return;
    }
    this.props.saveBasicConfiguration(data.response);
  };

  getCustomTemplate = async () => {
    const data = await fetchCustomTemplateMessByMetaCode(this.props.match.params.id);
    if (!data || !data.response) {
      return;
    }
    this.props.saveBasicConfiguration(data.response);
  };

  render() {
    const { mode, match, history } = this.props;
    const { tab } = this.state;

    return (
      <GutterWrapper flexN={1} mode="vertical">
        {(mode === TEMPLATE_VIEW_MODE_TYPE.PREVIEW ||
          mode === TEMPLATE_VIEW_MODE_TYPE.CUSTOM_VIEW) && (
          <>
            <BasicConfiguration mode={mode} match={match} history={history} />
            <TinyCard>
              <GutterWrapper mode="vertical">
                {mode === TEMPLATE_VIEW_MODE_TYPE.PREVIEW && (
                  <Radio.Group
                    onChange={e => {
                      this.setState({ tab: e.target.value });
                    }}
                    value={tab}
                  >
                    <Radio.Button value="rule">规则配置</Radio.Button>
                    <Radio.Button value="template">关联模板组</Radio.Button>
                  </Radio.Group>
                )}
                {(tab === 'rule' || mode === TEMPLATE_VIEW_MODE_TYPE.CUSTOM_VIEW) && (
                  <ConfigurationRulesList mode={mode} match={match} />
                )}
                {tab === 'template' && <AssociatedTemplateGroup mode={mode} match={match} />}
              </GutterWrapper>
            </TinyCard>
          </>
        )}
        {(mode === TEMPLATE_VIEW_MODE_TYPE.NEW || mode === TEMPLATE_VIEW_MODE_TYPE.CUSTOM_NEW) && (
          <NewCollection mode={mode} history={history} />
        )}
        {(mode === TEMPLATE_VIEW_MODE_TYPE.EDIT ||
          mode === TEMPLATE_VIEW_MODE_TYPE.CUSTOM_EDIT) && (
          <EditCollection mode={mode} match={match} history={history} />
        )}
      </GutterWrapper>
    );
  }
}

const mapStateToProps = ({
  alarmConfigurationTemplate: { basicConfiguration, templateGroupData },
}) => ({
  basicConfiguration,
  templateGroupData,
});
const mapDispatchToProps = {
  saveBasicConfiguration: alarmConfigurationTemplateActions.saveBasicConfiguration,
  fetchAssociatedItem: fetchAssociatedItem,
  saveTemplateGroup: alarmConfigurationTemplateActions.saveTemplateGroup,
  syncCommonData: syncCommonDataActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(CreateEditDetail);
