import React, { Component } from 'react';
import { connect } from 'react-redux';
import { <PERSON> } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Tag } from '@manyun/base-ui.ui.tag';

import { User } from '@manyun/auth-hub.ui.user';

import { TinyTable } from '@manyun/dc-brain.legacy.components';
import EditArea from '@manyun/dc-brain.legacy.pages/template-group-view/view/component/view-area';
import {
  fetchTemplateGroupAreaAction,
  templateGroupViewActions,
} from '@manyun/dc-brain.legacy.redux/actions/templateGroupViewActions';
import * as alarmConfigService from '@manyun/dc-brain.legacy.services/alarmConfigService';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';

const columns = ctx => [
  {
    title: '模板组名称',
    dataIndex: 'name',
    render: (text, record) => (
      <Link to={urls.generateViewTemplteGroupConfigLocation(record)}>{text}</Link>
    ),
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
  },
  {
    title: '更新时间',
    dataIndex: 'gmtModified',
  },
  {
    title: '生效域',
    dataIndex: '',
    render: (_, record) => (
      <Button compact type="link" onClick={() => ctx.showIds(record)}>
        查看
      </Button>
    ),
  },
  {
    title: '更新人',
    dataIndex: 'lastOperatorName',
    render: (text, { lastOperator }) => <User.Link id={lastOperator} name={text} />,
  },
  {
    title: '状态',
    dataIndex: 'available',
    render: available => (
      <Tag color={available ? 'green' : 'default'}>{available ? '启用' : '停用'}</Tag>
    ),
  },
];

class AssociatedTemplateGroup extends Component {
  state = {
    editMess: null,
    list: [],
    total: 0,
  };

  async componentDidMount() {
    const { response, error } = await alarmConfigService.getGroupListInTemplate({
      groupId: this.props.match.params.id,
    });
    if (error) {
      return;
    }
    this.setState({ list: response.data, total: response.data.length });
  }

  showIds = row => {
    let temp = {
      configId: row.id,
      configType: 'SCHEME',
    };
    this.setState({
      editMess: row,
    });
    this.props.fetchTemplateGroupArea(temp);
    this.props.viewAreaVisible();
  };

  render() {
    const { editMess, list, total } = this.state;

    return (
      <>
        <TinyTable
          rowKey="id"
          columns={columns(this)}
          align={'left'}
          dataSource={list}
          total={total}
        />
        <EditArea editMess={editMess} />
      </>
    );
  }
}
const mapDispatchToProps = {
  fetchTemplateGroupArea: fetchTemplateGroupAreaAction,
  viewAreaVisible: templateGroupViewActions.viewAreaVisible,
};
export default connect(null, mapDispatchToProps)(AssociatedTemplateGroup);
