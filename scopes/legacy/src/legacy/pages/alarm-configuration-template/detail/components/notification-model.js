import React, { useEffect } from 'react';
import { connect } from 'react-redux';

import { Tag } from '@manyun/base-ui.ui.tag';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';
import { reviewAlarmNoticifyAction } from '@manyun/dc-brain.legacy.redux/actions/alarmConfigurationTemplateActions';

import styles from '../../styles.module.less';

function Notification({ notifyRule, review, fields = [] }) {
  useEffect(() => {
    if (!notifyRule) {
      return;
    }
    review(notifyRule);
  }, [notifyRule, review]);
  return (
    <GutterWrapper>
      <div style={{ display: 'flex', justifyContent: 'flex-start', margin: '20px 0' }}>
        <span style={{ width: '10%' }}>告警通知文案:</span>
        <div
          className={styles.border}
          style={{
            border: '1px solid',
            width: '90%',
            minHeight: '100px',
            padding: '10px',
            boxSizing: 'border-box',
          }}
        >
          {fields &&
            fields.map(({ id, label, value, text }) => {
              if (value === 'CUSTOMIZED_TEXT') {
                return <Tag key={id}>{text || label}</Tag>;
              }
              return (
                <Tag key={id} color="blue">
                  {label}
                </Tag>
              );
            })}
        </div>
      </div>
    </GutterWrapper>
  );
}

const mapStateToProps = ({ alarmConfigurationTemplate: { reviewNotify } }) => {
  let fields = null;
  if (reviewNotify) {
    fields = reviewNotify;
  }

  return {
    fields,
  };
};

const mapDispatchToProps = {
  review: reviewAlarmNoticifyAction,
};

export default connect(mapStateToProps, mapDispatchToProps)(Notification);
