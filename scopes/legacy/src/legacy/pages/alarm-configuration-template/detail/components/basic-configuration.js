import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import get from 'lodash/get';

import { Tag } from '@manyun/base-ui.ui.tag';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';

import { TinyCard, TinyDescriptions, UserLink } from '@manyun/dc-brain.legacy.components';
import { TEMPLATE_VIEW_MODE_TYPE } from '@manyun/dc-brain.legacy.constants/alarm';
import {
  alarmConfigurationTemplateActions,
  alarmLargeClassAction,
  alarmTypeAction,
  getAllTemplateGroupList,
  getAssociatedGroupList,
} from '@manyun/dc-brain.legacy.redux/actions/alarmConfigurationTemplateActions';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';

import AssociatedTemplateGroup from '../../component/associated-template-group';

class BasicCollection extends Component {
  state = {
    alarmSmallClass: [],
    templateTopTypeList: [],
    secondTemplateList: [],
  };

  componentDidMount() {
    // 目标设备类型
    this.props.syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });
  }

  visible = () => {
    this.getList();
    this.props.showAssociatedTemplateGroup();
  };

  getList = () => {
    const { match, mode } = this.props;
    if (mode === TEMPLATE_VIEW_MODE_TYPE.NEW) {
      this.props.getAllTemplateGroupList();
    }
    if (mode === TEMPLATE_VIEW_MODE_TYPE.PREVIEW || mode === TEMPLATE_VIEW_MODE_TYPE.EDIT) {
      this.props.templateGroupInfo({
        activeKey: 'all',
        id: this.props.basicConfiguration.id,
      });
      this.props.getAllTemplateGroupList();
      this.props.getAssociatedGroupList({ groupId: match.params.id });
    }
  };

  getRemarks = remarks => {
    if (!remarks) {
      return null;
    }
    if (remarks.length <= 15) {
      return <span>{remarks}</span>;
    } else {
      return (
        <Tooltip placement="top" title={remarks}>
          <span>{remarks.slice(0, 15)}...</span>
        </Tooltip>
      );
    }
  };

  render() {
    const { basicConfiguration, mode, match, nomalizedDeviceCategory } = this.props;
    return (
      <TinyCard title="基本配置" style={{ marginBottom: 16 }}>
        {(mode === TEMPLATE_VIEW_MODE_TYPE.PREVIEW ||
          mode === TEMPLATE_VIEW_MODE_TYPE.CUSTOM_VIEW) && (
          <TinyDescriptions
            column={5}
            descriptionsItems={[
              {
                label: '模板名称',
                value: basicConfiguration.name,
              },
              {
                label: '监控对象',
                value: get(
                  nomalizedDeviceCategory,
                  [basicConfiguration.target, 'metaName'],
                  basicConfiguration.target
                ),
              },

              {
                label: '告警规则数',
                value: basicConfiguration.itemCount,
              },

              {
                label: '更新人',
                value: (
                  <UserLink
                    userId={basicConfiguration.lastOperator || basicConfiguration.operatorId}
                    userName={
                      basicConfiguration.lastOperatorName || basicConfiguration.operatorName
                    }
                  />
                ),
              },
              {
                label: '是否启用',
                value: (
                  <Tag color={basicConfiguration.available ? 'green' : 'default'}>
                    {basicConfiguration.available ? '启用' : '停用'}
                  </Tag>
                ),
              },
              {
                label: '创建时间',
                value: basicConfiguration.gmtCreate,
              },
              {
                label: '更新时间',
                value: basicConfiguration.gmtModified,
              },
              {
                label: '备注',
                value: this.getRemarks(basicConfiguration.description),
              },
            ]}
          />
        )}

        <AssociatedTemplateGroup mode={mode} match={match} type="editOrNew" />
      </TinyCard>
    );
  }
}

const mapStateToProps = ({
  alarmConfigurationTemplate: {
    alarmTypeData,
    alarmLargeClassData,
    basicConfiguration,
    activeKey,
    showAlarmTemplateList,
  },
  common: { deviceCategory },
}) => ({
  alarmTypeData,
  alarmLargeClassData,
  basicConfiguration,
  activeKey,
  showAlarmTemplateList,
  nomalizedDeviceCategory: deviceCategory ? deviceCategory.normalizedList : {},
});
const mapDispatchToProps = {
  alarmTypeAction,
  alarmLargeClassAction,
  saveBasicConfiguration: alarmConfigurationTemplateActions.saveBasicConfiguration,
  templateGroupInfo: alarmConfigurationTemplateActions.templateGroupInfo,
  showAssociatedTemplateGroup: alarmConfigurationTemplateActions.showAssociatedTemplateGroup,
  getAllTemplateGroupList: getAllTemplateGroupList,
  getAssociatedGroupList: getAssociatedGroupList,
  syncCommonData: syncCommonDataActionCreator,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(Form.create({ name: 'Add_Alarm' })(BasicCollection));
