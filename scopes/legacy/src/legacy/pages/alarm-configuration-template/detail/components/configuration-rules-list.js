import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { Space } from '@manyun/base-ui.ui.space';
import { Tag } from '@manyun/base-ui.ui.tag';

import { TriggerRule, TriggerRuleType } from '@manyun/monitoring.model.trigger-rule';
import { AlarmLevelText } from '@manyun/monitoring.ui.alarm-level-text';
import { TriggerRulesText } from '@manyun/monitoring.ui.trigger-rules-text';

import { ModalButton, TinyTable } from '@manyun/dc-brain.legacy.components';
import { METADATA_TYPE } from '@manyun/dc-brain.legacy.constants/index';
import { getNotificationTxtTplFieldsAction } from '@manyun/dc-brain.legacy.redux/actions/alarmActions';
import {
  alarmConfigurationTemplateActions,
  alarmLargeClassAction,
  alarmTypeAction,
  fetchAssociatedItem,
} from '@manyun/dc-brain.legacy.redux/actions/alarmConfigurationTemplateActions';
import { getEventTypeName } from '@manyun/dc-brain.legacy.utils';
import * as urlsUtil from '@manyun/dc-brain.legacy.utils/urls';

import Notification from './notification-model';

const { Search } = Input;

const columns = ctx => [
  {
    title: '监控测点名称',
    dataIndex: 'name',
    fixed: 'left',
    render: (name, { id, deviceType, pointCode }) => (
      <Link
        to={urlsUtil.generateSpecificMonitorItemConfigLocation({
          configId: id,
          deviceType,
          pointCode,
        })}
      >
        <Button type="link" style={{ padding: 0, height: 'auto' }}>
          {name}
        </Button>
      </Link>
    ),
  },
  {
    title: '告警类型',
    dataIndex: ['alarmType', 'name'],
  },
  {
    title: '告警级别',
    dataIndex: 'alarmLevel',
    render: text => <AlarmLevelText code={text} />,
  },
  {
    title: '前置条件',
    key: 'preTriggerRules',
    dataIndex: 'preTriggerRules',
    render: (_, entity) => {
      return (
        <TriggerRulesText
          type={TriggerRuleType.PreCondition}
          triggerRules={(entity.triggerRules ?? [])
            .map(rule => TriggerRule.fromApiObject(rule))
            .map(rule => rule.toJSON())}
        />
      );
    },
  },
  {
    title: '告警条件',
    key: 'triggerRules',
    dataIndex: 'triggerRules',
    render: (_, entity) => {
      return (
        <TriggerRulesText
          type={TriggerRuleType.AlarmCondition}
          triggerRules={(entity.triggerRules ?? [])
            .map(rule => TriggerRule.fromApiObject(rule))
            .map(rule => rule.toJSON())}
        />
      );
    },
  },
  {
    title: '是否启用',
    dataIndex: 'available',
    render: available => (
      <Tag color={available ? 'green' : 'default'}>{available ? '启用' : '停用'}</Tag>
    ),
  },
  {
    title: '告警通知文案',
    dataIndex: '',
    render: text => {
      return (
        <ModalButton
          compact
          type="link"
          text="查看"
          title="告警通知文案"
          footer={null}
          modalStyle={{ minWidth: (1200 / 1600) * 100 + '%', maxWidth: 1200 }}
          destroyOnClose
        >
          <Notification notifyRule={text.notifyRule} />
        </ModalButton>
      );
    },
  },
  {
    title: '是否自动创建事件',
    dataIndex: 'createIncident',
    render: text => {
      if (text) {
        return <span>是</span>;
      }
      return <span>否</span>;
    },
  },
  {
    title: '事件类型',
    dataIndex: '',
    render: (_, record) => {
      if (!record.createIncident) {
        return `--`;
      }
      if (!record.eventTopCategory && !record.eventSecondCategory) {
        return '--';
      }
      const eventTypeLevel1 = getEventTypeName(
        ctx.props.eventTypeData,
        record.eventTopCategory,
        METADATA_TYPE.EVENT_TOP_CATEGORY
      );
      const eventTypeLevel2 = getEventTypeName(
        ctx.props.eventTypeData,
        record.eventSecondCategory,
        METADATA_TYPE.EVENT_SECOND_CATEGORY
      );
      return `${eventTypeLevel1 ? eventTypeLevel1 : ''}/${eventTypeLevel2 ? eventTypeLevel2 : ''}`;
    },
  },
];

class PointConfigInTemplate extends Component {
  state = {
    allDeviceParam: null,
  };

  componentDidMount() {
    const { alarmCopyList } = this.props;
    if (!alarmCopyList.physicalFields.length) {
      // 文案
      this.props.getNotificationTxtTplFieldsAction();
    }
  }

  onPressSearch = value => {
    const { alarmTemplateList } = this.props;

    if (!value) {
      this.props.setShowAlarmTemplateList(alarmTemplateList);
    } else {
      const list = alarmTemplateList.filter(({ name }) => {
        const idx = name.indexOf(value);
        if (idx > -1) {
          return true;
        } else {
          return false;
        }
      });
      this.props.setShowAlarmTemplateList(list);
    }
  };

  handleReset = () => {
    const { alarmTemplateList } = this.props;
    this.props.form.resetFields();
    this.props.setShowAlarmTemplateList(alarmTemplateList);
  };

  render() {
    const { showAlarmTemplateList } = this.props;

    return (
      <TinyTable
        rowKey="id"
        columns={columns(this)}
        scroll={{ x: 'max-content' }}
        dataSource={showAlarmTemplateList}
        pagination={{
          total: showAlarmTemplateList.length,
          showTotal: () => `共 ${showAlarmTemplateList.length} 条`,
        }}
        actionsWrapperStyle={{ justifyContent: 'flex-end' }}
        actions={[
          <Space key="search">
            监控测点名称
            <Search allowClear style={{ width: '200px' }} onSearch={this.onPressSearch} />
          </Space>,
        ]}
      />
    );
  }
}

const mapStateToProps = ({
  alarmConfigurationTemplate: {
    alarmTypeData,
    alarmLargeClassData,
    alarmTemplateList,
    showAlarmTemplateList,
    basicConfiguration,
    associatedGroupList,
    treeDataList,
    alarmConfiguration,
  },
  alarmManage: { alarmCopyList },
  common: { eventTypes },
}) => {
  let eventTypeData = {};
  if (eventTypes) {
    eventTypeData = eventTypes.normalizedList;
  }
  return {
    alarmTypeData,
    alarmLargeClassData,
    alarmTemplateList,
    showAlarmTemplateList,
    basicConfiguration,
    associatedGroupList,
    treeDataList,
    alarmConfiguration,
    eventTypeData,
    alarmCopyList,
  };
};
const mapDispatchToProps = {
  alarmTypeAction,
  alarmLargeClassAction,
  setAlarmTemplateList: alarmConfigurationTemplateActions.alarmTemplateList,
  setShowAlarmTemplateList: alarmConfigurationTemplateActions.showAlarmTemplateList,
  fetchAssociatedItem: fetchAssociatedItem,
  getNotificationTxtTplFieldsAction,
};

export default connect(mapStateToProps, mapDispatchToProps)(PointConfigInTemplate);
