import React, { Component } from 'react';
import { connect, useDispatch } from 'react-redux';
import { Link } from 'react-router-dom';

import { FiltersForm, Form, Select } from '@galiojs/awesome-antd';
import get from 'lodash.get';
import omit from 'lodash.omit';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Tag } from '@manyun/base-ui.ui.tag';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import {
  ALARM_CONFIGURATION_TEMPLATE_NEW,
  generateAlarmConfigurationTemplateEditUrl,
} from '@manyun/monitoring.route.monitoring-routes';

import { GutterWrapper, TinyTable } from '@manyun/dc-brain.legacy.components';
import { AVAI_LABLE } from '@manyun/dc-brain.legacy.constants/alarm';
import {
  alarmConfigurationTemplateActions,
  changeAvailableStatus,
  deleteTemplateAction,
  fetchTemplateList,
} from '@manyun/dc-brain.legacy.redux/actions/alarmConfigurationTemplateActions';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import { getLocationSearchMap } from '@manyun/dc-brain.legacy.utils';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';

import { AlarmTargetSelect } from './target-select';

class AlarmConfigurationTemplateListLegacy extends Component {
  state = {
    selectedRowKeys: [],
    selectedRows: [],
  };

  componentDidMount() {
    // 目标设备类型
    this.props.syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });
    const { templateListPage } = this.props;
    const { deviceTypes } = this._getInitialSearchValues();
    this.props.updateSearchValues({
      deviceTypeList: { name: 'deviceTypeList', value: deviceTypes },
    });
    this.props.fetchTemplateList({
      ...templateListPage,
      initialSearchValues: {
        deviceTypeList: deviceTypes,
      },
    });
  }

  /**
   * @private
   * @returns {{ deviceTypes: string[] }}
   */
  _getInitialSearchValues = () => {
    const searchParams = getLocationSearchMap(window.location.search, ['deviceTypes']);

    return {
      deviceTypes: searchParams.deviceTypes?.split(','),
    };
  };

  onSelectChange = (selectedRowKeys, selectedRows) => {
    this.setState({ selectedRowKeys, selectedRows });
  };

  onChangePage = (pageNum, pageSize) => {
    const { templateListPage } = this.props;
    this.setState({ selectedRowKeys: [], selectedRows: [] });
    this.props.fetchTemplateList({
      ...templateListPage,
      pageNum,
      pageSize,
    });
  };

  deleteConfiguration = record => {
    this.props.deleteTemplateAction({ groupId: record.id });
  };

  onSearch = data => {
    const params = omit(data, 'template');
    this.props.fetchTemplateList({
      pageSize: 10,
      pageNum: 1,
      ...params,
    });
  };

  selectBuild = value => {
    this.getBuildChilfred(value);
  };

  reset = () => {
    this.props.dispatch(alarmConfigurationTemplateActions.resetSearchValues());
    const { deviceTypes } = this._getInitialSearchValues();
    this.props.fetchTemplateList({
      pageNum: 1,
      pageSize: 10,
      initialSearchValues: {
        deviceTypeList: deviceTypes,
      },
    });
  };

  changeAvailableStatus = available => {
    const { selectedRowKeys } = this.state;
    const params = {
      ...Object.keys(this.props.fields).reduce((map, id) => {
        map[id] = this.props.fields[id].value;

        return map;
      }, {}),
      pageNum: 1,
      pageSize: 10,
    };
    this.props.changeAvailableStatus({ available, groupIds: selectedRowKeys, params });
    message.success(`成功${available ? '启用' : '停用'}${selectedRowKeys.length}项`);
    this.setState({ selectedRowKeys: [], selectedRows: [] });
  };

  render() {
    const { form, templateListPage, nomalizedDeviceCategory, fields, updateSearchValues } =
      this.props;
    const { selectedRowKeys, selectedRows } = this.state;

    return (
      <GutterWrapper mode="vertical">
        <Card>
          <FiltersForm
            form={form}
            fields={fields}
            initialValues={{
              lastOperatorName: fields.lastOperatorName?.value,
            }}
            items={[
              {
                label: '监控对象',
                name: 'deviceTypeList',
                control: <AlarmTargetSelect allowClear />,
              },
              {
                label: '状态',
                name: 'available',
                control: (
                  <Select allowClear>
                    {AVAI_LABLE.map(item => {
                      return <Select.Option key={item.key}>{item.value}</Select.Option>;
                    })}
                  </Select>
                ),
              },
              {
                label: '模板名称',
                name: 'name',
                control: <Input allowClear />,
              },
              {
                label: '更新人',
                name: 'lastOperatorName',
                control: <UserSelect allowClear />,
              },
            ]}
            onFieldsChange={changedFields => {
              updateSearchValues(
                changedFields.reduce((mapper, field) => {
                  const name = field.name.join('.');
                  mapper[name] = {
                    ...field,
                    name,
                  };

                  return mapper;
                }, {})
              );
            }}
            onSearch={this.onSearch}
            onReset={this.reset}
          />
        </Card>
        <Card>
          <TinyTable
            className={this.props.className}
            rowKey="id"
            dataSource={templateListPage.list}
            loading={templateListPage.loading}
            actions={[
              <Link key="new-template-link" to={ALARM_CONFIGURATION_TEMPLATE_NEW}>
                <Button type="primary">新建模板</Button>
              </Link>,
              <Button
                disabled={!selectedRowKeys.length}
                key="batch-enable"
                type="success"
                onClick={() => this.changeAvailableStatus(true)}
              >
                批量启用
              </Button>,
              <Button
                disabled={!selectedRowKeys.length}
                key="batch-disable"
                type="danger"
                onClick={() => this.changeAvailableStatus(false)}
              >
                批量停用
              </Button>,
            ]}
            columns={columns(this, nomalizedDeviceCategory)}
            scroll={{ x: 'max-content' }}
            rowSelection={{
              selectedRowKeys,
              selectedRows,
              onChange: this.onSelectChange,
            }}
            pagination={{
              total: templateListPage.total,
              current: templateListPage.pageNum,
              pageSize: templateListPage.pageSize,
              onChange: this.onChangePage,
            }}
          />
        </Card>
      </GutterWrapper>
    );
  }
}

function AlarmConfigurationTemplateList(props) {
  const [form] = Form.useForm();
  const dispatch = useDispatch();

  return <AlarmConfigurationTemplateListLegacy form={form} dispatch={dispatch} {...props} />;
}

const mapStateToProps = ({
  alarmConfigurationTemplate: { templateListPage, searchValues },
  common: { deviceCategory },
}) => ({
  templateListPage,
  nomalizedDeviceCategory: deviceCategory ? deviceCategory.normalizedList : {},
  deviceCategory,
  fields: Object.keys(searchValues).map(name => {
    const field = searchValues[name];

    return {
      ...field,
      name: name.split('.'),
    };
  }),
});

const mapDispatchToProps = {
  fetchTemplateList: fetchTemplateList,
  changeAvailableStatus: changeAvailableStatus,
  saveBasicConfiguration: alarmConfigurationTemplateActions.saveBasicConfiguration,
  deleteTemplateAction,
  syncCommonData: syncCommonDataActionCreator,
  updateSearchValues: alarmConfigurationTemplateActions.updateSearchValues,
};

export default connect(mapStateToProps, mapDispatchToProps)(AlarmConfigurationTemplateList);

const columns = (ctx, nomalizedDeviceCategory) => [
  {
    title: '模板名称',
    dataIndex: 'name',
    fixed: 'left',
    render: (text, record) => (
      <Link to={urls.generateAlarmConfigurationTemplateDetailUrl({ id: record.id, record })}>
        <Button
          type="link"
          style={{ padding: 0, height: 'auto' }}
          onClick={() => ctx.props.saveBasicConfiguration(record)}
        >
          {text}
        </Button>
      </Link>
    ),
  },

  {
    title: '监控对象',
    dataIndex: 'target',
    render(target, { type }) {
      return get(nomalizedDeviceCategory, [target, 'metaName'], target);
    },
  },
  {
    title: '包含规则数',
    dataIndex: 'itemCount',
  },

  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    dataType: 'datetime',
  },

  {
    title: '更新时间',
    dataIndex: 'gmtModified',
    dataType: 'datetime',
  },
  {
    title: '更新人',
    dataIndex: 'lastOperatorName',
  },
  {
    title: '状态',
    dataIndex: 'available',
    render: text => <Tag color={text ? 'green' : 'default'}>{text ? '启用' : '停用'}</Tag>,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    fixed: 'right',
    render: (text, record) => (
      <span>
        <Link to={generateAlarmConfigurationTemplateEditUrl({ id: record.id })}>
          <Button type="link" compact onClick={() => ctx.props.saveBasicConfiguration(record)}>
            编辑
          </Button>
        </Link>
        <Divider type="vertical" />
        <DeleteConfirm
          variant="popconfirm"
          title={`确认删除模板： ${record.name}吗？`}
          onOk={() => ctx.deleteConfiguration(record)}
        >
          <Button type="link" compact>
            删除
          </Button>
        </DeleteConfirm>
      </span>
    ),
  },
];
