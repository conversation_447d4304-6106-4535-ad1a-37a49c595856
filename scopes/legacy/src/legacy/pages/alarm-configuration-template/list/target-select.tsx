import React, { useEffect, useMemo } from 'react';

import type { RefSelectProps } from '@manyun/base-ui.ui.select';
import { TreeSelect } from '@manyun/base-ui.ui.tree-select';
import type { TreeSelectProps } from '@manyun/base-ui.ui.tree-select';

import { useDevices } from '@manyun/resource-hub.ui.device-type-cascader';
import { useSpaces } from '@manyun/resource-hub.ui.location-tree-select';

export type AlarmTargetSelectProps = {} & TreeSelectProps;

export const AlarmTargetSelect = React.forwardRef(
  (props: AlarmTargetSelectProps, ref?: React.Ref<RefSelectProps>) => {
    const [tree, getDeviceTypes] = useDevices({
      numbered: true,
      disabledTypeList: ['C0', 'C1'],
      dataType: ['snDevice', 'space'],
    });

    const [{ treeSpaces }] = useSpaces({
      nodeTypes: ['IDC'],
      disabledTypes: [],
      includeVirtualBlocks: false,
    });

    useEffect(() => {
      getDeviceTypes();
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const treeData = useMemo(
      () => [
        {
          value: 'Device',
          title: '设备类型',
          disabled: true,
          children: tree ? [...tree] : undefined,
        },
        {
          value: 'Instance',
          title: '实例',
          disabled: true,
          children: treeSpaces ? [...treeSpaces] : undefined,
        },
      ],
      [tree, treeSpaces]
    );

    return (
      <TreeSelect
        {...props}
        ref={ref}
        listHeight={400}
        treeData={treeData}
        multiple
        maxTagCount={1}
        showSearch
        treeNodeFilterProp="title"
      />
    );
  }
);

AlarmTargetSelect.displayName = 'AlarmTargetSelect';
