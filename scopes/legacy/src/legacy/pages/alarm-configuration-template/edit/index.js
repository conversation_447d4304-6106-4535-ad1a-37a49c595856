import React, { Component } from 'react';
import { connect } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Steps } from '@manyun/base-ui.ui.steps';
import { Typography } from '@manyun/base-ui.ui.typography';

import { fetchPointsByCondition } from '@manyun/resource-hub.service.fetch-points-by-condition';

import { FooterToolBar, GutterWrapper, TinyCard } from '@manyun/dc-brain.legacy.components';
import {
  alarmConfigurationTemplateActions,
  saveEditCustomTemplateModuleAction,
  saveEditTemplateModuleAction,
  searchTemplatePointConfigAction,
  submitEditCustomTemplateConfigAction,
  submitEditTemplateConfigAction,
} from '@manyun/dc-brain.legacy.redux/actions/alarmConfigurationTemplateActions';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import { redirectActionCreator } from '@manyun/dc-brain.legacy.redux/actions/routerActions';
import { getGroupListInTemplate } from '@manyun/dc-brain.legacy.services/alarmConfigService';
import {
  fetchCustomTemplateMessByMetaCode,
  fetchTemplateMessByMetaCode,
} from '@manyun/dc-brain.legacy.services/templateGroupViewService';

import ModuleInfo from '../new/components/module-info';
import NotificationCopy from '../new/components/notification-copy';
import NewResult from '../new/components/result/index';
import RuleInfo from '../new/components/rule-collection';

// import { getSpaceDeviceTypeIfNeeded } from '@manyun/dc-brain.legacy.utils/deviceType';

const { Title, Paragraph } = Typography;
const { Step } = Steps;

const steps = [
  {
    title: '模板信息',
  },
  {
    title: '规则配置',
  },
  {
    title: '文案配置',
  },
  {
    title: '配置完成',
  },
];

function throws(error) {
  throw new Error(error);
}
class EditCollection extends Component {
  state = {
    _pointConfigRefMap: new Map(),
    submitButtonloading: false,
  };

  _refreshTime = React.createRef();
  _moduleInfoRef = React.createRef();
  _globalNotificationRef = React.createRef();

  componentWillUnmount() {
    this.props.resetState4Creation();
  }

  componentDidMount() {
    const { mode } = this.props;
    if (mode === 'edit') {
      this.getTemplate();
    }
    if (mode === 'customEdit') {
      this.getCustomTemplate();
    }
    this.props.syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });
  }

  componentDidUpdate(prevProps) {
    const { monitorItemIds } = this.props;

    if (
      monitorItemIds.length !== prevProps.monitorItemIds.length ||
      monitorItemIds.some(id => !prevProps.monitorItemIds.includes(id))
    ) {
      const newMap = new Map(monitorItemIds.map(id => [id, React.createRef()]));
      this.setState({
        _pointConfigRefMap: newMap,
      });
    }
  }

  getTemplate = async () => {
    // 查询模板信息
    const data = await fetchTemplateMessByMetaCode(this.props.match.params.id);
    // 查询模板关联的模板组信息
    const groupData = await getGroupListInTemplate({ groupId: this.props.match.params.id });
    // 查询模板关联的监控项
    this.props.searchTemplatePointConfigAction({ groupId: this.props.match.params.id });
    if (!data || !data.response || !groupData || !groupData.response) {
      return;
    }
    const groupIds = groupData.response.data.map(item => {
      return item.id;
    });
    const list = {
      name: {
        value: data.response.name,
      },
      deviceType: {
        value: {
          code: data.response.deviceType,
          name: '',
        },
      },
      available: {
        value: data.response.available.toString(),
      },
      schemeIds: {
        value: groupIds,
      },
      description: {
        value: data.response.description,
      },
    };
    this.props.savefetchModuleConfiguration(list);

    // 根据请求 模板信息 结果中的 deviceType 请求测点项
    const pointDataInDevice = await fetchPointsByCondition({
      deviceType: data.response.deviceType,
      dataTypeList: ['AI', 'DI'],
      isRemoveSub: true,
    });

    if (!pointDataInDevice || !pointDataInDevice.data) {
      return;
    }
    this.props.savePointIndevice(pointDataInDevice.data.data.map(point => point.toApiObject()));
  };

  getCustomTemplate = async () => {
    // 查询模板信息
    const data = await fetchCustomTemplateMessByMetaCode(this.props.match.params.id);
    // 查询模板关联的监控项
    this.props.searchTemplatePointConfigAction({
      groupId: this.props.match.params.id,
      mode: 'custom',
    });
    if (!data || !data.response) {
      return;
    }
    const list = {
      name: {
        value: data.response.name,
      },
      available: {
        value: data.response.available.toString(),
      },
      idcTag: {
        value: [data.response.idcTag],
      },
      description: {
        value: data.response.description,
      },
    };
    this.props.savefetchModuleConfiguration(list);

    // 根据请求 模板信息 结果中的 idcTag 请求测点项
    const pointDataInDevice = await fetchPointsByCondition({
      spaceGuid: data.response.idcTag,
      dataTypeList: ['AI', 'DI'],
      isRemoveSub: true,
      pointTypeList: ['CUSTOM'],
    });

    if (!pointDataInDevice || !pointDataInDevice.data) {
      return;
    }
    this.props.savePointIndevice(pointDataInDevice.data.data.map(point => point.toApiObject()));
  };
  onChange = current => {
    if (current !== 3) {
      this.setState({ current });
    }
  };

  confirm = async () => {
    const { current, mode } = this.props;

    if (current === 0) {
      try {
        await this._moduleInfoRef.current.props.form.validateFieldsAndScroll().catch(throws);
        if (mode === 'customNew' || mode === 'customEdit') {
          this.props.saveEditCustomTemplateModuleAction(this.props.match.params.id);
        } else {
          this.props.saveEditTemplateModuleAction(this.props.match.params.id);
        }
      } catch (error) {}
    }
  };

  prev = () => {
    this.props.changeCurrent({ current: this.props.current - 1 });
  };

  _next = () => {
    this.props.changeCurrent({ current: this.props.current + 1 });
  };

  confirmRule = async () => {
    try {
      for (const [, pointConfigRef] of this.state._pointConfigRefMap) {
        if (pointConfigRef.current) {
          await pointConfigRef.current.form
            .validateFieldsAndScroll({ scroll: { offsetTop: 100, offsetBottom: 100 } })
            .catch(throws);
        }
      }
      await this._refreshTime.current.props.form
        .validateFieldsAndScroll({ scroll: { offsetTop: 100, offsetBottom: 100 } })
        .catch(throws);
      this._next();
    } catch (error) {}
  };

  submit = async () => {
    const { notifyConfigMode, mode } = this.props;
    let id = null;
    if (notifyConfigMode === 'GLOBAL') {
      try {
        id = this._globalNotificationRef.current.props.id;
        await this._globalNotificationRef.current.props.form
          .validateFieldsAndScroll({ scroll: { offsetTop: 100 } })
          .catch(throws);
        this.setState({ submitButtonloading: true });
        if (mode === 'customEdit') {
          this.props.onCustomSubmit({
            id: this.props.match.params.id,
            callback: () => {
              this.setState({ submitButtonloading: false });
            },
          });
        } else {
          this.props.onSubmit({
            id: this.props.match.params.id,
            successCb: () => {
              this.setState({ submitButtonloading: false });
            },
            failedCb: () => {
              this.setState({ submitButtonloading: false });
            },
          });
        }
      } catch (error) {
        this.props.saveValidateErrorId(id);
      }
    } else {
      try {
        for (const [, pointConfigRef] of this.state._pointConfigRefMap) {
          id = pointConfigRef.current.props.id;
          if (pointConfigRef.current) {
            await pointConfigRef.current.props.form
              .validateFieldsAndScroll({ scroll: { offsetTop: 100 } })
              .catch(throws);
          }
        }
        this.setState({ submitButtonloading: true });
        if (mode === 'customEdit') {
          this.props.onCustomSubmit({
            id: this.props.match.params.id,
            callback: () => {
              this.setState({ submitButtonloading: false });
            },
          });
        } else {
          this.props.onSubmit({
            id: this.props.match.params.id,
            successCb: () => {
              this.setState({ submitButtonloading: false });
            },
            failedCb: () => {
              this.setState({ submitButtonloading: false });
            },
          });
        }
      } catch (error) {
        this.props.saveValidateErrorId(id);
      }
    }
  };

  _updatePointRef = id => {
    const { form } = this.state._pointConfigRefMap.get(id).current;
    if (!form) {
      return;
    }
    form.validateFields(['incidentType'], { force: true });
  };

  render() {
    const { mode, match, current, isStep1FieldsTouched } = this.props;
    const finish = {
      title: '修改成功',
      subTitle:
        mode === 'customEdit' ? '告警模板已生效' : '告警模板修改后，无需再次关联模板组与资源',
    };
    const { submitButtonloading } = this.state;

    return (
      <>
        <div style={{ marginBottom: '40px' }}>
          <GutterWrapper mode="vertical">
            <Title level={4}>{'告警模板配置'}</Title>
            <Paragraph>
              {'通过模板配置，设置某一类型基础设备相关测点告警项，形成该类型设备告警项配置'}
            </Paragraph>
            <TinyCard>
              <GutterWrapper mode="vertical" size="2rem">
                <Steps
                  style={{ paddingLeft: 100, paddingRight: 100 }}
                  current={current}
                  // onChange={this.onChange}
                >
                  {steps.map(item => (
                    <Step key={item.title} title={item.title} />
                  ))}
                </Steps>

                {current === 0 && (
                  <ModuleInfo wrappedComponentRef={this._moduleInfoRef} mode={mode} />
                )}
                {current === 1 && (
                  <RuleInfo
                    pointConfigRefMap={this.state._pointConfigRefMap}
                    wrappedComponentRef={this._refreshTime}
                    mode={mode}
                    match={match}
                    updatePointRef={this._updatePointRef}
                  />
                )}

                {current === 2 && (
                  <NotificationCopy
                    globalNotificationRef={this._globalNotificationRef}
                    singleNotificationRef={this.state._pointConfigRefMap}
                  />
                )}
                {current === 3 && <NewResult finish={finish} mode={mode} />}
              </GutterWrapper>
            </TinyCard>
          </GutterWrapper>
        </div>

        <FooterToolBar>
          <GutterWrapper>
            {current > 0 && current < 3 && <Button onClick={this.prev}>上一步</Button>}

            {current === 0 && isStep1FieldsTouched && (
              <Popconfirm title="模板信息已被修改，确认保存吗？" onConfirm={this.confirm}>
                <Button type="primary">下一步</Button>
              </Popconfirm>
            )}

            {current === 0 && !isStep1FieldsTouched && (
              <Button type="primary" onClick={this._next}>
                下一步
              </Button>
            )}

            {current === 1 && (
              <Button type="primary" onClick={this.confirmRule}>
                下一步
              </Button>
            )}

            {current === 2 && (
              <Button type="primary" onClick={this.submit} loading={submitButtonloading}>
                提交
              </Button>
            )}

            {current < 3 && <Button onClick={() => this.props.history.goBack()}>取消</Button>}
          </GutterWrapper>
        </FooterToolBar>
      </>
    );
  }
}

const mapDispatchToProps = {
  onSubmit: submitEditTemplateConfigAction,
  onCustomSubmit: submitEditCustomTemplateConfigAction,
  searchTemplatePointConfigAction,
  savefetchModuleConfiguration: alarmConfigurationTemplateActions.savefetchModuleConfiguration,
  saveEditTemplateModuleAction,
  saveEditCustomTemplateModuleAction,
  savePointIndevice: alarmConfigurationTemplateActions.savePointIndevice,
  resetState4Creation: alarmConfigurationTemplateActions.resetState4Creation,
  changeCurrent: alarmConfigurationTemplateActions.changeCurrent,
  saveValidateErrorId: alarmConfigurationTemplateActions.saveValidateErrorId,
  syncCommonData: syncCommonDataActionCreator,
  redirect: redirectActionCreator,
};

export default connect(
  ({
    alarmConfigurationTemplate: {
      newTemplateMess: { monitorItemIds, module, notifyConfigMode },
      resultData,
      editTemplateModuleResult,
      current,
    },
  }) => {
    return {
      module,
      resultData,
      editTemplateModuleResult,
      notifyConfigMode,
      monitorItemIds,
      current,
      isStep1FieldsTouched: checkFieldsTouched(module),
    };
  },
  mapDispatchToProps
)(EditCollection);

function checkFieldsTouched(fields) {
  return Object.keys(fields).some(fieldId => {
    const { touched } = fields[fieldId];
    return touched;
  });
}
