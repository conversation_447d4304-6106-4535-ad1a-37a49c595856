import React, { Component, useEffect, useState } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import { Select } from '@galiojs/awesome-antd';
import _ from 'lodash';
import styled from 'styled-components';

import { Button } from '@manyun/base-ui.ui.button';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Popover } from '@manyun/base-ui.ui.popover';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import { Tag } from '@manyun/base-ui.ui.tag';

import { GutterWrapper, TinyTable } from '@manyun/dc-brain.legacy.components';
import { TEMPLATE_VIEW_MODE_TYPE } from '@manyun/dc-brain.legacy.constants/alarm';
import {
  alarmConfigurationTemplateActions,
  getAllTemplateGroupList,
  getAssociatedGroupList,
  monitorGroupAssociateScheme,
  removeAssociate,
} from '@manyun/dc-brain.legacy.redux/actions/alarmConfigurationTemplateActions';

const { TabPane } = Tabs;
const { Option } = Select;

export function PopoverButton({ cancelAssociated, record, loading }) {
  const [visible, setVisible] = useState(false);
  const [associatedTemplateGroupLoading, setAssociatedTemplateGroupLoading] = useState(false);

  useEffect(() => {
    setAssociatedTemplateGroupLoading(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [loading]);

  return (
    <Popover
      content={
        <GutterWrapper>
          <p>确认取消关联吗？</p>
          <Button
            style={{ padding: 0, marginLeft: 0 }}
            type="link"
            onClick={() => {
              cancelAssociated(record);
              setVisible(!visible);
              setAssociatedTemplateGroupLoading(true);
            }}
          >
            确认
          </Button>
        </GutterWrapper>
      }
      trigger="click"
      visible={visible}
      onVisibleChange={visible => setVisible(visible)}
    >
      <Button type="link" loading={associatedTemplateGroupLoading}>
        取消关联
      </Button>
    </Popover>
  );
}

const columns = (ctx, tabType, pageType, loading) => {
  const col = [
    {
      title: '模板组名称',
      dataIndex: 'name',
    },
    {
      title: '子模板数',
      dataIndex: 'groupNum',
    },
    {
      title: '创建人',
      dataIndex: 'lastOperatorName',
    },

    {
      title: '模板组状态',
      dataIndex: 'available',
      render: available => (
        <Tag color={available ? 'green' : 'default'}>{available ? '启用' : '停用'}</Tag>
      ),
    },
  ];
  if (pageType !== TEMPLATE_VIEW_MODE_TYPE.PREVIEW && tabType === 'associated') {
    col.push({
      title: '操作',
      dataIndex: 'operation',
      render: (_, record) => (
        <PopoverButton cancelAssociated={ctx.cancelAssociated} record={record} loading={loading} />
      ),
    });
  }
  return col;
};
class BasicCollection extends Component {
  state = {
    selectedRowKeys: [],
    selectedRows: [],
    name: '',
    available: null,
    visible: false,
  };

  handleVisibleChange = visible => {
    this.setState({ visible });
  };

  onCancel = () => {
    this.props.showAssociatedTemplateGroup();
  };

  onSelectChange = (selectedRowKeys, selectedRows) => {
    this.setState({
      selectedRowKeys,
      selectedRows,
    });
  };

  onChangeTab = tab => {
    this.props.templateGroupInfo({ ...this.props.templateGroup, activeKey: tab });
    this.getList();
  };

  getList = () => {
    const { type } = this.props;
    // if ((mode === 'new' || type === 'editOrNew') && tab === 'all') {
    //   this.props.getAllTemplateGroupList();
    // }
    if (type === 'list') {
      this.props.getAllTemplateGroupList();
      // this.props.templateGroup.related &&
      this.props.getAssociatedGroupList({ groupId: this.props.templateGroup.id });
    }
  };

  onConfirm = () => {
    const { mode, associatedGroupList, type, params } = this.props;
    const { selectedRows, selectedRowKeys } = this.state;
    if (mode === TEMPLATE_VIEW_MODE_TYPE.NEW) {
      // 新建或编辑时关联
      this.props.associatedGroupSuccess({
        allList: [...selectedRows, ...associatedGroupList.allList],
        showList: [...selectedRows, ...associatedGroupList.showList],
      });
      this.setState({
        selectedRows: [],
        selectedRowKeys: [],
      });
      this.props.templateGroupInfo({ ...this.props.templateGroup, activeKey: 'associated' });
    } else if (type === 'list' || mode === TEMPLATE_VIEW_MODE_TYPE.EDIT) {
      // 关联
      const addParams = { groupId: this.props.templateGroup.id, schemeIds: selectedRowKeys };
      if (params) {
        addParams.params = { pageNum: 1, pageSize: 10, ...this.props.params };
      }
      this.props.monitorGroupAssociateScheme(addParams);
      this.setState({
        selectedRows: [],
        selectedRowKeys: [],
      });
    }
  };

  cancelAssociated = record => {
    const { associatedGroupList, params, mode } = this.props;
    if (mode === TEMPLATE_VIEW_MODE_TYPE.NEW) {
      const list = associatedGroupList.allList.filter(item => item.id !== record.id);
      this.props.associatedGroupSuccess({
        allList: list,
        showList: list,
      });
    }
    const removeAssociateParams = { groupId: this.props.templateGroup.id, schemeId: record.id };
    if (params) {
      removeAssociateParams.params = { pageNum: 1, pageSize: 10, ...this.props.params };
    }
    this.props.removeAssociate(removeAssociateParams);
  };

  onReset = () => {
    const { templateGroup } = this.props;
    const { templateList, associatedGroupList } = this.props;
    if (templateGroup.activeKey === 'all') {
      this.props.templateListSuccess({
        allList: templateList.allList,
        showList: templateList.allList,
      });
    }
    if (templateGroup.activeKey === 'associated') {
      this.props.associatedGroupSuccess({
        allList: associatedGroupList.allList,
        showList: associatedGroupList.allList,
      });
    }
    this.setState({
      name: '',
      available: null,
    });
  };

  searchTemplate = () => {
    const { name, available } = this.state;
    const { templateList, associatedGroupList, templateGroup } = this.props;
    if (templateGroup.activeKey === 'all') {
      let showTemplateList = templateList.allList;
      if (name) {
        showTemplateList = templateList.allList.filter(item => item.name.indexOf(name) > -1);
      }
      if (available !== null) {
        showTemplateList = showTemplateList.filter(
          item => item.available.toString() === available.toString()
        );
      }
      this.props.templateListSuccess({ allList: templateList.allList, showList: showTemplateList });
    }
    if (templateGroup.activeKey === 'associated') {
      let showAssociatedGroupListt = associatedGroupList.allList;
      if (name) {
        showAssociatedGroupListt = associatedGroupList.allList.filter(
          item => item.name.indexOf(name) > -1
        );
      }
      if (available !== null) {
        showAssociatedGroupListt = showAssociatedGroupListt.filter(
          item => item.available.toString() === available.toString()
        );
      }
      this.props.associatedGroupSuccess({
        allList: associatedGroupList.allList,
        showList: showAssociatedGroupListt,
      });
    }
  };

  onChangeSearchValue = (value, name) => {
    this.setState({
      [name]: value,
    });
  };

  render() {
    const {
      associatedTemplateGroupVisible,
      mode,
      loading,
      templateList,
      associatedGroupList,
      associatedTemplateGroupLoading,
      templateGroup,
      className,
    } = this.props;
    const { selectedRowKeys, selectedRows, name, available } = this.state;
    const option = { rowKey: 'id', align: 'center' };
    if (mode !== TEMPLATE_VIEW_MODE_TYPE.PREVIEW) {
      option.rowSelection = {
        selectedRowKeys,
        onChange: this.onSelectChange,
      };
    }
    return (
      <div>
        <Modal
          width="60%"
          title="关联模板组"
          open={associatedTemplateGroupVisible}
          destroyOnClose
          className={className}
          footer={
            mode === TEMPLATE_VIEW_MODE_TYPE.PREVIEW || templateGroup.activeKey === 'associated'
              ? null
              : [
                  <Button
                    key={1}
                    type="primary"
                    loading={associatedTemplateGroupLoading}
                    disabled={!selectedRows.length}
                    onClick={this.onConfirm}
                  >
                    关联
                  </Button>,
                  <Button key={2} onClick={this.onCancel}>
                    取消
                  </Button>,
                ]
          }
          onCancel={this.onCancel}
        >
          <Form layout="inline">
            <Row>
              <Col span={18}>
                <Form.Item label="关键字">
                  <Input
                    style={{ width: 200 }}
                    value={name}
                    onChange={e => this.onChangeSearchValue(e.target.value, 'name')}
                  />
                </Form.Item>
                <Form.Item label="模板组状态">
                  <Select
                    style={{ width: 200 }}
                    value={available}
                    onChange={value => this.onChangeSearchValue(value, 'available')}
                  >
                    {/*eslint-disable-next-line react/jsx-boolean-value*/}
                    <Option key={true}>启用</Option>
                    <Option key={false}>停用</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={6} style={{ textAlign: 'right' }}>
                <Button type="primary" onClick={this.searchTemplate}>
                  搜索
                </Button>
                <Button style={{ marginLeft: 8 }} onClick={this.onReset}>
                  重置
                </Button>
              </Col>
            </Row>
          </Form>
          <Tabs
            activeKey={templateGroup.activeKey}
            tabBarStyle={{ borderBottom: 0 }}
            onChange={this.onChangeTab}
          >
            <TabPane key="all" tab="待关联">
              <TinyTable
                dataSource={_.differenceBy(
                  templateList.showList,
                  associatedGroupList.allList,
                  'id'
                )}
                size="small"
                columns={columns(this, 'all', mode, loading)}
                pagination={{
                  total: _.differenceBy(templateList.showList, associatedGroupList.allList, 'id')
                    .length,
                  showTotal: () =>
                    `共 ${
                      _.differenceBy(templateList.showList, associatedGroupList.allList, 'id')
                        .length
                    } 条`,
                }}
                {...option}
              />
            </TabPane>
            <TabPane key="associated" tab="已关联">
              <TinyTable
                rowKey="id"
                dataSource={associatedGroupList.showList}
                columns={columns(this, 'associated', mode, loading)}
                size="small"
                pagination={{
                  total: associatedGroupList.showList.length,
                  showTotal: () => `共${associatedGroupList.showList.length}条`,
                }}
              />
            </TabPane>
          </Tabs>
        </Modal>
      </div>
    );
  }
}

const mapStateToProps = ({
  alarmConfigurationTemplate: {
    associatedTemplateGroupVisible,
    templateList,
    associatedGroupList,
    associatedTemplateGroupLoading,
    templateGroup,
    loading,
  },
}) => ({
  associatedTemplateGroupVisible,
  templateList,
  associatedGroupList,
  associatedTemplateGroupLoading,
  templateGroup,
  loading,
});
const mapDispatchToProps = {
  getAllTemplateGroupList: getAllTemplateGroupList,
  getAssociatedGroupList: getAssociatedGroupList,
  associatedGroupSuccess: alarmConfigurationTemplateActions.associatedGroupSuccess,
  monitorGroupAssociateScheme: monitorGroupAssociateScheme,
  removeAssociate: removeAssociate,
  templateListSuccess: alarmConfigurationTemplateActions.templateListSuccess,
  templateGroupInfo: alarmConfigurationTemplateActions.templateGroupInfo,
  showAssociatedTemplateGroup: alarmConfigurationTemplateActions.showAssociatedTemplateGroup,
};

// export default connect(mapStateToProps, mapDispatchToProps)(BasicCollection);

export const BasicCollectionConnect = connect(mapStateToProps, mapDispatchToProps)(BasicCollection);
export const StyledBasicCollectionConnect = styled(BasicCollectionConnect)`
  .manyun-table-small {
    border: 0;
  }
`;

export default StyledBasicCollectionConnect;
