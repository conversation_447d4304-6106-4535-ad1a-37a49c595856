import React, { Component } from 'react';
import { connect } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { Steps } from '@manyun/base-ui.ui.steps';
import { Typography } from '@manyun/base-ui.ui.typography';

import { Footer<PERSON>ool<PERSON><PERSON>, GutterWrapper, TinyCard } from '@manyun/dc-brain.legacy.components';
import {
  alarmConfigurationTemplateActions,
  submitNewCustomTemplateConfigAction,
  submitNewTemplateConfigAction,
} from '@manyun/dc-brain.legacy.redux/actions/alarmConfigurationTemplateActions';
import { redirectActionCreator } from '@manyun/dc-brain.legacy.redux/actions/routerActions';

import ModuleInfo from './components/module-info';
import NotificationCopy from './components/notification-copy';
import NewResult from './components/result/index';
import RuleInfo from './components/rule-collection';

const { Title, Paragraph } = Typography;
const { Step } = Steps;

const steps = [
  {
    title: '模板信息',
  },
  {
    title: '规则配置',
  },
  {
    title: '文案配置',
  },
  {
    title: '配置完成',
  },
];

function throws(error) {
  throw new Error(error);
}

class NewCollection extends Component {
  state = {
    _pointConfigRefMap: new Map(this.props.monitorItemIds.map(id => [id, React.createRef()])),
    submitButtonloading: false,
  };
  _refreshTime = React.createRef();
  _moduleInfoRef = React.createRef();
  _globalNotificationRef = React.createRef();

  componentWillUnmount() {
    this.props.resetState4Creation();
  }
  componentDidUpdate(prevProps) {
    const { monitorItemIds } = this.props;
    if (
      monitorItemIds.length !== prevProps.monitorItemIds.length ||
      monitorItemIds.some(id => !prevProps.monitorItemIds.includes(id))
    ) {
      const newMap = new Map(monitorItemIds.map(id => [id, React.createRef()]));
      this.setState({
        _pointConfigRefMap: newMap,
      });
    }
  }

  _handleNext = async () => {
    if (this.props.current === 0) {
      try {
        await this._moduleInfoRef.current.props.form.validateFields().catch(throws);
        this._next();
      } catch (error) {}
    } else {
      try {
        if (this.state._pointConfigRefMap.size <= 0) {
          throws('至少添加一条告警规则！');
        }
        for (const [, pointConfigRef] of this.state._pointConfigRefMap) {
          if (pointConfigRef.current) {
            await pointConfigRef.current.form
              .validateFieldsAndScroll({ scroll: { offsetTop: 100, offsetBottom: 100 } })
              .catch(throws);
          }
        }
        await this._refreshTime.current.props.form
          .validateFieldsAndScroll({ scroll: { offsetTop: 100, offsetBottom: 100 } })
          .catch(throws);
        this._next();
      } catch (error) {}
    }
  };

  _next = () => {
    this.props.changeCurrent({ current: this.props.current + 1 });
  };

  prev = () => {
    this.props.changeCurrent({ current: this.props.current - 1 });
  };

  _submitHandler = async () => {
    const { notifyConfigMode, mode } = this.props;
    let id = null;
    if (notifyConfigMode === 'GLOBAL') {
      try {
        id = this._globalNotificationRef.current.props.id;
        await this._globalNotificationRef.current.props.form
          .validateFieldsAndScroll({ scroll: { offsetTop: 100 } })
          .catch(throws);
        this.setState({ submitButtonloading: true });

        if (mode === 'customNew' || mode === 'customEdit') {
          this.props.onCustomSubmit({
            callback: () => {
              this.setState({ submitButtonloading: false });
            },
          });
        } else {
          this.props.onSubmit({
            successCb: () => {
              this.setState({ submitButtonloading: false });
            },
            failedCb: () => {
              this.setState({ submitButtonloading: false });
            },
          });
        }
      } catch (error) {
        this.props.saveValidateErrorId(id);
      }
    } else {
      try {
        for (const [, pointConfigRef] of this.state._pointConfigRefMap) {
          id = pointConfigRef.current.props.id;
          if (pointConfigRef.current) {
            await pointConfigRef.current.props.form
              .validateFieldsAndScroll({ scroll: { offsetTop: 100 } })
              .catch(throws);
          }
        }
        this.setState({ submitButtonloading: true });

        if (mode === 'customNew' || mode === 'customEdit') {
          this.props.onCustomSubmit({
            callback: () => {
              this.setState({ submitButtonloading: false });
            },
          });
        } else {
          this.props.onSubmit({
            successCb: () => {
              this.setState({ submitButtonloading: false });
            },
            failedCb: () => {
              this.setState({ submitButtonloading: false });
            },
          });
        }
      } catch (error) {
        this.props.saveValidateErrorId(id);
      }
    }
  };

  _refresh = () => {
    window.location.reload();
  };

  render() {
    const { mode, current } = this.props;
    const finish = {
      title: '创建成功',
      subTitle:
        mode === 'customNew' || mode === 'customEdit'
          ? '新增告警模板已生效'
          : '新增告警模板需通过关联模板组与资源后方可生效',
    };
    const { submitButtonloading } = this.state;

    return (
      <>
        <div style={{ marginBottom: '40px' }}>
          <GutterWrapper>
            <Title level={4}>
              {mode === 'customNew' || mode === 'customEdit' ? '自定义告警配置' : '告警模板配置'}
            </Title>
            <Paragraph>
              {mode === 'customNew' || mode === 'customEdit'
                ? '通过自定义告警配置，设置自定义测点告警项，形成该特殊设备/空间告警项配置'
                : '通过模板配置，设置某一类型基础设备相关测点告警项，形成该类型设备告警项配置'}
            </Paragraph>
            <TinyCard>
              <GutterWrapper mode="vertical" size="2rem">
                <Steps style={{ paddingLeft: 100, paddingRight: 100 }} current={current}>
                  {steps.map(item => (
                    <Step key={item.title} title={item.title} />
                  ))}
                </Steps>
                {current === 0 && (
                  <ModuleInfo wrappedComponentRef={this._moduleInfoRef} mode={mode} />
                )}
                {current === 1 && (
                  <RuleInfo
                    pointConfigRefMap={this.state._pointConfigRefMap}
                    wrappedComponentRef={this._refreshTime}
                    mode={mode}
                  />
                )}
                {current === 2 && (
                  <NotificationCopy
                    globalNotificationRef={this._globalNotificationRef}
                    singleNotificationRef={this.state._pointConfigRefMap}
                    mode={mode}
                  />
                )}
                {current === 3 && <NewResult finish={finish} mode={mode} />}
              </GutterWrapper>
            </TinyCard>
          </GutterWrapper>
        </div>
        <FooterToolBar>
          <GutterWrapper>
            {current > 0 && current < 3 && <Button onClick={this.prev}>上一步</Button>}

            {current < 2 && (
              <Button type="primary" onClick={this._handleNext}>
                下一步
              </Button>
            )}
            {current === 2 && (
              <Button type="primary" onClick={this._submitHandler} loading={submitButtonloading}>
                提交
              </Button>
            )}
            {current < 3 && <Button onClick={() => this.props.history.goBack()}>取消</Button>}
          </GutterWrapper>
        </FooterToolBar>
      </>
    );
  }
}

const mapDispatchToProps = {
  onSubmit: submitNewTemplateConfigAction,
  onCustomSubmit: submitNewCustomTemplateConfigAction,
  resetState4Creation: alarmConfigurationTemplateActions.resetState4Creation,
  changeCurrent: alarmConfigurationTemplateActions.changeCurrent,
  saveValidateErrorId: alarmConfigurationTemplateActions.saveValidateErrorId,
  redirect: redirectActionCreator,
};

export default connect(
  ({
    alarmConfigurationTemplate: {
      newTemplateMess: { monitorItemIds, module, notifyConfigMode, globalNotificationTpl },
      resultData,
      current,
    },
  }) => ({
    module,
    resultData,
    notifyConfigMode,
    globalNotificationTpl,
    current,
    monitorItemIds,
  }),
  mapDispatchToProps
)(NewCollection);
