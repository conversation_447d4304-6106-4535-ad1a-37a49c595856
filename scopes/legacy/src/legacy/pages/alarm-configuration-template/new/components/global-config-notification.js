import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import { Select } from '@galiojs/awesome-antd';
import shortid from 'shortid';

import { alarmConfigurationTemplateActions } from '@manyun/dc-brain.legacy.redux/actions/alarmConfigurationTemplateActions';

import { defaultTemplateMess } from '../constants';
import NotificationTplCfg from './notification-tpl-cfg';

const { Option } = Select;

class GlobalNotification extends Component {
  state = {
    template: '',
  };

  setDefaultTemplate = () => {
    const templateMess = defaultTemplateMess.map((item, index) => {
      return {
        ...item,
        id: shortid(),
        destinationIndex: index,
      };
    });
    this.props.setGlobalNotificationTpl(templateMess);
  };

  render() {
    const {
      form: { getFieldDecorator },
      globalNotificationTpl,
    } = this.props;

    return (
      <>
        <Form.Item label="告警通知模板">
          <Select
            style={{ width: 200 }}
            allowClear
            labelInValue
            onChange={value => {
              this.setState({ template: value });
              if (value) {
                this.setDefaultTemplate();
                return;
              }
              this.props.setGlobalNotificationTpl([]);
            }}
          >
            <Option key="1" value="标准模版">
              标准模版
            </Option>
          </Select>
        </Form.Item>
        <Form.Item label="告警通知文案">
          {getFieldDecorator('notifyRule', {
            rules: [
              {
                required: true,
                message: '告警通知文案为必填项!',
              },
            ],
          })(
            <NotificationTplCfg
              singleMess={globalNotificationTpl.notifyRule.value[0]}
              template={this.state.template}
              setDefaultTemplate={this.setDefaultTemplate}
            />
          )}
        </Form.Item>
      </>
    );
  }
}

const mapStateToProps = ({
  alarmConfigurationTemplate: {
    newTemplateMess: { notifyConfigMode, globalNotificationTpl },
  },
}) => ({
  notifyConfigMode,
  globalNotificationTpl,
});

const mapDispatchToProps = {
  onChange: alarmConfigurationTemplateActions.handleGlobalFormChange,
  setGlobalNotificationTpl: alarmConfigurationTemplateActions.setGlobalNotificationTpl,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(
  Form.create({
    name: 'global_notification',
    mapPropsToFields(props) {
      return {
        notifyRule: Form.createFormField(props.globalNotificationTpl.notifyRule),
      };
    },
  })(GlobalNotification)
);
