import styled from 'styled-components';

export const DragFieldWrapper = styled.span`
  display: inline-block;
  margin: 4px;
`;

export const commonStyle = `
  position: relative;
  display: block;
  width: 100%;
  overflow: auto;
  font-size: 14px;
  line-height: 1.5;
  border-left-width: 1px;
  border-right-width: 1px;
  border-style: solid;
`;

export const DropZoneWrapper = styled.div`
  ${commonStyle}
  min-height: 64px;
  padding: 4px 11px 0;
  border-top-width: 1px;
  border-bottom-width: 0;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
`;

export const DropZoneActionWrapper = styled.div`
  ${commonStyle}
  min-height: 32px;
  padding: 0 11px 4px;
  border-top-width: 0;
  border-bottom-width: 1px;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
`;
