import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import { Select } from '@galiojs/awesome-antd';
import { cloneDeep } from 'lodash';
import get from 'lodash/get';

import { Cascader } from '@manyun/base-ui.ui.cascader';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { Radio } from '@manyun/base-ui.ui.radio';

import { AlarmLevelSelect } from '@manyun/monitoring.ui.alarm-level-select';

import { TinyEmpty } from '@manyun/dc-brain.legacy.components';
// import { ALARM_LEVEL } from '@manyun/dc-brain.legacy.constants/alarm';
import {
  BOTTOM_LIMMIT,
  LIMIT_VALUE_MAP,
  TOP_LIMMIT,
} from '@manyun/dc-brain.legacy.constants/alarm';
import { TEMPLATE_VIEW_MODE_TYPE } from '@manyun/dc-brain.legacy.constants/alarm';
import { POINT_VALUE_LIMIT } from '@manyun/dc-brain.legacy.pages/alarm-configuration-template/new/constants';
import { VALUE_TYPE_KEY_MAP } from '@manyun/dc-brain.legacy.pages/merged-processed-point/constants';
import { alarmConfigurationTemplateActions } from '@manyun/dc-brain.legacy.redux/actions/alarmConfigurationTemplateActions';
import { fetchSpecList } from '@manyun/dc-brain.legacy.services/specService';
import { generateCreateMetaConfigUrl } from '@manyun/dc-brain.legacy.utils/urls';

import Threshold from './threshold-value-top-bottom';

const { Option } = Select;

const alarmTypeData = [
  {
    value: 'ERROR',
    label: '告警',
  },
  {
    value: 'WARN',
    label: '预警',
  },
];

const FCForm = React.forwardRef((props, ref) => {
  React.useImperativeHandle(ref, () => ({ form: props.form }));
  const { getFieldDecorator, getFieldValue, validateFields } = props.form;
  const {
    deviceParamData,
    // thresholdRange,
    pointIndevice,
    getDeviceInPoint,
    pointMess,
    monitorItemMap,
    id,
    selectedPointInfo,
    _setThresholdMax,
    _setThresholdMin,
    treeList,
    mode,
  } = props;
  const { lowerLimit, upperLimit, pointCode, deviceType, name } = monitorItemMap[id];
  const lowerLimitValue = lowerLimit.value;
  const upperLimitValue = upperLimit.value;
  let dataType = 'AI';
  let editPointMess = [];
  let validLimits;
  // 判断是否有请求测点
  if (pointCode) {
    const selectedPointDetail = pointIndevice.filter(item => {
      if (
        (mode === 'customNew' || mode === 'customEdit') &&
        `${item.deviceType}${item.pointCode}` === `${deviceType}${pointCode}`
      ) {
        return true;
      } else if (
        mode !== 'customNew' &&
        mode !== 'customEdit' &&
        pointCode &&
        item.pointCode === pointCode
      ) {
        return true;
      } else {
        return false;
      }
    });

    if (selectedPointDetail.length && selectedPointDetail[0].dataType) {
      dataType = selectedPointDetail[0].dataType.code;
      editPointMess = [
        monitorItemMap[id].pointCode,
        selectedPointDetail[0].deviceType,
        selectedPointDetail[0].unit,
        selectedPointDetail[0].pointType.code,
        selectedPointDetail[0].dataType.code,
        selectedPointDetail[0].name,
      ];
      validLimits = selectedPointDetail[0].validLimits;
    } else {
      editPointMess = [monitorItemMap[id].code];
      validLimits = monitorItemMap[id].validLimits;
    }
  } else {
    const selectedCode =
      mode === 'customNew' || mode === 'customEdit'
        ? `${pointMess[1]}${pointMess[0]}`
        : pointMess[0];
    if (pointMess.length && selectedPointInfo && selectedPointInfo[selectedCode]) {
      validLimits = get(selectedPointInfo[selectedCode], ['validLimits'], []);
      dataType = selectedPointInfo[selectedCode].dataType.code;
    } else {
      editPointMess = name.value;
    }
  }
  const lowerLimitFieldValue = getFieldValue('lowerLimit');
  const upperLimitFieldValue = getFieldValue('upperLimit');

  const disabledPoint = (pointCode, deviceType) => {
    if (mode === 'customNew' || mode === 'customEdit') {
      return Object.values(monitorItemMap).filter(monitorItem => {
        return monitorItem.deviceType + monitorItem.pointCode === deviceType + pointCode;
      }).length;
    }
    return false;
  };

  return (
    <Form colon={false} style={{ width: '100%' }}>
      <Row>
        <Col xl={8}>
          <Form.Item label="监控测点名称" labelCol={{ xl: 6 }} wrapperCol={{ xl: 18 }}>
            {getFieldDecorator('name', {
              rules: [{ required: true, message: '监控测点名称为必填项' }],
            })(
              <Select showSearch onChange={getDeviceInPoint}>
                {pointIndevice.map(point => {
                  return (
                    <Option
                      key={point.pointCode}
                      disabled={disabledPoint(point.pointCode, point.deviceType)}
                      value={
                        mode === 'customNew' || mode === 'customEdit'
                          ? `${point.pointCode}_$$_${point.deviceType}_$$_${point.unit}_$$_${point.pointType.code}_$$_${point.dataType.code}_$$_${point.name}_$$_${point.spaceGuid}`
                          : `${point.pointCode}_$$_${point.deviceType}_$$_${point.unit}_$$_${point.pointType.code}_$$_${point.dataType.code}_$$_${point.name}`
                      }
                    >
                      {point.name}
                    </Option>
                  );
                })}
              </Select>
            )}
          </Form.Item>
        </Col>
        <Col xl={4}>
          <Form.Item label="触发次数" labelCol={{ xl: 12 }} wrapperCol={{ xl: 12 }}>
            {getFieldDecorator('triggerCount', {
              rules: [
                { required: true, message: '触发次数为必填项' },
                {
                  type: 'number',
                  max: 500,
                  message: '触发次数最大值为500',
                },
              ],
            })(<InputNumber min={1} max={500} />)}
          </Form.Item>
        </Col>
        {dataType === 'AI' && (
          <>
            <Col xl={6}>
              <Form.Item label="最小值" labelCol={{ xl: 8 }} wrapperCol={{ xl: 16 }}>
                {getFieldDecorator('lowerLimit', {
                  rules: [{ required: true, validator: validateChildItems({ upperLimitValue }) }],
                })(
                  <Threshold
                    options={BOTTOM_LIMMIT}
                    pointMess={pointMess.length ? pointMess : editPointMess}
                    required={upperLimitValue && upperLimitValue.operator === LIMIT_VALUE_MAP.NULL}
                    max={
                      upperLimitFieldValue
                        ? [null, undefined, ''].includes(upperLimitFieldValue.limit)
                          ? POINT_VALUE_LIMIT.MAX
                          : upperLimitFieldValue.limit
                        : POINT_VALUE_LIMIT.MAX
                    }
                    min={POINT_VALUE_LIMIT.MIN}
                    deviceType={deviceType}
                    deviceParamData={
                      deviceParamData[id] || [{ code: 'USER_DEFINED', name: '自定义' }]
                    }
                    onInputNumberBlur={limit => {
                      _setThresholdMin(limit);
                    }}
                    onBlur={() => {
                      validateFields(['lowerLimit', 'upperLimit'], { force: true });
                    }}
                  />
                )}
              </Form.Item>
            </Col>
            <Col xl={6}>
              <Form.Item label="最大值" labelCol={{ xl: 8 }} wrapperCol={{ xl: 16 }}>
                {getFieldDecorator('upperLimit', {
                  rules: [{ required: true, validator: validateChildItems({ lowerLimitValue }) }],
                })(
                  <Threshold
                    options={TOP_LIMMIT}
                    pointMess={pointMess.length ? pointMess : editPointMess}
                    required={lowerLimitValue && lowerLimitValue.operator === LIMIT_VALUE_MAP.NULL}
                    min={
                      lowerLimitFieldValue
                        ? [null, undefined, ''].includes(lowerLimitFieldValue.limit)
                          ? POINT_VALUE_LIMIT.MIN
                          : lowerLimitFieldValue.limit
                        : POINT_VALUE_LIMIT.MIN
                    }
                    max={POINT_VALUE_LIMIT.MAX}
                    deviceType={deviceType}
                    deviceParamData={
                      deviceParamData[id] || [{ code: 'USER_DEFINED', name: '自定义' }]
                    }
                    onInputNumberBlur={limit => {
                      _setThresholdMax(limit);
                    }}
                    onBlur={() => {
                      validateFields(['lowerLimit', 'upperLimit'], { force: true });
                    }}
                  />
                )}
              </Form.Item>
            </Col>
          </>
        )}
        {dataType === 'DI' && (
          <Col xl={12}>
            <Form.Item label="正常状态" labelCol={{ xl: 4 }} wrapperCol={{ xl: 20 }}>
              {getFieldDecorator('normalLimits', {
                rules: [{ required: true, message: '正常状态为必填项' }],
              })(
                <Select mode="multiple">
                  {validLimits &&
                    validLimits.length &&
                    validLimits.map(express => {
                      const [value, label] = String(express).split('=');

                      return (
                        <Option key={value} value={value}>
                          {label}
                        </Option>
                      );
                    })}
                </Select>
              )}
            </Form.Item>
          </Col>
        )}

        <Col xl={4}>
          <Form.Item label="告警级别" labelCol={{ xl: 12 }} wrapperCol={{ xl: 12 }}>
            {getFieldDecorator('alarmLevel', {
              rules: [{ required: true, message: '告警级别为必填项' }],
            })(<AlarmLevelSelect trigger="onDidMount" style={{ width: '100%' }} showSearch />)}
          </Form.Item>
        </Col>

        <Col xl={4}>
          <Form.Item label="告警类型" labelCol={{ xl: 12 }} wrapperCol={{ xl: 12 }}>
            {getFieldDecorator('alarmType', {
              rules: [{ required: true, message: '告警类型为必填项' }],
            })(
              <Select>
                {alarmTypeData.map(item => {
                  return (
                    <Option key={item.value} value={item.value}>
                      {item.label}
                    </Option>
                  );
                })}
              </Select>
            )}
          </Form.Item>
        </Col>

        <Col xl={4}>
          <Form.Item label="是否启用" labelCol={{ xl: 8 }} wrapperCol={{ xl: 16 }}>
            {getFieldDecorator('available')(
              <Radio.Group>
                <Radio value="true">是</Radio>
                <Radio value="false">否</Radio>
              </Radio.Group>
            )}
          </Form.Item>
        </Col>
        <Col xl={6}>
          <Form.Item label="自动创建事件" labelCol={{ xl: 8 }} wrapperCol={{ xl: 16 }}>
            {getFieldDecorator('createIncident')(
              <Radio.Group>
                <Radio value="true">是</Radio>
                <Radio value="false">否</Radio>
              </Radio.Group>
            )}
          </Form.Item>
        </Col>
        {getFieldValue('createIncident') === 'true' && (
          <Col xl={6}>
            <Form.Item label="事件类型" labelCol={{ xl: 8 }} wrapperCol={{ xl: 16 }}>
              {getFieldDecorator('incidentType', {
                rules: [
                  {
                    required: true,
                    message: '事件类型为必填项',
                  },
                ],
              })(
                <Cascader
                  placeholder=""
                  notFoundContent={
                    <TinyEmpty
                      description={
                        <a
                          href={generateCreateMetaConfigUrl()}
                          rel="noopener noreferrer"
                          target="_blank"
                          style={{ display: 'block', width: '100%', textAlign: 'center' }}
                        >
                          新增事件类型
                        </a>
                      }
                    />
                  }
                  options={treeList}
                  fieldNames={{ value: 'metaCode', label: 'metaName', children: 'children' }}
                />
              )}
            </Form.Item>
          </Col>
        )}
      </Row>
    </Form>
  );
});

const CustomizedForm = Form.create({
  name: 'global_state',
  onFieldsChange(props, changedFields) {
    if (!Object.keys(changedFields).length) {
      return;
    }
    const tmp = props.form.isFieldsTouched(Object.keys(changedFields));
    if (!tmp) {
      return;
    }
    props.onChange(changedFields);
  },
  mapPropsToFields(props) {
    return {
      name: Form.createFormField({
        ...props.name,
      }),
      alarmLevel: Form.createFormField({
        ...props.alarmLevel,
        value: props.alarmLevel.value,
      }),
      triggerCount: Form.createFormField({
        ...props.triggerCount,
        value: props.triggerCount.value,
      }),
      lowerLimit: Form.createFormField({
        ...props.lowerLimit,
      }),
      upperLimit: Form.createFormField({
        ...props.upperLimit,
      }),
      alarmType: Form.createFormField({
        ...props.alarmType,
        value: props.alarmType.value,
      }),
      available: Form.createFormField({
        ...props.available,
        value: props.available.value,
      }),
      createIncident: Form.createFormField({
        ...props.createIncident,
        value: props.createIncident.value,
      }),
      incidentType: Form.createFormField({
        ...props.incidentType,
        value: props.incidentType.value,
      }),
      normalLimits: Form.createFormField({
        ...props.normalLimits,
      }),
    };
  },
})(FCForm);

class PointConfig extends Component {
  state = {
    thresholdRange: [undefined, undefined],
    // deviceParamData: [{ code: 'USER_DEFINED', name: '自定义' }],
    triggerCountValidateStatus: {},
    pointMess: [],
    alarmLevelList: [],
  };

  componentDidMount() {
    const {
      newTemplateMess: { module, monitorItemMap },
      pointIndevice,
      id,
      mode,
    } = this.props;
    const { pointCode } = monitorItemMap[id];
    if (pointCode && mode !== TEMPLATE_VIEW_MODE_TYPE.CUSTOM_EDIT) {
      const selectedPointDetail = pointIndevice.filter(item => item.pointCode === pointCode);
      if (selectedPointDetail.length) {
        const specUnit = selectedPointDetail[0].unit;
        this.getDeviceParamData({
          deviceType: module.deviceType.value.code,
          specUnit: specUnit,
        });
      }
    }
  }

  getDeviceParamData = async ({ deviceType, specUnit }) => {
    const { id } = this.props;
    const { response } = await fetchSpecList({ deviceType });
    if (!response || !response.data) {
      return;
    }
    const unit = specUnit ? specUnit : this.state.pointMess[2];
    const list = response.data.filter(
      item =>
        item.specUnit?.toLowerCase() === unit?.toLowerCase() &&
        item.valueType === VALUE_TYPE_KEY_MAP.NUMBER
    );
    if (list.length) {
      const valueList = [{ code: 'USER_DEFINED', name: '自定义' }];
      this.props.saveDeviceParamData({
        key: id,
        value: valueList.concat(list.map(item => ({ code: item.specCode, name: item.specName }))),
      });
    } else {
      this.props.saveDeviceParamData({
        key: id,
        value: [{ code: 'USER_DEFINED', name: '自定义' }],
      });
    }
  };

  _setThresholdMax = max => {
    this.setState(({ thresholdRange }) => {
      if (max < thresholdRange[0]) {
        return;
      }
      return { thresholdRange: [thresholdRange[0], max] };
    });
  };

  _setThresholdMin = min => {
    this.setState(({ thresholdRange }) => {
      if (min > thresholdRange[1]) {
        return;
      }
      return { thresholdRange: [min, thresholdRange[1]] };
    });
  };

  handleFormChange = (changedFields, nowId) => {
    const {
      newTemplateMess: { monitorItems, monitorItemMap },
    } = this.props;
    const nameSplitToArr = judgePointIsChanged({ changedFields });
    const baseQ = getPointChangedInitLimits({ nameSplitToArr });
    const newItems = monitorItems.map(item => {
      if (item.code === nowId || item.id === nowId) {
        return {
          ...item,
          ...changedFields,
          ...baseQ,
        };
      }
      return item;
    });

    let newMap = cloneDeep(monitorItemMap);
    let currentModule = { ...cloneDeep(monitorItemMap[nowId]), ...changedFields };
    newMap[nowId] = {
      ...currentModule,
      ...baseQ,
    };
    this.props.saveNewTemplateMess({
      monitorItemMap: newMap,
      monitorItems: newItems,
    });
  };

  getDeviceInPoint = async value => {
    const { id, pointIndevice, mode } = this.props;
    const keys = value.split('_$$_');
    const selectedCode =
      mode === 'customNew' || mode === 'customEdit' ? `${keys[1]}${keys[0]}` : keys[0];
    this.props.saveSelectPointMess([...keys, id]);
    this.setState({
      pointMess: [...keys, id],
    });

    // 保存选择的监控项信息
    const selectedPointDetail = pointIndevice.filter(item => {
      if (
        (mode === 'customNew' || mode === 'customEdit') &&
        `${item.deviceType}${item.pointCode}` === selectedCode
      ) {
        return true;
      }
      if (mode !== 'customNew' && mode !== 'customEdit' && item.pointCode === selectedCode) {
        return true;
      } else {
        return false;
      }
    });
    this.props.saveSelectedPointInfo({ [selectedCode]: selectedPointDetail[0] });
    this.getDeviceParamData({ deviceType: keys[1] });
  };

  render() {
    const { thresholdRange, pointMess, alarmLevelList } = this.state;
    const {
      xRef,
      id,
      newTemplateMess: { monitorItemMap, monitorItems },
      pointIndevice,
      selectedPointInfo,
      treeList,
      deviceParamData,
      mode,
    } = this.props;
    const fields = monitorItemMap[id];
    return (
      <CustomizedForm
        {...fields}
        wrappedComponentRef={xRef}
        id={id}
        monitorItemMap={monitorItemMap}
        monitorItems={monitorItems}
        deviceParamData={deviceParamData}
        thresholdRange={thresholdRange}
        pointIndevice={pointIndevice}
        pointMess={pointMess}
        onChange={changedFields => this.handleFormChange(changedFields, id)}
        getDeviceInPoint={this.getDeviceInPoint}
        selectedPointInfo={selectedPointInfo}
        _setThresholdMax={this._setThresholdMax}
        _setThresholdMin={this._setThresholdMin}
        treeList={treeList}
        getAlarmLevelList={this.getAlarmLevelList}
        alarmLevelList={alarmLevelList}
        mode={mode}
      />
    );
  }
}

const mapStateToProps = ({
  alarmConfigurationTemplate: {
    newTemplateMess,
    pointIndevice,
    selectedPointInfo,
    deviceParamData,
  },
  common: { eventTypes },
}) => {
  let treeList = [];
  if (eventTypes) {
    treeList = eventTypes.treeList.map(topCategory => {
      if (topCategory.children.length === 0) {
        return {
          ...topCategory,
          children: null,
          disabled: true,
        };
      }
      const second = topCategory.children.map(secondCategory => {
        if (secondCategory.children.length === 0) {
          return {
            ...secondCategory,
            children: null,
          };
        }
        return secondCategory;
      });
      return {
        ...topCategory,
        children: second,
        disabled: false,
      };
    });
  }

  return {
    newTemplateMess,
    pointIndevice,
    selectedPointInfo,
    treeList,
    deviceParamData,
  };
};

const mapDispatchToProps = {
  saveNewTemplateMess: alarmConfigurationTemplateActions.saveNewTemplateMess,
  saveSelectPointMess: alarmConfigurationTemplateActions.saveSelectPointMess,
  saveSelectedPointInfo: alarmConfigurationTemplateActions.saveSelectedPointInfo,
  saveDeviceParamData: alarmConfigurationTemplateActions.saveDeviceParamData,
};
export default connect(mapStateToProps, mapDispatchToProps)(PointConfig);

function validateChildItems({ upperLimitValue, lowerLimitValue }) {
  return (__, values, callback) => {
    if (
      values.operator !== 'NULL' &&
      values.limitCode === 'USER_DEFINED' &&
      (upperLimitValue?.limitCode || lowerLimitValue?.limitCode) === 'USER_DEFINED' &&
      (upperLimitValue?.operator || lowerLimitValue?.operator) !== 'NULL'
    ) {
      if (values.limit === null) {
        callback('选择自定义时必须要填写具体数值！');
      } else {
        if (
          (lowerLimitValue && values && values.limit < lowerLimitValue.limit) ||
          (upperLimitValue && values && values.limit > upperLimitValue.limit)
        ) {
          callback('最小值必须小于等于最大值');
          return;
        }
        callback();
      }
    } else {
      callback();
    }
  };
}

/**
 * 如果是否有数据正确的测点发生改变
 * @param {object} props
 * @param {object} [props.changedFields] 表单的改变项
 * @return {Array}  null || 返回name 使用 _$$_ 分开后的数组
 */
function judgePointIsChanged({ changedFields }) {
  if (!changedFields || !changedFields.name) {
    return null;
  }
  const nameSplitToArr = changedFields.name.value.split('_$$_');
  const len = nameSplitToArr.length;
  if (len === 6 || len === 7) {
    return nameSplitToArr;
  }
  return null;
}

/**
 * 如果测点发生了改变，初始化最大值 最小值，正常状态
 * @param {object} props
 * @param {Array | null} props.nameSplitToArr
 * @return {object}
 */
function getPointChangedInitLimits({ nameSplitToArr }) {
  if (!nameSplitToArr) {
    return {};
  }
  return {
    pointCode: nameSplitToArr[0],
    deviceType: nameSplitToArr[1],
    spaceGuid: nameSplitToArr.length === 7 ? nameSplitToArr[6] : null,
    lowerLimit: { value: { limit: null, limitCode: 'USER_DEFINED', operator: 'gt' } },
    upperLimit: { value: { limit: null, limitCode: 'USER_DEFINED', operator: 'lt' } },
    normalLimits: { value: [] },
  };
}
