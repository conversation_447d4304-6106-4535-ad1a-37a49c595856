import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import QuestionCircleOutlined from '@ant-design/icons/es/icons/QuestionCircleOutlined';

import { Col, Row } from '@manyun/base-ui.ui.grid';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';

import { alarmConfigurationTemplateActions } from '@manyun/dc-brain.legacy.redux/actions/alarmConfigurationTemplateActions';

const layout = {
  labelCol: { span: 14 },
  wrapperCol: { span: 10 },
};

class RefreshTime extends Component {
  render() {
    const {
      form: { getFieldDecorator },
      newTemplateMess: { ruleTimeForm },
    } = this.props;
    return (
      <Form colon={false} {...layout} style={{ maxWidth: '225px' }}>
        <Row>
          <Col span={24}>
            <Form.Item
              label={
                <span>
                  恢复观察周期&nbsp;
                  <Tooltip title="指告警发生后，多少周期内未产生任何告警，默认为恢复正常。">
                    <QuestionCircleOutlined />
                  </Tooltip>
                </span>
              }
            >
              {getFieldDecorator('recoverInterval', {
                rules: [{ required: true, message: '恢复观察周期为必填项' }],
                initialValue: ruleTimeForm.recoverInterval?.value,
              })(
                <InputNumber
                  precision={1}
                  formatter={value => `${value}分钟`}
                  parser={value => {
                    let number = value;
                    Array.from('分钟').forEach(char => {
                      number = number.replace(char, '');
                    });
                    return number;
                  }}
                  min={0.1}
                  max={9999.9}
                  style={{ width: 208 }}
                />
              )}
            </Form.Item>
          </Col>
        </Row>
      </Form>
    );
  }
}
const mapStateToProps = ({ alarmConfigurationTemplate: { newTemplateMess } }) => ({
  newTemplateMess,
});
const mapDispatchToProps = {
  onChange: alarmConfigurationTemplateActions.handleRefresTimeFormChange,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(
  Form.create({
    name: 'refresh_time',
    onFieldsChange(props, changedFields) {
      props.onChange(changedFields);
    },
    mapPropsToFields(props) {
      return {
        recoverInterval: Form.createFormField(props.newTemplateMess.ruleTimeForm.recoverInterval),
      };
    },
  })(RefreshTime)
);
