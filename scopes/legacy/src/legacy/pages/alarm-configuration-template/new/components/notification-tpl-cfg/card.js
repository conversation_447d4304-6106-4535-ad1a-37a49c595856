import React from 'react';
import { DragDropContext, Droppable } from 'react-beautiful-dnd';
import { connect } from 'react-redux';

import shallowequal from 'shallowequal';
import shortid from 'shortid';

import { GutterWrapper, TinyCard } from '@manyun/dc-brain.legacy.components';
import { getNotificationTxtTplFieldsAction } from '@manyun/dc-brain.legacy.redux/actions/alarmActions';
import {
  alarmConfigurationTemplateActions,
  insertNotificationTplFieldAction,
} from '@manyun/dc-brain.legacy.redux/actions/alarmConfigurationTemplateActions';

import { ConnectedDropZone, DragField } from './dnd';

export class NotificationTxtTplCfgCard extends React.Component {
  componentDidMount() {
    const { alarmCopyList } = this.props;
    if (!alarmCopyList.physicalFields.length) {
      this.props.getNotificationTxtTplFields();
    }
  }

  dragEndHandler = ({ source, destination, draggableId }) => {
    if (!destination || shallowequal(source, destination)) {
      return;
    }
    const { alarmCopyList, notifyConfigMode, pointId, globalNotificationTpl, monitorItemMap } =
      this.props;
    if (source.droppableId === 'items' && destination.droppableId === 'notification') {
      const tmp = [
        ...alarmCopyList.physicalFields,
        ...alarmCopyList.faultedFields,
        ...alarmCopyList.otherFields,
      ].filter(item => item.metaCode === draggableId);
      this.props.onDrop({
        field: {
          id: shortid(),
          label: tmp[0] ? tmp[0].metaName : '',
          value: draggableId,
          notifyConfigMode: notifyConfigMode,
          pointId: pointId,
          description: tmp[0] ? tmp[0].description : '',
        },
        destinationIndex: destination.index,
      });
    }
    if (source.droppableId === 'notification' && destination.droppableId === 'notification') {
      if (source.index === destination.index) {
        return;
      }
      if (notifyConfigMode === 'GLOBAL') {
        const items = this.reorder(globalNotificationTpl.value, source.index, destination.index);
        this.props.setGlobalNotificationTpl(items);
        return;
      }
      const items = this.reorder(
        monitorItemMap[pointId].notifyRule.value,
        source.index,
        destination.index
      );
      const newItemMap = {
        ...monitorItemMap,
        [pointId]: {
          ...monitorItemMap[pointId],
          notifyRule: {
            value: items,
            dirty: true,
          },
        },
      };
      this.props.updateSingleNotificationTpl(newItemMap);
    }
  };

  reorder = (list, startIndex, endIndex) => {
    const result = Array.from(list);
    const [removed] = result.splice(startIndex, 1);
    result.splice(endIndex, 0, removed);

    return result;
  };

  render() {
    const { alarmCopyList, pointId, forwardRef, template, setDefaultTemplate } = this.props;

    return (
      <DragDropContext onDragEnd={this.dragEndHandler}>
        <GutterWrapper mode="vertical" ref={forwardRef}>
          <ConnectedDropZone
            pointId={pointId}
            template={template}
            setDefaultTemplate={setDefaultTemplate}
          >
            拖拽下方字段到此区域组装文案模板
          </ConnectedDropZone>
          <Droppable isDropDisabled droppableId="items" direction="horizontal">
            {provided => (
              <GutterWrapper ref={provided.innerRef} {...provided.droppableProps} flex>
                <TinyCard style={{ flex: 1 }} size="small" title="物理信息">
                  <GutterWrapper mode="horizontal">
                    {alarmCopyList.physicalFields.map(
                      ({ metaName, metaCode, description }, index) => (
                        <DragField
                          key={metaCode}
                          label={metaName}
                          value={metaCode}
                          index={index}
                          description={description}
                        ></DragField>
                      )
                    )}
                  </GutterWrapper>
                </TinyCard>
                <TinyCard style={{ flex: 1 }} size="small" title="故障信息">
                  <GutterWrapper mode="horizontal">
                    {alarmCopyList.faultedFields.map(
                      ({ metaName, metaCode, description }, index) => (
                        <DragField
                          key={metaCode}
                          label={metaName}
                          value={metaCode}
                          description={description}
                          index={alarmCopyList.physicalFields.length + index}
                        ></DragField>
                      )
                    )}
                  </GutterWrapper>
                </TinyCard>
                <TinyCard style={{ flex: 1 }} size="small" title="其他">
                  <GutterWrapper mode="horizontal">
                    {alarmCopyList.otherFields.map(({ metaName, metaCode, description }, index) => (
                      <DragField
                        key={metaCode}
                        label={metaName}
                        value={metaCode}
                        description={description}
                        index={
                          alarmCopyList.physicalFields.length +
                          alarmCopyList.faultedFields.length +
                          index
                        }
                      ></DragField>
                    ))}
                  </GutterWrapper>
                </TinyCard>
                {provided.placeholder}
              </GutterWrapper>
            )}
          </Droppable>
        </GutterWrapper>
      </DragDropContext>
    );
  }
}

const mapStateToProps = ({
  alarmManage: { alarmCopyList },
  alarmConfigurationTemplate: {
    newTemplateMess: { notifyConfigMode, globalNotificationTpl, monitorItemMap },
  },
}) => ({ alarmCopyList, notifyConfigMode, globalNotificationTpl, monitorItemMap });
const mapDispatchToProps = {
  getNotificationTxtTplFields: getNotificationTxtTplFieldsAction,
  onDrop: insertNotificationTplFieldAction,
  setGlobalNotificationTpl: alarmConfigurationTemplateActions.setGlobalNotificationTpl,
  updateSingleNotificationTpl: alarmConfigurationTemplateActions.updateSingleNotificationTpl,
};

const ConnectedNotificationTxtTplCfgCard = connect(
  mapStateToProps,
  mapDispatchToProps
)(NotificationTxtTplCfgCard);

export default React.forwardRef((props, ref) => {
  return <ConnectedNotificationTxtTplCfgCard {...props} forwardRef={ref} />;
});
