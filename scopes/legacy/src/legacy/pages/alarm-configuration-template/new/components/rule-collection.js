import React, { Component } from 'react';
import { connect } from 'react-redux';

import PlusOutlined from '@ant-design/icons/es/icons/PlusOutlined';
import _ from 'lodash';
import shortid from 'shortid';

import { Button } from '@manyun/base-ui.ui.button';
import { List } from '@manyun/base-ui.ui.list';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';
import {
  alarmConfigurationTemplateActions,
  deletePointConfigAction, // searchEventTypeAction,
} from '@manyun/dc-brain.legacy.redux/actions/alarmConfigurationTemplateActions';

import PointConfig from './point-config';
import RefreshTime from './refresh-time';
import { StyledList } from './styled';

class RuleInfo extends Component {
  _addRowHandler = () => {
    const {
      newTemplateMess: { monitorItemIds, monitorItems, monitorItemMap },
    } = this.props;
    const newId = shortid.generate();
    const prevId = monitorItemIds[monitorItemIds.length - 1];
    const itemModule = monitorItemMap[prevId];
    const itemIds = [...monitorItemIds, newId];
    const newItems = [...monitorItems, { ...itemModule, id: newId }];
    let newMap = {};
    newMap[newId] = { ...itemModule };
    this.props.saveNewTemplateMess({
      monitorItemIds: itemIds,
      monitorItems: newItems,
      monitorItemMap: { ...monitorItemMap, ...newMap },
    });
  };

  loadMore() {
    return (
      <Button type="link" onClick={this._addRowHandler}>
        <PlusOutlined />
        添加告警规则
      </Button>
    );
  }

  _deleteRowByIdx = id => {
    const {
      newTemplateMess: { monitorItemIds, monitorItems, monitorItemMap },
    } = this.props;
    const newIds = monitorItemIds.filter(item => item !== id);
    const newItems = monitorItems.filter(item => {
      if (item.id === id || item.code === id) {
        return false;
      } else {
        return true;
      }
    });
    const temp = _.cloneDeep(monitorItemMap);
    delete temp[id];
    this.props.saveNewTemplateMess({
      monitorItemIds: newIds,
      monitorItems: newItems,
      monitorItemMap: temp,
    });
  };

  onConfirm = id => {
    this.props.deletePointConfigAction({ groupId: this.props.match.params.id, itemId: id });
  };

  render() {
    const {
      pointConfigRefMap,
      newTemplateMess: { monitorItemIds, module },
      wrappedComponentRef,
      mode,
      nomalizedDeviceCategory,
    } = this.props;

    const deviceTypeName =
      module.deviceType.value.name ||
      _.get(
        nomalizedDeviceCategory,
        [module.deviceType.value.code, 'metaName'],
        module.deviceType.value.code
      );
    return (
      <GutterWrapper mode="vertical">
        {mode !== 'customNew' && mode !== 'customEdit' && (
          <span>目标设备类型&nbsp;&nbsp;{deviceTypeName}</span>
        )}
        <StyledList
          itemLayout="horizontal"
          dataSource={monitorItemIds}
          loadMore={this.loadMore()}
          locale={{
            emptyText: (
              <div className="has-error">
                <span className="manyun-form-explain">至少添加一条告警规则！</span>
              </div>
            ),
          }}
          renderItem={(id, index) => {
            return (
              <List.Item
                actions={
                  monitorItemIds.length > 1
                    ? [
                        <Button type="link" key="delete" onClick={() => this._deleteRowByIdx(id)}>
                          删除
                        </Button>,
                      ]
                    : []
                }
              >
                <PointConfig xRef={pointConfigRefMap.get(id)} id={id} mode={mode} />
              </List.Item>
            );
          }}
        />
        <RefreshTime wrappedComponentRef={wrappedComponentRef} />
      </GutterWrapper>
    );
  }
}

const mapStateToProps = ({
  alarmConfigurationTemplate: { newTemplateMess, pintConfigInLine, eventTypeData },
  common: { deviceCategory },
}) => ({
  newTemplateMess,
  pintConfigInLine,
  eventTypeData,
  nomalizedDeviceCategory: deviceCategory ? deviceCategory.normalizedList : {},
});
const mapDispatchToProps = {
  saveNewTemplateMess: alarmConfigurationTemplateActions.saveNewTemplateMess,
  deletePointConfigAction,
  // searchEventType: searchEventTypeAction,
};

export default connect(mapStateToProps, mapDispatchToProps)(RuleInfo);
