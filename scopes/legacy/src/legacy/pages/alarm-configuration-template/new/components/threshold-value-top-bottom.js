import React from 'react';
import { useSelector } from 'react-redux';

import { Select } from '@galiojs/awesome-antd';
import { cloneDeep } from 'lodash';

import { InputNumber } from '@manyun/base-ui.ui.input-number';

import { ConfigUtil } from '@manyun/dc-brain.util.config';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';
import { LIMIT_VALUE_MAP } from '@manyun/dc-brain.legacy.constants/alarm';
import { POINT_VALUE_LIMIT } from '@manyun/dc-brain.legacy.pages/alarm-configuration-template/new/constants';
import { getCurrentConfig } from '@manyun/dc-brain.legacy.redux/selectors/configSelectors';

// import { ReactReduxContext } from 'react-redux';

function Threshold({
  deviceType,
  forwardedRef,
  value = {},
  pointMess = [],
  options = [],
  // min,
  // max,
  onChange,
  deviceParamData = null,
  required,
  // onInputNumberBlur,
  onBlur,
}) {
  // 控制除自定义情况外的选择框宽度
  const [selectWidth, setSelectWidth] = React.useState(93);
  const config = useSelector(getCurrentConfig);
  const configUtil = new ConfigUtil(config);
  const gridDT = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_GRID);
  // 新增机柜阈值上下限类型
  let deviceParamPlusData = cloneDeep(deviceParamData);
  if (deviceType === gridDT && pointMess && pointMess[2]?.toLowerCase() === 'kw') {
    if (deviceParamPlusData?.length) {
      deviceParamPlusData?.push({ code: 'RATED_POWER', name: '机柜设计功率' });
      deviceParamPlusData?.push({ code: 'SIGNED_POWER', name: '机柜签约功率' });
    }
  }

  React.useEffect(() => {
    setSelectWidth(
      value?.limitCode === 'RATED_POWER' || value?.limitCode === 'SIGNED_POWER' ? 123 : 93
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value]);
  return (
    <GutterWrapper size="4px" style={{ display: 'inline-block' }}>
      <Select
        ref={forwardedRef}
        disabled={!pointMess.length}
        style={{ width: 52 }}
        value={value.operator ? value.operator : 'NULL'}
        onChange={selectedOperator => {
          onChange({ ...value, operator: selectedOperator });
        }}
        onBlur={() => {
          onBlur();
        }}
      >
        {options.map(({ label, value: optionVal }) => {
          return (
            <Select.Option
              key={optionVal}
              value={optionVal}
              disabled={optionVal === LIMIT_VALUE_MAP.NULL && required}
            >
              {label}
            </Select.Option>
          );
        })}
      </Select>
      {value.operator !== LIMIT_VALUE_MAP.NULL && (
        <Select
          style={{ width: selectWidth }}
          disabled={!pointMess.length}
          value={value.limitCode ? value.limitCode : 'USER_DEFINED'}
          onChange={limitCode => {
            const isEmptyString = limitCode === '';
            if (limitCode === 'USER_DEFINED') {
              setSelectWidth(93);
            } else {
              setSelectWidth(123);
            }
            onChange({ ...value, limitCode: isEmptyString ? null : limitCode });
          }}
        >
          {deviceParamPlusData?.length
            ? deviceParamPlusData.map(item => {
                return (
                  <Select.Option key={item.code} value={item.code}>
                    {item.name}
                  </Select.Option>
                );
              })
            : null}
        </Select>
      )}
      {value.operator !== LIMIT_VALUE_MAP.NULL &&
        (value.limitCode === 'USER_DEFINED' ||
          (value.limit && !value.limitCode) ||
          (!value.limit && !value.limitCode)) && (
          <InputNumber
            style={{ width: 'calc(100% - 55px - 90px - 4px - 4px)' }}
            // min={Number.isNaN(Number(min)) ? -1 : min}
            // max={Number.isNaN(Number(max)) ? 10000 : max}
            min={POINT_VALUE_LIMIT.MIN}
            max={POINT_VALUE_LIMIT.MAX}
            value={value.limit}
            formatter={value => {
              if (!pointMess || !pointMess[2] || pointMess[2] === 'null') {
                return `${value}`;
              }
              return `${value}${pointMess[2]}`;
            }}
            parser={value => {
              if (!pointMess || !pointMess[2] || pointMess[2] === 'null') {
                return `${value}`;
              }
              let number = value;
              Array.from(pointMess[2]).forEach(char => {
                number = number.replace(char, '');
              });
              return number;
            }}
            disabled={!pointMess.length}
            onChange={limit => {
              const isEmptyString = limit === '';
              onChange({ ...value, limit: isEmptyString ? null : limit });
            }}
            onBlur={() => {
              onBlur();
            }}
          />
        )}
    </GutterWrapper>
  );
}

export default React.forwardRef((props, ref) => <Threshold forwardedRef={ref} {...props} />);
