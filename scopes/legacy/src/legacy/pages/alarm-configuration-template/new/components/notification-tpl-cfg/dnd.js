import React, { useState } from 'react';
import { Draggable, Droppable } from 'react-beautiful-dnd';
import { connect } from 'react-redux';

import styled from 'styled-components';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Popover } from '@manyun/base-ui.ui.popover';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';
import {
  alarmConfigurationTemplateActions,
  deleteNotificationTplFieldAction,
  pasteNoticifyAction,
  resetNotificationTplAction,
  updateNotificationTplAction,
} from '@manyun/dc-brain.legacy.redux/actions/alarmConfigurationTemplateActions';

import styles from '../../../styles.module.less';
import { getNotifyExample, getNotifyRule } from '../../utils';
import { CONFIGURE_TYPE_MAP } from '../temp';
import { DropZoneActionWrapper, DropZoneWrapper } from './styled';

const DndItemWrapper = styled.span`
  display: inline-block;
  font-weight: 400;
  white-space: nowrap;
  text-align: center;
  background: var(--${prefixCls}-primary-color);
  height: 32px;
  line-height: 32px;
  padding: 0 15px;
  font-size: 14px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 16px;
`;

const DropWrapper = styled.span`
  display: inline-block;
`;

function renderTitle(description) {
  return description && description.indexOf('_$$_') ? description.split('_$$_')[0] : description;
}

export function DragField({ label, value, index, description }) {
  return (
    <Draggable draggableId={value} index={index}>
      {provided => (
        <Tooltip title={renderTitle(description)}>
          <DndItemWrapper
            ref={provided.innerRef}
            {...provided.draggableProps}
            {...provided.dragHandleProps}
          >
            {label}
          </DndItemWrapper>
        </Tooltip>
      )}
    </Draggable>
  );
}

function TextEditor({ onOk, text }) {
  const [txt, setTxt] = useState(text);

  return (
    <GutterWrapper flex>
      <Input
        defaultValue={text}
        size="small"
        onChange={({ target: { value } }) => {
          setTxt(value);
        }}
      />
      <Button
        size="small"
        onClick={() => {
          onOk(txt);
        }}
      >
        确定
      </Button>
    </GutterWrapper>
  );
}

function PopoverField({ id, label, text, onFieldTextOk, onDelete }) {
  const [visible, setVisible] = useState(false);
  return (
    <Popover
      key={id}
      visible={visible}
      title={label}
      content={
        <TextEditor
          text={text}
          onOk={txt => {
            if (!txt || !txt.trim()) {
              return;
            }
            if (txt.length > 50) {
              message.error('自定义文案最大可输入50个字符，请删减输入的文案字符');
              return;
            }
            onFieldTextOk(txt);
            setVisible(false);
          }}
        />
      }
      placement="topLeft"
      // getPopupContainer={() => document.querySelector('.layout_content--root')}
    >
      <Tag
        closable
        onClose={() => {
          setVisible(false);
          onDelete();
        }}
        onClick={() => {
          setVisible(prevVisible => !prevVisible);
        }}
        style={{ cursor: 'pointer' }}
      >
        {text || label}
      </Tag>
    </Popover>
  );
}

export function DropZone({
  fields,
  fieldsCode,
  onUpdateField,
  onDeleteField,
  onReset,
  notifyConfigMode,
  onCopy,
  pointId,
  resetNotificationTpl,
  onPaste,
  validateErrorId,
  fieldsExample,
  template,
  setDefaultTemplate,
}) {
  const getCustomizedTxtChangeHandler = fieldId => text => {
    onUpdateField({
      fieldId,
      attrs: { text },
      notifyConfigMode: notifyConfigMode,
      pointId: pointId,
    });
  };

  const getOnDelete = id => () => {
    onDeleteField({ id: id, notifyConfigMode: notifyConfigMode, pointId: pointId });
  };

  const onResetNotification = () => {
    if (template) {
      setDefaultTemplate();
      return;
    }
    if (notifyConfigMode === CONFIGURE_TYPE_MAP.GLOBAL) {
      onReset([]);
    } else {
      resetNotificationTpl(pointId);
    }
  };

  const onCopyNotification = fields => () => {
    onCopy(fields);
  };

  const confirm = () => {
    onPaste({ pointId: pointId, notifyConfigMode: notifyConfigMode });
  };

  return (
    <div style={{ borderColor: 'var(--border-color-special)' }}>
      <Droppable droppableId="notification" direction="horizontal">
        {provided => (
          <DropZoneWrapper
            className={styles.border}
            ref={provided.innerRef}
            {...provided.droppableProps}
            style={{
              borderColor: validateErrorId.includes(pointId) && fields.length === 0 ? 'red' : null,
            }}
          >
            {fields?.map(({ id, label, value, text }, index) => {
              if (value === 'CUSTOMIZED_TEXT') {
                return (
                  <Draggable draggableId={`notification_&&_${id}`} index={index} key={id}>
                    {provided => (
                      <DropWrapper
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        {...provided.dragHandleProps}
                        key={id}
                      >
                        <PopoverField
                          id={id}
                          label={label}
                          text={text}
                          fieldsCode={fieldsCode}
                          onFieldTextOk={getCustomizedTxtChangeHandler(id, text)}
                          onDelete={getOnDelete(id)}
                        />
                      </DropWrapper>
                    )}
                  </Draggable>
                );
              }
              return (
                <Draggable draggableId={`notification_&&_${id}`} index={index} key={id}>
                  {provided => (
                    <DropWrapper
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                      {...provided.dragHandleProps}
                      key={id}
                    >
                      <Tag
                        key={id}
                        color="blue"
                        closable
                        onClose={getOnDelete(id)}
                        style={{ cursor: 'pointer' }}
                      >
                        {label}
                      </Tag>
                    </DropWrapper>
                  )}
                </Draggable>
              );
            })}
            {provided.placeholder}
          </DropZoneWrapper>
        )}
      </Droppable>
      <DropZoneActionWrapper
        className={styles.border}
        style={{
          borderColor: validateErrorId.includes(pointId) && fields.length === 0 ? 'red' : null,
        }}
      >
        <GutterWrapper flex justifyContent="flex-end">
          <Button
            size="small"
            type="primary"
            data-type="reset-btn"
            onClick={onCopyNotification(fields)}
          >
            复制
          </Button>
          <Popconfirm title="粘贴操作会覆盖已选择的文案" onConfirm={confirm}>
            <Button size="small" type="primary" data-type="reset-btn">
              粘贴
            </Button>
          </Popconfirm>
          <Button size="small" type="primary" data-type="reset-btn" onClick={onResetNotification}>
            重置
          </Button>
        </GutterWrapper>
      </DropZoneActionWrapper>
      <span>{fieldsExample && '结果示例：' + fieldsExample}</span>
    </div>
  );
}

const mapStateToProps = (
  {
    alarmConfigurationTemplate: {
      newTemplateMess: { notifyConfigMode, globalNotificationTpl, monitorItemMap },
      validateErrorId,
    },
  },
  { pointId }
) => {
  let fields = null;
  if (notifyConfigMode === CONFIGURE_TYPE_MAP.GLOBAL) {
    fields = globalNotificationTpl.notifyRule.value;
  } else {
    fields = monitorItemMap[pointId].notifyRule.value || monitorItemMap[pointId].notifyRule;
  }
  let fieldsCode = getNotifyRule(fields);
  let fieldsExample = getNotifyExample(fields);
  return { notifyConfigMode, fields, fieldsCode, validateErrorId, fieldsExample };
};

const mapDispatchToProps = {
  onUpdateField: updateNotificationTplAction,
  onDeleteField: deleteNotificationTplFieldAction,
  onReset: alarmConfigurationTemplateActions.setGlobalNotificationTpl,
  onCopy: alarmConfigurationTemplateActions.copyNotificationTpl,
  resetNotificationTpl: resetNotificationTplAction,
  onPaste: pasteNoticifyAction,
};

export const ConnectedDropZone = connect(mapStateToProps, mapDispatchToProps)(DropZone);
