import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import { Select } from '@galiojs/awesome-antd';
import shortid from 'shortid';

import {
  alarmConfigurationTemplateActions,
  resetNotificationTplAction,
} from '@manyun/dc-brain.legacy.redux/actions/alarmConfigurationTemplateActions';

import { defaultTemplateMess } from '../constants';
import NotificationTplCfg from './notification-tpl-cfg';

const { Option } = Select;
class SingleNotificationForm extends Component {
  state = {
    template: '',
  };

  setDefaultTemplate = () => {
    const { monitorItems, monitorItemMap, id } = this.props;
    const templateMess = defaultTemplateMess.map((item, index) => {
      return {
        ...item,
        id: shortid(),
        destinationIndex: index,
        pointId: id,
      };
    });
    const newItemMap = {
      ...monitorItemMap,
      [id]: {
        ...monitorItemMap[id],
        notifyRule: {
          value: templateMess,
        },
      },
    };
    const pointItem = monitorItems.map(item => {
      if (item.id === id || item.code === id) {
        return {
          ...item,
          notifyRule: {
            value: templateMess,
          },
        };
      } else {
        return item;
      }
    });
    this.props.setSingleNotificationTpl({ pointItem, newItemMap });
  };

  render() {
    const {
      form: { getFieldDecorator },
      pointName,
      id,
      singleMess,
    } = this.props;

    return (
      <>
        <Form.Item label="监控项">{pointName && pointName[0]}</Form.Item>
        <Form.Item label="告警通知模板">
          <Select
            style={{ width: 200 }}
            allowClear
            labelInValue
            onChange={value => {
              this.setState({ template: value });
              if (value) {
                this.setDefaultTemplate();
                return;
              }
              this.props.resetNotificationTpl(id);
            }}
          >
            <Option key="1" value="标准模版">
              标准模版
            </Option>
          </Select>
        </Form.Item>
        <Form.Item label="告警通知文案">
          {getFieldDecorator('notifyRule', {
            rules: [
              {
                required: true,
                message: '告警通知文案必填!',
              },
            ],
          })(
            <NotificationTplCfg
              pointId={id}
              singleMess={singleMess}
              template={this.state.template}
              setDefaultTemplate={this.setDefaultTemplate}
            />
          )}
        </Form.Item>
      </>
    );
  }
}

const EnhancedSingleNotificationForm = Form.create({
  name: 'single_notification',
  mapPropsToFields(props) {
    return {
      notifyRule: Form.createFormField(props.notifyRule),
    };
  },
})(SingleNotificationForm);

const mapStateToProps = ({
  alarmConfigurationTemplate: {
    newTemplateMess: { monitorItemIds, monitorItemMap, monitorItems },
  },
}) => ({
  monitorItemIds,
  monitorItemMap,
  monitorItems,
});

const mapDispatchToProps = {
  setSingleNotificationTpl: alarmConfigurationTemplateActions.setSingleNotificationTpl,
  resetNotificationTpl: resetNotificationTplAction,
};

export default connect(mapStateToProps, mapDispatchToProps)(EnhancedSingleNotificationForm);
