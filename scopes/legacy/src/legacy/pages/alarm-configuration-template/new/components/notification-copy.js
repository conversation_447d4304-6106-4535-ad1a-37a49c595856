import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import get from 'lodash/get';

import { Radio } from '@manyun/base-ui.ui.radio';

import { alarmConfigurationTemplateActions } from '@manyun/dc-brain.legacy.redux/actions/alarmConfigurationTemplateActions';

import GlobalNotification from './global-config-notification';
import SingleNotification from './single-config-notification';
import { CONFIGURE_TYPE, CONFIGURE_TYPE_MAP } from './temp';

class NotificationCopy extends Component {
  onChangeConfigtureType = e => {
    this.props.saveNotifyConfigMode(e.target.value);
  };

  render() {
    const {
      newTemplateMess: { notifyConfigMode, module },
      globalNotificationRef,
      singleNotificationRef,
      nomalizedDeviceCategory,
      mode,
    } = this.props;

    const deviceTypeName =
      module.deviceType.value.name ||
      get(
        nomalizedDeviceCategory,
        [module.deviceType.value.code, 'metaName'],
        module.deviceType.value.code
      );

    return (
      <Form labelCol={{ xl: 3 }} wrapperCol={{ xl: 21 }} colon={false}>
        <Form.Item label="目标设备类型" style={{ marginBottom: 0 }}>
          {deviceTypeName}
        </Form.Item>
        <Form.Item label="配置模式" style={{ borderBottom: 0, margin: 0 }}>
          <Radio.Group value={notifyConfigMode} onChange={this.onChangeConfigtureType}>
            {CONFIGURE_TYPE.map(item => {
              return (
                <Radio.Button key={item.key} value={item.key}>
                  {item.value}
                </Radio.Button>
              );
            })}
          </Radio.Group>
        </Form.Item>
        {notifyConfigMode === CONFIGURE_TYPE_MAP.GLOBAL && (
          <GlobalNotification wrappedComponentRef={globalNotificationRef} />
        )}

        {notifyConfigMode === CONFIGURE_TYPE_MAP.INDEPENDENT && (
          <SingleNotification singleNotificationRef={singleNotificationRef} mode={mode} />
        )}
      </Form>
    );
  }
}

const mapStateToProps = ({
  alarmConfigurationTemplate: { newTemplateMess },
  common: { deviceCategory },
}) => ({
  newTemplateMess,
  nomalizedDeviceCategory: deviceCategory ? deviceCategory.normalizedList : {},
});
const mapDispatchToProps = {
  saveNotifyConfigMode: alarmConfigurationTemplateActions.saveNotifyConfigMode,
};

export default connect(mapStateToProps, mapDispatchToProps)(NotificationCopy);
