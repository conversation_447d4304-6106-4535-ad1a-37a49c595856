import React, { Component } from 'react';
import { connect } from 'react-redux';

import get from 'lodash/get';

import { Button } from '@manyun/base-ui.ui.button';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';

import * as urls from '@manyun/dc-brain.legacy.constants/urls';
import { alarmConfigurationTemplateActions } from '@manyun/dc-brain.legacy.redux/actions/alarmConfigurationTemplateActions';
import { redirectActionCreator } from '@manyun/dc-brain.legacy.redux/actions/routerActions';

import { StyledResult } from './style';

class NewResult extends Component {
  render() {
    const { resultData, finish, nomalizedDeviceCategory, mode } = this.props;
    return (
      <StyledResult
        status="success"
        title={finish.title}
        subTitle={finish.subTitle}
        extra={[
          <Button
            key="refresh-button"
            type="primary"
            onClick={() => {
              if (mode === 'new') {
                window.location.reload();
              } else if (mode === 'edit') {
                this.props.redirect(urls.ALARM_CONFIGURATION_TEMPLATE_NEW);
              } else {
                throw new Error(`mode(${mode}) not supported.`);
              }
            }}
          >
            新建模板
          </Button>,
          <Button
            key="go-back-button"
            onClick={() => {
              this.props.redirect(urls.ALARM_CONFIGURATION_TEMPLATE_LIST);
            }}
          >
            返回列表
          </Button>,
        ]}
      >
        <Descriptions style={{ width: 400, marginLeft: 'auto', marginRight: 'auto' }} column={1}>
          <Descriptions.Item label="告警模板名称">
            {mode === 'customNew' ? resultData.name : resultData.groupName}
          </Descriptions.Item>
          {mode !== 'customNew' && mode !== 'customEdit' && (
            <Descriptions.Item label="关联模板组">
              {resultData.schemeCount ? `${resultData.schemeCount}个` : '无'}
            </Descriptions.Item>
          )}
          <Descriptions.Item label="包含规则数">{resultData.itemCount}条</Descriptions.Item>
          {mode !== 'customNew' && mode !== 'customEdit' && (
            <Descriptions.Item label="所属设备分类">
              {get(nomalizedDeviceCategory, [resultData.deviceType, 'metaName'])}
            </Descriptions.Item>
          )}
          {(mode === 'customNew' || mode === 'customEdit') && (
            <Descriptions.Item label="目标机房">{resultData.idcTag}</Descriptions.Item>
          )}
        </Descriptions>
      </StyledResult>
    );
  }
}
const mapStateToProps = ({
  alarmConfigurationTemplate: { resultData },
  common: { deviceCategory },
}) => {
  return {
    resultData,
    nomalizedDeviceCategory: deviceCategory ? deviceCategory.normalizedList : {},
  };
};
const mapDispatchToProps = {
  resetState: alarmConfigurationTemplateActions.resetState4Creation,
  redirect: redirectActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(NewResult);
