import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import { Select } from '@galiojs/awesome-antd';
import get from 'lodash/get';

import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Radio } from '@manyun/base-ui.ui.radio';

import { fetchPointsByCondition } from '@manyun/resource-hub.service.fetch-points-by-condition';

import {
  AssetClassificationApiTreeSelect,
  LocationCascader,
} from '@manyun/dc-brain.legacy.components';
import {
  alarmConfigurationTemplateActions,
  searchTemplatePointConfigAction,
} from '@manyun/dc-brain.legacy.redux/actions/alarmConfigurationTemplateActions';
import { alarmConfigService } from '@manyun/dc-brain.legacy.services';

class ModuleInfo extends Component {
  changeCloneTemplate = async value => {
    if (!value) {
      this.props.saveTemplateGroupIntemplate([]);
      this.props.resetConfig([]);
    }
    const { cloneTemplate } = this.props;
    // 获取选中模板的所有信息
    const telMess = cloneTemplate.filter(tel => tel.id === value);
    this.props.changeModule(telMess[0]);
    // 请求模板下面的模板组
    const templateGroupInTemplate = await alarmConfigService.getGroupListInTemplate({
      groupId: value,
    });
    if (!templateGroupInTemplate || !templateGroupInTemplate.response) {
      return;
    }
    const templateIds = templateGroupInTemplate.response.data.map(item => {
      return item.id;
    });
    this.props.saveTemplateGroupIntemplate(templateIds);

    // 请求克隆模板下面的监控项
    this.props.searchTemplatePointConfigAction({ groupId: value });
  };

  getSelectDevice = async device => {
    const {
      form: { setFieldsValue },
      mode,
    } = this.props;
    this.props.resetConfig();
    if (!device) {
      setFieldsValue({ groupId: '', name: '', schemeIds: [] });
    }
    if (mode === 'new' && device) {
      const name = device.name + '-告警模板';
      this.props.generateModuleName(name);
      // 请求模板
      const templateData = await alarmConfigService.getTemplateListByDeviceType(device.code);
      // 请求测点
      const pointData = await fetchPointsByCondition({
        deviceType: device.code,
        dataTypeList: ['AI', 'DI'],
        isRemoveSub: true,
      });
      if (
        !templateData.response ||
        !templateData.response.data ||
        !pointData.data ||
        !pointData.data.data
      ) {
        return;
      }
      this.props.savePointIndevice(pointData.data.data.map(point => point.toApiObject()));
      this.props.saveCloneTemplate(templateData.response.data);
      // 当设备类型改变的时候 克隆模板初始化
      setFieldsValue({ groupId: '', schemeIds: [] });
    }
  };

  getCustomPoint = async value => {
    const { data, error } = await fetchPointsByCondition({
      // deviceType: selectCategoryPoint.deviceType,
      dataTypeList: ['AI', 'DI'],
      pointTypeList: ['CUSTOM'],
      spaceGuid: value[0],
    });
    if (error) {
      message.error(error);
    }
    if (data) {
      this.props.savePointIndevice(data.data.map(point => point.toApiObject()));
    }
  };

  render() {
    const form = this.props.form;
    const { getFieldDecorator } = form;
    const { module, cloneTemplate, templateGroupData, mode, nomalizedDeviceCategory } = this.props;
    return (
      <Form
        style={{ maxWidth: 450, marginLeft: 'auto', marginRight: 'auto' }}
        labelCol={{ xl: 6 }}
        wrapperCol={{ xl: 18 }}
        colon={false}
      >
        {mode === 'new' && (
          <>
            <Form.Item label="目标设备类型">
              {getFieldDecorator('deviceType', {
                rules: [
                  {
                    required: true,
                    validator: validateChildItems,
                  },
                ],
              })(
                <AssetClassificationApiTreeSelect
                  dataType={['space', 'snDevice']}
                  category="category"
                  requestOnDidMount
                  disabledDepths={[0, 1]}
                  maxTagCount={1}
                  onChange={this.getSelectDevice}
                />
              )}
            </Form.Item>
            <Form.Item label="克隆模板">
              {getFieldDecorator('groupId', {
                initialValue: module.groupId && module.groupId.value ? module.groupId.value : null,
              })(
                <Select
                  showSearch
                  allowClear
                  disabled={
                    !(module.deviceType.value
                      ? module.deviceType.value.code
                      : module.deviceType.value)
                  }
                  onChange={this.changeCloneTemplate}
                >
                  {cloneTemplate &&
                    cloneTemplate.length &&
                    cloneTemplate.map(tem => {
                      return (
                        <Select.Option key={tem.id} value={tem.id}>
                          {tem.name}
                        </Select.Option>
                      );
                    })}
                </Select>
              )}
            </Form.Item>
          </>
        )}

        {mode === 'edit' && (
          <Form.Item label="目标设备类型">
            {get(
              nomalizedDeviceCategory,
              [module.deviceType.value.code, 'metaName'],
              module.deviceType.value.code
            )}
          </Form.Item>
        )}
        {(mode === 'customNew' || mode === 'customEdit') && (
          <Form.Item label="目标机房">
            {getFieldDecorator('idcTag', {
              rules: [
                {
                  required: true,
                  message: '目标机房为必选！',
                },
              ],
            })(
              <LocationCascader
                disabled={mode === 'customEdit'}
                idcsOnly
                onChange={this.getCustomPoint}
              />
            )}
          </Form.Item>
        )}
        <Form.Item label={mode === 'customNew' || mode === 'customEdit' ? '配置名称' : '模板名称'}>
          {getFieldDecorator('name', {
            rules: [
              {
                required: true,
                whitespace: true,
                message: `${
                  mode === 'customNew' || mode === 'customEdit' ? '配置名称' : '模板名称'
                }必须填写！`,
              },
              {
                max: 60,
                message: '最多输入 60 个字符！',
              },
            ],
          })(<Input allowClear />)}
        </Form.Item>
        <Form.Item label="是否启用">
          {getFieldDecorator('available')(
            <Radio.Group>
              <Radio value="true">是</Radio>
              <Radio value="false">否</Radio>
            </Radio.Group>
          )}
        </Form.Item>
        {mode !== 'customNew' && mode !== 'customEdit' && (
          <Form.Item label="关联模板组">
            {getFieldDecorator('schemeIds')(
              <Select mode="multiple">
                {templateGroupData.length &&
                  templateGroupData.map(tel => {
                    return (
                      <Select.Option key={tel.id} value={tel.id}>
                        {tel.name}
                      </Select.Option>
                    );
                  })}
              </Select>
            )}
          </Form.Item>
        )}
        <Form.Item label="备注">
          {getFieldDecorator('description', {
            rules: [
              {
                max: 67,
                message: '最多输入 67 个字符！',
              },
            ],
          })(<Input.TextArea />)}
        </Form.Item>
      </Form>
    );
  }
}

const mapStateToProps = (
  {
    common: { deviceCategory },
    alarmConfigurationTemplate: {
      newTemplateMess: { module },
      cloneTemplate,
      templateGroupData,
    },
  },
  { mode }
) => {
  return {
    module,
    cloneTemplate,
    templateGroupData,
    nomalizedDeviceCategory: deviceCategory ? deviceCategory.normalizedList : {},
    mode,
  };
};
const mapDispatchToProps = {
  savePointIndevice: alarmConfigurationTemplateActions.savePointIndevice,
  onChange: alarmConfigurationTemplateActions.handleModuleFormChange,
  saveCloneTemplate: alarmConfigurationTemplateActions.saveCloneTemplate,
  changeModule: alarmConfigurationTemplateActions.changeModule,
  saveTemplateGroupIntemplate: alarmConfigurationTemplateActions.saveTemplateGroupIntemplate,
  generateModuleName: alarmConfigurationTemplateActions.generateModuleName,
  searchTemplatePointConfigAction,
  resetConfig: alarmConfigurationTemplateActions.resetConfig,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(
  Form.create({
    name: 'module_info',
    onFieldsChange({ mode, ...props }, changedFields) {
      props.onChange(changedFields);
    },
    mapPropsToFields({ mode, ...props }) {
      if (mode === 'customNew' || mode === 'customEdit') {
        return {
          name: Form.createFormField(props.module.name),
          idcTag: Form.createFormField(props.module.idcTag),
          available: Form.createFormField(props.module.available),
          description: Form.createFormField(props.module.description),
        };
      }
      return {
        deviceType: Form.createFormField(props.module.deviceType),
        name: Form.createFormField(props.module.name),
        groupId: Form.createFormField(props.module.groupId),
        available: Form.createFormField(props.module.available),
        schemeIds: Form.createFormField(props.module.schemeIds),
        description: Form.createFormField(props.module.description),
      };
    },
  })(ModuleInfo)
);

function validateChildItems(__, deviceTypes, callback) {
  if (!deviceTypes.code || !deviceTypes.name) {
    callback('目标设备类型为必填项！');
  } else {
    callback();
  }
}
