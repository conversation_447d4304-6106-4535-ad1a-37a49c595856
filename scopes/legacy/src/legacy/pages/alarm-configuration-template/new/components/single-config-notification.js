import React, { Component } from 'react';
import { connect } from 'react-redux';

import { List } from '@manyun/base-ui.ui.list';

import SingleNotificationForm from './single-notification-form';

class SingleNotification extends Component {
  render() {
    const { monitorItemIds, monitorItemMap, singleNotificationRef, mode } = this.props;
    return (
      <>
        <List
          style={{ marginTop: 0 }}
          itemLayout="vertical"
          dataSource={monitorItemIds}
          renderItem={id => {
            const singleMess = monitorItemMap[id];
            if (!singleMess || !singleMess.name) {
              return;
            }
            const fields = monitorItemMap[id];
            const keys = singleMess.name.value.split('_$$_');
            const pointName =
              mode === 'customNew' || mode === 'customEdit' ? keys.slice(-2) : keys.slice(-1);

            return (
              <List.Item>
                <SingleNotificationForm
                  {...fields}
                  keys={keys}
                  pointName={pointName}
                  id={id}
                  singleMess={singleMess}
                  wrappedComponentRef={singleNotificationRef.get(id)}
                />
              </List.Item>
            );
          }}
        />
      </>
    );
  }
}

const mapStateToProps = ({
  alarmConfigurationTemplate: {
    newTemplateMess: { monitorItemIds, monitorItemMap },
  },
}) => ({
  monitorItemIds,
  monitorItemMap,
});

const mapDispatchToProps = {};

export default connect(mapStateToProps, mapDispatchToProps)(SingleNotification);
