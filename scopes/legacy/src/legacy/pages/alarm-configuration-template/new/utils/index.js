import { CUSTOMIZED_NOTIFICATION_TEXT } from '@manyun/dc-brain.legacy.constants';

export function getNotifyRule(notificationTpl) {
  return notificationTpl
    .map(({ value, text }) => {
      if (value === CUSTOMIZED_NOTIFICATION_TEXT) {
        return text;
      }
      return '${' + value + '}';
    })
    .join('');
}

export function getNotifyExample(notificationTpl) {
  return notificationTpl
    .map(({ value, description, text, label }) => {
      if (value === CUSTOMIZED_NOTIFICATION_TEXT) {
        return text || label;
      }
      return description?.split('_$$_')?.[1] || description;
    })
    .join(' ');
}
