import React, { Component } from 'react';
import { connect } from 'react-redux';

import {
  GutterWrapper,
  TinyCard,
  TinyDescriptions,
  TinyTable,
} from '@manyun/dc-brain.legacy.components';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import { getConfigInfoActionCreator } from '@manyun/dc-brain.legacy.redux/actions/inventoryConfigActions';
import { getDeviceTypeName } from '@manyun/dc-brain.legacy.utils/ticket';

const getColumns = ctx => [
  {
    title: '一级分类',
    dataIndex: 'topCategory',
    render: (_, record) =>
      getDeviceTypeName(record.deviceType.slice(0, 1), ctx.props.normalizedList),
  },
  {
    title: '二级分类',
    dataIndex: 'secondCategory',
    render: (_, record) =>
      getDeviceTypeName(record.deviceType.slice(0, 3), ctx.props.normalizedList),
  },
  {
    title: '三级分类',
    dataIndex: 'deviceType',
    render: (_, record) => getDeviceTypeName(record.deviceType, ctx.props.normalizedList),
  },
];

class InventoryConfigDetail extends Component {
  componentDidMount() {
    this.props.syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });
    this.props.getConfigInfo(this.props.match.params.id);
  }

  render() {
    const { detail } = this.props;

    return (
      <GutterWrapper mode="vertical">
        <TinyCard title="基本信息" bordered={false}>
          <TinyDescriptions
            column={1}
            descriptionsItems={[
              {
                label: 'SOP名称',
                value: detail.configName,
              },
              {
                label: '盘点方式',
                value: detail.inventoryType ? detail.inventoryType.desc : '',
              },
              {
                label: '盘点子方式',
                value: detail.inventorySubType ? detail.inventorySubType.desc : '',
              },
            ]}
          />
        </TinyCard>
        <TinyCard title="盘点内容" style={{ marginBottom: 40 }}>
          <TinyTable
            style={{ marginBottom: 40 }}
            rowKey="deviceType"
            columns={getColumns(this)}
            dataSource={
              detail.deviceTypes
                ? detail.deviceTypes.map(item => {
                    return {
                      deviceType: item,
                    };
                  })
                : []
            }
          />
        </TinyCard>
      </GutterWrapper>
    );
  }
}

const mapStateToProps = ({ inventoryConfig: { detail }, common: { deviceCategory } }) => {
  return {
    detail,
    normalizedList: deviceCategory?.normalizedList,
  };
};
const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
  getConfigInfo: getConfigInfoActionCreator,
};
export default connect(mapStateToProps, mapDispatchToProps)(InventoryConfigDetail);
