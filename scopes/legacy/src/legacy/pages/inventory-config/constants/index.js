// 盘点方式
export const INVENTORY_TYPE_KEY_MAP = {
  MANUAL_INVENTORY: 'MA<PERSON>AL_INVENTORY',
  AUTO_INVENTORY: 'AUTO_INVENTORY',
};
export const INVENTORY_TYPE_TEXT_MAP = {
  [INVENTORY_TYPE_KEY_MAP.MANUAL_INVENTORY]: '人工盘点',
  [INVENTORY_TYPE_KEY_MAP.AUTO_INVENTORY]: '自动盘点',
};
export const INVENTORY_TYPE_OPTIONS = [
  {
    value: INVENTORY_TYPE_KEY_MAP.MANUAL_INVENTORY,
    label: INVENTORY_TYPE_TEXT_MAP[INVENTORY_TYPE_KEY_MAP.MANUAL_INVENTORY],
  },
  {
    value: INVENTORY_TYPE_KEY_MAP.AUTO_INVENTORY,
    label: INVENTORY_TYPE_TEXT_MAP[INVENTORY_TYPE_KEY_MAP.AUTO_INVENTORY],
  },
];

// 盘点子方式
export const INVENTORY_CHILDREN_TYPE_KEY_MAP = {
  DISPLAY: 'DISPLAY',
  IMPLICIT: 'IMPLICIT',
};
export const INVENTORY_CHILDREN_TYPE_TEXT_MAP = {
  [INVENTORY_CHILDREN_TYPE_KEY_MAP.DISPLAY]: '明盘',
  [INVENTORY_CHILDREN_TYPE_KEY_MAP.IMPLICIT]: '盲盘',
};
export const INVENTORY_CHILDREN_TYPE_OPTIONS = [
  {
    value: INVENTORY_CHILDREN_TYPE_KEY_MAP.DISPLAY,
    label: INVENTORY_CHILDREN_TYPE_TEXT_MAP[INVENTORY_CHILDREN_TYPE_KEY_MAP.DISPLAY],
  },
  {
    value: INVENTORY_CHILDREN_TYPE_KEY_MAP.IMPLICIT,
    label: INVENTORY_CHILDREN_TYPE_TEXT_MAP[INVENTORY_CHILDREN_TYPE_KEY_MAP.IMPLICIT],
  },
];
