import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import { Select } from '@galiojs/awesome-antd';

import { Button } from '@manyun/base-ui.ui.button';
import { Explanation } from '@manyun/base-ui.ui.explanation';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Radio } from '@manyun/base-ui.ui.radio';

import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import { CONFIG_RANGE_KEY_MAP, ConfigRangeType } from '@manyun/ticket.model.task';

import { FooterToolBar, GutterWrapper, TinyCard } from '@manyun/dc-brain.legacy.components';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  createConfigActionCreator,
  editConfigActionCreator,
  getConfigInfoActionCreator,
  inventoryConfigActions,
} from '@manyun/dc-brain.legacy.redux/actions/inventoryConfigActions';

import {
  INVENTORY_CHILDREN_TYPE_OPTIONS,
  INVENTORY_TYPE_KEY_MAP,
  INVENTORY_TYPE_OPTIONS,
} from '../constants';
import AddConfigButton from './components/add-config-button';

class InventoryConfigCreate extends Component {
  state = {
    deviceTypes: [],
  };

  componentDidMount() {
    this.props.syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });
    if (this.props.mode === 'edit' || this.props.mode === 'copy') {
      this.props.getConfigInfo(this.props.match.params.id);
    }
  }

  componentDidUpdate(prevProps) {
    const {
      fieldValues: { deviceTypes },
    } = this.props;
    if (
      (this.props.mode === 'edit' || this.props.mode === 'copy') &&
      prevProps.fieldValues.deviceTypes !== deviceTypes
    ) {
      this.setState({
        deviceTypes: deviceTypes.value?.map(item => {
          return item.thirdCategorycode;
        }),
      });
    }
  }

  handleOk = event => {
    event.preventDefault();
    const { deviceTypes } = this.state;
    this.props.form.validateFields((err, values) => {
      if (err) {
        return;
      }
      if (!deviceTypes.length) {
        message.error('请添加盘点内容！');
        return;
      }
      if (this.props.mode === 'new' || this.props.mode === 'copy') {
        this.props.createConfig({
          ...values,
          mode: this.props.mode,
          configName: values.configName !== null ? values.configName.trim() : null,
          deviceTypes,
        });
      } else {
        this.props.editConfig({
          ...values,
          configId: this.props.fieldValues.id,
          configName: values.configName !== null ? values.configName.trim() : null,
          deviceTypes,
        });
      }
    });
  };

  setDeviceTypes = deviceTypes => {
    this.setState({ deviceTypes });
  };

  render() {
    const {
      form: { getFieldDecorator, getFieldValue },
    } = this.props;
    const { deviceTypes } = this.state;

    return (
      <GutterWrapper mode="vertical">
        <TinyCard title="基本信息" bordered={false}>
          <Form labelCol={{ xl: 3 }} wrapperCol={{ xl: 21 }} style={{ width: 848 }} colon={false}>
            <Form.Item label="适用范围">
              {getFieldDecorator('configType', {
                rules: [{ required: true, message: '适用范围为必选项！' }],
              })(
                <Radio.Group style={{ width: 218 }}>
                  {CONFIG_RANGE_KEY_MAP.map(({ label, value }) => {
                    return (
                      <Radio key={value} value={value}>
                        {label}
                      </Radio>
                    );
                  })}
                </Radio.Group>
              )}
            </Form.Item>
            {getFieldValue('configType') === ConfigRangeType.Block && (
              <Form.Item label="属地楼栋">
                {getFieldDecorator('blockGuid', {
                  rules: [{ required: true, message: '属地楼栋为必选项！' }],
                })(
                  <LocationTreeSelect
                    style={{ width: 218 }}
                    authorizedOnly
                    disabledTypes={['IDC']}
                    allowClear
                  />
                )}
              </Form.Item>
            )}
            <Form.Item label="SOP类型">
              {getFieldDecorator('inventoryType', {
                rules: [{ required: true, message: 'SOP类型为必选项！' }],
              })(
                <Select style={{ width: 218 }}>
                  {INVENTORY_TYPE_OPTIONS.map(({ value, label }) => {
                    return (
                      <Select.Option key={value} value={value}>
                        {label}
                      </Select.Option>
                    );
                  })}
                </Select>
              )}
              <Explanation
                iconType="question"
                tooltip={{
                  title: (
                    <>
                      <p>配置只有在确保已录入盘点对象情况下才可生效。</p>
                      <p>自动盘点：系统通过线上进行设备实物盘点。</p>
                      <p>人工盘点：人工通过移动端扫码设备实物二维码进行盘点。</p>
                    </>
                  ),
                }}
              />
            </Form.Item>
            {getFieldValue('inventoryType') === INVENTORY_TYPE_KEY_MAP.MANUAL_INVENTORY && (
              <Form.Item label="盘点方式">
                {getFieldDecorator('inventorySubType', {
                  rules: [{ required: true, message: '盘点方式为必选项！' }],
                })(
                  <Select style={{ width: 218 }}>
                    {INVENTORY_CHILDREN_TYPE_OPTIONS.map(({ value, label }) => {
                      return (
                        <Select.Option key={value} value={value}>
                          {label}
                        </Select.Option>
                      );
                    })}
                  </Select>
                )}
                <Explanation
                  iconType="question"
                  tooltip={{
                    title: (
                      <>
                        <p>明盘：盘点工单中展示需盘点资产清单。</p>
                        <p>
                          盲盘：有编号资产不展示需盘点资产清单，无编号资产展示不包括数量在内的资产清单。说明：无编号资产仅支持盲盘。
                        </p>
                      </>
                    ),
                  }}
                />
              </Form.Item>
            )}
            <Form.Item label="SOP名称">
              {getFieldDecorator('configName', {
                rules: [
                  { required: true, whitespace: true, message: 'SOP名称为必填项！' },
                  {
                    max: 15,
                    message: '最多输入 15 个字符！',
                  },
                ],
              })(<Input allowClear style={{ width: 218 }} />)}
            </Form.Item>
          </Form>
        </TinyCard>
        <TinyCard title="盘点内容" style={{ marginBottom: 40 }}>
          <AddConfigButton
            setDeviceTypes={this.setDeviceTypes}
            deviceTypes={deviceTypes}
            inventoryType={getFieldValue('inventoryType')}
            inventorySubType={getFieldValue('inventorySubType')}
          />
        </TinyCard>
        <FooterToolBar>
          <GutterWrapper>
            <Button type="primary" onClick={this.handleOk}>
              确认
            </Button>
          </GutterWrapper>
        </FooterToolBar>
      </GutterWrapper>
    );
  }
}

const mapStateToProps = ({ inventoryConfig: { edit, create } }, { mode }) => {
  let fieldValues = create;
  if (mode === 'edit' || mode === 'copy') {
    fieldValues = edit;
  }
  return {
    fieldValues,
  };
};

const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
  upDateCreateOptionValues: inventoryConfigActions.upDateCreateOptionValues,
  createConfig: createConfigActionCreator,
  getConfigInfo: getConfigInfoActionCreator,
  upDateEditOptionValues: inventoryConfigActions.upDateEditOptionValues,
  editConfig: editConfigActionCreator,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(
  Form.create({
    onFieldsChange(props, changedFields) {
      props.upDateCreateOptionValues(changedFields);
    },
    mapPropsToFields(props) {
      return {
        configName: Form.createFormField(props.fieldValues.configName),
        blockGuid: Form.createFormField(props.fieldValues.blockGuid),
        configType: Form.createFormField(props.fieldValues.configType),
        inventoryType: Form.createFormField(props.fieldValues.inventoryType),
        inventorySubType: Form.createFormField(props.fieldValues.inventorySubType),
      };
    },
  })(InventoryConfigCreate)
);
