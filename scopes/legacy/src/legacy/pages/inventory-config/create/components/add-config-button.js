import React, { Component } from 'react';
import { connect } from 'react-redux';

import get from 'lodash/get';
import omit from 'lodash/omit';
import uniq from 'lodash/uniq';

import { Button } from '@manyun/base-ui.ui.button';
import { Collapse } from '@manyun/base-ui.ui.collapse';
import { List } from '@manyun/base-ui.ui.list';

import {
  ApiTree,
  GutterWrapper,
  TinyCard,
  TinyModal,
  TinyTable,
} from '@manyun/dc-brain.legacy.components';
import { DEVICE_SPACE_TYPE_KEY_MAP } from '@manyun/dc-brain.legacy.constants/enums/deviceType';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import { flattenTreeData } from '@manyun/dc-brain.legacy.utils';
import { getDeviceTypeName } from '@manyun/dc-brain.legacy.utils/ticket';

import { INVENTORY_CHILDREN_TYPE_KEY_MAP, INVENTORY_TYPE_KEY_MAP } from '../../constants';

const getColumns = ctx => [
  {
    title: '一级分类',
    dataIndex: 'topCategory',
    render: (_, record) =>
      getDeviceTypeName(
        get(record, ['computedNodeProps', 'parentKeys', 0]).split('-')[0],
        ctx.props.normalizedList
      ),
  },
  {
    title: '二级分类',
    dataIndex: 'secondCategory',
    render: (_, record) =>
      getDeviceTypeName(
        get(record, ['computedNodeProps', 'parentKeys', 1]).split('-')[0],
        ctx.props.normalizedList
      ),
  },
  {
    title: '三级分类',
    dataIndex: 'deviceType',
    render: (_, record) => getDeviceTypeName(record.key?.split('-')?.[0], ctx.props.normalizedList),
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    render: (__, record) => (
      <Button
        compact
        type="link"
        onClick={() =>
          ctx.onDeleteChildItem(
            get(record, ['computedNodeProps', 'parentKeys', 0]),
            record.key,
            true
          )
        }
      >
        移除
      </Button>
    ),
  },
];

const initialState = {
  checkedNodes: [],
  checkedItemKeys: [],
  panelKeys: [],
  activeKey: [],
  childItemsMap: {},
  configList: [],
  expandedKeys: [],
};

class AddConfigButton extends Component {
  state = {
    ...initialState,
    clickOk: false,
  };

  componentDidMount() {
    this.props.syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });
  }

  componentDidUpdate(prevProps, prevState) {
    const { configList, panelKeys, modelVisible } = this.state;
    const { inventoryType, inventorySubType, deviceTypes } = this.props;

    if (panelKeys && panelKeys !== prevState.panelKeys) {
      this.setState({ activeKey: panelKeys });
    }

    if (inventoryType && prevProps.inventoryType !== inventoryType) {
      this.setState({ ...initialState });
      return;
    }
    if (inventorySubType && prevProps.inventorySubType !== inventorySubType) {
      this.setState({ ...initialState });
      return;
    }

    if (modelVisible && prevState.modelVisible !== modelVisible) {
      this.setState(omit(initialState, 'configList', 'childItemsMap'));
      return;
    }

    if (prevState.configList !== configList) {
      this.props.setDeviceTypes(
        configList.map(item => {
          return item.key.split('-')[0];
        })
      );
    }
    if (!prevProps.deviceTypes.length && deviceTypes.length) {
      const panelKeys = uniq(
        deviceTypes.map(item => {
          return item.slice(0, 1);
        })
      );
      const childItemsMap = {};
      panelKeys.forEach(item => {
        if (!childItemsMap[item]) {
          childItemsMap[item] = [];
        }
        deviceTypes.forEach(i => {
          if (i.indexOf(item) === 0) {
            childItemsMap[item].push({
              computedNodeProps: {
                parentKeys: [i.slice(0, 1) + '-C0', i.slice(0, 3) + '-C1'],
              },
              key: i + '-C2',
            });
          }
        });
      });

      const configList = [];
      panelKeys.forEach(item => {
        configList.push(...childItemsMap[item]);
      });

      this.setState({
        childItemsMap,
        configList,
      });
    }
  }

  editModelVisible = () => {
    this.setState({ modelVisible: !this.state.modelVisible });
  };

  handleAdd = () => {
    const { checkedNodes, configList } = this.state;
    this.setState({
      configList: [
        ...configList,
        ...checkedNodes.filter(item => get(item, ['computedNodeProps', 'parentKeys']).length === 2),
      ],
    });
    this.editModelVisible();
  };

  onDeleteChildItem = (topCategory, childItemKey, shouldUpdate) => {
    const { childItemsMap, panelKeys, configList, checkedItemKeys, checkedNodes } = this.state;
    if (shouldUpdate) {
      this.setState({ configList: configList.filter(item => item.key !== childItemKey) });
      return;
    }
    const childItems = childItemsMap[topCategory];
    const list = childItems.filter(item => item.key !== childItemKey);

    this.setState({
      childItemsMap: {
        ...childItemsMap,
        [topCategory]: list,
      },
      checkedItemKeys: checkedItemKeys
        .filter(item => item !== childItemKey)
        .filter(i => i !== childItemKey.substring(0, 3) + '-C1')
        .filter(i => i !== childItemKey.substring(0, 1) + '-C0'),
      checkedNodes: checkedNodes.filter(item => item.key !== childItemKey),
    });
    if (!list.length) {
      this.setState({ panelKeys: panelKeys.filter(item => item !== topCategory) });
    }
  };

  render() {
    const {
      configList,
      modelVisible,
      checkedItemKeys,
      panelKeys,
      childItemsMap,
      expandedKeys,
      activeKey,
    } = this.state;
    const { normalizedList, treeList, inventorySubType, inventoryType } = this.props;

    return (
      <GutterWrapper mode="vertical">
        <TinyTable
          scroll={{ x: 'max-content' }}
          style={{ marginBottom: 40 }}
          rowKey="key"
          columns={getColumns(this)}
          dataSource={configList}
          actions={
            <Button type="primary" onClick={this.editModelVisible}>
              创建盘点项
            </Button>
          }
        />
        <TinyModal
          width="60%"
          title="创建盘点项"
          destroyOnClose
          visible={modelVisible}
          maskClosable={false}
          onCancel={this.editModelVisible}
          footer={
            <>
              <Button key="cancel" onClick={this.editModelVisible}>
                取消
              </Button>
              <Button key="ok" type="primary" onClick={this.handleAdd}>
                确定
              </Button>
            </>
          }
        >
          <GutterWrapper flex>
            <TinyCard style={{ flex: 3 }} bordered>
              <ApiTree
                treeNodeFilterProp="metaName"
                showSearch
                expandedKeys={expandedKeys}
                autoExpandParent
                onExpandedKeysUpdate={expandedKeys => this.setState({ expandedKeys })}
                onExpand={expandedKeys => this.setState({ expandedKeys })}
                treeStyle={{
                  maxHeight: (500 / 900) * window.innerHeight,
                  overflowY: 'auto',
                }}
                checkable
                selectable={false}
                checkedKeys={checkedItemKeys}
                fieldNames={{
                  key: ({ metaCode, metaType }) => generateNodeKey(metaCode, metaType),
                  title: 'metaName',
                  parentKey: ({ metaCode, metaType }) => generateNodeKey(metaCode, metaType),
                  disableCheckbox: ({ metaCode, metaType }) => {
                    if (
                      configList.filter(item => item.key === generateNodeKey(metaCode, metaType))
                        .length
                    ) {
                      return true;
                    }
                    return false;
                  },
                }}
                filterLeafTreeNode={({ metaType }) => metaType === 'C2'}
                dataService={() => {
                  let filterList = [];
                  if (
                    inventorySubType === INVENTORY_CHILDREN_TYPE_KEY_MAP.IMPLICIT &&
                    inventoryType === INVENTORY_TYPE_KEY_MAP.MANUAL_INVENTORY
                  ) {
                    filterList =
                      treeList &&
                      treeList.filter(item => item.metaStyle === DEVICE_SPACE_TYPE_KEY_MAP.DEVICE);
                  } else {
                    filterList =
                      treeList &&
                      treeList.filter(
                        item =>
                          item.metaStyle === DEVICE_SPACE_TYPE_KEY_MAP.DEVICE &&
                          item.numbered === true
                      );
                  }
                  return Promise.resolve({
                    treeList: filterList,
                    parallelList: flattenTreeData(filterList),
                  });
                }}
                onCheck={async (checkedKeys, { checkedNodes }) => {
                  this.setState({ checkedItemKeys: checkedKeys, checkedNodes });
                  if (!checkedNodes.length) {
                    this.setState({ panelKeys: [] });
                    return;
                  }
                  const deviceTypes = [];
                  const childItemsMap = {};
                  checkedNodes.forEach(item => {
                    const { parentKeys } = item.computedNodeProps || {};
                    let deviceType = parentKeys?.[0] || item.key;
                    deviceTypes.push(deviceType);
                    if (!parentKeys?.[1]) {
                      return;
                    }
                    childItemsMap[deviceType] = [...(childItemsMap[deviceType] || []), item];
                  });
                  this.setState({
                    panelKeys: uniq(deviceTypes),
                    childItemsMap,
                  });
                }}
              />
            </TinyCard>
            <TinyCard style={{ flex: 5 }}>
              <Collapse
                bordered={false}
                activeKey={activeKey}
                onChange={key => this.setState({ activeKey: key })}
                style={{ maxHeight: (500 / 900) * window.innerHeight, overflowY: 'auto' }}
              >
                {Array.isArray(panelKeys) &&
                  panelKeys.map(topCategory => (
                    <Collapse.Panel
                      key={topCategory}
                      header={get(normalizedList, [topCategory.split('-')[0], 'metaName'])}
                    >
                      <List
                        size="small"
                        itemLayout="horizontal"
                        dataSource={childItemsMap[topCategory]}
                        renderItem={item => (
                          <List.Item
                            style={{ paddingTop: 4, paddingBottom: 4 }}
                            actions={[
                              <Button
                                compact
                                type="link"
                                onClick={() => {
                                  this.onDeleteChildItem(topCategory, item.key);
                                }}
                              >
                                移除
                              </Button>,
                            ]}
                          >
                            {getDeviceTypeName(
                              item.computedNodeProps.parentKeys?.[1]?.split('-')[0],
                              normalizedList
                            ) +
                              '-' +
                              getDeviceTypeName(item.key.split('-')[0], normalizedList)}
                          </List.Item>
                        )}
                      />
                    </Collapse.Panel>
                  ))}
              </Collapse>
            </TinyCard>
          </GutterWrapper>
        </TinyModal>
      </GutterWrapper>
    );
  }
}

const mapStateToProps = ({ common: { deviceCategory } }) => {
  return {
    normalizedList: deviceCategory?.normalizedList,
    treeList: deviceCategory?.treeList,
  };
};
const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(AddConfigButton);

function generateNodeKey(metaCode, metaType) {
  return `${metaCode}-${metaType}`;
}
