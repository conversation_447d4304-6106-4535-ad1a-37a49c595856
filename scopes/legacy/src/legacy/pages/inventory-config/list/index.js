import React, { useState } from 'react';

import { Card } from '@manyun/base-ui.ui.card';
import { Space } from '@manyun/base-ui.ui.space';

import { ConfigRangeType } from '@manyun/ticket.model.task';
import { ConfigRangeTypeRadioButton } from '@manyun/ticket.ui.config-range-type-radio-button';

import DataCard from './components/data-card';
import SearchCard from './components/search-card';

export function InventoryConfigList() {
  const [configType, setConfigType] = useState(ConfigRangeType.Block);

  return (
    <Card
      title="盘点SOP配置"
      extra={
        <ConfigRangeTypeRadioButton
          configRangeType={configType}
          onChange={setConfigType}
          ticketPrefix="SOP"
        />
      }
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        <SearchCard configType={configType} />
        <DataCard configType={configType} />
      </Space>
    </Card>
  );
}

export default InventoryConfigList;
