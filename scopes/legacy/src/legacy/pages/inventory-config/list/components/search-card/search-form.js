import React, { useEffect } from 'react';
import { connect } from 'react-redux';

import { FiltersForm, Form, Select } from '@galiojs/awesome-antd';

import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Input } from '@manyun/base-ui.ui.input';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import { ConfigRangeType } from '@manyun/ticket.model.task';

// import { treeDataService } from '@manyun/dc-brain.legacy.services';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  getDataActionCreator,
  inventoryConfigActions,
  resetSearchValuesActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/inventoryConfigActions';

import { INVENTORY_TYPE_OPTIONS } from '../../../constants';

export function SearchForm({
  onReset,
  onSearch,
  configType,
  syncCommonData,
  updateSearchValues,
  searchValues,
}) {
  const [form] = Form.useForm();
  useEffect(() => {
    syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });
  }, [syncCommonData]);

  const items = [
    {
      label: 'SOP类型',
      name: 'inventoryType',
      control: <Select options={INVENTORY_TYPE_OPTIONS} allowClear />,
    },
    { label: 'SOP名称', name: 'configName', control: <Input allowClear /> },

    {
      label: '更新时间',
      name: 'updateTimeRange',
      span: 2,
      control: (
        <DatePicker.RangePicker
          format="YYYY-MM-DD HH:mm:ss"
          showTime
          placeholder={['开始时间', '结束时间']}
        />
      ),
    },
    {
      label: '更新人',
      name: 'modifierId',
      control: <UserSelect allowClear />,
    },
    {
      label: '创建时间',
      name: 'createTimeRange',
      span: 2,
      control: (
        <DatePicker.RangePicker
          format="YYYY-MM-DD HH:mm:ss"
          showTime
          placeholder={['开始时间', '结束时间']}
        />
      ),
    },
  ];

  return (
    <FiltersForm
      form={form}
      items={
        configType === ConfigRangeType.Block
          ? [
              {
                label: '机房楼栋',
                name: 'blockGuids',
                control: (
                  <LocationTreeSelect
                    style={{ width: 218 }}
                    multiple
                    authorizedOnly
                    disabledTypes={['IDC']}
                    allowClear
                  />
                ),
              },
              ...items,
            ]
          : items
      }
      fields={Object.keys(searchValues).map(name => {
        const field = searchValues[name];
        return {
          ...field,
          // name 为数组形式
          name: name.split('.'),
        };
      })}
      onFieldsChange={changedFields => {
        updateSearchValues(
          changedFields.reduce((mapper, field) => {
            // field.name 为数组形式，老代码需要的是字符串形式
            const name = field.name.join('.');
            mapper[name] = {
              ...field,
              name,
            };
            return mapper;
          }, {})
        );
      }}
      onSearch={() => onSearch({ shouldResetPageNum: true, configType })}
      onReset={() => onReset(configType)}
    />
  );
}

const mapStateToProps = ({ inventoryConfig: { searchValues } }) => {
  return { searchValues };
};

const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
  updateSearchValues: inventoryConfigActions.updateSearchValues,
  onReset: resetSearchValuesActionCreator,
  onSearch: getDataActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(SearchForm);
