import React, { useCallback, useEffect } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Divider } from '@manyun/base-ui.ui.divider';

import { ConfigRangeType } from '@manyun/ticket.model.task';
import {
  generateInventoryConfigDetailRoutePath,
  generateInventoryCopyLocation,
} from '@manyun/ticket.route.ticket-routes';

import { Ellipsis, TinyTable, UserLink } from '@manyun/dc-brain.legacy.components';
import {
  deleteConfigActionCreator,
  getDataActionCreator,
  setPaginationThenGetDataActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/inventoryConfigActions';
import * as generateUrls from '@manyun/dc-brain.legacy.utils/urls';

export function DataTable({
  data,
  total,
  pageNum,
  pageSize,
  getData,
  setPagination,
  deleteConfig,
  deviceCategory,
  loading,
  configType,
}) {
  useEffect(() => {
    getData({ shouldResetPageNum: true, configType });
  }, [configType, getData]);

  const paginationChangeHandler = useCallback(
    (current, size) => {
      setPagination({ pageNum: current, pageSize: size, configType: configType });
    },
    [setPagination, configType]
  );

  return (
    <TinyTable
      rowKey="id"
      loading={loading}
      columns={getColumns({ deleteConfig, deviceCategory, configType })}
      align="left"
      dataSource={data}
      actions={[
        <Button key="create" type="primary" href={generateUrls.generateInventoryCreateLocation()}>
          新建SOP
        </Button>,
      ]}
      pagination={{
        total,
        current: pageNum,
        pageSize,
        onChange: paginationChangeHandler,
      }}
    />
  );
}

const mapStateToProps = ({
  inventoryConfig: {
    data,
    total,
    pagination: { pageNum, pageSize },
    loading,
  },
  common: { deviceCategory },
}) => {
  return {
    data,
    total,
    pageNum,
    pageSize,
    deviceCategory,
    loading,
  };
};
const mapDispatchToProps = {
  getData: getDataActionCreator,
  setPagination: setPaginationThenGetDataActionCreator,
  deleteConfig: deleteConfigActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(DataTable);

const getColumns = ({ deleteConfig, deviceCategory, configType }) => {
  const columns = [
    {
      title: 'SOP名称',
      dataIndex: 'configName',
      render(configName, record) {
        return (
          <Link
            type="link"
            target="_blank"
            to={generateInventoryConfigDetailRoutePath({
              id: record.id,
              name: record.configName,
            })}
          >
            <Ellipsis lines={1} tooltip>
              {configName}
            </Ellipsis>
          </Link>
        );
      },
    },
    {
      title: 'SOP类型',
      dataIndex: ['inventoryType', 'desc'],
    },
    {
      title: '盘点方式',
      dataIndex: ['inventorySubType', 'desc'],
    },
    {
      title: '盘点项条数',
      dataIndex: 'deviceTypes',
      render: deviceTypes => deviceTypes.length,
    },
    {
      title: '创建时间',
      dataIndex: 'gmtCreate',
      dataType: 'datetime',
    },
    {
      title: '更新时间',
      dataIndex: 'gmtModified',
      dataType: 'datetime',
    },

    {
      title: '更新人',
      dataIndex: 'modifierId',
      render: (modifierId, record) => (
        <UserLink userId={modifierId} userName={record.modifierName} />
      ),
    },
    {
      title: '操作',
      dataIndex: 'inspectId',
      fixed: 'right',
      render: (__, record) => (
        <span>
          <Link
            target="_blank"
            to={generateUrls.generateInventoryEditocation({
              id: record.id,
              name: record.configName,
            })}
          >
            编辑
          </Link>
          <Divider type="vertical" />
          <Link
            target="_blank"
            to={generateInventoryCopyLocation({
              id: record.id,
              name: record.configName,
            })}
          >
            复制
          </Link>
          <Divider type="vertical" />
          <DeleteConfirm
            variant="popconfirm"
            targetName={record.configName}
            onOk={() => deleteConfig({ configId: record.id, configType })}
          >
            <Button type="link" compact>
              删除
            </Button>
          </DeleteConfirm>
        </span>
      ),
    },
  ];

  return configType === ConfigRangeType.Block
    ? [{ title: '楼栋', dataIndex: 'blockGuid' }, ...columns]
    : columns;
};
