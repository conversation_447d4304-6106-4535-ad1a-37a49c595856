import React, { Component, useEffect, useState } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import PageHeader from 'antd/es/page-header';
import moment from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { message } from '@manyun/base-ui.ui.message';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import { Typography } from '@manyun/base-ui.ui.typography';

import { USER_GROUPS_ROUTE_PATH, generateRoleRoutePath } from '@manyun/auth-hub.route.auth-routes';
import { fetchInfluenceResources } from '@manyun/auth-hub.service.fetch-influence-resources';
import { User } from '@manyun/auth-hub.ui.user';

import { <PERSON><PERSON>Wrapper, TinyCard } from '@manyun/dc-brain.legacy.components';
import { redirectActionCreator } from '@manyun/dc-brain.legacy.redux/actions/routerActions';
import {
  fetchCreateUserGroup,
  fetchRemoveResource,
  fetchRemoveRole,
  fetchRemoveUser,
  fetchUserGroupManageDetail,
  fetchUserGroupsAssociatedResource,
  fetchUserGroupsAssociatedRoles,
  fetchUserGroupsAssociatedUser,
  userGroupManageActions,
} from '@manyun/dc-brain.legacy.redux/actions/userGroupManageActions';
import { getLocationSearchMap } from '@manyun/dc-brain.legacy.utils';
import {
  generateBasicResourcesBuildingUrlLocation,
  generateBasicResourcesIdcUrl,
} from '@manyun/dc-brain.legacy.utils/urls';

import { resourceOptions, roleOptions, userOptions } from '../component/columns';
import CreateUserGroup from '../component/create-user-group';
import ManageTable from '../component/manage-table';
import UserGroupAddResource from '../component/user-group-add-resource';
import UserGroupAddRoles from '../component/user-group-add-roles';
import UserGroupAddUsers from '../component/user-group-add-users';

const { Paragraph } = Typography;

const userColumns = ctx => {
  const { basicInfo } = ctx.props;
  const groupId = basicInfo.id;
  return [
    {
      title: '用户名称',
      dataIndex: 'userName',
      render: (text, { id }) => <User.Link id={id} name={text} />,
    },
    {
      title: '用户ID',
      dataIndex: 'loginName',
      render: (text, { id }) => <User.Link id={id} name={text} />,
    },
    {
      title: '备注',
      dataIndex: 'remarks',
      ellipsis: true,
    },
    {
      title: '加入时间',
      dataType: 'datetime',
      dataIndex: 'gmtCreate',
    },
    {
      title: '创建人',
      dataIndex: 'creatorName',
      render: (text, { creatorId }) => <User.Link id={creatorId} name={text} />,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      fixed: 'right',
      render: (_, { id }) => {
        return (
          <DeleteConfirm
            variant="popconfirm"
            title={<DeleteConfirmTextContent groupId={groupId} userId={id} />}
            onOk={() => ctx.removeUser(id)}
          >
            <Button type="link" compact>
              移出用户组
            </Button>
          </DeleteConfirm>
        );
      },
    },
  ];
};

const DeleteConfirmTextContent = (groupId, userId) => {
  const [resourceNameList, setResourceNameList] = useState([]);
  useEffect(() => {
    const getUserInfluenceResources = async (groupId, userId) => {
      const { data, error } = await fetchInfluenceResources(groupId, userId);
      if (error) {
        message.error(error.message);
        return;
      }
      setResourceNameList(generateDisplayResouces(data.data));
    };
    getUserInfluenceResources(groupId, userId);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  return (
    <div style={{ width: 400 }}>
      <Typography.Text>是否确认移出用户组？</Typography.Text>
      {resourceNameList && resourceNameList?.length !== 0 && (
        <Typography.Paragraph
          type="secondary"
          ellipsis={{ rows: 3 }}
        >{`移出后用户将被取消资源：${resourceNameList}的权限，无法添加到上述资源对应的班组中，请谨慎操作！
PS：移除后，用户已排班班次，仍可正常考勤打卡。`}</Typography.Paragraph>
      )}
    </div>
  );
};
const generateDisplayResouces = resources => {
  if (resources && resources.length) {
    const resourcesLength = resources.length;
    if (resourcesLength > 3) {
      return `${resources[0]}|${resources[1]}|${resources[2]}...(共计${resources.length}个)`;
    } else {
      let displayResources = '';
      for (let i = 0; i < resourcesLength - 1; i++) {
        displayResources += `${resources[i]}|`;
      }
      displayResources += resources[resourcesLength - 1];
      return displayResources;
    }
  } else {
    return;
  }
};

const roleColumns = ctx => [
  {
    title: '角色名称',
    dataIndex: 'roleName',
    dataType: {
      type: 'link',
      options: {
        to(__, { id }) {
          return generateRoleRoutePath({ roleId: id });
        },
      },
    },
  },
  {
    title: '角色ID',
    dataIndex: 'roleCode',
    dataType: {
      type: 'link',
      options: {
        to(__, { id }) {
          return generateRoleRoutePath({ roleId: id });
        },
      },
    },
  },
  {
    title: '备注',
    dataIndex: 'remarks',
    ellipsis: true,
  },
  {
    title: '加入时间',
    dataIndex: 'gmtCreate',
    dataType: 'datetime',
  },
  {
    title: '创建人',
    dataIndex: 'creatorName',
    render: (text, { creatorId }) => <User.Link id={creatorId} name={text} />,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    fixed: 'right',
    render: (_, { roleName, id }) => (
      <DeleteConfirm
        variant="popconfirm"
        title={`确认移出角色： ${roleName}吗？`}
        onOk={() => ctx.removeRole(id)}
      >
        <Button type="link" style={{ padding: 0, height: 'auto' }}>
          移除角色
        </Button>
      </DeleteConfirm>
    ),
  },
];
const resourcesColumns = ctx => [
  {
    title: '机房ID',
    dataIndex: ['machineRoom', 'resourceCode'],
    dataType: {
      type: 'link',
      options: {
        to(resourceCode) {
          // return `/page/auth/user-group-manage/detail/${id}/1`;
          return generateBasicResourcesIdcUrl({ idc: resourceCode, name: '' });
        },
      },
    },
  },
  {
    title: '楼栋名称',
    dataIndex: 'resourceName',
    dataType: {
      type: 'link',
      options: {
        to(__, { machineRoom, resourceCode }) {
          // return `/page/auth/user-group-manage/detail/${id}/1`;
          return generateBasicResourcesBuildingUrlLocation({
            idc: machineRoom.resourceCode,
            block: resourceCode.substring(resourceCode.indexOf('.') + 1),
          });
        },
      },
    },
  },
  {
    title: '楼栋ID',
    dataIndex: 'resourceCode',
    dataType: {
      type: 'link',
      options: {
        to(__, { machineRoom, resourceCode }) {
          // return `/page/auth/user-group-manage/detail/${id}/1`;
          return generateBasicResourcesBuildingUrlLocation({
            idc: machineRoom.resourceCode,
            block: resourceCode.substring(resourceCode.indexOf('.') + 1),
          });
        },
      },
    },
  },
  {
    title: '备注',
    dataIndex: 'remarks',
    ellipsis: true,
  },
  {
    title: '加入时间',
    dataIndex: 'gmtCreate',
    dataType: 'datetime',
  },
  {
    title: '创建人',
    dataIndex: 'creatorName',
    render: (text, { creatorId }) => <User.Link id={creatorId} name={text} />,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    fixed: 'right',
    render: (_, record) => (
      <DeleteConfirm
        variant="popconfirm"
        title={
          <div style={{ width: 400 }}>
            <Typography.Text>是否确认移除资源？</Typography.Text>
            <Typography.Paragraph
              type="secondary"
              ellipsis={{ rows: 3 }}
            >{`移除后用户组内的所有用户，将被取消资源：${record.resourceCode} 的权限，无法添加到上述资源对应的班组中，请谨慎操作！
        PS：移除后，用户已排班班次，仍可正常考勤打卡。`}</Typography.Paragraph>
          </div>
        }
        onOk={() => ctx.removeResources(record.id)}
      >
        <Button type="link" style={{ padding: 0, height: 'auto' }}>
          移除资源
        </Button>
      </DeleteConfirm>
    ),
  },
];
class UserManageDetail extends Component {
  state = {
    editVisible: false,
    associatedVisible: false,
  };

  componentDidMount() {
    const { match } = this.props;
    this.props.fetchUserGroupManageDetail({ groupId: match.params.id });
  }

  removeUser = userId => {
    const { userList } = this.props;
    const { searchType, pageNo, pageSize, saerchName } = userList;
    this.props.fetchRemoveUser({
      groupId: this.props.match.params.id,
      searchType,
      pageNo,
      pageSize,
      saerchName,
      userId,
    });
  };

  onCreateUserGroup = () => {
    this.props.onCreateVisible();
  };

  onEditConfirm = () => {
    const data = this.props.form.getFieldsValue();
    const { groupName, groupCode, remarks } = data;
    const { match } = this.props;
    this.props.fetchCreateUserGroup({
      groupName,
      groupCode:
        match.params.id && this.props.basicInfo.groupCode
          ? this.props.basicInfo.groupCode
          : groupCode,
      remarks,
      groupId: match.params.id,
    });
  };

  removeRole = roleId => {
    const { roleList } = this.props;
    const { searchType, pageNo, pageSize, saerchName } = roleList;
    this.props.fetchRemoveRole({
      groupId: this.props.match.params.id,
      searchType,
      pageNo,
      pageSize,
      saerchName,
      roleId,
    });
  };

  removeResources = resourceId => {
    const { resourceList } = this.props;
    const { searchType, pageNo, pageSize, saerchName } = resourceList;
    this.props.fetchRemoveResource({
      groupId: this.props.match.params.id,
      searchType,
      pageNo,
      pageSize,
      saerchName,
      resourceId,
    });
  };

  getDefaultChekedKeys = type => {
    const { resourceList, userList, roleList } = this.props;
    let list = [];
    if (type === 'resource') {
      resourceList.list.forEach(item => (list = [...list, String(item.id)]));
    }
    if (type === 'user') {
      userList.list.forEach(item => (list = [...list, item.id]));
    }
    if (type === 'role') {
      roleList.list.forEach(item => (list = [...list, item.id]));
    }
    return list;
  };

  render() {
    const {
      resourceList,
      location,
      basicInfo,
      userList,
      roleList,
      createVisible,
      userListSearchType,
      roleListSearchType,
      resourceListSearchType,
      redirect,
    } = this.props;
    const { defaultTab } = getLocationSearchMap(location.search, ['defaultTab']);

    const tabsItems = [
      {
        key: '1',
        label: '用户管理',
        children: (
          <ManageTable
            listContent={userList}
            fetchActions={this.props.fetchUserGroupsAssociatedUser}
            columns={userColumns(this)}
            addButton={
              <UserGroupAddUsers
                needFetchList
                type="primary"
                defaultSelectedSources={[{ label: basicInfo.groupName, key: basicInfo.id }]}
                checkedKeys={this.getDefaultChekedKeys('user')}
              />
            }
            selectOptions={userOptions}
            searchType={userListSearchType}
          />
        ),
      },
      {
        key: '2',
        label: '资源管理',
        children: (
          <ManageTable
            listContent={resourceList}
            fetchActions={this.props.fetchUserGroupsAssociatedResource}
            columns={resourcesColumns(this)}
            addButton={
              <UserGroupAddResource
                needFetchList
                type="primary"
                text="关联资源"
                checkedKeys={this.getDefaultChekedKeys('resource')}
                defaultSelectedSources={[{ label: basicInfo.groupName, key: basicInfo.id }]}
              />
            }
            selectOptions={resourceOptions}
            searchType={resourceListSearchType}
          />
        ),
      },
      {
        key: '3',
        label: '角色管理',
        children: (
          <ManageTable
            listContent={roleList}
            fetchActions={this.props.fetchUserGroupsAssociatedRoles}
            columns={roleColumns(this)}
            addButton={
              <UserGroupAddRoles
                needFetchList
                text="关联角色"
                type="primary"
                checkedKeys={this.getDefaultChekedKeys('role')}
                defaultSelectedSources={[{ label: basicInfo.groupName, key: basicInfo.id }]}
              />
            }
            selectOptions={roleOptions}
            searchType={roleListSearchType}
          />
        ),
      },
    ];
    return (
      <GutterWrapper mode="vertical">
        <TinyCard style={{ width: '100%' }}>
          <PageHeader
            style={{
              padding: '0 0 16px 0',
            }}
            title={basicInfo.groupName}
            onBack={() => redirect(USER_GROUPS_ROUTE_PATH)}
          />
          <Descriptions column={4}>
            <Descriptions.Item span={4} label="用户组基本信息">
              <Button
                style={{ padding: 0, height: 'auto' }}
                type="link"
                onClick={this.onCreateUserGroup}
              >
                编辑基本信息
              </Button>
            </Descriptions.Item>
            <Descriptions.Item label="用户组名称">{basicInfo.groupName}</Descriptions.Item>
            <Descriptions.Item label="用户组ID">
              <Paragraph style={{ marginBottom: 0 }} copyable={{ text: basicInfo.groupCode }}>
                {basicInfo.groupCode}
              </Paragraph>
            </Descriptions.Item>
            <Descriptions.Item label="备注">{basicInfo.remarks}</Descriptions.Item>
            <Descriptions.Item label="更新人">
              <User.Link
                id={basicInfo.modifierId ? basicInfo.modifierId : basicInfo.creatorId}
                name={basicInfo.modifierName ? basicInfo.modifierName : basicInfo.creatorName}
              />
            </Descriptions.Item>
            <Descriptions.Item label="创建时间">
              {moment(basicInfo.gmtCreate).format('YYYY-MM-DD HH:mm:ss')}
            </Descriptions.Item>
            <Descriptions.Item label="更新时间">
              {basicInfo.gmtModified
                ? moment(basicInfo.gmtModified).format('YYYY-MM-DD HH:mm:ss')
                : moment(basicInfo.gmtCreate).format('YYYY-MM-DD HH:mm:ss')}
            </Descriptions.Item>
          </Descriptions>
        </TinyCard>
        <TinyCard>
          <Tabs defaultActiveKey={defaultTab} items={tabsItems} />
        </TinyCard>
        <CreateUserGroup
          visible={createVisible}
          title="编辑用户组"
          defaultInfo={{
            groupName: basicInfo.groupName,
            groupId: basicInfo.groupCode
              ? basicInfo.groupCode.indexOf('@') > -1
                ? basicInfo.groupCode.slice(0, basicInfo.groupCode.indexOf('@'))
                : basicInfo.groupCode
              : '',
            remarks: basicInfo.remarks,
          }}
          form={this.props.form}
          onClose={this.onCreateUserGroup}
          onSubmit={this.onEditConfirm}
        />
      </GutterWrapper>
    );
  }
}

const mapStateToProps = ({
  userGroupManage: {
    basicInfo,
    loading,
    joinUserGroupList,
    joinUserGroupTotal,
    userList,
    roleList,
    createVisible,
    resourceList,
  },
}) => ({
  basicInfo,
  loading,
  joinUserGroupList,
  joinUserGroupTotal,
  userList,
  roleList,
  createVisible,
  resourceList,
});
const mapDispatchToProps = {
  fetchUserGroupManageDetail,
  editBasicInfoVisible: userGroupManageActions.editBasicInfoVisible,
  fetchUserGroupsAssociatedUser,
  fetchUserGroupsAssociatedRoles,
  onCreateVisible: userGroupManageActions.createVisible,
  fetchCreateUserGroup,
  fetchRemoveUser,
  fetchRemoveRole,
  fetchRemoveResource,
  fetchUserGroupsAssociatedResource,
  userListSearchType: userGroupManageActions.userListSuccess,
  roleListSearchType: userGroupManageActions.roleListSuccess,
  resourceListSearchType: userGroupManageActions.resourceListSuccess,
  redirect: redirectActionCreator,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(Form.create({ name: 'user_manage_detail_edit' })(UserManageDetail));
