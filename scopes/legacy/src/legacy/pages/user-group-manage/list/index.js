import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import EllipsisOutlined from '@ant-design/icons/es/icons/EllipsisOutlined';
import { Select } from '@galiojs/awesome-antd';
import trim from 'lodash/trim';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Dropdown } from '@manyun/base-ui.ui.dropdown';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Typography } from '@manyun/base-ui.ui.typography';

import { User } from '@manyun/auth-hub.ui.user';

import { <PERSON>utterWrapper, TinyCard, TinyTable } from '@manyun/dc-brain.legacy.components';
import {
  fetchCreateUserGroup,
  fetchDelete,
  fetchUserGroupList,
  userGroupManageActions,
} from '@manyun/dc-brain.legacy.redux/actions/userGroupManageActions';
import { generateUserGroupManageDetailUrlLocation } from '@manyun/dc-brain.legacy.utils/urls';

import CreateUserGroup from '../component/create-user-group';
import UserGroupAddResource from '../component/user-group-add-resource';
import UserGroupAddRoles from '../component/user-group-add-roles';
import UserGroupAddUsers from '../component/user-group-add-users';

const { Title } = Typography;
const InputGroup = Input.Group;
const { Option } = Select;
const { Search } = Input;

const columns = ctx => [
  {
    title: '用户组名称',
    dataIndex: 'groupName',
    dataType: {
      type: 'link',
      options: {
        to(__, { id }) {
          return generateUserGroupManageDetailUrlLocation({ id, defaultTab: '1' });
        },
      },
    },
  },
  {
    title: '关联角色',
    dataIndex: 'userGroup',
    dataType: {
      type: 'link',
      options: {
        to(__, { id }) {
          return generateUserGroupManageDetailUrlLocation({ id, defaultTab: '3' });
        },
        text: '查看',
      },
    },
  },
  {
    title: '备注',
    dataIndex: 'remarks',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataType: 'datetime',
    dataIndex: 'gmtCreate',
  },
  {
    title: '更新时间',
    dataType: 'datetime',
    dataIndex: 'gmtModified',
  },
  {
    title: '创建人',
    dataIndex: 'creatorName',
    render: (text, { creatorId }) => <User.Link id={creatorId} name={text} />,
  },

  {
    title: '操作',
    dataIndex: 'operation',
    fixed: 'right',
    width: 200,
    render: (_, record) => (
      <span>
        <UserGroupAddUsers
          style={{ padding: 0, height: 'auto' }}
          needFetchList={false}
          type="link"
          defaultSelectedSources={[{ label: record.groupName, key: record.id }]}
        />
        <Divider type="vertical" />
        <UserGroupAddResource
          style={{ padding: 0, height: 'auto' }}
          checkedKeys={[]}
          needFetchList={false}
          type="link"
          text="关联资源"
          defaultSelectedSources={[{ label: record.groupName, key: record.id }]}
        />
        <Divider type="vertical" />
        <Dropdown
          menu={{
            items: [
              {
                label: (
                  <UserGroupAddRoles
                    style={{ padding: 0, height: 'auto' }}
                    needFetchList={false}
                    type="link"
                    text="关联角色"
                    defaultSelectedSources={[{ label: record.groupName, key: record.id }]}
                  />
                ),
              },
              {
                label: (
                  <DeleteConfirm
                    targetName={`用户组：${record.groupName}`}
                    onOk={() => ctx.onDelete(record)}
                  >
                    <Button type="link" compact>
                      删除
                    </Button>
                  </DeleteConfirm>
                ),
              },
            ],
          }}
        >
          <EllipsisOutlined />
        </Dropdown>
      </span>
    ),
  },
];

class UserGroupManageList extends Component {
  state = {
    selectedRowsList: [],
    selectedRowKeys: [],
    visible: false,
  };

  componentDidMount() {
    this.props.fetchUserGroupList({
      pageNo: 1,
      pageSize: 10,
      searchType: 'GROUP_NAME',
      searchName: '',
    });
  }

  onParamKey = paramKey => {
    const { userGroupList } = this.props;
    this.props.success({
      ...userGroupList,
      searchType: paramKey,
    });
  };

  onParamValue = value => {
    const { userGroupList } = this.props;
    const { searchType, pageSize } = userGroupList;
    this.props.fetchUserGroupList({
      searchType,
      searchName: trim(value),
      pageNo: 1,
      pageSize,
    });
  };

  onSelectChange = (selectedRowKeys, selectedRows) => {
    const selectedRowsList = selectedRows.map(item => {
      return {
        label: item.groupName,
        key: item.id,
      };
    });
    this.setState({ selectedRowsList, selectedRowKeys });
  };

  onChangePage = (pageNo, pageSize) => {
    const { userGroupList } = this.props;
    const { searchType, searchName } = userGroupList;
    this.props.fetchUserGroupList({ searchType, searchName, pageNo, pageSize });
  };

  onDelete = row => {
    const { userGroupList } = this.props;
    const { searchType, searchName, pageSize, pageNo } = userGroupList;
    if (row.groupCode === 'ADMIN_GROUP') {
      message.error('禁止删除管理员组！');
      return;
    }
    this.props.fetchDelete({
      groupId: row.id,
      searchType,
      pageSize,
      searchName,
      pageNo,
    });
  };

  onCreateUserGroup = () => {
    this.props.onCreateVisible();
  };

  onCreateConfirm = () => {
    this.props.form.validateFields((err, values) => {
      const data = this.props.form.getFieldsValue();
      if (!err) {
        const { groupName, groupCode, remarks } = data;
        const { userGroupList } = this.props;
        const { searchType, searchName, pageSize, pageNo } = userGroupList;
        this.props.fetchCreateUserGroup({
          groupName,
          groupCode,
          remarks,
          pageNo,
          pageSize,
          searchName,
          searchType,
        });
      }
    });
  };

  render() {
    const { selectedRowKeys, selectedRowsList } = this.state;
    const { userGroupList, createVisible } = this.props;
    return (
      <GutterWrapper mode="vertical">
        <TinyCard>
          <GutterWrapper mode="vertical">
            <Title level={4}>用户组</Title>
            <Alert
              message="通过用户组管理对同职责用户进行分类并隔离机房权限，可以更加高效的管理用户及其可查看数据权限。对一个用户组进行该角色关联后，用户组内的所有用户会自动继承该用户组的权限，如果一个用户被加入到多个用户组，那么该用户将会继承多个用户组的权限。"
              type="info"
            />
          </GutterWrapper>
        </TinyCard>
        <TinyCard>
          <TinyTable
            rowKey="id"
            columns={columns(this)}
            scroll={{ x: 'max-content' }}
            dataSource={userGroupList.list}
            loading={userGroupList.loading}
            // scroll={{ x: true }}
            rowSelection={{
              selectedRowKeys,
              onChange: this.onSelectChange,
            }}
            pagination={{
              total: userGroupList.total,
              current: userGroupList.pageNo,
              onChange: this.onChangePage,
              pageSize: userGroupList.pageSize,
            }}
            actions={
              <GutterWrapper flex justifyContent="flex-start">
                <Button key="create" type="primary" onClick={this.onCreateUserGroup}>
                  新建用户组
                </Button>
                <UserGroupAddUsers
                  key="connect-user"
                  needFetchList={false}
                  type="primary"
                  defaultSelectedSources={selectedRowsList}
                />
                <UserGroupAddRoles
                  key="connect-power"
                  needFetchList={false}
                  type="primary"
                  text="关联角色"
                  defaultSelectedSources={selectedRowsList}
                />
                <InputGroup key="search" compact style={{ width: '20%', minWidth: 400 }}>
                  <Select
                    value={userGroupList.searchType}
                    style={{ width: '40%' }}
                    onChange={this.onParamKey}
                  >
                    <Option value="GROUP_NAME">组名称</Option>
                    <Option value="CREATOR_NAME">创建人名称</Option>
                  </Select>
                  <Search allowClear style={{ width: '50%' }} onSearch={this.onParamValue} />
                </InputGroup>
              </GutterWrapper>
            }
          />
        </TinyCard>
        <CreateUserGroup
          visible={createVisible}
          title="新建用户组"
          defaultInfo={{}}
          form={this.props.form}
          onClose={this.onCreateUserGroup}
          onSubmit={this.onCreateConfirm}
        />
      </GutterWrapper>
    );
  }
}

const mapStateToProps = ({ userGroupManage: { userGroupList, createVisible } }) => ({
  userGroupList,
  createVisible,
});
const mapDispatchToProps = {
  fetchUserGroupList,
  selectUserListSuccess: userGroupManageActions.selectUserListSuccess,
  selectedUser: userGroupManageActions.selectedUser,
  fetchDelete,
  onCreateVisible: userGroupManageActions.createVisible,
  fetchCreateUserGroup: fetchCreateUserGroup,
  success: userGroupManageActions.success,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(Form.create({ name: 'user_group_create' })(UserGroupManageList));
