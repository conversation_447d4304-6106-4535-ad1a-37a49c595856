import React from 'react';
import { connect } from 'react-redux';
import { useParams } from 'react-router-dom';

import { RelationshipConnectorButton } from '@manyun/dc-brain.legacy.components';
import { fetchUserGroupsAssociatedUser } from '@manyun/dc-brain.legacy.redux/actions/userGroupManageActions';
import { userGroupManageService } from '@manyun/dc-brain.legacy.services';
import { fetchUserGroupAddUser } from '@manyun/dc-brain.legacy.services/userGroupManageService';

import { userGroupColumns } from './columns';

export function UserGroupAddUsers({
  defaultSelectedSources,
  type,
  fetchUserGroupsAssociatedUser,
  needFetchList,
  userList,
  style = {},
  checkedKeys,
}) {
  const { id } = useParams();
  const { pageNo, pageSize, searchName, searchType } = userList;

  return (
    <RelationshipConnectorButton
      text="关联用户"
      title="关联用户"
      alertMessage="用户加入到用户组后，将拥有该组所有权限。"
      type={type}
      sourceTitle="用户组"
      targetTitle="用户"
      sourceSelectPlaceholder="支持多选 支持模糊搜索"
      targetSelectPlaceholder="请输入"
      defaultSelectedSources={defaultSelectedSources}
      targetSelectService={userGroupManageService.fetchSelectUser}
      targetTableLeftColumns={userGroupColumns}
      targetTableRightColumns={userGroupColumns}
      targetDatasourceService={userGroupManageService.fetchSelectUser}
      sourceSelectService={userGroupManageService.fetchSelectUserGroup}
      buttonStyle={style}
      checkedKeys={checkedKeys}
      submitService={(selectedSources, selectedTargets) =>
        fetchUserGroupAddUser(
          selectedSources,
          selectedTargets,
          id,
          fetchUserGroupsAssociatedUser,
          needFetchList,
          pageNo,
          pageSize,
          searchName,
          searchType
        )
      }
    />
  );
}

const mapStateToProps = ({ userGroupManage: { userList } }) => ({
  userList,
});

const mapDispatchToProps = {
  fetchUserGroupsAssociatedUser,
};

export default connect(mapStateToProps, mapDispatchToProps)(UserGroupAddUsers);
