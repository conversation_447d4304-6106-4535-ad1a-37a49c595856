import React from 'react';
import { connect } from 'react-redux';
import { useParams } from 'react-router-dom';

import { RelationshipConnectorButton } from '@manyun/dc-brain.legacy.components';
import { fetchUserGroupsAssociatedRoles } from '@manyun/dc-brain.legacy.redux/actions/userGroupManageActions';
import { userGroupManageService } from '@manyun/dc-brain.legacy.services';
import { fetchAssociatedRole } from '@manyun/dc-brain.legacy.services/userGroupManageService';

import { roleColumns } from './columns';

export function UserGroupAddRoles({
  defaultSelectedSources,
  type,
  fetchUserGroupsAssociatedRoles,
  needFetchList,
  roleList,
  text,
  style = {},
  checkedKeys,
}) {
  const { id } = useParams();
  const { pageNo, pageSize, searchName, searchType } = roleList;

  return (
    <RelationshipConnectorButton
      text={text}
      title="关联角色"
      alertMessage="用户组加入到角色后，将拥有该角色所有权限。"
      type={type}
      sourceTitle="用户组"
      targetTitle="角色"
      sourceSelectPlaceholder="支持多选 支持模糊搜索"
      targetSelectPlaceholder="请输入"
      defaultSelectedSources={defaultSelectedSources}
      targetSelectService={userGroupManageService.fetchSelectRole}
      targetTableLeftColumns={roleColumns}
      targetTableRightColumns={roleColumns}
      targetDatasourceService={userGroupManageService.fetchSelectRole}
      sourceSelectService={userGroupManageService.fetchSelectUserGroup}
      buttonStyle={style}
      checkedKeys={checkedKeys}
      submitService={(selectedSources, selectedTargets) =>
        fetchAssociatedRole(
          selectedSources,
          selectedTargets,
          id,
          fetchUserGroupsAssociatedRoles,
          needFetchList,
          pageNo,
          pageSize,
          searchName,
          searchType
        )
      }
    />
  );
}

const mapStateToProps = ({ userGroupManage: { roleList } }) => ({
  roleList,
});

const mapDispatchToProps = {
  fetchUserGroupsAssociatedRoles,
};

export default connect(mapStateToProps, mapDispatchToProps)(UserGroupAddRoles);
