import React from 'react';
import { connect } from 'react-redux';
import { useParams } from 'react-router-dom';

import { RelationshipConnectorButton } from '@manyun/dc-brain.legacy.components';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import { fetchUserGroupsAssociatedResource } from '@manyun/dc-brain.legacy.redux/actions/userGroupManageActions';
import { userGroupManageService } from '@manyun/dc-brain.legacy.services';
import { fetchUserGroupAddResource } from '@manyun/dc-brain.legacy.services/userGroupManageService';

export function UserGroupAddResource({
  defaultSelectedSources,
  type,
  fetchUserGroupsAssociatedResource,
  needFetchList,
  resourceList,
  text,
  style = {},
  checkedKeys,
  syncCommonData,
}) {
  const { id } = useParams();
  const { pageNo, pageSize, searchName, searchType } = resourceList;
  return (
    <RelationshipConnectorButton
      text={text}
      title="关联资源"
      alertMessage="用户关联到机房后，将拥有该机房所有数据权限。"
      type={type}
      targetType="tree"
      sourceTitle="用户组"
      targetTitle="资源"
      sourceSelectPlaceholder="支持多选 支持模糊搜索"
      targetSelectPlaceholder="请输入"
      checkedKeys={checkedKeys}
      defaultSelectedSources={defaultSelectedSources}
      targetDatasourceService={userGroupManageService.fetchSelectResourceList}
      sourceSelectService={userGroupManageService.fetchSelectUserGroup}
      buttonStyle={style}
      submitService={async (selectedSources, selectedTargets) => {
        const data = await fetchUserGroupAddResource(
          selectedSources,
          selectedTargets,
          id,
          fetchUserGroupsAssociatedResource,
          needFetchList,
          pageNo,
          pageSize,
          searchName,
          searchType
        );
        syncCommonData({ strategy: { currentUser: 'FORCED' } });
        return data;
      }}
    />
  );
}

const mapStateToProps = ({ userGroupManage: { resourceList } }) => ({
  resourceList,
});

const mapDispatchToProps = {
  fetchUserGroupsAssociatedResource,
  syncCommonData: syncCommonDataActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(UserGroupAddResource);
