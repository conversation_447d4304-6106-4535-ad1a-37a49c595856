import React from 'react';
import { connect } from 'react-redux';

import { Input } from '@manyun/base-ui.ui.input';
import { Typography } from '@manyun/base-ui.ui.typography';

import { Form, StyledInput, TinyDrawer } from '@manyun/dc-brain.legacy.components';
import { fetchJoinUserGroup } from '@manyun/dc-brain.legacy.redux/actions/userManageActions';

export function UserGroupAddRoles({
  title,
  visible,
  onClose,
  createUserGroupLoading,
  onSubmit,
  form,
  defaultInfo,
}) {
  return (
    <TinyDrawer
      width={600}
      title={<Typography.Title level={4}>{title}</Typography.Title>}
      open={visible}
      destroyOnClose
      submitButtonLoading={createUserGroupLoading}
      onClose={onClose}
      onSubmit={onSubmit}
      onCancel={onClose}
    >
      <Form
        form={form}
        items={[
          {
            label: '用户组ID',
            decorate: true,
            // extra: '不超过64个字符，允许英文字母、数字或“-”',
            decorateOptions: {
              initialValue: defaultInfo.groupId || '',
              rules: [
                { required: true, message: '用户组ID!' },
                { max: 64, message: '不可超过64位' },
                {
                  pattern: /^[a-zA-Z0-9-_]+$/,
                  message: '用户组ID必须是英文、数字、“-”或“_”',
                },
              ],
            },
            id: 'groupCode',
            control: <StyledInput disabled={defaultInfo.groupId} />,
          },
          {
            label: '用户组名称',
            decorate: true,
            // extra: '最大长度24个字符或汉字',
            decorateOptions: {
              initialValue: defaultInfo.groupName || '',
              rules: [
                { required: true, message: '用户组名称!' },
                {
                  max: 24,
                  message: '最多输入 24 个字符！',
                },
              ],
            },
            id: 'groupName',
            control: <Input />,
          },
          {
            label: '备注',
            decorate: true,
            id: 'remarks',
            // extra: '最大长度128个字符',
            decorateOptions: {
              initialValue: defaultInfo.remarks || '',
              rules: [
                {
                  max: 128,
                  message: '最多输入 128 个字符！',
                },
              ],
            },
            control: <Input.TextArea />,
          },
        ]}
      />
    </TinyDrawer>
  );
}

const mapStateToProps = ({ userGroupManage: { createUserGroupLoading } }) => ({
  createUserGroupLoading,
});

const mapDispatchToProps = {
  fetchJoinUserGroup,
};

export default connect(mapStateToProps, mapDispatchToProps)(UserGroupAddRoles);
