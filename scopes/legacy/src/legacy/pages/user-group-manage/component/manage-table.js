import React, { useEffect } from 'react';
import { useParams } from 'react-router-dom';

import { Select } from '@galiojs/awesome-antd';

import { Input } from '@manyun/base-ui.ui.input';

import { GutterWrapper, TinyTable } from '@manyun/dc-brain.legacy.components';

const InputGroup = Input.Group;
const { Option } = Select;
const { Search } = Input;

function onParamValue(listContent, id, fetchActions, e) {
  fetchActions({
    groupId: id,
    pageNo: 1,
    pageSize: listContent.pageSize,
    searchType: listContent.searchType,
    searchName: e,
  });
}

function onFilterType(listContent, id, searchType) {
  return value => {
    searchType({ ...listContent, searchType: value });
  };
}

export function AddUser({
  listContent,
  addButton,
  fetchActions,
  columns,
  selectOptions,
  searchType,
}) {
  const { id } = useParams();
  useEffect(() => {
    fetchActions({
      groupId: id,
      pageNo: listContent.pageNo,
      pageSize: 2000, // 给予比较大的数字以方便前端分页
      searchType: listContent.searchType,
      searchName: listContent.searchName,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <GutterWrapper mode="vertical">
      <GutterWrapper flex>
        {addButton}
        <InputGroup style={{ width: '20%', minWidth: 400 }} compact>
          <Select
            style={{ width: '40%' }}
            value={listContent.searchType}
            onChange={onFilterType(listContent, id, searchType)}
          >
            {selectOptions.map(item => (
              <Option key={item.value} value={item.value}>
                {item.label}
              </Option>
            ))}
          </Select>
          <Search
            style={{ width: '50%' }}
            allowClear
            onSearch={value => onParamValue(listContent, id, fetchActions, value)}
          />
        </InputGroup>
      </GutterWrapper>
      <TinyTable
        rowKey="id"
        scroll={{ x: 'max-content' }}
        columns={columns}
        dataSource={listContent.list}
        loading={listContent.loading}
        pagination={{
          showTotal: () => `共 ${listContent.total} 条`,
        }}
      />
    </GutterWrapper>
  );
}

export default AddUser;
