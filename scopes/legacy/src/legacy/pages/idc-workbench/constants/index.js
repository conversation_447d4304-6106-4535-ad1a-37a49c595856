import { CHANGE_TICKET_STATUS_KEY_MAP } from '@manyun/dc-brain.legacy.pages/change/constants';

/**
 * 待处理的事件状态包括：已创建、处理中、已缓解
 */
export const UN_RESOLVED_EVENT_STATES = ['CREATED', 'PROCESSING', 'RELIEVED'];

/**
 * 待处理的变更状态包括：待变更、变更中、总结、待关闭
 */
export const UN_RESOLVED_CHANGE_STATES = [
  CHANGE_TICKET_STATUS_KEY_MAP.WAITING_CHANGE,
  CHANGE_TICKET_STATUS_KEY_MAP.CHANGING,
  CHANGE_TICKET_STATUS_KEY_MAP.IN_SUMMARY,
  CHANGE_TICKET_STATUS_KEY_MAP.WAITING_CLOSE,
];
