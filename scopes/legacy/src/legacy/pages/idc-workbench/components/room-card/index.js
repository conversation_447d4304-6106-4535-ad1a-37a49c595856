import React from 'react';
import { connect } from 'react-redux';

import BellOutlined from '@ant-design/icons/es/icons/BellOutlined';
import get from 'lodash/get';

import { LightningOutlined } from '@manyun/base-ui.icons/dist/outlined/lightning';
import { ThermometerOutlined } from '@manyun/base-ui.icons/dist/outlined/thermometer';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Space } from '@manyun/base-ui.ui.space';

import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { PointsLineModalButton } from '@manyun/monitoring.chart.points-line';
import { PointDataRenderer } from '@manyun/monitoring.ui.point-data-renderer';
import { generateGetPointMonitoringData } from '@manyun/monitoring.util.get-monitoring-data';
import { typeofRacksRoom } from '@manyun/resource-hub.util.type-of-racks-room';

import { StatusText } from '@manyun/dc-brain.legacy.components';
import { getSpaceGuidMap } from '@manyun/dc-brain.legacy.utils';

export function RoomCard({
  isRacksRoom,
  roomGuid,
  title,
  roomLoadRateAvg,
  roomTAvg,
  powerSystemAlarmsCount,
  hvacSystemAlarmsCount,
  linkRoom,
  linkAlarmList,
}) {
  const cardTitle = (
    <Button
      style={{ textAlign: 'left' }}
      compact
      block
      type="link"
      onClick={evt => {
        linkRoom(roomGuid, evt);
      }}
    >
      {title}
    </Button>
  );

  return (
    <Card size="small" title={cardTitle}>
      <Space direction="vertical" size={4}>
        {isRacksRoom && (
          <Space align="center" size={4}>
            <LightningOutlined />
            <span>实时功率</span>
            <PointsLineModalButton
              idcTag={getSpaceGuidMap(roomGuid).idc}
              btnStyle={{ fontSize: 12 }}
              btnText={<PointDataRenderer data={roomLoadRateAvg} />}
              modalText={`${roomGuid} 实时功率`}
              pointGuids={roomLoadRateAvg.pointGuids}
            />
          </Space>
        )}
        <Space align="center" size={4}>
          <ThermometerOutlined />
          <span>平均温度</span>
          <PointsLineModalButton
            idcTag={getSpaceGuidMap(roomGuid).idc}
            btnStyle={{ fontSize: 12 }}
            btnText={<PointDataRenderer data={roomTAvg} />}
            modalText={`${roomGuid} 平均温度`}
            pointGuids={roomTAvg.pointGuids}
          />
        </Space>
        <Space align="center" size={4}>
          <BellOutlined />
          <span>告警条数</span>
          <div>
            <Button
              style={{ fontSize: 12 }}
              type="link"
              compact
              disabled={powerSystemAlarmsCount <= 0}
              onClick={event => {
                linkAlarmList(roomGuid);
                event.stopPropagation();
                event.nativeEvent.stopImmediatePropagation();
              }}
            >
              电力
              <StatusText
                style={{ display: 'inline-block', marginLeft: 2 }}
                status={powerSystemAlarmsCount > 0 ? 'alarm' : 'default'}
              >
                {powerSystemAlarmsCount}
              </StatusText>
            </Button>
            <Divider type="vertical" />
            <Button
              style={{ fontSize: 12 }}
              type="link"
              compact
              disabled={hvacSystemAlarmsCount <= 0}
              onClick={event => {
                linkAlarmList(roomGuid);
                event.stopPropagation();
                event.nativeEvent.stopImmediatePropagation();
              }}
            >
              暖通
              <StatusText
                style={{ display: 'inline-block', marginLeft: 2 }}
                status={hvacSystemAlarmsCount > 0 ? 'alarm' : 'default'}
              >
                {hvacSystemAlarmsCount}
              </StatusText>
            </Button>
          </div>
        </Space>
      </Space>
    </Card>
  );
}

const mapStateToProps = (
  {
    idcWorkbench: { roomAlarmsCount },
    'monitoring.subscriptions': { devicesRealtimeData, devicesAlarmsData },
    config: { configMap },
  },
  { blockGuid, roomGuid, roomType }
) => {
  const configUtil = new ConfigUtil(configMap.current, { defaultSpaceGuid: blockGuid });
  const roomDeviceType = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_ROOM);
  const deviceGuid = roomGuid;
  const device = { deviceGuid, deviceType: roomDeviceType };
  const getData = generateGetPointMonitoringData({
    realtimeData: devicesRealtimeData,
    alarmsData: devicesAlarmsData,
    configUtil,
  });

  const roomLoadRatePointCode = configUtil.getPointCode(
    roomDeviceType,
    ConfigUtil.constants.pointCodes.RPP_INPUT_POWER_SUM
  );
  const roomLoadRateAvg = getData(device, {
    formatted: true,
    hardCodedPointCode: roomLoadRatePointCode,
  });
  roomLoadRateAvg.pointGuids = [
    {
      serieName: '实时功率',
      unit: roomLoadRateAvg.unit,
      deviceGuid,
      pointCode: roomLoadRatePointCode,
      deviceType: roomDeviceType,
      spaceGuid: roomGuid,
    },
  ];

  const isRacksRoom = typeofRacksRoom(roomType);
  const roomTAvgPointCode = configUtil.getPointCode(
    roomDeviceType,
    isRacksRoom
      ? ConfigUtil.constants.pointCodes.COLD_AISLE_T_AVG
      : ConfigUtil.constants.pointCodes.THTB_SENSOR_T
  );
  const roomTAvg = getData(device, {
    formatted: true,
    hardCodedPointCode: roomTAvgPointCode,
  });
  roomTAvg.pointGuids = [
    {
      serieName: '平均温度',
      unit: roomTAvg.unit,
      deviceGuid,
      pointCode: roomTAvgPointCode,
      deviceType: roomDeviceType,
      spaceGuid: roomGuid,
    },
  ];

  const powerSystemAlarmsCount = get(
    roomAlarmsCount,
    [roomGuid, configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.POWER_SYSTEM)],
    0
  );
  const hvacSystemAlarmsCount = get(
    roomAlarmsCount,
    [roomGuid, configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.HVAC_SYSTEM)],
    0
  );

  return {
    isRacksRoom,
    roomLoadRateAvg,
    roomTAvg,
    powerSystemAlarmsCount,
    hvacSystemAlarmsCount,
  };
};

export default connect(mapStateToProps)(RoomCard);
