import React from 'react';
import { connect } from 'react-redux';

import get from 'lodash/get';

import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { getTop1ByPositiveCount } from '@manyun/monitoring.model.point';
import { generateGetPointMonitoringData } from '@manyun/monitoring.util.get-monitoring-data';

import { StatusText } from '@manyun/dc-brain.legacy.components';
import { STATUS_MAP } from '@manyun/dc-brain.legacy.constants/statusColor';

import { CardLevel1, StyledTable } from '../styled';

export function CoolingSystem({ columns, data }) {
  return (
    <CardLevel1
      headStyle={{ border: 0, padding: '0 12px' }}
      bodyStyle={{ padding: 0 }}
      size="small"
      title="制冷系统"
    >
      <StyledTable size="small" dataSource={data} columns={columns} pagination={false} />
    </CardLevel1>
  );
}

const mapStateToProps = (
  {
    idcWorkbench: { blockDevicesCountMap },
    'monitoring.subscriptions': { devicesRealtimeData, devicesAlarmsData },
    config: { configMap },
  },
  { blockGuid }
) => {
  const configUtil = new ConfigUtil(configMap.current, { defaultSpaceGuid: blockGuid });
  const blockDevicesCount = blockDevicesCountMap[blockGuid];
  const blockDeviceType = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_BLOCK);
  const device = { deviceGuid: blockGuid, deviceType: blockDeviceType };
  const getData = generateGetPointMonitoringData({
    realtimeData: devicesRealtimeData,
    alarmsData: devicesAlarmsData,
    configUtil,
  });

  const columns = [
    {
      key: 'prop-name',
      render(_, __, idx) {
        switch (idx) {
          case 0:
            return '运行状态';
          case 1:
            return '运行模式';
          default:
            return 'Not defined.';
        }
      },
    },
    {
      title: '制冷单元',
      dataIndex: 'cooling-system-unit',
      render(text, _, idx) {
        if (idx === 0) {
          return (
            <StatusText background status={text.count > 0 ? STATUS_MAP.ALARM : STATUS_MAP.NORMAL}>
              {text.count + text.name}
            </StatusText>
          );
        }
        if (idx === 1) {
          return text.count + text.name;
        }
        return text.value;
      },
    },
    {
      title: '冷冻机组',
      dataIndex: 'water-cooled-chiller',
      render(text, _, idx) {
        if (idx === 0) {
          return (
            <StatusText background status={text.count > 0 ? STATUS_MAP.ALARM : STATUS_MAP.NORMAL}>
              {text.count + text.name}
            </StatusText>
          );
        }
        if (idx === 1) {
          return text.count + text.name;
        }
        return text.value;
      },
    },
    {
      title: '冷却塔',
      dataIndex: 'cooling-tower',
      render(text, _, idx) {
        if (idx === 0) {
          return (
            <StatusText background status={text.count > 0 ? STATUS_MAP.ALARM : STATUS_MAP.NORMAL}>
              {text.count + text.name}
            </StatusText>
          );
        }
        if (idx === 1) {
          return text.count + text.name;
        }
        return text.value;
      },
    },
    {
      title: '冷冻泵',
      dataIndex: 'chilled-water-pump',
      render(text, _, idx) {
        if (idx === 0) {
          return (
            <StatusText background status={text.count > 0 ? STATUS_MAP.ALARM : STATUS_MAP.NORMAL}>
              {text.count + text.name}
            </StatusText>
          );
        }
        if (idx === 1) {
          return text.count + text.name;
        }
        return text.value;
      },
    },
    {
      title: '冷却泵',
      dataIndex: 'cooled-water-pump',
      render(text, _, idx) {
        if (idx === 0) {
          return (
            <StatusText background status={text.count > 0 ? STATUS_MAP.ALARM : STATUS_MAP.NORMAL}>
              {text.count + text.name}
            </StatusText>
          );
        }
        if (idx === 1) {
          return text.count + text.name;
        }
        return text.value + text.unit;
      },
    },
  ];

  const data = [
    // 运行状态
    {
      key: 'working-state',
      'cooling-system-unit': getTop1ByPositiveCount(
        [
          {
            name: '单元异常',
            count: get(
              blockDevicesCount,
              configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.COOLING_SYSTEM_UNIT),
              0
            ),
          },
        ],
        {
          name: '正常',
          count: '',
        }
      ),
      'water-cooled-chiller': getTop1ByPositiveCount(
        [
          {
            name: '台异常',
            count: get(blockDevicesCount, configUtil.getOneDeviceType('water-cooled-chiller'), 0),
          },
        ],
        {
          name: '正常',
          count: '',
        }
      ),
      'cooling-tower': getTop1ByPositiveCount(
        [
          {
            name: '台异常',
            count: get(blockDevicesCount, configUtil.getOneDeviceType('cooling-tower'), 0),
          },
        ],
        {
          name: '正常',
          count: '',
        }
      ),
      'chilled-water-pump': getTop1ByPositiveCount(
        [
          {
            name: '台异常',
            count: get(blockDevicesCount, configUtil.getOneDeviceType('chilled-water-pump'), 0),
          },
        ],
        {
          name: '正常',
          count: '',
        }
      ),
      'cooled-water-pump': getTop1ByPositiveCount(
        [
          {
            name: '台异常',
            count: get(blockDevicesCount, configUtil.getOneDeviceType('cooled-water-pump'), 0),
          },
        ],
        {
          name: '正常',
          count: '',
        }
      ),
    },
    // 运行模式
    {
      key: 'working-mode',
      'cooling-system-unit': getTop1ByPositiveCount([
        {
          name: '免费模式',
          count: getData(device, {
            hardCodedPointCode: configUtil.getPointCode(
              blockDeviceType,
              ConfigUtil.constants.pointCodes.COOLING_SYSTEM_UNIT_POWER_OFF_COUNT
            ),
          }).value,
        },
        {
          name: '预冷模式',
          count: getData(device, {
            hardCodedPointCode: configUtil.getPointCode(
              blockDeviceType,
              ConfigUtil.constants.pointCodes.COOLING_SYSTEM_UNIT_REFRIGERATION_MODE_COUNT
            ),
          }).value,
        },
        {
          name: '机械制冷',
          count: getData(device, {
            hardCodedPointCode: configUtil.getPointCode(
              blockDeviceType,
              ConfigUtil.constants.pointCodes.COOLING_SYSTEM_UNIT_PRECOOLING_MODE_COUNT
            ),
          }).value,
        },
        {
          name: '关闭',
          count: getData(device, {
            hardCodedPointCode: configUtil.getPointCode(
              blockDeviceType,
              ConfigUtil.constants.pointCodes.COOLING_SYSTEM_UNIT_ECO_MODE_COUNT
            ),
          }).value,
        },
      ]),
      'water-cooled-chiller': getTop1ByPositiveCount([
        {
          name: '台停机',
          count: getData(device, {
            hardCodedPointCode: configUtil.getPointCode(
              blockDeviceType,
              ConfigUtil.constants.pointCodes.WATER_COOLED_CHILLER_POWER_OFF_COUNT
            ),
          }).value,
        },
        {
          name: '台运行',
          count: getData(device, {
            hardCodedPointCode: configUtil.getPointCode(
              blockDeviceType,
              ConfigUtil.constants.pointCodes.WATER_COOLED_CHILLER_WORKING_COUNT
            ),
          }).value,
        },
      ]),
      'cooling-tower': getTop1ByPositiveCount([
        {
          name: '台停机',
          count: getData(device, {
            hardCodedPointCode: configUtil.getPointCode(
              blockDeviceType,
              ConfigUtil.constants.pointCodes.COOLING_TOWER_POWER_OFF_COUNT
            ),
          }).value,
        },
        {
          name: '台运行',
          count: getData(device, {
            hardCodedPointCode: configUtil.getPointCode(
              blockDeviceType,
              ConfigUtil.constants.pointCodes.COOLING_TOWER_WORKING_COUNT
            ),
          }).value,
        },
      ]),
      'chilled-water-pump': getTop1ByPositiveCount([
        {
          name: '台停机',
          count: getData(device, {
            hardCodedPointCode: configUtil.getPointCode(
              blockDeviceType,
              ConfigUtil.constants.pointCodes.CHILLED_WATER_PUMP_POWER_OFF_COUNT
            ),
          }).value,
        },
        {
          name: '台启动',
          count: getData(device, {
            hardCodedPointCode: configUtil.getPointCode(
              blockDeviceType,
              ConfigUtil.constants.pointCodes.CHILLED_WATER_PUMP_WORKING_COUNT
            ),
          }).value,
        },
      ]),
      'cooled-water-pump': getTop1ByPositiveCount([
        {
          name: '台停机',
          count: getData(device, {
            hardCodedPointCode: configUtil.getPointCode(
              blockDeviceType,
              ConfigUtil.constants.pointCodes.COOLED_WATER_PUMP_POWER_OFF_COUNT
            ),
          }).value,
        },
        {
          name: '台启动',
          count: getData(device, {
            hardCodedPointCode: configUtil.getPointCode(
              blockDeviceType,
              ConfigUtil.constants.pointCodes.COOLED_WATER_PUMP_WORKING_COUNT
            ),
          }).value,
        },
      ]),
    },
  ];

  return { columns, data };
};

export default connect(mapStateToProps)(CoolingSystem);
