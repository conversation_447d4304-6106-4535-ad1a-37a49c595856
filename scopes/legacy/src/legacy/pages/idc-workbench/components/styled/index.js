import styled from 'styled-components';

import { prefixCls } from '@manyun/base-ui.style.style';
import { Card } from '@manyun/base-ui.ui.card';

import { TinyTable } from '@manyun/dc-brain.legacy.components';

export const CardLevel1 = styled(Card)`
  .${prefixCls}-card-head {
    padding: 0 8px;

    .${prefixCls}-card-head-wrapper {
      .${prefixCls}-card-head-title {
        font-size: 16px;
        padding: 6px 0;
      }
    }

    .${prefixCls}-tabs {
      margin-bottom: 0;

      .${prefixCls}-tabs-bar {
        margin: 0;

        &.${prefixCls}-tabs-large-bar {
          .${prefixCls}-tabs-nav-container {
            font-size: 14px;
          }
        }

        .${prefixCls}-tabs-tab {
          padding: 8px;
        }
      }
    }
  }

  .${prefixCls}-card-body {
    height: calc(100% - 50px);
  }
`;

export const StyledTable = styled(TinyTable)`
  .${prefixCls}-table {
    background: transparent;
  }

  .${prefixCls}-table-small {
    font-size: 12px;
    border-width: 0;

    .${prefixCls}-table-content {
      .${prefixCls}-table-body {
        table {
          .${prefixCls}-table-thead {
            tr {
              th {
                background: transparent;
              }
            }
          }
        }
      }

      .${prefixCls}-table-placeholder {
        background-color: transparent;
        border-bottom: 0;
      }
    }
  }
`;
