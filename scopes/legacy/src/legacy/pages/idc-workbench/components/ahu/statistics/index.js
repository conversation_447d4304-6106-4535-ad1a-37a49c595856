import React from 'react';
import { connect } from 'react-redux';

import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { generateGetPointMonitoringData } from '@manyun/monitoring.util.get-monitoring-data';

import CountsPieChart from './counts-pie-chart';
import CustomLegends from './custom-legends';
import ExtraCounts from './extra-counts';

function Statistics({
  blockGuid,
  blockDeviceType,
  ahuEvaporationUnitDeviceType,
  underDryStateModeCount,
  underSprayingnDXModeCount,
  underSprayingModeCount,
  underDrynDXModeCount,
}) {
  return (
    <div style={{ position: 'relative' }}>
      <CountsPieChart
        blockGuid={blockGuid}
        ahuEvaporationUnitDeviceType={ahuEvaporationUnitDeviceType}
        underDryStateModeCount={underDryStateModeCount}
        underSprayingnDXModeCount={underSprayingnDXModeCount}
        underSprayingModeCount={underSprayingModeCount}
        underDrynDXModeCount={underDrynDXModeCount}
      />
      <ExtraCounts
        blockGuid={blockGuid}
        blockDeviceType={blockDeviceType}
        ahuEvaporationUnitDeviceType={ahuEvaporationUnitDeviceType}
      />
      <CustomLegends
        blockGuid={blockGuid}
        blockDeviceType={blockDeviceType}
        underDryStateModeCount={underDryStateModeCount}
        underSprayingnDXModeCount={underSprayingnDXModeCount}
        underSprayingModeCount={underSprayingModeCount}
        underDrynDXModeCount={underDrynDXModeCount}
      />
    </div>
  );
}

const mapStateToProps = (
  { 'monitoring.subscriptions': { devicesRealtimeData, devicesAlarmsData }, config: { configMap } },
  { blockGuid, blockDeviceType, configUtil }
) => {
  const ahuEvaporationUnitDeviceType = configUtil.getOneDeviceType(
    ConfigUtil.constants.deviceTypes.AHU_EVAPORATION_UNIT_DX
  );
  const getData = generateGetPointMonitoringData({
    realtimeData: devicesRealtimeData,
    alarmsData: devicesAlarmsData,
    configUtil,
  });

  const device = {
    deviceGuid: blockGuid,
    deviceType: blockDeviceType,
  };

  const underDryStateModeCountPointCode = configUtil.getPointCode(
    blockDeviceType,
    ConfigUtil.constants.pointCodes.AHU_EVAPORATION_UNIT_DRY_STATE_MODE_COUNT
  );
  const underDryStateModeCount = getData(device, {
    hardCodedPointCode: underDryStateModeCountPointCode,
    defaults: { value: 0 },
  });

  const underSprayingnDXModeCountPointCode = configUtil.getPointCode(
    blockDeviceType,
    ConfigUtil.constants.pointCodes.AHU_EVAPORATION_UNIT_SPRAYING_N_DX_MODE_COUNT
  );
  const underSprayingnDXModeCount = getData(device, {
    hardCodedPointCode: underSprayingnDXModeCountPointCode,
    defaults: { value: 0 },
  });

  const underSprayingModeCountPointCode = configUtil.getPointCode(
    blockDeviceType,
    ConfigUtil.constants.pointCodes.AHU_EVAPORATION_UNIT_SPRAYING_MODE_COUNT
  );
  const underSprayingModeCount = getData(device, {
    hardCodedPointCode: underSprayingModeCountPointCode,
    defaults: { value: 0 },
  });

  const underDrynDXModeCountPointCode = configUtil.getPointCode(
    blockDeviceType,
    ConfigUtil.constants.pointCodes.AHU_EVAPORATION_UNIT_DRY_N_DX_MODE_COUNT
  );
  const underDrynDXModeCount = getData(device, {
    hardCodedPointCode: underDrynDXModeCountPointCode,
    defaults: { value: 0 },
  });

  return {
    ahuEvaporationUnitDeviceType,
    underDryStateModeCount,
    underSprayingnDXModeCount,
    underSprayingModeCount,
    underDrynDXModeCount,
    getData,
  };
};

export default connect(mapStateToProps)(Statistics);
