import React from 'react';

import { Pie } from '@manyun/base-ui.chart.pie';

// import { equipmentManageService } from '@manyun/dc-brain.legacy.services';
import { SERIES_COLOR_MAP } from './constants';

export default function CountsPieChart({
  // blockGuid,
  underDryStateModeCount,
  underSprayingnDXModeCount,
  underSprayingModeCount,
  underDrynDXModeCount,
  // ahuEvaporationUnitDeviceType,
}) {
  // const [ahuEvaporationUnitCount, setAhuEvaporationUnitCount] = useState(0);

  // useEffect(() => {
  //   (async () => {
  //     const { response, error } = await equipmentManageService.fetchEquipmentListPage({
  //       pageNum: 1,
  //       pageSize: 1,
  //       deviceTypeList: [ahuEvaporationUnitDeviceType],
  //       spaceGuidList: [blockGuid],
  //     });

  //     if (error) {
  //       return;
  //     }

  //     setAhuEvaporationUnitCount(response.total);
  //   })();
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, []);

  const seriesData = [
    [
      {
        value: underDryStateModeCount.value,
        name: 'DRY_STATE_MODE',
        itemStyle: { color: SERIES_COLOR_MAP.DRY_STATE_MODE },
      },
      {
        value: underSprayingModeCount.value,
        name: 'SPRAYING_MODE',
        itemStyle: { color: SERIES_COLOR_MAP.SPRAYING_MODE },
      },
      {
        value: underSprayingnDXModeCount.value,
        name: 'SPRAYING_N_DX_MODE',
        itemStyle: { color: SERIES_COLOR_MAP.SPRAYING_N_DX_MODE },
      },
      {
        value: underDrynDXModeCount.value,
        name: 'DRY_N_DX_MODE',
        itemStyle: { color: SERIES_COLOR_MAP.DRY_N_DX_MODE },
      },
    ],
  ];

  return (
    <Pie
      style={{ width: '100%', height: 144 }}
      option={{
        backgroundColor: 'transparent',
        legend: { show: false },
        grid: { top: 0, right: 0, bottom: 0, left: 0 },
        series: [{ ...series[0], data: seriesData[0] }],
      }}
    />
  );
}

const series = [
  {
    name: 'Mode',
    type: 'pie',
    emphasis: { scale: false },
    hoverOffset: 1,
    cursor: 'default',
    radius: ['75%', '100%'],
    roseType: 'radius',
    label: {
      show: false,
    },
  },
];
