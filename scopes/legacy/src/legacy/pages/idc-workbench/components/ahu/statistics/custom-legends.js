import React from 'react';

import { Badge } from '@manyun/base-ui.ui.badge';

import { PointsLineModalButton } from '@manyun/monitoring.chart.points-line';

import { DebugPointValue, GutterWrapper, StatusText } from '@manyun/dc-brain.legacy.components';
import { getSpaceGuidMap } from '@manyun/dc-brain.legacy.utils';

import { SERIES_COLOR_MAP } from './constants';

export default function CustomLegends({
  blockGuid,
  blockDeviceType,
  underSprayingModeCount,
  underDryStateModeCount,
  underSprayingnDXModeCount,
  underDrynDXModeCount,
}) {
  return (
    <GutterWrapper
      style={{
        position: 'absolute',
        top: 77,
        right: 32,
      }}
      direction="vertical"
      size="small"
    >
      <GutterWrapper flex size="small">
        <Badge color={SERIES_COLOR_MAP.SPRAYING_MODE} />
        <div>
          喷淋模式
          <PointsLineModalButton
            idcTag={getSpaceGuidMap(blockGuid).idc}
            btnText={
              <DebugPointValue
                {...underSprayingModeCount}
                deviceType={blockDeviceType}
                nonDebugRender={() => (
                  <StatusText status={underSprayingModeCount.status}>
                    {underSprayingModeCount.value}
                  </StatusText>
                )}
              />
            }
            modalText="喷淋模式"
            pointGuids={[
              {
                spaceGuid: blockGuid,
                deviceGuid: blockGuid,
                deviceType: blockDeviceType,
                pointCode: underSprayingModeCount.pointCode,
              },
            ]}
            seriesOption={[{ name: '喷淋模式' }]}
          />
          台
        </div>
      </GutterWrapper>
      <GutterWrapper flex size="small">
        <Badge color={SERIES_COLOR_MAP.DRY_STATE_MODE} />
        <div>
          干态模式
          <PointsLineModalButton
            idcTag={getSpaceGuidMap(blockGuid).idc}
            btnText={
              <DebugPointValue
                {...underDryStateModeCount}
                deviceType={blockDeviceType}
                nonDebugRender={() => (
                  <StatusText status={underDryStateModeCount.status}>
                    {underDryStateModeCount.value}
                  </StatusText>
                )}
              />
            }
            modalText="干态模式"
            pointGuids={[
              {
                spaceGuid: blockGuid,
                deviceGuid: blockGuid,
                deviceType: blockDeviceType,
                pointCode: underDryStateModeCount.pointCode,
              },
            ]}
            seriesOption={[{ name: '干态模式' }]}
          />
          台
        </div>
      </GutterWrapper>
      <GutterWrapper flex size="small">
        <Badge color={SERIES_COLOR_MAP.SPRAYING_N_DX_MODE} />
        <div>
          喷淋+DX模式
          <PointsLineModalButton
            idcTag={getSpaceGuidMap(blockGuid).idc}
            btnText={
              <DebugPointValue
                {...underSprayingnDXModeCount}
                deviceType={blockDeviceType}
                nonDebugRender={() => (
                  <StatusText status={underSprayingnDXModeCount.status}>
                    {underSprayingnDXModeCount.value}
                  </StatusText>
                )}
              />
            }
            modalText="喷淋+DX模式"
            pointGuids={[
              {
                spaceGuid: blockGuid,
                deviceGuid: blockGuid,
                deviceType: blockDeviceType,
                pointCode: underSprayingnDXModeCount.pointCode,
              },
            ]}
            seriesOption={[{ name: '喷淋+DX模式' }]}
          />
          台
        </div>
      </GutterWrapper>
      <GutterWrapper flex size="small">
        <Badge color={SERIES_COLOR_MAP.DRY_N_DX_MODE} />
        <div>
          干态+DX模式
          <PointsLineModalButton
            idcTag={getSpaceGuidMap(blockGuid).idc}
            btnText={
              <DebugPointValue
                {...underDrynDXModeCount}
                deviceType={blockDeviceType}
                nonDebugRender={() => (
                  <StatusText status={underDrynDXModeCount.status}>
                    {underDrynDXModeCount.value}
                  </StatusText>
                )}
              />
            }
            modalText="干态+DX模式"
            pointGuids={[
              {
                spaceGuid: blockGuid,
                deviceGuid: blockGuid,
                deviceType: blockDeviceType,
                pointCode: underDrynDXModeCount.pointCode,
              },
            ]}
            seriesOption={[{ name: '干态+DX模式' }]}
          />
          台
        </div>
      </GutterWrapper>
    </GutterWrapper>
  );
}
