import React from 'react';

import { Space } from '@manyun/base-ui.ui.space';

import AlertingCount from './alerting-count';
import AHUWorkingCount from './working-count';

export default function ExtraCounts({ blockGuid, blockDeviceType, ahuEvaporationUnitDeviceType }) {
  return (
    <Space
      style={{
        position: 'absolute',
        top: 33,
        left: 'calc(50% - 22px)',
        fontSize: 12,
      }}
      direction="vertical"
      size={4}
    >
      <AHUWorkingCount
        spaceGuid={blockGuid}
        device={{ deviceGuid: blockGuid, deviceType: blockDeviceType }}
      />
      <AlertingCount
        blockGuid={blockGuid}
        ahuEvaporationUnitDeviceType={ahuEvaporationUnitDeviceType}
      />
    </Space>
  );
}
