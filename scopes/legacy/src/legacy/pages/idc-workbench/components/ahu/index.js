import React from 'react';
import { connect } from 'react-redux';

import { ConfigUtil } from '@manyun/dc-brain.util.config';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';

import { CardLevel1 } from '../styled';
import Statistics from './statistics';
import TOfAirSupplyLineChart from './t-of-air-supply-line-chart';

function Ahu({ blockGuid, blockDeviceType, configUtil }) {
  return (
    <CardLevel1
      headStyle={{ border: 0, padding: '0 12px' }}
      // bodyStyle={{ padding: 0 }}
      size="small"
      title="AHU"
    >
      <GutterWrapper direction="vertical">
        <Statistics
          blockGuid={blockGuid}
          blockDeviceType={blockDeviceType}
          configUtil={configUtil}
        />
        <TOfAirSupplyLineChart
          blockGuid={blockGuid}
          blockDeviceType={blockDeviceType}
          configUtil={configUtil}
        />
      </GutterWrapper>
    </CardLevel1>
  );
}

const mapStateToProps = ({ config: { configMap } }, { blockGuid }) => {
  const configUtil = new ConfigUtil(configMap.current, { defaultSpaceGuid: blockGuid });
  const blockDeviceType = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_BLOCK);

  return {
    blockDeviceType,
    configUtil,
  };
};

export default connect(mapStateToProps)(Ahu);
