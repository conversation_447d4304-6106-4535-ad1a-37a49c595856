import React from 'react';

import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { getTimeRangeFromNow } from '@manyun/monitoring.chart.duration-select';
import { PointsLine } from '@manyun/monitoring.chart.points-line';

import { getSpaceGuidMap } from '@manyun/dc-brain.legacy.utils';

export default function TOfAirSupplyLineChart({ blockGuid, blockDeviceType, configUtil }) {
  const tOfAirSupplyMaxPointCode = configUtil.getPointCode(
    blockDeviceType,
    ConfigUtil.constants.pointCodes.AHU_EVAPORATION_UNIT_T_OF_AIR_SUPPLY_MAX
  );
  const tOfAirSupplyAvgPointCode = configUtil.getPointCode(
    blockDeviceType,
    ConfigUtil.constants.pointCodes.AHU_EVAPORATION_UNIT_T_OF_AIR_SUPPLY_AVG
  );

  return (
    <PointsLine
      idcTag={getSpaceGuidMap(blockGuid).idc}
      durationSelectStyle={{
        display: 'none',
      }}
      echartStyle={{
        height: 200,
      }}
      allowInterval
      defaultIntervalEnabled
      defaultTimeRange={getTimeRangeFromNow(-1, 'day')}
      chartFunction="MAX"
      pointGuids={[
        {
          spaceGuid: blockGuid,
          deviceGuid: blockGuid,
          deviceType: blockDeviceType,
          pointCode: tOfAirSupplyMaxPointCode,
        },
        {
          spaceGuid: blockGuid,
          deviceGuid: blockGuid,
          deviceType: blockDeviceType,
          pointCode: tOfAirSupplyAvgPointCode,
        },
      ]}
      seriesOption={[{ name: '最高送风温度' }, { name: '平均送风温度' }]}
      basicOption={{
        backgroundColor: 'transparent',
        grid: { right: 40 },
      }}
      showToolbox={false}
    />
  );
}
