import React from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import { getSpaceGuidMap } from '@manyun/dc-brain.legacy.utils';
import * as urlsUtil from '@manyun/dc-brain.legacy.utils/urls';

function AlertingCount({ idc, block, ahuEvaporationUnitDeviceType, alertingCount }) {
  return (
    <Space direction="vertical" size={2}>
      <div>故障数量</div>
      {alertingCount <= 0 && <div>0台</div>}
      {alertingCount > 0 && (
        <div>
          <Link to="/">
            <Typography.Text type="danger">{alertingCount}</Typography.Text>
          </Link>
          台
        </div>
      )}
    </Space>
  );
}

const mapStateToProps = (
  { idcWorkbench: { blockDevicesCountMap } },
  { ahuEvaporationUnitDeviceType, blockGuid }
) => {
  const { idc, block } = getSpaceGuidMap(blockGuid);
  const alertingCount = blockDevicesCountMap[blockGuid]?.[ahuEvaporationUnitDeviceType] || 0;

  return {
    idc,
    block,
    alertingCount,
  };
};

export default connect(mapStateToProps)(AlertingCount);
