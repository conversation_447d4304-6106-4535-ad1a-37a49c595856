import React from 'react';
import { connect } from 'react-redux';

import { Space } from '@manyun/base-ui.ui.space';

import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { PointsLineModalButton } from '@manyun/monitoring.chart.points-line';
import { PointDataRenderer } from '@manyun/monitoring.ui.point-data-renderer';
import { generateGetPointMonitoringData } from '@manyun/monitoring.util.get-monitoring-data';

import { getSpaceGuidMap } from '@manyun/dc-brain.legacy.utils';

/**
 * AHU 开启数量
 *
 * @param {object} props
 * @param {string} props.spaceGuid
 * @param {{ deviceGuid: string; deviceType: string }} props.device
 * @param {import('@manyun/dc-brain.legacy.utils/device').PointData} props.workingCount
 * @returns
 */
function AHUWorkingCount({ spaceGuid, device, workingCount }) {
  return (
    <Space direction="vertical" size={2}>
      <div>开启数量</div>
      <div>
        <AHUWorkingCountLineChartButton
          spaceGuid={spaceGuid}
          device={device}
          workingCount={workingCount}
        />
        台
      </div>
    </Space>
  );
}

const mapStateToProps = (
  { 'monitoring.subscriptions': { devicesRealtimeData, devicesAlarmsData }, config: { configMap } },
  { spaceGuid, device }
) => {
  const configUtil = new ConfigUtil(configMap.current, { defaultSpaceGuid: spaceGuid });
  const workingCountPointCode = configUtil.getPointCode(
    device.deviceType,
    ConfigUtil.constants.pointCodes.AHU_EVAPORATION_UNIT_WORKING_COUNT
  );
  const getData = generateGetPointMonitoringData({
    realtimeData: devicesRealtimeData,
    alarmsData: devicesAlarmsData,
    configUtil,
  });
  const workingCount = getData(device, {
    hardCodedPointCode: workingCountPointCode,
    defaults: { value: 0 },
  });

  return { workingCount };
};

export default connect(mapStateToProps)(AHUWorkingCount);

/**
 * AHU 开启数量曲线图按钮
 *
 * @param {object} props
 * @param {string} props.spaceGuid
 * @param {{ deviceGuid: string; deviceType: string }} props.device
 * @param {import('@manyun/dc-brain.legacy.utils/device').PointData} props.workingCount
 * @returns
 */
export function AHUWorkingCountLineChartButton({ spaceGuid, device, workingCount }) {
  return (
    <PointsLineModalButton
      idcTag={getSpaceGuidMap(spaceGuid).idc}
      btnText={<PointDataRenderer data={workingCount} />}
      modalText="开启数量"
      pointGuids={[
        {
          spaceGuid,
          ...device,
          pointCode: workingCount.pointCode,
        },
      ]}
      seriesOption={[{ name: '开启数量' }]}
    />
  );
}
