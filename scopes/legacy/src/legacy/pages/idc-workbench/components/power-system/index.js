import React from 'react';
import { connect } from 'react-redux';

import get from 'lodash/get';

import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { PointsLineModalButton } from '@manyun/monitoring.chart.points-line';
import { getTop1ByPositiveCount } from '@manyun/monitoring.model.point';
import { PointDataRenderer } from '@manyun/monitoring.ui.point-data-renderer';
import { generateGetPointMonitoringData } from '@manyun/monitoring.util.get-monitoring-data';

import { StatusText } from '@manyun/dc-brain.legacy.components';
import { STATUS_MAP } from '@manyun/dc-brain.legacy.constants/statusColor';
import { getSpaceGuid } from '@manyun/dc-brain.legacy.utils';
import { getSpaceGuidMap } from '@manyun/dc-brain.legacy.utils';

import { CardLevel1, StyledTable } from '../styled';

export function PowerSystem({ columns, data }) {
  return (
    <CardLevel1
      headStyle={{ border: 0, padding: '0 12px' }}
      bodyStyle={{ padding: 0 }}
      size="small"
      title="供电系统"
    >
      <StyledTable size="small" columns={columns} dataSource={data} pagination={false} />
    </CardLevel1>
  );
}

const mapStateToProps = (
  {
    idcWorkbench: { blockDevicesMap, blockDevicesCountMap },
    'monitoring.subscriptions': { devicesRealtimeData, devicesAlarmsData },
    config: { configMap },
  },
  { blockGuid }
) => {
  const configUtil = new ConfigUtil(configMap.current, { defaultSpaceGuid: blockGuid });
  const devicesMap = blockDevicesMap[blockGuid];
  const blockDevicesCount = blockDevicesCountMap[blockGuid];
  const blockDeviceType = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_BLOCK);
  const showHvdcPoints =
    configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.HVDC, {
      useFallback: false,
      logLevel: 'silent',
    }) !== undefined;
  const deviceGuid = blockGuid;
  const device = { deviceGuid, deviceType: blockDeviceType };
  const getData = generateGetPointMonitoringData({
    realtimeData: devicesRealtimeData,
    alarmsData: devicesAlarmsData,
    configUtil,
  });

  const hvIncomingSwitchgears = get(
    devicesMap,
    configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.HV_INCOMING_SWITCHGEAR),
    []
  );

  const sumDeviceTypeAlarmsCount = deviceTypes =>
    deviceTypes.reduce((count, deviceType) => count + get(blockDevicesCount, deviceType, 0), 0);

  const utilityGridLoadRateMaxPointCode = configUtil.getPointCode(
    blockDeviceType,
    ConfigUtil.constants.pointCodes.UTILITY_GRID_LOAD_RATE_MAX
  );
  const utilityGridLoadRateMax = getData(device, {
    formatted: true,
    hardCodedPointCode: utilityGridLoadRateMaxPointCode,
  });
  // 显示每个高压进线柜的负载率
  const utilityGridPointGuids = hvIncomingSwitchgears.map(
    ({ guid, name, deviceType, spaceGuid }) => ({
      serieName: name,
      unit: utilityGridLoadRateMax.unit,
      deviceGuid: guid,
      pointCode: configUtil.getPointCode(
        deviceType,
        ConfigUtil.constants.pointCodes.HV_INCOMING_SWITCHGEAR_LOAD_RATE
      ),
      deviceType,
      spaceGuid: getSpaceGuid(spaceGuid.idcTag, spaceGuid.blockTag, spaceGuid.roomTag),
    })
  );
  const utilityGridLoadRateAvgPointCode = configUtil.getPointCode(
    blockDeviceType,
    ConfigUtil.constants.pointCodes.UTILITY_GRID_LOAD_RATE_AVG
  );
  const utilityGridLoadRateAvg = getData(device, {
    formatted: true,
    hardCodedPointCode: utilityGridLoadRateAvgPointCode,
  });

  const generatorLoadRateMaxPointCode = configUtil.getPointCode(
    blockDeviceType,
    ConfigUtil.constants.pointCodes.GENERATOR_LOAD_RATE_MAX
  );
  const generatorLoadRateMax = getData(device, {
    formatted: true,
    hardCodedPointCode: generatorLoadRateMaxPointCode,
  });
  const generatorLoadRateMaxPointGuids = [
    {
      serieName: '最高负载率',
      unit: generatorLoadRateMax.unit,
      deviceGuid,
      pointCode: generatorLoadRateMaxPointCode,
      deviceType: blockDeviceType,
      spaceGuid: blockGuid,
    },
  ];

  const generatorLoadRateAvgPointCode = configUtil.getPointCode(
    blockDeviceType,
    ConfigUtil.constants.pointCodes.GENERATOR_LOAD_RATE_AVG
  );
  const generatorLoadRateAvg = getData(device, {
    formatted: true,
    hardCodedPointCode: generatorLoadRateAvgPointCode,
  });
  const generatorLoadRateAvgPointGuids = [
    {
      serieName: '平均负载率',
      unit: generatorLoadRateAvg.unit,
      deviceGuid,
      pointCode: generatorLoadRateAvgPointCode,
      deviceType: blockDeviceType,
      spaceGuid: blockGuid,
    },
  ];

  const transformerLoadRateMaxPointCode = configUtil.getPointCode(
    blockDeviceType,
    ConfigUtil.constants.pointCodes.TRANSFORMER_LOAD_RATE_MAX
  );
  const transformerLoadRateMax = getData(device, {
    formatted: true,
    hardCodedPointCode: transformerLoadRateMaxPointCode,
  });
  const transformerLoadRateMaxPointGuids = [
    {
      serieName: '最高负载率',
      unit: transformerLoadRateMax.unit,
      deviceGuid,
      pointCode: transformerLoadRateMaxPointCode,
      deviceType: blockDeviceType,
      spaceGuid: blockGuid,
    },
  ];

  const transformerLoadRateAvgPointCode = configUtil.getPointCode(
    blockDeviceType,
    ConfigUtil.constants.pointCodes.TRANSFORMER_LOAD_RATE_AVG
  );
  const transformerLoadRateAvg = getData(device, {
    formatted: true,
    hardCodedPointCode: transformerLoadRateAvgPointCode,
  });
  const transformerLoadRateAvgPointGuids = [
    {
      serieName: '平均负载率',
      unit: transformerLoadRateAvg.unit,
      deviceGuid,
      pointCode: transformerLoadRateAvgPointCode,
      deviceType: blockDeviceType,
      spaceGuid: blockGuid,
    },
  ];

  const upsLoadRateMaxPointCode = configUtil.getPointCode(
    blockDeviceType,
    ConfigUtil.constants.pointCodes.THREE_PHASE_MODULAR_UPS_LOAD_RATE_MAX
  );
  const upsLoadRateMax = getData(device, {
    formatted: true,
    hardCodedPointCode: upsLoadRateMaxPointCode,
  });
  const upsLoadRateMaxPointGuids = [
    {
      serieName: '最高负载率',
      unit: upsLoadRateMax.unit,
      deviceGuid,
      pointCode: upsLoadRateMaxPointCode,
      deviceType: blockDeviceType,
      spaceGuid: blockGuid,
    },
  ];

  const upsLoadRateAvgPointCode = configUtil.getPointCode(
    blockDeviceType,
    ConfigUtil.constants.pointCodes.THREE_PHASE_MODULAR_UPS_LOAD_RATE_AVG
  );
  const upsLoadRateAvg = getData(device, {
    formatted: true,
    hardCodedPointCode: upsLoadRateAvgPointCode,
  });
  const upsLoadRateAvgPointGuids = [
    {
      serieName: '平均负载率',
      unit: upsLoadRateAvg.unit,
      deviceGuid,
      pointCode: upsLoadRateAvgPointCode,
      deviceType: blockDeviceType,
      spaceGuid: blockGuid,
    },
  ];

  const rppLoadRateMaxPointCode = configUtil.getPointCode(
    blockDeviceType,
    ConfigUtil.constants.pointCodes.RPP_LOAD_RATE_MAX
  );
  const rppLoadRateMax = getData(device, {
    formatted: true,
    hardCodedPointCode: rppLoadRateMaxPointCode,
  });

  const rppLoadRateAvgPointCode = configUtil.getPointCode(
    blockDeviceType,
    ConfigUtil.constants.pointCodes.RPP_LOAD_RATE_AVG
  );
  const rppLoadRateAvg = getData(device, {
    formatted: true,
    hardCodedPointCode: rppLoadRateAvgPointCode,
  });

  const columns = [
    {
      key: 'prop-name',
      render(_, __, idx) {
        switch (idx) {
          case 0:
            return '运行状态';
          case 1:
            return '运行模式';
          case 2:
            return '最高负载率';
          case 3:
            return '平均负载率';
          default:
            return 'Not defined.';
        }
      },
    },
    {
      title: '市电',
      dataIndex: 'utility-grid',
      render(text, _, idx) {
        if (idx === 0) {
          return (
            <StatusText background status={text.count > 0 ? STATUS_MAP.ALARM : STATUS_MAP.NORMAL}>
              {text.count + text.name}
            </StatusText>
          );
        }
        if (idx === 1) {
          return text.count + text.name;
        }

        return (
          <PointsLineModalButton
            idcTag={getSpaceGuidMap(blockGuid).idc}
            basicOption={{ grid: { right: 40, bottom: 80, left: 50 } }}
            btnText={<PointDataRenderer data={text} />}
            modalText={blockGuid + ` 市电负载率`}
            pointGuids={text.pointGuids}
          />
        );
      },
    },
    {
      title: '柴发',
      dataIndex: 'generator',
      render(text, _, idx) {
        if (idx === 0) {
          return (
            <StatusText background status={text.count > 0 ? STATUS_MAP.ALARM : STATUS_MAP.NORMAL}>
              {text.count + text.name}
            </StatusText>
          );
        }
        if (idx === 1) {
          return text.count + text.name;
        }

        return (
          <PointsLineModalButton
            idcTag={getSpaceGuidMap(blockGuid).idc}
            btnText={<PointDataRenderer data={text} />}
            modalText={blockGuid + ` 柴发`}
            pointGuids={text.pointGuids}
          />
        );
      },
    },
    {
      title: '变压器',
      dataIndex: 'transformer',
      render(text, _, idx) {
        if (idx === 0) {
          return (
            <StatusText background status={text.count > 0 ? STATUS_MAP.ALARM : STATUS_MAP.NORMAL}>
              {text.count + text.name}
            </StatusText>
          );
        }
        if (idx === 1) {
          return text.count + text.name;
        }

        return (
          <PointsLineModalButton
            idcTag={getSpaceGuidMap(blockGuid).idc}
            btnText={<PointDataRenderer data={text} />}
            modalText={blockGuid + ` 变压器`}
            pointGuids={text.pointGuids}
          />
        );
      },
    },
    {
      title: 'UPS',
      dataIndex: 'ups',
      render(text, _, idx) {
        if (idx === 0) {
          return (
            <StatusText background status={text.count > 0 ? STATUS_MAP.ALARM : STATUS_MAP.NORMAL}>
              {text.count + text.name}
            </StatusText>
          );
        }
        if (idx === 1) {
          return text.count + text.name;
        }

        return (
          <PointsLineModalButton
            idcTag={getSpaceGuidMap(blockGuid).idc}
            btnText={<PointDataRenderer data={text} />}
            modalText={blockGuid + ` UPS`}
            pointGuids={text.pointGuids}
          />
        );
      },
    },
    {
      title: '列头柜',
      dataIndex: 'rpp',
      render(text, _, idx) {
        if (idx === 0) {
          return (
            <StatusText background status={text.count > 0 ? STATUS_MAP.ALARM : STATUS_MAP.NORMAL}>
              {text.count + text.name}
            </StatusText>
          );
        }
        if (idx === 1) {
          return text.count + text.name;
        }

        return (
          <PointsLineModalButton
            idcTag={getSpaceGuidMap(blockGuid).idc}
            btnText={<PointDataRenderer data={text} />}
            modalText={blockGuid + ' 列头柜'}
            pointGuids={text.pointGuids}
          />
        );
      },
    },
    {
      visible: showHvdcPoints,
      title: 'HVDC',
      dataIndex: 'hvdc',
      render(text, _, idx) {
        if (idx === 0) {
          return (
            <StatusText background status={text.count > 0 ? STATUS_MAP.ALARM : STATUS_MAP.NORMAL}>
              {text.count + text.name}
            </StatusText>
          );
        }
        if (idx === 1) {
          return '--';
        }

        return (
          <PointsLineModalButton
            idcTag={getSpaceGuidMap(blockGuid).idc}
            btnText={<PointDataRenderer data={text} />}
            modalText={blockGuid + ' HVDC'}
            pointGuids={text.pointGuids}
          />
        );
      },
    },
  ];

  const data = [
    // 运行状态
    {
      key: 'working-state',
      'utility-grid': getTop1ByPositiveCount(
        [
          {
            name: '路异常',
            count: get(
              blockDevicesCount,
              configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.HV_INCOMING_SWITCHGEAR),
              0
            ),
          },
        ],
        {
          name: '正常',
          count: '',
        }
      ),
      generator: getTop1ByPositiveCount(
        [
          {
            name: '台异常',
            count: sumDeviceTypeAlarmsCount(
              configUtil.getAllDeviceTypes(ConfigUtil.constants.deviceTypes.GENERATOR)
            ),
          },
        ],
        {
          name: '正常',
          count: '',
        }
      ),
      transformer: getTop1ByPositiveCount(
        [
          {
            name: '台异常',
            count: get(
              blockDevicesCount,
              configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.TRANSFORMER),
              0
            ),
          },
        ],
        {
          name: '正常',
          count: '',
        }
      ),
      ups: getTop1ByPositiveCount(
        [
          {
            name: '台异常',
            count: sumDeviceTypeAlarmsCount(
              configUtil.getAllDeviceTypes(ConfigUtil.constants.deviceTypes.UPS)
            ),
          },
        ],
        {
          name: '正常',
          count: '',
        }
      ),
      rpp: getTop1ByPositiveCount(
        [
          {
            name: '台异常',
            count: sumDeviceTypeAlarmsCount(
              configUtil.getAllDeviceTypes(ConfigUtil.constants.deviceTypes.RPP)
            ),
          },
        ],
        {
          name: '正常',
          count: '',
        }
      ),
      hvdc: {
        name: '--',
        count: '',
      },
    },
    // 运行模式
    {
      key: 'working-mode',
      'utility-grid': getTop1ByPositiveCount([
        {
          name: '路失电',
          count: getData(device, {
            hardCodedPointCode: configUtil.getPointCode(
              blockDeviceType,
              ConfigUtil.constants.pointCodes.HV_INCOMING_SWITCHGEAR_POWER_OFF_COUNT
            ),
          }).value,
        },
        {
          name: '路正常供电',
          count: getData(device, {
            hardCodedPointCode: configUtil.getPointCode(
              blockDeviceType,
              ConfigUtil.constants.pointCodes.HV_INCOMING_SWITCHGEAR_POWER_ON_COUNT
            ),
          }).value,
        },
      ]),
      generator: getTop1ByPositiveCount(
        [
          {
            name: '台停机',
            count: getData(device, {
              hardCodedPointCode: configUtil.getPointCode(
                blockDeviceType,
                ConfigUtil.constants.pointCodes.GENERATOR_POWER_OFF_COUNT
              ),
            }).value,
          },
          {
            name: '台运行',
            count: getData(device, {
              hardCodedPointCode: configUtil.getPointCode(
                blockDeviceType,
                ConfigUtil.constants.pointCodes.GENERATOR_WORKING_COUNT
              ),
            }).value,
          },
        ],
        { name: '待命', count: '' }
      ),
      transformer: getTop1ByPositiveCount([
        {
          name: '台停运',
          count: getData(device, {
            hardCodedPointCode: configUtil.getPointCode(
              blockDeviceType,
              ConfigUtil.constants.pointCodes.TRANSFORMER_ISOLATION_SWITCHGEAR_POWER_OFF_COUNT
            ),
          }).value,
        },
        {
          name: '台运行',
          count: getData(device, {
            hardCodedPointCode: configUtil.getPointCode(
              blockDeviceType,
              ConfigUtil.constants.pointCodes.TRANSFORMER_ISOLATION_SWITCHGEAR_POWER_ON_COUNT
            ),
          }).value,
        },
      ]),
      ups: getTop1ByPositiveCount(
        [
          {
            name: '台均不供电',
            count: getData(device, {
              hardCodedPointCode: configUtil.getPointCode(
                blockDeviceType,
                ConfigUtil.constants.pointCodes.UPS_POWER_OFF_COUNT
              ),
            }).value,
          },
          {
            name: '台电池供电',
            count: getData(device, {
              hardCodedPointCode: configUtil.getPointCode(
                blockDeviceType,
                ConfigUtil.constants.pointCodes.UPS_BATTERY_MODE_COUNT
              ),
            }).value,
          },
          {
            name: '台旁路供电',
            count: getData(device, {
              hardCodedPointCode: configUtil.getPointCode(
                blockDeviceType,
                ConfigUtil.constants.pointCodes.UPS_BYPASS_MODE_COUNT
              ),
            }).value,
          },
        ],
        {
          name: '正常供电',
          count: '',
        }
      ),
      rpp: getTop1ByPositiveCount([
        {
          name: '台分闸',
          count: getData(device, {
            hardCodedPointCode: configUtil.getPointCode(
              blockDeviceType,
              ConfigUtil.constants.pointCodes.RPP_POWER_OFF_COUNT
            ),
          }).value,
        },
        {
          name: '台合闸',
          count: getData(device, {
            hardCodedPointCode: configUtil.getPointCode(
              blockDeviceType,
              ConfigUtil.constants.pointCodes.RPP_POWER_ON_COUNT
            ),
          }).value,
        },
      ]),
      hvdc: '--',
    },
    // 最高负载率
    {
      key: 'load-rate--max',
      'utility-grid': {
        ...utilityGridLoadRateMax,
        pointGuids: utilityGridPointGuids,
      },
      generator: {
        ...generatorLoadRateMax,
        pointGuids: generatorLoadRateMaxPointGuids,
      },
      transformer: {
        ...transformerLoadRateMax,
        pointGuids: transformerLoadRateMaxPointGuids,
      },
      ups: {
        ...upsLoadRateMax,
        pointGuids: upsLoadRateMaxPointGuids,
      },
      rpp: {
        ...rppLoadRateMax,
        pointGuids: [
          {
            serieName: '最高负载率',
            deviceGuid,
            pointCode: rppLoadRateMaxPointCode,
            deviceType: blockDeviceType,
          },
        ],
      },
      hvdc: '--',
    },
    // 平均负载率
    {
      key: 'load-rate--avg',
      'utility-grid': {
        ...utilityGridLoadRateAvg,
        pointGuids: utilityGridPointGuids,
      },
      generator: {
        ...generatorLoadRateAvg,
        pointGuids: generatorLoadRateAvgPointGuids,
      },
      transformer: {
        ...transformerLoadRateAvg,
        pointGuids: transformerLoadRateAvgPointGuids,
      },
      ups: {
        ...upsLoadRateAvg,
        pointGuids: upsLoadRateAvgPointGuids,
      },
      rpp: {
        ...rppLoadRateAvg,
        pointGuids: [
          {
            serieName: '平均负载率',
            unit: rppLoadRateMax.unit,
            deviceGuid,
            pointCode: rppLoadRateAvgPointCode,
          },
        ],
      },
      hvdc: '--',
    },
  ];

  return { columns, data };
};

export default connect(mapStateToProps)(PowerSystem);
