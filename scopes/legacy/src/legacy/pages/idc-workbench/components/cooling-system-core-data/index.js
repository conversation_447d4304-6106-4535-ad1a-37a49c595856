import React from 'react';
import { connect } from 'react-redux';

import get from 'lodash/get';

import { Descriptions } from '@manyun/base-ui.ui.descriptions';

import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { PointsLineModalButton } from '@manyun/monitoring.chart.points-line';
import { PointDataRenderer } from '@manyun/monitoring.ui.point-data-renderer';
import { generateGetPointMonitoringData } from '@manyun/monitoring.util.get-monitoring-data';

import { getSpaceGuid } from '@manyun/dc-brain.legacy.utils';
import { getSpaceGuidMap } from '@manyun/dc-brain.legacy.utils';

export function CoolingSystemCoreData({
  blockGuid,
  showAhuPoints,
  tOfChilledWaterSupply,
  tOfReturnChilledWater,
  diffTOfSnrChilledWater,
  pressureOfChilledWaterSupply,
  pressureOfReturnChilledWater,
  tesTankLiquidLevelMin,
  tesTankTOfWaterMax,
  tesTankTOfWaterAvg,
  waterTankLiquidLevelMin,
  ahuTOfAirSupplyMax,
  ahuTOfReturnAirMax,
}) {
  return (
    <>
      <div style={{ textAlign: 'center', padding: '8px 0' }}>制冷系统核心参数</div>
      <Descriptions style={{ width: '100%' }} size="small">
        <Descriptions.Item label="冷冻水供水温度">
          <PointsLineModalButton
            idcTag={getSpaceGuidMap(blockGuid).idc}
            btnText={<PointDataRenderer data={tOfChilledWaterSupply} />}
            modalText={blockGuid + ' 各个冷冻水管道的供水温度'}
            pointGuids={tOfChilledWaterSupply.pointGuids}
          />
        </Descriptions.Item>
        <Descriptions.Item label="冷冻水回水温度">
          <PointsLineModalButton
            idcTag={getSpaceGuidMap(blockGuid).idc}
            btnText={<PointDataRenderer data={tOfReturnChilledWater} />}
            modalText={blockGuid + ' 各个冷冻水管道的回水温度'}
            pointGuids={tOfReturnChilledWater.pointGuids}
          />
        </Descriptions.Item>
        <Descriptions.Item label="冷冻水供回水温差">
          <PointsLineModalButton
            idcTag={getSpaceGuidMap(blockGuid).idc}
            btnText={<PointDataRenderer data={diffTOfSnrChilledWater} />}
            modalText={blockGuid + ' 冷冻水管道的供回水温差'}
            pointGuids={diffTOfSnrChilledWater.pointGuids}
          />
        </Descriptions.Item>
        <Descriptions.Item label="冷冻水供水压力">
          <PointsLineModalButton
            idcTag={getSpaceGuidMap(blockGuid).idc}
            btnText={<PointDataRenderer data={pressureOfChilledWaterSupply} />}
            modalText={blockGuid + ' 各个冷冻水管道的供水压力'}
            pointGuids={pressureOfChilledWaterSupply.pointGuids}
          />
        </Descriptions.Item>
        <Descriptions.Item label="冷冻水回水压力">
          <PointsLineModalButton
            idcTag={getSpaceGuidMap(blockGuid).idc}
            btnText={<PointDataRenderer data={pressureOfReturnChilledWater} />}
            modalText={blockGuid + ' 各个冷冻水管道的回水压力'}
            pointGuids={pressureOfReturnChilledWater.pointGuids}
          />
        </Descriptions.Item>
        <Descriptions.Item label="蓄冷罐最小液位">
          <PointsLineModalButton
            idcTag={getSpaceGuidMap(blockGuid).idc}
            btnText={<PointDataRenderer data={tesTankLiquidLevelMin} />}
            modalText={blockGuid + ' 各个蓄冷罐最小液位'}
            pointGuids={tesTankLiquidLevelMin.pointGuids}
          />
        </Descriptions.Item>
        <Descriptions.Item label="蓄冷罐最高水温">
          <PointsLineModalButton
            idcTag={getSpaceGuidMap(blockGuid).idc}
            btnText={<PointDataRenderer data={tesTankTOfWaterMax} />}
            modalText={blockGuid + ' 各个蓄冷罐最高水温'}
            pointGuids={tesTankTOfWaterMax.pointGuids}
          />
        </Descriptions.Item>
        <Descriptions.Item label="蓄冷罐平均温度">
          <PointsLineModalButton
            idcTag={getSpaceGuidMap(blockGuid).idc}
            btnText={<PointDataRenderer data={tesTankTOfWaterAvg} />}
            modalText={blockGuid + ' 各个蓄冷罐平均温度'}
            pointGuids={tesTankTOfWaterAvg.pointGuids}
          />
        </Descriptions.Item>
        <Descriptions.Item label="水池最小液位">
          <PointsLineModalButton
            idcTag={getSpaceGuidMap(blockGuid).idc}
            btnText={<PointDataRenderer data={waterTankLiquidLevelMin} />}
            modalText={blockGuid + ' 各个水池的最小液位'}
            pointGuids={waterTankLiquidLevelMin.pointGuids}
          />
        </Descriptions.Item>
        {showAhuPoints && (
          <Descriptions.Item label="AHU送风温度">
            <PointDataRenderer data={ahuTOfAirSupplyMax} />
          </Descriptions.Item>
        )}
        {showAhuPoints && (
          <Descriptions.Item label="AHU回风温度">
            <PointDataRenderer data={ahuTOfReturnAirMax} />
          </Descriptions.Item>
        )}
      </Descriptions>
    </>
  );
}

const mapStateToProps = (
  {
    idcWorkbench: { blockDevicesMap },
    'monitoring.subscriptions': { devicesRealtimeData, devicesAlarmsData },
    config: { configMap },
  },
  { blockGuid }
) => {
  const configUtil = new ConfigUtil(configMap.current, { defaultSpaceGuid: blockGuid });
  const blockDeviceType = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_BLOCK);
  const devicesMap = blockDevicesMap[blockGuid];
  const chilldWaterPipeBranchWaterSupplyThermometers = get(
    devicesMap,
    configUtil.getOneDeviceType(
      ConfigUtil.constants.deviceTypes.CHILLED_WATER_PIPE_BRANCH_WATER_SUPPLY_THERMOMETER
    ),
    []
  );
  const chilldWaterPipeBranchReturnWaterThermometers = get(
    devicesMap,
    configUtil.getOneDeviceType(
      ConfigUtil.constants.deviceTypes.CHILLED_WATER_PIPE_BRANCH_RETURN_WATER_THERMOMETER
    ),
    []
  );
  const chilldWaterPipeBranchWaterSupplyManometers = get(
    devicesMap,
    configUtil.getOneDeviceType(
      ConfigUtil.constants.deviceTypes.CHILLED_WATER_PIPE_BRANCH_WATER_SUPPLY_MANOMETER
    ),
    []
  );
  const chilldWaterPipeBranchReturnWaterManometers = get(
    devicesMap,
    configUtil.getOneDeviceType(
      ConfigUtil.constants.deviceTypes.CHILLED_WATER_PIPE_BRANCH_RETURN_WATER_MANOMETER
    ),
    []
  );
  const tesTanks = get(
    devicesMap,
    configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.TES_TANK),
    []
  );
  const waterTanks = get(
    devicesMap,
    configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.WATER_TANK),
    []
  );
  const showAhuPoints =
    configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.AHU, {
      useFallback: false,
      logLevel: 'silent',
    }) !== undefined;
  const deviceGuid = blockGuid;
  const device = { deviceGuid, deviceType: blockDeviceType };
  const getData = generateGetPointMonitoringData({
    realtimeData: devicesRealtimeData,
    alarmsData: devicesAlarmsData,
    configUtil,
  });

  const tOfChilledWaterSupply = getData(device, {
    hardCodedPointCode: configUtil.getPointCode(
      blockDeviceType,
      ConfigUtil.constants.pointCodes.PIPE_T_OF_CHILLED_WATER_SUPPLY_MAX
    ),
    formatted: true,
  });
  tOfChilledWaterSupply.pointGuids = chilldWaterPipeBranchWaterSupplyThermometers.map(
    ({ name, guid, deviceType, spaceGuid }) => ({
      serieName: name,
      unit: tOfChilledWaterSupply.unit,
      deviceGuid: guid,
      pointCode: configUtil.getPointCode(
        deviceType,
        ConfigUtil.constants.pointCodes.CHILLED_WATER_PIPE_T_OF_WATER_SUPPLY
      ),
      deviceType,
      spaceGuid: getSpaceGuid(spaceGuid.idcTag, spaceGuid.blockTag, spaceGuid.roomTag),
    })
  );

  const tOfReturnChilledWater = getData(device, {
    hardCodedPointCode: configUtil.getPointCode(
      blockDeviceType,
      ConfigUtil.constants.pointCodes.PIPE_T_OF_RETURN_CHILLED_WATER_MAX
    ),
    formatted: true,
  });
  tOfReturnChilledWater.pointGuids = chilldWaterPipeBranchReturnWaterThermometers.map(
    ({ name, guid, deviceType, spaceGuid }) => ({
      serieName: name,
      unit: tOfReturnChilledWater.unit,
      deviceGuid: guid,
      pointCode: configUtil.getPointCode(
        deviceType,
        ConfigUtil.constants.pointCodes.CHILLED_WATER_PIPE_T_OF_RETURN_WATER
      ),
      deviceType,
      spaceGuid: getSpaceGuid(spaceGuid.idcTag, spaceGuid.blockTag, spaceGuid.roomTag),
    })
  );

  const diffTOfSnrChilledWaterPointCode = configUtil.getPointCode(
    blockDeviceType,
    ConfigUtil.constants.pointCodes.PIPE_DIFF_T_OF_SNR_CHILLED_WATER_MAX
  );
  const diffTOfSnrChilledWater = getData(device, {
    hardCodedPointCode: diffTOfSnrChilledWaterPointCode,
    formatted: true,
  });
  diffTOfSnrChilledWater.pointGuids = [
    {
      serieName: `冷冻水供回水温差`,
      unit: tOfReturnChilledWater.unit,
      deviceGuid: device.deviceGuid,
      deviceType: device.deviceType,
      pointCode: diffTOfSnrChilledWaterPointCode,
      spaceGuid: blockGuid,
    },
  ];

  const pressureOfChilledWaterSupply = getData(device, {
    hardCodedPointCode: configUtil.getPointCode(
      blockDeviceType,
      ConfigUtil.constants.pointCodes.PIPE_PRESSURE_OF_CHILLED_WATER_SUPPLY_MAX
    ),
    formatted: true,
  });
  pressureOfChilledWaterSupply.pointGuids = chilldWaterPipeBranchWaterSupplyManometers.map(
    ({ name, guid, deviceType, spaceGuid }) => ({
      serieName: name,
      unit: pressureOfChilledWaterSupply.unit,
      deviceGuid: guid,
      pointCode: configUtil.getPointCode(
        deviceType,
        ConfigUtil.constants.pointCodes.CHILLED_WATER_PIPE_PRESSURE_OF_WATER_SUPPLY
      ),
      deviceType,
      spaceGuid: getSpaceGuid(spaceGuid.idcTag, spaceGuid.blockTag, spaceGuid.roomTag),
    })
  );

  const pressureOfReturnChilledWater = getData(device, {
    hardCodedPointCode: configUtil.getPointCode(
      blockDeviceType,
      ConfigUtil.constants.pointCodes.PIPE_PRESSURE_OF_RETURN_CHILLED_WATER_MAX
    ),
    formatted: true,
  });
  pressureOfReturnChilledWater.pointGuids = chilldWaterPipeBranchReturnWaterManometers.map(
    ({ name, guid, deviceType, spaceGuid }) => ({
      serieName: name,
      unit: pressureOfReturnChilledWater.unit,
      deviceGuid: guid,
      pointCode: configUtil.getPointCode(
        deviceType,
        ConfigUtil.constants.pointCodes.CHILLED_WATER_PIPE_PRESSURE_OF_RETURN_WATER
      ),
      deviceType,
      spaceGuid: getSpaceGuid(spaceGuid.idcTag, spaceGuid.blockTag, spaceGuid.roomTag),
    })
  );

  const tesTankLiquidLevelMin = getData(device, {
    hardCodedPointCode: configUtil.getPointCode(
      blockDeviceType,
      ConfigUtil.constants.pointCodes.TES_TANK_LIQUID_LEVEL_MIN
    ),
    formatted: true,
  });
  tesTankLiquidLevelMin.pointGuids = tesTanks.map(({ name, guid, deviceType, spaceGuid }) => ({
    serieName: name,
    unit: tesTankLiquidLevelMin.unit,
    deviceGuid: guid,
    pointCode: configUtil.getPointCode(
      deviceType,
      ConfigUtil.constants.pointCodes.TES_TANK_LIQUID_LEVEL_MIN
    ),
    deviceType,
    spaceGuid: getSpaceGuid(spaceGuid.idcTag, spaceGuid.blockTag, spaceGuid.roomTag),
  }));

  const tesTankTOfWaterMax = getData(device, {
    hardCodedPointCode: configUtil.getPointCode(
      blockDeviceType,
      ConfigUtil.constants.pointCodes.TES_TANK_T_OF_WATER_MAX
    ),
    formatted: true,
  });
  tesTankTOfWaterMax.pointGuids = tesTanks.map(({ name, guid, deviceType, spaceGuid }) => ({
    serieName: name,
    unit: tesTankTOfWaterMax.unit,
    deviceGuid: guid,
    pointCode: configUtil.getPointCode(
      deviceType,
      ConfigUtil.constants.pointCodes.TES_TANK_T_OF_WATER_MAX
    ),
    deviceType,
    spaceGuid: getSpaceGuid(spaceGuid.idcTag, spaceGuid.blockTag, spaceGuid.roomTag),
  }));

  const tesTankTOfWaterAvg = getData(device, {
    hardCodedPointCode: configUtil.getPointCode(
      blockDeviceType,
      ConfigUtil.constants.pointCodes.TES_TANK_T_OF_WATER_AVG
    ),
    formatted: true,
  });
  tesTankTOfWaterAvg.pointGuids = tesTanks.map(({ name, guid, deviceType, spaceGuid }) => ({
    serieName: name,
    unit: tesTankTOfWaterAvg.unit,
    deviceGuid: guid,
    pointCode: configUtil.getPointCode(
      deviceType,
      ConfigUtil.constants.pointCodes.TES_TANK_T_OF_WATER_AVG
    ),
    deviceType,
    spaceGuid: getSpaceGuid(spaceGuid.idcTag, spaceGuid.blockTag, spaceGuid.roomTag),
  }));

  const waterTankLiquidLevelMin = getData(device, {
    hardCodedPointCode: configUtil.getPointCode(
      blockDeviceType,
      ConfigUtil.constants.pointCodes.WATER_TANK_LIQUID_LEVEL_MIN
    ),
    formatted: true,
  });
  waterTankLiquidLevelMin.pointGuids = waterTanks.map(({ name, guid, deviceType, spaceGuid }) => ({
    serieName: name,
    unit: waterTankLiquidLevelMin.unit,
    deviceGuid: guid,
    pointCode: configUtil.getPointCode(
      deviceType,
      ConfigUtil.constants.pointCodes.WATER_TANK_LIQUID_LEVEL_MIN
    ),
    deviceType,
    spaceGuid: getSpaceGuid(spaceGuid.idcTag, spaceGuid.blockTag, spaceGuid.roomTag),
  }));

  const ahuTOfAirSupplyMax = getData(device, {
    hardCodedPointCode: configUtil.getPointCode(
      blockDeviceType,
      ConfigUtil.constants.pointCodes.AHU_T_OF_AIR_SUPPLY_MAX
    ),
    formatted: true,
  });
  const ahuTOfReturnAirMax = getData(device, {
    hardCodedPointCode: configUtil.getPointCode(
      blockDeviceType,
      ConfigUtil.constants.pointCodes.AHU_T_OF_RETURN_AIR_MAX
    ),
    formatted: true,
  });

  return {
    showAhuPoints,
    tOfChilledWaterSupply,
    tOfReturnChilledWater,
    diffTOfSnrChilledWater,
    pressureOfChilledWaterSupply,
    pressureOfReturnChilledWater,
    tesTankLiquidLevelMin,
    tesTankTOfWaterMax,
    tesTankTOfWaterAvg,
    waterTankLiquidLevelMin,
    ahuTOfAirSupplyMax,
    ahuTOfReturnAirMax,
  };
};

export default connect(mapStateToProps)(CoolingSystemCoreData);
