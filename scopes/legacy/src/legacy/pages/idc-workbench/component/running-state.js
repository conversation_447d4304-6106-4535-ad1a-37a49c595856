import React, { Component } from 'react';
import { connect } from 'react-redux';

import { Space } from '@manyun/base-ui.ui.space';

import { ConfigUtil } from '@manyun/dc-brain.util.config';

import { GutterWrapper, TinyEmpty } from '@manyun/dc-brain.legacy.components';
import {
  getBlockDevicesActionCreator,
  runningStatesActiveBlockChangeActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/idcWorkbenchActions';
import { getPixelPercentageWidth, getSpaceGuid } from '@manyun/dc-brain.legacy.utils/index';

import Ahu from '../components/ahu';
import CoolingSystem from '../components/cooling-system';
import CoolingSystemCoreData from '../components/cooling-system-core-data';
import PowerSystem from '../components/power-system';
import { CardLevel1 } from '../components/styled';
import BlockSelector from './block-selector';

class RunningStates extends Component {
  componentDidMount() {
    const { idc, activeBlock: selectedBlock, getDevices } = this.props;
    if (idc && selectedBlock) {
      getDevices({ idc, block: selectedBlock });
    }
  }

  componentDidUpdate({ idc: prevIdc, activeBlock: prevSelectedBlock }) {
    const { idc, activeBlock: selectedBlock, getDevices } = this.props;
    if (prevIdc !== idc || prevSelectedBlock !== selectedBlock) {
      getDevices({ idc, block: selectedBlock });
    }
  }

  _blockTabChangeHandler = activeBlock => {
    const { idc } = this.props;
    this.props.updateActiveBlock({ idc, activeBlock });
  };

  render() {
    const { activeBlock, blockGuid, showAhu } = this.props;
    return (
      <CardLevel1
        style={{ width: getPixelPercentageWidth(688) }}
        bodyStyle={{
          height: 'calc(var(--content-height) - 190px - 1rem - 39px - 1px)',
          overflowY: 'auto',
        }}
        size="small"
        bordered
        title="运行状态"
        extra={
          <BlockSelector
            activeBlock={activeBlock}
            onChange={block => {
              this._blockTabChangeHandler(block);
            }}
          />
        }
      >
        <Space style={{ width: '100%', display: 'flex' }} direction="vertical">
          {!activeBlock && (
            <GutterWrapper style={{ height: '100%' }} flex center>
              <TinyEmpty />
            </GutterWrapper>
          )}
          {activeBlock && <PowerSystem blockGuid={blockGuid} />}
          {activeBlock && !showAhu && <CoolingSystem blockGuid={blockGuid} />}
          {activeBlock && !showAhu && <CoolingSystemCoreData blockGuid={blockGuid} />}
          {activeBlock && showAhu && <Ahu blockGuid={blockGuid} />}
        </Space>
      </CardLevel1>
    );
  }
}

const mapStateToProps = ({
  idcWorkbench: { idc, blocks, blockDevicesCountMap, runningStatesActiveBlock },
  config: { configMap },
}) => {
  const blockGuid = getSpaceGuid(idc, runningStatesActiveBlock);
  const configUtil = new ConfigUtil(configMap.current, { defaultSpaceGuid: blockGuid });
  const showAhu = configUtil.getOneDeviceType(
    ConfigUtil.constants.deviceTypes.AHU_EVAPORATION_UNIT_DX,
    { useFallback: false, logLevel: 'silent' }
  );

  return {
    idc,
    blocks,
    blockDevicesCountMap,
    activeBlock: runningStatesActiveBlock,
    blockGuid,
    showAhu,
  };
};
const mapDispatchToProps = {
  updateActiveBlock: runningStatesActiveBlockChangeActionCreator,
  getDevices: getBlockDevicesActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(RunningStates);
