import React from 'react';

import { Radio } from '@manyun/base-ui.ui.radio';

import { PuePie } from '@manyun/monitoring.chart.pue-pie';
import { WueGauge } from '@manyun/monitoring.chart.wue-gauge';
import { RackUseLiquidFill } from '@manyun/resource-hub.chart.rack-use-liquid-fill';

import { CardLevel1 } from '../components/styled';

export default function EnergyEfficiency({ idc }) {
  const [viewMode, setViewMode] = React.useState('pue');

  return (
    <CardLevel1
      style={{
        height: '190px',
        flex: 1,
      }}
      size="small"
      bordered
      title="能效"
    >
      <Radio.Group
        style={{ zIndex: 1, position: 'absolute' }}
        onChange={({ target }) => {
          setViewMode(target.value);
        }}
        value={viewMode}
        size="small"
      >
        <Radio.Button value="pue">PUE</Radio.Button>
        <Radio.Button value="wue">WUE</Radio.Button>
        <Radio.Button value="grid">机柜使用率</Radio.Button>
      </Radio.Group>
      {viewMode === 'pue' && <PuePie idc={idc} />}
      {viewMode === 'wue' && <WueGauge idc={idc} />}
      {viewMode === 'grid' && <RackUseLiquidFill idc={idc} />}
    </CardLevel1>
  );
}
