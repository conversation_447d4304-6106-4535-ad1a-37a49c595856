import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import Form from '@ant-design/compatible/es/form';
import CaretRightOutlined from '@ant-design/icons/es/icons/CaretRightOutlined';
import moment from 'moment';
import styled from 'styled-components';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { Collapse } from '@manyun/base-ui.ui.collapse';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Switch } from '@manyun/base-ui.ui.switch';
import { Typography } from '@manyun/base-ui.ui.typography';

import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { McUpload as Upload } from '@manyun/dc-brain.ui.upload';

import { TinyModal } from '@manyun/dc-brain.legacy.components';
import TargetTreeTransfer from '@manyun/dc-brain.legacy.components/relationship-connector/target-tree-transfer';
import {
  getNoticesActionCreator,
  idcWorkbenchActions,
} from '@manyun/dc-brain.legacy.redux/actions/idcWorkbenchActions';
import * as noticeService from '@manyun/dc-brain.legacy.services/noticeService';
import { generateNoticeManageListUrl } from '@manyun/dc-brain.legacy.utils/urls';

import { CardLevel1 } from '../components/styled';

const { Panel } = Collapse;

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};
class Notice extends Component {
  state = {
    validEndDate: '',
    validBeginDate: '',
    fileList: [],
    rangeInfo: '',

    activeKey: '',
    announcementVisible: false,
    permissionCurrent: [],
  };

  componentDidUpdate(preProps, preState) {
    const { announcementVisible } = this.state;
    if (preState.announcementVisible !== announcementVisible) {
      this.setState({ fileList: [] });
    }
  }

  onVisible = () => {
    this.props.form.setFieldsValue({
      content: null,
      importance: null,
      rangeType: null,
      resourceIdList: null,
      title: null,
      date: [],
    });
    this.setState(({ announcementVisible }) => ({
      announcementVisible: !announcementVisible,
      resourceIdList: [],
    }));
  };

  onChangeTime = (value, dateString) => {
    this.setState({
      validEndDate: Date.parse(dateString[1]),
      validBeginDate: Date.parse(dateString[0]),
    });
  };

  submitNewNotice = (event, form) => {
    event.preventDefault();
    this.props.form.validateFields(async (err, values) => {
      if (!err) {
        const { validBeginDate, validEndDate, fileList } = this.state;
        const { content, importance, rangeType, resourceIdList, title, immediately, popUp } =
          values;
        const params = {
          content,
          importance,
          rangeType,
          resourceIdList: resourceIdList === undefined ? [] : resourceIdList,
          title,
          files: fileList.map(file => ({ ...McUploadFile.toApiObject(file), fileType: null })),

          validBeginDate,
          validEndDate,
          immediately,
          popUp,
        };
        const data = await noticeService.addNotice(params);
        const { response, error } = data;
        if (response) {
          this.props.searchNoticeList(this.props.idc);
          this.setState({ resourceIdList: [] });
          this.onVisible();
          // this.props.form.setFieldsValue({
          //   content: null,
          //   importance: null,
          //   rangeType: null,
          //   resourceIdList: null,
          //   title: null,
          //   date: [],
          // });
        }
        if (error) {
          message.error(error || '发布公告失败');
        }
      }
    });
  };

  onChangeCollapse = value => {
    this.setState({
      activeKey: value,
    });
  };

  render() {
    const { className, noticeList, permissions } = this.props;
    const { getFieldDecorator, getFieldValue } = this.props.form;
    const { activeKey, announcementVisible, fileList, rangeInfo } = this.state;

    const disabledDate = current => {
      if (rangeInfo === 'end') {
        return false;
      }
      if (rangeInfo === 'start') {
        return current && current <= moment(moment().format('YYYY/MM/DD'));
      }
    };

    const range = (start, end) => {
      const result = [];
      for (let i = start; i < end; i++) {
        result.push(i);
      }
      return result;
    };

    const disabledTime = (current, type) => {
      const hours = moment().hours();
      const minute = moment().minute();
      const second = moment().second();
      const choseHour = moment(current).hours();
      const choseMinute = moment(current).minute();
      const isToday = moment(current).isSame(moment(), 'day');
      if (isToday && type === 'end') {
        if (choseHour === hours) {
          return {
            disabledHours: () => range(0, hours),
            disabledMinutes: () => range(0, minute),
            disabledSeconds: choseMinute === minute ? () => range(0, second) : undefined,
          };
        }
        return {
          disabledHours: () => range(0, hours),
        };
      }
      return;
    };

    return (
      <CardLevel1
        style={{ width: '23%', maxWidth: 600, height: 190 }}
        bodyStyle={{ height: 150, overflowY: 'auto' }}
        className={className}
        size="small"
        bordered
        title={'公告通知'}
        extra={
          <div>
            {permissions.includes('notice:add') && (
              <Button
                key={1}
                type="link"
                onClick={this.onVisible}
                style={{ padding: 0, height: 'auto' }}
              >
                发布公告
              </Button>
            )}
            <Link to={generateNoticeManageListUrl()}>
              <span key={2} style={{ color: `var(--${prefixCls}-error-color)`, marginLeft: '8px' }}>
                更多
              </span>
            </Link>
          </div>
        }
      >
        <Collapse
          bordered={false}
          expandIcon={({ isActive }) => <CaretRightOutlined otate={isActive ? 90 : 0} />}
          onChange={this.onChangeCollapse}
          accordion
        >
          {noticeList.length &&
            noticeList.map((item, index) => {
              let type = 'default';
              let actived = false;
              if (activeKey === String(index)) {
                actived = true;
              }
              if (item.importance === 'HIGH') {
                type = 'danger';
              }
              if (item.importance === 'MID') {
                type = 'warning';
              }
              if (item.importance === 'LOW') {
                type = 'success';
              }

              return (
                <Panel
                  header={
                    <Typography.Text ellipsis type={type}>
                      【{item.title}】{!actived && item.content}
                    </Typography.Text>
                  }
                  key={index}
                >
                  <p style={{ fontSize: 12 }}>
                    {item.content}
                    <br />
                    {moment(item.validBeginDate).format('YYYY-MM-DD HH:mm:ss')} -
                    {moment(item.validEndDate).format('YYYY-MM-DD HH:mm:ss')}
                  </p>
                </Panel>
              );
            })}
          {/* <Panel header="This is panel header 1" key="1">
             <p>{text}</p>
          </Panel>
          <Panel header={<span >sdadadad</span>} key="2">
            <p>{text}</p>
          </Panel>
          <Panel header="This is panel header 3" key="3">
            <p>{text}</p>
          </Panel> */}
        </Collapse>
        <TinyModal
          open={announcementVisible}
          title="发布公告"
          onCancel={this.onVisible}
          onClose={this.onVisible}
          onOk={this.submitNewNotice}
        >
          <Form {...formItemLayout} colon={false}>
            <Form.Item label="公告标题">
              {getFieldDecorator('title', {
                rules: [
                  { required: true, message: '公告标题必填！' },
                  {
                    max: 24,
                    message: '最多输入 24 个字符！',
                  },
                ],
              })(<Input />)}
            </Form.Item>
            <Form.Item label="公告内容">
              {getFieldDecorator('content', {
                rules: [
                  { required: true, message: '公告内容必填！' },
                  {
                    max: 128,
                    message: '最多输入 128 个字符！',
                  },
                ],
              })(<Input.TextArea />)}
            </Form.Item>
            <Form.Item label="公告范围">
              {getFieldDecorator('rangeType', {
                rules: [{ required: true, message: '公告范围必选！' }],
                initialValue: 1,
              })(
                <Radio.Group name="radiogroup">
                  <Radio value="ALL">全部</Radio>
                  <Radio value="PART">部分</Radio>
                </Radio.Group>
              )}
            </Form.Item>
            {getFieldValue('rangeType') === 'PART' && (
              <Form.Item label=" ">
                {getFieldDecorator('resourceIdList', {
                  rules: [{ required: true, message: '公告范围必选！' }],
                })(
                  <TargetTreeTransfer
                    targetDatasourceService={noticeService.fetchSelectResourceList}
                    checkedKeys={[]}
                  ></TargetTreeTransfer>
                )}
              </Form.Item>
            )}
            <Form.Item label="有效期限">
              {getFieldDecorator('date', {
                validateFirst: true,
                rules: [
                  { required: true, message: '有效期限必选！' },
                  {
                    validator: (_, value, callback) => {
                      if (value[1] >= moment(moment().format('YYYY-MM-DD HH:mm:ss'))) {
                        callback();
                      } else {
                        callback('结束时间不可选择历史时间!');
                      }
                    },
                  },
                ],
              })(
                <DatePicker.RangePicker
                  showTime
                  format="YYYY-MM-DD HH:mm:ss"
                  onChange={this.onChangeTime}
                  disabledDate={disabledDate}
                  disabledTime={disabledTime}
                  onCalendarChange={(values, _, info) => this.setState({ rangeInfo: info.range })}
                />
              )}
            </Form.Item>
            <Form.Item label="重要程度">
              {getFieldDecorator('importance', {
                rules: [{ required: true, message: '重要程度必选！' }],
                initialValue: 1,
              })(
                <Radio.Group name="radiogroup">
                  <Radio value="HIGH">
                    <Typography.Text type="danger">重要</Typography.Text>
                  </Radio>
                  <Radio value="MID">
                    <Typography.Text type="warning">中等</Typography.Text>
                  </Radio>
                  <Radio value="LOW">
                    <Typography.Text type="success">普通</Typography.Text>
                  </Radio>
                </Radio.Group>
              )}
            </Form.Item>
            <Form.Item label="通知时间">
              {getFieldDecorator('immediately', {
                rules: [{ required: true, message: '通知时间必选！' }],
                initialValue: true,
              })(
                <Radio.Group>
                  <Radio value={true}>立即通知</Radio>
                  <Radio value={false}>生效通知</Radio>
                </Radio.Group>
              )}
            </Form.Item>
            <Form.Item label="弹窗提醒">
              {getFieldDecorator('popUp', {
                valuePropName: 'checked',
                initialValue: false,
              })(<Switch />)}
            </Form.Item>
            <Form.Item label="附件">
              <Upload
                accept="image/*,.pdf,.xls,.xlsx,.doc,.docx"
                showUploadList
                allowDelete
                maxFileSize={20}
                maxCount={5}
                fileList={fileList}
                onChange={({ file, fileList }) => {
                  if (
                    file.status === 'uploading' &&
                    fileList.filter(file => file.status !== 'uploading').length === 5
                  ) {
                    message.error('上传附件数量最多5个!');
                    return;
                  }
                  this.setState({ fileList });
                }}
              >
                <Button type="primary">上传</Button> <br />
                <Typography.Text type="secondary" style={{ marginTop: '8px' }}>
                  支持扩展名：image/*,.pdf,.xls,.xlsx,.doc,.docx
                </Typography.Text>
              </Upload>
            </Form.Item>
          </Form>
        </TinyModal>
      </CardLevel1>
    );
  }
}
const mapStateToProps = ({
  idcWorkbench: { announcementVisible, noticeList },
  user: { permissions },
}) => ({
  announcementVisible,
  noticeList,
  permissions,
});
const mapDispatchToProps = {
  visible: idcWorkbenchActions.announcementVisible,
  searchNoticeList: getNoticesActionCreator,
};

export const NoticeForm = connect(
  mapStateToProps,
  mapDispatchToProps
)(Form.create({ name: 'user_manage_create' })(Notice));

export const StyledNotice = styled(NoticeForm)`
  .manyun-collapse > .manyun-collapse-item > .manyun-collapse-header {
    padding: 0;
    padding-left: 12px;
    border: 0;
  }

  .manyun-collapse-content > .manyun-collapse-content-box {
    padding: 0 0 0 18px;
  }

  .manyun-collapse > .manyun-collapse-item > .manyun-collapse-header .manyun-collapse-arrow {
    left: 0;
  }

  .manyun-collapse-content .manyun-collapse-content-box p {
    margin-bottom: 0;
  }

  .manyun-collapse-borderless > .manyun-collapse-item {
    border: 0;
  }

  .manyun-collapse > .manyun-collapse-item > .manyun-collapse-header {
    background: transparent;
  }
`;

export default StyledNotice;
