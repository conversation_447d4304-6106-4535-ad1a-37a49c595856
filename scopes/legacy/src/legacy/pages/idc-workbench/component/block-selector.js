import React from 'react';
import { connect } from 'react-redux';

import { Select } from '@manyun/base-ui.ui.select';
import { Typography } from '@manyun/base-ui.ui.typography';

import { getSpaceGuid, getSpaceGuidMap } from '@manyun/dc-brain.legacy.utils';

function BlockSelector({ blockDevicesCountMap, idc, blocks, activeBlock, onChange }) {
  const _activeBlock = activeBlock ?? blocks[0];
  if (!(idc && _activeBlock)) {
    return null;
  }

  const current = getSpaceGuid(idc, _activeBlock);

  return (
    <Select
      size="small"
      bordered={false}
      value={current}
      onChange={blockGuid => {
        const guids = getSpaceGuidMap(blockGuid);
        onChange(guids.block);
      }}
    >
      {blocks.map(block => {
        const _blockGuid = getSpaceGuid(idc, block);
        const hasAlarms = blockDevicesCountMap[_blockGuid] !== undefined;

        return (
          <Select.Option key={block} value={_blockGuid}>
            <Typography.Text type={hasAlarms ? 'danger' : undefined}>{_blockGuid}</Typography.Text>
          </Select.Option>
        );
      })}
    </Select>
  );
}

const mapStateToProps = ({ idcWorkbench: { idc, blocks, blockDevicesCountMap } }) => {
  return {
    idc,
    blocks,
    blockDevicesCountMap,
  };
};

export default connect(mapStateToProps)(BlockSelector);
