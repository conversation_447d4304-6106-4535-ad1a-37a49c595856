import { Select } from '@galiojs/awesome-antd';
import get from 'lodash/get';
import uniq from 'lodash/uniq';
import React, { Component } from 'react';
import { connect } from 'react-redux';
import styled from 'styled-components';

import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { generateRoomMonitoringUrl } from '@manyun/monitoring.route.monitoring-routes';

import { GutterWrapper, TinyEmpty } from '@manyun/dc-brain.legacy.components';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import { roomsInfoActiveBlockChangeActionCreator } from '@manyun/dc-brain.legacy.redux/actions/idcWorkbenchActions';
import { redirectActionCreator } from '@manyun/dc-brain.legacy.redux/actions/routerActions';
import { getSpaceGuid, getSpaceGuidMap } from '@manyun/dc-brain.legacy.utils/index';

import { CardLevel1 } from '../components/styled';
import RoomCard from './../components/room-card';
import BlockSelector from './block-selector';

class RoomInfo extends Component {
  state = {
    /**
     * 筛选中的包间类型编码
     */
    roomType: undefined,

    /**
     * 筛选中包间 Tag 关键字
     */
    keyword: '',
  };

  componentDidMount() {
    this.props.syncCommonData({ strategy: { roomTypes: 'IF_NULL' } });
  }

  _roomTabChangeHandler = activeBlock => {
    const { idc } = this.props;
    this.props.updateActiveBlock({ idc, activeBlock });
  };

  _redirectToRoomMonitoringPage = (roomGuid, evt) => {
    const isModal = isModalButton(evt.target.parentNode);
    if (isModal) {
      return;
    }
    const searchParams = this._getRedirectQ(roomGuid);
    window.open(generateRoomMonitoringUrl(searchParams));
  };

  _setVisibleRoomType = roomType => {
    this.setState({
      roomType,
    });
  };

  _setRoomKeyword = value => {
    this.setState({
      keyword: value.target.value,
    });
  };

  _getRedirectQ = roomGuid => {
    const { idc, block, room } = getSpaceGuidMap(roomGuid);

    return {
      idc,
      block,
      room,
      isStatusNull: 'true',
    };
  };

  _redirectToAlarmList = roomGuid => {};

  render() {
    const { className, idc, rooms, blockGuid, activeBlock, roomTypes } = this.props;
    const { roomType, keyword } = this.state;
    const visibleRooms =
      roomType || keyword
        ? rooms.filter(room => {
            let visible = false;
            const sameRoomType = roomType === room.roomType;
            const includesKeyword = room.roomTag
              .toLowerCase()
              .includes(keyword.toLowerCase().trim());
            if (roomType && keyword) {
              visible = sameRoomType && includesKeyword;
            } else if (roomType && !keyword) {
              visible = sameRoomType;
            } else if (!roomType && keyword) {
              visible = includesKeyword;
            }

            return visible;
          })
        : rooms;

    return (
      <CardLevel1
        style={{ flex: 1 }}
        className={className}
        size="small"
        bordered
        title="包间信息"
        extra={
          <BlockSelector
            activeBlock={activeBlock}
            onChange={block => {
              this._roomTabChangeHandler(block);
            }}
          />
        }
      >
        <GutterWrapper mode="vertical">
          <Row>
            <Col span={12}>
              <Select
                style={{ width: 120 }}
                showSearch
                allowClear
                placeholder="包间类型"
                options={uniq(rooms.map(({ roomType }) => roomType)).map(roomType => {
                  const label = get(roomTypes, roomType, roomType);

                  return {
                    title: label,
                    label,
                    value: roomType,
                  };
                })}
                onChange={this._setVisibleRoomType}
              />
            </Col>
            <Col span={12} style={{ textAlign: 'right' }}>
              <Input.Search
                style={{
                  width: 200,
                }}
                placeholder="请输入包间编号"
                onChange={this._setRoomKeyword}
              />
            </Col>
          </Row>
          <div
            style={{
              height: 'calc(var(--content-height) - 190px - 1rem - 39px - 8px - 1rem - 32px - 8px)',
              overflowY: 'auto',
            }}
          >
            {visibleRooms.length > 0 ? (
              <div
                style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fill,minmax(207px,1fr))',
                  gridRowGap: '8px',
                  gridColumnGap: '8px',
                }}
              >
                {visibleRooms.map(room => (
                  <RoomCard
                    key={room.roomGuid}
                    idc={idc}
                    blockGuid={blockGuid}
                    roomGuid={room.roomGuid}
                    roomType={room.roomType}
                    title={`${room.roomTag} ${get(roomTypes, room.roomType, room.roomType)}`}
                    linkRoom={this._redirectToRoomMonitoringPage}
                    linkAlarmList={this._redirectToAlarmList}
                  />
                ))}
              </div>
            ) : (
              <GutterWrapper style={{ height: '100%' }} flex center>
                <TinyEmpty />
              </GutterWrapper>
            )}
          </div>
        </GutterWrapper>
      </CardLevel1>
    );
  }
}

const mapStateToProps = (
  {
    idcWorkbench: { blocks, roomsMap, roomsInfoActiveBlock, blockDevicesCountMap },
    common: { roomTypes },
  },
  { idc }
) => {
  const blockGuid = getSpaceGuid(idc, roomsInfoActiveBlock);

  return {
    blockGuid,
    blocks,
    activeBlock: roomsInfoActiveBlock,
    blockDevicesCountMap,
    rooms: roomsMap[blockGuid] || [],
    roomTypes,
  };
};
const mapDispatchToProps = {
  updateActiveBlock: roomsInfoActiveBlockChangeActionCreator,
  redirect: redirectActionCreator,
  syncCommonData: syncCommonDataActionCreator,
};

export const Building = connect(mapStateToProps, mapDispatchToProps)(RoomInfo);

export const StyledIdcWorkbench = styled(Building)`
  font-size: 12px;

  .title {
    font-size: 18px;
    display: inline-block;
  }

  .roomMargin {
    margin-right: 8px;
    margin-left: 2px
    display: inline-block;
  }

  .manyun-card-body::before {
    content: none;
  }

  .manyun-card-body::after {
    content: none;
  }
`;

export default StyledIdcWorkbench;

function isModalButton(node) {
  if (!node) {
    return false;
  }

  if (
    node?.className?.includes('idc-workbench_room_info-modal-button') ||
    node?.className?.includes('ant-select-dropdown') ||
    node?.className?.includes('ant-calendar-picker-container')
  ) {
    return true;
  }

  if (node.parentNode) {
    return isModalButton(node.parentNode);
  }

  return false;
}
