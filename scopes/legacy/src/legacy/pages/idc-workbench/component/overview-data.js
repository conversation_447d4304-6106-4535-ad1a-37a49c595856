import get from 'lodash/get';
import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import { Space } from '@manyun/base-ui.ui.space';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { Alarm } from '@manyun/monitoring.model.alarm';
import { generateSpaceOrDeviceRoutePath } from '@manyun/monitoring.route.monitoring-routes';
import { generateGetPointMonitoringData } from '@manyun/monitoring.util.get-monitoring-data';
import {
  generateChangeTicketsRoutePath,
  generateEventsRoutePath,
} from '@manyun/ticket.route.ticket-routes';

import { CLICK_RENDER_TYPE_MAP, Statistics } from '@manyun/dc-brain.legacy.components/statistics';
import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';
import { STATUS_MAP } from '@manyun/dc-brain.legacy.constants/statusColor';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import { terminateGetStatisticsIntervalActionCreator } from '@manyun/dc-brain.legacy.redux/actions/idcWorkbenchActions';
import { getPixelPercentageWidth } from '@manyun/dc-brain.legacy.utils';
import {
  generateMineTicketsLocation,
  generateTemplteGroupListUrlByIdc,
} from '@manyun/dc-brain.legacy.utils/urls';

import { AHUWorkingCountLineChartButton } from '../components/ahu/statistics/extra-counts/working-count';
import { CardLevel1 } from '../components/styled';
import { UN_RESOLVED_CHANGE_STATES, UN_RESOLVED_EVENT_STATES } from '../constants';

class OverviewData extends Component {
  componentDidMount() {
    const { virtualType } = this.props;
    if (!virtualType) {
      this.props.syncCommonDataActionCreator({ strategy: { allVirtualType: 'IF_NULL' } });
    }
  }

  componentWillUnmount() {
    this.props.cancelStatisticsDataIntervalReq();
  }

  render() {
    const {
      pendingAlarmsCount,
      pendingEventsCount,
      pendingChangesCount,
      pendingTicketsCount,
      idc,
      idcDeviceType,
      hvIncomingSwitchgear,
      generator,
      ups,
      transformer,
      chiller,
      chilledWaterPump,
      ahuVisible,
      ahuWorkingCount,
    } = this.props;
    const overView = getData({
      pendingAlarmsCount,
      pendingEventsCount,
      pendingChangesCount,
      pendingTicketsCount,
      idc,
      idcDeviceType,
      hvIncomingSwitchgear,
      generator,
      ups,
      transformer,
      chiller,
      chilledWaterPump,
      ahuVisible,
      ahuWorkingCount,
    });
    return (
      <CardLevel1
        style={{ width: getPixelPercentageWidth(688), height: '190px' }}
        bodyStyle={{ padding: '0 16px' }}
        size="small"
        bordered
        title="概览数据"
        extra={
          <Space>
            <Link
              key={1}
              to={generateSpaceOrDeviceRoutePath({ guid: idc, type: 'IDC', showTargetTree: true })}
              style={{ padding: 0, fontSize: 14 }}
            >
              机房信息
            </Link>
            <Link
              key={2}
              to={generateTemplteGroupListUrlByIdc({ idc: idc })}
              style={{ padding: 0, fontSize: 14 }}
            >
              监控阈值
            </Link>
          </Space>
        }
      >
        <Statistics
          style={{ fontSize: 12 }}
          cellStyle={{ padding: 8, fontSize: 12 }}
          textStyle={{ fontSize: 16 }}
          dataSource={overView}
        />
      </CardLevel1>
    );
  }
}

const getData = ({
  pendingAlarmsCount,
  pendingEventsCount,
  pendingChangesCount,
  pendingTicketsCount,
  idc,
  idcDeviceType,
  hvIncomingSwitchgear,
  generator,
  ups,
  transformer,
  chiller,
  chilledWaterPump,
  ahuVisible,
  ahuWorkingCount,
}) => {
  const list = [
    [
      {
        description: '市电进线柜状态',
        key: 'SD',
        text: hvIncomingSwitchgear.count > 0 ? `${hvIncomingSwitchgear.count} 台异常` : '正常',
        status: hvIncomingSwitchgear.count > 0 ? STATUS_MAP.ALARM : STATUS_MAP.NORMAL,
        clickRender: () => ({
          type: CLICK_RENDER_TYPE_MAP.LINK,
          props: {
            to: '/',
          },
        }),
      },
      {
        description: '柴发状态',
        key: 'CF',
        text: generator.count > 0 ? `${generator.count} 台异常` : '正常',
        status: generator.count > 0 ? STATUS_MAP.ALARM : STATUS_MAP.NORMAL,
        clickRender: () => ({
          type: CLICK_RENDER_TYPE_MAP.LINK,
          props: {
            to: '/',
          },
        }),
      },
      {
        description: 'UPS状态',
        key: 'UPS',
        text: ups.count > 0 ? `${ups.count} 台异常` : '正常',
        status: ups.count > 0 ? STATUS_MAP.ALARM : STATUS_MAP.NORMAL,
        clickRender: () => ({
          type: CLICK_RENDER_TYPE_MAP.LINK,
          props: {
            to: '/',
          },
        }),
      },
      {
        description: '变压器状态',
        key: 'BYQ',
        text: transformer.count > 0 ? `${transformer.count} 台异常` : '正常',
        status: transformer.count > 0 ? STATUS_MAP.ALARM : STATUS_MAP.NORMAL,
        clickRender: () => ({
          type: CLICK_RENDER_TYPE_MAP.LINK,
          props: {
            to: '/',
          },
        }),
      },
      ...(ahuVisible
        ? [
            {
              key: 'AHU',
              colSpan: 2,
              description: 'AHU 开启数量',
              text: (
                <AHUWorkingCountLineChartButton
                  spaceGuid={idc}
                  device={{ deviceGuid: idc, deviceType: idcDeviceType }}
                  // 注意：因为 PUE 卡片已经订阅了当前机房的实时数据，所以这里不再订阅
                  workingCount={ahuWorkingCount}
                />
              ),
            },
          ]
        : [
            {
              key: 'LJ',
              description: '冷机状态',
              text: chiller.count > 0 ? `${chiller.count} 台异常` : '正常',
              status: chiller.count > 0 ? STATUS_MAP.ALARM : STATUS_MAP.NORMAL,
              clickRender: () => ({
                type: CLICK_RENDER_TYPE_MAP.LINK,
                props: {
                  to: '/',
                },
              }),
            },
            {
              description: '冷冻泵状态',
              key: 'LDB',
              text: chilledWaterPump.count > 0 ? `${chilledWaterPump.count} 台异常` : '正常',
              status: chilledWaterPump.count > 0 ? STATUS_MAP.ALARM : STATUS_MAP.NORMAL,
              clickRender: () => ({
                type: CLICK_RENDER_TYPE_MAP.LINK,
                props: {
                  to: '/',
                },
              }),
            },
          ]),
    ],
    [
      {
        description: '待处理告警',
        key: 'pendingAlarm',
        text: pendingAlarmsCount,
        status: Number(pendingAlarmsCount) > 0 ? STATUS_MAP.REFERENCE : STATUS_MAP.NODATA,
        clickRender: () => ({
          type: CLICK_RENDER_TYPE_MAP.LINK,
          props: {
            to: '/',
          },
        }),
      },
      {
        description: '待处理事件',
        key: 'pendingEvents',
        text: pendingEventsCount,
        status: Number(pendingEventsCount) > 0 ? STATUS_MAP.REFERENCE : STATUS_MAP.NODATA,
        clickRender: () => ({
          type: CLICK_RENDER_TYPE_MAP.LINK,
          props: {
            to: generateEventsRoutePath({
              idcs: idc,
              eventStatus: UN_RESOLVED_EVENT_STATES.join(','),
            }),
          },
        }),
      },
      {
        description: '待处理变更',
        key: 'pendingChanges',
        text: pendingChangesCount,
        status: Number(pendingChangesCount) > 0 ? STATUS_MAP.REFERENCE : STATUS_MAP.NODATA,
        clickRender: () => ({
          type: CLICK_RENDER_TYPE_MAP.LINK,
          props: {
            to: generateChangeTicketsRoutePath({
              idc,
              status: UN_RESOLVED_CHANGE_STATES.join(','),
            }),
          },
        }),
      },
      {
        description: '待处理工单',
        key: 'tickets',
        text: pendingTicketsCount,
        status: Number(pendingTicketsCount) > 0 ? STATUS_MAP.REFERENCE : STATUS_MAP.NODATA,
        clickRender: () => ({
          type: CLICK_RENDER_TYPE_MAP.LINK,
          props: {
            to: generateMineTicketsLocation(),
          },
        }),
      },
      {
        description: '风险数量',
        key: 'risk',
        text: BLANK_PLACEHOLDER,
      },
      {
        description: '演练数量',
        key: 'drill',
        text: BLANK_PLACEHOLDER,
      },
    ],
  ];

  return list;
};

const mapStateToProps = (
  {
    'monitoring.subscriptions': { devicesRealtimeData, devicesAlarmsData },
    idcWorkbench: {
      pendingAlarmsCount,
      pendingEventsCount,
      pendingChangesCount,
      pendingTicketsCount,
      pendingMattersList: { task },
      overviewDataAlarmNum,
      idcDevicesCount,
    },
    common: { virtualType },
    config: { configMap },
  },
  { idc }
) => {
  const configUtil = new ConfigUtil(configMap.current, { defaultSpaceGuid: idc });
  const idcDeviceType = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_IDC);
  const ahuVisible = configUtil.checkAHUVisibility(idc);
  let ahuWorkingCount;
  if (ahuVisible) {
    const getData = generateGetPointMonitoringData({
      realtimeData: devicesRealtimeData,
      alarmsData: devicesAlarmsData,
      configUtil,
    });
    const ahuWorkingCountPointCode = configUtil.getPointCode(
      idcDeviceType,
      ConfigUtil.constants.pointCodes.AHU_EVAPORATION_UNIT_WORKING_COUNT
    );
    ahuWorkingCount = getData(
      { deviceGuid: idc, deviceType: idcDeviceType },
      { hardCodedPointCode: ahuWorkingCountPointCode }
    );
  }
  const hvIncomingSwitchgearType = configUtil.getOneDeviceType(
    ConfigUtil.constants.deviceTypes.HV_INCOMING_SWITCHGEAR
  );
  const transformerType = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.TRANSFORMER);
  const chilledWaterPumpType = configUtil.getOneDeviceType(
    ConfigUtil.constants.deviceTypes.CHILLED_WATER_PUMP
  );

  const generatorTypes =
    configUtil.getAllDeviceTypes(ConfigUtil.constants.deviceTypes.GENERATOR) || [];
  const upsTypes = configUtil.getAllDeviceTypes(ConfigUtil.constants.deviceTypes.UPS) || [];
  const chillerTypes = configUtil.getAllDeviceTypes(ConfigUtil.constants.deviceTypes.CHILLER) || [];

  const sumDeviceTypeAlarmsCount = deviceTypes =>
    deviceTypes.reduce((count, deviceType) => count + get(idcDevicesCount, deviceType, 0), 0);

  return {
    idcDeviceType,
    ahuVisible,
    ahuWorkingCount,
    pendingAlarmsCount,
    pendingEventsCount,
    pendingChangesCount,
    pendingTicketsCount,
    overviewDataAlarmNum,
    virtualType,
    hvIncomingSwitchgear: {
      count: get(idcDevicesCount, hvIncomingSwitchgearType, 0),
      deviceTypes: [hvIncomingSwitchgearType],
    },
    generator: {
      count: sumDeviceTypeAlarmsCount(generatorTypes),
      deviceTypes: generatorTypes,
    },
    ups: {
      count: sumDeviceTypeAlarmsCount(upsTypes),
      deviceTypes: upsTypes,
    },
    transformer: {
      count: get(idcDevicesCount, transformerType, 0),
      deviceTypes: [transformerType],
    },
    chiller: {
      count: sumDeviceTypeAlarmsCount(chillerTypes),
      deviceTypes: chillerTypes,
    },
    chilledWaterPump: {
      count: get(idcDevicesCount, chilledWaterPumpType, 0),
      deviceTypes: [chilledWaterPumpType],
    },
  };
};
const mapDispatchToProps = {
  syncCommonDataActionCreator: syncCommonDataActionCreator,
  cancelStatisticsDataIntervalReq: terminateGetStatisticsIntervalActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(OverviewData);
