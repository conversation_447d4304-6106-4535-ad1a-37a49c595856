import React from 'react';
import { connect } from 'react-redux';

import sortBy from 'lodash/sortBy';
import moment from 'moment';

import { Radio } from '@manyun/base-ui.ui.radio';
import { Typography } from '@manyun/base-ui.ui.typography';

import { UserLink } from '@manyun/dc-brain.legacy.components';
import { getAttStaffSchedulesActionCreator } from '@manyun/dc-brain.legacy.redux/actions/idcWorkbenchActions';

import { CardLevel1, StyledTable } from '../components/styled';
import BlockSelector from './block-selector';

const columns = ({ viewMode }) => {
  if (viewMode === 'today') {
    return [
      {
        title: '姓名',
        dataIndex: 'staffName',
        fixed: 'left',
        render: (_, record) => <UserLink userId={record.staffId} userName={record.staffName} />,
      },
      {
        title: '岗位',
        dataIndex: 'position',
      },
      {
        title: '班次',
        dataIndex: 'dutyName',
      },
      {
        title: '状态',
        dataIndex: 'inDuty',
        render: txt => <StatusText status={txt} />,
      },
    ];
  }
  return [
    {
      title: '姓名',
      dataIndex: 'staffName',
      fixed: 'left',
      render: (_, record) => <UserLink userId={record.staffId} userName={record.staffName} />,
    },
    {
      title: '岗位',
      dataIndex: 'position',
    },
    {
      title: '班次',
      dataIndex: 'dutyName',
    },
    {
      title: '上班时间',
      dataIndex: 'timeInterval',
    },
  ];
};

function Scheduling({ userShiftsGroups, idc, defaultActiveBlock, getAttStaffSchedules }) {
  const [block, setBlock] = React.useState(defaultActiveBlock);
  const [viewMode, setViewMode] = React.useState('today');

  React.useEffect(() => {
    setBlock(defaultActiveBlock);
  }, [defaultActiveBlock]);

  const blockGuid = `${idc}.${block}`;
  const dataSource = React.useMemo(() => {
    const userShiftsKeys = userShiftsGroups[viewMode][blockGuid];
    if (!Array.isArray(userShiftsKeys)) {
      return [];
    }
    const userShifts = userShiftsKeys.map(key => ({
      key,
      ...userShiftsGroups[viewMode].entities[key],
    }));

    if (viewMode === 'today') {
      return sortBy(userShifts, userShift => userShift.inDuty === '缺岗');
    }

    return userShifts;
  }, [userShiftsGroups, viewMode, blockGuid]);

  const getSchedulesWidthBlock = ({ type }) => {
    if (type === 'today') {
      getAttStaffSchedules({ idc, type: 'today' });
      return;
    }
    const tomorrow = moment().valueOf() + 1 * 24 * 60 * 60 * 1000;
    const params = {
      idcTag: idc,
      scheduleDate: moment(tomorrow).startOf('day').valueOf(),
      needOvertime: true,
    };
    getAttStaffSchedules({ idc, type: 'tomorrow', params });
  };

  return (
    <CardLevel1
      bodyStyle={{
        height: 'calc((var(--content-height) - 1rem)/2 - 31px)',
        overflowY: 'auto',
      }}
      size="small"
      bordered
      title="排班信息"
      extra={
        <BlockSelector
          activeBlock={block}
          onChange={block => {
            setBlock(block);
          }}
        />
      }
    >
      <StyledTable
        size="small"
        scroll={{ x: 'max-content' }}
        columns={columns({ viewMode })}
        dataSource={dataSource}
        pagination={false}
        actions={[
          <Radio.Group
            key="view-mode"
            value={viewMode}
            size="small"
            onChange={({ target }) => {
              setViewMode(target.value);
              getSchedulesWidthBlock({ type: target.value });
            }}
          >
            <Radio.Button value="today">今日</Radio.Button>
            <Radio.Button value="tomorrow">明日</Radio.Button>
          </Radio.Group>,
        ]}
      />
    </CardLevel1>
  );
}

const mapStateToProps = ({ idcWorkbench: { idc, blocks, userShiftsGroups } }) => ({
  idc,
  defaultActiveBlock: blocks[0],
  userShiftsGroups,
});
const mapDispatchToProps = {
  getAttStaffSchedules: getAttStaffSchedulesActionCreator,
};
export default connect(mapStateToProps, mapDispatchToProps)(Scheduling);

const StatusText = ({ status }) => {
  return (
    // Todo 这里接口的返回的就是中文，因此就按照中文判断了
    // 接口链接：https://manyun.yuque.com/ewe5b3/lgi8mf/bgm5wy
    <Typography.Text type={['缺岗', '休班'].includes(status) ? 'secondary' : 'success'}>
      {status}
    </Typography.Text>
  );
};
