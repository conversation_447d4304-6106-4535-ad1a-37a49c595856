import React from 'react';
import { connect, useSelector } from 'react-redux';
import { useHistory, useParams } from 'react-router-dom';

import styled from 'styled-components';

import { Result } from '@manyun/base-ui.ui.result';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { generateUserProfileRoutePath } from '@manyun/auth-hub.route.auth-routes';
import { selectMe } from '@manyun/auth-hub.state.user';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { EssentialTodos } from '@manyun/dc-brain.ui.essential-todos';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import {
  MONITORING_IDC_ROUTE_AUTH_CODE,
  MONITORING_IDC_ROUTE_PATH,
} from '@manyun/monitoring.route.monitoring-routes';

import { <PERSON><PERSON>Wrapper } from '@manyun/dc-brain.legacy.components';
import {
  initializeActionCreator,
  terminateGetBlocksAlarmsIntervalActionCreator,
  terminateGetRoomRealtimeDataIntervalActionCreator,
  terminateGetRunningStatesRealtimeDataIntervalActionCreator,
  terminateGetStatisticsIntervalActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/idcWorkbenchActions';
import { getPixelPercentageWidth } from '@manyun/dc-brain.legacy.utils';

import EnergyEfficiency from './component/energy-efficiency';
import Notice from './component/notice';
import OverviewData from './component/overview-data';
import RoomInfo from './component/room-info';
import RunningState from './component/running-state';
import Scheduling from './component/scheduling';

function IdcWorkbench(props) {
  const [authorized] = useAuthorized({ checkByCode: MONITORING_IDC_ROUTE_AUTH_CODE });

  const { idc } = useParams();
  React.useEffect(() => {
    if (authorized) {
      props.initialize({ idc });
    }

    return () => {
      if (authorized) {
        props.terminateGetStatisticsInterval();
        props.terminateGetRunningStatesRealtimeDataInterval();
        props.terminateGetRoomRealtimeDataInterval();
        props.terminateGetBlocksAlarmsInterval();
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [authorized, idc]);

  const config = useSelector(selectCurrentConfig);
  const configUtil = new ConfigUtil(config);
  const homeUrl = configUtil.getHomeUrl();
  const isCurrentHomeUrl = homeUrl.startsWith(MONITORING_IDC_ROUTE_PATH.replace('/:idc', ''));
  const history = useHistory();
  const { userId } = useSelector(selectMe, (left, right) => left.userId === right.userId);
  const userProfileRoutePath = generateUserProfileRoutePath({ id: userId });
  React.useEffect(() => {
    // See http://chandao.manyun-local.com/zentao/task-view-688.html
    if (!authorized && isCurrentHomeUrl) {
      history.push(userProfileRoutePath);
    }
  }, [authorized, isCurrentHomeUrl, history, userProfileRoutePath]);

  if (!authorized) {
    return <Result status="403" title="403" subTitle="抱歉，你无权查看机房工作台！" />;
  }

  return (
    <GutterWrapper className={props.className} flex mode="vertical">
      <GutterWrapper style={{ flex: 1 }} mode="vertical">
        <GutterWrapper flex>
          <OverviewData idc={idc} />
          <EnergyEfficiency idc={idc} />
          <Notice idc={idc} />
        </GutterWrapper>
        <GutterWrapper flex>
          <RunningState />
          <RoomInfo idc={idc} />
        </GutterWrapper>
      </GutterWrapper>
      <GutterWrapper
        mode="vertical"
        style={{ width: getPixelPercentageWidth(300), marginTop: 0, marginLeft: '1rem' }}
      >
        <StyledEssentialTodos idc={idc} />
        <Scheduling />
      </GutterWrapper>
    </GutterWrapper>
  );
}

const mapDispatchToProps = {
  initialize: initializeActionCreator,
  terminateGetStatisticsInterval: terminateGetStatisticsIntervalActionCreator,
  terminateGetRunningStatesRealtimeDataInterval:
    terminateGetRunningStatesRealtimeDataIntervalActionCreator,
  terminateGetRoomRealtimeDataInterval: terminateGetRoomRealtimeDataIntervalActionCreator,
  terminateGetBlocksAlarmsInterval: terminateGetBlocksAlarmsIntervalActionCreator,
};

export default connect(null, mapDispatchToProps)(IdcWorkbench);

const StyledEssentialTodos = styled(EssentialTodos)`
  height: calc((var(--content-height) - 1rem) / 2);
  .manyun-card-head {
    background: transparent;
  }
  .manyun-card-body {
    background: transparent;
  }
  .manyun-tabs-tab {
    padding-top: 0;
    padding-bottom: 10px;
  }
  .manyun-timeline-item {
    padding-bottom: 0;
  }
  .manyun-tabs-content-holder {
    height: calc(((var(--content-height) - 1rem) / 2) - 184px);
    overflow-y: auto;
    padding-top: 5px;
  }
`;
