import { EllipsisOutlined } from '@ant-design/icons';
import { ApiSelect, FiltersForm, Form, Select } from '@galiojs/awesome-antd';
import moment from 'moment';
import React, { Component } from 'react';
import { connect, useDispatch } from 'react-redux';
import { Link } from 'react-router-dom';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { UserLink } from '@manyun/auth-hub.ui.user';
import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Dropdown } from '@manyun/base-ui.ui.dropdown';
import { Menu } from '@manyun/base-ui.ui.menu';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { syncCommonDataAction } from '@manyun/dc-brain.state.common';
import { EditColumns } from '@manyun/dc-brain.ui.edit-columns';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { getLocationSearchMap } from '@manyun/dc-brain.util.get-location-search-map';
import { TopologyTypesMapper } from '@manyun/monitoring.page.topology-graphix';
import { generateTopologyGraphixRoutePath } from '@manyun/monitoring.route.monitoring-routes';
import {
  BASIC_RESOURCES_BLOCK_CREATE_ROUTE_PATH,
  generateResourceBlockDetailPath,
  generateResourceBlockMutatorPath,
} from '@manyun/resource-hub.route.resource-routes';
import { fetchSpecs } from '@manyun/resource-hub.service.fetch-specs';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import { MetaTypeText } from '@manyun/resource-hub.ui.meta-type-text';
import { SpecSearchSelect } from '@manyun/resource-hub.ui.spec-search-select';

import { Ellipsis, GutterWrapper, TinyCard, TinyTable } from '@manyun/dc-brain.legacy.components';
import {
  basicResourcesBuildingActions,
  deleteBlockActionCreator,
  fetchBasicResourceBuildingList,
  fetchMetaSpaceList,
} from '@manyun/dc-brain.legacy.redux/actions/basicResourcesBuildingActions';
import * as basicResourcesBuildingService from '@manyun/dc-brain.legacy.services/basicResourcesBuildingService';
import * as infrastructureService from '@manyun/dc-brain.legacy.services/infrastructureService';
import { getObjectOwnProps } from '@manyun/dc-brain.legacy.utils';

const { Option } = Select;

const Actions = ({ blockJSON, getParams, getDeleteHandler }) => {
  const [, { checkCode }] = useAuthorized();
  const allowFuelSystemTopologyMutation = checkCode('page_mutate-infra-topology--FUEL_SYSTEM');

  return (
    <Space size={0}>
      <Link to={generateResourceBlockMutatorPath({ id: blockJSON.id, blockGuid: blockJSON.guid })}>
        编辑
      </Link>
      <Divider type="vertical" />
      <Link
        to={generateTopologyGraphixRoutePath({
          idc: blockJSON.idcTag,
          block: blockJSON.tag,
          topologyType: TopologyTypesMapper.ELECTRIC_POWER,
          mode: 'new',
        })}
      >
        电力拓扑
      </Link>
      <Divider type="vertical" />
      <Dropdown
        overlay={
          <Menu>
            <Menu.Item key="hvac-system-topology">
              <Link
                to={generateTopologyGraphixRoutePath({
                  idc: blockJSON.idcTag,
                  block: blockJSON.tag,
                  topologyType: TopologyTypesMapper.HVAC,
                  mode: 'new',
                })}
              >
                暖通拓扑
              </Link>
            </Menu.Item>
            {allowFuelSystemTopologyMutation && (
              <Menu.Item key="fuel-system-topology">
                <Link
                  to={generateTopologyGraphixRoutePath({
                    idc: blockJSON.idcTag,
                    block: blockJSON.tag,
                    topologyType: TopologyTypesMapper.FUEL_SYSTEM,
                    mode: 'new',
                  })}
                >
                  燃油系统图
                </Link>
              </Menu.Item>
            )}
            <Menu.Item key="delete">
              <DeleteConfirm targetName={blockJSON.guid} onOk={getDeleteHandler(blockJSON.guid)}>
                <span role="button">删除</span>
              </DeleteConfirm>
            </Menu.Item>
          </Menu>
        }
      >
        <EllipsisOutlined style={{ color: 'var(--manyun-primary-color)' }} />
      </Dropdown>
    </Space>
  );
};

const columns = ctx => [
  {
    title: '楼栋编号',
    dataIndex: 'tag',
    show: true,
    render: (text, record) => (
      <Link to={generateResourceBlockDetailPath({ id: record.id, blockGuid: record.guid })}>
        {text}
      </Link>
    ),
  },
  {
    title: '楼栋名称',
    dataIndex: 'name',
    show: true,
    width: 432,
    render: name => (
      <Typography.Text style={{ maxWidth: 432 }} ellipsis={{ tooltip: name }}>
        {name}
      </Typography.Text>
    ),
  },
  {
    title: '机房编号',
    dataIndex: 'idcTag',
    show: true,
  },
  {
    title: '机房名称',
    dataIndex: 'idcName',
    show: true,
    render: idcName => {
      return (
        <Ellipsis lines={1} tooltip>
          {idcName}
        </Ellipsis>
      );
    },
  },
  {
    title: '所属周期',
    dataIndex: 'period',
    show: true,
  },
  {
    title: '类别',
    show: true,
    dataIndex: ['blockType', 'name'],
  },
  {
    title: '建设日期',
    show: true,
    dataIndex: 'constructTime',
  },
  {
    title: '投产日期',
    show: true,
    dataIndex: 'operationTime',
  },
  {
    title: '状态',
    show: true,
    dataIndex: ['operationStatus', 'name'],
  },
  {
    title: '责任人',
    show: true,
    dataIndex: 'principalName',
    render: (text, { principalId }) => <UserLink id={principalId} name={text} />,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    fixed: 'right',
    width: 180,
    exportable: false,
    render: (_, record) => (
      <Actions
        blockJSON={record}
        getParams={ctx.getParams}
        getDeleteHandler={ctx._getDeleteHandler}
      />
    ),
  },
];

class BasicResourcesBuilding extends Component {
  state = {
    visible: false,
    provinceList: [],
    cityList: [],
    loading: false,
    selectedRowKeys: [],
    selectedRows: [],
    specColumns: [],
    tableColumns: [],
  };

  componentDidMount() {
    this.props.syncCommonData({ strategy: { space: 'FORCED' } });
    const { search } = this.props.location;
    const { idc = '', block = '' } = getLocationSearchMap(search, ['idc', 'block']);
    const p = this.getParams();
    const params = {
      pageNum: 1,
      pageSize: 10,
      ...p,
    };
    if (idc) {
      params.idcTag = idc;
      params.tag = block;
      this.props.updateSearchValues({ IdcBlock: { name: 'IdcBlock', value: [idc, block] } });
    }
    this.props.fetchBasicResourceBuildingList(params);
    this.getSpecs();
  }

  onChangePage = (pageNum, pageSize) => {
    const params = this.getParams();
    this.props.fetchBasicResourceBuildingList({ pageNum, pageSize, ...params });
  };

  getOptions = list => {
    const element = list.map(item => (
      <Option key={item.metaCode} value={item.metaName}>
        {item.metaName}
      </Option>
    ));
    return element;
  };

  processingAreaData = (list, value) => {
    let returnList = [];
    list.forEach(item => {
      if (item.metaCode === value && Array.isArray(item.children) && item.children.length) {
        returnList = item.children;
      }
    });
    return returnList;
  };

  handleReset = () => {
    this.props.fetchBasicResourceBuildingList({
      pageNum: 1,
      pageSize: 10,
    });
  };

  handleSearch = fieldsValue => {
    const params = this.getParams(fieldsValue);
    this.props.fetchBasicResourceBuildingList({
      pageNum: 1,
      pageSize: 10,
      ...params,
    });
  };

  getParams = () => {
    const fieldsValue = this.props.fields;
    const params = Object.keys(fieldsValue).reduce((map, fieldName) => {
      const value = fieldsValue[fieldName]?.value;
      if ([null, undefined, ''].includes(value) || (Array.isArray(value) && value.length === 0)) {
        return map;
      } else if (fieldName === 'constructTime') {
        map.constructTimeEnd = value[1]?.endOf('day').valueOf();
        map.constructTimeStart = value[0]?.startOf('day').valueOf();
      } else if (fieldName === 'operationTime') {
        map.operationTimeEnd = value[1]?.endOf('day').valueOf();
        map.operationTimeStart = value[0]?.startOf('day').valueOf();
      } else if (fieldName === 'IdcBlock') {
        map.idcTag = value.split('.')[0];
        map.tag = value.split('.')[1];
      } else if (fieldName === 'specInfo') {
        // 属性值额外处理，包含时间选择，普通输入框，楼栋选择等
        if (moment.isMoment(value)) {
          map.specValueList = [moment(value).format('HH:mm')];
        } else {
          map.specValueList = Array.isArray(value)
            ? value.some(item => item instanceof Array)
              ? value.flat() // 兼容二维数组
              : value
            : [value];
        }
      } else {
        map[fieldName] = value;
      }
      return map;
    }, {});

    const excelHeadList = this.state.tableColumns
      .filter(item => item.show && item.exportable !== false)
      .map(item => {
        if (item.dataIndex) {
          if (Array.isArray(item.dataIndex)) {
            return {
              [item.dataIndex[0]]: item.title,
            };
          }
          return {
            [item.dataIndex]: item.title,
          };
        }
        return undefined;
      })
      .filter(Boolean);
    return { ...params, excelHeadList };
  };

  handleCancel = () => {
    this.setState({ visible: false });
  };

  onSelectChange = (selectedRowKeys, selectedRows) => {
    this.setState({
      selectedRowKeys,
      selectedRows,
    });
  };

  _getDeleteHandler =
    blockGuid =>
    async ({ reason }) => {
      if (!blockGuid) {
        console.warn(`block guid expected!`);
        return;
      }

      const { error } = await infrastructureService.deleteBlock({
        guid: blockGuid,
        operatorNotes: reason,
      });

      if (error) {
        message.error(error);
        return false;
      }
      message.success(`${blockGuid} 已成功删除！`);

      const {
        buildingList: { pageNum, pageSize },
        fetchBasicResourceBuildingList,
      } = this.props;
      const searchValues = {
        ...this.getParams(),
        pageNum,
        pageSize,
      };
      fetchBasicResourceBuildingList(searchValues);

      return true;
    };

  getSpecs = async () => {
    if (!this.props.blockDeviceType) {
      return;
    }
    const { data, error } = await fetchSpecs({ deviceType: this.props.blockDeviceType });
    if (error) {
      message.error(error.message);
      return;
    }

    const specs = data.data.map(item => ({
      code: item.specCode,
      label: item.specName,
    }));
    this.setState({
      specColumns: specs.map(item => {
        return {
          title: item.label,
          width: 120,
          dataIndex: item.code,
          show: false,
          render: (value, record) => {
            return <div>{value}</div>;
          },
        };
      }),
    });
  };

  render() {
    const { buildingList, className, fields, updateSearchValues, form } = this.props;
    const { selectedRowKeys, selectedRows, tableColumns, specColumns } = this.state;
    return (
      <GutterWrapper mode="vertical" className={className}>
        <TinyCard>
          <FiltersForm
            form={form}
            items={[
              {
                label: '位置',
                name: 'IdcBlock',
                control: <LocationTreeSelect authorizedOnly allowClear />,
              },
              {
                label: '所属周期',
                name: 'period',
                control: (
                  <Select allowClear>
                    <Option value="一期">一期</Option>
                    <Option value="二期">二期</Option>
                    <Option value="三期">三期</Option>
                    <Option value="四期">四期</Option>
                  </Select>
                ),
              },
              {
                label: '状态',
                name: 'operationStatus',
                control: (
                  <ApiSelect
                    showSearch
                    fieldNames={{ label: 'label', value: 'value' }}
                    dataService={async () => {
                      const { response } =
                        await basicResourcesBuildingService.fetchGetOperationStatus();
                      if (response) {
                        return Promise.resolve(getObjectOwnProps(response));
                      } else {
                        return Promise.resolve([]);
                      }
                    }}
                    allowClear
                    trigger="onDidMount"
                  />
                ),
              },
              {
                span: 2,
                label: '投产日期',
                name: 'operationTime',
                control: <DatePicker.RangePicker />,
              },
              {
                span: 2,
                label: '建设日期',
                name: 'constructTime',
                control: <DatePicker.RangePicker />,
              },
              {
                span: 2,
                label: '属性',
                name: 'spec',
                control: <SpecSearchSelect searchType="block" form={form} />,
              },
            ]}
            fields={Object.keys(fields).map(name => {
              const field = fields[name];
              return {
                ...field,
                name: name.split('.'),
              };
            })}
            onFieldsChange={changedFields => {
              const fields = changedFields.reduce((mapper, field) => {
                const name = field.name.join('.');
                mapper[name] = {
                  ...field,
                  name,
                };
                return mapper;
              }, {});
              if (fields?.specId) {
                updateSearchValues({
                  ...fields,
                  specInfo: undefined,
                });
              } else {
                updateSearchValues(fields);
              }
            }}
            onSearch={this.handleSearch}
            onReset={() => {
              this.props.dispatch(basicResourcesBuildingActions.resetSearchValues());
              this.handleReset();
            }}
          />
        </TinyCard>
        <TinyCard>
          <TinyTable
            rowKey="id"
            columns={tableColumns?.length ? tableColumns : columns(this)}
            dataSource={buildingList.list.map(item => ({
              ...item,
              ...Object.fromEntries(
                item?.specDataList?.map(item => ({
                  ...[item.specCode, (item.specValue ?? '--') + (item.specUnit ?? '')],
                })) ?? []
              ),
            }))}
            loading={buildingList.loading}
            showExport={{
              filename: '楼栋数据',
              useServiceOnly: true,
            }}
            scroll={{ x: 'max-content' }}
            exportServices={() =>
              basicResourcesBuildingService.fetchExportBuilding({
                ...this.getParams(),
                ids: selectedRowKeys,
              })
            }
            actions={
              <Button
                type="primary"
                onClick={() => {
                  this.props.history.push(BASIC_RESOURCES_BLOCK_CREATE_ROUTE_PATH);
                }}
              >
                新建
              </Button>
            }
            extra={
              specColumns.length ? (
                <EditColumns
                  uniqKey="RESOURCE_HUB_PAGE_BLOCK_LIST2"
                  allowSetAsFixed={false}
                  defaultValue={[...columns(this), ...specColumns]}
                  listsHeight={300}
                  onChange={value => {
                    this.setState({
                      tableColumns: value,
                    });
                  }}
                />
              ) : null
            }
            pagination={{
              total: buildingList.total,
              current: buildingList.pageNum,
              onChange: this.onChangePage,
              pageSize: buildingList.pageSize,
              showSizeChanger: true,
            }}
            rowSelection={{
              selectedRowKeys,
              selectedRows,
              onChange: this.onSelectChange,
            }}
          />
        </TinyCard>
      </GutterWrapper>
    );
  }
}

function BasicResourcesBuildingList(props) {
  const [form] = Form.useForm();
  const dispatch = useDispatch();

  return <BasicResourcesBuilding form={form} dispatch={dispatch} {...props} />;
}

const mapStateToProps = ({
  basicResourcesBuilding: { buildingList, metaSpaceList, searchValues },
  config: { configMap },
}) => {
  const configUtil = new ConfigUtil(configMap.current);
  const blockDeviceType = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_BLOCK);

  return {
    buildingList,
    metaSpaceList,
    fields: searchValues,
    blockDeviceType,
  };
};
const mapDispatchToProps = {
  fetchBasicResourceBuildingList,
  fetchMetaSpaceList,
  onCreate: basicResourcesBuildingActions.onCreate,
  updateSearchValues: basicResourcesBuildingActions.updateSearchValues,
  deleteBlock: deleteBlockActionCreator,
  syncCommonData: syncCommonDataAction,
};

function isValidJSON(str) {
  try {
    const _v = JSON.parse(str);
    return _v;
  } catch (e) {
    return undefined;
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(BasicResourcesBuildingList);
