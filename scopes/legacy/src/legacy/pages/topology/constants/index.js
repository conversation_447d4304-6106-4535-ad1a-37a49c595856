import { TopologyTypesMapper } from '@manyun/monitoring.page.topology-graphix';

/**
 * 线缆状态颜色映射
 *
 * 注意： 这里不能使用 CSS Variables
 */
export const WIRE_FILL_COLOR_MAP = {
  DEFAULT: 'rgba(0, 0, 0, 0.45)',
  ELECTRICAL: '#ff4d4f',
  NOT_ELECTRICAL: '#52c41a',
};

export const TOPOLOGY_TYPE_TEXT_MAP = {
  [TopologyTypesMapper.ELECTRIC_POWER]: '电力拓扑',
  [TopologyTypesMapper.HVAC]: '暖通拓扑',
  [TopologyTypesMapper.FUEL_SYSTEM]: '燃油系统图',
  LIQUID_ELECTRIC: '液冷电力拓扑',
};

export const LINE_TYPE_KEY_MAP = {
  WIRE: 'wire',
  WATER_PIPE: 'water_pipe',
};

export const TOPLOGY_TYPE_ABSTRACT_LINE_TYPE_MAP = {
  [TopologyTypesMapper.ELECTRIC_POWER]: LINE_TYPE_KEY_MAP.WIRE,
  [TopologyTypesMapper.HVAC]: LINE_TYPE_KEY_MAP.WATER_PIPE,
};

// 电力拓扑图细分后的边界
export const TOPOLOGY_BOUNDARY_MAP = {
  // 柴发
  GENERATOR: 'GENERATOR',
  // 高压
  HV: 'HV',
  // 低压
  LV: 'LV',
};

export const TOPOLOGY_BOUNDARY_TEXT_MAP = {
  [TOPOLOGY_BOUNDARY_MAP.GENERATOR]: '柴发视图',
  [TOPOLOGY_BOUNDARY_MAP.HV]: '高压视图',
  [TOPOLOGY_BOUNDARY_MAP.LV]: '低压视图',
};

export const TOPOLOGY_JSON_NODE_TYPE_MAP = {
  NORMAL: 'NORMAL', // 真实节点、虚拟节点
  RELATE: 'RELATE', // 联络节点：高压联络柜，与高压联络柜连接的高压隔离柜，低压联络柜
};
