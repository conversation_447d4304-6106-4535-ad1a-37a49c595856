import Form from '@ant-design/compatible/es/form';
import React, { Component } from 'react';
import { connect } from 'react-redux';
import { useLocation } from 'react-router-dom';

import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';
import { RedirectToEcc } from '@manyun/monitoring.ui.redirect-to-ecc';

import { GutterWrapper, TinyCard } from '@manyun/dc-brain.legacy.components';
import {
  createVersionBtn,
  deleteVersionBtn,
  deployListBtn,
  deployVersionBtn,
  fetchVersionManagePage,
  versionListBtn,
  versionManageActions,
} from '@manyun/dc-brain.legacy.redux/actions/versionManageActions';

import BatchDeployModal from '../components/batch-deploy';
import CreateVersionModal from '../components/create-version';
import DeployListModal from '../components/deploy-list';
import DeployVersionModal from '../components/deploy-version';
import TabList from '../components/tab-list';
import VersionListModal from '../components/version-list';

class VersionManageDetail extends Component {
  state = {
    loading: true,
    versionData: [], //
    batchDeployShow: false,
    createVersionData: {},
    deployListData: [],
    batchDeployData: [],
  };

  componentDidMount() {
    const { type, tab } = this.props;
    if (tab) {
      this.props.changeType(tab);
      this.props.fetchVersionManagePage({
        type: tab,
      });
    } else {
      this.props.fetchVersionManagePage({
        type,
      });
    }
  }

  componentWillUnmount() {
    this.props.changeType('DEVICE');
  }

  _createVersionChangeVisible = () => {
    this.props.changeModal('createVersionShow');
  };

  _versionListChange = range => {
    this.props.changeModal('versionListShow');
    this.props.versionListBtn({
      type: this.props.type,
      range,
    });
  };

  _deployListChange = range => {
    this.props.changeModal('deployListShow');
    this.props.deployListBtn({
      type: this.props.type,
      range: range,
    });
  };

  _deployVersionChange = range => {
    this.setState({
      currRange: range,
    });
    this.props.changeModal('deployVersionShow');
    this.props.versionListBtn({
      type: this.props.type,
      range,
    });
  };

  _batchDeployChangeVisible = () => {
    this.props.clearRangeVersionData();
    this.props.changeModal('batchDeployShow');
  };

  render() {
    const { type } = this.props;

    return (
      <RedirectToEcc
        jumpKey="version_manage_detail"
        idcTag={getLocationSearchMap(location.search)?.idc ?? undefined}
        versionTab={getLocationSearchMap(location.search)?.tab}
      >
        <GutterWrapper mode="vertical">
          <TinyCard>
            <TabList
              deployVersionChange={this._deployVersionChange}
              versionListChange={this._versionListChange}
              deployListChange={this._deployListChange}
              createVersionChangeVisible={this._createVersionChangeVisible}
              batchDeploy={this._batchDeployChangeVisible}
            />
          </TinyCard>
          <CreateVersionModal type={type} />
          <VersionListModal type={type} />
          <DeployListModal type={type} />
          <BatchDeployModal type={type} />
          <DeployVersionModal type={type} currRange={this.state.currRange} />
        </GutterWrapper>
      </RedirectToEcc>
    );
  }
}

const mapStateToProps = ({
  versionManage: {
    loading,
    type,
    versionData,
    createVersionData,
    rangeVersionDataList,
    deployListData,
  },
}) => ({
  loading,
  type,
  versionData,
  createVersionData,
  rangeVersionDataList,
  deployListData,
});
const mapDispatchToProps = {
  versionManageActions,
  fetchVersionManagePage,
  createVersionBtn,
  versionListBtn,
  deleteVersionBtn,
  deployVersionBtn,
  deployListBtn,
  changeModal: versionManageActions.changeModal,
  clearRangeVersionData: versionManageActions.clearRangeVersionData,
  changeType: versionManageActions.changeType,
};
function VersionManage(props) {
  const { search } = useLocation();
  const { tab } = getLocationSearchMap(search);

  return <VersionManageDetail tab={tab} {...props} />;
}

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(Form.create({ name: 'version_create' })(VersionManage));
