import React, { Component } from 'react';
import { connect } from 'react-redux';

import { Cascader } from '@manyun/base-ui.ui.cascader';
import { Spin } from '@manyun/base-ui.ui.spin';
import { TreeSelect } from '@manyun/base-ui.ui.tree-select';

import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import { generateTreeData } from '@manyun/dc-brain.legacy.utils/index';

/**
 * 楼栋树组件
 * 和楼栋及联组件
 */
class ListSpaceCascader extends Component {
  componentDidMount() {
    this.props.syncCommonData({ strategy: { space: 'FORCED' } });
  }

  _selectSpace = (value, selectedOptions) => {
    const data = {};
    data[this.props.name] = value;
    if (this.props.setFieldsValue) {
      this.props.setFieldsValue(data);
    }
    if (this.props.onChangeData) {
      this.props.onChangeData(data);
    }
  };

  _displayRender = label => label.join('.');

  _onChange = (v, l, e) => {
    const data = {};
    data[this.props.name] = v;
    this.props.setFieldsValue(data);
  };

  render() {
    const { spaceTreeData, dataType, style } = this.props;

    if (dataType === 'tree') {
      return (
        <TreeSelect
          showSearch
          allowClear
          multiple
          maxTagCount={3}
          fieldNames={{
            value: 'code',
            title: 'code',
          }}
          treeData={spaceTreeData}
          onChange={this._onChange}
        />
      );
    }

    return (
      <Cascader
        placeholder=""
        style={style}
        fieldNames={{ label: 'label', value: 'value', children: 'children' }}
        notFoundContent={<Spin size="small" />}
        displayRender={this._displayRender}
        options={spaceTreeData}
        allowClear
        onChange={this._selectSpace}
      />
    );
  }
}

const mapStateToProps = ({ 'resource.spaces': { entities, codes } }, ownProps) => {
  if (codes) {
    const flatSpaceData = (codes || []).map(code => entities[code]);
    const { nodeTypes } = ownProps;
    const selectable = nodeTypes.length === 1 ?? false;
    const spaceTreeData = generateTreeData(flatSpaceData, {
      key: 'code',
      typeKey: 'type',
      parentKey: 'parentCode',
      nodeTypes: nodeTypes,
      getNode: node => {
        if (node.type === 'IDC') {
          return {
            ...node,
            label: node.code,
            value: node.code,
            selectable: selectable,
          };
        }
        return {
          ...node,
          label: node.name,
          value: node.code,
        };
      },
    });
    return {
      spaceTreeData,
    };
  }
  return {
    spaceTreeData: [],
  };
};

const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps, null, { forwardRef: true })(
  ListSpaceCascader
);
