import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import size from 'lodash/size';

import { Input } from '@manyun/base-ui.ui.input';

import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';

import { TinyModal } from '@manyun/dc-brain.legacy.components';
import {
  createVersionBtn,
  versionManageActions,
} from '@manyun/dc-brain.legacy.redux/actions/versionManageActions';

import {
  allRangeTypeList,
  blockRangeTypeList,
  formItemLayout,
  idcRangeTypeList,
} from '../constants';

/**
 * 创建版本弹出框
 */
class CreateVersionModal extends Component {
  /**
   * 创建版本按钮触发
   */
  _createVersionAction = () => {
    this.props.form.validateFields((err, values) => {
      if (!err) {
        const { ranges, description } = values;
        const params = {
          ranges: ranges,
          description,
          type: this.props.type,
        };
        if (allRangeTypeList.indexOf(this.props.type) === -1) {
          params.ranges = ['ALL'];
        }
        this.props.createVersionBtn({
          ...params,
        });
      }
    });
  };
  /**
   * 创建版本框展示/隐藏切换
   */
  _changeVisible = () => {
    this.props.changeVisible('createVersionShow');
  };
  _checkLength = (_rule, stand, callback) => {
    if (!stand) {
      callback();
    } else {
      const len = size(stand);
      if (len >= 128) {
        callback('输入内容太长');
      } else {
        callback();
      }
    }
  };
  /**
   * 渲染
   */
  render() {
    const { createVersionShow, type, confirmLoading } = this.props;
    const { getFieldDecorator, setFieldsValue } = this.props.form;
    return (
      <TinyModal
        key="createVersion"
        title="生成版本"
        destroyOnClose
        open={createVersionShow}
        confirmLoading={confirmLoading}
        onCancel={this._changeVisible}
        onOk={this._createVersionAction}
      >
        <Form {...formItemLayout}>
          {(idcRangeTypeList.indexOf(type) !== -1 || blockRangeTypeList.indexOf(type) !== -1) && (
            <Form.Item label="选择位置">
              {getFieldDecorator('ranges', {
                rules: [{ required: true, message: '位置必选' }],
              })(
                <LocationTreeSelect
                  style={{ width: '100%' }}
                  multiple
                  setFieldsValue={setFieldsValue}
                  disabledTypes={blockRangeTypeList.indexOf(type) === -1 ? [] : ['IDC']}
                  nodeTypes={idcRangeTypeList.indexOf(type) === -1 ? ['IDC', 'BLOCK'] : ['IDC']}
                  onChange={e => {
                    setFieldsValue({ ranges: e });
                  }}
                />
              )}
            </Form.Item>
          )}
          <Form.Item label="描述">
            {getFieldDecorator('description', {
              rules: [
                { required: true, message: '描述必填！' },
                {
                  validator: (_rule, stand, callback) => this._checkLength(_rule, stand, callback),
                },
              ],
            })(<Input.TextArea />)}
          </Form.Item>
        </Form>
      </TinyModal>
    );
  }
}
const mapStateToProps = ({ versionManage: { createVersionShow, confirmLoading } }) => ({
  createVersionShow,
  confirmLoading,
});
const mapDispatchToProps = {
  createVersionBtn,
  changeVisible: versionManageActions.changeModal,
};
export default connect(
  mapStateToProps,
  mapDispatchToProps
)(Form.create({ name: 'version_create' })(CreateVersionModal));
