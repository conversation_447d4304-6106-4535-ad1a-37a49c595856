import React, { Component } from 'react';
import { connect } from 'react-redux';

import MinusCircleOutlined from '@ant-design/icons/es/icons/MinusCircleOutlined';
import PlusCircleOutlined from '@ant-design/icons/es/icons/PlusCircleOutlined';
import { Select } from '@galiojs/awesome-antd';

import {
  versionListBtn,
  versionManageActions,
} from '@manyun/dc-brain.legacy.redux/actions/versionManageActions';

import { idcRangeTypeList } from '../constants';
import ListSpaceCascader from './list-block';

/**
 * 楼栋范围和版本列表
 */
class BlockVersion extends Component {
  state = {
    //范围
    range: null,
    /**
     * 版本
     */
    version: null,
  };

  /**
   * 选择range
   * @param {*}} data
   */
  _onChangeData = data => {
    this._setValue(undefined);
    const rangeDataArr = data[this.props.name];

    if (!data || !rangeDataArr || (Array.isArray(rangeDataArr) && rangeDataArr.length < 1)) {
      this.setState({ version: null });
      return;
    }

    const rangeData = rangeDataArr[1];
    this.setState({
      range: rangeData,
      version: null,
    });
    this.props.versionListBtn({
      range: rangeData,
      type: this.props.type,
      from: 'batch',
      name: this.props.name,
      versionStatusList: ['WAIT_RELEASE'],
    });
  };
  /**
   * 选择版本
   *
   * @param {*} value
   * @param {*} opt
   */
  _onChangeSelectData = (value, opt) => {
    if (!value) {
      return;
    }
    this.setState({
      version: value,
    });
    if (this.state.range) {
      this._setValue({
        version: value,
        range: this.state.range,
      });
    }
  };
  /**
   * 更改属性
   *
   * @param {*} value
   */
  _setValue = value => {
    const { onChange } = this.props;
    onChange({
      value,
    });
  };
  /**
   * 渲染
   */
  render() {
    const {
      type,
      name,
      rangeVersionTreeData,
      plusIcon,
      minusIcon,
      plusClick,
      minusClick,
      rowIndex,
    } = this.props;
    return (
      <div>
        <ListSpaceCascader
          style={{ width: '40%', marginRight: '1%' }}
          nodeTypes={idcRangeTypeList.indexOf(type) === -1 ? ['IDC', 'BLOCK'] : ['IDC']}
          name={name}
          onChangeData={this._onChangeData}
        />
        <Select
          style={{ width: '40%', marginRight: '1%' }}
          value={this.state.version}
          onChange={this._onChangeSelectData}
        >
          {rangeVersionTreeData[name] &&
            rangeVersionTreeData[name].map((item, index) => (
              <Select.Option key={name + '_op_' + index} value={item.version}>
                {item.version}
              </Select.Option>
            ))}
        </Select>
        {plusIcon && <PlusCircleOutlined style={{ marginRight: '1%' }} onClick={plusClick} />}

        {minusIcon && <MinusCircleOutlined onClick={() => minusClick(rowIndex)} />}
      </div>
    );
  }
}

const mapStateToProps = ({ versionManage: { rangeVersionTreeData } }) => ({
  rangeVersionTreeData,
});
const mapDispatchToProps = {
  versionListBtn,
  changeVisible: versionManageActions.changeModal,
};
export default connect(mapStateToProps, mapDispatchToProps, null, { forwardRef: true })(
  BlockVersion
);
