import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import keys from 'lodash/keys';
import size from 'lodash/size';

import { Input } from '@manyun/base-ui.ui.input';

import { TinyModal } from '@manyun/dc-brain.legacy.components';
import {
  deployVersionBtn,
  versionManageActions,
} from '@manyun/dc-brain.legacy.redux/actions/versionManageActions';

import { formItemLayout } from '../constants';
import BlockVersion from './block-version';

/**
 * 创建版本弹出框
 */
class BatchDeployModal extends Component {
  state = {
    //处理动态生成范围版本
    items: [1],
  };
  /**
   * 窗口显示/隐藏
   */
  _changeVisible = () => {
    this.setState({ items: [1] });
    this.props.changeVisible('batchDeployShow');
  };
  /**
   * 批量部署按钮确定
   */
  _batchDeployVersion = () => {
    this.props.form.validateFields((err, values) => {
      if (!err) {
        const versionMap = {};
        keys(values).forEach(key => {
          if (key.indexOf('range_') === 0) {
            const tmp = values[key];
            versionMap[tmp.value.range] = tmp.value.version;
          }
        });
        this.props.deployVersionBtn({
          type: this.props.type,
          versionMap: versionMap,
          description: values.description,
          from: 'batch',
        });
      }
    });
  };
  /**
   * 动态删除范围版本列表
   *
   * @param {*} indx 需要删除的项
   */
  _minusRangeAndVersion = indx => {
    const curItems = this.state.items;
    curItems.splice(indx, 1);
    this.setState({
      items: curItems,
    });
    this.props.form.validateFields({ force: true });
  };
  /**
   * 动态添加范围版本列表
   */
  _plusRangeAndVersion = () => {
    const currMax = Math.max(...this.state.items);
    const curItems = this.state.items;
    curItems.push(currMax + 1);
    this.setState({
      items: curItems,
    });
  };
  /**
   * 相同范围的版本不能同时部署
   *
   * @param {*}} _rule
   * @param {*} stand
   * @param {*} callback
   */
  _checkRangeAndVersion = (_item, _rule, stand, callback) => {
    if (!stand || !stand.value) {
      callback('范围和版本不能为空');
    } else {
      const rangeList = this.state.items.map(item => 'range_' + item);
      const allData = this.props.form.getFieldsValue(rangeList);
      let flag = true;
      const crr_item = 'range_' + _item;
      rangeList.forEach(v => {
        try {
          if (!allData[v] || !allData[v].value || crr_item === v) {
            return;
          }
          const curRang = allData[v].value.range;
          if (curRang === stand.value.range) {
            flag = false;
          }
        } catch (err) {
          callback(err);
        }
      });
      if (flag) {
        callback();
      } else {
        callback('一个范围只能发布一个版本！！！');
      }
    }
  };
  /**
   * check size
   * @param {ch} _rule
   * @param {*} stand
   * @param {*} callback
   */
  _checkDescSize = (_rule, stand, callback) => {
    if (!stand) {
      callback('描述不能为空');
    } else {
      if (size(stand) >= 128) {
        callback('描述内容过长，最大128！！！');
      } else {
        callback();
      }
    }
  };
  /**
   * 渲染
   */
  render() {
    const { batchDeployShow, type, confirmLoading } = this.props;
    const { getFieldDecorator } = this.props.form;
    const rangeAndVersion = this.state.items.map((item, index) => (
      <Form.Item key={'form_item_' + item} label={index === 0 ? '位置&版本' : ' '}>
        {getFieldDecorator('range_' + item, {
          rules: [
            { required: true, message: '位置&版本必选' },
            {
              validator: (_rule, stand, callback) =>
                this._checkRangeAndVersion(item, _rule, stand, callback),
            },
          ],
        })(
          <BlockVersion
            name={'range_' + item}
            type={type}
            plusIcon
            minusIcon={index > 0}
            plusClick={this._plusRangeAndVersion}
            minusClick={this._minusRangeAndVersion}
            rowIndex={index}
          />
        )}
      </Form.Item>
    ));
    return (
      <TinyModal
        key="batchDeployVersion"
        title="批量发布"
        destroyOnClose
        open={batchDeployShow}
        confirmLoading={confirmLoading}
        onCancel={this._changeVisible}
        onOk={this._batchDeployVersion}
      >
        <Form {...formItemLayout}>
          {rangeAndVersion}
          <Form.Item label="描述">
            {getFieldDecorator('description', {
              rules: [
                { required: true, message: '描述必填！' },
                {
                  validator: (_rule, stand, callback) =>
                    this._checkDescSize(_rule, stand, callback),
                },
              ],
            })(<Input.TextArea />)}
          </Form.Item>
        </Form>
      </TinyModal>
    );
  }
}

const mapStateToProps = ({ versionManage: { batchDeployShow, type, confirmLoading } }) => ({
  batchDeployShow,
  type,
  confirmLoading,
});
const mapDispatchToProps = {
  deployVersionBtn,
  changeVisible: versionManageActions.changeModal,
};
export default connect(
  mapStateToProps,
  mapDispatchToProps
)(Form.create({ name: 'batch_deploy' })(BatchDeployModal));
