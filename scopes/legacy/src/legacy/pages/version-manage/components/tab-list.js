import React, { useEffect, useMemo, useRef } from 'react';
import { connect, useDispatch, useSelector } from 'react-redux';

import last from 'lodash/last';
import reduce from 'lodash/reduce';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import { Typography } from '@manyun/base-ui.ui.typography';

import { selectMyResources } from '@manyun/auth-hub.state.user';
import { syncCommonDataAction } from '@manyun/dc-brain.state.common';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';
import {
  fetchVersionManagePage,
  versionManageActions,
} from '@manyun/dc-brain.legacy.redux/actions/versionManageActions';

import { blockRangeTypeList, idcRangeTypeList, tabList } from '../constants';

const { Text } = Typography;

export function onTabChange(changeType, myBlocks, fetchVersionManagePage) {
  return key => {
    changeType(key);
    handleFetchVersionManagePage(key, myBlocks, fetchVersionManagePage);
  };
}

const handleFetchVersionManagePage = (type, myBlocks, fetchVersionManagePage) => {
  const params = {
    type,
  };
  if (type === 'NON_POINT' || type === 'NON_ALARM') {
    params.rangeList = myBlocks;
  } else if (type === 'POINT' || type === 'ALARM') {
    params.rangeList = ['ALL'];
  }
  return fetchVersionManagePage(params);
};

export function TabList({
  versionData,
  deployVersionChange,
  versionListChange,
  deployListChange,
  createVersionChangeVisible,
  batchDeploy,
  fetchVersionManagePage,
  changeType,
  type,
}) {
  const timeIntervalRef = useRef();

  const myResources = useSelector(selectMyResources);
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(
      syncCommonDataAction({
        strategy: {
          space: 'FORCED',
        },
      })
    );
  }, [dispatch]);

  const myBlocks = useMemo(
    () => myResources.filter(resource => resource.type === 'BLOCK').map(item => item.code),
    [myResources]
  );

  useEffect(() => {
    const currentInterval = setInterval(() => {
      handleFetchVersionManagePage(type, myBlocks, fetchVersionManagePage);
    }, 3 * 1000);

    timeIntervalRef.current = currentInterval;

    return () => {
      clearInterval(timeIntervalRef.current);
    };
  }, [type, fetchVersionManagePage, myBlocks]);
  const pd = reduce(
    versionData,
    (result, v, indx) => {
      const index = indx % 6;
      if (index === 0) {
        result.push([v]);
      } else {
        last(result).push(v);
      }
      return result;
    },
    []
  );
  const rowList = pd.map((row, index) => (
    <Row key={row[index]?.range} gutter={[8, 10]}>
      {row.map(item => (
        <Col key={item.range} span={4}>
          <Card
            bodyStyle={{ padding: '8px' }}
            title={item.range}
            extra={
              <Button
                type="link"
                style={{ padding: 0, height: 'auto' }}
                onClick={() => {
                  deployVersionChange(item.range);
                }}
              >
                发布
              </Button>
            }
            bordered
            hoverable={false}
            actions={[
              <Button
                key={item.range}
                type="link"
                style={{ padding: 0, height: 'auto' }}
                onClick={() => {
                  versionListChange(item.range);
                }}
              >
                版本列表
              </Button>,
              <Button
                key={item.range}
                type="link"
                style={{ padding: 0, height: 'auto' }}
                onClick={() => {
                  deployListChange(item.range);
                }}
              >
                发布记录
              </Button>,
            ]}
          >
            <Text>当前版本: {item.currentVersion ? item.currentVersion : '待发布'}</Text>
            <br />
            <Row>
              <Col span={12}>
                <Text>最新版本: {item.newVersion}</Text>
              </Col>
              <Col span={12} push={0}>
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    color: 'var(--text-color-1)',
                    fontSize: 12,
                  }}
                >
                  <div
                    style={{
                      display: 'inline-block',
                      width: 6,
                      height: 6,
                      borderRadius: '50%',
                      backgroundColor:
                        item.versionStatus.code === 'RELEASED'
                          ? `var(--${prefixCls}-success-color)`
                          : `var(--${prefixCls}-warning-color)`,
                    }}
                  />
                  <div
                    style={{
                      display: 'inline-block',
                      margin: '0 6px',
                    }}
                  >
                    {item.versionStatus.name}
                  </div>
                </div>
              </Col>
            </Row>
            <Text>发布时间: {item.pushTime ? item.pushTime : '待发布'}</Text>
          </Card>
        </Col>
      ))}
    </Row>
  ));
  const tabs = tabList.map(item => (
    <Tabs.TabPane key={item.key} tab={item.name}>
      <GutterWrapper style={{ lineHeight: '40px' }}>
        <Button type="primary" onClick={createVersionChangeVisible}>
          生成版本
        </Button>
        {(idcRangeTypeList.indexOf(item.key) !== -1 ||
          blockRangeTypeList.indexOf(item.key) !== -1) && (
          <Button type="primary" onClick={batchDeploy}>
            批量发布
          </Button>
        )}
      </GutterWrapper>
      <GutterWrapper style={{ margin: '10px 0 0 0' }}>{rowList}</GutterWrapper>
    </Tabs.TabPane>
  ));

  return (
    <Tabs activeKey={type} onChange={onTabChange(changeType, myBlocks, fetchVersionManagePage)}>
      {tabs}
    </Tabs>
  );
}

const mapStateToProps = ({ versionManage: { type, versionData } }) => ({
  type,
  versionData,
});
const mapDispatchToProps = {
  changeVisible: versionManageActions.changeModal,
  changeType: versionManageActions.changeType,
  fetchVersionManagePage,
};

export default connect(mapStateToProps, mapDispatchToProps)(TabList);
