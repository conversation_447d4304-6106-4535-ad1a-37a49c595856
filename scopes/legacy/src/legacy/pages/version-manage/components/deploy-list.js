import React, { Component } from 'react';
import { connect } from 'react-redux';

import { TinyModal, TinyTable } from '@manyun/dc-brain.legacy.components';
import { versionManageActions } from '@manyun/dc-brain.legacy.redux/actions/versionManageActions';

const deployListColums = [
  {
    title: '版本号',
    dataIndex: 'version',
  },
  {
    title: '发布人',
    dataIndex: 'operatorName',
  },
  {
    title: '发布时间',
    dataIndex: 'gmtModified',
  },
  {
    title: '备注',
    dataIndex: 'description',
  },
];

/**
 * 发布列表模块，可以改为函数式组件
 */
class DeployListModal extends Component {
  _changeVisible = () => {
    this.props.changeVisible('deployListShow');
  };

  render() {
    const { deployListShow, deployListData, loading } = this.props;
    return (
      <TinyModal
        key="deployList"
        title="发布记录"
        destroyOnClose
        width="808px"
        open={deployListShow}
        footer={null}
        onCancel={this._changeVisible}
      >
        <TinyTable
          rowKey="id"
          columns={deployListColums}
          align="left"
          scroll={{ x: true }}
          dataSource={deployListData}
          loading={loading}
        />
      </TinyModal>
    );
  }
}

const mapStateToProps = ({ versionManage: { deployListShow, deployListData } }) => ({
  deployListShow,
  deployListData,
});
const mapDispatchToProps = {
  changeVisible: versionManageActions.changeModal,
};

export default connect(mapStateToProps, mapDispatchToProps)(DeployListModal);
