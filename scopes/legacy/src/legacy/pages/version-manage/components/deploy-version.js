import React from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import filter from 'lodash/filter';
import size from 'lodash/size';

import { Input } from '@manyun/base-ui.ui.input';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Select } from '@manyun/base-ui.ui.select';

import {
  deployVersionBtn,
  versionManageActions,
} from '@manyun/dc-brain.legacy.redux/actions/versionManageActions';

import { formItemLayout } from '../constants';

/**
 *
 * @param {*} changeVisible 改变modal对应状态
 */
function deployVersionChangeVisible(changeVisible) {
  return () => changeVisible('deployVersionShow');
}

/**
 * 发布版本确定按钮响应
 *
 * @param {*} form 表单信息
 * @param {*} deployVersionBtn 调用后台
 * @param {*} type 当前tab类型
 * @param {*} currRange 当前范围
 */
function deployVersionOk(form, deployVersionBtn, type, currRange) {
  return () =>
    form.validateFields(['description', 'version'], (err, values) => {
      if (!err) {
        const { version, description } = values;
        const versionMap = {};
        versionMap[currRange] = version;
        deployVersionBtn({
          type: type,
          versionMap: versionMap,
          range: currRange,
          description,
          from: 'deploy',
        });
      }
    });
}
function _checkLength(_rule, stand, callback) {
  if (!stand) {
    callback('');
  } else {
    const len = size(stand);
    if (len >= 128) {
      callback('输入内容太长');
    } else {
      callback();
    }
  }
}
/**
 * 发布版本弹出框组件
 *
 * @param {*} type tab类型
 * @param currRange 当前范围
 * @param rangeVersionDataList 下拉框版本列表
 * @param
 */
export function DeployVersionModal({
  type,
  currRange,
  rangeVersionDataList,
  deployVersionShow,
  changeVisible,
  form,
  deployVersionBtn,
  confirmLoading,
}) {
  const { getFieldDecorator } = form;
  return (
    <Modal
      key="deployVersion"
      title="发布"
      destroyOnClose
      open={deployVersionShow}
      confirmLoading={confirmLoading}
      onCancel={deployVersionChangeVisible(changeVisible)}
      onOk={deployVersionOk(form, deployVersionBtn, type, currRange)}
    >
      <Form {...formItemLayout}>
        <Form.Item label="版本号">
          {getFieldDecorator('version', {
            rules: [{ required: true, message: '版本号必选' }],
          })(
            <Select style={{ width: '100%' }}>
              {filter(rangeVersionDataList, { versionStatus: { code: 'WAIT_RELEASE' } }).map(d => (
                <Select.Option key={d.version}>{d.version}</Select.Option>
              ))}
            </Select>
          )}
        </Form.Item>
        <Form.Item label="描述">
          {getFieldDecorator('description', {
            rules: [
              { required: true, message: '描述必填！' },
              {
                validator: (_rule, stand, callback) => _checkLength(_rule, stand, callback),
              },
            ],
          })(<Input.TextArea />)}
        </Form.Item>
      </Form>
    </Modal>
  );
}

const mapStateToProps = ({
  versionManage: { deployVersionShow, rangeVersionDataList, confirmLoading },
}) => ({
  deployVersionShow,
  rangeVersionDataList,
  confirmLoading,
});
const mapDispatchToProps = {
  deployVersionBtn,
  changeVisible: versionManageActions.changeModal,
};
export default connect(
  mapStateToProps,
  mapDispatchToProps
)(Form.create({ name: 'deploy_version' })(DeployVersionModal));
