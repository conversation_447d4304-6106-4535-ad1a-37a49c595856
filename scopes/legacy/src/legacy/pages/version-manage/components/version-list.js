import React, { Component } from 'react';
import { connect } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';

import { TinyModal, TinyTable } from '@manyun/dc-brain.legacy.components';
import {
  deleteVersionBtn,
  deployVersionBtn,
  versionManageActions,
} from '@manyun/dc-brain.legacy.redux/actions/versionManageActions';

const getColumns = ctx => {
  const columns = [
    {
      title: '版本号',
      dataIndex: 'version',
    },
    {
      title: '生成时间',
      dataIndex: 'gmtCreate',
    },
    {
      title: '发布时间',
      dataIndex: 'pushTime',
    },
    {
      title: '备注',
      dataIndex: 'description',
    },
    {
      title: '使用状态',
      dataIndex: ['versionStatus', 'name'],
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      width: 100,
      render: (__, record) => (
        <Space align="center">
          {(record.versionStatus.code === 'WAIT_RELEASE' ||
            record.versionStatus.code === 'BUILDING') && (
            <span>
              <Popconfirm
                title="确认删除吗？"
                placement="topRight"
                onConfirm={() => ctx._deleteVersion(record)}
              >
                <Button type="link" compact>
                  删除
                </Button>
              </Popconfirm>
            </span>
          )}
          {record.versionStatus.code === 'WAIT_RELEASE' && <Divider type="vertical" />}

          {record.versionStatus.code === 'WAIT_RELEASE' && (
            <span>
              <Popconfirm
                title="确认发布该版本吗？"
                placement="topRight"
                onConfirm={() => ctx._deployVersion(record, '', 'record')}
              >
                <Button type="link" compact>
                  发布
                </Button>
              </Popconfirm>
            </span>
          )}
          {(record.versionStatus.code === 'EXPIRED' ||
            record.versionStatus.code === 'RELEASED') && <span>--</span>}
        </Space>
      ),
    },
  ];
  return columns;
};
/**
 * 版本列表模块，可以改为函数式组件
 */
class VersionListModal extends Component {
  _changeVisible = () => {
    this.props.changeVisible('versionListShow');
  };

  _deployVersion = (record, description, from) => {
    const versionMap = {};
    versionMap[record.range] = record.version;
    this.props.deployVersionBtn({
      type: this.props.type,
      versionMap: versionMap,
      range: record.range,
      description,
      from,
    });
  };
  _deleteVersion = record => {
    this.props.deleteVersionBtn({
      type: this.props.type,
      version: record.version,
      range: record.range,
    });
  };

  render() {
    const { versionListShow, rangeVersionDataList, loading } = this.props;
    return (
      <TinyModal
        key="versionList"
        title="版本列表"
        destroyOnClose
        width="808px"
        open={versionListShow}
        footer={null}
        onCancel={this._changeVisible}
      >
        <TinyTable
          rowKey="id"
          columns={getColumns(this)}
          scroll={{ x: 'max-content' }}
          dataSource={rangeVersionDataList}
          loading={loading}
        />
      </TinyModal>
    );
  }
}

const mapStateToProps = ({ versionManage: { versionListShow, rangeVersionDataList } }) => ({
  versionListShow,
  rangeVersionDataList,
});
const mapDispatchToProps = {
  deleteVersionBtn,
  deployVersionBtn,
  changeVisible: versionManageActions.changeModal,
};

export default connect(mapStateToProps, mapDispatchToProps)(VersionListModal);
