// 定义tab列表，后续可以从接口中返回，从而动态确定tabs
export const tabList = [
  {
    name: '设备配置',
    key: 'DEVICE',
  },
  {
    name: '型号规格配置',
    key: 'SPEC',
  },
  {
    name: '标准测点配置',
    key: 'POINT',
  },
  {
    name: '属地测点配置',
    key: 'NON_POINT',
  },
  {
    name: '集团告警配置',
    key: 'ALARM',
  },
  {
    name: '属地告警配置',
    key: 'NON_ALARM',
  },
  {
    name: '北向空间配置',
    key: 'SPACE',
  },
  {
    name: '采集配置',
    key: 'CHANNEL',
  },
  {
    name: '空间配置',
    key: 'SPACE_CNF',
  },
  {
    name: '北向资源配置',
    key: 'CONFIG_DATA',
  },
  {
    name: '北向用户配置',
    key: 'USER_DATA',
  },
  {
    name: '动态基线配置',
    key: 'AI_SCENES',
  },
];
export const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};
export const allRangeTypeList = [
  'CHANNEL',
  'DEVICE',
  'SPACE_CNF',
  'CONFIG_DATA',
  'USER_DATA',
  'NON_POINT',
  'NON_ALARM',
  'AI_SCENES',
];
export const idcRangeTypeList = ['SPACE_CNF'];
export const blockRangeTypeList = [
  'CHANNEL',
  'DEVICE',
  'CONFIG_DATA',
  'USER_DATA',
  'NON_POINT',
  'NON_ALARM',
  'AI_SCENES',
];
