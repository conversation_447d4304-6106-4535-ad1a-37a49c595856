import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLatest } from 'react-use';

import cloneDeep from 'lodash.clonedeep';

import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import { createStore } from '@manyun/dc-brain.aura-graphix';
import { redirectAction } from '@manyun/dc-brain.state.router';
import { generateTopologyGraphixRoutePath } from '@manyun/monitoring.route.monitoring-routes';
import { getBlockFullGraphAction, selectBlockFullGraph } from '@manyun/monitoring.state.topology';
import { GraphPointLineModal } from '@manyun/monitoring.ui.graph-point-line-modal';

import PreviewWrapper from '../preview';

/** 电力拓扑预览，不拆分低压 */
function FullGraphPreview({
  cacheOnly = false,
  highlights = [],
  idc,
  block,
  tenantId,
  defaultTransformerGroupKey,
  hoveringCursor,
  onElementClick,
  topologyType,
}) {
  const blockGuid = React.useMemo(() => `${idc}.${block}`, [idc, block]);

  const dispatch = useDispatch();

  const { loading, code, custom, graph } = useSelector(
    selectBlockFullGraph(blockGuid, topologyType)
  );

  const [store, setStore] = React.useState(null);

  React.useEffect(() => {
    if (!cacheOnly) {
      dispatch(getBlockFullGraphAction({ idc, block, tenantId, topologyType }));
    }

    return () => {
      setStore(null);
    };
  }, [idc, block, cacheOnly, dispatch, tenantId, topologyType]);

  const _highlights = useLatest(highlights);
  const __store = useLatest(store);
  React.useEffect(() => {
    let _store;

    if (code === 'success') {
      const prevStageSize = {};
      if (__store) {
        prevStageSize.width = __store.width;
        prevStageSize.height = __store.height;

        // 避免显示上一次的缓存
        setStore(null);
      }
      _store = createStore();
      if (env.__DEBUG_MODE__) {
        window.topologyGraphixStore = _store;
      }
      const clone = cloneDeep({ ...graph, ...prevStageSize });

      if (_highlights.current.length > 0) {
        clone.pages[0].children.forEach(elem => {
          if (_highlights.current.includes(elem.id)) {
            if (elem.type === 'group') {
              elem.children.push({
                id: elem.id + '_highlight',
                type: 'highlight',
                width: elem.width,
                height: elem.height,
                locked: true,
              });
            }
          }
        });
      }

      _store.loadJSON(clone).then(() => {
        setStore(_store);
      });
    }
  }, [code, __store, _highlights, defaultTransformerGroupKey, custom, graph]);

  const [pointGuids, setPointGuids] = React.useState([]);
  const [pointLineModalVisible, setPointLineModalVisible] = React.useState(false);

  const handleElementClick = element => {
    if (element.custom?.type === 'point-text') {
      const { point } = element.custom;
      const pointGuids = [point].map(() => ({
        ...point,
        deviceGuid: point.deviceGuid,
        unit: point.unit,
        pointCode: point.code,
        serieName: point.name,
      }));
      setPointLineModalVisible(true);
      setPointGuids(pointGuids);
    }
    if (onElementClick) {
      onElementClick();
    }
  };

  return (
    <>
      <GraphPointLineModal
        visible={pointLineModalVisible}
        pointGuids={pointGuids}
        idc={idc}
        modalTitle={pointGuids[0]?.serieName}
        onVisibleChange={() => setPointLineModalVisible(!pointLineModalVisible)}
      />
      <PreviewWrapper
        topologyType="ELECTRIC_POWER" // 这里的液冷拓扑的类型都属于电力拓扑（配置对应的类型，才能匹配到设备）
        moduleId={`${blockGuid}-full-graph-preview`}
        store={store}
        devices={custom?.devices || []}
        loading={loading}
        code={code}
        hoveringCursor={hoveringCursor}
        onRetryBtnClick={() => {
          dispatch(getBlockFullGraphAction({ idc, block, tenantId }));
        }}
        onCreateBtnClick={() => {
          dispatch(
            redirectAction(
              generateTopologyGraphixRoutePath({
                idc,
                block,
                topologyType,
                mode: 'new',
              })
            )
          );
        }}
        onElementClick={handleElementClick}
      />
    </>
  );
}

export default FullGraphPreview;
