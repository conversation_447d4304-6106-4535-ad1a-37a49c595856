import React from 'react';

import Form from '@ant-design/compatible/es/form';
import { Select } from '@galiojs/awesome-antd';

function Optionsform({ form }) {
  const { getFieldDecorator } = form;

  return (
    <Form>
      <Form.Item label="位置">
        {getFieldDecorator('placement', {
          initialValue: 'left-vertical',
        })(
          <Select
            style={{ width: 200 }}
            options={[
              // {
              //   label: '上侧',
              //   value: 'top',
              // },
              // {
              //   label: '右侧',
              //   value: 'right',
              // },
              // {
              //   label: '右侧（逆时针旋转90°）',
              //   value: 'right-vertical',
              // },
              // {
              //   label: '下侧',
              //   value: 'bottom',
              // },
              // {
              //   label: '左侧',
              //   value: 'left',
              // },
              {
                label: '左侧（逆时针旋转90°）',
                value: 'left-vertical',
              },
            ]}
          />
        )}
      </Form.Item>
    </Form>
  );
}

export default Form.create({
  onValuesChange: ({ onChange }, changedValues) => {
    onChange(changedValues);
  },
})(Optionsform);
