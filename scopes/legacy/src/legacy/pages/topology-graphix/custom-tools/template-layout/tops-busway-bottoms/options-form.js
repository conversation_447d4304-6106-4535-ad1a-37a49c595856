import React from 'react';

import Form from '@ant-design/compatible/es/form';

import { Col, Row } from '@manyun/base-ui.ui.grid';
import { InputNumber } from '@manyun/base-ui.ui.input-number';

function Optionsform({ form }) {
  const { getFieldDecorator } = form;

  return (
    <Form style={{ width: 360 }}>
      <Row>
        <Col span={8}>
          <Form.Item label="母线左侧留空">
            {getFieldDecorator('buswayMarginLeft', {
              initialValue: 0,
            })(<InputNumber />)}
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item label="母线右侧留空">
            {getFieldDecorator('buswayMarginRight', {
              initialValue: 32,
            })(<InputNumber />)}
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item label="第三行元素间间距">
            {getFieldDecorator('elementsSpacing', {
              initialValue: 8,
            })(<InputNumber />)}
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
}

export default Form.create({
  onValuesChange: ({ onChange }, changedValues) => {
    onChange(changedValues);
  },
})(Optionsform);
