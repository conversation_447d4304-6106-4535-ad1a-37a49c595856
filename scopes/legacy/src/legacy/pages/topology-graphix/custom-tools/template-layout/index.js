import React, { useState } from 'react';

import SketchOutlined from '@ant-design/icons/es/icons/SketchOutlined';
import { observer } from 'mobx-react-lite';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Radio } from '@manyun/base-ui.ui.radio';

import { ObservableToolItem as ToolItem } from '@manyun/dc-brain.aura-graphix';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';

import deviceNamePlacementThrows from '../../utils/template-layout/device-name-placement';
import nodeWithChildren from '../../utils/template-layout/node-with-children';
import nodeWithMultipleParents from '../../utils/template-layout/node-with-multiple-parents';
import oneByOneThrows from '../../utils/template-layout/one-by-one';
import straightenEdgesThrows from '../../utils/template-layout/straighten-edges';
import top2BottomThrows from '../../utils/template-layout/top-2-bottom';
import DeviceNamePlacementOptionsForm from './device-name-placement/options-form';
import NodeWithChildrenOptionsForm from './node-with-children/options-form';
import TopsBuswayBottomsOptionsForm from './tops-busway-bottoms/options-form';

function TemplateLayout({ store }) {
  const [visible, setVisible] = useState(false);
  const [templateCode, setTemplateCode] = useState(null);
  const [options, setOptions] = useState({});

  const findElement = elementId => store.activePage.findOne(elementId);

  const addLineElement = attrs =>
    store.activePage.addElement(
      {
        type: 'polyline',
        ...attrs,
      },
      false
    );

  return (
    <ToolItem label="模板布局">
      <Button
        size="small"
        block
        disabled={store.selectedElementsIds.length <= 0}
        onClick={() => {
          setVisible(true);
        }}
      >
        <SketchOutlined />
      </Button>
      <Modal
        style={{ minWidth: 648, maxWidth: 1056 }}
        width="75%"
        open={visible}
        title="选择模板进行布局"
        okButtonProps={{ disabled: !templateCode }}
        onOk={() => {
          try {
            if (templateCode === 'top-2-bottom') {
              top2BottomThrows(store.selectedElements, {
                store,
                ...options,
                findElement,
                addLineElement,
              });
            } else if (templateCode === 'node-with-multiple-parents') {
              nodeWithMultipleParents(store.selectedElements, {
                ...options,
                addLineElement,
              });
            } else if (templateCode === 'node-with-children') {
              nodeWithChildren(store.selectedElements, {
                ...options,
                addLineElement,
              });
            } else if (templateCode === 'one-by-one') {
              oneByOneThrows(store.selectedElements, { ...options, addLineElement });
            } else if (templateCode === 'device-name-placement') {
              deviceNamePlacementThrows(store.selectedElements, {
                ...options,
                getTextWidth: store.getTextWidth,
                findOneKonvaNode: store.findOneKonvaNode,
              });
            } else if (templateCode === 'straighten-edges') {
              straightenEdgesThrows(store.selectedElements, {
                findElement,
              });
            }
          } catch (error) {
            message.error('布局失败！');
            console.error(error);
          }
          setVisible(false);
        }}
        onCancel={() => {
          setVisible(false);
        }}
      >
        <GutterWrapper direction="vertical">
          <GutterWrapper style={{ overflowX: 'auto' }} flex>
            <Card
              style={{ width: 240 }}
              hoverable
              bordered={false}
              cover={
                <video
                  style={{ width: 222, height: 140 }}
                  src="/videos/topology-graphix/layout-templates/elements-busway-elements.imv.mp4"
                  autoPlay={false}
                  controls
                />
              }
              actions={[
                <Radio
                  key="check"
                  style={{ width: '100%', margin: 0 }}
                  checked={templateCode === 'top-2-bottom'}
                  onClick={() => {
                    setTemplateCode('top-2-bottom');
                    setOptions({
                      buswayMargin: {
                        top: 128,
                        right: -32,
                        bottom: 128,
                        left: -0,
                      },
                    });
                  }}
                />,
              ]}
            >
              <Card.Meta
                title="元素行+母线+元素行"
                description={
                  <GutterWrapper style={{ height: 105 }} direction="vertical" size="small">
                    <div>1. 不自动调整第一行元素的位置</div>
                    <div>2. 自动调整母线的位置、宽度</div>
                    <div>3. 自动调整第三行元素的位置</div>
                    <div>4. 自动从上往下连线</div>
                  </GutterWrapper>
                }
              />
            </Card>
            <Card
              style={{ width: 240 }}
              hoverable
              bordered={false}
              cover={
                <img
                  style={{ width: 222, height: 140, objectFit: 'contain' }}
                  alt="node-with-multiple-parents"
                  src="/images/topology-graphix/layout-templates/node-with-multiple-parents.jpg"
                />
              }
              actions={[
                <Radio
                  key="check"
                  style={{ width: '100%', margin: 0 }}
                  checked={templateCode === 'node-with-multiple-parents'}
                  onClick={() => {
                    setTemplateCode('node-with-multiple-parents');
                    setOptions({});
                  }}
                />,
              ]}
            >
              <Card.Meta
                title="单元素+多上联"
                description={
                  <GutterWrapper style={{ height: 105 }} direction="vertical" size="small">
                    <div>1. 不自动调整所有上联元素的位置</div>
                    <div>2. 自动调整单元素的位置</div>
                    <div>3. 自动从上往下连线</div>
                  </GutterWrapper>
                }
              />
            </Card>
            <Card
              style={{ width: 240 }}
              hoverable
              bordered={false}
              cover={
                <img
                  style={{ width: 222, height: 140, objectFit: 'contain' }}
                  alt="node-with-children"
                  src="/images/topology-graphix/layout-templates/node-with-children.jpg"
                />
              }
              actions={[
                <Radio
                  key="check"
                  style={{ width: '100%', margin: 0 }}
                  checked={templateCode === 'node-with-children'}
                  onClick={() => {
                    setTemplateCode('node-with-children');
                    setOptions({
                      nodeMargin: {
                        top: 128,
                        left: 8,
                      },
                    });
                  }}
                />,
              ]}
            >
              <Card.Meta
                title="单元素+多下联"
                description={
                  <GutterWrapper style={{ height: 105 }} direction="vertical" size="small">
                    <div>1. 不自动调整单元素的位置</div>
                    <div>2. 自动调整下联元素的位置</div>
                    <div>3. 自动从上往下连线</div>
                  </GutterWrapper>
                }
              />
            </Card>
            <Card
              style={{ width: 240 }}
              hoverable
              bordered={false}
              cover={
                <video
                  style={{ width: 222, height: 140 }}
                  src="/videos/topology-graphix/layout-templates/elements-elements.imv.mp4"
                  autoPlay={false}
                  controls
                />
              }
              actions={[
                <Radio
                  key="check"
                  style={{ width: '100%', margin: 0 }}
                  checked={templateCode === 'one-by-one'}
                  onClick={() => {
                    setTemplateCode('one-by-one');
                    setOptions({});
                  }}
                />,
              ]}
            >
              <Card.Meta
                title="两行元素按顺序连线"
                description={
                  <GutterWrapper style={{ height: 105 }} direction="vertical" size="small">
                    <div>1. 不自动调整所有上联元素的位置</div>
                    <div>2. 自动调整所有对应顺序的下联元素的位置</div>
                    <div>3. 自动从上往下连线</div>
                  </GutterWrapper>
                }
              />
            </Card>
            <Card
              style={{ width: 240 }}
              hoverable
              bordered={false}
              cover={
                <img
                  style={{ width: 222, height: 140, objectFit: 'contain' }}
                  alt="one-by-one"
                  src="/images/topology-graphix/layout-templates/device-name-placement.png"
                />
              }
              actions={[
                <Radio
                  key="check"
                  style={{ width: '100%', margin: 0 }}
                  checked={templateCode === 'device-name-placement'}
                  onClick={() => {
                    setTemplateCode('device-name-placement');
                    setOptions({});
                  }}
                />,
              ]}
            >
              <Card.Meta
                title="设备名称文本位置"
                description={
                  <GutterWrapper style={{ height: 105 }} direction="vertical" size="small">
                    <div>自动调整设备名称文本的位置</div>
                  </GutterWrapper>
                }
              />
            </Card>
            <Card
              style={{ width: 240 }}
              hoverable
              bordered={false}
              cover={
                <video
                  style={{ width: 222, height: 140 }}
                  src="/videos/topology-graphix/layout-templates/straighten-edges.mp4"
                  autoPlay={false}
                  controls
                />
              }
              actions={[
                <Radio
                  key="check"
                  style={{ width: '100%', margin: 0 }}
                  checked={templateCode === 'straighten-edges'}
                  onClick={() => {
                    setTemplateCode('straighten-edges');
                    setOptions({});
                  }}
                />,
              ]}
            >
              <Card.Meta
                title="将母线的边调整为直线"
                description={
                  <GutterWrapper style={{ height: 105 }} direction="vertical" size="small">
                    <div>1. 不调整母线位置</div>
                    <div>2. 若有必要，会调整上联或下联元素的位置</div>
                  </GutterWrapper>
                }
              />
            </Card>
          </GutterWrapper>
          {templateCode === 'top-2-bottom' && (
            <TopsBuswayBottomsOptionsForm
              onChange={changedOptions => {
                setOptions(prev => {
                  if (changedOptions.buswayMarginLeft !== undefined) {
                    changedOptions.buswayMargin = {
                      ...prev.buswayMargin,
                      left: -Number(changedOptions.buswayMarginLeft),
                    };
                  }
                  if (changedOptions.buswayMarginRight) {
                    changedOptions.buswayMargin = {
                      ...prev.buswayMargin,
                      right: -Number(changedOptions.buswayMarginRight),
                    };
                  }

                  return {
                    ...prev,
                    ...changedOptions,
                  };
                });
              }}
            />
          )}
          {templateCode === 'device-name-placement' && (
            <DeviceNamePlacementOptionsForm
              onChange={changedOptions => {
                setOptions(prev => ({
                  ...prev,
                  ...changedOptions,
                }));
              }}
            />
          )}
          {templateCode === 'node-with-children' && (
            <NodeWithChildrenOptionsForm
              options={options}
              onChange={changedOptions => {
                setOptions(prev => {
                  if (changedOptions.nodeMarginTop !== undefined) {
                    changedOptions.nodeMargin = {
                      ...prev.nodeMargin,
                      top: changedOptions.nodeMarginTop,
                    };
                  }

                  if (changedOptions.nodeMarginLeft !== undefined) {
                    changedOptions.nodeMargin = {
                      ...prev.nodeMargin,
                      left: changedOptions.nodeMarginLeft,
                    };
                  }

                  return {
                    ...prev,
                    ...changedOptions,
                  };
                });
              }}
            />
          )}
        </GutterWrapper>
      </Modal>
    </ToolItem>
  );
}

export default observer(TemplateLayout);
