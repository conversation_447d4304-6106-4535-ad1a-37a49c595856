import React from 'react';

import Form from '@ant-design/compatible/es/form';

import { Col, Row } from '@manyun/base-ui.ui.grid';
import { InputNumber } from '@manyun/base-ui.ui.input-number';

function Optionsform({ form, options }) {
  const { getFieldDecorator } = form;

  return (
    <Form style={{ width: 360 }}>
      <Row>
        <Col span={12}>
          <Form.Item label="下联元素与单元素的距离">
            {getFieldDecorator('nodeMarginTop', {
              initialValue: options.nodeMargin.top,
            })(<InputNumber />)}
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item label="下联元素间间距">
            {getFieldDecorator('nodeMarginLeft', {
              initialValue: options.nodeMargin.left,
            })(<InputNumber />)}
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
}

export default Form.create({
  onValuesChange: ({ onChange }, changedValues) => {
    onChange(changedValues);
  },
})(Optionsform);
