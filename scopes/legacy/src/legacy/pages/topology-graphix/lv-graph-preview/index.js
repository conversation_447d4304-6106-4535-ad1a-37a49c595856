import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLatest } from 'react-use';

import cloneDeep from 'lodash.clonedeep';

import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import { createStore } from '@manyun/dc-brain.aura-graphix';
import { redirectAction } from '@manyun/dc-brain.state.router';
import { generateTopologyGraphixRoutePath } from '@manyun/monitoring.route.monitoring-routes';
import { getBlockLVGraphAction, selectBlockLVGraph } from '@manyun/monitoring.state.topology';

import PreviewWrapper from '../preview';

function LVGraphPreview({
  cacheOnly = false,
  highlights = [],
  idc,
  block,
  tenantId,
  defaultTransformerGroupKey,
  hoveringCursor,
  onElementClick,
  topologyType = 'ELECTRIC_POWER',
}) {
  const blockGuid = React.useMemo(() => `${idc}.${block}`, [idc, block]);

  const dispatch = useDispatch();

  const { loading, code, custom } = useSelector(selectBlockLVGraph(blockGuid, topologyType));

  const [store, setStore] = React.useState(null);

  React.useEffect(() => {
    if (!cacheOnly) {
      dispatch(getBlockLVGraphAction({ idc, block, tenantId, topologyType }));
    }

    return () => {
      setStore(null);
    };
  }, [idc, block, cacheOnly, dispatch, tenantId, topologyType]);

  const _highlights = useLatest(highlights);
  const __store = useLatest(store);
  React.useEffect(() => {
    let _store;

    if (code === 'success') {
      // 在切换变压器组时，`graph` 的 `width, height` 都是 0
      // 如果不使用上一次的 `store size`(即 `stage size`)
      // 则无法渲染出切换后的变压器组的 `graph`
      const prevStageSize = {};
      if (__store) {
        prevStageSize.width = __store.width;
        prevStageSize.height = __store.height;

        // 避免显示上一次的缓存
        setStore(null);
      }
      _store = createStore();
      if (env.__DEBUG_MODE__) {
        window.topologyGraphixStore = _store;
      }
      const _activeKey = defaultTransformerGroupKey ?? custom.activeTransformerGroupKey;
      const { graph } = custom.lvGraphs.find(item => item.key === _activeKey);
      const clone = cloneDeep({ ...graph, ...prevStageSize });

      if (_highlights.current.length > 0) {
        clone.pages[0].children.forEach(elem => {
          if (_highlights.current.includes(elem.id)) {
            if (elem.type === 'group') {
              elem.children.push({
                id: elem.id + '_highlight',
                type: 'highlight',
                width: elem.width,
                height: elem.height,
                locked: true,
              });
            }
          }
        });
      }

      _store.loadJSON(clone).then(() => {
        setStore(_store);
      });
    }
  }, [code, __store, _highlights, defaultTransformerGroupKey, custom]);

  return (
    <PreviewWrapper
      topologyType="ELECTRIC_POWER"
      moduleId={`${blockGuid}-lv-graph-preview`}
      store={store}
      devices={custom?.devices || []}
      loading={loading}
      code={code}
      hoveringCursor={hoveringCursor}
      onRetryBtnClick={() => {
        dispatch(getBlockLVGraphAction({ idc, block, tenantId }));
      }}
      onCreateBtnClick={() => {
        dispatch(
          redirectAction(
            generateTopologyGraphixRoutePath({
              idc,
              block,
              topologyType,
              mode: 'new',
            })
          )
        );
      }}
      onElementClick={onElementClick}
    />
  );
}

export default LVGraphPreview;
