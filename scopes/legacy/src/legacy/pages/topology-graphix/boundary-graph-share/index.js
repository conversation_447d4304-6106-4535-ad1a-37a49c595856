import React, { useEffect } from 'react';
import { useLocation, useParams } from 'react-router-dom';

import { TOPOLOGY_TYPE_KEY_MAP } from '@manyun/dc-brain.legacy.constants';
import { TOPOLOGY_BOUNDARY_MAP } from '@manyun/dc-brain.legacy.pages/topology/constants';
import { getLocationSearchMap } from '@manyun/dc-brain.legacy.utils';

import GensetGraphPreview from './../genset-graph-preview';
import HVGraphPreview from './../hv-graph-preview';
import { HVACGraphPreview } from './../hvac-graph-preview/hvac-graph-preview';
import LVGraphPreview from './../lv-graph-preview';

const noop = () => {};

const hoveringCursor = () => 'default';

export default function BoundaryGraphShare() {
  const { boundary, idc, block } = useParams();
  const { search } = useLocation();
  const { embed, tenantId } = getLocationSearchMap(search, ['embed', 'tenantId']);

  useEffect(() => {
    if (embed) {
      document.body.style.backgroundColor = 'transparent';
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div
      style={{
        width: '100vw',
        height: '100vh',
      }}
    >
      {boundary === TOPOLOGY_BOUNDARY_MAP.GENERATOR && (
        <GensetGraphPreview
          idc={idc}
          block={block}
          tenantId={tenantId}
          hoveringCursor={hoveringCursor}
          onElementClick={noop}
        />
      )}
      {boundary === TOPOLOGY_BOUNDARY_MAP.HV && (
        <HVGraphPreview
          idc={idc}
          block={block}
          tenantId={tenantId}
          hoveringCursor={hoveringCursor}
          onElementClick={noop}
        />
      )}
      {boundary === TOPOLOGY_BOUNDARY_MAP.LV && (
        <LVGraphPreview
          idc={idc}
          block={block}
          tenantId={tenantId}
          hoveringCursor={hoveringCursor}
          onElementClick={noop}
        />
      )}
      {boundary === TOPOLOGY_TYPE_KEY_MAP.HVAC && (
        <HVACGraphPreview
          idc={idc}
          block={block}
          tenantId={tenantId}
          hoveringCursor={hoveringCursor}
          onElementClick={noop}
        />
      )}
    </div>
  );
}
