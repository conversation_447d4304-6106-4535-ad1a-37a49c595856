import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLatest } from 'react-use';

import cloneDeep from 'lodash.clonedeep';

import { createStore } from '@manyun/dc-brain.aura-graphix';
import { redirectAction } from '@manyun/dc-brain.state.router';
import { generateTopologyGraphixRoutePath } from '@manyun/monitoring.route.monitoring-routes';
import {
  getBlockGensetsGraphAction,
  selectBlockGensetsGraph,
} from '@manyun/monitoring.state.topology';

import PreviewWrapper from '../preview';

export function GensetGraphPreview({
  cacheOnly = false,
  highlights = [],
  idc,
  block,
  tenantId,
  hoveringCursor,
  onElementClick,
}) {
  const blockGuid = React.useMemo(() => `${idc}.${block}`, [idc, block]);

  const dispatch = useDispatch();

  const { loading, code, graph, custom } = useSelector(selectBlockGensetsGraph(blockGuid));

  const [store, setStore] = React.useState(null);

  React.useEffect(() => {
    if (!cacheOnly) {
      dispatch(getBlockGensetsGraphAction({ idc, block, tenantId }));
    }

    return () => {
      setStore(null);
    };
  }, [idc, block, cacheOnly, dispatch, tenantId]);

  const _highlights = useLatest(highlights);
  const __store = useLatest(store);
  React.useEffect(() => {
    let _store;

    if (code === 'success' && !__store.current) {
      _store = createStore();
      const clone = cloneDeep(graph);

      if (_highlights.current.length > 0) {
        clone.pages[0].children.forEach(elem => {
          if (_highlights.current.includes(elem.id)) {
            if (elem.type === 'group') {
              elem.children.push({
                id: elem.id + '_highlight',
                type: 'highlight',
                width: elem.width,
                height: elem.height,
                locked: true,
              });
            }
          }
        });
      }

      _store.loadJSON(clone).then(() => {
        setStore(_store);
      });
    }
  }, [code, graph, _highlights, __store]);

  return (
    <PreviewWrapper
      topologyType="ELECTRIC_POWER"
      moduleId={`${blockGuid}-genset-graph-preview`}
      store={store}
      devices={custom?.devices ?? []}
      loading={loading}
      code={code}
      onRetryBtnClick={() => {
        dispatch(getBlockGensetsGraphAction({ idc, block, tenantId }));
      }}
      onCreateBtnClick={() => {
        dispatch(
          redirectAction(
            generateTopologyGraphixRoutePath({
              idc,
              block,
              topologyType: 'ELECTRIC_POWER',
              mode: 'new',
            })
          )
        );
      }}
      hoveringCursor={hoveringCursor}
      onElementClick={onElementClick}
    />
  );
}

export default GensetGraphPreview;
