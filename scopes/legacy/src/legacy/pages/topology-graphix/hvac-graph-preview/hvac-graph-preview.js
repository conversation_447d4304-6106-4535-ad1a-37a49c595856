import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import cloneDeep from 'lodash/cloneDeep';

import { createStore } from '@manyun/dc-brain.aura-graphix';

import { TOPOLOGY_TYPE_KEY_MAP } from '@manyun/dc-brain.legacy.constants';
import { redirectActionCreator } from '@manyun/dc-brain.legacy.redux/actions/routerActions';
import { getHVACGraphActionCreator } from '@manyun/dc-brain.legacy.redux/actions/topologyGraphixActions';
import { selectHVACPreview } from '@manyun/dc-brain.legacy.redux/selectors/topologyGraphixSelectors';
import * as urlsUtil from '@manyun/dc-brain.legacy.utils/urls';

import PreviewWrapper from '../preview';

export function HVACGraphPreview({
  cacheOnly = false,
  highlights = [],
  idc,
  block,
  tenantId,
  hoveringCursor,
  onElementClick,
}) {
  const { loading, code, graph, devices, extraDevices } = useSelector(selectHVACPreview);
  const dispatch = useDispatch();

  const [store, setStore] = React.useState(null);

  const blockGuid = `${idc}.${block}`;

  React.useEffect(() => {
    return () => {
      setStore(null);
    };
  }, []);

  React.useEffect(() => {
    if (!cacheOnly) {
      dispatch(getHVACGraphActionCreator({ idc, block, tenantId }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [idc, block, cacheOnly]);

  React.useEffect(() => {
    let _store;

    if (code === 'success' && !store) {
      _store = createStore();
      const clone = cloneDeep(graph);

      if (highlights.length > 0) {
        clone.pages[0].children.forEach(elem => {
          if (highlights.includes(elem.id)) {
            if (elem.type === 'group') {
              elem.children.push({
                id: elem.id + '_highlight',
                type: 'highlight',
                width: elem.width,
                height: elem.height,
                locked: true,
              });
            }
          }
        });
      }

      _store.loadJSON(clone).then(() => {
        setStore(_store);
      });
    }

    return () => {
      if (_store) {
        _store.deletePages(_store.pages.map(page => page.id));
        setStore(null);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [code]);

  return (
    <PreviewWrapper
      topologyType={TOPOLOGY_TYPE_KEY_MAP.HVAC}
      moduleId={`${blockGuid}-hvac-graph-preview`}
      store={store}
      devices={devices || []}
      extraDevices={[...(extraDevices || []), /* 订阅当前楼栋的实时数据 */ { guid: blockGuid }]}
      loading={loading}
      code={code}
      onRetryBtnClick={() => {
        dispatch(getHVACGraphActionCreator({ idc, block, tenantId }));
      }}
      onCreateBtnClick={() => {
        dispatch(
          redirectActionCreator(
            urlsUtil.generateTopologyGraphixUrl({
              idc,
              block,
              topologyType: TOPOLOGY_TYPE_KEY_MAP.HVAC,
              mode: 'new',
            })
          )
        );
      }}
      hoveringCursor={hoveringCursor}
      onElementClick={onElementClick}
    />
  );
}
