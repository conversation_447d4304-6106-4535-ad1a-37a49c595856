import React from 'react';
import { useDeepCompareEffect } from 'react-use';

import { normalize, schema } from 'normalizr';

import type { SpecJSON } from '@manyun/resource-hub.model.spec';
import { fetchSpecs } from '@manyun/resource-hub.service.fetch-specs';
import type { ApiQ as SpecsArgs } from '@manyun/resource-hub.service.fetch-specs';

const specsSchema = [
  new schema.Entity<Record<string, SpecJSON>>(
    'specs',
    {},
    {
      idAttribute: 'code',
    }
  ),
];

export type DeviceType = string;
export type SpecCode = string;
export type DeviceSpecsHookOptions = { targets: SpecsArgs[] };
export type DeviceSpecsMap = Record<DeviceType, Record<SpecCode, SpecJSON> | undefined>;

export function useDeviceSpecs({
  targets,
}: DeviceSpecsHookOptions): [React.MutableRefObject<DeviceSpecsMap>] {
  const deviceSpecsRef = React.useRef<DeviceSpecsMap>({});
  useDeepCompareEffect(() => {
    Promise.all(targets.map(target => fetchSpecs(target))).then(resps => {
      resps.forEach(({ data }, idx) => {
        const { deviceType, vendor, modelCode } = targets[idx];
        const key = `${deviceType}_$$_${vendor}_$$_${modelCode}`;
        const { entities } = normalize<
          Record<string, SpecJSON>,
          { specs: Record<string, SpecJSON> },
          string[]
        >(data.data, specsSchema);
        deviceSpecsRef.current[key] = entities.specs;
      });
    });
  }, [targets]);

  return [deviceSpecsRef];
}
