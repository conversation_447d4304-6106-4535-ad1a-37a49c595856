import React, { useCallback, useEffect } from 'react';
import { connect } from 'react-redux';
import { useLatest, usePrevious } from 'react-use';

import clone from 'lodash.clone';
import get from 'lodash.get';
import noop from 'lodash.noop';
import template from 'lodash.template';

import { AuraGraphixPreview, flatPoints2d, getPoints2d } from '@manyun/dc-brain.aura-graphix';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { OutletCabinet } from '@manyun/monitoring.page.room-view';
import { generateSpaceOrDeviceRoutePath } from '@manyun/monitoring.route.monitoring-routes';
import {
  addRealtimeNAlarmsDataSubscriptionActionCreator,
  removeRealtimeNAlarmsDataSubscriptionActionCreator,
} from '@manyun/monitoring.state.subscriptions';
import { generateGetDeviceAlarmStatus } from '@manyun/monitoring.util.get-monitoring-data';
import { CorePointsCard } from '@manyun/resource-hub.ui.core-points-card';

import { POINT_DATA_TYPE_CODE_MAP } from '@manyun/dc-brain.legacy.constants/point';
import { SUBSCRIPTIONS_MODE } from '@manyun/dc-brain.legacy.constants/subscriptions';
import { tryEvaluate } from '@manyun/dc-brain.legacy.pages/topology-graphix/utils/expr-eval';
import { WIRE_FILL_COLOR_MAP } from '@manyun/dc-brain.legacy.pages/topology/constants';
import { generateGetDeviceMonitoringData } from '@manyun/dc-brain.legacy.utils/device';

import { findElementEdges } from '../utils/find-element-edges';
import getDeviceStateImgFilename from '../utils/getDeviceStateImgFilename';
import { useDeviceSpecs } from './hooks/use-device-specs';
import { updateDevicePartsStates } from './reactions/device-parts-reaction';
import { updateWaterLevel } from './reactions/water-level-reaction';
import { getElementStates } from './utils/utils';

// NOTE: can not use CSS Variable here
const ALERTING_COLOR = '#ff4d4f';

function Preview({
  /** @type {import('@manyun/dc-brain.util.config').ConfigUtil} */
  configUtil,
  store,
  devices,
  extraDevices,
  topologyType,
  moduleId,
  realtimeData,
  alarmsData,
  subscribe,
  unsubscribe,
  typeofHvbtc,
  typeofLvbtc,
  typeofTransformer,
  typeofFuelTank,
  typeofFuelDayTank,
  typeofGensetFuelReturnPump,
  typeofGensetFuelSupplyPump,
  hoveringCursor,
  onElementClick,
}) {
  // External hooks
  // ------
  const _devices = [...(devices || []), ...(extraDevices || [])];
  const devicesRef = useLatest(_devices);
  const deviceGuidsRef = useLatest(devices.map(({ guid }) => guid));

  const specsTargets = _devices
    .map(device => {
      if (typeofFuelTank(device.deviceType) || typeofFuelDayTank(device.deviceType)) {
        return {
          deviceType: device.deviceType,
          modelCode: device.productModel,
          vendor: device.vendor,
        };
      }
      return null;
    })
    .filter(Boolean);
  const [specsRef] = useDeviceSpecs({ targets: specsTargets });

  // Internal hooks
  // ------

  const blockGuid = (devices?.length || 0) > 0 ? devices[0].spaceGuid.blockGuid : null;
  const upsOutgoingCabinetDeviceType = configUtil.getOneDeviceType(
    ConfigUtil.constants.deviceTypes.UPS_OUTGOING_CABINET
  );
  const lvOutgoingCabinetDeviceType = configUtil.getOneDeviceType(
    ConfigUtil.constants.deviceTypes.LV_OUTGOING_CABINET
  );

  useEffect(() => {
    const mode = SUBSCRIPTIONS_MODE.REALTIME_N_ALARMS;
    let subscribed = false;
    let timer;
    if (blockGuid && deviceGuidsRef.current.length > 0) {
      timer = setTimeout(() => {
        subscribe({ mode, blockGuid, moduleId, deviceGuids: deviceGuidsRef.current });
        subscribed = true;
      }, 500);
    }

    return () => {
      if (typeof timer == 'number') {
        clearTimeout(timer);
      }
      if (subscribed) {
        unsubscribe({ mode, blockGuid, moduleId });
      }
    };
  }, [blockGuid, moduleId, subscribe, unsubscribe, deviceGuidsRef]);

  const prevReltimeData = usePrevious(realtimeData);
  useEffect(() => {
    if (!realtimeData || !store.activePage) {
      return;
    }

    const isPowerSystemGraph = topologyType === 'ELECTRIC_POWER';

    const pointTextElements = [];
    const hvbtcElements = [];
    const lvbtcElements = [];
    const deviceElements = [];
    // 变压器要特殊处理:
    // 采用其上联的上联的连线是否有电来判断变压器是否有电及是否向下供电
    const transformerElements = [];
    const buswayElements = [];
    store.activePage.children.forEach(child => {
      if (child.custom?.type === 'point-text') {
        pointTextElements.push(child);
      } else if (child.custom?.type === 'device') {
        if (typeofHvbtc(child.custom.deviceType)) {
          hvbtcElements.push(child);
        } else if (typeofLvbtc(child.custom.deviceType)) {
          lvbtcElements.push(child);
        } else if (typeofTransformer(child.custom.deviceType)) {
          transformerElements.push(child);
        } else {
          deviceElements.push(child);
        }
      } else if (child.custom?.type === 'bus-way') {
        buswayElements.push(child);
      }
    });

    const getPrevValue = generateGetPointValue(prevReltimeData);
    const getValue = generateGetPointValue(realtimeData);
    const findOne = id => store.activePage.findOne(id);
    const findParentItems = generateFindConnectedNodes('target', 'source', { findOne });

    if (isPowerSystemGraph) {
      /**
       * 处理高（中）、低压联络柜上的边
       *
       * 如果是高（中）压联络柜，可能存在 3 种情况
       * 1. 母线1 ---> 高（中）压联络柜 <--- 母线2
       * 2. 母线1 ---> 高（中）压联络柜1 <---> 高（中）压隔离柜 <--- 母线2
       * 3. 母线1 ---> 高（中）压联络柜1 <---> 高（中）压联络柜2 <--- 母线2
       *
       * 如果是低压联络柜，可能存在 2 种情况
       * 1. 母线1 ---> 低压联络柜 <--- 母线2
       * 2. 母线1 ---> 低压联络柜1 <---> 低压联络柜2 <--- 母线2
       *
       * @param {object} edge 低压联络柜边
       * @param {Function} callback1 若发现对端节点是母线时的回调方法
       * @param {Function} callback2 若发现对端节点不是母线时的回调方法
       */
      const findBtcRelatedElements = (edge, callback1, callback2 = noop) => {
        const oppositeLinkType = edge.linkType === 'source' ? 'target' : 'source';
        const lineNode = findOne(edge.id);
        if (!lineNode) {
          console.error('Can not find line node(%s)!', edge.id);
          return;
        }
        const element = findOne(lineNode[oppositeLinkType].id);
        if (!element) {
          console.error('Can not find opposite node(%s)!', lineNode[oppositeLinkType].id);
          return;
        }
        if (element.custom.type === 'bus-way') {
          callback1(element);
          return;
        }
        callback2(element);
      };

      const hasOutputtingParentElement = parentElements =>
        parentElements.some(parentElement => {
          const elementConfig = configUtil.getTopologyElementConfig(
            parentElement.custom?.deviceType
          );
          if (!elementConfig.electricityRelatedPointMap) {
            return false;
          }

          const prevStates = parentElement.custom.states;
          const states = getNodeStates(
            elementConfig.electricityRelatedPointMap,
            getPrevValue(parentElement.custom.deviceGuid),
            getValue(parentElement.custom.deviceGuid),
            prevStates
          );

          return states.output.state === 'ON';
        });

      // 若存在高压联络柜，则先处理高压联络柜
      if (hvbtcElements.length > 0) {
        /**
         * 如果高（中）压联络柜一端连的不是母线，而是高（中）压联络柜或高（中）压隔离柜的话
         * 需要把处理过的元素 ID 记录下，方便跳过
         *
         * @type {string[]}
         */
        const skipElementIds = [];

        hvbtcElements.forEach(hvbtcElement => {
          if (skipElementIds.includes(hvbtcElement.id)) {
            return;
          }

          let buswayElement1;
          let buswayElement1Edge;
          let buswayElement2;
          let buswayElement2Edge;

          /**
           * 高（中）压联络柜对端的高（中）压联络柜/高（中）压隔离柜
           */
          let oppositeElement;

          /**
           * 连接 2 个高（中）压联络柜或 1 个高（中）压联络柜和 1 个高（中）压隔离柜的边
           */
          let hvbtcEdge;

          hvbtcElement.edges.forEach(edge => {
            findBtcRelatedElements(
              edge,
              busway => {
                if (!buswayElement1) {
                  buswayElement1 = busway;
                  buswayElement1Edge = edge;
                } else if (!buswayElement2) {
                  buswayElement2 = busway;
                  buswayElement2Edge = edge;
                }
              },
              element => {
                skipElementIds.push(element.id);
                oppositeElement = element;
                hvbtcEdge = edge;
                element.edges.forEach(oppsiteNodeEdge => {
                  findBtcRelatedElements(oppsiteNodeEdge, busway => {
                    buswayElement2 = busway;
                    buswayElement2Edge = oppsiteNodeEdge;
                  });
                });
              }
            );
          });

          if (!(buswayElement1 && buswayElement1Edge && buswayElement2 && buswayElement2Edge)) {
            console.warn(
              `高（中）压联络柜（${hvbtcElement.id}）必须连接 2 条母线或者连接 1 条母线和另 1 个高（中）压联络（或者隔离）柜（也须连接母线）！`
            );
          } else {
            // 要排除掉和联络柜（或隔离柜）连接的边
            const edges = (buswayElement1.edges || []).filter(
              edge => edge.id !== buswayElement1Edge.id
            );
            const busway1ParentElements = findParentItems(edges);
            const busway1Outputting = hasOutputtingParentElement(busway1ParentElements);

            // 母线1 给 高（中）压联络柜 供电
            if (busway1Outputting) {
              // 母线1 ---> 高（中）压联络柜1
              const line1 = store.activePage.findOne(buswayElement1Edge.id);
              const shouldReverseLine1 = line1.source.id !== buswayElement1.id;
              if (shouldReverseLine1) {
                reverseALine(line1, hvbtcElement, buswayElement1);
              }

              // 高（中）压联络柜1/2 ---> 母线2
              const line2 = store.activePage.findOne(buswayElement2Edge.id);
              const targetElement = oppositeElement ? oppositeElement : hvbtcElement;
              const shouldReverseLine2 = line2.source.id !== targetElement.id;
              if (shouldReverseLine2) {
                reverseALine(line2, buswayElement2, targetElement);
              }

              // 高（中）压联络柜1 ---> 高（中）压联络柜2
              if (hvbtcEdge) {
                const line3 = store.activePage.findOne(hvbtcEdge.id);
                const shouldReverseLine3 = line3.source.id !== hvbtcElement.id;
                if (shouldReverseLine3) {
                  reverseALine(line3, oppositeElement, hvbtcElement);
                }
              }
            } else {
              // 要排除掉和联络柜（或隔离柜）连接的边
              const edges = (buswayElement2.edges || []).filter(
                edge => edge.id !== buswayElement2Edge.id
              );
              const busway2ParentElements = findParentItems(edges);
              const busway2Outputting = hasOutputtingParentElement(busway2ParentElements);

              // 母线 2 给 高（中）压联络柜 供电
              if (busway2Outputting) {
                // 母线1 <--- 高（中）压联络柜1
                const line1 = store.activePage.findOne(buswayElement1Edge.id);
                const shouldReverseLine1 = line1.target.id !== buswayElement1.id;
                if (shouldReverseLine1) {
                  reverseALine(line1, buswayElement1, hvbtcElement);
                }

                // 高（中）压联络柜1/2 <--- 母线2
                const line2 = store.activePage.findOne(buswayElement2Edge.id);
                const targetElement = oppositeElement ? oppositeElement : hvbtcElement;
                const shouldReverseLine2 = line2.target.id !== targetElement.id;
                if (shouldReverseLine2) {
                  reverseALine(line2, targetElement, buswayElement2);
                }

                // 高（中）压联络柜1 <--- 高（中）压联络柜2
                if (hvbtcEdge) {
                  const line3 = store.activePage.findOne(hvbtcEdge.id);
                  const shouldReverseLine3 = line3.target.id !== hvbtcElement.id;
                  if (shouldReverseLine3) {
                    reverseALine(line3, hvbtcElement, oppositeElement);
                  }
                }
              }
            }
          }
        });

        // 更新设备及其下联连线的通电状态
        hvbtcElements.forEach(hvbtcElement => {
          const elementConfig = configUtil.getTopologyElementConfig(hvbtcElement.custom.deviceType);
          if (!elementConfig.electricityRelatedPointMap) {
            return;
          }

          const prevStates = hvbtcElement.custom.states;
          const states = getNodeStates(
            elementConfig.electricityRelatedPointMap,
            getPrevValue(hvbtcElement.custom.deviceGuid),
            getValue(hvbtcElement.custom.deviceGuid),
            prevStates
          );
          hvbtcElement.set({
            custom: {
              ...hvbtcElement.custom,
              states,
            },
          });
          updateDeviceImage(hvbtcElement, states);
          updateDownLinkLines(hvbtcElement, states, { findOne });
        });
      }

      // 若存在低压联络柜，则先处理低压联络柜
      if (lvbtcElements.length > 0) {
        // 表示所有的低压联络柜已连接，且正常工作
        let connected = true;
        lvbtcElements.forEach(lvbtcElement => {
          const elementConfig = configUtil.getTopologyElementConfig(lvbtcElement.custom.deviceType);
          if (!elementConfig.electricityRelatedPointMap && connected) {
            connected = false;
            return;
          }

          const prevStates = lvbtcElement.custom.states;
          const states = getNodeStates(
            elementConfig.electricityRelatedPointMap,
            getPrevValue(lvbtcElement.custom.deviceGuid),
            getValue(lvbtcElement.custom.deviceGuid),
            prevStates
          );
          lvbtcElement.set({
            custom: {
              ...lvbtcElement.custom,
              states,
            },
          });

          if (states.core.state !== 'ON' && connected) {
            connected = false;
          }
        });

        // 若所有低压联络柜正常工作中，则需要根据上联设备调整低压联络柜的连线方向
        if (connected) {
          // 只处理第一个低压联络柜
          // 因为要么只有一个低压联络柜
          // 要么两个低压联络相连，处理第一个时会同时处理掉第二个
          const lvbtcElement = lvbtcElements[0];

          let buswayElement1;
          let busway1Edge;
          let buswayElement2;
          let busway2Edge;

          /**
           * 对端低压联络柜
           */
          let oppositeLvbtcElement;
          /**
           * 连接 2 个低压联络柜的边
           */
          let lvbtcEdge;

          lvbtcElement.edges.forEach(edge => {
            findBtcRelatedElements(
              edge,
              busway => {
                if (!buswayElement1) {
                  buswayElement1 = busway;
                  busway1Edge = edge;
                } else if (!buswayElement2) {
                  buswayElement2 = busway;
                  busway2Edge = edge;
                }
              },
              element => {
                if (typeofLvbtc(element.custom?.deviceType)) {
                  oppositeLvbtcElement = element;
                  lvbtcEdge = edge;
                  element.edges.forEach(oppsiteNodeEdge => {
                    findBtcRelatedElements(oppsiteNodeEdge, busway => {
                      // 如果此低压联络柜对端也是低压联络柜，则认为对端连接的母线为母线 2
                      buswayElement2 = busway;
                      busway2Edge = oppsiteNodeEdge;
                    });
                  });
                }
              }
            );
          });

          if (!(buswayElement1 && busway1Edge && buswayElement2 && busway2Edge)) {
            console.warn(
              `低压联络柜（${lvbtcElement.id}）必须连接 2 条母线或者连接 1 条母线和另 1 个低压联络柜（也须连接母线）！`
            );
          } else {
            const edges = (buswayElement1.edges || []).filter(edge => edge.id !== busway1Edge.id);
            const busway1ParentElements = findParentItems(edges);
            const busway1Outputting = hasOutputtingParentElement(busway1ParentElements);

            // 母线 1 给低压联络柜供电
            if (busway1Outputting) {
              // 母线1 ---> 低压联络柜1
              const line1 = store.activePage.findOne(busway1Edge.id);
              const shouldReverseLine1 = line1.source.id !== buswayElement1.id;
              if (shouldReverseLine1) {
                reverseALine(line1, lvbtcElement, buswayElement1);
              }

              // 低压联络柜1/2 ---> 母线2
              const line2 = store.activePage.findOne(busway2Edge.id);
              const targetLvbtc = oppositeLvbtcElement ? oppositeLvbtcElement : lvbtcElement;
              const shouldReverseLine2 = line2.source.id !== targetLvbtc.id;
              if (shouldReverseLine2) {
                reverseALine(line2, buswayElement2, targetLvbtc);
              }

              // 低压联络柜1 ---> 低压联络柜2
              if (lvbtcEdge) {
                const line3 = store.activePage.findOne(lvbtcEdge.id);
                const shouldReverseLine3 = line3.source.id !== lvbtcElement.id;
                if (shouldReverseLine3) {
                  reverseALine(line3, oppositeLvbtcElement, lvbtcElement);
                }
              }
            } else {
              const edges = (buswayElement2.edges || []).filter(edge => edge.id !== busway2Edge.id);
              const busway2ParentElements = findParentItems(edges);
              const busway2Outputting = hasOutputtingParentElement(busway2ParentElements);

              // 母线 2 给低压联络柜供电
              if (busway2Outputting) {
                // 母线1 <--- 低压联络柜1
                const line1 = store.activePage.findOne(busway1Edge.id);
                const shouldReverseLine1 = line1.target.id !== buswayElement1.id;
                if (shouldReverseLine1) {
                  reverseALine(line1, buswayElement1, lvbtcElement);
                }

                // 低压联络柜1/2 <--- 母线2
                const line2 = store.activePage.findOne(busway2Edge.id);
                const targetLvbtc = oppositeLvbtcElement ? oppositeLvbtcElement : lvbtcElement;
                const shouldReverseLine2 = line2.target.id !== targetLvbtc.id;
                if (shouldReverseLine2) {
                  reverseALine(line2, targetLvbtc, buswayElement2);
                }

                // 低压联络柜1 <--- 低压联络柜2
                if (lvbtcEdge) {
                  const line3 = store.activePage.findOne(lvbtcEdge.id);
                  const shouldReverseLine3 = line3.target.id !== lvbtcElement.id;
                  if (shouldReverseLine3) {
                    reverseALine(line3, lvbtcElement, oppositeLvbtcElement);
                  }
                }
              }
            }
          }
        }

        // 调整完低压联络柜的连线方向后
        // 更新连线的通电状态
        lvbtcElements.forEach(lvbtcElement => {
          const states = lvbtcElement.custom.states;
          if (states) {
            updateDeviceImage(lvbtcElement, states);
            updateDownLinkLines(lvbtcElement, states, { findOne });
          }
        });
      }
    }

    pointTextElements.forEach(pointTextElement => {
      const { point, textTemplate: tpl } = pointTextElement.custom;
      const prevValue = getPrevValue(point.deviceGuid)(point.code);
      const value = getValue(point.deviceGuid)(point.code);
      if (prevValue === value) {
        return;
      }
      let text;
      if (Number.isNaN(value)) {
        text = template(tpl)({ value: '--' });
      } else if (point.dataType === POINT_DATA_TYPE_CODE_MAP.DI) {
        const mappings = point.validLimits.map(validLimit => validLimit.split('='));
        const t = mappings.find(([val]) => Number(val) === value);
        if (t) {
          text = template(tpl)({ value: t[1] });
        }
      } else {
        text = template(tpl)({ value });
      }
      pointTextElement.set({ text });
    });

    deviceElements.forEach(deviceElement => {
      const { deviceType, deviceGuid } = deviceElement.custom;
      const device = devicesRef.current.find(device => device.guid === deviceGuid);
      const getPrevPointValue = getPrevValue(deviceGuid);
      const getPointValue = getValue(deviceGuid);
      const elementConfig = configUtil.getTopologyElementConfig(deviceType, topologyType);
      if (elementConfig === undefined) {
        console.error(`Element config not found by device type(${deviceType}).`);
        return;
      }

      const isFuelDayTank = typeofFuelDayTank(deviceType);
      const isFuelTank = typeofFuelTank(deviceType);
      if (isFuelDayTank || isFuelTank) {
        updateWaterLevel({
          element: deviceElement,
          specs: specsRef.current[`${deviceType}_$$_${device?.vendor}_$$_${device?.productModel}`],
          getPointValue: () => {
            const liquidLevelPointCode = configUtil.getPointCode(
              deviceType,
              ConfigUtil.constants.pointCodes.LIQUID_LEVEL
            );
            const liquidLevel = getPointValue(liquidLevelPointCode);

            if (isFuelDayTank) {
              // 标准测点单位为 `mm`，这里需要转成 `cm`，
              // 因为要求设备类型对应的规格 `满油液位高度` 的单位为 `cm`
              // 保持单位一致才能准确计算
              // 后期考虑改为判断测点单位来进行换算
              return liquidLevel / 10;
            }

            // 储油箱的液位单位为 `m`
            return liquidLevel;
          },
        });
      } else if (typeofGensetFuelReturnPump(deviceType) || typeofGensetFuelSupplyPump(deviceType)) {
        if (!elementConfig || !elementConfig.statePointsExpressions) {
          return;
        }
        const prevStates = getElementStates(
          elementConfig.statePointsExpressions,
          getPrevPointValue
        );
        const states = getElementStates(elementConfig.statePointsExpressions, getPointValue);
        updateDevicePartsStates({
          element: deviceElement,
          prevStates,
          states,
        });
      }

      if (!elementConfig.electricityRelatedPointMap) {
        return;
      }
      const prevStates = deviceElement.custom.states;
      const states = getNodeStates(
        elementConfig.electricityRelatedPointMap,
        getPrevValue(deviceElement.custom.deviceGuid),
        getPointValue,
        prevStates
      );

      const shouldUpdateDeviceState =
        prevStates?.core.state !== states.core.state ||
        prevStates?.handcart.state !== states.handcart.state ||
        prevStates?.protectiveEarth.state !== states.protectiveEarth.state;

      const shouldUpdateOutputState =
        isPowerSystemGraph && prevStates?.output.state !== states.output.state;

      if (!shouldUpdateDeviceState && !shouldUpdateOutputState) {
        return;
      }

      deviceElement.set({
        custom: {
          ...deviceElement.custom,
          // 存一下每次的 states
          // 用于校验 states 是否变化来提升性能
          states,
        },
      });

      // 更新设备图片
      if (shouldUpdateDeviceState) {
        updateDeviceImage(deviceElement, states);
      }

      // 更新设备输出线缆颜色
      if (shouldUpdateOutputState) {
        updateDownLinkLines(deviceElement, states, { findOne });
      }
    });

    // Other special Devices that should execute checks after complete `deviceElements` checks
    if (isPowerSystemGraph) {
      transformerElements.forEach(transformerElement => {
        const {
          // 到达变压器的边
          // Should only have one edge
          targets: targetEdges,
        } = findElementEdges(transformerElement);

        // The current Transformer electrical state
        let electrical = null;
        if (targetEdges.length > 0) {
          // We only use the first edge
          const incomingEdge = targetEdges[0];
          const lineElement = store.activePage.findOne(incomingEdge.id);
          if (!lineElement || !lineElement.custom) {
            console.error('element(id: %s) not found in the graph data!', incomingEdge.id);
            electrical = false;
          } else {
            const directUplinkDeviceElement = store.activePage.findOne(lineElement.source.id);
            if (!directUplinkDeviceElement) {
              console.error('element(id: %s) not found in the graph data!', lineElement.source.id);
              electrical = false;
            } else {
              const { targets: directUplinkDeviceElementTargetEdges } =
                findElementEdges(directUplinkDeviceElement);
              const electricals = directUplinkDeviceElementTargetEdges.map(targetEdge => {
                const _lineElement = store.activePage.findOne(targetEdge.id);
                if (!_lineElement || !_lineElement.custom) {
                  return false;
                }

                return _lineElement.custom.electrical;
              });
              electrical = electricals.some(Boolean);

              const shouldUpdate = electrical !== !!directUplinkDeviceElement.custom?.electrical;
              if (!shouldUpdate) {
                return;
              }

              directUplinkDeviceElement.set({
                custom: {
                  ...directUplinkDeviceElement.custom,
                  electrical,
                },
              });

              /** @type {State} */
              const state = electrical === true ? 'ON' : electrical === false ? 'OFF' : 'UNKNOW';

              /** @type {ReturnType<getNodeStates>} */
              const states = {
                core: { state },
                output: { state },
                handcart: { state },
                protectiveEarth: { state },
              };

              updateDeviceImage(directUplinkDeviceElement, states);
              updateDownLinkLines(directUplinkDeviceElement, states, { findOne });
            }
          }
        }

        const shouldUpdate = electrical !== !!transformerElement.custom?.electrical;
        if (!shouldUpdate) {
          return;
        }

        transformerElement.set({
          custom: {
            ...transformerElement.custom,
            electrical,
          },
        });

        /** @type {State} */
        const state = electrical === true ? 'ON' : electrical === false ? 'OFF' : 'UNKNOW';

        /** @type {ReturnType<getNodeStates>} */
        const states = {
          core: { state },
          output: { state },
          handcart: { state: 'N/A' },
          protectiveEarth: { state: 'N/A' },
        };

        updateDeviceImage(transformerElement, states);
        updateDownLinkLines(transformerElement, states, { findOne });
      });

      // 应等待所有设备更新完出线状态后再更新母线状态
      buswayElements.forEach(buswayElement => {
        const downLinkEdges = [];
        const upLinkEdges = [];
        // eslint-disable-next-line no-unused-expressions
        buswayElement.edges?.forEach(edge => {
          if (edge.linkType === 'source') {
            downLinkEdges.push(edge);
          } else if (edge.linkType === 'target') {
            upLinkEdges.push(edge);
          }
        });

        const electricals = upLinkEdges.map(targetEdge => {
          const lineElement = store.activePage.findOne(targetEdge.id);
          if (!lineElement || !lineElement.custom) {
            return false;
          }

          return lineElement.custom.electrical;
        });
        let electrical = null;
        if (electricals.includes(true)) {
          electrical = true;
        } else if (electricals.includes(false)) {
          electrical = false;
        }

        const color = getLineColor(electrical);
        buswayElement.children[0].set({ fill: color });
        downLinkEdges.forEach(sourceEdge => {
          const lineElement = store.activePage.findOne(sourceEdge.id);
          if (!lineElement) {
            return;
          }

          return lineElement.set({ stroke: color, custom: { ...lineElement.custom, electrical } });
        });
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [topologyType, prevReltimeData, realtimeData]);

  useEffect(() => {
    if (!alarmsData || !store.activePage) {
      return;
    }
    const getDeviceAlarmStatus = generateGetDeviceAlarmStatus(alarmsData);
    const getPointAlarmStatus = generateGetPointAlarmStatus(alarmsData);
    store.activePage.children.forEach(child => {
      const customType = child.custom?.type;
      if (customType === 'device') {
        const { deviceGuid, isAlerting: prevIsAlerting } = child.custom;
        const { isAlerting } = getDeviceAlarmStatus(deviceGuid);
        if (!!prevIsAlerting === isAlerting) {
          return;
        }
        child.set({
          custom: {
            ...child.custom,
            isAlerting,
          },
        });
        const remarkTexts = child.store.activePage.find(
          elem =>
            elem.type === 'text' &&
            elem.custom?.type === 'remark-text' &&
            elem.id.startsWith(`${deviceGuid}_$$_`)
        );
        if (remarkTexts) {
          remarkTexts.forEach(remarkText => {
            const __DEFAULT_FILL = remarkText.fill;
            if (isAlerting && remarkText.fill !== ALERTING_COLOR) {
              remarkText.set({
                fill: ALERTING_COLOR,
                custom: { ...remarkText.custom, __DEFAULT_FILL },
              });
            } else if (!isAlerting && remarkText.fill === ALERTING_COLOR) {
              remarkText.set({ fill: remarkText.custom.__DEFAULT_FILL });
            }
          });
        }
        const image = child.findOne(el => el.type === 'image');
        if (image) {
          image.set({ animation: { blink: isAlerting } });
        }
        const alertingMarker = child.findOne(el => el.type === 'alerting-marker');
        if (alertingMarker) {
          alertingMarker.set({ animating: isAlerting });
        } else {
          child.addChild({
            type: 'alerting-marker',
            x: child.width + 14,
            y: 12,
            animating: isAlerting,
          });
        }
      } else if (customType === 'point-text') {
        const {
          point: { code, deviceGuid },
        } = child.custom;
        const { isAlerting } = getPointAlarmStatus(deviceGuid)(code);
        const __DEFAULT_FILL = child.fill;
        if (isAlerting && child.fill !== ALERTING_COLOR) {
          child.set({
            fill: ALERTING_COLOR,
            custom: { ...child.custom, __DEFAULT_FILL },
          });
        } else if (!isAlerting && child.fill === ALERTING_COLOR) {
          child.set({ fill: child.custom.__DEFAULT_FILL });
        }
      }
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [alarmsData]);

  const elementContextMenuHandler = useCallback(e => {
    e.evt.preventDefault();
  }, []);

  const elementClickHandler = useCallback((element, e) => {
    if (element?.custom?.type !== 'device') {
      return;
    }
    window.open(generateSpaceOrDeviceRoutePath({ guid: element.custom.deviceGuid }));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const defaultHoveringCursor = useCallback(({ currentTarget }) => {
    const element = currentTarget.getAttr('element');

    if (!element || element.custom?.type !== 'device') {
      return 'default';
    }

    return 'pointer';
  }, []);

  return (
    <AuraGraphixPreview
      width="100%"
      height="100%"
      store={store}
      hoveringCursor={hoveringCursor || defaultHoveringCursor}
      tooltipRender={({ currentTarget }, { Container }) => {
        const element = currentTarget.getAttr('element');
        const { type, deviceType, deviceGuid } = element?.custom || {};
        if (!element || type !== 'device') {
          return;
        }

        if (
          deviceType === upsOutgoingCabinetDeviceType ||
          deviceType === lvOutgoingCabinetDeviceType
        ) {
          return (
            <Container>
              <OutletCabinet deviceGuid={deviceGuid} />
            </Container>
          );
        } else {
          return (
            <Container innerStyle={{ padding: 0 }}>
              <CorePointsCard
                deviceGuid={deviceGuid}
                deviceType={deviceType}
                bordered={false}
                hideNoDataItems
              />
            </Container>
          );
        }
      }}
      onElementContextMenu={elementContextMenuHandler}
      onElementClick={onElementClick || elementClickHandler}
    />
  );
}

const mapStateToProps = ({
  'monitoring.subscriptions': { devicesRealtimeData, devicesAlarmsData },
  config: { configMap },
}) => {
  const configUtil = new ConfigUtil(configMap.current);
  const getData = generateGetDeviceMonitoringData(
    devicesRealtimeData,
    devicesAlarmsData,
    configMap.current.pointsDefinitionMap
  );

  return {
    configUtil,
    realtimeData: devicesRealtimeData,
    alarmsData: devicesAlarmsData,
    getData,
    typeofHvbtc: configUtil.typeofDeviceGen(ConfigUtil.constants.deviceTypes.HV_BUS_TIE_CABINET),
    typeofLvbtc: configUtil.typeofDeviceGen(ConfigUtil.constants.deviceTypes.LV_BUS_TIE_CABINET),
    typeofTransformer: configUtil.typeofDeviceGen(ConfigUtil.constants.deviceTypes.TRANSFORMER),
    typeofFuelTank: configUtil.typeofDeviceGen(ConfigUtil.constants.deviceTypes.FUEL_TANK),
    typeofFuelDayTank: configUtil.typeofDeviceGen(ConfigUtil.constants.deviceTypes.FUEL_DAY_TANK),
    typeofGensetFuelReturnPump: configUtil.typeofDeviceGen(
      ConfigUtil.constants.deviceTypes.GENERATOR_SET_FUEL_RETURN_PUMP
    ),
    typeofGensetFuelSupplyPump: configUtil.typeofDeviceGen(
      ConfigUtil.constants.deviceTypes.GENERATOR_SET_FUEL_SUPPLY_PUMP
    ),
  };
};
const mapDispatchToProps = {
  subscribe: addRealtimeNAlarmsDataSubscriptionActionCreator,
  unsubscribe: removeRealtimeNAlarmsDataSubscriptionActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(Preview);

function updateDeviceImage(deviceElement, states) {
  const deviceImageElem = deviceElement.findOne(el => el.custom?.type === 'device/image');
  if (!deviceImageElem) {
    return;
  }
  const extension = deviceImageElem.src.substring(deviceImageElem.src.lastIndexOf('.'));
  const filename = getDeviceStateImgFilename(
    {
      core: states.core.state,
      handcart: states.handcart.state,
      protectiveEarth: states.protectiveEarth.state,
    },
    extension
  );
  const newImgSrc =
    deviceImageElem.src.substring(0, deviceImageElem.src.lastIndexOf('/') + 1) + filename;
  deviceImageElem.set({ src: newImgSrc });
}

function updateDownLinkLines(element, states, { findOne }) {
  // null 代表未知
  let electrical = null;
  if (states.output.state === 'ON') {
    electrical = true;
  } else if (states.output.state === 'OFF') {
    electrical = false;
  }
  const color = getLineColor(electrical);
  element.edges.forEach(edge => {
    if (edge.linkType === 'target') {
      return;
    }
    const lineElement = findOne(edge.id);
    if (!lineElement) {
      return;
    }
    lineElement.set({ stroke: color, custom: { ...lineElement.custom, electrical } });
  });
}

/**
 * 将表达式中的测点转为测点值
 *
 * @param {string} expression `expr-eval` 可解析的表达式
 * @param {(pointCode: string) => number} getValueByPointCode 获取测点实时数据的方法
 * @returns {string}
 */
function replacePointValue(expression, getValueByPointCode) {
  const regex = /\$\{[A-Z]*[0-9]+\}/;
  const m = regex.exec(expression);
  if (m === null) {
    return expression;
  }
  const firstMatch = m[0];
  const [prefix, postfix] = expression.split(firstMatch);
  const pointCode = firstMatch.replace('${', '').replace('}', '');

  // 保证测点值被转换成 `string` 进行字符串拼接
  // [prefix, null, postfix].join() 的方式将会把 `undefined, null, []` 转换成 `""`（空字符串）
  // See [Array#join#description](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/join#description)
  const pointValueString = String(getValueByPointCode(pointCode));

  const newExpression = [prefix, pointValueString, postfix].join('');

  return replacePointValue(newExpression, getValueByPointCode);
}

function getNodeStates(
  electricityRelatedPointMap,
  getPrevPointValue,
  getPointValue,
  prevStates = {
    core: {},
    handcart: {},
    protectiveEarth: {},
    output: {},
  }
) {
  let workingState;
  if (typeof electricityRelatedPointMap.working == 'string') {
    const prevWorkingExpr = replacePointValue(
      electricityRelatedPointMap.working,
      getPrevPointValue
    );
    const workingExpr = replacePointValue(electricityRelatedPointMap.working, getPointValue);
    const noDataExpr = replacePointValue(electricityRelatedPointMap.working, getNoDataPointValue);
    const prevWorking = tryEvaluate(prevWorkingExpr);
    const working = tryEvaluate(workingExpr);
    workingState = {
      state: working,
      noData: workingExpr === noDataExpr,
      changed: working !== prevWorking,
    };
  }

  let notWorkingState;
  if (typeof electricityRelatedPointMap.notWorking == 'string') {
    const prevNotWorkingExpr = replacePointValue(
      electricityRelatedPointMap.notWorking,
      getPrevPointValue
    );
    const notWorkingExpr = replacePointValue(electricityRelatedPointMap.notWorking, getPointValue);
    const noDataExpr = replacePointValue(
      electricityRelatedPointMap.notWorking,
      getNoDataPointValue
    );
    const prevNotWorking = tryEvaluate(prevNotWorkingExpr);
    const notWorking = tryEvaluate(notWorkingExpr);
    notWorkingState = {
      state: notWorking,
      noData: notWorkingExpr === noDataExpr,
      changed: notWorking !== prevNotWorking,
    };
  }

  const core = computeFinalState(workingState, notWorkingState, prevStates.core.state);

  let handcartOnState;
  if (typeof electricityRelatedPointMap.handcartOn == 'string') {
    const prevHandcartOnExpr = replacePointValue(
      electricityRelatedPointMap.handcartOn,
      getPrevPointValue
    );
    const handcartOnExpr = replacePointValue(electricityRelatedPointMap.handcartOn, getPointValue);
    const noDataExpr = replacePointValue(
      electricityRelatedPointMap.handcartOn,
      getNoDataPointValue
    );
    const prevHandcartOn = tryEvaluate(prevHandcartOnExpr);
    const handcartOn = tryEvaluate(handcartOnExpr);
    handcartOnState = {
      state: handcartOn,
      noData: handcartOnExpr === noDataExpr,
      changed: handcartOn !== prevHandcartOn,
    };
  }

  let handcartOffState;
  if (typeof electricityRelatedPointMap.handcartOff == 'string') {
    const prevHandcartOffExpr = replacePointValue(
      electricityRelatedPointMap.handcartOff,
      getPrevPointValue
    );
    const handcartOffExpr = replacePointValue(
      electricityRelatedPointMap.handcartOff,
      getPointValue
    );
    const noDataExpr = replacePointValue(
      electricityRelatedPointMap.handcartOff,
      getNoDataPointValue
    );
    const handcartOff = tryEvaluate(handcartOffExpr);
    handcartOffState = {
      state: handcartOff,
      noData: handcartOffExpr === noDataExpr,
      changed: prevHandcartOffExpr !== handcartOffExpr,
    };
  }

  const handcart = computeFinalState(handcartOnState, handcartOffState, prevStates.handcart.state);

  let protectiveEarthOnState;
  if (typeof electricityRelatedPointMap.protectiveEarthOn == 'string') {
    const prevProtectiveEarthOnExpr = replacePointValue(
      electricityRelatedPointMap.protectiveEarthOn,
      getPrevPointValue
    );
    const protectiveEarthOnExpr = replacePointValue(
      electricityRelatedPointMap.protectiveEarthOn,
      getPointValue
    );
    const noDataExpr = replacePointValue(
      electricityRelatedPointMap.protectiveEarthOn,
      getNoDataPointValue
    );
    const prevProtectiveEarthOn = tryEvaluate(prevProtectiveEarthOnExpr);
    const protectiveEarthOn = tryEvaluate(protectiveEarthOnExpr);
    protectiveEarthOnState = {
      state: protectiveEarthOn,
      noData: prevProtectiveEarthOnExpr === noDataExpr,
      changed: protectiveEarthOn !== prevProtectiveEarthOn,
    };
  }

  let protectiveEarthOffState;
  if (typeof electricityRelatedPointMap.protectiveEarthOff == 'string') {
    const prevProtectiveEarthOffExpr = replacePointValue(
      electricityRelatedPointMap.protectiveEarthOff,
      getPrevPointValue
    );
    const protectiveEarthOffExpr = replacePointValue(
      electricityRelatedPointMap.protectiveEarthOff,
      getPointValue
    );
    const noDataExpr = replacePointValue(
      electricityRelatedPointMap.protectiveEarthOff,
      getNoDataPointValue
    );
    const prevProtectiveEarthOff = tryEvaluate(prevProtectiveEarthOffExpr);
    const protectiveEarthOff = tryEvaluate(protectiveEarthOffExpr);
    protectiveEarthOffState = {
      state: protectiveEarthOff,
      noData: protectiveEarthOffExpr === noDataExpr,
      changed: protectiveEarthOff !== prevProtectiveEarthOff,
    };
  }

  const protectiveEarth = computeFinalState(
    protectiveEarthOnState,
    protectiveEarthOffState,
    prevStates.protectiveEarth.state
  );

  let outputtingState;
  if (typeof electricityRelatedPointMap.outputting == 'string') {
    const prevOutputtingExpr = replacePointValue(
      electricityRelatedPointMap.outputting,
      getPrevPointValue
    );
    const outputtingExpr = replacePointValue(electricityRelatedPointMap.outputting, getPointValue);
    const noDataExpr = replacePointValue(
      electricityRelatedPointMap.outputting,
      getNoDataPointValue
    );
    const prevOutputting = tryEvaluate(prevOutputtingExpr);
    const outputting = tryEvaluate(outputtingExpr);
    outputtingState = {
      state: outputting,
      noData: outputtingExpr === noDataExpr,
      changed: outputting !== prevOutputting,
    };
  }

  let noOutputState;
  if (typeof electricityRelatedPointMap.noOutput == 'string') {
    const prevNoOutputExpr = replacePointValue(
      electricityRelatedPointMap.noOutput,
      getPrevPointValue
    );
    const noOutputExpr = replacePointValue(electricityRelatedPointMap.noOutput, getPointValue);
    const noDataExpr = replacePointValue(electricityRelatedPointMap.noOutput, getNoDataPointValue);
    const prevNoOutput = tryEvaluate(prevNoOutputExpr);
    const noOutput = tryEvaluate(noOutputExpr);
    noOutputState = {
      state: noOutput,
      noData: noOutputExpr === noDataExpr,
      changed: noOutput !== prevNoOutput,
    };
  }

  const output = computeFinalState(outputtingState, noOutputState, prevStates.output.state);

  return { core, handcart, protectiveEarth, output };
}

/**
 * 根据正反面状态计算出最终状态
 *
 * @typedef {'N/A'|'UNKNOW'|'ON'|'OFF'} State
 *
 * @typedef StateRecord
 * @property {boolean} state
 * @property {boolean} noData
 * @property {boolean} changed
 *
 * @param {StateRecord} heads 正面状态
 * @param {StateRecord} tails 反面状态
 * @param {State} [defaultState='UNKNOW']
 * @returns {{ state: State }}
 */
function computeFinalState(heads, tails) {
  let finalState = 'UNKNOW';
  if (heads && !tails) {
    finalState = heads.state ? 'ON' : 'OFF';
  } else if (!heads && tails) {
    finalState = tails.state ? 'OFF' : 'ON';
  } else if (heads && tails) {
    if (heads.state && !tails.state) {
      finalState = 'ON';
    } else if (!heads.state && tails.state) {
      finalState = 'OFF';
    }
  } else if (!heads && !tails) {
    finalState = 'N/A';
  }

  return { state: finalState };
}

const NO_DATA_POINT_VALUE = Number.NaN;

/**
 * 从实时数据中获取某个设备下某个点位的测点值的方法生成器
 *
 * @param {Record<string, object>} realtimeData
 * @returns
 */
function generateGetPointValue(realtimeData) {
  /**
   * 获取某个设备下某个点位的测点值
   *
   * > `undefined, null` 会被处理成 `Number.NaN`
   *
   * @param {string} deviceGuid
   * @returns {(pointCode: string) => number}
   */
  const getPointValue = deviceGuid => pointCode => {
    const value = get(
      realtimeData,
      [deviceGuid, 'pointValueMap', pointCode, 'value'],
      NO_DATA_POINT_VALUE
    );
    if (value === undefined || value === null) {
      return NO_DATA_POINT_VALUE;
    }

    return value;
  };

  return getPointValue;
}

function generateGetPointAlarmStatus(alarmsData) {
  const getPointAlarmStatus = deviceGuid => {
    const devicePointsAlarmsCount = get(alarmsData, [deviceGuid, 'pointsCount']);

    return pointCode => {
      const alarmsCount = get(devicePointsAlarmsCount, pointCode);
      const isAlerting = alarmsCount && (alarmsCount.ERROR > 0 || alarmsCount.WARN > 0);

      return { isAlerting };
    };
  };

  return getPointAlarmStatus;
}

function getNoDataPointValue() {
  return NO_DATA_POINT_VALUE;
}

/**
 * 根据通电状态给出连线的颜色
 * @param {null|boolean} electrical `null` 表示未知
 */
function getLineColor(electrical) {
  let color = WIRE_FILL_COLOR_MAP.DEFAULT;
  if (electrical === true) {
    color = WIRE_FILL_COLOR_MAP.ELECTRICAL;
  } else if (electrical === false) {
    color = WIRE_FILL_COLOR_MAP.NOT_ELECTRICAL;
  }

  return color;
}

function hasOne(data, itemId) {
  return data.findIndex(({ id }) => itemId === id) > -1;
}

function generateFindConnectedNodes(edgeLinkType, oppositeEdgeLinkType, { findOne }) {
  return edges => {
    const oppositeNodes = edges.reduce((nodes, { id, linkType }) => {
      if (linkType === edgeLinkType) {
        const line = findOne(id);
        if (!line) {
          return nodes;
        }
        const node = findOne(line[oppositeEdgeLinkType].id);

        if (!hasOne(nodes, node.id)) {
          nodes.push(node);
        }
      }

      return nodes;
    }, []);

    return oppositeNodes;
  };
}

function reverseALine(lineElement, sourceElement, targetElement) {
  targetElement.set({
    edges: targetElement.edges.map(edge => {
      if (edge.id === lineElement.id) {
        return {
          ...edge,
          linkType: 'source',
        };
      }

      return edge;
    }),
  });

  const prevStartPointPlacement = lineElement.startPointPlacement;
  const prevEndPointPlacement = lineElement.endPointPlacement;
  lineElement.set({
    points: flatPoints2d(getPoints2d(clone(lineElement.points)).reverse()),
    source: {
      ...lineElement.source,
      id: targetElement.id,
    },
    target: {
      ...lineElement.target,
      id: sourceElement.id,
    },
    startPointPlacement: prevEndPointPlacement,
    endPointPlacement: prevStartPointPlacement,
  });

  sourceElement.set({
    edges: sourceElement.edges.map(edge => {
      if (edge.id === lineElement.id) {
        return {
          ...edge,
          linkType: 'target',
        };
      }
      return edge;
    }),
  });
}
