import debug from 'debug';

import type {
  GroupElementInstance,
  LiquidFillElementInstance,
  LiquidFillElementSnapshotIn,
  LiquidFillElementSnapshotOut,
} from '@manyun/dc-brain.aura-graphix';
import type { SpecJSON } from '@manyun/resource-hub.model.spec';

const logger = debug('topology-preview:reactions:water-level');

class NotChangedError extends Error {
  constructor(
    message = 'skipping due to water level percentage not changed.',
    options?: ErrorOptions
  ) {
    super(message, options);
  }
}

export type WaterLevelReactionOptions = {
  element: GroupElementInstance;
  specs: Record<string, SpecJSON | undefined> | undefined;
  getPointValue(): number | undefined;
};

export function updateWaterLevel({ element, specs, getPointValue }: WaterLevelReactionOptions) {
  const _logger = makeElementLogger(element);
  function logCatchedError(error: unknown) {
    _logger(error instanceof Error ? error.message : error);
  }

  let liquidFillElement: LiquidFillElementInstance | undefined;
  try {
    liquidFillElement = findLiquidFillElementThrows({ element });
  } catch (error) {
    logCatchedError(error);
  }

  if (!liquidFillElement) {
    return;
  }

  try {
    const {
      waterLevelMax,
      waterLevelUnit,
      currentWaterLevel: unSafeCurrent,
    } = getValuesThrows({
      specs,
      getPointValue,
    });
    const safeCurrent = getSafeCurrent(unSafeCurrent, waterLevelMax, _logger);
    const currentPercentage = Number((safeCurrent / waterLevelMax).toFixed(2));
    skipIfInvalidThrows(liquidFillElement.waterLevel, currentPercentage);
    const labelText = `${unSafeCurrent.toFixed(2)}${waterLevelUnit}`;
    const snapshotIn: Partial<LiquidFillElementSnapshotIn> = makeSnapshotIn(
      currentPercentage,
      labelText,
      liquidFillElement.toJSON()
    );
    _logger('setting partial snapshot: ', snapshotIn);
    liquidFillElement.set(snapshotIn);
  } catch (error) {
    if (!(error instanceof NotChangedError)) {
      liquidFillElement.set({ visible: false });
    }
    logCatchedError(error);
  }
}

function makeElementLogger(element: GroupElementInstance) {
  const prefix = `element(${element.id}): `;

  return (...args: unknown[]) => logger(prefix, ...args);
}

function makeSnapshotIn(
  currentPercentage: number,
  labelText: string,
  existingSnapshot: Partial<LiquidFillElementSnapshotOut>
) {
  const snapshot: Partial<LiquidFillElementSnapshotIn> = {
    waterLevel: currentPercentage,
    label: {
      ...existingSnapshot.label,
      text: labelText,
    },
    animation: {
      ...existingSnapshot.animation,
      waterLevel: true,
    },
    visible: true,
  };

  return snapshot;
}

function skipIfInvalidThrows(previous: number, current: number) {
  const invalid = current < 0;
  if (invalid) {
    throw new Error(`hiding LiquidFill element as water level percentage is less than 0.`);
  }

  const notChanged = previous === current;
  if (notChanged) {
    throw new NotChangedError();
  }
}

/**
 * Get the safe(not overflowed) current water level
 *
 * @param currentWaterLevel
 * @param waterLevelMax
 * @param _logger
 * @returns
 */
function getSafeCurrent(
  currentWaterLevel: number,
  waterLevelMax: number,
  _logger: (...args: unknown[]) => void
) {
  const overflowed = currentWaterLevel > waterLevelMax;
  overflowed &&
    _logger(`current water level is overflowed(${currentWaterLevel} > ${waterLevelMax}).`);
  const currentSafeWaterLevel = overflowed ? waterLevelMax : currentWaterLevel;

  return currentSafeWaterLevel;
}

function findLiquidFillElementThrows({ element }: Pick<WaterLevelReactionOptions, 'element'>) {
  const liquidFillElement = element.findOne<LiquidFillElementInstance>(
    child => child.type === 'liquid-fill'
  );
  if (!liquidFillElement) {
    const message = `skipping due to LiquidFill element not found in element(${element.id}).`;
    throw new Error(message);
  }

  return liquidFillElement;
}

function getValuesThrows({
  specs,
  getPointValue,
}: Pick<WaterLevelReactionOptions, 'specs' | 'getPointValue'>) {
  const waterLevelMaxSpec = specs?.['LIQUID_LEVEL_MAX'];
  const maybeWaterLevelMax = waterLevelMaxSpec?.value;
  const waterLevelMax = Number(maybeWaterLevelMax ?? NaN);
  if (!waterLevelMaxSpec || Number.isNaN(waterLevelMax)) {
    throw new Error(`skipping due to water level max is not a valid value: ${maybeWaterLevelMax}`);
  }
  const waterLevelUnit = waterLevelMaxSpec.unit;
  const currentWaterLevel = getPointValue();
  if (
    typeof currentWaterLevel != 'number' ||
    Number.isNaN(currentWaterLevel) ||
    currentWaterLevel < 0
  ) {
    throw new Error(
      `skipping due to current water level is not a valid value: ${currentWaterLevel}`
    );
  }

  return { waterLevelMax, waterLevelUnit, currentWaterLevel };
}
