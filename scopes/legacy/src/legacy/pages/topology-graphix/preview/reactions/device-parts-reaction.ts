import type {
  AnimationSnapshotIn,
  GroupElementInstance,
  ImageElementInstance,
} from '@manyun/dc-brain.aura-graphix';

export type PartsExpressionResults = {
  fans?: ExpressionResults;
  sprayers?: ExpressionResults;
  pumps?: ExpressionResults;
};

export type DevicePartsReactionOptions = {
  element: GroupElementInstance;
  prevStates: PartsExpressionResults;
  states: PartsExpressionResults;
};

export function updateDevicePartsStates({
  element,
  prevStates,
  states,
}: DevicePartsReactionOptions) {
  const pump = element.findOne<ImageElementInstance>(child => child.custom?.type === 'parts_pump');
  if (pump) {
    updateDevicePartsWorkingState(
      pump,
      prevStates.pumps as ExpressionResults,
      states.pumps as ExpressionResults,
      {
        spin: states.pumps?.working
          ? {
              duration: 1000,
            }
          : false,
      }
    );
  }
}

function updateDevicePartsWorkingState(
  element: ImageElementInstance,
  prevStates: ExpressionResults,
  states: ExpressionResults,
  animation: AnimationSnapshotIn
) {
  updateElementWorkingState(element, prevStates, states, (result, { filename, ext }) => {
    // hide the parts if the point data is not valid
    if (result === 'noisy-data') {
      element.set({
        visible: false,
      });
      return;
    }
    const newFilename = `${result ? 'working' : 'not-working'}.${ext}`;
    const newSrc = element.src.replace(filename, newFilename);
    element.set({
      visible: true,
      src: newSrc,
      animation,
    });
  });
}

type ExpressionResults = {
  working: boolean;
  notWorking: boolean;
};
function updateElementWorkingState(
  element: ImageElementInstance,
  prevStates: ExpressionResults,
  states: ExpressionResults,
  callback: (
    result: 'noisy-data' | boolean,
    infos: {
      filename: string;
      ext: string;
    }
  ) => void
) {
  const filename = element.src.substring(element.src.lastIndexOf('/') + 1);
  const ext = filename.substring(filename.lastIndexOf('.') + 1);
  const infos = { filename, ext };

  if (states.working === states.notWorking && element.visible) {
    callback('noisy-data', infos);
    return;
  }
  if (prevStates.working !== states.working || prevStates.notWorking !== states.notWorking) {
    callback(states.working, infos);
  }
}
