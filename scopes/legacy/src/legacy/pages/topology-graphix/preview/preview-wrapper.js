import React from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Empty } from '@manyun/base-ui.ui.empty';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Space } from '@manyun/base-ui.ui.space';

import { Loading } from '@manyun/dc-brain.legacy.components';

import Preview from './preview';

export default function PreviewWrapper({
  topologyType,
  moduleId,
  loading,
  code,
  store,
  devices,
  extraDevices,
  onRetryBtnClick,
  onCreateBtnClick,
  hoveringCursor,
  onElementClick,
}) {
  let codeTips = null;

  if (code === 'error') {
    codeTips = (
      <Button type="link" onClick={onRetryBtnClick}>
        重试
      </Button>
    );
  }

  if (code === 'no-graph') {
    codeTips = (
      <Empty
        description={
          <Space size="small">
            <span>暂无拓扑数据</span>
            <Button type="link" onClick={onCreateBtnClick}>
              创建
            </Button>
          </Space>
        }
      />
    );
  }

  if (code === 'empty-graph') {
    codeTips = <Empty description="暂无元素可供展示" />;
  }

  if (codeTips) {
    return (
      <Row style={{ width: '100%', height: '100%' }} justify="center" align="middle">
        <Col span={24}>{codeTips}</Col>
      </Row>
    );
  }

  if (loading || !store) {
    return <Loading />;
  }

  return (
    <Preview
      topologyType={topologyType}
      moduleId={moduleId}
      store={store}
      devices={devices}
      extraDevices={extraDevices}
      hoveringCursor={hoveringCursor}
      onElementClick={onElementClick}
    />
  );
}
