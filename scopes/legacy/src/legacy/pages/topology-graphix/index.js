import React, { useCallback, useEffect, useReducer, useRef } from 'react';
import { connect, useSelector } from 'react-redux';
import { useHistory, useParams } from 'react-router-dom';

import BookOutlined from '@ant-design/icons/es/icons/BookOutlined';
import FallOutlined from '@ant-design/icons/es/icons/FallOutlined';
import MinusCircleOutlined from '@ant-design/icons/es/icons/MinusCircleOutlined';
import NumberOutlined from '@ant-design/icons/es/icons/NumberOutlined';
import PlusCircleOutlined from '@ant-design/icons/es/icons/PlusCircleOutlined';
import { ApiSelect } from '@galiojs/awesome-antd';
import cloneDeep from 'lodash/cloneDeep';

import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Result } from '@manyun/base-ui.ui.result';
import { Typography } from '@manyun/base-ui.ui.typography';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import {
  AuraGraphix,
  ThemeColors,
  ThemeCompositions,
  createStore,
} from '@manyun/dc-brain.aura-graphix';
import { syncCommonDataAction } from '@manyun/dc-brain.state.common';
import { redirectAction } from '@manyun/dc-brain.state.router';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { FuelPipeTypeSelect, TopologyTypesMapper } from '@manyun/monitoring.page.topology-graphix';
import { fetchPointsByCondition } from '@manyun/resource-hub.service.fetch-points-by-condition';
import {
  PipeTypeSelect,
  getTextElementsPropertiesPanel,
  useTextElementsProperties,
} from '@manyun/resource-hub.ui.topology-graphix';

import { Loading } from '@manyun/dc-brain.legacy.components';
import { selectDevicesCache } from '@manyun/dc-brain.legacy.redux/__next/device';
import {
  initializeActionCreator,
  topologyGraphixActions,
} from '@manyun/dc-brain.legacy.redux/actions/topologyGraphixActions';
import { topologyService } from '@manyun/dc-brain.legacy.services';
import * as urlsUtil from '@manyun/dc-brain.legacy.utils/urls';

import { TOPOLOGY_TYPE_TEXT_MAP } from '../topology/constants';
import CustomTools from './custom-tools';
import toTopologyJSON from './utils/transformer/topologyJSON';
import toViewJSON from './utils/transformer/viewJSON';
import * as relatedElementUtil from './utils/uniq-id/relatedElement';

message.config({
  top: 50,
});

function TopologyGraphix({
  isReady,
  id,
  graph,
  resourceCategories,
  deviceTypeMap,
  currentConfig,
  syncCommonData,
  initialize,
  redirect,
  reset,
}) {
  const [, forceUpdate] = useReducer(x => x + 1, 0);

  const storeRef = useRef(null);
  const resetHandler = useCallback(() => {
    reset();
    if (storeRef.current) {
      storeRef.current.deletePages(storeRef.current.pages.map(page => page.id));
      storeRef.current = null;
    }
  }, [reset]);

  const [
    { selectedDeviceType, textElements, pointElements, textPropsConfig },
    { setSelectedDeviceType, setTextPropsConfig },
  ] = useTextElementsProperties(storeRef.current);

  const history = useHistory();
  const { topologyType, idc, block, mode } = useParams();
  const blockGuid = `${idc}.${block}`;

  const devicesCache = useSelector(selectDevicesCache);

  const categories = [...getBaseResourceCategories(topologyType), ...resourceCategories];

  useEffect(() => {
    syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });
  }, [syncCommonData]);

  useEffect(() => {
    initialize({ idc, block, mode, topologyType });

    return () => {
      resetHandler();
    };
  }, [idc, block, mode, topologyType, initialize, resetHandler]);

  useEffect(() => {
    if (!isReady) {
      return;
    }
    const _store = createStore();
    if (env.__DEBUG_MODE__) {
      window.topologyGraphixStore = _store;
    }
    _store.setGetCustomTransformerAnchors(store => {
      const id = store.selectedElementsIds[0];
      if (!id) {
        return {
          rotateEnabled: false,
          enabledAnchors: [],
        };
      }

      const element = store.activePage.findOne(id);
      if (element.custom?.type === 'bus-way') {
        return {
          rotateEnabled: false,
          enabledAnchors: ['middle-left', 'middle-right'],
        };
      } else if (element.custom?.type === 'pipe-network') {
        return {
          rotateEnabled: false,
          enabledAnchors: undefined,
        };
      } else if (element.type === 'text') {
        return {
          rotateEnabled: true,
          enabledAnchors: undefined,
        };
      }

      if (topologyType === TopologyTypesMapper.HVAC) {
        if (element.custom?.type === 'device') {
          return {
            rotateEnabled: true,
            enabledAnchors: [],
          };
        }
      }

      return {
        rotateEnabled: false,
        enabledAnchors: [],
      };
    });
    _store.on('ready', async () => {
      if (graph) {
        await _store.loadJSON({ ...cloneDeep(graph), width: 0, height: 0 });
      } else {
        _store.addPage();
      }
      initializePageConfig(_store.activePage, topologyType);
    });

    storeRef.current = _store;
    forceUpdate();
  }, [topologyType, isReady, graph]);

  const elementAttrsChangeHandler = useCallback((element, changedAttrs) => {
    if (element.custom?.type === 'point-text' && changedAttrs.text) {
      element.set({ custom: { ...element.custom, textTemplate: changedAttrs.text } });
    }
  }, []);

  if (!isReady || !storeRef.current) {
    return <Loading containerStyle={{ height: '100vh' }} description="正在初始化..." />;
  }

  return (
    <ThemeCompositions theme={document.body.dataset.theme === 'dark' ? 'dark' : 'light'}>
      <div
        style={{
          width: '100vw',
          height: '100vh',
          position: 'fixed',
          top: 0,
          right: 0,
          bottom: 0,
          left: 0,
          zIndex: 999,
          background: 'var(--manyun-body-bg)',
        }}
      >
        <AuraGraphix
          debug={env.__DEBUG_MODE__}
          width="100vw"
          height="100vh"
          store={storeRef.current}
          title={`${blockGuid} ${TOPOLOGY_TYPE_TEXT_MAP[topologyType]}`}
          resourceCategories={categories}
          resourceLabelRender={resource => {
            if (Array.isArray(resource?.label)) {
              return {
                renderingLabel: (
                  <>
                    {resource.label.map(item => (
                      <Typography.Text
                        key={item}
                        style={{ textAlign: 'center' }}
                        ellipsis={{ tooltip: item }}
                      >
                        {item}
                      </Typography.Text>
                    ))}
                  </>
                ),
                filteringLabel: resource.label.join(' '),
              };
            }

            const deviceTypeName = deviceTypeMap?.[resource.label /* deviceType */]?.metaName;

            if (!deviceTypeName) {
              return {
                renderingLabel: resource.label,
                filteringLabel: resource.label,
              };
            }

            return {
              renderingLabel: deviceTypeName,
              filteringLabel: deviceTypeName,
            };
          }}
          extraElementPropsRender={(elements, { Panel, BlockProp, Divider }) => {
            const textElementsPropertiesByDeviceType = getTextElementsPropertiesPanel({
              Panel,
              BlockProp,
              Divider,
              store: storeRef.current,
              deviceTypeMap,
              selectedDeviceType,
              textPropsConfig,
              textElements,
              pointElements,
              onDeviceTypeChange: deviceType => {
                setSelectedDeviceType(deviceType);
              },
              onTextPropsConfigChange: config => {
                setTextPropsConfig(prev => ({
                  ...prev,
                  ...config,
                }));
              },
            });

            const element = elements && elements.length === 1 ? elements[0] : undefined;
            const elementType = element?.custom?.type;
            if (
              !['device', 'bus-way', 'pipe-network'].includes(elementType) &&
              element?.type !== 'polyline' &&
              element?.type !== 'line'
            ) {
              return [!elements?.length && textElementsPropertiesByDeviceType].filter(Boolean);
            }

            const customAttrs = element.custom;

            return [
              !elements?.length && textElementsPropertiesByDeviceType,

              elementType === 'device' && (
                <Panel key="device-props" header="设备属性">
                  <BlockProp.Wrapper>
                    <BlockProp.Name>名称</BlockProp.Name>
                    <BlockProp.Value>{customAttrs.name}</BlockProp.Value>
                  </BlockProp.Wrapper>
                  <Divider />
                  <BlockProp.Wrapper>
                    <BlockProp.Name>设备类型</BlockProp.Name>
                    <BlockProp.Value>
                      {deviceTypeMap?.[customAttrs.deviceType]?.metaName}
                      {env.__DEBUG_MODE__ && `(${customAttrs.deviceType})`}
                    </BlockProp.Value>
                  </BlockProp.Wrapper>
                  <Divider />
                  <BlockProp.Wrapper>
                    <BlockProp.Name>包间</BlockProp.Name>
                    <BlockProp.Value>
                      {devicesCache?.[customAttrs.deviceGuid]?.spaceGuid?.roomTag}
                    </BlockProp.Value>
                  </BlockProp.Wrapper>
                </Panel>
              ),

              elementType === 'device' && (
                <Panel key="device-points" header={<span>设备测点</span>}>
                  {customAttrs.visiblePointCodes?.map(pointCode => {
                    const pointTextElementId = relatedElementUtil.join(
                      customAttrs.deviceGuid,
                      pointCode
                    );
                    const pointTextElement =
                      storeRef.current.activePage.findOne(pointTextElementId);

                    if (!pointTextElement) {
                      throw new Error(
                        `[Topology Graphix] Can not find point text element by ID(${pointTextElementId})`
                      );
                    }

                    return (
                      <React.Fragment key={pointCode}>
                        <BlockProp.Wrapper>
                          <BlockProp.Value align="left">
                            {pointTextElement.custom.point.name}
                          </BlockProp.Value>
                          <BlockProp.Name align="right">
                            <MinusCircleOutlined
                              style={{ cursor: 'pointer' }}
                              onClick={() => {
                                element.set({
                                  custom: {
                                    ...customAttrs,
                                    visiblePointCodes: customAttrs.visiblePointCodes.filter(
                                      code => code !== pointCode
                                    ),
                                  },
                                });
                                storeRef.current.deleteElements([pointTextElementId]);
                              }}
                            />
                          </BlockProp.Name>
                        </BlockProp.Wrapper>
                        <Divider />
                      </React.Fragment>
                    );
                  })}
                  <ApiSelect
                    style={{ width: '100%' }}
                    size="small"
                    showSearch
                    optionWithValue
                    value=""
                    disabledOptionValues={customAttrs.visiblePointCodes}
                    dataService={async () => {
                      const { data, error } = await fetchPointsByCondition({
                        deviceType: customAttrs.deviceType,
                        isQueryNon: true,
                        blockGuid: blockGuid,
                      });

                      if (error) {
                        return;
                      }
                      const result = data.data.map(point => point.toApiObject());

                      return result.map(
                        ({ code, dataType, name, pointType, unit, validLimits }) => ({
                          label: name,
                          value: code,
                          custom: {
                            code,
                            name,
                            dataType: dataType.code,
                            pointType: pointType.code,
                            unit,
                            validLimits,
                          },
                        })
                      );
                    }}
                    onChange={(code, { custom }) => {
                      const newPointTextElementId = relatedElementUtil.join(
                        customAttrs.deviceGuid,
                        code
                      );
                      // eslint-disable-next-line no-template-curly-in-string
                      const valueTemplate = '= ${value}' + (custom.unit ? custom.unit : '');
                      const textTemplate = custom.name + valueTemplate;
                      const fontSize = 25;
                      const width =
                        custom.name.length * fontSize + valueTemplate.length * (fontSize / 2);
                      const height = fontSize;
                      const stage = storeRef.current.stageRef.current;
                      const node = stage.findOne(`#${element.id}`);
                      const elementsContainer = stage.findOne('.elements-container');
                      const absolutePosition = node.getAbsolutePosition(elementsContainer);
                      const activePage = storeRef.current.activePage;
                      activePage.addElement(
                        {
                          id: newPointTextElementId,
                          type: 'text',
                          x: absolutePosition.x + element.width + 10,
                          y:
                            absolutePosition.y +
                            ((customAttrs.visiblePointCodes || []).length - 1) * (height + 10),
                          width,
                          height,
                          text: textTemplate,
                          align: 'left',
                          fontSize,
                          rotation: 0,
                          custom: {
                            type: 'point-text',
                            point: {
                              deviceGuid: customAttrs.deviceGuid,
                              ...custom,
                            },
                            textTemplate,
                          },
                        },
                        // We don't want to select the added point text element,
                        // because the user may add more point text element later.
                        false
                      );
                      element.set({
                        custom: {
                          ...customAttrs,
                          visiblePointCodes: [...(customAttrs.visiblePointCodes || []), code],
                        },
                      });
                      activePage.updateRect();
                    }}
                  />
                </Panel>
              ),

              topologyType === TopologyTypesMapper.HVAC &&
                (element.type === 'line' ||
                  element.type === 'polyline' ||
                  elementType === 'pipe-network') && (
                  <Panel key="linetype-config" header="暖通配置">
                    <BlockProp.Wrapper>
                      <BlockProp.Name>水管类型</BlockProp.Name>
                      <BlockProp.Value align="left">
                        <PipeTypeSelect element={element} />
                      </BlockProp.Value>
                    </BlockProp.Wrapper>
                  </Panel>
                ),

              topologyType === TopologyTypesMapper.FUEL_SYSTEM &&
                (element.type === 'line' || element.type === 'polyline') && (
                  <Panel key="linetype-config" header="燃油系统图配置">
                    <BlockProp.Wrapper>
                      <BlockProp.Name>线路类型</BlockProp.Name>
                      <BlockProp.Value align="left">
                        <FuelPipeTypeSelect element={element} />
                      </BlockProp.Value>
                    </BlockProp.Wrapper>
                  </Panel>
                ),

              elementType === 'device' && (
                <Panel key="device-remarks" header="备注">
                  {customAttrs.remarkTextRowKeys?.map(remarkTextRowKey => {
                    const remarkTextElementId = relatedElementUtil.join(
                      customAttrs.deviceGuid,
                      remarkTextRowKey
                    );
                    const remarkTextElement =
                      storeRef.current.activePage.findOne(remarkTextElementId);

                    if (!remarkTextElement) {
                      throw new Error(
                        `[Topology Graphix] Can not find remark text element by ID(${remarkTextElementId})`
                      );
                    }

                    return (
                      <React.Fragment key={remarkTextRowKey}>
                        <BlockProp.Wrapper>
                          {/* <BlockProp.Value align="left"> */}
                          <Input.TextArea
                            defaultValue={remarkTextElement.text}
                            onChange={({ target: { value } }) => {
                              remarkTextElement.set({ text: value });
                            }}
                          />
                          {/* </BlockProp.Value> */}
                          {/* <BlockProp.Name align="right"> */}
                          <MinusCircleOutlined
                            style={{ cursor: 'pointer' }}
                            onClick={() => {
                              element.set({
                                custom: {
                                  ...customAttrs,
                                  remarkTextRowKeys: customAttrs.remarkTextRowKeys.filter(
                                    rowKey => rowKey !== remarkTextRowKey
                                  ),
                                },
                              });
                              storeRef.current.deleteElements([remarkTextElementId]);
                            }}
                          />
                          {/* </BlockProp.Name> */}
                        </BlockProp.Wrapper>
                        <Divider />
                      </React.Fragment>
                    );
                  })}
                  <BlockProp.Wrapper>
                    <PlusCircleOutlined
                      style={{ cursor: 'pointer' }}
                      onClick={() => {
                        const newRemarkTextElementId = relatedElementUtil.generate(
                          customAttrs.deviceGuid
                        );
                        const [, newRemarkTextElementIdRowKey] =
                          relatedElementUtil.split(newRemarkTextElementId);
                        const stage = storeRef.current.stageRef.current;
                        const node = stage.findOne(`#${element.id}`);
                        const elementsContainer = stage.findOne('.elements-container');
                        const absolutePosition = node.getAbsolutePosition(elementsContainer);
                        const activePage = storeRef.current.activePage;
                        activePage.addElement(
                          {
                            id: newRemarkTextElementId,
                            type: 'text',
                            x: absolutePosition.x - 100 - 10,
                            y: absolutePosition.y + element.height / 2,
                            width: 100,
                            height: 25,
                            text: '',
                            fontSize: 25,
                            rotation: 0,
                            custom: {
                              type: 'remark-text',
                            },
                          },
                          false
                        );
                        element.set({
                          custom: {
                            ...customAttrs,
                            remarkTextRowKeys: [
                              ...(customAttrs.remarkTextRowKeys || []),
                              newRemarkTextElementIdRowKey,
                            ],
                          },
                        });
                        activePage.updateRect();
                      }}
                    />
                  </BlockProp.Wrapper>
                </Panel>
              ),
            ].filter(Boolean);
          }}
          CustomTools={CustomTools}
          onElementAttrsChange={elementAttrsChangeHandler}
          onGroupEnd={() => {
            setSelectedDeviceType(undefined);
          }}
          onUngroupEnd={() => {
            setSelectedDeviceType(undefined);
          }}
          onElementAdded={element => {
            setSelectedDeviceType(undefined);
          }}
          onDeleteEnd={elementsJSON => {
            const pendingDeleteIds = [];
            elementsJSON.forEach(elementJSON => {
              const elementType = elementJSON.custom?.type;
              if (['device', 'bus-way'].includes(elementType)) {
                const { deviceGuid, visiblePointCodes, remarkTextRowKeys } = elementJSON.custom;
                const pointTextElementIds = visiblePointCodes?.map(pointCode =>
                  relatedElementUtil.join(deviceGuid, pointCode)
                );
                if ((pointTextElementIds?.length || 0) > 0) {
                  pendingDeleteIds.push(...pointTextElementIds);
                }
                const remarkTextElementIds = remarkTextRowKeys?.map(remarkTextRowKey =>
                  relatedElementUtil.join(deviceGuid, remarkTextRowKey)
                );
                if ((remarkTextElementIds?.length || 0) > 0) {
                  pendingDeleteIds.push(...remarkTextElementIds);
                }
              } else if (['point-text', 'remark-text'].includes(elementType)) {
                const [hostId, childKey] = relatedElementUtil.split(elementJSON.id);
                const hostElement = storeRef.current.activePage.findOne(hostId);
                const prop =
                  elementType === 'point-text' ? 'visiblePointCodes' : 'remarkTextRowKeys';
                if (hostElement) {
                  hostElement.set({
                    custom: {
                      ...hostElement.custom,
                      [prop]: hostElement.custom[prop].filter(key => key !== childKey),
                    },
                  });
                }
              }
            });
            if (pendingDeleteIds.length > 0) {
              storeRef.current.deleteElements(pendingDeleteIds);
            }
            setSelectedDeviceType(undefined);
          }}
          onSave={snapshot => {
            const elements = snapshot.pages[0].children;
            if (elements.length <= 0) {
              message.warn('画布是空的，画点儿什么再保存吧！');
              return;
            }
            const configUtil = new ConfigUtil(currentConfig, { defaultSpaceGuid: blockGuid });
            const topologyJSON = toTopologyJSON(topologyType, elements, devicesCache, configUtil);
            const viewJSON = toViewJSON(topologyType, cloneDeep(snapshot), configUtil);
            saveTopology(id, viewJSON, topologyJSON, idc, block, topologyType, history);
          }}
          onQuitClick={() => {
            resetHandler();
            redirect(urlsUtil.generateBasicResourcesBuildingUrl());
          }}
        />
      </div>
    </ThemeCompositions>
  );
}

const mapStateToProps = ({
  topologyGraphix: { isReady, devicesTree, id, graph },
  common: { deviceCategory },
  config: { configMap },
}) => {
  baseResourceCategories[0].children = devicesTree;

  return {
    isReady,
    id,
    graph,
    resourceCategories: baseResourceCategories,
    deviceTypeMap: deviceCategory?.normalizedList,
    currentConfig: configMap.current,
  };
};

const mapDispatchToProps = {
  syncCommonData: syncCommonDataAction,
  initialize: initializeActionCreator,
  redirect: redirectAction,
  reset: topologyGraphixActions.reset,
};

const ConnectedTopologyGraphix = connect(mapStateToProps, mapDispatchToProps)(TopologyGraphix);

export default function TopologyGraphixEntry() {
  const { topologyType } = useParams();
  const [, { checkCode }] = useAuthorized();

  let authorized = true;
  switch (topologyType) {
    case TopologyTypesMapper.FUEL_SYSTEM:
      authorized = checkCode('page_mutate-infra-topology--FUEL_SYSTEM');
      break;
    default:
      break;
  }

  if (!authorized) {
    return (
      <div
        style={{
          height: 'var(--content-height)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Result status={403} title="403" subTitle="抱歉，您无权新建/编辑燃油系统图" />
      </div>
    );
  }

  return <ConnectedTopologyGraphix />;
}

const busWayName = '母线';
const busWayInitialSize = {
  width: 704,
  height: 8,
};
const baseResourceCategories = [
  {
    key: 'devices',
    icon: <BookOutlined />,
    label: '设备',
    children: [],
  },
];

function getBaseResourceCategories(topologyType) {
  if (topologyType === TopologyTypesMapper.ELECTRIC_POWER || topologyType === 'LIQUID_ELECTRIC') {
    return [
      {
        key: 'edges',
        icon: <FallOutlined />,
        label: '线',
        children: [
          {
            key: 'bus-way',
            copyMax: Number.POSITIVE_INFINITY,
            type: 'bus-way',
            label: busWayName,
            img: '/images/bus-way.png',
            elementConfig: {
              custom: {
                type: 'bus-way',
                name: busWayName,
              },
              type: 'group',
              name: busWayName,
              canUngroup: false,
              allowAddAnchorPoints: true,
              ...busWayInitialSize,
              anchorPointsConfig: [],
              anchorPointsPlacementConfig: [],
              children: [
                {
                  custom: {
                    type: 'bus-way_rect',
                  },
                  type: 'rect',
                  x: 0,
                  y: 0,
                  ...busWayInitialSize,
                  fill: ThemeColors.LineColor,
                  locked: true,
                },
              ],
            },
            isLeaf: true,
          },
        ],
      },
    ];
  } else if (topologyType === TopologyTypesMapper.HVAC) {
    return [
      {
        key: 'nets',
        icon: <NumberOutlined />,
        label: '网',
        children: [
          {
            key: 'pipe-network',
            copyMax: Number.POSITIVE_INFINITY,
            type: 'pipe-network',
            label: '环网',
            img: '/images/topology-graphix/nodes/pipe-network.png',
            isLeaf: true,
            elementConfig: {
              custom: {
                type: 'pipe-network',
                name: '环网',
              },
              type: 'group',
              name: '环网',
              canUngroup: false,
              allowAddAnchorPoints: true,
              width: 400,
              height: 600,
              anchorPointsConfig: [],
              anchorPointsPlacementConfig: [],
              children: [
                {
                  custom: {
                    type: 'pipe-network_rect',
                  },
                  type: 'rect',
                  x: 0,
                  y: 0,
                  width: 400,
                  height: 600,
                  strokeWidth: 12,
                  stroke: ThemeColors.LineColor,
                  locked: true,
                },
              ],
            },
          },
        ],
      },
    ];
  } else {
    return [];
  }
}

function initializePageConfig(page, topologyType) {
  page.setBasePolylineAttrs({
    showArrows: true,
    strokeWidth: 6,
  });
}

async function saveTopology(id, graph, topologyJson, idc, block, topologyType, history) {
  const isCreation = id === null;

  const blockGuid = `${idc}.${block}`;
  const data = {
    viewJson: JSON.stringify(graph),
    topologyJson,
    blocks: [blockGuid],
    topologyType,
  };

  let result;
  if (isCreation) {
    result = await topologyService.saveTopology(data);
  } else {
    result = await topologyService.updateTopology({
      id,
      ...data,
    });
  }

  const { response, error } = result;
  if (response) {
    message.success('保存成功');
    if (isCreation) {
      history.replace(
        urlsUtil.generateTopologyGraphixUrl({ idc, block, topologyType, mode: 'edit' })
      );
    }
  } else {
    message.error(`保存失败：${error}`);
  }
}
