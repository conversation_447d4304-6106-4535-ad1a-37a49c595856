import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import cloneDeep from 'lodash/cloneDeep';

import { createStore } from '@manyun/dc-brain.aura-graphix';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { generateTopologyGraphixRoutePath } from '@manyun/monitoring.route.monitoring-routes';

import { redirectActionCreator } from '@manyun/dc-brain.legacy.redux/actions/routerActions';
import { getFuelSystemGraphAction } from '@manyun/dc-brain.legacy.redux/actions/topologyGraphixActions';
import { selectFuelSystemPreview } from '@manyun/dc-brain.legacy.redux/selectors/topologyGraphixSelectors';

import PreviewWrapper from '../preview';

export function FuelSystemGraphPreview({
  cacheOnly = false,
  highlights = [],
  idc,
  block,
  tenantId,
  hoveringCursor,
  onElementClick,
}) {
  const config = useSelector(selectCurrentConfig);
  const configUtil = React.useMemo(() => new ConfigUtil(config), [config]);
  const typeofFuelTank = configUtil.typeofDeviceGen(ConfigUtil.constants.deviceTypes.FUEL_TANK);
  const typeofFuelDayTank = configUtil.typeofDeviceGen(
    ConfigUtil.constants.deviceTypes.FUEL_DAY_TANK
  );
  const typeofGensetFuelReturnPump = configUtil.typeofDeviceGen(
    ConfigUtil.constants.deviceTypes.GENERATOR_SET_FUEL_RETURN_PUMP
  );
  const typeofGensetFuelSupplyPump = configUtil.typeofDeviceGen(
    ConfigUtil.constants.deviceTypes.GENERATOR_SET_FUEL_SUPPLY_PUMP
  );

  const { loading, code, graph, devices, extraDevices } = useSelector(selectFuelSystemPreview);
  const dispatch = useDispatch();

  const [store, setStore] = React.useState(null);

  const blockGuid = `${idc}.${block}`;

  React.useEffect(() => {
    return () => {
      setStore(null);
    };
  }, []);

  React.useEffect(() => {
    if (!cacheOnly) {
      dispatch(getFuelSystemGraphAction({ idc, block, tenantId }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [idc, block, cacheOnly]);

  React.useEffect(() => {
    let _store;

    if (code === 'success' && !store) {
      _store = createStore();
      /** @type {import('@manyun/dc-brain.aura-graphix').StoreSnapshotIn} */
      const clone = cloneDeep(graph);

      clone.pages[0].children.forEach(elem => {
        if (highlights.includes(elem.id)) {
          if (elem.type === 'group') {
            elem.children.push({
              id: elem.id + '_highlight',
              type: 'highlight',
              width: elem.width,
              height: elem.height,
              locked: true,
            });
          }
        }

        if (elem.custom?.type === 'device') {
          const deviceType = elem.custom?.deviceType;
          if (typeofFuelTank(deviceType)) {
            const widthRatio = elem.width / 513;
            const heightRatio = elem.height / 235;
            /** @type {import('@manyun/dc-brain.aura-graphix').LiquidFillElementSnapshotIn} */
            const liquidFill = {
              id: `${elem.id}_liquid-fill`,
              type: 'liquid-fill',
              x: widthRatio * 80,
              y: heightRatio * 60,
              width: widthRatio * 70,
              height: heightRatio * 110,
              locked: true,
              cornerRadius: 4,
              waterLevel: 0,
              label: {
                fontSize: 10,
              },
              animation: {
                waterLevel: false,
              },
              visible: false,
            };
            elem.children.push(liquidFill);
          } else if (typeofFuelDayTank(deviceType)) {
            /** @type {import('@manyun/dc-brain.aura-graphix').LiquidFillElementSnapshotIn} */
            const liquidFill = {
              id: `${elem.id}_liquid-fill`,
              type: 'liquid-fill',
              x: 2,
              y: 22,
              width: elem.width - 4,
              height: elem.height - 40,
              locked: true,
              cornerRadius: 4,
              background: 'transparent',
              label: {
                fontSize: 22,
              },
              waterLevel: 0,
              animation: {
                waterLevel: false,
              },
              visible: false,
            };
            elem.children.push(liquidFill);
          } else if (
            typeofGensetFuelReturnPump(deviceType) ||
            typeofGensetFuelSupplyPump(deviceType)
          ) {
            const fuelPumpTopologyConfigs = configUtil.getTopologyElementConfig(
              deviceType,
              'FUEL_SYSTEM'
            );
            if (!fuelPumpTopologyConfigs) {
              return;
            }
            const { parts } = fuelPumpTopologyConfigs;
            if (!parts || !Array.isArray(parts) || parts.length <= 0) {
              return;
            }
            // TODO: @jerry move the `ratios` into `tenant configs`
            const xRatio = 153 / 357;
            const yRatio = 326 / 512;
            const pumpWidthRatio = 160 / 357;
            const partsImages = parts.map(({ type, thumbnail, x, y, width, height }, idx) => {
              const customType = `parts_${type}`;
              /** @type {import('@manyun/dc-brain.aura-graphix').ImageElementSnapshotIn} */
              const partsImage = {
                custom: {
                  type: customType,
                },
                id: `${elem.id}_${customType}_${idx}`,
                type: 'image',
                src: thumbnail,
                x: elem.width * xRatio,
                y: elem.height * yRatio,
                width: elem.width * pumpWidthRatio,
                height: elem.width * pumpWidthRatio,
              };

              return partsImage;
            });
            elem.children.push(...partsImages);
          }
        }
      });

      _store.loadJSON(clone).then(() => {
        setStore(_store);
      });
    }

    return () => {
      if (_store) {
        _store.deletePages(_store.pages.map(page => page.id));
        setStore(null);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [code]);

  return (
    <PreviewWrapper
      topologyType="FUEL_SYSTEM"
      moduleId={`${blockGuid}-fuel-system-graph-preview`}
      store={store}
      devices={devices || []}
      extraDevices={[...(extraDevices || []), /* 订阅当前楼栋的实时数据 */ { guid: blockGuid }]}
      loading={loading}
      code={code}
      hoveringCursor={hoveringCursor}
      onRetryBtnClick={() => {
        dispatch(getFuelSystemGraphAction({ idc, block, tenantId }));
      }}
      onCreateBtnClick={() => {
        dispatch(
          redirectActionCreator(
            generateTopologyGraphixRoutePath({
              idc,
              block,
              topologyType: 'FUEL_SYSTEM',
              mode: 'new',
            })
          )
        );
      }}
      onElementClick={onElementClick}
    />
  );
}
