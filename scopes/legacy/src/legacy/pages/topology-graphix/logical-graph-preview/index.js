import React, { useEffect, useImperativeHandle, useState } from 'react';
import { connect } from 'react-redux';

import { cloneDeep } from 'lodash';

import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import { createStore } from '@manyun/dc-brain.aura-graphix';

import { TOPOLOGY_TYPE_KEY_MAP } from '@manyun/dc-brain.legacy.constants';
import { redirectActionCreator } from '@manyun/dc-brain.legacy.redux/actions/routerActions';
import * as urlsUtil from '@manyun/dc-brain.legacy.utils/urls';

import PreviewWrapper from '../preview';

function LogicalGraphPreview({ idc, block, loading, code, graph, devices, redirect }, ref) {
  const [store, setStore] = useState(null);

  useImperativeHandle(ref, () => ({ store }));

  const blockGuid = `${idc}.${block}`;

  useEffect(() => {
    return () => {
      setStore(null);
    };
  }, []);

  useEffect(() => {
    // 因为在此组件销毁时并没有把 `code` 重置为 `null`，
    // 导致这个 `effect` 会执行 2 次
    // 第二次执行时 `graph size` 是 0
    // 会导致 `graph` 无法正常渲染出来
    // 所以这边加强了判断，避免重新 `loadJSON`
    if (code === 'success' && !store) {
      const _store = createStore();
      if (env.__DEBUG__) {
        window.topologyGraphixStore = _store;
      }
      _store.loadJSON(cloneDeep(graph)).then(() => {
        setStore(_store);
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [code]);

  return (
    <PreviewWrapper
      topologyType={TOPOLOGY_TYPE_KEY_MAP.ELECTRIC_POWER}
      moduleId={`${blockGuid}-logical-graph-preview`}
      store={store}
      devices={devices || []}
      loading={loading}
      code={code}
      onRetryBtnClick={() => {}}
      onCreateBtnClick={() => {
        redirect(
          urlsUtil.generateTopologyGraphixUrl({
            idc,
            block,
            topologyType: TOPOLOGY_TYPE_KEY_MAP.ELECTRIC_POWER,
            mode: 'new',
          })
        );
      }}
    />
  );
}

const mapDispatchToProps = {
  redirect: redirectActionCreator,
};

export default connect(null, mapDispatchToProps, null, { forwardRef: true })(
  React.forwardRef(LogicalGraphPreview)
);
