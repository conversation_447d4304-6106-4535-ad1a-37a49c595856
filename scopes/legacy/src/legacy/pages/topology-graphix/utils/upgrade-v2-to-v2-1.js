/**
 * Upgrade data structure V2 -> V2.1
 * @param {import('@/biz-types/topology').v2.TopologyJSON} data V2 data structure
 * @returns {import('@/biz-types/topology').v2.TopologyJSON}
 */

const recursionElement = element => {
  if (element && element.type === 'group') {
    if (element.anchorPointsPlacementConfig.length) {
      element.defaultAnchorPointsPlacementConfig = element.anchorPointsPlacementConfig;
    }

    if (element.children) {
      element.children.forEach(element => recursionElement(element));
    }
  }

  return element;
};

const upgradeV2ToV21 = data => {
  data.pages[0].children.forEach(element => {
    recursionElement(element);
  });

  return data;
};

export default upgradeV2ToV21;
