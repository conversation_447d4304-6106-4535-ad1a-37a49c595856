import shortid from 'shortid';

const DELIMITER = '_$$_';

/**
 * @param {string} hostId The host element ID
 * @returns
 */
export function generate(hostId) {
  const id = shortid();

  return `${hostId}${DELIMITER}${id}`;
}

/**
 * @param {string} hostId The host element ID
 * @param {string} id The related element ID
 * @returns
 */
export function join(hostId, id) {
  return `${hostId}${DELIMITER}${id}`;
}

/**
 * @param {string} fullId The host element ID and the related element ID concatenated together
 * @returns
 */
export function split(fullId) {
  return fullId.split(DELIMITER);
}
