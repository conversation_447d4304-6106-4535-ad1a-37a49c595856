/**
 * 根据设备状态生成对应图片文件名
 * @param {object} statesMapping
 * @param {'ON'|'OFF'|'UNKNOW'} statesMapping.core 核心状态（即 working 表达式的结果）
 * @param {'ON'|'OFF'|'UNKNOW'|'N/A'} [statesMapping.handcart='N/A'] 手车状态
 * @param {'ON'|'OFF'|'UNKNOW'|'N/A'} [statesMapping.protectiveEarth='N/A'] 地刀状态
 * @param {'.png'|'.svg'} [extension='.png'] 文件扩展名
 */
const getDeviceStateImgFilename = (
  { core, handcart = 'N/A', protectiveEarth = 'N/A' },
  extension = '.png'
) => {
  const states = [core, handcart, protectiveEarth];

  // 任意状态未知都展示无数据状态（即离线状态）
  if (states.includes('UNKNOW')) {
    return 'no-data' + extension;
  }

  // 手车、地刀状态为 N/A，表示此设备无手车、地刀，只需参考核心状态即可
  if (handcart === 'N/A' && protectiveEarth === 'N/A') {
    if (states[0] === 'ON') {
      return 'working' + extension;
    } else if (states[0] === 'OFF') {
      return 'not-working' + extension;
    } else {
      return 'no-data' + extension;
    }
  }

  // 去除无用的 N/A 状态，即设备可能只有手车或地刀
  return states.filter(state => state !== 'N/A').join('_n_') + extension;
};

export default getDeviceStateImgFilename;
