import type { EdgeInstance, GroupElementInstance } from '@manyun/dc-brain.aura-graphix';

export function findElementEdges(element: GroupElementInstance) {
  const sources: EdgeInstance[] = [];
  const targets: EdgeInstance[] = [];
  element.edges.forEach(edge => {
    if (edge.linkType === 'source') {
      sources.push(edge);
    } else if (edge.linkType === 'target') {
      targets.push(edge);
    }
  });

  return { sources, targets };
}
