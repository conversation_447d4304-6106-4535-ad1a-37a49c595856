import { flattenTreeData } from '@manyun/dc-brain.aura-graphix';
import { ConfigUtil } from '@manyun/dc-brain.util.config';

import { TOPOLOGY_TYPE_KEY_MAP } from '@manyun/dc-brain.legacy.constants';
import { TOPOLOGY_JSON_NODE_TYPE_MAP } from '@manyun/dc-brain.legacy.pages/topology/constants';

import { getClosestSpaceGuid } from '../get-closest-space-guid';

function getConnectedNodeId(edge, selfLinkType) {
  if (selfLinkType === 'source') {
    return edge.target.id;
  }
  return edge.source.id;
}

/**
 * 解析联络柜的通电条件的表达式，找出测点编码和期望的值
 * 注意：联络柜只允许配置1个DI量点位
 * @param {object} config 配置
 * @param {{ id: string }} extras
 */
function getBtcElectricalPointInfo(config, { id }) {
  const { electricityRelatedPointMap } = config;
  if (
    !electricityRelatedPointMap.connected ||
    !electricityRelatedPointMap.connected.includes('==')
  ) {
    console.warn(`联络柜（${id}）缺少用于判断是否通电的条件表达式!`);
    return null;
  }
  const [pointCodeStr, pointValueStr] = electricityRelatedPointMap.connected.split('==');
  const pointCode = pointCodeStr.replace('${', '').replace('}', '').trim();
  const pointValue = pointValueStr.trim();

  return { pointCode, pointValue };
}

/**
 * @param {string} topologyType
 * @param {any[]} data
 * @param {any} devicesCache
 * @param {ConfigUtil} configUtil
 * @returns
 */
export default function (topologyType, data, devicesCache, configUtil) {
  if (topologyType !== TOPOLOGY_TYPE_KEY_MAP.ELECTRIC_POWER) {
    return {
      nodeList: [],
      relateList: [],
      flowList: [],
    };
  }

  const flatData = flattenTreeData(data);
  const findItem = itemId => flatData.find(({ id }) => id === itemId);
  const findDevice = deviceGuid => devicesCache[deviceGuid];
  const typeofBusTieCabient = deviceType => {
    return [
      ...configUtil.getAllDeviceTypes(ConfigUtil.constants.deviceTypes.HV_BUS_TIE_CABINET),
      ...configUtil.getAllDeviceTypes(ConfigUtil.constants.deviceTypes.LV_BUS_TIE_CABINET),
    ]
      .filter(Boolean)
      .includes(deviceType);
  };
  const typeofHvisoc = deviceType => {
    return configUtil
      .getAllDeviceTypes(ConfigUtil.constants.deviceTypes.HV_ISOLATING_CABINET)
      .includes(deviceType);
  };
  const isHvisocWithBtc = node => {
    if (!typeofHvisoc(node.custom?.deviceType)) {
      return false;
    }

    return node.edges?.some(({ id, linkType }) => {
      const edge = findItem(id);
      const oppositeNodeId = getConnectedNodeId(edge, linkType);
      const oppositeNode = findItem(oppositeNodeId);

      return typeofBusTieCabient(oppositeNode.custom?.deviceType);
    });
  };

  const { nodes, busTieCabinets, edges } = flatData.reduce(
    (acc, item) => {
      const { id, type, custom, edges, source, target } = item;
      const customType = custom?.type;

      // 母线
      if (customType === 'bus-way') {
        const node = {
          guid: id,
          deviceType: customType,
          nodeType: TOPOLOGY_JSON_NODE_TYPE_MAP.NORMAL,
          tag: null,
          isVirtual: true,
          spaceGuid: null,
          extendPosition: null,
          pointCode: null,
          statusValue: null,
        };
        acc.nodes.push(node);

        return acc;
      } else if (['line', 'polyline'].includes(type)) {
        const sourceNode = findItem(source.id);
        const targetNode = findItem(target.id);

        if (
          typeofBusTieCabient(sourceNode.custom?.deviceType) ||
          isHvisocWithBtc(sourceNode) ||
          typeofBusTieCabient(targetNode.custom?.deviceType) ||
          isHvisocWithBtc(targetNode)
        ) {
          return acc;
        }

        const edge = {
          sourceGuid: source.id,
          targetGuid: target.id,
        };
        acc.edges.push(edge);

        return acc;
      } else if (customType === 'device') {
        const device = findDevice(id);
        const node = {
          guid: id,
          deviceType: custom.deviceType,
          nodeType: TOPOLOGY_JSON_NODE_TYPE_MAP.NORMAL,
          tag: null,
          deviceName: device ? device.name : custom.name,
          isVirtual: false,
          spaceGuid: device ? getClosestSpaceGuid(device.spaceGuid) : custom.spaceGuid,
          extendPosition: device ? device.extendPosition : custom.extendPosition,
        };

        // 联络柜
        if (typeofBusTieCabient(custom.deviceType) || isHvisocWithBtc(item)) {
          node.nodeType = TOPOLOGY_JSON_NODE_TYPE_MAP.RELATE;

          const device = findDevice(id);
          const pointInfo = getBtcElectricalPointInfo(
            configUtil.getTopologyElementConfig(custom.deviceType),
            {
              id,
            }
          );
          const busTieCabinet = {
            guid1:
              Array.isArray(edges) && edges.length > 0
                ? getConnectedNodeId(findItem(edges[0].id), edges[0].linkType)
                : null,
            guid2:
              Array.isArray(edges) && edges.length > 1
                ? getConnectedNodeId(findItem(edges[1].id), edges[1].linkType)
                : null,
            pointCode: pointInfo ? pointInfo.pointCode : null,
            statusValue: pointInfo ? pointInfo.pointValue : null,
            spaceGuid: custom.spaceGuid,
            deviceGuid: id,
            deviceType: custom.deviceType,
            deviceTag: null,
            deviceName: device ? device.name : custom.name,
          };
          acc.busTieCabinets.push(busTieCabinet);
        }

        acc.nodes.push(node);

        return acc;
      } else {
        return acc;
      }
    },
    {
      nodes: [],
      busTieCabinets: [],
      edges: [],
    }
  );

  return {
    nodeList: nodes,
    relateList: busTieCabinets,
    flowList: edges,
  };
}
