import { traverse } from '@manyun/dc-brain.aura-graphix';

import { TOPOLOGY_TYPE_KEY_MAP } from '@manyun/dc-brain.legacy.constants';

/**
 * @param {string} topologyType
 * @param {import('@/biz-types/topology').v2.Store} snapshot
 * @param {import('@manyun/dc-brain.legacy.utils/ConfigUtil').default} configUtil
 * @returns
 */
export default function (topologyType, snapshot, configUtil) {
  if (topologyType === TOPOLOGY_TYPE_KEY_MAP.ELECTRIC_POWER) {
    traverse(snapshot.pages[0].children, elem => {
      if (elem.custom?.type !== 'device') {
        return;
      }

      const config = configUtil.getTopologyElementConfig(elem.custom.deviceType, topologyType);

      if (!config) {
        console.warn(`Config not found by device type(${elem.custom.deviceType})!`);
        return;
      }

      // update device group attrs
      elem.width = config.size[0];
      elem.height = config.size[1];
      elem.anchorPointsConfig = config.anchorPointsConfig;
      elem.anchorPointsPlacementConfig = config.anchorPointsPlacementConfig;

      // update device image attrs
      const deviceImageElem = elem.children.find(el => el.custom?.type === 'device/image');
      if (!deviceImageElem) {
        return;
      }
      deviceImageElem.src = config.thumbnail;
      deviceImageElem.width = config.size[0];
      deviceImageElem.height = config.size[1];
    });
  }

  return {
    // 这里保存就是保存2.1版本了
    version: 2.1,
    snapshot,
  };
}
