import { Parser } from 'expr-eval';

import { env } from '@manyun/dc-brain.app-env.react-app-envs';

const parser = new Parser();

// 允许表达式中出现 `NaN`
parser.consts.NaN = Number.NaN;

/**
 * 尝试计算表达式的结果
 *
 * > 在生产环境会 catch `parser` 抛出的异常，避免页面崩溃
 *
 * @param {string} expression
 * @returns {boolean}
 */
export function tryEvaluate(expression) {
  try {
    return parser.evaluate(expression);
  } catch (error) {
    if (env.__DEV_MODE__) {
      // 非生产环境仍抛出异常使页面崩溃
      // 这样更容易发现 BUG
      throw error;
    } else {
      console.error(error);
    }
  }
}

export default parser;
