const {
  ascByY,
  horizontalSort,
  groupByY,
  findAnchorPoint,
  findDeviceElementAndOffets,
} = require('./utils');

/**
 * Perform an ACNL(Auto Connect-aNd-Layout) action on elements:
 *
 * Prerequisites:
 *  - 2-line elements
 *  - elements have the same y considered in the same line
 *
 * Actions:
 * - 1st-line elements stay still
 * - auto-connect 1st-line elements with 2nd-line elements one by one as per their order
 * - auto-reposition 2nd-line elements to make the lines vertical straight
 *
 * @example
 * ```bash
 *  elem1 elem2 elem3 .....
 *    |     |     |     |
 *  elem1 elem2 elem3 .....
 * ```
 *
 * @param {any[]} elements AuraGraphix elements
 * @param {{ spacing: number; addLineElement: Function }} options
 */
function oneByOneThrows(elements, { spacing = 128, addLineElement }) {
  const [parents, children] = ascByY(horizontalSort(groupByY(elements)));

  if (parents.length !== children.length) {
    throw new Error('Expect the same size of parents and children elements!');
  }

  parents.forEach((parent, idx) => {
    const { device: parentDevice, offsets: parentDeviceOffsets } =
      findDeviceElementAndOffets(parent);
    const sourceAnchorPoint = findAnchorPoint(parentDevice, { placement: 'bottom', index: 0 });
    if (sourceAnchorPoint.pointIndex === -1) {
      return;
    }

    const child = children[idx];
    const { device: childDevice, offsets: childDeviceOffsets } = findDeviceElementAndOffets(child);
    const targetAnchorPoint = findAnchorPoint(childDevice, { placement: 'top', index: 0 });
    if (targetAnchorPoint.pointIndex === -1) {
      return;
    }

    const x1 =
      sourceAnchorPoint.point[0] * parentDevice.width + parentDevice.x + parentDeviceOffsets.x;
    const y1 =
      sourceAnchorPoint.point[1] * parentDevice.height + parentDevice.y + parentDeviceOffsets.y;
    const x2 = x1;
    const y2 = y1 + spacing;

    const newChildDevicePos = {
      x: x2 - childDeviceOffsets.x - targetAnchorPoint.point[0] * childDevice.width,
      y: y2 - childDeviceOffsets.y - targetAnchorPoint.point[1] * childDevice.height,
    };
    const childDevicePosDeltas = {
      x: newChildDevicePos.x - childDevice.x,
      y: newChildDevicePos.y - childDevice.y,
    };
    child.set({
      x: child.x + childDevicePosDeltas.x,
      y: child.y + childDevicePosDeltas.y,
    });

    const lineElem = addLineElement({
      points: [x1, y1, x2, y2],
      source: { id: parentDevice.id, linkPointIndex: sourceAnchorPoint.pointIndex },
      target: { id: childDevice.id, linkPointIndex: targetAnchorPoint.pointIndex },
      startPointPlacement: 'bottom',
      endPointPlacement: 'top',
    });
    parentDevice.set({
      edges: [
        ...parentDevice.edges,
        { id: lineElem.id, linkPointIndex: sourceAnchorPoint.pointIndex, linkType: 'source' },
      ],
    });
    childDevice.set({
      edges: [
        ...childDevice.edges,
        { id: lineElem.id, linkPointIndex: targetAnchorPoint.pointIndex, linkType: 'target' },
      ],
    });
  });
}

export default oneByOneThrows;
