import cloneDeep from 'lodash/cloneDeep';

import { getContainerRect, mathUtils as math } from '@manyun/dc-brain.aura-graphix';

/**
 * @typedef BuswayMargin
 * @property {number} [top=128]
 * @property {number} [bottom=128]
 * @property {number} [left=0]
 * @property {number} [right=-32]
 *
 * @typedef Options
 * @property {import('@manyun/dc-brain.aura-graphix').IStore} store
 * @property {BuswayMargin} [buswayMargin]
 * @property {number} elementsSpacing
 * @property {(elementId: string) => any} findElement
 * @property {(elementAttrs: object) => any} addLineElement
 */

/**
 * Perform an `ACNL(Auto Connect-aNd-Layout)` action on elements
 *
 * @example
 * ```bash
 *    elem1  elem2  elem3
 *      |      |      |
 * -------------------------- busway
 *    |     |     |     |
 *  elem1 elem2 elem3 elem4
 * ```
 *
 * @param {any[]} elements AuraGraphix elements
 * @param {Options} options
 */
function top2BottomThrows(
  elements,
  {
    store,
    buswayMargin = { top: 128, bottom: 128, left: 0, right: -32 },
    elementsSpacing = 8,
    findElement,
    addLineElement,
  } = {}
) {
  const layeredElements = ascByY(horizontalSort(groupByY(elements)));

  const { topElements, buswayElement, bottomElements } = findEBEsThrows(layeredElements);
  store.distribute('horizontal', topElements);

  const maxBottomYInTopElements = findMaxBottomY(topElements);

  layoutElements(bottomElements, { direction: 'horizontal', spacing: elementsSpacing });
  // const bottomWidth = calcWidth(bottomElements);

  const { x, width } = getContainerRect([...topElements, ...bottomElements]);
  const buswayWidth = -buswayMargin.left + width + -buswayMargin.right;
  const buswayX = x + buswayMargin.left;
  const buswayY = maxBottomYInTopElements + buswayMargin.top;
  buswayElement.set({ width: buswayWidth, x: buswayX, y: buswayY });
  buswayElement.children[0].set({ width: buswayWidth });

  const bottomElementsY = buswayY + buswayElement.height + buswayMargin.bottom;
  if (bottomElementsY !== bottomElements[0].y) {
    bottomElements.forEach(bottomElem => bottomElem.set({ y: bottomElementsY }));
  }

  connectElements(topElements, [buswayElement], {
    style: 'vertical-straight',
    findElement,
    addLineElement,
  });
  connectElements([buswayElement], bottomElements, {
    style: 'vertical-straight',
    findElement,
    addLineElement,
  });
}

function groupByY(elements) {
  const map = new Map();

  elements.forEach(element => {
    if (map.has(element.y)) {
      const prev = map.get(element.y);
      map.set(element.y, [...prev, element]);
      return;
    }
    map.set(element.y, [element]);
  });

  return map;
}

function horizontalSort(map) {
  [...map.values()].forEach(elements => elements.sort((a, b) => a.x - b.x));

  return map;
}

function ascByY(map) {
  return [...map.keys()].sort((a, b) => a - b).map(key => map.get(key));
}

/**
 * Find the top, bottom elements and the bus-way element between them
 * @param {any[][]} layeredElements
 * @returns
 */
function findEBEsThrows(layeredElements) {
  /**
   * @type {any[]|undefined}
   */
  let topElements;

  /**
   * @type {any[]|undefined}
   */
  let buswayElement;

  /**
   * @type {any[]|undefined}
   */
  let bottomElements;

  layeredElements.forEach((layerElements, idx) => {
    if (layerElements.length === 1 && layerElements[0].custom?.type === 'bus-way') {
      topElements = idx === 0 ? [] : layeredElements[idx - 1];
      buswayElement = layerElements[0];
      bottomElements = idx === layeredElements.length - 1 ? [] : layeredElements[idx + 1];
    }
  });

  if (topElements === undefined) {
    const error = new Error('Top elements not found.');
    error.code = 'top-elements-expected';
    throw error;
  }

  if (buswayElement === undefined) {
    const error = new Error('Bus-way element not found.');
    error.code = 'busway-element-expected';
    throw error;
  }

  if (bottomElements === undefined) {
    const error = new Error('Bottom elements not found.');
    error.code = 'bottom-elements-expected';
    throw error;
  }

  return {
    topElements,
    buswayElement,
    bottomElements,
  };
}

function layoutElements(elements, { direction, spacing }) {
  if (direction === 'horizontal') {
    horizontalLayout(elements, { spacing });
  }
}

function horizontalLayout(elements, { spacing }) {
  elements.forEach((el, idx) => {
    if (idx === 0) {
      return;
    }
    const prevEl = elements[idx - 1];
    const prevElRect = math.getClientRect(prevEl);
    el.set({ x: prevElRect.x + prevElRect.width + spacing });
  });
}

// /**
//  * Calculate horizontal elements width
//  * @param {any[]} elements Elements sort by `x`(ASC)
//  * @returns {number}
//  */
// function calcWidth(elements) {
//   if (elements.length <= 0) {
//     return 0;
//   } else if (elements.length === 1) {
//     return elements[0].width;
//   } else {
//     const leftmostElem = elements[0];
//     const rightmostElem = elements[elements.length - 1];
//     return rightmostElem.x + rightmostElem.width - leftmostElem.x;
//   }
// }

/**
 * Connect `sources` and `targets` one by one
 * @param {any[]} sources
 * @param {any[]} targets
 * @param {{ findElement: (elementId: string) => any; addLineElement: (attrs: any) => any }} options
 */
function connectElements(sources, targets, { findElement, addLineElement } = {}) {
  const firstTargetElem = targets[0];

  if (firstTargetElem.custom?.type === 'bus-way') {
    sources.forEach(deviceElem => {
      const deviceGroupElem =
        deviceElem.custom?.type === 'device'
          ? deviceElem
          : deviceElem.children.find(child => child.custom?.type === 'device');
      if (!deviceGroupElem) {
        return;
      }
      const offsets =
        deviceGroupElem === deviceElem ? { x: 0, y: 0 } : { x: deviceElem.x, y: deviceElem.y };
      const sourceAnchorPoint = findAnchorPoint(deviceGroupElem, {
        placement: 'bottom',
        index: 0,
      });

      const x1 = sourceAnchorPoint.point[0] * deviceGroupElem.width + deviceGroupElem.x + offsets.x;
      const y1 =
        sourceAnchorPoint.point[1] * deviceGroupElem.height + deviceGroupElem.y + offsets.y;
      const x2 = x1;
      const y2 = firstTargetElem.y;

      const edgeOnAnchorPoint = findEdgeOnAnchorPoint(
        deviceGroupElem,
        sourceAnchorPoint.pointIndex
      );
      if (edgeOnAnchorPoint) {
        // Adjust the existing line end point position on the anchor point
        const existingLineElem = findElement(edgeOnAnchorPoint.id);
        existingLineElem.set({ points: [x1, y1, x2, y2] });
        const newAnchorPointsConfig = cloneDeep(firstTargetElem.anchorPointsConfig);
        newAnchorPointsConfig[existingLineElem.target.linkPointIndex] = [
          (x2 - firstTargetElem.x) / firstTargetElem.width,
          0,
        ];
        firstTargetElem.set({ anchorPointsConfig: newAnchorPointsConfig });
      } else {
        const newAnchorPointsConfig = [
          ...firstTargetElem.anchorPointsConfig,
          [(x2 - firstTargetElem.x) / firstTargetElem.width, 0],
        ];
        const newAnchorPointsPlacementConfig = [
          ...firstTargetElem.anchorPointsPlacementConfig,
          'top',
        ];
        const targetLinkPointIndex = newAnchorPointsConfig.length - 1;
        const lineElem = addLineElement({
          points: [x1, y1, x2, y2],
          source: { id: deviceGroupElem.id, linkPointIndex: sourceAnchorPoint.pointIndex },
          target: { id: firstTargetElem.id, linkPointIndex: targetLinkPointIndex },
          startPointPlacement: 'bottom',
          endPointPlacement: 'top',
        });
        deviceGroupElem.set({
          edges: [
            ...deviceGroupElem.edges,
            {
              id: lineElem.id,
              linkPointIndex: sourceAnchorPoint.pointIndex,
              linkType: 'source',
            },
          ],
        });
        firstTargetElem.set({
          edges: [
            ...firstTargetElem.edges,
            { id: lineElem.id, linkPointIndex: targetLinkPointIndex, linkType: 'target' },
          ],
          anchorPointsConfig: newAnchorPointsConfig,
          anchorPointsPlacementConfig: newAnchorPointsPlacementConfig,
        });
      }
    });
  }

  const sourceElem = sources[0];
  if (sources.length === 1 && sourceElem.custom?.type === 'bus-way') {
    targets.forEach(targetElem => {
      const targetDeviceElem =
        targetElem.custom?.type === 'device'
          ? targetElem
          : targetElem.children.find(child => child.custom?.type === 'device');
      if (!targetDeviceElem) {
        return;
      }
      const offsets =
        targetDeviceElem === targetElem ? { x: 0, y: 0 } : { x: targetElem.x, y: targetElem.y };
      const firstTopAnchorPoint = findAnchorPoint(targetDeviceElem, {
        placement: 'top',
        index: 0,
      });
      const x1 =
        firstTopAnchorPoint.point[0] * targetDeviceElem.width + targetDeviceElem.x + offsets.x;
      const y1 = sourceElem.y + sourceElem.height;
      const x2 = x1;
      const y2 =
        firstTopAnchorPoint.point[1] * targetDeviceElem.height + targetDeviceElem.y + offsets.y;
      const edgeOnAnchorPoint = findEdgeOnAnchorPoint(
        targetDeviceElem,
        firstTopAnchorPoint.pointIndex
      );
      if (edgeOnAnchorPoint) {
        // Adjust the existing line end point position on the anchor point
        const existingLineElem = findElement(edgeOnAnchorPoint.id);
        existingLineElem.set({ points: [x1, y1, x2, y2] });
        const newAnchorPointsConfig = cloneDeep(sourceElem.anchorPointsConfig);
        newAnchorPointsConfig[existingLineElem.source.linkPointIndex] = [
          (x2 - sourceElem.x) / sourceElem.width,
          0,
        ];
        sourceElem.set({ anchorPointsConfig: newAnchorPointsConfig });
      } else {
        const newAnchorPointsConfig = [
          ...sourceElem.anchorPointsConfig,
          [(x1 - sourceElem.x) / sourceElem.width, 1],
        ];
        const newAnchorPointsPlacementConfig = [
          ...sourceElem.anchorPointsPlacementConfig,
          'bottom',
        ];
        const sourceLinkPointIndex = newAnchorPointsConfig.length - 1;
        const lineElem = addLineElement({
          points: [x1, y1, x2, y2],
          source: { id: sourceElem.id, linkPointIndex: sourceLinkPointIndex },
          target: { id: targetDeviceElem.id, linkPointIndex: firstTopAnchorPoint.pointIndex },
          startPointPlacement: 'bottom',
          endPointPlacement: 'top',
        });
        sourceElem.set({
          edges: [
            ...sourceElem.edges,
            { id: lineElem.id, linkPointIndex: sourceLinkPointIndex, linkType: 'source' },
          ],
          anchorPointsConfig: newAnchorPointsConfig,
          anchorPointsPlacementConfig: newAnchorPointsPlacementConfig,
        });
        targetDeviceElem.set({
          edges: [
            ...targetDeviceElem.edges,
            {
              id: lineElem.id,
              linkPointIndex: firstTopAnchorPoint.pointIndex,
              linkType: 'target',
            },
          ],
        });
      }
    });
  }
}

function findAnchorPoint(element, { placement = 'top', index = 0 }) {
  let idx = -1;
  for (let i = 0; i < element.anchorPointsPlacementConfig.length; i++) {
    const p = element.anchorPointsPlacementConfig[i];
    if (p === placement) {
      idx = i;
    }
    if (idx === index) {
      break;
    }
  }

  return { pointIndex: idx, point: element.anchorPointsConfig[idx] };
}

function findEdgeOnAnchorPoint(element, anchorPointIndex) {
  return element.edges.find(({ linkPointIndex }) => linkPointIndex === anchorPointIndex);
}

// function hasEdgesOnAchorPoint(element, anchorPointIndex) {
//   return element.edges.some(({ linkPointIndex }) => linkPointIndex === anchorPointIndex);
// }

function findMaxBottomY(elements) {
  return Math.max(...elements.map(elem => elem.y + elem.height));
}

export default top2BottomThrows;
