import cloneDeep from 'lodash/cloneDeep';

/**
 * @typedef Options
 * @property {'ALL'|'SOURCE'|'TARGET'} [filterMode='ALL']
 * @property {(elementId: string) => any} findElement
 */

/**
 *
 * @param {any[]} elements
 * @param {Options} options
 */
function straightenEdgesThrows(elements, { filterMode = 'ALL', findElement } = {}) {
  elements.forEach(element => {
    const newAnchorPointsConfig = cloneDeep(element.toJSON().anchorPointsConfig);
    // eslint-disable-next-line no-unused-expressions
    element.edges?.forEach(edge => {
      const lineElem = findElement(edge.id);

      if (['ALL', 'SOURCE'].includes(filterMode) && edge.linkType === 'source') {
        const endPointX = lineElem.points[lineElem.points.length - 2];
        newAnchorPointsConfig[edge.linkPointIndex] = [
          (endPointX - element.x) / element.width,
          newAnchorPointsConfig[edge.linkPointIndex][1],
        ];
        element.set({ anchorPointsConfig: newAnchorPointsConfig });
      }

      if (['ALL', 'TARGET'].includes(filterMode) && edge.linkType === 'target') {
        const startPointX = lineElem.points[0];
        newAnchorPointsConfig[edge.linkPointIndex] = [
          (startPointX - element.x) / element.width,
          newAnchorPointsConfig[edge.linkPointIndex][1],
        ];
        element.set({ anchorPointsConfig: newAnchorPointsConfig });
      }
    });
  });
}

export default straightenEdgesThrows;
