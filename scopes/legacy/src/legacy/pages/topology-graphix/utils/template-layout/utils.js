import * as relatedElement from './../uniq-id/relatedElement';

export function groupByY(elements, getY = element => element.y) {
  const map = new Map();

  elements.forEach(element => {
    const y = getY(element);
    if (map.has(y)) {
      const prev = map.get(y);
      map.set(y, [...prev, element]);
      return;
    }
    map.set(y, [element]);
  });

  return map;
}

export function groupByBottom(elements) {
  return groupByY(elements, element => element.y + element.height);
}

export function horizontalSort(map) {
  [...map.values()].forEach(elements => elements.sort((a, b) => a.x - b.x));

  return map;
}

export function ascByY(map) {
  return [...map.keys()].sort((a, b) => a - b).map(key => map.get(key));
}

/**
 * Calculate horizontal elements width
 * @param {any[]} elements Elements sort by `x`(ASC)
 * @returns {number}
 */
export function calcWidth(elements) {
  if (elements.length <= 0) {
    return 0;
  } else if (elements.length === 1) {
    return elements[0].width;
  } else {
    const leftmostElem = elements[0];
    const rightmostElem = elements[elements.length - 1];
    return rightmostElem.x + rightmostElem.width - leftmostElem.x;
  }
}

export function findMaxBottomY(elements) {
  return Math.max(...elements.map(elem => elem.y + elem.height));
}

export function findAnchorPoint(element, { placement = 'top', index = 0 }) {
  const placementConfigs = [];
  element.anchorPointsPlacementConfig.forEach((p, i) => {
    if (p === placement) {
      placementConfigs.push({ placement: p, index: i });
    }
  });

  const idx = (placementConfigs[index] || placementConfigs[0]).index;

  return { pointIndex: idx, point: element.anchorPointsConfig[idx] };
}

/**
 * @param {any} element
 * @returns {{ device: any; offsets: null|{x:number;y:number} }}
 */
export function findDeviceElementAndOffets(element) {
  if (isDeviceElement(element)) {
    return {
      device: element,
      offsets: { x: 0, y: 0 },
    };
  }

  if (element.type !== 'group') {
    return {
      device: null,
      offsets: null,
    };
  }

  const device = element.children.find(el => isDeviceElement(el));

  if (!device) {
    return {
      device: null,
      offsets: null,
    };
  }

  return {
    device,
    offsets: {
      x: element.x,
      y: element.y,
    },
  };
}

export function isDeviceElement(element) {
  return element.custom?.type === 'device';
}

/**
 * To check the given element is a text element or not.
 * If provide a `deviceElement`, check the relationship between these 2 elements as well.
 * @param {any} element Text element
 * @param {{ deviceElement: any }} options
 * @returns
 */
export function isDeviceNameTextElement(element, { deviceElement }) {
  const isDeviceName = element.custom?.type === 'remark-text';
  if (!isDeviceName || !deviceElement) {
    return isDeviceName;
  }

  const [deviceGuid] = relatedElement.split(element.id);

  return deviceElement.id === deviceGuid;
}
