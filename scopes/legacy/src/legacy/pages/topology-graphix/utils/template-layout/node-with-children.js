import { generatePolylinePoints } from '@manyun/dc-brain.aura-graphix';

const { ascByY, horizontalSort, groupByBottom, findAnchorPoint } = require('./utils');

/**
 * Perform an `ACNL(Auto Connect-aNd-Layout)` action on elements
 *
 * @example
 * ```bash
 *       node
 *    ___|  |___
 *    |        |
 * child1   child2
 * ```
 *
 * @param {any} elements
 * @param {{ nodeMargin: { top: number; left: number }; addLineElement: Function }} options
 */
function nodeWithChildrenThrows(
  elements,
  { nodeMargin = { top: 128, left: 8 }, addLineElement } = {}
) {
  const [[node], children] = ascByY(horizontalSort(groupByBottom(elements)));

  const childrenWidth = children.reduce((w, child) => child.width + w, 0);
  const firstChildX =
    node.x - (childrenWidth + (children.length - 1) * nodeMargin.left - node.width) / 2;
  const firstChildY = node.y + node.height + nodeMargin.top;
  const nodeDevice =
    node.custom?.type === 'device'
      ? node
      : node.children?.find(child => child.custom?.type === 'device');
  const nodeOffsets = nodeDevice === node ? { x: 0, y: 0 } : { x: node.x, y: node.y };

  children.forEach((child, index) => {
    if (index === 0) {
      child.set({ x: firstChildX, y: firstChildY });
    } else {
      const prevChild = children[index - 1];
      const firstChild = children[0];
      child.set({
        x: prevChild.x + prevChild.width + nodeMargin.left,
        y: firstChildY + firstChild.height - child.height,
      });
    }

    const device =
      child.custom?.type === 'device'
        ? child
        : child.children?.find(child => child.custom?.type === 'device');
    if (!device) {
      return;
    }
    const startPointPlacement = 'bottom';
    const endPointPlacement = 'top';
    const sourceAnchorPoint = findAnchorPoint(nodeDevice, {
      placement: startPointPlacement,
      index,
    });
    const targetAnchorPoint = findAnchorPoint(device, { placement: endPointPlacement, index: 0 });
    const offsets = device === child ? { x: 0, y: 0 } : { x: child.x, y: child.y };
    const x1 = sourceAnchorPoint.point[0] * nodeDevice.width + nodeDevice.x + nodeOffsets.x;
    const y1 = sourceAnchorPoint.point[1] * nodeDevice.height + nodeDevice.y + nodeOffsets.y;
    const x2 = targetAnchorPoint.point[0] * device.width + device.x + offsets.x;
    const y2 = targetAnchorPoint.point[1] * device.height + device.y + offsets.y;
    const polylinePoints = generatePolylinePoints([x1, y1, x2, y2], {
      startPointPlacement,
      endPointPlacement,
    });
    const lineElem = addLineElement({
      points: [
        x1,
        y1,

        x1,
        polylinePoints[3],

        x1,
        polylinePoints[9],

        polylinePoints[6],
        polylinePoints[9],

        x2,
        polylinePoints[9],

        x2,
        y2,
      ],
      source: { id: nodeDevice.id, linkPointIndex: sourceAnchorPoint.pointIndex },
      target: { id: device.id, linkPointIndex: targetAnchorPoint.pointIndex },
      startPointPlacement,
      endPointPlacement,
    });
    nodeDevice.addEdge({
      id: lineElem.id,
      linkPointIndex: sourceAnchorPoint.pointIndex,
      linkType: 'source',
    });
    device.addEdge({
      id: lineElem.id,
      linkPointIndex: targetAnchorPoint.pointIndex,
      linkType: 'target',
    });
  });
}

export default nodeWithChildrenThrows;
