import {
  alitnUtils as align,
  getContainerRect,
  getUngroupElementsSnapshot,
  toDecimalX,
} from '@manyun/dc-brain.aura-graphix';

import { isDeviceElement, isDeviceNameTextElement } from './utils';

/**
 * @param {any[]} elements
 * @param {{ placement: 'top'|'right'|'bottom'|'left'|'right-vertical'|'left-vertical'; getTextWidth: Function; findOneKonvaNode: Function }} options
 */
function deviceNamePlacementThrows(
  elements,
  { placement = 'left-vertical', getTextWidth, findOneKonvaNode }
) {
  elements.forEach(element => {
    const deviceElement = element.children.find(isDeviceElement);
    const deviceNameElement = element.children.find(el =>
      isDeviceNameTextElement(el, { deviceElement })
    );

    if (placement === 'left-vertical') {
      const txtWidth = getTextWidth(deviceNameElement.text, deviceNameElement);
      if (txtWidth) {
        deviceNameElement.set({
          align: 'left',
          width: Math.ceil(txtWidth),
          height: Math.ceil(deviceNameElement.fontSize),
        });
      }
      if (!(deviceNameElement.rotation === -90 || deviceNameElement.rotation === 270)) {
        deviceNameElement.set({
          x: deviceElement.x - deviceNameElement.height - 4,
          // move device name text element higher than device element
          // so that device element could keep its position
          y: -deviceNameElement.width,
          rotation: 270,
        });
      }
      const children = [deviceElement, deviceNameElement];
      align.alignBottomEdges(children);
      const ungroupChildrenSnapshot = getUngroupElementsSnapshot(element, { findOneKonvaNode });
      const groupRect = getContainerRect(ungroupChildrenSnapshot);
      element.set(groupRect);
      ungroupChildrenSnapshot.forEach(childSnapshot => {
        const childElement = element.children.find(elem => elem.id === childSnapshot.id);
        childElement.set({
          x: toDecimalX(childSnapshot.x - groupRect.x, 0),
          y: toDecimalX(childSnapshot.y - groupRect.y, 0),
        });
      });
    }
  });
}

export default deviceNamePlacementThrows;
