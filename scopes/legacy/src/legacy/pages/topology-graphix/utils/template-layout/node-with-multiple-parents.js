import { generatePolylinePoints } from '@manyun/dc-brain.aura-graphix';

const {
  ascByY,
  horizontalSort,
  groupByY,
  calcWidth,
  findMaxBottomY,
  findAnchorPoint,
} = require('./utils');

/**
 * Perform an `ACNL(Auto Connect-aNd-Layout)` action on elements
 *
 * @example
 * ```bash
 * parent1  parent2
 *    |        |
 *    |___  ___|
 *       |  |
 *       node
 * ```
 *
 * @param {any} elements
 * @param {{ nodeMargin: { top: number }; addLineElement: Function }} options
 */
function nodeWithMultipleParentsThrows(
  elements,
  { nodeMargin = { top: 128 }, addLineElement } = {}
) {
  const [parents, [node]] = ascByY(horizontalSort(groupByY(elements)));

  const parentsWidth = calcWidth(parents);
  const nodeX = parents[0].x + (parentsWidth - node.width) / 2;
  const nodeY = findMaxBottomY(parents) + nodeMargin.top;
  node.set({ x: nodeX, y: nodeY });
  const nodeDevice =
    node.custom?.type === 'device'
      ? node
      : node.children?.find(child => child.custom?.type === 'device');
  const nodeOffsets = nodeDevice === node ? { x: 0, y: 0 } : { x: node.x, y: node.y };

  parents.forEach((parent, index) => {
    const device =
      parent.custom?.type === 'device'
        ? parent
        : parent.children?.find(child => child.custom?.type === 'device');
    if (!device) {
      return;
    }
    const startPointPlacement = 'bottom';
    const endPointPlacement = 'top';
    const sourceAnchorPoint = findAnchorPoint(device, { placement: startPointPlacement, index });
    const targetAnchorPoint = findAnchorPoint(nodeDevice, { placement: endPointPlacement, index });
    const offsets = device === parent ? { x: 0, y: 0 } : { x: parent.x, y: parent.y };
    const x1 = sourceAnchorPoint.point[0] * device.width + device.x + offsets.x;
    const y1 = sourceAnchorPoint.point[1] * device.height + device.y + offsets.y;
    const x2 = targetAnchorPoint.point[0] * nodeDevice.width + nodeDevice.x + nodeOffsets.x;
    const y2 = targetAnchorPoint.point[1] * nodeDevice.height + nodeDevice.y + nodeOffsets.y;
    const polylinePoints = generatePolylinePoints([x1, y1, x2, y2], {
      startPointPlacement,
      endPointPlacement,
    });
    const lineElem = addLineElement({
      points: [
        x1,
        y1,

        x1,
        polylinePoints[3],

        x1,
        polylinePoints[9],

        polylinePoints[6],
        polylinePoints[9],

        x2,
        polylinePoints[9],

        x2,
        y2,
      ],
      source: { id: device.id, linkPointIndex: sourceAnchorPoint.pointIndex },
      target: { id: nodeDevice.id, linkPointIndex: targetAnchorPoint.pointIndex },
      startPointPlacement,
      endPointPlacement,
    });
    device.addEdge({
      id: lineElem.id,
      linkPointIndex: sourceAnchorPoint.pointIndex,
      linkType: 'source',
    });
    nodeDevice.addEdge({
      id: lineElem.id,
      linkPointIndex: targetAnchorPoint.pointIndex,
      linkType: 'target',
    });
  });
}

export default nodeWithMultipleParentsThrows;
