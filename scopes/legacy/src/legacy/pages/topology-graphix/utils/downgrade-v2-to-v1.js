import { flattenTreeData } from '@manyun/dc-brain.aura-graphix';

import * as relatedElementUtil from './uniq-id/relatedElement';

/**
 * 拓扑数据 V2 降级到 V1
 * @deprecated 现已将拓扑数据按 V2 的结构保存，无需降级了
 * @param {import('@/biz-types/topology').v2.TopologyJSON} snapshot
 * @returns {import('@/biz-types/topology').v1.TopologyJSON}
 */
const downgradeV2ToV1 = snapshot => {
  /**
   * @type {import('@/biz-types/topology').v2.PageChildren}
   */
  const flatData = flattenTreeData(snapshot.pages[0].children);

  /**
   * @type {import('@/biz-types/topology').v1.TopologyJSON['nodes']}
   */
  const nodes = flatData.map(element => {
    if (element.type === 'polyline') {
      return {
        abstractType: 'wire',
        shape: 'polyline',
        id: element.id,
        points: element.points,
        source: element.source,
        target: element.target,
        draggable: false,
        startPointPlacement: element.startPointPlacement,
        endPointPlacement: element.endPointPlacement,
      };
    }

    /**
     * @type {'device'|'bus-way'|'remark-text'|'point-text'|undefined}
     */
    const customType = element.custom?.type;

    if (customType === 'device') {
      let x = element.x;
      if (element.custom.__deprecated_shape === 'RPP') {
        x = element.x - 25;
      } else if (element.custom.__deprecated_shape === 'transformer') {
        x = element.x - 10;
      }
      // 变压器检修隔离柜
      if (element.custom.deviceType === '10107') {
        x = element.x - 32;
      }

      return {
        id: element.id,
        abstractType: 'device',
        shape: element.custom.__deprecated_shape,
        img: element.custom.__deprecated_img,
        nodeType: element.custom.deviceType,
        x,
        y: element.y,
        hovering: false,
        magnifiedAnchorPointIndexes: [],
        anchorPointsConfig: element.anchorPointsConfig,
        anchorPoints: undefined,
        draggable: true,
        width: element.width,
        height: element.height,
        rotation: element.rotation,
        edges: element.edges,
        groupId: undefined,

        // device node specific attrs
        spaceGuid: element.custom.spaceGuid,
        extendPosition: element.custom.extendPosition,
        tag: null,
        name: element.custom.name,
        remarkTexts: element.custom.remarkTextRowKeys?.map(rowKey => {
          const remarkTextElementId = relatedElementUtil.join(element.custom.deviceGuid, rowKey);
          const remarkTextElementJSON = flatData.find(
            element => element.id === remarkTextElementId
          );

          return {
            rowKey,
            inserting: true,
            value: remarkTextElementJSON.text,
          };
        }),
        visiblePoints: element.custom.visiblePointCodes?.map(pointCode => {
          const pointTextElementId = relatedElementUtil.join(element.custom.deviceGuid, pointCode);
          const pointTextElementJSON = flatData.find(element => element.id === pointTextElementId);

          return {
            ...pointTextElementJSON?.custom.point,
            inserting: false,
            rowKey: pointCode,
          };
        }),
      };
    } else if (customType === 'bus-way') {
      return {
        abstractType: 'wire',
        shape: 'bus-line',
        nodeType: 'bus-line',
        id: element.id,
        x: element.x,
        y: element.y,
        width: element.width,
        height: element.height,
        rotation: element.rotation,
        scaleX: 1,
        scaleY: 1,
        edges: element.edges,
        hovering: false,
        anchorPointsConfig: element.anchorPointsConfig,
        draggable: true,
        fill: element.children[0].fill,
        groupId: undefined,
      };
    } else if (customType === 'remark-text') {
      return {
        nodeType: 'remark-text',
        text: element.text,
        shape: 'text',
        abstractType: 'text',
        fontSize: element.fontSize,
        draggable: true,
        color: element.stroke,
        backgroundColor: 'transparent',
        id: element.id,
        x: element.x,
        y: element.y,
        width: element.width,
        height: element.height,
        rotation: element.rotation,
        padding: 0,
        textAlign: 'left',
        groupId: undefined,
      };
    } else if (customType === 'point-text') {
      return {
        nodeType: 'point-text',
        textTemplate: element.custom.textTemplate,
        point: {
          ...element.custom.point,
          rowKey: element.custom.point.code,
        },
        shape: 'text',
        abstractType: 'text',
        fontSize: element.fontSize,
        draggable: true,
        color: element.stroke,
        backgroundColor: 'transparent',
        id: element.id,
        x: element.x,
        y: element.y,
        width: element.width,
        height: element.height,
        rotation: element.rotation,
        padding: 0,
        textAlign: 'left',
        groupId: undefined,
      };
    }

    console.log(element);
    throw new Error(`Can not downgrade element(${element.id})`);
  });

  return {
    groups: [],
    nodes,
  };
};

export default downgradeV2ToV1;
