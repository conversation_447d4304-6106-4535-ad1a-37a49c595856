/**
 * @typedef {number|[number, number]} AnchorPointCountConfig
 * @typedef {{ top: AnchorPointCountConfig; bottom: AnchorPointCountConfig }} AnchorPointCountsConfig
 * @typedef {{ width: number }} NodeSize
 */

/**
 * @param {AnchorPointCountsConfig} counts To indicate the top & bottom anchor point's count(must great than 0)
 *   - `top: 1`: generate 1 point on the top side
 *   - `top: [1, 5]` generate 1 point every 5px on the top side
 * @param {NodeSize} [size] Node's size
 * @returns
 */
export function generate({ top, bottom }, size) {
  /**
   * @type {[number, number][]}
   */
  const anchorPointsConfig = [];
  /**
   * @type {('top'|'bottom')[]}
   */
  const anchorPointsPlacementConfig = [];

  if (Array.isArray(top)) {
    const interval = top[1] / top[0];
    const count = Math.floor((size.width - interval) / interval);
    anchorPointsConfig.push(
      ...new Array(count).fill(null).map((_value, idx) => [((idx + 1) * interval) / size.width, 0])
    );
    anchorPointsPlacementConfig.push(...new Array(count).fill(null).map(() => 'top'));
  } else {
    anchorPointsConfig.push(
      ...new Array(top).fill(null).map((_value, idx) => [(idx + 1) * (1 / (top + 1)), 0])
    );
    anchorPointsPlacementConfig.push(...new Array(top).fill(null).map(() => 'top'));
  }

  if (Array.isArray(bottom)) {
    const interval = bottom[1] / bottom[0];
    const count = Math.floor((size.width - interval) / interval);
    anchorPointsConfig.push(
      ...new Array(count).fill(null).map((_value, idx) => [((idx + 1) * interval) / size.width, 1])
    );
    anchorPointsPlacementConfig.push(...new Array(count).fill(null).map(() => 'bottom'));
  } else {
    anchorPointsConfig.push(
      ...new Array(bottom).fill(null).map((_value, idx) => [(idx + 1) * (1 / (bottom + 1)), 1])
    );
    anchorPointsPlacementConfig.push(...new Array(bottom).fill(null).map(() => 'bottom'));
  }

  return {
    anchorPointsConfig,
    anchorPointsPlacementConfig,
  };
}

/**
 * Split the anchor points config by placement.
 * @param {{ anchorPointsConfig: number[][]; anchorPointsPlacementConfig: string[] }} configs The anchor points configs
 * @returns {{ tops: number[][]; bottoms: number[][] }}
 */
export function split({ anchorPointsConfig, anchorPointsPlacementConfig }) {
  const tops = [];
  const bottoms = [];
  anchorPointsPlacementConfig.forEach((placement, idx) => {
    const anchorPoint = anchorPointsConfig[idx];
    if (!anchorPoint) {
      return;
    }
    if (placement === 'top') {
      tops.push(anchorPoint);
    } else if (placement === 'bottom') {
      bottoms.push(anchorPoint);
    }
  });

  return { tops, bottoms };
}
