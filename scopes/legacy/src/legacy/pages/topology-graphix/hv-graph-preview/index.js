import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLatest } from 'react-use';

import cloneDeep from 'lodash.clonedeep';

import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import { createStore } from '@manyun/dc-brain.aura-graphix';
import { redirectAction } from '@manyun/dc-brain.state.router';
import { generateTopologyGraphixRoutePath } from '@manyun/monitoring.route.monitoring-routes';
import { getBlockHVGraphAction, selectBlockHVGraph } from '@manyun/monitoring.state.topology';

import PreviewWrapper from '../preview';

function HVGraphPreview({
  cacheOnly,
  highlights = [],
  idc,
  block,
  tenantId,
  hoveringCursor,
  onElementClick,
  topologyType = 'ELECTRIC_POWER',
}) {
  const blockGuid = React.useMemo(() => `${idc}.${block}`, [idc, block]);

  const dispatch = useDispatch();

  const { loading, code, graph, custom } = useSelector(selectBlockHVGraph(blockGuid, topologyType));

  const [store, setStore] = React.useState(null);

  React.useEffect(() => {
    if (!cacheOnly) {
      dispatch(getBlockHVGraphAction({ idc, block, tenantId, topologyType }));
    }

    return () => {
      setStore(null);
    };
  }, [idc, block, cacheOnly, dispatch, tenantId, topologyType]);

  const _highlights = useLatest(highlights);
  const __store = useLatest(store);
  React.useEffect(() => {
    let _store;

    // 因为在此组件销毁时并没有把 `code` 重置为 `null`，
    // 导致这个 `effect` 会执行 2 次
    // 第二次执行时 `graph size` 是 0
    // 会导致 `graph` 无法正常渲染出来
    // 所以这边加强了判断，避免重新 `loadJSON`
    if (code === 'success' && !__store.current) {
      _store = createStore();
      if (env.__DEBUG__) {
        window.topologyGraphixStore = _store;
      }

      const clone = cloneDeep(graph);

      if (_highlights.current.length > 0) {
        clone.pages[0].children.forEach(elem => {
          if (_highlights.current.includes(elem.id)) {
            if (elem.type === 'group') {
              elem.children.push({
                id: elem.id + '_highlight',
                type: 'highlight',
                width: elem.width,
                height: elem.height,
                locked: true,
              });
            }
          }
        });
      }

      _store.loadJSON(clone).then(() => {
        setStore(_store);
      });
    }
  }, [code, graph, _highlights, __store]);

  return (
    <PreviewWrapper
      topologyType="ELECTRIC_POWER"
      moduleId={`${blockGuid}-hv-graph-preview`}
      store={store}
      devices={custom?.devices || []}
      loading={loading}
      code={code}
      hoveringCursor={hoveringCursor}
      onRetryBtnClick={() => {
        dispatch(getBlockHVGraphAction({ idc, block, tenantId }));
      }}
      onCreateBtnClick={() => {
        dispatch(
          redirectAction(
            generateTopologyGraphixRoutePath({
              idc,
              block,
              topologyType,
              mode: 'new',
            })
          )
        );
      }}
      onElementClick={onElementClick}
    />
  );
}

export default HVGraphPreview;
