import React, { Component } from 'react';
import { connect } from 'react-redux';

import QuestionCircleOutlined from '@ant-design/icons/es/icons/QuestionCircleOutlined';
import { saveAs } from 'file-saver';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';

import { Upload } from '@manyun/dc-brain.ui.upload';

import { GutterWrapper, StatusText, TinyCard, TinyTable } from '@manyun/dc-brain.legacy.components';
import * as urls from '@manyun/dc-brain.legacy.constants/urls';
import { redirectActionCreator } from '@manyun/dc-brain.legacy.redux/actions/routerActions';
import { spareActions } from '@manyun/dc-brain.legacy.redux/actions/spareActions';
import { spareService } from '@manyun/dc-brain.legacy.services';

const columns = ctx => [
  {
    title: '行号',
    dataIndex: 'rowTag',
    fixed: 'left',
    render: (tag, { errMessage, rowTag }) => getToolTilp(rowTag, errMessage, 'rowTag'),
  },
  {
    title: '机房',
    dataIndex: ['errDto', 'idcTag'],
    render: (idcTag, { errMessage }) => getToolTilp(idcTag, errMessage, 'idcTag'),
  },
  {
    title: '楼栋',
    dataIndex: ['errDto', 'blockTag'],
    render: (blockTag, { errMessage }) => getToolTilp(blockTag, errMessage, 'blockTag'),
  },
  {
    title: '包间',
    dataIndex: ['errDto', 'roomTag'],
    render: (roomTag, { errMessage }) => getToolTilp(roomTag, errMessage, 'roomTag'),
  },
  {
    title: '货架',
    dataIndex: ['errDto', 'shelves'],
    render: (shelves, { errMessage }) => getToolTilp(shelves, errMessage, 'shelves'),
  },
  {
    title: '厂商',
    dataIndex: ['errDto', 'vendor'],
    render: (vendor, { errMessage }) => getToolTilp(vendor, errMessage, 'vendor'),
  },
  {
    title: '型号',
    dataIndex: ['errDto', 'productModel'],
    render: (productModel, { errMessage }) => getToolTilp(productModel, errMessage, 'productModel'),
  },
  {
    title: '三级分类',
    dataIndex: ['errDto', 'spareType'],
    render: (spareType, { errMessage }) => getToolTilp(spareType, errMessage, 'spareType'),
  },
  {
    title: '供应商简称',
    dataIndex: ['errDto', 'supplyVendor'],
    render: (supplyVendor, { errMessage }) => getToolTilp(supplyVendor, errMessage, 'supplyVendor'),
  },
  {
    title: '数量',
    dataIndex: ['errDto', 'totalNum'],
    render: (productModel, { errMessage }) => getToolTilp(productModel, errMessage, 'totalNum'),
  },
];

class CreateSpare extends Component {
  state = {
    selectedRowKeys: [],
    selectedRows: [],
    importButtonLoading: false,
    pageNo: 1,
    pageSize: 10,
  };

  onSelectChange = (selectedRowKeys, selectedRows) => {
    this.setState({ selectedRowKeys, selectedRows });
  };

  downloadDemo = async () => {
    const { response, filename = 'template.csv' } = await spareService.downloadSpareModel();
    if (!response) {
      return;
    }
    saveAs(response, filename);
  };

  beforeUpload = (file, fileList) => {
    this.props.importSpareSuccess({});
    const fd = new FormData();
    fd.append('file', file);
    this.loadFetchData(fd);
    return false;
  };

  loadFetchData = async fd => {
    this.setState({
      importButtonLoading: true,
    });
    const { response, error } = await spareService.uploadSpare(fd);
    this.props.startImportLoading();
    if (error) {
      message.error(error);
      this.props.stopImportLoading();
      this.setState({
        importButtonLoading: false,
      });
      return;
    }
    if (response) {
      this.props.stopImportLoading();
      this.setState({
        importButtonLoading: false,
      });
      if (response.faultTotal === 0) {
        message.success('导入成功！');
        this.props.redirect(urls.SPARE_LIST);
      } else {
        this.props.importSpareSuccess(response);
      }
    }
  };

  deleteEquip = (row, index) => {
    const { importSpareList } = this.props;
    const list = [...importSpareList.list];
    list.splice(index, 1);
    this.props.changedatalist({
      data: list,
      total: list.length,
    });
  };

  onChangePage = (pageNo, pageSize) => {
    this.setState({
      pageNo,
      pageSize,
    });
  };

  render() {
    const { importLoading, importSpareList } = this.props;
    const { importButtonLoading, pageNo, pageSize } = this.state;
    return (
      <>
        <TinyCard>
          <TinyTable
            rowKey={({ errDto }) => errDto.guid}
            actionsWrapperStyle={{ justifyContent: 'space-between' }}
            actions={[
              <GutterWrapper key="left" flex>
                <Upload
                  key="1"
                  beforeUpload={this.beforeUpload}
                  showUploadList={false}
                  accept=".csv,.xls,.xlsx"
                >
                  <Button type="primary" loading={importButtonLoading}>
                    导入
                  </Button>
                </Upload>

                <Button key="2" onClick={this.downloadDemo}>
                  下载模板
                </Button>
              </GutterWrapper>,
              <div key="right">
                {importSpareList.excelCheckErrDtos && (
                  <GutterWrapper flex>
                    <StatusText status="normal">
                      导入总数&nbsp;{importSpareList.checkTotal}
                    </StatusText>
                    <StatusText status="normal">
                      正确总数&nbsp;{importSpareList.correctTotal}
                    </StatusText>
                    <StatusText status="alarm">
                      错误行数&nbsp;{importSpareList.faultTotal}
                    </StatusText>
                  </GutterWrapper>
                )}
              </div>,
            ]}
            dataSource={importSpareList.excelCheckErrDtos}
            loading={importLoading}
            columns={columns(this)}
            scroll={{ x: true }}
            pagination={{
              total: importSpareList.excelCheckErrDtos
                ? importSpareList.excelCheckErrDtos.length
                : 0,
              current: pageNo,
              pageSize: pageSize,
              onChange: this.onChangePage,
            }}
          />
        </TinyCard>
      </>
    );
  }
}

const mapStateToProps = ({ spareManage: { importLoading, importSpareList } }) => ({
  importLoading,
  importSpareList,
});

const mapDispatchToProps = {
  changedatalist: spareActions.importSpareSuccess,
  startImportLoading: spareActions.importLoading,
  importSpareSuccess: spareActions.importSpareSuccess,
  stopImportLoading: spareActions.failure,
  redirect: redirectActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(CreateSpare);

function getToolTilp(value, errMessage, dataType) {
  if (Object.prototype.hasOwnProperty.call(errMessage, dataType)) {
    return (
      <Tooltip title={errMessage[dataType]}>
        <GutterWrapper size="2px" flex center>
          <StatusText status="alarm" style={{ display: 'inline-block' }}>
            {value ? value : '--'}
          </StatusText>
          <QuestionCircleOutlined />
        </GutterWrapper>
      </Tooltip>
    );
  }
  return value;
}
