import Form from '@ant-design/compatible/es/form';
import { get } from 'lodash';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { Typography } from '@manyun/base-ui.ui.typography';
import { VendorModelSelect } from '@manyun/crm.ui.vendor-model-select';
import { VendorSelect } from '@manyun/crm.ui.vendor-select';

import { LocationCascader, TinyDrawer } from '@manyun/dc-brain.legacy.components';
import { editSpare, spareActions } from '@manyun/dc-brain.legacy.redux/actions/spareActions';
import { getPixelPercentageWidth, getSpaceGuidMap } from '@manyun/dc-brain.legacy.utils';

class EditSpareMess extends Component {
  state = {
    editBasicInfoVisible: false,
  };

  closeEditBasic = () => {
    this.setState({ editBasicInfoVisible: false });
  };

  onVisible = async () => {
    this.setState({ editBasicInfoVisible: true });
  };

  handleSubmit = (event, form) => {
    event.preventDefault();
    const id = this.props.basicInfo.id;
    this.props.form.validateFields(async (err, values) => {
      const data = this.props.form.getFieldsValue();
      const { vendorAndProductModel, location, totalNum, shelves, supplyVendor } = data;
      if (err) {
        return;
      }
      if (!err) {
        this.props.editSpare({
          id,
          newVendor: vendorAndProductModel[0],
          newProductModel: vendorAndProductModel[1],
          newIdcTag: location[0],
          newBlockGuid: location[0] + '.' + location[1],
          newRoomTag: location[2],
          shelves: shelves ? shelves : null,
          supplyVendor: supplyVendor && supplyVendor.length > 0 ? supplyVendor.join() : null,
          totalNum,
          callback: () => {
            this.closeEditBasic();
          },
        });
      }
    });
  };

  render() {
    const form = this.props.form;
    const { getFieldDecorator } = form;
    const { text, type, style, editBasicInfoLoading, basicInfo, deviceTypeMap } = this.props;

    return (
      <>
        <Button style={style} type={type} onClick={this.onVisible}>
          {text}
        </Button>

        <TinyDrawer
          width={getPixelPercentageWidth(554)}
          title={<Typography.Title level={4}>编辑基本信息</Typography.Title>}
          destroyOnClose
          open={this.state.editBasicInfoVisible}
          submitButtonLoading={editBasicInfoLoading}
          onClose={this.closeEditBasic}
          onCancel={this.closeEditBasic}
          onSubmit={this.handleSubmit}
        >
          <Form layout="vertical" onSubmit={this.handleSubmit}>
            <Form.Item label="位置">
              {getFieldDecorator('location', {
                rules: [
                  {
                    required: true,
                    message: '机房、楼栋、包间必选！',
                    type: 'number',
                    transform: value => (value?.length === 3 ? 2 : false),
                  },
                ],

                initialValue: [
                  basicInfo.idcTag,
                  getSpaceGuidMap(basicInfo.blockGuid).block,
                  basicInfo.roomTag,
                ],
              })(<LocationCascader changeOnSelect={false} showRoom currentAuthorize />)}
            </Form.Item>
            <Form.Item label="货架">
              {getFieldDecorator('shelves', {
                initialValue: basicInfo.shelves,
                rules: [
                  {
                    max: 50,
                    message: '最多输入 50 个字符！',
                  },
                  {
                    pattern: /^[0-9a-zA-Z\u4e00-\u9fa5\\——\-、/\\]+$/,
                    message: '必须是数字、字母、中文，符号- —— 、/ \\',
                  },
                ],
              })(<Input />)}
            </Form.Item>
            <Form.Item label="厂商、型号">
              {getFieldDecorator('vendorAndProductModel', {
                rules: [
                  {
                    required: true,
                    message: '厂商、型号必选！',
                    type: 'number',
                    transform: value => (value?.length === 2 ? 2 : false),
                  },
                ],
                initialValue: [basicInfo.vendor, basicInfo.productModel],
              })(
                <VendorModelSelect
                  style={{ width: '50%' }}
                  deviceType={basicInfo.spareType}
                  allowClear
                />
              )}
            </Form.Item>

            <Form.Item label="数量">
              {getFieldDecorator('totalNum', {
                rules: [{ required: true, message: '数量必填' }],
                initialValue: basicInfo.totalNum,
              })(<InputNumber style={{ width: 120 }} min={0} precision={0} max={999999999} />)}
            </Form.Item>

            <Form.Item label="资产分类">
              {getFieldDecorator('categoryType', {
                initialValue:
                  get(deviceTypeMap, [basicInfo.topCategory, 'metaName'], basicInfo.topCategory) +
                  ' > ' +
                  get(
                    deviceTypeMap,
                    [basicInfo.secondCategory, 'metaName'],
                    basicInfo.secondCategory
                  ) +
                  ' > ' +
                  get(deviceTypeMap, [basicInfo.spareType, 'metaName'], basicInfo.spareType),
              })(<Input disabled />)}
            </Form.Item>
            <Form.Item label="供应商简称">
              {getFieldDecorator('supplyVendor', {
                rules: [{ required: true, message: '供应商简称必填' }],
                initialValue:
                  typeof basicInfo.supplyVendor === 'string' && basicInfo.supplyVendor
                    ? basicInfo.supplyVendor.split(',').filter(item => item !== '')
                    : [],
              })(<VendorSelect mode="multiple" showArrow />)}
            </Form.Item>
          </Form>
        </TinyDrawer>
      </>
    );
  }
}

const mapStateToProps = ({
  spareManage: { editBasicInfoLoading },
  common: { deviceCategory },
}) => ({
  editBasicInfoLoading,
  deviceTypeMap: get(deviceCategory, 'normalizedList'),
});

const mapDispatchToProps = {
  visible: spareActions.editBasicInfoVisible,
  editSpare,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(Form.create({ name: 'edit_spare_basic_info' })(EditSpareMess));
