import React, { Component } from 'react';
import { connect, useDispatch } from 'react-redux';

import { FiltersForm, Form } from '@galiojs/awesome-antd';
import get from 'lodash/get';
import trim from 'lodash/trim';

import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Divider } from '@manyun/base-ui.ui.divider';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { Typography } from '@manyun/base-ui.ui.typography';
import { toCSVBlob } from '@manyun/base-ui.util.csv';

import { VendorModelSelect } from '@manyun/crm.ui.vendor-model-select';
import { syncCommonDataAction } from '@manyun/dc-brain.state.common';

import {
  AssetClassificationApiTreeSelect,
  GutterWrapper,
  LocationCascader,
  TinyCard,
  TinyTable,
  UserLink,
  UserSelect,
} from '@manyun/dc-brain.legacy.components';
import { DEVICE_SPACE_TYPE_KEY_MAP } from '@manyun/dc-brain.legacy.constants/enums/deviceType';
import {
  deleteSpare,
  initializeSearchValues,
  setPagination,
  spareActions,
  spareListPage,
} from '@manyun/dc-brain.legacy.redux/actions/spareActions';
import * as spareService from '@manyun/dc-brain.legacy.services/spareService';
import { getLocationSearchMap } from '@manyun/dc-brain.legacy.utils';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';

import EditSpareMess from './../edit';

const getColumns = (ctx, deviceTypeMap) => [
  {
    title: 'ID',
    dataIndex: 'id',
    visible: true,
    exportable: false,
  },
  {
    title: '一级分类',
    dataIndex: 'topCategory',
    visible: true,
    render(topCategory) {
      return get(deviceTypeMap, [topCategory, 'metaName'], topCategory);
    },
    stringify: text => get(deviceTypeMap, [text, 'metaName'], text),
  },
  {
    title: '二级分类',
    dataIndex: 'secondCategory',
    visible: true,
    render(secondCategory) {
      return get(deviceTypeMap, [secondCategory, 'metaName'], secondCategory);
    },
    stringify: text => get(deviceTypeMap, [text, 'metaName'], text),
  },
  {
    title: '三级分类',
    dataIndex: 'spareType',
    visible: true,
    render(spareType) {
      return get(deviceTypeMap, [spareType, 'metaName'], spareType);
    },
    stringify: text => get(deviceTypeMap, [text, 'metaName'], text),
  },
  {
    title: '机房编号',
    dataIndex: 'idcTag',
    visible: true,
  },
  {
    title: '楼栋编号',
    dataIndex: 'blockGuid',
    visible: true,
    render: blockGuid => {
      if (!blockGuid) {
        return blockGuid;
      }
      return blockGuid.split('.')[1];
    },
  },
  {
    title: '包间编号',
    dataIndex: 'roomTag',
  },
  {
    title: '货架',
    dataIndex: 'shelves',
  },
  {
    title: '厂商',
    dataIndex: 'vendor',
  },

  {
    title: '型号',
    dataIndex: 'productModel',
  },
  {
    title: '数量',
    dataIndex: 'totalNum',
  },
  {
    title: '供应商简称',
    dataIndex: 'supplyVendor',
    render: supplyVendor => (
      <Typography.Text style={{ width: 200 }} ellipsis={{ tooltip: supplyVendor }}>
        {supplyVendor ?? '--'}
      </Typography.Text>
    ),
  },
  {
    title: '单位',
    dataIndex: 'unit',
    render: unit => unit ?? '--',
  },
  {
    title: '更新人',
    dataIndex: 'modifyPersonId',
    render: (modifyPersonId, record) => (
      <UserLink userId={modifyPersonId} userName={record.modifyPersonName} />
    ),
    exportable: false,
  },
  {
    title: '更新时间',
    dataIndex: 'gmtModified',
    dataType: 'datetime',
    exportable: false,
  },
  {
    title: '操作',
    key: '__actions',
    dataIndex: '__actions',
    fixed: 'right',
    exportable: false,
    render: (__, record) => (
      <span>
        <EditSpareMess
          style={{ padding: 0, height: 'auto' }}
          mode="edit"
          text="编辑"
          title="编辑基本信息"
          type="link"
          basicInfo={record}
          searchParams={ctx.getParams}
          onClick={() => ctx.showEditDrawer()}
        />

        <Divider type="vertical" />
        <DeleteConfirm targetName="资产" onOk={({ reason }) => ctx.deleteSpare(record.id, reason)}>
          <Button type="link" compact>
            删除
          </Button>
        </DeleteConfirm>
      </span>
    ),
  },
];

class SpareManage extends Component {
  state = {
    pageNo: 1,
    pageSize: 10,
    secondCategory: [],
    selectedRowKeys: [],
    selectedRows: [],
    deviceTypeMap: {},
    selectLoading: false,
  };

  showEditDrawer = () => {
    this.props.editBasicInfoVisible();
  };

  componentDidMount() {
    this.props.syncCommonData({ strategy: { deviceCategory: 'FORCED' } });
    this.props.initializeSearchValues(this._getUrlSearchParams());
  }

  _getUrlSearchParams = () => {
    const { search } = this.props.location;
    return getLocationSearchMap(search, ['deviceType', 'productModel', 'vendor', 'blockGuid']);
  };

  search = () => {
    this._clearSelection();
    this.props.resetSparePagination();
    this.props.spareListPage();
  };

  deleteSpare = (id, operatorNotes) =>
    new Promise(resolve => {
      this.props.deleteSpare({
        data: { id, operatorNotes },
        successCallback: () => {
          resolve(true);
        },
        errorCallback: () => {
          resolve(false);
        },
      });
    });

  handleReset = () => {
    this.props.dispatch(this.props.resetSearchValues());
    this._clearSelection();
    this.props.setPagination({ pageNum: 1, pageSize: 10 });
  };

  getParams = () => {
    const fieldsValue = this.props.searchValues;
    const params = Object.keys(fieldsValue).reduce((map, fieldName) => {
      const value = fieldsValue[fieldName]?.value;
      if ([null, undefined, ''].includes(value) || (Array.isArray(value) && value.length === 0)) {
        return map;
      } else if (fieldName === 'vendorModel') {
        map.vendor = value[0];
        map.productModel = value[1];
      } else if (fieldName === 'cascaderList') {
        map.topCategory = value.firstCategoryCode;
        map.secondCategory = value.secondCategoryCode;
        map.spareTypeList = value.thirdCategorycode && [value.thirdCategorycode];
      } else if (fieldName === 'areaIdcBlockRoom') {
        map.idcTag = value[0];
        if (value[1]) {
          map.blockGuid = value[0] + '.' + value[1];
        }
        map.roomTag = value[2];
      } else if (fieldName === 'modifiedPerson') {
        map.modifyPersonId = value.id;
      } else if (fieldName === 'modifiedTime') {
        map.modifyEndTime = Number(value[1].unix() + '000');
        map.modifyStartTime = Number(value[0].unix() + '000');
      } else {
        map[fieldName] = trim(value);
      }
      return map;
    }, {});
    return params;
  };

  onSelectChange = (selectedRowKeys, selectedRows) => {
    this.setState({ selectedRowKeys, selectedRows });
  };

  _clearSelection = () => {
    this.setState({ selectedRowKeys: [], selectedRows: [] });
  };

  handleFileExport = async type => {
    const { deviceTypeMap } = this.props;
    const { selectedRows } = this.state;
    if (type === 'all') {
      const { response } = await spareService.exportSpare({});
      return response;
    } else if (type === 'filtered') {
      const { response } = await spareService.exportSpare(this.getParams());
      return response;
    } else {
      return toCSVBlob(
        { columns: getColumns(this, deviceTypeMap), data: selectedRows },
        {
          bom: true,
        }
      );
    }
  };

  render() {
    const {
      loading,
      sparePage,
      deviceTypeMap,
      updateSearchValues,
      searchValues,
      pagination,
      setPagination,
      form,
    } = this.props;
    const { selectedRowKeys, selectedRows } = this.state;

    return (
      <GutterWrapper mode="vertical">
        <TinyCard>
          <FiltersForm
            form={form}
            items={[
              {
                label: '资产分类',
                name: 'cascaderList',
                control: (
                  <AssetClassificationApiTreeSelect
                    dataType={['noSnDevice']}
                    category="allCategoryCode"
                    allowClear
                  />
                ),
              },
              {
                label: '位置',
                name: 'areaIdcBlockRoom',
                control: <LocationCascader showRoom currentAuthorize />,
              },
              {
                label: '更新人',
                name: 'modifiedPerson',
                control: <UserSelect allowClear />,
              },
              {
                label: '更新时间',
                name: 'modifiedTime',
                span: 2,
                control: (
                  <DatePicker.RangePicker
                    format="YYYY-MM-DD HH:mm:ss"
                    showTime
                    placeholder={['开始时间', '结束时间']}
                  />
                ),
              },
              {
                label: '厂商、型号',
                name: 'vendorModel',
                control: (
                  <VendorModelSelect
                    deviceType={searchValues.cascaderList?.value?.thirdCategorycode}
                    allowClear
                    numbered={false}
                  />
                ),
              },
            ]}
            fields={Object.keys(searchValues).map(name => {
              const field = searchValues[name];
              return {
                ...field,
                name: name.split('.'),
              };
            })}
            onFieldsChange={changedFields => {
              let fields = changedFields.reduce((mapper, field) => {
                const name = field.name.join('.');
                mapper[name] = {
                  ...field,
                  name,
                };
                return mapper;
              }, {});
              if (fields.cascaderList) {
                fields = {
                  ...fields,
                  vendorModel: {
                    value: [],
                  },
                };
              }
              updateSearchValues(fields);
            }}
            onSearch={this.search}
            onReset={this.handleReset}
          />
        </TinyCard>
        <TinyCard>
          <TinyTable
            rowKey="id"
            exportServices={() => spareService.exportSpare(this.getParams())}
            rowSelection={{
              selectedRowKeys,
              selectedRows,
              onChange: this.onSelectChange,
            }}
            selectRowsSpreadPage
            actions={
              <GutterWrapper flex>
                <Button
                  key="new-device-btn"
                  type="primary"
                  href={urls.generateCreateSpareConfigUrl()}
                >
                  新建
                </Button>
                <FileExport
                  filename="耗材数据"
                  disabled={!sparePage.total}
                  showExportFiltered
                  showExportSelected={selectedRows.length}
                  data={type => {
                    return this.handleFileExport(type);
                  }}
                />
              </GutterWrapper>
            }
            dataSource={sparePage.list}
            loading={loading}
            columns={getColumns(this, deviceTypeMap)}
            scroll={{ x: 'max-content' }}
            pagination={{
              total: sparePage.total,
              current: pagination.pageNum,
              pageSize: pagination.pageSize,
              onChange: (current, size) => {
                this.setState({ selectedRowKeys: [], selectedRows: [] });
                setPagination({ pageNum: current, pageSize: size });
              },
            }}
          />
        </TinyCard>
      </GutterWrapper>
    );
  }
}

const mapStateToProps = ({
  spareManage: { loading, sparePage, pagination, searchValues },
  common: { deviceCategory },
}) => ({
  loading,
  sparePage,
  pagination,
  searchValues,
  deviceTypeMap: get(deviceCategory, 'normalizedList'),
  deviceCategoryTreeList: deviceCategory?.treeList?.filter(
    ({ metaStyle }) => metaStyle !== DEVICE_SPACE_TYPE_KEY_MAP.SPACE
  ),
});

const mapDispatchToProps = {
  spareActions,
  spareListPage,
  deleteSpare,
  syncCommonData: syncCommonDataAction,
  updateSearchValues: spareActions.updateSearchValues,
  resetSearchValues: spareActions.resetSearchValues,
  editBasicInfoVisible: spareActions.editBasicInfoVisible,
  initializeSearchValues,
  setPagination,
  resetSparePagination: spareActions.resetSparePagination,
};

function SpareManageList(props) {
  const [form] = Form.useForm();
  const dispatch = useDispatch();

  return <SpareManage form={form} dispatch={dispatch} {...props} />;
}

export default connect(mapStateToProps, mapDispatchToProps)(SpareManageList);
