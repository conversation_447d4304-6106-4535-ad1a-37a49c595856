import React, { Component } from 'react';
import { connect } from 'react-redux';

import { Select } from '@galiojs/awesome-antd';
import trim from 'lodash/trim';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Input } from '@manyun/base-ui.ui.input';
import { Typography } from '@manyun/base-ui.ui.typography';

import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { App } from '@manyun/dc-brain.model.app';

import { GutterWrapper, TinyCard, TinyTable } from '@manyun/dc-brain.legacy.components';
import {
  fetchDeletePower,
  fetchPowerMenuPage,
  powerActions,
} from '@manyun/dc-brain.legacy.redux/actions/powerActions';

import CreatePower from '../components/create-power';
import EditPower from '../components/edit-power';

const columns = ctx => [
  {
    title: '权限名称',
    dataIndex: 'permissionName',
  },
  {
    title: '权限ID',
    dataIndex: 'permissionCode',
  },
  {
    title: '父权限',
    dataIndex: 'parentPermissionName',
    render: parentPermissionName => <span>{parentPermissionName}</span>,
  },
  {
    title: '权限类型',
    dataIndex: 'permissionTypeCh',
  },
  {
    title: 'URL',
    dataIndex: 'url',
  },
  {
    title: '系统站点',
    dataIndex: 'sysSite',
    render: (__, option) => {
      if (option?.sysSite) {
        const _list = option.sysSite.split(',');
        return <div>{_list?.map(sysSite => App.t(`code.${sysSite}`)).join(',')}</div>;
      }
    },
  },
  {
    title: '排序',
    dataIndex: 'sort',
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    dataType: 'datetime',
  },
  {
    title: '更新时间',
    dataIndex: 'gmtModified',
    dataType: 'datetime',
  },
  {
    title: '创建人',
    dataIndex: 'creatorName',
    render: (__, { creatorName, creatorId }) => {
      if (creatorId === null || creatorId === undefined) {
        return '--';
      }
      return <UserLink userId={creatorId} userName={creatorName} />;
    },
  },
  {
    title: '更新人',
    dataIndex: 'modifierName',
    render: (__, { modifierName, modifierId }) => {
      if (modifierId === null || modifierId === undefined) {
        return '--';
      }
      return <UserLink userId={modifierId} userName={modifierName} />;
    },
  },
  {
    title: '备注',
    dataIndex: 'remarks',
    render: remarks => {
      return (
        <Typography.Text style={{ maxWidth: 120 }} ellipsis={{ tooltip: true }}>
          {remarks}
        </Typography.Text>
      );
    },
  },
  {
    title: '操作',
    key: '__actions',
    fixed: 'right',
    render: (__, record) => (
      <span>
        <Button
          style={{ padding: 0, height: 'auto' }}
          type="link"
          onClick={() => ctx.editPower(record)}
        >
          编辑
        </Button>
        <Divider type="vertical" />
        <DeleteConfirm
          variant="popconfirm"
          title={`确认删除权限：${record.permissionName}吗？`}
          onOk={() => ctx.deletePower(record)}
        >
          <Button type="link" style={{ padding: 0, height: 'auto' }}>
            删除
          </Button>
        </DeleteConfirm>
      </span>
    ),
  },
];
class PowerManageList extends Component {
  componentDidMount() {
    const { powerPage } = this.props;
    this.props.fetchPowerMenuPage({
      pageNo: powerPage.pageNo,
      pageSize: powerPage.pageSize,
      searchType: powerPage.searchType,
      searchName: powerPage.searchName,
    });
  }

  onChangePage = (pageNo, pageSize) => {
    const { powerPage } = this.props;
    this.props.fetchPowerMenuPage({
      pageSize,
      searchType: powerPage.searchType,
      searchName: powerPage.searchName,
      pageNo,
    });
  };

  // 删除
  deletePower = row => {
    const powerId = row.id;
    const { powerPage } = this.props;
    this.props.fetchDeletePower({
      permissionId: powerId,
      pageNo: powerPage.pageNo,
      pageSize: powerPage.pageSize,
      searchType: powerPage.searchType,
      searchName: powerPage.searchName,
    });
  };

  // 编辑
  editPower = row => {
    this.props.editPowerVisible();
    this.props.editPowerMess(row);
  };

  // 新建角色
  createPower = () => {
    this.props.createPowerVisible();
  };

  // 搜索查询
  onParamValue = value => {
    const { powerPage } = this.props;
    this.props.fetchPowerMenuPage({
      pageNo: 1,
      pageSize: powerPage.pageSize,
      searchType: powerPage.searchType,
      searchName: trim(value),
    });
  };

  onParamKey = paramKey => {
    this.props.saveSearchCondition({ searchType: paramKey });
  };

  changeSearchName = e => {
    this.props.saveSearchCondition({ searchName: e.target.value });
  };

  render() {
    const { powerPage, loading } = this.props;
    return (
      <GutterWrapper mode="vertical">
        <TinyCard>
          <GutterWrapper mode="vertical">
            <Typography.Title level={4}>权限管理</Typography.Title>
            <Alert message="权限管理针对不同角色，提供不同访问能力" type="info" />
          </GutterWrapper>
        </TinyCard>
        <TinyCard>
          <TinyTable
            rowKey="id"
            columns={columns(this)}
            loading={loading}
            dataSource={powerPage.list}
            scroll={{ x: 'max-content' }}
            pagination={{
              total: powerPage.total,
              current: powerPage.pageNo,
              pageSize: powerPage.pageSize,
              onChange: this.onChangePage,
            }}
            actions={
              <GutterWrapper flex justifyContent="flex-start">
                <Button key="add" type="primary" onClick={this.createPower}>
                  新建权限
                </Button>
                <Input.Group key="search" compact style={{ width: '30%', minWidth: 400 }}>
                  <Select
                    value={powerPage.searchType}
                    style={{ width: '30%' }}
                    onChange={this.onParamKey}
                  >
                    <Select.Option value="SYS">系统权限</Select.Option>
                    <Select.Option value="MENU">菜单权限</Select.Option>
                    <Select.Option value="PAGE">页面权限</Select.Option>
                    <Select.Option value="ELEMENT">元素权限</Select.Option>
                  </Select>
                  <Input.Search
                    allowClear
                    style={{ width: '70%' }}
                    value={powerPage.searchName}
                    onChange={this.changeSearchName}
                    onSearch={this.onParamValue}
                  />
                </Input.Group>
              </GutterWrapper>
            }
          />
        </TinyCard>
        {/* 新建权限弹框 */}
        <CreatePower />
        {/* 编辑弹框 */}
        <EditPower />
      </GutterWrapper>
    );
  }
}

const mapStateToProps = ({ powerManage: { loading, powerPage } }) => ({
  loading,
  powerPage,
});
const mapDispatchToProps = {
  powerActions,
  fetchPowerMenuPage,
  fetchDeletePower,
  createPowerVisible: powerActions.createPowerVisible,
  editPowerVisible: powerActions.editPowerVisible,
  editPowerMess: powerActions.editPowerMess,
  success: powerActions.success,
  saveSearchCondition: powerActions.saveSearchCondition,
};

export default connect(mapStateToProps, mapDispatchToProps)(PowerManageList);
