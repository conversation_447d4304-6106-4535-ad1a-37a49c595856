import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';

import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { Select } from '@manyun/base-ui.ui.select';
import { Typography } from '@manyun/base-ui.ui.typography';

import { AppsSelect } from '@manyun/dc-brain.ui.apps-select';

import { TinyDrawer } from '@manyun/dc-brain.legacy.components';
import ApiTreeSelect from '@manyun/dc-brain.legacy.components/api-tree-select';
import {
  fetchPowerTree,
  fetchUpdatePower,
  powerActions,
} from '@manyun/dc-brain.legacy.redux/actions/powerActions';
import { fetchAllPowerApiTreeSelect } from '@manyun/dc-brain.legacy.services/treeDataService';
import { getPixelPercentageWidth } from '@manyun/dc-brain.legacy.utils/index';

class EditPower extends Component {
  state = {
    pageNo: 1,
    pageSize: 10,
    searchName: '',
    searchType: '',
    parentId: '',
  };

  closeEditPower = () => {
    this.props.closeVisible();
  };

  submitEditPower = (event, form) => {
    event.preventDefault();
    const permissionId = this.props.powerMess.id;
    this.props.form.validateFields(async (err, values) => {
      const { powerPage } = this.props;
      if (err) {
        return;
      }
      if (!err) {
        let remarks = values.remarks;
        if (values.remarks === null || values.remarks === undefined) {
          remarks = '';
        }
        this.props.fetchUpdatePower({
          params: { permissionId, ...values, remarks },
          ...powerPage,
          sysSite: values.sysSite.join(','),
        });
      }
    });
  };

  focus = () => {
    this.props.fetchPowerTree();
  };

  changeTree = value => {
    this.setState({
      parentId: value.value,
    });
  };

  render() {
    const form = this.props.form;
    const { getFieldDecorator } = form;
    const { editPowerVisible, powerMess } = this.props;

    return (
      <div>
        <TinyDrawer
          width={getPixelPercentageWidth(554)}
          title={<Typography.Title level={4}>编辑权限</Typography.Title>}
          destroyOnClose
          visible={editPowerVisible}
          onClose={this.closeEditPower}
          onCancel={this.closeEditPower}
          onSubmit={this.submitEditPower}
        >
          <Form layout="vertical" onSubmit={this.submitEditPower}>
            <Form.Item label="权限ID">
              {getFieldDecorator('permissionCode', {
                rules: [
                  { required: true, whitespace: true, message: '权限ID必须输入' },
                  {
                    max: 64,
                    message: '最多输入 64 个字符！',
                  },
                ],
                initialValue: powerMess.permissionCode,
              })(<Input />)}
            </Form.Item>

            <Form.Item label="权限名称">
              {getFieldDecorator('permissionName', {
                rules: [
                  { required: true, whitespace: true, message: '权限名称必须输入' },
                  {
                    max: 24,
                    message: '最多输入 24 个字符！',
                  },
                ],
                initialValue: powerMess.permissionName,
              })(<Input />)}
            </Form.Item>

            <Form.Item label="父类权限">
              {getFieldDecorator('parentId', {
                rules: [{ required: true, message: '请选择父类权限' }],
                initialValue: powerMess.parentId,
              })(
                <ApiTreeSelect
                  dataService={fetchAllPowerApiTreeSelect}
                  fieldNames={{ value: 'id', key: 'id', title: 'title' }}
                  requestOnDidMount
                  style={{ width: 200 }}
                />
              )}
            </Form.Item>

            <Form.Item label="权限类型">
              {getFieldDecorator('permissionType', {
                rules: [{ required: true, message: '请选择权限类型' }],
                initialValue: powerMess.permissionType,
              })(
                <Select style={{ width: 200 }}>
                  <Select.Option value="SYS">系统权限</Select.Option>
                  <Select.Option value="MENU">菜单权限</Select.Option>
                  <Select.Option value="PAGE">页面权限</Select.Option>
                  <Select.Option value="ELEMENT">元素权限</Select.Option>
                </Select>
              )}
            </Form.Item>
            <Form.Item label="系统站点">
              {getFieldDecorator('sysSite', {
                rules: [{ required: true, message: '系统站点为必填项' }],
                initialValue: powerMess.sysSite?.split(','),
              })(<AppsSelect style={{ width: 240 }} mode="multiple" />)}
            </Form.Item>

            {(powerMess.permissionType === 'MENU' || powerMess.permissionType === 'PAGE') && (
              <Form.Item label="URL">
                {getFieldDecorator('url', {
                  initialValue: powerMess.url,
                })(<Input />)}
              </Form.Item>
            )}

            <Form.Item label="排序">
              {getFieldDecorator('sort', {
                initialValue: powerMess.sort,
                rules: [{ required: true, message: '排序为必填项' }],
              })(<InputNumber />)}
            </Form.Item>

            <Form.Item label="备注">
              {getFieldDecorator('remarks', {
                initialValue: powerMess.remarks,
                rules: [
                  {
                    max: 128,
                    message: '最多输入 128 个字符！',
                  },
                ],
              })(<Input.TextArea rows={4} />)}
            </Form.Item>
          </Form>
        </TinyDrawer>
      </div>
    );
  }
}
const mapStateToProps = ({
  powerManage: { editPowerVisible, powerMess, powerTreeSeclect, powerPage },
}) => ({
  editPowerVisible,
  powerMess,
  powerTreeSeclect,
  powerPage,
});

const mapDispatchToProps = {
  closeVisible: powerActions.editPowerVisible,
  fetchUpdatePower,
  fetchPowerTree,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(Form.create({ name: 'edit_power' })(EditPower));
