import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';

import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { Select } from '@manyun/base-ui.ui.select';
import { Typography } from '@manyun/base-ui.ui.typography';

import { AppsSelect } from '@manyun/dc-brain.ui.apps-select';

import { TinyDrawer } from '@manyun/dc-brain.legacy.components';
import ApiTreeSelect from '@manyun/dc-brain.legacy.components/api-tree-select';
import {
  fetchCreatePower,
  fetchPowerTree,
  powerActions,
} from '@manyun/dc-brain.legacy.redux/actions/powerActions';
import { fetchAllPowerApiTreeSelect } from '@manyun/dc-brain.legacy.services/treeDataService';
import { getPixelPercentageWidth } from '@manyun/dc-brain.legacy.utils/index';

class CreatePower extends Component {
  state = {
    pageNo: 1,
    pageSize: 10,
    parentId: '',
  };

  closeCreatePower = () => {
    this.props.closeVisible();
  };

  submitCreatePower = event => {
    this.props.form.validateFields(async (err, values) => {
      if (err) {
        return;
      }
      if (!err) {
        const { pageSize, pageNo } = this.state;
        let parentId = values.parentId;
        let remarks = values.remarks;
        if (values.remarks === null || values.remarks === undefined) {
          remarks = '';
        }
        if (values.parentId === null || values.parentId === undefined) {
          parentId = '';
        }
        this.props.fetchCreatePower({
          pageSize,
          pageNo,
          ...values,
          remarks,
          parentId,
          sysSite: values.sysSite.join(','),
        });
      }
    });
  };

  focus = () => {
    this.props.fetchPowerTree();
  };

  changeTree = value => {
    this.setState({
      parentId: value,
    });
  };

  render() {
    const form = this.props.form;
    const { getFieldDecorator, getFieldValue } = form;
    const { createPowerVisible, createPowerLoading } = this.props;

    return (
      <div>
        <TinyDrawer
          width={getPixelPercentageWidth(554)}
          title={<Typography.Title level={4}>新建权限</Typography.Title>}
          destroyOnClose
          visible={createPowerVisible}
          submitButtonLoading={createPowerLoading}
          onClose={this.closeCreatePower}
          onCancel={this.closeCreatePower}
          onSubmit={this.submitCreatePower}
        >
          <Form layout="vertical" onSubmit={this.submitCreatePower}>
            <Form.Item label="权限ID">
              {getFieldDecorator('permissionCode', {
                rules: [
                  { required: true, whitespace: true, message: '权限ID为必填项' },
                  {
                    max: 64,
                    message: '最多输入 64 个字符！',
                  },
                ],
              })(<Input />)}
            </Form.Item>
            <Form.Item label="权限名称">
              {getFieldDecorator('permissionName', {
                rules: [
                  { required: true, whitespace: true, message: '权限名称为必填项' },
                  {
                    max: 24,
                    message: '最多输入 24 个字符！',
                  },
                ],
              })(<Input />)}
            </Form.Item>

            <Form.Item label="父类权限">
              {getFieldDecorator('parentId', {
                rules: [{ required: true, message: '父类权限为必填项' }],
              })(
                <ApiTreeSelect
                  dataService={fetchAllPowerApiTreeSelect}
                  fieldNames={{ value: 'id', key: 'id', title: 'title' }}
                  style={{ width: 200 }}
                />
              )}
            </Form.Item>

            <Form.Item label="权限类型">
              {getFieldDecorator('permissionType', {
                rules: [{ required: true, whitespace: true, message: '权限类型为必填项' }],
              })(
                <Select style={{ width: 200 }}>
                  <Select.Option value="SYS">系统权限</Select.Option>
                  <Select.Option value="MENU">菜单权限</Select.Option>
                  <Select.Option value="PAGE">页面权限</Select.Option>
                  <Select.Option value="ELEMENT">元素权限</Select.Option>
                </Select>
              )}
            </Form.Item>
            <Form.Item label="系统站点">
              {getFieldDecorator('sysSite', {
                rules: [{ required: true, message: '系统站点为必填项' }],
              })(<AppsSelect style={{ width: 240 }} mode="multiple" />)}
            </Form.Item>

            {getFieldValue('permissionType') && (
              <Form.Item label="URL">{getFieldDecorator('url')(<Input />)}</Form.Item>
            )}

            <Form.Item label="排序">
              {getFieldDecorator('sort', {
                rules: [{ required: true, message: '排序为必填项' }],
              })(<InputNumber />)}
            </Form.Item>

            <Form.Item label="备注">
              {getFieldDecorator('remarks', {
                rules: [
                  {
                    max: 128,
                    message: '最多输入 128 个字符！',
                  },
                ],
              })(<Input.TextArea rows={4} />)}
            </Form.Item>
          </Form>
        </TinyDrawer>
      </div>
    );
  }
}
const mapStateToProps = ({
  powerManage: { createPowerVisible, createPowerLoading, powerTreeSeclect },
}) => ({
  createPowerVisible,
  createPowerLoading,
  powerTreeSeclect,
});

const mapDispatchToProps = {
  closeVisible: powerActions.createPowerVisible,
  fetchCreatePower,
  fetchPowerTree,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(Form.create({ name: 'create_power' })(CreatePower));
