import React, { Component } from 'react';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';

import RoleDetailCardList from '../components/role-detail-list';
import RoleDetailCardOption from '../components/role-detail-option';

export default class RoleDetail extends Component {
  render() {
    const match = this.props.match.params;
    return (
      <GutterWrapper mode="vertical">
        {/* 基本信息 */}
        <RoleDetailCardOption {...this.props} />
        {/* 用户组和权限管理列表 */}
        <RoleDetailCardList {...match} {...this.props} />
      </GutterWrapper>
    );
  }
}
