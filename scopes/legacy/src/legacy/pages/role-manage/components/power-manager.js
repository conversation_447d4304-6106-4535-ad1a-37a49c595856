import { Select } from '@galiojs/awesome-antd';
import trim from 'lodash/trim';
import uniq from 'lodash/uniq';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Input } from '@manyun/base-ui.ui.input';
import { App } from '@manyun/dc-brain.model.app';

import { GutterWrapper, TinyTable } from '@manyun/dc-brain.legacy.components';
import {
  fetchAllPowerTree,
  fetchPowerJoinRole,
  fetchUnbindPowerFromRole,
  roleActions,
} from '@manyun/dc-brain.legacy.redux/actions/roleActions';
import * as treeDataService from '@manyun/dc-brain.legacy.services/treeDataService';

import ConnectPowerModel from './connect-power-model';
import styles from './power-manager.module.less';

const InputGroup = Input.Group;
const { Option } = Select;
const { Search } = Input;

const powercolumns = ctx => [
  {
    title: '权限策略名称',
    dataIndex: 'permissionName',
  },
  {
    title: '权限策略ID',
    dataIndex: 'permissionCode',
  },
  {
    title: '权限策略类型',
    dataIndex: 'permissionTypeCh',
  },
  {
    title: '所属系统站点',
    dataIndex: 'sites',
    render: (sites, option) => {
      return sites
        ?.map(webSite =>
          webSite === 'MONITOR'
            ? `${option.idcTag ?? ''} ${App.t(`code.${webSite}`)}`
            : App.t(`code.${webSite}`)
        )
        .join(',');
    },
  },
  {
    title: '创建人',
    dataIndex: 'creatorName',
  },

  {
    title: '备注',
    dataIndex: 'remarks',
    ellipsis: true,
  },
  {
    title: '操作',
    key: '__actions',
    fixed: 'right',

    render: (__, record) => {
      return (
        <DeleteConfirm
          variant="popconfirm"
          title={`确认从角色中移除权限： ${record.permissionName}吗？`}
          onOk={() => ctx.deletePower(record)}
        >
          <Button type="link" style={{ padding: 0, height: 'auto' }}>
            移除权限
          </Button>
        </DeleteConfirm>
      );
    },
  },
];

class PowerManager extends Component {
  state = {
    addPower: false,
    searchName: '',
    searchType: 'permissionName',
    selectedRowsList: [],
    expandedKeys: [],
    selectedKeys: [],
  };
  // 获取下拉框的选择项
  checkedSelectOption = paramKey => {
    this.setState({ searchType: paramKey });
  };

  // 搜索查询
  onParamValue = async value => {
    await this.setState({ searchName: trim(value) });
    const { powerList } = this.props;
    const { searchName, searchType } = this.state;
    if (searchName) {
      const { parallelList } = treeDataService.processingReturnData(powerList.list);
      let expandeds = [];
      let selecteds = [];
      let list = [];
      list =
        searchType === 'permissionName'
          ? parallelList.filter(item => item.permissionName.includes(searchName))
          : parallelList.filter(item => item.creatorName.includes(searchName));
      list.forEach(item => (expandeds = [...expandeds, item.parentId]));
      list.forEach(item => (selecteds = [...selecteds, item.id]));
      expandeds.forEach(
        item => (expandeds = [...expandeds, ...this.getParentIds(item, parallelList)])
      );
      this.setState({
        expandedKeys: uniq(expandeds),
        selectedKeys: selecteds,
      });
    }
  };

  getParentIds = (id, tree, sum = []) => {
    const node = tree.filter(item => item.id === id);
    if (node.length === 0) {
      return sum;
    }
    return node[0].parentId
      ? this.getParentIds(node[0].parentId, tree, [...sum, node[0].id])
      : [...sum, node[0].id];
  };

  deletePower = row => {
    const { basicInfo } = this.props;
    this.props.fetchUnbindPowerFromRole({
      roleId: basicInfo.id,
      permissionId: row.id,
    });
  };

  connectPowerModel = () => {
    const { basicInfo } = this.props;
    this.setState({
      selectedRowsList: [{ key: basicInfo.id, label: basicInfo.roleName }],
    });
    this.props.openConnectVisible();
    this.props.fetchAllPowerTree();
  };

  onExpand = (expanded, record) => {
    const { expandedKeys } = this.state;
    if (expanded) {
      this.setState({
        expandedKeys: [...expandedKeys, record.id],
      });
    } else {
      expandedKeys.splice(expandedKeys.indexOf(record.id), 1);
      this.setState({
        expandedKeys,
      });
    }
  };

  render() {
    const { className, powerList, loading } = this.props;
    const { searchType, selectedRowsList, expandedKeys, selectedKeys } = this.state;

    return (
      <GutterWrapper className={className} mode="vertical">
        <TinyTable
          rowKey="id"
          columns={powercolumns(this)}
          dataSource={powerList.list}
          expandedRowKeys={expandedKeys}
          rowClassName={record =>
            selectedKeys.includes(record.id) ? `${styles.tinyTableRowSelected}` : ''
          }
          loading={loading}
          pagination={false}
          scroll={{ x: 'max-content' }}
          actions={[
            <Button key="connect-power" type="primary" onClick={this.connectPowerModel}>
              维护权限
            </Button>,
            <InputGroup key="search" compact style={{ width: '20%', minWidth: 400 }}>
              <Select
                value={searchType}
                style={{ width: '35%' }}
                onChange={this.checkedSelectOption}
              >
                <Option value="permissionName">权限策略名称</Option>
                <Option value="creatorName">创建人名称</Option>
              </Select>
              <Search
                allowClear
                placeholder="请输入"
                style={{ width: '65%' }}
                onSearch={this.onParamValue}
              />
            </InputGroup>,
          ]}
          onExpand={this.onExpand}
        />

        {/* 关联权限 */}
        <ConnectPowerModel mode="edit" defaultSelectedSources={selectedRowsList} />
      </GutterWrapper>
    );
  }
}
const mapStateToProps = ({ roleManage: { powerList, total, loading, basicInfo } }) => ({
  powerList,
  total,
  loading,
  basicInfo,
});
const mapDispatchToProps = {
  fetchPowerJoinRole,
  fetchUnbindPowerFromRole,
  saveSelectRole: roleActions.saveSelectRole,
  openConnectVisible: roleActions.connectPowerVisible,
  fetchAllPowerTree,
  success: roleActions.rolePowerListSuccess,
};

export default connect(mapStateToProps, mapDispatchToProps)(PowerManager);
