import PageHeader from 'antd/es/page-header';
import moment from 'moment';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import { ROLES_ROUTE_PATH } from '@manyun/auth-hub.route.auth-routes';
import { RoleEditorDrawer as UpdateRoleDrawer } from '@manyun/auth-hub.ui.role-editor-drawer';
import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { Button } from '@manyun/base-ui.ui.button';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Typography } from '@manyun/base-ui.ui.typography';
import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';
import { Website } from '@manyun/dc-brain.model.website';
import { getAuthResourceLocales } from '@manyun/iam.model.auth-resource';

import { <PERSON><PERSON>Wrapper, TinyCard } from '@manyun/dc-brain.legacy.components';
import { fetchRoleManageDetail } from '@manyun/dc-brain.legacy.redux/actions/roleActions';
import { redirectActionCreator } from '@manyun/dc-brain.legacy.redux/actions/routerActions';

const { Paragraph } = Typography;

class RoleOption extends Component {
  state = {
    updateRoleDrawerVisible: false,
  };

  componentDidMount() {
    const { search } = this.props.location;
    const { 'role-code': roleCode, 'role-id': roleId } = getLocationSearchMap(search, [
      'role-code',
      'role-id',
    ]);
    this.props.fetchRoleManageDetail({ roleId, roleCode });
  }

  changeUpdateRoleDrawerVisible = () => {
    this.setState({
      updateRoleDrawerVisible: !this.state.updateRoleDrawerVisible,
    });
  };

  render() {
    const { basicInfo, redirect } = this.props;
    return (
      <TinyCard>
        <GutterWrapper mode="vertical">
          <PageHeader
            style={{
              padding: '0 0 16px 0',
            }}
            title={basicInfo.roleName}
            onBack={() => redirect(ROLES_ROUTE_PATH)}
          />
          <Descriptions column={4}>
            <Descriptions.Item label="角色基本信息" span={4}>
              <Button
                type="link"
                style={{ padding: 0, height: 'auto' }}
                onClick={() => this.changeUpdateRoleDrawerVisible()}
              >
                编辑基本信息
              </Button>
            </Descriptions.Item>
            <Descriptions.Item label="角色名称">{basicInfo.roleName}</Descriptions.Item>
            <Descriptions.Item label="角色 ID">
              <Paragraph style={{ marginBottom: 0 }} copyable>
                {basicInfo.roleCode}
              </Paragraph>
            </Descriptions.Item>
            <Descriptions.Item label="角色负责人">
              <UserLink userId={basicInfo.approveId} />
            </Descriptions.Item>
            <Descriptions.Item label="资源类型">
              {basicInfo.resourceType
                ? basicInfo.resourceType
                    .split(',')
                    .map(_resourceType => getAuthResourceLocales().resourceType[_resourceType])
                    ?.join(' | ')
                : '--'}
            </Descriptions.Item>
            <Descriptions.Item label="更新人">{basicInfo.modifierName}</Descriptions.Item>
            <Descriptions.Item label="更新时间">
              {basicInfo.gmtModified
                ? moment(basicInfo.gmtModified).format('YYYY-MM-DD HH:mm:ss')
                : null}
            </Descriptions.Item>
            <Descriptions.Item label="创建人">{basicInfo.creatorName}</Descriptions.Item>
            <Descriptions.Item label="创建时间">
              {basicInfo.gmtCreate
                ? moment(basicInfo.gmtCreate).format('YYYY-MM-DD HH:mm:ss')
                : null}
            </Descriptions.Item>
            <Descriptions.Item label="备注">{basicInfo.remarks}</Descriptions.Item>
          </Descriptions>
        </GutterWrapper>
        {/* 编辑基本信息弹框 */}
        <UpdateRoleDrawer
          open={this.state.updateRoleDrawerVisible}
          id={basicInfo.id}
          afterSubmit={() => {
            this.props.fetchRoleManageDetail({
              roleId: basicInfo.id,
              roleCode: basicInfo.roleCode,
            });
            this.changeUpdateRoleDrawerVisible();
          }}
          onClose={this.changeUpdateRoleDrawerVisible}
        />
      </TinyCard>
    );
  }
}

const mapStateToProps = ({ roleManage: { basicInfo } }) => ({
  basicInfo,
});
const mapDispatchToProps = {
  fetchRoleManageDetail,
  redirect: redirectActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(RoleOption);
