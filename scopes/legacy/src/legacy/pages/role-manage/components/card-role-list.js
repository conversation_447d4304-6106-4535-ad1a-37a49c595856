import { Select } from '@galiojs/awesome-antd';
import trim from 'lodash/trim';
import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import { generateRoleRoutePath } from '@manyun/auth-hub.route.auth-routes';
import { RoleEditorDrawer as CreateRoleDrawer } from '@manyun/auth-hub.ui.role-editor-drawer';
import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { Button } from '@manyun/base-ui.ui.button';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Website } from '@manyun/dc-brain.model.website';
import { getAuthResourceLocales } from '@manyun/iam.model.auth-resource';

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, TinyTable } from '@manyun/dc-brain.legacy.components';
import {
  fetchAllPowerTree,
  fetchDeleteRole,
  loadRoleInfo,
  roleActions,
} from '@manyun/dc-brain.legacy.redux/actions/roleActions';

import ConnectPowerModel from './connect-power-model';

const InputGroup = Input.Group;
const { Option } = Select;
const { Search } = Input;

class RoleList extends Component {
  state = {
    roleId: '',
    /** 当前点击维护权限对应的 role */
    currentRow: null,
    /** 选中的 row list */
    selectedRowList: [],
    /** 角色权限 modal 的操作类型，值为 'single' ｜ 'multiple' */
    modalOperationType: 'single',
    /** 新建角色 drawer 是否展示 */
    createRoleDrawerVisible: false,
  };

  componentDidMount() {
    const { searchCondition } = this.props;
    this.props.loadRoleInfo(searchCondition);
  }

  deleteRole = row => {
    if (row.roleCode === '<EMAIL>') {
      message.error('禁止删除管理员角色！');
      return;
    }
    this.props.fetchDeleteRole({
      roleId: row.id,
      pageNo: 1,
      pageSize: 10,
      searchName: '',
      searchType: '',
    });
  };

  onSelectChange = (selectedRowKeys, selectedRows) => {
    const selectedRowList = selectedRows.map(item => {
      return {
        label: item.roleName,
        key: item.id,
      };
    });
    this.setState({ selectedRowList, selectedRowKeys });
  };

  onChangePage = (pageNo, pageSize) => {
    const { searchCondition } = this.props;
    this.props.loadRoleInfo({
      searchName: searchCondition.searchName,
      searchType: searchCondition.searchType,
      pageNo,
      pageSize,
    });
  };

  connectPowerModel = row => {
    const currentRow = {
      label: row.roleName,
      key: row.id,
    };
    this.setState({ currentRow, modalOperationType: 'single' });
    this.props.openConnectVisible();
    this.props.fetchAllPowerTree();
  };
  showPowerModel = () => {
    this.setState({ modalOperationType: 'multiple' });
    this.props.openConnectVisible();
    this.props.fetchAllPowerTree();
  };

  changeCreateRoleDrawerVisible = () => {
    this.setState({
      createRoleDrawerVisible: !this.state.createRoleDrawerVisible,
    });
  };

  onParamKey = paramKey => {
    this.props.saveSearchConditionSuccess({
      searchType: paramKey,
    });
  };

  // 搜索查询
  onParamValue = value => {
    const { searchCondition } = this.props;
    this.props.loadRoleInfo({
      ...searchCondition,
      searchName: trim(value),
      pageNo: 1,
    });
  };

  changeSearchName = e => {
    this.props.saveSearchConditionSuccess({ searchName: e.target.value });
  };

  render() {
    const { loading, roleList, searchCondition } = this.props;
    const { selectedRowKeys, selectedRowList, currentRow, modalOperationType } = this.state;
    return (
      <TinyCard>
        <TinyTable
          rowKey="id"
          rowSelection={{
            selectedRowKeys,
            onChange: this.onSelectChange,
          }}
          columns={columns(this)}
          dataSource={roleList.list}
          loading={loading}
          pagination={{
            total: roleList.total,
            current: searchCondition.pageNo,
            pageSize: searchCondition.pageSize,
            onChange: this.onChangePage,
          }}
          scroll={{ x: 'max-content' }}
          actions={
            <GutterWrapper flex justifyContent="flex-start">
              <Button key="add" type="primary" onClick={this.changeCreateRoleDrawerVisible}>
                新建角色
              </Button>
              <Button key="connect-power" type="primary" onClick={this.showPowerModel}>
                增加角色权限
              </Button>
              <InputGroup key="search" compact style={{ width: '30%', minWidth: 400 }}>
                <Select
                  style={{ width: '30%' }}
                  value={searchCondition.searchType}
                  onChange={this.onParamKey}
                >
                  <Option value="ROLE_NAME">角色名称</Option>
                  <Option value="CREATOR_NAME">创建人名称</Option>
                </Select>
                <Search
                  style={{ width: '70%' }}
                  allowClear
                  value={searchCondition.searchName}
                  onChange={this.changeSearchName}
                  onSearch={this.onParamValue}
                />
              </InputGroup>
            </GutterWrapper>
          }
        />

        {/* 关联权限 */}
        <ConnectPowerModel
          defaultSelectedSources={
            modalOperationType === 'multiple' ? selectedRowList : [currentRow]
          }
          operationType={modalOperationType}
        />
        {/* 新建角色弹框 */}
        <CreateRoleDrawer
          open={this.state.createRoleDrawerVisible}
          afterSubmit={() => {
            this.props.loadRoleInfo({
              searchName: '',
              searchType: '',
              pageNo: 1,
              pageSize: 10,
            });
            this.changeCreateRoleDrawerVisible();
          }}
          onClose={this.changeCreateRoleDrawerVisible}
        />
      </TinyCard>
    );
  }
}

const mapStateToProps = ({ roleManage: { roleList, total, loading, searchCondition } }) => ({
  roleList,
  total,
  loading,
  searchCondition,
});

const mapDispatchToProps = {
  loadRoleInfo,
  fetchDeleteRole,
  openConnectVisible: roleActions.connectPowerVisible,
  fetchAllPowerTree,
  saveSearchConditionSuccess: roleActions.saveSearchConditionSuccess,
};

export default connect(mapStateToProps, mapDispatchToProps)(RoleList);

const columns = ctx => [
  {
    title: '角色名称',
    dataIndex: 'roleName',
    dataType: {
      type: 'link',
      options: {
        to(__, { id }) {
          return generateRoleRoutePath({ roleId: id });
        },
      },
    },
  },
  {
    title: '角色负责人',
    dataIndex: 'approveId',
    render: approveId => <UserLink userId={approveId} />,
  },
  {
    title: '资源类型',
    dataIndex: 'resourceType',
    render: resourceType => {
      return resourceType
        ? resourceType
            .split(',')
            .map(_resourceType => getAuthResourceLocales().resourceType[_resourceType])
            ?.join(' | ')
        : '--';
    },
  },
  {
    title: '权限',
    dataIndex: '',
    render: (_, { id }) => <Link to={generateRoleRoutePath({ roleId: id })}>查看</Link>,
  },

  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    dataType: 'datetime',
  },
  {
    title: '更新时间',
    dataIndex: 'gmtModified',
    dataType: 'datetime',
  },
  {
    title: '创建人',
    dataIndex: '',
    render: (__, { creatorId }) => {
      return <UserLink userId={creatorId} />;
    },
  },
  {
    title: '备注',
    dataIndex: 'remarks',
    render: remarks => {
      return (
        <Ellipsis lines={1} tooltip>
          {remarks}
        </Ellipsis>
      );
    },
  },
  {
    title: '操作',
    key: '__actions',
    fixed: 'right',
    width: 180,
    render: (__, record) => (
      <span>
        <Button
          style={{ padding: 0, height: 'auto' }}
          type="link"
          onClick={() => ctx.connectPowerModel(record)}
        >
          维护权限
        </Button>
        <Divider type="vertical" />
        <DeleteConfirm
          variant="popconfirm"
          title={`确认删除角色： ${record.roleName}吗？`}
          onOk={() => ctx.deleteRole(record)}
        >
          <Button type="link" style={{ padding: 0, height: 'auto' }}>
            删除
          </Button>
        </DeleteConfirm>
      </span>
    ),
  },
];
