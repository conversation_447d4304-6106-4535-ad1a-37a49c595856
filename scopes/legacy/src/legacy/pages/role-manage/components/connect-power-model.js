import Form from '@ant-design/compatible/es/form';
import uniq from 'lodash/uniq';
import React, { Component, useEffect, useState } from 'react';
import { connect } from 'react-redux';

import { fetchRolePermissionsTree } from '@manyun/auth-hub.service.fetch-role-permissions-tree';
import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Switch } from '@manyun/base-ui.ui.switch';
import { Tree } from '@manyun/base-ui.ui.tree';
import { Typography } from '@manyun/base-ui.ui.typography';
import { flattenTreeData } from '@manyun/dc-brain.util.flatten-tree-data';

import {
  GutterWrapper,
  RoleSelect,
  TinyCard,
  TinyDrawer,
} from '@manyun/dc-brain.legacy.components';
import { StyledTitle } from '@manyun/dc-brain.legacy.components/relationship-connector';
import { roleActions, submitConnectPower } from '@manyun/dc-brain.legacy.redux/actions/roleActions';

const { Search } = Input;
const { TreeNode } = Tree;

const dataList = [];
const roleList = [];
let path = [];

const getParentKey = (key, tree) => {
  let parentKey;
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i];

    if (node.children) {
      if (node.children.some(item => item.id === key)) {
        parentKey = String(node.id);
      } else if (getParentKey(key, node.children)) {
        parentKey = getParentKey(key, node.children);
      }
    }
  }
  return parentKey;
};

const setNodePath = tree => {
  let node = {};
  for (let i = 0; i < tree.length; i++) {
    node = tree[i];
    roleList.push({ ...node, treePath: [...path, String(node.id)] });
    path = [...path, String(node.id)];
    if (node.children) {
      setNodePath(node.children);
    } else {
      path.pop();
    }
  }
  path.pop();
  return;
};

class ConnectPowerModel extends Component {
  state = {
    /**
     * 是否自动选中子权限的标识
     */
    autoCheckedChildren: false,
    // 若 autoExpandParent 为 true，则 antd expandedKeys 要等数据加载完才能获取，导致显示异常
    // 故这边默认值应为 false
    autoExpandParent: false,
    searchValue: '',
    /**
     * 类型为 Key[]，只包含真正选中的
     */
    checkedKeys: [],
    expandedKeys: [],
    selectedSources: [],
  };

  componentDidUpdate(prevProps) {
    const { allPowerList, checkedKeys } = this.props;
    if (prevProps.allPowerList !== allPowerList && roleList.length === 0) {
      setNodePath(allPowerList);
    }
    // 每次 props.checkedKeys 更新时都更新 state.checkedKeys
    if (prevProps.checkedKeys !== checkedKeys) {
      const keys = Array.isArray(this.props.checkedKeys)
        ? [...this.props.checkedKeys]
        : [...this.props.checkedKeys.checked];
      this.setState({ checkedKeys: keys });
    }
    // const { connectPowerVisible } = this.props;

    // if (connectPowerVisible === false) {
    //   if (this.state.searchValue !== '') {
    //     // console.log('this.props.searchValue', this.props.searchValue);
    //     this.setState({ searchValue: '' });
    //   }
    // }
  }

  closeConnectPower = () => {
    this.setState({ expandedKeys: [], selectedSources: [] });
    this.props.closeVisible();
  };

  submitConnectPower = () => {
    const { powerList, basicInfo, mode, defaultSelectedSources, operationType } = this.props;
    const { selectedSources, checkedKeys } = this.state;
    if (!defaultSelectedSources.length && !selectedSources.length) {
      message.error('请选择角色');
    } else if (!checkedKeys.length) {
      message.error('请选择权限');
    } else {
      let ids = [];
      defaultSelectedSources.forEach(({ key }) => {
        ids = [...ids, key];
      });
      selectedSources.forEach(({ key }) => {
        ids = [...ids, key];
      });
      ids = Array.from(new Set(ids));
      this.props.submitConnectPower({
        permissionIds: checkedKeys.map(key => Number(key)),
        roleIds: ids,
        roleId: basicInfo.id,
        ...powerList,
        mode,
        operationType,
      });
    }
  };

  handleChangeSearch = value => {
    const selectedRowKeys = value.map(({ id, label }) => {
      return { label: label, key: id };
    });
    this.setState({ selectedSources: selectedRowKeys });
  };

  onExpand = expandedKeys => {
    this.setState({ expandedKeys, autoExpandParent: false });
  };

  onCheck = checkedKeys => {
    this.props.setCheckedKeys(checkedKeys);
  };

  renderTreeNodes = (data, searchValue) => {
    const list = data.map(item => {
      let index = -1;
      let beforeStr;
      let afterStr;
      if (searchValue) {
        index = item.title.indexOf(searchValue);
        beforeStr = item.title.substr(0, index);
        afterStr = item.title.substr(index + searchValue.length);
      }

      const title =
        index > -1 ? (
          <span>
            {beforeStr}
            <span style={{ color: `var(--${prefixCls}-warning-color)` }}>{searchValue}</span>
            {afterStr}
            {!item.parentId}
          </span>
        ) : (
          <span>{item.title}</span>
        );

      if (item.children) {
        return (
          <TreeNode key={item.id} title={title}>
            {Array.isArray(item.children) &&
              item.children.length &&
              this.renderTreeNodes(item.children, searchValue)}
          </TreeNode>
        );
      }

      return <TreeNode {...item} key={item.id} title={title} />;
    });
    return list;
  };

  deleteTag = value => {
    const { checkedTag } = this.props;
    let keys = [];
    const newTag = checkedTag.filter(item => item.id !== value.id);
    newTag.forEach(({ id, title }) => {
      keys = [...keys, id];
    });
    this.props.setCheckedKeys(keys);
    this.props.saveCheckedTag(newTag);
  };

  generateList = data => {
    for (let i = 0; i < data.length; i++) {
      const node = data[i];
      const { id, title } = node;
      dataList.push({ key: id, title: title });
      if (node.children) {
        this.generateList(node.children);
      }
    }
  };

  onChangeSearch = ({ target: { value } }) => {
    if (value === '') {
      // 当查询全部数据时，此时 expandedKeys 应是用户当前选中的，且 autoExpandParent 为 true
      this.setState({
        expandedKeys: this.state.checkedKeys,
        searchValue: '',
        autoExpandParent: true,
      });

      return;
    }

    const { allPowerList } = this.props;
    this.generateList(allPowerList);
    const expandedKeys = dataList
      .map(item => {
        if (item.title.indexOf(value) > -1) {
          return getParentKey(item.key, allPowerList);
        }
        return null;
      })
      .filter((item, i, self) => item && self.indexOf(item) === i);

    this.setState({ expandedKeys, searchValue: value, autoExpandParent: true });
  };

  setAutoCheckedChildren = () => {
    const { setCheckedKeys } = this.props;
    let keys = [];
    this.state.checkedKeys.forEach(item => {
      roleList.forEach(i => {
        if (i.treePath.includes(item)) {
          keys = [...keys, i.key];
        }
      });
    });
    setCheckedKeys(uniq(keys));
  };

  render() {
    const {
      connectPowerLoading,
      connectPowerVisible,
      allPowerList,
      //   在权限树中选择的key
      checkedKeys,
      defaultExpandedKeys,
      // checkedTag,
      defaultSelectedSources,
      operationType,
    } = this.props;
    const { searchValue, expandedKeys, autoExpandParent, autoCheckedChildren } = this.state;
    const defaultKeys = expandedKeys.length > 0 ? expandedKeys : defaultExpandedKeys;

    return (
      <TinyDrawer
        width={600}
        title={
          <Typography.Title level={4}>
            {operationType === 'single' ? '维护权限' : '增加角色权限'}
          </Typography.Title>
        }
        destroyOnClose
        open={connectPowerVisible}
        submitButtonLoading={connectPowerLoading}
        onClose={this.closeConnectPower}
        onCancel={this.closeConnectPower}
        onSubmit={this.submitConnectPower}
      >
        <GutterWrapper mode="vertical">
          {StyledTitle('角色')}
          <RoleSelect
            mode="multiple"
            labelInValue
            style={{ width: '100%' }}
            placeholder="角色名称，支持多选，支持模糊搜索"
            defaultValue={defaultSelectedSources}
            disabled={operationType === 'single'}
            onChange={this.handleChangeSearch}
          />
          {StyledTitle('接口权限')}
          <GutterWrapper>
            <TinyCard bordered>
              <Search
                allowClear
                style={{ marginBottom: 8 }}
                value={searchValue}
                onChange={this.onChangeSearch}
              />
              <div>
                自动选中子权限：
                <Switch
                  checked={autoCheckedChildren}
                  onChange={checked => {
                    this.setState({ autoCheckedChildren: checked });
                    checked && this.setAutoCheckedChildren();
                  }}
                />
              </div>
              <Tree
                checkable
                checkStrictly={!autoCheckedChildren}
                expandedKeys={defaultKeys}
                autoExpandParent={autoExpandParent}
                checkedKeys={checkedKeys}
                onExpand={this.onExpand}
                onCheck={this.onCheck}
              >
                {this.renderTreeNodes(allPowerList, searchValue)}
              </Tree>
            </TinyCard>
            {/* <TinyCard style={{ width: '50%' }} bordered>
              {checkedTag.map(item => {
                return (
                  <Tag
                    closable
                    key={item.id}
                    onClose={e => {
                      e.preventDefault();
                      this.deleteTag(item);
                    }}
                  >
                    {item.title}
                  </Tag>
                );
              })}
            </TinyCard> */}
          </GutterWrapper>
        </GutterWrapper>
      </TinyDrawer>
    );
  }
}

function ConnectPowerModelWrapper(props) {
  const { operationType = 'single', defaultSelectedSources, connectPowerVisible } = props;
  const [loading, setLoading] = useState(false);
  const [checkedKeys, setCheckedKeys] = useState([]);
  const [defaultExpandedKeys, setDefaultExpandedKeys] = useState([]);

  useEffect(() => {
    if (connectPowerVisible && operationType === 'single' && defaultSelectedSources[0]?.key) {
      setLoading(true);
      fetchRolePermissionsTree({ roleId: defaultSelectedSources[0].key }).then(
        ({ error, data }) => {
          setLoading(false);
          if (error) {
            message.error(error.message);
            return;
          }
          const keys = flattenTreeData(data.data).map(item => `${item.id}`);
          setCheckedKeys(keys);
          setDefaultExpandedKeys(keys);
        }
      );
    } else {
      setCheckedKeys([]);
      setDefaultExpandedKeys([]);
    }
  }, [connectPowerVisible, defaultSelectedSources, operationType]);

  return (
    <ConnectPowerModel
      {...props}
      checkedKeys={checkedKeys}
      setCheckedKeys={setCheckedKeys}
      defaultExpandedKeys={defaultExpandedKeys}
      connectPowerLoading={props.connectPowerLoading || loading}
      operationType={operationType}
    />
  );
}

const mapStateToProps = ({
  roleManage: {
    connectPowerVisible,
    connectPowerLoading,
    allPowerList,
    checkedTag,
    powerList,
    basicInfo,
    allPowerJson,
  },
}) => ({
  connectPowerVisible,
  connectPowerLoading,
  allPowerList,
  checkedTag,
  powerList,
  basicInfo,
  allPowerJson,
});

const mapDispatchToProps = {
  closeVisible: roleActions.connectPowerVisible,
  saveCheckedTag: roleActions.saveCheckedTag,
  submitConnectPower,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(Form.create({ name: 'connect_power' })(ConnectPowerModelWrapper));
