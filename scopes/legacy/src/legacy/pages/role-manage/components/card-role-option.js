import React, { Component } from 'react';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Typography } from '@manyun/base-ui.ui.typography';

import { GutterWrapper, TinyCard } from '@manyun/dc-brain.legacy.components';

const { Title } = Typography;

class RoleOption extends Component {
  render() {
    return (
      <TinyCard>
        <GutterWrapper mode="vertical">
          <Title level={4}>角色</Title>
          <Alert
            message="通过角色管理对相同角色的用户进行分类并授权可操作权限，可以更加高效的管理数据权限及其功能权限，对一个用户进行角色授权后，用户会自动继承该角色的权限。"
            type="info"
          />
        </GutterWrapper>
      </TinyCard>
    );
  }
}

export default RoleOption;
