import React, { Component } from 'react';

import { Typography } from '@manyun/base-ui.ui.typography';

import { TinyCard } from '@manyun/dc-brain.legacy.components';

import PowerManager from './power-manager';

export default class RoleOption extends Component {
  render() {
    return (
      <TinyCard>
        <Typography.Title style={{ marginBottom: '1em' }} showBadge level={5}>
          权限管理
        </Typography.Title>
        <PowerManager {...this.props} />
      </TinyCard>
    );
  }
}
