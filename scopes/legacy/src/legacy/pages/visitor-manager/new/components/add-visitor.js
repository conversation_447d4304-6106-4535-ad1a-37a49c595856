import Form from '@ant-design/compatible/es/form';
import moment from 'moment';
import React, { useState } from 'react';
import { useLocation } from 'react-router-dom';

import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Link } from '@manyun/dc-brain.navigation.link';
import { generateVisitRoutePath } from '@manyun/sentry.route.routes';
import { checkExistingVisitsRecords } from '@manyun/sentry.service.check-existing-visits-records';

import { visitorManagerService } from '@manyun/dc-brain.legacy.services';

export function AddVisitor({
  onAddVisitor,
  addedVisitorList = [],
  form,
  idcTag,
  registerTime,
  hiddenVoucherCode,
  hiddenPersonalItem,
  hiddenOperationalAuthorization,
}) {
  const _location = useLocation();
  const searchParams = new URLSearchParams(_location.search);

  const [visible, setVisible] = useState(false);
  const [checkLoading, setCheckLoading] = useState(false);
  const [tableData, setDataSource] = useState([]);
  const [specialStaffTableData, setSpecialStaffDataSource] = useState([]);
  const [selectedRow, setSelectedRow] = useState({ selectedRowKeys: [], selectedRows: [] });

  const { getFieldDecorator } = form;
  const isSpecialStaff = React.useMemo(
    () => searchParams.get('specialStaff') === 'true',
    [searchParams.get('specialStaff')]
  );

  const columns = [
    {
      title: '人员类型',
      fixed: 'left',
      dataIndex: ['visitorType', 'name'],
    },
    {
      title: '姓名',
      dataIndex: 'name',
    },
    {
      title: '凭证码',
      dataIndex: 'authNo',
      hidden: hiddenVoucherCode,
    },
    {
      title: '证件类型',
      dataIndex: ['certificateType', 'name'],
    },
    {
      title: '证件号码',
      dataIndex: 'identityNo',
    },
    {
      title: '公司名称',
      dataIndex: 'companyName',
    },
    {
      title: '联系方式',
      dataIndex: 'contactWay',
      render: (val, record) => {
        return `${record.phoneCountryCode ? `${record.phoneCountryCode}` : ''} ${val}`;
      },
    },
    {
      title: '车牌号',
      dataIndex: 'plateNo',
    },
    {
      title: '随身物品',
      dataIndex: 'personalItem',
      hidden: hiddenPersonalItem,
      render: val => (val ? val.split(',').filter(Boolean).join('｜') : '--'),
    },
    {
      title: '申请时间',
      dataIndex: 'approveStartTime',
      ellipsis: true,
      render: (approveStartTime, { approveEndTime }) => {
        return (
          moment(approveStartTime).format('YYYY-MM-DD HH:mm') +
          '~' +
          moment(approveEndTime).format('YYYY-MM-DD HH:mm')
        );
      },
    },
    {
      title: '是否具备操作权限',
      dataIndex: 'operable',
      hidden: hiddenOperationalAuthorization,
      render(operable) {
        if (operable) {
          return '需要';
        }
        return '不需要';
      },
    },
  ].filter(i => !i.hidden);
  const getVisitor = async () => {
    if (isSpecialStaff) {
      const { response, error } = await visitorManagerService.fetchVisitorStaffList({
        bizTag: 'GOVERNMENT',
        idcTag: idcTag[0],
        blockGuid: idcTag[1],
        registerTime: registerTime.valueOf(),
        isAuth: true,
      });
      if (error) {
        message.error(error);
        return;
      }
      setSpecialStaffDataSource(response.data);
    }
    if (!isSpecialStaff) {
      const { response, error } = await visitorManagerService.fetchVisitorStaffList({
        idcTag: idcTag[0],
        blockGuid: idcTag[1],
        registerTime: registerTime.valueOf(),
        isAuth: true,
        ...form.getFieldsValue(),
      });
      if (error) {
        message.error(error);
        return;
      }
      setDataSource(response.data);
    }
  };

  const onSelectChange = (selectedRowKeys, selectedRows) => {
    setSelectedRow({ selectedRowKeys, selectedRows });
  };

  const handleSearch = () => {
    getVisitor();
  };

  const location = () => {
    if (!idcTag) {
      return;
    }
    if (!idcTag[0]) {
      return;
    }
    return idcTag[0];
  };

  return (
    <>
      <Button
        type="primary"
        disabled={!location() || !registerTime}
        onClick={() => {
          if (!visible) {
            getVisitor();
          }
          form.resetFields();
          setVisible(true);
        }}
        onCancel={() => {
          form.resetFields();
        }}
      >
        进入登记
      </Button>
      <Modal
        width={1180}
        title="人员进入"
        open={visible}
        okButtonProps={{ disabled: selectedRow.selectedRowKeys.length <= 0, loading: checkLoading }}
        onOk={async () => {
          if (
            selectedRow.selectedRowKeys.some(selectedKey =>
              addedVisitorList.some(existingVisitor => existingVisitor.id === selectedKey)
            )
          ) {
            message.warning('该人员在列表中已存在! ');
            return;
          }

          const visitorRecordIds = selectedRow.selectedRows.map(selectRow => selectRow.id);
          setCheckLoading(true);
          const { error } = await checkExistingVisitsRecords({
            idc: idcTag[0],
            blockGuid: idcTag[1],
            staffIdList: visitorRecordIds,
          });
          setCheckLoading(false);
          if (error) {
            message.error(error.message);
            return;
          }
          onAddVisitor(selectedRow.selectedRows);
          setVisible(false);
          setSelectedRow({ selectedRowKeys: [], selectedRows: [] });
          form.resetFields();
        }}
        onCancel={() => {
          setVisible(false);
          setSelectedRow({ selectedRowKeys: [], selectedRows: [] });
          form.resetFields();
        }}
      >
        <Space style={{ display: 'flex' }} direction="vertical">
          {!isSpecialStaff && (
            <>
              <Form layout="inline">
                <Form.Item label="人员姓名">
                  {getFieldDecorator('name')(<Input allowClear style={{ width: 200 }} />)}
                </Form.Item>
                {!hiddenVoucherCode && (
                  <Form.Item label="凭证码">
                    {getFieldDecorator('authNo')(<Input allowClear style={{ width: 200 }} />)}
                  </Form.Item>
                )}
                <Form.Item label="有效证件号">
                  {getFieldDecorator('identityNo')(<Input allowClear style={{ width: 200 }} />)}
                </Form.Item>
                <Form.Item label="单号">
                  {getFieldDecorator('taskNo')(<Input allowClear style={{ width: 200 }} />)}
                </Form.Item>
                <Form.Item>
                  <Space>
                    <Button type="primary" onClick={handleSearch}>
                      搜索
                    </Button>
                    <Button
                      onClick={() => {
                        form.resetFields();
                        handleSearch();
                      }}
                    >
                      重置
                    </Button>
                  </Space>
                </Form.Item>
              </Form>
              <Table
                rowKey="id"
                columns={columns}
                tableLayout="fixed"
                scroll={{ x: 'max-content' }}
                pagination={false}
                rowSelection={{
                  type: 'checkbox',
                  selectedRowKeys: selectedRow.selectedRowKeys,
                  onChange: onSelectChange,
                  getCheckboxProps: visitor => ({
                    disabled: addedVisitorList.some(
                      existingVisitor => existingVisitor.id === visitor.id
                    ),
                  }),
                }}
                dataSource={tableData}
              />
            </>
          )}
          {isSpecialStaff && (
            <>
              <Table
                rowKey="id"
                columns={[
                  {
                    title: '单号',
                    dataIndex: 'taskNo',
                    render: val =>
                      val ? (
                        <Link
                          target="_blank"
                          href={generateVisitRoutePath({
                            id: val,
                          })}
                        >
                          {val}
                        </Link>
                      ) : (
                        '--'
                      ),
                  },
                  {
                    title: '单位名称',
                    dataIndex: 'companyName',
                  },
                  {
                    title: '接待人',
                    dataIndex: 'name',
                    render(name, o) {
                      const { userId } = o;
                      return userId ? <UserLink userId={userId} userName={name} /> : name;
                    },
                  },
                  {
                    title: '接待人联系方式',
                    dataIndex: 'contactWay',
                  },
                  {
                    title: '申请人数',
                    dataIndex: 'guestNum',
                  },
                ]}
                tableLayout="fixed"
                scroll={{ x: 'max-content' }}
                pagination={false}
                rowSelection={{
                  type: 'checkbox',
                  selectedRowKeys: selectedRow.selectedRowKeys,
                  onChange: onSelectChange,
                  getCheckboxProps: visitor => ({
                    disabled: addedVisitorList.some(
                      existingVisitor => existingVisitor.id === visitor.id
                    ),
                  }),
                }}
                dataSource={specialStaffTableData}
              />
            </>
          )}
        </Space>
      </Modal>
    </>
  );
}

export default Form.create()(AddVisitor);
