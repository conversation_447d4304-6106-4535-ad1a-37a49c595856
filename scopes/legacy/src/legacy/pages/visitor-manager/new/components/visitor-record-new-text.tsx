import React from 'react';
import { useLocation } from 'react-router-dom';

import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';

export function VisitorRecordNewText() {
  const { search } = useLocation();
  const specialStaff = getLocationSearchMap<{ ['specialStaff']?: string }>(search)['specialStaff'];
  return <div>{`${specialStaff === 'true' ? '特殊访客' : '普通访客'}进入登记`}</div>;
}
