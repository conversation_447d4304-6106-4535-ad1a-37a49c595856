import Form from '@ant-design/compatible/es/form';
import moment from 'moment';
import React, { useEffect, useState } from 'react';
import { useHistory, useLocation } from 'react-router-dom';

import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { Link } from '@manyun/dc-brain.navigation.link';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';
import { generateVisitRoutePath } from '@manyun/sentry.route.routes';
import { generateVisitsRecordsRoutePath } from '@manyun/sentry.route.routes';
import { specialVisitorRegistration } from '@manyun/sentry.services.special-visitor-registration';

import { visitorManagerService } from '@manyun/dc-brain.legacy.services';

import AddVisitor from './components/add-visitor';

export { VisitorRecordNewText } from './components/visitor-record-new-text';
export function VisitorRecordNew({ form }) {
  const [loading, setLoading] = useState(false);
  const [visitorList, setVisitorList] = useState([]);
  const history = useHistory();
  const _location = useLocation();
  const searchParams = new URLSearchParams(_location.search);

  const { getFieldDecorator, validateFields, getFieldValue, setFieldsValue } = form;

  const isSpecialStaff = React.useMemo(
    () => searchParams.get('specialStaff') === 'true',
    [searchParams.get('specialStaff')]
  );

  const [configUtil] = useConfigUtil();
  const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket');
  const {
    position = '职位',
    hiddenVoucherCode = false,
    hiddenOperationalAuthorization = false,
    hiddenPersonalItem = false,
  } = ticketScopeCommonConfigs?.entryOfPersons?.features ?? {
    position: '职位',
    hiddenVoucherCode: false,
    hiddenOperationalAuthorization: false,
    hiddenPersonalItem: false,
  };

  useEffect(() => {
    setFieldsValue({ enterTime: moment() });
  }, [setFieldsValue]);

  const handleDelete = record => {
    setVisitorList(prevVisitorList => prevVisitorList.filter(item => item.id !== record.id));
  };

  return (
    <Space style={{ display: 'flex' }} direction="vertical">
      <Card title="基本信息">
        <Form style={{ width: 300 }} colon labelCol={{ span: 7 }} wrapperCol={{ span: 17 }}>
          <Form.Item label="进入区域">
            {getFieldDecorator('idcTag', {
              rules: [
                {
                  required: true,
                  message: '进入区域必填！',
                },
              ],
            })(
              <LocationCascader
                style={{ width: 300 }}
                nodeTypes={['IDC', 'BLOCK']}
                authorizedOnly
                onChange={() => {
                  setVisitorList([]);
                }}
              />
            )}
          </Form.Item>
          <Form.Item label="进入时间">
            {getFieldDecorator('enterTime', {
              rules: [
                {
                  required: true,
                  message: '进入时间必填！',
                },
              ],
            })(
              <DatePicker
                style={{ width: 300 }}
                showTime
                format="YYYY-MM-DD HH:mm:ss"
                allowClear
                disabledDate={current => current > moment().endOf('day')}
                onChange={() => {
                  setVisitorList([]);
                }}
              />
            )}
          </Form.Item>
        </Form>
      </Card>
      <Card title="人员信息">
        <Space style={{ display: 'flex' }} direction="vertical">
          <AddVisitor
            key="add-visitor"
            addedVisitorList={visitorList}
            idcTag={getFieldValue('idcTag')}
            registerTime={getFieldValue('enterTime')}
            onAddVisitor={visitor => {
              setVisitorList(prevVisitorList => [
                ...visitor.map(item => ({ ...item, registerNum: 1 })),
                ...prevVisitorList,
              ]);
            }}
            hiddenVoucherCode={hiddenVoucherCode}
            hiddenOperationalAuthorization={hiddenOperationalAuthorization}
            hiddenPersonalItem={hiddenPersonalItem}
          />
          <Table
            rowKey="id"
            columns={getColumns({
              handleDelete,
              hiddenPersonalItem,
              hiddenOperationalAuthorization,
              isSpecialStaff,
              setVisitorList,
            })}
            tableLayout="fixed"
            scroll={{ x: 'max-content' }}
            dataSource={visitorList}
          />
        </Space>
      </Card>
      <div style={{ height: 50 }} />
      <FooterToolBar>
        <Space>
          <Button
            type="primary"
            loading={loading}
            disabled={!visitorList.length}
            onClick={() => {
              validateFields(async (error, valueMap) => {
                if (error) {
                  return;
                }
                setLoading(true);
                const query = getQ(valueMap, visitorList);
                if (isSpecialStaff && visitorList.some(i => !i.registerNumError)) {
                  setLoading(false);
                  return;
                }
                const response = await createVisitorRecord(query, isSpecialStaff);
                if (response) {
                  setTimeout(() => {
                    history.push(generateVisitsRecordsRoutePath());
                  }, 1.5 * 1000);
                }
                setLoading(false);
              });
            }}
          >
            提交
          </Button>
          <Button
            loading={loading}
            onClick={() => {
              history.goBack();
            }}
          >
            取消
          </Button>
        </Space>
      </FooterToolBar>
    </Space>
  );
}

export default Form.create()(VisitorRecordNew);

function getQ({ idcTag, enterTime }, visitorList) {
  return {
    idcTag: idcTag[0],
    blockGuid: idcTag[1],
    registerTime: enterTime.valueOf(),
    visitorInfos: visitorList,
  };
}

async function createVisitorRecord(data, isSpecialStaff) {
  const { response, error } = await (isSpecialStaff
    ? specialVisitorRegistration({
        ...data,
        visitorInfos: data?.visitorInfos.map(i => ({ id: i.id, registerNum: i.registerNum })),
        entryWay: 'PC',
      })
    : visitorManagerService.fetchVisitorRecordCreate({
        ...data,
        entryWay: 'PC',
      }));

  if (error) {
    message.error(error);
    return;
  }

  message.success('创建成功！');

  return isSpecialStaff ? Promise.resolve(true) : response;
}

const getColumns = ctx => {
  if (ctx.isSpecialStaff) {
    return [
      {
        title: '单位名称',
        dataIndex: 'companyName',
      },
      {
        title: '接待人',
        dataIndex: 'name',
        render(name, o) {
          const { userId } = o;
          return userId ? <UserLink userId={userId} userName={name} /> : name;
        },
      },
      {
        title: '接待人联系方式',
        dataIndex: 'contactWay',
      },
      {
        title: '单号',
        dataIndex: 'taskNo',
        render: val =>
          val ? (
            <Link
              target="_blank"
              href={generateVisitRoutePath({
                id: val,
              })}
            >
              {val}
            </Link>
          ) : (
            '--'
          ),
      },
      {
        title: '申请人数',
        dataIndex: 'guestNum',
      },
      {
        title: '登记人数',
        dataIndex: 'registerNum',
        render: (registerNum, record, index) => {
          return (
            <>
              <InputNumber
                maxLength={6}
                min={1}
                // max={record.guestNum}
                precision={0}
                step={1}
                stringMode={false}
                // value={record.registerNum}
                onChange={value => {
                  // const safeValue = value && value >= 1 ? value : 1;
                  ctx.setVisitorList(prevVisitorList =>
                    prevVisitorList.map(item => {
                      const hasFieldError = value >= 1;
                      if (item.id === record.id) {
                        return { ...item, registerNum: value, registerNumError: hasFieldError };
                      }
                      return item;
                    })
                  );
                }}
              />
              {!record.registerNumError && (
                <div style={{ color: 'var(--manyun-error-color)', marginTop: '2px' }}>
                  请输入登记人数
                </div>
              )}
            </>
          );
        },
      },
      {
        title: '操作',
        dataIndex: 'operation',
        fixed: 'right',
        render: (text, record) => (
          <Button compact type="link" onClick={() => ctx.handleDelete(record)}>
            删除
          </Button>
        ),
      },
    ];
  }
  return [
    {
      title: '人员类型',
      dataIndex: ['visitorType', 'name'],
    },
    {
      title: '姓名',
      dataIndex: 'name',
    },
    {
      title: '证件类型',
      dataIndex: ['certificateType', 'name'],
    },
    {
      title: '证件号码',
      dataIndex: 'identityNo',
    },
    {
      title: '公司名称',
      dataIndex: 'companyName',
    },
    {
      title: '联系方式',
      dataIndex: 'contactWay',
      render: (val, record) => (
        <Space>
          {record.phoneCountryCode ?? ''}
          {val}
        </Space>
      ),
    },
    {
      title: '车牌号',
      dataIndex: 'plateNo',
    },
    {
      title: '随身物品',
      dataIndex: 'personalItem',
      hidden: ctx.hiddenPersonalItem,
      render: val => (val ? val.split(',').filter(Boolean).join('｜') : '--'),
    },
    {
      title: '申请时间',
      dataIndex: 'approveStartTime',
      ellipsis: true,
      render: (approveStartTime, { approveEndTime }) => {
        return (
          moment(approveStartTime).format('YYYY-MM-DD HH:mm') +
          '~' +
          moment(approveEndTime).format('YYYY-MM-DD HH:mm')
        );
      },
    },
    {
      title: '是否具备操作权限',
      dataIndex: 'operable',
      hidden: ctx.hiddenOperationalAuthorization,
      render(operable) {
        if (operable) {
          return '需要';
        }
        return '不需要';
      },
    },
    {
      title: '操作',
      dataIndex: 'operation',
      fixed: 'right',
      render: (text, record) => (
        <Button compact type="link" onClick={() => ctx.handleDelete(record)}>
          删除
        </Button>
      ),
    },
  ].filter(i => !i.hidden);
};
