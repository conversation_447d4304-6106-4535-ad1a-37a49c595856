import dayjs from 'dayjs';
import React from 'react';

import { Modal } from '@manyun/base-ui.ui.modal';
import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useLazyVisitAccessLogs } from '@manyun/sentry.gql.client.visits';

export type LogsModalViewProps = {
  recordId: number;
  idcTag: string;
};
export const LogsModalView = ({ recordId, idcTag }: LogsModalViewProps) => {
  const [visible, setVisible] = React.useState(false);
  const [getVisitAccessLogs, { data, loading, refetch }] = useLazyVisitAccessLogs();
  const [pagination, setPagination] = React.useState<{ page: number; pageSize: number }>({
    page: 1,
    pageSize: 10,
  });

  return (
    <>
      <Typography.Link
        onClick={() => {
          getVisitAccessLogs({
            variables: {
              params: {
                page: 1,
                pageSize: 10,
                idcTag,
                recordId,
              },
            },
          });
          setVisible(true);
        }}
      >
        查看
      </Typography.Link>
      <Modal
        width={1100}
        title="日志"
        open={visible}
        footer={false}
        onCancel={() => {
          setVisible(false);
          setPagination({
            page: 1,
            pageSize: 10,
          });
        }}
      >
        <Table
          loading={loading}
          scroll={{ x: 'max-content' }}
          tableLayout="fixed"
          dataSource={data?.paginationVisitorAccessLog?.data ?? []}
          columns={[
            {
              title: '出入门时间',
              dataIndex: 'eventTime',
              render: val => {
                return val ? dayjs(val).format('YYYY-MM-DD HH:mm:ss') : '--';
              },
            },
            {
              title: '楼栋',
              dataIndex: 'blockTag',
            },
            {
              title: '包间',
              dataIndex: 'roomTag',
            },
            {
              title: '门禁点',
              dataIndex: 'deviceName',
            },
            {
              title: '类型',
              dataIndex: 'inOutType',
              render: val => {
                if (val === 0) {
                  return '出门';
                } else if (val === 1) {
                  return '入门';
                } else if (val === -1) {
                  return '未知';
                } else {
                  return '--';
                }
              },
            },
            {
              title: '备注',
              dataIndex: 'remark',
            },
            {
              title: '数据时间',
              dataIndex: 'receiveTime',
              render: val => {
                return val ? dayjs(val).format('YYYY-MM-DD HH:mm:ss') : '--';
              },
            },
          ]}
          pagination={{
            current: pagination.page,
            pageSize: pagination.pageSize,
            total: data?.paginationVisitorAccessLog?.total ?? 0,
            onChange: (page, pageSize) => {
              setPagination({
                ...pagination,
                page,
                pageSize,
              });
              refetch({
                params: {
                  page,
                  pageSize,
                  idcTag,
                  recordId,
                },
              });
            },
          }}
        />
      </Modal>
    </>
  );
};
