import { FiltersForm, Form, Select } from '@galiojs/awesome-antd';
import moment from 'moment';
import React from 'react';
import { connect } from 'react-redux';
import { useLocation } from 'react-router-dom';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Input } from '@manyun/base-ui.ui.input';
import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';

import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  resetSearchValuesActionCreator,
  visitorRecordActionCreator,
  visitorRecordActions,
} from '@manyun/dc-brain.legacy.redux/actions/visitorManagerActions';

export function SearchForm({ fields, updateSearchValues, mode, onSearch, onReset }) {
  useDefaultSearchParams(updateSearchValues);

  const [form] = Form.useForm();

  return (
    <FiltersForm
      form={form}
      fields={Object.keys(fields).map(name => {
        const field = fields[name];
        return {
          ...field,
          name: name.split('.'),
        };
      })}
      items={[
        {
          label: '姓名',
          name: 'name',
          control: <Input allowClear />,
          hidden: mode !== 1,
        },

        {
          label: '证件号码',
          name: 'identityNo',
          control: <Input allowClear />,
          hidden: mode !== 1,
        },
        {
          label: '进入区域',
          name: 'idcTag',
          control: <LocationCascader nodeTypes={['IDC', 'BLOCK']} authorizedOnly />,
        },
        {
          label: '人员状态',
          name: 'entryStatus',
          control: (
            <Select allowClear>
              <Select.Option value="ENTERED">已入室</Select.Option>
              <Select.Option value="LEAVED">已离开</Select.Option>
              <Select.Option value="NO_LEAVE">禁止离开</Select.Option>
            </Select>
          ),
          hidden: mode !== 1,
        },
        {
          label: '登记进入时间',
          name: 'enterTime',
          span: 2,
          control: (
            <DatePicker.RangePicker
              format="YYYY-MM-DD HH:mm:ss"
              showTime
              placeholder={['开始时间', '结束时间']}
            />
          ),
        },
        {
          label: '登记离开时间',
          name: 'leaveTime',
          span: 2,
          control: (
            <DatePicker.RangePicker
              format="YYYY-MM-DD HH:mm:ss"
              showTime
              placeholder={['开始时间', '结束时间']}
            />
          ),
          hidden: mode !== 1,
        },
        {
          label: '工单单号',
          name: 'taskNo',
          control: <Input allowClear />,
        },
        {
          label: '接待人',
          name: 'usher',
          control: <UserSelect allowClear labelInValue reserveSearchValue />,
          hidden: mode === 1,
        },
      ].filter(i => !i.hidden)}
      onFieldsChange={changedFields => {
        updateSearchValues(
          changedFields.reduce((mapper, field) => {
            const name = field.name.join('.');
            mapper[name] = {
              ...field,
              name,
            };

            return mapper;
          }, {})
        );
      }}
      onSearch={onSearch}
      onReset={onReset}
    />
  );
}

const mapStateToProps = ({ visitorRecord: { searchValues }, mode }) => ({ fields: searchValues });
const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
  updateSearchValues: visitorRecordActions.updateSearchValues,
  onSearch: visitorRecordActionCreator,
  onReset: resetSearchValuesActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(SearchForm);

function useDefaultSearchParams(updateSearchValues) {
  const { search } = useLocation();
  /** @type {import('@manyun/sentry.route.routes').VistsRecordsRouteParams} */
  const { name, ICN, idc, status, enteredAtRange, leftAtRange } = getLocationSearchMap(search, {
    arrayKeys: ['enteredAtRange', 'leftAtRange'],
  });
  React.useEffect(() => {
    const values = {};
    if (name) {
      values.name = {
        value: name,
      };
    }
    if (ICN) {
      values.identityNo = {
        value: ICN,
      };
    }
    if (idc) {
      values.idcTag = {
        value: idc,
      };
    }
    if (status) {
      values.entryStatus = {
        value: status === 'LEFT' ? 'LEAVED' : 'ENTERED',
      };
    }
    if (enteredAtRange) {
      values.enterTime = {
        value: [moment(Number(enteredAtRange[0])), moment(Number(enteredAtRange[1]))],
      };
    }
    if (leftAtRange) {
      values.leaveTime = {
        value: [moment(Number(leftAtRange[0])), moment(Number(leftAtRange[1]))],
      };
    }
    updateSearchValues(values);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
}
