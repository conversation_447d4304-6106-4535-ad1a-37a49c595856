import dayjs from 'dayjs';
import React, { useCallback, useEffect, useState } from 'react';
import { connect } from 'react-redux';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { FilePreviewWithContainer } from '@manyun/base-ui.ui.file-preview';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { Link } from '@manyun/dc-brain.navigation.link';
import { useSpace } from '@manyun/resource-hub.gql.client.spaces';
import {
  DisabledLeftButton,
  MarkAsLeftButton,
  MarkAsLeftModal,
  MarkAsLeftProvider,
} from '@manyun/sentry.page.visits-records';
import { generateVisitRoutePath } from '@manyun/sentry.route.routes';
import { exportVisitsRecords } from '@manyun/sentry.service.export-visits-records';
import { exportRecepRecordVisitor } from '@manyun/sentry.services.export-recep-record-visitor';

import {
  setPaginationThenGetDataActionCreator,
  visitorRecordActionCreator,
  visitorRecordActions,
} from '@manyun/dc-brain.legacy.redux/actions/visitorManagerActions';
import { visitorManagerService } from '@manyun/dc-brain.legacy.services';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';

import { EntrySignatureModalView } from './entry-signature-modal-view';
import { LogsModalView } from './logs-modal-view';

function SpaceText({ guid }) {
  const [spaceIdc, spaceBlock, spaceRoom] = useSpace(guid);
  if (spaceBlock?.label) {
    return <>{spaceBlock.label}</>;
  }

  return <>{guid}</>;
}
function DataTable({
  searchValues,
  data,
  total,
  pageNum,
  pageSize,
  selectedRowKeys,
  loading,
  getData,
  setPagination,
  setSelectedRowKeys,
  resetSelectedRowKeys,
  mode,
}) {
  const [configUtil] = useConfigUtil();

  const {
    showRecycleTime,
    showLogs,
    crewDisableTitle = false,
  } = configUtil.getScopeCommonConfigs('sentry').visitorManager;
  const isSpecialStaff = React.useMemo(() => mode !== 1, [mode]);
  const [authorized] = useAuthorized({ checkByCode: 'element_sentry_visits-records_export' });
  const [disabledLeaveAuthorized] = useAuthorized({
    checkByCode: 'element_sentry-disabled-visited-left',
  });
  const [records, setRecords] = React.useState({
    idc: undefined,
    blockGuid: undefined,
    ids: [],
    enteredAt: undefined,
  });

  useEffect(() => {
    getData();
  }, [getData]);

  const paginationChangeHandler = useCallback(
    (current, size) => {
      setPagination({ pageNum: current, pageSize: size, mode: isSpecialStaff ? mode : undefined });
    },
    [setPagination, isSpecialStaff, mode]
  );
  React.useEffect(() => {
    setPagination({ pageNum: 1, pageSize: 10, mode: isSpecialStaff ? mode : undefined });
  }, [isSpecialStaff, mode]);
  return (
    <MarkAsLeftProvider
      idc={records.idc}
      ids={records.ids}
      enteredAt={records.enteredAt}
      onOk={() => {
        setRecords({ idc: undefined, ids: [], enteredAt: undefined });
        resetSelectedRowKeys();
        getData();

        return Promise.resolve();
      }}
    >
      <MarkAsLeftModal />
      <Space style={{ display: 'flex' }} direction="vertical">
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <Space>
            <Button
              type="primary"
              href={urls.generateVisitorRecordCreateLocation({ specialStaff: isSpecialStaff })}
            >
              进入登记
            </Button>
            {!isSpecialStaff && (
              <MarkAsLeftButton
                type="danger"
                disabled={!selectedRowKeys.length}
                onClick={() => {
                  if (selectedRowKeys.length <= 0) {
                    return;
                  }
                  const selectedRows = [];
                  let lastEnteredAt = 0;
                  data.forEach(record => {
                    if (selectedRowKeys.includes(record.id)) {
                      selectedRows.push(record);
                      const enteredAt = new Date(record.enterTime).valueOf();
                      if (enteredAt > lastEnteredAt) {
                        lastEnteredAt = enteredAt;
                      }
                    }
                  });
                  const selectedIdc = selectedRows[0].idcTag;
                  setRecords({
                    idc: selectedIdc,
                    ids: selectedRowKeys,
                    enteredAt: lastEnteredAt,
                  });
                }}
              >
                离开登记
              </MarkAsLeftButton>
            )}
          </Space>
          {authorized && (
            <FileExport
              filename="人员出入室记录.xls"
              showExportFiltered
              data={async type => {
                const params = {
                  idc: searchValues.idcTag?.value?.[0],
                  name: searchValues.name?.value,
                  ICN: searchValues.identityNo?.value,
                  taskNo: searchValues.taskNo?.value,
                };
                const recepParams = {
                  name: searchValues.usher?.value?.name,
                  idcTag: searchValues.idcTag?.value?.[0],
                  taskNo: searchValues.taskNo?.value,
                };
                /**选了楼栋就不传idcTag */
                if (searchValues.idcTag?.value?.[1]) {
                  params.idc = undefined;
                  params.blockGuidList = [searchValues.idcTag.value[1]];
                  recepParams.blockGuidList = [searchValues.idcTag.value[1]];
                }
                if (!!searchValues.entryStatus?.value) {
                  params.status = searchValues.entryStatus.value === 'LEAVED' ? 'LEFT' : 'ENTERED';
                  recepParams.status =
                    searchValues.entryStatus.value === 'LEAVED' ? 'LEFT' : 'ENTERED';
                }
                if ((searchValues.enterTime?.value?.length ?? 0) >= 2) {
                  params.enteredAtRange = [
                    searchValues.enterTime.value[0].valueOf(),
                    searchValues.enterTime.value[1].valueOf(),
                  ];
                  recepParams.enterStartTime = searchValues.enterTime.value[0].valueOf();
                  recepParams.enterEndTime = searchValues.enterTime.value[1].valueOf();
                }
                if ((searchValues.leaveTime?.value?.length ?? 0) >= 2) {
                  params.leftAtRange = [
                    searchValues.leaveTime.value[0].valueOf(),
                    searchValues.leaveTime.value[1].valueOf(),
                  ];
                }

                const { error, data } = await (isSpecialStaff
                  ? exportRecepRecordVisitor(type === 'filtered' ? recepParams : {})
                  : exportVisitsRecords(type === 'filtered' ? params : undefined));
                if (error) {
                  message.error(error.message);
                  return Promise.reject(error);
                }
                return Promise.resolve(data);
              }}
            />
          )}
        </div>
        <Table
          rowKey="id"
          columns={getColumns({
            setRecords,
            disabledLeaveAuthorized,
            getData,
            showRecycleTime,
            showLogs,
            crewDisableTitle,
            isSpecialStaff,
          })}
          scroll={{ x: 'max-content' }}
          dataSource={data}
          loading={loading}
          rowSelection={
            !isSpecialStaff && {
              selectedRowKeys,
              onChange: keys => {
                setSelectedRowKeys(keys);
              },
              getCheckboxProps: item => {
                const selectedRows = data.filter(({ id }) => selectedRowKeys.includes(id));
                const selectedIdc = selectedRows[0]?.idcTag;

                return {
                  disabled:
                    item.entryStatus.code === 'LEAVED' ||
                    (selectedIdc && item.idcTag !== selectedIdc),
                };
              },
            }
          }
          pagination={{
            total,
            current: pageNum,
            pageSize,
            onChange: paginationChangeHandler,
          }}
          onChange={(pagination, filters, sorter, extra) => {
            // 从 sorter 中提取排序信息
            if (sorter.field) {
              const { field, order } = sorter;
              setPagination({
                mode: isSpecialStaff ? mode : undefined,
                sortField: field,
                sortOrder: order?.toUpperCase(),
              });
            }
          }}
        />
      </Space>
    </MarkAsLeftProvider>
  );
}

const mapStateToProps = ({
  visitorRecord: {
    searchValues,
    data,
    total,
    pagination: { pageNum, pageSize },
    selectedRowKeys,
    loading,
  },
  mode,
}) => ({
  searchValues,
  data,
  total,
  loading,
  pageNum,
  pageSize,
  selectedRowKeys,
});
const mapDispatchToProps = {
  getData: visitorRecordActionCreator,
  setPagination: setPaginationThenGetDataActionCreator,
  setSelectedRowKeys: visitorRecordActions.setSelectedRowKeys,
  resetSelectedRowKeys: visitorRecordActions.resetSelectedRowKeys,
};

export default connect(mapStateToProps, mapDispatchToProps)(DataTable);

const getColumns = ctx => {
  if (ctx.isSpecialStaff) {
    return [
      {
        title: '单位名称',
        dataIndex: 'companyName',
        render(companyName) {
          return (
            <Tooltip placement="topLeft" title={companyName}>
              <Typography.Text style={{ maxWidth: 224 - 32 }} ellipsis>
                {companyName ?? '--'}
              </Typography.Text>
            </Tooltip>
          );
        },
      },
      {
        title: '接待人',
        dataIndex: 'name',
        render(name, o) {
          const { userId } = o;
          return userId ? <UserLink userId={userId} userName={name} /> : name;
        },
      },

      {
        title: '接待人联系方式',
        dataIndex: 'contactWay',
      },
      {
        title: '登记人数/申请人数',
        dataIndex: 'guestNum',
        render(guestNum, o) {
          return (
            <>
              {o.registerNum ?? '--'}/{guestNum ?? '--'}
            </>
          );
        },
      },
      {
        title: '进入区域',
        dataIndex: 'blockGuid',
        render(blockGuid) {
          return <SpaceText guid={blockGuid} />;
        },
      },
      {
        title: '登记进入时间',
        dataIndex: 'enterTime',
        sorter: true,
        render(enterTime) {
          return dayjs(enterTime).format('YYYY-MM-DD HH:mm:ss');
        },
      },
      {
        title: '申请原因',
        dataIndex: 'reason',
        width: 240,
        render(reason) {
          if (!reason) {
            return '--';
          }
          return (
            <Tooltip placement="topLeft" title={reason}>
              <Typography.Text style={{ maxWidth: 240 - 32 }} ellipsis>
                {reason}
              </Typography.Text>
            </Tooltip>
          );
        },
      },
      {
        title: '单号',
        dataIndex: 'taskNo',
        render: val =>
          val ? (
            <Link
              target="_blank"
              href={generateVisitRoutePath({
                id: val,
              })}
            >
              {val}
            </Link>
          ) : (
            '--'
          ),
      },
      {
        title: '登记设备',
        dataIndex: 'entryIp',
        render(reason) {
          if (!reason) {
            return '--';
          }
          return reason;
        },
      },
    ];
  }
  return [
    {
      title: '人员类型',
      dataIndex: ['visitorType', 'name'],
      fixed: 'left',
    },
    {
      title: '姓名',
      dataIndex: 'name',
      fixed: 'left',
    },
    {
      title: '公司名称',
      dataIndex: 'companyName',
      width: 200,
      render(companyName) {
        if (!companyName) {
          return '--';
        }
        return (
          <Tooltip placement="topLeft" title={companyName}>
            <Typography.Text style={{ maxWidth: 200 - 32 }} ellipsis>
              {companyName}
            </Typography.Text>
          </Tooltip>
        );
      },
    },
    {
      title: '职位',
      dataIndex: 'position',
      render: val => val ?? '--',
    },
    {
      title: '联系方式',
      dataIndex: 'contactWay',
      render: (val, record) => (
        <Space>
          {record.phoneCountryCode ?? ''}
          {val}
        </Space>
      ),
    },
    {
      title: '证件类型',
      dataIndex: ['certificateType', 'name'],
      render(identificationTypeText) {
        if (!identificationTypeText) {
          return '--';
        }
        return identificationTypeText;
      },
    },
    {
      title: '证件号码',
      dataIndex: 'identityNo',
      render(identityNo) {
        if (!identityNo) {
          return '--';
        }
        return identityNo;
      },
    },
    {
      title: '进入区域',
      dataIndex: 'blockGuid',
      render: val => {
        const locations = (val ?? '').split('.');
        /**blockGuid 为 ECO6.EC06 展示机房EC06 */
        if (locations[0]) {
          if (locations[0] === locations[1]) {
            return locations[0];
          }
          return val;
        }
        return '--';
      },
    },
    {
      title: '登记进入时间',
      dataIndex: 'enterTime',
      sorter: true,
      render(enterTime) {
        return dayjs(enterTime).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '申请原因',
      dataIndex: 'reason',
      width: 240,
      render(reason) {
        if (!reason) {
          return '--';
        }
        return (
          <Tooltip placement="topLeft" title={reason}>
            <Typography.Text style={{ maxWidth: 240 - 32 }} ellipsis>
              {reason}
            </Typography.Text>
          </Tooltip>
        );
      },
    },
    {
      title: '单号',
      dataIndex: 'taskNo',
      render: val =>
        val ? (
          <Link
            target="_blank"
            href={generateVisitRoutePath({
              id: val,
            })}
          >
            {val}
          </Link>
        ) : (
          '--'
        ),
    },
    {
      title: '登记离开时间',
      dataIndex: 'leaveTime',
      // sorter: true,
      render(leaveTime) {
        if (leaveTime === null) {
          return '--';
        }
        return dayjs(leaveTime).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '人员状态',
      dataIndex: ['entryStatus', 'name'],
    },
    {
      title: '进出设备',
      dataIndex: '_entryIp-outIp',
      render(_, { entryIp, outIp }) {
        if (!entryIp && !outIp) {
          return '--';
        }
        return `${entryIp ?? '--'}/${outIp ?? '--'}`;
      },
    },
    {
      title: '记录照片',
      dataIndex: 'filePath',
      render(filePath) {
        if (!filePath) {
          return '--';
        }

        const filename = '访客照片.png';
        const fileSrc = McUploadFile.generateSrc(filePath, filename);

        return (
          <FilePreviewWithContainer
            file={{
              name: filename,
              src: fileSrc,
              ext: '.png',
            }}
          >
            <Button type="link" compact>
              查看
            </Button>
          </FilePreviewWithContainer>
        );
      },
    },
    {
      title: '来访签字',
      dataIndex: 'signFile',
      render: (signFile, record) => {
        if (!signFile) {
          return '--';
        }
        const filename = '入园签署照片.png';
        const fileSrc = McUploadFile.generateSrc(signFile, filename);
        return <EntrySignatureModalView filePath={fileSrc} />;
      },
    },
    {
      title: '回收时间',
      dataIndex: 'recycleTime',
      hidden: ctx.showRecycleTime !== true,
      render: (val, record) => {
        return (
          <Space>
            {val ? dayjs(val).format('YYYY-MM-DD HH:mm:ss') : '--'}
            {record.recycleType !== null && record.recycleType !== undefined && (
              <Tag color={record.recycleType === 0 ? 'processing' : 'warning'}>
                {record.recycleType === 0 ? '系统' : '手动'}
              </Tag>
            )}
          </Space>
        );
      },
    },
    {
      title: '日志',
      dataIndex: 'logs',
      hidden: ctx.showLogs !== true,
      render: (_, record) => {
        return <LogsModalView recordId={record.id} idcTag={record.idcTag} />;
      },
    },
    {
      title: '操作',
      dataIndex: 'id',
      width: 180,
      fixed: 'right',
      render: (_id, record) => {
        return (
          <Space>
            <MarkAsLeftButton
              compact
              type="link"
              disabled={record.entryStatus.code !== 'ENTERED'}
              onClick={() => {
                ctx.setRecords({
                  idc: record.idcTag,
                  blockGuid: record.blockGuid,
                  ids: [record.id],
                  enteredAt: new Date(record.enterTime).valueOf(),
                });
              }}
            >
              办理离开
            </MarkAsLeftButton>
            <AddBlacklistModalView
              record={record}
              onOk={data => {
                ctx.getData();
              }}
            />
            {ctx.disabledLeaveAuthorized && (
              <DisabledLeftButton
                disabled={record.entryStatus.code !== 'ENTERED'}
                recordId={record.id}
                recordUserName={record.name}
                title={
                  ctx.crewDisableTitle && (
                    <Typography.Text> 人员禁止离开后不可刷脸进出。</Typography.Text>
                  )
                }
                onSuccess={() => {
                  message.success('操作成功！');
                  ctx.getData();
                }}
              />
            )}
          </Space>
        );
      },
    },
  ].filter(item => !item.hidden);
};

const AddBlacklistModalView = ({ record, onOk }) => {
  const [open, setOpen] = useState(false);
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const description = Form.useWatch('description', form);

  return (
    <>
      <Button
        compact
        type="link"
        disabled={record.visitorType.code === 'VIP'}
        onClick={() => {
          setLoading(false);
          form.resetFields();
          setOpen(true);
        }}
      >
        加入黑名单
      </Button>
      <Modal
        width={422}
        open={open}
        title="加入黑名单"
        okButtonProps={{ disabled: !description || loading }}
        onOk={async () => {
          form.validateFields().then(async values => {
            setLoading(true);
            const { error } = await visitorManagerService.fetchCreateVisitorBlacklist({
              description: description,
              id: record.id,
            });
            setOpen(false);
            setLoading(false);
            if (error) {
              message.error(error);
              return;
            }
            message.success('加入黑名单成功！');
            onOk?.();
          });
        }}
        onCancel={() => {
          setOpen(false);
        }}
      >
        <Space style={{ width: '100%' }} direction="vertical" size={10}>
          <Alert type="warning" message={`${record.name}加入黑名单之后将禁止申请访客`} showIcon />
          <Form form={form}>
            <Form.Item
              label="原因"
              name="description"
              rules={[
                {
                  required: true,
                  whitespace: true,
                  message: '原因必填',
                },
                {
                  type: 'string',
                  max: 200,
                  message: '最多输入 200 个字符！',
                },
              ]}
            >
              <Input.TextArea rows={4} />
            </Form.Item>
          </Form>
        </Space>
      </Modal>
    </>
  );
};
