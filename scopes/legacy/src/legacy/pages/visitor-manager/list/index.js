import React from 'react';

import { Tabs } from '@manyun/base-ui.ui.tabs';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';

import DataCard from './components/data-card';
import SearchCard from './components/search-card';

export default function VisitorRecordList() {
  const [configUtil] = useConfigUtil();
  const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket');
  const { dockingStation } =
    // @ts-ignore
    ticketScopeCommonConfigs?.entryOfPersons?.features ?? {
      dockingStation: false,
    };

  const [mode, setMode] = React.useState(1);

  return (
    <GutterWrapper mode="vertical">
      <Tabs
        style={{ backgroundColor: '#fff', padding: '12px 24px' }}
        onChange={v => {
          setMode(v);
        }}
        items={[
          {
            label: dockingStation && `普通访客登记`,
            key: 1,
            children: (
              <>
                <SearchCard mode={mode} />
              </>
            ),
          },
          {
            label: `特殊访客登记`,
            key: 'GOVERNMENT',
            children: (
              <>
                <SearchCard mode={mode} />
              </>
            ),
          },
        ].filter(i => !(!dockingStation && i.key === 'GOVERNMENT'))}
      />
      <DataCard mode={mode} />
    </GutterWrapper>
  );
}
