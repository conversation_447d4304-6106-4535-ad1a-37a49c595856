import { LIMIT_VALUE_MAP } from '@manyun/dc-brain.legacy.constants/alarm';

import { META_TYPE_MAP } from './../constants';

export function getMetaCode(item) {
  return item.deviceType + item.pointCode;
}

export function getKey({ metaType, metaCode, id = '' }) {
  return `${metaType}_$$_${metaCode}_$$_${id}`;
}

export function childNodeDataFormatter(data, parentCode) {
  return {
    metaName: data.name,
    metaCode: getMetaCode(data),
    metaType: META_TYPE_MAP.C4,
    parentCode,
    ...data,
  };
}

export const getLimitsByNotifyRule = notifyRule => {
  let lowerLimit;
  let upperLimit;
  let normalLimits;
  let rule = String(notifyRule);
  if (notifyRule && rule.startsWith('in=')) {
    // DI
    const [, normalLimitsValueStr] = rule.split('=');
    normalLimits = normalLimitsValueStr.split(',');
  } else {
    // AI
    if (!rule.includes(';')) {
      if (
        rule.startsWith(LIMIT_VALUE_MAP.GREATER_THAN) ||
        rule.startsWith(LIMIT_VALUE_MAP.GREATER_THAN_OR_EQUALS)
      ) {
        // 仅配置了阈值下限
        rule += ';';
      } else {
        // 仅配置了阈值上限
        rule = ';' + rule;
      }
    }
    const [lowerLimitStr, upperLimitStr] = rule.split(';');
    if (lowerLimitStr) {
      const lower = lowerLimitStr.split('=');

      lowerLimit = {
        operator: lower[0],
        ...getLimitValue(lower[1]),
      };
    } else {
      lowerLimit = {
        operator: LIMIT_VALUE_MAP.NULL,
      };
    }
    if (upperLimitStr) {
      const upper = upperLimitStr.split('=');
      upperLimit = {
        operator: upper[0],
        ...getLimitValue(upper[1]),
      };
    } else {
      upperLimit = {
        operator: LIMIT_VALUE_MAP.NULL,
      };
    }
  }

  return { lowerLimit, upperLimit, normalLimits };
};

function getLimitValue(limitValueStr) {
  let limit = 0;
  let limitCode = null;
  if (Number.isNaN(Number(limitValueStr))) {
    limitCode = limitValueStr;
  } else {
    limit = Number(limitValueStr);
    limitCode = 'USER_DEFINED';
  }

  return { limit, limitCode };
}
