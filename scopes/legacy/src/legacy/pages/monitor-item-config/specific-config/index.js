import get from 'lodash/get';
import React, { useEffect } from 'react';
import { connect, useDispatch } from 'react-redux';
import { Link, useHistory, useParams } from 'react-router-dom';

import { Card } from '@manyun/base-ui.ui.card';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Tag } from '@manyun/base-ui.ui.tag';
import { syncCommonDataAction } from '@manyun/dc-brain.state.common';
import { TriggerRule, TriggerRuleType } from '@manyun/monitoring.model.trigger-rule';
import { AlarmLevelText } from '@manyun/monitoring.ui.alarm-level-text';
import { TriggerRulesText } from '@manyun/monitoring.ui.trigger-rules-text';
import { useLazyMetadata } from '@manyun/resource-hub.gql.client.resources';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { MetaTypeText } from '@manyun/resource-hub.ui.meta-type-text';
import { useJudgeEventBetaVersion } from '@manyun/ticket.gql.client.tickets';

import { GutterWrapper, TinyCard } from '@manyun/dc-brain.legacy.components';
import { METADATA_TYPE } from '@manyun/dc-brain.legacy.constants/index';
import {
  alarmActions,
  getNotificationTxtTplFieldsAction,
  getViewingMonitorItemActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/alarmActions';
import { fetchDeviceCategory } from '@manyun/dc-brain.legacy.services/treeDataService';
import { getEventTypeName, getLocationSearchMap } from '@manyun/dc-brain.legacy.utils';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';

import { ALARM_CONFIG_ITEM_TYPE_MAP } from './../constants';

export function SpecificConfig({
  fields = [],
  monitorItem,
  getMonitorItem,
  getNotificationTxtTplFields,
  treeNodeMap,
  setMonitorItemTreeNodeMap,
  eventTypeData,
}) {
  const dispatch = useDispatch();
  const { configId } = useParams();
  const { location } = useHistory();

  const [getMetaDataRes, { data: metaDataRes }] = useLazyMetadata({
    variables: { type: MetaType.N_EVENT_CATEGORY },
    fetchPolicy: 'cache-and-network',
  });

  const [judgeEventBetaVersion, { data: eventBetaVersion }] = useJudgeEventBetaVersion({
    onCompleted(data) {
      if (data.judgeEventBetaVersion?.data) {
        getMetaDataRes();
      }
    },
  });

  const params = { id: configId };
  const { pointCode, deviceType } = getLocationSearchMap(location.search, [
    'deviceType',
    'pointCode',
  ]);
  if (pointCode) {
    params.pointCode = pointCode;
  }
  if (deviceType) {
    params.deviceType = deviceType;
  }
  useEffect(() => {
    getNotificationTxtTplFields();
    getMonitorItem(params);

    if (!treeNodeMap) {
      fetchData();
      async function fetchData() {
        const data = await fetchDeviceCategory();
        if (data) {
          setMonitorItemTreeNodeMap(data.normalizedList);
        }
      }
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [configId]);

  useEffect(() => {
    dispatch(syncCommonDataAction({ strategy: { eventTypes: 'FORCED' } }));
  }, [dispatch]);

  useEffect(() => {
    if (monitorItem && monitorItem.source === 'LOCAL') {
      const [idcTag] = monitorItem.blockGuid.split('.');
      judgeEventBetaVersion({ variables: { guid: idcTag } });
    }
  }, [monitorItem, judgeEventBetaVersion]);

  const itemType = get(monitorItem, ['itemType', 'code'], '');

  const isSinglePoint =
    itemType === ALARM_CONFIG_ITEM_TYPE_MAP.SINGLE_POINT || itemType === 'CUSTOM';
  let eventTypes;
  if (monitorItem && !monitorItem.createIncident) {
    eventTypes = `--`;
  }
  if (monitorItem && monitorItem.createIncident) {
    const eventTypeLevel1 = getEventTypeName(
      eventTypeData,
      monitorItem.eventTopCategory,
      METADATA_TYPE.EVENT_TOP_CATEGORY
    );
    const eventTypeLevel2 = getEventTypeName(
      eventTypeData,
      monitorItem.eventSecondCategory,
      METADATA_TYPE.EVENT_SECOND_CATEGORY
    );
    eventTypes = eventTypeLevel1 + '/' + eventTypeLevel2;
  }
  if (monitorItem?.source === 'LOCAL') {
    if (
      (metaDataRes?.metadata ?? []).filter(item => item.code === monitorItem.eventTopCategory)
        .length &&
      monitorItem.eventTopCategory
    ) {
      eventTypes = (
        <MetaTypeText metaType={MetaType.N_EVENT_CATEGORY} code={monitorItem.eventTopCategory} />
      );
    } else {
      eventTypes = '--';
    }
  }

  return (
    <GutterWrapper mode="vertical">
      <TinyCard title="基本配置">
        {monitorItem && (
          <Descriptions column={4}>
            <Descriptions.Item label="告警项名称">{monitorItem.name}</Descriptions.Item>
            <Descriptions.Item label="是否启用">
              {monitorItem.available ? '启用' : '禁用'}
            </Descriptions.Item>
            <Descriptions.Item label="告警级别">
              <AlarmLevelText code={monitorItem.alarmLevel} />
            </Descriptions.Item>
            <Descriptions.Item label="告警类型">{monitorItem.alarmType.name}</Descriptions.Item>
            <Descriptions.Item
              label={eventBetaVersion?.judgeEventBetaVersion?.data ? '专业分类' : '事件类型'}
            >
              {eventTypes}
            </Descriptions.Item>
            <Descriptions.Item label="是否自动创建事件">
              {monitorItem.createIncident ? '是' : '否'}
            </Descriptions.Item>
            <Descriptions.Item label="所属模板">
              <Link
                to={urls.generateAlarmConfigurationTemplateDetailUrl({
                  id: monitorItem.groupId,
                })}
              >
                {monitorItem.groupName}
              </Link>
            </Descriptions.Item>
          </Descriptions>
        )}
      </TinyCard>
      <TinyCard title="告警项配置">
        {isSinglePoint && monitorItem && (
          <Descriptions column={4}>
            <Descriptions.Item label="监控测点名称"> {monitorItem.name}</Descriptions.Item>
            <Descriptions.Item label="触发观察周期">
              {typeof monitorItem.triggerInterval === 'number'
                ? `${monitorItem.triggerInterval}秒`
                : '--'}
            </Descriptions.Item>
            <Descriptions.Item label="前置条件">
              <TriggerRulesText
                type={TriggerRuleType.PreCondition}
                triggerRules={(monitorItem?.triggerRules || [])
                  .map(rule => TriggerRule.fromApiObject(rule))
                  .map(rule => rule.toJSON())}
              />
            </Descriptions.Item>
            <Descriptions.Item label="告警条件">
              <TriggerRulesText
                type={TriggerRuleType.AlarmCondition}
                triggerRules={(monitorItem?.triggerRules || [])
                  .map(rule => TriggerRule.fromApiObject(rule))
                  .map(rule => rule.toJSON())}
              />
            </Descriptions.Item>
            <Descriptions.Item label="恢复观察周期">
              {typeof monitorItem.recoverInterval === 'number'
                ? `${monitorItem.recoverInterval}秒`
                : '--'}
            </Descriptions.Item>
          </Descriptions>
        )}
      </TinyCard>
      <Card title="处置建议">{monitorItem?.description ?? ''}</Card>
      <TinyCard title="告警文案配置">
        {monitorItem && (
          // <Descriptions>
          //   <Descriptions.Item label="告警通知文案">{monitorItem.notifyRule}</Descriptions.Item>
          // </Descriptions>
          <div style={{ borderColor: 'var(--border-color-special)' }}>
            {fields.map(({ id, label, value, text }) => {
              if (value === 'CUSTOMIZED_TEXT') {
                return <Tag key={id}>{text}</Tag>;
              }
              return (
                <Tag key={id} color="blue">
                  {label}
                </Tag>
              );
            })}
          </div>
        )}
      </TinyCard>
    </GutterWrapper>
  );
}

const mapStateToProps = ({
  alarmManage: { viewingMonitorItem, notificationTpl, monitorItemTreeNodeMap },
  common: { eventTypes },
}) => {
  let eventTypeData = {};
  if (eventTypes) {
    eventTypeData = eventTypes.normalizedList;
  }
  return {
    monitorItem: viewingMonitorItem,
    fields: notificationTpl,
    treeNodeMap: monitorItemTreeNodeMap,
    eventTypeData,
  };
};
const mapDispatchToProps = {
  getMonitorItem: getViewingMonitorItemActionCreator,
  getNotificationTxtTplFields: getNotificationTxtTplFieldsAction,
  setMonitorItemTreeNodeMap: alarmActions.setMonitorItemTreeNodeMap,
  syncCommonData: syncCommonDataAction,
};

export default connect(mapStateToProps, mapDispatchToProps)(SpecificConfig);
