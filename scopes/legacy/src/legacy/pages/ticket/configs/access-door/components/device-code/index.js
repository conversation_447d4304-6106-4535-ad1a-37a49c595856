import React from 'react';

import { Input } from '@manyun/base-ui.ui.input';

export function DeviceCodeInput({ forwardedRef, getFieldDecorator, ...props }) {
  return (
    <Input
      ref={forwardedRef}
      allowClear
      {...props}
      value={props.value ? props.value.join(' ') : props.value}
      onChange={({ target: { value } }) => {
        const list = value.split(' ');
        const newlist = list.filter((item, index) => {
          if (index === list.length - 1) {
            return true;
          }
          if (item) {
            return true;
          }
          return false;
        });
        props.onChange(newlist);
      }}
    />
  );
}

export default React.forwardRef((props, ref) => <DeviceCodeInput forwardedRef={ref} {...props} />);
