export function insertSecondAndFirsetCode({ data, deviceObj }) {
  let newData = [];
  newData = data.map(item => {
    let tmp = { ...item };
    if (deviceObj && deviceObj[item.deviceType]) {
      tmp.thirdCategoryName = deviceObj[item.deviceType].metaName;
      if (deviceObj[item.deviceType].parentCode.indexOf('C1') !== -1) {
        tmp.secondCategory = deviceObj[item.deviceType].parentCode.replace('C1', '');
        tmp.secondCategoryName = deviceObj[tmp.secondCategory]?.metaName;
      }
      if (
        deviceObj[tmp.secondCategory] &&
        deviceObj[tmp.secondCategory].parentCode.indexOf('C0') !== -1
      ) {
        tmp.topCategory = deviceObj[tmp.secondCategory].parentCode.replace('C0', '');
        tmp.topCategoryName = deviceObj[tmp.topCategory]?.metaName;
      }
    } else {
      tmp.thirdCategoryName = item.spareType;
      tmp.secondCategory = item.secondCategory;
      tmp.secondCategoryName = item.secondCategory;
      tmp.topCategory = item.topCategory;
      tmp.topCategoryName = item.topCategory;
      item.deviceType = item.spareType;
    }

    return tmp;
  });
  return newData;
}

export function getFileInfoList(fileList) {
  if (!Array.isArray(fileList)) {
    return [];
  }
  return fileList.map(file => {
    return {
      fileFormat: file.fileFormat || file.ext,
      fileName: file.fileName || file.name,
      filePath: file.filePath || file.patialPath,
      fileSize: file.fileSize || file.size,
      uploadBy: file.uploadBy || file.uploadUser.id,
      uploadByName: file.uploadByName || file.uploadUser.name,
      uploadTime: file.uploadTime || file.uploadedAt,
    };
  });
}
