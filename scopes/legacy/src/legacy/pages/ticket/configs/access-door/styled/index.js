import React from 'react';

import classNames from 'classnames';

import { Typography } from '@manyun/base-ui.ui.typography';

import { McUpload } from '@manyun/dc-brain.ui.upload';

import styles from './mc-upload.modules.less';

export const StyledMcUpload = React.forwardRef(({ showAccept = false, ...props }, ref) => {
  return (
    <McUpload className={classNames(props.className, styles.upload)} {...props}>
      {props.children}
      <p style={{ marginTop: 8 }}>
        {showAccept && (
          <Typography.Text type="secondary">支持扩展名：{props.accept}</Typography.Text>
        )}
      </p>
    </McUpload>
  );
});
