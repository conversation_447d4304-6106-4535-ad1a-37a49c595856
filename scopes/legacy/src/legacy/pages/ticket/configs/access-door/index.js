import React from 'react';

import { BackendTaskStatus } from '@manyun/ticket.model.ticket';
import { TicketStatusSelect } from '@manyun/ticket.ui.ticket-status-select';

import { CURRENT_PAGE_TYPES } from '../../constants';
import TTR from './../../registries/ticket-type-registry';
import Detail from './components/detail';
import Print from './components/detail/conponents/print';
import DeviceCodeInput from './components/device-code';
import NewTicket from './components/new';

TTR.registerTicketType('access')
  .registerConfig({
    type: CURRENT_PAGE_TYPES.TICKETS,
    showNewBtn: true,
    listServiceEndpoint: 'fetchAccessList',

    showResendOperation: true,
    showRevokeOperation: true,
    /**
     * @param {object} param1
     * @param {typeof import('./../../views/tickets/constants').DEFAULT_FILTER_KEY_MAP} param1.DEFAULT_FILTER_KEY_MAP
     * @param {any[]} param1.defaultColumns
     */
    mergeFilters: ({ DEFAULT_FILTER_KEY_MAP, defaultFilters }) => {
      const newFilters = [...defaultFilters].filter(
        ({ key }) => key !== DEFAULT_FILTER_KEY_MAP.END_TIME
      );
      newFilters.push({
        label: '设备编号',
        key: 'guidList',
        Comp: DeviceCodeInput,
      });
      newFilters.splice(
        newFilters.findIndex(item => item.key === 'taskStatusList'),
        1,
        {
          label: '工单状态',
          key: 'taskStatusList',
          initialProps: {
            optionFilter: option => option.value !== BackendTaskStatus.CLOSE_APPROVER,
            allowClear: true,
            mode: 'multiple',
          },
          Comp: TicketStatusSelect,
        }
      );
      return newFilters;
    },
  })
  .registerConfig({
    type: CURRENT_PAGE_TYPES.NEW_TICKET,
    content: NewTicket,
  })
  .registerConfig({
    type: CURRENT_PAGE_TYPES.SPECIFIC_TICKET,
    content: Detail,
    showRevocationBtn: true,
    showReapprovalBtn: true,
    tabBarExtraContent: value => {
      if (value === 'content') {
        return <Print />;
      }
    },
  });
