import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';

import template from 'lodash/template';
import moment from 'moment';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';

import { fetchBpmInstance } from '@manyun/bpm.service.fetch-bpm-instance';

import PrintFile from '@manyun/dc-brain.legacy.components/print-file';

function Print({ info }) {
  const [relateBpmData, setRelateBpmData] = useState();
  const [isRelateBpmEffected, setIsRelateBpmEffected] = useState(false);
  useEffect(() => {
    (async function () {
      if (info.workFlowId) {
        setIsRelateBpmEffected(true);
        const { error, data } = await fetchBpmInstance({ code: info.workFlowId });
        if (error) {
          return;
        }
        setRelateBpmData(data);
      } else {
        setIsRelateBpmEffected(false);
      }
    })();
  }, [info?.workFlowId]);
  const contents = useDeepCompareMemo(() => {
    let bpmRecords = [];
    if (isRelateBpmEffected) {
      if (relateBpmData) {
        bpmRecords = relateBpmData.operationRecords
          .filter(record => Array.isArray(record.operationTasks))
          .map(record => {
            const operationTasks = record.operationTasks;
            const userOperations = [];
            for (const task of operationTasks) {
              if (Array.isArray(task.userList) && task.userList.length > 0) {
                for (const user of task.userList) {
                  userOperations.push({
                    userName: user.userName,
                    remark: task.remark ?? '--',
                    taskResult: task.taskResult,
                    processDate: task.date ? moment(task.date).format('YYYY-MM-DD HH:mm:ss') : '--',
                  });
                }
              }
            }
            return {
              nodeName: record.operationName,
              userOperations: userOperations,
            };
          });
      }
    }

    const obj = template(
      `<style>
          h3{
            margin-bottom: 0px;
    
          }
          td{
            padding:4px;
            text-align: left ;

          }
          table {
            text-align: center ;
            width:920px
          }
          thead > tr {
            height:55px
          }
          .remark{
            font-weight:700;
    
          }
          .device {
            height:35px
          }
          
          </style>
          <table border='1' cellspacing='0' cellpadding='0' align='center'>
            <thead>
                <tr>
                    <th colSpan="12"><%- info.taskTitle %></th>
                </tr>
            </thead>
            <tbody>
                <tr>
                <td class='remark'  colSpan="2">工单单号</td>
                    <td   colSpan="2"><%- info.taskNo %></td>
                    <td   colSpan="2"class='remark'>申请人</td>
                    <td colSpan="2"><%- info.creatorName %></td>
                    <td class='remark'  colSpan="1">授权时间范围</td>
                    <td  colSpan="3"><%- info.accessDoorTime %></td>
                </tr>
                <% if(info.assetType === 'CLIENT_ASSET') {%> <tr>                
                <td class='remark'  colSpan="2">客户确认人姓名</td>
                <td   colSpan="2"><%- info.confirmStaffName %></td>
                <td   colSpan="2 "class='remark'>客户所属单位</td>
                <td colSpan="5"><%- info.departmentName %></td>
                </tr> <%}
                else {%><%} 
                %>
                <tr class='device'>
                    <td align="left" colSpan="12"><h3><%-info.prefix %>清单</h3></td>
                </tr>
                <tr>
                    <td align="left" colSpan="4"><%- info.assetType === 'IDC_ASSET'? "类型":"物资分类" %></td>
                    <% if(info.assetType === 'IDC_ASSET') {%>
                      
                      <td align="left" colSpan="2">资产ID</td>

                      <%}
                    else {%><%} 
                    %>
                    <td align="left" colSpan="2">SN</td>
                    <td align="left" colSpan="2">品牌</td>
                    <td align="left" colSpan="1">型号</td>
                    <td align="left" colSpan="1">数量</td>
    
                </tr>
               <%  info.codeData.forEach((device) => { %><tr>
                <td align="left" colSpan="4">
               <%- device.sortType %></td>
               <% if(info.assetType === 'IDC_ASSET') {%>
                      
                <td align="left" colSpan="2"><%- device.assetNo %></td>

                <%}
              else {%><%} 
              %>
               <td align="left" colSpan="2"><%- device.serialNumber %></td>
               <td align="left" colSpan="2"><%- device.brand %></td>
               <td align="left" colSpan="1"><%- device.model %></td>
               <td align="left" colSpan="1"><%- device.sum %></td>
               </tr><% } ); %>

            <% if(bpmRecords.length === 0) {%><%} else {%>
              <tr class='device'>
              <td align="left" colSpan="12"><h3>审批记录</h3></td>
              <tr>
              <td align="left" colSpan="2">节点名称</td>
              <td align="left" colSpan="2">审批人</td>
              <td align="left" colSpan="2">审批时间</td>
              <td align="left" colSpan="2">审批结果</td>
              <td align="left" colSpan="4">备注</td>
              <%  bpmRecords.forEach((record) => { %>
             
              <% record.userOperations.forEach((user,index)=>{ %>
               <tr>
               <% if(index=== 0)  { %> <td align="left" colSpan="2" rowSpan=<%-index === 0?record.userOperations.length:"1"%>><%- record.nodeName %></td>  <%} else {%><%} %>                <td align="left" colSpan="2"><%- user.userName %></td>
              <td align="left" colSpan="2"><%- user.processDate %></td>
              <td align="left" colSpan="2"><%- user.taskResult %></td>
              <td align="left" colSpan="4"><%- user.remark %></td>
              </tr>
               <%}) %>
              
              </tr><% } ); %>
          
          </tr>
              
              <%} %>   
            </tbody>
            <tr >
            <td align="left" colSpan="12"><h3>安保核查</h3></td>
        </tr>
            <tr style='height:115px;line-heighe:115px'>
                <td align="left" colSpan="6">姓名：_________________________________</td>
                <td align="left" colSpan="6">日期：_________________________________</td>
            </tr>
          </table>`
    );
    const str = obj({
      info,
      bpmRecords,
    });
    return str;
  }, [info, relateBpmData]);

  return contents ? <PrintFile contents={contents} /> : null;
}

const mapStateToProps = ({
  common: { deviceCategory },

  ticket: {
    ticketView: { basicInfo },
    access_door: {
      detail: { codeData, noCodeData },
    },
  },
}) => {
  let info = {
    taskTitle: basicInfo.taskTitle,
    creatorName: basicInfo.creatorName,
    workFlowId: basicInfo.workFlowId,
    taskNo: basicInfo.taskNo,
    taskAssigneeName: basicInfo.taskAssigneeName,
    codeData: codeData.map(item => ({
      sortType: `${item.materialType === 'ON_LINE' ? '线上物资' : '线下物资'}/${item.deviceType}`,
      assetNo: getDefalutContentDisplay(item.assetNo),
      serialNumber: getDefalutContentDisplay(item.serialNo),
      brand: getDefalutContentDisplay(item.vendor),
      model: getDefalutContentDisplay(item.productModel),
      sum: item.accessCount,
      materialType: item.materialType,
    })),
  };
  if (basicInfo && basicInfo.taskProperties) {
    info = {
      ...info,
      ...JSON.parse(basicInfo.taskProperties),
    };
  }
  return {
    info: {
      ...info,
      prefix: basicInfo.taskSubType === 'GO_IN' ? '入门' : '出门',
      accessDoorTime: info.accessDoorEndTime
        ? `${moment(info.accessDoorTime).format('YYYY-MM-DD HH:mm')}~${moment(
            info.accessDoorEndTime
          ).format('YYYY-MM-DD HH:mm')}`
        : moment(info.accessDoorTime).format('YYYY-MM-DD'),
    },
  };
};
export default connect(mapStateToProps, null)(Print);

function getDefalutContentDisplay(text) {
  return (text === '') | (text === null) ? '--' : text;
}
