import React from 'react';

import PlusCircleFilled from '@ant-design/icons/es/icons/PlusCircleFilled';
import { EditableTable, Form } from '@galiojs/awesome-antd';
import { generateGetRowSpan } from '@galiojs/awesome-antd/lib/table/utils';
import cloneDeep from 'lodash.clonedeep';
import shortid from 'shortid';

import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Radio } from '@manyun/base-ui.ui.radio';

const codeTyle = [
  {
    value: 1,
    label: '是',
  },
  {
    value: 0,
    label: '否',
  },
];

class ClientDeviceModalLegacy extends React.Component {
  state = {
    dataInModal: [],
    editingRowKey: null,
    deleteByCancel: false,
    initialSaved: null,
  };

  componentDidMount() {
    const id = shortid();
    this.setState({
      dataInModal: [
        {
          id: id,
          num: null,
          needCode: 1,
          deviceType: null,
          code: null,
          mergeRowsKey: id,
        },
      ],
      editingRowKey: id,
      deleteByCancel: false,
      initialSaved: id,
    });
  }

  addRow = (rowData, insertIdx) => {
    const { dataInModal } = this.state;
    const nextData = [...dataInModal];
    nextData.splice(insertIdx, 0, rowData);
    this.setState({
      deleteByCancel: true,
      dataInModal: nextData,
      editingRowKey: rowData.id,
      initialSaved: rowData.id,
    });
  };

  render() {
    const { form, visible, updateVisible } = this.props;
    const { dataInModal, editingRowKey, deleteByCancel, initialSaved } = this.state;
    return (
      <Modal
        visible={visible}
        title="添加设备"
        width={1100}
        bodyStyle={{ maxHeight: '452px', overflowY: 'auto' }}
        onCancel={() => {
          this.setState({
            dataInModal: [],
            editingRowKey: null,
            deleteByCancel: false,
            initialSaved: null,
          });
          updateVisible(false);
        }}
        onOk={() => {
          if (editingRowKey) {
            message.error('当前还有未保存得设备，请先保存！');
            return;
          }
          updateVisible(false);
          this.props.setClientTableData(dataInModal);
        }}
      >
        <EditableTable
          form={form}
          rowKey="id"
          showActionsColumn
          dataSource={dataInModal}
          columns={getColumns({
            editingRowKey,
            dataInModal,
            addRow: this.addRow,
            getRowSpan: generateGetRowSpan(dataInModal),
          })}
          editingRowKey={editingRowKey}
          onSave={(_currentKey, records) => {
            this.setState({
              dataInModal: records,
              editingRowKey: null,
              deleteByCancel: false,
              initialSaved: null,
            });
          }}
          onEdit={rowKey => this.setState({ editingRowKey: rowKey })}
          onCancel={() => {
            if (deleteByCancel) {
              const nextData = cloneDeep(dataInModal);
              const idx = nextData.findIndex(record => record.id === editingRowKey);
              if (idx > -1) {
                nextData.splice(idx, 1);
              }
              this.setState({
                dataInModal: nextData,
                editingRowKey: null,
                initialSaved: null,
                deleteByCancel: false,
              });
              return;
            }
            if (!initialSaved) {
              this.setState({ editingRowKey: null });
            } else {
              message.error('第一条初始化数据未保存前不可取消！');
            }
          }}
          onDelete={(rowKey, data) => {
            if (!data.length) {
              message.error('仅有一条数据时不可删除！');
              return;
            }
            let editKey = editingRowKey;
            if (rowKey === editingRowKey) {
              editKey = null;
            }
            this.setState({ dataInModal: data, editKey, editingRowKey: editKey });
          }}
        />
      </Modal>
    );
  }
}

function ClientDeviceModal(props) {
  const [form] = Form.useForm();

  return <ClientDeviceModalLegacy form={form} {...props} />;
}

export default ClientDeviceModal;

const getColumns = ({ editingRowKey, dataInModal, addRow, getRowSpan }) => [
  {
    title: '是否具备编号',
    dataIndex: 'needCode',
    editable: true,
    editingCtrl: (
      <Radio.Group>
        {codeTyle.map(item => {
          return (
            <Radio key={item.value} value={item.value}>
              {item.label}
            </Radio>
          );
        })}
      </Radio.Group>
    ),
    formItemProps: {
      rules: [
        {
          required: true,
          message: '是否具备编号必选！',
        },
      ],
    },
    render: (needCode, record, idx) => {
      let children = '否';
      if (needCode === 1) {
        children = '是';
      }
      if (idx === 0) {
        children = (
          <span>
            <PlusCircleFilled
              style={{ marginRight: 8 }}
              onClick={() => {
                const key = shortid();
                addRow({
                  mergeRowsKey: key,
                  id: key,
                  num: null,
                  needCode: 1,
                  deviceType: null,
                  code: null,
                });
              }}
            />
            {children}
          </span>
        );
      }
      return {
        children,
        props: {
          rowSpan: getRowSpan(record, idx),
        },
      };
    },
  },
  {
    title: '类型',
    dataIndex: 'deviceType',
    editable: true,
    editingCtrl: <Input allowClear />,
    formItemProps: {
      rules: [
        {
          required: true,
          message: '类型必填！',
        },
        {
          type: 'string',
          max: 32,
          message: '最多输入 32 个字符！',
        },
      ],
    },
  },
  {
    title: '编号',
    dataIndex: 'code',
    editable: true,
    editingCtrl: fieldsValue => {
      let disabled = false;
      if (!fieldsValue.needCode || fieldsValue.needCode === 0) {
        disabled = true;
      }
      if (disabled) {
        return '--';
      }
      return <Input allowClear />;
    },
    formItemProps: {
      dependencies: ['needCode', 'deviceType'],
      rules: [
        form => ({
          validator: (_rule, code) => {
            const needCode = form.getFieldValue('needCode');
            const deviceType = form.getFieldValue('deviceType');

            if (needCode === 1 && !code) {
              return Promise.reject('编号必填！');
            }

            const sameData = dataInModal.filter(
              item =>
                item.id !== editingRowKey && item.code === code && item.deviceType === deviceType
            );
            if (sameData.length) {
              return Promise.reject('编号已经存在!');
            }

            return Promise.resolve();
          },
        }),
        {
          type: 'string',
          max: 46,
          message: '最多输入 46 个字符！',
        },
      ],
    },
    render: (code, record) => {
      if (record.needCode === 1) {
        return code;
      }
      return '--';
    },
  },
  {
    title: '数量',
    dataIndex: 'num',
    editable: true,
    editingCtrl: fieldsValue => {
      let disabled = true;
      if (fieldsValue.needCode === 0) {
        disabled = false;
      }
      if (disabled) {
        return '--';
      }
      return <InputNumber min={1} precision={0} />;
    },
    formItemProps: {
      dependencies: ['needCode'],
      rules: [
        form => ({
          validator: (_rule, num) => {
            const needCode = form.getFieldValue('needCode');
            if (needCode === 0 && !num) {
              return Promise.reject('数量必填！');
            }

            return Promise.resolve();
          },
        }),
      ],
    },
    render: (num, record) => {
      if (record.needCode === 0) {
        return num;
      }
      return '--';
    },
  },
];
