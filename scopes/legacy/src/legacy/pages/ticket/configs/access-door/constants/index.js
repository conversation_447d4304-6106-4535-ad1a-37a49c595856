// 资产类型
/**@deprecated 请使用ticket scope下model中定义的类型，后续会重构@lyd */
export const ASSET_TYPE_KEY_MAP = {
  CLIENT_ASSET: 'CLIENT_ASSET',
  IDC_ASSET: 'IDC_ASSET',
  SUPPLIER_ASSET: 'SUPPLIER_ASSET',
};
export const ASSET_TYPE_TEXT_MAP = {
  [ASSET_TYPE_KEY_MAP.CLIENT_ASSET]: '客户资产',
  [ASSET_TYPE_KEY_MAP.IDC_ASSET]: '机房资产',
  [ASSET_TYPE_KEY_MAP.SUPPLIER_ASSET]: '供应商资产',
};
export const ASSET_TYPE_OPTIONS = [
  {
    value: ASSET_TYPE_KEY_MAP.CLIENT_ASSET,
    label: ASSET_TYPE_TEXT_MAP[ASSET_TYPE_KEY_MAP.CLIENT_ASSET],
  },
  {
    value: ASSET_TYPE_KEY_MAP.IDC_ASSET,
    label: ASSET_TYPE_TEXT_MAP[ASSET_TYPE_KEY_MAP.IDC_ASSET],
  },
  {
    value: ASSET_TYPE_KEY_MAP.SUPPLIER_ASSET,
    label: ASSET_TYPE_TEXT_MAP[ASSET_TYPE_KEY_MAP.SUPPLIER_ASSET],
  },
];

// 出入门类型
export const ACCESS_TYPE_KEY_MAP = {
  GO_OUT: 'GO_OUT',
  GO_IN: 'GO_IN',
};
