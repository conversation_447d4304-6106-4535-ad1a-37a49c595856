import React from 'react';
import { connect } from 'react-redux';

import moment from 'moment';
import shortid from 'shortid';

import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';

import { AssetsTable } from '@manyun/resource-hub.ui.assets-table';

import { TinyDescriptions } from '@manyun/dc-brain.legacy.components';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import { ticketActions } from '@manyun/dc-brain.legacy.redux/actions/ticketActions';
import * as taskCenterService from '@manyun/dc-brain.legacy.services/taskCenterService';

import { ACCESS_TYPE_KEY_MAP, ASSET_TYPE_KEY_MAP, ASSET_TYPE_TEXT_MAP } from '../../constants';

export class TicketDetail extends React.Component {
  componentDidMount() {
    this.props.syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });
    this.getCodeData();
    this.getNoCodeData();
  }

  getCodeData = async () => {
    const { error, response } = await taskCenterService.fetchAccessDeviceList({
      taskNo: this.props.taskNo,
      haveSerialNo: true,
    });
    if (error) {
      message.error(error);
    }
    if (response) {
      this.props.setAccsssDoorCodeDeviceData(response.data);
    }
  };

  getNoCodeData = async () => {
    const { error, response } = await taskCenterService.fetchAccessDeviceList({
      taskNo: this.props.taskNo,
      haveSerialNo: false,
    });
    if (error) {
      message.error(error);
    }
    if (response) {
      const newData = response.data.map(item => {
        return {
          ...item,
          id: shortid(),
        };
      });
      this.props.setAccsssDoorNoCodeDeviceData(newData);
    }
  };

  render() {
    const { info, codeData } = this.props;
    const isClientAsset = info.assetType === ASSET_TYPE_KEY_MAP.CLIENT_ASSET;
    const isIdcAsset = info.assetType === ASSET_TYPE_KEY_MAP.IDC_ASSET;
    const descriptionsItems = [
      {
        label: '授权时间范围',
        value: info.accessDoorEndTime
          ? `${moment(info.accessDoorTime).format('YYYY-MM-DD HH:mm')}~${moment(
              info.accessDoorEndTime
            ).format('YYYY-MM-DD HH:mm')}`
          : moment(info.accessDoorTime).format('YYYY-MM-DD'),
      },
      {
        label: `${info.taskSubTypeTimeTxt}原因`,
        value: info.accessDoorReason,
      },
      {
        label: '资产类型',
        value: ASSET_TYPE_TEXT_MAP[info.assetType],
      },
    ];
    if (isClientAsset) {
      descriptionsItems.splice(2, 0, {
        label: '客户确认人姓名',
        value: info.confirmStaffName ?? '--',
      });
      descriptionsItems.splice(3, 0, { label: '客户所属单位', value: info.departmentName ?? '--' });
    }
    const assets = codeData.map(item => ({
      sortType: item.deviceType,
      assetNo: item.assetNo,
      serialNumber: item.serialNo !== '' ? item.serialNo : null,
      brand: item.vendor !== '' ? item.vendor : null,
      model: item.productModel !== '' ? item.productModel : null,
      sum: item.accessCount,
      materialType: item.materialType,
    }));
    return (
      <>
        <TinyDescriptions column={4} descriptionsItems={descriptionsItems} />
        <Space direction="vertical" style={{ width: '100%' }}>
          <AssetsTable
            mode="viewer"
            viewType={isIdcAsset ? 'idc' : 'other'}
            parentDataSource={assets}
          />
        </Space>
      </>
    );
  }
}

const mapStateToProps = ({
  common: { deviceCategory },
  ticket: {
    ticketView: { basicInfo },
    access_door: { detail },
  },
}) => {
  let info = {};
  if (basicInfo && basicInfo.taskProperties) {
    info = JSON.parse(basicInfo.taskProperties);
    if (basicInfo.taskSubType === ACCESS_TYPE_KEY_MAP.GO_OUT) {
      info.taskSubTypeTimeTxt = '出门';
    } else {
      info.taskSubTypeTimeTxt = '入门';
    }
  }
  return {
    info,
    basicInfo,
    deviceCategory,
    codeData: detail.codeData,
    noCodeData: detail.noCodeData,
  };
};
const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
  setAccsssDoorCodeDeviceData: ticketActions.setAccsssDoorCodeDeviceData,
  setAccsssDoorNoCodeDeviceData: ticketActions.setAccsssDoorNoCodeDeviceData,
};
export default connect(mapStateToProps, mapDispatchToProps)(TicketDetail);
