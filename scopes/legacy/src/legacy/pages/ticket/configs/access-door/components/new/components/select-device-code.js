import React from 'react';

import Form from '@ant-design/compatible/es/form';

import { Radio } from '@manyun/base-ui.ui.radio';

const codeTyle = [
  {
    value: 1,
    label: '有设备编号',
  },
  {
    value: 0,
    label: '无设备编号',
  },
];

export class DeviceCode extends React.Component {
  render() {
    const {
      form: { getFieldDecorator },
    } = this.props;
    return (
      <Form colon={false} layout="inline">
        <Form.Item label="添加设备">
          {getFieldDecorator('code', {
            rules: [{ required: true, message: '类型必选！' }],
          })(
            <Radio.Group>
              {codeTyle.map(item => {
                return (
                  <Radio value={item.value} key={item.value}>
                    {item.label}
                  </Radio>
                );
              })}
            </Radio.Group>
          )}
        </Form.Item>
      </Form>
    );
  }
}

export default Form.create({
  onFieldsChange: (props, changedFields) => {
    props.setCode(changedFields.code.value);
  },
  mapPropsToFields(props) {
    return {
      code: Form.createFormField(props.code),
    };
  },
})(DeviceCode);
