import React, { useState } from 'react';

import styled from 'styled-components';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import { ASSET_TYPE_KEY_MAP } from '../../../constants';
import ClientDeviceModal from './client-device-modal';
import IdcSelectedDeviceModal from './idc-select-device-modal';

export const NumButton = styled(Button)`
  padding: 0 5px;
`;

function Statistics({
  assetType,
  region,
  blockTag,
  idcTag,
  selectedDevices,
  setCodeDeviceData,
  setNoCodeDeviceData,
  codeTable,
  noCodeTable,
  reset,
  accessType,
  batchRemove,
}) {
  const [idcModalVisible, setIdcModalVisible] = useState(false);
  const [clientVisible, setClientVisible] = useState(false);

  return (
    <>
      <Space style={{ width: '100%', display: 'flex' }} direction="vertical">
        <Button
          key="batch-create"
          disabled={
            !assetType || (assetType === ASSET_TYPE_KEY_MAP.IDC_ASSET && !blockTag) ? true : false
          }
          onClick={() => {
            if (
              [ASSET_TYPE_KEY_MAP.CLIENT_ASSET, ASSET_TYPE_KEY_MAP.SUPPLIER_ASSET].includes(
                assetType
              )
            ) {
              setClientVisible(true);
            }
            if (assetType === ASSET_TYPE_KEY_MAP.IDC_ASSET) {
              setIdcModalVisible(true);
            }
          }}
        >
          添加线上物资
        </Button>
        {selectedDevices && selectedDevices.length > 0 && (
          <Alert
            type="info"
            message={
              <Space>
                <Typography.Text>{`已选 ${selectedDevices.length} 项`}</Typography.Text>
                <Button
                  type="link"
                  compact
                  onClick={() => {
                    reset();
                  }}
                >
                  取消选择
                </Button>
              </Space>
            }
            action={
              <Button
                type="link"
                compact
                onClick={() => {
                  batchRemove();
                }}
              >
                批量删除
              </Button>
            }
          />
        )}
      </Space>
      {idcModalVisible && (
        <IdcSelectedDeviceModal
          region={region}
          blockTag={blockTag}
          idcTag={idcTag}
          visible={idcModalVisible}
          updateVisible={value => setIdcModalVisible(value)}
          setCodeDeviceData={setCodeDeviceData}
          setNoCodeDeviceData={setNoCodeDeviceData}
          codeTable={codeTable}
          noCodeTable={noCodeTable}
          accessType={accessType}
        />
      )}
      {clientVisible && (
        <ClientDeviceModal
          visible={clientVisible}
          updateVisible={value => setClientVisible(value)}
          setClientTableData={setCodeDeviceData}
        />
      )}
    </>
  );
}

export default Statistics;
