import React from 'react';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';

import Form from '@ant-design/compatible/es/form';
import { Select } from '@galiojs/awesome-antd';
import differenceBy from 'lodash/differenceBy';
import get from 'lodash/get';
import omit from 'lodash/omit';
import moment from 'moment';
import { nanoid } from 'nanoid';

import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import { AssetsTable } from '@manyun/resource-hub.ui.assets-table';
import { AssetImportModalButton } from '@manyun/ticket.page.access-door-mutator';
import { SlaSelect, generateCorrectJobSla } from '@manyun/ticket.ui.sla-select';

import {
  FooterToolBar,
  GutterWrapper,
  LocationCascader,
  TinyCard,
  TinyTable,
} from '@manyun/dc-brain.legacy.components';
import { StyledMcUpload } from '@manyun/dc-brain.legacy.pages/ticket/configs/access-door/styled';
import { getFileInfoList } from '@manyun/dc-brain.legacy.pages/ticket/configs/access-door/utils';
import { getQ } from '@manyun/dc-brain.legacy.pages/ticket/configs/device-online-offline/utils';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import { redirectActionCreator } from '@manyun/dc-brain.legacy.redux/actions/routerActions';
import {
  getTicketFilesActionCreator,
  ticketActions,
} from '@manyun/dc-brain.legacy.redux/actions/ticketActions';
import * as taskCenterService from '@manyun/dc-brain.legacy.services/taskCenterService';
import * as ticketService from '@manyun/dc-brain.legacy.services/ticketService';
import { getLocationSearchMap } from '@manyun/dc-brain.legacy.utils';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';

import { ASSET_TYPE_KEY_MAP, ASSET_TYPE_OPTIONS } from '../../constants';
import { insertSecondAndFirsetCode } from '../../utils';
import Statistics from './components/statistics-buttons';

export class NewTicket extends React.Component {
  state = {
    subBtnloading: false,
    codeTable: [], // 有编号设备表格数据--idc
    noCodeTable: [], //无编号设备表格数据--idc
    selectedCodeRows: [],
    selecteNoCodeRows: [],
    departmentNameDefaultValue: undefined,
    confirmStaffNameDefaultValue: undefined,
    // 新版的客户/供应商资产线下物资
    clientAssets: [],
    // 新版的机房资产线下物资
    idcOfflineAssets: [],
  };

  componentDidMount() {
    const {
      match: { params },
    } = this.props;
    this.props.syncCommonData({
      strategy: { ticketTypes: 'IF_NULL', space: 'IF_NULL', deviceCategory: 'IF_NULL' },
    });
    if (params.id) {
      this.getInfoData(params.id);
      return;
    }
    const { variables } = getLocationSearchMap(this.props.history.location.search, ['variables']);
    if (variables) {
      try {
        const varsMap = JSON.parse(variables);
        this.setState({ variables: true });
        this.props.form.setFieldsValue({
          accessType: varsMap.taskSubType,
          location: varsMap.blockTag ? varsMap.blockTag.split('.') : [],
          relateTaskNo: varsMap.relateTaskNo,
        });
      } catch (error) {
        // ignore...
      }
    }
    if (this.props.ticketTreeData.length) {
      this.props.form.setFieldsValue({ accessType: this.props.ticketTreeData[0].taskType });
    }
    this.props.form.setFieldsValue({
      assetType: variables ? ASSET_TYPE_KEY_MAP.IDC_ASSET : ASSET_TYPE_KEY_MAP.CLIENT_ASSET,
    });
  }

  componentDidUpdate(prevProps) {
    // 如果出入门类型,位置改变，则清空机房设备的数据
    const prevLocationString = prevProps.fieldValues.location.value
      ? prevProps.fieldValues.location.value.join('')
      : null;
    const nowLocationString = this.props.fieldValues.location.value
      ? this.props.fieldValues.location.value.join('')
      : null;
    const prevAccessType = prevProps.fieldValues.accessType.value;
    const currentAccessType = this.props.fieldValues.accessType.value;
    const {
      match: { params },
    } = this.props;
    if (
      (prevLocationString && nowLocationString !== prevLocationString) ||
      (prevAccessType && prevAccessType !== currentAccessType && !params.id)
    ) {
      this.setState({
        codeTable: [],
        selectedCodeRows: [],
        noCodeTable: [],
        selecteNoCodeRows: [],
      });
    }
    if (!prevProps.ticketTreeData.length && this.props.ticketTreeData.length) {
      this.props.form.setFieldsValue({ accessType: this.props.ticketTreeData[0].taskType });
    }
  }

  componentWillUnmount() {
    this.props.resetAccsssDoorCreateFileds();
  }

  getInfoData = async taskNo => {
    const { response, error } = await ticketService.fetchTicketBasicInfo({
      taskNo,
    });
    if (error) {
      message.error(error);
      return;
    }
    try {
      const varsMap = JSON.parse(response.taskProperties);
      this.setState({ editable: true });
      this.props.form.setFieldsValue({
        accessType: response.taskSubType,
        location: [response.idcTag, response.blockTag.split('.')[1]],
        relateTaskNo: response.relateTaskNo,
        accessDoorTime: [moment(varsMap.accessDoorTime), moment(varsMap.accessDoorEndTime)],
        assetType: varsMap.assetType,
        accessDoorReason: varsMap.accessDoorReason,
        taskTitle: response.taskTitle,
        taskSla: {
          sla: generateCorrectJobSla(response.taskSla, response.unit),
          unit: response.unit,
        },
      });
      this.setState({
        departmentNameDefaultValue: varsMap.departmentName,
        confirmStaffNameDefaultValue: varsMap.confirmStaffName,
      });
      this.getCodeData(taskNo, varsMap.assetType);
    } catch (error) {}
    this.props.getTicketFiles({
      targetId: response.taskNo,
      targetType: response.taskType,
      extensionType: 'BASE_FILE',
      callback: res => {
        this.props.updateAccessDoorCreateFields({
          fileInfoList: {
            value: res,
          },
        });
      },
    });
  };

  getCodeData = async (taskNo, assetType) => {
    const { error, response } = await taskCenterService.fetchAccessDeviceList({
      taskNo,
      haveSerialNo: true,
    });
    if (error) {
      message.error(error);
    }
    if (response) {
      if (assetType === ASSET_TYPE_KEY_MAP.IDC_ASSET) {
        const noCodeTableData = [];
        const codeTableData = [];
        const offLineAssets = [];
        for (const item of response.data) {
          if (item.materialType !== 'OFF_LINE') {
            if (item.assetNo) {
              codeTableData.push({
                ...item,
                guid: item.deviceGuid,
              });
            } else {
              noCodeTableData.push({
                ...item,
                totalNum: item.remainCount,
                num: item.accessCount,
                id: nanoid(),
              });
            }
          } else {
            offLineAssets.push({
              sortType: item.deviceType,
              serialNumber: item.serialNo !== '' ? item.serialNo : null,
              brand: item.vendor !== '' ? item.vendor : null,
              model: item.productModel !== '' ? item.productModel : null,
              sum: item.accessCount,
              materialType: item.materialType,
              id: nanoid(),
            });
          }
        }
        this.setState({
          codeTable: codeTableData,
          noCodeTable: noCodeTableData,

          idcOfflineAssets: offLineAssets,
        });
      } else {
        this.setState({
          clientAssets: response.data.map(item => ({
            sortType: item.deviceType,
            serialNumber: item.serialNo !== '' ? item.serialNo : null,
            brand: item.vendor !== '' ? item.vendor : null,
            model: item.productModel !== '' ? item.productModel : null,
            sum: item.accessCount,
            materialType: item.materialType,
            id: nanoid(),
          })),
        });
      }
    }
  };

  getNoCodeData = async (taskNo, assetType) => {
    const { error, response } = await taskCenterService.fetchAccessDeviceList({
      taskNo,
      haveSerialNo: false,
    });
    if (error) {
      message.error(error);
    }
    if (response) {
      if (assetType === ASSET_TYPE_KEY_MAP.IDC_ASSET) {
        this.setState({
          noCodeTable: response.data.map(item => {
            return {
              ...item,
              totalNum: item.remainCount,
              num: item.accessCount,
              id: nanoid(),
            };
          }),
        });
      } else {
        this.setState({
          clientNoCodeTable: response.data.map(item => {
            return {
              ...item,
              num: item.accessCount,
              id: nanoid(),
            };
          }),
        });
      }
    }
  };

  submit = () => {
    const {
      fieldValues,
      form,
      deviceCategory,
      match: { params },
    } = this.props;
    const { codeTable, noCodeTable, clientAssets, idcOfflineAssets } = this.state;
    form.validateFields(async (error, values) => {
      const { departmentName, confirmStaffName } = values;
      if (error) {
        this.setState({ subBtnloading: false });
        return;
      }
      const p = getQ({
        ...fieldValues,
        departmentName: { name: 'departmentName', value: departmentName },
        confirmStaffName: { name: 'confirmStaffName', value: confirmStaffName },
      });
      const fileInfoList = getFileInfoList(fieldValues.fileInfoList?.value);
      let q = {
        ...p,
        relateTaskNo: p.relateTaskNo?.trim(),
        taskTitle: p.taskTitle?.trim(),
        accessDoorReason: p.accessDoorReason?.trim(),
        accessDoorTime: moment(p.accessDoorTime[0]).milliseconds(0).valueOf(),
        accessDoorEndTime: moment(p.accessDoorTime[1]).milliseconds(999).valueOf(),
        taskNo: params.id,
        fileInfoList,
      };
      if (fieldValues.assetType.value === ASSET_TYPE_KEY_MAP.IDC_ASSET) {
        const idcOnlineAssetsParam = [...codeTable, ...noCodeTable].map(asset => ({
          deviceGuid: asset.guid,
          // 这里是个中文
          deviceType:
            get(deviceCategory, ['normalizedList', asset.deviceType, 'metaName']) ||
            asset.deviceType,
          serialNo: asset.serialNumber ?? null,
          assetNo: asset.assetNo ?? null,
          vendor: asset.vendor ?? null,
          productModel: asset.productModel ?? null,
          accessCount: asset.spareType ? Number(asset.num) : 1,
          materialType: 'ON_LINE',
        }));
        const idcOfflineAssetsParam = idcOfflineAssets.map(item => {
          return {
            deviceType: item.sortType,
            accessCount: item.sum,
            serialNo: item.serialNumber ?? null,
            vendor: item.brand ?? null,
            productModel: item.model ?? null,
            materialType: 'OFF_LINE',
          };
        });

        q = {
          ...q,
          assetDevices: [...idcOnlineAssetsParam, ...idcOfflineAssetsParam],
        };
      }
      if (
        [ASSET_TYPE_KEY_MAP.CLIENT_ASSET, ASSET_TYPE_KEY_MAP.SUPPLIER_ASSET].includes(
          fieldValues.assetType.value
        )
      ) {
        q = {
          ...q,
          assetDevices: clientAssets.length
            ? clientAssets.map(item => {
                return {
                  deviceType: item.sortType,
                  accessCount: item.sum,
                  serialNo: item.serialNumber ?? null,
                  vendor: item.brand ?? null,
                  productModel: item.model ?? null,
                  materialType: 'OFF_LINE',
                };
              })
            : [],
        };
      }
      if (!q.assetDevices.length && !q.consumables.length) {
        message.error('物资不能为空！');
        return;
      }
      this.setState({ subBtnloading: true });
      const response = params.id ? await rePostTicket(q) : await createTicket(q);
      if (response) {
        this.setState({ subBtnloading: false });
        setTimeout(() => {
          this.props.history.push(
            urls.generateTicketDetailUrl({ ticketType: this.props.ticketType, id: response })
          );
        }, 1.5 * 1000);
      } else {
        this.setState({ subBtnloading: false });
      }
    });
  };

  getRegin = () => {
    const {
      fieldValues: { location },
      space,
    } = this.props;
    if (!location.value) {
      return null;
    }
    if (!space) {
      return location.value[0];
    }
    const idcTag = location.value[0];
    const region = space.normalizedList[idcTag] ? space.normalizedList[idcTag].parentCode : null;
    return region;
  };

  deleteDevice = (records, type) => {
    if (type === 'idc-code') {
      const { codeTable } = this.state;
      const newData = codeTable.filter(({ guid }) => guid !== records.guid);
      this.setState({ codeTable: newData });
    }
    if (type === 'idc-noCode') {
      const { noCodeTable } = this.state;
      const newData = noCodeTable.filter(({ id }) => id !== records.id);
      this.setState({ noCodeTable: newData });
    }
  };

  editNum = (value, record, type) => {
    if (!value || Number.isNaN(Number(value))) {
      message.error('请输入正确的数字！');
      return;
    }
    if (value.includes('.')) {
      message.error('不可输入小数！');
      return;
    }
    if (!Number(value) || Number(value) < 0) {
      message.error('数量必须大于0！');
      return;
    }
    const { noCodeTable } = this.state;
    let tmp = false;
    const newData = noCodeTable.map(item => {
      if (item.id === record.id) {
        if (item.totalNum < Number(value)) {
          tmp = item.totalNum;
          return item;
        }
        return {
          ...item,
          num: value,
        };
      }
      return item;
    });
    if (tmp) {
      message.error(`最大值为${tmp}`);
      return;
    }
    this.setState({ noCodeTable: newData });
  };

  render() {
    const {
      form: { getFieldDecorator, getFieldValue },
      ticketTreeData,
      fieldValues,
      deviceCategory,
      taskTypeTxt,
    } = this.props;
    const {
      subBtnloading,
      selectedCodeRows,
      selecteNoCodeRows,
      codeTable,
      noCodeTable,
      selectedClientCodeRows,
      selectedClientNoCodeRows,
      variables,
      editable,
      departmentNameDefaultValue,
      confirmStaffNameDefaultValue,
    } = this.state;
    const location = getFieldValue('location');
    const assetType = getFieldValue('assetType');
    const accessType = getFieldValue('accessType');

    const isClientAsset = assetType === ASSET_TYPE_KEY_MAP.CLIENT_ASSET;
    const isIdcAsset = assetType === ASSET_TYPE_KEY_MAP.IDC_ASSET;

    const cardPrefix = accessType === 'GO_IN' ? '入门' : '出门';
    const canAddAsset = assetType && location;

    return (
      <GutterWrapper mode="vertical">
        <TinyCard title="基本信息" bodyStyle={{ width: '720px' }}>
          <Form colon={false} labelCol={{ xl: 4 }} wrapperCol={{ xl: 20 }}>
            <Form.Item label="出入门类型">
              {getFieldDecorator('accessType', {
                rules: [{ required: true, message: '出入门类型必选！' }],
              })(
                <Select
                  requestOnDidMount
                  style={{ width: 216 }}
                  disabled={variables || editable}
                  onChange={() => {
                    this.setState({
                      codeTable: [],
                      noCodeTable: [],
                      selectedCodeRows: [],
                      selecteNoCodeRows: [],
                      clientAssets: [],
                      idcOfflineAssets: [],
                    });
                  }}
                >
                  {ticketTreeData.map(({ taskType, taskValue }) => {
                    return (
                      <Select.Option key={taskType} value={taskType}>
                        {taskValue}
                      </Select.Option>
                    );
                  })}
                </Select>
              )}
            </Form.Item>
            <Form.Item label="位置">
              {getFieldDecorator('location', {
                rules: [
                  {
                    required: true,
                    message: '机房、楼栋必选！',
                    type: 'number',
                    transform: value => (value?.length === 2 ? 2 : false),
                  },
                ],
              })(<LocationCascader style={{ width: 216 }} disabled={variables} currentAuthorize />)}
            </Form.Item>
            <Form.Item label="关联单号">
              {getFieldDecorator('relateTaskNo', {
                rules: [
                  {
                    type: 'string',
                    max: 20,
                    message: '最多输入 20 个字符！',
                  },
                ],
              })(<Input disabled={variables} allowClear style={{ width: 216 }} />)}
            </Form.Item>

            <Form.Item label="资产类型">
              {getFieldDecorator('assetType', {
                rules: [{ required: true, message: '资产类型必选！' }],
              })(
                <Select style={{ width: 216 }} disabled={editable}>
                  {ASSET_TYPE_OPTIONS.map(({ label, value }) => {
                    return (
                      <Select.Option key={value} value={value}>
                        {label}
                      </Select.Option>
                    );
                  })}
                </Select>
              )}
            </Form.Item>
            {isClientAsset && (
              <Form.Item label="客户确认人姓名">
                {getFieldDecorator('confirmStaffName', {
                  initialValue: confirmStaffNameDefaultValue,
                  rules: [
                    { required: true, message: '客户确认人姓名必填！' },
                    {
                      type: 'string',
                      max: 20,
                      message: '最多输入 20 个字符！',
                    },
                  ],
                })(<Input allowClear style={{ width: 216 }} />)}
              </Form.Item>
            )}
            {isClientAsset && (
              <Form.Item label="客户所属单位">
                {getFieldDecorator('departmentName', {
                  initialValue: departmentNameDefaultValue,
                  rules: [
                    { required: true, message: '客户所属单位必填！' },
                    {
                      type: 'string',
                      max: 32,
                      message: '最多输入 32 个字符！',
                    },
                  ],
                })(<Input allowClear style={{ width: 216 }} />)}
              </Form.Item>
            )}
            <Form.Item label="工单标题">
              {getFieldDecorator('taskTitle', {
                rules: [
                  { required: true, message: '工单标题必填！' },
                  {
                    type: 'string',
                    max: 20,
                    message: '最多输入 20 个字符！',
                  },
                ],
              })(<Input allowClear style={{ width: 216 }} />)}
            </Form.Item>
            <Form.Item label="授权时间范围">
              {getFieldDecorator('accessDoorTime', {
                rules: [{ required: true, message: '授权时间范围必选！' }],
              })(
                <DatePicker.RangePicker
                  style={{ width: 395 }}
                  disabledDate={current => current < moment().startOf('day')}
                  disabledTime={disabledDateTime}
                  showTime={{ format: 'HH:mm' }}
                  format="YYYY-MM-DD HH:mm"
                  allowClear
                />
              )}
            </Form.Item>
            <Form.Item label={`${taskTypeTxt}原因`}>
              {getFieldDecorator('accessDoorReason', {
                rules: [
                  {
                    required: true,
                    message: `${taskTypeTxt}原因必填！`,
                  },
                  {
                    type: 'string',
                    max: 120,
                    message: '最多输入 120 个字符！',
                  },
                ],
              })(<Input.TextArea style={{ width: 395 }} />)}
            </Form.Item>
            <Form.Item label="SLA">{getFieldDecorator('taskSla', {})(<SlaSelect />)}</Form.Item>
            <Form.Item label="附件">
              {getFieldDecorator('fileInfoList', {
                valuePropName: 'fileList',
                normalize: value => {
                  if (Array.isArray(value)) {
                    return value;
                  }
                  return value?.fileList || [];
                },
              })(
                <StyledMcUpload
                  accept="image/*,.xls,.xlsx"
                  showUploadList
                  maxFileSize={20}
                  allowDelete
                  showAccept
                >
                  <Button>上传</Button>
                </StyledMcUpload>
              )}
            </Form.Item>
          </Form>
        </TinyCard>
        <TinyCard title={`${cardPrefix}信息`} style={{ marginBottom: '40px' }}>
          <Space direction="vertical" style={{ width: '100%', display: 'flex' }}>
            {!isIdcAsset && (
              <AssetImportModalButton
                location={location}
                currentData={this.state.clientAssets}
                setCurrentData={assets => {
                  this.setState({ clientAssets: assets });
                }}
                taskSubType={assetType}
                disabled={!canAddAsset}
              />
            )}

            {isIdcAsset ? (
              <Space direction="vertical" style={{ width: '100%', display: 'flex' }}>
                <Space direction="vertical" style={{ width: '100%', display: 'flex' }}>
                  <Typography.Title style={{ marginBottom: 0 }} level={5}>
                    线上物资
                  </Typography.Title>
                  <Statistics
                    selectedDevices={
                      fieldValues.assetType.value === ASSET_TYPE_KEY_MAP.IDC_ASSET
                        ? [...selectedCodeRows, ...selecteNoCodeRows]
                        : [...selectedClientCodeRows, ...selectedClientNoCodeRows]
                    }
                    codeTable={codeTable}
                    noCodeTable={noCodeTable}
                    region={this.getRegin()}
                    idcTag={
                      fieldValues.location.value && fieldValues.location.value[0]
                        ? fieldValues.location.value[0]
                        : null
                    }
                    blockTag={
                      fieldValues.location.value && fieldValues.location.value[1]
                        ? fieldValues.location.value[1]
                        : null
                    }
                    assetType={fieldValues.assetType.value}
                    setCodeDeviceData={value => {
                      if (fieldValues.assetType.value === ASSET_TYPE_KEY_MAP.IDC_ASSET) {
                        this.setState(({ codeTable }) => ({
                          codeTable: insertSecondAndFirsetCode({
                            data: [...codeTable, ...value],
                            deviceObj: deviceCategory.normalizedList,
                          }),
                        }));
                      }
                    }}
                    setNoCodeDeviceData={value => {
                      this.setState({
                        noCodeTable: insertSecondAndFirsetCode({
                          data: value.map(item => {
                            return {
                              ...item,
                              deviceType: item.spareType,
                            };
                          }),
                          deviceObj: deviceCategory.normalizedList,
                        }),
                      });
                    }}
                    reset={() => {
                      if (fieldValues.assetType.value === ASSET_TYPE_KEY_MAP.IDC_ASSET) {
                        this.setState({
                          selectedCodeRows: [],
                          selecteNoCodeRows: [],
                        });
                      }
                    }}
                    accessType={fieldValues.accessType.value}
                    batchRemove={() => {
                      if (fieldValues.assetType.value === ASSET_TYPE_KEY_MAP.IDC_ASSET) {
                        const newDataTable = differenceBy(
                          [...codeTable, ...noCodeTable],
                          selectedCodeRows,
                          'id'
                        );
                        const newCodeTable = newDataTable.filter(data => data.assetNo);
                        const newNoCodeTable = newDataTable.filter(data => !data.assetNo);
                        this.setState({
                          codeTable: newCodeTable,
                          noCodeTable: newNoCodeTable,
                          selectedCodeRows: [],
                          selecteNoCodeRows: [],
                        });
                      }
                    }}
                  />
                  <TinyTable
                    align="left"
                    rowKey="id"
                    dataSource={[...codeTable, ...noCodeTable]}
                    columns={getColumns({
                      deleteDevice: this.deleteDevice,
                      type: 'idc-code',
                      editNum: this.editNum,
                      deviceCategory,
                    })}
                    rowSelection={{
                      selectedRowKeys: selectedCodeRows.map(({ id }) => id),

                      selectedRows: selectedCodeRows,
                      onChange: (keys, rows) => {
                        this.setState({ selectedCodeRows: rows });
                      },
                    }}
                  />
                </Space>
                <Space direction="vertical" style={{ width: '100%', display: 'flex' }}>
                  <Typography.Title style={{ marginBottom: 0 }} level={5}>
                    线下物资
                  </Typography.Title>
                  <AssetsTable
                    mode="editor"
                    editableFromParent={canAddAsset}
                    scroll={{ y: 240 }}
                    parentDataSource={this.state.idcOfflineAssets}
                    setParentDataSource={assets => {
                      this.setState({ idcOfflineAssets: assets });
                    }}
                  />
                </Space>
              </Space>
            ) : (
              <AssetsTable
                mode="editor"
                editableFromParent={canAddAsset}
                scroll={{ y: 240 }}
                parentDataSource={this.state.clientAssets}
                setParentDataSource={assets => {
                  this.setState({ clientAssets: assets });
                }}
              />
            )}
          </Space>
        </TinyCard>
        <FooterToolBar>
          <GutterWrapper>
            <Button
              type="primary"
              disabled={
                (this.state.clientAssets.length === 0 && !isIdcAsset) ||
                (isIdcAsset &&
                  [
                    ...this.state.noCodeTable,
                    ...this.state.codeTable,
                    ...this.state.idcOfflineAssets,
                  ].length === 0)
              }
              loading={subBtnloading}
              onClick={this.submit}
            >
              提交
            </Button>
            <Button onClick={() => this.props.history.goBack()}>取消</Button>
          </GutterWrapper>
        </FooterToolBar>
      </GutterWrapper>
    );
  }
}

const mapStateToProps = ({
  common: { ticketTypes, deviceCategory, citiesTree },
  'resource.spaces': { entities, codes },
  ticket: {
    access_door: { create },
  },
}) => {
  const fieldValues = create;
  let ticketTreeData = [];
  let taskTypeTxt = '';

  if (ticketTypes) {
    const assessTypes = ticketTypes.treeList.filter(item => {
      if (item.taskType === 'ACCESS') {
        return true;
      }
      return false;
    });
    if (assessTypes.length) {
      ticketTreeData = assessTypes[0].children;
    }
    taskTypeTxt = ticketTypes.normalizedList?.[create.accessType.value]?.taskValue;
  }
  return {
    space: codes ? { normalizedList: entities } : null,
    ticketTreeData,
    fieldValues,
    ticketTypes,
    deviceCategory,
    taskTypeTxt,
  };
};
const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
  updateAccessDoorCreateFields: ticketActions.updateAccessDoorCreateFields,
  redirect: redirectActionCreator,
  resetAccsssDoorCreateFileds: ticketActions.resetAccsssDoorCreateFileds,
  getTicketFiles: getTicketFilesActionCreator,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(
  Form.create({
    onFieldsChange: (props, changedFields) => {
      let fields = changedFields;
      if (
        changedFields.location &&
        changedFields.location.value &&
        props.fieldValues.accessType.value &&
        props.fieldValues.assetType.value
      ) {
        let timeType = '';
        if (props.ticketTypes) {
          timeType = props.ticketTypes.normalizedList[props.fieldValues.accessType.value].taskValue;
        }
        const customer = getTitlePrefix(props.fieldValues.assetType.value);
        const txt = `${changedFields.location.value[0]}机房${customer}物资${timeType}单`;
        fields = {
          ...fields,
          taskTitle: {
            name: 'taskTitle',
            value: txt,
          },
        };
      }
      if (
        changedFields.accessType &&
        changedFields.accessType.value &&
        props.fieldValues.location.value &&
        props.fieldValues.assetType.value
      ) {
        let timeType = '出门';
        if (props.ticketTypes) {
          timeType = props.ticketTypes.normalizedList[changedFields.accessType.value].taskValue;
        }
        const customer = getTitlePrefix(props.fieldValues.assetType.value);
        const txt = `${props.fieldValues.location.value[0]}机房${customer}物资${timeType}单`;
        fields = {
          ...fields,
          taskTitle: {
            name: 'taskTitle',
            value: txt,
          },
        };
      }

      if (
        changedFields.assetType &&
        changedFields.assetType.value &&
        props.fieldValues.location.value &&
        props.fieldValues.accessType.value
      ) {
        let timeType = '出门';
        if (props.ticketTypes) {
          timeType = props.ticketTypes.normalizedList[props.fieldValues.accessType.value].taskValue;
        }
        const customer = getTitlePrefix(changedFields.assetType.value);
        const txt = `${props.fieldValues.location.value[0]}机房${customer}物资${timeType}单`;
        fields = {
          ...fields,
          taskTitle: {
            name: 'taskTitle',
            value: txt,
          },
        };
      }
      props.updateAccessDoorCreateFields(fields);
    },
    mapPropsToFields(props) {
      return {
        relateTaskNo: Form.createFormField(props.fieldValues.relateTaskNo),
        taskTitle: Form.createFormField(props.fieldValues.taskTitle),
        location: Form.createFormField(props.fieldValues.location),
        assetType: Form.createFormField(props.fieldValues.assetType),
        taskSla: Form.createFormField(props.fieldValues.taskSla),
        departmentName: Form.createFormField(props.fieldValues.departmentName),
        confirmStaffName: Form.createFormField(props.fieldValues.confirmStaffName),
        accessType: Form.createFormField(props.fieldValues.accessType),
        accessDoorTime: Form.createFormField(props.fieldValues.accessDoorTime),
        accessDoorReason: Form.createFormField(props.fieldValues.accessDoorReason),
        fileInfoList: Form.createFormField({
          value: props.fieldValues.fileInfoList.value,
        }),
      };
    },
  })(withRouter(NewTicket))
);

async function createTicket(data) {
  const { response, error } = await taskCenterService.createAccessDoorTicket(omit(data, 'taskNo'));
  if (error) {
    message.error(error);
    return false;
  }
  message.success('创建成功！');
  return response;
}

async function rePostTicket(data) {
  const { error } = await taskCenterService.rePostAccessDoorTicket(
    omit(data, ['accessType', 'assetType'])
  );
  if (error) {
    message.error(error);
    return false;
  }
  message.success('重新发起成功！');
  return data.taskNo;
}

const getColumns = ({ deleteDevice, type, editNum, deviceCategory }) => {
  let tmp = [
    {
      title: '三级分类',
      dataIndex: 'thirdCategoryName',
      render: (text, record) =>
        text ||
        get(deviceCategory, ['normalizedList', record.deviceType, 'metaName']) ||
        record.deviceType,
    },
    {
      title: '资产ID',
      dataIndex: 'assetNo',
      render: (_, { assetNo }) => assetNo ?? '--',
    },
    {
      title: 'SN',
      dataIndex: 'serialNumber',
      render: (_, { serialNumber }) => serialNumber ?? '--',
    },
    {
      title: '品牌',
      dataIndex: 'vendor',
      render: (_, { vendor }) => vendor ?? '--',
    },
    {
      title: '型号',
      dataIndex: ' productModel',
      render: (_, { productModel }) => productModel ?? '--',
    },
    {
      title: '数量',
      dataIndex: 'num',
      render: (num, record) =>
        record.assetNo ? (
          1
        ) : (
          <div style={{ width: 80 }}>
            <Typography.Paragraph
              editable={{ onChange: value => editNum(value, record, 'idc-noCode') }}
              style={{ marginBottom: 0 }}
            >
              {String(num)}
            </Typography.Paragraph>
          </div>
        ),
    },
  ];

  tmp = [
    ...tmp,
    {
      title: '操作',
      key: 'actions',
      width: 100,
      render: (_, records) => (
        <Popconfirm
          title="删除此行"
          onConfirm={() => {
            deleteDevice(records, records.assetNo ? 'idc-code' : 'idc-noCode');
          }}
        >
          <Button compact type="link">
            删除
          </Button>
        </Popconfirm>
      ),
    },
  ];
  return tmp;
};

const range = (start, end) => {
  const result = [];
  for (let i = start; i < end; i++) {
    result.push(i);
  }
  return result;
};
export function disabledDateTime(current) {
  const compareMoment = moment();
  const hours = compareMoment.hours();
  const minute = compareMoment.minute();
  if (current) {
    const choseHour = current.hours();

    const isToday = current.isSame(compareMoment, 'day');
    if (isToday) {
      if (choseHour === hours) {
        return {
          disabledHours: () => range(0, hours),
          disabledMinutes: () => range(0, minute),
          disabledSeconds: () => [],
        };
      }
      return {
        disabledHours: () => [],
        disabledMinutes: () => [],
        disabledSeconds: () => [],
      };
    }
  }

  return {
    disabledHours: () => [],
    disabledMinutes: () => [],
    disabledSeconds: () => [],
  };
}

function getTitlePrefix(type) {
  switch (type) {
    case ASSET_TYPE_KEY_MAP.CLIENT_ASSET:
      return '客户';

    case ASSET_TYPE_KEY_MAP.SUPPLIER_ASSET:
      return '供应商';
    case ASSET_TYPE_OPTIONS.IDC_ASSET:
    default:
      return '机房';
  }
}
