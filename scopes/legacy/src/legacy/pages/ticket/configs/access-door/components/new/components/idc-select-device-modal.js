import Form from '@ant-design/compatible/es/form';
import { differenceBy } from 'lodash';
import React from 'react';
import { connect } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import { generateDeviceRecordRoutePath } from '@manyun/resource-hub.route.resource-routes';
import { RoomSelect } from '@manyun/resource-hub.ui.room-select';

import {
  AssetClassificationApiTreeSelect,
  LocationCascader,
  TinyTable,
} from '@manyun/dc-brain.legacy.components';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import { equipmentManageService, spareService } from '@manyun/dc-brain.legacy.services';
import { getSpaceGuid } from '@manyun/dc-brain.legacy.utils';

import { ACCESS_TYPE_KEY_MAP } from '../../../constants';

export class IdcSelectedDeviceModal extends React.Component {
  state = {
    fieldsValue: {
      roomTag: null,
      deviceType: null,
      name: null,
      locationCascaderValue: null,
    },
    code: { value: 1 },
    codeTable: {
      data: [],
      total: 10,
      pagination: { pageNum: 1, pageSize: 10 },
    },
    noCodeTable: {
      data: [],
      total: 10,
      pagination: { pageNum: 1, pageSize: 10 },
    },

    selectCodeRowsInModal: [],
    selectNoCodeRowsInModal: [],
  };

  componentDidMount() {
    this.props.syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });
    const { blockTag, idcTag } = this.props;
    if (blockTag && idcTag) {
      this.onSearch();
    }
  }

  componentDidUpdate(prevProps, prevState) {
    if (prevState.code.value !== this.state.code.value) {
      if (this.state.code.value === 0) {
        this.setState({ selectNoCodeRowsInModal: this.props.noCodeTable });
      }
      if (this.judgeDeviceType()) {
        this.setState({ selectCodeRowsInModal: this.props.codeTable });
      }
    }
    if (!prevProps.visible && this.props.visible) {
      if (this.judgeDeviceType()) {
        this.setState({ selectCodeRowsInModal: this.props.codeTable });
      } else {
        this.setState({ selectNoCodeRowsInModal: this.props.noCodeTable });
      }
    }
    const prevLocationString = prevProps.region + prevProps.idcTag + prevProps.blockTag;
    const currentLocationString = this.props.region + this.props.idcTag + this.props.blockTag;
    const { blockTag, idcTag } = this.props;
    if (prevLocationString !== currentLocationString && blockTag && idcTag) {
      this.onSearch();
    }
  }

  /***
   * 判断添加设备的类型
   * @returns {Boolean}  true 代表为有设备编号，false 代表无设备编号
   */
  judgeDeviceType = () => {
    const {
      code: { value },
    } = this.state;
    return value === 1;
  };

  /***
   * 判断添加设备的类型
   * @returns {{ idcTag: null | string; blockTag: null | string }} `idcTag` and `blockTag`
   */
  getBaseQ = () => {
    const { fieldsValue } = this.state;
    const { blockTag, idcTag, accessType } = this.props;
    if (accessType === ACCESS_TYPE_KEY_MAP.GO_OUT) {
      return {
        idcTag,
        blockTag,
        roomTag: fieldsValue.roomTag ? fieldsValue.roomTag.split('.')[2] : undefined,
      };
    }
    return {
      idcTag: fieldsValue.locationCascaderValue?.[0] || null,
      blockTag: fieldsValue.locationCascaderValue?.[1] || null,
      roomTag: fieldsValue.locationCascaderValue?.[2] || null,
    };
  };

  getQ = () => {
    const { fieldsValue } = this.state;
    const { idcTag, blockTag, roomTag } = this.getBaseQ();
    let baseQ = {};
    if (this.judgeDeviceType()) {
      const spaceGuid = getSpaceGuid(idcTag, blockTag, roomTag);
      const { codeTable } = this.state;
      baseQ = {
        ...baseQ,
        name: fieldsValue.name?.trim(),
        idcTag,
        blockTag,
        roomTag,
        spaceGuidList: spaceGuid ? [spaceGuid] : null,
        secondCategory: fieldsValue.secondCategory,
        topCategory: fieldsValue.topCategory,
        deviceTypeList: fieldsValue.deviceType ? [fieldsValue.deviceType] : undefined,
        ...codeTable.pagination,
      };
    } else {
      const { noCodeTable } = this.state;
      const blockGuid = getSpaceGuid(idcTag, blockTag);
      baseQ = {
        ...baseQ,
        secondCategory: fieldsValue.spareSecondCategory,
        topCategory: fieldsValue.spareTopCategory,
        spareTypeList: fieldsValue.spareThirdCategorycode
          ? [fieldsValue.spareThirdCategorycode]
          : null,
        roomTag,
        blockGuid,
        ...noCodeTable.pagination,
      };
    }
    return baseQ;
  };

  onSearch = async () => {
    const params = this.getQ();
    if (this.judgeDeviceType()) {
      const { response, error } = await equipmentManageService.fetchEquipmentListPage(params);
      if (error) {
        message.error(error);
      }
      if (response) {
        this.setState(({ codeTable }) => ({
          codeTable: { ...codeTable, ...response },
        }));
      }
    } else {
      const { response, error } = await spareService.fetchSpares(params);
      if (error) {
        message.error(error);
      }
      if (response) {
        this.setState(({ noCodeTable }) => ({
          noCodeTable: {
            ...noCodeTable,
            ...response,
            data: response.data.map(item => {
              return {
                ...item,
                num: 1,
              };
            }),
          },
        }));
      }
    }
  };

  onSearchDevice = () => {
    this.setState(
      ({ codeTable: { pagination } }) => ({
        codeTable: { pagination: { ...pagination, pageNum: 1 } },
      }),
      this.onSearch
    );
  };

  onSearchSpare = () => {
    this.setState(
      ({ noCodeTable: { pagination } }) => ({
        noCodeTable: { pagination: { ...pagination, pageNum: 1 } },
      }),
      this.onSearch
    );
  };

  paginationChangeHandler = (current, size) => {
    this.setState(
      { codeTable: { pagination: { pageNum: current, pageSize: size } } },
      this.onSearch
    );
  };

  noCodepaginationChangeHandler = (current, size) => {
    this.setState({ noCodeTable: { pagination: { pageNum: current, pageSize: size } } }, () =>
      this.getNoCodeDevice()
    );
  };

  getCodeTableDisabled = record => {
    const { codeTable } = this.props;
    let tmp = false;
    if (codeTable.length) {
      codeTable.forEach(item => {
        if (item.guid === record.guid) {
          tmp = true;
        }
      });
    }
    return tmp;
  };

  getNoCodeTableDisabled = record => {
    const { noCodeTable } = this.props;
    if (record.totalNum === 0) {
      return true;
    }
    let tmp = false;
    if (noCodeTable.length) {
      noCodeTable.forEach(item => {
        if (item.id === record.id) {
          tmp = true;
        }
      });
    }
    return tmp;
  };

  getModalButtonDisabled = () => {
    const { selectCodeRowsInModal, selectNoCodeRowsInModal } = this.state;
    const { codeTable, noCodeTable } = this.props;
    let tmp = false;
    if (this.judgeDeviceType()) {
      if (!selectCodeRowsInModal.length) {
        tmp = true;
      } else {
        const newData = differenceBy(selectCodeRowsInModal, codeTable, 'guid');
        if (!newData.length) {
          tmp = true;
        }
      }
    } else {
      if (!selectNoCodeRowsInModal.length) {
        tmp = true;
      } else {
        const newData = differenceBy(selectNoCodeRowsInModal, noCodeTable, 'id');
        if (!newData.length) {
          tmp = true;
        }
      }
    }
    return tmp;
  };

  render() {
    const { visible, updateVisible, deviceTypeMapping, accessType, blockTag, idcTag } = this.props;
    const {
      code,
      fieldsValue,
      codeTable,
      selectCodeRowsInModal,
      selectNoCodeRowsInModal,
      noCodeTable,
    } = this.state;
    return (
      <Modal
        title="添加线上物资"
        visible={visible}
        width={1100}
        bodyStyle={{ maxHeight: '452px', overflowY: 'auto' }}
        okButtonProps={{
          disabled: this.getModalButtonDisabled(),
        }}
        onCancel={() => {
          updateVisible(false);
        }}
        onOk={() => {
          if (this.judgeDeviceType()) {
            this.props.setCodeDeviceData(selectCodeRowsInModal);
          } else {
            this.props.setNoCodeDeviceData(selectNoCodeRowsInModal);
          }
          updateVisible(false);
        }}
      >
        <Space style={{ width: '100%', display: 'flex' }} direction="vertical">
          <Tabs
            items={[
              {
                label: '设备',
                key: '1',
              },
              { label: '耗材', key: '0' },
            ]}
            onChange={value => {
              this.setState(
                {
                  code: {
                    value: Number(value),
                  },
                  fieldsValue: {
                    roomTag: null,
                    deviceType: null,
                    name: null,
                  },
                },
                this.onSearch
              );
            }}
          />

          <Form colon={false} layout="inline">
            <Form.Item label="所属包间">
              {accessType !== ACCESS_TYPE_KEY_MAP.GO_OUT && (
                <LocationCascader
                  style={{ width: 200 }}
                  showRoom
                  currentAuthorize
                  value={fieldsValue.locationCascaderValue}
                  onChange={value => {
                    this.setState({
                      fieldsValue: { ...fieldsValue, locationCascaderValue: value },
                    });
                  }}
                />
              )}
              {accessType === ACCESS_TYPE_KEY_MAP.GO_OUT && (
                <RoomSelect
                  style={{ width: 200 }}
                  value={fieldsValue.roomTag}
                  blockGuid={`${idcTag}.${blockTag}`}
                  allowClear
                  showSearch
                  onChange={value =>
                    this.setState({ fieldsValue: { ...fieldsValue, roomTag: value } })
                  }
                />
              )}
            </Form.Item>
            <Form.Item label="资产分类">
              {this.judgeDeviceType() && (
                <AssetClassificationApiTreeSelect
                  dataType={['snDevice']}
                  style={{ width: 200 }}
                  category="allCategoryCode"
                  value={{
                    thirdCategorycode: fieldsValue.deviceType,
                    firstCategoryCode: fieldsValue.topCategory,
                    secondCategoryCode: fieldsValue.secondCategory,
                  }}
                  allowClear
                  onChange={({ thirdCategorycode, secondCategoryCode, firstCategoryCode }) => {
                    this.setState({
                      fieldsValue: {
                        ...fieldsValue,
                        topCategory: firstCategoryCode,
                        secondCategory: secondCategoryCode,
                        deviceType: thirdCategorycode,
                      },
                    });
                  }}
                />
              )}
              {code.value === 0 && (
                <AssetClassificationApiTreeSelect
                  dataType={['noSnDevice']}
                  category="allCategoryCode"
                  style={{ width: 200 }}
                  value={{
                    thirdCategorycode: fieldsValue.spareThirdCategorycode,
                    secondCategoryCode: fieldsValue.spareSecondCategory,
                    firstCategoryCode: fieldsValue.spareTopCategory,
                  }}
                  allowClear
                  onChange={({ thirdCategorycode, secondCategoryCode, firstCategoryCode }) => {
                    this.setState({
                      fieldsValue: {
                        ...fieldsValue,
                        spareTopCategory: firstCategoryCode,
                        spareSecondCategory: secondCategoryCode,
                        spareThirdCategorycode: thirdCategorycode,
                      },
                    });
                  }}
                />
              )}
            </Form.Item>
            {this.judgeDeviceType() && (
              <Form.Item label="设备名称">
                <Input
                  allowClear
                  style={{ width: 200 }}
                  value={fieldsValue.name}
                  onChange={({ target: { value } }) => {
                    this.setState({ fieldsValue: { ...fieldsValue, name: value } });
                  }}
                />
              </Form.Item>
            )}
            <Form.Item>
              <Space>
                <Button
                  type="primary"
                  onClick={() => {
                    if (this.judgeDeviceType()) {
                      this.onSearchDevice();
                    } else {
                      this.onSearchSpare();
                    }
                  }}
                >
                  搜索
                </Button>
                <Button
                  onClick={() => {
                    this.setState({ fieldsValue: { roomTag: null, deviceType: null } }, () => {
                      this.onSearch();
                    });
                  }}
                >
                  重置
                </Button>
              </Space>
            </Form.Item>
          </Form>
          {this.judgeDeviceType() && (
            <TinyTable
              align="left"
              rowKey="guid"
              selectRowsSpreadPage
              scroll={{ y: 165 }}
              dataSource={codeTable.data}
              columns={getColumns({ deviceTypeMapping, code: code.value })}
              pagination={{
                total: codeTable.total,
                current: codeTable.pagination.pageNum,
                pageSize: codeTable.pagination.pageSize,
                onChange: this.paginationChangeHandler,
              }}
              rowSelection={{
                getCheckboxProps: item => ({ disabled: this.getCodeTableDisabled(item) }),
                selectedRowKeys: selectCodeRowsInModal.map(({ guid }) => guid),
                selectedRows: selectCodeRowsInModal,
                onChange: (keys, rows) => this.setState({ selectCodeRowsInModal: rows }),
              }}
            />
          )}
          {code.value === 0 && (
            <TinyTable
              align="left"
              rowKey="id"
              selectRowsSpreadPage
              dataSource={noCodeTable.data}
              columns={getColumns({ deviceTypeMapping, code: code.value })}
              pagination={{
                total: noCodeTable.total,
                current: noCodeTable.pagination.pageNum,
                pageSize: noCodeTable.pagination.pageSize,
                onChange: this.noCodepaginationChangeHandler,
              }}
              rowSelection={{
                getCheckboxProps: item => ({ disabled: this.getNoCodeTableDisabled(item) }),
                selectedRowKeys: selectNoCodeRowsInModal.map(({ id }) => id),
                selectedRows: selectNoCodeRowsInModal,
                onChange: (keys, rows) => this.setState({ selectNoCodeRowsInModal: rows }),
              }}
            />
          )}
        </Space>
      </Modal>
    );
  }
}

const mapStateToProps = ({ common: { deviceCategory } }, { region, idcTag, blockTag }) => {
  const deviceTypeMapping = deviceCategory?.normalizedList;
  return {
    deviceTypeMapping,
  };
};
const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(Form.create({ name: 'create-access-door-ticket' })(IdcSelectedDeviceModal));

const getColumns = ({ deviceTypeMapping, code }) => {
  let tmp = [];
  if (code === 1) {
    tmp = [
      {
        title: '设备名称',
        dataIndex: 'name',
        render: (name, record) => {
          return (
            <a
              rel="noopener noreferrer"
              target="_blank"
              href={generateDeviceRecordRoutePath({ guid: record.guid })}
            >
              {name}
            </a>
          );
        },
      },
      {
        title: '设备编号',
        dataIndex: 'assetNo',
      },
      {
        title: '包间',
        dataIndex: 'spaceGuid',
        render: spaceGuid => spaceGuid?.roomTag,
      },
      {
        title: '三级分类',
        dataIndex: 'deviceType',
        render: deviceType => {
          if (!deviceTypeMapping || !deviceTypeMapping[deviceType]) {
            return deviceType;
          }
          return deviceTypeMapping[deviceType].metaName;
        },
      },
    ];
  }
  if (code === 0) {
    tmp = [
      {
        title: '三级分类',
        dataIndex: 'spareType',
        render: spareType => {
          if (!deviceTypeMapping || !deviceTypeMapping[spareType]) {
            return spareType;
          }
          return deviceTypeMapping[spareType].metaName;
        },
      },
      {
        title: '包间',
        dataIndex: 'roomTag',
      },
      {
        title: '剩余数量',
        dataIndex: 'totalNum',
      },
    ];
  }
  return tmp;
};
