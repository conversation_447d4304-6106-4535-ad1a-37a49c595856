import cloneDeep from 'lodash.clonedeep';
import pick from 'lodash.pick';

import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Input } from '@manyun/base-ui.ui.input';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { MetaTypeText } from '@manyun/resource-hub.ui.meta-type-text';
import { BackendTaskStatus, RiskStatusMap } from '@manyun/ticket.model.ticket';
import {
  Detail,
  NewRiskRegisterTicket,
  RiskLevelSelect,
  RiskStatusSelect,
} from '@manyun/ticket.page.risk-register';
import { ImportRiskTicketsModal } from '@manyun/ticket.ui.import-risk-tickets-modal';
import { TicketStatusSelect } from '@manyun/ticket.ui.ticket-status-select';

import { CURRENT_PAGE_TYPES } from '../../constants';
import TTR from './../../registries/ticket-type-registry';

TTR.registerTicketType('risk_register')
  .registerConfig({
    type: CURRENT_PAGE_TYPES.TICKETS,
    showNewBtn: true,
    mergeFilters: ({ DEFAULT_FILTER_KEY_MAP, defaultFilters }) => {
      const newFilters = [...defaultFilters].filter(
        ({ key }) =>
          key !== DEFAULT_FILTER_KEY_MAP.IS_DELAY &&
          key !== DEFAULT_FILTER_KEY_MAP.TICKET_TITLE &&
          key !== DEFAULT_FILTER_KEY_MAP.EFFECT_TIME &&
          key !== DEFAULT_FILTER_KEY_MAP.END_TIME
      );

      newFilters.splice(
        newFilters.findIndex(item => item.key === DEFAULT_FILTER_KEY_MAP.TICKET_STATE),
        1,
        {
          label: '工单状态',
          key: 'taskStatusList',
          initialProps: {
            optionFilter: option =>
              ![BackendTaskStatus.INIT, BackendTaskStatus.CLOSE_APPROVER].includes(option.value),
            allowClear: true,
            mode: 'multiple',
          },
          Comp: TicketStatusSelect,
        }
      );

      newFilters.splice(
        newFilters.findIndex(({ dataIndex }) => dataIndex === DEFAULT_FILTER_KEY_MAP.CREATOR_NAME),
        0,
        {
          label: '风险等级',
          key: 'riskLevel',
          Comp: RiskLevelSelect,
        },
        {
          key: DEFAULT_FILTER_KEY_MAP.TICKET_TITLE,
          label: '工单标题',
          Comp: Input,
          whitespace: 'trim',
          initialProps: { allowClear: true },
        },
        {
          label: '风险解除状态',
          key: 'riskStatus',
          Comp: RiskStatusSelect,
        }
      );
      newFilters.splice(newFilters.length, 0, {
        key: DEFAULT_FILTER_KEY_MAP.EFFECT_TIME,
        label: '创建时间',
        Comp: DatePicker.RangePicker,
        initialProps: {
          showTime: true,
          placeholder: ['开始时间', '结束时间'],
        },
        span: 2,
      });

      return newFilters;
    },
    mergeColumns: ({ DEFAULT_COLUMN_DATA_INDEX_MAP, defaultColumns }) => {
      const newColumns = [...defaultColumns].filter(
        ({ dataIndex }) =>
          dataIndex !== DEFAULT_COLUMN_DATA_INDEX_MAP.TASK_SLA &&
          dataIndex !== DEFAULT_COLUMN_DATA_INDEX_MAP.TASK_RESPOND_TIME
      );

      const insertIdx = newColumns.findIndex(
        ({ dataIndex }) => dataIndex === DEFAULT_COLUMN_DATA_INDEX_MAP.CREATOR_NAME
      );

      newColumns.splice(
        insertIdx,
        0,
        {
          title: '风险等级',
          dataIndex: 'riskLevel',
          render(_ignored, { riskLevel }) {
            return <MetaTypeText code={riskLevel} metaType={MetaType.riskLevel} />;
          },
        },
        {
          title: '风险解除状态',
          dataIndex: 'riskStatus',
          render(_ignored, { riskStatus }) {
            return <span>{RiskStatusMap[riskStatus]}</span>;
          },
        }
      );

      return newColumns;
    },
    mergeActions: ({ defaultActions }) => {
      const newActions = cloneDeep(defaultActions);
      newActions.splice(0, 0, {
        key: 'showRiskImportBtn',
        Comp: ImportRiskTicketsModal,
        propsUtils: {
          pick: props => pick(props, ['ticketType', 'resetSearchValues', 'initialize']),
        },
      });
      return newActions;
    },
  })
  .registerConfig({
    type: CURRENT_PAGE_TYPES.NEW_TICKET,
    content: NewRiskRegisterTicket,
  })
  .registerConfig({
    type: CURRENT_PAGE_TYPES.SPECIFIC_TICKET,
    content: Detail,
  });
