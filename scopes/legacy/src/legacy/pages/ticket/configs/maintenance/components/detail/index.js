import React from 'react';
import { connect } from 'react-redux';

import { User } from '@manyun/auth-hub.ui.user';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Space } from '@manyun/base-ui.ui.space';

import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  ticketPatrolCheckItemActionCreator,
  ticketPatrolRoomInfoActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/ticketActions';

import ChekcItem from './check-item';
import Room from './room';

export class PatorlDetail extends React.Component {
  state = {
    currentContent: 'room', // room || checkItem
    roomGuid: null,
    roomType: null,
  };

  componentDidMount() {
    this.props.syncCommonData({ strategy: { roomTypes: 'IF_NULL' } });
  }

  changeCurrentContent = (value, roomGuid = null) => {
    this.setState({ currentContent: value, roomGuid });
  };

  render() {
    const { basicInfo, setProviderValue, taskNo } = this.props;
    const { currentContent, roomGuid, roomType } = this.state;

    return (
      <Space style={{ width: '100%', display: 'flex' }} direction="vertical" size="middle">
        {Boolean(basicInfo.assigneeList?.length) && (
          <Descriptions column={4}>
            <Descriptions.Item label="指派人">
              <Space>
                {basicInfo.assigneeList.map(item => (
                  <User.Link key={item.id} id={item.id} name={item.userName} />
                ))}
              </Space>
            </Descriptions.Item>
          </Descriptions>
        )}
        {currentContent === 'room' && basicInfo && (
          <Room
            setProviderValue={setProviderValue}
            taskNo={taskNo}
            changeCurrentContent={this.changeCurrentContent}
            setRoomType={roomType => this.setState({ roomType })}
          />
        )}
        {currentContent === 'checkItem' && basicInfo && (
          <ChekcItem
            roomGuid={roomGuid}
            changeCurrentContent={this.changeCurrentContent}
            basicInfo={basicInfo}
            roomType={roomType}
          />
        )}
      </Space>
    );
  }
}

const mapStateToProps = ({ ticket: { patrol } }) => ({
  patrol,
});

const mapDispatchToProps = {
  ticketPatrolRoomInfoActionCreator: ticketPatrolRoomInfoActionCreator,
  ticketPatrolCheckItemActionCreator: ticketPatrolCheckItemActionCreator,
  syncCommonData: syncCommonDataActionCreator,
};
export default connect(mapStateToProps, mapDispatchToProps)(PatorlDetail);
