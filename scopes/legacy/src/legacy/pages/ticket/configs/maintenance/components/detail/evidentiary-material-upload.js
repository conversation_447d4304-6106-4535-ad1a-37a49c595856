import { useEffect, useState } from 'react';

import InboxOutlined from '@ant-design/icons/es/icons/InboxOutlined';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { McUpload } from '@manyun/dc-brain.ui.upload';
import { useUploadMaintenanceItemFile } from '@manyun/ticket.gql.client.tickets';

export function EvidentiaryMaterialUpload({ evidentiaryMaterial, maintenanceItemId, onSuccess }) {
  const [files, setFiles] = useState([]);
  const [updateVisible, setUpdateVisible] = useState(false);
  const [save] = useUploadMaintenanceItemFile();

  useEffect(() => {
    setFiles(evidentiaryMaterial);
  }, [evidentiaryMaterial]);

  return (
    <>
      <Button type="link" compact onClick={() => setUpdateVisible(true)}>
        上传
      </Button>
      <Modal
        destroyOnClose
        title="上传附件"
        style={{ minWidth: 443 }}
        open={updateVisible}
        okText="提交"
        okButtonProps={{ disabled: evidentiaryMaterial?.length ? false : !files.length }}
        onCancel={() => {
          setUpdateVisible(false);
        }}
        onOk={async () => {
          save({
            variables: {
              query: {
                maintenanceItemId,
                fileInfoList: files.map(obj => McUploadFile.fromJSON(obj)),
              },
            },
            onCompleted(data) {
              if (!data.uploadMaintenanceItemFile?.success) {
                message.error(data.uploadMaintenanceItemFile?.message);
              } else {
                setUpdateVisible(false);
                onSuccess(files.map(file => McUploadFile.fromJSON(file).toApiObject()));
              }
            },
          });
        }}
      >
        <McUpload
          type="drag"
          fileList={files}
          accept="image/*"
          multiple
          onChange={info => {
            if (info.fileList.length >= 10) {
              message.error('图片上传数量不能超过9张');
              return;
            }
            setFiles(info.fileList);
          }}
        >
          <Space direction="vertical">
            <p>
              <InboxOutlined style={{ fontSize: 48, color: `var(--${prefixCls}-primary-color)` }} />
            </p>
            <Typography.Text>点击或将文件拖拽到这里上传</Typography.Text>
            <Typography.Text type="secondary">支持扩展名：image/*;图片上限9张</Typography.Text>
          </Space>
        </McUpload>
      </Modal>
    </>
  );
}

export default EvidentiaryMaterialUpload;
