import Form from '@ant-design/compatible/es/form';
import QuestionCircleOutlined from '@ant-design/icons/es/icons/QuestionCircleOutlined';
import { ApiSelect, Select } from '@galiojs/awesome-antd';
import React from 'react';
import { connect } from 'react-redux';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Button } from '@manyun/base-ui.ui.button';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { DeviceTypeText } from '@manyun/resource-hub.ui.device-type-text';
import { SlaUnit } from '@manyun/ticket.model.task';
import { MaintenanceTaskSubType } from '@manyun/ticket.model.ticket';
import { getMaintenanceContent } from '@manyun/ticket.state.ticket';
import { SlaSelect } from '@manyun/ticket.ui.sla-select';

import {
  AssetClassificationApiTreeSelect,
  FooterToolBar,
  GutterWrapper, // ApiTreeSelect,
  LocationCascader,
  TinyCard,
} from '@manyun/dc-brain.legacy.components';
import { getTableData } from '@manyun/dc-brain.legacy.components/inspection-table/utils';
import { StyledMcUpload } from '@manyun/dc-brain.legacy.pages/ticket/configs/access-door/styled';
import { getFileInfoList } from '@manyun/dc-brain.legacy.pages/ticket/configs/access-door/utils';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import { ticketActions } from '@manyun/dc-brain.legacy.redux/actions/ticketActions';
import { roomManageService, ticketService } from '@manyun/dc-brain.legacy.services';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';

import RadioTables from '../../../../../maintain-config/create/components/index';
// import InspectionTable from '@manyun/dc-brain.legacy.components/inspection-table';
import Device from './device';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 2 },
    xl: { span: 2 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
    xl: { span: 20 },
  },
};

export class NewTicket extends React.Component {
  state = {
    // roomList: [],
    tools: { tableData: [] },
    securityStds: { tableData: [] },
    maintenanceItems: { tableData: [] },
    deviceTypeList: [],
    subBtnLoading: false,
  };
  maintainData = {
    tools: { tableData: [] },
    securityStds: { tableData: [] },
    maintenanceItems: { tableData: [] },
  };

  componentDidMount() {
    this.props.syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });
    this.props.syncCommonData({ strategy: { roomTypes: 'IF_NULL' } });
    this.props.syncCommonData({ strategy: { ticketTypes: 'IF_NULL' } });
  }

  onChangeCheckItem = checkItems => {
    const { deviceInfoList, checkItem } = this.props;
    // const device = checkItem[INSPECTION_TYPE_KEY_MAP.DEVICE]
    //   ? Object.keys(checkItem[INSPECTION_TYPE_KEY_MAP.DEVICE])
    //   : [];
    const device = getTableData(checkItem).map(item => item.deviceType);
    const deviceList = deviceInfoList.filter(item => device.includes(item.deviceType));

    this.props.setCreateMaintenanceDevices(deviceList);
    this.props.setCreateMaintenanceCheckItem(checkItems);
    this.props.form.setFieldsValue({ checkItem: getTableData(checkItems) });
  };

  onChangeSpecifyRoom = e => {
    if (!e.target.value) {
      this.props.setCreateMaintenanceRooms([]);
    }
  };

  onChangeRoomList = (value, rooms) => {
    const { deviceInfoList, form } = this.props;
    const deviceList = deviceInfoList.filter(item => value.includes(item.spaceGuid.roomGuid));
    this.props.setCreateMaintenanceRooms(rooms);
    this.props.setCreateMaintenanceDevices(deviceList);
    form.setFieldsValue({ deviceInfoList: deviceList });
  };

  validateDevice = (_, value, callback) => {
    if (value && value.length) {
      callback();
    } else {
      callback('至少添加一个设备');
    }
  };

  onSubmit = () => {
    this.props.form.validateFields(async (errs, values) => {
      if (errs) {
        return;
      }
      const { roomInfoList, deviceInfoList } = this.props;
      const fileInfoList = getFileInfoList(values?.fileInfoList);

      const params = {
        title: values.title,
        idcTag: values.location[0],
        blockGuid: `${values.location[0]}.${values.location[1]}`,
        maintenanceType: values.maintenanceType,
        deviceTypeList: values.deviceTypeList,
        sla: values.taskSla.sla,
        slaUnit: values.taskSla.unit,
        roomInfoList: roomInfoList.map(item => {
          return {
            roomTag: item.tag,
            roomGuid: item.guid,
            roomFloor: item.floor,
            roomType: item.roomType?.code,
            blockGuid: `${item.idcTag}.${item.blockTag}`,
            idcTag: item.idcTag,
            sort: item.sort,
          };
        }),
        deviceInfoList: deviceInfoList.map(item => {
          return {
            deviceGuid: item.guid,
            deviceTag: item.name,
            deviceType: item.deviceType,
            roomGuid: item.spaceGuid.roomGuid,
            productModel: item.productModel,
            sort: item.sort,
          };
        }),
        fileInfoList,
      };
      if (values.assigneeList) {
        params.assignee = values.assigneeList.label;
        params.assigneeId = values.assigneeList.key;
      }
      const { tools, securityStds, maintenanceItems } = this.state;
      if (!securityStds.tableData.length) {
        message.error('至少添加1条安全标准!');
        return false;
      }
      if (!maintenanceItems.tableData.length) {
        message.error('至少添加1个维护项!');
        return false;
      }
      if (tools.editingRowKey) {
        message.error('有尚未保存的工具仪器!');
        return false;
      }
      if (securityStds.editingRowKey) {
        message.error('有尚未保存的安全标准!');
        return false;
      }
      if (maintenanceItems.editingRowKey) {
        message.error('有尚未保存的维护内容!');
        return false;
      }
      this.setState({ subBtnLoading: true });
      const data = getMaintenanceContent(tools, securityStds, maintenanceItems);

      params.maintenanceContent = data.maintenanceContent;
      const { error, response } = await ticketService.maintenanceCreate(params);
      this.setState({ subBtnLoading: false });

      if (error) {
        message.error(error);
        return;
      }
      message.success('新建维护工单成功');
      setTimeout(() => {
        this.props.history.push(
          urls.generateTicketDetailUrl({ ticketType: this.props.ticketType, id: response })
        );
      }, 1.5 * 1000);
    });
  };

  getCheckPoints = point => {
    let list = [];
    if (Array.isArray(point)) {
      list = point.map(item => {
        let diValueText = '';
        if (typeof item.dataType === 'string' && item.dataType === 'DI') {
          diValueText = item.diValueText;
        }
        if (typeof item.dataType === 'object' && item.dataType?.code === 'DI') {
          diValueText = item.validLimits.join(',');
        }
        return {
          deviceType: item.deviceType,
          pointName: item.pointName ? item.pointName : item.name,
          pointCode: item.pointCode,
          dataType: typeof item.dataType === 'string' ? item.dataType : item.dataType?.code,
          isMeterRead: item.isMeterRead,
          metaName: item.metaName,
          unit: item.unit,
          diValueText: diValueText,
        };
      });
    }
    return list;
  };

  getCheckItems = checkItem => {
    if (!checkItem) {
      return [];
    }
    const list = checkItem.map(item => {
      return {
        checkItemName: item.checkItemName?.value,
        checkStdInfoList: item.standards?.map(standardsItem => standardsItem.standardTxt.value),
      };
    });
    return list;
  };

  validateCheckItem = (_, value, callback) => {
    if (value && value.length) {
      callback();
    } else {
      callback('至少添加一个巡检项');
    }
  };

  onChangeSpecifyDevice = e => {
    if (!e.target.value) {
      this.props.setCreateMaintenanceDevices([]);
    }
  };

  normalizeDevice = () => {
    const { deviceInfoList } = this.props;
    return deviceInfoList;
  };

  setMaintainData = value => {
    // this.props.form.setFieldsValue({ deviceTypeList: value.deviceTypeList });
    this.setState({
      ...value,
    });
    // this.maintainData = value;
  };

  render() {
    const { getFieldDecorator, getFieldsValue, setFieldsValue } = this.props.form;
    const { nomalizedDeviceCategory, ticketTypes, history } = this.props;
    const {
      location,
      deviceTypeList,
      specifyRoom,
      specifyDevice,
      maintenanceType,
      configurationType,
    } = getFieldsValue();
    const params = {
      idcTag: location?.[0],
      blockTag: location?.[1],
      deviceTypeList: deviceTypeList,
    };
    if (maintenanceType === MaintenanceTaskSubType.M_SJ) {
      params.deviceTypeIsSpare = true;
      params.roomType = 'WAREHOUSE';
    }
    const { subBtnLoading, deviceTypeList: importDeviceTypeList } = this.state;
    const deviceAndRoomView = (
      <>
        {maintenanceType !== 'M_SJ' && (
          <Col span={24}>
            <Form.Item label="是否指定设备">
              {getFieldDecorator('specifyDevice', {
                rules: [{ required: true, message: '是否指定设备必选！' }],
                initialValue: false,
              })(
                <Radio.Group onChange={this.onChangeSpecifyDevice}>
                  <Radio value>是</Radio>
                  <Radio value={false}>否</Radio>
                </Radio.Group>
              )}
            </Form.Item>
          </Col>
        )}
        {specifyDevice && (
          <Col span={24}>
            <Form.Item wrapperCol={{ span: 24 }} style={{ width: '100%' }}>
              {getFieldDecorator('deviceInfoList', {
                rules: [{ validator: this.validateDevice }],
              })(
                <Device
                  fieldValues={getFieldsValue()}
                  nomalizedDeviceCategory={nomalizedDeviceCategory}
                />
              )}
            </Form.Item>
          </Col>
        )}
        <Col span={24}>
          <Form.Item label="是否指定包间">
            {getFieldDecorator('specifyRoom', {
              rules: [{ required: true, message: '是否指定包间必选！' }],
              initialValue: false,
            })(
              <Radio.Group onChange={this.onChangeSpecifyRoom}>
                <Radio value>是</Radio>
                <Radio value={false}>否</Radio>
              </Radio.Group>
            )}
          </Form.Item>
        </Col>
        {specifyRoom && (
          <Col span={24}>
            <Form.Item label="请选择包间" labelAlign="left">
              {getFieldDecorator('room', {
                rules: [{ required: true, message: '包间必填！' }],
              })(
                <ApiSelect
                  style={{ minWidth: 200, maxWidth: 960 }}
                  disabled={!(location && location.length === 2 && deviceTypeList)}
                  mode="multiple"
                  optionWithValue
                  fieldNames={{ label: 'tag', value: 'guid' }}
                  dataService={async q => {
                    const { response } = await roomManageService.fetchRoomByDeviceType(q);
                    if (response) {
                      return Promise.resolve(response.data);
                    } else {
                      return Promise.resolve([]);
                    }
                  }}
                  serviceQueries={[params]}
                  onChange={this.onChangeRoomList}
                />
              )}
            </Form.Item>
          </Col>
        )}
      </>
    );

    return (
      <GutterWrapper mode="vertical">
        <Space style={{ width: '100%' }} direction="vertical">
          <TinyCard title="基本信息">
            <Form colon={false} {...formItemLayout} layout="horizontal">
              <Form.Item label="维护类型">
                {getFieldDecorator('maintenanceType', {
                  rules: [{ required: true, message: '维护类型为必选项！' }],
                })(
                  <Select style={{ width: 200 }}>
                    {ticketTypes.map(({ taskType, taskValue }) => {
                      return (
                        <Select.Option key={taskType} value={taskType}>
                          {taskValue}
                        </Select.Option>
                      );
                    })}
                  </Select>
                )}
              </Form.Item>
              <Form.Item label="位置">
                {getFieldDecorator('location', {
                  rules: [
                    {
                      required: true,
                      message: '所属机房/楼栋必选！',
                      type: 'number',
                      transform: value => (value?.length === 2 ? 2 : false),
                    },
                  ],
                })(
                  <LocationCascader
                    currentAuthorize
                    style={{ width: 200 }}
                    onChange={values => {
                      if (!values.length) {
                        setFieldsValue({ room: [] });
                        this.props.setCreateMaintenanceRooms([]);
                        this.props.setCreateMaintenanceDevices([]);
                      }
                    }}
                  />
                )}
              </Form.Item>
              <Form.Item label="工单标题">
                {getFieldDecorator('title', {
                  rules: [
                    { required: true, message: '工单标题为必填！' },
                    {
                      type: 'string',
                      max: 20,
                      message: '最多输入 20 个字符！',
                    },
                  ],
                })(<Input style={{ width: 300 }} />)}
              </Form.Item>
              <Form.Item label="指派人">
                {getFieldDecorator('assigneeList')(
                  <UserSelect
                    disabled={!location || (location && location.length !== 2)}
                    blockGuid={location && location.join('.')}
                    labelInValue
                    includeCurrentUser
                    allowClear
                    style={{ width: 200 }}
                  />
                )}
                &nbsp;&nbsp;
                <Tooltip title="指派后，仅指派人可执行工单">
                  <QuestionCircleOutlined />
                </Tooltip>
              </Form.Item>
              <Form.Item label="SLA">
                {getFieldDecorator('taskSla', {
                  initialValue: { sla: 1, unit: SlaUnit.Day },
                })(<SlaSelect />)}
              </Form.Item>
              <Form.Item label="附件">
                {getFieldDecorator('fileInfoList', {
                  valuePropName: 'fileList',
                  normalize: value => {
                    if (Array.isArray(value)) {
                      return value;
                    }
                    return value?.fileList || [];
                  },
                })(
                  <StyledMcUpload
                    accept="image/*,.xls,.xlsx"
                    showUploadList
                    maxFileSize={20}
                    allowDelete
                    showAccept
                  >
                    <Button type="primary" style={{ marginTop: 8 }}>
                      上传
                    </Button>
                  </StyledMcUpload>
                )}
              </Form.Item>
            </Form>
          </TinyCard>
          <TinyCard style={{ marginBottom: 40 }} title="配置">
            <Form labelAlign="left" layout="inline">
              <Row gutter={[16, 16]}>
                <Col span={24}>
                  <Form.Item label="配置方式">
                    {getFieldDecorator('configurationType', {
                      rules: [{ required: true, message: '配置方式为必选项！' }],
                      initialValue: 'custom',
                    })(
                      <Radio.Group
                        onChange={() => {
                          setFieldsValue({ room: [], deviceTypeList: [], specifyDevice: false });
                          this.setState({
                            tools: { tableData: [] },
                            securityStds: { tableData: [] },
                            maintenanceItems: { tableData: [] },
                            deviceTypeList: [],
                          });
                        }}
                      >
                        {/* <Radio value="import">导入配置</Radio> */}
                        <Radio value="custom">自定义</Radio>
                      </Radio.Group>
                    )}
                  </Form.Item>
                </Col>
                {configurationType === 'custom' && (
                  <Col span={24}>
                    <Form.Item label="三级分类">
                      {getFieldDecorator('deviceTypeList', {
                        rules: [{ required: true, message: '三级分类为必选项！' }],
                      })(
                        <AssetClassificationApiTreeSelect
                          requestOnDidMount
                          allowClear
                          multiple
                          disabledDepths={[0, 1]}
                          dataType={maintenanceType === 'M_SJ' ? ['noSnDevice'] : ['snDevice']}
                          category="categorycode"
                          style={{ minWidth: 200, maxWidth: 960 }}
                          onChange={value => {
                            setFieldsValue({
                              room: [],
                            });
                            this.setState({
                              tools: { tableData: [] },
                              securityStds: { tableData: [] },
                              maintenanceItems: { tableData: [] },
                            });
                            this.props.setCreateMaintenanceRooms([]);
                            this.props.setCreateMaintenanceDevices([]);
                          }}
                        />
                      )}
                    </Form.Item>
                  </Col>
                )}
                {configurationType === 'custom' && deviceAndRoomView}
                <RadioTables
                  showInportButton={configurationType === 'import'}
                  maintenanceType={maintenanceType}
                  deviceType={this.props.form.getFieldValue('deviceTypeList')}
                  clearData={configurationType === 'custom'}
                  recoverConfigTableContent={value => {
                    this.setMaintainData(value);
                  }}
                />
                {!!(configurationType === 'import' && importDeviceTypeList.length) && (
                  <Col span={24}>
                    <Form.Item label="三级分类">
                      {getFieldDecorator('deviceTypeList', {
                        rules: [{ required: true, message: '三级分类为必选项！' }],
                        initialValue: importDeviceTypeList,
                      })(
                        <Select mode="multiple" style={{ minWidth: 200, maxWidth: 960 }}>
                          {importDeviceTypeList.map(deviceType => {
                            return (
                              <Select.Option key={deviceType} value={deviceType}>
                                <DeviceTypeText code={deviceType} />
                              </Select.Option>
                            );
                          })}
                        </Select>
                      )}
                    </Form.Item>
                  </Col>
                )}
                {configurationType === 'import' && deviceAndRoomView}
              </Row>
            </Form>
          </TinyCard>
        </Space>

        <FooterToolBar>
          <GutterWrapper>
            <Button type="primary" loading={subBtnLoading} onClick={this.onSubmit}>
              提交
            </Button>
            <Button loading={subBtnLoading} onClick={() => history.goBack()}>
              取消
            </Button>
          </GutterWrapper>
        </FooterToolBar>
      </GutterWrapper>
    );
  }
}
const connectOpts = { forwardRef: true };

const mapStateToProps = ({
  ticket: {
    maintenance: {
      new: { deviceInfoList, roomInfoList, checkItem },
    },
  },
  common: { deviceCategory, roomTypes, ticketTypes },
}) => {
  let types = [];
  ticketTypes &&
    ticketTypes.treeList.forEach(item => {
      if (item.taskType === 'MAINTENANCE') {
        types = item.children;
      }
    });
  return {
    // fieldValues,
    deviceInfoList,
    roomInfoList,
    checkItem,
    nomalizedDeviceCategory: deviceCategory ? deviceCategory.normalizedList : {},
    roomTypes: roomTypes,
    ticketTypes: types,
  };
};

const mapDispatchToProps = {
  // updateValues: ticketActions.updateValues,
  setCreateMaintenanceRooms: ticketActions.setCreateMaintenanceRooms,
  setCreateMaintenanceCheckItem: ticketActions.setCreateMaintenanceCheckItem,
  setCreateMaintenanceDevices: ticketActions.setCreateMaintenanceDevices,
  syncCommonData: syncCommonDataActionCreator,
};

// const createOptions = {
// onFieldsChange({ updateValues }, changedFields) {
//   updateValues(changedFields);
// },
// mapPropsToFields({ fieldValues }) {
//   return Object.keys(fieldValues).reduce((formFields, fieldKey) => {
//     formFields[fieldKey] = Form.createFormField(fieldValues[fieldKey]);
//     return formFields;
//   }, {});
// },
// };
export default connect(
  mapStateToProps,
  mapDispatchToProps,
  null,
  connectOpts
)(Form.create()(NewTicket));
