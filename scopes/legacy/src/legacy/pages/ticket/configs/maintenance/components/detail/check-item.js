import CheckOutlined from '@ant-design/icons/es/icons/CheckOutlined';
import PageHeader from 'antd/es/page-header';
import React, { useState } from 'react';
import { connect } from 'react-redux';
import { Link, Redirect } from 'react-router-dom';

import { useAuthorized } from '@manyun/auth-hub.hook.gql.use-authorized';
import { User } from '@manyun/auth-hub.ui.user';
import { Button } from '@manyun/base-ui.ui.button';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { SimpleFileList } from '@manyun/base-ui.ui.file-list';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Table } from '@manyun/base-ui.ui.table';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { generateSpaceOrDeviceRoutePath } from '@manyun/monitoring.route.monitoring-routes';
import { useBatchMaintenanceCheckItems } from '@manyun/ticket.gql.client.tickets';
import { BackendTaskStatus, MaintenanceTaskSubType } from '@manyun/ticket.model.ticket';
import {
  generateEventDetailRoutePath,
  generateEvnetLocation,
  generateTicketUrl,
} from '@manyun/ticket.route.ticket-routes';
import { RECORD_METHOD_KEY_MAP } from '@manyun/ticket.ui.maintenance-items';

import { GutterWrapper, StatusText, TinyTable } from '@manyun/dc-brain.legacy.components';
import { STATUS_MAP } from '@manyun/dc-brain.legacy.constants/statusColor';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  ticketActions,
  ticketMaintenanceCheckItemActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/ticketActions';
import { ticketService } from '@manyun/dc-brain.legacy.services';
import { getRowSpan } from '@manyun/dc-brain.legacy.utils';

import ExceptionModal from '../../../../components/exception-modal';
import { CHECK_SUBJECT_KEY_MAPS } from '../../../../constants';
import {
  MAINTENANCE_OPERATION_STATE_TEXT,
  MAINTENANCE_RUNNING_STATE_TEXT,
  MAINTENANCE_STATE,
} from '../constants';
import EvidentiaryMaterialUpload from './evidentiary-material-upload';

const columns = (
  ctx,
  dataSource,
  nomalizedDeviceCategory,
  basicInfo,
  authorized,
  rowSpansGroupByDataIndex
) => [
  {
    title: '设备类型',
    dataIndex: 'deviceType',
    className: 'merge-row-span',
    onCell: (record, index) => {
      return {
        rowSpan: getRowSpan(record.deviceType, index, {
          datasource: dataSource,
          dataIndex: 'deviceType',
          rowSpansGroupByDataIndex,
        }),
      };
    },
    render: (text, record, index) => {
      if (!text) {
        return '--';
      }

      return nomalizedDeviceCategory[text]?.metaName;
    },
  },
  {
    title: '维护对象',
    dataIndex: 'deviceTag',
    className: 'merge-row-span',
    onCell: (record, index) => {
      return {
        rowSpan: getRowSpan(record.deviceGuid, index, {
          datasource: dataSource,
          dataIndex: 'deviceGuid',
          rowSpansGroupByDataIndex,
        }),
      };
    },
    render: (text, record, index) => {
      if (!text) {
        return '';
      }

      if (basicInfo.taskSubType === MaintenanceTaskSubType.M_SJ) {
        const [vender, modal] = record.deviceGuid.split(',');

        return (
          <>
            <Tooltip title="厂商">{vender}</Tooltip>， <Tooltip title="型号">{modal}</Tooltip>
          </>
        );
      }
      return (
        <Link
          to={generateSpaceOrDeviceRoutePath({
            guid: record.deviceGuid,
          })}
        >
          {text}
        </Link>
      );
    },
  },
  {
    title: '维护类型',
    className: 'merge-row-span',
    dataIndex: 'maintenanceType',
    onCell: (record, index) => {
      return {
        rowSpan: getRowSpan(record.maintenanceTypeMerge, index, {
          datasource: dataSource,
          dataIndex: 'maintenanceTypeMerge',
          rowSpansGroupByDataIndex,
        }),
      };
    },
    render: (text, record, index) => {
      if (!text) {
        return '';
      }

      return text;
    },
  },
  {
    title: '维护项',
    className: 'merge-row-span',
    dataIndex: 'itemName',
    width: 360,
    onCell: (record, index) => {
      return {
        rowSpan: getRowSpan(record.itemNameMerge, index, {
          datasource: dataSource,
          dataIndex: 'itemNameMerge',
          rowSpansGroupByDataIndex,
        }),
      };
    },
    render: text => {
      if (!text) {
        return '';
      }

      return (
        <Typography.Text style={{ width: 360 }} ellipsis={{ tooltip: true }}>
          {text}
        </Typography.Text>
      );
    },
  },

  {
    title: '维护方法',
    className: 'merge-row-span',
    dataIndex: 'maintenanceMethod',
    onCell: (record, index) => {
      return {
        rowSpan: getRowSpan(record.maintenanceMethodMerge, index, {
          datasource: dataSource,
          dataIndex: 'maintenanceMethodMerge',
          rowSpansGroupByDataIndex,
        }),
      };
    },
    render: (text, record, index) => {
      if (!text) {
        return '';
      }

      return text;
    },
  },
  {
    title: '维护标准',
    dataIndex: 'maintenanceStd',
    render: text => <span>{text || '--'}</span>,
  },
  {
    title: '维护数量',
    dataIndex: 'upperLimitValue',
    visible: basicInfo.taskSubType === MaintenanceTaskSubType.M_SJ,
    render: text => {
      if (!text) {
        return '--';
      }
      return text;
    },
  },
  {
    title: '工具仪器',
    dataIndex: 'tools',
    width: 360,
    render: text => (
      <Typography.Text style={{ width: 360 }} ellipsis={{ tooltip: true }}>
        {text ?? '--'}
      </Typography.Text>
    ),
  },
  {
    title: '处理人',
    dataIndex: 'operatorName',
    render: (text, record) => {
      return text && <User.Link id={record.operator} name={text} />;
    },
  },
  {
    title: '维护结果',
    dataIndex: 'maintenanceValue',
    render: (text, record) => {
      if (basicInfo.taskStatus !== BackendTaskStatus.PROCESSING || !authorized) {
        if (text !== null) {
          if (record.recordMethod === RECORD_METHOD_KEY_MAP.RUNING) {
            return (
              <Typography.Text type={text === MAINTENANCE_STATE.EXCEPTION ? 'danger' : 'default'}>
                {MAINTENANCE_RUNNING_STATE_TEXT[text]}
              </Typography.Text>
            );
          }
          if (record.recordMethod === RECORD_METHOD_KEY_MAP.OPERATION) {
            return MAINTENANCE_OPERATION_STATE_TEXT[text];
          }
          if (record.recordMethod === RECORD_METHOD_KEY_MAP.NUMBER) {
            return (
              <Typography.Text type={text === MAINTENANCE_STATE.EXCEPTION ? 'danger' : 'success'}>
                {text}
                {record.valueUnit || ''}
              </Typography.Text>
            );
          }
          return text;
        }
        return '--';
      }
      if (record.recordMethod === RECORD_METHOD_KEY_MAP.RUNING) {
        // 运行状态
        return (
          <Radio.Group
            style={{ minWidth: '200px' }}
            disabled={text === MAINTENANCE_STATE.EXCEPTION}
            value={text}
            onChange={ctx.handleCheckItemStatus(record)}
          >
            <Radio value={MAINTENANCE_STATE.NORMAL}>正常</Radio>
            <Radio value={MAINTENANCE_STATE.EXCEPTION}>
              <Typography.Text
                type={
                  ctx.state.exceptionInfo.id === record.id || text === MAINTENANCE_STATE.EXCEPTION
                    ? 'danger'
                    : 'default'
                }
              >
                异常
              </Typography.Text>
            </Radio>
          </Radio.Group>
        );
      }
      if (record.recordMethod === RECORD_METHOD_KEY_MAP.OPERATION) {
        // 操作状态
        return (
          <Radio.Group
            // disabled={text === MAINTENANCE_STATE.EXCEPTION}
            value={text}
            onChange={ctx.handleChangeOperationState(record)}
          >
            <Radio value={MAINTENANCE_STATE.NORMAL}>完成</Radio>
            <Radio value={MAINTENANCE_STATE.EXCEPTION}>未完成</Radio>
          </Radio.Group>
        );
      }
      if (record.recordMethod === RECORD_METHOD_KEY_MAP.NUMBER) {
        // 数值
        if (record.editable) {
          return (
            <Space>
              <Input
                value={text}
                allowClear
                style={{ width: 120 }}
                maxLength={9}
                suffix={<CheckOutlined onClick={() => ctx.onSave(record)} />}
                onChange={ctx.onChangeMaintenanceValue(record.id)}
              />
              {record.valueUnit || ''}
            </Space>
          );
        }
        if (
          text !== null &&
          basicInfo.taskSubType !== MaintenanceTaskSubType.M_SJ &&
          (text > record.upperLimitValue || text < record.lowerLimitValue)
        ) {
          return (
            <StatusText style={{ display: 'inline-block' }} fontSize={14} status={STATUS_MAP.ALARM}>
              {text} {record.valueUnit || ''}
            </StatusText>
          );
        }
        return (
          <GutterWrapper>
            <span>
              {text} {record.valueUnit || ''}
            </span>
            <Button
              type="link"
              style={{ padding: 0, height: 'auto' }}
              onClick={() => ctx.handleEdit(record.id)}
            >
              修改
            </Button>
          </GutterWrapper>
        );
      }
      return '--';
    },
  },
  {
    title: '备注说明',
    dataIndex: 'remark',
    width: 360,
    className: 'merge-row-span',
    onCell: (record, index) => {
      return {
        rowSpan: getRowSpan(record.itemNameMerge, index, {
          datasource: dataSource,
          dataIndex: 'itemNameMerge',
          rowSpansGroupByDataIndex,
        }),
      };
    },
    render: (remark, record) => {
      const { handleBizType, handleBizId, handleType } = record;
      const isEvent = handleBizType === 'EVENT' || handleBizType === 'NEW_EVENT';

      return handleBizId ? (
        <Typography.Text style={{ width: 360 }} ellipsis={{ tooltip: true }}>
          {handleType === 'CREATE' ? '创建' : '关联'}
          {isEvent ? '事件' : '维修'}工单：
          <Typography.Text>
            {isEvent ? (
              <Link
                to={
                  handleBizType === 'NEW_EVENT'
                    ? generateEvnetLocation({
                        id: handleBizId,
                      })
                    : generateEventDetailRoutePath({
                        id: handleBizId,
                      })
                }
              >
                {handleBizId}
              </Link>
            ) : (
              <Link
                target="_blank"
                to={{
                  pathname: generateTicketUrl({
                    ticketType: 'repair',
                    id: handleBizId,
                  }),
                }}
              >
                {handleBizId}
              </Link>
            )}
          </Typography.Text>
          <br />
          <p>{remark}</p>
        </Typography.Text>
      ) : (
        <Typography.Text style={{ width: 360 }} ellipsis={{ tooltip: true }}>
          {remark || '--'}
        </Typography.Text>
      );
    },
  },
  {
    title: '现场照片',
    dataIndex: 'fileInfoList',
    className: 'merge-row-span',
    onCell: (record, index) => {
      return {
        rowSpan: getRowSpan(record.itemNameMerge, index, {
          datasource: dataSource,
          dataIndex: 'itemNameMerge',
          rowSpansGroupByDataIndex,
        }),
      };
    },
    render: (_, record, index) => {
      if (!record?.fileInfoList?.length) {
        return 0;
      }

      return (
        <SimpleFileList files={record.fileInfoList.map(file => McUploadFile.fromApiObject(file))}>
          <Button type="link" compact>
            {record.fileInfoList.length}
          </Button>
        </SimpleFileList>
      );
    },
  },

  {
    title: '操作',
    dataIndex: 'operatin',
    className: 'merge-row-span',
    width: 80,
    // fixed: 'right',
    onCell: (record, index) => {
      return {
        rowSpan: getRowSpan(record.itemNameMerge, index, {
          datasource: dataSource,
          dataIndex: 'itemNameMerge',
          rowSpansGroupByDataIndex,
        }),
      };
    },
    render: (_, record) => {
      if (basicInfo.taskStatus !== BackendTaskStatus.PROCESSING || !authorized) {
        return '--';
      }

      return (
        <EvidentiaryMaterialUpload
          targetType={basicInfo.taskType}
          maintenanceItemId={record.id}
          evidentiaryMaterial={
            record?.fileInfoList?.length
              ? record.fileInfoList.map(file => McUploadFile.fromApiObject(file))
              : []
          }
          onSuccess={fileInfoList => {
            ctx.setCheckItem(record.id, { fileInfoList: fileInfoList });
            this.props.ticketMaintenanceCheckItemActionCreator({
              roomGuid: this.props.roomGuid,
              taskNo: this.props.basicInfo.taskNo,
            });
          }}
        />
      );
    },
  },
];

const securityColumns = [
  {
    title: '序号',
    dataIndex: 'index',
    width: '30%',
    render: (_, __, index) => index + 1,
  },
  {
    title: '标准要求',
  },
];

export class MaintenanceDetail extends React.Component {
  state = {
    primaryKey: '',
    exceptionVisible: false,
    exceptionInfo: {},
  };

  componentDidMount() {
    this.props.ticketMaintenanceCheckItemActionCreator({
      roomGuid: this.props.roomGuid,
      taskNo: this.props.basicInfo.taskNo,
    });
    this.props.syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });
  }

  handleChangeCurrentContent = () => {
    this.props.changeCurrentContent('room');
  };

  handleChangeCheckableTag = values => {
    this.props.setMaintenanceCheckedTypes(values);
  };

  getList = dataSource => {
    const { checkedCheckItemTypes } = this.props;
    const { primaryKey } = this.state;
    let list = [...dataSource];
    if (checkedCheckItemTypes.length) {
      list = list.filter(checkItem => checkedCheckItemTypes.includes(checkItem.deviceType));
    } else {
      list = [];
    }
    if (primaryKey) {
      list = list.filter(checkItem =>
        checkItem.deviceTag.toLowerCase().includes(primaryKey.toLowerCase())
      );
    }
    list = list.sort((a, b) => {
      const sortA = a.sort === null ? Infinity : a.sort; // null 视为最大值
      const sortB = b.sort === null ? Infinity : b.sort;
      return sortA - sortB;
    });
    return list;
  };

  handleEdit = id => {
    this.setCheckItem(id, { editable: true });
  };

  onSave = async record => {
    const { basicInfo } = this.props;
    if (!record.maintenanceValue) {
      return message.error('请填写数值');
    }
    if (basicInfo.taskSubType === MaintenanceTaskSubType.M_SJ) {
      const res = /^[+]{0,1}(\d+)$/;
      if (!res.test(record.maintenanceValue)) {
        return message.error('请输入大于等于0的整数');
      }
    }
    if (
      record.maintenanceValue > record.upperLimitValue ||
      record.maintenanceValue < record.lowerLimitValue
    ) {
      if (basicInfo.taskSubType === MaintenanceTaskSubType.M_SJ) {
        const { error } = await ticketService.maintenanceItemExceptionResultHandle({
          taskNo: record.taskNo,
          maintenanceItemId: record.id,
          idcTag: basicInfo.idcTag,
          blockGuid: basicInfo.blockTag,
          deviceType: record.deviceType,
          deviceGuid: record.deviceGuid,
          maintenanceValue: record.maintenanceValue,
          maintenanceType: basicInfo.taskSubType,
        });
        if (error) {
          message.error(error);
        }
        this.setCheckItem(record.id, { editable: false });

        return;
      }
      this.setState({
        exceptionVisible: true,
        exceptionInfo: record,
      });
      return;
    }
    const { error } = await ticketService.maintenanceCheckItemNormalSave({
      taskNo: record.taskNo,
      maintenanceItemId: record.id,
      maintenanceValue: record.maintenanceValue,
      maintenanceType: basicInfo.taskSubType,
    });
    if (error) {
      message.error(error);
      return;
    }
    this.setCheckItem(record.id, { editable: false });
    this.props.ticketMaintenanceCheckItemActionCreator({
      roomGuid: this.props.roomGuid,
      taskNo: this.props.basicInfo.taskNo,
    });
  };

  handleChangeOperationState = record => async e => {
    const { basicInfo } = this.props;
    const { error } = ticketService.maintenanceCheckItemNormalSave({
      taskNo: record.taskNo,
      maintenanceItemId: record.id,
      maintenanceValue: e.target.value,
      maintenanceType: basicInfo.taskSubType,
    });
    if (error) {
      message.error(error);
      return;
    }
    this.setCheckItem(record.id, { maintenanceValue: e.target.value });
    this.props.ticketMaintenanceCheckItemActionCreator({
      roomGuid: this.props.roomGuid,
      taskNo: this.props.basicInfo.taskNo,
    });
  };

  onChangeMaintenanceValue = id => e => {
    this.setCheckItem(id, { maintenanceValue: e.target.value.trim() });
  };
  setCheckItem = (id, editValues) => {
    const { checkItem } = this.props;
    const list = checkItem.map(item => {
      return {
        securityStds: item.securityStds,
        maintenanceItemList: item.maintenanceItemList.map(maintenanceItem => {
          if (maintenanceItem.id === id) {
            return { ...maintenanceItem, ...editValues };
          }
          return {
            ...maintenanceItem,
          };
        }),
      };
    });
    this.props.setMaintenanceCheckItem(list);
  };

  handleCheckItemStatus = record => async e => {
    const value = e.target.value;

    if (value === MAINTENANCE_STATE.NORMAL) {
      const { basicInfo } = this.props;
      this.props.setMaintenanceCheckItemSaveValueLoading();

      const { error } = await ticketService.maintenanceCheckItemNormalSave({
        taskNo: basicInfo.taskNo,
        maintenanceItemId: record.id,
        maintenanceValue: value,
      });
      this.props.setMaintenanceCheckItemSaveValueLoading();
      if (error) {
        message.error(error);
        return;
      }
      this.setCheckItem(record.id, { maintenanceValue: value });
    }
    if (value === MAINTENANCE_STATE.EXCEPTION) {
      this.setState({
        exceptionVisible: true,
        exceptionInfo: { ...record, maintenanceValue: value },
      });
    }
    this.props.ticketMaintenanceCheckItemActionCreator({
      roomGuid: this.props.roomGuid,
      taskNo: this.props.basicInfo.taskNo,
    });
  };

  onExceptionVisible = type => {
    this.setState({ exceptionVisible: false });
    if (type === 'success') {
      const { exceptionInfo } = this.state;
      this.setCheckItem(exceptionInfo.id, {
        maintenanceValue:
          exceptionInfo.recordMethod === RECORD_METHOD_KEY_MAP.NUMBER
            ? exceptionInfo.maintenanceValue
            : 1,
        editable: false,
      });
    }
    if (type === 'cancel') {
      this.setState({ exceptionInfo: {} });
    }
  };

  onConfirm = params => {
    return new Promise(async resolve => {
      const { exceptionInfo } = this.state;
      const { basicInfo } = this.props;
      const { error } = await ticketService.maintenanceItemExceptionResultHandle({
        ...params,
        taskNo: exceptionInfo.taskNo,
        maintenanceItemId: exceptionInfo.id,
        idcTag: basicInfo.idcTag,
        blockGuid: basicInfo.blockTag,
        deviceType: exceptionInfo.deviceType,
        maintenanceValue: exceptionInfo.maintenanceValue,
      });
      if (error) {
        message.error(error);
        resolve(false);
        return;
      }
      resolve(true);
    });
  };

  render() {
    const {
      basicInfo,
      loading,
      checkItemSaveValueLoading,
      checkItem,
      checkItemTypes,
      checkedCheckItemTypes,
      nomalizedDeviceCategory,
      rowSpansGroupByDataIndex,
      roomGuid,
      roomTypes,
      roomInfo,
      roomType,
    } = this.props;
    const { exceptionVisible, exceptionInfo } = this.state;
    const typeFilterList = checkItemTypes.map(item => {
      return { value: item, label: nomalizedDeviceCategory[item]?.metaName };
    });

    const selectedRoom = roomInfo?.find(room => room.roomGuid === roomGuid);

    if (loading) {
      return (
        <GutterWrapper flex justifyContent="center">
          <Spin />
        </GutterWrapper>
      );
    }
    const isCheckItemOnlyHasNumber = isOnlyValueMethodInCheckItems(checkItem);
    return (
      <Space style={{ width: '100%' }} direction="vertical" size={16}>
        <Space style={{ width: '100%', justifyContent: 'space-between' }}>
          <Space>
            <PageHeader
              style={{ padding: 0 }}
              title={
                selectedRoom ? (
                  <span>{`${selectedRoom.roomTag}\n${roomTypes[selectedRoom.roomType]}`}</span>
                ) : (
                  ''
                )
              }
              onBack={this.handleChangeCurrentContent}
            />

            {typeFilterList.length > 1 && (
              <Checkbox.Group
                options={typeFilterList}
                value={checkedCheckItemTypes}
                onChange={this.handleChangeCheckableTag}
              />
            )}
            <Input.Search
              style={{ width: 200 }}
              placeholder="请输入维护对象名称"
              onSearch={value => this.setState({ primaryKey: value })}
            />
          </Space>
          {!isCheckItemOnlyHasNumber && basicInfo.taskStatus === BackendTaskStatus.PROCESSING && (
            <OneKeyMaintenanceButton
              handlerId={basicInfo.taskAssignee}
              taskNo={basicInfo.taskNo}
              roomGuid={roomGuid}
              onSuccess={files => {
                this.props.ticketMaintenanceCheckItemActionCreator({
                  roomGuid: this.props.roomGuid,
                  taskNo: this.props.basicInfo.taskNo,
                });
                this.props.syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });
              }}
            />
          )}
        </Space>
        <RenderAuthorizedTable userId={basicInfo.taskAssignee}>
          {authorized =>
            checkItem.map(item => (
              <div key={item?.maintenanceItemList[0]?.deviceTag}>
                <TinyTable
                  rowKey={record => record}
                  columns={securityColumns}
                  dataSource={item?.securityStds?.split(',')}
                  pagination={false}
                  align="left"
                />
                <Table
                  rowKey="id"
                  scroll={{ x: 2600, y: 680 }}
                  columns={columns(
                    this,
                    this.getList(item.maintenanceItemList),
                    nomalizedDeviceCategory,
                    basicInfo,
                    authorized,
                    rowSpansGroupByDataIndex
                  )}
                  dataSource={this.getList(item.maintenanceItemList)}
                  loading={checkItemSaveValueLoading}
                  pagination={false}
                  align="left"
                />
              </div>
            ))
          }
        </RenderAuthorizedTable>
        <ExceptionModal
          basicInfo={basicInfo}
          exceptionInfo={{
            ...exceptionInfo,
            roomType,
            checkSubjectTag: exceptionInfo.deviceTag,
            deviceTypeName: nomalizedDeviceCategory[exceptionInfo.deviceType]?.metaName,
            itemName: `${exceptionInfo.itemName}-${exceptionInfo.maintenanceStd}`,
          }}
          visible={exceptionVisible}
          handling={[
            { value: CHECK_SUBJECT_KEY_MAPS.EVENT, label: '事件工单' },
            { value: CHECK_SUBJECT_KEY_MAPS.REPAIR, label: '维修工单' },
          ]}
          taskType="MAINTENANCE"
          checkType="DEVICE"
          onConfirm={this.onConfirm}
          onExceptionVisible={this.onExceptionVisible}
        />
      </Space>
    );
  }
}

const mapStateToProps = ({
  ticket: {
    maintenance: { detail },
  },
  common: { deviceCategory, roomTypes },
}) => ({
  checkItem: detail.checkItem,
  checkItemTypes: detail.checkItemTypes,
  loading: detail.checkItemLoading,
  checkedCheckItemTypes: detail.checkedCheckItemTypes,
  checkItemSaveValueLoading: detail.checkItemSaveValueLoading,
  nomalizedDeviceCategory: deviceCategory ? deviceCategory.normalizedList : {},
  rowSpansGroupByDataIndex: detail.rowSpansGroupByDataIndex,
  roomTypes: roomTypes || {},
  roomInfo: detail.roomInfo,
});

const mapDispatchToProps = {
  ticketMaintenanceCheckItemActionCreator: ticketMaintenanceCheckItemActionCreator,
  setCheckItemTypes: ticketActions.setMaintenanceCheckItemTypes,
  setMaintenanceCheckItem: ticketActions.setMaintenanceCheckItem,
  setMaintenanceCheckItemSaveValueLoading: ticketActions.setMaintenanceCheckItemSaveValueLoading,
  syncCommonData: syncCommonDataActionCreator,
  setMaintenanceCheckedTypes: ticketActions.setMaintenanceCheckedTypes,
};
export default connect(mapStateToProps, mapDispatchToProps)(MaintenanceDetail);

function RenderAuthorizedTable({ userId, children }) {
  const [, { checkUserId }] = useAuthorized();
  const isCurrentUser = checkUserId(userId);
  if (typeof children == 'function') {
    return children(isCurrentUser);
  }
  return isCurrentUser ? children : <Redirect to={{ pathname: '/403' }} />;
}

function OneKeyMaintenanceButton({ handlerId, taskNo, roomGuid, onSuccess }) {
  const [, { checkUserId }] = useAuthorized();
  const [batchMaintenanceCheckItems] = useBatchMaintenanceCheckItems({
    onCompleted(data) {
      setLoading(false);
      if (!data || !data.batchMaintenanceCheckItems?.success) {
        message.error(data.batchMaintenanceCheckItems?.message);
        return;
      } else {
        message.success('一键维护成功');
        onSuccess && onSuccess();
      }
    },
  });
  const isCurrentUserHandler = checkUserId(handlerId);
  const [loading, setLoading] = useState(false);
  return isCurrentUserHandler ? (
    <Button
      type="link"
      compact
      loading={loading}
      onClick={() => {
        setLoading(true);
        batchMaintenanceCheckItems({
          variables: {
            taskNo,
            roomGuid,
          },
        });
      }}
    >
      一键维护
    </Button>
  ) : null;
}

function isOnlyValueMethodInCheckItems(checkItems) {
  if (Array.isArray(checkItems) && checkItems.length > 0) {
    const allMaintenanceItems = checkItems.reduce((pre, cur) => {
      return [...pre, ...cur.maintenanceItemList];
    }, []);
    return (
      allMaintenanceItems?.filter(item => {
        return [RECORD_METHOD_KEY_MAP.RUNING, RECORD_METHOD_KEY_MAP.OPERATION].includes(
          item.recordMethod
        );
      }).length === 0
    );
  }

  return;
}
