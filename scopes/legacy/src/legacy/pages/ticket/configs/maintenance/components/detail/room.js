import uniqBy from 'lodash/uniqBy';
import React from 'react';
import { connect } from 'react-redux';

import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Input } from '@manyun/base-ui.ui.input';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';

import { DisplayCard, GutterWrapper } from '@manyun/dc-brain.legacy.components';
import { MAINTENANCE_DETAIL_RECENTLY_CLICK_ROOM } from '@manyun/dc-brain.legacy.constants/ticket';
import {
  ticketActions,
  ticketMaintenanceRoomInfoActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/ticketActions';
import {
  getDisplayCardColor,
  setRecentlyClickCardCode,
} from '@manyun/dc-brain.legacy.utils/ticket';

import { formatTime } from '../../../util.ts';
import Enclosure from './enclosure';

export class PatorlDetail extends React.Component {
  state = {
    primaryKey: '',
  };

  componentDidMount() {
    this.props.ticketMaintenanceRoomInfoActionCreator({
      params: { taskNo: this.props.taskNo },
      successCallback: this.props.setProviderValue,
    });
  }

  handleChangeCurrentContent = (roomGuid, roomType) => {
    setRecentlyClickCardCode(roomGuid, this.props.taskNo, MAINTENANCE_DETAIL_RECENTLY_CLICK_ROOM);
    this.props.changeCurrentContent('checkItem', roomGuid);
    this.props.setRoomType(roomType);
  };

  handleChangeCheckableTag = values => {
    this.props.setMaintenanceRoomTypes(values);
  };

  getList = () => {
    const { roomInfo, checkedRoomTypes } = this.props;
    const { primaryKey } = this.state;
    let list = [...roomInfo];
    if (checkedRoomTypes.length) {
      list = list.filter(room => checkedRoomTypes.includes(room.roomType));
    } else {
      list = [];
    }
    if (primaryKey) {
      list = list.filter(room => room.roomTag.toLowerCase().includes(primaryKey.toLowerCase()));
    }
    list = list.sort((a, b) => {
      const sortA = a.sort === null ? Infinity : a.sort; // null 视为最大值
      const sortB = b.sort === null ? Infinity : b.sort;
      return sortA - sortB;
    });
    return list;
  };

  render() {
    const { roomTypes, loading, checkedRoomTypes, roomInfo, basicInfo } = this.props;
    const typeFilterList = uniqBy(
      roomInfo.map(item => {
        return { value: item.roomType, label: roomTypes[item.roomType] };
      }),
      'value'
    );
    const showList = this.getList();

    if (loading) {
      return (
        <GutterWrapper flex justifyContent="center">
          <Spin />
        </GutterWrapper>
      );
    }

    return (
      <Space style={{ width: '100%' }} direction="vertical" size="middle">
        <Enclosure label="维护报告:" basicInfo={basicInfo} />
        <Space>
          <Checkbox.Group
            options={typeFilterList}
            value={checkedRoomTypes}
            onChange={this.handleChangeCheckableTag}
          />
          <Input.Search
            style={{ width: 200 }}
            onSearch={value => this.setState({ primaryKey: value })}
          />
        </Space>
        <div
          style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fill,minmax(244px,1fr))',
            gridRowGap: '58px',
            gridColumnGap: '16px',
          }}
        >
          {showList.map(room => (
            <DisplayCard
              key={room.roomTag}
              title={<span>{`${room.roomTag}\n${roomTypes[room.roomType]}`}</span>}
              describe={formatTime(room.maintenanceTime)}
              subDescribe="维护时长"
              backgroundColor={getDisplayCardColor({
                exceptionNum: room.exceptionNum,
                waitNum: room.waitMaintenanceNum,
                alreadyNum: room.alreadyMaintenanceNum,
                code: room.roomGuid,
                taskNo: this.props.taskNo,
                recentlyCode: MAINTENANCE_DETAIL_RECENTLY_CLICK_ROOM,
              })}
              countList={[
                { describe: '异常数', number: room.exceptionNum, color: 'error' },
                {
                  describe: '待维护',
                  number: room.waitMaintenanceNum,
                  color: 'warning',
                },
                {
                  describe: '已维护',
                  number: room.alreadyMaintenanceNum,
                  color: 'success',
                },
              ]}
              onClick={() => this.handleChangeCurrentContent(room.roomGuid, room.roomType)}
            />
          ))}
        </div>
      </Space>
    );
  }
}

const mapStateToProps = ({
  ticket: {
    maintenance: { detail },
    ticketView: { basicInfo },
  },
  common: { roomTypes },
}) => ({
  basicInfo,
  roomInfo: detail.roomInfo,
  checkedRoomTypes: detail.roomTypes,
  roomTypes: roomTypes || {},
  loading: detail.roomLoading,
});

const mapDispatchToProps = {
  ticketMaintenanceRoomInfoActionCreator: ticketMaintenanceRoomInfoActionCreator,
  setMaintenanceRoomTypes: ticketActions.setMaintenanceRoomTypes,
};
export default connect(mapStateToProps, mapDispatchToProps)(PatorlDetail);
