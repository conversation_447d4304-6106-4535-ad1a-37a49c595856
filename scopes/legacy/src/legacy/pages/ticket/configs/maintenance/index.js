import { BackendTaskStatus } from '@manyun/ticket.model.ticket';
import { TicketStatusSelect } from '@manyun/ticket.ui.ticket-status-select';

import { CURRENT_PAGE_TYPES } from '../../constants';
import TTR from '../../registries/ticket-type-registry';
import Detail from './components/detail';
import NewTicket from './components/new-ticket';

TTR.registerTicketType('maintenance')
  .registerConfig({
    type: CURRENT_PAGE_TYPES.TICKETS,
    showNewBtn: true,
    mergeFilters: ({ DEFAULT_FILTER_KEY_MAP, defaultFilters }) => {
      const newFilters = [...defaultFilters].filter(
        ({ key }) => key !== DEFAULT_FILTER_KEY_MAP.END_TIME
      );
      newFilters.splice(
        newFilters.findIndex(item => item.key === 'taskStatusList'),
        1,
        {
          label: '工单状态',
          key: 'taskStatusList',
          initialProps: {
            optionFilter: option =>
              ![BackendTaskStatus.INIT, BackendTaskStatus.UNDO].includes(option.value),
            allowClear: true,
            mode: 'multiple',
          },
          Comp: TicketStatusSelect,
        }
      );
      return newFilters;
    },
  })
  .registerConfig({
    type: CURRENT_PAGE_TYPES.NEW_TICKET,
    content: NewTicket,
  })
  .registerConfig({
    type: CURRENT_PAGE_TYPES.SPECIFIC_TICKET,
    content: Detail,
    showFailureBtn: false,
    showRevocationBtn: true,
    // canClose: (basicInfo, a) => {
    //   console.log('aadsadad', basicInfo, a);
    //   return new Promise(resolve=>{

    //   })
    // },
  });
