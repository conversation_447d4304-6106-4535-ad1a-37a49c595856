import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import moment from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Spin } from '@manyun/base-ui.ui.spin';

import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { McUpload } from '@manyun/dc-brain.ui.upload';
import { BackendTaskStatus } from '@manyun/ticket.model.ticket';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';
import { ticketActions } from '@manyun/dc-brain.legacy.redux/actions/ticketActions';
import { deleteFile, saveFiles } from '@manyun/dc-brain.legacy.services/dcomService';

/**
 *  工单详情的附件 - 上传、回显、删除
 */

export default function Enclosure({
  label = '维护报告：',
  basicInfo: { taskStatus, taskType, taskNo, taskAssignee, fileList, fileLoading },
}) {
  const { userId } = useSelector(state => state.user);
  const [files, setFiles] = useState([]);

  useEffect(() => {
    setFiles(
      (fileList || [])
        .filter(file => file.type === taskType)
        .map(file => ({ ...file, status: 'done' }))
    );
  }, [fileList, taskType]);

  const dispatch = useDispatch();

  const onSaveFiles = async file => {
    const fileUrl = file.response[0].patialPath;
    const fileInfo = {
      targetId: taskNo,
      targetType: taskType,
      fileFormat: file.ext,
      fileName: file.name,
      filePath: fileUrl,
      fileSize: file.size,
      fileTime: file.lastModified,
      fileType: taskType,
      uploadBy: file.uploadUser.id,
      uploadByName: file.uploadUser.name,
      uploadTime: moment(file.uploadedAt).toISOString(true),
    };
    const { error, response } = await saveFiles({
      fileInfos: [fileInfo],
    });
    if (error) {
      message.error(error);
      return;
    }
    if (response) {
      message.success('上传成功！');
    }
    const mcuploadFile = McUploadFile.fromApiObject(fileInfo);
    const files = [
      ...fileList,
      { ...mcuploadFile, uid: file.uid, url: mcuploadFile.src, src: mcuploadFile.src },
    ];
    dispatch(ticketActions.setTicketFiles(files));
  };

  const onRemoveFiles = async file => {
    const { error, response } = await deleteFile({
      targetId: taskNo,
      targetType: taskType,
      fileType: taskType,
      filePath: file.patialPath,
    });
    if (error) {
      message.error(error);
      return;
    }
    if (response) {
      message.success('删除成功！');
    }
    const files = fileList.filter(fileItem => fileItem.uid !== file.uid);
    dispatch(ticketActions.setTicketFiles(files));
  };

  /*** 除 处理中、已关单、失败 状态 不返回附件信息*/
  if (
    ![BackendTaskStatus.PROCESSING, BackendTaskStatus.FAILURE, BackendTaskStatus.FINISH].includes(
      taskStatus
    )
  ) {
    return null;
  }

  const disabledUpload = [BackendTaskStatus.FAILURE, BackendTaskStatus.FINISH].includes(taskStatus)
    ? true
    : userId !== taskAssignee;

  if (fileLoading) {
    return <Spin spinning={fileLoading} />;
  }
  return (
    <GutterWrapper mode="horizontal" style={{ width: '50%' }}>
      <span>{label}</span>
      <McUpload
        disabled={disabledUpload}
        allowDelete
        accept=".jpg,.png,.doc,.docx,.pdf,.xls,.xlsx"
        fileList={files}
        onChange={info => {
          setFiles(info.fileList.filter(file => file.status && file.status !== 'error'));
          if (info.file.status === 'done') {
            onSaveFiles(info.file);
          }
        }}
        onRemove={file => {
          onRemoveFiles(file);
        }}
      >
        {disabledUpload &&
          (fileList || []).filter(file => file.type === taskType).length === 0 &&
          '--'}
        {!disabledUpload && [BackendTaskStatus.PROCESSING].includes(taskStatus) && (
          <Button type="primary">上传</Button>
        )}
      </McUpload>
    </GutterWrapper>
  );
}
