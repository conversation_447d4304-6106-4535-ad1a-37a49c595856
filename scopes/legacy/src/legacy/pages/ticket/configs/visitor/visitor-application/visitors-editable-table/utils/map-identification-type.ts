import type { CertificateType } from '@manyun/base-ui.ui.certificate-type-select';

export function mapIdentificationType(typeText: string): CertificateType | null {
  switch (typeText) {
    case '身份证':
    case 'ID Card':
      return 'ID_CARD';
    case '护照':
    case 'Passport':
      return 'PASSPORT';
    case '港澳台胞证':
    case 'Hong Kong & Macao & Taiwan Compatriot Identity Card':
      return 'PERMIT';
    case '其他':
    case 'Other':
      return 'OTHER';
    default:
      return null;
  }
}
