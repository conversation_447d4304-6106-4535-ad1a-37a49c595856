import type { VisitorsTicketMutatorLocales } from './type.js';

export const enUS: VisitorsTicketMutatorLocales = {
  visitorRequest: 'Visitor Application',
  basicTitle: 'General Information',
  visitType: 'Type',
  relatedTicketType: 'Related Order Types',
  relatedTicketNumber: 'Related Order Number',
  title: 'Order Title',
  purpose: 'Reason for Entry',
  allowedTimeRange: 'Authorization Time',
  allowedStartTime: 'Authorization start time',
  allowedEndTime: 'Authorization end time',
  startGTCurrent: 'The authorization needs to be in a more reasonable time frame',
  endGtStart: 'End time must be greater than the start time',
  limitDays: 'Start time and end time cannot exceed {{day}} days',
  allowedTimeRangeLimitDates: 'Authorisation time cannot be requested for more than 14 days',
  protectionDateTip:
    'Includes a high-security day. Please confirm with the relevant parties in advance',
  idc: 'Visit Data Center',
  authorizedArea: 'Authorized Area',
  receptionist: 'Receptionist',
  authorizedNotify: {
    title: 'Authorization Succeeds Notification',
    tooltip:
      'When application process is completed, you will receive a notification email. Please note.',
    enum: {
      email: 'Email Notification',
      disabled: 'No Need for Notification',
    },
  },
  insideVisitorNotices: 'Internal staff',
  outSideVisitorNotices: 'External staff',
  email: 'Email ',
  emailPlaceholder: 'Please enter your email address',
  emailFormatError: 'Please enter the correct email address!',
  location: 'Location',
  fileInfoList: {
    label: 'Attachment',
    tooltip: 'Supported File Extensions',
    upload: 'Upload',
  },
  userInfos: {
    title: 'Personnel Information',
    infoTitle: '{{userName}} apply for a visit to {{idc}}(data center) as {{visitType}}',
  },
  message: {
    addUserEmptyError: 'Please add at least one person!',
    userCheckError: 'Please delete the personnel with existing authorization records!',
    noticeUserMustRequiredError: 'Either internal personnel or external personnel must be filled',
    success: 'Submitted Successfully',
    timeFirst: 'Set the authorization time first',
    idcFirst: 'Please select the visiting machine room first',
  },
  button: {
    submit: 'Submit',
    cancel: 'Cancel',
    addEmail: 'Add Email',
    addUser: 'Add Person',
    editUser: 'Edit Person',
    edit: 'Edit',
    delete: 'Delete',
    reset: 'Reset',
    confirm: 'Confirm',
    checkAll: 'Check All',
  },
  input: {
    maxLen: 'Maximum input of {{max}} characters!',
    required: ' is required!',
  },
  select: {
    required: ' is required!',
  },
};
export default enUS;
