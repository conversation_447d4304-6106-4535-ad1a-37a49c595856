import { CheckOutlined, CloseOutlined, DeleteOutlined, LoadingOutlined } from '@ant-design/icons';
import { Empty as AntdEmpty } from 'antd';
import { useCallback, useEffect, useRef, useState } from 'react';
import styled from 'styled-components';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Empty } from '@manyun/base-ui.ui.empty';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { UserAvatar } from '@manyun/iam.ui.user-avatar';
import { useSpace } from '@manyun/resource-hub.gql.client.resources';
import {
  useUpdateApproveAreaMutation,
  useUpdateAssigneeConfigMutation,
  useUpdateOrderAssigneeMutation,
} from '@manyun/ticket.gql.client.tickets';
import { addWorkOrderAssignee } from '@manyun/ticket.service.add-work-order-assignee';
import { deleteWorkOrderAssignee } from '@manyun/ticket.service.delete-work-order-assignee';
import type { ApiResponseData as cardListData } from '@manyun/ticket.service.fetch-list-of-online-offline-orders';

const StyledGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(244px, 1fr));
  gap: 16px;
`;
const StyledCard = styled(Card)`
  /* max-width: 244px; */
  padding: 10px;
  margin: 0;
  &:hover {
    box-shadow: 0 4px 14px rgba(0, 0, 0, 0.1);
  }

  .manyun-card-body {
    padding: 0;
    cursor: pointer;
  }

  .card-title {
    padding-bottom: 14px;
    display: flex;
    justify-content: space-between;
    flex-flow: nowrap;
  }
  .manyun-typography {
    margin-bottom: 0;
  }
`;

type StateType = {
  key: string;
  cardTitle: string;
  roomType: string;
  cardList: cardListData[];
  _roomGuid: string;
  roomGuid: string | undefined;
  loading: boolean;
};
export function SpaceTag({ guid }: { guid: string }) {
  const spaces = useSpace(guid);
  const spaceNames = spaces.map(space => space?.label).filter(Boolean);

  if (spaceNames.length === 0) {
    return <>{guid}</>;
  }

  return spaceNames[spaceNames.length - 1];
}

function transformData(v: any) {
  // v--->y
  // const v = [
  //   {
  //     blockGuid: 'sxdtyg.1',
  //     resourceNo: 'sxdtyg.1.模块机房M201',
  //     resourceType: 'ROOM',
  //     roomCategory: 'IT',
  //     roomType: 'IT_ROOM',
  //   },
  //   {
  //     blockGuid: 'sxdtyg.1',
  //     resourceNo: 'sxdtyg.1.模块机房M202',
  //     resourceType: 'ROOM',
  //     roomCategory: 'IT',
  //     roomType: 'IT_ROOM',
  //   },
  //   {
  //     blockGuid: 'sxdtyg.1',
  //     resourceNo: 'sxdtyg.1.ALL',
  //     resourceType: 'ROOM',
  //     roomCategory: 'FM',
  //     roomType: null,
  //   },
  //   {
  //     blockGuid: 'sxdtyg.1',
  //     resourceNo: 'sxdtyg.1.ALL',
  //     resourceType: 'ROOM',
  //     roomCategory: 'Office',
  //     roomType: null,
  //   },
  //   {
  //     blockGuid: 'sxdtyg.PZ',
  //     resourceNo: 'sxdtyg.PZ',
  //     resourceType: 'BLOCK',
  //     roomCategory: null,
  //     roomType: null,
  //   },
  // ];
  // const y = [
  //   {
  //     blockGuid: 'sxdtyg.1',
  //     roomCategoryList: ['IT', 'FM', 'Office'],
  //     approveAreaList: [
  //       {
  //         blockGuid: 'sxdtyg.1',
  //         resourceNo: 'sxdtyg.1.模块机房M201',
  //         resourceType: 'ROOM',
  //         roomCategory: 'IT',
  //         roomType: 'IT_ROOM',
  //       },
  //       {
  //         blockGuid: 'sxdtyg.1',
  //         resourceNo: 'sxdtyg.1.模块机房M202',
  //         resourceType: 'ROOM',
  //         roomCategory: 'IT',
  //         roomType: 'IT_ROOM',
  //       },
  //       {
  //         blockGuid: 'sxdtyg.1',
  //         resourceNo: 'sxdtyg.1.ALL',
  //         resourceType: 'ROOM',
  //         roomCategory: 'FM',
  //         roomType: null,
  //       },
  //       {
  //         blockGuid: 'sxdtyg.1',
  //         resourceNo: 'sxdtyg.1.ALL',
  //         resourceType: 'ROOM',
  //         roomCategory: 'Office',
  //         roomType: null,
  //       },
  //     ],
  //   },
  //   {
  //     blockGuid: 'sxdtyg.PZ',
  //     roomCategoryList: [],
  //     approveAreaList: [
  //       {
  //         blockGuid: 'sxdtyg.PZ',
  //         resourceNo: 'sxdtyg.PZ',
  //         resourceType: 'BLOCK',
  //         roomCategory: null,
  //         roomType: null,
  //       },
  //     ],
  //   },
  // ];
  return Object.values(
    v.reduce((acc, item) => {
      const { blockGuid, roomCategory } = item;

      // 如果当前 blockGuid 不存在，则创建新对象
      if (!acc[blockGuid]) {
        acc[blockGuid] = {
          blockGuid,
          roomCategoryList: [],
          approveAreaList: [],
        };
      }

      // 将 roomCategory 添加到 roomCategoryList 中（去重）
      if (roomCategory && !acc[blockGuid].roomCategoryList.includes(roomCategory)) {
        acc[blockGuid].roomCategoryList.push(roomCategory);
      }

      // 将当前 item 添加到 approveAreaList 中
      acc[blockGuid].approveAreaList.push(item);

      return acc;
    }, {})
  );
}
function groupRoomsByCategory(dataArray: any, equal = true) {
  if (!Array.isArray(dataArray)) {
    return;
  }
  const result: any[] = [];

  dataArray.forEach(area => {
    if (equal ? area.resourceType === 'ROOM_CATEGORY' : area.resourceType !== 'ROOM_CATEGORY') {
      const category = result.find(item => item.roomCategory === area.roomCategory);

      if (category) {
        category.roomGuids.push(area.resourceNo);
      } else {
        // 如果不存在，创建新的 category
        result.push({
          roomCategory: area.roomCategory,
          roomGuids: [area.resourceNo],
        });
      }
    }
  });

  return result;
}

type PropsType = {
  mode?: 'vetting' | 'visitorSlip';
  taskNo: string;
  idcTag: string;
  visitType: string;
  bizTag?: string | undefined;
  showAddAssignee?: boolean;
  showDeleteAssignee?: boolean;
};
// 定义需要过滤的业务类型
const initDisableVisitTypes: string[] = [
  'dcim',
  'physical_security',
  'property',
  'business_visitor',
  'government_visitor',
  'other',
];
export function RegionalCardList({
  mode = 'visitorSlip',
  taskNo,
  idcTag,
  visitType,
  bizTag,
  showAddAssignee = false,
  showDeleteAssignee = false,
}: PropsType) {
  const [configUtil] = useConfigUtil();
  const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket');
  const {
    dockingStation,
  } = // @ts-ignore
    ticketScopeCommonConfigs?.entryOfPersons?.features ?? {
      dockingStation: false,
    };
  const [getApproveArea, { loading: approveAreaLoading }] = useUpdateApproveAreaMutation();
  const [getOrderAssignee, { loading: orderAssigneeLoading }] = useUpdateOrderAssigneeMutation();
  const [getAssigneeConfig, { loading: assigneeConfigLoading }] = useUpdateAssigneeConfigMutation();

  const [modalInfo, setModalInfo] = useState({
    open: false,
    blockGuid: '',
    assigneeList: [],
    roomCategoryList: [],
    approveAreaList: [],
    roomCategoryGuidList: [],
    allRoomGuids: [],
    assigneeType: undefined,
    selectOptions: [],
  });

  const [approveArea, setApproveArea] = useState();

  const [editAssignee, setEditAssignee] = useState<{
    switchMode: 'btn' | 'select';
    values:
      | {
          id: number;
          userName: string;
        }[]
      | [];
    loading: boolean;
  }>({
    switchMode: 'btn',
    values: [],
    loading: false,
  });

  const getSelectOptions = useCallback(
    async ({
      blockGuid,
      orderAssignees,
    }: {
      blockGuid: string;
      orderAssignees: { id: number; userName: string }[];
    }) => {
      const {
        data: { updateChangeAssigneeConfig },
      } = await getAssigneeConfig({
        variables: {
          query: {
            blockGuid: blockGuid,
            customerNo: 'SYSTEM',
          },
        },
      });
      const { data } = updateChangeAssigneeConfig;
      if (data) {
        const _assigneeList: { id: number; userName: string; userType: number }[] =
          data.map((item: any) => item.assigneeList).flat() || [];
        const _options =
          _assigneeList
            .filter(
              assignee =>
                !orderAssignees.some(i => i.id === assignee.id && i.userName === assignee.userName)
            )
            .map(assignee => {
              return {
                value: assignee.id,
                label: assignee.userName,
                disabled: false,
              };
            }) || [];
        // @ts-ignore
        setModalInfo(preState => ({
          ...preState,
          assigneeList: orderAssignees,
          selectOptions: _options,
          /** item.assigneeType === 0 是自动确认  只保留 assigneeList(展示有对接人) */
          assigneeType: data[0]?.assigneeType,
        }));
      }
    },
    [modalInfo]
  );
  const updateOrderAssignee = async ({ blockGuid }: { blockGuid: string }) => {
    const {
      data: { updateChangeOrderAssignee },
    } = await getOrderAssignee({
      variables: {
        query: {
          taskNo,
          blockGuid,
        },
      },
    });
    const { data: orderAssignee } = updateChangeOrderAssignee;
    if (orderAssignee && orderAssignee.length >= 1) {
      getSelectOptions({ blockGuid, orderAssignees: orderAssignee[0].assigneeList });
      setApproveArea(preState =>
        preState?.map(item =>
          item.blockGuid === blockGuid
            ? { ...item, assigneeList: orderAssignee[0].assigneeList }
            : item
        )
      );
    }
  };

  const onDeleteAssignee = useCallback(
    async ({
      blockGuid,
      assigneeId,
      assigneeName,
    }: {
      blockGuid: string;
      assigneeId: number;
      assigneeName: string;
    }) => {
      // @ts-ignore
      setModalInfo(preState => ({
        ...preState,
        assigneeList: preState.assigneeList?.map((item: { id: number; userName: string }) =>
          item.id === assigneeId && item.userName === assigneeName
            ? { ...item, loading: true }
            : item
        ),
      }));
      const { data, error } = await deleteWorkOrderAssignee({
        taskNo,
        blockGuid,
        assigneeId,
        assigneeName,
      });

      if (data) {
        message.success('删除成功');
        updateOrderAssignee({ blockGuid });
        return;
      }
      if (error) {
        message.error(error.message);
      }
      // @ts-ignore
      setModalInfo(preState => ({
        ...preState,
        assigneeList: preState.assigneeList?.map((item: { id: number; userName: string }) =>
          item.id === assigneeId && item.userName === assigneeName
            ? { ...item, loading: false }
            : item
        ),
      }));
    },
    [taskNo, modalInfo]
  );
  const onAddAssignee = useCallback(
    async ({
      blockGuid,
      assigneeList,
    }: {
      blockGuid: string;
      assigneeList: { id: number; userName: string }[];
    }) => {
      setEditAssignee(preState => ({ ...preState, loading: true }));
      const { data, error } = await addWorkOrderAssignee({ taskNo, blockGuid, assigneeList });
      if (data) {
        message.success('添加成功');
        setEditAssignee({ switchMode: 'btn', values: [], loading: false });
        updateOrderAssignee({ blockGuid });
        return;
      }
      if (error) {
        message.error(error.message);
      }
      setEditAssignee(preState => ({ ...preState, loading: false }));
    },
    [taskNo, modalInfo]
  );

  useEffect(() => {
    (async () => {
      const {
        data: { updateChangeApproveArea },
      } = await getApproveArea({
        variables: {
          query: {
            taskNo,
          },
        },
        // onCompleted(data) {},
      });

      const {
        data: { updateChangeOrderAssignee },
      } = await getOrderAssignee({
        variables: {
          query: {
            taskNo,
          },
        },
        // onCompleted(data) {},
      });

      const { data: approveAreaData } = updateChangeApproveArea;
      const { data: orderAssignee } = updateChangeOrderAssignee;
      // 过滤数据：当 visitType 在 initDisableVisitTypes 中，并且approveAreaData里有 blockType === 'IDC_BLOCK' 时，
      // 过滤掉中，并且approveAreaData里面 resourceNo 末尾是 'PZ' 或 'Z' 的项
      let filteredApproveAreaData = [...approveAreaData];

      if (initDisableVisitTypes.includes(visitType)) {
        // 存在机房楼
        const hasIdcBlock = approveAreaData.some((i: any) => i.blockType === 'IDC_BLOCK');
        // 存在变电站且不存在机房楼
        const hasSubstation =
          approveAreaData.some((i: any) => i.blockType === 'POWER_STATION') && !hasIdcBlock;
        // 是否为 政府 商务 特殊类型 且有机房楼
        const isSpecialtyVisitType = hasIdcBlock && bizTag;

        filteredApproveAreaData = approveAreaData.filter((item: any) => {
          // 检查 blockGuid 的末尾是否是 'PZ' 或 'Z'
          const segments = item.blockGuid.split('.');
          const lastSegment = segments[segments.length - 1];
          return isSpecialtyVisitType || hasSubstation
            ? !(lastSegment === 'PZ')
            : hasIdcBlock
              ? !(lastSegment === 'PZ')
              : // ? !(lastSegment === 'PZ' || lastSegment === 'Z')
                true;
        });
      }

      const _approveAreaData = transformData(filteredApproveAreaData)?.map(areaIndo => {
        const assignee = orderAssignee.find(i => i.blockGuid === areaIndo.blockGuid);
        return {
          ...areaIndo,
          assigneeList: assignee?.assigneeList ?? [],
          roomCategoryGuidList: areaIndo.approveAreaList
            .filter(area => area.resourceType === 'ROOM_CATEGORY')
            .map(area => area.resourceNo),
          allRoomGuids: groupRoomsByCategory(areaIndo.approveAreaList, false),
          assigneeType: assignee?.assigneeType,
        };
      });
      setApproveArea(_approveAreaData);
    })();
  }, [taskNo, idcTag]);

  useEffect(() => {
    if (modalInfo.blockGuid && modalInfo.open) {
      getSelectOptions({
        blockGuid: modalInfo.blockGuid,
        orderAssignees: modalInfo.assigneeList,
      });
    }
  }, [modalInfo.blockGuid, modalInfo.open]);

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <StyledGrid>
        {approveArea?.map(areaInfo => {
          const {
            blockGuid,
            roomCategoryList,
            assigneeList = [],
            approveAreaList,
            roomCategoryGuidList = [],
            allRoomGuids = [],
            assigneeType,
          } = areaInfo;
          return (
            <StyledCard
              key={blockGuid}
              onClick={() => {
                if (mode === 'vetting' && !dockingStation) {
                  return;
                }
                setModalInfo({
                  open: true,
                  blockGuid,
                  assigneeList,
                  roomCategoryList,
                  approveAreaList,
                  roomCategoryGuidList,
                  allRoomGuids,
                  assigneeType,
                  selectOptions: [],
                });
              }}
            >
              <div className="card-title">
                <Typography.Text
                  style={{ fontSize: '16px', paddingRight: '6px', whiteSpace: 'nowrap' }}
                >
                  <SpaceTag guid={blockGuid} />
                </Typography.Text>

                {assigneeList?.length >= 1 && (
                  <Typography.Text
                    ellipsis={{
                      tooltip: (
                        <>
                          {assigneeList?.map(
                            ({ userName }: { userName: string }, index: number) => (
                              <a key={userName}>
                                {userName}
                                {assigneeList.length > index + 1 && <Divider type="vertical" />}
                              </a>
                            )
                          )}
                        </>
                      ),
                    }}
                  >
                    <Typography.Text style={{ whiteSpace: 'nowrap' }}>对接人 :</Typography.Text>

                    {assigneeList?.map(
                      ({ userName, id }: { userName: string; id: number }, index: number) => (
                        <a>
                          {userName}
                          {assigneeList.length > index + 1 && <Divider type="vertical" />}
                        </a>
                      )
                    )}
                  </Typography.Text>
                )}
              </div>
              {roomCategoryList?.length >= 1 && (
                <Space>
                  {roomCategoryList.map((roomCategory: string) => (
                    <Typography.Text key={roomCategory}>{roomCategory} </Typography.Text>
                  ))}
                </Space>
              )}
            </StyledCard>
          );
        })}
      </StyledGrid>
      <Modal
        title={<SpaceTag guid={modalInfo.blockGuid} />}
        open={modalInfo.open}
        onCancel={() => {
          setModalInfo({
            open: false,
            blockGuid: '',
            assigneeList: [],
            approveAreaList: [],
            roomCategoryList: [],
            roomCategoryGuidList: [],
            allRoomGuids: [],
            selectOptions: [],
            assigneeType: undefined,
          });
          setEditAssignee({ switchMode: 'btn', values: [], loading: false });
        }}
        okText={false}
        cancelText={false}
        footer={null}
      >
        <Space direction="vertical" size="large" style={{ width: '100%' }} wrap>
          <Space style={{ width: '100%' }} align="start">
            <div style={{ width: '54px' }}>
              <Typography.Text> 对接人 : </Typography.Text>
            </div>
            <Space style={{ width: '100%' }} wrap>
              {modalInfo.assigneeList?.length >= 1 && !!modalInfo.assigneeType ? (
                modalInfo.assigneeList?.map(
                  ({
                    userName,
                    id,
                    loading,
                  }: {
                    userName: string;
                    id: number;
                    loading: boolean;
                  }) => (
                    <div
                      key={id}
                      style={{ display: 'flex', paddingRight: '6px', alignItems: 'center' }}
                    >
                      <UserAvatar
                        avatarProps={{
                          style: {
                            width: '22px',
                            height: '22px',
                            lineHeight: '22px',
                            fontSize: '12px',
                          },
                        }}
                        userId={id}
                      />
                      <a style={{ padding: '0 2px' }}>{userName}</a>
                      {showDeleteAssignee &&
                        modalInfo.assigneeList?.length > 1 &&
                        !!modalInfo.assigneeType && (
                          <div style={{ position: 'relative', top: '1px' }}>
                            {loading ? (
                              <LoadingOutlined
                                style={{
                                  margin: '0 12px',
                                  color: 'var(--manyun-primary-color)',
                                  fontSize: '14px',
                                }}
                              />
                            ) : (
                              <Popconfirm
                                title="删除后无法再查看未确认登记记录"
                                onConfirm={() => {
                                  onDeleteAssignee({
                                    blockGuid: modalInfo.blockGuid,
                                    assigneeId: id,
                                    assigneeName: userName,
                                  });
                                }}
                                okText="删除"
                                cancelText="取消"
                              >
                                <DeleteOutlined style={{ color: 'rgba(0, 0, 0, 0.45)' }} />
                              </Popconfirm>
                            )}
                          </div>
                        )}
                    </div>
                  )
                )
              ) : (
                <div style={{ display: 'flex', paddingRight: '6px', alignItems: 'center' }}>
                  <Typography.Text> 系统 (自动确认) </Typography.Text>
                </div>
              )}
            </Space>
          </Space>

          {showAddAssignee && modalInfo.assigneeList?.length >= 1 && !!modalInfo.assigneeType && (
            <div style={{ marginLeft: '54px' }}>
              {editAssignee.switchMode === 'btn' && (
                <Button
                  style={{ width: '86px' }}
                  type="dashed"
                  onClick={() => {
                    setEditAssignee({ switchMode: 'select', values: [], loading: false });
                  }}
                >
                  + 添加
                </Button>
              )}
              {editAssignee.switchMode === 'select' && (
                <div style={{ width: '100%', display: 'flex' }}>
                  <Select
                    style={{ width: '86%' }}
                    showSearch
                    allowClear
                    mode="multiple"
                    // maxTagCount="responsive"
                    placeholder="对接人"
                    options={modalInfo.selectOptions}
                    filterOption={(input, option) =>
                      (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                    }
                    onChange={(
                      v: string[],
                      options: { value: number; label: string; disabled: boolean }[]
                    ) => {
                      if (!Array.isArray(options)) {
                        setEditAssignee({ ...editAssignee, values: [], loading: false });
                      }
                      setEditAssignee({
                        ...editAssignee,
                        values: options.map(i => ({
                          id: i.value,
                          userName: i.label,
                        })),
                      });
                    }}
                  />
                  {/* <UserSelect
                    filteredUsers={modalInfo.assigneeList}
                    style={{ width: '86%' }}
                    blockGuid={modalInfo.blockGuid}
                    mode="multiple"
                    onChange={v => {
                      if (!Array.isArray(v)) {
                        setEditAssignee({ ...editAssignee, values: [], loading: false });
                      }
                      setEditAssignee({
                        ...editAssignee,
                        values: v.map((i: any) => ({ id: i.id, userName: i.name })),
                      });
                    }}
                  /> */}
                  {editAssignee.loading ? (
                    <LoadingOutlined
                      style={{
                        margin: '0 12px',
                        color: 'var(--manyun-primary-color)',
                        fontSize: '16px',
                      }}
                    />
                  ) : (
                    <CheckOutlined
                      style={{
                        margin: '0 12px',
                        color: 'var(--manyun-primary-color)',
                        fontSize: '16px',
                      }}
                      onClick={() => {
                        if (editAssignee.values.length >= 1) {
                          onAddAssignee({
                            blockGuid: modalInfo.blockGuid,
                            assigneeList: editAssignee.values,
                          });
                          return;
                        }
                        message.error('请选择添加对接人');
                      }}
                    />
                  )}
                  <CloseOutlined
                    style={{ color: 'var(--manyun-primary-color)', fontSize: '16px' }}
                    onClick={() => {
                      setEditAssignee({ switchMode: 'btn', values: [], loading: false });
                    }}
                  />
                </div>
              )}
            </div>
          )}

          {modalInfo.roomCategoryList?.length >= 1 ? (
            modalInfo.roomCategoryList.map(roomCategory => {
              return (
                <StyledCard key={roomCategory} bodyStyle={{ cursor: 'default' }}>
                  <div className="card-title">
                    <Typography.Text style={{ fontSize: '16px', fontWeight: '500' }}>
                      {
                        modalInfo.approveAreaList.find(area => area.roomCategory === roomCategory)
                          ?.roomCategoryName
                      }

                      <Tag color="blue" style={{ marginLeft: '6px' }}>
                        {roomCategory}
                      </Tag>
                    </Typography.Text>
                  </div>
                  <Space style={{ width: '100%', maxHeight: '200px', overflow: 'auto' }} wrap>
                    {modalInfo.allRoomGuids.length >= 1 ? (
                      modalInfo.allRoomGuids.map(room => {
                        if (room?.roomCategory === roomCategory) {
                          return (
                            <>
                              {room.roomGuids?.map((roomGuid: string, index: number) => (
                                <Typography.Text key={roomGuid}>
                                  <SpaceTag guid={roomGuid} />
                                  {room.roomGuids.length > index + 1 && <Divider type="vertical" />}
                                </Typography.Text>
                              ))}
                            </>
                          );
                        }
                        return <Typography.Text>所有包间</Typography.Text>;
                      })
                    ) : (
                      <Typography.Text>所有包间</Typography.Text>
                    )}
                  </Space>
                </StyledCard>
              );
            })
          ) : (
            <Empty description={<div>暂无包间区域</div>} image={AntdEmpty.PRESENTED_IMAGE_SIMPLE} />
          )}
        </Space>
      </Modal>
    </Space>
  );
}
