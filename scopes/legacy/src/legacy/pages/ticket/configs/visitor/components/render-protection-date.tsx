import React from 'react';

import InfoCircleOutlined from '@ant-design/icons/es/icons/InfoCircleOutlined';

import { Tooltip } from '@manyun/base-ui.ui.tooltip';

import { useLazyExistingProtectionDate } from '@manyun/sentry.gql.client.visits';

export const RenderProtectionDate = ({
  startTime,
  endTime,
}: {
  startTime: number;
  endTime: number;
}) => {
  const [getExistingProtectionDate, { data }] = useLazyExistingProtectionDate();
  React.useEffect(() => {
    if (startTime && endTime) {
      getExistingProtectionDate({
        variables: {
          startTime,
          endTime,
        },
      });
    }
  }, [endTime, getExistingProtectionDate, startTime]);

  if ((data?.existingProtectionDate?.data ?? []).length === 0) {
    return null;
  }
  return (
    <Tooltip title={`其中${(data?.existingProtectionDate?.data ?? []).join('，')}为重保日`}>
      <InfoCircleOutlined style={{ color: 'var(--manyun-error-color)' }} />
    </Tooltip>
  );
};
