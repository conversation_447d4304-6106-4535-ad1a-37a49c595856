import type { VisitorsEditableTableLocales } from './type';

export const enUS: VisitorsEditableTableLocales = {
  userInfos: {
    index: 'Serial number',
    title: 'Personnel Information',
    visitorType: 'Personnel Type',
    visitorTypeEnum: {
      CUSTOMER: 'Customer',
      GENERAL: 'Visitor',
      MAINTAIN: 'Maintenance/Construction',
      SERVICE: 'Service Provider',
      VIP: 'VIP',
    },
    name: 'Name',
    gender: 'Gender',
    genderEnum: {
      male: 'Male',
      female: 'Female',
    },
    mobile: {
      title: 'Contact Information',
      error: 'Invalid Contact Information Format',
      duplicate: 'Duplicate contact',
    },
    identification: {
      type: 'ID Type',
      number: 'ID Number',
      numberRequire: 'ID Number is required！',
      error: 'Invalid Format！',
      duplicate: 'Duplicate ID number',
    },
    companyName: 'Company Name',
    position: 'Station',
    personalGoods: 'Personal effects',
    personalGoodsTips: {
      prefix: 'Prohibited items are not allowed',
      middle: ', please submit ',
      suffix: 'a goods in/out form',
    },
    LPN: 'License Plate Number',
    authorizedArea: 'Authorized Area',
    operationalAuthorization: {
      title: 'Operational Authorization',
      enum: { yes: 'Yes', no: 'No' },
    },
    infoTitle: '{{userName}} apply for a visit to {{idc}}(data center) as {{visitType}}',
    option: 'Operation',
  },
  alter: {
    timeRange: 'Between {{startTime}} and {{endTime}} ',
    entryNum: 'Entry Number',
    exit: 'Existing Authorization Records',
    repeatError: 'application records already exist at {{blockGuid}}',
    repeatAt: ' in ',
  },
  message: {
    importSuccess: 'Import successful！',
  },
  button: {
    addUser: 'Add Person',
    editUser: 'Edit Person',
    edit: 'Edit',
    add: 'Add',
    delete: 'Delete',
    reset: 'Reset',
    confirm: 'Confirm',
    checkAll: 'Check All',
    importPerson: 'Import Person',
    import: 'Import',
    downloadTpl: 'Download Template',
    downloadTplFileName: 'Personnel Access Template',
    deleteConfirm: 'Delete this line ?',
  },
  input: {
    maxLen: 'Maximum input of {{max}} characters!',
    required: ' is required!',
  },
  select: {
    required: ' is required!',
  },
};
export default enUS;
