import React from 'react';
import { useLatest } from 'react-use';

import { Cascader } from '@manyun/base-ui.ui.cascader';
import type { CascaderProps, CascaderRef } from '@manyun/base-ui.ui.cascader';

import { useLazyTicketTypes } from '@manyun/ticket.gql.client.tickets';
import type { QueryTicketTypesData, TicketTypeNode } from '@manyun/ticket.gql.client.tickets';

export type VisitTypesCascaderProps = CascaderProps<TicketTypeNode> & {
  trigger?: 'onFocus' | 'onDidMount';
  onOptionsChange?: (options: TicketTypeNode[]) => void;
};

export const VisitTypesCascader = React.forwardRef<CascaderRef, VisitTypesCascaderProps>(
  ({ trigger = 'onFocus', onFocus, onOptionsChange, ...props }, ref) => {
    const onOptionsChangeRef = useLatest(onOptionsChange);
    const [getTicketTypes, { loading, data }] = useLazyTicketTypes({ fetchPolicy: 'cache-first' });
    const options = pickVisitTypes(data);

    React.useEffect(() => {
      if (trigger === 'onDidMount') {
        (async () => {
          const { data } = await getTicketTypes();
          const options: TicketTypeNode[] = pickVisitTypes(data);
          onOptionsChangeRef.current?.(options);
        })();
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
      <Cascader<TicketTypeNode>
        ref={ref}
        options={options}
        loading={loading}
        {...props}
        onFocus={async evt => {
          if (trigger === 'onFocus' && options.length <= 0) {
            const { data } = await getTicketTypes();
            const options: TicketTypeNode[] = pickVisitTypes(data);
            onOptionsChangeRef.current?.(options);
          }
          onFocus?.(evt);
        }}
      />
    );
  }
);

VisitTypesCascader.displayName = 'VisitTypesCascader';

function pickVisitTypes(data: QueryTicketTypesData | undefined): TicketTypeNode[] {
  return (
    data?.ticketTypes?.find(ticketTypeNode => ticketTypeNode.value === 'VISITOR')?.children ?? []
  );
}
