import { gql } from '@apollo/client';
import type { MockedResponse } from '@apollo/client/testing/index.js';
import { MockedProvider } from '@apollo/client/testing/index.js';
import { i18n } from '@teammc/i18n';
import React from 'react';
import { initReactI18next } from 'react-i18next';
import { Link, MemoryRouter, Route } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';
import { mockTicketTypes } from '@manyun/ticket.gql.client.tickets/mocks/ticket-types.mock.js';

import { VisitsMutator } from './visits-mutator.js';

const ticketTypesMocks = mockTicketTypes();

const mocks: MockedResponse[] = [
  ...ticketTypesMocks,
  {
    request: {
      query: gql`
        query GetSpaces(
          $idc: String
          $blocks: [String!]
          $nodeTypes: [SpaceNodeType!]
          $authorizedOnly: Boolean
          $includeVirtualBlocks: Boolean
        ) {
          spaces(
            idc: $idc
            blocks: $blocks
            nodeTypes: $nodeTypes
            authorizedOnly: $authorizedOnly
            includeVirtualBlocks: $includeVirtualBlocks
          ) {
            type
            value
            parentValue
            label
            isVirtual
            children {
              type
              value
              parentValue
              label
              isVirtual
              children {
                type
                value
                parentValue
                label
                isVirtual
              }
            }
          }
        }
      `,
      variables: {
        nodeTypes: ['BLOCK', 'ROOM_TYPE', 'ROOM'],
        includeVirtualBlocks: false,
        authorizedOnly: true,
        idc: 'EC06',
      },
    },
    result: {
      data: {
        spaces: [
          {
            type: 'BLOCK',
            value: 'EC06.A',
            parentValue: 'EC06',
            label: 'A 栋',
            isVirtual: false,
            children: [
              {
                type: 'ROOM_TYPE',
                value: 'EC06.A.POWER_ROOM',
                parentValue: 'EC06.A',
                label: '强电间',
                isVirtual: null,
                children: [
                  {
                    type: 'ROOM',
                    value: 'EC06.A.1-1',
                    parentValue: 'EC06.A.POWER_ROOM',
                    label: '1-1 SXDTYG_DA_A运营商机房',
                    isVirtual: null,
                  },
                  {
                    type: 'ROOM',
                    value: 'EC06.A.A1-31',
                    parentValue: 'EC06.A.POWER_ROOM',
                    label: 'A1-31 SXDTYG_DA_强电间（一）',
                    isVirtual: null,
                  },
                ],
              },
              {
                type: 'ROOM_TYPE',
                value: 'EC06.A.40',
                parentValue: 'EC06.A',
                label: '蓄冷罐室',
                isVirtual: null,
                children: [
                  {
                    type: 'ROOM',
                    value: 'EC06.A.A1-0',
                    parentValue: 'EC06.A.40',
                    label: 'A1-0 SXDTYG_DA_室外柴发区',
                    isVirtual: null,
                  },
                  {
                    type: 'ROOM',
                    value: 'EC06.A.A1-2',
                    parentValue: 'EC06.A.40',
                    label: 'A1-2 数据机房（二）',
                    isVirtual: null,
                  },
                ],
              },
            ],
          },
        ],
      },
    },
  },
  {
    request: {
      query: gql`
        query GetSpaces(
          $idc: String
          $blocks: [String!]
          $nodeTypes: [SpaceNodeType!]
          $authorizedOnly: Boolean
          $includeVirtualBlocks: Boolean
        ) {
          spaces(
            idc: $idc
            blocks: $blocks
            nodeTypes: $nodeTypes
            authorizedOnly: $authorizedOnly
            includeVirtualBlocks: $includeVirtualBlocks
          ) {
            type
            value
            parentValue
            label
            isVirtual
            __typename
          }
        }
      `,
      variables: {
        nodeTypes: ['IDC'],
        includeVirtualBlocks: false,
        authorizedOnly: true,
      },
    },
    result: {
      data: {
        spaces: [
          {
            type: 'IDC',
            value: 'EC06',
            parentValue: null,
            label: 'EC06 普洛斯东南数据中心',
            isVirtual: false,
          },
          {
            type: 'IDC',
            value: 'EC01',
            parentValue: null,
            label: 'EC01 普洛斯哈弄弄数据中心',
            isVirtual: false,
          },
        ],
      },
    },
  },
];

export const BasicVisitsMutator = () => {
  initReactI18next.init(i18n.current);
  return (
    <MockedProvider mocks={mocks}>
      <ConfigProvider>
        <MemoryRouter>
          <Route exact path="/">
            <div style={{ margin: 24 }}>
              <p>
                <Link to="/page/tickets/visitor/new">Create a new visit ticket</Link>
              </p>
              <p>
                <Link
                  to={{
                    pathname: '/page/tickets/visitor/123/edit',
                    search: `?variables={"relateTaskType":"INSPECTION","relateTaskNo":"T123456","idcTag":"EC06"}`,
                  }}
                >
                  Update the visit ticket: 123
                </Link>
              </p>
            </div>
          </Route>
          <Route exact path="/page/tickets/visitor/new">
            <div style={{ margin: 24 }}>
              <VisitsMutator />
            </div>
          </Route>
          <Route exact path="/page/tickets/visitor/:id/edit">
            <div style={{ margin: 24 }}>
              <VisitsMutator />
            </div>
          </Route>
          <Route exact path="/page/tickets/visitor/:id">
            This is the "Visit Detail Page", click <Link to="/">here</Link> to go back.
          </Route>
        </MemoryRouter>
      </ConfigProvider>
    </MockedProvider>
  );
};
