import React from 'react';
import { initReactI18next, useTranslation } from 'react-i18next';

import { gql } from '@apollo/client';
import { MockedProvider } from '@apollo/client/testing/index.js';
import type { MockedResponse } from '@apollo/client/testing/index.js';
import { i18n } from '@teammc/i18n';

import { ConfigProvider } from '@manyun/base-ui.context.config';
import { Button } from '@manyun/base-ui.ui.button';
import { Space } from '@manyun/base-ui.ui.space';

import { LOCALE_SCOPE_NAME, LOCALE_UNIQ_KEY, enUS } from './locales/index.js';
import { VisitorsEditableTable } from './visitors-editable-table.js';
import type { Visitor } from './visitors-editable-table.js';

const mocks: MockedResponse[] = [
  {
    request: {
      query: gql`
        query GetSpaces(
          $idc: String
          $blocks: [String!]
          $nodeTypes: [SpaceNodeType!]
          $authorizedOnly: Boolean
          $includeVirtualBlocks: Boolean
          $includeNoIdcRegions: Boolean
          $roomTypes: [String!]
        ) {
          spaces(
            idc: $idc
            blocks: $blocks
            nodeTypes: $nodeTypes
            authorizedOnly: $authorizedOnly
            includeVirtualBlocks: $includeVirtualBlocks
            includeNoIdcRegions: $includeNoIdcRegions
            roomTypes: $roomTypes
          ) {
            type
            value
            parentValue
            label
            isVirtual
            custom
            children {
              type
              value
              parentValue
              label
              isVirtual
              custom
              children {
                type
                value
                parentValue
                label
                isVirtual
                custom
              }
            }
          }
        }
      `,
      variables: {
        nodeTypes: ['BLOCK', 'ROOM_TYPE', 'ROOM'],
        includeVirtualBlocks: false,
        authorizedOnly: true,
        idc: 'EC06',
      },
    },
    result: {
      data: {
        spaces: [
          {
            type: 'BLOCK',
            value: 'EC06.A',
            parentValue: 'EC06',
            label: 'A 栋',
            isVirtual: false,
            children: [
              {
                type: 'ROOM_TYPE',
                value: 'EC06.A.POWER_ROOM',
                parentValue: 'EC06.A',
                label: '强电间',
                isVirtual: null,
                children: [
                  {
                    type: 'ROOM',
                    value: 'EC06.A.1-1',
                    parentValue: 'EC06.A.POWER_ROOM',
                    label: '1-1 SXDTYG_DA_A运营商机房',
                    isVirtual: null,
                  },
                  {
                    type: 'ROOM',
                    value: 'EC06.A.A1-31',
                    parentValue: 'EC06.A.POWER_ROOM',
                    label: 'A1-31 SXDTYG_DA_强电间（一）',
                    isVirtual: null,
                  },
                ],
              },
              {
                type: 'ROOM_TYPE',
                value: 'EC06.A.40',
                parentValue: 'EC06.A',
                label: '蓄冷罐室',
                isVirtual: null,
                children: [
                  {
                    type: 'ROOM',
                    value: 'EC06.A.A1-0',
                    parentValue: 'EC06.A.40',
                    label: 'A1-0 SXDTYG_DA_室外柴发区',
                    isVirtual: null,
                  },
                  {
                    type: 'ROOM',
                    value: 'EC06.A.A1-2',
                    parentValue: 'EC06.A.40',
                    label: 'A1-2 数据机房（二）',
                    isVirtual: null,
                  },
                ],
              },
            ],
          },
        ],
      },
    },
  },
];
initReactI18next.init(i18n.current);

i18n.current.addResourceBundle('en-US', LOCALE_SCOPE_NAME, {
  [LOCALE_UNIQ_KEY]: enUS,
});
export const BasicVisitorsEditableTable = () => {
  const { i18n } = useTranslation();
  const [show, setShow] = React.useState(false);

  const [allowedTimeRange, setAllowedTimeRange] = React.useState<[number, number]>([
    1678282017000, 1678282027000,
  ]);
  const [visitors, setVisitors] = React.useState<Visitor[] | undefined>([]);
  const isValidRef = React.useRef({});

  return (
    <MockedProvider mocks={mocks} addTypename={false}>
      <ConfigProvider>
        <div style={{ margin: 24 }}>
          <Button
            onClick={() => {
              i18n.changeLanguage(i18n.language === 'zh-CN' ? 'en-US' : 'zh-CN');
            }}
          >
            当前语言： {i18n.language}
          </Button>
          <Space style={{ display: 'flex' }} direction="vertical">
            <Button
              type="primary"
              onClick={() => {
                setShow(true);
                const now = Date.now();
                setAllowedTimeRange([now - 1000, now]);
              }}
            >
              设置随机的 `allowedTimeRange`
            </Button>
            {show && (
              <VisitorsEditableTable
                idc="EC06"
                allowedTimeRange={allowedTimeRange}
                authorizedArea={['EC06.A']}
                isValidRef={isValidRef}
                value={visitors}
                contrabandItems={[]}
                onChange={setVisitors}
              />
            )}
          </Space>
        </div>
      </ConfigProvider>
    </MockedProvider>
  );
};
