import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { BackendTaskStatus as TaskStatus, TaskStatusMap } from '@manyun/ticket.model.ticket';
import type { BackendTaskStatus } from '@manyun/ticket.model.ticket';

export type Option = {
  label: string;
  value: string;
};

export type VisitorStatusSelectProps<Value extends string | string[] = string> = {
  optionFilter?: (option: Option) => boolean;
  onChange?: (value: Value, option: Value extends string ? Option : Option[]) => void;
} & Omit<SelectProps<Value, Option>, 'options' | 'onChange'>;

function _VisitorStatusSelect<Value extends string | string[] = string>(
  { optionFilter, onChange, ...restProps }: VisitorStatusSelectProps<Value>,
  ref?: React.ForwardedRef<RefSelectProps>
) {
  const [configUtil] = useConfigUtil();
  const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket');
  const { dockingStation } =
    // @ts-ignore
    ticketScopeCommonConfigs?.entryOfPersons?.features ?? {
      dockingStation: false,
    };
  let options = Object.keys(TaskStatusMap)
    .filter(key => key !== TaskStatus.PROCESSING && key !== TaskStatus.WAITTAKEOVER)
    .map(key => {
      if (key === TaskStatus.UNDO) {
        return {
          label: '草稿',
          value: key,
        };
      }
      return {
        label: TaskStatusMap[key as BackendTaskStatus],
        value: key,
      };
    });

  if (optionFilter) {
    options = options.filter(optionFilter);
  }

  return (
    <Select<Value, Option>
      {...restProps}
      ref={ref}
      options={options}
      onChange={(value, option) => {
        onChange?.(value, option as Value extends string ? Option : Option[]);
      }}
    />
  );
}

interface GenericVisitorStatusSelect {
  <Value extends string | string[] = string>(
    props: VisitorStatusSelectProps<Value>,
    ref: React.ForwardedRef<RefSelectProps>
  ): JSX.Element;
  displayName: string;
}

export const VisitorStatusSelect = React.forwardRef(
  _VisitorStatusSelect
) as GenericVisitorStatusSelect;

VisitorStatusSelect.displayName = 'VisitorStatusSelect';
