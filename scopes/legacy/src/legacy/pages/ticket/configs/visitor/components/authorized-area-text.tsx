import React from 'react';

import { useApolloClient } from '@apollo/client';

import { Divider } from '@manyun/base-ui.ui.divider';
import { Space } from '@manyun/base-ui.ui.space';

import { readSpace } from '@manyun/resource-hub.gql.client.spaces';
import { useLazyVisitAuthorizedAreas } from '@manyun/sentry.gql.client.visits';

export type AuthorizedAreaTextProps = {
  taskNo: string;
};
export const AuthorizedAreaText = ({ taskNo }: AuthorizedAreaTextProps) => {
  const [getVisitAuthorizedAreas, { data }] = useLazyVisitAuthorizedAreas();
  const client = useApolloClient();

  React.useEffect(() => {
    if (taskNo) {
      getVisitAuthorizedAreas({
        variables: {
          ticketNumber: taskNo,
        },
      });
    }
  }, [getVisitAuthorizedAreas, taskNo]);

  const labelList = React.useMemo(
    () =>
      (data?.visitAuthorizedAreas?.data ?? []).map(({ code }) => {
        return readSpace(client, code)?.label ?? code;
      }),
    [client, data?.visitAuthorizedAreas?.data]
  );

  return (
    <Space wrap split={<Divider style={{ padding: 0, margin: 0 }} type="vertical" />}>
      {labelList}
    </Space>
  );
};
