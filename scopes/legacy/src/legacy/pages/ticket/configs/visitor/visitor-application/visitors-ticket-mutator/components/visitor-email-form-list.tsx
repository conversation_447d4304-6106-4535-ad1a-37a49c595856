import React from 'react';
import { useTranslation } from 'react-i18next';

import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';

import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { Space } from '@manyun/base-ui.ui.space';

import { LOCALE_SCOPE_NAME, generateFiledKey } from '../locales/index.js';
import { formItem, formItemLayoutWithOutLabel } from '../visitors-ticket-mutator.js';

export type VisitorEmailFormListProps = {
  idc?: string;
  formLayout?: 'horizontal' | 'vertical' | 'inline';
  name: 'insideVisitorNotices' | 'outSideVisitorNotices';
  canDelete?: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  UserSelect?: React.ComponentType<any>;
};
export const VisitorEmailFormList = ({
  idc,
  UserSelect,
  formLayout,
  canDelete,
  name,
}: VisitorEmailFormListProps) => {
  const { t } = useTranslation(LOCALE_SCOPE_NAME);

  return (
    <Form.List name={name}>
      {(fields, { add, remove }) => (
        <>
          {fields.map((field, index) => (
            <Form.Item
              key={field.key}
              {...(formLayout !== 'vertical'
                ? index === 0
                  ? formItem
                  : formItemLayoutWithOutLabel
                : {})}
              label={
                index === 0
                  ? t(generateFiledKey(name), {
                      defaultValue: name === 'outSideVisitorNotices' ? '内部人员' : '外部人员',
                    })
                  : ''
              }
            >
              <Space>
                <Form.Item
                  {...field}
                  rules={
                    name === 'insideVisitorNotices'
                      ? [
                          () => ({
                            validator(_, value) {
                              if (value && value?.email) {
                                return Promise.resolve();
                              }
                              return Promise.reject('邮箱必选!');
                            },
                          }),
                        ]
                      : [
                          {
                            required: true,
                            message: `${t(generateFiledKey('email'), { defaultValue: '邮箱' })}${t(
                              generateFiledKey('input.required'),
                              {
                                defaultValue: '必填!',
                              }
                            )}`,
                          },
                          {
                            type: 'email',
                            message: t(generateFiledKey('emailFormatError'), {
                              defaultValue: '请输入正确的邮箱地址',
                            }),
                          },
                          {
                            max: 40,
                            message: t(generateFiledKey('input.maxLen'), {
                              defaultValue: '最多输入 40 个字符！',
                              max: 40,
                            }),
                          },
                        ]
                  }
                  noStyle
                >
                  {UserSelect ? (
                    /* @ts-ignore because of `TS2786: 'UserSelect' cannot be used as a JSX component.` */
                    <UserSelect
                      style={{ width: 288 }}
                      labelInValue
                      authorized
                      resourceParams={
                        idc
                          ? [
                              {
                                resourceType: 'IDC',
                                resourceCodes: [idc],
                              },
                            ]
                          : undefined
                      }
                    />
                  ) : (
                    <Input style={{ width: 288 }} />
                  )}
                </Form.Item>
                {fields.length > 1 || canDelete ? (
                  <DeleteOutlined onClick={() => remove(field.name)} />
                ) : null}
              </Space>
            </Form.Item>
          ))}
          <Form.Item {...(formLayout !== 'vertical' ? formItemLayoutWithOutLabel : {})}>
            <Button
              style={{ width: 288 }}
              type="dashed"
              disabled={fields.length >= 10}
              icon={<PlusOutlined />}
              onClick={() => {
                add();
              }}
            >
              {t(generateFiledKey('button.addEmail'), { defaultValue: '添加邮箱' })}
            </Button>
          </Form.Item>
        </>
      )}
    </Form.List>
  );
};
