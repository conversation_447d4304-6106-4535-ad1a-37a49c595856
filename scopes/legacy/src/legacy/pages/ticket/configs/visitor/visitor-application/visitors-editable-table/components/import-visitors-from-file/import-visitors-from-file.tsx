import { QuestionCircleOutlined } from '@ant-design/icons';
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Button } from '@manyun/base-ui.ui.button';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnsType } from '@manyun/base-ui.ui.table';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import { Upload } from '@manyun/base-ui.ui.upload';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { getVisitorsFromFile } from '@manyun/sentry.service.get-visitors-from-file';
import type { VisitorFromFile } from '@manyun/sentry.service.get-visitors-from-file';
import { getVisitorsTemplateFile } from '@manyun/sentry.service.get-visitors-template-file';

import { LOCALE_SCOPE_NAME, generateFiledKey } from '../../locales/index.js';

export type ImportVisitorsFromFileProps = {
  idc: string;
  allowedTimeRange: [number, number];
  bizTag?: string | undefined;
  onOk: (data: VisitorFromFile[]) => void;
};

export function ImportVisitorsFromFile({
  idc,
  allowedTimeRange,
  bizTag,
  onOk,
}: ImportVisitorsFromFileProps) {
  const { t } = useTranslation(LOCALE_SCOPE_NAME);
  const [visible, setVisible] = React.useState(false);
  const [tableData, setDataSource] = React.useState<VisitorFromFile[]>([]);
  const [confirmDisable, setConfirmDisable] = React.useState(true);
  const [configUtil] = useConfigUtil();
  const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket');
  const { hiddenOperationalAuthorization = false, hiddenPersonalItem = false } =
    ticketScopeCommonConfigs?.entryOfPersons?.features ?? {
      hiddenOperationalAuthorization: false,
      hiddenPersonalItem: false,
    };
  const disabled = !idc || allowedTimeRange.length < 2;
  const uploadFile = async (fd: FormData) => {
    const { error, data } = await getVisitorsFromFile(fd);
    if (error) {
      message.error(error.message);
      return;
    }
    if (data) {
      if (data.faultTotal === 0) {
        message.success(
          t(generateFiledKey('message.importSuccess'), { defaultValue: '导入成功！' })
        );
        setConfirmDisable(false);
      }
      setDataSource(data.excelCheckDtos);
    }
  };

  const beforeUpload = (file: File) => {
    const fd = new FormData();
    fd.append('file', file);
    fd.append('idcTag', idc);
    fd.append('approveStartTime', allowedTimeRange[0].toString());
    fd.append('approveEndTime', allowedTimeRange[1].toString());
    bizTag && fd.append('bizTag', bizTag);

    uploadFile(fd);

    return false;
  };

  const columns: ColumnsType<VisitorFromFile> = useMemo(
    () =>
      [
        {
          title: t(generateFiledKey('userInfos.index'), { defaultValue: '序号' }),
          dataIndex: 'rowTag',
          fixed: 'left',
        },
        {
          title: t(generateFiledKey('userInfos.visitorType'), { defaultValue: '人员类型' }),
          dataIndex: ['data', 'visitorType'],
          render: (text, { errMessage }) => getToolTilp(text, errMessage, 'visitorType'),
        },
        {
          title: t(generateFiledKey('userInfos.name'), { defaultValue: '姓名' }),
          dataIndex: ['data', 'name'],
          render: (text, { errMessage }) => getToolTilp(text, errMessage, 'name'),
        },
        {
          title: t(generateFiledKey('userInfos.identification.type'), { defaultValue: '证件类型' }),
          dataIndex: ['data', 'certificateType'],
          render: (text, { errMessage }) => getToolTilp(text, errMessage, 'certificateType'),
        },
        {
          title: t(generateFiledKey('userInfos.identification.number'), {
            defaultValue: '证件号码',
          }),
          dataIndex: ['data', 'identityNo'],
          render: (text, { errMessage }) => getToolTilp(text, errMessage, 'identityNo'),
        },
        {
          title: bizTag
            ? '单位名称'
            : t(generateFiledKey('userInfos.companyName'), { defaultValue: '公司名称' }),
          dataIndex: ['data', 'companyName'],
          render: (text, { errMessage }) => getToolTilp(text, errMessage, 'companyName'),
        },
        {
          title: t(generateFiledKey('userInfos.mobile.title'), { defaultValue: '联系方式' }),
          dataIndex: ['data', 'contactWay'],
          render: (text, { data: { phoneCountryCode }, errMessage }) => (
            <Space>
              {getToolTilp(phoneCountryCode, errMessage, 'phoneCountryCode')}
              {getToolTilp(text, errMessage, 'contactWay')}
            </Space>
          ),
        },
        {
          title: t(generateFiledKey('userInfos.position')),
          dataIndex: ['data', 'position'],
          render: (text, { errMessage }) => getToolTilp(text, errMessage, 'position'),
        },
        {
          title: t(generateFiledKey('userInfos.LPN'), { defaultValue: '车牌号' }),
          dataIndex: ['data', 'plateNo'],
          render: (text, { errMessage }) => getToolTilp(text, errMessage, 'plateNo'),
        },
        {
          title: t(generateFiledKey('userInfos.personalGoods')),
          dataIndex: ['data', 'personalItem'],
          hidden: hiddenPersonalItem,
          render: (text, { errMessage }) => getToolTilp(text, errMessage, 'personalItem'),
        },
        {
          title: t(generateFiledKey('userInfos.operationalAuthorization.title'), {
            defaultValue: '操作权限',
          }),
          hidden: hiddenOperationalAuthorization,
          dataIndex: ['data', 'operable'],
          render: (text, { errMessage }) => getToolTilp(text, errMessage, 'operable'),
        },
      ].filter(i => !i.hidden),
    [t, bizTag, hiddenPersonalItem, hiddenOperationalAuthorization]
  );
  return (
    <>
      <Button
        type="primary"
        disabled={disabled}
        onClick={() => {
          setVisible(true);
        }}
      >
        {t(generateFiledKey('button.importPerson'), { defaultValue: '导入人员' })}
      </Button>
      <Modal
        width={1024}
        title={t(generateFiledKey('button.importPerson'), { defaultValue: '导入人员' })}
        open={visible}
        okButtonProps={{ disabled: confirmDisable }}
        onOk={() => {
          onOk(tableData);
          setVisible(false);
          setDataSource([]);
          setConfirmDisable(true);
        }}
        onCancel={() => {
          setVisible(false);
          setDataSource([]);
          setConfirmDisable(true);
        }}
      >
        <Space style={{ display: 'flex' }} direction="vertical">
          <Space>
            <Upload accept=".csv,.xls,.xlsx" showUploadList={false} beforeUpload={beforeUpload}>
              <Button type="primary">
                {t(generateFiledKey('button.import'), { defaultValue: '导入' })}
              </Button>
            </Upload>
            <FileExport
              text={t(generateFiledKey('button.downloadTpl'), { defaultValue: '下载模板' })}
              filename={`${t(generateFiledKey('button.downloadTplFileName'), {
                defaultValue: '人员入室模板',
              })}.xlsx`}
              data={async () => {
                // @ts-ignore
                const { error, data } = await getVisitorsTemplateFile({
                  bizTag: bizTag ? bizTag : '',
                });
                if (error) {
                  message.error(error.message);
                  return Promise.reject(error);
                }
                if (!data) {
                  return Promise.reject('unknow error');
                }
                return data;
              }}
            />
          </Space>
          <Table
            rowKey="rowTag"
            columns={columns}
            scroll={{ x: 'max-content' }}
            dataSource={tableData}
          />
        </Space>
      </Modal>
    </>
  );
}

function getToolTilp(
  value: string | undefined,
  errMessage: Record<string, string>,
  dataType: string
) {
  if (Object.prototype.hasOwnProperty.call(errMessage, dataType)) {
    return (
      <Tooltip title={errMessage[dataType]} placement="topLeft">
        <Space size={2}>
          <Typography.Text type="danger">{value ?? '--'}</Typography.Text>
          <QuestionCircleOutlined />
        </Space>
      </Tooltip>
    );
  }

  return value;
}
