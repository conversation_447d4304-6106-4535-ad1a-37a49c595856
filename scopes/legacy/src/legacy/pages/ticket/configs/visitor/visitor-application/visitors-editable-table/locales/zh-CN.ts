import type { VisitorsEditableTableLocales } from './type';

export const zhCN: VisitorsEditableTableLocales = {
  userInfos: {
    index: '序号',
    title: '人员信息',
    visitorType: '人员类型',
    visitorTypeEnum: {
      CUSTOMER: '客户',
      GENERAL: '参观访客',
      MAINTAIN: '维护施工',
      SERVICE: '服务商',
      VIP: 'VIP',
    },
    name: '姓名',
    gender: '性别',
    genderEnum: {
      male: '男',
      female: '女',
    },
    mobile: {
      title: '联系方式',
      error: '联系方式格式不正确',
      duplicate: '联系方式重复',
    },
    identification: {
      type: '证件类型',
      number: '证件号码',
      numberRequire: '证件号码必填！',
      error: '格式不正确！',
      duplicate: '证件号重复',
    },
    companyName: '公司名称',
    position: '职位',
    personalGoods: '随身物品',
    personalGoodsTips: {
      prefix: '禁止携带有违禁品',
      middle: '，请提交 ',
      suffix: '物资进出单',
    },
    LPN: '车牌号',
    authorizedArea: '授权区域',
    operationalAuthorization: {
      title: '操作权限',
      enum: { yes: '需要', no: '不需要' },
    },
    infoTitle: '{{userName}} 申请 {{idc}} 来访 {{visitType}}人员',
    option: '操作',
  },
  alter: {
    timeRange: '在{{startTime}}至{{endTime}}',
    entryNum: '入室单号',
    exit: '已有授权记录',
    repeatError: '已有{{blockGuid}}申请记录 ',
    repeatAt: '在',
  },
  message: {
    importSuccess: '导入成功！',
  },
  button: {
    addUser: '添加人员',
    editUser: '编辑人员',
    edit: '编辑',
    add: '添加',
    delete: '删除',
    reset: '重置',
    confirm: '确认',
    checkAll: '全选',
    importPerson: '导入人员',
    import: '导入',
    downloadTpl: '下载模板',
    downloadTplFileName: '人员入室模板',
    deleteConfirm: '删除此行?',
  },
  input: {
    maxLen: '最多输入 {{max}} 个字符！',
    required: '必填',
  },
  select: {
    required: '必选',
  },
};
export default zhCN;
