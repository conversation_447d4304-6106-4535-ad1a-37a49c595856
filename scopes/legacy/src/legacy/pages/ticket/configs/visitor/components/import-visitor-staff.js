import React, { useState } from 'react';

import QuestionCircleOutlined from '@ant-design/icons/es/icons/QuestionCircleOutlined';
import { saveAs } from 'file-saver';
import shortid from 'shortid';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';

import { Upload } from '@manyun/dc-brain.ui.upload';

import { ticketService } from '@manyun/dc-brain.legacy.services';

import {
  CERTIFICATE_TYPE_MAP,
  VISITOR_IS_OPERABLE_KEY_TEXT_MAP,
  VISITOR_IS_SEX_KEY_TEXT_MAP,
  VISITOR_TYPE_MAP,
} from '../constants';

const columns = [
  {
    title: '序号',
    dataIndex: 'rowTag',
    fixed: 'left',
  },

  {
    title: '人员类型',
    dataIndex: ['data', 'visitorType'],
    render: (text, { errMessage }) => getToolTilp(text, errMessage, 'visitorType'),
  },
  {
    title: '姓名',
    dataIndex: ['data', 'name'],
    render: (text, { errMessage }) => getToolTilp(text, errMessage, 'name'),
  },
  {
    title: '证件类型',
    dataIndex: ['data', 'certificateType'],
    render: (text, { errMessage }) => getToolTilp(text, errMessage, 'certificateType'),
  },
  {
    title: '证件号码',
    dataIndex: ['data', 'identityNo'],
    render: (text, { errMessage }) => getToolTilp(text, errMessage, 'identityNo'),
  },
  {
    title: '公司名称',
    dataIndex: ['data', 'companyName'],
    render: (text, { errMessage }) => getToolTilp(text, errMessage, 'companyName'),
  },
  {
    title: '联系方式',
    dataIndex: ['data', 'contactWay'],
    render: (text, { errMessage }) => getToolTilp(text, errMessage, 'contactWay'),
  },
  {
    title: '车牌号',
    dataIndex: ['data', 'plateNo'],
    render: (text, { errMessage }) => getToolTilp(text, errMessage, 'plateNo'),
  },
  {
    title: '授权区域',
    dataIndex: ['data', 'approveArea'],
    render: (text, { errMessage }) => getToolTilp(text, errMessage, 'approveArea'),
  },
  {
    title: '是否具备操作权限',
    dataIndex: ['data', 'operable'],
    render: (text, { errMessage }) => getToolTilp(text, errMessage, 'operable'),
  },
];

export function ImportVisitor({ idc, allowedTimeRange, onImportVisitor }) {
  const [visible, setVisible] = useState(false);
  const [tableData, setDataSource] = useState([]);
  const [confirmDisable, setConfirmDisable] = useState(true);

  const downloadDemo = async () => {
    const { response, filename = 'import-visitor-template.csv' } =
      await ticketService.downloadVisitorModel();
    if (!response) {
      return;
    }
    saveAs(response, filename);
  };

  const disabled = !idc || allowedTimeRange.length < 2;
  const importVisitsRecords = async fd => {
    if (disabled) {
      return;
    }
    const { response, error } = await ticketService.visitorStaffImport(fd, allowedTimeRange);
    if (error) {
      message.error(error);
      return;
    }
    if (response) {
      if (response.faultTotal === 0) {
        message.success('导入成功！');
        setConfirmDisable(false);
      }
      setDataSource(response.excelCheckDtos);
    }
  };

  const beforeUpload = file => {
    const fd = new FormData();
    fd.append('file', file);
    fd.append('idcTag', idc);
    fd.append('approveStartTime', allowedTimeRange[0]);
    fd.append('approveEndTime', allowedTimeRange[1]);
    importVisitsRecords(fd);
    return false;
  };

  return (
    <>
      <Button
        type="primary"
        disabled={disabled}
        onClick={() => {
          setVisible(true);
        }}
      >
        导入
      </Button>
      <Modal
        width="1024px"
        title="导入"
        open={visible}
        okButtonProps={{ disabled: confirmDisable }}
        onOk={() => {
          onImportVisitor(
            tableData.map(item => {
              return {
                ...item.data,
                id: shortid(),
                sex: VISITOR_IS_SEX_KEY_TEXT_MAP.get(item.data.sex),
                operable: VISITOR_IS_OPERABLE_KEY_TEXT_MAP.get(item.data.operable),
                visitorType: VISITOR_TYPE_MAP.get(item.data.visitorType),
                certificateType: CERTIFICATE_TYPE_MAP.get(item.data.certificateType),
              };
            })
          );
          setVisible(false);
          setDataSource([]);
          setConfirmDisable(true);
        }}
        onCancel={() => {
          setVisible(false);
          setDataSource([]);
          setConfirmDisable(true);
        }}
      >
        <Space style={{ display: 'flex' }} direction="vertical">
          <Space>
            <Upload
              key="import"
              beforeUpload={beforeUpload}
              showUploadList={false}
              accept=".csv,.xls,.xlsx"
            >
              <Button type="primary">导入</Button>
            </Upload>
            <Button key="download" onClick={downloadDemo}>
              下载模板
            </Button>
          </Space>
          <Table
            rowKey="rowTag"
            columns={columns}
            tableLayout="fixed"
            scroll={{ x: 'max-content' }}
            dataSource={tableData}
          />
        </Space>
      </Modal>
    </>
  );
}

export default ImportVisitor;

function getToolTilp(value, errMessage, dataType) {
  const newValue = value;
  if (Object.prototype.hasOwnProperty.call(errMessage, dataType)) {
    return (
      <Tooltip title={errMessage[dataType]} placement="topLeft">
        <Space size={2}>
          <Typography.Text type="danger">{newValue ? newValue : '--'}</Typography.Text>
          <QuestionCircleOutlined />
        </Space>
      </Tooltip>
    );
  }
  return newValue;
}
