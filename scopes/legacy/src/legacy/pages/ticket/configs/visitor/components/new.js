/** 注意：此文件已废弃 */
import React, { useEffect, useState } from 'react';
import { connect, useDispatch } from 'react-redux';
import { useHistory, useLocation, useParams } from 'react-router-dom';

import Form from '@ant-design/compatible/es/form';
import { ApiSelect, Select } from '@galiojs/awesome-antd';
import md5 from 'crypto-js/md5';
import pick from 'lodash.pick';
import uniqWith from 'lodash/uniqWith';
import moment from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Tag } from '@manyun/base-ui.ui.tag';

import { syncCommonDataAction } from '@manyun/dc-brain.state.common';
import { AddVisitorModalButton } from '@manyun/sentry.ui.visitors-ticket-mutator';

import { FooterToolBar, GutterWrapper, LocationCascader } from '@manyun/dc-brain.legacy.components';
import { TinyTable } from '@manyun/dc-brain.legacy.components/tiny-table';
import { METADATA_TYPE } from '@manyun/dc-brain.legacy.constants';
import { taskCenterService, ticketService } from '@manyun/dc-brain.legacy.services';
import { getLocationSearchMap, getObjectOwnProps } from '@manyun/dc-brain.legacy.utils';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';

import { CERTIFICATE_TYPE_KEY_MAP, VISITOR_TYPE_KEY_MAP } from '../constants';
import ImportVisitor from './import-visitor-staff';

export function VisitorTicketNew({ form, ticketType, subTaskTypes }) {
  const [loading, setLoading] = useState(false);
  const [visitorList, setVisitorList] = useState([]);
  const { id } = useParams();
  const history = useHistory();
  const { search } = useLocation();

  const { getFieldDecorator, validateFields, getFieldValue, setFieldsValue } = form;

  const dispatch = useDispatch();
  React.useEffect(() => {
    dispatch(
      syncCommonDataAction({
        strategy: {
          ticketTypes: 'IF_NULL',
        },
      })
    );
  }, [dispatch]);

  useEffect(() => {
    if (!id) {
      return;
    }
    (async () => {
      const { response, error } = await ticketService.fetchTicketBasicInfo({
        taskNo: id,
      });
      if (error) {
        message.error(error);
        return;
      }

      let taskProperties = {};
      try {
        taskProperties = JSON.parse(response.taskProperties);
      } catch (error) {
        console.error(error);
      }

      setFieldsValue({
        taskSubType: response.taskSubType,
        relateTaskNo: taskProperties.relateTaskNo,
        relateTaskType: taskProperties.relateTaskType,
        taskTitle: response.taskTitle,
        enterReason: taskProperties.enterReason,
        dataRange: [moment(taskProperties.approveStartTime), moment(taskProperties.approveEndTime)],
        idcTag: [response.idcTag],
      });
    })();
    (async () => {
      const { response, error } = await taskCenterService.fetchVisitorInfoStaffList({
        taskNo: id,
      });
      if (error) {
        message.error(error);
        return;
      }
      setVisitorList(
        response.data.map(item => {
          return {
            ...item,
            visitorType: item.visitorType.code,
            certificateType: item.certificateType?.code,
          };
        })
      );
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id]);

  const { variables } = getLocationSearchMap(search, ['variables']);
  let varsMap;

  try {
    varsMap = JSON.parse(variables);
  } catch (error) {
    // ignore...
  }

  const handleDelete = record => {
    setVisitorList(prevVisitorList => prevVisitorList.filter(item => item.id !== record.id));
  };

  const allowedTimeRange = getFieldValue('dataRange');
  const _allowedTimeRange =
    allowedTimeRange && allowedTimeRange.length >= 2
      ? [allowedTimeRange[0].valueOf(), allowedTimeRange[1].valueOf()]
      : [];
  const idc = getFieldValue('idcTag')?.[0];

  return (
    <GutterWrapper mode="vertical">
      <Card title="基本信息">
        <Form colon={false} labelCol={{ xl: 3, xxl: 2 }} wrapperCol={{ xl: 7, xxl: 6 }}>
          <Form.Item label="入室类型">
            {getFieldDecorator('taskSubType', {
              rules: [{ required: true, message: '入室类型必选！' }],
            })(
              <Select style={{ width: '100%' }} showSearch allowClear>
                {subTaskTypes.map(item => (
                  <Select.Option key={item.taskType}>{item.taskValue}</Select.Option>
                ))}
              </Select>
            )}
          </Form.Item>
          <Form.Item label="关联工单类型">
            {getFieldDecorator('relateTaskType', {
              initialValue: varsMap?.relateTaskType,
              rules: [{ required: false }],
            })(
              <ApiSelect
                style={{ width: '100%' }}
                disabled={variables ? true : false}
                showSearch
                allowClear
                trigger="onDidMount"
                fieldNames={{ label: 'label', value: 'value' }}
                dataService={async () => {
                  const { response } = await ticketService.fetchTaskType();
                  if (response) {
                    return Promise.resolve(getObjectOwnProps(response));
                  } else {
                    return Promise.resolve([]);
                  }
                }}
              />
            )}
          </Form.Item>
          <Form.Item label="关联单号">
            {getFieldDecorator('relateTaskNo', {
              initialValue: varsMap?.relateTaskNo,
              rules: [
                {
                  required: false,
                  whitespace: true,
                },
                {
                  type: 'string',
                  max: 20,
                  message: '最多输入 20 个字符！',
                },
              ],
            })(<Input disabled={variables ? true : false} />)}
          </Form.Item>
          <Form.Item label="工单标题">
            {getFieldDecorator('taskTitle', {
              rules: [
                {
                  required: true,
                  whitespace: true,
                  message: '工单标题必填！',
                },
                {
                  type: 'string',
                  max: 20,
                  message: '最多输入 20 个字符！',
                },
              ],
            })(<Input />)}
          </Form.Item>
          <Form.Item label="入室原因">
            {getFieldDecorator('enterReason', {
              rules: [
                {
                  required: true,
                  whitespace: true,
                  message: '入室原因必填！',
                },
                {
                  type: 'string',
                  max: 100,
                  message: '最多输入 100 个字符！',
                },
              ],
            })(<Input.TextArea />)}
          </Form.Item>
          <Form.Item label="授权时间">
            {getFieldDecorator('dataRange', {
              rules: [
                {
                  required: true,
                  message: '授权时间必填！',
                },
              ],
            })(
              <DatePicker.RangePicker
                showTime
                format="YYYY-MM-DD HH:mm:ss"
                allowClear
                style={{ width: '100%' }}
              />
            )}
          </Form.Item>
          <Form.Item label="申请入室机房">
            {getFieldDecorator('idcTag', {
              initialValue: [varsMap?.idcTag],
              rules: [
                {
                  required: true,
                  message: '入室机房必填！',
                },
              ],
            })(
              <LocationCascader
                disabled={variables ? true : false}
                idcsOnly
                currentAuthorize
                allowClear={false}
                onChange={() => {
                  setVisitorList([]);
                }}
              />
            )}
          </Form.Item>
        </Form>
      </Card>
      <Card bordered={false} title="人员信息">
        <TinyTable
          rowKey="id"
          columns={getColumns({
            handleDelete,
          })}
          actions={[
            <AddVisitorModalButton
              key="add-visitor"
              idc={idc}
              allowedTimeRange={_allowedTimeRange}
              onOk={values => {
                let picks = ['name', 'mobile'];
                if (!!values.ICN) {
                  picks = ['ICN'];
                }
                const syntheticId = md5(JSON.stringify(pick(values, picks))).toString();
                if (visitorList.some(({ id }) => id === syntheticId)) {
                  const reason = '该人员已存在列表中！';
                  message.error(reason);
                  return Promise.reject(reason);
                }
                setVisitorList(prevVisitorList => [
                  {
                    id: syntheticId,
                    visitorType: values.visitorType,
                    name: values.name,
                    sex: values.gender === 'male' ? 1 : 0,
                    certificateType: values.identificationType,
                    identityNo: values.ICN,
                    companyName: values.companyName,
                    contactWay: values.mobile,
                    plateNo: values.LPN,
                    approveArea: values.authorizedArea?.join(','),
                    operable: values.operationalAuthorization === 'yes',
                  },
                  ...prevVisitorList,
                ]);
                return Promise.resolve();
              }}
            />,
            <ImportVisitor
              key="import-visitor"
              idc={idc}
              allowedTimeRange={_allowedTimeRange}
              onImportVisitor={importVisitor => {
                setVisitorList(prevVisitorList =>
                  uniqWith([...importVisitor, ...prevVisitorList], (a, b) => {
                    return (
                      a.identityNo !== null &&
                      b.identityNo !== null &&
                      a.identityNo === b.identityNo
                    );
                  })
                );
              }}
            />,
          ]}
          dataSource={visitorList}
        />
      </Card>
      <div style={{ height: 50 }} />
      <FooterToolBar>
        <GutterWrapper>
          <Button
            type="primary"
            loading={loading}
            disabled={!visitorList.length}
            onClick={() => {
              validateFields(async (error, valueMap) => {
                if (error) {
                  return;
                }
                setLoading(true);

                const query = getQ(valueMap, visitorList, id);
                let taskNo = null;
                if (id !== null && id !== undefined) {
                  taskNo = await updateTicket(query);
                } else {
                  taskNo = await createTicket(query);
                }

                if (taskNo !== null && taskNo !== undefined) {
                  setTimeout(() => {
                    history.push(urls.generateTicketDetailLocation({ ticketType, id: taskNo }));
                  }, 1.5 * 1000);
                }
                setLoading(false);
              });
            }}
          >
            提交
          </Button>
          <Button
            loading={loading}
            onClick={() => {
              history.goBack();
            }}
          >
            取消
          </Button>
        </GutterWrapper>
      </FooterToolBar>
    </GutterWrapper>
  );
}
const mapStateToProps = ({ common: { ticketTypes } }) => {
  let subTaskTypes = [];
  const visitorTicketType = ticketTypes?.treeList.find(
    ({ taskType }) => taskType === METADATA_TYPE.VISITOR
  );
  if (visitorTicketType) {
    subTaskTypes = visitorTicketType.children
      .map(({ taskType, taskValue, flag }) => {
        if (flag) {
          return null;
        }
        return {
          taskType,
          taskValue,
        };
      })
      .filter(Boolean);
  }
  return {
    subTaskTypes,
  };
};
export default connect(mapStateToProps)(Form.create()(VisitorTicketNew));

function getQ(
  { taskSubType, relateTaskNo, relateTaskType, taskTitle, enterReason, dataRange, idcTag },
  visitorList,
  taskNo
) {
  return {
    taskNo,
    taskSubType,
    relateTaskNo,
    relateTaskType,
    taskTitle,
    enterReason,
    approveStartTime: dataRange[0],
    approveEndTime: dataRange[1],
    idcTag: idcTag[0],
    visitorInfos: visitorList,
  };
}

async function updateTicket(data) {
  const { response, error } = await taskCenterService.updateVisitorTicket(data);

  if (error) {
    message.error(error);
    return;
  }

  message.success('重新发起成功！');
  return response;
}

async function createTicket(data) {
  const { response, error } = await taskCenterService.createVisitorTicket(data);

  if (error) {
    message.error(error);
    return;
  }

  message.success('创建成功！');
  return response;
}

const getColumns = ctx => [
  {
    title: '人员类型',
    dataIndex: 'visitorType',
    render(visitorType) {
      return VISITOR_TYPE_KEY_MAP.get(visitorType);
    },
  },
  {
    title: '姓名',
    dataIndex: 'name',
  },
  {
    title: '证件类型',
    dataIndex: 'certificateType',
    render(certificateType) {
      return CERTIFICATE_TYPE_KEY_MAP.get(certificateType);
    },
  },
  {
    title: '证件号码',
    dataIndex: 'identityNo',
  },
  {
    title: '公司名称',
    dataIndex: 'companyName',
  },
  {
    title: '联系方式',
    dataIndex: 'contactWay',
  },
  {
    title: '车牌号',
    dataIndex: 'plateNo',
  },
  {
    title: '授权区域',
    dataIndex: 'approveArea',
    render(approveArea) {
      if (!approveArea) {
        return '--';
      }
      const areas = approveArea.split(',');

      return areas.map(area => {
        const metaCodes = area.split('.');
        const isIdcDotIdc = metaCodes.length === 2 && metaCodes[0] === metaCodes[1];
        // 如果是园区的话，就展示园区
        const label = isIdcDotIdc ? `${metaCodes[0]}.园区` : area;

        return <Tag key={area}>{label}</Tag>;
      });
    },
  },
  {
    title: '是否具备操作权限',
    dataIndex: 'operable',
    render(operable) {
      if (operable) {
        return '是';
      }
      return '否';
    },
  },
  {
    title: '操作',
    dataIndex: 'operation',
    fixed: 'right',
    render: (text, record) => (
      <Button type="link" onClick={() => ctx.handleDelete(record)}>
        删除
      </Button>
    ),
  },
];
