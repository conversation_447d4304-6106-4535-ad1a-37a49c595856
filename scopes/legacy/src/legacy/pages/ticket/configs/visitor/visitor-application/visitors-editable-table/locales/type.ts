export type VisitorsEditableTableLocales = {
  userInfos: {
    index: string;
    title: string;
    visitorType: string;
    visitorTypeEnum: {
      CUSTOMER: string;
      GENERAL: string;
      MAINTAIN: string;
      SERVICE: string;
      VIP: string;
    };
    name: string;
    gender: string;
    genderEnum: {
      male: string;
      female: string;
    };
    mobile: {
      title: string;
      error: string;
      duplicate: string;
    };
    identification: {
      type: string;
      number: string;
      numberRequire: string;
      error: string;
      duplicate: string;
    };
    companyName: string;
    position: string;
    personalGoods: string;
    personalGoodsTips: {
      prefix: string;
      middle: string;
      suffix: string;
    };
    LPN: string;
    authorizedArea: string;
    operationalAuthorization: {
      title: string;
      enum: { yes: string; no: string };
    };
    infoTitle: string;
    option: string;
  };
  alter: {
    timeRange: string;
    entryNum: string;
    exit: string;
    repeatError: string;
    repeatAt: string;
  };
  message: {
    importSuccess: string;
  };
  button: {
    addUser: string;
    editUser: string;
    edit: string;
    add: string;
    delete: string;
    reset: string;
    confirm: string;
    checkAll: string;
    importPerson: string;
    import: string;
    downloadTpl: string;
    downloadTplFileName: string;
    deleteConfirm: string;
  };
  input: {
    maxLen: string;
    required: string;
  };
  select: {
    required: string;
  };
};
