import React from 'react';

import { Input } from '@manyun/base-ui.ui.input';
import type { InputProps } from '@manyun/base-ui.ui.input';
import { Select } from '@manyun/base-ui.ui.select';
import type { SelectProps } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { useMetaData } from '@manyun/resource-hub.hook.use-meta-data';
import { MetaType } from '@manyun/resource-hub.model.metadata';

export type Value = [string | undefined, string | undefined];
export type MobileFieldProps = {
  typeSelectProps?: SelectProps;
  MobileInputProps?: InputProps;
  value?: Value;
  onChange?: (value: Value) => void;
};

function _MobileField(
  { typeSelectProps, MobileInputProps, value, onChange }: MobileFieldProps,
  ref?: React.ForwardedRef<void>
) {
  const [mobileOptions, setMobileOptions] = React.useState([
    {
      value: '+86',
      label: '+86',
    },
    // {
    //   value: '+1',
    //   label: '+1',
    // },
    // {
    //   value: '+351',
    //   label: '+351',
    // },
    // {
    //   value: '+353',
    //   label: '+353',
    // },
    // {
    //   value: '+44',
    //   label: '+44',
    // },
    // {
    //   value: '+60',
    //   label: '+60',
    // },
    // {
    //   value: '+65',
    //   label: '+65',
    // },
    // {
    //   value: '+852',
    //   label: '+852',
    // },
    // {
    //   value: '+886',
    //   label: '+886',
    // },
  ]);

  const [{ data }, { readMetaData }] = useMetaData(MetaType.PHONE_REGION_CODE);
  React.useEffect(() => {
    readMetaData();
  }, []);
  React.useEffect(() => {
    if (Array.isArray(data?.data)) {
      setMobileOptions(data?.data);
    }
  }, [data]);

  return (
    <Space.Compact>
      <Select
        {...typeSelectProps}
        options={mobileOptions}
        style={{ width: 100 }}
        value={value?.[0]}
        onChange={type => {
          onChange?.([type, value?.[1]]);
        }}
      />
      <Input
        {...MobileInputProps}
        value={value?.[1]}
        onChange={({ target: { value: ICN } }) => {
          onChange?.([value?.[0], ICN]);
        }}
      />
    </Space.Compact>
  );
}

export const MobileField = React.forwardRef(_MobileField);
MobileField.displayName = 'MobileField';
