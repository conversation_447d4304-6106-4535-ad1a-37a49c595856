import moment from 'moment';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Cascader } from '@manyun/base-ui.ui.cascader';
import { Input } from '@manyun/base-ui.ui.input';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';
import { BackendTaskStatus } from '@manyun/ticket.model.ticket';

import TTR from './../../registries/ticket-type-registry';
import VisitorTicketDetail from './components/detail';
import { VisitorStatusSelect } from './visitor-application/visitor-status-select';
// import { VisitsMutator } from '@manyun/sentry.page.visits-mutator';
import { VisitsMutator } from './visitor-application/visits-mutator';

TTR.registerTicketType('visitor')
  .registerConfig({
    type: 'tickets',

    showNewBtn: true,
    showColumnOperation: true,
    listServiceEndpoint: 'fetchVisitorList',

    showBatchCloseTicketBtn: false,
    showBatchTakeOverBtn: false,
    showRowSelection: false,
    showResendOperation: true,
    showRevokeOperation: true,

    /**
     * @param {object} param1
     * @param {typeof import('./../../views/tickets/constants').DEFAULT_FILTER_KEY_MAP} param1.DEFAULT_FILTER_KEY_MAP
     * @param {any[]} param1.defaultColumns
     */
    mergeFilters: ({ DEFAULT_FILTER_KEY_MAP, defaultFilters }) => {
      const newFilters = [...defaultFilters]
        .filter(({ key }) => key !== DEFAULT_FILTER_KEY_MAP.END_TIME)
        .map(i => {
          if (i.key === DEFAULT_FILTER_KEY_MAP.TASK_SUB_TYPE) {
            return { ...i, label: '业务分类' };
          }
          return i;
        });
      const insertIdx = newFilters.findIndex(({ key }) => key === DEFAULT_FILTER_KEY_MAP.IS_DELAY);

      const locationIdx = newFilters.findIndex(
        ({ key }) => key === DEFAULT_FILTER_KEY_MAP.LOCATION
      );

      newFilters.splice(locationIdx, 1, {
        label: '进入区域',
        key: 'blockGuidList',
        Comp: LocationCascader,
        initialProps: {
          allowClear: true,
          authorizedOnly: true,
          multiple: true,
          showCheckedStrategy: Cascader.SHOW_CHILD,
          maxTagCount: 'responsive',
          disabledNoChildsNodes: ['IDC'],
        },
        propsUtils: {
          pick: props => ({ defaultValue: props.searchValues?.location?.value }),
        },
        serializationOptions: {
          serialize: data => {
            return {
              value: data,
              blocks: data?.reduce((blockGuids, arr) => {
                if (arr[1]) {
                  blockGuids.push(arr[1]);
                }
                return blockGuids;
              }, []),
            };
          },
          parseValue: data => {
            return data.value;
          },
        },
      });

      newFilters.splice(insertIdx, 2);
      newFilters.push({
        label: '人员姓名',
        key: 'visitorName',
        Comp: Input,
        whitespace: 'trim',
        initialProps: { allowClear: true },
      });
      newFilters.push({
        label: '证件号码',
        key: 'identityNo',
        Comp: Input,
        whitespace: 'trim',
        initialProps: { allowClear: true },
      });

      newFilters.splice(
        newFilters.findIndex(item => item.key === 'taskStatusList'),
        1,
        {
          label: '工单状态',
          key: 'taskStatusList',
          initialProps: {
            optionFilter: option => ![BackendTaskStatus.CLOSE_APPROVER].includes(option.value),
            allowClear: true,
            mode: 'multiple',
          },
          Comp: VisitorStatusSelect,
        }
      );

      return newFilters;
    },

    /**
     * @param {object} param1
     * @param {typeof import('./../../views/tickets/constants').DEFAULT_COLUMN_DATA_INDEX_MAP} param1.DEFAULT_COLUMN_DATA_INDEX_MAP
     * @param {any[]} param1.defaultColumns
     */
    mergeColumns: ({ DEFAULT_COLUMN_DATA_INDEX_MAP, defaultColumns }) => {
      const newColumns = [...defaultColumns]
        .filter(({ dataIndex }) => dataIndex !== DEFAULT_COLUMN_DATA_INDEX_MAP.TASK_RESPOND_TIME)
        .map(i => {
          if (i.dataIndex === DEFAULT_COLUMN_DATA_INDEX_MAP.TASK_SUB_TYPE) {
            return { ...i, title: '业务分类' };
          }
          return i;
        });

      const assigneeIdx = newColumns.findIndex(
        ({ dataIndex }) => dataIndex === DEFAULT_COLUMN_DATA_INDEX_MAP.TASK_ASSIGNEE_NAME
      );
      const creatorIdx = newColumns.findIndex(
        ({ dataIndex }) => dataIndex === DEFAULT_COLUMN_DATA_INDEX_MAP.CREATOR_NAME
      );
      const slaIdx = newColumns.findIndex(
        ({ dataIndex }) => dataIndex === DEFAULT_COLUMN_DATA_INDEX_MAP.TASK_SLA
      );
      [newColumns[creatorIdx], newColumns[assigneeIdx]] = [
        newColumns[assigneeIdx],
        newColumns[creatorIdx],
      ];
      newColumns.splice(slaIdx, 1);
      newColumns.splice(creatorIdx, 1);

      const stateIdx = newColumns.findIndex(
        ({ dataIndex }) => dataIndex === DEFAULT_COLUMN_DATA_INDEX_MAP.TICKET_STATE
      );
      newColumns.splice(stateIdx, 0, {
        title: '授权人数',
        dataIndex: 'approveNumber',
        render(_, { taskProperties }) {
          try {
            const props = JSON.parse(taskProperties);
            return props.approveNumber;
          } catch (error) {
            return '--';
          }
        },
      });
      newColumns.splice(stateIdx, 0, {
        title: '申请结束时间',
        dataIndex: 'approveEndTime',
        dataType: 'datetime',
        render(_, { taskProperties }) {
          try {
            const props = JSON.parse(taskProperties);
            if (!props.approveEndTime) {
              return '--';
            }
            return moment(props.approveEndTime).format('YYYY-MM-DD HH:mm');
          } catch (error) {
            return '--';
          }
        },
      });
      newColumns.splice(stateIdx, 0, {
        title: '申请开始时间',
        dataIndex: 'approveStartTime',
        dataType: 'datetime',
        render(_, { taskProperties }) {
          try {
            const props = JSON.parse(taskProperties);
            if (!props.approveStartTime) {
              return '--';
            }
            return moment(props.approveStartTime).format('YYYY-MM-DD HH:mm');
          } catch (error) {
            return '--';
          }
        },
      });

      const locationIdx = newColumns.findIndex(
        ({ dataIndex }) => dataIndex === DEFAULT_COLUMN_DATA_INDEX_MAP.LOCATION
      );

      newColumns.splice(locationIdx, 1, {
        title: '进入园区',
        dataIndex: 'idcTag',
      });

      return newColumns;
    },
  })
  .registerConfig({
    type: 'specific-ticket',
    showStep: true,
    showRespondTime: false,
    showOperationRecords: true,
    showRevocationBtn: true,
    showReapprovalBtn: true,
    content: VisitorTicketDetail,
  })
  .registerConfig({
    type: 'new-ticket',
    content: VisitsMutatorPage,
  });

function VisitsMutatorPage() {
  return <VisitsMutator accessDirectUrl="/page/tickets/access/new" UserSelect={UserSelect} />;
}
