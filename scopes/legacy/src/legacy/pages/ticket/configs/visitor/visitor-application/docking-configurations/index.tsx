import { DeleteOutlined } from '@ant-design/icons';
import debounce from 'lodash.debounce';
import React from 'react';
import { useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';

import { selectMe } from '@manyun/auth-hub.state.user';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Form } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { message } from '@manyun/base-ui.ui.message';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { LocationCascader as ResourceHubLocationCascader } from '@manyun/resource-hub.ui.location-cascader';
import MetatypeSelect from '@manyun/resource-hub.ui.metatype-select';
import { useUpdateAssigneeConfigMutation } from '@manyun/ticket.gql.client.tickets';
import { fetchAddAssigneeConfig } from '@manyun/ticket.service.fetch-add-assignee-config';

export default function ConfiguringPopups() {
  const history = useHistory();
  const [configUtil] = useConfigUtil();
  const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket');

  const [getAssigneeConfig, { loading: assigneeConfigLoading }] = useUpdateAssigneeConfigMutation();
  const [form] = Form.useForm();
  const { idc } = useSelector(selectMe, (left, right) => left.idc === right.idc);

  const [state, setState] = React.useState([
    {
      key: 1,
      customerNo: undefined,
      assigneeType: undefined,
      blockGuid: undefined,
      assigneeList: [{ id: undefined, userName: undefined }],
    },
  ]);
  const [initFlatTreeData, setInitFlatTreeData] = React.useState([]);
  const [disabledAreas, setDisabledAreas] = React.useState({ 0: undefined });
  const [confirmedLoading, setConfirmedLoading] = React.useState(false);
  const [defaultAssigneeOptions, setDefaultAssigneeOptions] = React.useState<{
    [key: number]: [v: { label: string; value: string }[]];
  }>({});

  const onSubmit = React.useCallback(() => {
    form.validateFields().then(async values => {
      setConfirmedLoading(true);
      const params = {
        configList: state.map(i => ({
          customerNo: i.customerNo,
          assigneeType: i.assigneeType,
          blockGuid: Array.isArray(i.blockGuid) ? i.blockGuid[1] : i.blockGuid,
          assigneeList: i.assigneeList.filter(i => i.id),
        })),
      };
      const { data, error } = await fetchAddAssigneeConfig(params);
      if (error?.message) {
        message.error(error.message);
        setConfirmedLoading(false);
        return;
      }
      if (data) {
        message.success('配置成功');
      }
      setConfirmedLoading(false);
    });
  }, [form, idc, state]);

  React.useEffect(() => {
    (async () => {
      const { data: assigneeConfigData } = await getAssigneeConfig({
        variables: {
          query: {
            blockGuid: undefined,
          },
        },
        onCompleted(data) {},
      });
      const { data } = assigneeConfigData?.updateChangeAssigneeConfig;

      if (data) {
        const _data = data.map((i, k) => ({ ...i, key: k }));
        _data.forEach(item => {
          setDefaultAssigneeOptions(preState => ({
            ...preState,
            [item.key]: item.assigneeList.map(i => ({ label: i.userName, value: i.id })),
          }));
          form.setFieldValue(`${item.key}-customerNo`, item.customerNo);
          form.setFieldValue(`${item.key}-blockGuid`, [
            item.blockGuid.split('.')[0],
            item.blockGuid,
          ]);
          form.setFieldValue(`${item.key}-assigneeType`, item.assigneeType);
          form.setFieldValue(
            `${item.key}-assigneeList`,
            item.assigneeList.map(i => ({ value: i.id }))
          );
          form.setFieldValue(
            `${item.key}-defaultAssignee`,
            item.assigneeList.filter(i => i.userType === 1).map(i => i.id)
          );
        });
        setState(_data);
      }
    })();
  }, []);

  React.useEffect(() => {
    // 获取已选中的生效区域，并用于禁用相同申请来源的子级项
    const disabledAreas = state
      .filter(i => i.customerNo !== undefined) // 过滤出已有申请来源的项
      .reduce((acc, curr) => {
        if (curr.blockGuid && curr.customerNo !== undefined) {
          // 假设有个 hasChildren 标志表示是否为父级
          if (!curr.hasChildren) {
            // 仅当当前项没有子级时，才将其 blockGuid 放入禁用列表
            acc[curr.customerNo] = (acc[curr.customerNo] || []).concat(
              Array.isArray(curr.blockGuid) ? curr.blockGuid.at(-1) : curr.blockGuid
            );
          }
        }
        return acc;
      }, {});

    setDisabledAreas(disabledAreas);
  }, [state]);

  return (
    <>
      <Card>
        <div
          style={{
            width: '100%',
            height: 'calc(100vh - 204px)',
            overflowY: 'scroll',
            overflowX: 'hidden',
          }}
        >
          <Typography.Title style={{ marginBottom: '16px' }} level={5} showBadge>
            配置
          </Typography.Title>
          <Form form={form} layout="horizontal">
            {state.map((item, index) => {
              return (
                <>
                  <div
                    key={item.key}
                    style={{
                      width: '100%',
                      padding: '16px 16px 4px 16px',
                      marginBottom: '24px',
                      background: 'rgba(250,250,250,1)',
                    }}
                  >
                    <div
                      style={{
                        display: 'grid',
                        position: 'relative',
                        gridTemplateColumns: 'auto 30px',
                      }}
                    >
                      <Row gutter={[24, 12]}>
                        {/* 申请来源 */}
                        <Col span={6}>
                          <Form.Item
                            style={{ marginBottom: '12px' }}
                            label={
                              <div style={{ width: '70px' }}>
                                <div
                                  style={{
                                    display: 'inline-block',
                                    color: '#ff4d4f',
                                    marginRight: '4px',
                                    fontSize: '14px',
                                    fontFamily: 'SimSun, sans-serif',
                                    lineHeight: 1,
                                  }}
                                >
                                  *
                                </div>
                                申请来源
                              </div>
                            }
                            name={`${item.key}-customerNo`}
                            rules={[{ required: true, message: '请选择申请来源' }]}
                            required={false}
                          >
                            <MetatypeSelect
                              style={{ width: '100%' }}
                              metaType={'CUSTOMER'}
                              labelInValue
                              onChange={v => {
                                setState(prevState => {
                                  const _index = prevState.findIndex(i => i.key === item.key);
                                  if (_index === -1 || prevState[_index].customerNo === v.value) {
                                    return prevState;
                                  }

                                  const updatedState = [...prevState];
                                  updatedState[_index] = {
                                    ...updatedState[_index],
                                    customerNo: v.value,
                                    blockGuid: v.blockGuid?.filter((i: string) => i.includes('.')),
                                  };
                                  return updatedState;
                                });
                                form.setFieldValue(`${item.key}-assigneeType`, undefined);
                                form.setFieldValue(`${item.key}-blockGuid`, undefined);
                                form.setFieldValue(`${item.key}-assigneeList`, undefined);
                                form.setFieldValue(`${item.key}-defaultAssignee`, undefined);
                              }}
                            />
                          </Form.Item>
                        </Col>

                        {/* 对接人类型 */}
                        <Col span={8}>
                          <Form.Item
                            noStyle
                            shouldUpdate={(pre, next) => {
                              return (
                                pre[`${item.key}-customerNo`] !== next[`${item.key}-customerNo`]
                              );
                            }}
                          >
                            {() => {
                              return (
                                <Form.Item
                                  style={{ marginBottom: '12px' }}
                                  label="对接人类型"
                                  name={`${item.key}-assigneeType`}
                                  rules={[{ required: true, message: '请选择对接人类型' }]}
                                >
                                  <Radio.Group
                                    options={[
                                      {
                                        label: '手动确认',
                                        value: 1,
                                        // disabled:
                                        //   typeof _customerNo === 'string'
                                        //     ? _customerNo === 'dora'
                                        //     : _customerNo?.value === 'dora',
                                      },
                                      {
                                        label: '自动确认',
                                        value: 0,
                                      },
                                    ]}
                                    onChange={v => {
                                      setState(prevState => {
                                        const _index = prevState.findIndex(i => i.key === item.key);
                                        if (_index === -1) {
                                          return prevState;
                                        }

                                        const updatedState = [...prevState];
                                        updatedState[_index] = {
                                          ...updatedState[_index],
                                          assigneeType: v.target.value,
                                        };
                                        return updatedState;
                                      });
                                    }}
                                  />
                                </Form.Item>
                              );
                            }}
                          </Form.Item>
                        </Col>
                        {/* 生效区域 */}
                        <Col span={10}>
                          <Form.Item
                            noStyle
                            shouldUpdate={(pre, next) => {
                              return (
                                pre[`${item.key}-customerNo`] !== next[`${item.key}-customerNo`]
                              );
                            }}
                          >
                            {() => {
                              const customerNo: string | undefined = state?.find(
                                i => i.key === item.key
                              )?.customerNo;

                              return (
                                <Form.Item
                                  style={{ marginBottom: '12px' }}
                                  label="生效区域"
                                  name={`${item.key}-blockGuid`}
                                  rules={[
                                    {
                                      required: true,
                                      message: '请选择',
                                    },
                                    {
                                      type: 'array',
                                      len: 2,
                                      message: '必须选择到楼栋',
                                    },
                                  ]}
                                >
                                  <ResourceHubLocationCascader
                                    nodeTypes={['IDC', 'BLOCK']}
                                    disabledSpaceCodes={customerNo ? disabledAreas[customerNo] : []}
                                    maxTagCount="responsive"
                                    authorizedOnly
                                    allowClear
                                    disabledNoChildsNodes={['IDC']}
                                    disabledTypes={
                                      !!!customerNo ||
                                      initFlatTreeData.every(blockGuid =>
                                        disabledAreas[customerNo]?.includes(blockGuid)
                                      )
                                        ? ['IDC']
                                        : []
                                    }
                                    onChange={(v, o) => {
                                      setState(prevState => {
                                        const _index = prevState.findIndex(i => i.key === item.key);
                                        if (_index === -1) {
                                          return prevState;
                                        }

                                        const updatedState = [...prevState];
                                        updatedState[_index] = {
                                          ...updatedState[_index],
                                          blockGuid: v,
                                        };
                                        return updatedState;
                                      });
                                    }}
                                    onTreeDataChange={treeData => {
                                      if (treeData[0]) {
                                        setInitFlatTreeData(
                                          treeData[0].children?.map(i => i.value)
                                        );
                                      }
                                    }}
                                  />
                                </Form.Item>
                              );
                            }}
                          </Form.Item>
                        </Col>
                      </Row>

                      {state.length > 1 && form.getFieldValue(`${item.key}-assigneeType`) !== 1 && (
                        <div
                          style={{
                            position: 'relative',
                            width: '14px',
                            height: '14px',
                            top: '4px',
                            right: '-16px',
                          }}
                        >
                          <a
                            onClick={() => {
                              setState(state.filter(i => i.key !== item.key));
                            }}
                          >
                            <DeleteOutlined style={{ fontSize: '14px' }} />
                          </a>
                        </div>
                      )}
                    </div>

                    {form.getFieldValue(`${item.key}-assigneeType`) === 1 && (
                      <>
                        <div
                          style={{
                            display: 'grid',
                            position: 'relative',
                            gridTemplateColumns: 'auto 30px',
                          }}
                        >
                          <Row gutter={[24, 12]}>
                            {/* 对接人 */}
                            <Col span={24}>
                              <Form.Item
                                noStyle
                                shouldUpdate={(pre, next) => {
                                  return (
                                    pre[`${item.key}-assigneeType`] !==
                                    next[`${item.key}-assigneeType`]
                                  );
                                }}
                              >
                                {() => {
                                  const _assigneeType = form.getFieldValue(
                                    `${item.key}-assigneeType`
                                  );
                                  const blockGuid = form.getFieldValue(`${item.key}-blockGuid`);
                                  return (
                                    <Form.Item
                                      style={{ marginBottom: '12px' }}
                                      label={
                                        <div style={{ width: '70px' }}>
                                          <div
                                            style={{
                                              display: 'inline-block',
                                              color: '#ff4d4f',
                                              marginRight: '4px',
                                              fontSize: '14px',
                                              fontFamily: 'SimSun, sans-serif',
                                              lineHeight: 1,
                                            }}
                                          >
                                            *
                                          </div>
                                          对接人
                                        </div>
                                      }
                                      name={`${item.key}-assigneeList`}
                                      rules={[{ required: true, message: '请选择对接人' }]}
                                      required={false}
                                    >
                                      <UserSelect
                                        mode="multiple"
                                        // maxTagCount={1}
                                        blockGuid={blockGuid?.[1]}
                                        userState="in-service"
                                        onChange={(v, o) => {
                                          setState(prevState => {
                                            const _index = prevState.findIndex(
                                              i => i.key === item.key
                                            );
                                            if (_index === -1) {
                                              return prevState;
                                            }
                                            const updatedState = [...prevState];
                                            const currentAssigneeList =
                                              updatedState[_index].assigneeList || [];

                                            // 生成新的 assigneeList，同时保留已存在的 userType
                                            const newAssigneeList = v.map(i => {
                                              const existingAssignee = currentAssigneeList.find(
                                                a => a.id === i.value
                                              );
                                              return {
                                                id: i.value,
                                                userName: i.name,
                                                userType: existingAssignee
                                                  ? existingAssignee.userType
                                                  : 0,
                                              };
                                            });

                                            // 更新 assigneeList
                                            updatedState[_index] = {
                                              ...updatedState[_index],
                                              assigneeList: newAssigneeList,
                                            };

                                            // 清理 defaultAssignee 中不存在于 assigneeList 的值
                                            const currentDefaultAssignees =
                                              form.getFieldValue(`${item.key}-defaultAssignee`) ||
                                              [];
                                            const validDefaultAssignees =
                                              currentDefaultAssignees.filter(id =>
                                                newAssigneeList.some(assignee => assignee.id === id)
                                              );
                                            form.setFieldsValue({
                                              [`${item.key}-defaultAssignee`]:
                                                validDefaultAssignees,
                                            });
                                            return updatedState;
                                          });
                                          setDefaultAssigneeOptions({
                                            ...defaultAssigneeOptions,
                                            [item.key]: o?.map(i => ({
                                              label: i.title,
                                              value: i.value,
                                            })),
                                          });
                                        }}
                                      />
                                    </Form.Item>
                                  );
                                }}
                              </Form.Item>
                            </Col>
                          </Row>
                        </div>
                        <div
                          style={{
                            display: 'grid',
                            position: 'relative',
                            gridTemplateColumns: 'auto 30px',
                          }}
                        >
                          <Row gutter={[24, 12]}>
                            {/* 默认对接人 */}
                            <Col span={24}>
                              <Form.Item noStyle shouldUpdate>
                                {() => {
                                  const customerNo = form.getFieldValue(`${item.key}-customerNo`);
                                  return (
                                    <Form.Item
                                      style={{ marginBottom: '12px' }}
                                      label={<div style={{ width: '70px' }}>默认对接人</div>}
                                      name={`${item.key}-defaultAssignee`}
                                      rules={[
                                        {
                                          required:
                                            typeof customerNo === 'string'
                                              ? customerNo !== 'SYSTEM'
                                              : customerNo?.value !== 'SYSTEM',
                                          message: '请选择默认对接人',
                                        },
                                      ]}
                                    >
                                      <Select
                                        mode="multiple"
                                        options={defaultAssigneeOptions[item.key] ?? []}
                                        onChange={v => {
                                          setState(prevState => {
                                            const _index = prevState.findIndex(
                                              i => i.key === item.key
                                            );
                                            if (_index === -1) {
                                              return prevState;
                                            }

                                            const updatedState = [...prevState];
                                            const currentAssigneeList =
                                              updatedState[_index].assigneeList || [];

                                            // 更新 assigneeList 的 userType
                                            const updatedAssigneeList = currentAssigneeList.map(
                                              assignee => ({
                                                ...assignee,
                                                userType: v.includes(assignee.id) ? 1 : 0,
                                              })
                                            );

                                            updatedState[_index] = {
                                              ...updatedState[_index],
                                              assigneeList: updatedAssigneeList,
                                            };

                                            return updatedState;
                                          });
                                        }}
                                      />
                                    </Form.Item>
                                  );
                                }}
                              </Form.Item>
                            </Col>
                          </Row>
                        </div>
                      </>
                    )}
                  </div>
                  {index + 1 === state.length && (
                    <Row gutter={24}>
                      <Col span={24}>
                        <Button
                          type="dashed"
                          block
                          onClick={() => {
                            const currMax = Math.max(...state.map(i => i.key));
                            setState([
                              ...state,
                              {
                                key: currMax + 1 + Date.now(),
                                customerNo: undefined,
                                assigneeType: undefined,
                                blockGuid: undefined,
                                assigneeList: [{ id: undefined, userName: undefined }],
                              },
                            ]);
                          }}
                        >
                          + 添加
                        </Button>
                      </Col>
                    </Row>
                  )}
                </>
              );
            })}
          </Form>
        </div>
      </Card>

      <FooterToolBar>
        <Space style={{ width: '100%', justifyContent: 'center' }}>
          <Button loading={confirmedLoading} type="primary" block onClick={debounce(onSubmit, 300)}>
            确认
          </Button>
          <Button
            onClick={() => {
              history.goBack();
            }}
          >
            取消
          </Button>
        </Space>
      </FooterToolBar>
    </>
  );
}
