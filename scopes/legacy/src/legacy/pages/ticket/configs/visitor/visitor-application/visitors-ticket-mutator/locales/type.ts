export type VisitorsTicketMutatorLocales = {
  visitorRequest: string;
  basicTitle: string;
  visitType: string;
  relatedTicketType: string;
  relatedTicketNumber: string;
  title: string;
  purpose: string;
  allowedTimeRange: string;
  allowedStartTime: string;
  allowedEndTime: string;
  startGTCurrent: string;
  endGtStart: string;
  limitDays: string;
  allowedTimeRangeLimitDates: string;
  protectionDateTip: string;
  idc: string;
  authorizedArea: string;
  receptionist: string;
  authorizedNotify: {
    title: string;
    tooltip: string;
    enum: {
      email: string;
      disabled: string;
    };
  };
  insideVisitorNotices: string;
  outSideVisitorNotices: string;
  email: string;
  emailPlaceholder: string;
  emailFormatError: string;
  location: string;
  fileInfoList: {
    label: string;
    tooltip: string;
    upload: string;
  };
  userInfos: {
    title: string;
    infoTitle: string;
  };
  message: {
    addUserEmptyError: string;
    userCheckError: string;
    noticeUserMustRequiredError: string;
    success: string;
    timeFirst: string;
    idcFirst: string;
  };
  button: {
    submit: string;
    cancel: string;
    addEmail: string;
    addUser: string;
    editUser: string;
    edit: string;
    delete: string;
    reset: string;
    confirm: string;
    checkAll: string;
  };
  input: {
    maxLen: string;
    required: string;
  };
  select: {
    required: string;
  };
};
