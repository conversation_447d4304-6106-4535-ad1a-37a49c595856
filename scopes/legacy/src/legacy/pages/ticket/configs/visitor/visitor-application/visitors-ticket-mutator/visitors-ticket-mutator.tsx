import { DeleteOutlined, QuestionCircleOutlined, UploadOutlined } from '@ant-design/icons';
import { i18n } from '@teammc/i18n';
import debounce from 'lodash.debounce';
import uniq from 'lodash.uniq';
import moment from 'moment';
import type { Moment } from 'moment';
import { nanoid } from 'nanoid';
import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useHistory } from 'react-router-dom';
import { useLatest } from 'react-use';

import { getUserInfo } from '@manyun/auth-hub.cache.user';
import { AutoComplete } from '@manyun/base-ui.ui.auto-complete';
import { Button } from '@manyun/base-ui.ui.button';
import { Cascader } from '@manyun/base-ui.ui.cascader';
import type { CertificateType } from '@manyun/base-ui.ui.certificate-type-select';
import { Container } from '@manyun/base-ui.ui.container';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import type { EditableFormInstance } from '@manyun/base-ui.ui.editable-pro-table';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Form } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import { fetchPagedVendors } from '@manyun/crm.service.fetch-paged-vendors';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import type { McUploadFileJSON } from '@manyun/dc-brain.model.mc-upload-file';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { McUpload as Upload } from '@manyun/dc-brain.ui.upload';
import { RolesSelect } from '@manyun/iam.ui.roles-select';
import { useMetadata, useMetadataTree } from '@manyun/resource-hub.gql.client.metadata';
import type { MetadataTree } from '@manyun/resource-hub.gql.client.metadata';
import { useSpaces } from '@manyun/resource-hub.gql.client.resources';
import { fetchRoomList } from '@manyun/resource-hub.service.fetch-room-list';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';
import { MetaTypeCascader } from '@manyun/resource-hub.ui.meta-type-cascader';
import {
  useLazyExistingProtectionDate,
  useLazyVisitTicket,
  useVisitTicketMutation,
} from '@manyun/sentry.gql.client.visits';
import type { VisitorNotifyInfoInput } from '@manyun/sentry.gql.client.visits';
import { businessClassificationMappingQuery } from '@manyun/sentry.service.business-classification-mapping-query';
import { depositaryDraftEntry } from '@manyun/sentry.services.depositary-draft-entry';
import type { Visitor } from '@manyun/sentry.ui.visitors-editable-table';
import {
  useUpdateApproveAreaMutation,
  useUpdateAssigneeConfigMutation,
  useUpdateOrderAssigneeMutation,
} from '@manyun/ticket.gql.client.tickets';
import { TicketTypesCascader } from '@manyun/ticket.ui.ticket-types-cascader';

import { VisitorsEditableTable } from '../visitors-editable-table';
import { PersonnelAccessRequestsContent } from './components/personnel-access-requests';
import { VisitorEmailFormList } from './components/visitor-email-form-list.js';
import {
  LOCALE_SCOPE_NAME,
  LOCALE_UNIQ_KEY,
  generateFiledKey,
  zhCN as visitorsTicketMutatorZhCN,
} from './locales/index.js';
import { VisitorsTicketProvider, useVisitorsTicketContext } from './visitors-ticket-context';

export const formItem = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
};

export const formItemLayoutWithOutLabel = {
  wrapperCol: {
    span: 20,
    offset: 4,
  },
};

const keys = [
  'visitType',
  'relatedTicketType',
  'relatedTicketNumber',
  'title',
  'purpose',
  'allowedTimeRange',
  'idc',
  'authorizedArea',
  'assignees',
  'receptionist',
  'authorizedNotify',
  'insideVisitorNotices',
  'fileInfoList',
] as const;
export type FieldName = (typeof keys)[number];

export type AuthorizedNotify = 'email' | 'disabled';

export type LabelInValue = {
  id?: string | null;
  value: string | null;
  label: string;
};
export type ReceptionistType = 'roles' | 'users';

export type FormValues = {
  creator?: {
    id: number;
    name: string;
  };

  visitType?: string[];
  relatedTicketType?: string[];
  relatedTicketNumber?: string;
  title?: string;
  purpose?: string;
  allowedTimeRange?: [Moment, Moment];
  idc?: string[];
  authorizedArea?: {
    spaceGuids: string[] | string[][];
    allSpaceGuids: string[];
  };
  /**
   * 指派人类型
   */
  receptionistType?: ReceptionistType;
  /**
   * 指派人(多选)/指派角色(单选)
   */
  receptionist?: LabelInValue[] | LabelInValue;
  visitors?: Visitor[];
  authorizedNotify?: AuthorizedNotify;
  insideVisitorNotices?: { label: string; value: string; email: string }[];
  outSideVisitorNotices?: string[];
  fileInfoList?: McUploadFileJSON[];
};

export type VisitorsTicketMutatorProps = {
  /**
   * 用于提交的唯一性，避免重复提交
   */
  formId?: string;
  formLayout?: 'horizontal' | 'vertical' | 'inline';
  renderExistingVisitTicketAsLink?: boolean;
  ticketNumber?: string;
  initialValues?: FormValues;
  visitTypeMetaType?: string[];
  accessDirectUrl?: string;
  unusedFormItems?: FieldName[];
  defaultShowAuthorizedNotify?: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  UserSelect?: React.ComponentType<any>;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  CustomerUpload?: React.ComponentType<any>;
  /**
   * 创建 / 更新 成功
   */
  onSuccess?: (ticketNumber: string) => void;
  onCancel?: () => void;
};

i18n.current.addResourceBundle('zh-CN', LOCALE_SCOPE_NAME, {
  [LOCALE_UNIQ_KEY]: visitorsTicketMutatorZhCN,
});
const formItemWidth = 404;

export function VisitorsTicketMutatorContent({
  formId,
  formLayout,
  renderExistingVisitTicketAsLink,
  ticketNumber,
  visitTypeMetaType = ['VISITOR', 'VISITOR_THIRD_CATEGORY'],
  initialValues,
  unusedFormItems,
  UserSelect,
  CustomerUpload,
  accessDirectUrl,
  defaultShowAuthorizedNotify = true,
  onSuccess,
  onCancel,
}: VisitorsTicketMutatorProps) {
  const history = useHistory();
  const { t } = useTranslation(LOCALE_SCOPE_NAME);
  const isEditing = !!ticketNumber;
  const hasInitialRelatedTicketType = !!initialValues?.relatedTicketType;
  const hasInitialRelatedTicketNumber = !!initialValues?.relatedTicketNumber;
  const hasInitialIdc = !!initialValues?.idc;

  const onSuccessRef = useLatest(onSuccess);
  const [mutateVisitTicket, { loading }] = useVisitTicketMutation({
    onCompleted(data) {
      if (!data.mutateVisitTicket?.success) {
        message.error(data.mutateVisitTicket?.message);
        return;
      }
      onSuccessRef.current?.(data.mutateVisitTicket.ticketNumber!);
    },
  });
  const { name } = getUserInfo();

  const [form] = Form.useForm<FormValues>();

  const [getVisitTicket] = useLazyVisitTicket({
    onCompleted(data) {
      if (!data.visitTicket) {
        return;
      }
      const defaultAuthorizedAreas: string[][] = (data.visitTicket.authorizedArea ?? []).map(
        ({ code, type }) =>
          type === 'BLOCK' ? [code] : [code.split('.').slice(0, 2).join('.'), code]
      );
      const defaultAuthorizedNotifyCheck =
        (data.visitTicket.visitorNotifyList ?? []).length > 0 ? 'email' : 'disabled';

      let receptionistType: ReceptionistType | undefined;
      let receptionist: LabelInValue[] | LabelInValue | undefined;
      if ((data.visitTicket?.assigneeList ?? []).length > 0) {
        receptionistType = 'users';
        receptionist = (data.visitTicket.assigneeList ?? []).map(user => ({
          label: user.name,
          value: user.id.toString(),
        })) as LabelInValue[];
      } else if ((data.visitTicket?.assigneeRoles ?? []).length === 1) {
        receptionistType = 'roles';
        receptionist = {
          id: data.visitTicket.assigneeRoles![0].id,
          label: data.visitTicket.assigneeRoles![0].name,
          value: data.visitTicket.assigneeRoles![0].code,
        } as LabelInValue;
      }

      form.setFieldsValue({
        visitType:
          data.visitTicket.subType && data.visitTicket.thirdType
            ? [data.visitTicket.subType, data.visitTicket.thirdType]
            : undefined,
        relatedTicketType: data.visitTicket.relatedTicketType
          ? [data.visitTicket.relatedTicketType]
          : undefined,
        relatedTicketNumber: data.visitTicket.relatedTicketNumber ?? undefined,
        title: data.visitTicket.title,
        purpose: data.visitTicket.purpose?.trim(),
        allowedTimeRange: [
          moment(data.visitTicket.allowedTimeRage[0]),
          moment(data.visitTicket.allowedTimeRage[1]),
        ],
        idc: [data.visitTicket.idc],
        authorizedArea: data.visitTicket.authorizedArea
          ? {
              spaceGuids: defaultAuthorizedAreas,
              allSpaceGuids: data.visitTicket.authorizedArea.map(({ code }) => code),
            }
          : undefined,
        receptionistType,
        receptionist,
        // @ts-ignore
        visitors: (data.visitTicket.visitors ?? []).map(
          ({
            type,
            identificationType,
            ICN,
            LPN,
            companyName,
            operationalAuthorization,
            mobileAreaCode,
            mobile,
            position,
            personalGoods,
            ...restVsitor
          }) => ({
            ...restVsitor,
            mobile: [mobileAreaCode, mobile],
            visitorType: type,
            id: nanoid(6),
            position: position ?? undefined,
            personalGoods: personalGoods ?? undefined,
            LPN: LPN ?? undefined,
            companyName: companyName ?? undefined,
            operationalAuthorization: operationalAuthorization ?? undefined,
            identification: [identificationType as CertificateType, ICN ?? undefined],
          })
        ),
        authorizedNotify: defaultAuthorizedNotifyCheck,
        insideVisitorNotices: data.visitTicket.visitorNotifyList
          ?.filter(notify => notify.userType === 'SYSTEM')
          .map(notify => ({
            id: notify.userId,
            name: notify.userName,
            email: notify.email,
            label: notify.userName as string,
            value: notify.userId,
          })),
        outSideVisitorNotices: data.visitTicket.visitorNotifyList
          ?.filter(notify => notify.userType === 'OUTSIDE')
          .map(notify => notify.email),
        // @ts-ignore ts(2322)
        fileInfoList: data.visitTicket.fileInfoList,
      });
    },
  });
  // 获取重保日
  const [checkExistingProtectionDate, { data: protectionDate }] = useLazyExistingProtectionDate();
  // 获取违禁品元数据配置
  const { data } = useMetadata({ variables: { type: 'CONTRABAND' }, fetchPolicy: 'network-only' });
  // 获取授权时间元数据配置，限制时间跨度
  const { data: visitConfig } = useMetadata({ variables: { type: 'VISITOR_TIME' } });
  const { data: visitTypesData } = useMetadataTree({
    variables: { type: visitTypeMetaType.join(',') },
    onCompleted: () => {
      if (!ticketNumber) {
        form.setFieldValue('visitType', ['visitor', 'visitor']);
      }
    },
  });

  const idc = Form.useWatch('idc', form);
  const authorizedArea = Form.useWatch('authorizedArea', form);

  const allowedTimeRange = Form.useWatch('allowedTimeRange', form);
  const insideVisitorNotices = Form.useWatch('insideVisitorNotices', form);
  const outSideVisitorNotices = Form.useWatch('outSideVisitorNotices', form);
  const receptionistType = Form.useWatch('receptionistType', form);
  const authorizedNotify = Form.useWatch('authorizedNotify', form);
  const watchVisitType = Form.useWatch('visitType', form);
  const isValidRef = React.useRef<Record<string, boolean>>({});
  const editableFormRef = React.useRef<EditableFormInstance<Visitor>>();

  const contrabandItems = React.useMemo(
    () => (data?.metadata ?? []).map(item => item.name),
    [data?.metadata]
  );
  const timeLimitDays = React.useMemo(() => {
    const timeConfig = visitConfig?.metadata?.find(record => record.code === idc?.[0]);
    return timeConfig ? Number(timeConfig.name) : null;
  }, [idc, visitConfig?.metadata]);

  // 提交
  const onSubmit = async (mode?: string) => {
    let values: FormValues = {};
    try {
      if (mode === 'drafts') {
        values = form.getFieldsValue();
        if (!values?.visitType?.[1]) {
          message.error('请填写业务分类');
          return;
        }
        if (!values?.title) {
          message.error('请填写工单标题');
          return;
        }
        if (!values?.idc?.[0]) {
          message.error('请填写机房');
          return;
        }
      } else {
        const [formValue] = await Promise.all([
          form.validateFields(),
          editableFormRef.current?.validateFields(),
        ]);
        values = formValue;
      }

      const { visitors } = values;
      visitors?.forEach(visitor => {
        if (
          visitor.personalGoods &&
          visitor.personalGoods.some(good =>
            contrabandItems.some(contrabandItem => {
              const pattern = new RegExp(contrabandItem);
              return pattern.test(good);
            })
          )
        ) {
          throw new Error(`Visitor(${visitor.id}) is not valid!`);
        }
      });

      let visitorNotices: VisitorNotifyInfoInput[] = [];
      const { authorizedNotify, insideVisitorNotices = [], outSideVisitorNotices = [] } = values;

      if (authorizedNotify === 'email') {
        if (!(mode === 'drafts') && !insideVisitorNotices.length && !outSideVisitorNotices.length) {
          message.error(
            t(generateFiledKey('message.noticeUserMustRequiredError'), {
              defaultValue: '内部人员或外部人员必填一项！',
            })
          );
          throw new Error(`authorizedNotify is not valid!`);
        }
        visitorNotices = (
          [
            ...insideVisitorNotices.map(item => ({
              type: 'internal',
              email: item?.email,
              userId: item?.value,
              userName: item?.label,
            })),
            ...outSideVisitorNotices.map(email => ({
              type: 'external',
              email,
            })),
          ] as VisitorNotifyInfoInput[]
        ).filter(i => i.email);
      } else {
        visitorNotices = [];
      }
      const filterVisitors =
        values.visitors
          ?.map(visitor => ({
            identificationType: visitor.identification![0]!,
            ICN: visitor.identification?.[1],
            LPN: visitor.LPN,
            companyName: visitor.companyName,
            mobileAreaCode: visitor.mobile[0]!,
            mobile: visitor.mobile[1]!,
            name: visitor.name!,
            operationalAuthorization: visitor.operationalAuthorization,
            type: visitor.visitorType!,
            position: visitor.position,
            personalGoods: visitor.personalGoods,
          }))
          .filter(visitor => {
            return [
              visitor.ICN,
              visitor.LPN,
              visitor.companyName,
              visitor.mobile,
              visitor.name,
              visitor.type,
              visitor.position,
            ].some(val => val != null);
          }) || [];
      const param = {
        variables: {
          formId,
          allowedTimeRange: [
            values.allowedTimeRange?.[0].startOf('day').valueOf(),
            values.allowedTimeRange?.[1].endOf('day').startOf('second').valueOf(),
          ],
          idc: values.idc![0],
          authorizedArea: values.authorizedArea?.allSpaceGuids || [],
          purpose: values.purpose,
          relatedTicketNumber: values.relatedTicketNumber,
          relatedTicketType: values.relatedTicketType?.[0],
          ticketNumber,
          title:
            values.title ??
            // eslint-disable-next-line @typescript-eslint/no-use-before-define
            generateDefaultTitle(t, values, visitTypesData?.metadataTree ?? [], initialValues),
          visitType: values.visitType![0],
          visitSecondaryType: values.visitType![1],
          visitors: filterVisitors,
          visitorNotices,
          fileIdList: values.fileInfoList
            ? values.fileInfoList.map(file => file.id ?? Number(file.patialPath))
            : undefined,
          fileInfoList: values.fileInfoList
            ? values.fileInfoList.map(({ uploadUser, ...file }) => ({
                ...file,
                uploadUser:
                  !uploadUser.id || !uploadUser.name
                    ? {
                        id: 0,
                        name: 'SYSTEM',
                      }
                    : uploadUser,
              }))
            : undefined,
          assigneeUsers:
            values.receptionistType === 'users' &&
            Array.isArray(values.receptionist) &&
            values.receptionist.length > 0
              ? values.receptionist.map(user => ({
                  id: user.value,
                  userName: user.label,
                }))
              : undefined,
          assigneeRoles:
            values.receptionistType === 'roles' &&
            !Array.isArray(values.receptionist) &&
            values.receptionist
              ? [
                  {
                    id: values.receptionist.id!,
                    roleCode: values.receptionist.value!,
                    roleName: values.receptionist.label!,
                  },
                ]
              : undefined,
        },
      };
      if (mode === 'drafts') {
        return Promise.resolve(param.variables);
      }
      mutateVisitTicket(param);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log(error);
    }
  };
  // 保存草稿
  const savingDrafts = async () => {
    const svcQuery = await onSubmit('drafts');
    if (svcQuery) {
      const params = {
        formId: svcQuery.formId,
        taskNo: svcQuery.ticketNumber,
        approveEndTime: svcQuery.allowedTimeRange[1],
        approveStartTime: svcQuery.allowedTimeRange[0],
        enterReason: svcQuery.purpose,
        idcTag: svcQuery.idc,
        approveArea: svcQuery.authorizedArea.join(','),
        relateTaskNo: svcQuery.relatedTicketNumber,
        relateTaskType: svcQuery.relatedTicketType,
        taskSubType: svcQuery.visitType,
        reasonType: svcQuery.visitSecondaryType,
        taskTitle: svcQuery.title,
        visitorInfos: svcQuery.visitors.map(visitor => ({
          certificateType: visitor.identificationType,
          companyName: visitor.companyName,
          phoneCountryCode: visitor.mobileAreaCode,
          contactWay: visitor.mobile,
          identityNo: visitor.ICN,
          name: visitor.name,
          operable: visitor.operationalAuthorization === 'yes',
          plateNo: visitor.LPN,
          visitorType: visitor.type,
          position: visitor.position,
          personalItem: visitor.personalGoods?.join(','),
        })),
        visitorNotifyList: svcQuery.visitorNotices?.map(notice => ({
          email: notice.email,
          userType: notice.type === 'external' ? 'OUTSIDE' : 'SYSTEM',
          userId: notice.userId,
          userName: notice.userName,
        })),
        fileInfoList: svcQuery.fileInfoList?.map(file => McUploadFile.fromJSON(file).toApiObject()),
        fileIdList: svcQuery.fileIdList,
        assigneeList: svcQuery.assigneeUsers,
        assigneeRoleList: svcQuery.assigneeRoles,
      };
      // @ts-ignore  enterReason: svcQuery.purpose 非必填
      const { data, error } = await depositaryDraftEntry(params);

      if (error) {
        return;
      }
      if (data) {
        //   savingTaskNo.current = data;
        message.success('保存草稿成功');
        history.push('/page/tickets/visitor');
      }
    }
  };
  React.useEffect(() => {
    if (ticketNumber) {
      getVisitTicket({ variables: { ticketNumber, maskedIdNumber: false } });
    }
  }, [getVisitTicket, ticketNumber]);

  return (
    <VisitorsTicketProvider
      value={{
        form,
        ticketNumber,
        idc,
        visitType: watchVisitType,
        isEditing,
      }}
    >
      <Form<FormValues>
        layout={formLayout}
        colon={false}
        labelCol={formLayout === 'vertical' ? undefined : formItem.labelCol}
        wrapperCol={formLayout === 'vertical' ? undefined : formItem.wrapperCol}
        form={form}
        initialValues={initialValues}
      >
        <Space style={{ display: 'flex' }} direction="vertical">
          <Container size="large">
            <Typography.Title showBadge level={5}>
              {t(generateFiledKey('basicTitle'))}
            </Typography.Title>
            <div style={{ width: 636 }}>
              {!unusedFormItems?.includes('visitType') && (
                <Form.Item
                  label={t(generateFiledKey('visitType'))}
                  name="visitType"
                  rules={[
                    {
                      required: true,
                      message: `${t(generateFiledKey('visitType'))}${t(
                        generateFiledKey('select.required')
                      )}`,
                    },
                    {
                      type: 'array',
                      len: 2,
                      message: `${t(generateFiledKey('visitType'))}${t(
                        generateFiledKey('select.required')
                      )}`,
                    },
                  ]}
                >
                  <MetaTypeCascader
                    style={{ width: formItemWidth }}
                    metaType={visitTypeMetaType}
                    trigger="onDidMount"
                  />
                </Form.Item>
              )}
              {!unusedFormItems?.includes('relatedTicketType') && (
                <Form.Item
                  label={t(generateFiledKey('relatedTicketType'))}
                  name="relatedTicketType"
                  rules={[{ required: false }]}
                >
                  <TicketTypesCascader
                    style={{ width: formItemWidth }}
                    allowClear
                    showRootNodesOnly
                    trigger={hasInitialRelatedTicketType || isEditing ? 'onDidMount' : 'onFocus'}
                    disabled={hasInitialRelatedTicketType}
                  />
                </Form.Item>
              )}
              {!unusedFormItems?.includes('relatedTicketNumber') && (
                <Form.Item
                  label={t(generateFiledKey('relatedTicketNumber'), {
                    defaultValue: '关联单号',
                  })}
                  name="relatedTicketNumber"
                  rules={[
                    {
                      required: false,
                      whitespace: true,
                    },
                    {
                      type: 'string',
                      max: 20,
                      message: t(generateFiledKey('input.maxLen'), {
                        defaultValue: '最多输入 20 个字符！',
                        max: 20,
                      }),
                    },
                  ]}
                >
                  <Input
                    style={{ width: formItemWidth }}
                    disabled={hasInitialRelatedTicketNumber}
                  />
                </Form.Item>
              )}
              {!unusedFormItems?.includes('title') && (
                <Form.Item
                  label={t(generateFiledKey('title'), { defaultValue: '工单标题' })}
                  name="title"
                  rules={[
                    {
                      required: true,
                      whitespace: true,
                      message: `${t(generateFiledKey('title'), {
                        defaultValue: '工单标题',
                      })}${t(generateFiledKey('input.required'), {
                        defaultValue: '必填!',
                      })}`,
                    },
                    {
                      type: 'string',
                      max: 20,
                      message: t(generateFiledKey('input.maxLen'), {
                        defaultValue: '最多输入 20 个字符！',
                        max: 20,
                      }),
                    },
                  ]}
                >
                  <Input style={{ width: formItemWidth }} />
                </Form.Item>
              )}

              {!unusedFormItems?.includes('idc') && (
                <Form.Item
                  label={t(generateFiledKey('idc'), { defaultValue: '进入园区' })}
                  name="idc"
                  rules={[
                    {
                      required: true,
                      message: `${t(generateFiledKey('idc'), {
                        defaultValue: '进入园区',
                      })}${t(generateFiledKey('select.required'), {
                        defaultValue: '必选!',
                      })}`,
                    },
                  ]}
                >
                  <LocationCascader
                    style={{ width: formItemWidth }}
                    authorizedOnly
                    nodeTypes={['IDC']}
                    disabled={hasInitialIdc}
                    allowClear={false}
                    onTreeDataChange={_data => {
                      if (hasInitialIdc) {
                        return;
                      }
                      if (_data.length === 1) {
                        form.setFieldValue('idc', [_data[0].value]);
                      }
                    }}
                    onChange={nextIdc => {
                      if (idc?.[0] === nextIdc[0]) {
                        return;
                      }
                      form.setFieldValue('authorizedArea', undefined);
                      form.setFieldValue('insideVisitorNotices', undefined);
                    }}
                  />
                </Form.Item>
              )}
              {!unusedFormItems?.includes('authorizedArea') && (
                <Form.Item
                  noStyle
                  shouldUpdate={(pre, next) => {
                    return pre.idc !== next.idc;
                  }}
                >
                  {({ getFieldValue }) => {
                    const idcCodes = getFieldValue('idc');
                    return (
                      <Form.Item
                        label={t(generateFiledKey('authorizedArea'))}
                        name="authorizedArea"
                        getValueFromEvent={(spaceGuids, _spaces, allSpaceGuids) => {
                          if (!spaceGuids?.length || !allSpaceGuids.length) {
                            return undefined;
                          }
                          return { spaceGuids, allSpaceGuids };
                        }}
                        getValueProps={value => {
                          return { value: value?.spaceGuids };
                        }}
                        rules={[
                          {
                            required: true,
                            message: `${t(generateFiledKey('authorizedArea'))}${t(
                              generateFiledKey('select.required')
                            )}`,
                          },
                        ]}
                      >
                        <LocationCascader
                          style={{ width: formItemWidth }}
                          disabled={!idcCodes}
                          authorizedOnly
                          showCheckedStrategy="SHOW_PARENT"
                          nodeTypes={['BLOCK', 'ROOM']}
                          idc={idcCodes && idcCodes[0] ? idcCodes[0] : undefined}
                          multiple
                          allowClear={false}
                          maxTagCount="responsive"
                          onChange={() => {
                            form.setFieldValue('receptionist', undefined);
                          }}
                        />
                      </Form.Item>
                    );
                  }}
                </Form.Item>
              )}

              {!unusedFormItems?.includes('allowedTimeRange') && (
                <Form.Item
                  label={t(generateFiledKey('allowedTimeRange'), { defaultValue: '申请时间' })}
                  name="allowedTimeRange"
                  rules={[
                    {
                      required: true,
                      validator: (_, value) => {
                        if (!value || !value[0] || !value[1]) {
                          return Promise.reject(
                            new Error(
                              `${t(generateFiledKey('allowedTimeRange'), {
                                defaultValue: '申请时间',
                              })}${t(generateFiledKey('input.required'), {
                                defaultValue: '必填!',
                              })}`
                            )
                          );
                        }
                        if (
                          value[0] &&
                          moment().startOf('day').isAfter(moment(value[0]).startOf('day'))
                        ) {
                          return Promise.reject(
                            new Error(
                              `${t(generateFiledKey('startGTCurrent'), {
                                defaultValue: '申请需在更合理的时间区间',
                              })}`
                            )
                          );
                        }
                        if (
                          value[1] &&
                          moment().endOf('day').diff(moment(value[1]).endOf('day')) > 0
                        ) {
                          return Promise.reject(
                            new Error(
                              `${t(generateFiledKey('startGTCurrent'), {
                                defaultValue: '申请需在更合理的时间区间',
                              })}`
                            )
                          );
                        }
                        if (
                          timeLimitDays &&
                          value[0] &&
                          value[1] &&
                          moment(value[1].clone().startOf('day'))
                            .add(1, 'day')
                            .diff(value[0].clone().startOf('day'), 'day') > timeLimitDays
                        ) {
                          return Promise.reject(
                            new Error(
                              `${t(generateFiledKey('limitDays'), {
                                defaultValue: `申请开始时间与结束时间不可超过${timeLimitDays}天`,
                                day: timeLimitDays,
                              })}`
                            )
                          );
                        }
                        // if (
                        //   dockingStation &&
                        //   (moment(value[0].clone().startOf('day')).isAfter(
                        //     moment().add(13, 'day')
                        //   ) ||
                        //     moment(value[1].clone().startOf('day')).isAfter(
                        //       moment().add(13, 'day')
                        //     ))
                        // ) {
                        //   return Promise.reject(t(generateFiledKey('allowedTimeRangeLimitDates')));
                        // }
                        return Promise.resolve();
                      },
                    },
                  ]}
                  tooltip={
                    (protectionDate?.existingProtectionDate?.data ?? []).length > 0
                      ? t(generateFiledKey('protectionDateTip'))
                      : undefined
                  }
                >
                  <DatePicker.RangePicker
                    style={{ width: formItemWidth }}
                    format="YYYY-MM-DD"
                    disabledDate={current => {
                      const disableHistoryDate =
                        current && current < moment().subtract(1, 'day').endOf('day');
                      return disableHistoryDate;
                    }}
                    onChange={value => {
                      if (value && value[0] && value[1]) {
                        checkExistingProtectionDate({
                          variables: {
                            startTime: value[0].valueOf(), // 传时间戳
                            endTime: value[1].valueOf(),
                          },
                        });
                      }
                    }}
                  />
                </Form.Item>
              )}
              {!unusedFormItems?.includes('purpose') && (
                <Form.Item
                  label={t(generateFiledKey('purpose'))}
                  name="purpose"
                  rules={
                    watchVisitType?.[0] === 'other'
                      ? [
                          {
                            required: true,
                            whitespace: true,
                            message: `${t(generateFiledKey('purpose'))}${t(
                              generateFiledKey('input.required'),
                              {
                                defaultValue: '必填!',
                              }
                            )}`,
                          },
                        ]
                      : []
                  }
                >
                  <Input.TextArea style={{ width: formItemWidth }} maxLength={100} showCount />
                </Form.Item>
              )}
              {!unusedFormItems?.includes('receptionist') &&
                authorizedArea &&
                (authorizedArea?.spaceGuids ?? []).length > 0 && (
                  <Form.Item
                    label={t(generateFiledKey('receptionist'), { defaultValue: '指派接待人' })}
                  >
                    <Space>
                      <Form.Item name="receptionistType" noStyle>
                        <Select
                          style={{ width: formItemWidth }}
                          allowClear
                          options={[
                            { label: '用户指派', value: 'users' },
                            { label: '角色指派', value: 'roles' },
                          ]}
                          onChange={() => {
                            form.setFieldsValue({
                              receptionist: undefined,
                            });
                          }}
                        />
                      </Form.Item>
                      {receptionistType && UserSelect && (
                        <Form.Item
                          noStyle
                          shouldUpdate={(pre, next) => {
                            return pre.receptionistType !== next.receptionistType;
                          }}
                        >
                          {({ getFieldValue }) => {
                            const _receptionistType = getFieldValue('receptionistType');
                            return (
                              <Form.Item
                                name="receptionist"
                                noStyle
                                getValueFromEvent={(value, _option) => {
                                  if (_receptionistType === 'roles') {
                                    return typeof value === 'object'
                                      ? {
                                          ...value,
                                          id: value?.id ?? _option?.id,
                                        }
                                      : value;
                                  }
                                  return value;
                                }}
                              >
                                {_receptionistType === 'users' ? (
                                  <UserSelect
                                    style={{ width: formItemWidth }}
                                    allowClear
                                    labelInValue
                                    mode="multiple"
                                    userState="in-service"
                                    blockGuid={uniq(
                                      (authorizedArea?.allSpaceGuids ?? []).map(
                                        spaceGuid =>
                                          `${spaceGuid?.split('.')[0]}.${spaceGuid?.split('.')[1]}`
                                      )
                                    ).join(',')}
                                    maxTagCount="responsive"
                                  />
                                ) : (
                                  <RolesSelect
                                    style={{ width: formItemWidth }}
                                    resourceTypes={['IDC', 'BUILDING']}
                                    allowClear
                                    labelInValue
                                  />
                                )}
                              </Form.Item>
                            );
                          }}
                        </Form.Item>
                      )}
                    </Space>
                  </Form.Item>
                )}
              {(defaultShowAuthorizedNotify || (!defaultShowAuthorizedNotify && idc?.[0])) &&
                !unusedFormItems?.includes('authorizedNotify') && (
                  <Form.Item
                    label={t(generateFiledKey('authorizedNotify.title'), {
                      defaultValue: '授权成功通知',
                    })}
                    tooltip={t(generateFiledKey('authorizedNotify.tooltip'), {
                      defaultValue: '您在申请流程完成后会收到通知邮件，请留意邮件',
                    })}
                    name="authorizedNotify"
                  >
                    <Radio.Group
                      onChange={() => {
                        form.setFieldsValue({
                          insideVisitorNotices: [{ label: '', value: '', email: '' }],
                          outSideVisitorNotices: [''],
                        });
                      }}
                    >
                      <Radio value="email">
                        {t(generateFiledKey('authorizedNotify.enum.email'), {
                          defaultValue: '邮件通知',
                        })}
                      </Radio>
                      <Radio value="disabled">
                        {t(generateFiledKey('authorizedNotify.enum.disabled'), {
                          defaultValue: '无需通知',
                        })}
                      </Radio>
                    </Radio.Group>
                  </Form.Item>
                )}
              {!unusedFormItems?.includes('authorizedNotify') &&
                UserSelect &&
                authorizedNotify === 'email' && (
                  <>
                    {!unusedFormItems?.includes('insideVisitorNotices') && (
                      <VisitorEmailFormList
                        idc={idc?.[0]}
                        UserSelect={UserSelect}
                        name="insideVisitorNotices"
                        formLayout={formLayout}
                        canDelete={
                          (insideVisitorNotices ?? []).length > 0 &&
                          (outSideVisitorNotices ?? []).length > 0
                        }
                      />
                    )}
                    <VisitorEmailFormList
                      name="outSideVisitorNotices"
                      formLayout={formLayout}
                      canDelete={
                        (insideVisitorNotices ?? []).length > 0 &&
                        (outSideVisitorNotices ?? []).length > 0
                      }
                    />
                  </>
                )}
              {!unusedFormItems?.includes('fileInfoList') && (
                <Form.Item
                  name="fileInfoList"
                  label={t(generateFiledKey('fileInfoList.label'))}
                  wrapperCol={{ span: 16 }}
                  valuePropName="fileList"
                  getValueFromEvent={value => {
                    if (typeof value === 'object') {
                      return value.fileList
                        ? value.fileList.map((file: McUploadFileJSON) =>
                            McUploadFile.fromJSON(file).toJSON()
                          )
                        : undefined;
                    }
                    return value;
                  }}
                >
                  {CustomerUpload ? (
                    <CustomerUpload />
                  ) : (
                    <Upload
                      accept=".jpg,.jpeg,.png,.doc, .docx, .xls, .xlsx"
                      showUploadList
                      allowDelete
                      maxFileSize={20}
                      maxCount={10}
                    >
                      <Space style={{ display: 'flex' }} direction="vertical">
                        <Button icon={<UploadOutlined />}>
                          {t(generateFiledKey('fileInfoList.upload'))}
                        </Button>
                        <Typography.Text type="secondary">
                          {t(generateFiledKey('fileInfoList.tooltip'))}：.jpg,.jpeg,.png,.doc,
                          .docx, .xls, .xlsx
                        </Typography.Text>
                      </Space>
                    </Upload>
                  )}
                </Form.Item>
              )}
            </div>
          </Container>
          <Container style={{ marginBottom: 66 }} size="large">
            <Typography.Title showBadge level={5}>
              {t(generateFiledKey('userInfos.title'), { defaultValue: '人员信息' })}
            </Typography.Title>
            <Form.Item
              wrapperCol={{ span: 24 }}
              name="visitors"
              rules={[
                {
                  message: t(generateFiledKey('message.addUserEmptyError'), {
                    defaultValue: '至少添加一个人员',
                  }),
                  type: 'number',
                  min: 1,
                  transform(value?: Visitor[]) {
                    return value?.length ?? 0;
                  },
                },
              ]}
            >
              <VisitorsEditableTable
                contrabandItems={contrabandItems}
                renderExistingVisitTicketAsLink={renderExistingVisitTicketAsLink}
                idc={idc?.[0]}
                authorizedArea={authorizedArea?.allSpaceGuids ?? []}
                allowedTimeRange={allowedTimeRange?.map(time => time.valueOf()) as [number, number]}
                isValidRef={isValidRef}
                editableFormRef={editableFormRef}
                accessDirectUrl={accessDirectUrl}
              />
            </Form.Item>
          </Container>
        </Space>
        <FooterToolBar>
          <Space>
            <Button
              type="primary"
              loading={loading}
              onClick={() => {
                onSubmit();
              }}
            >
              {t(generateFiledKey('button.submit'), { defaultValue: '提交' })}
            </Button>
            {<Button onClick={savingDrafts}>保存草稿</Button>}
            {onCancel && (
              <Button onClick={onCancel}>
                {t(generateFiledKey('button.cancel'), { defaultValue: '取消' })}
              </Button>
            )}
          </Space>
        </FooterToolBar>
      </Form>
    </VisitorsTicketProvider>
  );
}
export function VisitorsTicketMutator(props: VisitorsTicketMutatorProps) {
  const [configUtil] = useConfigUtil();
  const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket');
  const {
    dockingStation,
  } = // @ts-ignore
    ticketScopeCommonConfigs?.entryOfPersons?.features ?? {
      dockingStation: false,
    };
  return dockingStation ? (
    <PersonnelAccessRequestsContent {...props} />
  ) : (
    <VisitorsTicketMutatorContent {...props} />
  );
}
function generateDefaultTitle(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  t: any,
  values: FormValues,
  visitTypes: MetadataTree[] | undefined,
  initialValues: FormValues | undefined
) {
  const creatorUserName = values.creator?.name ?? initialValues?.creator?.name ?? '';
  const types = values.visitType;
  if (!types) {
    return '';
  }
  const visitTypeFirstNode = visitTypes?.find(visitType => visitType.code === types[0]);

  return `${t(generateFiledKey('userInfos.infoTitle'), {
    userName: creatorUserName,
    idc: values.idc,
    visitType: visitTypeFirstNode?.name ?? '',
    defaultValue: `${creatorUserName}申请 ${values.idc} 来访${visitTypeFirstNode?.name ?? ''}人员`,
  })}`;
}

export const FuzzySearchInput = (props: any) => {
  const { value, onChange, onSelect, placeholder, style, ...restProps } = props;

  // 存储搜索结果
  const [options, setOptions] = React.useState<{ value: string; label: string }[]>([]);
  // 存储加载状态
  const [loading, setLoading] = React.useState(false);
  // 创建一个节流的搜索函数
  const debouncedFetchRef = React.useRef(
    debounce(async (searchValue: string) => {
      if (!searchValue) {
        setOptions([]);
        return;
      }

      setLoading(true);
      try {
        const { data } = await fetchPagedVendors({
          pageNum: 1,
          pageSize: 6,
          vendorName: searchValue,
        });

        const newOptions = data?.data?.map(item => ({
          value: item.vendorName,
          label: item.vendorName,
        }));
        setOptions(newOptions || []);
      } catch (err) {
        console.error('搜索出错:', err);
      } finally {
        setLoading(false);
      }
    }, 500)
  );

  // 处理输入变化
  const handleChange = (value: string) => {
    onChange?.(value);
    debouncedFetchRef.current(value);
  };
  // 组件卸载时取消未完成的节流函数调用
  useEffect(() => {
    return () => {
      debouncedFetchRef.current.cancel();
    };
  }, []);

  return (
    <AutoComplete
      value={value}
      options={options}
      onChange={handleChange}
      onSelect={onSelect}
      placeholder={placeholder || '请输入关键词搜索'}
      style={style}
      notFoundContent={loading ? '加载中...' : '无匹配结果'}
      {...restProps}
    />
  );
};
