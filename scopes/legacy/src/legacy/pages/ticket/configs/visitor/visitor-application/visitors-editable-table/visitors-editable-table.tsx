import { i18n } from '@teammc/i18n';
import dayjs from 'dayjs';
import uniq from 'lodash.uniq';
import uniqby from 'lodash.uniqby';
import { nanoid } from 'nanoid';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useLatest, useShallowCompareEffect } from 'react-use';

import { Alert } from '@manyun/base-ui.ui.alert';
import { isIdCard, isPassport } from '@manyun/base-ui.ui.certificate-type-select';
import type { CertificateType } from '@manyun/base-ui.ui.certificate-type-select';
import { EditableProTable } from '@manyun/base-ui.ui.editable-pro-table';
import type { EditableFormInstance } from '@manyun/base-ui.ui.editable-pro-table';
import type { FormInstance } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Radio } from '@manyun/base-ui.ui.radio';
import type { DefaultOptionType } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { checkExistingVisitsRecords } from '@manyun/sentry.service.check-existing-visits-records';
import type {
  BackendExistingVisitRecord,
  SvcQuery as CheckExistingVisitsRecordsArgs,
  ExistingVisitRecord,
} from '@manyun/sentry.service.check-existing-visits-records';
import { fetchVisitorInfos } from '@manyun/sentry.service.fetch-visitor-infos';
import { fetchVisitorTypes } from '@manyun/sentry.service.fetch-visitor-types';
import { VisitorTypesSelect } from '@manyun/sentry.ui.visitor-types-select';

import { FuzzySearchInput } from '../visitors-ticket-mutator/visitors-ticket-mutator';
import { Identification } from './components/identification/index.js';
import type { Value as IdentificationValue } from './components/identification/index.js';
import { ImportVisitorsFromFile } from './components/import-visitors-from-file/index.js';
import { MobileField } from './components/mobile-field/index.js';
import { PersonalGoodItems } from './components/personal-good-items/index.js';
import {
  LOCALE_SCOPE_NAME,
  LOCALE_UNIQ_KEY,
  generateFiledKey,
  zhCN as visitorsEditableTableZhCN,
} from './locales/index.js';
import { mapIdentificationType } from './utils/map-identification-type.js';
import { mapVisitorType } from './utils/map-visitor-type.js';

export type Visitor = {
  id: string;
  mobile: [string | undefined, string | undefined];
  name?: string;
  visitorType?: string;
  identification?: IdentificationValue;
  /** License Plate Number */
  LPN?: string;
  position?: string;
  companyName?: string;
  personalGoods?: string[];
  operationalAuthorization?: 'yes' | 'no';
};

export type VisitorsEditableTableProps = {
  renderExistingVisitTicketAsLink?: boolean;
  idc?: string;
  authorizedArea?: string[];

  /**
   * 授权时间段
   */
  allowedTimeRange?: [number, number];
  /**
   * 用于缓存每一行数据是否有效
   */
  isValidRef: React.MutableRefObject<Record<string, boolean>>;
  editableFormRef?: React.MutableRefObject<EditableFormInstance<Visitor> | undefined>;
  /**
   * @defaultValue `true`
   */
  allowAddRow?: boolean;
  value?: Visitor[];
  /**出入门工单链接 */
  accessDirectUrl?: string;
  /**违禁品 */
  contrabandItems: string[];
  onChange?: (value?: Visitor[]) => void;
  /**是否是政府或商务业务类型 */
  isSpecialtyVisitType?: boolean;
  /**是否是政府 业务类型 */
  isGovernments?: boolean;
  visitTypeTag?: {
    [key: string]: string;
  };
};

i18n.current.addResourceBundle('zh-CN', LOCALE_SCOPE_NAME, {
  [LOCALE_UNIQ_KEY]: visitorsEditableTableZhCN,
});

export function VisitorsEditableTable({
  renderExistingVisitTicketAsLink = true,
  idc,
  authorizedArea,
  allowedTimeRange,
  isValidRef,
  editableFormRef,
  allowAddRow = !!idc?.length && !!allowedTimeRange?.length && !!authorizedArea?.length,
  contrabandItems,
  value,
  onChange,
  accessDirectUrl,
  isSpecialtyVisitType,
  visitTypeTag,
  isGovernments,
}: VisitorsEditableTableProps) {
  const { t } = useTranslation(LOCALE_SCOPE_NAME);
  const [configUtil] = useConfigUtil();
  const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket');
  const {
    dockingStation = false,
    position = '职位',
    hiddenOperationalAuthorization = false,
    hiddenPersonalGoods = false,
  } = ticketScopeCommonConfigs?.entryOfPersons?.features ?? {
    dockingStation: false,
    position: '职位',
    hiddenOperationalAuthorization: false,
    hiddenPersonalGoods: false,
  };
  allowAddRow = dockingStation
    ? dockingStation
    : !!idc?.length && !!allowedTimeRange?.length && !!authorizedArea?.length;

  const [existingVisitsRecords, setExistingVisitsRecords] = React.useState<
    (ExistingVisitRecord & { rowKey: string })[]
  >([]);
  // 应用 导出人员类型 逻辑判断
  const [visitorTypeOptions, setVisitorTypeOptions] = React.useState<DefaultOptionType[]>([]);

  const validateRow = React.useCallback(
    async (args: CheckExistingVisitsRecordsArgs, rowKey: string) => {
      const { error, data } = await checkExistingVisitsRecords(args);
      if (error) {
        if (error.code === 'VISITOR_STAFF_IN_IDC') {
          isValidRef.current[rowKey] = false;
        }
        message.error(error.message);
        return null;
      }
      if (!data) {
        isValidRef.current[rowKey] = true;
        return null;
      }
      isValidRef.current[rowKey] = false;
      return { ...data, rowKey };
    },
    [isValidRef]
  );
  const allowedTimeRangeRef = useLatest(allowedTimeRange);

  const getExistingVisitsRecords = React.useCallback(
    async (form: FormInstance<Record<string, Visitor>>, rowKey: string) => {
      if (!idc || allowedTimeRangeRef.current?.length !== 2) {
        throw new Error(`Expected required props: "idc" and "allowedTimeRange".`);
      }
      const { name, mobile, identification }: Visitor = form.getFieldValue(rowKey);
      if (!name || !mobile || !mobile[1] || !authorizedArea || !authorizedArea.length) {
        return;
      }
      const existingRecord = await validateRow(
        {
          idc,
          authorizedAreas: authorizedArea,
          allowedTimeRange: allowedTimeRangeRef.current,
          name,
          mobile: mobile[1]!,
          ICN: identification?.[1],
        },
        rowKey
      );

      setExistingVisitsRecords(pre => {
        if (!existingRecord) {
          return pre.filter(item => item.rowKey !== rowKey);
        }
        const existingItem = pre.find(item => item.rowKey === rowKey);
        if (existingItem) {
          return uniqby(
            pre.map(item => (item.rowKey === rowKey ? { ...item, ...existingRecord } : item)),
            item => `${item.name}_$$_${item.mobile[1]}`
          );
        } else {
          // 否则添加新的项
          return uniqby([existingRecord, ...pre], item => `${item.name}_$$_${item.mobile[1]}`);
        }
      });
    },
    [idc, allowedTimeRangeRef, authorizedArea, validateRow]
  );

  const datasourceRef = useLatest(value);
  useShallowCompareEffect(() => {
    if (
      !datasourceRef.current ||
      !idc ||
      allowedTimeRange?.length !== 2 ||
      !authorizedArea ||
      !authorizedArea.length
    ) {
      return;
    }
    const rows = datasourceRef.current;
    if (
      !rows ||
      !rows.length ||
      rows.some(row => !row.name || !row.mobile?.[1] || !row.identification?.[1])
    ) {
      return;
    }
    (async () => {
      const results = await Promise.all(
        rows.map(({ id, name, mobile, identification }) => {
          if (!mobile || !authorizedArea || !authorizedArea.length) {
            return Promise.resolve(null);
          }
          return validateRow(
            {
              idc,
              authorizedAreas: authorizedArea,
              allowedTimeRange,
              name,
              mobile: mobile[1],
              ICN: identification?.[1],
            },
            id
          );
        })
      ).then(_results =>
        _results.filter(
          (existingRecord): existingRecord is ExistingVisitRecord & { rowKey: string } =>
            !!existingRecord
        )
      );

      setExistingVisitsRecords(results);
    })();
  }, [idc, allowedTimeRange, datasourceRef, authorizedArea, validateRow]);

  const tryAutoFillVisitorInfos = React.useCallback(
    async (form: FormInstance<Record<string, Visitor>>, rowKey: string) => {
      const { identification }: Visitor = form.getFieldValue(rowKey);
      const [identificationType, ICN] = identification ?? [undefined, undefined];
      if (!identificationType || !ICN?.trim()) {
        return Promise.reject('Skipped');
      }
      const { error, data } = await fetchVisitorInfos({ identificationType, ICN });
      if (error || !data) {
        return Promise.reject(error?.message ?? 'No data');
      }
      const existing: Visitor = form.getFieldValue(rowKey);
      form.setFieldValue(rowKey, {
        ...existing,
        mobile:
          data.mobile && data.phoneCountryCode ? [data.phoneCountryCode, data.mobile] : undefined,
        name: data!.name,
        position: data.position,
        companyName: data!.companyName,
      });
      onChange?.(
        (value ?? []).map(item =>
          item.id === rowKey
            ? {
                ...item,
                mobile:
                  data.mobile && data.phoneCountryCode
                    ? [data.phoneCountryCode, data.mobile]
                    : [undefined, undefined],
                name: data!.name,
                position: data.position,
                companyName: data!.companyName,
                personalGoods: data.personalItem?.split(','),
              }
            : item
        )
      );
      return Promise.resolve();
    },
    [onChange, value]
  );

  const _authorizedAreas: string[] = React.useMemo(
    () =>
      uniq(
        (authorizedArea ?? []).map(guid => {
          const [firstPart, secondPart] = guid.split('.');
          return secondPart ? `${firstPart}.${secondPart}` : guid;
        })
      ),
    [authorizedArea]
  );
  const getRecordMergedContent = React.useCallback(
    (name: string, records: BackendExistingVisitRecord[]) => {
      if (records.length === 1) {
        //单个时间
        const record = records[0];
        return `${name}${t(generateFiledKey('alter.repeatAt'))}${dayjs(
          record.approveStartTime
        ).format('YYYY-MM-DD')}~${dayjs(record.approveEndTime).format('YYYY-MM-DD')}${t(
          generateFiledKey('alter.repeatError'),
          {
            blockGuid: `${(record.approveArea ?? '')
              .split(',')
              .filter((guid: string) => _authorizedAreas.includes(guid))
              .join('，')}`,
          }
        )}`;
      } else if (records.length > 1 && _authorizedAreas.length > 0) {
        const count = _authorizedAreas.reduce((acc, guid) => {
          return acc + (records.some(dt => dt.approveArea?.split(',').includes(guid)) ? 1 : 0);
        }, 0);
        if (_authorizedAreas.length === 1 || count === 1) {
          //多个时间 单个楼栋
          return `${name}${t(generateFiledKey('alter.repeatAt'), { defaultValue: '在' })}${records
            .map(
              record =>
                `${dayjs(record.approveStartTime).format('YYYY-MM-DD')}~${dayjs(
                  record.approveEndTime
                ).format('YYYY-MM-DD')}`
            )
            .join('，')}${t(generateFiledKey('alter.repeatError'), {
            blockGuid: `${(_authorizedAreas ?? [])[0]}`,
          })}`;
        } else {
          //多个时间 多个楼栋
          return `${name}${t(generateFiledKey('alter.repeatAt'))}${records
            .map(
              record =>
                `${dayjs(record.approveStartTime).format('YYYY-MM-DD')}~${dayjs(
                  record.approveEndTime
                ).format('YYYY-MM-DD')}
              ${t(generateFiledKey('alter.repeatError'), {
                blockGuid: `${record.approveArea}`,
              })}`
            )
            .join('，')}`;
        }
      }
    },
    [_authorizedAreas, t]
  );
  React.useEffect(() => {
    fetchVisitorTypes().then(({ data }) => {
      setVisitorTypeOptions(data);
    });
  }, []);

  return (
    <EditableProTable<Visitor>
      rowKey="id"
      editableFormRef={editableFormRef}
      headerTitle={
        <Space style={{ display: 'flex', marginBottom: 16, width: '100%' }} direction="vertical">
          {idc && allowedTimeRange && authorizedArea && (
            <ImportVisitorsFromFile
              idc={idc}
              bizTag={visitTypeTag && Object.keys(visitTypeTag)?.[0]}
              allowedTimeRange={allowedTimeRange}
              onOk={data => {
                const ids: string[] = [];
                const visitors = data
                  .map(visitorFromFile => {
                    const visitorType =
                      mapVisitorType(visitorFromFile.data.visitorType) ??
                      visitorTypeOptions.find(i => i.label === visitorFromFile.data.visitorType)
                        ?.value;
                    if (visitorType === null) {
                      return null;
                    }

                    const identificationType =
                      mapIdentificationType(visitorFromFile.data.certificateType) ?? undefined;

                    const id = nanoid(6);
                    ids.push(id);
                    const visitor: Visitor = {
                      id,
                      mobile: [
                        visitorFromFile.data.phoneCountryCode,
                        visitorFromFile.data.contactWay,
                      ],
                      name: visitorFromFile.data.name,
                      visitorType,
                      identification: [identificationType, visitorFromFile.data.identityNo] as [
                        CertificateType | undefined,
                        string | undefined,
                      ],
                      LPN: visitorFromFile.data.plateNo,
                      companyName: visitorFromFile.data.companyName,
                      operationalAuthorization: ['Yes', '是', '需要'].includes(
                        visitorFromFile.data.operable
                      )
                        ? 'yes'
                        : 'no',
                      position: visitorFromFile.data.position,
                      personalGoods: visitorFromFile.data.personalItem?.split(','),
                    };

                    return visitor;
                  })
                  .filter((item): item is Visitor => item !== null);
                onChange?.(visitors);
              }}
            />
          )}

          {existingVisitsRecords.length > 0 && (
            <Alert
              showIcon
              closable
              type="warning"
              message={
                <>
                  {existingVisitsRecords.map(existingVisitRecord => (
                    <Typography.Paragraph
                      key={`${existingVisitRecord.name}_$$_${existingVisitRecord.mobile}`}
                      style={{ marginBottom: existingVisitsRecords.length <= 1 ? 0 : 4 }}
                    >
                      {getRecordMergedContent(
                        existingVisitRecord.name,
                        existingVisitRecord.records
                      )}
                    </Typography.Paragraph>
                  ))}
                </>
              }
            />
          )}
        </Space>
      }
      scroll={{ x: 'max-content' }}
      recordCreatorProps={
        allowAddRow
          ? {
              position: 'top',
              creatorButtonText: t(generateFiledKey('button.addUser')),
              newRecordType: 'dataSource',
              record(index, dataSource) {
                return {
                  id: nanoid(6),
                  identification: ['ID_CARD', undefined],
                  operationalAuthorization: 'no',
                  mobile: ['+86', undefined],
                };
              },
            }
          : false
      }
      editable={{
        type: 'multiple',
        deletePopconfirmMessage: t(generateFiledKey('button.deleteConfirm'), {
          defaultValue: '删除此行？',
        }),
        editableKeys: value?.map(({ id }) => id),
        onValuesChange(_record, datasource) {
          onChange?.(datasource.map(visitor => trimStringValues(visitor) as Visitor));
        },
        actionRender: (_row, _config, dom) => [dom.delete],
      }}
      // @ts-ignore
      columns={[
        {
          width: 140,
          title: t(generateFiledKey('userInfos.visitorType')),
          dataIndex: 'visitorType',
          fixed: 'left',
          formItemProps(form, config) {
            return {
              rules: [
                {
                  required: !isGovernments,
                  message: `${t(generateFiledKey('userInfos.visitorType'))}${t(
                    generateFiledKey('select.required')
                  )}`,
                },
              ],
            };
          },
          renderFormItem(schema, config, form) {
            return <VisitorTypesSelect trigger="onDidMount" />;
          },
        },
        {
          title: t(generateFiledKey('userInfos.name'), { defaultValue: '姓名' }),
          dataIndex: 'name',
          formItemProps(form, config) {
            return {
              rules: [
                {
                  required: !isGovernments,
                  type: 'string',
                  whitespace: true,
                  message: `${t(generateFiledKey('userInfos.name'))}${t(
                    generateFiledKey('input.required')
                  )}`,
                },
                {
                  max: 32,
                  message: t(generateFiledKey('input.maxLen'), {
                    max: 32,
                  }),
                },
              ],
            };
          },
          renderFormItem(_schema, config, form) {
            return (
              <Input
                onBlur={() => {
                  getExistingVisitsRecords(form, config.record!.id);
                }}
              />
            );
          },
        },
        {
          width: 240,
          title: t(generateFiledKey('userInfos.mobile.title')),
          dataIndex: 'mobile',
          formItemProps(form, config) {
            return {
              dependencies: ['visitorType'],
              rules: [
                {
                  required: !isGovernments,
                  validator: (_, _value: [string, string]) => {
                    const reg = /^1[3456789]\d{9}$/;
                    const rowKey = config.rowKey![0];
                    const { visitorType } = form.getFieldValue(rowKey);

                    const isEmpty = !_value?.[0] || !_value?.[1];
                    if (isEmpty) {
                      // 如果是政府访客 && 没填，允许不填（跳过校验）
                      if (isGovernments) {
                        return Promise.resolve();
                      } else {
                        return Promise.reject(
                          new Error(
                            `${t(generateFiledKey('userInfos.mobile.title'))}${t(
                              generateFiledKey('input.required')
                            )}`
                          )
                        );
                      }
                    }

                    if (!_value[0] || !_value || !_value[1]) {
                      return Promise.reject(
                        new Error(
                          `${t(generateFiledKey('userInfos.mobile.title'))}${t(
                            generateFiledKey('input.required')
                          )}`
                        )
                      );
                    }
                    if (_value[0] === '+86' && !reg.test(_value[1])) {
                      return Promise.reject(
                        new Error(`${t(generateFiledKey('userInfos.mobile.error'))}`)
                      );
                    } else if (_value[0] === '+86' && _value[1].length > 11) {
                      return Promise.reject(
                        new Error(
                          t(generateFiledKey('input.maxLen'), {
                            max: 11,
                          })
                        )
                      );
                    } else if (
                      visitorType !== 'VIP' &&
                      (value ?? [])
                        .filter(record => record.id !== rowKey)
                        .some(record => record.mobile.join(',') === _value.join(','))
                    ) {
                      //校验手机号是否存在重复
                      return Promise.reject(
                        new Error(t(generateFiledKey('userInfos.mobile.duplicate')))
                      );
                    }
                    return Promise.resolve();
                  },
                },
              ],
            };
          },
          renderFormItem(schema, config, form) {
            return (
              <MobileField
                MobileInputProps={{
                  onBlur: () => {
                    getExistingVisitsRecords(form, config.record!.id);
                  },
                }}
              />
            );
          },
        },
        {
          width: 300,
          title: t(generateFiledKey('userInfos.identification.number')),
          dataIndex: 'identification',
          formItemProps(form, config) {
            return {
              rules: [
                _form => {
                  const rowKey = config.rowKey![0];
                  const required =
                    isItemRequired(form as FormInstance<Visitor>, rowKey) && !isGovernments;
                  return {
                    required,
                    validator: (_, [idType, id]: IdentificationValue) => {
                      const _id = id?.trim();

                      const isEmpty = !_id;

                      if (isEmpty && isGovernments) {
                        // 政府访客，证件号为空，允许跳过
                        return Promise.resolve();
                      }

                      if (required) {
                        if (!_id) {
                          return Promise.reject(
                            `${t(generateFiledKey('userInfos.identification.numberRequire'), {
                              defaultValue: '证件号码必填！',
                            })}`
                          );
                        }
                      }
                      if (_id) {
                        const PermitReg =
                          /(^[a-zA-Z0-9]{9}$)|(^[a-zA-Z0-9]{11}$)|(^\d{8}$)|(^[a-zA-Z0-9]{10}$)|(^\d{18}$)/;
                        if (
                          (idType === 'ID_CARD' && !isIdCard(_id)) ||
                          (idType === 'PASSPORT' && !isPassport(_id)) ||
                          (idType === 'PERMIT' && !PermitReg.test(_id))
                        ) {
                          return Promise.reject(
                            t(generateFiledKey('userInfos.identification.error'), {
                              defaultValue: '格式不正确！',
                            })
                          );
                        } else if (
                          (value ?? [])
                            .filter(record => record.id !== rowKey)
                            .some(record => record.identification?.join(',') === `${idType},${id}`)
                        ) {
                          //校验证件号是否存在重复
                          return Promise.reject(
                            new Error(t(generateFiledKey('userInfos.identification.duplicate')))
                          );
                        }
                      }
                      return Promise.resolve();
                    },
                  };
                },
                {
                  max: 20,
                  message: t(generateFiledKey('input.maxLen'), {
                    defaultValue: '最多输入 20 个字符！',
                    max: 20,
                  }),
                  transform(value?: IdentificationValue) {
                    return value?.[1];
                  },
                },
              ],
            };
          },
          renderFormItem(schema, config, form) {
            return (
              <Identification
                typeSelectProps={{
                  onSelect: async () => {
                    const [, ICN] = form.getFieldValue('identification') ?? [];
                    if (ICN) {
                      // Note: 为何这里需要 `Try...Catch`？
                      // 因为“手机号码、姓名、证件号码”变化时会调用一次 `getExistingVisitsRecords`，
                      // 所以当 `tryAutoFillVisitorInfos rejected` 时无需调用 `getExistingVisitsRecords`
                      try {
                        await tryAutoFillVisitorInfos(form, config.record!.id);
                        await getExistingVisitsRecords(form, config.record!.id);
                      } catch (error) {
                        console.error(error);
                      }
                    }
                  },
                }}
                ICNInputProps={{
                  onBlur: async () => {
                    await tryAutoFillVisitorInfos(form, config.record!.id).catch(
                      (_reason: string) => {
                        // ignore...
                      }
                    );
                    await getExistingVisitsRecords(form, config.record!.id);
                  },
                }}
              />
            );
          },
        },
        {
          title: isSpecialtyVisitType
            ? '单位名称'
            : t(generateFiledKey('userInfos.companyName'), { defaultValue: '公司名称' }),
          dataIndex: 'companyName',
          formItemProps(form, config) {
            return {
              dependencies: ['visitorType'],
              rules: [
                _form => {
                  const required =
                    isItemRequired(form as FormInstance<Visitor>, config.rowKey![0]) &&
                    !isGovernments;

                  return {
                    required,
                    type: 'string',
                    whitespace: true,
                    message: `${t(generateFiledKey('userInfos.companyName'), {
                      defaultValue: '公司名称',
                    })}${t(generateFiledKey('input.required'), {
                      defaultValue: '必填!',
                    })}`,
                  };
                },
                {
                  max: 40,
                  message: t(generateFiledKey('input.maxLen'), {
                    defaultValue: '最多输入 40 个字符！',
                    max: 40,
                  }),
                },
              ],
            };
          },
          renderFormItem(schema, config, form) {
            return <FuzzySearchInput placeholder=" " style={{ minWidth: 200 }} />;
          },
        },
        {
          title: position ?? t(generateFiledKey('userInfos.position'), { defaultValue: '岗位' }),
          dataIndex: 'position',
          formItemProps(form, config) {
            return {
              dependencies: ['visitorType'],
              rules: [
                _form => {
                  const required =
                    isItemRequired(form as FormInstance<Visitor>, config.rowKey![0]) &&
                    !isGovernments;

                  return {
                    required,
                    type: 'string',
                    whitespace: true,
                    message: position
                      ? `${position}必填!`
                      : `${t(generateFiledKey('userInfos.position'), {
                          defaultValue: '岗位',
                        })}${t(generateFiledKey('input.required'), {
                          defaultValue: '必填!',
                        })}`,
                  };
                },
                {
                  max: 20,
                  message: t(generateFiledKey('input.maxLen'), {
                    defaultValue: '最多输入 20 个字符！',
                    max: 20,
                  }),
                },
              ],
            };
          },
          renderFormItem(schema, config, form) {
            return <Input />;
          },
        },
        {
          title: t(generateFiledKey('userInfos.LPN'), { defaultValue: '车牌号' }),
          dataIndex: 'LPN',
          formItemProps(form, config) {
            return {
              rules: [
                {
                  max: 10,
                  type: 'string',
                  whitespace: true,
                  required: true,
                  validator: (_, _value: string) => {
                    const rowKey = config.rowKey![0];

                    if (_value?.trim()?.length > 10) {
                      return Promise.reject(new Error('最多输入 10 个字符！'));
                    }
                    if (
                      dockingStation &&
                      _value?.trim()?.length >= 1 &&
                      (value ?? [])
                        .filter(record => record.id !== rowKey)
                        .some(record => record.LPN === _value)
                    ) {
                      //校验车牌号号是否存在重复
                      return Promise.reject(new Error('车牌号重复! '));
                    }
                    return Promise.resolve();
                  },
                },
              ],
            };
          },
          renderFormItem(schema, config, form) {
            return <Input allowClear />;
          },
        },
        {
          title: t(generateFiledKey('userInfos.personalGoods')),
          dataIndex: 'personalGoods',
          width: 480,
          hidden: hiddenPersonalGoods,

          renderFormItem(schema, config, form) {
            return (
              <PersonalGoodItems
                accessDirectUrl={accessDirectUrl}
                contrabandItems={contrabandItems}
              />
            );
          },
        },
        {
          title: t(generateFiledKey('userInfos.operationalAuthorization.title')),
          dataIndex: 'operationalAuthorization',
          hidden: hiddenOperationalAuthorization,
          renderFormItem(schema, config, form) {
            return (
              <Radio.Group
                options={[
                  {
                    label: t(generateFiledKey('userInfos.operationalAuthorization.enum.yes')),
                    value: 'yes',
                  },
                  {
                    label: t(generateFiledKey('userInfos.operationalAuthorization.enum.no')),
                    value: 'no',
                  },
                ]}
              />
            );
          },
        },
        {
          width: 60,
          fixed: 'right',
          title: t(generateFiledKey('userInfos.option')),
          valueType: 'option',
        },
      ].filter(i => !i.hidden)}
      value={value}
      onChange={onChange}
    />
  );
}

function isItemRequired(_form: FormInstance<Visitor>, rowKey: string) {
  const { visitorType } = _form.getFieldValue(rowKey);
  const isVIP = visitorType === 'VIP';
  const required = !isVIP;

  return required;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function trimStringValues(values: Record<string, any>) {
  for (const key in values) {
    if (Object.prototype.hasOwnProperty.call(values, key)) {
      const value = values[key];
      if (typeof value == 'string') {
        const _value = value.trim();
        values[key] = !_value ? null : _value;
      }
    }
  }

  return values;
}
