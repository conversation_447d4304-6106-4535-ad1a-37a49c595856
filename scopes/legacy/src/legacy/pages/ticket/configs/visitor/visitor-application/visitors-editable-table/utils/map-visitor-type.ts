export function mapVisitorType(typeText: string) {
  switch (typeText) {
    case 'VIP':
      return 'VIP';
    case '参观访客':
    case 'Visitor':
      return 'GENERAL';
    case '维护施工':
    case 'Maintenance/Construction':
      return 'MAINTAIN';
    case '客户':
    case 'Customer':
      return 'CUSTOMER';
    case '服务商':
    case 'Service Provider':
      return 'SERVICE';
    default:
      return null;
  }
}
