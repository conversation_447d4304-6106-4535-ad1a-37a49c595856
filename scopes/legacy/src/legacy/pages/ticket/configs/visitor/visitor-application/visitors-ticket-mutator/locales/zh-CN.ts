import type { VisitorsTicketMutatorLocales } from './type.js';

export const zhCN: VisitorsTicketMutatorLocales = {
  visitorRequest: '访客申请',
  basicTitle: '基础信息',
  visitType: '业务分类',
  relatedTicketType: '关联工单类型',
  relatedTicketNumber: '关联单号',
  title: '工单标题',
  purpose: '申请原因',
  allowedTimeRange: '申请时间',
  allowedStartTime: '授权开始时间',
  allowedEndTime: '授权结束时间',
  startGTCurrent: '授权需在更合理的时间区间',
  endGtStart: '结束时间需大于开始时间',
  limitDays: '授权开始时间与结束时间不可超过{{day}}天',
  allowedTimeRangeLimitDates: '申请时间不可申请逾今14天',
  protectionDateTip: '包含重保日，请事先与相关方确认',
  idc: '进入园区',
  authorizedArea: '进入区域',
  receptionist: '指派接待人',
  authorizedNotify: {
    title: '授权成功通知',
    tooltip: '您在申请流程完成后会收到通知邮件，请留意邮件',
    enum: {
      email: '邮件通知',
      disabled: '无需通知',
    },
  },
  userInfos: {
    title: '人员信息',
    infoTitle: '{{userName}} 申请 {{idc}} 来访 {{visitType}}人员',
  },
  insideVisitorNotices: '内部人员',
  outSideVisitorNotices: '外部人员',
  email: '邮箱',
  emailPlaceholder: '请输入邮箱地址',
  emailFormatError: '请输入正确的邮箱地址',
  location: '位置',
  fileInfoList: {
    label: '附件',
    tooltip: '支持扩展名',
    upload: '上传',
  },
  message: {
    addUserEmptyError: '至少添加一个人员',
    userCheckError: '请删除已有授权记录的人员！',
    noticeUserMustRequiredError: '内部人员或外部人员必填一项',
    success: '提交成功',
    timeFirst: '请先设置授权时间',
    idcFirst: '请先选择来访机房',
  },
  button: {
    submit: '提交',
    cancel: '取消',
    addEmail: '添加邮箱',
    addUser: '添加人员',
    editUser: '编辑人员',
    edit: '编辑',
    delete: '删除',
    reset: '重置',
    confirm: '确认',
    checkAll: '全选',
  },
  input: {
    maxLen: '最多输入 {{max}} 个字符！',
    required: '必填!',
  },
  select: {
    required: '必选!',
  },
};

export default zhCN;
