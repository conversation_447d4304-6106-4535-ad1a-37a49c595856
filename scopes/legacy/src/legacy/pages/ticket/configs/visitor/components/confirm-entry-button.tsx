import React from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { FilePreview, FilePreviewWithContainer } from '@manyun/base-ui.ui.file-preview';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Table } from '@manyun/base-ui.ui.table';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { useConfirmVisitorsMutation } from '@manyun/sentry.gql.client.visits';

export type ConfirmEntryButtonProps = {
  taskNo: string;
  idcTag?: string;
  staffId?: string;
  recordIdList?: string[];
  blockGuidList?: string[];
  waitingConfirmVisitorData?: {
    key: string;
    type: string;
    title: string;
    data?: any[];
  };
  children?: React.ReactNode;
  onSuccess?: () => void;
};
export const ConfirmEntryButton = ({
  taskNo,
  idcTag,
  blockGuidList,
  staffId,
  recordIdList,
  waitingConfirmVisitorData,
  children,
  onSuccess,
}: ConfirmEntryButtonProps) => {
  const [onConfirmVisit, { loading: submitLoading }] = useConfirmVisitorsMutation();

  const [modalState, setModalState] = React.useState({
    open: false,
    filterWaitingConfirmVisitorData: waitingConfirmVisitorData?.data || [],
    selectedRowKeys: [],
    selectedRows: [],
  });

  const isIdc = React.useMemo(
    () => waitingConfirmVisitorData?.type === 'idc',
    [waitingConfirmVisitorData?.type]
  );

  const onClick = async () => {
    const length = modalState.filterWaitingConfirmVisitorData.length;
    if (length === 1) {
      const recordIdList = modalState.filterWaitingConfirmVisitorData[0].id;
      const params = isIdc
        ? {
            // 入园
            variables: {
              idcTag: idcTag,
              recordIdList,
            },
          }
        : {
            // 入楼
            variables: {
              blockGuidList: modalState.filterWaitingConfirmVisitorData[0].blockGuid,
              recordIdList,
            },
          };
      onVerify(params);
    }
    if (length > 1) {
      setModalState({ ...modalState, open: true });
    }
  };
  const onVerify = async params => {
    const { data } = await onConfirmVisit(params);
    if (data?.confirmVisitors.message) {
      message.error(data.confirmVisitors.message);
      setModalState({ ...modalState, open: false, selectedRowKeys: [], selectedRows: [] });
      onSuccess?.();
      return;
    }
    if ((data?.confirmVisitors.data ?? []).length > 0) {
      message.warning('系统异常，请重新确认');
      setModalState({ ...modalState, open: false, selectedRowKeys: [], selectedRows: [] });
      onSuccess?.();
      return;
    }
    message.success('成功确认');
    setModalState({ ...modalState, open: false, selectedRowKeys: [], selectedRows: [] });
    onSuccess?.();
  };

  return (
    <>
      <Button
        disabled={submitLoading}
        loading={submitLoading}
        type="link"
        compact
        onClick={onClick}
      >
        {children}
      </Button>
      <Modal
        destroyOnClose
        title={waitingConfirmVisitorData?.title}
        width={1080}
        bodyStyle={{ maxHeight: '450px', overflow: 'hidden', overflowY: 'scroll' }}
        open={modalState.open}
        onCancel={() => {
          setModalState({ ...modalState, open: false, selectedRowKeys: [], selectedRows: [] });
        }}
        okButtonProps={{
          disabled: !(modalState.selectedRowKeys?.length >= 1), // 确认按钮禁用条件
        }}
        onOk={() => {
          const recordIdList = modalState.selectedRows.map(i => i.id);
          const params = isIdc
            ? {
                // 入园
                variables: {
                  idcTag: idcTag,
                  recordIdList,
                },
              }
            : {
                // 入楼
                variables: {
                  blockGuidList: modalState.selectedRows.map(i => i.blockGuid),
                  recordIdList,
                },
              };
          onVerify(params);
        }}
      >
        <Table
          rowKey="id"
          columns={[
            {
              title: '登记位置',
              dataIndex: 'blockGuid',
            },
            {
              title: '进入时间',
              dataIndex: 'enteredAt',
              render: isoTime => {
                const date = new Date(isoTime);
                // 格式化为 年-月-日 时:分:秒
                const formattedTime = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`;
                return <div>{formattedTime}</div>;
              },
            },
            {
              title: '备注',
              dataIndex: 'confirmResult',
            },
            {
              title: '记录照片',
              dataIndex: 'filePath',
              render: (_, record) => {
                return (
                  <FilePreviewWithContainer
                    key="preview"
                    file={{
                      src: McUploadFile.generateSrc(record.filePath, '访客照片.png'),
                      name: '访客照片.png',
                      ext: '.png',
                    }}
                  >
                    <Button type="link" compact>
                      查看
                    </Button>
                  </FilePreviewWithContainer>
                );
              },
            },
          ]}
          dataSource={modalState.filterWaitingConfirmVisitorData}
          scroll={{ x: 'max-content' }}
          pagination={false}
          rowSelection={{
            type: 'checkbox',
            selectedRowKeys: modalState.selectedRowKeys,
            onChange: (selectedRowKeys, selectedRows) => {
              setModalState({ ...modalState, selectedRowKeys, selectedRows });
            },
            // getCheckboxProps: visitor => ({
            //   disabled: addedVisitorList.some(
            //     existingVisitor => existingVisitor.id === visitor.id
            //   ),
            // }),
          }}
        />
      </Modal>
    </>
  );
};
