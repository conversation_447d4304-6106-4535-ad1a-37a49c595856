import { PlusOutlined } from '@ant-design/icons';
import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Input } from '@manyun/base-ui.ui.input';
import { Popover } from '@manyun/base-ui.ui.popover';
import { Tag } from '@manyun/base-ui.ui.tag';

import { LOCALE_SCOPE_NAME, generateFiledKey } from '../../locales/index.js';

export type PersonalGoodItemsProps = {
  value?: string[];
  onChange?: (value?: string[]) => void;
  accessDirectUrl?: string;
  contrabandItems: string[];
};

interface InputRef {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  focus: (options?: any) => void;
  blur: () => void;
  setSelectionRange: (
    start: number,
    end: number,
    direction?: 'forward' | 'backward' | 'none'
  ) => void;
  select: () => void;
  input: HTMLInputElement | null;
}
const maxTagCount = 5;

export const PersonalGoodItems = ({
  value,
  onChange,
  accessDirectUrl,
  contrabandItems,
}: PersonalGoodItemsProps) => {
  const { t } = useTranslation(LOCALE_SCOPE_NAME);
  const [inputVisible, setInputVisible] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [editInputIndex, setEditInputIndex] = useState(-1);
  const [editInputValue, setEditInputValue] = useState('');
  const inputRef = useRef<InputRef>(null);
  const editInputRef = useRef<InputRef>(null);

  useEffect(() => {
    if (inputVisible) {
      inputRef.current?.focus();
    }
  }, [inputVisible]);

  useEffect(() => {
    editInputRef.current?.focus();
  }, [inputValue]);

  const handleClose = (removedTag: string) => {
    const newTags = (value ?? []).filter(tag => tag !== removedTag);
    onChange?.(newTags);
  };

  const showInput = () => {
    setInputVisible(true);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const handleInputConfirm = () => {
    if (inputValue && (value ?? []).indexOf(inputValue) === -1) {
      onChange?.([...(value ?? []), inputValue]);
    }
    setInputVisible(false);
    setInputValue('');
  };

  const handleEditInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEditInputValue(e.target.value);
  };

  const handleEditInputConfirm = () => {
    const newTags = value ? [...value] : [];
    newTags[editInputIndex] = editInputValue;
    onChange?.(newTags);
    setEditInputIndex(-1);
    setInputValue('');
  };

  return (
    <>
      {value?.map((tag, index) => {
        if (editInputIndex === index) {
          return (
            <Input
              ref={editInputRef}
              key={tag}
              style={{
                width: 78,
                marginRight: 8,
                verticalAlign: 'top',
              }}
              maxLength={20}
              size="small"
              value={editInputValue}
              onChange={handleEditInputChange}
              onBlur={handleEditInputConfirm}
              onPressEnter={handleEditInputConfirm}
            />
          );
        }

        const isErrorTag = contrabandItems.some(contrabandItem => {
          const pattern = new RegExp(contrabandItem);
          return pattern.test(tag);
        });

        const tagElem = (
          <Tag
            key={tag}
            style={{
              borderColor: isErrorTag ? 'red' : undefined,
              userSelect: 'none',
            }}
            closable
            onClose={() => handleClose(tag)}
          >
            <span
              onDoubleClick={e => {
                setEditInputIndex(index);
                setEditInputValue(tag);
                e.preventDefault();
              }}
            >
              {tag}
            </span>
          </Tag>
        );
        return isErrorTag ? (
          <Popover
            key={tag}
            getPopupContainer={elem => elem.parentElement!}
            content={
              <>
                {t(generateFiledKey('userInfos.personalGoodsTips.prefix'), {
                  defaultValue: '禁止携带有违禁品',
                })}
              </>
            }
            open
          >
            {tagElem}
          </Popover>
        ) : (
          tagElem
        );
      })}
      {inputVisible && (
        <Input
          ref={inputRef}
          size="small"
          style={{
            width: 78,
            marginRight: 8,
            verticalAlign: 'top',
          }}
          type="text"
          maxLength={20}
          value={inputValue}
          onChange={handleInputChange}
          onBlur={handleInputConfirm}
          onPressEnter={handleInputConfirm}
        />
      )}
      {!inputVisible && (value ?? []).length < maxTagCount && (
        <Tag
          style={{
            borderStyle: 'dashed',
          }}
          onClick={showInput}
        >
          <PlusOutlined /> {t(generateFiledKey('button.add'))}
        </Tag>
      )}
    </>
  );
};
