import { number } from 'echarts';
import React from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Cascader } from '@manyun/base-ui.ui.cascader';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { fetchMetadataByType } from '@manyun/resource-hub.service.fetch-metadata-by-type';
import { businessClassificationMappingAdd } from '@manyun/sentry.service.business-classification-mapping-add';
import { businessClassificationMappingDelete } from '@manyun/sentry.service.business-classification-mapping-delete';
import { businessClassificationMappingQuery } from '@manyun/sentry.service.business-classification-mapping-query';
import { businessClassificationMappingUpdate } from '@manyun/sentry.service.business-classification-mapping-update';

import { StyledDiv, StyledSpace, StyledTableTitle } from './styles';

function processVisitorData(data) {
  // 分类并转换
  const categorizedData = {
    VISITOR: [],
    VISITOR_THIRD_CATEGORY: [],
    VISITOR_DORA: [],
    VISITOR_SUB_DORA: [],
  };

  // 将数据按照 type 分类，并同时归类 code -> value, name -> label
  data.forEach(item => {
    const transformedItem = {
      value: item.code,
      label: item.name,
      ...item,
    };

    if (categorizedData[item.type]) {
      categorizedData[item.type].push(transformedItem);
    }
  });

  // 通用树形结构处理函数
  const buildTree = (parents, children, parentKey = 'code', childParentKey = 'parentCode') => {
    return parents.map(parent => {
      const parentId = `${parent.type}${parent[parentKey]}`;
      const filteredChildren = children.filter(child => child[childParentKey] === parentId);

      return {
        value: parent.code,
        label: parent.name,
        disabled: filteredChildren.length === 0, // 如果没有子节点，则禁用
        children: filteredChildren.map(child => ({
          value: child.code,
          label: child.name,
        })),
      };
    });
  };

  // 处理 VISITOR 和 VISITOR_THIRD_CATEGORY 的树形结构
  // const targetTree1 = buildTree(categorizedData.VISITOR, categorizedData.VISITOR_THIRD_CATEGORY);

  // 处理 VISITOR_DORA 和 VISITOR_SUB_DORA 的树形结构
  const targetTree0 = buildTree(categorizedData.VISITOR_DORA, categorizedData.VISITOR_SUB_DORA);

  // 返回结果对象
  return {
    'source-0': categorizedData.VISITOR,
    'source-0-sub': categorizedData.VISITOR_THIRD_CATEGORY,
    'target-0-tree': targetTree0,

    'source-1': categorizedData.VISITOR,
    'source-1-sub': categorizedData.VISITOR_THIRD_CATEGORY,
    'target-1-tree': targetTree0,
  };
}

export default function DualTableForms() {
  const initMetadataRef = React.useRef<any[]>([]);
  const [editStatus, setEditStatus] = React.useState<{ leftAddBtn: boolean; rightAddBtn: boolean }>(
    { leftAddBtn: false, rightAddBtn: false }
  );
  const [selectOption, setSelectOption] = React.useState({
    'source-0': [],
    'source-0-sub': [],
    'target-0': [],
    'source-1': [],
    'source-1-sub': [],
    'target-1': [],
  });

  // 左侧表格的状态
  const [formLeft] = Form.useForm();
  const [dataLeft, setDataLeft] = React.useState([]);
  const [editingKeyLeft, setEditingKeyLeft] = React.useState('');

  // 右侧表格的状态
  const [formRight] = Form.useForm();
  const [dataRight, setDataRight] = React.useState();
  const [editingKeyRight, setEditingKeyRight] = React.useState('');

  const leftSource = Form.useWatch(`source-0`, formLeft);
  const rightSource = Form.useWatch(`source-1`, formRight);

  console.log(
    dataLeft,
    dataRight,
    formLeft.getFieldsValue(),
    formRight.getFieldsValue(),
    '11111-----form'
  );

  // 判断当前记录是否在编辑状态
  const isEditing = (key: string, editingKey: string) => key === editingKey;
  // 编辑记录
  const edit = (
    relateType: 0 | 1, //关联类型，0:中联关联字节；1:字节关联中联
    record: any,
    setEditingKey: React.Dispatch<React.SetStateAction<string>>,
    form: any
  ) => {
    form.setFieldsValue({ ...record });
    setEditingKey(record.key);
    setEditStatus({ ...editStatus, [relateType ? 'rightAddBtn' : 'leftAddBtn']: true });
  };
  // 取消编辑
  const cancel = (
    relateType: 0 | 1, //关联类型，0:中联关联字节；1:字节关联中联
    setEditingKey: React.Dispatch<React.SetStateAction<string>>,
    data,
    setData
  ) => {
    setEditingKey('');
    setEditStatus({ ...editStatus, [relateType ? 'rightAddBtn' : 'leftAddBtn']: false });
    setData([
      ...data.filter(obj => {
        return Object.values(obj).every(i => i);
      }),
    ]);
  };

  // 查询
  const searchConfigs = async (relateType: number) => {
    // relateType	number	关联类型，0:中联关联字节；1:字节关联中联
    const { data, error } = await businessClassificationMappingQuery({ relateType });
    if (error) {
      console.log('businessClassificationMappingQuery', relateType);
      return;
    }
    if (data?.data && Array.isArray(data.data)) {
      // {
      //   key: '1',
      //   'source-0': 'VISITOR_26',
      //   'source-0-sub': '37',
      //   'target-0': ['dcim', 'auditing_work'],
      // },
      const source = `source-${relateType}`;
      const sourceName = `source-${relateType}_name_`;
      const sourceSub = `source-${relateType}-sub`;
      const sourceSubName = `source-${relateType}-sub_name_`;
      const target = `target-${relateType}`;
      const targetName = `target-${relateType}_name_`;

      const convertData = data.data.map(info =>
        relateType
          ? {
              id: info.id,
              key: info.id,
              [source]: info.targetCode,
              [sourceName]: info.targetName || '',
              [sourceSub]: info.targetSubCode,
              [sourceSubName]: info.targetSubName || '',
              [target]: [info.sourceCode, info.sourceSubCode],
              // @ts-ignore tag类型声明错误 SourceSubName -> sourceSubName
              [targetName]: `${info.sourceName || ''}/${info.sourceSubName || ''}`,
            }
          : {
              id: info.id,
              key: info.id,
              [source]: info.sourceCode,
              [sourceName]: info.sourceName || '',
              [sourceSub]: info.sourceSubCode,
              // @ts-ignore tag类型声明错误 SourceSubName -> sourceSubName
              [sourceSubName]: info.sourceSubName || '',
              [target]: [info.targetCode, info.targetSubCode],
              [targetName]: `${info.targetName || ''}/${info.targetSubName || ''}`,
            }
      );
      relateType ? setDataRight(convertData) : setDataLeft(convertData);
    }
  };
  // 新增记录
  const addRow = (
    relateType: 0 | 1, //关联类型，0:中联关联字节；1:字节关联中联
    setData: any,
    setEditingKey: React.Dispatch<React.SetStateAction<string>>,
    form: any
  ) => {
    const source = `source-${relateType}`;
    const target = `target-${relateType}`;
    const newRow = {
      key: `_add_${relateType}`,
      [source]: undefined,
      [`${source}-sub`]: undefined,
      [target]: undefined,
    };
    setData((prev = []) => [...prev, newRow]);
    edit(relateType, newRow, setEditingKey, form);
  };
  // 保存记录
  const save = async (
    relateType: 0 | 1, //关联类型，0:中联关联字节；1:字节关联中联
    key: string,
    data: any,
    setData: any,
    setEditingKey: React.Dispatch<React.SetStateAction<string>>,
    form: any
  ) => {
    try {
      const row = (await form.validateFields()) as any;
      const newData = [...data];

      const source = `source-${relateType}`;
      const sourceSub = `source-${relateType}-sub`;
      const target = `target-${relateType}`;
      const param = relateType
        ? {
            sourceCode: row[target][0],
            sourceSubCode: row[target][1],
            targetCode: row[source],
            targetSubCode: row[sourceSub],
          }
        : {
            sourceCode: row[source],
            sourceSubCode: row[sourceSub],
            targetCode: row[target][0],
            targetSubCode: row[target][1],
          };

      const _key = `_add_${relateType}`; //区分 新建 还是 编辑

      const findInfo = newData.find(item => key != _key && key === item.key);
      if (findInfo) {
        const { data: upDateData, error } = await businessClassificationMappingUpdate({
          ...param,
          id: findInfo.id,
        });
        if (error) {
          message.error(error.message);
        }
        if (upDateData) {
          setEditingKey('');
          setEditStatus({ ...editStatus, [relateType ? 'rightAddBtn' : 'leftAddBtn']: false });
          searchConfigs(relateType);
          message.success('保存成功');
        }
      } else {
        const key = Date.now().toString() + '_edit_' + relateType;
        const { data: addData, error } = await businessClassificationMappingAdd({
          ...param,
          relateType,
        });
        if (error) {
          message.error(error.message);
        }
        if (addData) {
          setEditingKey('');
          setEditStatus({ ...editStatus, [relateType ? 'rightAddBtn' : 'leftAddBtn']: false });
          searchConfigs(relateType);
          message.success('保存成功');
        }
      }
    } catch (errInfo) {
      console.log('Validate Failed:', errInfo);
    }
  };

  // 删除记录
  const deleteRow = async (
    relateType: 0 | 1, //关联类型，0:中联关联字节；1:字节关联中联
    key: string
  ) => {
    const { data: deleteDate, error } = await businessClassificationMappingDelete({ id: +key });
    if (error) {
      message.error(error.message);
      return;
    }
    if (deleteDate) {
      searchConfigs(relateType);
      setEditStatus({ ...editStatus, [relateType ? 'rightAddBtn' : 'leftAddBtn']: false });
      message.success('删除成功');
    }
  };

  const columns = (
    relateType: 0 | 1, //关联类型，0:中联关联字节；1:字节关联中联
    editingKey: string,
    form: any,
    data: any,
    setData: any,
    setEditingKey: React.Dispatch<React.SetStateAction<string>>
  ) => [
    {
      title: relateType ? '对应中联业务分类 ' : '中联业务分类',
      dataIndex: `source-${relateType}`,
      width: '45%',
      editable: true,
      render: (_: any, record: any) => {
        return (
          <div style={{ display: 'flex', justifyContent: 'space-around' }}>
            <div>
              <Typography.Text>{record[`source-${relateType}_name_`]}</Typography.Text>
            </div>
            <div>
              <Typography.Text>{record[`source-${relateType}-sub_name_`]}</Typography.Text>
            </div>
          </div>
        );
      },
    },
    {
      title: relateType ? '字节业务分类' : '对应字节业务分类',
      dataIndex: `target-${relateType}`,
      width: '40%',
      editable: true,
      render: (_: any, record: any) => {
        return (
          <>
            <Typography.Text>{record[`target-${relateType}_name_`]}</Typography.Text>
          </>
        );
      },
    },
    {
      title: '操作',

      width: '15%',
      dataIndex: `operation-${relateType}`,
      render: (_: any, record: any) => {
        const editable = isEditing(record.key, editingKey);
        return editable ? (
          <Space>
            <Typography.Link
              onClick={() => save(relateType, record.key, data, setData, setEditingKey, form)}
              style={{ marginRight: 8 }}
            >
              保存
            </Typography.Link>
            <Popconfirm
              title="确定取消?"
              onConfirm={() => cancel(relateType, setEditingKey, data, setData)}
            >
              <a>取消</a>
            </Popconfirm>
          </Space>
        ) : (
          <Space>
            <Typography.Link
              disabled={editingKey !== ''}
              onClick={() => edit(relateType, record, setEditingKey, form)}
            >
              编辑
            </Typography.Link>
            <Popconfirm title="确定删除?" onConfirm={() => deleteRow(relateType, record.key)}>
              <a>删除</a>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  const mergedColumns = (
    relateType: 0 | 1, //关联类型，0:中联关联字节；1:字节关联中联
    editingKey: string,
    form: any,
    data: any,
    setData: any,
    setEditingKey: React.Dispatch<React.SetStateAction<string>>
  ) =>
    columns(relateType, editingKey, form, data, setData, setEditingKey).map(col => {
      if (!col.editable) {
        return col;
      }
      return {
        ...col,
        onCell: (record: any) => ({
          record,
          relateType,
          dataIndex: col.dataIndex,
          title: col.title,
          editing: isEditing(record.key, editingKey),
          selectOption,
          form,
          data,
        }),
      };
    });

  React.useEffect(() => {
    (async () => {
      const { data } = await fetchMetadataByType({
        type: 'VISITOR,VISITOR_THIRD_CATEGORY,VISITOR_DORA,VISITOR_SUB_DORA',
      });

      if (data.data) {
        initMetadataRef.current = data.data;
        const _data = processVisitorData(data.data);
        setSelectOption({
          'source-0': _data['source-0'],
          'source-0-sub': _data['source-0-sub'],
          'target-0': _data['target-0-tree'],
          'source-1': _data['source-1'],
          'source-1-sub': _data['source-1-sub'],
          'target-1': _data['target-1-tree'],
        });
      }
    })();
  }, []);
  React.useEffect(() => {
    searchConfigs(0);
    searchConfigs(1);
  }, []);

  React.useEffect(() => {
    if (leftSource) {
      const leftSourceType = initMetadataRef.current.find(i => i.code === leftSource)?.type || '';
      const data =
        initMetadataRef.current.filter(i => i.parentCode === `${leftSourceType}${leftSource}`) ||
        [];
      console.log(leftSource, data, 'leftSource');
      setSelectOption({
        ...selectOption,
        'source-0-sub': data.map(i => ({ ...i, value: i.code, label: i.name })),
      });
    }
  }, [leftSource]);
  React.useEffect(() => {
    if (rightSource) {
      const rightSourceType = initMetadataRef.current.find(i => i.code === rightSource)?.type || '';
      const data =
        initMetadataRef.current.filter(i => i.parentCode === `${rightSourceType}${rightSource}`) ||
        [];
      console.log(rightSource, data, 'rightSource');
      setSelectOption({
        ...selectOption,
        'source-1-sub': data.map(i => ({ ...i, value: i.code, label: i.name })),
      });
    }
  }, [rightSource]);

  return (
    <StyledDiv>
      {/* 左侧表格 */}
      <div style={{ width: '50%', marginRight: '8px', overflow: 'auto', padding: ' 12px 0px' }}>
        <Form form={formLeft} component={false}>
          <Table
            title={() => (
              <StyledTableTitle>
                <Typography.Title level={5} showBadge>
                  中联映射至字节
                </Typography.Title>
              </StyledTableTitle>
            )}
            bordered={false}
            components={{
              body: {
                cell: EditableCell,
              },
            }}
            dataSource={dataLeft}
            columns={mergedColumns(
              0,
              editingKeyLeft,
              formLeft,
              dataLeft,
              setDataLeft,
              setEditingKeyLeft
            )}
            rowClassName="editable-row"
            pagination={false}
          />
        </Form>

        <Button
          type="dashed"
          style={{ marginTop: 16, width: '100%' }}
          disabled={editStatus.leftAddBtn}
          onClick={() => addRow(0, setDataLeft, setEditingKeyLeft, formLeft)}
        >
          + 添加
        </Button>
      </div>

      {/* 右侧表格 */}
      <div style={{ width: '50%', marginLeft: '8px', overflow: 'auto', padding: ' 12px 0px' }}>
        <Form form={formRight} component={false}>
          <Table
            title={() => (
              <Typography.Title level={5} showBadge>
                字节映射至中联
              </Typography.Title>
            )}
            components={{
              body: {
                cell: EditableCell,
              },
            }}
            bordered={false}
            dataSource={dataRight}
            columns={(() => {
              const adjustedColumns = mergedColumns(
                1,
                editingKeyRight,
                formRight,
                dataRight,
                setDataRight,
                setEditingKeyRight
              );
              // 交换第一项和第二项
              [adjustedColumns[0], adjustedColumns[1]] = [adjustedColumns[1], adjustedColumns[0]];
              return adjustedColumns;
            })()}
            rowClassName="editable-row"
            pagination={false}
          />
        </Form>

        <Button
          type="dashed"
          style={{ marginTop: 16, width: '100%' }}
          disabled={editStatus.rightAddBtn}
          onClick={() => addRow(1, setDataRight, setEditingKeyRight, formRight)}
        >
          + 添加
        </Button>
      </div>
    </StyledDiv>
  );
}

// 编辑单元格组件
const EditableCell = ({
  editing,
  dataIndex,
  title,
  relateType,
  record,
  selectOption,
  form,
  data,
  children,
  ...restProps
}: any) => {
  return (
    <td {...restProps}>
      {editing ? (
        <>
          {
            <>
              {dataIndex === `target-${relateType}` ? (
                <Form.Item
                  name={`${dataIndex}`}
                  style={{ margin: 0 }}
                  rules={[
                    {
                      required: true,
                      message: `请输入 ${title}!`,
                    },
                    ({ getFieldValue }) => ({
                      validator(_, value) {
                        if (!value || relateType !== 1) return Promise.resolve();

                        const duplicateExists = data.some(item => {
                          return (
                            item.key !== record.key &&
                            item[dataIndex]?.join('.') === value.join('.')
                          );
                        });

                        if (duplicateExists) {
                          return Promise.reject(new Error('该类型名已存在，请勿重复添加'));
                        }
                        return Promise.resolve();
                      },
                    }),
                  ]}
                >
                  <Cascader
                    placeholder="字节业务分类"
                    options={selectOption[dataIndex]}
                    showSearch={{
                      filter: (inputValue: string, path: any[]) =>
                        path.some(
                          option =>
                            (option.label as string)
                              .toLowerCase()
                              .indexOf(inputValue.toLowerCase()) > -1
                        ),
                    }}
                    onChange={(v, o) => {
                      form.setFieldValue(dataIndex, v);
                    }}
                  />
                </Form.Item>
              ) : (
                <StyledSpace size="large">
                  <Form.Item
                    name={dataIndex}
                    style={{ margin: 0, width: '100%' }}
                    rules={[
                      {
                        required: true,
                        message: `请输入 ${title}!`,
                      },
                    ]}
                  >
                    <Select
                      placeholder="一级分类"
                      style={{ width: '96%' }}
                      options={selectOption[dataIndex]}
                      showSearch
                      onChange={(v, o) => {
                        form.setFieldValue(`${dataIndex}-sub`, undefined);
                        form.setFieldValue(dataIndex, v);
                      }}
                      filterOption={(inputValue, option) => {
                        const searchValue = inputValue.toLowerCase();
                        return (
                          option?.label?.toLowerCase()?.includes(searchValue) ||
                          option?.value?.toLowerCase()?.includes(searchValue)
                        );
                      }}
                    />
                  </Form.Item>
                  <Form.Item
                    name={`${dataIndex}-sub`}
                    style={{ margin: 0, width: '100%' }}
                    rules={[
                      {
                        required: true,
                        message: `请输入 ${title}!`,
                      },
                      ({ getFieldValue }) => ({
                        validator(_, value) {
                          if (!value || relateType === 1) return Promise.resolve();

                          // 获取当前行的一级分类和二级分类
                          const currentSource = getFieldValue(dataIndex);
                          const currentSub = value;

                          const duplicateExists = data.some(item => {
                            return (
                              item.key !== record.key && // 忽略当前行
                              item[dataIndex] === currentSource && // 一级分类相同
                              item[`${dataIndex}-sub`] === currentSub // 二级分类相同
                            );
                          });

                          if (duplicateExists) {
                            return Promise.reject(new Error('该类型名已存在，请勿重复添加'));
                          }
                          return Promise.resolve();
                        },
                      }),
                    ]}
                  >
                    <Select
                      placeholder="二级分类"
                      style={{ width: '96%' }}
                      options={selectOption[`${dataIndex}-sub`]}
                      showSearch
                      disabled={!form.getFieldValue(dataIndex)}
                      onChange={(v, o) => {
                        const _name = `${dataIndex}-sub`;
                        form.setFieldValue(_name, v);
                      }}
                      filterOption={(inputValue, option) => {
                        const searchValue = inputValue.toLowerCase();
                        return (
                          option?.label?.toLowerCase()?.includes(searchValue) ||
                          option?.value?.toLowerCase()?.includes(searchValue)
                        );
                      }}
                    />
                  </Form.Item>
                </StyledSpace>
              )}
            </>
          }
        </>
      ) : (
        children
      )}
    </td>
  );
};
