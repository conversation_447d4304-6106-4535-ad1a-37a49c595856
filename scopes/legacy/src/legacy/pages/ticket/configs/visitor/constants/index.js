export const VISITOR_IS_SEX_KEY_TEXT_MAP = new Map([
  ['男', 1],
  ['女', 0],
]);

export const VISITOR_IS_OPERABLE_KEY_TEXT_MAP = new Map([
  ['是', true],
  ['否', false],
]);

export const VISITOR_TYPE_MAP = new Map([
  ['VIP', 'VIP'],
  ['参观访客', 'GENERAL'],
  ['维护施工', 'MAINTAIN'],
  ['客户', 'CUSTOMER'],
  ['服务商', 'SERVICE'],
]);

export const CERTIFICATE_TYPE_MAP = new Map([
  ['身份证', 'ID_CARD'],
  ['护照', 'PASSPORT'],
  ['其他', 'OTHER'],
]);

export const VISITOR_TYPE_KEY_MAP = new Map([
  ['VIP', 'VIP'],
  ['GENERAL', '参观访客'],
  ['MAINTAIN', '维护施工'],
  ['CUSTOMER', '客户'],
  ['SERVICE', '服务商'],
]);

export const CERTIFICATE_TYPE_KEY_MAP = new Map([
  ['ID_CARD', '身份证'],
  ['PASSPORT', '护照'],
  ['OTHER', '其他'],
]);
