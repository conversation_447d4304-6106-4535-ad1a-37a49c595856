import dayjs from 'dayjs';
import React from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import styled, { Theme, css } from '@manyun/dc-brain.theme.theme';
import { useSpace } from '@manyun/resource-hub.gql.client.spaces';

import { visitorManagerService } from '@manyun/dc-brain.legacy.services';

type Item = {
  blockGuid: string;
  registerNum: number;
  [key: string]: any;
};

const TypographyTextStyled = styled(Typography.Text)`
  & {
    color: rgba(0, 0, 0, 0.65) !important;
  }
`;

function groupByBlockGuid(data: Item[]) {
  const resultMap = new Map<string, { blockGuid: string; registerNum: number; items: Item[] }>();

  for (const item of data) {
    const { blockGuid, registerNum } = item;

    if (!resultMap.has(blockGuid)) {
      resultMap.set(blockGuid, {
        blockGuid,
        registerNum,
        items: [item],
      });
    } else {
      const group = resultMap.get(blockGuid)!;
      group.registerNum += registerNum;
      group.items.push(item);
    }
  }

  return Array.from(resultMap.values());
}
function SpaceText({ guid }: { guid: string }) {
  const [spaceIdc, spaceBlock, spaceRoom] = useSpace(guid);
  if (spaceBlock?.label) {
    return <>{spaceBlock.label}</>;
  }

  return <>{guid}</>;
}

export function RegistrationRecordPopup({ taskNo, bizTag, visibility, setVisibility }: any) {
  const [state, setState] = React.useState({ loading: false, list: [], blockList: [] });

  React.useEffect(() => {
    if (visibility) {
      (async () => {
        setState({
          ...state,
          loading: true,
        });

        // @ts-ignore
        const { response, error } = await visitorManagerService.fetchVisitorRecordPage({
          // @ts-ignore
          taskNo,
          bizTag,
          pageNum: 1,
          pageSize: 500,
        });
        if (error) {
          message.error(error);
          setState({
            ...state,
            loading: false,
          });
          return;
        }
        // @ts-ignore
        setState({
          loading: false,
          list: response?.data || [],
          blockList: groupByBlockGuid(response?.data || []),
        });
      })();
    }
  }, [taskNo, visibility]);

  return (
    <Modal
      title="登记记录列表"
      centered
      closable
      open={visibility}
      onOk={() => setVisibility(false)}
      onCancel={() => setVisibility(false)}
      width={832}
      footer={null}
    >
      <div style={{ display: 'flex', margin: '-4px 4px 10px 4px', width: 832, flexFlow: 'wrap' }}>
        {state.blockList?.map(i => {
          return (
            <Space direction="vertical" style={{ width: '130px', height: '62px' }}>
              <Typography.Text type="secondary" style={{ fontSize: '14px' }}>
                <SpaceText guid={i.blockGuid} />
              </Typography.Text>
              <Space size={0}>
                <Typography.Text style={{ fontSize: '24px' }}>{i.registerNum}</Typography.Text>
                <TypographyTextStyled
                  style={{
                    position: 'relative',
                    top: '2px',
                    fontSize: '16px',
                  }}
                >
                  人
                </TypographyTextStyled>
              </Space>
            </Space>
          );
        })}
      </div>
      <Table
        rowKey="id"
        loading={state.loading}
        columns={[
          {
            title: '登记时间',
            dataIndex: 'enterTime',
            key: 'enterTime',
            render: (enterTime, record) => {
              return (
                <TypographyTextStyled type="secondary">
                  {dayjs(enterTime).format('YYYY-MM-DD HH:mm')}
                </TypographyTextStyled>
              );
            },
          },
          {
            title: '登记区域',
            dataIndex: 'blockGuid',
            key: 'blockGuid',
            render: (blockGuid, record) => {
              return (
                <TypographyTextStyled type="secondary">
                  <SpaceText guid={blockGuid} />
                </TypographyTextStyled>
              );
            },
          },
          {
            title: '登记人数',
            dataIndex: 'registerNum',
            key: 'registerNum',
            render: (guestNum, record) => (
              <TypographyTextStyled type="secondary">{guestNum}</TypographyTextStyled>
            ),
          },
          // {
          //   title: '操作时间',
          //   dataIndex: 'confirmTime',
          //   key: 'confirmTime',
          //   render: (val, record) => {
          //     return val;
          //   },
          // },
        ]}
        scroll={{ x: 'max-content', y: '60vh' }}
        dataSource={state.list}
        pagination={false}
      />
    </Modal>
  );
}
