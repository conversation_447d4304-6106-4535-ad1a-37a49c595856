import React from 'react';

import { FilePreviewWithContainer } from '@manyun/base-ui.ui.file-preview';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Table } from '@manyun/base-ui.ui.table';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { SpaceText } from '@manyun/resource-hub.ui.space-text';
import { useLazyVisitRecords } from '@manyun/sentry.gql.client.visits';

export type EntryRecordsModalViewProps = {
  entryNum: number;
  visitorId: string;
};
export const EntryRecordsModalView = ({ entryNum, visitorId }: EntryRecordsModalViewProps) => {
  const [visible, setVisible] = React.useState(false);
  const [getVisitRecords, { data }] = useLazyVisitRecords();
  const [configUtil] = useConfigUtil();
  const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket');
  const {
    entryPosition = '入室位置',
    admissionTime = '入室时间',
    entryStatus = '入室状态',
  } = ticketScopeCommonConfigs?.entryOfPersons?.features ?? {
    entryPosition: '入室位置',
    admissionTime: '入室时间',
    entryStatus: '入室状态',
  };
  if (entryNum > 0) {
    return (
      <>
        <Typography.Link
          onClick={() => {
            setVisible(true);
            getVisitRecords({
              variables: {
                params: {
                  page: 1,
                  pageSize: 500,
                  visitId: visitorId,
                },
              },
            });
          }}
        >
          {entryNum}
        </Typography.Link>
        <Modal
          width={864}
          bodyStyle={{
            maxHeight: 'calc(80vh - 55px)',
            overflowY: 'auto',
          }}
          title="登记记录"
          open={visible}
          footer={null}
          onCancel={() => {
            setVisible(false);
          }}
        >
          <Table
            tableLayout="fixed"
            scroll={{ x: 'max-content' }}
            columns={[
              {
                title: entryPosition,
                dataIndex: 'blockGuid',
                render: val => {
                  const locations = (val ?? '').split('.');
                  return val ? (
                    <SpaceText
                      guid={locations[0] && locations[0] === locations[1] ? locations[0] : val}
                    />
                  ) : (
                    '--'
                  );
                },
              },
              {
                title: admissionTime,
                dataIndex: 'enteredAt',
              },
              {
                title: '离开时间',
                dataIndex: 'leftAt',
              },
              {
                title: entryStatus,
                dataIndex: 'blockGuid',
                render: (val, record) => {
                  return (
                    <Tag
                      color={
                        record.status.value === 'ENTERED'
                          ? 'success'
                          : record.status.value === 'NO_LEAVE'
                            ? 'default'
                            : 'error'
                      }
                    >
                      {record.status.name}
                    </Tag>
                  );
                },
              },
              {
                title: '记录照片',
                dataIndex: 'filePath',
                fixed: 'right',
                render: val => {
                  const filename = '访客照片.png';
                  return val ? (
                    <FilePreviewWithContainer
                      file={{
                        name: filename,
                        src: McUploadFile.generateSrc(val, filename),
                        ext: '.png',
                      }}
                    >
                      <Typography.Link>查看</Typography.Link>
                    </FilePreviewWithContainer>
                  ) : (
                    '--'
                  );
                },
              },
            ]}
            dataSource={data?.visitRecords?.data ?? []}
            pagination={false}
          />
        </Modal>
      </>
    );
  }

  return null;
};
