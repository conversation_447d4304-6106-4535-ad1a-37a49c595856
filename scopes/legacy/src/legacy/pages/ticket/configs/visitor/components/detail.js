import Form from '@ant-design/compatible/es/form';
import { Select } from '@galiojs/awesome-antd';
import dayjs from 'dayjs';
import React, { useCallback, useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { Button } from '@manyun/base-ui.ui.button';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { useWaitingConfirmVisitRecords } from '@manyun/sentry.gql.client.visits';
import { cancelVisitAuthorization } from '@manyun/sentry.service.cancel-visit-authorization';
import { BackendTaskStatus } from '@manyun/ticket.model.ticket';

import { YES_OR_NO_OPTIONS } from '@manyun/dc-brain.legacy.constants';
import { visitorManagerService } from '@manyun/dc-brain.legacy.services';
import { taskCenterService } from '@manyun/dc-brain.legacy.services';
import * as urlsUtil from '@manyun/dc-brain.legacy.utils/urls';

import { AuthorizedAreaText } from './authorized-area-text';
import { ConfirmEntryButton } from './confirm-entry-button';
import { EntryRecordsModalView } from './entry-records-modal-view';
import { RegionalCardList } from './regional-card-list';
import { RegistrationRecordPopup } from './registration-record-popup';
import { RenderProtectionDate } from './render-protection-date';

const defaultFormat = 'YYYY-MM-DD HH:mm';

export function VisitorTicketDetail({ basicInfo, getBasicInfo, ticketTypes, form }) {
  const { taskProperties, idcTag, taskNo, creatorId, taskStatus, taskAssignee, blockGuidList } =
    basicInfo;
  const propties = React.useMemo(() => {
    if (!(taskProperties && typeof taskProperties == 'string')) {
      return {};
    }
    try {
      return JSON.parse(taskProperties);
    } catch (error) {
      console.error(error);
      return {};
    }
  }, [taskProperties]);
  const allowedStartedAt = propties.approveStartTime && dayjs(propties.approveStartTime);
  const allowedEndedAt = propties.approveEndTime && dayjs(propties.approveEndTime);
  const isNowAfterAllowedEndedAt = Date.now() >= allowedEndedAt?.valueOf();
  const { getFieldDecorator } = form;
  const [tableData, setDataSource] = useState({ list: [], total: 0, pageNum: 1, pageSize: 10 });
  const [isAssignee] = useAuthorized({ checkByUserId: taskAssignee });
  const [bizTagInfo, setBizTagInfo] = React.useState();
  const [visibility, setVisibility] = React.useState(false);
  const [registrationBtn, setRegistrationBtn] = React.useState(false);
  const canConfirm = BackendTaskStatus.PROCESSING === taskStatus && isAssignee;
  const canClosed = [BackendTaskStatus.FINISH, BackendTaskStatus.PROCESSING].includes(taskStatus);
  const [isCreator] = useAuthorized({ checkByUserId: creatorId });
  const [configUtil] = useConfigUtil();
  const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket');
  const {
    dockingStation = false,
    position = '职位',
    allowPhotographyTime = '允许拍照时间',
    hiddenOperationalAuthorization = false,
    hiddenPersonalItem = false,
    hiddenVoucherCode = false,
    entryNum = '入室登记次数',
  } = ticketScopeCommonConfigs?.entryOfPersons?.features ?? {
    dockingStation: false,
    position: '职位',
    allowPhotographyTime: '允许拍照时间',
    hiddenOperationalAuthorization: false,
    hiddenPersonalItem: false,
    hiddenVoucherCode: false,
    entryNum: '入室登记次数',
  };

  const { showTakePhotoTime = false } = configUtil.getScopeCommonConfigs('sentry').visit || {
    showTakePhotoTime: false,
  };

  // 需求 得需要先查询当前工单下 查询入室确认列表  点击确认入楼或者入园 拿 staffId 去匹配 list id. 以 blockGuid去区分 超过一条的需要弹窗
  // culms [登记位置，进入时间，备注，记录照片]
  const {
    data: confirmVisitData,
    refetch,
    confirmLoading,
  } = useWaitingConfirmVisitRecords({
    variables: { taskNo },
  });

  const waitingConfirmVisitorData = React.useMemo(
    () => confirmVisitData?.waitingConfirmVisitor?.data,
    [confirmVisitData]
  );
  useEffect(() => {
    if (!taskNo) {
      return;
    }
    getCabinet();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [taskNo, waitingConfirmVisitorData]);

  const getCabinet = async (pageNum, pageSize) => {
    const { response, error } = await taskCenterService.fetchVisitorStaffList({
      taskNo: taskNo,
      pageNum: pageNum ?? tableData.pageNum,
      pageSize: pageSize ?? tableData.pageSize,
      ...form.getFieldsValue(),
      operable: formatBoolean(form.getFieldsValue().operable),
    });
    if (error) {
      message.error(error);
      return;
    }
    const list = response.data.map(item => ({
      ...item,
      waitingConfirmVisitorIdcData:
        waitingConfirmVisitorData?.filter(i => {
          const [idc, block] = i.blockGuid?.split('.');
          return i.staffId === item.id && idc === block;
        }) || [],
      waitingConfirmVisitorBlockData:
        waitingConfirmVisitorData?.filter(i => {
          const [idc, block] = i.blockGuid?.split('.');
          return i.staffId === item.id && idc !== block;
        }) || [],
    }));

    setDataSource({
      list,
      total: response.total,
      pageNum,
      pageSize,
    });
  };

  const onChangePage = (pageNo, pageSize) => {
    getCabinet(pageNo, pageSize);
  };

  const handleSearch = () => {
    getCabinet(1, 10);
    setDataSource({ ...tableData, pageNum: 1, pageSize: 10 });
  };

  function formatBoolean(operable) {
    if (operable === null || operable === undefined) {
      return null;
    }
    return Boolean(operable);
  }

  function linkTaskDetail(id, taskType, idcTag, blockTag) {
    if (id === null || id === undefined || id === '') {
      return '--';
    }
    return (
      <Link
        to={urlsUtil.generateTicketDetailLocation({
          id: id,
          ticketType: taskType.toLowerCase(),
          idcTag: idcTag,
        })}
      >
        {id}
      </Link>
    );
  }
  const getBizTagInfo = async () => {
    const { response, error } = await taskCenterService.fetchVisitorInfoStaffList({
      taskNo,
      bizTag: propties.bizTag,
    });
    if (error) {
      message.error(error);
      return;
    }

    setBizTagInfo(response.data?.[0] || {});
    // @ts-ignore
    const { response: registrationData } = await visitorManagerService.fetchVisitorRecordPage({
      // @ts-ignore
      taskNo,
      bizTag: propties.bizTag,
      pageNum: 1,
      pageSize: 500,
    });
    if (registrationData?.data?.length >= 1) {
      setRegistrationBtn(true);
    }
  };
  const columns = React.useMemo(
    () =>
      [
        {
          title: '人员类型',
          dataIndex: ['visitorType', 'name'],
          render: value => {
            return value ?? '--';
          },
        },
        {
          title: '姓名',
          dataIndex: 'name',
          render: (value, { authorized }) => {
            return (
              <Space>
                {value ?? '--'}
                {canClosed &&
                  !isNowAfterAllowedEndedAt &&
                  !authorized &&
                  propties.bizTag !== 'GOVERNMENT' && <Tag>取消授权</Tag>}
              </Space>
            );
          },
        },
        {
          title: '联系方式',
          dataIndex: 'contactWay',
          width: 128,
          render: (val, record) => (
            <Space>
              {record.phoneCountryCode ?? ''}
              {val ?? '--'}
            </Space>
          ),
        },
        // {
        //   title: '证件类型',
        //   dataIndex: ['certificateType', 'name'],
        //   width: 108,
        // },
        {
          title: '证件号码',
          dataIndex: 'identityNo',
          width: 240,
          render: (val, record) => {
            return (
              <div>
                {record.certificateType?.name}
                {record.certificateType?.name && ' /'} {val ?? '--'}
              </div>
            );
          },
        },
        {
          title: propties.bizTag ? '单位名称' : '公司名称',
          dataIndex: 'companyName',
          width: 224,
          render(companyName) {
            return (
              <Tooltip placement="topLeft" title={companyName}>
                <Typography.Text style={{ maxWidth: 224 - 32 }} ellipsis>
                  {companyName ?? '--'}
                </Typography.Text>
              </Tooltip>
            );
          },
        },
        {
          title: position,
          dataIndex: 'position',
          render: value => {
            return value ?? '--';
          },
        },
        {
          title: allowPhotographyTime,
          dataIndex: 'photoTime',
          hidden: showTakePhotoTime !== true,
          render: (_, record) =>
            record.photoStartTime || record.photoEndTime
              ? `${record.photoStartTime ? dayjs(record.photoStartTime).format('YYYY-MM-DD HH:mm:ss') : '--'} ~ ${record.photoEndTime ? dayjs(record.photoEndTime).format('YYYY-MM-DD HH:mm:ss') : '--'}`
              : '--',
        },
        {
          title: '车牌号',
          dataIndex: 'plateNo',
          render: value => {
            return value ?? '--';
          },
        },
        {
          title: '随身物品',
          dataIndex: 'personalItem',
          hidden: hiddenPersonalItem,
          render: val => (val ? val.split(',').filter(Boolean).join('｜') : '--'),
        },
        {
          title: '是否具备操作权限',
          dataIndex: 'operable',
          hidden: hiddenOperationalAuthorization,
          render(operable) {
            if (operable) {
              return '需要';
            }
            return '不需要';
          },
        },
        {
          title: '凭证码',
          hidden: hiddenVoucherCode,
          dataIndex: 'authNo',
        },
        {
          title: entryNum,
          dataIndex: 'entryNum',
          render: (val, record) => (
            <EntryRecordsModalView entryNum={record.entryNum} visitorId={record.id} />
          ),
          hidden: propties.bizTag === 'GOVERNMENT',
        },
        {
          title: '操作',
          dataIndex: '__actions',
          fixed: 'right',
          width: 96,
          render(
            _,
            {
              id,
              authorized,
              confirmIdc,
              confirmBlock,
              waitingConfirmVisitorIdcData,
              waitingConfirmVisitorBlockData,
            }
          ) {
            const actions = [];
            if (BackendTaskStatus.FINISH === taskStatus && authorized) {
              // if (canConfirm && (confirmIdc || confirmBlock) && authorized) {
              actions.push(
                <Space key="confirm">
                  {waitingConfirmVisitorIdcData.length >= 1 && (
                    <ConfirmEntryButton
                      taskNo={taskNo}
                      staffId={id}
                      idcTag={idcTag}
                      waitingConfirmVisitorData={{
                        key: id,
                        type: 'idc',
                        title: '确认入园',
                        data: [...waitingConfirmVisitorIdcData],
                      }}
                      onSuccess={() => {
                        refetch();
                        getCabinet();
                      }}
                    >
                      确认入园
                    </ConfirmEntryButton>
                  )}
                  {waitingConfirmVisitorBlockData.length >= 1 && (
                    <ConfirmEntryButton
                      taskNo={taskNo}
                      staffId={id}
                      blockGuidList={blockGuidList}
                      waitingConfirmVisitorData={{
                        key: id,
                        type: 'block',
                        title: '确认入楼',
                        data: [...waitingConfirmVisitorBlockData],
                      }}
                      onSuccess={() => {
                        refetch();
                        getCabinet();
                      }}
                    >
                      确认入楼
                    </ConfirmEntryButton>
                  )}
                </Space>
              );
            }
            if (authorized && !isNowAfterAllowedEndedAt && canClosed) {
              actions.push(
                <Popconfirm
                  title="确定取消授权？"
                  onConfirm={async () => {
                    const { error } = await cancelVisitAuthorization({ idc: idcTag, userId: id });
                    if (error) {
                      message.error(error.message);
                      return;
                    }
                    setDataSource(prev => ({
                      ...prev,
                      list: prev.list.map(visitRecord => {
                        if (visitRecord.id === id) {
                          return {
                            ...visitRecord,
                            authorized: false,
                          };
                        }
                        return visitRecord;
                      }),
                    }));
                    message.success('取消授权成功！');
                  }}
                >
                  <Button key="cancel" compact type="link">
                    取消授权
                  </Button>
                </Popconfirm>
              );
            }
            return actions.length ? <Space key={`${+new Date()}_${id}`}>{actions}</Space> : '--';
          },
          hidden: propties.bizTag === 'GOVERNMENT',
        },
      ].filter(item => !item.hidden),
    [
      showTakePhotoTime,
      canClosed,
      isNowAfterAllowedEndedAt,
      canConfirm,
      isCreator,
      isAssignee,
      taskNo,
      idcTag,
      blockGuidList,
      position,
      allowPhotographyTime,
      hiddenOperationalAuthorization,
      hiddenPersonalItem,
      hiddenVoucherCode,
      entryNum,
      getCabinet,
      confirmLoading,
    ]
  );

  React.useEffect(() => {
    if (taskNo && propties?.bizTag) {
      getBizTagInfo();
    }
  }, [taskNo, propties?.bizTag]);
  return (
    <Space style={{ display: 'flex' }} direction="vertical">
      <RegistrationRecordPopup
        taskNo={taskNo}
        bizTag={propties.bizTag}
        visibility={visibility}
        setVisibility={setVisibility}
      />
      <Descriptions column={2}>
        {!dockingStation && (
          <Descriptions.Item label="授权区域">
            <AuthorizedAreaText taskNo={taskNo} />
          </Descriptions.Item>
        )}
        {!!propties.bizTag && (
          <>
            <Descriptions.Item label="单位名称">{bizTagInfo?.companyName}</Descriptions.Item>
            <Descriptions.Item label="申请人数">
              {bizTagInfo?.guestNum ?? '--'}
              {registrationBtn && !!bizTagInfo?.guestNum && (
                <a
                  style={{ marginLeft: '8px' }}
                  onClick={() => {
                    setVisibility(true);
                  }}
                >
                  查看登记明细
                </a>
              )}
            </Descriptions.Item>
            <Descriptions.Item label="接待人">
              {bizTagInfo?.userId ? (
                <UserLink userId={bizTagInfo?.userId} userName={bizTagInfo?.name} />
              ) : (
                (bizTagInfo?.name ?? '--')
              )}
              <span style={{ marginLeft: '8px' }}> {` (${bizTagInfo?.contactWay ?? '--'})`} </span>
            </Descriptions.Item>
          </>
        )}

        <Descriptions.Item label="申请时间">
          {allowedStartedAt && allowedEndedAt ? (
            <Space align="center">
              {allowedStartedAt.format(defaultFormat)}-{allowedEndedAt.format(defaultFormat)}
              <RenderProtectionDate
                startTime={allowedStartedAt.valueOf()}
                endTime={allowedEndedAt.valueOf()}
              />
            </Space>
          ) : (
            '--'
          )}
        </Descriptions.Item>

        <Descriptions.Item label={propties.bizTag ? '参观报备' : '申请原因'}>
          {propties.enterReason?.trim() ? propties.enterReason : '--'}
        </Descriptions.Item>

        {((basicInfo?.assigneeList ?? []).length > 0 ||
          (basicInfo?.assigneeRoleList ?? []).length > 0) && (
          <Descriptions.Item label="指派接待人">
            <Space size={0} split={<Divider type="vertical" />} wrap>
              {(basicInfo?.assigneeList ?? []).map(user => (
                <UserLink key={user.id} external userId={user.id} userName={user.userName} />
              ))}
            </Space>
            {(basicInfo?.assigneeRoleList ?? []).map(role => role.roleName).join('｜')}
            {(basicInfo?.assigneeList ?? []).length === 0 &&
              (basicInfo?.assigneeRoleList ?? []).length === 0 &&
              '--'}
          </Descriptions.Item>
        )}
        {propties.relateTaskType && (
          <Descriptions.Item label="关联工单类型">
            {ticketTypes[propties.relateTaskType]?.taskValue}
          </Descriptions.Item>
        )}
        {propties.relateTaskNo && (
          <Descriptions.Item label="关联工单单号">
            {linkTaskDetail(propties.relateTaskNo, propties.relateTaskType, idcTag, idcTag)}
          </Descriptions.Item>
        )}
      </Descriptions>
      {dockingStation && (
        <div style={{ paddingBottom: '16px' }}>
          <RegionalCardList
            taskNo={taskNo}
            idcTag={idcTag}
            bizTag={propties.bizTag}
            visitType={basicInfo.taskSubType}
            showAddAssignee={basicInfo.taskStatus === '1'}
            showDeleteAssignee={basicInfo.taskStatus === '1'}
          />
        </div>
      )}
      <Form layout="inline">
        <Form.Item label="人员姓名" colon={false}>
          {getFieldDecorator('name')(<Input allowClear style={{ width: 180 }} />)}
        </Form.Item>
        <Form.Item label="有效证件号" colon={false}>
          {getFieldDecorator('identityNo')(<Input allowClear style={{ width: 180 }} />)}
        </Form.Item>
        <Form.Item label={propties.bizTag ? '单位名称' : '公司名称'} colon={false}>
          {getFieldDecorator('companyName')(<Input allowClear style={{ width: 180 }} />)}
        </Form.Item>
        {!hiddenOperationalAuthorization && (
          <Form.Item label="是否具备操作权限" colon={false}>
            {getFieldDecorator('operable', {
              initialValue: null,
            })(
              <Select showSearch allowClear style={{ width: 100 }}>
                {YES_OR_NO_OPTIONS.map(({ label, value }) => (
                  <Select.Option key={value} value={value}>
                    {label}
                  </Select.Option>
                ))}
              </Select>
            )}
          </Form.Item>
        )}
        <Form.Item>
          <Space>
            <Button type="primary" onClick={handleSearch}>
              搜索
            </Button>
            <Button
              onClick={() => {
                form.resetFields();
                setDataSource({ ...tableData, pageNum: 1, pageSize: 10 });
                getCabinet(1, 10);
              }}
            >
              重置
            </Button>
          </Space>
        </Form.Item>
      </Form>
      {canConfirm &&
        tableData.list.some(
          visitor =>
            (visitor.confirmBlock && visitor.authorized) ||
            (visitor.confirmIdc && visitor.authorized)
        ) && (
          <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
            <ConfirmEntryButton
              taskNo={taskNo}
              onSuccess={() => {
                getCabinet();
              }}
            >
              一键确认
            </ConfirmEntryButton>
          </Space>
        )}
      <Table
        rowKey="id"
        columns={columns}
        scroll={{ x: 'max-content' }}
        dataSource={tableData.list}
        pagination={{
          style: { marginBottom: '0px' },
          total: tableData.total,
          current: tableData.pageNum,
          onChange: onChangePage,
          pageSize: tableData.pageSize,
        }}
      />
    </Space>
  );
}
const mapStateToProps = ({ common: { ticketTypes } }) => ({
  ticketTypes: ticketTypes?.normalizedList || {},
});

export default connect(mapStateToProps)(Form.create()(VisitorTicketDetail));
