import React from 'react';

import { CertificateTypeSelect } from '@manyun/base-ui.ui.certificate-type-select';
import type {
  CertificateType,
  CertificateTypeSelectProps,
} from '@manyun/base-ui.ui.certificate-type-select';
import { Input } from '@manyun/base-ui.ui.input';
import type { InputProps } from '@manyun/base-ui.ui.input';
import { Space } from '@manyun/base-ui.ui.space';

export type Value = [CertificateType | undefined, string | undefined];
export type IdentificationProps = {
  typeSelectProps?: CertificateTypeSelectProps;
  ICNInputProps?: InputProps;
  value?: Value;
  onChange?: (value: Value) => void;
};

function _Identification(
  { typeSelectProps, ICNInputProps, value, onChange }: IdentificationProps,
  ref?: React.ForwardedRef<void>
) {
  return (
    <Space.Compact>
      <CertificateTypeSelect
        {...typeSelectProps}
        style={{ width: 120 }}
        value={value?.[0]}
        onChange={type => {
          onChange?.([type as CertificateType, value?.[1]]);
        }}
      />
      <Input
        {...ICNInputProps}
        value={value?.[1]}
        onChange={({ target: { value: ICN } }) => {
          onChange?.([value?.[0], ICN]);
        }}
      />
    </Space.Compact>
  );
}

export const Identification = React.forwardRef(_Identification);
Identification.displayName = 'Identification';
