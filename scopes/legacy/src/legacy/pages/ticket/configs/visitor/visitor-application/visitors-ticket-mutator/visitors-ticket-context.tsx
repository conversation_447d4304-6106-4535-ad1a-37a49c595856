import React, { ReactNode, createContext, useContext } from 'react';

import { MetaType } from '@manyun/resource-hub.model.metadata';
import { fetchMetadataByType } from '@manyun/resource-hub.service.fetch-metadata-by-type';

export interface VisitorsTicketContext {
  ticketNumber: string | undefined; // 工单号
  isEditing: boolean; // 是否编辑模式
  form: any; // Form实例
  areaForm: any; // 区域Form实例
  idc: string[] | undefined; // 机房
  visitType: string[] | undefined; // 业务分类
  initTreeDataRef: React.MutableRefObject<any>; // 机房树数据
  roomTypeMetaDataRef: React.MutableRefObject<any>; // 元数据

  initDisableVisitTypes: string[]; // 禁用的业务分类
  initDisableBlockValues: string[]; // 默认所需 禁用 PZ Z 楼栋匹配的字段
}

export const VisitorsTicketContext = createContext<VisitorsTicketContext | null>(null);

interface VisitorsTicketProviderProps {
  children: ReactNode;
  value: VisitorsTicketContext | null | any;
}

// 创建Provider组件
export const VisitorsTicketProvider: React.FC<VisitorsTicketProviderProps> = ({
  children,
  value,
}) => {
  const roomTypeMetaDataRef: any = React.useRef();

  const [state, setState] = React.useState({
    initDisableVisitTypes: [
      'dcim',
      'physical_security',
      'property',
      'business_visitor',
      'government_visitor',
      'other',
    ],
    initDisableBlockValues: ['PZ', 'Z'],
  });

  React.useEffect(() => {
    (async () => {
      // @ts-ignore 不能将类型“MetaType.ROOM_TYPE”分配给类型“MetaType”。ts(2322)
      const { data } = await fetchMetadataByType({ type: MetaType.ROOM_TYPE });
      if (data.data) {
        roomTypeMetaDataRef.current = data.data;
      }
    })();
  }, []);

  return (
    <VisitorsTicketContext.Provider
      value={{
        ...value,
        ...state,
        roomTypeMetaDataRef,
      }}
    >
      {children}
    </VisitorsTicketContext.Provider>
  );
};

// 创建Context Hook
export const useVisitorsTicketContext = () => {
  const context = useContext(VisitorsTicketContext);
  if (!context) {
    throw new Error('useVisitorsTicketContext must be used within a VisitorsTicketProvider');
  }
  return context;
};
