import { nanoid } from 'nanoid';
import React from 'react';
import { useHistory, useLocation, useParams } from 'react-router-dom';

import { message } from '@manyun/base-ui.ui.message';
import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';
import { generateTicketLocation } from '@manyun/ticket.route.ticket-routes';

import { VisitorsTicketMutator } from '../visitors-ticket-mutator';

export type VisitsMutatorProps = {
  initialReceptionist?: {
    type: 'external' | 'internal';
    label: string;
    value: string;
  };
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  UserSelect?: React.ComponentType<any>;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  CustomerUpload?: React.ComponentType<any>;
  accessDirectUrl?: string;
  visitTypeMetaType?: string[];
};

export function VisitsMutator({
  accessDirectUrl,
  visitTypeMetaType,
  initialReceptionist,
  UserSelect,
  CustomerUpload,
}: VisitsMutatorProps) {
  const history = useHistory();
  const { id } = useParams<{ id?: string }>();
  const { search } = useLocation();
  const { variables } = getLocationSearchMap<{ variables?: string }>(search);
  let varsMap: { relateTaskType: string; relateTaskNo: string; idcTag: string } | undefined;
  try {
    if (variables) {
      varsMap = JSON.parse(variables);
    }
  } catch (error) {
    // ignore...
  }

  return (
    <VisitorsTicketMutator
      defaultShowAuthorizedNotify={false}
      ticketNumber={id}
      formId={nanoid(6)}
      accessDirectUrl={accessDirectUrl}
      visitTypeMetaType={visitTypeMetaType}
      unusedFormItems={['relatedTicketType', 'relatedTicketNumber']}
      initialValues={{
        relatedTicketType: varsMap?.relateTaskType ? [varsMap.relateTaskType] : undefined,
        relatedTicketNumber: varsMap?.relateTaskNo,
        idc: varsMap?.idcTag ? [varsMap.idcTag] : undefined,
        receptionist: initialReceptionist,
        authorizedNotify: 'disabled',
      }}
      UserSelect={UserSelect}
      CustomerUpload={CustomerUpload}
      onSuccess={ticketNumber => {
        message.success('创建成功！');
        setTimeout(() => {
          history.push(generateTicketLocation({ ticketType: 'visitor', id: ticketNumber }));
        }, 500);
      }}
      onCancel={() => {
        history.goBack();
      }}
    />
  );
}
