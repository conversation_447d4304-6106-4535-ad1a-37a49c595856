import {
  DeleteOutlined,
  ExclamationCircleFilled,
  QuestionCircleOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import { i18n } from '@teammc/i18n';
import debounce from 'lodash.debounce';
import uniq from 'lodash.uniq';
import moment from 'moment';
import type { Moment } from 'moment';
import { nanoid } from 'nanoid';
import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useHistory } from 'react-router-dom';
import { useLatest } from 'react-use';

import { getUserInfo } from '@manyun/auth-hub.cache.user';
import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { AutoComplete } from '@manyun/base-ui.ui.auto-complete';
import { Button } from '@manyun/base-ui.ui.button';
import { Cascader } from '@manyun/base-ui.ui.cascader';
import type { CertificateType } from '@manyun/base-ui.ui.certificate-type-select';
import { Container } from '@manyun/base-ui.ui.container';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import type { EditableFormInstance } from '@manyun/base-ui.ui.editable-pro-table';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Form } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { Popover } from '@manyun/base-ui.ui.popover';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import { fetchPagedVendors } from '@manyun/crm.service.fetch-paged-vendors';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import type { McUploadFileJSON } from '@manyun/dc-brain.model.mc-upload-file';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { McUpload as Upload } from '@manyun/dc-brain.ui.upload';
import { RolesSelect } from '@manyun/iam.ui.roles-select';
import { useMetadata, useMetadataTree } from '@manyun/resource-hub.gql.client.metadata';
import type { MetadataTree } from '@manyun/resource-hub.gql.client.metadata';
import { useSpaces } from '@manyun/resource-hub.gql.client.resources';
import { fetchRoomList } from '@manyun/resource-hub.service.fetch-room-list';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';
import { MetaTypeCascader } from '@manyun/resource-hub.ui.meta-type-cascader';
import {
  useLazyExistingProtectionDate,
  useLazyVisitTicket,
  useVisitTicketMutation,
} from '@manyun/sentry.gql.client.visits';
import type { VisitorNotifyInfoInput } from '@manyun/sentry.gql.client.visits';
import { businessClassificationMappingQuery } from '@manyun/sentry.service.business-classification-mapping-query';
import { fetchVisitTicketCustomInfos } from '@manyun/sentry.service.fetch-visit-ticket-custom-infos';
import { depositaryDraftEntry } from '@manyun/sentry.services.depositary-draft-entry';
import type { Visitor } from '@manyun/sentry.ui.visitors-editable-table';
import {
  useUpdateApproveAreaMutation,
  useUpdateAssigneeConfigMutation,
  useUpdateOrderAssigneeMutation,
} from '@manyun/ticket.gql.client.tickets';
import { TicketTypesCascader } from '@manyun/ticket.ui.ticket-types-cascader';

import { VisitorsEditableTable } from '../../../visitors-editable-table';
import {
  LOCALE_SCOPE_NAME,
  LOCALE_UNIQ_KEY,
  generateFiledKey,
  zhCN as visitorsTicketMutatorZhCN,
} from '../../locales/index.js';
import { VisitorEmailFormList } from '../visitor-email-form-list.js';
import {
  PersonnelAccessFormContextProvider,
  usePersonnelAccessFormContext,
} from './personnel-access-form-context';

export const formItem = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
};

export const formItemLayoutWithOutLabel = {
  wrapperCol: {
    span: 20,
    offset: 4,
  },
};

const keys = [
  'visitType',
  'relatedTicketType',
  'relatedTicketNumber',
  'title',
  'purpose',
  'allowedTimeRange',
  'idc',
  'authorizedArea',
  'assignees',
  'receptionist',
  'authorizedNotify',
  'insideVisitorNotices',
  'fileInfoList',
] as const;
export type FieldName = (typeof keys)[number];

export type AuthorizedNotify = 'email' | 'disabled';

export type LabelInValue = {
  id?: string | null;
  value: string | null;
  label: string;
};
export type ReceptionistType = 'roles' | 'users';

export type FormValues = {
  creator?: {
    id: number;
    name: string;
  };

  visitType?: string[];
  relatedTicketType?: string[];
  relatedTicketNumber?: string;
  title?: string;
  purpose?: string;
  allowedTimeRange?: [Moment, Moment];
  idc?: string[];
  authorizedArea?: {
    spaceGuids: string[] | string[][];
    allSpaceGuids: string[];
  };
  /**
   * 指派人类型
   */
  receptionistType?: ReceptionistType;
  /**
   * 指派人(多选)/指派角色(单选)
   */
  receptionist?: LabelInValue[] | LabelInValue;
  visitors?: Visitor[];
  authorizedNotify?: AuthorizedNotify;
  insideVisitorNotices?: { label: string; value: string; email: string }[];
  outSideVisitorNotices?: string[];
  fileInfoList?: McUploadFileJSON[];
};

export type VisitorsTicketMutatorProps = {
  /**
   * 用于提交的唯一性，避免重复提交
   */
  formId?: string;
  formLayout?: 'horizontal' | 'vertical' | 'inline';
  renderExistingVisitTicketAsLink?: boolean;
  ticketNumber?: string;
  initialValues?: FormValues;
  visitTypeMetaType?: string[];
  accessDirectUrl?: string;
  unusedFormItems?: FieldName[];
  // defaultShowAuthorizedNotify?: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  UserSelect?: React.ComponentType<any>;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  CustomerUpload?: React.ComponentType<any>;
  /**
   * 创建 / 更新 成功
   */
  onSuccess?: (ticketNumber: string) => void;
  onCancel?: () => void;
};

i18n.current.addResourceBundle('zh-CN', LOCALE_SCOPE_NAME, {
  [LOCALE_UNIQ_KEY]: visitorsTicketMutatorZhCN,
});
const formItemWidth = 768;
function transformToTree(data) {
  const tree = [];
  const map = new Map(); // 用于快速查找父级

  data.forEach(item => {
    const {
      id,
      relateType,
      sourceCode,
      sourceName,
      sourceSubCode,
      sourceSubName,
      targetCode,
      targetSubCode,
      tag,
      sort,
    } = item;

    // 如果一级节点不存在，创建并添加到树
    if (!map.has(sourceCode)) {
      const parentNode = {
        relateType,
        value: sourceCode,
        label: sourceName,
        tag,
        sort,
        children: tag ? null : [],
        _children: [],
      };
      tree.push(parentNode);
      map.set(sourceCode, parentNode); // 保存引用
    }

    // 添加二级节点
    const parentNode = map.get(sourceCode);
    const childNode = {
      id, // 保留二级节点的 id
      parentId: parentNode.value, // 指向父级的 value
      relateType,
      value: sourceSubCode,
      label: sourceSubName,
      isOther: targetCode === 'other',
    };
    // 特殊处理 有政府 或 商务标签  不展示二级 ,_children 用来默认携带 二级数据下的第一个
    !!!parentNode.tag && parentNode.children.push(childNode);
    parentNode._children.push(childNode);
  });

  // 根据sort字段对数组进行排序
  tree.sort((a, b) => {
    // 如果sort字段存在，则按sort排序
    if (a.sort !== undefined && b.sort !== undefined) {
      return a.sort - b.sort;
    }
    // 如果只有a有sort字段，a排在前面
    if (a.sort !== undefined) {
      return -1;
    }
    // 如果只有b有sort字段，b排在前面
    if (b.sort !== undefined) {
      return 1;
    }
    // 如果都没有sort字段，保持原有顺序
    return 0;
  });
  //入室进入申请下的 元素权限 code visittype-governments-tag 政府， visittype-commercial-tag 商务  需要做下权限过滤
  return tree;
}

function extractValuesByType(dataArray) {
  if (!Array.isArray(dataArray)) {
    return;
  }
  const result = {
    types: ['ROOM_CATEGORY'],
    roomCategoryList: [],
    roomCategoryGuidList: [],
    allRoomGuids: [],
  };
  dataArray.forEach(item => {
    // 根据 types 中声明的类型进行匹配
    if (result.types.includes(item.type)) {
      // 将最后一部分添加到 roomCategoryList
      const lastPart = item.value.split('.').pop();
      result.roomCategoryList.push(lastPart);

      // 将完整的 value 存入 roomCategoryGuidList
      result.roomCategoryGuidList.push(item.value);
    } else {
      // 不符合 types 的项添加到 allRoomGuids
      result.allRoomGuids.push(item.value);
    }
  });
  return result;
}
function checkWarnings(businessArray, originalDataArray) {
  if (!Array.isArray(businessArray) && !Array.isArray(originalDataArray)) {
    return false;
  }
  for (const businessItem of businessArray) {
    const customType = businessItem.custom?.type;
    if (customType) {
      // 在 originalDataArray 中查找对应的 dataItem
      for (const dataItem of originalDataArray) {
        if (dataItem.code === customType && dataItem.description) {
          try {
            const parsedDescription = JSON.parse(dataItem.description);

            if (parsedDescription.warn === true) {
              return true;
            }
          } catch (e) {
            console.error('Failed to parse JSON:', e);
            return false;
          }
        }
      }
    }
  }

  return false;
}

const generateBlockAssigneeList = formValues => {
  const blockAssigneeList = [];

  Object.keys(formValues).forEach(key => {
    const assigneeMatch = key.match(/area-assignees-(\d+)/);
    if (assigneeMatch) {
      const keyIndex = assigneeMatch[1];
      const assignees = formValues[key];
      const blockKey = `area-block-${keyIndex}`;
      const block = formValues[blockKey];

      if (block && assignees) {
        blockAssigneeList.push({
          blockGuid: block[0], // 楼栋的唯一标识
          assigneeList: assignees.map(assignee => ({
            id: assignee.id,
            userName: assignee.userName,
          })), // 对应的对接人数组
        });
      }
    }
  });

  return blockAssigneeList;
};

const getAreaList = formValues => {
  // 第一类数据：`area-area-*` 为空时对应的 `area-block-*`
  const areaEmptyList = Object.entries(formValues)
    .filter(
      ([key, value]) => key.startsWith('area-block-') && Array.isArray(value) && value.length > 0
    )
    .map(([blockKey, blockGuids]) => {
      const areaKey = blockKey.replace('area-block-', 'area-area-'); // 找到对应的区域 key
      const areaData = formValues[areaKey]; // 获取区域数据

      if (!areaData || !areaData.spaceOptions) {
        // 如果区域为空，则将楼栋数据处理为结果
        return blockGuids.map(blockGuid => ({
          blockGuid,
          spaceOptions: [], // 区域为空
        }));
      }
      return null; // 区域不为空时，不需要加入第一类数据
    })
    .filter(Boolean) // 过滤掉不需要的 `null`
    .flat(); // 拍平结果

  // 第二类数据：正常的区域数据
  const areaList = Object.entries(formValues)
    .filter(([key, value]) => key.startsWith('area-area-') && value?.spaceOptions) // 筛选出有效的区域数据
    .flatMap(([_, value]) => value.spaceOptions); // 拍平 `spaceOptions`

  // 返回两类数据的数组
  return [areaEmptyList, areaList];
};
function findNodeInTree(tree, value) {
  for (const node of tree) {
    // 判断当前节点是否匹配
    if (node.value === value) {
      return node;
    }

    // 判断子节点是否匹配（假设子节点保存在 children 数组中）
    if (node.children && node.children.length > 0) {
      const result = node.children.find(child => child.value === value);
      if (result) {
        return result;
      }
    }
  }

  // 如果没有找到返回 null
  return null;
}
export function PersonnelAccessRequestsContent({
  formId,
  formLayout,
  renderExistingVisitTicketAsLink,
  ticketNumber,
  visitTypeMetaType = ['VISITOR', 'VISITOR_THIRD_CATEGORY'],
  initialValues,
  unusedFormItems,
  // UserSelect,
  CustomerUpload,
  accessDirectUrl,
  // defaultShowAuthorizedNotify = true,
  onSuccess,
  onCancel,
}: VisitorsTicketMutatorProps) {
  const history = useHistory();
  const { userId } = getUserInfo();
  // @ts-ignore
  const [, { checkUserId, checkCode }] = useAuthorized();

  const { t } = useTranslation(LOCALE_SCOPE_NAME);
  const isEditing = !!ticketNumber;
  // const hasInitialRelatedTicketType = !!initialValues?.relatedTicketType;
  // const hasInitialRelatedTicketNumber = !!initialValues?.relatedTicketNumber;
  const hasInitialIdc = !!initialValues?.idc;
  const [configUtil] = useConfigUtil();
  const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket');
  const { dockingStation } = ticketScopeCommonConfigs?.entryOfPersons?.features ?? {
    dockingStation: false,
  };

  const onSuccessRef = useLatest(onSuccess);
  const [mutateVisitTicket, { loading }] = useVisitTicketMutation({
    onCompleted(data) {
      if (!data.mutateVisitTicket?.success) {
        message.error(data.mutateVisitTicket?.message);
        return;
      }
      onSuccessRef.current?.(data.mutateVisitTicket.ticketNumber!);
    },
  });

  const [getAssigneeConfig, { loading: assigneeConfigLoading }] = useUpdateAssigneeConfigMutation();
  const [getApproveArea, { loading: approveAreaLoading }] = useUpdateApproveAreaMutation();
  const [getOrderAssignee, { loading: orderAssigneeLoading }] = useUpdateOrderAssigneeMutation();

  const [form] = Form.useForm<FormValues>();
  const [areaForm] = Form.useForm<FormValues>();
  const [editAreaData, setEditAreaData] = React.useState();
  const [purpose, setPurpose] = React.useState<string>();

  const [visitTypeTag, setVisitTypeTag] = React.useState({});
  const [open, setOpen] = React.useState(false);
  const [getVisitTicket] = useLazyVisitTicket({
    onCompleted(data) {
      if (!data.visitTicket) {
        return;
      }
      const defaultAuthorizedAreas: string[][] = (data.visitTicket.authorizedArea ?? []).map(
        ({ code, type }) =>
          type === 'BLOCK' ? [code] : [code.split('.').slice(0, 2).join('.'), code]
      );
      const defaultAuthorizedNotifyCheck =
        (data.visitTicket.visitorNotifyList ?? []).length > 0 ? 'email' : 'disabled';

      let receptionistType: ReceptionistType | undefined;
      let receptionist: LabelInValue[] | LabelInValue | undefined;
      if ((data.visitTicket?.assigneeList ?? []).length > 0) {
        receptionistType = 'users';
        receptionist = (data.visitTicket.assigneeList ?? []).map(user => ({
          label: user.name,
          value: user.id.toString(),
        })) as LabelInValue[];
      } else if ((data.visitTicket?.assigneeRoles ?? []).length === 1) {
        receptionistType = 'roles';
        receptionist = {
          id: data.visitTicket.assigneeRoles![0].id,
          label: data.visitTicket.assigneeRoles![0].name,
          value: data.visitTicket.assigneeRoles![0].code,
        } as LabelInValue;
      }

      if (data.visitTicket.bizTag && ticketNumber) {
        setPurpose(data.visitTicket.purpose?.trim());
        setSpecialtyVisitForm({ bizTag: data.visitTicket.bizTag });
      }

      const mapVisitors = (data.visitTicket.visitors ?? []).map(
        ({
          type,
          identificationType,
          ICN,
          LPN,
          companyName,
          operationalAuthorization,
          mobileAreaCode,
          mobile,
          position,
          personalGoods,
          ...restVsitor
        }) => ({
          ...restVsitor,
          mobile: mobileAreaCode || mobile ? [mobileAreaCode ?? '', mobile ?? ''] : undefined,
          visitorType: type,
          id: nanoid(6),
          position: position ?? undefined,
          personalGoods: personalGoods ?? undefined,
          LPN: LPN ?? undefined,
          companyName: companyName ?? undefined,
          operationalAuthorization: operationalAuthorization ?? undefined,
          identification:
            identificationType || ICN
              ? [(identificationType as CertificateType) ?? '', ICN ?? '']
              : undefined,
        })
      );
      console.log(data.visitTicket.visitors, mapVisitors, 'data.visitTicket-121223');
      form.setFieldsValue({
        visitType:
          data.visitTicket.subType && data.visitTicket.thirdType
            ? data.visitTicket.bizTag
              ? [data.visitTicket.subType]
              : [data.visitTicket.subType, data.visitTicket.thirdType]
            : undefined,
        relatedTicketType: data.visitTicket.relatedTicketType
          ? [data.visitTicket.relatedTicketType]
          : undefined,
        relatedTicketNumber: data.visitTicket.relatedTicketNumber ?? undefined,
        title: data.visitTicket.title?.replace(
          data.visitTicket.bizTag ? '参观申请单' : '人员进入申请单',
          ''
        ),
        purpose: data.visitTicket.purpose?.trim(),
        allowedTimeRange: [
          moment(data.visitTicket.allowedTimeRage[0]),
          moment(data.visitTicket.allowedTimeRage[1]),
        ],
        idc: [data.visitTicket.idc],
        authorizedArea: data.visitTicket.authorizedArea
          ? {
              spaceGuids: defaultAuthorizedAreas,
              allSpaceGuids: data.visitTicket.authorizedArea.map(({ code }) => code),
            }
          : undefined,
        receptionistType,
        receptionist,
        visitors: mapVisitors,
        authorizedNotify: defaultAuthorizedNotifyCheck,
        insideVisitorNotices: data.visitTicket.visitorNotifyList
          ?.filter(notify => notify.userType === 'SYSTEM')
          .map(notify => ({ id: notify.userId, name: notify.userName, email: notify.email })),
        outSideVisitorNotices: data.visitTicket.visitorNotifyList
          ?.filter(notify => notify.userType === 'OUTSIDE')
          .map(notify => notify.email),
        fileInfoList: data.visitTicket.fileInfoList,
      });
    },
  });
  // 获取重保日
  const [checkExistingProtectionDate, { data: protectionDate }] = useLazyExistingProtectionDate();
  // 获取违禁品元数据配置
  const { data } = useMetadata({ variables: { type: 'CONTRABAND' }, fetchPolicy: 'network-only' });
  // 获取授权时间元数据配置，限制时间跨度
  const { data: visitConfig } = useMetadata({ variables: { type: 'VISITOR_TIME' } });
  const { data: visitTypesData } = useMetadataTree({
    variables: { type: visitTypeMetaType.join(',') },
  });

  const allAssigneeRef = React.useRef([]);
  const initTreeDataRef = React.useRef(null);
  const disabledSpaceCodes = React.useRef([]);
  const visitTypeOptionRef = React.useRef([]);
  const validateFileInfoList = React.useRef({
    initExamine: false,
    examine: debounce(() => {
      form.validateFields(['fileInfoList']).catch(() => {});
    }, 500),
  });
  const savingTaskNo = React.useRef<undefined | string>(undefined);

  const [businessCagoteryOptions, setBusinessCagoteryOptions] = React.useState([]);
  const [areaAllSpaceGuids, setAreaAllSpaceGuids] = React.useState();
  const [isOther, setIsOther] = React.useState(false);

  const idc = Form.useWatch('idc', form);
  const authorizedArea = Form.useWatch('authorizedArea', form);

  const allowedTimeRange = Form.useWatch('allowedTimeRange', form);
  // const insideVisitorNotices = Form.useWatch('insideVisitorNotices', form);
  // const outSideVisitorNotices = Form.useWatch('outSideVisitorNotices', form);
  // const receptionistType = Form.useWatch('receptionistType', form);
  // const authorizedNotify = Form.useWatch('authorizedNotify', form);

  const watchVisitType = Form.useWatch('visitType', form);
  const isValidRef = React.useRef<Record<string, boolean>>({});
  const editableFormRef = React.useRef<EditableFormInstance<Visitor>>();

  const contrabandItems = React.useMemo(
    () => (data?.metadata ?? []).map(item => item.name),
    [data?.metadata]
  );
  const timeLimitDays = React.useMemo(() => {
    const timeConfig = visitConfig?.metadata?.find(record => record.code === idc?.[0]);
    return timeConfig ? Number(timeConfig.name) : null;
  }, [idc, visitConfig?.metadata]);

  // 政府或商务类型
  const isSpecialtyVisitType = React.useMemo(
    () => Object.keys(visitTypeTag)?.length >= 1,
    [visitTypeTag]
  );
  // 政府类型
  const isGovernments = React.useMemo(
    () => Object.keys(visitTypeTag)?.length >= 1 && Object.keys(visitTypeTag)[0] === 'GOVERNMENT',
    [visitTypeTag]
  );

  // 提交
  const onSubmit = async (mode?: string) => {
    let values = {};
    let areaValues = {};
    try {
      validateFileInfoList.current.initExamine = true;
      if (mode === 'drafts') {
        values = { ...form.getFieldsValue() };
        areaValues = { ...areaForm.getFieldsValue() };
        if (!values?.visitType) {
          message.error('请填写业务分类');
          return;
        }
        if (!values?.title) {
          message.error('请填写工单标题');
          return;
        }
        if (!values?.idc?.[0]) {
          message.error('请填写机房');
          return;
        }
      } else {
        const [_values, _areaValues] = await Promise.all([
          form.validateFields(),
          areaForm.validateFields(),
          editableFormRef.current?.validateFields(),
        ]);
        values = _values;
        areaValues = _areaValues;
      }

      const { visitors } = values;
      visitors?.forEach(visitor => {
        if (
          visitor.personalGoods &&
          visitor.personalGoods.some(good =>
            contrabandItems.some(contrabandItem => {
              const pattern = new RegExp(contrabandItem);
              return pattern.test(good);
            })
          )
        ) {
          throw new Error(`Visitor(${visitor.id}) is not valid!`);
        }
      });
      const filterVisitors =
        values.visitors
          ?.map(visitor => ({
            identificationType: visitor.identification?.[0],
            ICN: visitor.identification?.[1],
            LPN: visitor.LPN,
            companyName: visitor.companyName,
            mobileAreaCode: visitor.mobile?.[0],
            mobile: visitor.mobile?.[1],
            name: visitor.name!,
            operationalAuthorization: visitor.operationalAuthorization,
            type: visitor.visitorType!,
            position: visitor.position,
            personalGoods: visitor.personalGoods,
          }))
          .filter(visitor => {
            return [
              visitor.ICN,
              visitor.LPN,
              visitor.companyName,
              visitor.mobile,
              visitor.name,
              visitor.type,
              visitor.position,
            ].some(val => val != null);
          }) || [];

      let visitorNotices: VisitorNotifyInfoInput[] = [];
      const { authorizedNotify, insideVisitorNotices = [], outSideVisitorNotices = [] } = values;

      if (authorizedNotify === 'email') {
        if (!insideVisitorNotices.length && !outSideVisitorNotices.length) {
          message.error(
            t(generateFiledKey('message.noticeUserMustRequiredError'), {
              defaultValue: '内部人员或外部人员必填一项！',
            })
          );
          throw new Error(`authorizedNotify is not valid!`);
        }
        visitorNotices = [
          ...insideVisitorNotices.map(item => ({
            type: 'internal',
            email: item.email,
            userId: item.value,
            userName: item.label,
          })),
          ...outSideVisitorNotices.map(email => ({
            type: 'external',
            email,
          })),
        ] as VisitorNotifyInfoInput[];
      } else {
        visitorNotices = [];
      }

      const areaList = getAreaList(areaValues);

      const category = extractValuesByType(areaList[1]);

      if (category?.roomCategoryList?.length >= 1 && category?.roomCategoryGuidList?.length >= 1) {
        const { error, data } = await fetchRoomList({
          pageNum: 1,
          pageSize: 5000,
          idcTag: idc?.[0],
          operationStatus: 'ON',
          roomCategoryList: category?.roomCategoryList,
          roomCategoryGuidList: category?.roomCategoryGuidList,
          isAuth: false,
        });
        if (data?.data) {
          data.data?.forEach(item => category?.allRoomGuids.push(item.guid));
        }
      }

      const draftsInfo = Object.keys(visitTypeTag)?.[0]
        ? {
            bizTag: Object.keys(visitTypeTag)?.[0],
            receptionName: values.receptionName?.label,
            receptionId:
              typeof values.receptionName?.id === 'string' ? undefined : values.receptionName?.id,
            receptionPhone: values.receptionPhone,
            guestCompany: values.guestCompany,
            guestNum: values.guestNum,
          }
        : {};
      const areas = areaList[0]
        ?.map(i => i.blockGuid)
        .filter(i => {
          if (disabledSpaceCodes.current.length >= 1) {
            return i.split('.')?.[1] !== 'Z';
          }
          return i;
        })
        .concat(category?.allRoomGuids || []);
      const param = {
        variables: {
          formId,
          allowedTimeRange: [
            values.allowedTimeRange?.[0].startOf('day').valueOf(),
            values.allowedTimeRange?.[1].endOf('day').startOf('second').valueOf(),
          ],
          idc: values.idc![0],
          authorizedArea: [...new Set([...disabledSpaceCodes.current, ...areas])],
          purpose: isSpecialtyVisitType ? (purpose ?? ' ') : (values.purpose ?? ' '),
          relatedTicketNumber: values.relatedTicketNumber,
          relatedTicketType: values.relatedTicketType?.[0],
          ticketNumber: ticketNumber ?? savingTaskNo.current,
          title: `${values.title}${isSpecialtyVisitType ? '参观申请单' : '人员进入申请单'}`,
          visitType: values.visitType![0],
          visitSecondaryType: isSpecialtyVisitType
            ? Object.values(visitTypeTag)[0]
            : values.visitType![1],
          visitors: filterVisitors,
          visitorNotices,
          fileIdList: values.fileInfoList
            ? values.fileInfoList.map(file => file.id ?? Number(file.patialPath))
            : undefined,
          fileInfoList: values.fileInfoList
            ? values.fileInfoList.map(({ uploadUser, ...file }) => ({
                ...file,
                uploadUser:
                  !uploadUser.id || !uploadUser.name
                    ? {
                        id: 0,
                        name: 'SYSTEM',
                      }
                    : { id: uploadUser.id, name: uploadUser.name },
                __typename: undefined,
              }))
            : undefined,
          assigneeUsers:
            values.receptionistType === 'users' &&
            Array.isArray(values.receptionist) &&
            values.receptionist.length > 0
              ? values.receptionist.map(user => ({
                  id: user.value,
                  userName: user.label,
                }))
              : undefined,
          assigneeRoles:
            values.receptionistType === 'roles' &&
            !Array.isArray(values.receptionist) &&
            values.receptionist
              ? [
                  {
                    id: values.receptionist.id!,
                    roleCode: values.receptionist.value!,
                    roleName: values.receptionist.label!,
                  },
                ]
              : undefined,
          blockAssigneeList: isGovernments
            ? []
            : generateBlockAssigneeList(areaValues).filter(
                item => Array.isArray(item.assigneeList) && item.assigneeList.length >= 1
              ),
          ...draftsInfo,
        },
      };

      if (mode === 'drafts') {
        return Promise.resolve(param.variables);
      }
      mutateVisitTicket(param);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log(error);
    }
  };
  // 保存草稿
  const savingDrafts = async () => {
    const svcQuery = await onSubmit('drafts');
    if (svcQuery) {
      const params = {
        formId: svcQuery.formId,
        // orderType: svcQuery.source === 'customers-service' ? 'VISITOR' : undefined,
        taskNo: svcQuery.ticketNumber,
        approveEndTime: svcQuery.allowedTimeRange[1],
        approveStartTime: svcQuery.allowedTimeRange[0],
        enterReason: svcQuery.purpose,
        idcTag: svcQuery.idc,
        approveArea: svcQuery.authorizedArea.join(','),
        relateTaskNo: svcQuery.relatedTicketNumber,
        relateTaskType: svcQuery.relatedTicketType,
        taskSubType: svcQuery.visitType,
        reasonType: svcQuery.visitSecondaryType,
        taskTitle: svcQuery.title,
        visitorInfos: svcQuery.visitors.map(visitor => ({
          certificateType: visitor.identificationType,
          companyName: visitor.companyName,
          phoneCountryCode: visitor.mobileAreaCode,
          contactWay: visitor.mobile,
          identityNo: visitor.ICN,
          name: visitor.name,
          operable: visitor.operationalAuthorization === 'yes',
          plateNo: visitor.LPN,
          visitorType: visitor.type,
          position: visitor.position,
          personalItem: visitor.personalGoods?.join(','),
        })),
        visitorNotifyList: svcQuery.visitorNotices?.map(notice => ({
          email: notice.email,
          userType: notice.type === 'external' ? 'OUTSIDE' : 'SYSTEM',
          userId: notice.userId,
          userName: notice.userName,
        })),
        fileInfoList: svcQuery.fileInfoList?.map(file => McUploadFile.fromJSON(file).toApiObject()),
        fileIdList: svcQuery.fileIdList,
        assigneeList: svcQuery.assigneeUsers,
        assigneeRoleList: svcQuery.assigneeRoles,
        blockAssigneeList: svcQuery.blockAssigneeList,
        bizTag: svcQuery.bizTag,
        receptionId: svcQuery.receptionId,
        receptionName: svcQuery.receptionName,
        receptionPhone: svcQuery.receptionPhone,
        guestCompany: svcQuery.guestCompany,
        guestNum: svcQuery.guestNum,
      };

      const { data, error } = await depositaryDraftEntry(params);

      if (error) {
        return;
      }
      if (data) {
        savingTaskNo.current = data;
        message.success('保存草稿成功');
        history.push('/page/tickets/visitor');
      }
    }
  };
  // 映射特殊人员
  const setSpecialtyVisitForm = async ({ bizTag }: { bizTag: string }) => {
    if (ticketNumber) {
      const { data: customInfos, error } = await fetchVisitTicketCustomInfos({
        ticketNumber,
        bizTag,
      });
      if (error) {
        return;
      }
      if (customInfos?.visitors && customInfos.visitors?.length >= 1) {
        const { guestNum, companyName, name, contactWay, userId } = customInfos.visitors[0];
        form.setFieldsValue({
          guestNum,
          guestCompany: companyName,
          receptionName: { id: userId ?? name, value: userId ?? name, label: name, name },
          receptionPhone: contactWay,
        });
      }
    }
  };

  useSpaces({
    variables: {
      nodeTypes: ['BLOCK', 'ROOM_CATEGORY', 'ROOM_TYPE', 'ROOM'],
      includeVirtualBlocks: false,
      idc: idc?.[0],
      queryRoomProperties: true,
      onlyEnableBlock: true,
      onlyEnableRoom: true,
    },
    onCompleted(data) {
      initTreeDataRef.current = data?.spaces;
    },
  });

  React.useEffect(() => {
    // 阳高 做的中联和字节映射配置逻辑 导致 需单独处理
    // relateType	number	关联类型，0:中联关联字节；1:字节关联中联
    (async () => {
      const { data, error } = await businessClassificationMappingQuery({ relateType: 0 });
      if (error) {
        message.error(error.message);
        return;
      }
      if (data?.data) {
        const treeData =
          transformToTree(data.data)?.filter(i => {
            if (i.tag === 'BUSINESS') {
              return checkCode('visittype-commercial-tag');
            }
            if (i.tag === 'GOVERNMENT') {
              return checkCode('visittype-governments-tag');
            }
            return i;
          }) || [];
        setBusinessCagoteryOptions(treeData);

        // 如果有数据，并且不是编辑模式， 业务类型设置默认值
        if (treeData.length > 0 && !ticketNumber) {
          const firstLevelOption = treeData[0];
          if (!firstLevelOption?.tag && firstLevelOption?.children?.length > 0) {
            form.setFieldValue('visitType', [
              firstLevelOption.value,
              firstLevelOption.children[0].value,
            ]);
          }
          if (firstLevelOption?.tag && firstLevelOption?._children?.length > 0) {
            form.setFieldValue('visitType', [
              firstLevelOption.value,
              firstLevelOption._children[0].value,
            ]);
          }
        }
      }
    })();
  }, []);

  React.useEffect(() => {
    if (businessCagoteryOptions && watchVisitType && Array.isArray(watchVisitType)) {
      setIsOther(findNodeInTree(businessCagoteryOptions, watchVisitType[1])?.isOther ?? false);
      const info = businessCagoteryOptions.find(i => i.value === watchVisitType[0]);
      if (info?.tag) {
        visitTypeOptionRef.current = [info];
        setVisitTypeTag({ [info.tag]: info?._children?.[0]?.value });
      } else {
        setVisitTypeTag({});
      }
      if (!info) {
        // Options和映射值不匹配（无资源选项等） 则重置
        form.setFieldValue('visitType', undefined);
      }
    }
  }, [watchVisitType, businessCagoteryOptions]);

  React.useEffect(() => {
    // 获取已配置的 对接人
    getAssigneeConfig({
      variables: {
        query: {
          customerNo: 'SYSTEM',
        },
      },
      onCompleted(rspData) {
        if (rspData) {
          const { data } = rspData?.updateChangeAssigneeConfig;
          allAssigneeRef.current = data;
        }
      },
    });
  }, [ticketNumber]);

  React.useEffect(() => {
    if (ticketNumber) {
      function combineData(taskData, areaData) {
        // 先将对接人数据按 blockGuid 生成映射
        const taskMap = {};
        taskData.forEach(task => {
          taskMap[task.blockGuid] = task.assigneeList || [];
        });

        // 遍历区域数据，按 blockGuid 聚合区域数据
        const result = [];
        const blockMap = {};

        areaData.forEach(area => {
          const { blockGuid, resourceNo } = area;
          if (!blockMap[blockGuid]) {
            // 如果该 blockGuid 不存在，初始化
            blockMap[blockGuid] = {
              blockGuid,
              assigneeList: taskMap[blockGuid] || [], // 匹配到的对接人或空数组
              rooms: [], // 存放区域数据
            };
            result.push(blockMap[blockGuid]);
          }
          blockMap[blockGuid].rooms.push(resourceNo);
        });

        return result;
      }
      function findPathAndOptions(node, targetValue, currentPath = []) {
        // 将当前节点的 value 添加到路径中
        const newPath = [...currentPath, node.value];

        // 如果找到目标值，立即返回路径和节点对象
        if (node.value === targetValue) {
          return { path: newPath, option: node };
        }

        // 如果有子节点，递归遍历子节点
        if (node.children) {
          for (const child of node.children) {
            const result = findPathAndOptions(child, targetValue, newPath);
            if (result) {
              return result;
            } // 找到结果后立即返回，停止进一步递归
          }
        }

        return null;
      }
      // 遍历输入的对象数组，基于 rooms 找到路径并更新 spaceGuids 和 spaceOptions
      function mapObjectsToPaths(objects, tree) {
        return objects.map(obj => {
          const spaceGuids = []; // 存储找到的路径的二维数组
          const allSpaceGuids = [];
          const spaceOptions = []; // 存储找到的完整 Option 对象

          // 遍历 rooms 数组，逐个匹配路径和对象
          obj.rooms.forEach(room => {
            for (const root of tree) {
              const result = findPathAndOptions(root, room);
              if (result) {
                spaceGuids.push(result.path); // 将找到的路径作为子数组添加到 spaceGuids
                allSpaceGuids.push(result.option.value);
                spaceOptions.push(result.option); // 将找到的 Option 对象追加到 spaceOptions
                break;
              }
            }
          });

          return {
            ...obj,
            spaceGuids, // 二维数组结构
            allSpaceGuids,
            spaceOptions,
          };
        });
      }

      // 初始化编辑 映射逻辑
      (async () => {
        const {
          // 重新发起 获取区域
          data: { updateChangeApproveArea },
        } = await getApproveArea({
          variables: {
            query: {
              taskNo: ticketNumber,
            },
          },
        });
        const {
          // 重新发起 获取对接人
          data: { updateChangeOrderAssignee },
        } = await getOrderAssignee({
          variables: {
            query: {
              taskNo: ticketNumber,
            },
          },
        });
        const { data: areaData } = updateChangeApproveArea;
        const { data: orderAssignee } = updateChangeOrderAssignee;
        const _editAreaData = mapObjectsToPaths(
          combineData(orderAssignee, areaData),
          initTreeDataRef.current
        );

        if (dockingStation) {
          setEditAreaData(() => _editAreaData);
        }
        getVisitTicket({ variables: { ticketNumber, maskedIdNumber: false } });
      })();
    }
  }, [getVisitTicket, ticketNumber]);

  React.useEffect(() => {
    // 单独触发校验
    if (validateFileInfoList.current.initExamine) {
      validateFileInfoList.current.examine();
    }
  }, [purpose, watchVisitType]);
  console.log(form.getFieldsValue()?.visitors, 'visitors-1212');
  return (
    <PersonnelAccessFormContextProvider
      value={{
        form,
        areaForm,
        ticketNumber,
        idc,
        visitType: watchVisitType,
        visitTypeTag,
        initTreeDataRef,
        isEditing,
      }}
    >
      <Form<FormValues>
        layout={formLayout}
        colon={false}
        labelCol={formLayout === 'vertical' ? undefined : { span: 3 }}
        wrapperCol={formLayout === 'vertical' ? undefined : formItem.wrapperCol}
        form={form}
        initialValues={initialValues}
      >
        <Space style={{ display: 'flex' }} direction="vertical">
          <Container size="large">
            <Typography.Title showBadge level={5}>
              {t(generateFiledKey('basicTitle'))}
            </Typography.Title>
            <div style={{ width: 946 }}>
              {!unusedFormItems?.includes('visitType') && (
                <Form.Item
                  label={
                    <>
                      <div
                        style={{
                          display: 'inline-block',
                          marginRight: '4px',
                          color: '#ff4d4f',
                          fontSize: '14px',
                          fontFamily: 'SimSun, sans-serif',
                          lineHeight: '1px',
                        }}
                      >
                        *
                      </div>
                      {t(generateFiledKey('visitType'))}
                    </>
                  }
                  name="visitType"
                  rules={[
                    {
                      validator: (_, value) => {
                        const option = visitTypeOptionRef.current;

                        if (!value || value.length === 0) {
                          return Promise.reject(
                            `${t(generateFiledKey('visitType'))}${t(generateFiledKey('select.required'))}`
                          );
                        }

                        // 如果 o[0]?.tag 为 true，只要一级就行
                        if (option?.[0]?.tag) {
                          return Promise.resolve();
                        }

                        // 否则必须选中两个层级
                        if (value.length < 2) {
                          return Promise.reject(
                            `${t(generateFiledKey('visitType'))}${t(generateFiledKey('select.required'))}`
                          );
                        }

                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  {/* 此处 阳高需求 添加的映射配置功能 需要查询配置 查询配置映射数据接口 relateType : 0 ，sourceCode 和 sourceSubCode组成树，change的时候。 targetCode 和 targetSubCode 为其他 oth 原因为必填 */}
                  {
                    <Cascader
                      style={{ width: formItemWidth }}
                      allowClear={false}
                      options={businessCagoteryOptions}
                      onChange={(v, o) => {
                        visitTypeOptionRef.current = o;
                        form.setFieldValue('visitType', v);
                      }}
                    />
                  }
                </Form.Item>
              )}
              {!unusedFormItems?.includes('title') && (
                <Form.Item
                  label={t(generateFiledKey('title'), { defaultValue: '工单标题' })}
                  name="title"
                  rules={[
                    {
                      required: true,
                      whitespace: true,
                      message: `${t(generateFiledKey('title'), {
                        defaultValue: '工单标题',
                      })}${t(generateFiledKey('input.required'), {
                        defaultValue: '必填!',
                      })}`,
                    },
                    {
                      type: 'string',
                      max: 20,
                      message: t(generateFiledKey('input.maxLen'), {
                        defaultValue: '最多输入 20 个字符！',
                        max: 20,
                      }),
                    },
                  ]}
                >
                  <Input
                    style={{ width: formItemWidth }}
                    placeholder={isSpecialtyVisitType ? '请输入来访人员单位简称' : '请输入公司简称'}
                  />
                  {/* <FuzzySearchInput
                     placeholder={isSpecialtyVisitType ? '请输入来访人员单位简称' : '请输入工单标题'}
                    style={{ width: formItemWidth }}
                  /> */}
                </Form.Item>
              )}

              {!unusedFormItems?.includes('idc') && (
                <Form.Item
                  label={t(generateFiledKey('idc'), { defaultValue: '进入园区' })}
                  name="idc"
                  rules={[
                    {
                      required: true,
                      message: `${t(generateFiledKey('idc'), {
                        defaultValue: '进入园区',
                      })}${t(generateFiledKey('select.required'), {
                        defaultValue: '必选!',
                      })}`,
                    },
                  ]}
                >
                  <LocationCascader
                    style={{ width: formItemWidth }}
                    authorizedOnly={!dockingStation}
                    nodeTypes={['IDC']}
                    disabled={hasInitialIdc}
                    allowClear={false}
                    onTreeDataChange={_data => {
                      if (hasInitialIdc) {
                        return;
                      }
                      if (_data.length === 1) {
                        form.setFieldValue('idc', [_data[0].value]);
                      }
                    }}
                    onChange={nextIdc => {
                      if (idc?.[0] === nextIdc[0]) {
                        return;
                      }
                      form.setFieldValue('authorizedArea', undefined);
                      form.setFieldValue('insideVisitorNotices', undefined);
                      areaForm.resetFields();
                      setEditAreaData(undefined);
                    }}
                  />
                </Form.Item>
              )}

              {/* -------------------------------------------- */}
              {!unusedFormItems?.includes('authorizedArea') && idc && (
                <AreaComponents
                  t={t}
                  assigneeData={allAssigneeRef.current}
                  editAreaData={editAreaData}
                  disabledSpaceCodes={disabledSpaceCodes}
                  isGovernments={isGovernments}
                  isSpecialtyVisitType={isSpecialtyVisitType}
                  setEditAreaData={setEditAreaData}
                  getAssigneeConfig={getAssigneeConfig}
                  setAreaAllSpaceGuids={setAreaAllSpaceGuids}
                />
              )}

              {!unusedFormItems?.includes('allowedTimeRange') && (
                <Form.Item
                  label={t(generateFiledKey('allowedTimeRange'), { defaultValue: '申请时间' })}
                  name="allowedTimeRange"
                  rules={[
                    {
                      required: true,
                      validator: (_, value) => {
                        if (!value || !value[0] || !value[1]) {
                          return Promise.reject(
                            new Error(
                              `${t(generateFiledKey('allowedTimeRange'), {
                                defaultValue: '申请时间',
                              })}${t(generateFiledKey('input.required'), {
                                defaultValue: '必填!',
                              })}`
                            )
                          );
                        }
                        if (
                          value[0] &&
                          moment().startOf('day').isAfter(moment(value[0]).startOf('day'))
                        ) {
                          return Promise.reject(
                            new Error(
                              `${t(generateFiledKey('startGTCurrent'), {
                                defaultValue: '申请需在更合理的时间区间',
                              })}`
                            )
                          );
                        }
                        if (
                          value[1] &&
                          moment().endOf('day').diff(moment(value[1]).endOf('day')) > 0
                        ) {
                          return Promise.reject(
                            new Error(
                              `${t(generateFiledKey('startGTCurrent'), {
                                defaultValue: '申请需在更合理的时间区间',
                              })}`
                            )
                          );
                        }
                        if (
                          timeLimitDays &&
                          value[0] &&
                          value[1] &&
                          moment(value[1].clone().startOf('day'))
                            .add(1, 'day')
                            .diff(value[0].clone().startOf('day'), 'day') > timeLimitDays
                        ) {
                          return Promise.reject(
                            new Error(
                              `${t(generateFiledKey('limitDays'), {
                                defaultValue: `申请开始时间与结束时间不可超过${timeLimitDays}天`,
                                day: timeLimitDays,
                              })}`
                            )
                          );
                        }
                        if (
                          dockingStation &&
                          (moment(value[0].clone().startOf('day')).isAfter(
                            moment().add(13, 'day')
                          ) ||
                            moment(value[1].clone().startOf('day')).isAfter(
                              moment().add(13, 'day')
                            ))
                        ) {
                          return Promise.reject(t(generateFiledKey('allowedTimeRangeLimitDates')));
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                  tooltip={
                    (protectionDate?.existingProtectionDate?.data ?? []).length > 0
                      ? t(generateFiledKey('protectionDateTip'))
                      : undefined
                  }
                >
                  <DatePicker.RangePicker
                    style={{ width: formItemWidth }}
                    showTime={{
                      defaultValue: [moment().startOf('day'), moment().endOf('day')], // 默认 00:00:00 - 23:59:59
                      disabledHours: () => [...Array(24).keys()], // 禁用小时选择
                      disabledMinutes: () => [...Array(60).keys()], // 禁用分钟选择
                      // disabledSeconds: () => [...Array(60).keys()], // 禁用秒选择
                    }}
                    format="YYYY-MM-DD HH:mm"
                    disabledDate={current => {
                      const disableHistoryDate =
                        current && current < moment().subtract(1, 'day').endOf('day');
                      return disableHistoryDate;
                    }}
                    onChange={value => {
                      if (value && value[0] && value[1]) {
                        const fixedValue = [
                          moment(value[0]).startOf('day'), // 00:00:00
                          moment(value[1]).endOf('day'), // 23:59:59
                        ];
                        checkExistingProtectionDate({
                          variables: {
                            startTime: fixedValue[0].valueOf(), // 传时间戳
                            endTime: fixedValue[1].valueOf(),
                          },
                        });

                        // 更新表单值
                        form.setFieldsValue({
                          allowedTimeRange: fixedValue,
                        });
                      }
                    }}
                  />
                </Form.Item>
              )}
              {isSpecialtyVisitType && (
                <>
                  <Form.Item
                    label="单位名称"
                    name="guestCompany"
                    rules={[
                      {
                        required: true,
                        message: `单位名称必填`,
                      },
                    ]}
                  >
                    <Input
                      style={{ width: formItemWidth }}
                      placeholder="请填写单位名称"
                      maxLength={50}
                    />
                  </Form.Item>
                  <Form.Item
                    label="申请人数"
                    name="guestNum"
                    rules={[
                      {
                        required: true,
                        message: `接待人姓名必填`,
                      },
                    ]}
                  >
                    <InputNumber
                      style={{ width: formItemWidth }}
                      placeholder="请填写申请人数"
                      maxLength={6}
                      min={1}
                      precision={0}
                      step={1}
                      stringMode={false}
                    />
                  </Form.Item>
                  <Form.Item
                    label="接待人姓名"
                    name="receptionName"
                    rules={[
                      {
                        required: true,
                        message: `接待人姓名必填`,
                      },
                    ]}
                  >
                    <UserSelect
                      style={{ width: formItemWidth }}
                      allowClear
                      labelInValue
                      reserveSearchValue
                      maxLength={6}
                    />
                  </Form.Item>
                  <Form.Item
                    label="接待人手机号"
                    name="receptionPhone"
                    rules={[
                      {
                        required: true,
                        validator: (_, value) => {
                          const reg = /^1[3456789]\d{9}$/;
                          if (!value) {
                            return Promise.reject(new Error('接待人手机号必填'));
                          } else if (!reg.test(value)) {
                            return Promise.reject(new Error('联系方式格式不正确'));
                          } else if (value.length > 11) {
                            return Promise.reject(new Error('最多输入 11 个字符'));
                          }
                          return Promise.resolve();
                        },
                      },
                    ]}
                  >
                    <Input style={{ width: formItemWidth }} placeholder="请填写手机号码" />
                  </Form.Item>
                </>
              )}
              {!unusedFormItems?.includes('purpose') && !isSpecialtyVisitType && (
                <Form.Item
                  label={t(generateFiledKey('purpose'))}
                  name="purpose"
                  rules={
                    watchVisitType?.[0] === 'other' || isOther
                      ? [
                          {
                            required: true,
                            whitespace: true,
                            message: `${t(generateFiledKey('purpose'))}${t(
                              generateFiledKey('input.required'),
                              {
                                defaultValue: '必填!',
                              }
                            )}`,
                          },
                        ]
                      : []
                  }
                >
                  <Input.TextArea style={{ width: formItemWidth }} maxLength={100} showCount />
                </Form.Item>
              )}
              {!unusedFormItems?.includes('fileInfoList') && (
                <Form.Item
                  name="fileInfoList"
                  label={
                    <>
                      <Typography.Text>
                        {isSpecialtyVisitType
                          ? '报备凭证'
                          : t(generateFiledKey('fileInfoList.label'))}
                      </Typography.Text>
                      <Tooltip
                        style={{ width: '294px' }}
                        title="进入生产区需完成客户机房参观报备，
                       格式要求：审批链接：https://XXXXXXXXX.com"
                      >
                        <QuestionCircleOutlined
                          style={{
                            margin: '0 4px',
                            fontSize: '14px',
                            color: 'rgba(0, 0, 0, 0.45)',
                          }}
                        />
                      </Tooltip>
                    </>
                  }
                  wrapperCol={{ span: 16 }}
                  valuePropName="fileList"
                  getValueFromEvent={value => {
                    if (typeof value === 'object') {
                      return value.fileList
                        ? value.fileList.map((file: McUploadFileJSON) =>
                            McUploadFile.fromJSON(file).toJSON()
                          )
                        : undefined;
                    }
                    return value;
                  }}
                  rules={[
                    {
                      validator: (_, value) => {
                        if (isSpecialtyVisitType && (!value || value.length === 0)) {
                          return Promise.reject(new Error('请上传报备凭证'));
                        }
                        if (isSpecialtyVisitType && !purpose?.trim()) {
                          return Promise.reject(new Error('请输入客户前置审批链接'));
                        }

                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  {CustomerUpload ? (
                    <CustomerUpload />
                  ) : (
                    <Upload
                      accept=".jpg,.jpeg,.png,.doc, .docx, .xls, .xlsx, .pdf"
                      showUploadList
                      allowDelete
                      maxFileSize={20}
                      maxCount={10}
                    >
                      <Space style={{ display: 'flex' }} direction="vertical">
                        <div>
                          <Button icon={<UploadOutlined />}>
                            {t(generateFiledKey('fileInfoList.upload'))}
                          </Button>
                          {isSpecialtyVisitType && (
                            <a
                              href="https://idc-service.bytedance.com/greatwall/external-visit/create"
                              target="_blank"
                              rel="noopener noreferrer"
                              onClick={e => e.stopPropagation()}
                              style={{ marginLeft: 8 }}
                            >
                              客户平台报备链接
                            </a>
                          )}
                        </div>
                        <Typography.Text type="secondary">
                          {t(generateFiledKey('fileInfoList.tooltip'))}：.jpg,.jpeg,.png,.doc,
                          .docx, .xls, .xlsx .pdf
                        </Typography.Text>
                        {isSpecialtyVisitType && (
                          <Input.TextArea
                            onPressEnter={e => e.stopPropagation()}
                            onClick={e => e.stopPropagation()}
                            style={{ width: formItemWidth }}
                            placeholder="请添加客户前置审批链接"
                            maxLength={100}
                            showCount
                            value={purpose}
                            onChange={e => {
                              setPurpose(e.target.value);
                            }}
                          />
                        )}
                      </Space>
                    </Upload>
                  )}
                </Form.Item>
              )}
            </div>
          </Container>
          <Container style={{ marginBottom: 66 }} size="large">
            <Typography.Title showBadge level={5}>
              {t(generateFiledKey('userInfos.title'), { defaultValue: '人员信息' })}
            </Typography.Title>
            <Form.Item
              wrapperCol={{ span: 24 }}
              name="visitors"
              rules={[
                ({ getFieldValue }) => ({
                  validator(_, value: Visitor[]) {
                    const isGovernment = Object.keys(visitTypeTag)?.[0] === 'GOVERNMENT';

                    if (!isGovernment && (!value || value.length < 1)) {
                      return Promise.reject(
                        t(generateFiledKey('message.addUserEmptyError'), {
                          defaultValue: '至少添加一个人员',
                        })
                      );
                    }

                    return Promise.resolve();
                  },
                }),
              ]}
            >
              <VisitorsEditableTable
                contrabandItems={contrabandItems}
                renderExistingVisitTicketAsLink={renderExistingVisitTicketAsLink}
                idc={idc?.[0]}
                authorizedArea={(authorizedArea?.allSpaceGuids || areaAllSpaceGuids) ?? []}
                allowedTimeRange={allowedTimeRange?.map(time => time.valueOf()) as [number, number]}
                isValidRef={isValidRef}
                editableFormRef={editableFormRef}
                accessDirectUrl={accessDirectUrl}
                isSpecialtyVisitType={isSpecialtyVisitType}
                isGovernments={isGovernments}
                visitTypeTag={visitTypeTag}
              />
            </Form.Item>
          </Container>
        </Space>
        <FooterToolBar>
          <Space>
            <Button type="primary" loading={loading} onClick={onSubmit}>
              {t(generateFiledKey('button.submit'), { defaultValue: '提交' })}
            </Button>
            {<Button onClick={savingDrafts}>保存草稿</Button>}
            {onCancel && (
              <Popover
                open={open}
                overlayStyle={{ width: '236px', height: '106px' }}
                content={
                  <Space direction="vertical" size={4} style={{ alignItems: 'end' }}>
                    <Space size={8}>
                      <ExclamationCircleFilled style={{ color: 'rgba(250, 173, 20, 1)' }} />
                      <Typography.Text>将会清空填写内容，可点击保存草稿暂存内容。</Typography.Text>
                    </Space>
                    <Space size={8}>
                      <Button
                        onClick={() => {
                          setOpen(false);
                        }}
                      >
                        取消
                      </Button>
                      <Button type="primary" onClick={onCancel}>
                        确定清空
                      </Button>
                    </Space>
                  </Space>
                }
                trigger="click"
                onOpenChange={open => {
                  setOpen(open);
                }}
              >
                <Button>{t(generateFiledKey('button.cancel'), { defaultValue: '取消' })}</Button>
              </Popover>
            )}
          </Space>
        </FooterToolBar>
      </Form>
    </PersonnelAccessFormContextProvider>
  );
}

const AreaComponents = ({
  t,
  assigneeData,
  editAreaData,
  disabledSpaceCodes,
  isGovernments,
  isSpecialtyVisitType,
  setEditAreaData,
  getAssigneeConfig,
  setAreaAllSpaceGuids,
}: any) => {
  const {
    idc,
    visitType,
    areaForm: form,
    initTreeDataRef,
    roomTypeMetaDataRef,
    initDisableVisitTypes,
    initDisableBlockValues,
  } = usePersonnelAccessFormContext();

  const [authorizedAreaTreeData, setAuthorizedAreaTreeData] = React.useState<
    { key: number; tree: unknown }[]
  >([{ key: 1, tree: [] }]);
  const [keys, setKeys] = React.useState([1]);
  const [tipsText, setTipsText] = React.useState({
    label: ' 您选择的是重点管控区域，此操作需经过客户审批，请谨慎选择!',
  });

  const addField = () => {
    setKeys(prev => [...prev, Math.max(...prev) + 1]);
  };
  const removeField = (key: number) => {
    setKeys(prev => prev.filter(k => k !== key));
    setTipsText(preState => ({ ...preState, [`show-${key}`]: false }));
    setAuthorizedAreaTreeData(authorizedAreaTreeData.filter(i => i.key !== key));
    form.resetFields([`area-block-${key}`, `area-area-${key}`, `area-assignees-${key}`]);
  };

  // 计算禁用的机房列表
  const getDisabledSpaceCodes = (selectedBlocks: string[]) => {
    // 获取已选择的楼栋列表
    const disabledList = [...selectedBlocks];
    const disabledSpaceCodesList: string[] = [];
    if (Array.isArray(visitType) && visitType.length > 0) {
      // 如果是特殊类别
      if (initDisableVisitTypes.includes(visitType[0])) {
        // 检查已勾选项中是否包含机房类型为IDC_BLOCK的项
        const allSelectedAreas: any = [];

        // 收集所有已选楼栋
        keys.forEach(k => {
          const blockValue = form.getFieldValue(`area-block-${k}`) || [];

          if (blockValue.length >= 1) {
            allSelectedAreas.push(
              initTreeDataRef.current.find((i: any) => i.value === blockValue[0])
            );
          }
        });

        // 检查是否有机房类型为IDC_BLOCK的
        const hasIdcBlock = allSelectedAreas.some((item: any) => item?.metaSubType === 'IDC_BLOCK');
        // 是否存在变电站类型（SUBSTATION）
        const hasSubstation = allSelectedAreas.some(
          (item: any) => item?.metaSubType === 'POWER_STATION'
        );

        // 禁用 PZ 和 Z 开头的机房
        initTreeDataRef.current?.forEach((item: any) => {
          const blockCode = item.value.split('.')?.[1];
          if (initDisableBlockValues.includes(blockCode)) {
            // 机房类型 并且业务类型不是政府商务
            if (
              hasIdcBlock &&
              !isSpecialtyVisitType
              // && !hasSubstation 不包含 变电站
            ) {
              disabledList.push(item.value);
              disabledSpaceCodesList.push(item.value);
              return;
            }
          }
          if (blockCode === 'PZ') {
            //  机房类型  且业务类型是政府商务
            if (
              hasIdcBlock &&
              isSpecialtyVisitType
              // && !hasSubstation 不包含 变电站
            ) {
              disabledList.push(item.value);
              disabledSpaceCodesList.push(item.value);
              return;
            }
            // 非机房类型 且包含变电站
            if (!hasIdcBlock && hasSubstation) {
              disabledList.push(item.value);
              disabledSpaceCodesList.push(item.value);
              return;
            }
          }
        });
      }
    }

    disabledSpaceCodes.current = disabledSpaceCodesList.filter(i => i.split('.')?.[1] !== 'Z');

    return disabledList;
  };

  // 使用 editAreaData 初始化表单字段
  React.useEffect(() => {
    if (!editAreaData || !Array.isArray(editAreaData)) {
      return;
    }

    // 根据 editAreaData 初始化 keys 和表单值
    const initialKeys = editAreaData.map((_, index) => index + 1);
    if (initialKeys.length < 1) {
      return;
    }
    setKeys(initialKeys);
    const allGuid: string[] = [];
    editAreaData.forEach((data, index) => {
      const key = index + 1;
      allGuid.push(data.allSpaceGuids);
      // 确保 spaceGuids 是三层嵌套数组，并去掉每个子数组的第一项
      const processedSpaceGuids = Array.isArray(data.spaceGuids)
        ? data.spaceGuids.map(subArray => (Array.isArray(subArray) ? subArray.slice(1) : []))
        : [];
      form.setFieldValue(`area-block-${key}`, data.blockGuid ? [data.blockGuid] : []);
      form.setFieldValue(`area-area-${key}`, {
        spaceGuids: processedSpaceGuids,
        allSpaceGuids: data.allSpaceGuids || [],
        spaceOptions: data.spaceOptions || [],
      });
      form.setFieldValue(`area-assignees-${key}`, data.assigneeList || []);
    });
    setAreaAllSpaceGuids(allGuid.flat());
    setEditAreaData(undefined);
  }, [editAreaData, form]);

  return (
    <Form colon={false} form={form}>
      <Row align="top" style={{ marginBottom: '16px' }}>
        <Col flex="95px" style={{ marginLeft: '23px' }}>
          <div
            style={{
              display: 'inline-block',
              marginRight: '4px',
              color: '#ff4d4f',
              fontSize: '14px',
              fontFamily: 'SimSun, sans-serif',
              lineHeight: '1px',
            }}
          >
            *
          </div>
          {t(generateFiledKey('authorizedArea'))}
          <Tooltip title="进入山西阳高机房的楼栋内则必须选择生产区域">
            <QuestionCircleOutlined
              style={{ margin: '0 4px', fontSize: '14px', color: 'rgba(0, 0, 0, 0.45)' }}
            />
          </Tooltip>
        </Col>
        <Col flex="auto">
          <Space direction="vertical" size={4} style={{ width: '100%' }}>
            {keys?.map((key, index) => {
              const allFields = form.getFieldsValue();
              // 收集所有已选楼栋，排除当前字段
              const selectedBlocks = Object.entries(allFields)
                .filter(
                  ([fieldKey, value]) =>
                    fieldKey.startsWith('area-block-') && value && fieldKey !== `area-block-${key}`
                )
                .map(([_, value]) => value?.[0])
                .filter(Boolean);

              const disabledSpaceCodes = getDisabledSpaceCodes(selectedBlocks);
              const isNone = disabledSpaceCodes.some(
                i =>
                  initDisableBlockValues.includes(i.split('.')?.[1]) &&
                  form.getFieldValue(`area-block-${key}`)?.[0] === i
              );

              if (isNone) {
                return null;
              }

              return (
                <div key={key} style={{ position: 'relative' }}>
                  <div key={key} style={{ display: 'flex', gap: '4px' }}>
                    {/* 楼栋选择 */}
                    <Form.Item
                      noStyle
                      dependencies={Object.keys(form.getFieldsValue()).filter(field =>
                        field.startsWith('area-block-')
                      )}
                    >
                      {() => {
                        return (
                          <Form.Item
                            style={{ marginBottom: 0 }}
                            name={`area-block-${key}`}
                            labelCol={{ span: 14 }}
                            rules={[
                              {
                                required: true,
                                message: '请选择楼栋',
                              },
                            ]}
                          >
                            <LocationCascader
                              style={{ width: '144px' }}
                              placeholder="楼栋"
                              showCheckedStrategy="SHOW_CHILD"
                              nodeTypes={['BLOCK']}
                              idc={idc?.[0] ?? undefined}
                              allowClear
                              onlyEnableBlock
                              disabledSpaceCodes={disabledSpaceCodes}
                              onChange={(values, options) => {
                                form.setFieldValue(`area-area-${key}`, undefined);
                                form.setFieldValue(`area-assignees-${key}`, undefined);
                                setTipsText(preState => ({
                                  ...preState,
                                  [`show-${key}`]: false,
                                }));
                              }}
                            />
                          </Form.Item>
                        );
                      }}
                    </Form.Item>

                    {/* 区域选择 */}
                    <Form.Item noStyle dependencies={[`area-block-${key}`]}>
                      {() => {
                        return (
                          <>
                            <Form.Item
                              style={{ marginBottom: 0 }}
                              name={`area-area-${key}`}
                              getValueFromEvent={(spaceGuids, _spaces, allSpaceGuids) => {
                                if (!spaceGuids?.length || !allSpaceGuids.length) {
                                  return undefined;
                                }
                                return { spaceGuids, allSpaceGuids };
                              }}
                              getValueProps={value => ({
                                value: value?.spaceGuids,
                              })}
                              rules={[
                                {
                                  validator: (_, value) => {
                                    // 获取当前 key 对应的 tree 数据
                                    const tree = authorizedAreaTreeData.find(
                                      i => i.key === key
                                    )?.tree;

                                    // 判断是否需要区域必填
                                    if (
                                      tree?.length >= 1 &&
                                      (!value?.spaceGuids || value.spaceGuids.length === 0)
                                    ) {
                                      return Promise.reject(new Error('进入区域必填'));
                                    }
                                    return Promise.resolve();
                                  },
                                },
                              ]}
                            >
                              <LocationCascader
                                style={{ width: '400px' }}
                                placeholder={
                                  authorizedAreaTreeData.find(i => i.key === key)?.tree.length >= 1
                                    ? '区域'
                                    : ''
                                }
                                disabled={!idc || !form.getFieldValue(`area-block-${key}`)}
                                showCheckedStrategy="SHOW_CHILD"
                                nodeTypes={['ROOM_CATEGORY', 'ROOM_TYPE', 'ROOM']}
                                idc={idc?.[0] ?? undefined}
                                blocks={
                                  Array.isArray(form.getFieldValue(`area-block-${key}`))
                                    ? [form.getFieldValue(`area-block-${key}`)[0]?.split('.')[1]]
                                    : undefined
                                }
                                multiple
                                allowClear
                                maxTagCount="responsive"
                                queryRoomProperties
                                onlyEnableBlock
                                onlyEnableRoom
                                showRoomTypeWarning
                                onTreeDataChange={tree => {
                                  setAuthorizedAreaTreeData(prevState => {
                                    const existingIndex = prevState.findIndex(i => i.key === key);
                                    if (existingIndex > -1) {
                                      // 更新已有的树
                                      const updatedState = [...prevState];
                                      updatedState[existingIndex].tree = tree;
                                      return updatedState;
                                    }
                                    // 添加新的树
                                    return [...prevState, { key, tree }];
                                  });
                                }}
                                onChange={(values, options, allSpaceGuids) => {
                                  // 保存当前选中的区域值
                                  form.setFieldValue(`area-area-${key}`, {
                                    spaceGuids: values,
                                    allSpaceGuids,
                                    spaceOptions: options?.map(option => option?.at(-1)),
                                  });
                                  // 检查是否有警告
                                  const isShow = checkWarnings(
                                    options?.map(option => option?.at(-1)),
                                    roomTypeMetaDataRef.current
                                  );
                                  setTipsText(preState => ({
                                    ...preState,
                                    [`show-${key}`]: isShow,
                                  }));
                                  if (!editAreaData) {
                                    const allGuid = keys
                                      .map(
                                        key => form.getFieldValue(`area-area-${key}`)?.allSpaceGuids
                                      )
                                      .flat()
                                      ?.filter(Boolean);
                                    setAreaAllSpaceGuids(allGuid);
                                  }
                                }}
                              />
                            </Form.Item>
                          </>
                        );
                      }}
                    </Form.Item>

                    {/* 对接人选择 */}
                    {
                      <Form.Item noStyle dependencies={[`area-block-${key}`]}>
                        {() => {
                          const assigneeInfo = assigneeData.find(
                            (assigneeInfo: unknown) =>
                              assigneeInfo.blockGuid ===
                              form.getFieldValue(`area-block-${key}`)?.[0]
                          );
                          return (
                            <Form.Item
                              style={{ marginBottom: 0 }}
                              name={`area-assignees-${key}`}
                              rules={[
                                {
                                  required:
                                    assigneeInfo &&
                                    assigneeInfo.assigneeType === 1 &&
                                    !isGovernments,
                                  message: '请选择对接人',
                                },
                              ]}
                            >
                              {/*手动确认类型才会展示对接人 且不是政府类型*/}
                              {assigneeInfo &&
                                assigneeInfo.assigneeType === 1 &&
                                !isGovernments && (
                                  <AreaAssigneeSelector
                                    style={{ width: '216px' }}
                                    setAssigneeValue={(
                                      assigneeList: { id: number; userName: string }[]
                                    ) => {
                                      form.setFieldValue(
                                        `area-assignees-${key}`,
                                        assigneeList || []
                                      );
                                    }}
                                    assigneeInfo={assigneeInfo}
                                    getAssigneeConfig={getAssigneeConfig}
                                  />
                                )}
                            </Form.Item>
                          );
                        }}
                      </Form.Item>
                    }

                    {keys.length > 1 && (
                      <a
                        style={{
                          position: 'relative',
                          left:
                            assigneeData?.find(
                              (assigneeInfo: unknown) =>
                                assigneeInfo.blockGuid ===
                                form.getFieldValue(`area-block-${key}`)?.[0]
                            )?.assigneeType !== 1 || isGovernments
                              ? '216px'
                              : '0px',
                          lineHeight: '32px',
                          color: 'rgba(0, 0, 0, 0.45)',
                        }}
                        onClick={() => removeField(key)}
                      >
                        <DeleteOutlined />
                      </a>
                    )}
                  </div>
                  {tipsText[`show-${key}`] && (
                    <div style={{}}>
                      <Typography.Text type="secondary">{tipsText.label}</Typography.Text>
                    </div>
                  )}
                </div>
              );
            })}
            {/* 添加按钮 */}
            <div style={{ width: 768 }}>
              <Button type="dashed" block onClick={addField}>
                + 添加
              </Button>
            </div>
          </Space>
        </Col>
      </Row>
    </Form>
  );
};

const AreaAssigneeSelector = ({
  assigneeInfo,
  getAssigneeConfig,
  setAssigneeValue,
  onChange,
  value,
  ...props
}: any) => {
  const [selectOptions, setSelectOptions] = React.useState<
    { label: string; value: number; disabled: boolean }[]
  >([]);
  const [selectValues, setSelectValues] = React.useState(() => {
    if (!Array.isArray(value)) {
      return [];
    }
    return value.map(item => (typeof item === 'number' ? item : item.id));
  });

  React.useEffect(() => {
    if (assigneeInfo?.blockGuid) {
      (async () => {
        const {
          data: { updateChangeAssigneeConfig },
        } = await getAssigneeConfig({
          variables: {
            query: {
              blockGuid: assigneeInfo.blockGuid,
              customerNo: assigneeInfo.customerNo ?? 'SYSTEM',
            },
          },
          // onCompleted(data) {},
        });
        const { data } = updateChangeAssigneeConfig;
        if (data) {
          const initSelectValues: number[] = [];
          const _assigneeList: { id: number; userName: string; userType: number }[] =
            data.map((item: unknown) => item.assigneeList).flat() || [];
          const _options =
            _assigneeList.map(assignee => {
              if (assignee.userType === 1) {
                initSelectValues.push(assignee.id);
              }
              return {
                value: assignee.id,
                label: assignee.userName,
                disabled: false,
              };
            }) || [];
          setSelectOptions(_options);
          if (!Array.isArray(value)) {
            // 默认对接人
            setSelectValues(initSelectValues);
            setAssigneeValue(
              _assigneeList.filter(assignee => initSelectValues.some(id => id === assignee.id))
            );
          }
        }
      })();
    }
  }, [assigneeInfo]);
  return (
    <Select
      {...props}
      showSearch
      allowClear
      mode="multiple"
      maxTagCount="responsive"
      placeholder="对接人"
      options={selectOptions}
      value={selectValues}
      optionFilterProp="label"
      onChange={(v: string[], options: { value: number; label: string }[]) => {
        setSelectValues(v);
        onChange?.(options?.map(option => ({ id: option.value, userName: option.label })));
      }}
    />
  );
};

export const FuzzySearchInput = (props: any) => {
  const { value, onChange, onSelect, placeholder, style, ...restProps } = props;

  // 存储搜索结果
  const [options, setOptions] = React.useState<{ value: string; label: string }[]>([]);
  // 存储加载状态
  const [loading, setLoading] = React.useState(false);
  // 创建一个节流的搜索函数
  const debouncedFetchRef = React.useRef(
    debounce(async (searchValue: string) => {
      if (!searchValue) {
        setOptions([]);
        return;
      }

      setLoading(true);
      try {
        const { data } = await fetchPagedVendors({
          pageNum: 1,
          pageSize: 6,
          vendorName: searchValue,
        });

        const newOptions = data?.data?.map(item => ({
          value: item.vendorName,
          label: item.vendorName,
        }));
        setOptions(newOptions || []);
      } catch (err) {
        console.error('搜索出错:', err);
      } finally {
        setLoading(false);
      }
    }, 500)
  );

  // 处理输入变化
  const handleChange = (value: string) => {
    onChange?.(value);
    debouncedFetchRef.current(value);
  };
  // 组件卸载时取消未完成的节流函数调用
  useEffect(() => {
    return () => {
      debouncedFetchRef.current.cancel();
    };
  }, []);

  return (
    <AutoComplete
      value={value}
      options={options}
      onChange={handleChange}
      onSelect={onSelect}
      placeholder={placeholder || '请输入关键词搜索'}
      style={style}
      notFoundContent={loading ? '加载中...' : '无匹配结果'}
      {...restProps}
    />
  );
};
