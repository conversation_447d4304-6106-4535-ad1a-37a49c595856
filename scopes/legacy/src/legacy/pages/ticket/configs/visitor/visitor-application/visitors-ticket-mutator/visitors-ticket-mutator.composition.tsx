import { gql } from '@apollo/client';
import { MockedProvider } from '@apollo/client/testing/index.js';
import { i18n } from '@teammc/i18n';
import React from 'react';
import { initReactI18next, useTranslation } from 'react-i18next';
import { Link, MemoryRouter, Route } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';
import { Button } from '@manyun/base-ui.ui.button';
import { Select } from '@manyun/base-ui.ui.select';
import type { SelectProps } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { mockVisitTicket } from '@manyun/sentry.gql.client.visits/mocks/visit-ticket.mock.js';
import {
  visitorsEditableTableEnUS,
  visitorsEditableTableLocaleNS,
} from '@manyun/sentry.ui.visitors-editable-table';
import { mockTicketTypes } from '@manyun/ticket.gql.client.tickets/mocks/ticket-types.mock.js';

import { LOCALE_SCOPE_NAME, LOCALE_UNIQ_KEY, enUS } from './locales/index.js';
import { VisitorsTicketMutator } from './visitors-ticket-mutator.js';

const ticketTypesMocks = mockTicketTypes();
const visitTicketMocks = mockVisitTicket({ args: { ticketNumber: 'FK23062619754' } });

const GET_SPACE_QUERY = gql`
  query GetSpaces(
    $idc: String
    $blocks: [String!]
    $nodeTypes: [SpaceNodeType!]
    $authorizedOnly: Boolean
    $includeVirtualBlocks: Boolean
    $includeNoIdcRegions: Boolean
    $roomTypes: [String!]
  ) {
    spaces(
      idc: $idc
      blocks: $blocks
      nodeTypes: $nodeTypes
      authorizedOnly: $authorizedOnly
      includeVirtualBlocks: $includeVirtualBlocks
      includeNoIdcRegions: $includeNoIdcRegions
      roomTypes: $roomTypes
    ) {
      type
      value
      parentValue
      label
      isVirtual
      custom
      children {
        type
        value
        parentValue
        label
        isVirtual
        custom
        children {
          type
          value
          parentValue
          label
          isVirtual
          custom
        }
      }
    }
  }
`;
const mocks = [
  ...ticketTypesMocks,
  ...visitTicketMocks,
  {
    request: {
      query: GET_SPACE_QUERY,
      variables: {
        nodeTypes: ['BLOCK', 'ROOM'],
        includeVirtualBlocks: false,
        authorizedOnly: true,
        idc: 'EC06',
      },
    },
    result: {
      data: {
        spaces: [
          {
            type: 'BLOCK',
            value: 'EC06.A',
            parentValue: 'EC06',
            label: 'A 栋',
            isVirtual: false,
            children: [
              {
                type: 'ROOM_TYPE',
                value: 'EC06.A.POWER_ROOM',
                parentValue: 'EC06.A',
                label: '强电间',
                isVirtual: null,
                children: [
                  {
                    type: 'ROOM',
                    value: 'EC06.A.1-1',
                    parentValue: 'EC06.A.POWER_ROOM',
                    label: '1-1 SXDTYG_DA_A运营商机房',
                    isVirtual: null,
                  },
                  {
                    type: 'ROOM',
                    value: 'EC06.A.A1-31',
                    parentValue: 'EC06.A.POWER_ROOM',
                    label: 'A1-31 SXDTYG_DA_强电间（一）',
                    isVirtual: null,
                  },
                ],
              },
              {
                type: 'ROOM_TYPE',
                value: 'EC06.A.40',
                parentValue: 'EC06.A',
                label: '蓄冷罐室',
                isVirtual: null,
                children: [
                  {
                    type: 'ROOM',
                    value: 'EC06.A.A1-0',
                    parentValue: 'EC06.A.40',
                    label: 'A1-0 SXDTYG_DA_室外柴发区',
                    isVirtual: null,
                  },
                  {
                    type: 'ROOM',
                    value: 'EC06.A.A1-2',
                    parentValue: 'EC06.A.40',
                    label: 'A1-2 数据机房（二）',
                    isVirtual: null,
                  },
                ],
              },
            ],
          },
        ],
      },
    },
  },
  {
    request: {
      query: GET_SPACE_QUERY,
      variables: {
        nodeTypes: ['IDC'],
        includeVirtualBlocks: false,
        authorizedOnly: true,
      },
    },
    result: {
      data: {
        spaces: [
          {
            type: 'IDC',
            value: 'EC06',
            parentValue: null,
            label: 'EC06 普洛斯东南数据中心',
            isVirtual: false,
          },
          {
            type: 'IDC',
            value: 'EC01',
            parentValue: null,
            label: 'EC01 普洛斯哈弄弄数据中心',
            isVirtual: false,
          },
        ],
      },
    },
  },
  {
    request: {
      query: gql`
        query GetUsersForUserSelect(
          $authorized: Boolean
          $resourceTypes: [String!]
          $resourceParams: [ResourceParam!]
          $userState: String
          $key: String
        ) {
          usersForUserSelect(
            authorized: $authorized
            resourceTypes: $resourceTypes
            resourceParams: $resourceParams
            userState: $userState
            key: $key
          ) {
            id
            name
            login
            mobileNumber
            email
            state
            type
            nameEn
            gender
            title
            company
            department
            departmentId
            supervisorUid
            subSupervisorUid
            jobDescriptions
            remarks
            birthday
            birthPlace
            workplace
            signature
            hiredDate
            joinWorkingDate
            gmtCreate
            gmtModified
            lastLoginTime
            createUser {
              id
              name
            }
            modifyUser {
              id
              name
            }
            userShifts
            certCount
            idc
          }
        }
      `,
      variables: {
        blockGuid: 'EC06.A',
      },
    },
    result: {
      data: {
        usersForUserSelect: [
          {
            id: 1,
            name: 'admin',
            login: 'admin',
          },
          {
            id: 2,
            name: 'test',
            login: 'test',
          },
        ],
      },
    },
  },
];
initReactI18next.init(i18n.current);

i18n.current.addResourceBundle('en-US', LOCALE_SCOPE_NAME, {
  [LOCALE_UNIQ_KEY]: enUS,
  [visitorsEditableTableLocaleNS]: visitorsEditableTableEnUS,
});

export const BasicVisitorsTicketMutator = () => {
  const { i18n } = useTranslation();
  return (
    <MockedProvider mocks={mocks}>
      <ConfigProvider>
        <MemoryRouter>
          <Route exact path="/">
            <Space direction="vertical">
              <Link to="/visit-ticket/new">Create a new visit ticket</Link>
              <Link to="/visit-ticket/FK23062619754/edit">
                Edit the existing visit ticket: FK23062619754
              </Link>
              <Button
                onClick={() => {
                  i18n.changeLanguage(i18n.language === 'zh-CN' ? 'en-US' : 'zh-CN');
                }}
              >
                当前语言： {i18n.language}
              </Button>
            </Space>
          </Route>
          <Route exact path="/visit-ticket/new">
            <div style={{ margin: 24 }}>
              <VisitorsTicketMutator
                formLayout={i18n.language === 'en-US' ? 'vertical' : undefined}
                initialValues={{
                  creator: {
                    id: 1,
                    name: 'Jerry',
                  },
                  authorizedNotify: 'email',
                  outSideVisitorNotices: ['123', '123'],
                  insideVisitorNotices: [
                    { email: '222', value: '1', label: 'admin' },
                    { email: '222', value: '2', label: 'ces' },
                  ],
                }}
                unusedFormItems={['title']}
                UserSelect={UserSelect}
              />
            </div>
          </Route>
          <Route exact path="/visit-ticket/:id/edit">
            <div style={{ margin: 24 }}>
              <VisitorsTicketMutator
                ticketNumber="FK23062619754"
                UserSelect={UserSelect}
                initialValues={{
                  authorizedNotify: 'email',
                  outSideVisitorNotices: ['123', '234'],
                  insideVisitorNotices: [
                    { email: '222', value: '1', label: 'admin' },
                    { email: '222', value: '2', label: 'ces' },
                  ],
                }}
              />
            </div>
          </Route>
          <Route exact path="/visit-ticket/:id">
            This is the "Visit Detail Page", click <Link to="/">here</Link> to go back.
          </Route>
        </MemoryRouter>
      </ConfigProvider>
    </MockedProvider>
  );
};

const UserSelect = ({ ...props }: SelectProps) => {
  return (
    <Select
      options={[
        { label: 'admin', value: '1' },
        { label: 'ces', value: '2' },
        { label: 'ces3', value: '3' },
      ]}
      {...props}
    />
  );
};
