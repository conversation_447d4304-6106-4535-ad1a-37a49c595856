import PageHeader from 'antd/es/page-header';
import sortBy from 'lodash/sortBy';
import React from 'react';
import { connect } from 'react-redux';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { Button } from '@manyun/base-ui.ui.button';
import { Row } from '@manyun/base-ui.ui.grid';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { useLazyTicketRoomStartCheck } from '@manyun/ticket.gql.client.tickets';
import { BackendTaskStatus } from '@manyun/ticket.model.ticket';

import { DisplayCard, Ellipsis, GutterWrapper } from '@manyun/dc-brain.legacy.components';
import { INSPECTION_DETAIL_RECENTLY_CLICK_DEVICE } from '@manyun/dc-brain.legacy.constants/ticket';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  ticketActions,
  ticketPatrolCheckSubjectGroupActionCreator,
  ticketPatrolRoomInfoActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/ticketActions';
import {
  getDisplayCardColor,
  getRecentlyClickCardCode,
  setRecentlyClickCardCode,
} from '@manyun/dc-brain.legacy.utils/ticket';

import { NonstandardInspectItem } from './nonstandard-inspect-item';

function InspectsBtn({ taskNo, roomGuid, ticketPatrolRoomInfoActionCreator, userId }) {
  const [, { checkUserId }] = useAuthorized();
  const isCurrentUser = checkUserId(userId);
  const [getTicketRoomStartCheck, { data, loading }] = useLazyTicketRoomStartCheck({
    onError(error) {
      if (error) {
        message.error(error.message);
      }
    },
  });
  React.useEffect(() => {
    if (data?.ticketRoomStartCheck?.success) {
      ticketPatrolRoomInfoActionCreator({ taskNo });
    }
  }, [data]);
  return (
    <>
      {isCurrentUser && (
        <Button
          type="primary"
          onClick={() => {
            getTicketRoomStartCheck({
              variables: { taskNo, roomGuid },
            });
          }}
        >
          开始巡检
        </Button>
      )}
    </>
  );
}

export class DeviceTypeCard extends React.Component {
  state = {
    checkItemKey: true,
  };

  componentDidMount() {
    this.props.syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });
    this.props.ticketPatrolCheckSubjectGroupActionCreator({
      taskNo: this.props.taskNo,
      roomGuid: this.props.roomGuid,
    });
  }

  handleChangeCurrentContent = (deviceType, checkSubjectType, roomTag) => {
    setRecentlyClickCardCode(
      deviceType
        ? `${this.props.roomGuid}_&&_${deviceType}`
        : `${this.props.roomGuid}_&&_${roomTag}`,
      this.props.taskNo,
      INSPECTION_DETAIL_RECENTLY_CLICK_DEVICE
    );
    this.props.changeCurrentContent('checkItem', this.props.roomGuid, deviceType, checkSubjectType);
  };

  render() {
    const {
      loading,
      checkSubjectGroup,
      deviceCategory,
      roomTypes,
      taskNo,
      roomGuid,
      userId,
      taskStatus,
      roomInfo,
      ticketPatrolRoomInfoActionCreator,
    } = this.props;
    const room = roomInfo.find(info => info.roomGuid === roomGuid);
    const { checkItemKey } = this.state;

    if (loading) {
      return (
        <GutterWrapper flex justifyContent="center">
          <Spin />
        </GutterWrapper>
      );
    }
    let list = checkSubjectGroup;
    const recentlyCardCode = getRecentlyClickCardCode(INSPECTION_DETAIL_RECENTLY_CLICK_DEVICE);
    if (recentlyCardCode) {
      list = sortBy(list, value => {
        return value.deviceType
          ? `${this.props.roomGuid}_&&_${value.deviceType}` !== recentlyCardCode.code
          : `${this.props.roomGuid}_&&_${value.roomTag}` !== recentlyCardCode.code;
      });
    }
    const roomInfos = list[0];

    return (
      <GutterWrapper mode="vertical" padding="0 16px">
        <Row justify="space-between">
          <Space size="middle">
            <PageHeader
              style={{ padding: 0 }}
              title={
                roomInfos ? (
                  <span>{`${roomInfos.roomTag}\n${roomTypes[roomInfos.roomType]}`}</span>
                ) : (
                  ''
                )
              }
              onBack={() => this.props.changeCurrentContent('room', null, null, null)}
            />
            {!room.startCheck && [BackendTaskStatus.PROCESSING].includes(taskStatus) && (
              <InspectsBtn
                userId={userId}
                roomGuid={roomGuid}
                taskNo={taskNo}
                ticketPatrolRoomInfoActionCreator={ticketPatrolRoomInfoActionCreator}
              />
            )}
          </Space>
          <Radio.Group
            value={checkItemKey}
            options={[
              { label: '标准检查项', value: true },
              { label: '非标准检查异常项', value: false },
            ]}
            optionType="button"
            defaultValue
            onChange={value => this.setState({ checkItemKey: value.target.value })}
          />
        </Row>
        {checkItemKey ? (
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fill,minmax(244px,1fr))',
              gridRowGap: '58px',
              gridColumnGap: '16px',
            }}
          >
            {list.map(subjectItem => (
              <DisplayCard
                key={subjectItem.roomTag}
                title={
                  <Ellipsis tooltip lines={1}>
                    {subjectItem.deviceType
                      ? deviceCategory?.normalizedList[subjectItem.deviceType]?.metaName
                      : subjectItem.roomTag}
                  </Ellipsis>
                }
                backgroundColor={getDisplayCardColor({
                  exceptionNum: subjectItem.exceptionNum,
                  waitNum: subjectItem.waitInspectNum,
                  alreadyNum: subjectItem.alreadyInspectNum,
                  code: subjectItem.deviceType
                    ? `${this.props.roomGuid}_&&_${subjectItem.deviceType}`
                    : `${this.props.roomGuid}_&&_${subjectItem.roomTag}`,
                  taskNo: this.props.taskNo,
                  recentlyCode: INSPECTION_DETAIL_RECENTLY_CLICK_DEVICE,
                })}
                countList={[
                  {
                    describe: '总异常',
                    number: subjectItem.exceptionNum,
                    color: 'error',
                  },
                  {
                    describe: '待巡检',
                    number: subjectItem.waitInspectNum,
                    color: 'warning',
                  },
                  {
                    describe: '已巡检',
                    number: subjectItem.alreadyInspectNum,
                    color: 'success',
                  },
                ]}
                onClick={() =>
                  this.handleChangeCurrentContent(
                    subjectItem.deviceType,
                    subjectItem.checkSubjectType,
                    subjectItem.roomTag
                  )
                }
              />
            ))}
          </div>
        ) : (
          <NonstandardInspectItem
            taskNo={taskNo}
            roomGuid={roomGuid}
            userId={userId}
            taskStatus={taskStatus}
          />
        )}
      </GutterWrapper>
    );
  }
}

const mapStateToProps = ({
  ticket: {
    patrol: { detail },
  },
  common: { deviceCategory, roomTypes },
}) => ({
  deviceCategory: deviceCategory,
  loading: detail.roomLoading,
  checkSubjectGroup: detail.checkSubjectGroup,
  roomTypes: roomTypes || {},
  roomInfo: detail.roomInfo,
});

const mapDispatchToProps = {
  ticketPatrolCheckSubjectGroupActionCreator: ticketPatrolCheckSubjectGroupActionCreator,
  ticketPatrolRoomInfoActionCreator: ticketPatrolRoomInfoActionCreator,
  setPatrolRoomTypes: ticketActions.setPatrolRoomTypes,
  syncCommonData: syncCommonDataActionCreator,
};
export default connect(mapStateToProps, mapDispatchToProps)(DeviceTypeCard);
