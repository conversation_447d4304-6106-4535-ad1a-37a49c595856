import React from 'react';
import { connect } from 'react-redux';

import { User } from '@manyun/auth-hub.ui.user';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Space } from '@manyun/base-ui.ui.space';

import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  ticketPatrolCheckItemActionCreator,
  ticketPatrolRoomInfoActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/ticketActions';

import ChekcItem from './check-item';
import DeviceTypeCard from './device-type-card';
import Room from './room';

export class PatorlDetail extends React.Component {
  state = {
    currentContent: 'room', // room || deviceType || checkItem
    roomGuid: null,
    deviceType: null,
    checkSubjectType: null,
    roomType: null,
  };

  componentDidMount() {
    this.props.syncCommonData({ strategy: { roomTypes: 'IF_NULL' } });
  }

  changeCurrentContent = (value, roomGuid = null, deviceType = null, checkSubjectType = null) => {
    this.setState({ currentContent: value, roomGuid, deviceType, checkSubjectType });
  };

  render() {
    const { basicInfo } = this.props;
    const { currentContent, roomGuid, checkSubjectType, deviceType, roomType } = this.state;

    return (
      <Space style={{ display: 'flex', flexDirection: 'column' }} direction="vertical">
        {basicInfo.assigneeList?.length > 0 && (
          <Descriptions column={4} style={{ padding: '0 16px' }}>
            <Descriptions.Item label="指派人">
              <Space style={{ flexWrap: 'wrap' }}>
                {basicInfo.assigneeList.map(item => (
                  <User.Link key={item.id} id={item.id} name={item.userName} />
                ))}
              </Space>
            </Descriptions.Item>
          </Descriptions>
        )}
        {currentContent === 'room' && basicInfo && (
          <Room
            setProviderValue={this.props.setProviderValue}
            taskNo={this.props.taskNo}
            changeCurrentContent={this.changeCurrentContent}
            setRoomType={roomType => this.setState({ roomType })}
          />
        )}
        {currentContent === 'deviceType' && basicInfo && (
          <DeviceTypeCard
            taskNo={this.props.taskNo}
            changeCurrentContent={this.changeCurrentContent}
            roomGuid={roomGuid}
            userId={basicInfo.taskAssignee}
            taskStatus={basicInfo.taskStatus}
          />
        )}
        {currentContent === 'checkItem' && basicInfo && (
          <ChekcItem
            roomGuid={roomGuid}
            changeCurrentContent={this.changeCurrentContent}
            basicInfo={basicInfo}
            deviceType={deviceType}
            checkSubjectType={checkSubjectType}
            roomType={roomType}
          />
        )}
      </Space>
    );
  }
}

const mapStateToProps = ({ ticket: { patrol } }) => ({
  patrol,
});

const mapDispatchToProps = {
  ticketPatrolRoomInfoActionCreator: ticketPatrolRoomInfoActionCreator,
  ticketPatrolCheckItemActionCreator: ticketPatrolCheckItemActionCreator,
  syncCommonData: syncCommonDataActionCreator,
};
export default connect(mapStateToProps, mapDispatchToProps)(PatorlDetail);
