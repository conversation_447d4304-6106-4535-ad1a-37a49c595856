import { PictureTwoTone, QuestionCircleOutlined } from '@ant-design/icons';
import PageHeader from 'antd/es/page-header';
import dayjs from 'dayjs';
import React from 'react';
import { connect } from 'react-redux';
import { Link, Redirect } from 'react-router-dom';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { User } from '@manyun/auth-hub.ui.user';
import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { SimpleFileList } from '@manyun/base-ui.ui.file-list';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { getDiPointValueText } from '@manyun/monitoring.model.point';
import { generateRoomMonitoringUrl } from '@manyun/monitoring.route.monitoring-routes';
import { useMetaData } from '@manyun/resource-hub.hook.use-meta-data';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { generateDeviceRecordRoutePath } from '@manyun/resource-hub.route.resource-routes';
import { useLazyTicketRoomStartCheck } from '@manyun/ticket.gql.client.tickets';
import { BackendTaskStatus } from '@manyun/ticket.model.ticket';
import {
  generateEventDetailRoutePath,
  generateEvnetLocation,
  generateTicketUrl,
} from '@manyun/ticket.route.ticket-routes';
import { updateInspectionDeviceStatus } from '@manyun/ticket.service.update-inspection-device-status';

import { GutterWrapper, StatusText, TinyTable } from '@manyun/dc-brain.legacy.components';
import { INSPECTION_STANDARD_TEXT_AI_AND_DI } from '@manyun/dc-brain.legacy.components/inspection-table/constants';
import { INSPECTION_TYPE_KEY_MAP } from '@manyun/dc-brain.legacy.constants/inspection';
import { STATUS_MAP } from '@manyun/dc-brain.legacy.constants/statusColor';
import {
  TICKET_CHECK_ITEM_RESULT_KEY_MAP,
  TICKET_CHECK_ITEM_RESULT_KEY_TEXT_MAP,
} from '@manyun/dc-brain.legacy.constants/ticket';
import {
  ticketActions,
  ticketPatrolCheckItemActionCreator,
  ticketPatrolCheckItemPointResultSaveActionCreator,
  ticketPatrolRoomInfoActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/ticketActions';
import { ticketService } from '@manyun/dc-brain.legacy.services';
import { getRowSpan } from '@manyun/dc-brain.legacy.utils';

import ExceptionModal from '../../../../components/exception-modal';
import { CHECK_SUBJECT_KEY_MAPS, PATROL_CHECK_ITEM_TYPE } from '../../../../constants';
import allExpectionSvg from '../assets/exception.svg';
import processedExceptionSvg from '../assets/processed-exception.svg';
import unprocessedExceptionSvg from '../assets/unprocessed-exception.svg';
import PointAIInput from './point-AI-input';
import PointDISelect from './point-DI-select';
import { UploadInspectItemFileModal } from './upload-inspect-item-file-modal';

function DeviceStatus(props) {
  const {
    checkScenes,
    taskNo,
    deviceGuid,
    disabled,
    initChecks,
    checkNormal,
    deviceStatus,
    exceptionScenes,
  } = props;
  const [state, setState] = React.useState([]);
  // 使用 useRef 持久化存储 initChecksOptions
  const initChecksOptionsRef = React.useRef([]);
  const [{ data }, { readMetaData }] = useMetaData(MetaType.INSPECT_SCENES, true);

  const customTagRender = props => {
    const { label, value, closable, onClose } = props;
    // 如果当前只有一个选中项，并且这个 tag 是唯一的那个，则禁用 closable
    const isDisabled = state.length === 1 && state.includes(value);
    return (
      <Tag closable={closable && !isDisabled} onClose={onClose} style={{ marginRight: 3 }}>
        {label}
      </Tag>
    );
  };

  React.useEffect(() => {
    readMetaData();
  }, []);

  React.useEffect(() => {
    // 当数据变化时重新计算
    if (Array.isArray(data.data) && Array.isArray(initChecks)) {
      const options =
        data.data
          ?.filter(
            item => initChecks.includes(item.value) && item.value !== 'ALL' && item.value !== ''
          )
          .map(item => ({
            key: item.value,
            value: item.value,
            label: item.label,
            disabled: exceptionScenes.includes(item.value),
          })) || [];
      // 存储到 ref 中
      initChecksOptionsRef.current = options;
      if (!!deviceStatus) {
        setState(deviceStatus?.split(',').filter(i => i !== '' && i != null) || []);
      }
      // // 如果没有初始值，则全选所有选项
      // if (!deviceStatus && deviceStatus !== '') {
      //   const allValues = options.map(option => option.value);
      //   setState(allValues);
      // }
    }
  }, [data?.data, initChecks, exceptionScenes, deviceStatus]);

  return (
    <Select
      style={{ minWidth: 140 }}
      maxTagCount="responsive"
      mode="multiple"
      value={state}
      options={initChecksOptionsRef.current}
      tagRender={customTagRender}
      onChange={async value => {
        if (state.length === 1 && value.length === 0) {
          // message.warning('至少需要保留一个状态。');
          return;
        }
        setState(value);
        if (taskNo && deviceGuid) {
          await updateInspectionDeviceStatus({
            deviceStatus: value.join(','),
            taskNo,
            deviceGuid,
          });
          props?.getPatrolCheckItem();
        }
      }}
    />
  );
}
function InspectionsStatus(props) {
  const { inspectionsList, handleCheckItemStatus, value } = props;

  const [state, setState] = React.useState({
    check: undefined,
    status: undefined,
    disabled: false,
  });

  React.useEffect(() => {
    setState({
      check: value,
      status: inspectionsList.find(i => i.value === value)?.status,
      disabled:
        inspectionsList.find(i => i.value === value)?.status ===
        TICKET_CHECK_ITEM_RESULT_KEY_MAP.EXCEPTION,
    });
  }, [value]);

  return (
    <>
      <Select
        style={{ width: 130 }}
        value={props.value || state.check}
        disabled={state.disabled || props.disabled}
        options={inspectionsList}
        onChange={value => {
          handleCheckItemStatus(value, inspectionsList.find(i => i.value === value)?.status);
        }}
      />
    </>
  );
}

const columns = (
  ctx,
  basicInfo,
  authorized,
  datasource,
  rowSpansGroupByDataIndex,
  invalidValues,
  showNotProcessed
) => [
  {
    title: '检查对象',
    dataIndex: 'checkSubjectTag',
    className: 'merge-row-span',
    hidden: false,
    render: (text, record, index) => {
      if (!text) {
        return '';
      }
      const obj = {
        children: (
          <Space>
            <Link
              onClick={() => {
                if (record.checkSubjectType === INSPECTION_TYPE_KEY_MAP.DEVICE) {
                  window.open(
                    generateDeviceRecordRoutePath({
                      guid: record.checkSubjectGuid,
                    })
                  );
                } else {
                  window.open(
                    generateRoomMonitoringUrl({
                      idc: ctx.props.basicInfo.idcTag,
                      block: ctx.props.basicInfo.blockTag.substring(
                        ctx.props.basicInfo.blockTag.indexOf('.') + 1
                      ),
                      room: record.roomTag,
                    })
                  );
                }
              }}
            >
              {text}
            </Link>
            {record.checkScenes &&
              record.supportCheckScenes &&
              authorized &&
              basicInfo.taskStatus === BackendTaskStatus.PROCESSING && (
                <DeviceStatus
                  initChecks={record.initChecks}
                  deviceStatus={record.deviceStatus}
                  checkScenes={record.checkScenes}
                  taskNo={record.taskNo}
                  deviceGuid={record.checkSubjectGuid}
                  checkNormal={record.checkNormal}
                  exceptionScenes={record.exceptionScenes}
                  getPatrolCheckItem={() => {
                    ctx.getPatrolCheckItem();
                  }}
                />
              )}
          </Space>
        ),
        props: {
          rowSpan: getRowSpan(record.checkSubjectGuid, index, {
            datasource,
            dataIndex: 'checkSubjectGuid',
            rowSpansGroupByDataIndex,
          }),
        },
      };
      return obj;
    },
  },
  {
    title: '检查项',
    dataIndex: 'checkItemName',
    className: 'merge-row-span',
    hidden: false,
    render: (text, record, index) => {
      if (!text || record.isPlaceholder) {
        return '';
      }
      const obj = {
        children: (
          <Space>
            {text}
            <>
              {Array.isArray(record.fileInfoList) && record.fileInfoList.length > 0 ? (
                <SimpleFileList
                  files={record.fileInfoList.map(backendMcUploadFile =>
                    McUploadFile.fromApiObject(backendMcUploadFile)
                  )}
                  children={
                    <PictureTwoTone>
                      <Button type="link" compact>
                        {record.fileInfoList.length}
                      </Button>
                    </PictureTwoTone>
                  }
                />
              ) : (
                ''
              )}
            </>
          </Space>
        ),
        props: {
          rowSpan: getRowSpan(record.metaCode, index, {
            datasource,
            dataIndex: 'metaCode',
            rowSpansGroupByDataIndex,
          }),
        },
      };
      return obj;
    },
  },

  {
    title: '检查标准',
    dataIndex: 'checkStd',
    hidden: false,
    render: (text, record) => {
      if (record.isPlaceholder) {
        return '';
      }
      const _icon = Array.isArray(record.exceptionFileInfoList) &&
        record.exceptionFileInfoList.length > 0 && (
          <SimpleFileList
            files={record.exceptionFileInfoList.map(backendMcUploadFile =>
              McUploadFile.fromApiObject(backendMcUploadFile)
            )}
            children={
              <PictureTwoTone>
                <Button type="link" compact>
                  {record.exceptionFileInfoList.length}
                </Button>
              </PictureTwoTone>
            }
          />
        );
      if (record?.checkValueJson?.dataType === 'AI') {
        return (
          <Space>
            {INSPECTION_STANDARD_TEXT_AI_AND_DI.AI_TEXT}
            {_icon}
          </Space>
        );
      }
      if (record?.checkValueJson?.dataType === 'DI') {
        return (
          <Space>
            {INSPECTION_STANDARD_TEXT_AI_AND_DI.DI_TEXT}
            {_icon}
          </Space>
        );
      }
      return (
        <Space>
          {text || '--'}
          {_icon}
        </Space>
      );
    },
  },

  {
    title: '实际完成时间',
    dataIndex: 'latestOpTime',
    hidden: false,
    render: (text, record) => {
      if (record.isPlaceholder) {
        return '';
      }
      return text ? dayjs(text).format('YYYY/MM/DD HH:mm:ss') : '--';
    },
  },
  {
    title: '处理人',
    dataIndex: 'operatorName',
    render: (_, record) => {
      const { operator, operatorName } = record.checkValueJson;
      return operatorName && <User.Link id={operator} name={operatorName} />;
    },
  },
  {
    title: '检查结果',
    dataIndex: 'operation',
    hidden: false,
    render: (text, record) => {
      // 如果是占位项，只显示检查对象名称
      if (record.isPlaceholder) {
        return '';
      }
      const room = ctx.props.roomInfo?.find(({ roomGuid }) => roomGuid === ctx.props.roomGuid);
      const inspectionsList = [
        record.checkValueJson?.normalTextList?.map(i => ({
          key: i,
          value: i,
          label: i,
          status: TICKET_CHECK_ITEM_RESULT_KEY_MAP.NORMAL,
        })) || [],
        record.checkValueJson?.exTextList?.map(i => ({
          key: i,
          value: i,
          label: i,
          status: TICKET_CHECK_ITEM_RESULT_KEY_MAP.EXCEPTION,
        })) || [],
      ].flat(); //区分新工单（自定义添加异常或者正常状态）
      if (inspectionsList.length >= 1 && record.isMeterRead === 0 && authorized) {
        // 已关单 或 失败关单展示文案
        if (
          basicInfo.taskStatus === BackendTaskStatus.FAILURE ||
          basicInfo.taskStatus === BackendTaskStatus.FINISH
        ) {
          if (
            record.checkValueJson?.exTextList?.some(i => i === record.checkValueJson?.valueText)
          ) {
            return <Typography.Text type="danger">异常</Typography.Text>;
          }
          if (
            record.checkValueJson?.normalTextList?.some(i => i === record.checkValueJson?.valueText)
          ) {
            return <Typography.Text>正常</Typography.Text>;
          }
        }
        if (basicInfo.taskStatus !== BackendTaskStatus.PROCESSING) {
          return '--';
        }
        return (
          <InspectionsStatus
            value={record.checkValueJson?.valueText}
            checkNormal={record.checkNormal}
            inspectionsList={inspectionsList}
            disabled={!room.startCheck}
            handleCheckItemStatus={(value, status) => {
              ctx.handleCheckItemStatus(record, value, status)();
            }}
          />
        );
      }
      if (
        [
          BackendTaskStatus.FINISH,
          BackendTaskStatus.FAILURE,
          BackendTaskStatus.CLOSE_APPROVER,
          BackendTaskStatus.UNDO,
        ].includes(basicInfo.taskStatus) ||
        (!authorized && basicInfo.taskStatus === BackendTaskStatus.PROCESSING)
      ) {
        let text = record.checkValueJson?.value;
        let type = 'default';
        if (record.checkValueJson?.dataType === 'DI' && record.checkValueJson?.value) {
          text = getDiPointValueText(
            record.checkValueJson.value,
            record.checkValueJson?.diValueText.split(',')
          );
        }
        if (
          record.checkNormal === TICKET_CHECK_ITEM_RESULT_KEY_MAP.EXCEPTION ||
          record.checkNormal === TICKET_CHECK_ITEM_RESULT_KEY_MAP.UN_HANDLE_EX
        ) {
          type = 'danger';
        }
        if (record.checkNormal === TICKET_CHECK_ITEM_RESULT_KEY_MAP.NORMAL) {
          type = 'success';
        }
        if (record.isMeterRead === 0) {
          return (
            <Typography.Text
              type={
                record.checkNormal === TICKET_CHECK_ITEM_RESULT_KEY_MAP.EXCEPTION ||
                record.checkNormal === TICKET_CHECK_ITEM_RESULT_KEY_MAP.UN_HANDLE_EX
                  ? 'danger'
                  : 'default'
              }
            >
              {TICKET_CHECK_ITEM_RESULT_KEY_TEXT_MAP[record.checkNormal] || '--'}
            </Typography.Text>
          );
        }
        return text || +text === 0 ? (
          <Typography.Text type={type}>
            {text} {record.checkValueJson?.unit}
          </Typography.Text>
        ) : (
          '--'
        );
      }

      if (basicInfo.taskStatus !== BackendTaskStatus.PROCESSING) {
        return '--';
      }
      const radio = (
        <Radio.Group
          disabled={
            record.checkNormal === TICKET_CHECK_ITEM_RESULT_KEY_MAP.EXCEPTION ||
            typeof record.checkValueJson.maxValue === 'number' ||
            (record.checkNormal === TICKET_CHECK_ITEM_RESULT_KEY_MAP.UN_HANDLE_EX &&
              showNotProcessed)
          }
          value={
            record.checkNormal === TICKET_CHECK_ITEM_RESULT_KEY_MAP.UN_HANDLE_EX
              ? TICKET_CHECK_ITEM_RESULT_KEY_MAP.EXCEPTION
              : record.checkNormal
          }
          onChange={ctx.handleCustomCheckItemStatus(record)}
        >
          <Radio value={TICKET_CHECK_ITEM_RESULT_KEY_MAP.NORMAL}>正常</Radio>
          <Radio value={TICKET_CHECK_ITEM_RESULT_KEY_MAP.EXCEPTION}>
            <Typography.Text
              type={
                ctx.state.exceptionInfo.id === record.id ||
                record.checkNormal === TICKET_CHECK_ITEM_RESULT_KEY_MAP.EXCEPTION
                  ? 'danger'
                  : 'default'
              }
            >
              异常
            </Typography.Text>
          </Radio>
        </Radio.Group>
      );
      // 自定义需要抄表项
      if (
        authorized &&
        ((record.itemType === PATROL_CHECK_ITEM_TYPE.CUSTOMIZE && record.isMeterRead) ||
          (invalidValues.includes(record.id) && record.checkValueJson?.dataType === 'AI'))
      ) {
        // 不在编辑状态下
        if (record.checkValueJson?.value !== null && !record.editable) {
          const value = `${record.checkValueJson?.value ?? '--'} ${record.checkValueJson?.unit}`;
          // 已选正常
          if (record.checkNormal === TICKET_CHECK_ITEM_RESULT_KEY_MAP.NORMAL) {
            return (
              <Space>
                <span style={{ width: 112, display: 'inline-block' }}>{value}</span>
                {radio}
                {authorized && (
                  <Button type="link" compact onClick={() => ctx.handleEdit(record.id)}>
                    修改
                  </Button>
                )}
              </Space>
            );
          }
          // 已选异常
          if (record.checkNormal === TICKET_CHECK_ITEM_RESULT_KEY_MAP.EXCEPTION) {
            return (
              <div>
                <StatusText
                  style={{ display: 'inline-block' }}
                  fontSize={14}
                  status={STATUS_MAP.ALARM}
                >
                  <span style={{ width: 112, display: 'inline-block' }}> {value}</span>
                </StatusText>
                {radio}
              </div>
            );
          }
          // 未选状态
          if (record.checkNormal === null && !record.editable) {
            return (
              <Space>
                <span style={{ width: 112, display: 'inline-block' }}>{value}</span>
                {radio}
                {authorized && (
                  <Button type="link" compact onClick={() => ctx.handleEdit(record.id)}>
                    修改
                  </Button>
                )}
              </Space>
            );
          }
        }
        // 编辑状态
        if (
          record.editable ||
          record.checkValueJson?.value === null ||
          record.checkNormal === null
        ) {
          const inItMinMaxValue =
            typeof record.checkValueJson?.minValue === 'number' &&
            typeof record.checkValueJson?.maxValue === 'number';
          return (
            <Space>
              <PointAIInput
                disabled={(!room.startCheck || record.checkNormal) && !record.editable}
                isNumValue={!!record.checkValueJson.minValue && !!record.checkValueJson.maxValue}
                value={record.checkValueJson?.value}
                onSave={() => ctx.handleCustomCheckItemValue(record, record.checkValueJson?.value)}
                hiddenSuffix={!inItMinMaxValue}
                onChange={ctx.onChangeAIValue(record.id)}
              />
              <span>{record.checkValueJson?.unit}</span>
              {!inItMinMaxValue && record.checkValueJson?.value && (
                <>
                  <Tag
                    style={{ cursor: 'pointer' }}
                    onClick={() =>
                      ctx.handleCustomCheckItemValue(record, record.checkValueJson?.value, {
                        customize: true,
                        situation: 'normal',
                      })
                    }
                  >
                    正常
                  </Tag>
                  <Tag
                    style={{ cursor: 'pointer' }}
                    onClick={() =>
                      ctx.handleCustomCheckItemValue(record, record.checkValueJson?.value, {
                        customize: true,
                        situation: 'abnormal',
                      })
                    }
                  >
                    异常
                  </Tag>
                </>
              )}
              {/* 未填写数值 只填写单位 支持自定义勾选 正常或者异常逻辑 在编辑状态需要 需要隐藏 radio inItMinMaxValue */}
              {inItMinMaxValue && (
                <>
                  {((record.checkValueJson?.value !== null &&
                    record.checkValueJson?.value !== '' &&
                    !record.editable) ||
                    record.checkNormal !== null) &&
                    radio}
                </>
              )}
            </Space>
          );
        }
      }
      if (
        authorized &&
        (record.itemType === PATROL_CHECK_ITEM_TYPE.CUSTOMIZE ||
          (record.itemType === PATROL_CHECK_ITEM_TYPE.SYSTEM_POINT && !record.isMeterRead))
      ) {
        return (
          <Radio.Group
            disabled={
              record.checkNormal === TICKET_CHECK_ITEM_RESULT_KEY_MAP.EXCEPTION ||
              !room.startCheck ||
              (record.checkNormal === TICKET_CHECK_ITEM_RESULT_KEY_MAP.UN_HANDLE_EX &&
                showNotProcessed)
            }
            value={
              record.checkNormal === TICKET_CHECK_ITEM_RESULT_KEY_MAP.UN_HANDLE_EX
                ? TICKET_CHECK_ITEM_RESULT_KEY_MAP.EXCEPTION
                : record.checkNormal
            }
            onChange={ctx.handleCheckItemStatus(record)}
          >
            <Radio value={TICKET_CHECK_ITEM_RESULT_KEY_MAP.NORMAL}>正常</Radio>
            <Radio value={TICKET_CHECK_ITEM_RESULT_KEY_MAP.EXCEPTION}>
              <Typography.Text
                type={
                  ctx.state.exceptionInfo.id === record.id ||
                  record.checkNormal === TICKET_CHECK_ITEM_RESULT_KEY_MAP.EXCEPTION
                    ? 'danger'
                    : 'default'
                }
              >
                异常
              </Typography.Text>
            </Radio>
          </Radio.Group>
        );
      }
      if (record.itemType === PATROL_CHECK_ITEM_TYPE.SYSTEM_POINT) {
        if (record.checkValueJson?.value !== null && !record.editable && authorized) {
          let text = record.checkValueJson?.value;
          if (record.checkValueJson?.dataType === 'DI') {
            text = getDiPointValueText(text, record.checkValueJson?.diValueText.split(','));
          }
          if (record.checkNormal === TICKET_CHECK_ITEM_RESULT_KEY_MAP.NORMAL) {
            return (
              <Space>
                <span>
                  {text}
                  {record.checkValueJson?.unit}
                </span>
                {authorized && (
                  <Button type="link" compact onClick={() => ctx.handleEdit(record.id)}>
                    修改
                  </Button>
                )}
              </Space>
            );
          }
          if (
            record.checkNormal === TICKET_CHECK_ITEM_RESULT_KEY_MAP.EXCEPTION ||
            record.checkNormal === TICKET_CHECK_ITEM_RESULT_KEY_MAP.UN_HANDLE_EX
          ) {
            return (
              <div>
                <StatusText
                  style={{ display: 'inline-block' }}
                  fontSize={14}
                  status={STATUS_MAP.ALARM}
                >
                  {text}
                </StatusText>
                {record.checkValueJson?.dataType === 'AI' && record.checkValueJson?.unit}
              </div>
            );
          }
        }

        if (
          (record.editable ||
            record.checkValueJson?.value === null ||
            record.checkNormal === null) &&
          authorized
        ) {
          if (record.checkValueJson?.dataType === 'DI') {
            return (
              <Space>
                <PointDISelect
                  disabled={(!room.startCheck || record.checkNormal) && !record.editable}
                  diValueText={record.checkValueJson?.diValueText}
                  value={
                    record.checkValueJson?.value && record.checkValueJson.value !== null
                      ? Number(record.checkValueJson.value)
                      : undefined
                  }
                  onSelect={value => ctx.onSavePoint(record, value)}
                />
                {invalidValues.includes(record.id) && radio}
              </Space>
            );
          }
          if (record.checkValueJson?.dataType === 'AI' && authorized) {
            return (
              <Space>
                <PointAIInput
                  disabled={(!room.startCheck || record.checkNormal) && !record.editable}
                  isNumValue={!!record.checkValueJson.minValue && !!record.checkValueJson.maxValue}
                  value={record.checkValueJson?.value}
                  onSave={() => ctx.onSavePoint(record, record.checkValueJson?.value)}
                  onChange={ctx.onChangeAIValue(record.id)}
                />
                <span>{record.checkValueJson?.unit}</span>
              </Space>
            );
          }
        }
      }
    },
  },
  {
    title: '备注',
    dataIndex: 'checkValueJson.resultSource',
    hidden: false,
    render: (_, record) => {
      // 如果是占位项，只显示检查对象名称
      if (record.isPlaceholder) {
        return '';
      }
      const { relateOpType, relateType, relateNo, remark } = record.checkValueJson;
      const isEvent = relateType === 'EVENT' || relateType === 'NEW_EVENT';
      return relateNo ? (
        <Typography.Text>
          {relateOpType === 'CREATE' ? '创建' : '关联'}
          {isEvent ? '事件' : '维修'}工单：
          <Typography.Text>
            {isEvent ? (
              <Link
                to={
                  relateType === 'NEW_EVENT'
                    ? generateEvnetLocation({
                        id: relateNo,
                      })
                    : generateEventDetailRoutePath({
                        id: relateNo,
                      })
                }
              >
                {relateNo}
              </Link>
            ) : (
              <Link
                target="_blank"
                to={{
                  pathname: generateTicketUrl({
                    ticketType: 'repair',
                    id: relateNo,
                  }),
                }}
              >
                {relateNo}
              </Link>
            )}
          </Typography.Text>
          <br />
          <p>{remark}</p>
        </Typography.Text>
      ) : (
        (<p>{remark}</p> ?? ' ')
      );
    },
  },
  {
    title: '操作',
    dataIndex: 'operation',
    hidden: !(
      +ctx.props.roomInfo?.find(({ roomGuid }) => roomGuid === ctx.props.roomGuid)?.inspectTime >
        0 &&
      ![
        BackendTaskStatus.WAITTAKEOVER,
        BackendTaskStatus.FINISH,
        BackendTaskStatus.FAILURE,
        BackendTaskStatus.CLOSE_APPROVER,
      ].includes(basicInfo.taskStatus)
    ),
    render: (_, record) => {
      // 如果是占位项，只显示检查对象名称
      if (record.isPlaceholder) {
        return '';
      }
      return (
        <Space>
          {record.checkNormal === 'UN_HANDLE_EX' && (!showNotProcessed || authorized) && (
            <Button
              type="link"
              onClick={() => {
                ctx.setState({
                  exceptionVisible: true,
                  exceptionInfo: record,
                });
              }}
            >
              处理异常
            </Button>
          )}
          {authorized && basicInfo.taskStatus === BackendTaskStatus.PROCESSING && (
            <UploadInspectItemFileModal
              inspectItemId={record.id}
              fileInfoList={record.fileInfoList ?? []}
              onSearch={() => ctx.getPatrolCheckItem()}
            />
          )}
        </Space>
      );
    },
  },
];

export class PatorlDetail extends React.Component {
  state = {
    primaryKey: '',
    exceptionVisible: false,
    exceptionInfo: {},
    successCallbackErrorMsg: '',
    allException: false,
    processedException: false,
    unprocessedException: false,
    invalidValues: [],
  };

  componentDidMount() {
    this.getPatrolCheckItem();
  }

  getPatrolCheckItem = () => {
    this.props.ticketPatrolCheckItemActionCreator({
      roomGuid: this.props.roomGuid,
      taskNo: this.props.basicInfo.taskNo,
      checkSubjectType: this.props.checkSubjectType,
      deviceType: this.props.deviceType,
    });
  };

  handleChangeCurrentContent = () => {
    this.props.changeCurrentContent(
      'deviceType',
      this.props.roomGuid,
      this.props.deviceType,
      this.props.checkSubjectType
    );
  };

  handleChangeCheckableTag = values => {
    this.props.setCheckItemTypes(values);
  };

  getList = () => {
    const { checkItem, checkItemTypes } = this.props;
    const { primaryKey, allException, processedException, unprocessedException } = this.state;
    let list = [...checkItem];
    if (checkItemTypes.length) {
      list = list.filter(checkItem => checkItemTypes.includes(checkItem.subTypeName));
    } else {
      list = [];
    }
    if (primaryKey) {
      list = list.filter(checkItem =>
        checkItem.checkSubjectTag.toLowerCase().includes(primaryKey.toLowerCase())
      );
    }
    list = list.filter(checkItem => {
      const isException = this.isException(checkItem.checkNormal);
      if (allException || (processedException && unprocessedException)) {
        return isException;
      }
      if (!allException && processedException && !unprocessedException) {
        return isException && checkItem.checkValueJson.relateNo;
      }
      if (!allException && !processedException && unprocessedException) {
        return isException && !checkItem.checkValueJson.relateNo;
      }
      return true;
    });

    // 开关机 筛选
    // 先收集所有数据的 checkScenes 字段，按 checkSubjectGuid 分组
    const groupedBySubjectGuid = {};
    const noSubjectGuidItems = [];

    // 用于存储每个分组的 checkScenes 字段的并集
    const checkScenesMap = {};

    // 用于存储每个分组中异常项的 checkScenes 字段的并集
    const exceptionScenesMap = {};

    // 分组并处理没有 checkSubjectGuid 的项
    list.forEach(item => {
      if (!item.checkSubjectGuid || !item.supportCheckScenes) {
        noSubjectGuidItems.push(item);
        return;
      }

      if (item.checkScenes?.split(',')?.some(i => i === 'ALL')) {
        item = { ...item, checkScenes: item?.checkScenes?.replace('ALL', '') };
      }

      if (!groupedBySubjectGuid[item.checkSubjectGuid]) {
        groupedBySubjectGuid[item.checkSubjectGuid] = [];
        // 初始化该分组的 checkScenes 并集
        checkScenesMap[item.checkSubjectGuid] = new Set();
        // 初始化该分组的异常项 checkScenes 并集
        exceptionScenesMap[item.checkSubjectGuid] = new Set();
      }

      // 收集 checkScenes 字段
      if (item.checkScenes) {
        const scenes = item.checkScenes.split(',');
        scenes.forEach(scene => checkScenesMap[item.checkSubjectGuid].add(scene));

        // 如果是异常项，收集到异常项的 checkScenes 并集中
        if (item.checkNormal === TICKET_CHECK_ITEM_RESULT_KEY_MAP.EXCEPTION) {
          scenes.forEach(scene => exceptionScenesMap[item.checkSubjectGuid].add(scene));
        }
      }

      groupedBySubjectGuid[item.checkSubjectGuid].push(item);
    });

    // 对每组数据进行处理
    const result = [...noSubjectGuidItems]; // 先添加没有 checkSubjectGuid 的项

    // 遍历每个分组
    Object.keys(groupedBySubjectGuid).forEach(subjectGuid => {
      const group = groupedBySubjectGuid[subjectGuid];
      if (group.length === 0) {
        return;
      }

      // 将收集到的 checkScenes 转换为数组
      const initChecks = Array.from(checkScenesMap[subjectGuid]);

      // 将收集到的异常项 checkScenes 转换为数组
      const exceptionScenes = Array.from(exceptionScenesMap[subjectGuid]);

      // 为该分组中的每一项添加 initChecks 字段
      for (let i = 0; i < group.length; i++) {
        const item = group[i];
        // 创建对象的副本，避免修改不可扩展的对象
        const newItem = { ...item, initChecks, exceptionScenes };
        // 替换原来的对象
        group[i] = newItem;
      }

      // 用于存储符合条件的项
      const filteredGroup = [];

      // 对组内每一项进行处理
      group.forEach(item => {
        //  deviceStatus 为 ''
        if (item.deviceStatus === '') {
          return;
        }
        //  如果没有 deviceStatus 或 checkScenes，直接保留
        if (!item.deviceStatus || !item.checkScenes) {
          filteredGroup.push(item);
          return;
        }

        // 将 deviceStatus 和 checkScenes 转换为数组
        const deviceStatusArray = item.deviceStatus.split(',');
        const checkScenesArray = item.checkScenes.split(',');

        // 检查 checkScenes 中是否有任何值在 deviceStatus 中
        const hasCommonValue = checkScenesArray.some(scene => deviceStatusArray.includes(scene));

        // 只要 checkScenes 中有任何值在 deviceStatus 中，就保留该项
        if (hasCommonValue) {
          filteredGroup.push(item);
        }
      });

      // 如果该分组被完全过滤掉且原分组有数据，添加一个标记项
      if (filteredGroup.length === 0 && group.length >= 1) {
        // 创建一个标记项，只保留必要的字段
        const placeholderItem = {
          ...group[0], // 复制第一项的所有字段
          taskNo: group[0].taskNo,
          id: `placeholder-${subjectGuid}`,
          isPlaceholder: true, // 标记为占位项
          checkSubjectGuid: subjectGuid, // 保留 checkSubjectGuid 以便于分组
          checkSubjectTag: group[0].checkSubjectTag, // 保留检查对象名称，方便筛选
          deviceStatus: group[0].deviceStatus,
          initChecks: initChecks, // 添加 initChecks 字段
          exceptionScenes: exceptionScenes, // 添加 exceptionScenes 字段
        };
        result.push(placeholderItem);
      } else {
        // 将符合条件的项添加到结果中
        result.push(...filteredGroup);
      }
    });

    return result.sort((a, b) => {
      const sortA = a.sort === null ? Infinity : a.sort; // null 视为最大值
      const sortB = b.sort === null ? Infinity : b.sort;
      return sortA - sortB;
    });
  };

  isException = checkNormal =>
    checkNormal === TICKET_CHECK_ITEM_RESULT_KEY_MAP.EXCEPTION ||
    checkNormal === TICKET_CHECK_ITEM_RESULT_KEY_MAP.UN_HANDLE_EX;

  handleEdit = id => {
    const { checkItem } = this.props;
    const list = checkItem.map(item => {
      if (item.id === id) {
        return { ...item, editable: true };
      }
      return {
        ...item,
      };
    });
    this.props.setPatrolCheckItem(list);
  };

  onChangeAIValue = id => e => {
    const { checkItem } = this.props;
    const list = checkItem.map(item => {
      if (item.id === id) {
        return {
          ...item,
          editable: true,
          checkValueJson: {
            ...item.checkValueJson,
            value: e.target.value,
          },
        };
      }
      return {
        ...item,
      };
    });
    this.props.setPatrolCheckItem(list);
  };

  onSavePoint = (record, value) => {
    if (!value.toString()) {
      return;
    }
    new Promise(() => {
      this.props.ticketPatrolCheckItemResultSave({
        params: {
          taskNo: record.taskNo,
          inspectItemId: record.id,
          value,
        },
        // 后端判断测点值异常需要填写异常信息
        successCallback: successCallbackErrorMsg => {
          if (successCallbackErrorMsg === 'INVALID_VALUE') {
            this.setState({
              invalidValues: [...this.state.invalidValues, record.id],
            });
            this.handleCustomCheckItemValue(record, value);

            return;
          }
          this.setState({
            exceptionVisible: true,
            successCallbackErrorMsg,
            exceptionInfo: {
              ...record,
              checkValueJson: { ...record.checkValueJson, value: value },
            },
          });
        },
        fetchPatrolCheckItemParams: {
          roomGuid: this.props.roomGuid,
          taskNo: this.props.basicInfo.taskNo,
          checkSubjectType: this.props.checkSubjectType,
          deviceType: this.props.deviceType,
        },
      });
    });
  };

  handleCheckItemStatus = (record, valueText, status) => async e => {
    const value = e?.target.value;
    const { basicInfo } = this.props;
    if (
      value === TICKET_CHECK_ITEM_RESULT_KEY_MAP.NORMAL ||
      status === TICKET_CHECK_ITEM_RESULT_KEY_MAP.NORMAL
    ) {
      this.props.setPatrolCheckItemSaveValueLoading();

      const { error } = await ticketService.patrolCheckItemNormalSave({
        taskNo: basicInfo.taskNo,
        inspectItemId: record.id,
        valueText,
      });
      this.props.setPatrolCheckItemSaveValueLoading();
      if (error) {
        message.error(error);
        return;
      }
      this.getPatrolCheckItem();
    }
    if (
      value === TICKET_CHECK_ITEM_RESULT_KEY_MAP.EXCEPTION ||
      status === TICKET_CHECK_ITEM_RESULT_KEY_MAP.EXCEPTION
    ) {
      this.setState({ exceptionVisible: true, exceptionInfo: { ...record, valueText } });
    }
  };

  // 自定义抄表巡检项
  handleCustomCheckItemStatus = record => async e => {
    const value = e.target.value;
    const { basicInfo } = this.props;
    if (value === TICKET_CHECK_ITEM_RESULT_KEY_MAP.NORMAL) {
      this.props.setPatrolCheckItemSaveValueLoading();

      const { error } = await ticketService.patrolCheckItemCustomizeResultSave({
        taskNo: basicInfo.taskNo,
        inspectItemId: record.id,
        isNormal: true,
      });
      this.props.setPatrolCheckItemSaveValueLoading();
      if (error) {
        message.error(error);
        return;
      }
      this.getPatrolCheckItem();
      this.setState({
        invalidValues: this.state.invalidValues.filter(item => item !== record.id),
      });
    }
    if (value === TICKET_CHECK_ITEM_RESULT_KEY_MAP.EXCEPTION) {
      this.setState({ exceptionVisible: true, exceptionInfo: record });
    }
  };

  // 自定义抄表巡检项数值
  handleCustomCheckItemValue = async (record, value, customaryState) => {
    if (!value.toString()) {
      return;
    }
    const { customize, situation } = customaryState || { customize: false, situation: undefined };
    // 未填写数值 只填写单位 支持自定义勾选 正常或者异常逻辑 customize
    const isNumValue =
      (typeof record.checkValueJson.minValue === 'number' &&
        typeof record.checkValueJson.maxValue === 'number') ||
      customize;
    const { basicInfo } = this.props;
    this.props.setPatrolCheckItemSaveValueLoading();
    const params = {
      taskNo: basicInfo.taskNo,
      inspectItemId: record.id,
      value: isNumValue ? value : undefined,
      valueText: isNumValue ? undefined : value,
      clearCheckResult: true,
    };

    if (isNumValue) {
      params.clearCheckResult = false;
      if (
        (record.checkValueJson.minValue <= Number(value) &&
          Number(value) <= record.checkValueJson.maxValue) ||
        situation === 'normal'
      ) {
        params.isNormal = true;
      } else {
        this.setState({
          exceptionVisible: true,
          exceptionInfo: { ...record, checkValueJson: { ...record.checkValueJson, value } },
        });
        this.props.setPatrolCheckItemSaveValueLoading();
        return;
      }
    }
    const { error } = await ticketService.patrolCheckItemCustomizeResultSave(params);
    this.props.setPatrolCheckItemSaveValueLoading();
    if (error) {
      message.error(error);
      return;
    }
    this.getPatrolCheckItem();
  };

  setCheckItem = (record, checkNormal) => {
    const { checkItem } = this.props;

    const list = checkItem.map(item => {
      if (item.id === record.id) {
        return { ...item, checkNormal: checkNormal };
      }
      return {
        ...item,
      };
    });
    this.props.setPatrolCheckItem(list);
  };

  onExceptionVisible = type => {
    this.setState({ exceptionVisible: false, successCallbackErrorMsg: '' });
    if (type === 'cancel') {
      const list = this.props.checkItem.map(item => {
        if (item.id === this.state.exceptionInfo.id) {
          return {
            ...item,
            checkNormal:
              item.itemType === PATROL_CHECK_ITEM_TYPE.CUSTOMIZE ? item.checkNormal : null,
            editable: item.itemType === PATROL_CHECK_ITEM_TYPE.CUSTOMIZE ? item.editable : true,
            checkValueJson:
              item.itemType === PATROL_CHECK_ITEM_TYPE.CUSTOMIZE
                ? item.checkValueJson
                : item.checkValueJson && { ...item.checkValueJson, value: null },
          };
        }
        return {
          ...item,
        };
      });

      this.props.setPatrolCheckItem(list);
      this.setState({ exceptionInfo: {} });
    }
    if (type !== 'cancel') {
      this.setState({
        invalidValues: this.state.invalidValues.filter(
          item => item !== this.state.exceptionInfo.id
        ),
      });
    }
    this.getPatrolCheckItem();
  };

  onConfirm = params => {
    return new Promise(async resolve => {
      const { exceptionInfo } = this.state;
      const { basicInfo } = this.props;
      const { error } = await ticketService.patrolCheckItemExceptionHandling({
        ...params,
        taskNo: exceptionInfo.taskNo,
        inspectItemId: exceptionInfo.id,
        idcTag: basicInfo.idcTag,
        blockGuid: basicInfo.blockTag,
        pointValue: exceptionInfo.checkValueJson?.value,
        valueText: exceptionInfo.valueText,
      });
      if (error) {
        message.error(error);
        resolve(false);
        return;
      }
      resolve(true);
    });
  };

  render() {
    const { basicInfo, checkItemSaveValueLoading, checkItem, roomInfo } = this.props;
    const {
      exceptionVisible,
      exceptionInfo,
      successCallbackErrorMsg,
      allException,
      processedException,
      unprocessedException,
      invalidValues,
    } = this.state;

    // 获取筛选后的数据
    const datasource = this.getList();

    // 基于筛选后的数据计算 rowSpansGroupByDataIndex
    const rowSpansGroupByDataIndex = {};

    // 计算每个字段的计数
    datasource.forEach(record => {
      // 处理 checkSubjectGuid
      if (record.checkSubjectGuid) {
        if (rowSpansGroupByDataIndex[record.checkSubjectGuid] === undefined) {
          rowSpansGroupByDataIndex[record.checkSubjectGuid] = 1;
        } else {
          rowSpansGroupByDataIndex[record.checkSubjectGuid] += 1;
        }
      }

      // 处理 metaCode
      if (record.metaCode) {
        if (rowSpansGroupByDataIndex[record.metaCode] === undefined) {
          rowSpansGroupByDataIndex[record.metaCode] = 1;
        } else {
          rowSpansGroupByDataIndex[record.metaCode] += 1;
        }
      }

      // 处理 subTypeName
      if (record.subTypeName) {
        if (rowSpansGroupByDataIndex[record.subTypeName] === undefined) {
          rowSpansGroupByDataIndex[record.subTypeName] = 1;
        } else {
          rowSpansGroupByDataIndex[record.subTypeName] += 1;
        }
      }
    });

    const showList = datasource;
    // 当 rowSpansGroupByDataIndex 不存在或者 checkItem 不存在时，显示加载中
    if (!rowSpansGroupByDataIndex || !checkItem || checkItem.length === 0) {
      return (
        <GutterWrapper flex justifyContent="center">
          <Spin />
        </GutterWrapper>
      );
    }
    const handling = [
      { value: CHECK_SUBJECT_KEY_MAPS.REPAIR, label: '维修工单' },
      { value: CHECK_SUBJECT_KEY_MAPS.EVENT, label: '事件工单' },
      { value: CHECK_SUBJECT_KEY_MAPS.NOT_PROCESSED, label: '暂不处理' },
      // { value: CHECK_SUBJECT_KEY_MAPS.RISK, label: '风险登记单' },
    ];

    const room = roomInfo.find(({ roomGuid }) => roomGuid === this.props.roomGuid);
    return (
      <Space direction="vertical" style={{ width: '100%' }} size="middle">
        <Space
          direction="horizontal"
          flex
          style={{ width: '100%', justifyContent: 'space-between' }}
        >
          <Space>
            <PageHeader
              style={{ padding: 0 }}
              title={checkItem?.[0]?.subTypeName ?? ' '}
              onBack={this.handleChangeCurrentContent}
            />
            <Input.Search
              style={{ width: 200 }}
              placeholder="请输入检查对象名称"
              onSearch={value => this.setState({ primaryKey: value })}
            />
          </Space>
        </Space>
        <div style={{ width: '100%', display: 'flex' }}>
          <Card
            style={{
              flex: 1,
              borderColor: allException
                ? `var(--${prefixCls}-primary-color)`
                : `var(--border-color-split)`,
            }}
            bodyStyle={{
              justifyContent: 'space-between',
              display: 'flex',
              alignItems: 'center',
            }}
            onClick={() => {
              this.setState(({ allException }) => ({ allException: !allException }));
            }}
          >
            <Space direction="vertical" style={{ width: '100%', display: 'flex' }}>
              <Tooltip title="该巡检工单所有异常检查结果数量">
                <Typography.Text type="secondary">
                  异常项 <QuestionCircleOutlined />
                </Typography.Text>
              </Tooltip>
              <Typography.Title level={3}>
                {checkItem.filter(item => this.isException(item.checkNormal)).length}
              </Typography.Title>
            </Space>
            <img width={40} height={40} src={allExpectionSvg} alt="circle" />
          </Card>
          <Card
            style={{
              flex: 1,
              margin: '0 16px',
              borderColor: processedException
                ? `var(--${prefixCls}-primary-color)`
                : `var(--border-color-split)`,
            }}
            bodyStyle={{
              justifyContent: 'space-between',
              display: 'flex',
              alignItems: 'center',
            }}
            onClick={() => {
              this.setState(({ processedException }) => ({
                processedException: !processedException,
              }));
            }}
          >
            <Space direction="vertical" style={{ width: '100%', display: 'flex' }}>
              <Tooltip title="已关联/创建事件/维修的异常结果数量">
                <Typography.Text type="secondary">
                  已处理异常项 <QuestionCircleOutlined />
                </Typography.Text>
              </Tooltip>
              <Typography.Title level={3}>
                {
                  checkItem.filter(
                    item => this.isException(item.checkNormal) && item.checkValueJson.relateNo
                  ).length
                }
              </Typography.Title>
            </Space>
            <img width={40} height={40} src={processedExceptionSvg} alt="circle" />
          </Card>
          <Card
            style={{
              flex: 1,
              borderColor: unprocessedException
                ? `var(--${prefixCls}-primary-color)`
                : `var(--border-color-split)`,
            }}
            bodyStyle={{
              justifyContent: 'space-between',
              display: 'flex',
              alignItems: 'center',
            }}
            onClick={() => {
              this.setState(({ unprocessedException }) => ({
                unprocessedException: !unprocessedException,
              }));
            }}
          >
            <Space direction="vertical" style={{ width: '100%', display: 'flex' }}>
              <Tooltip title="未关联/创建事件/维修的异常结果数量">
                <Typography.Text type="secondary">
                  未处理异常项 <QuestionCircleOutlined />
                </Typography.Text>
              </Tooltip>
              <Typography.Title level={3}>
                {
                  checkItem.filter(
                    item => this.isException(item.checkNormal) && !item.checkValueJson.relateNo
                  ).length
                }
              </Typography.Title>
            </Space>
            <img width={40} height={40} src={unprocessedExceptionSvg} alt="circle" />
          </Card>
        </div>
        <RenderAuthorizedTable userId={basicInfo.taskAssignee}>
          {(authorized, showNotProcessed) =>
            rowSpansGroupByDataIndex && (
              <TinyTable
                rowKey="id"
                columns={columns(
                  this,
                  basicInfo,
                  authorized,
                  showList,
                  rowSpansGroupByDataIndex,
                  invalidValues,
                  showNotProcessed
                ).filter(column => !column.hidden)}
                scroll={{ x: 'max-content' }}
                dataSource={showList}
                loading={checkItemSaveValueLoading}
                pagination={false}
              />
            )
          }
        </RenderAuthorizedTable>
        <ExceptionModal
          basicInfo={basicInfo}
          exceptionInfo={{
            ...exceptionInfo,
            roomType: this.props.roomType,
            deviceGuid: exceptionInfo.checkSubjectGuid,
            deviceTypeName: exceptionInfo.subTypeName,
            itemName: `${exceptionInfo.checkItemName}${
              exceptionInfo.checkStd ? '-' + exceptionInfo.checkStd : ''
            }`,
          }}
          visible={exceptionVisible}
          handling={handling}
          successCallbackErrorMsg={successCallbackErrorMsg}
          checkType={
            exceptionInfo.checkSubjectType === INSPECTION_TYPE_KEY_MAP.ENVIRONMENT
              ? 'ROOM'
              : 'DEVICE'
          }
          taskType="INSPECTION"
          onExceptionVisible={this.onExceptionVisible}
          onConfirm={this.onConfirm}
        />
      </Space>
    );
  }
}

const mapStateToProps = ({
  ticket: {
    patrol: { detail },
  },
}) => ({
  checkItem: detail.checkItem,
  checkItemTypes: detail.checkItemTypes,
  loading: detail.checkItemLoading,
  checkItemSaveValueLoading: detail.checkItemSaveValueLoading,
  rowSpansGroupByDataIndex: detail.rowSpansGroupByDataIndex,
  roomInfo: detail.roomInfo,
});

const mapDispatchToProps = {
  ticketPatrolRoomInfoActionCreator: ticketPatrolRoomInfoActionCreator,
  ticketPatrolCheckItemActionCreator: ticketPatrolCheckItemActionCreator,
  setCheckItemTypes: ticketActions.setPatrolCheckItemTypes,
  setPatrolCheckItem: ticketActions.setPatrolCheckItem,
  ticketPatrolCheckItemResultSave: ticketPatrolCheckItemPointResultSaveActionCreator,
  setPatrolCheckItemSaveValueLoading: ticketActions.setPatrolCheckItemSaveValueLoading,
};
export default connect(mapStateToProps, mapDispatchToProps)(PatorlDetail);

function RenderAuthorizedTable({ userId, children }) {
  const [, { checkUserId }] = useAuthorized();
  const [configUtil] = useConfigUtil();
  const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket');
  const showNotProcessed = ticketScopeCommonConfigs.powerOnOff.features.showNotProcessed;
  const isCurrentUser = checkUserId(userId);
  if (typeof children == 'function') {
    return children(isCurrentUser, showNotProcessed);
  }
  return isCurrentUser ? children : <Redirect to={{ pathname: '/403' }} />;
}
