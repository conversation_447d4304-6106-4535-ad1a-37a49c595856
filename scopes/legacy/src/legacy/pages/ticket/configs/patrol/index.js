import dayjs from 'dayjs';
import cloneDeep from 'lodash.clonedeep';
import pick from 'lodash.pick';
import moment from 'moment';

import { AUDIT_LOG_LIST_ROUTE_AUTH_CODE } from '@manyun/auth-hub.route.auth-routes';
import { BackendTaskStatus } from '@manyun/ticket.model.ticket';
import { TicketStatusSelect } from '@manyun/ticket.ui.ticket-status-select';

import { LogLinkButton } from '../../components/log-link-button';
import { CURRENT_PAGE_TYPES } from '../../constants';
import TTR from './../../registries/ticket-type-registry';
import Detail from './components/detail';
import NewTicket from './components/new-ticket';

TTR.registerTicketType('inspection')
  .registerConfig({
    type: CURRENT_PAGE_TYPES.TICKETS,
    showNewBtn: true,
    showRevokeOperation: true,
    mergeFilters: ({ DEFAULT_FILTER_KEY_MAP, defaultFilters }) => {
      const newFilters = [...defaultFilters].filter(
        ({ key }) => key !== DEFAULT_FILTER_KEY_MAP.END_TIME
      );
      const taskStatusIndex = newFilters.findIndex(
        item => item.key === DEFAULT_FILTER_KEY_MAP.TICKET_STATE
      );
      if (taskStatusIndex !== -1) {
        newFilters.splice(
          newFilters.findIndex(item => item.key === 'taskStatusList'),
          1,
          {
            label: '工单状态',
            key: 'taskStatusList',
            initialProps: {
              optionFilter: option =>
                ![BackendTaskStatus.INIT, BackendTaskStatus.UNDO].includes(option.value),
              allowClear: true,
              mode: 'multiple',
            },
            Comp: TicketStatusSelect,
          }
        );
        const targetOrderKeys = [
          DEFAULT_FILTER_KEY_MAP.TASK_NO,
          DEFAULT_FILTER_KEY_MAP.TASK_SUB_TYPE,
          DEFAULT_FILTER_KEY_MAP.TICKET_STATE, // 第三位
          DEFAULT_FILTER_KEY_MAP.EFFECT_TIME, // 第四位
          DEFAULT_FILTER_KEY_MAP.LOCATION,
          DEFAULT_FILTER_KEY_MAP.CREATOR_NAME,
        ];

        const filterMap = new Map(newFilters.map(item => [item.key, item]));

        const rearrangedFilters = [];
        const handledKeys = new Set();

        // 按目标顺序添加元素
        targetOrderKeys.forEach(key => {
          if (filterMap.has(key)) {
            rearrangedFilters.push(filterMap.get(key));
            handledKeys.add(key);
          }
        });

        // 剩余的元素按其在原始数组中的相对顺序添加到末尾
        newFilters.forEach(item => {
          if (!handledKeys.has(item.key)) {
            rearrangedFilters.push(item);
          }
        });

        return rearrangedFilters;
      }

      return newFilters;
    },

    mergeRightActions: ({ defaultActions }) => {
      const newActions = cloneDeep(defaultActions);
      newActions.splice(0, 0, {
        key: 'linkToLog',
        Comp: LogLinkButton,
        propsUtils: {
          authCode: AUDIT_LOG_LIST_ROUTE_AUTH_CODE,
          pick: props => pick(props, ['ticketType']),
        },
      });
      return newActions;
    },
  })
  .registerConfig({
    type: CURRENT_PAGE_TYPES.NEW_TICKET,
    content: NewTicket,
  })
  .registerConfig({
    type: CURRENT_PAGE_TYPES.SPECIFIC_TICKET,
    content: Detail,
    showRevocationBtn: true,
  });
