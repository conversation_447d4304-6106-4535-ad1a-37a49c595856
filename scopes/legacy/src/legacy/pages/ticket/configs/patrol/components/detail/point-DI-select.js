import React from 'react';
import { connect } from 'react-redux';

import { Select } from '@galiojs/awesome-antd';

import { generateValidLimitsDataSource } from '@manyun/monitoring.model.point';

export function PointDISelect({ diValueText = '', forwardedRef, ...props }) {
  return (
    <Select ref={forwardedRef} {...props} style={{ width: 120 }}>
      {generateValidLimitsDataSource(diValueText.split(',')).map(item => (
        <Select.Option key={item.value} value={Number(item.value)}>
          {item.label}
        </Select.Option>
      ))}
    </Select>
  );
}

export default connect(null, null, null, { forwardRef: true })(
  React.forwardRef((props, ref) => <PointDISelect forwardedRef={ref} {...props} />)
);
