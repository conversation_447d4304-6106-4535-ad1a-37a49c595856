import CheckOutlined from '@ant-design/icons/es/icons/CheckOutlined';
import omit from 'lodash/omit';
import React from 'react';
import { connect } from 'react-redux';

import { Input } from '@manyun/base-ui.ui.input';

export function PointAIInput({ forwardedRef, onSave, onChange, ...props }) {
  return (
    <Input
      ref={forwardedRef}
      allowClear
      onChange={e => {
        const { value } = e.target;
        var reg = /^-?(0|[1-9][0-9]*)(\.[0-9]*)?$/;
        if (!props.isNumValue) {
          e.target.value = value;
          onChange(e);
          return;
        }
        if ((!Number.isNaN(value) && reg.test(value)) || value === '' || value === '-') {
          e.target.value = value;
          onChange(e);
        }
      }}
      {...omit(props, 'dispatch')}
      style={{ width: 120 }}
      suffix={!props.hiddenSuffix && <CheckOutlined onClick={onSave} />}
      maxLength={8}
    />
  );
}

export default connect(null, null, null, { forwardRef: true })(
  React.forwardRef((props, ref) => <PointAIInput forwardedRef={ref} {...props} />)
);
