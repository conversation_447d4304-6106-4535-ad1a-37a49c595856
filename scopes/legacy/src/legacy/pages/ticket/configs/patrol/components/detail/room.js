import sortBy from 'lodash/sortBy';
import uniqBy from 'lodash/uniqBy';
import React from 'react';
import { connect } from 'react-redux';

import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Input } from '@manyun/base-ui.ui.input';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';

import { DisplayCard, GutterWrapper } from '@manyun/dc-brain.legacy.components';
import { INSPECTION_DETAIL_RECENTLY_CLICK_ROOM } from '@manyun/dc-brain.legacy.constants/ticket';
import Enclosure from '@manyun/dc-brain.legacy.pages/ticket/configs/maintenance/components/detail/enclosure';
import {
  ticketActions,
  ticketPatrolRoomInfoActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/ticketActions';
import {
  getDisplayCardColor,
  getRecentlyClickCardCode,
  setRecentlyClickCardCode,
} from '@manyun/dc-brain.legacy.utils/ticket';

import { formatTime } from '../../../util.ts';

export class PatorlDetail extends React.Component {
  state = {
    primaryKey: '',
  };

  componentDidMount() {
    this.props.ticketPatrolRoomInfoActionCreator({ taskNo: this.props.taskNo });
  }

  handleChangeCurrentContent = (roomGuid, roomType) => {
    setRecentlyClickCardCode(roomGuid, this.props.taskNo, INSPECTION_DETAIL_RECENTLY_CLICK_ROOM);
    this.props.changeCurrentContent('deviceType', roomGuid, null, null);
    this.props.setRoomType(roomType);
  };

  handleChangeCheckableTag = values => {
    this.props.setPatrolRoomTypes(values);
  };

  getList = () => {
    const { roomInfo, checkedRoomTypes } = this.props;
    const { primaryKey } = this.state;
    let list = [...roomInfo];
    if (checkedRoomTypes.length) {
      list = list.filter(room => checkedRoomTypes.includes(room.roomType));
    } else {
      list = [];
    }
    if (primaryKey) {
      list = list.filter(room => room.roomTag.toLowerCase().includes(primaryKey.toLowerCase()));
    }
    const recentlyRoom = getRecentlyClickCardCode(INSPECTION_DETAIL_RECENTLY_CLICK_ROOM);
    list = list.sort((a, b) => {
      const sortA = a.sort === null ? Infinity : a.sort; // null 视为最大值
      const sortB = b.sort === null ? Infinity : b.sort;
      return sortA - sortB;
    });
    if (recentlyRoom) {
      list = sortBy(list, value => {
        return value.roomGuid !== recentlyRoom.code;
      });
    }

    return list;
  };

  render() {
    const { roomTypes, loading, checkedRoomTypes, roomInfo, basicInfo } = this.props;

    const typeFilterList = uniqBy(
      roomInfo.map(item => {
        return { value: item.roomType, label: roomTypes[item.roomType] };
      }),
      'value'
    );
    const showList = this.getList();

    if (loading) {
      return (
        <GutterWrapper flex justifyContent="center">
          <Spin />
        </GutterWrapper>
      );
    }

    return (
      <GutterWrapper mode="vertical">
        <div style={{ marginLeft: '2px' }}>
          <Enclosure label="巡检报告 : " basicInfo={basicInfo} />
        </div>
        <Space>
          <Checkbox.Group
            options={typeFilterList}
            value={checkedRoomTypes}
            onChange={this.handleChangeCheckableTag}
          />
          <Input.Search
            style={{ width: 200 }}
            placeholder="请输入包间编号"
            onSearch={value => this.setState({ primaryKey: value })}
          />
        </Space>
        <div
          style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fill,minmax(244px,1fr))',
            gridRowGap: '58px',
            gridColumnGap: '16px',
          }}
        >
          {showList.map(room => (
            <DisplayCard
              key={room.roomTag}
              title={
                <Space
                  style={{
                    width: ' 100%',
                    justifyContent: 'space-between',
                  }}
                >
                  <span>{`${room.roomTag}\n${roomTypes[room.roomType]}`}</span>
                </Space>
              }
              describe={formatTime(room.inspectTime)}
              subDescribe="巡检时长"
              backgroundColor={getDisplayCardColor({
                exceptionNum: room.exceptionNum,
                waitNum: room.waitInspectNum,
                alreadyNum: room.alreadyInspectNum,
                code: room?.roomGuid,
                taskNo: this.props.taskNo,
                recentlyCode: INSPECTION_DETAIL_RECENTLY_CLICK_ROOM,
              })}
              countList={[
                { describe: '总异常', number: room.exceptionNum, color: 'error' },
                { describe: '待巡检', number: room.waitInspectNum, color: 'warning' },
                {
                  describe: '已巡检',
                  number: room.alreadyInspectNum,
                  color: 'success',
                },
              ]}
              onClick={() => this.handleChangeCurrentContent(room.roomGuid, room.roomType)}
            />
          ))}
        </div>
      </GutterWrapper>
    );
  }
}

const mapStateToProps = ({
  ticket: {
    patrol: { detail },
    ticketView: { basicInfo },
  },
  common: { roomTypes },
}) => ({
  basicInfo,
  roomInfo: detail.roomInfo,
  checkedRoomTypes: detail.roomTypes,
  roomTypes: roomTypes || {},
  loading: detail.roomLoading,
});

const mapDispatchToProps = {
  ticketPatrolRoomInfoActionCreator: ticketPatrolRoomInfoActionCreator,
  setPatrolRoomTypes: ticketActions.setPatrolRoomTypes,
};
export default connect(mapStateToProps, mapDispatchToProps)(PatorlDetail);
