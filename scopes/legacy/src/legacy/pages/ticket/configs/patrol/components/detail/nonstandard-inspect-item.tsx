import React, { useCallback, useEffect, useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { SimpleFileList } from '@manyun/base-ui.ui.file-list';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';

import { useAuthorized } from '@manyun/auth-hub.hook.gql.use-authorized';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { McUploadFileJSON } from '@manyun/dc-brain.model.mc-upload-file';
import {
  useLazyQueryFetchNonstandardInspectItem,
  useUpdateOrDeleteNonstandardInspectItem,
} from '@manyun/ticket.gql.client.tickets';
import type {
  FetchNonstandardInspectItem,
  UpdateOrDeleteNonstandardInspectItemQ,
} from '@manyun/ticket.gql.client.tickets';
import { BackendTaskStatus } from '@manyun/ticket.model.ticket';

import { NonstandardInspectItemModalButton } from './nonstandard-inspect-item-modal-button';

export type NonstandardInspectItemProps = {
  roomGuid: string;
  taskNo: string;
  userId: number;
  taskStatus: BackendTaskStatus;
};

export function NonstandardInspectItem({
  roomGuid,
  taskNo,
  userId,
  taskStatus,
}: NonstandardInspectItemProps) {
  const [authorized] = useAuthorized({ checkByUserId: userId });

  const isAuthorizedProcess = authorized && taskStatus === BackendTaskStatus.PROCESSING;

  const [nonstandardInspectItem, setNonstandardInspectItem] =
    useState<(FetchNonstandardInspectItem | null)[]>();

  const [loading, setLoading] = useState(false);

  const [getNonstandardInspectItem] = useLazyQueryFetchNonstandardInspectItem();
  const [deleteNonstandardInspectItem] = useUpdateOrDeleteNonstandardInspectItem();

  const fetchNonstandardInspectItem = useCallback(() => {
    getNonstandardInspectItem({
      variables: { query: { taskNo, roomGuid } },
    }).then(({ data, error }) => {
      if (error) {
        message.error(error.message);
      } else if (data?.fetchNonstandardInspectItem?.data) {
        setNonstandardInspectItem(data?.fetchNonstandardInspectItem?.data);
      }
    });
  }, [taskNo, roomGuid, getNonstandardInspectItem]);

  const deleteNonstandardInspect = async (inspectData: UpdateOrDeleteNonstandardInspectItemQ) => {
    setLoading(true);
    const { data } = await deleteNonstandardInspectItem({
      variables: {
        query: {
          ...inspectData,
          isDelete: true,
          fileInfoList:
            Array.isArray(inspectData.fileInfoList) && inspectData.fileInfoList.length > 0
              ? inspectData.fileInfoList.map((file: McUploadFileJSON) =>
                  McUploadFile.fromJSON({
                    ...file,
                    uploadUser: { id: file.uploadUser.id, name: file.uploadUser.name },
                  })
                )
              : undefined,
        },
      },
    });
    const errorMessage = data?.updateOrDeleteNonstandardInspectItem?.message;

    if (errorMessage) {
      message.error(errorMessage);
      return;
    }
    message.success('删除成功');
    setLoading(false);
    fetchNonstandardInspectItem();
    return Promise.resolve(true);
  };

  useEffect(() => {
    fetchNonstandardInspectItem();
  }, [fetchNonstandardInspectItem]);

  const columns: Array<ColumnType<FetchNonstandardInspectItem>> = [
    {
      title: '检查对象',
      dataIndex: 'subjectName',
    },
    {
      title: '检查异常描述',
      dataIndex: 'exDesc',
      render: exDesc => (
        <Typography.Text style={{ maxWidth: 720 }} ellipsis={{ tooltip: true }}>
          {exDesc}
        </Typography.Text>
      ),
    },
    {
      title: '现场照片',
      dataIndex: 'fileInfoList',
      render: (_, record) =>
        Array.isArray(record?.fileInfoList) && record.fileInfoList.length > 0 ? (
          <SimpleFileList
            files={record?.fileInfoList}
            children={
              <Button type="link" compact>
                {record.fileInfoList.length}
              </Button>
            }
          />
        ) : (
          0
        ),
    },
    {
      title: '操作',
      dataIndex: 'operation',
      width: 104,
      fixed: 'right',
      render: (_, record) =>
        isAuthorizedProcess ? (
          <Space>
            <NonstandardInspectItemModalButton
              type="edit"
              roomGuid={roomGuid}
              taskNo={taskNo}
              nsItemId={record.id}
              data={{
                subjectName: record.subjectName,
                exDesc: record.exDesc,
                fileInfoList: record.fileInfoList,
              }}
              onSearch={fetchNonstandardInspectItem}
            />
            <Popconfirm
              title="是否删除非标准检查异常项?"
              onConfirm={() =>
                deleteNonstandardInspect({
                  nsItemId: record.id,
                  subjectName: record.subjectName,
                  exDesc: record.exDesc,
                  fileInfoList: record.fileInfoList,
                })
              }
            >
              <Button type="link" compact loading={loading}>
                删除
              </Button>
            </Popconfirm>
          </Space>
        ) : null,
    },
  ];
  return (
    <Space style={{ width: '100%' }} direction="vertical">
      {isAuthorizedProcess && (
        <NonstandardInspectItemModalButton
          type="new"
          roomGuid={roomGuid}
          taskNo={taskNo}
          onSearch={fetchNonstandardInspectItem}
        />
      )}
      <Table
        style={{ width: '100%' }}
        scroll={{ x: 'max-content' }}
        columns={columns}
        dataSource={nonstandardInspectItem}
      />
    </Space>
  );
}
