import React from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import { ApiSelect } from '@galiojs/awesome-antd';
import uniq from 'lodash/uniq';

import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';

import {
  AssetClassificationApiTreeSelect,
  GutterWrapper,
  TinyTable,
} from '@manyun/dc-brain.legacy.components';
import { getTableData } from '@manyun/dc-brain.legacy.components/inspection-table/utils';
import { ticketActions } from '@manyun/dc-brain.legacy.redux/actions/ticketActions';
import { equipmentManageService, roomManageService } from '@manyun/dc-brain.legacy.services';

const columns = (ctx, opreation) => {
  const list = [
    {
      title: '设备名称',
      dataIndex: 'name',
    },
    {
      title: '设备类型',
      dataIndex: 'deviceType',
      render: text => ctx.props.nomalizedDeviceCategory[text].metaName,
    },
    {
      title: '所属包间',
      dataIndex: ['spaceGuid', 'roomTag'],
    },
    {
      title: '厂商',
      dataIndex: 'vendor',
    },
  ];
  if (opreation) {
    list.push({
      title: '操作',
      dataIndex: 'operation',
      render: (_, record) => (
        <Button
          type="link"
          style={{ padding: 0, height: 'auto' }}
          onClick={() => ctx.handleDelete(record)}
        >
          删除
        </Button>
      ),
    });
  }
  return list;
};

export class Device extends React.Component {
  state = {
    visible: false,
    deviceList: [],
    total: 0,
    pageNum: 1,
    pageSize: 10,
    selectedRowKeys: [],
    selectedRows: [],
  };

  handleVisible = () => {
    this.setState(({ visible }) => ({ visible: !visible }));
    if (!this.state.visible) {
      this.getList(1, 10);
    } else {
      this.setState({
        selectedRowKeys: [],
        selectedRows: [],
      });
    }
  };

  getList = async (pageNum, pageSize) => {
    const params = this.getParams();
    params.pageNum = pageNum;
    params.pageSize = pageSize;
    const { response, error } = await equipmentManageService.fetchEquipmentListPage(params);
    if (error) {
      message.error(error);
      return;
    }
    this.setState({ deviceList: response.data, total: response.total, pageNum, pageSize });
  };

  getParams = () => {
    const { fieldValues, form, roomInfoList } = this.props;
    const { roomTags, deviceTypeList, name } = form.getFieldsValue();
    const device = this.getDevice();
    const rooms = roomInfoList.length ? roomInfoList.map(item => item.tag) : [];
    const p = {
      assetStatusList: ['REPAIR', 'NORMAL'],
      idcTag: fieldValues.location[0],
      blockTag: fieldValues.location[1],
      roomTags: rooms.length ? rooms : roomTags && roomTags.length ? roomTags : null,
      deviceTypeList: device?.length
        ? device
        : deviceTypeList && deviceTypeList.length
        ? deviceTypeList
        : null, // device是巡检项中的deviceType，如果有就取device，没有就取用户自己筛选的

      name,
    };

    return p;
  };

  onSelectChange = (selectedRowKeys, selectedRows) => {
    this.setState({
      selectedRowKeys,
      selectedRows,
    });
  };

  onChangePage = (pageNum, pageSize) => {
    this.getList(pageNum, pageSize);
  };

  handleOk = () => {
    this.props.setCreatePatrolDevices([...this.state.selectedRows, ...this.props.deviceInfoList]);
    this.props.onChange([...this.state.selectedRows, ...this.props.deviceInfoList]);
    this.handleVisible();
  };

  handleDelete = record => {
    const { deviceInfoList } = this.props;
    const deviceList = deviceInfoList.filter(item => record.guid !== item.guid);

    this.props.setCreatePatrolDevices(deviceList);
    this.props.onChange(deviceList);
  };

  getDevice = () => {
    const { checkItem } = this.props;
    const device = getTableData(checkItem)
      .map(item => {
        if (item.inspectSubject === 'DEVICE') {
          return item.subTypeCode;
        }
        return item.deviceType;
      })
      .filter(Boolean);
    return uniq(device);
  };

  render() {
    const { fieldValues, roomInfoList, deviceInfoList } = this.props;
    const { getFieldDecorator, getFieldsValue, setFieldsValue } = this.props.form;
    const { visible, deviceList, selectedRowKeys, selectedRows, total, pageNum, pageSize } =
      this.state;

    const device = this.getDevice();

    const { roomTags, deviceTypeList } = getFieldsValue();

    return (
      <GutterWrapper mode="vertical">
        <Button
          type="primary"
          disabled={fieldValues.location?.length !== 2}
          onClick={this.handleVisible}
        >
          选择设备
        </Button>
        <TinyTable
          rowKey="guid"
          size="small"
          scroll={{ x: true }}
          columns={columns(this, true)}
          // loading={loading}
          dataSource={deviceInfoList}
        />
        <Modal
          visible={visible}
          destroyOnClose
          title="选择设备"
          width={895}
          onCancel={this.handleVisible}
          onOk={this.handleOk}
        >
          <GutterWrapper mode="vertical">
            <Form layout="inline">
              <Form.Item label="资产分类">
                {getFieldDecorator('deviceTypeList', { initialValue: device })(
                  // <ApiTreeSelect
                  //   maxTagCount={1}
                  //   dataService={treeDataService.fetchDeviceCategory}
                  //   fieldNames={{ value: 'metaCode', key: 'metaCode', title: 'metaName' }}
                  //   disabledDepths={[0, 1]}
                  //   allowClear
                  //   multiple
                  //   disabled={!!device.length}
                  //   requestOnDidMount
                  //   style={{ width: 200 }}
                  // />
                  <AssetClassificationApiTreeSelect
                    dataType={['snDevice']}
                    category="categorycode"
                    maxTagCount={1}
                    requestOnDidMount
                    allowClear
                    multiple
                    disabledDepths={[0, 1]}
                    style={{ width: 200 }}
                    disabled={!!device.length}
                  />
                )}
              </Form.Item>
              <Form.Item label="包间">
                {getFieldDecorator('roomTags', {
                  initialValue: roomInfoList.length ? roomInfoList.map(item => item.tag) : [],
                })(
                  <ApiSelect
                    maxTagCount={1}
                    fieldNames={{ value: 'tag', label: 'tag' }}
                    allowClear
                    mode="multiple"
                    disabled={!!roomInfoList.length}
                    trigger="onDidMount"
                    style={{ width: 200 }}
                    dataService={async () => {
                      const { response } = await roomManageService.fetchRoomPage({
                        pageNum: 1,
                        pageSize: 200,
                        idcTag: fieldValues.location[0],
                        blockTag: fieldValues.location[1],
                      });
                      if (response) {
                        return response.data;
                      }
                    }}
                  />
                )}
              </Form.Item>
              <Form.Item label="设备名称">{getFieldDecorator('name')(<Input />)}</Form.Item>
            </Form>
            <GutterWrapper style={{ textAlign: 'right' }}>
              <Button
                type="primary"
                onClick={() => {
                  this.getList(1, pageSize);
                }}
              >
                搜索
              </Button>
              <Button
                onClick={() => {
                  setFieldsValue({
                    name: undefined,
                    roomTags: !!roomInfoList.length ? roomTags : undefined,
                    deviceTypeList: !!device.length ? deviceTypeList : undefined,
                  });
                  this.getList(1, pageSize);
                }}
              >
                重置
              </Button>
            </GutterWrapper>
            <TinyTable
              rowKey="guid"
              size="small"
              scroll={{ x: true }}
              columns={columns(this, false)}
              dataSource={deviceList}
              rowSelection={{
                selectedRowKeys,
                selectedRows,
                onChange: this.onSelectChange,
                getCheckboxProps: record => ({
                  disabled: !!deviceInfoList.filter(item => item.guid === record.guid).length,
                  // name: record.name,
                }),
              }}
              pagination={{
                size: 'small',
                total: total,
                current: pageNum,
                pageSize: pageSize,
                showSizeChanger: true,
                onChange: this.onChangePage,
              }}
            />
          </GutterWrapper>
        </Modal>
      </GutterWrapper>
    );
  }
}

const mapStateToProps = ({
  ticket: {
    patrol: {
      new: { roomInfoList, deviceInfoList, checkItem },
    },
  },
}) => {
  return {
    // fieldValues,
    roomInfoList,
    deviceInfoList,
    checkItem,
  };
};

const mapDispatchToProps = {
  setCreatePatrolDevices: ticketActions.setCreatePatrolDevices,
};

const DeviceConnect = connect(mapStateToProps, mapDispatchToProps, null, { forwardRef: true })(
  Form.create()(Device)
);

export default React.forwardRef((props, ref) => <DeviceConnect xRef={ref} {...props} />);
