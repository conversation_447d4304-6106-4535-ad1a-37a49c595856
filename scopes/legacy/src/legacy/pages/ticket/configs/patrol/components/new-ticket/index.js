import Form from '@ant-design/compatible/es/form';
import { ApiSelect, Select } from '@galiojs/awesome-antd';
import React from 'react';
import { connect } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Radio } from '@manyun/base-ui.ui.radio';
import { SlaUnit } from '@manyun/ticket.model.task';
import { SlaSelect } from '@manyun/ticket.ui.sla-select';

import {
  FooterTool<PERSON><PERSON>,
  GutterWrapper,
  LocationCascader,
  TinyCard,
} from '@manyun/dc-brain.legacy.components';
import InspectionTable from '@manyun/dc-brain.legacy.components/inspection-table';
import { getTableData } from '@manyun/dc-brain.legacy.components/inspection-table/utils';
import { INSPECTION_TYPE_KEY_MAP } from '@manyun/dc-brain.legacy.constants/inspection';
import { StyledMcUpload } from '@manyun/dc-brain.legacy.pages/ticket/configs/access-door/styled';
import { getFileInfoList } from '@manyun/dc-brain.legacy.pages/ticket/configs/access-door/utils';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import { ticketActions } from '@manyun/dc-brain.legacy.redux/actions/ticketActions';
import { roomManageService, ticketService } from '@manyun/dc-brain.legacy.services';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';

import Device from './device';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 2 },
    xl: { span: 2 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 6 },

    xl: { span: 12 },
  },
};
const formItemLayout1 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 2 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 22 },
  },
};

export class NewTicket extends React.Component {
  state = {
    roomList: [],
    subBtnLoading: false,
  };
  inspectionTableRef = React.createRef();

  componentDidMount() {
    this.props.syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });
    this.props.syncCommonData({ strategy: { roomTypes: 'IF_NULL' } });
    this.props.syncCommonData({ strategy: { ticketTypes: 'IF_NULL' } });
  }

  onChangeCheckItem = checkItems => {
    const { deviceInfoList, checkItem } = this.props;
    const device = getTableData(checkItem).map(item => item.deviceType);

    const deviceList = deviceInfoList.filter(item => device.includes(item.deviceType));
    this.props.setCreatePatrolRooms([]);
    this.props.setCreatePatrolDevices(deviceList);
    this.props.setCreatePatrolCheckItem(checkItems);
    this.props.form.setFieldsValue({ checkItem: getTableData(checkItems), room: [] });
  };

  onChangeSpecifyRoom = e => {
    if (!e.target.value) {
      this.props.setCreatePatrolRooms([]);
    }
  };

  onChangeRoomList = (value, values) => {
    const { deviceInfoList, form } = this.props;
    const deviceList = deviceInfoList.filter(item => value.includes(item.spaceGuid.roomGuid));
    this.props.setCreatePatrolRooms(values);
    this.props.setCreatePatrolDevices(deviceList);
    form.setFieldsValue({ deviceInfoList: deviceList });
  };

  validateDevice = (_, value, callback) => {
    if (value && value.length) {
      callback();
    } else {
      callback('至少添加一个设备');
    }
  };

  onSubmit = () => {
    if (this.inspectionTableRef.current) {
      this.inspectionTableRef.current.validateFields((errs, value) => {
        if (errs) {
          return;
        }
        this.props.form.validateFields(async (errs, values) => {
          if (errs) {
            return;
          }
          this.setState({ subBtnLoading: true });
          const { roomInfoList, deviceInfoList, checkItem, nomalizedDeviceCategory, roomTypes } =
            this.props;
          const fileInfoList = getFileInfoList(values?.fileInfoList);

          const params = {
            title: values.title,
            idcTag: values.location[0],
            blockGuid: `${values.location[0]}.${values.location[1]}`,
            inspectSubType: values.inspectSubType,
            supportCheckScenes: values.supportCheckScenes,
            roomInfoList: roomInfoList.map(item => {
              return {
                roomTag: item.tag,
                roomGuid: item.guid,
                roomFloor: item.floor,
                roomType: item.roomType,
                blockGuid: `${item.idcTag}.${item.blockTag}`,
                sort: item.sort,
              };
            }),
            deviceInfoList: deviceInfoList.map(item => {
              return {
                deviceGuid: item.guid,
                deviceTag: item.name,
                deviceType: item.deviceType,
                roomGuid: item.spaceGuid.roomGuid,
                sort: item.sort,
              };
            }),
            sla: values.taskSla.sla,
            slaUnit: values.taskSla.unit,
            fileInfoList,
          };
          const inspectItemInfoList = [];
          // const checkItems = deleteNoChildrensKeys(checkItem);
          // console.log('checkItems', checkItems);
          const device = checkItem[INSPECTION_TYPE_KEY_MAP.DEVICE];
          if (device) {
            for (const i in device) {
              inspectItemInfoList.push({
                subTypeCode: i,
                subTypeName: nomalizedDeviceCategory[i]?.metaName,
                inspectSubject: INSPECTION_TYPE_KEY_MAP.DEVICE,
                checkPoints: this.getCheckPoints(device[i]?.POINT),
                checkItems: device[i]?.CUSTOM && this.getCheckItems(device[i]?.CUSTOM),
              });
            }
          }
          const environment = checkItem[INSPECTION_TYPE_KEY_MAP.ENVIRONMENT];
          if (environment) {
            for (const i in environment) {
              inspectItemInfoList.push({
                subTypeCode: i,
                inspectSubject: INSPECTION_TYPE_KEY_MAP.ENVIRONMENT,
                subTypeName: roomTypes[i],
                checkItems: this.getCheckItems(environment[i]?.CUSTOM),
              });
            }
          }
          params.inspectItemInfoList = inspectItemInfoList;
          const { error, response } = await ticketService.patrolInspectOrderCreate(params);
          this.setState({ subBtnLoading: false });

          if (error) {
            message.error(error);
            return;
          }
          setTimeout(() => {
            this.props.history.push(
              urls.generateTicketDetailUrl({ ticketType: this.props.ticketType, id: response })
            );
          }, 1.5 * 1000);
        });
      });
    }
  };

  getCheckPoints = point => {
    let list = [];
    if (Array.isArray(point)) {
      list = point.map(item => {
        let diValueText = '';
        if (typeof item.dataType === 'string' && item.dataType === 'DI') {
          diValueText = item.diValueText;
        }
        if (typeof item.dataType === 'object' && item.dataType?.code === 'DI') {
          diValueText = item?.validLimits?.join(',');
        }
        return {
          deviceType: item.deviceType,
          pointName: item.pointName ? item.pointName : item.name,
          pointCode: item.pointCode,
          dataType: typeof item.dataType === 'string' ? item.dataType : item.dataType?.code,
          isMeterRead: item.isMeterRead,
          metaName: item.metaName,
          unit: item.unit,
          maxValue: item.maxValue,
          minValue: item.minValue,
          diValueText: diValueText,
          checkScenes: item.checkScenes,
          exTextList: item.exTextList,
          normalTextList: item.normalTextList,
        };
      });
    }
    return list;
  };

  getCheckItems = checkItem => {
    if (!checkItem) {
      return [];
    }
    const list = checkItem.map(item => {
      return {
        checkItemName: item.checkItemName?.value,
        checkStdInfoList: item.standards?.map(standardsItem => {
          let checkStd = null;
          if (standardsItem.standardTxt && standardsItem.standardTxt.value) {
            checkStd = standardsItem.standardTxt.value?.trim();
          }
          if (
            checkStd &&
            typeof standardsItem.maxValue === 'number' &&
            typeof standardsItem.minValue === 'number'
          ) {
            checkStd = `${checkStd}【阈值：${standardsItem.minValue}~${standardsItem.maxValue}】`;
          }
          return { ...standardsItem, checkStd };
        }),
      };
    });
    return list;
  };

  validateCheckItem = (_, value, callback) => {
    if (value && value.length) {
      callback();
    } else {
      callback('至少添加一个巡检项');
    }
  };

  onChangeSpecifyDevice = e => {
    if (!e.target.value) {
      this.props.setCreatePatrolDevices([]);
    }
  };

  normalizeDevice = () => {
    const { deviceInfoList } = this.props;
    return deviceInfoList;
  };

  render() {
    const { getFieldDecorator, getFieldsValue, setFieldsValue } = this.props.form;
    const { nomalizedDeviceCategory, ticketTypes, checkItem, history } = this.props;
    const { location, specifyRoom, inspectSubType, supportCheckScenes } = getFieldsValue();
    const { subBtnLoading } = this.state;

    const device = checkItem[INSPECTION_TYPE_KEY_MAP.DEVICE]
      ? Object.keys(checkItem[INSPECTION_TYPE_KEY_MAP.DEVICE])
      : [];
    const roomType = getTableData(checkItem)
      .map(item => {
        if (item.inspectSubject === INSPECTION_TYPE_KEY_MAP.ENVIRONMENT) {
          return item.subTypeCode;
        }
        return false;
      })
      .filter(Boolean);
    const params = {
      idcTag: location?.[0],
      blockTag: location?.[1],
      deviceTypeList: device,
      roomTypeList: roomType,
    };
    return (
      <GutterWrapper mode="vertical">
        <TinyCard title="基本信息">
          <Form colon={false} {...formItemLayout} style={{ width: '960px' }}>
            <Form.Item label="工单标题">
              {getFieldDecorator('title', {
                rules: [
                  { required: true, message: '工单标题为必填！' },
                  {
                    type: 'string',
                    max: 20,
                    message: '最多输入 20 个字符！',
                  },
                ],
              })(<Input allowClear style={{ width: 200 }} />)}
            </Form.Item>
            <Form.Item label="工单类型">
              {getFieldDecorator('inspectSubType', {
                rules: [{ required: true, message: '工单类型为必选！' }],
              })(
                <Select style={{ width: 200 }}>
                  {ticketTypes.map(item => (
                    <Select.Option key={item.taskType} value={item.taskType}>
                      {item.taskValue}
                    </Select.Option>
                  ))}
                </Select>
              )}
            </Form.Item>
            <Form.Item label="位置">
              {getFieldDecorator('location', {
                rules: [
                  {
                    required: true,
                    message: '所属机房/楼栋必选！',
                    type: 'number',
                    transform: value => (value?.length === 2 ? 2 : false),
                  },
                ],
              })(
                <LocationCascader
                  style={{ width: 200 }}
                  currentAuthorize
                  onChange={values => {
                    setFieldsValue({ room: [], deviceInfoList: [] });
                    this.props.setCreatePatrolRooms([]);
                    this.props.setCreatePatrolDevices([]);
                  }}
                />
              )}
            </Form.Item>
            <Form.Item label="SLA">
              {getFieldDecorator('taskSla', { initialValue: { sla: 1, unit: SlaUnit.Hour } })(
                <SlaSelect />
              )}
            </Form.Item>
            <Form.Item label="附件">
              {getFieldDecorator('fileInfoList', {
                valuePropName: 'fileList',
                normalize: value => {
                  if (Array.isArray(value)) {
                    return value;
                  }
                  return value?.fileList || [];
                },
              })(
                <StyledMcUpload
                  accept="image/*,.xls,.xlsx"
                  showUploadList
                  maxFileSize={20}
                  allowDelete
                  showAccept
                >
                  <Button type="primary">上传</Button>
                </StyledMcUpload>
              )}
            </Form.Item>
          </Form>
        </TinyCard>
        <TinyCard bordered={false} style={{ marginBottom: 46 }}>
          <Form colon={false} {...formItemLayout1} labelAlign="left">
            <Form.Item label="是否配置巡检场景" style={{ minWidth: 1580 }}>
              {getFieldDecorator('supportCheckScenes', {
                rules: [{ required: true, message: '是否配置巡检场景为必选项！' }],
                initialValue: false,
              })(
                <Radio.Group
                  options={[
                    { label: '否', value: false },
                    { label: '是', value: true },
                  ]}
                />
              )}
            </Form.Item>
            <Form.Item>
              <InspectionTable
                ref={this.inspectionTableRef}
                taskType={inspectSubType}
                inspectScope="patrol"
                supportCheckScenes={supportCheckScenes}
                onChange={this.onChangeCheckItem}
              />
            </Form.Item>
            <Form.Item>
              {getFieldDecorator('checkItem', {
                rules: [{ validator: this.validateCheckItem }],
              })(<span> </span>)}
            </Form.Item>
            <Form.Item label="是否指定包间">
              {getFieldDecorator('specifyRoom', {
                rules: [{ required: true, message: '是否指定包间必选！' }],
                initialValue: false,
              })(
                <Radio.Group onChange={this.onChangeSpecifyRoom}>
                  <Radio value>是</Radio>
                  <Radio value={false}>否</Radio>
                </Radio.Group>
              )}
            </Form.Item>
            {specifyRoom && (
              <Form.Item
                label="请选择包间"
                labelCol={{ span: 2, offset: 2 }}
                wrapperCol={{ span: 10 }}
              >
                {getFieldDecorator('room', {
                  rules: [{ required: true, message: '包间必填！' }],
                })(
                  <ApiSelect
                    style={{ width: '100%' }}
                    fieldNames={{ label: 'tag', value: 'guid' }}
                    dataService={async q => {
                      if (!q.blockTag || !q.idcTag) {
                        return Promise.reject('缺少机房/楼栋信息');
                      }
                      const { response } = await roomManageService.fetchRoomByDeviceType(q);
                      if (response) {
                        return Promise.resolve(
                          response.data.filter(item => item?.operationStatus?.code === 'ON')
                        );
                      } else {
                        return Promise.resolve([]);
                      }
                    }}
                    disabled={!(location && location.length === 2)}
                    mode="multiple"
                    serviceQueries={[params]}
                    optionWithValue
                    onChange={this.onChangeRoomList}
                  />
                )}
              </Form.Item>
            )}
            <Form.Item label="是否指定设备">
              {getFieldDecorator('specifyDevice', {
                rules: [{ required: true, message: '是否指定设备必选！' }],
                initialValue: false,
              })(
                <Radio.Group disabled={!device.length} onChange={this.onChangeSpecifyDevice}>
                  <Radio value>是</Radio>
                  <Radio value={false}>否</Radio>
                </Radio.Group>
              )}
            </Form.Item>
            {getFieldsValue().specifyDevice && (
              <Form.Item wrapperCol={{ span: 22, offset: 2 }}>
                {getFieldDecorator('deviceInfoList', {
                  rules: [{ validator: this.validateDevice }],
                })(
                  <Device
                    fieldValues={getFieldsValue()}
                    nomalizedDeviceCategory={nomalizedDeviceCategory}
                  />
                )}
              </Form.Item>
            )}
          </Form>
        </TinyCard>
        <FooterToolBar>
          <GutterWrapper>
            <Button type="primary" loading={subBtnLoading} onClick={this.onSubmit}>
              提交
            </Button>
            <Button loading={subBtnLoading} onClick={() => history.goBack()}>
              取消
            </Button>
          </GutterWrapper>
        </FooterToolBar>
      </GutterWrapper>
    );
  }
}
const connectOpts = { forwardRef: true };

const mapStateToProps = ({
  ticket: {
    patrol: {
      new: { deviceInfoList, roomInfoList, checkItem },
    },
  },
  common: { deviceCategory, roomTypes, ticketTypes },
}) => {
  let types = [];
  ticketTypes &&
    ticketTypes.treeList.forEach(item => {
      if (item.taskType === 'INSPECTION') {
        types = item.children;
      }
    });
  return {
    deviceInfoList,
    roomInfoList,
    checkItem,
    nomalizedDeviceCategory: deviceCategory ? deviceCategory.normalizedList : {},
    roomTypes: roomTypes,
    ticketTypes: types,
  };
};

const mapDispatchToProps = {
  setCreatePatrolRooms: ticketActions.setCreatePatrolRooms,
  setCreatePatrolCheckItem: ticketActions.setCreatePatrolCheckItem,
  setCreatePatrolDevices: ticketActions.setCreatePatrolDevices,
  syncCommonData: syncCommonDataActionCreator,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
  null,
  connectOpts
)(Form.create()(NewTicket));
