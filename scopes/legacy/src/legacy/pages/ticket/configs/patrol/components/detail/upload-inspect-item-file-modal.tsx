/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-2-22
 *
 * @packageDocumentation
 */
import { InboxOutlined } from '@ant-design/icons';
import React, { useState } from 'react';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { BackendMcUploadFile, McUploadFileJSON } from '@manyun/dc-brain.model.mc-upload-file';
import { McUpload } from '@manyun/dc-brain.ui.upload';
import { useUploadInspectItemFile } from '@manyun/ticket.gql.client.tickets';

export type UploadInspectItemFileModalProps = {
  inspectItemId: number;
  fileInfoList?: BackendMcUploadFile[];
  onSearch: () => void;
};

export function UploadInspectItemFileModal({
  inspectItemId,
  fileInfoList,
  onSearch,
}: UploadInspectItemFileModalProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  const [files, setFiles] = useState<McUploadFileJSON[]>(
    (fileInfoList ?? []).map(file => McUploadFile.fromApiObject(file).toJSON())
  );

  const [uploadInspectItemFile] = useUploadInspectItemFile();

  const closeModal = () => {
    setOpen(false);
  };
  return (
    <>
      <Button
        type="link"
        compact
        onClick={() => {
          setOpen(true);
          setFiles((fileInfoList ?? []).map(file => McUploadFile.fromApiObject(file).toJSON()));
        }}
      >
        上传
      </Button>
      <Modal
        destroyOnClose
        title="上传附件"
        open={open}
        footer={[
          <Button
            key="cancel"
            onClick={() => {
              closeModal();
              setFiles([]);
            }}
          >
            取消
          </Button>,
          <Button
            key="save"
            type="primary"
            disabled={
              (!files.length && (!Array.isArray(fileInfoList) || !fileInfoList.length)) ||
              files.some(file => file.status !== 'done')
            }
            loading={loading}
            onClick={async () => {
              setLoading(true);
              const { data } = await uploadInspectItemFile({
                variables: {
                  query: {
                    inspectItemId,
                    fileInfoList: files.map(file => McUploadFile.fromJSON(file)),
                  },
                },
              });
              setLoading(false);
              if (data?.uploadInspectItemFile?.message) {
                message.error(data.uploadInspectItemFile.message);
                return;
              }
              message.success('上传现场照片成功');
              data?.uploadInspectItemFile?.success && closeModal();
              onSearch();
            }}
          >
            提交
          </Button>,
        ]}
        onCancel={() => {
          closeModal();
          setFiles([]);
        }}
      >
        <McUpload
          type="drag"
          fileList={files}
          accept="image/*"
          multiple
          onChange={info => {
            /**上传第十个时提示文案 */
            if (info.fileList.length >= 10) {
              message.error('图片上传数量不能超过9张');
              return;
            }
            setFiles(info.fileList);
          }}
        >
          <Space direction="vertical">
            <p>
              <InboxOutlined style={{ fontSize: 48, color: `var(--${prefixCls}-primary-color)` }} />
            </p>
            <Typography.Text>点击或将文件拖拽到这里上传</Typography.Text>
            <Typography.Text type="secondary">支持扩展名：image/*；图片上限9张</Typography.Text>
          </Space>
        </McUpload>
      </Modal>
    </>
  );
}
