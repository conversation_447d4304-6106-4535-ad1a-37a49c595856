import React, { useState } from 'react';

import { UploadOutlined } from '@ant-design/icons';

import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Typography } from '@manyun/base-ui.ui.typography';

import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { McUploadFileJSON } from '@manyun/dc-brain.model.mc-upload-file';
import { McUpload as Upload } from '@manyun/dc-brain.ui.upload';
import {
  useCreateNonstandardInspectItem,
  useUpdateOrDeleteNonstandardInspectItem,
} from '@manyun/ticket.gql.client.tickets';

export type NonstandardInspectItemModalButtonProps = {
  type: string;
  roomGuid: string;
  taskNo: string;
  nsItemId?: string | null;
  onSearch: () => void;
  data?: {
    subjectName?: string | null;
    exDesc?: string | null;
    fileInfoList?: McUploadFileJSON[] | null;
  };
};

export function NonstandardInspectItemModalButton({
  type,
  roomGuid,
  taskNo,
  nsItemId,
  onSearch,
  data,
}: NonstandardInspectItemModalButtonProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const isCreate = type === 'new';
  const [createNonstandardInspectItem] = useCreateNonstandardInspectItem();
  const [updateNonstandardInspectItem] = useUpdateOrDeleteNonstandardInspectItem();

  const onClose = () => {
    setOpen(false);
  };

  const onConfirm = () => {
    form.validateFields().then(async values => {
      setLoading(true);
      let errorMessage;
      const queryFileInfoList =
        Array.isArray(values.fileInfoList) && values.fileInfoList.length > 0
          ? values.fileInfoList.map((file: McUploadFileJSON) =>
              McUploadFile.fromJSON({
                ...file,
                uploadUser: { id: file.uploadUser.id, name: file.uploadUser.name },
              })
            )
          : undefined;
      if (isCreate) {
        const { data } = await createNonstandardInspectItem({
          variables: {
            query: {
              ...values,
              roomGuid,
              taskNo,
              fileInfoList: queryFileInfoList,
            },
          },
        });
        errorMessage = data?.createNonstandardInspectItem?.message;
      } else {
        const { data } = await updateNonstandardInspectItem({
          variables: {
            query: {
              ...values,
              nsItemId,
              fileInfoList: queryFileInfoList,
            },
          },
        });
        errorMessage = data?.updateOrDeleteNonstandardInspectItem?.message;
      }
      if (errorMessage) {
        message.error(errorMessage);
        return;
      }
      message.success(isCreate ? '创建成功' : '编辑成功');

      setLoading(false);
      setOpen(false);

      onSearch();
    });
  };
  return (
    <>
      <Button
        type={isCreate ? 'primary' : 'link'}
        compact={!isCreate}
        onClick={() => {
          setOpen(true);
          form.setFieldsValue(data);
        }}
      >
        {isCreate ? '添加非标准检查异常项' : '编辑'}
      </Button>
      <Modal
        destroyOnClose
        title={isCreate ? '添加非标准检查异常项' : '编辑非标准检查异常项'}
        open={open}
        okButtonProps={{ loading }}
        okText={isCreate ? '提交' : '保存'}
        onCancel={() => {
          onClose();
          form.resetFields();
        }}
        onOk={onConfirm}
      >
        <Form
          form={form}
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 18 }}
          onValuesChange={changedValues => {
            if (
              Array.isArray(changedValues.fileInfoList) &&
              changedValues.fileInfoList.length >= 10
            ) {
              message.error('图片上传数量不能超过9张');
              form.setFieldValue('fileInfoList', changedValues.fileInfoList.slice(0, 9));
            }
          }}
        >
          <Form.Item
            label="检查对象"
            name="subjectName"
            rules={[
              { required: true, message: '请输入检查对象' },
              { max: 20, message: '最多输入 20 个字符！' },
            ]}
          >
            <Input style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item
            label="检查异常描述"
            name="exDesc"
            rules={[
              { required: true, message: '请输入检查异常描述' },
              { max: 300, message: '最多输入 300 个字符！' },
            ]}
          >
            <Input.TextArea style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item
            label="现场照片"
            name="fileInfoList"
            valuePropName="fileList"
            getValueFromEvent={value => {
              if (typeof value === 'object') {
                return value.fileList;
              }
            }}
          >
            <Upload accept=".png,.jpg,.gif,.mp4" maxFileSize={20} allowDelete showUploadList>
              <Button icon={<UploadOutlined />}>上传</Button>
              <Typography.Text type="secondary">
                支持扩展名：.png,.jpg,.gif,.mp4；图片上限9张
              </Typography.Text>
            </Upload>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
