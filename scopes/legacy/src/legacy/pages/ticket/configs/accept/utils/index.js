import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';
import { getDeviceTypeName } from '@manyun/dc-brain.legacy.utils/ticket';

import { ACCEPT_TYPE_KEY_MAP } from '../constants';

export function getDefaultColumns({ ctx, isTemporary = false }) {
  const defaultColumns = [
    {
      title: '资产ID',
      dataIndex: 'assetNo',
      render: txt => txt || BLANK_PLACEHOLDER,
    },
    {
      title: '三级分类',
      dataIndex: 'deviceType',
      render: text => getDeviceTypeName(text, ctx.props.normalizedList),
    },
    {
      title: '厂商',
      dataIndex: 'vendor',
    },
    {
      title: '型号',
      dataIndex: 'productModel',
    },
    {
      title: '预计到货日期',
      dataIndex: 'arrivalTime',
      dataType: 'date',
    },
    {
      title: '到货数量',
      dataIndex: 'arrivalNum',
    },
    {
      title: '本次拒收数量',
      dataIndex: isTemporary ? 'temporaryRefuseNum' : 'refuseNum',
    },
    {
      title: '本次实收数量',
      dataIndex: isTemporary ? 'temporaryAcceptNum' : 'acceptNum',
    },
  ];
  if (ctx.props.acceptType === ACCEPT_TYPE_KEY_MAP.PURCHASE) {
    defaultColumns.splice(0, 1);
  }
  return defaultColumns;
}
