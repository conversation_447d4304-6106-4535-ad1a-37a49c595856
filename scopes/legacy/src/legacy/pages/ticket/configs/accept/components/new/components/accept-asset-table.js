import React, { Component, useEffect } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';

import { InputNumber } from '@manyun/base-ui.ui.input-number';

import { GutterWrapper, TinyTable } from '@manyun/dc-brain.legacy.components';

import { TEMPORARY_NUM_TYPE_KEY_MAP, TEMPORARY_NUM_TYPE_TEXT_MAP } from '../../../constants';
import { getDefaultColumns } from '../../../utils';

const EditableContext = React.createContext();

const EditableRow = ({ form, index, saveForm, ...props }) => {
  useEffect(() => {
    saveForm && saveForm(form, index);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <EditableContext.Provider value={form}>
      <tr {...props} />
    </EditableContext.Provider>
  );
};

const EditableFormRow = Form.create()(EditableRow);

class EditableCell extends Component {
  save = async (e, dataIndex) => {
    const { record, handleSave } = this.props;
    record[dataIndex] = e;
    await handleSave(record);
  };

  renderCell = form => {
    const { dataIndex, record } = this.props;
    const { getFieldDecorator, getFieldValue, validateFields } = form;
    const anotherDataIndex =
      dataIndex === TEMPORARY_NUM_TYPE_KEY_MAP.TEMPORARY_REFUSE_NUM
        ? TEMPORARY_NUM_TYPE_KEY_MAP.TEMPORARY_ACCEPT_NUM
        : TEMPORARY_NUM_TYPE_KEY_MAP.TEMPORARY_REFUSE_NUM;

    return (
      <Form.Item style={{ margin: 0 }}>
        {getFieldDecorator(dataIndex, {
          rules: [
            {
              required: true,
              message: `${TEMPORARY_NUM_TYPE_TEXT_MAP[dataIndex]}必填`,
            },
            {
              validator: validateTemporaryNum(record, getFieldValue(anotherDataIndex)),
            },
          ],
          initialValue:
            dataIndex === TEMPORARY_NUM_TYPE_KEY_MAP.TEMPORARY_REFUSE_NUM
              ? 0
              : record?.arrivalNum - record?.refuseNum - record?.acceptNum,
        })(
          <InputNumber
            min={0}
            max={record.arrivalNum}
            precision={0}
            style={{ width: 140 }}
            onChange={async value => {
              await this.save(value, dataIndex);
              validateFields([anotherDataIndex]);
            }}
          />
        )}
      </Form.Item>
    );
  };

  render() {
    const { dataIndex, children, handleSave, ...restProps } = this.props;

    return (
      <td {...restProps}>
        {dataIndex === TEMPORARY_NUM_TYPE_KEY_MAP.TEMPORARY_REFUSE_NUM ||
        dataIndex === TEMPORARY_NUM_TYPE_KEY_MAP.TEMPORARY_ACCEPT_NUM ? (
          <EditableContext.Consumer>{form => this.renderCell(form)}</EditableContext.Consumer>
        ) : (
          children
        )}
      </td>
    );
  }
}

class AcceptAssetTable extends Component {
  state = {
    formsMap: new Map(),
    dataSource: this.props.dataSource,
  };

  components = {
    body: {
      row: EditableFormRow,
      cell: EditableCell,
    },
  };

  getColumns = columns =>
    columns.map(col => {
      return {
        ...col,
        onCell: record => ({
          record,
          dataIndex: col.dataIndex,
          handleSave: this.handleSave,
        }),
      };
    });

  handleSave = row => {
    this.setState(
      prevState => {
        const foundIndex = prevState.dataSource.findIndex(item => row.key === item.key);
        const foundRow = prevState.dataSource[foundIndex];
        prevState.dataSource.splice(foundIndex, 1, foundRow);
      },
      () => this.props.setListAandForms({ list: this.state.dataSource })
    );
  };

  saveForm = (form, rowKey) => {
    this.setState(
      prevState => {
        prevState.formsMap.set(rowKey, form);
      },
      () => this.props.setListAandForms({ forms: this.state.formsMap })
    );
  };

  componentDidUpdate(prevProps) {
    const { dataSource } = this.props;
    if (dataSource !== prevProps.dataSource) {
      this.setState({ dataSource });
    }
  }

  render() {
    const { dataSource } = this.state;

    return (
      <GutterWrapper mode="vertical">
        <TinyTable
          align="left"
          style={{ marginBottom: 40 }}
          rowKey="id"
          columns={this.getColumns(getDefaultColumns({ ctx: this, isTemporary: true }))}
          dataSource={dataSource}
          components={this.components}
          onRow={(record, index) => {
            return {
              record,
              index,
              saveForm: this.saveForm,
            };
          }}
          pagination={false}
        />
      </GutterWrapper>
    );
  }
}
const mapStateToProps = ({ common: { deviceCategory } }) => ({
  normalizedList: deviceCategory?.normalizedList,
});

export default connect(mapStateToProps)(Form.create()(AcceptAssetTable));

function validateTemporaryNum(record, anotherValue) {
  return (_, value, callback) => {
    const { arrivalNum, refuseNum, acceptNum } = record;
    const num = anotherValue + value;
    if (num > arrivalNum - refuseNum - acceptNum) {
      callback('不可大于剩余收货数量');
    } else {
      callback();
    }
  };
}
