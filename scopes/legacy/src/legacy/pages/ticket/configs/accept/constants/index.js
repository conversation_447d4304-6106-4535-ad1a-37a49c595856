// 验收类型
export const ACCEPT_TYPE_KEY_MAP = {
  PURCHASE: 'PURCHASE',
  MOVE: 'MOVE',
};

export const ACCEPT_TYPE_TEXT_MAP = {
  [ACCEPT_TYPE_KEY_MAP.PURCHASE]: '采购',
  [ACCEPT_TYPE_KEY_MAP.MOVE]: '调拨',
};

export const ACCEPT_TYPE_OPTIONS = [
  {
    value: ACCEPT_TYPE_KEY_MAP.PURCHASE,
    label: ACCEPT_TYPE_TEXT_MAP[ACCEPT_TYPE_KEY_MAP.PURCHASE],
  },
  {
    value: ACCEPT_TYPE_KEY_MAP.MOVE,
    label: ACCEPT_TYPE_TEXT_MAP[ACCEPT_TYPE_KEY_MAP.MOVE],
  },
];

// 本次数量类型
export const TEMPORARY_NUM_TYPE_KEY_MAP = {
  TEMPORARY_REFUSE_NUM: 'temporaryRefuseNum',
  TEMPORARY_ACCEPT_NUM: 'temporaryAcceptNum',
};

export const TEMPORARY_NUM_TYPE_TEXT_MAP = {
  [TEMPORARY_NUM_TYPE_KEY_MAP.TEMPORARY_REFUSE_NUM]: '本次拒收数量',
  [TEMPORARY_NUM_TYPE_KEY_MAP.TEMPORARY_ACCEPT_NUM]: '本次实收数量',
};
