import React, { Component } from 'react';
import { connect } from 'react-redux';

import { message } from '@manyun/base-ui.ui.message';

import { GutterWrapper, TinyDescriptions, TinyTable } from '@manyun/dc-brain.legacy.components';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import * as taskCenterService from '@manyun/dc-brain.legacy.services/taskCenterService';
import { getTaskProperties } from '@manyun/dc-brain.legacy.utils/ticket';

import { ACCEPT_TYPE_KEY_MAP } from '../../constants';
import { getDefaultColumns } from '../../utils';

class Detail extends Component {
  state = {
    assetList: [],
    pageNum: 1,
    pageSize: 10,
    total: 0,
    loading: false,
  };

  componentDidMount() {
    this.props.syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });
    this.search();
  }

  search = async () => {
    const { taskNo } = this.props;
    const { pageNum, pageSize } = this.state;
    this.setState({ loading: true });
    const { response, error } = await taskCenterService.fetchAcceptDetailAssets({
      pageNum,
      pageSize,
      taskNo,
    });
    if (error) {
      message.error(error);
      return;
    }
    this.setState({
      loading: false,
      assetList: response.data,
      total: response.total,
    });
  };

  setPagination = pagination => {
    this.setState({ ...pagination }, () => this.search);
  };

  getDescriptionItem = () => {
    const { basicInfo } = this.props;
    const properties = getTaskProperties(basicInfo);
    const items = [
      {
        label: '关联单号',
        value: `${basicInfo.relateTaskNo}(${
          basicInfo.taskSubType === ACCEPT_TYPE_KEY_MAP.PURCHASE ? '采购' : '调拨'
        })`,
      },
    ];
    basicInfo.taskSubType === ACCEPT_TYPE_KEY_MAP.MOVE &&
      items.push({
        label: '调出位置',
        value: properties.sourceBlockGuid,
      });

    return items;
  };

  render() {
    const { assetList, total, pageSize, pageNum, loading } = this.state;

    return (
      <GutterWrapper mode="vertical">
        <TinyTable
          rowKey="id"
          align="left"
          columns={getDefaultColumns({ ctx: this, isTemporary: false })}
          dataSource={assetList}
          loading={loading}
          actions={[
            <TinyDescriptions
              key="description"
              column={4}
              descriptionsItems={this.getDescriptionItem()}
            />,
          ]}
          pagination={{
            total,
            current: pageNum,
            pageSize,
            onChange: (current, size) => this.setPagination({ pageNum: current, pageSize: size }),
          }}
        />
      </GutterWrapper>
    );
  }
}

const mapStateToProps = ({ common: { deviceCategory }, ticket: { ticketView } }) => ({
  normalizedList: deviceCategory?.normalizedList,
  basicInfo: ticketView?.basicInfo,
  acceptType: ticketView?.basicInfo.taskSubType,
});

const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(Detail);
