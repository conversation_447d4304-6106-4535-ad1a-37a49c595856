import Form from '@ant-design/compatible/es/form';
import debounce from 'lodash/debounce';
import get from 'lodash/get';
import omit from 'lodash/omit';
import trim from 'lodash/trim';
import React, { Component } from 'react';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Select } from '@manyun/base-ui.ui.select';
import { LocationCascader as NewLocationCascader } from '@manyun/resource-hub.ui.location-cascader';

import {
  FooterToolBar,
  GutterWrapper,
  LocationCascader,
  TinyCard,
} from '@manyun/dc-brain.legacy.components';
import { StyledMcUpload } from '@manyun/dc-brain.legacy.pages/ticket/configs/access-door/styled';
import { getFileInfoList } from '@manyun/dc-brain.legacy.pages/ticket/configs/access-door/utils';
import TicketSubType from '@manyun/dc-brain.legacy.pages/ticket/views/tickets/components/filters/ticket-sub-type';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import { redirectActionCreator } from '@manyun/dc-brain.legacy.redux/actions/routerActions';
import * as arrivalAssetService from '@manyun/dc-brain.legacy.services/arrivalAssetService';
import * as taskCenterService from '@manyun/dc-brain.legacy.services/taskCenterService';
import { getLocationSearchMap } from '@manyun/dc-brain.legacy.utils';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';

import { ACCEPT_TYPE_KEY_MAP, ACCEPT_TYPE_TEXT_MAP } from '../../constants';
import AcceptAssetTable from './components/accept-asset-table';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};

export class NewTicket extends Component {
  state = {
    loading: false,
    acceptAssetCreateInfoList: [],
    formsMap: {},
    ids: [],
    initAcceptType: '',
    relateNo: '',
    exist: null,
    init: true,
  };

  componentDidMount() {
    const {
      location: { search },
      form,
      syncCommonData,
    } = this.props;
    syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });
    const { variables } = getLocationSearchMap(search, ['variables']);
    if (!variables) {
      return;
    }
    try {
      const { relateNo, acceptType, ids = [] } = JSON.parse(variables);
      this.setState(
        { ids, initAcceptType: acceptType, relateNo },
        () => relateNo && this.validateRelateNo()
      );
      form.setFieldsValue({ acceptType });
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log(error);
    }
  }

  setListAandForms = ({ list, forms }) => {
    list && this.setState({ acceptAssetCreateInfoList: list });
    forms && this.setState({ formsMap: forms });
  };

  validateRelateNo = debounce(async () => {
    const { getFieldsValue } = this.props.form;
    const { relateNo } = this.state;
    const { acceptType } = getFieldsValue();
    if (!relateNo) {
      this.setState({ acceptAssetCreateInfoList: [] });
      return;
    }
    const { response, error } = await arrivalAssetService.validateRelateNo({
      batchNo: relateNo,
      sourceType: acceptType,
    });
    if (error) {
      message.error(error);
      return;
    }
    this.setState({ exist: response });
    if (response) {
      this.fetchArrivalAssets();
    } else {
      this.setState({ acceptAssetCreateInfoList: [] });
    }
  }, 800);

  fetchArrivalAssets = async () => {
    const { getFieldsValue, setFieldsValue } = this.props.form;
    const { ids, initAcceptType, relateNo } = this.state;
    const { acceptType } = getFieldsValue();
    const { response, error } = await taskCenterService.fetchArrivalAssets({
      sourceType: acceptType,
      batchNo: relateNo,
    });
    if (error) {
      message.error(error);
      return;
    }
    let list = response.data.map(asset => {
      return { ...asset, arrivalTime: asset.planArrivalTime };
    });
    list =
      initAcceptType === acceptType && ids.length
        ? list.filter(asset => ids.includes(asset.id))
        : list;
    this.setState({ acceptAssetCreateInfoList: list });
    const blockGuid = get(response, ['data', '0', 'targetBlockGuid']);
    setFieldsValue({
      location: blockGuid ? blockGuid.split('.') : null,
      title: blockGuid ? `${blockGuid}${ACCEPT_TYPE_TEXT_MAP[acceptType]}到货` : null,
    });
  };

  validationProps = () => {
    const { relateNo, exist, init } = this.state;
    let validationProps = { validateStatus: 'success', help: '' };
    if (init) {
      return validationProps;
    } else if (!relateNo) {
      validationProps.validateStatus = 'error';
      validationProps.help = '关联单号为必填！';
    } else if (typeof exist === 'boolean' && !exist) {
      validationProps.validateStatus = 'error';
      validationProps.help = '请输入正确单号！';
    }
    if (relateNo?.length > 25) {
      validationProps.validateStatus = 'error';
      validationProps.help = '最多输入 25 个字符！';
    }
    return validationProps;
  };

  render() {
    const { form, history, ticketType } = this.props;
    const { loading, acceptAssetCreateInfoList, formsMap, relateNo, exist } = this.state;
    const { getFieldDecorator, validateFields, setFieldsValue, getFieldValue } = form;
    const acceptType = getFieldValue('acceptType');
    const autoCreateInWarehouse = getFieldValue('autoCreateInWarehouse');
    const location = getFieldValue('location');
    return (
      <GutterWrapper mode="vertical">
        <TinyCard title="基本信息">
          <Form colon={false} {...formItemLayout} style={{ width: '620px' }}>
            <Form.Item label="验收类型">
              {getFieldDecorator('acceptType', {
                rules: [{ required: true, message: '验收类型为必选！' }],
                initialValue: ACCEPT_TYPE_KEY_MAP.MOVE,
              })(
                <TicketSubType
                  allowClear={false}
                  ticketType={ticketType}
                  style={{ width: 200 }}
                  onChange={() => {
                    setFieldsValue({
                      title: null,
                      location: null,
                    });
                    this.setState({
                      acceptAssetCreateInfoList: [],
                      relateNo: null,
                      exist: null,
                      init: true,
                    });
                  }}
                />
              )}
            </Form.Item>
            <Form.Item required label="关联单号" {...this.validationProps()}>
              <Input
                value={relateNo}
                allowClear
                style={{ width: 360 }}
                placeholder={
                  acceptType === ACCEPT_TYPE_KEY_MAP.PURCHASE
                    ? '请输入到货批次号'
                    : '请输入调拨编号'
                }
                onChange={({ target: { value } }) => {
                  setFieldsValue({ location: null, title: null });
                  this.setState({ relateNo: trim(value), init: false }, () =>
                    this.validateRelateNo()
                  );
                  form.setFieldsValue({ autoCreateInWarehouse: false });
                }}
              />
            </Form.Item>
            {acceptAssetCreateInfoList.length >= 1 && (
              <AutoCreateInWarehouse
                getFieldDecorator={getFieldDecorator}
                setFieldsValue={setFieldsValue}
                acceptType={acceptType}
                acceptAssetCreateInfoList={acceptAssetCreateInfoList}
              />
            )}

            <Form.Item label="位置">
              {getFieldDecorator('location', {
                rules: [{ required: true, message: '位置为必选！' }],
              })(
                <LocationCascader
                  allowClear={false}
                  changeOnSelect={false}
                  currentAuthorize
                  disabled={!exist}
                  style={{ width: 200 }}
                  onChange={location =>
                    location.length &&
                    setFieldsValue({
                      title: `${location.join('.')}${ACCEPT_TYPE_TEXT_MAP[acceptType]}到货`,
                    })
                  }
                />
              )}
            </Form.Item>

            {autoCreateInWarehouse && location?.[0] && (
              <Form.Item label="入仓库至">
                {getFieldDecorator('targetRoomGuid', {
                  rules: [{ required: true, message: '入仓库为必选！' }],
                })(
                  <NewLocationCascader
                    style={{ width: 200 }}
                    dropdownMenuColumnStyle={{ minWidth: '200px' }}
                    idc={location?.[0]}
                    blocks={location?.[1] && [location?.[1]]}
                    nodeTypes={['ROOM']}
                    onlyEnableBlock
                    onlyEnableRoom
                    authorizedOnly
                    roomTypes={['WAREHOUSE']}
                  />
                )}
              </Form.Item>
            )}
            <Form.Item label="工单标题">
              {getFieldDecorator('title', {
                rules: [
                  { required: true, message: '工单标题为必填！' },
                  {
                    type: 'string',
                    max: 20,
                    message: '最多输入 20 个字符！',
                  },
                ],
              })(<Input allowClear style={{ width: 360 }} />)}
            </Form.Item>
            <Form.Item label="附件">
              {getFieldDecorator('fileInfoList', {
                valuePropName: 'fileList',
                normalize: value => {
                  if (Array.isArray(value)) {
                    return value;
                  }
                  return value?.fileList || [];
                },
              })(
                <StyledMcUpload
                  accept="image/*,.xls,.xlsx"
                  showUploadList
                  maxFileSize={20}
                  allowDelete
                  showAccept
                >
                  <Button type="primary">上传</Button>
                </StyledMcUpload>
              )}
            </Form.Item>
          </Form>
        </TinyCard>
        <TinyCard title="到货信息" style={{ marginBottom: '55px' }}>
          <AcceptAssetTable
            dataSource={acceptAssetCreateInfoList}
            acceptType={acceptType}
            setListAandForms={this.setListAandForms}
          />
        </TinyCard>
        <FooterToolBar>
          <GutterWrapper>
            <Button
              loading={loading}
              type="primary"
              onClick={() => {
                this.setState({ init: false });
                validateFields(async (error, valueMap) => {
                  if (error) {
                    return;
                  }
                  if (!acceptAssetCreateInfoList.length) {
                    message.error('请添加到货信息！');
                    return;
                  }
                  const list = [...acceptAssetCreateInfoList];
                  let validateErr = true;
                  try {
                    for (const [key, form] of formsMap) {
                      validateErr = true;
                      await form.validateFieldsAndScroll(
                        { scroll: { offsetTop: 100, offsetBottom: 100 } },
                        (err, values) => {
                          if (!err) {
                            list[key] = omit(
                              {
                                ...list[key],
                                refuseNum: values.temporaryRefuseNum,
                                acceptNum: values.temporaryAcceptNum,
                              },
                              ['temporaryRefuseNum', 'temporaryAcceptNum']
                            );
                          }
                        }
                      );
                      validateErr = false;
                    }
                  } catch (error) {}
                  if (validateErr) {
                    return;
                  }
                  this.setState({ loading: true });
                  console.log(getQ(list, valueMap, relateNo), 1111222);
                  const id = await createTicket(getQ(list, valueMap, relateNo));
                  if (id) {
                    setTimeout(() => {
                      this.props.redirect(
                        urls.generateTicketDetailLocation({
                          ticketType: ticketType,
                          id: id,
                        })
                      );
                    }, 1.5 * 1000);
                  }
                  this.setState({ loading: false });
                });
              }}
            >
              提交
            </Button>
            <Button loading={loading} onClick={() => history.goBack()}>
              取消
            </Button>
          </GutterWrapper>
        </FooterToolBar>
      </GutterWrapper>
    );
  }
}
function AutoCreateInWarehouse({
  getFieldDecorator,
  setFieldsValue,
  acceptType,
  acceptAssetCreateInfoList,
}) {
  React.useEffect(() => {
    setFieldsValue({ autoCreateInWarehouse: false });
  }, [acceptType]);
  return (
    <>
      {/*  单选有默认选项 隐藏+禁用 还两个选项都禁用  */}
      <Form.Item label="自动创建入库">
        {getFieldDecorator('autoCreateInWarehouse', {
          rules: [{ required: true, message: '自动创建入库为必选！' }],
        })(
          <Radio.Group>
            <Radio
              value={false}
              disabled={
                (acceptType === ACCEPT_TYPE_KEY_MAP.MOVE &&
                  acceptAssetCreateInfoList.some(i => !!i.assetNo)) ||
                (!acceptAssetCreateInfoList.length >= 1 &&
                  acceptType === ACCEPT_TYPE_KEY_MAP.MOVE) ||
                !acceptType
              }
            >
              否
            </Radio>
            <Radio
              value={true}
              disabled={
                (acceptType === ACCEPT_TYPE_KEY_MAP.MOVE &&
                  acceptAssetCreateInfoList.some(i => !!i.assetNo)) ||
                (!acceptAssetCreateInfoList.length >= 1 &&
                  acceptType === ACCEPT_TYPE_KEY_MAP.MOVE) ||
                !acceptType
              }
            >
              是
            </Radio>
          </Radio.Group>
        )}
      </Form.Item>
    </>
  );
}

const mapDispatchToProps = {
  redirect: redirectActionCreator,
  syncCommonData: syncCommonDataActionCreator,
};

export default connect(null, mapDispatchToProps)(Form.create()(withRouter(NewTicket)));

async function createTicket(data) {
  const { response, error } = await taskCenterService.createAcceptTicket(data);
  if (error) {
    message.error(error);
    return;
  }
  message.success('创建成功！');
  return response;
}

function getQ(
  list,
  { acceptType, location, title, fileInfoList, autoCreateInWarehouse, targetRoomGuid },
  relateNo
) {
  const fileList = getFileInfoList(fileInfoList);

  return {
    acceptType,
    relateNo,
    idcTag: location[0],
    blockGuid: location.join('.'),
    title,
    acceptAssetCreateInfoList: list,
    fileInfoList: fileList,
    autoCreateInWarehouse,
    targetRoomGuid: Array.isArray(targetRoomGuid) && targetRoomGuid[0],
  };
}
