import { Input } from '@manyun/base-ui.ui.input';

import { CURRENT_PAGE_TYPES } from '../../constants';
import TTR from '../../registries/ticket-type-registry';
import Detail from './components/detail';
import NewTicket from './components/new';

TTR.registerTicketType('accept')
  .registerConfig({
    type: CURRENT_PAGE_TYPES.TICKETS,
    showNewBtn: true,
    showBatchCloseTicketBtn: false,
    showBatchTakeOverBtn: false,
    showRowSelection: false,
    mergeColumns: ({ DEFAULT_COLUMN_DATA_INDEX_MAP, defaultColumns }) => {
      const newColumns = [...defaultColumns].filter(
        ({ dataIndex }) =>
          dataIndex !== DEFAULT_COLUMN_DATA_INDEX_MAP.TASK_SLA &&
          dataIndex !== DEFAULT_COLUMN_DATA_INDEX_MAP.TASK_RESPOND_TIME
      );
      const insertIndex = newColumns.findIndex(
        ({ dataIndex }) => dataIndex === DEFAULT_COLUMN_DATA_INDEX_MAP.BLOCK_TAG
      );

      newColumns.splice(insertIndex, 0, {
        title: '关联单号',
        dataIndex: 'relateTaskNo',
      });

      return newColumns;
    },
    /**
     * @param {object} param1
     * @param {typeof import('./../../views/tickets/constants').DEFAULT_FILTER_KEY_MAP} param1.DEFAULT_FILTER_KEY_MAP
     * @param {any[]} param1.defaultColumns
     */

    mergeFilters: ({ defaultFilters, DEFAULT_FILTER_KEY_MAP }) => {
      const newFilters = [...defaultFilters].filter(
        ({ key }) => key !== DEFAULT_FILTER_KEY_MAP.END_TIME
      );
      const deleteIdx = newFilters.findIndex(item => item.key === DEFAULT_FILTER_KEY_MAP.IS_DELAY);
      const removeIdx = newFilters.findIndex(
        item => item.key === DEFAULT_FILTER_KEY_MAP.TICKET_STATE
      );
      newFilters.splice(deleteIdx);
      newFilters.splice(removeIdx, 1);
      newFilters.splice(
        newFilters.findIndex(item => item.key === DEFAULT_FILTER_KEY_MAP.LOCATION),
        0,
        {
          label: '关联单号',
          key: 'relateTaskNo',
          Comp: Input,
        }
      );

      return newFilters;
    },
  })
  .registerConfig({
    type: CURRENT_PAGE_TYPES.NEW_TICKET,
    content: NewTicket,
  })
  .registerConfig({
    type: CURRENT_PAGE_TYPES.SPECIFIC_TICKET,
    content: Detail,
  });
