// 子类型
export const TICKET_SUBTYPE_KEY_MAP = {
  CARD_APPLY: 'CARD_APPLY',
  CARD_CHANGE: 'CARD_CHANGE',
  CARD_OFF: 'CARD_OFF',
  CARD_EXCHANGE: 'CARD_EXCHANGE',
};
export const TICKET_SUBTYPE_TEXT_MAP = {
  [TICKET_SUBTYPE_KEY_MAP.CARD_APPLY]: '门禁卡申请',
  [TICKET_SUBTYPE_KEY_MAP.CARD_CHANGE]: '门禁卡变更',
  [TICKET_SUBTYPE_KEY_MAP.CARD_OFF]: '门禁卡注销',
  [TICKET_SUBTYPE_KEY_MAP.CARD_EXCHANGE]: '门禁卡换卡',
};

// 工单状态
export const TICKET_STATUS_KEY_MAP = {
  INIT: '4',
  WAITTAKEOVER: '3',
  FINISH: '1',
  PROCESSING: '2',
  FAILURE: '0',
  UNDO: '5',
};
