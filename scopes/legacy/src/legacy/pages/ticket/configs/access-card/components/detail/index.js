import { generateGetRowSpan } from '@galiojs/awesome-antd/lib/table/utils';
import moment from 'moment';
import React from 'react';
import { connect } from 'react-redux';
import { Redirect } from 'react-router-dom';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { UserLink } from '@manyun/auth-hub.ui.user';
import { Button } from '@manyun/base-ui.ui.button';
import { CERTIFICATE_TYPE_TEXT_MAP } from '@manyun/base-ui.ui.certificate-type-select';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { SpaceText } from '@manyun/resource-hub.ui.space-text';
import { UnboundEntranceGuardCardSelect } from '@manyun/sentry.ui.unbound-entrance-guard-card-select';
import { BackendTaskStatus } from '@manyun/ticket.model.ticket';
import { fetchAccessCard } from '@manyun/ticket.service.fetch-access-card';
import { sendAccessCardNumber } from '@manyun/ticket.service.send-access-card-number';
import { EntranceGuardCardAuthGroupText } from '@manyun/ticket.ui.entrance-guard-card-auth-group-text';
import { EntranceGuardCardCancelledInfo } from '@manyun/ticket.ui.entrance-guard-card-cancelled-info';

import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  ticketPatrolCheckItemActionCreator,
  ticketPatrolRoomInfoActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/ticketActions';

import { EntranceGuardCardLicenseInformation } from '../../../../../../components/entrance-guard-card-license-information/entrance-guard-card-license-information.tsx';
import { TICKET_SUBTYPE_KEY_MAP } from '../../constants/index';

export class AccessCardDetail extends React.Component {
  state = {
    authInfoList: [],
    applyInfo: {},
    accessCardNoEdit: false,
    editAccessCardNoText: '',
    loading: false,
  };

  componentDidMount() {
    this.props.syncCommonData({ strategy: { accessCardTypes: 'IF_NULL' } });
    this.getInfo();
  }

  componentDidUpdate(prevProps) {
    if (
      prevProps.basicInfo.taskNo !== this.props.basicInfo.taskNo ||
      prevProps.basicInfo.taskStatus !== this.props.basicInfo.taskStatus
    ) {
      this.getInfo();
    }
  }

  setEntranceNo = async entranceCardNo => {
    this.setState({ entranceCardNo });
  };

  getInfo = async () => {
    const { data, error } = await fetchAccessCard({
      taskNo: this.props.match.params.id,
    });
    if (error) {
      message.error(error.message);
      return;
    }

    this.props.setProviderValue(data);
    this.setState(data);
  };

  onSave = async records => {
    const defaultInfo = records[0];
    if (!defaultInfo.entranceCardNo) {
      message.error('请先选择门禁卡');
      return;
    }
    this.setState({ loading: true });
    const { error } = await sendAccessCardNumber({
      taskNo: this.props.match.params.id,
      cardType: defaultInfo.cardType,
      cardNo: defaultInfo.cardNo,
      entranceCardNo: defaultInfo.entranceCardNo,
      authInfoList: records.map(({ effectTime, blockGuid }) => {
        const currentTime = effectTime ? effectTime : moment().add(10, 'years');
        return {
          blockGuid,
          effectTime: moment(currentTime).clone().endOf('D').valueOf(),
        };
      }),
    });
    this.setState({ loading: false });
    if (error) {
      message.error(error.message);
      return;
    }
    this.setState(({ applyAuths }) => ({
      applyAuths: applyAuths.map(auth => ({
        ...auth,
        effectTime:
          auth.entranceCardNo === defaultInfo.entranceCardNo
            ? auth.effectTime
              ? auth.effectTime
              : moment().add(10, 'years')
            : auth.effectTime,
        editable: auth.mergeRowsKey === defaultInfo.mergeRowsKey ? false : auth.editable,
      })),
    }));
  };

  // 是否变更了门禁卡类型
  isEntranceTypeChanged = () => {
    const { applyInfo } = this.state;
    if (
      applyInfo.afterEntranceType === applyInfo.beforeEntranceType &&
      applyInfo.afterParentEntranceType === applyInfo.beforeParentEntranceType
    ) {
      return false;
    }
    return true;
  };

  render() {
    const {
      taskSubType,
      description,
      applyAuths,
      idcTag,
      changeAuthInfo,
      deleteAuthInfos,
      entranceCardNo,
      replaceAuthInfos,
      loading,
    } = this.state;
    const { basicInfo } = this.props;
    const apply = taskSubType === TICKET_SUBTYPE_KEY_MAP.CARD_APPLY;
    const change = taskSubType === TICKET_SUBTYPE_KEY_MAP.CARD_CHANGE;
    const off = taskSubType === TICKET_SUBTYPE_KEY_MAP.CARD_OFF;
    const replace = taskSubType === TICKET_SUBTYPE_KEY_MAP.CARD_EXCHANGE;

    return (
      <Space direction="vertical" style={{ display: 'flex', width: '100%' }} size={0}>
        <Descriptions column={2} colon={false}>
          <Descriptions.Item labelStyle={{ width: 132 }} label="说明">
            {description || '--'}
          </Descriptions.Item>
        </Descriptions>
        {apply && (
          <Table
            tableLayout="fixed"
            dataSource={applyAuths}
            scroll={{ x: 'max-content' }}
            columns={columns(
              this,
              this.onSave,
              idcTag,
              basicInfo,
              applyAuths,
              entranceCardNo,
              this.setEntranceNo,
              loading
            )}
            pagination={{
              total: applyAuths.length,
            }}
          />
        )}
        {change && (
          <EntranceGuardCardLicenseInformation
            idcTag={idcTag}
            authInfoList={changeAuthInfo.map(
              ({
                apply,
                effectTime,
                authId,
                changeType,
                oldEffectTime,
                originAuthId,
                ...rest
              }) => ({
                ...apply,
                ...rest,
                blockTag: rest.blockGuid.split('.')[1],
                changeType,
                oldEffectTime,
                originAuthId,
                effectTime: changeType === 'DELETE' ? oldEffectTime : effectTime,
                authId: changeType === 'DELETE' ? originAuthId : authId,
              })
            )}
          />
        )}
        {replace && (
          <EntranceGuardCardLicenseInformation
            idcTag={idcTag}
            authInfoList={replaceAuthInfos.map(({ apply, ...rest }) => ({
              ...rest,
              applyName: apply.label,
              blockTag: rest.blockGuid.split('.')[1],
            }))}
          />
        )}
        {off && (
          <Space direction="vertical" style={{ width: '100%' }}>
            <Typography.Text>注销信息：</Typography.Text>
            <EntranceGuardCardCancelledInfo deleteAuthInfos={deleteAuthInfos} />
          </Space>
        )}
      </Space>
    );
  }
}

const mapStateToProps = ({ common: { accessCardTypes } }) => {
  return {
    accessCardTypes: accessCardTypes?.treeList || [],
  };
};

const mapDispatchToProps = {
  ticketPatrolRoomInfoActionCreator: ticketPatrolRoomInfoActionCreator,
  ticketPatrolCheckItemActionCreator: ticketPatrolCheckItemActionCreator,
  syncCommonData: syncCommonDataActionCreator,
};
export default connect(mapStateToProps, mapDispatchToProps)(AccessCardDetail);

const columns = (
  ctx,
  onSave,
  idcTag,
  basicInfo,
  applyAuths,
  entranceCardNo,
  setEntranceNo,
  loading
) => [
  {
    title: '申请人',
    dataIndex: 'apply',
    render: (text, record) => {
      if (record.apply.id) {
        return <UserLink id={record.apply.id} />;
      }
      return text.label;
    },
    onCell: (record, index) => {
      return {
        rowSpan: generateGetRowSpan(applyAuths)(record, index ?? 1),
      };
    },
  },
  {
    title: '联系电话',
    dataIndex: 'phone',
    onCell: (record, index) => {
      return {
        rowSpan: generateGetRowSpan(applyAuths)(record, index ?? 1),
      };
    },
  },
  {
    title: '电子邮箱',
    dataIndex: 'email',
    onCell: (record, index) => {
      return {
        rowSpan: generateGetRowSpan(applyAuths)(record, index ?? 1),
      };
    },
  },
  {
    title: '证件类型',
    dataIndex: 'cardType',
    render: text => CERTIFICATE_TYPE_TEXT_MAP[text],
    onCell: (record, index) => {
      return {
        rowSpan: generateGetRowSpan(applyAuths)(record, index ?? 1),
      };
    },
  },
  {
    title: '证件编号',
    dataIndex: 'cardNo',
    onCell: (record, index) => {
      return {
        rowSpan: generateGetRowSpan(applyAuths)(record, index ?? 1),
      };
    },
  },
  {
    title: '所属部门',
    dataIndex: 'dept',
    onCell: (record, index) => {
      return {
        rowSpan: generateGetRowSpan(applyAuths)(record, index ?? 1),
      };
    },
  },
  {
    title: '所属公司',
    dataIndex: 'company',
    onCell: (record, index) => {
      return {
        rowSpan: generateGetRowSpan(applyAuths)(record, index ?? 1),
      };
    },
  },
  {
    title: '授权楼栋',
    dataIndex: 'blockGuid',
    render: text => <SpaceText guid={text} />,
  },
  {
    title: '门禁权限组角色',
    dataIndex: 'authId',
    render: (text, { blockGuid }) => (
      <EntranceGuardCardAuthGroupText code={text} blockGuid={blockGuid} />
    ),
  },
  {
    title: '有效期限',
    dataIndex: 'effectTime',
    fixed: 'right',
    width: 240,
    render: (_, { authId, entranceCardNo, effectTime, editable, mergeRowsKey, id, ...rest }) => {
      return editable ? (
        <DatePicker
          defaultValue={effectTime ? moment(effectTime) : moment().add(10, 'years')}
          format="YYYY-MM-DD 23:59:59"
          allowClear={false}
          disabledDate={current => {
            return current && current < moment().subtract(1, 'day').endOf('day');
          }}
          onChange={value => {
            ctx.setState(({ applyAuths }) => ({
              applyAuths: applyAuths.map(auth => ({
                ...auth,
                effectTime: auth.id === id ? value : auth.effectTime,
              })),
            }));
          }}
        />
      ) : effectTime ? (
        moment(effectTime).endOf('D').format('YYYY-MM-DD HH:mm:ss')
      ) : (
        ''
      );
    },
  },
  {
    title: '门禁卡编号',
    dataIndex: 'entranceCardNo',
    width: 240,
    fixed: 'right',
    onCell: (record, index) => {
      return {
        rowSpan: generateGetRowSpan(applyAuths)(record, index ?? 1),
      };
    },
    render: (text, record) => {
      if (record.editable) {
        return (
          <RenderAuthorized userId={basicInfo.taskAssignee}>
            {authorized =>
              authorized &&
              basicInfo.taskStatus === BackendTaskStatus.PROCESSING && (
                <UnboundEntranceGuardCardSelect
                  showSearch
                  idcTag={idcTag}
                  style={{ width: 216 }}
                  defaultValue={{ value: record.entranceCardNo, label: record.entranceCardNo }}
                  onChange={value => {
                    ctx.setState(({ applyAuths }) => ({
                      applyAuths: applyAuths.map(auth => ({
                        ...auth,
                        entranceCardNo:
                          auth.mergeRowsKey === record.mergeRowsKey ? value : auth.entranceCardNo,
                      })),
                    }));
                  }}
                />
              )
            }
          </RenderAuthorized>
        );
      }
      return text;
    },
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 100,
    onCell: (record, index) => {
      return {
        rowSpan: generateGetRowSpan(applyAuths)(record, index ?? 1),
      };
    },
    render: (_, record) => (
      <RenderAuthorized userId={basicInfo.taskAssignee}>
        {authorized => {
          return (
            basicInfo.taskStatus === BackendTaskStatus.PROCESSING &&
            authorized &&
            (record.editable ? (
              <Space>
                <Button
                  type="link"
                  compact
                  loading={loading}
                  onClick={() => {
                    const currentList = applyAuths.filter(
                      auth => auth.mergeRowsKey === record.mergeRowsKey
                    );
                    ctx.onSave(currentList);
                  }}
                >
                  保存
                </Button>
                <Button
                  type="link"
                  compact
                  loading={loading}
                  onClick={() => {
                    ctx.getInfo();
                    ctx.setState(({ applyAuths }) => ({
                      applyAuths: applyAuths.map(auth => ({
                        ...auth,
                        editable: auth.mergeRowsKey === record.mergeRowsKey ? false : auth.editable,
                      })),
                    }));
                  }}
                >
                  取消
                </Button>
              </Space>
            ) : (
              <Button
                type="link"
                onClick={() => {
                  ctx.setState(({ applyAuths }) => ({
                    applyAuths: applyAuths.map(auth => ({
                      ...auth,
                      editable: auth.mergeRowsKey === record.mergeRowsKey || auth.editable,
                    })),
                  }));
                }}
              >
                编辑
              </Button>
            ))
          );
        }}
      </RenderAuthorized>
    ),
  },
];

function RenderAuthorized({ userId, children }) {
  const [, { checkUserId }] = useAuthorized();
  const isCurrentUser = checkUserId(userId);
  if (typeof children == 'function') {
    return children(isCurrentUser);
  }
  return isCurrentUser ? children : <Redirect to={{ pathname: '/403' }} />;
}
