import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';

import template from 'lodash/template';

// import moment from 'moment';
import PrintFile from '@manyun/dc-brain.legacy.components/print-file';

import { TICKET_SUBTYPE_KEY_MAP, TICKET_SUBTYPE_TEXT_MAP } from '../../constants';

function Print({ info, basicInfo, authList }) {
  const [contents, setContents] = useState('');

  useEffect(() => {
    let obj = template();
    if (basicInfo.taskSubType === TICKET_SUBTYPE_KEY_MAP.CARD_APPLY) {
      obj = template(
        `<style>
        table {
          text-align: center ;
          width:700px
        }
        thead > tr {
          height:55px
        }
        .bold{
          font-weight:700
        }
        .device {
          height:35px
        }
        
        </style>
        <table border='1' cellspacing='0' cellpadding='0' align='center'>
          <thead>
              <tr>
                  <th colSpan="6"><%- info.title %></th>
              </tr>
          </thead>
          <tbody>
              <tr>
              <td class='bold'>单号</td>
                  <td><%- info.taskNo %></td>
                  <td class='bold'>申请人</td>
                  <td><%- info.apply %></td>
                  <td class='bold'>身份证</td>
                  <td><%- info.idCard %></td>
              </tr>
              <tr>
              <td class='bold'>手机号</td>
                  <td><%- info.phone %></td>
                  <td class='bold'>邮箱</td>
                  <td><%- info.email %></td>
                  <td class='bold'>公司名称</td>
                  <td><%- info.company %></td>
              </tr>
              <tr>
              <td class='bold'>授权机房</td>
                  <td><%- info.idcTag %></td>
                  <td class='bold'>说明</td>
                  <td><%- info.remark %></td>
                  <td ></td>
                  <td><%-  %></td>
              </tr>
              <tr >
                  <td colSpan="6">门禁授权</td>
              </tr>
              <tr>
                <td colSpan="3">位置</td>
                <td colSpan="3">申请权限</td>
              </tr>
              <% authList.forEach((authItem) => { %><tr> <% if(authItem.rowSpan!==0) { %><td colSpan="3" rowSpan="<%- authItem.rowSpan %>" ><%- authItem.blockGuid %></td> <% }%><td colSpan="3"><%- authItem.auth %></td></tr><% } ); %>
          </tbody>
          <tr style='height:115px;line-heighe:115px'>
              <td colSpan="3">发卡人签字：___________</td>
              <td colSpan="3">申请人签字：___________</td>
          </tr>
        </table>`
      );
    }
    if (basicInfo.taskSubType === TICKET_SUBTYPE_KEY_MAP.CARD_CHANGE) {
      obj = template(
        `<style>
        table {
          text-align: center ;
          width:700px
        }
        thead > tr {
          height:55px
        }
        .bold{
          font-weight:700
        }
        .device {
          height:35px
        }
        
        </style>
        <table border='1' cellspacing='0' cellpadding='0' align='center'>
          <thead>
              <tr>
                  <th colSpan="4"><%- info.title %></th>
              </tr>
          </thead>
          <tbody>
              <tr>
              <td class='bold'>单号</td>
                  <td><%- info.taskNo %></td>
                  <td class='bold'>申请人</td>
                  <td><%- info.apply %></td>
              </tr>
              <tr>
              <td class='bold'>授权机房</td>
                  <td><%- info.idcTag %></td>
                  <td class='bold'>说明</td>
                  <td><%- info.remark %></td>
              </tr>
              <tr >
                  <td colSpan="4">门禁授权</td>
              </tr>
              <tr>
                <td colSpan="2">位置</td>
                <td colSpan="2">申请权限</td>
              </tr>
              <% authList.forEach((authItem) => { %><tr> <% if(authItem.rowSpan!==0) { %><td colSpan="2" rowSpan="<%- authItem.rowSpan %>" ><%- authItem.blockGuid %></td> <% }%><td colSpan="2"><%- authItem.auth %></td></tr><% } ); %>
          </tbody>
          <tr style='height:115px;line-heighe:115px'>
              <td colSpan="2">发卡人签字：___________</td>
              <td colSpan="2">申请人签字：___________</td>
          </tr>
        </table>`
      );
    }
    if (basicInfo.taskSubType === TICKET_SUBTYPE_KEY_MAP.CARD_OFF) {
      obj = template(
        `<style>
        table {
          text-align: center ;
          width:700px
        }
        thead > tr {
          height:55px
        }
        .bold{
          font-weight:700
        }
        .device {
          height:35px
        }
        
        </style>
        <table border='1' cellspacing='0' cellpadding='0' align='center'>
          <thead>
              <tr>
                  <th colSpan="6"><%- info.title %></th>
              </tr>
          </thead>
          <tbody>
              <tr>
              <td class='bold'>单号</td>
                  <td><%- info.taskNo %></td>
                  <td class='bold'>申请人</td>
                  <td><%- info.apply %></td>
                  <td class='bold'>授权机房</td>
                  <td><%- info.idcTag %></td>
              </tr>
              <tr>
                <td class='bold'>说明</td>
                <td  colSpan="5"><%- info.remark %></td>
              </tr>
          </tbody>
          <tr style='height:115px;line-heighe:115px'>
              <td colSpan="3">发卡人签字：___________</td>
              <td colSpan="3">申请人签字：___________</td>
          </tr>
        </table>`
      );
    }
    const str = obj({
      info,
      authList,
    });
    setContents(str);
  }, [setContents, info, authList, basicInfo]);
  return <PrintFile contents={contents} />;
}

const mapStateToProps = (
  {
    common: { accessCardTypes },
    ticket: {
      ticketView: { basicInfo },
    },
  },
  { providerValue }
) => {
  if (!providerValue) {
    return {
      info: {},
      authList: [],
      basicInfo,
    };
  }
  const { applyInfo, authInfoList } = providerValue;
  const info = {
    title: TICKET_SUBTYPE_TEXT_MAP[basicInfo.taskSubType],
    taskNo: basicInfo.taskNo,
    ...applyInfo,
  };
  const authList = [];
  authInfoList.forEach(accessCardPermissionItem => {
    accessCardPermissionItem.authIdList.forEach((authItem, index) => {
      let text = authItem;
      if (accessCardTypes) {
        accessCardTypes.treeList.forEach(i => {
          i.children.forEach(second => {
            if (second.metaCode === authItem.toString()) {
              text = `${i.metaName}-${second.metaName}`;
            }
          });
        });
      }

      authList.push({
        blockGuid: accessCardPermissionItem.blockGuid,
        auth: text,
        rowSpan: index === 0 ? accessCardPermissionItem.authIdList.length : 0,
      });
    });
  });
  return {
    info,
    authList,
    basicInfo,
  };
};
export default connect(mapStateToProps, null)(Print);
