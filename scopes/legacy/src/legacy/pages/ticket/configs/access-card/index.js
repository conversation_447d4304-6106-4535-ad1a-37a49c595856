import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { BackendTaskStatus } from '@manyun/ticket.model.ticket';
import { AccessCardMutator } from '@manyun/ticket.page.access-card-mutator';
import { TicketStatusSelect } from '@manyun/ticket.ui.ticket-status-select';

import { CURRENT_PAGE_TYPES } from '../../constants';
import TTR from './../../registries/ticket-type-registry';
import Detail from './components/detail';

TTR.registerTicketType('access_card_auth')
  .registerConfig({
    type: CURRENT_PAGE_TYPES.TICKETS,
    showNewBtn: true,
    showResendOperation: true,
    showRevokeOperation: true,
    mergeFilters: ({ DEFAULT_FILTER_KEY_MAP, defaultFilters }) => {
      const newFilters = [...defaultFilters].filter(
        ({ key }) =>
          key !== DEFAULT_FILTER_KEY_MAP.IS_DELAY && key !== DEFAULT_FILTER_KEY_MAP.END_TIME
      );
      newFilters.splice(
        newFilters.findIndex(item => item.key === DEFAULT_FILTER_KEY_MAP.TICKET_STATE),
        1,
        {
          label: '工单状态',
          key: 'taskStatusList',
          initialProps: {
            optionFilter: option => option.value !== BackendTaskStatus.CLOSE_APPROVER,
            allowClear: true,
            mode: 'multiple',
          },
          Comp: TicketStatusSelect,
        }
      );
      newFilters.splice(
        newFilters.findIndex(item => item.key === DEFAULT_FILTER_KEY_MAP.CREATOR_NAME),
        0,
        {
          label: '持卡人',
          key: 'applyFlag',
          Comp: UserSelect,
          initialProps: { allowClear: true, reserveSearchValue: true },
        }
      );
      const insertIdx = newFilters.findIndex(
        ({ key }) => key === DEFAULT_FILTER_KEY_MAP.TASK_SUB_TYPE
      );
      const titleIdx = newFilters.findIndex(
        ({ key }) => key === DEFAULT_FILTER_KEY_MAP.TICKET_TITLE
      );
      newFilters.splice(insertIdx, 0, newFilters[titleIdx]);
      newFilters.splice(titleIdx + 1, 1);
      return newFilters;
    },
    mergeColumns: ({ DEFAULT_COLUMN_DATA_INDEX_MAP, defaultColumns }) => {
      const newColumns = [...defaultColumns].filter(
        ({ dataIndex }) =>
          dataIndex !== DEFAULT_COLUMN_DATA_INDEX_MAP.LOCATION &&
          dataIndex !== DEFAULT_COLUMN_DATA_INDEX_MAP.TICKET_TITLE &&
          dataIndex !== DEFAULT_COLUMN_DATA_INDEX_MAP.TASK_RESPOND_TIME
      );
      newColumns.splice(1, 0, {
        title: '工单标题',
        dataIndex: DEFAULT_COLUMN_DATA_INDEX_MAP.TICKET_TITLE,
      });
      return newColumns;
    },
  })
  .registerConfig({
    type: CURRENT_PAGE_TYPES.NEW_TICKET,
    content: AccessCardMutator,
  })
  .registerConfig({
    type: CURRENT_PAGE_TYPES.SPECIFIC_TICKET,
    content: Detail,
    showRevocationBtn: true,
    showReapprovalBtn: true,
  });
