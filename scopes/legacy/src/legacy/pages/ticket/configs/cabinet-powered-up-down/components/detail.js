import Form from '@ant-design/compatible/es/form';
import PageHeader from 'antd/es/page-header';
import uniqBy from 'lodash/uniqBy';
import moment from 'moment';
import { nanoid } from 'nanoid';
import React, { useEffect, useMemo, useState } from 'react';
import { connect, useDispatch, useSelector } from 'react-redux';
import { Link, Redirect } from 'react-router-dom';
import { useDeepCompareEffect } from 'react-use';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { Badge } from '@manyun/base-ui.ui.badge';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Container } from '@manyun/base-ui.ui.container';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Explanation } from '@manyun/base-ui.ui.explanation';
import { SimpleFileList } from '@manyun/base-ui.ui.file-list';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Statistic } from '@manyun/base-ui.ui.statistic';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useClientLogger } from '@manyun/dc-brain.gql.client.client-logs';
import { fetchBizFileInfos } from '@manyun/dc-brain.service.fetch-biz-file-infos';
import { syncCommonDataAction } from '@manyun/dc-brain.state.common';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { usePointsData } from '@manyun/monitoring.hook.use-points-data';
import { FilterAlarms } from '@manyun/monitoring.ui.filter-alarms';
import { PointChartText, PointDataRenderer } from '@manyun/monitoring.ui.point-data-renderer';
import { useLazyRacksOnPowerTicket } from '@manyun/resource-hub.gql.client.racks';
import { generateDeviceRecordRoutePath } from '@manyun/resource-hub.route.resource-routes';
import { RacksWithPduDevicesVirtualTable } from '@manyun/resource-hub.ui.racks-with-pdu-devices-virtual-table';
import {
  useLazyAlarmCountByBiz,
  useStartExecutionPowerGrid,
} from '@manyun/ticket.gql.client.tickets';
import { BackendTaskStatus } from '@manyun/ticket.model.ticket';
import { deletePowerGrid } from '@manyun/ticket.services.delete-power-grid';

import {
  DisplayCard,
  GutterWrapper,
  StatusText,
  TicketCheckListModalButton,
  UserLink,
} from '@manyun/dc-brain.legacy.components';
import { STATUS_MAP } from '@manyun/dc-brain.legacy.constants/statusColor';
import { POWER_UP_DOWN_DETAIL_RECENTLY_CLICK_ROOM } from '@manyun/dc-brain.legacy.constants/ticket';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  powerGridCountActionCreator,
  powerGridListActionCreator,
  ticketActions,
} from '@manyun/dc-brain.legacy.redux/actions/ticketActions';
import { ticketService } from '@manyun/dc-brain.legacy.services';
import { generateGetDeviceMonitoringData } from '@manyun/dc-brain.legacy.utils/device';
import {
  getDisplayCardColor,
  setRecentlyClickCardCode,
} from '@manyun/dc-brain.legacy.utils/ticket';

import { FailureReasonType } from '../../../views/components/failure-reason-type';
import { formatTime } from '../../util.ts';
import recoverSvg from './assets/recover.svg';
import totalSvg from './assets/total.svg';
import triggerSvg from './assets/trigger.svg';
import EvidentiaryMaterialUpload from './evidentiary-material-upload';
import OperationChecked from './operation-checked';
import { PowerOnTimeDetail } from './power-on-time-detail';
import { PowerOnTimeEditor } from './power-on-time-editor';
import { ReviseModal } from './revise-modal';

// 这次发起的订阅的唯一值，自己定义
const moduleId = 'get-pdus-and-front-racks-on-ticket';
function isSameDay(executeTime, powerOnTime = Date.now()) {
  if (!executeTime) {
    return false;
  }
  try {
    const date1 = new Date(executeTime);
    const date2 = new Date(powerOnTime);
    return (
      date1.getFullYear() === date2.getFullYear() &&
      date1.getMonth() === date2.getMonth() &&
      date1.getDate() === date2.getDate()
    );
  } catch (error) {
    console.error(error.message);
    return false;
  }
}
function tryParseJSON(jsonString, defaultValue = null) {
  try {
    return JSON.parse(jsonString);
  } catch (error) {
    console.error('Failed to parse JSON string:', error);
    return defaultValue;
  }
}
function getRecordWithTrueId(record) {
  return { ...record, id: Number(record.id.split('__')[0]) };
}
function NewTicketCheckListModalButton({ ctx, record }) {
  const [, { checkCode }] = useAuthorized();
  return (
    <>
      {checkCode('taskcenter_power_grid_start_exe') && (
        <TicketCheckListModalButton
          key="execute"
          title="执行"
          taskType={ctx.props.basicInfo.taskType}
          taskSubType={ctx.props.basicInfo.taskSubType}
          customStep={[
            <OperationChecked
              key={record.id}
              gridInfo={getRecordWithTrueId(record)}
              checkedResult={ctx.state.checkedResult}
              time={ctx.state.time}
              taskSubType={ctx.props.basicInfo.taskSubType}
              showCheckItem={false}
            />,
          ]}
          customFooter={callback => ctx.getSuffixFooter(getRecordWithTrueId(record), callback)}
          showNextStep={ctx.state.checkedResult === 'normal'}
          getCurrentStepAndVisible={async (visible, step, updateModalVisible) => {
            if (ctx.props.showAlarm) {
              ctx.setExecutionLoading(true);
              await ctx.props.startExecutionPowerGrid({
                variables: {
                  query: {
                    taskNo: ctx.props.basicInfo.taskNo,
                    gridGuid: record.gridGuid,
                    pduDeviceGuidList: record.pduDevices.map(item => item.deviceGuid),
                  },
                },
              });
              ctx.setExecutionLoading(false);
              updateModalVisible && updateModalVisible(true);
            }
            ctx.setGuids(visible, step, getRecordWithTrueId(record));
          }}
          beforeValues={ctx.state.beforeValues}
          setBeforeValues={beforeValues => ctx.setState({ beforeValues })}
          afterValues={ctx.state.afterValues}
          setAfterValues={afterValues => ctx.setState({ afterValues })}
          getVisibleFromExternal
          onFilture={callback => ctx.handleVisible(getRecordWithTrueId(record), callback)}
          onFinish={() => ctx.onFinish(getRecordWithTrueId(record))}
        />
      )}
    </>
  );
}
function DeleteConfirm({ taskNo, gridGuid, onSuccess }) {
  return (
    <Popconfirm
      key="delete"
      title={<>确认删除吗？</>}
      okText="确定删除"
      onConfirm={async () => {
        const { error } = await deletePowerGrid({
          taskNo,
          gridGuid,
        });
        if (error) {
          message.error(error.message);
          return;
        }
        message.success('删除成功');
        onSuccess?.();
      }}
    >
      <Button type="link">删除</Button>
    </Popconfirm>
  );
}
const columnsForVitualList = (
  ctx,
  authorized,
  ticketStatus,
  pointData,
  configUtil,
  setPagination
) => {
  const list = [
    {
      title: '机柜',
      dataIndex: 'gridTag',
      render: (text, record) => (
        <Button
          style={{ padding: 0, height: 'auto' }}
          type="link"
          onClick={() =>
            ctx.setState({
              gridInfoVisible: true,
              gridInfo: getRecordWithTrueId(record),
            })
          }
        >
          {text}
        </Button>
      ),
      onCell: record => {
        return {
          rowSpan: record.roomRowSpan,
        };
      },
    },
    {
      title: '对应列头柜',
      dataIndex: 'frontRack',
      render: (_, { frontRack }) => {
        if (!frontRack) {
          return '--';
        }
        return (
          <Link
            target="_blank"
            to={generateDeviceRecordRoutePath({
              guid: frontRack.deviceGuid,
            })}
          >
            {frontRack.deviceName}
          </Link>
        );
      },
      onCell: record => {
        return {
          rowSpan: record.rackRowSpan,
        };
      },
    },
    {
      title: '列头柜状态',
      dataIndex: 'frontRackStatus',
      width: 109.5,
      render: (_, { frontRack }) => {
        if (!frontRack) {
          return '--';
        }
        let _pointData;
        if (frontRack.deviceType === '11401') {
          _pointData = pointData[frontRack.deviceGuid]?.get('7011000');
        } else {
          _pointData = pointData[frontRack.deviceGuid]?.get('2001000');
        }
        if (!_pointData) {
          return '--';
        }

        const status = getOnOffStatus(_pointData.originalValue, ctx.props.basicInfo.taskSubType);
        return _pointData && !_pointData.value.ERROR_OUT_OF_RANGE ? (
          <Tag color={status ? (status === 'normal' ? 'success' : 'error') : undefined}>
            {_pointData.value.NAME}
          </Tag>
        ) : (
          <PointDataRenderer data={_pointData} showUnit={false} />
        );
      },
      onCell: record => {
        return {
          rowSpan: record.rackRowSpan,
        };
      },
    },
    {
      title: '对应支路空开',
      dataIndex: 'pduDevice',
      width: 116,
      render: (_, { pduDevice }) => {
        if (!pduDevice) {
          return '--';
        }
        return (
          <Link
            target="_blank"
            to={generateDeviceRecordRoutePath({
              guid: pduDevice.deviceGuid,
            })}
          >
            {pduDevice.deviceName}
          </Link>
        );
      },
    },
    {
      title: '支路空开状态',
      dataIndex: 'pduDeviceStatus',
      width: 116,
      render: (_, { pduDevice }) => {
        if (!pduDevice) {
          return '--';
        }
        const _pointData = pointData[pduDevice.deviceGuid]?.get(
          configUtil.getPointCode(
            pduDevice.deviceType,
            ConfigUtil.constants.pointCodes.PDU_ON_OFF_STATE
          )
        );

        if (!_pointData) {
          return '--';
        }

        const status = getOnOffStatus(_pointData.originalValue, ctx.props.basicInfo.taskSubType);
        return _pointData && !_pointData.value?.ERROR_OUT_OF_RANGE ? (
          <Tag color={status ? (status === 'normal' ? 'success' : 'error') : undefined}>
            {_pointData.value?.NAME}
          </Tag>
        ) : (
          <PointDataRenderer data={_pointData} showUnit={false} />
        );
      },
    },

    {
      title: '支路空开电流',
      dataIndex: 'pduDeviceCurrent',
      width: 156,
      render: (_, { pduDevice }) => {
        if (!pduDevice) {
          return '---';
        }
        if (pduDevice.deviceType === '11101') {
          if (!pduDevice?.deviceGuid) {
            return '--';
          }
          const _pointDatas = [
            pointData[pduDevice.deviceGuid]?.get('1004000'),
            pointData[pduDevice.deviceGuid]?.get('1005000'),
            pointData[pduDevice.deviceGuid]?.get('1006000'),
          ].filter(item => item);

          return (
            <Space split={<Divider type="vertical" spaceSize="mini" />}>
              {_pointDatas.map(item => (
                <PointChartText
                  key={item?.deviceGuid}
                  pointData={item}
                  spaceGuid={ctx.props.basicInfo.blockTag}
                  dataType="AI"
                  linkTextColorType="primary"
                  showUnit
                />
              ))}
            </Space>
          );
        } else {
          const _pointData = pointData[pduDevice.deviceGuid]?.get(
            getPowerLoopCodeByType(pduDevice.deviceType)
          );

          return (
            <PointChartText
              key={_pointData?.deviceGuid}
              pointData={_pointData}
              spaceGuid={ctx.props.basicInfo.blockTag}
              dataType="AI"
              linkTextColorType="primary"
              showUnit
            />
          );
        }
      },
    },
    {
      title: '支路空开功率',
      dataIndex: 'pduDevicePower',
      width: 156,
      render: (_, { pduDevice }) => {
        if (!pduDevice) {
          return '--';
        }
        if (pduDevice.deviceType === '11101') {
          const pointDatas = [
            pointData[pduDevice.deviceGuid]?.get('1009000'),
            pointData[pduDevice.deviceGuid]?.get('1010000'),
            pointData[pduDevice.deviceGuid]?.get('1011000'),
          ].filter(item => item);

          return (
            <Space split={<Divider type="vertical" spaceSize="mini" />}>
              {pointDatas.map(item => (
                <PointChartText
                  key={item?.deviceGuid}
                  pointData={item}
                  spaceGuid={ctx.props.basicInfo.blockTag}
                  dataType="AI"
                  linkTextColorType="primary"
                  showUnit
                />
              ))}
            </Space>
          );
        } else {
          const _pointData = pointData[pduDevice.deviceGuid]?.get(
            getPowerCodeByType(pduDevice.deviceType)
          );
          return (
            <PointChartText
              key={_pointData?.deviceGuid}
              pointData={_pointData}
              spaceGuid={ctx.props.basicInfo.blockTag}
              dataType="AI"
              linkTextColorType="primary"
              showUnit
            />
          );
        }
      },
    },
    {
      title: '设计功率',
      dataIndex: 'ratedPower',
      render: text => `${text}KW`,
      onCell: record => {
        return {
          rowSpan: record.roomRowSpan,
        };
      },
    },
    {
      title: '机柜类型',
      dataIndex: ['gridType', 'name'],
      onCell: record => {
        return {
          rowSpan: record.roomRowSpan,
        };
      },
    },
    {
      title: (
        <Explanation
          style={{ color: 'inherit', fontSize: 'inherit' }}
          size="large"
          iconType="question"
          tooltip={{ title: '圆点符号表示处理人手动修订过时间' }}
        >
          实际{ctx.props.basicInfo.taskSubType === 'POWER_OFF' ? '下' : '上'}电时间
        </Explanation>
      ),
      dataIndex: 'powerOnTime',
      dataType: 'datetime',
      render: (val, record) => (
        <Space>
          {val ? moment(val).format('YYYY-MM-DD HH:mm:ss') : ''}
          {record.modified && <Badge color="gold" />}
        </Space>
      ),
      onCell: record => {
        return {
          rowSpan: record.roomRowSpan,
        };
      },
    },
    {
      title: '状态',
      dataIndex: 'checkStatus',
      render: text => {
        if (text.code === 'UNDONE') {
          return (
            <StatusText fontSize={14} status={STATUS_MAP.WARNING}>
              {text.name}
            </StatusText>
          );
        }
        if (text.code === 'COMPLETED') {
          return (
            <StatusText fontSize={14} status={STATUS_MAP.NORMAL}>
              {text.name}
            </StatusText>
          );
        }
        if (text.code === 'FAILURE') {
          return (
            <StatusText fontSize={14} status={STATUS_MAP.ALARM}>
              {text.name}
            </StatusText>
          );
        }
      },
      onCell: record => {
        return {
          rowSpan: record.roomRowSpan,
        };
      },
    },
    {
      title: '操作人',
      dataIndex: 'operatorName',
      render: (text, { operatorBy }) => <UserLink userName={text} userId={operatorBy} />,
      onCell: record => {
        return {
          rowSpan: record.roomRowSpan,
        };
      },
    },
    {
      title: '操作',
      dataIndex: 'operation',
      fixed: 'right',
      render: (_, record) => {
        const operations = [];

        if (
          ticketStatus === BackendTaskStatus.PROCESSING &&
          record.checkStatus?.code === 'UNDONE' &&
          record.pduDevices !== null &&
          authorized
        ) {
          operations.push(<NewTicketCheckListModalButton ctx={ctx} record={record} />);
        }
        if (
          ticketStatus === BackendTaskStatus.PROCESSING &&
          record.checkStatus?.code === 'COMPLETED' &&
          authorized
        ) {
          operations.push(
            <PowerOnTimeEditor
              key="editorTime"
              taskNo={record.taskNo}
              gridGuid={record.gridGuid}
              taskSubType={ctx.props.basicInfo.taskSubType}
              expectTime={tryParseJSON(ctx.props.basicInfo.taskProperties)?.expectTime}
              powerOnTime={record.powerOnTime}
              onSuccess={() => ctx.onChangePage()}
            />
          );
        }
        if (record.modified) {
          operations.push(
            <PowerOnTimeDetail
              key="detail"
              taskSubType={ctx.props.basicInfo.taskSubType}
              executeTime={record.executeTime}
              modReason={record.modReason}
              powerOnTime={record.powerOnTime}
            />
          );
        }
        if (
          ticketStatus === BackendTaskStatus.PROCESSING &&
          authorized &&
          ctx.props.basicInfo.taskSubType === 'POWER_TEST_TO_ON'
        ) {
          operations.push(
            <DeleteConfirm
              taskNo={record.taskNo}
              gridGuid={record.gridGuid}
              onSuccess={() => {
                setPagination(preState => ({ ...preState, key: nanoid() }));
              }}
            />
          );
        }
        return operations.length > 0 ? <Space>{operations}</Space> : '--';
      },
      onCell: record => {
        return {
          rowSpan: record.roomRowSpan,
        };
      },
    },
  ];
  return list;
};

export class DetailInfo extends React.Component {
  state = {
    gridList: [],
    visible: false,
    modalStep: 1,
    checkedResult: null, // normal abnormal
    time: null,
    gridInfo: {},
    currentContent: 'room',
    gridTag: '',
    roomPrimaryKey: '',
    pageNum: 1,
    pageSize: 10,
    beforeValues: null,
    afterValues: null,
    updateVisible: false,
    shouldFilteredSearch: true,
    modificationTimeVisible: false,
    executeTime: '',
    powerOnTime: '',
    gridGuid: '',
    executionLoading: false,
  };

  setExecutionLoading(loading) {
    this.setState({ executionLoading: loading });
  }

  /** 获取每个检查项的检查结果 */
  getCheckResultList() {
    const res = [];
    Object.keys(this.state.beforeValues).forEach(key => {
      const value = this.state.beforeValues[key];
      res.push({
        checkItemId: Number(key.split('-')[0]),
        dataValue: value.value?.num,
        checkResult: value.errors ? 'ABNORMAL' : 'NORMAL',
      });
    });
    Object.keys(this.state.afterValues).forEach(key => {
      const value = this.state.afterValues[key];
      res.push({
        checkItemId: Number(key.split('-')[0]),
        dataValue: value.value?.num,
        checkResult: value.errors ? 'ABNORMAL' : 'NORMAL',
      });
    });
    return res;
  }

  componentDidMount() {
    this.props.powerGridCountActionCreator({
      ...this.getParams(),
    });
    this.props.syncCommonData({
      strategy: {
        ticketTypes: 'IF_NULL',
      },
    });
  }

  componentWillUnmount() {
    this.props.setPowerDetailCheckedRoom({
      checkedRoom: '',
      checkedRoomType: '',
    });
  }

  componentDidUpdate(prevProps) {
    // 设置 providerValue 值为 basicInfo ，并去除值为 {} 的情况
    if (this.props.basicInfo === null || Object.keys(this.props.basicInfo).length !== 0) {
      this.props.setProviderValue(this.props.basicInfo);
    }
    if (
      prevProps.basicInfo.idcTag !== this.props.basicInfo.idcTag ||
      prevProps.basicInfo.blockTag !== this.props.basicInfo.blockTag
    ) {
      this.props.powerGridCountActionCreator({
        ...this.getParams(),
      });
    }
  }

  getFileInfoList = async () => {
    const { basicInfo, taskNo } = this.props;
    const { data } = await fetchBizFileInfos({
      targetId: taskNo,
      targetType: basicInfo.taskType,
      extensionType: 'POWER_PROVE_FILE',
    });
    this.setState({
      evidentiaryMaterial: data.data,
    });
  };

  getParams = () => {
    const { basicInfo, taskNo } = this.props;
    const { idcTag = '', blockTag = '' } = basicInfo;
    return {
      idcTag,
      blockTag: blockTag.substring(blockTag.indexOf('.') + 1),
      taskNo,
    };
  };

  addGrid = selectedRow => {
    this.setState(({ gridList }) => ({
      gridList: [...gridList, ...selectedRow.selectedRows],
    }));
  };

  getTime = taskProperties => {
    let time = '--';
    if (!(taskProperties && typeof taskProperties == 'string')) {
      return;
    }
    try {
      time = JSON.parse(taskProperties).expectTime;
    } catch (error) {
      console.error(error);
    }
    return moment(time).format('YYYY-MM-DD HH:mm:ss');
  };

  conversionUnit = (unit, timestamp = 0) => {
    if (!unit) {
      return;
    }
    switch (unit.toLocaleLowerCase()) {
      case 'minutes':
        return `${Math.floor(timestamp / 60)}分钟`;
      case 'hour':
        return `${Math.floor(timestamp / 3600)}小时`;
      case 'day':
        return `${Math.floor(timestamp / 86400)}天`;
      default:
        return;
    }
  };

  getBranchCoolInterval = taskProperties => {
    let value = '';
    const data = JSON.parse(taskProperties);
    if (!(taskProperties && typeof taskProperties == 'string')) {
      return;
    }
    try {
      if (data.branchCoolInterval === 0) {
        return '无限制';
      }
      value = this.conversionUnit(data.branchCoolIntervalUnit, data.branchCoolInterval) ?? '';
    } catch (error) {
      console.error(error);
    }
    return value;
  };

  getGridCoolInterval = taskProperties => {
    let value = '';
    const data = JSON.parse(taskProperties);
    if (!(taskProperties && typeof taskProperties == 'string')) {
      return;
    }
    try {
      if (data.gridCoolInterval === 0) {
        return '无限制';
      }
      value = this.conversionUnit(data.gridCoolIntervalUnit, data.gridCoolInterval) ?? '';
    } catch (error) {
      console.error(error);
    }
    return value;
  };

  setGuids = (visible, step, record) => {
    const deviceGuids = [];

    if (visible) {
      this.setState({ modalStep: step });
      record.pduDevices.forEach(item => {
        const pointMaps = this.props.concretePointCodeMappings[item.deviceType] || {};
        const {
          pdu_current: current,
          'pdu_on-off-state': on_off_state,
          'pdu_power--sum': power_sum,
          pdu_voltage: voltage,
        } = pointMaps;
        deviceGuids.push(
          { pointCode: current, deviceGuid: item.deviceGuid },
          { pointCode: on_off_state, deviceGuid: item.deviceGuid },
          { pointCode: power_sum, deviceGuid: item.deviceGuid },
          { pointCode: voltage, deviceGuid: item.deviceGuid }
        );
      });
      this.props.setPowerDeviceGuids(deviceGuids);
    } else {
      this.props.setPowerDeviceGuids([]);
      this.setState({
        modalStep: 1,
        checkedResult: null,
        time: null,
      });
    }
  };

  getSuffixFooter = (record, callback) => {
    const { checkedResult } = this.state;
    const checked = (
      <Button key="check" type="primary" onClick={() => this.handleChecked(record)}>
        检查
      </Button>
    );
    if (checkedResult === null) {
      return checked;
    }
    if (checkedResult === 'abnormal') {
      return (
        <>
          {checked}
          <Button danger onClick={() => this.handleVisible(record, callback)}>
            失败
          </Button>
        </>
      );
    }
    return null;
  };

  handleFailure = async () => {
    const { taskNo, form } = this.props;
    const { gridInfo, gridTag } = this.state;
    const checkResultList = this.getCheckResultList();
    form.validateFields(async (err, values) => {
      if (err) {
        return;
      }
      const { error } = await ticketService.powerGridComplete({
        taskNo,
        gridGuid: gridInfo.gridGuid,
        powerTime: this.state.time,
        checkStatus: 'FAILURE',
        ...values,
        reasonType: values?.reasonType?.label,
        checkResultList,
      });
      if (error) {
        message.error(error);
        return;
      }
      this.state.callback(false);
      this.handleVisible({});
      this.props.getRacksOnTicket({
        variables: {
          ...this.getParams(),
          roomTag: this.props.checkedRoom,
          gridTag,
          sortModel: 'DETAIL_LIST',
        },
      });
      this.props.powerGridCountActionCreator({
        ...this.getParams(),
      });
    });
  };

  onFinish = gridInfo => {
    const { taskNo } = this.props;
    const { gridTag } = this.state;
    const checkResultList = this.getCheckResultList();
    return new Promise(async resolve => {
      const { error } = await ticketService.powerGridComplete({
        taskNo,
        gridGuid: gridInfo.gridGuid,
        powerTime: this.state.time,
        checkStatus: 'COMPLETED',
        checkResultList,
      });
      if (error) {
        message.error(error);
        return;
      }
      resolve(true);
      message.success('已完成');
      this.props.getRacksOnTicket({
        variables: {
          ...this.getParams(),
          roomTag: this.props.checkedRoom,
          gridTag,
          sortModel: 'DETAIL_LIST',
        },
      });
      this.props.powerGridCountActionCreator({
        ...this.getParams(),
      });
      (async () => {
        const { response, error } = await ticketService.fetchTicketBasicInfo({
          taskNo: taskNo,
        });
        if (!error) {
          const _powerOnTime = Date.now();
          const _executeTime = tryParseJSON(response.taskProperties)?.expectTime;
          this.setState({
            ...this.state,
            powerOnTime: _powerOnTime,
            executeTime: _executeTime,
            gridGuid: gridInfo.gridGuid,
          });
          if (!isSameDay(_executeTime, _powerOnTime)) {
            this.setState({
              ...this.state,
              powerOnTime: _powerOnTime,
              modificationTimeVisible: true,
            });
          }
        }
      })();
    });
  };

  handleVisible = (gridInfo, callback) => {
    this.setState(({ visible }) => ({ visible: !visible, gridInfo, callback }));
  };

  handleChecked = record => {
    const { basicInfo } = this.props;
    let checkedResult = null;
    const onOffState = record.pduDevices
      ? record.pduDevices.map(item => {
          return this.props.getData(
            { deviceGuid: item.deviceGuid, deviceType: item.deviceType },
            { pointType: ConfigUtil.constants.pointCodes.PDU_ON_OFF_STATE }
          ).value;
        })
      : [];

    if (basicInfo.taskSubType === 'POWER_OFF') {
      if (
        record.pduDevices &&
        onOffState.filter(item => Number(item) === 0).length === record.pduDevices.length
      ) {
        checkedResult = 'normal';
      } else {
        checkedResult = 'abnormal';
      }
    }
    if (['POWER_OFF_TO_TEST', 'POWER_OFF_TO_ON'].includes(basicInfo.taskSubType)) {
      if (
        record.pduDevices &&
        onOffState.filter(item => Number(item) === 1).length === record.pduDevices.length
      ) {
        checkedResult = 'normal';
      } else {
        checkedResult = 'abnormal';
      }
    }
    this.setState({
      checkedResult,
      time: checkedResult === 'normal' ? new Date().getTime() : null,
    });
  };

  onChangePage = () => {
    const { gridTag } = this.state;
    this.props.getRacksOnTicket({
      variables: {
        ...this.getParams(),
        roomTag: this.props.checkedRoom,
        gridTag,
        sortModel: 'DETAIL_LIST',
      },
    });
  };

  render() {
    const { getFieldDecorator } = this.props.form;
    const {
      basicInfo,
      gridCount,
      checkedRoom,
      checkedRoomType,
      cabinetMess,
      taskNo,
      evidentiaryMaterial,
      alarmCountData,
      getAlarmCountByBiz,
      showAlarm,
      setEvidentiaryMaterial,
    } = this.props;
    const {
      visible,
      currentContent,
      roomPrimaryKey,
      gridInfoVisible,
      gridInfo,
      pageSize,
      updateVisible,
      executionLoading,
    } = this.state;
    const descriptionsItems = [
      {
        label: '机柜操作间隔冷静期',
        value: <div>{this.getGridCoolInterval(basicInfo.taskProperties)}</div>,
      },
      {
        label: '证明材料',
        value: (
          <RenderAuthorizedEvidentiaryMaterial userId={basicInfo.taskAssignee}>
            {authorized =>
              authorized && basicInfo.taskStatus === BackendTaskStatus.PROCESSING ? (
                <Button type="link" compact onClick={() => this.setState({ updateVisible: true })}>
                  {evidentiaryMaterial.length ? '查看' : '上传'}
                </Button>
              ) : evidentiaryMaterial.length ? (
                <SimpleFileList files={evidentiaryMaterial}>
                  <Button type="link" compact>
                    查看
                  </Button>
                </SimpleFileList>
              ) : (
                <span>--</span>
              )
            }
          </RenderAuthorizedEvidentiaryMaterial>
        ),
      },
      {
        label: '支路操作间隔冷静期',
        value: <div>{this.getBranchCoolInterval(basicInfo.taskProperties)}</div>,
      },
    ];
    if (basicInfo.taskSubType !== 'POWER_TEST_TO_ON' && basicInfo.taskProperties) {
      descriptionsItems.unshift({
        label: `期望${basicInfo.taskSubType === 'POWER_OFF' ? '下' : '上'}电时间`,
        value: this.getTime(basicInfo.taskProperties),
      });
    }

    return (
      <Space style={{ width: '100%' }} direction="vertical" size="0">
        <Descriptions column={4} colon={false}>
          {descriptionsItems.map(item => (
            <Descriptions.Item
              key={item.key}
              span={2}
              label={item.label}
              labelStyle={{ width: 136 }}
            >
              {item.value}
            </Descriptions.Item>
          ))}
        </Descriptions>
        {currentContent === 'room' && (
          <Space style={{ width: '100%' }} direction="vertical" size="middle">
            <Input.Search
              style={{ width: 200 }}
              placeholder="请输入包间号"
              onChange={e => {
                this.setState({ roomPrimaryKey: e.target.value });
              }}
            />
            <div
              style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fill,minmax(244px,1fr))',
                gridRowGap: '58px',
                gridColumnGap: '16px',
              }}
            >
              {gridCount
                .filter(item => (roomPrimaryKey ? item.roomTag.includes(roomPrimaryKey) : true))
                .map(item => (
                  <DisplayCard
                    key={item.roomTag}
                    bodyStyle={{ paddingLeft: 22 }}
                    title={`${item.roomTag} ${item.roomTypeName}`}
                    showDescribe
                    highlighted={checkedRoom === item.roomTag}
                    countList={[
                      {
                        describe: '未完成',
                        number: item.UNDONE,
                        color: 'warning',
                      },
                      {
                        describe: '已完成',
                        number: item.COMPLETED,
                        color: 'success',
                      },
                      {
                        describe: '失败',
                        number: item.FAILURE,
                        color: 'error',
                      },
                    ]}
                    backgroundColor={getDisplayCardColor({
                      exceptionNum: item.FAILURE,
                      waitNum: item.UNDONE,
                      alreadyNum: item.COMPLETED,
                      code: item?.roomTag,
                      taskNo: this.props.taskNo,
                      recentlyCode: POWER_UP_DOWN_DETAIL_RECENTLY_CLICK_ROOM,
                    })}
                    describe={
                      <Space direction="vertical" size={4}>
                        <div>{formatTime(item.executeTime)}</div>
                        <Typography.Text style={{ fontSize: '12px' }} type="secondary">
                          {basicInfo.taskSubType === 'POWER_OFF' ? '下' : '上'}电时长
                        </Typography.Text>
                      </Space>
                    }
                    subDescribe={
                      item.modCount > 0 ? (
                        <Typography.Text>
                          修订实际{item.taskSubType === 'POWER_OFF' ? '下' : '上'}电时间：
                          {item.modCount}
                        </Typography.Text>
                      ) : null
                    }
                    onClick={() => {
                      this.setState({ currentContent: 'detail' });
                      setRecentlyClickCardCode(
                        item.roomTag,
                        this.props.taskNo,
                        POWER_UP_DOWN_DETAIL_RECENTLY_CLICK_ROOM
                      );

                      this.props.getRacksOnTicket({
                        variables: {
                          ...this.getParams(),
                          roomTag: item.roomTag,
                          sortModel: 'DETAIL_LIST',
                        },
                      });
                      this.setState({ pageNum: 1, pageSize: 10, gridTag: '' });
                      this.props.setPowerDetailCheckedRoom({
                        checkedRoom: item.roomTag,
                        checkedRoomType: item.roomTypeName,
                      });
                    }}
                  />
                ))}
            </div>
          </Space>
        )}
        {currentContent === 'detail' && (
          <GutterWrapper style={{ marginTop: 0 }} mode="vertical">
            <GutterWrapper style={{ alignItems: 'center' }} mode="horizontal" flex>
              <PageHeader
                style={{ padding: 0 }}
                title={`${checkedRoom} ${checkedRoomType}`}
                onBack={() => {
                  this.setState({
                    currentContent: 'room',
                  });
                  this.props.powerGridCountActionCreator({
                    ...this.getParams(),
                  });
                }}
              />
              <Input.Search
                style={{ width: 200 }}
                placeholder="请输入机柜编号"
                onChange={e => {
                  this.setState({ gridTag: e.target.value, shouldFilteredSearch: false });
                }}
                onSearch={() => {
                  this.setState({ pageNum: 1, pageSize, shouldFilteredSearch: true });
                }}
              />
            </GutterWrapper>
            <RenderAuthorizedTable userId={basicInfo.taskAssignee} ctx={this}>
              {(authorized, data, loading, pointData, configUtil, pagination, setPagination) => (
                <Space style={{ display: 'flex', width: '100%' }} direction="vertical">
                  <Spin spinning={executionLoading} delay={500}>
                    <RacksWithPduDevicesVirtualTable
                      loading={loading}
                      columns={columnsForVitualList(
                        this,
                        authorized,
                        basicInfo.taskStatus,
                        pointData,
                        configUtil,
                        setPagination
                      )}
                      dataSource={data?.racksOnPowerTicket?.racks ?? []}
                      setPagination={setPagination}
                      pagination={pagination}
                    />
                  </Spin>
                </Space>
              )}
            </RenderAuthorizedTable>
          </GutterWrapper>
        )}

        {visible && (
          <Modal
            visible={visible}
            destroyOnClose
            title="失败原因"
            onOk={this.handleFailure}
            onCancel={() => this.handleVisible({})}
          >
            <Form>
              <Form.Item label="原因类型">
                {getFieldDecorator('reasonType', {
                  rules: [
                    {
                      required: true,
                      message: '原因类型必选！',
                    },
                  ],
                })(<FailureReasonType labelInValue allowClear />)}
              </Form.Item>
              <Form.Item label="失败原因">
                {getFieldDecorator('failReason', {
                  rules: [
                    {
                      required: true,
                      message: '失败原因必填！',
                    },
                    {
                      type: 'string',
                      max: 20,
                      message: '最多输入 20 个字符！',
                    },
                  ],
                })(<Input allowClear />)}
              </Form.Item>
            </Form>
          </Modal>
        )}
        <Modal
          style={{ minWidth: 700 }}
          bodyStyle={{
            maxHeight: 'calc(80vh - 55px)',
            overflowY: 'auto',
          }}
          width="min-content"
          visible={gridInfoVisible}
          destroyOnClose
          title={`${gridInfo.gridTag}机柜(${cabinetMess.powerStatus?.name})`}
          footer={null}
          onOk={() => {
            this.setState({ gridInfo: {}, gridInfoVisible: false });
          }}
          onCancel={() => {
            this.setState({ gridInfo: {}, gridInfoVisible: false });
          }}
        >
          <OperationChecked
            gridInfo={gridInfo}
            showCheckResultAndTime={false}
            taskSubType={this.props.basicInfo.taskSubType}
          />
        </Modal>
        <EvidentiaryMaterialUpload
          updateVisible={updateVisible}
          evidentiaryMaterial={evidentiaryMaterial}
          targetType={basicInfo.taskType}
          targetId={taskNo}
          fileType="POWER_PROVE_FILE"
          onChangeFile={file => {
            setEvidentiaryMaterial(file);
            this.setState({ updateVisible: false });
          }}
          onCancel={() => this.setState({ updateVisible: false })}
        />
        {showAlarm && (alarmCountData?.totalCount || 0) > 0 && (
          <Container style={{ padding: '24px 0 0 0' }}>
            <Space style={{ display: 'flex', height: '100%' }} direction="vertical" size="middle">
              <div style={{ display: 'flex', width: '100%', justifyContent: 'space-between' }}>
                <Typography.Title style={{ marginBottom: 0 }} showBadge level={5}>
                  监控数据
                </Typography.Title>
              </div>
              <Row gutter={16}>
                <Col span={8}>
                  <RenderCard
                    title={
                      <div>
                        <Explanation
                          style={{ color: 'inherit', fontSize: 'inherit' }}
                          size="large"
                          iconType="question"
                          tooltip={{ title: '统计数量包括收敛告警' }}
                        >
                          总告警数
                        </Explanation>
                      </div>
                    }
                    value={alarmCountData?.totalCount}
                    src={totalSvg}
                  />
                </Col>
                <Col span={8}>
                  <RenderCard
                    title={
                      <Explanation
                        style={{ color: 'inherit', fontSize: 'inherit' }}
                        size="large"
                        iconType="question"
                        tooltip={{ title: '统计数量包括收敛告警' }}
                      >
                        恢复告警数
                      </Explanation>
                    }
                    value={alarmCountData?.RECOVER}
                    src={recoverSvg}
                  />
                </Col>
                <Col span={8}>
                  <RenderCard
                    title={
                      <Explanation
                        style={{ color: 'inherit', fontSize: 'inherit' }}
                        size="large"
                        iconType="question"
                        tooltip={{ title: '统计数量包括收敛告警' }}
                      >
                        正在告警数
                      </Explanation>
                    }
                    value={alarmCountData?.TRIGGER}
                    src={triggerSvg}
                  />
                </Col>
              </Row>
              <FilterAlarms
                bizId={taskNo}
                isMerge={false}
                bizType="POWER"
                editColumnsKey="TICKET_POWER_ON_OFF_ALARMS_LIST"
                idc={basicInfo.idcTag}
                showFormItems={[
                  'alarm-types',
                  'alarm-levels',
                  'alarm-states',
                  'alarm-lifecycle-states',
                  'alarms-triggered-at-time-range',
                  'alarms-recovered-at-time-range',
                ]}
                status="REMOVED"
                onSearchCallback={() => {
                  getAlarmCountByBiz();
                }}
              />
            </Space>
          </Container>
        )}
        <ReviseModal
          modificationTimeVisible={this.state.modificationTimeVisible}
          setModificationTimeVisible={() => {
            this.setState({
              ...this.state,
              modificationTimeVisible: false,
            });
          }}
          taskSubType={basicInfo.taskSubType}
          taskNo={taskNo}
          gridGuid={this.state.gridGuid}
          executeTime={this.state.executeTime}
          powerOnTime={this.state.powerOnTime}
          onSuccess={() => this.onChangePage()}
        />
      </Space>
    );
  }
}
const connectOpts = { forwardRef: true };

const mapStateToProps = ({
  ticket: {
    power: {
      detail: { gridCount, gridList, checkedRoom, lodaing, gridListTotal, checkedRoomType },
    },
  },
  common: { ticketTypes },
  config: { configMap },
  'monitoring.subscriptions': { devicesRealtimeData },
  cabinetManage: { cabinetMess },
}) => {
  const { concretePointCodeMappings, pointsDefinitionMap } = configMap.current;

  const getData = generateGetDeviceMonitoringData(
    devicesRealtimeData,
    undefined,
    pointsDefinitionMap,
    concretePointCodeMappings
  );

  return {
    gridCount,
    gridList,
    checkedRoom,
    checkedRoomType,
    ticketTypes: ticketTypes ? ticketTypes.treeList : [],
    lodaing,
    devicesRealtimeData,
    concretePointCodeMappings,
    pointsDefinitionMap,
    gridListTotal,
    getData,
    cabinetMess,
  };
};

const mapDispatchToProps = {
  setCreatePatrolRooms: ticketActions.setCreatePatrolRooms,
  setCreatePatrolCheckItem: ticketActions.setCreatePatrolCheckItem,
  setCreatePatrolDevices: ticketActions.setCreatePatrolDevices,
  syncCommonData: syncCommonDataActionCreator,
  powerGridCountActionCreator: powerGridCountActionCreator,
  setPowerDetailCheckedRoom: ticketActions.setPowerDetailCheckedRoom,
  powerGridListActionCreator: powerGridListActionCreator,
  setPowerDeviceGuids: ticketActions.setPowerDeviceGuids,
};

const DetailForm = connect(
  mapStateToProps,
  mapDispatchToProps,
  null,
  connectOpts
)(Form.create()(DetailInfo));

function RenderAuthorizedTable({ userId, children, ctx }) {
  const [, { checkUserId }] = useAuthorized();
  const isCurrentUser = checkUserId(userId);
  const [pagination, setPagination] = useState({
    key: undefined,
    current: 1,
    pageSize: 10,
    total: 0,
    flatData: [],
  });

  const [getRacksOnTicket, { data, loading }] = useLazyRacksOnPowerTicket({
    onCompleted(data) {
      if (data?.racksOnPowerTicket?.errorPduDeviceGuids?.length > 0) {
        message.error(
          `以下PDU设备配置错误:${(data?.racksOnPowerTicket?.errorPduDeviceGuids).join(',')}`
        );
        return;
      }
      // 更新分页信息，确保total是正确的
      setPagination({
        ...pagination,
        total: data.racksOnPowerTicket.total,
        flatData: data?.racksOnPowerTicket?.racks,
      });
    },
  });
  const dispatch = useDispatch();

  const config = useSelector(selectCurrentConfig);
  const configUtil = useMemo(() => {
    return new ConfigUtil(config);
  }, [config]);

  const targetList = useMemo(() => {
    const _targetList = data?.racksOnPowerTicket?.racks?.reduce((result, item) => {
      if (item.pduDevice) {
        if (item.pduDevice.deviceType === '11101') {
          result.push({
            guid: item.pduDevice.deviceGuid,
            type: item.pduDevice.deviceType,
            hardCodedPoints: [
              //支路空开功率
              {
                pointCode: '1009000',
                dataType: 'AI',
              },
              {
                pointCode: '1010000',
                dataType: 'AI',
              },
              {
                pointCode: '1011000',
                dataType: 'AI',
              },
              //支路空开电流
              {
                pointCode: '1004000',
                dataType: 'AI',
              },
              {
                pointCode: '1005000',
                dataType: 'AI',
              },
              {
                pointCode: '1006000',
                dataType: 'AI',
              },
            ],
          });
        } else {
          const _hardCodedPoints = [
            //支路空开功率
            {
              pointCode: getPowerCodeByType(item.pduDevice.deviceType),
              dataType: 'AI',
            },
            //支路空开电流
            {
              pointCode: getPowerLoopCodeByType(item.pduDevice.deviceType),
              dataType: 'AI',
            },
          ];
          result.push({
            guid: item.pduDevice.deviceGuid,
            type: item.pduDevice.deviceType,
            hardCodedPoints: _hardCodedPoints.filter(item => item.pointCode),
          });
        }
        //支路空开状态
        result.push({
          guid: item.pduDevice.deviceGuid,
          type: item.pduDevice.deviceType,
          hardCodedPoints: [
            {
              pointCode: configUtil.getPointCode(
                item.pduDevice.deviceType,
                ConfigUtil.constants.pointCodes.PDU_ON_OFF_STATE
              ),
              dataType: 'DI',
            },
          ],
        });
      }
      if (item.frontRack) {
        //列头柜状态
        if (item.frontRack.deviceType === '11401') {
          result.push({
            guid: item.frontRack.deviceGuid,
            type: item.frontRack.deviceType,
            hardCodedPoints: [
              {
                pointCode: '7011000',
                dataType: 'DI',
              },
            ],
          });
        } else {
          result.push({
            guid: item.frontRack.deviceGuid,
            type: item.frontRack.deviceType,
            hardCodedPoints: [
              {
                pointCode: '2001000',
                dataType: 'DI',
              },
            ],
          });
        }
      }
      return result;
    }, []);
    return _targetList;
  }, [configUtil, data?.racksOnPowerTicket?.racks]);

  const mergeTargetList = targetList?.reduce((result, curr) => {
    const existing = result.find(item => item.guid === curr.guid);
    const exisIndex = result.findIndex(item => item.guid === curr.guid);
    if (existing) {
      const mergeHardCodedPoints = [...existing.hardCodedPoints, ...curr.hardCodedPoints];
      result[exisIndex] = {
        ...curr,
        hardCodedPoints: uniqBy(mergeHardCodedPoints, item => item?.pointCode),
      };
    } else {
      result.push(curr);
    }
    return result;
  }, []);

  const [pointData] = usePointsData({
    blockGuid: ctx.props.basicInfo.blockTag,
    moduleId: moduleId,
    targets: mergeTargetList,
    fetchModeTime: 10 * 1000,
  });
  useEffect(() => {
    if (ctx.state.shouldFilteredSearch) {
      setPagination({ ...pagination, current: 1 });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [ctx.state.shouldFilteredSearch]);

  useEffect(() => {
    if (ctx.state.shouldFilteredSearch && ctx.getParams()) {
      // 直接使用后端分页
      getRacksOnTicket({
        variables: {
          ...ctx.getParams(),
          roomTag: ctx.props.checkedRoom,
          sortModel: 'DETAIL_LIST',
          gridTag: ctx.state.gridTag,
          pageNum: pagination.current,
          pageSize: pagination.pageSize,
        },
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    ctx.state.gridTag,
    ctx.props.checkedRoom,
    ctx.state.shouldFilteredSearch,
    pagination.current,
    pagination.pageSize,
    pagination.key,
  ]);
  const deviceTypesPointsDefinitionList = Array.from(
    new Set(
      data?.racksOnPowerTicket?.racks?.reduce((pre, cur) => {
        if (cur.pduDevice) {
          pre.push(cur.pduDevice.deviceType);
        }
        if (cur.frontRack) {
          pre.push(cur.frontRack.deviceType);
        }
        return pre;
      }, [])
    )
  );

  useDeepCompareEffect(() => {
    if (deviceTypesPointsDefinitionList.length) {
      dispatch(
        syncCommonDataAction({
          // 同步测点定义类型
          strategy: {
            deviceTypesPointsDefinition: deviceTypesPointsDefinitionList,
          },
        })
      );
    }
  }, [dispatch, deviceTypesPointsDefinitionList]);
  if (typeof children == 'function') {
    return children(isCurrentUser, data, loading, pointData, configUtil, pagination, setPagination);
  }

  return isCurrentUser ? children : <Redirect to={{ pathname: '/403' }} />;
}

function RenderAuthorizedEvidentiaryMaterial({ userId, children }) {
  const [, { checkUserId }] = useAuthorized();
  const isCurrentUser = checkUserId(userId);
  if (typeof children == 'function') {
    return children(isCurrentUser);
  }
  return isCurrentUser ? children : <Redirect to={{ pathname: '/403' }} />;
}

export default function Detail(props) {
  const [getRacksOnTicket] = useLazyRacksOnPowerTicket();
  const [remoteLog] = useClientLogger();
  const [evidentiaryMaterial, setEvidentiaryMaterial] = useState([]);

  const [getAlarmCountByBiz, { data, refetch }] = useLazyAlarmCountByBiz();
  const [startExecutionPowerGrid, { loading }] = useStartExecutionPowerGrid({
    onCompleted: data => {
      if (!data?.startExecutionPowerGrid?.success) {
        message.error(data?.startExecutionPowerGrid?.message);
        return;
      }
      refetch();
    },
  });

  const fetchRelateFiles = async () => {
    const { basicInfo, taskNo } = props;
    const { error, data, config, request } = await fetchBizFileInfos({
      targetId: taskNo,
      targetType: basicInfo.taskType,
      extensionType: 'POWER_PROVE_FILE',
    });
    const { headers } = config;
    if (error) {
      message.error(error.message);
      remoteLog({
        variables: {
          type: 'error',
          message: 'Fetch Powered up down ticket evidentiary material files from Web/DC Base',
          metas: [
            JSON.stringify({
              traceId: headers['X-TRACE-ID'],
              taskType: basicInfo.taskType,
              targetId: taskNo,
              userInfo: {
                role: headers['x-role'],
                roleId: headers['x-role-id'],
              },
              basicInfo: basicInfo,
              requestUrl: request.responseURL,
              requestReferer: window.location.href,
              errorMessage: error.message,
              sendServiceParams: config.data,
            }),
          ],
        },
      });
    }

    setEvidentiaryMaterial(data.data);
  };
  useEffect(() => {
    const { basicInfo, taskNo } = props;

    fetchRelateFiles();
    getAlarmCountByBiz({
      variables: {
        query: {
          bizId: taskNo,
          bizType: 'POWER',
          idcTag: basicInfo.idcTag,
        },
      },
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <DetailForm
      {...props}
      evidentiaryMaterial={evidentiaryMaterial}
      setEvidentiaryMaterial={setEvidentiaryMaterial}
      getRacksOnTicket={getRacksOnTicket}
      startExecutionPowerGrid={startExecutionPowerGrid}
      alarmCountData={data?.alarmCountByBiz?.data}
      showAlarm={!loading}
      getAlarmCountByBiz={refetch}
    />
  );
}

function getPowerLoopCodeByType(deviceType) {
  switch (deviceType) {
    case '11102':
      return '1006000';
    case '11103':
      return '1002000';
    default:
      return '';
  }
}

function getPowerCodeByType(deviceType) {
  switch (deviceType) {
    case '11102':
      return '1007000';
    case '11103':
      return '1004000';
    default:
      return '';
  }
}
function getOnOffStatus(value, taskType) {
  if (taskType === 'POWER_OFF') {
    if (value === 0) {
      return 'normal';
    }
    if (value === 1) {
      return 'alarm';
    }
    return;
  } else {
    if (value === 1) {
      return 'normal';
    }
    if (value === 0) {
      return 'alarm';
    }
    return;
  }
}

function RenderCard({ title, value, src }) {
  return (
    <Card>
      <Space style={{ justifyContent: 'space-between', width: '100%' }}>
        <Statistic className="static-flex" title={title} value={value} />
        <img width={40} height={40} src={src} alt="circle" />
      </Space>
    </Card>
  );
}
