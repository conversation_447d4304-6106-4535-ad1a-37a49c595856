import Form from '@ant-design/compatible/es/form';
import moment from 'moment';
import React from 'react';
import { connect } from 'react-redux';
import styled from 'styled-components';

import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { message } from '@manyun/base-ui.ui.message';
import { findServerRackPDUs } from '@manyun/dcbrain-utils';
import { CheckItemTable } from '@manyun/ticket.page.rack-power-on-or-off';

import { CabinetDescription, StatusText } from '@manyun/dc-brain.legacy.components';
import {
  ticketActions,
  ticketPatrolCheckItemActionCreator,
  ticketPatrolCheckItemPointResultSaveActionCreator,
  ticketPatrolRoomInfoActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/ticketActions';
import { metadataConfigurationService } from '@manyun/dc-brain.legacy.services';

const StyledContentDescriptions = styled(Descriptions)`
  &.manyun-descriptions-bordered {
    .manyun-descriptions-item-label {
      min-width: 126px;
      width: 120px;
      max-width: 120px;
      text-align: center;
    }

    .manyun-descriptions-item-content {
      padding: 8px 16px;
    }
  }
`;

export class OperationChecked extends React.Component {
  state = {
    visible: false,
  };

  handleChangeCurrentContent = () => {
    this.props.changeCurrentContent('room');
  };

  handleChangeCheckableTag = values => {
    this.props.setCheckItemTypes(values);
  };

  getTypeList = async () => {
    let taskType = this.props.form.getFieldValue('taskType')?.key;
    if (taskType === 'EVENT') {
      taskType = 'EVENT_TOP_CATEGORY';
    }
    const { response, error } = await metadataConfigurationService.fetchMetadataByType(taskType);
    if (error) {
      message.error(error);
      return;
    }
    if (response) {
      this.setState({
        typeList: response.data,
      });
    }
  };

  onChangeTaskType = () => {
    this.props.form.setFieldsValue({ handleType: { label: '', key: '' } });
    this.setState({ typeList: [] });
  };

  render() {
    const {
      gridInfo,
      checkedResult,
      time,
      taskSubType,
      showCheckItem = true,
      showCheckResultAndTime = true,
    } = this.props;

    const rppAs = [];
    const rppBs = [];
    // eslint-disable-next-line no-unused-expressions
    gridInfo.frontGrids?.forEach(item => {
      if (item.extendPosition?.endsWith('.A')) {
        rppAs.push(item);
      }
      if (item.extendPosition?.endsWith('.B')) {
        rppBs.push(item);
      }
    });

    const devices = gridInfo.pduDevices?.reduce((acc, item) => {
      if (!acc[item.extendPosition]) {
        acc[item.extendPosition] = [];
      }

      acc[item.extendPosition].push(item);

      return acc;
    }, {});

    const { A: pduAs, B: pduBs } = findServerRackPDUs(gridInfo.gridTag, devices);

    return (
      <>
        <CabinetDescription
          gridInfo={gridInfo}
          taskSubType={taskSubType}
          rppAs={rppAs}
          rppBs={rppBs}
          pduDevicesA={pduAs}
          pduDevicesB={pduBs}
        />
        {taskSubType !== undefined && showCheckItem && (
          <CheckItemTable taskSubType={taskSubType} powerGridId={gridInfo.id} />
        )}
        {showCheckResultAndTime && (
          <StyledContentDescriptions
            style={{ marginTop: -1 }}
            bordered
            layout="horizontal"
            column={1}
          >
            <StyledContentDescriptions.Item label="操作检查">
              {checkedResult === null && '未开始'}
              {checkedResult === 'normal' && (
                <StatusText fontSize={14} status="normal">
                  通过
                </StatusText>
              )}
              {checkedResult === 'abnormal' && (
                <StatusText fontSize={14} status="alarm">
                  未通过
                </StatusText>
              )}
            </StyledContentDescriptions.Item>
            <StyledContentDescriptions.Item
              label={`${taskSubType === 'POWER_OFF' ? '下' : '上'}电通过时间`}
            >
              {time ? moment(time).format('YYYY-MM-DD HH:mm:ss') : '--'}
            </StyledContentDescriptions.Item>
          </StyledContentDescriptions>
        )}
      </>
    );
  }
}

const mapStateToProps = ({
  ticket: {
    power: { detail },
  },
}) => {
  return {
    checkItem: detail.checkItem,
    checkItemTypes: detail.checkItemTypes,
    loading: detail.checkItemLoading,
  };
};

const mapDispatchToProps = {
  ticketPatrolRoomInfoActionCreator: ticketPatrolRoomInfoActionCreator,
  ticketPatrolCheckItemActionCreator: ticketPatrolCheckItemActionCreator,
  setCheckItemTypes: ticketActions.setPatrolCheckItemTypes,
  setPatrolCheckItem: ticketActions.setPatrolCheckItem,
  ticketPatrolCheckItemResultSave: ticketPatrolCheckItemPointResultSaveActionCreator,
  setPowerDeviceGuids: ticketActions.setPowerDeviceGuids,
};

const operationCheckedConnect = connect(
  mapStateToProps,
  mapDispatchToProps
)(Form.create({ name: 'ticket_patrol_exception_modal' })(OperationChecked));

export default styled(operationCheckedConnect)``;
