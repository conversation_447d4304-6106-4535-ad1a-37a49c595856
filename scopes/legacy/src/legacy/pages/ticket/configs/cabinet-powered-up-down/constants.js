// 上下电类型
export const POWER_STATUS_TYPE_KEY_MAP = {
  POWER_OFF: 'POWER_OFF',
  POWER_OFF_TO_TEST: 'POWER_OFF_TO_TEST',
  POWER_OFF_TO_ON: 'POWER_OFF_TO_ON',
  POWER_TEST_TO_ON: 'POWER_TEST_TO_ON',
};
export const POWER_STATUS_TYPE_TEXT_MAP = {
  [POWER_STATUS_TYPE_KEY_MAP.POWER_OFF]: '下电',
  [POWER_STATUS_TYPE_KEY_MAP.POWER_OFF_TO_TEST]: '测试上电',
  [POWER_STATUS_TYPE_KEY_MAP.POWER_OFF_TO_ON]: '正式上电',
  [POWER_STATUS_TYPE_KEY_MAP.POWER_TEST_TO_ON]: '测试转生产',
};
