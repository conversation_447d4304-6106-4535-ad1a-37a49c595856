import { ExportPowerButton } from '@manyun/ticket.ui.export-power-button';
import { TicketStatusSelect } from '@manyun/ticket.ui.ticket-status-select';

import { CURRENT_PAGE_TYPES } from '../../constants';
import TTR from './../../registries/ticket-type-registry';
import Detail from './components/detail';
import NewTicket from './components/new';

TTR.registerTicketType('power')
  .registerConfig({
    type: CURRENT_PAGE_TYPES.TICKETS,
    showNewBtn: true,
    showResendOperation: true,
    showRevokeOperation: true,
    mergeFilters: ({ DEFAULT_FILTER_KEY_MAP, defaultFilters }) => {
      const newFilters = [...defaultFilters].filter(
        ({ key }) => key !== DEFAULT_FILTER_KEY_MAP.END_TIME
      );

      newFilters.splice(
        newFilters.findIndex(item => item.key === DEFAULT_FILTER_KEY_MAP.TICKET_STATE),
        1,
        {
          label: '工单状态',
          key: 'taskStatusList',
          initialProps: {
            allowClear: true,
            mode: 'multiple',
          },
          Comp: TicketStatusSelect,
        }
      );

      return newFilters;
    },
  })
  .registerConfig({
    type: CURRENT_PAGE_TYPES.NEW_TICKET,
    content: NewTicket,
  })
  .registerConfig({
    type: CURRENT_PAGE_TYPES.SPECIFIC_TICKET,
    content: Detail,
    showRevocationBtn: true,
    showReapprovalBtn: true,
    tabBarExtraContent: (_, providerValue) => <ExportPowerButton info={providerValue} />,
  });
