import Form from '@ant-design/compatible/es/form';
import { ApiSelect } from '@galiojs/awesome-antd';
import React, { useMemo, useState } from 'react';
import { useSelector } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { fetchSpecs } from '@manyun/resource-hub.service.fetch-specs';
import { RackTypesSelect } from '@manyun/resource-hub.ui.rack-types-select';
import { getComponent } from '@manyun/resource-hub.ui.spec-form-items';
import { fetchRacksByCustomer } from '@manyun/ticket.service.fetch-racks-by-customer';

import { GutterWrapper, TinyTable } from '@manyun/dc-brain.legacy.components';
import { roomManageService } from '@manyun/dc-brain.legacy.services';

export function AddCabinet({ onAddGrid, addedGridList = [], form, location, taskSubType }) {
  const [visible, setVisible] = useState(false);
  const [tableData, setDataSource] = useState({ list: [], total: 0, pageNum: 1, pageSize: 10 });
  const [selectedRow, setSelectedRow] = useState({ selectedRowKeys: [], selectedRows: [] });
  const [gridAlias, setGridAlias] = useState();
  const { getFieldDecorator } = form;
  const pagination = { pageNum: 1, pageSize: 10 };

  const [configUtil] = useConfigUtil();
  const ticketConfig = configUtil.getScopeCommonConfigs('ticket');
  const showGridAlias = ticketConfig.powerOnOff.features.showGridAlias;
  const deviceType = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_GRID);
  const columns = React.useCallback(
    () => [
      {
        title: showGridAlias ? '机柜编号' : '机柜',
        dataIndex: 'tag',
      },
      {
        title: '机柜别名',
        dataIndex: 'gridAlias',
        render: (val, { specDataList }) => {
          if (specDataList) {
            const _text = specDataList.find(item => item.specCode === 'IT_ALIAS')?.specValue;
            return (
              <Typography.Text style={{ width: 200 }} ellipsis={{ tooltip: _text }}>
                {_text}
              </Typography.Text>
            );
          }
        },
        isHide: !showGridAlias,
      },
      {
        title: '机列',
        dataIndex: 'columnTag',
      },
      {
        title: '包间',
        dataIndex: 'roomTag',
      },
      {
        title: '机柜类型',
        dataIndex: ['gridType', 'name'],
      },
      {
        title: '设计功率',
        dataIndex: 'ratedPower',
        render: ratedPower =>
          ratedPower !== null && ratedPower !== undefined ? `${ratedPower}kW` : '',
      },
      {
        title: '机柜状态',
        dataIndex: 'powerStatus',
        render: powerStatus => (powerStatus ? powerStatus.name : '--'),
      },
    ],
    [showGridAlias]
  );
  const getCabinet = async (pageNum, pageSize) => {
    let powerStatusList = [];
    if (taskSubType === 'POWER_OFF') {
      powerStatusList = ['ON', 'TEST'];
    }
    switch (taskSubType) {
      case 'POWER_OFF':
        powerStatusList = ['ON', 'TEST'];
        break;
      case 'POWER_OFF_TO_TEST':
      case 'POWER_OFF_TO_ON':
        powerStatusList = ['OFF'];
        break;
      case 'POWER_TEST_TO_ON':
        powerStatusList = ['TEST'];
        break;
      default:
        powerStatusList = [];
    }
    const params = {
      pageNum,
      pageSize,
      idcTag: location[0],
      blockTag: location[1],
      powerStatusList,
      ...form.getFieldsValue(),
    };

    if (form.getFieldValue('gridType')) {
      params.gridTypeList = [form.getFieldValue('gridType')];
    }

    const { data, error } = await fetchRacksByCustomer(params);
    if (error) {
      message.error(error);
      return;
    }
    setDataSource({
      list: data.data,
      total: data.total,
      pageNum,
      pageSize,
    });
  };
  const onSelectChange = (selectedRowKeys, selectedRows) => {
    setSelectedRow({ selectedRowKeys, selectedRows });
  };

  const onChangePage = (pageNo, pageSize) => {
    getCabinet(pageNo, pageSize);
  };

  const handleSearch = () => {
    getCabinet(pagination.pageNum, pagination.pageSize);
  };
  React.useEffect(() => {
    if (deviceType && showGridAlias) {
      (async () => {
        const { data, error } = await fetchSpecs({
          deviceType,
        });
        if (error) {
          message.error(error.message);
          return;
        }
        setGridAlias(data.data.find(item => item.code === 'IT_ALIAS'));
      })();
    } else {
      setGridAlias();
    }
  }, [deviceType, showGridAlias]);

  return (
    <GutterWrapper>
      <Button
        type="primary"
        disabled={!location || location.length !== 2 || !taskSubType}
        onClick={() => {
          if (!visible) {
            getCabinet(1, 10);
          }
          setVisible(true);
        }}
      >
        添加机柜
      </Button>
      <Modal
        width="1101px"
        title="添加机柜"
        visible={visible}
        destroyOnClose
        onOk={() => {
          onAddGrid(selectedRow);
          setVisible(false);
          setSelectedRow({ selectedRowKeys: [], selectedRows: [] });
        }}
        onCancel={() => {
          setVisible(false);
          setSelectedRow({ selectedRowKeys: [], selectedRows: [] });
        }}
      >
        <GutterWrapper mode="vertical">
          <Form layout="inline">
            <Form.Item label="所属包间">
              {getFieldDecorator('roomTag')(
                <ApiSelect
                  style={{ width: 200 }}
                  fieldNames={{ label: 'tag', value: 'tag' }}
                  dataService={async () => {
                    const { response } = await roomManageService.fetchRoomPage({
                      pageSize: 200,
                      pageNum: 1,
                      idcTag: location[0],
                      blockTag: location[1],
                    });
                    if (response) {
                      return Promise.resolve(response.data);
                    } else {
                      return Promise.resolve([]);
                    }
                  }}
                  showSearch
                />
              )}
            </Form.Item>
            <Form.Item label="所属机列">
              {getFieldDecorator('columnTag')(<Input style={{ width: 200 }} allowClear />)}
            </Form.Item>
            <Form.Item label="机柜类型">
              {getFieldDecorator('gridType')(
                <RackTypesSelect style={{ width: 200 }} allowClear trigger="onDidMount" />
              )}
            </Form.Item>

            {gridAlias && (
              <Form.Item label="机柜别名">
                {getFieldDecorator('gridAlias')(getComponent(gridAlias))}
              </Form.Item>
            )}

            <Form.Item>
              <GutterWrapper>
                <Button type="primary" onClick={handleSearch}>
                  搜索
                </Button>
                <Button
                  onClick={() => {
                    form.resetFields();
                    getCabinet(pagination.pageNum, pagination.pageSize);
                  }}
                >
                  重置
                </Button>
              </GutterWrapper>
            </Form.Item>
          </Form>

          <TinyTable
            rowKey="guid"
            columns={columns().filter(item => !item.isHide)}
            scroll={{ x: 'max-content' }}
            rowSelection={{
              selectedRowKeys: selectedRow.selectedRowKeys,
              selectedRows: selectedRow.selectedRows,
              onChange: onSelectChange,
              getCheckboxProps: item => ({
                disabled: !!addedGridList.filter(i => i.gridGuid === item.guid).length,
              }),
            }}
            pagination={{
              total: tableData.total,
              current: tableData.pageNum,
              onChange: onChangePage,
              pageSize: tableData.pageSize,
            }}
            dataSource={tableData.list}
          />
        </GutterWrapper>
      </Modal>
    </GutterWrapper>
  );
}

export default Form.create()(AddCabinet);
