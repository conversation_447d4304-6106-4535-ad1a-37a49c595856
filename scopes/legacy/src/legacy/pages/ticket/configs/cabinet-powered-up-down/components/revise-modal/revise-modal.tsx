import moment from 'moment';
import React, { useEffect, useMemo, useState } from 'react';
import styled from 'styled-components';

import { Alert } from '@manyun/base-ui.ui.alert';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { useAuthorized } from '@manyun/iam.hook.use-authorized';
import { updatePowerGridTime } from '@manyun/ticket.service.update-power-grid-time';

const StyledAlert = styled(Alert)`
  margin-bottom: 14px;
`;

function generateRange(start: number, end: number) {
  const result = [];
  for (let i = start; i < end; i++) {
    result.push(i);
  }
  return result;
}
export function ReviseModal({
  taskSubType,
  taskNo,
  gridGuid,
  executeTime,
  powerOnTime,
  modificationTimeVisible,
  setModificationTimeVisible,
  onSuccess,
}: {
  taskSubType: string;
  taskNo: string;
  gridGuid: string;
  executeTime: number;
  powerOnTime: number;
  modificationTimeVisible: boolean;
  setModificationTimeVisible: () => void;
  onSuccess: () => void;
}) {
  const [, { checkCode }] = useAuthorized();
  const [loading, setLoading] = useState(false);

  const [form] = Form.useForm();
  useEffect(() => {
    form.setFieldsValue({ executeTime: moment(executeTime), dateTime: moment(powerOnTime) });
  }, [executeTime, form, powerOnTime]);
  const v = useMemo(() => (taskSubType === 'POWER_OFF' ? '下' : '上'), [taskSubType]);
  return (
    <div>
      <Modal
        width={545}
        open={modificationTimeVisible}
        maskClosable={false}
        title={`编辑实际${v}电时间`}
        destroyOnClose
        okText={<div>提交</div>}
        cancelText={<div>不修改</div>}
        okButtonProps={{
          loading,
        }}
        onOk={async () => {
          if (checkCode('taskcenter_power_grid_start_exe')) {
            form.validateFields().then(async values => {
              setLoading(true);
              const { error } = await updatePowerGridTime({
                taskNo: taskNo,
                gridGuid: gridGuid,
                powerRealTime: moment(values.dateTime).valueOf(),
                modReason: values.reason,
              });
              setLoading(false);
              if (error) {
                message.error(error.message);
                return;
              }
              message.success('操作成功');
              setModificationTimeVisible();
              onSuccess();
            });
          } else {
            message.error('您未有执行权限，请联系管理员');
          }
        }}
        onCancel={() => {
          setModificationTimeVisible();
        }}
      >
        <StyledAlert
          message={`机柜${v}电通过时间不符合期望${v}电时间，是否需要修改${v}电通过时间`}
          type="warning"
          showIcon
        />

        <Form form={form} labelCol={{ span: 5 }}>
          <Form.Item
            name="executeTime"
            label={`期望${taskSubType === 'POWER_OFF' ? '下' : '上'}电时间: `}
            rules={[
              {
                required: true,
                message: `期望${taskSubType === 'POWER_OFF' ? '下' : '上'}电通过时间必选`,
              },
            ]}
          >
            <DatePicker showTime disabled />
          </Form.Item>
          <Form.Item
            name="dateTime"
            label={`${taskSubType === 'POWER_OFF' ? '下' : '上'}电通过时间: `}
            rules={[
              {
                required: true,
                message: `${taskSubType === 'POWER_OFF' ? '下' : '上'}电通过时间必选`,
              },
            ]}
          >
            <DatePicker
              showTime
              allowClear={false}
              disabledDate={current => current && current >= moment().endOf('day')}
              disabledTime={current => {
                const compareMoment = moment();
                const hour = compareMoment.hour();
                const minute = compareMoment.minute();
                const second = compareMoment.second();
                if (current && current.isSame(compareMoment, 'day')) {
                  const currentHour = current.hour();
                  const currentMinute = current.minute();
                  if (currentHour === hour) {
                    return {
                      disabledHours: () => generateRange(hour + 1, 24),
                      disabledMinutes: () => generateRange(minute + 1, 60),
                      disabledSeconds: () =>
                        currentMinute === minute ? generateRange(second, 60) : [],
                    };
                  } else {
                    return {
                      disabledHours: () => generateRange(hour + 1, 24),
                      disabledMinutes: () => [],
                      disabledSeconds: () => [],
                    };
                  }
                }
                return {
                  disabledHours: () => [],
                  disabledMinutes: () => [],
                  disabledSeconds: () => [],
                };
              }}
            />
          </Form.Item>
          <Form.Item
            name="reason"
            label="修订原因"
            rules={[
              {
                required: true,
                message: '修订原因必填',
              },
              { max: 300, message: '最多输入 300 个字符！' },
            ]}
          >
            <Input.TextArea rows={3} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}
