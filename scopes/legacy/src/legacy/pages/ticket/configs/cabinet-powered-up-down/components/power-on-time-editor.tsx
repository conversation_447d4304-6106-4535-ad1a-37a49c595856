import moment from 'moment';
import React, { useState } from 'react';

import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Typography } from '@manyun/base-ui.ui.typography';
import { updatePowerGridTime } from '@manyun/ticket.service.update-power-grid-time';

export type PowerOnTimeEditorProps = {
  /**
   * 工单编号
   */
  taskNo: string;
  /**
   * 机柜编号
   */
  gridGuid: string;
  taskSubType: string;
  expectTime: number;
  powerOnTime: number;
  onSuccess?: () => void;
};

export const PowerOnTimeEditor = ({
  taskNo,
  gridGuid,
  taskSubType,
  expectTime,
  powerOnTime,
  onSuccess,
}: PowerOnTimeEditorProps) => {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  React.useEffect(() => {
    expectTime && form.setFieldsValue({ expectTime: moment(expectTime) });
    powerOnTime && form.setFieldsValue({ dateTime: moment(powerOnTime) });
  }, [expectTime, powerOnTime, open]);

  return (
    <>
      <Typography.Link
        onClick={() => {
          setOpen(true);
        }}
      >
        编辑
      </Typography.Link>
      <Modal
        width={550}
        open={open}
        title={`编辑实际${taskSubType === 'POWER_OFF' ? '下' : '上'}电时间`}
        okText="保存"
        destroyOnClose
        afterClose={() => {
          form.resetFields();
        }}
        okButtonProps={{
          loading,
        }}
        onCancel={() => {
          setOpen(false);
        }}
        onOk={() => {
          form.validateFields().then(async values => {
            setLoading(true);
            const { error } = await updatePowerGridTime({
              taskNo: taskNo,
              gridGuid: gridGuid,
              powerRealTime: moment(values.dateTime).valueOf(),
              modReason: values.reason,
            });
            setLoading(false);
            if (error) {
              message.error(error.message);
              return;
            }
            message.success('操作成功');
            setOpen(false);
            onSuccess?.();
          });
        }}
      >
        <Form form={form} labelCol={{ span: 5 }}>
          <Form.Item
            name="expectTime"
            label={`期望${taskSubType === 'POWER_OFF' ? '下' : '上'}电时间: `}
            rules={[
              {
                required: true,
                message: `期望${taskSubType === 'POWER_OFF' ? '下' : '上'}电通过时间必选`,
              },
            ]}
          >
            <DatePicker showTime disabled />
          </Form.Item>
          <Form.Item
            name="dateTime"
            label={`实际${taskSubType === 'POWER_OFF' ? '下' : '上'}电时间`}
            rules={[
              {
                required: true,
                message: `实际${taskSubType === 'POWER_OFF' ? '下' : '上'}电时间必选`,
              },
            ]}
          >
            <DatePicker
              showTime
              disabledDate={current => current && current >= moment().endOf('day')}
              disabledTime={current => {
                const compareMoment = moment();
                const hour = compareMoment.hour();
                const minute = compareMoment.minute();
                const second = compareMoment.second();
                if (current && current.isSame(compareMoment, 'day')) {
                  const currentHour = current.hour();
                  const currentMinute = current.minute();
                  if (currentHour === hour) {
                    return {
                      disabledHours: () => generateRange(hour + 1, 24),
                      disabledMinutes: () => generateRange(minute + 1, 60),
                      disabledSeconds: () =>
                        currentMinute === minute ? generateRange(second, 60) : [],
                    };
                  } else {
                    return {
                      disabledHours: () => generateRange(hour + 1, 24),
                      disabledMinutes: () => [],
                      disabledSeconds: () => [],
                    };
                  }
                }
                return {
                  disabledHours: () => [],
                  disabledMinutes: () => [],
                  disabledSeconds: () => [],
                };
              }}
            />
          </Form.Item>
          <Form.Item
            name="reason"
            label="修订原因"
            rules={[
              {
                required: true,
                message: '修订原因必填',
              },
              { max: 300, message: '最多输入 300 个字符！' },
            ]}
          >
            <Input.TextArea rows={3} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

const generateRange = (start: number, end: number) => {
  const result = [];
  for (let i = start; i < end; i++) {
    result.push(i);
  }
  return result;
};
