import DownOutlined from '@ant-design/icons/es/icons/DownOutlined';
import QuestionCircleOutlined from '@ant-design/icons/es/icons/QuestionCircleOutlined';
import { saveAs } from 'file-saver';
import React, { useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Dropdown } from '@manyun/base-ui.ui.dropdown';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Upload } from '@manyun/dc-brain.ui.upload';

import { GutterWrapper, StatusText, TinyTable } from '@manyun/dc-brain.legacy.components';
import { ticketService } from '@manyun/dc-brain.legacy.services';

export function ImportCabinet({ onImportGrid, location, taskSubType, showGridAlias }) {
  const [visible, setVisible] = useState(false);
  const [tableData, setDataSource] = useState([]);
  const [confirmDisable, setConfirmDisable] = useState(true);
  const [importName, setImportName] = useState('导入');
  const [downloadTemplateName, setDownloadTemplateName] = useState('下载模板');

  const columns = React.useMemo(
    () =>
      [
        {
          title: '行号',
          dataIndex: 'rowTag',
          fixed: 'left',
        },

        {
          title: '包间',
          dataIndex: ['data', 'roomTag'],
          render: (text, { errMessage }) => getToolTilp(text, errMessage, 'roomTag'),
        },
        {
          title: '机列',
          dataIndex: ['data', 'columnTag'],
          render: (text, { errMessage }) => getToolTilp(text, errMessage, 'columnTag'),
        },
        {
          title: '机柜',
          dataIndex: ['data', 'gridTag'],
          render: (text, { errMessage }) => getToolTilp(text, errMessage, 'gridTag'),
        },
        {
          title: '机柜类型',
          dataIndex: ['data', 'gridType', 'name'],
        },
        {
          title: '设计功率',
          dataIndex: ['data', 'ratedPower'],
          render: ratedPower =>
            ratedPower !== null && ratedPower !== undefined ? `${ratedPower}kW` : '',
        },
        {
          title: '机柜状态',
          dataIndex: ['data', 'powerStatus'],
          render: powerStatus => (powerStatus ? powerStatus.name : '--'),
        },
        {
          title: '机柜别名',
          dataIndex: ['data', 'gridAlias'],
          render: (text, { errMessage }) => getToolTilp(text, errMessage, 'gridAlias'),
          isHide: !showGridAlias,
        },
      ].filter(item => !item?.isHide),
    [showGridAlias]
  );

  const downloadDemo = async () => {
    const { response, filename = 'import-grids-template.csv' } =
      await ticketService.downloadGridModel();
    if (!response) {
      return;
    }
    setDownloadTemplateName('下载编号模板');
    saveAs(response, filename);
  };

  // by 机柜别名
  const downloadDemoByGridAlias = async () => {
    const { response, filename = 'import-grids-template-by-alias.csv' } =
      await ticketService.downloadGridModelByGridAlias();
    if (!response) {
      return;
    }
    setDownloadTemplateName('下载别名模板');
    saveAs(response, filename);
  };

  const beforeUpload = (file, fileList) => {
    const fd = new FormData();
    fd.append('file', file);
    fd.append('taskSubType', taskSubType);
    fd.append('idcTag', location[0]);
    fd.append('blockTag', location[1]);
    loadFetchData(fd);
    setImportName('导入编号');
    return false;
  };

  const loadFetchData = async fd => {
    const { response, error } = await ticketService.powerGridImport(fd);
    if (error) {
      message.error(error);
      return;
    }
    if (response) {
      if (response.faultTotal === 0) {
        message.success('导入成功！');
        setConfirmDisable(false);
      }
      setDataSource(response.excelCheckDtos);
    }
  };

  // by 机柜别名
  const beforeUploadByGridAlias = (file, fileList) => {
    const fd = new FormData();
    fd.append('file', file);
    fd.append('taskSubType', taskSubType);
    fd.append('idcTag', location[0]);
    fd.append('blockTag', location[1]);
    loadFetchDataGridAlias(fd);
    setImportName('导入别名');
    return false;
  };

  // by 机柜别名
  const loadFetchDataGridAlias = async fd => {
    const { response, error } = await ticketService.powerGridImportByAlias(fd);
    if (error) {
      message.error(error);
      return;
    }
    if (response) {
      if (response.faultTotal === 0) {
        message.success('导入成功！');
        setConfirmDisable(false);
      }
      setDataSource(response.excelCheckDtos);
    }
  };

  return (
    <GutterWrapper>
      <Button
        disabled={!location || location.length !== 2 || !taskSubType}
        onClick={() => {
          setVisible(true);
        }}
      >
        导入机柜
      </Button>
      <Modal
        width="738px"
        title="导入机柜"
        bodyStyle={{ maxHeight: '452px', overflowY: 'auto' }}
        visible={visible}
        okButtonProps={{ disabled: confirmDisable }}
        onOk={() => {
          onImportGrid(tableData);
          setVisible(false);
          setDataSource([]);
          setConfirmDisable(true);
        }}
        onCancel={() => {
          setVisible(false);
          setDataSource([]);
          setConfirmDisable(true);
        }}
      >
        <GutterWrapper mode="vertical">
          <TinyTable
            rowKey="rowTag"
            align="left"
            columns={columns}
            actions={
              <GutterWrapper>
                {showGridAlias ? (
                  <Dropdown
                    menu={{
                      items: [
                        {
                          key: 'import-code',
                          label: (
                            <Upload
                              key="import"
                              beforeUpload={beforeUpload}
                              showUploadList={false}
                              accept=".csv,.xls,.xlsx"
                            >
                              <Button type="text">导入编号</Button>
                            </Upload>
                          ),
                        },
                        {
                          key: 'import-alias',
                          label: (
                            <Upload
                              key="import"
                              beforeUpload={beforeUploadByGridAlias}
                              showUploadList={false}
                              accept=".csv,.xls,.xlsx"
                            >
                              <Button type="text">导入别名</Button>
                            </Upload>
                          ),
                        },
                      ],
                    }}
                  >
                    <Button>
                      {importName}
                      <DownOutlined />
                    </Button>
                  </Dropdown>
                ) : (
                  <Upload
                    key="import"
                    beforeUpload={beforeUpload}
                    showUploadList={false}
                    accept=".csv,.xls,.xlsx"
                  >
                    <Button type="primary">导入</Button>
                  </Upload>
                )}

                {showGridAlias ? (
                  <Dropdown
                    menu={{
                      items: [
                        {
                          key: 'download-alias',
                          label: (
                            <Button type="text" onClick={downloadDemoByGridAlias}>
                              下载别名模板
                            </Button>
                          ),
                        },
                        {
                          key: 'import-code',
                          label: (
                            <Button type="text" onClick={downloadDemo}>
                              下载编号模板
                            </Button>
                          ),
                        },
                      ],
                    }}
                  >
                    <Button>
                      {downloadTemplateName}
                      <DownOutlined />
                    </Button>
                  </Dropdown>
                ) : (
                  <Button key="download" onClick={downloadDemo}>
                    下载模板
                  </Button>
                )}
              </GutterWrapper>
            }
            //   loading={loading}
            dataSource={tableData}
          />
        </GutterWrapper>
      </Modal>
    </GutterWrapper>
  );
}

export default ImportCabinet;

function getToolTilp(value, errMessage, dataType) {
  const newValue = value;
  if (Object.prototype.hasOwnProperty.call(errMessage, dataType)) {
    return (
      <Tooltip title={errMessage[dataType]} placement="topLeft">
        <GutterWrapper size="2px" flex center>
          <StatusText status="alarm" style={{ display: 'inline-block' }}>
            {newValue ? newValue : '--'}
          </StatusText>
          <QuestionCircleOutlined />
        </GutterWrapper>
      </Tooltip>
    );
  }
  return newValue;
}
