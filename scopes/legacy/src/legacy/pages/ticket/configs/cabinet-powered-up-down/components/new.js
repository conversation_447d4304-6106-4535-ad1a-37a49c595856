import Form from '@ant-design/compatible/es/form';
import ExclamationCircleOutlined from '@ant-design/icons/es/icons/ExclamationCircleOutlined';
import UploadOutlined from '@ant-design/icons/es/icons/UploadOutlined';
import { Select } from '@galiojs/awesome-antd';
import omit from 'lodash/omit';
import uniqBy from 'lodash/uniqBy';
import moment from 'moment';
import React, { useState } from 'react';
import { connect, useSelector } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Explanation } from '@manyun/base-ui.ui.explanation';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { SlaUnit } from '@manyun/ticket.model.task';
import { SlaSelect, generateCorrectJobSla } from '@manyun/ticket.ui.sla-select';

import {
  FooterToolBar,
  GutterWrapper,
  LocationCascader,
  TinyCard,
  TinyTable,
} from '@manyun/dc-brain.legacy.components';
import { StyledMcUpload } from '@manyun/dc-brain.legacy.pages/ticket/configs/access-door/styled';
import { getFileInfoList } from '@manyun/dc-brain.legacy.pages/ticket/configs/access-door/utils';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  getTicketFilesActionCreator,
  ticketActions,
} from '@manyun/dc-brain.legacy.redux/actions/ticketActions';
import { ticketService } from '@manyun/dc-brain.legacy.services';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';

import AddCabinet from './add-cabinet';
import ImportCabinet from './import-cabinet';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};

export class NewTicketComponent extends React.Component {
  state = {
    gridList: [],
    selectedRowKeys: [],
    selectedRows: [],
    info: {},
    fileInfoList: [],
    subBtnLoading: false,
  };

  componentDidMount() {
    const id = this.props.match.params.id;
    this.props.syncCommonData({
      strategy: {
        ticketTypes: 'IF_NULL',
        deviceTypesPointsDefinition: ['11101', '11102', '11103'],
      },
    });

    if (id) {
      this.getInfo();
    } else {
      if (this.props.ticketTypes.length) {
        this.props.form.setFieldsValue({ taskSubType: this.props.ticketTypes[0].taskType });
      }
    }
  }

  componentDidUpdate(prevProps) {
    const { fileList } = this.props;
    if (fileList && fileList.length && !prevProps.fileList) {
      this.props.form.setFieldsValue({
        fileInfoList: fileList,
      });
    }
  }

  getInfo = async () => {
    const { setGridList } = this.props;
    const { response, error } = await ticketService.fetchTicketBasicInfo({
      taskNo: this.props.match.params.id,
    });
    const { response: gridList, error: gridErr } = await ticketService.powerResetGridList({
      taskNo: this.props.match.params.id,
    });
    if (error || gridErr) {
      message.error(error || gridErr);
      return;
    }
    let taskPropertiesJSON = {};
    try {
      taskPropertiesJSON = JSON.parse(response.taskProperties);
    } catch (e) {}

    this.setState({
      info: {
        taskSubType: response.taskSubType,
        taskTitle: response.taskTitle,
        expectTime: moment(new Date(Number(taskPropertiesJSON.expectTime))),
        location: [
          response.idcTag,
          response.blockTag.substring(response.blockTag.indexOf('.') + 1),
        ],
        taskSla: {
          sla: generateCorrectJobSla(response.taskSla, response.unit),
          unit: response.unit,
        },

        branchCoolOffPeriod: taskPropertiesJSON.branchCoolInterval
          ? {
              sla: generateCorrectJobSla(
                taskPropertiesJSON.branchCoolInterval,
                taskPropertiesJSON.branchCoolIntervalUnit
              ),
              unit: taskPropertiesJSON.branchCoolIntervalUnit,
            }
          : undefined,
        gridCoolOffPeriod: taskPropertiesJSON.gridCoolInterval
          ? {
              sla: generateCorrectJobSla(
                taskPropertiesJSON.gridCoolInterval,
                taskPropertiesJSON.gridCoolIntervalUnit
              ),
              unit: taskPropertiesJSON.gridCoolIntervalUnit,
            }
          : undefined,
      },
      gridList: gridList.data,
    });
    setGridList(gridList.data);
    this.props.getTicketFiles({
      targetId: response.taskNo,
      targetType: response.taskType,
      extensionType: 'BASE_FILE',
      callback: mcUploadFiles => {
        this.setState({ fileInfoList: mcUploadFiles });
      },
    });
  };

  onSubmit = () => {
    this.props.form.validateFields(async (errs, values) => {
      if (errs) {
        return;
      }
      const { gridList } = this.props;
      const fileInfoList = getFileInfoList(this.state.fileInfoList);
      const params = {
        ...omit(values, ['location', 'expectTime', 'branchCoolOffPeriod', 'gridCoolOffPeriod']),
        title: values.title,
        idcTag: values.location[0],
        blockGuid: `${values.location[0]}.${values.location[1]}`,
        gridParams: gridList,
        expectTime: values.expectTime && values.expectTime.valueOf(),
        taskSla: values.taskSla.sla ?? 0,
        branchCoolInterval: values.branchCoolOffPeriod.sla ?? 0,
        branchCoolIntervalUnit: values.branchCoolOffPeriod.unit,
        gridCoolInterval: values.gridCoolOffPeriod.sla ?? 0,
        gridCoolIntervalUnit: values.gridCoolOffPeriod.unit,
        unit: values.taskSla.unit,
        fileInfoList,
      };
      let res = {};
      this.setState({ subBtnLoading: true });
      //重新发送
      if (this.props.match.params.id) {
        const { response, error } = await ticketService.powerUpdate({
          ...params,
          taskNo: this.props.match.params.id,
        });
        this.setState({ subBtnLoading: false });
        if (error) {
          message.error(error);
          return;
        }
        res = response;
        message.success('重新发送成功');
      } else {
        // 新建
        const { response, error } = await ticketService.powerGridCreate(params);
        this.setState({ subBtnLoading: false });
        if (error) {
          message.error(error);
          return;
        }
        res = response;

        message.success('创建成功');
      }

      setTimeout(() => {
        this.props.history.push(
          urls.generateTicketDetailLocation({
            ticketType: this.props.ticketType,
            id: res.taskNo,
            idcTag: res.idcTag,
            blockTag: res.blockTag,
          })
        );
      }, 1.5 * 1000);
    });
  };

  addGrid = ({ selectedRows }) => {
    const { setGridList } = this.props;
    setGridList(gridList => [
      ...gridList,
      ...selectedRows.map(item => {
        return {
          idcTag: item.idcTag,
          blockTag: item.blockTag,
          columnTag: item.columnTag,
          gridTag: item.tag,
          gridGuid: item.guid,
          gridType: item.gridType.code,
          gridTypeName: item.gridType.name,
          ratedPower: item.ratedPower,
          roomTag: item.roomTag,
          powerStatus: item.powerStatus,
          gridAlias: item.specDataList?.find(item => item.specCode === 'IT_ALIAS')?.specValue,
        };
      }),
    ]);
  };

  importGrid = importGrids => {
    const { setGridList } = this.props;

    const list = [
      ...this.state.gridList,
      ...importGrids.map(item => {
        return {
          ...item.data,
          gridType: item.data.gridType.code,
          gridTypeName: item.data.gridType.name,
          powerStatus: item.data.powerStatus,
        };
      }),
    ];
    setGridList(uniqBy(list, 'gridGuid'));
  };

  handleDelete = record => {
    const { setGridList } = this.props;

    setGridList(gridList => gridList.filter(item => item.gridGuid !== record.gridGuid));
  };

  onSelectChange = (selectedRowKeys, selectedRows) => {
    this.setState({
      selectedRowKeys,
      selectedRows,
    });
  };

  onBatchDelete = () => {
    const { setGridList } = this.props;
    setGridList(gridList =>
      gridList.filter(item => !this.state.selectedRowKeys.includes(item.gridGuid))
    );
    this.setState({
      selectedRowKeys: [],
      selectedRows: [],
    });
  };

  render() {
    const { getFieldDecorator, getFieldsValue } = this.props.form;
    const { ticketTypes, history, gridList, setGridList, ticketConfig } = this.props;
    const { selectedRowKeys, selectedRows, info, subBtnLoading } = this.state;
    const branchCoolOffInitialValue = ticketConfig.powerOnOff.features.branchCoolOffPeriod;
    const gridCoolOffPeriodInitialValue = ticketConfig.powerOnOff.features
      .gridCoolOffPeriodPeriod ?? { value: 0, unit: SlaUnit.Minutes };

    const showGridAlias = ticketConfig.powerOnOff.features.showGridAlias; // 是否显示机柜别名等操作

    return (
      <GutterWrapper mode="vertical">
        <TinyCard title="基本信息">
          <Form colon={false} {...formItemLayout}>
            <div style={{ width: 939 }}>
              <Form.Item label="上下电类型">
                {getFieldDecorator('taskSubType', {
                  rules: [{ required: true, message: '上下电类型必选！' }],
                  initialValue: info.taskSubType,
                })(
                  <Select
                    style={{ width: 354 }}
                    onChange={() => {
                      setGridList([]);
                    }}
                  >
                    {ticketTypes.map(item => (
                      <Select.Option key={item.taskType} value={item.taskType}>
                        {item.taskValue}
                      </Select.Option>
                    ))}
                  </Select>
                )}
              </Form.Item>

              <Form.Item label="位置">
                {getFieldDecorator('location', {
                  rules: [
                    {
                      required: true,
                      message: '所属机房/楼栋必选！',
                      type: 'number',
                      transform: value => (value?.length === 2 ? 2 : false),
                    },
                  ],
                  initialValue: info.location,
                })(
                  <LocationCascader
                    currentAuthorize
                    style={{ width: 354 }}
                    onChange={() => {
                      setGridList([]);
                      this.setState({
                        selectedRowKeys: [],
                        selectedRows: [],
                      });
                    }}
                  />
                )}
              </Form.Item>
              <Form.Item label="工单标题">
                {getFieldDecorator('taskTitle', {
                  initialValue: info.taskTitle,
                  rules: [
                    { required: true, message: '工单标题必填！' },
                    {
                      type: 'string',
                      max: 20,
                      message: '最多输入 20 个字符',
                    },
                  ],
                })(<Input style={{ width: 354 }} />)}
              </Form.Item>
              {getFieldsValue().taskSubType &&
                getFieldsValue().taskSubType !== 'POWER_TEST_TO_ON' && (
                  <Form.Item
                    label={`期望${
                      getFieldsValue().taskSubType === 'POWER_OFF' ? '下' : '上'
                    }电时间`}
                  >
                    {getFieldDecorator('expectTime', {
                      rules: [
                        {
                          required: true,
                          message: `期望${
                            getFieldsValue().taskSubType === 'POWER_OFF' ? '下' : '上'
                          }电时间必选！`,
                        },
                      ],
                      initialValue: info.expectTime,
                    })(
                      <DatePicker
                        style={{ width: 354 }}
                        showTime={{ format: 'HH:mm' }}
                        format="YYYY-MM-DD HH:mm"
                        allowClear
                      />
                    )}
                  </Form.Item>
                )}
              <Form.Item label="SLA">
                {getFieldDecorator('taskSla', {
                  initialValue: info.taskSla ?? { sla: 1, unit: SlaUnit.Hour },
                })(<SlaSelect />)}
              </Form.Item>
              <Form.Item
                label={
                  <Space align="baseline" size={0}>
                    支路操作间隔冷静期
                    <Explanation
                      iconType="exclamation"
                      tooltip={{
                        title: '表示移动端操作同机柜各支路开关的间隔时间',
                      }}
                    />
                  </Space>
                }
              >
                {getFieldDecorator('branchCoolOffPeriod', {
                  initialValue: info.branchCoolOffPeriod ?? {
                    sla: branchCoolOffInitialValue.value,
                    unit: branchCoolOffInitialValue.unit,
                  },
                })(<SlaSelect precision={0} />)}
              </Form.Item>
              <Form.Item
                label={
                  <Space align="baseline" size={0}>
                    机柜操作间隔冷静期
                    <Explanation
                      iconType="exclamation"
                      tooltip={{
                        title: '表示移动端操作不同机柜对应开关的间隔时间',
                      }}
                    />
                  </Space>
                }
              >
                {getFieldDecorator('gridCoolOffPeriod', {
                  initialValue: info.gridCoolOffPeriod ?? {
                    sla: gridCoolOffPeriodInitialValue.value,
                    unit: gridCoolOffPeriodInitialValue.unit,
                  },
                })(<SlaSelect precision={0} />)}
              </Form.Item>
              <Form.Item label="附件">
                <StyledMcUpload
                  accept="image/*,.xls,.xlsx"
                  showUploadList
                  maxFileSize={20}
                  allowDelete
                  fileList={this.state.fileInfoList}
                  showAccept
                  onChange={({ fileList }) => this.setState({ fileInfoList: fileList })}
                >
                  <Button icon={<UploadOutlined />}>上传</Button>
                </StyledMcUpload>
              </Form.Item>
            </div>
          </Form>
        </TinyCard>
        <TinyCard title="已选列表" style={{ marginBottom: '40px' }}>
          <TinyTable
            rowKey="gridGuid"
            align="left"
            scroll={{ x: 'max-content' }}
            columns={this.props.columns(this)}
            actions={
              <GutterWrapper mode="horizontal" flex>
                <AddCabinet
                  addedGridList={gridList}
                  taskSubType={getFieldsValue().taskSubType}
                  location={getFieldsValue().location}
                  onAddGrid={this.addGrid}
                />
                <ImportCabinet
                  location={getFieldsValue().location}
                  taskSubType={getFieldsValue().taskSubType}
                  showGridAlias={showGridAlias}
                  onImportGrid={this.importGrid}
                />
                <Button disabled={!selectedRowKeys.length} onClick={this.onBatchDelete}>
                  批量删除
                </Button>
                <Space size={4}>
                  <ExclamationCircleOutlined /> <span>已选择{selectedRowKeys.length}项</span>
                  <Button
                    compact
                    type="link"
                    onClick={() => {
                      this.setState({ selectedRowKeys: [], selectedRows: [] });
                    }}
                  >
                    清空
                  </Button>
                </Space>
              </GutterWrapper>
            }
            dataSource={gridList}
            rowSelection={{
              selectedRowKeys: selectedRowKeys,
              selectedRows: selectedRows,
              onChange: this.onSelectChange,
            }}
          />
        </TinyCard>
        <FooterToolBar>
          <GutterWrapper>
            <Button
              disabled={!gridList.length}
              type="primary"
              loading={subBtnLoading}
              onClick={this.onSubmit}
            >
              提交
            </Button>
            <Button loading={subBtnLoading} onClick={() => history.goBack()}>
              取消
            </Button>
          </GutterWrapper>
        </FooterToolBar>
      </GutterWrapper>
    );
  }
}
const connectOpts = { forwardRef: true };

const mapStateToProps = ({ common: { ticketTypes } }) => {
  let powerTypes = [];
  if (ticketTypes) {
    ticketTypes.treeList.forEach(item => {
      if (item.taskType === 'POWER' && item.children) {
        powerTypes = item.children;
      }
    });
  }
  return {
    ticketTypes: powerTypes,
  };
};

const mapDispatchToProps = {
  setCreatePatrolRooms: ticketActions.setCreatePatrolRooms,
  setCreatePatrolCheckItem: ticketActions.setCreatePatrolCheckItem,
  setCreatePatrolDevices: ticketActions.setCreatePatrolDevices,
  syncCommonData: syncCommonDataActionCreator,
  getTicketFiles: getTicketFilesActionCreator,
};

const NewTicketForm = connect(
  mapStateToProps,
  mapDispatchToProps,
  null,
  connectOpts
)(Form.create()(NewTicketComponent));

export default function NewTicket(props) {
  const [configUtil] = useConfigUtil();
  const ticketConfig = configUtil.getScopeCommonConfigs('ticket');
  const showGridAlias = ticketConfig.powerOnOff.features.showGridAlias;
  const [gridList, setGridList] = useState([]);

  const columns = (context, showGridAlias) => {
    return [
      {
        title: '楼栋',
        dataIndex: 'blockTag',
      },
      {
        title: '包间',
        dataIndex: 'roomTag',
      },
      {
        title: '机列',
        dataIndex: 'columnTag',
      },
      {
        title: showGridAlias ? '机柜编号' : '机柜',
        dataIndex: 'gridTag',
      },
      {
        title: '机柜别名',
        dataIndex: 'gridAlias',
        render: (val, o) => {
          return (
            <Typography.Text style={{ width: 200 }} ellipsis={{ tooltip: val }}>
              {val}
            </Typography.Text>
          );
        },
        isHide: !showGridAlias,
      },
      {
        title: '机柜类型',
        dataIndex: 'gridTypeName',
        render: (gridTypeName, record) => (gridTypeName ? gridTypeName : record.gridType.name),
      },
      {
        title: '设计功率',
        dataIndex: 'ratedPower',
        render: ratedPower =>
          ratedPower !== null && ratedPower !== undefined ? `${ratedPower}kW` : '',
      },
      {
        title: '机柜状态',
        dataIndex: 'powerStatus',
        render: powerStatus => (powerStatus ? powerStatus.name : '--'),
      },
      {
        title: '操作',
        dataIndex: 'operation',
        render: (text, record) => (
          <Button compact type="link" onClick={() => context.handleDelete(record)}>
            删除
          </Button>
        ),
      },
    ].filter(item => !item?.isHide);
  };
  return (
    <NewTicketForm
      {...props}
      ticketConfig={ticketConfig}
      gridList={gridList}
      setGridList={setGridList}
      columns={c => columns(c, showGridAlias)}
    />
  );
}
