import { useEffect, useState } from 'react';

import InboxOutlined from '@ant-design/icons/es/icons/InboxOutlined';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import { saveBizFiles } from '@manyun/dc-brain.service.save-biz-files';
import { McUpload } from '@manyun/dc-brain.ui.upload';

export function EvidentiaryMaterialUpload({
  updateVisible,
  evidentiaryMaterial,
  targetType,
  targetId,
  fileType,
  onChangeFile,
  onCancel,
}) {
  const [files, setFiles] = useState([]);

  useEffect(() => {
    setFiles(evidentiaryMaterial);
  }, [evidentiaryMaterial]);

  return (
    <Modal
      destroyOnClose
      title="上传附件"
      style={{ minWidth: 443 }}
      open={updateVisible}
      okText="提交"
      onCancel={() => {
        onCancel();
        setFiles(evidentiaryMaterial);
      }}
      onOk={async () => {
        const { data, error } = await saveBizFiles({
          fileInfos: files,
          targetType,
          targetId,
          fileType,
        });
        if (error) {
          message.error(error.message);
          return;
        }
        data && onChangeFile(files);
      }}
    >
      <McUpload
        type="drag"
        fileList={files}
        accept="image/*,.xls,.xlsx"
        onChange={info => {
          setFiles(info.fileList);
        }}
      >
        <Space direction="vertical">
          <p>
            <InboxOutlined style={{ fontSize: 48, color: `var(--${prefixCls}-primary-color)` }} />
          </p>
          <Typography.Text>点击或将文件拖拽到这里上传</Typography.Text>
          <Typography.Text type="secondary">支持扩展名：image/*,.xls,.xlsx</Typography.Text>
        </Space>
      </McUpload>
    </Modal>
  );
}

export default EvidentiaryMaterialUpload;
