import { CURRENT_PAGE_TYPES } from '../../constants';
import TTR from '../../registries/ticket-type-registry';
import Detail from './components/detail';
import NewTicket from './components/new';

TTR.registerTicketType(/* #24 !!!∑(ﾟДﾟノ)ノ 设备上下架工单 */ 'device_general')
  .registerConfig({
    type: CURRENT_PAGE_TYPES.TICKETS,
    showNewBtn: true,
    mergeFilters: ({ DEFAULT_FILTER_KEY_MAP, defaultFilters }) => {
      const newFilters = [...defaultFilters].filter(
        ({ key }) => key !== DEFAULT_FILTER_KEY_MAP.END_TIME
      );

      return newFilters;
    },
  })
  .registerConfig({
    type: CURRENT_PAGE_TYPES.NEW_TICKET,
    content: NewTicket,
  })
  .registerConfig({
    type: CURRENT_PAGE_TYPES.SPECIFIC_TICKET,
    content: Detail,
  });
