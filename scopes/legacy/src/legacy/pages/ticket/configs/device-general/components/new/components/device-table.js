import React from 'react';

import { EditableTable, Form } from '@galiojs/awesome-antd';
import cloneDeep from 'lodash.clonedeep';

import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';

import Cabinet from './cabinet';

function DeviceTable({
  data,
  deleteByCancel,
  editingRowKey,
  setEditingRowKey,
  setData,
  setDeleteByCancel,
  idcTag,
  blockTag,
}) {
  const [form] = Form.useForm();

  return (
    <EditableTable
      form={form}
      rowKey="id"
      dataSource={data}
      columns={getColumns({ idcTag, blockTag })}
      editingRowKey={editingRowKey}
      onSave={(currentKey, records) => {
        // 判断设备编号是否重复
        const currentRow = records.find(record => record.id === currentKey);
        const isSameGuid = records.find(
          record => record.id !== currentRow.id && record.itemNo.trim() === currentRow.itemNo.trim()
        );
        if (isSameGuid) {
          message.error('设备编号不可重复！');
          return;
        }
        setData(
          records.map(item => {
            return {
              ...item,
              desc: item.desc.trim(),
              guid: item.guid.trim(),
            };
          })
        );
        setEditingRowKey(null);
        setDeleteByCancel(false);
      }}
      onEdit={rowKey => setEditingRowKey(rowKey)}
      onCancel={() => {
        if (deleteByCancel) {
          const nextData = cloneDeep(data);
          const idx = nextData.findIndex(record => record.id === editingRowKey);
          if (idx > -1) {
            nextData.splice(idx, 1);
          }
          setData(nextData);
          setEditingRowKey(null);
          setDeleteByCancel(false);
        } else {
          setEditingRowKey(null);
        }
      }}
      onDelete={(rowKey, data) => {
        let editKey = editingRowKey;
        if (rowKey === editingRowKey) {
          editKey = null;
        }
        setData(data);
        setEditingRowKey(editKey);
      }}
    />
  );
}

export default DeviceTable;

const getColumns = ({ idcTag, blockTag }) => [
  {
    title: '设备编号',
    dataIndex: 'itemNo',
    editable: true,
    editingCtrl: <Input allowClear />,
    formItemProps: {
      rules: [
        {
          required: true,
          message: '设备编号必填！',
        },
        {
          type: 'string',
          max: 20,
          message: '最多输入 20 个字符！',
        },
      ],
    },
  },
  {
    title: '目的机柜',
    dataIndex: 'guid',
    editable: true,
    width: 300,
    editingCtrl: <Cabinet idcTag={idcTag} blockTag={blockTag} />,
    formItemProps: {
      rules: [
        {
          required: true,
          message: '目的位置必选！',
        },
      ],
    },
  },
  {
    title: '描述',
    dataIndex: 'desc',
    editable: true,
    editingCtrl: <Input allowClear />,
    formItemProps: {
      rules: [
        {
          required: true,
          message: '描述必填！',
        },
        {
          type: 'string',
          max: 120,
          message: '最多输入 120 个字符！',
        },
      ],
    },
  },
];
