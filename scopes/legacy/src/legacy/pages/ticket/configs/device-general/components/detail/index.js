import React from 'react';
import { connect } from 'react-redux';

import { message } from '@manyun/base-ui.ui.message';

import { GutterWrapper, TinyDescriptions, TinyTable } from '@manyun/dc-brain.legacy.components';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import { taskCenterService } from '@manyun/dc-brain.legacy.services';

export class TicketDetail extends React.Component {
  state = {
    generalItemInfoList: [],
  };

  componentDidMount() {
    this.props.syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });
    this.getGeneralItemInfoList();
  }

  getGeneralItemInfoList = async () => {
    const { taskNo } = this.props;
    const { error, response } = await taskCenterService.fetchDeviceGeneralItems({ taskNo });
    if (error) {
      message.error(error);
      return;
    }
    this.setState({ generalItemInfoList: response.data });
  };

  render() {
    const { taskProperties } = this.props;
    const { generalItemInfoList } = this.state;
    return (
      <GutterWrapper mode="vertical">
        <TinyDescriptions
          column={4}
          descriptionsItems={[{ label: '说明', value: taskProperties }]}
        />
        <TinyTable
          rowKey="itemNo"
          align="left"
          dataSource={generalItemInfoList}
          columns={[
            {
              title: '设备编号',
              dataIndex: 'itemNo',
            },
            {
              title: '目的机柜',
              dataIndex: 'guid',
            },
            {
              title: '描述',
              dataIndex: 'desc',
            },
          ]}
        />
      </GutterWrapper>
    );
  }
}

const mapStateToProps = ({
  ticket: {
    ticketView: { basicInfo },
  },
}) => {
  return {
    taskProperties: basicInfo.taskProperties,
  };
};
const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
};
export default connect(mapStateToProps, mapDispatchToProps)(TicketDetail);
