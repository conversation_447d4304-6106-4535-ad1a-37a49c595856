import React from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import { ApiSelect } from '@galiojs/awesome-antd';
import shortid from 'shortid';

import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';

import { SlaUnit } from '@manyun/ticket.model.task';
import { SlaSelect } from '@manyun/ticket.ui.sla-select';

import {
  <PERSON>er<PERSON>oolBar,
  GutterWrapper,
  LocationCascader,
  TinyCard,
} from '@manyun/dc-brain.legacy.components';
import { METADATA_TYPE } from '@manyun/dc-brain.legacy.constants';
import { StyledMcUpload } from '@manyun/dc-brain.legacy.pages/ticket/configs/access-door/styled';
import { getFileInfoList } from '@manyun/dc-brain.legacy.pages/ticket/configs/access-door/utils';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import { redirectActionCreator } from '@manyun/dc-brain.legacy.redux/actions/routerActions';
import { taskCenterService } from '@manyun/dc-brain.legacy.services';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';

import DeviceTable from './components/device-table';

const initNo = shortid();

export class NewTicket extends React.Component {
  state = {
    editingRowKey: initNo,
    deleteByCancel: true,
    deviceTableData: [{ itemNo: null, desc: null, guid: null, id: initNo }],
    subBtnloading: false,
  };

  componentDidMount() {
    this.props.syncCommonData({
      strategy: { ticketTypes: 'IF_NULL' },
    });
  }

  submit = () => {
    const { form } = this.props;
    const { editingRowKey, deviceTableData } = this.state;
    form.validateFields(async (error, values) => {
      if (error) {
        return;
      }
      if (editingRowKey) {
        message.error('请先保存设备信息！');
        return;
      }
      if (!deviceTableData.length) {
        message.error('至少添加一条设备！');
        return;
      }
      this.setState({ subBtnloading: true });
      const fileInfoList = getFileInfoList(values?.fileInfoList);
      const params = {
        generalTaskType: METADATA_TYPE.DEVICE_GENERAL,
        generalSubTaskType: values.generalSubTaskType,
        idcTag: values.location[0],
        blockGuid: values.location.join('.'),
        generalItemInfoList: deviceTableData.map(({ guid, itemNo, desc }) => ({
          guid: guid.trim(),
          itemNo,
          desc: desc.trim(),
        })),
        desc: values.desc.trim(),
        sla: values.taskSla.sla,
        slaUnit: values.taskSla.unit,
        fileInfoList,
      };
      const data = await taskCenterService.fetchCreateDeviceGeneralTicket(params);
      if (data.error) {
        message.error(data.error);
        this.setState({ subBtnloading: true });
        return;
      }
      message.success('创建成功！');
      this.props.redirect(
        urls.generateTicketDetailLocation({
          ticketType: 'device_general',
          id: data.response,
        })
      );
    });
  };

  render() {
    const {
      form: { getFieldDecorator, getFieldValue },
      generalSubTaskType,
    } = this.props;
    const { editingRowKey, deleteByCancel, deviceTableData, subBtnloading } = this.state;
    return (
      <GutterWrapper mode="vertical">
        <TinyCard title="基本信息" style={{ marginBottom: 40 }}>
          <Form
            colon={false}
            labelCol={{ xl: 2 }}
            wrapperCol={{ xl: 20 }}
            // style={{ width: '540px' }}
          >
            <Form.Item label="工单子类型">
              {getFieldDecorator('generalSubTaskType', {
                rules: [{ required: true, message: '工单子类型必选！' }],
              })(
                <ApiSelect
                  showSearch
                  fieldNames={{ value: 'taskType', label: 'taskValue' }}
                  dataService={async () => Promise.resolve(generalSubTaskType)}
                  allowClear
                  style={{ width: 200 }}
                />
              )}
            </Form.Item>
            <Form.Item label="位置">
              {getFieldDecorator('location', {
                rules: [
                  {
                    required: true,
                    message: '机房、楼栋必选！',
                  },
                ],
              })(
                <LocationCascader style={{ width: 200 }} changeOnSelect={false} currentAuthorize />
              )}
            </Form.Item>
            <Form.Item label="说明">
              {getFieldDecorator('desc', {
                rules: [
                  {
                    required: true,
                    message: '说明必填',
                  },
                  {
                    type: 'string',
                    max: 100,
                    message: '最多输入 100 个字符！',
                  },
                ],
              })(<Input.TextArea style={{ width: 335 }} />)}
            </Form.Item>
            <Form.Item label="SLA">
              {getFieldDecorator('taskSla', {
                initialValue: { sla: 0, unit: SlaUnit.Hour },
              })(<SlaSelect />)}
            </Form.Item>
            <Form.Item label="附件">
              {getFieldDecorator('fileInfoList', {
                valuePropName: 'fileList',
                normalize: value => {
                  if (Array.isArray(value)) {
                    return value;
                  }
                  return value?.fileList || [];
                },
              })(
                <StyledMcUpload
                  accept="image/*,.xls,.xlsx"
                  showUploadList
                  maxFileSize={20}
                  allowDelete
                  showAccept
                >
                  <Button type="primary">上传</Button>
                </StyledMcUpload>
              )}
            </Form.Item>
            <Form.Item label=" ">
              <Button
                type="primary"
                disabled={editingRowKey ? true : false}
                onClick={() => {
                  const addNo = shortid();
                  this.setState({
                    deviceTableData: [
                      ...deviceTableData,
                      { itemNo: null, desc: null, guid: null, id: addNo },
                    ],
                    editingRowKey: addNo,
                    deleteByCancel: true,
                  });
                }}
              >
                添加
              </Button>
            </Form.Item>
            {getFieldValue('location') && (
              <Form.Item label=" ">
                <DeviceTable
                  data={deviceTableData}
                  deleteByCancel={deleteByCancel}
                  editingRowKey={editingRowKey}
                  setEditingRowKey={value => this.setState({ editingRowKey: value })}
                  setData={value => this.setState({ deviceTableData: value })}
                  setDeleteByCancel={value => this.setState({ deleteByCancel: value })}
                  idcTag={getFieldValue('location')[0]}
                  blockTag={getFieldValue('location')[1]}
                />
              </Form.Item>
            )}
          </Form>
        </TinyCard>
        <FooterToolBar>
          <GutterWrapper>
            <Button type="primary" loading={subBtnloading} onClick={this.submit}>
              提交
            </Button>
            <Button onClick={() => this.props.history.goBack()}>取消</Button>
          </GutterWrapper>
        </FooterToolBar>
      </GutterWrapper>
    );
  }
}

const mapStateToProps = ({ common: { ticketTypes } }) => {
  let generalSubTaskType = [];
  const generalTaskType = ticketTypes?.treeList.find(
    ({ taskType }) => taskType === METADATA_TYPE.DEVICE_GENERAL
  );
  if (generalTaskType) {
    generalSubTaskType = generalTaskType.children
      .map(({ taskType, taskValue, flag }) => {
        if (flag) {
          return null;
        }
        return {
          taskType,
          taskValue,
        };
      })
      .filter(Boolean);
  }
  return {
    generalSubTaskType,
  };
};

const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
  redirect: redirectActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(Form.create()(NewTicket));
