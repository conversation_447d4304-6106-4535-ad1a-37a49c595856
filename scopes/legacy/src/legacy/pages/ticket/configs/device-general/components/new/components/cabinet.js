import React, { useState } from 'react';

import { Select } from '@galiojs/awesome-antd';
import debounce from 'lodash/debounce';

import { message } from '@manyun/base-ui.ui.message';

import { cabinetManageService } from '@manyun/dc-brain.legacy.services';

function Cabinet({ idcTag, blockTag, ...props }) {
  const [cabinents, setCabinents] = useState([]);
  return (
    <Select
      {...props}
      showSearch
      allowClear
      defaultActiveFirstOption={false}
      showArrow={false}
      filterOption={false}
      onSearch={debounce(async value => {
        if (value === '') {
          return;
        }
        const { response, error } = await cabinetManageService.fetchGrids({
          tag: value,
          idcTag,
          blockTag,
          pageNum: 1,
          pageSize: 10,
        });
        if (error) {
          message.error(error);
          return;
        }
        setCabinents(response.data);
      }, 500)}
    >
      {cabinents.map(item => {
        return (
          <Select.Option key={item.guid} value={item.guid} title={item.guid}>
            {item.guid}
          </Select.Option>
        );
      })}
    </Select>
  );
}
export default React.forwardRef((props, ref) => <Cabinet forwardedRef={ref} {...props} />);
