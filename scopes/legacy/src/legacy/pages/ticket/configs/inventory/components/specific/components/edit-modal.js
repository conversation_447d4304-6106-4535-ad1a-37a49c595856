import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';

import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Typography } from '@manyun/base-ui.ui.typography';

import { VendorModelSelect } from '@manyun/crm.ui.vendor-model-select';
import { RoomSelect } from '@manyun/resource-hub.ui.room-select';

import { TinyModal, TinyUpload } from '@manyun/dc-brain.legacy.components';
import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';
import { INVENTORY_STATUS_TYPE_KEY_MAP } from '@manyun/dc-brain.legacy.constants/inventory';
import * as taskCenterService from '@manyun/dc-brain.legacy.services/taskCenterService';
import { getDeviceTypeName } from '@manyun/dc-brain.legacy.utils/ticket';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};

class EditModal extends Component {
  state = {
    editVisible: false,
    deviceType: this.props.info.deviceType,
  };

  changeVisible = () => {
    this.setState({ editVisible: !this.state.editVisible });
  };

  render() {
    const { form, text, type, title, info = {}, normalizedList, fetchDeviceList } = this.props;
    const { editVisible } = this.state;
    const { getFieldDecorator, validateFields } = form;

    return [
      <Button compact key="btn" type={type} onClick={this.changeVisible}>
        {text}
      </Button>,
      <TinyModal
        width={648}
        title={<Typography.Title level={4}>{title}</Typography.Title>}
        visible={editVisible}
        onClose={this.changeVisible}
        onOk={() => {
          validateFields(async (err, valueMap) => {
            if (err) {
              return;
            }
            const { error } = await taskCenterService.updateInventoryDevice(getQ(info, valueMap));
            if (error) {
              message.error(error);
              return;
            }
            this.changeVisible();
            fetchDeviceList(1, 10);
          });
        }}
        onCancel={this.changeVisible}
        key="drawer"
      >
        <Form colon={false} {...formItemLayout}>
          <Form.Item label="资产ID">{info.assetNo || BLANK_PLACEHOLDER}</Form.Item>
          {info.deviceStatus !== INVENTORY_STATUS_TYPE_KEY_MAP.UN_INVENTORY &&
            info.deviceStatus !== INVENTORY_STATUS_TYPE_KEY_MAP.INVENTORY_LOSS && (
              <>
                <Form.Item label="包间">
                  {getFieldDecorator('roomTag', {
                    initialValue: info.roomTag,
                  })(<RoomSelect blockGuid={info.blockGuid} style={{ width: 180 }} disabled />)}
                </Form.Item>
                <Form.Item label="资产分类">
                  {getDeviceTypeName(info.deviceType, normalizedList)}
                </Form.Item>
                <Form.Item label="厂商型号">
                  {getFieldDecorator('vendorModel', {
                    initialValue: [info.vendor, info.productModel],
                    rules: [
                      {
                        required: true,
                        message: '厂商型号必选！',
                        type: 'number',
                        transform: value => (value?.length === 2 ? 2 : false),
                      },
                    ],
                  })(
                    <VendorModelSelect
                      style={{ width: 200 }}
                      deviceType={info.deviceType}
                      allowClear
                    />
                  )}
                </Form.Item>
              </>
            )}
          <Form.Item label="上传图片">
            {getFieldDecorator('files', {
              rules: [
                {
                  required: true,
                  message: '图片必传！',
                },
              ],
              initialValue: info.files,
            })(
              <TinyUpload
                showUploadBtn
                allowDelete
                maxFileSize={5}
                accept={'.jpg,.png'}
                listType="picture-card"
              />
            )}
          </Form.Item>
          <Form.Item label="备注描述">
            {getFieldDecorator('remark', {
              rules: [
                {
                  required: true,
                  message: '备注描述必填！',
                },
                {
                  max: 50,
                  message: '最多输入 50 个字符！',
                },
              ],
              initialValue: info.remark,
            })(<Input.TextArea placeholder="请输入" autoSize={{ minRows: 3 }} />)}
          </Form.Item>
        </Form>
      </TinyModal>,
    ];
  }
}

const mapStateToProps = ({ common: { deviceCategory, space } }) => {
  return {
    normalizedList: deviceCategory?.normalizedList,
  };
};
export default connect(mapStateToProps, null)(Form.create({ name: 'modal_edit' })(EditModal));

function switchStatus(deviceStatus) {
  switch (deviceStatus) {
    case INVENTORY_STATUS_TYPE_KEY_MAP.INVENTORY_LOSS:
    case INVENTORY_STATUS_TYPE_KEY_MAP.UN_INVENTORY:
      return INVENTORY_STATUS_TYPE_KEY_MAP.INVENTORY_LOSS;
    case INVENTORY_STATUS_TYPE_KEY_MAP.INVENTORY_ERROR:
    case INVENTORY_STATUS_TYPE_KEY_MAP.INVENTORY_NORMAL:
      return INVENTORY_STATUS_TYPE_KEY_MAP.INVENTORY_ERROR;
    default:
      return deviceStatus;
  }
}

function getQ(info, { files, remark, vendorModel }) {
  const { assetNo, deviceStatus, taskNo, roomGuid, deviceGuid, productModel, vendor, deviceType } =
    info;

  switch (deviceStatus) {
    case INVENTORY_STATUS_TYPE_KEY_MAP.INVENTORY_LOSS:
    case INVENTORY_STATUS_TYPE_KEY_MAP.UN_INVENTORY:
      return {
        assetNo,
        inventoryStatus: switchStatus(deviceStatus),
        fileInfos: files,
        remark,
        taskNo,
        roomGuid,
        productModel,
        vendor,
        deviceType,
        deviceGuid,
      };
    default:
      return {
        assetNo,
        deviceType,
        inventoryStatus: switchStatus(deviceStatus),
        fileInfos: files,
        productModel: vendorModel?.[1],
        remark,
        taskNo,
        vendor: vendorModel?.[0],
        roomGuid,
        deviceGuid,
      };
  }
}
