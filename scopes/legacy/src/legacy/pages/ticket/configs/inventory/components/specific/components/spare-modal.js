import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';

import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { Typography } from '@manyun/base-ui.ui.typography';

import { VendorModelSelect } from '@manyun/crm.ui.vendor-model-select';

import { TinyModal } from '@manyun/dc-brain.legacy.components';
import * as taskCenterService from '@manyun/dc-brain.legacy.services/taskCenterService';
import { getDeviceTypeName } from '@manyun/dc-brain.legacy.utils/ticket';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};

class SpareModal extends Component {
  state = {
    editVisible: false,
    deviceType: this.props.info.deviceType,
  };

  changeVisible = () => {
    this.setState({ editVisible: !this.state.editVisible });
  };

  render() {
    const { form, text, type, title, info = {}, normalizedList, fetchDeviceList } = this.props;
    const { editVisible, deviceType } = this.state;
    const { getFieldDecorator, validateFields } = form;

    return [
      <Button compact key="btn" type={type} onClick={this.changeVisible}>
        {text}
      </Button>,
      <TinyModal
        title={<Typography.Title level={4}>{title}</Typography.Title>}
        visible={editVisible}
        onClose={this.changeVisible}
        onOk={() => {
          validateFields(async (err, valueMap) => {
            if (err) {
              return;
            }
            const { error } = await taskCenterService.updateInventorySpareDevice(
              getQ(info, valueMap, deviceType)
            );
            if (error) {
              message.error(error);
              return;
            }
            this.changeVisible();
            fetchDeviceList(1, 10);
          });
        }}
        onCancel={this.changeVisible}
        key="drawer"
      >
        <Form colon={false} {...formItemLayout}>
          <Form.Item label="资产分类">{getDeviceTypeName(deviceType, normalizedList)}</Form.Item>
          <Form.Item label="厂商型号">
            {getFieldDecorator('vendorModel', {
              initialValue: [info.vendor, info.productModel],
            })(<VendorModelSelect style={{ width: '50%' }} disabled numbered={false} />)}
          </Form.Item>
          <Form.Item label="盘点数量">
            {getFieldDecorator('inventoryCount', {
              rules: [
                {
                  required: true,
                  message: '数量必填！',
                },
                { pattern: /^\d+$/, message: '盘点数量必须为整数' },
              ],
              initialValue: info.inventoryCount,
            })(<InputNumber min={0} max={999999} allowClear />)}
          </Form.Item>
          <Form.Item label="备注描述">
            {getFieldDecorator('remark', {
              rules: [
                {
                  required: true,
                  message: '备注描述必填！',
                },
                {
                  max: 50,
                  message: '最多输入 50 个字符！',
                },
              ],
              initialValue: info.remark,
            })(<Input.TextArea placeholder="请输入" autoSize={{ minRows: 3 }} />)}
          </Form.Item>
        </Form>
      </TinyModal>,
    ];
  }
}

const mapStateToProps = ({ common: { deviceCategory } }) => {
  return {
    normalizedList: deviceCategory?.normalizedList,
  };
};
export default connect(mapStateToProps, null)(Form.create({ name: 'modal_spare' })(SpareModal));

function getQ(info, { remark, vendorModel, inventoryCount }, deviceType) {
  const { taskNo, roomGuid } = info;

  return {
    deviceType,
    productModel: vendorModel?.[1],
    remark,
    taskNo,
    vendor: vendorModel?.[0],
    roomGuid,
    inventoryCount,
  };
}
