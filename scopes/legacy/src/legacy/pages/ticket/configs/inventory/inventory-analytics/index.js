import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import Form from '@ant-design/compatible/es/form';
import Icon from '@ant-design/icons';
import difference from 'lodash/difference';
import trim from 'lodash/trim';
import uniq from 'lodash/uniq';

import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Radio } from '@manyun/base-ui.ui.radio';

import { VendorModelSelect } from '@manyun/crm.ui.vendor-model-select';
import { SpaceOrDeviceLink } from '@manyun/resource-hub.ui.space-or-device-link';

import {
  ApiTreeSelect,
  AssetClassificationApiTreeSelect,
  GutterWrapper,
  TinyCard,
  TinyTable,
} from '@manyun/dc-brain.legacy.components';
import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';
import {
  INVENTORY_COUNT_STATUS_OPTIONS,
  INVENTORY_STATUS_TYPE_VALUE_MAP,
} from '@manyun/dc-brain.legacy.constants/inventory';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  fetchInventoryAnalyticsCount,
  fetchInventoryAnalyticsPage,
  inventoryAnalyticsActions,
} from '@manyun/dc-brain.legacy.redux/actions/inventoryAnalysticsActions';
import { ticketActions } from '@manyun/dc-brain.legacy.redux/actions/ticketActions';
import * as taskCenterService from '@manyun/dc-brain.legacy.services/taskCenterService';
import { generateTreeData } from '@manyun/dc-brain.legacy.utils';
import { getDeviceTypeName } from '@manyun/dc-brain.legacy.utils/ticket';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';

const getColumns = (ctx, type) => {
  const defaultList = [
    {
      title: '资产ID',
      dataIndex: 'assetNo',
      type: ['snDevice'],
      render: assetNo => {
        if (!assetNo) {
          return BLANK_PLACEHOLDER;
        }
        return <SpaceOrDeviceLink id={assetNo} type="DEVICE_ASSET_NO" />;
      },
    },
    {
      title: 'SN',
      dataIndex: 'serialNo',
      type: ['snDevice'],
    },
    {
      title: '设备名称',
      dataIndex: 'deviceName',
      type: ['snDevice'],
    },
    {
      title: '一级分类',
      dataIndex: 'topCategory',
      type: ['snDevice', 'noSnDevice'],
      render: text => getDeviceTypeName(text, ctx.props.normalizedList),
    },
    {
      title: '二级分类',
      dataIndex: 'secondCategory',
      type: ['snDevice', 'noSnDevice'],
      render: text => getDeviceTypeName(text, ctx.props.normalizedList),
    },
    {
      title: '三级分类',
      dataIndex: 'deviceType',
      type: ['snDevice', 'noSnDevice'],
      render: text => getDeviceTypeName(text, ctx.props.normalizedList),
    },
    {
      title: '位置',
      dataIndex: 'roomGuid',
      type: ['snDevice', 'noSnDevice'],
    },
    {
      title: '厂商',
      dataIndex: 'vendor',
      type: ['snDevice', 'noSnDevice'],
    },
    {
      title: '型号',
      dataIndex: 'productModel',
      type: ['snDevice', 'noSnDevice'],
    },
    {
      title: '盘点状态',
      dataIndex: 'deviceStatus',
      type: ['snDevice', 'noSnDevice'],
      render: deviceStatus => INVENTORY_STATUS_TYPE_VALUE_MAP[deviceStatus].label,
    },
    {
      title: '盘点数量',
      dataIndex: 'inventoryCount',
      type: ['noSnDevice'],
    },
    {
      title: '备注',
      dataIndex: 'remark',
      type: ['snDevice', 'noSnDevice'],
    },
    {
      title: '盘点工单',
      dataIndex: 'taskNo',
      fixed: 'right',
      type: ['snDevice', 'noSnDevice'],
      render: (text, record) => (
        <Link
          to={urls.generateTicketDetailUrl({
            ticketType: 'inventory',
            id: text,
          })}
          onClick={() => {
            ctx.fetchCard(record);
          }}
        >
          {text}
        </Link>
      ),
    },
  ];
  return defaultList.filter(item => item.type.includes(type));
};

export class InventoryAnalyticsPage extends Component {
  state = {
    hiddenTreeNodeKeys: [],
    type: 'snDevice',
    activeKey: 'ALL',
  };

  componentDidMount() {
    const { pageCondition } = this.props;
    const { type } = this.state;
    this.props.syncCommonData({
      strategy: { citiesTree: 'IF_NULL', space: 'IF_NULL', deviceCategory: 'IF_NULL' },
    });
    this.props.fetchInventoryAnalyticsPage({ params: { ...pageCondition }, type });
    this.props.fetchInventoryAnalyticsCount({ params: {}, type });
    this.getHiddenTreeNodeKeys();
  }

  componentDidUpdate(prevProps) {
    if (prevProps.normalizedTreeList !== this.props.normalizedTreeList) {
      this.getHiddenTreeNodeKeys();
    }
  }

  fetchCard = async record => {
    const { response, error } = await taskCenterService.fetchInventoryCard({
      taskNo: record.taskNo,
    });
    if (error) {
      message.error(error);
      return;
    }
    this.props.setInventoryCheckItem(
      response.data.filter(item => item.roomGuid === record.roomGuid)[0]
    );
  };

  onChangePage = (pageNum, pageSize) => {
    const { pageCondition } = this.props;
    this.props.fetchInventoryAnalyticsPage({
      params: { ...pageCondition, pageNum, pageSize },
      type: this.state.type,
    });
  };

  filterList = card => {
    const { pageCondition } = this.props;
    this.setState({ activeKey: card.key });
    this.props.fetchInventoryAnalyticsPage({
      params: {
        ...pageCondition,
        pageNum: 1,
        pageSize: 10,
        countStatus: card.key,
      },
      type: this.state.type,
    });
  };

  handleSearch = () => {
    const { type, activeKey } = this.state;
    const data = this.getFormData();
    this.props.fetchInventoryAnalyticsPage({
      params: {
        pageNum: 1,
        pageSize: 10,
        countStatus: activeKey,
        ...data,
      },
      type,
    });
    this.props.fetchInventoryAnalyticsCount({
      params: { ...data, countStatus: activeKey },
      type,
    });
  };

  getFormData = () => {
    let data = this.props.form.getFieldsValue();
    const { normalizedTreeList } = this.props;
    let blockGuidList = [];
    if (data.blockGuidList) {
      //第一次遍历将楼栋数据保存，机房降级楼栋，区域降级机房[数据格式，区域：EC, 机房：EC01, 楼栋：EC01.A]
      data.blockGuidList.forEach(item => {
        if (item.split('.').length === 2) {
          blockGuidList = [...blockGuidList, item];
          return;
        }
        for (let i in normalizedTreeList) {
          if (normalizedTreeList[i].parentCode === item) {
            blockGuidList = [...blockGuidList, normalizedTreeList[i].metaCode];
          }
        }
      });
      //第二次遍历将机房降级楼栋
      blockGuidList.forEach(item => {
        if (item.split('.').length === 2) {
          return;
        }
        for (let i in normalizedTreeList) {
          if (normalizedTreeList[i].parentCode === item) {
            blockGuidList = [...blockGuidList, normalizedTreeList[i].metaCode];
          }
        }
      });
    }
    blockGuidList = blockGuidList
      .map(item => {
        if (item.split('.').length !== 2) {
          return false;
        }
        return item;
      })
      .filter(Boolean);
    const params = Object.keys(data).reduce((map, fieldName) => {
      const value = data[fieldName];
      if ([null, undefined, ''].includes(value) || (Array.isArray(value) && value.length === 0)) {
        return map;
      } else if (fieldName === 'vendorModel') {
        map.vendor = value[0];
        map.productModel = value[1];
      } else if (fieldName === 'blockGuidList') {
        map.blockGuidList = blockGuidList;
      } else if (fieldName === 'deviceType') {
        map.topCategoryList = value.firstCategoryCode && [value.firstCategoryCode];
        map.secondCategoryList = value.secondCategoryCode && [value.secondCategoryCode];
        map.deviceTypeList = value.thirdCategorycode && [value.thirdCategorycode];
      } else {
        map[fieldName] = trim(value);
      }
      return map;
    }, {});
    return params;
  };

  handleReset = () => {
    this.props.form.resetFields();
    const { type } = this.state;
    this.props.clearInventoryAnalyticsList();
    this.props.fetchInventoryAnalyticsPage({ params: { pageNum: 1, pageSize: 10 }, type });
    this.props.fetchInventoryAnalyticsCount({ params: {}, type });
    this.setState({ activeKey: 'ALL' });
  };

  getHiddenTreeNodeKeys = () => {
    const { normalizedTreeList } = this.props;
    let keys = [];
    let idcKeys = [];
    let regionKeys = [];
    for (let i in normalizedTreeList) {
      if (normalizedTreeList[i].metaType === 'BLOCK') {
        idcKeys = [...idcKeys, normalizedTreeList[i].parentCode];
      }
      if (normalizedTreeList[i].metaType === 'REGION' || normalizedTreeList[i].metaType === 'IDC') {
        keys = [...keys, i];
      }
    }
    idcKeys = uniq(idcKeys);
    idcKeys.forEach(item => (regionKeys = [...regionKeys, normalizedTreeList[item].parentCode]));
    regionKeys = uniq(regionKeys);
    this.setState({
      hiddenTreeNodeKeys: difference(keys, [...idcKeys, ...regionKeys]),
    });
  };

  onChange = type => {
    this.setState({ type: type }, () => {
      this.handleReset();
    });
  };

  render() {
    const { space, inventoryAnalyticsPage, pageCondition, countList, form } = this.props;
    const { getFieldDecorator } = form;
    const { hiddenTreeNodeKeys, type } = this.state;

    return (
      <GutterWrapper mode="vertical">
        <TinyCard>
          <Radio.Group value={type} onChange={e => this.onChange(e.target.value)}>
            <Radio.Button value={'snDevice'}>有编号资产</Radio.Button>
            <Radio.Button value={'noSnDevice'}>无编号资产</Radio.Button>
          </Radio.Group>
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit,250px)',
              gridColumnGap: '15px',
              gridRowGap: '15px',
              margin: '10px 0',
            }}
          >
            {INVENTORY_COUNT_STATUS_OPTIONS.map(
              item =>
                item.value.type.includes(type) && (
                  <Button
                    key={item.key}
                    style={{
                      borderRadius: '10px',
                      display: 'flex',
                      justifyContent: 'space-between',
                      height: '80px',
                    }}
                    onClick={() => this.filterList(item)}
                  >
                    <GutterWrapper
                      flex
                      mode="vertical"
                      style={{ flexDirection: 'column', padding: '12px' }}
                    >
                      {item.value.label}
                      <span style={{ color: item.value.color, fontSize: '28px' }}>
                        {countList[item.value.count]}
                      </span>
                      <Icon
                        component={item.value.icon}
                        style={{ position: 'absolute', right: '20px', fontSize: '150%' }}
                      />
                    </GutterWrapper>
                  </Button>
                )
            )}
          </div>
          <Form colon={false} layout="inline">
            <Form.Item label="位置">
              {getFieldDecorator('blockGuidList')(
                <ApiTreeSelect
                  maxTagCount={1}
                  multiple
                  treeCheckable
                  showCheckedStrategy={'SHOW_PARENT'}
                  dataService={() => {
                    return Promise.resolve(space);
                  }}
                  fieldNames={{ value: 'metaCode', key: 'metaCode', title: 'metaName' }}
                  hiddenTreeNodeKeys={hiddenTreeNodeKeys}
                  hiddenDepth={2}
                  allowClear
                  style={{ width: 180 }}
                />
              )}
            </Form.Item>
            {type === 'snDevice' ? (
              <Form.Item label="资产ID">
                {getFieldDecorator('assetNo')(<Input allowClear />)}
              </Form.Item>
            ) : (
              <Form.Item label="资产分类">
                {getFieldDecorator('deviceType')(
                  <AssetClassificationApiTreeSelect
                    style={{ width: 200 }}
                    dataType={['noSnDevice']}
                    category="allCategoryCode"
                    trigger="onDidMount"
                  />
                )}
              </Form.Item>
            )}
            <Form.Item label="厂商、型号">
              {getFieldDecorator('vendorModel')(
                <VendorModelSelect
                  style={{ width: 200 }}
                  allowClear
                  numbered={type === 'snDevice'}
                />
              )}
            </Form.Item>
            <Form.Item>
              <GutterWrapper style={{ textAlign: 'right', lineHeight: '40px' }}>
                <Button onClick={this.handleSearch} type={'primary'}>
                  搜索
                </Button>
                <Button onClick={this.handleReset}>重置</Button>
              </GutterWrapper>
            </Form.Item>
          </Form>
        </TinyCard>
        <TinyCard>
          <TinyTable
            rowKey="id"
            columns={getColumns(this, type)}
            dataSource={inventoryAnalyticsPage.list}
            loading={inventoryAnalyticsPage.loading}
            scroll={{ x: 'max-content' }}
            pagination={{
              total: inventoryAnalyticsPage.total,
              current: pageCondition.pageNum,
              pageSize: pageCondition.pageSize,
              onChange: this.onChangePage,
            }}
          />
        </TinyCard>
      </GutterWrapper>
    );
  }
}
const mapStateToProps = ({
  common: { deviceCategory, citiesTree },
  'resource.spaces': { entities, codes },
  inventoryAnalytics: {
    inventoryAnalyticsPage,
    inventoryAnalyticsPageCondition,
    countList,
    searchValues,
  },
}) => {
  let treeData = {
    treeList: [],
    parallelList: [],
    normalizedList: {},
  };
  if (codes) {
    treeData = selectRegionSpaces(
      citiesTree,
      codes.map(code => entities[code])
    );
  }
  return {
    space: treeData,
    normalizedList: deviceCategory?.normalizedList,
    treeList: treeData.treeList,
    normalizedTreeList: treeData.normalizedList,
    pageCondition: inventoryAnalyticsPageCondition,
    inventoryAnalyticsPage,
    countList,
    ...searchValues,
  };
};
const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
  fetchInventoryAnalyticsPage,
  fetchInventoryAnalyticsCount,
  updateSearchValues: inventoryAnalyticsActions.updateSearchValues,
  clearInventoryAnalyticsList: inventoryAnalyticsActions.clearInventoryAnalyticsList,
  setInventoryCheckItem: ticketActions.setInventoryCheckItem,
};
export default connect(
  mapStateToProps,
  mapDispatchToProps
)(
  Form.create({
    name: 'search_analytics',
    onFieldsChange(props, changedFields) {
      props.updateSearchValues(changedFields);
    },
    mapPropsToFields(props) {
      return {
        blockGuidList: Form.createFormField(props.blockGuidList),
        assetNo: Form.createFormField(props.assetNo),
        vendorModel: Form.createFormField(props.vendorModel),
        deviceType: Form.createFormField(props.deviceType),
      };
    },
  })(InventoryAnalyticsPage)
);

function selectRegionSpaces(cities, spaces) {
  let region = [];
  (cities || []).forEach(city => {
    region = region.concat(
      (city.children || []).map(item => ({
        metaCode: item.value,
        metaName: item.label,
        metaType: 'REGION',
        parentCode: null,
      }))
    );
  });
  const parallelList = [
    ...region,
    ...spaces.map(space => ({
      ...space,
      metaCode: space.code,
      metaName: space.name,
      metaType: space.type,
    })),
  ];
  const treeList = generateTreeData(parallelList, {
    key: 'metaCode',
    typeKey: 'metaType',
    parentKey: 'parentCode',
    nodeTypes: ['REGION', 'IDC', 'BLOCK'],
    getNode: data => {
      let node = {};
      if (data.metaType === 'IDC') {
        node = {
          ...data,
          label: data.metaCode,
          value: data.metaCode,
        };
      }
      node = {
        ...data,
        label: data.metaName,
        value: data.metaCode,
      };
      return node;
    },
  });
  let normalizedList = {};
  parallelList.forEach(space => {
    normalizedList[space.metaCode] = space;
  });
  return { normalizedList, parallelList, treeList };
}
