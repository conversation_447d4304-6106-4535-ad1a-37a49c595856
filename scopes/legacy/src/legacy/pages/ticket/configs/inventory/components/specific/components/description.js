import React, { Component } from 'react';
import { connect } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Tag } from '@manyun/base-ui.ui.tag';

import { User } from '@manyun/auth-hub.ui.user';

import { GutterWrapper, TinyDescriptions, TinyModal } from '@manyun/dc-brain.legacy.components';
import {
  INVENTORY_SUB_TYPE_TEXT_MAP,
  INVENTORY_TARGET_TYPE_KEY_MAP,
  INVENTORY_TYPE_TEXT_MAP,
} from '@manyun/dc-brain.legacy.constants/inventory';
import { ticketInventoryTaskConfigActionCreator } from '@manyun/dc-brain.legacy.redux/actions/ticketActions';
import * as taskCenterService from '@manyun/dc-brain.legacy.services/taskCenterService';
import { getDeviceTypeName, getTaskProperties } from '@manyun/dc-brain.legacy.utils/ticket';

export class InventoryDescription extends Component {
  state = {
    roomGuid: '',
    roomTypes: [],
    visible: false,
    specificData: null,
  };

  componentDidMount() {
    this.props.selectedCard && this.fetchSpecificInventoryTarget();
  }

  componentDidUpdate(perProps) {
    const { basicInfo, fetchInventoryTaskConfig } = this.props;
    if (Object.keys(perProps.basicInfo).length === 0 && Object.keys(basicInfo).length !== 0) {
      fetchInventoryTaskConfig({ taskNo: basicInfo.taskNo });
    }
  }

  changeVisible = () => {
    this.setState({ visible: !this.state.visible });
  };

  getDescriptionItem = () => {
    const { basicInfo } = this.props;
    if (Object.keys(basicInfo).length === 0) {
      return;
    }
    const properties = getTaskProperties(basicInfo);
    const items = [
      {
        label: '盘点方式',
        value: (
          <span>
            {INVENTORY_TYPE_TEXT_MAP[basicInfo?.taskSubType]}
            {properties?.inventorySubType
              ? `-${INVENTORY_SUB_TYPE_TEXT_MAP[properties?.inventorySubType]}`
              : ''}
          </span>
        ),
      },
      {
        label: '盘点目标',
        value: (
          <Button compact type="link" onClick={this.changeVisible}>
            查看
          </Button>
        ),
      },
    ];
    if (basicInfo?.assigneeList.length) {
      items.push({
        label: '指派人',
        value: (
          <GutterWrapper>
            {basicInfo.assigneeList.map(item => (
              <User.Link id={item.id} name={item.userName} />
            ))}
          </GutterWrapper>
        ),
      });
    }
    return items;
  };

  fetchInventoryTarget = () => {
    const { basicInfo, normalizedList, targetConfig } = this.props;
    const { specificData } = this.state;
    if (Object.keys(basicInfo).length === 0) {
      return;
    }
    let properties = getTaskProperties(basicInfo);
    properties = specificData
      ? { inventoryTargetType: properties.inventoryTargetType, targetTags: specificData }
      : { ...properties, ...targetConfig };

    return (
      <GutterWrapper mode="vertical">
        {properties?.inventoryTargetType === INVENTORY_TARGET_TYPE_KEY_MAP.DEVICE_TYPE && (
          <GutterWrapper mode="horizontal">
            资产分类:
            <div>
              {properties.targetTags.map(item => (
                <Tag key={item} style={{ margin: '4px' }}>
                  {getDeviceTypeName(item, normalizedList)}
                </Tag>
              ))}
            </div>
          </GutterWrapper>
        )}
        {properties?.limitRoom && (
          <GutterWrapper mode="horizontal">
            包间号:
            <div>
              {properties.roomTags.map(item => (
                <Tag key={item} style={{ margin: '4px' }}>
                  {item}
                </Tag>
              ))}
            </div>
          </GutterWrapper>
        )}
        {properties?.inventoryTargetType === INVENTORY_TARGET_TYPE_KEY_MAP.ASSET_NO && (
          <GutterWrapper mode="horizontal">
            {'资产ID:'}
            <div>
              {properties.targetTags.map(item => (
                <Tag key={item} style={{ margin: '4px' }}>
                  {item}
                </Tag>
              ))}
            </div>
          </GutterWrapper>
        )}
      </GutterWrapper>
    );
  };

  fetchSpecificInventoryTarget = async () => {
    const { basicInfo, selectedCard } = this.props;
    if (Object.keys(basicInfo).length === 0) {
      return;
    }
    const properties = getTaskProperties(basicInfo);
    const { response, error } = await taskCenterService.fetchInventoryCardTarget({
      roomGuid: selectedCard.roomGuid,
      taskNo: basicInfo.taskNo,
      inventoryTargetType: properties.inventoryTargetType,
    });
    if (error) {
      message.error(error);
      return;
    }
    this.setState({ specificData: response.data });
  };

  render() {
    const { visible } = this.state;

    return (
      <GutterWrapper mode="vertical" padding="0">
        <TinyDescriptions
          key="description"
          column={4}
          descriptionsItems={this.getDescriptionItem()}
        />
        <TinyModal
          title="盘点目标"
          key="modal"
          visible={visible}
          footer={null}
          onCancel={this.changeVisible}
          onClose={this.changeVisible}
        >
          {this.fetchInventoryTarget()}
        </TinyModal>
      </GutterWrapper>
    );
  }
}

const mapStateToProps = ({ common: { deviceCategory }, ticket: { ticketView, inventory } }) => ({
  normalizedList: deviceCategory?.normalizedList,
  basicInfo: ticketView?.basicInfo,
  targetConfig: inventory.detail.targetConfig,
});
const mapDispatchToProps = {
  fetchInventoryTaskConfig: ticketInventoryTaskConfigActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(InventoryDescription);
