import uniqBy from 'lodash/uniqBy';
import React, { Component } from 'react';
import { connect } from 'react-redux';

import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Input } from '@manyun/base-ui.ui.input';
import { Space } from '@manyun/base-ui.ui.space';
import { Tag } from '@manyun/base-ui.ui.tag';

import { DisplayCard, Ellipsis, GutterWrapper } from '@manyun/dc-brain.legacy.components';
import { ticketActions } from '@manyun/dc-brain.legacy.redux/actions/ticketActions';

import { formatTime } from '../../../../util.ts';
import InventoryDescription from '../components/description';

export class InventoryCard extends Component {
  state = {
    cardList: [],
    roomGuid: '',
    roomTypes: [],
    visible: false,
    taskProperties: {},
  };

  async componentDidMount() {
    await this.props.fetchCardList();
    this.setState({ roomTypes: this.props.roomTypes });
  }

  componentDidUpdate(perProps) {
    const taskStatus = this.props.basicInfo.taskStatus;
    if (perProps.roomTypes !== this.props.roomTypes) {
      this.setState({ roomTypes: this.props.roomTypes });
    }
    if (
      perProps.basicInfo.taskStatus !== taskStatus &&
      (taskStatus === '0' || taskStatus === '1')
    ) {
      this.props.fetchCardList();
    }
  }

  handleChangeCheckableTag = values => {
    this.setState({ roomTypes: values });
  };

  getList = () => {
    const { cardList } = this.props;
    const { roomGuid, roomTypes } = this.state;
    let list = [...cardList];
    if (roomTypes.length) {
      list = list.filter(room => roomTypes.includes(room.roomType));
    } else {
      list = [];
    }
    if (roomGuid) {
      list = list.filter(room => room.roomTag.toLowerCase().includes(roomGuid.toLowerCase()));
    }
    return list;
  };

  render() {
    const { roomTypesMappings, cardList } = this.props;
    const { roomTypes } = this.state;

    const typeFilterList = uniqBy(
      cardList.map(item => {
        return { value: item.roomType, label: roomTypesMappings?.[item.roomType] };
      }),
      'value'
    );
    const showList = this.getList();

    return (
      <Space style={{ width: '100%' }} direction="vertical" size={0}>
        <InventoryDescription />
        <Space style={{ width: '100%' }} direction="vertical" size={16}>
          {cardList.length > 0 && (
            <Space>
              <Checkbox.Group
                options={typeFilterList}
                value={roomTypes}
                onChange={this.handleChangeCheckableTag}
              />
              <Input.Search
                placeholder="请输入包间编号"
                style={{ width: '200px' }}
                onSearch={value => this.setState({ roomGuid: value })}
              />
            </Space>
          )}
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fill,minmax(244px,1fr))',
              gridRowGap: '58px',
              gridColumnGap: '16px',
            }}
          >
            {showList.map(card => (
              <DisplayCard
                key={card.roomTag}
                title={
                  <Ellipsis tooltip lines={1}>
                    {card.roomTag} {roomTypesMappings?.[card.roomType]}
                  </Ellipsis>
                }
                extra={
                  <Tag style={{ margin: 0 }} color={card.finished ? 'green' : 'red'}>
                    {card.finished ? '已完成' : '未完成'}
                  </Tag>
                }
                describe={formatTime(card.inventoryTime)}
                subDescribe={
                  <GutterWrapper size=".5em" mode="vertical">
                    盘点时长
                  </GutterWrapper>
                }
                countList={[
                  {
                    describe: '异常数',
                    number: card.exceptionCount,
                    color: 'error',
                  },
                  {
                    describe: '待盘点',
                    number: card.unInventoryCount,
                    color: 'warning',
                  },
                  { describe: '已盘点', number: card.inventoryCount, color: 'success' },
                ]}
                onClick={() => {
                  this.props.setInventoryCheckItem(card);
                }}
              />
            ))}
          </div>
        </Space>
      </Space>
    );
  }
}

const mapStateToProps = ({
  ticket: {
    inventory: { detail },
    ticketView,
  },
  common: { deviceCategory, roomTypes },
}) => ({
  cardList: detail.cardList,
  roomTypes: detail.roomTypes,
  basicInfo: ticketView?.basicInfo,
  roomTypesMappings: roomTypes,
  normalizedList: deviceCategory?.normalizedList,
});
const mapDispatchToProps = {
  setInventoryCheckItem: ticketActions.setInventoryCheckItem,
};
export default connect(mapStateToProps, mapDispatchToProps)(InventoryCard);
