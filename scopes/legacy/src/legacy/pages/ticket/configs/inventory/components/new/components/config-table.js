import React, { useCallback, useEffect, useState } from 'react';

import uniq from 'lodash/uniq';

import { message } from '@manyun/base-ui.ui.message';

import { TinyTable } from '@manyun/dc-brain.legacy.components';
import * as taskCenterService from '@manyun/dc-brain.legacy.services/taskCenterService';

export function DataTable({
  inventoryType,
  inventorySubType,
  selectedConfigRowKeys,
  setSelectedConfigs,
}) {
  const [pagination, setPagination] = useState({ pageNum: 1, pageSize: 10 });
  const [pageData, setPageData] = useState([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    (async () => {
      setLoading(true);
      const { response, error } = await taskCenterService.fetchInventoryConfigData({
        inventoryType,
        inventorySubType,
        ...pagination,
      });
      setLoading(false);
      if (error) {
        message.error(error);
        return;
      }
      setPageData(response.data);
      setTotal(response.total);
    })();
  }, [inventoryType, inventorySubType, pagination]);

  const paginationChangeHandler = useCallback((current, size) => {
    setPagination({ pageNum: current, pageSize: size });
  }, []);

  return (
    <TinyTable
      rowKey="id"
      loading={loading}
      columns={getColumns()}
      align="left"
      dataSource={pageData}
      rowSelection={{
        selectedRowKeys: selectedConfigRowKeys,
        onChange: (keys, rows) => {
          if (!rows.length) {
            setSelectedConfigs([], []);
            return;
          }
          const deviceTypes = [];
          rows.forEach(item => {
            deviceTypes.push(...item.deviceTypes);
          });
          const selectedRows = uniq(deviceTypes).map(item => {
            return {
              key: item + '-C2',
              computedNodeProps: {
                parentKeys: [item.slice(0, 1), item.slice(0, 3)],
              },
            };
          });
          setSelectedConfigs(keys, selectedRows);
        },
      }}
      pagination={{
        total,
        current: pagination.pageNum,
        pageSize: pagination.pageSize,
        onChange: paginationChangeHandler,
      }}
    />
  );
}

export default DataTable;

const getColumns = () => [
  {
    title: '盘点项配置名称',
    dataIndex: 'configName',
  },
  {
    title: '盘点方式',
    dataIndex: ['inventoryType', 'desc'],
  },
  {
    title: '盘点子方式',
    dataIndex: ['inventorySubType', 'desc'],
  },
  {
    title: '盘点对象数',
    dataIndex: 'deviceTypes',
    render: deviceTypes => deviceTypes.length,
  },
];
