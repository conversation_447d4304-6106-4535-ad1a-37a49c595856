import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import debounce from 'lodash/debounce';
import trim from 'lodash/trim';
import uniqBy from 'lodash/uniqBy';

import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';

import { GutterWrapper, TinyModal, TinyTable } from '@manyun/dc-brain.legacy.components';
import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';
import * as deviceService from '@manyun/dc-brain.legacy.services/deviceService';
import { getDeviceTypeName } from '@manyun/dc-brain.legacy.utils/ticket';

const getColumns = ctx => [
  {
    title: '资产ID',
    dataIndex: 'assetNo',
    render: txt => txt || BLANK_PLACEHOLDER,
  },
  {
    title: '一级分类',
    dataIndex: 'topCategory',
    render: text => getDeviceTypeName(text, ctx.props.normalizedList),
  },
  {
    title: '二级分类',
    dataIndex: 'secondCategory',
    render: text => getDeviceTypeName(text, ctx.props.normalizedList),
  },
  {
    title: '三级分类',
    dataIndex: 'deviceType',
    render: text => getDeviceTypeName(text, ctx.props.normalizedList),
  },
  {
    title: '厂商',
    dataIndex: 'vendor',
  },
  {
    title: '型号',
    dataIndex: 'productModel',
  },
  {
    title: '操作',
    dataIndex: 'action',
    render: (__, record) => (
      <Button compact type="link" onClick={() => ctx.handleDelete(record.assetNo)}>
        移除
      </Button>
    ),
  },
];

class AddAssetConfig extends Component {
  state = {
    assetList: [],
    assetNoList: [],
    visible: false,
    text: '',
  };

  componentDidUpdate(prevProps) {
    const { roomGuids, location } = this.props;
    if (
      (location && location !== prevProps.location) ||
      (roomGuids && roomGuids !== prevProps.roomGuids)
    ) {
      this.setAssetList([]);
    }
  }

  handleAdd = async () => {
    const { assetNoList, assetList } = this.state;
    const { location, roomGuids } = this.props;
    const q = {
      idcTag: location[0],
      blockTag: location[1],
      assetNoList,
    };
    if (roomGuids.length) {
      q.roomGuidList = roomGuids;
    }
    const { response, error } = await deviceService.fetchDevicesByTypes(q);
    if (error) {
      message.error(error);
      return;
    }
    if (!response.data.length) {
      this.setState({
        text: (roomGuids.length ? '当前指定包间不存在' : '当前位置不存在') + assetNoList.join('、'),
      });
      this.changeVisible();
      return;
    }
    const resultData = response.data.map(item => item.assetNo);
    const lostData = assetNoList.filter(item => !resultData.includes(item));
    if (lostData.length) {
      this.setState({
        text: (roomGuids.length ? '当前指定包间不存在' : '当前位置不存在') + lostData.join('、'),
      });
      this.changeVisible();
    }
    this.setAssetList(uniqBy([...assetList, ...response.data], 'assetNo'));
  };

  handleDelete = key => {
    const { assetList } = this.state;
    this.setAssetList(key ? assetList.filter(item => item.assetNo !== key) : []);
  };

  debouncedHandle = debounce(e => {
    if (e === '') {
      return;
    }
    this.setState({
      assetNoList: trim(e.target.value)
        .split(' ')
        .map(item => {
          if (item === ' ') {
            return false;
          }
          return item;
        })
        .filter(Boolean),
    });
  }, 500);

  changeVisible = () => {
    this.setState({ visible: !this.state.visible });
  };

  setAssetList = data => {
    const { setTargetTags } = this.props;
    this.setState({ assetList: data }, () => {
      setTargetTags(this.state.assetList.map(item => item.assetNo));
    });
  };

  render() {
    const { assetList, assetNoList, visible, text } = this.state;
    const { location } = this.props;

    return (
      <GutterWrapper mode="vertical">
        <TinyTable
          style={{ marginBottom: 40 }}
          rowKey="assetNo"
          columns={getColumns(this)}
          dataSource={assetList}
          actions={[
            <Input
              key="text"
              placeholder={'请输入资产ID'}
              allowClear
              onChange={e => {
                e.persist();
                this.debouncedHandle(e);
              }}
              style={{ width: 512 }}
            />,
            <Button
              key="add"
              type="primary"
              onClick={this.handleAdd}
              disabled={!assetNoList.length || location?.length !== 2}
            >
              添加
            </Button>,
          ]}
        />
        <TinyModal
          title="提示"
          visible={visible}
          footer={
            <Button onClick={this.changeVisible} type="primary">
              确定
            </Button>
          }
          onCancel={this.changeVisible}
        >
          {<span style={{ whiteSpace: 'pre-wrap' }}>{text}</span>}
        </TinyModal>
      </GutterWrapper>
    );
  }
}
const mapStateToProps = ({ common: { deviceCategory } }) => ({
  normalizedList: deviceCategory?.normalizedList,
});
export default connect(mapStateToProps)(Form.create({ name: 'create-in' })(AddAssetConfig));
