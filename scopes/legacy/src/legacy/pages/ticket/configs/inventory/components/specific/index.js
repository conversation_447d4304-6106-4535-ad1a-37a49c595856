import React, { Component } from 'react';
import { connect } from 'react-redux';

import { message } from '@manyun/base-ui.ui.message';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import { ticketActions } from '@manyun/dc-brain.legacy.redux/actions/ticketActions';
import * as taskCenterService from '@manyun/dc-brain.legacy.services/taskCenterService';

import InventoryCard from './components/card';
import InventoryCardDetail from './components/card-detail';

class SpecificInventory extends Component {
  componentDidMount() {
    this.props.syncCommonData({
      strategy: { space: 'IF_NULL', deviceCategory: 'IF_NULL', roomTypes: 'IF_NULL' },
    });
    // this.fetchCardList();
  }

  componentWillUnmount() {
    this.props.setInventoryCheckItem([]);
  }

  fetchCardList = async () => {
    const { taskNo } = this.props;
    const { response, error } = await taskCenterService.fetchInventoryCard({
      taskNo,
    });
    if (error) {
      message.error(error);
      return;
    }
    this.props.setInventoryRoomInfo(response.data);
  };

  render() {
    const { taskNo, checkItem } = this.props;

    return (
      <GutterWrapper>
        {checkItem.length === 0 ? (
          <InventoryCard taskNo={taskNo} fetchCardList={this.fetchCardList} />
        ) : (
          <InventoryCardDetail fetchCardList={this.fetchCardList} />
        )}
      </GutterWrapper>
    );
  }
}

const mapStateToProps = ({
  ticket: {
    inventory: { detail },
  },
}) => ({ checkItem: detail.checkItem });
const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
  setInventoryRoomInfo: ticketActions.setInventoryRoomInfo,
  setInventoryCheckItem: ticketActions.setInventoryCheckItem,
};
export default connect(mapStateToProps, mapDispatchToProps)(SpecificInventory);
