import Icon from '@ant-design/icons';
import PageHeader from 'antd/es/page-header';
import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Redirect } from 'react-router-dom';

// Deprecated, replace with "useAuthorized" hook
import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Space } from '@manyun/base-ui.ui.space';
import { UserLink } from '@manyun/iam.ui.user-link';
import { SpaceOrDeviceLink } from '@manyun/resource-hub.ui.space-or-device-link';
import { BackendTaskStatus } from '@manyun/ticket.model.ticket';

import { <PERSON><PERSON><PERSON>rapper, TinyTable } from '@manyun/dc-brain.legacy.components';
import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';
import {
  INVENTORY_STATUS_TYPE_KEY_MAP,
  INVENTORY_STATUS_TYPE_VALUE_MAP,
  INVENTORY_TYPE_KEY_MAP,
} from '@manyun/dc-brain.legacy.constants/inventory';
import { ticketActions } from '@manyun/dc-brain.legacy.redux/actions/ticketActions';
import * as taskCenterService from '@manyun/dc-brain.legacy.services/taskCenterService';
import { getDeviceTypeName, getTaskProperties } from '@manyun/dc-brain.legacy.utils/ticket';

import CompleteButton from '../components/complete-btn';
import InventoryDescription from '../components/description';
import EditModal from '../components/edit-modal';
import SpareModal from '../components/spare-modal';

const getColumns = (ctx, authorized, type) => {
  const list = [
    {
      title: '资产ID',
      dataIndex: 'assetNo',
      type: ['snDevice'],
      render: (assetNo, { deviceGuid }) => {
        if (!assetNo) {
          return BLANK_PLACEHOLDER;
        }
        return <SpaceOrDeviceLink id={deviceGuid} type="DEVICE_GUID" text={assetNo} />;
      },
    },
    {
      title: 'SN',
      dataIndex: 'serialNo',
      type: ['snDevice'],
    },
    {
      title: '设备名称',
      dataIndex: 'deviceName',
      type: ['snDevice'],
    },
    {
      title: '三级分类',
      dataIndex: 'deviceType',
      type: ['snDevice', 'noSnDevice'],
      render: (__, record) => getDeviceTypeName(record.deviceType, ctx.props.normalizedList),
    },
    {
      title: '货架',
      dataIndex: 'shelves',
      type: ['noSnDevice'],
      render: text => text ?? '--',
    },
    {
      title: '厂商',
      dataIndex: 'vendor',
      type: ['snDevice', 'noSnDevice'],
    },
    {
      title: '型号',
      dataIndex: 'productModel',
      type: ['snDevice', 'noSnDevice'],
    },
    {
      title: '处理人',
      dataIndex: 'modifierId',
      type: ['snDevice', 'noSnDevice'],
      render: (text, record) => {
        return text && <UserLink userId={text} />;
      },
    },
    {
      title: '盘点数量',
      dataIndex: 'inventoryCount',
      type: ['noSnDevice'],
    },
    {
      title: '盘点状态',
      dataIndex: 'deviceStatus',
      type: ['snDevice', 'noSnDevice'],
      render: deviceStatus => (
        <Space size={4}>
          <Icon component={INVENTORY_STATUS_TYPE_VALUE_MAP[deviceStatus].icon} />
          {INVENTORY_STATUS_TYPE_VALUE_MAP[deviceStatus].label}
        </Space>
      ),
    },
    {
      title: '备注',
      dataIndex: 'remark',
      type: ['snDevice', 'noSnDevice'],
    },
    {
      title: '操作',
      dataIndex: 'action',
      type: ['snDevice', 'noSnDevice'],
      fixed: 'right',
      render: (__, record) => {
        if (record.finishTime) {
          return BLANK_PLACEHOLDER;
        }
        return type === 'snDevice' ? (
          <EditModal
            text={ctx.getActionType(record.deviceStatus, type)}
            title={ctx.getActionType(record.deviceStatus, type)}
            type="link"
            info={record}
            fetchDeviceList={ctx.fetchDeviceList}
          />
        ) : (
          <SpareModal
            text={ctx.getActionType(record.deviceStatus, type)}
            title={ctx.getActionType(record.deviceStatus, type)}
            type="link"
            info={record}
            fetchDeviceList={ctx.fetchDeviceList}
          />
        );
      },
    },
  ];
  const { basicInfo, selectedCard } = ctx.props;
  if (
    basicInfo?.taskStatus !== BackendTaskStatus.PROCESSING ||
    !authorized ||
    selectedCard.finished ||
    basicInfo?.taskSubType === INVENTORY_TYPE_KEY_MAP.AUTO_INVENTORY
  ) {
    list.splice(-1, 1);
  }
  return list.filter(item => item.type.includes(type));
};

export class InventoryCardDetail extends Component {
  state = {
    deviceList: [],
    onlyException: false,
    count: 0,
    pageNum: 1,
    pageSize: 10,
    total: 0,
    type: 'snDevice',
    shelves: '',
  };
  componentDidMount() {
    this.fetchDeviceList(1, 10);
  }

  componentDidUpdate(perProps) {
    const taskStatus = this.props.basicInfo.taskStatus;
    if (
      perProps.basicInfo.taskStatus !== taskStatus &&
      (taskStatus === '0' || taskStatus === '1')
    ) {
      this.fetchDeviceList(1, 10);
      this.props.fetchCardList();
    }
  }

  fetchExceptionCount = async () => {
    const { basicInfo, selectedCard } = this.props;
    const { type } = this.state;
    const { response, error } = await taskCenterService.fetchInventoryExceptionCount({
      roomGuid: selectedCard.roomGuid,
      taskNo: basicInfo.taskNo,
      numbered: type === 'snDevice',
    });
    if (error) {
      message.error(error);
      return;
    }
    this.setState({ count: response });
  };

  fetchDeviceList = async (pageNum, pageSize) => {
    const { basicInfo, selectedCard } = this.props;
    const { onlyException, type, shelves } = this.state;
    const properties = getTaskProperties(basicInfo);
    const { response, error } = await taskCenterService.fetchInventoryDevice({
      onlyException,
      pageNum,
      pageSize,
      roomGuid: selectedCard.roomGuid,
      taskNo: basicInfo.taskNo,
      inventorySubType: properties.inventorySubType,
      numbered: type === 'snDevice',
      shelves,
    });
    if (error) {
      message.error(error);
      return;
    }
    this.setState({
      deviceList: response.data,
      total: response.total,
      pageNum,
      pageSize,
    });
    this.fetchExceptionCount();
  };

  handleDelete = async assetNo => {
    const { basicInfo } = this.props;
    const { error } = await taskCenterService.removeInventoryDevice({
      taskNo: basicInfo.taskNo,
      assetNo,
    });
    if (error) {
      message.error(error);
      return;
    }
    this.fetchDeviceList(1, 10);
  };

  onChangePage = (pageNum, pageSize) => {
    this.fetchDeviceList(pageNum, pageSize);
  };

  getActionType = (deviceStatus, type) => {
    if (type === 'snDevice') {
      switch (deviceStatus) {
        case INVENTORY_STATUS_TYPE_KEY_MAP.INVENTORY_LOSS:
        case INVENTORY_STATUS_TYPE_KEY_MAP.UN_INVENTORY:
          return '盘亏说明';
        case INVENTORY_STATUS_TYPE_KEY_MAP.INVENTORY_ERROR:
        case INVENTORY_STATUS_TYPE_KEY_MAP.INVENTORY_NORMAL:
          return '纠正';
        case INVENTORY_STATUS_TYPE_KEY_MAP.INVENTORY_EXCEED:
          return '盘盈说明';
        default:
          return;
      }
    }
    switch (deviceStatus) {
      case INVENTORY_STATUS_TYPE_KEY_MAP.UN_INVENTORY:
        return '录入数量';
      default:
        return '修改数量';
    }
  };

  onChange = type => {
    this.setState({ type: type, shelves: '' }, () => {
      this.fetchDeviceList(1, 10);
    });
  };

  onSearch = value => {
    this.setState({ shelves: value }, () => {
      this.fetchDeviceList(1, 10);
    });
  };

  render() {
    const { basicInfo, selectedCard, roomTypes } = this.props;
    const { pageNum, pageSize, total, count, deviceList, type, shelves } = this.state;

    return (
      <Space style={{ width: '100%' }} direction="vertical" size={0}>
        <InventoryDescription selectedCard={selectedCard} />
        <Space style={{ width: '100%' }} direction="vertical" size={16}>
          <PageHeader
            style={{ padding: 0 }}
            title={`${selectedCard.roomTag} ${roomTypes[selectedCard.roomType]}`}
            onBack={() => {
              this.props.setInventoryCheckItem([]);
            }}
          />
          <RenderAuthorizedTable userId={basicInfo?.taskAssignee}>
            {authorized =>
              authorized && (
                <CompleteButton
                  selectedCard={selectedCard}
                  fetchDeviceList={this.fetchDeviceList}
                />
              )
            }
          </RenderAuthorizedTable>
          <Space size={24}>
            <Radio.Group value={type} onChange={e => this.onChange(e.target.value)}>
              <Radio.Button value="snDevice">有编号资产</Radio.Button>
              <Radio.Button value="noSnDevice">无编号资产</Radio.Button>
            </Radio.Group>
            {type === 'noSnDevice' && (
              <Input.Search
                style={{ width: 216 }}
                placeholder="搜索货架"
                value={shelves}
                onSearch={this.onSearch}
                onChange={({ target: { value } }) => {
                  this.setState({ shelves: value });
                }}
              />
            )}
          </Space>
          {count !== 0 ? (
            <GutterWrapper mode="horizontal" flex>
              <Checkbox
                style={{ lineHeight: '40px' }}
                onChange={e => {
                  this.setState({ onlyException: e.target.checked }, () => {
                    this.fetchDeviceList(1, 10);
                  });
                }}
              >
                只看异常{`(${count})`}
              </Checkbox>
            </GutterWrapper>
          ) : null}
          <RenderAuthorizedTable userId={basicInfo?.taskAssignee}>
            {authorized => {
              return (
                <TinyTable
                  rowKey="deviceGuid"
                  columns={getColumns(this, authorized, type)}
                  dataSource={deviceList}
                  scroll={{ x: true }}
                  align="left"
                  pagination={{
                    total,
                    current: pageNum,
                    pageSize,
                    onChange: this.onChangePage,
                  }}
                />
              );
            }}
          </RenderAuthorizedTable>
        </Space>
      </Space>
    );
  }
}

const mapStateToProps = ({
  ticket: {
    inventory: { detail },
    ticketView,
  },
  common: { deviceCategory, roomTypes },
}) => ({
  selectedCard: detail.checkItem,
  normalizedList: deviceCategory?.normalizedList,
  basicInfo: ticketView?.basicInfo,
  roomTypes: roomTypes || {},
});
const mapDispatchToProps = {
  setInventoryCheckItem: ticketActions.setInventoryCheckItem,
};
export default connect(mapStateToProps, mapDispatchToProps)(InventoryCardDetail);

function RenderAuthorizedTable({ userId, children }) {
  const [, { checkUserId }] = useAuthorized();
  const isCurrentUser = checkUserId(userId);
  if (typeof children == 'function') {
    return children(isCurrentUser);
  }
  return isCurrentUser ? children : <Redirect to={{ pathname: '/403' }} />;
}
