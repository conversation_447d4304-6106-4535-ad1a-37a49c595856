import React, { Component } from 'react';
import { connect } from 'react-redux';

import get from 'lodash/get';
import omit from 'lodash/omit';
import uniq from 'lodash/uniq';
import uniqBy from 'lodash/uniqBy';

import { Button } from '@manyun/base-ui.ui.button';
import { Collapse } from '@manyun/base-ui.ui.collapse';
import { List } from '@manyun/base-ui.ui.list';
import { Tabs } from '@manyun/base-ui.ui.tabs';

import {
  ApiTree,
  GutterWrapper,
  TinyCard,
  TinyModal,
  TinyTable,
} from '@manyun/dc-brain.legacy.components';
import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';
import { DEVICE_SPACE_TYPE_KEY_MAP } from '@manyun/dc-brain.legacy.constants/enums/deviceType';
import {
  INVENTORY_SUB_TYPE_KEY_MAP,
  INVENTORY_TYPE_KEY_MAP,
} from '@manyun/dc-brain.legacy.constants/inventory';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import { flattenTreeData } from '@manyun/dc-brain.legacy.utils';
import { getDeviceTypeName } from '@manyun/dc-brain.legacy.utils/ticket';

const getColumns = ctx => [
  {
    title: '一级分类',
    dataIndex: 'topCategory',
    render: (_, record) =>
      getDeviceTypeName(
        get(record, ['computedNodeProps', 'parentKeys', 0]).split('-')[0],
        ctx.props.normalizedList
      ),
  },
  {
    title: '二级分类',
    dataIndex: 'secondCategory',
    render: (_, record) =>
      getDeviceTypeName(
        get(record, ['computedNodeProps', 'parentKeys', 1]).split('-')[0],
        ctx.props.normalizedList
      ),
  },
  {
    title: '三级分类',
    dataIndex: 'deviceType',
    render: (_, record) => getDeviceTypeName(record.key.split('-')[0], ctx.props.normalizedList),
  },
  {
    title: '操作',
    dataIndex: 'action',
    render: (__, record) =>
      record.tabKey === 'custom' ? (
        <Button
          type="link"
          compact
          onClick={() =>
            ctx.onDeleteChildItem(
              get(record, ['computedNodeProps', 'parentKeys', 0]),
              record.key,
              true
            )
          }
        >
          移除
        </Button>
      ) : (
        BLANK_PLACEHOLDER
      ),
  },
];

const initialState = {
  checkedNodes: [],
  checkedItemKeys: [],
  panelKeys: [],
  activeKey: [],
  childItemsMap: {},
  selectedRowKeys: [],
  selectedRows: [],
  tabKey: 'custom',
  checkedConfigList: [],
  selectedConfigList: [],
  expandedKeys: [],
};

class AddConfig extends Component {
  state = {
    ...initialState,
  };

  componentDidMount() {
    this.props.syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });
  }

  componentDidUpdate(prevProps, prevState) {
    const { checkedConfigList, selectedConfigList, panelKeys, tabKey, modelVisible } = this.state;
    const { inventoryType, inventorySubType } = this.props;
    //盘点方式发生变化时，初始化state数据
    if (inventoryType && prevProps.inventoryType !== inventoryType) {
      this.setState({ ...initialState });
      return;
    }
    //盘点子方式发生变化时，初始化state数据
    if (inventorySubType && prevProps.inventorySubType !== inventorySubType) {
      this.setState({ ...initialState });
      return;
    }
    //盘点方式、盘点子方式未发生变化时，弹窗打开时，初始化部分state数据，剩余数据与累计信息相关
    if (modelVisible && prevState.modelVisible !== modelVisible) {
      this.setState(
        omit(
          initialState,
          'checkedConfigList',
          'selectedConfigList',
          'childItemsMap',
          'selectedRows'
        )
      );
      return;
    }

    if (
      prevState.checkedConfigList !== checkedConfigList ||
      prevState.selectedConfigList !== selectedConfigList
    ) {
      const filterList = [...checkedConfigList, ...selectedConfigList].filter(item =>
        item.key.includes('-C2')
      );
      this.props.setTargetTags(filterList.map(item => item.key.split('-')[0]));
    }
    if (tabKey === 'custom' && panelKeys && panelKeys !== prevState.panelKeys) {
      this.setState({ activeKey: panelKeys });
    }
  }

  editModelVisible = () => {
    this.setState({ modelVisible: !this.state.modelVisible, tabKey: 'custom' });
  };

  handleAdd = () => {
    const { checkedNodes, tabKey, selectedRows, checkedConfigList } = this.state;
    if (tabKey === '1') {
      const filterData = selectedRows?.filter(item =>
        checkedNodes?.findIndex(i => i.key === item.key)
      );
      this.setState({
        selectedRows: filterData,
        selectedConfigList: filterData.map(item => {
          return { ...item, tabKey: '1' };
        }),
      });
    } else {
      const filterData = checkedNodes
        ?.filter(item => selectedRows?.findIndex(i => i.key === item.key))
        .filter(item => get(item, ['computedNodeProps', 'parentKeys']).length === 2);
      this.setState({
        // checkedNodes: filterData,
        checkedConfigList: [
          ...filterData.map(item => {
            return { ...item, tabKey: 'custom' };
          }),
          ...checkedConfigList,
        ],
        // checkedItemKeys: filterData.map(item => item.key),
      });
    }
    this.editModelVisible();
  };

  onDeleteChildItem = (topCategory, childItemKey, shouldUpdate) => {
    const { childItemsMap, panelKeys, checkedItemKeys, checkedConfigList } = this.state;
    const filterNodes = checkedConfigList.filter(item => item.key !== childItemKey);

    if (shouldUpdate) {
      this.setState({
        checkedConfigList: filterNodes.map(item => {
          return {
            ...item,
            tabKey: 'custom',
          };
        }),
      });
      return;
    }
    const childItems = childItemsMap[topCategory];
    const list = childItems.filter(item => item.key !== childItemKey);
    this.setState({
      childItemsMap: {
        ...childItemsMap,
        [topCategory]: list,
      },
      checkedItemKeys: checkedItemKeys
        .filter(item => item !== childItemKey)
        .filter(i => i !== childItemKey.substring(0, 3) + '-C1')
        .filter(i => i !== childItemKey.substring(0, 1) + '-C0'),
      checkedNodes: filterNodes,
    });
    if (!list.length) {
      this.setState({ panelKeys: panelKeys.filter(item => item !== topCategory) });
    }
  };

  setSelectedConfigs = (selectedRowKeys, selectedRows) => {
    this.setState(prevState => ({
      selectedRows: uniqBy([...prevState.selectedRows, ...selectedRows], 'key'),
      selectedRowKeys,
    }));
  };

  render() {
    const {
      modelVisible,
      checkedItemKeys,
      activeKey,
      childItemsMap,
      checkedConfigList,
      selectedConfigList,
      tabKey,
      panelKeys,
      expandedKeys,
    } = this.state;
    const { normalizedList, inventorySubType, inventoryType, treeList } = this.props;

    return (
      <GutterWrapper mode="vertical">
        <TinyTable
          align="left"
          style={{ marginBottom: 40 }}
          rowKey="key"
          columns={getColumns(this)}
          dataSource={[...checkedConfigList, ...selectedConfigList].filter(
            item => get(item, ['computedNodeProps', 'parentKeys']).length === 2
          )}
          actions={
            <Button type="primary" onClick={this.editModelVisible}>
              添加
            </Button>
          }
        />
        <TinyModal
          width="60%"
          title="添加"
          maskClosable={false}
          destroyOnClose
          visible={modelVisible}
          onCancel={this.editModelVisible}
          onOk={this.handleAdd}
        >
          <Tabs
            value={tabKey}
            tabBarStyle={{ borderBottom: 0 }}
            onChange={key => this.setState({ tabKey: key })}
          >
            {/* <Tabs.TabPane tab="现有配置" key="1">
              <ConfigTable
                inventoryType={inventoryType}
                inventorySubType={inventorySubType}
                selectedConfigRowKeys={selectedRowKeys}
                setSelectedConfigs={this.setSelectedConfigs}
              />
            </Tabs.TabPane> */}
            <Tabs.TabPane key="2" tab="自定义">
              <GutterWrapper flex>
                <TinyCard style={{ flex: 3 }} bordered>
                  <ApiTree
                    treeNodeFilterProp="metaName"
                    showSearch
                    expandedKeys={expandedKeys}
                    autoExpandParent
                    treeStyle={{
                      maxHeight: (500 / 900) * window.innerHeight,
                      overflowY: 'auto',
                    }}
                    checkable
                    selectable={false}
                    checkedKeys={checkedItemKeys}
                    fieldNames={{
                      key: ({ metaCode, metaType }) => generateNodeKey(metaCode, metaType),
                      title: 'metaName',
                      parentKey: ({ metaCode, metaType }) => generateNodeKey(metaCode, metaType),
                      disableCheckbox: ({ metaCode, metaType }) => {
                        if (
                          [...checkedConfigList, ...selectedConfigList].filter(
                            item => item.key === generateNodeKey(metaCode, metaType)
                          ).length
                        ) {
                          return true;
                        }
                        return false;
                      },
                    }}
                    filterLeafTreeNode={({ metaType }) => metaType === 'C2'}
                    dataService={() => {
                      let filterList = [];
                      if (
                        inventorySubType === INVENTORY_SUB_TYPE_KEY_MAP.IMPLICIT &&
                        inventoryType === INVENTORY_TYPE_KEY_MAP.MANUAL_INVENTORY
                      ) {
                        filterList =
                          treeList &&
                          treeList.filter(
                            item => item.metaStyle === DEVICE_SPACE_TYPE_KEY_MAP.DEVICE
                          );
                      } else {
                        filterList =
                          treeList &&
                          treeList.filter(
                            item =>
                              item.metaStyle === DEVICE_SPACE_TYPE_KEY_MAP.DEVICE &&
                              item.numbered === true
                          );
                      }
                      return Promise.resolve({
                        treeList: filterList,
                        parallelList: flattenTreeData(filterList),
                      });
                    }}
                    onCheck={async (checkedKeys, { checkedNodes }) => {
                      await this.setState({ checkedItemKeys: checkedKeys, checkedNodes });
                      if (!checkedNodes.length) {
                        this.setState({ panelKeys: [] });
                        return;
                      }
                      const deviceTypes = [];
                      const childItemsMap = {};
                      checkedNodes.forEach(item => {
                        const parentKeys = get(item, ['computedNodeProps', 'parentKeys']);
                        const deviceType = parentKeys.length ? parentKeys[0] : item.key;
                        deviceTypes.push(deviceType);
                        if (!parentKeys[1]) {
                          return;
                        }
                        childItemsMap[deviceType] = [...(childItemsMap[deviceType] || []), item];
                      });

                      this.setState({ panelKeys: uniq(deviceTypes), childItemsMap });
                    }}
                    onExpandedKeysUpdate={expandedKeys => this.setState({ expandedKeys })}
                    onExpand={expandedKeys => this.setState({ expandedKeys })}
                  />
                </TinyCard>
                <TinyCard style={{ flex: 5 }}>
                  <Collapse
                    bordered={false}
                    activeKey={activeKey}
                    style={{ maxHeight: (500 / 900) * window.innerHeight, overflowY: 'auto' }}
                    onChange={key => this.setState({ activeKey: key })}
                  >
                    {Array.isArray(panelKeys) &&
                      panelKeys.map(topCategory => (
                        <Collapse.Panel
                          key={topCategory}
                          header={get(normalizedList, [topCategory.split('-')[0], 'metaName'])}
                        >
                          <List
                            size="small"
                            itemLayout="horizontal"
                            dataSource={childItemsMap[topCategory]}
                            renderItem={item => (
                              <List.Item
                                style={{ paddingTop: 4, paddingBottom: 4 }}
                                actions={[
                                  <Button
                                    key={item.key}
                                    style={{ height: 'auto' }}
                                    type="link"
                                    onClick={() => {
                                      this.onDeleteChildItem(topCategory, item.key, false);
                                    }}
                                  >
                                    删除
                                  </Button>,
                                ]}
                              >
                                {get(normalizedList, [
                                  get(item, ['computedNodeProps', 'parentKeys', 1]).split('-')[0],
                                  'metaName',
                                ]) +
                                  '-' +
                                  getDeviceTypeName(item.key.split('-')[0], normalizedList)}
                              </List.Item>
                            )}
                          />
                        </Collapse.Panel>
                      ))}
                  </Collapse>
                </TinyCard>
              </GutterWrapper>
            </Tabs.TabPane>
          </Tabs>
        </TinyModal>
      </GutterWrapper>
    );
  }
}

const mapStateToProps = ({ common: { deviceCategory } }) => ({
  normalizedList: deviceCategory?.normalizedList,
  treeList: deviceCategory?.treeList,
});
const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(AddConfig);

function generateNodeKey(metaCode, metaType) {
  return `${metaCode}-${metaType}`;
}
