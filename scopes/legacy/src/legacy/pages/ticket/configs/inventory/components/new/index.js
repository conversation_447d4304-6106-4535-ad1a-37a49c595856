import React, { Component } from 'react';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';

import Form from '@ant-design/compatible/es/form';
import QuestionCircleOutlined from '@ant-design/icons/es/icons/QuestionCircleOutlined';
import { Select } from '@galiojs/awesome-antd';

import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';

import { RoomSelect } from '@manyun/resource-hub.ui.room-select';
import { SlaUnit } from '@manyun/ticket.model.task';
import { SlaSelect } from '@manyun/ticket.ui.sla-select';

import {
  <PERSON>er<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rapper,
  LocationCascader,
  TinyCard,
} from '@manyun/dc-brain.legacy.components';
import {
  INVENTORY_SUB_TYPE_KEY_MAP,
  INVENTORY_SUB_TYPE_OPTIONS,
  INVENTORY_TARGET_TYPE_KEY_MAP,
  INVENTORY_TYPE_KEY_MAP,
  INVENTORY_TYPE_OPTIONS,
} from '@manyun/dc-brain.legacy.constants/inventory';
import { StyledMcUpload } from '@manyun/dc-brain.legacy.pages/ticket/configs/access-door/styled';
import { getFileInfoList } from '@manyun/dc-brain.legacy.pages/ticket/configs/access-door/utils';
import { redirectActionCreator } from '@manyun/dc-brain.legacy.redux/actions/routerActions';
import * as taskCenterService from '@manyun/dc-brain.legacy.services/taskCenterService';
import { getSpaceGuidMap } from '@manyun/dc-brain.legacy.utils';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';

import AddAssetConfig from './components/add-asset-config';
import AddConfig from './components/add-config';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};

export class NewTicket extends Component {
  state = {
    loading: false,
    targetTags: [],
  };

  setTargetTags = values => {
    this.setState({ targetTags: values });
  };

  render() {
    const { form, history, ticketType } = this.props;
    const { loading } = this.state;
    const { getFieldDecorator, getFieldsValue, getFieldValue, validateFields, setFieldsValue } =
      form;
    const { location, inventoryType, inventoryTargetType } = getFieldsValue();

    return (
      <GutterWrapper mode="vertical">
        <TinyCard title="基本信息">
          <Form colon={false} {...formItemLayout} style={{ width: '520px' }}>
            <Form.Item label="工单标题">
              {getFieldDecorator('taskTitle', {
                rules: [
                  { required: true, message: '工单标题为必填！' },
                  {
                    type: 'string',
                    max: 20,
                    message: '最多输入 20 个字符！',
                  },
                ],
              })(<Input allowClear style={{ width: 335 }} />)}
            </Form.Item>
            <Form.Item label="位置">
              {getFieldDecorator('location', {
                rules: [
                  {
                    required: true,
                    message: '位置需选至楼栋！',
                  },
                ],
              })(
                <LocationCascader
                  currentAuthorize
                  changeOnSelect={false}
                  style={{ width: 220 }}
                  onChange={() => setFieldsValue({ rooms: [] })}
                />
              )}
            </Form.Item>
            <Form.Item label="指定包间">
              {getFieldDecorator('rooms')(
                <RoomSelect
                  style={{ width: 220 }}
                  disabled={location?.length !== 2 && !location}
                  mode="multiple"
                  blockGuid={location && location.length !== 0 ? location.join('.') : ''}
                  allowClear
                />
              )}
            </Form.Item>
            <Form.Item label="盘点目标">
              {getFieldDecorator('inventoryTargetType', {
                rules: [{ required: true, message: '盘点目标为必选！' }],
                initialValue: INVENTORY_TARGET_TYPE_KEY_MAP.DEVICE_TYPE,
              })(
                <Radio.Group
                  onChange={e => {
                    this.setState({ targetTags: [] });
                    e.target.value === INVENTORY_TARGET_TYPE_KEY_MAP.ASSET_NO &&
                      setFieldsValue({
                        inventorySubType: INVENTORY_SUB_TYPE_KEY_MAP.DISPLAY,
                        inventoryType: INVENTORY_TYPE_KEY_MAP.MANUAL_INVENTORY,
                      });
                  }}
                >
                  <Radio
                    style={{ lineHeight: '40px' }}
                    value={INVENTORY_TARGET_TYPE_KEY_MAP.DEVICE_TYPE}
                  >
                    按照资产分类
                  </Radio>
                  <Radio
                    style={{ lineHeight: '40px' }}
                    value={INVENTORY_TARGET_TYPE_KEY_MAP.ASSET_NO}
                  >
                    按照资产ID
                  </Radio>
                </Radio.Group>
              )}
              <Tooltip title="按照资产分类: 无编号资产展示不包括数量在内的资产清单,说明无编号资产仅支持盲盘。">
                <QuestionCircleOutlined />
              </Tooltip>
            </Form.Item>
            <Form.Item label="盘点方式">
              {getFieldDecorator('inventoryType', {
                initialValue: INVENTORY_TYPE_KEY_MAP.AUTO_INVENTORY,
              })(
                <Select
                  style={{ width: 220 }}
                  onChange={inventoryType => {
                    inventoryType === INVENTORY_TYPE_KEY_MAP.AUTO_INVENTORY &&
                      setFieldsValue({ inventorySubType: null });
                  }}
                >
                  {INVENTORY_TYPE_OPTIONS.map(({ label, value }) => {
                    return (
                      <Select.Option key={value} value={value}>
                        {label}
                      </Select.Option>
                    );
                  })}
                </Select>
              )}
              <Tooltip
                title={
                  <>
                    <p>自动盘点：系统通过线上进行设备实物盘点。</p>
                    <p>人工盘点：人工通过移动端扫码设备实物二维码进行盘点。</p>
                  </>
                }
              >
                <QuestionCircleOutlined style={{ paddingLeft: 13.6 }} />
              </Tooltip>
            </Form.Item>
            {inventoryType === INVENTORY_TYPE_KEY_MAP.MANUAL_INVENTORY && (
              <Form.Item label="盘点子方式">
                {getFieldDecorator('inventorySubType', {
                  initialValue: INVENTORY_SUB_TYPE_KEY_MAP.DISPLAY,
                })(
                  <Select
                    style={{ width: 220 }}
                    disabled={inventoryTargetType === INVENTORY_TARGET_TYPE_KEY_MAP.ASSET_NO}
                  >
                    {INVENTORY_SUB_TYPE_OPTIONS.map(({ label, value }) => {
                      return (
                        <Select.Option key={value} value={value}>
                          {label}
                        </Select.Option>
                      );
                    })}
                  </Select>
                )}
                <Tooltip
                  title={
                    <>
                      <p>明盘：盘点工单中展示需盘点资产清单。</p>
                      <p>
                        盲盘：有编号资产不展示需盘点资产清单，无编号资产展示不包括数量在内的资产清单。说明：无编号资产仅支持盲盘。
                      </p>
                    </>
                  }
                >
                  <QuestionCircleOutlined style={{ paddingLeft: 13.6 }} />
                </Tooltip>
              </Form.Item>
            )}
            <Form.Item label="SLA">
              {getFieldDecorator('taskSla', {
                initialValue: { sla: 1, unit: SlaUnit.Day },
              })(<SlaSelect />)}
            </Form.Item>
            <Form.Item label="附件">
              {getFieldDecorator('fileInfoList', {
                valuePropName: 'fileList',
                normalize: value => {
                  if (Array.isArray(value)) {
                    return value;
                  }
                  return value?.fileList || [];
                },
              })(
                <StyledMcUpload
                  accept="image/*,.xls,.xlsx"
                  showUploadList
                  maxFileSize={20}
                  allowDelete
                  showAccept
                >
                  <Button type="primary">上传</Button>
                </StyledMcUpload>
              )}
            </Form.Item>
          </Form>
        </TinyCard>
        <TinyCard title="盘点内容" style={{ marginBottom: 40 }}>
          {getFieldValue('inventoryTargetType') === INVENTORY_TARGET_TYPE_KEY_MAP.DEVICE_TYPE ? (
            <AddConfig
              setTargetTags={this.setTargetTags}
              //赋有初值的表单项，getFieldsValue获取操作在赋初值前，且不会再触发render
              inventoryType={getFieldValue('inventoryType')}
              //直接在之前使用getFieldsValue来获取时，无法取到表单域中inventorySubType对应项
              inventorySubType={getFieldValue('inventorySubType')}
            />
          ) : (
            <AddAssetConfig
              location={location}
              roomGuids={getFieldValue('rooms')}
              setTargetTags={this.setTargetTags}
            />
          )}
        </TinyCard>
        <FooterToolBar>
          <GutterWrapper>
            <Button
              loading={loading}
              type="primary"
              onClick={() => {
                validateFields(async (error, valueMap) => {
                  if (error) {
                    return;
                  }
                  if (!this.state.targetTags.length) {
                    message.error('请添加盘点内容！');
                    return;
                  }
                  this.setState({ loading: true });
                  const id = await createTicket(getQ(this.state, valueMap));
                  if (id) {
                    setTimeout(() => {
                      this.props.redirect(
                        urls.generateTicketDetailLocation({
                          ticketType: ticketType,
                          id: id,
                        })
                      );
                    }, 1.5 * 1000);
                  }
                  this.setState({ loading: false });
                });
              }}
            >
              提交
            </Button>
            <Button loading={loading} onClick={() => history.goBack()}>
              取消
            </Button>
          </GutterWrapper>
        </FooterToolBar>
      </GutterWrapper>
    );
  }
}

const mapDispatchToProps = {
  redirect: redirectActionCreator,
};
export default connect(null, mapDispatchToProps)(Form.create()(withRouter(NewTicket)));

async function createTicket(data) {
  const { response, error } = await taskCenterService.createInventoryTicket(data);
  if (error) {
    message.error(error);
    return;
  }
  message.success('创建成功！');
  return response;
}

function getQ(
  state,
  {
    inventorySubType,
    inventoryTargetType,
    inventoryType,
    location,
    taskTitle,
    rooms,
    fileInfoList,
    taskSla,
  }
) {
  const fileList = getFileInfoList(fileInfoList);

  return {
    taskTitle,
    targetTags: state.targetTags,
    roomTags: rooms.map(guid => getSpaceGuidMap(guid).room),
    inventoryType,
    inventoryTargetType,
    inventorySubType,
    idcTag: location?.[0],
    blockGuid: location?.join('.'),
    limitRoom: rooms.length > 0,
    fileInfoList: fileList,
    taskSla: taskSla.sla,
    unit: taskSla.unit,
  };
}
