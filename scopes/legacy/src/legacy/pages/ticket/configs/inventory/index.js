import { INVENTORY_TYPE_KEY_MAP } from '@manyun/dc-brain.legacy.constants/inventory';

import TTR from './../../registries/ticket-type-registry';
import NewTicket from './components/new';
import SpecificInventory from './components/specific';

TTR.registerTicketType('inventory')
  .registerConfig({
    type: 'tickets',
    showNewBtn: true,
    mergeFilters: ({ DEFAULT_FILTER_KEY_MAP, defaultFilters }) => {
      const newFilters = [...defaultFilters].filter(
        ({ key }) => key !== DEFAULT_FILTER_KEY_MAP.END_TIME
      );

      return newFilters;
    },
  })
  .registerConfig({
    type: 'new-ticket',
    content: NewTicket,
  })
  .registerConfig({
    type: 'specific-ticket',
    content: SpecificInventory,
    showEndOfTicketBtn: basicInfo => {
      return basicInfo?.taskSubType !== INVENTORY_TYPE_KEY_MAP.AUTO_INVENTORY;
    },
    showTransferBtn: basicInfo => {
      return basicInfo?.taskSubType !== INVENTORY_TYPE_KEY_MAP.AUTO_INVENTORY;
    },
  });
