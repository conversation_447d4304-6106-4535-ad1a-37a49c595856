import React, { Component } from 'react';
import { connect } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';

import { BackendTaskStatus } from '@manyun/ticket.model.ticket';

import { TinyModal } from '@manyun/dc-brain.legacy.components';
import { INVENTORY_TYPE_KEY_MAP } from '@manyun/dc-brain.legacy.constants/inventory';
import * as taskCenterService from '@manyun/dc-brain.legacy.services/taskCenterService';
import { getTaskProperties } from '@manyun/dc-brain.legacy.utils/ticket';

class CompleteButton extends Component {
  state = {
    visible: false,
    text: '',
    footer: null,
    checkFinished: false,
  };

  changeVisible = () => {
    this.setState({ visible: !this.state.visible });
  };

  handleClick = async () => {
    const { basicInfo, selectedCard } = this.props;
    const { response, error } = await taskCenterService.validInventoryFinished({
      roomGuid: selectedCard.roomGuid,
      taskNo: basicInfo.taskNo,
    });
    if (response === 'ALL_SUCC') {
      this.handleFinish();
      return;
    }
    if (response === 'PART_SUCC') {
      this.setState({
        text: '当前包间存在有编号资产未盘点，是否结束盘点？',
        footer: (
          <>
            <Button onClick={this.changeVisible}>取消</Button>
            <Button type="primary" onClick={this.handleFinish}>
              确定
            </Button>
          </>
        ),
      });
      this.changeVisible();
      return;
    }
    this.setState({
      text: error,
      footer: (
        <Button type="primary" onClick={this.changeVisible}>
          确定
        </Button>
      ),
    });
    this.changeVisible();
  };

  handleFinish = async () => {
    const { basicInfo, selectedCard, fetchDeviceList } = this.props;
    const properties = getTaskProperties(basicInfo);
    const { error } = await taskCenterService.updateInventoryCard({
      inventorySubType: properties.inventorySubType,
      roomGuid: selectedCard.roomGuid,
      taskNo: basicInfo.taskNo,
    });
    if (error) {
      message.error(error);
      return;
    }
    message.success('盘点已完成');
    this.setState({ checkFinished: true });
    fetchDeviceList(1, 10);
    this.state.visible && this.changeVisible();
  };

  render() {
    const { basicInfo, selectedCard } = this.props;
    const { visible, text, footer, checkFinished } = this.state;

    if (
      basicInfo?.taskSubType !== INVENTORY_TYPE_KEY_MAP.MANUAL_INVENTORY ||
      basicInfo?.taskStatus !== BackendTaskStatus.PROCESSING ||
      selectedCard.finished ||
      checkFinished
    ) {
      return null;
    }

    return (
      <>
        <Button type="primary" onClick={this.handleClick}>
          完成
        </Button>
        <TinyModal title="提示" visible={visible} footer={footer} onCancel={this.changeVisible}>
          {text}
        </TinyModal>
      </>
    );
  }
}
const mapStateToProps = ({ ticket: { ticketView } }) => ({
  basicInfo: ticketView?.basicInfo,
});
export default connect(mapStateToProps, null)(CompleteButton);
