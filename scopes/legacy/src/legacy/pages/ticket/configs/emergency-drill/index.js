import React from 'react';

import { EmergencyDrillLevelMap, EmergencyDrillMajorMap } from '@manyun/ticket.model.ticket';
import {
  Detail,
  EmergencyMajorSelect,
  NewEmergencyDrill,
} from '@manyun/ticket.page.emergency-drill';

import TTR from './../../registries/ticket-type-registry';

TTR.registerTicketType('emergency_drill')
  .registerConfig({
    type: 'tickets',

    showNewBtn: true,
    /**
     * @param {object} param1
     * @param {typeof import('./../../views/tickets/constants').DEFAULT_FILTER_KEY_MAP} param1.DEFAULT_FILTER_KEY_MAP
     * @param {any[]} param1.defaultColumns
     */
    mergeFilters: ({ DEFAULT_FILTER_KEY_MAP, defaultFilters }) => {
      const newFilters = [...defaultFilters].filter(
        ({ key }) =>
          key !== DEFAULT_FILTER_KEY_MAP.IS_DELAY && key !== DEFAULT_FILTER_KEY_MAP.END_TIME
      );

      const insertIdx = newFilters.findIndex(
        ({ key }) => key === DEFAULT_FILTER_KEY_MAP.TICKET_TITLE
      );
      newFilters.splice(insertIdx, 0, {
        key: 'subjectTag',
        label: '专业',
        Comp: EmergencyMajorSelect,
      });

      return newFilters;
    },

    /**
     * @param {object} param1
     * @param {typeof import('./../../views/tickets/constants').DEFAULT_COLUMN_DATA_INDEX_MAP} param1.DEFAULT_COLUMN_DATA_INDEX_MAP
     * @param {any[]} param1.defaultColumns
     */
    mergeColumns: ({ DEFAULT_COLUMN_DATA_INDEX_MAP, defaultColumns }) => {
      const newColumns = [...defaultColumns].filter(
        ({ dataIndex }) =>
          dataIndex !== DEFAULT_COLUMN_DATA_INDEX_MAP.TASK_SLA &&
          dataIndex !== DEFAULT_COLUMN_DATA_INDEX_MAP.TASK_RESPOND_TIME
      );

      const insertIdx = newColumns.findIndex(
        ({ dataIndex }) => dataIndex === DEFAULT_COLUMN_DATA_INDEX_MAP.TICKET_TITLE
      );
      newColumns.splice(insertIdx, 0, {
        title: '专业',
        dataIndex: 'specialty',
        render(_ignored, { taskProperties }) {
          // eslint-disable-next-line no-new-func
          const taskPropertiesJSON = new Function('return ' + taskProperties)();

          return (
            <span>
              {taskPropertiesJSON && EmergencyDrillMajorMap[taskPropertiesJSON.specialty]}
            </span>
          );
        },
      });
      const levelIdx = newColumns.findIndex(
        ({ dataIndex }) => dataIndex === DEFAULT_COLUMN_DATA_INDEX_MAP.CREATOR_NAME
      );

      newColumns.splice(levelIdx, 0, {
        title: '等级',
        dataIndex: 'level',
        render(_ignored, { taskProperties }) {
          // eslint-disable-next-line no-new-func
          const taskPropertiesJSON = new Function('return ' + taskProperties)();

          return (
            <span>{taskPropertiesJSON && EmergencyDrillLevelMap[taskPropertiesJSON.level]}</span>
          );
        },
      });
      return newColumns;
    },
  })
  .registerConfig({
    type: 'specific-ticket',
    showFailureBtn: true,
    content: Detail,
  })
  .registerConfig({
    type: 'new-ticket',
    content: NewEmergencyDrill,
  });
