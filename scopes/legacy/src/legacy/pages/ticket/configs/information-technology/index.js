import { Input } from '@manyun/base-ui.ui.input';

import { BackendTaskStatus } from '@manyun/ticket.model.ticket';
import { InformationTechnologyServiceCreate } from '@manyun/ticket.page.information-technology-service-create';
import { InformationTechnologyServiceDetail } from '@manyun/ticket.page.information-technology-service-detail';
import { TicketStatusSelect } from '@manyun/ticket.ui.ticket-status-select';

import { CURRENT_PAGE_TYPES } from '../../constants';
import TTR from './../../registries/ticket-type-registry';

TTR.registerTicketType('it_service')
  .registerConfig({
    type: CURRENT_PAGE_TYPES.TICKETS,
    showNewBtn: true,
    mergeFilters: ({ DEFAULT_FILTER_KEY_MAP, defaultFilters }) => {
      const newFilters = [...defaultFilters].filter(
        ({ key }) =>
          key !== DEFAULT_FILTER_KEY_MAP.IS_DELAY &&
          key !== DEFAULT_FILTER_KEY_MAP.TICKET_TITLE &&
          key !== DEFAULT_FILTER_KEY_MAP.END_TIME
      );

      newFilters.splice(
        newFilters.findIndex(item => item.key === DEFAULT_FILTER_KEY_MAP.TICKET_STATE),
        1,
        {
          label: '工单状态',
          key: 'taskStatusList',
          initialProps: {
            optionFilter: option =>
              ![
                BackendTaskStatus.INIT,
                BackendTaskStatus.CLOSE_APPROVER,
                BackendTaskStatus.UNDO,
              ].includes(option.value),
            allowClear: true,
            mode: 'multiple',
          },
          Comp: TicketStatusSelect,
        }
      );

      newFilters.splice(
        newFilters.findIndex(({ dataIndex }) => dataIndex === DEFAULT_FILTER_KEY_MAP.CREATOR_NAME),
        0,

        {
          key: DEFAULT_FILTER_KEY_MAP.TICKET_TITLE,
          label: '工单标题',
          Comp: Input,
          whitespace: 'trim',
          initialProps: { allowClear: true },
        }
      );

      return newFilters;
    },
  })
  .registerConfig({
    type: CURRENT_PAGE_TYPES.NEW_TICKET,
    content: InformationTechnologyServiceCreate,
  })
  .registerConfig({
    type: 'specific-ticket',
    content: InformationTechnologyServiceDetail,
  });
