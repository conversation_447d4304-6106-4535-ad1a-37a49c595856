import { Form } from '@galiojs/awesome-antd';
import React from 'react';
import { connect } from 'react-redux';

import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { QueryFilter } from '@manyun/base-ui.ui.query-filter';
import { Space } from '@manyun/base-ui.ui.space';
import { VendorModelSelect } from '@manyun/crm.ui.vendor-model-select';
import { generateDeviceRecordRoutePath } from '@manyun/resource-hub.route.resource-routes';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';

import { AssetClassificationApiTreeSelect, TinyTable } from '@manyun/dc-brain.legacy.components';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import { fetchEquipmentListPage } from '@manyun/dc-brain.legacy.redux/actions/equipmentActions';
import { equipmentManageService } from '@manyun/dc-brain.legacy.services';

export class DevicesModalLegacy extends React.Component {
  state = {
    fieldsValue: {},
    pageNum: 1,
    pageSize: 10,
    selectRowsInModal: [],
    loading: false,
    tableData: [],
    total: 0,
    fieldsConditions: {},
  };

  componentDidMount() {
    this.props.syncCommonData({ strategy: { deviceCategory: 'IF_NULL', space: 'IF_NULL' } });
  }

  componentDidUpdate(prevProps) {
    const { region, idcTag, blockTag, deviceDtoList, operationStatus } = this.props;
    const prevLocationString = `${prevProps.region}${prevProps.idcTag}${prevProps.blockTag}`;
    const nowLocationString = `${region}${idcTag}${blockTag}`;

    // 位置信息改变，类型改变，就重新请求设备列表
    if (
      (prevLocationString !== nowLocationString || operationStatus !== prevProps.operationStatus) &&
      region &&
      idcTag &&
      blockTag
    ) {
      this.getDeviceData('fieldsConditions');
      this.setState({
        selectRowsInModal: [],
      });
    }
    if (
      Array.isArray(deviceDtoList) &&
      this.state.selectRowsInModal.length !== 0 &&
      Array.isArray(prevProps.deviceDtoList) &&
      prevProps.deviceDtoList.length !== deviceDtoList.length
    ) {
      this.setState({
        selectRowsInModal: [],
      });
    }
  }

  getItems = () => {
    const { idcTag, blockTag } = this.props;

    const items = [
      {
        label: '所属包间',
        name: 'roomTag',
        control: (
          <LocationCascader
            idc={idcTag}
            authorizedOnly
            blocks={[blockTag]}
            nodeTypes={['ROOM']}
            showSearch={(inputValue, path) =>
              path.some(option => option.label.toLowerCase().indexOf(inputValue.toLowerCase()) > -1)
            }
          />
        ),
      },
      {
        label: '资产分类',
        name: 'cascaderList',
        control: (
          <AssetClassificationApiTreeSelect
            dataType={['snDevice']}
            allowClear
            category="allCategoryCode"
          />
        ),
      },
      {
        label: '设备编号',
        name: 'name',
        control: <Input allowClear />,
      },
      {
        label: '厂商、型号',
        name: 'vendor',
        control: <VendorModelSelect allowClear />,
      },
    ];
    return items;
  };

  search = () => {
    this.setState(
      {
        pageNum: 1,
      },
      () => {
        this.getDeviceData('fieldsValue');
      }
    );
  };

  getDeviceData = async type => {
    const { region, idcTag, blockTag, operationStatus } = this.props;
    const { fieldsConditions } = this.state;
    const _values = this.props.form.getFieldsValue();
    const searchValues = {
      cascaderList: { name: 'cascaderList', value: _values.cascaderList },
      name: { name: 'name', value: _values.name },
      roomTag: { name: 'roomTag', value: _values.roomTag },
      vendor: { name: 'vendor', value: _values.vendor },
    };
    const { pageNum, pageSize } = this.state;
    this.setState({ loading: true, fieldsValue: searchValues });
    const params = getQ(type === 'fieldsConditions' ? fieldsConditions : searchValues);
    let vendor;
    let productModel;

    if (params.vendor) {
      const [_vendor, _productModel] = params.vendor;
      vendor = _vendor;
      productModel = _productModel;
      delete params.vendor;
    }

    const { response, error } = await equipmentManageService.fetchEquipmentListPage({
      pageNum,
      pageSize,
      region,
      idcTag,
      blockTag,
      ...params,
      vendor,
      productModel,
      operationStatus,
      assetStatus: operationStatus === 'OFF' ? 'NORMAL' : null,
    });
    if (error) {
      message.error(error);
      this.setState({ loading: false });
    }
    if (response) {
      this.setState(({ fieldsValue }) => {
        const tmp = {
          loading: false,
          tableData: response.data,
          total: response.total,
        };
        if (type === 'fieldsValue') {
          tmp.fieldsConditions = fieldsValue;
        }
        return tmp;
      });
    }
  };

  paginationChangeHandler = (current, size) => {
    this.setState({ pageNum: current, pageSize: size }, () =>
      this.getDeviceData('fieldsConditions')
    );
  };

  render() {
    const { form, visible, deviceCategory } = this.props;
    const { pageNum, pageSize, selectRowsInModal, loading, tableData, total } = this.state;
    return (
      <Modal
        title="添加设备"
        destroyOnClose
        open={visible}
        width={1100}
        bodyStyle={{ maxHeight: '465px', overflowY: 'auto' }}
        onCancel={() => {
          this.props.updateVisible(false);
        }}
        onOk={() => {
          this.props.setDeviceDtoList(selectRowsInModal);
          this.props.updateVisible(false);
          this.setState({
            selectRowsInModal: [],
          });
        }}
      >
        <Space style={{ width: '100%' }} direction="vertical">
          <QueryFilter
            form={form}
            items={this.getItems()}
            onSearch={values => {
              this.search();
            }}
            onReset={() => {
              this.search();
            }}
          />
          <TinyTable
            rowKey="guid"
            align="left"
            loading={loading}
            dataSource={tableData}
            columns={getColumns({ deviceCategory })}
            pagination={{
              total,
              current: pageNum,
              pageSize: pageSize,
              onChange: this.paginationChangeHandler,
            }}
            rowSelection={{
              preserveSelectedRowKeys: true,
              getCheckboxProps: item => ({
                disabled: (() => {
                  const tmp = this.props.deviceDtoList.find(({ guid }) => item.guid === guid);
                  return tmp ? true : false;
                })(),
              }),
              selectedRowKeys: selectRowsInModal.map(({ guid }) => guid),
              selectedRows: selectRowsInModal,
              onChange: (keys, rows) => {
                this.setState({ selectRowsInModal: rows });
              },
            }}
          />
        </Space>
      </Modal>
    );
  }
}

function DevicesModal(props) {
  const [form] = Form.useForm();

  return <DevicesModalLegacy form={form} {...props} />;
}

const mapStateToProps = (
  { common: { deviceCategory }, 'resource.spaces': { entities, codes } },
  { idcTag, blockTag }
) => {
  let roomTags = [];
  if (codes) {
    const blockGuid = `${idcTag}.${blockTag}`;
    roomTags = codes
      .map(code => entities[code])
      .filter(space => space.parentCode === blockGuid)
      .map(space => ({
        ...space,
        metaCode: space.code,
        metaName: space.name,
        metaType: space.type,
      }));
  }

  return {
    deviceCategory,
    roomTags,
  };
};
const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
  getData: fetchEquipmentListPage,
};
export default connect(mapStateToProps, mapDispatchToProps)(DevicesModal);

const getColumns = ({ deviceCategory }) => [
  {
    title: '设备编号',
    dataIndex: 'name',
    render: (name, record) => {
      return (
        <a
          rel="noopener noreferrer"
          target="_blank"
          href={generateDeviceRecordRoutePath({ guid: record.guid })}
        >
          {name}
        </a>
      );
    },
  },
  {
    title: 'SN',
    dataIndex: 'serialNumber',
  },
  {
    title: '包间',
    dataIndex: 'spaceGuid',
    render: spaceGuid => spaceGuid.roomTag,
  },
  {
    title: '三级分类',
    dataIndex: 'deviceType',
    render: deviceType => {
      if (!deviceCategory || !deviceCategory.normalizedList) {
        return deviceType;
      }
      return deviceCategory?.normalizedList[deviceType]?.metaName ?? '--';
    },
  },
];

function getQ(searchValues) {
  const baseQ = {};
  for (const [, { name, value }] of Object.entries(searchValues)) {
    const val = value === '' ? null : value;
    if (!name) {
      return;
    }
    if (val) {
      if (name === 'roomTag') {
        baseQ.spaceGuidList = val;
      } else if (name === 'cascaderList' && value) {
        baseQ.topCategory = value.firstCategoryCode;
        baseQ.secondCategory = value.secondCategoryCode;
        baseQ.deviceTypeList = value.thirdCategorycode && [value.thirdCategorycode];
      } else {
        baseQ[name] = val;
      }
    }
  }
  return baseQ;
}
