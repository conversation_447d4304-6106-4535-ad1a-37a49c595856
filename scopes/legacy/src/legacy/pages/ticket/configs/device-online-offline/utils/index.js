export function getQ(data) {
  const baseQ = {
    taskType: 'ON_OFF',
  };
  for (const [key, { name, value }] of Object.entries(data)) {
    const val = value === '' ? null : value;
    if (name) {
      if (name === 'location' && val.length === 2) {
        baseQ.idcTag = val[0];
        baseQ.blockGuid = `${val[0]}.${val[1]}`;
      } else if (name === 'taskSla') {
        baseQ.taskSla = val.sla;
        baseQ.unit = val.unit;
      } else if (name === 'deviceDtoList' && val.length) {
        baseQ[name] = val.map(
          ({ guid, name, spaceGuid, deviceType, roomName, serialNumber, roomType }) => {
            return {
              deviceGuid: guid,
              deviceName: name,
              deviceType,
              roomGuid: spaceGuid.roomGuid,
              roomName,
              serialNumber,
              roomType,
            };
          }
        );
      } else {
        baseQ[name] = val;
      }
    } else {
      baseQ[key] = val;
    }
  }
  return baseQ;
}
