import React from 'react';
import { connect } from 'react-redux';

import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Typography } from '@manyun/base-ui.ui.typography';

import { syncCommonDataAction } from '@manyun/dc-brain.state.common';

import { CardList } from './card-list';

export class TicketDetail extends React.Component {
  componentDidMount() {
    this.props.syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });
  }

  render() {
    const { info, basicInfo, normalizedList } = this.props;
    return (
      <div>
        <Descriptions column={4}>
          <Descriptions.Item
            label={`${info.taskSubTypeTimeTxt}时间`}
            span={2}
            labelStyle={{ width: 132 }}
          >
            {info.lineTime}
          </Descriptions.Item>
          <Descriptions.Item
            label={`${info.taskSubTypeTimeTxt}原因`}
            span={2}
            labelStyle={{ width: 132 }}
          >
            <Typography.Text>{info.lineReason}</Typography.Text>
          </Descriptions.Item>
        </Descriptions>
        <CardList
          taskNo={basicInfo.taskNo}
          roomGuid={basicInfo.roomGuid}
          normalizedList={normalizedList}
        />
      </div>
    );
  }
}

const mapStateToProps = ({
  common: { deviceCategory },
  ticket: {
    ticketView: { basicInfo },
  },
}) => {
  let info = {};
  if (basicInfo && basicInfo.taskProperties) {
    info = JSON.parse(basicInfo.taskProperties);
    if (
      deviceCategory &&
      deviceCategory.normalizedList &&
      deviceCategory.normalizedList[info.deviceType]
    ) {
      info.deviceThirdName = deviceCategory.normalizedList[info.deviceType].metaName;
      const deviceThirdParentCode = deviceCategory.normalizedList[info.deviceType].parentCode;
      const deviceSecondCode = deviceThirdParentCode.slice(2, deviceThirdParentCode.length + 1);
      info.deviceSecondName = deviceCategory.normalizedList[deviceSecondCode].metaName;
      const deviceSecondParentCode = deviceCategory.normalizedList[deviceSecondCode].parentCode;
      const deviceFirstCode = deviceSecondParentCode.slice(2, deviceSecondParentCode.length + 1);
      info.deviceFirstName = deviceCategory.normalizedList[deviceFirstCode].metaName;
    }
    if (basicInfo.taskSubType === 'ON') {
      info.taskSubTypeTimeTxt = '上线';
    }
    if (basicInfo.taskSubType === 'OFF') {
      info.taskSubTypeTimeTxt = '下线';
    }
    info.workFlowId = basicInfo.workFlowId;
  }
  return {
    info,
    normalizedList: deviceCategory?.normalizedList,
  };
};
const mapDispatchToProps = {
  syncCommonData: syncCommonDataAction,
};
export default connect(mapStateToProps, mapDispatchToProps)(TicketDetail);
