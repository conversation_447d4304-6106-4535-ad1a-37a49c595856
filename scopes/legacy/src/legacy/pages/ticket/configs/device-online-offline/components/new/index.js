import Form from '@ant-design/compatible/es/form';
import ExclamationCircleOutlined from '@ant-design/icons/es/icons/ExclamationCircleOutlined';
import { Select } from '@galiojs/awesome-antd';
import differenceBy from 'lodash/differenceBy';
import omit from 'lodash/omit';
import React from 'react';
import { connect } from 'react-redux';
import styled from 'styled-components';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { generateDeviceRecordRoutePath } from '@manyun/resource-hub.route.resource-routes';

import {
  FooterToolBar,
  GutterWrapper,
  LocationCascader,
  TinyCard,
  TinyTable,
} from '@manyun/dc-brain.legacy.components';
import { StyledMcUpload } from '@manyun/dc-brain.legacy.pages/ticket/configs/access-door/styled';
import { getFileInfoList } from '@manyun/dc-brain.legacy.pages/ticket/configs/access-door/utils';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import { redirectActionCreator } from '@manyun/dc-brain.legacy.redux/actions/routerActions';
import { ticketActions } from '@manyun/dc-brain.legacy.redux/actions/ticketActions';
import * as taskCenterService from '@manyun/dc-brain.legacy.services/taskCenterService';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';

import { getQ } from '../../utils';
import DevicesModal from './components/devices-modal';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 2 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 22 },
  },
};
export class NewTicket extends React.Component {
  state = {
    deviceModalVisible: false,
    selectedDeviceDtoList: [],
    subBtnloading: false,
  };

  componentDidMount() {
    this.props.syncCommonData({ strategy: { ticketTypes: 'IF_NULL' } });
    if (this.props.ticketTreeData.length) {
      this.props.form.setFieldsValue({ taskSubType: this.props.ticketTreeData[0].taskType });
    }
  }

  componentDidUpdate(prevProps) {
    if (!prevProps.ticketTreeData.length && this.props.ticketTreeData.length) {
      this.props.form.setFieldsValue({ taskSubType: this.props.ticketTreeData[0].taskType });
    }
  }

  componentWillUnmount() {
    this.props.resetOnOffCreateValues();
  }

  getRegin = () => {
    const {
      form: { getFieldValue },
      space,
    } = this.props;
    if (!getFieldValue('location') || !getFieldValue('location').length || !space) {
      return null;
    }
    const idcTag = getFieldValue('location')[0];
    const region = space.normalizedList[idcTag].parentCode;
    return region;
  };

  deleteDevice = id => {
    const {
      fieldValues: { deviceDtoList },
    } = this.props;
    const newData = deviceDtoList.value.filter(({ guid }) => guid !== id);
    this.props.updateOnOffCreateFields({
      deviceDtoList: { value: newData, dirty: false, name: 'deviceDtoList' },
    });
  };

  submit = () => {
    const { fieldValues, form } = this.props;
    form.validateFields(async (error, value) => {
      if (error || !fieldValues.deviceDtoList.value.length) {
        return;
      }
      this.setState({ subBtnloading: true });
      const values = getQ(fieldValues);
      const params = omit(values, 'lineReason');
      params.deviceDtoList = values.deviceDtoList.map(item => {
        return { ...item };
      });
      const fileInfoList = getFileInfoList(fieldValues?.fileInfoList?.value);
      params.fileInfoList = fileInfoList;
      const response = await createTicket({ ...params, lineReason: values.lineReason });
      if (response) {
        this.setState({ subBtnloading: false });
        if (response.data) {
          // 只有一个工单时不拆分
          if (response.data.length === 1) {
            this.props.redirect(
              urls.generateTicketListUrl({
                type: 'on_off',
              })
            );
          } else {
            this.submitSucceed(response.data);
          }
        }
      }
    });
  };

  submitSucceed = data => {
    Modal.success({
      title: '创建成功',
      content: data.join('，'),
      onOk: () => {
        this.props.redirect(
          urls.generateTicketListUrl({
            type: 'on_off',
          })
        );
      },
    });
  };

  render() {
    const {
      form: { getFieldDecorator, getFieldValue },
      deviceCategory,
      fieldValues: { deviceDtoList },
      ticketTreeData,
      taskTypeTxt,
    } = this.props;
    const { deviceModalVisible, selectedDeviceDtoList, subBtnloading } = this.state;

    return (
      <>
        <Form colon={false} {...formItemLayout}>
          <GutterWrapper mode="vertical">
            <TinyCard title="基本信息" bodyStyle={{ width: '1104px' }}>
              <Form.Item label="上下线类型">
                {getFieldDecorator('taskSubType', {
                  rules: [{ required: true, message: '上下线类型必选！' }],
                })(
                  <Select style={{ width: 200 }}>
                    {ticketTreeData.map(({ taskType, taskValue }) => {
                      return (
                        <Select.Option key={taskType} value={taskType}>
                          {taskValue}
                        </Select.Option>
                      );
                    })}
                  </Select>
                )}
              </Form.Item>
              <Form.Item label="位置">
                {getFieldDecorator('location', {
                  rules: [
                    {
                      required: true,
                      message: '位置必选！',
                      type: 'number',
                      transform: value => (value?.length === 2 ? 2 : false),
                    },
                  ],
                })(<LocationCascader style={{ width: 200 }} currentAuthorize />)}
              </Form.Item>
              <Form.Item label={`${taskTypeTxt}原因`}>
                {getFieldDecorator('lineReason', {
                  rules: [
                    {
                      required: true,
                      message: `${taskTypeTxt}原因必填！`,
                    },
                    {
                      type: 'string',
                      max: 50,
                      message: `最多 50 个字符！`,
                    },
                  ],
                })(<Input.TextArea style={{ width: 200 }} />)}
              </Form.Item>
              <Form.Item label="附件">
                {getFieldDecorator('fileInfoList', {
                  valuePropName: 'fileList',
                  normalize: value => {
                    if (Array.isArray(value)) {
                      return value;
                    }
                    return value?.fileList || [];
                  },
                })(
                  <StyledMcUpload
                    accept="image/*,.xls,.xlsx"
                    showUploadList
                    maxFileSize={20}
                    allowDelete
                    showAccept
                  >
                    <Button type="primary">上传</Button>
                  </StyledMcUpload>
                )}
              </Form.Item>
            </TinyCard>
            <TinyCard title="已选列表" style={{ marginBottom: '40px' }}>
              <TinyTable
                rowKey="guid"
                align="left"
                dataSource={deviceDtoList.value}
                actions={[
                  <Button
                    key="batch-create"
                    type="primary"
                    disabled={
                      getFieldValue('location') &&
                      getFieldValue('location').length === 2 &&
                      getFieldValue('taskSubType')
                        ? false
                        : true
                    }
                    onClick={() => this.setState({ deviceModalVisible: true })}
                  >
                    添加设备
                  </Button>,
                  <Button
                    key="batch-delete"
                    type="danger "
                    disabled={selectedDeviceDtoList.length ? false : true}
                    onClick={() => {
                      const newData = differenceBy(
                        deviceDtoList.value,
                        selectedDeviceDtoList,
                        'guid'
                      );
                      this.props.updateOnOffCreateFields({
                        deviceDtoList: { value: newData, dirty: false, name: 'deviceDtoList' },
                      });
                      this.setState({ selectedDeviceDtoList: [] });
                    }}
                  >
                    批量移除
                  </Button>,
                  <Statistics
                    key="statistics"
                    selectedDeviceDtoList={selectedDeviceDtoList}
                    setSelectedDeviceDtoList={value => {
                      // const newData = differenceBy(
                      //   deviceDtoList.value,
                      //   selectedDeviceDtoList,
                      //   'guid'
                      // );
                      // this.props.updateOnOffCreateFields({
                      //   deviceDtoList: { value: newData, dirty: false, name: 'deviceDtoList' },
                      // });
                      this.setState({ selectedDeviceDtoList: value });
                    }}
                  />,
                ]}
                columns={getColumns({ deviceCategory, deleteDevice: this.deleteDevice })}
                rowSelection={{
                  selectedRowKeys: selectedDeviceDtoList.map(({ guid }) => guid),
                  selectedRows: selectedDeviceDtoList,
                  onChange: (keys, rows) => this.setState({ selectedDeviceDtoList: rows }),
                }}
                locale={{
                  emptyText: (
                    <div className="has-error">
                      <span className="manyun-form-explain">至少添加1个设备</span>
                    </div>
                  ),
                }}
              />
            </TinyCard>
            <FooterToolBar>
              <GutterWrapper>
                <Button
                  type="primary"
                  loading={subBtnloading}
                  disabled={!deviceDtoList || deviceDtoList.value.length === 0}
                  onClick={this.submit}
                >
                  提交
                </Button>
                <Button onClick={() => this.props.history.goBack()}>取消</Button>
              </GutterWrapper>
            </FooterToolBar>
          </GutterWrapper>
        </Form>
        <DevicesModal
          visible={deviceModalVisible}
          updateVisible={visible => this.setState({ deviceModalVisible: visible })}
          operationStatus={getFieldValue('taskSubType') === 'ON' ? 'OFF' : 'ON'} // 上线工单对应下线设备，下线工单对应上线设备
          region={this.getRegin()}
          idcTag={getFieldValue('location') ? getFieldValue('location')[0] : null}
          blockTag={getFieldValue('location') ? getFieldValue('location')[1] : null}
          deviceDtoList={deviceDtoList.value}
          setDeviceDtoList={list =>
            this.props.updateOnOffCreateFields({
              deviceDtoList: {
                value: [...deviceDtoList.value, ...list],
                dirty: false,
                name: 'deviceDtoList',
              },
            })
          }
        />
      </>
    );
  }
}

const mapStateToProps = ({
  common: { deviceCategory, ticketTypes },
  'resource.spaces': { entities, codes },
  ticket: {
    on_off: { create },
  },
}) => {
  const fieldValues = create;
  let ticketTreeData = [];
  let taskTypeTxt = '';

  if (ticketTypes) {
    const assessTypes = ticketTypes.treeList.filter(item => {
      if (item.taskType === 'ON_OFF') {
        return true;
      }
      return false;
    });
    if (assessTypes.length) {
      ticketTreeData = assessTypes[0].children;
    }
    taskTypeTxt =
      ticketTypes.normalizedList && ticketTypes.normalizedList[create.taskSubType.value]
        ? ticketTypes.normalizedList[create.taskSubType.value].taskValue
        : '';
  }
  return {
    space: codes ? { normalizedList: entities } : null,
    deviceCategory,
    fieldValues,
    ticketTreeData,
    taskTypeTxt,
  };
};
const mapDispatchToProps = {
  updateOnOffCreateFields: ticketActions.updateOnOffCreateFields,
  redirect: redirectActionCreator,
  resetOnOffCreateValues: ticketActions.resetOnOffCreateValues,
  syncCommonData: syncCommonDataActionCreator,
};
export default connect(
  mapStateToProps,
  mapDispatchToProps
)(
  Form.create({
    onFieldsChange: (props, changedFields) => {
      let fields = changedFields;
      if (changedFields.location) {
        const prevLocationString = props.fieldValues.location.value
          ? props.fieldValues.location.value.join('')
          : null;
        const nowLocationString = changedFields.location.value
          ? changedFields.location.value.join('')
          : null;
        if (nowLocationString !== prevLocationString) {
          fields = {
            ...fields,
            deviceDtoList: {
              value: [],
              name: 'deviceDtoList',
            },
          };
        }
      }
      if (changedFields.taskSubType) {
        const prevTaskSubType = props.fieldValues.taskSubType.value;
        const nowTaskSubType = changedFields.taskSubType.value;
        if (prevTaskSubType !== nowTaskSubType) {
          fields = {
            ...fields,
            deviceDtoList: {
              value: [],
              name: 'deviceDtoList',
            },
          };
        }
      }
      props.updateOnOffCreateFields(fields);
    },
    mapPropsToFields(props) {
      return {
        taskSubType: Form.createFormField(props.fieldValues.taskSubType),
        location: Form.createFormField(props.fieldValues.location),
        lineReason: Form.createFormField(props.fieldValues.lineReason),
        fileInfoList: Form.createFormField({
          value: props.fieldValues.fileInfoList.value,
        }),
      };
    },
  })(NewTicket)
);

export const SpanContain = styled.span`
  display: inline-block;
  vertical-align: middle;
  line-height: 32px;
`;

export const NumButton = styled(Button)`
  padding: 0 5px;
`;

function Statistics({ selectedDeviceDtoList, setSelectedDeviceDtoList }) {
  if (!selectedDeviceDtoList.length) {
    return null;
  }
  return (
    <SpanContain>
      <ExclamationCircleOutlined style={{ color: `var(--${prefixCls}-warning-color)` }} />
      &nbsp;已选择
      <NumButton
        type="link"
        onClick={() => {
          return Modal.info({
            title: `已选择${selectedDeviceDtoList.length}项`,
            content: selectedDeviceDtoList.map(({ name }) => name).join('，'),
            okText: '关闭',
          });
        }}
      >
        {selectedDeviceDtoList.length}
      </NumButton>
      项
      <Button
        type="link"
        onClick={() => setSelectedDeviceDtoList([])}
        // onClick={() => {
        //   Modal.confirm({
        //     title: `确认要删除这${selectedDeviceDtoList.length}条信息吗？`,
        //     content: '删除之后数据将不可恢复。',
        //     onOk() {
        //       setSelectedDeviceDtoList([]);
        //     },
        //   });
        // }}
      >
        清空
      </Button>
    </SpanContain>
  );
}

const getColumns = ({ deviceCategory, deleteDevice }) => [
  {
    title: '包间',
    dataIndex: 'spaceGuid',
    render: spaceGuid => spaceGuid.roomTag,
  },
  {
    title: '三级分类',
    dataIndex: 'deviceType',
    render: deviceType => {
      if (!deviceCategory || !deviceCategory.normalizedList) {
        return deviceType;
      }
      return deviceCategory?.normalizedList[deviceType]?.metaName ?? '--';
    },
  },
  {
    title: '设备名称',
    dataIndex: 'name',
    render: (name, record) => {
      return (
        <a
          rel="noopener noreferrer"
          target="_blank"
          href={generateDeviceRecordRoutePath({ guid: record.guid })}
        >
          {name}
        </a>
      );
    },
  },
  {
    title: 'SN',
    dataIndex: 'serialNumber',
  },

  {
    title: '操作',
    key: 'actions',
    render: (_, records) => (
      <Button compact type="link" onClick={() => deleteDevice(records.guid)}>
        删除
      </Button>
    ),
  },
];

async function createTicket(data) {
  const { response, error } = await taskCenterService.createOnOffTicket(data);
  if (error) {
    message.error(error);
    return true;
  }
  message.success('创建成功！');
  return response;
}
