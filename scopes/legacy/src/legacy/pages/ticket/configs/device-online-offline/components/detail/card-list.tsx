import { useEffect, useRef, useState } from 'react';

import { ArrowLeftOutlined } from '@ant-design/icons';
import { nanoid } from 'nanoid';
import styled from 'styled-components';

import { Card } from '@manyun/base-ui.ui.card';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Input } from '@manyun/base-ui.ui.input';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';

import { RoomTypeText } from '@manyun/resource-hub.ui.room-type-text';
import type { ApiResponseData as tableDetailsData } from '@manyun/ticket.service.fetch-details-of-online-offline-orders';
import { fetchDetailsOfOnlineOfflineOrders } from '@manyun/ticket.service.fetch-details-of-online-offline-orders';
import type { ApiResponseData as cardListData } from '@manyun/ticket.service.fetch-list-of-online-offline-orders';
import { fetchListOfOnlineOfflineOrders } from '@manyun/ticket.service.fetch-list-of-online-offline-orders';

import { getDeviceTypeName } from '@manyun/dc-brain.legacy.utils/ticket';

const StyledGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(244px, 1fr));
  gap: 16px;
`;
const StyledCard = styled(Card)`
  /* max-width: 244px; */
  padding: 8px;
  margin: 0;
  /* flex: 1; */

  .manyun-card-body {
    padding: 0;
    cursor: pointer;
  }

  .card-title {
    padding-bottom: 14px;
    & ::before {
      content: '';
      display: inline-block;
      width: 4px;
      height: 1em;
      margin-right: 8px;
      vertical-align: -0.125em;
      border-radius: 4px;
      background: var(--manyun-primary-color);
    }
  }
  .manyun-typography {
    margin-bottom: 0;
  }
`;
const getColumns = (normalizedList: []) => [
  {
    title: '三级分类',
    dataIndex: 'deviceType',
    key: 'deviceType',
    width: 350,
    render: (value: string) => (
      <Typography.Text>{getDeviceTypeName(value, normalizedList)}</Typography.Text>
    ),
  },
  {
    title: '设备编号',
    dataIndex: 'deviceName',
    key: 'deviceName',
    width: 350,
    render: (value: string) => <Typography.Text>{value || '--'}</Typography.Text>,
  },
  {
    title: 'SN',
    dataIndex: 'serialNumber',
    key: 'serialNumber',
    width: 350,
    render: (value: string) => <Typography.Text>{value || '--'}</Typography.Text>,
  },
];
type StateType = {
  key: string;
  cardTitle: string;
  roomType: string;
  cardList: cardListData[];
  _roomGuid: string;
  roomGuid: string | undefined;
  detailsTable: boolean;
  tableData: tableDetailsData[];
  loading: boolean;
  total: number;
  pageSize: number;
  pageNum: number;
};
export function CardList({
  taskNo,
  roomGuid,
  normalizedList,
}: {
  taskNo: string;
  roomGuid: string;
  normalizedList: [];
}) {
  const init = useRef(true);
  const [state, setState] = useState<StateType>({
    key: '',
    cardTitle: '',
    cardList: [],
    roomType: '',
    roomGuid: undefined,
    _roomGuid: '',
    detailsTable: false,
    tableData: [],
    loading: true,
    total: 0,
    pageSize: 10,
    pageNum: 1,
  });
  const [checks, setChecks] = useState<{ roomType: string; switch: boolean }[]>([]);
  const paginationConfig = {
    total: state.total,
    current: state.pageNum,
    pageSize: state.pageSize,
    onChange: (pageNum: number, pageSize: number) => {
      setState({
        ...state,
        pageNum,
        pageSize,
      });
    },
  };
  useEffect(() => {
    if (!state.detailsTable) {
      (async () => {
        const { data, error } = await fetchListOfOnlineOfflineOrders({
          taskNo,
          roomGuid: state.roomGuid,
        });
        if (error) {
          return;
        }

        setState({ ...state, cardList: data.data });
        if (init.current) {
          const _roomTypes = data.data.map(i => i.roomType).filter(i => i != null);

          setChecks(
            [...new Set(_roomTypes)].map(roomType => ({
              roomType,
              switch: true,
            }))
          );
          init.current = false;
        }
      })();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [taskNo, state.roomType, state.roomGuid, state.key]);
  useEffect(() => {
    if (state.detailsTable) {
      (async () => {
        setState({ ...state, loading: true });
        const { data, error } = await fetchDetailsOfOnlineOfflineOrders({
          taskNo,
          roomGuid: state.roomGuid as string,
          pageSize: state.pageSize,
          pageNum: state.pageNum,
        });
        if (error) {
          setState({ ...state, loading: false });
          return;
        }
        setState({ ...state, loading: false, tableData: data.data, total: data.total });
      })();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state.detailsTable, state.roomGuid, state.pageNum, state.pageSize, taskNo]);
  return (
    <div>
      <Space direction="vertical" style={{ width: '100%' }}>
        <Space>
          {!state.detailsTable ? (
            <>
              {checks.map(check => (
                <Checkbox
                  key={check.roomType}
                  checked={check.switch}
                  onChange={e => {
                    setChecks(
                      checks.map(i => {
                        if (i.roomType === check.roomType) {
                          return { ...i, switch: e.target.checked };
                        }
                        return { ...i };
                      })
                    );
                  }}
                >
                  <RoomTypeText code={check.roomType} />
                </Checkbox>
              ))}
              <Input.Search
                style={{
                  width: 200,
                }}
                placeholder="请输入包间编号"
                allowClear
                value={state._roomGuid}
                onChange={e => {
                  setState({ ...state, _roomGuid: e.target.value });
                }}
                onSearch={v => {
                  setState({ ...state, roomGuid: v });
                }}
              />
            </>
          ) : (
            <>
              <div
                style={{ paddingRight: '10px' }}
                onClick={() => {
                  setState({ ...state, detailsTable: false, roomGuid: undefined, key: nanoid() });
                }}
              >
                <ArrowLeftOutlined />
              </div>
              <Typography.Text style={{ fontSize: '20px', fontWeight: 600 }} strong>
                {state.cardTitle}
              </Typography.Text>
            </>
          )}
        </Space>
        {!state.detailsTable ? (
          <StyledGrid>
            {state.cardList.map(item => {
              const roomTag = item.roomGuid.split('.')?.at(-1) ?? '';
              return (
                checks.find(i => i.roomType === item.roomType)?.switch && (
                  <StyledCard
                    key={item.roomGuid}
                    onClick={() => {
                      setState({
                        ...state,
                        detailsTable: true,
                        cardTitle: item.roomName as string,
                        roomGuid: item.roomGuid,
                        pageSize: 10,
                        pageNum: 1,
                      });
                    }}
                  >
                    <div className="card-title">
                      <Typography.Text ellipsis={{ tooltip: item.roomName }}>
                        {roomTag} {item.roomName}
                      </Typography.Text>
                    </div>
                    <Space>
                      <Typography.Text>数量</Typography.Text>
                      <Typography.Text> {item.deviceNum}台</Typography.Text>
                    </Space>
                  </StyledCard>
                )
              );
            })}
          </StyledGrid>
        ) : (
          <Table
            columns={getColumns(normalizedList)}
            rowKey="id"
            loading={state.loading}
            dataSource={state.tableData}
            pagination={state.total > 10 && paginationConfig}
          />
        )}
      </Space>
    </div>
  );
}
