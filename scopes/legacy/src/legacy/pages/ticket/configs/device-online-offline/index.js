import { BackendTaskStatus } from '@manyun/ticket.model.ticket';
import { TicketStatusSelect } from '@manyun/ticket.ui.ticket-status-select';

import { CURRENT_PAGE_TYPES } from '../../constants';
import TTR from './../../registries/ticket-type-registry';
import Detail from './components/detail';
// import DeviceTitle from './components/device-name';
import NewTicket from './components/new';

TTR.registerTicketType('on_off')
  .registerConfig({
    type: CURRENT_PAGE_TYPES.TICKETS,
    showNewBtn: true,
    showBatchCloseTicketBtn: false,
    showBatchTakeOverBtn: false,
    showRowSelection: false,
    showRevokeOperation: true,

    /**
     * @param {object} param1
     * @param {typeof import('./../../views/tickets/constants').DEFAULT_COLUMN_DATA_INDEX_MAP} param1.DEFAULT_COLUMN_DATA_INDEX_MAP
     * @param {any[]} param1.defaultColumns
     */
    mergeColumns: ({ DEFAULT_COLUMN_DATA_INDEX_MAP, defaultColumns }) => {
      const newColumns = [...defaultColumns].filter(
        ({ dataIndex }) =>
          dataIndex !== DEFAULT_COLUMN_DATA_INDEX_MAP.TASK_SLA &&
          dataIndex !== DEFAULT_COLUMN_DATA_INDEX_MAP.TASK_ASSIGNEE_NAME &&
          dataIndex !== DEFAULT_COLUMN_DATA_INDEX_MAP.TASK_RESPOND_TIME
      );
      const insertIdx = newColumns.findIndex(
        ({ dataIndex }) => dataIndex === DEFAULT_COLUMN_DATA_INDEX_MAP.BLOCK_TAG
      );
      newColumns.splice(insertIdx, 0, {
        title: '设备数量',
        dataIndex: 'subjectTag',
      });
      return newColumns;
    },

    /**
     * @param {object} param1
     * @param {typeof import('./../../views/tickets/constants').DEFAULT_FILTER_KEY_MAP} param1.DEFAULT_FILTER_KEY_MAP
     * @param {any[]} param1.defaultColumns
     */
    mergeFilters: ({ DEFAULT_FILTER_KEY_MAP, defaultFilters }) => {
      const newFilters = [...defaultFilters].filter(
        ({ key }) =>
          key !== DEFAULT_FILTER_KEY_MAP.TASK_ASSIGNEE &&
          key !== DEFAULT_FILTER_KEY_MAP.IS_DELAY &&
          key !== DEFAULT_FILTER_KEY_MAP.END_TIME
      );
      // newFilters.push({
      //   label: '设备名称',
      //   key: 'subjectTag',
      //   Comp: DeviceTitle,
      //   whitespace: 'trim',
      // });
      newFilters.splice(
        newFilters.findIndex(item => item.key === DEFAULT_FILTER_KEY_MAP.TICKET_STATE),
        1,
        {
          label: '工单状态',
          key: 'taskStatusList',
          initialProps: {
            optionFilter: option => option.value !== BackendTaskStatus.CLOSE_APPROVER,
            allowClear: true,
            mode: 'multiple',
          },
          Comp: TicketStatusSelect,
        }
      );
      return newFilters;
    },
  })
  .registerConfig({
    type: CURRENT_PAGE_TYPES.NEW_TICKET,
    content: NewTicket,
  })
  .registerConfig({
    type: CURRENT_PAGE_TYPES.SPECIFIC_TICKET,
    content: Detail,
    showRevocationBtn: true,
  });
