import React from 'react';

import { ApiSelect } from '@galiojs/awesome-antd';

import { Input } from '@manyun/base-ui.ui.input';

import { RELATE_BIZ_TYPE_OPTIONS } from '@manyun/dc-brain.legacy.pages/borrows-and-return/constants';

class RelateNo extends React.Component {
  render() {
    const { value, onChange } = this.props;
    return (
      <Input.Group compact>
        <ApiSelect
          allowClear
          showSearch
          trigger="onDidMount"
          style={{ width: 120 }}
          {...this.props}
          dataService={() => Promise.resolve(RELATE_BIZ_TYPE_OPTIONS)}
          value={value?.relateTaskType}
          onChange={p => onChange({ ...value, relateTaskType: p })}
        />
        <Input
          style={{ width: 200 }}
          allowClear
          {...this.props}
          value={value?.relateTaskNo}
          onChange={({ target }) => onChange({ ...value, relateTaskNo: target.value })}
        />
      </Input.Group>
    );
  }
}
export default RelateNo;
