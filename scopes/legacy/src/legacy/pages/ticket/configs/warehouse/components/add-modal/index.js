import Form from '@ant-design/compatible/es/form';
import { DownOutlined, DownloadOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { saveAs } from 'file-saver';
import cloneDeep from 'lodash.clonedeep';
import { nanoid } from 'nanoid';
import React, { Component } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Dropdown } from '@manyun/base-ui.ui.dropdown';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import { VendorModelSelect } from '@manyun/crm.ui.vendor-model-select';
import { VendorSelect } from '@manyun/crm.ui.vendor-select';
import { Upload } from '@manyun/dc-brain.ui.upload';
import { RoomTypes } from '@manyun/resource-hub.model.room';
import { downloadConsumableTemplate } from '@manyun/ticket.service.download-consumable-template';
import { fetchConsumableTemplateImport } from '@manyun/ticket.service.fetch-consumable-template-import';

import { AssetClassificationApiTreeSelect, TinyTable } from '@manyun/dc-brain.legacy.components';
import * as deviceService from '@manyun/dc-brain.legacy.services/deviceService';
import * as spareService from '@manyun/dc-brain.legacy.services/spareService';

import {
  ASSET_TYPE_KEY_MAPS,
  TASK_SUB_TYPE_KEY_MAPS,
  TASK_SUB_TYPE_TEXT_MAPS,
} from '../../constants';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 5 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 19 },
  },
};

const removeDuplicates = (array, keyFields) => {
  const uniqueMap = new Map();

  array.forEach(item => {
    const key = keyFields.map(field => item[field]).join('|');
    uniqueMap.set(key, item);
  });

  return Array.from(uniqueMap.values());
};
const getColumns = (ctx, type) => {
  const list = [
    {
      title: '一级分类',
      dataIndex: 'topCategory',
      type: [ASSET_TYPE_KEY_MAPS.SN_DEVICE, ASSET_TYPE_KEY_MAPS.NO_SN_DEVICE],
      render: (text, options) => {
        let type;
        let color;
        if (options.errMessage) {
          const is = Object.keys(options.errMessage)?.indexOf('topCategoryName') !== -1;
          type = is ? 'danger' : '';
          color = is ? 'red' : 'rgba(0,0,0,0)';
        }
        return (
          <Space>
            <Typography.Text type={type} ellipsis={{ tooltip: text }}>
              {text ?? '--'}
            </Typography.Text>
            <Tooltip title={options.errMessage?.topCategoryName}>
              <QuestionCircleOutlined style={{ color }} />
            </Tooltip>
          </Space>
        );
      },
    },
    {
      title: '二级分类',
      dataIndex: 'secondCategory',
      type: [ASSET_TYPE_KEY_MAPS.SN_DEVICE, ASSET_TYPE_KEY_MAPS.NO_SN_DEVICE],
      render: (text, options) => {
        let type;
        let color;
        if (options.errMessage) {
          const is = Object.keys(options.errMessage)?.indexOf('secondCategoryName') !== -1;
          type = is ? 'danger' : '';
          color = is ? 'red' : 'rgba(0,0,0,0)';
        }
        return (
          <Space>
            <Typography.Text type={type} ellipsis={{ tooltip: text }}>
              {text ?? '--'}
            </Typography.Text>
            <Tooltip title={options.errMessage?.secondCategoryName}>
              <QuestionCircleOutlined style={{ color }} />
            </Tooltip>
          </Space>
        );
      },
    },
    {
      title: '三级分类',
      dataIndex: 'deviceType',
      type: [ASSET_TYPE_KEY_MAPS.SN_DEVICE, ASSET_TYPE_KEY_MAPS.NO_SN_DEVICE],
      render: (text, options) => {
        let type;
        let color;
        if (options.errMessage) {
          const is = Object.keys(options.errMessage)?.indexOf('deviceTypeName') !== -1;
          type = is ? 'danger' : '';
          color = is ? 'red' : 'rgba(0,0,0,0)';
        }
        return (
          <Space>
            <Typography.Text type={type} ellipsis={{ tooltip: text }}>
              {text ?? '--'}
            </Typography.Text>
            <Tooltip title={options.errMessage?.deviceTypeName}>
              <QuestionCircleOutlined style={{ color }} />
            </Tooltip>
          </Space>
        );
      },
    },
    {
      title: '厂商',
      dataIndex: 'vendor',
      type: [ASSET_TYPE_KEY_MAPS.SN_DEVICE, ASSET_TYPE_KEY_MAPS.NO_SN_DEVICE],
      render: (vendor, options) => {
        let type;
        let color;
        if (options.errMessage) {
          const is = Object.keys(options.errMessage)?.indexOf('vendor') !== -1;
          type = is ? 'danger' : '';
          color = is ? 'red' : 'rgba(0,0,0,0)';
        }
        return (
          <Space>
            <Typography.Text type={type} ellipsis={{ tooltip: vendor }}>
              {vendor ?? '--'}
            </Typography.Text>
            <Tooltip title={options.errMessage?.vendor}>
              <QuestionCircleOutlined style={{ color }} />
            </Tooltip>
          </Space>
        );
      },
    },
    {
      title: '型号',
      dataIndex: 'productModel',
      type: [ASSET_TYPE_KEY_MAPS.SN_DEVICE, ASSET_TYPE_KEY_MAPS.NO_SN_DEVICE],
      render: (productModel, options) => {
        let type;
        let color;
        if (options.errMessage) {
          const is = Object.keys(options.errMessage)?.indexOf('productModel') !== -1;
          type = is ? 'danger' : '';
          color = is ? 'red' : 'rgba(0,0,0,0)';
        }
        return (
          <Space>
            <Typography.Text type={type} ellipsis={{ tooltip: productModel }}>
              {productModel ?? '--'}
            </Typography.Text>
            <Tooltip title={options.errMessage?.productModel}>
              <QuestionCircleOutlined style={{ color }} />
            </Tooltip>
          </Space>
        );
      },
    },
    {
      title: '供应商简称',
      dataIndex: 'supplyVendor',
      type: [ASSET_TYPE_KEY_MAPS.SN_DEVICE, ASSET_TYPE_KEY_MAPS.NO_SN_DEVICE],
      render: (supplyVendor, options) => {
        let type;
        let color;
        if (options.errMessage) {
          const is = Object.keys(options.errMessage)?.indexOf('supplyVendor') !== -1;
          type = is ? 'danger' : '';
          color = is ? 'red' : 'rgba(0,0,0,0)';
        }
        return (
          <Space>
            <Typography.Text type={type} ellipsis={{ tooltip: supplyVendor }}>
              {supplyVendor ?? '--'}
            </Typography.Text>
            <Tooltip title={options.errMessage?.supplyVendor}>
              <QuestionCircleOutlined style={{ color }} />
            </Tooltip>
          </Space>
        );
      },
    },
    {
      title: '数量',
      dataIndex: 'warehouseCount',
      type: [ASSET_TYPE_KEY_MAPS.SN_DEVICE, ASSET_TYPE_KEY_MAPS.NO_SN_DEVICE],
      render: (warehouseCount, options) => {
        let type;
        let color;
        if (options.errMessage) {
          const is =
            Object.keys(options.errMessage)?.indexOf('warehouseCount') !== -1 ? 'danger' : '';
          type = is ? 'danger' : '';
          color = is ? 'red' : 'rgba(0,0,0,0)';
        }
        return (
          <Space>
            <Typography.Text type={type} ellipsis={{ tooltip: warehouseCount }}>
              {warehouseCount ?? '--'}
            </Typography.Text>
            <Tooltip title={options.errMessage?.warehouseCount}>
              <QuestionCircleOutlined style={{ color }} />
            </Tooltip>
          </Space>
        );
      },
    },
  ];
  return list.filter(item => item.type.includes(type));
};

class AddModal extends Component {
  constructor(props) {
    super(props);
    this.editModelVisible = this.editModelVisible.bind(this);
  }
  state = {
    modalType: 'add',
    modelVisible: false,
    dataSource: [],
    count: 1,
    remain: null,
  };

  editModelVisible = ({ modalType = 'add' }) => {
    this.setState({ modelVisible: !this.state.modelVisible, remain: null, modalType });
    this.props.form.resetFields();
  };

  handleAdd = () => {
    const { count } = this.state;
    const { setDataList, dataList } = this.props;
    const list = cloneDeep(dataList);
    this.props.form.validateFields(async (err, values) => {
      if (err) {
        return;
      }
      const { cascaderList, vendorModel, deviceCount, supplyVendor } = values;
      const index = dataList.findIndex(
        item =>
          item.deviceType === cascaderList.thirdCategorycode &&
          item.vendor === vendorModel[0] &&
          item.productModel === vendorModel[1]
      );
      if (index > -1) {
        const item = list[index];
        item.warehouseCount = deviceCount;
        item.supplyVendor = Array.isArray(supplyVendor) ? supplyVendor.join(',') : null;
        setDataList(list);
      } else {
        const newData = {
          key: count,
          topCategory: cascaderList.firstCategoryCode,
          secondCategory: cascaderList.secondCategoryCode,
          deviceType: cascaderList.thirdCategorycode,
          vendor: vendorModel[0],
          supplyVendor: Array.isArray(supplyVendor) ? supplyVendor.join(',') : null,
          productModel: vendorModel[1],
          warehouseCount: deviceCount,
        };
        this.setState({
          count: count + 1,
        });
        setDataList([...dataList, newData]);
      }
      this.editModelVisible({ modalType: 'add' });
    });
  };

  getRemainCount = async vendorModel => {
    const { location, type, title, form, room } = this.props;
    const [vendor, productModel] = vendorModel;
    if (!vendor || !productModel) {
      this.setState({ remain: null });
      return;
    }
    if (title === TASK_SUB_TYPE_KEY_MAPS.IN_WAREHOUSE) {
      return;
    }
    const cascaderList = form.getFieldValue('cascaderList');
    const { response, error } =
      type === ASSET_TYPE_KEY_MAPS.SN_DEVICE
        ? await deviceService.fetchDeviceNum({
            deviceType: cascaderList.thirdCategorycode,
            idcTag: location[0],
            blockTag: location[1],
            vendor,
            productModel,
            roomTypeList: [RoomTypes.Warehouse],
            filterRoomGuid: typeof room === 'object' ? room.value : room,
          })
        : await spareService.fetchUsableNum({
            spareType: cascaderList.thirdCategorycode,
            locationGuid: location.join('.'),
            vendor,
            productModel,
            dimension: 'BLOCK',
          });
    if (error) {
      message.error(error);
      return;
    }
    this.setState({ remain: Number(response) });
  };
  uploadFile = async fd => {
    const { setDataList, dataList } = this.props;
    const { error, data } = await fetchConsumableTemplateImport(fd);
    if (error) {
      message.error(error.message);
      return;
    }
    if (data) {
      if (data.faultTotal === 0) {
        message.success('导入成功！');

        const afterTypeChangeRacks = data.excelCheckDtos.map(asset => ({
          id: nanoid(),
          topCategory: asset.data.topCategory,
          secondCategory: asset.data.secondCategory,
          deviceType: asset.data.deviceType,
          vendor: asset.data.vendor,
          productModel: asset.data.productModel,
          supplyVendor: asset.data.supplyVendor,
          warehouseCount: asset.data.warehouseCount,
        }));
        setDataList(
          removeDuplicates(
            [...cloneDeep(dataList), ...afterTypeChangeRacks],
            [
              'topCategory',
              'secondCategory',
              'deviceType',
              'vendor',
              'productModel',
              'supplyVendor',
            ]
          )
        );
        this.setState({ ...this.state, modelVisible: false, dataSource: [] });
        return;
      }
      const afterTypeChangeRacks = data.excelCheckDtos.map(asset => ({
        id: nanoid(),
        topCategory: asset.data.topCategoryName,
        secondCategory: asset.data.secondCategoryName,
        deviceType: asset.data.deviceTypeName,
        vendor: asset.data.vendor,
        productModel: asset.data.productModel,
        supplyVendor: asset.data.supplyVendor,
        warehouseCount: asset.data.warehouseCount,
        errMessage: asset.errMessage ?? {},
      }));
      this.setState({
        ...this.state,
        dataSource: afterTypeChangeRacks,
      });
    }
  };

  render() {
    const { form, location, type, title, existenceRelateTask, room, custom } = this.props;
    const { modelVisible, remain } = this.state;
    const { getFieldDecorator, getFieldValue, setFieldsValue } = form;

    return (
      <>
        <>
          {custom ? (
            <Dropdown
              menu={{
                items: [
                  {
                    key: 'add',
                    label: (
                      <div onClick={this.editModelVisible}>
                        添加{TASK_SUB_TYPE_TEXT_MAPS[title]}库
                      </div>
                    ),
                    disabled: controlButtonDisabled({
                      type,
                      title,
                      room,
                      location,
                      existenceRelateTask,
                    }),
                  },
                  {
                    key: 'import',
                    label: (
                      <div
                        onClick={() => {
                          this.editModelVisible({ modalType: 'template' });
                        }}
                      >
                        模版导入
                      </div>
                    ),
                  },
                ],
              }}
              onOpenChange={() => {}}
            >
              <Button type="primary" icon={<DownOutlined />} iconPosition="end">
                入库信息
              </Button>
            </Dropdown>
          ) : (
            <Button
              key="add"
              type="primary"
              disabled={controlButtonDisabled({ type, title, room, location, existenceRelateTask })}
              onClick={this.editModelVisible}
            >
              添加{TASK_SUB_TYPE_TEXT_MAPS[title]}库
            </Button>
          )}
        </>
        <Modal
          style={{ top: '60px' }}
          bodyStyle={this.state.modalType === 'template' ? { height: '83vh' } : {}}
          title={this.state.modalType === 'template' ? '模版导入' : '添加'}
          destroyOnClose
          width={this.state.modalType === 'template' ? 1152 : 600}
          open={modelVisible}
          footer={
            this.state.modalType === 'template' ? (
              false
            ) : (
              <>
                <Button onClick={this.editModelVisible}>取消</Button>
                <Button type="primary" onClick={this.handleAdd}>
                  确定
                </Button>
              </>
            )
          }
          onCancel={this.editModelVisible}
        >
          {this.state.modalType === 'template' ? (
            <TinyTable
              rowKey="key"
              columns={getColumns(this, 'snDevice')}
              actions={[
                <Upload
                  key="import"
                  accept=".csv,.xls,.xlsx"
                  showUploadList={false}
                  beforeUpload={file => {
                    const formData = new FormData();
                    formData.append('file', file);

                    this.uploadFile(formData);
                    return false;
                  }}
                >
                  <Button type="primary">模版导入</Button>
                </Upload>,

                <Button
                  key="download"
                  type="link"
                  compact
                  icon={<DownloadOutlined />}
                  onClick={async () => {
                    const { error, data } = await downloadConsumableTemplate();
                    if (error) {
                      message.error(error.message);
                      return;
                    }
                    saveAs(data, '耗材入库模板.xlsx');
                  }}
                >
                  下载模版
                </Button>,
              ]}
              dataSource={this.state.dataSource}
            />
          ) : (
            <Form {...formItemLayout} colon={false}>
              <Form.Item label="资产分类">
                {getFieldDecorator('cascaderList', {
                  rules: [
                    {
                      required: true,
                      message: '资产分类必选！',
                    },
                  ],
                })(
                  <AssetClassificationApiTreeSelect
                    style={{ width: 200 }}
                    dataType={[type]}
                    category="allCategoryCode"
                    trigger="onDidMount"
                    disabledDepths={[0, 1]}
                    onChange={() => {
                      form.resetFields(['vendorModel']);
                      this.setState({ remain: null });
                      setFieldsValue({ deviceCount: null });
                    }}
                  />
                )}
              </Form.Item>
              <Form.Item label="厂商、型号">
                {getFieldDecorator('vendorModel', {
                  rules: [
                    {
                      required: true,
                      message: '厂商型号必选！',
                      type: 'number',
                      transform: value => (value?.length === 2 ? 2 : false),
                    },
                  ],
                })(
                  <VendorModelSelect
                    style={{ width: 200 }}
                    allowClear
                    disabled={!getFieldValue('cascaderList')?.thirdCategorycode}
                    deviceType={getFieldValue('cascaderList')?.thirdCategorycode}
                    onChange={vendorModel => {
                      this.getRemainCount(vendorModel);
                      setFieldsValue({ deviceCount: null });
                      setFieldsValue({ vendorName: null });
                    }}
                  />
                )}
              </Form.Item>

              {type === ASSET_TYPE_KEY_MAPS.NO_SN_DEVICE &&
                title === TASK_SUB_TYPE_KEY_MAPS.IN_WAREHOUSE && (
                  <Form.Item label="供应商简称">
                    {getFieldDecorator('supplyVendor', {
                      rules: [
                        {
                          required: true,
                          message: '供应商简称必选！',
                        },
                      ],
                    })(<VendorSelect style={{ width: 200 }} mode="multiple" allowClear />)}
                  </Form.Item>
                )}

              <Form.Item label="数量">
                {getFieldDecorator('deviceCount', {
                  rules: [
                    {
                      required: true,
                      message: `数量必填`,
                    },
                    {
                      type: 'number',
                      message:
                        title === TASK_SUB_TYPE_KEY_MAPS.EX_WAREHOUSE
                          ? `不可超过库存数量${remain}`
                          : '最大取值为999999',
                      max: title === TASK_SUB_TYPE_KEY_MAPS.EX_WAREHOUSE ? remain : 999999,
                    },
                    { pattern: /^\d+$/, message: '设备数量必须为整数' },
                  ],
                })(
                  title === TASK_SUB_TYPE_KEY_MAPS.EX_WAREHOUSE ? (
                    <InputNumber
                      min={1}
                      max={remain || 1}
                      style={{ width: 200 }}
                      disabled={typeof remain !== 'number'}
                    />
                  ) : (
                    <InputNumber min={1} max={999999} style={{ width: 200 }} />
                  )
                )}
                {typeof remain === 'number' && (
                  <span
                    style={{ lineHeight: '40px', paddingLeft: 16 }}
                  >{`库存数量:${remain}`}</span>
                )}
              </Form.Item>
            </Form>
          )}
        </Modal>
      </>
    );
  }
}

export default Form.create()(AddModal);

function controlButtonDisabled({ title, type, room, location, existenceRelateTask }) {
  if (existenceRelateTask) {
    return existenceRelateTask;
  }

  if (title === TASK_SUB_TYPE_KEY_MAPS.IN_WAREHOUSE) {
    return false;
  }
  if (type === ASSET_TYPE_KEY_MAPS.SN_DEVICE) {
    //出库类型下有编号的需要外部先选包间信息
    return !room;
  }
  //出库类型下无编号的需要外部先选机房楼
  return !location?.[1];
}
