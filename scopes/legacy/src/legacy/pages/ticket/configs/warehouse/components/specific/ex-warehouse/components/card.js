import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import { useDeviceType } from '@manyun/resource-hub.hook.use-device-type';
import { generateBorrowAndReturnDetailLocation } from '@manyun/resource-hub.route.resource-routes';
import { SpaceText } from '@manyun/resource-hub.ui.space-text';
import { UnitMetaDataText } from '@manyun/resource-hub.ui.unit-meta-data-text';
import { generateTicketUrl } from '@manyun/ticket.route.ticket-routes';

import {
  AssetClassificationApiTreeSelect,
  DisplayCard,
  Ellipsis,
  GutterWrapper,
  TinyDescriptions,
  UserLink,
} from '@manyun/dc-brain.legacy.components';
import {
  RELATE_BIZ_TYPE_KEY_MAP,
  RELATE_BIZ_TYPE_TEXT_MAP,
} from '@manyun/dc-brain.legacy.pages/borrows-and-return/constants';
import { ticketActions } from '@manyun/dc-brain.legacy.redux/actions/ticketActions';
import { getDeviceTypeName, getTaskProperties } from '@manyun/dc-brain.legacy.utils/ticket';

import { ASSET_TYPE_KEY_MAPS } from '../../../../constants';

export class ExWarehouseCard extends Component {
  state = {
    deviceType: '',
    showTreeSelect: true,
  };
  componentDidMount() {
    this.props.fetchCardList();
    this.setState({ ...this.state, showTreeSelect: this.props.deviceInfo?.length >= 1 });
  }

  getDescriptionItem = () => {
    const { basicInfo, ticketTypeMapping } = this.props;
    const relateBizTypeMapping = {
      ...ticketTypeMapping,
      [RELATE_BIZ_TYPE_KEY_MAP.BORROW]: {
        taskValue: RELATE_BIZ_TYPE_TEXT_MAP[RELATE_BIZ_TYPE_KEY_MAP.BORROW],
      },
    };
    const properties = getTaskProperties(basicInfo);
    let exWarehouseReason = null;
    try {
      const reason = JSON.parse(properties.exWarehouseReason);
      exWarehouseReason = reason.otherWarehouseReason ?? reason.selectWarehouseReason.label;
    } catch (error) {
      exWarehouseReason = properties.exWarehouseReason;
    }
    const list = properties.numbered
      ? [
          {
            label: '出库位置',
            value: <SpaceText guid={basicInfo.blockTag} />,
          },
          {
            label: '目标位置',
            value: <SpaceText guid={basicInfo.roomGuid} />,
          },
        ]
      : [];

    return [
      ...list,
      {
        label: '领料人',
        value: <UserLink userName={properties.takeStaffName} userId={properties.takeStaffId} />,
      },
      {
        label: '出库原因',
        value: <span>{exWarehouseReason}</span>,
      },
      !!basicInfo.relateTaskNo && {
        label: '关联单号',
        value: showRelateBizType({
          relateTaskNo: basicInfo.relateTaskNo,
          relateBizType: basicInfo.relateBizType,
          mapping: relateBizTypeMapping,
        }),
      },
    ].filter(Boolean);
  };

  render() {
    const { normalizedList, deviceInfo, basicInfo } = this.props;

    return (
      <GutterWrapper mode="vertical" padding="0 16px">
        <GutterWrapper mode="horizontal">
          <TinyDescriptions
            key="description"
            column={2}
            descriptionsItems={this.getDescriptionItem()}
          />
          {(this.state.showTreeSelect || deviceInfo) && basicInfo.taskType !== 'WAREHOUSE' && (
            <AssetClassificationApiTreeSelect
              style={{ width: 200 }}
              dataType={[ASSET_TYPE_KEY_MAPS.SN_DEVICE, ASSET_TYPE_KEY_MAPS.NO_SN_DEVICE]}
              category="categorycode"
              trigger="onDidMount"
              disabledDepths={[0, 1]}
              allowClear
              value={this.state.deviceType}
              onChange={e => {
                this.setState({ deviceType: e });
                this.props.fetchCardList(e);
              }}
            />
          )}
        </GutterWrapper>
        <div
          style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fill,minmax(244px,1fr))',
            gridRowGap: '58px',
            gridColumnGap: '16px',
          }}
        >
          {deviceInfo?.map(card => (
            <DisplayCard
              key={card.productModel}
              title={
                <Ellipsis tooltip lines={1}>
                  {getDeviceTypeName(card.deviceType, normalizedList)}
                </Ellipsis>
              }
              describe={
                <>
                  {card.deviceCount}
                  <DeviceTypeUnitText deviceType={card.deviceType} />
                </>
              }
              subDescribe={
                <GutterWrapper size=".5em" mode="vertical" style={{ wordBreak: 'break-all' }}>
                  <div>{`厂商：${card.vendor}`}</div>
                  <div>{`型号：${card.productModel}`}</div>
                </GutterWrapper>
              }
              countList={[
                {
                  describe: '已出库',
                  number: card.exWarehouseCount,
                  color: 'success',
                },
                { describe: '未出库', number: card.remainCount, color: 'error' },
              ]}
              onClick={() => this.props.setWareHouseCheckItem(card)}
            />
          ))}
        </div>
      </GutterWrapper>
    );
  }
}

const mapStateToProps = ({
  ticket: {
    wareHouse: { detail },
    ticketView,
  },
  common: { deviceCategory, ticketTypes },
}) => ({
  deviceInfo: detail.deviceInfo,
  normalizedList: deviceCategory?.normalizedList,
  basicInfo: ticketView?.basicInfo,
  ticketTypeMapping: ticketTypes?.normalizedList || {},
});
const mapDispatchToProps = {
  setWareHouseCheckItem: ticketActions.setWareHouseCheckItem,
};
export default connect(mapStateToProps, mapDispatchToProps)(ExWarehouseCard);

/**
 * 显示工单关联单号
 *
 * @param {Object} param
 * @param {string | undefined} param.relateTaskNo 工单关联单号
 * @param {string | undefined} param.relateBizType 工单关联单号类型
 * @param {Object} param.mapping 工单类型的normalizedList加上借用归还类型
 *
 * @returns  {string}
 */
export function showRelateBizType({ relateTaskNo, relateBizType, mapping }) {
  let txt = relateTaskNo;
  const typeTxt = mapping[relateBizType]?.taskValue;
  if (typeTxt) {
    txt += `（${typeTxt}）`;
  }
  const relateBizTypeInLowerCase = relateBizType.toLocaleLowerCase();
  return (
    <Link
      target="_blank"
      to={
        relateBizTypeInLowerCase === 'borrow'
          ? generateBorrowAndReturnDetailLocation({ id: relateTaskNo })
          : generateTicketUrl({ ticketType: relateBizTypeInLowerCase, id: relateTaskNo })
      }
    >
      {txt}
    </Link>
  );
}

function DeviceTypeUnitText({ deviceType }) {
  const deviceTypeInfos = useDeviceType(deviceType);

  if (!deviceTypeInfos?.unitCode) {
    return '台';
  }

  return (
    <UnitMetaDataText
      style={{ maxWidth: 130 }}
      ellipsis={{
        tooltip: true,
      }}
      unitCode={deviceTypeInfos.unitCode}
    />
  );
}
