import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import QuestionCircleOutlined from '@ant-design/icons/es/icons/QuestionCircleOutlined';

import { Button } from '@manyun/base-ui.ui.button';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';

import { GutterWrapper, TinyTable } from '@manyun/dc-brain.legacy.components';
import { getDeviceTypeName } from '@manyun/dc-brain.legacy.utils/ticket';

import { TASK_SUB_TYPE_KEY_MAPS } from '../../../constants';
import AddModal from '../../add-modal';

const getColumns = ctx => [
  {
    title: '一级分类',
    dataIndex: 'topCategory',
    render: text => getDeviceTypeName(text, ctx.props.normalizedList),
  },
  {
    title: '二级分类',
    dataIndex: 'secondCategory',
    render: text => getDeviceTypeName(text, ctx.props.normalizedList),
  },
  {
    title: '三级分类',
    dataIndex: 'deviceType',
    render: text => getDeviceTypeName(text, ctx.props.normalizedList),
  },
  {
    title: '厂商',
    dataIndex: 'vendor',
  },
  {
    title: '型号',
    dataIndex: 'productModel',
  },
  {
    title: '数量',
    dataIndex: 'warehouseCount',
  },
  {
    title: '操作',
    dataIndex: 'action',
    render: (__, record) => (
      <Button compact type="link" onClick={() => ctx.handleDelete(record.key)}>
        移除
      </Button>
    ),
  },
];

class ExWarehouse extends Component {
  state = {
    deviceModelList: [],
    count: 1,
  };

  componentDidMount() {
    this.setDeviceModelList(this.props.devices);
  }

  componentDidUpdate(prevProps) {
    const { type, room, devices } = this.props;
    if (type !== prevProps.type || room !== prevProps.room) {
      this.setDeviceModelList([]);
    }
    if (devices !== prevProps.devices) {
      this.setDeviceModelList(devices);
    }
    const { refetchDevice } = this.props;
    if (prevProps.refetchDevice !== refetchDevice) {
      this.props.getdeviceWithRelateTask().then(value => {
        this.setState({ deviceModelList: value });
      });
    }
  }

  handleDelete = key => {
    const { deviceModelList } = this.state;
    const dataSource = [...deviceModelList];
    this.setDeviceModelList(key ? dataSource.filter(item => item.key !== key) : []);
  };

  setDeviceModelList = data => {
    const { getList } = this.props;
    this.setState({ deviceModelList: data }, () => {
      getList(this.state.deviceModelList);
    });
  };

  render() {
    const { deviceModelList } = this.state;
    const { location, type, room, existenceRelateTask } = this.props;

    return (
      <GutterWrapper mode="vertical">
        <TinyTable
          align="left"
          style={{ marginBottom: 40 }}
          rowKey="key"
          columns={getColumns(this)}
          dataSource={deviceModelList}
          actionsWrapperStyle={{ alignItems: 'center' }}
          actions={
            !existenceRelateTask
              ? [
                  <AddModal
                    key="modal"
                    room={room}
                    type={type}
                    location={location}
                    title={TASK_SUB_TYPE_KEY_MAPS.EX_WAREHOUSE}
                    dataList={deviceModelList}
                    setDataList={this.setDeviceModelList}
                    existenceRelateTask={existenceRelateTask}
                  />,
                  <Button
                    key="remove"
                    disabled={!deviceModelList || !deviceModelList.length}
                    onClick={e => {
                      e.persist();
                      this.handleDelete();
                    }}
                  >
                    全部移除
                  </Button>,
                  <Tooltip key="info" title="选择设备必须属于当前工单相同楼栋位置内">
                    <QuestionCircleOutlined />
                  </Tooltip>,
                ]
              : []
          }
        />
      </GutterWrapper>
    );
  }
}

const mapStateToProps = ({ common: { deviceCategory } }) => ({
  normalizedList: deviceCategory?.normalizedList,
});

export default connect(mapStateToProps, null)(Form.create({ name: 'create-ex' })(ExWarehouse));
