import Form from '@ant-design/compatible/es/form';
import { UploadOutlined } from '@ant-design/icons';
import { ApiSelect, Select } from '@galiojs/awesome-antd';
import { isArray } from 'lodash';
import get from 'lodash/get';
import React, { Component } from 'react';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';
import shortid from 'shortid';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Space } from '@manyun/base-ui.ui.space';
import { RoomTypes } from '@manyun/resource-hub.model.room';
import { LocationCascader as ResourceHubLocationCascader } from '@manyun/resource-hub.ui.location-cascader';
import { SlaUnit } from '@manyun/ticket.model.task';
import { createInWarehouseTicket } from '@manyun/ticket.service.create-in-warehouse-ticket';
import { recreateWarehouseOutTicket } from '@manyun/ticket.service.recreate-warehouse-out-ticket';
import { SlaSelect, generateCorrectJobSla } from '@manyun/ticket.ui.sla-select';

import {
  FooterToolBar,
  GutterWrapper,
  LocationCascader,
  TinyCard,
} from '@manyun/dc-brain.legacy.components';
import { METADATA_TYPE } from '@manyun/dc-brain.legacy.constants';
import {
  BORROW_TYPE_KEY_MAP,
  RELATE_BIZ_TYPE_KEY_MAP,
} from '@manyun/dc-brain.legacy.pages/borrows-and-return/constants';
import { StyledMcUpload } from '@manyun/dc-brain.legacy.pages/ticket/configs/access-door/styled';
import { getFileInfoList } from '@manyun/dc-brain.legacy.pages/ticket/configs/access-door/utils';
import {
  borrowsAndReturnActions,
  getBorrowInfoActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/borrowsAndReturnActions';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import { redirectActionCreator } from '@manyun/dc-brain.legacy.redux/actions/routerActions';
import { getTicketFilesActionCreator } from '@manyun/dc-brain.legacy.redux/actions/ticketActions';
import { borrowsAndReturnService } from '@manyun/dc-brain.legacy.services';
import * as metadataConfigurationService from '@manyun/dc-brain.legacy.services/metadataConfigurationService';
import * as roomService from '@manyun/dc-brain.legacy.services/roomService';
import * as taskCenterService from '@manyun/dc-brain.legacy.services/taskCenterService';
import * as ticketService from '@manyun/dc-brain.legacy.services/ticketService';
import { getLocationSearchMap } from '@manyun/dc-brain.legacy.utils';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';

import TicketSubType from '../../../../../views/tickets/components/filters/ticket-sub-type';
import {
  ASSET_TYPE_KEY_MAPS,
  ASSET_TYPE_TEXT_MAP,
  TASK_SUB_TYPE_KEY_MAPS,
  TASK_SUB_TYPE_TEXT_MAPS,
} from '../../../constants';
import ExWarehouse from '../ex-warehouse';
import InWarehouse from '../in-warehouse';
import RelateNo from './components/relate-no';

const width = '320px';
class PublicWarehouse extends Component {
  state = {
    editable: false,
    deviceModelList: [],
    assetList: [],
    loading: false,
    [METADATA_TYPE.IN_WAREHOUSE_REASON]: [],
    [METADATA_TYPE.EX_WAREHOUSE_REASON]: [],
    refetchDevice: 0,
    varsMap: {},
    fileInfoList: [],
    otherWarehouseReason: undefined,
  };

  componentDidMount() {
    const {
      match: { params },
    } = this.props;
    this.props.syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });
    this.getList(METADATA_TYPE.IN_WAREHOUSE_REASON);
    this.getList(METADATA_TYPE.EX_WAREHOUSE_REASON);
    if (params.id) {
      this.getInfoData(params.id);
      this.fetchDeviceModelList(params.id);
      return;
    }
    const { search } = this.props.location;
    const { variables } = getLocationSearchMap(search, ['variables']);
    try {
      const varsMap = JSON.parse(variables) || {};
      // 请求借用归还详情
      if (varsMap.relateTaskNo && varsMap.relateTaskType === RELATE_BIZ_TYPE_KEY_MAP.BORROW) {
        this.props.getBorrowInfo({ borrowNo: varsMap.relateTaskNo });
      }
      this.setState({
        varsMap,
      });
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log(error);
    }
  }

  componentDidUpdate(prevProps) {
    if (!prevProps.borrowInfo.borrowNo && this.props.borrowInfo.borrowNo) {
      this.setState(({ refetchDevice }) => ({ refetchDevice: refetchDevice + 1 }));
    }
  }

  componentWillUnmount() {
    // 如果是从借用归还跳转至出入库，离开时清空借用详情
    if (
      this.state.varsMap.relateTaskNo &&
      this.state.varsMap.relateTaskType === RELATE_BIZ_TYPE_KEY_MAP.BORROW
    ) {
      this.props.resetBorrowDetail();
    }
  }

  fetchDeviceModelList = async taskNo => {
    const { response, error } = await taskCenterService.fetchExWarehouseDeviceCard({
      taskNo,
    });
    if (error) {
      message.error(error);
      return;
    }
    this.setState({
      deviceModelList: response.data.map(device => ({
        key: shortid(),
        topCategory: device.deviceType.slice(0, 1),
        secondCategory: device.deviceType.slice(0, 3),
        deviceType: device.deviceType,
        vendor: device.vendor,
        productModel: device.productModel,
        warehouseCount: device.deviceCount,
      })),
    });
  };

  getInfoData = async taskNo => {
    const { response, error } = await ticketService.fetchTicketBasicInfo({
      taskNo,
    });
    if (error) {
      message.error(error);
      return;
    }
    try {
      const varsMap = JSON.parse(response.taskProperties);
      const reason = initialReason(JSON.parse(response.taskProperties));

      this.setState({
        editable: true,
        otherWarehouseReason: varsMap.relateTaskType ? undefined : reason.otherWarehouseReason,
      });
      const [_idc, _block] = response.roomGuid?.split('.');
      this.props.form.setFieldsValue({
        taskSubType: response.taskSubType,
        taskTitle: response.taskTitle,
        type: varsMap.numbered ? ASSET_TYPE_KEY_MAPS.SN_DEVICE : ASSET_TYPE_KEY_MAPS.NO_SN_DEVICE,
        taskStaff: {
          id: varsMap.takeStaffId,
          label: varsMap.takeStaffName,
        },
        taskSla: {
          sla: generateCorrectJobSla(response.taskSla, response.unit),
          unit: response.unit,
        },
        taskNo: response.relateBizType ? null : response.relateTaskNo,
        location: [response.idcTag, response.blockTag.split('.')[1]],
        // 入库无撤回逻辑 目前都是 ===
        roomGuid:
          response.taskSubType === TASK_SUB_TYPE_KEY_MAPS.EX_WAREHOUSE
            ? [_idc, `${_idc}.${_block}`, response.roomGuid]
            : response.roomGuid,
        selectWarehouseReason: reason.reasonInfo,
      });
    } catch (error) {}
    this.props.getTicketFiles({
      targetId: response.taskNo,
      targetType: response.taskType,
      extensionType: 'BASE_FILE',
      callback: res => {
        this.setState({
          fileInfoList: res,
        });
      },
    });
  };

  getList = async metadataType => {
    const { response, error } =
      await metadataConfigurationService.fetchMetadataByType(metadataType);
    if (response) {
      this.setState({
        [metadataType]: response.data,
      });
    }
    if (error) {
      message.error(error);
    }
  };

  goDetail = taskNo => {
    const { ticketType } = this.props;
    this.props.redirect(
      urls.generateTicketDetailUrl({
        ticketType,
        id: taskNo,
      })
    );
  };

  getQ = valueMap => {
    const { deviceModelList, assetList } = this.state;
    let assetNoList = [];
    const {
      location,
      selectWarehouseReason,
      otherWarehouseReason,
      taskTitle,
      taskSubType,
      roomGuid,
      taskStaff,
      type,
      relateTask,
      taskNo,
      taskSla,
    } = valueMap;
    const fileList = getFileInfoList(this.state.fileInfoList);
    const params = {
      taskTitle,
      idcTag: location[0],
      blockGuid: location.join('.'),
      roomGuid: Array.isArray(roomGuid) ? roomGuid.at(-1) : roomGuid,
      numbered: type === ASSET_TYPE_KEY_MAPS.SN_DEVICE,
      relateTaskNo: relateTask?.relateTaskNo || taskNo?.trim() || null,
      relateBizType: relateTask?.relateTaskType,
      fileInfoList: fileList,
    };
    if (taskSubType === TASK_SUB_TYPE_KEY_MAPS.EX_WAREHOUSE) {
      params.taskStaffId = taskStaff.id;
      params.deviceModelList = deviceModelList;
      params.exWarehouseReason = generateReason(otherWarehouseReason, selectWarehouseReason);
      params.viewReason = otherWarehouseReason ? otherWarehouseReason : selectWarehouseReason.label;
      params.taskSla = taskSla.sla;
      params.unit = taskSla.unit;
    }
    if (taskSubType === TASK_SUB_TYPE_KEY_MAPS.IN_WAREHOUSE) {
      if (params.numbered) {
        assetList.forEach(item => (assetNoList = [...assetNoList, { assetNo: item.assetNo }]));
        params.deviceModelList = assetNoList;
      } else {
        params.deviceModelList = assetList;
      }
      params.inWarehouseReason = generateReason(otherWarehouseReason, selectWarehouseReason);
    }
    return params;
  };

  handleSubmit = async valueMap => {
    const isExWarehouseTicket = valueMap.taskSubType === TASK_SUB_TYPE_KEY_MAPS.EX_WAREHOUSE;
    const {
      match: { params },
    } = this.props;
    const { deviceModelList, assetList } = this.state;
    if (deviceModelList.length === 0 && assetList?.length === 0) {
      message.error('设备列表不能为空！');
      return;
    }
    const p = this.getQ(valueMap);
    this.setState({ loading: true });
    if (params.id && isExWarehouseTicket) {
      const { data, error } = await recreateWarehouseOutTicket({ ...p, taskNo: params.id });
      this.setState({ loading: false });
      if (error) {
        message.error(error.message);
      } else {
        message.success('重新发起成功！');
        setTimeout(() => {
          this.goDetail(data);
        }, 1.5 * 1000);
      }
      return;
    }
    if (isExWarehouseTicket) {
      const { response, error } = await taskCenterService.createExWarehousTicket(p);
      this.setState({ loading: false });
      if (error) {
        message.error(error);
        return;
      }
      message.success('创建成功！');
      this.goDetail(response);
    } else {
      const { data, error } = await createInWarehouseTicket(p);
      this.setState({ loading: false });
      if (error) {
        message.error(error.message);
        return;
      }
      message.success(data.respMsg);
      this.goDetail(data.taskNo);
    }
  };

  getDefaultTitle = location => {
    const { form } = this.props;
    const { getFieldsValue, setFieldsValue } = form;
    const { taskTitle, taskSubType, type } = getFieldsValue();
    if (taskTitle) {
      return;
    }
    location.length === 2 &&
      setFieldsValue({
        taskTitle: `${location.join('.')}${TASK_SUB_TYPE_TEXT_MAPS[taskSubType]}库${
          ASSET_TYPE_TEXT_MAP[type]
        }`,
      });
  };

  getDeviceModelList = list => {
    this.setState({ deviceModelList: list });
  };

  getAssetList = list => {
    this.setState({ assetList: list });
  };

  getExistenceRelateTask = () => {
    const {
      form: { getFieldValue },
    } = this.props;
    const relateTask = getFieldValue('relateTask');
    if (
      relateTask &&
      relateTask.relateTaskType &&
      relateTask.relateTaskNo &&
      relateTask.relateTaskType === RELATE_BIZ_TYPE_KEY_MAP.BORROW
    ) {
      return true;
    }
    return false;
  };

  setRefetchDevice = params => {
    const {
      form: { getFieldValue },
    } = this.props;
    const relateTask = getFieldValue('relateTask');
    const numbered = getFieldValue('type') === ASSET_TYPE_KEY_MAPS.SN_DEVICE ? 1 : 0;
    const room = getFieldValue('room');
    const p = {
      relateTask,
      numbered,
      room,
      ...params,
    };
    if (
      p.relateTask &&
      p.relateTask.relateTaskNo &&
      p.numbered &&
      p.room &&
      this.state.varsMap.relateTaskType === RELATE_BIZ_TYPE_KEY_MAP.BORROW
    ) {
      this.setState(({ refetchDevice }) => ({ refetchDevice: refetchDevice + 1 }));
      return;
    }
    if (p.relateTask && p.relateTask.relateTaskNo && !p.numbered) {
      this.setState(({ refetchDevice }) => ({ refetchDevice: refetchDevice + 1 }));
    }
  };

  getdeviceWithRelateTask = async () => {
    const {
      form: { getFieldValue },
    } = this.props;
    const relateTask = getFieldValue('relateTask');
    const numbered = getFieldValue('type') === ASSET_TYPE_KEY_MAPS.SN_DEVICE ? 1 : 0;
    const roomGuid = getFieldValue('roomGuid');
    if (numbered && !roomGuid) {
      return;
    }
    const params = {
      borrowNo: relateTask?.relateTaskNo,
      roomGuid: Array.isArray(roomGuid) ? roomGuid.at(-1) : roomGuid,
      numbered,
    };
    const { response, error } = await borrowsAndReturnService.fetchBorrowSaaetExWarehouse(params);
    if (error) {
      message.error(error);
      return [];
    }
    const data = response.data.map(item => {
      return {
        ...item,
        key: shortid(),
        topCategory: item.topCategory,
        secondCategory: item.secondCategory,
        deviceType: item.deviceType,
        vendor: item.vendor,
        productModel: item.productModel,
        warehouseCount: item.canStockOutNum,
      };
    });
    this.setState({
      deviceModelList: data,
    });
    return data;
  };

  getTaskStaffInitialValue = () => {
    const { borrowInfo, userName, userId } = this.props;
    const { varsMap } = this.state;
    if (!varsMap.relateTaskNo) {
      return;
    }
    if (varsMap.relateTaskType === RELATE_BIZ_TYPE_KEY_MAP.REPAIR) {
      return {
        key: userId,
        id: userId,
        label: userName,
      };
    }
    if (borrowInfo.borrowType === BORROW_TYPE_KEY_MAP.INNER) {
      return {
        key: String(borrowInfo.borrower),
        id: String(borrowInfo.borrower),
        label: borrowInfo.borrowerName,
      };
    }
    if (borrowInfo.borrowType === BORROW_TYPE_KEY_MAP.OUT) {
      return {
        key: String(borrowInfo.personLiable),
        id: String(borrowInfo.personLiable),
        label: borrowInfo.personLiableName,
      };
    }
    return;
  };

  render() {
    const { form, history, ticketType, borrowInfo } = this.props;
    const { loading, refetchDevice, varsMap, editable } = this.state;
    const { getFieldDecorator, getFieldsValue, validateFields, setFieldsValue } = form;
    const {
      taskSubType,
      location,
      type,
      roomGuid: _guid,
      selectWarehouseReason,
    } = getFieldsValue();
    const isExWarehouseTicket = taskSubType === TASK_SUB_TYPE_KEY_MAPS.EX_WAREHOUSE;
    const isRelateTicketRepair = String(varsMap.relateTaskType) === 'REPAIR';
    const roomGuid = Array.isArray(_guid) ? _guid.at(-1) : _guid;
    const idc = Array.isArray(getFieldsValue().location)
      ? getFieldsValue().location?.[0]
      : undefined;

    return (
      <GutterWrapper mode="vertical">
        <TinyCard title="基本信息">
          <GutterWrapper mode="vertical">
            <Form colon={false}>
              <Form.Item label="工单子类型" style={{ marginBottom: '16px' }}>
                {getFieldDecorator('taskSubType', {
                  rules: [{ required: true, message: '工单子类型必选！' }],
                  initialValue: TASK_SUB_TYPE_KEY_MAPS.EX_WAREHOUSE,
                })(
                  <TicketSubType
                    allowClear={false}
                    ticketType={ticketType}
                    style={{ width }}
                    disabled={varsMap.relateTaskNo || editable ? true : false}
                    onChange={() => {
                      setFieldsValue({
                        location: null,
                        selectWarehouseReason: undefined,
                        otherWarehouseReason: null,
                        taskTitle: null,
                        taskStaffId: null,
                        roomGuid: null,
                      });
                    }}
                  />
                )}
              </Form.Item>
              <Form.Item label=" " style={{ marginLeft: 80, marginBottom: 13 }}>
                {getFieldDecorator('type', {
                  initialValue: (() => {
                    if (borrowInfo.applyAssetType) {
                      if (
                        borrowInfo.applyAssetType === 'all' ||
                        borrowInfo.applyAssetType === 'device'
                      ) {
                        return ASSET_TYPE_KEY_MAPS.SN_DEVICE;
                      }
                      return ASSET_TYPE_KEY_MAPS.NO_SN_DEVICE;
                    }
                    return ASSET_TYPE_KEY_MAPS.SN_DEVICE;
                  })(),
                })(
                  <Radio.Group
                    disabled={
                      borrowInfo.applyAssetType && borrowInfo.applyAssetType !== 'all'
                        ? true
                        : false
                    }
                    onChange={({ target: { value } }) => {
                      // 新建借用出库时，切换类型，不改变位置和标题
                      if (!varsMap.relateTaskNo) {
                        setFieldsValue({
                          location: null,
                          taskTitle: null,
                        });
                        return;
                      }
                      if (varsMap.relateTaskType === RELATE_BIZ_TYPE_KEY_MAP.BORROW) {
                        this.setRefetchDevice({
                          numbered: value === ASSET_TYPE_KEY_MAPS.SN_DEVICE ? 1 : 0,
                        });
                      }
                    }}
                  >
                    <Radio value={ASSET_TYPE_KEY_MAPS.SN_DEVICE}>
                      {ASSET_TYPE_TEXT_MAP[ASSET_TYPE_KEY_MAPS.SN_DEVICE]}
                    </Radio>
                    <Radio value={ASSET_TYPE_KEY_MAPS.NO_SN_DEVICE}>
                      {ASSET_TYPE_TEXT_MAP[ASSET_TYPE_KEY_MAPS.NO_SN_DEVICE]}
                    </Radio>
                  </Radio.Group>
                )}
              </Form.Item>
              {isExWarehouseTicket && (
                <Form.Item label="领料人" style={{ marginLeft: 28, marginBottom: 17 }}>
                  {getFieldDecorator('taskStaff', {
                    rules: [
                      {
                        required: true,
                        message: '领料人必选！',
                      },
                    ],
                    initialValue: this.getTaskStaffInitialValue(),
                  })(<UserSelect placeholder="请输入用户名称" allowClear style={{ width }} />)}
                </Form.Item>
              )}
              <Form.Item
                label={isExWarehouseTicket ? '出库位置' : '入库位置'}
                style={{ marginLeft: 14, marginBottom: 17 }}
              >
                {getFieldDecorator('location', {
                  rules: [
                    {
                      required: true,
                      message: '必须选到楼栋！',
                    },
                  ],
                  initialValue:
                    varsMap.relateTaskType === RELATE_BIZ_TYPE_KEY_MAP.REPAIR
                      ? varsMap.blockTag?.split('.')
                      : borrowInfo.blockGuid?.split('.'),
                })(
                  <LocationCascader
                    changeOnSelect={false}
                    style={{ width }}
                    currentAuthorize
                    disabled={varsMap.relateTaskNo ? true : false}
                    onChange={e => {
                      this.getDefaultTitle(e, 'location');
                      setFieldsValue({
                        roomGuid: null,
                      });
                    }}
                  />
                )}
              </Form.Item>
              {varsMap.relateTaskNo && (
                <Form.Item label="关联单号" style={{ marginLeft: 25, marginBottom: 17 }}>
                  {getFieldDecorator('relateTask', {
                    initialValue: {
                      relateTaskType: varsMap.relateTaskType,
                      relateTaskNo: varsMap.relateTaskNo,
                    },
                    rules: [
                      {
                        message: '关联单号必选！',
                        type: 'number',
                        transform: value =>
                          !varsMap.relateTaskNo ||
                          (value && value.relateTaskType && value.relateTaskNo)
                            ? 2
                            : false,
                      },
                    ],
                  })(
                    <RelateNo
                      disabled={!!varsMap.relateTaskNo}
                      onChange={relateTask => {
                        this.setRefetchDevice({ relateTask });
                      }}
                    />
                  )}
                </Form.Item>
              )}
              {(!isExWarehouseTicket || type === ASSET_TYPE_KEY_MAPS.SN_DEVICE) && (
                <Form.Item
                  label={isExWarehouseTicket ? '目标位置' : '入仓库至'}
                  style={{
                    marginLeft: 14,
                    marginBottom: 17,
                  }}
                >
                  {getFieldDecorator('roomGuid', {
                    rules: [
                      {
                        required: true,
                        message: '仓库包间必选！',
                      },
                      {
                        validator: (rule, value, callback) => {
                          if (isExWarehouseTicket && type === ASSET_TYPE_KEY_MAPS.SN_DEVICE) {
                            if (!value || value.length === 0) {
                              callback(); // 让 required 规则处理
                              return;
                            }
                            if (value.length === 3) {
                              callback();
                            } else {
                              callback('请选择到包间');
                            }
                          } else {
                            callback();
                          }
                        },
                      },
                    ],

                    // getValueFromEvent: value => {
                    //   if (isExWarehouseTicket && type === ASSET_TYPE_KEY_MAPS.SN_DEVICE) {
                    //     // 假设 value 是 Cascader 的数组，比如 ['IDC_GUID', 'BLOCK_GUID']
                    //     // 如果 'location' 字段只想存 BLOCK_GUID，那么就返回 value[1]
                    //     if (isArray(value) && value.length === 3) {
                    //       return value[value.length - 1];
                    //     }
                    //     return undefined;
                    //   }
                    //   return value;
                    // },
                  })(
                    isExWarehouseTicket && type === ASSET_TYPE_KEY_MAPS.SN_DEVICE ? (
                      <ResourceHubLocationCascader
                        style={{ width }}
                        authorizedOnly
                        allowClear
                        idc={idc}
                        disabledNoChildsNodes={['IDC', 'BLOCK']}
                        nodeTypes={['IDC', 'BLOCK', 'ROOM']}
                        onChange={value => {
                          if (value?.length === 3) {
                            this.setRefetchDevice({ room: value[2] });
                            return;
                          }
                          this.setRefetchDevice({ room: undefined });
                        }}
                      />
                    ) : (
                      <ApiSelect
                        disabled={location?.length !== 2}
                        fieldNames={{
                          value:
                            varsMap.relateTaskNo &&
                            varsMap.relateTaskType === RELATE_BIZ_TYPE_KEY_MAP.BORROW
                              ? 'roomGuid'
                              : 'guid',
                          label: 'tag',
                        }}
                        showSearch
                        allowClear
                        style={{ width }}
                        serviceQueries={[location, taskSubType]}
                        dataService={async () => {
                          if (
                            varsMap.relateTaskNo &&
                            varsMap.relateTaskType === RELATE_BIZ_TYPE_KEY_MAP.BORROW
                          ) {
                            const { response, error } =
                              await borrowsAndReturnService.fetchBorrowExWarehouseTargetRoom({
                                borrowNo: varsMap.relateTaskNo,
                              });
                            if (error) {
                              message.error(error);
                            }
                            if (response) {
                              const newData = response.data.map(item => ({
                                ...item,
                                tag: item.roomTag,
                              }));
                              return newData;
                            }
                            return;
                          }
                          const params = {
                            pageNum: 1,
                            pageSize: 200,
                            idcTag: Array.isArray(location) ? location[0] : '',
                            blockTag: Array.isArray(location) ? location[1] : '',
                          };
                          if (taskSubType === TASK_SUB_TYPE_KEY_MAPS.IN_WAREHOUSE) {
                            params.roomType = RoomTypes.Warehouse;
                          }
                          const { response } = await roomService.fetchRoomPage(params);
                          if (response) {
                            return response.data;
                          }
                        }}
                        onChange={value => {
                          this.setRefetchDevice({ room: value });
                        }}
                      />
                    )
                  )}
                </Form.Item>
              )}
              <Form.Item
                label={isExWarehouseTicket ? '出库原因' : '入库原因'}
                style={{ marginLeft: 14, marginBottom: 17 }}
              >
                {getFieldDecorator('selectWarehouseReason', {
                  rules: [
                    {
                      required: true,
                      message: `${TASK_SUB_TYPE_TEXT_MAPS[taskSubType]}库原因必选！`,
                    },
                  ],
                  initialValue: varsMap.relateTaskType
                    ? {
                        key: isRelateTicketRepair ? 'TAKE' : String(varsMap.relateTaskType),
                        label: isRelateTicketRepair ? '备件领用' : '资产借用',
                      }
                    : undefined,
                })(
                  <Select
                    style={{ width }}
                    labelInValue
                    allowClear
                    options={
                      taskSubType
                        ? get(
                            this.state,
                            isExWarehouseTicket ? 'EX_WAREHOUSE_REASON' : 'IN_WAREHOUSE_REASON'
                          )
                            .sort((a, b) => {
                              if (a.sort === null) return 1;
                              if (b.sort === null) return -1;
                              // 正常按照 sort 从小到大排序
                              return a.sort - b.sort;
                            })
                            .map(({ metaCode, metaName, sort }) => ({
                              label: metaName,
                              value: metaCode,
                            }))
                        : []
                    }
                    disabled={varsMap.relateTaskNo}
                  />
                )}
              </Form.Item>
              {selectWarehouseReason?.key === 'OTHER' && (
                <Form.Item style={{ marginLeft: 91, marginBottom: 17 }}>
                  {getFieldDecorator('otherWarehouseReason', {
                    rules: [
                      { required: true, message: `详细原因必填！` },
                      {
                        type: 'string',
                        max: 50,
                        message: '最多输入 50 个字符！',
                      },
                    ],
                    initialValue: this.state.otherWarehouseReason,
                  })(
                    <Input.TextArea
                      autoSize={{ minRows: 3 }}
                      placeholder="请输入详细原因"
                      style={{ width: 200 }}
                    />
                  )}
                </Form.Item>
              )}
              <Form.Item label="工单标题" style={{ marginLeft: 14, marginBottom: 17 }}>
                {getFieldDecorator('taskTitle', {
                  rules: [
                    { required: true, message: `工单标题必填！` },
                    {
                      type: 'string',
                      max: 20,
                      message: '最多输入 20 个字符！',
                    },
                  ],
                })(<Input allowClear style={{ width }} />)}
              </Form.Item>
              {isExWarehouseTicket && (
                <Form.Item label="SLA" style={{ marginLeft: 54, marginBottom: 17 }}>
                  {getFieldDecorator('taskSla', {
                    initialValue: { sla: 0, unit: SlaUnit.Hour },
                  })(<SlaSelect style={{ width }} />)}
                </Form.Item>
              )}
              <Form.Item label="附件" style={{ marginLeft: 52, marginBottom: 17 }}>
                <StyledMcUpload
                  accept="image/*,.xls,.xlsx"
                  showUploadList
                  maxFileSize={20}
                  allowDelete
                  fileList={this.state.fileInfoList}
                  showAccept
                  onChange={({ fileList }) => {
                    this.setState({
                      fileInfoList: fileList,
                    });
                  }}
                >
                  <Space style={{ display: 'flex' }} direction="vertical">
                    <Button icon={<UploadOutlined />}>上传</Button>
                  </Space>
                </StyledMcUpload>
              </Form.Item>
            </Form>
          </GutterWrapper>
        </TinyCard>
        <TinyCard
          style={{ marginBottom: '55px' }}
          title={type === ASSET_TYPE_KEY_MAPS.SN_DEVICE ? '设备信息' : '耗材信息'}
        >
          {isExWarehouseTicket && (
            <ExWarehouse
              devices={this.state.deviceModelList}
              getList={this.getDeviceModelList}
              existenceRelateTask={this.getExistenceRelateTask()}
              refetchDevice={refetchDevice}
              getdeviceWithRelateTask={this.getdeviceWithRelateTask}
              location={location}
              room={roomGuid}
              type={type}
            />
          )}
          {taskSubType === TASK_SUB_TYPE_KEY_MAPS.IN_WAREHOUSE && (
            <InWarehouse
              location={location}
              room={roomGuid}
              getList={this.getAssetList}
              type={type}
            />
          )}
        </TinyCard>
        <FooterToolBar>
          <GutterWrapper>
            <Button
              loading={loading}
              type="primary"
              onClick={() => {
                validateFields(async (err, valueMap) => {
                  if (err) {
                    return;
                  }
                  this.handleSubmit(valueMap);
                });
              }}
            >
              提交
            </Button>
            <Button loading={loading} onClick={() => history.goBack()}>
              取消
            </Button>
          </GutterWrapper>
        </FooterToolBar>
      </GutterWrapper>
    );
  }
}
const mapStateToProps = ({
  common: { deviceCategory },
  borrowsAndReturn: {
    detail: { info },
  },
  user: { name, userId },
}) => ({
  normalizedList: deviceCategory?.normalizedList,
  borrowInfo: info,
  userName: name,
  userId,
});
const mapDispatchToProps = {
  redirect: redirectActionCreator,
  syncCommonData: syncCommonDataActionCreator,
  getBorrowInfo: getBorrowInfoActionCreator,
  resetBorrowDetail: borrowsAndReturnActions.resetDetail,
  getTicketFiles: getTicketFilesActionCreator,
};
export default connect(
  mapStateToProps,
  mapDispatchToProps
)(Form.create({ name: 'create-warehouse' })(withRouter(PublicWarehouse)));

function generateReason(otherWarehouseReason, selectWarehouseReason) {
  return JSON.stringify({
    otherWarehouseReason,
    selectWarehouseReason,
  });
}

function initialReason(taskProperties) {
  let reasonInfo = undefined;
  let otherWarehouseReason = undefined;
  try {
    const reasonObject = JSON.parse(
      taskProperties.exWarehouseReason ?? taskProperties.inWarehouseReason
    );

    reasonInfo = reasonObject.selectWarehouseReason;
    otherWarehouseReason = reasonObject.otherWarehouseReason;
  } catch (e) {}
  return { reasonInfo, otherWarehouseReason };
}
