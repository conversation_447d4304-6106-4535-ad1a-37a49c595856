import React, { Component } from 'react';
import { connect } from 'react-redux';

import { message } from '@manyun/base-ui.ui.message';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';
import { ticketActions } from '@manyun/dc-brain.legacy.redux/actions/ticketActions';
import * as taskCenterService from '@manyun/dc-brain.legacy.services/taskCenterService';

import ExWarehouseCard from './components/card';
import ExWarehouseCardDetail from './components/card-detail';

class ExWarehouse extends Component {
  componentWillUnmount() {
    this.props.setWareHouseCheckItem([]);
  }

  fetchCardList = async (deviceType, check) => {
    const { taskNo, checkItem } = this.props;
    const { response, error } = await taskCenterService.fetchExWarehouseDeviceCard({
      taskNo,
      deviceType,
    });
    if (error) {
      message.error(error);
      return;
    }
    this.props.setWareHouseDeviceInfo(response.data);
    if (check) {
      const newCheckItem = response.data.find(
        cardData => cardData.productModel === checkItem.productModel
      );
      this.props.setWareHouseCheckItem(newCheckItem);
    }
  };

  render() {
    const { checkItem, taskNo } = this.props;

    return (
      <GutterWrapper>
        {checkItem.length === 0 ? (
          <ExWarehouseCard taskNo={taskNo} fetchCardList={this.fetchCardList} />
        ) : (
          <ExWarehouseCardDetail taskNo={taskNo} fetchCardList={this.fetchCardList} />
        )}
      </GutterWrapper>
    );
  }
}

const mapStateToProps = ({
  ticket: {
    wareHouse: { detail },
  },
}) => ({
  checkItem: detail.checkItem,
});
const mapDispatchToProps = {
  setWareHouseCheckItem: ticketActions.setWareHouseCheckItem,
  setWareHouseDeviceInfo: ticketActions.setWareHouseDeviceInfo,
};
export default connect(mapStateToProps, mapDispatchToProps)(ExWarehouse);
