import { BackendTaskStatus } from '@manyun/ticket.model.ticket';
import { TicketStatusSelect } from '@manyun/ticket.ui.ticket-status-select';

import TTR from './../../registries/ticket-type-registry';
import PublicWarehouse from './components/new/public-warehouse';
import SpecificPublicWarehouse from './components/specific/public-warehouse';
import { TASK_SUB_TYPE_KEY_MAPS } from './constants';

function getButtonVisible(basicInfo) {
  return basicInfo.taskSubType === TASK_SUB_TYPE_KEY_MAPS.EX_WAREHOUSE;
}

TTR.registerTicketType('warehouse')
  .registerConfig({
    type: 'tickets',
    showNewBtn: true,

    showResendOperation: getButtonVisible,
    showRevokeOperation: getButtonVisible,

    mergeFilters: ({ DEFAULT_FILTER_KEY_MAP, defaultFilters }) => {
      const newFilters = [...defaultFilters].filter(
        ({ key }) => key !== DEFAULT_FILTER_KEY_MAP.END_TIME
      );
      newFilters.splice(
        newFilters.findIndex(item => item.key === 'taskStatusList'),
        1,
        {
          label: '工单状态',
          key: 'taskStatusList',
          initialProps: {
            optionFilter: option => option.value !== BackendTaskStatus.CLOSE_APPROVER,
            allowClear: true,
            mode: 'multiple',
          },
          Comp: TicketStatusSelect,
        }
      );
      return newFilters;
    },
  })
  .registerConfig({
    type: 'new-ticket',
    content: PublicWarehouse,
  })
  .registerConfig({
    type: 'specific-ticket',
    content: SpecificPublicWarehouse,

    showRevocationBtn: getButtonVisible,
    showReapprovalBtn: getButtonVisible,
  });
