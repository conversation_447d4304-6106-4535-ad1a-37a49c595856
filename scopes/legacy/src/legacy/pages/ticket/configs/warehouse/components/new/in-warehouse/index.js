import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import debounce from 'lodash/debounce';
import trim from 'lodash/trim';
import uniqBy from 'lodash/uniqBy';

import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Typography } from '@manyun/base-ui.ui.typography';

import { SpaceOrDeviceLink } from '@manyun/resource-hub.ui.space-or-device-link';

import { GutterWrapper, TinyModal, TinyTable } from '@manyun/dc-brain.legacy.components';
import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';
import * as deviceService from '@manyun/dc-brain.legacy.services/deviceService';
import { getDeviceTypeName } from '@manyun/dc-brain.legacy.utils/ticket';

import { ASSET_TYPE_KEY_MAPS, TASK_SUB_TYPE_KEY_MAPS } from '../../../constants';
import AddModal from '../../add-modal';

const getColumns = (ctx, type) => {
  const list = [
    {
      title: '资产ID',
      dataIndex: 'assetNo',
      type: [ASSET_TYPE_KEY_MAPS.SN_DEVICE],
      render: assetNo => {
        if (!assetNo) {
          return BLANK_PLACEHOLDER;
        }
        return <SpaceOrDeviceLink id={assetNo} type="DEVICE_ASSET_NO" />;
      },
    },
    {
      title: 'SN',
      dataIndex: 'serialNumber',
      type: [ASSET_TYPE_KEY_MAPS.SN_DEVICE],
    },
    {
      title: '一级分类',
      dataIndex: 'topCategory',
      type: [ASSET_TYPE_KEY_MAPS.SN_DEVICE, ASSET_TYPE_KEY_MAPS.NO_SN_DEVICE],
      render: text => getDeviceTypeName(text, ctx.props.normalizedList),
    },
    {
      title: '二级分类',
      dataIndex: 'secondCategory',
      type: [ASSET_TYPE_KEY_MAPS.SN_DEVICE, ASSET_TYPE_KEY_MAPS.NO_SN_DEVICE],
      render: text => getDeviceTypeName(text, ctx.props.normalizedList),
    },
    {
      title: '三级分类',
      dataIndex: 'deviceType',
      type: [ASSET_TYPE_KEY_MAPS.SN_DEVICE, ASSET_TYPE_KEY_MAPS.NO_SN_DEVICE],
      render: text => getDeviceTypeName(text, ctx.props.normalizedList),
    },
    {
      title: '厂商',
      dataIndex: 'vendor',
      type: [ASSET_TYPE_KEY_MAPS.SN_DEVICE, ASSET_TYPE_KEY_MAPS.NO_SN_DEVICE],
    },
    {
      title: '型号',
      dataIndex: 'productModel',
      type: [ASSET_TYPE_KEY_MAPS.SN_DEVICE, ASSET_TYPE_KEY_MAPS.NO_SN_DEVICE],
    },
    {
      title: '供应商简称',
      dataIndex: 'supplyVendor',
      type: [ASSET_TYPE_KEY_MAPS.NO_SN_DEVICE],
      render: supplyVendor => (
        <Typography.Text style={{ width: 200 }} ellipsis={{ tooltip: supplyVendor }}>
          {supplyVendor ?? '--'}
        </Typography.Text>
      ),
    },
    {
      title: '数量',
      dataIndex: 'warehouseCount',
      type: [ASSET_TYPE_KEY_MAPS.NO_SN_DEVICE],
    },
    {
      title: '操作',
      dataIndex: 'action',
      type: [ASSET_TYPE_KEY_MAPS.SN_DEVICE, ASSET_TYPE_KEY_MAPS.NO_SN_DEVICE],
      render: (__, record) => (
        <Button
          compact
          type="link"
          onClick={() =>
            ctx.handleDelete(
              record.assetNo ?? record.deviceType,
              type === ASSET_TYPE_KEY_MAPS.SN_DEVICE
            )
          }
        >
          移除
        </Button>
      ),
    },
  ];
  return list.filter(item => item.type.includes(type));
};

class InWarehouse extends Component {
  state = {
    assetList: [],
    assetNoList: [],
    visible: false,
    text: '',
  };

  componentDidUpdate(prevProps) {
    const { type, room } = this.props;
    if (
      type !== prevProps.type ||
      (type === ASSET_TYPE_KEY_MAPS.SN_DEVICE && room !== prevProps.room)
    ) {
      this.setAssetList([]);
    }
  }

  handleAdd = async () => {
    const { assetNoList, assetList } = this.state;
    const { location, room } = this.props;
    const { response, error } = await deviceService.fetchDevicesByTypes({
      idcTag: location[0],
      blockTag: location[1],
      assetNoList,
    });
    if (error) {
      message.error(error);
      return;
    }
    const [restData, lostData, onData] = this.filterData(response.data, room, assetNoList);
    if (restData.length !== response.data.length || lostData.length || onData.length) {
      this.changeVisible();
    }
    this.setAssetList(uniqBy([...assetList, ...restData], 'assetNo'));
  };

  filterData = (deviceList, roomGuid, assetNoList) => {
    const devicesNo = [];
    const filtersNo = [];
    const onsNo = [];
    const lostsNo = [];
    const text = [];
    deviceList.forEach(device => devicesNo.push(device.assetNo));
    assetNoList.forEach(assetNo => {
      if (!devicesNo.includes(assetNo)) {
        lostsNo.push(assetNo);
      }
    });
    const ons = deviceList.filter(device => device.operationStatus.code === 'ON');
    const filters = deviceList.filter(device => device.spaceGuid.roomGuid === roomGuid);
    const rests = deviceList
      .filter(
        device => device.operationStatus.code !== 'ON' && device.spaceGuid.roomGuid !== roomGuid
      )
      .map(device => {
        return { ...device, key: device.assetNo };
      });
    if (ons.length) {
      ons.forEach(device => onsNo.push(device.assetNo));
      text.push(onsNo.join('、') + '已启用。');
    }
    if (filters.length) {
      filters.forEach(device => filtersNo.push(device.assetNo));
      text.push(filtersNo.join('、') + '已在当前位置，不可添加。');
    }
    if (lostsNo.length) {
      text.push(lostsNo.join('、') + '不存在。');
    }
    this.setState({ text: text.length > 1 ? text.join('\n') : text.join('') });
    return [rests, lostsNo, ons];
  };

  handleDelete = (key, haveSerialNo) => {
    const { assetList } = this.state;
    const dataSource = [...assetList];
    this.setAssetList(
      key
        ? dataSource.filter(item => {
            if (haveSerialNo) {
              return item.key !== key;
            } else {
              return item.deviceType !== key;
            }
          })
        : []
    );
  };

  debouncedHandle = debounce(e => {
    if (e === '') {
      return;
    }
    this.setState({
      assetNoList: trim(e.target.value)
        .split(' ')
        .map(item => {
          if (item === ' ') {
            return false;
          }
          return item;
        })
        .filter(Boolean),
    });
  }, 500);

  changeVisible = () => {
    this.setState({ visible: !this.state.visible });
  };

  setAssetList = data => {
    const { getList } = this.props;
    this.setState({ assetList: data }, () => {
      getList(this.state.assetList);
    });
  };

  render() {
    const { assetList, assetNoList, visible, text } = this.state;
    const { location, type, room } = this.props;
    return (
      <GutterWrapper mode="vertical">
        <TinyTable
          style={{ marginBottom: 40 }}
          rowKey="key"
          columns={getColumns(this, type)}
          dataSource={assetList}
          actions={
            type === ASSET_TYPE_KEY_MAPS.SN_DEVICE
              ? [
                  <Button
                    key="remove"
                    disabled={!assetList.length}
                    onClick={e => {
                      e.persist();
                      this.handleDelete();
                    }}
                  >
                    全部移除
                  </Button>,
                  <Input
                    key="text"
                    placeholder="请输入资产ID，支持批量用空格区分"
                    allowClear
                    style={{ width: 512 }}
                    onChange={e => {
                      e.persist();
                      this.debouncedHandle(e);
                    }}
                  />,
                  <Button
                    key="add"
                    type="primary"
                    disabled={!room || !assetNoList.length}
                    onClick={this.handleAdd}
                  >
                    添加入库
                  </Button>,
                ]
              : [
                  <AddModal
                    key="modal"
                    custom
                    title={TASK_SUB_TYPE_KEY_MAPS.IN_WAREHOUSE}
                    type={type}
                    location={location}
                    dataList={assetList}
                    setDataList={this.setAssetList}
                    normalizedList={this.props.normalizedList}
                  />,
                  <Button
                    key="remove"
                    disabled={!assetList.length}
                    onClick={e => {
                      e.persist();
                      this.handleDelete();
                    }}
                  >
                    全部移除
                  </Button>,
                ]
          }
        />
        <TinyModal
          title="提示"
          visible={visible}
          footer={
            <Button type="primary" onClick={this.changeVisible}>
              确定
            </Button>
          }
          onCancel={this.changeVisible}
        >
          <span style={{ whiteSpace: 'pre-wrap' }}>{text}</span>
        </TinyModal>
      </GutterWrapper>
    );
  }
}
const mapStateToProps = ({ common: { deviceCategory } }) => ({
  normalizedList: deviceCategory?.normalizedList,
});
export default connect(mapStateToProps, null)(Form.create({ name: 'create-in' })(InWarehouse));
