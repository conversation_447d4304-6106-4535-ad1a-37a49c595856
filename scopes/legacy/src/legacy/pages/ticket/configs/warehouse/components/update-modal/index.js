import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import { ApiSelect } from '@galiojs/awesome-antd';

import { Button } from '@manyun/base-ui.ui.button';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';

import { RoomTypes } from '@manyun/resource-hub.model.room';

import { TinyModal } from '@manyun/dc-brain.legacy.components';
import * as roomService from '@manyun/dc-brain.legacy.services/roomService';
import * as spareService from '@manyun/dc-brain.legacy.services/spareService';
import { getDeviceTypeFullMetaName } from '@manyun/dc-brain.legacy.utils/deviceType';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};

class UpdateModal extends Component {
  state = {
    modelVisible: false,
    remain: null,
    id: null,
  };

  editModelVisible = () => {
    this.setState({ modelVisible: !this.state.modelVisible });
  };

  handleOk = () => {
    const { handleAdd, location } = this.props;
    const { id } = this.state;
    this.props.form.validateFields(async (err, values) => {
      if (err) {
        return;
      }
      if (id) {
        handleAdd({
          spare: { id, count: values.deviceCount, room: [...location, values.room].join('.') },
          callback: this.editModelVisible,
        });
      } else {
        return;
      }
    });
  };

  getRemainCount = async roomTag => {
    this.setState({ remain: null });

    const { location, checkItem } = this.props;
    if (!roomTag) {
      this.setState({ remain: null });
      return;
    }
    const { response, error } = await spareService.fetchUniqueNum({
      spareType: checkItem.deviceType,
      vendor: checkItem.vendor,
      productModel: checkItem.productModel,
      idcTag: location[0],
      blockGuid: location.join('.'),
      roomTag,
    });

    if (error) {
      message.error(error);

      return;
    }
    this.setState({
      remain: response?.totalNum - response?.lockNum || 0,
      id: response?.id,
    });
  };

  render() {
    const { form, location, checkItem, normalizedList } = this.props;
    const { modelVisible, remain } = this.state;
    const { getFieldDecorator, getFieldValue, setFieldsValue } = form;

    return (
      <>
        <Button key="add" type="primary" onClick={this.editModelVisible}>
          添加出库
        </Button>
        <TinyModal
          title="添加"
          destroyOnClose
          visible={modelVisible}
          style={{ height: 449 }}
          width={680}
          footer={
            <>
              <Button onClick={this.editModelVisible}>取消</Button>
              <Button type="primary" onClick={this.handleOk}>
                确定
              </Button>
            </>
          }
          afterClose={() => {
            this.setState({ remain: null });
          }}
          onCancel={this.editModelVisible}
        >
          <Form {...formItemLayout} colon={false}>
            <Form.Item label="资产分类">
              {getDeviceTypeFullMetaName(checkItem.deviceType, normalizedList, '/')}
            </Form.Item>
            <Form.Item label="厂商">{checkItem.vendor}</Form.Item>
            <Form.Item label="型号">{checkItem.productModel}</Form.Item>
            <Form.Item label="包间">
              {getFieldDecorator('room', {
                rules: [
                  {
                    required: true,
                    message: '仓库包间必选！',
                  },
                ],
              })(
                <ApiSelect
                  fieldNames={{ value: 'tag', label: 'tag' }}
                  allowClear
                  trigger="onDidMount"
                  style={{ width: 200 }}
                  dataService={async () => {
                    const { response } = await roomService.fetchRoomPage({
                      pageNum: 1,
                      pageSize: 200,
                      idcTag: location[0],
                      blockTag: location[1],
                      roomType: RoomTypes.Warehouse,
                    });
                    if (response) {
                      return response.data;
                    }
                  }}
                  onChange={roomTag => {
                    this.getRemainCount(roomTag);
                    setFieldsValue({ deviceCount: null });
                  }}
                />
              )}
            </Form.Item>
            <Row>
              <Col span={18}>
                <Form.Item label="数量" labelCol={{ xl: 8 }} wrapperCol={{ xl: 16 }}>
                  {getFieldDecorator('deviceCount', {
                    rules: [
                      {
                        required: true,
                        message: `数量必填且小于库存余量`,
                      },
                      {
                        type: 'number',
                        message: '不可超过库存余量',
                        max: remain,
                      },
                      { pattern: /^\d+$/, message: '设备数量必须为整数' },
                    ],
                  })(
                    <InputNumber
                      disabled={!getFieldValue('room')}
                      allowClear
                      min={1}
                      max={remain || 1}
                      style={{ width: 200 }}
                    />
                  )}
                </Form.Item>
              </Col>
              <Col span={6}>
                {typeof remain === 'number' && (
                  <span style={{ lineHeight: '40px' }}>{`库存余量:${remain}`}</span>
                )}
              </Col>
            </Row>
          </Form>
        </TinyModal>
      </>
    );
  }
}
const mapStateToProps = ({
  ticket: {
    wareHouse: { detail },
  },
  common: { deviceCategory },
}) => ({
  checkItem: detail.checkItem,
  normalizedList: deviceCategory?.normalizedList,
});
export default connect(mapStateToProps, null)(Form.create()(UpdateModal));
