import PageHeader from 'antd/es/page-header';
import debounce from 'lodash/debounce';
import trim from 'lodash/trim';
import React, { Component } from 'react';
import { connect } from 'react-redux';

// Deprecated, replace with "useAuthorized" hook
// import { Authorize } from '@manyun/base-ui.ui.authorize';
import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Typography } from '@manyun/base-ui.ui.typography';
import { SpaceOrDeviceLink } from '@manyun/resource-hub.ui.space-or-device-link';
import { BackendTaskStatus } from '@manyun/ticket.model.ticket';

import {
  <PERSON><PERSON><PERSON><PERSON>per,
  StatusText,
  TinyModal,
  TinyTable,
} from '@manyun/dc-brain.legacy.components';
import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';
import { STATUS_MAP } from '@manyun/dc-brain.legacy.constants/statusColor';
import { ticketActions } from '@manyun/dc-brain.legacy.redux/actions/ticketActions';
import * as taskCenterService from '@manyun/dc-brain.legacy.services/taskCenterService';
import { getDeviceTypeName, getTaskProperties } from '@manyun/dc-brain.legacy.utils/ticket';

import { ASSET_TYPE_KEY_MAPS } from '../../../../constants';
import UpdateModal from '../../../update-modal';

const getColumns = (ctx, numbered) => {
  const type = numbered ? ASSET_TYPE_KEY_MAPS.SN_DEVICE : ASSET_TYPE_KEY_MAPS.NO_SN_DEVICE;
  const list = [
    {
      title: '资产ID',
      dataIndex: 'assetNo',
      type: [ASSET_TYPE_KEY_MAPS.SN_DEVICE],
      render: assetNo => {
        if (!assetNo) {
          return BLANK_PLACEHOLDER;
        }
        return <SpaceOrDeviceLink id={assetNo} type="DEVICE_ASSET_NO" />;
      },
    },
    {
      title: 'SN',
      dataIndex: 'serialNo',
      type: [ASSET_TYPE_KEY_MAPS.SN_DEVICE],
    },
    {
      title: '一级分类',
      dataIndex: 'topCategory',
      type: [ASSET_TYPE_KEY_MAPS.SN_DEVICE, ASSET_TYPE_KEY_MAPS.NO_SN_DEVICE],
      render: text => getDeviceTypeName(text, ctx.props.normalizedList),
    },
    {
      title: '二级分类',
      dataIndex: 'secondCategory',
      type: [ASSET_TYPE_KEY_MAPS.SN_DEVICE, ASSET_TYPE_KEY_MAPS.NO_SN_DEVICE],
      render: text => getDeviceTypeName(text, ctx.props.normalizedList),
    },
    {
      title: '三级分类',
      dataIndex: 'deviceType',
      type: [ASSET_TYPE_KEY_MAPS.SN_DEVICE, ASSET_TYPE_KEY_MAPS.NO_SN_DEVICE],
      render: text => getDeviceTypeName(text, ctx.props.normalizedList),
    },
    {
      title: '包间',
      dataIndex: 'roomTag',
      type: [ASSET_TYPE_KEY_MAPS.SN_DEVICE, ASSET_TYPE_KEY_MAPS.NO_SN_DEVICE],
    },
    {
      title: '厂商',
      dataIndex: 'vendor',
      type: [ASSET_TYPE_KEY_MAPS.SN_DEVICE, ASSET_TYPE_KEY_MAPS.NO_SN_DEVICE],
    },
    {
      title: '型号',
      dataIndex: 'productModel',
      type: [ASSET_TYPE_KEY_MAPS.SN_DEVICE, ASSET_TYPE_KEY_MAPS.NO_SN_DEVICE],
    },
    {
      title: '数量',
      dataIndex: 'warehouseCount',
      type: [ASSET_TYPE_KEY_MAPS.NO_SN_DEVICE],
    },
    {
      title: '状态',
      dataIndex: 'warehouseStatus',
      type: [ASSET_TYPE_KEY_MAPS.NO_SN_DEVICE],
      render: text =>
        typeof text === 'boolean' ? (
          text ? (
            <StatusText fontSize={14} status={STATUS_MAP.NORMAL}>
              成功
            </StatusText>
          ) : (
            <StatusText fontSize={14} status={STATUS_MAP.ALARM}>
              失败
            </StatusText>
          )
        ) : (
          BLANK_PLACEHOLDER
        ),
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      type: [ASSET_TYPE_KEY_MAPS.SN_DEVICE],
      render: (_, record) => (
        <Button compact type="link" onClick={() => ctx.handleDelete(record.id)}>
          移除
        </Button>
      ),
    },
  ];
  const filters = list.filter(item => item.type.includes(type));
  if (ctx.props.basicInfo.taskStatus !== BackendTaskStatus.PROCESSING) {
    filters.splice(-1, 1);
  }
  return filters;
};
const CheckUserId = props => {
  const { basicInfo, numbered, deviceList, location, handleDelete, debouncedHandle, handleAdd } =
    props;
  const [authorized] = useAuthorized({ checkByUserId: basicInfo.taskAssignee });

  return (
    <>
      {authorized &&
        basicInfo?.taskStatus === BackendTaskStatus.PROCESSING &&
        (numbered ? (
          <>
            <Button
              key="remove"
              disabled={!deviceList.length}
              onClick={e => {
                e.persist();
                handleDelete();
              }}
            >
              全部移除
            </Button>
            <Input
              key="text"
              style={{ width: 512 }}
              placeholder="请输入资产ID，支持批量用空格区分"
              onChange={e => {
                e.persist();
                debouncedHandle(e);
              }}
            />
            <Button
              key="add"
              type="primary"
              disabled={basicInfo?.taskStatus !== BackendTaskStatus.PROCESSING}
              onClick={handleAdd}
            >
              添加
            </Button>
          </>
        ) : (
          <UpdateModal key="spare_modal" location={location} handleAdd={handleAdd} />
        ))}
    </>
  );
};

export class ExWarehouseCardDetail extends Component {
  state = {
    deviceList: [],
    assetNoList: [],
    pageNum: 1,
    pageSize: 10,
    total: 0,
    numbered: true,
    location: [],
    visible: false,
    text: '',
  };

  componentDidMount() {
    this.setNumberedAndLocation();
    this.fetchDeviceList(1, 10);
  }

  componentDidUpdate(prevProps) {
    const {
      basicInfo: { taskNo },
    } = prevProps;
    const { basicInfo } = this.props;
    const currentTaskNo = basicInfo.taskNo;
    if (taskNo && taskNo !== currentTaskNo) {
      this.setNumberedAndLocation();
    }
  }

  setNumberedAndLocation = () => {
    const { basicInfo } = this.props;
    const properties = getTaskProperties(basicInfo);
    this.setState({ numbered: properties.numbered, location: basicInfo.blockTag.split('.') });
  };

  fetchDeviceList = async (pageNum, pageSize) => {
    const {
      taskNo,
      checkItem: { deviceType, productModel, vendor },
    } = this.props;
    const { response, error } = await taskCenterService.fetchExWarehouseDevice({
      deviceType,
      pageNum,
      pageSize,
      productModel,
      taskNo,
      vendor,
    });
    if (error) {
      message.error(error);
      return;
    }
    this.setState({
      deviceList: response.data.map((item, index) => {
        return {
          ...item,
          key: index,
        };
      }),
      total: response.total,
      pageNum,
      pageSize,
    });
  };

  handleAdd = async ({ callback, spare }) => {
    const {
      taskNo,
      checkItem: { deviceType, productModel, vendor },
      fetchCardList,
    } = this.props;
    const { assetNoList, numbered } = this.state;
    const params = {
      taskNo,
      deviceType,
      productModel,
      vendor,
      numbered,
    };
    if (numbered) {
      params.assetNoList = assetNoList;
    } else {
      params.spareGuid = spare.id;
      params.roomGuid = spare.room;
      params.exWarehouseCount = spare.count;
    }
    const { error } = await taskCenterService.updateExWarehouse(params);
    if (error) {
      if (error.includes('已在目标位置')) {
        message.warning(error);
        return;
      }
      message.error(error);
      return;
    }
    if (callback && typeof callback === 'function') {
      callback();
    }
    this.fetchDeviceList(1, 10);
    fetchCardList(deviceType, true);
  };

  handleDelete = async id => {
    const { taskNo, checkItem, fetchCardList } = this.props;
    const { numbered } = this.state;
    const { error } = await taskCenterService.cancelExWarehouse({
      taskNo,
      numbered,
      id,
    });
    if (error) {
      message.error(error);
      return;
    }
    this.fetchDeviceList(1, 10);
    fetchCardList(checkItem.deviceType, true);
  };

  debouncedHandle = debounce(e => {
    if (e === '') {
      return;
    }
    this.setState({
      assetNoList: trim(e.target.value)
        .split(' ')
        .map(item => {
          if (item === ' ') {
            return false;
          }
          return item;
        })
        .filter(Boolean),
    });
  }, 500);

  onChangePage = (pageNum, pageSize) => {
    this.fetchDeviceList(pageNum, pageSize);
  };

  changeVisible = () => {
    this.setState({ visible: !this.state.visible });
  };
  render() {
    const {
      checkItem: { deviceType, vendor, productModel, deviceCount, remainCount },
      normalizedList,
    } = this.props;
    const { deviceList, total, pageNum, pageSize, numbered, visible, text } = this.state;

    return (
      <GutterWrapper mode="vertical">
        <PageHeader
          title={getDeviceTypeName(deviceType, normalizedList)}
          onBack={() => this.props.setWareHouseCheckItem([])}
        />
        <GutterWrapper style={{ flexWrap: 'wrap' }} flex>
          <GutterWrapper flex style={{ marginBottom: '12px' }}>
            <CheckUserId
              {...this.state}
              {...this.props}
              handleDelete={this.handleDelete}
              handleAdd={this.handleAdd}
              debouncedHandle={this.debouncedHandle}
            />
          </GutterWrapper>
          <Descriptions column={2} style={{ marginLeft: '0px' }}>
            <Descriptions.Item label={<Typography type="secondary">厂商</Typography>}>
              {vendor}
            </Descriptions.Item>
            <Descriptions.Item label={<Typography type="secondary">型号</Typography>}>
              {productModel}
            </Descriptions.Item>
            <Descriptions.Item
              label={
                <Typography type="secondary">
                  {getDeviceTypeName(deviceType, normalizedList)}共需出库
                </Typography>
              }
            >
              {deviceCount}
            </Descriptions.Item>
            <Descriptions.Item label={<Typography type="secondary">该楼栋仓库还需出库</Typography>}>
              <span style={{ color: `var(--${prefixCls}-error-color)` }}>{remainCount}</span>
            </Descriptions.Item>
          </Descriptions>
          {/* <StatusText key="description" status={STATUS_MAP.DISABLED} lineHeight={40} fontSize={14}>
            <span>厂商: </span>
            {vendor},<span>型号: </span>
            {productModel},<span>{getDeviceTypeName(deviceType, normalizedList)}共需出库:</span>
            {deviceCount},<span>该楼栋仓库还需出库: </span>
            <span style={{ color: `var(--${prefixCls}-error-color)` }}>{remainCount}</span>
          </StatusText> */}
        </GutterWrapper>
        <TinyTable
          style={{ marginTop: '-12px', marginBottom: 40 }}
          rowKey="key"
          columns={getColumns(this, numbered)}
          dataSource={deviceList}
          align="left"
          pagination={{
            total,
            current: pageNum,
            pageSize,
            onChange: this.onChangePage,
          }}
        />
        <TinyModal
          title="提示"
          visible={visible}
          footer={
            <Button type="primary" onClick={this.changeVisible}>
              确定
            </Button>
          }
          onCancel={this.changeVisible}
        >
          <span style={{ whiteSpace: 'pre-wrap' }}>{text}</span>
        </TinyModal>
      </GutterWrapper>
    );
  }
}
const mapStateToProps = ({
  ticket: {
    wareHouse: { detail },
    ticketView,
  },
  common: { deviceCategory },
}) => ({
  checkItem: detail.checkItem,
  normalizedList: deviceCategory?.normalizedList,
  basicInfo: ticketView?.basicInfo,
});
const mapDispatchToProps = {
  setWareHouseCheckItem: ticketActions.setWareHouseCheckItem,
};
export default connect(mapStateToProps, mapDispatchToProps)(ExWarehouseCardDetail);
