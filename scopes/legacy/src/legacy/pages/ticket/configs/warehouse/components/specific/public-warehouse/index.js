import React, { Component } from 'react';
import { connect } from 'react-redux';

import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';

import { TASK_SUB_TYPE_KEY_MAPS } from '../../../constants';
import ExWarehouse from '../ex-warehouse';
import InWarehouse from '../in-warehouse';

class SpecificPublicWarehouse extends Component {
  componentDidMount() {
    this.props.syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });
  }

  render() {
    const { taskNo, basicInfo } = this.props;
    const target = basicInfo?.taskSubType;

    return (
      <>
        {target === TASK_SUB_TYPE_KEY_MAPS.EX_WAREHOUSE && <ExWarehouse taskNo={taskNo} />}
        {target === TASK_SUB_TYPE_KEY_MAPS.IN_WAREHOUSE && <InWarehouse taskNo={taskNo} />}
      </>
    );
  }
}

const mapStateToProps = ({ ticket: { ticketView } }) => ({
  basicInfo: ticketView?.basicInfo,
});
const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
};
export default connect(mapStateToProps, mapDispatchToProps)(SpecificPublicWarehouse);
