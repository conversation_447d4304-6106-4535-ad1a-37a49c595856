import React, { Component } from 'react';
import { connect } from 'react-redux';

import { message } from '@manyun/base-ui.ui.message';
import { Typography } from '@manyun/base-ui.ui.typography';

import { SpaceOrDeviceLink } from '@manyun/resource-hub.ui.space-or-device-link';

import {
  <PERSON><PERSON><PERSON>rapper,
  StatusText,
  TinyDescriptions,
  TinyTable,
} from '@manyun/dc-brain.legacy.components';
import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';
import { STATUS_MAP } from '@manyun/dc-brain.legacy.constants/statusColor';
import {
  RELATE_BIZ_TYPE_KEY_MAP,
  RELATE_BIZ_TYPE_TEXT_MAP,
} from '@manyun/dc-brain.legacy.pages/borrows-and-return/constants';
import * as taskCenterService from '@manyun/dc-brain.legacy.services/taskCenterService';
import { getDeviceTypeName, getTaskProperties } from '@manyun/dc-brain.legacy.utils/ticket';

import { ASSET_TYPE_KEY_MAPS } from '../../../constants';
import { showRelateBizType } from '../ex-warehouse/components/card';

const getColumns = (ctx, numbered) => {
  const type = numbered ? ASSET_TYPE_KEY_MAPS.SN_DEVICE : ASSET_TYPE_KEY_MAPS.NO_SN_DEVICE;
  const list = [
    {
      title: '资产ID',
      dataIndex: 'assetNo',
      type: [ASSET_TYPE_KEY_MAPS.SN_DEVICE],
      render: (assetNo, { deviceGuid }) => {
        if (!assetNo) {
          return BLANK_PLACEHOLDER;
        }
        return <SpaceOrDeviceLink id={deviceGuid} type="DEVICE_GUID" text={assetNo} />;
      },
    },
    {
      title: 'SN',
      dataIndex: 'serialNo',
      type: [ASSET_TYPE_KEY_MAPS.SN_DEVICE],
    },
    {
      title: '一级分类',
      dataIndex: 'topCategory',
      type: [ASSET_TYPE_KEY_MAPS.SN_DEVICE, ASSET_TYPE_KEY_MAPS.NO_SN_DEVICE],
      render: text => getDeviceTypeName(text, ctx.props.normalizedList),
    },
    {
      title: '二级分类',
      dataIndex: 'secondCategory',
      type: [ASSET_TYPE_KEY_MAPS.SN_DEVICE, ASSET_TYPE_KEY_MAPS.NO_SN_DEVICE],
      render: text => getDeviceTypeName(text, ctx.props.normalizedList),
    },
    {
      title: '三级分类',
      dataIndex: 'deviceType',
      type: [ASSET_TYPE_KEY_MAPS.SN_DEVICE, ASSET_TYPE_KEY_MAPS.NO_SN_DEVICE],
      render: text => getDeviceTypeName(text, ctx.props.normalizedList),
    },
    {
      title: '厂商',
      dataIndex: 'vendor',
      type: [ASSET_TYPE_KEY_MAPS.SN_DEVICE, ASSET_TYPE_KEY_MAPS.NO_SN_DEVICE],
    },
    {
      title: '型号',
      dataIndex: 'productModel',
      type: [ASSET_TYPE_KEY_MAPS.SN_DEVICE, ASSET_TYPE_KEY_MAPS.NO_SN_DEVICE],
    },
    {
      title: '供应商简称',
      dataIndex: 'supplyVendor',
      type: [ASSET_TYPE_KEY_MAPS.NO_SN_DEVICE],
      render: supplyVendor => (
        <Typography.Text style={{ width: 200 }} ellipsis={{ tooltip: supplyVendor }}>
          {supplyVendor ?? '--'}
        </Typography.Text>
      ),
    },
    {
      title: '数量',
      dataIndex: 'warehouseCount',
      type: [ASSET_TYPE_KEY_MAPS.NO_SN_DEVICE],
    },
    {
      title: '状态',
      dataIndex: 'warehouseStatus',
      type: [ASSET_TYPE_KEY_MAPS.NO_SN_DEVICE],
      render: text =>
        typeof text === 'boolean' ? (
          text ? (
            <StatusText fontSize={14} status={STATUS_MAP.NORMAL}>
              成功
            </StatusText>
          ) : (
            <StatusText fontSize={14} status={STATUS_MAP.ALARM}>
              失败
            </StatusText>
          )
        ) : (
          BLANK_PLACEHOLDER
        ),
    },
  ];
  return list.filter(item => item.type.includes(type));
};

class InWarehouse extends Component {
  state = {
    deviceList: [],
    pageNum: 1,
    pageSize: 10,
    total: 0,
    numbered: null,
  };

  componentDidMount() {
    this.search(1, 10);
    this.setNumbered();
  }

  componentDidUpdate(prevProps) {
    const {
      basicInfo: { taskNo },
    } = prevProps;
    const { basicInfo } = this.props;
    const currentTaskNo = basicInfo.taskNo;
    if (taskNo && taskNo !== currentTaskNo) {
      this.setNumbered();
    }
  }

  setNumbered = () => {
    const { basicInfo } = this.props;
    const properties = getTaskProperties(basicInfo);
    this.setState({ numbered: properties.numbered });
  };

  search = async (pageNum, pageSize) => {
    const { taskNo } = this.props;
    const { response, error } = await taskCenterService.fetchInWarehouseDevice({
      pageNum,
      pageSize,
      taskNo,
    });
    if (error) {
      message.error(error);
      return;
    }
    this.setState({
      deviceList: response.data,
      total: response.total,
      pageNum,
      pageSize,
    });
  };

  onChangePage = (pageNum, pageSize) => {
    this.search(pageNum, pageSize);
  };

  getDescriptionItem = () => {
    const { basicInfo, ticketTypeMapping } = this.props;
    const properties = getTaskProperties(basicInfo);
    let inWarehouseReason = null;
    try {
      const reason = JSON.parse(properties.inWarehouseReason);
      inWarehouseReason = reason.otherWarehouseReason ?? reason.selectWarehouseReason.label;
    } catch (error) {
      inWarehouseReason = properties.inWarehouseReason;
    }
    const relateBizTypeMapping = {
      ...ticketTypeMapping,
      [RELATE_BIZ_TYPE_KEY_MAP.BORROW]: {
        taskValue: RELATE_BIZ_TYPE_TEXT_MAP[RELATE_BIZ_TYPE_KEY_MAP.BORROW],
      },
    };
    return [
      {
        label: '入仓库至',
        value: <span>{basicInfo.roomGuid?.split('.')?.[2]}</span>,
      },
      {
        label: '入库原因',
        value: <span>{inWarehouseReason}</span>,
      },
      !!basicInfo.relateTaskNo && {
        label: '关联单号',
        value: showRelateBizType({
          relateTaskNo: basicInfo.relateTaskNo,
          relateBizType: basicInfo.relateBizType,
          mapping: relateBizTypeMapping,
        }),
      },
    ].filter(Boolean);
  };

  render() {
    const { deviceList, total, pageSize, pageNum, numbered } = this.state;

    return (
      <GutterWrapper mode="vertical">
        <TinyTable
          rowKey="assetNo"
          columns={getColumns(this, numbered)}
          dataSource={deviceList}
          actions={[
            <TinyDescriptions
              key="description"
              column={4}
              descriptionsItems={this.getDescriptionItem()}
            />,
          ]}
          pagination={{
            total,
            current: pageNum,
            pageSize,
            onChange: this.onChangePage,
          }}
        />
      </GutterWrapper>
    );
  }
}

const mapStateToProps = ({ common: { deviceCategory, ticketTypes }, ticket: { ticketView } }) => ({
  normalizedList: deviceCategory?.normalizedList,
  basicInfo: ticketView?.basicInfo,
  ticketTypeMapping: ticketTypes?.normalizedList || {},
});
export default connect(mapStateToProps, null)(InWarehouse);
