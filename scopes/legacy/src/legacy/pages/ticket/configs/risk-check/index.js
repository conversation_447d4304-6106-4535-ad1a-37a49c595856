import { Link } from 'react-router-dom';

import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { BackendTaskStatus } from '@manyun/ticket.model.ticket';
import { RiskCheckTicketCreate } from '@manyun/ticket.page.risk-check-ticket-create';
import { RiskCheckTicketDetail } from '@manyun/ticket.page.risk-check-ticket-detail';
import { generateRiskCheckTaskDetailRoutePath } from '@manyun/ticket.route.ticket-routes';
import { TicketStatusSelect } from '@manyun/ticket.ui.ticket-status-select';

import TTR from './../../registries/ticket-type-registry';

TTR.registerTicketType('risk_check')
  .registerConfig({
    type: 'tickets',
    showNewBtn: true,
    showBatchTakeOverBtn: false,
    showBatchCloseTicketBtn: false,
    showBatchUrgeBtn: true,
    showExportBtn: true,
    mergeFilters: ({ DEFAULT_FILTER_KEY_MAP, defaultFilters }) => {
      const newFilters = [...defaultFilters].filter(({ key }) =>
        [
          DEFAULT_FILTER_KEY_MAP.TASK_NO,
          DEFAULT_FILTER_KEY_MAP.TICKET_TITLE,
          DEFAULT_FILTER_KEY_MAP.TICKET_STATE,
          DEFAULT_FILTER_KEY_MAP.TASK_ASSIGNEE,
          DEFAULT_FILTER_KEY_MAP.LOCATION,
          DEFAULT_FILTER_KEY_MAP.END_TIME,
        ].includes(key)
      );

      newFilters.splice(
        newFilters.findIndex(item => item.key === 'taskStatusList'),
        1,

        {
          label: '工单状态',
          key: 'taskStatusList',
          initialProps: {
            optionFilter: option =>
              ![
                BackendTaskStatus.CLOSE_APPROVER,
                BackendTaskStatus.INIT,
                BackendTaskStatus.UNDO,
              ].includes(option.value),
            allowClear: true,
            mode: 'multiple',
          },
          Comp: TicketStatusSelect,
        },
        {
          key: DEFAULT_FILTER_KEY_MAP.EFFECT_TIME,
          label: '创建时间',
          Comp: DatePicker.RangePicker,
          initialProps: {
            placeholder: ['开始时间', '结束时间'],
          },
          span: 2,
        }
      );
      const expectSort = [
        DEFAULT_FILTER_KEY_MAP.TASK_NO,
        DEFAULT_FILTER_KEY_MAP.TICKET_TITLE,
        DEFAULT_FILTER_KEY_MAP.LOCATION,
        DEFAULT_FILTER_KEY_MAP.TICKET_STATE,
        DEFAULT_FILTER_KEY_MAP.TASK_ASSIGNEE,
        DEFAULT_FILTER_KEY_MAP.EFFECT_TIME,
        DEFAULT_FILTER_KEY_MAP.END_TIME,
      ];
      const sortedFilters = [];
      const currentFilterKeys = newFilters.map(item => item.key);
      for (const item of expectSort) {
        if (currentFilterKeys.findIndex(key => key === item) !== -1) {
          sortedFilters.push(newFilters[currentFilterKeys.indexOf(item)]);
        }
      }
      return sortedFilters;
    },

    mergeColumns: ({ DEFAULT_COLUMN_DATA_INDEX_MAP, defaultColumns }) => {
      const newColumns = [...defaultColumns].filter(
        ({ dataIndex }) =>
          dataIndex !== DEFAULT_COLUMN_DATA_INDEX_MAP.TASK_TYPE &&
          dataIndex !== DEFAULT_COLUMN_DATA_INDEX_MAP.TASK_SUB_TYPE &&
          dataIndex !== DEFAULT_COLUMN_DATA_INDEX_MAP.CREATOR_NAME &&
          dataIndex !== DEFAULT_COLUMN_DATA_INDEX_MAP.TASK_RESPOND_TIME
      );
      const insertIdx = newColumns.findIndex(
        ({ dataIndex }) => dataIndex === DEFAULT_COLUMN_DATA_INDEX_MAP.EFFECT_TIME
      );
      newColumns.splice(insertIdx, 0, {
        title: '关联检查任务',
        dataIndex: 'scheduleId',
        render(_ignored, { scheduleId, schName }) {
          return scheduleId ? (
            <Link target="_blank" to={generateRiskCheckTaskDetailRoutePath({ id: scheduleId })}>
              {schName}
            </Link>
          ) : (
            '--'
          );
        },
      });
      const expectSort = [
        DEFAULT_COLUMN_DATA_INDEX_MAP.TASK_NO,
        DEFAULT_COLUMN_DATA_INDEX_MAP.TICKET_TITLE,
        DEFAULT_COLUMN_DATA_INDEX_MAP.LOCATION,
        DEFAULT_COLUMN_DATA_INDEX_MAP.TICKET_STATE,
        DEFAULT_COLUMN_DATA_INDEX_MAP.TASK_SLA,
        'scheduleId',
        DEFAULT_COLUMN_DATA_INDEX_MAP.EFFECT_TIME,
        DEFAULT_COLUMN_DATA_INDEX_MAP.END_TIME,
        DEFAULT_COLUMN_DATA_INDEX_MAP.TASK_ASSIGNEE_NAME,
        'operation',
      ];
      const sortedColumns = [];
      const currentFilterKeys = newColumns.map(item => item.dataIndex);

      for (const item of expectSort) {
        if (currentFilterKeys.findIndex(key => key === item) !== -1) {
          if (currentFilterKeys.indexOf(item) !== -1) {
            sortedColumns.push(newColumns[currentFilterKeys.indexOf(item)]);
          }
        }
      }
      return sortedColumns;
    },
  })
  .registerConfig({
    type: 'new-ticket',
    content: RiskCheckTicketCreate,
  })
  .registerConfig({
    type: 'specific-ticket',
    showFile: false,
    showOperationRecords: true,
    content: RiskCheckTicketDetail,
  });
