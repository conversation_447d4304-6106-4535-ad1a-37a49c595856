import React, { useState } from 'react';

import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Radio } from '@manyun/base-ui.ui.radio';

import { ModalButton } from '@manyun/dc-brain.legacy.components';
import { taskCenterService } from '@manyun/dc-brain.legacy.services';

export default function FeedbackProgressModalButton({ taskNo, text, onSuccess }) {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  return (
    <ModalButton
      type="primary"
      forceRender
      text={text}
      title={text}
      okText="提交"
      okButtonProps={{ loading }}
      onOk={async () => {
        const { feedbackContent, isResolved } = await form.validateFields();
        if (!feedbackContent.trim()) {
          return;
        }
        setLoading(true);
        const { error } = await taskCenterService.addRepairTicketProgressFeedback({
          taskNo,
          resolved: isResolved,
          feedbackContent,
        });
        setLoading(false);
        if (error) {
          message.error(error);
          return false;
        }
        onSuccess();
        return true;
      }}
      onVisibleChanged={visible => {
        if (!visible) {
          form.resetFields();
        }
      }}
    >
      <Form
        form={form}
        colon={false}
        labelCol={{ xs: 4 }}
        wrapperCol={{ xs: 20 }}
        initialValues={{ isResolved: false }}
      >
        <Form.Item label="是否解决" name="isResolved" required>
          <Radio.Group
            options={[
              {
                label: '是',
                value: true,
              },
              {
                label: '否',
                value: false,
              },
            ]}
          />
        </Form.Item>
        <Form.Item
          name="feedbackContent"
          label="进度描述"
          rules={[
            { required: true, message: '进度描述必填' },
            { max: 50, message: '最多输入 50 个字符！' },
          ]}
        >
          <Input.TextArea />
        </Form.Item>
      </Form>
    </ModalButton>
  );
}
