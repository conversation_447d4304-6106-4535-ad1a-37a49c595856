import Form from '@ant-design/compatible/es/form';
import QuestionCircleOutlined from '@ant-design/icons/es/icons/QuestionCircleOutlined';
import React, { useState } from 'react';
import { useHistory } from 'react-router-dom';

import { UserSelect as UserSelectByBlockGuid } from '@manyun/auth-hub.ui.user-select';
import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import { DeviceOrSpaceSelect } from '@manyun/resource-hub.ui.device-or-space-select';
import { useLazyEventAllVersionDetail } from '@manyun/ticket.gql.client.tickets';
import { SlaUnit } from '@manyun/ticket.model.task';
import { SlaSelect } from '@manyun/ticket.ui.sla-select';

import {
  FooterToolBar,
  GutterWrapper,
  LocationCascader,
  TinyCard,
  TinyUpload,
} from '@manyun/dc-brain.legacy.components';
import { taskCenterService } from '@manyun/dc-brain.legacy.services';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';

import TicketSubType from './../../../views/tickets/components/filters/ticket-sub-type';

/**
 * 维修工单创建表单
 * @param {object} props
 * @param {import('antd-3/lib/form/Form').WrappedFormUtils} props.form
 * @param {string} props.ticketType 工单类型。在实例化时会传入此 prop
 */
export function RepairTicketNew({ form, ticketType }) {
  const [loading, setLoading] = useState(false);
  const [eventInfo, setEventInfo] = useState({});
  const history = useHistory();
  const [fetchEventDetail] = useLazyEventAllVersionDetail();
  const { getFieldDecorator, getFieldValue, validateFields, setFieldsValue } = form;

  const location = getFieldValue('location');
  const taskSubType = getFieldValue('taskSubType');
  const relateTaskNo = getFieldValue('relateTaskNo');

  return (
    <TinyCard title="基本信息">
      <Form
        colon={false}
        labelCol={{ span: 3 }}
        wrapperCol={{ span: 12 }}
        style={{ width: '680px' }}
      >
        <Form.Item label="关联事件">
          {getFieldDecorator('relateTaskNo')(
            <Input
              onBlur={async e => {
                if (!e.target.value) {
                  setEventInfo({});
                  setFieldsValue({
                    taskSubType: undefined,
                    location: undefined,
                    targetId: undefined,
                  });
                  return;
                }
                const { data, error } = await fetchEventDetail({
                  variables: { query: { eventId: e.target.value, bizType: 'REPAIR' } },
                });
                if (error) {
                  setEventInfo({});
                  setFieldsValue({
                    taskSubType: undefined,
                    location: undefined,
                    targetId: undefined,
                  });
                  message.error(error.message || '获取事件信息失败');
                  return;
                }
                if (!data.eventAllVersionDetail.data) {
                  setEventInfo({});
                  setFieldsValue({
                    taskSubType: undefined,
                    location: undefined,
                    targetId: undefined,
                  });
                  return;
                }
                setEventInfo(data.eventAllVersionDetail.data);
                setFieldsValue({
                  taskSubType: data.eventAllVersionDetail.data.targetType,
                  location: data.eventAllVersionDetail.data.blockGuid.split('.'),
                  targetId: undefined,
                });
              }}
            />
          )}
          <Typography.Text type="secondary">{eventInfo.eventDesc}</Typography.Text>
        </Form.Item>
        <Form.Item label="目标类型">
          {getFieldDecorator('taskSubType', {
            rules: [{ required: true, message: '目标类型必选！' }],
          })(
            <TicketSubType
              style={{ width: 200 }}
              disabled={!!relateTaskNo}
              ticketType={ticketType}
              onChange={() => {
                setFieldsValue({ targetId: undefined });
              }}
            />
          )}
        </Form.Item>
        <Form.Item label="位置">
          {getFieldDecorator('location', {
            rules: [
              {
                required: true,
                type: 'number',
                transform: value => (value?.length === 2 ? 2 : false),
                message: '必须选到楼！',
              },
            ],
          })(
            <LocationCascader
              style={{ width: 200 }}
              currentAuthorize
              disabled={!!relateTaskNo}
              onChange={() => {
                setFieldsValue({ targetId: null });
              }}
            />
          )}
        </Form.Item>
        <Form.Item label="目标名称">
          {getFieldDecorator('targetId', {
            rules: [
              {
                required: true,
                message: '目标名称必选',
              },
              {
                max: taskSubType === 'OTHER' && 20,
                message: '最多输入 20 个字符！',
              },
            ],
          })(
            <DeviceOrSpaceSelect
              idcTag={location?.[0]}
              blockTag={location?.[1]}
              disabled={location && location.length === 2 && taskSubType ? false : true}
              type={taskSubType}
              roomGuids={taskSubType === 'ROOM' && eventInfo?.targetGuidList}
              deviceGuidList={taskSubType === 'DEVICE' && eventInfo?.targetGuidList}
            />
          )}
        </Form.Item>
        <Form.Item label="工单标题">
          {getFieldDecorator('taskTitle', {
            rules: [
              {
                required: true,
                whitespace: true,
                message: '工单标题必填！',
              },
              {
                type: 'string',
                max: 20,
                message: '最多输入 20 个字符！',
              },
            ],
          })(<Input />)}
        </Form.Item>
        <Form.Item label="故障说明">
          {getFieldDecorator('faultDesc', {
            rules: [
              {
                required: true,
                whitespace: true,
                message: '故障说明必填！',
              },
              {
                type: 'string',
                max: 50,
                message: '最多输入 50 个字符！',
              },
            ],
          })(<Input.TextArea />)}
        </Form.Item>
        <Form.Item label="指派人">
          {getFieldDecorator('assigneeList')(
            <UserSelectByBlockGuid
              disabled={!location || (location && location.length !== 2)}
              blockGuid={location && location.join('.')}
              placeholder="请根据用户名或id搜索"
              labelInValue
              includeCurrentUser
              style={{ width: '90%' }}
            />
          )}
          &nbsp;&nbsp;
          <Tooltip title="指派后，仅指派人可执行工单">
            <QuestionCircleOutlined />
          </Tooltip>
        </Form.Item>
        <Form.Item label="SLA">
          {getFieldDecorator('taskSla', { initialValue: { sla: 7, unit: SlaUnit.Day } })(
            <SlaSelect />
          )}
        </Form.Item>
        <Form.Item label="附件">
          {getFieldDecorator('attachments')(
            <TinyUpload accept=".png,.jpg,.gif,.mp4" showUploadBtn showAccept allowDelete />
          )}
        </Form.Item>
      </Form>
      <FooterToolBar>
        <GutterWrapper>
          <Button
            type="primary"
            loading={loading}
            onClick={() => {
              validateFields(async (error, valueMap) => {
                if (error) {
                  return;
                }
                setLoading(true);
                const id = await createTicket(getQ(valueMap));
                if (id) {
                  setTimeout(() => {
                    history.push(urls.generateTicketDetailLocation({ ticketType, id }));
                  }, 1.5 * 1000);
                }
                setLoading(false);
              });
            }}
          >
            提交
          </Button>
          <Button
            loading={loading}
            onClick={() => {
              history.goBack();
            }}
          >
            取消
          </Button>
        </GutterWrapper>
      </FooterToolBar>
    </TinyCard>
  );
}

export default Form.create()(RepairTicketNew);

function getQ({
  taskSubType,
  location,
  targetType,
  targetId,
  taskTitle,
  faultDesc,
  attachments,
  assigneeList,
  taskSla,
  relateTaskNo,
}) {
  const blockGuid = location.join('.');
  const fileInfoList = Array.isArray(attachments) ? attachments : null;

  return {
    taskSubType,
    idcTag: location[0],
    blockGuid,
    targetType,
    targetId,
    taskTitle,
    faultDesc,
    fileInfoList,
    relateTaskNo,
    taskSla: taskSla.sla,
    unit: taskSla.unit,
    relateBizType: relateTaskNo ? (relateTaskNo.startsWith('N') ? 'EVENT_BETA' : 'EVENT') : '',
    assigneeList: assigneeList ? [{ id: assigneeList.key, userName: assigneeList.label }] : null,
  };
}

async function createTicket(data) {
  const { response, error } = await taskCenterService.createRepairTicket(data);

  if (error) {
    message.error(error);
    return;
  }

  message.success('创建成功！');
  return response;
}
