/**
 * @typedef {import('@/biz-types/ticket').RepairTicketProperties} RepairTicketProperties
 * @typedef {import('@/biz-types/ticket').RepairTicketContentState} RepairTicketContentState
 * @typedef {import('@/biz-types/ticket').RepairTicketContentReducerActionType} RepairTicketContentReducerActionType
 */
import { CheckOutlined, CloseOutlined, DownOutlined, EditOutlined } from '@ant-design/icons';
import chunk from 'lodash.chunk';
import pick from 'lodash/pick';
import moment from 'moment';
import React, { useCallback, useEffect, useMemo, useReducer, useRef, useState } from 'react';
import { Link, Redirect } from 'react-router-dom';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { User } from '@manyun/auth-hub.ui.user';
import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Badge } from '@manyun/base-ui.ui.badge';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Dropdown } from '@manyun/base-ui.ui.dropdown';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { Popover } from '@manyun/base-ui.ui.popover';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { fetchBizFileInfos } from '@manyun/dc-brain.service.fetch-biz-file-infos';
import { BackendTaskStatus } from '@manyun/ticket.model.ticket';
import {
  FeedbackExceptionModalButton,
  ManufacturerSparePartsReplacementModalButton,
  ManufacturerSparePartsReplacementTable,
  ProcessFeedbackTable,
} from '@manyun/ticket.page.repair';
import {
  generateEventDetailRoutePath,
  generateEvnetLocation,
  generateTicketLocation,
} from '@manyun/ticket.route.ticket-routes';
import { deleteRepairSpare } from '@manyun/ticket.service.delete-repair-spare';
import { fetchRepairRecord } from '@manyun/ticket.service.fetch-repair-record';
import { fetchRepairSpares } from '@manyun/ticket.service.fetch-repair-spares';
import { updateRepairProcessFeedbacks } from '@manyun/ticket.service.update-repair-process-feedbacks';
import { updateRepairRecords } from '@manyun/ticket.service.update-repair-records';
import { updateRepairSpare } from '@manyun/ticket.service.update-repair-spare';
import { updateRepairTicket } from '@manyun/ticket.service.update-repair-ticket';
import { TicketStatusText } from '@manyun/ticket.ui.ticket-status-text';

import {
  ApproveLink,
  Ellipsis,
  GutterWrapper,
  TinyTable,
} from '@manyun/dc-brain.legacy.components';
import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';
import { TICKET_STATUS_KEY_TEXT_MAP } from '@manyun/dc-brain.legacy.pages/ticket/constants';
import {
  dcomService,
  taskCenterService,
  ticketService,
  vendorService,
} from '@manyun/dc-brain.legacy.services';
import * as urlsUtil from '@manyun/dc-brain.legacy.utils/urls';

import FeedbackProgressModalButton from './feedback-progress-modal-button';
import NotifyManufacturerModalButton from './notify-manufacturer-modal-button';
import TargetLink from './target-link';
import { useWorkDays } from './use-workdays';

/**
 * 维修单内容部分
 *
 * 分为 2 部分
 *  - 基本信息
 *  - 子任务信息（进度反馈、入室申请、出门申请、通知厂商）
 *
 * @param {object} props
 * @param {import('@/biz-types/ticket').TicketBaseInfo} props.basicInfo
 */
export default function RepairTicketContent({ basicInfo, getBasicInfo }) {
  const { taskProperties, taskNo, taskStatus, taskAssignee, idcTag, blockTag } = basicInfo;
  const [, { checkUserId }] = useAuthorized();
  const confirmCompleteTimeRef = useRef();
  const repairHoursRef = useRef();
  const [isCompleteTimeEditing, setIsCompleteTimeEditing] = useState(false);
  const [isCompleteRepairHours, setIsCompleteRepairHours] = useState(false);
  const [isOtherRecordEditable, setIsOtherRecordEditable] = useState(false);
  const isCurrentUserCreator =
    checkUserId(taskAssignee) &&
    ![
      BackendTaskStatus.FINISH,
      BackendTaskStatus.FAILURE,
      BackendTaskStatus.CLOSE_APPROVER,
    ].includes(taskStatus);
  const [
    {
      loading,
      activeSubTaskValue,
      progressFeedbacks,
      visitorInfos,
      exWarehouseInfos,
      accessTaskInfos,
      notificationRecords,
      refreshCount,
    },
    dispatch,
  ] = useReducer(reducer, initialState);
  const underProcessingStep = taskStatus === BackendTaskStatus.PROCESSING;
  const tableDatas = [
    progressFeedbacks,
    notificationRecords,
    visitorInfos,
    exWarehouseInfos,
    accessTaskInfos,
  ];
  const [spares, setSpares] = useState();
  const [spareTableLoading, setSpareTableLoading] = useState(false);
  const [warrantyVendorContacts, setWarrantyVendorContacts] = useState([]);
  const [repairRecord, setRepairRecord] = useState({});
  const [defaultWorkdays, getWorkdays] = useWorkDays();
  const getRepairRecords = async ({ taskNo, idcTag }) => {
    const { data, error } = await fetchRepairRecord({ taskNo, idcTag });
    if (error) {
      message.error(error.message);
      return;
    }
    setRepairRecord(data);
  };
  const userEditable = isCurrentUserCreator && taskStatus === BackendTaskStatus.PROCESSING;
  /**
   * 用于动态展示子任务相关的操作按钮和信息
   */
  const subTaskValueIdx = subTaskValueMapList.findIndex(item => item.value === activeSubTaskValue);

  /**
   * @type {React.RefObject<RepairTicketProperties>}
   */
  const propertiesRef = useRef({});
  const momentCompleteTime = useMemo(() => {
    if (taskProperties) {
      const { completeTime } = JSON.parse(taskProperties);
      return completeTime ? moment(completeTime) : undefined;
    }
  }, [taskProperties]);
  const repairTicketHours = useMemo(() => {
    if (taskProperties) {
      const { repairHours } = JSON.parse(taskProperties);
      return repairHours ? repairHours : defaultWorkdays;
    }
  }, [defaultWorkdays, taskProperties]);
  const getSpares = async () => {
    setSpareTableLoading(true);
    const { error, data } = await fetchRepairSpares({ taskNo, idcTag });
    setSpareTableLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    setSpares(data.data);
  };
  const updateTableSpare = async spare => {
    const { error } = await updateRepairSpare({ ...spare });
    if (error) {
      message.error(error.message);
      return;
    }
    getSpares();
  };
  const deleteTableSpare = async id => {
    const { error } = await deleteRepairSpare({ id });
    if (error) {
      message.error(error.message);
      return;
    }
    getSpares();
  };
  useEffect(() => {
    if (activeSubTaskValue === 'ex-warehouse') {
      getSpares();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeSubTaskValue]);
  useEffect(() => {
    if (!(taskProperties && typeof taskProperties == 'string')) {
      return;
    }
    try {
      propertiesRef.current = JSON.parse(taskProperties);
    } catch (error) {
      console.error(error);
    }
  }, [taskProperties]);

  useEffect(() => {
    if (!taskNo) {
      return;
    }
    dcomService
      .fetchTicketAttachmentInfos({
        targetId: taskNo,
        targetType: 'REPAIR',
      })
      .then(({ response, error }) => {
        if (error) {
          message.error(error);
          return;
        }
        dispatch({ type: 'SET_ATTACHMENT_INFOS', attachmentInfos: response.data });
      });
  }, [taskNo]);

  useEffect(() => {
    try {
      const { repairHours } = JSON.parse(taskProperties);
      /*填写完预计完成时间且维修工时为空，则根据 预计完成时间 - 工单接单时间，去除非工作日，精确到天，进一制 得出默认的维修工时 */
      if (momentCompleteTime && basicInfo.takeTime && !repairHours) {
        getWorkdays({
          startTime: moment(basicInfo.takeTime).valueOf(),
          endTime: moment(momentCompleteTime).valueOf(),
        });
      }
    } catch (e) {
      console.error(e);
    }
  }, [basicInfo.takeTime, getWorkdays, momentCompleteTime, taskProperties]);

  const onLoadProgressFeedbacks = useCallback(() => {
    taskCenterService
      .fetchRepairTicketProgressFeedbacks({ taskNo })
      .then(async ({ response, error }) => {
        if (error) {
          message.error(error);
          return;
        }
        const { data } = response;
        const resolved = !!data?.some(record => record.resolved);

        let mergedData = data;
        if (data.length > 0) {
          const ids = data.map(record => record.id);
          const { error: fileError, data: files } = await fetchBizFileInfos({
            targetId: ids[0],
            targetIdList: ids,
            targetType: 'REPAIR_FEEDBACK',
          });
          if (fileError) {
            message.error(fileError.message);
            return;
          }
          if (files.data.length > 0) {
            mergedData = data.map(record => {
              return {
                ...record,
                files: files.data.filter(file => file.targetId === record.id.toString()),
              };
            });
          }
        }
        dispatch({
          type: 'SET_PROGRESS_FEEDBACKS',
          progressFeedbacks: { resolved, data: mergedData, total: data?.length },
        });
      });
  }, [taskNo]);

  useEffect(() => {
    if (!taskNo) {
      return;
    }
    if (activeSubTaskValue === 'progress-feedbacks') {
      onLoadProgressFeedbacks();
    }
    if (activeSubTaskValue === 'notifications-records') {
      taskCenterService
        .fetchRepairTicketNotificationRecords({ taskNo })
        .then(({ response, error }) => {
          if (error) {
            message.error(error);
            return;
          }
          const { data } = response;
          dispatch({
            type: 'SET_NOTIFICATION_RECORDS',
            notificationRecords: { data, total: data?.length },
          });
        });
    }
    if (activeSubTaskValue === 'ex-warehouse') {
      ticketService
        .fetchRelateTask({
          relateTaskNo: taskNo,
          taskType: 'WAREHOUSE',
          taskSubType: 'EX_WAREHOUSE',
        })
        .then(({ response, error }) => {
          if (error) {
            message.error(error);
            return;
          }
          const { data } = response;
          dispatch({
            type: 'SET_EX_WAREHOUSE',
            exWarehouseInfos: { data, total: data?.length },
          });
        });
    }
    if (activeSubTaskValue === 'entry-requests') {
      ticketService
        .fetchRelateTask({ relateTaskNo: taskNo, taskType: 'VISITOR' })
        .then(({ response, error }) => {
          if (error) {
            message.error(error);
            return;
          }
          const { data } = response;
          dispatch({
            type: 'SET_VISITORINFOS',
            visitorInfos: { data, total: data?.length },
          });
        });
    }
    if (activeSubTaskValue === 'departure-requests') {
      ticketService
        .fetchRelateTask({ relateTaskNo: taskNo, taskType: 'ACCESS' })
        .then(({ response, error }) => {
          if (error) {
            message.error(error);
            return;
          }
          const { data } = response;
          dispatch({
            type: 'SET_ACCESSTASKINFOS',
            accessTaskInfos: { data, total: data?.length },
          });
        });
    }
  }, [taskNo, activeSubTaskValue, refreshCount, onLoadProgressFeedbacks]);

  const warrantyVendor = propertiesRef.current?.warrantyVendor;
  useEffect(() => {
    if (!taskNo || !idcTag) {
      return;
    }
    getRepairRecords({ taskNo, idcTag });
  }, [taskNo, idcTag]);
  useEffect(() => {
    if (!warrantyVendor) {
      return;
    }
    (async () => {
      const { error, response } = await vendorService.getVendorDetail({
        vendorCode: warrantyVendor,
      });
      if (error) {
        message.error(error);
        return;
      }
      if (response) {
        const contacts = response.vendorContacts;
        if (response.contactName) {
          contacts.unshift({
            id: response.contactName,
            name: response.contactName,
            mobile: response.contactMobile,
            type: 'MAIN',
          });
        }
        setWarrantyVendorContacts(contacts);
      }
    })();
  }, [warrantyVendor]);

  const refreshData = () => {
    dispatch({ type: 'INCREMENT_REFRESH_COUNT' });
  };
  const handleRowEditing = async ({ id, feedbackContent, resolved }) => {
    const { error } = await updateRepairProcessFeedbacks({ id, feedbackContent, resolved });
    if (error) {
      message.error(error.message);
      return Promise.reject();
    }
    onLoadProgressFeedbacks();
  };

  const renderTableByActiveSubTaskValue = activeSubTaskValue => {
    switch (activeSubTaskValue) {
      case 'progress-feedbacks':
        return (
          <Space style={{ width: '100%' }} direction="vertical">
            <Descriptions column={2} colon={false}>
              <Descriptions.Item
                span={4}
                label="故障根因描述"
                contentStyle={{ width: 1 }}
                labelStyle={{ width: 132 }}
              >
                <RepairFeedbackTypeComponent
                  taskNo={taskNo}
                  idcTag={idcTag}
                  feedbackType="REPAIR_REASON"
                  userEditable={userEditable}
                  value={repairRecord?.repairReason}
                  repairRecord={repairRecord}
                  isOtherRecordEditable={isOtherRecordEditable}
                  setIsOtherRecordEditable={setIsOtherRecordEditable}
                  onSuccess={() => {
                    getRepairRecords({ taskNo, idcTag });
                    getBasicInfo();
                  }}
                />
              </Descriptions.Item>
              <Descriptions.Item
                label="自维修协同人"
                contentStyle={{ width: 1 }}
                labelStyle={{ width: 132 }}
              >
                <RepairFeedbackTypeComponent
                  taskNo={taskNo}
                  idcTag={idcTag}
                  feedbackType="SYSTEM_MAINTENANCE_STAFF"
                  userEditable={userEditable}
                  repairRecord={repairRecord}
                  value={
                    Array.isArray(repairRecord?.repairPersonList) &&
                    repairRecord.repairPersonList.length > 0
                      ? repairRecord.repairPersonList.filter(person => person.type === 'SYSTEM')
                      : undefined
                  }
                  isOtherRecordEditable={isOtherRecordEditable}
                  setIsOtherRecordEditable={setIsOtherRecordEditable}
                  onSuccess={() => getRepairRecords({ taskNo, idcTag })}
                />
              </Descriptions.Item>
              <Descriptions.Item
                label="外部厂商维修人员"
                contentStyle={{ width: 1 }}
                labelStyle={{ width: 132 }}
              >
                <RepairFeedbackTypeComponent
                  taskNo={taskNo}
                  feedbackType="OUTSIDE_MAINTENANCE_STAFF"
                  userEditable={userEditable}
                  idcTag={idcTag}
                  repairRecord={repairRecord}
                  value={
                    Array.isArray(repairRecord?.repairPersonList) &&
                    repairRecord.repairPersonList.length > 0
                      ? repairRecord.repairPersonList.filter(person => person.type === 'OUTSIDE')
                      : undefined
                  }
                  isOtherRecordEditable={isOtherRecordEditable}
                  setIsOtherRecordEditable={setIsOtherRecordEditable}
                  onSuccess={() => getRepairRecords({ taskNo, idcTag })}
                />
              </Descriptions.Item>
              <Descriptions.Item
                label="故障维修过程记录"
                span={2}
                contentStyle={{ width: 1 }}
                labelStyle={{ width: 132 }}
              >
                <RepairFeedbackTypeComponent
                  taskNo={taskNo}
                  idcTag={idcTag}
                  feedbackType="REPAIR_PROCESS"
                  userEditable={userEditable}
                  repairRecord={repairRecord}
                  value={repairRecord?.repairProcess}
                  isOtherRecordEditable={isOtherRecordEditable}
                  setIsOtherRecordEditable={setIsOtherRecordEditable}
                  onSuccess={() => getRepairRecords({ taskNo, idcTag })}
                />
              </Descriptions.Item>
            </Descriptions>
            <ProcessFeedbackTable
              processFeedbacks={tableDatas[subTaskValueIdx]?.data}
              handleRowEditing={handleRowEditing}
              isCurrentUserCreator={isCurrentUserCreator}
              onRefresh={() => {
                onLoadProgressFeedbacks();
              }}
            />
          </Space>
        );
      case 'notifications-records':
        return (
          <Space style={{ width: '100%' }} direction="vertical">
            <>
              {warrantyVendorContacts.length > 0 &&
                chunk(warrantyVendorContacts, 4).map((rowItems, rowIndex) => {
                  while (rowItems.length < 4) {
                    rowItems.push(null);
                  }
                  const rowKey = rowIndex;
                  return (
                    <Row key={rowKey} gutter={16}>
                      {rowItems.map(item => {
                        const isMainContact = item?.type === 'MAIN';

                        return item ? (
                          <Col key={item.id} span={6}>
                            <Card>
                              <Space direction="vertical" style={{ width: '100%' }}>
                                <div
                                  style={{
                                    width: '100%',
                                    display: 'flex',
                                    justifyContent: 'space-between',
                                    alignItems: 'center',
                                  }}
                                >
                                  <div
                                    style={{
                                      display: 'flex',
                                      marginRight: 8,
                                      overflow: 'hidden',
                                    }}
                                  >
                                    <Typography.Text
                                      strong
                                      style={{
                                        flex: 1,
                                      }}
                                      ellipsis={{ tooltip: true }}
                                    >
                                      {item.name || '--'}
                                    </Typography.Text>
                                    <Space style={{ marginLeft: 8 }}>
                                      <Badge color={isMainContact ? 'blue' : 'green'} />
                                      <Typography.Text type="secondary">
                                        {isMainContact ? '主联系人' : '分区联系人'}
                                      </Typography.Text>
                                    </Space>
                                  </div>

                                  {userEditable && (
                                    <FeedbackExceptionModalButton
                                      taskNo={taskNo}
                                      idcTag={idcTag}
                                      currentProcessor={basicInfo.taskAssigneeName}
                                      blockGuid={basicInfo.blockTag}
                                      onSuccess={getBasicInfo}
                                    />
                                  )}
                                </div>
                                <Typography.Text type="secondary">
                                  {item.mobile || '--'}
                                </Typography.Text>
                              </Space>
                            </Card>
                          </Col>
                        ) : null;
                      })}
                    </Row>
                  );
                })}
            </>
            <TinyTable
              loading={loading}
              rowKey={TABLE_ID[subTaskValueIdx]}
              columns={SUB_TASK_DATA_COLUMNS[subTaskValueIdx]}
              dataSource={tableDatas[subTaskValueIdx]?.data}
              pagination={{ total: tableDatas[subTaskValueIdx]?.total }}
            />
          </Space>
        );
      case 'ex-warehouse':
        const relaTasks = tableDatas[3]?.data?.filter(
          task => !['0', '1'].includes(task.taskStatus)
        );
        return (
          <Space style={{ width: '100%', display: 'flex' }} direction="vertical">
            {Array.isArray(relaTasks) && relaTasks.length > 0 && (
              <Space size={0}>
                <span> 出库工单</span>
                <Space split="、">
                  {relaTasks.map(item => (
                    <Link
                      key={item.taskNo}
                      to={generateTicketLocation({ id: item.taskNo, ticketType: 'warehouse' })}
                    >
                      {item.taskNo}
                    </Link>
                  ))}
                </Space>
                <span>还未完成出库，完成出库后会将实际出库备件信息填入下方表格</span>
              </Space>
            )}
            <ManufacturerSparePartsReplacementTable
              spares={spares}
              loading={spareTableLoading}
              handleRowDeleting={deleteTableSpare}
              handleRowEditing={updateTableSpare}
              isCurrentUserCreator={isCurrentUserCreator}
            />
          </Space>
        );
      default:
        return (
          <TinyTable
            loading={loading}
            rowKey={TABLE_ID[subTaskValueIdx]}
            columns={SUB_TASK_DATA_COLUMNS[subTaskValueIdx]}
            dataSource={tableDatas[subTaskValueIdx]?.data}
            pagination={{ total: tableDatas[subTaskValueIdx]?.total }}
          />
        );
    }
  };

  return (
    <Space style={{ width: '100%' }} direction="vertical">
      <Descriptions column={2} colon={false}>
        <Descriptions.Item label="目标名称" labelStyle={{ width: 132 }}>
          <TargetLink
            {...pick(propertiesRef.current, ['targetId', 'targetName', 'targetType', 'spaceGuid'])}
          />
        </Descriptions.Item>
        {(propertiesRef.current?.vendorCode || propertiesRef.current?.modelCode) && (
          <Descriptions.Item label="品牌/型号" labelStyle={{ width: 132 }}>
            {propertiesRef.current?.vendorCode || BLANK_PLACEHOLDER}/
            {propertiesRef.current?.modelCode || BLANK_PLACEHOLDER}
          </Descriptions.Item>
        )}
        {basicInfo.relateTaskNo &&
          (basicInfo.relateBizType === 'EVENT' || basicInfo.relateBizType === 'EVENT_BETA') && (
            <Descriptions.Item label="关联事件单号" labelStyle={{ width: 132 }}>
              <Link
                to={
                  basicInfo.relateBizType === 'EVENT_BETA'
                    ? generateEvnetLocation({ id: basicInfo.relateTaskNo })
                    : generateEventDetailRoutePath({
                        id: basicInfo.relateTaskNo,
                      })
                }
              >
                {basicInfo.relateTaskNo}
              </Link>
            </Descriptions.Item>
          )}
        {basicInfo.taskSubType === 'DEVICE' && (
          <Descriptions.Item label="通知状态" labelStyle={{ width: 132 }}>
            {propertiesRef.current?.notifyStatus?.name || BLANK_PLACEHOLDER}
          </Descriptions.Item>
        )}
        {Array.isArray(basicInfo.assigneeList) && basicInfo.assigneeList.length > 0 && (
          <Descriptions.Item label="指派人" labelStyle={{ width: 132 }}>
            <Space size={0} split={<Divider type="vertical" spaceSize="mini" emphasis />}>
              {basicInfo.assigneeList.map(item => (
                <User.Link key={item.id} id={item.id} name={item.userName} />
              ))}
            </Space>
          </Descriptions.Item>
        )}
        {propertiesRef.current?.warrantyVendor && (
          <Descriptions.Item label="维保厂商" labelStyle={{ width: 132 }}>
            {propertiesRef.current?.warrantyVendor || BLANK_PLACEHOLDER}
          </Descriptions.Item>
        )}
        {propertiesRef.current?.warrantyTime && (
          <Descriptions.Item label="过保时间" labelStyle={{ width: 132 }}>
            {propertiesRef.current?.warrantyTime
              ? moment(propertiesRef.current.warrantyTime).format('YYYY-MM-DD')
              : BLANK_PLACEHOLDER}
          </Descriptions.Item>
        )}
        <Descriptions.Item label="预计完成时间" labelStyle={{ width: 132 }}>
          <>
            {isCompleteTimeEditing ? (
              <Space>
                <DatePicker
                  defaultValue={momentCompleteTime}
                  size="small"
                  onChange={value => {
                    confirmCompleteTimeRef.current = value;
                  }}
                />
                <CheckOutlined
                  style={{ color: `var(--${prefixCls}-primary-color)` }}
                  onClick={async () => {
                    if (confirmCompleteTimeRef.current) {
                      const { error } = await updateRepairTicket({
                        taskNo,
                        idcTag,
                        completeTime: moment(confirmCompleteTimeRef.current).valueOf(),
                      });
                      if (error) {
                        message.error(error.message);
                        return;
                      }
                      setIsCompleteTimeEditing(false);
                      getBasicInfo();
                    } else {
                      message.error('请选择正确日期后提交！');
                      return;
                    }
                  }}
                />
                <CloseOutlined
                  style={{ color: `var(--${prefixCls}-primary-color)` }}
                  onClick={() => {
                    setIsCompleteTimeEditing(false);
                  }}
                />
              </Space>
            ) : (
              <Space>
                <span>{momentCompleteTime ? momentCompleteTime.format('YYYY-MM-DD') : '--'}</span>
                {userEditable && (
                  <EditOutlined
                    style={{ color: `var(--${prefixCls}-primary-color)` }}
                    onClick={() => {
                      setIsCompleteTimeEditing(true);
                    }}
                  />
                )}
                {!momentCompleteTime &&
                  isCurrentUserCreator &&
                  taskStatus === BackendTaskStatus.PROCESSING && (
                    <Typography.Text type="warning">请及时反馈预计完成时间</Typography.Text>
                  )}
              </Space>
            )}
          </>
        </Descriptions.Item>
        <Descriptions.Item label="维修工时" labelStyle={{ width: 132 }}>
          {isCompleteRepairHours ? (
            <Space>
              <div>
                <InputNumber
                  defaultValue={repairTicketHours}
                  size="small"
                  min={1}
                  max={999999}
                  precision={0}
                  onChange={value => {
                    repairHoursRef.current = value;
                  }}
                />
                天
              </div>
              <CheckOutlined
                style={{ color: `var(--${prefixCls}-primary-color)` }}
                onClick={async () => {
                  if (repairHoursRef.current) {
                    const { error } = await updateRepairTicket({
                      taskNo,
                      idcTag,
                      repairHours: repairHoursRef.current,
                    });
                    if (error) {
                      message.error(error.message);
                      return;
                    }
                    setIsCompleteRepairHours(false);
                    getBasicInfo();
                  } else {
                    message.error('请输入维修工时');
                    return;
                  }
                }}
              />
              <CloseOutlined
                style={{ color: `var(--${prefixCls}-primary-color)` }}
                onClick={() => {
                  setIsCompleteRepairHours(false);
                }}
              />
            </Space>
          ) : (
            <Space>
              <span>{repairTicketHours ? `${repairTicketHours}天` : BLANK_PLACEHOLDER}</span>
              {userEditable && (
                <EditOutlined
                  style={{ color: `var(--${prefixCls}-primary-color)` }}
                  onClick={() => {
                    setIsCompleteRepairHours(true);
                  }}
                />
              )}
            </Space>
          )}
        </Descriptions.Item>
        <Descriptions.Item label="故障说明" labelStyle={{ width: 132 }}>
          {propertiesRef.current?.faultDesc || BLANK_PLACEHOLDER}
        </Descriptions.Item>
        {basicInfo.workFlowId && (
          <Descriptions.Item label="审批" labelStyle={{ width: 132 }}>
            <ApproveLink id={basicInfo.endWorkFlowId} />
          </Descriptions.Item>
        )}
      </Descriptions>
      <GutterWrapper flex justifyContent="space-between">
        <RenderAuthorizedStep userId={taskAssignee}>
          {authorized =>
            authorized && underProcessingStep ? (
              <>
                {activeSubTaskValue === 'progress-feedbacks' &&
                  (progressFeedbacks.resolved ? (
                    // 这里放个 `span` 是为了不破坏外层的 `flex` 布局，
                    // 否则右侧的 `Radio.Group` 将会被布局到左侧
                    <span />
                  ) : (
                    <FeedbackProgressModalButton
                      taskNo={taskNo}
                      text={subTaskValueMap['progress-feedbacks']}
                      onSuccess={refreshData}
                    />
                  ))}
                {activeSubTaskValue === 'entry-requests' && (
                  <Button
                    key="createVisitorTask"
                    type="primary"
                    href={urlsUtil.generateTicketCreateUrl({
                      ticketType: 'visitor',
                      variables: { idcTag: idcTag, relateTaskNo: taskNo, relateTaskType: 'REPAIR' },
                    })}
                  >
                    入室申请
                  </Button>
                )}
                {activeSubTaskValue === 'departure-requests' && (
                  <Button
                    key="createAccessTask"
                    type="primary"
                    href={urlsUtil.generateTicketCreateUrl({
                      ticketType: 'access',
                      variables: {
                        blockTag: blockTag,
                        taskSubType: 'GO_OUT',
                        relateTaskNo: taskNo,
                        relateTaskType: 'ACCESS',
                      },
                    })}
                  >
                    出门申请
                  </Button>
                )}
                {activeSubTaskValue === 'notifications-records' && (
                  <NotifyManufacturerModalButton
                    taskNo={taskNo}
                    text={subTaskValueMap['notifications-records']}
                    warrantyVendor={propertiesRef.current?.warrantyVendor}
                    getBasicInfo={getBasicInfo}
                    onSuccess={refreshData}
                  />
                )}
                {activeSubTaskValue === 'ex-warehouse' && (
                  <Dropdown
                    menu={{
                      items: [
                        {
                          label: (
                            <a
                              href={urlsUtil.generateTicketCreateUrl({
                                ticketType: 'warehouse',
                                variables: {
                                  relateTaskNo: taskNo,
                                  relateTaskType: 'REPAIR',
                                  blockTag: blockTag,
                                },
                              })}
                            >
                              自有备件出库
                            </a>
                          ),
                          key: 0,
                        },
                        {
                          label: (
                            <ManufacturerSparePartsReplacementModalButton
                              idcTag={idcTag}
                              taskNo={taskNo}
                              type="text"
                              onSuccess={getSpares}
                            />
                          ),
                          key: 1,
                        },
                      ],
                    }}
                    trigger={['click']}
                  >
                    <Button type="primary">
                      备件更换
                      <DownOutlined />
                    </Button>
                  </Dropdown>
                )}
              </>
            ) : (
              <span />
            )
          }
        </RenderAuthorizedStep>
        <Radio.Group
          value={activeSubTaskValue}
          onChange={({ target: { value } }) => {
            dispatch({ type: 'SET_ACTIVE_SUB_TASK_VALUE', value });
          }}
        >
          {(propertiesRef.current?.targetType?.code === 'DEVICE'
            ? subTaskValueMapList
            : subTaskValueMapList.filter(item => item.value !== 'notifications-records')
          ) // 空间类型的维修不需要展示通知厂商的功能
            .map((item, index) => (
              <Radio.Button key={item.value} value={item.value}>
                {subTaskValueMap[item.value]}
              </Radio.Button>
            ))}
        </Radio.Group>
      </GutterWrapper>

      {renderTableByActiveSubTaskValue(activeSubTaskValue)}
    </Space>
  );
}

/**
 * 子任务唯一码集合
 * @type {Array<RepairTicketContentState['activeSubTaskValue']>}
 */

const subTaskValueMapList = [
  { value: 'progress-feedbacks', label: '进度反馈' },
  { value: 'notifications-records', label: '通知厂商' },
  { value: 'entry-requests', label: '入室申请' },
  { value: 'ex-warehouse', label: '备件更换' },
  { value: 'departure-requests', label: '出门申请' },
];
const subTaskValueMap = {
  'progress-feedbacks': '进度反馈',
  'notifications-records': '通知厂商',
  'entry-requests': '入室申请',
  'ex-warehouse': '备件更换',
  'departure-requests': '出门申请',
};

const TABLE_ID = ['id', 'taskNo', 'taskNo', 'id'];

/**
 * 按 `SUB_TASK_VALUES` 顺序的数据表头集合
 */
const SUB_TASK_DATA_COLUMNS = [
  [
    {
      title: '反馈时间',
      dataIndex: 'gmtCreate',
      dataType: 'datetime',
    },
    {
      title: '反馈内容',
      dataIndex: 'feedback',
    },
    {
      title: '是否解决',
      dataIndex: 'resolved',
      render(resolved) {
        if (resolved === true) {
          return '是';
        }
        return '否';
      },
    },
    {
      title: '操作人',
      dataIndex: 'operatorName',
      render(name, { operatorId }) {
        return <UserLink userId={operatorId} userName={name} external />;
      },
    },
  ],
  [
    {
      title: '通知时间',
      dataIndex: 'gmtCreate',
      dataType: 'datetime',
    },
    {
      title: '通知人员',
      dataIndex: 'informByName',
    },
    {
      title: '通知方式',
      dataIndex: ['informMethod', 'name'],
    },
    {
      title: '通知内容',
      dataIndex: 'informContent',
    },
    {
      title: '操作人',
      dataIndex: 'operatorName',
      render(name, { operatorBy }) {
        return <UserLink userId={operatorBy} userName={name} external />;
      },
    },
  ],
  [
    {
      title: '入室单号',
      dataIndex: 'taskNo',
      dataType: {
        type: 'link',
        options: {
          to(text, record) {
            return urlsUtil.generateTicketDetailLocation({
              id: text,
              ticketType: record.taskType.toLowerCase(),
              idcTag: record.idcTag,
              blockTag: record.blockTag,
            });
          },
        },
      },
    },
    {
      title: '申请时间',
      dataIndex: 'gmtCreate',
      dataType: 'datetime',
    },
    {
      title: '入室人数',
      dataIndex: 'approveNumber',
      render(_, { taskProperties, taskNo }) {
        try {
          const props = JSON.parse(taskProperties);
          return (
            <ContentPopover
              title="维保人员"
              taskNo={taskNo}
              showContent={props.approveNumber + '人'}
            />
          );
        } catch (error) {
          return '--';
        }
      },
    },
    {
      title: '授权开始时间',
      dataIndex: 'approveStartTime',
      dataType: 'datetime',
      render(_, { taskProperties }) {
        try {
          const props = JSON.parse(taskProperties);
          return moment(props.approveStartTime).format('YYYY-MM-DD HH:mm:ss');
        } catch (error) {
          return '--';
        }
      },
    },
    {
      title: '授权结束时间',
      dataIndex: 'approveEndTime',
      dataType: 'datetime',
      render(_, { taskProperties }) {
        try {
          const props = JSON.parse(taskProperties);
          return moment(props.approveEndTime).format('YYYY-MM-DD HH:mm:ss');
        } catch (error) {
          return '--';
        }
      },
    },
    {
      title: '状态',
      dataIndex: 'taskStatus',
      render: text => <TicketStatusText status={text} />,
    },
    {
      title: '操作人',
      dataIndex: 'creatorName',
      render(creatorName, { creatorId }) {
        return <UserLink userId={creatorId} userName={creatorName} external />;
      },
    },
  ],

  [
    {
      title: '出库单号',
      dataIndex: 'taskNo',
      dataType: {
        type: 'link',
        options: {
          to(text, record) {
            return urlsUtil.generateTicketDetailLocation({
              id: text,
              ticketType: record.taskType.toLowerCase(),
              idcTag: record.idcTag,
              blockTag: record.blockTag,
            });
          },
        },
      },
    },
    {
      title: '标题',
      dataIndex: 'taskTitle',
    },
    {
      title: '状态',
      dataIndex: 'taskStatus',
      render: text => <TicketStatusText status={text} />,
    },
    {
      title: '操作人',
      dataIndex: 'creatorName',
      render(creatorName, { creatorId }) {
        return <UserLink userId={creatorId} userName={creatorName} external />;
      },
    },
  ],

  [
    {
      title: '出门单号',
      dataIndex: 'taskNo',
      dataType: {
        type: 'link',
        options: {
          to(text, record) {
            return urlsUtil.generateTicketDetailLocation({
              id: text,
              ticketType: record.taskType.toLowerCase(),
              idcTag: record.idcTag,
              blockTag: record.blockTag,
            });
          },
        },
      },
    },
    {
      title: '申请时间',
      dataIndex: 'gmtCreate',
      dataType: 'datetime',
    },
    {
      title: '设备数量',
      dataIndex: 'deviceNum',
      render(_, { taskProperties }) {
        try {
          const props = JSON.parse(taskProperties);
          return (
            <ContentsPopover
              title="设备数量"
              showContent={props.accessDeviceCountModel.total}
              content={objectToArray(props.accessDeviceCountModel.deviceTypeCountMap)}
            />
          );
        } catch (error) {
          return '--';
        }
      },
    },
    {
      title: '状态',
      dataIndex: 'taskStatus',
      render: text => TICKET_STATUS_KEY_TEXT_MAP[text],
    },
    {
      title: '操作人',
      dataIndex: 'creatorName',
      render(creatorName, { creatorId }) {
        return <UserLink userId={creatorId} userName={creatorName} external />;
      },
    },
  ],
];

/**
 * @type {RepairTicketContentState}
 */
const initialState = {
  attachmentInfos: [],
  activeSubTaskValue: 'progress-feedbacks',
  refreshCount: -1,
  loading: false,
  progressFeedbacks: {
    /**
     * 进度反馈-是否解决标识
     *
     * > 这里把初始值设置为 `true`，是为了保证在未获取进度反馈数据前不先显示`进度反馈`按钮，以免用户趁机点击
     *
     * See {@link [BUG #2752](http://chandao.manyun-local.com/zentao/bug-view-2752.html)}
     */
    resolved: true,

    data: [],
    total: 0,
  },
};

/**
 * @param {RepairTicketContentState} state
 * @param {{type:RepairTicketContentReducerActionType;[prop: string]: any}} action
 * @returns {RepairTicketContentState}
 */
function reducer(state, action) {
  switch (action.type) {
    case 'SET_ATTACHMENT_INFOS':
      return { ...state, attachmentInfos: action.attachmentInfos };
    case 'SET_ACTIVE_SUB_TASK_VALUE':
      return {
        ...state,
        loading: true,
        activeSubTaskValue: action.value,
        progressFeedbacks: initialState.progressFeedbacks,
        visitorInfos: undefined,
        accessTaskInfos: undefined,
        notificationRecords: undefined,
        exWarehouseInfos: undefined,
      };
    case 'SET_PROGRESS_FEEDBACKS':
      return { ...state, loading: false, progressFeedbacks: action.progressFeedbacks };
    case 'SET_VISITORINFOS':
      return { ...state, loading: false, visitorInfos: action.visitorInfos };
    case 'SET_EX_WAREHOUSE':
      return { ...state, loading: false, exWarehouseInfos: action.exWarehouseInfos };
    case 'SET_ACCESSTASKINFOS':
      return { ...state, loading: false, accessTaskInfos: action.accessTaskInfos };
    case 'SET_NOTIFICATION_RECORDS':
      return { ...state, loading: false, notificationRecords: action.notificationRecords };
    case 'INCREMENT_REFRESH_COUNT':
      return { ...state, loading: false, refreshCount: state.refreshCount + 1 };
    default:
      throw new Error();
  }
}

function objectToArray(object) {
  const map = new Map();
  return Object.keys(object).map(key => {
    const value = object[key];
    map.set('key', key);
    map.set('value', value);
    const result = key + ': ' + value;

    return result;
  });
}

/**
 * 维保人员（气泡）
 *
 * TODO：考虑抽个通用的组件？
 * @param {object} props
 * @param {string} props.users
 */
function ContentsPopover({ content, title, showContent }) {
  if (!content) {
    return BLANK_PLACEHOLDER;
  }

  return (
    <Popover
      placement="bottom"
      trigger="hover"
      title={title}
      content={content.map(name => (
        <Typography.Paragraph key={name}>{name}</Typography.Paragraph>
      ))}
    >
      <Ellipsis lines={1}>{showContent}</Ellipsis>
    </Popover>
  );
}

function ContentPopover({ title, taskNo, showContent }) {
  const [contents, setContents] = useState([]);

  useEffect(() => {
    if (!taskNo) {
      return;
    }
    taskCenterService.fetchVisitorStaffNameList({ taskNo }).then(({ response, error }) => {
      if (error) {
        return;
      }
      setContents(response.data);
    });
  }, [taskNo]);

  return (
    <Popover
      placement="bottom"
      title={title}
      trigger="hover"
      content={
        <>
          {contents.map(name => (
            <Typography.Paragraph key={name}>{name}</Typography.Paragraph>
          ))}
        </>
      }
    >
      {showContent}
    </Popover>
  );
}

function RenderAuthorizedStep({ userId, children }) {
  const [, { checkUserId }] = useAuthorized();
  const isCurrentUser = checkUserId(userId);
  if (typeof children == 'function') {
    return children(isCurrentUser);
  }
  return isCurrentUser ? children : <Redirect to={{ pathname: '/403' }} />;
}

function RepairFeedbackTypeComponent({
  taskNo,
  feedbackType,
  userEditable,
  onSuccess,
  value,
  idcTag,
  repairRecord,
  isOtherRecordEditable,
  setIsOtherRecordEditable,
}) {
  const [isEditing, setIsCompleteTimeEditing] = useState(false);

  const componentValueRef = useRef({});
  const renderContent = () => {
    switch (feedbackType) {
      case 'REPAIR_REASON':
        componentValueRef.current.reason = value;
        return {
          render: (
            <Input
              defaultValue={value}
              maxLength={256}
              size="small"
              allowClear
              onChange={e => {
                componentValueRef.current.reason = e.target.value;
              }}
            />
          ),
          display: <Typography.Text ellipsis={{ tooltip: true }}>{value || '--'}</Typography.Text>,
        };
      case 'SYSTEM_MAINTENANCE_STAFF':
        componentValueRef.current.systemUser = value?.map(user => ({
          ...user,
          value: user.id,
          label: user.name,
        }));

        return {
          render: (
            <UserSelect
              style={{ width: 156 }}
              maxTagCount="responsive"
              size="small"
              defaultValue={value?.map(user => ({
                ...user,
                value: user.id,
                label: user.name,
              }))}
              mode="multiple"
              onChange={value => {
                componentValueRef.current.systemUser = value?.map(user => ({
                  id: user.value,
                  name: user.label,
                  type: 'SYSTEM',
                }));
              }}
            />
          ),
          display: (
            <>
              {value?.length > 0 ? (
                <Popover
                  content={
                    <Space split={<Divider type="vertical" spaceSize="mini" />}>
                      {value.map(item => (
                        <UserLink key={item.id} userId={item.id} userName={item.name} external />
                      ))}
                    </Space>
                  }
                >
                  <Typography.Text style={{ color: `var(--${prefixCls}-primary-color)` }} ellipsis>
                    {value.map((item, index) => (
                      <>
                        <UserLink key={item.id} userId={item.id} userName={item.name} external />
                        {index !== value.length - 1 && <Divider type="vertical" />}
                      </>
                    ))}
                  </Typography.Text>
                </Popover>
              ) : (
                '--'
              )}
            </>
          ),
        };
      case 'OUTSIDE_MAINTENANCE_STAFF':
        componentValueRef.current.outsideUser = value?.map(user => ({
          ...user,
          value: user.name,
          label: user.name,
        }));
        return {
          render: (
            <Input
              style={{ width: 156 }}
              maxLength={12}
              size="small"
              allowClear
              defaultValue={componentValueRef.current.outsideUser?.map(people => people.name)}
              onChange={e => {
                if (e.target.value) {
                  componentValueRef.current.outsideUser = [
                    { name: e.target.value, type: 'OUTSIDE' },
                  ];
                } else {
                  componentValueRef.current.outsideUser = undefined;
                }
              }}
            />
          ),
          display: (
            <>
              {value?.length > 0 ? (
                <Typography.Text ellipsis={{ tooltip: true }}>
                  {componentValueRef.current.outsideUser[0].name}
                </Typography.Text>
              ) : (
                '--'
              )}
            </>
          ),
        };
      case 'REPAIR_PROCESS':
        componentValueRef.current.process = value;

        return {
          render: (
            <Input
              defaultValue={value}
              maxLength={512}
              size="small"
              allowClear
              onChange={e => {
                componentValueRef.current.process = e.target.value;
              }}
            />
          ),
          display: <Typography.Text ellipsis={{ tooltip: true }}>{value || '--'}</Typography.Text>,
        };
      default:
        return null;
    }
  };

  return (
    <>
      {isEditing ? (
        <Space>
          {renderContent().render}
          <CheckOutlined
            style={{ color: `var(--${prefixCls}-primary-color)` }}
            onClick={async () => {
              let repairPersonList = Array.isArray(repairRecord?.repairPersonList)
                ? [...repairRecord.repairPersonList].filter(item => {
                    if (feedbackType === 'SYSTEM_MAINTENANCE_STAFF') {
                      return item.type !== 'SYSTEM';
                    }
                    if (feedbackType === 'OUTSIDE_MAINTENANCE_STAFF') {
                      return item.type !== 'OUTSIDE';
                    }
                    return true;
                  })
                : [];
              if (Array.isArray(componentValueRef.current?.systemUser)) {
                if (componentValueRef.current.systemUser.length > 20) {
                  message.error('最多选择20位自维修人员');
                  return;
                }
                repairPersonList = [...repairPersonList, ...componentValueRef.current.systemUser];
              }
              if (Array.isArray(componentValueRef.current?.outsideUser)) {
                if (componentValueRef.current.outsideUser.length > 20) {
                  message.error('最多选择20位外部厂商维修人员');
                  return;
                }
                repairPersonList = [...repairPersonList, ...componentValueRef.current.outsideUser];
              }

              const { error } = await updateRepairRecords({
                taskNo,
                idcTag,
                reason: componentValueRef.current?.reason ?? repairRecord?.repairReason,
                process: componentValueRef.current?.process ?? repairRecord?.repairProcess,
                repairPersonList: repairPersonList,
              });
              if (error) {
                message.error(error.message);
                return;
              }
              setIsCompleteTimeEditing(false);
              setIsOtherRecordEditable(false);
              onSuccess();
            }}
          />
          <CloseOutlined
            style={{ color: `var(--${prefixCls}-primary-color)` }}
            onClick={() => {
              setIsCompleteTimeEditing(false);
              setIsOtherRecordEditable(false);
            }}
          />
        </Space>
      ) : (
        <div
          style={{
            display: 'flex',
            width: '100%',
            alignItems: 'center',
          }}
        >
          {renderContent().display}
          {userEditable && (
            <EditOutlined
              style={{ color: `var(--${prefixCls}-primary-color)`, margin: '0 8px 0 4px' }}
              onClick={() => {
                if (isOtherRecordEditable) {
                  message.warning('其他维修字段正在编辑');
                  return;
                }
                setIsCompleteTimeEditing(true);
                setIsOtherRecordEditable(true);
              }}
            />
          )}
        </div>
      )}
    </>
  );
}
