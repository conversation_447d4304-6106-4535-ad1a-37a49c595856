import { useCallback, useState } from 'react';

import { message } from '@manyun/base-ui.ui.message';

import { fetchWorkdays } from '@manyun/hrm.service.fetch-workdays';
import type { ApiArgs } from '@manyun/hrm.service.fetch-workdays';

export const useWorkDays = () => {
  const [days, setDays] = useState<number | null>(null);
  const onLoadWorkDay = useCallback(async (params: ApiArgs) => {
    const { error, data } = await fetchWorkdays(params);
    if (error) {
      message.error(error.message);
      return;
    }
    setDays(data);
  }, []);

  return [days, onLoadWorkDay] as const;
};
