import React from 'react';

import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';

import { BackendTaskStatus } from '@manyun/ticket.model.ticket';
import { fetchRepairRecord } from '@manyun/ticket.service.fetch-repair-record';
import { TicketStatusSelect } from '@manyun/ticket.ui.ticket-status-select';

import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';

import TTR from './../../registries/ticket-type-registry';
import RepairTicketContent from './components/repair-ticket-content';
import RepairTicketNew from './components/repair-ticket-new';
import TargetLink from './components/target-link';

TTR.registerTicketType('repair')
  .registerConfig({
    type: 'tickets',
    showRevokeOperation: true,
    showNewBtn: 'element_ticket_repair_create',
    showExportBtn: true,

    /**
     * @param {object} param1
     * @param {typeof import('./../../views/tickets/constants').DEFAULT_FILTER_KEY_MAP} param1.DEFAULT_FILTER_KEY_MAP
     * @param {any[]} param1.defaultColumns
     */
    mergeFilters: ({ DEFAULT_FILTER_KEY_MAP, defaultFilters }) => {
      const newFilters = [...defaultFilters].filter(
        ({ key }) => key !== DEFAULT_FILTER_KEY_MAP.END_TIME
      );

      const insertIdx = newFilters.findIndex(
        ({ key }) => key === DEFAULT_FILTER_KEY_MAP.TICKET_STATE
      );
      newFilters.splice(insertIdx, 0, {
        key: 'subjectTag',
        label: '目标名称',
        Comp: Input,
        whitespace: 'trim',
        initialProps: { allowClear: true },
      });

      newFilters.splice(
        newFilters.findIndex(item => item.key === 'taskStatusList'),
        1,
        {
          label: '工单状态',
          key: 'taskStatusList',
          initialProps: {
            optionFilter: option =>
              ![BackendTaskStatus.INIT, BackendTaskStatus.UNDO].includes(option.value),
            allowClear: true,
            mode: 'multiple',
          },
          Comp: TicketStatusSelect,
        }
      );
      return newFilters;
    },

    /**
     * @param {object} param1
     * @param {typeof import('./../../views/tickets/constants').DEFAULT_COLUMN_DATA_INDEX_MAP} param1.DEFAULT_COLUMN_DATA_INDEX_MAP
     * @param {any[]} param1.defaultColumns
     */
    mergeColumns: ({ DEFAULT_COLUMN_DATA_INDEX_MAP, defaultColumns }) => {
      const newColumns = [...defaultColumns];

      const insertIndex = newColumns.findIndex(
        ({ dataIndex }) => dataIndex === DEFAULT_COLUMN_DATA_INDEX_MAP.CREATOR_NAME
      );
      newColumns.splice(insertIndex, 0, {
        title: '目标名称',
        key: 'targetNameLink',
        render(_ignored, { taskProperties }) {
          /**
           * @type {import('@/biz-types/ticket').RepairTicketProperties|undefined}
           */
          let properties;

          try {
            properties = JSON.parse(taskProperties);
          } catch (error) {
            console.error(error);
          }

          if (!properties) {
            return BLANK_PLACEHOLDER;
          }

          return (
            <TargetLink
              targetId={properties.targetId}
              targetName={properties.targetName}
              targetType={properties.targetType}
            />
          );
        },
      });

      return newColumns;
    },
  })
  .registerConfig({
    type: 'specific-ticket',
    content: RepairTicketContent,
    showRevocationBtn: true,
    checkTicketEndable: checkRepairTicketEndable,
  })
  .registerConfig({
    type: 'new-ticket',
    content: RepairTicketNew,
  });

async function checkRepairTicketEndable(taskNo, idcTag, taskType) {
  if (taskNo && idcTag && taskType === 'REPAIR') {
    const { data, error } = await fetchRepairRecord({ taskNo, idcTag });
    if (error) {
      message.error(error.message);
      return false;
    }
    if (data?.repairReason) {
      return true;
    }
  }
  return false;
}
