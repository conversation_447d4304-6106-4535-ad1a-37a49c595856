/**
 * @typedef {import('@/biz-types/ticket').RepairTicketProperties} RepairTicketProperties
 */
import React from 'react';
import { Link } from 'react-router-dom';

import { Typography } from '@manyun/base-ui.ui.typography';
import { generateRoomMonitoringUrl } from '@manyun/monitoring.route.monitoring-routes';
import { generateDeviceRecordRoutePath } from '@manyun/resource-hub.route.resource-routes';

import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';

/**
 * @param {Pick<RepairTicketProperties, 'targetId'|'targetName'|'targetType'>} props
 */
export default function TargetLink({
  targetId,
  targetName = BLANK_PLACEHOLDER,
  targetType,
  spaceGuid,
}) {
  let to;

  if (targetType?.code === 'ROOM') {
    const [idc, block, room] = targetId.split('.');
    to = generateRoomMonitoringUrl({ idc, block, room });
  }

  if (targetType?.code === 'DEVICE') {
    to = generateDeviceRecordRoutePath({
      guid: targetId,
    });
  }

  const getRoomMonitoringUrlParams = () => {
    if (spaceGuid) {
      const [idc, block, room] = spaceGuid.split('.');
      return {
        idc,
        block,
        room,
      };
    }
  };

  if (!to) {
    return targetName;
  }

  // PRD 上说要新建页面进行跳转
  return (
    <span>
      <a rel="noopener noreferrer" target="_blank" href={to}>
        {targetName}
      </a>
      {targetType?.code === 'DEVICE' && spaceGuid && (
        <Typography.Text>
          (包间:
          <Link
            target="_blank"
            onClick={() => {
              window.open(generateRoomMonitoringUrl(getRoomMonitoringUrlParams()));
            }}
          >
            {spaceGuid?.substring(spaceGuid.lastIndexOf('.') + 1)}
          </Link>
          )
        </Typography.Text>
      )}
    </span>
  );
}
