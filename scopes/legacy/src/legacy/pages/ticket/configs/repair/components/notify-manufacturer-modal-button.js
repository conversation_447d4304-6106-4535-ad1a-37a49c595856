import React from 'react';

import Form from '@ant-design/compatible/es/form';
import { Select } from '@galiojs/awesome-antd';

import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';

import { ModalButton, VendorContactsSelect } from '@manyun/dc-brain.legacy.components';
import { taskCenterService } from '@manyun/dc-brain.legacy.services';

/**
 * 维修单子任务-通知厂商
 * @param {object} props
 * @param {import('antd-3/lib/form/Form').WrappedFormUtils} props.form
 */
export function NotifyManufacturerModalButton({
  form,
  taskNo,
  text,
  warrantyVendor,
  getBasicInfo,
  onSuccess,
}) {
  const { getFieldDecorator, validateFields, getFieldValue } = form;
  return (
    <ModalButton
      type="primary"
      text={text}
      title={text}
      okText="提交"
      onOk={() =>
        new Promise(resolve => {
          validateFields(async (errors, valueMap) => {
            if (errors) {
              return;
            }
            const { error } = await taskCenterService.addRepairTicketNotificationRecord(
              getQ(taskNo, { ...valueMap })
            );
            if (error) {
              message.error(error);
              resolve(false);
              return;
            }
            getBasicInfo();
            onSuccess();
            resolve(true);
          });
        })
      }
    >
      {visible => {
        if (!visible) {
          return null;
        }
        const notificationMethod = getFieldValue('notificationMethod');
        const notifiedParty = getFieldValue('notifiedParty');
        return (
          <Form colon={false} labelCol={{ xs: 4 }} wrapperCol={{ xs: 20 }}>
            <Form.Item label="通知对象">
              {getFieldDecorator('notifiedParty', {
                rules: [{ required: true, message: '通知对象必填！' }],
                // initialValue: notifiedParty,
              })(
                <VendorContactsSelect vendorCode={warrantyVendor} />
                // <Input disabled={!!notifiedParty}  />
              )}
            </Form.Item>
            <Form.Item label="通知方式">
              {getFieldDecorator('notificationMethod', {
                initialValue: 'OFFLINE',
                rules: [{ required: true, message: '通知方式必选！' }],
              })(
                <Select disabled>
                  <Select.Option value={NOTIFICATION_METHOD_KEY_MAP.EMAIL}>邮件</Select.Option>
                  <Select.Option value={NOTIFICATION_METHOD_KEY_MAP.OFFLINE}>线下</Select.Option>
                </Select>
              )}
            </Form.Item>
            {notificationMethod === NOTIFICATION_METHOD_KEY_MAP.EMAIL && (
              <Form.Item label="邮件地址">{notifiedParty?.email}</Form.Item>
            )}
            <Form.Item label="通知内容">
              {getFieldDecorator('notificationContent', {
                rules: [
                  { required: true, message: '通知内容必填！' },
                  {
                    max: 20,
                    message: '最多输入 20 个字符！',
                  },
                ],
              })(<Input.TextArea />)}
            </Form.Item>
          </Form>
        );
      }}
    </ModalButton>
  );
}

export default Form.create()(NotifyManufacturerModalButton);

const NOTIFICATION_METHOD_KEY_MAP = {
  EMAIL: 'EMAIL',
  OFFLINE: 'OFFLINE',
};

function getQ(taskNo, { notifiedParty, notificationMethod, notificationContent }) {
  return {
    taskNo,
    informBy: -1, // 和 @宋阳 说了，先这样了
    informByName: notifiedParty?.name,
    informContent: notificationContent,
    informMethod: notificationMethod,
    email: notificationMethod === NOTIFICATION_METHOD_KEY_MAP.EMAIL ? notifiedParty?.email : null,
  };
}
