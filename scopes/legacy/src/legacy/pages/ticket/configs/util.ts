export function formatTime(inspectTime: number) {
  const timeInSeconds = inspectTime / 1000;

  if (timeInSeconds >= 0 && timeInSeconds < 100) {
    return `${timeInSeconds.toFixed(0)}秒`;
  } else if (timeInSeconds >= 100 && timeInSeconds < 100 * 60) {
    const timeInMinutes = timeInSeconds / 60;
    return `${timeInMinutes.toFixed(2)}分钟`;
  } else if (timeInSeconds >= 100 * 60 && timeInSeconds < 100 * 60 * 60) {
    const timeInHours = timeInSeconds / 3600;
    return `${timeInHours.toFixed(2)}小时`;
  } else if (timeInSeconds >= 100 * 60 * 60) {
    const timeInDays = timeInSeconds / (3600 * 24);
    return `${timeInDays.toFixed(2)}天`;
  }

  // Fallback: 无效时间
  return '';
}
