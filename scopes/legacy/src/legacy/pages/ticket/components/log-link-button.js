import React, { useMemo } from 'react';
import { Link } from 'react-router-dom';

import { generateAuditLogListRoutePath } from '@manyun/auth-hub.route.auth-routes';

export const LogLinkButton = ({ ticketType, ...props }) => {
  const paramLink = useMemo(() => {
    const getLogType = () => {
      switch (ticketType) {
        case 'inspection':
          return 'INSPECT_OFFLINE_DATA';
        default:
          return 'APPROVE_OFFLINE_DATA';
      }
    };
    return generateAuditLogListRoutePath({ logType: getLogType() });
  }, [ticketType]);
  return <Link to={paramLink}>导出审计日志</Link>;
};
