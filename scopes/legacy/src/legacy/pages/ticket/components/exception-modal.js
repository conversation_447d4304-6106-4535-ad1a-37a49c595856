import { UploadOutlined } from '@ant-design/icons';
import { Select } from '@galiojs/awesome-antd';
import React, { useCallback, useEffect, useState } from 'react';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { McUpload } from '@manyun/dc-brain.ui.upload';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { DeviceOrSpaceSelect } from '@manyun/resource-hub.ui.device-or-space-select';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';
import { useJudgeEventBetaVersion } from '@manyun/ticket.gql.client.tickets';
import {
  BackendEventStatusCode,
  EventSpecificProcessStatus,
  FAULT_TARGET_TYPE_TEXT,
} from '@manyun/ticket.model.event';
import { EventCabinetInfluenceEditDrawerButton } from '@manyun/ticket.ui.event-cabinet-influence-edit-drawer-button';
import { EventIdSelect } from '@manyun/ticket.ui.event-id-select';
import { EventLevelSelect } from '@manyun/ticket.ui.event-level-select';
import { EventTypeCascader } from '@manyun/ticket.ui.event-type-cascader';
import { FaultTargetTypeSelect } from '@manyun/ticket.ui.fault-target-type-select';
import { filterEventInfluencesTreeData } from '@manyun/ticket.util.filter-event-influences-tree-data';

import { CHECK_SUBJECT_KEY_MAPS, CHECK_SUBJECT_NAME } from '../constants';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
    xl: { span: 5 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
    xl: { span: 19 },
  },
};

function ExceptionModalFunctionComponent(props) {
  const {
    visible,
    form,
    handling,
    successCallbackErrorMsg,
    checkType,
    exceptionInfo,
    basicInfo,
    allowPatrolsCreateRepairTicket,
    allowMaintenancesCreateRepairTicket,
    featuresTitleRequired,
    // featuresNorthboundRequired,
    showRacksImpacts,
    isTypeRequired,
    featuresIsOwnerLabelChangeRequired,
    isEventBetaVersion,
    featureSpecialInfoSort,
  } = props;

  const [loading, setLoading] = useState(false);
  const [cabinetDrawerOpen, setCabinetDrawerOpen] = useState(false);

  const getQ = values => {
    const {
      taskType,
      _handleTypehandleType,
      eventLevel,
      eventType,
      targetName,
      desc,
      title,
      fileInfoList,
      followMethod,
      eventId,
      eventOwner,
      eventInfluence,
      eventCategory,
      ...rest
    } = values;
    let p = {
      fileInfoList: fileInfoList?.map(file => McUploadFile.fromJSON(file).toApiObject()),
      desc,
      taskTypeName: taskType.label,
      taskType: taskType.key,
    };
    if (taskType.key === CHECK_SUBJECT_KEY_MAPS.REPAIR) {
      p = {
        ...p,
        handleType,
        title,
        handleName: FAULT_TARGET_TYPE_TEXT[handleType],
      };
      if (props.taskType === 'MAINTENANCE') {
        p = {
          ...p,
          deviceGuid: props.exceptionInfo?.deviceGuid,
        };
      }
      if (props.taskType === 'INSPECTION') {
        p = {
          ...p,
          checkSubjectType: handleType,
          checkSubjectGuid: props.exceptionInfo?.checkSubjectGuid,
        };
      }
    } else {
      let _targetName = '';
      // 目标类型为 包间 时可多选 需逗号分隔
      const _handleType = featureSpecialInfoSort ? checkType : handleType;
      const currentTargetName = featureSpecialInfoSort ? getCheckSubjectGuid() : targetName;
      if (_handleType === 'ROOM' && Array.isArray(currentTargetName)) {
        _targetName = currentTargetName.join(',');
      }
      if (_handleType === 'ROOM' && !Array.isArray(currentTargetName)) {
        _targetName = currentTargetName.key ?? props.exceptionInfo.checkSubjectGuid;
      }
      // 目标类型为 设备 时因回填问题需要labelInValue 需取key值
      if (_handleType === 'DEVICE' && props.checkType === 'DEVICE') {
        _targetName = currentTargetName.key;
      }
      if (
        _handleType === 'DEVICE' &&
        props.checkType !== 'DEVICE' &&
        Array.isArray(currentTargetName)
      ) {
        _targetName = currentTargetName.map(item => item.key).join(',');
      }
      if (_handleType === 'OTHER') {
        _targetName = currentTargetName;
      }

      if (followMethod === 'available') {
        if (isEventBetaVersion) {
          // 内测版
          p = {
            ...p,
            eventId: eventId,
            itemName: props.exceptionInfo?.itemName,
            taskType:
              props.showNotProcessed && p.taskType === 'NOT_PROCESSED' ? p.taskType : 'NEW_EVENT',
          };
        } else {
          p = {
            ...p,
            eventLocationList: [
              {
                locationType: checkType,
                subType:
                  checkType === 'DEVICE'
                    ? props.exceptionInfo.deviceType
                    : props.exceptionInfo.roomType,
                guid: props.exceptionInfo.checkSubjectGuid ?? props.exceptionInfo.deviceGuid,
              },
            ],
            eventId: Number(eventId),
            itemName: props.exceptionInfo?.itemName,
          };
        }
      } else {
        if (isEventBetaVersion) {
          // 内测版
          p = {
            ...rest,
            ...p,
            eventLevel: eventLevel?.key,
            eventLevelName: eventLevel?.label,
            handleType: eventCategory?.value,
            handleName: eventCategory?.label,
            itemName: props.exceptionInfo?.itemName,
            taskType:
              props.showNotProcessed && p.taskType === 'NOT_PROCESSED' ? p.taskType : 'NEW_EVENT',
          };
        } else {
          p = {
            ...rest,
            ...p,
            eventLevel: eventLevel?.key,
            eventLevelName: eventLevel?.label,
            handleType: eventType ? eventType[0].value : undefined,
            handleName: eventType ? eventType[0].label : undefined,
            secondHandleType: eventType ? eventType[1].value : undefined,
            secondHandleName: eventType ? eventType[1].label : undefined,
            itemName: props.exceptionInfo?.itemName,
            // eventOwnerId: eventOwner ? eventOwner.value : undefined,
            // eventOwnerName: eventOwner ? eventOwner.label : undefined,
            eventOwnerIdList: eventOwner ? eventOwner.map(item => item.value) : undefined,
            eventLocationList: [
              {
                locationType: checkType,
                subType:
                  checkType === 'DEVICE'
                    ? props.exceptionInfo.deviceType
                    : props.exceptionInfo.roomType,
                guid: props.exceptionInfo.checkSubjectGuid ?? props.exceptionInfo.deviceGuid,
              },
            ],
            eventInfluence: eventInfluence
              ? {
                  gridInfluence: eventInfluence.cabinetInfluence,
                  influenceScope: Array.isArray(eventInfluence.influenceScope)
                    ? filterEventInfluencesTreeData(eventInfluence.influenceScope)?.map(item => ({
                        influenceType: item.type,
                        influenceGuid: item.key,
                      }))
                    : [],
                }
              : undefined,
          };
        }
      }

      if (props.taskType === 'MAINTENANCE') {
        p = {
          ...p,
          infoType: _handleType,
          deviceGuid: props.exceptionInfo?.deviceGuid,
          deviceTag: props.exceptionInfo?.checkSubjectTag,
          northSync: false,
        };
      }
      if (props.taskType === 'INSPECTION') {
        p = {
          ...p,
          checkSubjectType: _handleType,
          checkSubjectGuid: _targetName,
          checkSubjectTag: props.exceptionInfo?.checkSubjectTag,
          northSync: false,
        };
      }
    }
    return p;
  };

  const handleOk = () => {
    props.form.validateFields().then(async values => {
      if (taskType?.key === CHECK_SUBJECT_KEY_MAPS.NOT_PROCESSED) {
        if (values.fileInfoList.length > 9) {
          message.error('最多上传9张文件');
          return;
        }
        const invalidFile = values.fileInfoList.find(file => {
          const { type } = McUploadFile.fromJSON(file);
          return !type.startsWith('image/'); // 只允许图片类型
        });

        if (invalidFile) {
          message.error('只能上传图片文件！');
          return;
        }
      }
      setLoading(true);
      const params = getQ(values);
      const result = await props.onConfirm(params);
      setLoading(false);
      if (result) {
        props.onExceptionVisible('success');
        props.form.resetFields();
      }
    });
  };

  const getCheckSubjectGuid = useCallback(() => {
    const { checkSubjectTag, checkSubjectGuid, deviceTypeName, roomTag } = props.exceptionInfo;
    return { label: `${checkSubjectTag} ${deviceTypeName}（${roomTag}）`, key: checkSubjectGuid };
  }, [props.exceptionInfo]);
  const { setFieldsValue } = form;
  const taskType = Form.useWatch('taskType', form);
  const handleType = Form.useWatch('handleType', form);
  const followMethod = Form.useWatch('followMethod', form);
  const eventInfluence = Form.useWatch('eventInfluence', form);

  const followMethodIsNew = followMethod === 'new';
  let taskTypeOptions = handling;
  if (!allowPatrolsCreateRepairTicket && props.taskType === 'INSPECTION') {
    taskTypeOptions = taskTypeOptions.filter(item => item.value !== CHECK_SUBJECT_KEY_MAPS.REPAIR);
  }
  if (!allowMaintenancesCreateRepairTicket && props.taskType === 'MAINTENANCE') {
    taskTypeOptions = taskTypeOptions.filter(item => item.value !== CHECK_SUBJECT_KEY_MAPS.REPAIR);
  }
  if (!props.showNotProcessed) {
    taskTypeOptions = taskTypeOptions.filter(
      item => item.value !== CHECK_SUBJECT_KEY_MAPS.NOT_PROCESSED
    );
  }
  const isRequired = props.taskType === 'INSPECTION' ? !allowPatrolsCreateRepairTicket : true;

  useEffect(() => {
    if (featureSpecialInfoSort) {
      return;
    }
    if (visible && checkType === 'DEVICE') {
      form.setFieldsValue({
        targetName: getCheckSubjectGuid(),
        handleType: checkType,
      });
    }
  }, [form, getCheckSubjectGuid, visible, checkType, featureSpecialInfoSort]);

  useEffect(() => {
    if (featureSpecialInfoSort) {
      return;
    }
    if (
      (visible && checkType === 'DEVICE') ||
      (taskType && Object.keys(taskType)?.find(i => i === 'value') && taskType.value === 'EVENT')
    ) {
      form.setFieldsValue({
        targetName: getCheckSubjectGuid(),
      });
    }
  }, [form, getCheckSubjectGuid, visible, checkType, taskType, featureSpecialInfoSort]);

  return (
    <Drawer
      destroyOnClose
      open={visible}
      forceRender
      title="异常处理"
      size="large"
      placement="right"
      width="60%"
      okButtonProps={{ loading: loading }}
      extra={
        <Space>
          <Button
            onClick={() => {
              form.resetFields();

              props.onExceptionVisible('cancel');
            }}
          >
            取消
          </Button>
          <Button
            type="primary"
            loading={loading}
            onClick={() => {
              handleOk();
            }}
          >
            提交
          </Button>
        </Space>
      }
      onClose={() => {
        form.resetFields();
        props.onExceptionVisible('cancel');
      }}
    >
      {successCallbackErrorMsg && (
        <Alert
          message={successCallbackErrorMsg}
          type="warning"
          showIcon
          style={{ marginBottom: 24 }}
        />
      )}
      <Form.Provider
        onFormFinish={(name, { values, forms }) => {
          if (name === 'cabinetInfluenceForm') {
            const { basicForm } = forms;
            const { cabinetInfluence, influenceScope } = values;

            basicForm.setFieldsValue({
              eventInfluence: { cabinetInfluence, influenceScope },
            });

            setCabinetDrawerOpen(false);
          }
        }}
      >
        <Form name="basicForm" form={form} {...formItemLayout}>
          <Form.Item
            label="处理方式"
            name="taskType"
            rules={[{ required: true, message: '处理方式必选' }]}
            initialValue={{ key: CHECK_SUBJECT_KEY_MAPS.EVENT, label: '事件工单' }}
          >
            <Select
              style={{ width: 200 }}
              labelInValue
              disabled={taskTypeOptions.length === 1}
              options={taskTypeOptions}
              onChange={value => {
                if (value.key === CHECK_SUBJECT_KEY_MAPS.REPAIR) {
                  setFieldsValue({
                    handleType: checkType,
                    targetName: undefined,
                    eventLevel: undefined,
                    eventType: undefined,
                    desc: undefined,
                  });
                } else {
                  setFieldsValue({
                    handleType: checkType === 'DEVICE' ? checkType : undefined,
                    targetName: undefined,
                    title: undefined,
                    desc: undefined,
                  });
                }
              }}
            />
          </Form.Item>

          {(featureSpecialInfoSort
            ? taskType?.key === CHECK_SUBJECT_KEY_MAPS.REPAIR
            : taskType?.key !== CHECK_SUBJECT_KEY_MAPS.NOT_PROCESSED) && (
            <Form.Item
              label="目标类型"
              name="handleType"
              rules={[{ required: isRequired, message: `目标类型必选` }]}
            >
              <FaultTargetTypeSelect
                style={{ width: 216 }}
                disabled={taskType?.key === CHECK_SUBJECT_KEY_MAPS.REPAIR || checkType === 'DEVICE'}
                onChange={() => {
                  form.setFieldsValue({ targetName: undefined });
                }}
              />
            </Form.Item>
          )}

          {taskType?.key === CHECK_SUBJECT_KEY_MAPS.REPAIR && (
            <Form.Item label={CHECK_SUBJECT_NAME[taskType?.key]}>
              <span>{exceptionInfo?.checkSubjectTag}</span>
            </Form.Item>
          )}
          {taskType?.key === CHECK_SUBJECT_KEY_MAPS.EVENT && !featureSpecialInfoSort && (
            <Form.Item
              label="目标名称"
              name="targetName"
              rules={[
                {
                  required: isRequired,
                  validator(_, value) {
                    if (!value || (Array.isArray(value) && !value.length)) {
                      return Promise.reject(new Error('目标名称必填'));
                    }
                    if (handleType === 'OTHER' && value.length > 20) {
                      return Promise.reject(new Error('最多仅允许输入20个字！'));
                    }

                    return Promise.resolve();
                  },
                },
              ]}
            >
              <DeviceOrSpaceSelect
                style={{ width: 216 }}
                idcTag={basicInfo.idcTag}
                blockTag={getSpaceGuidMap(basicInfo.blockTag).block}
                disabled={checkType === 'DEVICE' || !handleType}
                type={handleType}
                multiple={checkType !== 'DEVICE'}
                labelInValue={handleType === 'DEVICE'}
              />
            </Form.Item>
          )}
          {taskType?.key === CHECK_SUBJECT_KEY_MAPS.EVENT &&
            featureSpecialInfoSort &&
            props.exceptionInfo.checkSubjectTag && (
              <Form.Item
                label="故障位置"
                name="eventLocationList"
                rules={[{ required: isRequired, message: `故障位置必选` }]}
                initialValue={`${props.exceptionInfo.checkSubjectTag}（${props.exceptionInfo.roomTag}）`}
              >
                <Select
                  style={{ width: 216 }}
                  disabled
                  value={`${props.exceptionInfo.checkSubjectTag}（${props.exceptionInfo.roomTag}）`}
                  options={[
                    {
                      label: `${props.exceptionInfo.checkSubjectTag}（${props.exceptionInfo.roomTag}）`,
                      key: 'default',
                    },
                  ]}
                />
              </Form.Item>
            )}
          {taskType?.key === CHECK_SUBJECT_KEY_MAPS.EVENT && (
            <Form.Item
              label="跟进方式"
              name="followMethod"
              rules={[{ required: isRequired, message: `跟进方式必选` }]}
              initialValue="new"
            >
              <Select
                style={{ width: 216 }}
                options={[
                  { label: '创建新事件', value: 'new', key: 'new' },
                  { label: '关联现有事件', value: 'available', key: 'available' },
                ]}
                onChange={() => {
                  form.setFieldsValue({
                    eventId: undefined,
                  });
                }}
              />
            </Form.Item>
          )}
          {taskType &&
            taskType.key === CHECK_SUBJECT_KEY_MAPS.EVENT &&
            followMethodIsNew &&
            !isEventBetaVersion &&
            featureSpecialInfoSort && (
              <Form.Item
                label="事件专业"
                name="eventMajorCode"
                rules={[{ required: true, message: '事件专业必选' }]}
              >
                <MetaTypeSelect style={{ width: 216 }} showSearch metaType="EVENT_MAJOR" />
              </Form.Item>
            )}

          {taskType &&
            taskType.key === CHECK_SUBJECT_KEY_MAPS.EVENT &&
            followMethodIsNew &&
            !isEventBetaVersion &&
            featureSpecialInfoSort && (
              <Form.Item
                label="事件类型"
                name="eventType"
                getValueFromEvent={(_, options) => {
                  return options;
                }}
                rules={isTypeRequired ? [{ required: true, message: '事件类型必选' }] : undefined}
              >
                <EventTypeCascader
                  style={{ width: 216 }}
                  displayRender={value => {
                    return value.map(item => item.label).join('/');
                  }}
                />
              </Form.Item>
            )}
          {taskType?.key === CHECK_SUBJECT_KEY_MAPS.EVENT &&
            followMethodIsNew &&
            !isEventBetaVersion && (
              <Form.Item
                label="事件级别"
                name="eventLevel"
                rules={[{ required: true, message: `事件级别必选` }]}
              >
                <EventLevelSelect
                  style={{ width: 216 }}
                  allowClear
                  trigger="onDidMount"
                  showSearch
                  optionFilterProp="title"
                  labelInValue
                />
              </Form.Item>
            )}
          {taskType?.key === CHECK_SUBJECT_KEY_MAPS.EVENT &&
            followMethodIsNew &&
            isEventBetaVersion && (
              <Form.Item
                label="事件等级"
                name="eventLevel"
                rules={[{ required: true, message: `事件等级必选` }]}
              >
                <MetaTypeSelect
                  style={{ width: 216 }}
                  allowClear
                  trigger="onDidMount"
                  showSearch
                  labelInValue
                  metaType={MetaType.N_EVENT_LEVEL}
                />
              </Form.Item>
            )}
          {taskType?.key === CHECK_SUBJECT_KEY_MAPS.EVENT &&
            followMethodIsNew &&
            isEventBetaVersion && (
              <Form.Item
                label="专业分类"
                name="eventCategory"
                rules={[{ required: true, message: `事件等级必选` }]}
              >
                <MetaTypeSelect
                  style={{ width: 216 }}
                  allowClear
                  trigger="onDidMount"
                  showSearch
                  labelInValue
                  metaType={MetaType.N_EVENT_CATEGORY}
                />
              </Form.Item>
            )}
          {taskType &&
            taskType.key === CHECK_SUBJECT_KEY_MAPS.EVENT &&
            followMethodIsNew &&
            !isEventBetaVersion &&
            !featureSpecialInfoSort && (
              <Form.Item
                label="事件类型"
                name="eventType"
                getValueFromEvent={(_, options) => {
                  return options;
                }}
                rules={isTypeRequired ? [{ required: true, message: '事件类型必选' }] : undefined}
              >
                <EventTypeCascader
                  style={{ width: 216 }}
                  displayRender={value => {
                    return value.map(item => item.label).join('/');
                  }}
                />
              </Form.Item>
            )}
          {taskType?.key === CHECK_SUBJECT_KEY_MAPS.REPAIR && (
            <Form.Item
              label="工单标题"
              name="title"
              rules={[
                {
                  required: true,
                  whitespace: true,
                  message: '工单标题必填！',
                },
                {
                  type: 'string',
                  max: 50,
                  message: '最多输入 50 个字符！',
                },
              ]}
            >
              <Input.TextArea style={{ width: 346 }} />
            </Form.Item>
          )}
          {taskType?.key === CHECK_SUBJECT_KEY_MAPS.EVENT &&
            followMethodIsNew &&
            (featuresTitleRequired || isEventBetaVersion) && (
              <Form.Item
                label="事件标题"
                name="eventTitle"
                rules={[
                  {
                    max: 100,
                    message: '最多输入 100 个字符！',
                  },
                  {
                    required: true,
                    message: '事件标题必填！',
                    whitespace: true,
                  },
                ]}
              >
                <Input style={{ width: '371px' }} allowClear />
              </Form.Item>
            )}

          {followMethod !== 'available' && (
            <Form.Item
              label={
                taskType?.key === CHECK_SUBJECT_KEY_MAPS.EVENT
                  ? '事件描述'
                  : taskType?.key === CHECK_SUBJECT_KEY_MAPS.NOT_PROCESSED
                    ? '备注'
                    : '问题描述'
              }
              name="desc"
              rules={[
                {
                  required: true,
                  message:
                    taskType && taskType.key === CHECK_SUBJECT_KEY_MAPS.EVENT
                      ? '事件描述必填'
                      : '问题描述必填',
                  whitespace: true,
                },
                {
                  type: 'string',
                  max:
                    (taskType && taskType.key === CHECK_SUBJECT_KEY_MAPS.EVENT) ||
                    (taskType && taskType.key === CHECK_SUBJECT_KEY_MAPS.NOT_PROCESSED)
                      ? 100
                      : 50,
                  message: `最多输入 ${
                    (taskType && taskType.key === CHECK_SUBJECT_KEY_MAPS.EVENT) ||
                    (taskType && taskType.key === CHECK_SUBJECT_KEY_MAPS.NOT_PROCESSED)
                      ? 100
                      : 50
                  } 个字符！`,
                },
              ]}
            >
              <Input.TextArea style={{ width: '371px' }} />
            </Form.Item>
          )}

          {taskType?.key === CHECK_SUBJECT_KEY_MAPS.EVENT &&
            followMethodIsNew &&
            showRacksImpacts && (
              <Form.Item
                name="eventInfluence"
                label="业务影响"
                rules={[{ required: true, message: '业务影响必选' }]}
              >
                <EventCabinetInfluenceEditDrawerButton
                  model="create"
                  spaceGuid={basicInfo.idcTag}
                  createSceneParam={{
                    openFromCreate: cabinetDrawerOpen,
                    textFlag:
                      (typeof eventInfluence?.cabinetInfluence === 'string' &&
                        eventInfluence?.cabinetInfluence?.trim() !== '') ||
                      (eventInfluence !== undefined &&
                        Array.isArray(eventInfluence?.influenceScope) &&
                        eventInfluence.influenceScope.length > 0),
                    setOpenFromCreate: setCabinetDrawerOpen,
                  }}
                  initialValue={eventInfluence}
                />
              </Form.Item>
            )}
          {taskType?.key === CHECK_SUBJECT_KEY_MAPS.EVENT &&
            followMethodIsNew &&
            featuresIsOwnerLabelChangeRequired && (
              <Form.Item
                label={featureSpecialInfoSort ? '责任人' : '负责人'}
                name="eventOwner"
                labelInValue={false}
                rules={[
                  {
                    required: true,
                    message: featureSpecialInfoSort ? '责任人必选！' : '负责人必选！',
                  },
                ]}
              >
                <UserSelect
                  mode="multiple"
                  style={{ width: 216 }}
                  userState="in-service"
                  placeholder={featureSpecialInfoSort ? '请选择责任人' : '请选择负责人'}
                />
              </Form.Item>
            )}
          {/* {taskType?.key === CHECK_SUBJECT_KEY_MAPS.EVENT &&
            followMethodIsNew &&
            featuresNorthboundRequired && (
              <Form.Item
                label="北向同步"
                name="northSync"
                rules={[
                  {
                    required: true,
                    message: '请选择北向同步',
                  },
                ]}
                initialValue={false}
              >
                <Radio.Group>
                  <Radio value>是</Radio>
                  <Radio value={false}>否</Radio>
                </Radio.Group>
              </Form.Item>
            )} */}
          {(featureSpecialInfoSort
            ? !(taskType?.key === CHECK_SUBJECT_KEY_MAPS.EVENT)
            : followMethod !== 'available') && (
            <Form.Item
              label="附件"
              name="fileInfoList"
              valuePropName="fileList"
              getValueFromEvent={value => {
                if (typeof value === 'object') {
                  return value.fileList;
                }
              }}
              rules={
                taskType?.key === CHECK_SUBJECT_KEY_MAPS.NOT_PROCESSED && [
                  {
                    required: true,
                    message: '请选择并上传',
                  },
                ]
              }
            >
              <McUpload
                key="upload"
                maxFileSize={20}
                maxCount={
                  taskType && taskType.key === CHECK_SUBJECT_KEY_MAPS.NOT_PROCESSED ? 9 : false
                }
                accept="image/*"
                onChange={v => {
                  if (
                    v.fileList?.filter(i => i.percent === 100).length >= 9 &&
                    v.file?.percent === 0
                  ) {
                    message.error('最多上传9张文件');
                    return;
                  }
                }}
              >
                <Space direction="vertical" size="middle">
                  <Button icon={<UploadOutlined />}>点此上传</Button>
                  {props.showNotProcessed &&
                    taskType &&
                    taskType.key === CHECK_SUBJECT_KEY_MAPS.NOT_PROCESSED && (
                      <Typography.Text type="secondary">
                        支持拓展名：image/* ; 图片上限9张
                      </Typography.Text>
                    )}
                </Space>
              </McUpload>
            </Form.Item>
          )}
          {taskType &&
            taskType.key === CHECK_SUBJECT_KEY_MAPS.EVENT &&
            followMethod === 'available' && (
              <Form.Item
                label="事件ID"
                name="eventId"
                rules={[
                  { required: isRequired || followMethod === 'available', message: '事件ID必选' },
                ]}
              >
                <EventIdSelect
                  style={{ width: '371px' }}
                  eventStatus={
                    showRacksImpacts
                      ? [
                          EventSpecificProcessStatus.Emergency,
                          EventSpecificProcessStatus.Fix,
                          EventSpecificProcessStatus.Recovery,
                          EventSpecificProcessStatus.Finished,
                        ]
                      : [
                          BackendEventStatusCode.Created,
                          BackendEventStatusCode.Relieved,
                          BackendEventStatusCode.Processing,
                        ]
                  }
                  infoType={props.checkType}
                  blockGuid={basicInfo.blockTag}
                />
              </Form.Item>
            )}
        </Form>
      </Form.Provider>
    </Drawer>
  );
}
export default function ExceptionModal(props) {
  const [configUtil] = useConfigUtil();

  const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket');
  const showNotProcessed = ticketScopeCommonConfigs.powerOnOff.features.showNotProcessed;
  const {
    events: { showRacksImpacts, isTypeRequired, features },
  } = ticketScopeCommonConfigs;
  const featuresTitle = features.title;
  const featuresNorthbound = features.northbound;
  const featuresTitleRequired = featuresTitle !== 'disabled';
  const featuresNorthboundRequired = featuresNorthbound !== 'disabled';
  const allowPatrolsCreateRepairTicket =
    ticketScopeCommonConfigs.patrols.features.allowCreateRepairTicket;
  const allowMaintenancesCreateRepairTicket =
    ticketScopeCommonConfigs.maintenances.features.allowCreateRepairTicket;
  const featuresIsOwnerLabelChange = features.isOwnerLabelChange;
  const featuresIsOwnerLabelChangeRequired = featuresIsOwnerLabelChange === 'required';
  const featureSpecialInfoSort = features.specialInfoSort;
  const [judgeEventBetaVersion, { data }] = useJudgeEventBetaVersion();

  useEffect(() => {
    if (props?.basicInfo?.idcTag) {
      judgeEventBetaVersion({ variables: { guid: props.basicInfo.idcTag } });
    }
  }, [props?.basicInfo?.idcTag, judgeEventBetaVersion]);
  const [form] = Form.useForm();

  return (
    <ExceptionModalFunctionComponent
      allowPatrolsCreateRepairTicket={allowPatrolsCreateRepairTicket}
      allowMaintenancesCreateRepairTicket={allowMaintenancesCreateRepairTicket}
      form={form}
      showRacksImpacts={showRacksImpacts}
      showNotProcessed={showNotProcessed}
      isTypeRequired={isTypeRequired}
      featuresTitleRequired={featuresTitleRequired}
      featuresNorthboundRequired={featuresNorthboundRequired}
      featuresIsOwnerLabelChangeRequired={featuresIsOwnerLabelChangeRequired}
      isEventBetaVersion={data?.judgeEventBetaVersion?.data}
      featureSpecialInfoSort={featureSpecialInfoSort}
      {...props}
    />
  );
}
