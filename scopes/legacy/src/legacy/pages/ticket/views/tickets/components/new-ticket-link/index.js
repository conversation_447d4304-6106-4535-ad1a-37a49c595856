import { ExclamationCircleFilled } from '@ant-design/icons';
import React from 'react';
import { Link } from 'react-router-dom';

import { getUserInfo } from '@manyun/auth-hub.cache.user';
import { Button } from '@manyun/base-ui.ui.button';
import { Popover } from '@manyun/base-ui.ui.popover';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { fetchPagedVisitors } from '@manyun/ticket.service.fetch-paged-visitors';

import { TICKET_TYPE_KEY_TEXT_MAP } from '@manyun/dc-brain.legacy.constants/ticket';
import { generateTicketCreateUrl, generateTicketEditUrl } from '@manyun/dc-brain.legacy.utils/urls';

export function NewTicketLink({ ticketType }) {
  const { userId } = getUserInfo();
  const [open, setOpen] = React.useState(false);
  const [drafts, setDrafts] = React.useState([]);

  React.useEffect(() => {
    if (ticketType === 'visitor') {
      (async () => {
        const { data, error } = await fetchPagedVisitors({
          taskType: 'VISITOR',
          taskStatusList: ['5'],
          onlyByTime: true,
          orderByTimeInfo: 'DESC',
          creatorId: userId,
        });
        if (error) {
          return;
        }
        if (Array.isArray(data.data)) {
          setDrafts(data.data);
        }
      })();
    }
  }, [ticketType]);

  return ticketType === 'visitor' ? (
    <>
      {drafts.length >= 1 ? (
        <Popover
          placement="topLeft"
          open={open}
          overlayStyle={{ width: '236px', height: '106px' }}
          content={
            <Space direction="vertical" size={4} style={{ alignItems: 'end' }}>
              <Space size={8}>
                <ExclamationCircleFilled style={{ color: 'rgba(250, 173, 20, 1)' }} />
                <Typography.Text>您有未提交的内容，是否继续编辑。</Typography.Text>
              </Space>
              <Space size={8}>
                <Link to={generateTicketCreateUrl({ ticketType })}>
                  <Button
                    onClick={() => {
                      setOpen(false);
                    }}
                  >
                    否
                  </Button>
                </Link>

                <Link
                  to={generateTicketEditUrl({ ticketType, id: drafts[0].taskNo || drafts[0].id })}
                >
                  <Button type="primary">是</Button>
                </Link>
              </Space>
            </Space>
          }
          trigger="click"
          onOpenChange={open => {
            setOpen(open);
          }}
        >
          <Button type="primary">{`${TICKET_TYPE_KEY_TEXT_MAP[ticketType]}`}</Button>
        </Popover>
      ) : (
        <Link to={generateTicketCreateUrl({ ticketType })}>
          <Button type="primary">{`${TICKET_TYPE_KEY_TEXT_MAP[ticketType]}`}</Button>
        </Link>
      )}
    </>
  ) : (
    <Link to={generateTicketCreateUrl({ ticketType })}>
      <Button type="primary">{`新建${TICKET_TYPE_KEY_TEXT_MAP[ticketType]}工单`}</Button>
    </Link>
  );
}

export default NewTicketLink;
