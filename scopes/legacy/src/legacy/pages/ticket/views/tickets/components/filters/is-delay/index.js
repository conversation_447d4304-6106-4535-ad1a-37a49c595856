import React from 'react';
import { connect } from 'react-redux';

import { Select } from '@galiojs/awesome-antd';

import { TICKET_IS_DELAY_KEY_TEXT_MAP } from '../../../../../constants';

export function IsDelay({ forwardedRef, getFieldDecorator, ...props }) {
  return (
    <Select ref={forwardedRef} allowClear {...props}>
      {Object.entries(TICKET_IS_DELAY_KEY_TEXT_MAP).map(item => (
        <Select.Option key={item[0]} value={item[0]}>
          {item[1]}
        </Select.Option>
      ))}
    </Select>
  );
}

export default connect(null, null, null, { forwardRef: true })(
  React.forwardRef((props, ref) => <IsDelay forwardedRef={ref} {...props} />)
);
