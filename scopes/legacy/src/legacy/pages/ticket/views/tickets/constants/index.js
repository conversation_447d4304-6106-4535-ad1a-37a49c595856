/**
 * 提供给 `mergeFilters` 方法，方便在注册工单列表页配置时根据 `key` 排序或插入、删除列
 */
export const DEFAULT_FILTER_KEY_MAP = {
  /**
   * 工单单号
   */
  TASK_NO: 'taskNo',

  /**
   * 工单子类型
   */
  TASK_SUB_TYPE: 'taskSubType',

  /**
   * 位置
   */
  LOCATION: 'location',

  /**
   * 创建时间
   */
  EFFECT_TIME: 'effectTime',

  /**
   * 工单标题
   */
  TICKET_TITLE: 'taskTitle',

  /**
   * 提单人
   */
  CREATOR_NAME: 'creatorName',

  /**
   * 是否超时
   */
  IS_DELAY: 'isDelay',

  /**
   * 处理人
   */
  TASK_ASSIGNEE: 'taskAssignee',

  /**
   * 工单状态
   */
  TICKET_STATE: 'taskStatusList',
  /**
   * 关单时间
   */

  END_TIME: 'endTime',
};

/**
 * 提供给 `mergeColumns` 方法，方便在注册工单列表页配置时根据 `dataIndex` 排序或插入、删除列
 */
export const DEFAULT_COLUMN_DATA_INDEX_MAP = {
  /**
   * 提单人
   */
  CREATOR_NAME: 'creatorName',
  /**
   * 机房|楼
   */
  BLOCK_TAG: 'blockTag',
  /**
   * 工单单号
   */
  TASK_NO: 'taskNo',

  /**
   * 工单类型
   */
  TASK_TYPE: 'taskType',

  /**
   * 工单子类型
   */
  TASK_SUB_TYPE: 'taskSubType',

  /**
   * 位置
   */
  LOCATION: 'blockTag',

  /**
   * 创建时间
   */
  EFFECT_TIME: 'effectTime',

  /**
   * 工单标题
   */
  TICKET_TITLE: 'taskTitle',

  /**
   * 处理人
   */
  TASK_ASSIGNEE_NAME: 'taskAssigneeName',

  /**
   * 工单状态
   */
  TICKET_STATE: 'taskStatus',

  /**
   * SLA
   */
  TASK_SLA: 'taskSla',
  /**
   * 响应时效
   */
  TASK_RESPOND_TIME: 'taskRespondTime',
};
