import React from 'react';

import { Button } from '@manyun/base-ui.ui.button';

import { BackendTaskStatus } from '@manyun/ticket.model.ticket';

/**
 *
 * @param {import('antd-3/lib/button').ButtonProps} props
 */
export function TakeOver({ selectedRows, takeOver }) {
  const processingList = selectedRows.filter(
    item => item.taskStatus === BackendTaskStatus.WAITTAKEOVER
  );

  return (
    <Button
      disabled={!processingList.length}
      type="warning"
      onClick={() => takeOver(processingList)}
    >
      批量接单
    </Button>
  );
}

export default TakeOver;
