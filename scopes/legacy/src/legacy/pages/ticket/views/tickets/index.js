import { DeleteOutlined, DownOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import pick from 'lodash/pick';
import React, { useEffect } from 'react';
import { useSelector } from 'react-redux';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import { useAuthorized } from '@manyun/auth-hub.hook.gql.use-authorized';
import { selectMe } from '@manyun/auth-hub.state.user';
import { User } from '@manyun/auth-hub.ui.user';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Dropdown } from '@manyun/base-ui.ui.dropdown';
import { Form } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { useAppIdentity } from '@manyun/iam.gql.client.iam';
import { BackendTaskStatus } from '@manyun/ticket.model.ticket';
import { generateTicketLocation } from '@manyun/ticket.route.ticket-routes';
import { BatchUrgeTicketsButton } from '@manyun/ticket.ui.batch-urge-tickets-button';
import { Resend } from '@manyun/ticket.ui.resend';
import { TicketStatusSelect } from '@manyun/ticket.ui.ticket-status-select';
import { TicketStatusText } from '@manyun/ticket.ui.ticket-status-text';
import { TicketSlaText } from '@manyun/ticket.ui.tickets-table';

import { LocationCascader, TinyCard, TinyTable } from '@manyun/dc-brain.legacy.components';
import { METADATA_TYPE } from '@manyun/dc-brain.legacy.constants';
import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  getTicketListAction,
  resetTicketSearchValuesActionCreator,
  revokeAction,
  setPaginationThenGetDataActionCreator,
  singleTakeOverActionCreator,
  takeOverActionCreator,
  ticketActions,
  ticketEndOfActionCreator,
  ticketFailureEndOfActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/ticketActions';
import { getTaskSubType } from '@manyun/dc-brain.legacy.utils/ticket';

import { CURRENT_PAGE_TYPES, NEED_TO_SHOW_TYPE_ROOM_FILTER } from '../../constants';
import TTR from '../../registries/ticket-type-registry';
import { Reapproval } from '../components/reapproval';
import { Revocation } from '../components/revocation';
import Transfer from '../components/transfer/index';
import EndOf from '../specific-ticket/components/footer-btns/detail-enf-of';
import {
  EndofTicketBtn,
  IsDelay,
  NewTicketLink,
  TakeOverBtn,
  TicketFileExport,
  TicketType,
} from './components/index';
import { DEFAULT_COLUMN_DATA_INDEX_MAP, DEFAULT_FILTER_KEY_MAP } from './constants';
import generateFilterForm from './utils/generateFilterForm';
import generateTableActions from './utils/generateTableActions';

const CURRENT_PAGE_TYPE = CURRENT_PAGE_TYPES.TICKETS;

const SingleUrgeButton = ({ record }) => {
  const [authorized] = useAuthorized({ checkByCode: 'element_risk-check-ticket-urge' });

  return authorized &&
    record.taskType === 'RISK_CHECK' &&
    [BackendTaskStatus.WAITTAKEOVER, BackendTaskStatus.PROCESSING].includes(record.taskStatus) ? (
    <BatchUrgeTicketsButton mode="single" type="link" compact selectedRows={[record]} />
  ) : null;
};

const CheckUserIdEndof = ({ record }) => {
  const [authorized] = useAuthorized({ checkByUserId: record.taskAssignee });

  return authorized ? (
    <EndOf
      key="close-ticket"
      compact
      basicInfo={record}
      type="link"
      style={{ padding: 0, height: 'auto' }}
      canClose={() => true}
      ticketPageType="tickets"
    />
  ) : null;
};

const CheckUserIdTransfer = ({ record }) => {
  const [authorized] = useAuthorized({ checkByUserId: record.taskAssignee });

  return authorized ? (
    <Transfer
      compact
      basicInfo={record}
      type="link"
      btnStyle={{ padding: 0, height: 'auto' }}
      ticketPageType="tickets"
    />
  ) : null;
};

const CheckUserIdTakeOver = ({ record, singleTakeOver, assignee }) => {
  const [, { checkUserId }] = useAuthorized();
  const { data: appIdentityData } = useAppIdentity({ fetchPolicy: 'no-cache' });
  let _authorized = true;
  if (record.assigneeList?.length) {
    _authorized = record.assigneeList.filter(item => checkUserId(item.id)).length;
  }

  if (record.assigneeRoleList?.length) {
    _authorized = record.assigneeRoleList.filter(
      item => appIdentityData?.appIdentity?.roleCode === item.roleCode
    ).length;
  }
  return _authorized ? (
    <Button key="takeover" compact type="link" onClick={() => singleTakeOver(record.taskNo)}>
      接单
    </Button>
  ) : null;
};

const DEFAULT_COLUMNS = (
  ctx,
  showColumnOperation,
  CustomOperation,
  showRevokeOperation,
  showResendOperation,
  showBatchUrgeBtn,
  revoke,
  sorterColumnKey,
  orderByTimeInfo
) => {
  const basicColumns = [
    {
      title: '工单单号',
      dataIndex: DEFAULT_COLUMN_DATA_INDEX_MAP.TASK_NO,
      fixed: true,
      render: (text, record) => (
        <Space>
          <Link
            to={generateTicketLocation({
              id: text,
              ticketType: record.taskType.toLowerCase(),
              idcTag: record.idcTag,
              blockTag: record.blockTag,
            })}
          >
            {text}
          </Link>
          {record.customerName && <Tag color="blue">{record.customerName}</Tag>}
        </Space>
      ),
    },
    {
      title: '工单类型',
      dataIndex: DEFAULT_COLUMN_DATA_INDEX_MAP.TASK_TYPE,
      render: text => {
        return ctx.props.ticketTypeList?.[text]?.taskValue;
      },
    },
    {
      title: '工单子类型',
      dataIndex: DEFAULT_COLUMN_DATA_INDEX_MAP.TASK_SUB_TYPE,
      render: (text, { taskType, taskProperties }) => {
        return taskType === 'IT_SERVICE'
          ? `${getTaskSubType(taskType, text, ctx.props.ticketTypeTreeList)}/${
              JSON.parse(taskProperties)?.thirdOrderTypeName
            }`
          : getTaskSubType(taskType, text, ctx.props.ticketTypeTreeList);
      },
    },
    {
      title: '工单标题',
      dataIndex: DEFAULT_COLUMN_DATA_INDEX_MAP.TICKET_TITLE,
    },
    {
      title: '位置',
      dataIndex: DEFAULT_COLUMN_DATA_INDEX_MAP.BLOCK_TAG,
      render: (text, { roomGuid, blockGuidList, taskType }) => {
        if (taskType === 'WAREHOUSE') {
          function formatSimpleBlockGuidList(blockGuidList) {
            if (!Array.isArray(blockGuidList)) {
              return text;
            }
            const commonPrefix = blockGuidList[0].split('.')[0];
            const joinedSuffixes = blockGuidList.map(item => item.split('.')[1]);
            return (
              <Typography.Text>
                {commonPrefix}.
                {joinedSuffixes.map((i, index) => (
                  <>
                    {i}
                    {joinedSuffixes.length - 1 > index && <Divider type="vertical" />}
                  </>
                ))}
              </Typography.Text>
            );
          }
          return ctx._getTicketType() === 'on_off'
            ? text
            : formatSimpleBlockGuidList(blockGuidList) || text;
        }

        return ctx._getTicketType() === 'on_off' ? text : roomGuid || text;
      },
    },
    {
      title: '提单人',
      dataIndex: DEFAULT_COLUMN_DATA_INDEX_MAP.CREATOR_NAME,

      render: (text, record) =>
        record.outerCreatorName ? (
          record.outerCreatorName
        ) : (
          <User.Link id={record.creatorId} name={text} resetSearchValues />
        ),
    },
    {
      title: '创建时间',
      dataIndex: DEFAULT_COLUMN_DATA_INDEX_MAP.EFFECT_TIME,
      dataType: 'datetime',
      sorter: true,
      key: DEFAULT_COLUMN_DATA_INDEX_MAP.EFFECT_TIME,
      sortOrder:
        orderByTimeInfo && sorterColumnKey === DEFAULT_COLUMN_DATA_INDEX_MAP.EFFECT_TIME
          ? orderByTimeInfo
          : null,
    },

    {
      title: '响应时效',
      dataIndex: DEFAULT_COLUMN_DATA_INDEX_MAP.TASK_RESPOND_TIME,
      render: (text, record) => (
        <TicketSlaText
          taskSla={record.responseSla}
          delay={record.currentResponse}
          unit="SECOND"
          effectTime={null}
          endTime={null}
          shouldLimitShow
        />
      ),
    },
    {
      title: 'SLA计时/标准',
      dataIndex: DEFAULT_COLUMN_DATA_INDEX_MAP.TASK_SLA,
      render: (text, record) => (
        <TicketSlaText
          taskSla={text}
          delay={record.delay}
          unit="SECOND"
          effectTime={record.effectTime}
          endTime={record.endTime}
          shouldLimitShow
        />
      ),
      hidden: ctx.props.match?.params?.type === 'warehouse',
    },
    {
      title: '工单状态',
      dataIndex: DEFAULT_COLUMN_DATA_INDEX_MAP.TICKET_STATE,
      render: text => <TicketStatusText status={text} />,
    },
    {
      title: '处理人',
      dataIndex: DEFAULT_COLUMN_DATA_INDEX_MAP.TASK_ASSIGNEE_NAME,
      render: (text, record) => <User.Link id={record.taskAssignee} name={text} />,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      fixed: 'right',
      render: (_, record) => {
        let btns = [];
        if (
          ((typeof showRevokeOperation === 'function' && showRevokeOperation(record)) ||
            showRevokeOperation) &&
          (record.taskStatus === BackendTaskStatus.INIT ||
            record.taskStatus === BackendTaskStatus.CLOSE_APPROVER)
        ) {
          btns.push(<Revocation key="check" type="link" compact record={record} revoke={revoke} />);
          return btns.length ? btns : BLANK_PLACEHOLDER;
        }
        if (
          ((typeof showResendOperation === 'function' && showResendOperation(record)) ||
            showResendOperation) &&
          record.taskStatus === BackendTaskStatus.UNDO
        ) {
          btns.push(<Reapproval key="checkId" compact type="link" record={record} />);
          return btns.length ? btns : BLANK_PLACEHOLDER;
        }
        if (showColumnOperation) {
          const endOfBtn = <CheckUserIdEndof key="endof" record={record} />;
          if (record.taskStatus === BackendTaskStatus.WAITTAKEOVER) {
            btns = [
              <CheckUserIdTakeOver
                key="takeover"
                record={record}
                singleTakeOver={ctx.singleTakeOver}
                assignee={record.assigneeList}
              />,
            ];
            if (record.assigneeList?.length) {
              btns.push([
                <Resend
                  key="resend"
                  compact
                  includeCurrentUser
                  id={record.assigneeList.map(item => item.id)}
                  style={{ padding: 0, height: 'auto' }}
                  btnType="link"
                  taskNo={record.taskNo}
                  taskType={record.taskType}
                  blockGuid={record.blockTag}
                  onResend={result => result && ctx.getList()}
                />,
              ]);
            }
          }
          if (record.taskStatus === BackendTaskStatus.PROCESSING) {
            btns = [<CheckUserIdTransfer key="transfer" record={record} />, endOfBtn];
          }
        }

        if (CustomOperation) {
          btns.push(<CustomOperation key="custom" record={record} getList={ctx.getList} />);
        }
        if (showBatchUrgeBtn) {
          btns.push(<SingleUrgeButton key="urge" record={record} />);
        }
        if (!btns.length) {
          return '--';
        }
        return <Space>{btns.filter(item => item).map(item => item)}</Space>;
      },
    },
  ];
  if (ctx.props.match.params.type.toUpperCase() === 'RISK_CHECK') {
    const insertSecIdx = basicColumns.findIndex(
      ({ dataIndex }) => dataIndex === DEFAULT_COLUMN_DATA_INDEX_MAP.EFFECT_TIME
    );
    basicColumns.splice(insertSecIdx, 0, {
      title: '关单时间',
      dataIndex: DEFAULT_COLUMN_DATA_INDEX_MAP.END_TIME,
      dataType: 'datetime',
      sorter: true,
      key: DEFAULT_COLUMN_DATA_INDEX_MAP.END_TIME,
      sortOrder:
        orderByTimeInfo && sorterColumnKey === DEFAULT_COLUMN_DATA_INDEX_MAP.END_TIME
          ? orderByTimeInfo
          : null,
      render: (_, { endTime }) => {
        return endTime ? dayjs(endTime).format('YYYY-MM-DD HH:mm:ss') : '--';
      },
    });
  }
  return basicColumns;
};
const DEFAULT_ACTIONS = [
  {
    key: 'urge',
    Comp: BatchUrgeTicketsButton,
    propsUtils: {
      pick: props => pick(props, ['selectedRows', 'urgeCallback']),
    },
  },
  {
    key: 'takeOver',
    Comp: TakeOverBtn,
    propsUtils: {
      pick: props => pick(props, ['selectedRows', 'takeOver']),
    },
  },
  {
    key: 'endOfTicket',
    Comp: EndofTicketBtn,
    propsUtils: {
      defaultProps: { type: 'danger' },
      pick: props => pick(props, ['ticketType', 'selectedRows', 'btnText', 'onSelectChange']),
    },
  },
];

const DEFAULT_RIGHT_ACTIONS = [
  {
    key: 'showExportBtn',
    Comp: TicketFileExport,
    propsUtils: {
      pick: props => pick(props, ['ticketType', 'searchValues', 'selectedRowKeys']),
    },
  },
];

const DEFAULT_FILTERS = [
  {
    key: DEFAULT_FILTER_KEY_MAP.TASK_NO,
    label: '工单单号',
    Comp: Input,
    initialProps: { allowClear: true },
    whitespace: 'trim',
  },
  {
    key: DEFAULT_FILTER_KEY_MAP.TASK_SUB_TYPE,
    label: '工单子类型',
    Comp: TicketType,
    propsUtils: {
      pick: props => pick(props, ['ticketType']),
    },
  },
  {
    key: DEFAULT_FILTER_KEY_MAP.LOCATION,
    label: '位置',
    Comp: LocationCascader,
    propsUtils: {
      pick: props => pick(props, ['showRoom']),
    },
    initialProps: { currentAuthorize: true },
  },
  {
    key: DEFAULT_FILTER_KEY_MAP.TICKET_STATE,
    label: '工单状态',
    initialProps: {
      optionFilter: option =>
        ![
          BackendTaskStatus.CLOSE_APPROVER,
          BackendTaskStatus.INIT,
          BackendTaskStatus.UNDO,
        ].includes(option.value),
      allowClear: true,
      mode: 'multiple',
    },
    Comp: TicketStatusSelect,
  },
  {
    key: DEFAULT_FILTER_KEY_MAP.CREATOR_NAME,
    label: '提单人',
    Comp: UserSelect,
    initialProps: { allowClear: true, showSystem: true },
  },
  {
    key: DEFAULT_FILTER_KEY_MAP.EFFECT_TIME,
    label: '创建时间',
    Comp: DatePicker.RangePicker,
    initialProps: {
      showTime: true,
      placeholder: ['开始时间', '结束时间'],
    },
    span: 2,
  },
  {
    key: DEFAULT_FILTER_KEY_MAP.END_TIME,
    label: '关单时间',
    Comp: DatePicker.RangePicker,
    initialProps: {
      placeholder: ['开始时间', '结束时间'],
    },
    span: 2,
  },
  {
    key: DEFAULT_FILTER_KEY_MAP.IS_DELAY,
    label: '是否超时',
    Comp: IsDelay,
  },
  {
    key: DEFAULT_FILTER_KEY_MAP.TASK_ASSIGNEE,
    label: '处理人',
    Comp: UserSelect,
    initialProps: { allowClear: true, showSystem: true },
  },
  {
    key: DEFAULT_FILTER_KEY_MAP.TICKET_TITLE,
    label: '工单标题',
    Comp: Input,
    initialProps: { allowClear: true },
  },
];
/**
 * @typedef { import('antd-3/lib/table').ColumnProps } ColumnProps
 *
 * @typedef {Array<{ key: string; label: string; Comp: React.ComponentType<any> }>} Components
 *
 * @typedef Config
 * @property {React.ComponentType<any>} filters
 * @property {(defaultFilters: Components[]) => Components[]} mergeFilters
 * @property {React.ComponentType<any> | React.ComponentType<any>[]} actions
 * @property {(defaultActions?: Components[]) => Components[]} mergeActions
 * @property {(defaultActions?: Components[]) => Components[]} mergeRightActions
 * @property {ColumnProps[]} columns
 * @property {(defaultColumns: ColumnProps[]) => ColumnProps[]} mergeColumns
 * @property {boolean | string} showNewBtn 是否展示新建按钮默认为false，如需校验元素权限则为string类型（元素的code）
 * @property {boolean} showBatchTakeOverBtn
 * @property {boolean} showBatchUrgeBtn 是否批量催办
 * @property {boolean} showBatchCloseTicketBtn
 * @property {boolean} showExportBtn //默认false  展示导出组件
 * @property {string} listServiceEndpoint
 * @property {boolean} showColumnOperation // 默认true
 * @property {boolean | (record: object) => boolean} showRevokeOperation // 默认false 展示撤回按钮 需在ticketSaga-> revoke 中添加撤回对应的Service的判断
 * @property {boolean | (record: object) => boolean} showResendOperation // 默认false 展示重新发送按钮
 *
 * @typedef State
 * @property {Config} config
 */

/**
 * @augments {React.PureComponent<any, State>}
 */
class TicketsComponent extends React.PureComponent {
  state = {
    selectedRowKeys: [],
    selectedRows: [],
    config: {
      // React Component
      filters: generateFilterForm({
        filters: DEFAULT_FILTERS,
        fieldsSerializationOptions: getFieldsSerializationOptions(DEFAULT_FILTERS),
      }),
      columns: DEFAULT_COLUMNS(
        this,
        true,
        null,
        false,
        false,
        this.revoke,
        DEFAULT_COLUMN_DATA_INDEX_MAP.EFFECT_TIME,
        undefined
      ),
      actions: generateTableActions({ actions: DEFAULT_ACTIONS }),
      rightActions: generateTableActions({ actions: DEFAULT_RIGHT_ACTIONS }),
      showColumnOperation: true,
      customOperation: null,
      showRowSelection: true,
      showRevokeOperation: false,
      showResendOperation: false,
      showBatchUrgeBtn: false,
      showExportBtn: false,
    },

    orderByTimeInfo: undefined,
    onlyByTime: false,
  };

  componentDidMount() {
    this.initData();
  }

  initData = () => {
    const ticketType = this.props.match.params.type.toUpperCase();
    this.props.setTicketType(ticketType);
    this._initialize();
    const paramsFromRoute = getLocationSearchMap(window.location.search);
    const { idc, block, statusList } = paramsFromRoute;
    const locationValue = [];
    if (idc) {
      locationValue.push(idc);
    }
    if (typeof block === 'string' && block.split('.').length) {
      locationValue.push(block.split('.')[1]);
    }
    const taskStatusListFromRoute =
      paramsFromRoute.taskStatusList && paramsFromRoute.taskStatusList.length > 0
        ? paramsFromRoute.taskStatusList
        : undefined;
    const taskAssigneeFromRoute = paramsFromRoute.taskAssignee
      ? JSON.parse(decodeURIComponent(paramsFromRoute.taskAssignee))
      : undefined;

    const getInitValue = () => {
      const obj = {};
      if (locationValue?.length >= 1) {
        obj['location'] = { value: locationValue };
      }
      if (Array.isArray(statusList)) {
        obj['taskStatusList'] = { value: statusList };
      }
      return obj;
    };
    this.props.updateSearchValues(
      ticketType === 'VISITOR'
        ? {
            location: { value: locationValue },
            idcTag: { value: idc },
            taskStatusList: {
              value: Array.isArray(statusList) ? statusList : undefined,
            },
          }
        : ticketType === 'POWER' || ticketType === 'REPAIR' || ticketType === 'MAINTENANCE'
          ? {
              location: { value: locationValue },
              taskStatusList: {
                value: taskStatusListFromRoute,
              },
              taskAssignee: {
                value: taskAssigneeFromRoute,
              },
            }
          : getInitValue()
    );
    this.props.syncCommonData({ strategy: { ticketTypes: 'FORCED' } });
  };

  componentDidUpdate(prevProps) {
    if (this.props.match.params.type !== prevProps.match.params.type) {
      window.scrollTo({ top: 0 });
      this.props.resetSearchValues(false);
      this.initData();
    }
  }

  getList = () => {
    this.props.getTicketListAction({
      shouldResetPageNum: true,
      taskType: this.props.match.params.type.toUpperCase(),
    });
  };

  _getTicketType = () => {
    return this.props.match.params.type;
  };

  resetColumns = (sorterColumnKey, orderByTimeInfo) => {
    const ticketType = this._getTicketType();
    const inst = TTR.getTicketTypeInstance(ticketType);
    if (!(inst && inst.hasConfig(CURRENT_PAGE_TYPE))) {
      return;
    }
    const config = inst.getConfig(CURRENT_PAGE_TYPE);
    const {
      mergeColumns,
      showColumnOperation = true,
      showRevokeOperation = false,
      showResendOperation = false,
      showBatchUrgeBtn = false,
      customOperation,
    } = config;
    if (typeof mergeColumns == 'function') {
      config.columns = mergeColumns({
        DEFAULT_COLUMN_DATA_INDEX_MAP,
        defaultColumns: DEFAULT_COLUMNS(
          this,
          showColumnOperation,
          customOperation,
          showRevokeOperation,
          showResendOperation,
          showBatchUrgeBtn,
          this.revoke,
          sorterColumnKey,
          orderByTimeInfo
        ),
      });
    } else {
      config.columns = DEFAULT_COLUMNS(
        this,
        showColumnOperation,
        customOperation,
        showRevokeOperation,
        showResendOperation,
        showBatchUrgeBtn,
        this.revoke,
        sorterColumnKey,
        orderByTimeInfo
      );
    }
    this.setState(({ config: prevConf }) => ({
      config: {
        ...prevConf,
        columns: config.columns,
      },
    }));
  };

  _initialize = () => {
    const ticketType = this._getTicketType();
    const inst = TTR.getTicketTypeInstance(ticketType);
    let actions = DEFAULT_ACTIONS;
    let rightActions = DEFAULT_RIGHT_ACTIONS;
    if (!(inst && inst.hasConfig(CURRENT_PAGE_TYPE))) {
      return;
    }

    const config = inst.getConfig(CURRENT_PAGE_TYPE);
    const {
      mergeFilters,
      mergeActions,
      mergeRightActions,
      mergeColumns,
      showNewBtn,
      showBatchTakeOverBtn = true,
      showBatchCloseTicketBtn = true,
      showColumnOperation = true,
      showExportBtn = false,
      showRevokeOperation = false,
      showResendOperation = false,
      customOperation,
      showRowSelection = true,
      showBatchUrgeBtn = false,
    } = config;

    if (typeof mergeFilters == 'function') {
      const mergedFilters = mergeFilters({
        DEFAULT_FILTER_KEY_MAP,
        defaultFilters: DEFAULT_FILTERS,
      });
      const fieldsSerializationOptions = getFieldsSerializationOptions(mergedFilters);
      config.filters = generateFilterForm({
        filters: mergedFilters,
        fieldsSerializationOptions,
      });
    } else {
      config.filters = generateFilterForm({
        filters: DEFAULT_FILTERS,
        fieldsSerializationOptions: getFieldsSerializationOptions(DEFAULT_FILTERS),
      });
    }

    if (typeof mergeActions == 'function') {
      const mergeActionsReturn = mergeActions({ defaultActions: DEFAULT_ACTIONS });
      config.actions = generateTableActions({
        actions: mergeActionsReturn,
      });
      actions = mergeActionsReturn;
    } else {
      config.actions = generateTableActions({ actions: DEFAULT_ACTIONS });
    }

    if (typeof mergeRightActions == 'function') {
      const mergeRightActionsReturn = mergeRightActions({ defaultActions: DEFAULT_RIGHT_ACTIONS });
      config.rightActions = generateTableActions({
        actions: mergeRightActionsReturn,
      });
      rightActions = mergeRightActionsReturn;
    } else {
      config.rightActions = generateTableActions({ actions: DEFAULT_RIGHT_ACTIONS });
    }

    if (typeof mergeColumns == 'function') {
      config.columns = mergeColumns({
        DEFAULT_COLUMN_DATA_INDEX_MAP,
        defaultColumns: DEFAULT_COLUMNS(
          this,
          showColumnOperation,
          customOperation,
          showRevokeOperation,
          showResendOperation,
          showBatchUrgeBtn,
          this.revoke,
          DEFAULT_COLUMN_DATA_INDEX_MAP.EFFECT_TIME,
          undefined
        ),
      });
    } else {
      config.columns = DEFAULT_COLUMNS(
        this,
        showColumnOperation,
        customOperation,
        showRevokeOperation,
        showResendOperation,
        showBatchUrgeBtn,
        this.revoke,
        DEFAULT_COLUMN_DATA_INDEX_MAP.EFFECT_TIME,
        undefined
      );
    }

    if (showNewBtn) {
      actions = [
        {
          key: 'newTicket',
          Comp: NewTicketLink,
          propsUtils: {
            pick: props => pick(props, ['ticketType']),
            showNewBtn,
          },
        },
        ...actions,
      ];
    }
    if (!showBatchCloseTicketBtn) {
      actions = actions.filter(item => item.key !== 'endOfTicket');
    }
    if (!showBatchTakeOverBtn) {
      actions = actions.filter(item => item.key !== 'takeOver');
    }

    if (!showBatchUrgeBtn) {
      actions = actions.filter(item => item.key !== 'urge');
    }

    if (!showExportBtn) {
      rightActions = rightActions.filter(item => item.key !== 'showExportBtn');
    }
    config.actions = generateTableActions({ actions });
    config.rightActions = generateTableActions({ actions: rightActions });
    if (config?.listServiceEndpoint) {
      this.props.setListServiceEndpoint(config?.listServiceEndpoint);
    } else {
      this.props.setListServiceEndpoint('fetchValidNoticeList');
    }
    this.setState(
      ({ config: prevConf }) => ({
        config: {
          ...prevConf,
          ...config,
          showRowSelection,
          showColumnOperation,
          showRevokeOperation,
          showResendOperation,
          showBatchUrgeBtn,
          showExportBtn,
        },
      }),
      () => this.getList()
    );
  };

  onChangePage = (pageNum, pageSize) => {
    this.props.setPagination({ pageNum, pageSize });
  };

  onSelectChange = (selectedRowKeys, selectedRows) => {
    this.setState({
      selectedRowKeys,
      selectedRows,
    });
  };

  takeOver = taskNos => {
    new Promise(() => {
      this.props.takeOver({
        params: {
          taskNos,
          ticketPageType: 'tickets',
        },
        successCallback: () => {
          this.setState({ selectedRowKeys: [], selectedRows: [] });
        },
        ticketPageType: 'tickets',
      });
    });
  };

  revoke = (taskNo, taskStatus) => {
    new Promise(() => {
      this.props.revokeAction({
        taskNo,
        type: this.props.match.params.type,
        ticketPageType: 'tickets',
        revokeType: taskStatus === BackendTaskStatus.CLOSE_APPROVER ? 'end' : 'create',
      });
    });
  };

  singleTakeOver = taskNo => {
    new Promise(() => {
      this.props.singleTakeOverActionCreator({
        taskNo,
        ticketPageType: 'tickets',
      });
    });
  };

  render() {
    const { config, selectedRowKeys, selectedRows } = this.state;
    const { ticketList, match, resetSearchValues, ticketTypeList, isRiskCheckTicketUrgable } =
      this.props;
    const isRiskCheckTicket = match.params.type.toUpperCase() === 'RISK_CHECK';
    // 当工单为风险检查工单时，则表格是否开启行选择取决于是否具备催办权限，其他类型工单还是依据config.showRowSelection
    const showRowSelection = isRiskCheckTicket ? isRiskCheckTicketUrgable : config.showRowSelection;
    return Object.keys(ticketTypeList || {}).length > 0 ? (
      <Space direction="vertical" style={{ width: '100%' }}>
        <TinyCard>
          <config.filters
            updateSearchValues={this.props.updateSearchValues}
            resetSearchValues={this.props.resetSearchValues}
            ticketType={match.params.type}
            showRoom={NEED_TO_SHOW_TYPE_ROOM_FILTER.includes(match.params.type)}
            onSearch={this.getList}
          />
        </TinyCard>
        <TinyCard>
          <TinyTable
            align="left"
            rowKey="taskNo"
            dataSource={ticketList.list}
            loading={ticketList.loading}
            columns={config.columns.filter(i => !i.hidden)}
            rowSelection={
              showRowSelection
                ? {
                    selectedRowKeys,
                    selectedRows,
                    onChange: this.onSelectChange,
                    getCheckboxProps: isRiskCheckTicket
                      ? record => ({
                          disabled: ![
                            BackendTaskStatus.WAITTAKEOVER,
                            BackendTaskStatus.PROCESSING,
                          ].includes(record.taskStatus),
                        })
                      : undefined,
                  }
                : undefined
            }
            scroll={{ x: 'max-content' }}
            actions={
              <div style={{ width: '100%', display: 'flex', justifyContent: 'space-between' }}>
                <config.actions
                  actions={DEFAULT_ACTIONS}
                  takeOver={() => this.takeOver(selectedRowKeys)}
                  selectedRows={selectedRows}
                  ticketType={match.params.type}
                  btnText="批量关单"
                  searchValues={ticketList.searchValues}
                  resetSearchValues={resetSearchValues}
                  initialize={this._initialize}
                  urgeCallback={() => {
                    this.setState({ selectedRowKeys: [], selectedRows: [] });
                  }}
                  onSelectChange={this.onSelectChange}
                />
                {config.rightActions && (
                  <config.rightActions
                    ticketType={match.params.type}
                    searchValues={ticketList.searchValues}
                    selectedRowKeys={selectedRowKeys}
                  />
                )}
                <ConfiguringText ctx={this} ticketType={match.params.type.toUpperCase()} />
              </div>
            }
            pagination={{
              total: ticketList.total,
              current: ticketList.pagination.pageNum,
              pageSize: ticketList.pagination.pageSize,
              showTotal: () => `共 ${ticketList.total} 条`,
              showSizeChanger: true,
              onChange: this.onChangePage,
            }}
            onChange={(pagination, _, sorter) => {
              const { field, order } = sorter;
              this.resetColumns(field, order);
              this.props.updateSearchValues({
                onlyByTime: {
                  value: Boolean(order) && field === DEFAULT_COLUMN_DATA_INDEX_MAP.EFFECT_TIME,
                },
                onlyByEndTime: {
                  value: Boolean(order) && field === DEFAULT_COLUMN_DATA_INDEX_MAP.END_TIME,
                },
                orderByTimeInfo: { value: getSorterValue(order) },
              });
              this.onChangePage(pagination.current, pagination.pageSize);
            }}
          />
        </TinyCard>
      </Space>
    ) : null;
  }
}
function ConfiguringText({ ctx, ticketType }) {
  const [configUtil] = useConfigUtil();
  const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket');
  const { dockingStation = false } = ticketScopeCommonConfigs?.entryOfPersons?.features ?? {
    dockingStation: false,
  };
  return (
    dockingStation &&
    ticketType === METADATA_TYPE.VISITOR && (
      <div>
        <Dropdown
          autoAdjustOverflow
          arrow
          destroyPopupOnHide
          menu={{
            items: [
              {
                key: '1',
                label: (
                  <Typography.Text>
                    <Link
                      //  target="_blank"
                      style={{ color: 'unset' }}
                      to={'/page/sentry/docking-configurations'}
                    >
                      对接人配置
                    </Link>
                  </Typography.Text>
                ),
              },
              {
                key: '2',
                label: (
                  <Typography.Text>
                    <Link
                      //  target="_blank"
                      style={{ color: 'unset' }}
                      to={'/page/sentry/business-mapping-configuration'}
                    >
                      业务映射配置
                    </Link>
                  </Typography.Text>
                ),
              },
            ],
          }}
        >
          <Space>
            <Link>
              配置 <DownOutlined />
            </Link>
          </Space>
        </Dropdown>
      </div>
    )
  );
}

const mapStateToProps = ({ ticket: { ticketList }, common: { ticketTypes } }) => ({
  ticketList,
  ticketTypeList: ticketTypes?.normalizedList || {},
  ticketTypeTreeList: ticketTypes?.treeList || [],
});

const mapDispatchToProps = {
  getTicketListAction: getTicketListAction,
  updateSearchValues: ticketActions.updateSearchValues,
  setPagination: setPaginationThenGetDataActionCreator,
  resetSearchValues: resetTicketSearchValuesActionCreator,
  takeOver: takeOverActionCreator,
  updateTicketList: ticketActions.success,
  syncCommonData: syncCommonDataActionCreator,
  ticketEndOfActionCreator: ticketEndOfActionCreator,
  ticketFailureEndOfActionCreator: ticketFailureEndOfActionCreator,
  setTicketType: ticketActions.setTicketType,
  setListServiceEndpoint: ticketActions.setListServiceEndpoint,
  singleTakeOverActionCreator,
  revokeAction,
};

const TicketsTable = connect(mapStateToProps, mapDispatchToProps)(TicketsComponent);
function getFieldsSerializationOptions(mergedFilters) {
  return mergedFilters.reduce((map, { key, serializationOptions }) => {
    if (serializationOptions) {
      map[key] = serializationOptions;
    }

    return map;
  }, {});
}

function getSorterValue(order) {
  if (typeof order === 'string') {
    return order === 'ascend' ? 'ASC' : 'DESC';
  }
  return undefined;
}

export default function Tickets(props) {
  const [authorized] = useAuthorized({ checkByCode: 'element_risk-check-ticket-urge' });

  return <TicketsTable {...props} isRiskCheckTicketUrgable={authorized} />;
}
