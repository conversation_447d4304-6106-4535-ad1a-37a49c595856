import React, { useEffect } from 'react';
import { connect } from 'react-redux';

import { Select } from '@galiojs/awesome-antd';

import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';

// import { TICKET_TYPE_WEB_JAVA_MAP } from '../../../../../constants';

export function TicketSubType({
  syncCommonData,
  forwardedRef,
  getFieldDecorator,
  ticketTypes,
  value,
  ticketType,
  ...props
}) {
  useEffect(() => {
    syncCommonData({ strategy: { ticketTypes: 'IF_NULL' } });
  }, [syncCommonData]);

  const ticketTypeList = ticketTypes.filter(item => item.taskType === ticketType.toUpperCase());
  let ticketSubTypeList = [];

  if (ticketTypeList.length === 1) {
    ticketSubTypeList = ticketTypeList[0].children;
  }

  return (
    <Select ref={forwardedRef} allowClear value={value} {...props}>
      {ticketSubTypeList.map(item => (
        <Select.Option key={item.taskType} value={item.taskType}>
          {item.taskValue}
        </Select.Option>
      ))}
    </Select>
  );
}

const mapStateToProps = ({ common: { ticketTypes } }) => ({
  ticketTypes: ticketTypes && ticketTypes.treeList ? ticketTypes.treeList : [],
});

const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps, null, { forwardRef: true })(
  React.forwardRef((props, ref) => <TicketSubType forwardedRef={ref} {...props} />)
);
