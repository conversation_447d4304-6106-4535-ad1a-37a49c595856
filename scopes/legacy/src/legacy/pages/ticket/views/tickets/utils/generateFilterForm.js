import { FiltersForm, Form } from '@galiojs/awesome-antd';
import moment from 'moment';
import React from 'react';
import { connect } from 'react-redux';

export default function generateFilterForm({ filters, fieldsSerializationOptions }) {
  function Filters(props) {
    const { resetSearchValues, onSearch, updateSearchValues, searchValues } = props;
    const [form] = Form.useForm();
    const [time, setTime] = React.useState([]);

    const items = filters.map(
      ({
        key,
        Comp,
        label,
        span = 1,
        propsUtils = { pick: () => {} },
        initialProps = {},
        ...formItemProps
      }) => {
        return {
          label,
          name: key,
          control: <Comp {...propsUtils.pick(props)} {...initialProps} />,
          span: span,
          ...formItemProps,
        };
      }
    );

    React.useEffect(() => {
      if (props.ticketType === 'inspection') {
        // 巡检工单
        const endTime = moment();
        const startTime = moment().subtract(6, 'months');
        startTime.startOf('day');
        endTime.endOf('day');
        const value = [startTime, endTime];
        setTime(value);
        form.setFieldValue('effectTime', value);
        updateSearchValues({ effectTime: { name: 'effectTime', value } });
        setTimeout(() => {
          onSearch();
        }, 0);
      }
    }, [props.ticketType]);

    return (
      <FiltersForm
        form={form}
        items={items}
        fields={Object.keys(searchValues).map(name => {
          let field = searchValues[name];
          if (fieldsSerializationOptions?.[name]) {
            field = {
              ...field,
              value: fieldsSerializationOptions[name].parseValue(field.value),
            };
          }

          return {
            ...field,
            name: field.name?.split('.') ?? [name],
          };
        })}
        onFieldsChange={changedFields => {
          if (changedFields.length <= 0) {
            return;
          }
          const [changedField] = changedFields;
          const name = changedField.name.join('.');
          const fields = {
            [name]: {
              ...changedField,
              name,
            },
          };
          const value = changedField.value;
          const changeValue = filters.filter(item => item.key === name);
          if (changeValue.length && ![null, undefined].includes(value)) {
            if (changeValue[0].whitespace === 'trim') {
              fields[name] = { ...fields[name], value: value.trim() };
            }
            if (changeValue[0].whitespace === 'trimLeft') {
              fields[name] = {
                ...fields[name],
                value: value.replace(/^[\s\n\t]+/g, ''),
              };
            }
            if (changeValue[0].whitespace === 'trimRight') {
              fields[name] = {
                ...fields[name],
                value: value.replace(/[\s\n\t]+$/g, ''),
              };
            }
          }
          if (fieldsSerializationOptions?.[name]) {
            fields[name] = {
              ...fields[name],
              value: fieldsSerializationOptions[name].serialize(fields[name].value),
            };
          }
          updateSearchValues(fields);
        }}
        onSearch={onSearch}
        onReset={() => {
          resetSearchValues(true);
          if (props.ticketType === 'inspection' && time.length >= 1) {
            form.setFieldValue('effectTime', time);
            updateSearchValues({ effectTime: { name: 'effectTime', value: time } });
            setTimeout(() => {
              onSearch();
            }, 0);
          }
        }}
        defaultExpanded={props.ticketType === 'inspection'}
      />
    );
  }

  const mapStateToProps = ({
    ticket: {
      ticketList: { searchValues },
    },
  }) => {
    return { searchValues };
  };

  const ConnectedFilterForm = connect(mapStateToProps, null, null, { forwardRef: true })(Filters);

  return ConnectedFilterForm;
}
