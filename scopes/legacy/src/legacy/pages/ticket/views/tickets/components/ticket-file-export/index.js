import React from 'react';

import dayjs from 'dayjs';

import { FileExport } from '@manyun/base-ui.ui.file-export';
import { message } from '@manyun/base-ui.ui.message';

import { TaskTypeMap } from '@manyun/ticket.model.ticket';
import { exportRiskCheckTicket } from '@manyun/ticket.service.export-risk-check-ticket';
import { exportTicket } from '@manyun/ticket.service.export-ticket';

import { getParams } from '@manyun/dc-brain.legacy.redux/sagas/ticketSaga';

function getExportFileName(type) {
  if (type === 'RISK_CHECK') {
    return `风险检查工单${dayjs().format('YYYY-MM-DD')}.xlsx`;
  } else {
    return `${TaskTypeMap[type.toUpperCase()]}工单记录_${dayjs().format('YYYY.MM.DD')}.xlsx`;
  }
}
export function TicketFileExport({ ticketType, searchValues, selectedRowKeys }) {
  const ticketTypeCapital = ticketType.toUpperCase();
  const fileName = getExportFileName(ticketTypeCapital);
  return (
    <FileExport
      showExportFiltered
      showExportSelected={selectedRowKeys.length}
      filename={fileName}
      data={async type => {
        let params = {
          taskType: ticketTypeCapital,
        };
        if (type === 'filtered') {
          params = { ...getParams(searchValues), taskType: ticketTypeCapital };
        }
        if (type === 'selected') {
          params.taskNoList = selectedRowKeys;
        }
        if (ticketTypeCapital === 'RISK_CHECK') {
          const { error, data } = await exportRiskCheckTicket(params);
          if (error) {
            message.error(error.message);
            return;
          }
          return data;
        }
        const { error, data } = await exportTicket(params);
        if (error) {
          message.error(error.message);
          return;
        }
        return data;
      }}
    />
  );
}

export default TicketFileExport;
