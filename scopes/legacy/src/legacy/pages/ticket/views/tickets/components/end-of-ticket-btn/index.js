import React, { useState } from 'react';
// import moment from 'moment';
// import { AweInput } from '@galiojs/awesome-antd';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import ExclamationCircleFilled from '@ant-design/icons/es/icons/ExclamationCircleFilled';
import debounce from 'lodash/debounce';

import { Button } from '@manyun/base-ui.ui.button';
import { Modal } from '@manyun/base-ui.ui.modal';

import { BackendTaskStatus } from '@manyun/ticket.model.ticket';
import { handleBatchEndFeedback } from '@manyun/ticket.util.handle-batch-end-feedback';

import {
  ticketEndOfActionCreator,
  ticketFailureEndOfActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/ticketActions';

// const formItemLayout = {
//   labelCol: {
//     xs: { span: 24 },
//     sm: { span: 6 },
//   },
//   wrapperCol: {
//     xs: { span: 24 },
//     sm: { span: 18 },
//   },
// };

/**
 *
 * @param {import('antd-3/lib/button').ButtonProps} props
 */
export function EndOfTicket({
  btnText,
  selectedRows,
  type,
  form,
  ticketEndOfActionCreator,
  ticketFailureEndOfActionCreator,
  ticketType,
  onSelectChange,
  ...props
}) {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  // const [timeout, setTimeout] = useState([]);

  // const { getFieldDecorator } = form;

  const getNormalselectedList = () => {
    return selectedRows.filter(item => item.taskStatus === BackendTaskStatus.PROCESSING);
  };

  const debouncedHandleSearchRole = debounce(value => {
    onOk();
  }, 500);

  // 失败关单暂时和正常关单判断一致，后续可能会改，原逻辑留着不动
  // const getFailureselectedList = () => {
  //   return selectedRows.filter(
  //     item =>
  //       ![BackendTaskStatus.FINISH, BackendTaskStatus.FAILURE].includes(item.taskStatus)
  //   );
  // };

  // const getTimeoutList = selectedRows => {
  //   const list = selectedRows.filter(item => {
  //     const gmtTime = moment(item.effectTime);
  //     const now = moment(new Date().getTime());
  //     const delay = now.diff(gmtTime, 'minutes');
  //     if (item.taskSla !== 0 && delay > item.taskSla) {
  //       return true;
  //     }
  //     return false;
  //   });
  //   return list;
  // };

  const onOk = () => {
    form.validateFields(async (err, values) => {
      if (err || loading) {
        return;
      }
      setLoading(true);

      // if (values.statementResult === 'failure') {
      //   new Promise(() => {
      //     ticketFailureEndOfActionCreator({
      //       params: {
      //         taskNos: rows.map(item => item.taskNo),
      //         ...values,
      //         taskType: ticketType.toUpperCase(),
      //       },
      //       successCallback: data => {
      //         setVisible(false);
      //         message.success(
      //           `失败关单成功！当前共有失败关单${data.totalNum}条，超时${data.delayNum}条。`
      //         );
      //       },
      //       ticketPageType: 'tickets',
      //     });
      //   });
      //   return;
      // }

      new Promise(() => {
        onSelectChange([], []);
        ticketEndOfActionCreator({
          params: {
            ticketNumbers: selectedRows.map(item => item.taskNo),
            ...values,
            taskType: ticketType.toUpperCase(),
          },
          successCallback: data => {
            setLoading(false);
            setVisible(false);
            const { endNum, approveNum } = data;
            const sumNum = selectedRows.length;

            handleBatchEndFeedback({ sumNum, approveNum, endNum });
          },
          ticketPageType: 'tickets',
        });
        setLoading(false);
      });
    });
  };

  const onClick = () => {
    Modal.confirm({
      icon: <ExclamationCircleFilled />,
      title: '确认要关单吗？',
      content: <div>当前选中{selectedRows.length}条, 确定批量关单？</div>,
      onOk() {
        debouncedHandleSearchRole();
        // onOk();
      },
      onCancel() {},
      okText: '确认',
      cancelText: '取消',
    });
  };

  return (
    <>
      <Button type={type} disabled={!getNormalselectedList().length} onClick={onClick} {...props}>
        {btnText}
      </Button>
      <Modal
        title="提示"
        open={visible}
        destroyOnClose
        onCancel={() => setVisible(false)}
        onOk={onOk}
        // okButtonProps={{ disabled: !getNormalselectedList().length }}
      >
        {/* <Form colon={false} {...formItemLayout}>
          <Form.Item label="选中："> */}
        <div>
          当前选中{selectedRows.length}条, 确定批量关单？
          {/* {timeout.length > 0 ? (
                <span>
                  ，其中超时
                  {
                    <StatusText style={{ display: 'inline-block' }} status={STATUS_MAP.ALARM}>
                      {timeout.length}
                    </StatusText>
                  }
                  条。
                </span>
              ) : (
                ''
              )} */}
        </div>
        {/* </Form.Item> */}
        {/* <Form.Item label="关单结果">
            {getFieldDecorator('statementResult', {
              rules: [
                {
                  required: true,
                },
              ],
              initialValue: selectedRows.length
                ? getNormalselectedList().length
                  ? 'normal'
                  : 'failure'
                : 'normal',
            })(
              <Radio.Group
                onChange={onChangeSatementResult}
                disabled={!getNormalselectedList().length}
              >
                <Radio value="normal">正常关单</Radio>
                <Radio value="failure">失败关单</Radio>
              </Radio.Group>
            )}
          </Form.Item>
          {getFieldValue('statementResult') === 'failure' ? (
            <Form.Item label="失败原因类型">
              {getFieldDecorator('failedReason', {
                rules: [
                  {
                    required: true,
                    message: '失败原因类型必选！',
                  },
                ],
              })(<FailureReasonType allowClear></FailureReasonType>)}
            </Form.Item>
          ) : null}
          {getFieldValue('statementResult') === 'failure' ? (
            <Form.Item label="失败原因">
              {getFieldDecorator('failedDesc', {
                rules: [
                  {
                    required: true,
                    message: '失败原因必填！',
                  },
                ],
              })(<AweInput  allowClear />)}
            </Form.Item>
          ) : null} */}
        {/* {timeout.length ? (
            <Form.Item label="超时原因类型">
              {getFieldDecorator('delayReason', {
                rules: [
                  {
                    required: true,
                    message: '超时原因类型必选！',
                  },
                ],
              })(<TimeoutReasonType allowClear />)}
            </Form.Item>
          ) : null}
          {timeout.length ? (
            <Form.Item label="超时原因">
              {getFieldDecorator('delayDesc', {
                rules: [
                  {
                    required: true,
                    message: '超时原因必填！',
                  },
                ],
              })(<AweInput  allowClear />)}
            </Form.Item>
          ) : null} */}
        {/* </Form> */}
      </Modal>
    </>
  );
}

const mapDispatchToProps = {
  ticketEndOfActionCreator: ticketEndOfActionCreator,
  ticketFailureEndOfActionCreator: ticketFailureEndOfActionCreator,
};

export default connect(null, mapDispatchToProps)(Form.create()(EndOfTicket));
