import React from 'react';

import { Space } from '@manyun/base-ui.ui.space';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';

function CheckCodeNewBtn({ code, children }) {
  const [, { checkCode }] = useAuthorized();
  const showBtn = checkCode(code);
  return showBtn ? children : null;
}

export default function generateFilterForm({ actions }) {
  class Actions extends React.PureComponent {
    render() {
      const { selectedRows, onSelectChange } = this.props;
      return (
        <Space>
          {actions.map(({ key, Comp, propsUtils = { showNewBtn: true, pick: () => {} } }) => {
            if (typeof propsUtils.showNewBtn === 'string') {
              return (
                <CheckCodeNewBtn key={key} code={propsUtils.showNewBtn}>
                  <Comp
                    key={key}
                    selectedRows={selectedRows}
                    onSelectChange={onSelectChange}
                    {...propsUtils.defaultProps}
                    {...propsUtils.pick(this.props)}
                  />
                </CheckCodeNewBtn>
              );
            } else if (typeof propsUtils.authCode === 'string') {
              return (
                <CheckCodeNewBtn
                  key={key}
                  code={propsUtils.authCode}
                  children={
                    <Comp key={key} {...propsUtils.defaultProps} {...propsUtils.pick(this.props)} />
                  }
                />
              );
            }
            return (
              <Comp
                key={key}
                selectedRows={selectedRows}
                onSelectChange={onSelectChange}
                {...propsUtils.defaultProps}
                {...propsUtils.pick(this.props)}
              />
            );
          })}
        </Space>
      );
    }
  }

  return Actions;
}
