import moment from 'moment';
import React, { useEffect, useMemo, useState } from 'react';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Badge } from '@manyun/base-ui.ui.badge';
import { Collapse } from '@manyun/base-ui.ui.collapse';
import { Space } from '@manyun/base-ui.ui.space';
import { Steps } from '@manyun/base-ui.ui.steps';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useLazyActiveApprovalScene } from '@manyun/bpm.gql.client.approval';
import { UserAvatar } from '@manyun/iam.ui.user-avatar';
import { SpaceText } from '@manyun/resource-hub.ui.space-text';
import { useLazyTicketFirstTimeRecipientInfo } from '@manyun/ticket.gql.client.tickets';
import { BackendTaskStatus, TaskStatusMap } from '@manyun/ticket.model.ticket';

import TTR from '../../../registries/ticket-type-registry';
import { DEFAULT_FILTER_KEY_MAP } from '../../tickets/constants';
import { TicketApprovalCommentButton } from './ticket-approval-comment-button';
import { TicketStepNodeRelateApprovals } from './ticket-step-node-relate-approvals';
import styles from './ticket-step.module.less';
import { TicketTransferRecordsStep } from './ticket-transfer-records-step';

const CURRENT_PAGE_TYPE = 'tickets';

export function TicketSteps({ basicInfo = {}, getBasicInfo }) {
  const [shouldInitApprovalCollapseArrowShow, setShouldInitApprovalCollapseArrowShow] =
    useState(false);
  const [shouldCloseApprovalCollapseArrowShow, setShouldCloseApprovalCollapseArrowShow] =
    useState(false);
  const [getTicketFirstTimeRecipientInfo, { data: firstTimeRecipientInfo }] =
    useLazyTicketFirstTimeRecipientInfo();
  const config = useMemo(() => {
    if (basicInfo.taskType) {
      return TTR.getTicketTypeInstance(basicInfo.taskType.toLowerCase()).getConfig(
        CURRENT_PAGE_TYPE
      );
    }
  }, [basicInfo.taskType]);
  const [getActiveApprovalScene, { data: approvalScene }] = useLazyActiveApprovalScene();

  const statusOptions = useDeepCompareMemo(() => {
    if (config) {
      const mergedFilters = config.mergeFilters({
        DEFAULT_FILTER_KEY_MAP,
        defaultFilters: [
          {
            key: DEFAULT_FILTER_KEY_MAP.TICKET_STATE,
            label: '工单状态',
            initialProps: {
              optionFilter: option =>
                ![
                  BackendTaskStatus.CLOSE_APPROVER,
                  BackendTaskStatus.INIT,
                  BackendTaskStatus.UNDO,
                ].includes(option.value),
              allowClear: true,
              mode: 'multiple',
            },
          },
        ],
      });
      let options = Object.keys(TaskStatusMap).map(key => ({
        label: TaskStatusMap[key],
        value: key,
      }));
      if (Array.isArray(mergedFilters) && mergedFilters.length > 0) {
        const ticketStatusFilter = mergedFilters.filter(item => item?.key === 'taskStatusList');
        if (ticketStatusFilter[0]?.initialProps) {
          if (ticketStatusFilter[0].initialProps?.optionFilter) {
            options = options.filter(ticketStatusFilter[0].initialProps.optionFilter);
          }
        } else {
          options = options.filter(
            option =>
              ![
                BackendTaskStatus.CLOSE_APPROVER,
                BackendTaskStatus.INIT,
                BackendTaskStatus.UNDO,
              ].includes(option.value)
          );
        }
      }

      return options.map(item => item.value);
    } else {
      return [];
    }
  }, [config]);
  const TICKET_STEP_STATUS_KEY_MAP = useDeepCompareMemo(() => {
    if (
      statusOptions.includes(BackendTaskStatus.INIT) &&
      statusOptions.includes(BackendTaskStatus.CLOSE_APPROVER)
    ) {
      return {
        [BackendTaskStatus.INIT]: 1,
        [BackendTaskStatus.WAITTAKEOVER]: 2,
        [BackendTaskStatus.PROCESSING]: 3,
        [BackendTaskStatus.FINISH]: 6, // step结点需要超过3
        [BackendTaskStatus.FAILURE]: 6,
        [BackendTaskStatus.UNDO]: 0, // 建单审批撤销节点展示在第一步
        [BackendTaskStatus.CLOSE_APPROVER]: 4, // 关单审批时步骤节点展示在第四步
      };
    } else if (statusOptions.includes(BackendTaskStatus.INIT)) {
      return {
        [BackendTaskStatus.INIT]: 1,
        [BackendTaskStatus.WAITTAKEOVER]: 2,
        [BackendTaskStatus.PROCESSING]: 3,
        [BackendTaskStatus.FINISH]: 5, // step结点需要超过3
        [BackendTaskStatus.FAILURE]: 5,
        [BackendTaskStatus.UNDO]: 0, // 建单审批撤销节点展示在第一步
      };
    } else if (statusOptions.includes(BackendTaskStatus.CLOSE_APPROVER)) {
      return {
        [BackendTaskStatus.WAITTAKEOVER]: 1,
        [BackendTaskStatus.PROCESSING]: 2,
        [BackendTaskStatus.FINISH]: 5, // step结点需要超过3
        [BackendTaskStatus.FAILURE]: 5,
        [BackendTaskStatus.CLOSE_APPROVER]: 3, // 关单审批时步骤节点展示在第四步
        [BackendTaskStatus.UNDO]: 0, // 建单审批撤销节点展示在第一步
      };
    } else {
      return {
        [BackendTaskStatus.WAITTAKEOVER]: 1,
        [BackendTaskStatus.PROCESSING]: 2,
        [BackendTaskStatus.FINISH]: 4, // step结点需要超过3
        [BackendTaskStatus.FAILURE]: 4,
        [BackendTaskStatus.UNDO]: 0, // 建单审批撤销节点展示在第一步
      };
    }
  }, [statusOptions]);
  const stepCurrent = useDeepCompareMemo(() => {
    return TICKET_STEP_STATUS_KEY_MAP[basicInfo.taskStatus] || 0;
  }, [statusOptions, basicInfo.taskStatus]);

  const getStep2Time = () => {
    let time;
    if (basicInfo.approvalCode && basicInfo.approvalEndTime) {
      time = basicInfo.approvalEndTime;
    }
    // 新建时配置了审批，关单时停用了审批会出现有approvalCode但是没有approvalEndTime的情况，需要取endTime
    if (
      (basicInfo.approvalCode && !basicInfo.approvalEndTime && basicInfo.endTime) ||
      (!basicInfo.approvalCode && stepCurrent !== 3 && basicInfo.endTime)
    ) {
      time = basicInfo.endTime;
    }
    return time && moment(time).format('YYYY-MM-DD HH:mm:ss');
  };

  const stepItems = useDeepCompareMemo(() => {
    const systemId = 0;
    const firstTimeRecipient = {
      id: firstTimeRecipientInfo?.ticketFirstTimeRecipientInfo?.taskAssignee,
      name: firstTimeRecipientInfo?.ticketFirstTimeRecipientInfo?.taskAssigneeName,
    };
    const isCurrentUserNotSystem = firstTimeRecipient.id !== systemId;
    const showFirstRecipient = isCurrentUserNotSystem && firstTimeRecipient.id;
    return [
      {
        key: 'new',
        title: <Typography.Text style={{ fontSize: 16, lineHeight: '32px' }}>创建</Typography.Text>,

        description: (
          <Space style={{ display: 'flex' }} direction="vertical" size={4}>
            <Space style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Space size={4}>
                {basicInfo.creatorId !== 0 && (
                  <UserAvatar
                    userId={basicInfo.creatorId}
                    username={basicInfo.creatorName}
                    avatarProps={{ size: 22 }}
                    showUserName={false}
                  />
                )}
                {basicInfo.outerCreatorName ? (
                  <Typography.Text>{basicInfo.outerCreatorName}</Typography.Text>
                ) : (
                  <Typography.Text>{basicInfo.creatorName}</Typography.Text>
                )}
              </Space>
              <Typography.Text type="secondary">
                {basicInfo.effectTime && moment(basicInfo.effectTime).format('YYYY-MM-DD HH:mm:ss')}
              </Typography.Text>
            </Space>
          </Space>
        ),
      },
      statusOptions.includes(BackendTaskStatus.INIT)
        ? {
            key: 'init',
            description: (
              <Space style={{ display: 'flex', width: '100%' }} direction="vertical">
                <Collapse
                  className={styles.transferCollapse}
                  defaultActiveKey={
                    stepCurrent === TICKET_STEP_STATUS_KEY_MAP[BackendTaskStatus.INIT]
                      ? 'init'
                      : undefined
                  }
                  bordered={false}
                  expandIconPosition="end"
                  ghost
                >
                  <Collapse.Panel
                    key="init"
                    forceRender
                    header={
                      isCreateApprovalSkipped(basicInfo.workFlowId) ? (
                        <Space size="middle">
                          <Typography.Text style={{ fontSize: 16, lineHeight: '32px' }}>
                            建单审批
                          </Typography.Text>
                          {basicInfo.taskStatus !== BackendTaskStatus.UNDO && <Tag>跳过</Tag>}
                        </Space>
                      ) : (
                        <div
                          style={{
                            display: 'flex',
                            width: `calc(100% - 44px)`,
                            justifyContent: 'space-between',
                          }}
                        >
                          <Typography.Text style={{ fontSize: 16, lineHeight: '32px' }}>
                            建单审批
                          </Typography.Text>

                          <TicketApprovalCommentButton
                            ticketNodeStatus="INIT"
                            ticketNumber={basicInfo.taskNo}
                            ticketStatus={basicInfo.taskStatus}
                          />
                        </div>
                      )
                    }
                    collapsible={shouldInitApprovalCollapseArrowShow ? 'icon' : 'disabled'}
                    showArrow={
                      !isCreateApprovalSkipped(basicInfo.workFlowId)
                        ? shouldInitApprovalCollapseArrowShow
                        : false
                    }
                  >
                    <TicketStepNodeRelateApprovals
                      ticketNodeStatus="INIT"
                      ticketNumber={basicInfo.taskNo}
                      ticketStatus={basicInfo.taskStatus}
                      getBasicInfo={getBasicInfo}
                      setShouldInitApprovalCollapseArrowShow={
                        setShouldInitApprovalCollapseArrowShow
                      }
                    />
                  </Collapse.Panel>
                </Collapse>
                <div>
                  <div style={{ fontSize: 14, position: 'absolute', right: 0 }}>
                    {basicInfo.approvalEffectTime &&
                      moment(basicInfo.approvalEffectTime).format('YYYY-MM-DD HH:mm:ss')}
                  </div>
                </div>
              </Space>
            ),
          }
        : undefined,
      {
        key: 'take',
        description: (
          <Space style={{ display: 'flex' }} direction="vertical" size={4}>
            <Collapse
              className={styles.collapse}
              defaultActiveKey={
                stepCurrent === TICKET_STEP_STATUS_KEY_MAP[BackendTaskStatus.WAITTAKEOVER]
                  ? 'takeOver'
                  : undefined
              }
              bordered={false}
              expandIconPosition="end"
              ghost
            >
              <Collapse.Panel
                key="takeOver"
                header={
                  <Space size="middle">
                    <Typography.Text
                      style={{
                        fontSize: 16,
                        lineHeight: '32px',
                      }}
                    >
                      接单
                    </Typography.Text>
                    {!isCurrentUserNotSystem && <Tag>跳过</Tag>}
                  </Space>
                }
                collapsible={
                  !(stepCurrent < TICKET_STEP_STATUS_KEY_MAP[BackendTaskStatus.WAITTAKEOVER])
                    ? 'icon'
                    : 'disabled'
                }
                showArrow={
                  !isCurrentUserNotSystem
                    ? false
                    : !(stepCurrent < TICKET_STEP_STATUS_KEY_MAP[BackendTaskStatus.WAITTAKEOVER])
                }
              >
                {stepCurrent <= TICKET_STEP_STATUS_KEY_MAP[BackendTaskStatus.WAITTAKEOVER] ? (
                  <div>
                    {Array.isArray(basicInfo.assigneeList) && basicInfo.assigneeList.length > 0 ? (
                      <Space size={0} direction="vertical">
                        <Badge status="warning" text="待接单" />
                        <Space wrap>
                          {basicInfo.assigneeList.map(user => (
                            <UserAvatar
                              key={user.id}
                              avatarContainerProps={{ className: styles.processingUser }}
                              userId={user.id}
                              style={{ width: 36 }}
                              username={user.userName}
                              avatarProps={{ size: 22 }}
                              showUserName
                            />
                          ))}
                        </Space>
                      </Space>
                    ) : (
                      <Space size={0} direction="vertical">
                        <Badge status="warning" text="待接单" />
                        <Typography.Text>
                          <SpaceText guid={basicInfo.blockTag} />
                          下所有用户
                        </Typography.Text>
                      </Space>
                    )}
                  </div>
                ) : (
                  <Space size={0} direction="vertical">
                    <Badge status="success" text="已接单" />
                    {showFirstRecipient ? (
                      <UserAvatar
                        userId={firstTimeRecipient.id}
                        username={firstTimeRecipient.name}
                        avatarProps={{ size: 22 }}
                        showUserName
                      />
                    ) : (
                      (firstTimeRecipient.name ?? '--')
                    )}
                  </Space>
                )}
              </Collapse.Panel>
            </Collapse>
            {stepCurrent > TICKET_STEP_STATUS_KEY_MAP[BackendTaskStatus.WAITTAKEOVER] &&
              isCurrentUserNotSystem && (
                <Space style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Space size={4}>
                    {showFirstRecipient && (
                      <UserAvatar
                        userId={firstTimeRecipient.id}
                        username={firstTimeRecipient.name}
                        avatarProps={{ size: 22 }}
                        showUserName={false}
                      />
                    )}
                    <Typography.Text style={{ width: 60 }} ellipsis={{ tooltip: true }}>
                      {firstTimeRecipient.name ?? '--'}
                    </Typography.Text>
                  </Space>
                  <Typography.Text type="secondary">
                    {firstTimeRecipientInfo?.ticketFirstTimeRecipientInfo?.takeTime &&
                      moment(firstTimeRecipientInfo.ticketFirstTimeRecipientInfo.takeTime).format(
                        'YYYY-MM-DD HH:mm:ss'
                      )}
                  </Typography.Text>
                </Space>
              )}
          </Space>
        ),
      },
      {
        key: 'processing',

        description: (
          <Space style={{ display: 'flex' }} direction="vertical">
            <Collapse
              className={styles.transferCollapse}
              defaultActiveKey={
                stepCurrent === TICKET_STEP_STATUS_KEY_MAP[BackendTaskStatus.PROCESSING]
                  ? 'processing'
                  : undefined
              }
              bordered={false}
              expandIconPosition="end"
              ghost
            >
              <Collapse.Panel
                key="processing"
                header={
                  <Space size="middle">
                    <Typography.Text
                      style={{
                        fontSize: 16,
                        lineHeight: '32px',
                      }}
                    >
                      处理
                    </Typography.Text>
                    {!isCurrentUserNotSystem && <Tag>跳过</Tag>}
                  </Space>
                }
                collapsible={basicInfo.taskAssigneeName ? 'icon' : 'disabled'}
                showArrow={!isCurrentUserNotSystem ? false : basicInfo.taskAssigneeName}
              >
                <TicketTransferRecordsStep basicInfo={basicInfo} />
              </Collapse.Panel>
            </Collapse>
            {[
              TICKET_STEP_STATUS_KEY_MAP[BackendTaskStatus.PROCESSING],
              TICKET_STEP_STATUS_KEY_MAP[BackendTaskStatus.FAILURE],
              TICKET_STEP_STATUS_KEY_MAP[BackendTaskStatus.FINISH],
              TICKET_STEP_STATUS_KEY_MAP[BackendTaskStatus.CLOSE_APPROVER],
            ].includes(stepCurrent) &&
              isCurrentUserNotSystem && (
                <Space style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Space size={4}>
                    {isCurrentUserNotSystem && basicInfo.taskAssignee && (
                      <UserAvatar
                        userId={basicInfo.taskAssignee}
                        username={basicInfo.taskAssigneeName}
                        avatarProps={{ size: 22 }}
                        showUserName={false}
                      />
                    )}
                    <Typography.Text>{basicInfo.taskAssigneeName ?? '--'}</Typography.Text>
                  </Space>
                  <Typography.Text type="secondary">
                    {getStep2Time() && getStep2Time()}
                  </Typography.Text>
                </Space>
              )}
          </Space>
        ),
      },
      statusOptions.includes(BackendTaskStatus.CLOSE_APPROVER)
        ? {
            key: 'close',
            description: (
              <Space style={{ display: 'flex', width: '100%' }} direction="vertical">
                <Collapse
                  className={styles.transferCollapse}
                  defaultActiveKey={
                    stepCurrent === TICKET_STEP_STATUS_KEY_MAP[BackendTaskStatus.CLOSE_APPROVER]
                      ? 'close'
                      : undefined
                  }
                  bordered={false}
                  expandIconPosition="end"
                  ghost
                >
                  <Collapse.Panel
                    key="close"
                    forceRender
                    header={
                      !basicInfo.endWorkFlowId &&
                      [BackendTaskStatus.FINISH, BackendTaskStatus.FAILURE].includes(
                        basicInfo.taskStatus
                      ) ? (
                        <Space size="middle">
                          <Typography.Text
                            style={{
                              fontSize: 16,
                              lineHeight: '32px',
                            }}
                          >
                            关单审批
                          </Typography.Text>
                          <Tag>跳过</Tag>
                        </Space>
                      ) : (
                        <div
                          style={{
                            display: 'flex',
                            width: `calc(100% - 44px)`,
                            justifyContent: 'space-between',
                          }}
                        >
                          <Typography.Text
                            style={{
                              fontSize: 16,
                              lineHeight: '32px',
                            }}
                          >
                            关单审批
                          </Typography.Text>
                          <TicketApprovalCommentButton
                            ticketNodeStatus="APPROVAL_END"
                            ticketNumber={basicInfo.taskNo}
                            ticketStatus={basicInfo.taskStatus}
                          />
                        </div>
                      )
                    }
                    collapsible={shouldCloseApprovalCollapseArrowShow ? 'icon' : 'disabled'}
                    showArrow={approvalScene ? shouldCloseApprovalCollapseArrowShow : false}
                  >
                    <TicketStepNodeRelateApprovals
                      ticketNodeStatus="APPROVAL_END"
                      ticketNumber={basicInfo.taskNo}
                      ticketStatus={basicInfo.taskStatus}
                      getBasicInfo={getBasicInfo}
                      setShouldCloseApprovalCollapseArrowShow={
                        setShouldCloseApprovalCollapseArrowShow
                      }
                    />
                  </Collapse.Panel>
                </Collapse>
                {!(
                  !basicInfo.endWorkFlowId &&
                  [BackendTaskStatus.FINISH, BackendTaskStatus.FAILURE].includes(
                    basicInfo.taskStatus
                  )
                ) && (
                  <div>
                    <div style={{ fontSize: 14, position: 'absolute', right: 0 }}>
                      {basicInfo.endTime && moment(basicInfo.endTime).format('YYYY-MM-DD HH:mm:ss')}
                    </div>
                  </div>
                )}
              </Space>
            ),
          }
        : undefined,
      {
        key: 'compelete',
        title: <div style={{ fontSize: 16, lineHeight: '32px' }}>结束</div>,

        description: (
          <div>
            <div style={{ fontSize: 14, position: 'absolute', right: 0 }}>
              {basicInfo.endTime && moment(basicInfo.endTime).format('YYYY-MM-DD HH:mm:ss')}
            </div>
          </div>
        ),
      },
    ].filter(item => item);
  }, [
    statusOptions,
    basicInfo,
    stepCurrent,
    firstTimeRecipientInfo?.ticketFirstTimeRecipientInfo,
    setShouldInitApprovalCollapseArrowShow,
    shouldInitApprovalCollapseArrowShow,
    setShouldCloseApprovalCollapseArrowShow,
    shouldCloseApprovalCollapseArrowShow,
    approvalScene,
  ]);

  useEffect(() => {
    if (basicInfo.taskNo && basicInfo.taskStatus) {
      getTicketFirstTimeRecipientInfo({ variables: { taskNo: basicInfo.taskNo } });
      basicInfo.approvalCode &&
        getActiveApprovalScene({ variables: { processType: basicInfo.approvalCode } });
    }
  }, [
    basicInfo.taskNo,
    basicInfo.taskStatus,
    basicInfo.approvalCode,
    getTicketFirstTimeRecipientInfo,
    getActiveApprovalScene,
  ]);

  return (
    <Steps direction="vertical" current={stepCurrent} items={stepItems} labelPlacement="vertical" />
  );
}

export default TicketSteps;

function isCreateApprovalSkipped(workflowId) {
  return workflowId === '0' || !workflowId;
}
