import React, { useEffect } from 'react';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Space } from '@manyun/base-ui.ui.space';
import { ApprovalProcessPreview } from '@manyun/bpm.ui.bpm-instance-viewer';
import { CommentModalButton } from '@manyun/bpm.ui.comment-modal-button';
import {
  useLazyTicketRelateApprovalDetail,
  useLazyTicketRelateApprovals,
} from '@manyun/ticket.gql.client.tickets';

export type TicketApprovalCommentButtonProps = {
  ticketNumber: string;
  ticketNodeStatus: string;
  ticketStatus: string;
  showApprovalPreviewIcon?: boolean;
  onSuccess?: () => void;
};
export function TicketApprovalCommentButton({
  ticketNumber,
  ticketNodeStatus,
  ticketStatus,
  showApprovalPreviewIcon = true,
  onSuccess,
}: TicketApprovalCommentButtonProps) {
  const [getTicketRelateApprovals, { data: approvalsData }] = useLazyTicketRelateApprovals();
  const [getDetail, { data }] = useLazyTicketRelateApprovalDetail();

  useEffect(() => {
    getTicketRelateApprovals({ variables: { taskNo: ticketNumber, taskNode: ticketNodeStatus } });
  }, [ticketNumber, ticketNodeStatus, getTicketRelateApprovals, ticketStatus]);
  const processId = useDeepCompareMemo(() => {
    if (
      approvalsData?.ticketRelateApprovals &&
      Array.isArray(approvalsData?.ticketRelateApprovals?.data) &&
      approvalsData.ticketRelateApprovals.data.length > 0
    ) {
      return approvalsData.ticketRelateApprovals.data[
        approvalsData.ticketRelateApprovals.data.length - 1
      ].processId;
    }
    return undefined;
  }, [approvalsData?.ticketRelateApprovals?.data]);

  useEffect(() => {
    // 停用的审批，后端并不会不返回数据，而是将审批id返回一个字符串"0",而"0"是一个无效审批id，因此要过滤
    // 不能请求审批详情
    if (processId && processId !== '0') {
      getDetail({
        variables: {
          taskNo: ticketNumber,
          processId,
        },
      });
    }
  }, [getDetail, processId, ticketNumber]);

  const baseInfo = data?.ticketRelateApprovalDetail;

  return processId && baseInfo?.applyUser && baseInfo?.status !== 'REVOKE' ? (
    <Space size="middle">
      <CommentModalButton
        type="link"
        compact
        style={{ fontSize: 16 }}
        applyUserId={baseInfo?.applyUser}
        instId={processId}
        onSuccess={() => {
          getDetail({
            variables: {
              taskNo: ticketNumber,
              processId,
            },
          });
        }}
      />
      {showApprovalPreviewIcon && <ApprovalProcessPreview processId={processId} />}
    </Space>
  ) : null;
}
