import React from 'react';
import { useParams } from 'react-router-dom';

import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { Button } from '@manyun/base-ui.ui.button';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Divider } from '@manyun/base-ui.ui.divider';
import { SimpleFileList } from '@manyun/base-ui.ui.file-list';
import { Space } from '@manyun/base-ui.ui.space';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import styled, { Theme, css } from '@manyun/dc-brain.theme.theme';
import { useSpace } from '@manyun/resource-hub.gql.client.spaces';
import { MetaTypeText } from '@manyun/resource-hub.ui.meta-type-text';
import { useUpdateApproveAreaMutation } from '@manyun/ticket.gql.client.tickets';
import { TicketStatusText } from '@manyun/ticket.ui.ticket-status-text';
import { TicketSlaText } from '@manyun/ticket.ui.tickets-table';

import { DEFAULT_COLUMN_DATA_INDEX_MAP } from '../../tickets/constants';

export const StyledDiv = styled.div`
  ${props => {
    const { prefixCls } = props.theme;

    return css`
      .manyun-descriptions-item-content {
        overflow: hidden;
      }
    `;
  }}
`;

const SYSTEM_ID = 0;
function extractFirstTwoParts(input) {
  const delimiters = ['.', ','];
  let parts = [input];
  for (const delimiter of delimiters) {
    parts = input.split(delimiter);
    if (parts.length > 1) {
      return parts.slice(0, 2).join(delimiter);
    }
  }
  return input;
}
function transformArray(arr) {
  if (!Array.isArray(arr)) {
    return;
  }

  const result = {};

  // 去重并排序，确保更详细的项目在后面
  const sortedArr = [...new Set(arr)].sort((a, b) => a.length - b.length);

  sortedArr.forEach(item => {
    const prefix = item.split('.')[0];

    if (result[prefix]) {
      result[prefix].push(item);
    } else {
      result[prefix] = item.includes('.') ? [item] : [];
    }
  });
  const _guid = Object.keys(result);
  // 清理掉空数组
  _guid.forEach(key => {
    if (result[key].length === 0) {
      delete result[key];
    }
  });

  return [_guid, result];
}

function SpaceText({ guid, resultText, line = true }) {
  const [spaceIdc, spaceBlock, spaceRoom] = useSpace(guid);
  let text = '';
  if (spaceIdc) {
    text = (
      <Typography.Text style={resultText ? { color: '#fff' } : {}}>
        {spaceIdc.label}
        <Typography.Text
          style={resultText ? { margin: '0 8px', color: '#fff' } : { margin: '0 8px' }}
        >
          /
        </Typography.Text>
      </Typography.Text>
    );
  }

  if (spaceBlock) {
    text = (
      <Typography.Text style={resultText ? { color: '#fff' } : {}}>
        {spaceBlock.label}
        {line && (
          <Divider
            type="vertical"
            style={{ width: '1px', height: '12px', borderLeft: ' 1px solid rgba(217, 217, 217,1)' }}
          />
        )}
      </Typography.Text>
    );
  }
  if (spaceRoom) {
    text = (
      <Typography.Text style={resultText ? { color: '#fff' } : {}}>
        {spaceRoom.label}
        {line && (
          <Divider
            type="vertical"
            style={{ width: '1px', height: '12px', borderLeft: '1px solid rgba(217, 217, 217,1)' }}
          />
        )}
      </Typography.Text>
    );
  }
  return <Typography.Text>{text ? text : guid}</Typography.Text>;
}

export function BasicInfo({ basicInfo = {}, ticketTypes, showSla, showRespondTime, showFile }) {
  const { type } = useParams();
  const [areaList, setAreaList] = React.useState([]);
  const [getApproveArea, { loading: approveAreaLoading }] = useUpdateApproveAreaMutation();
  const fileList = basicInfo.fileList?.filter(({ type }) => type === 'BASE_FILE') || [];
  React.useEffect(() => {
    getApproveArea({
      variables: {
        query: {
          taskNo: basicInfo.taskNo,
        },
      },
      onCompleted(data) {
        setAreaList(data.updateChangeApproveArea?.data || []);
      },
    });
  }, [basicInfo.taskNo]);

  let descriptionsItems = [
    {
      label: '工单单号',
      key: DEFAULT_COLUMN_DATA_INDEX_MAP.TASK_NO,
      value: (
        <Space>
          <Typography.Text>{basicInfo.taskNo}</Typography.Text>
          {basicInfo.customerName && <Tag color="blue">{basicInfo.customerName}</Tag>}
        </Space>
      ),
    },
    {
      label: '工单类型',
      key: DEFAULT_COLUMN_DATA_INDEX_MAP.TASK_TYPE,
      value:
        basicInfo.taskType &&
        ticketTypes[basicInfo.taskType] &&
        ticketTypes[basicInfo.taskType]?.taskValue,
    },
    {
      label: '业务分类',
      key: DEFAULT_COLUMN_DATA_INDEX_MAP.TASK_SUB_TYPE,
      value: (() => {
        let extraData = {};
        try {
          extraData = JSON.parse(basicInfo.taskProperties);
        } catch (error) {}
        if (basicInfo.taskType === 'IT_SERVICE') {
          return basicInfo.taskSubType && ticketTypes[basicInfo.taskSubType]
            ? `${ticketTypes[basicInfo.taskSubType].taskValue}/${extraData.thirdOrderTypeName}`
            : '--';
        }
        if (basicInfo.taskType === 'VISITOR') {
          return basicInfo.taskSubType && ticketTypes[basicInfo.taskSubType] ? (
            <Space size={2}>
              {ticketTypes[basicInfo.taskSubType].taskValue}
              {extraData.reasonType && !extraData.bizTag ? (
                <Space size={2}>
                  /
                  <MetaTypeText
                    metaType="VISITOR_THIRD_CATEGORY"
                    code={extraData.reasonType}
                    queryDeleted
                  />
                </Space>
              ) : (
                ''
              )}
            </Space>
          ) : (
            '--'
          );
        }
        return (
          basicInfo.taskSubType &&
          ticketTypes[basicInfo.taskSubType] &&
          ticketTypes[basicInfo.taskSubType].taskValue
        );
      })(),
    },
    {
      label: '位置',
      key: DEFAULT_COLUMN_DATA_INDEX_MAP.BLOCK_TAG,
      value: (() => {
        let extraData = {};
        try {
          extraData = JSON.parse(basicInfo.taskProperties);
        } catch (error) {
          console.error(`${error} 入室详情页 位置`);
        }
        if (basicInfo.taskType === 'VISITOR') {
          const [guid, value] = transformArray(basicInfo.blockGuidList);

          // 检查是否需要过滤PZ和Z开头的楼栋
          const initDisableVisitTypes = [
            'dcim',
            'physical_security',
            'property',
            'business_visitor',
            'government_visitor',
            'other',
          ];

          // 检查是否有机房类型为IDC_BLOCK的项
          const hasIdcBlock = areaList.some(item => item.blockType === 'IDC_BLOCK');
          // 存在变电站且不存在机房楼
          const hasSubstation = areaList.some(i => i.blockType === 'POWER_STATION') && !hasIdcBlock;
          // 是否为 政府 商务 特殊类型 且有机房楼
          const isSpecialtyVisitType = hasIdcBlock && extraData.bizTag;

          // 如果taskSubType符合条件且有IDC_BLOCK类型的机房，则过滤掉PZ和Z开头的楼栋
          if (initDisableVisitTypes.includes(basicInfo.taskSubType)) {
            guid.forEach(idcGuid => {
              if (value[idcGuid] && Array.isArray(value[idcGuid])) {
                value[idcGuid] = value[idcGuid].filter(blockGuid => {
                  const blockCode = blockGuid.split('.')[1];
                  return isSpecialtyVisitType || hasSubstation
                    ? !(blockCode === 'PZ')
                    : hasIdcBlock
                      ? !(blockCode === 'PZ')
                      : // ? !(blockCode === 'PZ' || blockCode === 'Z')
                        true;
                });
              }
            });
          }

          return (
            <>
              {value
                ? guid.map(idcGuid => (
                    <Typography.Text
                      ellipsis={{
                        tooltip: (
                          <>
                            <SpaceText key={idcGuid} guid={idcGuid} resultText={true} />
                            {value[idcGuid]?.map((blockGuid, index) => (
                              <SpaceText
                                key={blockGuid}
                                guid={blockGuid}
                                line={value[idcGuid].length > index + 1}
                                resultText={true}
                              />
                            ))}
                          </>
                        ),
                      }}
                    >
                      <SpaceText key={idcGuid} guid={idcGuid} />
                      {value[idcGuid]?.map((blockGuid, index) => (
                        <SpaceText
                          key={blockGuid}
                          guid={blockGuid}
                          line={value[idcGuid].length > index + 1}
                        />
                      ))}
                    </Typography.Text>
                  ))
                : '--'}
            </>
          );
        }

        if (basicInfo.taskType === 'WAREHOUSE') {
          function formatSimpleBlockGuidList(blockGuidList) {
            const commonPrefix = blockGuidList[0].split('.')[0];
            const joinedSuffixes = blockGuidList.map(item => item.split('.')[1]);
            return (
              <Typography.Text>
                {commonPrefix}.
                {joinedSuffixes.map((i, index) => (
                  <>
                    {i}
                    {joinedSuffixes.length - 1 > index && <Divider type="vertical" />}
                  </>
                ))}
              </Typography.Text>
            );
          }
          // 出入库工单详情页
          return Array.isArray(basicInfo.blockGuidList) || basicInfo.blockGuidList?.length !== 0
            ? formatSimpleBlockGuidList(basicInfo.blockGuidList)
            : basicInfo.roomGuid;
        }
        return basicInfo.roomGuid
          ? extractFirstTwoParts(basicInfo.roomGuid)
          : basicInfo.taskType === 'ACCESS_CARD_AUTH'
            ? basicInfo.blockGuidList?.join('，')
            : extractFirstTwoParts(basicInfo.blockTag);
      })(),
    },
    {
      label: '工单标题',
      key: DEFAULT_COLUMN_DATA_INDEX_MAP.TICKET_TITLE,
      value: basicInfo.taskTitle,
    },
    {
      label: 'SLA计时/标准',
      key: DEFAULT_COLUMN_DATA_INDEX_MAP.TASK_SLA,
      value: (
        <TicketSlaText
          taskSla={basicInfo.taskSla}
          delay={basicInfo.delay}
          unit="SECOND"
          effectTime={basicInfo.effectTime}
          endTime={basicInfo.endTime}
          shouldLimitShow
        />
      ),
    },
    {
      label: '响应计时/标准',
      key: DEFAULT_COLUMN_DATA_INDEX_MAP.TASK_RESPOND_TIME,
      value: (
        <TicketSlaText
          taskSla={basicInfo.responseSla}
          delay={basicInfo.currentResponse}
          unit="SECOND"
          effectTime={null}
          endTime={null}
          shouldLimitShow
        />
      ),
    },
    {
      label: '当前处理人',
      key: DEFAULT_COLUMN_DATA_INDEX_MAP.TASK_ASSIGNEE_NAME,
      value:
        basicInfo.taskAssignee === SYSTEM_ID ? (
          basicInfo.taskAssigneeName
        ) : (
          <UserLink userId={basicInfo.taskAssignee} userName={basicInfo.taskAssigneeName} />
        ),
    },
    {
      label: '工单状态',
      key: DEFAULT_COLUMN_DATA_INDEX_MAP.TICKET_STATE,
      value: basicInfo.taskStatus && <TicketStatusText status={basicInfo.taskStatus} />,
    },
    {
      label: '提单人',
      key: DEFAULT_COLUMN_DATA_INDEX_MAP.CREATOR_NAME,

      value: basicInfo.outerCreatorName ? (
        basicInfo.outerCreatorName
      ) : basicInfo.creatorId !== SYSTEM_ID ? (
        <UserLink userId={basicInfo.creatorId} userName={basicInfo.creatorName} />
      ) : (
        basicInfo.creatorName
      ),
    },
    {
      label: '附件',
      key: 'fileList',
      value: (() => {
        if (!fileList.length) {
          return null;
        }
        return (
          <SimpleFileList files={fileList}>
            <Button type="link" style={{ height: 'auto', padding: 0 }}>
              预览
            </Button>
          </SimpleFileList>
        );
      })(),
    },
  ];
  if (!showSla) {
    descriptionsItems = descriptionsItems.filter(
      item => item.key !== DEFAULT_COLUMN_DATA_INDEX_MAP.TASK_SLA
    );
  }
  if (!showRespondTime) {
    descriptionsItems = descriptionsItems.filter(
      item => item.key !== DEFAULT_COLUMN_DATA_INDEX_MAP.TASK_RESPOND_TIME
    );
  }
  if (!showFile) {
    descriptionsItems = descriptionsItems.filter(item => item.key !== 'fileList');
  }
  return (
    <StyledDiv>
      <Descriptions column={4} colon={false}>
        {descriptionsItems.map(item => (
          <Descriptions.Item
            key={item.key}
            span={2}
            label={item.label}
            labelStyle={type === 'power' ? { width: 136 } : { width: 132 }}
          >
            {item.value}
          </Descriptions.Item>
        ))}
      </Descriptions>
    </StyledDiv>
  );
}

export default BasicInfo;
