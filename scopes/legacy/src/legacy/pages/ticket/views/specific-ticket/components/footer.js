import React from 'react';
import { useHistory } from 'react-router-dom';

import { useAuthorized } from '@manyun/auth-hub.hook.gql.use-authorized';
import { Button } from '@manyun/base-ui.ui.button';
import { useAppIdentity } from '@manyun/iam.gql.client.iam';
import { BackendTaskStatus } from '@manyun/ticket.model.ticket';
import { BatchUrgeTicketsButton } from '@manyun/ticket.ui.batch-urge-tickets-button';
import { Resend } from '@manyun/ticket.ui.resend';

import { FooterToolBar, GutterWrapper } from '@manyun/dc-brain.legacy.components';

import { Reapproval } from '../../components/reapproval';
import { Revocation } from '../../components/revocation';
import Transfer from '../../components/transfer/index';
import EndOf from '../components/footer-btns/detail-enf-of';
import FailureEndOf from '../components/footer-btns/detail-failure-enf-of';

const CheckUserIdTransfer = ({ basicInfo, detailInfoLoading }) => {
  const [authorized] = useAuthorized({ checkByUserId: basicInfo.taskAssignee });

  return (
    <>
      {authorized && (
        <Transfer
          key="transfer"
          basicInfo={basicInfo}
          type="warning"
          ticketPageType="specific-ticket"
          detailInfoLoading={detailInfoLoading}
        />
      )}
    </>
  );
};
const CheckUserIdEndof = ({ basicInfo, canClose, checkTicketEndable, detailInfoLoading }) => {
  const [authorized] = useAuthorized({ checkByUserId: basicInfo.taskAssignee });

  return (
    <>
      {authorized && (
        <EndOf
          basicInfo={basicInfo}
          canClose={canClose}
          ticketPageType="specific-ticket"
          type="primary"
          checkTicketEndable={checkTicketEndable}
          detailInfoLoading={detailInfoLoading}
        />
      )}
    </>
  );
};

const CheckUserIdFailureEndof = ({
  basicInfo,
  canClose,
  checkTicketEndable,
  detailInfoLoading,
}) => {
  const [authorized] = useAuthorized({ checkByUserId: basicInfo.taskAssignee });

  return (
    <>
      {authorized && (
        <FailureEndOf
          basicInfo={basicInfo}
          canClose={canClose}
          ticketPageType="specific-ticket"
          type="primary"
          checkTicketEndable={checkTicketEndable}
          detailInfoLoading={detailInfoLoading}
        />
      )}
    </>
  );
};
const UrgeBtn = ({ info }) => {
  const [authorized] = useAuthorized({ checkByCode: 'element_risk-check-ticket-urge' });
  return authorized &&
    info.taskType === 'RISK_CHECK' &&
    [BackendTaskStatus.WAITTAKEOVER, BackendTaskStatus.PROCESSING].includes(info.taskStatus) ? (
    <BatchUrgeTicketsButton
      mode="single"
      type="primary"
      selectedRows={[{ taskNo: info.taskNo, taskStatus: info.taskStatus }]}
    />
  ) : (
    <></>
  );
};
const CheckUserIdTakeOver = ({ info, loading, takeOver, detailInfoLoading }) => {
  const [, { checkUserId }] = useAuthorized();
  const { data: appIdentityData } = useAppIdentity({ fetchPolicy: 'no-cache' });
  let _authorized = true;
  if (info.assigneeList?.length) {
    _authorized = info.assigneeList.filter(item => checkUserId(item.id)).length;
  }
  if (info.assigneeRoleList?.length) {
    _authorized = info.assigneeRoleList.filter(
      item => appIdentityData?.appIdentity?.roleCode === item.roleCode
    ).length;
  }

  return (
    <>
      {_authorized ? (
        <Button
          key="takeover"
          loading={loading || detailInfoLoading}
          type="primary"
          onClick={() => takeOver(info.taskNo)}
        >
          接单
        </Button>
      ) : null}
    </>
  );
};

export function Footer({
  basicInfo = {},
  takeOver,
  sufixFooter: SufixFooter,
  showFailureBtn,
  showEndOfTicketBtn,
  showTakeoverBtn,
  showTransferBtn,
  takeOverLoading,
  showReapprovalBtn,
  showRevocationBtn,
  canClose,
  providerValue,
  revoke,
  reFresh,
  checkTicketEndable,
  detailInfoLoading,
}) {
  const history = useHistory();
  const getTransfer = () => {
    if (
      (typeof showTransferBtn === 'function' && showTransferBtn(basicInfo, providerValue)) ||
      (basicInfo.taskStatus === BackendTaskStatus.PROCESSING && showTransferBtn === true)
    ) {
      return <CheckUserIdTransfer basicInfo={basicInfo} detailInfoLoading={detailInfoLoading} />;
    }
    return null;
  };
  const getUrgebtn = () => {
    if (
      [BackendTaskStatus.WAITTAKEOVER, BackendTaskStatus.PROCESSING].includes(basicInfo.taskStatus)
    ) {
      return <UrgeBtn info={basicInfo} />;
    }
  };
  const getTakeover = () => {
    if (
      (typeof showTakeoverBtn === 'function' && showTakeoverBtn(basicInfo, providerValue)) ||
      (basicInfo.taskStatus === BackendTaskStatus.WAITTAKEOVER && showTakeoverBtn === true)
    ) {
      return (
        <CheckUserIdTakeOver
          loading={takeOverLoading}
          takeOver={takeOver}
          info={basicInfo}
          detailInfoLoading={detailInfoLoading}
        />
      );
    }
    return null;
  };

  const getResend = () => {
    if (basicInfo.assigneeList?.length && basicInfo.taskStatus === BackendTaskStatus.WAITTAKEOVER) {
      return (
        <Resend
          assigneeId={basicInfo.assigneeList.map(item => item.id)}
          btnType="default"
          includeCurrentUser
          taskNo={basicInfo.taskNo}
          taskType={basicInfo.taskType}
          blockGuid={basicInfo.blockTag}
          detailInfoLoading={detailInfoLoading}
          onResend={result => result && reFresh()}
        />
      );
    }
    return null;
  };

  const getEndOf = checkTicketEndable => {
    if (
      (typeof showEndOfTicketBtn === 'function' && showEndOfTicketBtn(basicInfo, providerValue)) ||
      (basicInfo.taskStatus === BackendTaskStatus.PROCESSING && showEndOfTicketBtn === true)
    ) {
      return (
        <CheckUserIdEndof
          basicInfo={basicInfo}
          canClose={canClose}
          checkTicketEndable={checkTicketEndable}
          detailInfoLoading={detailInfoLoading}
        />
      );
    }
    return null;
  };

  const getFailureEndOf = checkTicketEndable => {
    if (basicInfo.taskStatus === BackendTaskStatus.PROCESSING && showFailureBtn === true) {
      return (
        <CheckUserIdFailureEndof
          basicInfo={basicInfo}
          canClose={canClose}
          checkTicketEndable={checkTicketEndable}
          detailInfoLoading={detailInfoLoading}
        />
      );
    }
    return null;
  };

  const getReapproval = () => {
    if (basicInfo.taskStatus === BackendTaskStatus.UNDO && showReapprovalBtn) {
      return <Reapproval type="primary" record={basicInfo} />;
    }
    return null;
  };

  const getRevocation = () => {
    if (
      (basicInfo.taskStatus === BackendTaskStatus.INIT ||
        basicInfo.taskStatus === BackendTaskStatus.CLOSE_APPROVER) &&
      showRevocationBtn
    ) {
      return (
        <Revocation
          type="primary"
          record={basicInfo}
          revoke={revoke}
          detailInfoLoading={detailInfoLoading}
        />
      );
    }
    return null;
  };
  // const getFailureEndOf = () => {
  //   if (
  //     (typeof showFailureBtn === 'function' && showFailureBtn(basicInfo, providerValue)) ||
  //     (basicInfo.taskStatus === BackendTaskStatus.PROCESSING && showFailureBtn === true)
  //   ) {
  //     return (
  //       <Authorize algorithm="user-id-only" userId={basicInfo.taskAssignee}>
  //         {authorized => authorized && <FailureEndOf basicInfo={basicInfo} />}
  //       </Authorize>
  //     );
  //   }
  // 失败关单判断逻辑暂时和关单一样
  // ![BackendTaskStatus.FINISH, BackendTaskStatus.FAILURE].includes(
  //   basicInfo.taskStatus
  // )
  //   return null;
  // };
  return (
    <FooterToolBar bodyStyle={{ padding: '16px 20px' }}>
      <GutterWrapper>
        {getTakeover()}
        {getUrgebtn()}
        {getEndOf(checkTicketEndable)}
        {getFailureEndOf(checkTicketEndable)}
        {getTransfer()}
        {getResend()}
        {getRevocation()}
        {getReapproval()}
        {/* {getFailureEndOf()} */}
        {SufixFooter !== null && (
          <SufixFooter
            basicInfo={basicInfo}
            providerValue={providerValue}
            detailInfoLoading={detailInfoLoading}
          />
        )}
        <Button
          onClick={() => {
            history.goBack();
          }}
        >
          返回
        </Button>
      </GutterWrapper>
    </FooterToolBar>
  );
}

export default Footer;
