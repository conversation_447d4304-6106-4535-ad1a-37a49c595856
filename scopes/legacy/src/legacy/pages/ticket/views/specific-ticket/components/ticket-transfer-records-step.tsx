import React, { useEffect, useState } from 'react';

import dayjs from 'dayjs';

import { TicketRedirectTwoTone } from '@manyun/base-ui.icons';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Steps } from '@manyun/base-ui.ui.steps';
import { Typography } from '@manyun/base-ui.ui.typography';

import { fetchOperationLogList } from '@manyun/auth-hub.service.fetch-operation-log-list';
import { UserAvatar } from '@manyun/iam.ui.user-avatar';
import type { BackendTicket } from '@manyun/ticket.model.ticket';

export type TransferRecord = {
  createdAt?: number;
  modifyBy: { id: number; name: string };
  note?: string;
};

export function TicketTransferRecordsStep({ basicInfo }: { basicInfo: BackendTicket }) {
  const [transferRecords, setTransferRecords] = useState<TransferRecord[]>([]);
  const [loading, setLoading] = useState(false);
  useEffect(() => {
    if (basicInfo.taskNo && basicInfo.taskType) {
      (async function () {
        setLoading(true);
        const { error, data } = await fetchOperationLogList({
          page: 1,
          pageSize: 1000,
          targetId: basicInfo.taskNo,
          targetType: basicInfo.taskType,
          modifyType: 'ASSIGN',
        });
        setLoading(false);
        if (error) {
          message.error(error.message);
          return;
        }
        const reverseData = data.data.reverse();
        const targetRecords: TransferRecord[] = [];

        reverseData.forEach((item, index) => {
          if (index !== reverseData.length - 1) {
            targetRecords.push({
              createdAt: item.createdAt,
              modifyBy: item.modifyBy,
              note: item.operation.notes,
            });
          } else {
            targetRecords.push({
              createdAt: item.createdAt,
              modifyBy: item.modifyBy,
              note: item.operation.notes,
            });
            const targetJson = JSON.parse(item.target.content ?? '');
            targetRecords.push({
              createdAt: undefined,
              modifyBy: { id: targetJson[1].targetId, name: targetJson[1].targetName },
              note: undefined,
            });
          }
        });
        setTransferRecords(targetRecords);
      })();
    }
  }, [basicInfo.taskNo, basicInfo.taskType, basicInfo.taskAssignee, basicInfo.taskAssigneeName]);
  return (
    <Spin spinning={loading}>
      <Steps
        size="small"
        direction="vertical"
        items={transferRecords.map(item => ({
          title: (
            <Space style={{ justifyContent: 'space-between' }}>
              <Typography.Text>转交</Typography.Text>
              {item.createdAt && (
                <Typography.Text type="secondary">
                  {dayjs(item.createdAt).format('YYYY-MM-DD HH:mm:ss')}
                </Typography.Text>
              )}
            </Space>
          ),
          status: 'finish',
          icon: (
            <>
              <UserAvatar
                userId={item.modifyBy.id}
                username={item.modifyBy.name}
                showUserName={false}
                avatarProps={{ size: 22 }}
              />
              <TicketRedirectTwoTone
                style={{ position: 'absolute', fontSize: 14, right: -9, bottom: -5 }}
              />
            </>
          ),
          description: (
            <Space style={{ display: 'flex' }} direction="vertical" size={4}>
              <Typography.Text>{item.modifyBy.name}</Typography.Text>
              {item.note && <Typography.Text type="secondary">{item.note}</Typography.Text>}
            </Space>
          ),
        }))}
        labelPlacement="vertical"
      />
    </Spin>
  );
}
