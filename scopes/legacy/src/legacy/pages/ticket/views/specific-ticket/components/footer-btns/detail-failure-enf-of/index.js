import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';

import ExclamationCircleTwoTone from '@ant-design/icons/es/icons/ExclamationCircleTwoTone';
import moment from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';

import {
  ticketFailureEndOfActionCreator,
  ticketTransferActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/ticketActions';

import { FailureReasonType } from '../../../../components/failure-reason-type';
import TimeoutReasonType from '../../../../components/timeout-reason-type';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};

export function DetailFailureEndOf({
  basicInfo = {},
  endOfTicket,
  checkTicketEndable,
  detailInfoLoading,
}) {
  const [visible, setVisible] = useState(false);
  const [delayVisible, setDelayVisible] = useState(false);
  const [form] = Form.useForm();
  const { taskNo, taskType, idcTag } = basicInfo;
  useEffect(() => {
    setDelayVisible(false);
  }, [taskNo, taskType, idcTag]);

  const onEndOf = () => {
    form.validateFields().then(values => {
      endOfTicket({
        params: {
          taskNo: basicInfo.taskNo,
          routeEndWay: 'normal',
          taskType: basicInfo.taskType,
          ...values,
        },
        successCallback: () => {
          setVisible(false);
          message.success('关单成功');
        },
        ticketPageType: 'specific-ticket',
      });
    });
  };

  const onClickEndOf = async () => {
    const { taskNo, idcTag, taskType } = basicInfo;
    if (typeof checkTicketEndable === 'function') {
      const result = await checkTicketEndable(taskNo, idcTag, taskType);
      if (!result) {
        message.error('请先填写故障根因描述');
        return;
      }
    }
    const gmtTime = moment(basicInfo.effectTime);
    const now = moment(new Date().getTime());
    const delay = now.diff(gmtTime, 'minutes');
    if (basicInfo.taskSla !== 0 && delay > basicInfo.taskSla) {
      setDelayVisible(true);
    }
    setVisible(true);
  };

  return (
    <div style={{ display: 'inline-block' }}>
      <Button danger loading={detailInfoLoading} onClick={onClickEndOf}>
        失败关单
      </Button>
      <Modal
        title="失败关单"
        visible={visible}
        destroyOnClose
        onCancel={() => setVisible(false)}
        onOk={() => onEndOf(true)}
      >
        <Space direction="vertical" size={16}>
          <Row>
            <Col span={24} offset={2}>
              <Space>
                <ExclamationCircleTwoTone />
                <span>提示：当前您主动以失败方式结束工单，结束后工单状态为失败</span>
              </Space>
            </Col>
          </Row>
          <Form preserve={false} form={form} colon={false} {...formItemLayout}>
            <Form.Item
              label="原因类型"
              name="failedReason"
              rules={[
                {
                  required: true,
                  message: '原因类型必选！',
                },
              ]}
            >
              <FailureReasonType allowClear />
            </Form.Item>
            <Form.Item
              label="失败原因"
              name="failedDesc"
              rules={[
                {
                  required: true,
                  message: '失败原因必填！',
                },
                {
                  type: 'string',
                  max: 20,
                  message: '最多输入 20 个字符！',
                },
              ]}
            >
              <Input allowClear />
            </Form.Item>
            {delayVisible && (
              <Form.Item
                label="超时原因类型"
                name="delayReason"
                rules={[
                  {
                    required: true,
                    message: '原因类型必选！',
                  },
                ]}
              >
                <TimeoutReasonType allowClear />
              </Form.Item>
            )}
            {delayVisible && (
              <Form.Item
                label="超时原因"
                name="delayDesc"
                rules={[
                  {
                    required: true,
                    message: '超时原因必选！',
                  },
                  { max: 20, message: '最多输入 20 个字符！' },
                ]}
              >
                <Input allowClear />
              </Form.Item>
            )}
          </Form>
        </Space>
      </Modal>
    </div>
  );
}

const mapDispatchToProps = {
  ticketTransferActionCreator: ticketTransferActionCreator,
  endOfTicket: ticketFailureEndOfActionCreator,
};

export default connect(null, mapDispatchToProps)(DetailFailureEndOf);
