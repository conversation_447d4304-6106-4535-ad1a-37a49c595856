@import '~@manyun/base-ui.theme.theme/dist/theme.variable.less';

.collapse {
  :global {
    .@{prefixCls}-collapse-header {
      padding: 0 !important;
    }
    .@{prefixCls}-collapse-content-box {
      background-color: @container-default-background-color !important;
    }
  }
}
.processingUser {
  :global {
    .@{prefixCls}-space-item:nth-child(2) {
      width: 100%;
      font-size: 12px;
    }
  }
}
.transferCollapse {
  :global {
    .@{prefixCls}-collapse-header {
      padding: 0 !important;
    }
    .@{prefixCls}-collapse-content-box {
      padding: 0 !important;
    }
  }
}

.ticketNodeCollapsePanel {
  :global {
    .@{prefixCls}-collapse-header.@{prefixCls}-collapse-icon-collapsible-only:first-child {
      .@{prefixCls}-collapse-header-text {
        // 这个颜色，在base ui里没有被声明为变量，需要注意
        background: #e6f7ff;
        padding: 4px 12px 4px 12px;
      }
    }
  }
}
