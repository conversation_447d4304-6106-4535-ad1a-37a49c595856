import React, { useEffect, useMemo } from 'react';
import { Link } from 'react-router-dom';

import { Collapse } from '@manyun/base-ui.ui.collapse';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Typography } from '@manyun/base-ui.ui.typography';

import type { BpmInstance } from '@manyun/bpm.model.bpm-instance';
import { generateBPMRoutePath } from '@manyun/bpm.route.bpm-routes';
import {
  useLazyTicketRelateApprovalDetail,
  useLazyTicketRelateApprovals,
} from '@manyun/ticket.gql.client.tickets';
import { BpmInstanceViewer } from '@manyun/ticket.ui.bpm-instance-viewer';

import styles from './ticket-step.module.less';

export type TicketStepNodeRelateApprovalsProps = {
  ticketNumber: string;
  /** 建单/关单 审批 */
  ticketNodeStatus: string;
  ticketStatus: string;
  getBasicInfo: () => void;
  setShouldInitApprovalCollapseArrowShow: (param: boolean) => void;
  setShouldCloseApprovalCollapseArrowShow: (param: boolean) => void;
};
export function TicketStepNodeRelateApprovals({
  ticketNumber,
  ticketNodeStatus,
  ticketStatus,
  getBasicInfo,
  setShouldInitApprovalCollapseArrowShow,
  setShouldCloseApprovalCollapseArrowShow,
}: TicketStepNodeRelateApprovalsProps) {
  const [getTicketRelateApprovals, { loading: approvalsLoading, data: approvalsData }] =
    useLazyTicketRelateApprovals();
  useEffect(() => {
    getTicketRelateApprovals({ variables: { taskNo: ticketNumber, taskNode: ticketNodeStatus } });
  }, [ticketNumber, ticketNodeStatus, getTicketRelateApprovals, ticketStatus]);
  const validApprovals = useMemo(() => {
    if (
      approvalsData?.ticketRelateApprovals?.data &&
      Array.isArray(approvalsData.ticketRelateApprovals.data)
    ) {
      return approvalsData.ticketRelateApprovals.data.filter(item => item.processId !== '0');
    }
    return [];
  }, [approvalsData?.ticketRelateApprovals?.data]);
  useEffect(() => {
    if (validApprovals.length > 0) {
      setShouldInitApprovalCollapseArrowShow && setShouldInitApprovalCollapseArrowShow(true);
      setShouldCloseApprovalCollapseArrowShow && setShouldCloseApprovalCollapseArrowShow(true);
    } else {
      setShouldInitApprovalCollapseArrowShow && setShouldInitApprovalCollapseArrowShow(false);
      setShouldCloseApprovalCollapseArrowShow && setShouldCloseApprovalCollapseArrowShow(false);
    }
  }, [
    setShouldInitApprovalCollapseArrowShow,
    validApprovals,
    setShouldCloseApprovalCollapseArrowShow,
  ]);
  return (
    <Spin spinning={approvalsLoading}>
      {validApprovals.length > 0 && (
        <Collapse
          bordered={false}
          expandIconPosition="end"
          ghost
          defaultActiveKey={[validApprovals[validApprovals.length - 1].processId]}
        >
          {validApprovals.map((approval, index) => (
            <Collapse.Panel
              key={approval.processId}
              className={styles.ticketNodeCollapsePanel}
              style={{
                marginBottom: index !== validApprovals.length ? 8 : 0,
              }}
              header={
                <>
                  <Typography.Text>审批ID:</Typography.Text>
                  <Link target="_blank" to={generateBPMRoutePath({ id: approval.processId })}>
                    {approval.processId}
                  </Link>
                </>
              }
              collapsible="icon"
            >
              <TicketRelateApproval
                ticketNumber={ticketNumber}
                processId={approval.processId}
                ticketStatus={ticketStatus}
                getBasicInfo={getBasicInfo}
              />
            </Collapse.Panel>
          ))}
        </Collapse>
      )}
    </Spin>
  );
}

export type TicketRelateApprovalProps = {
  processId: string;
  ticketNumber: string;
  ticketStatus: string;
  getBasicInfo: () => void;
};
export function TicketRelateApproval({
  ticketNumber,
  processId,
  ticketStatus,
  getBasicInfo,
}: TicketRelateApprovalProps) {
  const [getDetail, { loading, data }] = useLazyTicketRelateApprovalDetail();
  useEffect(() => {
    getDetail({
      variables: {
        taskNo: ticketNumber,
        processId,
      },
    });
  }, [processId, ticketNumber, getDetail, ticketStatus]);
  return (
    <Spin spinning={loading}>
      {data?.ticketRelateApprovalDetail && (
        <BpmInstanceViewer
          bpmInstance={data.ticketRelateApprovalDetail! as unknown as BpmInstance}
          showFirstStep={false}
          showCarbonCopyStep={false}
          showOperationButtons
          onSuccess={() => {
            getDetail({
              variables: {
                taskNo: ticketNumber,
                processId,
              },
            });
            getBasicInfo && getBasicInfo();
          }}
        />
      )}
    </Spin>
  );
}
