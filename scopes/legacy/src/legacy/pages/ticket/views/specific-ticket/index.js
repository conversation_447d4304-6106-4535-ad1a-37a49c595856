import React, { createContext, useContext, useRef } from 'react';
import { connect } from 'react-redux';

import {
  OperationLogTable,
  OperationLogTableContext,
} from '@manyun/auth-hub.ui.operation-log-table';
import { Container } from '@manyun/base-ui.ui.container';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { BackendTaskStatus } from '@manyun/ticket.model.ticket';
import { NotificationModalButton } from '@manyun/ticket.ui.notification-modal-button';

import { TinyEmpty } from '@manyun/dc-brain.legacy.components';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  getTicketFilesActionCreator,
  revokeAction,
  singleTakeOverActionCreator,
  ticketActions,
  ticketBasicInfoActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/ticketActions';

import { CURRENT_PAGE_TYPES } from '../../constants';
import TTR from '../../registries/ticket-type-registry';
import BasicInfo from './components/basicInfo';
import Footer from './components/footer';
import TicketSteps from './components/steps';

const CURRENT_PAGE_TYPE = CURRENT_PAGE_TYPES.SPECIFIC_TICKET;
const StateContext = createContext(null);
const initialConfigData = {
  content: null, // React Component
  sufixFooter: null,
  showFailureBtn: false,
  showEndOfTicketBtn: true,
  showTakeoverBtn: true,
  showTransferBtn: true,
  showRevocationBtn: false,
  showReapprovalBtn: false,
  canClose: () => true,
  canCloseArgsUtil: null,
  tabBarExtraContent: () => null,
  showStep: true,
  showOperationRecords: true,
  showSla: true,
  showRespondTime: true,
  showFile: true,
  checkTicketEndable: null,
};
export function useStoreState() {
  return useContext(StateContext);
}
/**
 *
 * @typedef {Array<{ label: string; key: string }>} BasicInfo
 *
 * @typedef Config
 * @property {React.ComponentType<any>} content
 * @property {React.ComponentType<any>} sufixFooter
 * @property {boolean | (basicInfo: {},providerValue: {}) => boolean} showFailureBtn 默认true
 * @property {boolean | (basicInfo: {},providerValue: {}) => boolean} showEndOfTicketBtn 展示关单按钮 默认true
 * @property {boolean | (basicInfo: {},providerValue: {}) => boolean} showTakeoverBtn 展示接受按钮 默认true
 * @property {boolean | (basicInfo: {},providerValue: {}) => boolean} showTransferBtn 展示转交按钮 默认true
 * @property {boolean | (basicInfo: {},providerValue: {}) => boolean} showRevocationBtn 展示撤回按钮 默认false
 * @property {boolean | (basicInfo: {},providerValue: {}) => boolean} showReapprovalBtn 展示重新审批按钮 默认false
 * @property {(basicInfo: {},closable:{}) => boolean} canClose 是否可以关单
 * @property {pick:(providerValue:{})=> {}} canCloseArgsUtil 获取canClose 的第二个参数
 * @property {boolean } showStep 默认true
 *
 *
 *
 * @typedef State
 * @property {Config} config
 */

/**
 * @augments {React.PureComponent<any, State>}
 */
export class SpecificTicket extends React.PureComponent {
  state = {
    config: initialConfigData,
    providerValue: null,
    activeTab: 'content',
  };

  componentDidMount() {
    this.initData();
  }
  componentDidUpdate(prevProps) {
    const prevId = prevProps.match.params.id;
    const id = this.props.match.params.id;
    const isIdChanged = id !== prevId;
    if (isIdChanged) {
      this.initData();
    }
    const prevTaskType = prevProps.ticketView.basicInfo.taskType;
    const taskType = this.props.ticketView.basicInfo.taskType;
    const isTaskTypeChanged = taskType !== prevTaskType;
    if (taskType && (isIdChanged || isTaskTypeChanged)) {
      this.props.getTicketFiles({
        targetId: id,
        targetType: taskType,
      });
    }
  }

  componentWillUnmount() {
    this.props.resetBaicInfo();
  }

  initData = () => {
    this._initialize();
    this.props.ticketBasicInfoActionCreator({ taskNo: this.props.match.params.id });
    this.props.syncCommonData({ strategy: { ticketTypes: 'IF_NULL' } });
    this.props.setTicketType(this.props.match.params.type.toUpperCase());
  };

  _getTicketType = () => {
    return this.props.match.params.type;
  };

  _initialize = () => {
    const ticketType = this._getTicketType();
    const inst = TTR.getTicketTypeInstance(ticketType);

    if (!(inst && inst.hasConfig(CURRENT_PAGE_TYPE))) {
      return;
    }

    const config = inst.getConfig(CURRENT_PAGE_TYPE);

    this.setState({ config: { ...initialConfigData, ...config } });
  };

  takeOver = taskNo => {
    this.props.takeOver({
      taskNo,
      successCallback: null,
      ticketPageType: 'specific-ticket',
    });
  };

  setProviderValue = value => {
    this.setState({ providerValue: value });
  };

  onChangeTab = key => {
    this.setState({ activeTab: key });
  };

  revoke = taskNo => {
    new Promise(() => {
      this.props.revokeAction({
        taskNo,
        type: this.props.match.params.type,
        ticketPageType: 'specific-ticket',
        revokeType:
          this.props.ticketView.basicInfo.taskStatus === BackendTaskStatus.CLOSE_APPROVER
            ? 'end'
            : 'create',
      });
    });
  };

  render() {
    const { ticketView, ticketTypes, takeOverLoading, location, match } = this.props;
    const { config, providerValue, activeTab } = this.state;

    const { basicInfo, loading } = ticketView;
    const detailProps = {
      ...this.props,
      ticketView,
      ticketTypes,
      takeOverLoading,
      location,
      match,
      config,
      providerValue,
      activeTab,
      basicInfo,
      loading,
      context: this,
    };
    return <DetailContent {...detailProps} />;
  }
}

const DetailContent = props => {
  const {
    ticketTypes,
    takeOverLoading,
    location,
    match,
    config,
    providerValue,
    activeTab,
    basicInfo,
    loading,
    context,
  } = props;
  const containerRef = useRef(null);

  const isBasicInfoInitialized = Object.keys(basicInfo).length > 2;
  return (
    <StateContext.Provider value={providerValue}>
      <div ref={containerRef} style={{ width: '100%', height: '100%' }}>
        <div
          style={{
            width: '100%',
            height: '100%',
            background: 'white',
            display: 'flex',
            marginBottom: 48,
          }}
          size={22}
        >
          <Space
            style={{
              display: 'flex',
              width: 'calc(100% - 388px)',
              marginRight: 22,
            }}
            direction="vertical"
          >
            <Container style={{ padding: '24px 0 0 24px' }}>
              <Space style={{ display: 'flex', height: '100%' }} direction="vertical" size="middle">
                <div style={{ display: 'flex', width: '100%', justifyContent: 'space-between' }}>
                  <Typography.Title style={{ marginBottom: 0 }} showBadge level={5}>
                    工单信息
                  </Typography.Title>
                  {['REPAIR', 'MAINTENANCE'].includes(basicInfo.taskType) &&
                  basicInfo.taskStatus &&
                  basicInfo.taskStatus !== BackendTaskStatus.WAITTAKEOVER ? (
                    <NotificationModalButton
                      type={basicInfo.taskType.toLowerCase()}
                      notificationInfo={basicInfo}
                    />
                  ) : null}
                </div>
                {Object.keys(basicInfo).length > 2 && (
                  <BasicInfo
                    basicInfo={basicInfo}
                    ticketTypes={ticketTypes}
                    showSla={config.showSla}
                    showRespondTime={config.showRespondTime}
                    showFile={config.showFile}
                  />
                )}
              </Space>
            </Container>

            <Container
              style={{
                padding: '8px 0 0 24px',
              }}
            >
              <Space
                style={{
                  display: 'flex',
                  height: '100%',
                }}
                direction="vertical"
                size="middle"
              >
                <div style={{ display: 'flex', width: '100%', justifyContent: 'space-between' }}>
                  <Typography.Title style={{ marginBottom: 0 }} showBadge level={5}>
                    工单内容
                  </Typography.Title>
                  {config?.tabBarExtraContent(activeTab, providerValue)}
                </div>
                {config.content && isBasicInfoInitialized ? (
                  <config.content
                    basicInfo={basicInfo}
                    taskNo={context.props.match.params.id}
                    setProviderValue={context.setProviderValue}
                    location={location}
                    getBasicInfo={(params = {}) =>
                      context.props.ticketBasicInfoActionCreator({
                        taskNo: context.props.match.params.id,
                        ...params,
                      })
                    }
                    match={match}
                  />
                ) : (
                  <TinyEmpty />
                )}
              </Space>
            </Container>
            {config.showOperationRecords && (
              <Container style={{ padding: '24px 0 24px 24px' }}>
                <Space
                  style={{ display: 'flex', height: '100%' }}
                  direction="vertical"
                  size="middle"
                >
                  <Typography.Title style={{ marginBottom: 0 }} showBadge level={5}>
                    操作记录
                  </Typography.Title>
                  <OperationLogTableContext.Provider
                    value={{
                      searchParams: {
                        targetId: context.props.match.params.id,
                        targetType: context.props.match.params.type.toUpperCase(),
                      },
                    }}
                  >
                    <OperationLogTable
                      showColumns={['serialNumber', 'modifyType']}
                      isTargetIdEqual={targetId => {
                        return targetId === context.props.match.params.id;
                      }}
                    />
                  </OperationLogTableContext.Provider>
                </Space>
              </Container>
            )}

            {basicInfo.taskStatus !== BackendTaskStatus.FINISH &&
              basicInfo.taskStatus !== BackendTaskStatus.FAILURE && (
                <Footer
                  detailInfoLoading={loading}
                  showFailureBtn={config.showFailureBtn}
                  showTransferBtn={config.showTransferBtn}
                  showEndOfTicketBtn={config.showEndOfTicketBtn}
                  showTakeoverBtn={config.showTakeoverBtn}
                  takeOver={context.takeOver}
                  basicInfo={basicInfo}
                  sufixFooter={config.sufixFooter}
                  takeOverLoading={takeOverLoading}
                  providerValue={providerValue}
                  showRevocationBtn={
                    typeof config.showRevocationBtn === 'function'
                      ? config.showRevocationBtn(basicInfo)
                      : config.showRevocationBtn
                  }
                  showReapprovalBtn={
                    typeof config.showReapprovalBtn === 'function'
                      ? config.showReapprovalBtn(basicInfo)
                      : config.showReapprovalBtn
                  }
                  checkTicketEndable={config?.checkTicketEndable}
                  revoke={context.revoke}
                  reFresh={() =>
                    context.props.ticketBasicInfoActionCreator({
                      taskNo: context.props.match.params.id,
                    })
                  }
                  canClose={() =>
                    config.canClose(
                      basicInfo,
                      config.canCloseArgsUtil
                        ? config.canCloseArgsUtil.pick(providerValue)
                        : providerValue
                    )
                  }
                />
              )}
          </Space>
          <div style={{ display: 'flex', flexDirection: 'vertical', alignItems: 'center' }}>
            <Divider spaceSize="mini" style={{ height: `calc(100% - 48px)` }} type="vertical" />
          </div>
          {config.showStep && isBasicInfoInitialized && (
            <Container
              style={{
                width: 342,
                display: 'flex',
                flex: 1,
                padding: '24px 24px 0 0',
                marginLeft: 22,
              }}
            >
              <Space
                style={{ display: 'flex', flex: 1, height: '100%' }}
                direction="vertical"
                size="middle"
              >
                <div
                  style={{
                    display: 'flex',
                    width: '100%',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                  }}
                >
                  <Typography.Title style={{ marginBottom: 0 }} showBadge level={5}>
                    工单审批流程
                  </Typography.Title>
                </div>
                <TicketSteps
                  basicInfo={basicInfo}
                  getBasicInfo={() =>
                    context.props.ticketBasicInfoActionCreator({
                      taskNo: context.props.match.params.id,
                    })
                  }
                />
              </Space>
            </Container>
          )}
        </div>
      </div>
    </StateContext.Provider>
  );
};

const mapStateToProps = ({ ticket: { ticketView, takeOverLoading }, common: { ticketTypes } }) => ({
  ticketView,
  ticketTypes: ticketTypes?.normalizedList || {},
  takeOverLoading,
});

const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
  ticketBasicInfoActionCreator: ticketBasicInfoActionCreator,
  takeOver: singleTakeOverActionCreator,
  setTicketType: ticketActions.setTicketType,
  getTicketFiles: getTicketFilesActionCreator,
  resetBaicInfo: ticketActions.resetBaicInfo,
  revokeAction,
};

export default connect(mapStateToProps, mapDispatchToProps)(SpecificTicket);
