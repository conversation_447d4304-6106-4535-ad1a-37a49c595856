import ExclamationCircleFilled from '@ant-design/icons/es/icons/ExclamationCircleFilled';
import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Radio } from '@manyun/base-ui.ui.radio';
import { useLazyAlarmCountByBiz } from '@manyun/ticket.gql.client.tickets';
import { BackendTaskStatus } from '@manyun/ticket.model.ticket';
import { fetchRiskOperationRecords } from '@manyun/ticket.service.fetch-risk-operation-records';
import { resolveRisk } from '@manyun/ticket.service.resolve-risk';

import {
  ticketFailureEndOfActionCreator,
  ticketSingleEndOfActionCreator,
  ticketTransferActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/ticketActions';

import { FailureReasonType } from '../../../../components/failure-reason-type';
import TimeoutReasonType from '../../../../components/timeout-reason-type';

const generateFormItemLayout = basicInfo =>
  isRiskTicket(basicInfo.taskType)
    ? {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 6 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 18 },
        },
      }
    : {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 4 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 20 },
        },
      };

export function DetailEndOf({
  basicInfo = {},
  type,
  style = {},
  endOfTicket,
  canClose,
  failureEndOfTicket,
  ticketPageType,
  compact = false,
  checkTicketEndable,
  detailInfoLoading,
}) {
  const [visible, setVisible] = useState(false);
  const [isFailture, setisFailture] = useState(false);
  const [isTimeout, setIsTimeout] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [alarmModal, setAlarmModal] = useState({ open: false });
  const [form] = Form.useForm();
  const { taskNo, taskType, idcTag } = basicInfo;
  const [getAlarmCountByBiz, {}] = useLazyAlarmCountByBiz(); //data.TRIGGER!==0 ?'提示':''

  useEffect(() => {
    setisFailture(false);
    setIsTimeout(false);
  }, [taskNo, taskType, idcTag]);
  const handleModalVisible = () => {
    setVisible(true);
    (async () => {
      const { error, data } = await fetchRiskOperationRecords({ taskNo: basicInfo.taskNo });
      if (error) {
        message.error(error.message);
        return;
      }
      if (!data.data.length) {
        setisFailture(true);
      }
    })();
  };

  const onEndOf = async () => {
    const { taskNo, idcTag, taskType } = basicInfo;
    if (typeof checkTicketEndable === 'function') {
      const result = await checkTicketEndable(taskNo, idcTag, taskType);
      if (!result) {
        message.error('请先填写故障根因描述');
        return;
      }
    }
    form.validateFields().then(async values => {
      setLoading(true);
      if (values.isRiskResolved) {
        const { error } = await resolveRisk({ taskNo: basicInfo.taskNo });
        if (error) {
          setLoading(false);
          message.error(error.message);
          return;
        }
      }
      endOfTicket({
        params: {
          taskNo: basicInfo.taskNo,
          taskType: basicInfo.taskType,
          routeEndWay: basicInfo.approvalCode ? 'approval' : 'normal',
          ...values,
        },
        successCallback: (error, errorCode, data) => {
          setLoading(false);

          setErrorMessage(error);
          if (errorCode === 'TIMEOUT_REASON_IS_EMPTY') {
            setIsTimeout(true);
            setVisible(true);
            return;
          }
          if (errorCode === 'FAILED_REASON_IS_EMPTY') {
            setisFailture(true);
            setVisible(true);
            return;
          }
          if (errorCode === 'CAN_NOT_END_TASK_WITH_EXCEPTION_YG') {
            Modal.confirm({
              content: '存在未处理的异常巡检项，确定继续？',
              okText: '确定',
              cancelText: '取消',
              onOk: () => {
                onYGHasExpectionEndOf(values);
              },
            });
            return;
          }
          if (!errorCode) {
            setisFailture(false);
            setTimeout(false);
            setVisible(false);
            message.success(
              `${data === BackendTaskStatus.CLOSE_APPROVER ? '已提交关单审批' : '已关单'}`
            );
          } else {
            message.error(error);
          }
        },
        ticketPageType: ticketPageType,
      });
    });
  };

  const onYGHasExpectionEndOf = async values => {
    const { taskNo, approvalCode, taskType } = basicInfo;
    setLoading(true);
    endOfTicket({
      params: {
        taskNo: taskNo,
        taskType: taskType,
        routeEndWay: approvalCode ? 'approval' : 'normal',
        ...values,
        ignoreCheckEnd: true,
      },
      successCallback: (error, errorCode, data) => {
        setLoading(false);
        if (!errorCode) {
          setisFailture(false);
          setTimeout(false);
          setVisible(false);
          message.success(
            `${data === BackendTaskStatus.CLOSE_APPROVER ? '已提交关单审批' : '已关单'}`
          );
        } else {
          message.error(error);
        }
      },
      ticketPageType: ticketPageType,
    });
  };
  const isPowerTask = basicInfo.taskType === 'POWER';

  return (
    <div style={{ display: 'inline-block' }}>
      <Button
        compact={compact}
        loading={loading || detailInfoLoading}
        type={type}
        style={{ style }}
        onClick={async () => {
          if (isPowerTask) {
            const {
              data: { alarmCountByBiz },
            } = await getAlarmCountByBiz({
              variables: {
                query: {
                  bizId: taskNo,
                  bizType: 'POWER',
                  idcTag: basicInfo.idcTag,
                },
              },
            });

            if (alarmCountByBiz?.data && +alarmCountByBiz?.data?.TRIGGER !== 0) {
              setAlarmModal({ open: true });
            } else {
              isRiskTicket(basicInfo.taskType) ? handleModalVisible() : onEndOf();
            }
          } else {
            isRiskTicket(basicInfo.taskType) ? handleModalVisible() : onEndOf();
          }
        }}
      >
        关单
      </Button>
      <Modal
        title={generateModalTitle(basicInfo.taskType, isTimeout, isFailture)}
        open={visible}
        confirmLoading={loading}
        destroyOnClose
        okText={isRiskTicket(basicInfo.taskType) && !isFailture ? '确认关单' : '确定'}
        onOk={() => onEndOf()}
        onCancel={() => {
          setVisible(false);
          setisFailture(false);
        }}
      >
        <Form
          form={form}
          preserve={false}
          colon={false}
          autoComplete="off"
          {...generateFormItemLayout(basicInfo)}
        >
          {!isRiskCheckTicket(basicInfo.taskType) && (
            <div style={{ marginBottom: 16 }}>
              {isFailture && (
                <>
                  <ExclamationCircleFilled
                    style={{
                      color: `var(--${prefixCls}-warning-color)`,
                      marginRight: 16,
                      fontSize: 16,
                    }}
                  />
                  <span>
                    {isRiskTicket(basicInfo.taskType)
                      ? '当前工单未添加处理记录，无法正常关单，请填写失败原因'
                      : errorMessage}
                  </span>
                </>
              )}
              {isTimeout && !isFailture && (
                <>
                  <ExclamationCircleFilled
                    style={{
                      color: `var(--${prefixCls}-warning-color)`,
                      marginRight: 16,
                      fontSize: 16,
                    }}
                  />
                  <span>当前工单SLA已超时，请填写超时原因！</span>
                </>
              )}
            </div>
          )}
          {isRiskTicket(basicInfo.taskType) && (
            <Form.Item
              name="isRiskResolved"
              label="风险是否解除"
              rules={[
                {
                  required: true,
                  message: '请选择风险解除状态！',
                },
              ]}
            >
              <Radio.Group>
                <Radio value>已解除</Radio>
                <Radio value={false}>未解除</Radio>
              </Radio.Group>
            </Form.Item>
          )}
          {isFailture && [
            <Form.Item
              key="failedReason"
              name="failedReason"
              label="原因类型"
              rules={[
                {
                  required: true,
                  message: '原因类型必选！',
                },
              ]}
            >
              <FailureReasonType ticketType={basicInfo.taskType} allowClear />
            </Form.Item>,
            <Form.Item
              key="failedDesc"
              name="failedDesc"
              label="失败原因"
              rules={[
                {
                  required: true,
                  message: '失败原因必填！',
                },
                {
                  type: 'string',
                  max: 20,
                  message: '最多输入 20 个字符！',
                },
              ]}
            >
              <Input allowClear />
            </Form.Item>,
          ]}
          {isTimeout &&
            !isFailture && [
              <Form.Item
                key="delayReason"
                name="delayReason"
                label="原因类型"
                rules={[
                  {
                    required: true,
                    message: '原因类型必选！',
                  },
                ]}
              >
                <TimeoutReasonType allowClear />
              </Form.Item>,
              <Form.Item
                key="delayDesc"
                name="delayDesc"
                label="超时原因"
                rules={[
                  {
                    required: true,
                    message: '超时原因必选！',
                  },
                  {
                    max: 20,
                    message: '最多输入 20 个字符！',
                  },
                ]}
              >
                <Input allowClear />
              </Form.Item>,
            ]}
        </Form>
      </Modal>
      {isPowerTask && (
        <Modal
          open={alarmModal.open}
          width={'480px'}
          title={null}
          closable={false}
          okText={'强制关闭'}
          cancelText={'取消'}
          onCancel={() => {
            setAlarmModal({ open: false });
          }}
          onOk={() => {
            setAlarmModal({ open: false });
            isRiskTicket(basicInfo.taskType) ? handleModalVisible() : onEndOf();
          }}
        >
          系统检查到该工单关联的告警尚未全部恢复，建议您稍后再试。如果您强行关闭工单，相关未恢复的告警将被推送至告警盯屏。
        </Modal>
      )}
    </div>
  );
}

const mapDispatchToProps = {
  ticketTransferActionCreator: ticketTransferActionCreator,
  endOfTicket: ticketSingleEndOfActionCreator,
  failureEndOfTicket: ticketFailureEndOfActionCreator,
};

export default connect(null, mapDispatchToProps)(DetailEndOf);

const generateModalTitle = (taskType, isTimeout, isFailture) => {
  if (taskType === 'RISK_REGISTER' && !isFailture) {
    return '完结风险工单';
  }
  return isTimeout && !isFailture ? '超时原因' : '失败关单';
};

const isRiskTicket = taskType => {
  return taskType === 'RISK_REGISTER';
};

const isRiskCheckTicket = taskType => {
  return taskType === 'RISK_CHECK';
};
