import React, { useState } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';

import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';

// import { ticketService } from '@manyun/dc-brain.legacy.services';
import { UserSelect } from '@manyun/iam.ui.user-select';
import { BackendTaskStatus } from '@manyun/ticket.model.ticket';

import { ticketTransferActionCreator } from '@manyun/dc-brain.legacy.redux/actions/ticketActions';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};

export function Transfer({
  basicInfo = {},
  form,
  type,
  btnStyle = {},
  ticketTransferActionCreator,
  ticketPageType,
  compact = false,
  detailInfoLoading,
}) {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const { getFieldDecorator } = form;

  const onTransfer = () => {
    form.validateFields(async (err, values) => {
      if (err) {
        return;
      }
      setLoading(true);
      ticketTransferActionCreator({
        params: {
          taskNo: basicInfo.taskNo,
          taskAssignee: values.taskAssignee.key,
          taskAssigneeName: values.taskAssignee.label,
          reassigneeDesc: values.reassigneeDesc,
        },
        successCallback: () => {
          setVisible(false);
          setLoading(false);
          message.success('转交成功');
        },
        errorCallback: () => {
          setLoading(false);
        },
        ticketPageType,
      });
    });
  };

  return (
    <div key={basicInfo.taskNo} style={{ display: 'inline-block' }}>
      {basicInfo.taskStatus === BackendTaskStatus.PROCESSING && (
        <Button
          loading={detailInfoLoading}
          compact={compact}
          style={btnStyle}
          type={type}
          onClick={() => setVisible(true)}
        >
          转交
        </Button>
      )}
      <Modal
        title="转交"
        open={visible}
        destroyOnClose
        okButtonProps={{ loading }}
        onCancel={() => setVisible(false)}
        onOk={onTransfer}
      >
        <Form colon={false} {...formItemLayout}>
          <Form.Item label="转交对象">
            {getFieldDecorator('taskAssignee', {
              rules: [{ required: true, message: '转交对象必选！' }],
            })(
              <UserSelect
                disabled={!basicInfo.blockTag}
                blockGuid={basicInfo.blockTag}
                includeCurrentUser={false}
                placeholder="请根据用户名或id搜索"
                labelInValue
                style={{ width: 334 }}
              />
            )}
          </Form.Item>

          <Form.Item label="转交原因">
            {getFieldDecorator('reassigneeDesc', {
              rules: [
                { required: true, whitespace: true, message: '转交原因必选！' },
                {
                  max: 20,
                  message: '最多输入 20 个字符！',
                },
              ],
            })(<Input style={{ width: 334 }} allowClear />)}
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}

const mapDispatchToProps = {
  ticketTransferActionCreator: ticketTransferActionCreator,
};

export default connect(null, mapDispatchToProps)(Form.create()(Transfer));
