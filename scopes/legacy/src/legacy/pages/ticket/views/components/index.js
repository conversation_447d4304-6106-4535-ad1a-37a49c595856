import React from 'react';

import moment from 'moment';

import { StatusText } from '@manyun/dc-brain.legacy.components';
import { STATUS_MAP } from '@manyun/dc-brain.legacy.constants/statusColor';

import { SLA_UNIT_KEY_MAP, SLA_UNIT_KEY_TEXT_MAP } from '../../constants';

export function GetSlaText({ taskSla, delay, unit, effectTime, endTime }) {
  if (taskSla === undefined) {
    return '--';
  }

  let actualExecutionTime = delay;
  let sla = taskSla;
  const unitText = SLA_UNIT_KEY_TEXT_MAP[unit];
  // 将分钟转为小时
  if (unit === SLA_UNIT_KEY_MAP.HOUR) {
    actualExecutionTime = (delay / 60).toFixed(2);
    sla = (taskSla / 60).toFixed(2);
  }
  // 将分钟转为天
  if (unit === SLA_UNIT_KEY_MAP.DAY) {
    actualExecutionTime = (delay / 60 / 24).toFixed(2);
    sla = (taskSla / 60 / 24).toFixed(2);
  }

  // 有默认sla时
  if (taskSla !== 0) {
    if (delay > taskSla) {
      return (
        <span style={{ display: 'flex' }}>
          <StatusText fontSize={14} lineHeight={22} status={STATUS_MAP.ALARM}>
            {actualExecutionTime}
          </StatusText>
          <span>
            /{sla}
            {unitText}
          </span>
        </span>
      );
    }
    return `${actualExecutionTime}/${sla}${unitText}`;
  }
  const gmtTime = moment(effectTime);
  const now = endTime ? moment(new Date(endTime).getTime()) : moment(new Date().getTime());
  actualExecutionTime = now.diff(gmtTime, UNIT_MAPS[unit]);
  return `${actualExecutionTime}/0${unitText}`;
}

export default GetSlaText;

const UNIT_MAPS = {
  HOUR: 'hours',
  MINUTES: 'minutes',
  DAY: 'days',
};
