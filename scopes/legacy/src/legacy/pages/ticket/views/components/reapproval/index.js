import React from 'react';
import { Link } from 'react-router-dom';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { Button } from '@manyun/base-ui.ui.button';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';

import { generateTicketEditUrl } from '@manyun/dc-brain.legacy.utils/urls';

export const Reapproval = ({ record, type, compact }) => {
  const [configUtil] = useConfigUtil();
  const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket');
  const { dockingStation = false } = ticketScopeCommonConfigs?.entryOfPersons?.features ?? {
    dockingStation: false,
  };
  const [authorized] = useAuthorized({ checkByUserId: record.creatorId });

  return (
    <>
      {authorized && (
        <Link
          to={generateTicketEditUrl({
            ticketType: record.taskType.toLowerCase(),
            id: record.taskNo,
          })}
        >
          <Button compact={compact || false} type={type}>
            {dockingStation ? '编辑' : '重新发起'}
          </Button>
        </Link>
      )}
    </>
  );
};
