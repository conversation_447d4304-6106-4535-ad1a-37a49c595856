import React from 'react';
import { connect } from 'react-redux';

import { ApiSelect } from '@galiojs/awesome-antd';

// import { TIMEOUT_REASON_TYPE_MAP } from '../../../constants';
import * as ticketService from '@manyun/dc-brain.legacy.services/ticketService';
import { getObjectOwnProps } from '@manyun/dc-brain.legacy.utils';

export function TimeoutReasonType({ forwardedRef, ...props }) {
  return (
    <ApiSelect
      style={{ width: '100%' }}
      ref={forwardedRef}
      allowClear
      {...props}
      fieldNames={{ label: 'label', value: 'value' }}
      dataService={async () => {
        const { response } = await ticketService.fetchTimeoutType();
        if (response) {
          return Promise.resolve(getObjectOwnProps(response));
        } else {
          return Promise.resolve([]);
        }
      }}
    />
  );
}

export default connect(null, null, null, { forwardRef: true })(
  React.forwardRef((props, ref) => <TimeoutReasonType forwardedRef={ref} {...props} />)
);
