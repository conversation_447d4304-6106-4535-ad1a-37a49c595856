import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';

import * as ticketService from '@manyun/dc-brain.legacy.services/ticketService';
import { getObjectOwnProps } from '@manyun/dc-brain.legacy.utils';

const riskCheckTicketFailReason = ['EMPTY_POINT_CODE', 'EXIST_UNCHECKED_RISK_ITEM'];
export const FailureReasonType = React.forwardRef(({ ticketType, ...rest }, ref) => {
  const [options, setOptions] = React.useState([]);
  const isRiskCheckTicket = ticketType === 'RISK_CHECK';
  React.useEffect(() => {
    (async () => {
      const { response } = await ticketService.fetchFailType();
      if (response) {
        const expectedOptions = getObjectOwnProps(response);
        const options = isRiskCheckTicket
          ? expectedOptions.filter(option => riskCheckTicketFailReason.includes(option.value))
          : expectedOptions.filter(option => !riskCheckTicketFailReason.includes(option.value));
        setOptions(options);
      }
    })();
  }, [isRiskCheckTicket]);

  return <Select ref={ref} style={{ width: '100%' }} allowClear {...rest} options={options} />;
});

FailureReasonType.displayName = 'FailureReasonType';
