import React from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Modal } from '@manyun/base-ui.ui.modal';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { BackendTaskStatus } from '@manyun/ticket.model.ticket';

export const Revocation = props => {
  const { record, type, revoke, detailInfoLoading } = props;
  const [, { checkUserId }] = useAuthorized();
  let authorized = checkUserId(record.creatorId);

  if (record.taskStatus === BackendTaskStatus.CLOSE_APPROVER) {
    // 关单审批 需要审批申请人（即工单处理人）也可撤回
    const taskAssigneeAuthorized = checkUserId(record.taskAssignee);
    authorized = authorized || taskAssigneeAuthorized;
  }

  return (
    <>
      {authorized && (
        <Button
          key="revoke"
          loading={detailInfoLoading}
          compact={props.compact || false}
          type={type}
          onClick={() => {
            Modal.confirm({
              title: '确认要撤回' + record.taskNo + '的工单吗？',
              content: '撤回后数据将不可恢复。',
              okText: '确认',
              cancelText: '取消',
              onOk() {
                revoke(record.taskNo, record.taskStatus);
              },
            });
          }}
        >
          撤回
        </Button>
      )}
    </>
  );
};
