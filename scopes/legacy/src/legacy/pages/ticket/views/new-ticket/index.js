import React from 'react';
import { connect } from 'react-redux';

import { GutterWrapper, TinyEmpty } from '@manyun/dc-brain.legacy.components';

import { CURRENT_PAGE_TYPES } from '../../constants';
import TTR from '../../registries/ticket-type-registry';

const CURRENT_PAGE_TYPE = CURRENT_PAGE_TYPES.NEW_TICKET;

/**
 *
 * @typedef {Array<{ label: string; key: string }>} BasicInfo
 *
 * @typedef Config
 * @property {React.ComponentType<any>} Content
 *
 *
 * @typedef State
 * @property {Config} config
 */

/**
 * @augments {React.PureComponent<any, State>}
 */
export class NewTicket extends React.PureComponent {
  state = {
    content: null,
  };

  componentDidMount() {
    this._initialize();
  }

  _getTicketType = () => {
    return this.props.match.params.type;
  };

  _initialize = () => {
    const ticketType = this._getTicketType();
    const inst = TTR.getTicketTypeInstance(ticketType);

    if (!(inst && inst.hasConfig(CURRENT_PAGE_TYPE))) {
      return;
    }

    const config = inst.getConfig(CURRENT_PAGE_TYPE);
    this.setState({ ...config });
  };

  render() {
    const { content: Content } = this.state;

    return (
      <GutterWrapper mode="vertical">
        {Content === null ? (
          <TinyEmpty description="缺少配置！" />
        ) : (
          <Content
            ticketType={this._getTicketType()}
            history={this.props.history}
            match={this.props.match}
          />
        )}
      </GutterWrapper>
    );
  }
}

const mapDispatchToProps = {};

export default connect(null, mapDispatchToProps)(NewTicket);
