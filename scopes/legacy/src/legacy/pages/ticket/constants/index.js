// export const TICKET_TYPE_WEB_JAVA_MAP = {
//    inspection: 'INSPECTION',
//   warehouse: 'WAREHOUSE',
//   repair: 'REPAIR',
//   inventory: 'INVENTORY',
//   power: 'POWER',
// };

export const CURRENT_PAGE_TYPES = {
  TICKETS: 'tickets',
  SPECIFIC_TICKET: 'specific-ticket',
  NEW_TICKET: 'new-ticket',
};

export const TICKET_IS_DELAY_KEY_TEXT_MAP = {
  1: '是',
  0: '否',
};
/** @deprecated Replaced by model/ticket */

export const TICKET_STATUS_KEY_MAP = {
  INIT: '4',
  WAITTAKEOVER: '3',
  FINISH: '1',
  PROCESSING: '2',
  FAILURE: '0',
  UNDO: '5',
  CLOSE_APPROVER: '6',
};
/** @deprecated Replaced by model/ticket */
export const TICKET_STATUS_KEY_TEXT_MAP = {
  [TICKET_STATUS_KEY_MAP.INIT]: '建单审批中',
  [TICKET_STATUS_KEY_MAP.WAITTAKEOVER]: '待接单',
  [TICKET_STATUS_KEY_MAP.FINISH]: '已关单',
  [TICKET_STATUS_KEY_MAP.PROCESSING]: '处理中',
  [TICKET_STATUS_KEY_MAP.FAILURE]: '失败',
  [TICKET_STATUS_KEY_MAP.UNDO]: '撤回',
  [TICKET_STATUS_KEY_MAP.CLOSE_APPROVER]: '关单审批中',
};
/** @deprecated Replaced by model/ticket */

export const TICKET_LIST_FILTER_STATUS_LIST = [
  {
    key: TICKET_STATUS_KEY_MAP.WAITTAKEOVER,
    label: '待接单',
  },
  {
    key: TICKET_STATUS_KEY_MAP.PROCESSING,
    label: '处理中',
  },
  {
    key: TICKET_STATUS_KEY_MAP.FINISH,
    label: '已关单',
  },
  {
    key: TICKET_STATUS_KEY_MAP.FAILURE,
    label: '失败',
  },
];

export const SLA_UNIT_KEY_MAP = {
  HOUR: 'HOUR',
  MINUTES: 'MINUTES',
  DAY: 'DAY',
};

export const SLA_UNIT_KEY_TEXT_MAP = {
  [SLA_UNIT_KEY_MAP.HOUR]: '小时',
  [SLA_UNIT_KEY_MAP.MINUTES]: '分钟',
  [SLA_UNIT_KEY_MAP.DAY]: '天',
};

export const TICKET_STEP_STATUS_KEY_MAP = {
  [TICKET_STATUS_KEY_MAP.INIT]: 0,
  [TICKET_STATUS_KEY_MAP.WAITTAKEOVER]: 1,
  [TICKET_STATUS_KEY_MAP.PROCESSING]: 2,
  [TICKET_STATUS_KEY_MAP.FINISH]: 10, // step结点需要超过3
  [TICKET_STATUS_KEY_MAP.FAILURE]: 4,
  [TICKET_STATUS_KEY_MAP.UNDO]: 0, // 建单审批撤销节点展示在第一步
  [TICKET_STATUS_KEY_MAP.CLOSE_APPROVER]: 3, // 关单审批时步骤节点展示在第四步
};

export const NEED_TO_SHOW_TYPE_ROOM_FILTER = [];

export const TIMEOUT_REASON_TYPE_MAP = {
  FAULT_HANDLING: '故障处理',
  DELAYED_STATEMENT: '未及时关单',
};

export const FAILURE_REASON_TYPE_MAP = {
  CREATE_ERROR: '工单误创建',
};

export const PATROL_CHECK_ITEM_TYPE = {
  SYSTEM_POINT: 'SYSTEM_POINT',
  CUSTOMIZE: 'CUSTOMIZE',
};

export const CHECK_SUBJECT_KEY_MAPS = {
  EVENT: 'EVENT',
  REPAIR: 'REPAIR',
  RISK: 'RISK',
  NOT_PROCESSED: 'NOT_PROCESSED',
};

export const CHECK_SUBJECT_NAME = {
  [CHECK_SUBJECT_KEY_MAPS.EVENT]: '目标名称',
  [CHECK_SUBJECT_KEY_MAPS.REPAIR]: '目标名称',
  [CHECK_SUBJECT_KEY_MAPS.RISK]: '风险对象',
};

export const TYPE_NAME_MAPS = {
  [CHECK_SUBJECT_KEY_MAPS.EVENT]: '事件类型',
  [CHECK_SUBJECT_KEY_MAPS.REPAIR]: '目标类型',
  [CHECK_SUBJECT_KEY_MAPS.RISK]: '风险类型',
};
