import React, { Component } from 'react';
import { connect } from 'react-redux';

import { FiltersForm, Form, Select } from '@galiojs/awesome-antd';
import moment from 'moment';
import styled from 'styled-components';

import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { Input } from '@manyun/base-ui.ui.input';

import { exportAlarmTransmissionRecords } from '@manyun/monitoring.service.export-alarm-transmission-records';
import { AlarmLevelSelect } from '@manyun/monitoring.ui.alarm-level-select';
import { AlarmLevelText } from '@manyun/monitoring.ui.alarm-level-text';

import {
  GutterWrapper,
  LocationCascader,
  TinyCard,
  TinyTable,
} from '@manyun/dc-brain.legacy.components';
import { ALARM_TRANSMISSION_RECORD_STATUS_MAPPINGS } from '@manyun/dc-brain.legacy.constants/alarm';
import {
  alarmTransmissionRecordActions,
  getListAction,
  resetValuesAction,
  setPaginationAction,
} from '@manyun/dc-brain.legacy.redux/actions/alarmTransmissionRecordActions';

const getColumns = () => [
  {
    title: '告警ID',
    dataIndex: 'alarmId',
    width: 150,
  },
  {
    title: '级别',
    dataIndex: 'alarmLevel',
    width: 80,
    render: level => <AlarmLevelText code={level} />,
  },
  {
    title: '位置',
    dataIndex: 'block',
    // render: (_, { idc, block }) => `${idc}.${block}`,
    width: 120,
  },
  {
    title: '设备名称',
    dataIndex: 'deviceName',
    width: 180,
  },
  {
    title: '测点名称',
    dataIndex: 'pointName',
    width: 180,
  },
  {
    title: '告警值',
    dataIndex: 'alarmValue',
    width: 180,
  },
  {
    title: '告警状态',
    dataIndex: 'alarmStatus',
    width: 120,
    render: text => ALARM_TRANSMISSION_RECORD_STATUS_MAPPINGS?.[text],
  },
  {
    title: '告警内容',
    dataIndex: 'alarmContent',
    ellipsis: true,
  },
  {
    title: '告警时间',
    dataIndex: 'alarmTime',
    dataType: 'datetime',
    width: 180,
  },
];

class AlarmTransmissionRecord extends Component {
  handleSearch = () => {
    this.props.setPaginationAction({ pageNum: 1, pageSize: 10 });
  };

  render() {
    const {
      alarmTransmissionRecordOrderList,
      updateSearchValues,
      fields,
      pagination,
      loading,
      setPaginationAction,
      resetValuesAction,
    } = this.props;

    return (
      <BasicAlarmTransmissionRecord
        alarmTransmissionRecordOrderList={alarmTransmissionRecordOrderList}
        updateSearchValues={updateSearchValues}
        fields={fields}
        pagination={pagination}
        setPaginationAction={setPaginationAction}
        loading={loading}
        resetValuesAction={resetValuesAction}
        handleSearch={this.handleSearch}
        getList={this.props.getListAction}
      />
    );
  }
}

const mapStateToProps = ({
  alarmTransmissionRecord: { alarmTransmissionRecordOrderList, loading, searchValues, pagination },
}) => ({
  alarmTransmissionRecordOrderList: alarmTransmissionRecordOrderList,
  loading,
  pagination,
  fields: searchValues,
});
const mapDispatchToProps = {
  updateSearchValues: alarmTransmissionRecordActions.updateSearchValues,
  setPaginationAction,
  getListAction,
  resetValuesAction,
};

export default connect(mapStateToProps, mapDispatchToProps)(AlarmTransmissionRecord);

function BasicAlarmTransmissionRecord({
  alarmTransmissionRecordOrderList,
  updateSearchValues,
  fields,
  pagination,
  loading,
  setPaginationAction,
  resetValuesAction,
  handleSearch,
  getList,
}) {
  const [form] = Form.useForm();
  const timeIntervalRef = React.useRef();

  React.useEffect(() => {
    const currentInterval = window.setInterval(getList, 1000);
    timeIntervalRef.current = currentInterval;

    return () => {
      window.clearInterval(currentInterval);
    };
  }, [getList]);

  const columns = getColumns();

  const handleFileExport = async type => {
    const [alarmStartTime, alarmEndTime] = fields.alarmTime.value.map(m => m.valueOf());
    if (type === 'all') {
      const params = {
        alarmStartTime,
        alarmEndTime,
      };
      const data = await exportAlarmTransmissionRecords(params);
      return data.data;
    } else {
      const params = {
        alarmStartTime,
        alarmEndTime,
        alarmLevel: fields.alarmLevel?.value,
        alarmStatus: fields.alarmStatus?.value,
        block: fields.location?.value?.[1],
        deviceName: fields.deviceName?.value,
        idc: fields.location?.value?.[0],
        pointName: fields.pointName?.value,
      };
      const data = await exportAlarmTransmissionRecords(params);
      return data.data;
    }
  };

  return (
    <GutterWrapper mode="vertical">
      <TinyCard>
        <StyledFiltersFormDiv>
          <FiltersForm
            form={form}
            fields={Object.keys(fields).map(name => {
              const field = fields[name];

              return {
                ...field,
                name: name.split('.'),
              };
            })}
            initialValues={{
              alarmTime: fields.alarmTime.value,
            }}
            items={[
              {
                label: '告警时间',
                name: 'alarmTime',
                span: 2,
                control: (
                  <DatePicker.RangePicker
                    disabledDate={current => {
                      return current < moment().subtract(1, 'years').valueOf();
                    }}
                    showTime
                    allowClear={false}
                    format="YYYY-MM-DD HH:mm:ss"
                  />
                ),
              },
              {
                label: '告警级别',
                name: 'alarmLevel',
                control: (
                  <AlarmLevelSelect style={{ width: 200 }} maxTagCount={1} allowClear showSearch />
                ),
              },
              {
                label: '位置',
                name: 'location',
                control: <LocationCascader />,
              },
              {
                label: '设备名称',
                name: 'deviceName',
                control: <Input allowClear />,
              },
              {
                label: '测点名称',
                name: 'pointName',
                control: <Input allowClear />,
              },
              {
                label: '告警状态',
                name: 'alarmStatus',
                control: (
                  <Select allowClear>
                    {Object.entries(ALARM_TRANSMISSION_RECORD_STATUS_MAPPINGS).map(item => {
                      return (
                        <Select.Option key={item[0]} value={item[0]}>
                          {item[1]}
                        </Select.Option>
                      );
                    })}
                  </Select>
                ),
              },
            ]}
            onFieldsChange={changedFields => {
              updateSearchValues(
                changedFields.reduce((mapper, field) => {
                  const name = field.name.join('.');
                  mapper[name] = {
                    ...field,
                    name,
                  };

                  return mapper;
                }, {})
              );
            }}
            onSearch={handleSearch}
            onReset={resetValuesAction}
          />
        </StyledFiltersFormDiv>
      </TinyCard>
      <TinyCard>
        <TinyTable
          rowKey="id"
          columns={columns}
          align="left"
          tableLayout="fixed"
          dataSource={alarmTransmissionRecordOrderList.list}
          // loading={loading}
          pagination={{
            total: alarmTransmissionRecordOrderList.total,
            current: pagination.pageNum,
            pageSize: pagination.pageSize,
            onChange: (current, size) => setPaginationAction({ pageNum: current, pageSize: size }),
          }}
          actions={[
            <FileExport
              text="导出"
              filename="告警传输记录.xls"
              disabled={!alarmTransmissionRecordOrderList.total}
              showExportFiltered
              data={type => {
                return handleFileExport(type);
              }}
            />,
          ]}
        />
      </TinyCard>
    </GutterWrapper>
  );
}

const StyledFiltersFormDiv = styled.div`
  .manyun-btn-primary {
    display: none;
  }
`;
