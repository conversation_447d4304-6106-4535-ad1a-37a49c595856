import React from 'react';

import { Select } from '@galiojs/awesome-antd';

import { CHANGE_EMERGENCY_LEVEL_OPTIONS } from './../../constants';

/**
 * Emergency-level select.
 * @param {import('antd-3/lib/select').SelectProps} props
 */
export function EmergencyLevelSelect({ forwardedRef, ...props }) {
  return (
    <Select ref={forwardedRef} {...props} allowClear>
      {CHANGE_EMERGENCY_LEVEL_OPTIONS.map(({ label, value }) => (
        <Select.Option key={value} value={value}>
          {label}
        </Select.Option>
      ))}
    </Select>
  );
}

export default React.forwardRef((props, ref) => (
  <EmergencyLevelSelect forwardedRef={ref} {...props} />
));
