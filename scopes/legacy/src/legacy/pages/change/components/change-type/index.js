import React from 'react';

import { Select } from '@galiojs/awesome-antd';

import { CHANGE_RISK_LEVEL_OPTIONS } from '@manyun/ticket.model.change';

/**
 * Risk-level select.
 * @param {import('antd-3/lib/select').SelectProps} props
 */
export function RiskLevelSelect({ forwardedRef, ...props }) {
  return (
    <Select ref={forwardedRef} {...props}>
      {CHANGE_RISK_LEVEL_OPTIONS.map(({ label, value }) => (
        <Select.Option key={value} value={value}>
          {label}
        </Select.Option>
      ))}
    </Select>
  );
}

export default React.forwardRef((props, ref) => <RiskLevelSelect forwardedRef={ref} {...props} />);
