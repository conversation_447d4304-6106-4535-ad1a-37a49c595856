import { ApiSelect } from '@galiojs/awesome-antd';
import React from 'react';
import { connect } from 'react-redux';

import { MetaType } from '@manyun/resource-hub.model.metadata';
import { fetchMetaDataByTypeWeb } from '@manyun/resource-hub.service.fetch-meta-data-by-type';

export function ChangeTypesTreeSelect({ forwardedRef, labelInValue, ...props }) {
  return (
    <ApiSelect
      style={{ width: '100%' }}
      ref={forwardedRef}
      trigger="onDidMount"
      labelInValue={labelInValue}
      dataService={async () => {
        const { data } = await fetchMetaDataByTypeWeb({ type: MetaType['CHANGE'] });
        if (data) {
          return data.data;
        }
      }}
      allowClear
      fieldNames={{ label: 'name', value: 'code' }}
      {...props}
    />
  );
}

export default connect(null, null, null, { forwardRef: true })(
  React.forwardRef((props, ref) => <ChangeTypesTreeSelect forwardedRef={ref} {...props} />)
);
