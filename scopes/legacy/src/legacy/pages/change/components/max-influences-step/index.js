import React from 'react';

import { Select } from '@galiojs/awesome-antd';

import { CHANGE_STEP_TYPE_KEY_MAP } from '../../constants';

/**
 *
 * @typedef Props
 * @property {Array} [stepCodes=[]]
 * @property {Number} [stepIndex=0]
 */

/**
 * @param {Props} props
 */
export function MaxInfluencesStepSelect({
  forwardedRef,
  stepCodes,
  stepIndex,
  stepMaps,
  ...props
}) {
  const filterStepCodes = stepCodes.slice(stepIndex);
  return (
    <Select ref={forwardedRef} style={{ width: 200 }} allowClear {...props}>
      {filterStepCodes.map(item => {
        if (stepMaps[item].stepType !== CHANGE_STEP_TYPE_KEY_MAP.RUN) {
          return <Select.Option key={item}>{stepMaps[item].stepName}</Select.Option>;
        } else {
          return null;
        }
      })}
    </Select>
  );
}

export default React.forwardRef((props, ref) => (
  <MaxInfluencesStepSelect forwardedRef={ref} {...props} />
));
