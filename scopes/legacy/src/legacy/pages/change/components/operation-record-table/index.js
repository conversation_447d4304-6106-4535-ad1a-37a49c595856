import React, { useEffect, useRef, useState } from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';

import { Ellipsis, TinyTable, UserLink } from '@manyun/dc-brain.legacy.components';
import { fetchTemplateOperationRecord } from '@manyun/dc-brain.legacy.services/changeService';
import { serialNumberInReverse } from '@manyun/dc-brain.legacy.utils';

import styles from './operation-record-table.module.less';

const columns = (opreation, containerRef) => [
  {
    title: '序号',
    dataIndex: 'id',
    // dataType: {
    //   type: 'link',
    //   options: {
    //     to(text) {
    //       return { pathname: generateEventCenterDetail({ id: text }) };
    //     },
    //   },
    // },
    render: (_, __, index) =>
      serialNumberInReverse({
        total: opreation.total,
        pageNum: opreation.pageNum,
        pageSize: opreation.pageSize,
        index,
      }),
  },
  {
    title: '操作类型',
    dataIndex: ['modifyType', 'name'],
  },
  {
    title: '操作时间',
    dataIndex: 'operatorTime',
    dataType: 'dateTime',
  },
  {
    title: '操作内容',
    dataIndex: 'operationContent',
    // width: 300,
    className: 'max-width-content',
    render: text => {
      if (!text) {
        return;
      }
      return (
        <Tooltip
          title={
            <div>
              {text.map(item => (
                <div key={item} style={{ display: 'block' }}>
                  {item}
                </div>
              ))}
            </div>
          }
          overlayStyle={{ minWidth: 150, maxWidth: 600 }}
          getPopupContainer={() => containerRef.current}
        >
          <Ellipsis
            lines={1}
            // tooltip
          >
            {text[0]}
          </Ellipsis>
        </Tooltip>
      );
    },
  },
  {
    title: '修改人',
    width: 80,
    dataIndex: 'modifyByName',
    render: (text, { modifyBy }) => <UserLink userName={text} userId={modifyBy} />,
  },
];

export function OperationRecord({ id, targetType }) {
  const [opreation, setOpreation] = useState({
    operationList: [],
    pageNum: 1,
    pageSize: 10,
    total: 0,
  });
  const containerRef = useRef(null);

  useEffect(() => {
    getList(1, 10);
    //  eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const getList = async (pageNum, pageSize) => {
    const { response, error } = await fetchTemplateOperationRecord({
      targetId: id,
      pageNum,
      pageSize,
      targetType,
    });

    if (error) {
      message.error(error || '操作记录请求失败');
    } else {
      setOpreation({
        operationList: response.data,
        pageNum,
        pageSize,
        total: response.total,
      });
    }
  };

  const onChangePage = (pageNum, pageSize) => {
    getList(pageNum, pageSize);
  };
  return (
    <div ref={containerRef} className={styles.operationRecordTableStyle}>
      <TinyTable
        rowKey="id"
        dataSource={opreation.operationList}
        columns={columns(opreation, containerRef)}
        pagination={{
          total: opreation.total,
          current: opreation.pageNum,
          pageSize: opreation.pageSize,
          onChange: onChangePage,
        }}
      />
    </div>
  );
}
