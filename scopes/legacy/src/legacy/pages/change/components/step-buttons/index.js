import React from 'react';

import { Button } from '@manyun/base-ui.ui.button';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';

/**
 * @typedef {React.MouseEventHandler<HTMLElement>} Click
 *
 * @typedef Props
 * @property {boolean} [showPrev=false]
 * @property {boolean} [showNext=true]
 * @property {boolean} [showSubmit=false]
 * @property {boolean} [showSave=true]
 * @property {Click} onPrev
 * @property {Click} onNext
 * @property {Click} onSubmit
 * @property {Click} onSave
 */

/**
 * @param {Props} props
 */
export function StepButtons({
  showPrev = false,
  showNext = true,
  showSubmit = false,
  showSave = true,
  onPrev,
  onNext,
  onSubmit,
  onSave,
  saveLoading = false,
  submitLoading = false,
}) {
  return (
    <GutterWrapper>
      {showPrev && (
        <Button type="primary" onClick={onPrev}>
          上一步
        </Button>
      )}
      {showNext && (
        <Button type="primary" onClick={onNext}>
          下一步
        </Button>
      )}
      {showSubmit && (
        <Button type="primary" onClick={onSubmit} loading={submitLoading}>
          提交
        </Button>
      )}
      {showSave && (
        <Button type="primary" onClick={onSave} loading={saveLoading}>
          保存
        </Button>
      )}
    </GutterWrapper>
  );
}

export default StepButtons;
