import { ApiSelect } from '@galiojs/awesome-antd';
import React from 'react';

import { MetaType } from '@manyun/resource-hub.model.metadata';
import { fetchMetaDataByTypeWeb } from '@manyun/resource-hub.service.fetch-meta-data-by-type';

export function ChangeReasonsSelect({ forwardedRef, ...props }) {
  return (
    <ApiSelect
      ref={forwardedRef}
      style={{ width: '100%' }}
      showSearch
      trigger="onDidMount"
      labelInValue={true}
      dataService={async () => {
        const { data } = await fetchMetaDataByTypeWeb({ type: MetaType['CHANGE_REASON'] });
        if (data) {
          return data.data;
        }
      }}
      allowClear
      fieldNames={{ label: 'name', value: 'code' }}
      {...props}
    />
  );
}

export default React.forwardRef((props, ref) => (
  <ChangeReasonsSelect forwardedRef={ref} {...props} />
));
