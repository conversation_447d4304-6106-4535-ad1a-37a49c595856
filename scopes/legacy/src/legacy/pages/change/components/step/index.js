import React from 'react';
import { DragDropContext, Draggable, Droppable } from 'react-beautiful-dnd';

import styles from '../../styles.module.less';

export function Step({ steps, stepCodes, setStepCodes, type }) {
  return (
    <DragDropContext
      onDragEnd={result => {
        const { source, destination } = result;
        if (!destination) {
          return;
        }
        const codes = [...stepCodes];
        const [remove] = codes.splice(source.index, 1);
        codes.splice(destination.index, 0, remove);
        setStepCodes({ stepCodes: codes });
      }}
    >
      <Droppable
        droppableId="stepMap"
        isDropDisabled={type === 'ticketView' || type === 'newByTemplate'}
      >
        {provided => (
          <div ref={provided.innerRef} {...provided.droppableProps}>
            {steps.map((item, index) => (
              <Draggable
                isDragDisabled={type === 'ticketView' || type === 'newByTemplate'}
                key={item.id}
                draggableId={item.id}
                index={index}
              >
                {p => (
                  <div
                    ref={p.innerRef}
                    {...p.draggableProps}
                    {...p.dragHandleProps}
                    // style={{
                    //   height: 'auto',
                    //   display: 'flex',
                    //   justifyContent: 'flex-start',
                    // }}
                    key={`${item.title}.${index}`}
                  >
                    <div
                      ref={p.innerRef}
                      {...p.draggableProps}
                      {...p.dragHandleProps}
                      style={{
                        height: 'auto',
                        display: 'flex',
                        justifyContent: 'flex-start',
                      }}
                      key={`${item.title}.${index}`}
                    >
                      <div
                        style={{
                          position: 'relative',
                          display: 'flex',
                          // width: '196px',
                          // color: 'var(--color-grey-1)',
                        }}
                      >
                        <div style={{ display: 'flex' }}>
                          <div
                            style={{
                              border: '1px solid',
                              width: 32,
                              height: 32,
                              fontSize: 16,
                              lineHeight: '32px',
                              textAlign: 'center',
                              borderRadius: 32,
                            }}
                            className={styles.border}
                          >
                            {index + 1}
                          </div>
                          <div
                            style={{
                              width: 134,
                              padding: '6px 16px 0 8px',
                              wordBreak: 'break-word',
                              fontSize: 14,
                            }}
                          >
                            {item.title}
                          </div>
                        </div>
                        {index + 1 !== steps.length && (
                          <div
                            style={{
                              display: 'inline-block',
                              position: 'absolute',
                              bottom: 12,
                              left: 16,
                              width: 1,
                              height: `calc(100% - 32px - 12px - ${
                                item.title.replace(/[^x00-xff]/g, 'aa').length / 14 > 1
                                  ? Math.ceil(item.title.replace(/[^x00-xff]/g, 'aa').length / 14) *
                                    20
                                  : 20
                              }px)`,
                            }}
                            className={styles.background}
                          ></div>
                        )}
                      </div>

                      <div
                        style={{
                          width: 'calc(100% - 158px)',
                          paddingBottom: index + 1 === steps.length ? 0 : 63,
                        }}
                      >
                        {item.children}
                      </div>
                    </div>
                  </div>
                )}
              </Draggable>
            ))}
            {provided.placeholder}
          </div>
        )}
      </Droppable>
    </DragDropContext>
  );
}

export default Step;
