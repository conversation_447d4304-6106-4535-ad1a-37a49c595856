import React from 'react';

import Form from '@ant-design/compatible/es/form';
import { Select } from '@galiojs/awesome-antd';

import { Col } from '@manyun/base-ui.ui.grid';
import { InputNumber } from '@manyun/base-ui.ui.input-number';

import { generateValidLimitsDataSource, getDiPointValueText } from '@manyun/monitoring.model.point';

import {
  CHANGE_EXPECTATION_METHOD_KEY_MAP,
  CHANGE_EXPECTATION_METHOD_TEXT_MAP,
  operatorList,
} from '../../constants';

export function RiskLevelSelect({
  info,
  onChangeJudgmentWay,
  type,
  form,
  getFieldDecorator,
  dataType,
}) {
  const judgmentWay = () => {
    let value;
    if (Array.isArray(info.operatorList) && info.operatorList.length) {
      if (
        (info.dataType === 'AI' && info.operatorList.length === 1) ||
        (info.dataType === 'DI' && info.expectedValue !== null && info.expectedValue !== undefined)
      ) {
        value = CHANGE_EXPECTATION_METHOD_KEY_MAP.SINGLE_VALUE;
      } else {
        value = CHANGE_EXPECTATION_METHOD_KEY_MAP.LIMIT_OF_ESTIMATION;
      }
    }

    return value;
  };

  let valid = info.pointValueText ? info.pointValueText.split(',') : [];
  // AI测点如果没有工作区间，限制在千位级
  let min = -9999.9999;
  let max = 9999.9999;
  if (valid.length > 0) {
    min = Number(getDiPointValueText('ge', valid));
    max = Number(getDiPointValueText('le', valid));
  }
  return (
    <>
      {type === 'template' && (
        <Col span={24}>
          <Form.Item
            label="预期判断方式"
            labelCol={{
              xs: { span: 24 },
              sm: { span: 6 },
            }}
            wrapperCol={{
              xs: { span: 24 },
              sm: { span: 18 },
            }}
          >
            {getFieldDecorator('judgmentWay', {
              rules: [
                {
                  required: true,
                  message: '预期判断方式必填！',
                },
              ],
              initialValue: judgmentWay(),
              // Array.isArray(info.operatorList) && info.operatorList.length
              //   ? (info.expectedValue !== null || info.expectedValue !== undefined) &&
              //     info.operatorList.length === 1 &&info.dataType ==='AI'
              //     ? CHANGE_EXPECTATION_METHOD_KEY_MAP.SINGLE_VALUE
              //     : CHANGE_EXPECTATION_METHOD_KEY_MAP.LIMIT_OF_ESTIMATION
              //   : undefined,
            })(
              <Select style={{ width: 200 }} onChange={onChangeJudgmentWay}>
                {Object.entries(CHANGE_EXPECTATION_METHOD_TEXT_MAP).map(item => (
                  <Select.Option key={item[0]}>{item[1]}</Select.Option>
                ))}
              </Select>
            )}
          </Form.Item>
        </Col>
      )}
      {/* 模板 现场判断  AI量时 */}
      {type === 'template' &&
        form.getFieldValue('judgmentWay') ===
          CHANGE_EXPECTATION_METHOD_KEY_MAP.LIMIT_OF_ESTIMATION &&
        (dataType === 'AI' || info.dataType === 'AI') && (
          <Col span={24}>
            <Form.Item label="预期判断">
              {getFieldDecorator('judgment', {
                rules: [
                  {
                    required: true,
                    message: '预期判断必填！',
                  },
                ],
                initialValue:
                  Array.isArray(info.operatorList) &&
                  info.operatorList.length &&
                  info.operatorList.length > 1
                    ? info.operatorList.includes('lt')
                      ? 'up'
                      : 'down'
                    : undefined,
              })(
                <Select style={{ width: 200 }}>
                  <Select.Option key="up">上升</Select.Option>
                  <Select.Option key="down">下降</Select.Option>
                </Select>
              )}
            </Form.Item>
          </Col>
        )}
      {/* 模板 统一设定 或新建 AI量*/}
      {((type === 'template' &&
        form.getFieldValue('judgmentWay') === CHANGE_EXPECTATION_METHOD_KEY_MAP.SINGLE_VALUE) ||
        type === 'new' ||
        type === 'edit' ||
        type === 'newByTemplate') &&
        (dataType === 'AI' || info.dataType === 'AI') && (
          <Col span={12}>
            <Form.Item
              label="预判值"
              labelCol={{
                xs: { span: 24 },
                sm: { span: 12 },
              }}
              wrapperCol={{
                xs: { span: 24 },
                sm: { span: 12 },
              }}
            >
              {getFieldDecorator('operatorList', {
                rules: [
                  {
                    required: true,
                    message: '预判表达式必填！',
                  },
                ],
                initialValue:
                  info.operatorList && info.operatorList.length && info.operatorList.length === 1
                    ? info.operatorList.join(',')
                    : undefined,
              })(
                <Select style={{ width: '100%' }}>
                  {operatorList.map(item => (
                    <Select.Option key={item.value} value={item.value}>
                      {item.label}
                    </Select.Option>
                  ))}
                </Select>
              )}
            </Form.Item>
          </Col>
        )}
      {((type === 'template' &&
        form.getFieldValue('judgmentWay') === CHANGE_EXPECTATION_METHOD_KEY_MAP.SINGLE_VALUE) ||
        type === 'new' ||
        type === 'edit' ||
        type === 'newByTemplate') &&
        (dataType === 'AI' || info.dataType === 'AI') && (
          <Col span={6}>
            <Form.Item
              label=" "
              labelCol={{
                xs: { span: 24 },
                sm: { span: 2 },
              }}
              wrapperCol={{
                xs: { span: 24 },
                sm: { span: 22 },
              }}
            >
              {getFieldDecorator('expectedValue', {
                rules: [
                  {
                    required: true,
                    message: '预判范围必填！',
                  },
                ],
                initialValue: info.expectedValue,
              })(
                <InputNumber
                  formatter={value => (info.unit ? `${value}${info.unit}` : `${value}`)}
                  parser={value => {
                    let number = value;
                    if (!info.unit) {
                      return `${value}`;
                    }
                    Array.from(info.unit).forEach(char => {
                      number = number.replace(char, '');
                    });
                    return number;
                  }}
                  min={min}
                  max={max}
                  precision={4}
                  style={{ width: '100%' }}
                />
              )}
            </Form.Item>
          </Col>
        )}
      {/* 模板统一设定 或新建 时 DI量*/}
      {((type === 'template' &&
        form.getFieldValue('judgmentWay') === CHANGE_EXPECTATION_METHOD_KEY_MAP.SINGLE_VALUE) ||
        type === 'new' ||
        type === 'edit' ||
        type === 'newByTemplate') &&
        (dataType === 'DI' || info.dataType === 'DI') && (
          <Col span={24}>
            <Form.Item label="预判值">
              {getFieldDecorator('expectedValue', {
                rules: [
                  {
                    required: true,
                    message: '预判值必填！',
                  },
                ],
                initialValue: Array.isArray(info.expectedValue)
                  ? info.expectedValue.map(item => Number(item))
                  : [],
              })(
                <Select style={{ width: 200 }} mode="multiple">
                  {generateValidLimitsDataSource(
                    info.pointValueText ? info.pointValueText.split(',') : []
                  ).map(item => (
                    <Select.Option value={Number(item.value)} key={Number(item.value)}>
                      {item.label}
                    </Select.Option>
                  ))}
                </Select>
              )}
            </Form.Item>
          </Col>
        )}
    </>
  );
}

export default React.forwardRef((props, ref) => <RiskLevelSelect forwardedRef={ref} {...props} />);
