import React, { useState } from 'react';
import { connect } from 'react-redux';

import { TreeSelect } from '@manyun/base-ui.ui.tree-select';

import { fetchPointsByCondition } from '@manyun/resource-hub.service.fetch-points-by-condition';

import { infrastructureService } from '@manyun/dc-brain.legacy.services';

// import { fetchQueryIdcByPermission } from '@manyun/dc-brain.legacy.services/eventCenterService';

export function PointCascader({ pointData, disabledPointCode, forwardedRef, ...props }) {
  const [pointTreeData, setPointTreeData] = useState([]);

  const getPointSelectDataService = async init => {
    const { data } = await fetchPointsByCondition({
      deviceType: pointData.deviceType,
      dataTypeList: ['AI', 'DI'],
      isRemoveSub: true,
    });
    if (data) {
      const datas = data.data.map(point => point.toApiObject());
      const result = datas.map(item => {
        const metaCode = `${item.deviceType}_$$_${item.pointCode}`;

        if (disabledPointCode.includes(metaCode)) {
          return {
            ...item,
            value: item.pointCode,
            title: item.name,
            metaCode,
            isLeaf: item.extCount ? false : true,
            disabled: true,
            id: metaCode,
            pId: 0,
            pointName: item.name,
          };
        } else {
          return {
            ...item,
            value: item.pointCode,
            title: item.name,
            metaCode,
            isLeaf: item.extCount ? false : true,
            disabled: item.extCount ? true : false,
            id: metaCode,
            pId: 0,
            pointName: item.name,
          };
        }
      });
      setPointTreeData(result);
    }
  };

  const genTreeNode = (parentId, response, isLeaf = false) => {
    return response.map(item => {
      const metaCode = `${item.deviceType}_$$_${item.pointCode}`;
      if (disabledPointCode.includes(metaCode)) {
        return {
          ...item,
          value: item.pointCode,
          title: item.name,
          metaCode,
          isLeaf: item.extCount ? false : true,
          disabled: true,
          id: metaCode,
          pId: 0,
          pointName: item.name,
        };
      }
      return {
        ...item,
        pId: parentId,
        metaCode,
        isLeaf,
        value: item.pointCode,
        title: item.name,
        pointName: item.name,
      };
    });
  };

  const onLoadData = async treeNode => {
    const { response } = await infrastructureService.fetchExtendedPoints({
      deviceType: pointData.deviceType,
      pointCode: treeNode.props.pointCode,
    });
    if (response) {
      const { metaCode } = treeNode.props;
      setPointTreeData(pointTreeData.concat(genTreeNode(metaCode, response.data, true)));
    }
  };
  return (
    <TreeSelect
      style={{ width: '100%' }}
      listHeight={400}
      treeData={pointTreeData}
      loadData={onLoadData}
      onFocus={getPointSelectDataService}
      treeDataSimpleMode
      ref={forwardedRef}
      {...props}
    />
  );
}

export default connect(null, null, null, { forwardRef: true })(
  React.forwardRef((props, ref) => <PointCascader forwardedRef={ref} {...props} />)
);
