import React from 'react';

import { Select } from '@galiojs/awesome-antd';

import { CHANGE_TICKET_STATUS_OPTIONS } from './../../constants';

/**
 * Emergency-level select.
 * @param {import('antd-3/lib/select').SelectProps} props
 */
export function TicketStatusSelect({ forwardedRef, ...props }) {
  return (
    <Select showArrow ref={forwardedRef} {...props} allowClear>
      {CHANGE_TICKET_STATUS_OPTIONS.map(({ label, value }) => (
        <Select.Option key={value} value={value}>
          {label}
        </Select.Option>
      ))}
    </Select>
  );
}

export default React.forwardRef((props, ref) => (
  <TicketStatusSelect forwardedRef={ref} {...props} />
));
