import { normalize, schema } from 'normalizr';

import { generateValidLimitsDataSource } from '@manyun/monitoring.model.point';

export function checkItemNormalized(checkItems, name) {
  const dataCenterCitySchema = [
    new schema.Entity(
      'normalizedList',
      {},
      {
        idAttribute: name,
        processStrategy: data => {
          return [
            {
              ...data,
            },
          ];
        },
        mergeStrategy: (entityA, entityB) => {
          return [...entityA, ...entityB];
        },
      }
    ),
  ];
  const normalizedList = normalize(checkItems, dataCenterCitySchema).entities.normalizedList;
  return normalizedList;
}

export function getMaxInfluencesStep(stepMaps, stepOrder) {
  if (!stepOrder) {
    return null;
  }
  let stepName = null;
  for (let i in stepMaps) {
    if (i === stepOrder) {
      stepName = stepMaps[i].stepName;
    }
  }
  return stepName;
}

export function generateValidLimitsDataSourceLabel(pointValueText, expectedValue) {
  let text = '';
  generateValidLimitsDataSource(pointValueText.split(',')).forEach(validLimitItem => {
    if (String(validLimitItem.value) === String(expectedValue)) {
      text = validLimitItem.label;
    }
  });
  return text;
}
