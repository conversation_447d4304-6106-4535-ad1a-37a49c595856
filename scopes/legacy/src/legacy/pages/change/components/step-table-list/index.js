import Form from '@ant-design/compatible/es/form';
import DeleteOutlined from '@ant-design/icons/es/icons/DeleteOutlined';
import { get, omit } from 'lodash';
import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Space } from '@manyun/base-ui.ui.space';
import { McUpload } from '@manyun/dc-brain.ui.upload';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';

import { CHANGE_CHECK_METHOD_KEY_MAP, CHANGE_STEP_TYPE_KEY_MAP } from '../../constants/index';
import Steps from '../step';
import AddRunTarget from './add-run-target';
import AddStepButton from './add-step-button';
import BatchSettingModal from './batch-setting-modal';
import EditStepModal from './components/add-edit-step-modal';
import Custom from './components/influences-device-custom';
import MatchingFacilitiesDevice from './components/matching-facilities/device';
import MatchingFacilitiesRoom from './components/matching-facilities/room';
import { StyledFormItem } from './components/styled';
import CustomerTable from './components/table-step/table-step-customer';
import DevicesTypeTable from './components/table-step/table-step-op-device-type';
import OtherTable from './components/table-step/table-step-op-other';
import RunTable from './components/table-step/table-step-run';
import WatchTable from './components/table-step/table-step-watch';
import { checkItemNormalized } from './constants';
import CopyPoint from './copy-point';
import PointSelect from './point-select';
import SettingModal from './setting-modal';

export function StepTableList({
  addStepBtnRef,
  stepCodes,
  stepMaps,
  addStep,
  step,
  editStep,
  deleteStep,
  syncCommonData,
  addCheckItemsMap,
  checkItemsMap,
  type,
  checkItemsRef,
  form,
  metaCategory,
  idcTag,
  blockTag,
  setStepMatchObjectInfoMaps,
  matchObjectInfoMaps,
  setCheckItemDeviceMaps,
  checkItemDeviceMaps,
  setStepCodes,
  removeExceptionHandling = false,
  onSaveBatchSettingCheckPoint,
  batchSettingCheckedPoint,
  setCustomizeUploadFileLoading,
  customizeUploadFileLoading,
}) {
  const [modalVisible, setModalVisible] = useState();
  const [stepInfo, setStepInfo] = useState();
  const { getFieldDecorator } = form;
  useEffect(() => {
    syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });
  }, [syncCommonData]);

  const onOk = ({ pointsMap, record, stepId }) => {
    const pointsMaps = {};
    for (const i in pointsMap) {
      const list = pointsMap[i].map(item => {
        return {
          unit: item.unit,
          dataType: item.dataType.code,
          pointValueText: item.validLimits && item.validLimits.join(','),
          pointCode: item.pointCode,
          pointName: item.name,
          deviceType: item.deviceType,
          identifyWay: CHANGE_CHECK_METHOD_KEY_MAP.SYSTEM,
          metaCode: item.metaCode,
          stepId,
        };
      });
      pointsMaps[i] = list;
    }
    if (checkItemsMap && checkItemsMap[record.id]) {
      const normalizedList = checkItemNormalized(checkItemsMap[record.id], 'deviceType');
      for (const i in normalizedList) {
        if (pointsMaps[i]) {
          pointsMaps[i] = [...pointsMaps[i], ...normalizedList[i]];
        } else {
          pointsMaps[i] = normalizedList[i];
        }
      }
    }
    let list = [];
    for (const i in pointsMaps) {
      const pointList = pointsMaps[i].map((item, index) => {
        if (pointsMaps[i].length === 1) {
          return { ...item, merge: 1, last: true };
        }
        if (index === 0) {
          return { ...item, merge: pointsMaps[i].length, last: false };
        }
        if (index === pointsMaps[i].length - 1) {
          return { ...item, merge: 0, last: true };
        }
        return { ...item, merge: 0, last: false };
      });
      list = list.concat(pointList);
    }
    form.setFieldsValue({ [`step-device-checkItem-${stepId}`]: list });
    addCheckItemsMap({ ...checkItemsMap, [record.id]: list });
  };

  const onDeleteStep = record => {
    const codes = stepCodes.filter(item => record.id !== item);
    const map = omit(stepMaps, record.id);
    const newCheckItemMaps = {};

    for (const i in checkItemsMap) {
      if (Array.isArray(checkItemsMap[i])) {
        newCheckItemMaps[i] = checkItemsMap[i].map(item => {
          return {
            ...item,
            maxInfluencesStep: item.maxInfluencesStep === record.id ? null : item.maxInfluencesStep,
          };
        });
      }
    }

    deleteStep({
      stepMaps: map,
      stepCodes: codes,
    });
    addCheckItemsMap({
      ...newCheckItemMaps,
      [record.id]: null,
    });
    if (get(checkItemDeviceMaps, record.id)) {
      const newCheckItemDeviceMaps = omit(checkItemDeviceMaps, record.id);
      setCheckItemDeviceMaps({
        ...newCheckItemDeviceMaps,
      });
    }
    if (get(checkItemDeviceMaps, record.id)) {
      const newMatchObjectInfoMaps = omit(matchObjectInfoMaps, record.id);
      setStepMatchObjectInfoMaps({
        ...newMatchObjectInfoMaps,
      });
    }
  };

  const onDeletePoint = (record, stepId) => {
    const checkItems = checkItemsMap[stepId];
    const list = checkItems.filter(item => item.deviceType !== record.deviceType);

    form.setFieldsValue({ [`step-device-checkItem-${stepId}`]: list });
    addCheckItemsMap({ [stepId]: list });
    if (get(checkItemDeviceMaps, stepId)) {
      const newCheckItemDeviceMaps = omit(checkItemDeviceMaps, stepId);
      setCheckItemDeviceMaps({
        ...newCheckItemDeviceMaps,
      });
    }
  };

  const onDeleteCheckItem = (record, stepId) => {
    const checkItems = checkItemsMap[stepId];
    const deletedList = checkItems.filter(item => item.metaCode !== record.metaCode);
    const list = [];
    const normalizedList = checkItemNormalized(deletedList, 'deviceType');
    // if (
    //   normalizedList &&
    //   normalizedList[record.deviceType] &&
    //   normalizedList[record.deviceType].filter(item => !item.pointCode).length &&
    //   !normalizedList[record.deviceType].filter(item => item.pointCode).length
    // ) {
    //   return message.error('不可删除，观察设备测点至少有一个系统测点');
    // }
    for (const i in normalizedList) {
      const devicesList = normalizedList[i].map((item, index) => {
        if (index === 0 && normalizedList[i].length === 1) {
          return { ...item, last: true, merge: 1 };
        }
        if (index === 0) {
          return { ...item, merge: normalizedList[i].length, last: false };
        }
        if (index === normalizedList[i].length - 1) {
          return { ...item, last: true };
        }
        return { ...item, last: false };
      });
      list.push(...devicesList);
    }
    form.setFieldsValue({ [`step-device-checkItem-${stepId}`]: list });
    addCheckItemsMap({ [stepId]: list });
    if (list.length === 0 && get(checkItemDeviceMaps, stepId)) {
      const newCheckItemDeviceMaps = omit(checkItemDeviceMaps, stepId);
      setCheckItemDeviceMaps({
        ...newCheckItemDeviceMaps,
      });
    }
  };

  // 步骤Table的操作列
  function getOperation({ stepType, stepId, stepIndex }) {
    if (type === 'templateView' || type === 'newByTemplate') {
      return null;
    }
    if (stepType === 'run' && step === 3) {
      const operationList = [
        {
          title: '匹配数量',
          dataIndex: 'number',
          fixed: 'right',
          width: 88,
          render: (_, record) => {
            const validationProps = {};
            const matchObjectInfo = get(matchObjectInfoMaps, stepId);
            if (!matchObjectInfo || (matchObjectInfo && matchObjectInfo.length)) {
              validationProps.validateStatus = 'error';
              validationProps.help = '请至少添加一个包间';
            }
            const obj = {
              children: (
                <MatchingFacilitiesRoom
                  defaultTab="matched"
                  btnText={`${
                    get(matchObjectInfoMaps, stepId) ? get(matchObjectInfoMaps, stepId).length : 0
                  }个`}
                  stepId={stepId}
                  deviceType={record.opObjectCode}
                  metaCategory={metaCategory}
                  idcTag={idcTag}
                  blockTag={blockTag}
                  setStepMatchObjectInfoMaps={({ stepId, selected }) => {
                    form.setFieldsValue({ [`step-room-${stepId}`]: selected });
                    setStepMatchObjectInfoMaps({
                      [stepId]: selected,
                    });
                  }}
                  selectedDevice={get(matchObjectInfoMaps, stepId) || []}
                  matchObjectInfoMaps={matchObjectInfoMaps}
                  mode="stepView"
                  roomTypeName={record.opObjectName}
                  roomType={record.opObjectCode}
                  footer={type === 'ticketView' ? null : undefined}
                />
              ),

              props: { rowSpan: record.merge },
            };
            return obj;
          },
        },
      ];
      if (type !== 'ticketView') {
        operationList.push({
          title: '操作',
          dataIndex: 'operation',
          width: 88,
          fixed: 'right',
          render: (_, record) => {
            const obj = {
              children: (
                <StyledFormItem>
                  {getFieldDecorator(`step-room-${stepId}`, {
                    rules: [
                      {
                        required: true,
                        validator: (_, value, callback) => {
                          const matchObjectInfo = get(matchObjectInfoMaps, stepId);
                          if (!matchObjectInfo || (matchObjectInfo && !matchObjectInfo.length)) {
                            callback('请至少添加一个包间');
                          } else {
                            callback();
                          }
                        },
                      },
                    ],
                  })(
                    <MatchingFacilitiesRoom
                      defaultTab="noMatched"
                      btnText="匹配包间"
                      stepId={stepId}
                      deviceType={record.opObjectCode}
                      metaCategory={metaCategory}
                      idcTag={idcTag}
                      blockTag={blockTag}
                      setStepMatchObjectInfoMaps={({ stepId, selected }) => {
                        form.setFieldsValue({ [`step-room-${stepId}`]: selected });
                        setStepMatchObjectInfoMaps({
                          [stepId]: selected,
                        });
                      }}
                      selectedDevice={get(matchObjectInfoMaps, stepId) || []}
                      matchObjectInfoMaps={matchObjectInfoMaps}
                      mode="step"
                      roomTypeName={record.opObjectName}
                      roomType={record.opObjectCode}
                      footer={type === 'ticketView' ? null : undefined}
                    />
                  )}
                </StyledFormItem>
              ),

              props: { rowSpan: record.merge },
            };
            return obj;
          },
        });
      }
      return operationList;
    }
    if (stepType === 'device' && step === 3) {
      const operationList = [
        {
          title: '匹配数量',
          dataIndex: 'number',
          fixed: 'right',
          width: 88,
          render: (_, record) => {
            const obj = {
              children: (
                <MatchingFacilitiesDevice
                  defaultTab="matched"
                  btnText={`${
                    get(matchObjectInfoMaps, stepId) ? get(matchObjectInfoMaps, stepId).length : 0
                  }台`}
                  stepId={stepId}
                  deviceType={record.opObjectCode}
                  metaCategory={metaCategory}
                  idcTag={idcTag}
                  blockTag={blockTag}
                  setStepMatchObjectInfoMaps={({ stepId, selected }) => {
                    form.setFieldsValue({ [`step-device-${stepId}`]: selected });
                    setStepMatchObjectInfoMaps({
                      [stepId]: selected,
                    });
                  }}
                  selectedDevice={get(matchObjectInfoMaps, stepId) || []}
                  matchObjectInfoMaps={matchObjectInfoMaps}
                  mode={type !== 'ticketView' ? 'step' : 'stepView'}
                  footer={type === 'ticketView' ? null : undefined}
                />
              ),

              props: { rowSpan: record.merge },
            };
            return obj;
          },
        },
      ];
      if (type !== 'ticketView') {
        operationList.push({
          title: '操作',
          dataIndex: 'operation',
          fixed: 'right',
          width: 88,
          render: (_, record) => {
            const obj = {
              children: (
                <StyledFormItem>
                  {getFieldDecorator(`step-device-${stepId}`, {
                    rules: [
                      {
                        required: true,
                        validator: (_, value, callback) => {
                          const matchObjectInfo = get(matchObjectInfoMaps, stepId);
                          if (!matchObjectInfo || (matchObjectInfo && !matchObjectInfo.length)) {
                            callback('请至少添加一台设备');
                          } else {
                            callback();
                          }
                        },
                      },
                    ],
                  })(
                    <MatchingFacilitiesDevice
                      defaultTab="noMatched"
                      btnText="匹配设备"
                      stepId={stepId}
                      deviceType={record.opObjectCode}
                      metaCategory={metaCategory}
                      idcTag={idcTag}
                      blockTag={blockTag}
                      setStepMatchObjectInfoMaps={({ stepId, selected }) => {
                        form.setFieldsValue({ [`step-device-${stepId}`]: selected });
                        setStepMatchObjectInfoMaps({
                          [stepId]: selected,
                        });
                      }}
                      selectedDevice={get(matchObjectInfoMaps, stepId) || []}
                      matchObjectInfoMaps={matchObjectInfoMaps}
                      mode="step"
                      addCheckItemsMap={addCheckItemsMap}
                    />
                  )}
                </StyledFormItem>
              ),

              props: { rowSpan: record.merge },
            };
            return obj;
          },
        });
      }
      return operationList;
    }
    let operation = null;
    if (step === 1) {
      operation = {
        title: '操作',
        dataIndex: 'operation',
        fixed: 'right',
        width: 150,
        render: (_, record) => (
          <span>
            <Button
              type="link"
              style={{ padding: 0, height: 'auto' }}
              onClick={() => {
                toggoleAddStepModalVisible();
                setStepInfo(record);
              }}
            >
              编辑
            </Button>
            <Divider type="vertical" />
            <DeleteConfirm
              variant="popconfirm"
              title={`确认删除步骤： ${record.stepName}吗？`}
              onOk={() => onDeleteStep(record)}
            >
              <Button style={{ padding: 0, height: 'auto', marginLeft: 0 }} type="link">
                删除
              </Button>
            </DeleteConfirm>
            {/* <Popover
              content={
                <GutterWrapper>
                  <p>{`确认删除步骤： ${record.stepName}吗？`}</p>
                  <Button
                    style={{ padding: 0, height: 'auto', marginLeft: 0 }}
                    type="link"
                    onClick={() => onDeleteStep(record)}
                  >
                    确认
                  </Button>
                </GutterWrapper>
              }
              trigger="focus"
            >
              <Button type="link" style={{ padding: 0, height: 'auto' }}>
                删除
              </Button>
            </Popover> */}
          </span>
        ),
      };
    }
    if (step === 2 && stepType !== 'run') {
      operation = {
        title: '操作',
        dataIndex: 'operation',
        fixed: 'right',
        width: 280,
        render: (_, record) => {
          const disabledTreeNodeKeys = [];
          if (checkItemsMap) {
            // for (let i in checkItemsMap) {
            if (checkItemsMap[stepId] && Object.keys(checkItemsMap[stepId]).length) {
              disabledTreeNodeKeys.push(...checkItemsMap[stepId].map(item => item.metaCode));
              // }
            }
          }

          // let text = record.stepType === 'OP' ? '添加影响目标' : '添加观察目标';

          return (
            <GutterWrapper direction="horizontal">
              <PointSelect
                disabledTreeNodeKeys={disabledTreeNodeKeys}
                stepMaps={stepMaps}
                stepCodes={stepCodes}
                stepIndex={stepIndex}
                checkItemsMap={checkItemsMap}
                stepId={stepId}
                addCheckItemsMap={addCheckItemsMap}
                metaCategory={metaCategory}
                idcTag={idcTag}
                blockTag={blockTag}
                type={type}
                onOk={({ pointsMap }) => onOk({ pointsMap, record, stepId })}
              />
              <Divider type="vertical" />
              <CopyPoint
                stepCodes={stepCodes}
                stepMaps={stepMaps}
                stepId={stepId}
                checkItemsMap={checkItemsMap}
                addCheckItemsMap={addCheckItemsMap}
              />
            </GutterWrapper>
          );
        },
      };
    }
    if (step === 2 && stepType && stepType === 'run') {
      operation = {
        title: '操作',
        dataIndex: 'operation',
        fixed: 'right',
        width: 280,
        render: (_, record) => (
          <AddRunTarget info={record} addStep={addStep} stepId={record.id} stepMaps={stepMaps} />
        ),
      };
    }
    if (step === 2 && stepType && stepType === 'customer') {
      operation = {
        title: '操作',
        dataIndex: 'operation',
        fixed: 'right',
        width: 280,
        render: (_, record) => {
          const fileListLength = record.fileInfoList?.length;
          let stepInfo = stepMaps[stepId];
          return (
            <Space split={<Divider type="vertical" spaceSize="mini" emphasis />}>
              <McUpload
                accept=".zip,.rar,.7z,.mp4,.3gp,.mov,.jpg,.jpeg,.png,.doc,.docx,.xls,.xlsx,.pdf"
                showUploadList={false}
                allowDelete={false}
                maxCount={1}
                onChange={({ fileList }) => {
                  if (fileList.filter(file => file.status === 'uploading').length) {
                    setCustomizeUploadFileLoading({ [stepId]: true });
                    return;
                  }
                  stepInfo = {
                    ...stepInfo,
                    fileInfoList: fileList,
                  };
                  setCustomizeUploadFileLoading({ [stepId]: false });
                  addStep({ stepMaps: { ...stepMaps, [stepId]: stepInfo } });
                }}
              >
                <Button loading={customizeUploadFileLoading[stepId]} type="link">
                  {fileListLength ? '替换附件' : '添加附件'}
                </Button>
              </McUpload>
              {fileListLength ? (
                <Button
                  type="link"
                  onClick={() => {
                    stepInfo = {
                      ...stepInfo,
                      fileInfoList: [],
                    };
                    addStep({ stepMaps: { ...stepMaps, [stepId]: stepInfo } });
                  }}
                >
                  删除
                </Button>
              ) : null}
            </Space>
          );
        },
      };
    }

    return operation;
  }

  const toggoleAddStepModalVisible = () => {
    setModalVisible(prevVisible => !prevVisible);
  };

  // 检测项Table的第一列
  const getFirstColumn = ({ stepId, stepType }) => {
    if (type === 'templateView' || type === 'ticketView' || type === 'newByTemplate') {
      return null;
    }
    // if (['device', 'watch', 'other'].includes(stepType) && step === 3) {
    //   return {
    //     title: '匹配数量',
    //     dataIndex: 'id',
    //     width: 88,
    //     className: 'merge-style',
    //     render: (_, record) => {
    //       const obj = {
    //         children: (
    //           <MatchingFacilitiesDevice
    //             defualtTab="matched"
    //             btnText={`${
    //               get(checkItemDeviceMaps, stepId)
    //                 ? get(get(checkItemDeviceMaps, stepId), record.deviceType)
    //                   ? get(get(checkItemDeviceMaps, stepId), record.deviceType).length
    //                   : 0
    //                 : 0
    //             }台`}
    //             metaCategory={metaCategory}
    //             idcTag={idcTag}
    //             checkItemsMap={checkItemsMap}
    //             defaultTab="matched"
    //             stepId={stepId}
    //             deviceType={record.deviceType}
    //             blockTag={blockTag}
    //             setCheckItemDeviceMaps={({ stepId, selected, deviceType }) => {
    //               form.setFieldsValue({
    //                 [`checkItem-device-${record.deviceType}-${stepId}`]: selected,
    //               });
    //               setCheckItemDeviceMaps({
    //                 [stepId]: { ...checkItemDeviceMaps[stepId], [deviceType]: selected },
    //               });
    //             }}
    //             selectedDevice={
    //               get(checkItemDeviceMaps, stepId) &&
    //               get(get(checkItemDeviceMaps, stepId), record.deviceType)
    //                 ? get(get(checkItemDeviceMaps, stepId), record.deviceType)
    //                 : []
    //             }
    //             checkItemDeviceMaps={checkItemDeviceMaps}
    //             mode="checkItem"
    //           />
    //         ),
    //         props: { rowSpan: record.merge },
    //       };
    //       return obj;
    //     },
    //   };
    // }

    if (step !== 3) {
      return {
        title: '',
        dataIndex: 'id',
        width: 88,
        className: 'merge-style',

        render: (_, record) => {
          const obj = {
            children: (
              <DeleteConfirm
                variant="popconfirm"
                title="确认删除吗？"
                onOk={() => onDeletePoint(record, stepId)}
              >
                <DeleteOutlined style={{ fontSize: '20px' }} />
              </DeleteConfirm>
              // <Popover
              //   content={
              //     <GutterWrapper>
              //       <p>{`确认删除吗？`}</p>
              //       <Button
              //         style={{ padding: 0, height: 'auto', marginLeft: 0 }}
              //         type="link"
              //         onClick={() => onDeletePoint(record, stepId)}
              //       >
              //         确认
              //       </Button>
              //     </GutterWrapper>
              //   }
              //   trigger="focus"
              // >
              //   <Button
              //     style={{ color: 'var(--text-color)' }}
              //     type="link"
              //     // onClick={() => console.log('tinyIcon need onClick')}
              //   >
              //     <DeleteOutlined style={{ fontSize: '20px' }} />
              //   </Button>
              // </Popover>
            ),
            props: { rowSpan: record.merge },
          };
          return obj;
        },
      };
    }
  };

  // 检测项的操作列
  const getCheckItemsOperation = (stepId, influencesStepText, stepType, stepIndex) => {
    if (type === 'templateView' || type === 'newByTemplate') {
      return null;
    }
    if (['device', 'watch', 'other'].includes(stepType) && step === 3) {
      return [
        type !== 'ticketView' && {
          title: '匹配数量',
          dataIndex: 'id',
          width: 88,
          className: 'last-merge-style',
          render: (_, record) => {
            const obj = {
              children: (
                <MatchingFacilitiesDevice
                  defualtTab="matched"
                  btnText={`${
                    get(checkItemDeviceMaps, stepId)
                      ? get(get(checkItemDeviceMaps, stepId), record.deviceType)
                        ? get(get(checkItemDeviceMaps, stepId), record.deviceType).length
                        : 0
                      : 0
                  }台`}
                  metaCategory={metaCategory}
                  idcTag={idcTag}
                  checkItemsMap={checkItemsMap}
                  defaultTab="matched"
                  stepId={stepId}
                  deviceType={record.deviceType}
                  blockTag={blockTag}
                  setCheckItemDeviceMaps={({ stepId, selected, deviceType }) => {
                    form.setFieldsValue({
                      [`checkItem-device-${record.deviceType}-${stepId}`]: selected,
                    });
                    setCheckItemDeviceMaps({
                      [stepId]: { ...checkItemDeviceMaps[stepId], [deviceType]: selected },
                    });
                  }}
                  selectedDevice={
                    get(checkItemDeviceMaps, stepId) &&
                    get(get(checkItemDeviceMaps, stepId), record.deviceType)
                      ? get(get(checkItemDeviceMaps, stepId), record.deviceType)
                      : []
                  }
                  checkItemDeviceMaps={checkItemDeviceMaps}
                  mode="checkItem"
                />
              ),
              props: { rowSpan: record.merge },
            };
            return obj;
          },
        },
        {
          title: `${type === 'ticketView' ? '匹配数量' : '操作'}`,
          dataIndex: 'operation',
          width: 88,
          className: 'last-merge-style',
          render: (_, record) => {
            const obj = {
              children: (
                <StyledFormItem>
                  {getFieldDecorator(`checkItem-device-${record.deviceType}-${stepId}`, {
                    rules: [
                      {
                        required: true,
                        validator: (_, value, callback) => {
                          const checkItemDevice = get(
                            get(checkItemDeviceMaps, stepId),
                            record.deviceType
                          );
                          if (!checkItemDevice || (checkItemDevice && !checkItemDevice.length)) {
                            callback('请至少添加一台设备');
                          } else {
                            callback();
                          }
                        },
                      },
                    ],
                  })(
                    <MatchingFacilitiesDevice
                      defaultTab={type !== 'ticketView' ? 'noMatched' : 'matched'}
                      btnText={
                        type === 'ticketView'
                          ? `${
                              get(checkItemDeviceMaps, stepId)
                                ? get(get(checkItemDeviceMaps, stepId), record.deviceType) &&
                                  get(get(checkItemDeviceMaps, stepId), record.deviceType).length
                                : 0
                            }台`
                          : '匹配设备'
                      }
                      stepId={stepId}
                      deviceType={record.deviceType}
                      metaCategory={metaCategory}
                      idcTag={idcTag}
                      blockTag={blockTag}
                      setCheckItemDeviceMaps={({ stepId, selected, deviceType }) => {
                        form.setFieldsValue({
                          [`checkItem-device-${record.deviceType}-${stepId}`]: selected,
                        });
                        setCheckItemDeviceMaps({
                          [stepId]: { ...checkItemDeviceMaps[stepId], [deviceType]: selected },
                        });
                      }}
                      selectedDevice={
                        get(checkItemDeviceMaps, stepId) &&
                        get(get(checkItemDeviceMaps, stepId), record.deviceType)
                          ? get(get(checkItemDeviceMaps, stepId), record.deviceType)
                          : []
                      }
                      checkItemDeviceMaps={checkItemDeviceMaps}
                      mode={type !== 'ticketView' ? 'checkItem' : 'checkItemView'}
                      footer={type === 'ticketView' ? null : undefined}
                    />
                  )}
                </StyledFormItem>
              ),

              props: { rowSpan: record.merge },
            };
            return obj;
          },
        },
      ].filter(Boolean);
    }

    if (step !== 3) {
      return {
        title: () => {
          return (
            <span>
              <span style={{ marginRight: 8 }}>操作</span>
              <BatchSettingModal
                influencesStepText={`${influencesStepText}周期`}
                stepId={stepId}
                checkItemsMap={checkItemsMap}
                addCheckItemsMap={addCheckItemsMap}
                stepCodes={stepCodes}
                stepIndex={stepIndex}
                stepMaps={stepMaps}
                type={type}
                batchSettingCheckedPoint={batchSettingCheckedPoint}
                onSaveBatchSettingCheckPoint={onSaveBatchSettingCheckPoint}
              />
            </span>
          );
        },
        dataIndex: 'operation',
        width: 170,
        render: (_, record) => {
          const deleteItem = (
            // <Popover
            //   content={
            //     <GutterWrapper>
            //       <p>{`确认删除： ${record.pointName}吗？`}</p>
            //       <Button
            //         style={{ padding: 0, height: 'auto', marginLeft: 0 }}
            //         type="link"
            //         onClick={() => onDeleteCheckItem(record, stepId)}
            //       >
            //         确认
            //       </Button>
            //     </GutterWrapper>
            //   }
            //   trigger="focus"
            // >
            //   <Button type="link" style={{ padding: 0, height: 'auto' }}>
            //     删除
            //   </Button>
            // </Popover>
            <DeleteConfirm
              variant="popconfirm"
              title={`确认删除： ${record.pointName}吗？`}
              onOk={() => onDeleteCheckItem(record, stepId)}
            >
              <Button style={{ padding: 0, height: 'auto', marginLeft: 0 }} type="link">
                删除
              </Button>
            </DeleteConfirm>
          );
          if (record.pointCode) {
            return (
              <span>
                <SettingModal
                  stepCodes={stepCodes}
                  checkItemsMap={checkItemsMap}
                  influencesStepText={`${influencesStepText}周期`}
                  stepId={stepId}
                  addCheckItemsMap={addCheckItemsMap}
                  type={type}
                  info={record}
                  stepIndex={stepIndex}
                  stepMaps={stepMaps}
                />
                <Divider type="vertical" />
                {deleteItem}
              </span>
            );
          }
          return (
            <span>
              <Custom
                type="edit"
                isSetting
                stepCodes={stepCodes}
                checkItems={checkItemsMap[stepId]}
                influencesStepText={influencesStepText}
                stepId={stepId}
                addCheckItemsMap={addCheckItemsMap}
                info={record}
                stepIndex={stepIndex}
                stepMaps={stepMaps}
                deviceType={record.deviceType}
                ticketType={type}
                dataType={record.dataType}
              />
              <Divider type="vertical" />
              {deleteItem}
            </span>
          );
        },
      };
    }
  };

  const steps = stepCodes
    .map((item, index) => {
      const record = stepMaps[item];
      const checkItems = checkItemsMap ? checkItemsMap[item] : [];
      if (record.stepType === CHANGE_STEP_TYPE_KEY_MAP.OP && record.opType === 'LIMIT') {
        return {
          id: item,
          title: record.stepName,
          children: (
            <DevicesTypeTable
              checkItems={checkItems}
              data={[{ ...record, id: item }]}
              operation={getOperation({ stepId: item, stepType: 'device', stepIndex: index })}
              firstColumn={getFirstColumn({ stepId: item, stepType: 'device' })}
              stepId={item}
              checkItemsOperation={getCheckItemsOperation(item, '观察', 'device', index)}
              type={type}
              addCheckItemsMap={addCheckItemsMap}
              stepCodes={stepCodes}
              checkItemsRef={checkItemsRef}
              step={step}
              getFieldDecorator={getFieldDecorator}
              matchObjectInfoMaps={matchObjectInfoMaps}
              checkItemDeviceMaps={checkItemDeviceMaps}
              stepMaps={stepMaps}
              stepIndex={index}
              batchSettingCheckedPoint={batchSettingCheckedPoint}
              removeExceptionHandling={removeExceptionHandling}
              onSaveBatchSettingCheckPoint={onSaveBatchSettingCheckPoint}
            />
          ),
        };
      }
      if (record.stepType === CHANGE_STEP_TYPE_KEY_MAP.OP && record.opType === 'CUSTOMIZE') {
        return {
          id: item,
          title: record.stepName,
          children: (
            <OtherTable
              checkItems={checkItems}
              data={[{ ...record, id: item }]}
              operation={getOperation({ stepId: item, stepType: 'other', stepIndex: index })}
              firstColumn={getFirstColumn({ stepId: item, stepType: 'other' })}
              stepId={item}
              checkItemsOperation={getCheckItemsOperation(item, '观察', 'other', index)}
              type={type}
              addCheckItemsMap={addCheckItemsMap}
              stepCodes={stepCodes}
              step={step}
              getFieldDecorator={getFieldDecorator}
              noCheck
              stepMaps={stepMaps}
              stepIndex={index}
              removeExceptionHandling={removeExceptionHandling}
              batchSettingCheckedPoint={batchSettingCheckedPoint}
              onSaveBatchSettingCheckPoint={onSaveBatchSettingCheckPoint}
            />
          ),
        };
      }
      if (record.stepType === CHANGE_STEP_TYPE_KEY_MAP.WATCH) {
        return {
          id: item,
          title: record.stepName,
          children: (
            <WatchTable
              checkItems={checkItems}
              data={[{ ...record, id: item }]}
              operation={getOperation({ stepId: item, stepType: 'watch', stepIndex: index })}
              type={type}
              firstColumn={getFirstColumn({ stepId: item, stepType: 'watch' })}
              stepId={item}
              checkItemsOperation={getCheckItemsOperation(item, '观察', 'watch', index)}
              addCheckItemsMap={addCheckItemsMap}
              stepCodes={stepCodes}
              step={step}
              getFieldDecorator={getFieldDecorator}
              checkItemDeviceMaps={checkItemDeviceMaps}
              stepMaps={stepMaps}
              stepIndex={index}
              batchSettingCheckedPoint={batchSettingCheckedPoint}
              removeExceptionHandling={removeExceptionHandling}
              onSaveBatchSettingCheckPoint={onSaveBatchSettingCheckPoint}
            />
          ),
        };
      }
      if (record.stepType === CHANGE_STEP_TYPE_KEY_MAP.RUN) {
        return {
          id: item,
          title: record.stepName,
          children: (
            <RunTable
              step={step}
              data={[{ ...record, id: item }]}
              operation={getOperation({ stepType: 'run', stepId: item, stepIndex: index })}
              stepId={item}
              getFieldDecorator={getFieldDecorator}
              checkItemsOperation={getCheckItemsOperation(item, '观察', 'run', index)}
              stepMaps={stepMaps}
            />
          ),
        };
      }
      if (record.stepType === CHANGE_STEP_TYPE_KEY_MAP.CUSTOMIZE) {
        return {
          id: item,
          title: record.stepName,
          children: (
            <CustomerTable
              step={step}
              data={[{ ...record, id: item }]}
              operation={getOperation({ stepType: 'customer', stepId: item, stepIndex: index })}
              stepId={item}
              getFieldDecorator={getFieldDecorator}
              checkItemsOperation={getCheckItemsOperation(item, '观察', 'customer', index)}
              stepMaps={stepMaps}
            />
          ),
        };
      }
      return null;
    })
    .filter(Boolean);
  return (
    <Form>
      {Array.isArray(stepCodes) && !!stepCodes.length && (
        <Steps steps={steps} stepCodes={stepCodes} setStepCodes={setStepCodes} type={type} />
      )}
      {modalVisible && (
        <EditStepModal
          editStep={editStep}
          title="编辑步骤"
          toggoleAddStepModalVisible={toggoleAddStepModalVisible}
          stepCodes={stepCodes}
          stepMaps={stepMaps}
          info={stepMaps[stepInfo?.id]}
          setStepMatchObjectInfoMaps={setStepMatchObjectInfoMaps}
          setCheckItemDeviceMaps={setCheckItemDeviceMaps}
          addCheckItemsMap={addCheckItemsMap}
        />
      )}
      {step === 1 && (
        <AddStepButton
          ref={addStepBtnRef}
          stepCodes={stepCodes}
          showInstruction={Array.isArray(stepCodes) && stepCodes.length ? false : true}
          addStep={addStep}
          stepMaps={stepMaps}
          step={step}
        />
      )}
    </Form>
  );
}

const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
};

const mapStateToProps = ({ common: { deviceCategory } }) => ({
  metaCategory: deviceCategory ? deviceCategory.normalizedList : {},
});
export const FCMergedItemsForm = React.forwardRef((props, ref) => {
  React.useImperativeHandle(ref, () => ({ form: props.form }));
  if (props.step === 2 || props.step === 3) {
    return <StepTableList {...props} />;
  }
  return <StepTableList {...props} />;
});
FCMergedItemsForm.displayName = 'FCMergedItemsForm';
export const EhancedFCDeviceTypeTableForm = Form.create('table-step-op-device-type-form')(
  FCMergedItemsForm
);
export default connect(mapStateToProps, mapDispatchToProps)(EhancedFCDeviceTypeTableForm);
