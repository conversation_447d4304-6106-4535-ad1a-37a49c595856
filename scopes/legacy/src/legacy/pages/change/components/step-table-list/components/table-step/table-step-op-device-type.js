import React from 'react';

import { Typography } from '@manyun/base-ui.ui.typography';

import { SLA_UNIT_TYPE_TEXT_MAP } from '@manyun/ticket.model.change';

import {
  CHANGE_CHECK_METHOD_KEY_MAP,
  <PERSON>ANGE_CHECK_METHOD_TEXT_MAP,
  CHANGE_EXECUTE_METHOD_KEY_MAP,
  CHANGE_EXECUTE_METHOD_TEXT_MAP,
  CHANGE_STEP_TYPE_KEY_MAP,
  CHANGE_STEP_TYPE_TEXT_MAP,
} from '../../../../constants';
import { StyledFormItem, StyledTinyTable } from '../styled';
import InfluencesDeviceTable from './influences-device-table';

export function OpDeviceTable({
  data,
  className,
  operation = null,
  checkItems,
  firstColumn,
  stepId,
  checkItemsOperation,
  type,
  addCheckItemsMap,
  stepCodes,
  step,
  getFieldDecorator,
  checkItemDeviceMaps,
  stepMaps,
  stepIndex,
  removeExceptionHandling,

  onSaveBatchSettingCheckPoint,
  batchSettingCheckedPoint,
}) {
  return (
    <div>
      <StyledTinyTable
        className={className}
        rowKey="id"
        columns={getColumns(operation)}
        dataSource={data}
        size="small"
        pagination={false}
        bordered={false}
        align="left"
        scroll={{ x: 'max-content' }}
      />
      <StyledFormItem>
        {getFieldDecorator(`step-device-checkItem-${stepId}`, {
          rules: [
            {
              required: true,
              validator: (_, value, callback) => {
                if (
                  step === 2 &&
                  ((Array.isArray(checkItems) && !checkItems.length) || !checkItems)
                ) {
                  callback('请至少添加一条观察目标');
                } else {
                  callback();
                }
              },
            },
          ],
        })(
          Array.isArray(checkItems) &&
            !!checkItems.length &&
            ([2, 3].includes(step) || type === 'templateView') ? (
            <InfluencesDeviceTable
              checkItems={checkItems}
              checkItemsOperation={checkItemsOperation}
              firstColumn={firstColumn}
              stepId={stepId}
              influencesText="观察"
              type={type}
              stepCodes={stepCodes}
              addCheckItemsMap={addCheckItemsMap}
              getFieldDecorator={getFieldDecorator}
              checkItemDeviceMaps={checkItemDeviceMaps}
              step={step}
              stepMaps={stepMaps}
              stepIndex={stepIndex}
              removeExceptionHandling={removeExceptionHandling}
              onSaveBatchSettingCheckPoint={onSaveBatchSettingCheckPoint}
              batchSettingCheckedPoint={batchSettingCheckedPoint}
            />
          ) : (
            <div></div>
          )
        )}
      </StyledFormItem>
    </div>
  );
}

const getColumns = operation => {
  const list = [
    {
      title: '步骤内容',
      dataIndex: 'stepDesc',
      width: 504,
      render: text => (
        <Typography.Text style={{ maxWidth: 504 }} ellipsis={{ tooltip: text }}>
          {text}
        </Typography.Text>
      ),
    },
    {
      title: '步骤类型',
      dataIndex: 'stepType',
      render: text => <span>{CHANGE_STEP_TYPE_TEXT_MAP[CHANGE_STEP_TYPE_KEY_MAP[text]]}</span>,
    },
    {
      title: '操作目标',
      dataIndex: 'opObjectName',
    },
    {
      title: '执行方式',
      dataIndex: 'exeWay',
      render: text => (
        <span>{CHANGE_EXECUTE_METHOD_TEXT_MAP[CHANGE_EXECUTE_METHOD_KEY_MAP[text]]}</span>
      ),
    },
    {
      title: '验证测点',
      dataIndex: 'pointName',
    },
    {
      title: '验证状态',
      dataIndex: 'pointValueText',
    },
    {
      title: '验证方式',
      dataIndex: 'identifyWay',

      render: text => (
        <span>{CHANGE_CHECK_METHOD_TEXT_MAP[CHANGE_CHECK_METHOD_KEY_MAP[text]]}</span>
      ),
    },
    {
      title: '操作动作',
      dataIndex: 'operate',
    },
    // {
    //   title: ' ',
    //   dataIndex: 'empty',
    // },
    {
      title: 'OLA',
      dataIndex: 'ola',
      fixed: 'right',
      render: (text, record) => (
        <span>
          {text !== null && text !== 0 ? `${text}${SLA_UNIT_TYPE_TEXT_MAP[record.olaUnit]}` : '--'}
        </span>
      ),
    },
  ];
  if (Array.isArray(operation)) {
    list.push(...operation);
  }
  if (!Array.isArray(operation) && operation) {
    list.push(operation);
  }

  return list;
};

export default OpDeviceTable;
