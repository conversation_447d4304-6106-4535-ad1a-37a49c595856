import React, { useEffect, useState } from 'react';

import Form from '@ant-design/compatible/es/form';
import { ApiSelect, Select } from '@galiojs/awesome-antd';
import { omit } from 'lodash';
import shortid from 'shortid';

import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Space } from '@manyun/base-ui.ui.space';

import { generateValidLimitsDataSource } from '@manyun/monitoring.model.point';
import { fetchPointsByCondition } from '@manyun/resource-hub.service.fetch-points-by-condition';
import { SLA_UNIT_TYPE_TEXT_MAP, SlaUnitTypeKeyMap } from '@manyun/ticket.model.change';

import { AssetClassificationApiTreeSelect } from '@manyun/dc-brain.legacy.components';

import {
  CHANGE_CHECK_METHOD_KEY_MAP,
  CHANGE_CHECK_METHOD_TEXT_MAP,
  CHANGE_EXECUTE_METHOD_KEY_MAP,
  CHANGE_EXECUTE_METHOD_TEXT_MAP,
  CHANGE_STEP_OPERATE_TYPE_KEY_MAP,
  CHANGE_STEP_OPERATE_TYPE_TEXT_MAP,
  CHANGE_STEP_TYPE_KEY_MAP,
  CHANGE_STEP_TYPE_TEXT_MAP,
} from '../../../constants/index';

// const formItemLayout = {
//   labelCol: {
//     xs: { span: 24 },
//     sm: { span: 6 },
//   },
//   wrapperCol: {
//     xs: { span: 24 },
//     sm: { span: 18 },
//   },
// };
const formItemLayoutSpan24 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 3 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 21 },
  },
};
// const formItemLayoutSpan24 = {
//   labelCol: {
//     xs: { span: 24 },
//     sm: { span: 2 },
//   },
//   wrapperCol: {
//     xs: { span: 24 },
//     sm: { span: 6 },
//   },
// };
// const formItemLayoutSpan24 = {
//   labelCol: {
//     xs: { span: 24 },
//     sm: { span: 4 },
//   },
//   wrapperCol: {
//     xs: { span: 24 },
//     sm: { span: 10 },
//   },
// };
export function AddEditStepModal({
  form,
  stepCodes,
  stepMaps,
  addStep,
  toggoleAddStepModalVisible,
  title,
  info = null,
  editStep,
  className,
  setStepMatchObjectInfoMaps,
  setCheckItemDeviceMaps,
  addCheckItemsMap,
}) {
  const [pointList, setPointList] = useState([]);
  const [validLimits, setValidLimits] = useState([]);
  const { getFieldDecorator } = form;
  useEffect(() => {
    if (info && info.opObjectCode) {
      getPoint(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [info]);

  const onCreate = () => {
    form.validateFields(async (err, values) => {
      if (err) {
        return;
      }
      const data = omit(values, 'opObject', 'point', 'expected', 'olaLimit');
      if (values.opObject) {
        data.opObjectName = values.opObject.name;
        data.opObjectCode = values.opObject.code;
      }
      if (values.olaLimit === 'false') {
        data.ola = 0;
      }
      if (values.point) {
        data.pointName = values.point.label;
        data.pointCode = values.point.key;
      }
      if (values.expected) {
        data.pointValueText = values.expected.label;
        data.expectedValue = values.expected.key;
      }
      if (
        data.stepType === CHANGE_STEP_TYPE_KEY_MAP.RUN ||
        data.stepType === CHANGE_STEP_TYPE_KEY_MAP.CUSTOMIZE ||
        (data.stepType === CHANGE_STEP_TYPE_KEY_MAP.OP &&
          data.opType === CHANGE_STEP_OPERATE_TYPE_KEY_MAP.CUSTOMIZE)
      ) {
        data.exeWay = CHANGE_EXECUTE_METHOD_KEY_MAP.PERSON;
        data.identifyWay = CHANGE_CHECK_METHOD_KEY_MAP.PERSON;
      }
      if (data.stepType === CHANGE_STEP_TYPE_KEY_MAP.WATCH) {
        data.exeWay = CHANGE_EXECUTE_METHOD_KEY_MAP.SYSTEM;
        data.identifyWay = CHANGE_CHECK_METHOD_KEY_MAP.SYSTEM;
      }

      if (info) {
        if (info.stepType !== data.stepType) {
          setStepMatchObjectInfoMaps && setStepMatchObjectInfoMaps({ [info.id]: null });
          setCheckItemDeviceMaps && setCheckItemDeviceMaps({ [info.id]: null });
          addCheckItemsMap && addCheckItemsMap({ [info.id]: null });
        }
        if (data.stepType === CHANGE_STEP_TYPE_KEY_MAP.CUSTOMIZE) {
          data.fileInfoList = stepMaps[info.id]?.fileInfoList;
        }
        editStep({
          stepMaps: { ...stepMaps, [info.id]: { id: info.id, ...data } },
        });
      } else {
        const id = shortid();
        addStep({
          stepMaps: { ...stepMaps, [id]: { ...data, id } },
          stepCodes: [...stepCodes, id],
        });
      }
      toggoleAddStepModalVisible();
    });
  };

  const onChangeStepType = () => {
    form.setFieldsValue({ opType: undefined, operate: undefined });
  };

  const getPoint = async init => {
    const { data } = await fetchPointsByCondition({
      deviceType: form.getFieldValue('opObject') && form.getFieldValue('opObject').code,
      dataTypeList: ['DI'],
      pointTypeList: ['ORI', 'CAL_DEVICE', 'AGG_DEVICE'],
      isRemoveSub: true,
    });
    if (data && Array.isArray(data.data) && data.data.length) {
      const result = data.data.map(point => point.toApiObject());
      setPointList(result);
      if (init) {
        getValidation(form.getFieldValue('point'), true, result);
      }
    }
  };

  const getValidation = async (value, init, initPointList) => {
    if (value) {
      const newPointList = init ? initPointList : pointList;
      const validLimitsItem = newPointList.filter(item => item.pointCode === value.key);
      const list = validLimitsItem.length
        ? generateValidLimitsDataSource(validLimitsItem[0].validLimits)
        : [];

      setValidLimits(list);
    }

    if (!init) {
      form.setFieldsValue({ expected: undefined });
    }
  };

  const onChangeDevicesType = () => {
    info && setStepMatchObjectInfoMaps && setStepMatchObjectInfoMaps({ [info.id]: null });
    form.setFieldsValue({ expected: undefined, point: undefined });
  };

  const onChangeOpType = type => {
    form.setFieldsValue({
      operate: undefined,
      opObjectName: undefined,
      opObject: undefined,
      identifyWay:
        type === CHANGE_STEP_OPERATE_TYPE_KEY_MAP.LIMIT
          ? CHANGE_CHECK_METHOD_KEY_MAP.SYSTEM
          : CHANGE_CHECK_METHOD_KEY_MAP.PERSON,
    });
    if (info) {
      editStep({
        stepMaps: {
          ...stepMaps,
          [info?.id]: {
            ...stepMaps[info?.id],
            id: info?.id,
            opObjectName: undefined,
            opObjectCode: undefined,
          },
        },
      });
      setStepMatchObjectInfoMaps && setStepMatchObjectInfoMaps({ [info.id]: null });
    }
  };

  return (
    <Modal
      style={{ minWidth: 750 }}
      open
      title={title}
      destroyOnClose
      className={className}
      forceRender
      onCancel={() => {
        toggoleAddStepModalVisible();
      }}
      onOk={onCreate}
    >
      <Form colon={false} {...formItemLayoutSpan24} autoComplete="off">
        {/* <Row gutter={16}>
          <Col span={24}> */}
        <Form.Item label="步骤标题" {...formItemLayoutSpan24}>
          {getFieldDecorator('stepName', {
            rules: [
              {
                required: true,
                message: '步骤标题必填！',
              },
              {
                max: 20,
                message: '最多输入 20 个字符！',
              },
            ],
            initialValue: info && info.stepName,
          })(<Input style={{ width: 468 }} />)}
        </Form.Item>
        {/* </Col>
          <Col span={24}> */}
        <Form.Item label="步骤内容" {...formItemLayoutSpan24}>
          {getFieldDecorator('stepDesc', {
            rules: [
              { required: true, message: '步骤内容必填！' },
              {
                max: 300,
                message: '最多输入 300 个字符！',
              },
            ],
            initialValue: info && info.stepDesc,
          })(<Input.TextArea style={{ width: 468 }} />)}
        </Form.Item>
        {/* </Col>
          <Col span={24}> */}
        <Form.Item label="OLA" {...formItemLayoutSpan24}>
          {getFieldDecorator('olaLimit', {
            rules: [
              {
                required: true,
                message: 'OLA必填！',
              },
            ],
            initialValue: info ? (info.ola === 0 ? 'false' : 'true') : 'true',
          })(
            <Radio.Group>
              <Radio value="true">有限制</Radio>
              <Radio value="false">无限制</Radio>
            </Radio.Group>
          )}
        </Form.Item>
        {form.getFieldValue('olaLimit') === 'true' && (
          <Form.Item label=" " required={false} style={{ marginBottom: 0 }}>
            <Space style={{ width: '100%' }}>
              <Form.Item style={{ display: 'inline-block' }} required={false} colon={false}>
                {getFieldDecorator('ola', {
                  rules: [
                    {
                      required: true,
                      message: 'OLA必填！',
                    },
                  ],
                  initialValue: info && info.ola,
                })(<InputNumber precision={0} min={1} max={1000} />)}
              </Form.Item>
              <Form.Item style={{ display: 'inline-block' }} required={false} colon={false}>
                {getFieldDecorator('olaUnit', {
                  rules: [
                    {
                      required: true,
                      message: 'OLA单位必填！',
                    },
                  ],
                  initialValue: info ? info.olaUnit : SlaUnitTypeKeyMap.Minutes,
                })(
                  <Select
                    style={{ width: 85 }}
                    options={Object.keys(SLA_UNIT_TYPE_TEXT_MAP).map(key => ({
                      key,
                      value: key,
                      label: SLA_UNIT_TYPE_TEXT_MAP[key],
                    }))}
                  />
                )}
              </Form.Item>
            </Space>
          </Form.Item>
        )}
        <Form.Item label="步骤类型" {...formItemLayoutSpan24}>
          {getFieldDecorator('stepType', {
            rules: [{ required: true, message: '步骤类型必选！' }],
            initialValue: info && info.stepType,
          })(
            <Select style={{ width: 200 }} allowClear onChange={onChangeStepType}>
              {Object.entries(CHANGE_STEP_TYPE_TEXT_MAP).map(item => (
                <Select.Option key={item[0]} value={item[0]}>
                  {item[1]}
                </Select.Option>
              ))}
            </Select>
          )}
        </Form.Item>
        {form.getFieldValue('stepType') === CHANGE_STEP_TYPE_KEY_MAP.OP && (
          <Form.Item label="操作类型">
            {getFieldDecorator('opType', {
              rules: [{ required: true, message: '操作类型必选！' }],
              initialValue: info ? info.opType : undefined,
            })(
              <Select style={{ width: 200 }} allowClear onChange={onChangeOpType}>
                {Object.entries(CHANGE_STEP_OPERATE_TYPE_TEXT_MAP).map(item => (
                  <Select.Option key={item[0]} value={item[0]}>
                    {item[1]}
                  </Select.Option>
                ))}
              </Select>
            )}
          </Form.Item>
        )}
        {form.getFieldValue('opType') === CHANGE_STEP_OPERATE_TYPE_KEY_MAP.LIMIT && (
          <Form.Item label="设备类型">
            {getFieldDecorator('opObject', {
              rules: [{ required: true, message: '设备类型必选！' }],
              initialValue:
                info && info.opObjectCode
                  ? {
                      name: info.opObjectName,
                      code: info.opObjectCode,
                    }
                  : undefined,
            })(
              <AssetClassificationApiTreeSelect
                style={{ width: 200 }}
                dataType={['snDevice']}
                category="category"
                disabledDepths={[0, 1]}
                requestOnDidMount
                onChange={onChangeDevicesType}
              />
            )}
          </Form.Item>
        )}
        {form.getFieldValue('opType') === CHANGE_STEP_OPERATE_TYPE_KEY_MAP.LIMIT && (
          <Form.Item label="操作动作">
            {getFieldDecorator('operate', {
              rules: [
                { required: true, message: '操作动作必填！' },
                {
                  max: 5,
                  message: '最多输入 5 个字符！',
                },
              ],
              initialValue: info && info.operate,
            })(<Input allowClear style={{ width: 200 }} />)}
          </Form.Item>
          // </Col>
        )}
        {form.getFieldValue('opType') === CHANGE_STEP_OPERATE_TYPE_KEY_MAP.LIMIT && (
          // <Col span={8}>
          <Form.Item label="执行方式">
            {getFieldDecorator('exeWay', {
              rules: [{ required: true, message: '执行方式必选！' }],
              initialValue: CHANGE_EXECUTE_METHOD_KEY_MAP.PERSON,
            })(
              <Select style={{ width: 200 }} allowClear disabled>
                <Select.Option value={CHANGE_EXECUTE_METHOD_KEY_MAP.PERSON}>
                  {CHANGE_EXECUTE_METHOD_TEXT_MAP[CHANGE_EXECUTE_METHOD_KEY_MAP.PERSON]}
                </Select.Option>
              </Select>
            )}
          </Form.Item>
          // </Col>
        )}
        {form.getFieldValue('opType') === CHANGE_STEP_OPERATE_TYPE_KEY_MAP.LIMIT && (
          // <Col span={8}>
          <Form.Item label="验证测点">
            {getFieldDecorator('point', {
              rules: [{ required: true, message: '验证测点必选！' }],
              initialValue: info?.pointCode
                ? { label: info.pointName, value: info.pointCode, key: info.pointCode }
                : undefined,
            })(
              <ApiSelect
                allowClear
                disabled={!form.getFieldValue('opObject')}
                labelInValue
                fieldNames={{ label: 'name', value: 'pointCode' }}
                serviceQueries={[form.getFieldValue('opObject')?.code]}
                dataService={async () => {
                  const { data } = await fetchPointsByCondition({
                    deviceType:
                      form.getFieldValue('opObject') && form.getFieldValue('opObject').code,
                    dataTypeList: ['DI'],
                    pointTypeList: ['ORI', 'CAL_DEVICE', 'AGG_DEVICE'],
                    isRemoveSub: true,
                  });
                  if (data) {
                    const result = data.data.map(point => point.toApiObject());
                    setPointList(result);
                    return Promise.resolve(result);
                  } else {
                    return Promise.resolve([]);
                  }
                }}
                style={{ width: 200 }}
                onChange={value => getValidation(value)}
              />
            )}
          </Form.Item>
          // </Col>
        )}
        {form.getFieldValue('opType') === CHANGE_STEP_OPERATE_TYPE_KEY_MAP.LIMIT && (
          // <Col span={8}>
          <Form.Item label="验证状态">
            {getFieldDecorator('expected', {
              rules: [{ required: true, message: '验证状态必选！' }],
              initialValue:
                info && info.expectedValue !== null
                  ? { label: info.pointValueText, key: info.expectedValue }
                  : undefined,
            })(
              <Select
                style={{ width: 200 }}
                labelInValue
                allowClear
                disabled={!form.getFieldValue('point')}
              >
                {validLimits.map(item => (
                  <Select.Option key={item.value}>{item.label}</Select.Option>
                ))}
              </Select>
            )}
          </Form.Item>
          // </Col>
        )}
        {form.getFieldValue('opType') === CHANGE_STEP_OPERATE_TYPE_KEY_MAP.LIMIT && (
          // <Col span={8}>
          <Form.Item label="验证方式">
            {getFieldDecorator('identifyWay', {
              rules: [{ required: true, message: '验证方式必选！' }],
              initialValue: CHANGE_CHECK_METHOD_KEY_MAP.SYSTEM,
            })(
              <Select style={{ width: 200 }} allowClear disabled>
                <Select.Option key={CHANGE_CHECK_METHOD_KEY_MAP.SYSTEM}>
                  {CHANGE_CHECK_METHOD_TEXT_MAP[CHANGE_CHECK_METHOD_KEY_MAP.SYSTEM]}
                </Select.Option>
              </Select>
            )}
          </Form.Item>
          // </Col>
        )}

        {form.getFieldValue('opType') === CHANGE_STEP_OPERATE_TYPE_KEY_MAP.CUSTOMIZE && (
          // <Col span={16}>
          <Form.Item label="操作目标" {...formItemLayoutSpan24}>
            {getFieldDecorator('opObjectName', {
              rules: [
                { required: true, message: '目标名称必填！' },
                {
                  max: 10,
                  message: '最多输入 10 个字符！',
                },
              ],
              initialValue: info && info.opObjectName,
            })(<Input allowClear style={{ width: 468 }} />)}
          </Form.Item>
          // </Col>
        )}
        {form.getFieldValue('opType') === CHANGE_STEP_OPERATE_TYPE_KEY_MAP.CUSTOMIZE && (
          // <Col span={24}>
          <Form.Item label="操作方法" {...formItemLayoutSpan24}>
            {getFieldDecorator('operate', {
              rules: [
                { required: true, message: '操作方法必填！' },
                {
                  max: 20,
                  message: '最多输入 20 个字符！',
                },
              ],
              initialValue: info && info.operate,
            })(<Input style={{ width: 468 }} allowClear />)}
          </Form.Item>
          // </Col>
        )}
        {form.getFieldValue('opType') === CHANGE_STEP_OPERATE_TYPE_KEY_MAP.CUSTOMIZE && (
          // <Col span={24}>
          <Form.Item label="验证方法" {...formItemLayoutSpan24}>
            {getFieldDecorator('identifyWay', {
              rules: [{ required: true, message: '验证方法必选！' }],
              initialValue: CHANGE_CHECK_METHOD_KEY_MAP.PERSON,
            })(
              <Select style={{ width: 200 }} allowClear disabled>
                <Select.Option key={CHANGE_CHECK_METHOD_KEY_MAP.PERSON}>
                  {CHANGE_CHECK_METHOD_TEXT_MAP[CHANGE_CHECK_METHOD_KEY_MAP.PERSON]}
                </Select.Option>
              </Select>
            )}
          </Form.Item>
          // </Col>
        )}
        {/* </Row> */}
      </Form>
    </Modal>
  );
}

export default Form.create('AddStepButtonRef')(AddEditStepModal);
