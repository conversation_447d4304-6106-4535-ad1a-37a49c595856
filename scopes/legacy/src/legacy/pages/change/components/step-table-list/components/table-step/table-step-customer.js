import React from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { SimpleFileList } from '@manyun/base-ui.ui.file-list';
import { Typography } from '@manyun/base-ui.ui.typography';

import { SLA_UNIT_TYPE_TEXT_MAP } from '@manyun/ticket.model.change';

import { CHANGE_STEP_TYPE_KEY_MAP, CHANGE_STEP_TYPE_TEXT_MAP } from '../../../../constants';
import { StyledFormItem, StyledTinyTable } from '../styled';

export function CustomerTable({
  data,
  className,
  operation = null,
  checkItems,
  step,
  getFieldDecorator,
  stepId,
}) {
  return (
    <StyledTinyTable
      bordered={false}
      className={className}
      rowKey="id"
      columns={getColumns(operation, step, getFieldDecorator, stepId)}
      dataSource={[{ ...data[0], checkItems }]}
      size="small"
      pagination={false}
      align="left"
      scroll={{ x: 'max-content' }}
    />
  );
}

const getColumns = (operation, step, getFieldDecorator, stepId) => {
  const list = [
    {
      title: '步骤内容',
      dataIndex: 'stepDesc',
      width: 504,
      render: text => (
        <Typography.Text style={{ maxWidth: 504 }} ellipsis={{ tooltip: text }}>
          {text}
        </Typography.Text>
      ),
    },
    {
      title: '步骤类型',
      dataIndex: 'stepType',
      render: text => <span>{CHANGE_STEP_TYPE_TEXT_MAP[CHANGE_STEP_TYPE_KEY_MAP[text]]}</span>,
    },
    {
      title: '附件',
      dataIndex: 'fileInfoList',
      render: (text, _, index) => {
        if (!text && step === 2) {
          return (
            <StyledFormItem>
              {getFieldDecorator(`fileInfoList-${index}-${stepId}`, {
                initialValue: text,
                // rules: [
                //   {
                //     required: true,
                //     validator: (_, value, callback) => {
                //       if (
                //         step === 2
                //         // ((Array.isArray(checkItems) && !checkItems.length) || !checkItems)
                //       ) {
                //         callback('请添加附件');
                //       } else {
                //         callback();
                //       }
                //     },
                //   },
                // ],
              })(<span>--</span>)}
            </StyledFormItem>
          );
        }
        if (Array.isArray(text) && text.length) {
          return (
            <SimpleFileList files={text}>
              <Button type="link" compact>
                {text[0].name}
              </Button>
            </SimpleFileList>
          );
        }
        return '--';
      },
    },
    {
      title: 'OLA',
      dataIndex: 'ola',
      fixed: 'right',
      render: (text, record) => (
        <span>
          {text !== null && text !== 0 ? `${text}${SLA_UNIT_TYPE_TEXT_MAP[record.olaUnit]}` : '--'}
        </span>
      ),
    },
  ];
  if (operation && Array.isArray(operation)) {
    list.push(...operation);
  }
  if (!Array.isArray(operation) && operation) {
    list.push(operation);
  }

  return list;
};

export default CustomerTable;
