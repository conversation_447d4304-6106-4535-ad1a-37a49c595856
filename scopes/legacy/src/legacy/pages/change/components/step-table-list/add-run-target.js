import React, { useState } from 'react';

import Form from '@ant-design/compatible/es/form';

import { Button } from '@manyun/base-ui.ui.button';
import { Modal } from '@manyun/base-ui.ui.modal';

import { RoomTypeSelect } from '@manyun/resource-hub.ui.room-type-select';

export function AddRunTarget({
  // stepCodes,
  stepMaps,
  form,
  stepId,
  addStep,
  info,
}) {
  const [modalVisible, setModalVisible] = useState();
  const { getFieldDecorator } = form;

  const toggoleAddStepModalVisible = () => {
    setModalVisible(visible => !visible);
  };
  const onSetting = () => {
    let stepInfo = stepMaps[stepId];
    form.validateFields(async (err, values) => {
      if (err) {
        return;
      }
      stepInfo = {
        ...stepInfo,
        opObjectName: values.opObject.map(item => item.label),
        opObjectCode: values.opObject.map(item => item.key),
      };
      addStep({ stepMaps: { ...stepMaps, [stepId]: stepInfo } });

      toggoleAddStepModalVisible();
    });
  };

  return (
    <div style={{ display: 'inline-block' }}>
      <Button type="link" style={{ padding: 0 }} onClick={toggoleAddStepModalVisible}>
        {Array.isArray(info?.opObjectName) && info.opObjectName.length ? '设置' : '添加跑位目标'}
      </Button>
      <Modal
        width={560}
        open={modalVisible}
        onCancel={toggoleAddStepModalVisible}
        title="添加跑位目标"
        onOk={onSetting}
        destroyOnClose
      >
        <Form colon={false}>
          <Form.Item label="跑位目标">
            {getFieldDecorator('opObject', {
              rules: [
                {
                  required: true,
                  message: '跑位目标必填！',
                },
              ],
              initialValue: Array.isArray(info?.opObjectName)
                ? info.opObjectName.map((item, index) => {
                    return {
                      label: item,
                      key: info.opObjectCode[index],
                    };
                  })
                : undefined,
            })(
              <RoomTypeSelect
                maxTagCount={4}
                style={{ width: '100%' }}
                mode="multiple"
                labelInValue
                showSearch
              />
            )}
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}

// const mapStateToProps = ({ common: { deviceCategory } }) => ({
//   nomalizedDeviceCategory: deviceCategory ? deviceCategory.normalizedList : {},
// });

export default Form.create('add-run-target-modal')(AddRunTarget);
