import React from 'react';
import { connect } from 'react-redux';

import { Typography } from '@manyun/base-ui.ui.typography';

import { SLA_UNIT_TYPE_TEXT_MAP } from '@manyun/ticket.model.change';

import {
  CHANGE_CHECK_METHOD_TEXT_MAP,
  CHANGE_STEP_TYPE_KEY_MAP,
  CHANGE_STEP_TYPE_TEXT_MAP,
} from '../../../../constants';
import { StyledFormItem, StyledTinyTable } from '../styled';
import InfluencesDeviceTable from './influences-device-table';

export function OpOtherTable({
  data,
  className,
  operation = null,
  checkItems,
  firstColumn,
  stepId,
  checkItemsOperation,
  type,
  addCheckItemsMap,
  stepCodes,
  step,
  getFieldDecorator,
  stepMaps,
  stepIndex,
  removeExceptionHandling,

  onSaveBatchSettingCheckPoint,
  batchSettingCheckedPoint,
}) {
  return (
    <div>
      <StyledTinyTable
        className={className}
        rowKey="id"
        columns={getColumns(operation)}
        dataSource={data}
        size="small"
        pagination={false}
        bordered={false}
        align="left"
        scroll={{ x: 'max-content' }}
      />
      <StyledFormItem>
        {getFieldDecorator(`step-device-checkItem-${stepId}`, {
          rules: [
            {
              required: true,
              validator: (_, value, callback) => {
                if (
                  step === 2 &&
                  ((Array.isArray(checkItems) && !checkItems.length) || !checkItems)
                ) {
                  callback('请至少添加一条观察目标');
                } else {
                  callback();
                }
              },
            },
          ],
        })(
          Array.isArray(checkItems) &&
            !!checkItems.length &&
            ([2, 3].includes(step) || type === 'templateView') ? (
            <InfluencesDeviceTable
              getFieldDecorator={getFieldDecorator}
              checkItems={checkItems}
              checkItemsOperation={checkItemsOperation}
              firstColumn={firstColumn}
              stepId={stepId}
              influencesText="观察"
              type={type}
              addCheckItemsMap={addCheckItemsMap}
              stepCodes={stepCodes}
              step={step}
              stepMaps={stepMaps}
              stepIndex={stepIndex}
              removeExceptionHandling={removeExceptionHandling}
              onSaveBatchSettingCheckPoint={onSaveBatchSettingCheckPoint}
              batchSettingCheckedPoint={batchSettingCheckedPoint}
            />
          ) : (
            <div></div>
          )
        )}
      </StyledFormItem>
    </div>
  );
}

const getColumns = operation => {
  const list = [
    {
      title: '步骤内容',
      dataIndex: 'stepDesc',
      width: 504,
      render: text => (
        <Typography.Text style={{ maxWidth: 504 }} ellipsis={{ tooltip: text }}>
          {text}
        </Typography.Text>
      ),
    },
    {
      title: '步骤类型',
      dataIndex: 'stepType',
      render: text => <span>{CHANGE_STEP_TYPE_TEXT_MAP[CHANGE_STEP_TYPE_KEY_MAP[text]]}</span>,
    },
    {
      title: '操作目标',
      dataIndex: 'opObjectName',
    },
    {
      title: '操作方法',
      dataIndex: 'operate',
    },
    {
      title: '验证方法',
      dataIndex: 'identifyWay',
      render: text => CHANGE_CHECK_METHOD_TEXT_MAP[text],
    },

    {
      title: 'OLA',
      dataIndex: 'ola',
      fixed: 'right',
      render: (text, record) => (
        <span>
          {text !== null && text !== 0 ? `${text}${SLA_UNIT_TYPE_TEXT_MAP[record.olaUnit]}` : '--'}
        </span>
      ),
    },
  ];
  if (operation) {
    list.push(operation);
  }
  return list;
};

export default connect(null, null, null, { forwardRef: true })(OpOtherTable);
