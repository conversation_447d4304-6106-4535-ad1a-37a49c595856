import React, { forwardRef, useImperativeHandle, useState } from 'react';
import { animated, useSpring } from 'react-spring';

import Form from '@ant-design/compatible/es/form';

import { Button } from '@manyun/base-ui.ui.button';
import { Empty } from '@manyun/base-ui.ui.empty';

import AddStepModal from './components/add-edit-step-modal';

export function AddStepButton({
  forwardedRef,
  showInstruction = false,
  stepCodes,
  stepMaps,
  addStep,
  step,
}) {
  const [modalVisible, setModalVisible] = useState();
  const [animating, setAnimating] = useState(false);
  const springProps = useSpring({
    to: [{ x: -10 }, { x: 10 }, { x: -10 }, { x: 10 }, { x: -10 }, { x: 10 }, { x: 0 }],
    from: {
      x: 0,
    },
    config: {
      duration: 100,
    },
    onRest: () => {
      setAnimating(false);
    },
  });

  const animateOnece = () => {
    setAnimating(true);
  };

  useImperativeHandle(forwardedRef, () => ({ animateOnece }));

  const toggoleAddStepModalVisible = () => {
    setModalVisible(prevVisible => !prevVisible);
  };

  const btn = (
    <Button
      type="link"
      onClick={toggoleAddStepModalVisible}
      style={{ marginLeft: showInstruction ? 0 : 171, padding: 0 }}
    >
      添加步骤
    </Button>
  );
  return (
    <div>
      {showInstruction && (
        <Empty
          image={<img alt="暂无数据" src="/images/empty.png" />}
          description={
            <animated.span
              style={
                animating
                  ? {
                      display: 'inline-block',
                      transform: springProps.x.interpolate(x => `translateX(${x}px)`),
                    }
                  : undefined
              }
            >
              至少添加一个步骤，{btn}
            </animated.span>
          }
        />
      )}
      {!showInstruction && stepCodes.length < 200 && btn}
      {modalVisible && (
        <AddStepModal
          addStep={addStep}
          title="新建步骤"
          toggoleAddStepModalVisible={toggoleAddStepModalVisible}
          stepCodes={stepCodes}
          stepMaps={stepMaps}
        />
      )}
    </div>
  );
}

export const AddStepButtonForm = Form.create('AddStepButtonRef')(AddStepButton);
export default forwardRef((props, ref) => <AddStepButtonForm forwardedRef={ref} {...props} />);
