import React from 'react';

import { Divider } from '@manyun/base-ui.ui.divider';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import { SLA_UNIT_TYPE_TEXT_MAP } from '@manyun/ticket.model.change';

import { CHANGE_STEP_TYPE_KEY_MAP, CHANGE_STEP_TYPE_TEXT_MAP } from '../../../../constants';
import { StyledFormItem, StyledTinyTable } from '../styled';

// import { Ellipsis } from '@manyun/dc-brain.legacy.components';

export function RunTable({
  data,
  className,
  operation = null,
  checkItems,
  step,
  getFieldDecorator,
  stepId,
}) {
  return (
    <StyledTinyTable
      bordered={false}
      className={className}
      rowKey="id"
      columns={getColumns(operation, step, getFieldDecorator, stepId)}
      dataSource={[{ ...data[0], checkItems }]}
      size="small"
      pagination={false}
      align="left"
      scroll={{ x: 'max-content' }}
    />
  );
}

const getColumns = (operation, step, getFieldDecorator, stepId) => {
  const list = [
    {
      title: '步骤内容',
      dataIndex: 'stepDesc',
      width: 504,
      render: text => (
        <Typography.Text style={{ maxWidth: 504 }} ellipsis={{ tooltip: text }}>
          {text}
        </Typography.Text>
      ),
    },
    {
      title: '步骤类型',
      dataIndex: 'stepType',
      render: text => <span>{CHANGE_STEP_TYPE_TEXT_MAP[CHANGE_STEP_TYPE_KEY_MAP[text]]}</span>,
    },
    {
      title: '操作目标',
      dataIndex: 'opObjectCode',
      render: (text, { opObjectName }, index) => {
        if (!text && step === 2) {
          return (
            <StyledFormItem>
              {getFieldDecorator(`opObjectCode-${index}-${stepId}`, {
                initialValue: text,
                rules: [{ required: true, message: '跑位目标为必填' }],
              })(<span>--</span>)}
            </StyledFormItem>
          );
        }
        if (Array.isArray(opObjectName) && opObjectName.length) {
          return (
            <Space size={0} split={<Divider type="vertical" spaceSize="mini" emphasis />}>
              {opObjectName?.map(item => (
                <span key={item}>{item}</span>
              ))}
            </Space>
          );
        }
        return '--';
      },
    },
    {
      title: 'OLA',
      dataIndex: 'ola',
      fixed: 'right',
      render: (text, record) => (
        <span>
          {text !== null && text !== 0 ? `${text}${SLA_UNIT_TYPE_TEXT_MAP[record.olaUnit]}` : '--'}
        </span>
      ),
    },
  ];
  if (operation && Array.isArray(operation)) {
    list.push(...operation);
  }
  if (!Array.isArray(operation) && operation) {
    list.push(operation);
  }

  return list;
};

export default RunTable;
