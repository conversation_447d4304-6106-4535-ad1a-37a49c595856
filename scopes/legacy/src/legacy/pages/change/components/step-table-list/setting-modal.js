import React, { useState } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import { Select } from '@galiojs/awesome-antd';

import { Button } from '@manyun/base-ui.ui.button';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Modal } from '@manyun/base-ui.ui.modal';

import { generateValidLimitsDataSource } from '@manyun/monitoring.model.point';

import {
  CHANGE_EXPECTATION_METHOD_KEY_MAP,
  CHANGE_REACT_TO_EXCEPTION_KEY_MAP,
  CHANGE_REACT_TO_EXCEPTION_TEXT_MAP,
} from '../../constants/index';
import JudgmentSelect from '../judgment-select';
import MaxInfluencesStepSelect from '../max-influences-step';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};

export function SettingModal({
  stepCodes,
  checkItemsMap,
  form,
  influencesStepText = '',
  stepId,
  addCheckItemsMap,
  type,
  info,
  nomalizedDeviceCategory,
  stepIndex,
  stepMaps,
}) {
  const [modalVisible, setModalVisible] = useState();
  const { getFieldDecorator } = form;

  const toggoleAddStepModalVisible = () => {
    setModalVisible(visible => !visible);
  };

  const onSetting = () => {
    const checkItem = checkItemsMap[stepId];
    form.validateFields(async (err, values) => {
      if (err) {
        return;
      }
      const list = checkItem.map(item => {
        let checkItemData = {
          ...values,
        };
        if (values.judgment && values.judgment === 'up') {
          checkItemData.operatorList = ['lt', 'le'];
        }
        if (values.judgment && values.judgment === 'down') {
          checkItemData.operatorList = ['gt', 'ge'];
        }
        if (item.dataType === 'AI' && values.operatorList) {
          checkItemData.operatorList = [values.operatorList];
        }
        if (item.dataType === 'DI') {
          checkItemData.operatorList = ['in'];
        }
        if (values.judgmentWay === CHANGE_EXPECTATION_METHOD_KEY_MAP.LIMIT_OF_ESTIMATION) {
          checkItemData.expectedValue = null;
        }
        if (item.metaCode === info.metaCode) {
          return { ...item, ...checkItemData };
        }
        return item;
      });

      addCheckItemsMap({ ...checkItemsMap, [stepId]: list });
      toggoleAddStepModalVisible();
    });
  };

  const onChangeJudgmentWay = () => {
    form.setFieldsValue({
      judgment: undefined,
      judgmentWay: undefined,
      operatorList: undefined,
      expectedValue: undefined,
    });
  };

  return (
    <div style={{ display: 'inline-block' }}>
      <Button
        type="link"
        onClick={toggoleAddStepModalVisible}
        style={{ padding: 0, height: 'auto' }}
      >
        设置
      </Button>
      <Modal
        style={{ minWidth: (480 / 1600) * 100 + '%', maxWidth: 480 }}
        visible={modalVisible}
        onCancel={toggoleAddStepModalVisible}
        title={`${
          nomalizedDeviceCategory[info.deviceType] &&
          nomalizedDeviceCategory[info.deviceType].metaName
        }-${info.pointName}`}
        onOk={onSetting}
        destroyOnClose
      >
        <Form colon={false} {...formItemLayout}>
          <Row>
            <JudgmentSelect
              info={info}
              onChangeJudgmentWay={onChangeJudgmentWay}
              type={type}
              form={form}
              getFieldDecorator={getFieldDecorator}
              generateValidLimitsDataSource={generateValidLimitsDataSource}
            />

            <Col span={24}>
              <Form.Item label="异常处理">
                {getFieldDecorator('exceptionHandle', {
                  rules: [
                    {
                      required: true,
                      message: '异常处理必填！',
                    },
                  ],
                  initialValue: CHANGE_REACT_TO_EXCEPTION_KEY_MAP[info.exceptionHandle],
                })(
                  <Select style={{ width: 200 }}>
                    {Object.entries(CHANGE_REACT_TO_EXCEPTION_TEXT_MAP).map(item => (
                      <Select.Option key={item[0]}>{item[1]}</Select.Option>
                    ))}
                  </Select>
                )}
              </Form.Item>
            </Col>

            <Col span={24}>
              <Form.Item label={influencesStepText}>
                {getFieldDecorator('maxInfluencesStep', {
                  rules: [{ required: true, message: `${influencesStepText}必选！` }],
                  initialValue: info.maxInfluencesStep || undefined,
                })(
                  <MaxInfluencesStepSelect
                    stepCodes={stepCodes}
                    stepIndex={stepIndex}
                    stepMaps={stepMaps}
                  />
                )}
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
}

const mapStateToProps = ({ common: { deviceCategory } }) => ({
  nomalizedDeviceCategory: deviceCategory ? deviceCategory.normalizedList : {},
});

export default connect(mapStateToProps, null)(Form.create('Setting-modal')(SettingModal));
