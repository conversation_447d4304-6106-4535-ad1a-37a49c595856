import React, { useState } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import PlusCircleOutlined from '@ant-design/icons/es/icons/PlusCircleOutlined';
import { Select } from '@galiojs/awesome-antd';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { Modal } from '@manyun/base-ui.ui.modal';

import {
  CHANGE_CHECK_METHOD_KEY_MAP,
  CHANGE_EXPECTATION_METHOD_KEY_MAP,
  CHANGE_REACT_TO_EXCEPTION_KEY_MAP,
  CHANGE_REACT_TO_EXCEPTION_TEXT_MAP,
  POINT_TYPE_KEY_MAP,
  POINT_TYPE_TEXT_MAP,
} from '../../../constants/index';
import JudgmentSelect from '../../judgment-select';
import MaxInfluencesStepSelect from '../../max-influences-step';
import PointCascader from '../../point-cascader';
import { checkItemNormalized } from '../constants';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};
export function InfluencesDeviceCustom({
  stepCodes,
  checkItems,
  form,
  influencesText = '',
  stepId,
  addCheckItemsMap,
  type,
  info = null,
  nomalizedDeviceCategory,
  isSetting,
  showIcon,
  stepIndex,
  stepMaps,
  deviceType,
  ticketType,
  dataType,
  disabledPointCode = [],
}) {
  const [modalVisible, setModalVisible] = useState();
  const [pointValue, setPointValue] = useState({});

  const { getFieldDecorator } = form;

  const toggoleAddStepModalVisible = () => {
    setModalVisible(visible => !visible);
  };
  const onChangeJudgmentWay = () => {
    form.setFieldsValue({
      judgment: undefined,
      judgmentWay: undefined,
      operatorList: undefined,
      expectedValue: undefined,
    });
  };
  const onSetting = () => {
    form.validateFields(async (err, values) => {
      if (err) {
        return;
      }
      let checkItemData = {
        ...values,
        ...pointValue,
        deviceType: info.deviceType,
      };
      const normalizedList = checkItemNormalized(checkItems, 'deviceType');
      let list = [];
      if (type === 'new') {
        checkItemData.merge = 0;
        checkItemData.metaCode = `${deviceType}_$$_${
          checkItemData.pointCode || checkItemData.pointName
        }`;
        checkItemData.identifyWay =
          values.pointType === POINT_TYPE_KEY_MAP.CUSTOM
            ? CHANGE_CHECK_METHOD_KEY_MAP.PERSON
            : CHANGE_CHECK_METHOD_KEY_MAP.SYSTEM;
        if (values.pointType === POINT_TYPE_KEY_MAP.SYSTEM) {
          checkItemData.dataType = pointValue.dataType.code;
          checkItemData.pointName = pointValue.name;
          if (values.judgment && values.judgment === 'up') {
            checkItemData.operatorList = ['lt', 'le'];
          }
          if (values.judgment && values.judgment === 'down') {
            checkItemData.operatorList = ['gt', 'ge'];
          }
          if (pointValue.dataType.code === 'AI' && values.operatorList) {
            checkItemData.operatorList = [values.operatorList];
          }
          if (pointValue.dataType.code === 'DI') {
            checkItemData.operatorList = ['in'];
          }
          if (values.judgmentWay === CHANGE_EXPECTATION_METHOD_KEY_MAP.LIMIT_OF_ESTIMATION) {
            checkItemData.expectedValue = null;
          }
        }

        for (let i in normalizedList) {
          if (i === info.deviceType) {
            normalizedList[i].push(checkItemData);
            const devicesList = normalizedList[i].map((item, index) => {
              if (index === 0) {
                return { ...item, merge: normalizedList[i].length, last: false };
              }
              if (index === normalizedList[i].length - 1) {
                return { ...item, last: true };
              }
              return { ...item, last: false };
            });
            list.push(...devicesList);
          } else {
            list.push(...normalizedList[i]);
          }
        }
      }
      if (type === 'edit') {
        list = checkItems.map(item => {
          if (item.metaCode === info.metaCode) {
            return {
              ...item,
              ...checkItemData,
              metaCode: `${item.deviceType}_$$_${checkItemData.pointName}`,
            };
          }
          return item;
        });
      }
      addCheckItemsMap({ [stepId]: list });
      toggoleAddStepModalVisible();
    });
  };

  return (
    <div style={{ display: 'inline-block' }}>
      {isSetting ? (
        <Button
          type="link"
          style={{ padding: 0, height: 'auto' }}
          onClick={toggoleAddStepModalVisible}
        >
          设置
        </Button>
      ) : (
        showIcon && (
          <PlusCircleOutlined
            onClick={toggoleAddStepModalVisible}
            style={{ color: `var(--${prefixCls}-success-color)`, marginRight: 8 }}
          />
        )
      )}

      <Modal
        style={{ minWidth: (409 / 1600) * 100 + '%', maxWidth: 409 }}
        visible={modalVisible}
        onCancel={toggoleAddStepModalVisible}
        title={
          nomalizedDeviceCategory[info.deviceType]
            ? `${nomalizedDeviceCategory[info.deviceType].metaName}-自定义测点`
            : info.deviceType
        }
        onOk={onSetting}
        destroyOnClose
      >
        <Form colon={false} {...formItemLayout}>
          <Row>
            {type === 'new' && (
              <Col span={24}>
                <Form.Item label="测点类型">
                  {getFieldDecorator('pointType', {
                    rules: [
                      {
                        required: true,
                        message: `测点类型必填！`,
                      },
                    ],
                    initialValue: type !== 'new' ? POINT_TYPE_KEY_MAP.CUSTOM : null,
                  })(
                    <Select style={{ width: 200 }}>
                      {Object.entries(POINT_TYPE_TEXT_MAP).map(item => (
                        <Select.Option key={item[0]}>{item[1]}</Select.Option>
                      ))}
                    </Select>
                  )}
                </Form.Item>
              </Col>
            )}
            {(type === 'edit' || form.getFieldValue('pointType') === POINT_TYPE_KEY_MAP.CUSTOM) && [
              <Col span={24} key="pointName">
                <Form.Item label="测点名称">
                  {getFieldDecorator('pointName', {
                    rules: [
                      {
                        required: true,
                        message: '测点名称必填！',
                      },
                      {
                        max: 20,
                        message: '最多输入 20 个字符！',
                      },
                    ],
                    initialValue: info.pointName,
                  })(<Input style={{ width: 200 }} />)}
                </Form.Item>
              </Col>,
              <Col span={24} key="expectedResult">
                <Form.Item label="预期判断">
                  {getFieldDecorator('expectedResult', {
                    rules: [
                      {
                        required: true,
                        message: '预期判断必填！',
                      },
                      {
                        max: 100,
                        message: '最多输入 100 个字符！',
                      },
                    ],
                    initialValue: info.expectedResult,
                  })(<Input style={{ width: 200 }} />)}
                </Form.Item>
              </Col>,
            ]}
            {form.getFieldValue('pointType') === POINT_TYPE_KEY_MAP.SYSTEM && [
              <Col span={24} key="pointName">
                <Form.Item label="设备测点">
                  {getFieldDecorator('pointName', {
                    rules: [
                      {
                        required: true,
                        message: '设备测点必填！',
                      },
                    ],
                  })(
                    <PointCascader
                      pointData={{ ...info, deviceType }}
                      disabledPointCode={disabledPointCode}
                      onChange={(_, __, values) => {
                        form.setFieldsValue({ pointName: values.pointName });
                        setPointValue({
                          ...values.triggerNode.props,
                          pointValueText: values.triggerNode.props.validLimits
                            ? values.triggerNode.props.validLimits.join(',')
                            : '',
                        });
                      }}
                    />
                  )}
                </Form.Item>
              </Col>,
              <JudgmentSelect
                info={{ ...info, ...pointValue }}
                onChangeJudgmentWay={onChangeJudgmentWay}
                type={ticketType}
                form={form}
                getFieldDecorator={getFieldDecorator}
                key="judgment-select"
                dataType={
                  type === 'new' ? pointValue.dataType && pointValue.dataType.code : dataType
                }
              />,
            ]}
            <Col span={24}>
              <Form.Item label="异常处理">
                {getFieldDecorator('exceptionHandle', {
                  rules: [
                    {
                      required: true,
                      message: '异常处理必填！',
                    },
                  ],
                  initialValue: CHANGE_REACT_TO_EXCEPTION_KEY_MAP[info.exceptionHandle],
                })(
                  <Select style={{ width: 200 }}>
                    {Object.entries(CHANGE_REACT_TO_EXCEPTION_TEXT_MAP).map(item => (
                      <Select.Option key={item[0]}>{item[1]}</Select.Option>
                    ))}
                  </Select>
                )}
              </Form.Item>
            </Col>

            <Col span={24}>
              <Form.Item label={`${influencesText}周期`}>
                {getFieldDecorator('maxInfluencesStep', {
                  rules: [{ required: true, message: `${influencesText}周期必选！` }],
                  initialValue: info.maxInfluencesStep || undefined,
                })(
                  <MaxInfluencesStepSelect
                    stepMaps={stepMaps}
                    stepCodes={stepCodes}
                    stepIndex={stepIndex}
                  />
                )}
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
}

const mapStateToProps = ({ common: { deviceCategory } }) => ({
  nomalizedDeviceCategory: deviceCategory ? deviceCategory.normalizedList : {},
});

export default connect(
  mapStateToProps,
  null
)(Form.create('influences-device-custom-modal')(InfluencesDeviceCustom));
