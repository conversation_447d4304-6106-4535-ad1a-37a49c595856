import React from 'react';
import { connect } from 'react-redux';

import { Select } from '@galiojs/awesome-antd';

import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { InputNumber } from '@manyun/base-ui.ui.input-number';

import { generateValidLimitsDataSource, getDiPointValueText } from '@manyun/monitoring.model.point';

import {
  CHANGE_CHECK_METHOD_TEXT_MAP,
  CHANGE_REACT_TO_EXCEPTION_KEY_MAP,
  CHANGE_REACT_TO_EXCEPTION_TEXT_MAP,
  LIMIT_TEXT_MAP,
  operatorList,
} from '../../../../constants';
import { generateValidLimitsDataSourceLabel, getMaxInfluencesStep } from '../../constants';
import Custom from '../influences-device-custom';
import { StyledFormItem, StyledTinyTable } from '../styled';

export function InfluencesDeviceTable({
  className,
  checkItems,
  firstColumn,
  stepId,
  checkItemsOperation,
  influencesText,
  type,
  nomalizedDeviceCategory,
  addCheckItemsMap,
  stepCodes,
  getFieldDecorator,
  stepMaps,
  step,
  stepIndex,
  removeExceptionHandling,
  onSaveBatchSettingCheckPoint,
  batchSettingCheckedPoint,
}) {
  // const [checkedPoint, setCheckedPoint] = useState([]);

  const onChangeOperatorList = (value, record) => {
    const list = checkItems.map(item => {
      if (item.metaCode === record.metaCode) {
        return {
          ...item,
          operatorList: [value],
        };
      }
      return item;
    });
    addCheckItemsMap({ [stepId]: list });
  };

  const onChangeExpectedValue = (value, record) => {
    const list = checkItems.map(item => {
      if (item.metaCode === record.metaCode && record.dataType === 'DI') {
        return {
          ...item,
          operatorList: ['in'],
          expectedValue: value,
        };
      }
      if (item.metaCode === record.metaCode && record.dataType === 'AI') {
        return {
          ...item,
          expectedValue: value,
        };
      }
      return item;
    });
    addCheckItemsMap({ [stepId]: list });
  };

  function validateCheckItemsExpectedValue(_, value, callback) {
    if (
      (value === null || value === undefined || (Array.isArray(value) && !value.length)) &&
      type !== 'templateView'
    ) {
      callback('预期判断为必填');
    } else {
      callback();
    }
  }

  const getCheckItemColumns = () => {
    let list = [
      {
        title: '观察设备类型',
        dataIndex: 'deviceType',
        className: 'merge-style',
        ellipsis: true,
        render: (text, record) => {
          if (!text) {
            return '';
          }
          const obj = {
            children: nomalizedDeviceCategory[text] && nomalizedDeviceCategory[text].metaName,
            props: { rowSpan: record.merge },
          };
          return obj;
        },
      },
      {
        title: '观察设备测点',
        dataIndex: 'pointName',
        ellipsis: true,
        render: (text, record, index) => {
          if (step === 2 && type !== 'newByTemplate') {
            const disabledPointCode = checkItems.map(item => item.metaCode);
            const settingCheckedPoint =
              (batchSettingCheckedPoint && batchSettingCheckedPoint[stepId]) || [];
            return (
              <div style={{ position: 'relative' }}>
                <Checkbox
                  style={{ marginRight: 8 }}
                  checked={settingCheckedPoint.includes(record.metaCode)}
                  onChange={e => {
                    if (e.target.checked) {
                      if (settingCheckedPoint) {
                        onSaveBatchSettingCheckPoint({
                          [stepId]: [...settingCheckedPoint, record.metaCode],
                        });
                      } else {
                        onSaveBatchSettingCheckPoint({ [stepId]: [record.metaCode] });
                      }
                    } else {
                      onSaveBatchSettingCheckPoint({
                        [stepId]: settingCheckedPoint.filter(item => item !== record.metaCode),
                      });
                    }
                  }}
                />
                {record.last && (
                  <Custom
                    showIcon={type !== 'templateView' ? true : false}
                    type="new"
                    stepCodes={stepCodes}
                    checkItems={checkItems}
                    stepId={stepId}
                    addCheckItemsMap={addCheckItemsMap}
                    info={{ deviceType: record.deviceType }}
                    influencesText={influencesText}
                    isSetting={false}
                    stepIndex={stepIndex}
                    stepMaps={stepMaps}
                    deviceType={record.deviceType}
                    ticketType={type}
                    disabledPointCode={disabledPointCode}
                  />
                )}
                {text}
              </div>
            );
          }
          return text;
        },
      },
      {
        title: '验证方式',
        dataIndex: 'identifyWay',
        render: text => <span>{CHANGE_CHECK_METHOD_TEXT_MAP[text]}</span>,
      },
      {
        title: '预期判断',
        dataIndex: 'expectedValue',
        ellipsis: true,
        render: (text, record, index) => {
          if (!record.pointCode) {
            return record.expectedResult;
          }
          if (
            type === 'newByTemplate' &&
            text !== null &&
            !record.limitOfEstimation &&
            record.dataType === 'DI'
          ) {
            const valid = record.pointValueText ? record.pointValueText.split(',') : [];

            return text
              .map(item => {
                return getDiPointValueText(Number(item), valid);
              })
              .join(',');
          }
          if (type === 'newByTemplate' && record.limitOfEstimation) {
            const valid = record.pointValueText ? record.pointValueText.split(',') : [];
            if (record.dataType === 'DI') {
              return (
                <StyledFormItem>
                  {getFieldDecorator(`expectedValue-${index}-${stepId}`, {
                    initialValue: text || [],
                    rules: [{ required: true, validator: validateCheckItemsExpectedValue }],
                  })(
                    <Select
                      style={{ width: 100 }}
                      mode="multiple"
                      onChange={value => onChangeExpectedValue(value, record)}
                    >
                      {generateValidLimitsDataSource(valid).map(item => (
                        <Select.Option key={item.value}>{item.label}</Select.Option>
                      ))}
                    </Select>
                  )}
                </StyledFormItem>
              );
            }
            if (record.dataType === 'AI') {
              let operators = operatorList;
              if (record.limitOfEstimation) {
                operators = record.limitOfEstimationOperatorList.map(item => {
                  return {
                    label: LIMIT_TEXT_MAP[item],
                    value: item,
                  };
                });
              }
              let min = -9999;
              let max = 9999;
              if (valid.length > 0) {
                min = getDiPointValueText('ge', valid);
                max = getDiPointValueText('le', valid);
              }
              return (
                <div style={{ display: 'flex' }}>
                  <StyledFormItem>
                    {getFieldDecorator(`operator-${index}-${stepId}`, {
                      rules: [
                        {
                          required: true,
                          message: '表达式为必填',
                        },
                      ],
                      initialValue:
                        Array.isArray(record.operatorList) && record.operatorList.length === 1
                          ? record.operatorList[0]
                          : undefined,
                    })(
                      <Select
                        style={{ width: 60, marginRight: 8 }}
                        onChange={value => onChangeOperatorList(value, record)}
                      >
                        {operators.map(item => (
                          <Select.Option key={item.value}>{item.label}</Select.Option>
                        ))}
                      </Select>
                    )}
                  </StyledFormItem>
                  <StyledFormItem>
                    {getFieldDecorator(`expectedValue-${index}-${stepId}`, {
                      rules: [{ required: true, validator: validateCheckItemsExpectedValue }],
                      initialValue: text,
                    })(
                      <InputNumber
                        formatter={value => (record.unit ? `${value}${record.unit}` : `${value}`)}
                        parser={value => {
                          let number = value;
                          if (!record.unit) {
                            return `${value}`;
                          }
                          Array.from(record.unit).forEach(char => {
                            number = number.replace(char, '');
                          });
                          return number;
                        }}
                        onChange={value => onChangeExpectedValue(value, record)}
                        style={{ width: 100 }}
                        precision={4}
                        min={min}
                        max={max}
                      />
                    )}
                  </StyledFormItem>
                </div>
              );
            }
          }

          if (type === 'template') {
            if (
              Array.isArray(record.operatorList) &&
              record.operatorList.length &&
              record.dataType === 'DI' &&
              record.expectedValue === null
            ) {
              return <span>现场判断</span>;
            }
            if (
              Array.isArray(record.operatorList) &&
              record.operatorList.length &&
              record.dataType === 'AI'
            ) {
              if (record.operatorList.length > 1 && record.operatorList.includes('lt')) {
                return <span>上升</span>;
              }
              if (record.operatorList.length > 1 && record.operatorList.includes('gt')) {
                return <span>下降</span>;
              }
            }
          }
          if (
            ((record.dataType === 'AI' && text !== null && text !== undefined) ||
              (record.dataType === 'DI' && text !== null && text !== undefined)) &&
            type !== 'templateView'
          ) {
            return (
              <StyledFormItem>
                <span>
                  {record.dataType === 'DI'
                    ? text
                        .map(item => {
                          return generateValidLimitsDataSourceLabel(record.pointValueText, item);
                        })
                        .join(',')
                    : `${LIMIT_TEXT_MAP[record.operatorList[0]]} ${record.expectedValue} ${
                        record.unit || ''
                      }`}
                </span>
              </StyledFormItem>
            );
          }
          if (type === 'templateView' || type === 'ticketView') {
            if (record.dataType === 'AI') {
              if (record.operatorList.length > 1) {
                return '现场判断';
              }
              return (
                <span>{`${LIMIT_TEXT_MAP[record.operatorList[0]]} ${record.expectedValue} ${
                  record.unit || ''
                }`}</span>
              );
            }
            if (record.dataType === 'DI') {
              if (record.expectedValue !== null) {
                return text
                  .map(item => {
                    return generateValidLimitsDataSourceLabel(record.pointValueText, item);
                  })
                  .join(',');
              }
              return '现场判断';
            }
          }
          return (
            <StyledFormItem>
              {getFieldDecorator(`expectedValue-${index}-${stepId}`, {
                initialValue: text,
                rules: [
                  {
                    required: true,
                    validator: validateCheckItemsExpectedValue,
                  },
                ],
              })(<span>--</span>)}
            </StyledFormItem>
          );
        },
      },
      {
        title: '异常处理',
        dataIndex: 'exceptionHandle',
        render: (text, _, index) => {
          if (!text) {
            return (
              <StyledFormItem>
                {getFieldDecorator(`exceptionHandle-${index}-${stepId}`, {
                  rules: [
                    {
                      required: true,
                      validator: (_, value, callback) => {
                        if (!text && type !== 'templateView') {
                          callback('异常处理为必填');
                        } else {
                          callback();
                        }
                      },
                    },
                  ],
                  initialValue: text,
                })(<span>--</span>)}
              </StyledFormItem>
            );
          } else {
            return (
              <span>
                {`${CHANGE_REACT_TO_EXCEPTION_TEXT_MAP[CHANGE_REACT_TO_EXCEPTION_KEY_MAP[text]]}`}
              </span>
            );
          }
        },
      },
      {
        title: `${influencesText}周期`,
        dataIndex: 'maxInfluencesStep',
        ellipsis: true,
        render: (text, ___, index) => {
          if (text) {
            return <span>{text ? getMaxInfluencesStep(stepMaps, text) : '--'}</span>;
          }
          return (
            <StyledFormItem>
              {getFieldDecorator(`exceptionHandle-${index}-${stepId}`, {
                rules: [
                  {
                    required: true,
                    validator: (_, value, callback) => {
                      if (!text && type !== 'templateView') {
                        callback(`${influencesText}周期为必填`);
                      } else {
                        callback();
                      }
                    },
                  },
                ],
                initialValue: text,
              })(<span>{text ? getMaxInfluencesStep(stepMaps, text) : '--'}</span>)}
            </StyledFormItem>
          );
        },
      },
    ];
    if (firstColumn) {
      list.unshift(firstColumn);
    }
    if (Array.isArray(checkItemsOperation)) {
      list.push(...checkItemsOperation);
    }
    if (!Array.isArray(checkItemsOperation) && checkItemsOperation) {
      list.push(checkItemsOperation);
    }
    if (removeExceptionHandling) {
      list = list.filter(item => item.dataIndex !== 'exceptionHandle');
    }
    return list;
  };

  return (
    <StyledTinyTable
      className={className}
      rowKey="metaCode"
      columns={getCheckItemColumns()}
      dataSource={checkItems}
      size="small"
      pagination={false}
      bordered={false}
      align="left"
      tableLayout="fixed"
    ></StyledTinyTable>
  );
}

const mapStateToProps = ({ common: { deviceCategory } }) => ({
  nomalizedDeviceCategory: deviceCategory ? deviceCategory.normalizedList : {},
});

export const InfluencesDeviceTableStyleConnect = connect(mapStateToProps)(InfluencesDeviceTable);
export default React.forwardRef((props, ref) => (
  <InfluencesDeviceTableStyleConnect xRef={ref} {...props} />
));
