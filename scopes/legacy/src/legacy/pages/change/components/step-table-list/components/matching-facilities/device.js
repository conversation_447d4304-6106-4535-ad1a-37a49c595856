import React from 'react';

import Form from '@ant-design/compatible/es/form';
import { ApiSelect } from '@galiojs/awesome-antd';
import { difference, differenceBy, get } from 'lodash';

import { Button } from '@manyun/base-ui.ui.button';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Transfer } from '@manyun/base-ui.ui.transfer';

// import { TinyTable, Ellipsis, DIErrorTooltip } from '@manyun/dc-brain.legacy.components';
import { GutterWrapper } from '@manyun/dc-brain.legacy.components';
import { fetchEquipmentListPage } from '@manyun/dc-brain.legacy.services/equipmentService';
import * as roomService from '@manyun/dc-brain.legacy.services/roomService';

import { StyledTinyTable } from '../styled';

const nomatchedColumns = (metaCategory, deviceType) => [
  {
    title: '设备名称',
    dataIndex: 'name',
  },
  {
    title: '位置',
    dataIndex: ['spaceGuid', 'roomTag'],
  },
  {
    title: '设备三级分类',
    dataIndex: 'deviceType',
    render() {
      return get(metaCategory, [deviceType, 'metaName'], deviceType);
    },
  },
];

const matchedColumns = (metaCategory, deviceType, mode) => [
  {
    title: '设备名称',
    dataIndex: `${['step', 'stepView'].includes(mode) ? 'name' : 'deviceTag'}`,
  },
  {
    title: '位置',
    dataIndex: 'roomTag',
  },
  {
    title: '设备三级分类',
    dataIndex: 'deviceType',
    render() {
      return get(metaCategory, [deviceType, 'metaName'], deviceType);
    },
  },
];
export class MatchingFacilities extends React.PureComponent {
  state = {
    checkedRadio: this.props.defaultTab,
    visible: false,
    noMatched: {
      list: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
      roomTag: null,
      tag: null,
      selectedRowKeys: [],
      selectedRows: [],
    },
    matchedSelectedRowKeys: [],
    matchedSelectedRows: [],
    matchedDeviceList: [],
    targetKeys: [],
  };

  onChangeRadio = value => {
    this.setState({ checkedRadio: value.target.value });
    if (value.target.value === 'matched') {
      const matchedDeviceList = this.getMatchedDeviceList(this.props.selectedDevice) || [];
      this.setState({ matchedDeviceList });
    }
  };

  getEquipmentList = async (pageNum, pageSize) => {
    const data = this.props.form.getFieldsValue();
    const { response, error } = await fetchEquipmentListPage({
      pageSize,
      pageNum,
      roomTag: null,
      tag: null,
      idcTag: this.props.idcTag,
      blockTag: this.props.blockTag,
      deviceTypeList: [this.props.deviceType],
      ...data,
      roomTags: data.roomTag ? [data.roomTag] : [],
      assetStatus: 'NORMAL',
    });
    if (error) {
      message.error('error');
    } else {
      this.setState({
        noMatched: {
          list: response.data,
          total: response.total,
          pageNum,
          pageSize,
          selectedRowKeys: [],
          selectedRows: [],
          ...data,
        },
      });
    }
  };

  handleVisible = () => {
    if (!this.state.visible) {
      this.getEquipmentList(1, 10);
    }
    this.setState(({ visible }) => ({
      visible: !visible,
      checkedRadio: this.props.defaultTab,
    }));

    // if (this.state.checkedRadio === 'matched') {
    const matchedDeviceList = this.getMatchedDeviceList(this.props.selectedDevice) || [];
    this.setState({
      matchedDeviceList,
      targetKeys: matchedDeviceList.map(
        item => item[`${['step', 'stepView'].includes(this.props.mode) ? 'code' : 'deviceGuid'}`]
      ),
    });
    // }
  };

  onSelectChangeNoMatched = (selectedRowKeys, selectedRows) => {
    if (this.props.mode === 'step') {
      const rows = selectedRows.map(item => {
        return {
          name: item.name,
          roomTag: item.spaceGuid.roomTag,
          code: item.guid,
        };
      });
      this.setState({
        noMatched: { ...this.state.noMatched, selectedRowKeys, selectedRows: rows },
      });
    } else {
      const rows = selectedRows.map(item => {
        return {
          deviceTag: item.name,
          roomTag: item.spaceGuid.roomTag,
          deviceGuid: item.guid,
        };
      });
      this.setState({
        noMatched: { ...this.state.noMatched, selectedRowKeys, selectedRows: rows },
      });
    }
  };

  onSelectChangeMatched = (selectedRowKeys, selectedRows) => {
    this.setState({
      matchedSelectedRowKeys: selectedRowKeys,
      matchedSelectedRows: selectedRows,
    });
  };

  onChangePageMatched = (pageNum, pageSize) => {
    // this.setState({ selectedRowKeys: [], selectedRows: [] });
    this.getEquipmentList(pageNum, pageSize);
  };

  onOk = (targetKeys, direction) => {
    const { matchedSelectedRows, noMatched } = this.state;
    const {
      stepId,
      setCheckItemDeviceMaps,
      deviceType,
      selectedDevice = [],
      setStepMatchObjectInfoMaps,
      mode,
      checkItemDeviceMaps,
      matchObjectInfoMaps,
    } = this.props;
    if (mode === 'checkItem') {
      if (direction === 'right' && noMatched.selectedRows.length) {
        message.success('添加成功');
        const selected = noMatched.selectedRows
          .map(item => {
            return {
              [`${mode === 'step' ? 'name' : 'deviceTag'}`]: item.name,
              roomTag: item.spaceGuid.roomTag,
              deviceGuid: item.guid,
            };
          })
          .concat(selectedDevice);
        setCheckItemDeviceMaps({
          stepId,
          selected: selected,
          deviceType,
        });
        this.setState({
          matchedDeviceList: selected,
        });
      }
      if (direction === 'left' && matchedSelectedRows.length) {
        const deviceList = get(get(checkItemDeviceMaps, stepId), deviceType);
        const selectedDeviceList = differenceBy(deviceList, matchedSelectedRows, 'deviceGuid');
        message.success('移除成功');
        setCheckItemDeviceMaps({
          stepId,
          selected: selectedDeviceList,
          deviceType,
        });
        const matchedDeviceList = this.getMatchedDeviceList(selectedDeviceList) || [];
        this.setState(({ visible, noMatched }) => ({
          noMatched: {
            ...noMatched,
            pageNum: 1,
            pageSize: 10,
            selectedRowKeys: [],
            selectedRows: [],
          },
          matchedDeviceList,
        }));
      }
    }
    if (mode === 'step') {
      if (direction === 'right' && noMatched.selectedRows.length) {
        const selected = noMatched.selectedRows
          .map(item => {
            return {
              [`${mode === 'step' ? 'name' : 'deviceTag'}`]: item.name,
              roomTag: item.spaceGuid.roomTag,
              code: item.guid,
            };
          })
          .concat(selectedDevice);
        message.success('添加成功');
        setStepMatchObjectInfoMaps({
          stepId,
          selected: selected,
        });
        this.setState(({ visible, noMatched }) => ({
          matchedDeviceList: selected,
        }));
      }
      if (direction === 'left' && matchedSelectedRows.length) {
        const deviceList = get(matchObjectInfoMaps, stepId);
        const selectedDeviceList = differenceBy(deviceList, matchedSelectedRows, 'code');
        message.success('移除成功');
        const matchedDeviceList = this.getMatchedDeviceList(selectedDeviceList) || [];
        setStepMatchObjectInfoMaps({
          stepId,
          selected: selectedDeviceList,
          matchedDeviceList,
        });
        this.setState(({ visible, noMatched }) => ({
          matchedDeviceList,
        }));
      }
    }
    this.setState(({ noMatched }) => ({
      noMatched: {
        ...noMatched,
        selectedRowKeys: [],
        selectedRows: [],
      },
      matchedSelectedRowKeys: [],
      matchedSelectedRows: [],
      targetKeys,
    }));
    // this.handleVisible();
  };

  handleSearchNoMatched = () => {
    this.getEquipmentList(1, 10);
  };

  handleSearchMatch = () => {
    const matchedDeviceList = this.getMatchedDeviceList(this.props.selectedDevice) || [];
    this.setState({ matchedDeviceList });
  };

  getMatchedDeviceList = selectedDevice => {
    const { matchedRoomTag, matchedTag } = this.props.form.getFieldsValue();
    const list = selectedDevice.filter(item => {
      let result = true;
      if (matchedRoomTag) {
        result = item.roomTag === matchedRoomTag;
      }
      if (matchedTag) {
        result = result && !!item.deviceTag.includes(matchedTag);
      }
      return result;
    });
    return list;
  };

  render() {
    const { form, btnText, metaCategory, deviceType, mode, idcTag, blockTag } = this.props;
    const { getFieldDecorator } = form;
    const {
      visible,
      noMatched,
      matchedSelectedRowKeys,
      matchedSelectedRows,
      matchedDeviceList,
      targetKeys,
    } = this.state;
    const formItemLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 18 },
      },
    };

    return (
      <div>
        <Button type="link" onClick={this.handleVisible} style={{ padding: 0 }}>
          {btnText}
        </Button>
        <Modal
          visible={visible}
          style={{ minWidth: (1200 / 1600) * 100 + '%', maxWidth: 1200 }}
          destroyOnClose
          // onOk={this.onOk}
          onCancel={this.handleVisible}
          // okText={checkedRadio === 'matched' ? '移除' : '添加'}
          // cancelText="关闭"
          footer={null}
          title="匹配设备"
        >
          {mode !== 'stepView' && mode !== 'checkItemView' && (
            <Transfer
              showSelectAll={false}
              titles={['未匹配', '已匹配']}
              targetKeys={targetKeys}
              dataSource={noMatched.list}
              rowKey={record => {
                return record.guid;
              }}
              onChange={this.onOk}
            >
              {({
                direction,
                onItemSelectAll,
                onItemSelect,
                selectedKeys: listSelectedKeys,
                // disabled: listDisabled,
              }) => {
                return direction === 'right' ? (
                  <StyledTinyTable
                    rowKey={`${['step', 'stepView'].includes(mode) ? 'code' : 'deviceGuid'}`}
                    rowSelection={
                      ['checkItemView', 'stepView'].includes(mode)
                        ? null
                        : {
                            onSelectAll: (selected, selectedRows) => {
                              const treeSelectedKeys = selectedRows
                                .filter(item => !item.disabled)
                                .map(
                                  record =>
                                    record[
                                      `${
                                        ['step', 'stepView'].includes(mode) ? 'code' : 'deviceGuid'
                                      }`
                                    ]
                                );
                              const diffKeys = selected
                                ? difference(treeSelectedKeys, listSelectedKeys)
                                : difference(listSelectedKeys, treeSelectedKeys);
                              onItemSelectAll(diffKeys, selected);
                              this.setState({
                                matchedSelectedRows: selectedRows,
                                matchedSelectedRowKeys: selectedRows.map(
                                  item =>
                                    item[
                                      `${
                                        ['step', 'stepView'].includes(mode) ? 'code' : 'deviceGuid'
                                      }`
                                    ]
                                ),
                              });
                            },
                            onSelect: (record, selected) => {
                              const guid =
                                record[
                                  `${['step', 'stepView'].includes(mode) ? 'code' : 'deviceGuid'}`
                                ];
                              onItemSelect(guid, selected);
                              if (selected) {
                                this.setState(
                                  ({ matchedSelectedRows, matchedSelectedRowKeys }) => ({
                                    matchedSelectedRows: [...matchedSelectedRows, record],
                                    matchedSelectedRowKeys: [...matchedSelectedRowKeys, guid],
                                  })
                                );
                              } else {
                                this.setState(
                                  ({ matchedSelectedRows, matchedSelectedRowKeys }) => ({
                                    matchedSelectedRows: matchedSelectedRows.filter(
                                      selectedItem =>
                                        selectedItem[
                                          `${
                                            ['step', 'stepView'].includes(mode)
                                              ? 'code'
                                              : 'deviceGuid'
                                          }`
                                        ] !== guid
                                    ),
                                    matchedSelectedRowKeys: matchedSelectedRowKeys.filter(
                                      i => i !== guid
                                    ),
                                  })
                                );
                              }
                            },
                            selectedRowKeys: listSelectedKeys,
                            // onChange: this.onSelectChangeMatched,
                          }
                    }
                    dataSource={matchedDeviceList}
                    columns={matchedColumns(metaCategory, deviceType, mode)}
                    pagination={{
                      total: matchedDeviceList.length,
                    }}
                  />
                ) : (
                  <StyledTinyTable
                    rowKey="guid"
                    rowSelection={{
                      onSelectAll: (selected, selectedRows) => {
                        const rows = selectedRows.filter(Boolean);
                        const treeSelectedKeys = rows
                          .filter(item => item && !item.disabled)
                          .map(({ guid }) => guid);
                        const diffKeys = selected
                          ? difference(treeSelectedKeys, listSelectedKeys)
                          : difference(listSelectedKeys, treeSelectedKeys);
                        this.setState(({ noMatched }) => ({
                          noMatched: {
                            ...noMatched,
                            selectedRowKeys: rows.map(item => item.guid),
                            selectedRows: rows,
                          },
                        }));
                        onItemSelectAll(diffKeys, selected);
                      },
                      onSelect: (record, selected) => {
                        if (selected) {
                          this.setState(({ noMatched }) => ({
                            noMatched: {
                              ...noMatched,
                              selectedRowKeys: [...noMatched.selectedRowKeys, record.guid],
                              selectedRows: [...noMatched.selectedRows, record],
                            },
                          }));
                        } else {
                          this.setState(({ noMatched }) => ({
                            noMatched: {
                              ...noMatched,
                              selectedRowKeys: noMatched.selectedRowKeys.filter(
                                noMatchedItem => record.guid !== noMatchedItem
                              ),
                              selectedRows: noMatched.selectedRows.filter(
                                noMatchedItem => record.guid !== noMatchedItem.guid
                              ),
                            },
                          }));
                        }

                        onItemSelect(record.guid, selected);
                      },
                      selectedRowKeys: listSelectedKeys,
                      getCheckboxProps: record => ({
                        disabled:
                          matchedDeviceList &&
                          !!matchedDeviceList.filter(item => {
                            if (['step', 'stepView'].includes(mode) && item.code === record.guid) {
                              return true;
                            }
                            if (mode === 'checkItem' && item.deviceGuid === record.guid) {
                              return true;
                            }
                            return false;
                          }).length,
                      }),
                    }}
                    // exportServices={() => downloadEquipment(this.getParams())}
                    dataSource={noMatched.list}
                    columns={nomatchedColumns(metaCategory, deviceType, mode)}
                    // actionsWrapperStyle={{ justifyContent: 'flex-end' }}
                    pagination={{
                      total: noMatched.total,
                      current: noMatched.pageNum,
                      pageSize: noMatched.pageSize,
                      onChange: this.onChangePageMatched,
                    }}
                  />
                );
              }}
            </Transfer>
          )}
          <GutterWrapper mode="vertical">
            {/* {mode !== 'stepView' && mode !== 'checkItemView' && (
              <Radio.Group onChange={this.onChangeRadio} defaultValue={defaultTab}>
                <Radio.Button value="matched">已匹配</Radio.Button>
                <Radio.Button value="noMatched">未匹配</Radio.Button>
              </Radio.Group>
            )} */}
            {/* {checkedRadio === 'noMatched' && mode !== 'stepView' && mode !== 'checkItemView' && (
              <GutterWrapper mode="vertical">
                <Form mode="vertical" colon={false} {...formItemLayout}>
                  <Row gutter={16}>
                    <Col span={8}>
                      <Form.Item label="包间">
                        {getFieldDecorator('roomTag')(
                          <ApiSelect
                            showSearch
                            fieldNames={{ label: 'tag', value: 'tag' }}
                            dataService={async () => {
                              const { response } = await roomService.fetchRoomPage({
                                pageSize: 100,
                                pageNum: 1,
                                idcTag,
                                blockTag,
                              });
                              if (response) {
                                return Promise.resolve(response.data);
                              } else {
                                return Promise.resolve([]);
                              }
                            }}
                          />
                        )}
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="设备名称">{getFieldDecorator('name')(<Input />)}</Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item>
                        <GutterWrapper>
                          <Button type="primary" onClick={this.handleSearchNoMatched}>
                            搜索
                          </Button>
                          <Button
                            onClick={() => {
                              form.resetFields(['name', 'roomTag']);
                              this.getEquipmentList(1, 10);
                            }}
                          >
                            重置
                          </Button>
                        </GutterWrapper>
                      </Form.Item>
                    </Col>
                  </Row>
                </Form>
                <StyledTinyTable
                  rowKey="guid"
                  rowSelection={{
                    selectedRows: noMatched.selectedRows,
                    selectedRowKeys: noMatched.selectedRowKeys,
                    onChange: this.onSelectChangeNoMatched,
                    getCheckboxProps: record => ({
                      disabled:
                        matchedDeviceList &&
                        !!matchedDeviceList.filter(item => {
                          if (['step', 'stepView'].includes(mode) && item.code === record.guid) {
                            return true;
                          }
                          if (mode === 'checkItem' && item.deviceGuid === record.guid) {
                            return true;
                          }
                          return false;
                        }).length,
                    }),
                  }}
                  // exportServices={() => downloadEquipment(this.getParams())}
                  dataSource={noMatched.list}
                  columns={nomatchedColumns(metaCategory, deviceType, mode)}
                  // actionsWrapperStyle={{ justifyContent: 'flex-end' }}
                  pagination={{
                    total: noMatched.total,
                    current: noMatched.pageNum,
                    pageSize: noMatched.pageSize,
                    onChange: this.onChangePageMatched,
                  }}
                />
              </GutterWrapper>
            )} */}
            {(mode === 'stepView' || mode === 'checkItemView') && (
              <GutterWrapper mode="vertical">
                {!['checkItemView', 'stepView'].includes(mode) && (
                  <Form mode="vertical" colon={false} {...formItemLayout}>
                    <Row gutter={16}>
                      <Col span={8}>
                        <Form.Item label="包间">
                          {getFieldDecorator('matchedRoomTag')(
                            <ApiSelect
                              style={{ width: '100%' }}
                              showSearch
                              fieldNames={{ label: 'tag', value: 'tag' }}
                              dataService={async () => {
                                const { response } = await roomService.fetchRoomPage({
                                  pageSize: 100,
                                  pageNum: 1,
                                  idcTag,
                                  blockTag,
                                });
                                if (response) {
                                  return Promise.resolve(response.data);
                                } else {
                                  return Promise.resolve([]);
                                }
                              }}
                            />
                          )}
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item label="设备名称">
                          {getFieldDecorator('matchedTag')(<Input />)}
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item>
                          <GutterWrapper>
                            <Button type="primary" onClick={this.handleSearchMatch}>
                              搜索
                            </Button>
                            <Button
                              onClick={() => {
                                form.resetFields(['matchedTag', 'matchedRoomTag']);
                                this.handleSearchMatch();
                                // this.setState({ matchedTag: null, matchedRoomTag: null });
                              }}
                            >
                              重置
                            </Button>
                          </GutterWrapper>
                        </Form.Item>
                      </Col>
                    </Row>
                  </Form>
                )}
                <StyledTinyTable
                  rowKey={`${['step', 'stepView'].includes(mode) ? 'code' : 'deviceGuid'}`}
                  rowSelection={
                    ['checkItemView', 'stepView'].includes(mode)
                      ? null
                      : {
                          selectedRowKeys: matchedSelectedRowKeys,
                          selectedRows: matchedSelectedRows,
                          onChange: this.onSelectChangeMatched,
                        }
                  }
                  dataSource={matchedDeviceList}
                  columns={matchedColumns(metaCategory, deviceType, mode)}
                  pagination={{
                    total: matchedDeviceList.length,
                  }}
                />
              </GutterWrapper>
            )}
          </GutterWrapper>
        </Modal>
      </div>
    );
  }
}
export default Form.create()(MatchingFacilities);
