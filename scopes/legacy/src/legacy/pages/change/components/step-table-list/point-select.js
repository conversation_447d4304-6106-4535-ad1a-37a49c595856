import { EditableTable, Form, Select } from '@galiojs/awesome-antd';
import cloneDeep from 'lodash.clonedeep';
import React, { useState } from 'react';
import shortid from 'shortid';

import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Radio } from '@manyun/base-ui.ui.radio';

import {
  AssetClassificationApiTreeSelect,
  GutterWrapper,
  PointsSelectModalButton,
} from '@manyun/dc-brain.legacy.components';

import { CHANGE_REACT_TO_EXCEPTION_TEXT_MAP } from '../../constants/index';
import MaxInfluencesStepSelect from '../max-influences-step';
import { checkItemNormalized } from './constants';

export function CopyPoint({
  stepCodes,
  checkItemsMap,
  stepId,
  addCheckItemsMap,
  stepMaps,
  disabledTreeNodeKeys,
  onOk,
  stepIndex,
  metaCategory,
  idcTag,
  blockTag,
  type,
}) {
  const [modalVisible, setModalVisible] = useState();
  const [pointType, setPointType] = useState();
  const [dataSource, setDataSource] = useState([]);
  const [editingRowKey, setEditingRowKey] = useState();
  const [deleteByCancel, setDeleteByCancel] = useState(false);

  const [form] = Form.useForm();

  const toggoleAddStepModalVisible = () => {
    setModalVisible(visible => !visible);
  };

  const onSetting = () => {
    let list = checkItemsMap ? checkItemsMap[stepId] || [] : [];

    dataSource.forEach(item => {
      // const newId = shortid();
      list = [
        ...list,
        {
          ...item,
          identifyWay: 'PERSON',
          metaCode: `${item.deviceType}_$$_${item.pointName}`,
          pointType: 'custom',
        },
      ];
    });
    const checkItemList = [];
    const normalizedList = checkItemNormalized(list, 'deviceType');

    for (const i in normalizedList) {
      const devicesList = normalizedList[i].map((item, index) => {
        if (index === 0 && normalizedList[i].length === 1) {
          return { ...item, last: true, merge: 1 };
        }
        if (index === 0) {
          return { ...item, merge: normalizedList[i].length, last: false };
        }
        if (index === normalizedList[i].length - 1) {
          return { ...item, last: true, merge: 0 };
        }
        return { ...item, last: false, merge: 0 };
      });
      checkItemList.push(...devicesList);
    }
    addCheckItemsMap({ [stepId]: checkItemList });
    setDataSource([]);
    setEditingRowKey(null);
    setDeleteByCancel(false);
    setModalVisible(false);
  };

  return (
    <div style={{ display: 'inline-block' }}>
      <Button
        type="link"
        style={{ padding: 0, height: 'auto' }}
        onClick={toggoleAddStepModalVisible}
      >
        添加观察目标
      </Button>
      <Modal
        style={{
          minWidth: ((pointType === 'custom' ? 1200 : 480) / 1600) * 100 + '%',
          maxWidth: 1200,
        }}
        visible={modalVisible}
        title="添加观察目标"
        destroyOnClose
        okButtonProps={{ disabled: editingRowKey || !dataSource.length || pointType === 'system' }}
        onOk={onSetting}
        onCancel={toggoleAddStepModalVisible}
      >
        <GutterWrapper direction="vertical">
          <Radio.Group value={pointType} onChange={e => setPointType(e.target.value)}>
            <Radio value="system">系统测点</Radio>
            <Radio value="custom">自定义测点</Radio>
          </Radio.Group>
          <br />
          {pointType === 'system' && (
            <PointsSelectModalButton
              title="添加观察目标"
              text="添加观察目标"
              disabledTreeNodeKeys={disabledTreeNodeKeys}
              visibleLoadTypes={['snDevice']}
              invalidPoint={
                type === 'template' ? {} : { invalid: true, blockGuid: `${idcTag}.${blockTag}` }
              }
              onOk={({ pointsMap }) => {
                onOk({ pointsMap });
                toggoleAddStepModalVisible();
              }}
            />
          )}
          {pointType === 'custom' && (
            <Button
              key="create"
              type="primary"
              disabled={editingRowKey ? true : false}
              onClick={() => {
                const newId = shortid();
                setDataSource(prev => [
                  {
                    id: newId,
                  },
                  ...prev,
                ]);
                setEditingRowKey(newId);
                setDeleteByCancel(true);
              }}
            >
              添加测点
            </Button>
          )}
          {pointType === 'custom' && (
            <EditableTable
              form={form}
              rowKey="id"
              size="small"
              showActionsColumn
              dataSource={dataSource}
              scroll={{ x: true }}
              columns={getColumns({
                stepMaps,
                stepCodes,
                stepIndex,
                metaCategory,
              })}
              editingRowKey={editingRowKey}
              onSave={(currentKey, records) => {
                setDataSource(records);
                setEditingRowKey(null);
                setDeleteByCancel(false);
              }}
              onEdit={rowKey => setEditingRowKey(rowKey)}
              onCancel={() => {
                if (deleteByCancel) {
                  const nextData = cloneDeep(dataSource);
                  const idx = nextData.findIndex(record => record.id === editingRowKey);
                  if (idx > -1) {
                    nextData.splice(idx, 1);
                  }
                  setDataSource(nextData);
                  setEditingRowKey(null);
                  setDeleteByCancel(false);
                } else {
                  setEditingRowKey(null);
                }
              }}
              onDelete={(rowKey, data) => {
                let editKey = editingRowKey;
                if (rowKey === editingRowKey) {
                  editKey = null;
                }
                setDataSource(data);
                setEditingRowKey(editKey);
              }}
            />
          )}
        </GutterWrapper>
      </Modal>
    </div>
  );
}

export default CopyPoint;

const getColumns = ({ stepMaps, stepCodes, stepIndex, metaCategory }) => [
  {
    title: '设备类型',
    dataIndex: 'deviceType',
    editable: true,
    render: text => metaCategory?.[text]?.metaName,
    editingCtrl: () => {
      return (
        <AssetClassificationApiTreeSelect
          dataType={['snDevice']}
          category="categorycode"
          disabledDepths={[0, 1]}
          requestOnDidMount
          style={{ width: 200 }}
        />
      );
    },
    formItemProps: {
      rules: [{ required: true, message: '设备类型必选！' }],
    },
  },
  {
    title: '测点名称',
    dataIndex: 'pointName',
    editable: true,
    editingCtrl: () => {
      return <Input style={{ width: 200 }} />;
    },
    formItemProps: {
      rules: [
        { required: true, message: '测点名称必填！' },
        {
          max: 20,
          message: '最多输入 20 个字符！',
        },
      ],
    },
  },
  {
    title: '预期判断',
    dataIndex: 'expectedResult',
    editable: true,
    editingCtrl: () => {
      return <Input style={{ width: 200 }} />;
    },
    formItemProps: {
      rules: [
        { required: true, message: '预期判断必填！' },
        {
          max: 100,
          message: '最多输入 100 个字符！',
        },
      ],
    },
  },
  {
    title: '异常处理',
    dataIndex: 'exceptionHandle',
    editable: true,
    editingCtrl: () => {
      return (
        <Select style={{ width: 200 }}>
          {Object.entries(CHANGE_REACT_TO_EXCEPTION_TEXT_MAP).map(item => (
            <Select.Option key={item[0]}>{item[1]}</Select.Option>
          ))}
        </Select>
      );
    },
    formItemProps: {
      rules: [{ required: true, message: '异常处理必选！' }],
    },
    render: exceptionHandle => CHANGE_REACT_TO_EXCEPTION_TEXT_MAP[exceptionHandle],
  },
  {
    title: '观察周期',
    dataIndex: 'maxInfluencesStep',
    editable: true,
    editingCtrl: () => {
      return (
        <MaxInfluencesStepSelect stepMaps={stepMaps} stepCodes={stepCodes} stepIndex={stepIndex} />
      );
    },
    formItemProps: {
      rules: [{ required: true, message: '观察周期必选！' }],
    },
    render: maxInfluencesStep => stepMaps[maxInfluencesStep]?.stepName,
  },
];
