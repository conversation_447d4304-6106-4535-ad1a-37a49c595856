import React, { useState } from 'react';

import omit from 'lodash/omit';
import uniqBy from 'lodash/uniqBy';

import { Button } from '@manyun/base-ui.ui.button';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Transfer } from '@manyun/base-ui.ui.transfer';

import { checkItemNormalized } from './constants';

export function CopyPoint({ stepCodes, checkItemsMap, stepId, addCheckItemsMap, stepMaps }) {
  const [modalVisible, setModalVisible] = useState();
  const [targetKeys, setTargetKeys] = useState([]);
  const [selectedKeys, setSelectedKeys] = useState([]);

  const toggoleAddStepModalVisible = () => {
    setModalVisible(visible => !visible);
  };

  const onSetting = () => {
    if (!checkItemsMap) {
      setModalVisible(false);

      return;
    }
    let list = checkItemsMap[stepId] || [];

    targetKeys.forEach(item => {
      if (checkItemsMap[item]) {
        list = [...list, ...checkItemsMap[item].map(item => omit(item, 'maxInfluencesStep'))];
      }
    });
    list = uniqBy(list, 'metaCode');
    let checkItemList = [];
    const normalizedList = checkItemNormalized(list, 'deviceType');

    for (let i in normalizedList) {
      const devicesList = normalizedList[i].map((item, index) => {
        if (index === 0 && normalizedList[i].length === 1) {
          return { ...item, last: true, merge: 1 };
        }
        if (index === 0) {
          return { ...item, merge: normalizedList[i].length, last: false };
        }
        if (index === normalizedList[i].length - 1) {
          return { ...item, last: true, merge: 0 };
        }
        return { ...item, last: false, merge: 0 };
      });
      checkItemList.push(...devicesList);
    }
    addCheckItemsMap({ [stepId]: checkItemList });
    setModalVisible(false);
    setTargetKeys([]);
    setSelectedKeys([]);
  };

  const dataSource = stepCodes
    .map(item => {
      if (item === stepId || stepMaps[item].stepType !== stepMaps[stepId].stepType) {
        return false;
      }
      return {
        key: item,
        title: stepMaps[item].stepName,
      };
    })
    .filter(Boolean);

  const handleChange = nextTargetKeys => {
    setTargetKeys(nextTargetKeys);
  };

  const handleSelectChange = (sourceSelectedKeys, targetSelectedKeys) => {
    setSelectedKeys([...sourceSelectedKeys, ...targetSelectedKeys]);
  };

  return (
    <div style={{ display: 'inline-block' }}>
      <Button
        type="link"
        style={{ padding: 0, height: 'auto' }}
        onClick={toggoleAddStepModalVisible}
      >
        复制观察目标
      </Button>
      <Modal
        style={{ minWidth: (480 / 1600) * 100 + '%', maxWidth: 480 }}
        visible={modalVisible}
        onCancel={toggoleAddStepModalVisible}
        title="复制步骤"
        destroyOnClose
        onOk={onSetting}
        okButtonProps={{ disabled: !targetKeys.length }}
      >
        <Transfer
          dataSource={dataSource}
          titles={['步骤', '已选择']}
          targetKeys={targetKeys}
          selectedKeys={selectedKeys}
          onChange={handleChange}
          onSelectChange={handleSelectChange}
          render={item => item.title}
        />
      </Modal>
    </div>
  );
}

export default CopyPoint;
