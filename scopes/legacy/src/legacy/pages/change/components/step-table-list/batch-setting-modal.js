import React, { useState } from 'react';

import Form from '@ant-design/compatible/es/form';
import { Select } from '@galiojs/awesome-antd';

import { Button } from '@manyun/base-ui.ui.button';
import { Modal } from '@manyun/base-ui.ui.modal';

// import { generateValidLimitsDataSource } from '@manyun/monitoring.model.point';
import { CHANGE_REACT_TO_EXCEPTION_TEXT_MAP } from '../../constants/index';
import MaxInfluencesStepSelect from '../max-influences-step';

// import JudgmentSelect from '../judgment-select';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};

export function BatchSettingModal({
  stepCodes,
  checkItemsMap,
  form,
  influencesStepText = '',
  stepId,
  addCheckItemsMap,
  stepIndex,
  stepMaps,
  onSaveBatchSettingCheckPoint,
  batchSettingCheckedPoint,
  type,
}) {
  const [modalVisible, setModalVisible] = useState();
  const { getFieldDecorator } = form;

  const toggoleAddStepModalVisible = () => {
    setModalVisible(visible => !visible);
  };

  const onSetting = () => {
    form.validateFields(async (err, values) => {
      if (err) {
        return;
      }
      const newValues = {};
      if (values.maxInfluencesStep) {
        newValues.maxInfluencesStep = values.maxInfluencesStep;
      }
      if (values.exceptionHandle) {
        newValues.exceptionHandle = values.exceptionHandle;
      }
      const checkItem = checkItemsMap[stepId];
      const checkPoint = batchSettingCheckedPoint ? batchSettingCheckedPoint[stepId] : [];
      const list = checkItem.map(item => {
        if (Array.isArray(checkPoint) && checkPoint.length && !checkPoint.includes(item.metaCode)) {
          return { ...item };
        }
        return { ...item, ...newValues };
      });
      addCheckItemsMap({ ...checkItemsMap, [stepId]: list });
      toggoleAddStepModalVisible();
      onSaveBatchSettingCheckPoint({});
    });
  };

  // const onChangeJudgmentWay = () => {
  //   form.setFieldsValue({
  //     judgment: undefined,
  //     judgmentWay: undefined,
  //     operatorList: undefined,
  //     expectedValue: undefined,
  //   });
  // };

  return (
    <div style={{ display: 'inline-block' }}>
      <Button
        type="link"
        style={{ padding: 0, height: 'auto' }}
        onClick={toggoleAddStepModalVisible}
      >
        批量设置
      </Button>
      <Modal
        style={{ minWidth: (480 / 1600) * 100 + '%', maxWidth: 480 }}
        visible={modalVisible}
        onCancel={toggoleAddStepModalVisible}
        title="批量设置"
        destroyOnClose
        onOk={onSetting}
      >
        <Form colon={false} {...formItemLayout}>
          {/* <JudgmentSelect
            info={{}}
            onChangeJudgmentWay={onChangeJudgmentWay}
            type={type}
            form={form}
            getFieldDecorator={getFieldDecorator}
            generateValidLimitsDataSource={generateValidLimitsDataSource}
          /> */}
          <Form.Item label="异常处理">
            {getFieldDecorator('exceptionHandle', {
              // rules: [
              //   {
              //     required: true,
              //     message: '异常处理必填！',
              //   },
              // ],
            })(
              <Select allowClear style={{ width: 200 }}>
                {Object.entries(CHANGE_REACT_TO_EXCEPTION_TEXT_MAP).map(item => (
                  <Select.Option key={item[0]}>{item[1]}</Select.Option>
                ))}
              </Select>
            )}
          </Form.Item>

          <Form.Item label={influencesStepText}>
            {getFieldDecorator('maxInfluencesStep', {
              // rules: [{ required: true, message: `${influencesStepText}必选！` }],
            })(
              <MaxInfluencesStepSelect
                stepMaps={stepMaps}
                stepCodes={stepCodes}
                stepIndex={stepIndex}
              />
            )}
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}

export default Form.create('batch_setting-modal')(BatchSettingModal);
