import React from 'react';

import Form from '@ant-design/compatible/es/form';
// import { Input } from '@manyun/base-ui.ui.input';
import { difference, differenceBy, get } from 'lodash';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Transfer } from '@manyun/base-ui.ui.transfer';

import { fetchRooms } from '@manyun/resource-hub.service.fetch-rooms';
import { RoomTypeText } from '@manyun/resource-hub.ui.room-type-text';

import { StyledTinyTable } from '../styled';

const columns = [
  {
    title: '房间号',
    dataIndex: 'tag',
  },
  {
    title: '包间类型',
    dataIndex: 'roomType',
    render: text => <RoomTypeText code={text} />,
  },
];

const matchedColumns = () => [
  {
    title: '房间号',
    dataIndex: 'name',
  },
  {
    title: '包间类型',
    dataIndex: 'type',
    render: text => <RoomTypeText code={text} />,
  },
];
export class MatchingFacilitiesRoom extends React.PureComponent {
  state = {
    checkedRadio: this.props.defaultTab,
    visible: false,
    noMatched: {
      allList: [],
      selectList: [],
      total: 0,
      roomTag: null,
      tag: null,
      selectedRowKeys: [],
      selectedRows: [],
    },
    matchedSelectedRowKeys: [],
    matchedSelectedRows: [],
    selectedDevice: [],
    targetKeys: [],
  };

  onChangeRadio = value => {
    this.setState({ checkedRadio: value.target.value });
    this.handleSearchMatched(this.state.selectedDevice);
  };

  getRoomList = async () => {
    const data = this.props.form.getFieldsValue();
    const { data: roomData, error } = await fetchRooms({
      roomTypeList: this.props.roomType,
      idcTag: this.props.idcTag,
      blockTag: this.props.blockTag,
      ...data,
    });
    if (error) {
      message.error('error');
    } else {
      this.setState({
        noMatched: {
          allList: roomData.data,
          selectList: roomData.data,
          total: roomData.total,
          selectedRowKeys: [],
          selectedRows: [],
          ...data,
        },
      });
    }
  };

  handleVisible = () => {
    if (!this.state.visible && this.props.mode !== 'ticketView') {
      this.getRoomList(1, 10);
    }
    if (this.state.visible) {
      this.setState(({ noMatched }) => ({
        selectedDevice: [],
        matchedSelectedRowKeys: [],
        matchedSelectedRows: [],
        noMatched: {
          ...noMatched,
          selectedRowKeys: [],
          selectedRows: [],
        },
      }));
    }
    this.handleSearchMatched(this.props.selectedDevice);
    this.setState(({ visible }) => ({
      visible: !visible,
      checkedRadio: this.props.defaultTab,
    }));
  };

  onSelectChangeNoMatched = (selectedRowKeys, selectedRows) => {
    this.setState({
      noMatched: {
        ...this.state.noMatched,
        selectedRowKeys,
        selectedRows: selectedRows.map(item => {
          return {
            name: item.tag,
            code: item.guid,
          };
        }),
      },
    });
  };

  onSelectChangeMatched = (selectedRowKeys, selectedRows) => {
    this.setState({
      matchedSelectedRowKeys: selectedRowKeys,
      matchedSelectedRows: selectedRows,
    });
  };

  onOk = (targetKeys, direction) => {
    const { matchedSelectedRows, noMatched } = this.state;
    const { stepId, selectedDevice, setStepMatchObjectInfoMaps, matchObjectInfoMaps } = this.props;
    if (direction === 'right' && noMatched.selectedRows.length) {
      const select = noMatched.selectedRows
        .map(item => {
          return {
            name: item.tag,
            code: item.guid,
            type: item.roomType,
          };
        })
        .concat(selectedDevice);
      message.success('添加成功');
      this.handleSearchMatched(select);
      setStepMatchObjectInfoMaps({
        stepId,
        selected: select,
      });
      this.setState({
        selectedDevice: select,
      });
    }
    if (direction === 'left' && matchedSelectedRows.length) {
      const deviceList = get(matchObjectInfoMaps, stepId);
      const selectedDeviceList = differenceBy(deviceList, matchedSelectedRows, 'code');

      message.success('移除成功');
      this.handleSearchMatched(selectedDeviceList);
      setStepMatchObjectInfoMaps({
        stepId,
        selected: selectedDeviceList,
      });
    }
    this.setState(({ noMatched }) => ({
      noMatched: {
        ...noMatched,
        selectedRowKeys: [],
        selectedRows: [],
      },
      matchedSelectedRowKeys: [],
      matchedSelectedRows: [],
      targetKeys,
    }));
  };

  handleSearchMatched = selectedDevice => {
    let matchedList = selectedDevice;
    if (this.props.form.getFieldValue('matchedTag')) {
      matchedList = selectedDevice.filter(
        item => item.name.indexOf(this.props.form.getFieldValue('matchedTag')) > -1
      );
    }
    this.setState({
      selectedDevice: matchedList,
      targetKeys: matchedList.map(item => item.code),
    });
  };

  handleSearchNoMatched = () => {
    let noMatchedList = this.state.noMatched.allList.filter(
      item => item.tag.indexOf(this.props.form.getFieldValue('noMatchedTag')) > -1
    );
    this.setState({
      noMatched: {
        ...this.state.noMatched,
        selectList: noMatchedList,
      },
    });
  };

  render() {
    const { btnText, mode, roomTypeName } = this.props;
    const { visible, noMatched, targetKeys, selectedDevice } = this.state;

    return (
      <div>
        <Button type="link" onClick={this.handleVisible}>
          {btnText}
        </Button>
        <Modal
          visible={visible}
          style={{ minWidth: (1200 / 1600) * 100 + '%', maxWidth: 1200 }}
          destroyOnClose
          // onOk={this.onOk}
          onCancel={this.handleVisible}
          // okText={checkedRadio === 'matched' ? '移除' : '添加'}
          title="匹配包间"
          footer={null}
          // cancelText="关闭"
        >
          {mode !== 'stepView' && mode !== 'checkItemView' && (
            <Transfer
              showSelectAll={false}
              titles={['未匹配', '已匹配']}
              targetKeys={targetKeys}
              dataSource={noMatched.allList}
              rowKey={record => record.guid}
              onChange={this.onOk}
            >
              {({
                direction,
                onItemSelectAll,
                onItemSelect,
                selectedKeys: listSelectedKeys,
                // disabled: listDisabled,
              }) => {
                return direction === 'right' ? (
                  <StyledTinyTable
                    rowKey="code"
                    rowSelection={
                      ['checkItemView', 'stepView'].includes(mode)
                        ? null
                        : {
                            onSelectAll: (selected, selectedRows) => {
                              const treeSelectedKeys = selectedRows
                                .filter(item => !item.disabled)
                                .map(({ code }) => code);
                              const diffKeys = selected
                                ? difference(treeSelectedKeys, listSelectedKeys)
                                : difference(listSelectedKeys, treeSelectedKeys);
                              onItemSelectAll(diffKeys, selected);
                              this.setState({
                                matchedSelectedRows: selectedRows,
                                matchedSelectedRowKeys: selectedRows.map(item => item.code),
                              });
                            },
                            onSelect: (record, selected) => {
                              // const gui .d =
                              // record[
                              //   `${['step', 'stepView'].includes(mode) ? 'code' : 'deviceGuid'}`
                              // ];
                              if (selected) {
                                this.setState(
                                  ({ matchedSelectedRows, matchedSelectedRowKeys }) => ({
                                    matchedSelectedRows: [...matchedSelectedRows, record],
                                    matchedSelectedRowKeys: [
                                      ...matchedSelectedRowKeys,
                                      record.code,
                                    ],
                                  })
                                );
                              } else {
                                this.setState(
                                  ({ matchedSelectedRows, matchedSelectedRowKeys }) => ({
                                    matchedSelectedRowKeys: matchedSelectedRowKeys.filter(
                                      noMatchedItem => record.code !== noMatchedItem
                                    ),
                                    matchedSelectedRows: matchedSelectedRows.filter(
                                      noMatchedItem => record.code !== noMatchedItem.code
                                    ),
                                  })
                                );
                              }
                              onItemSelect(record.code, selected);
                            },
                            selectedRowKeys: listSelectedKeys,
                          }
                    }
                    dataSource={selectedDevice}
                    columns={matchedColumns()}
                    pagination={{
                      total: selectedDevice.length,
                    }}
                  />
                ) : (
                  <StyledTinyTable
                    rowKey="guid"
                    rowSelection={{
                      onSelectAll: (selected, selectedRows) => {
                        const treeSelectedKeys = selectedRows
                          .filter(item => !item.disabled)
                          .map(({ guid }) => guid);
                        const diffKeys = selected
                          ? difference(treeSelectedKeys, listSelectedKeys)
                          : difference(listSelectedKeys, treeSelectedKeys);
                        this.setState(({ noMatched }) => ({
                          noMatched: {
                            ...noMatched,
                            selectedRowKeys: selectedRows.map(item => item.guid),
                            selectedRows: selectedRows,
                          },
                        }));
                        onItemSelectAll(diffKeys, selected);
                      },
                      onSelect: (record, selected) => {
                        if (selected) {
                          this.setState(({ noMatched }) => ({
                            noMatched: {
                              ...noMatched,
                              selectedRowKeys: [...noMatched.selectedRowKeys, record.guid],
                              selectedRows: [...noMatched.selectedRows, record],
                            },
                          }));
                        } else {
                          this.setState(({ noMatched }) => ({
                            noMatched: {
                              ...noMatched,
                              selectedRowKeys: noMatched.selectedRowKeys.filter(
                                noMatchedItem => record.guid !== noMatchedItem
                              ),
                              selectedRows: noMatched.selectedRows.filter(
                                noMatchedItem => record.guid !== noMatchedItem.guid
                              ),
                            },
                          }));
                        }
                        onItemSelect(record.guid, selected);
                      },
                      selectedRowKeys: listSelectedKeys,
                      getCheckboxProps: record => ({
                        disabled:
                          selectedDevice &&
                          !!selectedDevice.filter(item => item.code === record.guid).length,
                      }),
                    }}
                    dataSource={noMatched.selectList}
                    columns={columns}
                    pagination={{
                      total: noMatched.selectList.length,
                      // onShowSizeChange: this.onChangePageSizeNoMatched,
                    }}
                  />
                );
              }}
            </Transfer>
          )}
          {/* <GutterWrapper mode="vertical">
            {mode !== 'ticketView' && (
              <Radio.Group onChange={this.onChangeRadio} defaultValue={defaultTab}>
                <Radio.Button value="matched">已匹配</Radio.Button>
                <Radio.Button value="noMatched">未匹配</Radio.Button>
              </Radio.Group>
            )}
            {checkedRadio === 'noMatched' && mode !== 'ticketView' && (
              <GutterWrapper mode="vertical">
                <Form mode="vertical" colon={false} {...formItemLayout}>
                  <Row gutter={16}>
                    <Col span={8}>
                      <Form.Item label="包间号">
                        {getFieldDecorator('noMatchedTag')(<Input />)}
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item>
                        <GutterWrapper>
                          <Button type="primary" onClick={this.handleSearchNoMatched}>
                            搜索
                          </Button>
                          <Button
                            onClick={() => {
                              form.resetFields(['noMatchedTag']);
                              this.getRoomList(1, 10);
                            }}
                          >
                            重置
                          </Button>
                        </GutterWrapper>
                      </Form.Item>
                    </Col>
                  </Row>
                </Form>
                <StyledTinyTable
                  rowKey="guid"
                  rowSelection={{
                    selectedRows: noMatched.selectedRows,
                    selectedRowKeys: noMatched.selectedRowKeys,
                    onChange: this.onSelectChangeNoMatched,
                    getCheckboxProps: record => ({
                      disabled:
                        selectedDevice &&
                        !!selectedDevice.filter(item => item.code === record.guid).length,
                    }),
                  }}
                  dataSource={noMatched.selectList}
                  columns={columns}
                  pagination={{
                    total: noMatched.selectList.length,
                    // current: noMatched.pageNum,
                    // pageSize: noMatched.pageSize,
                    // onChange: this.onChangePageNumNoMatched,
                  }}
                />
              </GutterWrapper>
            )}
            {checkedRadio === 'matched' && (
              <GutterWrapper mode="vertical">
                {mode !== 'ticketView' && (
                  <Form mode="vertical" {...formItemLayout}>
                    <Row gutter={16}>
                      <Col span={8}>
                        <Form.Item label="包间号">
                          {getFieldDecorator('matchedTag')(<Input />)}
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item>
                          <GutterWrapper>
                            <Button
                              type="primary"
                              onClick={() => this.handleSearchMatched(this.props.selectedDevice)}
                            >
                              搜索
                            </Button>
                            <Button
                              onClick={() => {
                                form.resetFields(['matchedTag']);
                                this.handleSearchMatched(this.props.selectedDevice);
                              }}
                            >
                              重置
                            </Button>
                          </GutterWrapper>
                        </Form.Item>
                      </Col>
                    </Row>
                  </Form>
                )}
                <StyledTinyTable
                  rowKey="code"
                  rowSelection={
                    mode === 'ticketView'
                      ? null
                      : {
                          selectedRowKeys: matchedSelectedRowKeys,
                          selectedRows: matchedSelectedRows,
                          onChange: this.onSelectChangeMatched,
                        }
                  }
                  dataSource={selectedDevice}
                  columns={matchedColumns(roomTypeName)}
                  pagination={{
                    total: selectedDevice.length,
                  }}
                />
              </GutterWrapper>
            )}
          </GutterWrapper> */}
          {(mode === 'stepView' || mode === 'checkItemView') && (
            <StyledTinyTable
              rowKey="code"
              // rowSelection={
              //   mode === 'ticketView'
              //     ? null
              //     : {
              //         selectedRowKeys: matchedSelectedRowKeys,
              //         selectedRows: matchedSelectedRows,
              //         onChange: this.onSelectChangeMatched,
              //       }
              // }
              dataSource={selectedDevice}
              columns={matchedColumns(roomTypeName)}
              pagination={{
                total: selectedDevice.length,
              }}
            />
          )}
        </Modal>
      </div>
    );
  }
}

export default Form.create()(MatchingFacilitiesRoom);
