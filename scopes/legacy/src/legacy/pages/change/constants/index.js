import { getObjectOwnProps } from '@manyun/dc-brain.legacy.utils';

// 紧急度
const CHANGE_EMERGENCY_LEVEL_KEY_MAP = {
  HIGH: 'HIGH',
  MIDDLE: 'MIDDLE',
  LOW: 'LOW',
};

export const CHANGE_EMERGENCY_LEVEL_TEXT_MAP = {
  [CHANGE_EMERGENCY_LEVEL_KEY_MAP.HIGH]: '高',
  [CHANGE_EMERGENCY_LEVEL_KEY_MAP.MIDDLE]: '中',
  [CHANGE_EMERGENCY_LEVEL_KEY_MAP.LOW]: '低',
};

export const CHANGE_EMERGENCY_LEVEL_OPTIONS = getObjectOwnProps(CHANGE_EMERGENCY_LEVEL_TEXT_MAP);

/**
 * 变更单状态
 * @deprecated Use `ChangeTicketState` instead
 */
export const CHANGE_TICKET_STATUS_KEY_MAP = {
  DRAFT: 'DRAFT',
  APPROVING: 'APPROVING',
  WAITING_CHANGE: 'WAITING_CHANGE',
  CHANGING: 'CHANGING',
  IN_SUMMARY: 'IN_SUMMARY',
  WAITING_CLOSE: 'WAITING_CLOSE',
  FINISH: 'FINISH',
  SUMMARY_APPROVING: 'SUMMARY_APPROVING',
  // TERMINATION: 'TERMINATION',
};
/**
 * @deprecated use @manyun/ticket.model.change
 */
export const CHANGE_TICKET_STATUS_TEXT_MAP = {
  [CHANGE_TICKET_STATUS_KEY_MAP.DRAFT]: '草稿',
  [CHANGE_TICKET_STATUS_KEY_MAP.APPROVING]: '申请审批中',
  [CHANGE_TICKET_STATUS_KEY_MAP.WAITING_CHANGE]: '待变更',
  [CHANGE_TICKET_STATUS_KEY_MAP.CHANGING]: '变更中', //变更执行
  [CHANGE_TICKET_STATUS_KEY_MAP.IN_SUMMARY]: '总结',
  [CHANGE_TICKET_STATUS_KEY_MAP.SUMMARY_APPROVING]: '总结审批中',
  [CHANGE_TICKET_STATUS_KEY_MAP.WAITING_CLOSE]: '待关闭',
  [CHANGE_TICKET_STATUS_KEY_MAP.FINISH]: '结束',
  // [CHANGE_TICKET_STATUS_KEY_MAP.TERMINATION]: '终止', // 不是一个状态了去掉
};
export const CHANGE_TICKET_STATUS_STEP = {
  [CHANGE_TICKET_STATUS_KEY_MAP.DRAFT]: 0,
  [CHANGE_TICKET_STATUS_KEY_MAP.APPROVING]: 1,
  [CHANGE_TICKET_STATUS_KEY_MAP.WAITING_CHANGE]: 2,
  [CHANGE_TICKET_STATUS_KEY_MAP.CHANGING]: 3,
  [CHANGE_TICKET_STATUS_KEY_MAP.IN_SUMMARY]: 4,
  [CHANGE_TICKET_STATUS_KEY_MAP.SUMMARY_APPROVING]: 5,
  [CHANGE_TICKET_STATUS_KEY_MAP.WAITING_CLOSE]: 6,
  [CHANGE_TICKET_STATUS_KEY_MAP.FINISH]: 7,
  // [CHANGE_TICKET_STATUS_KEY_MAP.TERMINATION]: 3,
};
export const CHANGE_TICKET_STATUS_OPTIONS = getObjectOwnProps(CHANGE_TICKET_STATUS_TEXT_MAP);

// 模板状态
export const CHANGE_TPL_STATUS_KEY_MAP = {
  DRAFT: 'DRAFT',
  APPROVING: 'APPROVING',
  AVAILABLE: 'AVAILABLE',
};

export const CHANGE_TPL_STATUS_TEXT_MAP = {
  [CHANGE_TPL_STATUS_KEY_MAP.DRAFT]: '草稿',
  [CHANGE_TPL_STATUS_KEY_MAP.APPROVING]: '审批中',
  [CHANGE_TPL_STATUS_KEY_MAP.AVAILABLE]: '完成',
};

// 步骤类型
export const CHANGE_STEP_TYPE_KEY_MAP = {
  // 操作步骤
  OP: 'OP',
  // 跑位步骤
  RUN: 'RUN',
  // 观察步骤
  WATCH: 'WATCH',
  // 自定义步骤
  CUSTOMIZE: 'CUSTOMIZE',
};

export const CHANGE_STEP_TYPE_TEXT_MAP = {
  [CHANGE_STEP_TYPE_KEY_MAP.OP]: '操作步骤',
  [CHANGE_STEP_TYPE_KEY_MAP.RUN]: '跑位步骤',
  [CHANGE_STEP_TYPE_KEY_MAP.WATCH]: '观察步骤',
  [CHANGE_STEP_TYPE_KEY_MAP.CUSTOMIZE]: '自定义步骤',
};
// 执行方式
export const CHANGE_EXECUTE_METHOD_KEY_MAP = {
  // 人工
  PERSON: 'PERSON',
  // 系统
  SYSTEM: 'SYSTEM',
};

export const CHANGE_EXECUTE_METHOD_TEXT_MAP = {
  [CHANGE_EXECUTE_METHOD_KEY_MAP.PERSON]: '人工',
  [CHANGE_EXECUTE_METHOD_KEY_MAP.SYSTEM]: '系统',
};
// 验证方式
export const CHANGE_CHECK_METHOD_KEY_MAP = {
  // 人工
  PERSON: 'PERSON',
  // 系统
  SYSTEM: 'SYSTEM',
};
export const CHANGE_CHECK_METHOD_TEXT_MAP = {
  [CHANGE_CHECK_METHOD_KEY_MAP.PERSON]: '人工验证',
  [CHANGE_CHECK_METHOD_KEY_MAP.SYSTEM]: '系统验证',
};

// 操作步骤的操作类型
export const CHANGE_STEP_OPERATE_TYPE_KEY_MAP = {
  // 限定（即选某个设备类型）
  LIMIT: 'LIMIT',
  // 自定义（即选其他）
  CUSTOMIZE: 'CUSTOMIZE',
};

export const CHANGE_STEP_OPERATE_TYPE_TEXT_MAP = {
  // 限定（即选某个设备类型）
  [CHANGE_STEP_OPERATE_TYPE_KEY_MAP.LIMIT]: '设备类型',
  // 自定义（即选其他）
  [CHANGE_STEP_OPERATE_TYPE_KEY_MAP.CUSTOMIZE]: '其他',
};

// 异常处理
export const CHANGE_REACT_TO_EXCEPTION_KEY_MAP = {
  // 变更终止
  CHANGE_STOP: 'CHANGE_STOP',
  // 变更异常
  CHANGE_EXCEPTION: 'CHANGE_EXCEPTION',
};

// 异常处理
export const CHANGE_REACT_TO_EXCEPTION_TEXT_MAP = {
  // 变更终止
  [CHANGE_REACT_TO_EXCEPTION_KEY_MAP.CHANGE_STOP]: '变更终止',
  // 变更异常
  [CHANGE_REACT_TO_EXCEPTION_KEY_MAP.CHANGE_EXCEPTION]: '变更异常',
};

// 预期判断方式
export const CHANGE_EXPECTATION_METHOD_KEY_MAP = {
  SINGLE_VALUE: 'SINGLE_VALUE',
  LIMIT_OF_ESTIMATION: 'LIMIT_OF_ESTIMATION',
};

export const CHANGE_EXPECTATION_METHOD_TEXT_MAP = {
  [CHANGE_EXPECTATION_METHOD_KEY_MAP.SINGLE_VALUE]: '统一设定',
  [CHANGE_EXPECTATION_METHOD_KEY_MAP.LIMIT_OF_ESTIMATION]: '现场判断',
};

// 步骤状态
export const CHANGE_STEP_STATUS_KEY_MAP = {
  // 未开始
  NOT_START: 'NOT_START',
  // 进行中
  PROCESSING: 'PROCESSING',
  // 正常
  NORMAL: 'NORMAL',
  // 异常
  EXCEPTION: 'EXCEPTION',
};

export const CHANGE_STEP_STATUS_TEXT_MAP = {
  // 未开始
  [CHANGE_STEP_STATUS_KEY_MAP.NOT_START]: '未开始',
  // 进行中
  [CHANGE_STEP_STATUS_KEY_MAP.PROCESSING]: '进行中',
  // 正常
  [CHANGE_STEP_STATUS_KEY_MAP.NORMAL]: '正常',
  // 异常
  [CHANGE_STEP_STATUS_KEY_MAP.EXCEPTION]: '异常',
};

// 检查项 操作项步骤状态
export const CHANGE_OP_ITEM_STATUS_KEY_MAP = {
  // 未开始
  NOT_START: 'NOT_START',
  // 检查中
  CHECKING: 'CHECKING',
  // 正常
  NORMAL: 'NORMAL',
  // 异常
  EXCEPTION: 'EXCEPTION',
};

export const CHANGE_OP_ITEM_STATUS_TEXT_MAP = {
  // 未开始
  [CHANGE_OP_ITEM_STATUS_KEY_MAP.NOT_START]: '未开始',
  // 检查中
  [CHANGE_OP_ITEM_STATUS_KEY_MAP.CHECKING]: '检查中',
  // 正常
  [CHANGE_OP_ITEM_STATUS_KEY_MAP.NORMAL]: '正常',
  // 异常
  [CHANGE_OP_ITEM_STATUS_KEY_MAP.EXCEPTION]: '异常',
};

// 人工操作
export const CHANGE_STEP_ITEM_CHECK_STATUS_KEY_MAP = {
  // 正常
  NORMAL: 'NORMAL',
  // 异常
  EXCEPTION: 'EXCEPTION',
};

export const CHANGE_STEP_ITEM_CHECK_STATUS_TEXT_MAP = {
  // 正常
  [CHANGE_STEP_ITEM_CHECK_STATUS_KEY_MAP.NORMAL]: '正常',
  // 异常
  [CHANGE_STEP_ITEM_CHECK_STATUS_KEY_MAP.EXCEPTION]: '异常',
};

export const LIMIT_VALUE_MAP = {
  GREATER_THAN: 'gt',
  GREATER_THAN_OR_EQUALS: 'ge',
  LESS_THAN: 'lt',
  LESS_THAN_OR_EQUALS: 'le',
  EQUALS: 'eq',
};

export const LIMIT_TEXT_MAP = {
  [LIMIT_VALUE_MAP.GREATER_THAN]: '>',
  [LIMIT_VALUE_MAP.GREATER_THAN_OR_EQUALS]: '≥',
  [LIMIT_VALUE_MAP.LESS_THAN]: '<',
  [LIMIT_VALUE_MAP.LESS_THAN_OR_EQUALS]: '≤',
  [LIMIT_VALUE_MAP.EQUALS]: '=',
};

export const operatorList = [
  {
    label: '>',
    value: 'gt',
  },
  {
    label: '≥',
    value: 'ge',
  },
  {
    label: '=',
    value: 'eq',
  },
  {
    label: '<',
    value: 'lt',
  },
  {
    label: '≤',
    value: 'le',
  },
];

export const POINT_TYPE_KEY_MAP = {
  SYSTEM: 'system',
  CUSTOM: 'custom',
};

export const POINT_TYPE_TEXT_MAP = {
  [POINT_TYPE_KEY_MAP.SYSTEM]: '系统测点',
  [POINT_TYPE_KEY_MAP.CUSTOM]: '自定义测点',
};

export const INIT_TICKET_DATA = {
  // 当前步骤索引
  submitLoading: false,
  saveLoading: false,
  step: 0,
  step1: {
    title: {},
    location: { value: [] },
    // urgency: {},
    changeType: {},
    riskLevel: {},
    planStartTime: {},
    planEndTime: {},
    source: {},
    templateId: {},
    templateName: {},
    changeReason: {},
    changeOrderExtJson: { statusInfoList: [] },
    reason: {},
  },
  result: {
    changeReason: null,
    id: null,
    idcTag: null,
    planEndTime: null,
    planStartTime: null,
    riskLevel: null,
    templateCode: null,
    title: null,
    reason: null,
  },
  step2: {
    stepCodes: [], //'TQRqFy5z7', 'TQRqFy5z71'
    stepMaps: {},
  },
  checkItemsMap: null,
  checkItemDeviceMaps: {},
  matchObjectInfoMaps: {},
};

export const INIT_TEMPLATE_DATA = {
  // 当前步骤索引
  submitLoading: false,
  saveLoading: false,
  step: 0,
  step1: {
    templateName: {},
    changeType: {},
    riskLevel: {},
    executeRole: {},
  },
  step2: {
    stepCodes: [], //'TQRqFy5z7', 'TQRqFy5z71'
    stepMaps: {},
  },
  checkItemsMap: null,
};
