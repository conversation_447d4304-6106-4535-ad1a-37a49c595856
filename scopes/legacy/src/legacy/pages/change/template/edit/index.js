import React, { useEffect, useRef } from 'react';
import { connect } from 'react-redux';
import { useLocation, useParams } from 'react-router-dom';

import { message } from '@manyun/base-ui.ui.message';
import { Typography } from '@manyun/base-ui.ui.typography';

import { Footer<PERSON>ool<PERSON><PERSON>, G<PERSON>Wrapper, TinyCard } from '@manyun/dc-brain.legacy.components';
import {
  changeActions,
  getTemplateEditDetailInfoActionCreator,
  setTemplateSaveActionCreator,
  setTemplateSubmitActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/changeActions';
import { getLocationSearchMap } from '@manyun/dc-brain.legacy.utils';

import StepButtons from '../../components/step-buttons';
import StepTableList from '../../components/step-table-list';
import { INIT_TEMPLATE_DATA } from '../../constants';
import BaseInfoForm from '../components/base-info-form';
import Result from '../components/result';
import Steps from '../components/steps';

export function TplEdit({
  step,
  stepForward,
  stepBack,
  stepCodes,
  stepMaps,
  checkItemsMap,
  setTemplateEditStep2Fields,
  setTemlateEditCheckItemsMap,
  setTemplateSaveActionCreator,
  saveLoading,
  submitLoading,
  // mode,
  setTemplateSubmitActionCreator,
  getTemplateEditDetailInfoActionCreator,
  setStepCodes,
  // setTemplateEditStep,
  setTemplateEditInfo,
  saveBatchSettingCheckPoint,
  batchSettingCheckedPoint,
  setTemplateEditCustomizeUploadFileLoading,
  customizeUploadFileLoading,
}) {
  /**
   * @type {React.RefObject<import('../components/base-info-form').BaseInfoForm>}
   */
  const baseInfoFormRef = useRef();
  const addStepBtnRef = useRef();
  const checkItemsRef = useRef();
  const { id } = useParams();
  const { search } = useLocation();
  const { stepCurrent } = getLocationSearchMap(search, ['stepCurrent']);

  useEffect(() => {
    getTemplateEditDetailInfoActionCreator({ id, stepCurrent });
    // setTemplateEditStep(stepCurrent);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [stepCurrent]);

  useEffect(() => {
    return () => setTemplateEditInfo(INIT_TEMPLATE_DATA);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  let showPrev;
  let onPrev;
  let onNext;
  let onSubmit;
  let onSave;
  if (step === 0) {
    onNext = async () => {
      try {
        await baseInfoFormRef.current.validateFieldsAndScroll();
        stepForward(step);
      } catch (error) {
        // ignore...
      }
    };
    onSave = async () => {
      try {
        await baseInfoFormRef.current.validateFieldsAndScroll();
        setTemplateSaveActionCreator({ id, mode: 'edit' });
      } catch (error) {
        // ignore...
      }
    };
  }
  if (step === 1) {
    onNext = () => {
      if (stepCodes.length) {
        stepForward(step);
      } else {
        addStepBtnRef.current.animateOnece();
      }
    };
    onSave = () => {
      setTemplateSaveActionCreator({ id, mode: 'edit' });
    };
  }
  if (step === 2) {
    onNext = () => {
      checkItemsRef.current &&
        checkItemsRef.current.form.validateFieldsAndScroll(
          { scroll: { offsetTop: 80, offsetBottom: 100 } },
          errs => {
            if (errs) {
              return;
            }
            stepForward(step);
          }
        );
    };
    onSubmit = () => {
      checkItemsRef.current &&
        checkItemsRef.current.form.validateFieldsAndScroll(
          { scroll: { offsetTop: 80, offsetBottom: 100 } },
          errs => {
            if (errs) {
              message.error(`当前页面存在未完成项，请检查后提交`);
              return;
            }
            setTemplateSubmitActionCreator({ id, mode: 'edit' });
          }
        );
    };
    onSave = () => {
      setTemplateSaveActionCreator({ id, mode: 'edit' });
    };
  }

  if (step > 0) {
    showPrev = true;
    onPrev = () => stepBack(step);
  }

  const addStep = value => {
    setTemplateEditStep2Fields(value);
  };

  const editStep = value => {
    setTemplateEditStep2Fields(value);
  };

  const deleteStep = value => {
    setTemplateEditStep2Fields(value);
  };

  const addCheckItemsMap = value => {
    setTemlateEditCheckItemsMap(value);
  };

  return (
    <GutterWrapper mode="vertical" size="2rem" style={{ marginBottom: 32 }}>
      <TinyCard>
        <Typography.Title level={4}>编辑变更模版</Typography.Title>
        <Typography.Paragraph>
          选择变更模板后，变更配置中自动匹配变更流程，仅需要匹配对应设备和包间即可
        </Typography.Paragraph>
      </TinyCard>
      <TinyCard>
        <GutterWrapper mode="vertical" size="2rem">
          <Steps current={step} />
          {step === 0 && <BaseInfoForm ref={baseInfoFormRef} mode="templateEdit" />}
          {step === 1 && (
            <StepTableList
              // mode="addSteps"
              addStepBtnRef={addStepBtnRef}
              stepCodes={stepCodes}
              stepMaps={stepMaps}
              // templateEdit={templateEdit}
              addStep={addStep}
              step={step}
              editStep={editStep}
              deleteStep={deleteStep}
              setStepCodes={setStepCodes}
              onSaveBatchSettingCheckPoint={saveBatchSettingCheckPoint}
              batchSettingCheckedPoint={batchSettingCheckedPoint}
              addCheckItemsMap={addCheckItemsMap}
              checkItemsMap={checkItemsMap}
              setCustomizeUploadFileLoading={setTemplateEditCustomizeUploadFileLoading}
              customizeUploadFileLoading={customizeUploadFileLoading}
            />
          )}
          {step === 2 && (
            <StepTableList
              wrappedComponentRef={checkItemsRef}
              stepCodes={stepCodes}
              stepMaps={stepMaps}
              step={step}
              addCheckItemsMap={addCheckItemsMap}
              checkItemsMap={checkItemsMap}
              addStep={addStep}
              type="template"
              setStepCodes={setStepCodes}
              onSaveBatchSettingCheckPoint={saveBatchSettingCheckPoint}
              batchSettingCheckedPoint={batchSettingCheckedPoint}
              setCustomizeUploadFileLoading={setTemplateEditCustomizeUploadFileLoading}
              customizeUploadFileLoading={customizeUploadFileLoading}
            />
          )}
          {step === 3 && <Result mode="templateEdit" />}
        </GutterWrapper>

        {step < 3 && (
          <FooterToolBar>
            <StepButtons
              showPrev={showPrev}
              showSubmit={step === 2}
              onSubmit={onSubmit}
              onPrev={onPrev}
              onNext={onNext}
              showNext={[0, 1].includes(step)}
              onSave={onSave}
              submitLoading={submitLoading}
              saveLoading={saveLoading}
            />
          </FooterToolBar>
        )}
      </TinyCard>
    </GutterWrapper>
  );
}

const mapStateToProps = ({
  change: {
    templateEdit: {
      step,
      step2: { stepCodes, stepMaps },
      checkItemsMap,
      saveLoading,
      submitLoading,
      templateId,
      batchSettingCheckedPoint,
      customizeUploadFileLoading,
    },
  },
}) => ({
  step,
  checkItemsMap,
  stepCodes,
  stepMaps,
  saveLoading,
  submitLoading,
  templateId,
  batchSettingCheckedPoint,
  customizeUploadFileLoading,
});
const mapDispatchToProps = dispatch => ({
  setTemplateEditCustomizeUploadFileLoading: values =>
    dispatch(changeActions.setTemplateEditCustomizeUploadFileLoading(values)),
  stepForward: step => dispatch(changeActions.setTemplateEditStep(step + 1)),
  stepBack: step => dispatch(changeActions.setTemplateEditStep(step - 1)),
  setTemplateEditStep2Fields: values => dispatch(changeActions.setTemplateEditStep2Fields(values)),
  setTemlateEditCheckItemsMap: values =>
    dispatch(changeActions.setTemlateEditCheckItemsMap(values)),
  setTemplateSaveActionCreator: values => dispatch(setTemplateSaveActionCreator(values)),
  setTemplateSubmitActionCreator: values => dispatch(setTemplateSubmitActionCreator(values)),
  getTemplateEditDetailInfoActionCreator: values =>
    dispatch(getTemplateEditDetailInfoActionCreator(values)),
  setStepCodes: values => dispatch(changeActions.setTemplateEditStepCodes(values)),
  setTemplateEditStep: values => dispatch(changeActions.setTemplateEditStep(values)),
  setTemplateEditInfo: values => dispatch(changeActions.setTemplateEditInfo(values)),
  saveBatchSettingCheckPoint: values =>
    dispatch(changeActions.setTemplateNewBatchSettingCheckedPoint(values)),
});

export default connect(mapStateToProps, mapDispatchToProps)(TplEdit);
