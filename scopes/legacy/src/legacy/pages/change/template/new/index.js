import React, { useEffect, useRef } from 'react';
import { connect } from 'react-redux';

import { message } from '@manyun/base-ui.ui.message';
import { Typography } from '@manyun/base-ui.ui.typography';

import { Footer<PERSON>ool<PERSON><PERSON>, G<PERSON>Wrapper, TinyCard } from '@manyun/dc-brain.legacy.components';
import {
  changeActions,
  setTemplateSaveActionCreator,
  setTemplateSubmitActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/changeActions';

import Result from '../components/result';
import StepButtons from './../../components/step-buttons';
import StepTableList from './../../components/step-table-list';
import BaseInfoForm from './../components/base-info-form';
import Steps from './../components/steps';

export function TplNew({
  step,
  stepForward,
  stepBack,
  stepCodes,
  stepMaps,
  checkItemsMap,
  setTemplateNewStep2Fields,
  setTemlateNewCheckItemsMap,
  setTemplateSaveActionCreator,
  saveLoading,
  submitLoading,
  mode,
  setTemplateSubmitActionCreator,
  resetTemplateNewInfo,
  batchSettingCheckedPoint,
  saveBatchSettingCheckPoint,
  setTemplateNewCustomizeUploadFileLoading,
  customizeUploadFileLoading,
}) {
  /**
   * @type {React.RefObject<import('./../components/base-info-form').BaseInfoForm>}
   */
  const baseInfoFormRef = useRef();
  const addStepBtnRef = useRef();
  const checkItemsRef = useRef();

  useEffect(() => {
    return () => resetTemplateNewInfo();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  let showPrev;
  let onPrev;
  let onNext;
  let onSubmit;
  let onSave;
  if (step === 0) {
    onNext = async () => {
      try {
        await baseInfoFormRef.current.validateFieldsAndScroll();
        stepForward(step);
      } catch (error) {
        // ignore...
      }
    };
    onSave = async () => {
      try {
        await baseInfoFormRef.current.validateFieldsAndScroll();
        setTemplateSaveActionCreator({ mode: 'new' });
      } catch (error) {
        // ignore...
      }
    };
  }
  if (step === 1) {
    onNext = () => {
      if (stepCodes.length) {
        stepForward(step);
      } else {
        addStepBtnRef.current.animateOnece();
      }
    };
    onSave = () => {
      setTemplateSaveActionCreator({ mode: 'new' });
    };
  }
  if (step === 2) {
    onNext = () => {
      checkItemsRef.current &&
        checkItemsRef.current.form.validateFieldsAndScroll({ scroll: { offsetTop: 80 } }, errs => {
          if (errs) {
            return;
          }
          // stepForward(step);
        });
    };
    onSubmit = () => {
      checkItemsRef.current &&
        checkItemsRef.current.form.validateFieldsAndScroll({ scroll: { offsetTop: 80 } }, errs => {
          if (errs) {
            message.error(`当前页面存在未完成项，请检查后提交`);
            return;
          }
          setTemplateSubmitActionCreator({ mode: 'new' });
        });
    };
    onSave = () => {
      setTemplateSaveActionCreator({ mode: 'new' });
    };
  }

  if (step > 0) {
    showPrev = true;
    onPrev = () => stepBack(step);
  }

  const addStep = value => {
    setTemplateNewStep2Fields(value);
  };

  const editStep = value => {
    setTemplateNewStep2Fields(value);
  };

  const deleteStep = value => {
    setTemplateNewStep2Fields(value);
  };

  const addCheckItemsMap = value => {
    setTemlateNewCheckItemsMap(value);
  };

  return (
    <GutterWrapper mode="vertical" size="2rem" style={{ marginBottom: 32 }}>
      <TinyCard>
        <Typography.Title level={4}>新增变更模版</Typography.Title>
        <Typography.Paragraph>
          选择变更模板后，变更配置中自动匹配变更流程，仅需要匹配对应设备和包间即可
        </Typography.Paragraph>
      </TinyCard>
      <TinyCard>
        <GutterWrapper mode="vertical" size="2rem">
          <Steps current={step} />
          {step === 0 && <BaseInfoForm ref={baseInfoFormRef} mode="templateNew" />}
          {step === 1 && (
            <StepTableList
              // mode="addSteps"
              addStepBtnRef={addStepBtnRef}
              stepCodes={stepCodes}
              stepMaps={stepMaps}
              // templateNew={templateNew}
              addStep={addStep}
              step={step}
              editStep={editStep}
              deleteStep={deleteStep}
              addCheckItemsMap={addCheckItemsMap}
              onSaveBatchSettingCheckPoint={saveBatchSettingCheckPoint}
              batchSettingCheckedPoint={batchSettingCheckedPoint}
              setCustomizeUploadFileLoading={setTemplateNewCustomizeUploadFileLoading}
              customizeUploadFileLoading={customizeUploadFileLoading}
            />
          )}
          {step === 2 && (
            <StepTableList
              wrappedComponentRef={checkItemsRef}
              stepCodes={stepCodes}
              stepMaps={stepMaps}
              step={step}
              addCheckItemsMap={addCheckItemsMap}
              checkItemsMap={checkItemsMap}
              addStep={addStep}
              type="template"
              onSaveBatchSettingCheckPoint={saveBatchSettingCheckPoint}
              batchSettingCheckedPoint={batchSettingCheckedPoint}
              setCustomizeUploadFileLoading={setTemplateNewCustomizeUploadFileLoading}
              customizeUploadFileLoading={customizeUploadFileLoading}
            />
          )}
          {step === 3 && <Result mode="templateNew" />}
        </GutterWrapper>
        {step < 3 && (
          <FooterToolBar>
            <StepButtons
              showPrev={showPrev}
              showSubmit={step === 2}
              onSubmit={onSubmit}
              onPrev={onPrev}
              onNext={onNext}
              showNext={[0, 1].includes(step)}
              onSave={onSave}
              submitLoading={submitLoading}
              saveLoading={saveLoading}
            />
          </FooterToolBar>
        )}
      </TinyCard>
    </GutterWrapper>
  );
}

const mapStateToProps = ({
  change: {
    templateNew: {
      step,
      step2: { stepCodes, stepMaps },
      checkItemsMap,
      saveLoading,
      submitLoading,
      batchSettingCheckedPoint,
      customizeUploadFileLoading,
    },
  },
}) => ({
  step,
  checkItemsMap,
  stepCodes,
  stepMaps,
  saveLoading,
  submitLoading,
  batchSettingCheckedPoint,
  customizeUploadFileLoading,
});
const mapDispatchToProps = dispatch => ({
  stepForward: step => dispatch(changeActions.setTemplateNewStep(step + 1)),
  stepBack: step => dispatch(changeActions.setTemplateNewStep(step - 1)),
  setTemplateNewStep2Fields: values => dispatch(changeActions.setTemplateNewStep2Fields(values)),
  setTemlateNewCheckItemsMap: values => dispatch(changeActions.setTemlateNewCheckItemsMap(values)),
  setTemplateSaveActionCreator: values => dispatch(setTemplateSaveActionCreator(values)),
  setTemplateSubmitActionCreator: values => dispatch(setTemplateSubmitActionCreator(values)),
  resetTemplateNewInfo: values => dispatch(changeActions.resetTemplateNewInfo(values)),
  saveBatchSettingCheckPoint: values =>
    dispatch(changeActions.setTemplateNewBatchSettingCheckedPoint(values)),
  setTemplateNewCustomizeUploadFileLoading: values =>
    dispatch(changeActions.setTemplateNewCustomizeUploadFileLoading(values)),
});

export default connect(mapStateToProps, mapDispatchToProps)(TplNew);
