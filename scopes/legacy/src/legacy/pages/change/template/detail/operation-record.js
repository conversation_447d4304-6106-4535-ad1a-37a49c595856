import React, { Component } from 'react';

import { ModalButton } from '@manyun/dc-brain.legacy.components';

import { OperationRecord as OperationRecordTable } from '../../components/operation-record-table';

class OperationRecord extends Component {
  render() {
    return (
      <ModalButton
        compact
        text="查看"
        title="操作记录"
        modalStyle={{ minWidth: (800 / 1600) * 100 + '%', maxWidth: 800 }}
        type="link"
        footer={null}
      >
        <OperationRecordTable id={this.props.id} targetType="CHANGE_TEMPLATE" />
      </ModalButton>
    );
  }
}

export default OperationRecord;
