import React, { useEffect } from 'react';
import { connect } from 'react-redux';
import { useParams } from 'react-router-dom';

import moment from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { SimpleFileList } from '@manyun/base-ui.ui.file-list';
import { message } from '@manyun/base-ui.ui.message';

import { User } from '@manyun/auth-hub.ui.user';
import { CHANGE_RISK_LEVEL_TEXT_MAP } from '@manyun/ticket.model.change';
import { exportChangeTicketsTemplate } from '@manyun/ticket.service.export-change-tickets-template';

import { ApproveLink, GutterWrapper } from '@manyun/dc-brain.legacy.components';
import {
  approvalActionCreator,
  changeActions,
  getTemplateDetailInfo,
} from '@manyun/dc-brain.legacy.redux/actions/changeActions';

import StepTableList from '../../components/step-table-list';
import { CHANGE_TPL_STATUS_KEY_MAP, CHANGE_TPL_STATUS_TEXT_MAP } from '../../constants/index';
import OperationRecord from './operation-record';

export function TplDetail({
  step,
  stepCodes,
  stepMaps,
  checkItemsMap,
  step1,
  getTemplateDetailInfo,
  approvalActionCreator,
}) {
  const { id } = useParams();

  useEffect(() => {
    getTemplateDetailInfo({ id, mode: 'detail' });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // const onApproval = type => {
  //   approvalActionCreator({
  //     approvalResult: type,
  //     approvalBusinessType: 'CHANGE_TEMPLATE',
  //     bizId: step1.templateId,
  //     type: 'template',
  //   });
  // };

  return (
    <GutterWrapper mode="vertical">
      <Card
        title="模板信息"
        extra={
          step1.templateStatus === CHANGE_TPL_STATUS_KEY_MAP.AVAILABLE ? (
            <FileExport
              filename={`${step1.templateName && step1.templateName.value}.xlsx`}
              data={async type => {
                const { error, data } = await exportChangeTicketsTemplate({
                  templateId: step1.templateId,
                });
                if (error) {
                  message.error(error.message);
                  return;
                }
                return data;
              }}
            />
          ) : null
        }
      >
        <Descriptions column={4}>
          <Descriptions.Item label="变更模板单号">{step1.templateId}</Descriptions.Item>
          <Descriptions.Item label="模板名称">
            {step1.templateName && step1.templateName.value}
          </Descriptions.Item>
          <Descriptions.Item label="变更类型">
            {step1.changeType && step1.changeTypeName}
          </Descriptions.Item>
          <Descriptions.Item label="变更等级">
            {step1.riskLevel && CHANGE_RISK_LEVEL_TEXT_MAP[step1.riskLevel.value]}
          </Descriptions.Item>
          <Descriptions.Item label="审批">
            <ApproveLink id={step1.workFlowId} />
          </Descriptions.Item>
          <Descriptions.Item label="执行角色">
            {step1.executeRole && step1.executeRole.value && step1.executeRole.value.label}
          </Descriptions.Item>
          <Descriptions.Item label="模板更新时间">
            {moment(step1.gmtModified).format('YYYY-MM-DD HH:mm:ss')}
          </Descriptions.Item>
          <Descriptions.Item label="创建人">
            <User.Link id={step1.modifyPersonId} name={step1.modifyPersonName} />
          </Descriptions.Item>
          <Descriptions.Item label="状态">
            <GutterWrapper>
              <span>{CHANGE_TPL_STATUS_TEXT_MAP[step1.templateStatus]}</span>
              {
                step1.templateStatus === CHANGE_TPL_STATUS_KEY_MAP.APPROVING && (
                  <ApproveLink id={step1.workFlowId} text="查看" />
                )
                // [
                //   <Button
                //     style={{ padding: 0 }}
                //     key="agree"
                //     type="link"
                //     onClick={() => onApproval('AGREE')}
                //   >
                //     通过
                //   </Button>,
                //   <Button
                //     style={{ padding: 0 }}
                //     key="rejected"
                //     type="link"
                //     onClick={() => onApproval('REFUSE')}
                //   >
                //     驳回
                //   </Button>,
                // ]
              }
            </GutterWrapper>
          </Descriptions.Item>
          <Descriptions.Item label="操作记录">
            <OperationRecord id={id} />
          </Descriptions.Item>
          <Descriptions.Item label="附件">
            {step1.fileInfoList?.value?.length ? (
              <SimpleFileList files={step1.fileInfoList.value}>
                <Button type="link" compact>
                  查看
                </Button>
              </SimpleFileList>
            ) : (
              '--'
            )}
          </Descriptions.Item>
        </Descriptions>
      </Card>
      <Card title="变更方案">
        <StepTableList
          stepCodes={stepCodes}
          stepMaps={stepMaps}
          step={step}
          checkItemsMap={checkItemsMap}
          type="templateView"
        />
      </Card>
    </GutterWrapper>
  );
}

const mapStateToProps = ({
  change: {
    templateDetail: {
      step,
      step1,
      step2: { stepCodes, stepMaps },
      checkItemsMap,
    },
  },
}) => ({ step, checkItemsMap, stepCodes, stepMaps, step1 });
const mapDispatchToProps = dispatch => ({
  stepForward: step => dispatch(changeActions.setTemplateNewStep(step + 1)),
  stepBack: step => dispatch(changeActions.setTemplateNewStep(step - 1)),
  setTemplateNewStep2Fields: values => dispatch(changeActions.setTemplateNewStep2Fields(values)),
  setTemlateNewCheckItemsMap: values => dispatch(changeActions.setTemlateNewCheckItemsMap(values)),
  getTemplateDetailInfo: values => dispatch(getTemplateDetailInfo(values)),
  approvalActionCreator: values => dispatch(approvalActionCreator(values)),
});

export default connect(mapStateToProps, mapDispatchToProps)(TplDetail);
