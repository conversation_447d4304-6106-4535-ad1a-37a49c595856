import React from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import CheckCircleFilled from '@ant-design/icons/es/icons/CheckCircleFilled';
import ExclamationCircleFilled from '@ant-design/icons/es/icons/ExclamationCircleFilled';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Result } from '@manyun/base-ui.ui.result';

import { CHANGE_RISK_LEVEL_TEXT_MAP } from '@manyun/ticket.model.change';

import { ApproveLink } from '@manyun/dc-brain.legacy.components';
import * as urls from '@manyun/dc-brain.legacy.constants/urls';

export function ResultStep({ mode, templateName, workflowId, changeType, riskLevel, templateId }) {
  const title = mode === 'templateNew' ? '创建成功' : '修改成功';
  const needWorkflow = workflowId && workflowId !== '0';
  return (
    <Result
      title={needWorkflow ? '待审批' : title}
      subTitle={needWorkflow ? '当前变更模板需要审批才可生效' : null}
      icon={
        needWorkflow ? (
          <ExclamationCircleFilled style={{ color: `var(--${prefixCls}-warning-color)` }} />
        ) : (
          <CheckCircleFilled style={{ color: `var(--${prefixCls}-success-color)` }} />
        )
      }
      extra={[
        <Link to={{ pathname: urls.CHANGE_TEMPLATE_NEW }}>
          <Button
            key="refresh-button"
            type="primary"
            onClick={() => {
              mode === 'templateNew' && window.location.reload();
            }}
          >
            新增模板
          </Button>
        </Link>,
        <Link key="go-back-button" to={{ pathname: urls.CHANGE_TEMPLATE_LIST }}>
          <Button key="go-back-button">返回列表</Button>
        </Link>,
      ]}
    >
      <Descriptions style={{ width: 200, marginLeft: 'auto', marginRight: 'auto' }} column={1}>
        <Descriptions.Item label="模板ID">{templateId}</Descriptions.Item>
        <Descriptions.Item label="模板名称">{templateName.value}</Descriptions.Item>
        <Descriptions.Item label="变更类型">{changeType.value.label}</Descriptions.Item>
        <Descriptions.Item label="变更等级">
          {CHANGE_RISK_LEVEL_TEXT_MAP[riskLevel.value]}
        </Descriptions.Item>
        {needWorkflow && (
          <Descriptions.Item label="审批">
            <ApproveLink id={workflowId} />
          </Descriptions.Item>
        )}
      </Descriptions>
    </Result>
  );
}
const mapStateToProps = ({ change: { templateNew, templateEdit } }, { mode }) => {
  if (mode === 'templateEdit') {
    return {
      templateName: templateEdit.step1.templateName,
      changeType: templateEdit.step1.changeType,
      riskLevel: templateEdit.step1.riskLevel,
      templateId: templateEdit.step1.templateId,
      workflowId: templateEdit.step1.workflowId,
    };
  }
  if (mode === 'templateNew') {
    return {
      templateName: templateNew.step1.templateName,
      changeType: templateNew.step1.changeType,
      riskLevel: templateNew.step1.riskLevel,
      templateId: templateNew.step1.templateId,
      workflowId: templateNew.step1.workflowId,
    };
  }
};
export default connect(mapStateToProps)(ResultStep);
