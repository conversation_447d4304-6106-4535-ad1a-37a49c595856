import React from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';

import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';

import { McUpload } from '@manyun/dc-brain.ui.upload';

import { RoleSelect } from '@manyun/dc-brain.legacy.components';
import { changeActions } from '@manyun/dc-brain.legacy.redux/actions/changeActions';

import ChangeTypesTreeSelect from './../../../components/change-types-tree-select';
import RiskLevelSelect from './../../../components/risk-level-select';

/**
 * @typedef Props
 * @property {import('antd-3/lib/form/Form').WrappedFormUtils} form
 */

/**
 * @augments {React.PureComponent<Props>}
 */
export class BaseInfoForm extends React.PureComponent {
  validateFields = () => {
    const { form } = this.props;
    return new Promise((resolve, reject) => {
      form.validateFields((error, values) => {
        if (error) {
          reject(error);
        }
        resolve(values);
      });
    });
  };

  render() {
    const { form } = this.props;
    const { getFieldDecorator } = form;

    return (
      <Form
        style={{ marginLeft: 'auto', marginRight: 'auto' }}
        labelCol={{ xl: 11 }}
        wrapperCol={{ xl: 13 }}
        colon={false}
      >
        <Form.Item label="模板名称">
          {getFieldDecorator('templateName', {
            rules: [
              {
                required: true,
                message: '模板名称必填！',
              },
              {
                max: 50,
                message: '最多输入 50 个字符！',
              },
            ],
          })(<Input style={{ width: 200 }} />)}
        </Form.Item>
        <Form.Item label="变更类型" wrapperCol={{ xl: 12 }}>
          {getFieldDecorator('changeType', {
            rules: [{ required: true, message: '变更类型必填！' }],
          })(<ChangeTypesTreeSelect style={{ width: 200 }} labelInValue />)}
        </Form.Item>
        <Form.Item label="变更等级" wrapperCol={{ xl: 6 }}>
          {getFieldDecorator('riskLevel', {
            rules: [
              {
                required: true,
                message: '变更等级必选！',
              },
            ],
          })(<RiskLevelSelect style={{ width: 200 }} />)}
        </Form.Item>
        <Form.Item label="执行角色" wrapperCol={{ xl: 12 }}>
          {getFieldDecorator('executeRole', {
            rules: [{ required: true, message: '执行角色必选！' }],
          })(
            <RoleSelect style={{ width: 200 }} labelInValue placeholder="请输入角色名称模糊查询" />
          )}
        </Form.Item>
        <Form.Item
          label="附件"
          getValueFromEvent={({ fileList }) => {
            return fileList;
          }}
        >
          {getFieldDecorator('fileInfoList', {
            getValueFromEvent: ({ fileList }) => {
              return fileList;
            },
            valuePropName: 'fileList',
          })(
            <McUpload
              showAccept
              accept=".zip,.rar,.7z,.mp4,.3gp,.mov,.jpg,.jpeg,.png,.doc,.docx,.xls,.xlsx,.pdf"
              showUploadList
              allowDelete
              maxCount={1}
            >
              <Button type="primary">上传</Button>
            </McUpload>
          )}
        </Form.Item>
      </Form>
    );
  }
}

const mapStateToProps = ({ change: { templateNew, templateEdit } }, { mode }) => {
  if (mode === 'templateEdit') {
    return { fields: templateEdit.step1 };
  }
  if (mode === 'templateNew') {
    return { fields: templateNew.step1 };
  }
};

const mapDispatchToProps = {
  onFieldsChangeNew: changeActions.setTemplateNewStep1Fields,
  onFieldsChangeEdit: changeActions.setTemplateEditStep1Fields,
};

const connectOpts = { forwardRef: true };

/**
 * @type {import('antd-3/lib/form').FormCreateOption}
 */
const createOptions = {
  onFieldsChange({ mode, onFieldsChangeNew, onFieldsChangeEdit }, changedFields) {
    if (mode === 'templateEdit') {
      onFieldsChangeEdit(changedFields);
    }
    if (mode === 'templateNew') {
      onFieldsChangeNew(changedFields);
    }
  },
  mapPropsToFields({ fields }) {
    return Object.keys(fields).reduce((formFields, fieldKey) => {
      formFields[fieldKey] = Form.createFormField(fields[fieldKey]);

      return formFields;
    }, {});
  },
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
  null,
  connectOpts
)(Form.create(createOptions)(BaseInfoForm));
