import React, { useCallback, useEffect } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Modal } from '@manyun/base-ui.ui.modal';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { CHANGE_RISK_LEVEL_TEXT_MAP } from '@manyun/ticket.model.change';

import { Ellipsis, TinyTable, UserLink } from '@manyun/dc-brain.legacy.components';
import * as urls from '@manyun/dc-brain.legacy.constants/urls';
import {
  changeActions,
  deleteTemplateActionCreator,
  getChangeDataActionCreator,
  getTemplateCopyInfoActionCreator,
  resetSearchValuesActionCreator,
  setPaginationThenGetDataActionCreator,
  templateApprovalOrRevertActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/changeActions';
import {
  generateChangeTemplateDetail,
  generateChangeTemplateEdit,
} from '@manyun/dc-brain.legacy.utils/urls';

import { CHANGE_TPL_STATUS_KEY_MAP, CHANGE_TPL_STATUS_TEXT_MAP } from '../../../../constants';
import { getSearchValuesAndUpdateValues, setLocationSearchValues } from '../utils';

export function ChangeDataTable({
  data,
  total,
  loading,
  pageNum,
  pageSize,
  getData,
  setPagination,
  permissions,
  templateApprovalOrRevertActionCreator,
  deleteTemplateActionCreator,
  searchValues,
  updateSearchValues,
  onReset,
  getTemplateCopyInfoActionCreator,
}) {
  useEffect(() => {
    getSearchValuesAndUpdateValues({ setPagination, updateSearchValues });
    getData();
    return () => {
      onReset();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [getData]);

  const paginationChangeHandler = useCallback(
    (current, size) => {
      setPagination({ pageNum: current, pageSize: size });
      setLocationSearchValues(searchValues, { pageNum: current, pageSize: size });
    },
    [setPagination, searchValues]
  );

  const onApprovalOrRevert = templateId => {
    templateApprovalOrRevertActionCreator({ templateId });
  };

  const onDeleteTemplate = templateId => {
    deleteTemplateActionCreator({ templateId });
  };

  const showConfirm = (templateId, templateName) => {
    Modal.confirm({
      title: `确认要删除${templateName}模板吗？`,
      content: '删除之后数据无法恢复！',
      okText: '确认',
      cancelText: '取消',
      onOk() {
        onDeleteTemplate(templateId);
      },
      onCancel() {},
    });
  };

  return (
    <TinyTable
      rowKey="templateId"
      actions={
        permissions.includes('change_template_list_create') && [
          <Link key="new" to={{ pathname: urls.CHANGE_TEMPLATE_NEW }}>
            <Button type="primary">新建模板</Button>
          </Link>,
        ]
      }
      columns={getColumns(
        onApprovalOrRevert,
        onDeleteTemplate,
        permissions,
        showConfirm,
        getTemplateCopyInfoActionCreator
      )}
      scroll={{ x: 'max-content' }}
      dataSource={data}
      loading={loading}
      pagination={{
        total,
        current: pageNum,
        pageSize,
        onChange: paginationChangeHandler,
      }}
    />
  );
}

const mapStateToProps = ({
  change: {
    templateList: {
      data,
      total,
      pagination: { pageNum, pageSize },
      loading,
      searchValues,
    },
  },
  user: { permissions },
}) => ({
  data,
  total,
  pageNum,
  pageSize,
  loading,
  permissions,
  searchValues,
});
const mapDispatchToProps = {
  getData: getChangeDataActionCreator,
  setPagination: setPaginationThenGetDataActionCreator,
  templateApprovalOrRevertActionCreator: templateApprovalOrRevertActionCreator,
  deleteTemplateActionCreator: deleteTemplateActionCreator,
  updateSearchValues: changeActions.updateSearchValues,
  onReset: resetSearchValuesActionCreator,
  getTemplateCopyInfoActionCreator: getTemplateCopyInfoActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(ChangeDataTable);

const getColumns = (
  onApprovalOrRevert,
  onDeleteTemplate,
  permissions,
  showConfirm,
  getTemplateCopyInfoActionCreator
) => [
  {
    title: '模板ID',
    dataIndex: 'templateId',
    render: text => <Link to={generateChangeTemplateDetail({ id: text })}>{text}</Link>,
  },
  {
    title: '模板名称',
    dataIndex: 'templateName',
    render(templateName) {
      return (
        <Ellipsis lines={1} tooltip>
          {templateName}
        </Ellipsis>
      );
    },
  },
  {
    title: '变更类型',
    dataIndex: 'changeTypeName',
  },
  {
    title: '变更等级',
    dataIndex: 'riskLevel',
    render: riskLevel => <span>{CHANGE_RISK_LEVEL_TEXT_MAP[riskLevel]}</span>,
  },
  {
    title: '模板更新时间',
    dataIndex: 'gmtModified',
    dataType: 'datetime',
  },

  {
    title: '创建人',
    dataIndex: 'modifyPersonName',
    render: (modifyPersonName, txt) => (
      <UserLink userName={modifyPersonName} userId={txt.modifyPersonId} />
    ),
  },
  {
    title: '状态',
    dataIndex: 'templateStatus',
    render: templateStatus => <span>{CHANGE_TPL_STATUS_TEXT_MAP[templateStatus]}</span>,
  },

  {
    title: '操作',
    dataIndex: 'operation',
    width: 180,
    fixed: 'right',
    render: (__, { templateId, modifyPersonId, templateName, templateStatus, hasPermission }) => (
      <TableOptions
        {...{
          templateId,
          templateStatus,
          onApprovalOrRevert,
          hasPermission,
          templateName,
          modifyPersonId,
          getTemplateCopyInfoActionCreator,
          showConfirm,
        }}
      />
    ),
  },
];

function TableOptions({
  templateId,
  templateStatus,
  onApprovalOrRevert,
  hasPermission,
  templateName,
  modifyPersonId,
  getTemplateCopyInfoActionCreator,
  showConfirm,
}) {
  const [authorized] = useAuthorized({ checkByUserId: modifyPersonId });
  const [createAuthorized] = useAuthorized({ checkByCode: 'change_template_list_create' });
  const isApproving = templateStatus === CHANGE_TPL_STATUS_KEY_MAP.APPROVING;
  return (
    <div style={{ display: 'flex', justifyContent: 'flex-start', alignItems: 'center' }}>
      {createAuthorized &&
        !isApproving &&
        authorized && [
          <Link key="template_edit_link" to={generateChangeTemplateEdit({ id: templateId })}>
            编辑
          </Link>,
        ]}
      {isApproving &&
        hasPermission && [
          createAuthorized && !isApproving && (
            <Divider key="template_edit_link_approval_divider" type="vertical" />
          ),
          <Button
            key="approval_or_revert"
            type="link"
            compact
            onClick={() => onApprovalOrRevert(templateId)}
          >
            撤回
          </Button>,
        ]}
      {!isApproving &&
        authorized && [
          <Divider key="template_delete_divider" type="vertical" />,
          <Button
            key="template_delete"
            type="link"
            compact
            onClick={() => showConfirm(templateId, templateName)}
          >
            删除
          </Button>,
          <Divider key="template_copy_divider" type="vertical" />,
        ]}
      {!isApproving && [
        <Link key="copy" to={{ pathname: urls.CHANGE_TEMPLATE_NEW }}>
          <Button
            type="link"
            compact
            onClick={() => {
              getTemplateCopyInfoActionCreator({ id: templateId });
            }}
          >
            复制
          </Button>
        </Link>,
      ]}
    </div>
  );
}
