import moment from 'moment';

import { getLocationSearchMap, setLocationSearch } from '@manyun/base-ui.util.query-string';

export function setLocationSearchValues(searchValues, pagination) {
  const searchObj = Object.keys(searchValues ?? {}).reduce((mapper, key) => {
    if (key === 'modifiedTime') {
      const time = searchValues[key].value;
      mapper[key] =
        time?.length === 2 ? [moment(time[0]).valueOf(), moment(time[1]).valueOf()] : undefined;
    } else {
      mapper[key] = searchValues[key].value;
    }
    return mapper;
  }, {});
  setLocationSearch({
    searchValues: JSON.stringify(searchObj),
    pagination: JSON.stringify(pagination),
  });
}
export function getSearchValuesAndUpdateValues({ setPagination, updateSearchValues }) {
  const { pagination, searchValues } = getLocationSearchMap(window.location.search);
  if (pagination && setPagination) {
    try {
      const { pageNum, pageSize } = JSON.parse(pagination);
      setPagination?.({ pageNum: pageNum, pageSize: pageSize });
    } catch (e) {}
  }
  if (searchValues && updateSearchValues) {
    try {
      const object = JSON.parse(searchValues);
      const fileds = Object.keys(object).reduce((mapper, key) => {
        let value = object[key];
        if (key === 'modifyTime') {
          value = value.length === 2 ? [moment(value[0]), moment(value[1])] : undefined;
        }
        mapper[key] = {
          name: key,
          value: value,
        };
        return mapper;
      }, {});
      updateSearchValues({ ...fileds });
    } catch (e) {}
  }
}
