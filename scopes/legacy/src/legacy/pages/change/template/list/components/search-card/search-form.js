import React, { useEffect } from 'react';
import { connect } from 'react-redux';

import { FiltersForm, Form } from '@galiojs/awesome-antd';

import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Input } from '@manyun/base-ui.ui.input';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { ChangeTypeSelect } from '@manyun/ticket.ui.change-type-select';

import {
  changeActions,
  getChangeDataActionCreator,
  resetSearchValuesActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/changeActions';

import RiskLevelSelect from '../../../../components/risk-level-select';
import { getSearchValuesAndUpdateValues, setLocationSearchValues } from '../utils';

const { RangePicker } = DatePicker;

const items = [
  {
    label: '模板名称',
    name: 'templateName',
    control: <Input allowClear />,
  },

  {
    label: '模板ID',
    name: 'templateId',
    control: <Input allowClear />,
  },
  {
    label: '变更类型',
    name: 'changeType',
    control: <ChangeTypeSelect />,
  },
  {
    label: '变更等级',
    name: 'changeLevelList',
    control: <RiskLevelSelect mode="multiple" allowClear maxTagCount={1} />,
  },
  {
    label: '创建人',
    name: 'modifyPersonId',
    control: <UserSelect allowClear />,
  },
  {
    span: 2,
    label: '模板更新日期',
    name: 'modifyTime',
    control: <RangePicker format="YYYY-MM-DD" />,
  },
];

export function TplListSearchForm({
  onSearch,
  onReset,
  searchValues,
  pagination,
  updateSearchValues,
  setPagination,
}) {
  const [form] = Form.useForm();
  useEffect(() => {
    getSearchValuesAndUpdateValues({ setPagination, updateSearchValues });
    return () => {
      onReset();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onSearchData = () => {
    onSearch();
    setLocationSearchValues(searchValues, pagination);
  };

  return (
    <FiltersForm
      form={form}
      items={items}
      fields={Object.keys(searchValues).map(name => {
        const field = searchValues[name];
        return {
          ...field,
          // name 为数组形式
          name: name.split('.'),
        };
      })}
      showSearch
      onReset={() => {
        onReset();
        setLocationSearchValues({}, { pageNum: 1, pageSize: 10 });
      }}
      onSearch={onSearchData}
      onFieldsChange={changedFields => {
        updateSearchValues(
          changedFields.reduce((mapper, field) => {
            // field.name 为数组形式，老代码需要的是字符串形式
            const name = field.name.join('.');
            mapper[name] = {
              ...field,
              name,
            };
            return mapper;
          }, {})
        );
      }}
    />
  );
}

const mapStateToProps = ({
  change: {
    templateList: { searchValues, pagination },
  },
}) => ({ searchValues, pagination });
const mapDispatchToProps = {
  updateSearchValues: changeActions.updateSearchValues,
  onSearch: getChangeDataActionCreator,
  onReset: resetSearchValuesActionCreator,
  setPagination: changeActions.setPagination,
};

export default connect(mapStateToProps, mapDispatchToProps)(TplListSearchForm);
