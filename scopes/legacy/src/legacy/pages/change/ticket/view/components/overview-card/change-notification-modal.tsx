import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';

import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { Button } from '@manyun/base-ui.ui.button';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Popover } from '@manyun/base-ui.ui.popover';
import { Space } from '@manyun/base-ui.ui.space';
import type { ColumnType } from '@manyun/base-ui.ui.table';
import { Table } from '@manyun/base-ui.ui.table';
import { useLazyChangeNotifications } from '@manyun/ticket.gql.client.tickets';
import { CHANGE_TICKET_STATUS_TEXT_MAP } from '@manyun/ticket.model.change';
import type { ChangeTicketState } from '@manyun/ticket.model.change';
import type { BackendEventUpgradeChannel } from '@manyun/ticket.model.event';
import { EventUpgradeChannelMap } from '@manyun/ticket.model.event';

import type { NotificationInfo } from './change-notification-new';
import { ChangeNotificationNew } from './change-notification-new';
import { NotificationDetail } from './notification-detail';

export type ChangeNotificationModalProps = {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  changeInfo: any;
  deviceGuids: string[];
  showNewDetail?: boolean;
};

export type Notification = {
  id: number;
  gmtCreate: string;
  reportContent: string;
  creatorId: number;
  creatorName: string;
  reportChannel: string;
  changeStatus: ChangeTicketState;
  reportObject: ReportObject[];
};

export type ReportObject = {
  type: string;
  code: string;
  name: string;
};
const columns = (total: number): ColumnType<Notification>[] => [
  {
    title: '序号',
    dataIndex: 'id',
    render: (_, __, index) => total - index,
  },
  {
    title: '通报时间',
    dataIndex: 'gmtCreate',
    render: (_, { gmtCreate }) => dayjs(gmtCreate).format('YYYY-MM-DD HH:mm:ss'),
  },

  {
    title: '变更状态',
    dataIndex: 'changeStatus',
    render: (_, { changeStatus }) => CHANGE_TICKET_STATUS_TEXT_MAP[changeStatus],
  },
  {
    title: '通报内容',
    dataIndex: 'reportContent',
    render: (_, { reportContent }) => {
      return (
        <Popover
          title="通报内容"
          overlayInnerStyle={{ maxWidth: 560, maxHeight: 480 }}
          content={<NotificationDetail reportContent={reportContent} />}
        >
          <Button type="link" compact>
            查看
          </Button>
        </Popover>
      );
    },
  },

  {
    title: '通报对象',
    dataIndex: 'reportObject',
    render: (_, { reportObject }) => {
      return (
        <Space size={0} split={<Divider type="vertical" spaceSize="mini" emphasis />}>
          {reportObject?.map(({ name }) => <span key={name}>{name}</span>)}
        </Space>
      );
    },
  },
  {
    title: '通报方式',
    dataIndex: 'reportChannel',
    render: (_, { reportChannel }) => {
      return (
        <Space>
          {reportChannel
            ?.split(',')
            ?.map(channel => EventUpgradeChannelMap[channel as BackendEventUpgradeChannel])}
        </Space>
      );
    },
  },
  {
    title: '通报人',
    dataIndex: 'creatorName',
    render: (_, { creatorName, creatorId }) => (
      <UserLink external userId={creatorId} userName={creatorName} />
    ),
  },
];
export function ChangeNotificationModal({
  changeInfo,
  deviceGuids,
  showNewDetail,
}: ChangeNotificationModalProps) {
  const [visible, setVisible] = useState<boolean>(false);
  const notificationInfo: NotificationInfo = showNewDetail
    ? {
        id: changeInfo.changeOrderId,
        idcTag: changeInfo.idcTag,
        blockGuid: changeInfo.blockGuid,
        riskLevel: changeInfo.riskLevel,
        changeStatus: changeInfo.changeStatus,
        title: changeInfo.title,
        planStartTime: changeInfo.planStartTime,
        planEndTime: changeInfo.planEndTime,
        reason: changeInfo.reason,
        changeCategory: changeInfo.changeCategory,
        changeType: changeInfo.changeType,
        changeResponsibleUserInfoList: changeInfo.changeResponsibleUserList,
        changeVersion: changeInfo.changeVersion,
        changeInfluence: changeInfo.changeInfluence,
      }
    : {
        id: changeInfo.changeOrderId,
        idcTag: changeInfo.idcTag,
        blockGuid: changeInfo.blockTag,
        riskLevel: changeInfo.riskLevel.value,
        changeStatus: changeInfo.changeStatus,
        title: changeInfo.title.value,
        planStartTime: changeInfo.planStartTime.value,
        planEndTime: changeInfo.planEndTime.value,
        reason: changeInfo.reason.value.label,
        causeDevices: deviceGuids,
        changeExeWay: changeInfo.exeWay,
        changeInfluence: changeInfo.changeInfluence,
        creatorName: changeInfo.creatorName,
        changeVersion: changeInfo.changeVersion,
        changeTypeName: changeInfo.changeTypeName,
        influenceArea: changeInfo.influenceArea,
        respPersonName: changeInfo.respPersonName,
        operatorName: changeInfo.operatorName,
        relateCustomer: changeInfo.relateCustomer,
      };
  const [lazyChangeNotifications, { data, refetch }] = useLazyChangeNotifications();
  const showModal = () => {
    setVisible(true);
  };
  const closeModal = () => {
    setVisible(false);
  };
  useEffect(() => {
    if (visible) {
      lazyChangeNotifications({ variables: { changeId: changeInfo.changeOrderId } });
    }
  }, [visible, changeInfo.changeOrderId, lazyChangeNotifications]);

  return (
    <>
      <Button type="primary" onClick={showModal}>
        手动通报
      </Button>
      <Modal
        width={1200}
        title={`${changeInfo.changeOrderId}变更通报`}
        open={visible}
        footer={null}
        onCancel={closeModal}
      >
        <Space style={{ width: '100%' }} direction="vertical">
          <ChangeNotificationNew
            notificationInfo={{
              ...notificationInfo,
            }}
            id={changeInfo.id}
            getNotificationList={refetch}
            showNewDetail={showNewDetail}
          />
          <Table<Notification>
            rowKey="id"
            columns={columns(data?.changeNotifications?.total ?? 0)}
            dataSource={data?.changeNotifications?.data ?? []}
            pagination={{
              total: data?.changeNotifications?.total,
            }}
          />
        </Space>
      </Modal>
    </>
  );
}
