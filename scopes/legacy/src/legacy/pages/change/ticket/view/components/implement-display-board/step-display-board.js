import React, { useEffect } from 'react';
import { connect } from 'react-redux';
import { useParams } from 'react-router-dom';

import CheckCircleOutlined from '@ant-design/icons/es/icons/CheckCircleOutlined';
import ExclamationCircleOutlined from '@ant-design/icons/es/icons/ExclamationCircleOutlined';
import LoadingOutlined from '@ant-design/icons/es/icons/LoadingOutlined';
import dayjs from 'dayjs';
import get from 'lodash/get';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { SimpleFileList } from '@manyun/base-ui.ui.file-list';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';

import { getDiPointValueText } from '@manyun/monitoring.model.point';

import {
  D<PERSON><PERSON>r<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rapper,
  StatusText,
  TinyCard,
  UserLink,
} from '@manyun/dc-brain.legacy.components';
import {
  cancelTicketRealtimeData,
  changeActions,
  checkItemArtificialValidationActionCreator,
  getAlarmLevelCount,
  getRealtimeDataAction,
  getTicketDetailAlarmList,
  getTicketStepDetail,
  opArtificialValidationActionCreator,
  stepStopActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/changeActions';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import { generateGetDeviceMonitoringData } from '@manyun/dc-brain.legacy.utils/device';

import { StyledTinyTable } from '../../../../components/step-table-list/components/styled';
import {
  // checkItemNormalized,
  // getMaxInfluencesStep,
  generateValidLimitsDataSourceLabel,
} from '../../../../components/step-table-list/constants';
import {
  CHANGE_CHECK_METHOD_TEXT_MAP,
  CHANGE_OP_ITEM_STATUS_KEY_MAP,
  CHANGE_OP_ITEM_STATUS_TEXT_MAP,
  CHANGE_STEP_ITEM_CHECK_STATUS_KEY_MAP,
  CHANGE_STEP_STATUS_KEY_MAP,
  CHANGE_STEP_TYPE_KEY_MAP,
  CHANGE_TICKET_STATUS_KEY_MAP,
  CHANGE_TICKET_STATUS_STEP,
  LIMIT_TEXT_MAP,
} from '../../../../constants';
import { getStatusText } from '../../constants';
import StepTable from './components/step-table';
import Steps from './components/steps';
import { VirtualTable } from './virtual-table';

const runColumn = (
  status,
  handleOpArtificialValidation,
  changeStatus,
  stepStatus,
  hasPermission
) => [
  {
    title: '目标单位',
    dataIndex: 'itemName',
  },
  {
    title: '操作时间',
    dataIndex: 'opTime',
    dataType: 'datetime',
  },
  {
    title: '操作人',
    dataIndex: 'operatorName',
    render: (text, { operatorId }) => <UserLink userName={text} userId={operatorId} />,
  },
  {
    title: '状态',
    dataIndex: 'itemStatus',

    render: (text, record) => {
      // status < 3 为状态在 变更中 之前
      if (status < 3 || !hasPermission) {
        return '--';
      }

      if (
        changeStatus === CHANGE_TICKET_STATUS_KEY_MAP.CHANGING &&
        [CHANGE_OP_ITEM_STATUS_KEY_MAP.NOT_START, CHANGE_OP_ITEM_STATUS_KEY_MAP.CHECKING].includes(
          text
        ) &&
        stepStatus === CHANGE_STEP_STATUS_KEY_MAP.PROCESSING
      ) {
        return (
          <GutterWrapper>
            <Button
              type="primary"
              onClick={() =>
                handleOpArtificialValidation(record, CHANGE_STEP_ITEM_CHECK_STATUS_KEY_MAP.NORMAL)
              }
            >
              正常
            </Button>

            <Button
              type="danger"
              onClick={() =>
                handleOpArtificialValidation(
                  record,
                  CHANGE_STEP_ITEM_CHECK_STATUS_KEY_MAP.EXCEPTION
                )
              }
            >
              异常
            </Button>
          </GutterWrapper>
        );
      }
      return (
        <StatusText status={getStatusText(text)}>
          {CHANGE_OP_ITEM_STATUS_TEXT_MAP[CHANGE_OP_ITEM_STATUS_KEY_MAP[text]] || '--'}
        </StatusText>
      );
    },
  },
];

function getStatusIcon(itemStatus, text, type) {
  let style = {};
  if (type === 'pointName') {
    style = { marginRight: 16, display: 'inline-block' };
  } else {
    style = { marginRight: 8 };
  }

  if (itemStatus === CHANGE_OP_ITEM_STATUS_KEY_MAP.NOT_START) {
    return text;
  }
  if (itemStatus === CHANGE_OP_ITEM_STATUS_KEY_MAP.CHECKING) {
    const antIcon = <LoadingOutlined style={{ color: `var(--${prefixCls}-success-color)` }} spin />;
    return (
      <>
        <Spin indicator={antIcon} style={style} />
        {text}
      </>
    );
  }
  if (itemStatus === CHANGE_OP_ITEM_STATUS_KEY_MAP.NORMAL) {
    return (
      <>
        <CheckCircleOutlined style={{ color: `var(--${prefixCls}-success-color)`, ...style }} />
        {text}
      </>
    );
  }
  if (itemStatus === CHANGE_OP_ITEM_STATUS_KEY_MAP.EXCEPTION) {
    return (
      <>
        <ExclamationCircleOutlined
          style={{ color: `var(--${prefixCls}-warning-color)`, ...style }}
        />
        {text}
      </>
    );
  }
}

const opDeviceColumn = (status, getData, stepStatus, pointsDefinitionMap) => [
  {
    title: '目标设备',
    dataIndex: 'itemName',
  },
  {
    title: '验证测点',
    dataIndex: 'pointName',
    render: (text, { itemStatus }) => getStatusIcon(itemStatus, text, 'pointName'),
  },
  {
    title: '测点值',
    dataIndex: 'pointValueText',
    render: (__, record) => {
      if (stepStatus === CHANGE_OP_ITEM_STATUS_KEY_MAP.NOT_START) {
        return '--';
      }
      if (stepStatus !== CHANGE_STEP_STATUS_KEY_MAP.PROCESSING) {
        const { validLimits } = get(pointsDefinitionMap, [record.deviceType, record.pointCode], '');
        // return getDiPointValueText(record.pointData, validLimits);
        const pointValue = getDiPointValueText(record.pointData, validLimits);
        if (pointValue === String(record.pointData)) {
          return <DIErrorTooltip title={record.pointData} />;
        }
        return pointValue;
      }
      const { value } = getData(
        { deviceGuid: record.itemCode, deviceType: record.deviceType },
        { hardCodedPointCode: record.pointCode, reflected: true }
      );
      return value ? value.NAME : '--';
    },
  },
  {
    title: '操作时间',
    dataIndex: 'opTime',
    dataType: 'datetime',
    render: text => {
      if (!text) {
        return '--';
      }
      return dayjs(text).format('YYYY-MM-DD HH:mm:ss');
    },
  },
  {
    title: '操作人',
    dataIndex: 'operatorName',
    render: (text, { operatorId }) => <UserLink userName={text} userId={operatorId} />,
  },
  {
    title: '状态',
    dataIndex: 'itemStatus',
    fixed: 'right',
    render: text => {
      if (status < 3) {
        return '--';
      }
      return (
        <StatusText status={getStatusText(text)}>
          {CHANGE_OP_ITEM_STATUS_TEXT_MAP[CHANGE_OP_ITEM_STATUS_KEY_MAP[text]]}
        </StatusText>
      );
    },
  },
];

const customerColumn = (
  status,
  handleCustomerStop,
  changeStatus,
  stepStatus,
  hasPermission,
  stepId
) => [
  {
    title: '附件',
    dataIndex: 'fileInfoList',
    render: fileInfoList => {
      if (Array.isArray(fileInfoList) && fileInfoList.length) {
        return (
          <SimpleFileList files={fileInfoList}>
            <Button type="link" compact>
              {fileInfoList[0].name}
            </Button>
          </SimpleFileList>
        );
      }
      return '--';
    },
  },
  {
    title: '操作时间',
    dataIndex: 'opTime',
    dataType: 'datetime',
  },
  {
    title: '操作人',
    dataIndex: 'operatorName',
    render: (text, { operatorId }) =>
      status > 3 ? <UserLink userName={text} userId={operatorId} /> : '--',
  },
  {
    title: '状态',
    dataIndex: 'itemStatus',
    render: (text, record) => {
      // status < 3 为状态在 变更中 之前
      if (status < 3 || !hasPermission) {
        return '--';
      }

      if (
        changeStatus === CHANGE_TICKET_STATUS_KEY_MAP.CHANGING &&
        [CHANGE_STEP_STATUS_KEY_MAP.PROCESSING].includes(text)
      ) {
        return (
          <Space>
            <Button
              type="primary"
              onClick={() =>
                handleCustomerStop(CHANGE_STEP_ITEM_CHECK_STATUS_KEY_MAP.NORMAL, stepId)
              }
            >
              正常结束
            </Button>

            <Button
              type="danger"
              onClick={() =>
                handleCustomerStop(CHANGE_STEP_ITEM_CHECK_STATUS_KEY_MAP.EXCEPTION, stepId)
              }
            >
              异常结束
            </Button>
          </Space>
        );
      }
      return (
        <StatusText status={getStatusText(text)}>
          {CHANGE_OP_ITEM_STATUS_TEXT_MAP[CHANGE_OP_ITEM_STATUS_KEY_MAP[text]] || '--'}
        </StatusText>
      );
    },
  },
];

// function merge(index, record, checkItemInfo, name) {
//   let mergeNumber;
//   const normalizedList = checkItemNormalized(checkItemInfo, name);
//   if (index === 0) {
//     mergeNumber =
//       normalizedList && normalizedList[record[name]] && normalizedList[record[name]].length;
//   } else {
//     const up = checkItemInfo.slice(0, index);
//     const down = checkItemInfo.slice(index);

//     if (up.filter(item => item[name] === record[name]).length) {
//       mergeNumber = 0;
//     } else if (down.filter(item => item[name] === record[name]).length) {
//       mergeNumber = normalizedList[record[name]].length;
//     }
//   }
//   return mergeNumber;
// }

const checkItemColumns = (
  metaCategoryEntities,
  status,
  handleCheckItemArtificialValidation,
  changeStatus,
  stepStatus,
  stepMaps,
  getData,
  checkItemInfo,
  stepCodes,
  pointsDefinitionMap,
  opAllNormal,
  hasPermission
) => [
  {
    title: '影响设备类型',
    dataIndex: 'deviceType',
    // className: 'merge-style',
    width: 200,
    ellipsis: true,
    // merge: (_text, record, index) => {
    //   return { rowSpan: merge(index, record, checkItemInfo, 'deviceType') };
    // },
    render: text => {
      if (!text) {
        return '';
      }
      return metaCategoryEntities[text]?.metaName;
    },
  },
  {
    title: '设备名称',
    dataIndex: 'deviceTag',
    // className: 'merge-style',
    width: 200,
    ellipsis: true,
    // merge: (_text, record, index) => {
    //   return { rowSpan: merge(index, record, checkItemInfo, 'deviceGuid') };
    // },
    render: text => {
      if (!text) {
        return '';
      }
      return text;
    },
  },
  {
    title: '影响设备测点',
    dataIndex: 'pointName',
    // width: 300,
    ellipsis: true,
    render: (text, { itemStatus }) => getStatusIcon(itemStatus, text),
  },
  {
    title: '验证方式',
    dataIndex: 'identifyWay',
    width: 120,
    render: text => <span>{CHANGE_CHECK_METHOD_TEXT_MAP[text]}</span>,
  },
  {
    title: '预期判断',
    dataIndex: 'expectedValue',
    width: 120,
    ellipsis: true,
    render: (text, record) => {
      if (!record.pointCode) {
        return record.expectedResult;
      }
      if (record.dataType === 'AI') {
        return `${LIMIT_TEXT_MAP[record.operatorList[0]]}${text} ${record.unit}`;
      }
      if (record.dataType === 'DI') {
        return text
          .map(item => {
            return generateValidLimitsDataSourceLabel(record.pointValueText, item);
          })
          .join(',');
      }
    },
  },
  {
    title: '测点值',
    dataIndex: 'pointData',
    width: 120,
    render: (text, record) => {
      if (
        stepStatus === CHANGE_OP_ITEM_STATUS_KEY_MAP.NOT_START ||
        record.itemStatus === CHANGE_OP_ITEM_STATUS_KEY_MAP.NOT_START
      ) {
        return '--';
      }
      if (status !== 3 && record.pointData === null) {
        return '--';
      }

      if (stepStatus !== CHANGE_STEP_STATUS_KEY_MAP.PROCESSING && record.dataType === 'AI') {
        return `${text} ${record.unit}`;
      }
      if (stepStatus !== CHANGE_STEP_STATUS_KEY_MAP.PROCESSING && record.dataType === 'DI') {
        const { validLimits } = get(pointsDefinitionMap, [record.deviceType, record.pointCode], '');
        const value = getDiPointValueText(record.pointData, validLimits);
        if (value === String(text)) {
          return <DIErrorTooltip title={text} />;
        }
        return value;
      }
      const params = { hardCodedPointCode: record.pointCode };
      if (record.dataType === 'AI') {
        params.formatted = true;
      }
      if (record.dataType === 'DI') {
        params.reflected = true;
      }
      const { formattedText, value } = getData(
        { deviceGuid: record.deviceGuid, deviceType: record.deviceType },
        params
      );

      if (record.dataType === 'DI') {
        return value?.NAME;
      }
      return formattedText;
    },
  },
  {
    title: `影响周期`,
    dataIndex: 'maxInfluencesStep',
    width: 160,
    ellipsis: true,
    render: text => <span>{text ? stepMaps[stepCodes[text - 1]]?.stepName : '--'}</span>,
  },
  {
    title: '状态',
    dataIndex: 'itemStatus',
    width: 160,
    // fixed: 'right',
    filters: [
      {
        text: '未开始',
        value: CHANGE_OP_ITEM_STATUS_KEY_MAP.NOT_START,
      },
      {
        text: '正常',
        value: CHANGE_OP_ITEM_STATUS_KEY_MAP.NORMAL,
      },
      {
        text: '异常',
        value: CHANGE_OP_ITEM_STATUS_KEY_MAP.EXCEPTION,
      },
    ],
    onFilter: (value, record) => record.itemStatus === value,
    render: (text, record) => {
      if (status < 3) {
        return '--';
      }
      if (
        changeStatus === CHANGE_TICKET_STATUS_KEY_MAP.CHANGING &&
        !record.pointCode &&
        [CHANGE_OP_ITEM_STATUS_KEY_MAP.NOT_START, CHANGE_OP_ITEM_STATUS_KEY_MAP.CHECKING].includes(
          text
        ) &&
        stepStatus === CHANGE_STEP_STATUS_KEY_MAP.PROCESSING &&
        opAllNormal
      ) {
        return (
          <GutterWrapper>
            <Popconfirm
              title="确定正常吗?"
              okText="确定"
              cancelText="取消"
              onConfirm={() =>
                handleCheckItemArtificialValidation(
                  record,
                  CHANGE_STEP_ITEM_CHECK_STATUS_KEY_MAP.NORMAL
                )
              }
            >
              <Button type="primary" disabled={!hasPermission}>
                正常
              </Button>
            </Popconfirm>
            <Popconfirm
              title="确定异常吗?"
              okText="确定"
              cancelText="取消"
              disabled={!hasPermission}
              onConfirm={() =>
                handleCheckItemArtificialValidation(
                  record,
                  CHANGE_STEP_ITEM_CHECK_STATUS_KEY_MAP.EXCEPTION
                )
              }
            >
              <Button danger disabled={!hasPermission}>
                异常
              </Button>
            </Popconfirm>
          </GutterWrapper>
        );
      } else {
        return (
          <StatusText status={getStatusText(text)}>
            {CHANGE_OP_ITEM_STATUS_TEXT_MAP[CHANGE_OP_ITEM_STATUS_KEY_MAP[text]]}
          </StatusText>
        );
      }
    },
  },
];
export function TicketStep({
  categoryDisplayBarList,
  list,
  total,
  pageNum,
  pageSize,
  metaCategoryEntities,
  syncCommonData,
  getTicketDetailAlarmList,
  getAlarmLevelCount,
  setAlarmLevelCount,
  getTicketStepDetail,
  className,
  stepList,
  opItemInfoMaps,
  stepOrder,
  getTicketRealtimeData,
  cancelTicketRealtimeData,
  checkItemInfoMaps,
  changeStatus,
  checkItemArtificialValidationActionCreator,
  opArtificialValidationActionCreator,
  pointsDefinitionMap,
  devicesRealtimeData,
  idcTag,
  blockTag,
  setTicketViewStepOrder,
  stepMaps,
  setTicketStepCheckItemMaps,
  selectedCheckItemInfoMaps,
  pointsDefinition,
  stepCodes,
  setTicketStepDetail,
  stepCurrent,
  hasPermission,
  stepStopActionCreator,
  ...props
}) {
  const { id } = useParams();
  const status = CHANGE_TICKET_STATUS_STEP[changeStatus];

  useEffect(() => {
    syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });
    getTicketStepDetail({ changeOrderId: id });
  }, [getTicketStepDetail, syncCommonData, id]);

  useEffect(() => {
    if (stepList.length === 1) {
      const stepInfo = stepList.filter(item => item.stepOrder === stepOrder)[0];
      const checkItemInfo = checkItemInfoMaps[stepOrder];
      const isPointValueText = checkItemInfo.filter(item => item.pointCode && !item.pointData);
      if (
        stepInfo.stepStatus === 'PROCESSING' &&
        isPointValueText.length > 0 &&
        stepInfo.stepType !== CHANGE_STEP_TYPE_KEY_MAP.RUN &&
        stepInfo.stepType !== CHANGE_STEP_TYPE_KEY_MAP.CUSTOMIZE
      ) {
        cancelTicketRealtimeData();
        getTicketRealtimeData();
      } else {
        cancelTicketRealtimeData();
      }
    } else {
      cancelTicketRealtimeData();
    }
  }, [stepList, getTicketRealtimeData, stepOrder, cancelTicketRealtimeData, checkItemInfoMaps]);

  useEffect(() => {
    syncCommonData({ strategy: { pointsDefinition: pointsDefinition } });
  }, [pointsDefinition, syncCommonData]);

  useEffect(() => {
    return () => cancelTicketRealtimeData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onChangeCheckItem = (pagination, filters) => {
    if (!filters.itemStatus.length) {
      setTicketStepCheckItemMaps({
        ...checkItemInfoMaps,
      });
      return;
    }
    const selectList = checkItemInfoMaps[stepOrder].filter(item =>
      filters.itemStatus.includes(item.itemStatus)
    );
    if (!selectList.length) {
      setTicketStepCheckItemMaps({ ...checkItemInfoMaps, [stepOrder]: [] });
    } else {
      setTicketStepCheckItemMaps({ ...selectedCheckItemInfoMaps, [stepOrder]: selectList });
    }
  };

  const getOPTable = () => {
    const stepInfos = stepList.filter(item => item.stepOrder === stepOrder);
    const opInfo = opItemInfoMaps[stepOrder];
    const stepInfo = stepInfos.length ? stepInfos[0] : {};
    if (stepInfo.stepType === CHANGE_STEP_TYPE_KEY_MAP.OP && stepInfo.opType === 'LIMIT') {
      return (
        <StyledTinyTable
          rowKey="opItemId"
          columns={opDeviceColumn(status, getData, stepInfo.stepStatus, pointsDefinitionMap)}
          dataSource={opInfo}
          size="small"
          pagination={false}
          align="left"
        />
      );
    }
    if (stepInfo.stepType === CHANGE_STEP_TYPE_KEY_MAP.RUN) {
      return (
        <StyledTinyTable
          rowKey="opItemId"
          columns={runColumn(
            status,
            handleOpArtificialValidation,
            changeStatus,
            stepInfo.stepStatus,
            hasPermission
          )}
          dataSource={opInfo}
          size="small"
          pagination={false}
          align="left"
        />
      );
    }
    if (stepInfo.stepType === CHANGE_STEP_TYPE_KEY_MAP.CUSTOMIZE) {
      return (
        <StyledTinyTable
          rowKey="opItemId"
          columns={customerColumn(
            status,
            handleCustomerStop,
            changeStatus,
            stepInfo.stepStatus,
            hasPermission,
            stepInfo.id
          )}
          dataSource={opInfo}
          size="small"
          pagination={false}
          align="left"
        />
      );
    }
  };

  const getCheckItemTable = () => {
    const stepInfos = stepList.filter(item => item.stepOrder === stepOrder);
    const checkItemInfo = selectedCheckItemInfoMaps[stepOrder];
    const stepInfo = stepInfos.length ? stepInfos[0] : {};
    let opAllNormal = true;
    if (stepInfo.stepType === CHANGE_STEP_TYPE_KEY_MAP.OP && stepInfo.opType === 'LIMIT') {
      const opInfo = opItemInfoMaps[stepOrder];
      opAllNormal = opInfo.every(i => i.itemStatus === CHANGE_OP_ITEM_STATUS_KEY_MAP.NORMAL);
    }

    if (
      stepInfo.stepType &&
      stepInfo.stepType !== CHANGE_STEP_TYPE_KEY_MAP.RUN &&
      stepInfo.stepType !== CHANGE_STEP_TYPE_KEY_MAP.CUSTOMIZE
    ) {
      return (
        <VirtualTable
          rowKey="checkItemId"
          columns={checkItemColumns(
            metaCategoryEntities,
            status,
            handleCheckItemArtificialValidation,
            changeStatus,
            stepInfo.stepStatus,
            stepMaps,
            getData,
            checkItemInfo,
            stepCodes,
            pointsDefinitionMap,
            opAllNormal,
            hasPermission
          )}
          dataSource={checkItemInfo}
          size="small"
          pagination={false}
          scroll={{ y: 400 }}
          onChange={onChangeCheckItem}
        />
      );
    }
  };

  const handleCheckItemArtificialValidation = (checkItem, itemStatus) => {
    checkItemArtificialValidationActionCreator({
      changeOrderId: id,
      checkItemId: checkItem.checkItemId,
      itemStatus,
    });
  };

  const handleOpArtificialValidation = (opItem, itemStatus) => {
    opArtificialValidationActionCreator({
      changeOrderId: id,
      opItemId: opItem.opItemId,
      opResult: itemStatus,
    });
  };

  const handleCustomerStop = (customerStepStatus, stepId) => {
    stepStopActionCreator({
      changeOrderId: id,
      stepId,
      stepOrder: stepOrder,
      customerStepStatus,
    });
  };

  const setStepOrder = stepCurrent => {
    setTicketViewStepOrder(stepCurrent);
  };

  const getData = generateGetDeviceMonitoringData(
    devicesRealtimeData,
    undefined,
    pointsDefinitionMap
  );

  const normal = () => {
    const needToDetermine = [];
    const opItem = opItemInfoMaps[stepOrder] || [];
    const checkItem = checkItemInfoMaps[stepOrder] || [];
    if (opItem && Array.isArray(opItem)) {
      needToDetermine.push(...opItem);
    }
    if (checkItem && Array.isArray(checkItem)) {
      needToDetermine.push(...checkItem);
    }

    // if (step[0].stepType === CHANGE_STEP_TYPE_KEY_MAP.RUN) {
    //跑位步骤时的按钮
    const nomalList = needToDetermine.filter(
      item => item.itemStatus === CHANGE_OP_ITEM_STATUS_KEY_MAP.NORMAL
    ).length;
    // let exceptionList = opItem.filter(
    //   item => item.itemStatus === CHANGE_OP_ITEM_STATUS_KEY_MAP.EXCEPTION
    // ).length;
    // if (normalList === opItem.length) {
    //   return stopBtn;
    // }
    // if (exceptionList + normalList === opItem.length && exceptionList) {
    //   return skipBtn;
    // }
    // return null;
    // }

    return needToDetermine.length === nomalList ? true : false;
  };

  return (
    <TinyCard>
      <GutterWrapper mode="vertical">
        <Steps {...props} setTicketViewStepOrder={setStepOrder} stepCurrent={stepOrder - 1} />
        <div>
          <StepTable isNormal={normal()} />
          {getOPTable()}
          {getCheckItemTable()}
        </div>
        {/* <FooterToolBar> */}

        {/* </FooterToolBar> */}
      </GutterWrapper>
    </TinyCard>
  );
}

const mapStateToProps = ({
  change: {
    ticketDetail: {
      step: {
        stepList,
        checkItemInfoMaps,
        opItemInfoMaps,
        stepOrder,
        // TODO: @Jerry deprecate this field
        // pointValue,
        selectedCheckItemInfoMaps,
        pointsDefinition,
      },
      changeInfo: {
        step1: { changeStatus, idcTag, blockTag, hasPermission },
        step2: { stepMaps, stepCodes },
      },
    },
  },
  common: { deviceCategory },
  config: { configMap },
  'monitoring.subscriptions': { devicesRealtimeData },
}) => ({
  stepList,
  stepOrder,
  checkItemInfoMaps,
  opItemInfoMaps,
  metaCategoryEntities: deviceCategory ? deviceCategory.normalizedList : {},
  changeStatus,
  pointsDefinitionMap: configMap.current.pointsDefinitionMap || {},
  devicesRealtimeData,
  idcTag,
  blockTag,
  stepMaps,
  selectedCheckItemInfoMaps,
  pointsDefinition,
  stepCodes,
  hasPermission,
});

const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
  getTicketDetailAlarmList: getTicketDetailAlarmList,
  getAlarmLevelCount: getAlarmLevelCount,
  setAlarmLevelCount: changeActions.setAlarmLevelCount,
  getTicketStepDetail: getTicketStepDetail,
  getTicketRealtimeData: getRealtimeDataAction,
  cancelTicketRealtimeData: cancelTicketRealtimeData,
  checkItemArtificialValidationActionCreator: checkItemArtificialValidationActionCreator,
  opArtificialValidationActionCreator: opArtificialValidationActionCreator,
  setTicketViewStepOrder: changeActions.setTicketViewStepOrder,
  setTicketStepCheckItemMaps: changeActions.setTicketStepCheckItemMaps,
  setTicketStepDetail: changeActions.setTicketStepDetail,
  stepStopActionCreator: stepStopActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(TicketStep);
