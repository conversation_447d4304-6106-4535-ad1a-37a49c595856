import React, { useState } from 'react';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { useStopChangeOnline } from '@manyun/ticket.gql.client.tickets';

export type StopButtonProps = {
  stepId: number;
  changeOrderId: string;
  startTime: number;
  onSuccess: () => void;
} & Omit<ButtonProps, 'onClick'>;

export function StopButton({ changeOrderId, onSuccess }: StopButtonProps) {
  const [visible, setVisible] = useState(false);
  const [form] = Form.useForm();
  const [stop, { loading }] = useStopChangeOnline({
    onCompleted(data) {
      if (!data.stopChangeOnline.success) {
        message.error(data.stopChangeOnline.message);
        return;
      }
      setVisible(false);
      onSuccess();
    },
  });

  const onSubmit = async () => {
    form.validateFields().then(values => {
      stop({
        variables: {
          query: {
            changeOrderId,
            stopReason: values.stopReason,
          },
        },
      });
    });
  };

  return (
    <>
      <Button
        loading={loading}
        onClick={() => {
          setVisible(true);
        }}
      >
        变更终止
      </Button>
      <Modal
        title="变更终止"
        open={visible}
        okText="确认终止"
        width={640}
        afterClose={() => form.resetFields()}
        onCancel={() => {
          setVisible(false);
        }}
        onOk={onSubmit}
      >
        <Alert
          description="是否终止变更？终止后变更单将直接进入总结阶段"
          type="warning"
          style={{ marginBottom: 24 }}
          showIcon
        />
        <Form form={form} labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
          <Form.Item
            label="变更终止原因"
            name="stopReason"
            rules={[
              { required: true, message: '变更终止原因必填！', whitespace: true },
              { max: 300, message: '最多输入 300 个字符！' },
            ]}
          >
            <Input.TextArea rows={3} showCount maxLength={300} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
