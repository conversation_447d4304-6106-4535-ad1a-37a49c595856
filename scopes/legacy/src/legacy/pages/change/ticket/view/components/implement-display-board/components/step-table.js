import React, { useEffect, useRef, useState } from 'react';
import { connect } from 'react-redux';

import moment from 'moment';

import { Typography } from '@manyun/base-ui.ui.typography';

import { SLA_UNIT_TYPE_TEXT_MAP, SlaUnitTypeKeyMap } from '@manyun/ticket.model.change';
import { TicketSlaText } from '@manyun/ticket.ui.tickets-table';

// import { get } from 'lodash';
import { StatusText, UserLink } from '@manyun/dc-brain.legacy.components';
import { STATUS_MAP } from '@manyun/dc-brain.legacy.constants/statusColor';

import { StyledTinyTable } from '../../../../../components/step-table-list/components/styled';
import {
  CHANGE_CHECK_METHOD_KEY_MAP,
  CHANGE_CHECK_METHOD_TEXT_MAP,
  CHANGE_EXECUTE_METHOD_KEY_MAP,
  CHANGE_EXECUTE_METHOD_TEXT_MAP,
  CHANGE_STEP_STATUS_KEY_MAP,
  CHANGE_STEP_STATUS_TEXT_MAP,
  CHANGE_STEP_TYPE_KEY_MAP,
  CHANGE_STEP_TYPE_TEXT_MAP,
  CHANGE_TICKET_STATUS_KEY_MAP,
} from '../../../../../constants';
import { getStatusText } from '../../../constants';

export function StepTable({ className, stepList, stepOrder, changeStatus, isNormal }) {
  const [difference, settDifference] = useState(null);
  const intervalRef = useRef();
  const delay = 60 * 1000;
  const stepDataSource = stepList.length
    ? stepList.filter(item => item.stepOrder === stepOrder)
    : [];
  const callback = () => {
    if (stepDataSource.length && stepDataSource[0].endTime) {
      let difference = moment(stepDataSource[0].endTime).diff(
        moment(stepDataSource[0].startTime),
        'minute'
      );
      settDifference(difference);
    } else if (stepDataSource.length && !stepDataSource[0].endTime) {
      const now = moment(new Date().getTime());
      const startTime = moment(stepDataSource[0].startTime);
      const difference = now.diff(startTime, 'minute'); //计算相差的分钟数
      settDifference(difference);
    }
  };

  useEffect(() => {
    callback();
    if (changeStatus === CHANGE_TICKET_STATUS_KEY_MAP.CHANGING && stepDataSource.length === 1) {
      intervalRef.current && window.clearInterval(intervalRef.current);
      intervalRef.current = window.setInterval(callback, delay);
    } else {
      intervalRef.current && window.clearInterval(intervalRef.current);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [stepList, stepOrder, changeStatus]);

  useEffect(() => {
    return () => {
      window.clearInterval(intervalRef.current);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const getColumns = () => {
    if (stepDataSource.length) {
      const data = stepDataSource[0];
      if (data.stepType === CHANGE_STEP_TYPE_KEY_MAP.OP && data.opType === 'LIMIT') {
        return deviceColumn(difference, changeStatus, isNormal);
      }
      if (data.stepType === CHANGE_STEP_TYPE_KEY_MAP.OP && data.opType === 'CUSTOMIZE') {
        return otherColumn(difference, changeStatus, isNormal);
      }
      if (data.stepType === CHANGE_STEP_TYPE_KEY_MAP.WATCH) {
        return watchColumn(difference, changeStatus, isNormal);
      }
      if (data.stepType === CHANGE_STEP_TYPE_KEY_MAP.RUN) {
        return runColumn(difference, changeStatus, isNormal);
      }
      if (data.stepType === CHANGE_STEP_TYPE_KEY_MAP.CUSTOMIZE) {
        return customerColumn(difference, changeStatus, isNormal);
      }
    }
    return [];
  };

  return (
    <StyledTinyTable
      className={className}
      rowKey="id"
      columns={getColumns()}
      dataSource={stepDataSource}
      size="small"
      pagination={false}
      scroll={{ x: 'max-content' }}
    />
  );
}
function OLA({ ola, difference, changeStatus, stepStatus, olaUnit }) {
  if (ola === 0 || !difference) {
    return '--';
  }
  if (
    changeStatus === CHANGE_TICKET_STATUS_KEY_MAP.WAITING_CHANGE ||
    stepStatus === CHANGE_STEP_STATUS_KEY_MAP.NOT_START
  ) {
    return `${ola}${SLA_UNIT_TYPE_TEXT_MAP[olaUnit]}`;
  }
  if (!ola) {
    return null;
  } else {
    return (
      <TicketSlaText
        delay={difference}
        effectTime={null}
        endTime={null}
        taskSla={ola}
        unit={olaUnit}
        shouldLimitShow
      />
    );
  }
}

function olaText(isNormal, ola, difference, text, olaUnit) {
  if (!ola) {
    return '--';
  }
  let olaMin = ola;
  if (olaUnit === SlaUnitTypeKeyMap.Hour) {
    olaMin = ola * 60;
  }
  if (olaUnit === SlaUnitTypeKeyMap.Day) {
    olaMin = ola * 60 * 24;
  }
  if (isNormal && olaMin - difference >= 0) {
    return <StatusText status={STATUS_MAP.NORMAL}>{CHANGE_STEP_STATUS_TEXT_MAP.NORMAL}</StatusText>;
  }

  if (olaMin - difference < 0) {
    return <StatusText status={STATUS_MAP.ALARM}>异常</StatusText>;
  }
  return (
    <StatusText status={getStatusText(text)}>
      {CHANGE_STEP_STATUS_TEXT_MAP[CHANGE_STEP_STATUS_KEY_MAP[text]]}
    </StatusText>
  );
}
const stepDescAndstepType = [
  {
    title: '步骤内容',
    dataIndex: 'stepDesc',
    render: text => (
      <Typography.Text style={{ maxWidth: 504 }} ellipsis={{ tooltip: text }}>
        {text}
      </Typography.Text>
    ),
  },
  {
    title: '步骤类型',
    dataIndex: 'stepType',
    render: text => <span>{CHANGE_STEP_TYPE_TEXT_MAP[CHANGE_STEP_TYPE_KEY_MAP[text]]}</span>,
  },
];
const deviceColumn = (difference, changeStatus, isNormal) => [
  ...stepDescAndstepType,
  {
    title: '操作目标',
    dataIndex: 'opObjectName',
  },
  {
    title: '执行方式',
    dataIndex: 'exeWay',
    render: text => (
      <span>{CHANGE_EXECUTE_METHOD_TEXT_MAP[CHANGE_EXECUTE_METHOD_KEY_MAP[text]]}</span>
    ),
  },
  {
    title: '验证测点',
    dataIndex: 'pointName',
  },
  {
    title: '验证状态',
    dataIndex: 'pointValueText',
  },
  {
    title: '验证方式',
    dataIndex: 'identifyWay',

    render: text => <span>{CHANGE_CHECK_METHOD_TEXT_MAP[CHANGE_CHECK_METHOD_KEY_MAP[text]]}</span>,
  },
  {
    title: '操作动作',
    dataIndex: 'operate',
  },
  {
    title: '开始时间',
    dataIndex: 'startTime',
    dataType: 'datetime',
  },
  {
    title: '结束时间',
    dataIndex: 'endTime',
    dataType: 'datetime',
  },
  {
    title: 'OLA',
    dataIndex: 'ola',
    render: (ola, { stepStatus, olaUnit }) =>
      OLA({ ola: ola, difference, changeStatus, stepStatus, olaUnit }),
  },
  {
    title: '操作人',
    dataIndex: 'operatorName',
    render: (text, { operatorId }) => <UserLink userName={text} userId={operatorId} />,
  },
  {
    title: '步骤状态',
    dataIndex: 'stepStatus',
    fixed: 'right',
    render: (text, { ola, olaUnit }) => olaText(isNormal, ola, difference, text, olaUnit),
  },
];

const otherColumn = (difference, changeStatus, isNormal) => [
  ...stepDescAndstepType,
  {
    title: '操作目标',
    dataIndex: 'opObjectName',
  },
  {
    title: '操作方法',
    dataIndex: 'operate',
    // width: 90,
    // render: riskLevel => <span>{CHANGE_RISK_LEVEL_TEXT_MAP[riskLevel]}</span>,
  },
  {
    title: '验证方法',
    dataIndex: 'identifyWay',
    render: text => CHANGE_CHECK_METHOD_TEXT_MAP[text],
  },

  {
    title: '开始时间',
    dataIndex: 'startTime',
    dataType: 'datetime',
  },
  {
    title: '结束时间',
    dataIndex: 'endTime',
    dataType: 'datetime',
  },
  {
    title: 'OLA',
    dataIndex: 'ola',
    render: (ola, { stepStatus, olaUnit }) =>
      OLA({ ola: ola, difference, changeStatus, stepStatus, olaUnit }),
  },
  {
    title: '操作人',
    dataIndex: 'operatorName',
    render: (text, { operatorId }) => <UserLink userName={text} userId={operatorId} />,
  },
  {
    title: '步骤状态',
    dataIndex: 'stepStatus',
    fixed: 'right',
    render: (text, { ola, olaUnit }) => olaText(isNormal, ola, difference, text, olaUnit),
  },
];

const watchColumn = (difference, changeStatus, isNormal) => [
  ...stepDescAndstepType,
  {
    title: '开始时间',
    dataIndex: 'startTime',
    dataType: 'datetime',
  },
  {
    title: '结束时间',
    dataIndex: 'endTime',
    dataType: 'datetime',
  },
  {
    title: 'OLA',
    dataIndex: 'ola',
    render: (ola, { stepStatus, olaUnit }) =>
      OLA({ ola: ola, difference, changeStatus, stepStatus, olaUnit }),
  },
  {
    title: '操作人',
    dataIndex: 'operatorName',
    render: (text, { operatorId }) => <UserLink userName={text} userId={operatorId} />,
  },
  {
    title: '步骤状态',
    dataIndex: 'stepStatus',
    fixed: 'right',
    render: (text, { ola, olaUnit }) => olaText(isNormal, ola, difference, text, olaUnit),
  },
];

const runColumn = (difference, changeStatus, isNormal) => [
  ...stepDescAndstepType,
  {
    title: '操作目标',
    dataIndex: 'opObjectName',
  },
  {
    title: '开始时间',
    dataIndex: 'startTime',
    dataType: 'datetime',
  },
  {
    title: '结束时间',
    dataIndex: 'endTime',
    dataType: 'datetime',
  },
  {
    title: 'OLA',
    dataIndex: 'ola',
    render: (ola, { stepStatus, olaUnit }) =>
      OLA({ ola: ola, difference, changeStatus, stepStatus, olaUnit }),
  },
  {
    title: '操作人',
    dataIndex: 'operatorName',
    render: (text, { operatorId }) => <UserLink userName={text} userId={operatorId} />,
  },
  {
    title: '步骤状态',
    dataIndex: 'stepStatus',
    fixed: 'right',
    render: (text, { ola, olaUnit }) => olaText(isNormal, ola, difference, text, olaUnit),
  },
];
const customerColumn = (difference, changeStatus, isNormal) => [
  ...stepDescAndstepType,
  {
    title: '开始时间',
    dataIndex: 'startTime',
    dataType: 'datetime',
  },
  {
    title: '结束时间',
    dataIndex: 'endTime',
    dataType: 'datetime',
  },
  {
    title: 'OLA',
    dataIndex: 'ola',
    render: (ola, { stepStatus, olaUnit }) =>
      OLA({ ola: ola, difference, changeStatus, stepStatus, olaUnit }),
  },
  {
    title: '操作人',
    dataIndex: 'operatorName',
    render: (text, { operatorId }) => <UserLink userName={text} userId={operatorId} />,
  },
  {
    title: '步骤状态',
    dataIndex: 'stepStatus',
    fixed: 'right',
    render: (text, { ola, olaUnit }) => olaText(isNormal, ola, difference, text, olaUnit),
  },
];

const mapStateToProps = ({
  change: {
    ticketDetail: {
      step: { stepList, checkItemInfoMaps, opItemInfoMaps, stepOrder },
      changeInfo: {
        step1: { changeStatus },
      },
    },
  },
  common: { deviceCategory },
}) => ({
  stepList,
  stepOrder,
  checkItemInfoMaps,
  opItemInfoMaps,
  metaCategoryEntities: deviceCategory ? deviceCategory.normalizedList : {},
  changeStatus,
});

export default connect(mapStateToProps, null)(StepTable);
