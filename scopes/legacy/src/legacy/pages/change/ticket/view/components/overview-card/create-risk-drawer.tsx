import omit from 'lodash.omit';
import moment from 'moment';
import React from 'react';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { useCreateRiskRegisterByChange } from '@manyun/ticket.gql.client.tickets';
import { RiskRegister } from '@manyun/ticket.model.risk-register';
import type { RiskRegisterJSON } from '@manyun/ticket.model.risk-register';
import { RiskRegisterForm } from '@manyun/ticket.page.risk-register-mutator';

export type CreateRiskDrawerProps = {
  changeOrderId: string;
  visibleType: 'event' | 'risk' | 'change' | null;
  changeTitle: string;
  onSuccess?: () => void;
  setVisibleType: () => void;
};
type RiskObject = {
  blockTag?: string | null;
  deviceTag?: string | null;
  deviceType?: string | null;
  idcTag?: string | null;
  key?: string | null;
  label: string;
  objectGuid?: string | null;
  objectName: string;
  roomTag?: string | null;
  value?: string | null;
};
export function CreateRiskDrawer({
  changeOrderId,
  visibleType,
  changeTitle,
  onSuccess,
  setVisibleType,
}: CreateRiskDrawerProps) {
  const [form] = Form.useForm<
    Omit<RiskRegisterJSON, 'riskObjects'> & {
      commit: boolean;
      riskObjects?: RiskObject[] | null | string;
    }
  >();
  const [createRiskRegister, { loading }] = useCreateRiskRegisterByChange();

  return (
    <>
      <Drawer
        forceRender
        title="创建风险单"
        size="large"
        placement="right"
        open={visibleType === 'risk'}
        width={1300}
        extra={
          <Space>
            <Button
              onClick={() => {
                form.resetFields();
                setVisibleType();
              }}
            >
              取消
            </Button>
            <Button
              type="primary"
              loading={loading}
              onClick={() => {
                form.validateFields().then(async formValues => {
                  const backendSaleQuote = {
                    ...RiskRegister.fromJSON({
                      ...formValues,
                      riskObjects:
                        typeof formValues.riskObjects === 'string'
                          ? [{ label: formValues.riskObjects, objectName: formValues.riskObjects }]
                          : formValues.riskObjects,
                    }).toApiObject(),
                  };
                  const { data } = await createRiskRegister({
                    variables: {
                      query: {
                        ...omit(backendSaleQuote, 'createUserId'),
                        changeOrderId,
                        fileInfoList: formValues?.fileInfoList?.map(obj =>
                          McUploadFile.fromJSON(obj)
                        ),
                        planCompleteTime: moment(formValues.planCompleteTime).valueOf(),
                        idcTag: backendSaleQuote.idcTag!,
                      },
                    },
                  });

                  if (!data?.createRiskRegisterByChange?.success) {
                    message.error(data?.createRiskRegisterByChange?.message);
                    return;
                  }
                  onSuccess && onSuccess();
                  message.success('提交成功');
                  setVisibleType();
                });
              }}
            >
              提交
            </Button>
          </Space>
        }
        onClose={() => setVisibleType()}
      >
        <Alert message="关联变更" description={`${changeOrderId} - ${changeTitle}`} type="info" />

        <RiskRegisterForm form={form} />
      </Drawer>
    </>
  );
}
