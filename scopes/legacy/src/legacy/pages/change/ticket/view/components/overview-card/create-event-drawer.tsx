import React from 'react';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';
import { useCreateEventByChange } from '@manyun/ticket.gql.client.tickets';
import EventMutator, {
  formatCauseDevicesParams,
  generateEventCategory,
} from '@manyun/ticket.ui.event-mutator';
import type { FormValues } from '@manyun/ticket.ui.event-mutator';
import { filterEventInfluencesTreeData } from '@manyun/ticket.util.filter-event-influences-tree-data';

export type CreateEventDrawerProps = {
  changeOrderId: string;
  changeTitle: string;
  setVisibleType: () => void;
  visibleType: 'event' | 'risk' | 'change' | null;
  onSuccess?: () => void;
};

export function CreateEventDrawer({
  changeOrderId,
  visibleType,
  changeTitle,
  setVisibleType,
  onSuccess,
}: CreateEventDrawerProps) {
  const [form] = Form.useForm<FormValues>();
  const [configUtil] = useConfigUtil();
  const {
    events: { features },
  } = configUtil.getScopeCommonConfigs('ticket');
  const [createEvent, { loading }] = useCreateEventByChange({
    onCompleted: data => {
      if (!data.createEventByChange?.success) {
        message.error(data.createEventByChange?.message);
        return;
      }
      setVisibleType();
      onSuccess && onSuccess();
      message.success('提交成功');
    },
  });

  return (
    <Drawer
      forceRender
      title="创建事件单"
      size="large"
      placement="right"
      open={visibleType === 'event'}
      width={1280}
      extra={
        <Space>
          <Button
            onClick={() => {
              form.resetFields();
              setVisibleType();
            }}
          >
            取消
          </Button>
          <Button
            type="primary"
            loading={loading}
            onClick={() => {
              form.validateFields().then(async formValues => {
                const {
                  location,
                  isChangeAlarm,
                  changeCode,
                  incidentType,
                  occurTime,
                  detectTime,
                  causeDevice,
                  eventLevel,
                  eventSource,
                  eventOwner,
                  files,
                  eventDesc,
                  eventTitle,
                  infoType,
                  isFalseAlarm,
                  northSync,
                  eventInfluence,
                  majorCode,
                  locationList,
                  blockGuidList,
                  ...rest
                } = formValues;
                const { idc } = getSpaceGuidMap(blockGuidList?.[0] ?? '');
                const svcQuery = {
                  ...rest,
                  files: files?.map((obj: McUploadFile) => McUploadFile.fromJSON(obj)),
                  // eventOwnerName: eventOwner!.label,
                  // eventOwnerId: eventOwner!.value,
                  northSync: false,
                  blockGuidList,
                  isFalseAlarm: features.isFalseAlarm === 'required' ? isFalseAlarm : 0,
                  eventSource: eventSource!.value,
                  eventSourceName: eventSource!.label,
                  eventLevel: eventLevel?.value,
                  eventLevelName: eventLevel?.label,
                  // blockTag: `${idc}.${block}`,
                  idcTag: idc!,
                  causeDevice: causeDevice ? formatCauseDevicesParams(causeDevice) : null,
                  occurTime: occurTime ? occurTime.valueOf() : null,
                  detectTime: detectTime ? detectTime.valueOf() : null,
                  topCategory: generateEventCategory(incidentType).topCategory!,
                  topCategoryName: generateEventCategory(incidentType).topCategoryName!,
                  secondCategory: generateEventCategory(incidentType).secondCategory,
                  secondCategoryName: generateEventCategory(incidentType).secondCategoryName,
                  changeCode: changeOrderId,
                  isChangeAlarm: true,
                  eventDesc: eventDesc!,
                  eventTitle: eventTitle!,
                  infoType: infoType,
                  eventMajorCode: majorCode,
                  eventLocationList: locationList?.map(item => ({
                    locationType: item.locationType,
                    subType: item.subType,
                    guid: item.guid,
                  })),
                  eventInfluence: eventInfluence
                    ? {
                        gridInfluence: eventInfluence.cabinetInfluence,
                        influenceScope: eventInfluence.influenceScope
                          ? filterEventInfluencesTreeData(eventInfluence.influenceScope)?.map(
                              item => ({
                                influenceType: item.type,
                                influenceGuid: item.key,
                              })
                            )
                          : null,
                      }
                    : undefined,
                };
                createEvent({
                  variables: {
                    query: { ...svcQuery, changeOrderId },
                  },
                });
              });
            }}
          >
            提交
          </Button>
        </Space>
      }
      onClose={() => setVisibleType()}
    >
      <Alert message="关联变更" description={`${changeOrderId} - ${changeTitle}`} type="info" />

      <EventMutator
        showFooter={false}
        externalForm={form}
        mode="create"
        // 产品要求从变更创建事件时，“事件来源”是写死的，并且禁止二次修改
        formInitialValues={{ eventSource: { label: '变更导致', value: 'CHANGE_CAUSE' } }}
        unusedFormItems={['isChangeAlarm', 'liableDept', 'liablePerson', 'eventOwner']}
        disabledFormItems={['eventSource']}
      />
    </Drawer>
  );
}
