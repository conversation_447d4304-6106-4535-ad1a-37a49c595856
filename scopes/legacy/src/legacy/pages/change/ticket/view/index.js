import Form from '@ant-design/compatible/es/form';
import ExclamationCircleFilled from '@ant-design/icons/es/icons/ExclamationCircleFilled';
import React, { useEffect, useRef, useState } from 'react';
import { connect } from 'react-redux';
import { Link, useHistory, useLocation } from 'react-router-dom';
import { useLatest } from 'react-use';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { DownloadPdfButton } from '@manyun/base-ui.ui.download-pdf-button';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Typography } from '@manyun/base-ui.ui.typography';
import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import {
  useFinishChangeOnline,
  useLazyChangeOnline,
  useStartExecuteChangeOnlineData,
} from '@manyun/ticket.gql.client.tickets';
import { CHANGE_EXE_WAY_MAP, ChangeTabs, ChangeTicketState } from '@manyun/ticket.model.change';
import { ChangeContext, initTabs } from '@manyun/ticket.page.change-offline';
import { changeOnlineToJson } from '@manyun/ticket.page.change-online-mutator';
import { PostponeChange, RescheduleChange } from '@manyun/ticket.page.change-onlines';
import {
  generateChangeOfflineEditLocation,
  generateChangeOfflineEditRoutePath,
  generateChangeOnlineDetailLocation,
  generateChangeTicketEdit,
} from '@manyun/ticket.route.ticket-routes';
import { completeOfflineChange } from '@manyun/ticket.service.complete-offline-change';

import { FooterToolBar } from '@manyun/dc-brain.legacy.components';
import {
  changeActions,
  getTicketDetailInfo,
  stopChangeActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/changeActions';

import { CHANGE_TICKET_STATUS_STEP } from '../../constants';
import { CancelButton } from './components/overview-card/cancel-button';
// import PostponeChange from './components/overview-card/postpone-change';
// import RescheduleChange from './components/overview-card/reschedule-change';
import { StopButton } from './components/overview-card/stop-button';
import StepCard, { NewStepDetail } from './components/step-card';
import TicketViewTabs from './components/tabs';

export function TicketDetail({
  changeStatus,
  hasPermission,
  changeOrderId,
  exeWay,
  tabCurrent,
  creatorId,
  operatorId,
  match,
  getTicketDetailInfo,
  stopChangeActionCreator,
  setTabCurrent,
  title,
  changeVersion,
  ...resp
}) {
  const [visible, setVisible] = useState(false);
  const [changeStopReason, setChangeStopReason] = useState('');
  const [stepCurrent, setStepCurrent] = useState('');
  const [isExportPDF, setIsExportPDF] = useState(false);
  const [exportPdfLoading, setExportPdfLoading] = useState(false);
  const [exportPDFTabs, setExportPDFTabs] = useState(initTabs);
  const [changeInfo, setChangeInfo] = useState({});
  const [stepCodes, setStepCodes] = useState([]);
  const [stepMaps, setStepMaps] = useState({});
  const [, { checkUserId }] = useAuthorized();
  const offLine = isOffLine(exeWay);
  const history = useHistory();
  const { search } = useLocation();
  const { currentTab } = getLocationSearchMap(search);
  const [configUtil] = useConfigUtil();

  const [getNewDetail, { data, loading }] = useLazyChangeOnline({
    onCompleted(data) {
      if (data?.changeOnline?.data) {
        const changeInfodata = changeOnlineToJson(data.changeOnline.data);
        setChangeInfo({
          ...changeInfodata.changeInfo,
          changeSourceInfoList: data.changeOnline.data.extJson.changeSourceInfoList,
        });
        setStepCodes(changeInfodata.stepCodes);
        setStepMaps(changeInfodata.stepMaps);
      }
    },
  });
  const [finished, { loading: finishLoading }] = useFinishChangeOnline({
    onCompleted(data) {
      if (!data?.finishChangeOnline?.success) {
        message.error(data?.finishChangeOnline?.message);
        return;
      }
      message.success('变更已完成');
      refetchNew();
    },
  });

  const [startChange, { loading: startLoading }] = useStartExecuteChangeOnlineData({
    onCompleted(data) {
      if (!data?.startExecuteChangeOnline.success) {
        message.error(data?.startExecuteChangeOnline?.message);
        return;
      }
      refetchNew();
    },
  });
  const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket');

  const changesTicketCreateChangeOffline =
    ticketScopeCommonConfigs.changes.features.createChangeOffline;
  const showNewDetail = ticketScopeCommonConfigs.changes.features.showNewDetail;

  // const newChangeDetail = data?.changeOnline?.data ?? {};
  useEffect(() => {
    const _changeSatus = showNewDetail ? data?.changeOnline?.data?.changeStatus : changeStatus;
    _changeSatus && setStepCurrent(CHANGE_TICKET_STATUS_STEP[_changeSatus]);
  }, [showNewDetail, data?.changeOnline?.data, changeStatus]);

  const exportPdfInterval = useRef(null);
  const latestExportPDFTabs = useLatest(exportPDFTabs);

  useEffect(() => {
    if (showNewDetail) {
      if (match.path.startsWith('/page/change/ticket')) {
        history.push(generateChangeOnlineDetailLocation({ id: match.params.id, currentTab }));
        return;
      }
      getNewDetail({ variables: { changeOrderId: match.params.id } });
      return;
    }
    getTicketDetailInfo({ id: match.params.id, mode: 'ticketDetail' });

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [match, getTicketDetailInfo, getNewDetail, showNewDetail]);

  useEffect(() => {
    if (changesTicketCreateChangeOffline === 'full') {
      setExportPDFTabs([
        ...initTabs,
        {
          key: ChangeTabs.Relate,
          isVolid: false,
          isRenderd: false,
          title: '关联事项',
        },
        {
          key: ChangeTabs.Alarm,
          isVolid: false,
          isRenderd: false,
          title: '告警看板',
        },
      ]);
    }
  }, [changesTicketCreateChangeOffline]);

  const onStopChange = () => {
    if (!changeStopReason) {
      return message.error('请填写变更终止原因！');
    }
    stopChangeActionCreator({ changeOrderId, stopReason: changeStopReason });
  };
  const refetchNew = () => {
    getNewDetail({ variables: { changeOrderId: match.params.id } });
  };
  const stopChange = () => {
    if (changeStopReason) {
      onStopChange();
      setVisible(false);
    } else {
      message.error('请填写变更终止原因！');
    }
  };

  const validationProps = () => {
    const validationProps = {};
    if (!changeStopReason) {
      validationProps.validateStatus = 'error';
      validationProps.help = '变更终止原因为必填';
      validationProps.required = true;
    }
    if (changeStopReason.length > 100) {
      validationProps.validateStatus = 'error';
      validationProps.help = '变更终止原因最多不可以超过100个字符！';
      validationProps.required = true;
    }
    return validationProps;
  };
  const isOwnerUser = showNewDetail
    ? changeInfo?.changeResponsibleUserList?.some(item => checkUserId(item.value))
    : false;

  return (
    <ChangeContext.Provider
      value={{
        isExportPDF: isExportPDF,
        exportPDFTabs: exportPDFTabs,
        setIsExportPDF: setIsExportPDF,
        setExportPDFTabs: setExportPDFTabs,
      }}
    >
      <Spin spinning={exportPdfLoading || loading}>
        {changeStatus && offLine && (
          <DownloadPdfButton
            key="download"
            compact={false}
            style={{ position: 'fixed', right: 8, top: 52 }}
            disabled={isExportPDF}
            pdfName={`${changeOrderId} ${title?.value}`}
            exportElement={document.getElementById('root')}
            beforeDownload={() => {
              setIsExportPDF(true);
              setExportPdfLoading(true);
              return new Promise(resolve => {
                exportPdfInterval.current = setInterval(() => {
                  if (latestExportPDFTabs.current.some(item => !item.isRenderd)) {
                    return;
                  }
                  setExportPdfLoading(false);
                  setTimeout(() => {
                    resolve();
                  }, 1000);
                }, 1000);
              });
            }}
            onFinish={() => {
              setIsExportPDF(false);
              setExportPDFTabs(initTabs);
              exportPdfInterval.current && clearInterval(exportPdfInterval.current);
            }}
          />
        )}
        <Space style={{ height: '100%', width: '100%', marginBottom: 48 }} direction="vertical">
          {showNewDetail ? (
            <NewStepDetail
              {...changeInfo}
              isOwnerUser={isOwnerUser}
              getTicketDetailInfo={() => refetchNew()}
            />
          ) : (
            <StepCard isOffLine={offLine} />
          )}

          <TicketViewTabs
            {...resp}
            match={match}
            stepCurrent={stepCurrent}
            isOffLine={offLine}
            changeStatus={changeStatus}
            setTabCurrent={setTabCurrent}
            tabCurrent={tabCurrent}
            changeInfo={changeInfo}
            newStepCodes={stepCodes}
            newStepMaps={stepMaps}
            isOwnerUser={isOwnerUser}
            currentTab={currentTab}
            refetchDetail={refetchNew}
          />
          {changeStatus === ChangeTicketState.Changing &&
            offLine &&
            tabCurrent !== 'summaryRecords' &&
            !isExportPDF && (
              <CompleteOffLineBtn
                creatorId={creatorId}
                changeOrderId={changeOrderId}
                getTicketDetailInfo={getTicketDetailInfo}
                operatorId={operatorId}
              />
            )}
          {stepCurrent === 2 && showNewDetail && isOwnerUser ? (
            <FooterToolBar>
              <Space>
                <Button
                  type="primary"
                  loading={startLoading}
                  onClick={() => {
                    startChange({ variables: { changeOrderId: changeInfo.changeOrderId } });
                  }}
                >
                  开始变更
                </Button>

                <RescheduleChange
                  changeOrderId={changeInfo.changeOrderId}
                  planEndTime={changeInfo.planEndTime}
                  planStartTime={changeInfo.planStartTime}
                  onSuccess={() => {
                    refetchNew();
                  }}
                />

                <CancelButton
                  changeOrderId={changeInfo.changeOrderId}
                  onSuccess={() => {
                    refetchNew();
                  }}
                />
              </Space>
            </FooterToolBar>
          ) : null}
          {stepCurrent === 3 && showNewDetail && isOwnerUser ? (
            <FooterToolBar>
              <Space>
                <Button
                  disabled={stepCodes.some(
                    item =>
                      stepMaps[item]?.stepStatus === 'NOT_START' ||
                      stepMaps[item]?.stepStatus === 'PROCESSING'
                  )}
                  loading={finishLoading}
                  type="primary"
                  onClick={async () => {
                    finished({
                      variables: {
                        query: {
                          changeOrderId: changeInfo.changeOrderId,
                        },
                      },
                    });
                  }}
                >
                  完成变更
                </Button>

                {changeInfo?.changeApprovalInfoList?.length &&
                changeInfo.changeApprovalInfoList[0].instStatus === 'APPROVING' ? null : (
                  <PostponeChange
                    changeOrderId={changeInfo.changeOrderId}
                    planEndTime={changeInfo.planEndTime}
                    onSuccess={() => {
                      refetchNew();
                    }}
                  />
                )}

                {stepCodes.some(
                  item =>
                    stepMaps[item]?.stepStatus === 'NOT_START' ||
                    stepMaps[item]?.stepStatus === 'PROCESSING'
                ) && (
                  <StopButton
                    changeOrderId={changeInfo.changeOrderId}
                    onSuccess={() => {
                      refetchNew();
                    }}
                  />
                )}
              </Space>
            </FooterToolBar>
          ) : null}
          {stepCurrent === 4 && tabCurrent !== 'summaryRecords' && showNewDetail && isOwnerUser ? (
            <FooterToolBar>
              <Button type="primary" onClick={() => setTabCurrent('summaryRecords')}>
                变更总结
              </Button>
            </FooterToolBar>
          ) : null}
          {stepCurrent === 4 && hasPermission && tabCurrent !== 'summaryRecords' && !isExportPDF ? (
            <FooterToolBar>
              <Button type="primary" onClick={() => setTabCurrent('summaryRecords')}>
                变更总结
              </Button>
            </FooterToolBar>
          ) : null}
          {stepCurrent === 3 && hasPermission && !offLine && !isExportPDF ? (
            <FooterToolBar>
              <Button danger onClick={() => setVisible(true)}>
                变更终止
              </Button>
            </FooterToolBar>
          ) : null}
          {changeStatus === ChangeTicketState.Draft &&
            hasPermission &&
            !offLine &&
            !isExportPDF && (
              <FooterToolBar>
                <Link
                  to={generateChangeTicketEdit({
                    id: changeOrderId,
                  })}
                >
                  <Button type="primary">编辑</Button>
                </Link>
              </FooterToolBar>
            )}
          {changeStatus === ChangeTicketState.Draft && offLine && !isExportPDF && (
            <OffLineEditBtn
              creatorId={creatorId}
              changeOrderId={changeOrderId}
              operatorId={operatorId}
              changeVersion={changeVersion}
            />
          )}
          <Modal
            width="400px"
            title="是否终止变更"
            open={visible}
            closable={false}
            onOk={stopChange}
            onCancel={() => setVisible(false)}
          >
            <Space style={{ width: '100%' }} direction="vertical">
              <Space style={{ justifyContent: 'center', alignItems: 'center', display: 'flex' }}>
                <ExclamationCircleFilled
                  style={{ color: `var(--${prefixCls}-warning-color)`, fontSize: 16 }}
                />
                <Typography.Text style={{ fontSize: 16, fontWeight: 500 }}>
                  确认后将不可更改
                </Typography.Text>
              </Space>
              <Form>
                <Form.Item {...validationProps()}>
                  <Input.TextArea
                    placeholder="请填写变更终止原因"
                    onChange={e => setChangeStopReason(e.target.value)}
                  />
                </Form.Item>
              </Form>
            </Space>
          </Modal>
        </Space>
      </Spin>
    </ChangeContext.Provider>
  );
}
const mapStateToProps = ({
  change: {
    ticketDetail: {
      tabCurrent,
      changeInfo: {
        step1: {
          changeStatus,
          changeOrderExtJson,
          changeOrderId,
          stopReason,
          workFlowId,
          hasPermission,
          exeWay,
          creatorId,
          operatorId,
          title,
          changeVersion,
        },
      },
    },
  },
}) => ({
  changeStatus,
  statusInfoList: changeOrderExtJson && changeOrderExtJson.statusInfoList,
  changeOrderId,
  stopReason,
  workFlowId,
  hasPermission,
  exeWay,
  tabCurrent,
  creatorId,
  operatorId,
  title,
  changeVersion,
});

const mapDispatchToProps = {
  getTicketDetailInfo: getTicketDetailInfo,
  stopChangeActionCreator: stopChangeActionCreator,
  setTabCurrent: changeActions.setTabCurrent,
};

export default connect(mapStateToProps, mapDispatchToProps)(TicketDetail);

const isOffLine = exeWay => exeWay === CHANGE_EXE_WAY_MAP.OffLine;

function OffLineEditBtn({ creatorId, changeOrderId, operatorId, changeVersion }) {
  const [authorized] = useAuthorized({ checkByUserId: creatorId });
  const [operatorAuthorized] = useAuthorized({ checkByUserId: operatorId });

  return authorized || operatorAuthorized ? (
    <FooterToolBar>
      <Link
        to={
          changeVersion === 2
            ? generateChangeOfflineEditLocation({
                id: changeOrderId,
              })
            : generateChangeOfflineEditRoutePath({
                id: changeOrderId,
              })
        }
      >
        <Button type="primary">编辑</Button>
      </Link>
    </FooterToolBar>
  ) : null;
}

function CompleteOffLineBtn({ creatorId, changeOrderId, getTicketDetailInfo, operatorId }) {
  const [authorized] = useAuthorized({ checkByUserId: creatorId });
  const [operatorAuthorized] = useAuthorized({ checkByUserId: operatorId });

  return authorized || operatorAuthorized ? (
    <FooterToolBar>
      <Button
        type="primary"
        onClick={async () => {
          const { error } = await completeOfflineChange({ changeOrderId });
          if (error) {
            message.error(error.message);
            return;
          }
          getTicketDetailInfo({ id: changeOrderId, mode: 'ticketDetail' });
          message.success('变更已完成');
        }}
      >
        完成变更
      </Button>
    </FooterToolBar>
  ) : null;
}
