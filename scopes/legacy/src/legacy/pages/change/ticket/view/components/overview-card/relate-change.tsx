/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-12-20
 *
 * @packageDocumentation
 */
import { MinusCircleOutlined } from '@ant-design/icons';
import PlusOutlined from '@ant-design/icons/es/icons/PlusOutlined';
import React, { useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { useChangeOnlineRelateTicket } from '@manyun/ticket.gql.client.tickets';

import { ChangeSourceSelect } from './change-source';

export type ChangeSourceTextProps = {
  idcTag: string;
  blockGuid?: string | null;
  changeOrderId: string;
};

export function RelateChangeSource({
  idcTag,
  blockGuid,
  changeOrderId,
}: ChangeSourceTextProps): JSX.Element {
  const [visible, setVisible] = useState(false);
  const [form] = Form.useForm();

  const [relate, { loading }] = useChangeOnlineRelateTicket({
    onCompleted(data) {
      if (!data.changeOnlineRelateTicket.success) {
        message.error(data.changeOnlineRelateTicket.message);
        return;
      }
      setVisible(false);
      message.success('关联成功，请在「关联事项」中查看');
      // onSuccess();
    },
  });
  const [configUtil] = useConfigUtil();
  const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket');

  const featureIsEventConfigWithProcessEngine =
    ticketScopeCommonConfigs.events.features.isEventConfigWithProcessEngine;
  const featureIsEventConfigWithProcessEngineRequired =
    featureIsEventConfigWithProcessEngine === 'required';

  return (
    <>
      <Button onClick={() => setVisible(true)}>关联已有工单</Button>
      <Modal
        title="关联已有工单"
        open={visible}
        okText="确定"
        width={640}
        afterClose={() => form.resetFields()}
        okButtonProps={{ disabled: loading }}
        onCancel={() => {
          setVisible(false);
        }}
        onOk={() => {
          form.validateFields().then(values => {
            relate({
              variables: {
                query: {
                  changeOrderId,
                  changeRelateInfoList: values.changeSourceInfoList,
                },
              },
            });
          });
        }}
      >
        <Form
          form={form}
          labelCol={{ flex: ' 0 0 110px' }}
          initialValues={{
            changeSourceInfoList: [{ relateType: undefined, relateNo: undefined }],
          }}
        >
          <Form.List name="changeSourceInfoList">
            {(fields, { add, remove }, { errors }) => (
              <>
                {fields.map(({ key, name, ...field }, index) => (
                  <Form.Item
                    key={`${key}.${name}`}
                    label={index === 0 ? '关联已有工单' : ' '}
                    colon={index === 0}
                  >
                    <div style={{ width: '100%', display: 'flex' }}>
                      <Form.Item
                        {...field}
                        style={{ display: 'flex', flex: 1 }}
                        rules={[
                          {
                            validator: (_, changeSourceInfoList) => {
                              if (changeSourceInfoList && !changeSourceInfoList.relateType) {
                                return Promise.reject('关联工单必选');
                              }
                              if (
                                changeSourceInfoList &&
                                changeSourceInfoList.relateType &&
                                !changeSourceInfoList.relateNo
                              ) {
                                return Promise.reject('工单ID必选');
                              }
                              return Promise.resolve();
                            },
                          },
                        ]}
                        noStyle
                        name={[name]}
                      >
                        <ChangeSourceSelect
                          idcTag={idcTag}
                          blockGuid={blockGuid}
                          shouldShowEventNumber={featureIsEventConfigWithProcessEngineRequired}
                        />
                      </Form.Item>

                      {fields.length > 1 ? (
                        <MinusCircleOutlined
                          style={{ marginLeft: 8 }}
                          onClick={() => remove(name)}
                        />
                      ) : null}
                    </div>
                  </Form.Item>
                ))}
                <Form.Item label=" " colon={false} labelCol={{ flex: ' 0 0 110px' }}>
                  {/* {changeSourceInfoList?.length < 10 && ( */}
                  <Button
                    style={{ width: '100%' }}
                    type="dashed"
                    disabled={fields.length >= 20}
                    icon={<PlusOutlined />}
                    onClick={() => {
                      add();
                    }}
                  >
                    添加关联工单
                  </Button>
                  {/* )} */}
                  <Form.ErrorList errors={errors} />
                </Form.Item>
              </>
            )}
          </Form.List>
        </Form>
      </Modal>
    </>
  );
}
