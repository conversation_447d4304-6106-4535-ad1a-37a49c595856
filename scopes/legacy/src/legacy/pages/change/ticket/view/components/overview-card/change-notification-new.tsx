import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Divider } from '@manyun/base-ui.ui.divider';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Popover } from '@manyun/base-ui.ui.popover';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { BackendNotifyEvent } from '@manyun/notification-hub.model.notice-config';
import type { Webhook } from '@manyun/notification-hub.service.fetch-webhooks';
import { fetchWebhooks } from '@manyun/notification-hub.service.fetch-webhooks';
import type { Metadata } from '@manyun/resource-hub.gql.client.resources';
import { useMetadata } from '@manyun/resource-hub.gql.client.resources';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import {
  useCreateChangeNotification,
  useLazyEventCurrentOwner,
} from '@manyun/ticket.gql.client.tickets';
import type { FetchEventCurrentOwnerData } from '@manyun/ticket.gql.client.tickets';
import type { CHANGE_RISK_LEVEL_KEY_MAP, ChangeTicketState } from '@manyun/ticket.model.change';
import {
  CHANGE_EXE_WAY_MAP,
  CHANGE_ONLINE_TICKET_STATUS_TEXT_MAP,
  CHANGE_RISK_LEVEL_TEXT_MAP,
  CHANGE_TICKET_STATUS_TEXT_MAP,
} from '@manyun/ticket.model.change';
import { BackendEventUpgradeChannel, EventUpgradeChannelMap } from '@manyun/ticket.model.event';
import { NotificationObject } from '@manyun/ticket.page.event';
import type { NotificationSelectObject } from '@manyun/ticket.page.event';
import { fetchNotifyEventList } from '@manyun/ticket.service.fetch-notify-event-list';

import { influenceService } from '@manyun/dc-brain.legacy.services';

export type ChangeNotificationNewProps = {
  id: number;
  notificationInfo: NotificationInfo;
  showNewDetail?: boolean;
  getNotificationList: () => void;
};

export type NotificationInfo = {
  title: string;
  blockGuid: string;
  riskLevel: CHANGE_RISK_LEVEL_KEY_MAP;
  planStartTime: number;
  planEndTime: number;
  reason: string;
  changeStatus: ChangeTicketState;
  causeDevices?: string[];
  idcTag: string;
  id: string;
  changeExeWay?: CHANGE_EXE_WAY_MAP;
  changeInfluence?: string;
  creatorName?: string;
  changeVersion?: number;
  changeTypeName?: string | null;
  influenceArea?: string | null;
  respPersonName?: string | null;
  operatorName?: string | null;
  relateCustomer?: string | null;
  changeCategory?: string | null;
  changeType?: string | null;
  changeResponsibleUserInfoList?: { label: string; value: string }[];
};

export function ChangeNotificationNew({
  id,
  notificationInfo,
  showNewDetail,
  getNotificationList,
}: ChangeNotificationNewProps) {
  const [visible, setVisible] = useState<boolean>(false);
  const [influenceSurface, setInfluenceSurface] = useState<string>('');
  const [manuallyAddRoleList, setManuallyAddRoleList] = useState<NotificationSelectObject[]>([]);
  const [reportChannels, setReportChannels] = useState<BackendEventUpgradeChannel[]>([]);
  const [reportObjects, setReportObjects] = useState<NotificationSelectObject[]>([]);
  const metaDataRes = useMetadata({
    variables: { type: MetaType.CHANGE_LEVEL },
    fetchPolicy: 'cache-and-network',
  });
  const onlineCategoryRes = useMetadata({
    variables: { type: MetaType.CHANGE_ONLINE_CATEGORY },
    fetchPolicy: 'cache-and-network',
  });
  const onlineLevelRes = useMetadata({
    variables: { type: MetaType.CHANGE_ONLINE_LEVEL },
    fetchPolicy: 'cache-and-network',
  });
  const onlineTypeRes = useMetadata({
    variables: { type: MetaType.CHANGE_ONLINE_TYPE },
    fetchPolicy: 'cache-and-network',
  });
  const onlineReasonRes = useMetadata({
    variables: { type: MetaType.CHANGE_ONLINE_REASON },
    fetchPolicy: 'cache-and-network',
  });
  const [getCurrentOwner, { data }] = useLazyEventCurrentOwner();
  const changeLevels = metaDataRes.data?.metadata ?? [];
  const onlineCategory = onlineCategoryRes.data?.metadata ?? [];
  const onlineLevel = onlineLevelRes.data?.metadata ?? [];
  const onlineType = onlineTypeRes.data?.metadata ?? [];
  const onlineReason = onlineReasonRes.data?.metadata ?? [];

  const handleChange = (manuallyAddRoleList: NotificationSelectObject[]) => {
    setManuallyAddRoleList(manuallyAddRoleList);
  };

  const [createChangeNotification, { loading }] = useCreateChangeNotification({
    onCompleted: data => {
      if (!data?.createChangeNotification?.success) {
        message.error(data?.createChangeNotification?.message);
        return;
      }
      message.success('发送成功');
      setVisible(false);
      getNotificationList();
    },
  });

  // 新增通报
  const onOk = async () => {
    createChangeNotification({
      variables: {
        query: {
          changeId: notificationInfo.id,
          reportChannel: reportChannels.join(','),
          reportContent: generateReportContent({
            notificationInfo,
            influenceSurface: influenceSurface,
            changeLevels,
            currentOwner: data?.fetchEventCurrentOwner?.data,
            onlineReason,
            onlineCategory,
            onlineLevel,
            onlineType,
          }),
          changeStatus: notificationInfo.changeStatus,
          reportObject: manuallyAddRoleList.map(item => ({
            type: item.type,
            name: item.label!,
            code: item.key!,
          })),
        },
      },
    });
  };

  const handleChangeLevelTextChange = () => {
    (async function () {
      const { error, data } = await fetchNotifyEventList({
        notifyCode: 'CHANGE_REPORT',
      });
      if (error) {
        message.error(error.message);
        return;
      }
      if (data.data?.length) {
        setReportChannels(data.data[0].notifyEventList[0].channelList);
        setReportObjects(generateReportObjects(data.data[0].notifyEventList[0]));
      }
    })();
  };

  const showModal = () => {
    setVisible(true);
  };
  const closeModal = () => {
    setVisible(false);
  };
  useEffect(() => {
    if (visible) {
      handleChangeLevelTextChange();
      if (notificationInfo?.changeVersion === 2) {
        getCurrentOwner({
          variables: {
            query: {
              idcTag: notificationInfo.idcTag,
              blockGuid: `${notificationInfo.idcTag}.${notificationInfo.blockGuid}`,
              currentTime: new Date().getTime(),
            },
          },
        });
      }
      (async function () {
        if (notificationInfo?.changeExeWay === CHANGE_EXE_WAY_MAP.OnLine) {
          const { response } = await influenceService.fetchBudgetInfluence({
            targetId: notificationInfo.id,
            idcTag: notificationInfo.idcTag,
            blockTag: notificationInfo.blockGuid.substring(
              notificationInfo.blockGuid.indexOf('.') + 1
            ),
            deviceGuidList: notificationInfo.causeDevices,
          });

          if (response) {
            const deviceCount = `${response.influenceDevices.length}个设备，`;
            const gridCount = `${response.influenceGrids.length}个机柜，`;
            const customerCount = `${response.influenceCustomers.length}个客户`;
            const influenceSurface = `${deviceCount}${gridCount}${customerCount}`;

            setInfluenceSurface(influenceSurface);
          }
        } else {
          setInfluenceSurface(notificationInfo?.changeInfluence);
        }
      })();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible]);

  return (
    <>
      <Button type="primary" onClick={showModal}>
        新增通报
      </Button>
      <Modal
        open={visible}
        width={1200}
        title={`${notificationInfo.id}变更新增通报`}
        okButtonProps={{ loading }}
        okText="发送通报"
        onOk={onOk}
        onCancel={closeModal}
      >
        <Descriptions bordered>
          <Descriptions.Item label="通报内容" span={3}>
            <ReportContent
              notificationInfo={notificationInfo}
              influenceSurface={influenceSurface}
              changeLevels={changeLevels}
              onlineCategory={onlineCategory}
              onlineLevel={onlineLevel}
              onlineType={onlineType}
              onlineReason={onlineReason}
              currentOwner={data?.fetchEventCurrentOwner?.data}
            />
          </Descriptions.Item>
          <Descriptions.Item label="通报对象" span={3}>
            <NotificationObject
              setParentObjectList={handleChange}
              initializeObjectList={reportObjects}
            />
          </Descriptions.Item>
          <Descriptions.Item label="通报方式" span={3}>
            <Space split={<Divider type="vertical" />}>
              {reportChannels.map((channel: BackendEventUpgradeChannel) => {
                if (channel === BackendEventUpgradeChannel.Webhook) {
                  return <WebhookItems key={channel} blockTag={notificationInfo.blockGuid} />;
                }
                return EventUpgradeChannelMap[channel];
              })}
            </Space>
          </Descriptions.Item>
        </Descriptions>
      </Modal>
    </>
  );
}
const ReportContent = ({
  notificationInfo,
  influenceSurface,
  changeLevels,
  currentOwner,
  onlineReason,
  onlineCategory,
  onlineLevel,
  onlineType,
}: {
  notificationInfo: NotificationInfo;
  influenceSurface: string;
  changeLevels: Metadata[];
  onlineReason: Metadata[];
  onlineCategory: Metadata[];
  onlineLevel: Metadata[];
  onlineType: Metadata[];
  currentOwner?: FetchEventCurrentOwnerData | null;
}) => {
  return (
    <Typography.Paragraph
      style={{
        width: '100%',
        maxHeight: '205px',
        whiteSpace: 'pre-wrap',
        textAlign: 'left',
        position: 'relative',
        overflowY: 'auto',
      }}
      copyable={{
        tooltips: false,
        icon: [
          <Button key="copy" style={{ position: 'absolute', top: 0, right: 0 }}>
            复制
          </Button>,
          <Button key="alreadyCopy" style={{ position: 'absolute', top: 0, right: 0 }}>
            已复制
          </Button>,
        ],
      }}
    >
      {generateReportContent({
        notificationInfo,
        influenceSurface,
        changeLevels,
        currentOwner,
        onlineReason,
        onlineCategory,
        onlineLevel,
        onlineType,
      })}
    </Typography.Paragraph>
  );
};
const generateReportContent = ({
  notificationInfo,
  influenceSurface,
  changeLevels,
  currentOwner,
  onlineLevel,
  onlineCategory,
  onlineReason,
  onlineType,
}: {
  notificationInfo: NotificationInfo;
  influenceSurface: string;
  changeLevels: Metadata[];
  onlineReason: Metadata[];
  onlineCategory: Metadata[];
  onlineLevel: Metadata[];
  onlineType: Metadata[];
  currentOwner?: FetchEventCurrentOwnerData | null;
}) => {
  let viewContent = `【变更通报】：${notificationInfo.title}\n【机房楼栋】：${
    notificationInfo.idcTag
  }.${notificationInfo.blockGuid}\n【变更等级】：${
    CHANGE_RISK_LEVEL_TEXT_MAP[notificationInfo.riskLevel]
  }\n【影响范围】：${influenceSurface || '--'}\n【计划时间】：${dayjs(
    notificationInfo.planStartTime
  ).format('YYYY-MM-DD HH:mm')}-${dayjs(notificationInfo.planEndTime).format(
    'YYYY-MM-DD HH:mm'
  )}\n【变更专业】：${notificationInfo.reason}\n【当前状态】：${
    CHANGE_TICKET_STATUS_TEXT_MAP[notificationInfo.changeStatus]
  }\n【提单人】：${notificationInfo?.creatorName}\n`;

  if (notificationInfo.changeVersion === 2) {
    const changeLevel =
      changeLevels.filter(item => item.code === notificationInfo.riskLevel)?.[0]?.name ?? '';
    viewContent = `【变更等级】：${changeLevel}\n【变更通报】：${
      notificationInfo.idcTag
    }.${notificationInfo.blockGuid}${notificationInfo.title}通报\n【变更类型】：${
      notificationInfo.changeTypeName
    }\n【变更窗口】：${dayjs(notificationInfo.planStartTime).format('YYYY-MM-DD HH:mm')}-${dayjs(
      notificationInfo.planEndTime
    ).format('YYYY-MM-DD HH:mm')}\n【变更进展】：${
      CHANGE_TICKET_STATUS_TEXT_MAP[notificationInfo.changeStatus]
    }\n【影响区域】：${notificationInfo.influenceArea ?? '--'}\n【涉及客户】：${notificationInfo.relateCustomer}\n【主要变更人员】：变更负责人：${notificationInfo.respPersonName}，变更执行人：${notificationInfo.operatorName}，当前值班长：${currentOwner?.userName ?? '--'}\n`;
  }

  if (notificationInfo.changeVersion === 3) {
    const changeLevel =
      onlineLevel.filter(item => item.code === notificationInfo.riskLevel)?.[0]?.name ?? '';
    const changeReason =
      onlineReason.filter(item => item.code === notificationInfo.reason)?.[0]?.name ?? '';
    const changeCategory =
      onlineCategory.filter(item => item.code === notificationInfo.changeCategory)?.[0]?.name ?? '';
    const changeType =
      onlineType.filter(item => item.code === notificationInfo.changeType)?.[0]?.name ?? '';
    const owner = notificationInfo?.changeResponsibleUserInfoList
      ?.map(item => item.label)
      .join(' | ');
    viewContent = `【变更名称】：${notificationInfo.title}\n【变更区域】：${notificationInfo.blockGuid ?? notificationInfo.idcTag}\n【变更专业】：${changeReason}\n【变更类别】：${changeCategory}\n【变更等级】：${changeLevel}\n【变更窗口期】：${dayjs(notificationInfo.planStartTime).format('YYYY-MM-DD HH:mm')}-${dayjs(
      notificationInfo.planEndTime
    ).format(
      'YYYY-MM-DD HH:mm'
    )}\n【变更类型】：${changeType}\n【变更负责人】：${owner}\n【影响说明】：${notificationInfo.changeInfluence ?? '--'}\n【变更状态】：${
      CHANGE_ONLINE_TICKET_STATUS_TEXT_MAP[notificationInfo.changeStatus]
    }\n`;
  }

  return viewContent;
};

const generateReportObjects = (data: BackendNotifyEvent) => {
  let roleList: { label: string; value: string; key: string; type: 'Role' | 'User' }[] = [];
  let userList: { label: string; value: string; key: string; type: 'Role' | 'User' }[] = [];
  if (data?.roleList) {
    roleList = data.roleList?.map((role: { roleName: string; roleCode: string }) => ({
      label: role.roleName,
      value: role.roleCode,
      key: role.roleCode,
      type: 'Role',
    }));
  }
  if (data?.userIdList) {
    userList = data.userIdList.map((user: number) => {
      const userString = user.toString();
      return { label: userString, value: userString, key: userString, type: 'User' };
    });
  }
  return [...roleList, ...userList];
};

const WebhookItems = ({ blockTag }: { blockTag: string }) => {
  const [webhooks, setWebhooks] = useState<Webhook[]>([]);
  useEffect(() => {
    (async function () {
      const { error, data } = await fetchWebhooks({
        pageNum: 1,
        pageSize: 500,
        scenes: 'CHANGE_REPORT',
        location: [blockTag],
        status: true,
      });
      if (error) {
        message.error(error.message);
        return;
      }
      setWebhooks(data.data);
    })();
  }, [blockTag]);
  return (
    <Popover
      content={
        <>
          {webhooks.length ? (
            <div style={{ maxHeight: 264, overflowY: 'auto' }}>
              <Space direction="vertical">
                {webhooks.map((webhook, index) => {
                  return <span key={webhook.webHookName}>{webhook.webHookName}</span>;
                })}
              </Space>
            </div>
          ) : (
            '空'
          )}
        </>
      }
      title={null}
    >
      <span>Webhook</span>
    </Popover>
  );
};
