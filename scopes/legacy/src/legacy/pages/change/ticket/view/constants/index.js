import { STATUS_MAP } from '@manyun/dc-brain.legacy.constants/statusColor';

import { CHANGE_OP_ITEM_STATUS_KEY_MAP, CHANGE_STEP_STATUS_KEY_MAP } from '../../../constants';

export function getStatusText(text) {
  let status = STATUS_MAP.NODATA;
  if (text === CHANGE_OP_ITEM_STATUS_KEY_MAP.NOT_START) {
    status = STATUS_MAP.NODATA;
  }
  if (
    text === CHANGE_OP_ITEM_STATUS_KEY_MAP.CHECKING ||
    text === CHANGE_OP_ITEM_STATUS_KEY_MAP.NORMAL
  ) {
    status = STATUS_MAP.NORMAL;
  }
  if (text === CHANGE_OP_ITEM_STATUS_KEY_MAP.EXCEPTION) {
    status = STATUS_MAP.ALARM;
  }
  return status;
}

export function getStepStatusText(text) {
  let status = STATUS_MAP.NODATA;
  if (text === CHANGE_STEP_STATUS_KEY_MAP.NOT_START) {
    status = STATUS_MAP.NODATA;
  }
  if (
    text === CHANGE_STEP_STATUS_KEY_MAP.PROCESSING ||
    text === CHANGE_STEP_STATUS_KEY_MAP.NORMAL
  ) {
    status = STATUS_MAP.NORMAL;
  }
  if (text === CHANGE_STEP_STATUS_KEY_MAP.EXCEPTION) {
    status = STATUS_MAP.ALARM;
  }
  return status;
}
