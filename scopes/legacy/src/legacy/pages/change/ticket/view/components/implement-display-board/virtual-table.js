/**
 * 这个组件暂时只给 变更详情/执行看板 使用。后续需要重构一个通用的 UI 组件到 base-ui 去
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-16
 */
import React from 'react';
import { VariableSizeGrid as Grid } from 'react-window';

import classNames from 'classnames';
import ResizeObserver from 'rc-resize-observer';

import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';

import styles from './virtual-table.module.less';

const SIZED_ROW_HEIGHTS = {
  large: 56,
  middle: 48,
  small: 40,
};

export const VirtualTable = ({ className, ...props }) => {
  const { bordered, size = 'large', columns, scroll } = props;
  const extraGridBorderRight = React.useMemo(() => (bordered ? 1 : 0), [bordered]);
  const rowHeight = React.useMemo(() => SIZED_ROW_HEIGHTS[size], [size]);
  const [tableWidth, setTableWidth] = React.useState(0);
  const totalWidth = columns.reduce((total, { width }) => {
    total += width ?? 0;

    return total;
  }, 0);
  const noWidthColumnCount = columns.filter(({ width }) => !width).length;
  const mergedColumns = columns.map(column => {
    if (column.width) {
      return column;
    }
    return {
      ...column,
      width: Math.floor(
        Math.abs(tableWidth - extraGridBorderRight - totalWidth) / noWidthColumnCount
      ),
    };
  });

  const gridRef = React.useRef();
  const [connectedObject] = React.useState(() => {
    const obj = {};
    Object.defineProperty(obj, 'scrollLeft', {
      get: () => {
        if (gridRef.current) {
          return gridRef.current?.state?.scrollLeft;
        }
        return null;
      },
      set: scrollLeft => {
        if (gridRef.current) {
          gridRef.current.scrollTo({
            scrollLeft,
          });
        }
      },
    });
    return obj;
  });

  React.useEffect(() => {
    gridRef.current?.resetAfterIndices({
      columnIndex: 0,
      shouldForceUpdate: true,
    });
  }, [tableWidth]);

  const renderVirtualList = (rawData, { scrollbarSize, ref, onScroll }) => {
    ref.current = connectedObject;
    const totalHeight = rawData.length * rowHeight;

    return (
      <Grid
        ref={gridRef}
        className={styles.grid}
        columnCount={mergedColumns.length}
        columnWidth={index => {
          const { width } = mergedColumns[index];
          return totalHeight > scroll.y && index === mergedColumns.length - 1
            ? width - scrollbarSize - 1
            : width;
        }}
        height={scroll.y}
        rowCount={rawData.length}
        rowHeight={() => rowHeight}
        width={tableWidth}
        onScroll={({ scrollLeft }) => {
          onScroll({
            scrollLeft,
          });
        }}
      >
        {({ columnIndex, rowIndex, style }) => {
          const column = mergedColumns[columnIndex];
          const record = rawData[rowIndex];
          const text = record[column.dataIndex];
          const renderedText =
            typeof column.render == 'function' ? column.render(text, record, rowIndex) : text;
          return (
            <Typography.Paragraph
              style={style}
              ellipsis={
                column.ellipsis
                  ? {
                      tooltip: renderedText,
                    }
                  : false
              }
              className={classNames(styles.cell, {
                // [styles.lastCell]: columnIndex === mergedColumns.length - 1,
              })}
            >
              {renderedText}
            </Typography.Paragraph>
          );
        }}
      </Grid>
    );
  };

  return (
    <ResizeObserver
      onResize={({ width }) => {
        setTableWidth(width);
      }}
    >
      <Table
        {...props}
        className={classNames(styles.virtualTable, className)}
        columns={mergedColumns}
        pagination={false}
        components={{
          body: renderVirtualList,
        }}
      />
    </ResizeObserver>
  );
};
