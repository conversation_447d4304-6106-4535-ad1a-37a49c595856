import moment from 'moment';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import shortid from 'shortid';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { EditableProTable } from '@manyun/base-ui.ui.editable-pro-table';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { createChangeOfflineExecuteLog } from '@manyun/ticket.service.create-change-offline-execute-log';
import { deleteChangeOfflineExecuteLog } from '@manyun/ticket.service.delete-change-offline-execute-log';
import { fetchChangeOfflineExecuteLog } from '@manyun/ticket.service.fetch-change-offline-execute-log';
import { updateChangeOfflineExecuteLog } from '@manyun/ticket.service.update-change-offline-execute-log';

export default function OfflineRecords({
  changeOrderId,
  blockGuid,
  creatorId,
  operatorId,
  statusNumber,
}) {
  const actionRef = useRef();
  const editorFormRef = useRef();
  const idCountRef = useRef(0);
  const [editableKeys, setEditableKey] = useState([]);
  const [dataSource, setDataSource] = useState([]);
  const [authorized] = useAuthorized({ checkByUserId: creatorId });
  const [operatorAuthorized] = useAuthorized({ checkByUserId: operatorId });

  useEffect(() => {
    getDataSouce();
    //  eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const disabledTime = current => {
    const hours = moment().hours();
    const minute = moment().minute();
    const choseHour = moment(current).hours();
    const isToday = moment(current).isSame(moment(), 'day');
    if (isToday) {
      if (choseHour === hours) {
        return {
          disabledHours: () => range(hours + 1, 24),
          disabledMinutes: () => range(minute + 1, 60),
        };
      }
      return {
        disabledHours: () => range(hours + 1, 24),
      };
    }
    return;
  };

  const getDataSouce = async () => {
    const { data, error } = await fetchChangeOfflineExecuteLog({ changeOrderId });
    if (error) {
      message.error(error.message);
      return;
    }
    setDataSource(
      data.data.map(item => {
        return {
          ...item,
          exeTime: moment(item.exeTime), // DatePicker回显需要moment，dayjs不行！
          operatorName: { label: item.operatorName, value: item.operatorId },
        };
      })
    );
  };

  const disabledDate = current => {
    return current && current.valueOf() > moment().endOf('day').valueOf();
  };

  const range = (start, end) => {
    const result = [];
    for (let i = start; i < end; i++) {
      result.push(i);
    }
    return result;
  };

  const getColumns = (total, editableKeys) => [
    {
      title: '序号',
      key: 'index',
      fixed: 'left',
      width: 80,
      render: (_, __, index) => {
        if (editableKeys.length) {
          return total - index + 1;
        }
        return total - index;
      },
      renderFormItem: ({ index }) => {
        return total - index + 1;
      },
    },
    {
      title: '执行人',
      dataIndex: 'operatorName',
      width: 196,
      renderFormItem: () => {
        return <UserSelect blockGuid={blockGuid} includeCurrentUser labelInValue />;
      },
      formItemProps: {
        rules: [{ required: true, message: '执行人必选！' }],
      },
      render: (_, { operatorName }) => operatorName?.label,
    },
    {
      title: '执行内容',
      dataIndex: 'exeContent',
      renderFormItem: (_, { record }) => {
        return <Input />;
      },
      formItemProps: {
        rules: [
          { required: true, message: '执行内容必填！' },
          {
            max: 50,
            message: '最多输入50个字符！',
          },
        ],
      },
    },
    {
      title: '执行时间',
      dataIndex: 'exeTime',
      render: text => {
        return moment(text).format('YYYY-MM-DD HH:mm');
      },
      width: 288,
      renderFormItem: () => {
        return (
          <DatePicker
            disabledDate={disabledDate}
            disabledTime={disabledTime}
            allowClear
            format="YYYY-MM-DD HH:mm"
            showTime
            showToday={false}
          />
        );
      },
      formItemProps: {
        rules: [
          { required: true, message: '执行时间必选！' },
          {
            validator(_, value) {
              if (value && moment(value).diff(moment(), 'minutes') > 0) {
                return Promise.reject(new Error('执行时间不可大于当前时间'));
              }
              return Promise.resolve();
            },
          },
        ],
      },
    },
    {
      title: '操作',
      valueType: 'option',
      fixed: 'right',
      width: 120,
      render: (text, record, _, action) =>
        (authorized || operatorAuthorized) && statusNumber === 3
          ? [
              <Button
                key="editable"
                type="link"
                compact
                onClick={() => {
                  action?.startEditable?.(record.id);
                }}
              >
                编辑
              </Button>,
              <Button
                key="delete"
                type="link"
                compact
                onClick={async () => {
                  const { error } = await deleteChangeOfflineExecuteLog({ id: record.id });
                  if (error) {
                    message.error(error.message);
                    return;
                  }
                  getDataSouce();
                }}
              >
                删除
              </Button>,
            ]
          : null,
    },
  ];

  const genNewId = useCallback(() => {
    const key = idCountRef.current++;
    const newId = shortid();
    return `${key}_${newId}`;
  }, [idCountRef]);

  return (
    <EditableProTable
      rowKey="id"
      ghost
      scroll={{ x: 'max-content' }}
      editableFormRef={editorFormRef}
      columns={getColumns(dataSource.length, editableKeys)}
      value={dataSource}
      // onChange={data => {
      //   setDataSource(data);
      // }}
      recordCreatorProps={
        (authorized || operatorAuthorized) && statusNumber === 3
          ? {
              position: 'top',
              creatorButtonText: '添加执行记录',
              record: () => {
                const newId = genNewId();
                return {
                  id: newId,
                  isNew: true,
                };
              },
            }
          : false
      }
      actionRef={actionRef}
      editable={{
        editableKeys: editableKeys,
        onChange: keys => {
          setEditableKey(keys);
        },
        actionRender: (_, __, dom) => [dom.save, dom.cancel],
        onSave: async (_, record) => {
          const { id, exeTime, exeContent, operatorName, isNew } = record;
          if (isNew) {
            const { error } = await createChangeOfflineExecuteLog({
              exeTime: moment(exeTime),
              exeContent,
              operatorName: operatorName.label,
              operatorId: operatorName.key,
              changeOrderId,
            });
            if (error) {
              message.error(error.message);
              return Promise.reject();
            }
          } else {
            const { error } = await updateChangeOfflineExecuteLog({
              exeTime: moment(exeTime),
              exeContent,
              operatorName: operatorName.label,
              operatorId: operatorName.value,
              id,
            });
            if (error) {
              message.error(error.message);
              return Promise.reject();
            }
          }
          getDataSouce();
          return Promise.resolve();
        },
      }}
    />
  );
}
