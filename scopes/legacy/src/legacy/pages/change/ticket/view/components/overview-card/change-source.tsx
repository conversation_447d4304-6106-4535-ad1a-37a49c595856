import React, { useMemo, useState } from 'react';

import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { useFetchFuzzyChanges, useLazyFetchFuzzyEvents } from '@manyun/ticket.gql.client.tickets';
import { getChangeLocales } from '@manyun/ticket.model.change';
import type { SourceType } from '@manyun/ticket.model.change';
import { fetchPagedRiskRegisters } from '@manyun/ticket.service.fetch-paged-risk-registers';

export type ChangeSourceProps = {
  idcTag: string;
  blockGuid?: string | null;
  shouldShowEventNumber: boolean;
  value?: { relateType: SourceType; relateNo: string } | undefined;
  onChange?: (value: { relateType?: SourceType; relateNo?: string }) => void;
};

type LabelInValue = {
  label: string;
  key: string;
  value: string;
};

export const ChangeSourceSelect = React.forwardRef(
  (
    { idcTag, blockGuid, shouldShowEventNumber, value, onChange }: ChangeSourceProps,
    ref: React.Ref<RefSelectProps>
  ) => {
    const [dataSource, setDataSource] = useState<LabelInValue[]>([]);
    const locales = useMemo(() => getChangeLocales(), []);

    const [getFuzzyChanges] = useFetchFuzzyChanges();
    const [getFuzzyEvents] = useLazyFetchFuzzyEvents();

    const getChangeList = async (contentKey: string) => {
      if (!contentKey) {
        return;
      }
      const { data, error } = await getFuzzyChanges({
        variables: { queryCondition: contentKey, blockGuid },
      });
      if (error) {
        message.error(error.message);
        return;
      }
      const list: LabelInValue[] = data?.fetchFuzzyChanges?.data
        ? data?.fetchFuzzyChanges?.data?.map(item => {
            return {
              title: item.title,
              label: `${item.changeOrderId} ${item.title}`,
              value: item.changeOrderId,
              key: item.changeOrderId,
            };
          })
        : [];
      setDataSource(list);
    };

    const getEventList = async (contentKey: string) => {
      const { data, error } = await getFuzzyEvents({
        variables: {
          query: {
            idcTag,
            blockGuid,
            keyword: contentKey,
          },
        },
      });
      if (error) {
        message.error(error.message);
        return;
      }
      const list: LabelInValue[] = (data?.fetchFuzzyEvents?.data ?? []).map(item => {
        return {
          title: item.eventTitle,
          label: `${shouldShowEventNumber ? item.eventNo : item.id} ${item.eventTitle ?? ''}`,
          value: item.eventNo!,
          key: item.eventNo!,
        };
      });
      setDataSource(list);
    };

    const getRiskRegisterList = async (contentKey: string) => {
      const { data, error } = await fetchPagedRiskRegisters({
        pageNum: 1,
        pageSize: 1000,

        blockGuids: blockGuid ? [blockGuid] : [],
        riskKey: contentKey,
      });
      if (error) {
        message.error(error.message);
        return;
      }
      const list: LabelInValue[] = data.data.map(item => {
        return {
          title: item.riskDesc,
          label: `${item.id} ${item.riskDesc}`,
          value: item.id,
          key: item.id,
        };
      });
      setDataSource(list);
    };

    const disabled = !idcTag || !value?.relateType;
    return (
      <Space.Compact block style={{ width: 'calc(100% - 32px)', display: 'flex' }}>
        <Select
          style={{ width: 112, marginRight: 8, height: 32 }}
          options={[
            { label: '事件', value: 'EVENT' },
            { label: '风险', value: 'RISK' },
            { label: '变更', value: 'CHANGE' },
          ]}
          value={value?.relateType}
          allowClear
          disabled={!idcTag}
          onChange={(value: SourceType) => {
            setDataSource([]);
            onChange && onChange({ relateType: value, relateNo: undefined });
          }}
        />
        {Object.keys(locales.changeSource.source).includes(value?.relateType ?? '') ? (
          <Select
            allowClear
            style={{ width: 356 }}
            disabled={disabled}
            options={dataSource}
            value={value?.relateNo}
            showSearch
            placeholder="请输入ID或名称查询"
            onSearch={(content: string) => {
              switch (value?.relateType) {
                case 'EVENT':
                  getEventList(content);
                  break;
                case 'CHANGE':
                  getChangeList(content);
                  break;
                case 'RISK':
                  getRiskRegisterList(content);
                  break;
              }
            }}
            onChange={(id: string) => {
              onChange && onChange({ relateType: value?.relateType, relateNo: id });
            }}
          />
        ) : (
          <Input
            allowClear
            maxLength={100}
            disabled={disabled}
            onChange={e => {
              onChange &&
                onChange({
                  relateType: value?.relateType,
                  relateNo: e.target.value,
                });
            }}
          />
        )}
      </Space.Compact>
    );
  }
);

ChangeSourceSelect.displayName = 'ChangeSourceSelect';
