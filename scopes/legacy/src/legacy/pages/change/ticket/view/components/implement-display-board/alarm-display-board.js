import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { useParams } from 'react-router-dom';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Badge } from '@manyun/base-ui.ui.badge';
import { Card } from '@manyun/base-ui.ui.card';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Alarm } from '@manyun/monitoring.model.alarm';
import { fetchAlarms } from '@manyun/monitoring.service.fetch-alarms';
import { ChangeTabs } from '@manyun/ticket.model.change';
import { useChangeContext } from '@manyun/ticket.page.change-offline';
import { exportChangeTicketAlarms } from '@manyun/ticket.service.export-change-ticket-alarms';

import { <PERSON><PERSON>T<PERSON>, <PERSON><PERSON><PERSON>rapper, Tiny<PERSON><PERSON> } from '@manyun/dc-brain.legacy.components';
import {
  changeActions,
  getAlarmLevelCount,
  getTicketDetailAlarmList,
} from '@manyun/dc-brain.legacy.redux/actions/changeActions';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';

export function TicketAlarm({
  categoryDisplayBarList,
  list,
  total,
  pageNum,
  pageSize,
  metaCategoryEntities,
  syncCommonData,
  getTicketDetailAlarmList,
  getAlarmLevelCount,
  setAlarmLevelCount,
  stepCurrent,
  idcTag,
  blockTags,
  newDetailIdcTag,
  newDetailblockTag,
}) {
  const [expected, setExpected] = useState(false);
  const [deviceName, setDeviceTag] = useState('');
  const [exportLoading, setExportLoading] = useState(false);
  const [isExpectedData, setIsExpectedData] = useState([]);

  const { isExportPDF, setExportPDFTabs } = useChangeContext();

  const { id } = useParams();

  useEffect(() => {
    syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });
    if (isExportPDF) {
      getExprotPdfData();
    }
    getTicketDetailAlarmList({
      pageNum,
      pageSize: isExportPDF ? 1000 : pageSize,
      changeId: id,
      deviceName,
      isExpected: false,

      idcTag: newDetailIdcTag,
      blockTag: newDetailblockTag,
      onFetchFinish: () => {
        if (isExportPDF) {
          setExportPDFTabs(exportPDFTabs => {
            return exportPDFTabs.map(item => ({
              ...item,
              isRenderd: item.key === ChangeTabs.Alarm ? true : item.isRenderd,
            }));
          });
        }
      },
    });
    getAlarmLevelCount({
      changeId: id,
      isExpected: false,
      idcTag: newDetailIdcTag,
      blockTag: newDetailblockTag,
      onFetchFinish: data => {
        if (isExportPDF) {
          setExportPDFTabs(exportPDFTabs => {
            return exportPDFTabs.map(item => ({
              ...item,
              isVolid: item.key === ChangeTabs.Alarm ? !data : item.isVolid,
            }));
          });
        }
      },
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const getExprotPdfData = async () => {
    const { data } = await fetchAlarms({
      pageNum: 1,
      pageSize: 1000,
      changeId: id,
      deviceName,
      isExpected: true,
      idcTag: newDetailIdcTag ?? idcTag,
      blockTags: [newDetailblockTag] ?? blockTags,
      isQueryData: true,
      isMerge: true,
    });
    setIsExpectedData(data?.data?.length ? data.data.map(Alarm.toApiObject) : []);
  };

  const onChangePage = (pageNum, pageSize) => {
    getTicketDetailAlarmList({
      pageSize,
      pageNum,
      isExpected: expected,
      deviceName,
      changeId: id,
      idcTag: newDetailIdcTag,
      blockTag: newDetailblockTag,
    });
  };

  const onChangeCard = record => {
    const list = categoryDisplayBarList.map(item => {
      if (item.expected === record.expected) {
        return {
          ...item,
          selected: record.selected ? false : true,
        };
      }
      return {
        ...item,
        selected: false,
      };
    });
    setAlarmLevelCount(list);
    getAlarmLevelCount({
      changeId: id,
      isExpected: record.expected ? true : false,
      idcTag: newDetailIdcTag,
      blockTag: newDetailblockTag,
    });
    getTicketDetailAlarmList({
      pageNum,
      pageSize,
      changeId: id,
      isExpected: record.expected ? true : false,
      deviceName,
      idcTag: newDetailIdcTag,
      blockTag: newDetailblockTag,
    });
    setExpected(record.expected);
  };

  const getList = e => {
    getTicketDetailAlarmList({
      pageNum,
      pageSize,
      changeId: id,
      isExpected: expected,
      deviceName: e.target.value,

      idcTag: newDetailIdcTag,
      blockTag: newDetailblockTag,
    });
    setDeviceTag(e.target.value);
  };

  const handleFileExport = async type => {
    const params = {
      changeId: id,
      idcTag: idcTag,
      isExpected: expected,
    };
    setExportLoading(true);
    if (type === 'filtered') {
      params.deviceName = deviceName?.trim();
    }
    const { error, data } = await exportChangeTicketAlarms(params);
    setExportLoading(false);
    if (error) {
      message.error(error.message);
      return error.message;
    }
    return data;
  };
  const showColumns = [
    'alarmType',
    'alarmLevel',
    'blockTag',
    'roomTag',
    'notifyContent',
    'deviceName',
    'pointCodeName',
    'triggerSnapshot',
    'pointValue',
    'triggerStatus',
    'gmtCreate',
    'triggerTime',
  ];
  return (
    <TinyCard>
      <GutterWrapper mode="vertical">
        {isExportPDF ? (
          <Space direction="vertical" style={{ width: '100%' }}>
            {list?.length ? <ConditionalCard cardInfo={categoryDisplayBarList?.[0]} /> : null}
            {list?.length ? (
              <AlarmTable
                showColumns={showColumns}
                actions={[
                  <Input
                    key="search"
                    style={{ width: 200 }}
                    placeholder="设备名称"
                    allowClear
                    onChange={getList}
                  />,
                  <FileExport
                    key="export"
                    text="导出"
                    filename="变更告警记录.xls"
                    disabled={exportLoading || !list?.length}
                    showExportFiltered
                    data={type => {
                      return handleFileExport(type);
                    }}
                  />,
                ]}
                dataSource={list || []}
                pagination={false}
                metaCategoryEntities={metaCategoryEntities}
                actionsWrapperStyle={{ justifyContent: 'space-between' }}
              />
            ) : null}
            {isExpectedData?.length ? (
              <ConditionalCard cardInfo={categoryDisplayBarList?.[1]} />
            ) : null}
            {isExpectedData?.length ? (
              <AlarmTable
                showColumns={showColumns}
                actions={[
                  <Input
                    key="search"
                    style={{ width: 200 }}
                    placeholder="设备名称"
                    allowClear
                    onChange={getList}
                  />,
                  <FileExport
                    key="export"
                    text="导出"
                    filename="变更告警记录.xls"
                    disabled={exportLoading || !isExpectedData.length}
                    showExportFiltered
                    data={type => {
                      return handleFileExport(type);
                    }}
                  />,
                ]}
                dataSource={isExpectedData}
                pagination={false}
                metaCategoryEntities={metaCategoryEntities}
                actionsWrapperStyle={{ justifyContent: 'space-between' }}
              />
            ) : null}
          </Space>
        ) : (
          <>
            <Space direction="horizontal">
              {categoryDisplayBarList.map(item => (
                <ConditionalCard key={item.type} cardInfo={item} onChangeCard={onChangeCard} />
              ))}
            </Space>
            <AlarmTable
              showColumns={showColumns}
              actions={[
                <Input
                  key="search"
                  style={{ width: 200 }}
                  placeholder="设备名称"
                  allowClear
                  onChange={getList}
                />,
                <FileExport
                  key="export"
                  text="导出"
                  filename="变更告警记录.xls"
                  disabled={exportLoading || !list.length}
                  showExportFiltered
                  data={type => {
                    return handleFileExport(type);
                  }}
                />,
              ]}
              dataSource={list}
              pagination={{
                total: total,
                current: pageNum,
                pageSize: pageSize,
                onChange: onChangePage,
              }}
              metaCategoryEntities={metaCategoryEntities}
              actionsWrapperStyle={{ justifyContent: 'space-between' }}
              onRowShowDetail
            />
          </>
        )}
      </GutterWrapper>
    </TinyCard>
  );
}

const mapStateToProps = ({
  change: {
    ticketDetail: {
      alarm: { categoryDisplayBarList, list, total, pageNum, pageSize },
      changeInfo: { step1 },
    },
  },
  common: { deviceCategory },
}) => ({
  categoryDisplayBarList,
  list,
  total,
  pageNum,
  pageSize,
  metaCategoryEntities: deviceCategory ? deviceCategory.normalizedList : {},
  idcTag: step1.idcTag,
  blockTags: [step1.blockTag],
});

const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
  getTicketDetailAlarmList: getTicketDetailAlarmList,
  getAlarmLevelCount: getAlarmLevelCount,
  setAlarmLevelCount: changeActions.setAlarmLevelCount,
};
export default connect(mapStateToProps, mapDispatchToProps)(TicketAlarm);

export function Level({ color, level, number }) {
  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
      }}
    >
      <Badge color={color} />
      <div
        style={{
          display: 'inline-block',
          margin: '0 6px',
        }}
      >
        {level}
      </div>
      <div>{number || 0}</div>
    </div>
  );
}

function ConditionalCard({ cardInfo, onChangeCard }) {
  return (
    <Card
      style={{
        borderWidth: 1,
        borderColor: cardInfo.selected
          ? `var(--${prefixCls}-primary-color)`
          : 'var(--border-color-split)',
        width: 300,
      }}
      onClick={() => onChangeCard(cardInfo)}
    >
      <div style={{ display: 'flex', justifyContent: 'space-between	' }}>
        <div style={{ width: '52%' }}>
          <div style={{ fontSize: '24px' }}>{cardInfo.totalCount || 0}</div>
          <div style={{ fontSize: '12px' }}>{cardInfo.type}</div>
        </div>
        <div style={{ width: '46%' }}>
          <Level color="red" level="一级" number={cardInfo[1]} selected={cardInfo.selected} />
          <Level color="orange" level="二级" number={cardInfo[2]} selected={cardInfo.selected} />
          <Level color="blue" level="三级" number={cardInfo[3]} selected={cardInfo.selected} />
        </div>
      </div>
    </Card>
  );
}
