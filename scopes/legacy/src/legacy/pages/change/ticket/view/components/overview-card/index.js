import moment from 'moment';
import React, { useEffect, useState } from 'react';
import { connect, useSelector } from 'react-redux';
import { Link } from 'react-router-dom';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { Badge } from '@manyun/base-ui.ui.badge';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Divider } from '@manyun/base-ui.ui.divider';
import { FileList, SimpleFileList } from '@manyun/base-ui.ui.file-list';
import { message } from '@manyun/base-ui.ui.message';
import { Popover } from '@manyun/base-ui.ui.popover';
import { Space } from '@manyun/base-ui.ui.space';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import { generateBPMRoutePath } from '@manyun/bpm.route.bpm-routes';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { MetaTypeText } from '@manyun/resource-hub.ui.meta-type-text';
import { useLazyChangeOnlineTemplate } from '@manyun/ticket.gql.client.tickets';
import {
  CHANGE_EXE_WAY_MAP,
  CHANGE_EXE_WAY_TEXT_MAP,
  CHANGE_RESULT_KEY_MAP,
  CHANGE_RESULT_KEY_TEXT_MAP,
  CHANGE_RISK_LEVEL_TEXT_MAP,
  ChangeTicketState,
} from '@manyun/ticket.model.change';
import { useChangeContext } from '@manyun/ticket.page.change-offline';
import { ChangeStep } from '@manyun/ticket.page.change-online-mutator';
import {
  generateChangeOnlineTemplateLocation,
  generateEventDetailRoutePath,
} from '@manyun/ticket.route.ticket-routes';
import { fetchChangeAssociateEvent } from '@manyun/ticket.service.fetch-change-associate-event';
import { ChangeOfflineCriticalStepTable } from '@manyun/ticket.ui.change-offline-critical-step-table';
import { ChangeOfflineDeviceTable } from '@manyun/ticket.ui.change-offline-device-table';
import { ChangeSourceText } from '@manyun/ticket.ui.change-source-text';
import { ChangeTimeText } from '@manyun/ticket.ui.change-time-text';

import { FooterToolBar, TinyCard, UserLink } from '@manyun/dc-brain.legacy.components';
import {
  getTicketDetailInfo,
  ticketCloseActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/changeActions';

import StepTableList from '../../../../components/step-table-list';
import { CHANGE_TICKET_STATUS_KEY_MAP } from '../../../../constants';
import { ChangeNotificationModal } from './change-notification-modal';
import { ChangeSourceListText } from './change-source-list-text';
import { CreateRelateTicket } from './create-relate-ticket';
import { RelateChangeSource } from './relate-change';

export function TicketOverviewCard({
  step1,
  stepCodes,
  stepMaps,
  checkItemsMap,
  matchObjectInfoMaps,
  checkItemDeviceMaps,
  stepCurrent,
  ticketCloseActionCreator,
  deviceGuids,
  showNewDetail,
  changeInfo,
  newStepCodes,
  newStepMaps,
  refetchDetail,
  isOwnerUser,
}) {
  const [authorized] = useAuthorized({ checkByUserId: step1.creatorId });
  const [operatorAuthorized] = useAuthorized({ checkByUserId: step1.operatorId });
  const { isExportPDF, setExportPDFTabs } = useChangeContext();
  const [expandAll, setExpandAll] = useState(true);
  const [, { checkUserId }] = useAuthorized();

  const config = useSelector(selectCurrentConfig);

  const configUtil = new ConfigUtil(config);

  const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket');
  const changesTicketCreateChangeOffline =
    ticketScopeCommonConfigs.changes.features.createChangeOffline;
  const customerAgreement = ticketScopeCommonConfigs.changes.features.customerAgreement;
  const isYgConfig = changesTicketCreateChangeOffline === 'full' && !showNewDetail;
  const [fetchDetail] = useLazyChangeOnlineTemplate({
    onCompleted(data) {
      if (data?.changeOnlineTemplate?.data?.isDelete) {
        message.error('模版已被删除');
        return;
      }
      window.open(
        generateChangeOnlineTemplateLocation({ id: changeInfo.templateId }),
        '_blank',
        'noopener noreferrer'
      );
    },
  });

  const onClose = () => {
    ticketCloseActionCreator({
      changeOrderId: step1.changeOrderId,
      type: 'detail',
    });
  };

  const gocItems = [
    {
      label: '变更标题',
      value: (
        <Typography.Text ellipsis={{ tooltip: step1.title.value }}>
          {step1.title.value}
        </Typography.Text>
      ),
    },
    {
      label: '位置',
      value: `${step1.location.value[0]}.${step1.location.value[1]}`,
    },
    {
      label: '变更方式',
      value: step1.exeWay && CHANGE_EXE_WAY_TEXT_MAP[step1.exeWay],
      hidden: isYgConfig,
    },
    {
      label: '变更专业',
      value: step1.reason && step1.reason.value.label,
    },
    {
      label: '变更类型',
      value: step1.changeTypeName,
    },
    {
      label: '变更等级',
      value: step1.riskLevel.value && CHANGE_RISK_LEVEL_TEXT_MAP[step1.riskLevel.value],
    },
    {
      label: '客户同意书',
      value: step1?.customerFileInfoList?.value?.length ? (
        <SimpleFileList files={step1.customerFileInfoList.value}>
          <Button type="link" compact>
            查看
          </Button>
        </SimpleFileList>
      ) : (
        '--'
      ),
      hidden: customerAgreement === 'disabled',
    },
    {
      label: '变更模板',
      value: step1.templateName || '无',
      hidden: step1.exeWay === CHANGE_EXE_WAY_MAP.OffLine,
    },
    {
      label: '关联事件',
      value: <AssociateEvent changeOrderId={step1.changeOrderId} />,
    },
    {
      label: '执行角色',
      value:
        step1.exeUserGroupCode &&
        step1.exeUserGroupCode.value &&
        step1.exeUserGroupCode.value.label,
      span: 1,
      hidden: step1.exeWay === CHANGE_EXE_WAY_MAP.OffLine,
    },
    {
      label: '提单人',
      value: <UserLink userName={step1.creatorName} userId={step1.creatorId} />,
    },
    {
      label: '当前操作人',
      value: <UserLink userName={step1.operatorName} userId={step1.operatorId} />,
    },
    {
      label: '计划时间',
      value:
        step1.planStartTime.value &&
        `${moment(step1.planStartTime.value).format('YYYY-MM-DD HH:mm')}  ${
          step1.planEndTime.value
            ? `- ${moment(step1.planEndTime.value).format('YYYY-MM-DD HH:mm')}`
            : ''
        }`,
      span: step1.exeWay === CHANGE_EXE_WAY_MAP.OnLine ? 2 : 1,
    },
    {
      label: '执行时间',
      value:
        step1.realStartTime &&
        `${moment(step1.realStartTime).format('YYYY-MM-DD HH:mm')}  ${
          step1.realEndTime ? `- ${moment(step1.realEndTime).format('YYYY-MM-DD HH:mm')}` : ''
        }`,

      span: step1.exeWay === CHANGE_EXE_WAY_MAP.OnLine ? 2 : 1,
    },

    {
      label: '附件',
      value: step1.fileInfoList.value?.length ? (
        <SimpleFileList files={step1.fileInfoList.value}>
          <Button type="link" compact>
            查看
          </Button>
        </SimpleFileList>
      ) : (
        '--'
      ),
      hidden: step1.exeWay === CHANGE_EXE_WAY_MAP.OffLine,
    },
    {
      label: '影响范围',
      value: step1.changeInfluence ? (
        <Typography.Text ellipsis={{ tooltip: step1.changeInfluence }}>
          {step1.changeInfluence}
        </Typography.Text>
      ) : (
        '--'
      ),
      span: 2,
      hidden: step1.exeWay === CHANGE_EXE_WAY_MAP.OnLine,
    },
  ].filter(({ hidden = false }) => !hidden);

  const ygItems = [
    {
      label: '机房/楼',
      value: `${step1.location.value[0]}.${step1.location.value[1]}`,
    },
    {
      label: '变更标题',
      value: (
        <Typography.Text ellipsis={!isExportPDF && { tooltip: step1.title.value }}>
          {step1.title.value}
        </Typography.Text>
      ),
    },
    {
      label: '变更目的',
      value: (
        <Typography.Text ellipsis={!isExportPDF && { tooltip: step1.purpose }}>
          {step1.purpose}
        </Typography.Text>
      ),
      // contentStyle={{ overflow: 'hidden' }}
    },
    {
      label: '变更来源',
      value: <ChangeSourceText sourceNo={step1.sourceNo} sourceType={step1.sourceType} />,
    },
    {
      label: '变更专业',
      value: step1.reason && step1.reason.value.label,
    },
    {
      label: '变更类型',
      value: step1.changeTypeName,
    },
    {
      label: '变更等级',
      value: step1.riskLevel.value && CHANGE_RISK_LEVEL_TEXT_MAP[step1.riskLevel.value],
    },
    {
      label: '提单人',
      value: <UserLink userName={step1.creatorName} userId={step1.creatorId} />,
    },
    {
      label: '变更计划时间',
      value: step1?.changeOrderExtJson?.changeTimeList ? (
        <ChangeTimeText
          changeTimeList={step1.changeOrderExtJson.changeTimeList}
          isExportPDF={isExportPDF}
        />
      ) : (
        '--'
      ),
      span: 2,
    },
    {
      label: '执行时间',
      value:
        step1.realStartTime &&
        `${moment(step1.realStartTime).format('YYYY-MM-DD HH:mm')}  ${
          step1.realEndTime ? `- ${moment(step1.realEndTime).format('YYYY-MM-DD HH:mm')}` : ''
        }`,
    },
    {
      label: '当前操作人',
      value: <UserLink userName={step1.operatorName} userId={step1.operatorId} />,
    },
    {
      label: '影响范围',
      value: (
        <Typography.Text ellipsis={!isExportPDF && { tooltip: step1.changeInfluence }}>
          {step1.changeInfluence}
        </Typography.Text>
      ),
      span: 4,
    },
  ];
  const gocTestItems = [
    {
      label: '变更标题',
      value: (
        <Typography.Text ellipsis={{ tooltip: step1.title.value }}>
          {step1.title.value}
        </Typography.Text>
      ),
    },
    {
      label: '位置',
      value: `${step1.location.value[0]}.${step1.location.value[1]}`,
    },
    {
      label: '变更等级',
      value: step1.riskLevel.value && (
        <MetaTypeText code={step1.riskLevel.value} metaType={MetaType.CHANGE_LEVEL} />
      ),
    },
    {
      label: '变更负责人',
      value: <UserLink userName={step1.respPersonName} userId={step1.respPersonId} />,
    },
    {
      label: '变更专业',
      value: step1.reason && (
        <MetaTypeText code={step1.reason.value.label} metaType={MetaType.CHANGE_REASON} />
      ),
    },
    {
      label: '变更类型',
      value: step1.changeTypeName,
    },
    {
      label: '客户同意书',
      value: step1?.customerFileInfoList?.value?.length ? (
        <SimpleFileList files={step1.customerFileInfoList.value}>
          <Button type="link" compact>
            查看
          </Button>
        </SimpleFileList>
      ) : (
        '--'
      ),
    },
    {
      label: '关联事件',
      value: <AssociateEvent changeOrderId={step1.changeOrderId} />,
    },
    {
      label: '变更申请人',
      value: <UserLink userName={step1.creatorName} userId={step1.creatorId} />,
    },
    {
      label: '变更执行人',
      value: <UserLink userName={step1.operatorName} userId={step1.operatorId} />,
    },
    {
      label: '计划时间',
      value:
        step1.planStartTime.value &&
        `${moment(step1.planStartTime.value).format('YYYY-MM-DD HH:mm')}  ${
          step1.planEndTime.value
            ? `- ${moment(step1.planEndTime.value).format('YYYY-MM-DD HH:mm')}`
            : ''
        }`,
    },
    {
      label: '执行时间',
      value:
        step1.realStartTime &&
        `${moment(step1.realStartTime).format('YYYY-MM-DD HH:mm')}  ${
          step1.realEndTime ? `- ${moment(step1.realEndTime).format('YYYY-MM-DD HH:mm')}` : ''
        }`,
    },
    {
      label: '涉及客户',
      value: step1.relateCustomer,
    },
    {
      label: '影响区域',
      value: step1.influenceArea ? (
        <Typography.Text ellipsis={{ tooltip: step1.influenceArea }}>
          {step1.influenceArea}
        </Typography.Text>
      ) : (
        '--'
      ),
      span: 3,
    },
  ];
  const newChangeItems = [
    {
      label: '变更名称',
      value: (
        <Typography.Text ellipsis={{ tooltip: changeInfo.title }}>
          {changeInfo.title || '--'}
        </Typography.Text>
      ),
    },
    {
      label: '变更区域',
      value: changeInfo.blockGuid ? changeInfo.blockGuid : changeInfo.idcTag || '--',
    },
    {
      label: '变更模板',
      value: changeInfo.templateName ? (
        <Typography.Text ellipsis={{ tooltip: changeInfo.templateName }}>
          {/* <Link
            to={generateChangeOnlineTemplateLocation({ id: changeInfo.templateId })}
            target="_blank"
          >
            {changeInfo.templateName}
          </Link> */}
          <Button
            type="link"
            compact
            onClick={() => {
              fetchDetail({
                variables: {
                  templateId: changeInfo.templateId,
                },
              });
            }}
          >
            {changeInfo.templateName}
          </Button>
        </Typography.Text>
      ) : (
        '--'
      ),
    },
    {
      label: '变更来源',
      value: changeInfo.changeSourceInfoList?.length ? (
        <ChangeSourceListText sourceList={changeInfo.changeSourceInfoList} />
      ) : (
        '--'
      ),
    },
    {
      label: '变更等级',
      value: changeInfo.riskLevel ? (
        <MetaTypeText code={changeInfo.riskLevel} metaType={MetaType.CHANGE_ONLINE_LEVEL} />
      ) : (
        '--'
      ),
    },
    {
      label: '变更专业',
      value: changeInfo.reason ? (
        <MetaTypeText code={changeInfo.reason} metaType={MetaType.CHANGE_ONLINE_REASON} />
      ) : (
        '--'
      ),
    },
    {
      label: '变更类别',
      value: changeInfo.changeCategory ? (
        <MetaTypeText code={changeInfo.changeCategory} metaType={MetaType.CHANGE_ONLINE_CATEGORY} />
      ) : (
        '--'
      ),
    },
    {
      label: '变更类型',
      value: changeInfo.changeType ? (
        <MetaTypeText code={changeInfo.changeType} metaType={MetaType.CHANGE_ONLINE_TYPE} />
      ) : (
        '--'
      ),
    },
    {
      label: '变更窗口期',
      value: (
        <>
          {changeInfo.planStartTime && changeInfo.planEndTime ? (
            <Typography.Text
              ellipsis={{
                tooltip: `${moment(changeInfo.planStartTime).format('YYYY-MM-DD HH:mm')}  
             ~ ${moment(moment(changeInfo.planEndTime).format('YYYY-MM-DD HH:mm')).format('YYYY-MM-DD HH:mm')}`,
              }}
            >
              {`${moment(changeInfo.planStartTime).format('YYYY-MM-DD HH:mm')}  
             ~ ${moment(moment(changeInfo.planEndTime).format('YYYY-MM-DD HH:mm')).format('YYYY-MM-DD HH:mm')}`}
            </Typography.Text>
          ) : (
            '-- ~ --'
          )}
          {changeInfo?.changeApprovalInfoList?.length &&
          changeInfo.changeApprovalInfoList[0].instStatus === 'APPROVING' ? (
            <Popover
              title="变更延期审批中"
              content={
                <Space style={{ width: 257 }} direction="vertical">
                  <Typography.Text>
                    审批ID：
                    <Link
                      target="_blank"
                      to={generateBPMRoutePath({
                        id: changeInfo.changeApprovalInfoList[0].instId,
                      })}
                    >
                      {changeInfo.changeApprovalInfoList[0].instId}
                    </Link>
                  </Typography.Text>
                  <Typography.Text>
                    原计划结束时间：
                    {moment(changeInfo.changeApprovalInfoList[0]?.extJson?.oldPlanEndDate).format(
                      'YYYY-MM-DD HH:mm'
                    )}
                  </Typography.Text>
                  <Typography.Text>
                    变更延期至：
                    {moment(changeInfo.changeApprovalInfoList[0]?.extJson?.newPlanEndDate).format(
                      'YYYY-MM-DD HH:mm'
                    )}
                  </Typography.Text>
                </Space>
              }
            >
              <Tag color="processing" style={{ marginLeft: 8 }}>
                变更延期审批中
              </Tag>
            </Popover>
          ) : null}
        </>
      ),
    },
    {
      label: '执行时间',
      value: (
        <Typography.Text ellipsis={{ tooltip: true }}>
          {`${changeInfo.realStartTime ? moment(changeInfo.realStartTime).format('YYYY-MM-DD HH:mm') : '--'} ~ ${
            changeInfo.realEndTime
              ? ` ${moment(changeInfo.realEndTime).format('YYYY-MM-DD HH:mm')}`
              : '--'
          }`}
        </Typography.Text>
      ),
    },

    {
      label: '附件',
      value: changeInfo?.fileInfoList?.length ? (
        <SimpleFileList files={changeInfo.fileInfoList}>
          <Button type="link" compact>
            查看
          </Button>
        </SimpleFileList>
      ) : (
        '--'
      ),
    },

    {
      label: '提单人',
      value: <UserLink userName={changeInfo.creatorName} userId={changeInfo.creatorId} />,
    },

    {
      label: '变更负责人',
      value: changeInfo?.changeResponsibleUserList?.length
        ? changeInfo.changeResponsibleUserList.map((item, index) => (
            <>
              <UserLink key={item.value} userName={item.label} userId={item.value} />
              {index + 1 !== changeInfo?.changeResponsibleUserList?.length && ' | '}
            </>
          ))
        : '--',
    },

    {
      label: '当前操作人',
      value: changeInfo?.currentOperatorList?.length
        ? changeInfo.currentOperatorList.map((item, index) => (
            <>
              <UserLink key={item.userId} userName={item.userName} userId={item.userId} />
              {index + 1 !== changeInfo?.currentOperatorList?.length && ' | '}
            </>
          ))
        : '--',
    },
    {
      label: '影响说明',
      value: changeInfo.changeInfluence ? (
        <Typography.Text ellipsis={{ tooltip: changeInfo.changeInfluence }}>
          {changeInfo.changeInfluence}
        </Typography.Text>
      ) : (
        '--'
      ),
    },
    {
      label: '原变更窗口期',
      hidden: !changeInfo?.extJson?.lastPlanStartTime || !changeInfo?.extJson?.lastPlanEndTime,
      value: (
        <>
          <Typography.Text
            ellipsis={{
              tooltip: `${moment(changeInfo?.extJson?.lastPlanStartTime).format('YYYY-MM-DD HH:mm')}  
             ~ ${moment(moment(changeInfo?.extJson?.lastPlanEndTime).format('YYYY-MM-DD HH:mm')).format('YYYY-MM-DD HH:mm')}`,
            }}
          >
            {`${moment(changeInfo?.extJson?.lastPlanStartTime).format('YYYY-MM-DD HH:mm')}  
             ~ ${moment(moment(changeInfo?.extJson?.lastPlanEndTime).format('YYYY-MM-DD HH:mm')).format('YYYY-MM-DD HH:mm')}`}
          </Typography.Text>
        </>
      ),
    },
  ].filter(({ hidden = false }) => !hidden);
  const isStepOwners = () => {
    if (isOwnerUser) {
      return true;
    }
    const stepOwners = [];
    newStepCodes.forEach(item => {
      (newStepMaps[item]?.stepResponsibleUserList ?? []).forEach(user => {
        stepOwners.push(user.value);
      });
    });
    if (stepOwners.some(i => checkUserId(i))) {
      return true;
    }
    return false;
  };

  return (
    <>
      <Badge.Ribbon
        text={CHANGE_RESULT_KEY_TEXT_MAP[step1?.exeResult]}
        color={step1?.exeResult === CHANGE_RESULT_KEY_MAP.Success ? 'green' : 'red'}
        style={{
          display:
            step1?.exeResult === CHANGE_RESULT_KEY_MAP.Success ||
            step1?.exeResult === CHANGE_RESULT_KEY_MAP.Failed
              ? 'flex'
              : 'none',
        }}
      >
        <TinyCard
          bordered={false}
          bodyStyle={{ paddingBottom: 0 }}
          headStyle={{ borderWidth: 0, marginBottom: -24 }}
          title="变更概览"
          extra={
            <Space>
              {[
                ChangeTicketState.WaitingChange,
                ChangeTicketState.Changing,
                ChangeTicketState.InSummary,
                ChangeTicketState.WaitingClose,
              ].includes(step1.changeStatus) && (
                <ChangeNotificationModal deviceGuids={deviceGuids} changeInfo={step1} />
              )}
              {[
                ChangeTicketState.WaitingChange,
                ChangeTicketState.Changing,
                ChangeTicketState.InSummary,
              ].includes(step1.changeStatus) &&
                isYgConfig && (
                  <CreateRelateTicket
                    changeOrderId={step1.changeOrderId}
                    changeTitle={step1.title.value}
                  />
                )}

              {/* 简版变更 */}
              {isStepOwners() && (
                <>
                  {[
                    ChangeTicketState.WaitingChange,
                    ChangeTicketState.Changing,
                    ChangeTicketState.InSummary,
                    ChangeTicketState.SummaryApproving,
                  ].includes(changeInfo.changeStatus) &&
                    showNewDetail && (
                      <ChangeNotificationModal
                        deviceGuids={deviceGuids}
                        changeInfo={changeInfo}
                        showNewDetail={showNewDetail}
                      />
                    )}
                  {[
                    ChangeTicketState.WaitingChange,
                    ChangeTicketState.Changing,
                    ChangeTicketState.InSummary,
                  ].includes(changeInfo.changeStatus) &&
                    showNewDetail && (
                      <CreateRelateTicket
                        changeOrderId={changeInfo.changeOrderId}
                        changeTitle={changeInfo.title}
                        idcTag={changeInfo.idcTag}
                        blockGuid={changeInfo.blockGuid}
                      />
                    )}
                  {[
                    ChangeTicketState.WaitingChange,
                    ChangeTicketState.Changing,
                    ChangeTicketState.InSummary,
                  ].includes(changeInfo.changeStatus) &&
                    showNewDetail && (
                      <RelateChangeSource
                        idcTag={changeInfo.idcTag}
                        blockGuid={changeInfo.blockGuid}
                        changeOrderId={changeInfo.changeOrderId}
                      />
                    )}
                </>
              )}
            </Space>
          }
        >
          <Space direction="vertical" style={{ width: '100%' }}>
            <Descriptions column={4}>
              {(showNewDetail
                ? newChangeItems
                : isYgConfig
                  ? ygItems
                  : step1.changeVersion === 1
                    ? gocItems
                    : gocTestItems
              ).map(({ label, value, span }) => (
                <Descriptions.Item
                  key={label}
                  span={span}
                  label={label}
                  contentStyle={{ overflow: 'hidden', paddingRight: 16 }}
                >
                  {value}
                </Descriptions.Item>
              ))}
            </Descriptions>
            {showNewDetail && newStepCodes.length && Object.keys(newStepMaps).length ? (
              <Card
                title={
                  <Space>
                    <Typography.Text style={{ fontSize: 16, fontWeight: 500 }}>
                      变更步骤
                    </Typography.Text>
                    <Button type="link" onClick={() => setExpandAll(!expandAll)}>
                      全部收起/展开
                    </Button>
                  </Space>
                }
                bordered={false}
                bodyStyle={{ padding: 0 }}
                headStyle={{
                  padding: 0,
                  borderBottomWidth: 0,
                  paddingBottom: 0,
                  marginBottom: -24,
                }}
              >
                <ChangeStep
                  idcTag={changeInfo.idcTag}
                  blockTag={
                    changeInfo.blockGuid
                      ? changeInfo.blockGuid.substring(changeInfo.blockGuid.indexOf('.') + 1)
                      : ''
                  }
                  activeKeyAll={expandAll}
                  stepMaps={newStepMaps}
                  stepCodes={newStepCodes}
                  mode="ticketView"
                  showOperation={changeInfo.changeStatus === 'CHANGING'}
                  changeOrderId={changeInfo.changeOrderId}
                  refetch={() => refetchDetail()}
                  isOwnerUser={isOwnerUser}
                />
              </Card>
            ) : null}
            {isYgConfig && (
              <Card
                title="关键步骤"
                bordered={false}
                bodyStyle={{ padding: 0 }}
                headStyle={{ padding: 0 }}
              >
                <ChangeOfflineCriticalStepTable dataSource={step1.stepList} />
              </Card>
            )}
            {isYgConfig && (
              <Card
                title="变更设备"
                bordered={false}
                bodyStyle={{ padding: 0 }}
                headStyle={{ padding: 0, borderBottomWidth: 0 }}
              >
                <ChangeOfflineDeviceTable
                  changeOrderId={step1.changeOrderId}
                  mode={
                    [ChangeTicketState.WaitingChange, ChangeTicketState.Changing].includes(
                      step1.changeStatus
                    ) &&
                    (authorized || operatorAuthorized)
                      ? 'edit'
                      : 'view'
                  }
                  inhibitionShowText={
                    ![ChangeTicketState.WaitingChange, ChangeTicketState.Changing].includes(
                      step1.changeStatus
                    )
                  }
                  dataSource={step1.changeDeviceList}
                  isExportPdf={isExportPDF}
                  onFetchFinish={() => {
                    setExportPDFTabs(exportPDFTabs => {
                      return exportPDFTabs.map(item => ({
                        ...item,
                        isRenderd: item.key === 'INFO' ? true : item.isRenderd,
                      }));
                    });
                  }}
                />
              </Card>
            )}
            {step1.exeWay === CHANGE_EXE_WAY_MAP.OffLine && (
              <FileList
                title="变更方案"
                files={step1.fileInfoList.value}
                groups={[
                  {
                    title: '文件',
                    fileTypes: ['others', 'pdf'],
                  },
                  {
                    title: '图片',
                    fileTypes: ['image', 'video'],
                    previewable: true,
                    showName: true,
                  },
                ]}
              />
            )}
          </Space>
        </TinyCard>

        {step1.exeWay === CHANGE_EXE_WAY_MAP.OnLine && (
          <TinyCard title="变更方案">
            <StepTableList
              stepCodes={stepCodes}
              stepMaps={stepMaps}
              step={3}
              checkItemsMap={checkItemsMap}
              type="ticketView"
              idcTag={step1.idcTag}
              blockTag={step1.blockTag}
              matchObjectInfoMaps={matchObjectInfoMaps}
              checkItemDeviceMaps={checkItemDeviceMaps}
              removeExceptionHandling
            />
          </TinyCard>
        )}
        {step1.changeStatus === CHANGE_TICKET_STATUS_KEY_MAP.WAITING_CLOSE && !isExportPDF && (
          <FooterToolBar>
            {(((authorized || operatorAuthorized) && step1.exeWay === CHANGE_EXE_WAY_MAP.OffLine) ||
              step1.exeWay === CHANGE_EXE_WAY_MAP.OnLine) && (
              <Button type="primary" onClick={onClose}>
                关闭
              </Button>
            )}
          </FooterToolBar>
        )}
      </Badge.Ribbon>
    </>
  );
}
const mapStateToProps = ({
  change: {
    ticketDetail: {
      changeInfo: {
        deviceGuids,
        step,
        step2: { stepCodes, stepMaps },
        checkItemsMap,
        saveLoading,
        submitLoading,
        step1,
        checkItemDeviceMaps,
        matchObjectInfoMaps,
      },
    },
  },
}) => ({
  step,
  stepCodes,
  stepMaps,
  checkItemsMap,
  saveLoading,
  submitLoading,
  step1,
  checkItemDeviceMaps,
  matchObjectInfoMaps,
  deviceGuids,
});
const mapDispatchToProps = {
  getTicketDetailInfo: getTicketDetailInfo,
  ticketCloseActionCreator: ticketCloseActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(TicketOverviewCard);

const AssociateEvent = ({ changeOrderId }) => {
  const [list, setList] = useState([]);

  useEffect(() => {
    if (changeOrderId) {
      getList();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [changeOrderId]);

  const getList = async () => {
    const { data, error } = await fetchChangeAssociateEvent({ changeId: changeOrderId });
    if (error) {
      return;
    }
    setList(data.data);
  };
  if (!list.length) {
    return '无';
  }
  return (
    <Space size={0} wrap split={<Divider type="vertical" spaceSize="mini" emphasis />}>
      {list?.map(event => (
        <Link
          key={event}
          target="_blank"
          to={generateEventDetailRoutePath({
            id: event,
          })}
        >
          {event}
        </Link>
      ))}
    </Space>
  );
};
