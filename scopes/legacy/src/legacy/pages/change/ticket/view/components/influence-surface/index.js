import React, { Component } from 'react';
import { connect } from 'react-redux';

import { InfluenceSurface } from '@manyun/dc-brain.legacy.components';
import { summerySubmitActionCreator } from '@manyun/dc-brain.legacy.redux/actions/changeActions';

class SummaryRecords extends Component {
  render() {
    const { deviceGuids, idcTag, blockTag } = this.props;
    return (
      <div>
        <InfluenceSurface
          type="budget"
          idcTag={idcTag}
          blockTag={blockTag}
          tabBarStyle={{ borderBottom: 0 }}
          deviceGuids={deviceGuids.join(',')}
        />
      </div>
    );
  }
}

const mapStateToProps = ({
  change: {
    ticketDetail: {
      changeInfo: {
        deviceGuids,
        step1: { idcTag, blockTag },
      },
    },
  },
}) => ({
  deviceGuids,

  idcTag,
  blockTag,
});

const mapDispatchToProps = {
  summerySubmitActionCreator: summerySubmitActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(SummaryRecords);
