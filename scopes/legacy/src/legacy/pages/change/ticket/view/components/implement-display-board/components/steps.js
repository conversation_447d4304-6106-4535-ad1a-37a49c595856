import React from 'react';
import { connect } from 'react-redux';

import styled from 'styled-components';

import { Button } from '@manyun/base-ui.ui.button';
import { Steps } from '@manyun/base-ui.ui.steps';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';

import { GutterWrapper, StatusText } from '@manyun/dc-brain.legacy.components';
import { STATUS_MAP } from '@manyun/dc-brain.legacy.constants/statusColor';
import {
  changeActions,
  getAlarmLevelCount,
  getTicketDetailAlarmList,
} from '@manyun/dc-brain.legacy.redux/actions/changeActions';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';

import { CHANGE_STEP_STATUS_KEY_MAP, CHANGE_STEP_STATUS_TEXT_MAP } from '../../../../../constants';
import OperationBtn from './operation-btn';

export function TicketStep({ stepList, stepCurrent, setTicketViewStepOrder }) {
  const onChange = step => {
    setTicketViewStepOrder(step + 1);
  };

  if (stepList.length === 1) {
    return (
      <GutterWrapper mode="vertical">
        <div style={{ display: 'flex' }}>
          <GutterWrapper mode="horizontal" flex alignItems="center" justifyContent="center">
            <div
              style={{
                width: 32,
                height: 32,
                fontsize: 16,
                lineHeight: '32px',
                textAlign: 'center',
                border: '1px solid ',
                borderRadius: 32,
              }}
            >
              {stepList[0].stepOrder}
            </div>
            <div>{stepList[0].stepName}</div>
            <OperationBtn />
          </GutterWrapper>
        </div>
      </GutterWrapper>
    );
  }
  return (
    <GutterWrapper mode="vertical">
      <Steps style={{ overflowX: 'auto' }} current={stepCurrent} onChange={onChange}>
        {stepList.map(item => (
          <Steps.Step
            key={item.stepOrder}
            title={item.stepName}
            style={{ minWidth: 180 }}
            description={
              <div>
                <GutterWrapper>
                  {item.stepStatus === CHANGE_STEP_STATUS_KEY_MAP.EXCEPTION && (
                    <GutterWrapper flex justifyContent="center">
                      <StatusText status={STATUS_MAP.ALARM}>
                        {CHANGE_STEP_STATUS_TEXT_MAP[item.stepStatus]}
                      </StatusText>

                      {item.exceptionReason && (
                        <Tooltip title={<span>{item.exceptionReason}</span>}>
                          <Button style={{ padding: 0, height: 'auto' }} type="link">
                            原因
                          </Button>
                        </Tooltip>
                      )}
                    </GutterWrapper>
                  )}
                </GutterWrapper>
              </div>
            }
          />
        ))}
      </Steps>
    </GutterWrapper>
  );
}

const mapStateToProps = ({
  change: {
    ticketDetail: {
      step: { stepList, checkItemInfoMaps, opItemInfoMaps, stepCurrent },
    },
  },
  common: { deviceCategory },
}) => ({
  stepList,
  checkItemInfoMaps,
  opItemInfoMaps,
  metaCategoryEntities: deviceCategory ? deviceCategory.normalizedList : {},
});

const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
  getTicketDetailAlarmList: getTicketDetailAlarmList,
  getAlarmLevelCount: getAlarmLevelCount,
  setAlarmLevelCount: changeActions.setAlarmLevelCount,
};
export const StyledTicketStep = styled(TicketStep)`
  .manyun-steps {
    justify-content: center;
  }
`;

export default connect(mapStateToProps, mapDispatchToProps)(TicketStep);
