import React, { useEffect } from 'react';
import { connect } from 'react-redux';

import { Card } from '@manyun/base-ui.ui.card';
import { Space } from '@manyun/base-ui.ui.space';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { ChangeTabs } from '@manyun/ticket.model.change';
import { useChangeContext } from '@manyun/ticket.page.change-offline';

import { changeActions } from '@manyun/dc-brain.legacy.redux/actions/changeActions';

import ExecuteRecords from '../execute-records';
import ImplementDisplayBoard from '../implement-display-board';
import Alarm from '../implement-display-board/alarm-display-board';
import InfluenceSurface from '../influence-surface';
import OperationRecords from '../operation-records/';
import TicketInfos from '../overview-card';
import { RelateTicket } from '../relate-ticket';
import SummaryRecords from '../summary-records';

export function TicketViewTabs({
  match,
  stepCurrent = [],
  isOffLine,
  changeStatus,
  step1,
  setTabCurrent,
  tabCurrent,
  changeInfo,
  newStepMaps,
  newStepCodes,
  refetchDetail,
  isOwnerUser,
  currentTab,
}) {
  const { isExportPDF, exportPDFTabs } = useChangeContext();

  const [configUtil] = useConfigUtil();

  const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket');

  const changesTicketCreateChangeOffline =
    ticketScopeCommonConfigs.changes.features.createChangeOffline;
  const showNewDetail = ticketScopeCommonConfigs.changes.features.showNewDetail;

  useEffect(() => {
    return () => {
      setTabCurrent('ticketInfo');
    };
    //  eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  useEffect(() => {
    if (currentTab && currentTab !== 'undefined') {
      setTabCurrent(currentTab);
    }
    //  eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentTab]);

  const onTabClick = current => {
    setTabCurrent(current);
  };

  const isFull = changesTicketCreateChangeOffline === 'full';
  const getStartTime = () => {
    if (showNewDetail) {
      const time = (newStepCodes?.map(item => newStepMaps[item]?.startTime) ?? []).sort();
      return time?.[0] ?? null;
    }
    return null;
  };
  const getEndTime = () => {
    if (showNewDetail) {
      return changeInfo.realEndTime;
    }
    return null;
  };
  return isExportPDF ? (
    <Space direction="vertical" style={{ width: '100%' }} size={24}>
      <Card bodyStyle={{ padding: '24px 0 0 0' }}>
        {isExportPDF && <Typography.Text style={{ marginLeft: 24 }}>变更信息</Typography.Text>}
        <TicketInfos id={match.params.id} stepCurrent={stepCurrent} />
      </Card>
      {exportPDFTabs
        .filter(item => item.key !== 'INFO')
        .map(item => {
          let cardContnet = null;
          if (item.isRenderd && item.isVolid) {
            return null;
          }
          switch (item.key) {
            case ChangeTabs.ExecuteRecord:
              cardContnet = <ExecuteRecords isOffLine={isOffLine} stepCurrent={stepCurrent} />;
              break;
            case ChangeTabs.SummaryRecord:
              cardContnet = (
                <SummaryRecords
                  isOffLine={isOffLine}
                  id={match.params.id}
                  stepCurrent={stepCurrent}
                />
              );
              break;
            case ChangeTabs.Relate:
              cardContnet = <RelateTicket changeOrderId={match.params.id} />;
              break;
            case ChangeTabs.Alarm:
              cardContnet = <Alarm stepCurrent={stepCurrent} />;
              break;
            default:
              break;
          }
          return (
            <Card key={item.key} bordered={false}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <Typography.Text>{item.title}</Typography.Text>
                {cardContnet}
              </Space>
            </Card>
          );
        })}
    </Space>
  ) : (
    <Card bodyStyle={{ padding: 0 }}>
      <Tabs
        style={{ width: 'auto', borderWidth: 0 }}
        tabBarStyle={{ paddingLeft: 24 }}
        defaultActiveKey="eventInfo"
        destroyInactiveTabPane
        borderWidth={0}
        activeKey={tabCurrent}
        //  产品确认暂时隐藏，以后可能会启用
        // tabBarExtraContent={
        //   changeStatus === ChangeTicketState.Finish && (
        //     <FileExport
        //       filename={`${step1.title.value}.xlsx`}
        //       data={async type => {
        //         const { error, data } = await exportChangeTicket({
        //           changeOrderId: match.params.id,
        //         });
        //         if (error) {
        //           message.error(error.message);
        //           return;
        //         }
        //         return data;
        //       }}
        //     />
        //   )
        // }
        onTabClick={onTabClick}
      >
        <Tabs.TabPane key="ticketInfo" tab="变更信息">
          <TicketInfos
            id={match.params.id}
            stepCurrent={stepCurrent}
            showNewDetail={showNewDetail}
            changeInfo={changeInfo}
            newStepCodes={newStepCodes}
            newStepMaps={newStepMaps}
            refetchDetail={refetchDetail}
            isOwnerUser={isOwnerUser}
          />
        </Tabs.TabPane>
        {isFull && (
          <Tabs.TabPane key="relate" tab="关联事项">
            <RelateTicket changeOrderId={match.params.id} />
          </Tabs.TabPane>
        )}
        {!isOffLine && !showNewDetail && (
          <Tabs.TabPane key="ticketOption" tab="执行看板">
            <ImplementDisplayBoard stepCurrent={stepCurrent} />
          </Tabs.TabPane>
        )}
        {!isOffLine && !showNewDetail && (
          <Tabs.TabPane key="influenceSuface" tab="影响面">
            <InfluenceSurface stepCurrent={stepCurrent} />
          </Tabs.TabPane>
        )}
        {showNewDetail && (
          <Tabs.TabPane key="alarm" tab="监控数据">
            <Alarm
              stepCurrent={stepCurrent}
              newDetailIdcTag={showNewDetail && changeInfo?.idcTag}
              newDetailblockTag={
                showNewDetail &&
                changeInfo.blockGuid &&
                changeInfo.blockGuid.substring(changeInfo.blockGuid.indexOf('.') + 1)
              }
            />
          </Tabs.TabPane>
        )}
        {isOffLine && isFull && (
          <Tabs.TabPane key="alarm" tab="告警看板">
            <Alarm stepCurrent={stepCurrent} />
          </Tabs.TabPane>
        )}
        {!showNewDetail && (
          <Tabs.TabPane key="executeRecords" tab="执行记录">
            <ExecuteRecords isOffLine={isOffLine} stepCurrent={stepCurrent} />
          </Tabs.TabPane>
        )}
        <Tabs.TabPane key="summaryRecords" tab="总结记录">
          <SummaryRecords
            isOffLine={isOffLine}
            id={match.params.id}
            stepCurrent={stepCurrent}
            showNewDetail={showNewDetail}
            startTime={getStartTime()}
            endTime={getEndTime()}
            newChangeStatus={showNewDetail ? changeInfo.changeStatus : null}
            newTitle={showNewDetail ? changeInfo.title : null}
            refetchDetail={refetchDetail}
            newDetailIdcTag={showNewDetail && changeInfo.idcTag}
            newDetailblockGuid={showNewDetail && changeInfo.blockGuid}
          />
        </Tabs.TabPane>
        <Tabs.TabPane key="operationRecords" tab="操作记录">
          <OperationRecords id={match.params.id} stepCurrent={stepCurrent} />
        </Tabs.TabPane>
      </Tabs>
    </Card>
  );
}

const mapStateToProps = ({
  change: {
    ticketDetail: {
      tabCurrent,
      changeInfo: { step1 },
    },
  },
}) => ({
  tabCurrent,
  step1,
});

const mapDispatchToProps = {
  setTabCurrent: changeActions.setTabCurrent,
};

export default connect(mapStateToProps, mapDispatchToProps)(TicketViewTabs);
