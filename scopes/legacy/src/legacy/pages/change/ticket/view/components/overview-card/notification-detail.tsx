import React from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Typography } from '@manyun/base-ui.ui.typography';

export type NotificationDetailProps = {
  reportContent: string;
};

export function NotificationDetail({ reportContent }: NotificationDetailProps) {
  return (
    <Typography.Paragraph
      style={{
        width: '100%',
        minHeight: 200,
        whiteSpace: 'pre-wrap',
        textAlign: 'left',
        overflowY: 'auto',
      }}
      copyable={{
        tooltips: false,
        icon: [
          <Button key="copy" style={{ position: 'absolute', bottom: 8, right: 8 }}>
            复制
          </Button>,
          <Button key="copied" style={{ position: 'absolute', bottom: 8, right: 8 }}>
            已复制
          </Button>,
        ],
      }}
    >
      {reportContent}
    </Typography.Paragraph>
  );
}
