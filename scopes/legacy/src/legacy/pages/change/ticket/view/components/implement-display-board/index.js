import React from 'react';
import { connect } from 'react-redux';

import { Card } from '@manyun/base-ui.ui.card';
import { Empty } from '@manyun/base-ui.ui.empty';
import { Tabs } from '@manyun/base-ui.ui.tabs';

import { CHANGE_TICKET_STATUS_STEP } from '../../../../constants';
import Alarm from './alarm-display-board';
import Step from './step-display-board';

export function StepDetail({ changeStatus, statusInfoList, stepCurrent, props }) {
  return (
    <Card>
      <Tabs destroyInactiveTabPane>
        <Tabs.TabPane tab="步骤看板" key="step">
          {CHANGE_TICKET_STATUS_STEP[changeStatus] > 1 ? (
            <Step stepCurrent={stepCurrent} />
          ) : (
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
          )}
        </Tabs.TabPane>
        <Tabs.TabPane tab="告警看板" key="alarm">
          {CHANGE_TICKET_STATUS_STEP[changeStatus] > 2 ? (
            <Alarm stepCurrent={stepCurrent}></Alarm>
          ) : (
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
          )}
        </Tabs.TabPane>
      </Tabs>
    </Card>
  );
}

const mapStateToProps = ({
  change: {
    ticketDetail: {
      changeInfo: {
        step1: { changeStatus, changeOrderExtJson },
      },
    },
  },
}) => ({
  changeStatus,
  statusInfoList: changeOrderExtJson && changeOrderExtJson.statusInfoList,
});
export default connect(mapStateToProps)(StepDetail);
