import React, { useState } from 'react';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { useCancelChangeOnline } from '@manyun/ticket.gql.client.tickets';

export type CancelButtonProps = {
  changeOrderId: string;
  onSuccess: () => void;
} & Omit<ButtonProps, 'onClick'>;

export function CancelButton({ changeOrderId, onSuccess }: CancelButtonProps) {
  const [visible, setVisible] = useState(false);
  const [form] = Form.useForm();
  const [cancelChangeOnline, { loading }] = useCancelChangeOnline({
    onCompleted(data) {
      if (!data.cancelChangeOnline.success) {
        message.error(data.cancelChangeOnline.message);
        return;
      }
      setVisible(false);
      onSuccess();
    },
  });

  const onSubmit = async () => {
    form.validateFields().then(values => {
      cancelChangeOnline({
        variables: {
          query: {
            changeOrderId,
            cancelReason: values.cancelReason,
          },
        },
      });
    });
  };

  return (
    <>
      <Button
        loading={loading}
        onClick={() => {
          setVisible(true);
        }}
      >
        取消变更
      </Button>
      <Modal
        title="取消变更"
        open={visible}
        okText="确认取消"
        width={640}
        afterClose={() => form.resetFields()}
        onCancel={() => {
          setVisible(false);
        }}
        onOk={onSubmit}
      >
        <Alert
          description="是否取消变更？取消后变更单将变为「已取消」状态，无法继续操作"
          type="warning"
          style={{ marginBottom: 24 }}
          showIcon
        />
        <Form form={form} labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
          <Form.Item
            label="变更取消原因"
            name="cancelReason"
            rules={[
              { required: true, message: '变更取消原因必填！', whitespace: true },
              { max: 300, message: '最多输入 300 个字符！' },
            ]}
          >
            <Input.TextArea rows={3} showCount maxLength={300} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
