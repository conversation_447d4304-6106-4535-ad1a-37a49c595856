import React from 'react';

import { OperationLogTable } from '@manyun/auth-hub.ui.operation-log-table';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';

import { TinyCard } from '@manyun/dc-brain.legacy.components';

import { OperationRecord as OperationRecordTable } from '../../../../components/operation-record-table';

export function OperationRecords({ id }) {
  const [configUtil] = useConfigUtil();

  const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket');

  const showNewDetail = ticketScopeCommonConfigs.changes.features.showNewDetail;
  return (
    <TinyCard>
      {showNewDetail ? (
        <OperationLogTable
          defaultSearchParams={{
            targetId: id,
            targetType: 'CHANGE',
          }}
          showColumns={['serialNumber', 'modifyType', 'targetType']}
          isTargetIdEqual={targetId => {
            return targetId === id;
          }}
        />
      ) : (
        <OperationRecordTable id={id} targetType="CHANGE" />
      )}
    </TinyCard>
  );
}
export default OperationRecords;
