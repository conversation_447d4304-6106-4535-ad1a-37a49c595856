import React, { useState } from 'react';
import { Link } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Dropdown } from '@manyun/base-ui.ui.dropdown';
import type { MenuProps } from '@manyun/base-ui.ui.menu';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { generateChangeOnlineCreateLocation } from '@manyun/ticket.route.ticket-routes';

import { CreateChangeDrawer } from './create-change-drawer';
import { CreateEventDrawer } from './create-event-drawer';
import { CreateRiskDrawer } from './create-risk-drawer';

export type CreateRelateTicketProps = {
  changeOrderId: string;
  changeTitle: string;
  idcTag: string;
  blockGuid?: string | null;
  newTitle?: string | null;
  onSuccess?: () => void;
};

export function CreateRelateTicket({
  changeOrderId,
  changeTitle,
  idcTag,
  blockGuid,
  newTitle,
  onSuccess,
}: CreateRelateTicketProps) {
  const [visibleType, setVisibleType] = useState<'event' | 'risk' | 'change' | null>(null);
  const [configUtil] = useConfigUtil();

  const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket');

  const showNewDetail = ticketScopeCommonConfigs.changes.features.showNewDetail;

  const items: MenuProps['items'] = [
    {
      label: (
        <Button type="text" compact onClick={() => setVisibleType('event')}>
          创建事件
        </Button>
      ),
      key: 'event',
    },
    {
      label: (
        <Button type="text" compact onClick={() => setVisibleType('risk')}>
          创建风险
        </Button>
      ),
      key: 'risk',
    },
    {
      label: showNewDetail ? (
        <Link
          to={generateChangeOnlineCreateLocation({
            changeSourceNo: changeOrderId,
            changeSourceType: 'CHANGE',
            spaceGuid: blockGuid ?? idcTag,
          })}
          target="_blank"
        >
          创建变更
        </Link>
      ) : (
        <Button
          type="text"
          compact
          onClick={() => {
            setVisibleType('change');
          }}
        >
          创建变更
        </Button>
      ),

      key: 'change',
    },
  ];
  return (
    <>
      <Dropdown menu={{ items }} placement="bottomLeft">
        <Button>创建关联工单</Button>
      </Dropdown>
      <CreateRiskDrawer
        changeOrderId={changeOrderId}
        setVisibleType={() => setVisibleType(null)}
        visibleType={visibleType}
        changeTitle={changeTitle ?? newTitle}
        onSuccess={onSuccess}
      />
      <CreateChangeDrawer
        visibleType={visibleType}
        changeOrderId={changeOrderId}
        setVisibleType={() => setVisibleType(null)}
        changeTitle={changeTitle ?? newTitle}
        onSuccess={onSuccess}
      />
      <CreateEventDrawer
        visibleType={visibleType}
        changeOrderId={changeOrderId}
        setVisibleType={() => setVisibleType(null)}
        changeTitle={changeTitle ?? newTitle}
        onSuccess={onSuccess}
      />
    </>
  );
}
