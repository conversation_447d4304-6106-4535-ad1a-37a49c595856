/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-12-18
 *
 * @packageDocumentation
 */
import moment from 'moment';
import React, { useEffect, useMemo } from 'react';
import { Link } from 'react-router-dom';

import { UserLink } from '@manyun/auth-hub.ui.user-link';
import type { ColumnType } from '@manyun/base-ui.ui.table';
import { Table } from '@manyun/base-ui.ui.table';
import { useChangeOfflineRelateTickets } from '@manyun/ticket.gql.client.tickets';
import type { ChangeOfflineRelateTicket } from '@manyun/ticket.gql.client.tickets';
import { ChangeTabs, getChangeLocales } from '@manyun/ticket.model.change';
import type { SourceType } from '@manyun/ticket.model.change';
import { useChangeContext } from '@manyun/ticket.page.change-offline';
import {
  generateChangeOfflineLocation,
  generateChangeTicketDetail,
  generateEventDetailRoutePath,
  generateRiskRegisterDetailLocation,
} from '@manyun/ticket.route.ticket-routes';

export type ChangeOfflineDeviceTableProps = {
  changeOrderId: string;
};

export function RelateTicket({ changeOrderId }: ChangeOfflineDeviceTableProps) {
  const { isExportPDF, setExportPDFTabs } = useChangeContext();

  const [getChangeOfflineRelateTickets, { data, loading }] = useChangeOfflineRelateTickets({
    onCompleted: res => {
      if (isExportPDF) {
        setExportPDFTabs(exportPDFTabs => {
          return exportPDFTabs.map(item => ({
            ...item,
            isVolid:
              item.key === ChangeTabs.Relate
                ? !res?.changeOfflineRelateTickets?.data?.length
                : item.isVolid,
            isRenderd: item.key === ChangeTabs.Relate ? true : item.isRenderd,
          }));
        });
      }
    },
  });
  const locales = useMemo(() => getChangeLocales(), []);

  useEffect(() => {
    changeOrderId && getChangeOfflineRelateTickets({ variables: { changeOrderId } });
  }, [changeOrderId, getChangeOfflineRelateTickets]);

  const columns: Array<ColumnType<ChangeOfflineRelateTicket>> = useMemo(() => {
    const defaultList: Array<ColumnType<ChangeOfflineRelateTicket>> = [
      {
        title: '类型',
        dataIndex: 'relateType',
        width: 150,
        render: (_, { relateType }) => locales.changeSource.source[relateType as SourceType],
      },
      {
        title: 'ID',
        dataIndex: 'relateNo',
        width: 200,
        render: (_, { relateNo, relateType }) => <IdLinkText type={relateType} id={relateNo} />,
      },
      {
        title: '事项描述',
        dataIndex: 'desc',
        ellipsis: !isExportPDF && {
          showTitle: !isExportPDF,
        },
      },
      {
        title: '状态',
        dataIndex: 'status',
        width: 150,
      },
      {
        title: '创建人',
        dataIndex: 'creatorId',
        width: 150,
        render: (_, { creatorId }) => <UserLink userId={creatorId} />,
      },
      {
        title: '创建时间',
        dataIndex: 'gmtCreate',
        width: 300,
        sorter: (a, b) => a.gmtCreate - b.gmtCreate,
        render: (_, { gmtCreate }) => moment(gmtCreate).format('YYYY-MM-DD HH:mm:ss'),
      },
    ];

    return defaultList;
  }, [locales.changeSource.source, isExportPDF]);

  return (
    <Table<ChangeOfflineRelateTicket>
      rowKey="relateNo"
      dataSource={data?.changeOfflineRelateTickets?.data ?? []}
      columns={columns}
      loading={loading}
      pagination={
        isExportPDF
          ? false
          : {
              total: data?.changeOfflineRelateTickets?.total,
            }
      }
    />
  );
}

export function IdLinkText({ type, id }: { type: string; id: string }): JSX.Element | string {
  switch (type) {
    case 'EVENT': {
      return (
        <Link
          to={generateEventDetailRoutePath({
            id: id as string,
          })}
        >
          {id}
        </Link>
      );
    }

    // 风险登记册
    case 'RISK': {
      return <Link to={generateRiskRegisterDetailLocation({ id: id })}>{id}</Link>;
    }
    // 变更
    case 'CHANGE': {
      if (id.startsWith('N')) {
        return (
          <Link
            target="_blank"
            to={generateChangeOfflineLocation({
              id,
            })}
          >
            {id}
          </Link>
        );
      }
      return <Link to={generateChangeTicketDetail({ id: id })}>{id}</Link>;
    }

    default:
      return '--';
  }
}
