import Form from '@ant-design/compatible/es/form';
import { UploadOutlined } from '@ant-design/icons';
import moment from 'moment';
import React, { Component, useCallback, useEffect, useState } from 'react';
import { connect, useSelector } from 'react-redux';
import { useLatest } from 'react-use';

import { getUserInfo } from '@manyun/auth-hub.cache.user';
import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { User } from '@manyun/auth-hub.ui.user';
import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Form as BaseForm } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { BpmInstanceForm } from '@manyun/bpm.ui.bpm-instance-form';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { McUpload } from '@manyun/dc-brain.ui.upload';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import {
  useChangeOfflineRelateTickets,
  useSubmitChangeOnlineSummery,
} from '@manyun/ticket.gql.client.tickets';
import {
  CHANGE_EXE_WAY_MAP,
  CHANGE_ONLINE_RESULT_KEY_TEXT_MAP,
  CHANGE_RESULT_KEY_TEXT_MAP,
  ChangeTabs,
} from '@manyun/ticket.model.change';
import { useChangeContext } from '@manyun/ticket.page.change-offline';
import { ChangeSourceText } from '@manyun/ticket.ui.change-source-text';

import {
  FooterToolBar,
  GutterWrapper,
  TinyCard,
  TinyUpload,
  UserLink,
} from '@manyun/dc-brain.legacy.components';
import { summerySubmitActionCreator } from '@manyun/dc-brain.legacy.redux/actions/changeActions';
import { changeService } from '@manyun/dc-brain.legacy.services';

import { CHANGE_TICKET_STATUS_KEY_MAP, CHANGE_TICKET_STATUS_STEP } from '../../../../constants';
import { CreateRelateTicket } from '../overview-card/create-relate-ticket';

class SummaryRecordView extends Component {
  state = {
    fileInfoList: [],
    summarizePersonName: '',
    summarizePersonId: null,
    summery: null,
    exeResult: '',
    failReason: '',
    changeBaseInfo: '',
    diffReason: '',
    problem: '',
    improveMethod: '',
    remark: '',
    loading: false,
  };

  componentDidMount() {
    const { changeStatus, isExportPDF, setExportPDFTabs, showNewDetail, newChangeStatus } =
      this.props;
    const status = showNewDetail ? newChangeStatus : changeStatus;
    const statusNumber = CHANGE_TICKET_STATUS_STEP[status];

    if (statusNumber >= 4) {
      this.getSummeryInfo();
    } else {
      if (isExportPDF) {
        setExportPDFTabs(exportPDFTabs => {
          return exportPDFTabs.map(item => ({
            ...item,
            isVolid: item.key === ChangeTabs.SummaryRecord ? true : item.isVolid,
            isRenderd: item.key === ChangeTabs.SummaryRecord ? true : item.isRenderd,
          }));
        });
      }
    }
  }

  getSummeryInfo = async () => {
    const { id, changesTicketCreateChangeOffline, isExportPDF, setExportPDFTabs, showNewDetail } =
      this.props;
    const { response, error } = await changeService.getSummeryInfo({
      changeOrderId: id,
    });
    if (isExportPDF) {
      setExportPDFTabs(exportPDFTabs => {
        return exportPDFTabs.map(item => ({
          ...item,
          isVolid: item.key === ChangeTabs.SummaryRecord ? false : item.isVolid,
          isRenderd: item.key === ChangeTabs.SummaryRecord ? true : item.isRenderd,
        }));
      });
    }
    if (error) {
      message.error(error);
    } else {
      if (
        (response?.exeResult === 'FAILED' && changesTicketCreateChangeOffline === 'full') ||
        showNewDetail
      ) {
        this.props.getRelateList();
      }
      let summery = {};
      if (this.props.changeVersion === 2) {
        try {
          summery = JSON.parse(response.summery);
        } catch (err) {
          console.error(err);
        }
      }
      this.setState({
        ...response,
        ...summery,
        fileInfoList: showNewDetail
          ? response.fileInfoList?.map(obj => McUploadFile.fromApiObject(obj))
          : response.fileInfoList,
      });
    }
  };

  _attchmentsChangeHandler = fileInfoList => {
    this.setState({ fileInfoList });
  };

  onSave = () => {
    const { id, form, baseForm, showNewDetail, submitChangeOnlineSummery, refetchDetail } =
      this.props;
    form.validateFields((errs, values) => {
      if (errs) {
        return;
      }
      const processCustomUserInfoList = baseForm.getFieldValue('processNodeInfoList');
      const processVersion = baseForm.getFieldValue('processVersion');
      new Promise(async () => {
        const { changeBaseInfo, diffReason, problem, improveMethod, remark, ...resp } = values;
        let summery = values.summery;
        if (this.props.changeVersion === 2) {
          summery = JSON.stringify({
            changeBaseInfo,
            diffReason,
            problem,
            improveMethod,
            remark,
          });
        }
        if (showNewDetail) {
          const { data } = await submitChangeOnlineSummery({
            variables: {
              query: {
                ...resp,
                changeOrderId: id,
                summery,
                realStartTime: resp.realStartTime.valueOf(),
                realEndTime: resp.realEndTime.valueOf(),
                fileInfoList: this.state.fileInfoList
                  ? this.state.fileInfoList.map(obj => McUploadFile.fromJSON(obj).toJSON())
                  : [],
              },
            },
          });
          if (!data.submitChangeOnlineSummery.success) {
            message.error(data.submitChangeOnlineSummery?.message);
            return;
          }
          message.success('提交成功');
          this.getSummeryInfo();
          refetchDetail();
          return;
        }
        this.props.summerySubmitActionCreator({
          params: {
            ...resp,
            changeOrderId: id,
            summery,
            fileInfoList: this.state.fileInfoList,
            processVersion,
            processCustomUserInfoList: Array.isArray(processCustomUserInfoList)
              ? processCustomUserInfoList
                  .filter(info => info.edit && (info.userIdList ?? []).length !== 0)
                  .map(({ id, name, userIdList }) => ({
                    id,
                    name,
                    userIdList,
                  }))
              : undefined,
          },
          successCallback: () => {
            message.success('提交成功');
            this.getSummeryInfo();
          },
        });
      });
    });
  };

  range = (start, end) => {
    const result = [];
    for (let i = start; i < end; i++) {
      result.push(i);
    }
    return result;
  };

  endTimeDisabledDateTime = (current, disabledTime) => {
    const time = disabledTime;
    const hours = time ? time.hours() : 0;
    const minute = time ? time.minute() : 0;
    const nowHours = moment().hours();
    const nowMinute = moment().minute();
    const currentHours = moment(current).hours();
    const choseHour = moment(current).hours();
    const isStartToday = time ? disabledTime.isSame(moment(current), 'day') : 0;
    const isToday = moment(current).isSame(moment(), 'day');
    if (isStartToday && isToday) {
      const disabledHours = [...this.range(0, hours), ...this.range(nowHours + 1, 24)];
      if (choseHour === hours) {
        return {
          disabledHours: () => disabledHours,
          disabledMinutes: () => this.range(0, minute),
        };
      }
      if (nowHours === choseHour) {
        return {
          disabledHours: () => disabledHours,
          disabledMinutes: () => this.range(nowMinute + 1, 60),
        };
      }
      return {
        disabledHours: () => disabledHours,
      };
    }
    if (isToday) {
      if (choseHour === nowHours) {
        return {
          disabledHours: () => this.range(nowHours + 1, 24),
          disabledMinutes: () => this.range(nowMinute + 1, 60),
        };
      }
      return {
        disabledHours: () => this.range(nowHours + 1, 24),
      };
    }
    if (isStartToday) {
      if (currentHours === hours) {
        return {
          disabledHours: () => this.range(0, hours),
          disabledMinutes: () => this.range(0, minute),
        };
      }
      return {
        disabledHours: () => this.range(0, hours),
      };
    }
    return;
  };

  startTimeDisabledDateTime = (current, disabledTime) => {
    if (!disabledTime) {
      return;
    }
    const { realEndTime } = this.props;
    const time = disabledTime;
    const hours = time.hours();
    const minute = time.minute();
    const second = time.second();
    const realHours = moment(realEndTime).hours();
    const realMinute = moment(realEndTime).minute();
    const currentHours = moment(current).hours();
    const choseHour = moment(current).hours();
    const isStartToday = disabledTime.isSame(moment(current), 'day');
    const isRealEndTime = moment(current).isSame(moment(realEndTime), 'day');
    if (isRealEndTime) {
      if (choseHour === realHours) {
        return {
          disabledHours: () => this.range(realHours + 1, 24),
          disabledMinutes: () => this.range(realMinute + 1, 60),
        };
      }
      return {
        disabledHours: () => this.range(realHours + 1, 24),
      };
    }
    if (isStartToday) {
      if (currentHours > hours) {
        return {
          disabledHours: () => this.range(0, hours),
        };
      }
      return {
        disabledHours: () => this.range(0, hours),
        disabledMinutes: () => this.range(0, minute),
        disabledSeconds: () => this.range(0, second),
      };
    }
    return;
  };

  endTimeDisabledDate = (current, disabledTime) => {
    return (
      current &&
      (current < moment(disabledTime).subtract(1, 'days').hour(23).minute(59).second(59) ||
        current.valueOf() > moment(this.props.realEndTime).endOf('day').valueOf())
    );
  };

  getNewDisabledTime = (current, startime) => {
    if (!startime) {
      return;
    }
    const compareMoment = moment(startime);
    const hours = compareMoment.hours();
    const minute = compareMoment.minute();
    if (current) {
      const choseHour = current.hours();

      const isToday = current.isSame(compareMoment, 'day');
      if (isToday) {
        if (choseHour === hours) {
          return {
            disabledHours: () => this.range(0, hours),
            disabledMinutes: () => this.range(0, minute),
            disabledSeconds: () => [],
          };
        }
        return {
          disabledHours: () => this.range(0, hours),
          disabledMinutes: () => [],
          disabledSeconds: () => [],
        };
      }
    }

    return {
      disabledHours: () => [],
      disabledMinutes: () => [],
      disabledSeconds: () => [],
    };
  };

  render() {
    const {
      fileInfoList,
      summarizePersonName,
      summarizePersonId,
      summery,
      exeResult,
      realEndTime,
      realStartTime,
      failReason,
      changeBaseInfo,
      diffReason,
      problem,
      improveMethod,
      remark,
    } = this.state;
    const {
      changeStatus,
      form,
      creatorId,
      exeWay,
      isOffLine,
      planStartTime,
      realEndTime: initialrealEndTime,
      operatorId,
      relateList,
      getRelateList,
      changeOrderId,
      title,
      changesTicketCreateChangeOffline,
      isExportPDF,
      changeVersion,
      baseForm,
      idcTag,
      blockGuid,
      riskLevel,
      reason,
      changeType,
      changeRespPerson,
      showNewDetail,
      startTime,
      endTime,
      newChangeStatus,
      id,
      newDetailblockGuid,
      newDetailIdcTag,
      newTitle,
    } = this.props;
    const status = showNewDetail ? newChangeStatus : changeStatus;
    const { getFieldDecorator, getFieldValue, setFieldsValue } = form;
    const inSummary = status === CHANGE_TICKET_STATUS_KEY_MAP.IN_SUMMARY;
    const statusNumber = CHANGE_TICKET_STATUS_STEP[status];
    const isFull = changesTicketCreateChangeOffline === 'full';
    if (showNewDetail) {
      return (
        <TinyCard>
          <Form {...formItemLayout}>
            <Form.Item label="执行变更开始时间">
              {getFieldDecorator('realStartTime', {
                rules: [
                  {
                    required: true,
                    message: '执行变更开始时间必选！',
                  },
                  // {
                  //   validator(_, value, callback) {
                  //     if (
                  //       value &&
                  //       initialrealEndTime &&
                  //       moment(value).diff(moment(initialrealEndTime), 'minutes') > 0
                  //     ) {
                  //       callback('执行变更开始时间不可大于执行变更结束时间');
                  //     }
                  //     if (value && planStartTime.value.diff(moment(value), 'minutes') > 0) {
                  //       callback('执行变更开始时间不可早于计划变更开始时间');
                  //     }
                  //     return callback();
                  //   },
                  // },
                ],
                initialValue: startTime && moment(startTime),
              })(
                statusNumber === 4 ? (
                  <DatePicker
                    style={{ width: 385 }}
                    allowClear
                    format="YYYY-MM-DD HH:mm"
                    showTime
                    showNow={false}
                  />
                ) : (
                  <div style={{ wordBreak: 'break-word' }}>
                    {realStartTime ? moment(realStartTime).format('YYYY-MM-DD HH:mm') : '--'}
                  </div>
                )
              )}
            </Form.Item>

            <Form.Item label="执行变更结束时间">
              {getFieldDecorator('realEndTime', {
                rules: [
                  {
                    required: true,
                    message: '执行变更结束时间必选！',
                  },
                  {
                    validator(_, value, callback) {
                      // if (value && moment(value).diff(moment(), 'minutes') > 0) {
                      //   callback('执行变更结束时间不可大于当前时间');
                      // }
                      if (
                        value &&
                        moment(getFieldValue('realStartTime')).diff(moment(value), 'minutes') > 0
                      ) {
                        callback('执行变更结束时间不可早于执行变更开始时间');
                      }
                      return callback();
                    },
                  },
                ],
                initialValue: endTime && moment(endTime),
              })(
                statusNumber === 4 ? (
                  <DatePicker
                    format="YYYY-MM-DD HH:mm"
                    showTime
                    style={{ width: 385 }}
                    allowClear
                    showNow={false}
                    // disabled={!(endTime || getFieldValue('realStartTime'))}
                    disabledTime={current =>
                      this.getNewDisabledTime(current, getFieldValue('realStartTime'))
                    }
                    disabledDate={current =>
                      current &&
                      current <
                        moment(getFieldValue('realStartTime'))
                          .subtract(1, 'days')
                          .hour(23)
                          .minute(59)
                          .second(59)
                    }
                  />
                ) : (
                  <div style={{ wordBreak: 'break-word' }}>
                    {realEndTime ? moment(realEndTime).format('YYYY-MM-DD HH:mm') : '--'}
                  </div>
                )
              )}
            </Form.Item>

            <Form.Item label="变更结果">
              {getFieldDecorator('exeResult', {
                rules: [
                  {
                    required: true,
                    message: '变更结果必选！',
                  },
                ],
                initialValue: exeResult ? exeResult : 'SUCCESS',
              })(
                statusNumber === 4 ? (
                  <Radio.Group>
                    {Object.entries(CHANGE_ONLINE_RESULT_KEY_TEXT_MAP).map((item, index) => (
                      <Radio key={item[0]} value={item[0]}>
                        {item[1]}
                        {/* {index === 1 &&
                          changeVersion === 2 &&
                          getFieldValue('exeResult') === 'FAILED' && (
                            <Typography.Text type="danger" style={{ marginLeft: 8 }}>
                              下次重新申请此变更时，请提升变更等级
                            </Typography.Text>
                          )} */}
                      </Radio>
                    ))}
                  </Radio.Group>
                ) : (
                  <div style={{ wordBreak: 'break-word' }}>
                    {CHANGE_ONLINE_RESULT_KEY_TEXT_MAP[exeResult]}
                  </div>
                )
              )}
            </Form.Item>

            <Form.Item label="关联事项">
              <Space>
                <Space split={<Divider type="vertical" spaceSize="mini" />} wrap>
                  {relateList?.map(item => (
                    <ChangeSourceText
                      key={item.relateNo}
                      sourceNo={item.relateNo}
                      sourceType={item.relateType}
                    />
                  ))}
                </Space>
                {statusNumber === 4 && (
                  <CreateRelateTicket
                    changeOrderId={id}
                    changeTitle={title.value}
                    idcTag={newDetailIdcTag}
                    blockGuid={newDetailblockGuid}
                    newTitle={newTitle}
                    onSuccess={() => getRelateList()}
                  />
                )}
              </Space>
            </Form.Item>

            {getFieldValue('exeResult') === 'FAILED' && isFull && (
              <Form.Item label="失败原因">
                {getFieldDecorator('failReason', {
                  rules: [
                    {
                      required: true,
                      message: '失败原因必填！',
                      whitespace: true,
                    },
                    {
                      max: 300,
                      message: '最多输入 300 个字符！',
                    },
                  ],
                  initialValue: failReason,
                })(
                  statusNumber === 4 ? (
                    <Input style={{ width: 385 }} />
                  ) : (
                    <div style={{ wordBreak: 'break-word' }}>{failReason}</div>
                  )
                )}
              </Form.Item>
            )}

            <Form.Item label="总结内容">
              {getFieldDecorator('summery', {
                rules: [
                  {
                    required: true,
                    message: '总结内容必填！',
                    whitespace: true,
                  },
                  {
                    max: 500,
                    message: '最多输入 500 个字符！',
                  },
                ],
                initialValue: summery,
              })(
                statusNumber === 4 ? (
                  <Input.TextArea style={{ width: 385 }} rows={10} />
                ) : (
                  <div style={{ wordBreak: 'break-word' }}>{summery}</div>
                )
              )}
            </Form.Item>

            <Form.Item label={inSummary ? '附件上传' : '附件'}>
              <div style={{ width: 420, marginTop: inSummary ? 0 : 8 }}>
                <McUpload
                  showAccept
                  accept={inSummary ? '.zip,.rar,.jpg,.jpeg,.png,.doc,.docx,.xls,.xlsx,.pdf' : ''}
                  showUploadList
                  maxCount={5}
                  fileList={this.state.fileInfoList}
                  maxFileSize={20}
                  allowDelete={inSummary}
                  openFileDialogOnClick={
                    fileInfoList === undefined || fileInfoList?.length < 5 || !inSummary
                  }
                  onChange={({ fileList, ...r }) => {
                    if (fileList.filter(file => file.status === 'uploading').length) {
                      this.setState({ loading: true });
                    } else {
                      this.setState({ loading: false });
                    }
                    this.setState({ fileInfoList: fileList });
                  }}
                >
                  {inSummary && (
                    <Button
                      icon={<UploadOutlined />}
                      disabled={fileInfoList?.length >= 5 || !inSummary}
                    >
                      点此上传
                    </Button>
                  )}
                </McUpload>
              </div>
            </Form.Item>
          </Form>
          {inSummary && !isExportPDF && (
            <FooterToolBar>
              <GutterWrapper>
                <Button type="primary" loading={this.state.loading} onClick={this.onSave}>
                  提交
                </Button>
                {/* <Button>取消</Button> */}
              </GutterWrapper>
            </FooterToolBar>
          )}
        </TinyCard>
      );
    }
    return (
      <TinyCard>
        <Form {...formItemLayout}>
          {!isOffLine && (
            <Form.Item label="总结人">
              {statusNumber > 4 && (
                <UserLink userName={summarizePersonName} userId={summarizePersonId} />
              )}
              {statusNumber === 4 && (
                <User.Link id={getUserInfo().userId} name={getUserInfo().name} />
              )}
              {statusNumber < 4 && <span>--</span>}
            </Form.Item>
          )}
          {isOffLine && (
            <Form.Item label="执行变更开始时间">
              {getFieldDecorator('realStartTime', {
                rules: [
                  {
                    required: true,
                    message: '执行变更开始时间必选！',
                  },
                  {
                    validator(_, value, callback) {
                      if (
                        value &&
                        initialrealEndTime &&
                        moment(value).diff(moment(initialrealEndTime), 'minutes') > 0
                      ) {
                        callback('执行变更开始时间不可大于执行变更结束时间');
                      }
                      if (value && planStartTime.value.diff(moment(value), 'minutes') > 0) {
                        callback('执行变更开始时间不可早于计划变更开始时间');
                      }
                      return callback();
                    },
                  },
                ],
                initialValue: realStartTime && moment(realStartTime),
              })(
                statusNumber === 4 ? (
                  <DatePicker
                    disabledTime={current =>
                      this.startTimeDisabledDateTime(current, moment(planStartTime.value))
                    }
                    disabledDate={current => this.endTimeDisabledDate(current, planStartTime.value)}
                    style={{ width: 385 }}
                    allowClear
                    format="YYYY-MM-DD HH:mm"
                    showTime
                    showNow={false}
                    onChange={() => {
                      if (initialrealEndTime) {
                        return;
                      }
                      setFieldsValue({ realEndTime: undefined });
                    }}
                  />
                ) : (
                  <div style={{ wordBreak: 'break-word' }}>
                    {realStartTime ? moment(realStartTime).format('YYYY-MM-DD HH:mm') : '--'}
                  </div>
                )
              )}
            </Form.Item>
          )}
          {isOffLine && (
            <Form.Item label="执行变更结束时间">
              {getFieldDecorator('realEndTime', {
                rules: [
                  {
                    required: true,
                    message: '执行变更结束时间必选！',
                  },
                  {
                    validator(_, value, callback) {
                      if (value && moment(value).diff(moment(), 'minutes') > 0) {
                        callback('执行变更结束时间不可大于当前时间');
                      }
                      if (
                        value &&
                        moment(getFieldValue('realStartTime')).diff(moment(value), 'minutes') > 0
                      ) {
                        callback('执行变更结束时间不可早于执行变更开始时间');
                      }
                      return callback();
                    },
                  },
                ],
                initialValue: initialrealEndTime && moment(initialrealEndTime),
              })(
                statusNumber === 4 ? (
                  <DatePicker
                    format="YYYY-MM-DD HH:mm"
                    showTime
                    style={{ width: 385 }}
                    allowClear
                    showNow={false}
                    disabled={!(initialrealEndTime || getFieldValue('realStartTime'))}
                    disabledTime={current =>
                      this.endTimeDisabledDateTime(current, getFieldValue('realStartTime'))
                    }
                    disabledDate={current =>
                      current &&
                      (current <
                        moment(getFieldValue('realStartTime'))
                          .subtract(1, 'days')
                          .hour(23)
                          .minute(59)
                          .second(59) ||
                        current.valueOf() > moment().endOf('day').valueOf())
                    }
                  />
                ) : (
                  <div style={{ wordBreak: 'break-word' }}>
                    {realEndTime ? moment(realEndTime).format('YYYY-MM-DD HH:mm') : '--'}
                  </div>
                )
              )}
            </Form.Item>
          )}
          {isOffLine && (
            <Form.Item label="变更执行结果">
              {getFieldDecorator('exeResult', {
                rules: [
                  {
                    required: true,
                    message: '变更执行结果必选！',
                  },
                ],
                initialValue: exeResult,
              })(
                statusNumber === 4 ? (
                  <Radio.Group
                    onChange={value => {
                      if (
                        value.target.value === 'FAILED' &&
                        changesTicketCreateChangeOffline === 'full'
                      ) {
                        getRelateList();
                      }
                    }}
                  >
                    {Object.entries(CHANGE_RESULT_KEY_TEXT_MAP).map((item, index) => (
                      <Radio key={item[0]} value={item[0]}>
                        {item[1]}
                        {index === 1 &&
                          changeVersion === 2 &&
                          getFieldValue('exeResult') === 'FAILED' && (
                            <Typography.Text type="danger" style={{ marginLeft: 8 }}>
                              下次重新申请此变更时，请提升变更等级
                            </Typography.Text>
                          )}
                      </Radio>
                    ))}
                  </Radio.Group>
                ) : (
                  <div style={{ wordBreak: 'break-word' }}>
                    {CHANGE_RESULT_KEY_TEXT_MAP[exeResult]}
                  </div>
                )
              )}
            </Form.Item>
          )}
          {getFieldValue('exeResult') === 'FAILED' && isFull && (
            <Form.Item label="关联事项">
              <Space>
                <Space split={<Divider type="vertical" spaceSize="mini" />} wrap>
                  {relateList?.map(item => (
                    <ChangeSourceText
                      key={item.relateNo}
                      sourceNo={item.relateNo}
                      sourceType={item.relateType}
                    />
                  ))}
                </Space>
                {statusNumber === 4 && (
                  <CreateRelateTicket
                    changeOrderId={changeOrderId}
                    changeTitle={title.value}
                    onSuccess={() => getRelateList()}
                  />
                )}
              </Space>
            </Form.Item>
          )}
          {getFieldValue('exeResult') === 'FAILED' && isFull && (
            <Form.Item label="失败原因">
              {getFieldDecorator('failReason', {
                rules: [
                  {
                    required: true,
                    message: '失败原因必填！',
                    whitespace: true,
                  },
                  {
                    max: 300,
                    message: '最多输入 300 个字符！',
                  },
                ],
                initialValue: failReason,
              })(
                statusNumber === 4 ? (
                  <Input style={{ width: 385 }} />
                ) : (
                  <div style={{ wordBreak: 'break-word' }}>{failReason}</div>
                )
              )}
            </Form.Item>
          )}
          {changeVersion === 1 && (
            <Form.Item label="总结内容">
              {getFieldDecorator('summery', {
                rules: [
                  {
                    required: true,
                    message: '总结内容必填！',
                    whitespace: true,
                  },
                  {
                    max: 1000,
                    message: '最多输入 1000 个字符！',
                  },
                ],
                initialValue: summery,
              })(
                statusNumber === 4 ? (
                  <Input.TextArea style={{ width: 385 }} rows={10} />
                ) : (
                  <div style={{ wordBreak: 'break-word' }}>{summery}</div>
                )
              )}
            </Form.Item>
          )}
          {changeVersion === 2 && (
            <>
              <Form.Item label="变更基本信息">
                {getFieldDecorator('changeBaseInfo', {
                  rules: [
                    {
                      required: true,
                      message: '变更基本信息必填！',
                      whitespace: true,
                    },
                    {
                      max: 300,
                      message: '最多输入 300 个字符！',
                    },
                  ],
                  initialValue: changeBaseInfo,
                })(
                  statusNumber === 4 ? (
                    <Input.TextArea rows={2} style={{ width: 520 }} />
                  ) : (
                    <div style={{ wordBreak: 'break-word' }}>{changeBaseInfo}</div>
                  )
                )}
              </Form.Item>
              <Form.Item label="变更方案与实际操作的差异与原因">
                {getFieldDecorator('diffReason', {
                  rules: [
                    {
                      required: true,
                      message: '变更方案与实际操作的差异与原因必填！',
                      whitespace: true,
                    },
                    {
                      max: 300,
                      message: '最多输入 300 个字符！',
                    },
                  ],
                  initialValue: diffReason,
                })(
                  statusNumber === 4 ? (
                    <Input.TextArea rows={2} style={{ width: 520 }} />
                  ) : (
                    <div style={{ wordBreak: 'break-word' }}>{diffReason}</div>
                  )
                )}
              </Form.Item>
              <Form.Item label="变更发现的问题">
                {getFieldDecorator('problem', {
                  rules: [
                    {
                      required: true,
                      message: '变更发现的问题必填！',
                      whitespace: true,
                    },
                    {
                      max: 300,
                      message: '最多输入 300 个字符！',
                    },
                  ],
                  initialValue: problem,
                })(
                  statusNumber === 4 ? (
                    <Input.TextArea rows={2} style={{ width: 520 }} />
                  ) : (
                    <div style={{ wordBreak: 'break-word' }}>{problem}</div>
                  )
                )}
              </Form.Item>
              <Form.Item label="同类变更改进措施">
                {getFieldDecorator('improveMethod', {
                  rules: [
                    {
                      required: true,
                      message: '同类变更改进措施必填！',
                      whitespace: true,
                    },
                    {
                      max: 300,
                      message: '最多输入 300 个字符！',
                    },
                  ],
                  initialValue: improveMethod,
                })(
                  statusNumber === 4 ? (
                    <Input.TextArea rows={2} style={{ width: 520 }} />
                  ) : (
                    <div style={{ wordBreak: 'break-word' }}>{improveMethod}</div>
                  )
                )}
              </Form.Item>
              <Form.Item label="备注">
                {getFieldDecorator('remark', {
                  initialValue: remark,
                  rules: [
                    {
                      max: 300,
                      message: '最多输入 300 个字符！',
                    },
                  ],
                })(
                  statusNumber === 4 ? (
                    <Input.TextArea rows={2} style={{ width: 520 }} />
                  ) : (
                    <div style={{ wordBreak: 'break-word' }}>{remark}</div>
                  )
                )}
              </Form.Item>
            </>
          )}
          <Form.Item label={inSummary ? '附件上传' : '附件'}>
            <div style={{ width: 385 }}>
              <TinyUpload
                showUploadBtn={inSummary}
                allowDelete={inSummary}
                fileList={fileInfoList}
                onChange={this._attchmentsChangeHandler}
              />
            </div>
          </Form.Item>
          {changeVersion === 2 &&
            statusNumber === 4 &&
            idcTag &&
            blockGuid &&
            riskLevel &&
            reason &&
            changeType &&
            changeRespPerson && (
              <BpmInstanceForm
                outerForm={baseForm}
                idcTag={idcTag}
                blockGuid={blockGuid}
                riskLevel={riskLevel}
                reason={reason}
                changeType={changeType}
                changeRespPerson={changeRespPerson}
                processType="CHANGE_SUMMERY"
                {...formItemLayout}
              />
            )}
        </Form>
        {inSummary && !isExportPDF && (
          <FooterToolBar>
            <GutterWrapper>
              <Submit
                creatorId={creatorId}
                exeWay={exeWay}
                operatorId={operatorId}
                changeVersion={changeVersion}
                form={baseForm}
                onSave={this.onSave}
              />
              {/* <Button>取消</Button> */}
            </GutterWrapper>
          </FooterToolBar>
        )}
      </TinyCard>
    );
  }
}

const mapStateToProps = ({
  change: {
    ticketDetail: {
      changeInfo: {
        step1: {
          changeStatus,
          changeOrderId,
          creatorId,
          exeWay,
          planStartTime,
          realEndTime,
          operatorId,
          title,
          changeVersion,
          idcTag,
          blockTag,
          riskLevel,
          reasonName,
          changeType,
          respPersonId,
        },
      },
    },
  },
}) => ({
  changeStatus,
  changeOrderId,
  creatorId,
  exeWay,
  planStartTime,
  realEndTime,
  operatorId,
  title,
  changeVersion,
  idcTag,
  blockGuid: `${idcTag}.${blockTag}`,
  riskLevel: riskLevel.value,
  reason: reasonName,
  changeType: changeType.value.key,
  changeRespPerson: respPersonId,
});
const SummaryRecords = ({ ...props }) => {
  const { isExportPDF, exportPDFTabs, setExportPDFTabs } = useChangeContext();
  const [form] = BaseForm.useForm();

  const [getChangeOfflineRelateTickets, { data }] = useChangeOfflineRelateTickets();
  const [submitChangeOnlineSummery] = useSubmitChangeOnlineSummery();
  const config = useSelector(selectCurrentConfig);

  const configUtil = new ConfigUtil(config);

  const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket');

  const changesTicketCreateChangeOffline =
    ticketScopeCommonConfigs.changes.features.createChangeOffline;
  useEffect(() => {
    if (props.newChangeStatus && props.newChangeStatus === 'IN_SUMMARY') {
      getChangeOfflineRelateTickets({
        variables: { changeOrderId: props.changeOrderId ?? props.id },
      });
    }
  }, [
    props.newChangeStatus,
    props.showNewDetail,
    props.changeOrderId,
    props.id,
    getChangeOfflineRelateTickets,
  ]);

  return (
    <SummaryRecordView
      {...props}
      baseForm={form}
      relateList={data?.changeOfflineRelateTickets?.data}
      changesTicketCreateChangeOffline={changesTicketCreateChangeOffline}
      isExportPDF={isExportPDF}
      exportPDFTabs={exportPDFTabs}
      setExportPDFTabs={setExportPDFTabs}
      submitChangeOnlineSummery={submitChangeOnlineSummery}
      getRelateList={() => {
        getChangeOfflineRelateTickets({
          variables: { changeOrderId: props.changeOrderId ?? props.id },
        });
      }}
    />
  );
};

const mapDispatchToProps = {
  summerySubmitActionCreator: summerySubmitActionCreator,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(Form.create('ticket-summary-records')(SummaryRecords));

const formItemLayout = {
  labelCol: {
    xs: { span: 12 },
    sm: { span: 5 },
    xl: { span: 5 },
    xxl: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 12 },
    sm: { span: 19 },
    xl: { span: 19 },
    xxl: { span: 20 },
  },
};

function Submit({ onSave, creatorId, exeWay, operatorId, changeVersion, form }) {
  const [authorized] = useAuthorized({ checkByUserId: creatorId });
  const [operatorAuthorized] = useAuthorized({ checkByUserId: operatorId });
  const [confirmOpen, setConfirmOpen] = useState(false);
  const processNodeInfoList = BaseForm.useWatch('processNodeInfoList', form) ?? [];
  const processNodeInfoListRef = useLatest(processNodeInfoList);
  const buttonVisible =
    ((authorized || operatorAuthorized) && exeWay === CHANGE_EXE_WAY_MAP.OffLine) ||
    exeWay === CHANGE_EXE_WAY_MAP.OnLine;
  const IndependentSubmitButton = buttonVisible && <Button type="primary">提交</Button>;
  const SubmitButton = buttonVisible && (
    <Button type="primary" onClick={onSave}>
      提交
    </Button>
  );

  const handleConfirmOpenChange = useCallback(
    newOpen => {
      if (!newOpen) {
        setConfirmOpen(false);
        return;
      }
      let canSubmit = true;
      processNodeInfoListRef.current.forEach(processNodeInfo => {
        if (processNodeInfo.edit && (processNodeInfo.userIdList ?? []).length === 0) {
          canSubmit = false;
          setConfirmOpen(newOpen);
          return;
        }
      });
      canSubmit && onSave();
    },
    [onSave, processNodeInfoListRef]
  );

  return changeVersion === 2 ? (
    <Popconfirm
      title="未完成选择审批流程中的审批人，确定按照默认审批流程？"
      open={confirmOpen}
      onOpenChange={handleConfirmOpenChange}
      onConfirm={() => {
        setConfirmOpen(true);
        onSave();
      }}
      onCancel={() => {
        setConfirmOpen(false);
      }}
    >
      {IndependentSubmitButton}
    </Popconfirm>
  ) : (
    SubmitButton
  );
}
