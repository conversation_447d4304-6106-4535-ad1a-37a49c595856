import React from 'react';
import { connect } from 'react-redux';

import { ChangeTabs } from '@manyun/ticket.model.change';
import { useChangeContext } from '@manyun/ticket.page.change-offline';
import { ChangeOfflineExecutionRecordTable } from '@manyun/ticket.ui.change-offline-execution-record-table';

import { TinyCard, TinyTable } from '@manyun/dc-brain.legacy.components';

import { CHANGE_TICKET_STATUS_STEP } from '../../../../constants';

export function ExecuteRecords({ step1, isOffLine }) {
  const { isExportPDF, setExportPDFTabs } = useChangeContext();

  return (
    <TinyCard>
      {isOffLine ? (
        <ChangeOfflineExecutionRecordTable
          changeOrderId={step1.changeOrderId}
          blockGuid={`${step1.idcTag}.${step1.blockTag}`}
          creatorId={step1.creatorId}
          statusNumber={CHANGE_TICKET_STATUS_STEP[step1.changeStatus]}
          operatorId={step1.operatorId}
          changeVersion={step1.changeVersion}
          onFecthFinish={total => {
            if (isExportPDF) {
              setExportPDFTabs(exportPDFTabs => {
                return exportPDFTabs.map(item => ({
                  ...item,
                  isVolid: item.key === ChangeTabs.ExecuteRecord ? !total : item.isVolid,
                  isRenderd: item.key === ChangeTabs.ExecuteRecord ? true : item.isRenderd,
                }));
              });
            }
          }}
        />
      ) : (
        <TinyTable columns={getColumns()} />
      )}
    </TinyCard>
  );
}

const mapStateToProps = ({
  change: {
    ticketDetail: {
      changeInfo: { step1 },
    },
  },
}) => ({
  step1,
});

export default connect(mapStateToProps, null)(ExecuteRecords);

const getColumns = () => [
  {
    title: '序号',
    dataIndex: '',
  },
  {
    title: '执行类别',
    dataIndex: '',
  },
  {
    title: '执行目标',
    dataIndex: '',
  },
  {
    title: '执行内容',
    dataIndex: '',
  },
  {
    title: '执行时间',
    dataIndex: '',
  },
  {
    title: '完成时间',
    dataIndex: '',
  },
  {
    title: '执行人',
    dataIndex: '',
  },
  {
    title: '执行状态',
    dataIndex: '',
  },
];
