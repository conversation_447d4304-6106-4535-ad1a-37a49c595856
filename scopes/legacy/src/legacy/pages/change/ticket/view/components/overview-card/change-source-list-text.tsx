/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-12-20
 *
 * @packageDocumentation
 */
import React, { useMemo } from 'react';
import { Link } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Popover } from '@manyun/base-ui.ui.popover';
import { Space } from '@manyun/base-ui.ui.space';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { MetaTypeText } from '@manyun/resource-hub.ui.meta-type-text';
import { getChangeLocales } from '@manyun/ticket.model.change';
import type { SourceType } from '@manyun/ticket.model.change';
import {
  generateChangeTicketDetail,
  generateEventDetailRoutePath,
  generateRiskRegisterDetailLocation,
  generateTicketLocation,
} from '@manyun/ticket.route.ticket-routes';

export type ChangeSourceTextProps = {
  sourceList: { sourceType: string; sourceId: string; title: string }[];
};

export function ChangeSourceListText({ sourceList }: ChangeSourceTextProps): JSX.Element {
  const locales = useMemo(() => getChangeLocales(), []);
  const getContent = ({
    sourceType,
    sourceId,
    title,
  }: {
    sourceType: string;
    sourceId: string;
    title: string;
  }) => {
    switch (sourceType) {
      case 'INSPECTION':
      case 'MAINTENANCE': {
        return (
          <Space direction="horizontal">
            <Tag color="blue">{locales.changeSource.source[sourceType as SourceType]}</Tag>
            <Link
              target="_blank"
              to={generateTicketLocation({
                ticketType: sourceType.toLowerCase(),
                id: sourceId as string,
              })}
            >
              {sourceId}
            </Link>
            <Typography.Text>{title}</Typography.Text>
          </Space>
        );
      }

      //
      case 'EVENT': {
        return (
          <Space direction="horizontal">
            <Tag color="blue">{locales.changeSource.source[sourceType as SourceType]}</Tag>
            <Link
              to={generateEventDetailRoutePath({
                id: sourceId as string,
              })}
              target="_blank"
            >
              {sourceId}
            </Link>
            <Typography.Text>{title}</Typography.Text>
          </Space>
        );
      }

      // 风险登记册
      case 'RISK': {
        return (
          <Space direction="horizontal">
            <Tag color="blue">{locales.changeSource.source[sourceType as SourceType]}</Tag>
            <Link to={generateRiskRegisterDetailLocation({ id: sourceId })} target="_blank">
              {sourceId}
            </Link>
            <Typography.Text>{title}</Typography.Text>
          </Space>
        );
      }
      //权限申请
      case 'CHANGE': {
        return (
          <Space direction="horizontal">
            <Tag color="blue">{locales.changeSource.source[sourceType as SourceType]}</Tag>
            <Link to={generateChangeTicketDetail({ id: sourceId })} target="_blank">
              {sourceId}
            </Link>
            <Typography.Text>{title}</Typography.Text>
          </Space>
        );
      }

      default:
        return (
          <Space direction="horizontal">
            <Tag color="blue">
              <MetaTypeText code={sourceType} metaType={MetaType.CHANGE_ONLINE_SOURCE} />
            </Tag>
            <Typography.Text> {sourceId ?? title}</Typography.Text>
          </Space>
        );
    }
  };
  return (
    <Space direction="horizontal">
      <Popover
        content={<Space direction="vertical">{sourceList?.map(item => getContent(item))}</Space>}
        title="变更来源"
        placement="bottomRight"
        overlayInnerStyle={{ maxHeight: 480, maxWidth: 680 }}
      >
        <Button type="link" compact>
          查看
        </Button>
      </Popover>
    </Space>
  );
}
