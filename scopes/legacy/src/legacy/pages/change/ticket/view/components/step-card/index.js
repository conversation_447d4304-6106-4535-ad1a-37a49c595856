import Form from '@ant-design/compatible/es/form';
import ExclamationCircleOutlined from '@ant-design/icons/es/icons/ExclamationCircleOutlined';
import moment from 'moment';
import React, { useMemo, useState } from 'react';
import { connect } from 'react-redux';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Steps } from '@manyun/base-ui.ui.steps';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import styled, { css } from '@manyun/dc-brain.theme.theme';

import { GutterWrapper, StatusText, TinyCard } from '@manyun/dc-brain.legacy.components';
import { STATUS_MAP } from '@manyun/dc-brain.legacy.constants/statusColor';
import {
  approvalActionCreator,
  getTicketDetailInfo,
  stopChangeActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/changeActions';

import { CHANGE_TICKET_STATUS_KEY_MAP, CHANGE_TICKET_STATUS_STEP } from '../../../../constants';
import { ApproveView } from './approve-view';

// http://chandao.manyun-local.com/zentao/bug-view-2856.html 开始变更和变更执行和为一个节点
const STATUS_STEP = {
  [CHANGE_TICKET_STATUS_KEY_MAP.DRAFT]: 0,
  [CHANGE_TICKET_STATUS_KEY_MAP.APPROVING]: 1,
  [CHANGE_TICKET_STATUS_KEY_MAP.WAITING_CHANGE]: 2,
  [CHANGE_TICKET_STATUS_KEY_MAP.CHANGING]: 2,
  [CHANGE_TICKET_STATUS_KEY_MAP.IN_SUMMARY]: 3,
  [CHANGE_TICKET_STATUS_KEY_MAP.SUMMARY_APPROVING]: 3,
  [CHANGE_TICKET_STATUS_KEY_MAP.WAITING_CLOSE]: 4,
  [CHANGE_TICKET_STATUS_KEY_MAP.FINISH]: 5,
};
export const StyledContainer = styled.div`
  ${props => {
    const { prefixCls } = props.theme;
    const stepsPrefixCls = `${prefixCls}-steps`;
    return css`
      // overrides steps's style
      .${stepsPrefixCls}-horizontal
        .${stepsPrefixCls}-item-content
        .${stepsPrefixCls}-item-description {
        max-width: none;
      }
    `;
  }}
`;

export function StepDetail({
  changeStatus,
  statusInfoList = [],
  stopReason,
  changeOrderId,
  workFlowId,
  stopChangeActionCreator,
  summeryApprovalId,
  creatorId,
  getTicketDetailInfo,
}) {
  const [changeStopReason, setStopReason] = useState('');
  const [visible, setVisible] = useState(false);

  const stepCurrent = CHANGE_TICKET_STATUS_STEP[changeStatus];
  const statusTime = {};
  statusInfoList &&
    statusInfoList.forEach(item => {
      if (
        item.changeStatus === CHANGE_TICKET_STATUS_KEY_MAP.APPROVING &&
        statusTime[item.changeStatus]
      ) {
        if (item.startTime > statusTime[item.changeStatus]) {
          statusTime[item.changeStatus] = item.startTime;
        }
      } else {
        statusTime[item.changeStatus] = item.startTime;
      }
    });

  // const onApproval = type => {
  //   approvalActionCreator({
  //     approvalResult: type,
  //     approvalBusinessType: 'CHANGE_ORDER',
  //     bizId: changeOrderId,
  //     type: 'ticket',
  //   });
  // };

  const onStopChange = () => {
    if (!changeStopReason) {
      return message.error('请填写变更终止原因！');
    }
    stopChangeActionCreator({ changeOrderId, stopReason: changeStopReason });
  };

  const stopChange = () => {
    if (changeStopReason) {
      onStopChange();
      setVisible(false);
    } else {
      message.error('请填写变更终止原因！');
    }
  };

  const validationProps = () => {
    const validationProps = {};
    if (!changeStopReason) {
      validationProps.validateStatus = 'error';
      validationProps.help = '变更终止原因为必填';
      validationProps.required = true;
    }
    if (changeStopReason.length > 100) {
      validationProps.validateStatus = 'error';
      validationProps.help = '变更终止原因最多不可以超过100个字符！';
      validationProps.required = true;
    }
    return validationProps;
  };

  return (
    <TinyCard>
      <StyledContainer>
        <Steps current={STATUS_STEP[changeStatus]} style={{ paddingLeft: 100, paddingRight: 100 }}>
          <Steps.Step
            title="创建"
            description={
              <div style={{ fontSize: '12px' }}>
                {moment(statusTime[CHANGE_TICKET_STATUS_KEY_MAP.DRAFT]).format(
                  'YYYY-MM-DD HH:mm:ss'
                )}
              </div>
            }
          />
          <Steps.Step
            title={
              <div>
                {stepCurrent < 1 && (
                  <div>
                    {workFlowId && workFlowId !== '0' ? '变更申请待审批' : '变更申请待审批'}
                  </div>
                )}
                {stepCurrent === 1 && <div>变更申请审批中</div>}

                {stepCurrent > 1 && <div>变更申请已审批</div>}
              </div>
            }
            description={
              <Space>
                {stepCurrent > 1 && (
                  <span style={{ fontSize: '12px' }}>
                    {moment(statusTime[CHANGE_TICKET_STATUS_KEY_MAP.WAITING_CHANGE]).format(
                      'YYYY-MM-DD HH:mm:ss'
                    )}
                  </span>
                )}
                {workFlowId && workFlowId !== '0' && (
                  <span style={{ fontSize: '12px' }}>
                    <ApproveView
                      changeOrderId={changeOrderId}
                      instId={workFlowId}
                      stepCurrent={stepCurrent}
                      status={changeStatus}
                      processType="CHANGE"
                      onCallBack={() => {
                        getTicketDetailInfo({ id: changeOrderId, mode: 'ticketDetail' });
                      }}
                    />
                  </span>
                )}
              </Space>
            }
          />
          <Steps.Step
            title={
              <Space size="8px">
                {stepCurrent <= 2 && <div>待变更</div>}
                {stepCurrent === 3 && <div key="title">变更执行</div>}
                {stepCurrent > 3 && <div key="title">变更执行结束</div>}
                {stopReason && (
                  <Tooltip title={<span>{stopReason}</span>}>
                    <StatusText status={STATUS_MAP.ALARM}>变更终止</StatusText>
                  </Tooltip>
                )}
              </Space>
            }
            description={
              stepCurrent > 3 && (
                <div style={{ fontSize: '12px' }}>
                  {moment(statusTime[CHANGE_TICKET_STATUS_KEY_MAP.IN_SUMMARY]).format(
                    'YYYY-MM-DD HH:mm:ss'
                  )}
                </div>
              )
            }
          />
          <Steps.Step
            title={
              <div>
                {stepCurrent <= 4 && (
                  <div>{summeryApprovalId ? '变更总结待审批' : '变更待总结'}</div>
                )}
                {stepCurrent === 5 && (
                  <div>
                    {summeryApprovalId && summeryApprovalId !== '0'
                      ? '变更总结审批中'
                      : '变更待总结'}
                  </div>
                )}
                {stepCurrent > 5 && <div>{summeryApprovalId ? '变更总结已审批' : '变更总结'}</div>}
              </div>
            }
            description={
              <Space>
                {stepCurrent === 4 && (
                  <div style={{ fontSize: '12px' }}>
                    {statusTime[CHANGE_TICKET_STATUS_KEY_MAP.WAITING_CLOSE] &&
                      moment(statusTime[CHANGE_TICKET_STATUS_KEY_MAP.WAITING_CLOSE]).format(
                        'YYYY-MM-DD HH:mm:ss'
                      )}
                  </div>
                )}

                {stepCurrent > 5 && (
                  <div style={{ fontSize: '12px', display: 'inline-block' }}>
                    {moment(statusTime[CHANGE_TICKET_STATUS_KEY_MAP.WAITING_CLOSE]).format(
                      'YYYY-MM-DD HH:mm:ss'
                    )}
                  </div>
                )}
                {summeryApprovalId && summeryApprovalId !== '0' && (
                  <div style={{ fontSize: '12px' }}>
                    <ApproveView
                      stepCurrent={stepCurrent}
                      changeOrderId={changeOrderId}
                      instId={summeryApprovalId}
                      status={changeStatus}
                      processType="CHANGE_SUMMERY"
                      onCallBack={() => {
                        getTicketDetailInfo({ id: changeOrderId, mode: 'ticketDetail' });
                      }}
                    />
                  </div>
                )}
              </Space>
            }
          />

          <Steps.Step
            title="变更关闭"
            description={
              stepCurrent === 7 && (
                <div style={{ fontSize: '12px' }}>
                  {moment(statusTime[CHANGE_TICKET_STATUS_KEY_MAP.FINISH]).format(
                    'YYYY-MM-DD HH:mm:ss'
                  )}
                </div>
              )
            }
          />
        </Steps>
      </StyledContainer>
      <Modal
        width="400px"
        title="是否终止变更"
        open={visible}
        closable={false}
        onCancel={() => setVisible(false)}
        onOk={stopChange}
      >
        <GutterWrapper mode="vertical">
          <GutterWrapper flex justifyContent="center" alignItems="center">
            <ExclamationCircleOutlined
              style={{ color: `var(--${prefixCls}-warning-color)`, fontSize: 16 }}
            />
            <span style={{ fontSize: 16, fontWeight: 500 }}>确认后将不可更改</span>
          </GutterWrapper>
          <Form>
            <Form.Item {...validationProps()}>
              <Input.TextArea
                placeholder="请填写变更终止原因"
                onChange={e => setStopReason(e.target.value)}
              />
            </Form.Item>
          </Form>
        </GutterWrapper>
      </Modal>
    </TinyCard>
  );
}
export function NewStepDetail({
  changeStatus,
  stopReason,
  changeOrderId,
  workFlowId,
  summeryApprovalId,
  creatorId,
  getTicketDetailInfo,
  extJson,
  planStartTime,
  planEndTime,
  realStartTime,
  realEndTime,
}) {
  const statusInfoList = extJson?.statusInfoList ?? [];
  const stepCurrent = useMemo(() => {
    return CHANGE_TICKET_STATUS_STEP[changeStatus];
  }, [changeStatus]);
  const statusTime = {};
  statusInfoList &&
    statusInfoList.forEach(item => {
      if (
        item.changeStatus === CHANGE_TICKET_STATUS_KEY_MAP.APPROVING &&
        statusTime[item.changeStatus]
      ) {
        if (item.startTime > statusTime[item.changeStatus]) {
          statusTime[item.changeStatus] = item.startTime;
        }
      } else {
        statusTime[item.changeStatus] = item.startTime;
      }
    });

  return (
    <TinyCard>
      <StyledContainer>
        <Steps
          current={changeStatus === 'CANCEL' ? 5 : STATUS_STEP[changeStatus]}
          style={{ paddingLeft: 100, paddingRight: 100 }}
        >
          <Steps.Step
            title="创建"
            description={
              <div style={{ fontSize: '12px' }}>
                {moment(statusTime[CHANGE_TICKET_STATUS_KEY_MAP.DRAFT]).format(
                  'YYYY-MM-DD HH:mm:ss'
                )}
              </div>
            }
          />
          <Steps.Step
            title={
              <div>
                {stepCurrent < 1 && (
                  <div>
                    {workFlowId && workFlowId !== '0' ? '变更申请待审批' : '变更申请待审批'}
                  </div>
                )}
                {stepCurrent === 1 && <div>变更申请审批中</div>}

                {(stepCurrent > 1 || changeStatus === 'CANCEL') && <div>变更申请已审批</div>}
              </div>
            }
            description={
              <Space>
                {(stepCurrent > 1 || changeStatus === 'CANCEL') && (
                  <span style={{ fontSize: '12px' }}>
                    {moment(statusTime[CHANGE_TICKET_STATUS_KEY_MAP.WAITING_CHANGE]).format(
                      'YYYY-MM-DD HH:mm:ss'
                    )}
                  </span>
                )}
                {workFlowId && workFlowId !== '0' && (
                  <span style={{ fontSize: '12px' }}>
                    <ApproveView
                      changeOrderId={changeOrderId}
                      instId={workFlowId}
                      stepCurrent={stepCurrent}
                      status={changeStatus}
                      processType="CHANGE_ONLINE_APPLY"
                      onCallBack={() => {
                        getTicketDetailInfo();
                      }}
                    />
                  </span>
                )}
              </Space>
            }
          />
          {changeStatus !== 'CANCEL' && (
            <Steps.Step
              title={
                <Space size="8px">
                  {stepCurrent <= 2 && <div>待变更</div>}
                  {stepCurrent === 3 && <div key="title">变更执行中</div>}
                  {stepCurrent > 3 && <div key="title">变更执行结束</div>}
                  {/* {stopReason && (
                  <Tooltip title={<span>{stopReason}</span>}>
                    <StatusText status={STATUS_MAP.ALARM}>变更终止</StatusText>
                  </Tooltip>
                )} */}
                </Space>
              }
              description={
                stepCurrent >= 3 && (
                  <Space>
                    {stepCurrent > 3 && (
                      <div style={{ fontSize: '12px' }}>
                        {moment(statusTime[CHANGE_TICKET_STATUS_KEY_MAP.IN_SUMMARY]).format(
                          'YYYY-MM-DD HH:mm:ss'
                        )}
                      </div>
                    )}
                    {realStartTime < planStartTime && <Tag color="warning">提前执行</Tag>}
                    {realStartTime >= planStartTime &&
                      ((!realEndTime && moment().valueOf() <= planEndTime) ||
                        (realEndTime && realEndTime <= planEndTime)) && (
                        <Tag color="success">正常执行</Tag>
                      )}
                    {((realEndTime && realEndTime > planEndTime) ||
                      (!realEndTime && moment().valueOf() > planEndTime)) && (
                      <Tag color="warning">变更超时</Tag>
                    )}
                  </Space>
                )
              }
            />
          )}
          {changeStatus !== 'CANCEL' && (
            <Steps.Step
              title={
                <div>
                  {stepCurrent <= 4 && (
                    <div>{summeryApprovalId ? '变更总结待审批' : '变更待总结'}</div>
                  )}
                  {stepCurrent === 5 && (
                    <div>
                      {summeryApprovalId && summeryApprovalId !== '0'
                        ? '变更总结审批中'
                        : '变更待总结'}
                    </div>
                  )}
                  {stepCurrent > 5 && (
                    <div>{summeryApprovalId ? '变更总结已审批' : '变更总结'}</div>
                  )}
                </div>
              }
              description={
                <Space>
                  {stepCurrent === 4 && (
                    <div style={{ fontSize: '12px' }}>
                      {statusTime[CHANGE_TICKET_STATUS_KEY_MAP.WAITING_CLOSE] &&
                        moment(statusTime[CHANGE_TICKET_STATUS_KEY_MAP.WAITING_CLOSE]).format(
                          'YYYY-MM-DD HH:mm:ss'
                        )}
                    </div>
                  )}

                  {stepCurrent > 5 && (
                    <div style={{ fontSize: '12px', display: 'inline-block' }}>
                      {moment(statusTime[CHANGE_TICKET_STATUS_KEY_MAP.WAITING_CLOSE]).format(
                        'YYYY-MM-DD HH:mm:ss'
                      )}
                    </div>
                  )}
                  {summeryApprovalId && summeryApprovalId !== '0' && (
                    <div style={{ fontSize: '12px' }}>
                      <ApproveView
                        stepCurrent={stepCurrent}
                        changeOrderId={changeOrderId}
                        instId={summeryApprovalId}
                        status={changeStatus}
                        processType="CHANGE_ONLINE_SUMMERY"
                        onCallBack={() => {
                          getTicketDetailInfo();
                        }}
                      />
                    </div>
                  )}
                </Space>
              }
            />
          )}

          {changeStatus !== 'CANCEL' && (
            <Steps.Step
              title="变更关闭"
              description={
                stepCurrent === 7 && (
                  <div style={{ fontSize: '12px' }}>
                    {moment(statusTime[CHANGE_TICKET_STATUS_KEY_MAP.FINISH]).format(
                      'YYYY-MM-DD HH:mm:ss'
                    )}
                  </div>
                )
              }
            />
          )}
          {changeStatus === 'CANCEL' && (
            <Steps.Step
              title="已取消变更"
              description={
                <div style={{ fontSize: '12px' }}>
                  {moment(statusTime['CANCEL']).format('YYYY-MM-DD HH:mm:ss')}
                </div>
              }
            />
          )}
        </Steps>
      </StyledContainer>
    </TinyCard>
  );
}

const mapStateToProps = ({
  change: {
    ticketDetail: {
      changeInfo: {
        step1: {
          changeStatus,
          changeOrderExtJson,
          changeOrderId,
          stopReason,
          workFlowId,
          summeryApprovalId,
          creatorId,
        },
      },
    },
  },
}) => ({
  changeStatus,
  statusInfoList: changeOrderExtJson && changeOrderExtJson.statusInfoList,
  changeOrderId,
  stopReason,
  workFlowId,
  summeryApprovalId,
  creatorId,
});

const mapDispatchToProps = {
  approvalActionCreator: approvalActionCreator,
  stopChangeActionCreator: stopChangeActionCreator,
  getTicketDetailInfo: getTicketDetailInfo,
};

export default connect(mapStateToProps, mapDispatchToProps)(StepDetail);
