import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { useParams } from 'react-router-dom';

import Form from '@ant-design/compatible/es/form';
import WarningOutlined from '@ant-design/icons/es/icons/WarningOutlined';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { Modal } from '@manyun/base-ui.ui.modal';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';
import {
  changeActions,
  getAlarmLevelCount,
  getTicketDetailAlarmList,
  getTicketStepDetail,
  startOperationActionCreator,
  stepCheckedActionCreator,
  stepSkipActionCreator,
  stepStopActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/changeActions';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';

import {
  CHANGE_OP_ITEM_STATUS_KEY_MAP,
  CHANGE_STEP_STATUS_KEY_MAP,
  CHANGE_STEP_TYPE_KEY_MAP,
  CHANGE_TICKET_STATUS_STEP,
} from '../../../../../constants';

export function OperationBtn({
  syncCommonData,
  opItemInfoMaps,
  checkItemInfoMaps,
  changeStatus,
  stepOrder,
  stepList,
  startOperationActionCreator,
  stepCheckedActionCreator,
  stepStopActionCreator,
  stepSkipActionCreator,
  form,
  hasPermission,
}) {
  const [stepSkipVisible, setStepSkipVisible] = useState(false);
  const { id } = useParams();
  const status = CHANGE_TICKET_STATUS_STEP[changeStatus];
  const { getFieldDecorator } = form;

  useEffect(() => {
    syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (stepList.length !== 1 || !hasPermission) {
    return null;
  }

  const step = stepList.filter(item => item.stepOrder === stepOrder);

  const handleStartOperation = () => {
    startOperationActionCreator({
      changeOrderId: id,
      stepId: step[0].id,
      stepOrder: stepOrder,
    });
  };

  const handleChecked = () => {
    stepCheckedActionCreator({
      changeOrderId: id,
      stepId: step[0].id,
      stepOrder: stepOrder,
    });
  };

  const handleStepSkip = () => {
    setStepSkipVisible(!stepSkipVisible);
  };

  const handleStepStop = () => {
    stepStopActionCreator({
      changeOrderId: id,
      stepId: step[0].id,
      stepOrder: stepOrder,
    });
  };

  const stepSkipOk = () => {
    new Promise(() => {
      form.validateFields((errs, values) => {
        if (errs) {
          return;
        }
        stepSkipActionCreator({
          param: {
            changeOrderId: id,
            stepId: step[0].id,
            stepOrder: stepOrder,
            ...values,
          },
          successCallback: () => {
            setStepSkipVisible(false);
          },
        });
      });
    });
  };

  if (status === 2 || step[0].stepStatus === CHANGE_STEP_STATUS_KEY_MAP.NOT_START) {
    return (
      <Button type="primary" onClick={handleStartOperation}>
        开始操作
      </Button>
    );
  }
  if (status === 3) {
    let needToDetermine = [];
    const checkBtn = (
      <Button type="primary" onClick={handleChecked}>
        检查
      </Button>
    );
    const stopBtn = (
      <Button type="primary" onClick={handleStepStop}>
        结束操作
      </Button>
    );
    const skipBtn = [
      <Button danger key="skip_btn" onClick={handleStepSkip}>
        强行跳过
      </Button>,
      <Modal
        key="skip_btn_modal"
        width="495px"
        title="是否强行跳过"
        visible={stepSkipVisible}
        onOk={stepSkipOk}
        onCancel={handleStepSkip}
        // footer={null}
        closable={false}
      >
        <GutterWrapper mode="vertical">
          <GutterWrapper justifyContent="center" alignItems="center" flex>
            {/* <span> */}
            <WarningOutlined style={{ color: `var(--${prefixCls}-warning-color)`, fontSize: 16 }} />
            <span style={{ fontSize: 16, fontWeight: 500 }}>跳过后将忽略异常项问题</span>
            {/* </span> */}
          </GutterWrapper>
          <Form>
            <Form.Item>
              {getFieldDecorator('jumpReason', {
                rules: [
                  { required: true, message: '跳过原因必填！' },
                  {
                    max: 100,
                    message: '最多输入 100 个字符！',
                  },
                ],
              })(<Input.TextArea rows={4} placeholder="强行跳过原因" />)}
            </Form.Item>
          </Form>
        </GutterWrapper>
      </Modal>,
    ];
    const opItem = opItemInfoMaps[stepOrder] || [];
    const checkItem = checkItemInfoMaps[stepOrder] || [];
    if (opItem && Array.isArray(opItem)) {
      needToDetermine.push(...opItem);
    }
    if (checkItem && Array.isArray(checkItem)) {
      needToDetermine.push(...checkItem);
    }

    if (step[0].stepType === CHANGE_STEP_TYPE_KEY_MAP.RUN) {
      //跑位步骤时的按钮
      let normalList = opItem.filter(
        item => item.itemStatus === CHANGE_OP_ITEM_STATUS_KEY_MAP.NORMAL
      ).length;
      let exceptionList = opItem.filter(
        item => item.itemStatus === CHANGE_OP_ITEM_STATUS_KEY_MAP.EXCEPTION
      ).length;
      if (normalList === opItem.length) {
        return stopBtn;
      }
      if (exceptionList + normalList === opItem.length && exceptionList) {
        return skipBtn;
      }
      return null;
    }
    // if (
    //   needToDetermine.filter(item => item.itemStatus === CHANGE_STEP_STATUS_KEY_MAP.EXCEPTION)
    //     .length
    // ) {
    //   return (
    //     <GutterWrapper>
    //       {btn}
    //       <Button danger onClick={handleStepSkip}>
    //         强行跳过
    //       </Button>
    //     </GutterWrapper>
    //   );
    // }
    if (
      !needToDetermine.filter(item => item.itemStatus !== CHANGE_OP_ITEM_STATUS_KEY_MAP.NORMAL)
        .length &&
      step[0].stepType !== CHANGE_STEP_TYPE_KEY_MAP.CUSTOMIZE
    ) {
      return <GutterWrapper>{stopBtn}</GutterWrapper>;
    }

    if (step[0].stepStatus === CHANGE_STEP_STATUS_KEY_MAP.NOT_START) {
      return (
        <Button type="primary" onClick={handleStartOperation}>
          开始操作
        </Button>
      );
    }

    if (step[0].stepType === CHANGE_STEP_TYPE_KEY_MAP.CUSTOMIZE) {
      return null;
    }

    return (
      <GutterWrapper>
        {checkBtn}
        {needToDetermine.filter(item => item.itemStatus === CHANGE_STEP_STATUS_KEY_MAP.EXCEPTION)
          .length &&
        !needToDetermine.filter(item => item.itemStatus === CHANGE_STEP_STATUS_KEY_MAP.NOT_START)
          .length
          ? skipBtn
          : null}
      </GutterWrapper>
    );
  }
  return null;
}

const mapStateToProps = ({
  change: {
    ticketDetail: {
      step: { stepList, checkItemInfoMaps, opItemInfoMaps, stepOrder },
      changeInfo: {
        step1: { changeStatus, hasPermission },
      },
    },
  },
  //   common: { deviceCategory },
}) => ({
  stepList,
  checkItemInfoMaps,
  opItemInfoMaps,
  //   metaCategoryEntities: deviceCategory ? deviceCategory.normalizedList : {},
  changeStatus,
  stepOrder,
  hasPermission,
});

const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
  getTicketDetailAlarmList: getTicketDetailAlarmList,
  getAlarmLevelCount: getAlarmLevelCount,
  setAlarmLevelCount: changeActions.setAlarmLevelCount,
  getTicketStepDetail: getTicketStepDetail,
  startOperationActionCreator: startOperationActionCreator,
  stepCheckedActionCreator: stepCheckedActionCreator,
  stepSkipActionCreator: stepSkipActionCreator,
  stepStopActionCreator: stepStopActionCreator,
};
export default connect(
  mapStateToProps,
  mapDispatchToProps
)(Form.create({ name: 'changge_operation_btn' })(OperationBtn));
