import { useEffect } from 'react';

import { Space } from '@manyun/base-ui.ui.space';

import { ApprovalOperationButtons } from '@manyun/bpm.ui.approval-operation-buttons';
import { ApprovalRecordsDropdown } from '@manyun/bpm.ui.approval-records-dropdown';
import { AwaitOperationPeopleTag } from '@manyun/bpm.ui.bpm-instance-viewer';
import { useLazyFetchChangeApproval } from '@manyun/ticket.gql.client.tickets';
import { useChangeContext } from '@manyun/ticket.page.change-offline';

export type ApproveViewProps = {
  instId: string;
  changeOrderId: string;
  stepCurrent: number;
  processType: string;
  status: string;
  onCallBack: () => void;
};

export function ApproveView({
  instId,
  changeOrderId,
  processType,
  stepCurrent,
  status,
  onCallBack,
}: ApproveViewProps) {
  const { isExportPDF } = useChangeContext();

  const [getFetchChangeApproval, { data, refetch }] = useLazyFetchChangeApproval();
  useEffect(() => {
    if (!instId) {
      return;
    }
    getFetchChangeApproval({
      variables: {
        query: {
          changeOrderId: changeOrderId,
          instId,
        },
      },
    });
    //  eslint-disable-next-line react-hooks/exhaustive-deps
  }, [instId]);

  if (!data?.fetchChangeApproval?.data?.length) {
    return;
  }
  const baseInfo = data?.fetchChangeApproval?.data[0] || {};
  return (
    <Space>
      <ApprovalRecordsDropdown
        businessOrderInfo={{
          taskNumber: changeOrderId,
          type: processType,
          approvalPermissionType: 'CHANGE',
          status: status,
        }}
      />
      <AwaitOperationPeopleTag bpmInstance={baseInfo} />
      {(stepCurrent === 1 || stepCurrent === 5) && !isExportPDF && (
        <ApprovalOperationButtons
          baseInfo={baseInfo}
          getDetail={() => {
            refetch();
            onCallBack();
          }}
          showCarbonCopyButton={false}
        />
      )}
    </Space>
  );
}
