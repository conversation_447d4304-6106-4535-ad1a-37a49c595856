@import (reference) '@manyun/base-ui.theme.theme/dist/theme.less';

@table-prefix-cls: ~'@{prefixCls}-table';

.virtualTable {
  .cell {
    border-bottom: @border-width-base @border-style-base @table-border-color;
  }

  :global(.@{table-prefix-cls}-bordered) {
    .grid {
      border-right: (@border-width-base + 1) @border-style-base @table-border-color;
      border-bottom: @border-width-base @border-style-base @table-border-color;
    }
  }

  :global(.@{table-prefix-cls}-small) {
    .cell {
      padding: @table-padding-vertical-sm @table-padding-horizontal-sm;
    }
  }
}
