import React, { useState } from 'react';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';

import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { CHANGE_EXE_WAY_MAP } from '@manyun/ticket.model.change';
import type { ChangeOfflineFormValues } from '@manyun/ticket.page.change-offline';
import { ChangeOfflineForm, getPlantime } from '@manyun/ticket.page.change-offline';
import { createChange } from '@manyun/ticket.service.create-change';
import type { CreateChange } from '@manyun/ticket.service.create-change';

export type CreateRiskDrawerProps = {
  setVisibleType: () => void;
  changeOrderId: string;
  changeTitle: string;
  visibleType: 'event' | 'risk' | 'change' | null;
  onSuccess?: () => void;
};

export function CreateChangeDrawer({
  changeOrderId,
  visibleType,
  changeTitle,
  setVisibleType,
  onSuccess,
}: CreateRiskDrawerProps) {
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm<ChangeOfflineFormValues>();

  return (
    <Drawer
      forceRender
      title="创建变更单"
      size="large"
      placement="right"
      open={visibleType === 'change'}
      width={752}
      extra={
        <Space>
          <Button
            onClick={() => {
              form.resetFields();
              setVisibleType();
            }}
          >
            取消
          </Button>
          <Button
            type="primary"
            loading={loading}
            onClick={() => {
              form.validateFields().then(async formValues => {
                const [idcTag] = formValues.blockGuid.split('.');
                const blockTag = formValues.blockGuid;
                const {
                  riskLevel,
                  changeType,
                  fileInfoList,
                  reason,
                  title,
                  changeInfluence,
                  customerFileInfoList,
                  changeDeviceList,
                  stepList,
                  changeTimeList,
                  changeSource,
                  purpose,
                } = formValues;
                const planTime = getPlantime({ changeTimeList });

                const _p: CreateChange = {
                  riskLevel,
                  changeType,
                  title: title,
                  fileInfoList: fileInfoList.map((obj: McUploadFile) =>
                    McUploadFile.toApiObject(obj)
                  ),
                  idcTag,
                  blockTag,
                  reason: reason,
                  exeWay: CHANGE_EXE_WAY_MAP.OffLine,
                  changeInfluence,
                  customerFileInfoList: customerFileInfoList?.map((obj: McUploadFile) =>
                    McUploadFile.toApiObject(obj)
                  ),
                  changeDeviceList,
                  stepList: stepList
                    ? stepList.map(item => ({
                        stepName: item.stepName,
                        stepOrder: item.stepOrder,
                        operatorId: item.operator?.value,
                        operatorName: item.operator?.label,
                        stepDesc: item.stepDesc,
                      }))
                    : [],
                  changeTimeList: planTime.changeTimeList,
                  sourceNo: changeSource?.id,
                  sourceType: changeSource?.type,
                  planStartTime: planTime.planStartTime,
                  planEndTime: planTime.planEndTime,
                  purpose,
                  sourceChangeOrderId: changeOrderId,
                };
                setLoading(true);

                const { error } = await createChange(_p);
                setLoading(false);
                if (error) {
                  message.error(error.message);
                  return;
                }
                onSuccess && onSuccess();
                message.success('提交成功');
                setVisibleType();
              });
            }}
          >
            提交
          </Button>
        </Space>
      }
      onClose={() => setVisibleType()}
    >
      <Alert message="关联变更" description={`${changeOrderId} - ${changeTitle}`} type="info" />
      <ChangeOfflineForm form={form} setSubmitting={setLoading} />
    </Drawer>
  );
}
