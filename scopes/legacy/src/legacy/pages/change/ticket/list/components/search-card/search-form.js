import React, { useEffect } from 'react';
import { connect, useSelector } from 'react-redux';
import { useLocation } from 'react-router-dom';

import { FiltersForm, Form } from '@galiojs/awesome-antd';

import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Input } from '@manyun/base-ui.ui.input';
import { Select } from '@manyun/base-ui.ui.select';
import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import { CHANGE_EXE_WAY_TEXT_MAP, CHANGE_RESULT_KEY_TEXT_MAP } from '@manyun/ticket.model.change';
import { ChangeTypeSelect } from '@manyun/ticket.ui.change-type-select';

import { ChangeReasonsSelect } from '@manyun/dc-brain.legacy.pages/change/components/change-reason-select';
import {
  changeActions,
  getTicketDataActionCreator,
  resetTicketSearchValuesActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/changeActions';

import RiskLevelSelect from '../../../../components/risk-level-select';
// import EmergencyLevelSelect from '../../../../components/emergrncy-level-select';
import TicketStatusSelect from '../../../../components/ticket-status-select';
import { getSearchValuesAndUpdateValues, setLocationSearchValues } from '../utils';

// const { RangePicker } = DatePicker;

const items = [
  {
    label: '变更ID',
    name: 'changeOrderId',
    control: <Input allowClear />,
  },

  {
    label: '位置',
    name: 'location',
    control: <LocationTreeSelect authorizedOnly allowClear />,
  },
  {
    label: '变更方式',
    name: 'exeWay',
    control: (
      <Select allowClear>
        {Object.entries(CHANGE_EXE_WAY_TEXT_MAP).map(item => (
          <Select.Option key={item[0]} value={item[0]}>
            {item[1]}
          </Select.Option>
        ))}
      </Select>
    ),
  },
  {
    label: '变更状态',
    name: 'statusList',
    control: <TicketStatusSelect mode="multiple" maxTagCount={1} />,
  },
  {
    label: '变更结果',
    name: 'exeResult',
    control: (
      <Select allowClear>
        {Object.entries(CHANGE_RESULT_KEY_TEXT_MAP).map(item => (
          <Select.Option key={item[0]} value={item[0]}>
            {item[1]}
          </Select.Option>
        ))}
      </Select>
    ),
  },
  {
    label: '变更专业',
    name: 'changeReasonList',
    control: <ChangeReasonsSelect mode="multiple" maxTagCount={1} />,
  },
  {
    label: '变更等级',
    name: 'changeLevelList',
    control: <RiskLevelSelect mode="multiple" allowClear maxTagCount={1} />,
  },
  {
    label: '变更类型',
    name: 'changeType',
    control: <ChangeTypeSelect />,
  },
  {
    label: '提单人',
    name: 'creatorId',
    control: <UserSelect labelInValue={false} allowClear />,
  },
  {
    span: 2,
    label: '计划时间',
    name: 'planTime',
    control: <DatePicker.RangePicker format="YYYY-MM-DD" />,
  },
  {
    span: 2,
    label: '执行时间',
    name: 'realTime',
    control: <DatePicker.RangePicker format="YYYY-MM-DD" />,
  },
];

export function ChangeListSearchForm({
  updateSearchValues,
  onSearch,
  onReset,
  searchValues,
  setPagination,
  pagination,
}) {
  const { search } = useLocation();
  const [form] = Form.useForm();

  const config = useSelector(selectCurrentConfig);

  const configUtil = new ConfigUtil(config);

  const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket');
  const changesTicketVersion = ticketScopeCommonConfigs.changes.features.version;

  useEffect(() => {
    const {
      status = [],
      idc = '',
      block = '',
      exeWay,
      creatorId,
    } = getLocationSearchMap(search, { arrayKeys: ['status'] });
    const updateSearchParams = {
      ...searchValues,
      statusList: { value: status, name: 'statusList' },
    };
    if (idc) {
      updateSearchParams.location = { value: idc, name: 'location' };
    }
    if (block) {
      updateSearchParams.location = { value: block, name: 'location' };
    }
    if (exeWay) {
      updateSearchParams.exeWay = { value: exeWay, name: 'exeWay' };
    }
    if (creatorId) {
      updateSearchParams.creatorId = { value: Number(creatorId), name: 'creatorId' };
    }
    updateSearchValues(updateSearchParams);
    getSearchValuesAndUpdateValues({ setPagination, updateSearchValues });
    return () => {
      onReset();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [search]);

  const onSearchData = () => {
    onSearch();
    setLocationSearchValues(searchValues, { pageNum: 1, pageSize: pagination.pageSize });
  };

  return (
    <FiltersForm
      form={form}
      items={changesTicketVersion === 'lite' ? items.filter(item => item.name !== 'exeWay') : items}
      fields={Object.keys(searchValues).map(name => {
        const field = searchValues[name];
        return {
          ...field,
          // name 为数组形式
          name: name.split('.'),
        };
      })}
      onSearch={onSearchData}
      onReset={() => {
        onReset();
        setLocationSearchValues({}, { pageNum: 1, pageSize: 10 });
      }}
      onFieldsChange={changedFields => {
        updateSearchValues(
          changedFields.reduce((mapper, field) => {
            // field.name 为数组形式，老代码需要的是字符串形式
            const name = field.name.join('.');
            mapper[name] = {
              ...field,
              name,
            };
            return mapper;
          }, {})
        );
      }}
    />
  );
}

const mapStateToProps = ({
  change: {
    ticketList: { searchValues, pagination },
  },
}) => {
  return { searchValues, pagination };
};
const mapDispatchToProps = {
  updateSearchValues: changeActions.updateTicketSearchValues,
  onSearch: getTicketDataActionCreator,
  onReset: resetTicketSearchValuesActionCreator,
  setPagination: changeActions.setTicketPagination,
};

export default connect(mapStateToProps, mapDispatchToProps)(ChangeListSearchForm);
