import React, { useCallback, useEffect, useState } from 'react';
import { connect, useSelector } from 'react-redux';
import { Link } from 'react-router-dom';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { Button } from '@manyun/base-ui.ui.button';
import { Divider } from '@manyun/base-ui.ui.divider';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { CHANGE_EXE_WAY_MAP, ChangeTicketState } from '@manyun/ticket.model.change';
import {
  CHANGE_OFFLINE_NEW, // CHANGE_TICKET_NEW,
  generateChangeOfflineEditRoutePath,
} from '@manyun/ticket.route.ticket-routes';
import { cancelChange } from '@manyun/ticket.service.cancel-change';
import { completeOfflineChange } from '@manyun/ticket.service.complete-offline-change';
import { deleteChange } from '@manyun/ticket.service.delete-change';
import { exportChange } from '@manyun/ticket.service.export-change';

import { ChangeTable } from '@manyun/dc-brain.legacy.components';
import * as urls from '@manyun/dc-brain.legacy.constants/urls';
// Deprecated, replace with "useAuthorized" hook
import {
  changeActions,
  getTicketCopyInfoActionCreator,
  getTicketDataActionCreator,
  setTicketPaginationThenGetDataActionCreator,
  ticketApprovalOrRevertActionCreator,
  ticketCloseActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/changeActions';
import {
  generateChangeTicketDetail,
  generateChangeTicketEdit,
} from '@manyun/dc-brain.legacy.utils/urls';

import { setLocationSearchValues } from '../utils';

export function TicketDataTable({
  data,
  total,
  loading,
  pageNum,
  pageSize,
  getData,
  setPagination,
  permissions,
  ticketApprovalOrRevertActionCreator,
  getTicketCopyInfoActionCreator,
  ticketCloseActionCreator,
  setTabCurrent,
  searchValues,
}) {
  const [visible, setVisible] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [form] = Form.useForm();
  const [changeOrderId, setChangeOrderId] = useState('');

  const config = useSelector(selectCurrentConfig);

  const configUtil = new ConfigUtil(config);

  const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket');

  const changesTicketVersion = ticketScopeCommonConfigs.changes.features.version;
  const [authorized] = useAuthorized({ checkByCode: 'change_ticket_list_create' });

  useEffect(() => {
    getData(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const paginationChangeHandler = useCallback(
    (current, size) => {
      setPagination({ pageNum: current, pageSize: size });
      setLocationSearchValues(searchValues, { pageNum: current, pageSize: size });
    },
    [setPagination, searchValues]
  );

  const onApprovalOrRevert = changeOrderId => {
    ticketApprovalOrRevertActionCreator({ changeOrderId });
  };

  const onClose = changeOrderId => {
    ticketCloseActionCreator({ changeOrderId: changeOrderId, type: 'list' });
  };

  const refetchData = () => {
    setPagination({ pageNum: 1, pageSize: 10 });
    setLocationSearchValues(searchValues, { pageNum: 1, pageSize: 10 });
  };

  const onVisibleCancel = changeOrderId => {
    setChangeOrderId(changeOrderId);
    setVisible(true);
  };

  const handleFileExport = useCallback(
    async type => {
      setExportLoading(true);
      let params = {};
      if (type === 'filtered') {
        params = { ...getParams() };
      }
      const { error, data } = await exportChange({ ...params, changeVersion: 1 });
      setExportLoading(false);
      if (error) {
        message.error(error.message);
        return false;
      }
      return data;
    },
    //  eslint-disable-next-line react-hooks/exhaustive-deps
    [searchValues]
  );

  const getParams = () => {
    const baseQ = { pageNum, pageSize };
    for (const [name, { value }] of Object.entries(searchValues)) {
      if (name === 'planTime' && value?.length) {
        baseQ['planStartTime'] = value[0].clone().startOf('day');
        baseQ['planEndTime'] = value[1].clone().endOf('day');
      } else if (name === 'realTime' && value?.length) {
        baseQ['executeStartTime'] = value[0].startOf('day');
        baseQ['executeEndTime'] = value[1].endOf('day');
      } else if (name === 'location' && value) {
        const location = value.split('.');
        baseQ['idcTag'] = location[0];
        baseQ['blockTag'] = location.length === 2 ? `${location[0]}.${location[1]}` : null;
      } else if (name === 'changeOrderId' || name === 'title') {
        baseQ[name] = value?.trim();
      } else if (name === 'creatorId') {
        baseQ[name] = value?.value;
      } else if (name === 'changeType' && value) {
        baseQ[name] = Number(value);
      } else if (name === 'changeReasonList' && value) {
        baseQ[name] = value.map(item => item.label);
      } else {
        baseQ[name] = value;
      }
    }
    return baseQ;
  };

  // const isChangesTicketFullVersionEnabled =
  //   changesTicketVersion === 'full' || changesTicketVersion === 'both';
  const isChangesTicketLiteVersionEnabled =
    changesTicketVersion === 'lite' || changesTicketVersion === 'both';
  // const changesTicketVersionIsLite = changesTicketVersion === 'lite';
  return [
    <ChangeTable
      key="changeTable"
      changesTicketVersion={changesTicketVersion}
      actions={
        <div
          style={{
            display: 'flex',
            width: '100%',
            justifyContent: authorized ? 'space-between' : 'flex-end',
          }}
        >
          {authorized && (
            <Space>
              {/* {isChangesTicketFullVersionEnabled && (
                <Link key="new" to={{ pathname: CHANGE_TICKET_NEW }}>
                  <Button type="primary">新建线上变更</Button>
                </Link>
              )} */}
              {isChangesTicketLiteVersionEnabled && (
                <Link key="new_offline" to={{ pathname: CHANGE_OFFLINE_NEW }}>
                  <Button type="primary">新建线下文档变更</Button>
                </Link>
              )}
            </Space>
          )}
          <FileExport
            text="导出"
            filename="变更.xls"
            disabled={exportLoading}
            data={type => {
              return handleFileExport(type);
            }}
            showExportFiltered
          />
        </div>
      }
      size="large"
      dataSource={data}
      loading={loading}
      pagination={{
        total,
        current: pageNum,
        pageSize,
        onChange: paginationChangeHandler,
      }}
      operation={{
        title: '操作',
        dataIndex: '',
        fixed: 'right',
        render: (_, { changeOrderId, changeStatus, workFlowId, creatorId, exeWay, operatorId }) => {
          const props = {
            changeOrderId,
            changeStatus,
            onApprovalOrRevert,
            getTicketCopyInfoActionCreator,
            onClose,
            permissions,
            workFlowId,
            setTabCurrent,
            creatorId,
            exeWay,
            operatorId,
            refetchData,
            onVisibleCancel,
          };
          return <GetTableOptions {...props} />;
        },
      }}
    />,
    <Modal
      key="cancelChange"
      title="取消变更"
      open={visible}
      width={750}
      onCancel={() => setVisible(false)}
      onOk={() => {
        form.validateFields().then(async values => {
          const { error } = await cancelChange({ cancelReason: values.reason, changeOrderId });
          if (error) {
            message.error(error.message);
            return;
          }
          refetchData();
          setVisible(false);
          message.success('已取消变更');
        });
      }}
    >
      <Form form={form} colon={false} labelCol={{ xl: 4 }} wrapperCol={{ xl: 20 }}>
        <Form.Item
          label="取消原因"
          name="reason"
          rules={[
            {
              required: true,
              whitespace: true,
              message: '原因必填',
            },
            {
              max: 50,
              message: '最多输入 50 个字符！',
            },
          ]}
        >
          <Input.TextArea style={{ width: 300 }} allowClear />
        </Form.Item>
      </Form>
    </Modal>,
  ];
}

const mapStateToProps = ({
  change: {
    ticketList: {
      data,
      total,
      pagination: { pageNum, pageSize },
      loading,
      searchValues,
    },
  },
  user: { permissions },
}) => ({
  data,
  total,
  pageNum,
  pageSize,
  loading,
  permissions,
  searchValues,
});
const mapDispatchToProps = {
  getData: getTicketDataActionCreator,
  setPagination: setTicketPaginationThenGetDataActionCreator,
  ticketApprovalOrRevertActionCreator: ticketApprovalOrRevertActionCreator,
  getTicketCopyInfoActionCreator: getTicketCopyInfoActionCreator,
  ticketCloseActionCreator: ticketCloseActionCreator,
  setTabCurrent: changeActions.setTabCurrent,
};

export default connect(mapStateToProps, mapDispatchToProps)(TicketDataTable);

function GetTableOptions({
  changeOrderId,
  changeStatus,
  onApprovalOrRevert,
  getTicketCopyInfoActionCreator,
  onClose,
  permissions,
  workFlowId,
  setTabCurrent,
  creatorId,
  exeWay,
  refetchData,
  onVisibleCancel,
  operatorId,
}) {
  const [authorized] = useAuthorized({ checkByUserId: creatorId });
  const [operatorAuthorized] = useAuthorized({ checkByUserId: operatorId });

  const closeView = (
    <Button type="link" compact onClick={() => onClose(changeOrderId)}>
      关闭
    </Button>
  );
  const deleteChangeView = (
    <Popconfirm
      key="delete"
      title="确认删除该条变更单吗？"
      okText="确定删除"
      onConfirm={async () => {
        const { error } = await deleteChange({ changeOrderId });
        if (error) {
          message.error(error.message);
          return;
        }
        refetchData();
      }}
    >
      <Button compact type="link">
        删除
      </Button>
    </Popconfirm>
  );
  if (exeWay === CHANGE_EXE_WAY_MAP.OffLine) {
    if (changeStatus === ChangeTicketState.Draft && (authorized || operatorAuthorized)) {
      return (
        <div style={{ display: 'flex', justifyContent: 'flex-start', alignItems: 'center' }}>
          <Link
            to={generateChangeOfflineEditRoutePath({
              id: changeOrderId,
            })}
          >
            编辑
          </Link>
          <Divider key="divider" type="vertical" />
          {deleteChangeView}
        </div>
      );
    }
    if (changeStatus === ChangeTicketState.Changing && (authorized || operatorAuthorized)) {
      return (
        <Button
          type="link"
          compact
          onClick={async () => {
            const { error } = await completeOfflineChange({ changeOrderId });
            if (error) {
              message.error(error.message);
              return;
            }
            message.success('变更已完成');
            refetchData();
          }}
        >
          完成变更
        </Button>
      );
    }
    if (changeStatus === ChangeTicketState.WaitingChange && (authorized || operatorAuthorized)) {
      return (
        <Button type="link" compact onClick={() => onVisibleCancel(changeOrderId)}>
          取消变更
        </Button>
      );
    }
    if (changeStatus === ChangeTicketState.WaitingClose && (authorized || operatorAuthorized)) {
      return closeView;
    }
    return '--';
  }
  const copyBtn = (
    <Link key="copy" to={{ pathname: urls.CHANGE_TICKET_NEW }}>
      <Button
        type="link"
        compact
        onClick={() => {
          getTicketCopyInfoActionCreator({ id: changeOrderId });
        }}
      >
        复制
      </Button>
    </Link>
  );
  if (changeStatus === ChangeTicketState.Changing) {
    return (
      <div style={{ display: 'flex', justifyContent: 'flex-start', alignItems: 'center' }}>
        <Link
          to={generateChangeTicketDetail({
            id: changeOrderId,
          })}
        >
          <Button type="link" compact onClick={() => setTabCurrent('ticketOption')}>
            执行
          </Button>
        </Link>
        <Divider key="divider" type="vertical" />
        {copyBtn}
      </div>
    );
  }
  if (changeStatus === ChangeTicketState.Draft && authorized) {
    return (
      <div style={{ display: 'flex', justifyContent: 'flex-start', alignItems: 'center' }}>
        <Link
          to={generateChangeTicketEdit({
            id: changeOrderId,
          })}
        >
          编辑
        </Link>
        <Divider key="divider" type="vertical" />
        {deleteChangeView}
      </div>
    );
  }
  if (changeStatus === ChangeTicketState.Approving && authorized) {
    return (
      <Button key="revert" type="link" compact onClick={() => onApprovalOrRevert(changeOrderId)}>
        撤回
      </Button>
    );
  }

  if (changeStatus === ChangeTicketState.WaitingClose) {
    return closeView;
  }

  if (
    permissions?.includes('change_ticket_list_create') &&
    changeStatus === ChangeTicketState.Finish
  ) {
    return copyBtn;
  }
  return copyBtn;
}
