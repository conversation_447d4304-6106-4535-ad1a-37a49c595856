import React, { useEffect, useRef } from 'react';
import { connect } from 'react-redux';

import { Typography } from '@manyun/base-ui.ui.typography';

import { Footer<PERSON>ool<PERSON><PERSON>, GutterWrapper, TinyCard } from '@manyun/dc-brain.legacy.components';
import {
  changeActions,
  setTicketSaveActionCreator,
  setTicketSubmitActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/changeActions';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';

import { INIT_TICKET_DATA } from '../../constants';
import Result from '../components/result';
import StepButtons from './../../components/step-buttons';
import StepTableList from './../../components/step-table-list';
import BaseInfoForm from './../components/base-info-form';
import Steps from './../components/steps';

export function TplNew({
  step,
  stepForward,
  stepBack,
  stepCodes,
  stepMaps,
  checkItemsMap,
  setTicketNewStep2Fields,
  setTicketNewCheckItemsMap,
  setTicketSaveActionCreator,
  saveLoading,
  submitLoading,
  // mode,
  setTicketSubmitActionCreator,
  metaCategory,
  syncCommonData,
  checkItemDeviceMaps,
  idcTag,
  blockTag,
  setTicketNewCheckItepDeviceMaps,
  matchObjectInfoMaps,
  setTicketNewMatchObjectInfoMaps,
  templateId,
  setTicketNewInfo,
  setStepCodes,
  saveBatchSettingCheckPoint,
  batchSettingCheckedPoint,
  setTicketNewCustomizeUploadFileLoading,
  customizeUploadFileLoading,
}) {
  /**
   * @type {React.RefObject<import('./../components/base-info-form').BaseInfoForm>}
   */
  const baseInfoFormRef = useRef();
  const addStepBtnRef = useRef();
  const checkItemsRef = useRef();
  const matchingFacilitiesRef = useRef();

  useEffect(() => {
    syncCommonData();
  }, [syncCommonData]);

  useEffect(() => {
    return () => setTicketNewInfo(INIT_TICKET_DATA);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  let showPrev;
  let onPrev;
  let onNext;
  let onSubmit;
  let onSave;
  if (step === 0) {
    onNext = async () => {
      try {
        await baseInfoFormRef.current.validateFieldsAndScroll();
        stepForward(templateId && templateId.key ? step + 1 : step);
      } catch (error) {
        // ignore...
      }
    };
    onSave = async () => {
      try {
        await baseInfoFormRef.current.validateFieldsAndScroll();
        setTicketSaveActionCreator({ mode: 'new' });
      } catch (error) {
        // ignore...
      }
    };
  }
  if (step === 1) {
    onNext = () => {
      if (stepCodes.length) {
        stepForward(step);
      } else {
        addStepBtnRef.current.animateOnece();
      }
    };
    onSave = () => {
      if (stepCodes.length) {
        setTicketSaveActionCreator({ mode: 'new' });
      } else {
        addStepBtnRef.current.animateOnece();
      }
    };
  }
  if (step === 2) {
    if (templateId && templateId.value) {
      onPrev = () => stepBack(step - 1);
    }
    onNext = () => {
      checkItemsRef.current &&
        checkItemsRef.current.form.validateFieldsAndScroll(
          { scroll: { offsetTop: 80, offsetBottom: 100 } },
          errs => {
            if (errs) {
              return;
            }
            stepForward(step);
          }
        );
    };
    onSave = () => {
      checkItemsRef.current &&
        checkItemsRef.current.form.validateFieldsAndScroll(
          { scroll: { offsetTop: 80, offsetBottom: 100 } },
          errs => {
            if (errs) {
              return;
            }
            setTicketSaveActionCreator({ mode: 'new' });
          }
        );
    };
  }
  if (step === 3) {
    // onNext = () => {
    //   checkItemsRef.current &&
    //     checkItemsRef.current.form.validateFields(errs => {
    //       if (errs) {
    //         return;
    //       }
    //       // stepForward(step);
    //     });
    // };
    onSubmit = () => {
      matchingFacilitiesRef.current &&
        matchingFacilitiesRef.current.form.validateFieldsAndScroll(
          { scroll: { offsetTop: 80, offsetBottom: 100 } },
          errs => {
            if (errs) {
              return;
            }
            setTicketSubmitActionCreator({ mode: 'new', id: null });
          }
        );
    };
    onSave = () => {
      matchingFacilitiesRef.current &&
        matchingFacilitiesRef.current.form.validateFieldsAndScroll(
          { scroll: { offsetTop: 80, offsetBottom: 100 } },
          errs => {
            if (errs) {
              return;
            }
            setTicketSaveActionCreator({ mode: 'new' });
          }
        );
    };
  }

  if (step > 0) {
    onPrev = () => stepBack(step);
    showPrev = true;
  }

  if (step === 2 && templateId && templateId.key) {
    showPrev = true;
    onPrev = () => stepBack(step - 1);
  }

  const addStep = value => {
    setTicketNewStep2Fields(value);
  };

  const editStep = value => {
    setTicketNewStep2Fields(value);
  };

  const deleteStep = value => {
    setTicketNewStep2Fields(value);
  };

  const addCheckItemsMap = value => {
    setTicketNewCheckItemsMap(value);
  };

  // const onSave = () => {
  //   // if (checkItemsRef.current) {
  //   //   checkItemsRef.current.form.validateFields(errs => {
  //   //     if (errs) {
  //   //       return;
  //   //     }
  //   //     setTicketSaveActionCreator({ mode: 'new', id: null });
  //   //   });
  //   // } else {
  //   //   setTicketSaveActionCreator({ mode: 'new', id: null });
  //   // }
  //   if (addStepBtnRef.current.animateOnece) {
  //     addStepBtnRef.current.animateOnece();
  //   } else {
  //     setTicketSaveActionCreator({ mode: 'new' });
  //   }
  // };

  const setCheckItemDeviceMaps = checkItemDeviceMaps => {
    setTicketNewCheckItepDeviceMaps(checkItemDeviceMaps);
  };

  const setStepMatchObjectInfoMaps = matchObjectInfoMaps => {
    setTicketNewMatchObjectInfoMaps(matchObjectInfoMaps);
  };
  return (
    <GutterWrapper mode="vertical" size="2rem" style={{ marginBottom: 32 }}>
      <TinyCard>
        <Typography.Title level={4}>新增变更</Typography.Title>
        <Typography.Paragraph>
          选择变更模板后，变更配置中自动匹配变更流程，仅需要匹配对应设备和包间即可
        </Typography.Paragraph>
      </TinyCard>
      <TinyCard>
        <GutterWrapper mode="vertical" size="2rem" style={{ marginBottom: 32 }}>
          <Steps current={step} />
          {step === 0 && <BaseInfoForm ref={baseInfoFormRef} mode="ticketNew" />}
          {step === 1 && (
            <StepTableList
              // mode="addSteps"
              addStepBtnRef={addStepBtnRef}
              stepCodes={stepCodes}
              stepMaps={stepMaps}
              // TicketNew={TicketNew}
              addStep={addStep}
              step={step}
              editStep={editStep}
              deleteStep={deleteStep}
              idcTag={idcTag}
              blockTag={blockTag}
              setStepMatchObjectInfoMaps={setStepMatchObjectInfoMaps}
              matchObjectInfoMaps={matchObjectInfoMaps}
              checkItemDeviceMaps={checkItemDeviceMaps}
              // checkItemDeviceMaps={checkItemDeviceMaps}
              setCheckItemDeviceMaps={setCheckItemDeviceMaps}
              addCheckItemsMap={addCheckItemsMap}
              setStepCodes={setStepCodes}
              setCustomizeUploadFileLoading={setTicketNewCustomizeUploadFileLoading}
              checkItemsMap={checkItemsMap}
              customizeUploadFileLoading={customizeUploadFileLoading}
            />
          )}
          {templateId && !templateId.key && step === 2 && (
            <StepTableList
              wrappedComponentRef={checkItemsRef}
              stepCodes={stepCodes}
              stepMaps={stepMaps}
              step={step}
              addCheckItemsMap={addCheckItemsMap}
              checkItemsMap={checkItemsMap}
              addStep={addStep}
              type="new"
              idcTag={idcTag}
              blockTag={blockTag}
              setStepMatchObjectInfoMaps={setStepMatchObjectInfoMaps}
              matchObjectInfoMaps={matchObjectInfoMaps}
              checkItemDeviceMaps={checkItemDeviceMaps}
              // checkItemDeviceMaps={checkItemDeviceMaps}
              setCheckItemDeviceMaps={setCheckItemDeviceMaps}
              setStepCodes={setStepCodes}
              onSaveBatchSettingCheckPoint={saveBatchSettingCheckPoint}
              setCustomizeUploadFileLoading={setTicketNewCustomizeUploadFileLoading}
              batchSettingCheckedPoint={batchSettingCheckedPoint}
              customizeUploadFileLoading={customizeUploadFileLoading}
            />
          )}
          {templateId && templateId.key && step === 2 && (
            <StepTableList
              wrappedComponentRef={checkItemsRef}
              stepCodes={stepCodes}
              stepMaps={stepMaps}
              step={step}
              addCheckItemsMap={addCheckItemsMap}
              checkItemsMap={checkItemsMap}
              addStep={addStep}
              type="newByTemplate"
              idcTag={idcTag}
              blockTag={blockTag}
              setStepMatchObjectInfoMaps={setStepMatchObjectInfoMaps}
              matchObjectInfoMaps={matchObjectInfoMaps}
              checkItemDeviceMaps={checkItemDeviceMaps}
              // checkItemDeviceMaps={checkItemDeviceMaps}
              setCheckItemDeviceMaps={setCheckItemDeviceMaps}
              setStepCodes={setStepCodes}
              onSaveBatchSettingCheckPoint={saveBatchSettingCheckPoint}
              setCustomizeUploadFileLoading={setTicketNewCustomizeUploadFileLoading}
              batchSettingCheckedPoint={batchSettingCheckedPoint}
              customizeUploadFileLoading={customizeUploadFileLoading}
            />
          )}
          {step === 3 && (
            <StepTableList
              wrappedComponentRef={matchingFacilitiesRef}
              stepCodes={stepCodes}
              stepMaps={stepMaps}
              step={step}
              addCheckItemsMap={addCheckItemsMap}
              checkItemsMap={checkItemsMap}
              type="new"
              idcTag={idcTag}
              blockTag={blockTag}
              setStepMatchObjectInfoMaps={setStepMatchObjectInfoMaps}
              matchObjectInfoMaps={matchObjectInfoMaps}
              checkItemDeviceMaps={checkItemDeviceMaps}
              // checkItemDeviceMaps={checkItemDeviceMaps}
              setCheckItemDeviceMaps={setCheckItemDeviceMaps}
              setStepCodes={setStepCodes}
              onSaveBatchSettingCheckPoint={saveBatchSettingCheckPoint}
              setCustomizeUploadFileLoading={setTicketNewCustomizeUploadFileLoading}
              batchSettingCheckedPoint={batchSettingCheckedPoint}
              customizeUploadFileLoading={customizeUploadFileLoading}
            />
          )}
          {step === 4 && <Result mode="new" />}
        </GutterWrapper>
        {step !== 4 && (
          <FooterToolBar>
            <StepButtons
              showPrev={showPrev}
              showSubmit={step === 3}
              onSubmit={onSubmit}
              onPrev={onPrev}
              onNext={onNext}
              showNext={[0, 1, 2].includes(step)}
              onSave={onSave}
              submitLoading={submitLoading}
              saveLoading={saveLoading}
            />
          </FooterToolBar>
        )}
      </TinyCard>
    </GutterWrapper>
  );
}

const mapStateToProps = ({
  change: {
    ticketNew: {
      step,
      step2: { stepCodes, stepMaps },
      checkItemsMap,
      saveLoading,
      submitLoading,
      checkItemDeviceMaps,
      matchObjectInfoMaps,
      step1: {
        location,
        templateId: { value },
      },
      batchSettingCheckedPoint,
      customizeUploadFileLoading,
    },
  },
  common: { deviceCategory },
}) => ({
  step,
  checkItemsMap,
  stepCodes,
  stepMaps,
  saveLoading,
  submitLoading,
  metaCategory: deviceCategory ? deviceCategory.normalizedList : null,
  idcTag: location.value ? location.value[0] : null,
  blockTag: location.value ? location.value[1] : null,
  checkItemDeviceMaps,
  matchObjectInfoMaps,
  templateId: value || {},
  batchSettingCheckedPoint,
  customizeUploadFileLoading,
});
const mapDispatchToProps = dispatch => ({
  stepForward: step => dispatch(changeActions.setTicketNewStep(step + 1)),
  stepBack: step => dispatch(changeActions.setTicketNewStep(step - 1)),
  setTicketNewStep2Fields: values => dispatch(changeActions.setTicketNewStep2Fields(values)),
  setTicketNewCheckItemsMap: values => dispatch(changeActions.setTicketNewCheckItemsMap(values)),
  setTicketSaveActionCreator: values => dispatch(setTicketSaveActionCreator(values)),
  setTicketSubmitActionCreator: values => dispatch(setTicketSubmitActionCreator(values)),
  syncCommonData: values => dispatch(syncCommonDataActionCreator(values)),
  setTicketNewCheckItepDeviceMaps: values =>
    dispatch(changeActions.setTicketNewCheckItepDeviceMaps(values)),
  setTicketNewMatchObjectInfoMaps: values =>
    dispatch(changeActions.setTicketNewMatchObjectInfoMaps(values)),
  setTicketNewInfo: values => dispatch(changeActions.setTicketNewInfo(values)),
  setStepCodes: values => dispatch(changeActions.setTicketNewStepCodes(values)),
  saveBatchSettingCheckPoint: values =>
    dispatch(changeActions.setTicketNewBatchSettingCheckedPoint(values)),
  setTicketNewCustomizeUploadFileLoading: values =>
    dispatch(changeActions.setTicketNewCustomizeUploadFileLoading(values)),
});

export default connect(mapStateToProps, mapDispatchToProps)(TplNew);
