import Form from '@ant-design/compatible/es/form';
import UploadOutlined from '@ant-design/icons/es/icons/UploadOutlined';
import { ApiSelect } from '@galiojs/awesome-antd';
import moment from 'moment';
import React, { useEffect } from 'react';
import { connect, useSelector } from 'react-redux';

import { RoleSelect } from '@manyun/auth-hub.ui.role-select';
import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Input } from '@manyun/base-ui.ui.input';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { McUpload } from '@manyun/dc-brain.ui.upload';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { useNeedUploadChangeCustomerAgreement } from '@manyun/ticket.gql.client.tickets';

import { LocationCascader } from '@manyun/dc-brain.legacy.components';
import ChangeReasonsSelect from '@manyun/dc-brain.legacy.pages/change/components/change-reason-select';
import {
  changeActions,
  getTemplateDetailInfo,
} from '@manyun/dc-brain.legacy.redux/actions/changeActions';
import { fetchAvailableTemplate } from '@manyun/dc-brain.legacy.services/changeService';

import ChangeTypesTreeSelect from './../../../components/change-types-tree-select';
import RiskLevelSelect from './../../../components/risk-level-select';

// import EmergencyLevelSelect from './../../../components/emergrncy-level-select';

/**
 * @typedef Props
 * @property {import('antd-3/lib/form/Form').WrappedFormUtils} form
 */

/**
 * @augments {React.PureComponent<Props>}
 */

export function BaseInfoForm({
  form,
  mode,
  fields,
  setTicketNewInfo,
  setTicketEditInfo,
  getTemplateDetailInfo,
  resetTicketNewMaps,
  resetTicketEditMaps,
}) {
  const config = useSelector(selectCurrentConfig);

  const configUtil = new ConfigUtil(config);
  const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket');

  const changesTicketCustomerAgreement =
    ticketScopeCommonConfigs.changes.features.customerAgreement;
  const isChangesTicketCustomerAgreementEnabled = changesTicketCustomerAgreement !== 'disabled';
  const { getFieldDecorator, getFieldValue } = form;

  const [needUploadChangeCustomerAgreement, { data }] = useNeedUploadChangeCustomerAgreement();

  useEffect(() => {
    fields.riskLevel?.value &&
      needUploadChangeCustomerAgreement({
        variables: {
          changeLevel: fields.riskLevel.value,
          type: 'CUSTOMER_AGREE',
        },
      });
  }, [fields.riskLevel, needUploadChangeCustomerAgreement]);

  const onChangeTemplate = template => {
    if (!template) {
      const info = {
        step1: {
          ...fields,
          changeType: {},
          riskLevel: {},
          exeUserGroupCode: {},
          executeRoleCode: '',
          executeRoleName: '',
        },
        step2: {
          stepCodes: [],
          stepMaps: {},
        },
        step: 0,
        result: {},
        checkItemDeviceMaps: {},
        checkItemsMap: null,
        matchObjectInfoMaps: {},
      };
      if (mode === 'ticketNew') {
        setTicketNewInfo(info);
      }
      if (mode === 'ticketEdit') {
        setTicketEditInfo(info);
      }
      return;
    }
    getTemplateDetailInfo({ id: template.key, mode: mode });
  };

  const onChangeIdcBlock = () => {
    if (mode === 'ticketNew') {
      resetTicketNewMaps();
    }
    if (mode === 'ticketEdit') {
      resetTicketEditMaps();
    }
  };

  const disabledDate = current => {
    return current && current < moment().subtract(1, 'days');
  };

  const endTimeDisabledDate = current => {
    const startTime = getFieldValue('planStartTime');
    return current && current < startTime;
  };

  return (
    <Form
      style={{ marginLeft: 'auto', marginRight: 'auto' }}
      labelCol={{ xl: 10 }}
      wrapperCol={{ xl: 4 }}
      colon={false}
    >
      <Form.Item label="变更标题">
        {getFieldDecorator('title', {
          rules: [
            {
              required: true,
              message: '变更标题必填！',
            },
            {
              max: 50,
              message: '最多输入 50 个字符！',
            },
          ],
        })(<Input allowClear />)}
      </Form.Item>
      <Form.Item label="机房/楼">
        {getFieldDecorator('location', {
          rules: [
            {
              required: true,
              message: '机房/楼必选！',
            },
            {
              required: true,
              type: 'array',
              len: 2,
              message: '必须选到楼！',
            },
          ],
        })(
          <LocationCascader
            authorizedOnly
            allowClear={false}
            changeOnSelect={false}
            onChange={onChangeIdcBlock}
          />
        )}
      </Form.Item>
      <Form.Item label="变更模板">
        {getFieldDecorator('templateId')(
          <ApiSelect
            trigger="onDidMount"
            labelInValue
            dataService={async () => {
              const { response } = await fetchAvailableTemplate();
              if (response) {
                return Promise.resolve(response.data);
              } else {
                return Promise.resolve([]);
              }
            }}
            allowClear
            fieldNames={{ label: 'templateName', value: 'templateId' }}
            onChange={onChangeTemplate}
          />
        )}
      </Form.Item>
      <Form.Item label="变更专业">
        {getFieldDecorator('reason', {
          rules: [
            {
              required: true,
              message: '变更专业必填！',
            },
          ],
        })(<ChangeReasonsSelect />)}
      </Form.Item>
      <Form.Item label="执行角色">
        {getFieldDecorator('exeUserGroupCode', {
          rules: [{ required: true, message: '执行角色必填！' }],
        })(
          <RoleSelect
            trigger="onDidMount"
            labelInValue
            fieldNames={{ label: 'name', key: 'code', value: 'code' }}
            disabled={getFieldValue('templateId') && !!getFieldValue('templateId').key}
          />
        )}
      </Form.Item>
      <Form.Item label="变更类型">
        {getFieldDecorator('changeType', {
          rules: [
            {
              required: true,
              validator: (_, value, callback) => {
                if (!value?.key) {
                  callback('变更类型必填！');
                } else {
                  callback();
                }
              },
            },
          ],
        })(
          <ChangeTypesTreeSelect
            labelInValue
            disabled={form.getFieldValue('templateId') && !!form.getFieldValue('templateId').key}
          />
        )}
      </Form.Item>
      <Form.Item label="变更等级">
        {getFieldDecorator('riskLevel', {
          rules: [
            {
              required: true,
              message: '变更等级必填！',
            },
          ],
        })(
          <RiskLevelSelect
            disabled={form.getFieldValue('templateId') && !!form.getFieldValue('templateId').key}
          />
        )}
      </Form.Item>
      <Form.Item label="变更计划开始时间">
        {getFieldDecorator('planStartTime', {
          rules: [
            {
              required: true,
              message: '变更计划开始时间必填！',
            },
          ],
        })(
          <DatePicker
            disabledDate={disabledDate}
            showTime
            format="YYYY-MM-DD HH:mm"
            onChange={() => {
              form.setFieldsValue({ planEndTime: null });
            }}
          />
        )}
      </Form.Item>
      <Form.Item label="变更计划完成时间">
        {getFieldDecorator('planEndTime', {
          rules: [
            {
              required: true,
              message: '变更计划完成时间必填！',
            },
          ],
        })(
          <DatePicker
            disabledDate={endTimeDisabledDate}
            disabled={!form.getFieldValue('planStartTime')}
            showTime
            format="YYYY-MM-DD HH:mm"
          />
        )}
      </Form.Item>
      {isChangesTicketCustomerAgreementEnabled &&
        getFieldValue('riskLevel') &&
        data?.needUploadChangeCustomerAgreement?.data && (
          <Form.Item
            label="客户同意书"
            getValueFromEvent={({ fileList }) => {
              return fileList;
            }}
            wrapperCol={{ xl: 9 }}
          >
            {getFieldDecorator('customerFileInfoList', {
              getValueFromEvent: ({ fileList }) => {
                return fileList;
              },
              valuePropName: 'fileList',
            })(
              <McUpload
                showAccept
                accept=".zip,.rar,.7z,.mp4,.3gp,.mov,.jpg,.jpeg,.png,.doc,.docx,.xls,.xlsx,.pdf"
                showUploadList
                allowDelete
                maxCount={3}
                openFileDialogOnClick={
                  getFieldValue('customerFileInfoList') === undefined ||
                  getFieldValue('customerFileInfoList')?.length < 3
                }
              >
                <Button
                  disabled={getFieldValue('customerFileInfoList')?.length >= 3}
                  icon={<UploadOutlined />}
                >
                  点此上传
                </Button>
              </McUpload>
            )}
          </Form.Item>
        )}
      <Form.Item
        label="附件"
        getValueFromEvent={({ fileList }) => {
          return fileList;
        }}
        wrapperCol={{ xl: 9 }}
      >
        {getFieldDecorator('fileInfoList', {
          getValueFromEvent: ({ fileList }) => {
            return fileList;
          },
          valuePropName: 'fileList',
        })(
          <McUpload
            showAccept
            accept=".zip,.rar,.7z,.mp4,.3gp,.mov,.jpg,.jpeg,.png,.doc,.docx,.xls,.xlsx,.pdf"
            showUploadList
            allowDelete
            maxCount={1}
            openFileDialogOnClick={
              getFieldValue('fileInfoList') === undefined ||
              getFieldValue('fileInfoList')?.length < 1
            }
          >
            <Button icon={<UploadOutlined />} disabled={getFieldValue('fileInfoList')?.length >= 1}>
              点此上传
            </Button>
          </McUpload>
        )}
      </Form.Item>
    </Form>
  );
}

const mapStateToProps = ({ change: { ticketNew, ticketEdit } }, { mode }) => {
  let fields = {};
  if (mode === 'ticketEdit') {
    fields = ticketEdit.step1;
  }
  if (mode === 'ticketNew') {
    fields = ticketNew.step1;
  }
  return { fields };
};

const mapDispatchToProps = {
  onFieldsChangeNew: changeActions.setTicketNewStep1Fields,
  onFieldsChangeEdit: changeActions.setTicketEditStep1Fields,
  getTemplateDetailInfo: getTemplateDetailInfo,
  setTicketEditInfo: changeActions.setTicketEditInfo,
  setTicketNewInfo: changeActions.setTicketNewInfo,
  resetTicketNewMaps: changeActions.resetTicketNewMaps,
  resetTicketEditMaps: changeActions.resetTicketEditMaps,
};

const connectOpts = { forwardRef: true };

/**
 * @type {import('antd-3/lib/form').FormCreateOption}
 */
const createOptions = {
  onFieldsChange({ mode, onFieldsChangeNew, onFieldsChangeEdit }, changedFields) {
    if (mode === 'ticketEdit') {
      onFieldsChangeEdit(changedFields);
    }
    if (mode === 'ticketNew') {
      onFieldsChangeNew(changedFields);
    }
  },
  mapPropsToFields({ fields }) {
    return Object.keys(fields).reduce((formFields, fieldKey) => {
      if (fieldKey === 'templateId' && !fields[fieldKey].value?.key) {
        formFields[fieldKey] = Form.createFormField({ value: { key: '', label: '' } });
      } else {
        formFields[fieldKey] = Form.createFormField(fields[fieldKey]);
      }

      return formFields;
    }, {});
  },
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
  null,
  connectOpts
)(Form.create(createOptions)(BaseInfoForm));
