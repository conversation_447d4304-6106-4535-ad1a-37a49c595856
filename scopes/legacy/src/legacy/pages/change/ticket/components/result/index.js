import React from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import CheckCircleFilled from '@ant-design/icons/es/icons/CheckCircleFilled';
import ExclamationCircleFilled from '@ant-design/icons/es/icons/ExclamationCircleFilled';
import moment from 'moment';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';

import { CHANGE_RISK_LEVEL_TEXT_MAP } from '@manyun/ticket.model.change';

import { ApproveLink } from '@manyun/dc-brain.legacy.components';
import * as urls from '@manyun/dc-brain.legacy.constants/urls';
import { changeActions } from '@manyun/dc-brain.legacy.redux/actions/changeActions';
import { redirectActionCreator } from '@manyun/dc-brain.legacy.redux/actions/routerActions';

import { INIT_TICKET_DATA } from '../../../constants';
import { StyledResult } from './styled';

export function TicketResultStep({ result, mode, setTicketNewInfo }) {
  const title = mode === 'new' ? '创建成功' : '修改成功';
  const needWorkflow = result?.workflowId && result?.workflowId !== '0';
  return (
    <StyledResult
      // status="warning"
      title={needWorkflow ? '待审批' : title}
      subTitle={needWorkflow ? '当前变更需要审批才可生效' : null}
      icon={
        needWorkflow ? (
          <ExclamationCircleFilled style={{ color: `var(--${prefixCls}-warning-color)` }} />
        ) : (
          <CheckCircleFilled style={{ color: `var(--${prefixCls}-success-color)` }} />
        )
      }
      extra={[
        <Link key="new_btn" to={{ pathname: urls.CHANGE_TICKET_NEW }}>
          <Button
            key="refresh-button"
            type="primary"
            onClick={() => {
              mode === 'new' && window.location.reload();
            }}
          >
            新增变更
          </Button>
        </Link>,
        <Link key="goback" to={{ pathname: urls.CHANGE_TICKET_LIST }}>
          <Button
            key="go-back-button"
            onClick={() => {
              setTicketNewInfo(INIT_TICKET_DATA);
            }}
          >
            返回列表
          </Button>
        </Link>,
      ]}
    >
      <Descriptions style={{ width: 400, marginLeft: 'auto', marginRight: 'auto' }} column={1}>
        <Descriptions.Item label="变更ID">{result.id}</Descriptions.Item>

        <Descriptions.Item label="变更标题">{result.title}</Descriptions.Item>
        <Descriptions.Item label="变更模板">
          {result.templateCode && result.templateCode !== 'NULL' ? result.templateCode : '无'}
        </Descriptions.Item>
        <Descriptions.Item label="机房">{result.idcTag}</Descriptions.Item>
        <Descriptions.Item label="楼栋">{result.blockTag}</Descriptions.Item>
        {/* <Descriptions.Item label="紧急度">
          {CHANGE_EMERGENCY_LEVEL_TEXT_MAP[result.urgency]}
        </Descriptions.Item> */}
        <Descriptions.Item label="变更类型">{result.changeType}</Descriptions.Item>
        <Descriptions.Item label="变更等级">
          {CHANGE_RISK_LEVEL_TEXT_MAP[result.riskLevel]}
        </Descriptions.Item>
        <Descriptions.Item label="变更计划时间">{`${moment(result.planStartTime).format(
          'YYYY-MM-DD HH:mm:ss'
        )}-${moment(result.planEndTime).format('YYYY-MM-DD HH:mm:ss')}`}</Descriptions.Item>
        <Descriptions.Item label="变更专业">{result.changeReason}</Descriptions.Item>
        {needWorkflow && (
          <Descriptions.Item label="审批">
            <ApproveLink id={result.workflowId} />
          </Descriptions.Item>
        )}
      </Descriptions>
    </StyledResult>
  );
}

const mapStateToProps = ({ change: { ticketNew, ticketEdit } }, { mode }) => {
  if (mode === 'new') {
    return {
      result: ticketNew.result,
      mode,
    };
  }
  if (mode === 'edit') {
    return {
      result: ticketEdit.result,
      mode,
    };
  }
};

const mapDispatchToProps = {
  redirect: redirectActionCreator,
  setTicketNewInfo: changeActions.setTicketNewInfo,
};

export default connect(mapStateToProps, mapDispatchToProps)(TicketResultStep);
