import React, { useEffect, useRef } from 'react';
import { connect } from 'react-redux';
import { useLocation, useParams } from 'react-router-dom';

import { Typography } from '@manyun/base-ui.ui.typography';

import { <PERSON>er<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rapper, TinyCard } from '@manyun/dc-brain.legacy.components';
import {
  changeActions,
  getTicketDetailInfo,
  setTicketSaveActionCreator,
  setTicketSubmitActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/changeActions';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import { getLocationSearchMap } from '@manyun/dc-brain.legacy.utils';

import StepButtons from '../../components/step-buttons';
import StepTableList from '../../components/step-table-list';
import { INIT_TICKET_DATA } from '../../constants';
import BaseInfoForm from '../components/base-info-form';
import Result from '../components/result';
import Steps from '../components/steps';

export function TicketEdit({
  step,
  stepForward,
  stepBack,
  stepCodes,
  stepMaps,
  checkItemsMap,
  setTicketEditStep2Fields,
  setTicketEditCheckItemsMap,
  setTicketSaveActionCreator,
  saveLoading,
  submitLoading,
  // mode,
  setTicketSubmitActionCreator,
  metaCategory,
  syncCommonData,
  checkItemDeviceMaps,
  idcTag,
  blockTag,
  setTicketEditCheckItepDeviceMaps,
  matchObjectInfoMaps,
  setTicketEditMatchObjectInfoMaps,
  templateId,
  getTicketDetailInfo,
  setStepCodes,
  setTicketEditStep,
  setTicketEditInfo,
  batchSettingCheckedPoint,
  saveBatchSettingCheckPoint,
  setTicketEditCustomizeUploadFileLoading,
  customizeUploadFileLoading,
}) {
  /**
   * @type {React.RefObject<import('../components/base-info-form').BaseInfoForm>}
   */
  const baseInfoFormRef = useRef();
  const addStepBtnRef = useRef();
  const checkItemsRef = useRef();
  const matchingFacilitiesRef = useRef();
  const { id } = useParams();

  const { search } = useLocation();
  const { stepCurrent } = getLocationSearchMap(search, ['stepCurrent']);

  useEffect(() => {
    syncCommonData();
    getTicketDetailInfo({ id, mode: 'ticketEdit', stepCurrent });
  }, [syncCommonData, id, getTicketDetailInfo, stepCurrent]);

  // useEffect(() => {
  //   setTicketEditStep(stepCurrent);
  //   // setTemplateEditStep(stepCurrent);
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [stepCurrent]);

  useEffect(() => {
    return () => setTicketEditInfo(INIT_TICKET_DATA);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  let showPrev;
  let onPrev;
  let onNext;
  let onSubmit;
  let onSave;
  if (step === 0) {
    onNext = async () => {
      try {
        await baseInfoFormRef.current.validateFieldsAndScroll();
        stepForward(templateId && templateId.key ? step + 1 : step);
      } catch (error) {
        // ignore...
      }
    };
    onSave = async () => {
      try {
        await baseInfoFormRef.current.validateFieldsAndScroll();
        setTicketSaveActionCreator({ id, mode: 'edit' });
      } catch (error) {
        // ignore...
      }
    };
  }

  if (step === 1) {
    onNext = () => {
      if (stepCodes.length) {
        stepForward(step);
      } else {
        addStepBtnRef.current.animateOnece();
      }
    };
    onSave = () => {
      if (stepCodes.length) {
        setTicketSaveActionCreator({ id, mode: 'edit' });
      } else {
        addStepBtnRef.current.animateOnece();
      }
    };
  }

  if (step === 2) {
    if (templateId && templateId.value) {
      onPrev = () => stepBack(step + 1);
    }
    onNext = () => {
      checkItemsRef.current &&
        checkItemsRef.current.form.validateFieldsAndScroll(
          { scroll: { offsetTop: 80, offsetBottom: 100 } },
          errs => {
            if (errs) {
              return;
            }
            stepForward(step);
          }
        );
    };
    onSave = () => {
      checkItemsRef.current &&
        checkItemsRef.current.form.validateFieldsAndScroll(
          { scroll: { offsetTop: 80, offsetBottom: 100 } },
          errs => {
            if (errs) {
              return;
            }
            setTicketSaveActionCreator({ id, mode: 'edit' });
          }
        );
    };
  }

  if (step === 3) {
    onSubmit = () => {
      matchingFacilitiesRef.current &&
        matchingFacilitiesRef.current.form.validateFieldsAndScroll(
          { scroll: { offsetTop: 80, offsetBottom: 100 } },
          errs => {
            if (errs) {
              return;
            }
            setTicketSubmitActionCreator({ mode: 'edit', id });
          }
        );
    };
    onSave = () => {
      matchingFacilitiesRef.current &&
        matchingFacilitiesRef.current.form.validateFieldsAndScroll(
          { scroll: { offsetTop: 80, offsetBottom: 100 } },
          errs => {
            if (errs) {
              return;
            }
            setTicketSaveActionCreator({ mode: 'edit', id });
          }
        );
    };
  }

  if (step > 0) {
    onPrev = () => stepBack(step);
    showPrev = true;
  }

  if (step === 2 && templateId && templateId.key) {
    showPrev = true;
    onPrev = () => stepBack(step - 1);
  }

  const addStep = value => {
    setTicketEditStep2Fields(value);
  };

  const editStep = value => {
    setTicketEditStep2Fields(value);
  };

  const deleteStep = value => {
    setTicketEditStep2Fields(value);
  };

  const addCheckItemsMap = value => {
    setTicketEditCheckItemsMap(value);
  };

  // const onSave = () => {
  //   if (addStepBtnRef.current.animateOnece) {
  //     addStepBtnRef.current.animateOnece();
  //   } else {
  //     setTicketSaveActionCreator({ id, mode: 'edit' });
  //   }

  //   if (checkItemsRef.current) {
  //     checkItemsRef.current.form.validateFields(errs => {
  //       if (errs) {
  //         return;
  //       }
  //       setTicketSaveActionCreator({ mode: 'edit', id });
  //     });
  //   }
  //   if (matchingFacilitiesRef.current) {
  //     matchingFacilitiesRef.current.form.validateFields(errs => {
  //       if (errs) {
  //         return;
  //       }
  //       setTicketSaveActionCreator({ mode: 'edit', id });
  //     });
  //   } else {
  //     setTicketSaveActionCreator({ mode: 'edit', id });
  //   }
  // };

  const setCheckItemDeviceMaps = checkItemDeviceMaps => {
    setTicketEditCheckItepDeviceMaps(checkItemDeviceMaps);
  };

  const setStepMatchObjectInfoMaps = matchObjectInfoMaps => {
    setTicketEditMatchObjectInfoMaps(matchObjectInfoMaps);
  };

  return (
    <GutterWrapper mode="vertical" size="2rem" style={{ marginBottom: 32 }}>
      <TinyCard>
        <Typography.Title level={4}>新增变更</Typography.Title>
        <Typography.Paragraph>
          选择变更模板后，变更配置中自动匹配变更流程，仅需要匹配对应设备和包间即可
        </Typography.Paragraph>
      </TinyCard>
      <TinyCard>
        <GutterWrapper mode="vertical" size="2rem" style={{ marginBottom: 32 }}>
          <Steps current={step} />
          {step === 0 && <BaseInfoForm ref={baseInfoFormRef} mode="ticketEdit" />}
          {step === 1 && (
            <StepTableList
              // mode="addSteps"
              addStepBtnRef={addStepBtnRef}
              stepCodes={stepCodes}
              stepMaps={stepMaps}
              // TicketEdit={TicketEdit}
              addStep={addStep}
              step={step}
              editStep={editStep}
              deleteStep={deleteStep}
              addCheckItemsMap={addCheckItemsMap}
              checkItemsMap={checkItemsMap}
              setStepCodes={setStepCodes}
              setStepMatchObjectInfoMaps={setStepMatchObjectInfoMaps}
              setCheckItemDeviceMaps={setCheckItemDeviceMaps}
              checkItemDeviceMaps={checkItemDeviceMaps}
              setCustomizeUploadFileLoading={setTicketEditCustomizeUploadFileLoading}
              customizeUploadFileLoading={customizeUploadFileLoading}
            />
          )}
          {templateId && !templateId.key && step === 2 && (
            <StepTableList
              wrappedComponentRef={checkItemsRef}
              stepCodes={stepCodes}
              stepMaps={stepMaps}
              step={step}
              addCheckItemsMap={addCheckItemsMap}
              checkItemsMap={checkItemsMap}
              addStep={addStep}
              type="edit"
              idcTag={idcTag}
              blockTag={blockTag}
              setStepMatchObjectInfoMaps={setStepMatchObjectInfoMaps}
              matchObjectInfoMaps={matchObjectInfoMaps}
              checkItemDeviceMaps={checkItemDeviceMaps}
              setCheckItemDeviceMaps={setCheckItemDeviceMaps}
              setStepCodes={setStepCodes}
              onSaveBatchSettingCheckPoint={saveBatchSettingCheckPoint}
              setCustomizeUploadFileLoading={setTicketEditCustomizeUploadFileLoading}
              batchSettingCheckedPoint={batchSettingCheckedPoint}
              customizeUploadFileLoading={customizeUploadFileLoading}
            />
          )}
          {templateId && templateId.key && step === 2 && (
            <StepTableList
              wrappedComponentRef={checkItemsRef}
              stepCodes={stepCodes}
              stepMaps={stepMaps}
              step={step}
              addCheckItemsMap={addCheckItemsMap}
              checkItemsMap={checkItemsMap}
              addStep={addStep}
              type="newByTemplate"
              idcTag={idcTag}
              blockTag={blockTag}
              setStepMatchObjectInfoMaps={setStepMatchObjectInfoMaps}
              matchObjectInfoMaps={matchObjectInfoMaps}
              checkItemDeviceMaps={checkItemDeviceMaps}
              setCheckItemDeviceMaps={setCheckItemDeviceMaps}
              setStepCodes={setStepCodes}
              onSaveBatchSettingCheckPoint={saveBatchSettingCheckPoint}
              setCustomizeUploadFileLoading={setTicketEditCustomizeUploadFileLoading}
              batchSettingCheckedPoint={batchSettingCheckedPoint}
              customizeUploadFileLoading={customizeUploadFileLoading}
            />
          )}
          {step === 3 && (
            <StepTableList
              wrappedComponentRef={matchingFacilitiesRef}
              stepCodes={stepCodes}
              stepMaps={stepMaps}
              step={step}
              addCheckItemsMap={addCheckItemsMap}
              checkItemsMap={checkItemsMap}
              type="edit"
              idcTag={idcTag}
              blockTag={blockTag}
              setStepMatchObjectInfoMaps={setStepMatchObjectInfoMaps}
              matchObjectInfoMaps={matchObjectInfoMaps}
              checkItemDeviceMaps={checkItemDeviceMaps}
              setCheckItemDeviceMaps={setCheckItemDeviceMaps}
              setStepCodes={setStepCodes}
              onSaveBatchSettingCheckPoint={saveBatchSettingCheckPoint}
              setCustomizeUploadFileLoading={setTicketEditCustomizeUploadFileLoading}
              batchSettingCheckedPoint={batchSettingCheckedPoint}
              customizeUploadFileLoading={customizeUploadFileLoading}
            />
          )}
          {step === 4 && <Result mode="edit" />}
        </GutterWrapper>
        {step !== 4 && (
          <FooterToolBar>
            <StepButtons
              showPrev={showPrev}
              showSubmit={step === 3}
              onSubmit={onSubmit}
              onPrev={onPrev}
              onNext={onNext}
              showNext={[0, 1, 2].includes(step)}
              onSave={onSave}
              submitLoading={submitLoading}
              saveLoading={saveLoading}
            />
          </FooterToolBar>
        )}
      </TinyCard>
    </GutterWrapper>
  );
}

const mapStateToProps = ({
  change: {
    ticketEdit: {
      step,
      step2: { stepCodes, stepMaps },
      checkItemsMap,
      saveLoading,
      submitLoading,

      checkItemDeviceMaps,
      matchObjectInfoMaps,
      step1: {
        location,
        templateId: { value },
      },
      batchSettingCheckedPoint,
      customizeUploadFileLoading,
    },
  },
  common: { deviceCategory },
}) => ({
  step,
  checkItemsMap,
  stepCodes,
  stepMaps,
  saveLoading,
  submitLoading,
  metaCategory: deviceCategory ? deviceCategory.normalizedList : null,
  idcTag: location.value ? location.value[0] : null,
  blockTag: location.value ? location.value[1] : null,
  checkItemDeviceMaps,
  matchObjectInfoMaps,
  templateId: value || {},
  batchSettingCheckedPoint,
  customizeUploadFileLoading,
});
const mapDispatchToProps = dispatch => ({
  stepForward: step => dispatch(changeActions.setTicketEditStep(step + 1)),
  stepBack: step => dispatch(changeActions.setTicketEditStep(step - 1)),
  setTicketEditStep2Fields: values => dispatch(changeActions.setTicketEditStep2Fields(values)),
  setTicketEditCheckItemsMap: values => dispatch(changeActions.setTicketEditCheckItemsMap(values)),
  setTicketSaveActionCreator: values => dispatch(setTicketSaveActionCreator(values)),
  setTicketSubmitActionCreator: values => dispatch(setTicketSubmitActionCreator(values)),
  syncCommonData: values => dispatch(syncCommonDataActionCreator(values)),
  setTicketEditCheckItepDeviceMaps: values =>
    dispatch(changeActions.setTicketEditCheckItepDeviceMaps(values)),
  setTicketEditMatchObjectInfoMaps: values =>
    dispatch(changeActions.setTicketEditMatchObjectInfoMaps(values)),
  getTicketDetailInfo: values => dispatch(getTicketDetailInfo(values)),
  setStepCodes: values => dispatch(changeActions.setTicketEditStepCodes(values)),
  setTicketEditStep: values => dispatch(changeActions.setTicketEditStep(values)),
  setTicketEditInfo: values => dispatch(changeActions.setTicketEditInfo(values)),
  saveBatchSettingCheckPoint: values =>
    dispatch(changeActions.setTicketEditBatchSettingCheckedPoint(values)),
  setTicketEditCustomizeUploadFileLoading: values =>
    dispatch(changeActions.setTicketEditCustomizeUploadFileLoading(values)),
});

export default connect(mapStateToProps, mapDispatchToProps)(TicketEdit);
