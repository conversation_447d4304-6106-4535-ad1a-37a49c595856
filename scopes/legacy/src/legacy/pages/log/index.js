import React, { Component, useEffect, useState } from 'react';

import { ApiSelect, FiltersForm, Form } from '@galiojs/awesome-antd';
import omit from 'lodash/omit';
import omitBy from 'lodash/omitBy';
import moment from 'moment';

import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Spin } from '@manyun/base-ui.ui.spin';

import { LogsActionTypeSelect } from '@manyun/auth-hub.ui.logs-action-type-select';
import {
  OperationLogTable,
  OperationLogTableContext,
} from '@manyun/auth-hub.ui.operation-log-table';

import { GutterWrapper, TinyCard, UserSelect } from '@manyun/dc-brain.legacy.components';
import * as logService from '@manyun/dc-brain.legacy.services/logService';

class LogList extends Component {
  state = {
    searchParams: {
      startedAt: moment().startOf('week').valueOf(),
      endedAt: moment().endOf('week').valueOf(),
    },
  };

  search = (_, { form }) => {
    const fieldsValue = form.getFieldsValue();
    if (!fieldsValue.operationTime) {
      message.error('请选择操作时间');
      return;
    }
    const params = omit(fieldsValue, 'operationTime', 'operatorName');
    params.operatorName = fieldsValue.operatorName?._userName || fieldsValue.operatorName?.userName;
    if (fieldsValue.operationTime) {
      params.endedAt = fieldsValue.operationTime.endOf('week').valueOf();
      params.startedAt = fieldsValue.operationTime.startOf('week').valueOf();
    }

    const p = omitBy(params, (value, key) => {
      if (value === null || value === undefined || value === '') {
        return key;
      }
    });
    this.setState({ searchParams: p });
  };

  render() {
    const { searchParams } = this.state;

    return (
      <GutterWrapper mode="vertical">
        <TinyCard>
          <FiltersFormWrapper onSearch={this.search} onReset={this.search} />
        </TinyCard>
        <TinyCard>
          <OperationLogTableContext.Provider value={{ searchParams }}>
            <OperationLogTable
              showColumns={['id', 'targetType', 'modifyType', 'operationCount']}
              showExportButton
            />
          </OperationLogTableContext.Provider>
        </TinyCard>
      </GutterWrapper>
    );
  }
}

const FiltersFormWrapper = props => {
  const [form] = Form.useForm();
  const operationTime = Form.useWatch('operationTime', form);
  const [contentDisabled, setContentDisabled] = useState(true);
  const [targetTypeLoading, setTargetTypeLoading] = useState(false);
  useEffect(() => {
    // 操作时间为空时操作内容值清空并禁用
    if (operationTime === undefined || operationTime === null) {
      setContentDisabled(true);
      form.setFieldsValue({ content: undefined });
    } else {
      setContentDisabled(false);
    }
  }, [form, operationTime]);
  return (
    <FiltersForm
      form={form}
      contentDisabled={contentDisabled}
      items={[
        {
          label: '操作人',
          id: 'operatorName',
          control: <UserSelect allowClear showSystem />,
        },
        {
          label: '操作类型',
          id: 'modifyType',
          control: <LogsActionTypeSelect />,
        },
        {
          label: '操作模块',
          id: 'targetType',
          control: (
            <ApiSelect
              showSearch
              allowClear
              loading={targetTypeLoading}
              fieldNames={{ label: 'value', value: 'key' }}
              dataService={async () => {
                setTargetTypeLoading(true);
                const response = await logService.fetchTargetType();
                setTargetTypeLoading(false);
                if (response) {
                  return Promise.resolve(response);
                } else {
                  return Promise.resolve([]);
                }
              }}
              notFoundContent={targetTypeLoading && <Spin size="small" />}
            />
          ),
        },
        {
          label: '操作时间',
          id: 'operationTime',
          initialValue: moment(),
          span: 1,
          control: <DatePicker.WeekPicker showTime allowClear />,
        },
        {
          label: '操作内容',
          id: 'content',
          control: <Input disabled={contentDisabled} />,
        },
        {
          label: '业务 ID',
          id: 'targetId',
          control: <Input />,
        },
      ]}
      {...props}
    />
  );
};

export default LogList;
