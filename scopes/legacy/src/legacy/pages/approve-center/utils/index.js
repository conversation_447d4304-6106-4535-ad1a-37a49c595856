import { BpmInstance } from '@manyun/bpm.model.bpm-instance';

export function getBPMInstance(BackendBpmInstance) {
  return BpmInstance.fromApiObject(BackendBpmInstance);
}
export const getFormattedContent = (content, bizType, approvalTypes) => {
  const arr = BpmInstance.getFormattedContent(content);
  if (arr.length === 0) {
    return;
  }
  if ([approvalTypes.BORROW, approvalTypes.TRANSFER, approvalTypes.RENEW].includes(bizType)) {
    return arr.map(item => item.label + '：' + getUserName(item.value)).join('，');
  }
  return arr.map(item => item.label + '：' + item.value).join('，');
};

function getUserName(str) {
  if (str.includes('|')) {
    return str.split('|')[1];
  } else {
    return str;
  }
}
