import React, { useEffect } from 'react';
import { connect } from 'react-redux';

import {
  CarbonCopyOutlined,
  MineOutlined,
  StampOutlined,
  WaitOutlined,
} from '@manyun/base-ui.icons';
import { Menu } from '@manyun/base-ui.ui.menu';
import { getLocationSearchMap, setLocationSearch } from '@manyun/base-ui.util.query-string';
import { useApprovalTypes } from '@manyun/bpm.hook.use-approval-types';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';
import {
  getApproveCenterListActionCreator,
  resetSearchValuesActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/approveCenterActions';
import { approveCenterActions } from '@manyun/dc-brain.legacy.redux/reducers/approveCenterSlice';

import DataCard from './components/data-card';
import SearchForm from './components/search-card';

function ApproveCenterList({ type, setType, getData, resetSearchValues, updateSearchValues }) {
  const { type: _type, bizType: _bizType } = getLocationSearchMap(window.location.search);
  const { approvalTypeTextKeyMap } = useApprovalTypes();

  useEffect(() => {
    if (_type) {
      setType(_type);
    }
    if (_bizType) {
      const bizTypes = Object.keys(approvalTypeTextKeyMap);
      if (bizTypes.includes(_bizType)) {
        updateSearchValues({ bizType: { name: 'bizType', value: _bizType } });
      }
    }
  }, [_bizType, _type, approvalTypeTextKeyMap, setType, updateSearchValues]);

  useEffect(() => {
    if (type !== 'CC') {
      getData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [getData]);

  const onClick = key => {
    setType(key.key);
    resetSearchValues();
    setLocationSearch({ type: key.key });
  };

  return (
    <GutterWrapper flex>
      <Menu mode="vertical" selectedKeys={[type]} style={{ width: '200px' }}>
        <Menu.Item key="WAIT" onClick={onClick}>
          <StampOutlined />
          <span>待办</span>
        </Menu.Item>
        <Menu.Item key="ALREADY" onClick={onClick}>
          <WaitOutlined style={{ transform: 'rotateX(180deg)' }} />
          <span>已办</span>
        </Menu.Item>
        <Menu.Item key="MINE" onClick={onClick}>
          <MineOutlined />
          <span>已发起</span>
        </Menu.Item>
        <Menu.Item key="CC" onClick={onClick}>
          <CarbonCopyOutlined />
          <span>抄送我</span>
        </Menu.Item>
      </Menu>
      <GutterWrapper mode="vertical" flexN={1} style={{ width: 'calc(100% - 200px - 1rem)' }}>
        <SearchForm type={type} />
        <DataCard type={type} />
      </GutterWrapper>
    </GutterWrapper>
  );
}

const mapStateToProps = ({ approveCenter: { type } }) => ({
  type,
});
const mapDispatchToProps = {
  resetSearchValues: resetSearchValuesActionCreator,
  getData: getApproveCenterListActionCreator,
  setType: approveCenterActions.setType,
  updateSearchValues: approveCenterActions.updateSearchValues,
};

export default connect(mapStateToProps, mapDispatchToProps)(ApproveCenterList);
