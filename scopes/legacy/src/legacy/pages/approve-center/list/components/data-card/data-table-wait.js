import React, { useCallback, useState } from 'react';
import { connect } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { Divider } from '@manyun/base-ui.ui.divider';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useApprovalTypes } from '@manyun/bpm.hook.use-approval-types';
import { BatchOperationResult } from '@manyun/bpm.model.bpm-instance';
import { approvalBatchOperate } from '@manyun/bpm.service.approval-batch-operate';
import { ApproveModalButton } from '@manyun/bpm.ui.approve-modal-button';
import { CorrespondingOrderLink } from '@manyun/bpm.ui.corresponding-order-link';
import { SpaceText } from '@manyun/resource-hub.ui.space-text';

import { ApproveLink, Ellipsis, TinyTable, UserLink } from '@manyun/dc-brain.legacy.components';
import {
  approveCenterActions,
  getApproveCenterListActionCreator,
  setPaginationThenGetDataActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/approveCenterActions';

import { getFormattedContent } from '../../../utils';

function DataTableWait({ data, total, pageNum, pageSize, loading, setPagination, getData }) {
  const paginationChangeHandler = useCallback(
    (current, size) => {
      setPagination({ pageNum: current, pageSize: size });
    },
    [setPagination]
  );
  const [selectedIds, setSelectedIds] = useState([]);
  const [selectedRows, setSelectedRows] = useState([]);
  const { approvalTypes, approvalTypeTextKeyMap } = useApprovalTypes();
  const batchOperateWorkflows = useCallback(
    async result => {
      const { error } = await approvalBatchOperate({
        result,
        taskApprovalInfoList: selectedRows?.map(row => ({
          taskId: row.taskId,
          instId: row.instId,
        })),
      });
      if (error) {
        message.error(error.message);
        return;
      }
      message.success(`批量${result ? '拒绝' : '同意'}操作成功`);
      const newPageNum = data.length === selectedIds.length ? pageNum - 1 : pageNum;
      setPagination({ pageNum: newPageNum > 1 ? newPageNum : 1, pageSize: pageSize });

      setSelectedIds([]);
      setSelectedRows([]);
    },
    [data.length, pageNum, pageSize, selectedIds.length, selectedRows, setPagination]
  );
  const getColumns = getData => [
    {
      title: '审批ID',
      dataIndex: 'instId',
      fixed: 'left',
      render(instId) {
        return <ApproveLink id={instId} />;
      },
    },
    {
      title: '标题',
      dataIndex: 'title',
      render: (_, { title }) => {
        return (
          <Typography.Text style={{ maxWidth: 480 }} ellipsis={{ tooltip: true }}>
            {title}
          </Typography.Text>
        );
      },
    },
    {
      title: '类型',
      dataIndex: 'bizType',
      render(bizType) {
        return approvalTypeTextKeyMap[bizType];
      },
    },
    {
      title: '对应单号',
      dataIndex: 'bizId',
      render(_, record) {
        return (
          <CorrespondingOrderLink
            targetName={record.bizId}
            targetId={record.bizId}
            targetType={record.bizType}
          />
        );
      },
    },
    {
      title: '内容',
      width: 300,
      dataIndex: 'content',
      render: (_, { content, bizType }) => {
        return (
          <Typography.Text style={{ maxWidth: 300 }} ellipsis={{ tooltip: true }}>
            {getFormattedContent(content, bizType, approvalTypes)}
          </Typography.Text>
        );
      },
    },
    {
      title: '位置',
      dataIndex: 'blockGuid',
      render(_, record) {
        if (record.roomTag) {
          return <SpaceText guid={`${record.blockGuid}.${record.roomTag}`} />;
        }
        return record.blockGuid || record.idcTag ? (
          <SpaceText guid={`${record.blockGuid || record.idcTag}`} />
        ) : (
          '--'
        );
      },
    },
    {
      title: '发起人',
      dataIndex: 'creatorName',
      render(name, { creatorId }) {
        return <UserLink userId={creatorId} userName={name} />;
      },
    },
    {
      title: '发起时间',
      dataIndex: 'gmtCreate',
      dataType: 'datetime',
    },
    {
      title: '操作',
      dataIndex: 'id',
      fixed: 'right',
      render: (id, record) => {
        const needAudit = JSON.parse(record?.formJson)?.needAudit;

        return (
          <Space split={<Divider type="vertical" spaceSize="mini" emphasis />}>
            <ApproveModalButton
              instId={record.instId}
              taskId={record.taskId}
              result={0}
              text="同意"
              type="link"
              compact
              onSuccess={() => getData(false)}
            />
            <ApproveModalButton
              instId={record.instId}
              taskId={record.taskId}
              result={1}
              text="拒绝"
              type="link"
              compact
              onSuccess={() => getData(false)}
              bizType={record?.bizType}
              bizId={record?.bizId}
              needAudit={needAudit}
            />
          </Space>
        );
      },
    },
  ];
  return (
    <Space style={{ width: '100%' }} direction="vertical">
      <Space>
        <Popconfirm
          title={`确认批量同意当前${selectedRows?.length}条审批？`}
          onConfirm={() => batchOperateWorkflows(BatchOperationResult.Passed)}
        >
          <Button disabled={!selectedRows?.length} type="primary">
            批量同意
          </Button>
        </Popconfirm>
        <Popconfirm
          title={`确认批量拒绝当前${selectedRows?.length}条审批？`}
          onConfirm={() => batchOperateWorkflows(BatchOperationResult.Rejected)}
        >
          <Button disabled={!selectedRows?.length}>批量拒绝</Button>
        </Popconfirm>
      </Space>
      <TinyTable
        rowKey="id"
        columns={getColumns(getData)}
        scroll={{ x: 'max-content' }}
        align="left"
        dataSource={data}
        loading={loading}
        rowSelection={{
          selectedRowKeys: selectedIds,
          onChange: (selectedRowKeys, selectedRows) => {
            setSelectedIds(selectedRowKeys);
            setSelectedRows(selectedRows);
          },
        }}
        pagination={{
          total,
          current: pageNum,
          pageSize,
          onChange: paginationChangeHandler,
        }}
      />
    </Space>
  );
}

const mapStateToProps = ({
  approveCenter: {
    data,
    total,
    pagination: { pageNum, pageSize },
    loading,
  },
}) => ({
  data,
  total,
  loading,
  pageNum,
  pageSize,
});
const mapDispatchToProps = {
  getData: getApproveCenterListActionCreator,
  setPagination: setPaginationThenGetDataActionCreator,
  setType: approveCenterActions.setType,
};

export default connect(mapStateToProps, mapDispatchToProps)(DataTableWait);
