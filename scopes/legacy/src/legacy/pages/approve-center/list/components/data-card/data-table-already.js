import React, { useCallback, useState } from 'react';
import { connect } from 'react-redux';

import { Radio } from '@manyun/base-ui.ui.radio';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useApprovalTypes } from '@manyun/bpm.hook.use-approval-types';
import { ApprovalStatus } from '@manyun/bpm.model.bpm-instance';
import { ApprovalStatusText } from '@manyun/bpm.ui.approval-status-text';
import { CorrespondingOrderLink } from '@manyun/bpm.ui.corresponding-order-link';
import { ManualCarbonCopyModal } from '@manyun/bpm.ui.manual-carbon-copy-modal';
import { SpaceText } from '@manyun/resource-hub.ui.space-text';

import { ApproveLink, Ellipsis, TinyTable, UserLink } from '@manyun/dc-brain.legacy.components';
import {
  approveCenterActions,
  getApproveCenterListActionCreator,
  setPaginationThenGetDataActionCreator,
  setSearchValuesActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/approveCenterActions';

import { getFormattedContent } from '../../../utils';

export function DataTableAlready({
  data,
  total,
  pageNum,
  pageSize,
  loading,
  setPagination,
  setSearchValues,
}) {
  const [tabKey, setTabKey] = useState('ALL');
  const { approvalTypes, approvalTypeTextKeyMap } = useApprovalTypes();
  const paginationChangeHandler = useCallback(
    (current, size) => {
      setPagination({ pageNum: current, pageSize: size });
    },
    [setPagination]
  );
  const getColumns = tabKey =>
    [
      {
        title: '审批ID',
        dataIndex: 'instId',
        fixed: 'left',
        render(instId) {
          return <ApproveLink id={instId} />;
        },
      },
      {
        title: '标题',
        dataIndex: 'title',
        render: (_, { title }) => {
          return (
            <Typography.Text style={{ maxWidth: 480 }} ellipsis={{ tooltip: true }}>
              {title}
            </Typography.Text>
          );
        },
      },
      {
        title: '类型',
        dataIndex: 'bizType',
        render(bizType) {
          return approvalTypeTextKeyMap[bizType];
        },
      },
      {
        title: '对应单号',
        dataIndex: 'bizId',
        render(_, record) {
          return (
            <CorrespondingOrderLink
              targetName={record.bizId}
              targetId={record.bizId}
              targetType={record.bizType}
            />
          );
        },
      },
      {
        title: '内容',
        dataIndex: 'content',
        width: 300,
        render: (_, { content, bizType }) => {
          return (
            <Typography.Text style={{ maxWidth: 300 }} ellipsis={{ tooltip: true }}>
              {getFormattedContent(content, bizType, approvalTypes)}
            </Typography.Text>
          );
        },
      },
      {
        title: '位置',
        dataIndex: 'blockGuid',
        render(_, record) {
          if (record.roomTag) {
            return <SpaceText guid={`${record.blockGuid}.${record.roomTag}`} />;
          }
          return record.blockGuid || record.idcTag ? (
            <SpaceText guid={`${record.blockGuid || record.idcTag}`} />
          ) : (
            '--'
          );
        },
      },
      {
        title: '发起人',
        dataIndex: 'creatorName',
        render(name, { creatorId }) {
          return <UserLink userId={creatorId} userName={name} />;
        },
      },
      {
        title: '发起时间',
        dataIndex: 'gmtCreate',
        dataType: 'datetime',
      },
      {
        title: '完成时间',
        dataIndex: 'gmtEnd',
        dataType: 'datetime',
        render(gmtEnd) {
          if (gmtEnd === null) {
            return '--';
          }
          return gmtEnd;
        },
      },
      {
        title: '状态',
        dataIndex: 'instStatus',
        render(instStatus) {
          return <ApprovalStatusText approveStatus={instStatus} />;
        },
      },
      (tabKey === 'PASS' || tabKey === 'ALL') && {
        title: '操作',
        dataIndex: 'id',
        fixed: 'right',
        render(_, { instId, gmtCreate, instStatus }) {
          return instStatus === ApprovalStatus.Pass ? (
            <ManualCarbonCopyModal applyTime={gmtCreate} type="link" instId={instId} />
          ) : (
            '--'
          );
        },
      },
    ].filter(Boolean);
  const getProcessStatus = status => {
    switch (status) {
      case 'ALL':
        return null;
      default:
        return [status];
    }
  };
  return (
    <TinyTable
      rowKey="id"
      actions={
        <Radio.Group
          defaultValue="ALL"
          onChange={e => {
            setTabKey(e.target.value);
            setSearchValues({
              instStatus: {
                name: 'instStatusList',
                value: getProcessStatus(e.target.value),
              },
            });
          }}
        >
          <Radio.Button value="ALL">全部</Radio.Button>
          <Radio.Button value="PASS">审批通过</Radio.Button>
          <Radio.Button value="REFUSE">审批拒绝</Radio.Button>
          <Radio.Button value="APPROVING">审批中</Radio.Button>
          <Radio.Button value="REVOKE">已撤销</Radio.Button>
        </Radio.Group>
      }
      columns={getColumns(tabKey)}
      scroll={{ x: 'max-content' }}
      align="left"
      dataSource={data}
      loading={loading}
      pagination={{
        total,
        current: pageNum,
        pageSize,
        onChange: paginationChangeHandler,
      }}
    />
  );
}

const mapStateToProps = ({
  approveCenter: {
    data,
    total,
    pagination: { pageNum, pageSize },
    loading,
  },
}) => ({
  data,
  total,
  loading,
  pageNum,
  pageSize,
});
const mapDispatchToProps = {
  getData: getApproveCenterListActionCreator,
  setPagination: setPaginationThenGetDataActionCreator,
  setType: approveCenterActions.setType,
  setSearchValues: setSearchValuesActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(DataTableAlready);
