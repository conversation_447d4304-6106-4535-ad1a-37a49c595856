import React, { useState } from 'react';

import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';

import { GutterWrapper, ModalButton } from '@manyun/dc-brain.legacy.components';
import { approveCenterService } from '@manyun/dc-brain.legacy.services';

export function ApproveModalButton({ instId, taskId, result, text, type, onSuccess, compact }) {
  const [reason, setReason] = useState('');
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  return (
    <ModalButton
      compact={compact}
      type={type}
      text={text}
      title={text}
      okText="提交"
      okButtonProps={{
        loading,
        disabled: reason.length > 50,
      }}
      onOk={async () => {
        setLoading(true);
        const { error } = await approveCenterService.taskApproveProcess({
          result,
          instId,
          taskId,
          comment: reason,
        });
        setLoading(false);

        if (error) {
          message.error(error);

          return false;
        }
        message.success('操作成功! ');
        onSuccess();
        return true;
      }}
      onVisibleChanged={visible => {
        if (!visible) {
          setReason('');
          form.setFieldsValue({ reason: '' });
        }
      }}
    >
      {
        <GutterWrapper mode="vertical">
          <Form form={form} colon={false} labelCol={{ xs: 6 }} wrapperCol={{ xs: 40 }}>
            <Form.Item name="reason" rules={[{ max: 50, message: '最多输入 50 个字符！' }]}>
              <Input.TextArea
                value={reason}
                onChange={({ target: { value } }) => {
                  setReason(value);
                }}
              />
            </Form.Item>
          </Form>
        </GutterWrapper>
      }
    </ModalButton>
  );
}
