import React from 'react';

import { DataTableCc } from '@manyun/bpm.ui.data-table-cc';

import { TinyCard } from '@manyun/dc-brain.legacy.components/tiny-card';

import DataTableAlready from './data-table-already';
import DataTableMine from './data-table-mine';
import DataTableWait from './data-table-wait';

export default function DataCard({ type }) {
  let dataTable;
  if (type === 'MINE') {
    dataTable = <DataTableMine />;
  } else if (type === 'ALREADY') {
    dataTable = <DataTableAlready />;
  } else if (type === 'CC') {
    dataTable = <DataTableCc />;
  } else {
    dataTable = <DataTableWait />;
  }

  return <TinyCard>{dataTable}</TinyCard>;
}
