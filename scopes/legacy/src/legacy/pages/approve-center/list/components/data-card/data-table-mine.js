import React, { useCallback, useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { useLocation } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useApprovalTypes } from '@manyun/bpm.hook.use-approval-types';
import { ApprovalStatusText } from '@manyun/bpm.ui.approval-status-text';
import { CorrespondingOrderLink } from '@manyun/bpm.ui.corresponding-order-link';
import { ManualCarbonCopyModal } from '@manyun/bpm.ui.manual-carbon-copy-modal';
import { SpaceText } from '@manyun/resource-hub.ui.space-text';

import { ApproveLink, Ellipsis, TinyTable } from '@manyun/dc-brain.legacy.components';
import {
  approveCenterActions,
  getApproveCenterListActionCreator,
  setPaginationThenGetDataActionCreator,
  setSearchValuesActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/approveCenterActions';
import { approveCenterService } from '@manyun/dc-brain.legacy.services';

import { getFormattedContent } from '../../../utils';

export function DataTableMine({
  data,
  total,
  pageNum,
  pageSize,
  loading,
  setPagination,
  setSearchValues,
  getData,
}) {
  const { search } = useLocation();
  const { approvalTypes, approvalTypeTextKeyMap } = useApprovalTypes();
  const searchParams = new URLSearchParams(search);

  const paginationChangeHandler = useCallback(
    (current, size) => {
      setPagination({ pageNum: current, pageSize: size });
    },
    [setPagination]
  );

  const [typeKey, setTypeKey] = useState('ALL');

  useEffect(() => {
    const status = searchParams.get('status');
    if (status) {
      setTypeKey(status);
      setSearchValues({
        instStatus: {
          name: 'instStatusList',
          value: getProcessStatus(status),
        },
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  const getProcessStatus = status => {
    switch (status) {
      case 'ALL':
        return null;
      default:
        return [status];
    }
  };
  const getColumns = (getData, typeKey) =>
    [
      {
        title: '审批ID',
        dataIndex: 'instId',
        fixed: 'left',
        render(instId) {
          return <ApproveLink id={instId} />;
        },
      },
      {
        title: '标题',
        dataIndex: 'title',
        render: (_, { title }) => {
          return (
            <Typography.Text style={{ maxWidth: 480 }} ellipsis={{ tooltip: true }}>
              {title}
            </Typography.Text>
          );
        },
      },
      {
        title: '类型',
        dataIndex: 'bizType',
        render(bizType) {
          return approvalTypeTextKeyMap[bizType];
        },
      },
      {
        title: '对应单号',
        dataIndex: 'bizId',
        render(_, record) {
          return (
            <CorrespondingOrderLink
              targetName={record.bizId}
              targetId={record.bizId}
              targetType={record.bizType}
            />
          );
        },
      },
      {
        title: '内容',
        dataIndex: 'content',
        width: 300,
        render: (_, { content, bizType }) => {
          return (
            <Typography.Text style={{ maxWidth: 300 }} ellipsis={{ tooltip: true }}>
              {getFormattedContent(content, bizType, approvalTypes)}
            </Typography.Text>
          );
        },
      },
      {
        title: '位置',
        dataIndex: 'blockGuid',
        render(_, record) {
          if (record.roomTag) {
            return <SpaceText guid={`${record.blockGuid}.${record.roomTag}`} />;
          }
          return record.blockGuid || record.idcTag ? (
            <SpaceText guid={`${record.blockGuid || record.idcTag}`} />
          ) : (
            '--'
          );
        },
      },
      {
        title: '发起时间',
        dataIndex: 'gmtCreate',
        dataType: 'datetime',
      },
      {
        title: '完成时间',
        dataIndex: 'gmtEnd',
        dataType: 'datetime',
        render(gmtEnd) {
          if (gmtEnd === null) {
            return '--';
          }
          return gmtEnd;
        },
      },
      {
        title: '状态',
        dataIndex: 'instStatus',
        render(instStatus) {
          return <ApprovalStatusText approveStatus={instStatus} />;
        },
      },
      ['ALL', 'APPROVING', 'PASS'].includes(typeKey) && {
        title: '操作',
        dataIndex: 'action',
        fixed: 'right',
        render(id, record) {
          if (record.instStatus === 'PASS') {
            return (
              <ManualCarbonCopyModal
                applyTime={record.gmtCreate}
                type="link"
                instId={record.instId}
              />
            );
          } else if (record.instStatus !== 'APPROVING') {
            return '--';
          } else {
            return (
              <Button
                key="revoke-button"
                compact
                type="link"
                onClick={() => {
                  Modal.confirm({
                    title: '确认要撤回' + record.instId + '的审批吗？',
                    content: '撤回后数据将不可恢复。',
                    okText: '确认',
                    cancelText: '取消',
                    onOk() {
                      revokeApprove({ operator: record.createId, instId: record.instId }, getData);
                    },
                  });
                }}
              >
                撤回
              </Button>
            );
          }
        },
      },
    ].filter(Boolean);
  return (
    <TinyTable
      rowKey="id"
      actions={
        <Radio.Group
          value={typeKey}
          onChange={e => {
            setTypeKey(e.target.value);
            setSearchValues({
              instStatus: {
                name: 'instStatusList',
                value: getProcessStatus(e.target.value),
              },
            });
          }}
        >
          <Radio.Button value="ALL">全部</Radio.Button>
          <Radio.Button value="PASS">审批通过</Radio.Button>
          <Radio.Button value="REFUSE">审批拒绝</Radio.Button>
          <Radio.Button value="APPROVING">审批中</Radio.Button>
          <Radio.Button value="REVOKE">已撤销</Radio.Button>
        </Radio.Group>
      }
      columns={getColumns(getData, typeKey)}
      scroll={{ x: 'max-content' }}
      align="left"
      dataSource={data}
      loading={loading}
      pagination={{
        total,
        current: pageNum,
        pageSize,
        onChange: paginationChangeHandler,
      }}
    />
  );
}

const mapStateToProps = ({
  approveCenter: {
    data,
    total,
    pagination: { pageNum, pageSize },
    loading,
  },
}) => ({
  data,
  total,
  loading,
  pageNum,
  pageSize,
});
const mapDispatchToProps = {
  getData: getApproveCenterListActionCreator,
  setPagination: setPaginationThenGetDataActionCreator,
  setType: approveCenterActions.setType,
  setSearchValues: setSearchValuesActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(DataTableMine);

async function revokeApprove(data, getData) {
  const { response, error } = await approveCenterService.revokeApproveProcess(data);

  if (error) {
    message.error(error);
    return;
  }

  message.success('撤回成功！');
  getData(false); /**不重置分页数据 */
  return response;
}
