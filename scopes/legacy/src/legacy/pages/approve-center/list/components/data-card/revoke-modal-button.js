import React, { useState } from 'react';

import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';

import { GutterWrapper, ModalButton } from '@manyun/dc-brain.legacy.components';
import { approveCenterService } from '@manyun/dc-brain.legacy.services';

export function RevokeModalButton({ operator, instId, onSuccess }) {
  const [reason, setReason] = useState('');
  const [form] = Form.useForm();

  return (
    <ModalButton
      type="link"
      text="撤回"
      title="撤回"
      okText="提交"
      okButtonProps={{
        disabled: reason.length > 50,
      }}
      onOk={async () => {
        const { error } = await approveCenterService.revokeApproveProcess({
          operator,
          instId,
          reason,
        });
        if (error) {
          message.error(error);
          return false;
        }
        message.success('撤回成功! ');
        onSuccess(false);
        return true;
      }}
      onVisibleChanged={visible => {
        if (!visible) {
          setReason('');
          form.setFieldsValue({ reason: '' });
        }
      }}
    >
      {
        <GutterWrapper mode="vertical">
          <Form form={form} colon={false} labelCol={{ xs: 4 }} wrapperCol={{ xs: 20 }}>
            <Form.Item name="reason" rules={[{ max: 50, message: '最多输入 50 个字符！' }]}>
              <Input.TextArea
                value={reason}
                onChange={({ target: { value } }) => {
                  setReason(value);
                }}
              />
            </Form.Item>
          </Form>
        </GutterWrapper>
      }
    </ModalButton>
  );
}
