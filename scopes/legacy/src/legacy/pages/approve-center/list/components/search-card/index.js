import React from 'react';

import { CarbonCopySearchForm } from '@manyun/bpm.ui.carbon-copy-search-form';

import { TinyCard } from '@manyun/dc-brain.legacy.components';

import SearchFormAlready from './search-form-already';
import SearchFormMine from './search-form-mine';
import SearchFormWait from './search-form-wait';

export default function SearchCard({ type }) {
  let dataTable;
  if (type === 'MINE') {
    dataTable = <SearchFormMine />;
  } else if (type === 'ALREADY') {
    dataTable = <SearchFormAlready />;
  } else if (type === 'WAIT') {
    dataTable = <SearchFormWait />;
  } else {
    dataTable = <CarbonCopySearchForm />;
  }

  return <TinyCard>{dataTable}</TinyCard>;
}
