import React from 'react';
import { connect } from 'react-redux';

import { FiltersForm, Form } from '@galiojs/awesome-antd';

import { Input } from '@manyun/base-ui.ui.input';

import { ApprovalTypeSelect } from '@manyun/bpm.ui.approval-type-select';

import { LocationCascader, UserSelect } from '@manyun/dc-brain.legacy.components';
import {
  approveCenterActions,
  getApproveCenterListActionCreator,
  resetSearchValuesActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/approveCenterActions';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';

function SearchFormWait({ fields, updateSearchValues, onSearch, onReset }) {
  const [form] = Form.useForm();

  return (
    <FiltersForm
      form={form}
      fields={fields}
      items={[
        {
          label: '审批ID',
          name: 'instId',
          control: <Input allowClear />,
        },
        {
          label: '标题',
          name: 'title',
          control: <Input allowClear />,
        },
        {
          label: '类型',
          name: 'bizType',
          control: <ApprovalTypeSelect allowClear />,
        },
        {
          label: '对应单号',
          name: 'bizId',
          control: <Input allowClear />,
        },
        {
          label: '位置',
          name: 'blockGuid',
          control: <LocationCascader currentAuthorize />,
        },
        {
          label: '发起人',
          name: 'creatorId',
          control: <UserSelect allowClear />,
        },
      ]}
      onFieldsChange={changedFields => {
        updateSearchValues(
          changedFields.reduce((mapper, field) => {
            const name = field.name.join('.');
            mapper[name] = {
              ...field,
              name,
            };

            return mapper;
          }, {})
        );
      }}
      onSearch={onSearch}
      onReset={onReset}
    />
  );
}

const mapStateToProps = ({ approveCenter: { searchValues } }) => ({
  fields: Object.keys(searchValues).map(name => {
    const field = searchValues[name];

    return {
      ...field,
      name: name.split('.'),
    };
  }),
});
const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
  updateSearchValues: approveCenterActions.updateSearchValues,
  onSearch: getApproveCenterListActionCreator,
  onReset: resetSearchValuesActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(SearchFormWait);
