import React from 'react';
import { connect } from 'react-redux';

import { FiltersForm, Form } from '@galiojs/awesome-antd';

import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Input } from '@manyun/base-ui.ui.input';

import { ApprovalTypeSelect } from '@manyun/bpm.ui.approval-type-select';

import { LocationCascader } from '@manyun/dc-brain.legacy.components';
import {
  approveCenterActions,
  getApproveCenterListActionCreator,
  resetSearchValuesActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/approveCenterActions';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';

export function SearchFormMine({ fields, updateSearchValues, onSearch, onReset }) {
  const [form] = Form.useForm();

  return (
    <FiltersForm
      form={form}
      fields={Object.keys(fields).map(name => {
        const field = fields[name];

        return {
          ...field,
          name: name.split('.'),
        };
      })}
      items={[
        {
          label: '审批ID',
          name: 'instId',
          control: <Input allowClear />,
        },
        {
          label: '标题',
          name: 'title',
          control: <Input allowClear />,
        },
        {
          label: '类型',
          name: 'bizType',
          control: <ApprovalTypeSelect allowClear />,
        },
        {
          label: '对应单号',
          name: 'bizId',
          control: <Input allowClear />,
        },
        {
          label: '位置',
          name: 'blockGuid',
          control: <LocationCascader currentAuthorize />,
        },
        {
          label: '发起时间',
          name: 'startTime',
          span: 2,
          control: <DatePicker.RangePicker />,
        },
        {
          label: '完成时间',
          name: 'endTime',
          span: 2,

          control: <DatePicker.RangePicker />,
        },
      ]}
      onFieldsChange={changedFields => {
        updateSearchValues(
          changedFields.reduce((mapper, field) => {
            const name = field.name.join('.');
            mapper[name] = {
              ...field,
              name,
            };

            return mapper;
          }, {})
        );
      }}
      onSearch={onSearch}
      onReset={onReset}
    />
  );
}

const mapStateToProps = ({ approveCenter: { searchValues } }) => ({ fields: searchValues });
const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
  updateSearchValues: approveCenterActions.updateSearchValues,
  onSearch: getApproveCenterListActionCreator,
  onReset: resetSearchValuesActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(SearchFormMine);
