import { ExclamationCircleOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import flatten from 'lodash/flatten';
import React from 'react';

import { Badge } from '@manyun/base-ui.ui.badge';
import { Card } from '@manyun/base-ui.ui.card';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import { EntranceGuardCardAuthGroupText } from '@manyun/ticket.ui.entrance-guard-card-auth-group-text';

export type EntranceGuardCardLicenseInformationProps = {
  formDataList: Record<string, any>[];
  idcTag: string;
};
export const statusMappings: Record<
  string,
  {
    status?: 'success' | 'error' | 'default';
    text: string;
    color?: 'geekblue' | 'cyan';
  }
> = {
  success: { status: 'success', text: '成功' },
  error: { status: 'error', text: '失败' },
  ADD: { color: 'geekblue', text: '新增授权' },
  DELETE: { status: 'error', text: '删除授权' },
  CHANGE: { color: 'cyan', text: '变更授权' },
  UN_CHANG: { status: 'default', text: '未变更' },
};

export type InfoCardProps = {
  authInfoList: {
    blockTag: string;
    authId: string;
    status: 'success' | 'error' | 'ADD' | 'DELETE' | 'CHANGE' | 'UN_CHANG';
    tooltipText: string;
  };
};

export function transferAuthInfoList(authInfoList: Record<string, any>[]) {
  const list = authInfoList.map(item => {
    let result: {
      blockTag: string;
      authId: number;
      status: 'success' | 'error' | 'ADD' | 'DELETE' | 'CHANGE' | 'UN_CHANG';
      originAuthId?: number;
    } = { blockTag: '', authId: 0, status: 'success', originAuthId: 0 };

    if ('originAuthIdList' in item) {
      result = {
        blockTag: item.blockTag,
        authId: item.authIdList[0],
        status: item.changeType,
        originAuthId: item.originAuthIdList[0],
        ...item,
      };
    }
    if ('changeType' in item && 'originAuthId' in item) {
      result = {
        blockTag: item.blockTag,
        authId: item.authId,
        status: item.changeType,
        originAuthId: item.originAuthId,
        ...item,
      };
    }
    if ('isSuccess' in item || 'status' in item) {
      result = {
        blockTag: item.blockTag,
        authId: item.authId,
        status: 'status' in item ? item.status : item.isSuccess,
        ...item,
      };
    }
    return result;
  });
  return list;
}

export function EntranceGuardCardLicenseInformation({
  idcTag,
  formDataList,
}: EntranceGuardCardLicenseInformationProps) {
  const singleBlockArr = flatten(
    formDataList?.map(item => {
      return item?.changeAuthInfoList?.map((val: Record<string, any>) => {
        return {
          applyId: item?.applyId,
          applyName: item?.applyName,
          entranceCardNo: item?.entranceCardNo,
          authIdList: val?.authIdList,
          blockTag: val.blockTag,
          changeType: val.changeType,
          effectTime: val.effectTime,
          oldEffectTime: val.oldEffectTime,
          originAuthIdList: val.originAuthIdList,
        };
      });
    })
  ).filter(_ => _);
  const authInfoArr = singleBlockArr.filter(
    (item: Record<string, any>) => item?.changeType !== 'UN_CHANG'
  ) as any;

  return (
    <Space wrap size={16}>
      {transferAuthInfoList(authInfoArr).map((item: Record<string, any>) => (
        <Card
          key={item.blockTag}
          style={{ minWidth: 256, height: '102px' }}
          bodyStyle={{ padding: '8px 12px' }}
        >
          <Space direction="vertical" style={{ width: '100%' }}>
            <Space style={{ justifyContent: 'space-between', width: '100%' }}>
              <Typography.Title style={{ marginBottom: 0 }} level={5}>
                {item?.applyName}
              </Typography.Title>
              <Typography.Text style={{ fontWeight: 500 }}>{item.blockTag}楼</Typography.Text>
              <Typography.Text style={{ fontWeight: 500 }}>{item.entranceCardNo}</Typography.Text>
            </Space>
            <Space style={{ justifyContent: 'space-between', width: '100%' }}>
              <Typography.Text>
                {idcTag && item.blockTag && (
                  <EntranceGuardCardAuthGroupText
                    blockGuid={`${idcTag}.${item.blockTag}`}
                    code={item.authId}
                  />
                )}
                {item.status === 'CHANGE' && item.originAuthId && (
                  <Tooltip
                    title={
                      <span>
                        由
                        <EntranceGuardCardAuthGroupText
                          blockGuid={`${idcTag}.${item.blockTag}`}
                          code={item.originAuthId}
                        />
                        变更为
                        <EntranceGuardCardAuthGroupText
                          blockGuid={`${idcTag}.${item.blockTag}`}
                          code={item.authId}
                        />
                      </span>
                    }
                  >
                    <ExclamationCircleOutlined
                      style={{ marginLeft: '4px', color: 'rgba(0,0,0,0.45)' }}
                    />
                  </Tooltip>
                )}
              </Typography.Text>
              <Space>
                <Badge
                  status={statusMappings[item.status]?.status}
                  color={statusMappings[item.status]?.color}
                />
                {statusMappings[item.status].text}
              </Space>
            </Space>
            <Typography.Text type="secondary" style={{ fontSize: 12 }}>
              {item?.effectTime ? dayjs(item?.effectTime).format('YYYY-MM-DD HH:mm:ss') : '--'}
            </Typography.Text>
          </Space>
        </Card>
      ))}
    </Space>
  );
}
