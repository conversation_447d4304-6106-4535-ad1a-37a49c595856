import React, { useEffect, useState } from 'react';

import { Table } from '@manyun/base-ui.ui.table';
import { Tag } from '@manyun/base-ui.ui.tag';
import { fetchVisitorInfoStaffList } from '@manyun/bpm.service.fetch-visitor-info-staff-list';

export function VisitorTable({ baseInfo }: Record<string, any>) {
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState<Record<string, any>[]>([]);

  const businessType = baseInfo?.formJson?.taskSubType;

  const columns = [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Record<string, any>) => {
        return (
          <div>
            {text}
            <Tag color="default" style={{ display: 'inline-block', marginLeft: '8px' }}>
              {record?.visitorType?.name}
            </Tag>
          </div>
        );
      },
    },
    {
      title: '联系方式',
      dataIndex: 'contactWay',
      key: 'contactWay',
      render: (text: string, record: Record<string, any>) => {
        return `${record?.phoneCountryCode} ${text}`;
      },
    },
    {
      title: '证件号码',
      dataIndex: 'identityNo',
      key: 'identityNo',
      render: (text: string, record: Record<string, any>) => {
        return `${record?.certificateType?.name} ${text}`;
      },
    },
    {
      title:
        businessType?.includes('政府') || businessType?.includes('商务') ? '单位名称' : '公司名称',
      dataIndex: 'companyName',
      key: 'companyName',
    },
    {
      title: '职位',
      dataIndex: 'position',
      key: 'position',
    },
    {
      title: '车牌号',
      dataIndex: 'plateNo',
      key: 'plateNo',
    },
  ];

  useEffect(() => {
    if (!baseInfo.bizId) {
      return;
    }
    setLoading(true);
    fetchVisitorInfoStaffList({
      taskNo: baseInfo.bizId,
    })
      .then((data: Record<string, any>) => {
        setList(data?.data?.data || []);
      })
      .catch(() => {
        setList([]);
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  return (
    <Table
      rowKey="id"
      columns={columns}
      scroll={{ x: 'max-content' }}
      dataSource={list}
      loading={loading}
    />
  );
}
