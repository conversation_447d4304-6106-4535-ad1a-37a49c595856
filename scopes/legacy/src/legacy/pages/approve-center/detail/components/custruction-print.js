import React from 'react';

import { getPrintTempates } from '@manyun/bpm.page.construction-request';

import { PrintFile } from '@manyun/dc-brain.legacy.components';

export default function CustructionPrint({ formJson, reason, approveRecords }) {
  let data = null;

  if (formJson) {
    data = formJson;
  }

  if (!formJson) {
    return null;
  }
  if (data) {
    const records = approveRecords
      .map(({ operationResult, operationName, remark }) => {
        if (operationResult === '通过' || operationResult === '拒绝') {
          let tmp = operationResult;
          if (remark) {
            tmp = `${operationResult}，${remark}`;
          }
          return {
            operationName: operationName || '',
            remark: tmp,
          };
        }
        return null;
      })
      .filter(Boolean);
    const obj = {
      hazardousOptTip: data.hazardousOptTip || '',
      constructionUsers: data.constructionUsers || [],
      ticketContent: data.ticketContent,
      approveRecords: records,
    };
    return (
      <PrintFile
        contents={getPrintTempates({ data: obj })}
        renderComponent={() => '打印'}
        bthProps={{ type: 'primary' }}
      />
    );
  } else {
    return <></>;
  }
}
