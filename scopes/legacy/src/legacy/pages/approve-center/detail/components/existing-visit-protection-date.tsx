import React from 'react';

import dayjs from 'dayjs';

import { Alert } from '@manyun/base-ui.ui.alert';

import { useLazyExistingProtectionDate, useVisitTicket } from '@manyun/sentry.gql.client.visits';

export type ExistingVisitProtectionDateProps = {
  bizId: string;
};
export const ExistingVisitProtectionDate = ({ bizId }: ExistingVisitProtectionDateProps) => {
  const { data } = useVisitTicket({
    variables: {
      ticketNumber: bizId,
    },
  });
  const [getExistingProtectionDate, { data: configureData }] = useLazyExistingProtectionDate();

  React.useEffect(() => {
    if (
      data?.visitTicket?.allowedTimeRage &&
      data.visitTicket.allowedTimeRage[0] &&
      data.visitTicket.allowedTimeRage[1]
    ) {
      getExistingProtectionDate({
        variables: {
          startTime: data.visitTicket.allowedTimeRage[0],
          endTime: data.visitTicket.allowedTimeRage[1],
        },
      });
    }
  }, [data?.visitTicket?.allowedTimeRage, getExistingProtectionDate]);

  const existingProtectionData = React.useMemo(
    () => configureData?.existingProtectionDate?.data ?? [],
    [configureData?.existingProtectionDate?.data]
  );

  if (existingProtectionData.length === 0) {
    return null;
  }
  return (
    <Alert
      style={{ width: '100%', marginBottom: 16 }}
      message={`当前访客授权时间范围内存在重保日，分别为：${(existingProtectionData ?? [])
        .map(str => dayjs(str).format('YYYY年MM月DD日'))
        .join('、')}`}
      type="warning"
      showIcon
    />
  );
};
