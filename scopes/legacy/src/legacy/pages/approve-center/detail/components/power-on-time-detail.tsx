import React, { useState } from 'react';

import moment from 'moment';

import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Typography } from '@manyun/base-ui.ui.typography';

export type PowerOnTimeDetailProps = {
  taskSubType: string;
  executeTime: string | null;
  modReason: string;
  powerOnTime: string;
};

export const PowerOnTimeDetail = ({
  taskSubType,
  executeTime,
  modReason,
  powerOnTime,
}: PowerOnTimeDetailProps) => {
  const [open, setOpen] = useState(false);

  return (
    <>
      <Typography.Link
        onClick={() => {
          setOpen(true);
        }}
      >
        查看
      </Typography.Link>
      <Modal
        width={550}
        open={open}
        title={`查看实际${taskSubType === 'POWER_OFF' ? '下' : '上'}电时间`}
        footer={null}
        onCancel={() => {
          setOpen(false);
        }}
      >
        <Descriptions column={1}>
          <Descriptions.Item label="工单实际执行完成时间">
            {executeTime ? moment(executeTime).format('YYYY-MM-DD HH:mm:ss') : '--'}
          </Descriptions.Item>
          <Descriptions.Item label={`修订实际${taskSubType === 'POWER_OFF' ? '下' : '上'}电时间`}>
            {moment(powerOnTime).format('YYYY-MM-DD HH:mm:ss')}
          </Descriptions.Item>
          <Descriptions.Item label="修订原因">{modReason}</Descriptions.Item>
        </Descriptions>
      </Modal>
    </>
  );
};
