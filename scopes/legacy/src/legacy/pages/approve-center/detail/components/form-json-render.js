import dayjs from 'dayjs';
import React from 'react';

import { UserEmailText } from '@manyun/auth-hub.ui.user-email-text';
import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import { useApprovalTypes } from '@manyun/bpm.hook.use-approval-types';
import { BusinessCustomizationApprovalContent } from '@manyun/bpm.ui.business-customization-approval-content';
import { ConstructionUsers } from '@manyun/bpm.ui.construction-users';
import { SaleQuote } from '@manyun/crm.model.sale-quote';
import { QuotationUpdateRecordApproval } from '@manyun/crm.ui.quotation-update-record';
import { SaleQuoteChangeRecord } from '@manyun/crm.ui.sale-quote-change-record';
import { SaleQuoteDescriptions } from '@manyun/crm.ui.sale-quote-descriptions';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { AnnualPerformanceObjective } from '@manyun/hrm.model.annual-performance-objective';
import { AnnualPerformanceObjectiveTable } from '@manyun/hrm.ui.annual-performance-objective-table';
import { ShiftAdjustmentUserTable } from '@manyun/hrm.ui.shift-adjustment-user-table';
import { AssetsTable } from '@manyun/resource-hub.ui.assets-table';
import { DeviceTypeText } from '@manyun/resource-hub.ui.device-type-text';
import { EntranceGuardCardAuthGroupText } from '@manyun/ticket.ui.entrance-guard-card-auth-group-text';
import { EntranceGuardCardCancelledInfo } from '@manyun/ticket.ui.entrance-guard-card-cancelled-info';

import { EntranceGuardCardLicenseInformation } from './card-change';
import { CardExchangeInfo } from './card-exchange';

export function FormJsonRender({ bizType, idcTag, formJson, bizId }) {
  const [configUtil] = useConfigUtil();
  const approvalListType = configUtil.getScopeCommonConfigs('bpm')?.approval?.approvalListType;

  const { approvalTypes } = useApprovalTypes();

  if (!formJson) {
    return null;
  }

  if ([approvalTypes.OUT_DOOR, approvalTypes.IN_DOOR].includes(bizType)) {
    return (
      <AssetsTable
        mode="viewer"
        viewType={formJson.assetType === 'IDC_ASSET' ? 'idc' : 'other'}
        parentDataSource={formJson.devices.map(device => ({
          sortType: device.deviceType,
          assetNo: getDefalutContentDisplay(device.assetNo),
          serialNumber: getDefalutContentDisplay(device.serialNo),
          brand: getDefalutContentDisplay(device.vendor),
          model: getDefalutContentDisplay(device.productModel),
          sum: device.accessCount,
          materialType: device.materialType,
        }))}
      />
    );
  }
  if (bizType === approvalTypes.CONSTRUCTION_APPLY) {
    return <ConstructionUsers value={formJson.constructionUsers} encryptIdNumber />;
  }
  if (
    (bizType === approvalTypes.VISITOR && !approvalListType) ||
    bizType === approvalTypes.CUS_EFFECTED_APPLY
  ) {
    return (
      <BusinessCustomizationApprovalContent
        dataSource={formJson.formDataList}
        columns={formJson.approvalFormFiledInfoList}
      />
    );
  }
  if (bizType === approvalTypes.AUTH_APPLY) {
    return null;
  }
  if (bizType === approvalTypes.LEAVE_PROCESS || bizType === approvalTypes.REST_PROCESS) {
    return <ShiftAdjustmentUserTable data={formJson} />;
  }
  if (bizType === approvalTypes.SALES_QUOTATION) {
    const saleData = SaleQuote.fromApiObject(formJson).toJSON();
    return (
      <Tabs
        destroyInactiveTabPane
        items={[
          {
            label: '报价详情',
            key: 'detail',
            children: (
              <SaleQuoteDescriptions
                isInSales={false}
                data={saleData}
                groupTypes={['idcInfos', 'customerInfos', 'quotes']}
              />
            ),
          },
          {
            label: '变更记录',
            key: 'change-record',
            children: <SaleQuoteChangeRecord applyId={bizId} saleData={saleData} />,
          },
        ]}
      />
    );
  }
  if (bizType === approvalTypes.CARD_APPLY) {
    return <AccessUserTable idcTag={idcTag} data={formJson.formDataList} />;
  }
  if (bizType === approvalTypes.CARD_CHANGE) {
    return Array.isArray(formJson.formDataList) && formJson.formDataList.length > 0 ? (
      <EntranceGuardCardLicenseInformation formDataList={formJson.formDataList} idcTag={idcTag} />
    ) : null;
  }

  if (bizType === approvalTypes.CARD_EXCHANGE) {
    return Array.isArray(formJson?.formDataList) && formJson.formDataList.length > 0 ? (
      <CardExchangeInfo formDataList={formJson?.formDataList} idcTag={idcTag} />
    ) : null;
  }
  if (bizType === approvalTypes.CARD_OFF) {
    return Array.isArray(formJson.formDataList) && formJson.formDataList.length > 0 ? (
      <EntranceGuardCardCancelledInfo deleteAuthInfos={formJson.formDataList} />
    ) : null;
  }
  if (bizType === approvalTypes.PF_KPI) {
    return (
      <AnnualPerformanceObjectiveTable
        showColumns={['name', 'measurements', 'gradeCriteria']}
        dataSource={
          typeof formJson === 'object'
            ? [AnnualPerformanceObjective.fromApiObject(formJson).toJSON()]
            : []
        }
        pagination={false}
      />
    );
  }
  if (
    bizType === approvalTypes.CUST_QUOTATION_ADD ||
    bizType === approvalTypes.CUST_QUOTATION_UPD
  ) {
    return (
      <QuotationUpdateRecordApproval
        projectName={formJson.projectName}
        idcCompanyName={formJson.idcCompanyName}
        projectAddress={formJson.projectAddress}
        updateContentBefore={formJson.updateContentBefore}
        updateContentAfter={formJson.updateContentAfter}
      />
    );
  }
  if ([approvalTypes.BORROW].includes(bizType)) {
    return (
      <Table
        rowKey="id"
        columns={[
          {
            title: '物资分类',
            dataIndex: 'deviceType',
            render: (deviceType, record) => {
              if (record.assetType === 'OFF_LINE') {
                return deviceType;
              }
              return <DeviceTypeText code={deviceType} />;
            },
          },
          {
            title: '品牌',
            dataIndex: 'vendor',
          },
          {
            title: '型号',
            dataIndex: 'productModel',
          },
          {
            title: '借用至目的位置',
            dataIndex: 'targetRoomGuid',
          },
          {
            title: '申请数量',
            dataIndex: 'applyNum',
          },
        ]}
        dataSource={formJson}
        scroll={{ x: 'max-content' }}
        tableLayout="fixed"
      />
    );
  }

  return null;
}
function AccessUserTable({ idcTag, data }) {
  const getDataSource = dataSource => {
    return dataSource.flatMap(item => {
      if (!item.authInfoList || item.authInfoList.length === 0) {
        return [
          {
            ...item,
            isMerged: true,
            mergeCount: 1,
            blockTag: '',
            authIdList: [],
            effectTime: '',
          },
        ];
      }
      return item.authInfoList.map((auth, index) => ({
        ...item,
        ...auth,
        isMerged: index === 0, // 只有第一行需要合并
        mergeCount: item.authInfoList.length, // 记录需要合并的行数
      }));
    });
  };

  const columns = [
    {
      title: '申请人',
      dataIndex: 'applyName',
      render: (value, { applyName, applyId, isMerged, mergeCount }) => {
        if (!isMerged) {
          return {
            children: applyId ? (
              <UserLink userId={applyId} userName={applyName} external />
            ) : (
              applyName
            ),
            props: { rowSpan: 0 },
          };
        }
        return {
          children: applyId ? (
            <UserLink userId={applyId} userName={applyName} external />
          ) : (
            applyName
          ),
          props: { rowSpan: mergeCount },
        };
      },
    },
    {
      title: '联系电话',
      dataIndex: 'phone',
      render: (value, record, index) => {
        if (!record.isMerged) {
          return { children: value, props: { rowSpan: 0 } };
        }
        return { children: value, props: { rowSpan: record.mergeCount } };
      },
    },
    {
      title: '电子邮箱',
      dataIndex: 'email',
      render: (text, record) => {
        if (!record.isMerged) {
          return {
            children: <UserEmailText email={text} applyId={record.applyId} />,
            props: { rowSpan: 0 },
          };
        }
        return {
          children: <UserEmailText email={text} applyId={record.applyId} />,
          props: { rowSpan: record.mergeCount },
        };
      },
    },
    {
      title: '证件类型',
      dataIndex: 'cardType',
      render: (value, record, index) => {
        if (!record.isMerged) {
          return { children: value, props: { rowSpan: 0 } };
        }
        return { children: value, props: { rowSpan: record.mergeCount } };
      },
    },
    {
      title: '证件编号',
      dataIndex: 'cardNo',
      render: (value, record, index) => {
        if (!record.isMerged) {
          return { children: value, props: { rowSpan: 0 } };
        }
        return { children: value, props: { rowSpan: record.mergeCount } };
      },
    },
    {
      title: '所属部门',
      dataIndex: 'dept',
      render: (value, record, index) => {
        if (!record.isMerged) {
          return { children: value, props: { rowSpan: 0 } };
        }
        return { children: value, props: { rowSpan: record.mergeCount } };
      },
    },
    {
      title: '所属公司',
      dataIndex: 'company',
      render: (value, record, index) => {
        if (!record.isMerged) {
          return { children: value, props: { rowSpan: 0 } };
        }
        return { children: value, props: { rowSpan: record.mergeCount } };
      },
    },
    {
      title: '授权楼栋',
      dataIndex: 'blockTag',
      render: value => value || '--',
    },
    {
      title: '门禁权限组角色',
      dataIndex: 'authIdList',
      render: (text, record) => {
        return text && text.length ? (
          <Space split={<Divider type="vertical" spaceSize="mini" />}>
            {text && text.length
              ? text?.map(role =>
                  role ? (
                    <EntranceGuardCardAuthGroupText
                      key={role}
                      code={role}
                      blockGuid={`${idcTag}.${record?.blockTag}`}
                    />
                  ) : (
                    '--'
                  )
                )
              : '--'}
          </Space>
        ) : (
          '--'
        );
      },
    },
    {
      title: '有效期限',
      dataIndex: 'effectTime',
      render: text => {
        return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '--';
      },
    },
  ];

  return <Table columns={columns} scroll={{ x: 'max-content' }} dataSource={getDataSource(data)} />;
}
function getDefalutContentDisplay(text) {
  return (text === '') | (text === null) ? '--' : text;
}
