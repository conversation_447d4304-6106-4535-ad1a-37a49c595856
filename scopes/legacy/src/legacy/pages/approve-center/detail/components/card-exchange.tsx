import { ExclamationCircleOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import flatten from 'lodash/flatten';
import React from 'react';

import { Card } from '@manyun/base-ui.ui.card';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import { EntranceGuardCardAuthGroupText } from '@manyun/ticket.ui.entrance-guard-card-auth-group-text';

export function CardExchangeInfo({
  formDataList,
  idcTag,
}: {
  formDataList: Record<string, any>[];
  idcTag: string;
}) {
  const singleBlockArr = flatten(
    formDataList.map(item => {
      return item?.authInfoList.map((val: Record<string, any>) => {
        return {
          applyId: item?.applyId,
          applyName: item?.applyName,
          entranceCardNo: item?.entranceCardNo,
          oldEntranceCardNo: item?.oldEntranceCardNo,
          authIdList: val?.authIdList,
          blockTag: val?.blockTag,
          effectTime: val?.effectTime,
        };
      });
    })
  ).filter(_ => _);

  return (
    <Space wrap size={16}>
      {singleBlockArr.map(item => {
        return (
          <Card
            key={item.applyName}
            style={{ minWidth: 256, height: '102px' }}
            bodyStyle={{ padding: '8px 12px' }}
          >
            <Space direction="vertical" style={{ width: '100%' }}>
              <Space style={{ justifyContent: 'space-between', width: '100%' }}>
                <Space direction="horizontal">
                  <Typography.Title style={{ marginBottom: 0 }} level={5}>
                    {item.applyName}
                  </Typography.Title>
                </Space>
                <Space>
                  <Typography.Text>
                    {item.entranceCardNo}
                    <Tooltip title={<span>原门禁卡号：{item.oldEntranceCardNo}</span>}>
                      <ExclamationCircleOutlined
                        style={{ marginLeft: '4px', color: 'rgba(0,0,0,0.45)' }}
                      />
                    </Tooltip>
                  </Typography.Text>
                </Space>
              </Space>
              <Space style={{ justifyContent: 'space-between', width: '100%' }}>
                <Typography.Text>{item.blockTag}楼</Typography.Text>
                <Typography.Text type="secondary">
                  {item.blockTag && (
                    <EntranceGuardCardAuthGroupText
                      blockGuid={`${idcTag}.${item.blockTag}`}
                      code={item?.authIdList?.[0]}
                    />
                  )}
                </Typography.Text>
              </Space>
              <Typography.Text type="secondary" style={{ fontSize: 12 }}>
                {dayjs(item?.effectTime).format('YYYY-MM-DD HH:mm:ss')}
              </Typography.Text>
            </Space>
          </Card>
        );
      })}
    </Space>
  );
}
