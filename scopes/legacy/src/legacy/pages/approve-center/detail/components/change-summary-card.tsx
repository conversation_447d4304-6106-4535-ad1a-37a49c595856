import dayjs from 'dayjs';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Divider } from '@manyun/base-ui.ui.divider';
import { SimpleFileList } from '@manyun/base-ui.ui.file-list';
import { Popover } from '@manyun/base-ui.ui.popover';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import type { BackendMcUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { ChangeOfflineExecutionRecordTable } from '@manyun/ticket.ui.change-offline-execution-record-table';
import { ChangeSourceText } from '@manyun/ticket.ui.change-source-text';

export function ChangeSummaryCard({
  formJson,
  changeOrderId,
  isYGChange,
}: {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  formJson: any;
  changeOrderId: string;
  isYGChange: boolean;
}) {
  const isExecuteSuccess = formJson.exeResult === 'SUCCESS';

  return (
    <Card title="总结内容">
      <Space style={{ width: '100%', display: 'flex' }} direction="vertical" size="middle">
        <Descriptions column={4}>
          <Descriptions.Item label="执行变更开始时间">
            {dayjs(formJson.realStartTime).format('YYYY-MM-DD HH:mm:ss')}
          </Descriptions.Item>
          <Descriptions.Item label="执行变更结束时间">
            {dayjs(formJson.realEndTime).format('YYYY-MM-DD HH:mm:ss')}
          </Descriptions.Item>
          <Descriptions.Item label="变更执行结果">
            {isExecuteSuccess ? '执行成功' : '执行失败'}
          </Descriptions.Item>
          <Descriptions.Item label="附件">
            {formJson.fileInfoList.length > 0 ? (
              <SimpleFileList
                files={formJson.fileInfoList.map((file: BackendMcUploadFile) =>
                  McUploadFile.fromApiObject(file)
                )}
              >
                <Button type="link" compact>
                  查看
                </Button>
              </SimpleFileList>
            ) : (
              '--'
            )}
          </Descriptions.Item>
          {!isExecuteSuccess && isYGChange && (
            <Descriptions.Item label="关联事项" contentStyle={{ overflow: 'hidden' }}>
              {formJson.changeRelateList?.length > 0 ? (
                <ChangeRelateTasks changeRelateList={formJson.changeRelateList} />
              ) : (
                '--'
              )}
            </Descriptions.Item>
          )}
          {!isExecuteSuccess && (
            <Descriptions.Item label="失败原因" contentStyle={{ overflow: 'hidden' }}>
              <Typography.Text ellipsis={{ tooltip: true }}>{formJson.failReason}</Typography.Text>
            </Descriptions.Item>
          )}

          <Descriptions.Item
            label="总结内容"
            span={isExecuteSuccess ? 4 : 2}
            contentStyle={{ overflow: 'hidden' }}
          >
            <Typography.Text ellipsis={{ tooltip: true }}> {formJson.summery}</Typography.Text>
          </Descriptions.Item>
        </Descriptions>
        <ChangeOfflineExecutionRecordTable changeOrderId={changeOrderId} />
      </Space>
    </Card>
  );
}

function ChangeRelateTasks({
  changeRelateList,
}: {
  changeRelateList: { relateNo: string; relateType: string }[];
}) {
  function getContent(isInPopover: boolean) {
    return changeRelateList.map((item, index) => (
      <span key={item.relateNo}>
        <ChangeSourceText sourceNo={item.relateNo} sourceType={item.relateType} />
        {!isInPopover && index !== changeRelateList.length - 1 && <Divider type="vertical" />}
      </span>
    ));
  }
  return (
    <Popover
      content={
        <Space style={{ maxWidth: 400 }} split={<Divider type="vertical" spaceSize="mini" />} wrap>
          {getContent(true)}
        </Space>
      }
    >
      <Typography.Text ellipsis>{getContent(false)}</Typography.Text>
    </Popover>
  );
}
