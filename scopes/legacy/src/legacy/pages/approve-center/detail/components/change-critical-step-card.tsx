import { Card } from '@manyun/base-ui.ui.card';

import type { CriticalStepForm } from '@manyun/ticket.ui.change-offline-critical-step-table';
import { ChangeOfflineCriticalStepTable } from '@manyun/ticket.ui.change-offline-critical-step-table';

export function ChangeCriticalStepCard({
  formJson,
}: {
  formJson: { changeStepList: CriticalStepForm[] };
}) {
  return (
    <Card title="关键步骤">
      <ChangeOfflineCriticalStepTable dataSource={formJson.changeStepList} />
    </Card>
  );
}
