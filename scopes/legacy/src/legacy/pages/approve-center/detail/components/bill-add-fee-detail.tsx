import React from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Collapse } from '@manyun/base-ui.ui.collapse';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { SimpleFileList } from '@manyun/base-ui.ui.file-list';
import { Space } from '@manyun/base-ui.ui.space';
import { OTHER_FEE_TYPE_KEY_MAP } from '@manyun/crm.model.bill';
import type { BackendMcUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { SpaceText } from '@manyun/resource-hub.ui.space-text';

export type FeeDetail = {
  amountUnit: string | null;
  endAmount: number | null;
  fee: number | null;
  price: number | null;
  remark: string;
  resourceNo: string;
  startAmount: number | null;
  useAmount: number;
  fileInfoList?: BackendMcUploadFile[];
};

export type BillAddFeeInfo = {
  feeCode: string;
  feeName: string;
  feeSource: number;
  feeDatilList: FeeDetail[];
  taxRate?: number;
};
export type BillAddFeeDetailProps = {
  info: BillAddFeeInfo;
};

export function BillAddFeeDetail({ info }: BillAddFeeDetailProps) {
  return (
    <Card title="新增费用信息">
      <Space style={{ width: '100%' }} direction="vertical">
        <Descriptions column={4}>
          <Descriptions.Item label="费用类目" span={2}>
            {info.feeName}
          </Descriptions.Item>
          <Descriptions.Item label="税率" span={2}>
            {typeof info.taxRate === 'number' ? `${info.taxRate}%` : '--'}
          </Descriptions.Item>
        </Descriptions>
        <Collapse>
          {info.feeDatilList.map(feeDetail => {
            return (
              <Collapse.Panel
                key={feeDetail.resourceNo}
                header={<SpaceText guid={feeDetail.resourceNo} />}
              >
                {getBillAddFeeContent(feeDetail, info.feeCode)}
              </Collapse.Panel>
            );
          })}
        </Collapse>
      </Space>
    </Card>
  );
}

function getBillAddFeeContent(feeDetail: FeeDetail, feeCode: string) {
  return (
    <Descriptions column={2}>
      {feeDetail.startAmount && (
        <Descriptions.Item label="期初电量">
          {getPowerText(feeDetail.startAmount, feeDetail.amountUnit)}
        </Descriptions.Item>
      )}
      {feeDetail.endAmount && (
        <Descriptions.Item label="期末电量">
          {getPowerText(feeDetail.endAmount, feeDetail.amountUnit)}
        </Descriptions.Item>
      )}
      <Descriptions.Item label="总用量">
        {getPowerText(feeDetail.useAmount, feeDetail.amountUnit)}
      </Descriptions.Item>
      {feeDetail.fee && (
        <Descriptions.Item label="消费金额">
          {getPriceText({ price: feeDetail.fee, precision: 2 })}
        </Descriptions.Item>
      )}
      {feeDetail.price && (
        <Descriptions.Item label={`${feeCode === OTHER_FEE_TYPE_KEY_MAP.EQ ? '电费' : '单位'}单价`}>
          {getPriceText({ price: feeDetail.price, precision: 4 })}
        </Descriptions.Item>
      )}
      <Descriptions.Item label="附件">
        {Array.isArray(feeDetail.fileInfoList) && feeDetail.fileInfoList.length > 0 ? (
          <SimpleFileList
            files={feeDetail.fileInfoList.map((file: BackendMcUploadFile) =>
              McUploadFile.fromApiObject(file)
            )}
          >
            <Button type="link" compact>
              查看
            </Button>
          </SimpleFileList>
        ) : (
          '--'
        )}
      </Descriptions.Item>
      <Descriptions.Item label="费用说明" span={2}>
        {feeDetail.remark}
      </Descriptions.Item>
    </Descriptions>
  );
}

function getPowerText(useAmount: number | null, amountUnit: string | null) {
  return useAmount ? `${useAmount}${amountUnit ?? ''}` : '--';
}

function getPriceText(data: { price: number | null; precision: number }) {
  return data.price ? `¥${(data.price / 100).toFixed(data.precision)}` : '--';
}
