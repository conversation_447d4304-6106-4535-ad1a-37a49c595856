import dayjs from 'dayjs';
import React, { useEffect } from 'react';

import { Card } from '@manyun/base-ui.ui.card';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useLazyAdjustAccountGrids } from '@manyun/bpm.gql.client.bpm-detail';

export type AdjustGrid = {
  adjustMaxPower: number;
  gridGuid: string;
  gridPower: number;
  maxPowerTime: string;
  originMaxPower: number;
  roomTag: string;
  originMaxPowerTime?: string;
  adjustMaxPowerTime?: string;
};
export type AdjustAccountInfo = {
  adjustSource: number;
  feeCode: string;
  batchAdjust: boolean;
  adjustNo: string;
  adjustObjType: string;
  resourceNo: string;
  originPrice: number | null;
  adjustPrice: number | null;
  originStartUseAmount: number | null;
  adjustStartUseAmount: number | null;
  originEndUseAmount: number | null;
  adjustEndUseAmount: number | null;
  adjustUseAmount: number | null;
  useAmount: number | null;
  originFee: number;
  adjustFee: number;
  amountUnit: string | null;
  originMaxPowerTime: string | null;
  adjustMaxPowerTime: string | null;
  originTaxRate: number | null;
  adjustTaxRate: number | null;
};
export type AdjustAccountDetailProps = {
  type: string;
  info: AdjustAccountInfo;
};

export function AdjustAccountDetail({ type, info }: AdjustAccountDetailProps) {
  const isbatchAdjust = info.batchAdjust;
  return (
    <Card title="调整信息">
      {isbatchAdjust ? (
        <AdjustAccountGridsTable accountNumber={info.adjustNo} />
      ) : (
        <Descriptions column={4}>{getAdjustAccountContent(type, info)}</Descriptions>
      )}
    </Card>
  );
}

function getAdjustAccountContent(type: string, formJson: AdjustAccountInfo) {
  switch (type) {
    case '1001':
      return (
        <>
          <Descriptions.Item label="调整对象">{formJson.resourceNo}</Descriptions.Item>
          {formJson.adjustUseAmount && (
            <Descriptions.Item label="峰值功率">
              {`${formJson.adjustUseAmount}kW`}
              <Typography.Text style={{ fontSize: '12px' }} delete disabled>
                {formJson.useAmount ? `${formJson.useAmount}kW` : '--'}
              </Typography.Text>
            </Descriptions.Item>
          )}
          {formJson.adjustFee && (
            <Descriptions.Item label="消费金额">
              {getPriceText({ price: formJson.adjustFee, precision: 2 })}
              <Typography.Text style={{ fontSize: '12px' }} delete disabled>
                {getPriceText({ price: formJson.originFee, precision: 2 })}
              </Typography.Text>
            </Descriptions.Item>
          )}
          <Descriptions.Item label="发生时刻">
            <Typography.Text>{formJson.adjustMaxPowerTime ?? '--'}</Typography.Text>
            <Typography.Text style={{ fontSize: '12px' }} delete disabled>
              {formJson.originMaxPowerTime ?? '--'}
            </Typography.Text>
          </Descriptions.Item>
        </>
      );
    case '2001':
    case '9001':
      return (
        <>
          <Descriptions.Item label="调整对象" span={4}>
            {formJson.resourceNo}
          </Descriptions.Item>
          <Descriptions.Item label="期初电量">
            {getPowerText(formJson.adjustStartUseAmount)}
            <Typography.Text style={{ fontSize: '12px' }} delete disabled>
              {getPowerText(formJson.originStartUseAmount)}
            </Typography.Text>
          </Descriptions.Item>
          <Descriptions.Item label="期末电量">
            {getPowerText(formJson.adjustEndUseAmount)}
            <Typography.Text style={{ fontSize: '12px' }} delete disabled>
              {getPowerText(formJson.originEndUseAmount)}
            </Typography.Text>
          </Descriptions.Item>
          <Descriptions.Item label="用电总量">
            {getPowerText(formJson.adjustUseAmount)}
            <Typography.Text style={{ fontSize: '12px' }} delete disabled>
              {getPowerText(formJson.useAmount)}
            </Typography.Text>
          </Descriptions.Item>
          <Descriptions.Item label="消费金额">
            {getPriceText({ price: formJson.adjustFee, precision: 2 })}
            <Typography.Text style={{ fontSize: '12px' }} delete disabled>
              {getPriceText({ price: formJson.originFee, precision: 2 })}
            </Typography.Text>
          </Descriptions.Item>
          <Descriptions.Item label="电费单价">
            {getPriceText({ price: formJson.adjustPrice, precision: 4 })}
            <Typography.Text style={{ fontSize: '12px' }} delete disabled>
              {getPriceText({ price: formJson.originPrice, precision: 4 })}
            </Typography.Text>
          </Descriptions.Item>
          <Descriptions.Item label="税率">
            {getTaxRateText(formJson.adjustTaxRate)}
            {typeof formJson.originTaxRate === 'number' && (
              <Typography.Text style={{ fontSize: '12px' }} delete disabled>
                {getTaxRateText(formJson.originTaxRate)}
              </Typography.Text>
            )}
          </Descriptions.Item>
        </>
      );
    case '3001':
      return (
        <>
          <Descriptions.Item label="调整对象">{formJson.resourceNo}</Descriptions.Item>
          <Descriptions.Item label="是否使用">
            {formJson.adjustUseAmount ? '是' : '否'}
            <Typography.Text style={{ fontSize: '12px' }} delete disabled>
              {formJson.useAmount ? '是' : '否'}
            </Typography.Text>
          </Descriptions.Item>
        </>
      );
    case '4001':
      return (
        <>
          <Descriptions.Item label="调整对象">{formJson.resourceNo}</Descriptions.Item>
          <Descriptions.Item label="消费工位数量">
            {formJson.adjustUseAmount ? `${formJson.adjustUseAmount}个` : '--'}
            <Typography.Text style={{ fontSize: '12px' }} delete disabled>
              {formJson.useAmount ? `${formJson.useAmount}个` : '--'}
            </Typography.Text>
          </Descriptions.Item>
          {typeof formJson.adjustFee === 'number' && (
            <Descriptions.Item label="消费金额">
              {getPriceText({ price: formJson.adjustFee, precision: 2 })}
              <Typography.Text style={{ fontSize: '12px' }} delete disabled>
                {getPriceText({ price: formJson.originFee, precision: 2 })}
              </Typography.Text>
            </Descriptions.Item>
          )}
        </>
      );
    default:
      return (
        <>
          <Descriptions.Item label="调整对象">{formJson.resourceNo}</Descriptions.Item>
          <Descriptions.Item label="总用量">
            {formJson.adjustUseAmount
              ? `${formJson.adjustUseAmount}${formJson.amountUnit ?? ''}`
              : '--'}
            <Typography.Text style={{ fontSize: '12px' }} delete disabled>
              {formJson.useAmount ? `${formJson.useAmount}${formJson.amountUnit ?? ''}` : '--'}
            </Typography.Text>
          </Descriptions.Item>
          <Descriptions.Item label="消费金额">
            {getPriceText({ price: formJson.adjustFee, precision: 2 })}
            <Typography.Text style={{ fontSize: '12px' }} delete disabled>
              {getPriceText({ price: formJson.originFee, precision: 2 })}
            </Typography.Text>
          </Descriptions.Item>
          <Descriptions.Item label="单位单价">
            {getPriceText({ price: formJson.adjustPrice, precision: 4 })}
            <Typography.Text style={{ fontSize: '12px' }} delete disabled>
              {getPriceText({ price: formJson.originPrice, precision: 4 })}
            </Typography.Text>
          </Descriptions.Item>
          {type.startsWith('9') && (
            <Descriptions.Item label="税率">
              {getTaxRateText(formJson.adjustTaxRate)}
              <Typography.Text style={{ fontSize: '12px' }} delete disabled>
                {getTaxRateText(formJson.originTaxRate)}
              </Typography.Text>
            </Descriptions.Item>
          )}
        </>
      );
  }
}

function getTaxRateText(taxRate: number | null) {
  return typeof taxRate === 'number' ? `${taxRate}%` : '--';
}

function getPowerText(useAmount: number | null) {
  return useAmount ? `${useAmount}kWh` : '--';
}

function getPriceText(data: { price: number | null; precision: number }) {
  return data.price ? `¥${(data.price / 100).toFixed(data.precision)}` : '--';
}

export type AdjustAccountGridsTableProps = {
  accountNumber: string;
};
function AdjustAccountGridsTable({ accountNumber }: AdjustAccountGridsTableProps) {
  const [getAdjustAccountGrids, { data, loading }] = useLazyAdjustAccountGrids();

  useEffect(() => {
    (async function () {
      await getAdjustAccountGrids({ variables: { adjustNo: accountNumber } });
    })();
  }, [accountNumber, getAdjustAccountGrids]);

  const columns: Array<ColumnType<AdjustGrid>> = [
    {
      title: '机柜',
      dataIndex: 'gridGuid',
    },
    {
      title: '包间',
      dataIndex: 'roomTag',
    },
    {
      title: '机柜规格',
      dataIndex: 'gridPower',
      render: (_, { gridPower }) => {
        return gridPower ? `${gridPower}kW` : '--';
      },
    },
    {
      title: '当前发生时刻',
      dataIndex: 'originMaxPowerTime',
      render: (_, { originMaxPowerTime }) => {
        return originMaxPowerTime ? dayjs(originMaxPowerTime).format('YYYY-MM-DD HH:mm:ss') : '--';
      },
    },
    {
      title: '调整后发生时刻',
      dataIndex: 'adjustMaxPowerTime',
      render: (_, { adjustMaxPowerTime }) => {
        return adjustMaxPowerTime ? dayjs(adjustMaxPowerTime).format('YYYY-MM-DD HH:mm:ss') : '--';
      },
    },
    {
      title: '当前峰值功率',
      dataIndex: 'originMaxPower',
      render: (_, { originMaxPower }) => {
        return originMaxPower ? `${originMaxPower}kW` : '--';
      },
    },
    {
      title: '调整后峰值功率',
      dataIndex: 'adjustMaxPower',
      render: (_, { adjustMaxPower }) => {
        return adjustMaxPower ? `${adjustMaxPower}kW` : '--';
      },
    },
  ];
  return (
    <Table
      loading={loading}
      columns={columns}
      dataSource={data?.batchAdjustApprovalGrids?.data}
      pagination={{ total: data?.batchAdjustApprovalGrids?.total ?? 0 }}
    />
  );
}
