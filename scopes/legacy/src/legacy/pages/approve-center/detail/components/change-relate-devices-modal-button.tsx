import React, { useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Modal } from '@manyun/base-ui.ui.modal';

import type { ChangeDeviceList } from '@manyun/ticket.gql.client.tickets';
import { ChangeOfflineDeviceTable } from '@manyun/ticket.ui.change-offline-device-table';

export function ChangeRelateDevicesModalButton({
  changeDeviceList,
}: {
  changeDeviceList: ChangeDeviceList[];
}) {
  const [open, setOpen] = useState(false);

  return (
    <>
      <Button
        type="link"
        compact
        onClick={() => {
          setOpen(true);
        }}
      >
        查看
      </Button>
      <Modal
        title="变更设备"
        footer={null}
        open={open}
        onCancel={() => {
          setOpen(false);
        }}
      >
        <ChangeOfflineDeviceTable mode="view" dataSource={changeDeviceList} />
      </Modal>
    </>
  );
}
