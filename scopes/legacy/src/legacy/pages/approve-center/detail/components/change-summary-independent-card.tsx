import dayjs from 'dayjs';
import get from 'lodash.get';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { SimpleFileList } from '@manyun/base-ui.ui.file-list';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { BackendMcUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';

export function ChangeSummaryIndependentCard({
  formJson,
}: {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  formJson: any;
}) {
  const isExecuteSuccess = formJson.exeResult === 'SUCCESS';
  const summery = JSON.parse(get(formJson, ['summery'], '{}'));

  return (
    <Card title="总结内容">
      <Descriptions contentStyle={{ paddingRight: 16 }} column={4}>
        <Descriptions.Item label="执行变更开始时间">
          {dayjs(formJson.realStartTime).format('YYYY-MM-DD HH:mm:ss')}
        </Descriptions.Item>
        <Descriptions.Item label="执行变更结束时间">
          {dayjs(formJson.realEndTime).format('YYYY-MM-DD HH:mm:ss')}
        </Descriptions.Item>
        <Descriptions.Item label="变更执行结果">
          {isExecuteSuccess ? '执行成功' : '执行失败'}
        </Descriptions.Item>
        <Descriptions.Item label="附件">
          {(formJson.fileInfoList ?? []).length > 0 ? (
            <SimpleFileList
              files={formJson.fileInfoList.map((file: BackendMcUploadFile) =>
                McUploadFile.fromApiObject(file)
              )}
            >
              <Button type="link" compact>
                查看
              </Button>
            </SimpleFileList>
          ) : (
            '--'
          )}
        </Descriptions.Item>
        <Descriptions.Item label="变更基本信息" span={2} contentStyle={{ overflow: 'hidden' }}>
          <Typography.Text ellipsis={{ tooltip: true }}>
            {get(summery, ['changeBaseInfo'], '--')}
          </Typography.Text>
        </Descriptions.Item>
        <Descriptions.Item
          label="变更方案与实际操作的差异与原因"
          span={2}
          contentStyle={{ overflow: 'hidden' }}
        >
          <Typography.Text ellipsis={{ tooltip: true }}>
            {get(summery, ['diffReason'], '--')}
          </Typography.Text>
        </Descriptions.Item>
        <Descriptions.Item label="变更发现的问题" span={2} contentStyle={{ overflow: 'hidden' }}>
          <Typography.Text ellipsis={{ tooltip: true }}>
            {get(summery, ['problem'], '--')}
          </Typography.Text>
        </Descriptions.Item>
        <Descriptions.Item label="同类变更改进措施" span={2} contentStyle={{ overflow: 'hidden' }}>
          <Typography.Text ellipsis={{ tooltip: true }}>
            {get(summery, ['improveMethod'], '--')}
          </Typography.Text>
        </Descriptions.Item>
        <Descriptions.Item label="备注" contentStyle={{ overflow: 'hidden' }}>
          <Typography.Text ellipsis={{ tooltip: true }}>
            {get(summery, ['remark'], '--')}
          </Typography.Text>
        </Descriptions.Item>
      </Descriptions>
    </Card>
  );
}
