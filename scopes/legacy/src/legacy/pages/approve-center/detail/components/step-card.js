import React from 'react';

import { But<PERSON> } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { FilePreviewWithContainer } from '@manyun/base-ui.ui.file-preview';
import { Space } from '@manyun/base-ui.ui.space';

import { BpmInstanceViewer } from '@manyun/bpm.ui.bpm-instance-viewer';

export function StepCard({ baseInfo, onSuccess }) {
  return (
    <Card
      title="流程日志"
      extra={
        <Space>
          <FilePreviewWithContainer
            file={{
              ext: '.png',
              src: `/api/workflow/process/export/img?workflowId=${baseInfo.code}`,
              name: '审批链.png',
            }}
          >
            <Button type="link" compact>
              审批链
            </Button>
          </FilePreviewWithContainer>
        </Space>
      }
    >
      <BpmInstanceViewer bpmInstance={baseInfo} onSuccess={onSuccess} />
    </Card>
  );
}

export default StepCard;
