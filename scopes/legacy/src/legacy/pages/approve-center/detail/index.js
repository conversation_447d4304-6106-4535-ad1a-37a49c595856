import LeftOutlined from '@ant-design/icons/es/icons/LeftOutlined';
import MenuDivider from 'antd/es/menu/MenuDivider';
import PageHeader from 'antd/es/page-header';
import dayjs from 'dayjs';
import get from 'lodash.get';
import flatten from 'lodash/flatten';
import uniqBy from 'lodash/uniqBy';
import moment from 'moment';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { connect, useSelector } from 'react-redux';
import { Link } from 'react-router-dom';

import { ErrorPage403 } from '@manyun/auth-hub.page.403';
import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { Badge } from '@manyun/base-ui.ui.badge';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Divider } from '@manyun/base-ui.ui.divider';
import { DownloadPdfButton } from '@manyun/base-ui.ui.download-pdf-button';
import { Explanation } from '@manyun/base-ui.ui.explanation';
import { FileList, SimpleFileList } from '@manyun/base-ui.ui.file-list';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';
import { useApprovalTypes } from '@manyun/bpm.hook.use-approval-types';
import { ApprovalStatus, BpmInstance, NodeStatus } from '@manyun/bpm.model.bpm-instance';
import { fetchBpmInstance } from '@manyun/bpm.service.fetch-bpm-instance';
import { fetchBpmRefusedRecords } from '@manyun/bpm.service.fetch-bpm-refused-records';
import { withdrawTodo } from '@manyun/bpm.service.withdraw-todo';
import { RedirectModalButton } from '@manyun/bpm.ui.approval-operation-buttons';
import { ApprovalStatusText } from '@manyun/bpm.ui.approval-status-text';
import { ApproveModalButton } from '@manyun/bpm.ui.approve-modal-button';
import { useTaskData } from '@manyun/bpm.ui.bpm-instance-viewer';
import { CommentModalButton } from '@manyun/bpm.ui.comment-modal-button';
import { CorrespondingOrderLink } from '@manyun/bpm.ui.corresponding-order-link';
import {
  ManualCarbonCopyModal,
  didHappenWithinOneMonth,
} from '@manyun/bpm.ui.manual-carbon-copy-modal';
import { RefusedApprovalTable } from '@manyun/bpm.ui.refused-approval-table';
import { QuotationType, getSaleQuoteLocales } from '@manyun/crm.model.sale-quote';
import { CompassOperateDetailTable } from '@manyun/crm.ui.compass-operate-detail-table';
import { useApps } from '@manyun/dc-brain.context.apps';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { fetchBizFileInfos } from '@manyun/dc-brain.service.fetch-biz-file-infos';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { fetchCertInfo } from '@manyun/hrm.service.fetch-cert-info';
import { ParentalLeaveRecordCard } from '@manyun/hrm.ui.parental-leave-record-card';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { generateDeviceRecordRoutePath } from '@manyun/resource-hub.route.resource-routes';
import { BorrowAssetTable } from '@manyun/resource-hub.ui.borrow-asset-table';
import { MetaTypeText } from '@manyun/resource-hub.ui.meta-type-text';
import { SpaceText } from '@manyun/resource-hub.ui.space-text';
import { CHANGE_RISK_LEVEL_TEXT_MAP } from '@manyun/ticket.model.change';
import { PopoverUser } from '@manyun/ticket.page.risk-register-detail';
import { RiskRegisterLocationDrawer } from '@manyun/ticket.page.risk-register-mutator';
import { generateChangeOfflineLocation } from '@manyun/ticket.route.ticket-routes';
import { fetchPagedPowerGrid } from '@manyun/ticket.service.fetch-paged-power-grid';
import { fetchPowerGridCount } from '@manyun/ticket.service.fetch-power-grid-count';
import { ChangeSourceText } from '@manyun/ticket.ui.change-source-text';
import ChangeTimeText from '@manyun/ticket.ui.change-time-text';
import { TicketSlaText } from '@manyun/ticket.ui.tickets-table';

import { DisplayCard } from '@manyun/dc-brain.legacy.components';
import StatusText from '@manyun/dc-brain.legacy.components/status-tag';
import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';
import { STATUS_MAP } from '@manyun/dc-brain.legacy.constants/statusColor';
import { POWER_UP_DOWN_DETAIL_RECENTLY_CLICK_ROOM } from '@manyun/dc-brain.legacy.constants/ticket';
import { getDisplayCardColor } from '@manyun/dc-brain.legacy.utils/ticket';

import { ChangeSourceListText } from '../../change/ticket/view/components/overview-card/change-source-list-text';
import { AdjustAccountDetail } from './components/adjust-account-detail';
import { BillAddFeeDetail } from './components/bill-add-fee-detail';
import { ChangeCriticalStepCard } from './components/change-critical-step-card';
import { ChangeRelateDevicesModalButton } from './components/change-relate-devices-modal-button';
import { ChangeSummaryCard } from './components/change-summary-card';
import { ChangeSummaryIndependentCard } from './components/change-summary-independent-card';
import CustructionPrint from './components/custruction-print';
import { ExistingVisitProtectionDate } from './components/existing-visit-protection-date';
import { FormJsonRender } from './components/form-json-render';
import { PowerOnTimeDetail } from './components/power-on-time-detail';
import { RegionalCardList } from './components/regional-card-list.tsx';
import StepCard from './components/step-card';
import { VisitorTable } from './components/visitor-table';

const getColumns = (taskSubTypeName, taskSubType) => [
  {
    title: '机柜',
    dataIndex: 'gridTag',
  },
  {
    title: '对应列头柜',
    dataIndex: 'frontGrids',
    render: (_, record) => {
      if (!Array.isArray(record.frontGrids)) {
        return '--';
      }
      return (
        <Space size={0} split={<Divider type="vertical" spaceSize="mini" emphasis />}>
          {record.frontGrids?.map(item => (
            <Link
              key={item.deviceGuid}
              to={generateDeviceRecordRoutePath({
                guid: item.deviceGuid,
              })}
            >
              {item.deviceName}
            </Link>
          ))}
        </Space>
      );
    },
  },
  {
    title: '对应支路空开',
    dataIndex: 'pduDevices',
    render: (_, record) => {
      if (!Array.isArray(record.pduDevices)) {
        return '--';
      }
      return (
        <Space size={0} split={<Divider type="vertical" spaceSize="mini" emphasis />}>
          {record.pduDevices?.map(item => (
            <Link
              key={item.deviceGuid}
              to={generateDeviceRecordRoutePath({
                guid: item.deviceGuid,
              })}
            >
              {item.deviceName}
            </Link>
          ))}
        </Space>
      );
    },
  },
  {
    title: '设计功率',
    dataIndex: 'ratedPower',
    render: text => `${text}KW`,
  },
  {
    title: '机柜类型',
    dataIndex: ['gridType', 'name'],
  },
  {
    title: (
      <Explanation
        style={{ color: 'inherit', fontSize: 'inherit' }}
        size="large"
        iconType="question"
        tooltip={{ title: '圆点符号表示处理人手动修订过时间' }}
      >
        实际{taskSubTypeName}电时间
      </Explanation>
    ),
    dataIndex: 'powerOnTime',
    render: (text, record) => {
      return (
        <Space>
          {text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : ''}
          {record.modified && <Badge color="gold" />}
        </Space>
      );
    },
  },
  {
    title: '状态',
    dataIndex: 'checkStatus',
    render: text => {
      if (text.code === 'UNDONE') {
        return (
          <StatusText fontSize={14} status={STATUS_MAP.WARNING}>
            {text.name}
          </StatusText>
        );
      }
      if (text.code === 'COMPLETED') {
        return (
          <StatusText fontSize={14} status={STATUS_MAP.NORMAL}>
            {text.name}
          </StatusText>
        );
      }
      if (text.code === 'FAILURE') {
        return (
          <StatusText fontSize={14} status={STATUS_MAP.ALARM}>
            {text.name}
          </StatusText>
        );
      }
    },
  },
  {
    title: '操作人',
    dataIndex: 'operatorName',
    render: (text, { operatorBy }) => (
      <UserLink target="_blank" userName={text} userId={operatorBy} />
    ),
  },
  {
    title: '操作',
    dataIndex: 'operation',
    render: (text, { modified, executeTime, modReason, powerOnTime }) => {
      return modified ? (
        <PowerOnTimeDetail
          taskSubType={taskSubType}
          executeTime={executeTime}
          modReason={modReason}
          powerOnTime={powerOnTime}
        />
      ) : null;
    },
  },
];

const LongTermRisk = ({ title }) => {
  return (
    <div style={{ display: 'flex', alignItems: 'baseline' }}>
      <div
        style={{
          marginRight: '8px',
          height: '6px',
          width: '6px',
          backgroundColor: title ? '#FAAD14' : '#d9d9d9',
          borderRadius: '50%',
          position: 'relative',
          top: '-2px',
        }}
      />
      {title ? '是' : '否'}
    </div>
  );
};
function parseJsonWithCache(jsonString, defaultValue = null) {
  // 如果输入为空，直接返回默认值
  if (!jsonString) {
    return defaultValue;
  }
  try {
    const result = JSON.parse(jsonString);
    return result;
  } catch (error) {
    console.error('JSON 解析错误:', error);
  }
}
export function ApproveCenterDetail({ curUserId }) {
  const [configUtil] = useConfigUtil();
  const showRiskRegister = configUtil.getScopeCommonConfigs('legacy')?.risk?.showRiskRegister;
  const approvalListType = configUtil.getScopeCommonConfigs('bpm')?.approval?.approvalListType;
  const { approvalTypeTextKeyMap, approvalTypes } = useApprovalTypes();
  const isTypeAbleToShowFiles = Object.values(approvalTypes);

  const [refusedRecords, setRefusedRecords] = useState([]);
  const [baseInfo, setBaseInfo] = useState(null);
  const { fetchFlag, setFetchFlag } = useState(false);
  const [attachmentInfos, setAttachmentInfos] = useState([]);
  const [formJson, setFormJson] = useState();
  const [errorCode, setErrorCode] = useState();
  const [gridCount, setGridCount] = useState([]);
  const [checkedRoom, setCheckedRoom] = useState(undefined);
  const [pagedPowerGridsPagination, setPagedPowerGridsPagination] = useState({
    pageNum: 1,
    pageSize: 10,
  });
  const [pagedPowerGridData, setPagedPowerGridData] = useState({
    total: 0,
    data: [],
  });
  const [taskSubType, setTaskSubType] = useState('');
  const locales = useMemo(() => getSaleQuoteLocales(), []);
  const { sales } = useApps();
  const { id, relateId } = getLocationSearchMap(window.location.search, ['id', 'relateId']);
  function getActualTargetType(type) {
    switch (type) {
      case approvalTypes.LEAVE_PROCESS:
      case approvalTypes.EXCHANGE_PROCESS:
      case approvalTypes.REST_PROCESS:
        return 'ALTER';
      case approvalTypes.SUPPLY_CHECK_PROCESS:
        return 'SUPPLY_CHECK';
      case approvalTypes.OVERTIME_PROCESS:
        return 'OVERTIME_WORK_APPLY';
      case approvalTypes.OUTWORK_PROCESS:
        return 'OUTWORK';
      case approvalTypes.OUT_DOOR:
      case approvalTypes.IN_DOOR:
        return 'ACCESS';
      case approvalTypes.EX_WAREHOUSE:
        return 'WAREHOUSE';
      case approvalTypes.INSPECT_END:
        return 'INSPECTION';
      case approvalTypes.MAINTENANCE_END:
        return 'MAINTENANCE';
      case approvalTypes.REPAIR_END:
        return 'REPAIR';
      case approvalTypes.EVENT_AUDIT_PROCESS:
        return 'EVENT';
      case approvalTypes.CARD_OFF:
      case approvalTypes.CARD_CHANGE:
      case approvalTypes.CARD_APPLY:
      case approvalTypes.CARD_EXCHANGE:
        return 'ACCESS_CARD_AUTH';
      case approvalTypes.ON_OFF_PROCESS:
        return 'ON_OFF';
      case approvalTypes.POWER_END:
      case approvalTypes.POWER:
        return 'POWER';
      case approvalTypes.RISK_REGISTER_CLOSE:
      case approvalTypes.RISK_REGISTER_EVAL:
      case approvalTypes.RISK_TIME_EXTENSION:
      case approvalTypes.RISK_ROC_UPGRADE:
        return 'RISK_REGISTER';
      case approvalTypes.BILL_ADJUST:
      case approvalTypes.BILL_FEE_ADD:
        return 'BILL';
      case approvalTypes.CHANGE_ONLINE_APPLY:
      case approvalTypes.CHANGE_ONLINE_SUMMERY:
      case approvalTypes.CHANGE_ONLINE_DELAY:
      case approvalTypes.CHANGE_ONLINE_TEMPLATE:
      case approvalTypes.CHANGE_SUMMERY:
      case approvalTypes.STANDARD_CHANGE_TEMPLATE:
      case approvalTypes.STANDARD_CHANGE_TEMPLATE_POSTPONE:
        return 'CHANGE';
      default:
        return type;
    }
  }

  const getDetail = useCallback(async () => {
    if (id) {
      const { error, data } = await fetchBpmInstance({
        code: id,
        relateCode: relateId,
      });

      if (data) {
        setFormJson(data.formJson);
        setBaseInfo(data);
      }
      if (error) {
        message.error(error.message);
        setErrorCode(error.code);
      }
    }
  }, [id, relateId]);

  useEffect(() => {
    (async () => {
      await getDetail();
    })();
  }, [getDetail]);
  const instId = get(baseInfo, 'code');
  const applyTime = get(baseInfo, 'applyTime');
  const bizId = get(baseInfo, 'bizId');
  const bizType = get(baseInfo, 'bizType');
  const records = get(baseInfo, 'operationRecords');
  const status = get(baseInfo, 'status');
  const oaType = get(baseInfo, 'oaType');
  const idcTag = get(baseInfo, 'idc');
  const applyUserId = get(baseInfo, 'applyUser');
  const applyUserName = get(baseInfo, 'applyUserName');
  const blockGuid = get(baseInfo, 'blockGuid');
  const roomTag = get(baseInfo, 'roomTag');
  const sla = get(baseInfo, 'sla');
  const averageSla = get(baseInfo, 'bizTypeSla');
  const attachmentType = get(baseInfo, 'attachmentType');
  const processChargeUser = get(baseInfo, 'processChargeUser');
  const processChargeUserName = get(baseInfo, 'processChargeUserName');
  const approveUserList = getApproveUserIdList(get(baseInfo, 'operationRecords'));
  const isBpmRevoked = status === ApprovalStatus.Revoke;
  const isApproveFromSales = bizType === approvalTypes.SALES_QUOTATION;
  const needAudit = baseInfo?.formJson?.needAudit;
  const config = useSelector(selectCurrentConfig);
  const wfProperties = get(baseInfo, 'wfProperties')
    ? parseJsonWithCache(get(baseInfo, 'wfProperties'))
    : {};

  const configUtils = new ConfigUtil(config);
  const ticketScopeCommonConfigs = configUtils.getScopeCommonConfigs('ticket');
  const changesTicketCreateChangeOffline =
    ticketScopeCommonConfigs.changes.features.createChangeOffline;
  const showVisitorFile =
    bizType === approvalTypes.VISITOR &&
    approvalListType &&
    (formJson?.taskSubType?.includes('政府') || formJson?.taskSubType?.includes('商务'));

  const [{ taskData }] = useTaskData({
    operationRecords: records?.length ? records : [],
    userId: String(curUserId),
    processChargeUser,
    processChargeUserName,
  });

  /** 新增费用审批如果有formJson，需要隐藏展示基本信息中的审批内容字段 */
  const showBillFeeAddContent = [approvalTypes.BILL_FEE_ADD].includes(bizType) && !formJson;

  const location = roomTag ? blockGuid + '.' + roomTag : blockGuid || idcTag;

  const getCertFile = () => {
    fetchCertInfo({
      id: bizId,
    }).then(data => {
      const info = data?.data;

      const file = McUploadFile.fromApiObject({
        fileFormat: info?.fileType,
        filePath: String(info?.fileId),
        id: info?.fileId,
        fileType: null,
        fileName: '证书图片',
        fileSize: 0,
        gmtCreate: null,
        gmtModified: null,
        targetId: bizId,
        targetType: bizType,
        uploadBy: 0,
        uploadByName: '',
        uploadTime: '',
      });

      setAttachmentInfos([
        { ...file, url: file.ext === '.pdf' ? pdfPng : file.src, src: file.src },
      ]);
    });
  };

  useEffect(() => {
    if (!bizId || !bizType || status !== ApprovalStatus.Approving) {
      return;
    }
    (async function () {
      const { error, data } = await fetchBpmRefusedRecords({
        bizId: bizId,
        bizType: bizType,
      });
      if (error) {
        message.error(error.message);
        return;
      }
      setRefusedRecords(data.data);
    })();
  }, [bizId, bizType, status]);

  useEffect(() => {
    if (!bizId || !bizType) {
      return;
    }

    if (bizId && bizType === 'CERT_APPROVAL') {
      getCertFile();
      return;
    }

    let type = bizType;
    const targetId = bizId;

    type = getActualTargetType(type);
    let params = {
      targetId: targetId,
      targetType: type,
      extensionType: getActualFileType(type),
    };
    if (attachmentType) {
      let fileTypeList = attachmentType.split('|');
      if (type === 'EVENT') {
        fileTypeList = fileTypeList.push('EVENT_AUDIT');
      }
      params = {
        targetId: targetId,
        targetType: type,
        fileTypeList,
      };
    }
    if (type === 'CHANGE') {
      params = {
        ...params,
        fileTypeList: ['CHANGE_ORDER', 'CHANGE_ORDER_CUSTOMER'],
        extensionType: null,
      };
    }

    if (!fetchFlag) {
      fetchBizFileInfos(params).then(({ data, error }) => {
        if (error) {
          console.error(error);
          setFetchFlag(true);
          return;
        }

        setAttachmentInfos(data.data);
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [bizId, bizType, attachmentType]);

  useEffect(() => {
    if (!idcTag || !blockGuid || bizType !== approvalTypes.POWER_END || !bizId) {
      return;
    }
    (async function () {
      const [, blockTag] = blockGuid.split('.');
      const { data } = await fetchPowerGridCount({ idc: idcTag, taskNo: bizId, block: blockTag });
      if (data) {
        const countList = [];
        for (const i in data) {
          countList.push({ roomTag: i, ...data[i] });
        }
        setGridCount(countList);
      }
    })();
  }, [idcTag, blockGuid, bizId, bizType, approvalTypes.POWER_END]);

  useEffect(() => {
    (async function () {
      if (!blockGuid || !idcTag || !checkedRoom || !bizId) {
        return;
      }

      const [, blockTag] = blockGuid.split('.');
      const { error, data } = await fetchPagedPowerGrid({
        page: pagedPowerGridsPagination.pageNum,
        pageSize: pagedPowerGridsPagination.pageSize,
        block: blockTag,
        room: checkedRoom.roomTag,
        idc: idcTag,
        taskNo: bizId,
        sortModel: 'END_APPROVE',
      });
      if (error) {
        message.error(error.message);
        return;
      }
      setPagedPowerGridData(data);
    })();
  }, [
    idcTag,
    blockGuid,
    pagedPowerGridsPagination.pageNum,
    pagedPowerGridsPagination.pageSize,
    bizId,
    checkedRoom,
  ]);

  //正常审批人
  const normalApproval = taskData?.userList
    ?.filter(val => val?.type !== 'PROCESS_CHARGER')
    ?.map(item => item?.id);

  const basicOperationButtonCaseSpecial = Boolean(
    oaType === 'LOCAL' &&
      status === ApprovalStatus.Approving &&
      taskData?.taskResult &&
      Array.isArray(taskData?.userList) &&
      normalApproval.includes(String(curUserId))
  );

  const basicOperationButtonCase = Boolean(
    oaType === 'LOCAL' &&
      status === ApprovalStatus.Approving &&
      taskData?.taskResult &&
      Array.isArray(taskData?.userList) &&
      taskData.userList?.map(item => item.id).includes(String(curUserId))
  );

  const revokeButtonCase = status === ApprovalStatus.Approving && applyUserId === curUserId;
  const printButtonCase =
    (status === ApprovalStatus.Refuse || status === ApprovalStatus.Pass) &&
    bizType === approvalTypes.CONSTRUCTION_APPLY;
  const carbonCopyButtonCase = status === ApprovalStatus.Pass && didHappenWithinOneMonth(applyTime);
  // 目前暂定审批评论在审批任何状态下都可进行
  const commentButtonCase = status !== ApprovalStatus.Revoke;
  const pdfDownloadCase =
    status === ApprovalStatus.Pass && bizType === approvalTypes.SALES_QUOTATION;
  const renderDescriptionsItem = (text, type, bizType) => {
    if (bizType === approvalTypes.CHANGE) {
      return '变更执行申请';
    }
    if (bizType === approvalTypes.CHANGE_SUMMERY) {
      return '变更执行总结';
    }
    if (text === null || text === undefined) {
      return BLANK_PLACEHOLDER;
    }

    return (
      <Typography.Paragraph style={{ marginBottom: 0 }} ellipsis={{ tooltip: true }}>
        {type === 'reason' ? text : getFormattedContent(text)}
      </Typography.Paragraph>
    );
  };
  const getBasicOperationButtons = approveUserList => {
    return (
      <>
        {basicOperationButtonCase && taskData.taskId !== '' && (
          <>
            {basicOperationButtonCaseSpecial && (
              <>
                <ApproveModalButton
                  instId={instId}
                  taskId={taskData.taskId}
                  result={0}
                  text="同意"
                  type="primary"
                  onSuccess={getDetail}
                />
                <ApproveModalButton
                  instId={instId}
                  taskId={taskData.taskId}
                  result={1}
                  text="拒绝"
                  type="danger"
                  bizType={bizType}
                  bizId={bizId}
                  needAudit={needAudit}
                  danger
                  onSuccess={getDetail}
                />
              </>
            )}
            {bizType !== approvalTypes.ORDER_APPROVAL && (
              <RedirectModalButton
                instId={instId}
                taskId={taskData.taskId}
                applyUserId={applyUserId}
                userId={curUserId}
                filterUserIdList={[...approveUserList, applyUserId]}
                blockGuid={isApproveFromSales ? null : blockGuid}
                text="转交"
                type="warning"
                onSuccess={getDetail}
              />
            )}
          </>
        )}
      </>
    );
  };
  const getRevokeButton = () => {
    return (
      <>
        {revokeButtonCase && (
          <Popconfirm
            key="remove"
            title={`确认要撤回${instId}的审批吗？`}
            content="撤回后数据将不可恢复。"
            onConfirm={() =>
              revokeApprove({ instId: instId, operator: String(curUserId) }, getDetail)
            }
          >
            <Button>撤回</Button>
          </Popconfirm>
        )}
      </>
    );
  };
  const getPrintButton = () => {
    return (
      <>
        {printButtonCase && (
          <CustructionPrint
            formJson={baseInfo ? formJson : null}
            reason={baseInfo?.reason}
            approveRecords={records}
          />
        )}
      </>
    );
  };
  const getCarbonCopyButton = () => {
    return (
      <>
        {carbonCopyButtonCase && (
          <ManualCarbonCopyModal instId={instId} type="primary" onSuccess={getDetail} />
        )}
      </>
    );
  };
  const getCommentButton = () => {
    return (
      <>
        {commentButtonCase && (
          <CommentModalButton instId={instId} applyUserId={applyUserId} onSuccess={getDetail} />
        )}
      </>
    );
  };

  const getFileCardTitle = type => {
    const types = attachmentType ? attachmentType.split('|') : [];
    if (types.length > 1 && types.includes('POWER_PROVE_FILE')) {
      return '附件信息 证明材料';
    }
    if (types.length === 1 && types[0] === 'POWER_PROVE_FILE') {
      return '证明材料';
    }
    if (getActualTargetType(type) === 'CHANGE') {
      return '变更方案附件';
    }
    return '附件信息';
  };

  const getPowerSubType = taskSubType => {
    return taskSubType === 'POWER_OFF' ? '下' : '上';
  };

  const getPdfDownloadButton = () => {
    return (
      <>
        {pdfDownloadCase && (
          <DownloadPdfButton
            type="default"
            exportElement={document.getElementById('bpm-content')}
            pdfName={formJson?.customerName ? `${formJson.customerName}的销售报价审批单` : '--'}
          />
        )}
      </>
    );
  };
  const isYGChange = changesTicketCreateChangeOffline === 'full';
  const shouldFooterToolBarAppear = [
    carbonCopyButtonCase,
    printButtonCase,
    revokeButtonCase,
    basicOperationButtonCase,
    commentButtonCase,
  ].includes(true);

  if (baseInfo) {
    const isChangeIndependent = bizId.startsWith('N');
    const auditDesc = get(formJson, ['auditDesc']);
    const occurTime = get(formJson, ['occurTime']);
    const secondCategoryName = get(formJson, ['secondCategoryName']);
    const eventId = get(formJson, ['eventId']);
    const isNewEventBpm = eventId && eventId.startsWith('N');
    const riskOwnerIdList = formJson?.riskOwnerInfoList
      ? JSON.parse(formJson?.riskOwnerInfoList)?.map(item => item?.id)
      : [];

    return (
      <>
        <Space id="bpm-content" style={{ display: 'flex' }} direction="vertical">
          <Card title="基本信息">
            {bizType === 'VISITOR' && <ExistingVisitProtectionDate bizId={bizId} />}
            <Space style={{ display: 'flex', width: '100%' }} direction="vertical" size={0}>
              <Descriptions column={4}>
                <Descriptions.Item label="审批ID">{instId}</Descriptions.Item>
                <Descriptions.Item label="标题" contentStyle={{ overflow: 'hidden' }}>
                  <Typography.Text ellipsis={{ tooltip: true }}>
                    {get(baseInfo, 'title')}
                  </Typography.Text>
                </Descriptions.Item>
                <Descriptions.Item label="创建人">
                  {applyUserId === 0 ? (
                    '系统'
                  ) : (
                    <UserLink userId={applyUserId} userName={applyUserName} />
                  )}
                </Descriptions.Item>
                <Descriptions.Item label="状态">
                  <ApprovalStatusText approveStatus={status} />
                </Descriptions.Item>
                <Descriptions.Item label="类型">
                  {approvalTypeTextKeyMap[bizType]}
                </Descriptions.Item>
                {![approvalTypes.SALES_QUOTATION].includes(bizType) && !showVisitorFile && (
                  <Descriptions.Item label="原因" contentStyle={{ overflow: 'hidden' }}>
                    {renderDescriptionsItem(baseInfo.reason, 'reason', bizType)}
                  </Descriptions.Item>
                )}
                {![approvalTypes.PF_PERIOD_KPI_RECORD].includes(bizType) && (
                  <>
                    <Descriptions.Item label="审批所用时长">
                      {isBpmRevoked || !sla ? '--' : <TicketSlaText delay={sla} unit="SECOND" />}
                    </Descriptions.Item>
                    <Descriptions.Item label="同类审批单所用时长">
                      {isBpmRevoked || !averageSla ? (
                        '--'
                      ) : (
                        <TicketSlaText delay={averageSla} unit="SECOND" />
                      )}
                    </Descriptions.Item>
                  </>
                )}
              </Descriptions>
              <Descriptions column={4}>
                {/* 施工单和权限申请不显示对应单号 和位置*/}
                {bizId &&
                  ![
                    approvalTypes.CONSTRUCTION_APPLY,
                    approvalTypes.AUTH_APPLY,
                    approvalTypes.CUS_EFFECTED_APPLY,
                    approvalTypes.CUS_FORBIDDEN_APPLY,
                    approvalTypes.ORDER_APPROVAL,
                    approvalTypes.PF_PERIOD_KPI_RECORD,
                    approvalTypes.REPORT_AUTH_APPLY,
                    approvalTypes.CERT_APPROVAL,
                  ].includes(bizType) && (
                    <Descriptions.Item label="对应单号">
                      <CorrespondingOrderLink
                        targetName={bizId}
                        targetId={bizId}
                        targetType={bizType}
                        idcTag={idcTag}
                        blockGuid={blockGuid}
                      />
                    </Descriptions.Item>
                  )}
                {bizId && [approvalTypes.PF_PERIOD_KPI_RECORD].includes(bizType) && (
                  <Descriptions.Item label="对应单号">--</Descriptions.Item>
                )}
                {(idcTag || blockGuid) &&
                  ![
                    approvalTypes.CONSTRUCTION_APPLY,
                    approvalTypes.CARD_OFF,
                    approvalTypes.CARD_APPLY,
                    approvalTypes.CUS_EFFECTED_APPLY,
                    approvalTypes.CUS_FORBIDDEN_APPLY,
                    approvalTypes.VISITOR,
                    approvalTypes.ORDER_APPROVAL,
                    approvalTypes.CARD_EXCHANGE,
                    approvalTypes.CARD_CHANGE,
                    approvalTypes.MAINTENANCE_END,
                    approvalTypes.OPERATION_DATA_CHANGE,
                  ].includes(bizType) && (
                    <Descriptions.Item label="位置">{location}</Descriptions.Item>
                  )}
                {bizType === approvalTypes.PF_PERIOD_KPI_RECORD && formJson && (
                  <>
                    <Descriptions.Item label="人员">
                      {formJson?.staffName || '--'}
                    </Descriptions.Item>
                    <Descriptions.Item label="发生日期">
                      {formJson?.occurTime ? dayjs(formJson?.occurTime).format('YYYY-MM-DD') : '--'}
                    </Descriptions.Item>
                    <Descriptions.Item label="指标名称" contentStyle={{ overflow: 'hidden' }}>
                      <Typography.Text ellipsis={{ tooltip: true }}>
                        {formJson?.name || '--'}
                      </Typography.Text>
                    </Descriptions.Item>
                    <Descriptions.Item label="衡量标准" contentStyle={{ overflow: 'hidden' }}>
                      <Typography.Text ellipsis={{ tooltip: true }}>
                        {formJson?.metrics || '--'}
                      </Typography.Text>
                    </Descriptions.Item>
                    <Descriptions.Item label="评分标准" contentStyle={{ overflow: 'hidden' }}>
                      <Typography.Text ellipsis={{ tooltip: true }}>
                        {formJson?.gradeCriteria
                          ?.map((item, index) => {
                            if (index === 0) {
                              return `${item?.criteria}(加${item.defaultGrade}分)`;
                            }
                          })
                          .join('、')}
                      </Typography.Text>
                    </Descriptions.Item>
                    <Descriptions.Item label="考评分" span={1}>
                      {formJson?.grade ? `加${formJson?.grade}分` : '--'}
                    </Descriptions.Item>
                  </>
                )}
                {bizType === approvalTypes.N_EVENT_LEVEL_CHG && formJson && (
                  <>
                    <Descriptions.Item label="原事件等级" span={1}>
                      {get(formJson, ['originLevelName'], '--')}
                    </Descriptions.Item>
                    <Descriptions.Item label="校正后等级" span={1}>
                      {get(formJson, ['targetLevelName'], '--')}
                    </Descriptions.Item>
                    <Descriptions.Item
                      label="事件标题"
                      contentStyle={{ overflow: 'hidden' }}
                      span={4}
                    >
                      <Typography.Text ellipsis={{ tooltip: true }}>
                        {get(formJson, ['eventTitle'], '--')}
                      </Typography.Text>
                    </Descriptions.Item>
                  </>
                )}
                {bizType === approvalTypes.EVENT_AUDIT_PROCESS && formJson && (
                  <>
                    <Descriptions.Item label="事件来源" span={1}>
                      {get(formJson, ['eventSourceName'], '--')}
                    </Descriptions.Item>
                    <Descriptions.Item label="事件发生时间" span={1}>
                      {occurTime ? dayjs(occurTime).format('YYYY-MM-DD HH:mm:ss') : '--'}
                    </Descriptions.Item>
                    <Descriptions.Item label={isNewEventBpm ? '事件等级' : '事件级别'} span={1}>
                      {get(formJson, ['eventLevelName'], '--')}
                    </Descriptions.Item>
                    <Descriptions.Item label={isNewEventBpm ? '专业分类' : '事件类型'} span={1}>
                      {(isNewEventBpm
                        ? get(formJson, ['categoryName'])
                        : secondCategoryName
                          ? `${get(formJson, ['topCategoryName'])}/${secondCategoryName}`
                          : undefined) ?? '--'}
                    </Descriptions.Item>
                    {showRiskRegister ? (
                      <>
                        <Descriptions.Item
                          label="事件标题"
                          contentStyle={{ overflow: 'hidden' }}
                          span={2}
                        >
                          <Typography.Text ellipsis={{ tooltip: true }}>
                            {get(formJson, ['eventDesc'], '--')}
                          </Typography.Text>
                        </Descriptions.Item>
                        <Descriptions.Item
                          label="复盘内容"
                          contentStyle={{ overflow: 'hidden' }}
                          span={3}
                        >
                          <Typography.Text ellipsis={{ tooltip: true }}>
                            {auditDesc || '--'}
                          </Typography.Text>
                        </Descriptions.Item>
                        <Descriptions.Item label="复盘附件" contentStyle={{ overflow: 'hidden' }}>
                          {attachmentInfos &&
                          Array.isArray(attachmentInfos) &&
                          attachmentInfos?.length > 0 ? (
                            <SimpleFileList files={attachmentInfos}>
                              <Button type="link" compact>
                                查看
                              </Button>
                            </SimpleFileList>
                          ) : (
                            '--'
                          )}
                        </Descriptions.Item>
                      </>
                    ) : (
                      <>
                        <Descriptions.Item
                          label="事件描述"
                          contentStyle={{ overflow: 'hidden' }}
                          span={1}
                        >
                          <Typography.Text ellipsis={{ tooltip: true }}>
                            {get(formJson, ['eventDesc'], '--')}
                          </Typography.Text>
                        </Descriptions.Item>
                        <Descriptions.Item label="是否转为问题">
                          <>
                            {typeof formJson?.convertToProblem === 'boolean' ? (
                              <LongTermRisk title={formJson?.convertToProblem} />
                            ) : (
                              '--'
                            )}
                            {formJson?.questionRiskId ? (
                              <div>
                                <Divider
                                  type="vertical"
                                  style={{ marginLeft: 4, marginRight: 4 }}
                                />
                                问题ID：
                                <a
                                  href={`/page/independent-tickets/risk-register/${formJson?.questionRiskId}/detail`}
                                >
                                  {formJson?.questionRiskId}
                                </a>
                              </div>
                            ) : null}
                          </>
                        </Descriptions.Item>
                        {auditDesc && (
                          <Descriptions.Item
                            label="复盘内容"
                            contentStyle={{ overflow: 'hidden' }}
                            span={3}
                          >
                            <Typography.Text ellipsis={{ tooltip: true }}>
                              {auditDesc || '--'}
                            </Typography.Text>
                          </Descriptions.Item>
                        )}
                      </>
                    )}
                  </>
                )}
                {(![
                  approvalTypes.SALES_QUOTATION,
                  approvalTypes.CARD_APPLY,
                  approvalTypes.CARD_CHANGE,
                  approvalTypes.AUTH_APPLY,
                  approvalTypes.CARD_OFF,
                  approvalTypes.CUS_EFFECTED_APPLY,
                  approvalTypes.CUS_FORBIDDEN_APPLY,
                  approvalTypes.OUT_DOOR,
                  approvalTypes.IN_DOOR,
                  approvalTypes.CHANGE,
                  approvalTypes.CHANGE_SUMMERY,
                  approvalTypes.BILL_FEE_ADD,
                  approvalTypes.CUST_QUOTATION_ADD,
                  approvalTypes.CUST_QUOTATION_UPD,
                  approvalTypes.LEAVE_PROCESS,
                  approvalTypes.BORROW,
                  approvalTypes.RENEW,
                  approvalTypes.TRANSFER,
                  approvalTypes.ON_OFF_PROCESS,
                  approvalTypes.STANDARD_CHANGE_TEMPLATE,
                  approvalTypes.STANDARD_CHANGE_TEMPLATE_POSTPONE,
                  approvalTypes.ORDER_APPROVAL,
                  approvalTypes.EVENT_AUDIT_PROCESS,
                  approvalTypes.N_EVENT_LEVEL_CHG,
                  approvalTypes.VISITOR,
                  approvalTypes.RISK_REGISTER_EVAL,
                  approvalTypes.RISK_REGISTER_CLOSE,
                  approvalTypes.RISK_TIME_EXTENSION,
                  approvalTypes.RISK_ROC_UPGRADE,
                  approvalTypes.CHANGE_ONLINE_APPLY,
                  approvalTypes.CHANGE_ONLINE_SUMMERY,
                  approvalTypes.CHANGE_ONLINE_DELAY,
                  approvalTypes.CHANGE_ONLINE_TEMPLATE,
                  approvalTypes.PF_PERIOD_KPI_RECORD,
                  approvalTypes.CARD_EXCHANGE,
                  approvalTypes.MAINTENANCE_END,
                  approvalTypes.OPERATION_DATA_CHANGE,
                  approvalTypes.REPORT_AUTH_APPLY,
                  approvalTypes.CERT_APPROVAL,
                ].includes(bizType) ||
                  showBillFeeAddContent) && (
                  <Descriptions.Item label="审批内容" contentStyle={{ overflow: 'hidden' }}>
                    {renderDescriptionsItem(baseInfo.content, 'content')}
                  </Descriptions.Item>
                )}
                {[
                  approvalTypes.RISK_REGISTER_EVAL,
                  approvalTypes.RISK_REGISTER_CLOSE,
                  approvalTypes.RISK_TIME_EXTENSION,
                  approvalTypes.RISK_ROC_UPGRADE,
                ].includes(bizType) &&
                  formJson &&
                  !showRiskRegister && (
                    <Descriptions.Item label="审批内容" contentStyle={{ overflow: 'hidden' }}>
                      {renderDescriptionsItem(baseInfo?.content, 'content')}
                    </Descriptions.Item>
                  )}
                {[
                  approvalTypes.RISK_REGISTER_EVAL,
                  approvalTypes.RISK_REGISTER_CLOSE,
                  approvalTypes.RISK_TIME_EXTENSION,
                  approvalTypes.RISK_ROC_UPGRADE,
                ].includes(bizType) &&
                  formJson &&
                  showRiskRegister && (
                    <>
                      <Descriptions.Item label="风险来源">
                        {formJson?.riskResourceCodeName}
                      </Descriptions.Item>
                      <Descriptions.Item label="风险专业">
                        {formJson?.riskTopTypeName}
                      </Descriptions.Item>
                      <Descriptions.Item label="风险等级">
                        {formJson?.riskLevelName}
                      </Descriptions.Item>
                      <Descriptions.Item label="风险位置">
                        <RiskRegisterLocationDrawer riskId={bizId} />
                      </Descriptions.Item>
                      {bizType === approvalTypes.RISK_REGISTER_EVAL && (
                        <>
                          <Descriptions.Item label="计划完成时间">
                            {formJson?.planCompleteTime &&
                              moment(formJson.planCompleteTime).format('YYYY-MM-DD HH:mm')}
                          </Descriptions.Item>
                          <Descriptions.Item label="是否长期风险项">
                            <LongTermRisk title={formJson?.longTermRisk} />
                          </Descriptions.Item>
                        </>
                      )}
                      {bizType === approvalTypes.RISK_REGISTER_CLOSE && (
                        <>
                          <Descriptions.Item label="计划完成时间">
                            {formJson?.planCompleteTime &&
                              moment(formJson.planCompleteTime).format('YYYY-MM-DD HH:mm')}
                          </Descriptions.Item>
                          <Descriptions.Item label="实际完成时间">
                            {formJson?.completeTime &&
                              moment(formJson.completeTime).format('YYYY-MM-DD HH:mm')}
                          </Descriptions.Item>
                        </>
                      )}
                      {bizType === approvalTypes.RISK_TIME_EXTENSION && (
                        <>
                          <Descriptions.Item label="原计划完成时间">
                            {formJson?.originPlanCompleteTime &&
                              moment(formJson.originPlanCompleteTime).format('YYYY-MM-DD HH:mm')}
                          </Descriptions.Item>
                          <Descriptions.Item label="延期至">
                            {formJson?.planCompleteTime &&
                              moment(formJson.planCompleteTime).format('YYYY-MM-DD HH:mm')}
                          </Descriptions.Item>
                        </>
                      )}
                      <Descriptions.Item
                        label="风险描述"
                        span={2}
                        contentStyle={{ overflow: 'hidden' }}
                      >
                        <Typography.Text ellipsis={{ tooltip: true }}>
                          {formJson?.riskDesc}
                        </Typography.Text>
                      </Descriptions.Item>
                      <Descriptions.Item label="责任人" contentStyle={{ overflow: 'hidden' }}>
                        <PopoverUser riskOwnerIdList={riskOwnerIdList} userLink />
                      </Descriptions.Item>
                      {bizType === approvalTypes.RISK_REGISTER_EVAL && (
                        <>
                          <Descriptions.Item label="风险识别人">
                            {formJson?.riskIdentifier}
                          </Descriptions.Item>
                          <Descriptions.Item
                            label="影响范围描述"
                            contentStyle={{ overflow: 'hidden' }}
                            span={2}
                          >
                            <Typography.Text ellipsis={{ tooltip: true }}>
                              {formJson?.riskInfluenceDesc}
                            </Typography.Text>
                          </Descriptions.Item>
                        </>
                      )}
                      {bizType === approvalTypes.RISK_REGISTER_CLOSE && (
                        <Descriptions.Item label="是否长期风险项">
                          <LongTermRisk title={formJson?.longTermRisk} />
                        </Descriptions.Item>
                      )}
                      {(bizType === approvalTypes.RISK_TIME_EXTENSION ||
                        bizType === approvalTypes.RISK_ROC_UPGRADE) && (
                        <>
                          <Descriptions.Item label="是否长期风险项">
                            <LongTermRisk title={formJson?.longTermRisk} />
                          </Descriptions.Item>
                          <Descriptions.Item
                            label="影响范围描述"
                            contentStyle={{ overflow: 'hidden' }}
                            span={2}
                          >
                            <Typography.Text ellipsis={{ tooltip: true }}>
                              {formJson?.riskInfluenceDesc}
                            </Typography.Text>
                          </Descriptions.Item>
                        </>
                      )}
                    </>
                  )}
                {[
                  approvalTypes.CHANGE_ONLINE_APPLY,
                  approvalTypes.CHANGE_ONLINE_SUMMERY,
                  approvalTypes.CHANGE_ONLINE_DELAY,
                  approvalTypes.CHANGE_ONLINE_TEMPLATE,
                ].includes(bizType) && (
                  <>
                    {bizType === approvalTypes.CHANGE_ONLINE_TEMPLATE && (
                      <>
                        <Descriptions.Item label="模板名称" contentStyle={{ overflow: 'hidden' }}>
                          <Typography.Paragraph ellipsis={{ tooltip: true }}>
                            {formJson?.templateName}
                          </Typography.Paragraph>
                        </Descriptions.Item>
                        <Descriptions.Item label="适用范围">
                          {formJson?.availArea}
                        </Descriptions.Item>
                        <Descriptions.Item label="变更等级">
                          {formJson?.riskLevel}
                        </Descriptions.Item>
                        <Descriptions.Item label="变更专业">{formJson?.reason}</Descriptions.Item>
                        <Descriptions.Item label="变更类别">
                          {formJson?.changeCategory}
                        </Descriptions.Item>
                        <Descriptions.Item label="附件">
                          {attachmentInfos &&
                          Array.isArray(attachmentInfos) &&
                          attachmentInfos?.length > 0 ? (
                            <SimpleFileList files={attachmentInfos}>
                              <Button type="link" compact>
                                查看
                              </Button>
                            </SimpleFileList>
                          ) : (
                            '--'
                          )}
                        </Descriptions.Item>
                      </>
                    )}
                    {bizType === approvalTypes.CHANGE_ONLINE_DELAY && (
                      <>
                        <Descriptions.Item label="变更延期至">
                          {moment(formJson?.newPlanEndDate).format('YYYY-MM-DD HH:mm')}
                        </Descriptions.Item>
                        <Descriptions.Item label="原计划结束时间">
                          {moment(formJson?.oldPlanEndDate).format('YYYY-MM-DD HH:mm')}
                        </Descriptions.Item>
                      </>
                    )}
                    {(bizType === approvalTypes.CHANGE_ONLINE_APPLY ||
                      bizType === approvalTypes.CHANGE_ONLINE_SUMMERY ||
                      bizType === approvalTypes.CHANGE_ONLINE_DELAY) && (
                      <>
                        {bizType !== approvalTypes.CHANGE_ONLINE_DELAY && (
                          <Descriptions.Item label="变更来源">
                            {formJson?.changeSourceInfoList ? (
                              <ChangeSourceListText sourceList={formJson.changeSourceInfoList} />
                            ) : (
                              '--'
                            )}
                          </Descriptions.Item>
                        )}
                        <Descriptions.Item label="变更等级">
                          {formJson?.riskLevel}
                        </Descriptions.Item>
                        <Descriptions.Item label="变更专业">{formJson?.reason}</Descriptions.Item>
                        <Descriptions.Item label="变更类别">
                          {formJson?.changeCategory}
                        </Descriptions.Item>
                        {bizType !== approvalTypes.CHANGE_ONLINE_DELAY && (
                          <Descriptions.Item
                            label="变更窗口期"
                            contentStyle={{ overflow: 'hidden' }}
                          >
                            <Typography.Paragraph
                              style={{ marginBottom: 0 }}
                              ellipsis={{ tooltip: true }}
                            >
                              {`${moment(formJson?.planStartTime).format('YYYY-MM-DD HH:mm')} ~ ${moment(formJson?.planEndTime).format('YYYY-MM-DD HH:mm')}`}
                            </Typography.Paragraph>
                          </Descriptions.Item>
                        )}
                        <Descriptions.Item label="变更类型">
                          {formJson?.changeType}
                        </Descriptions.Item>
                        <Descriptions.Item label="变更负责人">
                          {formJson?.changeResponsibleUserInfoList?.length
                            ? formJson?.changeResponsibleUserInfoList?.map((item, index) => {
                                return (
                                  <>
                                    <UserLink
                                      target="_blank"
                                      userId={item?.userId}
                                      style={{
                                        marginRight: '4px',
                                        marginLeft: index === 0 ? '0px' : '4px',
                                      }}
                                    />
                                    {index === formJson?.changeResponsibleUserInfoList.length - 1
                                      ? ''
                                      : '|'}
                                  </>
                                );
                              })
                            : '--'}
                        </Descriptions.Item>
                        <Descriptions.Item label="附件">
                          {attachmentInfos &&
                          Array.isArray(attachmentInfos) &&
                          attachmentInfos?.length > 0 ? (
                            <SimpleFileList files={attachmentInfos}>
                              <Button type="link" compact>
                                查看
                              </Button>
                            </SimpleFileList>
                          ) : (
                            '--'
                          )}
                        </Descriptions.Item>
                        <Descriptions.Item
                          label="影响说明"
                          contentStyle={{ overflow: 'hidden' }}
                          span={2}
                        >
                          <Typography.Paragraph ellipsis={{ tooltip: true }}>
                            {formJson?.changeInfluence}
                          </Typography.Paragraph>
                        </Descriptions.Item>
                      </>
                    )}
                  </>
                )}

                {[approvalTypes.CHANGE, approvalTypes.CHANGE_SUMMERY].includes(bizType) &&
                  formJson &&
                  (!isChangeIndependent ? (
                    <>
                      {isYGChange && (
                        <Descriptions.Item label="变更来源">
                          <ChangeSourceText
                            sourceNo={formJson.sourceNo}
                            sourceType={formJson.sourceType}
                          />
                        </Descriptions.Item>
                      )}
                      <Descriptions.Item label="变更方式">{formJson.exeWay}</Descriptions.Item>
                      <Descriptions.Item label="变更等级">{formJson.riskLevel}</Descriptions.Item>
                      <Descriptions.Item label="变更专业">{formJson.reason}</Descriptions.Item>
                      <Descriptions.Item label="变更类型">{formJson.changeType}</Descriptions.Item>
                      {isYGChange && (
                        <Descriptions.Item
                          contentStyle={{ overflow: 'hidden' }}
                          label="变更计划时间"
                          span={2}
                        >
                          {Array.isArray(formJson.changeTimeList) &&
                          formJson.changeTimeList.length > 0 ? (
                            <ChangeTimeText changeTimeList={formJson.changeTimeList} />
                          ) : (
                            '--'
                          )}
                        </Descriptions.Item>
                      )}
                      {!isYGChange && (
                        <Descriptions.Item
                          contentStyle={{ overflow: 'hidden' }}
                          label="变更计划开始时间"
                        >
                          {formJson.planStartTime
                            ? moment(formJson.planStartTime).format('YYYY-MM-DD HH:mm')
                            : '--'}
                        </Descriptions.Item>
                      )}
                      {!isYGChange && (
                        <Descriptions.Item
                          contentStyle={{ overflow: 'hidden' }}
                          label="变更计划结束时间"
                        >
                          {formJson.planEndTime
                            ? moment(formJson.planEndTime).format('YYYY-MM-DD HH:mm')
                            : '--'}
                        </Descriptions.Item>
                      )}
                      {isYGChange && (
                        <Descriptions.Item label="变更设备">
                          <ChangeRelateDevicesModalButton
                            changeDeviceList={formJson.changeDeviceList}
                          />
                        </Descriptions.Item>
                      )}
                      {isYGChange && (
                        <Descriptions.Item label="变更目的" contentStyle={{ overflow: 'hidden' }}>
                          <Typography.Text ellipsis={{ tooltip: true }}>
                            {formJson.purpose}
                          </Typography.Text>
                        </Descriptions.Item>
                      )}
                    </>
                  ) : (
                    <>
                      <Descriptions.Item label="变更等级">{formJson.riskLevel}</Descriptions.Item>
                      <Descriptions.Item label="标准变更模版" contentStyle={{ overflow: 'hidden' }}>
                        {formJson.templateName ?? '--'}
                      </Descriptions.Item>
                      <Descriptions.Item label="变更专业">{formJson.reason}</Descriptions.Item>
                      <Descriptions.Item label="变更类型">{formJson.changeType}</Descriptions.Item>
                      <Descriptions.Item
                        contentStyle={{ overflow: 'hidden' }}
                        label="变更计划开始时间"
                      >
                        {formJson.planStartTime
                          ? moment(formJson.planStartTime).format('YYYY-MM-DD HH:mm')
                          : '--'}
                      </Descriptions.Item>
                      <Descriptions.Item
                        contentStyle={{ overflow: 'hidden' }}
                        label="变更计划结束时间"
                      >
                        {formJson.planEndTime
                          ? moment(formJson.planEndTime).format('YYYY-MM-DD HH:mm')
                          : '--'}
                      </Descriptions.Item>
                      <Descriptions.Item label="变更负责人">
                        {formJson.respPersonName}
                      </Descriptions.Item>
                      <Descriptions.Item label="涉及客户" contentStyle={{ overflow: 'hidden' }}>
                        {formJson.relateCustomer}
                      </Descriptions.Item>
                      <Descriptions.Item
                        label="影响区域"
                        span={2}
                        contentStyle={{ overflow: 'hidden' }}
                      >
                        <Typography.Text ellipsis={{ tooltip: true }}>
                          {formJson.influenceArea}
                        </Typography.Text>
                      </Descriptions.Item>
                    </>
                  ))}
                {bizType === approvalTypes.ORDER_APPROVAL && formJson && (
                  <>
                    <Descriptions.Item label="合同编号">
                      <a href={`${sales.baseURL}page/billing-contract/${formJson.orderNo}`}>
                        {formJson.orderNo}
                      </a>
                    </Descriptions.Item>
                    <Descriptions.Item label="审批内容">{formJson.content}</Descriptions.Item>
                    <Descriptions.Item label="合同负责人/协助人">{`${formJson.respPerson}/${formJson.assistantsName}`}</Descriptions.Item>
                    <Descriptions.Item label="机房位置">{formJson.idcName}</Descriptions.Item>
                  </>
                )}
                {bizType === approvalTypes.REPORT_AUTH_APPLY && formJson && (
                  <>
                    <Descriptions.Item label="申请人">
                      <UserLink userId={formJson?.applyId} userName={formJson?.apply} />
                    </Descriptions.Item>
                    <Descriptions.Item label="申请报表">{formJson.viewName}</Descriptions.Item>
                    <Descriptions.Item label="授权项">
                      {formJson.permissionTypeName}
                    </Descriptions.Item>
                    <Descriptions.Item label="授权有效期">{formJson.expireTime}</Descriptions.Item>
                  </>
                )}
                {[
                  approvalTypes.STANDARD_CHANGE_TEMPLATE,
                  approvalTypes.STANDARD_CHANGE_TEMPLATE_POSTPONE,
                ].includes(bizType) &&
                  formJson && (
                    <>
                      <Descriptions.Item label="模板名称">
                        {formJson.templateName}
                      </Descriptions.Item>
                      <Descriptions.Item label="适用机房/楼">
                        {formJson.availArea}
                      </Descriptions.Item>
                      <Descriptions.Item label="变更等级">{formJson.riskLevel}</Descriptions.Item>
                      <Descriptions.Item label="变更专业">{formJson.reason}</Descriptions.Item>
                      <Descriptions.Item label="变更类型">{formJson.changeType}</Descriptions.Item>
                      <Descriptions.Item
                        label="一般变更实施记录"
                        contentStyle={{ overflow: 'hidden' }}
                      >
                        <Tooltip title={formJson.sourceChangeTitle}>
                          <Typography.Link
                            ellipsis
                            target="_blank"
                            href={generateChangeOfflineLocation({
                              id: formJson.sourceChangeId,
                            })}
                          >
                            {formJson.sourceChangeTitle}
                          </Typography.Link>
                        </Tooltip>
                      </Descriptions.Item>
                    </>
                  )}
                {[approvalTypes.OUT_DOOR, approvalTypes.IN_DOOR].includes(bizType) && (
                  <>
                    {Array.isArray(getAssetsOutTicketContent(baseInfo.content)) &&
                    getAssetsOutTicketContent(baseInfo.content).length >= 2 ? (
                      <>
                        <Descriptions.Item label="授权时间范围">
                          {getAssetsOutTicketContent(baseInfo.content)[0].value}
                        </Descriptions.Item>
                        <Descriptions.Item label="资产类型">
                          {getAssetsOutTicketContent(baseInfo.content)[1].value}
                        </Descriptions.Item>
                        {getAssetsOutTicketContent(baseInfo.content).length > 2 && (
                          <>
                            <Descriptions.Item label="客户确认人姓名">
                              {getAssetsOutTicketContent(baseInfo.content)[2].value}
                            </Descriptions.Item>
                            <Descriptions.Item label="客户所属单位">
                              {getAssetsOutTicketContent(baseInfo.content)[3].value}
                            </Descriptions.Item>
                          </>
                        )}
                      </>
                    ) : (
                      <Descriptions.Item label="审批内容" contentStyle={{ overflow: 'hidden' }}>
                        {renderDescriptionsItem(baseInfo.content, 'content')}
                      </Descriptions.Item>
                    )}
                  </>
                )}
                {(bizType === approvalTypes.CARD_APPLY ||
                  bizType === approvalTypes.CARD_EXCHANGE ||
                  bizType === approvalTypes.CARD_CHANGE) && (
                  <Descriptions.Item label="授权机房">{idcTag ?? '--'}</Descriptions.Item>
                )}
                {/* 维护关单 */}
                {bizType === approvalTypes.MAINTENANCE_END && (
                  <>
                    <Descriptions.Item label="位置">{idcTag ?? '--'}</Descriptions.Item>
                    <Descriptions.Item label="业务分类">
                      {formJson?.subTaskTypeName ?? '--'}
                    </Descriptions.Item>
                    <Descriptions.Item label="异常项">
                      {formJson?.exceptionNum ?? '--'}
                    </Descriptions.Item>
                    <Descriptions.Item label="待维护项">
                      {formJson?.waitInspectNum ?? '--'}
                    </Descriptions.Item>
                    <Descriptions.Item label="已维护项">
                      {formJson?.alreadyInspectNum ?? '--'}
                    </Descriptions.Item>
                  </>
                )}
                {bizType === approvalTypes.AUTH_APPLY && (
                  <>
                    <Descriptions.Item label="申请人">
                      <UserLink userId={Number(formJson[0].applyId)} />
                    </Descriptions.Item>
                    <Descriptions.Item label="申请角色">
                      {formJson[0].roleName || BLANK_PLACEHOLDER}
                    </Descriptions.Item>
                    <Descriptions.Item label="授权过期时间">
                      {formJson[0].expireTime
                        ? `${formJson[0].expireTime} 00:00:00`
                        : BLANK_PLACEHOLDER}
                    </Descriptions.Item>
                    <Descriptions.Item />
                    <Descriptions.Item
                      label={formJson[0].resourceType === 'TEAM' ? '申请加入团队' : '申请资源'}
                      span={4}
                      contentStyle={{ flexWrap: 'wrap' }}
                    >
                      {formJson[0].resourceNames.length > 0
                        ? formJson[0].resourceNames?.map((name, index) => {
                            const content =
                              formJson[0].resourceType === 'BLOCK' ? (
                                <SpaceText
                                  key={formJson[0].resourceCodes[index]}
                                  guid={formJson[0].resourceCodes[index]}
                                />
                              ) : (
                                name
                              );
                            if (index === 0) {
                              return content;
                            }
                            return (
                              <React.Fragment key={content}>
                                <Divider style={{ top: '.125em' }} type="vertical" />
                                {content}
                              </React.Fragment>
                            );
                          })
                        : BLANK_PLACEHOLDER}
                    </Descriptions.Item>
                  </>
                )}
                {bizType === approvalTypes.SALES_QUOTATION && formJson && (
                  <Descriptions.Item label="申请类型">
                    {
                      locales.quotationType.enum[
                        formJson.quotationType ?? QuotationType.CABINET_QUOTATION
                      ]
                    }
                  </Descriptions.Item>
                )}
                {/* 解析content里面的数据 */}
                {[approvalTypes.CUS_EFFECTED_APPLY, approvalTypes.CUS_FORBIDDEN_APPLY].includes(
                  bizType
                ) && (
                  <>
                    {getCardChangeFormattedContent(baseInfo.content).contentMapList?.map(
                      content => (
                        <Descriptions.Item key={content.label} label={content.label}>
                          {['授权机房楼栋', '所属机房楼栋'].includes(content.label) ? (
                            <Space
                              split={<Divider type="vertical" spaceSize="mini" />}
                              size="small"
                              wrap
                            >
                              {content.value.split('|')?.map(blockGuid => (
                                <SpaceText key={blockGuid} guid={blockGuid} />
                              ))}
                            </Space>
                          ) : (
                            content.value
                          )}
                        </Descriptions.Item>
                      )
                    )}
                  </>
                )}
                {[approvalTypes.BORROW, approvalTypes.RENEW, approvalTypes.TRANSFER].includes(
                  bizType
                ) &&
                  baseInfo.content &&
                  getCardChangeFormattedContent(baseInfo.content).contentMapList?.map(item => (
                    <Descriptions.Item key={item.label} label={item.label}>
                      {getUserName(item.value).userId ? (
                        <UserLink
                          userId={getUserName(item.value).userId}
                          userName={getUserName(item.value).userName}
                        />
                      ) : (
                        item.value
                      )}
                    </Descriptions.Item>
                  ))}
                {/* 把content里面的数据展示到页面上，不要使用formjson */}
                {[
                  approvalTypes.ON_OFF_PROCESS,
                  approvalTypes.LEAVE_PROCESS,
                  approvalTypes.CERT_APPROVAL,
                ].includes(bizType) &&
                  getCardChangeFormattedContent(baseInfo.content).contentMapList?.map(item => (
                    <Descriptions.Item
                      key={item.label}
                      label={item.label}
                      contentStyle={{ overflow: 'hidden' }}
                    >
                      <Typography.Text ellipsis={{ tooltip: true }}>
                        {item.value ?? '--'}
                      </Typography.Text>
                    </Descriptions.Item>
                  ))}
                {[approvalTypes.VISITOR].includes(bizType) ? (
                  <>
                    {approvalListType &&
                    (formJson?.taskSubType?.includes('政府') ||
                      formJson?.taskSubType?.includes('商务')) ? (
                      <>
                        <Descriptions.Item label="业务分类">
                          {formJson?.taskSubType || '--'}
                        </Descriptions.Item>
                        <Descriptions.Item label="进入园区">
                          {!formJson?.idcName && !formJson?.idcTag
                            ? '--'
                            : `${formJson?.idcTag} ${formJson?.idcName} `}
                        </Descriptions.Item>
                        <Descriptions.Item label="申请时间">
                          {`${dayjs(formJson?.approveStartTime).format('YYYY-MM-DD HH:mm')} ~ ${dayjs(formJson?.approveEndTime).format('YYYY-MM-DD HH:mm')} `}
                        </Descriptions.Item>
                        <Descriptions.Item label="单位名称" contentStyle={{ overflow: 'hidden' }}>
                          <Typography.Text ellipsis={{ tooltip: true }}>
                            {formJson?.guestCompany || '--'}
                          </Typography.Text>
                        </Descriptions.Item>
                        <Descriptions.Item label="申请人数">
                          {formJson?.approvalNum || '--'}
                        </Descriptions.Item>
                        <Descriptions.Item label="接待人">
                          {formJson?.receptionId ? (
                            <Space wrap>
                              <UserLink
                                userId={formJson?.receptionId}
                                userName={formJson?.receptionName}
                              />
                              {`（${formJson?.receptionPhone}）`}
                            </Space>
                          ) : (
                            '--'
                          )}
                        </Descriptions.Item>
                        <Descriptions.Item label="来源">
                          <Tag color="default">
                            {baseInfo?.instSource === 'DC_BASE' ? '中联环境' : '字节'}
                          </Tag>
                        </Descriptions.Item>
                        {wfProperties?.bizTag === 'BUSINESS' ||
                        wfProperties?.bizTag === 'GOVERNMENT' ? (
                          <Descriptions.Item label="报备凭证" contentStyle={{ overflow: 'hidden' }}>
                            <Space direction="vertical" style={{ overflow: 'hidden' }}>
                              <Typography.Text
                                ellipsis={{ tooltip: true }}
                                style={{ width: '100%' }}
                              >
                                {renderDescriptionsItem(baseInfo.reason, 'reason', bizType)}
                              </Typography.Text>
                              {attachmentInfos &&
                              Array.isArray(attachmentInfos) &&
                              attachmentInfos?.length > 0 ? (
                                <SimpleFileList files={attachmentInfos}>
                                  <Button type="link" compact>
                                    查看
                                  </Button>
                                </SimpleFileList>
                              ) : (
                                ''
                              )}
                            </Space>
                          </Descriptions.Item>
                        ) : null}
                      </>
                    ) : (
                      getCardChangeFormattedContent(baseInfo.content).contentMapList?.map(item => (
                        <Descriptions.Item key={item.label} label={item.label}>
                          {item.value ?? '--'}
                        </Descriptions.Item>
                      ))
                    )}
                  </>
                ) : null}
              </Descriptions>
            </Space>
            {!checkedRoom && (
              <div
                style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fill,minmax(244px,1fr))',
                  gridRowGap: '58px',
                  gridColumnGap: '16px',
                }}
              >
                {gridCount?.map(item => (
                  <DisplayCard
                    key={item.roomTag}
                    bodyStyle={{ paddingLeft: 22 }}
                    title={`${item.roomTag} ${item.roomTypeName}`}
                    showDescribe
                    countList={[
                      {
                        describe: '未完成',
                        number: item.UNDONE,
                        color: 'warning',
                      },
                      {
                        describe: '已完成',
                        number: item.COMPLETED,
                        color: 'success',
                      },
                      {
                        describe: '失败',
                        number: item.FAILURE,
                        color: 'error',
                      },
                    ]}
                    backgroundColor={getDisplayCardColor({
                      exceptionNum: item.FAILURE,
                      waitNum: item.UNDONE,
                      alreadyNum: item.COMPLETED,
                      code: item?.roomTag,
                      taskNo: bizId,
                      recentlyCode: POWER_UP_DOWN_DETAIL_RECENTLY_CLICK_ROOM,
                    })}
                    describe={
                      <Space direction="vertical" size={4}>
                        <div>{formatTime(item.executeTime)}分钟</div>
                        <Typography.Text style={{ fontSize: '12px' }} type="secondary">
                          {item.taskSubType === 'POWER_OFF' ? '下' : '上'}电时长
                        </Typography.Text>
                      </Space>
                    }
                    subDescribe={
                      item.modCount > 0 ? (
                        <Typography.Text>
                          修订实际{item.taskSubType === 'POWER_OFF' ? '下' : '上'}电时间：
                          {item.modCount}
                        </Typography.Text>
                      ) : null
                    }
                    onClick={() => {
                      setCheckedRoom(item);
                      setTaskSubType(item.taskSubType);
                    }}
                  />
                ))}
              </div>
            )}
            {checkedRoom && (
              <>
                <PageHeader
                  style={{ padding: 0 }}
                  backIcon={<LeftOutlined style={{ color: 'var(--text-color-secondary)' }} />}
                  title={`${checkedRoom.roomTag} ${checkedRoom.roomTypeName}`}
                  onBack={() => {
                    setCheckedRoom(undefined);
                  }}
                />
                <Table
                  // loading={loading}
                  scroll={{ x: 'max-content' }}
                  pagination={{
                    total: pagedPowerGridData.total,
                    current: pagedPowerGridsPagination.pageNum,
                    pageSize: pagedPowerGridsPagination.pageSize,
                    onChange: (pageNum, pageSize) => {
                      setPagedPowerGridsPagination({ pageNum, pageSize });
                    },
                  }}
                  columns={getColumns(getPowerSubType(taskSubType), taskSubType)}
                  dataSource={pagedPowerGridData.data}
                />
              </>
            )}
            <div style={{ paddingBottom: '16px' }}>
              <RegionalCardList
                mode="vetting"
                taskNo={bizId}
                idcTag={idcTag}
                bizTag={wfProperties?.bizTag}
                visitType={wfProperties?.visitorSubType}
              />
            </div>
            {[approvalTypes.VISITOR].includes(bizType) && approvalListType && (
              <VisitorTable baseInfo={baseInfo} />
            )}
            <FormJsonRender
              bizType={bizType}
              idcTag={idcTag}
              formJson={baseInfo ? formJson : null}
              bizId={bizId}
            />
          </Card>
          {bizType === approvalTypes.EVENT_AUDIT_PROCESS && showRiskRegister ? (
            <Card title="根因定位">
              <Descriptions column={4}>
                <Descriptions.Item label="定位时间">
                  {dayjs(formJson?.detectTime).format('YYYY-MM-DD HH:mm:ss') || '--'}
                </Descriptions.Item>
                <Descriptions.Item label="定位人">
                  {formJson?.detectUserName || '--'}
                </Descriptions.Item>
                <Descriptions.Item label="根因定位" span={2} contentStyle={{ overflow: 'hidden' }}>
                  <Typography.Text ellipsis={{ tooltip: true }}>
                    {formJson?.detectReason || '--'}
                  </Typography.Text>
                </Descriptions.Item>
              </Descriptions>
            </Card>
          ) : null}
          {[approvalTypes.RENEW, approvalTypes.TRANSFER].includes(bizType) && formJson && (
            <Space style={{ display: 'flex' }} direction="vertical" size={0}>
              {shouldShowOnLineDevices(formJson).length > 0 && (
                <Card title="线上物资" contentStyle={{}}>
                  <Space style={{ display: 'flex' }} direction="vertical" size="large">
                    {shouldShowOnLineNumberedDevices(formJson) && (
                      <Space style={{ display: 'flex' }} direction="vertical" size="middle">
                        <Typography.Text>设备</Typography.Text>
                        <BorrowAssetTable
                          rowKey="id"
                          pagination={false}
                          dataSource={formJson.filter(
                            item => item.numbered && item.assetType === 'ON_LINE'
                          )}
                        />
                      </Space>
                    )}
                    {shouldShowOnLineUnNumberedDevices(formJson) && (
                      <Space style={{ display: 'flex' }} direction="vertical" size="middle">
                        <Typography.Text>耗材</Typography.Text>
                        <BorrowAssetTable
                          rowKey="id"
                          dataIndexs={[
                            'deviceType',
                            'vendor',
                            'productModel',
                            'borrowNum',
                            'toBeReturned',
                            'ownerId',
                          ]}
                          pagination={false}
                          dataSource={formJson.filter(
                            item => !item.numbered && item.assetType === 'ON_LINE'
                          )}
                        />
                      </Space>
                    )}
                  </Space>
                </Card>
              )}
              {shouldShowOffLineDevices(formJson) && (
                <Card title="线下物资">
                  <BorrowAssetTable
                    rowKey="id"
                    dataIndexs={[
                      'deviceType',
                      'assetNo',
                      'vendor',
                      'productModel',
                      'borrowNum',
                      'toBeReturned',
                      'ownerId',
                    ]}
                    assetNoTitle="SN"
                    deviceTypeTitle="物资分类"
                    pagination={false}
                    dataSource={formJson.filter(item => item.assetType === 'OFF_LINE')}
                  />{' '}
                </Card>
              )}
            </Space>
          )}
          {bizType === approvalTypes.OPERATION_DATA_CHANGE && (
            <Card title="变更信息" bodyStyle={{ overflowY: 'hidden' }}>
              <CompassOperateDetailTable instId={id} dt={bizId} title="更正" />
            </Card>
          )}
          {bizType === approvalTypes.BILL_ADJUST && formJson && (
            <AdjustAccountDetail type={formJson.feeCode} info={formJson} />
          )}
          {bizType === approvalTypes.BILL_FEE_ADD && formJson && (
            <BillAddFeeDetail info={formJson} />
          )}
          {approvalTypes.CHANGE_SUMMERY &&
            formJson &&
            [approvalTypes.CHANGE_SUMMERY, approvalTypes.CHANGE].includes(bizType) &&
            !isChangeIndependent &&
            isYGChange && <ChangeCriticalStepCard formJson={formJson} />}
          {approvalTypes.CHANGE_SUMMERY &&
            bizType === approvalTypes.CHANGE_SUMMERY &&
            formJson &&
            (!isChangeIndependent ? (
              <ChangeSummaryCard
                formJson={formJson}
                changeOrderId={bizId}
                isYGChange={isYGChange}
              />
            ) : (
              <ChangeSummaryIndependentCard formJson={formJson} />
            ))}
          {isTypeAbleToShowFiles.includes(bizType) &&
            attachmentInfos.length > 0 &&
            !showVisitorFile && (
              <Card title={getFileCardTitle(bizType)}>
                <FileList
                  files={attachmentInfos}
                  groups={[
                    {
                      title: '文件',
                      fileTypes: ['others', 'pdf'],
                    },
                    {
                      title: '图片',
                      fileTypes: ['image', 'video'],
                      previewable: true,
                      showName: true,
                    },
                  ]}
                />
              </Card>
            )}
          {bizType === approvalTypes.LEAVE_PROCESS && bizId && (
            <ParentalLeaveRecordCard bizId={bizId} />
          )}
          {[approvalTypes.CHANGE_ONLINE_SUMMERY].includes(bizType) && (
            <Card title="总结内容" bodyStyle={{ maxHeight: 355 }}>
              <Space style={{ display: 'flex', width: '100%' }} direction="vertical" size={0}>
                <Descriptions column={4}>
                  <Descriptions.Item label="执行变更开始时间">
                    {moment(formJson?.realStartTime).format('YYYY-MM-DD HH:mm:ss')}
                  </Descriptions.Item>
                  <Descriptions.Item label="执行变更结束时间">
                    {moment(formJson?.realEndTime).format('YYYY-MM-DD HH:mm:ss')}
                  </Descriptions.Item>
                  <Descriptions.Item label="变更结果"> {formJson?.exeResult}</Descriptions.Item>
                  <Descriptions.Item label="附件">
                    {Array.isArray(formJson?.fileInfoList) && formJson?.fileInfoList.length > 0 ? (
                      <SimpleFileList
                        files={formJson?.fileInfoList?.map(file =>
                          McUploadFile.fromApiObject(file)
                        )}
                      >
                        <Button type="link" compact>
                          查看
                        </Button>
                      </SimpleFileList>
                    ) : (
                      '--'
                    )}
                  </Descriptions.Item>
                  {formJson?.exeResult === '执行失败' && (
                    <Descriptions.Item label="失败原因" contentStyle={{ overflow: 'hidden' }}>
                      <Typography.Paragraph ellipsis={{ tooltip: true }}>
                        {formJson?.failReason}
                      </Typography.Paragraph>
                    </Descriptions.Item>
                  )}
                  <Descriptions.Item
                    label="总结内容"
                    contentStyle={{ overflow: 'hidden' }}
                    span={formJson?.exeResult === '执行失败' ? 3 : 4}
                  >
                    <Typography.Paragraph ellipsis={{ tooltip: true }}>
                      {formJson?.summery}
                    </Typography.Paragraph>
                  </Descriptions.Item>
                </Descriptions>
              </Space>
            </Card>
          )}
          {status === ApprovalStatus.Approving &&
            refusedRecords.length > 0 &&
            ![approvalTypes.OPERATION_DATA_CHANGE].includes(bizType) && (
              <Card title="历史驳回记录" bodyStyle={{ maxHeight: 355, overflowY: 'hidden' }}>
                <RefusedApprovalTable dataSource={refusedRecords} parentId={id} />
              </Card>
            )}
          <StepCard baseInfo={baseInfo} onSuccess={getDetail} />
        </Space>
        {shouldFooterToolBarAppear && (
          <FooterToolBar>
            <Space>
              {getBasicOperationButtons(approveUserList)}
              {getRevokeButton()}
              {getPrintButton()}
              {getCarbonCopyButton()}
              {getCommentButton()}
              {getPdfDownloadButton()}
            </Space>
          </FooterToolBar>
        )}
      </>
    );
  } else if (errorCode === 'NO_PERMISSION') {
    return <ErrorPage403 subTitleContent="您不在此审批流程中，无法查看详情" hasExtra={false} />;
  } else if (errorCode === 'ALREADY_REVOKE') {
    return <ErrorPage403 subTitleContent="当前审批已撤回，无法查看详情" hasExtra={false} />;
  } else {
    return <></>;
  }
}

const mapStateToProps = ({ user: { userId } }) => ({
  curUserId: userId,
});

export default connect(mapStateToProps)(ApproveCenterDetail);

async function revokeApprove(params, getDetail) {
  const { error } = await withdrawTodo(params);

  if (error) {
    message.error(error);
    return;
  }

  message.success('撤回成功！');
  getDetail();
  return;
}

function getCardChangeFormattedContent(content) {
  const [contentText, systemUserId] = content.split('=');
  const contentMapList = BpmInstance.getFormattedContent(contentText);
  if (contentMapList.some(item => item.value === '删除机房权限')) {
    contentMapList.pop();
  }
  if (contentMapList.length === 0) {
    return;
  }

  return { contentMapList, systemUserId };
}
function getAssetsOutTicketContent(content) {
  return BpmInstance.getFormattedContent(content);
}

function getApproveUserIdList(operationRecords) {
  let approveUserList = [];
  const operationTasks = getCurrentExecuteOperationTasks(operationRecords);
  if (operationTasks && operationTasks.length) {
    operationTasks.forEach(task => {
      if (Array.isArray(task.userList) && task.userList.length > 0) {
        approveUserList = approveUserList.concat(getUserIdList(task.userList));
      }
    });
    return approveUserList.length > 0
      ? approveUserList?.map(userId => parseInt(userId))
      : approveUserList;
  }
  return approveUserList;
}
function getUserIdList(userList) {
  return userList?.map(user => user.id);
}
function getCurrentExecuteOperationTasks(operationRecords) {
  const currentExecuteOperationRecords = operationRecords?.filter(
    record => record.operationType === NodeStatus.Execute
  );
  if (currentExecuteOperationRecords && currentExecuteOperationRecords.length) {
    return currentExecuteOperationRecords[0].operationTasks;
  }
  return [];
}

const getActualFileType = type => {
  switch (type) {
    case 'EVENT':
      return 'EVENT_AUDIT';
    case 'N_EVENT_LEVEL_CHG':
      return 'EVENT_ADD';
    case 'CHANGE':
    case 'CHANGE_SUMMERY':
      return 'CHANGE_ORDER';
    default:
      return undefined;
  }
};

function getUserName(str) {
  if (str.includes('|')) {
    const [userId, userName] = str.split('|');
    return { userId, userName };
  } else {
    return { userId: undefined, userName: str };
  }
}

function shouldShowOnLineDevices(formJson) {
  return formJson.filter(item => item.assetType === 'ON_LINE');
}

function shouldShowOnLineNumberedDevices(formJson) {
  return shouldShowOnLineDevices(formJson).filter(item => item.numbered).length > 0;
}

function shouldShowOnLineUnNumberedDevices(formJson) {
  return shouldShowOnLineDevices(formJson).filter(item => !item.numbered).length > 0;
}

function shouldShowOffLineDevices(formJson) {
  return formJson.filter(item => item.assetType === 'OFF_LINE').length > 0;
}
function getFormattedContent(content) {
  const arr = BpmInstance.getFormattedContent(content);
  if (arr.length === 0) {
    return;
  }
  return arr?.map(item => item.label + '：' + item.value).join('，');
}
function formatTime(inspectTime) {
  const timeInSeconds = inspectTime / 1000;

  if (timeInSeconds >= 0 && timeInSeconds < 100) {
    return `${timeInSeconds.toFixed(0)}秒`;
  } else if (timeInSeconds >= 100 && timeInSeconds < 100 * 60) {
    const timeInMinutes = timeInSeconds / 60;
    return `${timeInMinutes.toFixed(2)}分钟`;
  } else if (timeInSeconds >= 100 * 60 && timeInSeconds < 100 * 60 * 60) {
    const timeInHours = timeInSeconds / 3600;
    return `${timeInHours.toFixed(2)}小时`;
  } else if (timeInSeconds >= 100 * 60 * 60) {
    const timeInDays = timeInSeconds / (3600 * 24);
    return `${timeInDays.toFixed(2)}天`;
  }

  // Fallback: 无效时间
  return '';
}
