import React, { useEffect, useMemo } from 'react';
import { connect } from 'react-redux';
import styled from 'styled-components';

import { Empty } from '@manyun/base-ui.ui.empty';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import {
  addRealtimeNAlarmsDataSubscriptionActionCreator,
  removeRealtimeNAlarmsDataSubscriptionActionCreator,
} from '@manyun/monitoring.state.subscriptions';

import { GutterWrapper, TinyCard } from '@manyun/dc-brain.legacy.components';
import { STATUS_MAP } from '@manyun/dc-brain.legacy.constants/statusColor';
import { SUBSCRIPTIONS_MODE } from '@manyun/dc-brain.legacy.constants/subscriptions';
import { batteryActions } from '@manyun/dc-brain.legacy.redux/actions/batteryActions';
import { generateGetDeviceAlarmStatus } from '@manyun/dc-brain.legacy.utils/device';

import TreeCard from './components/tree-card';
import ViewCard from './components/view-card';

const ScrollWrapper = styled.div`
  height: ${({ height }) => height};
  overflow-y: auto;
  min-width: 320px;
`;
const cardHeight = `calc(var(--content-height) - 58.14px - 1px - 10px)`;

function BatteryPack({
  match,
  selectedTreeKey,
  subscribe,
  unsubscribe,
  spaceDevcie,
  spaceBlockGuids: blockGuids,
  alarmSpacesAndGuids,
}) {
  const blockGuidsStr = blockGuids?.join(',');
  const batteryUnitsStrs = spaceDevcie
    ?.map(({ spaceGuid, guid }) => `${spaceGuid.blockGuid}_$$_${guid}`)
    .join(',');

  const moduleId = 'battery-packs_alarms';
  const targets = useMemo(() => {
    if (!blockGuidsStr || !batteryUnitsStrs) {
      return null;
    }

    return blockGuidsStr.split(',').map(blockGuid => {
      const batteryUnitsGuids = batteryUnitsStrs
        .split(',')
        .map(batteryUnitsStr => batteryUnitsStr.split('_$$_'))
        .filter(item => item[0] === blockGuid)
        .map(item => item[1]);

      return {
        mode: SUBSCRIPTIONS_MODE.ALARMS_ONLY,
        blockGuid,
        moduleId,
        deviceGuids: batteryUnitsGuids,
      };
    });
  }, [blockGuidsStr, batteryUnitsStrs]);

  // 订阅所有设备的告警数据
  useEffect(() => {
    if (targets) {
      subscribe({ targets });
    }

    return () => {
      if (targets) {
        unsubscribe({ moduleId, mode: SUBSCRIPTIONS_MODE.ALARMS_ONLY });
      }
    };
  }, [targets, subscribe, unsubscribe, moduleId]);

  return (
    <GutterWrapper flex>
      <ScrollWrapper>
        <TreeCard
          cardHeight={cardHeight}
          idc={match.params.idc}
          alarmSpacesAndGuids={alarmSpacesAndGuids}
        />
      </ScrollWrapper>
      {selectedTreeKey ? (
        <ViewCard
          totalHeight="calc(var(--content-height) - 10px)"
          alarmSpacesAndGuids={alarmSpacesAndGuids}
        />
      ) : (
        <GutterWrapper flexN={1} mode="vertical">
          <TinyCard bodyStyle={{ height: `calc(${cardHeight} + 56px)` }}>
            <GutterWrapper style={{ height: '100%' }} flex center>
              <Empty image={<img alt="暂无数据" src="/images/empty.png" />} />
            </GutterWrapper>
          </TinyCard>
        </GutterWrapper>
      )}
    </GutterWrapper>
  );
}

const mapStateToProps = ({
  battery: { selectedTreeKey, roomTagDevice, blockRoomTreeData },
  config: { configMap },
  'monitoring.subscriptions': { devicesAlarmsData },
}) => {
  const spaceBlockGuids = blockRoomTreeData
    ? blockRoomTreeData.map(({ metaCode }) => metaCode)
    : [];
  const getDeviceAlarmStatus = generateGetDeviceAlarmStatus(devicesAlarmsData);
  const alarmBlockGuids = [];
  const alarmRoomGuids = [];
  const alarmParentGuids = [];
  const alarmGuids = [];
  if (roomTagDevice && Array.isArray(roomTagDevice)) {
    roomTagDevice.forEach(({ guid, parentGuid, spaceGuid: { blockGuid, roomGuid } }) => {
      const hasAlarms = getDeviceAlarmStatus(guid) !== STATUS_MAP.DEFAULT;
      if (hasAlarms) {
        if (!alarmBlockGuids.includes(blockGuid)) {
          alarmBlockGuids.push(blockGuid);
        }
        if (!alarmRoomGuids.includes(roomGuid)) {
          alarmRoomGuids.push(roomGuid);
        }
        if (!alarmParentGuids.includes(parentGuid)) {
          alarmParentGuids.push(parentGuid);
        }
        if (!alarmGuids.includes(guid)) {
          alarmGuids.push(guid);
        }
      }
    });
  }

  const configUtil = new ConfigUtil(configMap.current);

  return {
    battery: configUtil.getAllDeviceTypes(ConfigUtil.constants.deviceTypes.BATTERY_UNIT),
    selectedTreeKey,
    spaceDevcie: roomTagDevice,
    spaceBlockGuids,
    alarmSpacesAndGuids: {
      alarmBlockGuids,
      alarmRoomGuids,
      alarmParentGuids,
      alarmGuids,
    },
  };
};
const mapDispatchToProps = {
  setBatteryAlarmList: batteryActions.setBatteryAlarmList,
  subscribe: addRealtimeNAlarmsDataSubscriptionActionCreator,
  unsubscribe: removeRealtimeNAlarmsDataSubscriptionActionCreator,
};
export default connect(mapStateToProps, mapDispatchToProps)(BatteryPack);
