import styled from 'styled-components';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';

export const StyledTitleIcon = styled.span`
  display: inline-block;
  width: 4px;
  height: 1em;
  border-radius: 4px;
  background-color: var(--color-reference);
  margin-right: 0.5rem;
  vertical-align: super;
`;

export const StyledTitleWapper = styled.div`
  width: 100%;
  height: 32px;
  line-height: 32px;
  font-size: 16px;
  font-weight: 500;
  cursor: ${({ clickable }) => (clickable ? 'pointer' : 'text')};
`;

export const StyledTitleTxT = styled.span`
  display: inline-block;
  min-width: 100px;
  max-width: 90%;
  height: 32px;
  line-height: 32px;
  word-break: break-all;
  overflow: hidden;
  color: ${({ alarm }) => (alarm ? `var(--${prefixCls}-error-color)` : 'var(--text-color)')};
`;
