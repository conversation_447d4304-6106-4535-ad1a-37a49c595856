import sortBy from 'lodash/sortBy';
import uniqBy from 'lodash/uniqBy';

import { Row } from '@manyun/base-ui.ui.grid';
import { Typography } from '@manyun/base-ui.ui.typography';

export function getSearchOptions({ roomTagDevice, treeDataSource, value }) {
  const { treeList } = treeDataSource;
  const options = [];

  //设备
  const deviceList = roomTagDevice.filter(item => item.name.includes(value));
  const deviceListOptions = deviceList.map(device => {
    const { spaceGuid, guid, parentGuid, name } = device;
    const { roomGuid } = spaceGuid;

    return {
      ...device,
      label: (
        <Row justify="space-between">
          <Typography.Text>{name}</Typography.Text>
          <Typography.Text type="secondary">{roomGuid?.replace(/\./g, '/') || ''}</Typography.Text>
        </Row>
      ),
      value: `${roomGuid}.${parentGuid}.${guid}`,
      isLeaf: true,
      metaCode: `${roomGuid}.${parentGuid}.${guid}`,
      metaType: 'DEVICE',
    };
  });

  const blockList = [];
  const roomList = [];
  if (treeList.length) {
    treeList.forEach(block => {
      if (block.metaName.includes(value)) {
        blockList.push({
          ...block,
          label: block.metaCode.replace(/\./g, '/'),
          value: block.metaCode,
        });
      }
      if (block.children.length) {
        block.children.forEach(room => {
          if (room.metaName.includes(value)) {
            roomList.push({
              ...room,
              label: room.metaCode.replace(/\./g, '/'),
              value: room.metaCode,
            });
          }
        });
      }
    });
  }
  if (blockList.length) {
    options.push({
      label: '楼栋',
      options: blockList,
    });
  }

  if (roomList.length) {
    options.push({
      label: '包间',
      options: roomList,
    });
  }

  if (deviceListOptions.length) {
    options.push({
      label: '设备',
      options: deviceListOptions,
    });
  }
  return options;
}

export async function childNodesDataService(
  { metaType, metaCode },
  roomTagDevice,
  treeMode,
  alarmSpacesAndGuids
) {
  let children = [];
  let device = [];
  if (!roomTagDevice) {
    return Promise.resolve([]);
  }
  if (metaType === 'ROOM') {
    device = roomTagDevice.filter(({ spaceGuid }) => spaceGuid.roomGuid === metaCode);
  }
  if (metaType === 'TAG') {
    device = roomTagDevice.filter(
      ({ spaceGuid, parentGuid }) => `${spaceGuid.roomGuid}.${parentGuid}` === metaCode
    );
  }
  if (
    (treeMode === 'device' && metaType === 'ROOM') ||
    (treeMode === 'tag' && metaType === 'TAG')
  ) {
    children = device.map(item => {
      if (alarmSpacesAndGuids.alarmGuids.includes(item.guid)) {
        return {
          ...item,
          metaName: item.name,
          metaCode: `${metaCode}.${item.parentGuid}.${item.guid}`,
          metaType: 'DEVICE',
          isLeaf: true,
          alarm: true,
        };
      }
      return {
        ...item,
        metaName: item.name,
        metaCode: `${metaCode}.${item.parentGuid}.${item.guid}`,
        metaType: 'DEVICE',
        isLeaf: true,
      };
    });
  }
  if (treeMode === 'tag' && metaType === 'ROOM') {
    const tags = device.map(item => {
      if (alarmSpacesAndGuids.alarmParentGuids.includes(item.parentGuid)) {
        return {
          ...item,
          metaName: item.parentTag,
          metaCode: `${metaCode}.${item.parentGuid}`,
          metaType: 'TAG',
          isLeaf: false,
          alarm: true,
        };
      }

      return {
        ...item,
        metaName: item.parentTag,
        metaCode: `${metaCode}.${item.parentGuid}`,
        metaType: 'TAG',
        isLeaf: false,
      };
    });
    const rows = uniqBy(tags, 'metaCode');
    children = rows;
  }
  children = sortBy(children, 'alarm');
  return Promise.resolve(children);
}

export function getParallelList(deviceTreeNodes) {
  const deviceWithTag = splitDeviceWithTag(deviceTreeNodes);
  return Object.keys(deviceWithTag).map(key => {
    const device = deviceWithTag[key][0];
    return {
      metaCode: `${device.spaceGuid.roomGuid}.${device.parentGuid}`,
      metaName: device.tag,
      metaType: 'TAG',
      parentCode: device.spaceGuid.roomGuid,
    };
  });
}

export function splitDeviceWithTag(data) {
  let newData = {};
  data.forEach(device => {
    const {
      spaceGuid: { roomGuid },
      parentGuid,
    } = device;
    if (!newData[`${roomGuid}.${parentGuid}`]) {
      newData = {
        ...newData,
        [`${roomGuid}.${parentGuid}`]: [device],
      };
    } else {
      newData = {
        ...newData,
        [`${roomGuid}.${parentGuid}`]: [...newData[`${roomGuid}.${parentGuid}`], device],
      };
    }
  });
  return newData;
}

export function getIsAlarm(blockRoomTreeData, alarmSpacesAndGuids) {
  return sortBy(
    blockRoomTreeData.map(item => {
      if (item.metaType === 'BLOCK') {
        if (alarmSpacesAndGuids.alarmBlockGuids.includes(item.metaCode)) {
          return {
            ...item,
            alarm: true,
            children: sortBy(getIsAlarm(item.children, alarmSpacesAndGuids), 'alarm'),
          };
        }
        return {
          ...item,
          children: sortBy(getIsAlarm(item.children, alarmSpacesAndGuids), 'alarm'),
        };
      }
      if (item.metaType === 'ROOM') {
        if (alarmSpacesAndGuids.alarmRoomGuids.includes(item.metaCode)) {
          return {
            ...item,
            alarm: true,
          };
        }
        return item;
      }
      return item;
    }),
    'alarm'
  );
}
