import React, { useState } from 'react';

import { Radio } from '@manyun/base-ui.ui.radio';
import { Space } from '@manyun/base-ui.ui.space';

import { TinyCard } from '@manyun/dc-brain.legacy.components';

import BatteryTree from './battery-tree';

function TreeCard({ cardHeight, idc, alarmSpacesAndGuids }) {
  const [treeMode, setMode] = useState('device');

  return (
    <TinyCard
      title="电池组查询"
      bodyStyle={{
        height: cardHeight,
        overflow: 'auto',
      }}
    >
      <Space direction="vertical" size="middle">
        <Radio.Group value={treeMode} onChange={({ target: { value } }) => setMode(value)}>
          <Radio.Button value="device">电池间视角</Radio.Button>
          <Radio.Button value="tag">不间断电源视角</Radio.Button>
        </Radio.Group>
        <BatteryTree idc={idc} treeMode={treeMode} alarmSpacesAndGuids={alarmSpacesAndGuids} />
      </Space>
    </TinyCard>
  );
}

export default TreeCard;
