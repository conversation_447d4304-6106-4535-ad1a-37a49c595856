import React, { useEffect, useRef, useState } from 'react';
import { connect, useSelector } from 'react-redux';

import AutoComplete from 'antd/es/auto-complete';
import cloneDeep from 'lodash.clonedeep';
import debounce from 'lodash.debounce';

import { Input } from '@manyun/base-ui.ui.input';

import { selectSpaceCodes, selectSpaceEntities } from '@manyun/resource-hub.state.space';

import { ApiTree } from '@manyun/dc-brain.legacy.components';
import {
  batteryActions,
  getIdcBatteryActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/batteryActions';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';

import styles from './tree-card.module.less';
import TreeTitle from './tree-title';
import { childNodesDataService, getIsAlarm, getParallelList, getSearchOptions } from './utils';

function BatteryTree({
  idc,
  deviceCategory,
  syncCommonData,
  getIdcBattery,
  setSelectedTreeKey,
  roomTagDevice,
  treeMode,
  selectedTreeKey,
  devicesAlarmsData,
  alarmSpacesAndGuids,
  blockRoomTreeData,
}) {
  const wrapperRef = useRef();

  const [selectedKeys, setSelectedKeys] = useState([]);
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [treeDataSource, setTreeDataSource] = useState({
    parallelList: [],
    treeList: [],
  });

  const [searchValue, setSearchValue] = useState('');
  const [searchOptions, setSearchOptions] = useState([]);

  const spaceCodes = useSelector(selectSpaceCodes);
  let spaceParalleList = useSelector(selectSpaceEntities(spaceCodes));
  spaceParalleList = spaceParalleList.map(data => ({
    ...data,
    metaCode: data.code,
    metaName: data.name,
    metaType: data.type,
  }));

  const deviceGuidsStr = roomTagDevice?.map(({ guid }) => guid).join(',');
  const parallelListStr = treeDataSource.parallelList.map(({ guid }) => guid).join(',');
  const blockRoomTreeDataLength = blockRoomTreeData?.length;
  const devicesAlarmsDataKeysStr = devicesAlarmsData
    ? Object.keys(devicesAlarmsData).join(',')
    : null;

  useEffect(() => {
    return () => {
      const { treeList } = treeDataSource;
      const initData = (treeList?.length ?? 0) > 0 ? treeList[0] : {};
      setExpandedKeys([initData.metaCode]);
      setSelectedKeys([initData.metaCode]);
      setSelectedTreeKey(initData);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    syncCommonData({ strategy: { space: 'IF_NULL', deviceCategory: 'IF_NULL' } });
  }, [syncCommonData]);

  useEffect(() => {
    if (spaceCodes) {
      getIdcBattery(idc);
    }
  }, [idc, spaceCodes, deviceCategory, getIdcBattery]);

  useEffect(() => {
    if (!parallelListStr) {
      return;
    }
    // eslint-disable-next-line no-unused-expressions
    wrapperRef.current?.refreshData();
  }, [devicesAlarmsDataKeysStr, parallelListStr, treeMode]);

  useEffect(() => {
    if (
      selectedTreeKey &&
      selectedTreeKey.metaCode &&
      !expandedKeys.length &&
      !selectedKeys.length
    ) {
      let keys = [];
      if (selectedTreeKey && selectedTreeKey.metaType === 'DEVICE') {
        const {
          metaCode,
          parentGuid,
          guid,
          spaceGuid: { blockGuid, roomGuid },
        } = selectedTreeKey;
        if (treeMode === 'device') {
          keys = [blockGuid, roomGuid, `${roomGuid}.${parentGuid}`, metaCode];
        }
        if (treeMode === 'tag') {
          keys = [
            blockGuid,
            roomGuid,
            `${roomGuid}.${parentGuid}`,
            `${roomGuid}.${parentGuid}.${guid}`,
          ];
        }
      }

      if (selectedTreeKey && selectedTreeKey.metaType === 'TAG') {
        const {
          parentGuid,
          spaceGuid: { blockGuid, roomGuid },
        } = selectedTreeKey;
        keys = [blockGuid, roomGuid, `${roomGuid}.${parentGuid}`];
      }

      if (
        selectedTreeKey &&
        (selectedTreeKey.metaType === 'ROOM' || selectedTreeKey.metaType === 'BLOCK')
      ) {
        keys = [selectedTreeKey.metaCode];
      }
      setSelectedKeys([keys[keys.length - 1]]);
      setExpandedKeys(keys);
    }
  }, [selectedTreeKey, setSelectedKeys, setExpandedKeys, selectedKeys, expandedKeys, treeMode]);

  useEffect(() => {
    if (!deviceGuidsStr || !blockRoomTreeDataLength) {
      return;
    }
    let deviceParallelList = [];
    roomTagDevice.forEach(device => {
      deviceParallelList = [
        ...deviceParallelList,
        {
          ...device,
          metaCode: `${device.spaceGuid.roomGuid}`,
          metaName: device.name,
          metaType: 'DEVICE',
          parentCode: `${device.spaceGuid.blockGuid}`,
        },
        {
          ...device,
          metaCode: `${device.spaceGuid.roomGuid}.${device.parentGuid}`,
          metaName: device.name,
          metaType: 'DEVICE',
          parentCode: `${device.spaceGuid.roomGuid}.${device.parentGuid}.${device.guid}`,
        },
        {
          ...device,
          metaCode: `${device.spaceGuid.roomGuid}.${device.parentGuid}.${device.guid}`,
          metaName: device.name,
          metaType: 'DEVICE',
          parentCode: `${device.spaceGuid.roomGuid}`,
        },
      ];
    });
    const tagParallelList = getParallelList(deviceParallelList);
    const parallelList = [...spaceParalleList, ...tagParallelList, ...deviceParallelList];
    const treeList = getIsAlarm(blockRoomTreeData, alarmSpacesAndGuids);
    const tmp = {
      parallelList,
      treeList,
    };
    setTreeDataSource(tmp);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [deviceGuidsStr, blockRoomTreeDataLength, devicesAlarmsDataKeysStr]);

  const onSelectTree = (selectedKeys, { node }) => {
    setSelectedKeys(selectedKeys);
    setSelectedTreeKey({
      ...cloneDeep(node.dataRef),
      metaCode: node.dataRef.metaCode,
      metaType: node.dataRef.metaType,
    });
  };

  if (treeDataSource.treeList.length <= 0) {
    return null;
  }

  const onSelect = (_, selectedTreeKey) => {
    setSelectedTreeKey({
      ...selectedTreeKey,
      metaCode: selectedTreeKey.metaCode,
      metaType: selectedTreeKey.metaType,
    });

    let selectedKeys = [];
    let keys = [];
    if (selectedTreeKey && selectedTreeKey.metaType === 'DEVICE') {
      const {
        metaCode,
        parentGuid,
        guid,
        spaceGuid: { blockGuid, roomGuid },
      } = selectedTreeKey;

      if (treeMode === 'device') {
        keys = [blockGuid, roomGuid];
        selectedKeys = [metaCode];
      }
      if (treeMode === 'tag') {
        keys = [blockGuid, roomGuid, `${roomGuid}.${parentGuid}`];
        selectedKeys = [`${roomGuid}.${parentGuid}.${parentGuid}.${guid}`];
      }
    }

    if (selectedTreeKey && selectedTreeKey.metaType === 'TAG') {
      const {
        parentGuid,
        spaceGuid: { blockGuid, roomGuid },
      } = selectedTreeKey;
      keys = [blockGuid, roomGuid, `${roomGuid}.${parentGuid}`];
      selectedKeys = [`${roomGuid}.${parentGuid}`];
    }

    if (
      selectedTreeKey &&
      (selectedTreeKey.metaType === 'ROOM' || selectedTreeKey.metaType === 'BLOCK')
    ) {
      keys = [selectedTreeKey.metaCode];
      selectedKeys = [selectedTreeKey.metaCode];
    }
    setSearchValue('');
    setSearchOptions([]);
    setSelectedKeys(selectedKeys);
    setExpandedKeys(keys);
  };

  const handleSearchValueChange = async value => {
    if (!value) {
      setSearchValue(value);
      setSearchOptions([]);
      return;
    }
    setSearchOptions(getSearchOptions({ roomTagDevice, treeDataSource, value, treeMode }));
  };

  return (
    <>
      <AutoComplete
        style={{ width: 250 }}
        popupClassName={styles.batteryTreeInput}
        options={searchOptions}
        value={searchValue}
        onSelect={onSelect}
        onChange={debounce(handleSearchValueChange, 200)}
      >
        <Input.Search value={searchValue} onChange={e => setSearchValue(e.target.value)} />
      </AutoComplete>
      <ApiTree
        ref={wrapperRef}
        autoExpandParent
        selectedKeys={selectedKeys}
        expandedKeys={expandedKeys}
        fieldNames={{
          key: 'metaCode',
          title: (node, { highlightedText }) => {
            return <TreeTitle node={node} highlightedText={highlightedText} />;
          },
          parentKey: 'metaCode',
        }}
        treeNodeFilterProp="metaName"
        dataService={() => Promise.resolve(cloneDeep(treeDataSource))}
        childNodesDataService={node =>
          childNodesDataService(node, roomTagDevice, treeMode, alarmSpacesAndGuids)
        }
        onSelect={onSelectTree}
        onExpand={expandedKeys => setExpandedKeys(expandedKeys)}
        onExpandedKeysUpdate={expandedKeys => setExpandedKeys(expandedKeys)}
      />
    </>
  );
}

const mapStateToProps = (
  {
    common: { deviceCategory },
    'monitoring.subscriptions': { devicesAlarmsData },
    battery: { blockRoomTreeData, roomTagDevice, selectedTreeKey },
  },
  { alarmSpacesAndGuids }
) => {
  return {
    deviceCategory,
    roomTagDevice,
    selectedTreeKey,
    devicesAlarmsData,
    alarmSpacesAndGuids,
    blockRoomTreeData,
  };
};
const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
  getIdcBattery: getIdcBatteryActionCreator,
  setSelectedTreeKey: batteryActions.setSelectedTreeKey,
};
export default connect(mapStateToProps, mapDispatchToProps)(BatteryTree);
