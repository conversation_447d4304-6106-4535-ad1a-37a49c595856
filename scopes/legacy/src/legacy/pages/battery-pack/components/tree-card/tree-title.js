import React from 'react';

import styled from 'styled-components';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Typography } from '@manyun/base-ui.ui.typography';

const DragSpanWrapper = styled.span`
  display: inline-block;
  color: ${({ alarm }) => {
    if (alarm) {
      return `var(--${prefixCls}-error-color)`;
    }
    return 'var(--text-color)';
  }};
`;

function TreeTitle({ highlightedText, node }) {
  return (
    <DragSpanWrapper alarm={node.alarm}>
      {highlightedText ? (
        <Typography.Text type="warning">{node.metaName}</Typography.Text>
      ) : (
        node.metaName
      )}
    </DragSpanWrapper>
  );
}
export default TreeTitle;
