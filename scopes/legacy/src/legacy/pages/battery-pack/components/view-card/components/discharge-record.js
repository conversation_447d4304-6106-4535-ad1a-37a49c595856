import React from 'react';

import { TinyTable } from '@manyun/dc-brain.legacy.components';

function DischargeRecord() {
  return (
    <div style={{ marginTop: '16px' }}>
      <TinyTable dataSource={[]} columns={getColumns()} />
    </div>
  );
}
export default DischargeRecord;

const getColumns = () => [
  {
    title: '电池组名称',
    dataIndex: 'name',
  },
  {
    title: '开始时间',
    dataIndex: 'start',
  },
  {
    title: '结束时间',
    dataIndex: 'end',
  },
  {
    title: '持续时长',
    dataIndex: 'long',
  },
  {
    title: '异常电池数',
    dataIndex: 'num',
  },
];
