import React from 'react';
import { connect } from 'react-redux';

import styled from 'styled-components';

import { LiquidFill } from '@manyun/base-ui.chart.liquid-fill';

import { ConfigUtil } from '@manyun/dc-brain.util.config';

import { G<PERSON>Wrapper, StatusText, TinyCard } from '@manyun/dc-brain.legacy.components';
import { generateGetDeviceMonitoringData } from '@manyun/dc-brain.legacy.utils/device';

const StyledWrapper = styled.span`
  display: inline-block;
  width: 56px;
`;

const StyledNumberSpan = styled.span`
  display: inline-block;
  font-size: 18px;
  width: 56px;
  text-align: center;
`;

const StyledTxtSpan = styled.span`
  display: inline-block;
  width: 56px;
  text-align: center;
  font-size: 12px;
`;

const defaultThemeProps = {
  liquidFill: {
    color: '#1890FF',
  },
};

function BatteryStatus({ batteryNum, selectedTreeKey, getData }) {
  const deviceTypeAndGuid = {
    deviceGuid: selectedTreeKey.guid,
    deviceType: selectedTreeKey.deviceType,
  };

  const batteryUnitWorkingMode = getData(deviceTypeAndGuid, {
    pointType: ConfigUtil.constants.pointCodes.BATTERY_UNIT_WORKING_MODE,
    reflected: true,
  });
  const batteryUnitCapacitance = getData(deviceTypeAndGuid, {
    pointType: ConfigUtil.constants.pointCodes.BATTERY_UNIT_CAPACITY,
    formatted: true,
  });

  return (
    <TinyCard
      title="电池组状态"
      headStyle={{
        border: 0,
      }}
      bodyStyle={{
        height: '208px',
      }}
    >
      <GutterWrapper mode="vertical">
        <BatteryEchart batteryUnitCapacitance={batteryUnitCapacitance} />
        <GutterWrapper flex justifyContent="space-between">
          <StyledWrapper>
            <StyledNumberSpan>
              {batteryUnitWorkingMode.disabled ? (
                batteryUnitWorkingMode.value.NAME
              ) : (
                <StatusText background status={batteryUnitWorkingMode.value.STATUS_CODE}>
                  {batteryUnitWorkingMode.value.NAME}
                </StatusText>
              )}
            </StyledNumberSpan>
            <StyledTxtSpan>电池状态</StyledTxtSpan>
          </StyledWrapper>
          <StyledWrapper>
            <StyledNumberSpan>{batteryNum}</StyledNumberSpan>
            <StyledTxtSpan>电池数量</StyledTxtSpan>
          </StyledWrapper>
        </GutterWrapper>
      </GutterWrapper>
    </TinyCard>
  );
}
const mapStateToProps = ({
  'monitoring.subscriptions': { devicesRealtimeData, devicesAlarmsData },
  battery: { selectedTreeKey },
  config: { configMap },
}) => {
  const { pointsDefinitionMap, concretePointCodeMappings } = configMap.current;
  const getData = generateGetDeviceMonitoringData(
    devicesRealtimeData,
    devicesAlarmsData,
    pointsDefinitionMap,
    concretePointCodeMappings
  );
  return {
    selectedTreeKey,
    getData,
  };
};
export default connect(mapStateToProps, null)(BatteryStatus);

function BatteryEchart({ batteryUnitCapacitance }) {
  return (
    <Chart>
      <LiquidFill
        style={{
          height: '116px',
        }}
        series={[
          {
            itemStyle: {
              shadowBlur: 0,
            },
            radius: '95%',
            data: [
              {
                value:
                  typeof batteryUnitCapacitance.value === 'number'
                    ? batteryUnitCapacitance.value / 100
                    : '--',
                itemStyle: {
                  normal: {
                    color: defaultThemeProps.liquidFill.color,
                  },
                },
              },
            ],
            outline: {
              show: true,
              borderDistance: 6,
              itemStyle: {
                borderWidth: 1,
                borderColor: defaultThemeProps.liquidFill.color,
              },
            },
            label: {
              fontSize: 16,
              formatter: function () {
                return `${batteryUnitCapacitance.formattedText}\n容量`;
              },
            },
          },
        ]}
      />
    </Chart>
  );
}

const Chart = styled.div`
  width: 256px;
  height: 116px;
  > div > div > canvas {
    cursor: default;
  }
`;
