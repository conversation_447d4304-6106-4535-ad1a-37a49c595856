import React, { useMemo } from 'react';
import { connect } from 'react-redux';

import Icon from '@ant-design/icons';
import styled from 'styled-components';

import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { PointsLineModalButton } from '@manyun/monitoring.chart.points-line';

import {
  DebugPointValue,
  GutterWrapper,
  StatusText,
  TinyCard,
} from '@manyun/dc-brain.legacy.components';
import { generateGetDeviceMonitoringData } from '@manyun/dc-brain.legacy.utils/device';

import { ElectricCurrent, Temperature, Voltage } from '../../../incons';

const StyledWrapper = styled.div`
  display: grid;
  grid-template-columns: auto auto auto;
  grid-gap: 8px;
`;

const tableInfo = [
  {
    key: 'voltage',
    title: '电池组电压',
    unit: 'V',
    num: '556.6',
    component: <Icon component={Voltage} />,
  },
  {
    key: 'electric-current',
    title: '电池组电流',
    unit: 'A',
    num: '0.0',
    component: <Icon component={ElectricCurrent} />,
  },
  {
    key: 'temperature',
    title: '环境温度',
    unit: '℃',
    num: '0.9',
    component: <Icon component={Temperature} />,
  },
];

function OverView({ device, getData }) {
  const deviceTypeAndGuid = { deviceGuid: device.guid, deviceType: device.deviceType };

  const batteryUnitVoltage = getData(deviceTypeAndGuid, {
    pointType: ConfigUtil.constants.pointCodes.BATTERY_UNIT_VOLTAGE,
    formatted: true,
  });
  const batteryUnitElectricCurrent = getData(deviceTypeAndGuid, {
    pointType: ConfigUtil.constants.pointCodes.BATTERY_UNIT_CURRENT,
    formatted: true,
  });
  const batteryUnitTemperature = getData(deviceTypeAndGuid, {
    pointType: ConfigUtil.constants.pointCodes.BATTERY_UNIT_T_OF_ENVIRONMENT,
    formatted: true,
  });

  return (
    <StyledWrapper>
      {tableInfo.map(info => {
        let pointData;
        let serieName;
        if (info.key === 'voltage') {
          pointData = batteryUnitVoltage;
          serieName = '电池组电压';
        }
        if (info.key === 'electric-current') {
          pointData = batteryUnitElectricCurrent;
          serieName = '电池组电流';
        }
        if (info.key === 'temperature') {
          pointData = batteryUnitTemperature;
          serieName = '环境温度';
        }
        return (
          <BatteryInfo
            info={info}
            key={info.title}
            pointData={pointData}
            device={device}
            serieName={serieName}
          />
        );
      })}
    </StyledWrapper>
  );
}
const mapStateToProps = ({
  'monitoring.subscriptions': { devicesRealtimeData, devicesAlarmsData },
  battery: { selectedTreeKey },
  config: { configMap },
}) => {
  const { pointsDefinitionMap, concretePointCodeMappings } = configMap.current;
  const getData = generateGetDeviceMonitoringData(
    devicesRealtimeData,
    devicesAlarmsData,
    pointsDefinitionMap,
    concretePointCodeMappings
  );
  return {
    device: selectedTreeKey,
    getData,
  };
};
export default connect(mapStateToProps, null)(OverView);

const StyledGutterWrapper = styled(GutterWrapper)`
  text-align: center;
`;

const StyledBatter = styled.span`
  display: inline-block;
  margin-left: 32px;
`;

const StyledTitleP = styled.p`
  font-size: 12px;
`;

const StyledIcon = styled.span``;

function BatteryInfo({ info, pointData, device, serieName }) {
  const pointGuids = useMemo(
    () => [
      {
        spaceGuid: device.spaceGuid.blockGuid,
        deviceGuid: device.guid,
        deviceType: device.deviceType,
        pointCode: pointData.pointCode,
      },
    ],
    [device.deviceType, device.guid, device.spaceGuid.blockGuid, pointData.pointCode]
  );

  return (
    <TinyCard>
      <StyledGutterWrapper mode="horizontal">
        <StyledIcon>{info.component}</StyledIcon>
        <StyledBatter>
          <StyledTitleP>{info.title}</StyledTitleP>
          <p>
            {pointData.disabled && pointData.value}
            {!pointData.disabled && (
              <>
                <PointsLineModalButton
                  idcTag={device.spaceGuid.idcTag}
                  btnText={
                    <DebugPointValue
                      {...pointData}
                      deviceType={device.deviceType}
                      nonDebugRender={() => (
                        <StatusText status={pointData.status}>{pointData.value}</StatusText>
                      )}
                    />
                  }
                  modalText={info.title}
                  pointGuids={pointGuids}
                  seriesOption={[{ name: serieName }]}
                />
                &nbsp;
                <span>({pointData.unit})</span>
              </>
            )}
          </p>
        </StyledBatter>
      </StyledGutterWrapper>
    </TinyCard>
  );
}
