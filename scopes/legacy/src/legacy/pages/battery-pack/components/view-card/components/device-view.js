import React, { useEffect, useMemo, useState } from 'react';
import { connect, useDispatch, useSelector } from 'react-redux';

import styled from 'styled-components';

import { Col, Row } from '@manyun/base-ui.ui.grid';
import { message } from '@manyun/base-ui.ui.message';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import { Typography } from '@manyun/base-ui.ui.typography';

import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { usePointsData } from '@manyun/monitoring.hook.use-points-data';
import {
  BatteryLocation,
  BatteryPackView,
  SingleBatteryView,
  getBatteryCode,
} from '@manyun/monitoring.page.battery-pack-view';
import { fetchBatteryList } from '@manyun/monitoring.service.fetch-battery-list';
import {
  SUBSCRIPTIONS_MODE,
  addRealtimeNAlarmsDataSubscriptionActionCreator as subscribe,
  removeRealtimeNAlarmsDataSubscriptionActionCreator as unsubscribe,
} from '@manyun/monitoring.state.subscriptions';
import { generateDeviceRecordRoutePath } from '@manyun/resource-hub.route.resource-routes';
import { DeviceTypeText } from '@manyun/resource-hub.ui.device-type-text';

import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';

import BatteryStatus from './battery-status';
import LineChartCard from './line-charts';
import OverView from './overview';

const contentHeight = `calc(var(--content-height) - 71px)`;

const StyledWrapper = styled.div`
  display: flex;
  flex-direction: column;
`;

const StyledOverviewWrapper = styled.div`
  width: 100%;
  height: 46px;
  background: #fff;
  display: flex;
  line-height: 46px;
  justify-content: space-between;
  padding: 0 24px;
  margin-bottom: 16px;
`;

const StyledCenterWapper = styled.div`
  display: grid;
  grid-template-rows: 266px auto;
  grid-gap: 22px;
`;

const moduleId = 'battery-packs_realtime_n_alarms';

function DeviceView({
  totalHeight,
  viewType,
  setViewType,
  selectedTreeKey,
  batteryAlarmList,
  getConfiguredPointCode,
  syncCommonData,
}) {
  const [batterys, setBatterys] = useState([]);
  const dispatch = useDispatch();

  useEffect(() => {
    if (!selectedTreeKey || !selectedTreeKey.deviceType) {
      return;
    }
    // 查询子设备 电池
    (async function getPointCodes() {
      const { data, error } = await fetchBatteryList({
        deviceGuid: selectedTreeKey.guid,
      });
      if (error) {
        message.error(error.message);
        return;
      }
      if (data.data.length) {
        setBatterys(data.data);
      }
      syncCommonData({
        // 电池组使用父设备type同步测点信息
        strategy: {
          deviceTypesPointsDefinition: [selectedTreeKey.deviceType],
        },
      });
    })();
  }, [selectedTreeKey, syncCommonData]);

  const config = useSelector(selectCurrentConfig);
  const configUtil = useMemo(() => {
    return new ConfigUtil(config);
  }, [config]);

  // 单体电压
  const BATTERY_VOLTAGE_POINTCODE = configUtil.getPointCode(
    selectedTreeKey.deviceType,
    ConfigUtil.constants.pointCodes.BATTERY_VOLTAGE
  );
  // 单体温度
  const BATTERY_T_POINTCODE = configUtil.getPointCode(
    selectedTreeKey.deviceType,
    ConfigUtil.constants.pointCodes.BATTERY_T
  );
  // 单体内阻
  const BATTERY_INTERNAL_RESISTANCE_POINTCODE = configUtil.getPointCode(
    selectedTreeKey.deviceType,
    ConfigUtil.constants.pointCodes.BATTERY_INTERNAL_RESISTANCE
  );

  const [voltagePointData] = usePointsData({
    blockGuid: selectedTreeKey?.spaceGuid.blockGuid,
    moduleId: 'BatteryViewVoltagePointData',
    targets: [
      {
        guid: selectedTreeKey.guid,
        type: selectedTreeKey.deviceType,
        hardCodedPoints: batterys.map(battery => ({
          pointCode: getBatteryCode(battery.guid, BATTERY_VOLTAGE_POINTCODE),
          dataType: 'AI',
        })),
      },
    ],
  });

  const [tempeturePointData] = usePointsData({
    blockGuid: selectedTreeKey?.spaceGuid.blockGuid,
    moduleId: 'BatteryViewTempeturePointData',
    targets: [
      {
        guid: selectedTreeKey.guid,
        type: selectedTreeKey.deviceType,
        hardCodedPoints: batterys.map(battery => ({
          pointCode: getBatteryCode(battery.guid, BATTERY_T_POINTCODE),
          dataType: 'AI',
        })),
      },
    ],
  });

  const [internalResistancePointData] = usePointsData({
    blockGuid: selectedTreeKey?.spaceGuid.blockGuid,
    moduleId: 'BatteryViewInternalResistancePointData',
    targets: [
      {
        guid: selectedTreeKey.guid,
        type: selectedTreeKey.deviceType,
        hardCodedPoints: batterys.map(battery => ({
          pointCode: getBatteryCode(battery.guid, BATTERY_INTERNAL_RESISTANCE_POINTCODE),
          dataType: 'AI',
        })),
      },
    ],
  });

  const blockGuid =
    selectedTreeKey.spaceGuid.roomGuid ||
    selectedTreeKey.spaceGuid.blockGuid ||
    selectedTreeKey.spaceGuid.idcTag;
  const batteryUnitsStrs = selectedTreeKey.guid;

  const targets = useMemo(() => {
    if (!blockGuid || !batteryUnitsStrs) {
      return null;
    }
    const batteryUnitsGuids = [batteryUnitsStrs];
    return [
      {
        mode: SUBSCRIPTIONS_MODE.REALTIME_N_ALARMS,
        blockGuid,
        moduleId,
        deviceGuids: batteryUnitsGuids,
      },
    ];
  }, [blockGuid, batteryUnitsStrs]);

  useEffect(() => {
    if (targets && dispatch) {
      dispatch(subscribe({ targets }));
    }
    return () => {
      if (targets) {
        dispatch(unsubscribe({ mode: SUBSCRIPTIONS_MODE.REALTIME_N_ALARMS, moduleId }));
      }
    };
  }, [targets, dispatch]);

  useEffect(() => {
    setBatterys(batterys => {
      return batterys.map(item => {
        const alarmPoints = batteryAlarmList.filter(({ pointCode }) => {
          const batteryUnitVoltagePointCode = getConfiguredPointCode(
            item.deviceType,
            'battery-unit_voltage'
          );
          const batteryUnitElectricCurrentPointCode = getConfiguredPointCode(
            item.deviceType,
            'battery-unit_electric-current'
          );
          const batteryUnitTemperaturePointCode = getConfiguredPointCode(
            item.deviceType,
            'battery-unit_temperature'
          );
          if (
            pointCode === batteryUnitVoltagePointCode ||
            pointCode === batteryUnitElectricCurrentPointCode ||
            pointCode === batteryUnitTemperaturePointCode
          ) {
            return true;
          }
          return false;
        });
        if (alarmPoints.length) {
          return {
            ...item,
            alarm: true,
          };
        }
        return item;
      });
    });
  }, [batteryAlarmList, getConfiguredPointCode]);

  return (
    <StyledWrapper viewType={viewType}>
      <StyledOverviewWrapper>
        <div>
          <Typography.Text style={{ fontWeight: 500, fontSize: 16 }}>
            {selectedTreeKey.name + '  '}
            <DeviceTypeText code={selectedTreeKey?.deviceType || ''} />
          </Typography.Text>
          <Typography.Link
            style={{ marginLeft: 32 }}
            target="_blank"
            href={generateDeviceRecordRoutePath({ guid: selectedTreeKey.guid })}
          >
            设备履历
          </Typography.Link>
        </div>
        <Tabs
          defaultActiveKey="overview"
          items={[
            {
              label: `总览`,
              key: 'overview',
            },
            {
              label: `单体电池`,
              key: 'battery',
            },
            {
              label: `充放电记录`,
              key: 'dischargeRecord',
            },
          ]}
          onChange={value => setViewType(value)}
        />
      </StyledOverviewWrapper>
      {viewType === 'overview' && (
        <StyledCenterWapper>
          <Row gutter={[16, 16]} style={{ overflowy: 'auto', height: contentHeight }}>
            <Col
              style={{
                maxWidth: 320,
              }}
            >
              <Row gutter={[16, 16]}>
                <Col span={24}>
                  <BatteryStatus batteryNum={batterys.length} />
                </Col>
                <Col span={24}>
                  <BatteryLocation
                    cardHeight={`calc(${totalHeight} - 402px)`}
                    childBatterys={batterys}
                    selectedBattery={selectedTreeKey}
                    pointsData={{
                      voltage: voltagePointData,
                      tempeture: tempeturePointData,
                      internalResistance: internalResistancePointData,
                    }}
                    pointsCode={{
                      voltage: BATTERY_VOLTAGE_POINTCODE,
                      tempeture: BATTERY_T_POINTCODE,
                      internalResistance: BATTERY_INTERNAL_RESISTANCE_POINTCODE,
                    }}
                  />
                </Col>
              </Row>
            </Col>
            <Col
              style={{
                flex: 1,
              }}
            >
              <Row gutter={[16, 16]}>
                <Col span={24}>
                  <OverView />
                </Col>
                <Col span={24}>
                  <LineChartCard cardHeight={`calc(${totalHeight} - 198px)`} />
                </Col>
              </Row>
            </Col>
          </Row>
        </StyledCenterWapper>
      )}
      {viewType === 'battery' && (
        <SingleBatteryView
          contentHeight={`calc(${totalHeight}  - 48px)`}
          childBatterys={batterys}
          selectedBattery={selectedTreeKey}
          pointsData={{
            voltage: voltagePointData,
            tempeture: tempeturePointData,
            internalResistance: internalResistancePointData,
          }}
          pointsCode={{
            voltage: BATTERY_VOLTAGE_POINTCODE,
            tempeture: BATTERY_T_POINTCODE,
            internalResistance: BATTERY_INTERNAL_RESISTANCE_POINTCODE,
          }}
        />
      )}
      {viewType === 'dischargeRecord' && <BatteryPackView device={selectedTreeKey} />}
    </StyledWrapper>
  );
}

const mapStateToProps = ({ battery: { selectedTreeKey, batteryAlarmList } }) => {
  return {
    selectedTreeKey,
    batteryAlarmList,
  };
};
const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
};
export default connect(mapStateToProps, mapDispatchToProps)(DeviceView);
