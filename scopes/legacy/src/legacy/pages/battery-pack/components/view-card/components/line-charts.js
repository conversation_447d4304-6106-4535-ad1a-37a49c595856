import React from 'react';
import { connect } from 'react-redux';

import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { PointsLine } from '@manyun/monitoring.chart.points-line';

import { GutterWrapper, TinyCard } from '@manyun/dc-brain.legacy.components';

function LineChartCard({ cardHeight, device, configUtil }) {
  const batteryUnitVoltagePointCode = configUtil.getPointCode(
    device.deviceType,
    ConfigUtil.constants.pointCodes.BATTERY_UNIT_VOLTAGE
  );
  const batteryUnitElectricCurrentPointCode = configUtil.getPointCode(
    device.deviceType,
    ConfigUtil.constants.pointCodes.BATTERY_UNIT_CURRENT
  );
  const batteryUnitVoltagePointInfo = configUtil.getPointDefinition(
    device.deviceType,
    batteryUnitVoltagePointCode
  );
  const batteryUnitElectricCurrentPointInfo = configUtil.getPointDefinition(
    device.deviceType,
    batteryUnitElectricCurrentPointCode
  );

  return (
    <TinyCard
      bodyStyle={{
        height: cardHeight,
        overflowY: 'auto',
      }}
    >
      <GutterWrapper mode="vertical" style={{ width: '99%' }}>
        <TinyCard>
          <GutterWrapper mode="vertical">
            <PointsLine
              idcTag={device.spaceGuid.idcTag}
              allowInterval={true}
              pointGuids={[
                {
                  deviceGuid: device.guid,
                  pointCode: batteryUnitVoltagePointCode,
                  unit: batteryUnitVoltagePointInfo?.unit,
                },
              ]}
              seriesOption={[{ name: '电压' }]}
              basicOption={{
                title: {
                  text: '电池组电压曲线图',
                  left: 'center',
                  top: 5,
                  textStyle: {
                    fontSize: 12,
                  },
                },
              }}
              chartFunction="MAX"
            />
          </GutterWrapper>
        </TinyCard>
        <TinyCard>
          <GutterWrapper mode="vertical">
            <PointsLine
              idcTag={device.spaceGuid.idcTag}
              allowInterval={true}
              pointGuids={[
                {
                  deviceGuid: device.guid,
                  pointCode: batteryUnitElectricCurrentPointCode,
                  unit: batteryUnitElectricCurrentPointInfo?.unit,
                },
              ]}
              seriesOption={[{ name: '电流' }]}
              basicOption={{
                title: {
                  text: '电池组电流曲线图',
                  left: 'center',
                  top: 5,
                  textStyle: {
                    fontSize: 12,
                  },
                },
              }}
              chartFunction="MAX"
            />
          </GutterWrapper>
        </TinyCard>
      </GutterWrapper>
    </TinyCard>
  );
}

const mapStateToProps = ({ battery: { selectedTreeKey }, config: { configMap } }) => {
  const configUtil = new ConfigUtil(configMap.current);

  return {
    device: selectedTreeKey,
    configUtil,
  };
};

export default connect(mapStateToProps, null)(LineChartCard);
