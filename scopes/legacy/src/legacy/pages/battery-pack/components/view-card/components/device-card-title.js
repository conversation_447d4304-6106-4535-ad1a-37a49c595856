import React from 'react';

import { Tooltip } from '@manyun/base-ui.ui.tooltip';

import { StyledTitleTxT, StyledTitleWapper } from '../../../styled';

function DeviceCardTitle({ device, onClickCard }) {
  return (
    <StyledTitleWapper
      onClick={() => {
        if (onClickCard && typeof onClickCard === 'function') {
          onClickCard(device);
        }
      }}
      clickable={onClickCard && typeof onClickCard === 'function'}
    >
      <Tooltip title={device.name}>
        <StyledTitleTxT alarm={device.alarm}>{device.name}</StyledTitleTxT>
      </Tooltip>
    </StyledTitleWapper>
  );
}
export default DeviceCardTitle;
