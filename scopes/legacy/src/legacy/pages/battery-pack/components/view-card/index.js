import sortBy from 'lodash/sortBy';
import React, { useEffect, useMemo, useState } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';
import styled from 'styled-components';

import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { PointsLineModalButton } from '@manyun/monitoring.chart.points-line';
import {
  addRealtimeNAlarmsDataSubscriptionActionCreator,
  removeRealtimeNAlarmsDataSubscriptionActionCreator,
} from '@manyun/monitoring.state.subscriptions';
import { generateDeviceRecordRoutePath } from '@manyun/resource-hub.route.resource-routes';

import {
  DebugPointValue,
  GutterWrapper,
  StatusText, // Ellipsis,
  TinyCard,
  TinyDescriptions,
} from '@manyun/dc-brain.legacy.components';
import { SUBSCRIPTIONS_MODE } from '@manyun/dc-brain.legacy.constants/subscriptions';
import { batteryActions } from '@manyun/dc-brain.legacy.redux/actions/batteryActions';
import { generateGetDeviceMonitoringData } from '@manyun/dc-brain.legacy.utils/device';

import { getLocationDevice } from '../../utils';
import DeviceCardTitle from './components/device-card-title';
import DeviceView from './components/device-view';

const StyledWrapper = styled.div`
  display: grid;
  grid-template-columns: auto auto;
  grid-template-rows: auto auto;
  grid-gap: 8px;
`;

const StyledBatteryWapper = styled.div`
  display: grid;
  grid-gap: 4px;
`;

function ViewCard({
  totalHeight,
  selectedTreeKey,
  setSelectedTreeKey,
  parentGuids,
  deviceSplitSameParent,
  getData,
  spaceDevcieGuids,
  subscribe,
  unsubscribe,
  alarmSpacesAndGuids,
}) {
  const [viewType, setViewType] = useState('overview');

  const moduleId = 'battery-packs_realtime_n_alarms';
  const blockGuid = selectedTreeKey.metaCode;
  const batteryUnitsStrs = spaceDevcieGuids?.join(',');

  const targets = useMemo(() => {
    if (!blockGuid || !batteryUnitsStrs) {
      return null;
    }
    const batteryUnitsGuids = batteryUnitsStrs.split(',');
    return [
      {
        mode: SUBSCRIPTIONS_MODE.REALTIME_N_ALARMS,
        blockGuid,
        moduleId,
        deviceGuids: batteryUnitsGuids,
      },
    ];
  }, [blockGuid, batteryUnitsStrs]);

  useEffect(() => {
    if (targets) {
      subscribe({ targets });
    }
    return () => {
      if (targets) {
        unsubscribe({ moduleId, mode: SUBSCRIPTIONS_MODE.REALTIME_N_ALARMS });
      }
    };
  }, [targets, subscribe, unsubscribe, moduleId]);

  const onClickCard = device => {
    setSelectedTreeKey({
      ...device,
      metaCode: `${device.spaceGuid.roomGuid}.${device.guid}`,
      metaName: device.name,
      metaType: 'DEVICE',
    });
  };

  return (
    <>
      {selectedTreeKey && selectedTreeKey.metaType !== 'DEVICE' && (
        <GutterWrapper style={{ height: totalHeight, overflowY: 'auto' }} flexN={1}>
          <StyledWrapper>
            {parentGuids.map(({ title }) => {
              return (
                <TinyCard key={title}>
                  <StyledBatteryWapper
                    style={{
                      gridTemplateColumns:
                        parentGuids.length === 1 ? '33.333% 33.333% 33.333%' : '50% 50%',
                    }}
                  >
                    {deviceSplitSameParent[title].map(device => {
                      return (
                        <Battery
                          key={device.guid}
                          device={device}
                          getData={getData}
                          alarmSpacesAndGuids={alarmSpacesAndGuids}
                          onClickCard={onClickCard}
                        />
                      );
                    })}
                  </StyledBatteryWapper>
                </TinyCard>
              );
            })}
          </StyledWrapper>
        </GutterWrapper>
      )}
      {selectedTreeKey && selectedTreeKey.metaType === 'DEVICE' && (
        <GutterWrapper style={{ height: totalHeight }} flexN={1}>
          <DeviceView totalHeight={totalHeight} viewType={viewType} setViewType={setViewType} />
        </GutterWrapper>
      )}
    </>
  );
}
const mapStateToProps = (
  {
    'monitoring.subscriptions': { devicesRealtimeData, devicesAlarmsData },
    battery: { roomTagDevice, selectedTreeKey },
    config: { configMap },
  },
  { alarmSpacesAndGuids }
) => {
  let viewData = {};
  if (roomTagDevice && selectedTreeKey) {
    viewData = getLocationDevice(roomTagDevice, selectedTreeKey);
  }

  // 查询父设备下是否有告警设备，如果有，则父设备按告警排序
  let deviceSplitSameParent = {};
  const parentGuids = Object.keys(viewData).map(parentGuid => {
    const newDevices = getIsAlarem(viewData[parentGuid], alarmSpacesAndGuids.alarmGuids);
    deviceSplitSameParent = {
      ...deviceSplitSameParent,
      [parentGuid]: sortBy(newDevices, 'alarm'),
    };
    if (alarmSpacesAndGuids.alarmParentGuids.includes(parentGuid)) {
      return {
        title: parentGuid,
        alarm: true,
      };
    }
    return {
      title: parentGuid,
    };
  });

  const { pointsDefinitionMap, concretePointCodeMappings } = configMap.current;

  const getData = generateGetDeviceMonitoringData(
    devicesRealtimeData,
    devicesAlarmsData,
    pointsDefinitionMap,
    concretePointCodeMappings
  );

  // 当前空间下的所有设备
  const spaceDevcieGuids = getSpaceDevcie(selectedTreeKey, roomTagDevice);

  return {
    selectedTreeKey,
    parentGuids: sortBy(parentGuids, 'alarm'),
    deviceSplitSameParent,
    getData,
    spaceDevcieGuids,
  };
};
const mapDispatchToProps = {
  setSelectedTreeKey: batteryActions.setSelectedTreeKey,
  subscribe: addRealtimeNAlarmsDataSubscriptionActionCreator,
  unsubscribe: removeRealtimeNAlarmsDataSubscriptionActionCreator,
};
export default connect(mapStateToProps, mapDispatchToProps)(ViewCard);

function Battery({ device, onClickCard, getData }) {
  const deviceTypeAndGuid = { deviceGuid: device.guid, deviceType: device.deviceType };

  const batteryUnitVoltage = getData(deviceTypeAndGuid, {
    pointType: ConfigUtil.constants.pointCodes.BATTERY_UNIT_VOLTAGE,
    formatted: true,
  });
  const batteryUnitElectricCurrent = getData(deviceTypeAndGuid, {
    pointType: ConfigUtil.constants.pointCodes.BATTERY_UNIT_CURRENT,
    formatted: true,
  });
  const batteryUnitCommunicationMode = getData(deviceTypeAndGuid, {
    pointType: ConfigUtil.constants.pointCodes.BATTERY_UNIT_COMMUNICATION_STATE,
    reflected: true,
  });
  const batteryUnitWorkingMode = getData(deviceTypeAndGuid, {
    pointType: ConfigUtil.constants.pointCodes.BATTERY_UNIT_WORKING_MODE,
    reflected: true,
  });

  return (
    <TinyCard headStyle={{ border: 0, cursor: 'pointer' }}>
      <GutterWrapper mode="vertical">
        <DeviceCardTitle device={device} onClickCard={onClickCard} />
        <TinyDescriptions
          column={1}
          descriptionsItems={[
            {
              label: '所属设备',
              value: (
                <Link
                  to={generateDeviceRecordRoutePath({
                    guid: device.parentGuid,
                  })}
                >
                  {device.parentName}
                </Link>
              ),
            },
            {
              label: '组端电压',
              value: batteryUnitVoltage.disabled ? (
                batteryUnitVoltage.formattedText
              ) : (
                <PointsLineModalButton
                  idcTag={device.spaceGuid.idcTag}
                  btnText={
                    <DebugPointValue
                      {...batteryUnitVoltage}
                      deviceType={device.deviceType}
                      nonDebugRender={() => (
                        <StatusText status={batteryUnitVoltage.status}>
                          {batteryUnitVoltage.formattedText}
                        </StatusText>
                      )}
                    />
                  }
                  modalText="组端电压"
                  pointGuids={[
                    {
                      spaceGuid:
                        device.spaceGuid.roomGuid ||
                        device.spaceGuid.blockGuid ||
                        device.spaceGuid.idcTag,
                      deviceGuid: device.guid,
                      deviceType: device.deviceType,
                      pointCode: batteryUnitVoltage.pointCode,
                    },
                  ]}
                  seriesOption={[{ name: '组端电压' }]}
                />
              ),
            },
            {
              label: '组端电流',
              value: batteryUnitElectricCurrent.disabled ? (
                batteryUnitElectricCurrent.formattedText
              ) : (
                <PointsLineModalButton
                  idcTag={device.spaceGuid.idcTag}
                  btnText={
                    <DebugPointValue
                      {...batteryUnitElectricCurrent}
                      deviceType={device.deviceType}
                      nonDebugRender={() => (
                        <StatusText status={batteryUnitElectricCurrent.status}>
                          {batteryUnitElectricCurrent.formattedText}
                        </StatusText>
                      )}
                    />
                  }
                  modalText="组端电流"
                  pointGuids={[
                    {
                      spaceGuid:
                        device.spaceGuid.roomGuid ||
                        device.spaceGuid.blockGuid ||
                        device.spaceGuid.idcTag,
                      deviceGuid: device.guid,
                      deviceType: device.deviceType,
                      pointCode: batteryUnitElectricCurrent.pointCode,
                    },
                  ]}
                  seriesOption={[{ name: '组端电流' }]}
                />
              ),
            },
            {
              label: '电池组状态',
              value: batteryUnitWorkingMode.disabled ? (
                batteryUnitWorkingMode.value.NAME
              ) : (
                <DebugPointValue
                  {...batteryUnitWorkingMode}
                  deviceType={device.deviceType}
                  nonDebugRender={() => (
                    <StatusText background status={batteryUnitWorkingMode.value.STATUS_CODE}>
                      {batteryUnitWorkingMode.value.NAME}
                    </StatusText>
                  )}
                />
              ),
            },
            {
              label: '通讯状态',
              value: batteryUnitCommunicationMode.disabled ? (
                batteryUnitCommunicationMode.value.NAME
              ) : (
                <DebugPointValue
                  {...batteryUnitCommunicationMode}
                  deviceType={device.deviceType}
                  nonDebugRender={() => (
                    <StatusText background status={batteryUnitCommunicationMode.value.STATUS_CODE}>
                      {batteryUnitCommunicationMode.value.NAME}
                    </StatusText>
                  )}
                />
              ),
            },
          ]}
        />
      </GutterWrapper>
    </TinyCard>
  );
}

function getIsAlarem(data, alarmGuids) {
  return data.map(item => {
    if (alarmGuids.includes(item.guid)) {
      return {
        ...item,
        alarm: true,
      };
    }
    return item;
  });
}

function getSpaceDevcie(selectedTreeKey, roomTagDevice) {
  if (!selectedTreeKey) {
    return [];
  }
  if (selectedTreeKey.metaType === 'BLOCK') {
    const device = roomTagDevice.filter(
      ({ spaceGuid }) => spaceGuid.blockGuid === selectedTreeKey.metaCode
    );
    return device.map(({ guid }) => guid);
  }
  if (selectedTreeKey.metaType === 'ROOM') {
    const device = roomTagDevice.filter(
      ({ spaceGuid }) => spaceGuid.roomGuid === selectedTreeKey.metaCode
    );
    return device.map(({ guid }) => guid);
  }
  if (selectedTreeKey.metaType === 'TAG') {
    const device = roomTagDevice.filter(
      ({ spaceGuid, parentGuid }) =>
        `${spaceGuid.roomGuid}.${parentGuid}` === selectedTreeKey.metaCode
    );
    return device.map(({ guid }) => guid);
  }
}
