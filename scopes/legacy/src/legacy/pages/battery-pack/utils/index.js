export function getLocationDevice(roomTagDevice, selectedTreeKey) {
  const { metaCode, metaType } = selectedTreeKey;
  let deviceArr = [];
  let splitTagDevice = {};
  if (metaType === 'BLOCK') {
    deviceArr = roomTagDevice.filter(({ spaceGuid }) => {
      if (spaceGuid.blockGuid === metaCode) {
        return true;
      }
      return false;
    });
  }
  if (metaType === 'ROOM') {
    deviceArr = roomTagDevice.filter(({ spaceGuid }) => {
      if (spaceGuid.roomGuid === metaCode) {
        return true;
      }
      return false;
    });
  }
  if (metaType === 'TAG') {
    deviceArr = roomTagDevice.filter(({ parentGuid }) => {
      if (parentGuid === selectedTreeKey.parentGuid) {
        return true;
      }
      return false;
    });
  }
  deviceArr.forEach(item => {
    if (!splitTagDevice[item.parentGuid]) {
      splitTagDevice = {
        ...splitTagDevice,
        [item.parentGuid]: [item],
      };
    } else {
      splitTagDevice = {
        ...splitTagDevice,
        [item.parentGuid]: [...splitTagDevice[item.parentGuid], item],
      };
    }
  });
  return splitTagDevice;
}
