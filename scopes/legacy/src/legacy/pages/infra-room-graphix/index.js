import React from 'react';
import { connect, useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';

import BookOutlined from '@ant-design/icons/es/icons/BookOutlined';
import EllipsisOutlined from '@ant-design/icons/es/icons/EllipsisOutlined';
import ImportOutlined from '@ant-design/icons/es/icons/ImportOutlined';
import LoadingOutlined from '@ant-design/icons/es/icons/LoadingOutlined';
import MinusCircleOutlined from '@ant-design/icons/es/icons/MinusCircleOutlined';
import PlusCircleOutlined from '@ant-design/icons/es/icons/PlusCircleOutlined';
import { ApiSelect } from '@galiojs/awesome-antd';
import cloneDeep from 'lodash.clonedeep';
import omit from 'lodash.omit';
import styled from 'styled-components';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import {
  AuraGraphix,
  ThemeCompositions,
  createStore,
  toBase64,
  traverse,
} from '@manyun/dc-brain.aura-graphix';
import { StrokeWidth } from '@manyun/dc-brain.aura-graphix/dist/props-panel/line-element-props/mutators/stroke-width';
import { syncCommonDataAction } from '@manyun/dc-brain.state.common';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { redirectAction } from '@manyun/dc-brain.state.router';
import { Upload } from '@manyun/dc-brain.ui.upload';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { fetchPointsByCondition } from '@manyun/resource-hub.service.fetch-points-by-condition';
import {
  PipeTypeSelect,
  getTextElementsPropertiesPanel,
  useTextElementsProperties,
} from '@manyun/resource-hub.ui.topology-graphix';
import { getSpaceGuid } from '@manyun/resource-hub.util.space-guid';

import { Loading } from '@manyun/dc-brain.legacy.components';
import * as urls from '@manyun/dc-brain.legacy.constants/urls';
import { ThemeColors } from '@manyun/dc-brain.legacy.pages/__next/infra-room-graphix';
import * as relatedElementUtil from '@manyun/dc-brain.legacy.pages/topology-graphix/utils/uniq-id/relatedElement';
import {
  infraRoomGraphixActions,
  initializeActionCreator,
  saveGraphActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/infraRoomGraphixActions';
import getImagePropsAsync from '@manyun/dc-brain.legacy.utils/getImagePropsAsync';

import arrowSvg from './assets/arrow.svg';
import textSvg from './assets/text.svg';
import GroupNamingModal from './components/group-naming-modal';

/**
 * 产品侧要求固定画布大小
 */
const PAGE_CONFIG = {
  size: 'fixed',
  width: 1920,
  height: 1080,
  padding: 0,
};

const BACKGROUD_IMAGE_TYPE = 'background-image';

function InfraRoomGraphix({
  isReady,
  resourceCategories,
  graph,
  deviceTypeMap,
  syncCommonData,
  initialize,
  saveGraph,
  clearGraph,
  redirect,
}) {
  const { idc, block, room: urlRoom, mode, topologyType } = useParams();
  const room = decodeURIComponent(urlRoom);
  /**
   * 要求
   *  - 必须添加底图
   *  - 可以在编组后对编组进行命名
   */
  const isGenericTopology = topologyType === 'ROOM_FACILITY' || topologyType === 'LIQUID_TOPOLOGY';

  const config = useSelector(selectCurrentConfig);
  const roomGuid = getSpaceGuid(idc, block, room);
  const blockGuid = getSpaceGuid(idc, block);
  const configUtil = React.useMemo(
    () => new ConfigUtil(config, { defaultSpaceGuid: roomGuid }),
    [config, roomGuid]
  );

  const typeofWaterLeakDetector = React.useMemo(
    () => configUtil.typeofDeviceGen(ConfigUtil.constants.deviceTypes.WATER_LEAK_DETECTOR),
    [configUtil]
  );
  const typeofPositioningWaterLeakDetector = React.useMemo(
    () =>
      configUtil.typeofDeviceGen(ConfigUtil.constants.deviceTypes.POSITIONING_WATER_LEAK_DETECTOR),
    [configUtil]
  );

  const [store, setStore] = React.useState(null);
  const [hasBackgroundImage, setHasBackgroupdImage] = React.useState(false);
  const [importingBackgroundImage, setImportingBackgroundImage] = React.useState(false);
  const [groupNamingModalVisible, setGroupNamingModalVisible] = React.useState(false);
  const [groupElement, setGroupElement] = React.useState(null);

  const [
    { selectedDeviceType, textElements, pointElements, textPropsConfig },
    { setSelectedDeviceType, setTextPropsConfig },
  ] = useTextElementsProperties(store);

  React.useEffect(() => {
    syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });
  }, [syncCommonData]);

  React.useEffect(() => {
    initialize({ idc, block, room, mode, topologyType });

    return () => {
      clearGraph();
      setStore(null);
    };
  }, [idc, block, room, initialize, mode, clearGraph, topologyType]);

  const storeRef = React.useRef(null);
  React.useEffect(() => {
    if (isReady && !graph) {
      const _store = createStore();
      _store.addPage(PAGE_CONFIG);
      storeRef.current = _store;
      setStore(_store);
    }
    if (graph) {
      const _store = createStore();
      _store
        .loadJSON(
          cloneDeep({
            ...graph,
            pages: graph.pages.map(p => ({
              ...p,
              ...PAGE_CONFIG,
            })),
          })
        )
        .then(() => {
          for (let index = 0; index < graph.pages.length; index++) {
            const page = graph.pages[index];
            let hasBackImg = false;
            traverse(page.children, element => {
              if (element.custom?.type === BACKGROUD_IMAGE_TYPE) {
                hasBackImg = true;
              }
            });
            if (hasBackImg) {
              setHasBackgroupdImage(true);
              break;
            }
          }
          storeRef.current = _store;
          setStore(_store);
          initializePageConfig(_store.activePage, topologyType);
        });
    }
  }, [isReady, graph, topologyType]);

  if (!isReady || !store) {
    return <Loading containerStyle={{ height: '100vh' }} description="正在初始化..." />;
  }

  const addBackgroundImageAsync = async file => {
    setImportingBackgroundImage(true);

    const dataUrl = await toBase64(file);
    const { width, height } = await getImagePropsAsync(dataUrl);
    const pageWidth = store.activePage.width + store.activePage.padding * 2;
    const pageHeight = store.activePage.height + store.activePage.padding * 2;
    const minPageSize = Math.min(pageWidth, pageHeight);
    const resizedWidth = width > minPageSize ? minPageSize * 0.9 : width;
    const resizedHeight = resizedWidth !== width ? (resizedWidth / width) * height : height;

    store.activePage.addElement({
      type: 'image',
      name: '底图',
      x: (pageWidth - resizedWidth) / 2 - store.activePage.padding,
      y: (pageHeight - resizedHeight) / 2 - store.activePage.padding,
      width: resizedWidth,
      height: resizedHeight,
      src: dataUrl,
      locked: false,
      custom: {
        type: BACKGROUD_IMAGE_TYPE,
      },
    });

    // AuraGraphix need some time to render this image onto it
    // but we don't know when it will be done
    // so we just wait a bit
    await new Promise(resolve => setTimeout(resolve, 500));

    setImportingBackgroundImage(false);
    setHasBackgroupdImage(true);
  };

  return (
    <ThemeCompositions theme={document.body.dataset.theme === 'dark' ? 'dark' : 'light'}>
      <div
        style={{
          width: '100vw',
          height: '100vh',
          position: 'fixed',
          top: 0,
          right: 0,
          bottom: 0,
          left: 0,
          zIndex: 999,
        }}
      >
        <AuraGraphix
          debug={env.__DEBUG_MODE__}
          store={store}
          width="100vw"
          height="100vh"
          title={`${mode === 'new' ? '新建' : '编辑'} ${getSpaceGuid(idc, block, room)} 视图`}
          resourceCategories={resourceCategories}
          resourceLabelRender={resource => {
            if (!deviceTypeMap) {
              return {
                renderingLabel: resource.label,
                filteringLabel: resource.label,
              };
            }

            const renderingLabel =
              deviceTypeMap[resource.label /* deviceType */]?.metaName || resource.label;

            return {
              renderingLabel,
              filteringLabel: renderingLabel,
            };
          }}
          extraElementPropsRender={(elements, { Panel, BlockProp, Divider }) => {
            const textElementsPropertiesByDeviceType = getTextElementsPropertiesPanel({
              Panel,
              BlockProp,
              Divider,
              store: storeRef.current,
              deviceTypeMap,
              selectedDeviceType,
              textPropsConfig,
              textElements,
              pointElements,
              onDeviceTypeChange: deviceType => {
                setSelectedDeviceType(deviceType);
              },
              onTextPropsConfigChange: config => {
                setTextPropsConfig(prev => ({
                  ...prev,
                  ...config,
                }));
              },
            });

            const element = elements && elements.length === 1 ? elements[0] : undefined;
            if (element?.custom?.type !== 'device_group' && element?.custom?.type !== 'device') {
              const isPipeNetwork = element?.custom?.type === 'pipe-network';
              const rectElement = isPipeNetwork
                ? element.children.find(child => child.type === 'rect')
                : null;

              return [
                !elements?.length && textElementsPropertiesByDeviceType,
                (element?.type === 'polyline' || element?.type === 'line' || isPipeNetwork) && (
                  <Panel key="pipe-props" header="水管属性">
                    <BlockProp.Wrapper>
                      <BlockProp.Name>水管类型</BlockProp.Name>
                      <BlockProp.Value align="left">
                        <PipeTypeSelect element={element} />
                      </BlockProp.Value>
                    </BlockProp.Wrapper>
                    {isPipeNetwork && rectElement && (
                      <BlockProp.Wrapper style={{ marginTop: 4 }}>
                        <BlockProp.Name>水管大小</BlockProp.Name>
                        <BlockProp.Value align="left">
                          <StrokeWidth element={rectElement} />
                        </BlockProp.Value>
                      </BlockProp.Wrapper>
                    )}
                  </Panel>
                ),
              ].filter(Boolean);
            }

            let deviceElement = element;
            if (element.custom.type === 'device_group') {
              deviceElement = element.children.find(el => el.custom?.type === 'device');
            }

            const customAttrs = deviceElement.custom;

            return [
              !elements?.length && textElementsPropertiesByDeviceType,

              <Panel key="device-props" header="设备属性">
                {env.__DEBUG_MODE__ && (
                  <>
                    <BlockProp.Wrapper>
                      <BlockProp.Name>ID</BlockProp.Name>
                      <BlockProp.Value>{deviceElement.id}</BlockProp.Value>
                    </BlockProp.Wrapper>
                    <BlockProp.Wrapper>
                      <BlockProp.Name>DEVICE GUID</BlockProp.Name>
                      <BlockProp.Value>{deviceElement.custom.deviceGuid}</BlockProp.Value>
                    </BlockProp.Wrapper>
                  </>
                )}
                <Divider />
                <BlockProp.Wrapper>
                  <BlockProp.Name>名称</BlockProp.Name>
                  <BlockProp.Value>{deviceElement.custom.name}</BlockProp.Value>
                </BlockProp.Wrapper>
                <Divider />
                <BlockProp.Wrapper>
                  <BlockProp.Name>设备类型</BlockProp.Name>
                  <BlockProp.Value>
                    {deviceTypeMap?.[deviceElement.custom.deviceType].metaName}
                    {env.__DEBUG_MODE__ && `(${deviceElement.custom.deviceType})`}
                  </BlockProp.Value>
                </BlockProp.Wrapper>
                {deviceElement.type === 'line' && (
                  <>
                    <Divider />
                    <BlockProp.Wrapper>
                      <BlockProp.Name>漏水绳粗细</BlockProp.Name>
                      <BlockProp.Value>
                        <InputNumber
                          min={1}
                          max={99}
                          value={deviceElement.strokeWidth}
                          onChange={value => {
                            if (value === null) {
                              return;
                            }
                            deviceElement.set({ strokeWidth: Number(value) });
                          }}
                        />
                      </BlockProp.Value>
                    </BlockProp.Wrapper>
                  </>
                )}
              </Panel>,
              <Panel key="device-points" header={<span>设备测点</span>}>
                {deviceElement.custom.points?.map(point => {
                  const pointTextElementId = relatedElementUtil.join(
                    deviceElement.custom.deviceGuid,
                    point.code
                  );

                  return (
                    <React.Fragment key={point.code}>
                      <BlockProp.Wrapper>
                        <BlockProp.Value align="left">{point.name}</BlockProp.Value>
                        <BlockProp.Name align="right">
                          <MinusCircleOutlined
                            style={{ cursor: 'pointer' }}
                            onClick={() => {
                              deviceElement.set({
                                custom: {
                                  ...deviceElement.custom,
                                  points: deviceElement.custom.points.filter(
                                    ({ code }) => code !== point.code
                                  ),
                                },
                              });
                              storeRef.current.deleteElements([pointTextElementId]);
                            }}
                          />
                        </BlockProp.Name>
                      </BlockProp.Wrapper>
                      <Divider />
                    </React.Fragment>
                  );
                })}
                <ApiSelect
                  style={{ width: '100%' }}
                  size="small"
                  showSearch
                  optionWithValue
                  value=""
                  disabledOptionValues={deviceElement.custom.points?.map(({ code }) => code)}
                  serviceQueries={[deviceElement.custom.deviceType]}
                  dataService={async deviceType => {
                    const { data, error } = await fetchPointsByCondition({
                      deviceType,
                      isQueryNon: true,
                      blockGuid: blockGuid,
                    });

                    if (error) {
                      return;
                    }
                    const result = data.data.map(point => point.toApiObject());
                    return result.map(({ code, dataType, name, pointType, unit, validLimits }) => ({
                      label: name,
                      value: code,
                      custom: {
                        code,
                        name,
                        dataType: dataType.code,
                        pointType: pointType.code,
                        unit,
                        validLimits,
                      },
                    }));
                  }}
                  onChange={(_pointCode, { custom }) => {
                    const newPointTextElementId = relatedElementUtil.join(
                      deviceElement.custom.deviceGuid,
                      _pointCode
                    );
                    // eslint-disable-next-line no-template-curly-in-string
                    const valueTemplate = '= ${value}' + (custom.unit ? custom.unit : '');
                    const textTemplate = custom.name + valueTemplate;
                    const fontSize = 25;
                    const width =
                      custom.name.length * fontSize + valueTemplate.length * (fontSize / 2);
                    const height = fontSize;
                    const stage = storeRef.current.stageRef.current;
                    const node = stage.findOne(`#${deviceElement.id}`);
                    const elementsContainer = stage.findOne('.elements-container');
                    const absolutePosition = node.getAbsolutePosition(elementsContainer);
                    const activePage = storeRef.current.activePage;
                    activePage.addElement(
                      {
                        id: newPointTextElementId,
                        type: 'text',
                        x:
                          deviceElement.type === 'line'
                            ? deviceElement.points[0]
                            : absolutePosition.x + deviceElement.width + 10,
                        y:
                          deviceElement.type === 'line'
                            ? deviceElement.points[1] - 25 - deviceElement.strokeWidth / 2
                            : absolutePosition.y +
                              ((deviceElement.custom.points || []).length - 1) * (height + 10),
                        width,
                        height,
                        text: textTemplate,
                        align: 'left',
                        fontSize,
                        rotation: 0,
                        custom: {
                          type: 'point-text',
                          point: {
                            deviceGuid: deviceElement.custom.deviceGuid,
                            ...custom,
                          },
                          textTemplate,
                        },
                      },
                      // We don't want to select the added point text element,
                      // because the user may add more point text element later.
                      false
                    );
                    deviceElement.set({
                      custom: {
                        ...deviceElement.custom,
                        visiblePointCodes: [
                          ...(deviceElement.custom.visiblePointCodes ?? []),
                          custom.code,
                        ],
                        points: [...(deviceElement.custom.points || []), custom],
                      },
                    });
                    activePage.updateRect();
                  }}
                />
              </Panel>,
              (typeofWaterLeakDetector(customAttrs.deviceType) ||
                typeofPositioningWaterLeakDetector(customAttrs.deviceType)) && (
                <Panel key="device-remarks" header="备注">
                  {customAttrs.remarkTextRowKeys?.map(remarkTextRowKey => {
                    const remarkTextElementId = relatedElementUtil.join(
                      customAttrs.deviceGuid,
                      remarkTextRowKey
                    );
                    const remarkTextElement =
                      storeRef.current.activePage.findOne(remarkTextElementId);

                    if (!remarkTextElement) {
                      const message = `[Infra-room Topology Graphix] Can not find remark text element by ID(${remarkTextElementId})`;
                      if (env.__DEV_MODE__) {
                        throw new Error(message);
                      } else {
                        console.error(message);
                      }
                    }

                    return (
                      <React.Fragment key={remarkTextRowKey}>
                        <BlockProp.Wrapper>
                          <Input.TextArea
                            defaultValue={remarkTextElement.text}
                            onChange={({ target: { value } }) => {
                              remarkTextElement.set({ text: value });
                            }}
                          />
                          <MinusCircleOutlined
                            style={{ cursor: 'pointer' }}
                            onClick={() => {
                              deviceElement.set({
                                custom: {
                                  ...customAttrs,
                                  remarkTextRowKeys: customAttrs.remarkTextRowKeys.filter(
                                    rowKey => rowKey !== remarkTextRowKey
                                  ),
                                },
                              });
                              storeRef.current.deleteElements([remarkTextElementId]);
                            }}
                          />
                        </BlockProp.Wrapper>
                        <Divider />
                      </React.Fragment>
                    );
                  })}
                  <BlockProp.Wrapper>
                    <PlusCircleOutlined
                      style={{ cursor: 'pointer' }}
                      onClick={() => {
                        const newRemarkTextElementId = relatedElementUtil.generate(
                          customAttrs.deviceGuid
                        );
                        const [, newRemarkTextElementIdRowKey] =
                          relatedElementUtil.split(newRemarkTextElementId);
                        const stage = storeRef.current.stageRef.current;
                        const node = stage.findOne(`#${deviceElement.id}`);
                        const elementsContainer = stage.findOne('.elements-container');
                        const absolutePosition = node.getAbsolutePosition(elementsContainer);
                        const activePage = storeRef.current.activePage;
                        activePage.addElement(
                          {
                            id: newRemarkTextElementId,
                            type: 'text',
                            x:
                              deviceElement.type === 'line'
                                ? deviceElement.points[0]
                                : absolutePosition.x - 100 - 10,
                            y:
                              deviceElement.type === 'line'
                                ? deviceElement.points[1] - 25 - deviceElement.strokeWidth / 2
                                : absolutePosition.y + deviceElement.height / 2,
                            width: 100,
                            height: 25,
                            text: '',
                            fontSize: 25,
                            rotation: 0,
                            custom: {
                              type: 'remark-text',
                            },
                          },
                          false
                        );
                        deviceElement.set({
                          custom: {
                            ...customAttrs,
                            remarkTextRowKeys: [
                              ...(customAttrs.remarkTextRowKeys || []),
                              newRemarkTextElementIdRowKey,
                            ],
                          },
                        });
                        activePage.updateRect();
                      }}
                    />
                  </BlockProp.Wrapper>
                </Panel>
              ),
            ].filter(Boolean);
          }}
          onElementAttrsChange={(element, changedAttrs) => {
            if (element.custom?.type === 'point-text' && changedAttrs.text) {
              element.set({ custom: { ...element.custom, textTemplate: changedAttrs.text } });
            }
          }}
          onGroupEnd={groupElem => {
            if (isGenericTopology) {
              if (isGroupingDevice(groupElem)) {
                groupElem.set({
                  custom: { type: 'device_group' },
                  name: findDeviceInGroup(groupElem).custom.name,
                });
                return;
              }
              setGroupElement(groupElem);
              setGroupNamingModalVisible(true);
            }
            setSelectedDeviceType(undefined);
          }}
          onUngroupEnd={() => {
            setSelectedDeviceType(undefined);
          }}
          onDrop={elementConfig => {
            const deviceElementConfig = elementConfig.children?.find(
              child => child.custom.type === 'device'
            );
            if (
              deviceElementConfig &&
              (typeofWaterLeakDetector(deviceElementConfig.custom.deviceType) ||
                typeofPositioningWaterLeakDetector(deviceElementConfig.custom.deviceType))
            ) {
              store.activePage?.setBasePenDrawingAttrs({
                id: deviceElementConfig.id,
                strokeWidth: 10,
                dash: [20, 40],
                background: 'yellow',
                foreground: 'black',
                custom: omit(deviceElementConfig.custom, ['remarkTextRowKeys']),
              });
              store.on('line:added', (_line, tool) => {
                if (tool === 'pen') {
                  store.activePage?.setBasePenDrawingAttrs(null);
                }
                store.off('line:added');
              });
              store.usePenTool();

              return false;
            }

            return true;
          }}
          onElementAdded={() => {
            setSelectedDeviceType(undefined);
          }}
          onDeleteEnd={deletedElemenetsJSON => {
            let hasBackImg;
            const pendingDeleteIds = [];
            deletedElemenetsJSON.forEach(el => {
              const elementType = el.custom?.type;
              hasBackImg = elementType === BACKGROUD_IMAGE_TYPE;
              if (['device', 'device_group'].includes(elementType)) {
                const deviceElement =
                  elementType === 'device_group'
                    ? el.children.find(child => child.custom?.type === 'device')
                    : el;
                const { deviceGuid, points, remarkTextRowKeys } = deviceElement.custom;
                const pointTextElementIds = points?.map(({ code }) =>
                  relatedElementUtil.join(deviceGuid, code)
                );
                if ((pointTextElementIds?.length || 0) > 0) {
                  pendingDeleteIds.push(...pointTextElementIds);
                }
                const remarkTextElementIds = remarkTextRowKeys?.map(key =>
                  relatedElementUtil.join(deviceGuid, key)
                );
                if ((remarkTextElementIds?.length || 0) > 0) {
                  pendingDeleteIds.push(...remarkTextElementIds);
                }
              } else if (['point-text'].includes(elementType)) {
                const [hostId, childKey] = relatedElementUtil.split(el.id);
                const hostElement = storeRef.current.activePage.findOne(hostId);
                if (hostElement) {
                  hostElement.set({
                    custom: {
                      ...hostElement.custom,
                      points: hostElement.custom.points.filter(({ code: key }) => key !== childKey),
                    },
                  });
                }
              } else if (['remark-text'].includes(elementType)) {
                const [hostId, childKey] = relatedElementUtil.split(el.id);
                const hostElement = storeRef.current.activePage.findOne(hostId);
                if (hostElement) {
                  hostElement.set({
                    custom: {
                      ...hostElement.custom,
                      remarkTextRowKeys: hostElement.custom.remarkTextRowKeys.filter(
                        key => key !== childKey
                      ),
                    },
                  });
                }
              }
            });
            if (hasBackImg) {
              setHasBackgroupdImage(false);
            }
            if (pendingDeleteIds.length > 0) {
              storeRef.current.deleteElements(pendingDeleteIds);
            }
            setSelectedDeviceType(undefined);
          }}
          onAutoFixes={store => {
            store.pages.forEach(page => {
              traverse(page.children, element => {
                if (element.custom?.type === 'device') {
                  element.set({ fill: ThemeColors.ContainerBg });
                } else if (element.custom?.type === 'device_text') {
                  element.set({ fill: ThemeColors.TextColor, stroke: ThemeColors.TextColor });
                }
              });
            });
          }}
          onSave={json => {
            saveGraph({ idc, block, room, json, mode, topologyType });
          }}
          onQuitClick={() => {
            clearGraph();

            store.selectElements([]);
            store.deletePages(store.pages.map(page => page.id));

            redirect(urls.ROOM_LIST);
          }}
        />
        <Modal
          title="新建视图"
          closable={false}
          footer={null}
          centered
          open={isGenericTopology && !hasBackgroundImage}
        >
          <Space align="start">
            <BackgroundImageUpload
              accept="image/*"
              listType="picture-card"
              showUploadList={false}
              beforeUpload={file => {
                addBackgroundImageAsync(file);

                return false;
              }}
            >
              <div>
                {importingBackgroundImage ? (
                  <LoadingOutlined style={{ fontSize: 60 }} />
                ) : (
                  <ImportOutlined style={{ fontSize: 60 }} />
                )}

                <div>导入底图</div>
              </div>
            </BackgroundImageUpload>
            <span>
              <Typography.Title level={4}>底图</Typography.Title>
              <Typography.Paragraph>底图是包间俯瞰图，可通过导入自动识别</Typography.Paragraph>
            </span>
          </Space>
        </Modal>
        <GroupNamingModal
          visible={groupNamingModalVisible}
          onOk={(name, addText) => {
            if (!groupElement) {
              return;
            }
            const attrs = { name };
            if (addText) {
              attrs.custom = { type: 'devices_group' };
              store.activePage.addElement({
                type: 'text',
                text: name,
                x: groupElement.x,
                y: groupElement.y - 14,
                width: groupElement.width,
              });
            }
            groupElement.set(attrs);
            setGroupElement(null);
            setGroupNamingModalVisible(false);
          }}
          onCancel={() => {
            setGroupElement(null);
            setGroupNamingModalVisible(false);
          }}
        />
      </div>
    </ThemeCompositions>
  );
}

const mapStateToProps = ({
  infraRoomGraphix: { isReady, devicesTree, graph },
  common: { deviceCategory },
}) => {
  baseResourceCategories[0].children = devicesTree;

  return {
    isReady,
    resourceCategories: baseResourceCategories,
    graph,
    deviceTypeMap: deviceCategory?.normalizedList,
  };
};
const mapDispatchToProps = {
  initialize: initializeActionCreator,
  saveGraph: saveGraphActionCreator,
  clearGraph: infraRoomGraphixActions.clearGraph,
  syncCommonData: syncCommonDataAction,
  redirect: redirectAction,
};

export default connect(mapStateToProps, mapDispatchToProps)(InfraRoomGraphix);

const BackgroundImageUpload = styled(Upload)`
  width: auto;

  > .${prefixCls}-upload {
    width: 128px;
    height: 128px;
  }
`;

const baseResourceCategories = [
  {
    key: 'devices',
    icon: <BookOutlined />,
    label: '设备',
    children: [],
  },
  {
    key: 'others',
    icon: <EllipsisOutlined />,
    label: '其他',
    children: [
      {
        key: 'direction-indicator',
        copyMax: Number.POSITIVE_INFINITY,
        type: 'arrow',
        label: '方向指示器',
        img: arrowSvg,
        isLeaf: true,
        elementConfig: {
          type: 'line',
          name: 'direction-indicator',
          showArrows: true,
          draggable: true,
          width: 32,
          height: 0,
          points: [0, 0, 32, 0],
          stroke: 'white',
          strokeWidth: 1,
        },
      },
      {
        key: 'plain-txet',
        copyMax: Number.POSITIVE_INFINITY,
        type: 'text',
        label: '文本',
        img: textSvg,
        isLeaf: true,
        elementConfig: {
          type: 'text',
          name: 'plain-txet',
          width: 100,
          height: 25,
          fontSize: 25,
          text: 'Text',
          fill: 'white',
        },
      },
      {
        key: 'pipe-network',
        copyMax: Number.POSITIVE_INFINITY,
        type: 'pipe-network',
        label: '环网',
        img: '/images/topology-graphix/nodes/pipe-network.png',
        isLeaf: true,
        elementConfig: {
          custom: {
            type: 'pipe-network',
            name: '环网',
          },
          type: 'group',
          name: '环网',
          canUngroup: false,
          allowAddAnchorPoints: true,
          width: 400,
          height: 600,
          anchorPointsConfig: [],
          anchorPointsPlacementConfig: [],
          children: [
            {
              custom: {
                type: 'pipe-network_rect',
              },
              type: 'rect',
              x: 0,
              y: 0,
              width: 400,
              height: 600,
              strokeWidth: 12,
              stroke: 'white',
              locked: true,
            },
          ],
        },
      },
    ],
  },
];

function isGroupingDevice(groupElement) {
  if (groupElement.children.length !== 2) {
    return false;
  }

  const [child1, child2] = groupElement.children;
  const child1Type = child1.custom?.type;
  const child2Type = child2.custom?.type;

  return (
    (child1Type === 'device' && child2Type === 'device_text') ||
    (child2Type === 'device' && child1Type === 'device_text')
  );
}

function findDeviceInGroup(groupElement) {
  return groupElement.children.find(el => el.custom?.type === 'device');
}

function initializePageConfig(page, topologyType) {
  if (topologyType === 'LIQUID_MONITORING') {
    page.setBasePolylineAttrs({
      // showArrows: true,
      strokeWidth: 12,
    });
  }
}
