import React from 'react';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Input } from '@manyun/base-ui.ui.input';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';

export default function GroupNamingModal({ visible, onOk, onCancel }) {
  const [groupName, setGroupName] = React.useState();
  const [addText, setAddText] = React.useState(true);

  const placeholder = '编组';

  return (
    <Modal
      destroyOnClose
      title="命名编组"
      open={visible}
      okButtonProps={{ disabled: !groupName }}
      onOk={() => {
        onOk(groupName, addText);
        setGroupName('');
        setAddText(true);
      }}
      onCancel={onCancel}
    >
      <Space style={{ width: '100%' }} direction="vertical">
        <Alert
          showIcon
          type="info"
          message="如果是将多个设备编组，请给编组命名。可以在预览时点击编组名称来查看编组的设备视图。"
        />
        <Input
          maxLength={10}
          placeholder={placeholder}
          onChange={({ target: { value } }) => {
            setGroupName(value);
          }}
        />
        <Checkbox
          checked={addText}
          onChange={({ target: { checked } }) => {
            setAddText(checked);
          }}
        >
          自动添加编组名称到图中
        </Checkbox>
      </Space>
    </Modal>
  );
}
