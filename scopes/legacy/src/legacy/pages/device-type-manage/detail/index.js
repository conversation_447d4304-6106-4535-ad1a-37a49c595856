import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import {
  GutterWrapper,
  TinyCard,
  TinyDescriptions,
  TinyTable,
} from '@manyun/dc-brain.legacy.components';
import {
  deviceTypeActions,
  fetchDeviceTypeDetail,
} from '@manyun/dc-brain.legacy.redux/actions/deviceTypeActions';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';

const getColumns = ctx => [
  {
    title: '规格名称',
    dataIndex: 'specName',
  },
  {
    title: '单位',
    dataIndex: 'specUnit',
  },
  {
    title: '是否必填',
    dataIndex: 'required',
    render: required => <>{required ? '是' : '否'}</>,
  },
  {
    title: '更新人',
    dataIndex: 'operatorName',
  },
  {
    title: '更新时间',
    dataIndex: 'gmtModified',
  },
];

class DeviceTypeDetail extends Component {
  componentDidMount() {
    const { match } = this.props;
    const id = match.params.id;
    this.props.fetchDeviceTypeDetail({ params: { id } });
  }

  render() {
    const { deviceTypeDetail } = this.props;
    return (
      <GutterWrapper mode="vertical">
        <TinyCard title="基本信息">
          <TinyDescriptions
            column={5}
            descriptionsItems={[
              {
                label: '类型名称',
                value: deviceTypeDetail.name,
              },
              {
                label: '类型编号',
                value: deviceTypeDetail.code,
              },
              {
                label: '包含测点数',
                value: deviceTypeDetail.pointNum ? (
                  <Link
                    to={urls.generatePointUrl({
                      deviceType: deviceTypeDetail.code,
                      deviceName: deviceTypeDetail.name,
                    })}
                  >
                    {deviceTypeDetail.pointNum}
                  </Link>
                ) : (
                  0
                ),
              },
              {
                label: '包含设备数',
                value: deviceTypeDetail.deviceNum ? (
                  <Link
                    to={urls.generateDeviceListUrl({
                      deviceType: deviceTypeDetail.code,
                    })}
                  >
                    {deviceTypeDetail.deviceNum}
                  </Link>
                ) : (
                  0
                ),
              },
              {
                label: '创建人',
                value: deviceTypeDetail.operatorName,
              },
              {
                label: '创建时间',
                value: deviceTypeDetail.gmtCreate,
              },
              {
                label: '更新人',
                value: deviceTypeDetail.operatorName,
              },
              {
                label: '更新时间',
                value: deviceTypeDetail.gmtModified,
              },
              {
                label: '备注',
                value: deviceTypeDetail.description,
              },
            ]}
          />
        </TinyCard>
        <TinyCard title="规格信息">
          <TinyTable
            rowKey="id"
            columns={getColumns(this)}
            dataSource={deviceTypeDetail.specInfos}
          />
        </TinyCard>
      </GutterWrapper>
    );
  }
}

const mapStateToProps = ({ deviceTypeManage: { deviceTypeDetail } }) => ({
  deviceTypeDetail,
});
const mapDispatchToProps = {
  deviceTypeActions,
  fetchDeviceTypeDetail,
};

export default connect(mapStateToProps, mapDispatchToProps)(DeviceTypeDetail);
