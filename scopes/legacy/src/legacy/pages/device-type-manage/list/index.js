import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import { FiltersForm } from '@galiojs/awesome-antd';
import trim from 'lodash/trim';

import { Button } from '@manyun/base-ui.ui.button';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Input } from '@manyun/base-ui.ui.input';

// Deprecated, replace with "useAuthorized" hook
import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';

import { GutterWrapper, TinyCard, TinyTable } from '@manyun/dc-brain.legacy.components';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import {
  deleteDeviceTypeAction,
  deviceTypeActions,
  fetchDeviceTypeListPage,
} from '@manyun/dc-brain.legacy.redux/actions/deviceTypeActions';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';

import AddEditType from '../components/add-edit-type';

const getColumns = ctx => [
  {
    title: '类型编号',
    dataIndex: 'code',
  },
  {
    title: '类型名称',
    dataIndex: 'name',
  },
  {
    title: '包含规格数',
    dataIndex: 'specNum',
  },
  {
    title: '包含测点数',
    dataIndex: 'pointNum',
  },
  {
    title: '包含设备数',
    dataIndex: 'deviceNum',
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
  },
  {
    title: '更新时间',
    dataIndex: 'gmtModified',
  },
  {
    title: '更新人',
    dataIndex: 'operatorName',
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    render: (__, record) => {
      return (
        <>
          <Link to={urls.generateDeviceTypeInfoUrl({ id: record.id })}>
            <Button compact type="link">
              查看
            </Button>
          </Link>
          {ctx.updateAuthorized && (
            <>
              <Divider type="vertical" />
              <AddEditType mode="edit" text="编辑" title="编辑设备类型" type="link" info={record} />
            </>
          )}
          {ctx.deleteAuthorized && (
            <>
              <Divider type="vertical" />
              <DeleteConfirm
                maxReasonLength={50}
                targetName={record.code + '的设备类型'}
                reasonPlaceholder={'请输入删除' + record.code + '的原因'}
                onOk={({ reason }) => ctx.delete(record, reason)}
              >
                <Button compact type="link">
                  删除
                </Button>
              </DeleteConfirm>
            </>
          )}
        </>
      );
    },
  },
];

class DeviceTypeList extends Component {
  state = {
    pageNo: 1,
    pageSize: 10,
    fieldsValue: {},
  };

  componentDidMount() {
    this.props.syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });
    const { deviceTypePageCondition } = this.props;
    this.props.fetchDeviceTypeListPage({
      ...deviceTypePageCondition,
    });
  }

  onChangePage = (pageNo, pageSize) => {
    const { deviceTypePageCondition } = this.props;
    this.props.fetchDeviceTypeListPage({
      ...deviceTypePageCondition,
      pageNum: pageNo,
      pageSize,
    });
  };

  searchDeviceTypeList = fieldsValue => {
    this.props.fetchDeviceTypeListPage({
      deviceType: trim(fieldsValue?.deviceType),
      deviceTypeName: trim(fieldsValue?.deviceTypeName),
      pageNum: 1,
      pageSize: 10,
    });
  };

  handleReset = () => {
    this.props.fetchDeviceTypeListPage({
      pageNum: 1,
      pageSize: 10,
    });
  };

  delete = ({ id }, operatorNotes) => {
    const { deleteDeviceTypeAction, fetchDeviceTypeListPage, deviceTypePageCondition } = this.props;

    return new Promise(resolve => {
      deleteDeviceTypeAction({
        params: { id, operatorNotes },
        successCb: () => {
          resolve(true);
          fetchDeviceTypeListPage({ ...deviceTypePageCondition });
        },
        errorCb: () => {
          resolve(false);
        },
      });
    });
  };

  render() {
    const { deviceTypePage, loading, deviceTypePageCondition, addAuthorized } = this.props;

    return (
      <GutterWrapper mode="vertical">
        <TinyCard>
          <FiltersForm
            items={[
              {
                label: '类型编号',
                id: 'deviceType',
                control: <Input whitespace="trim" allowClear />,
              },
              { label: '类型名称', id: 'deviceTypeName', control: <Input allowClear /> },
            ]}
            onSearch={this.searchDeviceTypeList}
            onReset={this.handleReset}
          />
        </TinyCard>
        <TinyCard>
          <TinyTable
            rowKey="id"
            columns={getColumns(this)}
            align={'left'}
            dataSource={deviceTypePage.list}
            loading={loading}
            actions={
              addAuthorized ? <AddEditType text="新建" title="新建设备类型" type="primary" /> : []
            }
            pagination={{
              total: deviceTypePage.total,
              current: deviceTypePageCondition.pageNum,
              pageSize: deviceTypePageCondition.pageSize,
              onChange: this.onChangePage,
            }}
          />
        </TinyCard>
      </GutterWrapper>
    );
  }
}

const mapStateToProps = ({
  deviceTypeManage: {
    loading,
    deviceTypePage,
    searchValues,
    deviceTypePageCondition,
    deviceTypeDetail,
  },
}) => ({
  loading,
  deviceTypePage,
  deviceTypePageCondition,
  deviceTypeDetail,
  searchValues,
});
const mapDispatchToProps = {
  deviceTypeActions,
  fetchDeviceTypeListPage,
  deleteDeviceTypeAction,
  syncCommonData: syncCommonDataActionCreator,
  updateSearchValues: deviceTypeActions.updateSearchValues,
};

const ConnectedCom = connect(mapStateToProps, mapDispatchToProps)(DeviceTypeList);

function DefaultCom() {
  const [updateAuthorized] = useAuthorized({ checkByCode: 'ele_device_type_update' });
  const [deleteAuthorized] = useAuthorized({ checkByCode: 'ele_device_type_delete' });
  const [addAuthorized] = useAuthorized({ checkByCode: 'ele_device_type_add' });

  return (
    <ConnectedCom
      updateAuthorized={updateAuthorized}
      deleteAuthorized={deleteAuthorized}
      addAuthorized={addAuthorized}
    />
  );
}

export default DefaultCom;
