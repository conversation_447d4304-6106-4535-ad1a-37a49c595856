import React, { Component } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import QuestionCircleOutlined from '@ant-design/icons/es/icons/QuestionCircleOutlined';
import get from 'lodash/get';
import split from 'lodash/split';

import { Button } from '@manyun/base-ui.ui.button';
import { Cascader } from '@manyun/base-ui.ui.cascader';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';

import { TinyDrawer } from '@manyun/dc-brain.legacy.components';
import { DEVICE_TYPE_META_TYPE_KEY_MAP } from '@manyun/dc-brain.legacy.constants/enums/deviceType';
import { fetchDeviceTypeListPage } from '@manyun/dc-brain.legacy.redux/actions/deviceTypeActions';
import * as deviceTypeService from '@manyun/dc-brain.legacy.services/deviceTypeService';
import { generateTreeData } from '@manyun/dc-brain.legacy.utils/index';

class AddEditType extends Component {
  state = {
    loading: false,
  };

  changeVisible = async () => {
    this.setState({
      createVisible: !this.state.createVisible,
    });
  };

  handleSubmit = () => {
    const { deviceTypePageCondition, info, form } = this.props;
    form.validateFields(async err => {
      if (err) {
        return;
      }
      const data = form.getFieldsValue();
      if (info) {
        data.id = info.id;
        data.code = data.parentCode[1] + data.code;
        const { error } = await deviceTypeService.editDeviceType(data);
        if (error) {
          message.error(error);
          return;
        }
        message.success('编辑设备类型成功');
        this.changeVisible();
        this.props.fetchDeviceTypeListPage(deviceTypePageCondition);
        return;
      }
      data.type = DEVICE_TYPE_META_TYPE_KEY_MAP.C2;
      data.parentType = DEVICE_TYPE_META_TYPE_KEY_MAP.C1;
      data.parentCode = data.parentCode[1];
      data.code = data.parentCode + data.code;
      const { error } = await deviceTypeService.addDeviceType(data);
      if (error) {
        message.error(error);
        return;
      }
      this.changeVisible();
      this.props.fetchDeviceTypeListPage(deviceTypePageCondition);
      message.success('添加设备类型成功');
    });
  };

  render() {
    const { form, text, type, title, info = {}, treeData, normalizedList } = this.props;
    const { createVisible } = this.state;
    const { getFieldDecorator, getFieldValue } = form;

    return [
      <Button key="btn" type={type} onClick={this.changeVisible}>
        {text}
      </Button>,
      <TinyDrawer
        width={416}
        title={<Typography.Title level={4}>{title}</Typography.Title>}
        visible={createVisible}
        onClose={this.changeVisible}
        onSubmit={this.handleSubmit}
        onCancel={this.changeVisible}
        key="drawer"
        destroyOnClose
      >
        <Form colon={false}>
          <Form.Item label="类型名称">
            {getFieldDecorator('name', {
              rules: [
                { required: true, message: '类型名称必填！' },
                {
                  max: 10,
                  message: '最多输入 10 个字符！',
                },
              ],
              initialValue: info.name,
            })(<Input />)}
          </Form.Item>
          <Form.Item label="上级类型">
            {getFieldDecorator('parentCode', {
              rules: [
                {
                  required: true,
                  message: '上级类型请选择至二级设备类型，只支持新建三级设备类型',
                  type: 'number',
                  transform: value => (value?.length === 2 ? 2 : false),
                },
              ],
              initialValue:
                normalizedList && text === '编辑'
                  ? getDeviceTypeNameList(info?.parentCode, normalizedList)
                  : [],
            })(
              <Cascader
                fieldNames={{ label: 'metaName', value: 'metaCode', children: 'children' }}
                options={treeData}
                disabled={!!info.name}
                changeOnSelect
                notFoundContent={<Spin size="small" />}
              />
            )}
          </Form.Item>
          <Form.Item
            label={
              <Tooltip title="为了保持设备类型编码唯一，当前新建设备类型时系统会自动编码前缀">
                类型编号&nbsp;&nbsp;
                <QuestionCircleOutlined />
              </Tooltip>
            }
          >
            {getFieldDecorator('code', {
              rules: [
                { required: true, message: '类型编号必填！' },
                {
                  pattern: /^\d{2}$/,
                  message: '选择上级类型后补充两位数字编号',
                },
              ],
              initialValue: info?.code?.slice(-2),
            })(
              <Input disabled={!!info.name} addonBefore={get(getFieldValue('parentCode'), 1, '')} />
            )}
          </Form.Item>
          <Form.Item label="备注">
            {getFieldDecorator('description', {
              initialValue: info.description,
              rules: [
                {
                  max: 50,
                  message: '最多输入 50 个字符！',
                },
              ],
            })(<Input.TextArea autoSize={{ minRows: 3 }} />)}
          </Form.Item>
        </Form>
      </TinyDrawer>,
    ];
  }
}
const mapStateToProps = ({
  deviceTypeManage: { createVisible, deviceTypePageCondition },
  common: { deviceCategory },
}) => {
  const flatData = get(deviceCategory, 'parallelList', []);
  const filterData = [];
  flatData.forEach(item =>
    item.metaType !== DEVICE_TYPE_META_TYPE_KEY_MAP.C2 ? filterData.push(item) : null
  );
  const treeData = generateTreeData(filterData, {
    key: ({ metaType, metaCode }) => `${metaType}${metaCode}`,
    typeKey: 'metaType',
    parentKey: 'parentCode',
    nodeTypes: [DEVICE_TYPE_META_TYPE_KEY_MAP.C0, DEVICE_TYPE_META_TYPE_KEY_MAP.C1],
    getNode: node => {
      return {
        ...node,
        label: node.metaName,
        value: node.metaCode,
      };
    },
  });
  return {
    createVisible,
    deviceTypePageCondition,
    treeData,
    normalizedList: deviceCategory?.normalizedList,
  };
};
const mapDispatchToProps = {
  fetchDeviceTypeListPage,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(Form.create({ name: 'type_add_edit' })(AddEditType));

function getDeviceTypeNameList(code, normalizedList) {
  const label2 = split(code, DEVICE_TYPE_META_TYPE_KEY_MAP.C1)[1];
  const top = normalizedList[label2]?.parentCode;
  const label1 = split(top, DEVICE_TYPE_META_TYPE_KEY_MAP.C0)[1];
  return [label1, label2];
}
