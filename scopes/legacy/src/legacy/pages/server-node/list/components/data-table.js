import React, { useCallback, useEffect } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import { Input } from '@manyun/base-ui.ui.input';

import { Ellipsis, ModalButton, TinyTable } from '@manyun/dc-brain.legacy.components';
import {
  resetSearchValuesActionCreator,
  serverNodeActionCreator,
  serverNodeActions,
  setPaginationThenGetDataActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/serverNodeActions';
import * as urlsUtil from '@manyun/dc-brain.legacy.utils/urls';

import { ConfigTable } from './config-table';

export function DataTable({
  data,
  total,
  pageNum,
  pageSize,
  loading,
  getData,
  setPagination,
  updateSearchValues,
}) {
  useEffect(() => {
    getData();
  }, [getData]);

  const paginationChangeHandler = useCallback(
    (current, size) => {
      setPagination({ pageNum: current, pageSize: size });
    },
    [setPagination]
  );

  const onChangeValuesAndGetData = value => {
    updateSearchValues({ condition: value });
    getData();
  };

  return (
    <TinyTable
      rowKey="guid"
      align={'left'}
      columns={columns}
      loading={loading}
      dataSource={data}
      pagination={{
        total,
        current: pageNum,
        pageSize,
        onChange: paginationChangeHandler,
      }}
      actions={
        <Input.Search
          key="seach"
          allowClear
          placeholder="输入服务器节点名称/IP"
          style={{ width: 211, height: 32 }}
          onSearch={onChangeValuesAndGetData}
        />
      }
    />
  );
}

const mapStateToProps = ({
  serverNode: {
    data,
    total,
    pagination: { pageNum, pageSize },
    loading,
    searchValues,
  },
}) => ({
  data,
  total,
  loading,
  pageNum,
  pageSize,
  searchValues,
});
const mapDispatchToProps = {
  getData: serverNodeActionCreator,
  setPagination: setPaginationThenGetDataActionCreator,
  resetSearchValues: resetSearchValuesActionCreator,
  updateSearchValues: serverNodeActions.updateSearchValues,
};

export default connect(mapStateToProps, mapDispatchToProps)(DataTable);
const columns = [
  {
    title: '服务器节点名称',
    dataIndex: 'nodeName',
    render: (nodeName, record) => (
      <Ellipsis lines={1} tooltip>
        <Link type="link" to={urlsUtil.generateServerNodeUrl({ id: record.id })}>
          {nodeName}
        </Link>
      </Ellipsis>
    ),
  },
  {
    title: '服务器节点IP',
    dataIndex: 'nodeIp',
  },
  {
    title: '配置信息',
    dataIndex: 'versionConfig',
    render(_, { guid }) {
      return (
        <ModalButton
          bodyStyle={{ height: '450px', overflow: 'hidden', overflowY: 'scroll' }}
          type="link"
          text="查看"
          title="配置信息"
          compact
          footer={null}
        >
          <ConfigTable guid={guid} />
        </ModalButton>
      );
    },
  },
  {
    title: '上次活跃时间',
    dataIndex: 'activeTime',
  },
  {
    title: '节点配置状态',
    dataIndex: ['configStatus', 'name'],
  },
];
