import React, { useEffect, useState } from 'react';

import { Table } from '@manyun/base-ui.ui.table';

import { fetchSystemNodeConfigs } from '@manyun/dc-brain.service.fetch-system-node-configs';

import { VERSION_TYPE_TEXT_KEY_MAP } from '../constants';

export const ConfigTable = ({ guid }) => {
  const [configLoading, setConfigLoading] = useState(false);
  const [configPagination, setConfigPagination] = useState({ pageNum: 1, pageSize: 10, total: 0 });
  const [configData, setConfigData] = useState([]);
  const handleSearch = React.useCallback((nodeGuid, pageNum, pageSize, type) => {
    setConfigPagination(pre => ({ ...pre, pageNum: pageNum || 1, pageSize: pageSize || 10 }));
    fetchChannelOrConfigData(nodeGuid, pageNum, pageSize, type);
  }, []);
  const pageHandler = React.useCallback(
    (nodeGuid, _page, _pageSize, type) => {
      handleSearch(nodeGuid, _page, _pageSize, type);
    },
    [handleSearch]
  );
  const fetchChannelOrConfigData = async (nodeGuid, pageNum, pageSize, type) => {
    const { data } = await fetchSystemNodeConfigs({
      nodeGuid: nodeGuid,
      pageNum: pageNum,
      pageSize: pageSize,
    });
    if (data) {
      setConfigData(data.data);
      setConfigLoading(false);
      setConfigPagination(pre => ({ ...pre, total: data.total }));
    }
  };
  useEffect(() => {
    setConfigLoading(true);
    const fetchData = async guid => {
      const { data } = await fetchSystemNodeConfigs({
        nodeGuid: guid,
        pageNum: configPagination.pageNum,
        pageSize: configPagination.pageSize,
      });
      if (data) {
        setConfigData(data.data);
        setConfigLoading(false);
        setConfigPagination(pre => ({ ...pre, total: data.total }));
      }
    };
    fetchData(guid);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [guid]);
  return (
    <Table
      loading={configLoading}
      rowKey="id"
      scroll={{ x: 'max-content' }}
      columns={configColumns}
      dataSource={configData}
      pagination={configPagination}
      onChange={pagination => {
        pageHandler(guid, pagination.current, pagination.pageSize, 'config');
      }}
    />
  );
};
const configColumns = [
  {
    title: '类型',
    dataIndex: 'type',
    render(type) {
      return VERSION_TYPE_TEXT_KEY_MAP[type];
    },
  },
  {
    title: '位置',
    dataIndex: 'range',
  },
  {
    title: '版本',
    dataIndex: 'version',
  },
  {
    title: '状态',
    dataIndex: 'status',
    render(status) {
      if (status === 'SYNCED') {
        return '已同步';
      }
      return '同步中';
    },
  },
];
