import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { useParams } from 'react-router-dom';

import { get } from 'lodash';

import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Table } from '@manyun/base-ui.ui.table';

import { fetchSystemNodeChannels } from '@manyun/dc-brain.service.fetch-system-node-channels';
import { fetchSystemNodeConfigs } from '@manyun/dc-brain.service.fetch-system-node-configs';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';
import { TinyCard } from '@manyun/dc-brain.legacy.components/tiny-card';
import { serverNodeService } from '@manyun/dc-brain.legacy.services';

import { VERSION_TYPE_TEXT_KEY_MAP } from '../list/constants';

function ServerNodeDetail() {
  const [nodeInfo, setNodeInfo] = useState(null);
  const [channelLoading, setChannelLoading] = useState(false);
  const [channelPagination, setChannelPagination] = useState({
    pageNum: 1,
    pageSize: 10,
    total: 0,
  });
  const [channelData, setChannelData] = useState([]);
  const [configLoading, setConfigLoading] = useState(false);
  const [configPagination, setConfigPagination] = useState({ pageNum: 1, pageSize: 10, total: 0 });
  const [configData, setConfigData] = useState([]);

  const { id } = useParams();
  const handleSearch = React.useCallback((nodeGuid, pageNum, pageSize, type) => {
    if (type === 'channel') {
      setChannelPagination(pre => ({ ...pre, pageNum: pageNum || 1, pageSize: pageSize || 10 }));
      fetchChannelOrConfigData(nodeGuid, pageNum, pageSize, type);
    } else {
      setConfigPagination(pre => ({ ...pre, pageNum: pageNum || 1, pageSize: pageSize || 10 }));
      fetchChannelOrConfigData(nodeGuid, pageNum, pageSize, type);
    }
  }, []);

  const pageHandler = React.useCallback(
    (nodeGuid, _page, _pageSize, type) => {
      handleSearch(nodeGuid, _page, _pageSize, type);
    },
    [handleSearch]
  );
  const fetchChannelOrConfigData = async (nodeGuid, pageNum, pageSize, type) => {
    if (type === 'channel') {
      const { data } = await fetchSystemNodeChannels({
        nodeGuid: nodeGuid,
        pageNum: pageNum,
        pageSize: pageSize,
      });
      if (data) {
        setChannelData(data.data);
        setChannelLoading(false);
        setChannelPagination(pre => ({ ...pre, total: data.total }));
      }
    } else {
      const { data } = await fetchSystemNodeConfigs({
        nodeGuid: nodeGuid,
        pageNum: pageNum,
        pageSize: pageSize,
      });
      if (data) {
        setConfigData(data.data);
        setConfigLoading(false);
        setConfigPagination(pre => ({ ...pre, total: data.total }));
      }
    }
  };
  useEffect(() => {
    setChannelLoading(true);
    setConfigLoading(true);
    (async () => {
      const { response } = await serverNodeService.fetchServerNodeDetail(id);
      if (response) {
        setNodeInfo(response);
        (async () => {
          const { data } = await fetchSystemNodeConfigs({
            nodeGuid: response.guid,
            pageNum: configPagination.pageNum,
            pageSize: configPagination.pageSize,
          });
          if (data) {
            setConfigData(data.data);
            setConfigLoading(false);
            setConfigPagination(pre => ({ ...pre, total: data.total }));
          }
        })();
        (async () => {
          const { data } = await fetchSystemNodeChannels({
            nodeGuid: response.guid,
            pageNum: channelPagination.pageNum,
            pageSize: channelPagination.pageSize,
          });
          if (data) {
            setChannelData(data.data);
            setChannelLoading(false);
            setChannelPagination(pre => ({ ...pre, total: data.total }));
          }
        })();
      }
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id]);

  return (
    <GutterWrapper mode="vertical">
      <TinyCard title="基本信息">
        <Descriptions column={1}>
          <Descriptions.Item label="服务器节点名称">{get(nodeInfo, 'nodeName')}</Descriptions.Item>
        </Descriptions>
        <Descriptions column={1}>
          <Descriptions.Item label="服务器节点IP">{get(nodeInfo, 'nodeIp')}</Descriptions.Item>
        </Descriptions>
        <Descriptions column={1}>
          <Descriptions.Item label="节点配置状态">
            {get(nodeInfo, 'configStatus.name')}
          </Descriptions.Item>
        </Descriptions>
        <Descriptions column={1}>
          <Descriptions.Item label="上次活跃时间">{get(nodeInfo, 'activeTime')}</Descriptions.Item>
        </Descriptions>
      </TinyCard>
      <TinyCard title="配置信息">
        <Table
          loading={configLoading}
          rowKey="id"
          scroll={{ x: 'max-content' }}
          columns={configColumns}
          dataSource={configData}
          pagination={configPagination}
          onChange={pagination => {
            pageHandler(nodeInfo.guid, pagination.current, pagination.pageSize, 'config');
          }}
        />
      </TinyCard>
      <TinyCard title="通道信息">
        <Table
          loading={channelLoading}
          rowKey="id"
          scroll={{ x: 'max-content' }}
          columns={channelColumns}
          dataSource={channelData}
          pagination={channelPagination}
          onChange={pagination => {
            pageHandler(nodeInfo.guid, pagination.current, pagination.pageSize, 'channel');
          }}
        />
      </TinyCard>
    </GutterWrapper>
  );
}

const configColumns = [
  {
    title: '类型',
    dataIndex: 'type',
    render(type) {
      return VERSION_TYPE_TEXT_KEY_MAP[type];
    },
  },
  {
    title: '位置',
    dataIndex: 'range',
  },
  {
    title: '版本',
    dataIndex: 'version',
  },
  {
    title: '状态',
    dataIndex: 'status',
    render(status) {
      if (status === 'SYNCED') {
        return '已同步';
      }
      return '同步中';
    },
  },
];

const channelColumns = [
  {
    title: '通道ID',
    dataIndex: 'id',
  },
  {
    title: '通道名称',
    dataIndex: 'name',
  },
  {
    title: '通道IP',
    dataIndex: 'ip',
  },
  {
    title: '通道端口',
    dataIndex: 'port',
  },
  {
    title: '支持协议',
    dataIndex: 'protocol',
  },
  {
    title: '包含设备数',
    dataIndex: 'deviceNum',
  },
  {
    title: '状态',
    dataIndex: 'connectStatus',
  },
];

export default connect()(ServerNodeDetail);
