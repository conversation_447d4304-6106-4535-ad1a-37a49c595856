import React, { useCallback, useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { useParams } from 'react-router-dom';

import get from 'lodash/get';

import { Input } from '@manyun/base-ui.ui.input';
import { Table } from '@manyun/base-ui.ui.table';

import { TinyCard, TinyTable } from '@manyun/dc-brain.legacy.components';
import {
  channelConfigActions,
  channelDeviceActionCreator,
  resetDeviceSearchValuesActionCreator,
  setDevicePaginationThenGetDataActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/channelConfigActions';
import { channelConfigService } from '@manyun/dc-brain.legacy.services';

export function DevicePointCard({
  data,
  total,
  pageNum,
  pageSize,
  loading,
  getData,
  setPagination,
  deviceTypeMap,
  updateSearchValues,
}) {
  const { id } = useParams();
  useEffect(() => {
    updateSearchValues({ channelId: id });
    getData();
  }, [getData, updateSearchValues, id]);

  const [childItemsMap, setChildItemsMap] = useState({});

  const [pointLoading, setPointLoading] = useState(true);

  const paginationChangeHandler = useCallback(
    (current, size) => {
      setPagination({ pageNum: current, pageSize: size });
    },
    [setPagination]
  );

  const onChangeValuesAndGetData = value => {
    updateSearchValues({ deviceName: value });
    getData();
  };

  const expandedRowRender = ({ deviceGuid }) => {
    const columns = [
      { align: 'center', title: '测点名称', dataIndex: 'pointName' },
      { align: 'center', title: '数据类型', dataIndex: ['dataType', 'name'] },
      { align: 'center', title: '操作类型', dataIndex: ['operationType', 'name'] },
      { align: 'center', title: '转换方式', dataIndex: ['changeMode', 'name'] },
      { align: 'center', title: '缩放因子', dataIndex: 'zoom' },
      { align: 'center', title: '单元标识符', dataIndex: 'elementId' },
      { align: 'center', title: '偏移位', dataIndex: 'position' },
      { align: 'center', title: '寄存器起始地址', dataIndex: 'address' },
      {
        align: 'center',
        title: '是否交换寄存器位置',
        dataIndex: 'registerSwap',
        render(registerSwap) {
          if (registerSwap === null || registerSwap === undefined) {
            return '';
          }
          if (registerSwap) {
            return '是';
          }
          return '否';
        },
      },
      { align: 'center', title: '测点guid', dataIndex: 'pointGuid' },
      { align: 'center', title: '目标设备', dataIndex: 'targetGuid' },
      { align: 'center', title: '基数', dataIndex: 'baseValue' },
    ];

    const data = childItemsMap[deviceGuid];

    return (
      <Table
        size="middle"
        rowKey="id"
        columns={columns}
        dataSource={data}
        loading={pointLoading}
        pagination={false}
      />
    );
  };

  const columns = [
    {
      title: '设备名称',
      dataIndex: 'deviceName',
    },
    { title: '设备编码', dataIndex: 'deviceGuid' },

    {
      title: '设备类型',
      dataIndex: 'deviceType',
      render(deviceType) {
        return get(deviceTypeMap, [deviceType, 'metaName'], deviceType);
      },
    },
    { title: '位置', dataIndex: 'spaceGuid' },
    { title: '测点数', dataIndex: 'pointNum' },
  ];

  return (
    <TinyCard title="设备信息">
      <TinyTable
        rowKey="deviceGuid"
        actions={
          <Input.Search
            key="seach"
            allowClear
            placeholder="输入设备名称"
            style={{ width: 211, height: 32 }}
            onSearch={onChangeValuesAndGetData}
          />
        }
        columns={columns}
        expandedRowRender={expandedRowRender}
        dataSource={data}
        loading={loading}
        pagination={{
          total,
          current: pageNum,
          pageSize,
          onChange: paginationChangeHandler,
        }}
        onExpand={(expanded, { deviceGuid }) => {
          if (!expanded || childItemsMap[deviceGuid]) {
            return;
          }
          setPointLoading(true);
          channelConfigService
            .fetchChannelPointList({ channelId: id, deviceGuid })
            .then(({ response }) => {
              if (response) {
                setPointLoading(false);
                setChildItemsMap(prev => ({
                  ...prev,
                  [deviceGuid]: response.data,
                }));
              }
            });
        }}
      />
    </TinyCard>
  );
}
const mapStateToProps = ({
  common: { deviceCategory },
  channelConfig: {
    data,
    total,
    pagination: { pageNum, pageSize },
    loading,
    searchValues,
    selectedRowKeys,
  },
}) => ({
  data,
  total,
  loading,
  pageNum,
  pageSize,
  searchValues,
  selectedRowKeys,
  deviceTypeMap: get(deviceCategory, 'normalizedList'),
});

const mapDispatchToProps = {
  getData: channelDeviceActionCreator,
  setPagination: setDevicePaginationThenGetDataActionCreator,
  resetSearchValues: resetDeviceSearchValuesActionCreator,
  updateSearchValues: channelConfigActions.updateSearchValues,
};

export default connect(mapStateToProps, mapDispatchToProps)(DevicePointCard);
