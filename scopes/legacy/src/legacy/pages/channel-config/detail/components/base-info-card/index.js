import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { useParams } from 'react-router-dom';

import get from 'lodash/get';
import shortid from 'shortid';

import { Descriptions } from '@manyun/base-ui.ui.descriptions';

import { GutterWrapper, TinyCard } from '@manyun/dc-brain.legacy.components';
import { TinyTable } from '@manyun/dc-brain.legacy.components/tiny-table';
import { channelConfigService } from '@manyun/dc-brain.legacy.services';

export function BaseInfoCard() {
  const [baseInfo, setBaseInfo] = useState(null);

  const { id } = useParams();
  useEffect(() => {
    (async () => {
      const { response } = await channelConfigService.fetchChannelConfigDetail({
        channelIds: [id],
      });
      if (response) {
        const data = response.data[0];
        setBaseInfo(data);
      }
    })();
  }, [id]);

  function getConfig() {
    let config = get(baseInfo, 'config');
    if (config != null) {
      config = JSON.parse(config);
      const configData = config.map(item => {
        return {
          ...item,
          id: shortid(),
        };
      });
      return configData;
    }
    return [];
  }

  return (
    <GutterWrapper mode="vertical">
      <TinyCard title="基本配置">
        <Descriptions column={4}>
          <Descriptions.Item label="通道名称">{get(baseInfo, 'name')}</Descriptions.Item>
          <Descriptions.Item label="通道IP">{get(baseInfo, 'ip')}</Descriptions.Item>
          <Descriptions.Item label="通道端口">{get(baseInfo, 'port')}</Descriptions.Item>
          <Descriptions.Item label="支持协议">{get(baseInfo, 'protocol')}</Descriptions.Item>
          <Descriptions.Item label="通道类型">
            {get(baseInfo, 'channelType')?.name}
          </Descriptions.Item>
          <Descriptions.Item label="厂商">{get(baseInfo, 'vendor')}</Descriptions.Item>
          <Descriptions.Item label="型号">{get(baseInfo, 'productModel')}</Descriptions.Item>
          <Descriptions.Item label="状态">{get(baseInfo, 'channelStatus')?.name}</Descriptions.Item>
          <Descriptions.Item label="用户名">{get(baseInfo, 'username')}</Descriptions.Item>
          <Descriptions.Item label="密码">{get(baseInfo, 'password')}</Descriptions.Item>
        </Descriptions>
        <div>操作配置:</div>
        <div style={{ height: 10 }} />
        <TinyTable
          rowKey="id"
          align={'left'}
          columns={configColumns}
          dataSource={getConfig()}
          pagination={false}
        />
      </TinyCard>
    </GutterWrapper>
  );
}

export default connect()(BaseInfoCard);

const configColumns = [
  {
    title: '操作类型',
    dataIndex: ['operatorType', 'name'],
  },
  {
    title: '读取寄存器数量',
    dataIndex: 'registerNum',
  },
];
