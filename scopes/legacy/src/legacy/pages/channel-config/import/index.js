import React, { useEffect, useState } from 'react';
import { useHistory, useParams } from 'react-router-dom';

import QuestionCircleOutlined from '@ant-design/icons/es/icons/QuestionCircleOutlined';
import { saveAs } from 'file-saver';
import get from 'lodash.get';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';

import { Upload } from '@manyun/dc-brain.ui.upload';

import { GutterWrapper, StatusText, TinyCard, TinyTable } from '@manyun/dc-brain.legacy.components';
import { channelConfigService } from '@manyun/dc-brain.legacy.services';
import * as urlsUtil from '@manyun/dc-brain.legacy.utils/urls';

const columns = [
  {
    title: '序号',
    dataIndex: 'rowTag',
    fixed: 'left',
  },

  {
    title: '设备guid',
    dataIndex: ['errDto', 'deviceGuid'],
    render: (text, { errMessage }) => getToolTilp(text, errMessage, 'deviceGuid'),
  },
  {
    title: '测点code',
    dataIndex: ['errDto', 'pointCode'],
    render: (text, { errMessage }) => getToolTilp(text, errMessage, 'pointCode'),
  },
  {
    title: '数据类型',
    dataIndex: ['errDto', 'dataType'],
    render: (text, { errMessage }) => getToolTilp(text, errMessage, 'dataType'),
  },
  {
    title: '操作类型',
    dataIndex: ['errDto', 'operationType'],
    render: (text, { errMessage }) => getToolTilp(text, errMessage, 'operationType'),
  },
  {
    title: '转换方式',
    dataIndex: ['errDto', 'changeMode'],
    render: (text, { errMessage }) => getToolTilp(text, errMessage, 'changeMode'),
  },
  {
    title: '缩放因子',
    dataIndex: ['errDto', 'zoom'],
    render: (text, { errMessage }) => getToolTilp(text, errMessage, 'zoom'),
  },
  {
    title: '基数',
    dataIndex: ['errDto', 'baseValue'],
    render: (text, { errMessage }) => getToolTilp(text, errMessage, 'baseValue'),
  },
  {
    title: '单元标识符',
    dataIndex: ['errDto', 'elementId'],
    render: (text, { errMessage }) => getToolTilp(text, errMessage, 'elementId'),
  },
  {
    title: '偏移位',
    dataIndex: ['errDto', 'position'],
    render: (text, { errMessage }) => getToolTilp(text, errMessage, 'position'),
  },
  {
    title: '寄存器起始地址',
    dataIndex: ['errDto', 'approveArea'],
    render: (text, { errMessage }) => getToolTilp(text, errMessage, 'approveArea'),
  },
  {
    title: '是否交换寄存器位置',
    dataIndex: ['errDto', 'registerSwap'],
    render: (text, { errMessage }) => {
      const value = getToolTilp(text, errMessage, 'registerSwap');
      if (value === null || value === undefined) {
        return '';
      }
      if (value) {
        return '是';
      }
      return '否';
    },
  },
  {
    title: '测点guid',
    dataIndex: ['errDto', 'pointGuid'],
    render: (text, { errMessage }) => getToolTilp(text, errMessage, 'pointGuid'),
  },
  {
    title: '目标设备',
    dataIndex: ['errDto', 'targetGuid'],
    render: (text, { errMessage }) => getToolTilp(text, errMessage, 'targetGuid'),
  },
  {
    title: 'bit总数',
    dataIndex: ['errDto', 'bitCount'],
    render: (text, { errMessage }) => getToolTilp(text, errMessage, 'bitCount'),
  },

  {
    title: '表达式',
    dataIndex: ['errDto', 'formula'],
    render: (text, { errMessage }) => getToolTilp(text, errMessage, 'formula'),
  },
];
export function ChannelPointImport() {
  const [tableData, setDataSource] = useState([]);
  const [baseInfo, setBaseInfo] = useState(null);

  const [importButtonLoading, setImportButtonLoading] = useState(false);

  const history = useHistory();

  const { id } = useParams();
  useEffect(() => {
    (async () => {
      const { response } = await channelConfigService.fetchChannelConfigDetail({
        channelIds: [id],
      });
      if (response) {
        const data = response.data[0];
        setBaseInfo(data);
      }
    })();
  }, [id]);

  const downloadDemo = async () => {
    const { response, filename = 'import-channel-template.csv' } =
      await channelConfigService.downloadChannelPointModel();
    if (!response) {
      return;
    }
    saveAs(response, filename);
  };

  const beforeUpload = (file, fileList) => {
    const fd = new FormData();
    fd.append('file', file);
    fd.append('channelId', get(baseInfo, 'id'));
    fd.append('blockGuid', get(baseInfo, 'blockGuid'));
    fd.append('protocol', get(baseInfo, 'protocol'));
    loadFetchData(fd);
    return false;
  };

  const loadFetchData = async fd => {
    setImportButtonLoading(true);
    const { response, error } = await channelConfigService.channelPointImport(fd);
    if (error) {
      message.error(error);
      setImportButtonLoading(false);
      return;
    }
    if (response) {
      if (response.faultTotal === 0) {
        setImportButtonLoading(false);
        message.success('导入成功！');
        setTimeout(() => {
          history.push(urlsUtil.generateChannelDeviceAssociateLocation({ id: id }));
        }, 1.5 * 1000);
      }
      setImportButtonLoading(false);
      setDataSource(response.excelCheckErrDtos);
    }
  };

  return (
    <GutterWrapper mode="vertical">
      <TinyCard>
        <TinyTable
          rowKey="rowTag"
          size="small"
          scroll={{ x: 'max-content' }}
          columns={columns}
          actions={
            <GutterWrapper>
              <Upload
                key="import"
                beforeUpload={beforeUpload}
                showUploadList={false}
                accept=".csv,.xls,.xlsx"
              >
                <Button type="primary" loading={importButtonLoading}>
                  导入
                </Button>
              </Upload>
              <Button key="download" onClick={downloadDemo}>
                下载模板
              </Button>
            </GutterWrapper>
          }
          //   loading={loading}
          dataSource={tableData}
        />
      </TinyCard>
    </GutterWrapper>
  );
}

export default ChannelPointImport;

function getToolTilp(value, errMessage, dataType) {
  const newValue = value;
  if (Object.prototype.hasOwnProperty.call(errMessage, dataType)) {
    return (
      <Tooltip title={errMessage[dataType]} placement="topLeft">
        <GutterWrapper size="2px" alignItems="center">
          <StatusText status="alarm" style={{ display: 'inline-block' }}>
            {newValue ? newValue : '--'}
          </StatusText>
          <QuestionCircleOutlined />
        </GutterWrapper>
      </Tooltip>
    );
  }
  return newValue;
}
