import React, { useEffect, useState } from 'react';
import { useHistory, useParams } from 'react-router-dom';

import FormLegacy from '@ant-design/compatible/es/form';
import PlusCircleOutlined from '@ant-design/icons/es/icons/PlusCircleOutlined';
import { ApiSelect, EditableTable, Form } from '@galiojs/awesome-antd';
import { generateGetRowSpan } from '@galiojs/awesome-antd/lib/table/utils';
import cloneDeep from 'lodash.clonedeep';
import shortid from 'shortid';

import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { Radio } from '@manyun/base-ui.ui.radio';

import { DeviceOrSpaceSelect } from '@manyun/resource-hub.ui.device-or-space-select';

import {
  <PERSON>er<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  LocationCascader,
  TinyCard,
} from '@manyun/dc-brain.legacy.components';
import { channelConfigService } from '@manyun/dc-brain.legacy.services';
import { getObjectOwnProps } from '@manyun/dc-brain.legacy.utils';

export function ChannelConfigNew({ form }) {
  const [editableTableForm] = Form.useForm();

  const { getFieldDecorator, validateFields, setFieldsValue, getFieldValue } = form;

  const { id } = useParams();

  const isODCC = getFieldValue('protocol') === 'ODCC';

  const [loading, setLoading] = useState(false);
  const [configList, setConfigList] = useState([]);
  const [editingRowKey, setEditingRowKey] = useState();
  const [deleteByCancel, setDeleteByCancel] = useState(false);

  const [blockGuid, setBlockGuid] = useState([]);
  const [initDeviceOption, setInitDeviceOption] = useState();

  useEffect(() => {
    if (!id) {
      return;
    }
    (async () => {
      const { response } = await channelConfigService.fetchChannelConfigDetail({
        channelIds: [id],
      });
      if (response) {
        const data = response.data[0];
        let config = [];
        try {
          config = JSON.parse(data.config) || [];
        } catch (error) {
          console.error(error);
        }

        setConfigList(
          config.map(item => {
            const key = shortid();
            return {
              ...item,
              operatorType: { key: item.operatorType.code, label: item.operatorType.name },
              mergeRowsKey: key,
              key: key,
            };
          })
        );
        setEditingRowKey(null);
        setDeleteByCancel(true);

        const location = data.blockGuid.split('.');
        setBlockGuid(location);
        setInitDeviceOption({ key: data.deviceGuid, label: data.deviceName });
        setFieldsValue({
          channelName: data.name,
          ip: data.ip,
          port: data.port,
          protocol: data.protocol,
          channelType: data.channelType.code,
          blockGuid: location,
          deviceGuid: data.deviceGuid,
          channelStatus: data.channelStatus.code,
          username: data.username,
          password: data.password,
        });
      }
    })();
  }, [id, setFieldsValue]);

  const history = useHistory();

  const addRow = (rowData, insertIdx) => {
    const nextData = cloneDeep(configList);
    nextData.splice(insertIdx, 0, rowData);
    setConfigList(nextData);
    setDeleteByCancel(true);
    setEditingRowKey(rowData.key);
  };

  return (
    <GutterWrapper mode="vertical">
      <TinyCard title="基本信息">
        <FormLegacy
          style={{ width: 600 }}
          colon={true}
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 20 }}
        >
          <FormLegacy.Item label="通道名称">
            {getFieldDecorator('channelName', {
              rules: [
                {
                  required: true,
                  whitespace: true,
                  message: '通道名称必填！',
                },
                {
                  max: 20,
                  message: '最多输入 20 个字符！',
                },
              ],
            })(<Input allowClear style={{ width: 211 }} />)}
          </FormLegacy.Item>
          <FormLegacy.Item label="通道IP">
            {getFieldDecorator('ip', {
              rules: [
                {
                  required: true,
                  whitespace: true,
                  message: '通道IP必填！',
                },
                {
                  pattern:
                    /^((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})){3}$/,
                  message: 'IP格式不正确',
                },
                {
                  max: 20,
                  message: '最多输入 20 个字符！',
                },
              ],
            })(<Input allowClear style={{ width: 211 }} />)}
          </FormLegacy.Item>
          <FormLegacy.Item label="通道端口">
            {getFieldDecorator('port', {
              rules: [
                {
                  required: true,
                  whitespace: true,
                  message: '通道端口必填！',
                },
                {
                  pattern: /^([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-5]{2}[0-3][0-5])$/,
                  message: '端口格式不正确',
                },
                {
                  max: 20,
                  message: '最多输入 20 个字符！',
                },
              ],
            })(<Input allowClear style={{ width: 211 }} />)}
          </FormLegacy.Item>
          <FormLegacy.Item label="支持协议">
            {getFieldDecorator('protocol', {
              rules: [
                {
                  required: true,
                  whitespace: true,
                  message: '支持协议必填！',
                },
              ],
            })(
              <ApiSelect
                showSearch
                allowClear
                trigger="onDidMount"
                fieldNames={{ label: 'label', value: 'value' }}
                dataService={async () => {
                  const { response } = await channelConfigService.fetchChannelProtocolType();
                  if (response) {
                    return Promise.resolve(getObjectOwnProps(response));
                  } else {
                    return Promise.resolve([]);
                  }
                }}
                style={{ width: 211 }}
              />
            )}
          </FormLegacy.Item>
          <FormLegacy.Item label="用户名">
            {getFieldDecorator('username', {
              rules: [
                {
                  required: isODCC,
                  message: '用户名必填！',
                },
                {
                  max: 20,
                  message: '最多输入 20 个字符！',
                },
              ],
            })(<Input allowClear style={{ width: 211 }} />)}
          </FormLegacy.Item>
          <FormLegacy.Item label="密码">
            {getFieldDecorator('password', {
              rules: [
                {
                  required: isODCC,
                  message: '密码必填！',
                },
                {
                  max: 20,
                  message: '最多输入 20 个字符！',
                },
              ],
            })(<Input allowClear style={{ width: 211 }} />)}
          </FormLegacy.Item>
          <FormLegacy.Item label="通道类型">
            {getFieldDecorator('channelType', {
              rules: [
                {
                  required: true,
                  message: '通道类型必填！',
                },
              ],
            })(
              <ApiSelect
                showSearch
                allowClear
                trigger="onDidMount"
                fieldNames={{ label: 'label', value: 'value' }}
                dataService={async () => {
                  const { response } = await channelConfigService.fetchChannelType();
                  if (response) {
                    return Promise.resolve(getObjectOwnProps(response));
                  } else {
                    return Promise.resolve([]);
                  }
                }}
                style={{ width: 211 }}
              />
            )}
          </FormLegacy.Item>

          <FormLegacy.Item label="位置">
            {getFieldDecorator('blockGuid', {
              rules: [
                {
                  required: true,
                  // type: 'number',
                  // transform: value => (value?.length === 2 ? 2 : false),
                  message: '位置必填！',
                },
              ],
            })(
              <LocationCascader
                currentAuthorize
                style={{ width: 211 }}
                onChange={value => {
                  setBlockGuid(value);
                }}
              />
            )}
          </FormLegacy.Item>
          {!!blockGuid.length && (
            <FormLegacy.Item label="通信设备">
              {getFieldDecorator('deviceGuid', {
                initialValue: initDeviceOption,
              })(
                <DeviceOrSpaceSelect
                  allowClear
                  idcTag={blockGuid[0]}
                  blockTag={blockGuid[1]}
                  disabled={!blockGuid.length}
                  type="DEVICE"
                  style={{ width: 211 }}
                />
              )}
            </FormLegacy.Item>
          )}

          <FormLegacy.Item label="状态">
            {getFieldDecorator('channelStatus', {
              initialValue: 'OFF',
              rules: [
                {
                  required: true,
                  message: '状态必填！',
                },
              ],
            })(
              <Radio.Group>
                <Radio key="ON" value="ON">
                  启用
                </Radio>
                <Radio key="OFF" value="OFF">
                  停用
                </Radio>
              </Radio.Group>
            )}
          </FormLegacy.Item>
        </FormLegacy>
        <div>操作配置:</div>
        <div style={{ height: 10 }} />
        <Button
          type="primary"
          disabled={editingRowKey ? true : false}
          onClick={() => {
            const key = shortid();
            addRow({ key, mergeRowsKey: key });
          }}
        >
          添加操作配置
        </Button>
        <div style={{ height: 10 }} />
        <EditableTable
          form={editableTableForm}
          rowKey="key"
          size="small"
          showActionsColumn
          dataSource={configList}
          columns={getColumns({
            addRow: addRow,
            editingRowKey: editingRowKey,
            getRowSpan: generateGetRowSpan(configList),
          })}
          editingRowKey={editingRowKey}
          onSave={(_currentKey, records) => {
            setConfigList(records);
            setDeleteByCancel(false);
            setEditingRowKey(null);
          }}
          onEdit={rowKey => {
            setEditingRowKey(rowKey);
          }}
          onCancel={() => {
            if (deleteByCancel) {
              const nextData = cloneDeep(configList);
              const idx = nextData.findIndex(record => record.key === editingRowKey);
              if (idx > -1) {
                nextData.splice(idx, 1);
              }
              setConfigList(nextData);
              setDeleteByCancel(false);
              setEditingRowKey(null);
            } else {
              setEditingRowKey(null);
            }
          }}
          onDelete={(rowKey, data) => {
            // if (data === null || data.length === 0) {
            //   message.error('最少保留一条数据! ');
            //   return;
            // }
            setConfigList(data);
            if (rowKey === editingRowKey) {
              setEditingRowKey(null);
            }
          }}
        />
      </TinyCard>
      <div style={{ height: 50 }} />
      <FooterToolBar>
        <GutterWrapper>
          <Button
            type="primary"
            loading={loading}
            onClick={() => {
              validateFields(async (error, valueMap) => {
                if (error) {
                  return;
                }

                let test = false;
                configList.map(item => {
                  if (item.operatorType === null || item.operatorType === undefined) {
                    message.error('操作配置未保存,请保存后在提交! ');
                    test = true;
                  }
                  return '';
                });
                if (test === true) {
                  return;
                }
                setLoading(true);
                const query = getQ(valueMap, configList, id);
                let response = null;
                if (id !== null && id !== undefined) {
                  response = await updateChannelConfig(query);
                } else {
                  response = await createChannelConfig(query);
                }
                if (response) {
                  history.goBack();
                }
                setLoading(false);
              });
            }}
          >
            提交
          </Button>
          <Button
            loading={loading}
            onClick={() => {
              history.goBack();
            }}
          >
            取消
          </Button>
        </GutterWrapper>
      </FooterToolBar>
    </GutterWrapper>
  );
}

export default FormLegacy.create()(ChannelConfigNew);

function getQ(
  {
    channelName,
    ip,
    port,
    protocol,
    channelType,
    channelStatus,
    blockGuid,
    deviceGuid,
    username,
    password,
  },
  configList,
  id
) {
  return {
    channelId: id,
    channelName,
    ip,
    port,
    protocol,
    channelType,
    channelStatus,
    deviceGuid,
    blockGuid: blockGuid.length === 2 ? blockGuid[0] + '.' + blockGuid[1] : blockGuid[0],
    username,
    password,
    config: configList.map(item => {
      return {
        operatorType: item.operatorType.key,
        registerNum: item.registerNum,
      };
    }),
  };
}

async function updateChannelConfig(data) {
  const { response, error } = await channelConfigService.updateChannelConfig(data);

  if (error) {
    message.error(error);
    return;
  }

  message.success('修改成功！');
  return response;
}

async function createChannelConfig(data) {
  const { response, error } = await channelConfigService.createChannelConfig(data);

  if (error) {
    message.error(error);
    return;
  }

  message.success('创建成功！');
  return response;
}

const getColumns = ({ editingRowKey, getRowSpan, addRow }) => [
  {
    title: '操作类型',
    dataIndex: ['operatorType', 'label'],
    editable: true,
    editingId: 'operatorType',
    editingCtrl: (
      <ApiSelect
        showSearch
        allowClear
        labelInValue // { key: string; label: string }
        fieldNames={{ label: 'label', value: 'value' }}
        dataService={async () => {
          const { response } = await channelConfigService.fetchChannelOperatorType();
          if (response) {
            return Promise.resolve(getObjectOwnProps(response));
          } else {
            return Promise.resolve([]);
          }
        }}
        style={{ width: 211 }}
      />
    ),
    formItemProps: {
      rules: [{ required: true, message: '操作类型必填！' }],
    },
    render(operatorType, record, idx) {
      if (idx === 0 && !editingRowKey) {
        return (
          <span>
            <PlusCircleOutlined
              onClick={() => {
                const key = shortid();
                addRow({
                  key: key,
                  mergeRowsKey: key,
                });
              }}
              style={{ marginRight: 8 }}
            />
            {operatorType}
          </span>
        );
      }

      return operatorType;
    },
  },
  {
    title: '读取寄存器数量',
    dataIndex: 'registerNum',
    editable: true,
    editingCtrl: <InputNumber min={0} style={{ width: 100 }} />,
    formItemProps: {
      rules: [{ required: true, message: '读取寄存器数量必填！' }],
    },
    render(registerNum, record, idx) {
      return registerNum;
    },
  },
];
