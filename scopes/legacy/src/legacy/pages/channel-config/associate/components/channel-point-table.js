import React, { useState } from 'react';

import Form from '@ant-design/compatible/es/form';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';

import { GutterWrapper, TinyTable } from '@manyun/dc-brain.legacy.components';
import { channelConfigService } from '@manyun/dc-brain.legacy.services';

const columns = [
  { title: '测点名称', dataIndex: 'pointName' },
  { title: '数据类型', dataIndex: ['dataType', 'name'] },
  { title: '操作类型', dataIndex: ['operationType', 'name'] },
  { title: '转换方式', dataIndex: ['changeMode', 'name'] },
  { title: '缩放因子', dataIndex: 'zoom' },
  { title: '单元标识符', dataIndex: 'elementId' },
  { title: '偏移位', dataIndex: 'position' },
  { title: '寄存器起始地址', dataIndex: 'address' },
  {
    title: '是否交换寄存器位置',
    dataIndex: 'registerSwap',
    render(registerSwap) {
      if (registerSwap === null || registerSwap === undefined) {
        return '';
      }
      if (registerSwap) {
        return '是';
      }
      return '否';
    },
  },
  { title: '测点guid', dataIndex: 'pointGuid' },
  { title: '目标设备', dataIndex: 'targetGuid' },
  { title: 'bit总数', dataIndex: 'bitCount' },
  { title: '表达式', dataIndex: 'formula' },

  { title: '基数', dataIndex: 'baseValue' },
  { title: '调试结果', dataIndex: 'value' },
];

export function ChannelPointTable({ channelId, deviceGuid }) {
  const [visible, setVisible] = useState(false);
  const [tableData, setDataSource] = useState({ list: [] });

  const getCabinet = async () => {
    const { response, error } = await channelConfigService.fetchChannelPointList({
      channelId,
      deviceGuid,
    });
    if (error) {
      message.error(error);
      return;
    }

    //const {result} = await channelConfigService.fetchChannelPointResult({})

    setDataSource({
      list: response.data,
    });
  };

  const debug = async () => {
    const { error } = await channelConfigService.fetchChannelPointList({
      channelId,
      deviceGuid,
    });
    if (error) {
      message.error(error);
      return;
    }
    var map = new Map();
    map.set(653576, 123);
    const data = tableData.list.map(item => {
      return {
        ...item,
        value: map.get(item.id),
      };
    });
    setDataSource({
      list: data,
    });
  };

  return (
    <GutterWrapper style={{ display: 'inline-block' }}>
      <Button
        type="link"
        size="small"
        onClick={() => {
          if (!visible) {
            getCabinet();
          }
          setVisible(true);
        }}
      >
        调试
      </Button>
      <Modal
        width="1101px"
        title="调试测点"
        visible={visible}
        onCancel={() => {
          setVisible(false);
        }}
        footer={null}
      >
        <GutterWrapper mode="vertical">
          <TinyTable
            rowKey="id"
            size="small"
            columns={columns}
            //   loading={loading}
            scroll={{ x: 'max-content' }}
            dataSource={tableData.list}
            actions={
              <Button
                key="enabled-button"
                type="danger"
                onClick={() => {
                  debug();
                }}
              >
                调试
              </Button>
            }
          />
        </GutterWrapper>
      </Modal>
    </GutterWrapper>
  );
}

export default Form.create()(ChannelPointTable);
