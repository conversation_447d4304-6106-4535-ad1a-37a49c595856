import React, { useCallback, useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { Link, useParams } from 'react-router-dom';

import get from 'lodash/get';

import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Popover } from '@manyun/base-ui.ui.popover';
import { Table } from '@manyun/base-ui.ui.table';

import { GutterWrapper, TinyCard, TinyTable } from '@manyun/dc-brain.legacy.components';
import {
  channelConfigActions,
  channelDeviceActionCreator,
  resetDeviceSearchValuesActionCreator,
  setDevicePaginationThenGetDataActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/channelConfigActions';
import { channelConfigService } from '@manyun/dc-brain.legacy.services';
import * as urlsUtil from '@manyun/dc-brain.legacy.utils/urls';

//import { ChannelPointTable } from './components/channel-point-table';

export function ChannelDeviceAssociate({
  data,
  total,
  pageNum,
  pageSize,
  loading,
  getData,
  setPagination,
  deviceTypeMap,
  updateSearchValues,
  searchValues,
}) {
  const { id } = useParams();
  useEffect(() => {
    updateSearchValues({ channelId: id });
    getData();
  }, [getData, updateSearchValues, id]);

  const [childItemsMap, setChildItemsMap] = useState({});

  const [pointLoading, setPointLoading] = useState(true);

  const paginationChangeHandler = useCallback(
    (current, size) => {
      setPagination({ pageNum: current, pageSize: size });
    },
    [setPagination]
  );

  const onChangeValuesAndGetData = value => {
    updateSearchValues({ deviceName: value });
    getData();
  };

  const expandedRowRender = ({ deviceGuid }) => {
    const columns = [
      { align: 'center', title: '测点名称', dataIndex: 'pointName' },
      { align: 'center', title: '数据类型', dataIndex: ['dataType', 'name'] },
      { align: 'center', title: '操作类型', dataIndex: ['operationType', 'name'] },
      { align: 'center', title: '转换方式', dataIndex: ['changeMode', 'name'] },
      { align: 'center', title: '缩放因子', dataIndex: 'zoom' },
      { align: 'center', title: '基数', dataIndex: 'baseValue' },
      { align: 'center', title: '单元标识符', dataIndex: 'elementId' },
      { align: 'center', title: '偏移位', dataIndex: 'position' },
      { align: 'center', title: '寄存器起始地址', dataIndex: 'address' },
      {
        align: 'center',
        title: '是否交换寄存器位置',
        dataIndex: 'registerSwap',
        render(registerSwap) {
          if (registerSwap === null || registerSwap === undefined) {
            return '';
          }
          if (registerSwap) {
            return '是';
          }
          return '否';
        },
      },
      { align: 'center', title: '测点guid', dataIndex: 'pointGuid' },
      { align: 'center', title: '目标设备', dataIndex: 'targetGuid' },
      { align: 'center', title: 'bit总数', dataIndex: 'bitCount' },
      { align: 'center', title: '表达式', dataIndex: 'formula' },
      {
        align: 'center',
        title: '操作',
        dataIndex: 'id',
        width: 150,
        render: (id, record) => (
          <span>
            <Popover
              trigger="click"
              placement="topRight"
              content={
                <GutterWrapper mode="vertical">
                  <div>确认删除 {record.pointName}吗？</div>
                  <Button
                    style={buttonStyle}
                    type="link"
                    size="small"
                    onClick={async () => {
                      const response = await deleteChannelPoint({
                        channelId: record.channelId,
                        deviceGuid: record.deviceGuid,
                        pointCode: record.pointCode,
                      });
                      setPointLoading(true);
                      if (response) {
                        getData();
                        channelConfigService
                          .fetchChannelPointList({
                            channelId: record.channelId,
                            deviceGuid: record.deviceGuid,
                          })
                          .then(({ response }) => {
                            if (response) {
                              setPointLoading(false);
                              setChildItemsMap(prev => ({
                                ...prev,
                                [deviceGuid]: response.data,
                              }));
                            }
                          });
                      }
                    }}
                  >
                    确认
                  </Button>
                </GutterWrapper>
              }
            >
              <Button type="link" style={buttonStyle}>
                删除
              </Button>
            </Popover>
          </span>
        ),
      },
    ];

    const data = childItemsMap[deviceGuid];

    return (
      <Table
        size="middle"
        rowKey="id"
        columns={columns}
        dataSource={data}
        scroll={{ x: 'max-content' }}
        loading={pointLoading}
        pagination={false}
      />
    );
  };

  const columns = [
    {
      title: '设备名称',
      dataIndex: 'deviceName',
    },
    { title: '设备编码', dataIndex: 'deviceGuid' },

    {
      title: '设备类型',
      dataIndex: 'deviceType',
      render(deviceType) {
        return get(deviceTypeMap, [deviceType, 'metaName'], deviceType);
      },
    },
    { title: '位置', dataIndex: 'spaceGuid' },
    { title: '测点数', dataIndex: 'pointNum' },
    {
      title: '操作',
      key: '_actions',
      width: 150,
      render: (_, record) => (
        <>
          {/* <ChannelPointTable channelId={id} deviceGuid={record.deviceGuid} />
          <Divider type="vertical" /> */}
          <Popover
            trigger="click"
            placement="topRight"
            content={
              <GutterWrapper mode="vertical">
                <div>确认删除 {record.deviceName}吗？</div>
                <Button
                  style={buttonStyle}
                  type="link"
                  size="small"
                  onClick={async () => {
                    const response = await deleteChannelPoint({
                      channelId: record.channelId,
                      deviceGuid: record.deviceGuid,
                    });
                    if (response) {
                      getData();
                    }
                  }}
                >
                  确认
                </Button>
              </GutterWrapper>
            }
          >
            <Button compact type="link" style={buttonStyle}>
              删除
            </Button>
          </Popover>
        </>
      ),
    },
  ];

  return (
    <TinyCard title="关联设备测点">
      <TinyTable
        rowKey="deviceGuid"
        actions={[
          <Link
            key="new-north-user-link"
            to={urlsUtil.generateChannelPointImportLocation({ id: id })}
          >
            <Button type="primary">导入</Button>
          </Link>,
          <Button
            key="disable-button"
            type="danger"
            disabled={!data || data.length === 0}
            onClick={async () => {
              const response = await deleteChannelPoint({
                channelId: id,
              });
              if (response) {
                getData();
              }
            }}
          >
            全部清除
          </Button>,
          <Input.Search
            key="seach"
            value={searchValues.deviceName}
            allowClear
            placeholder="输入设备名称"
            style={{ width: 211, height: 32 }}
            onChange={e => {
              updateSearchValues({ deviceName: e.target.value });
            }}
            onSearch={onChangeValuesAndGetData}
          />,
        ]}
        columns={columns}
        expandedRowRender={expandedRowRender}
        scroll={{ x: 'max-content' }}
        dataSource={data}
        loading={loading}
        pagination={{
          total,
          current: pageNum,
          pageSize,
          onChange: paginationChangeHandler,
        }}
        onExpand={(expanded, { deviceGuid }) => {
          if (!expanded || childItemsMap[deviceGuid]) {
            return;
          }
          setPointLoading(true);
          channelConfigService
            .fetchChannelPointList({ channelId: id, deviceGuid })
            .then(({ response }) => {
              if (response) {
                setPointLoading(false);
                setChildItemsMap(prev => ({
                  ...prev,
                  [deviceGuid]: response.data,
                }));
              }
            });
        }}
      />
    </TinyCard>
  );
}
const mapStateToProps = ({
  common: { deviceCategory },
  channelConfig: {
    data,
    total,
    pagination: { pageNum, pageSize },
    loading,
    searchValues,
    selectedRowKeys,
  },
}) => ({
  data,
  total,
  loading,
  pageNum,
  pageSize,
  searchValues,
  selectedRowKeys,
  deviceTypeMap: get(deviceCategory, 'normalizedList'),
});

const mapDispatchToProps = {
  getData: channelDeviceActionCreator,
  setPagination: setDevicePaginationThenGetDataActionCreator,
  resetSearchValues: resetDeviceSearchValuesActionCreator,
  updateSearchValues: channelConfigActions.updateSearchValues,
  searchValues: channelConfigActions.searchValues,
};

export default connect(mapStateToProps, mapDispatchToProps)(ChannelDeviceAssociate);

async function deleteChannelPoint({ channelId, deviceGuid, pointCode }) {
  const { response, error } = await channelConfigService.deleteChannelPoint({
    channelId,
    deviceGuid,
    pointCode,
  });

  if (error) {
    message.error(error);
    return;
  }

  message.success('删除成功！');

  return response;
}

const buttonStyle = { padding: 0, height: 'auto' };
