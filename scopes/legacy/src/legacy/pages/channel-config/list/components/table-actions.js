import React from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';

import {
  batchUpdateChannelStatusActionCreator,
  channelConfigActionCreator,
  channelConfigActions,
} from '@manyun/dc-brain.legacy.redux/actions/channelConfigActions';
import * as urlsUtil from '@manyun/dc-brain.legacy.utils/urls';

export function TableActions({
  selectedRowKeys,
  batchUpdateChannelStatus,
  updateSearchValues,
  getData,
}) {
  const onChangeValuesAndGetData = value => {
    updateSearchValues({ condition: value });
    getData();
  };

  return [
    <Link key="new-north-user-link" to={urlsUtil.generateChannelConfigCreateLocation()}>
      <Button type="primary">新建通道</Button>
    </Link>,
    <Button
      key="disable-button"
      type="success"
      disabled={!selectedRowKeys.length}
      onClick={() => {
        batchUpdateChannelStatus({
          channelStatus: 'ON',
          channelIds: selectedRowKeys,
        });
      }}
    >
      启用
    </Button>,
    <Button
      key="enabled-button"
      type="danger"
      disabled={!selectedRowKeys.length}
      onClick={() => {
        batchUpdateChannelStatus({
          channelStatus: 'OFF',
          channelIds: selectedRowKeys,
        });
      }}
    >
      停用
    </Button>,
    <Input.Search
      key="seach"
      allowClear
      placeholder="输入通道名称/IP"
      style={{ width: 211, height: 32 }}
      onSearch={onChangeValuesAndGetData}
    />,
  ];
}

const mapDispatchToProps = {
  getData: channelConfigActionCreator,
  updateSearchValues: channelConfigActions.updateSearchValues,
  batchUpdateChannelStatus: batchUpdateChannelStatusActionCreator,
};

export default connect(null, mapDispatchToProps)(TableActions);
