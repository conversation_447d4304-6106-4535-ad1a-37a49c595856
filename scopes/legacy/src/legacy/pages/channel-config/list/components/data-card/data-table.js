import React, { useCallback, useEffect } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Divider } from '@manyun/base-ui.ui.divider';

import { DeviceTypeText } from '@manyun/resource-hub.ui.device-type-text';

import { Ellipsis, TinyTable } from '@manyun/dc-brain.legacy.components';
import {
  channelConfigActionCreator,
  channelConfigActions,
  resetSearchValuesActionCreator,
  setPaginationThenGetDataActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/channelConfigActions';
import * as urlsUtil from '@manyun/dc-brain.legacy.utils/urls';

import TableActions from '../table-actions';

export function DataTable({
  data,
  total,
  pageNum,
  pageSize,
  selectedRowKeys,
  loading,
  getData,
  setPagination,
  setSelectedRowKeys,
  setDataAndTotal,
}) {
  useEffect(() => {
    getData();
  }, [getData]);

  const paginationChangeHandler = useCallback(
    (current, size) => {
      setPagination({ pageNum: current, pageSize: size });
    },
    [setPagination]
  );

  return (
    <TinyTable
      rowKey="id"
      scroll={{ x: 'max-content' }}
      columns={getColumns({ setDataAndTotal })}
      loading={loading}
      dataSource={data}
      rowSelection={{
        selectedRowKeys,
        onChange: keys => {
          setSelectedRowKeys(keys);
        },
      }}
      pagination={{
        total,
        current: pageNum,
        pageSize,
        onChange: paginationChangeHandler,
      }}
      actions={<TableActions selectedRowKeys={selectedRowKeys} />}
    />
  );
}

const mapStateToProps = ({
  channelConfig: {
    data,
    total,
    pagination: { pageNum, pageSize },
    loading,
    searchValues,
    selectedRowKeys,
  },
}) => ({
  data,
  total,
  loading,
  pageNum,
  pageSize,
  searchValues,
  selectedRowKeys,
});

const mapDispatchToProps = {
  getData: channelConfigActionCreator,
  setPagination: setPaginationThenGetDataActionCreator,
  resetSearchValues: resetSearchValuesActionCreator,
  setSelectedRowKeys: channelConfigActions.setSelectedRowKeys,
  setDataAndTotal: channelConfigActions.setDataAndTotal,
};

export default connect(mapStateToProps, mapDispatchToProps)(DataTable);

const getColumns = ({ setDataAndTotal }) => {
  return [
    {
      title: '通道ID',
      dataIndex: 'id',
    },
    {
      title: '通道名称',
      dataIndex: 'name',
      render: (name, record) => (
        <Ellipsis lines={1} tooltip>
          <Link
            type="link"
            to={urlsUtil.generateChannelConfigUrl({ id: record.id })}
            onClick={() => {
              setDataAndTotal({ data: [], total: 0 });
            }}
          >
            {name}
          </Link>
        </Ellipsis>
      ),
    },
    {
      title: '通道IP',
      dataIndex: 'ip',
    },
    {
      title: '通道端口',
      dataIndex: 'port',
    },
    {
      title: '支持协议',
      dataIndex: 'protocol',
    },
    {
      title: '通信设备',
      dataIndex: 'deviceName',
    },
    {
      title: '设备分类',
      dataIndex: 'deviceType',
      render: text => {
        return <DeviceTypeText code={text} />;
      },
    },
    {
      title: '通道类型',
      dataIndex: ['channelType', 'name'],
    },
    {
      title: '位置',
      dataIndex: 'blockGuid',
    },
    {
      title: '包含设备数',
      dataIndex: 'deviceNum',
    },

    {
      title: '状态',
      dataIndex: ['channelStatus', 'name'],
    },
    {
      title: '操作',
      key: '_actions',
      fixed: 'right',
      width: 120,
      render: (_, record) => (
        <span>
          <Link to={urlsUtil.generateChannelConfigEditLocation({ id: record.id })}>
            <Button style={buttonStyle} type="link" size="small">
              编辑
            </Button>
          </Link>
          <Divider type="vertical" />
          <Link to={urlsUtil.generateChannelDeviceAssociateLocation({ id: record.id })}>
            <Button
              style={buttonStyle}
              type="link"
              size="small"
              onClick={() => {
                setDataAndTotal({ data: [], total: 0 });
              }}
            >
              关联
            </Button>
          </Link>
        </span>
      ),
    },
  ];
};

const buttonStyle = { padding: 0, height: 'auto' };
