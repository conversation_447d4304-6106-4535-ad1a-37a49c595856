import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';

import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Tabs } from '@manyun/base-ui.ui.tabs';

import { Ellipsis, GutterWrapper, TinyCard, TinyTable } from '@manyun/dc-brain.legacy.components';
import {
  getInsideNoticeListActionCreator,
  insideNoticeActions,
  setAlreadyRead,
  setReadAll,
} from '@manyun/dc-brain.legacy.redux/actions/insideNoticeActions';

import { TAB_LIST } from '../constants';
import { renderContent } from '../format-content';

function UnreadNotice({
  deviceTypeMapper,
  insideNoticeList,
  getInsideNoticeList,
  searchValues,
  updateSearchValues,
  setAlreadyRead,
  setReadAll,
  pagination,
  updatePagination,
  changePtype,
  insideMsgPType,
  isRead,
  form: { getFieldDecorator },
}) {
  useEffect(() => {
    getInsideNoticeList({
      insideMsgPType,
      isRead,
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize,
    });
  }, [getInsideNoticeList, insideMsgPType, isRead, pagination.pageNum, pagination.pageSize]);

  const params = Object.keys(searchValues).reduce((map, fieldName) => {
    const value = searchValues[fieldName]?.value;
    if ([null, undefined, ''].includes(value) || (Array.isArray(value) && value.length === 0)) {
      return map;
    } else if (fieldName === 'gmtCreate') {
      map.startTime = value[0].valueOf();
      map.endTime = value[1].valueOf();
    } else {
      map[fieldName] = value;
    }
    return map;
  }, {});

  const [records, setSelect] = useState({ selectedRowKeys: [], selectedRows: [] });

  //参数
  const [isModalVisible, setReadVisible] = useState(false);
  //参数
  const [isallreadModalVisible, setallreadModalVisible] = useState(false);

  //获取选中的值
  const selectParam = records.selectedRows.map(row => {
    return row.id;
  });

  const searchInsideNoticeList = () => {
    getInsideNoticeList({
      ...params,
      isRead,
      insideMsgPType,
      ...pagination,
    });
  };

  const setRead = () => {
    setAlreadyRead({
      params: {
        idList: selectParam,
      },
      successCallback: data => {
        getInsideNoticeList({
          ...params,
          isRead,
          insideMsgPType,
          ...pagination,
        });
        message.success(data ? `标记已读成功!` : `标记已读失败!`);
      },
    });
    setSelect({ selectedRowKeys: [], selectedRows: [] });
    setReadVisible(false);
  };

  const setInsideReadAll = (insideMsgPType, params) => {
    setReadAll({
      params: {
        parentType: insideMsgPType,
        ...params,
      },
      successCallback: data => {
        getInsideNoticeList({
          ...params,
          isRead,
          insideMsgPType,
          ...pagination,
        });
        message.success(data ? `标记全部已读成功!` : `标记全部已读失败!`);
      },
    });
    setallreadModalVisible(false);
  };

  const onClickBatchTake = () => {
    setReadVisible(true);
  };

  const onClickAllRead = () => {
    setallreadModalVisible(true);
  };

  const onChangePageSize = (page, params, insideMsgPType, isRead, getInsideNoticeList) => {
    getInsideNoticeList({
      ...params,
      pageNum: page.current,
      pageSize: page.pageSize,
      insideMsgPType,
      isRead,
    });
    updatePagination({
      pageNum: page.current,
      pageSize: page.pageSize,
    });
  };

  const onTabChange = key => {
    changePtype(key);
    updateSearchValues({
      gmtCreate: {
        value: null,
      },
    });
    setSelect({ selectedRowKeys: [], selectedRows: [] });
  };

  return (
    <TinyCard>
      <Tabs activeKey={insideMsgPType} onChange={onTabChange}>
        {TAB_LIST.map(item => (
          <Tabs.TabPane tab={item.name} key={item.key}>
            <GutterWrapper mode="vertical">
              <Form colon={false} layout="inline">
                <Form.Item label="创建时间">
                  {getFieldDecorator('gmtCreate')(
                    <DatePicker.RangePicker
                      format="YYYY-MM-DD HH:mm:ss"
                      showTime
                      placeholder={['开始时间', '结束时间']}
                    />
                  )}
                </Form.Item>
                <Form.Item>
                  <Button type="primary" onClick={searchInsideNoticeList}>
                    搜索
                  </Button>
                </Form.Item>
              </Form>
              <TinyTable
                rowKey="id"
                align="left"
                columns={getColumns(deviceTypeMapper)}
                loading={insideNoticeList.loading}
                dataSource={insideNoticeList.data}
                pagination={{
                  total: insideNoticeList.total,
                  current: pagination.pageNum,
                  pageSize: pagination.pageSize,
                }}
                rowSelection={{
                  selectedRowKeys: records.selectedRowKeys,
                  selectedRows: records.selectedRows,
                  onChange: (selectedRowKeys, selectedRows) => {
                    setSelect({ selectedRowKeys, selectedRows });
                  },
                }}
                actions={[
                  <div key="batch-read">
                    <Button
                      type="danger"
                      disabled={!(records.selectedRowKeys.length !== 0 && isRead === '0')}
                      onClick={onClickBatchTake}
                    >
                      标记已读
                    </Button>
                  </div>,

                  <div key="all-read">
                    <Button
                      disabled={!(insideNoticeList.total !== 0 && isRead === '0')}
                      onClick={onClickAllRead}
                    >
                      全部已读
                    </Button>
                  </div>,
                ]}
                onChange={page =>
                  onChangePageSize(page, params, item.key, isRead, getInsideNoticeList)
                }
              />
            </GutterWrapper>
          </Tabs.TabPane>
        ))}
      </Tabs>
      <Modal
        title="标记已读"
        centered={true}
        visible={isModalVisible}
        onOk={() => setRead()}
        onCancel={() => setReadVisible(false)}
      >
        <p>是否确定标记已读?</p>
      </Modal>
      <Modal
        title="全部已读"
        centered={true}
        visible={isallreadModalVisible}
        onOk={() => setInsideReadAll(insideMsgPType, params)}
        onCancel={() => setallreadModalVisible(false)}
      >
        <p>是否全部标记已读?</p>
      </Modal>
    </TinyCard>
  );
}

const mapStateToProps = ({
  insideNotice: { insideNoticeList, searchValues, insideMsgPType, isRead, pagination },
}) => {
  return {
    insideNoticeList,
    searchValues,
    insideMsgPType,
    isRead,
    pagination,
  };
};

const mapDispatchToProps = {
  getInsideNoticeList: getInsideNoticeListActionCreator,
  setAlreadyRead: setAlreadyRead,
  setReadAll: setReadAll,
  changePtype: insideNoticeActions.changePtype,
  updateSearchValues: insideNoticeActions.updateSearchValues,
  updatePagination: insideNoticeActions.updatePagination,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(
  Form.create({
    onFieldsChange(props, changedFields) {
      props.updateSearchValues(changedFields);
    },
    mapPropsToFields(props) {
      return {
        gmtCreate: Form.createFormField(props.searchValues.gmtCreate),
      };
    },
  })(UnreadNotice)
);

const getColumns = deviceTypeMapper => [
  {
    title: '内容',
    dataIndex: 'content',
    width: 500,
    render: (content, { targetContentList }) => {
      const linkContent = renderContent(content, targetContentList, deviceTypeMapper);
      return (
        <Ellipsis lines={1} tooltip>
          {linkContent}
        </Ellipsis>
      );
    },
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    dataType: 'datetime',
  },
  {
    title: '类型',
    dataIndex: 'insideScenes',
  },
];
