import React, { useEffect } from 'react';
import { connect } from 'react-redux';

import Icon from '@ant-design/icons';

import { Layout } from '@manyun/base-ui.ui.layout';
import { Menu } from '@manyun/base-ui.ui.menu';

import { GutterWrapper } from '@manyun/dc-brain.legacy.components';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import { insideNoticeActions } from '@manyun/dc-brain.legacy.redux/actions/insideNoticeActions';

import { Finish, Wait } from './icon';
import ReadedNotice from './readed-notice';
import UnreadNotice from './unread-notice';

const Sider = Layout.Sider;

function BaseInsideNotice({
  syncCommonData,
  deviceTypeMapper,
  changeReadFlag,
  updateSearchValues,
  isRead,
}) {
  useEffect(() => {
    syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });
  }, [syncCommonData]);

  const onTabChange = ({ key }) => {
    changeReadFlag(key);
    updateSearchValues({
      isDelay: {
        value: null,
      },
      gmtCreate: {
        value: null,
      },
    });
  };

  return (
    <GutterWrapper flex>
      <Sider style={{ width: 200 }}>
        <Menu mode="vertical" defaultSelectedKeys={isRead} onClick={onTabChange}>
          <Menu.Item key="0">
            <Icon component={Wait} />
            <span>未读</span>
          </Menu.Item>
          <Menu.Item key="1">
            <Icon component={Finish} />
            <span>已读</span>
          </Menu.Item>
        </Menu>
      </Sider>
      <GutterWrapper style={{ width: `calc(100% - 200px - 1rem)` }}>
        {isRead === '0' && <UnreadNotice deviceTypeMapper={deviceTypeMapper} />}
        {isRead === '1' && <ReadedNotice deviceTypeMapper={deviceTypeMapper} />}
      </GutterWrapper>
    </GutterWrapper>
  );
}

const mapStateToProps = ({ common: { deviceCategory }, insideNotice: { isRead } }) => {
  return {
    deviceTypeMapper: deviceCategory?.normalizedList || {},
    isRead,
  };
};

const mapDispatchToProps = {
  syncCommonData: syncCommonDataActionCreator,
  changeReadFlag: insideNoticeActions.changeReadFlag,
  updateSearchValues: insideNoticeActions.updateSearchValues,
};

export default connect(mapStateToProps, mapDispatchToProps)(BaseInsideNotice);
