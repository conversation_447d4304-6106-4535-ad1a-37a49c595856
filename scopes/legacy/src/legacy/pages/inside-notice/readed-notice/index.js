import React, { useEffect } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';

import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Tabs } from '@manyun/base-ui.ui.tabs';

import { El<PERSON><PERSON>, GutterWrapper, TinyCard, TinyTable } from '@manyun/dc-brain.legacy.components';
import {
  getInsideNoticeListActionCreator,
  insideNoticeActions,
} from '@manyun/dc-brain.legacy.redux/actions/insideNoticeActions';

import { TAB_LIST } from '../constants';
import { renderContent } from '../format-content';

function ReadedNotice({
  deviceTypeMapper,
  insideNoticeList,
  getInsideNoticeList,
  searchValues,
  updateSearchValues,
  pagination,
  updatePagination,
  changePtype,
  insideMsgPType,
  isRead,
  form: { getFieldDecorator },
}) {
  useEffect(() => {
    getInsideNoticeList({
      insideMsgPType,
      isRead,
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize,
    });
  }, [getInsideNoticeList, insideMsgPType, isRead, pagination.pageNum, pagination.pageSize]);

  const params = Object.keys(searchValues).reduce((map, fieldName) => {
    const value = searchValues[fieldName]?.value;
    if ([null, undefined, ''].includes(value) || (Array.isArray(value) && value.length === 0)) {
      return map;
    } else if (fieldName === 'gmtCreate') {
      map.startTime = value[0].valueOf();
      map.endTime = value[1].valueOf();
    } else {
      map[fieldName] = value;
    }
    return map;
  }, {});

  //参数
  const searchInsideNoticeList = () => {
    getInsideNoticeList({
      ...params,
      isRead,
      insideMsgPType,
      ...pagination,
    });
  };

  const onChangePageSize = (page, params, isRead, getInsideNoticeList) => {
    getInsideNoticeList({
      ...params,
      pageNum: page.current,
      pageSize: page.pageSize,
      insideMsgPType,
      isRead,
    });
    updatePagination({
      pageNum: page.current,
      pageSize: page.pageSize,
    });
  };

  const onTabChange = key => {
    changePtype(key);
    updateSearchValues({
      gmtCreate: {
        value: null,
      },
    });
  };

  const tabs = TAB_LIST.map(item => (
    <Tabs.TabPane tab={item.name} key={item.key}>
      <GutterWrapper mode="vertical">
        <Form colon={false} layout="inline">
          <Form.Item label="创建时间">
            {getFieldDecorator('gmtCreate')(
              <DatePicker.RangePicker
                format="YYYY-MM-DD HH:mm:ss"
                showTime
                placeholder={['开始时间', '结束时间']}
              />
            )}
          </Form.Item>
          <Form.Item>
            <Button type="primary" onClick={searchInsideNoticeList}>
              搜索
            </Button>
          </Form.Item>
        </Form>

        <TinyTable
          rowKey="id"
          align="left"
          columns={getColumns(deviceTypeMapper)}
          loading={insideNoticeList.loading}
          dataSource={insideNoticeList.data}
          pagination={{
            total: insideNoticeList.total,
            current: pagination.pageNum,
            pageSize: pagination.pageSize,
          }}
          onChange={page => onChangePageSize(page, params, isRead, getInsideNoticeList)}
        />
      </GutterWrapper>
    </Tabs.TabPane>
  ));

  return (
    <TinyCard>
      <Tabs activeKey={insideMsgPType} onChange={onTabChange}>
        {tabs}
      </Tabs>
    </TinyCard>
  );
}

const mapStateToProps = ({
  insideNotice: { insideNoticeList, searchValues, insideMsgPType, isRead, pagination },
}) => {
  return {
    insideNoticeList,
    searchValues,
    insideMsgPType,
    isRead,
    pagination,
  };
};

const mapDispatchToProps = {
  getInsideNoticeList: getInsideNoticeListActionCreator,
  changePtype: insideNoticeActions.changePtype,
  updateSearchValues: insideNoticeActions.updateSearchValues,
  updatePagination: insideNoticeActions.updatePagination,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(
  Form.create({
    onFieldsChange(props, changedFields) {
      props.updateSearchValues(changedFields);
    },
    mapPropsToFields(props) {
      return {
        gmtCreate: Form.createFormField(props.searchValues.gmtCreate),
      };
    },
  })(ReadedNotice)
);

const getColumns = deviceTypeMapper => [
  {
    title: '内容',
    dataIndex: 'content',
    width: 500,
    render: (content, { targetContentList }) => {
      const linkContent = renderContent(content, targetContentList, deviceTypeMapper);
      return (
        <Ellipsis lines={1} tooltip>
          {linkContent}
        </Ellipsis>
      );
    },
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    dataType: 'datetime',
  },
  {
    title: '类型',
    dataIndex: 'insideScenes',
  },
];
