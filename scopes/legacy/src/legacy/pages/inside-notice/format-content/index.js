import React from 'react';
import { Link } from 'react-router-dom';

import { OnSiteMessagesOowDevices } from '@manyun/notification-hub.ui.on-site-messages-oow-devices';
import { generateBorrowAndReturnDetailLocation } from '@manyun/resource-hub.route.resource-routes';
import { generateEventDetailRoutePath } from '@manyun/ticket.route.ticket-routes';

import { ApproveLink, ModalButton, UserLink } from '@manyun/dc-brain.legacy.components';
import { getBaseColumns } from '@manyun/dc-brain.legacy.pages/equipment-manage/list/utils/get-columns';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';

import { INSIDE_MSG_TARGET_TYPE_KEY_MAP } from '../constants';

export function renderContent(content, targetContentList, deviceTypeMapper) {
  const targetRender = getTargetRender(deviceTypeMapper);
  if (!content) {
    return '';
  }
  let newContent = content;
  if (targetContentList) {
    //listRegex = {0}/{1}/...  numberRegex=0/1/...
    const listRegex = /\{\d+\}/g;
    const numberRegex = /\d+/g;

    //去除{0}/{1}/...的内容数组
    const mainText = content.split(listRegex);

    if (!content.match(listRegex)?.length) {
      return content;
    }

    //numberList= [0,1]
    const numberList = content.match(listRegex).join().match(numberRegex);

    newContent = numberList.map((item, index) => {
      const name = targetContentList[parseInt(item)]?.targetName;
      const id = targetContentList[parseInt(item)]?.targetId;
      const targetType = targetContentList[parseInt(item)]?.targetType;
      if (index === numberList.length - 1) {
        //last one
        return (
          <span key={index}>
            {mainText[index]}
            {targetRender(name, id, targetType)}
            {mainText[index + 1]}
          </span>
        );
      }
      return (
        <span key={index}>
          {mainText[index]}
          {targetRender(name, id, targetType)}
        </span>
      );
    });
  }
  return newContent;
}

function getTargetRender(deviceTypeMapper) {
  return (targetName, targetId, targetType) => {
    switch (targetType) {
      case INSIDE_MSG_TARGET_TYPE_KEY_MAP.PERSON:
        return <UserLink userId={targetId} userName={targetName} />;

      //工单
      case INSIDE_MSG_TARGET_TYPE_KEY_MAP.INSPECTION:
      case INSIDE_MSG_TARGET_TYPE_KEY_MAP.INVENTORY:
      case INSIDE_MSG_TARGET_TYPE_KEY_MAP.MAINTENANCE:
      case INSIDE_MSG_TARGET_TYPE_KEY_MAP.POWER:
      case INSIDE_MSG_TARGET_TYPE_KEY_MAP.REPAIR:
      case INSIDE_MSG_TARGET_TYPE_KEY_MAP.WAREHOUSE:
      case INSIDE_MSG_TARGET_TYPE_KEY_MAP.ACCESS:
      case INSIDE_MSG_TARGET_TYPE_KEY_MAP.DEVICE_GENERAL:
      case INSIDE_MSG_TARGET_TYPE_KEY_MAP.ACCEPT:
      case INSIDE_MSG_TARGET_TYPE_KEY_MAP.ON_OFF:
      case INSIDE_MSG_TARGET_TYPE_KEY_MAP.ACCESS_CARD_AUTH: {
        return (
          <Link
            to={urls.generateTicketDetailLocation({
              ticketType: targetType.toLowerCase(),
              id: targetId,
            })}
          >
            {targetName}
          </Link>
        );
      }

      //事件详情
      case INSIDE_MSG_TARGET_TYPE_KEY_MAP.EVENT_DETAIL: {
        return (
          <Link
            to={generateEventDetailRoutePath({
              id: targetId,
            })}
          >
            {targetName}
          </Link>
        );
      }

      //todo公告详情
      case INSIDE_MSG_TARGET_TYPE_KEY_MAP.NOTICE_DETAIL: {
        return (
          <Link
            to={urls.generateNoticeDetailLocation({
              id: targetId,
            })}
          >
            {targetName}
          </Link>
        );
      }

      case INSIDE_MSG_TARGET_TYPE_KEY_MAP.MAINTENANCE_POOL:
        const columns = getBaseColumns(deviceTypeMapper).filter(
          ({ defaultVisible = true, visible }) => defaultVisible || visible
        );
        const scrollX = columns.reduce((w, { width }) => w + width, 0);

        return (
          <ModalButton type="link" text={targetName} title="过保设备" width="75%" footer={null}>
            {visible =>
              visible && (
                <OnSiteMessagesOowDevices
                  columns={columns}
                  scroll={{ x: scrollX }}
                  targetId={targetId}
                />
              )
            }
          </ModalButton>
        );

      //待办工单
      //todo列表
      //case INSIDE_MSG_TARGET_TYPE_KEY_MAP.WAITING_ACCEPCT_LIST:

      //case INSIDE_MSG_TARGET_TYPE_KEY_MAP.WAITING_ACCEPCTED_LIST:

      //todo借用归还记录
      //todo列表
      //case INSIDE_MSG_TARGET_TYPE_KEY_MAP.BORROW_LIST:

      //详情
      case INSIDE_MSG_TARGET_TYPE_KEY_MAP.BORROW_RETURN_DETAIL: {
        return (
          <Link
            to={generateBorrowAndReturnDetailLocation({
              id: targetId,
            })}
          >
            {targetName}
          </Link>
        );
      }

      //审批
      //详情
      case INSIDE_MSG_TARGET_TYPE_KEY_MAP.APPROVAL_DETAIL: {
        return <ApproveLink id={targetId} text={targetName} />;
      }
      //todo列表
      //case INSIDE_MSG_TARGET_TYPE_KEY_MAP.APPROVAL_LIST:

      default:
        return targetName;
    }
  };
}
