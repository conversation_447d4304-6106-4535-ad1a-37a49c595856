import React from 'react';
import { connect } from 'react-redux';
import { useHistory, useParams } from 'react-router-dom';

import GithubOutlined from '@ant-design/icons/es/icons/GithubOutlined';

import { Avatar } from '@manyun/base-ui.ui.avatar';
import { Button } from '@manyun/base-ui.ui.button';

import { TinyCard } from '@manyun/dc-brain.legacy.components';

function User({ user = {}, reposCount = 0 }) {
  const { login } = useParams();
  const history = useHistory();

  return (
    <TinyCard
      style={{ width: 300 }}
      actions={[
        <Button
          onClick={() => {
            history.push(`${login}/repos`);
          }}
        >
          <GithubOutlined key="github" /> View his/her {reposCount} Repos
        </Button>,
      ]}
    >
      <TinyCard.Meta
        avatar={<Avatar src={user.avatarUrl} />}
        title={user.name}
        description={user.bio}
      />
    </TinyCard>
  );
}

const mapStateToProps = ({ user: { user }, repos: { repos } }) => ({
  user,
  reposCount: repos.length,
});

export default connect(mapStateToProps)(User);
