import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';

import Form from '@ant-design/compatible/es/form';
import { ApiSelect } from '@galiojs/awesome-antd';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Radio } from '@manyun/base-ui.ui.radio';

import {
  approvalProcessListWithoutPage,
  scenesProcessRelateEdit,
  scenesProcessRelateList,
} from '@manyun/dc-brain.legacy.redux/actions/approvalConfigActions';
import { approvalConfigService } from '@manyun/dc-brain.legacy.services';

function CommonRelate({ subScenes, editData, scenesProcessRelateList, searchCondition }) {
  const [visible, setVisible] = useState(false);
  const [approvalProcessList, setApprovalProcessList] = useState([]);

  useEffect(() => {
    if (visible && subScenes) {
      scenesProcessRelateList({ subBizScenes: subScenes, callback: setApprovalProcessList });
    }
  }, [scenesProcessRelateList, subScenes, visible]);

  const processCode = approvalProcessList[0]?.processCode;
  const adioValue = processCode === 'NOT_NEED_APPROVAL' ? 'false' : 'true';

  return (
    <>
      <Button style={{ padding: 0, height: 'auto' }} type="link" onClick={() => setVisible(true)}>
        关联
      </Button>
      {visible && approvalProcessList.length > 0 && (
        <ModalForm
          visible={visible}
          id={approvalProcessList[0]?.id}
          processCode={processCode}
          adioValue={adioValue}
          onCancle={() => setVisible(false)}
          onOk={params => {
            editData({
              params,
              callback: () => {
                setVisible(false);
                setApprovalProcessList([]);
              },
              searchCondition,
            });
          }}
        />
      )}
    </>
  );
}

const mapStateToProps = ({ approvalProcessConfig: { approvalProcessList, searchCondition } }) => ({
  approvalProcessList,
  searchCondition,
});

const mapDispatchToProps = {
  queryProcessList: approvalProcessListWithoutPage,
  scenesProcessRelateList,
  editData: scenesProcessRelateEdit,
};

export default connect(mapStateToProps, mapDispatchToProps)(CommonRelate);

function ModalForm({ visible, id, processCode, adioValue, onCancle, onOk }) {
  const [adioViewValue, setAdioViewValue] = useState(adioValue);
  const [selectValue, setSelectValue] = useState();

  useEffect(() => {
    if (processCode && processCode !== '') {
      setSelectValue(processCode);
    }
  }, [processCode]);

  return (
    <Modal
      destroyOnClose
      title="关联"
      visible={visible}
      onCancel={onCancle}
      onOk={() => {
        if (adioViewValue === 'true' && (!selectValue || selectValue === 'NOT_NEED_APPROVAL')) {
          message.error('请选择审批流程！');
          return;
        }
        const code = adioViewValue === 'true' ? selectValue : 'NOT_NEED_APPROVAL';
        onOk({ modifyList: [{ id: id, processCode: code }] });
      }}
    >
      <Form colon={false} labelCol={{ span: 5 }} wrapperCol={{ span: 19 }}>
        <Form.Item label="是否关联流程">
          <Radio.Group
            value={adioViewValue === 'true' ? 'true' : 'false'}
            onChange={value => {
              setAdioViewValue(value.target.value);
            }}
          >
            <Radio value="true">是</Radio>
            <Radio value="false">否</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item label="审批流程">
          <ApiSelect
            style={{ marginLeft: 8, width: 'calc(100% - 100px - 8px)' }}
            showSearch
            trigger="onDidMount"
            disabled={adioViewValue !== 'true'}
            dataService={processInfoService}
            value={selectValue === 'NOT_NEED_APPROVAL' ? '' : selectValue}
            onChange={value => setSelectValue(value)}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
}

async function processInfoService(processName) {
  const { response, error } = await approvalConfigService.fetchApprovalProcessListWithoutPage({
    processName: processName,
  });

  if (error) {
    return [];
  }

  return response.data.map(({ processName, processCode }) => ({
    label: processName,
    value: processCode,
  }));
}
