import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';

import {
  generateApprovalProcessConfigDetailUrl,
  generateBpmEditUrl,
  generateSceneConfigListUrl,
} from '@manyun/bpm.route.bpm-routes';

import { GutterWrapper, TinyCard, UserLink } from '@manyun/dc-brain.legacy.components';
import { TinyTable } from '@manyun/dc-brain.legacy.components';
import {
  approvalConfigActions,
  approvalProcessList,
  deleteScenesProcessActionCreator,
} from '@manyun/dc-brain.legacy.redux/actions/approvalConfigActions';
import { syncCommonDataActionCreator } from '@manyun/dc-brain.legacy.redux/actions/commonActions';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';

const getColumns = ctx => [
  {
    title: '流程名称',
    dataIndex: 'processName',
    visible: true,
    render: (text, record) => (
      <Link to={generateApprovalProcessConfigDetailUrl({ processCode: record.processCode })}>
        {record.processName}
      </Link>
    ),
  },
  {
    title: '关联业务场景',
    dataIndex: 'subBizScenesList',
    visible: true,
    render: (text, record) => {
      if (!record.subBizScenesList) {
        return null;
      }
      return record.subBizScenesList.map(subBizScenes => {
        return (
          <span key={subBizScenes}>
            <Link to={generateSceneConfigListUrl({ subBizScenes })}>{subBizScenes}</Link>{' '}
          </span>
        );
      });
    },
  },
  {
    title: '更新人',
    dataIndex: 'operatorName',
    render: (operatorName, record) => (
      <UserLink userId={record.operator} userName={record.operatorName} />
    ),
  },
  {
    title: '更新时间',
    dataIndex: 'gmtModified',
    dataType: 'datetime',
  },
  {
    title: '操作',
    key: '__actions',
    fixed: 'right',
    exportable: false,
    render: (__, record) => (
      <span>
        <Link to={generateBpmEditUrl({ processCode: record.processCode })}>编辑</Link>
        <Divider type="vertical" />
        {record.subBizScenesList && record.subBizScenesList.length > 0 && (
          <Button
            type="link"
            style={{ padding: 0, height: 'auto' }}
            onClick={() => message.warning('请先将业务场景取消关联')}
          >
            删除
          </Button>
        )}
        {(!record.subBizScenesList ||
          (Array.isArray(record.subBizScenesList) && !record.subBizScenesList.length)) && (
          <DeleteConfirm
            variant="popconfirm"
            title={`确认删除流程 ${record.processName} 吗？`}
            onOk={() => {
              const searchParams = {
                pageNum: ctx.props.approvalProcessPageCondition.pageNum,
                pageSize: ctx.props.approvalProcessPageCondition.pageSize,
                processName: ctx.props.approvalProcessPageCondition.processName,
              };
              ctx.props.deleteScenesProcess({ processCode: record.processCode, searchParams });
            }}
          >
            <Button type="link" style={{ padding: 0, height: 'auto' }}>
              删除
            </Button>
          </DeleteConfirm>
        )}
      </span>
    ),
  },
];

class ApprovalProcessConfig extends Component {
  state = {
    loading: false,
  };

  componentDidMount() {
    this.props.getData({
      pageNum: 1,
      pageSize: 10,
      processName: null,
    });
  }

  searchData = async (value, event) => {
    this.props.getData({
      pageNum: 1,
      pageSize: 10,
      processName: value,
    });
  };

  onChangePage = (pageNum, pageSize) => {
    const { approvalProcessPageCondition } = this.props;
    this.props.getData({ ...approvalProcessPageCondition, pageNum, pageSize });
  };

  render() {
    const { loading, approvalProcessList, approvalProcessPageCondition } = this.props;

    return (
      <GutterWrapper mode="vertical">
        <TinyCard>
          <TinyTable
            rowKey="id"
            actionsWrapperStyle={{ justifyContent: 'space-between' }}
            actions={[
              <Button key="create" type="primary" href={urls.generateBpmCreateUrl()}>
                新建流程
              </Button>,
              <Input.Search
                key="search"
                allowClear
                placeholder="请输入流程名称"
                style={{ width: '250px' }}
                onSearch={this.searchData}
              />,
            ]}
            dataSource={approvalProcessList.list}
            loading={loading}
            columns={getColumns(this)}
            scroll={{ x: 'max-content' }}
            pagination={{
              total: approvalProcessList.total,
              current: approvalProcessPageCondition?.pageNum,
              onChange: this.onChangePage,
              pageSize: approvalProcessPageCondition?.pageSize,
            }}
          />
        </TinyCard>
      </GutterWrapper>
    );
  }
}

const mapStateToProps = ({
  approvalProcessConfig: { loading, approvalProcessPageCondition, approvalProcessList },
}) => ({
  loading,
  approvalProcessPageCondition,
  approvalProcessList,
});

const mapDispatchToProps = {
  getData: approvalProcessList,
  approvalConfigActions,
  syncCommonData: syncCommonDataActionCreator,
  deleteScenesProcess: deleteScenesProcessActionCreator,
};

export default connect(mapStateToProps, mapDispatchToProps)(ApprovalProcessConfig);
