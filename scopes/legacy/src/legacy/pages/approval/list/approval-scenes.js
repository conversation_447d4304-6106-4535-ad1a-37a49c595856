import { Button } from 'antd';
import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { Alert } from '@manyun/base-ui.ui.alert';
import { Collapse } from '@manyun/base-ui.ui.collapse';
import { Input } from '@manyun/base-ui.ui.input';
import { generateApprovalProcessConfigDetailUrl } from '@manyun/bpm.route.bpm-routes';
import { generateBpmEditUrl } from '@manyun/bpm.route.bpm-routes';

import { GutterWrapper, TinyCard } from '@manyun/dc-brain.legacy.components';
import { TinyTable } from '@manyun/dc-brain.legacy.components';
import {
  approvalConfigActions,
  approvalScenesList,
} from '@manyun/dc-brain.legacy.redux/actions/approvalConfigActions';
import { getLocationSearchMap } from '@manyun/dc-brain.legacy.utils';
import * as urls from '@manyun/dc-brain.legacy.utils/urls';

import ScenesEdit from './scenes-edit';

const getColumns = bizSubScenesVoList => [
  {
    title: '场景',
    dataIndex: 'subBizScenes',
    width: 210,
    visible: true,
  },
  {
    title: '说明',
    dataIndex: 'remark',
    visible: true,
  },
  {
    title: '状态',
    dataIndex: 'usable',
    width: 120,
    visible: true,
    render: (__, record) => (record.usable ? '启用' : '停用'),
  },
  {
    title: '关联流程',
    dataIndex: 'processName',
    width: 210,
    visible: true,
    render: (text, record) => (
      <Link to={generateApprovalProcessConfigDetailUrl({ processCode: record.processCode })}>
        {text}
      </Link>
    ),
  },
  {
    title: '负责人',
    dataIndex: 'chargePersonId',
    width: 120,
    render: text => <UserLink userId={text} native />,
  },
  {
    title: '更新时间',
    dataIndex: 'gmtModified',
    width: 210,
    dataType: 'datetime',
  },
  {
    title: '操作',
    key: '__actions',
    fixed: 'right',
    exportable: false,
    width: 160,
    render: (__, record) => {
      let url =
        record?.processCode &&
        record?.processCode !== 'NULL' &&
        record?.processCode !== 'NOT_NEED_APPROVAL'
          ? `${generateBpmEditUrl({ processCode: record.processCode })}?bizScenes=${record.bizScenes}&subBizScenes=${record.subBizScenes}&bizFlowType=${record.bizFlowType}`
          : `${urls.generateBpmCreateUrl()}?bizScenes=${record.bizScenes}&subBizScenes=${record.subBizScenes}&bizFlowType=${record.bizFlowType}&usable=${record.usable}`;

      return (
        <span>
          <ScenesEdit
            value={{
              subScenes: record.subBizScenes,
              usable: record.usable,
              remark: record.remark,
              processCode: record.processCode,
              chargePerson: record.chargePersonId,
            }}
          ></ScenesEdit>
          <Button type="link" href={url}>
            设置流程
          </Button>
        </span>
      );
    },
  },
];

const { Panel } = Collapse;

class ApprovalScenesList extends Component {
  state = {
    scenesListLoading: false,
  };

  componentDidMount() {
    this.search();
  }

  componentDidUpdate(prevProps) {
    const { subBizScenes } = getLocationSearchMap(this.props.location.search, ['subBizScenes']);
    const prevPropsParams = getLocationSearchMap(prevProps.location.search, ['subBizScenes']);

    if (subBizScenes !== prevPropsParams.subBizScenes) {
      this.search();
    }
  }

  search = () => {
    const { subBizScenes } = getLocationSearchMap(this.props.location.search, ['subBizScenes']);
    this.props.setSearchValue(subBizScenes);
    this.props.getData({
      subBizScenes: subBizScenes ? subBizScenes : null,
    });
  };

  searchData = async (value, event) => {
    this.props.getData({
      subBizScenes: value,
    });
  };

  render() {
    const { scenesList, searchValue } = this.props;

    return (
      <GutterWrapper mode="vertical">
        <GutterWrapper flex justifyContent="flex-end">
          <span style={{ display: 'flex', width: '85%', justifyContent: 'space-around' }}>
            <Alert
              message="若需要增加业务场景请联系技术支持"
              type="warning"
              showIcon
              style={{ width: 400 }}
            />
          </span>
          <Input.Search
            allowClear
            placeholder="请输入业务场景名称"
            style={{ width: '15%' }}
            onSearch={this.searchData}
            value={searchValue}
            onChange={({ target: { value } }) => {
              this.props.setSearchValue(value);
            }}
          />
        </GutterWrapper>
        {scenesList.length > 0 && (
          <Collapse
            defaultActiveKey={scenesList.map(({ bizScenes }) => bizScenes)}
            onChange={keys => this.setState({ activePanelKeys: keys })}
          >
            {scenesList.map(({ bizScenes, bizSubScenesVoList }) => (
              <Panel
                header={`${bizScenes ? bizScenes : ''}(${bizSubScenesVoList.length})`}
                key={bizScenes}
              >
                <GutterWrapper mode="vertical">
                  <TinyCard>
                    <TinyTable
                      rowKey="subBizScenes"
                      actionsWrapperStyle={{ justifyContent: 'space-between' }}
                      dataSource={bizSubScenesVoList}
                      columns={getColumns(bizSubScenesVoList)}
                      scroll={{ x: 'max-content' }}
                      pagination={false}
                    />
                  </TinyCard>
                </GutterWrapper>
              </Panel>
            ))}
          </Collapse>
        )}
      </GutterWrapper>
    );
  }
}

const mapStateToProps = ({
  approvalProcessConfig: { scenesListLoading, scenesList, searchValue },
}) => {
  return {
    scenesListLoading,
    scenesList,
    searchValue,
  };
};

const mapDispatchToProps = {
  getData: approvalScenesList,
  setSearchValue: approvalConfigActions.setSearchValue,
};

export default connect(mapStateToProps, mapDispatchToProps)(ApprovalScenesList);
