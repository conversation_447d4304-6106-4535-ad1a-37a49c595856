import { ApiSelect } from '@galiojs/awesome-antd';
import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Radio } from '@manyun/base-ui.ui.radio';

import {
  approvalConfigActions,
  approvalScenesEdit,
} from '@manyun/dc-brain.legacy.redux/actions/approvalConfigActions';
import { approvalConfigService } from '@manyun/dc-brain.legacy.services';

function ScenesEdit({
  editData,
  value: { subScenes, usable, remark, processCode, chargePerson },
  searchCondition,
}) {
  const initialValues = {
    usableResult: usable,
    remarkResult: remark,
    chargePersonId: Number(chargePerson),
  };

  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false);
  const processCodeFormData =
    processCode === 'NOT_NEED_APPROVAL' || processCode === 'NULL' || !processCode
      ? null
      : processCode;

  const handleOK = async () => {
    const values = await form.validateFields();
    try {
      editData({
        params: {
          usable: values?.usableResult,
          remark: values?.remarkResult,
          chargePersonId: values?.chargePersonId,
          subBizScenes: subScenes,
          processCode: processCodeFormData,
        },
        callback: () => {
          setVisible(false);
        },
        searchCondition,
      });
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <>
      <Button style={{ padding: 0, height: 'auto' }} type="link" onClick={() => setVisible(true)}>
        编辑
      </Button>
      {visible && (
        <Modal
          title="编辑"
          destroyOnClose
          visible={visible}
          onCancel={() => setVisible(false)}
          onOk={handleOK}
          okButtonProps={{
            disabled: form.getFieldValue('remarkResult')?.length > 30,
          }}
        >
          <Form
            form={form}
            colon={true}
            initialValues={initialValues}
            layout="horizontal"
            labelCol={{ span: 3 }}
            labelAlign="right"
          >
            <Form.Item label="状态" name="usableResult">
              <Radio.Group>
                <Radio value={true}>启用</Radio>
                <Radio value={false}>停用</Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item label="责任人" name="chargePersonId">
              <UserSelect style={{ width: 216 }} labelInValue={false} userState="in-service" />
            </Form.Item>
            <Form.Item
              label="说明"
              name="remarkResult"
              rules={[{ max: 30, message: '最多输入 30 个字符！' }]}
            >
              <Input.TextArea />
            </Form.Item>
          </Form>
        </Modal>
      )}
    </>
  );
}

const mapStateToProps = ({
  approvalProcessConfig: { scenesEditModalVisible, searchCondition },
}) => ({
  scenesEditModalVisible,
  searchCondition,
});

const mapDispatchToProps = {
  editData: approvalScenesEdit,

  setscenesEditModalVisible: approvalConfigActions.setscenesEditModalVisible,
};

export default connect(mapStateToProps, mapDispatchToProps)(ScenesEdit);
