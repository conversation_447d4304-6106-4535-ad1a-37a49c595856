export function getIsRepeat(data, currentKey, keys) {
  let isRepeat = false;
  const currentData = data.filter(({ key }) => key === currentKey)[0];
  const otherData = data.filter(({ key }) => key !== currentKey);
  otherData.forEach(item => {
    let samekey = [];
    keys.forEach(key => {
      if (item[key] === currentData[key]) {
        samekey = [...samekey, key];
      }
    });
    if (samekey.length === keys.length) {
      isRepeat = true;
    }
  });
  return isRepeat;
}
