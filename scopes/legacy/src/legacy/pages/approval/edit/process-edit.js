import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { Link, useParams } from 'react-router-dom';

import Form from '@ant-design/compatible/es/form';
import MinusCircleFilled from '@ant-design/icons/es/icons/MinusCircleFilled';
import PlusCircleFilled from '@ant-design/icons/es/icons/PlusCircleFilled';
import shortid from 'shortid';

import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { Steps } from '@manyun/base-ui.ui.steps';

import { FooterToolBar, GutterWrapper } from '@manyun/dc-brain.legacy.components';
import { TinyCard } from '@manyun/dc-brain.legacy.components/tiny-card';
import {
  approvalProcessDetail,
  approvalProcessEdit,
} from '@manyun/dc-brain.legacy.redux/actions/approvalConfigActions';
import { generateApprovalProcessConfigListUrl } from '@manyun/dc-brain.legacy.utils/urls';

import UserTaskNode from '../create/user-task-node';

const { Step } = Steps;

function ProcessConfigEdit({
  processConfigDetail,
  form,
  editLoading,
  approvalProcessDetail,
  editProcess,
}) {
  const [data, setData] = useState(processConfigDetail.userTaskNodeList);

  const { getFieldDecorator, setFieldsValue } = form;
  const { processCode } = useParams();
  const { loading } = editLoading;

  useEffect(() => {
    approvalProcessDetail({ processCode });
  }, [approvalProcessDetail, processCode]);

  useEffect(() => {
    setData(processConfigDetail.userTaskNodeList);
  }, [processConfigDetail.userTaskNodeList]);

  useEffect(() => {
    setFieldsValue({ processName: processConfigDetail.processName });
  }, [processConfigDetail, setFieldsValue]);

  const buildHandlers = handlerInfoList => {
    return handlerInfoList.length > 0 ? handlerInfoList.map(({ key }) => key).join(',') : '';
  };

  const confirm = () => {
    form.validateFields((err, value) => {
      if (err) {
        return;
      }
      // 1. API Q
      const editProcessParams = {
        processCode: processCode,
        processName: value.processName,
        processJsonInfo: {
          userTaskNodeList: data,
        },
      };
      // 2. DISPATCH ACTION
      editProcess(editProcessParams);
    });
  };
  return (
    <div>
      <TinyCard title="基本信息" style={{ marginBottom: '40px' }}>
        <Form colon={false} labelCol={{ xxl: 1, xl: 2 }} wrapperCol={{ xxl: 23, xl: 22 }}>
          <Form.Item label="流程名称">
            {getFieldDecorator(`processName`, {
              rules: [
                { required: true, whitespace: true, message: '请输入流程名称' },
                {
                  max: 20,
                  message: '最多输入 20 个字符！',
                },
              ],
            })(<Input style={{ width: 352 }} />)}
          </Form.Item>
          <Form.Item label=" ">
            <Steps direction="vertical" current={-1}>
              {data.map(({ id, name, handlerType, handlerInfoList }, index) => (
                <Step
                  key={id}
                  title={
                    <GutterWrapper mode="horizontal">
                      <GutterWrapper
                        mode="horizontal"
                        style={{ display: 'inline-block', verticalAlign: 'top' }}
                      >
                        <PlusCircleFilled
                          style={{ color: 'var(--color-blue-7)' }}
                          onClick={() => {
                            const copy = [...data];
                            copy.splice(index + 1, 0, {
                              id: shortid(),
                              name: '',
                              handlerType: 'ROLE',
                              handlerInfoList: [],
                            });
                            setData(copy);
                          }}
                        />
                        {data.length > 1 && (
                          <MinusCircleFilled
                            style={{ color: 'var(--color-blue-7)' }}
                            onClick={() => {
                              setData(prev => {
                                if (data.length === 1) {
                                  return data;
                                }
                                const copy = [...prev];
                                copy.splice(index, 1);
                                return copy;
                              });
                            }}
                          />
                        )}
                      </GutterWrapper>
                      <UserTaskNode
                        disabled={false}
                        value={{
                          userTaskName: name,
                          type: handlerType,
                          selected: handlerInfoList,
                        }}
                        onChange={({ userTaskName, type, selected }) => {
                          setData(prev => {
                            const copy = [...prev];
                            copy[index] = {
                              name: userTaskName,
                              handlerType: type,
                              handlers: buildHandlers(selected),
                              handlerInfoList: selected,
                            };
                            return copy;
                          });
                        }}
                      ></UserTaskNode>
                    </GutterWrapper>
                  }
                />
              ))}
            </Steps>
          </Form.Item>
        </Form>
      </TinyCard>
      <FooterToolBar>
        <GutterWrapper>
          <Button
            type="primary"
            htmlType="submit"
            className="create-form-button"
            onClick={confirm}
            loading={loading}
          >
            提交
          </Button>
          <Link to={generateApprovalProcessConfigListUrl}>
            <Button htmlType="cancel" loading={loading}>
              取消
            </Button>
          </Link>
        </GutterWrapper>
      </FooterToolBar>
    </div>
  );
}

const mapStateToProps = ({ approvalProcessConfig: { processConfigDetail, editLoading } }) => ({
  processConfigDetail,
  editLoading,
});

const mapDispatchToProps = {
  approvalProcessDetail,
  editProcess: approvalProcessEdit,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(Form.create({ name: 'process_edit' })(ProcessConfigEdit));
