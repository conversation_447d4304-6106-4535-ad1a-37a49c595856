import React from 'react';

import Form from '@ant-design/compatible/es/form';

import { Input } from '@manyun/base-ui.ui.input';

import RoleOrUserSelect from '@manyun/dc-brain.legacy.components/role-or-user-select';

export default function UserTaskNode({
  disabled,
  value: { userTaskName, type, selected, id },
  onChange,
}) {
  return (
    <div style={{ display: 'inline-block' }}>
      {/* <Form colon={false}> */}
      <Form.Item
        style={{ marginBottom: 0 }}
        validateStatus={userTaskName ? 'success' : 'error'}
        help={userTaskName ? '' : '审批节点名称为必填项！'}
      >
        <Input
          maxLength={20}
          style={{ width: 352 }}
          disabled={disabled}
          // placeholder="请输入审批节点名称"
          value={userTaskName}
          onChange={value => {
            onChange({ userTaskName: value.target.value, type, selected, id });
          }}
        />
      </Form.Item>
      <Form.Item
        style={{ marginBottom: 0 }}
        validateStatus={type && selected.length > 0 ? 'success' : 'error'}
        help={type && selected.length > 0 ? '' : '用户名 | 角色名 为必选项！'}
      >
        <RoleOrUserSelect
          disabled={disabled}
          value={[type, selected]}
          onChange={value => {
            onChange({ userTaskName, type: value[0], selected: value[1] ? value[1] : [], id });
          }}
        />
      </Form.Item>
      {/* </Form> */}
    </div>
  );
}
