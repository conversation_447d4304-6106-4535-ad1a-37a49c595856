import React, { useState } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import Form from '@ant-design/compatible/es/form';
import MinusCircleFilled from '@ant-design/icons/es/icons/MinusCircleFilled';
import PlusCircleFilled from '@ant-design/icons/es/icons/PlusCircleFilled';
import shortid from 'shortid';

import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { Steps } from '@manyun/base-ui.ui.steps';

import { FooterToolBar, GutterWrapper, TinyCard } from '@manyun/dc-brain.legacy.components';
import { approvalProcessCreate } from '@manyun/dc-brain.legacy.redux/actions/approvalConfigActions';
import { generateApprovalProcessConfigListUrl } from '@manyun/dc-brain.legacy.utils/urls';

import UserTaskNode from './user-task-node';

const { Step } = Steps;

const userTasks = [
  {
    id: '1',
    name: '',
    handlerType: 'ROLE',
    handlers: '',
    handlerInfoList: [],
  },
];

function ProcessConfigCreate(props) {
  const [data, setData] = useState(userTasks);
  const { getFieldDecorator } = props.form;

  const confirm = () => {
    props.form.validateFields((err, value) => {
      if (err) {
        return;
      }
      // 1. API Q
      const createProcessParams = {
        processName: value.processName,
        processJsonInfo: {
          userTaskNodeList: data,
        },
      };
      // 2. DISPATCH ACTION
      props.createProcess(createProcessParams);
    });
  };

  const buildHandlers = selected => {
    return selected.length > 0 ? selected.map(({ key }) => key).join(',') : '';
  };

  return (
    <div>
      <TinyCard title="基本信息" style={{ marginBottom: '40px' }}>
        <Form colon={false} labelCol={{ xxl: 1, xl: 2 }} wrapperCol={{ xxl: 23, xl: 22 }}>
          <Form.Item label="流程名称">
            {getFieldDecorator(`processName`, {
              rules: [
                { required: true, whitespace: true, message: '请输入流程名称' },
                {
                  max: 20,
                  message: '最多输入 20 个字符！',
                },
              ],
            })(<Input style={{ width: 352 }} />)}
          </Form.Item>
          <Form.Item label=" ">
            <Steps direction="vertical" current={-1}>
              {data.map(({ id, name, handlerType, handlerInfoList }, index) => (
                <Step
                  key={id}
                  title={
                    <GutterWrapper mode="horizontal">
                      <GutterWrapper
                        mode="horizontal"
                        style={{ display: 'inline-block', verticalAlign: 'top' }}
                      >
                        <PlusCircleFilled
                          style={{ color: 'var(--color-blue-7)' }}
                          onClick={() => {
                            const copy = [...data];
                            copy.splice(index + 1, 0, {
                              id: shortid(),
                              name: '',
                              handlerType: 'ROLE',
                              handlerInfoList: [],
                            });
                            setData(copy);
                          }}
                        />
                        {data.length > 1 && (
                          <MinusCircleFilled
                            style={{ color: 'var(--color-blue-7)' }}
                            onClick={() => {
                              setData(prev => {
                                if (data.length === 1) {
                                  return data;
                                }
                                const copy = [...prev];
                                copy.splice(index, 1);
                                return copy;
                              });
                            }}
                          />
                        )}
                      </GutterWrapper>
                      <UserTaskNode
                        disabled={false}
                        value={{
                          userTaskName: name,
                          type: handlerType,
                          selected: handlerInfoList,
                          id,
                        }}
                        onChange={({ userTaskName, type, selected, id }) => {
                          setData(prev => {
                            const copy = [...prev];
                            copy[index] = {
                              name: userTaskName,
                              handlerType: type,
                              handlers: buildHandlers(selected),
                              handlerInfoList: selected,
                              id,
                            };
                            return copy;
                          });
                        }}
                      />
                    </GutterWrapper>
                  }
                />
              ))}
            </Steps>
          </Form.Item>
        </Form>
      </TinyCard>
      <FooterToolBar>
        <GutterWrapper>
          <Button type="primary" onClick={confirm}>
            提交
          </Button>
          <Link to={generateApprovalProcessConfigListUrl}>
            <Button>取消</Button>
          </Link>
        </GutterWrapper>
      </FooterToolBar>
    </div>
  );
}

const mapStateToProps = ({ approvalProcessConfig: { createLoading } }) => ({
  createLoading,
});

const mapDispatchToProps = {
  createProcess: approvalProcessCreate,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(Form.create({ name: 'process_create' })(ProcessConfigCreate));
