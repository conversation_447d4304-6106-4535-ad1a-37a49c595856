export const URGENCY_TYPE_KEY_MAP = {
  HIGH: 'HIGH',
  MIDDLE: 'MIDDLE',
  LOW: 'LOW',
};
export const URGENCY_TYPE_TEXT_MAP = {
  [URGENCY_TYPE_KEY_MAP.HIGH]: '高',
  [URGENCY_TYPE_KEY_MAP.MIDDLE]: '中',
  [URGENCY_TYPE_KEY_MAP.LOW]: '低',
};
export const URGENCY_TYPE_OPTIONS = [
  {
    value: URGENCY_TYPE_KEY_MAP.HIGH,
    label: URGENCY_TYPE_TEXT_MAP[URGENCY_TYPE_KEY_MAP.HIGH],
  },
  {
    value: URGENCY_TYPE_KEY_MAP.MIDDLE,
    label: URGENCY_TYPE_TEXT_MAP[URGENCY_TYPE_KEY_MAP.MIDDLE],
  },
  {
    value: URGENCY_TYPE_KEY_MAP.LOW,
    label: URGENCY_TYPE_TEXT_MAP[URGENCY_TYPE_KEY_MAP.LOW],
  },
];

export const RISK_LEVEL_TYPE_KEY_MAP = {
  HIGH: 'HIGH',
  MIDDLE: 'MIDDLE',
  LOW: 'LOW',
};
export const RISK_LEVEL_TYPE_TEXT_MAP = {
  [RISK_LEVEL_TYPE_KEY_MAP.HIGH]: '高',
  [RISK_LEVEL_TYPE_KEY_MAP.MIDDLE]: '中',
  [RISK_LEVEL_TYPE_KEY_MAP.LOW]: '低',
};
export const RISK_LEVEL_TYPE_OPTIONS = [
  {
    value: RISK_LEVEL_TYPE_KEY_MAP.HIGH,
    label: RISK_LEVEL_TYPE_TEXT_MAP[RISK_LEVEL_TYPE_KEY_MAP.HIGH],
  },
  {
    value: RISK_LEVEL_TYPE_KEY_MAP.MIDDLE,
    label: RISK_LEVEL_TYPE_TEXT_MAP[RISK_LEVEL_TYPE_KEY_MAP.MIDDLE],
  },
  {
    value: RISK_LEVEL_TYPE_KEY_MAP.LOW,
    label: RISK_LEVEL_TYPE_TEXT_MAP[RISK_LEVEL_TYPE_KEY_MAP.LOW],
  },
];

export const LEAVE_TYPE_KEY_MAP = {
  ANNUAL_LEAVE: 'ANNUAL_LEAVE',
  PERSONAL_LEAVE: 'PERSONAL_LEAVE',
  SICK_LEAVE: 'SICK_LEAVE',
  BREAK_OFF: 'BREAK_OFF',
  MATERNITY_LEAVE: 'MATERNITY_LEAVE',
  PATERNITY_LEAVE: 'PATERNITY_LEAVE',
  MARRIAGE_HOLIDAY: 'MARRIAGE_HOLIDAY',
  FUNERAL_LEAVE: 'FUNERAL_LEAVE',
  INJURY_LEAVE: 'INJURY_LEAVE',
  ROAD_LEAVE: 'ROAD_LEAVE',
};
export const LEAVE_TYPE_TEXT_MAP = {
  [LEAVE_TYPE_KEY_MAP.ANNUAL_LEAVE]: '年假',
  [LEAVE_TYPE_KEY_MAP.PERSONAL_LEAVE]: '事假',
  [LEAVE_TYPE_KEY_MAP.SICK_LEAVE]: '病假',
  [LEAVE_TYPE_KEY_MAP.BREAK_OFF]: '调休',
  [LEAVE_TYPE_KEY_MAP.MATERNITY_LEAVE]: '产假',
  [LEAVE_TYPE_KEY_MAP.PATERNITY_LEAVE]: '陪产假',
  [LEAVE_TYPE_KEY_MAP.FUNERAL_LEAVE]: '丧假',
  [LEAVE_TYPE_KEY_MAP.MARRIAGE_HOLIDAY]: '婚假',
  [LEAVE_TYPE_KEY_MAP.INJURY_LEAVE]: '工伤假',
  [LEAVE_TYPE_KEY_MAP.ROAD_LEAVE]: '路途假',
};
export const LEAVE_TYPE_OPTIONS = [
  {
    value: LEAVE_TYPE_KEY_MAP.ANNUAL_LEAVE,
    label: LEAVE_TYPE_TEXT_MAP[LEAVE_TYPE_KEY_MAP.ANNUAL_LEAVE],
  },
  {
    value: LEAVE_TYPE_KEY_MAP.PERSONAL_LEAVE,
    label: LEAVE_TYPE_TEXT_MAP[LEAVE_TYPE_KEY_MAP.PERSONAL_LEAVE],
  },
  {
    value: LEAVE_TYPE_KEY_MAP.SICK_LEAVE,
    label: LEAVE_TYPE_TEXT_MAP[LEAVE_TYPE_KEY_MAP.SICK_LEAVE],
  },
  {
    value: LEAVE_TYPE_KEY_MAP.BREAK_OFF,
    label: LEAVE_TYPE_TEXT_MAP[LEAVE_TYPE_KEY_MAP.BREAK_OFF],
  },
  {
    value: LEAVE_TYPE_KEY_MAP.MATERNITY_LEAVE,
    label: LEAVE_TYPE_TEXT_MAP[LEAVE_TYPE_KEY_MAP.MATERNITY_LEAVE],
  },
  {
    value: LEAVE_TYPE_KEY_MAP.MARRIAGE_HOLIDAY,
    label: LEAVE_TYPE_TEXT_MAP[LEAVE_TYPE_KEY_MAP.MARRIAGE_HOLIDAY],
  },
  {
    value: LEAVE_TYPE_KEY_MAP.FUNERAL_LEAVE,
    label: LEAVE_TYPE_TEXT_MAP[LEAVE_TYPE_KEY_MAP.FUNERAL_LEAVE],
  },
  {
    value: LEAVE_TYPE_KEY_MAP.INJURY_LEAVE,
    label: LEAVE_TYPE_TEXT_MAP[LEAVE_TYPE_KEY_MAP.INJURY_LEAVE],
  },
  {
    value: LEAVE_TYPE_KEY_MAP.ROAD_LEAVE,
    label: LEAVE_TYPE_TEXT_MAP[LEAVE_TYPE_KEY_MAP.ROAD_LEAVE],
  },
];
