import React, { useEffect } from 'react';
import { connect } from 'react-redux';
import { useParams } from 'react-router-dom';

import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import { BpmViewer } from '@manyun/bpm.ui.bpm-viewer';

import { TinyCard } from '@manyun/dc-brain.legacy.components/tiny-card';
import { approvalProcessDetail } from '@manyun/dc-brain.legacy.redux/actions/approvalConfigActions';

function ProcessConfigDatail({ approvalProcessDetail, processConfigDetail }) {
  const { processCode } = useParams();

  useEffect(() => {
    approvalProcessDetail({ processCode });
  }, [approvalProcessDetail, processCode]);

  return (
    <div>
      <TinyCard title="基本信息" style={{ marginBottom: '40px' }}>
        <Space direction="vertical" style={{ height: '100%', width: '100%' }}>
          <Typography.Text type="secondary">{processConfigDetail.processName}</Typography.Text>

          {processConfigDetail.processXml && (
            <BpmViewer xml={processConfigDetail.processXml} bpmInstance={undefined} />
          )}
        </Space>
      </TinyCard>
    </div>
  );
}

const mapStateToProps = ({ approvalProcessConfig: { processConfigDetail } }) => ({
  processConfigDetail,
});

const mapDispatchToProps = {
  approvalProcessDetail,
};

export default connect(mapStateToProps, mapDispatchToProps)(ProcessConfigDatail);
