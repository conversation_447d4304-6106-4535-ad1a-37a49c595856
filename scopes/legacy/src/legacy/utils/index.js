import get from 'lodash/get';
import { normalize, schema } from 'normalizr';

import { getDiPointValueText } from '@manyun/monitoring.model.point';
import * as spaceGuidUtil from '@manyun/resource-hub.util.space-guid';
import { INVALID_REQUEST_ERROR_CODES } from '@manyun/service.request';

import {
  BLANK_PLACEHOLDER,
  SUBSCRIPTION_TYPE,
  YES_OR_NO_TEXT_MAP,
} from '@manyun/dc-brain.legacy.constants';
import { LIMIT_TEXT_MAP, LIMIT_VALUE_MAP } from '@manyun/dc-brain.legacy.constants/alarm';

/**
 * 格式化百分比文案
 * @param {number} value 需要计算百分比的值（不要乘以100，此方法内部会乘）
 * @param {0|1|2|3|4|5|6|7|8|9|10|11|12|13|14|15|16|17|18|19|20} digits 保留小数位数（会透传给 Number#toFixed(digits)）
 */
export function toPercentText(value, digits) {
  return (Number.parseFloat(value) * 100).toFixed(digits) + '%';
}

/**
 * @deprecated Use `@manyun/dc-brain.util.flatten-tree-data` instead
 * @param {*} treeData
 * @returns
 */
export function flattenTreeData(treeData) {
  const dataList = [];
  loop(treeData);
  function loop(data) {
    for (let i = 0; i < data.length; i++) {
      const { children, ...node } = data[i];
      if (children) {
        loop(children);
      }
      dataList.push(node);
    }
  }
  return dataList;
}

/**
 * @typedef { 'value' | 'key' } PropName
 */

/**
 * Given an `Object`, returns its own enumerable properties in the form of `Array<{ label: any; value: any }>`.
 * @param {object} object An object you want to transform it into an array.
 * @param {object} [options] Options.
 * @param {PropName} [options.labelProp='value'] Assign `key` or `value` into `label` property.
 * @param {PropName} [options.valueProp='key'] Assign `key` or `value` into `value` property.
 */
export function getObjectOwnProps(object, { labelProp = 'value', valueProp = 'key' } = {}) {
  const map = new Map();
  return Object.keys(object).map(key => {
    const value = object[key];
    map.set('key', key);
    map.set('value', value);
    const result = { label: map.get(labelProp), value: map.get(valueProp) };

    return result;
  });
}

export function extractSubDomain(hostname = window.location.hostname) {
  const regex = /[a-zA-Z]+/;
  const m = regex.exec(hostname);
  if (m === null) {
    return null;
  }
  return m[0];
}

// /**
//  * @enum {'user' | 'role' | 'group'}
//  */
// const ACL_TYPE_MAP = {
//   USER: 'user',
//   ROLE: 'role',
//   USER_GROUP: 'group',
// };

// const ACL_DOMAIN = 'ondcbase.com'; // 用户账户控制模块所在的域名

// /**
//  * 组装用户、角色、用户组 ID 后缀（域名）
//  * @param {ACL_TYPE_MAP} aclType
//  */
// export function getAclIdPostfix(aclType) {
//   const aclTypeTxt = aclType === ACL_TYPE_MAP.USER ? '' : `${aclType}.`;
//   const tenantId = extractSubDomain();

//   return `@${aclTypeTxt}${tenantId}.${ACL_DOMAIN}`;
// }

// /**
//  * 1600 * 900分辨率下高度的像素百分比
//  * @param {Number} number
//  */
// export function getPixelPercentageHeight(number) {
//   const pixel = (number / 900) * 100;
//   return pixel + '%';
// }

/**
 * 1600 * 900分辨率下宽度的像素百分比
 * @param {Number} number
 * @param {boolean} includedScrollbar 如果会出现纵向滚动条的话，这个参数要传 `true`。
 */
export function getPixelPercentageWidth(number, includedScrollbar = false) {
  let width = 1600 - 22 * 2; /* paddingLeft & paddingRight */
  if (includedScrollbar) {
    width = width - 6 /* scrollbar's width */;
  }
  const p = (number / width) * 100 + '%';

  return p;
}

export function buildUrlSearchParams(variablesMap) {
  const urlSearchParams = new URLSearchParams();

  for (const [name, value] of Object.entries(variablesMap)) {
    if (typeof value === 'string') {
      urlSearchParams.append(name, value);
    }
  }

  return urlSearchParams;
}

/** @deprecated Use `@manyun/base-ui.util.query-string` instead */
export function getLocationSearchMap(locationSearch, keys = []) {
  const urlSearchParams = new URLSearchParams(locationSearch);

  return keys.reduce((map, key) => {
    map[key] = urlSearchParams.get(key);

    return map;
  }, {});
}

/**
 * 将 true/false 转换成对应的展示文案
 * @param {boolean} yesOrNo （布尔值 ）
 */
export function getYesOrNoText(yesOrNo) {
  if (typeof yesOrNo !== 'boolean') {
    console.warn(`boolean yesOrNo(${typeof yesOrNo}) expected.`);
    return;
  }
  if (yesOrNo === true) {
    return YES_OR_NO_TEXT_MAP.YES;
  }
  return YES_OR_NO_TEXT_MAP.NO;
}

/**
 * 使用扁平的数据生成🌲形数据
 *
 * @typedef {(nodeData: any) => string} OptionsKeyFunc
 *
 * @param {any[]} data
 * @param {object} [options]
 * @param {string|OptionsKeyFunc} [options.key='key']
 * @param {string} [options.typeKey='type']
 * @param {string} [options.parentKey='parentKey']
 * @param {string[]} [options.nodeTypes=['root','leaf']]
 * @param {(node: any) => any} [options.getNode=n=>n]
 * @param {number} [startDepth=0] 不需要传这个参数，内部递归时会传递
 * @param {string} [curParentKey] 不需要传这个参数，内部递归时会传递
 * @param {{ [x: string]: any[] }} [typedDataMap] 不需要传这个参数，内部递归时会传递
 */
export function generateTreeData(data, options, startDepth = 0, curParentKey, typedDataMap) {
  if (!(Array.isArray(data) && data.length)) {
    return [];
  }

  const {
    key = 'key',
    typeKey = 'type',
    parentKey = 'parentKey',
    nodeTypes = ['root', 'leaf'],
    getNode = n => n,
  } = options;

  if (startDepth === 0) {
    typedDataMap = data.reduce((map, item) => {
      if (!map[item[typeKey]]) {
        map[item[typeKey]] = [item];
      } else {
        map[item[typeKey]].push(item);
      }

      return map;
    }, {});
  }

  const typedData = get(typedDataMap, nodeTypes[startDepth], []);

  return typedData.reduce((treeData, item) => {
    if (startDepth > 0 && item[parentKey] !== curParentKey) {
      // 不是当前 `parent` 的孩子
      return treeData;
    }
    let children = [];
    if (startDepth + 1 < nodeTypes.length) {
      children = generateTreeData(
        data,
        options,
        startDepth + 1,
        typeof key == 'function' ? key(item) : item[key],
        typedDataMap
      );
    }
    children = children.length ? children : null;

    treeData.push({
      children,
      ...getNode({ ...item, children }),
    });

    return treeData;
  }, []);
}

/**
 * 处理订阅等级展示
 * @param {string} type  订阅类型
 * @param {boolean} cumulative  是否累加1
 *
 */
export function processingSubscriptionLevel({ text = '', type, cumulative = false }) {
  if (!text) {
    return text;
  }
  let levelText;
  if (typeof text === 'number') {
    levelText = text;
  } else {
    const index = text.lastIndexOf('_');
    if (index < 0) {
      return text;
    }
    levelText = text.substring(index + 1, text.length);
  }
  if (cumulative) {
    levelText = Number(levelText) + 1;
  }
  if (type === SUBSCRIPTION_TYPE.ALARM_SUBSCRIBE) {
    return levelText;
  }

  if (type === SUBSCRIPTION_TYPE.EVENT_SUBSCRIBE) {
    return levelText;
  }
}

/**
 * @typedef {{ operator: LIMIT_VALUE_MAP, limit: number|string }} Limit
 */

/**
 * 解析监控项阈值（DI：正常取值范围；AI：上下限）
 * @param {string} triggerRule
 * @return {null|number[]|{lowerLimit: null|Limit; upperLimit: null|Limit}}
 */
export function parseTriggerRule(triggerRule) {
  if (!triggerRule || typeof triggerRule !== 'string') {
    return null;
  }

  // DI
  // example: in=1,2,3
  if (triggerRule.startsWith('in=')) {
    const [, limitStr] = triggerRule.split('=');

    return limitStr.split(',').map(Number); // DI 量的取值范围是数字
  }

  // AI
  if (!triggerRule.includes(';')) {
    // 只配置了下限
    // example1: gt=1
    // example2: ge=1
    if (
      triggerRule.includes(LIMIT_VALUE_MAP.GREATER_THAN) ||
      triggerRule.includes(LIMIT_VALUE_MAP.GREATER_THAN_OR_EQUALS)
    ) {
      const [operator, lowerLimit] = triggerRule.split('=');

      return {
        lowerLimit: { operator, limit: lowerLimit },
        upperLimit: null,
      };
    }

    // 只配置了上限
    // example1: lt=10
    // example2: le=10
    if (
      triggerRule.includes(LIMIT_VALUE_MAP.LESS_THAN) ||
      triggerRule.includes(LIMIT_VALUE_MAP.LESS_THAN_OR_EQUALS)
    ) {
      const [operator, upperLimit] = triggerRule.split('=');

      return {
        lowerLimit: null,
        upperLimit: { operator, limit: upperLimit },
      };
    }
  }

  // 既配置了下限，又配置了上限
  // example1: gt=1;lt=10
  // example2: gt=1;lt=NOMINAL_VOLTAGE
  const [lowerLimitStr, upperLimitStr] = triggerRule.split(';');
  const [lowerLimitOperator, lowerLimit] = lowerLimitStr.split('=');
  const [upperLimitOperator, upperLimit] = upperLimitStr.split('=');

  return {
    lowerLimit: {
      operator: lowerLimitOperator,
      limit: tryFixLimitType(lowerLimit),
    },
    upperLimit: {
      operator: upperLimitOperator,
      limit: tryFixLimitType(upperLimit),
    },
  };
}

/**
 * 尝试修正监控项阈值的数据类型
 *
 * - 如果阈值为数字字符串，则转换成数字
 * - 反之，直接返回传入的阈值
 *
 * @param {string|number} limit
 * @returns
 */
function tryFixLimitType(limit) {
  const limitNumber = Number(limit);

  // 如果不是数字，则认为是规格编码字符串
  if (Number.isNaN(limitNumber)) {
    return limit;
  }

  return limitNumber;
}

/**
 * 将 `parseTriggerRule` 返回值处理成用户可读的字符串
 * @param {{null|number[]|{lowerLimit: null|Limit; upperLimit: null|Limit}}} limits
 * @param {{ validLimits: string[]; unit: string|null; specMappings: Record<string, object>|undefined }} options
 */
export function getReadableTriggerRule(limits, { validLimits, unit, specMappings }) {
  // console.log(limits)
  if (limits === null) {
    return BLANK_PLACEHOLDER;
  }
  if (Array.isArray(limits)) {
    if (Array.isArray(validLimits)) {
      return limits.map(limit => getDiPointValueText(limit, validLimits)).join(',');
    }
    return limits.join(',');
  }
  const { lowerLimit, upperLimit } = limits;
  let limitStr = '';
  if (lowerLimit) {
    const limitTxt = getLimitText(lowerLimit.limit, specMappings);
    limitStr += LIMIT_TEXT_MAP[lowerLimit.operator] + limitTxt + (unit || '');
  }
  if (limitStr !== '') {
    limitStr += ',';
  }
  if (upperLimit) {
    const limitTxt = getLimitText(upperLimit.limit, specMappings);
    limitStr += LIMIT_TEXT_MAP[upperLimit.operator] + limitTxt + (unit || '');
  }

  return limitStr;
}

/**
 * 处理监控项阈值文案（如果是规格编码，会尝试将规格编码转换成规格名称）
 *
 * @param {string|number} limit 监控项配置的阈值上限或下限
 * @param {Record<string, object>|undefined} specMappings 某个设备类型下的规格映射
 * @returns {string|number}
 */
function getLimitText(limit, specMappings) {
  if (typeof limit == 'number') {
    return limit;
  }

  if (typeof limit != 'string') {
    throw new Error(`Unexpected monitoring item thresholds limit: `, limit);
  }

  const spec = specMappings?.[limit];
  if (!spec) {
    return limit;
  }

  return spec.specName;
}

/**
 * 映射 事件类型
 */
export function getEventTypeName(eventTypesMap, code, type) {
  const eventTypes = eventTypesMap[code];
  if (!(Array.isArray(eventTypes) && eventTypes.length)) {
    return code;
  }
  const eventType = eventTypes.find(({ metaType }) => metaType === type);
  if (!eventType) {
    return code;
  }
  return eventType.metaName;
}

/**
 * 序号 倒序
 */
export function serialNumberInReverse({ total, pageNum, pageSize, index }) {
  const number = total - ((pageNum - 1) * pageSize + index);
  return number;
}

/**
 * 用于判断 `errorCode` 所属的 `API` 请求是否无效
 * @param {number|string} errorCode HTTP Status code / Customized HTTP `response.errCode`
 */
export function isInvalidRequest(errorCode) {
  return INVALID_REQUEST_ERROR_CODES.includes(errorCode);
}

/**
 * @deprecated Use `@manyun/resource-hub.util.space-guid` instead
 */
export const getSpaceGuid = spaceGuidUtil.getSpaceGuid;
/**
 * @deprecated Use `@manyun/resource-hub.util.space-guid` instead
 */
export const getSpaceGuidMap = spaceGuidUtil.getSpaceGuidMap;

/**
 * @param {any} text Table cell text
 * @param {number} index Table row index
 * @param {{ datasource: any[]; dataIndex: string; rowSpansGroupByDataIndex: Record<string, number>; currentText: string }} options
 * @returns
 */
export function getRowSpan(
  text,
  index,
  { datasource, dataIndex, rowSpansGroupByDataIndex, currentText }
) {
  // const normalizedList = checkItemNormalized(datasource, dataIndex);
  if (index === 0 && rowSpansGroupByDataIndex[text] !== undefined) {
    return rowSpansGroupByDataIndex[text];
  }

  const prevRecord = datasource[index - 1];
  if (prevRecord?.[dataIndex] === text || prevRecord?.[dataIndex] === currentText) {
    return 0;
  }

  if (rowSpansGroupByDataIndex[text] !== undefined) {
    return rowSpansGroupByDataIndex[text];
  }

  return 0;
}

export function checkItemNormalized(checkItems, name) {
  const dataSchema = [
    new schema.Entity(
      'normalizedList',
      {},
      {
        idAttribute: name,
        processStrategy: data => {
          return [
            {
              ...data,
            },
          ];
        },
        mergeStrategy: (entityA, entityB) => {
          return [...entityA, ...entityB];
        },
      }
    ),
  ];
  const normalizedList = normalize(checkItems, dataSchema).entities.normalizedList;
  return normalizedList;
}
