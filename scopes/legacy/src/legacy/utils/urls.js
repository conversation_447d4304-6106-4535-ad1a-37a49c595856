import { generateSpaceOrDeviceRoutePath } from '@manyun/monitoring.route.monitoring-routes';
import {
  generateDeviceListUrl as generateDeviceListUrl2,
  generateSpareListUrl as generateSpareListUrl2,
} from '@manyun/resource-hub.route.resource-routes';

import * as urls from '@manyun/dc-brain.legacy.constants/urls';

import { buildUrlSearchParams } from '.';

/**
 * 生成某个 ID 的监控项配置页面的 URL（不应 `export` ，应使用 `generateSpecificMonitorItemConfigLocation`）
 * @param {object} variables
 * @param {string} variables.configId 配置 ID
 */
const generateSpecificMonitorItemConfigUrl = ({ configId }) =>
  urls.SPECIFIC_MONITOR_ITEM_CONFIG.replace(':configId', configId);

/**
 * 生成某个 ID 的监控项配置页面的Location
 * @param {object} variables
 * @param {string} variables.configId 配置 ID
 * @param {string} variables.deviceType
 * @param {string} variables.pointCode
 */
export const generateSpecificMonitorItemConfigLocation = ({ configId, deviceType, pointCode }) => {
  let search = '?';
  if (deviceType) {
    search += `deviceType=${deviceType}&`;
  }
  if (pointCode) {
    search += `pointCode=${pointCode}`;
  }
  return {
    pathname: generateSpecificMonitorItemConfigUrl({ configId }),
    search,
  };
};

/**
 * 角色详情页面的 URL
 */
export const generateRoleConfigUrl = () => urls.ROLE_ITEM_CONFIG;

/**
 * 设备列表页面的 URL
 * @param {object} variables
 * @param {string} [variables.deviceGuid] 设备名称
 * @param {string} [variables.deviceName] 设备名称
 * @param {string} [variables.deviceTag] 设备名称（废弃）
 * @param {string} [variables.insertVersion] 导入设备的版本（从操作日志跳转过来可能会携带此参数）
 * @param {string} [variables.deviceType] 设备类型
 * @param {string} [variables.productModel] 型号名称
 * @param {string} [variables.vendor] 厂商名称
 */
export const generateDeviceListUrl = generateDeviceListUrl2;
// export const generateDeviceListUrl = ({
//   deviceGuid,
//   deviceName,
//   deviceTag,
//   insertVersion,
//   deviceType,
//   productModel,
//   vendor,
// } = {}) => {
//   const pathname = urls.EQUIPMENT_LIST;

//   const searchParams = buildUrlSearchParams({
//     guid: deviceGuid,
//     name: deviceName || deviceTag,
//     insertVersion,
//     deviceType,
//     productModel,
//     vendor,
//   });
//   const search = searchParams.toString();
//   if (!search) {
//     return pathname;
//   }

//   return pathname + '?' + search;
// };

/**
 * 新建设备页面的 URL
 * @param {object} variables
 */
export const generateCreateEquipmentConfigUrl = () => urls.SPECIFIC_CREATE_EQUIPMENT_ITEM;

/**
 * 编辑设备页面的 URL
 * @param {object} variables
 */
export const generateEditEquipmentConfigUrl = ({ id }) =>
  urls.SPECIFIC_EDIT_EQUIPMENT_ITEM.replace(':id', id);

/**
 * 新建耗材页面的 URL
 * @param {object} variables
 */
export const generateCreateSpareConfigUrl = () => urls.SPECIFIC_CREATE_SPARE_ITEM;

/**
 * 耗材列表页面的 URL
 * @param {object} variables
 * @param {string} [variables.deviceType] 设备类型
 * @param {string} [variables.productModel] 型号名称
 * @param {string} [variables.vendor] 厂商名称
 */
export const generateSpareListUrl = generateSpareListUrl2;

/**
 * 审批流程配置列表页面 URL
 * @param {object} variables
 */
//export const generateApprovalProcessConfigListUrl = () => urls.APPROVAL_PROCESS_CONFIG_LIST;

/**
 * 审批流程配置创建页面 URL
 * @param {object} variables
 */
export const generateApprovalProcessConfigCreateUrl = () => urls.APPROVAL_PROCESS_CONFIG_CREATE;

/**
 * 流程设计器新建流程模型页面 URL
 * @param {object} variables
 */
export const generateBpmCreateUrl = () => urls.BPM_CREATE;
/**
 * 流程设计器编辑流程模型页面 URL
 * @param {object} variables
 */
export const generateBpmEditUrl = ({ processCode }) => urls.BPM_EDIT.replace(':code', processCode);

/**
 * 审批流程配置详情页面 URL
 * @param {object} variables
 */
export const generateApprovalProcessConfigDetailUrl = ({ processCode }) =>
  urls.APPROVAL_PROCESS_CONFIG_DETAIL.replace(':processCode', processCode);

/**
 * 审批流程配置详情页面 URL
 * @param {object} variables
 */
export const generateApprovalProcessConfigEditUrl = ({ processCode }) =>
  urls.APPROVAL_PROCESS_CONFIG_EDIT.replace(':processCode', processCode);

/**
 * 审批业务场景 URL
 * @param {object} variables
 */
export const generateSceneConfigListUrl = ({ subBizScenes }) => ({
  pathname: urls.APPROVAL_SCENES_LIST,
  search: `?subBizScenes=${subBizScenes}`,
});

/**
 * 模板组列表页面的 URL
 * @param {object} variables
 */
export const generateTemplteGroupListUrl = () => urls.TEMPLATE_GROUP_VIEW_LIST;

/**
 * 生成某个机房的模板组的 URL
 * @param {string} [variables.idc] 机房代码
 */
export const generateTemplteGroupListUrlByIdc = ({ idc }) => ({
  pathname: generateTemplteGroupListUrl(),
  search: `?idc=${idc}`,
});

/**
 * 新建模板组页面的 URL
 * @param {object} variables
 */
export const generateCreateTemplteGroupConfigUrl = () => urls.CREATE_TEMPLATE_GROUP_CONFIG;

/**
 * 模板组生效域页面的 URL
 * @param {object} variables
 */
export const generateTemplteGroupAreaConfigUrl = () => urls.TEMPLATE_AREA_CONFIG;

/**
 * 模板组编辑页面的 URL
 * @param {object} variables
 * @param {object} variables.id 模板组代码
 */
export const generateEditTemplteGroupConfigUrl = ({ id }) =>
  urls.EDIT_TEMPLATE_GROUP_CONFIG.replace(':id', id);

/**
 * 生成 模板组编辑的 Location
 * @param {object} variables
 * @param {string} [variables.name] 模板组代码
 * @param {string} [variables.available] 是否启用
 */
export const generateEditTemplteGroupConfigLocation = ({ id, name = '', available = '' }) => ({
  pathname: generateEditTemplteGroupConfigUrl({ id }),
  search: `?name=${name}&available=${available}`,
});

/**
 * 查看模板组页面的 URL
 * @param {object} variables
 * @param {object} variables.id 模板组代码
 */
export const generateViewTemplteGroupConfigUrl = ({ id }) =>
  urls.VIEW_TEMPLATE_GROUP_CONFIG.replace(':id', id);

/**
 * 生成查看模板组页面的 location
 * @param {object} variables
 * @param {object} variables.id 模板组代码
 */
export const generateViewTemplteGroupConfigLocation = ({
  id,
  name = '',
  available = '',
  lastOperatorName = '',
}) => ({
  pathname: generateViewTemplteGroupConfigUrl({ id }),
  search: `?name=${name}&available=${available}&lastOperatorName=${lastOperatorName}`,
});

/**
 * 告警模板详情页面 URL
 * @param {object} variables
 * @param {string} variables.id 告警id
 */
export const generateAlarmConfigurationTemplateDetailUrl = ({ id }) =>
  urls.ALARM_CONFIGURATION_TEMPLATE_DETAIL.replace(':id', id);

/**
 * 告警模板列表页面 URL
 * @param {object} variables
 * @param {string[]} [variables.deviceTypes] 设备类型
 */
export const generateAlarmConfigurationTemplateListUrl = ({ deviceTypes }) => {
  let search = '';

  if (Array.isArray(deviceTypes) && deviceTypes.length) {
    search += `?deviceTypes=${deviceTypes.join(',')}`;
  }

  return urls.ALARM_CONFIGURATION_TEMPLATE_LIST + search;
};

/**
 * 公告列表的 URL
 */
export const generateNoticeManageListUrl = () => urls.NOTICE_MANAGE_LIST;

/**
 * 楼栋管理的 URL
 */
export const generateBasicResourcesBuildingUrl = () => urls.BASIC_RESOURCES_BUILDING;

/**
 * 生成楼栋管理的 Location
 * @param {object} variables
 * @param {string} [variables.idc] 机房
 * @param {string} [variables.block] 楼栋
 */
export const generateBasicResourcesBuildingUrlLocation = ({ idc, block }) => ({
  pathname: generateBasicResourcesBuildingUrl(),
  search: `?idc=${idc}&block=${block}`,
});

/**
 * 机房管理的 URL
 */
export const generateBasicResourcesIdcUrl = ({ idc, name = '' }) => {
  return {
    pathname: urls.BASIC_RESOURCES_IDC,
    search: `?idc=${idc}&name=${name}`,
  };
};

/**
 * 设备类型详情页面 URL
 * @param {object} variables
 * @param {string} variables.id
 */
export const generateDeviceTypeInfoUrl = ({ id }) => urls.DEVICE_TYPE_DETAIL.replace(':id', id);

/**
 * 测点管理的 URL
 * @param {object} variables
 * @param {string} [variables.deviceType] 设备类型
 * @param {string} [variables.deviceName] 类型名称
 */
export const generatePointUrl = ({ deviceType, deviceName } = {}) => {
  const pathname = urls.DOPOINT_LIST;
  const searchParams = buildUrlSearchParams({
    deviceType,
    deviceName,
  });
  const search = searchParams.toString();
  if (!search) {
    return pathname;
  }

  return pathname + '?' + search;
};

/**
 * 用户组管理详情的 URL
 * @param {object} variables
 * @param {string} variables.id 用户组 ID
 * @param {string} variables.defaultTab 默认选中哪一个Tab
 *
 */
export const generateUserGroupManageDetailUrl = ({ id }) =>
  urls.USER_GROUP_MANAGE_DETAIL.replace(':id', id);

/**
 * 用户组管理详情的 Location
 * @param {string} [variables.id] 用户组 ID
 * @param {string} variables.defaultTab 默认选中哪一个Tab
 *
 */
export const generateUserGroupManageDetailUrlLocation = ({ id, defaultTab }) => ({
  pathname: generateUserGroupManageDetailUrl({ id }),
  search: `?defaultTab=${defaultTab}`,
});

/**
 * 用户组管理列表的 URL
 */
export const generateUserGroupManageListUrl = () => urls.USER_GROUP_MANAGE_LIST;

/**
 * 用户管理新建的 URL
 */
export const generateUserManageNewUrl = () => urls.USER_MANAGE_NEW;

/**
 * 用户管理详情的 URL
 * @param {object} variables
 * @param {string} variables.id 用户组 ID
 * @param {string} variables.loginName 用户loginName
 */
export const generateUserManageDetailUrl = () => urls.USER_MANAGE_DETAIL;

/**
 * 用户管理详情的 Location
 * @param {string} [variables.id] 用户id
 * @param {string} [variables.loginName] 用户 loginName
 * @param {'1'|'2'} [variables.defaultTab] 默认选中Tab（1-用户管理，2-加入的组）
 */
export const generateUserManageDetailUrlLocation = ({
  id = '',
  loginName = '',
  defaultTab = '1',
}) => ({
  pathname: generateUserManageDetailUrl(),
  search: `?id=${id}&loginName=${loginName}&defaultTab=${defaultTab}`,
});

/**
 * 用户管理列表的 URL
 */
export const generateUserManageListUrl = () => urls.USER_MANAGE_LIST;

/**
 * @param {object} variables
 * @param {string} variables.idc
 * @param {string} variables.block
 * @param {string} variables.topologyType
 * @param {'new'|'edit'} variables.mode
 * @returns
 */
export const generateTopologyGraphixUrl = ({ idc, block, topologyType, mode }) =>
  urls.TOPOLOGY_GRAPHIX.replace(':topologyType', topologyType)
    .replace(':idc', idc)
    .replace(':block', block)
    .replace(':mode', mode);

//#endregion

/**
 * 生成告警配置的 URL
 */
export const generateMonitorListUrl = () => urls.MONITOR_ITEM_CONFIG_LIST;

/**
 * 生成新建收敛配置的 URL
 */
export const generateNewMonitorConvergenceConfigUrl = () => urls.NEW_MERGED_MONITOR_CONFIG;

/**
 * 生成查看某个 id 对应的收敛配置详情
 * @param {string|number} id 收敛配置 id
 */
export const generateSpecificMonitorConfigUrl = ({ id }) =>
  urls.SPECIFIC_MERGED_MONITOR_CONFIG.replace(':id', id);

/**
 * 生成编辑某个 id 对应的收敛配置详情
 * @param {string|number} id 收敛配置 id
 */
export const generateEditSpecificMonitorConfigUrl = ({ id }) =>
  urls.EDIT_SPECIFIC_MERGED_MONITOR_CONFIG.replace(':id', id);

/**
 * 新建机柜页面的 URL
 * @param {object} variables
 */
export const generateEditCabinetUrl = () => urls.SPECIFIC_CREATE_CABINET_ITEM;

/**
 * 机柜列表
 * @param {object} variables
 * @param {string} [variables.insertVersion] 导入机柜的版本（从操作日志跳转过来可能会携带此参数）
 * @param {string} [variables.guid] 机柜guid（从操作日志跳转过来可能携带此参数）
 */
export const generateGridListUrl = variables => {
  const pathname = urls.CABINET_LIST;

  const searchParams = buildUrlSearchParams(variables);
  const search = searchParams.toString();
  if (!search) {
    return pathname;
  }

  return pathname + '?' + search;
};

//#region 运行中心/事件管理

/**
 * 新增事件类型页面 URL
 */
export const generateCreateMetaConfigUrl = () => urls.METADATA_CONFIGURATION;

/**
 * 事件编辑
 * @param {obejct} variables
 * @param {string|number} variables.id 事件 id
 */
export const generateEventCenterEdit = ({ id }) => urls.EVENT_CENTER_EDIT.replace(':id', id);

/**
 * @deprecated 请使用ticket route 下的generateEventDetailRoutePath
 * 事件详情
 * @param {object} variables
 * @param {string} variables.id
 */
export const generateEventCenterDetail = ({ id }) => urls.EVENT_CENTER_DETAIL.replace(':id', id);

//#endregion

/**
 * 聚合加工测点编辑页面的 URL
 * @param {object} variables
 * @param {object} variables.id 模板组代码
 */
export const generateEditMergedPreesedPointConfigUrl = ({ deviceType, pointCode }) =>
  urls.MERGES_PROCESSED_POINT_EDIT.replace(':deviceType', deviceType).replace(
    ':pointCode',
    pointCode
  );

/**
 * 变更模板详情
 * @deprecated 请使用ticket scope下routes中的方法
 * @param {object} variables
 * @param {string} variables.id
 */
export const generateChangeTemplateDetail = ({ id }) =>
  urls.CHANGE_TEMPLATE_DETAIL.replace(':id', id);

/**
 * 变更模板编辑
 * @param {object} variables
 * @param {string} variables.id
 */
export const generateChangeTemplateEdit = ({ id }) => urls.CHANGE_TEMPLATE_EDIT.replace(':id', id);

/**
 * 变更编辑
 * @param {object} variables
 * @param {string} variables.id
 */
export const generateChangeTicketEdit = ({ id }) => urls.CHANGE_TICKET_EDIT.replace(':id', id);

/**
 * 考勤组编辑页面的 URL
 * @param {object} variables
 * @param {object} variables.id 考勤组代码
 */
export const generateEditAttGroupConfigUrl = ({ id }) =>
  urls.ATT_GROUP_MANAGEMENT_EDIT.replace(':id', id);

/**
 * 考勤组排班页面的 URL
 * @param {object} variables
 * @param {object} variables.id 考勤组代码
 * @param {object} variables.groupName 考勤组名称
 */
export const generateAttGroupScheduleLocation = ({ id, groupName }) => ({
  pathname: urls.ATT_GROUP_MANAGEMENT_SCHEDULE.replace(':id', id),
  search: `?groupName=${groupName}`,
});

/**
 * 考勤组排班详情页面的 URL
 * @param {object} variables
 * @param {object} variables.id 考勤组代码
 * @param {object} variables.groupName 考勤组名称
 */
export const generateAttGroupScheduleDetailLocation = ({ id, groupName }) => ({
  pathname: urls.ATT_GROUP_MANAGEMENT_SCHEDULE_DETAIL.replace(':id', id),
  search: `?groupName=${groupName}`,
});

/**
 * 考勤组排班编辑页面的 URL
 * @param {object} variables
 * @param {object} variables.id 考勤组代码
 * @param {object} variables.groupName 考勤组名称
 */
export const generateAttGroupScheduleEditLocation = ({ id, groupName }) => ({
  pathname: urls.ATT_GROUP_MANAGEMENT_SCHEDULE_EDIT.replace(':id', id),
  search: `?groupName=${groupName}`,
});

/**
 * 考勤组list URL
 */
export const generateAttGroupScheduleListUrl = () => urls.ATT_GROUP_MANAGEMENT_LIST;

/*
 * 变更编辑页面的 URL
 * @param {object} variables
 * @param {object} variables.id 模板代码
 */
export const generateChangeTicketEditLocation = ({ id = '', stepCurrent = 0 }) => ({
  pathname: generateChangeTicketEdit({ id }),
  search: `?stepCurrent=${stepCurrent}`,
});

/**
 * 变更编辑页面的 Location
 * @param {object} variables
 * @param {object} variables.id 模板代码
 */
export const generateChangeTemplateEditLocation = ({ id = '', stepCurrent = 0 }) => ({
  pathname: generateChangeTemplateEdit({ id }),
  search: `?stepCurrent=${stepCurrent}`,
});

/**
 * 变更详情
 * @param {object} variables
 * @param {string} variables.id
 */
export const generateChangeTicketDetail = ({ id, currentTab }) => ({
  pathname: urls.CHANGE_TICKET_CONFIG.replace(':id', id),
  search: `?currentTab=${currentTab}`,
});

export const generateLogListUrl = () => urls.LOG_LIST;

export const generateChangeTicketDetailLocation = ({ id }) => {
  let search = '?';
  if (id) {
    search += `id=${id}`;
  }
  return { pathname: generateChangeTicketDetail({ id }), search };
};

/**
 * 型号新建页面 URL
 */
export const generateModelNewUrl = () => urls.MODEL_NEW;

/**
 * 考勤统计的 Location
 * @param {object} variables
 * @param {string} variables.byFunc 每日统计 或 月度统计
 */
export const generateAttendanceStatisticsLocation = ({ byFunc }) =>
  urls.ATTENDANCE_STATISTICS_MONTHLY_AND_DAILY.replace(':byFunc', byFunc);

/**
 * 任务详情 Location
 * @param {object} variables
 * @param {string} variables.id
 */
export const generateTaskLocation = ({ id, name }) => ({
  pathname: urls.TASK_CENTER_DETAIL.replace(':id', id),
  search: `?name=${name}`,
});

/**
 * 巡检配置页面的 URL
 */
export const generateInspectionConfigListLocation = () => urls.INSPECTION_CONFIG_LIST;

/**
 * 巡检配置新建页面的 URL
 */
export const generateInspectionConfigCreateLocation = () => urls.INSPECTION_CONFIG_CREATE;

/**
 * 巡检配置详情页面的 URL
 * @param {object} variables
 * @param {object} variables.id 巡检配置代码
 */
export const generateInspectionConfigDetailLocation = ({ id, name }) => ({
  pathname: urls.INSPECTION_CONFIG_DETAIL.replace(':id', id),
  search: `?name=${name}`,
});

/**
 * 巡检配置编辑页面的 URL
 * @param {object} variables
 * @param {object} variables.id 巡检配置代码
 */
export const generateInspectionConfigEditLocation = ({ id, name }) => ({
  pathname: urls.INSPECTION_CONFIG_EDIT.replace(':id', id),
  search: `?name=${name}`,
});

/**
 * 任务列表 Location
 */
export const generateTaskListLocation = () => urls.TASK_CENTER_LIST;

/**
 * 新增任务 Location
 */
export const generateTaskCreateLocation = () => urls.TASK_CENTER_CREATE;

/**
 * 任务编辑 Location
 * @param {object} variables
 * @param {string} variables.id
 */
export const generateTaskEditLocation = ({ id, name }) => ({
  pathname: urls.TASK_CENTER_EDIT.replace(':id', id),
  search: `?name=${name}`,
});

/**
 * 工单列表
 * @param {object} variables
 * @param {string} variables.type
 */
export const generateTicketListUrl = ({ type }) => urls.TICKET_LIST.replace(':type', type);

/**
 * 工单新建
 * @param {object} variables
 * @param {string} variables.ticketType
 * @param {Record<any, any>} variables.variables
 */
export const generateTicketCreateUrl = ({ ticketType, variables }) => {
  const pathname = urls.TICKET_CREATE.replace(':type', ticketType);
  const search = variables ? '?variables=' + JSON.stringify(variables) : '';
  return pathname + search;
};

/**
 * 工单详情
 * @param {object} variables
 * @param {string} variables.type
 * @param {string} variables.id
 */
export const generateTicketDetailUrl = ({ ticketType, id }) =>
  urls.TICKET_VIEW.replace(':type', ticketType).replace(':id', id);

/**
 * 工单编辑
 * @param {object} variables
 * @param {string} variables.type
 * @param {string} variables.id
 */
export const generateTicketEditUrl = ({ ticketType, id }) =>
  urls.TICKET_EDIT.replace(':type', ticketType).replace(':id', id);

/**
 * @deprecated 请使用ticket scopes下的方法 generateTicketLocation
 * 工单详情的 location
 * @param {object} variables
 * @param {object} variables.id
 * @param {object} variables.idcTag
 * @param {object} variables.blockTag
 */
export const generateTicketDetailLocation = ({ id, idcTag, blockTag, ticketType }) => {
  let search = '?';
  if (idcTag) {
    search += `idcTag=${idcTag}`;
  }
  if (blockTag) {
    search += `&blockTag=${blockTag}`;
  }
  return { pathname: generateTicketDetailUrl({ ticketType, id }), search };
};

/**
 * 盘点项配置新建 Location
 */
export const generateInventoryCreateLocation = () => urls.INVENTORY_CONFIG_CREATE;

/**
 * 盘点项配置列表 Location
 */
export const generateInventoryListLocation = () => urls.INVENTORY_CONFIG_LIST;

/**
 * 盘点项配置编辑 Location
 */
export const generateInventoryEditocation = ({ id, name }) => ({
  pathname: urls.INVENTORY_CONFIG_EDIT.replace(':id', id),
  search: `?name=${name}`,
});

/**
 * 盘点项配置详情 Location
 */
export const generateInventoryDetailocation = ({ id, name }) => ({
  pathname: urls.INVENTORY_CONFIG_DETAIL.replace(':id', id),
  search: `?name=${name}`,
});

/**
 * 上下电配置详情 Location
 */
export const generateOperationCheckConfigDetailocation = ({ id, name }) => ({
  pathname: urls.OPERATION_CHECK_CONFIG_DETAIL.replace(':id', id),
  search: `?name=${name}`,
});

/**
 * 上下电配置列表 Location
 */
export const generateOperationCheckConfigListLocation = () => urls.OPERATION_CHECK_CONFIG_LIST;

/**
 * 上下电配置新增 Location
 */
export const generateOperationCheckConfigCreateLocation = () => urls.OPERATION_CHECK_CONFIG_CREATE;

/**
 * 上下电配置编辑 Location
 */
export const generateOperationCheckConfigEditLocation = ({ id, name }) => ({
  pathname: urls.OPERATION_CHECK_CONFIG_EDIT.replace(':id', id),
  search: `?name=${name}`,
});

/**
 * 维护配置新增 Location
 */
export const generateMaintainConfigCreateLocation = () => urls.MAINTAIN_CONFIG_CREATE;

/**
 * 维护配置列表 Location
 */
export const generateMaintainConfigListLocation = () => urls.MAINTAIN_CONFIG_LIST;

/**
 * 维护配置详情 Location
 */
export const generateMaintainConfigDetailocation = ({ id, name }) => ({
  pathname: urls.MAINTAIN_CONFIG_DETAIL.replace(':id', id),
  search: `?name=${name}`,
});

/**
 * 维护配置编辑 Location
 */
export const generateMaintainConfigEditlocation = ({ id, name }) => ({
  pathname: urls.MAINTAIN_CONFIG_EDIT.replace(':id', id),
  search: `?name=${name}`,
});

/**
 * 生成查看某个 id 对应的采集节点详情
 * @deprecated import it from `@manyun/dc-brain.route.admin-routes` instead.
 * @param {string|number} id 节点 id
 */
export const generateServerNodeUrl = ({ id }) => urls.SERVER_NODE_DETAIL.replace(':id', id);

/**
 * 生成查看某个 id 对应的北向用户详情
 * @param {string|number} id 节点 id
 */
export const generateNorthUserUrl = ({ id }) => urls.NORTH_USER_DETAIL.replace(':id', id);

/**
 *新建北向用户
 */
export const generateNorthUserCreateLocation = () => urls.NORTH_USER_NEW;

/**
 * @deprecated 请使用hrm scope下的routes中的方法
 * 调班记录详情 Location
 */
export const generateAlterInfoDetaillocation = ({ id }) => ({
  pathname: urls.ALTER_INFO_DETAIL.replace(':bizId', id),
});

/**
 * @deprecated 请使用hrm scope下的routes中的方法
 * 补卡记录详情
 */
export const generateSupplyCheckDetaillocation = ({ id }) => ({
  pathname: urls.SUPPLY_CHECK_DETAIL.replace(':bizId', id),
});

/**
 * 调整排班新建 URL
 */
export const generateAlterInfoCreateLocation = () => urls.ALTER_INFO_NEW;

/**
 * @deprecated scope/hrm routes SUPPLY_CHECK_NEW_ROUTE_PATH
 * 调整排班新建 URL
 */
export const generateSupplyCheckCreateLocation = () => urls.SUPPLY_CHECK_NEW;
/*
 * 生成查看某个 id 对应的工作日详情
 * @param {string|number} id 节点 id
 */
export const generateWorkdayUrl = ({ id }) => urls.WORKDAY_DETAIL.replace(':id', id);

/**
 * 生成某个 id 对应的工作日编辑
 * @param {string|number} id 节点 id
 */
export const generateEditWorkdayUrl = ({ id }) => urls.WORKDAY_EDIT.replace(':id', id);

/**
 * 工作日管理列表 Location
 */
export const generateWorkdayListLocation = () => urls.WORKDAY_MANAGE_LIST;

/**
 *生成入室登记
 */
export const generateVisitorRecordCreateLocation = params => {
  const base = urls.VISITOR_RECORD_NEW;
  if (!params) {
    return base;
  }
  const query = new URLSearchParams(params).toString();
  return `${base}?${query}`;
};

/**
 * 参观大屏页面 URL
 * @param {object} variables
 * @param {string} variables.tag
 */
export const generateBigScreenUrl = ({ tag }) => urls.VISITOR_SCREEN.replace(':idc', tag);

/**
 * 生成电池组的 URL
 * @param {object} variables
 * @param {string} variables.idc 机房
 */
export const generateBatterPackUrl = ({ idc }) => urls.BATTERY_PACK.replace(':idc', idc);

/**
 * 生成查看某个 id 对应的通道详情
 * @deprecated import it from `@manyun/dc-brain.route.admin-routes` instead.
 * @param {string|number} id 节点 id
 */
export const generateChannelConfigUrl = ({ id }) => urls.CHANNEL_CONFIG_DETAIL.replace(':id', id);

/**
 *新建通道
 */
export const generateChannelConfigCreateLocation = () => urls.CHANNEL_CONFIG_NEW;

/**
 *编辑通道
 */
export const generateChannelConfigEditLocation = ({ id }) =>
  urls.CHANNEL_CONFIG_EDIT.replace(':id', id);

/**
 *通道关联设备
 */
export const generateChannelDeviceAssociateLocation = ({ id }) =>
  urls.CHANNEL_CONFIG_ASSOCIATE.replace(':id', id);

/**
 *导入通道点位
 */
export const generateChannelPointImportLocation = ({ id }) =>
  urls.CHANNEL_CONFIG_IMPORT.replace(':id', id);

/**
 * 供应商详情 Location
 */
export const generateVendorDetailLocation = ({ id, vendorCode }) => {
  const search = `?vendorCode=${vendorCode}`;
  return { pathname: urls.VENDOR_DETAIL.replace(':id', id), search };
};

/**
 * 维保订单编辑页面 URL
 * @param {object} variables
 * @param {string} variables.id 维保订单id
 */
export const generateWarrantyOrderEditUrl = ({ id }) =>
  urls.WARRANTY_MANAGE_EDIT.replace(':id', id);

/**
 * 维保订单详情页面 URL
 * @deprecated 请使用ticket scope下routes中的方法
 * @param {object} variables
 * @param {string} variables.id 维保订单id
 */
export const generateWarrantyOrderDetailUrl = ({ id }) =>
  urls.WARRANTY_MANAGE_DETAIL.replace(':id', id);

/**
 * 公告通知详情 Location
 * @param {string} id 公告通知 id
 */
export const generateNoticeDetailLocation = ({ id }) =>
  urls.NOTICE_MANAGE_DETAIL.replace(':id', id);

/**
 * 待办 Location
 */
export const generateMineTicketsLocation = () => urls.MINE_TICKET;

/**
 * 预到货池新建页 Location
 * @param {object} variables
 * @param {string} variables.type
 */
export const generateArrivalAssetNewLocation = ({ type }) => {
  const search = `?type=${type}`;
  return { pathname: urls.ARRIVAL_ASSET_NEW, search };
};
