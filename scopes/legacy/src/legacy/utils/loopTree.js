/**
 * 遍历树形数据，可以简化节点或进行过滤、排序操作
 *
 * @param {any[]} tree
 * @param {{ simplifyNode?: (node: any) => any; filterNode?: (node: any, index: number, depth: number) => boolean; sort?: (a: any, b: any) => number }} opts
 * @returns
 */
export function loopTree(tree, opts, depth = 0) {
  const { simplifyNode, filterNode, sort } = opts;

  const filtered =
    typeof filterNode == 'function'
      ? tree.filter((_node, _idx) => filterNode(_node, _idx, depth))
      : tree;
  const sorted = typeof sort == 'function' ? filtered.sort(sort) : filtered;

  return sorted.map(treeNode => {
    const simplifiedTreeNode = simplifyNode(treeNode);

    if (Array.isArray(simplifiedTreeNode.children) && simplifiedTreeNode.children.length) {
      simplifiedTreeNode.children = loopTree(treeNode.children, opts, depth + 1);
    }

    return simplifiedTreeNode;
  });
}

export default loopTree;
