import { ALARM_TYPE_STATUS_MAP, COLOR_MAP } from '@manyun/dc-brain.legacy.constants/statusColor';

/**
 * @deprecated Donot use this constant anymore, consider using `monitoring/ui/point-renderer` instead
 */
export function getColorByStauts(status) {
  return COLOR_MAP[status];
}

/**
 * Given an `alarmType`, return a mapped `status`.
 * @param {string} alarmType Alarm type
 * @returns {string} status
 */
export function getStatusByAlarmType(alarmType) {
  const status = ALARM_TYPE_STATUS_MAP[alarmType];
  if (status === undefined) {
    console.error(`Alarm type(${alarmType}) not supported.`);
  }

  return status;
}
